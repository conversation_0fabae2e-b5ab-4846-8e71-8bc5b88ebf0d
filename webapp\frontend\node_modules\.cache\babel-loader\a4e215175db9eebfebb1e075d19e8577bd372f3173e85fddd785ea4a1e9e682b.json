{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\PycharmProjects\\\\CMS\\\\webapp\\\\frontend\\\\src\\\\pages\\\\cavi\\\\VisualizzaCaviPage.js\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect } from 'react';\nimport { Box, Typography, Paper, Button, Grid, Card, CardContent, Alert, IconButton, Chip, CircularProgress, LinearProgress, Dialog, DialogTitle, DialogContent, DialogActions } from '@mui/material';\nimport InfoIcon from '@mui/icons-material/Info';\nimport RefreshIcon from '@mui/icons-material/Refresh';\nimport { useNavigate } from 'react-router-dom';\nimport { useAuth } from '../../context/AuthContext';\nimport { useGlobalContext } from '../../context/GlobalContext';\nimport PosaCaviCollegamenti from '../../components/cavi/PosaCaviCollegamenti';\nimport caviService from '../../services/caviService';\nimport AggiungiCavoForm from '../../components/cavi/AggiungiCavoForm';\nimport { normalizeInstallationStatus } from '../../utils/validationUtils';\nimport CaviFilterableTable from '../../components/cavi/CaviFilterableTable';\nimport './CaviPage.css';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst VisualizzaCaviPage = () => {\n  _s();\n  var _caviAttivi$, _caviAttivi$2, _caviAttivi$3;\n  const {\n    isImpersonating,\n    user\n  } = useAuth();\n  const {\n    openEliminaCavoDialog,\n    setOpenEliminaCavoDialog,\n    openModificaCavoDialog,\n    setOpenModificaCavoDialog,\n    openAggiungiCavoDialog,\n    setOpenAggiungiCavoDialog\n  } = useGlobalContext();\n  const navigate = useNavigate();\n  const [cantiereId, setCantiereId] = useState(null);\n  const [cantiereName, setCantiereName] = useState('');\n  const [caviAttivi, setCaviAttivi] = useState([]);\n  const [caviSpare, setCaviSpare] = useState([]);\n  const [loading, setLoading] = useState(true);\n  const [error, setError] = useState(null);\n  // Rimosso stato viewMode\n\n  // Stato per il dialogo dei dettagli del cavo\n  const [selectedCavo, setSelectedCavo] = useState(null);\n  const [detailsDialogOpen, setDetailsDialogOpen] = useState(false);\n\n  // Stato per le statistiche\n  const [stats, setStats] = useState(null);\n  const [loadingStats, setLoadingStats] = useState(false);\n\n  // Rimosso stato per il debug\n\n  // Funzione per caricare gli stati di installazione disponibili\n  const loadStatiInstallazione = () => {\n    // Usa i valori dell'enum StatoInstallazione\n    setStatiInstallazione(['Installato', 'Da installare', 'In corso']);\n  };\n\n  // Stato per filtri e ordinamento\n  const [filters, setFilters] = useState({\n    stato_installazione: '',\n    tipologia: '',\n    sort_by: '',\n    sort_order: 'asc'\n  });\n\n  // Opzioni per i filtri\n  const [statiInstallazione, setStatiInstallazione] = useState([]);\n  const [tipologieCavi, setTipologieCavi] = useState([]);\n\n  // Rimossa funzione di debug\n\n  // Funzione per caricare i cavi\n  const fetchCavi = async () => {\n    try {\n      setLoading(true);\n      console.log('Caricamento cavi per cantiere:', cantiereId);\n\n      // Verifica che cantiereId sia valido\n      if (!cantiereId) {\n        console.error('fetchCavi: cantiereId non valido:', cantiereId);\n        setError('ID cantiere non valido o mancante. Ricarica la pagina.');\n        setLoading(false);\n        return;\n      }\n\n      // Recupera il cantiereId dal localStorage come fallback\n      let cantiereIdToUse = cantiereId;\n      if (!cantiereIdToUse) {\n        cantiereIdToUse = localStorage.getItem('selectedCantiereId');\n        console.log('Usando cantiereId dal localStorage:', cantiereIdToUse);\n        if (!cantiereIdToUse) {\n          console.error('Impossibile trovare un ID cantiere valido');\n          setError('ID cantiere non trovato. Ricarica la pagina.');\n          setLoading(false);\n          return;\n        }\n      }\n\n      // Carica i cavi attivi\n      console.log('Caricamento cavi attivi (tipo_cavo=0)...');\n      let attivi = [];\n      try {\n        attivi = await caviService.getCavi(cantiereIdToUse, 0, filters);\n        console.log('Cavi attivi caricati:', attivi ? attivi.length : 0);\n      } catch (attiviError) {\n        console.error('Errore nel caricamento dei cavi attivi:', attiviError);\n        // Continua con un array vuoto\n        attivi = [];\n      }\n\n      // Verifica se ci sono cavi con modificato_manualmente = 3 tra i cavi attivi\n      if (attivi && attivi.length > 0) {\n        const caviSpareTraAttivi = attivi.filter(cavo => cavo.modificato_manualmente === 3);\n        if (caviSpareTraAttivi.length > 0) {\n          console.error('ERRORE: Trovati cavi con modificato_manualmente = 3 tra i cavi attivi:', caviSpareTraAttivi);\n        }\n      }\n      setCaviAttivi(attivi || []);\n\n      // Carica i cavi SPARE con la nuova funzione dedicata\n      let spare = [];\n      try {\n        console.log('Caricamento cavi SPARE con funzione dedicata...');\n        spare = await caviService.getCaviSpare(cantiereIdToUse);\n        console.log('Cavi SPARE caricati con funzione dedicata:', spare ? spare.length : 0);\n        if (spare && spare.length > 0) {\n          console.log('Primo cavo SPARE:', spare[0]);\n        }\n      } catch (spareError) {\n        console.error('Errore nel caricamento dei cavi SPARE con funzione dedicata:', spareError);\n        // Se fallisce, prova con il metodo standard\n        try {\n          console.log('Tentativo con metodo standard...');\n          spare = await caviService.getCavi(cantiereIdToUse, 3);\n          console.log('Cavi SPARE caricati con metodo standard:', spare ? spare.length : 0);\n        } catch (standardError) {\n          console.error('Errore anche con metodo standard:', standardError);\n          // Continua con un array vuoto\n          spare = [];\n        }\n      }\n      setCaviSpare(spare || []);\n\n      // Carica le statistiche\n      try {\n        console.log('Caricamento statistiche...');\n        const statsData = await caviService.getCaviStats(cantiereIdToUse);\n        console.log('Statistiche caricate:', statsData);\n        setStats(statsData);\n      } catch (statsError) {\n        console.error('Errore nel caricamento delle statistiche:', statsError);\n        // Continua senza statistiche\n        setStats({});\n      }\n\n      // Se siamo arrivati qui, rimuovi eventuali messaggi di errore precedenti\n      setError('');\n    } catch (error) {\n      console.error('Errore generale nel caricamento dei cavi:', error);\n      setError(`Errore nel caricamento dei cavi: ${error.message || 'Errore sconosciuto'}`);\n\n      // Prova a ricaricare la pagina dopo un ritardo se l'errore persiste\n      setTimeout(() => {\n        // Verifica se siamo ancora in errore\n        if (document.body.textContent.includes('Errore nel caricamento dei cavi')) {\n          console.log('Errore persistente, tentativo di ricaricamento della pagina...');\n          window.location.reload();\n        }\n      }, 5000); // 5 secondi di ritardo\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  // Carica i dati del cantiere e dei cavi\n  useEffect(() => {\n    // Carica gli stati di installazione all'avvio\n    loadStatiInstallazione();\n    const fetchData = async () => {\n      try {\n        console.log('Inizializzazione VisualizzaCaviPage...');\n\n        // Verifica che l'utente sia autenticato\n        const token = localStorage.getItem('token');\n        console.log('Token presente:', !!token);\n        if (!token) {\n          setError('Sessione scaduta. Effettua nuovamente il login.');\n          setLoading(false);\n          return;\n        }\n\n        // Recupera l'ID del cantiere selezionato dal localStorage\n        let selectedCantiereId = localStorage.getItem('selectedCantiereId');\n        let selectedCantiereName = localStorage.getItem('selectedCantiereName');\n        console.log('Cantiere selezionato dal localStorage:', {\n          selectedCantiereId,\n          selectedCantiereName\n        });\n        console.log('Dati utente:', user);\n\n        // Stampa tutti i dati nel localStorage per debug\n        console.log('DEBUG - Tutti i dati nel localStorage:');\n        for (let i = 0; i < localStorage.length; i++) {\n          const key = localStorage.key(i);\n          console.log(`${key}: ${localStorage.getItem(key)}`);\n        }\n\n        // SOLUZIONE DIRETTA: Ottieni l'ID del cantiere direttamente dal token JWT\n        if ((user === null || user === void 0 ? void 0 : user.role) === 'cantieri_user') {\n          console.log('Utente cantiere rilevato, tentativo di recupero ID cantiere dai dati utente');\n\n          // Verifica se l'utente ha un ID cantiere nei dati utente\n          if (user.cantiere_id) {\n            console.log('Trovato ID cantiere nei dati utente:', user.cantiere_id);\n            selectedCantiereId = user.cantiere_id.toString();\n            selectedCantiereName = user.cantiere_name || `Cantiere ${user.cantiere_id}`;\n\n            // Salva l'ID e il nome del cantiere nel localStorage\n            localStorage.setItem('selectedCantiereId', selectedCantiereId);\n            localStorage.setItem('selectedCantiereName', selectedCantiereName);\n            console.log('Salvato ID cantiere nel localStorage:', selectedCantiereId);\n          } else {\n            // Tentativo di recupero dal token JWT\n            try {\n              console.log('Tentativo di decodifica del token JWT per recuperare l\\'ID cantiere');\n              const token = localStorage.getItem('token');\n              if (token) {\n                // Decodifica il token JWT (senza verifica della firma)\n                const base64Url = token.split('.')[1];\n                const base64 = base64Url.replace(/-/g, '+').replace(/_/g, '/');\n                const jsonPayload = decodeURIComponent(atob(base64).split('').map(c => {\n                  return '%' + ('00' + c.charCodeAt(0).toString(16)).slice(-2);\n                }).join(''));\n                const payload = JSON.parse(jsonPayload);\n                console.log('Payload del token JWT:', payload);\n                if (payload.cantiere_id) {\n                  console.log('Trovato ID cantiere nel token JWT:', payload.cantiere_id);\n                  selectedCantiereId = payload.cantiere_id.toString();\n                  // Usa un nome generico se non disponibile\n                  selectedCantiereName = `Cantiere ${payload.cantiere_id}`;\n\n                  // Salva l'ID e il nome del cantiere nel localStorage\n                  localStorage.setItem('selectedCantiereId', selectedCantiereId);\n                  localStorage.setItem('selectedCantiereName', selectedCantiereName);\n                  console.log('Salvato ID cantiere nel localStorage:', selectedCantiereId);\n                }\n              }\n            } catch (e) {\n              console.error('Errore durante la decodifica del token JWT:', e);\n            }\n          }\n        }\n\n        // SOLUZIONE TEMPORANEA: Se ancora non abbiamo un ID cantiere, usa un valore hardcoded per debug\n        if (!selectedCantiereId || selectedCantiereId === 'undefined' || selectedCantiereId === 'null') {\n          console.warn('ATTENZIONE: Nessun ID cantiere trovato, utilizzo valore hardcoded per debug');\n          // Usa il primo cantiere disponibile (questo è solo per debug)\n          selectedCantiereId = '1'; // Sostituisci con un ID cantiere valido nel tuo database\n          selectedCantiereName = 'Cantiere Debug';\n\n          // Salva l'ID e il nome del cantiere nel localStorage\n          localStorage.setItem('selectedCantiereId', selectedCantiereId);\n          localStorage.setItem('selectedCantiereName', selectedCantiereName);\n          console.log('Salvato ID cantiere hardcoded nel localStorage:', selectedCantiereId);\n        }\n\n        // Verifica finale\n        if (!selectedCantiereId) {\n          setError('Nessun cantiere selezionato. Torna alla pagina dei cantieri.');\n          setLoading(false);\n          return;\n        }\n\n        // Verifica che l'ID del cantiere sia un numero valido\n        const cantiereIdNum = parseInt(selectedCantiereId, 10);\n        console.log('ID cantiere convertito a numero:', cantiereIdNum);\n        if (isNaN(cantiereIdNum)) {\n          setError(`ID cantiere non valido: ${selectedCantiereId}. Torna alla pagina dei cantieri.`);\n          setLoading(false);\n          return;\n        }\n\n        // Usa il numero convertito, non la stringa\n        setCantiereId(cantiereIdNum);\n        setCantiereName(selectedCantiereName || `Cantiere ${cantiereIdNum}`);\n\n        // Carica le statistiche dei cavi\n        try {\n          setLoadingStats(true);\n          console.log('Caricamento statistiche cavi per cantiere:', cantiereIdNum);\n          const statsData = await caviService.getCaviStats(cantiereIdNum);\n          console.log('Statistiche cavi caricate:', statsData);\n          setStats(statsData);\n\n          // Estrai gli stati di installazione e le tipologie per i filtri\n          if (statsData && statsData.stati) {\n            const stati = statsData.stati.map(item => item.stato).filter(stato => stato !== 'Non specificato');\n            setStatiInstallazione(stati);\n          }\n          if (statsData && statsData.tipologie) {\n            const tipologie = statsData.tipologie.map(item => item.tipologia).filter(tipo => tipo !== 'Non specificata');\n            setTipologieCavi(tipologie);\n          }\n          setLoadingStats(false);\n        } catch (statsError) {\n          console.error('Errore nel caricamento delle statistiche:', statsError);\n          setLoadingStats(false);\n          // Non interrompere il flusso se le statistiche falliscono\n        }\n\n        // Carica i cavi attivi con gestione degli errori migliorata\n        console.log('Caricamento cavi attivi per cantiere:', cantiereIdNum);\n        try {\n          // Imposta un timeout per evitare che la richiesta rimanga bloccata\n          const timeoutPromise = new Promise((_, reject) => {\n            setTimeout(() => reject(new Error('Timeout durante il caricamento dei cavi attivi')), 30000); // Aumentato a 30 secondi\n          });\n\n          // Esegui la richiesta con un timeout di sicurezza e applica i filtri\n          console.log('Iniziando chiamata API per cavi attivi con filtri:', filters);\n          const caviPromise = caviService.getCavi(cantiereIdNum, 0, filters);\n          const attivi = await Promise.race([caviPromise, timeoutPromise]);\n          console.log('Cavi attivi caricati:', attivi);\n          console.log('Numero di cavi attivi trovati:', attivi ? attivi.length : 0);\n          if (attivi && attivi.length > 0) {\n            console.log('Primo cavo attivo:', attivi[0]);\n          } else {\n            console.warn('Nessun cavo attivo trovato per il cantiere', cantiereIdNum);\n          }\n          setCaviAttivi(attivi || []);\n        } catch (caviError) {\n          console.error('Errore nel caricamento dei cavi attivi:', caviError);\n          console.error('Dettagli errore cavi attivi:', {\n            message: caviError.message,\n            status: caviError.status,\n            data: caviError.data,\n            stack: caviError.stack,\n            code: caviError.code,\n            name: caviError.name,\n            response: caviError.response ? {\n              status: caviError.response.status,\n              statusText: caviError.response.statusText,\n              data: caviError.response.data\n            } : 'No response'\n          });\n\n          // Non interrompere il flusso, continua con i cavi spare\n          setCaviAttivi([]);\n          console.warn('Continuazione del flusso dopo errore nei cavi attivi');\n\n          // Aggiungi un messaggio di errore visibile all'utente\n          setError(`Errore nel caricamento dei cavi attivi: ${caviError.message}. Controlla la console per maggiori dettagli.`);\n        }\n\n        // Carica i cavi spare con gestione degli errori migliorata\n        console.log('Caricamento cavi spare per cantiere:', cantiereIdNum);\n        try {\n          // Imposta un timeout per evitare che la richiesta rimanga bloccata\n          const timeoutPromise = new Promise((_, reject) => {\n            setTimeout(() => reject(new Error('Timeout durante il caricamento dei cavi spare')), 30000); // Aumentato a 30 secondi\n          });\n\n          // Esegui la richiesta con un timeout di sicurezza\n          console.log('Iniziando chiamata API per cavi spare...');\n          // Non applichiamo i filtri ai cavi spare, solo agli attivi\n          const sparePromise = caviService.getCavi(cantiereIdNum, 3);\n          const spare = await Promise.race([sparePromise, timeoutPromise]);\n          console.log('Cavi spare caricati:', spare);\n          console.log('Numero di cavi spare trovati:', spare ? spare.length : 0);\n          if (spare && spare.length > 0) {\n            console.log('Primo cavo spare:', spare[0]);\n          } else {\n            console.warn('Nessun cavo spare trovato per il cantiere', cantiereIdNum);\n          }\n          setCaviSpare(spare || []);\n        } catch (spareError) {\n          console.error('Errore nel caricamento dei cavi spare:', spareError);\n          console.error('Dettagli errore cavi spare:', {\n            message: spareError.message,\n            status: spareError.status,\n            data: spareError.data,\n            stack: spareError.stack,\n            code: spareError.code,\n            name: spareError.name,\n            response: spareError.response ? {\n              status: spareError.response.status,\n              statusText: spareError.response.statusText,\n              data: spareError.response.data\n            } : 'No response'\n          });\n\n          // Non interrompere il flusso, imposta un array vuoto\n          setCaviSpare([]);\n\n          // Aggiungi un messaggio di errore visibile all'utente se non c'è già un errore per i cavi attivi\n          if (!error) {\n            setError(`Errore nel caricamento dei cavi spare: ${spareError.message}. Controlla la console per maggiori dettagli.`);\n          }\n        }\n\n        // Se siamo arrivati qui, almeno abbiamo caricato l'interfaccia di base\n        setLoading(false);\n      } catch (err) {\n        var _err$response, _err$response2, _err$response3, _err$response4, _err$response5, _err$response5$data;\n        console.error('Errore nel caricamento dei cavi:', err);\n        console.error('Dettagli errore generale:', {\n          message: err.message,\n          status: err.status || ((_err$response = err.response) === null || _err$response === void 0 ? void 0 : _err$response.status),\n          data: err.data || ((_err$response2 = err.response) === null || _err$response2 === void 0 ? void 0 : _err$response2.data),\n          stack: err.stack\n        });\n\n        // Estrai il messaggio di errore dettagliato\n        let errorMessage = 'Errore sconosciuto';\n        if (err.message && err.message.includes('ID cantiere non valido')) {\n          errorMessage = err.message;\n        } else if (err.status === 401 || err.status === 403 || ((_err$response3 = err.response) === null || _err$response3 === void 0 ? void 0 : _err$response3.status) === 401 || ((_err$response4 = err.response) === null || _err$response4 === void 0 ? void 0 : _err$response4.status) === 403) {\n          errorMessage = 'Sessione scaduta o non autorizzata. Effettua nuovamente il login.';\n        } else if ((_err$response5 = err.response) !== null && _err$response5 !== void 0 && (_err$response5$data = _err$response5.data) !== null && _err$response5$data !== void 0 && _err$response5$data.detail) {\n          // Estrai il messaggio di errore dettagliato dall'API\n          errorMessage = `Errore API: ${err.response.data.detail}`;\n        } else if (err.code === 'ERR_NETWORK') {\n          // Errore di rete\n          errorMessage = 'Network Error. Verifica che il backend sia in esecuzione e accessibile.';\n        } else if (err.message) {\n          errorMessage = err.message;\n        }\n        setError(`Impossibile caricare i cavi: ${errorMessage}. Riprova più tardi.`);\n\n        // Imposta array vuoti per evitare errori di rendering\n        setCaviAttivi([]);\n        setCaviSpare([]);\n      } finally {\n        setLoading(false);\n      }\n    };\n    fetchData();\n  }, [filters]); // Ricarica i dati quando cambiano i filtri\n\n  // I filtri sono ora gestiti dal componente CaviFilterableTable\n\n  // Funzione per aprire il dialogo dei dettagli del cavo\n  const handleOpenDetails = cavo => {\n    setSelectedCavo(cavo);\n    setDetailsDialogOpen(true);\n  };\n\n  // Funzione per chiudere il dialogo dei dettagli del cavo\n  const handleCloseDetails = () => {\n    setDetailsDialogOpen(false);\n    setSelectedCavo(null);\n  };\n\n  // Nessuna funzione di navigazione necessaria, tutto è gestito dal menu principale\n\n  // La visualizzazione dei cavi è ora gestita dal componente CaviFilterableTable\n\n  // Rimossa funzione handleViewModeChange\n\n  // Renderizza il dialogo dei dettagli del cavo\n  const renderDetailsDialog = () => {\n    if (!selectedCavo) return null;\n    return /*#__PURE__*/_jsxDEV(Dialog, {\n      open: detailsDialogOpen,\n      onClose: handleCloseDetails,\n      maxWidth: \"md\",\n      fullWidth: true,\n      children: [/*#__PURE__*/_jsxDEV(DialogTitle, {\n        children: [\"Dettagli Cavo: \", selectedCavo.id_cavo]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 478,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(DialogContent, {\n        dividers: true,\n        children: /*#__PURE__*/_jsxDEV(Grid, {\n          container: true,\n          spacing: 2,\n          children: [/*#__PURE__*/_jsxDEV(Grid, {\n            item: true,\n            xs: 12,\n            md: 6,\n            children: [/*#__PURE__*/_jsxDEV(Typography, {\n              variant: \"subtitle1\",\n              gutterBottom: true,\n              children: \"Informazioni Generali\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 484,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Box, {\n              sx: {\n                mb: 2\n              },\n              children: [/*#__PURE__*/_jsxDEV(Typography, {\n                variant: \"body2\",\n                children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n                  children: \"Sistema:\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 486,\n                  columnNumber: 45\n                }, this), \" \", selectedCavo.sistema || 'N/A']\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 486,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                variant: \"body2\",\n                children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n                  children: \"Utility:\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 487,\n                  columnNumber: 45\n                }, this), \" \", selectedCavo.utility || 'N/A']\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 487,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                variant: \"body2\",\n                children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n                  children: \"Tipologia:\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 488,\n                  columnNumber: 45\n                }, this), \" \", selectedCavo.tipologia || 'N/A']\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 488,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                variant: \"body2\",\n                children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n                  children: \"Colore:\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 489,\n                  columnNumber: 45\n                }, this), \" \", selectedCavo.colore_cavo || 'N/A']\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 489,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                variant: \"body2\",\n                children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n                  children: \"N. Conduttori:\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 490,\n                  columnNumber: 45\n                }, this), \" \", selectedCavo.n_conduttori || 'N/A']\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 490,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                variant: \"body2\",\n                children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n                  children: \"Sezione:\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 491,\n                  columnNumber: 45\n                }, this), \" \", selectedCavo.sezione || 'N/A']\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 491,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                variant: \"body2\",\n                children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n                  children: \"SH:\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 492,\n                  columnNumber: 45\n                }, this), \" \", selectedCavo.sh || 'N/A']\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 492,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 485,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Typography, {\n              variant: \"subtitle1\",\n              gutterBottom: true,\n              children: \"Partenza\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 495,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Box, {\n              sx: {\n                mb: 2\n              },\n              children: [/*#__PURE__*/_jsxDEV(Typography, {\n                variant: \"body2\",\n                children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n                  children: \"Ubicazione:\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 497,\n                  columnNumber: 45\n                }, this), \" \", selectedCavo.ubicazione_partenza || 'N/A']\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 497,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                variant: \"body2\",\n                children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n                  children: \"Utenza:\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 498,\n                  columnNumber: 45\n                }, this), \" \", selectedCavo.utenza_partenza || 'N/A']\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 498,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                variant: \"body2\",\n                children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n                  children: \"Descrizione:\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 499,\n                  columnNumber: 45\n                }, this), \" \", selectedCavo.descrizione_utenza_partenza || 'N/A']\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 499,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                variant: \"body2\",\n                children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n                  children: \"Responsabile:\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 500,\n                  columnNumber: 45\n                }, this), \" \", selectedCavo.responsabile_partenza || 'N/A']\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 500,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                variant: \"body2\",\n                children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n                  children: \"Comanda:\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 501,\n                  columnNumber: 45\n                }, this), \" \", selectedCavo.comanda_partenza || 'N/A']\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 501,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 496,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 483,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Grid, {\n            item: true,\n            xs: 12,\n            md: 6,\n            children: [/*#__PURE__*/_jsxDEV(Typography, {\n              variant: \"subtitle1\",\n              gutterBottom: true,\n              children: \"Arrivo\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 506,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Box, {\n              sx: {\n                mb: 2\n              },\n              children: [/*#__PURE__*/_jsxDEV(Typography, {\n                variant: \"body2\",\n                children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n                  children: \"Ubicazione:\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 508,\n                  columnNumber: 45\n                }, this), \" \", selectedCavo.ubicazione_arrivo || 'N/A']\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 508,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                variant: \"body2\",\n                children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n                  children: \"Utenza:\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 509,\n                  columnNumber: 45\n                }, this), \" \", selectedCavo.utenza_arrivo || 'N/A']\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 509,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                variant: \"body2\",\n                children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n                  children: \"Descrizione:\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 510,\n                  columnNumber: 45\n                }, this), \" \", selectedCavo.descrizione_utenza_arrivo || 'N/A']\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 510,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                variant: \"body2\",\n                children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n                  children: \"Responsabile:\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 511,\n                  columnNumber: 45\n                }, this), \" \", selectedCavo.responsabile_arrivo || 'N/A']\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 511,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                variant: \"body2\",\n                children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n                  children: \"Comanda:\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 512,\n                  columnNumber: 45\n                }, this), \" \", selectedCavo.comanda_arrivo || 'N/A']\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 512,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 507,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Typography, {\n              variant: \"subtitle1\",\n              gutterBottom: true,\n              children: \"Installazione\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 515,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Box, {\n              sx: {\n                mb: 2\n              },\n              children: [/*#__PURE__*/_jsxDEV(Typography, {\n                variant: \"body2\",\n                children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n                  children: \"Metri Teorici:\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 517,\n                  columnNumber: 45\n                }, this), \" \", selectedCavo.metri_teorici || 'N/A']\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 517,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                variant: \"body2\",\n                children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n                  children: \"Metratura Reale:\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 518,\n                  columnNumber: 45\n                }, this), \" \", selectedCavo.metratura_reale || '0']\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 518,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                variant: \"body2\",\n                children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n                  children: \"Stato:\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 519,\n                  columnNumber: 45\n                }, this), \" \", normalizeInstallationStatus(selectedCavo.stato_installazione)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 519,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                variant: \"body2\",\n                children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n                  children: \"Collegamenti:\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 520,\n                  columnNumber: 45\n                }, this), \" \", selectedCavo.collegamenti || '0']\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 520,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                variant: \"body2\",\n                children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n                  children: \"Bobina:\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 521,\n                  columnNumber: 45\n                }, this), \" \", selectedCavo.id_bobina || 'N/A']\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 521,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                variant: \"body2\",\n                children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n                  children: \"Responsabile Posa:\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 522,\n                  columnNumber: 45\n                }, this), \" \", selectedCavo.responsabile_posa || 'N/A']\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 522,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                variant: \"body2\",\n                children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n                  children: \"Comanda Posa:\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 523,\n                  columnNumber: 45\n                }, this), \" \", selectedCavo.comanda_posa || 'N/A']\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 523,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                variant: \"body2\",\n                children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n                  children: \"Ultimo Aggiornamento:\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 524,\n                  columnNumber: 45\n                }, this), \" \", new Date(selectedCavo.timestamp).toLocaleString()]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 524,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 516,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 505,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 482,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 481,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(DialogActions, {\n        children: /*#__PURE__*/_jsxDEV(Button, {\n          onClick: handleCloseDetails,\n          children: \"Chiudi\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 530,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 529,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 477,\n      columnNumber: 7\n    }, this);\n  };\n\n  // Il pannello dei filtri è ora gestito dal componente CaviFilterableTable\n\n  // Renderizza il pannello delle statistiche\n  const renderStatsPanel = () => {\n    // Verifica che stats sia definito e abbia la struttura attesa\n    if (!stats || !stats.totali || !stats.metrature || !stats.stati) {\n      return /*#__PURE__*/_jsxDEV(Paper, {\n        sx: {\n          mb: 3,\n          p: 2\n        },\n        children: [/*#__PURE__*/_jsxDEV(Typography, {\n          variant: \"h6\",\n          gutterBottom: true,\n          children: \"Statistiche\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 544,\n          columnNumber: 11\n        }, this), loadingStats ? /*#__PURE__*/_jsxDEV(LinearProgress, {}, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 546,\n          columnNumber: 13\n        }, this) : /*#__PURE__*/_jsxDEV(Typography, {\n          variant: \"body2\",\n          children: \"Nessuna statistica disponibile\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 548,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 543,\n        columnNumber: 9\n      }, this);\n    }\n\n    // Valori predefiniti per evitare errori\n    const totali = stats.totali || {\n      cavi_attivi: 0,\n      cavi_spare: 0,\n      cavi_totali: 0\n    };\n    const metrature = stats.metrature || {\n      metri_teorici_totali: 0,\n      metri_reali_totali: 0,\n      percentuale_completamento: 0\n    };\n    const stati = stats.stati || [];\n    return /*#__PURE__*/_jsxDEV(Paper, {\n      sx: {\n        mb: 3,\n        p: 2\n      },\n      children: [/*#__PURE__*/_jsxDEV(Typography, {\n        variant: \"h6\",\n        gutterBottom: true,\n        children: \"Statistiche\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 561,\n        columnNumber: 9\n      }, this), loadingStats ? /*#__PURE__*/_jsxDEV(LinearProgress, {}, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 563,\n        columnNumber: 11\n      }, this) : /*#__PURE__*/_jsxDEV(Grid, {\n        container: true,\n        spacing: 2,\n        children: [/*#__PURE__*/_jsxDEV(Grid, {\n          item: true,\n          xs: 12,\n          md: 4,\n          children: [/*#__PURE__*/_jsxDEV(Typography, {\n            variant: \"subtitle1\",\n            gutterBottom: true,\n            children: \"Totali\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 567,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(Typography, {\n            variant: \"body2\",\n            children: [\"Cavi Attivi: \", totali.cavi_attivi || 0]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 568,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(Typography, {\n            variant: \"body2\",\n            children: [\"Cavi Spare: \", totali.cavi_spare || 0]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 569,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(Typography, {\n            variant: \"body2\",\n            children: [\"Totale Cavi: \", totali.cavi_totali || 0]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 570,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 566,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(Grid, {\n          item: true,\n          xs: 12,\n          md: 4,\n          children: [/*#__PURE__*/_jsxDEV(Typography, {\n            variant: \"subtitle1\",\n            gutterBottom: true,\n            children: \"Metrature\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 575,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(Typography, {\n            variant: \"body2\",\n            children: [\"Metri Teorici: \", (metrature.metri_teorici_totali || 0).toFixed(2)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 576,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(Typography, {\n            variant: \"body2\",\n            children: [\"Metri Posati: \", (metrature.metri_reali_totali || 0).toFixed(2)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 577,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(Box, {\n            sx: {\n              display: 'flex',\n              alignItems: 'center',\n              mt: 1\n            },\n            children: [/*#__PURE__*/_jsxDEV(Box, {\n              sx: {\n                width: '100%',\n                mr: 1\n              },\n              children: /*#__PURE__*/_jsxDEV(LinearProgress, {\n                variant: \"determinate\",\n                value: metrature.percentuale_completamento || 0,\n                sx: {\n                  height: 10,\n                  borderRadius: 5\n                }\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 580,\n                columnNumber: 19\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 579,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(Box, {\n              sx: {\n                minWidth: 35\n              },\n              children: /*#__PURE__*/_jsxDEV(Typography, {\n                variant: \"body2\",\n                color: \"text.secondary\",\n                children: `${(metrature.percentuale_completamento || 0).toFixed(1)}%`\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 587,\n                columnNumber: 19\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 586,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 578,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 574,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(Grid, {\n          item: true,\n          xs: 12,\n          md: 4,\n          children: [/*#__PURE__*/_jsxDEV(Typography, {\n            variant: \"subtitle1\",\n            gutterBottom: true,\n            children: \"Stati\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 593,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(Box, {\n            sx: {\n              display: 'flex',\n              flexWrap: 'wrap',\n              gap: 1\n            },\n            children: stati.length > 0 ? stati.map((stato, index) => /*#__PURE__*/_jsxDEV(Chip, {\n              label: `${stato.stato || 'N/A'}: ${stato.count || 0}`,\n              size: \"small\",\n              onClick: () => {\n                setFilters(prev => ({\n                  ...prev,\n                  stato_installazione: stato.stato === 'Non specificato' ? '' : stato.stato\n                }));\n              }\n            }, index, false, {\n              fileName: _jsxFileName,\n              lineNumber: 596,\n              columnNumber: 19\n            }, this)) : /*#__PURE__*/_jsxDEV(Typography, {\n              variant: \"body2\",\n              children: \"Nessuno stato disponibile\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 608,\n              columnNumber: 19\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 594,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 592,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 565,\n        columnNumber: 11\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 560,\n      columnNumber: 7\n    }, this);\n  };\n  return /*#__PURE__*/_jsxDEV(Box, {\n    className: \"cavi-page\",\n    children: loading ? /*#__PURE__*/_jsxDEV(Box, {\n      sx: {\n        display: 'flex',\n        flexDirection: 'column',\n        alignItems: 'center',\n        mt: 4\n      },\n      children: [/*#__PURE__*/_jsxDEV(CircularProgress, {\n        size: 40\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 622,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(Typography, {\n        sx: {\n          mt: 2\n        },\n        children: \"Caricamento cavi...\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 623,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(Button, {\n        variant: \"outlined\",\n        color: \"primary\",\n        onClick: () => window.location.reload(),\n        sx: {\n          mt: 2\n        },\n        children: \"Ricarica la pagina\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 624,\n        columnNumber: 11\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 621,\n      columnNumber: 9\n    }, this) : error ? /*#__PURE__*/_jsxDEV(Box, {\n      children: [/*#__PURE__*/_jsxDEV(Alert, {\n        severity: \"error\",\n        sx: {\n          mb: 2\n        },\n        children: [error, error.includes('Network Error') && /*#__PURE__*/_jsxDEV(Typography, {\n          variant: \"body2\",\n          sx: {\n            mt: 1\n          },\n          children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n            children: \"Suggerimento:\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 639,\n            columnNumber: 17\n          }, this), \" Verifica che il server backend sia in esecuzione sulla porta 8001.\", /*#__PURE__*/_jsxDEV(\"br\", {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 640,\n            columnNumber: 17\n          }, this), \"Puoi avviare il backend eseguendo il file \", /*#__PURE__*/_jsxDEV(\"code\", {\n            children: \"run_system.py\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 641,\n            columnNumber: 59\n          }, this), \" nella cartella principale del progetto.\"]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 638,\n          columnNumber: 15\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 635,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(Box, {\n        sx: {\n          display: 'flex',\n          gap: 2\n        },\n        children: /*#__PURE__*/_jsxDEV(Button, {\n          variant: \"contained\",\n          className: \"primary-button\",\n          onClick: () => window.location.reload(),\n          children: \"Ricarica la pagina\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 646,\n          columnNumber: 13\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 645,\n        columnNumber: 11\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 634,\n      columnNumber: 9\n    }, this) : /*#__PURE__*/_jsxDEV(Box, {\n      children: [renderStatsPanel(), /*#__PURE__*/_jsxDEV(Box, {\n        sx: {\n          mt: 4\n        },\n        children: /*#__PURE__*/_jsxDEV(Box, {\n          sx: {\n            display: 'flex',\n            justifyContent: 'space-between',\n            alignItems: 'center',\n            mb: 2\n          },\n          children: [/*#__PURE__*/_jsxDEV(Typography, {\n            variant: \"h5\",\n            children: [\"Cavi Attivi \", caviAttivi.length > 0 ? `(${caviAttivi.length})` : '']\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 665,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(Button, {\n            variant: \"outlined\",\n            size: \"small\",\n            onClick: () => window.location.reload(),\n            startIcon: loading ? /*#__PURE__*/_jsxDEV(CircularProgress, {\n              size: 20\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 672,\n              columnNumber: 38\n            }, this) : /*#__PURE__*/_jsxDEV(RefreshIcon, {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 672,\n              columnNumber: 71\n            }, this),\n            disabled: loading,\n            children: \"Aggiorna\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 668,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 664,\n          columnNumber: 13\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 663,\n        columnNumber: 11\n      }, this), caviAttivi.length > 0 ? /*#__PURE__*/_jsxDEV(Box, {\n        sx: {\n          mb: 2\n        },\n        children: [process.env.NODE_ENV === 'development' && /*#__PURE__*/_jsxDEV(Box, {\n          sx: {\n            mb: 2,\n            p: 1,\n            bgcolor: '#f0f0f0',\n            borderRadius: 1,\n            fontSize: '0.8rem',\n            fontFamily: 'monospace',\n            display: 'none'\n          },\n          children: Object.keys(caviAttivi[0]).map(key => /*#__PURE__*/_jsxDEV(\"div\", {\n            children: [key, \": \", JSON.stringify(caviAttivi[0][key])]\n          }, key, true, {\n            fileName: _jsxFileName,\n            lineNumber: 686,\n            columnNumber: 21\n          }, this))\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 684,\n          columnNumber: 17\n        }, this), /*#__PURE__*/_jsxDEV(CaviFilterableTable, {\n          cavi: caviAttivi,\n          loading: loading,\n          onFilteredDataChange: filteredData => console.log('Cavi attivi filtrati:', filteredData.length),\n          revisioneCorrente: ((_caviAttivi$ = caviAttivi[0]) === null || _caviAttivi$ === void 0 ? void 0 : _caviAttivi$.revisione_ufficiale) || ((_caviAttivi$2 = caviAttivi[0]) === null || _caviAttivi$2 === void 0 ? void 0 : _caviAttivi$2.revisione) || ((_caviAttivi$3 = caviAttivi[0]) === null || _caviAttivi$3 === void 0 ? void 0 : _caviAttivi$3.rev)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 690,\n          columnNumber: 15\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 681,\n        columnNumber: 13\n      }, this) : /*#__PURE__*/_jsxDEV(Alert, {\n        severity: \"info\",\n        sx: {\n          mb: 2\n        },\n        children: \"Nessun cavo attivo trovato. I cavi attivi appariranno qui.\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 698,\n        columnNumber: 13\n      }, this), /*#__PURE__*/_jsxDEV(Box, {\n        sx: {\n          mt: 4\n        },\n        children: [/*#__PURE__*/_jsxDEV(Box, {\n          sx: {\n            display: 'flex',\n            justifyContent: 'space-between',\n            alignItems: 'center',\n            mb: 2\n          },\n          children: [/*#__PURE__*/_jsxDEV(Typography, {\n            variant: \"h5\",\n            children: [\"Cavi Spare \", caviSpare.length > 0 ? `(${caviSpare.length})` : '']\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 706,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(Button, {\n            variant: \"outlined\",\n            size: \"small\",\n            onClick: () => window.location.reload(),\n            startIcon: loading ? /*#__PURE__*/_jsxDEV(CircularProgress, {\n              size: 20\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 713,\n              columnNumber: 38\n            }, this) : /*#__PURE__*/_jsxDEV(RefreshIcon, {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 713,\n              columnNumber: 71\n            }, this),\n            disabled: loading,\n            children: \"Aggiorna\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 709,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 705,\n          columnNumber: 13\n        }, this), caviSpare.length > 0 ? /*#__PURE__*/_jsxDEV(CaviFilterableTable, {\n          cavi: caviSpare,\n          loading: loading,\n          onFilteredDataChange: filteredData => console.log('Cavi spare filtrati:', filteredData.length)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 720,\n          columnNumber: 15\n        }, this) : /*#__PURE__*/_jsxDEV(Alert, {\n          severity: \"info\",\n          sx: {\n            mb: 2\n          },\n          children: \"Nessun cavo SPARE trovato. I cavi marcati come SPARE appariranno qui.\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 726,\n          columnNumber: 15\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 704,\n        columnNumber: 11\n      }, this), renderDetailsDialog(), /*#__PURE__*/_jsxDEV(Dialog, {\n        open: openEliminaCavoDialog,\n        onClose: () => setOpenEliminaCavoDialog(false),\n        fullWidth: true,\n        maxWidth: \"md\",\n        children: /*#__PURE__*/_jsxDEV(PosaCaviCollegamenti, {\n          cantiereId: cantiereId,\n          onSuccess: message => {\n            // Chiudi il dialogo\n            setOpenEliminaCavoDialog(false);\n\n            // Se c'è un messaggio, è un'operazione completata con successo\n            if (message) {\n              // Mostra un messaggio di successo\n              console.log('Operazione completata:', message);\n              // Mostra un messaggio all'utente\n              alert(message);\n              // Ricarica i dati dopo un ritardo per dare tempo al database di aggiornarsi\n              setTimeout(() => {\n                console.log('Ricaricamento dati dopo operazione...');\n                try {\n                  // Ricarica i dati invece di ricaricare la pagina\n                  fetchCavi();\n                } catch (error) {\n                  console.error('Errore durante il ricaricamento dei dati:', error);\n                  // Se fallisce, prova a ricaricare la pagina\n                  window.location.reload();\n                }\n              }, 1000);\n            } else {\n              // È un'operazione annullata, non mostrare messaggi\n              console.log('Operazione annullata dall\\'utente');\n            }\n          },\n          onError: message => {\n            // Mostra un messaggio di errore\n            console.error('Errore durante l\\'eliminazione del cavo:', message);\n            // Mostra un alert all'utente\n            alert(`Errore: ${message}`);\n            // Chiudi il dialogo\n            setOpenEliminaCavoDialog(false);\n            // Ricarica comunque i dati per assicurarsi che la vista sia aggiornata\n            fetchCavi();\n          },\n          initialOption: \"eliminaCavo\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 744,\n          columnNumber: 13\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 738,\n        columnNumber: 11\n      }, this), openModificaCavoDialog && console.log('VisualizzaCaviPage - cantiereId prima di aprire il dialog:', cantiereId), /*#__PURE__*/_jsxDEV(Dialog, {\n        open: openModificaCavoDialog,\n        onClose: () => setOpenModificaCavoDialog(false),\n        fullWidth: true,\n        maxWidth: \"md\",\n        children: /*#__PURE__*/_jsxDEV(PosaCaviCollegamenti, {\n          cantiereId: cantiereId,\n          onSuccess: message => {\n            // Chiudi il dialogo\n            setOpenModificaCavoDialog(false);\n\n            // Se c'è un messaggio, è un'operazione completata con successo\n            if (message) {\n              // Mostra un messaggio di successo\n              console.log('Operazione completata:', message);\n              // Mostra un messaggio all'utente\n              alert(message);\n              // Ricarica i dati immediatamente\n              console.log('Ricaricamento dati dopo operazione...');\n              // Ricarica i dati con un ritardo per dare tempo al database di aggiornarsi\n              setTimeout(() => {\n                try {\n                  fetchCavi();\n                } catch (error) {\n                  console.error('Errore durante il ricaricamento dei dati:', error);\n                  // Se fallisce, prova a ricaricare la pagina\n                  window.location.reload();\n                }\n              }, 1000);\n            } else {\n              // È un'operazione annullata, non mostrare messaggi\n              console.log('Operazione annullata dall\\'utente');\n            }\n          },\n          onError: message => {\n            // Mostra un messaggio di errore\n            console.error('Errore durante la modifica del cavo:', message);\n            // Mostra un alert all'utente\n            alert(`Errore: ${message}`);\n            // Chiudi il dialogo\n            setOpenModificaCavoDialog(false);\n            // Ricarica comunque i dati per assicurarsi che la vista sia aggiornata\n            console.log('Ricaricamento dati dopo errore...');\n            fetchCavi();\n          },\n          initialOption: \"modificaCavo\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 797,\n          columnNumber: 13\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 791,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(Dialog, {\n        open: openAggiungiCavoDialog,\n        onClose: () => setOpenAggiungiCavoDialog(false),\n        fullWidth: true,\n        maxWidth: \"md\",\n        children: [/*#__PURE__*/_jsxDEV(DialogTitle, {\n          children: \"Aggiungi Nuovo Cavo\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 848,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(DialogContent, {\n          children: /*#__PURE__*/_jsxDEV(Box, {\n            sx: {\n              mt: 1\n            },\n            children: /*#__PURE__*/_jsxDEV(AggiungiCavoForm, {\n              cantiereId: cantiereId,\n              onSuccess: message => {\n                // Chiudi il dialogo\n                setOpenAggiungiCavoDialog(false);\n                // Mostra un messaggio di successo\n                alert(message);\n                // Ricarica i dati dopo un ritardo per dare tempo al database di aggiornarsi\n                setTimeout(() => {\n                  console.log('Ricaricamento dati dopo operazione...');\n                  try {\n                    // Ricarica i dati invece di ricaricare la pagina\n                    fetchCavi();\n                  } catch (error) {\n                    console.error('Errore durante il ricaricamento dei dati:', error);\n                    // Se fallisce, prova a ricaricare la pagina immediatamente\n                    console.log('Tentativo di ricaricamento della pagina...');\n                    window.location.reload();\n                  }\n                }, 1000);\n              },\n              onError: message => {\n                // Mostra un messaggio di errore\n                console.error('Errore durante l\\'aggiunta del cavo:', message);\n                // Mostra un alert all'utente\n                alert(`Errore: ${message}`);\n              },\n              isDialog: true\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 851,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 850,\n            columnNumber: 15\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 849,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(DialogActions, {\n          children: /*#__PURE__*/_jsxDEV(Button, {\n            onClick: () => setOpenAggiungiCavoDialog(false),\n            children: \"Annulla\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 883,\n            columnNumber: 15\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 882,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 842,\n        columnNumber: 11\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 656,\n      columnNumber: 9\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 619,\n    columnNumber: 5\n  }, this);\n};\n_s(VisualizzaCaviPage, \"tvZzodNHDIdbAtiErryuJ2G6WY8=\", false, function () {\n  return [useAuth, useGlobalContext, useNavigate];\n});\n_c = VisualizzaCaviPage;\nexport default VisualizzaCaviPage;\nvar _c;\n$RefreshReg$(_c, \"VisualizzaCaviPage\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "Box", "Typography", "Paper", "<PERSON><PERSON>", "Grid", "Card", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "IconButton", "Chip", "CircularProgress", "LinearProgress", "Dialog", "DialogTitle", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "DialogActions", "InfoIcon", "RefreshIcon", "useNavigate", "useAuth", "useGlobalContext", "PosaCaviCollegamenti", "caviService", "AggiungiCavoForm", "normalizeInstallationStatus", "CaviFilterableTable", "jsxDEV", "_jsxDEV", "VisualizzaCaviPage", "_s", "_caviAttivi$", "_caviAttivi$2", "_caviAttivi$3", "isImpersonating", "user", "openEliminaCavoDialog", "setOpenEliminaCavoDialog", "openModificaCavoDialog", "setOpenModificaCavoDialog", "openAggiungiCavoDialog", "setOpenAggiungiCavoDialog", "navigate", "cantiereId", "setCantiereId", "cantiereName", "setCantiereName", "caviAttivi", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "caviSpare", "setCaviSpare", "loading", "setLoading", "error", "setError", "selected<PERSON><PERSON><PERSON>", "setSelectedCavo", "detailsDialogOpen", "setDetailsDialogOpen", "stats", "setStats", "loadingStats", "setLoadingStats", "loadStatiInstallazione", "setStatiInstallazione", "filters", "setFilters", "stato_installazione", "tipologia", "sort_by", "sort_order", "statiInstallazione", "tipologieCavi", "setTipologieCavi", "<PERSON><PERSON><PERSON>", "console", "log", "cantiereIdToUse", "localStorage", "getItem", "attivi", "get<PERSON><PERSON>", "length", "attiviError", "caviSpareTra<PERSON>ttivi", "filter", "cavo", "modificato_manualmente", "spare", "getCaviSpare", "spareError", "standardError", "statsData", "getCaviStats", "statsError", "message", "setTimeout", "document", "body", "textContent", "includes", "window", "location", "reload", "fetchData", "token", "selectedCantiereId", "selectedCantiereName", "i", "key", "role", "cantiere_id", "toString", "cantiere_name", "setItem", "base64Url", "split", "base64", "replace", "jsonPayload", "decodeURIComponent", "atob", "map", "c", "charCodeAt", "slice", "join", "payload", "JSON", "parse", "e", "warn", "cantiereIdNum", "parseInt", "isNaN", "stati", "item", "stato", "tipologie", "tipo", "timeoutPromise", "Promise", "_", "reject", "Error", "caviPromise", "race", "caviError", "status", "data", "stack", "code", "name", "response", "statusText", "sparePromise", "err", "_err$response", "_err$response2", "_err$response3", "_err$response4", "_err$response5", "_err$response5$data", "errorMessage", "detail", "handleOpenDetails", "handleCloseDetails", "renderDetailsDialog", "open", "onClose", "max<PERSON><PERSON><PERSON>", "fullWidth", "children", "id_cavo", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "dividers", "container", "spacing", "xs", "md", "variant", "gutterBottom", "sx", "mb", "sistema", "utility", "colore_cavo", "n_conduttori", "sezione", "sh", "ubicazione_partenza", "utenza_partenza", "descrizione_utenza_partenza", "responsabile_partenza", "comanda_partenza", "ubicazione_arrivo", "utenza_arrivo", "descrizione_utenza_arrivo", "responsabile_arrivo", "comanda_arrivo", "metri_te<PERSON>ci", "metratura_reale", "colle<PERSON>nti", "id_bobina", "responsabile_posa", "comanda_posa", "Date", "timestamp", "toLocaleString", "onClick", "renderStatsPanel", "totali", "metrature", "p", "cavi_attivi", "cavi_spare", "cavi_totali", "metri_teorici_totali", "metri_reali_totali", "percentuale_completamento", "toFixed", "display", "alignItems", "mt", "width", "mr", "value", "height", "borderRadius", "min<PERSON><PERSON><PERSON>", "color", "flexWrap", "gap", "index", "label", "count", "size", "prev", "className", "flexDirection", "severity", "justifyContent", "startIcon", "disabled", "process", "env", "NODE_ENV", "bgcolor", "fontSize", "fontFamily", "Object", "keys", "stringify", "cavi", "onFilteredDataChange", "filteredData", "revisioneCorrente", "revisione_ufficiale", "revisione", "rev", "onSuccess", "alert", "onError", "initialOption", "isDialog", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/PycharmProjects/CMS/webapp/frontend/src/pages/cavi/VisualizzaCaviPage.js"], "sourcesContent": ["import React, { useState, useEffect } from 'react';\nimport {\n  Box,\n  Typography,\n  Paper,\n  Button,\n  Grid,\n  Card,\n  CardContent,\n  Alert,\n  IconButton,\n  Chip,\n  CircularProgress,\n  LinearProgress,\n  Dialog,\n  DialogTitle,\n  DialogContent,\n  DialogActions\n} from '@mui/material';\nimport InfoIcon from '@mui/icons-material/Info';\nimport RefreshIcon from '@mui/icons-material/Refresh';\nimport { useNavigate } from 'react-router-dom';\nimport { useAuth } from '../../context/AuthContext';\nimport { useGlobalContext } from '../../context/GlobalContext';\nimport PosaCaviCollegamenti from '../../components/cavi/PosaCaviCollegamenti';\nimport caviService from '../../services/caviService';\nimport AggiungiCavoForm from '../../components/cavi/AggiungiCavoForm';\nimport { normalizeInstallationStatus } from '../../utils/validationUtils';\nimport CaviFilterableTable from '../../components/cavi/CaviFilterableTable';\nimport './CaviPage.css';\n\nconst VisualizzaCaviPage = () => {\n  const { isImpersonating, user } = useAuth();\n  const { openEliminaCavoDialog, setOpenEliminaCavoDialog, openModificaCavoDialog, setOpenModificaCavoDialog, openAggiungiCavoDialog, setOpenAggiungiCavoDialog } = useGlobalContext();\n  const navigate = useNavigate();\n  const [cantiereId, setCantiereId] = useState(null);\n  const [cantiereName, setCantiereName] = useState('');\n  const [caviAttivi, setCaviAttivi] = useState([]);\n  const [caviSpare, setCaviSpare] = useState([]);\n  const [loading, setLoading] = useState(true);\n  const [error, setError] = useState(null);\n  // Rimosso stato viewMode\n\n  // Stato per il dialogo dei dettagli del cavo\n  const [selectedCavo, setSelectedCavo] = useState(null);\n  const [detailsDialogOpen, setDetailsDialogOpen] = useState(false);\n\n  // Stato per le statistiche\n  const [stats, setStats] = useState(null);\n  const [loadingStats, setLoadingStats] = useState(false);\n\n  // Rimosso stato per il debug\n\n  // Funzione per caricare gli stati di installazione disponibili\n  const loadStatiInstallazione = () => {\n    // Usa i valori dell'enum StatoInstallazione\n    setStatiInstallazione(['Installato', 'Da installare', 'In corso']);\n  };\n\n  // Stato per filtri e ordinamento\n  const [filters, setFilters] = useState({\n    stato_installazione: '',\n    tipologia: '',\n    sort_by: '',\n    sort_order: 'asc'\n  });\n\n  // Opzioni per i filtri\n  const [statiInstallazione, setStatiInstallazione] = useState([]);\n  const [tipologieCavi, setTipologieCavi] = useState([]);\n\n  // Rimossa funzione di debug\n\n  // Funzione per caricare i cavi\n  const fetchCavi = async () => {\n    try {\n      setLoading(true);\n      console.log('Caricamento cavi per cantiere:', cantiereId);\n\n      // Verifica che cantiereId sia valido\n      if (!cantiereId) {\n        console.error('fetchCavi: cantiereId non valido:', cantiereId);\n        setError('ID cantiere non valido o mancante. Ricarica la pagina.');\n        setLoading(false);\n        return;\n      }\n\n      // Recupera il cantiereId dal localStorage come fallback\n      let cantiereIdToUse = cantiereId;\n      if (!cantiereIdToUse) {\n        cantiereIdToUse = localStorage.getItem('selectedCantiereId');\n        console.log('Usando cantiereId dal localStorage:', cantiereIdToUse);\n        if (!cantiereIdToUse) {\n          console.error('Impossibile trovare un ID cantiere valido');\n          setError('ID cantiere non trovato. Ricarica la pagina.');\n          setLoading(false);\n          return;\n        }\n      }\n\n      // Carica i cavi attivi\n      console.log('Caricamento cavi attivi (tipo_cavo=0)...');\n      let attivi = [];\n      try {\n        attivi = await caviService.getCavi(cantiereIdToUse, 0, filters);\n        console.log('Cavi attivi caricati:', attivi ? attivi.length : 0);\n      } catch (attiviError) {\n        console.error('Errore nel caricamento dei cavi attivi:', attiviError);\n        // Continua con un array vuoto\n        attivi = [];\n      }\n\n      // Verifica se ci sono cavi con modificato_manualmente = 3 tra i cavi attivi\n      if (attivi && attivi.length > 0) {\n        const caviSpareTraAttivi = attivi.filter(cavo => cavo.modificato_manualmente === 3);\n        if (caviSpareTraAttivi.length > 0) {\n          console.error('ERRORE: Trovati cavi con modificato_manualmente = 3 tra i cavi attivi:', caviSpareTraAttivi);\n        }\n      }\n\n      setCaviAttivi(attivi || []);\n\n      // Carica i cavi SPARE con la nuova funzione dedicata\n      let spare = [];\n      try {\n        console.log('Caricamento cavi SPARE con funzione dedicata...');\n        spare = await caviService.getCaviSpare(cantiereIdToUse);\n        console.log('Cavi SPARE caricati con funzione dedicata:', spare ? spare.length : 0);\n        if (spare && spare.length > 0) {\n          console.log('Primo cavo SPARE:', spare[0]);\n        }\n      } catch (spareError) {\n        console.error('Errore nel caricamento dei cavi SPARE con funzione dedicata:', spareError);\n        // Se fallisce, prova con il metodo standard\n        try {\n          console.log('Tentativo con metodo standard...');\n          spare = await caviService.getCavi(cantiereIdToUse, 3);\n          console.log('Cavi SPARE caricati con metodo standard:', spare ? spare.length : 0);\n        } catch (standardError) {\n          console.error('Errore anche con metodo standard:', standardError);\n          // Continua con un array vuoto\n          spare = [];\n        }\n      }\n      setCaviSpare(spare || []);\n\n      // Carica le statistiche\n      try {\n        console.log('Caricamento statistiche...');\n        const statsData = await caviService.getCaviStats(cantiereIdToUse);\n        console.log('Statistiche caricate:', statsData);\n        setStats(statsData);\n      } catch (statsError) {\n        console.error('Errore nel caricamento delle statistiche:', statsError);\n        // Continua senza statistiche\n        setStats({});\n      }\n\n      // Se siamo arrivati qui, rimuovi eventuali messaggi di errore precedenti\n      setError('');\n    } catch (error) {\n      console.error('Errore generale nel caricamento dei cavi:', error);\n      setError(`Errore nel caricamento dei cavi: ${error.message || 'Errore sconosciuto'}`);\n\n      // Prova a ricaricare la pagina dopo un ritardo se l'errore persiste\n      setTimeout(() => {\n        // Verifica se siamo ancora in errore\n        if (document.body.textContent.includes('Errore nel caricamento dei cavi')) {\n          console.log('Errore persistente, tentativo di ricaricamento della pagina...');\n          window.location.reload();\n        }\n      }, 5000); // 5 secondi di ritardo\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  // Carica i dati del cantiere e dei cavi\n  useEffect(() => {\n    // Carica gli stati di installazione all'avvio\n    loadStatiInstallazione();\n\n    const fetchData = async () => {\n      try {\n        console.log('Inizializzazione VisualizzaCaviPage...');\n\n        // Verifica che l'utente sia autenticato\n        const token = localStorage.getItem('token');\n        console.log('Token presente:', !!token);\n        if (!token) {\n          setError('Sessione scaduta. Effettua nuovamente il login.');\n          setLoading(false);\n          return;\n        }\n\n        // Recupera l'ID del cantiere selezionato dal localStorage\n        let selectedCantiereId = localStorage.getItem('selectedCantiereId');\n        let selectedCantiereName = localStorage.getItem('selectedCantiereName');\n\n        console.log('Cantiere selezionato dal localStorage:', { selectedCantiereId, selectedCantiereName });\n        console.log('Dati utente:', user);\n\n        // Stampa tutti i dati nel localStorage per debug\n        console.log('DEBUG - Tutti i dati nel localStorage:');\n        for (let i = 0; i < localStorage.length; i++) {\n          const key = localStorage.key(i);\n          console.log(`${key}: ${localStorage.getItem(key)}`);\n        }\n\n        // SOLUZIONE DIRETTA: Ottieni l'ID del cantiere direttamente dal token JWT\n        if (user?.role === 'cantieri_user') {\n          console.log('Utente cantiere rilevato, tentativo di recupero ID cantiere dai dati utente');\n\n          // Verifica se l'utente ha un ID cantiere nei dati utente\n          if (user.cantiere_id) {\n            console.log('Trovato ID cantiere nei dati utente:', user.cantiere_id);\n            selectedCantiereId = user.cantiere_id.toString();\n            selectedCantiereName = user.cantiere_name || `Cantiere ${user.cantiere_id}`;\n\n            // Salva l'ID e il nome del cantiere nel localStorage\n            localStorage.setItem('selectedCantiereId', selectedCantiereId);\n            localStorage.setItem('selectedCantiereName', selectedCantiereName);\n            console.log('Salvato ID cantiere nel localStorage:', selectedCantiereId);\n          } else {\n            // Tentativo di recupero dal token JWT\n            try {\n              console.log('Tentativo di decodifica del token JWT per recuperare l\\'ID cantiere');\n              const token = localStorage.getItem('token');\n              if (token) {\n                // Decodifica il token JWT (senza verifica della firma)\n                const base64Url = token.split('.')[1];\n                const base64 = base64Url.replace(/-/g, '+').replace(/_/g, '/');\n                const jsonPayload = decodeURIComponent(atob(base64).split('').map(c => {\n                  return '%' + ('00' + c.charCodeAt(0).toString(16)).slice(-2);\n                }).join(''));\n\n                const payload = JSON.parse(jsonPayload);\n                console.log('Payload del token JWT:', payload);\n\n                if (payload.cantiere_id) {\n                  console.log('Trovato ID cantiere nel token JWT:', payload.cantiere_id);\n                  selectedCantiereId = payload.cantiere_id.toString();\n                  // Usa un nome generico se non disponibile\n                  selectedCantiereName = `Cantiere ${payload.cantiere_id}`;\n\n                  // Salva l'ID e il nome del cantiere nel localStorage\n                  localStorage.setItem('selectedCantiereId', selectedCantiereId);\n                  localStorage.setItem('selectedCantiereName', selectedCantiereName);\n                  console.log('Salvato ID cantiere nel localStorage:', selectedCantiereId);\n                }\n              }\n            } catch (e) {\n              console.error('Errore durante la decodifica del token JWT:', e);\n            }\n          }\n        }\n\n        // SOLUZIONE TEMPORANEA: Se ancora non abbiamo un ID cantiere, usa un valore hardcoded per debug\n        if (!selectedCantiereId || selectedCantiereId === 'undefined' || selectedCantiereId === 'null') {\n          console.warn('ATTENZIONE: Nessun ID cantiere trovato, utilizzo valore hardcoded per debug');\n          // Usa il primo cantiere disponibile (questo è solo per debug)\n          selectedCantiereId = '1'; // Sostituisci con un ID cantiere valido nel tuo database\n          selectedCantiereName = 'Cantiere Debug';\n\n          // Salva l'ID e il nome del cantiere nel localStorage\n          localStorage.setItem('selectedCantiereId', selectedCantiereId);\n          localStorage.setItem('selectedCantiereName', selectedCantiereName);\n          console.log('Salvato ID cantiere hardcoded nel localStorage:', selectedCantiereId);\n        }\n\n        // Verifica finale\n        if (!selectedCantiereId) {\n          setError('Nessun cantiere selezionato. Torna alla pagina dei cantieri.');\n          setLoading(false);\n          return;\n        }\n\n        // Verifica che l'ID del cantiere sia un numero valido\n        const cantiereIdNum = parseInt(selectedCantiereId, 10);\n        console.log('ID cantiere convertito a numero:', cantiereIdNum);\n        if (isNaN(cantiereIdNum)) {\n          setError(`ID cantiere non valido: ${selectedCantiereId}. Torna alla pagina dei cantieri.`);\n          setLoading(false);\n          return;\n        }\n\n        // Usa il numero convertito, non la stringa\n        setCantiereId(cantiereIdNum);\n        setCantiereName(selectedCantiereName || `Cantiere ${cantiereIdNum}`);\n\n        // Carica le statistiche dei cavi\n        try {\n          setLoadingStats(true);\n          console.log('Caricamento statistiche cavi per cantiere:', cantiereIdNum);\n          const statsData = await caviService.getCaviStats(cantiereIdNum);\n          console.log('Statistiche cavi caricate:', statsData);\n          setStats(statsData);\n\n          // Estrai gli stati di installazione e le tipologie per i filtri\n          if (statsData && statsData.stati) {\n            const stati = statsData.stati.map(item => item.stato).filter(stato => stato !== 'Non specificato');\n            setStatiInstallazione(stati);\n          }\n\n          if (statsData && statsData.tipologie) {\n            const tipologie = statsData.tipologie.map(item => item.tipologia).filter(tipo => tipo !== 'Non specificata');\n            setTipologieCavi(tipologie);\n          }\n\n          setLoadingStats(false);\n        } catch (statsError) {\n          console.error('Errore nel caricamento delle statistiche:', statsError);\n          setLoadingStats(false);\n          // Non interrompere il flusso se le statistiche falliscono\n        }\n\n        // Carica i cavi attivi con gestione degli errori migliorata\n        console.log('Caricamento cavi attivi per cantiere:', cantiereIdNum);\n        try {\n          // Imposta un timeout per evitare che la richiesta rimanga bloccata\n          const timeoutPromise = new Promise((_, reject) => {\n            setTimeout(() => reject(new Error('Timeout durante il caricamento dei cavi attivi')), 30000); // Aumentato a 30 secondi\n          });\n\n          // Esegui la richiesta con un timeout di sicurezza e applica i filtri\n          console.log('Iniziando chiamata API per cavi attivi con filtri:', filters);\n          const caviPromise = caviService.getCavi(cantiereIdNum, 0, filters);\n          const attivi = await Promise.race([caviPromise, timeoutPromise]);\n\n          console.log('Cavi attivi caricati:', attivi);\n          console.log('Numero di cavi attivi trovati:', attivi ? attivi.length : 0);\n          if (attivi && attivi.length > 0) {\n            console.log('Primo cavo attivo:', attivi[0]);\n          } else {\n            console.warn('Nessun cavo attivo trovato per il cantiere', cantiereIdNum);\n          }\n          setCaviAttivi(attivi || []);\n        } catch (caviError) {\n          console.error('Errore nel caricamento dei cavi attivi:', caviError);\n          console.error('Dettagli errore cavi attivi:', {\n            message: caviError.message,\n            status: caviError.status,\n            data: caviError.data,\n            stack: caviError.stack,\n            code: caviError.code,\n            name: caviError.name,\n            response: caviError.response ? {\n              status: caviError.response.status,\n              statusText: caviError.response.statusText,\n              data: caviError.response.data\n            } : 'No response'\n          });\n\n          // Non interrompere il flusso, continua con i cavi spare\n          setCaviAttivi([]);\n          console.warn('Continuazione del flusso dopo errore nei cavi attivi');\n\n          // Aggiungi un messaggio di errore visibile all'utente\n          setError(`Errore nel caricamento dei cavi attivi: ${caviError.message}. Controlla la console per maggiori dettagli.`);\n        }\n\n        // Carica i cavi spare con gestione degli errori migliorata\n        console.log('Caricamento cavi spare per cantiere:', cantiereIdNum);\n        try {\n          // Imposta un timeout per evitare che la richiesta rimanga bloccata\n          const timeoutPromise = new Promise((_, reject) => {\n            setTimeout(() => reject(new Error('Timeout durante il caricamento dei cavi spare')), 30000); // Aumentato a 30 secondi\n          });\n\n          // Esegui la richiesta con un timeout di sicurezza\n          console.log('Iniziando chiamata API per cavi spare...');\n          // Non applichiamo i filtri ai cavi spare, solo agli attivi\n          const sparePromise = caviService.getCavi(cantiereIdNum, 3);\n          const spare = await Promise.race([sparePromise, timeoutPromise]);\n\n          console.log('Cavi spare caricati:', spare);\n          console.log('Numero di cavi spare trovati:', spare ? spare.length : 0);\n          if (spare && spare.length > 0) {\n            console.log('Primo cavo spare:', spare[0]);\n          } else {\n            console.warn('Nessun cavo spare trovato per il cantiere', cantiereIdNum);\n          }\n          setCaviSpare(spare || []);\n        } catch (spareError) {\n          console.error('Errore nel caricamento dei cavi spare:', spareError);\n          console.error('Dettagli errore cavi spare:', {\n            message: spareError.message,\n            status: spareError.status,\n            data: spareError.data,\n            stack: spareError.stack,\n            code: spareError.code,\n            name: spareError.name,\n            response: spareError.response ? {\n              status: spareError.response.status,\n              statusText: spareError.response.statusText,\n              data: spareError.response.data\n            } : 'No response'\n          });\n\n          // Non interrompere il flusso, imposta un array vuoto\n          setCaviSpare([]);\n\n          // Aggiungi un messaggio di errore visibile all'utente se non c'è già un errore per i cavi attivi\n          if (!error) {\n            setError(`Errore nel caricamento dei cavi spare: ${spareError.message}. Controlla la console per maggiori dettagli.`);\n          }\n        }\n\n        // Se siamo arrivati qui, almeno abbiamo caricato l'interfaccia di base\n        setLoading(false);\n\n      } catch (err) {\n        console.error('Errore nel caricamento dei cavi:', err);\n        console.error('Dettagli errore generale:', {\n          message: err.message,\n          status: err.status || err.response?.status,\n          data: err.data || err.response?.data,\n          stack: err.stack\n        });\n\n        // Estrai il messaggio di errore dettagliato\n        let errorMessage = 'Errore sconosciuto';\n\n        if (err.message && err.message.includes('ID cantiere non valido')) {\n          errorMessage = err.message;\n        } else if (err.status === 401 || err.status === 403 ||\n                  err.response?.status === 401 || err.response?.status === 403) {\n          errorMessage = 'Sessione scaduta o non autorizzata. Effettua nuovamente il login.';\n        } else if (err.response?.data?.detail) {\n          // Estrai il messaggio di errore dettagliato dall'API\n          errorMessage = `Errore API: ${err.response.data.detail}`;\n        } else if (err.code === 'ERR_NETWORK') {\n          // Errore di rete\n          errorMessage = 'Network Error. Verifica che il backend sia in esecuzione e accessibile.';\n        } else if (err.message) {\n          errorMessage = err.message;\n        }\n\n        setError(`Impossibile caricare i cavi: ${errorMessage}. Riprova più tardi.`);\n\n        // Imposta array vuoti per evitare errori di rendering\n        setCaviAttivi([]);\n        setCaviSpare([]);\n      } finally {\n        setLoading(false);\n      }\n    };\n\n    fetchData();\n  }, [filters]); // Ricarica i dati quando cambiano i filtri\n\n  // I filtri sono ora gestiti dal componente CaviFilterableTable\n\n  // Funzione per aprire il dialogo dei dettagli del cavo\n  const handleOpenDetails = (cavo) => {\n    setSelectedCavo(cavo);\n    setDetailsDialogOpen(true);\n  };\n\n  // Funzione per chiudere il dialogo dei dettagli del cavo\n  const handleCloseDetails = () => {\n    setDetailsDialogOpen(false);\n    setSelectedCavo(null);\n  };\n\n  // Nessuna funzione di navigazione necessaria, tutto è gestito dal menu principale\n\n  // La visualizzazione dei cavi è ora gestita dal componente CaviFilterableTable\n\n  // Rimossa funzione handleViewModeChange\n\n  // Renderizza il dialogo dei dettagli del cavo\n  const renderDetailsDialog = () => {\n    if (!selectedCavo) return null;\n\n    return (\n      <Dialog open={detailsDialogOpen} onClose={handleCloseDetails} maxWidth=\"md\" fullWidth>\n        <DialogTitle>\n          Dettagli Cavo: {selectedCavo.id_cavo}\n        </DialogTitle>\n        <DialogContent dividers>\n          <Grid container spacing={2}>\n            <Grid item xs={12} md={6}>\n              <Typography variant=\"subtitle1\" gutterBottom>Informazioni Generali</Typography>\n              <Box sx={{ mb: 2 }}>\n                <Typography variant=\"body2\"><strong>Sistema:</strong> {selectedCavo.sistema || 'N/A'}</Typography>\n                <Typography variant=\"body2\"><strong>Utility:</strong> {selectedCavo.utility || 'N/A'}</Typography>\n                <Typography variant=\"body2\"><strong>Tipologia:</strong> {selectedCavo.tipologia || 'N/A'}</Typography>\n                <Typography variant=\"body2\"><strong>Colore:</strong> {selectedCavo.colore_cavo || 'N/A'}</Typography>\n                <Typography variant=\"body2\"><strong>N. Conduttori:</strong> {selectedCavo.n_conduttori || 'N/A'}</Typography>\n                <Typography variant=\"body2\"><strong>Sezione:</strong> {selectedCavo.sezione || 'N/A'}</Typography>\n                <Typography variant=\"body2\"><strong>SH:</strong> {selectedCavo.sh || 'N/A'}</Typography>\n              </Box>\n\n              <Typography variant=\"subtitle1\" gutterBottom>Partenza</Typography>\n              <Box sx={{ mb: 2 }}>\n                <Typography variant=\"body2\"><strong>Ubicazione:</strong> {selectedCavo.ubicazione_partenza || 'N/A'}</Typography>\n                <Typography variant=\"body2\"><strong>Utenza:</strong> {selectedCavo.utenza_partenza || 'N/A'}</Typography>\n                <Typography variant=\"body2\"><strong>Descrizione:</strong> {selectedCavo.descrizione_utenza_partenza || 'N/A'}</Typography>\n                <Typography variant=\"body2\"><strong>Responsabile:</strong> {selectedCavo.responsabile_partenza || 'N/A'}</Typography>\n                <Typography variant=\"body2\"><strong>Comanda:</strong> {selectedCavo.comanda_partenza || 'N/A'}</Typography>\n              </Box>\n            </Grid>\n\n            <Grid item xs={12} md={6}>\n              <Typography variant=\"subtitle1\" gutterBottom>Arrivo</Typography>\n              <Box sx={{ mb: 2 }}>\n                <Typography variant=\"body2\"><strong>Ubicazione:</strong> {selectedCavo.ubicazione_arrivo || 'N/A'}</Typography>\n                <Typography variant=\"body2\"><strong>Utenza:</strong> {selectedCavo.utenza_arrivo || 'N/A'}</Typography>\n                <Typography variant=\"body2\"><strong>Descrizione:</strong> {selectedCavo.descrizione_utenza_arrivo || 'N/A'}</Typography>\n                <Typography variant=\"body2\"><strong>Responsabile:</strong> {selectedCavo.responsabile_arrivo || 'N/A'}</Typography>\n                <Typography variant=\"body2\"><strong>Comanda:</strong> {selectedCavo.comanda_arrivo || 'N/A'}</Typography>\n              </Box>\n\n              <Typography variant=\"subtitle1\" gutterBottom>Installazione</Typography>\n              <Box sx={{ mb: 2 }}>\n                <Typography variant=\"body2\"><strong>Metri Teorici:</strong> {selectedCavo.metri_teorici || 'N/A'}</Typography>\n                <Typography variant=\"body2\"><strong>Metratura Reale:</strong> {selectedCavo.metratura_reale || '0'}</Typography>\n                <Typography variant=\"body2\"><strong>Stato:</strong> {normalizeInstallationStatus(selectedCavo.stato_installazione)}</Typography>\n                <Typography variant=\"body2\"><strong>Collegamenti:</strong> {selectedCavo.collegamenti || '0'}</Typography>\n                <Typography variant=\"body2\"><strong>Bobina:</strong> {selectedCavo.id_bobina || 'N/A'}</Typography>\n                <Typography variant=\"body2\"><strong>Responsabile Posa:</strong> {selectedCavo.responsabile_posa || 'N/A'}</Typography>\n                <Typography variant=\"body2\"><strong>Comanda Posa:</strong> {selectedCavo.comanda_posa || 'N/A'}</Typography>\n                <Typography variant=\"body2\"><strong>Ultimo Aggiornamento:</strong> {new Date(selectedCavo.timestamp).toLocaleString()}</Typography>\n              </Box>\n            </Grid>\n          </Grid>\n        </DialogContent>\n        <DialogActions>\n          <Button onClick={handleCloseDetails}>Chiudi</Button>\n        </DialogActions>\n      </Dialog>\n    );\n  };\n\n  // Il pannello dei filtri è ora gestito dal componente CaviFilterableTable\n\n  // Renderizza il pannello delle statistiche\n  const renderStatsPanel = () => {\n    // Verifica che stats sia definito e abbia la struttura attesa\n    if (!stats || !stats.totali || !stats.metrature || !stats.stati) {\n      return (\n        <Paper sx={{ mb: 3, p: 2 }}>\n          <Typography variant=\"h6\" gutterBottom>Statistiche</Typography>\n          {loadingStats ? (\n            <LinearProgress />\n          ) : (\n            <Typography variant=\"body2\">Nessuna statistica disponibile</Typography>\n          )}\n        </Paper>\n      );\n    }\n\n    // Valori predefiniti per evitare errori\n    const totali = stats.totali || { cavi_attivi: 0, cavi_spare: 0, cavi_totali: 0 };\n    const metrature = stats.metrature || { metri_teorici_totali: 0, metri_reali_totali: 0, percentuale_completamento: 0 };\n    const stati = stats.stati || [];\n\n    return (\n      <Paper sx={{ mb: 3, p: 2 }}>\n        <Typography variant=\"h6\" gutterBottom>Statistiche</Typography>\n        {loadingStats ? (\n          <LinearProgress />\n        ) : (\n          <Grid container spacing={2}>\n            <Grid item xs={12} md={4}>\n              <Typography variant=\"subtitle1\" gutterBottom>Totali</Typography>\n              <Typography variant=\"body2\">Cavi Attivi: {totali.cavi_attivi || 0}</Typography>\n              <Typography variant=\"body2\">Cavi Spare: {totali.cavi_spare || 0}</Typography>\n              <Typography variant=\"body2\">Totale Cavi: {totali.cavi_totali || 0}</Typography>\n              {/* Rimossa visualizzazione della revisione da qui, spostata nel titolo delle statistiche della tabella */}\n            </Grid>\n\n            <Grid item xs={12} md={4}>\n              <Typography variant=\"subtitle1\" gutterBottom>Metrature</Typography>\n              <Typography variant=\"body2\">Metri Teorici: {(metrature.metri_teorici_totali || 0).toFixed(2)}</Typography>\n              <Typography variant=\"body2\">Metri Posati: {(metrature.metri_reali_totali || 0).toFixed(2)}</Typography>\n              <Box sx={{ display: 'flex', alignItems: 'center', mt: 1 }}>\n                <Box sx={{ width: '100%', mr: 1 }}>\n                  <LinearProgress\n                    variant=\"determinate\"\n                    value={metrature.percentuale_completamento || 0}\n                    sx={{ height: 10, borderRadius: 5 }}\n                  />\n                </Box>\n                <Box sx={{ minWidth: 35 }}>\n                  <Typography variant=\"body2\" color=\"text.secondary\">{`${(metrature.percentuale_completamento || 0).toFixed(1)}%`}</Typography>\n                </Box>\n              </Box>\n            </Grid>\n\n            <Grid item xs={12} md={4}>\n              <Typography variant=\"subtitle1\" gutterBottom>Stati</Typography>\n              <Box sx={{ display: 'flex', flexWrap: 'wrap', gap: 1 }}>\n                {stati.length > 0 ? stati.map((stato, index) => (\n                  <Chip\n                    key={index}\n                    label={`${stato.stato || 'N/A'}: ${stato.count || 0}`}\n                    size=\"small\"\n                    onClick={() => {\n                      setFilters(prev => ({\n                        ...prev,\n                        stato_installazione: stato.stato === 'Non specificato' ? '' : stato.stato\n                      }));\n                    }}\n                  />\n                )) : (\n                  <Typography variant=\"body2\">Nessuno stato disponibile</Typography>\n                )}\n              </Box>\n            </Grid>\n          </Grid>\n        )}\n      </Paper>\n    );\n  };\n\n  return (\n    <Box className=\"cavi-page\">\n      {loading ? (\n        <Box sx={{ display: 'flex', flexDirection: 'column', alignItems: 'center', mt: 4 }}>\n          <CircularProgress size={40} />\n          <Typography sx={{ mt: 2 }}>Caricamento cavi...</Typography>\n          <Button\n            variant=\"outlined\"\n            color=\"primary\"\n            onClick={() => window.location.reload()}\n            sx={{ mt: 2 }}\n          >\n            Ricarica la pagina\n          </Button>\n        </Box>\n      ) : error ? (\n        <Box>\n          <Alert severity=\"error\" sx={{ mb: 2 }}>\n            {error}\n            {error.includes('Network Error') && (\n              <Typography variant=\"body2\" sx={{ mt: 1 }}>\n                <strong>Suggerimento:</strong> Verifica che il server backend sia in esecuzione sulla porta 8001.\n                <br />\n                Puoi avviare il backend eseguendo il file <code>run_system.py</code> nella cartella principale del progetto.\n              </Typography>\n            )}\n          </Alert>\n          <Box sx={{ display: 'flex', gap: 2 }}>\n            <Button\n              variant=\"contained\"\n              className=\"primary-button\"\n              onClick={() => window.location.reload()}\n            >\n              Ricarica la pagina\n            </Button>\n          </Box>\n        </Box>\n      ) : (\n        <Box>\n          {/* Rimosso il pulsante di refresh, gli utenti possono usare il refresh del browser */}\n\n          {/* Pannello delle statistiche */}\n          {renderStatsPanel()}\n\n          {/* Sezione Cavi Attivi */}\n          <Box sx={{ mt: 4 }}>\n            <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 2 }}>\n              <Typography variant=\"h5\">\n                Cavi Attivi {caviAttivi.length > 0 ? `(${caviAttivi.length})` : ''}\n              </Typography>\n              <Button\n                variant=\"outlined\"\n                size=\"small\"\n                onClick={() => window.location.reload()}\n                startIcon={loading ? <CircularProgress size={20} /> : <RefreshIcon />}\n                disabled={loading}\n              >\n                Aggiorna\n              </Button>\n            </Box>\n          </Box>\n\n          {caviAttivi.length > 0 ? (\n            <Box sx={{ mb: 2 }}>\n              {/* Debug: Mostra le proprietà del primo cavo per verificare il nome del campo revisione */}\n              {process.env.NODE_ENV === 'development' && (\n                <Box sx={{ mb: 2, p: 1, bgcolor: '#f0f0f0', borderRadius: 1, fontSize: '0.8rem', fontFamily: 'monospace', display: 'none' }}>\n                  {Object.keys(caviAttivi[0]).map(key => (\n                    <div key={key}>{key}: {JSON.stringify(caviAttivi[0][key])}</div>\n                  ))}\n                </Box>\n              )}\n              <CaviFilterableTable\n                cavi={caviAttivi}\n                loading={loading}\n                onFilteredDataChange={(filteredData) => console.log('Cavi attivi filtrati:', filteredData.length)}\n                revisioneCorrente={caviAttivi[0]?.revisione_ufficiale || caviAttivi[0]?.revisione || caviAttivi[0]?.rev}\n              />\n            </Box>\n          ) : (\n            <Alert severity=\"info\" sx={{ mb: 2 }}>\n              Nessun cavo attivo trovato. I cavi attivi appariranno qui.\n            </Alert>\n          )}\n\n          {/* Sezione Cavi Spare */}\n          <Box sx={{ mt: 4 }}>\n            <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 2 }}>\n              <Typography variant=\"h5\">\n                Cavi Spare {caviSpare.length > 0 ? `(${caviSpare.length})` : ''}\n              </Typography>\n              <Button\n                variant=\"outlined\"\n                size=\"small\"\n                onClick={() => window.location.reload()}\n                startIcon={loading ? <CircularProgress size={20} /> : <RefreshIcon />}\n                disabled={loading}\n              >\n                Aggiorna\n              </Button>\n            </Box>\n            {caviSpare.length > 0 ? (\n              <CaviFilterableTable\n                cavi={caviSpare}\n                loading={loading}\n                onFilteredDataChange={(filteredData) => console.log('Cavi spare filtrati:', filteredData.length)}\n              />\n            ) : (\n              <Alert severity=\"info\" sx={{ mb: 2 }}>\n                Nessun cavo SPARE trovato. I cavi marcati come SPARE appariranno qui.\n              </Alert>\n            )}\n          </Box>\n\n          {/* Rimossa sezione Debug */}\n\n          {/* Dialogo dei dettagli del cavo */}\n          {renderDetailsDialog()}\n\n          {/* Dialogo per l'eliminazione dei cavi */}\n          <Dialog\n            open={openEliminaCavoDialog}\n            onClose={() => setOpenEliminaCavoDialog(false)}\n            fullWidth\n            maxWidth=\"md\"\n          >\n            <PosaCaviCollegamenti\n              cantiereId={cantiereId}\n              onSuccess={(message) => {\n                // Chiudi il dialogo\n                setOpenEliminaCavoDialog(false);\n\n                // Se c'è un messaggio, è un'operazione completata con successo\n                if (message) {\n                  // Mostra un messaggio di successo\n                  console.log('Operazione completata:', message);\n                  // Mostra un messaggio all'utente\n                  alert(message);\n                  // Ricarica i dati dopo un ritardo per dare tempo al database di aggiornarsi\n                  setTimeout(() => {\n                    console.log('Ricaricamento dati dopo operazione...');\n                    try {\n                      // Ricarica i dati invece di ricaricare la pagina\n                      fetchCavi();\n                    } catch (error) {\n                      console.error('Errore durante il ricaricamento dei dati:', error);\n                      // Se fallisce, prova a ricaricare la pagina\n                      window.location.reload();\n                    }\n                  }, 1000);\n                } else {\n                  // È un'operazione annullata, non mostrare messaggi\n                  console.log('Operazione annullata dall\\'utente');\n                }\n              }}\n              onError={(message) => {\n                // Mostra un messaggio di errore\n                console.error('Errore durante l\\'eliminazione del cavo:', message);\n                // Mostra un alert all'utente\n                alert(`Errore: ${message}`);\n                // Chiudi il dialogo\n                setOpenEliminaCavoDialog(false);\n                // Ricarica comunque i dati per assicurarsi che la vista sia aggiornata\n                fetchCavi();\n              }}\n              initialOption=\"eliminaCavo\"\n            />\n          </Dialog>\n\n          {/* Dialogo per la modifica dei cavi */}\n          {/* Log del cantiereId prima di aprire il dialog */}\n          {openModificaCavoDialog && console.log('VisualizzaCaviPage - cantiereId prima di aprire il dialog:', cantiereId)}\n\n          <Dialog\n            open={openModificaCavoDialog}\n            onClose={() => setOpenModificaCavoDialog(false)}\n            fullWidth\n            maxWidth=\"md\"\n          >\n            <PosaCaviCollegamenti\n              cantiereId={cantiereId}\n              onSuccess={(message) => {\n                // Chiudi il dialogo\n                setOpenModificaCavoDialog(false);\n\n                // Se c'è un messaggio, è un'operazione completata con successo\n                if (message) {\n                  // Mostra un messaggio di successo\n                  console.log('Operazione completata:', message);\n                  // Mostra un messaggio all'utente\n                  alert(message);\n                  // Ricarica i dati immediatamente\n                  console.log('Ricaricamento dati dopo operazione...');\n                  // Ricarica i dati con un ritardo per dare tempo al database di aggiornarsi\n                  setTimeout(() => {\n                    try {\n                      fetchCavi();\n                    } catch (error) {\n                      console.error('Errore durante il ricaricamento dei dati:', error);\n                      // Se fallisce, prova a ricaricare la pagina\n                      window.location.reload();\n                    }\n                  }, 1000);\n                } else {\n                  // È un'operazione annullata, non mostrare messaggi\n                  console.log('Operazione annullata dall\\'utente');\n                }\n              }}\n              onError={(message) => {\n                // Mostra un messaggio di errore\n                console.error('Errore durante la modifica del cavo:', message);\n                // Mostra un alert all'utente\n                alert(`Errore: ${message}`);\n                // Chiudi il dialogo\n                setOpenModificaCavoDialog(false);\n                // Ricarica comunque i dati per assicurarsi che la vista sia aggiornata\n                console.log('Ricaricamento dati dopo errore...');\n                fetchCavi();\n              }}\n              initialOption=\"modificaCavo\"\n            />\n          </Dialog>\n\n          {/* Dialogo per l'aggiunta di un nuovo cavo */}\n          <Dialog\n            open={openAggiungiCavoDialog}\n            onClose={() => setOpenAggiungiCavoDialog(false)}\n            fullWidth\n            maxWidth=\"md\"\n          >\n            <DialogTitle>Aggiungi Nuovo Cavo</DialogTitle>\n            <DialogContent>\n              <Box sx={{ mt: 1 }}>\n                <AggiungiCavoForm\n                  cantiereId={cantiereId}\n                  onSuccess={(message) => {\n                    // Chiudi il dialogo\n                    setOpenAggiungiCavoDialog(false);\n                    // Mostra un messaggio di successo\n                    alert(message);\n                    // Ricarica i dati dopo un ritardo per dare tempo al database di aggiornarsi\n                    setTimeout(() => {\n                      console.log('Ricaricamento dati dopo operazione...');\n                      try {\n                        // Ricarica i dati invece di ricaricare la pagina\n                        fetchCavi();\n                      } catch (error) {\n                        console.error('Errore durante il ricaricamento dei dati:', error);\n                        // Se fallisce, prova a ricaricare la pagina immediatamente\n                        console.log('Tentativo di ricaricamento della pagina...');\n                        window.location.reload();\n                      }\n                    }, 1000);\n                  }}\n                  onError={(message) => {\n                    // Mostra un messaggio di errore\n                    console.error('Errore durante l\\'aggiunta del cavo:', message);\n                    // Mostra un alert all'utente\n                    alert(`Errore: ${message}`);\n                  }}\n                  isDialog={true}\n                />\n              </Box>\n            </DialogContent>\n            <DialogActions>\n              <Button onClick={() => setOpenAggiungiCavoDialog(false)}>Annulla</Button>\n            </DialogActions>\n          </Dialog>\n        </Box>\n      )}\n    </Box>\n  );\n};\n\nexport default VisualizzaCaviPage;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAClD,SACEC,GAAG,EACHC,UAAU,EACVC,KAAK,EACLC,MAAM,EACNC,IAAI,EACJC,IAAI,EACJC,WAAW,EACXC,KAAK,EACLC,UAAU,EACVC,IAAI,EACJC,gBAAgB,EAChBC,cAAc,EACdC,MAAM,EACNC,WAAW,EACXC,aAAa,EACbC,aAAa,QACR,eAAe;AACtB,OAAOC,QAAQ,MAAM,0BAA0B;AAC/C,OAAOC,WAAW,MAAM,6BAA6B;AACrD,SAASC,WAAW,QAAQ,kBAAkB;AAC9C,SAASC,OAAO,QAAQ,2BAA2B;AACnD,SAASC,gBAAgB,QAAQ,6BAA6B;AAC9D,OAAOC,oBAAoB,MAAM,4CAA4C;AAC7E,OAAOC,WAAW,MAAM,4BAA4B;AACpD,OAAOC,gBAAgB,MAAM,wCAAwC;AACrE,SAASC,2BAA2B,QAAQ,6BAA6B;AACzE,OAAOC,mBAAmB,MAAM,2CAA2C;AAC3E,OAAO,gBAAgB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAExB,MAAMC,kBAAkB,GAAGA,CAAA,KAAM;EAAAC,EAAA;EAAA,IAAAC,YAAA,EAAAC,aAAA,EAAAC,aAAA;EAC/B,MAAM;IAAEC,eAAe;IAAEC;EAAK,CAAC,GAAGf,OAAO,CAAC,CAAC;EAC3C,MAAM;IAAEgB,qBAAqB;IAAEC,wBAAwB;IAAEC,sBAAsB;IAAEC,yBAAyB;IAAEC,sBAAsB;IAAEC;EAA0B,CAAC,GAAGpB,gBAAgB,CAAC,CAAC;EACpL,MAAMqB,QAAQ,GAAGvB,WAAW,CAAC,CAAC;EAC9B,MAAM,CAACwB,UAAU,EAAEC,aAAa,CAAC,GAAG7C,QAAQ,CAAC,IAAI,CAAC;EAClD,MAAM,CAAC8C,YAAY,EAAEC,eAAe,CAAC,GAAG/C,QAAQ,CAAC,EAAE,CAAC;EACpD,MAAM,CAACgD,UAAU,EAAEC,aAAa,CAAC,GAAGjD,QAAQ,CAAC,EAAE,CAAC;EAChD,MAAM,CAACkD,SAAS,EAAEC,YAAY,CAAC,GAAGnD,QAAQ,CAAC,EAAE,CAAC;EAC9C,MAAM,CAACoD,OAAO,EAAEC,UAAU,CAAC,GAAGrD,QAAQ,CAAC,IAAI,CAAC;EAC5C,MAAM,CAACsD,KAAK,EAAEC,QAAQ,CAAC,GAAGvD,QAAQ,CAAC,IAAI,CAAC;EACxC;;EAEA;EACA,MAAM,CAACwD,YAAY,EAAEC,eAAe,CAAC,GAAGzD,QAAQ,CAAC,IAAI,CAAC;EACtD,MAAM,CAAC0D,iBAAiB,EAAEC,oBAAoB,CAAC,GAAG3D,QAAQ,CAAC,KAAK,CAAC;;EAEjE;EACA,MAAM,CAAC4D,KAAK,EAAEC,QAAQ,CAAC,GAAG7D,QAAQ,CAAC,IAAI,CAAC;EACxC,MAAM,CAAC8D,YAAY,EAAEC,eAAe,CAAC,GAAG/D,QAAQ,CAAC,KAAK,CAAC;;EAEvD;;EAEA;EACA,MAAMgE,sBAAsB,GAAGA,CAAA,KAAM;IACnC;IACAC,qBAAqB,CAAC,CAAC,YAAY,EAAE,eAAe,EAAE,UAAU,CAAC,CAAC;EACpE,CAAC;;EAED;EACA,MAAM,CAACC,OAAO,EAAEC,UAAU,CAAC,GAAGnE,QAAQ,CAAC;IACrCoE,mBAAmB,EAAE,EAAE;IACvBC,SAAS,EAAE,EAAE;IACbC,OAAO,EAAE,EAAE;IACXC,UAAU,EAAE;EACd,CAAC,CAAC;;EAEF;EACA,MAAM,CAACC,kBAAkB,EAAEP,qBAAqB,CAAC,GAAGjE,QAAQ,CAAC,EAAE,CAAC;EAChE,MAAM,CAACyE,aAAa,EAAEC,gBAAgB,CAAC,GAAG1E,QAAQ,CAAC,EAAE,CAAC;;EAEtD;;EAEA;EACA,MAAM2E,SAAS,GAAG,MAAAA,CAAA,KAAY;IAC5B,IAAI;MACFtB,UAAU,CAAC,IAAI,CAAC;MAChBuB,OAAO,CAACC,GAAG,CAAC,gCAAgC,EAAEjC,UAAU,CAAC;;MAEzD;MACA,IAAI,CAACA,UAAU,EAAE;QACfgC,OAAO,CAACtB,KAAK,CAAC,mCAAmC,EAAEV,UAAU,CAAC;QAC9DW,QAAQ,CAAC,wDAAwD,CAAC;QAClEF,UAAU,CAAC,KAAK,CAAC;QACjB;MACF;;MAEA;MACA,IAAIyB,eAAe,GAAGlC,UAAU;MAChC,IAAI,CAACkC,eAAe,EAAE;QACpBA,eAAe,GAAGC,YAAY,CAACC,OAAO,CAAC,oBAAoB,CAAC;QAC5DJ,OAAO,CAACC,GAAG,CAAC,qCAAqC,EAAEC,eAAe,CAAC;QACnE,IAAI,CAACA,eAAe,EAAE;UACpBF,OAAO,CAACtB,KAAK,CAAC,2CAA2C,CAAC;UAC1DC,QAAQ,CAAC,8CAA8C,CAAC;UACxDF,UAAU,CAAC,KAAK,CAAC;UACjB;QACF;MACF;;MAEA;MACAuB,OAAO,CAACC,GAAG,CAAC,0CAA0C,CAAC;MACvD,IAAII,MAAM,GAAG,EAAE;MACf,IAAI;QACFA,MAAM,GAAG,MAAMzD,WAAW,CAAC0D,OAAO,CAACJ,eAAe,EAAE,CAAC,EAAEZ,OAAO,CAAC;QAC/DU,OAAO,CAACC,GAAG,CAAC,uBAAuB,EAAEI,MAAM,GAAGA,MAAM,CAACE,MAAM,GAAG,CAAC,CAAC;MAClE,CAAC,CAAC,OAAOC,WAAW,EAAE;QACpBR,OAAO,CAACtB,KAAK,CAAC,yCAAyC,EAAE8B,WAAW,CAAC;QACrE;QACAH,MAAM,GAAG,EAAE;MACb;;MAEA;MACA,IAAIA,MAAM,IAAIA,MAAM,CAACE,MAAM,GAAG,CAAC,EAAE;QAC/B,MAAME,kBAAkB,GAAGJ,MAAM,CAACK,MAAM,CAACC,IAAI,IAAIA,IAAI,CAACC,sBAAsB,KAAK,CAAC,CAAC;QACnF,IAAIH,kBAAkB,CAACF,MAAM,GAAG,CAAC,EAAE;UACjCP,OAAO,CAACtB,KAAK,CAAC,wEAAwE,EAAE+B,kBAAkB,CAAC;QAC7G;MACF;MAEApC,aAAa,CAACgC,MAAM,IAAI,EAAE,CAAC;;MAE3B;MACA,IAAIQ,KAAK,GAAG,EAAE;MACd,IAAI;QACFb,OAAO,CAACC,GAAG,CAAC,iDAAiD,CAAC;QAC9DY,KAAK,GAAG,MAAMjE,WAAW,CAACkE,YAAY,CAACZ,eAAe,CAAC;QACvDF,OAAO,CAACC,GAAG,CAAC,4CAA4C,EAAEY,KAAK,GAAGA,KAAK,CAACN,MAAM,GAAG,CAAC,CAAC;QACnF,IAAIM,KAAK,IAAIA,KAAK,CAACN,MAAM,GAAG,CAAC,EAAE;UAC7BP,OAAO,CAACC,GAAG,CAAC,mBAAmB,EAAEY,KAAK,CAAC,CAAC,CAAC,CAAC;QAC5C;MACF,CAAC,CAAC,OAAOE,UAAU,EAAE;QACnBf,OAAO,CAACtB,KAAK,CAAC,8DAA8D,EAAEqC,UAAU,CAAC;QACzF;QACA,IAAI;UACFf,OAAO,CAACC,GAAG,CAAC,kCAAkC,CAAC;UAC/CY,KAAK,GAAG,MAAMjE,WAAW,CAAC0D,OAAO,CAACJ,eAAe,EAAE,CAAC,CAAC;UACrDF,OAAO,CAACC,GAAG,CAAC,0CAA0C,EAAEY,KAAK,GAAGA,KAAK,CAACN,MAAM,GAAG,CAAC,CAAC;QACnF,CAAC,CAAC,OAAOS,aAAa,EAAE;UACtBhB,OAAO,CAACtB,KAAK,CAAC,mCAAmC,EAAEsC,aAAa,CAAC;UACjE;UACAH,KAAK,GAAG,EAAE;QACZ;MACF;MACAtC,YAAY,CAACsC,KAAK,IAAI,EAAE,CAAC;;MAEzB;MACA,IAAI;QACFb,OAAO,CAACC,GAAG,CAAC,4BAA4B,CAAC;QACzC,MAAMgB,SAAS,GAAG,MAAMrE,WAAW,CAACsE,YAAY,CAAChB,eAAe,CAAC;QACjEF,OAAO,CAACC,GAAG,CAAC,uBAAuB,EAAEgB,SAAS,CAAC;QAC/ChC,QAAQ,CAACgC,SAAS,CAAC;MACrB,CAAC,CAAC,OAAOE,UAAU,EAAE;QACnBnB,OAAO,CAACtB,KAAK,CAAC,2CAA2C,EAAEyC,UAAU,CAAC;QACtE;QACAlC,QAAQ,CAAC,CAAC,CAAC,CAAC;MACd;;MAEA;MACAN,QAAQ,CAAC,EAAE,CAAC;IACd,CAAC,CAAC,OAAOD,KAAK,EAAE;MACdsB,OAAO,CAACtB,KAAK,CAAC,2CAA2C,EAAEA,KAAK,CAAC;MACjEC,QAAQ,CAAC,oCAAoCD,KAAK,CAAC0C,OAAO,IAAI,oBAAoB,EAAE,CAAC;;MAErF;MACAC,UAAU,CAAC,MAAM;QACf;QACA,IAAIC,QAAQ,CAACC,IAAI,CAACC,WAAW,CAACC,QAAQ,CAAC,iCAAiC,CAAC,EAAE;UACzEzB,OAAO,CAACC,GAAG,CAAC,gEAAgE,CAAC;UAC7EyB,MAAM,CAACC,QAAQ,CAACC,MAAM,CAAC,CAAC;QAC1B;MACF,CAAC,EAAE,IAAI,CAAC,CAAC,CAAC;IACZ,CAAC,SAAS;MACRnD,UAAU,CAAC,KAAK,CAAC;IACnB;EACF,CAAC;;EAED;EACApD,SAAS,CAAC,MAAM;IACd;IACA+D,sBAAsB,CAAC,CAAC;IAExB,MAAMyC,SAAS,GAAG,MAAAA,CAAA,KAAY;MAC5B,IAAI;QACF7B,OAAO,CAACC,GAAG,CAAC,wCAAwC,CAAC;;QAErD;QACA,MAAM6B,KAAK,GAAG3B,YAAY,CAACC,OAAO,CAAC,OAAO,CAAC;QAC3CJ,OAAO,CAACC,GAAG,CAAC,iBAAiB,EAAE,CAAC,CAAC6B,KAAK,CAAC;QACvC,IAAI,CAACA,KAAK,EAAE;UACVnD,QAAQ,CAAC,iDAAiD,CAAC;UAC3DF,UAAU,CAAC,KAAK,CAAC;UACjB;QACF;;QAEA;QACA,IAAIsD,kBAAkB,GAAG5B,YAAY,CAACC,OAAO,CAAC,oBAAoB,CAAC;QACnE,IAAI4B,oBAAoB,GAAG7B,YAAY,CAACC,OAAO,CAAC,sBAAsB,CAAC;QAEvEJ,OAAO,CAACC,GAAG,CAAC,wCAAwC,EAAE;UAAE8B,kBAAkB;UAAEC;QAAqB,CAAC,CAAC;QACnGhC,OAAO,CAACC,GAAG,CAAC,cAAc,EAAEzC,IAAI,CAAC;;QAEjC;QACAwC,OAAO,CAACC,GAAG,CAAC,wCAAwC,CAAC;QACrD,KAAK,IAAIgC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG9B,YAAY,CAACI,MAAM,EAAE0B,CAAC,EAAE,EAAE;UAC5C,MAAMC,GAAG,GAAG/B,YAAY,CAAC+B,GAAG,CAACD,CAAC,CAAC;UAC/BjC,OAAO,CAACC,GAAG,CAAC,GAAGiC,GAAG,KAAK/B,YAAY,CAACC,OAAO,CAAC8B,GAAG,CAAC,EAAE,CAAC;QACrD;;QAEA;QACA,IAAI,CAAA1E,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAE2E,IAAI,MAAK,eAAe,EAAE;UAClCnC,OAAO,CAACC,GAAG,CAAC,6EAA6E,CAAC;;UAE1F;UACA,IAAIzC,IAAI,CAAC4E,WAAW,EAAE;YACpBpC,OAAO,CAACC,GAAG,CAAC,sCAAsC,EAAEzC,IAAI,CAAC4E,WAAW,CAAC;YACrEL,kBAAkB,GAAGvE,IAAI,CAAC4E,WAAW,CAACC,QAAQ,CAAC,CAAC;YAChDL,oBAAoB,GAAGxE,IAAI,CAAC8E,aAAa,IAAI,YAAY9E,IAAI,CAAC4E,WAAW,EAAE;;YAE3E;YACAjC,YAAY,CAACoC,OAAO,CAAC,oBAAoB,EAAER,kBAAkB,CAAC;YAC9D5B,YAAY,CAACoC,OAAO,CAAC,sBAAsB,EAAEP,oBAAoB,CAAC;YAClEhC,OAAO,CAACC,GAAG,CAAC,uCAAuC,EAAE8B,kBAAkB,CAAC;UAC1E,CAAC,MAAM;YACL;YACA,IAAI;cACF/B,OAAO,CAACC,GAAG,CAAC,qEAAqE,CAAC;cAClF,MAAM6B,KAAK,GAAG3B,YAAY,CAACC,OAAO,CAAC,OAAO,CAAC;cAC3C,IAAI0B,KAAK,EAAE;gBACT;gBACA,MAAMU,SAAS,GAAGV,KAAK,CAACW,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC;gBACrC,MAAMC,MAAM,GAAGF,SAAS,CAACG,OAAO,CAAC,IAAI,EAAE,GAAG,CAAC,CAACA,OAAO,CAAC,IAAI,EAAE,GAAG,CAAC;gBAC9D,MAAMC,WAAW,GAAGC,kBAAkB,CAACC,IAAI,CAACJ,MAAM,CAAC,CAACD,KAAK,CAAC,EAAE,CAAC,CAACM,GAAG,CAACC,CAAC,IAAI;kBACrE,OAAO,GAAG,GAAG,CAAC,IAAI,GAAGA,CAAC,CAACC,UAAU,CAAC,CAAC,CAAC,CAACZ,QAAQ,CAAC,EAAE,CAAC,EAAEa,KAAK,CAAC,CAAC,CAAC,CAAC;gBAC9D,CAAC,CAAC,CAACC,IAAI,CAAC,EAAE,CAAC,CAAC;gBAEZ,MAAMC,OAAO,GAAGC,IAAI,CAACC,KAAK,CAACV,WAAW,CAAC;gBACvC5C,OAAO,CAACC,GAAG,CAAC,wBAAwB,EAAEmD,OAAO,CAAC;gBAE9C,IAAIA,OAAO,CAAChB,WAAW,EAAE;kBACvBpC,OAAO,CAACC,GAAG,CAAC,oCAAoC,EAAEmD,OAAO,CAAChB,WAAW,CAAC;kBACtEL,kBAAkB,GAAGqB,OAAO,CAAChB,WAAW,CAACC,QAAQ,CAAC,CAAC;kBACnD;kBACAL,oBAAoB,GAAG,YAAYoB,OAAO,CAAChB,WAAW,EAAE;;kBAExD;kBACAjC,YAAY,CAACoC,OAAO,CAAC,oBAAoB,EAAER,kBAAkB,CAAC;kBAC9D5B,YAAY,CAACoC,OAAO,CAAC,sBAAsB,EAAEP,oBAAoB,CAAC;kBAClEhC,OAAO,CAACC,GAAG,CAAC,uCAAuC,EAAE8B,kBAAkB,CAAC;gBAC1E;cACF;YACF,CAAC,CAAC,OAAOwB,CAAC,EAAE;cACVvD,OAAO,CAACtB,KAAK,CAAC,6CAA6C,EAAE6E,CAAC,CAAC;YACjE;UACF;QACF;;QAEA;QACA,IAAI,CAACxB,kBAAkB,IAAIA,kBAAkB,KAAK,WAAW,IAAIA,kBAAkB,KAAK,MAAM,EAAE;UAC9F/B,OAAO,CAACwD,IAAI,CAAC,6EAA6E,CAAC;UAC3F;UACAzB,kBAAkB,GAAG,GAAG,CAAC,CAAC;UAC1BC,oBAAoB,GAAG,gBAAgB;;UAEvC;UACA7B,YAAY,CAACoC,OAAO,CAAC,oBAAoB,EAAER,kBAAkB,CAAC;UAC9D5B,YAAY,CAACoC,OAAO,CAAC,sBAAsB,EAAEP,oBAAoB,CAAC;UAClEhC,OAAO,CAACC,GAAG,CAAC,iDAAiD,EAAE8B,kBAAkB,CAAC;QACpF;;QAEA;QACA,IAAI,CAACA,kBAAkB,EAAE;UACvBpD,QAAQ,CAAC,8DAA8D,CAAC;UACxEF,UAAU,CAAC,KAAK,CAAC;UACjB;QACF;;QAEA;QACA,MAAMgF,aAAa,GAAGC,QAAQ,CAAC3B,kBAAkB,EAAE,EAAE,CAAC;QACtD/B,OAAO,CAACC,GAAG,CAAC,kCAAkC,EAAEwD,aAAa,CAAC;QAC9D,IAAIE,KAAK,CAACF,aAAa,CAAC,EAAE;UACxB9E,QAAQ,CAAC,2BAA2BoD,kBAAkB,mCAAmC,CAAC;UAC1FtD,UAAU,CAAC,KAAK,CAAC;UACjB;QACF;;QAEA;QACAR,aAAa,CAACwF,aAAa,CAAC;QAC5BtF,eAAe,CAAC6D,oBAAoB,IAAI,YAAYyB,aAAa,EAAE,CAAC;;QAEpE;QACA,IAAI;UACFtE,eAAe,CAAC,IAAI,CAAC;UACrBa,OAAO,CAACC,GAAG,CAAC,4CAA4C,EAAEwD,aAAa,CAAC;UACxE,MAAMxC,SAAS,GAAG,MAAMrE,WAAW,CAACsE,YAAY,CAACuC,aAAa,CAAC;UAC/DzD,OAAO,CAACC,GAAG,CAAC,4BAA4B,EAAEgB,SAAS,CAAC;UACpDhC,QAAQ,CAACgC,SAAS,CAAC;;UAEnB;UACA,IAAIA,SAAS,IAAIA,SAAS,CAAC2C,KAAK,EAAE;YAChC,MAAMA,KAAK,GAAG3C,SAAS,CAAC2C,KAAK,CAACb,GAAG,CAACc,IAAI,IAAIA,IAAI,CAACC,KAAK,CAAC,CAACpD,MAAM,CAACoD,KAAK,IAAIA,KAAK,KAAK,iBAAiB,CAAC;YAClGzE,qBAAqB,CAACuE,KAAK,CAAC;UAC9B;UAEA,IAAI3C,SAAS,IAAIA,SAAS,CAAC8C,SAAS,EAAE;YACpC,MAAMA,SAAS,GAAG9C,SAAS,CAAC8C,SAAS,CAAChB,GAAG,CAACc,IAAI,IAAIA,IAAI,CAACpE,SAAS,CAAC,CAACiB,MAAM,CAACsD,IAAI,IAAIA,IAAI,KAAK,iBAAiB,CAAC;YAC5GlE,gBAAgB,CAACiE,SAAS,CAAC;UAC7B;UAEA5E,eAAe,CAAC,KAAK,CAAC;QACxB,CAAC,CAAC,OAAOgC,UAAU,EAAE;UACnBnB,OAAO,CAACtB,KAAK,CAAC,2CAA2C,EAAEyC,UAAU,CAAC;UACtEhC,eAAe,CAAC,KAAK,CAAC;UACtB;QACF;;QAEA;QACAa,OAAO,CAACC,GAAG,CAAC,uCAAuC,EAAEwD,aAAa,CAAC;QACnE,IAAI;UACF;UACA,MAAMQ,cAAc,GAAG,IAAIC,OAAO,CAAC,CAACC,CAAC,EAAEC,MAAM,KAAK;YAChD/C,UAAU,CAAC,MAAM+C,MAAM,CAAC,IAAIC,KAAK,CAAC,gDAAgD,CAAC,CAAC,EAAE,KAAK,CAAC,CAAC,CAAC;UAChG,CAAC,CAAC;;UAEF;UACArE,OAAO,CAACC,GAAG,CAAC,oDAAoD,EAAEX,OAAO,CAAC;UAC1E,MAAMgF,WAAW,GAAG1H,WAAW,CAAC0D,OAAO,CAACmD,aAAa,EAAE,CAAC,EAAEnE,OAAO,CAAC;UAClE,MAAMe,MAAM,GAAG,MAAM6D,OAAO,CAACK,IAAI,CAAC,CAACD,WAAW,EAAEL,cAAc,CAAC,CAAC;UAEhEjE,OAAO,CAACC,GAAG,CAAC,uBAAuB,EAAEI,MAAM,CAAC;UAC5CL,OAAO,CAACC,GAAG,CAAC,gCAAgC,EAAEI,MAAM,GAAGA,MAAM,CAACE,MAAM,GAAG,CAAC,CAAC;UACzE,IAAIF,MAAM,IAAIA,MAAM,CAACE,MAAM,GAAG,CAAC,EAAE;YAC/BP,OAAO,CAACC,GAAG,CAAC,oBAAoB,EAAEI,MAAM,CAAC,CAAC,CAAC,CAAC;UAC9C,CAAC,MAAM;YACLL,OAAO,CAACwD,IAAI,CAAC,4CAA4C,EAAEC,aAAa,CAAC;UAC3E;UACApF,aAAa,CAACgC,MAAM,IAAI,EAAE,CAAC;QAC7B,CAAC,CAAC,OAAOmE,SAAS,EAAE;UAClBxE,OAAO,CAACtB,KAAK,CAAC,yCAAyC,EAAE8F,SAAS,CAAC;UACnExE,OAAO,CAACtB,KAAK,CAAC,8BAA8B,EAAE;YAC5C0C,OAAO,EAAEoD,SAAS,CAACpD,OAAO;YAC1BqD,MAAM,EAAED,SAAS,CAACC,MAAM;YACxBC,IAAI,EAAEF,SAAS,CAACE,IAAI;YACpBC,KAAK,EAAEH,SAAS,CAACG,KAAK;YACtBC,IAAI,EAAEJ,SAAS,CAACI,IAAI;YACpBC,IAAI,EAAEL,SAAS,CAACK,IAAI;YACpBC,QAAQ,EAAEN,SAAS,CAACM,QAAQ,GAAG;cAC7BL,MAAM,EAAED,SAAS,CAACM,QAAQ,CAACL,MAAM;cACjCM,UAAU,EAAEP,SAAS,CAACM,QAAQ,CAACC,UAAU;cACzCL,IAAI,EAAEF,SAAS,CAACM,QAAQ,CAACJ;YAC3B,CAAC,GAAG;UACN,CAAC,CAAC;;UAEF;UACArG,aAAa,CAAC,EAAE,CAAC;UACjB2B,OAAO,CAACwD,IAAI,CAAC,sDAAsD,CAAC;;UAEpE;UACA7E,QAAQ,CAAC,2CAA2C6F,SAAS,CAACpD,OAAO,+CAA+C,CAAC;QACvH;;QAEA;QACApB,OAAO,CAACC,GAAG,CAAC,sCAAsC,EAAEwD,aAAa,CAAC;QAClE,IAAI;UACF;UACA,MAAMQ,cAAc,GAAG,IAAIC,OAAO,CAAC,CAACC,CAAC,EAAEC,MAAM,KAAK;YAChD/C,UAAU,CAAC,MAAM+C,MAAM,CAAC,IAAIC,KAAK,CAAC,+CAA+C,CAAC,CAAC,EAAE,KAAK,CAAC,CAAC,CAAC;UAC/F,CAAC,CAAC;;UAEF;UACArE,OAAO,CAACC,GAAG,CAAC,0CAA0C,CAAC;UACvD;UACA,MAAM+E,YAAY,GAAGpI,WAAW,CAAC0D,OAAO,CAACmD,aAAa,EAAE,CAAC,CAAC;UAC1D,MAAM5C,KAAK,GAAG,MAAMqD,OAAO,CAACK,IAAI,CAAC,CAACS,YAAY,EAAEf,cAAc,CAAC,CAAC;UAEhEjE,OAAO,CAACC,GAAG,CAAC,sBAAsB,EAAEY,KAAK,CAAC;UAC1Cb,OAAO,CAACC,GAAG,CAAC,+BAA+B,EAAEY,KAAK,GAAGA,KAAK,CAACN,MAAM,GAAG,CAAC,CAAC;UACtE,IAAIM,KAAK,IAAIA,KAAK,CAACN,MAAM,GAAG,CAAC,EAAE;YAC7BP,OAAO,CAACC,GAAG,CAAC,mBAAmB,EAAEY,KAAK,CAAC,CAAC,CAAC,CAAC;UAC5C,CAAC,MAAM;YACLb,OAAO,CAACwD,IAAI,CAAC,2CAA2C,EAAEC,aAAa,CAAC;UAC1E;UACAlF,YAAY,CAACsC,KAAK,IAAI,EAAE,CAAC;QAC3B,CAAC,CAAC,OAAOE,UAAU,EAAE;UACnBf,OAAO,CAACtB,KAAK,CAAC,wCAAwC,EAAEqC,UAAU,CAAC;UACnEf,OAAO,CAACtB,KAAK,CAAC,6BAA6B,EAAE;YAC3C0C,OAAO,EAAEL,UAAU,CAACK,OAAO;YAC3BqD,MAAM,EAAE1D,UAAU,CAAC0D,MAAM;YACzBC,IAAI,EAAE3D,UAAU,CAAC2D,IAAI;YACrBC,KAAK,EAAE5D,UAAU,CAAC4D,KAAK;YACvBC,IAAI,EAAE7D,UAAU,CAAC6D,IAAI;YACrBC,IAAI,EAAE9D,UAAU,CAAC8D,IAAI;YACrBC,QAAQ,EAAE/D,UAAU,CAAC+D,QAAQ,GAAG;cAC9BL,MAAM,EAAE1D,UAAU,CAAC+D,QAAQ,CAACL,MAAM;cAClCM,UAAU,EAAEhE,UAAU,CAAC+D,QAAQ,CAACC,UAAU;cAC1CL,IAAI,EAAE3D,UAAU,CAAC+D,QAAQ,CAACJ;YAC5B,CAAC,GAAG;UACN,CAAC,CAAC;;UAEF;UACAnG,YAAY,CAAC,EAAE,CAAC;;UAEhB;UACA,IAAI,CAACG,KAAK,EAAE;YACVC,QAAQ,CAAC,0CAA0CoC,UAAU,CAACK,OAAO,+CAA+C,CAAC;UACvH;QACF;;QAEA;QACA3C,UAAU,CAAC,KAAK,CAAC;MAEnB,CAAC,CAAC,OAAOwG,GAAG,EAAE;QAAA,IAAAC,aAAA,EAAAC,cAAA,EAAAC,cAAA,EAAAC,cAAA,EAAAC,cAAA,EAAAC,mBAAA;QACZvF,OAAO,CAACtB,KAAK,CAAC,kCAAkC,EAAEuG,GAAG,CAAC;QACtDjF,OAAO,CAACtB,KAAK,CAAC,2BAA2B,EAAE;UACzC0C,OAAO,EAAE6D,GAAG,CAAC7D,OAAO;UACpBqD,MAAM,EAAEQ,GAAG,CAACR,MAAM,MAAAS,aAAA,GAAID,GAAG,CAACH,QAAQ,cAAAI,aAAA,uBAAZA,aAAA,CAAcT,MAAM;UAC1CC,IAAI,EAAEO,GAAG,CAACP,IAAI,MAAAS,cAAA,GAAIF,GAAG,CAACH,QAAQ,cAAAK,cAAA,uBAAZA,cAAA,CAAcT,IAAI;UACpCC,KAAK,EAAEM,GAAG,CAACN;QACb,CAAC,CAAC;;QAEF;QACA,IAAIa,YAAY,GAAG,oBAAoB;QAEvC,IAAIP,GAAG,CAAC7D,OAAO,IAAI6D,GAAG,CAAC7D,OAAO,CAACK,QAAQ,CAAC,wBAAwB,CAAC,EAAE;UACjE+D,YAAY,GAAGP,GAAG,CAAC7D,OAAO;QAC5B,CAAC,MAAM,IAAI6D,GAAG,CAACR,MAAM,KAAK,GAAG,IAAIQ,GAAG,CAACR,MAAM,KAAK,GAAG,IACzC,EAAAW,cAAA,GAAAH,GAAG,CAACH,QAAQ,cAAAM,cAAA,uBAAZA,cAAA,CAAcX,MAAM,MAAK,GAAG,IAAI,EAAAY,cAAA,GAAAJ,GAAG,CAACH,QAAQ,cAAAO,cAAA,uBAAZA,cAAA,CAAcZ,MAAM,MAAK,GAAG,EAAE;UACtEe,YAAY,GAAG,mEAAmE;QACpF,CAAC,MAAM,KAAAF,cAAA,GAAIL,GAAG,CAACH,QAAQ,cAAAQ,cAAA,gBAAAC,mBAAA,GAAZD,cAAA,CAAcZ,IAAI,cAAAa,mBAAA,eAAlBA,mBAAA,CAAoBE,MAAM,EAAE;UACrC;UACAD,YAAY,GAAG,eAAeP,GAAG,CAACH,QAAQ,CAACJ,IAAI,CAACe,MAAM,EAAE;QAC1D,CAAC,MAAM,IAAIR,GAAG,CAACL,IAAI,KAAK,aAAa,EAAE;UACrC;UACAY,YAAY,GAAG,yEAAyE;QAC1F,CAAC,MAAM,IAAIP,GAAG,CAAC7D,OAAO,EAAE;UACtBoE,YAAY,GAAGP,GAAG,CAAC7D,OAAO;QAC5B;QAEAzC,QAAQ,CAAC,gCAAgC6G,YAAY,sBAAsB,CAAC;;QAE5E;QACAnH,aAAa,CAAC,EAAE,CAAC;QACjBE,YAAY,CAAC,EAAE,CAAC;MAClB,CAAC,SAAS;QACRE,UAAU,CAAC,KAAK,CAAC;MACnB;IACF,CAAC;IAEDoD,SAAS,CAAC,CAAC;EACb,CAAC,EAAE,CAACvC,OAAO,CAAC,CAAC,CAAC,CAAC;;EAEf;;EAEA;EACA,MAAMoG,iBAAiB,GAAI/E,IAAI,IAAK;IAClC9B,eAAe,CAAC8B,IAAI,CAAC;IACrB5B,oBAAoB,CAAC,IAAI,CAAC;EAC5B,CAAC;;EAED;EACA,MAAM4G,kBAAkB,GAAGA,CAAA,KAAM;IAC/B5G,oBAAoB,CAAC,KAAK,CAAC;IAC3BF,eAAe,CAAC,IAAI,CAAC;EACvB,CAAC;;EAED;;EAEA;;EAEA;;EAEA;EACA,MAAM+G,mBAAmB,GAAGA,CAAA,KAAM;IAChC,IAAI,CAAChH,YAAY,EAAE,OAAO,IAAI;IAE9B,oBACE3B,OAAA,CAACf,MAAM;MAAC2J,IAAI,EAAE/G,iBAAkB;MAACgH,OAAO,EAAEH,kBAAmB;MAACI,QAAQ,EAAC,IAAI;MAACC,SAAS;MAAAC,QAAA,gBACnFhJ,OAAA,CAACd,WAAW;QAAA8J,QAAA,GAAC,iBACI,EAACrH,YAAY,CAACsH,OAAO;MAAA;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACzB,CAAC,eACdrJ,OAAA,CAACb,aAAa;QAACmK,QAAQ;QAAAN,QAAA,eACrBhJ,OAAA,CAACvB,IAAI;UAAC8K,SAAS;UAACC,OAAO,EAAE,CAAE;UAAAR,QAAA,gBACzBhJ,OAAA,CAACvB,IAAI;YAACmI,IAAI;YAAC6C,EAAE,EAAE,EAAG;YAACC,EAAE,EAAE,CAAE;YAAAV,QAAA,gBACvBhJ,OAAA,CAAC1B,UAAU;cAACqL,OAAO,EAAC,WAAW;cAACC,YAAY;cAAAZ,QAAA,EAAC;YAAqB;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAY,CAAC,eAC/ErJ,OAAA,CAAC3B,GAAG;cAACwL,EAAE,EAAE;gBAAEC,EAAE,EAAE;cAAE,CAAE;cAAAd,QAAA,gBACjBhJ,OAAA,CAAC1B,UAAU;gBAACqL,OAAO,EAAC,OAAO;gBAAAX,QAAA,gBAAChJ,OAAA;kBAAAgJ,QAAA,EAAQ;gBAAQ;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,KAAC,EAAC1H,YAAY,CAACoI,OAAO,IAAI,KAAK;cAAA;gBAAAb,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAa,CAAC,eAClGrJ,OAAA,CAAC1B,UAAU;gBAACqL,OAAO,EAAC,OAAO;gBAAAX,QAAA,gBAAChJ,OAAA;kBAAAgJ,QAAA,EAAQ;gBAAQ;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,KAAC,EAAC1H,YAAY,CAACqI,OAAO,IAAI,KAAK;cAAA;gBAAAd,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAa,CAAC,eAClGrJ,OAAA,CAAC1B,UAAU;gBAACqL,OAAO,EAAC,OAAO;gBAAAX,QAAA,gBAAChJ,OAAA;kBAAAgJ,QAAA,EAAQ;gBAAU;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,KAAC,EAAC1H,YAAY,CAACa,SAAS,IAAI,KAAK;cAAA;gBAAA0G,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAa,CAAC,eACtGrJ,OAAA,CAAC1B,UAAU;gBAACqL,OAAO,EAAC,OAAO;gBAAAX,QAAA,gBAAChJ,OAAA;kBAAAgJ,QAAA,EAAQ;gBAAO;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,KAAC,EAAC1H,YAAY,CAACsI,WAAW,IAAI,KAAK;cAAA;gBAAAf,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAa,CAAC,eACrGrJ,OAAA,CAAC1B,UAAU;gBAACqL,OAAO,EAAC,OAAO;gBAAAX,QAAA,gBAAChJ,OAAA;kBAAAgJ,QAAA,EAAQ;gBAAc;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,KAAC,EAAC1H,YAAY,CAACuI,YAAY,IAAI,KAAK;cAAA;gBAAAhB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAa,CAAC,eAC7GrJ,OAAA,CAAC1B,UAAU;gBAACqL,OAAO,EAAC,OAAO;gBAAAX,QAAA,gBAAChJ,OAAA;kBAAAgJ,QAAA,EAAQ;gBAAQ;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,KAAC,EAAC1H,YAAY,CAACwI,OAAO,IAAI,KAAK;cAAA;gBAAAjB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAa,CAAC,eAClGrJ,OAAA,CAAC1B,UAAU;gBAACqL,OAAO,EAAC,OAAO;gBAAAX,QAAA,gBAAChJ,OAAA;kBAAAgJ,QAAA,EAAQ;gBAAG;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,KAAC,EAAC1H,YAAY,CAACyI,EAAE,IAAI,KAAK;cAAA;gBAAAlB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAa,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACrF,CAAC,eAENrJ,OAAA,CAAC1B,UAAU;cAACqL,OAAO,EAAC,WAAW;cAACC,YAAY;cAAAZ,QAAA,EAAC;YAAQ;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAY,CAAC,eAClErJ,OAAA,CAAC3B,GAAG;cAACwL,EAAE,EAAE;gBAAEC,EAAE,EAAE;cAAE,CAAE;cAAAd,QAAA,gBACjBhJ,OAAA,CAAC1B,UAAU;gBAACqL,OAAO,EAAC,OAAO;gBAAAX,QAAA,gBAAChJ,OAAA;kBAAAgJ,QAAA,EAAQ;gBAAW;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,KAAC,EAAC1H,YAAY,CAAC0I,mBAAmB,IAAI,KAAK;cAAA;gBAAAnB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAa,CAAC,eACjHrJ,OAAA,CAAC1B,UAAU;gBAACqL,OAAO,EAAC,OAAO;gBAAAX,QAAA,gBAAChJ,OAAA;kBAAAgJ,QAAA,EAAQ;gBAAO;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,KAAC,EAAC1H,YAAY,CAAC2I,eAAe,IAAI,KAAK;cAAA;gBAAApB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAa,CAAC,eACzGrJ,OAAA,CAAC1B,UAAU;gBAACqL,OAAO,EAAC,OAAO;gBAAAX,QAAA,gBAAChJ,OAAA;kBAAAgJ,QAAA,EAAQ;gBAAY;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,KAAC,EAAC1H,YAAY,CAAC4I,2BAA2B,IAAI,KAAK;cAAA;gBAAArB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAa,CAAC,eAC1HrJ,OAAA,CAAC1B,UAAU;gBAACqL,OAAO,EAAC,OAAO;gBAAAX,QAAA,gBAAChJ,OAAA;kBAAAgJ,QAAA,EAAQ;gBAAa;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,KAAC,EAAC1H,YAAY,CAAC6I,qBAAqB,IAAI,KAAK;cAAA;gBAAAtB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAa,CAAC,eACrHrJ,OAAA,CAAC1B,UAAU;gBAACqL,OAAO,EAAC,OAAO;gBAAAX,QAAA,gBAAChJ,OAAA;kBAAAgJ,QAAA,EAAQ;gBAAQ;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,KAAC,EAAC1H,YAAY,CAAC8I,gBAAgB,IAAI,KAAK;cAAA;gBAAAvB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAa,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACxG,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACF,CAAC,eAEPrJ,OAAA,CAACvB,IAAI;YAACmI,IAAI;YAAC6C,EAAE,EAAE,EAAG;YAACC,EAAE,EAAE,CAAE;YAAAV,QAAA,gBACvBhJ,OAAA,CAAC1B,UAAU;cAACqL,OAAO,EAAC,WAAW;cAACC,YAAY;cAAAZ,QAAA,EAAC;YAAM;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAY,CAAC,eAChErJ,OAAA,CAAC3B,GAAG;cAACwL,EAAE,EAAE;gBAAEC,EAAE,EAAE;cAAE,CAAE;cAAAd,QAAA,gBACjBhJ,OAAA,CAAC1B,UAAU;gBAACqL,OAAO,EAAC,OAAO;gBAAAX,QAAA,gBAAChJ,OAAA;kBAAAgJ,QAAA,EAAQ;gBAAW;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,KAAC,EAAC1H,YAAY,CAAC+I,iBAAiB,IAAI,KAAK;cAAA;gBAAAxB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAa,CAAC,eAC/GrJ,OAAA,CAAC1B,UAAU;gBAACqL,OAAO,EAAC,OAAO;gBAAAX,QAAA,gBAAChJ,OAAA;kBAAAgJ,QAAA,EAAQ;gBAAO;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,KAAC,EAAC1H,YAAY,CAACgJ,aAAa,IAAI,KAAK;cAAA;gBAAAzB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAa,CAAC,eACvGrJ,OAAA,CAAC1B,UAAU;gBAACqL,OAAO,EAAC,OAAO;gBAAAX,QAAA,gBAAChJ,OAAA;kBAAAgJ,QAAA,EAAQ;gBAAY;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,KAAC,EAAC1H,YAAY,CAACiJ,yBAAyB,IAAI,KAAK;cAAA;gBAAA1B,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAa,CAAC,eACxHrJ,OAAA,CAAC1B,UAAU;gBAACqL,OAAO,EAAC,OAAO;gBAAAX,QAAA,gBAAChJ,OAAA;kBAAAgJ,QAAA,EAAQ;gBAAa;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,KAAC,EAAC1H,YAAY,CAACkJ,mBAAmB,IAAI,KAAK;cAAA;gBAAA3B,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAa,CAAC,eACnHrJ,OAAA,CAAC1B,UAAU;gBAACqL,OAAO,EAAC,OAAO;gBAAAX,QAAA,gBAAChJ,OAAA;kBAAAgJ,QAAA,EAAQ;gBAAQ;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,KAAC,EAAC1H,YAAY,CAACmJ,cAAc,IAAI,KAAK;cAAA;gBAAA5B,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAa,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACtG,CAAC,eAENrJ,OAAA,CAAC1B,UAAU;cAACqL,OAAO,EAAC,WAAW;cAACC,YAAY;cAAAZ,QAAA,EAAC;YAAa;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAY,CAAC,eACvErJ,OAAA,CAAC3B,GAAG;cAACwL,EAAE,EAAE;gBAAEC,EAAE,EAAE;cAAE,CAAE;cAAAd,QAAA,gBACjBhJ,OAAA,CAAC1B,UAAU;gBAACqL,OAAO,EAAC,OAAO;gBAAAX,QAAA,gBAAChJ,OAAA;kBAAAgJ,QAAA,EAAQ;gBAAc;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,KAAC,EAAC1H,YAAY,CAACoJ,aAAa,IAAI,KAAK;cAAA;gBAAA7B,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAa,CAAC,eAC9GrJ,OAAA,CAAC1B,UAAU;gBAACqL,OAAO,EAAC,OAAO;gBAAAX,QAAA,gBAAChJ,OAAA;kBAAAgJ,QAAA,EAAQ;gBAAgB;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,KAAC,EAAC1H,YAAY,CAACqJ,eAAe,IAAI,GAAG;cAAA;gBAAA9B,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAa,CAAC,eAChHrJ,OAAA,CAAC1B,UAAU;gBAACqL,OAAO,EAAC,OAAO;gBAAAX,QAAA,gBAAChJ,OAAA;kBAAAgJ,QAAA,EAAQ;gBAAM;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,KAAC,EAACxJ,2BAA2B,CAAC8B,YAAY,CAACY,mBAAmB,CAAC;cAAA;gBAAA2G,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAa,CAAC,eAChIrJ,OAAA,CAAC1B,UAAU;gBAACqL,OAAO,EAAC,OAAO;gBAAAX,QAAA,gBAAChJ,OAAA;kBAAAgJ,QAAA,EAAQ;gBAAa;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,KAAC,EAAC1H,YAAY,CAACsJ,YAAY,IAAI,GAAG;cAAA;gBAAA/B,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAa,CAAC,eAC1GrJ,OAAA,CAAC1B,UAAU;gBAACqL,OAAO,EAAC,OAAO;gBAAAX,QAAA,gBAAChJ,OAAA;kBAAAgJ,QAAA,EAAQ;gBAAO;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,KAAC,EAAC1H,YAAY,CAACuJ,SAAS,IAAI,KAAK;cAAA;gBAAAhC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAa,CAAC,eACnGrJ,OAAA,CAAC1B,UAAU;gBAACqL,OAAO,EAAC,OAAO;gBAAAX,QAAA,gBAAChJ,OAAA;kBAAAgJ,QAAA,EAAQ;gBAAkB;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,KAAC,EAAC1H,YAAY,CAACwJ,iBAAiB,IAAI,KAAK;cAAA;gBAAAjC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAa,CAAC,eACtHrJ,OAAA,CAAC1B,UAAU;gBAACqL,OAAO,EAAC,OAAO;gBAAAX,QAAA,gBAAChJ,OAAA;kBAAAgJ,QAAA,EAAQ;gBAAa;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,KAAC,EAAC1H,YAAY,CAACyJ,YAAY,IAAI,KAAK;cAAA;gBAAAlC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAa,CAAC,eAC5GrJ,OAAA,CAAC1B,UAAU;gBAACqL,OAAO,EAAC,OAAO;gBAAAX,QAAA,gBAAChJ,OAAA;kBAAAgJ,QAAA,EAAQ;gBAAqB;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,KAAC,EAAC,IAAIgC,IAAI,CAAC1J,YAAY,CAAC2J,SAAS,CAAC,CAACC,cAAc,CAAC,CAAC;cAAA;gBAAArC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAa,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAChI,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACF,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACM,CAAC,eAChBrJ,OAAA,CAACZ,aAAa;QAAA4J,QAAA,eACZhJ,OAAA,CAACxB,MAAM;UAACgN,OAAO,EAAE9C,kBAAmB;UAAAM,QAAA,EAAC;QAAM;UAAAE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACvC,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACV,CAAC;EAEb,CAAC;;EAED;;EAEA;EACA,MAAMoC,gBAAgB,GAAGA,CAAA,KAAM;IAC7B;IACA,IAAI,CAAC1J,KAAK,IAAI,CAACA,KAAK,CAAC2J,MAAM,IAAI,CAAC3J,KAAK,CAAC4J,SAAS,IAAI,CAAC5J,KAAK,CAAC4E,KAAK,EAAE;MAC/D,oBACE3G,OAAA,CAACzB,KAAK;QAACsL,EAAE,EAAE;UAAEC,EAAE,EAAE,CAAC;UAAE8B,CAAC,EAAE;QAAE,CAAE;QAAA5C,QAAA,gBACzBhJ,OAAA,CAAC1B,UAAU;UAACqL,OAAO,EAAC,IAAI;UAACC,YAAY;UAAAZ,QAAA,EAAC;QAAW;UAAAE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAY,CAAC,EAC7DpH,YAAY,gBACXjC,OAAA,CAAChB,cAAc;UAAAkK,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,gBAElBrJ,OAAA,CAAC1B,UAAU;UAACqL,OAAO,EAAC,OAAO;UAAAX,QAAA,EAAC;QAA8B;UAAAE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAY,CACvE;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACI,CAAC;IAEZ;;IAEA;IACA,MAAMqC,MAAM,GAAG3J,KAAK,CAAC2J,MAAM,IAAI;MAAEG,WAAW,EAAE,CAAC;MAAEC,UAAU,EAAE,CAAC;MAAEC,WAAW,EAAE;IAAE,CAAC;IAChF,MAAMJ,SAAS,GAAG5J,KAAK,CAAC4J,SAAS,IAAI;MAAEK,oBAAoB,EAAE,CAAC;MAAEC,kBAAkB,EAAE,CAAC;MAAEC,yBAAyB,EAAE;IAAE,CAAC;IACrH,MAAMvF,KAAK,GAAG5E,KAAK,CAAC4E,KAAK,IAAI,EAAE;IAE/B,oBACE3G,OAAA,CAACzB,KAAK;MAACsL,EAAE,EAAE;QAAEC,EAAE,EAAE,CAAC;QAAE8B,CAAC,EAAE;MAAE,CAAE;MAAA5C,QAAA,gBACzBhJ,OAAA,CAAC1B,UAAU;QAACqL,OAAO,EAAC,IAAI;QAACC,YAAY;QAAAZ,QAAA,EAAC;MAAW;QAAAE,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAY,CAAC,EAC7DpH,YAAY,gBACXjC,OAAA,CAAChB,cAAc;QAAAkK,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC,gBAElBrJ,OAAA,CAACvB,IAAI;QAAC8K,SAAS;QAACC,OAAO,EAAE,CAAE;QAAAR,QAAA,gBACzBhJ,OAAA,CAACvB,IAAI;UAACmI,IAAI;UAAC6C,EAAE,EAAE,EAAG;UAACC,EAAE,EAAE,CAAE;UAAAV,QAAA,gBACvBhJ,OAAA,CAAC1B,UAAU;YAACqL,OAAO,EAAC,WAAW;YAACC,YAAY;YAAAZ,QAAA,EAAC;UAAM;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAY,CAAC,eAChErJ,OAAA,CAAC1B,UAAU;YAACqL,OAAO,EAAC,OAAO;YAAAX,QAAA,GAAC,eAAa,EAAC0C,MAAM,CAACG,WAAW,IAAI,CAAC;UAAA;YAAA3C,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAa,CAAC,eAC/ErJ,OAAA,CAAC1B,UAAU;YAACqL,OAAO,EAAC,OAAO;YAAAX,QAAA,GAAC,cAAY,EAAC0C,MAAM,CAACI,UAAU,IAAI,CAAC;UAAA;YAAA5C,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAa,CAAC,eAC7ErJ,OAAA,CAAC1B,UAAU;YAACqL,OAAO,EAAC,OAAO;YAAAX,QAAA,GAAC,eAAa,EAAC0C,MAAM,CAACK,WAAW,IAAI,CAAC;UAAA;YAAA7C,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAa,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAE3E,CAAC,eAEPrJ,OAAA,CAACvB,IAAI;UAACmI,IAAI;UAAC6C,EAAE,EAAE,EAAG;UAACC,EAAE,EAAE,CAAE;UAAAV,QAAA,gBACvBhJ,OAAA,CAAC1B,UAAU;YAACqL,OAAO,EAAC,WAAW;YAACC,YAAY;YAAAZ,QAAA,EAAC;UAAS;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAY,CAAC,eACnErJ,OAAA,CAAC1B,UAAU;YAACqL,OAAO,EAAC,OAAO;YAAAX,QAAA,GAAC,iBAAe,EAAC,CAAC2C,SAAS,CAACK,oBAAoB,IAAI,CAAC,EAAEG,OAAO,CAAC,CAAC,CAAC;UAAA;YAAAjD,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAa,CAAC,eAC1GrJ,OAAA,CAAC1B,UAAU;YAACqL,OAAO,EAAC,OAAO;YAAAX,QAAA,GAAC,gBAAc,EAAC,CAAC2C,SAAS,CAACM,kBAAkB,IAAI,CAAC,EAAEE,OAAO,CAAC,CAAC,CAAC;UAAA;YAAAjD,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAa,CAAC,eACvGrJ,OAAA,CAAC3B,GAAG;YAACwL,EAAE,EAAE;cAAEuC,OAAO,EAAE,MAAM;cAAEC,UAAU,EAAE,QAAQ;cAAEC,EAAE,EAAE;YAAE,CAAE;YAAAtD,QAAA,gBACxDhJ,OAAA,CAAC3B,GAAG;cAACwL,EAAE,EAAE;gBAAE0C,KAAK,EAAE,MAAM;gBAAEC,EAAE,EAAE;cAAE,CAAE;cAAAxD,QAAA,eAChChJ,OAAA,CAAChB,cAAc;gBACb2K,OAAO,EAAC,aAAa;gBACrB8C,KAAK,EAAEd,SAAS,CAACO,yBAAyB,IAAI,CAAE;gBAChDrC,EAAE,EAAE;kBAAE6C,MAAM,EAAE,EAAE;kBAAEC,YAAY,EAAE;gBAAE;cAAE;gBAAAzD,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACrC;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACC,CAAC,eACNrJ,OAAA,CAAC3B,GAAG;cAACwL,EAAE,EAAE;gBAAE+C,QAAQ,EAAE;cAAG,CAAE;cAAA5D,QAAA,eACxBhJ,OAAA,CAAC1B,UAAU;gBAACqL,OAAO,EAAC,OAAO;gBAACkD,KAAK,EAAC,gBAAgB;gBAAA7D,QAAA,EAAE,GAAG,CAAC2C,SAAS,CAACO,yBAAyB,IAAI,CAAC,EAAEC,OAAO,CAAC,CAAC,CAAC;cAAG;gBAAAjD,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAa;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC1H,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACF,CAAC,eAEPrJ,OAAA,CAACvB,IAAI;UAACmI,IAAI;UAAC6C,EAAE,EAAE,EAAG;UAACC,EAAE,EAAE,CAAE;UAAAV,QAAA,gBACvBhJ,OAAA,CAAC1B,UAAU;YAACqL,OAAO,EAAC,WAAW;YAACC,YAAY;YAAAZ,QAAA,EAAC;UAAK;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAY,CAAC,eAC/DrJ,OAAA,CAAC3B,GAAG;YAACwL,EAAE,EAAE;cAAEuC,OAAO,EAAE,MAAM;cAAEU,QAAQ,EAAE,MAAM;cAAEC,GAAG,EAAE;YAAE,CAAE;YAAA/D,QAAA,EACpDrC,KAAK,CAACrD,MAAM,GAAG,CAAC,GAAGqD,KAAK,CAACb,GAAG,CAAC,CAACe,KAAK,EAAEmG,KAAK,kBACzChN,OAAA,CAAClB,IAAI;cAEHmO,KAAK,EAAE,GAAGpG,KAAK,CAACA,KAAK,IAAI,KAAK,KAAKA,KAAK,CAACqG,KAAK,IAAI,CAAC,EAAG;cACtDC,IAAI,EAAC,OAAO;cACZ3B,OAAO,EAAEA,CAAA,KAAM;gBACblJ,UAAU,CAAC8K,IAAI,KAAK;kBAClB,GAAGA,IAAI;kBACP7K,mBAAmB,EAAEsE,KAAK,CAACA,KAAK,KAAK,iBAAiB,GAAG,EAAE,GAAGA,KAAK,CAACA;gBACtE,CAAC,CAAC,CAAC;cACL;YAAE,GARGmG,KAAK;cAAA9D,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OASX,CACF,CAAC,gBACArJ,OAAA,CAAC1B,UAAU;cAACqL,OAAO,EAAC,OAAO;cAAAX,QAAA,EAAC;YAAyB;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAY;UAClE;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACE,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACF,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CACP;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACI,CAAC;EAEZ,CAAC;EAED,oBACErJ,OAAA,CAAC3B,GAAG;IAACgP,SAAS,EAAC,WAAW;IAAArE,QAAA,EACvBzH,OAAO,gBACNvB,OAAA,CAAC3B,GAAG;MAACwL,EAAE,EAAE;QAAEuC,OAAO,EAAE,MAAM;QAAEkB,aAAa,EAAE,QAAQ;QAAEjB,UAAU,EAAE,QAAQ;QAAEC,EAAE,EAAE;MAAE,CAAE;MAAAtD,QAAA,gBACjFhJ,OAAA,CAACjB,gBAAgB;QAACoO,IAAI,EAAE;MAAG;QAAAjE,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC,eAC9BrJ,OAAA,CAAC1B,UAAU;QAACuL,EAAE,EAAE;UAAEyC,EAAE,EAAE;QAAE,CAAE;QAAAtD,QAAA,EAAC;MAAmB;QAAAE,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAY,CAAC,eAC3DrJ,OAAA,CAACxB,MAAM;QACLmL,OAAO,EAAC,UAAU;QAClBkD,KAAK,EAAC,SAAS;QACfrB,OAAO,EAAEA,CAAA,KAAM/G,MAAM,CAACC,QAAQ,CAACC,MAAM,CAAC,CAAE;QACxCkF,EAAE,EAAE;UAAEyC,EAAE,EAAE;QAAE,CAAE;QAAAtD,QAAA,EACf;MAED;QAAAE,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAQ,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACN,CAAC,GACJ5H,KAAK,gBACPzB,OAAA,CAAC3B,GAAG;MAAA2K,QAAA,gBACFhJ,OAAA,CAACpB,KAAK;QAAC2O,QAAQ,EAAC,OAAO;QAAC1D,EAAE,EAAE;UAAEC,EAAE,EAAE;QAAE,CAAE;QAAAd,QAAA,GACnCvH,KAAK,EACLA,KAAK,CAAC+C,QAAQ,CAAC,eAAe,CAAC,iBAC9BxE,OAAA,CAAC1B,UAAU;UAACqL,OAAO,EAAC,OAAO;UAACE,EAAE,EAAE;YAAEyC,EAAE,EAAE;UAAE,CAAE;UAAAtD,QAAA,gBACxChJ,OAAA;YAAAgJ,QAAA,EAAQ;UAAa;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC,uEAC9B,eAAArJ,OAAA;YAAAkJ,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAK,CAAC,8CACoC,eAAArJ,OAAA;YAAAgJ,QAAA,EAAM;UAAa;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC,4CACtE;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAY,CACb;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACI,CAAC,eACRrJ,OAAA,CAAC3B,GAAG;QAACwL,EAAE,EAAE;UAAEuC,OAAO,EAAE,MAAM;UAAEW,GAAG,EAAE;QAAE,CAAE;QAAA/D,QAAA,eACnChJ,OAAA,CAACxB,MAAM;UACLmL,OAAO,EAAC,WAAW;UACnB0D,SAAS,EAAC,gBAAgB;UAC1B7B,OAAO,EAAEA,CAAA,KAAM/G,MAAM,CAACC,QAAQ,CAACC,MAAM,CAAC,CAAE;UAAAqE,QAAA,EACzC;QAED;UAAAE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACN,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,gBAENrJ,OAAA,CAAC3B,GAAG;MAAA2K,QAAA,GAIDyC,gBAAgB,CAAC,CAAC,eAGnBzL,OAAA,CAAC3B,GAAG;QAACwL,EAAE,EAAE;UAAEyC,EAAE,EAAE;QAAE,CAAE;QAAAtD,QAAA,eACjBhJ,OAAA,CAAC3B,GAAG;UAACwL,EAAE,EAAE;YAAEuC,OAAO,EAAE,MAAM;YAAEoB,cAAc,EAAE,eAAe;YAAEnB,UAAU,EAAE,QAAQ;YAAEvC,EAAE,EAAE;UAAE,CAAE;UAAAd,QAAA,gBACzFhJ,OAAA,CAAC1B,UAAU;YAACqL,OAAO,EAAC,IAAI;YAAAX,QAAA,GAAC,cACX,EAAC7H,UAAU,CAACmC,MAAM,GAAG,CAAC,GAAG,IAAInC,UAAU,CAACmC,MAAM,GAAG,GAAG,EAAE;UAAA;YAAA4F,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACxD,CAAC,eACbrJ,OAAA,CAACxB,MAAM;YACLmL,OAAO,EAAC,UAAU;YAClBwD,IAAI,EAAC,OAAO;YACZ3B,OAAO,EAAEA,CAAA,KAAM/G,MAAM,CAACC,QAAQ,CAACC,MAAM,CAAC,CAAE;YACxC8I,SAAS,EAAElM,OAAO,gBAAGvB,OAAA,CAACjB,gBAAgB;cAACoO,IAAI,EAAE;YAAG;cAAAjE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,gBAAGrJ,OAAA,CAACV,WAAW;cAAA4J,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAE;YACtEqE,QAAQ,EAAEnM,OAAQ;YAAAyH,QAAA,EACnB;UAED;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACN;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,EAELlI,UAAU,CAACmC,MAAM,GAAG,CAAC,gBACpBtD,OAAA,CAAC3B,GAAG;QAACwL,EAAE,EAAE;UAAEC,EAAE,EAAE;QAAE,CAAE;QAAAd,QAAA,GAEhB2E,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,aAAa,iBACrC7N,OAAA,CAAC3B,GAAG;UAACwL,EAAE,EAAE;YAAEC,EAAE,EAAE,CAAC;YAAE8B,CAAC,EAAE,CAAC;YAAEkC,OAAO,EAAE,SAAS;YAAEnB,YAAY,EAAE,CAAC;YAAEoB,QAAQ,EAAE,QAAQ;YAAEC,UAAU,EAAE,WAAW;YAAE5B,OAAO,EAAE;UAAO,CAAE;UAAApD,QAAA,EACzHiF,MAAM,CAACC,IAAI,CAAC/M,UAAU,CAAC,CAAC,CAAC,CAAC,CAAC2E,GAAG,CAACb,GAAG,iBACjCjF,OAAA;YAAAgJ,QAAA,GAAgB/D,GAAG,EAAC,IAAE,EAACmB,IAAI,CAAC+H,SAAS,CAAChN,UAAU,CAAC,CAAC,CAAC,CAAC8D,GAAG,CAAC,CAAC;UAAA,GAA/CA,GAAG;YAAAiE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAkD,CAChE;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACC,CACN,eACDrJ,OAAA,CAACF,mBAAmB;UAClBsO,IAAI,EAAEjN,UAAW;UACjBI,OAAO,EAAEA,OAAQ;UACjB8M,oBAAoB,EAAGC,YAAY,IAAKvL,OAAO,CAACC,GAAG,CAAC,uBAAuB,EAAEsL,YAAY,CAAChL,MAAM,CAAE;UAClGiL,iBAAiB,EAAE,EAAApO,YAAA,GAAAgB,UAAU,CAAC,CAAC,CAAC,cAAAhB,YAAA,uBAAbA,YAAA,CAAeqO,mBAAmB,OAAApO,aAAA,GAAIe,UAAU,CAAC,CAAC,CAAC,cAAAf,aAAA,uBAAbA,aAAA,CAAeqO,SAAS,OAAApO,aAAA,GAAIc,UAAU,CAAC,CAAC,CAAC,cAAAd,aAAA,uBAAbA,aAAA,CAAeqO,GAAG;QAAC;UAAAxF,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACzG,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACC,CAAC,gBAENrJ,OAAA,CAACpB,KAAK;QAAC2O,QAAQ,EAAC,MAAM;QAAC1D,EAAE,EAAE;UAAEC,EAAE,EAAE;QAAE,CAAE;QAAAd,QAAA,EAAC;MAEtC;QAAAE,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAO,CACR,eAGDrJ,OAAA,CAAC3B,GAAG;QAACwL,EAAE,EAAE;UAAEyC,EAAE,EAAE;QAAE,CAAE;QAAAtD,QAAA,gBACjBhJ,OAAA,CAAC3B,GAAG;UAACwL,EAAE,EAAE;YAAEuC,OAAO,EAAE,MAAM;YAAEoB,cAAc,EAAE,eAAe;YAAEnB,UAAU,EAAE,QAAQ;YAAEvC,EAAE,EAAE;UAAE,CAAE;UAAAd,QAAA,gBACzFhJ,OAAA,CAAC1B,UAAU;YAACqL,OAAO,EAAC,IAAI;YAAAX,QAAA,GAAC,aACZ,EAAC3H,SAAS,CAACiC,MAAM,GAAG,CAAC,GAAG,IAAIjC,SAAS,CAACiC,MAAM,GAAG,GAAG,EAAE;UAAA;YAAA4F,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACrD,CAAC,eACbrJ,OAAA,CAACxB,MAAM;YACLmL,OAAO,EAAC,UAAU;YAClBwD,IAAI,EAAC,OAAO;YACZ3B,OAAO,EAAEA,CAAA,KAAM/G,MAAM,CAACC,QAAQ,CAACC,MAAM,CAAC,CAAE;YACxC8I,SAAS,EAAElM,OAAO,gBAAGvB,OAAA,CAACjB,gBAAgB;cAACoO,IAAI,EAAE;YAAG;cAAAjE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,gBAAGrJ,OAAA,CAACV,WAAW;cAAA4J,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAE;YACtEqE,QAAQ,EAAEnM,OAAQ;YAAAyH,QAAA,EACnB;UAED;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACN,CAAC,EACLhI,SAAS,CAACiC,MAAM,GAAG,CAAC,gBACnBtD,OAAA,CAACF,mBAAmB;UAClBsO,IAAI,EAAE/M,SAAU;UAChBE,OAAO,EAAEA,OAAQ;UACjB8M,oBAAoB,EAAGC,YAAY,IAAKvL,OAAO,CAACC,GAAG,CAAC,sBAAsB,EAAEsL,YAAY,CAAChL,MAAM;QAAE;UAAA4F,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAClG,CAAC,gBAEFrJ,OAAA,CAACpB,KAAK;UAAC2O,QAAQ,EAAC,MAAM;UAAC1D,EAAE,EAAE;YAAEC,EAAE,EAAE;UAAE,CAAE;UAAAd,QAAA,EAAC;QAEtC;UAAAE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAO,CACR;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACE,CAAC,EAKLV,mBAAmB,CAAC,CAAC,eAGtB3I,OAAA,CAACf,MAAM;QACL2J,IAAI,EAAEpI,qBAAsB;QAC5BqI,OAAO,EAAEA,CAAA,KAAMpI,wBAAwB,CAAC,KAAK,CAAE;QAC/CsI,SAAS;QACTD,QAAQ,EAAC,IAAI;QAAAE,QAAA,eAEbhJ,OAAA,CAACN,oBAAoB;UACnBqB,UAAU,EAAEA,UAAW;UACvB4N,SAAS,EAAGxK,OAAO,IAAK;YACtB;YACA1D,wBAAwB,CAAC,KAAK,CAAC;;YAE/B;YACA,IAAI0D,OAAO,EAAE;cACX;cACApB,OAAO,CAACC,GAAG,CAAC,wBAAwB,EAAEmB,OAAO,CAAC;cAC9C;cACAyK,KAAK,CAACzK,OAAO,CAAC;cACd;cACAC,UAAU,CAAC,MAAM;gBACfrB,OAAO,CAACC,GAAG,CAAC,uCAAuC,CAAC;gBACpD,IAAI;kBACF;kBACAF,SAAS,CAAC,CAAC;gBACb,CAAC,CAAC,OAAOrB,KAAK,EAAE;kBACdsB,OAAO,CAACtB,KAAK,CAAC,2CAA2C,EAAEA,KAAK,CAAC;kBACjE;kBACAgD,MAAM,CAACC,QAAQ,CAACC,MAAM,CAAC,CAAC;gBAC1B;cACF,CAAC,EAAE,IAAI,CAAC;YACV,CAAC,MAAM;cACL;cACA5B,OAAO,CAACC,GAAG,CAAC,mCAAmC,CAAC;YAClD;UACF,CAAE;UACF6L,OAAO,EAAG1K,OAAO,IAAK;YACpB;YACApB,OAAO,CAACtB,KAAK,CAAC,0CAA0C,EAAE0C,OAAO,CAAC;YAClE;YACAyK,KAAK,CAAC,WAAWzK,OAAO,EAAE,CAAC;YAC3B;YACA1D,wBAAwB,CAAC,KAAK,CAAC;YAC/B;YACAqC,SAAS,CAAC,CAAC;UACb,CAAE;UACFgM,aAAa,EAAC;QAAa;UAAA5F,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC5B;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACI,CAAC,EAIR3I,sBAAsB,IAAIqC,OAAO,CAACC,GAAG,CAAC,4DAA4D,EAAEjC,UAAU,CAAC,eAEhHf,OAAA,CAACf,MAAM;QACL2J,IAAI,EAAElI,sBAAuB;QAC7BmI,OAAO,EAAEA,CAAA,KAAMlI,yBAAyB,CAAC,KAAK,CAAE;QAChDoI,SAAS;QACTD,QAAQ,EAAC,IAAI;QAAAE,QAAA,eAEbhJ,OAAA,CAACN,oBAAoB;UACnBqB,UAAU,EAAEA,UAAW;UACvB4N,SAAS,EAAGxK,OAAO,IAAK;YACtB;YACAxD,yBAAyB,CAAC,KAAK,CAAC;;YAEhC;YACA,IAAIwD,OAAO,EAAE;cACX;cACApB,OAAO,CAACC,GAAG,CAAC,wBAAwB,EAAEmB,OAAO,CAAC;cAC9C;cACAyK,KAAK,CAACzK,OAAO,CAAC;cACd;cACApB,OAAO,CAACC,GAAG,CAAC,uCAAuC,CAAC;cACpD;cACAoB,UAAU,CAAC,MAAM;gBACf,IAAI;kBACFtB,SAAS,CAAC,CAAC;gBACb,CAAC,CAAC,OAAOrB,KAAK,EAAE;kBACdsB,OAAO,CAACtB,KAAK,CAAC,2CAA2C,EAAEA,KAAK,CAAC;kBACjE;kBACAgD,MAAM,CAACC,QAAQ,CAACC,MAAM,CAAC,CAAC;gBAC1B;cACF,CAAC,EAAE,IAAI,CAAC;YACV,CAAC,MAAM;cACL;cACA5B,OAAO,CAACC,GAAG,CAAC,mCAAmC,CAAC;YAClD;UACF,CAAE;UACF6L,OAAO,EAAG1K,OAAO,IAAK;YACpB;YACApB,OAAO,CAACtB,KAAK,CAAC,sCAAsC,EAAE0C,OAAO,CAAC;YAC9D;YACAyK,KAAK,CAAC,WAAWzK,OAAO,EAAE,CAAC;YAC3B;YACAxD,yBAAyB,CAAC,KAAK,CAAC;YAChC;YACAoC,OAAO,CAACC,GAAG,CAAC,mCAAmC,CAAC;YAChDF,SAAS,CAAC,CAAC;UACb,CAAE;UACFgM,aAAa,EAAC;QAAc;UAAA5F,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC7B;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACI,CAAC,eAGTrJ,OAAA,CAACf,MAAM;QACL2J,IAAI,EAAEhI,sBAAuB;QAC7BiI,OAAO,EAAEA,CAAA,KAAMhI,yBAAyB,CAAC,KAAK,CAAE;QAChDkI,SAAS;QACTD,QAAQ,EAAC,IAAI;QAAAE,QAAA,gBAEbhJ,OAAA,CAACd,WAAW;UAAA8J,QAAA,EAAC;QAAmB;UAAAE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAa,CAAC,eAC9CrJ,OAAA,CAACb,aAAa;UAAA6J,QAAA,eACZhJ,OAAA,CAAC3B,GAAG;YAACwL,EAAE,EAAE;cAAEyC,EAAE,EAAE;YAAE,CAAE;YAAAtD,QAAA,eACjBhJ,OAAA,CAACJ,gBAAgB;cACfmB,UAAU,EAAEA,UAAW;cACvB4N,SAAS,EAAGxK,OAAO,IAAK;gBACtB;gBACAtD,yBAAyB,CAAC,KAAK,CAAC;gBAChC;gBACA+N,KAAK,CAACzK,OAAO,CAAC;gBACd;gBACAC,UAAU,CAAC,MAAM;kBACfrB,OAAO,CAACC,GAAG,CAAC,uCAAuC,CAAC;kBACpD,IAAI;oBACF;oBACAF,SAAS,CAAC,CAAC;kBACb,CAAC,CAAC,OAAOrB,KAAK,EAAE;oBACdsB,OAAO,CAACtB,KAAK,CAAC,2CAA2C,EAAEA,KAAK,CAAC;oBACjE;oBACAsB,OAAO,CAACC,GAAG,CAAC,4CAA4C,CAAC;oBACzDyB,MAAM,CAACC,QAAQ,CAACC,MAAM,CAAC,CAAC;kBAC1B;gBACF,CAAC,EAAE,IAAI,CAAC;cACV,CAAE;cACFkK,OAAO,EAAG1K,OAAO,IAAK;gBACpB;gBACApB,OAAO,CAACtB,KAAK,CAAC,sCAAsC,EAAE0C,OAAO,CAAC;gBAC9D;gBACAyK,KAAK,CAAC,WAAWzK,OAAO,EAAE,CAAC;cAC7B,CAAE;cACF4K,QAAQ,EAAE;YAAK;cAAA7F,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAChB;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACC;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACO,CAAC,eAChBrJ,OAAA,CAACZ,aAAa;UAAA4J,QAAA,eACZhJ,OAAA,CAACxB,MAAM;YAACgN,OAAO,EAAEA,CAAA,KAAM3K,yBAAyB,CAAC,KAAK,CAAE;YAAAmI,QAAA,EAAC;UAAO;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC5D,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACV,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACN;EACN;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACE,CAAC;AAEV,CAAC;AAACnJ,EAAA,CA11BID,kBAAkB;EAAA,QACYT,OAAO,EACyHC,gBAAgB,EACjKF,WAAW;AAAA;AAAAyP,EAAA,GAHxB/O,kBAAkB;AA41BxB,eAAeA,kBAAkB;AAAC,IAAA+O,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}