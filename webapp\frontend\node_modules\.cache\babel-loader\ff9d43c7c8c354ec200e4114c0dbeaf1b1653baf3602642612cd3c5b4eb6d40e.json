{"ast": null, "code": "import { buildLocalizeFn } from \"../../_lib/buildLocalizeFn.js\";\nconst eraValues = {\n  narrow: [\"ق\", \"ب\"],\n  abbreviated: [\"ق.م.\", \"ب.م.\"],\n  wide: [\"قبل الميلاد\", \"بعد الميلاد\"]\n};\nconst quarterValues = {\n  narrow: [\"1\", \"2\", \"3\", \"4\"],\n  abbreviated: [\"ر1\", \"ر2\", \"ر3\", \"ر4\"],\n  wide: [\"الربع الأول\", \"الربع الثاني\", \"الربع الثالث\", \"الربع الرابع\"]\n};\nconst monthValues = {\n  narrow: [\"ج\", \"ف\", \"م\", \"أ\", \"م\", \"ج\", \"ج\", \"أ\", \"س\", \"أ\", \"ن\", \"د\"],\n  abbreviated: [\"جانـ\", \"فيفـ\", \"مارس\", \"أفريل\", \"مايـ\", \"جوانـ\", \"جويـ\", \"أوت\", \"سبتـ\", \"أكتـ\", \"نوفـ\", \"ديسـ\"],\n  wide: [\"جانفي\", \"فيفري\", \"مارس\", \"أفريل\", \"ماي\", \"جوان\", \"جويلية\", \"أوت\", \"سبتمبر\", \"أكتوبر\", \"نوفمبر\", \"ديسمبر\"]\n};\nconst dayValues = {\n  narrow: [\"ح\", \"ن\", \"ث\", \"ر\", \"خ\", \"ج\", \"س\"],\n  short: [\"أحد\", \"اثنين\", \"ثلاثاء\", \"أربعاء\", \"خميس\", \"جمعة\", \"سبت\"],\n  abbreviated: [\"أحد\", \"اثنـ\", \"ثلا\", \"أربـ\", \"خميـ\", \"جمعة\", \"سبت\"],\n  wide: [\"الأحد\", \"الاثنين\", \"الثلاثاء\", \"الأربعاء\", \"الخميس\", \"الجمعة\", \"السبت\"]\n};\nconst dayPeriodValues = {\n  narrow: {\n    am: \"ص\",\n    pm: \"م\",\n    midnight: \"ن\",\n    noon: \"ظ\",\n    morning: \"صباحاً\",\n    afternoon: \"بعد الظهر\",\n    evening: \"مساءاً\",\n    night: \"ليلاً\"\n  },\n  abbreviated: {\n    am: \"ص\",\n    pm: \"م\",\n    midnight: \"نصف الليل\",\n    noon: \"ظهر\",\n    morning: \"صباحاً\",\n    afternoon: \"بعد الظهر\",\n    evening: \"مساءاً\",\n    night: \"ليلاً\"\n  },\n  wide: {\n    am: \"ص\",\n    pm: \"م\",\n    midnight: \"نصف الليل\",\n    noon: \"ظهر\",\n    morning: \"صباحاً\",\n    afternoon: \"بعد الظهر\",\n    evening: \"مساءاً\",\n    night: \"ليلاً\"\n  }\n};\nconst formattingDayPeriodValues = {\n  narrow: {\n    am: \"ص\",\n    pm: \"م\",\n    midnight: \"ن\",\n    noon: \"ظ\",\n    morning: \"في الصباح\",\n    afternoon: \"بعد الظـهر\",\n    evening: \"في المساء\",\n    night: \"في الليل\"\n  },\n  abbreviated: {\n    am: \"ص\",\n    pm: \"م\",\n    midnight: \"نصف الليل\",\n    noon: \"ظهر\",\n    morning: \"في الصباح\",\n    afternoon: \"بعد الظهر\",\n    evening: \"في المساء\",\n    night: \"في الليل\"\n  },\n  wide: {\n    am: \"ص\",\n    pm: \"م\",\n    midnight: \"نصف الليل\",\n    noon: \"ظهر\",\n    morning: \"صباحاً\",\n    afternoon: \"بعد الظـهر\",\n    evening: \"في المساء\",\n    night: \"في الليل\"\n  }\n};\nconst ordinalNumber = dirtyNumber => {\n  return String(dirtyNumber);\n};\nexport const localize = {\n  ordinalNumber: ordinalNumber,\n  era: buildLocalizeFn({\n    values: eraValues,\n    defaultWidth: \"wide\"\n  }),\n  quarter: buildLocalizeFn({\n    values: quarterValues,\n    defaultWidth: \"wide\",\n    argumentCallback: quarter => Number(quarter) - 1\n  }),\n  month: buildLocalizeFn({\n    values: monthValues,\n    defaultWidth: \"wide\"\n  }),\n  day: buildLocalizeFn({\n    values: dayValues,\n    defaultWidth: \"wide\"\n  }),\n  dayPeriod: buildLocalizeFn({\n    values: dayPeriodValues,\n    defaultWidth: \"wide\",\n    formattingValues: formattingDayPeriodValues,\n    defaultFormattingWidth: \"wide\"\n  })\n};", "map": {"version": 3, "names": ["buildLocalizeFn", "<PERSON><PERSON><PERSON><PERSON>", "narrow", "abbreviated", "wide", "quarterValues", "month<PERSON><PERSON><PERSON>", "dayV<PERSON><PERSON>", "short", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "am", "pm", "midnight", "noon", "morning", "afternoon", "evening", "night", "formattingDayPeriodValues", "ordinalNumber", "dirtyNumber", "String", "localize", "era", "values", "defaultWidth", "quarter", "argument<PERSON>allback", "Number", "month", "day", "<PERSON><PERSON><PERSON><PERSON>", "formattingValues", "defaultFormattingWidth"], "sources": ["C:/Users/<USER>/PycharmProjects/CMS/webapp/frontend/node_modules/date-fns/locale/ar-DZ/_lib/localize.js"], "sourcesContent": ["import { buildLocalizeFn } from \"../../_lib/buildLocalizeFn.js\";\n\nconst eraValues = {\n  narrow: [\"ق\", \"ب\"],\n  abbreviated: [\"ق.م.\", \"ب.م.\"],\n  wide: [\"قبل الميلاد\", \"بعد الميلاد\"],\n};\n\nconst quarterValues = {\n  narrow: [\"1\", \"2\", \"3\", \"4\"],\n  abbreviated: [\"ر1\", \"ر2\", \"ر3\", \"ر4\"],\n  wide: [\"الربع الأول\", \"الربع الثاني\", \"الربع الثالث\", \"الربع الرابع\"],\n};\n\nconst monthValues = {\n  narrow: [\"ج\", \"ف\", \"م\", \"أ\", \"م\", \"ج\", \"ج\", \"أ\", \"س\", \"أ\", \"ن\", \"د\"],\n  abbreviated: [\n    \"جانـ\",\n    \"فيفـ\",\n    \"مارس\",\n    \"أفريل\",\n    \"مايـ\",\n    \"جوانـ\",\n    \"جويـ\",\n    \"أوت\",\n    \"سبتـ\",\n    \"أكتـ\",\n    \"نوفـ\",\n    \"ديسـ\",\n  ],\n\n  wide: [\n    \"جانفي\",\n    \"فيفري\",\n    \"مارس\",\n    \"أفريل\",\n    \"ماي\",\n    \"جوان\",\n    \"جويلية\",\n    \"أوت\",\n    \"سبتمبر\",\n    \"أكتوبر\",\n    \"نوفمبر\",\n    \"ديسمبر\",\n  ],\n};\n\nconst dayValues = {\n  narrow: [\"ح\", \"ن\", \"ث\", \"ر\", \"خ\", \"ج\", \"س\"],\n  short: [\"أحد\", \"اثنين\", \"ثلاثاء\", \"أربعاء\", \"خميس\", \"جمعة\", \"سبت\"],\n  abbreviated: [\"أحد\", \"اثنـ\", \"ثلا\", \"أربـ\", \"خميـ\", \"جمعة\", \"سبت\"],\n  wide: [\n    \"الأحد\",\n    \"الاثنين\",\n    \"الثلاثاء\",\n    \"الأربعاء\",\n    \"الخميس\",\n    \"الجمعة\",\n    \"السبت\",\n  ],\n};\n\nconst dayPeriodValues = {\n  narrow: {\n    am: \"ص\",\n    pm: \"م\",\n    midnight: \"ن\",\n    noon: \"ظ\",\n    morning: \"صباحاً\",\n    afternoon: \"بعد الظهر\",\n    evening: \"مساءاً\",\n    night: \"ليلاً\",\n  },\n  abbreviated: {\n    am: \"ص\",\n    pm: \"م\",\n    midnight: \"نصف الليل\",\n    noon: \"ظهر\",\n    morning: \"صباحاً\",\n    afternoon: \"بعد الظهر\",\n    evening: \"مساءاً\",\n    night: \"ليلاً\",\n  },\n  wide: {\n    am: \"ص\",\n    pm: \"م\",\n    midnight: \"نصف الليل\",\n    noon: \"ظهر\",\n    morning: \"صباحاً\",\n    afternoon: \"بعد الظهر\",\n    evening: \"مساءاً\",\n    night: \"ليلاً\",\n  },\n};\nconst formattingDayPeriodValues = {\n  narrow: {\n    am: \"ص\",\n    pm: \"م\",\n    midnight: \"ن\",\n    noon: \"ظ\",\n    morning: \"في الصباح\",\n    afternoon: \"بعد الظـهر\",\n    evening: \"في المساء\",\n    night: \"في الليل\",\n  },\n  abbreviated: {\n    am: \"ص\",\n    pm: \"م\",\n    midnight: \"نصف الليل\",\n    noon: \"ظهر\",\n    morning: \"في الصباح\",\n    afternoon: \"بعد الظهر\",\n    evening: \"في المساء\",\n    night: \"في الليل\",\n  },\n  wide: {\n    am: \"ص\",\n    pm: \"م\",\n    midnight: \"نصف الليل\",\n    noon: \"ظهر\",\n    morning: \"صباحاً\",\n    afternoon: \"بعد الظـهر\",\n    evening: \"في المساء\",\n    night: \"في الليل\",\n  },\n};\n\nconst ordinalNumber = (dirtyNumber) => {\n  return String(dirtyNumber);\n};\n\nexport const localize = {\n  ordinalNumber: ordinalNumber,\n\n  era: buildLocalizeFn({\n    values: eraValues,\n    defaultWidth: \"wide\",\n  }),\n\n  quarter: buildLocalizeFn({\n    values: quarterValues,\n    defaultWidth: \"wide\",\n    argumentCallback: (quarter) => Number(quarter) - 1,\n  }),\n\n  month: buildLocalizeFn({\n    values: monthValues,\n    defaultWidth: \"wide\",\n  }),\n\n  day: buildLocalizeFn({\n    values: dayValues,\n    defaultWidth: \"wide\",\n  }),\n\n  dayPeriod: buildLocalizeFn({\n    values: dayPeriodValues,\n    defaultWidth: \"wide\",\n    formattingValues: formattingDayPeriodValues,\n    defaultFormattingWidth: \"wide\",\n  }),\n};\n"], "mappings": "AAAA,SAASA,eAAe,QAAQ,+BAA+B;AAE/D,MAAMC,SAAS,GAAG;EAChBC,MAAM,EAAE,CAAC,GAAG,EAAE,GAAG,CAAC;EAClBC,WAAW,EAAE,CAAC,MAAM,EAAE,MAAM,CAAC;EAC7BC,IAAI,EAAE,CAAC,aAAa,EAAE,aAAa;AACrC,CAAC;AAED,MAAMC,aAAa,GAAG;EACpBH,MAAM,EAAE,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,CAAC;EAC5BC,WAAW,EAAE,CAAC,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,CAAC;EACrCC,IAAI,EAAE,CAAC,aAAa,EAAE,cAAc,EAAE,cAAc,EAAE,cAAc;AACtE,CAAC;AAED,MAAME,WAAW,GAAG;EAClBJ,MAAM,EAAE,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,CAAC;EACpEC,WAAW,EAAE,CACX,MAAM,EACN,MAAM,EACN,MAAM,EACN,OAAO,EACP,MAAM,EACN,OAAO,EACP,MAAM,EACN,KAAK,EACL,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,CACP;EAEDC,IAAI,EAAE,CACJ,OAAO,EACP,OAAO,EACP,MAAM,EACN,OAAO,EACP,KAAK,EACL,MAAM,EACN,QAAQ,EACR,KAAK,EACL,QAAQ,EACR,QAAQ,EACR,QAAQ,EACR,QAAQ;AAEZ,CAAC;AAED,MAAMG,SAAS,GAAG;EAChBL,MAAM,EAAE,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,CAAC;EAC3CM,KAAK,EAAE,CAAC,KAAK,EAAE,OAAO,EAAE,QAAQ,EAAE,QAAQ,EAAE,MAAM,EAAE,MAAM,EAAE,KAAK,CAAC;EAClEL,WAAW,EAAE,CAAC,KAAK,EAAE,MAAM,EAAE,KAAK,EAAE,MAAM,EAAE,MAAM,EAAE,MAAM,EAAE,KAAK,CAAC;EAClEC,IAAI,EAAE,CACJ,OAAO,EACP,SAAS,EACT,UAAU,EACV,UAAU,EACV,QAAQ,EACR,QAAQ,EACR,OAAO;AAEX,CAAC;AAED,MAAMK,eAAe,GAAG;EACtBP,MAAM,EAAE;IACNQ,EAAE,EAAE,GAAG;IACPC,EAAE,EAAE,GAAG;IACPC,QAAQ,EAAE,GAAG;IACbC,IAAI,EAAE,GAAG;IACTC,OAAO,EAAE,QAAQ;IACjBC,SAAS,EAAE,WAAW;IACtBC,OAAO,EAAE,QAAQ;IACjBC,KAAK,EAAE;EACT,CAAC;EACDd,WAAW,EAAE;IACXO,EAAE,EAAE,GAAG;IACPC,EAAE,EAAE,GAAG;IACPC,QAAQ,EAAE,WAAW;IACrBC,IAAI,EAAE,KAAK;IACXC,OAAO,EAAE,QAAQ;IACjBC,SAAS,EAAE,WAAW;IACtBC,OAAO,EAAE,QAAQ;IACjBC,KAAK,EAAE;EACT,CAAC;EACDb,IAAI,EAAE;IACJM,EAAE,EAAE,GAAG;IACPC,EAAE,EAAE,GAAG;IACPC,QAAQ,EAAE,WAAW;IACrBC,IAAI,EAAE,KAAK;IACXC,OAAO,EAAE,QAAQ;IACjBC,SAAS,EAAE,WAAW;IACtBC,OAAO,EAAE,QAAQ;IACjBC,KAAK,EAAE;EACT;AACF,CAAC;AACD,MAAMC,yBAAyB,GAAG;EAChChB,MAAM,EAAE;IACNQ,EAAE,EAAE,GAAG;IACPC,EAAE,EAAE,GAAG;IACPC,QAAQ,EAAE,GAAG;IACbC,IAAI,EAAE,GAAG;IACTC,OAAO,EAAE,WAAW;IACpBC,SAAS,EAAE,YAAY;IACvBC,OAAO,EAAE,WAAW;IACpBC,KAAK,EAAE;EACT,CAAC;EACDd,WAAW,EAAE;IACXO,EAAE,EAAE,GAAG;IACPC,EAAE,EAAE,GAAG;IACPC,QAAQ,EAAE,WAAW;IACrBC,IAAI,EAAE,KAAK;IACXC,OAAO,EAAE,WAAW;IACpBC,SAAS,EAAE,WAAW;IACtBC,OAAO,EAAE,WAAW;IACpBC,KAAK,EAAE;EACT,CAAC;EACDb,IAAI,EAAE;IACJM,EAAE,EAAE,GAAG;IACPC,EAAE,EAAE,GAAG;IACPC,QAAQ,EAAE,WAAW;IACrBC,IAAI,EAAE,KAAK;IACXC,OAAO,EAAE,QAAQ;IACjBC,SAAS,EAAE,YAAY;IACvBC,OAAO,EAAE,WAAW;IACpBC,KAAK,EAAE;EACT;AACF,CAAC;AAED,MAAME,aAAa,GAAIC,WAAW,IAAK;EACrC,OAAOC,MAAM,CAACD,WAAW,CAAC;AAC5B,CAAC;AAED,OAAO,MAAME,QAAQ,GAAG;EACtBH,aAAa,EAAEA,aAAa;EAE5BI,GAAG,EAAEvB,eAAe,CAAC;IACnBwB,MAAM,EAAEvB,SAAS;IACjBwB,YAAY,EAAE;EAChB,CAAC,CAAC;EAEFC,OAAO,EAAE1B,eAAe,CAAC;IACvBwB,MAAM,EAAEnB,aAAa;IACrBoB,YAAY,EAAE,MAAM;IACpBE,gBAAgB,EAAGD,OAAO,IAAKE,MAAM,CAACF,OAAO,CAAC,GAAG;EACnD,CAAC,CAAC;EAEFG,KAAK,EAAE7B,eAAe,CAAC;IACrBwB,MAAM,EAAElB,WAAW;IACnBmB,YAAY,EAAE;EAChB,CAAC,CAAC;EAEFK,GAAG,EAAE9B,eAAe,CAAC;IACnBwB,MAAM,EAAEjB,SAAS;IACjBkB,YAAY,EAAE;EAChB,CAAC,CAAC;EAEFM,SAAS,EAAE/B,eAAe,CAAC;IACzBwB,MAAM,EAAEf,eAAe;IACvBgB,YAAY,EAAE,MAAM;IACpBO,gBAAgB,EAAEd,yBAAyB;IAC3Ce,sBAAsB,EAAE;EAC1B,CAAC;AACH,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}