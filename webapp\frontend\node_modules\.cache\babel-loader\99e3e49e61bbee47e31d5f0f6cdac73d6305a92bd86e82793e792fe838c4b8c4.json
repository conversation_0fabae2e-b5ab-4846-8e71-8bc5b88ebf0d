{"ast": null, "code": "import buildFormatLongFn from \"../../../_lib/buildFormatLongFn/index.js\";\nvar dateFormats = {\n  full: \"EEEE, d MMMM y 'г.'\",\n  long: \"d MMMM y 'г.'\",\n  medium: \"d MMM y 'г.'\",\n  short: 'dd.MM.y'\n};\nvar timeFormats = {\n  full: 'H:mm:ss zzzz',\n  long: 'H:mm:ss z',\n  medium: 'H:mm:ss',\n  short: 'H:mm'\n};\nvar dateTimeFormats = {\n  any: '{{date}}, {{time}}'\n};\nvar formatLong = {\n  date: buildFormatLongFn({\n    formats: dateFormats,\n    defaultWidth: 'full'\n  }),\n  time: buildFormatLongFn({\n    formats: timeFormats,\n    defaultWidth: 'full'\n  }),\n  dateTime: buildFormatLongFn({\n    formats: dateTimeFormats,\n    defaultWidth: 'any'\n  })\n};\nexport default formatLong;", "map": {"version": 3, "names": ["buildFormatLongFn", "dateFormats", "full", "long", "medium", "short", "timeFormats", "dateTimeFormats", "any", "formatLong", "date", "formats", "defaultWidth", "time", "dateTime"], "sources": ["C:/CMS/webapp/frontend/node_modules/date-fns/esm/locale/be-tarask/_lib/formatLong/index.js"], "sourcesContent": ["import buildFormatLongFn from \"../../../_lib/buildFormatLongFn/index.js\";\nvar dateFormats = {\n  full: \"EEEE, d MMMM y 'г.'\",\n  long: \"d MMMM y 'г.'\",\n  medium: \"d MMM y 'г.'\",\n  short: 'dd.MM.y'\n};\nvar timeFormats = {\n  full: 'H:mm:ss zzzz',\n  long: 'H:mm:ss z',\n  medium: 'H:mm:ss',\n  short: 'H:mm'\n};\nvar dateTimeFormats = {\n  any: '{{date}}, {{time}}'\n};\nvar formatLong = {\n  date: buildFormatLongFn({\n    formats: dateFormats,\n    defaultWidth: 'full'\n  }),\n  time: buildFormatLongFn({\n    formats: timeFormats,\n    defaultWidth: 'full'\n  }),\n  dateTime: buildFormatLongFn({\n    formats: dateTimeFormats,\n    defaultWidth: 'any'\n  })\n};\nexport default formatLong;"], "mappings": "AAAA,OAAOA,iBAAiB,MAAM,0CAA0C;AACxE,IAAIC,WAAW,GAAG;EAChBC,IAAI,EAAE,qBAAqB;EAC3BC,IAAI,EAAE,eAAe;EACrBC,MAAM,EAAE,cAAc;EACtBC,KAAK,EAAE;AACT,CAAC;AACD,IAAIC,WAAW,GAAG;EAChBJ,IAAI,EAAE,cAAc;EACpBC,IAAI,EAAE,WAAW;EACjBC,MAAM,EAAE,SAAS;EACjBC,KAAK,EAAE;AACT,CAAC;AACD,IAAIE,eAAe,GAAG;EACpBC,GAAG,EAAE;AACP,CAAC;AACD,IAAIC,UAAU,GAAG;EACfC,IAAI,EAAEV,iBAAiB,CAAC;IACtBW,OAAO,EAAEV,WAAW;IACpBW,YAAY,EAAE;EAChB,CAAC,CAAC;EACFC,IAAI,EAAEb,iBAAiB,CAAC;IACtBW,OAAO,EAAEL,WAAW;IACpBM,YAAY,EAAE;EAChB,CAAC,CAAC;EACFE,QAAQ,EAAEd,iBAAiB,CAAC;IAC1BW,OAAO,EAAEJ,eAAe;IACxBK,YAAY,EAAE;EAChB,CAAC;AACH,CAAC;AACD,eAAeH,UAAU", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}