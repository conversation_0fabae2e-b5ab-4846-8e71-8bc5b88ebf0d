{"ast": null, "code": "\"use client\";\n\nimport createSvgIcon from './utils/createSvgIcon';\nimport { jsx as _jsx } from \"react/jsx-runtime\";\nexport default createSvgIcon([/*#__PURE__*/_jsx(\"path\", {\n  d: \"M20 2c-.55 0-1 .45-1 1v3c0 .55.45 1 1 1s1-.45 1-1V3c0-.55-.45-1-1-1\"\n}, \"0\"), /*#__PURE__*/_jsx(\"circle\", {\n  cx: \"20\",\n  cy: \"9\",\n  r: \"1\"\n}, \"1\"), /*#__PURE__*/_jsx(\"path\", {\n  d: \"m12 10.41 3.29-3.29c.39-.39.39-1.02 0-1.41a.9959.9959 0 0 0-1.41 0L12 7.59V5c0-.55-.45-1-1-1s-1 .45-1 1v2.59L8.12 5.71a.9959.9959 0 0 0-1.41 0c-.39.39-.39 1.02 0 1.41L10 10.41V12H8.41L5.12 8.71a.9959.9959 0 0 0-1.41 0c-.39.39-.39 1.02 0 1.41L5.59 12H3c-.55 0-1 .45-1 1s.45 1 1 1h2.59l-1.88 1.88c-.39.39-.39 1.02 0 1.41.39.39 1.02.39 1.41 0L8.41 14H10v1.59l-3.29 3.29c-.39.39-.39 1.02 0 1.41.39.39 1.02.39 1.41 0L10 18.41V21c0 .55.45 1 1 1s1-.45 1-1v-2.59l1.88 1.88c.39.39 1.02.39 1.41 0 .39-.39.39-1.02 0-1.41L12 15.59V14h1.59l3.29 3.29c.39.39 1.02.39 1.41 0 .39-.39.39-1.02 0-1.41L16.41 14H19c.55 0 1-.45 1-1s-.45-1-1-1h-7z\"\n}, \"2\")], 'SevereColdRounded');", "map": {"version": 3, "names": ["createSvgIcon", "jsx", "_jsx", "d", "cx", "cy", "r"], "sources": ["C:/Users/<USER>/PycharmProjects/CMS/webapp/frontend/node_modules/@mui/icons-material/esm/SevereColdRounded.js"], "sourcesContent": ["\"use client\";\n\nimport createSvgIcon from './utils/createSvgIcon';\nimport { jsx as _jsx } from \"react/jsx-runtime\";\nexport default createSvgIcon([/*#__PURE__*/_jsx(\"path\", {\n  d: \"M20 2c-.55 0-1 .45-1 1v3c0 .55.45 1 1 1s1-.45 1-1V3c0-.55-.45-1-1-1\"\n}, \"0\"), /*#__PURE__*/_jsx(\"circle\", {\n  cx: \"20\",\n  cy: \"9\",\n  r: \"1\"\n}, \"1\"), /*#__PURE__*/_jsx(\"path\", {\n  d: \"m12 10.41 3.29-3.29c.39-.39.39-1.02 0-1.41a.9959.9959 0 0 0-1.41 0L12 7.59V5c0-.55-.45-1-1-1s-1 .45-1 1v2.59L8.12 5.71a.9959.9959 0 0 0-1.41 0c-.39.39-.39 1.02 0 1.41L10 10.41V12H8.41L5.12 8.71a.9959.9959 0 0 0-1.41 0c-.39.39-.39 1.02 0 1.41L5.59 12H3c-.55 0-1 .45-1 1s.45 1 1 1h2.59l-1.88 1.88c-.39.39-.39 1.02 0 1.41.39.39 1.02.39 1.41 0L8.41 14H10v1.59l-3.29 3.29c-.39.39-.39 1.02 0 1.41.39.39 1.02.39 1.41 0L10 18.41V21c0 .55.45 1 1 1s1-.45 1-1v-2.59l1.88 1.88c.39.39 1.02.39 1.41 0 .39-.39.39-1.02 0-1.41L12 15.59V14h1.59l3.29 3.29c.39.39 1.02.39 1.41 0 .39-.39.39-1.02 0-1.41L16.41 14H19c.55 0 1-.45 1-1s-.45-1-1-1h-7z\"\n}, \"2\")], 'SevereColdRounded');"], "mappings": "AAAA,YAAY;;AAEZ,OAAOA,aAAa,MAAM,uBAAuB;AACjD,SAASC,GAAG,IAAIC,IAAI,QAAQ,mBAAmB;AAC/C,eAAeF,aAAa,CAAC,CAAC,aAAaE,IAAI,CAAC,MAAM,EAAE;EACtDC,CAAC,EAAE;AACL,CAAC,EAAE,GAAG,CAAC,EAAE,aAAaD,IAAI,CAAC,QAAQ,EAAE;EACnCE,EAAE,EAAE,IAAI;EACRC,EAAE,EAAE,GAAG;EACPC,CAAC,EAAE;AACL,CAAC,EAAE,GAAG,CAAC,EAAE,aAAaJ,IAAI,CAAC,MAAM,EAAE;EACjCC,CAAC,EAAE;AACL,CAAC,EAAE,GAAG,CAAC,CAAC,EAAE,mBAAmB,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}