{"ast": null, "code": "const formatRelativeLocale = {\n  lastWeek: date => {\n    switch (date.getDay()) {\n      case 0:\n        return \"'prošle nedjelje u' p\";\n      case 3:\n        return \"'prošle srijede u' p\";\n      case 6:\n        return \"'prošle subote u' p\";\n      default:\n        return \"'prošli' EEEE 'u' p\";\n    }\n  },\n  yesterday: \"'juče u' p\",\n  today: \"'danas u' p\",\n  tomorrow: \"'sutra u' p\",\n  nextWeek: date => {\n    switch (date.getDay()) {\n      case 0:\n        return \"'sljedeće nedjelje u' p\";\n      case 3:\n        return \"'sljedeću srijedu u' p\";\n      case 6:\n        return \"'sljedeću subotu u' p\";\n      default:\n        return \"'sljedeći' EEEE 'u' p\";\n    }\n  },\n  other: \"P\"\n};\nexport const formatRelative = (token, date, _baseDate, _options) => {\n  const format = formatRelativeLocale[token];\n  if (typeof format === \"function\") {\n    return format(date);\n  }\n  return format;\n};", "map": {"version": 3, "names": ["formatRelativeLocale", "lastWeek", "date", "getDay", "yesterday", "today", "tomorrow", "nextWeek", "other", "formatRelative", "token", "_baseDate", "_options", "format"], "sources": ["C:/Users/<USER>/PycharmProjects/CMS/webapp/frontend/node_modules/date-fns/locale/bs/_lib/formatRelative.js"], "sourcesContent": ["const formatRelativeLocale = {\n  lastWeek: (date) => {\n    switch (date.getDay()) {\n      case 0:\n        return \"'prošle nedjelje u' p\";\n      case 3:\n        return \"'prošle srijede u' p\";\n      case 6:\n        return \"'prošle subote u' p\";\n      default:\n        return \"'prošli' EEEE 'u' p\";\n    }\n  },\n  yesterday: \"'juče u' p\",\n  today: \"'danas u' p\",\n  tomorrow: \"'sutra u' p\",\n  nextWeek: (date) => {\n    switch (date.getDay()) {\n      case 0:\n        return \"'sljedeće nedjelje u' p\";\n      case 3:\n        return \"'sljedeću srijedu u' p\";\n      case 6:\n        return \"'sljedeću subotu u' p\";\n      default:\n        return \"'sljedeći' EEEE 'u' p\";\n    }\n  },\n  other: \"P\",\n};\n\nexport const formatRelative = (token, date, _baseDate, _options) => {\n  const format = formatRelativeLocale[token];\n\n  if (typeof format === \"function\") {\n    return format(date);\n  }\n\n  return format;\n};\n"], "mappings": "AAAA,MAAMA,oBAAoB,GAAG;EAC3BC,QAAQ,EAAGC,IAAI,IAAK;IAClB,QAAQA,IAAI,CAACC,MAAM,CAAC,CAAC;MACnB,KAAK,CAAC;QACJ,OAAO,uBAAuB;MAChC,KAAK,CAAC;QACJ,OAAO,sBAAsB;MAC/B,KAAK,CAAC;QACJ,OAAO,qBAAqB;MAC9B;QACE,OAAO,qBAAqB;IAChC;EACF,CAAC;EACDC,SAAS,EAAE,YAAY;EACvBC,KAAK,EAAE,aAAa;EACpBC,QAAQ,EAAE,aAAa;EACvBC,QAAQ,EAAGL,IAAI,IAAK;IAClB,QAAQA,IAAI,CAACC,MAAM,CAAC,CAAC;MACnB,KAAK,CAAC;QACJ,OAAO,yBAAyB;MAClC,KAAK,CAAC;QACJ,OAAO,wBAAwB;MACjC,KAAK,CAAC;QACJ,OAAO,uBAAuB;MAChC;QACE,OAAO,uBAAuB;IAClC;EACF,CAAC;EACDK,KAAK,EAAE;AACT,CAAC;AAED,OAAO,MAAMC,cAAc,GAAGA,CAACC,KAAK,EAAER,IAAI,EAAES,SAAS,EAAEC,QAAQ,KAAK;EAClE,MAAMC,MAAM,GAAGb,oBAAoB,CAACU,KAAK,CAAC;EAE1C,IAAI,OAAOG,MAAM,KAAK,UAAU,EAAE;IAChC,OAAOA,MAAM,CAACX,IAAI,CAAC;EACrB;EAEA,OAAOW,MAAM;AACf,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}