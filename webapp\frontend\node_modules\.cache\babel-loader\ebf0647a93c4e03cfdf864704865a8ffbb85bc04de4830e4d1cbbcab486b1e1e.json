{"ast": null, "code": "import { InternSet } from \"internmap\";\nexport default function intersection(values) {\n  for (var _len = arguments.length, others = new Array(_len > 1 ? _len - 1 : 0), _key = 1; _key < _len; _key++) {\n    others[_key - 1] = arguments[_key];\n  }\n  values = new InternSet(values);\n  others = others.map(set);\n  out: for (const value of values) {\n    for (const other of others) {\n      if (!other.has(value)) {\n        values.delete(value);\n        continue out;\n      }\n    }\n  }\n  return values;\n}\nfunction set(values) {\n  return values instanceof InternSet ? values : new InternSet(values);\n}", "map": {"version": 3, "names": ["InternSet", "intersection", "values", "_len", "arguments", "length", "others", "Array", "_key", "map", "set", "out", "value", "other", "has", "delete"], "sources": ["C:/Users/<USER>/PycharmProjects/CMS/webapp/frontend/node_modules/d3-array/src/intersection.js"], "sourcesContent": ["import {InternSet} from \"internmap\";\n\nexport default function intersection(values, ...others) {\n  values = new InternSet(values);\n  others = others.map(set);\n  out: for (const value of values) {\n    for (const other of others) {\n      if (!other.has(value)) {\n        values.delete(value);\n        continue out;\n      }\n    }\n  }\n  return values;\n}\n\nfunction set(values) {\n  return values instanceof InternSet ? values : new InternSet(values);\n}\n"], "mappings": "AAAA,SAAQA,SAAS,QAAO,WAAW;AAEnC,eAAe,SAASC,YAAYA,CAACC,MAAM,EAAa;EAAA,SAAAC,IAAA,GAAAC,SAAA,CAAAC,MAAA,EAARC,MAAM,OAAAC,KAAA,CAAAJ,IAAA,OAAAA,IAAA,WAAAK,IAAA,MAAAA,IAAA,GAAAL,IAAA,EAAAK,IAAA;IAANF,MAAM,CAAAE,IAAA,QAAAJ,SAAA,CAAAI,IAAA;EAAA;EACpDN,MAAM,GAAG,IAAIF,SAAS,CAACE,MAAM,CAAC;EAC9BI,MAAM,GAAGA,MAAM,CAACG,GAAG,CAACC,GAAG,CAAC;EACxBC,GAAG,EAAE,KAAK,MAAMC,KAAK,IAAIV,MAAM,EAAE;IAC/B,KAAK,MAAMW,KAAK,IAAIP,MAAM,EAAE;MAC1B,IAAI,CAACO,KAAK,CAACC,GAAG,CAACF,KAAK,CAAC,EAAE;QACrBV,MAAM,CAACa,MAAM,CAACH,KAAK,CAAC;QACpB,SAASD,GAAG;MACd;IACF;EACF;EACA,OAAOT,MAAM;AACf;AAEA,SAASQ,GAAGA,CAACR,MAAM,EAAE;EACnB,OAAOA,MAAM,YAAYF,SAAS,GAAGE,MAAM,GAAG,IAAIF,SAAS,CAACE,MAAM,CAAC;AACrE", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}