{"ast": null, "code": "\"use client\";\n\nimport createSvgIcon from './utils/createSvgIcon';\nimport { jsx as _jsx } from \"react/jsx-runtime\";\nexport default createSvgIcon(/*#__PURE__*/_jsx(\"path\", {\n  d: \"m20 18 2-2V4H2v12l2 2H0v2h24v-2zm-7-3.53v-2.19c-2.78 0-4.61.85-6 2.72.56-2.67 2.11-5.33 6-5.87V7l4 3.73z\"\n}), 'ScreenShareSharp');", "map": {"version": 3, "names": ["createSvgIcon", "jsx", "_jsx", "d"], "sources": ["C:/CMS/webapp/frontend/node_modules/@mui/icons-material/esm/ScreenShareSharp.js"], "sourcesContent": ["\"use client\";\n\nimport createSvgIcon from './utils/createSvgIcon';\nimport { jsx as _jsx } from \"react/jsx-runtime\";\nexport default createSvgIcon( /*#__PURE__*/_jsx(\"path\", {\n  d: \"m20 18 2-2V4H2v12l2 2H0v2h24v-2zm-7-3.53v-2.19c-2.78 0-4.61.85-6 2.72.56-2.67 2.11-5.33 6-5.87V7l4 3.73z\"\n}), 'ScreenShareSharp');"], "mappings": "AAAA,YAAY;;AAEZ,OAAOA,aAAa,MAAM,uBAAuB;AACjD,SAASC,GAAG,IAAIC,IAAI,QAAQ,mBAAmB;AAC/C,eAAeF,aAAa,CAAE,aAAaE,IAAI,CAAC,MAAM,EAAE;EACtDC,CAAC,EAAE;AACL,CAAC,CAAC,EAAE,kBAAkB,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}