{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\PycharmProjects\\\\CMS\\\\webapp\\\\frontend\\\\src\\\\components\\\\common\\\\RedirectToCaviVisualizza.js\",\n  _s = $RefreshSig$();\nimport React, { useEffect } from 'react';\nimport { useNavigate } from 'react-router-dom';\nimport { useGlobalContext } from '../../context/GlobalContext';\n\n// Componente che reindirizza alla pagina di visualizzazione cavi\n// e opzionalmente apre il dialog di modifica cavo\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst RedirectToCaviVisualizza = ({\n  openModificaDialog = false\n}) => {\n  _s();\n  const navigate = useNavigate();\n  const {\n    setOpenModificaCavoDialog\n  } = useGlobalContext();\n  useEffect(() => {\n    // Se richiesto, apre il dialog di modifica cavo\n    if (openModificaDialog) {\n      setOpenModificaCavoDialog(true);\n    }\n\n    // Reindirizza alla pagina di visualizzazione cavi\n    navigate('/dashboard/cavi/visualizza');\n  }, [navigate, setOpenModificaCavoDialog, openModificaDialog]);\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    style: {\n      display: 'flex',\n      justifyContent: 'center',\n      alignItems: 'center',\n      height: '100vh'\n    },\n    children: \"Reindirizzamento in corso...\"\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 22,\n    columnNumber: 5\n  }, this);\n};\n_s(RedirectToCaviVisualizza, \"3ZkpC5KQI9I5n3KTmJRdEGsLVMI=\", false, function () {\n  return [useNavigate, useGlobalContext];\n});\n_c = RedirectToCaviVisualizza;\nexport default RedirectToCaviVisualizza;\nvar _c;\n$RefreshReg$(_c, \"RedirectToCaviVisualizza\");", "map": {"version": 3, "names": ["React", "useEffect", "useNavigate", "useGlobalContext", "jsxDEV", "_jsxDEV", "RedirectToCaviVisualizza", "openModificaDialog", "_s", "navigate", "setOpenModificaCavoDialog", "style", "display", "justifyContent", "alignItems", "height", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/PycharmProjects/CMS/webapp/frontend/src/components/common/RedirectToCaviVisualizza.js"], "sourcesContent": ["import React, { useEffect } from 'react';\nimport { useNavigate } from 'react-router-dom';\nimport { useGlobalContext } from '../../context/GlobalContext';\n\n// Componente che reindirizza alla pagina di visualizzazione cavi\n// e opzionalmente apre il dialog di modifica cavo\nconst RedirectToCaviVisualizza = ({ openModificaDialog = false }) => {\n  const navigate = useNavigate();\n  const { setOpenModificaCavoDialog } = useGlobalContext();\n\n  useEffect(() => {\n    // Se richiesto, apre il dialog di modifica cavo\n    if (openModificaDialog) {\n      setOpenModificaCavoDialog(true);\n    }\n    \n    // Reindirizza alla pagina di visualizzazione cavi\n    navigate('/dashboard/cavi/visualizza');\n  }, [navigate, setOpenModificaCavoDialog, openModificaDialog]);\n\n  return (\n    <div style={{ display: 'flex', justifyContent: 'center', alignItems: 'center', height: '100vh' }}>\n      Reindirizzamento in corso...\n    </div>\n  );\n};\n\nexport default RedirectToCaviVisualizza;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,SAAS,QAAQ,OAAO;AACxC,SAASC,WAAW,QAAQ,kBAAkB;AAC9C,SAASC,gBAAgB,QAAQ,6BAA6B;;AAE9D;AACA;AAAA,SAAAC,MAAA,IAAAC,OAAA;AACA,MAAMC,wBAAwB,GAAGA,CAAC;EAAEC,kBAAkB,GAAG;AAAM,CAAC,KAAK;EAAAC,EAAA;EACnE,MAAMC,QAAQ,GAAGP,WAAW,CAAC,CAAC;EAC9B,MAAM;IAAEQ;EAA0B,CAAC,GAAGP,gBAAgB,CAAC,CAAC;EAExDF,SAAS,CAAC,MAAM;IACd;IACA,IAAIM,kBAAkB,EAAE;MACtBG,yBAAyB,CAAC,IAAI,CAAC;IACjC;;IAEA;IACAD,QAAQ,CAAC,4BAA4B,CAAC;EACxC,CAAC,EAAE,CAACA,QAAQ,EAAEC,yBAAyB,EAAEH,kBAAkB,CAAC,CAAC;EAE7D,oBACEF,OAAA;IAAKM,KAAK,EAAE;MAAEC,OAAO,EAAE,MAAM;MAAEC,cAAc,EAAE,QAAQ;MAAEC,UAAU,EAAE,QAAQ;MAAEC,MAAM,EAAE;IAAQ,CAAE;IAAAC,QAAA,EAAC;EAElG;IAAAC,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OAAK,CAAC;AAEV,CAAC;AAACZ,EAAA,CAnBIF,wBAAwB;EAAA,QACXJ,WAAW,EACUC,gBAAgB;AAAA;AAAAkB,EAAA,GAFlDf,wBAAwB;AAqB9B,eAAeA,wBAAwB;AAAC,IAAAe,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}