{"ast": null, "code": "import _extends from \"@babel/runtime/helpers/esm/extends\";\nexport const getPickersLocalization = pickersTranslations => {\n  return {\n    components: {\n      MuiLocalizationProvider: {\n        defaultProps: {\n          localeText: _extends({}, pickersTranslations)\n        }\n      }\n    }\n  };\n};", "map": {"version": 3, "names": ["_extends", "getPickersLocalization", "pickersTranslations", "components", "MuiLocalizationProvider", "defaultProps", "localeText"], "sources": ["C:/CMS/webapp/frontend/node_modules/@mui/x-date-pickers/esm/locales/utils/getPickersLocalization.js"], "sourcesContent": ["import _extends from \"@babel/runtime/helpers/esm/extends\";\nexport const getPickersLocalization = pickersTranslations => {\n  return {\n    components: {\n      MuiLocalizationProvider: {\n        defaultProps: {\n          localeText: _extends({}, pickersTranslations)\n        }\n      }\n    }\n  };\n};"], "mappings": "AAAA,OAAOA,QAAQ,MAAM,oCAAoC;AACzD,OAAO,MAAMC,sBAAsB,GAAGC,mBAAmB,IAAI;EAC3D,OAAO;IACLC,UAAU,EAAE;MACVC,uBAAuB,EAAE;QACvBC,YAAY,EAAE;UACZC,UAAU,EAAEN,QAAQ,CAAC,CAAC,CAAC,EAAEE,mBAAmB;QAC9C;MACF;IACF;EACF,CAAC;AACH,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}