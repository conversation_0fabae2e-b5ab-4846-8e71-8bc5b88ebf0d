{"ast": null, "code": "var _jsxFileName = \"C:\\\\CMS\\\\webapp\\\\frontend\\\\src\\\\components\\\\admin\\\\UserForm.js\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect } from 'react';\nimport { Box, TextField, Button, FormControl, InputLabel, Select, MenuItem, FormHelperText, Typography, Paper, Grid, Switch, FormControlLabel } from '@mui/material';\n// Date picker imports temporarily commented out due to dependency issues\n// import { DatePicker } from '@mui/x-date-pickers/DatePicker';\n// import { AdapterDateFns } from '@mui/x-date-pickers/AdapterDateFns';\n// import { LocalizationProvider } from '@mui/x-date-pickers/LocalizationProvider';\n// import { it } from 'date-fns/locale';\nimport userService from '../../services/userService';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst UserForm = ({\n  user,\n  onSave,\n  onCancel\n}) => {\n  _s();\n  const [formData, setFormData] = useState({\n    username: '',\n    password: '',\n    ruolo: 'user',\n    // L'amministratore può creare solo utenti standard\n    data_scadenza: null,\n    abilitato: true\n  });\n  const [errors, setErrors] = useState({});\n  const [loading, setLoading] = useState(false);\n  const [error, setError] = useState('');\n\n  // Inizializza il form con i dati dell'utente se presente\n  useEffect(() => {\n    if (user) {\n      setFormData({\n        username: user.username || '',\n        password: '',\n        // Non mostrare la password esistente\n        ruolo: user.ruolo || 'user',\n        data_scadenza: user.data_scadenza ? new Date(user.data_scadenza) : null,\n        abilitato: user.abilitato !== undefined ? user.abilitato : true\n      });\n    }\n  }, [user]);\n\n  // Gestisce il cambio dei campi del form\n  const handleChange = e => {\n    const {\n      name,\n      value,\n      checked\n    } = e.target;\n    setFormData({\n      ...formData,\n      [name]: name === 'abilitato' ? checked : value\n    });\n\n    // Resetta l'errore per il campo\n    if (errors[name]) {\n      setErrors({\n        ...errors,\n        [name]: ''\n      });\n    }\n  };\n\n  // Gestisce il cambio della data di scadenza\n  const handleDateChange = date => {\n    setFormData({\n      ...formData,\n      data_scadenza: date\n    });\n\n    // Resetta l'errore per il campo\n    if (errors.data_scadenza) {\n      setErrors({\n        ...errors,\n        data_scadenza: ''\n      });\n    }\n  };\n\n  // Valida il form\n  const validateForm = () => {\n    const newErrors = {};\n    if (!formData.username.trim()) {\n      newErrors.username = 'Username obbligatorio';\n    }\n    if (!user && !formData.password.trim()) {\n      newErrors.password = 'Password obbligatoria';\n    }\n    if (!formData.ruolo) {\n      newErrors.ruolo = 'Ruolo obbligatorio';\n    }\n    setErrors(newErrors);\n    return Object.keys(newErrors).length === 0;\n  };\n\n  // Gestisce il salvataggio dell'utente\n  const handleSubmit = async e => {\n    e.preventDefault();\n    if (!validateForm()) {\n      return;\n    }\n    setLoading(true);\n    setError('');\n    try {\n      // Prepara i dati da inviare\n      const userData = {\n        ...formData\n      };\n\n      // Rimuovi la password se è vuota (modifica utente)\n      if (user && !userData.password.trim()) {\n        delete userData.password;\n      }\n\n      // Converti la data in formato ISO\n      if (userData.data_scadenza) {\n        userData.data_scadenza = userData.data_scadenza.toISOString().split('T')[0];\n      }\n      let result;\n      if (user) {\n        // Aggiorna l'utente esistente\n        result = await userService.updateUser(user.id_utente, userData);\n      } else {\n        // Crea un nuovo utente\n        result = await userService.createUser(userData);\n      }\n      onSave(result);\n    } catch (err) {\n      setError(err.detail || 'Errore durante il salvataggio dell\\'utente');\n    } finally {\n      setLoading(false);\n    }\n  };\n  return /*#__PURE__*/_jsxDEV(Paper, {\n    sx: {\n      p: 3\n    },\n    children: [/*#__PURE__*/_jsxDEV(Typography, {\n      variant: \"h6\",\n      gutterBottom: true,\n      children: user ? 'Modifica Utente' : 'Nuovo Utente'\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 148,\n      columnNumber: 7\n    }, this), error && /*#__PURE__*/_jsxDEV(Typography, {\n      color: \"error\",\n      sx: {\n        mb: 2\n      },\n      children: error\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 153,\n      columnNumber: 9\n    }, this), /*#__PURE__*/_jsxDEV(Box, {\n      component: \"form\",\n      onSubmit: handleSubmit,\n      noValidate: true,\n      children: /*#__PURE__*/_jsxDEV(Grid, {\n        container: true,\n        spacing: 2,\n        children: [/*#__PURE__*/_jsxDEV(Grid, {\n          item: true,\n          xs: 12,\n          sm: 6,\n          children: /*#__PURE__*/_jsxDEV(TextField, {\n            margin: \"normal\",\n            required: true,\n            fullWidth: true,\n            id: \"username\",\n            label: \"Username\",\n            name: \"username\",\n            autoComplete: \"username\",\n            value: formData.username,\n            onChange: handleChange,\n            error: !!errors.username,\n            helperText: errors.username,\n            disabled: loading\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 161,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 160,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Grid, {\n          item: true,\n          xs: 12,\n          sm: 6,\n          children: /*#__PURE__*/_jsxDEV(TextField, {\n            margin: \"normal\",\n            required: !user,\n            fullWidth: true,\n            name: \"password\",\n            label: user ? 'Nuova Password (lascia vuoto per non modificare)' : 'Password',\n            type: \"password\",\n            id: \"password\",\n            autoComplete: \"new-password\",\n            value: formData.password,\n            onChange: handleChange,\n            error: !!errors.password,\n            helperText: errors.password,\n            disabled: loading\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 178,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 177,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Grid, {\n          item: true,\n          xs: 12,\n          sm: 6,\n          children: /*#__PURE__*/_jsxDEV(FormControl, {\n            fullWidth: true,\n            margin: \"normal\",\n            error: !!errors.ruolo,\n            children: [/*#__PURE__*/_jsxDEV(InputLabel, {\n              id: \"ruolo-label\",\n              children: \"Ruolo\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 197,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Select, {\n              labelId: \"ruolo-label\",\n              id: \"ruolo\",\n              name: \"ruolo\",\n              value: formData.ruolo,\n              onChange: handleChange,\n              label: \"Ruolo\",\n              disabled: true /* Il ruolo è sempre 'user' per i nuovi utenti creati dall'amministratore */,\n              children: /*#__PURE__*/_jsxDEV(MenuItem, {\n                value: \"user\",\n                children: \"Utente Standard\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 207,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 198,\n              columnNumber: 15\n            }, this), errors.ruolo && /*#__PURE__*/_jsxDEV(FormHelperText, {\n              children: errors.ruolo\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 210,\n              columnNumber: 32\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 196,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 195,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Grid, {\n          item: true,\n          xs: 12,\n          sm: 6,\n          children: /*#__PURE__*/_jsxDEV(TextField, {\n            margin: \"normal\",\n            fullWidth: true,\n            id: \"data_scadenza\",\n            label: \"Data Scadenza (YYYY-MM-DD) (opzionale)\",\n            name: \"data_scadenza\",\n            value: formData.data_scadenza ? formData.data_scadenza.toISOString().split('T')[0] : '',\n            onChange: e => {\n              try {\n                const dateStr = e.target.value;\n                const date = dateStr ? new Date(dateStr) : null;\n                handleDateChange(date);\n              } catch (err) {\n                console.error('Invalid date format', err);\n              }\n            },\n            error: !!errors.data_scadenza,\n            helperText: errors.data_scadenza || 'Formato: YYYY-MM-DD',\n            disabled: loading\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 216,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 214,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Grid, {\n          item: true,\n          xs: 12,\n          children: /*#__PURE__*/_jsxDEV(FormControlLabel, {\n            control: /*#__PURE__*/_jsxDEV(Switch, {\n              checked: formData.abilitato,\n              onChange: handleChange,\n              name: \"abilitato\",\n              color: \"primary\",\n              disabled: loading || user && user.ruolo === 'owner'\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 241,\n              columnNumber: 17\n            }, this),\n            label: \"Utente abilitato\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 239,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 238,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Grid, {\n          item: true,\n          xs: 12,\n          children: /*#__PURE__*/_jsxDEV(Box, {\n            sx: {\n              display: 'flex',\n              justifyContent: 'flex-end',\n              mt: 2\n            },\n            children: [/*#__PURE__*/_jsxDEV(Button, {\n              onClick: onCancel,\n              sx: {\n                mr: 1\n              },\n              disabled: loading,\n              children: \"Annulla\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 255,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Button, {\n              type: \"submit\",\n              variant: \"contained\",\n              disabled: loading,\n              children: loading ? 'Salvataggio...' : 'Salva'\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 262,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 254,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 253,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 159,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 158,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 147,\n    columnNumber: 5\n  }, this);\n};\n_s(UserForm, \"X3gIsxgTj/Zqt/Zjfo6T3Z1RMPs=\");\n_c = UserForm;\nexport default UserForm;\nvar _c;\n$RefreshReg$(_c, \"UserForm\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "Box", "TextField", "<PERSON><PERSON>", "FormControl", "InputLabel", "Select", "MenuItem", "FormHelperText", "Typography", "Paper", "Grid", "Switch", "FormControlLabel", "userService", "jsxDEV", "_jsxDEV", "UserForm", "user", "onSave", "onCancel", "_s", "formData", "setFormData", "username", "password", "ruolo", "data_scadenza", "abilitato", "errors", "setErrors", "loading", "setLoading", "error", "setError", "Date", "undefined", "handleChange", "e", "name", "value", "checked", "target", "handleDateChange", "date", "validateForm", "newErrors", "trim", "Object", "keys", "length", "handleSubmit", "preventDefault", "userData", "toISOString", "split", "result", "updateUser", "id_utente", "createUser", "err", "detail", "sx", "p", "children", "variant", "gutterBottom", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "color", "mb", "component", "onSubmit", "noValidate", "container", "spacing", "item", "xs", "sm", "margin", "required", "fullWidth", "id", "label", "autoComplete", "onChange", "helperText", "disabled", "type", "labelId", "dateStr", "console", "control", "display", "justifyContent", "mt", "onClick", "mr", "_c", "$RefreshReg$"], "sources": ["C:/CMS/webapp/frontend/src/components/admin/UserForm.js"], "sourcesContent": ["import React, { useState, useEffect } from 'react';\nimport {\n  Box,\n  TextField,\n  Button,\n  FormControl,\n  InputLabel,\n  Select,\n  MenuItem,\n  FormHelperText,\n  Typography,\n  Paper,\n  Grid,\n  Switch,\n  FormControlLabel\n} from '@mui/material';\n// Date picker imports temporarily commented out due to dependency issues\n// import { DatePicker } from '@mui/x-date-pickers/DatePicker';\n// import { AdapterDateFns } from '@mui/x-date-pickers/AdapterDateFns';\n// import { LocalizationProvider } from '@mui/x-date-pickers/LocalizationProvider';\n// import { it } from 'date-fns/locale';\nimport userService from '../../services/userService';\n\nconst UserForm = ({ user, onSave, onCancel }) => {\n  const [formData, setFormData] = useState({\n    username: '',\n    password: '',\n    ruolo: 'user', // L'amministratore può creare solo utenti standard\n    data_scadenza: null,\n    abilitato: true\n  });\n  const [errors, setErrors] = useState({});\n  const [loading, setLoading] = useState(false);\n  const [error, setError] = useState('');\n\n  // Inizializza il form con i dati dell'utente se presente\n  useEffect(() => {\n    if (user) {\n      setFormData({\n        username: user.username || '',\n        password: '', // Non mostrare la password esistente\n        ruolo: user.ruolo || 'user',\n        data_scadenza: user.data_scadenza ? new Date(user.data_scadenza) : null,\n        abilitato: user.abilitato !== undefined ? user.abilitato : true\n      });\n    }\n  }, [user]);\n\n  // Gestisce il cambio dei campi del form\n  const handleChange = (e) => {\n    const { name, value, checked } = e.target;\n    setFormData({\n      ...formData,\n      [name]: name === 'abilitato' ? checked : value\n    });\n\n    // Resetta l'errore per il campo\n    if (errors[name]) {\n      setErrors({\n        ...errors,\n        [name]: ''\n      });\n    }\n  };\n\n  // Gestisce il cambio della data di scadenza\n  const handleDateChange = (date) => {\n    setFormData({\n      ...formData,\n      data_scadenza: date\n    });\n\n    // Resetta l'errore per il campo\n    if (errors.data_scadenza) {\n      setErrors({\n        ...errors,\n        data_scadenza: ''\n      });\n    }\n  };\n\n  // Valida il form\n  const validateForm = () => {\n    const newErrors = {};\n\n    if (!formData.username.trim()) {\n      newErrors.username = 'Username obbligatorio';\n    }\n\n    if (!user && !formData.password.trim()) {\n      newErrors.password = 'Password obbligatoria';\n    }\n\n    if (!formData.ruolo) {\n      newErrors.ruolo = 'Ruolo obbligatorio';\n    }\n\n    setErrors(newErrors);\n    return Object.keys(newErrors).length === 0;\n  };\n\n  // Gestisce il salvataggio dell'utente\n  const handleSubmit = async (e) => {\n    e.preventDefault();\n\n    if (!validateForm()) {\n      return;\n    }\n\n    setLoading(true);\n    setError('');\n\n    try {\n      // Prepara i dati da inviare\n      const userData = {\n        ...formData\n      };\n\n      // Rimuovi la password se è vuota (modifica utente)\n      if (user && !userData.password.trim()) {\n        delete userData.password;\n      }\n\n      // Converti la data in formato ISO\n      if (userData.data_scadenza) {\n        userData.data_scadenza = userData.data_scadenza.toISOString().split('T')[0];\n      }\n\n      let result;\n      if (user) {\n        // Aggiorna l'utente esistente\n        result = await userService.updateUser(user.id_utente, userData);\n      } else {\n        // Crea un nuovo utente\n        result = await userService.createUser(userData);\n      }\n\n      onSave(result);\n    } catch (err) {\n      setError(err.detail || 'Errore durante il salvataggio dell\\'utente');\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  return (\n    <Paper sx={{ p: 3 }}>\n      <Typography variant=\"h6\" gutterBottom>\n        {user ? 'Modifica Utente' : 'Nuovo Utente'}\n      </Typography>\n\n      {error && (\n        <Typography color=\"error\" sx={{ mb: 2 }}>\n          {error}\n        </Typography>\n      )}\n\n      <Box component=\"form\" onSubmit={handleSubmit} noValidate>\n        <Grid container spacing={2}>\n          <Grid item xs={12} sm={6}>\n            <TextField\n              margin=\"normal\"\n              required\n              fullWidth\n              id=\"username\"\n              label=\"Username\"\n              name=\"username\"\n              autoComplete=\"username\"\n              value={formData.username}\n              onChange={handleChange}\n              error={!!errors.username}\n              helperText={errors.username}\n              disabled={loading}\n            />\n          </Grid>\n\n          <Grid item xs={12} sm={6}>\n            <TextField\n              margin=\"normal\"\n              required={!user}\n              fullWidth\n              name=\"password\"\n              label={user ? 'Nuova Password (lascia vuoto per non modificare)' : 'Password'}\n              type=\"password\"\n              id=\"password\"\n              autoComplete=\"new-password\"\n              value={formData.password}\n              onChange={handleChange}\n              error={!!errors.password}\n              helperText={errors.password}\n              disabled={loading}\n            />\n          </Grid>\n\n          <Grid item xs={12} sm={6}>\n            <FormControl fullWidth margin=\"normal\" error={!!errors.ruolo}>\n              <InputLabel id=\"ruolo-label\">Ruolo</InputLabel>\n              <Select\n                labelId=\"ruolo-label\"\n                id=\"ruolo\"\n                name=\"ruolo\"\n                value={formData.ruolo}\n                onChange={handleChange}\n                label=\"Ruolo\"\n                disabled={true} /* Il ruolo è sempre 'user' per i nuovi utenti creati dall'amministratore */\n              >\n                <MenuItem value=\"user\">Utente Standard</MenuItem>\n                {/* Rimossa opzione Utente Cantiere poiché gli utenti cantiere vengono creati dagli utenti standard */}\n              </Select>\n              {errors.ruolo && <FormHelperText>{errors.ruolo}</FormHelperText>}\n            </FormControl>\n          </Grid>\n\n          <Grid item xs={12} sm={6}>\n            {/* Date picker temporarily replaced with a text field */}\n            <TextField\n              margin=\"normal\"\n              fullWidth\n              id=\"data_scadenza\"\n              label=\"Data Scadenza (YYYY-MM-DD) (opzionale)\"\n              name=\"data_scadenza\"\n              value={formData.data_scadenza ? formData.data_scadenza.toISOString().split('T')[0] : ''}\n              onChange={(e) => {\n                try {\n                  const dateStr = e.target.value;\n                  const date = dateStr ? new Date(dateStr) : null;\n                  handleDateChange(date);\n                } catch (err) {\n                  console.error('Invalid date format', err);\n                }\n              }}\n              error={!!errors.data_scadenza}\n              helperText={errors.data_scadenza || 'Formato: YYYY-MM-DD'}\n              disabled={loading}\n            />\n          </Grid>\n\n          <Grid item xs={12}>\n            <FormControlLabel\n              control={\n                <Switch\n                  checked={formData.abilitato}\n                  onChange={handleChange}\n                  name=\"abilitato\"\n                  color=\"primary\"\n                  disabled={loading || (user && user.ruolo === 'owner')}\n                />\n              }\n              label=\"Utente abilitato\"\n            />\n          </Grid>\n\n          <Grid item xs={12}>\n            <Box sx={{ display: 'flex', justifyContent: 'flex-end', mt: 2 }}>\n              <Button\n                onClick={onCancel}\n                sx={{ mr: 1 }}\n                disabled={loading}\n              >\n                Annulla\n              </Button>\n              <Button\n                type=\"submit\"\n                variant=\"contained\"\n                disabled={loading}\n              >\n                {loading ? 'Salvataggio...' : 'Salva'}\n              </Button>\n            </Box>\n          </Grid>\n        </Grid>\n      </Box>\n    </Paper>\n  );\n};\n\nexport default UserForm;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAClD,SACEC,GAAG,EACHC,SAAS,EACTC,MAAM,EACNC,WAAW,EACXC,UAAU,EACVC,MAAM,EACNC,QAAQ,EACRC,cAAc,EACdC,UAAU,EACVC,KAAK,EACLC,IAAI,EACJC,MAAM,EACNC,gBAAgB,QACX,eAAe;AACtB;AACA;AACA;AACA;AACA;AACA,OAAOC,WAAW,MAAM,4BAA4B;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAErD,MAAMC,QAAQ,GAAGA,CAAC;EAAEC,IAAI;EAAEC,MAAM;EAAEC;AAAS,CAAC,KAAK;EAAAC,EAAA;EAC/C,MAAM,CAACC,QAAQ,EAAEC,WAAW,CAAC,GAAGxB,QAAQ,CAAC;IACvCyB,QAAQ,EAAE,EAAE;IACZC,QAAQ,EAAE,EAAE;IACZC,KAAK,EAAE,MAAM;IAAE;IACfC,aAAa,EAAE,IAAI;IACnBC,SAAS,EAAE;EACb,CAAC,CAAC;EACF,MAAM,CAACC,MAAM,EAAEC,SAAS,CAAC,GAAG/B,QAAQ,CAAC,CAAC,CAAC,CAAC;EACxC,MAAM,CAACgC,OAAO,EAAEC,UAAU,CAAC,GAAGjC,QAAQ,CAAC,KAAK,CAAC;EAC7C,MAAM,CAACkC,KAAK,EAAEC,QAAQ,CAAC,GAAGnC,QAAQ,CAAC,EAAE,CAAC;;EAEtC;EACAC,SAAS,CAAC,MAAM;IACd,IAAIkB,IAAI,EAAE;MACRK,WAAW,CAAC;QACVC,QAAQ,EAAEN,IAAI,CAACM,QAAQ,IAAI,EAAE;QAC7BC,QAAQ,EAAE,EAAE;QAAE;QACdC,KAAK,EAAER,IAAI,CAACQ,KAAK,IAAI,MAAM;QAC3BC,aAAa,EAAET,IAAI,CAACS,aAAa,GAAG,IAAIQ,IAAI,CAACjB,IAAI,CAACS,aAAa,CAAC,GAAG,IAAI;QACvEC,SAAS,EAAEV,IAAI,CAACU,SAAS,KAAKQ,SAAS,GAAGlB,IAAI,CAACU,SAAS,GAAG;MAC7D,CAAC,CAAC;IACJ;EACF,CAAC,EAAE,CAACV,IAAI,CAAC,CAAC;;EAEV;EACA,MAAMmB,YAAY,GAAIC,CAAC,IAAK;IAC1B,MAAM;MAAEC,IAAI;MAAEC,KAAK;MAAEC;IAAQ,CAAC,GAAGH,CAAC,CAACI,MAAM;IACzCnB,WAAW,CAAC;MACV,GAAGD,QAAQ;MACX,CAACiB,IAAI,GAAGA,IAAI,KAAK,WAAW,GAAGE,OAAO,GAAGD;IAC3C,CAAC,CAAC;;IAEF;IACA,IAAIX,MAAM,CAACU,IAAI,CAAC,EAAE;MAChBT,SAAS,CAAC;QACR,GAAGD,MAAM;QACT,CAACU,IAAI,GAAG;MACV,CAAC,CAAC;IACJ;EACF,CAAC;;EAED;EACA,MAAMI,gBAAgB,GAAIC,IAAI,IAAK;IACjCrB,WAAW,CAAC;MACV,GAAGD,QAAQ;MACXK,aAAa,EAAEiB;IACjB,CAAC,CAAC;;IAEF;IACA,IAAIf,MAAM,CAACF,aAAa,EAAE;MACxBG,SAAS,CAAC;QACR,GAAGD,MAAM;QACTF,aAAa,EAAE;MACjB,CAAC,CAAC;IACJ;EACF,CAAC;;EAED;EACA,MAAMkB,YAAY,GAAGA,CAAA,KAAM;IACzB,MAAMC,SAAS,GAAG,CAAC,CAAC;IAEpB,IAAI,CAACxB,QAAQ,CAACE,QAAQ,CAACuB,IAAI,CAAC,CAAC,EAAE;MAC7BD,SAAS,CAACtB,QAAQ,GAAG,uBAAuB;IAC9C;IAEA,IAAI,CAACN,IAAI,IAAI,CAACI,QAAQ,CAACG,QAAQ,CAACsB,IAAI,CAAC,CAAC,EAAE;MACtCD,SAAS,CAACrB,QAAQ,GAAG,uBAAuB;IAC9C;IAEA,IAAI,CAACH,QAAQ,CAACI,KAAK,EAAE;MACnBoB,SAAS,CAACpB,KAAK,GAAG,oBAAoB;IACxC;IAEAI,SAAS,CAACgB,SAAS,CAAC;IACpB,OAAOE,MAAM,CAACC,IAAI,CAACH,SAAS,CAAC,CAACI,MAAM,KAAK,CAAC;EAC5C,CAAC;;EAED;EACA,MAAMC,YAAY,GAAG,MAAOb,CAAC,IAAK;IAChCA,CAAC,CAACc,cAAc,CAAC,CAAC;IAElB,IAAI,CAACP,YAAY,CAAC,CAAC,EAAE;MACnB;IACF;IAEAb,UAAU,CAAC,IAAI,CAAC;IAChBE,QAAQ,CAAC,EAAE,CAAC;IAEZ,IAAI;MACF;MACA,MAAMmB,QAAQ,GAAG;QACf,GAAG/B;MACL,CAAC;;MAED;MACA,IAAIJ,IAAI,IAAI,CAACmC,QAAQ,CAAC5B,QAAQ,CAACsB,IAAI,CAAC,CAAC,EAAE;QACrC,OAAOM,QAAQ,CAAC5B,QAAQ;MAC1B;;MAEA;MACA,IAAI4B,QAAQ,CAAC1B,aAAa,EAAE;QAC1B0B,QAAQ,CAAC1B,aAAa,GAAG0B,QAAQ,CAAC1B,aAAa,CAAC2B,WAAW,CAAC,CAAC,CAACC,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC;MAC7E;MAEA,IAAIC,MAAM;MACV,IAAItC,IAAI,EAAE;QACR;QACAsC,MAAM,GAAG,MAAM1C,WAAW,CAAC2C,UAAU,CAACvC,IAAI,CAACwC,SAAS,EAAEL,QAAQ,CAAC;MACjE,CAAC,MAAM;QACL;QACAG,MAAM,GAAG,MAAM1C,WAAW,CAAC6C,UAAU,CAACN,QAAQ,CAAC;MACjD;MAEAlC,MAAM,CAACqC,MAAM,CAAC;IAChB,CAAC,CAAC,OAAOI,GAAG,EAAE;MACZ1B,QAAQ,CAAC0B,GAAG,CAACC,MAAM,IAAI,4CAA4C,CAAC;IACtE,CAAC,SAAS;MACR7B,UAAU,CAAC,KAAK,CAAC;IACnB;EACF,CAAC;EAED,oBACEhB,OAAA,CAACN,KAAK;IAACoD,EAAE,EAAE;MAAEC,CAAC,EAAE;IAAE,CAAE;IAAAC,QAAA,gBAClBhD,OAAA,CAACP,UAAU;MAACwD,OAAO,EAAC,IAAI;MAACC,YAAY;MAAAF,QAAA,EAClC9C,IAAI,GAAG,iBAAiB,GAAG;IAAc;MAAAiD,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAChC,CAAC,EAEZrC,KAAK,iBACJjB,OAAA,CAACP,UAAU;MAAC8D,KAAK,EAAC,OAAO;MAACT,EAAE,EAAE;QAAEU,EAAE,EAAE;MAAE,CAAE;MAAAR,QAAA,EACrC/B;IAAK;MAAAkC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACI,CACb,eAEDtD,OAAA,CAACf,GAAG;MAACwE,SAAS,EAAC,MAAM;MAACC,QAAQ,EAAEvB,YAAa;MAACwB,UAAU;MAAAX,QAAA,eACtDhD,OAAA,CAACL,IAAI;QAACiE,SAAS;QAACC,OAAO,EAAE,CAAE;QAAAb,QAAA,gBACzBhD,OAAA,CAACL,IAAI;UAACmE,IAAI;UAACC,EAAE,EAAE,EAAG;UAACC,EAAE,EAAE,CAAE;UAAAhB,QAAA,eACvBhD,OAAA,CAACd,SAAS;YACR+E,MAAM,EAAC,QAAQ;YACfC,QAAQ;YACRC,SAAS;YACTC,EAAE,EAAC,UAAU;YACbC,KAAK,EAAC,UAAU;YAChB9C,IAAI,EAAC,UAAU;YACf+C,YAAY,EAAC,UAAU;YACvB9C,KAAK,EAAElB,QAAQ,CAACE,QAAS;YACzB+D,QAAQ,EAAElD,YAAa;YACvBJ,KAAK,EAAE,CAAC,CAACJ,MAAM,CAACL,QAAS;YACzBgE,UAAU,EAAE3D,MAAM,CAACL,QAAS;YAC5BiE,QAAQ,EAAE1D;UAAQ;YAAAoC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACnB;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACE,CAAC,eAEPtD,OAAA,CAACL,IAAI;UAACmE,IAAI;UAACC,EAAE,EAAE,EAAG;UAACC,EAAE,EAAE,CAAE;UAAAhB,QAAA,eACvBhD,OAAA,CAACd,SAAS;YACR+E,MAAM,EAAC,QAAQ;YACfC,QAAQ,EAAE,CAAChE,IAAK;YAChBiE,SAAS;YACT5C,IAAI,EAAC,UAAU;YACf8C,KAAK,EAAEnE,IAAI,GAAG,kDAAkD,GAAG,UAAW;YAC9EwE,IAAI,EAAC,UAAU;YACfN,EAAE,EAAC,UAAU;YACbE,YAAY,EAAC,cAAc;YAC3B9C,KAAK,EAAElB,QAAQ,CAACG,QAAS;YACzB8D,QAAQ,EAAElD,YAAa;YACvBJ,KAAK,EAAE,CAAC,CAACJ,MAAM,CAACJ,QAAS;YACzB+D,UAAU,EAAE3D,MAAM,CAACJ,QAAS;YAC5BgE,QAAQ,EAAE1D;UAAQ;YAAAoC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACnB;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACE,CAAC,eAEPtD,OAAA,CAACL,IAAI;UAACmE,IAAI;UAACC,EAAE,EAAE,EAAG;UAACC,EAAE,EAAE,CAAE;UAAAhB,QAAA,eACvBhD,OAAA,CAACZ,WAAW;YAAC+E,SAAS;YAACF,MAAM,EAAC,QAAQ;YAAChD,KAAK,EAAE,CAAC,CAACJ,MAAM,CAACH,KAAM;YAAAsC,QAAA,gBAC3DhD,OAAA,CAACX,UAAU;cAAC+E,EAAE,EAAC,aAAa;cAAApB,QAAA,EAAC;YAAK;cAAAG,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAY,CAAC,eAC/CtD,OAAA,CAACV,MAAM;cACLqF,OAAO,EAAC,aAAa;cACrBP,EAAE,EAAC,OAAO;cACV7C,IAAI,EAAC,OAAO;cACZC,KAAK,EAAElB,QAAQ,CAACI,KAAM;cACtB6D,QAAQ,EAAElD,YAAa;cACvBgD,KAAK,EAAC,OAAO;cACbI,QAAQ,EAAE,IAAK,CAAC;cAAAzB,QAAA,eAEhBhD,OAAA,CAACT,QAAQ;gBAACiC,KAAK,EAAC,MAAM;gBAAAwB,QAAA,EAAC;cAAe;gBAAAG,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAU;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAE3C,CAAC,EACRzC,MAAM,CAACH,KAAK,iBAAIV,OAAA,CAACR,cAAc;cAAAwD,QAAA,EAAEnC,MAAM,CAACH;YAAK;cAAAyC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAiB,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACrD;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACV,CAAC,eAEPtD,OAAA,CAACL,IAAI;UAACmE,IAAI;UAACC,EAAE,EAAE,EAAG;UAACC,EAAE,EAAE,CAAE;UAAAhB,QAAA,eAEvBhD,OAAA,CAACd,SAAS;YACR+E,MAAM,EAAC,QAAQ;YACfE,SAAS;YACTC,EAAE,EAAC,eAAe;YAClBC,KAAK,EAAC,wCAAwC;YAC9C9C,IAAI,EAAC,eAAe;YACpBC,KAAK,EAAElB,QAAQ,CAACK,aAAa,GAAGL,QAAQ,CAACK,aAAa,CAAC2B,WAAW,CAAC,CAAC,CAACC,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,GAAG,EAAG;YACxFgC,QAAQ,EAAGjD,CAAC,IAAK;cACf,IAAI;gBACF,MAAMsD,OAAO,GAAGtD,CAAC,CAACI,MAAM,CAACF,KAAK;gBAC9B,MAAMI,IAAI,GAAGgD,OAAO,GAAG,IAAIzD,IAAI,CAACyD,OAAO,CAAC,GAAG,IAAI;gBAC/CjD,gBAAgB,CAACC,IAAI,CAAC;cACxB,CAAC,CAAC,OAAOgB,GAAG,EAAE;gBACZiC,OAAO,CAAC5D,KAAK,CAAC,qBAAqB,EAAE2B,GAAG,CAAC;cAC3C;YACF,CAAE;YACF3B,KAAK,EAAE,CAAC,CAACJ,MAAM,CAACF,aAAc;YAC9B6D,UAAU,EAAE3D,MAAM,CAACF,aAAa,IAAI,qBAAsB;YAC1D8D,QAAQ,EAAE1D;UAAQ;YAAAoC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACnB;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACE,CAAC,eAEPtD,OAAA,CAACL,IAAI;UAACmE,IAAI;UAACC,EAAE,EAAE,EAAG;UAAAf,QAAA,eAChBhD,OAAA,CAACH,gBAAgB;YACfiF,OAAO,eACL9E,OAAA,CAACJ,MAAM;cACL6B,OAAO,EAAEnB,QAAQ,CAACM,SAAU;cAC5B2D,QAAQ,EAAElD,YAAa;cACvBE,IAAI,EAAC,WAAW;cAChBgC,KAAK,EAAC,SAAS;cACfkB,QAAQ,EAAE1D,OAAO,IAAKb,IAAI,IAAIA,IAAI,CAACQ,KAAK,KAAK;YAAS;cAAAyC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACvD,CACF;YACDe,KAAK,EAAC;UAAkB;YAAAlB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACzB;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACE,CAAC,eAEPtD,OAAA,CAACL,IAAI;UAACmE,IAAI;UAACC,EAAE,EAAE,EAAG;UAAAf,QAAA,eAChBhD,OAAA,CAACf,GAAG;YAAC6D,EAAE,EAAE;cAAEiC,OAAO,EAAE,MAAM;cAAEC,cAAc,EAAE,UAAU;cAAEC,EAAE,EAAE;YAAE,CAAE;YAAAjC,QAAA,gBAC9DhD,OAAA,CAACb,MAAM;cACL+F,OAAO,EAAE9E,QAAS;cAClB0C,EAAE,EAAE;gBAAEqC,EAAE,EAAE;cAAE,CAAE;cACdV,QAAQ,EAAE1D,OAAQ;cAAAiC,QAAA,EACnB;YAED;cAAAG,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC,eACTtD,OAAA,CAACb,MAAM;cACLuF,IAAI,EAAC,QAAQ;cACbzB,OAAO,EAAC,WAAW;cACnBwB,QAAQ,EAAE1D,OAAQ;cAAAiC,QAAA,EAEjBjC,OAAO,GAAG,gBAAgB,GAAG;YAAO;cAAAoC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC/B,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACN;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACF,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACJ,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACD,CAAC;AAEZ,CAAC;AAACjD,EAAA,CA3PIJ,QAAQ;AAAAmF,EAAA,GAARnF,QAAQ;AA6Pd,eAAeA,QAAQ;AAAC,IAAAmF,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}