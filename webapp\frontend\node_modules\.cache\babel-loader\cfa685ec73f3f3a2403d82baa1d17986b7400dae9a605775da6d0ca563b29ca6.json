{"ast": null, "code": "import React from'react';import{<PERSON><PERSON><PERSON>,<PERSON>,<PERSON><PERSON><PERSON><PERSON>,<PERSON><PERSON><PERSON><PERSON>,<PERSON><PERSON><PERSON>,<PERSON><PERSON><PERSON>,<PERSON>,Respons<PERSON><PERSON><PERSON><PERSON>,<PERSON><PERSON><PERSON>,<PERSON>,Cell,Composed<PERSON>hart,Line}from'recharts';import{Box,Typography,Grid,Paper,Chip}from'@mui/material';import{jsx as _jsx,jsxs as _jsxs}from\"react/jsx-runtime\";const COLORS={'Installato':'#2e7d32','Da Installare':'#ed6c02','In Progresso':'#1976d2','Spare':'#9c27b0','Sospeso':'#d32f2f'};const STATUS_COLORS={primary:'#1976d2',secondary:'#dc004e',success:'#2e7d32',warning:'#ed6c02',info:'#0288d1',error:'#d32f2f'};const CaviStatoChart=_ref=>{let{data}=_ref;if(!data||!data.cavi_per_stato)return null;// Prepara dati per i grafici\nconst statoData=data.cavi_per_stato.map(stato=>{var _stato$stato;return{...stato,stato_short:((_stato$stato=stato.stato)===null||_stato$stato===void 0?void 0:_stato$stato.length)>12?stato.stato.substring(0,12)+'...':stato.stato,color:COLORS[stato.stato]||STATUS_COLORS.info,efficienza:stato.metri_teorici>0?stato.metri_reali/stato.metri_teorici*100:0};});// Calcola totali\nconst totali=statoData.reduce((acc,stato)=>{acc.cavi+=stato.num_cavi||0;acc.metri_teorici+=stato.metri_teorici||0;acc.metri_reali+=stato.metri_reali||0;return acc;},{cavi:0,metri_teorici:0,metri_reali:0});// Dati per grafico a torta\nconst pieData=statoData.map(stato=>({name:stato.stato,value:stato.num_cavi,color:stato.color}));// Dati per confronto metri\nconst metriData=statoData.map(stato=>({stato:stato.stato_short,stato_full:stato.stato,metri_teorici:stato.metri_teorici||0,metri_reali:stato.metri_reali||0,differenza:(stato.metri_reali||0)-(stato.metri_teorici||0)}));const CustomTooltip=_ref2=>{let{active,payload,label}=_ref2;if(active&&payload&&payload.length){return/*#__PURE__*/_jsxs(Paper,{sx:{p:1,border:'1px solid #ccc'},children:[/*#__PURE__*/_jsx(Typography,{variant:\"body2\",children:`${label}`}),payload.map((entry,index)=>/*#__PURE__*/_jsx(Typography,{variant:\"body2\",style:{color:entry.color},children:`${entry.name}: ${typeof entry.value==='number'?entry.value.toFixed(2):entry.value}`},index))]});}return null;};const renderCustomizedLabel=_ref3=>{let{cx,cy,midAngle,innerRadius,outerRadius,percent}=_ref3;if(percent<0.05)return null;const RADIAN=Math.PI/180;const radius=innerRadius+(outerRadius-innerRadius)*0.5;const x=cx+radius*Math.cos(-midAngle*RADIAN);const y=cy+radius*Math.sin(-midAngle*RADIAN);return/*#__PURE__*/_jsx(\"text\",{x:x,y:y,fill:\"white\",textAnchor:x>cx?'start':'end',dominantBaseline:\"central\",fontSize:\"12\",fontWeight:\"bold\",children:`${(percent*100).toFixed(0)}%`});};return/*#__PURE__*/_jsxs(Box,{sx:{mt:3},children:[/*#__PURE__*/_jsx(Typography,{variant:\"h6\",gutterBottom:true,children:\"Analisi Cavi per Stato di Installazione\"}),/*#__PURE__*/_jsxs(Grid,{container:true,spacing:3,children:[/*#__PURE__*/_jsx(Grid,{item:true,xs:12,children:/*#__PURE__*/_jsxs(Paper,{sx:{p:2},children:[/*#__PURE__*/_jsx(Typography,{variant:\"subtitle1\",gutterBottom:true,children:\"Riepilogo Generale\"}),/*#__PURE__*/_jsxs(Grid,{container:true,spacing:2,children:[/*#__PURE__*/_jsx(Grid,{item:true,xs:12,sm:4,children:/*#__PURE__*/_jsxs(Box,{sx:{textAlign:'center',p:1,border:'1px solid #e0e0e0',borderRadius:1},children:[/*#__PURE__*/_jsx(Typography,{variant:\"h6\",color:\"primary\",children:totali.cavi}),/*#__PURE__*/_jsx(Typography,{variant:\"body2\",children:\"Cavi Totali\"})]})}),/*#__PURE__*/_jsx(Grid,{item:true,xs:12,sm:4,children:/*#__PURE__*/_jsxs(Box,{sx:{textAlign:'center',p:1,border:'1px solid #e0e0e0',borderRadius:1},children:[/*#__PURE__*/_jsxs(Typography,{variant:\"h6\",color:\"info.main\",children:[totali.metri_teorici.toFixed(0),\"m\"]}),/*#__PURE__*/_jsx(Typography,{variant:\"body2\",children:\"Metri Teorici\"})]})}),/*#__PURE__*/_jsx(Grid,{item:true,xs:12,sm:4,children:/*#__PURE__*/_jsxs(Box,{sx:{textAlign:'center',p:1,border:'1px solid #e0e0e0',borderRadius:1},children:[/*#__PURE__*/_jsxs(Typography,{variant:\"h6\",color:\"success.main\",children:[totali.metri_reali.toFixed(0),\"m\"]}),/*#__PURE__*/_jsx(Typography,{variant:\"body2\",children:\"Metri Reali\"})]})})]})]})}),/*#__PURE__*/_jsx(Grid,{item:true,xs:12,md:6,children:/*#__PURE__*/_jsxs(Paper,{sx:{p:2,height:350},children:[/*#__PURE__*/_jsx(Typography,{variant:\"subtitle1\",gutterBottom:true,align:\"center\",children:\"Distribuzione Cavi per Stato\"}),/*#__PURE__*/_jsx(ResponsiveContainer,{width:\"100%\",height:280,children:/*#__PURE__*/_jsxs(PieChart,{children:[/*#__PURE__*/_jsx(Pie,{data:pieData,cx:\"50%\",cy:\"50%\",labelLine:false,label:renderCustomizedLabel,outerRadius:80,fill:\"#8884d8\",dataKey:\"value\",children:pieData.map((entry,index)=>/*#__PURE__*/_jsx(Cell,{fill:entry.color},`cell-${index}`))}),/*#__PURE__*/_jsx(Tooltip,{content:/*#__PURE__*/_jsx(CustomTooltip,{})}),/*#__PURE__*/_jsx(Legend,{})]})})]})}),/*#__PURE__*/_jsx(Grid,{item:true,xs:12,md:6,children:/*#__PURE__*/_jsxs(Paper,{sx:{p:2,height:350},children:[/*#__PURE__*/_jsx(Typography,{variant:\"subtitle1\",gutterBottom:true,align:\"center\",children:\"Numero Cavi per Stato\"}),/*#__PURE__*/_jsx(ResponsiveContainer,{width:\"100%\",height:280,children:/*#__PURE__*/_jsxs(BarChart,{data:statoData,margin:{top:20,right:30,left:20,bottom:5},children:[/*#__PURE__*/_jsx(CartesianGrid,{strokeDasharray:\"3 3\"}),/*#__PURE__*/_jsx(XAxis,{dataKey:\"stato_short\",angle:-45,textAnchor:\"end\",height:80}),/*#__PURE__*/_jsx(YAxis,{}),/*#__PURE__*/_jsx(Tooltip,{content:/*#__PURE__*/_jsx(CustomTooltip,{})}),/*#__PURE__*/_jsx(Legend,{}),/*#__PURE__*/_jsx(Bar,{dataKey:\"num_cavi\",fill:STATUS_COLORS.primary,name:\"Numero Cavi\"})]})})]})}),/*#__PURE__*/_jsx(Grid,{item:true,xs:12,children:/*#__PURE__*/_jsxs(Paper,{sx:{p:2,height:350},children:[/*#__PURE__*/_jsx(Typography,{variant:\"subtitle1\",gutterBottom:true,align:\"center\",children:\"Confronto Metri Teorici vs Reali per Stato\"}),/*#__PURE__*/_jsx(ResponsiveContainer,{width:\"100%\",height:280,children:/*#__PURE__*/_jsxs(BarChart,{data:metriData,margin:{top:20,right:30,left:20,bottom:5},children:[/*#__PURE__*/_jsx(CartesianGrid,{strokeDasharray:\"3 3\"}),/*#__PURE__*/_jsx(XAxis,{dataKey:\"stato\",angle:-45,textAnchor:\"end\",height:80}),/*#__PURE__*/_jsx(YAxis,{}),/*#__PURE__*/_jsx(Tooltip,{content:/*#__PURE__*/_jsx(CustomTooltip,{})}),/*#__PURE__*/_jsx(Legend,{}),/*#__PURE__*/_jsx(Bar,{dataKey:\"metri_teorici\",fill:STATUS_COLORS.info,name:\"Metri Teorici\"}),/*#__PURE__*/_jsx(Bar,{dataKey:\"metri_reali\",fill:STATUS_COLORS.success,name:\"Metri Reali\"})]})})]})}),/*#__PURE__*/_jsx(Grid,{item:true,xs:12,children:/*#__PURE__*/_jsxs(Paper,{sx:{p:2},children:[/*#__PURE__*/_jsx(Typography,{variant:\"subtitle1\",gutterBottom:true,children:\"Dettaglio per Stato\"}),/*#__PURE__*/_jsx(Grid,{container:true,spacing:2,children:statoData.map((stato,index)=>{var _stato$metri_teorici,_stato$metri_reali,_stato$efficienza;return/*#__PURE__*/_jsx(Grid,{item:true,xs:12,sm:6,md:4,lg:3,children:/*#__PURE__*/_jsxs(Box,{sx:{textAlign:'center',p:2,border:'1px solid #e0e0e0',borderRadius:1,borderLeft:`4px solid ${stato.color}`},children:[/*#__PURE__*/_jsx(Chip,{label:stato.stato,style:{backgroundColor:stato.color,color:'white'},sx:{mb:1}}),/*#__PURE__*/_jsx(Typography,{variant:\"h6\",children:stato.num_cavi}),/*#__PURE__*/_jsx(Typography,{variant:\"body2\",children:\"cavi\"}),/*#__PURE__*/_jsxs(Typography,{variant:\"body2\",children:[\"Teorici: \",/*#__PURE__*/_jsxs(\"strong\",{children:[(_stato$metri_teorici=stato.metri_teorici)===null||_stato$metri_teorici===void 0?void 0:_stato$metri_teorici.toFixed(0),\"m\"]})]}),/*#__PURE__*/_jsxs(Typography,{variant:\"body2\",children:[\"Reali: \",/*#__PURE__*/_jsxs(\"strong\",{children:[(_stato$metri_reali=stato.metri_reali)===null||_stato$metri_reali===void 0?void 0:_stato$metri_reali.toFixed(0),\"m\"]})]}),/*#__PURE__*/_jsxs(Typography,{variant:\"body2\",children:[\"Efficienza: \",/*#__PURE__*/_jsxs(\"strong\",{children:[(_stato$efficienza=stato.efficienza)===null||_stato$efficienza===void 0?void 0:_stato$efficienza.toFixed(1),\"%\"]})]}),stato.metri_reali>stato.metri_teorici&&/*#__PURE__*/_jsx(Chip,{label:`+${(stato.metri_reali-stato.metri_teorici).toFixed(0)}m`,color:\"warning\",size:\"small\",sx:{mt:1}}),stato.metri_reali<stato.metri_teorici&&/*#__PURE__*/_jsx(Chip,{label:`-${(stato.metri_teorici-stato.metri_reali).toFixed(0)}m`,color:\"error\",size:\"small\",sx:{mt:1}})]})},index);})})]})}),/*#__PURE__*/_jsx(Grid,{item:true,xs:12,children:/*#__PURE__*/_jsxs(Paper,{sx:{p:2},children:[/*#__PURE__*/_jsx(Typography,{variant:\"subtitle1\",gutterBottom:true,children:\"Analisi Efficienza Installazione\"}),/*#__PURE__*/_jsxs(Grid,{container:true,spacing:2,children:[/*#__PURE__*/_jsxs(Grid,{item:true,xs:12,md:6,children:[/*#__PURE__*/_jsx(Typography,{variant:\"body2\",gutterBottom:true,children:/*#__PURE__*/_jsx(\"strong\",{children:\"Stati con Surplus di Metri:\"})}),metriData.filter(stato=>stato.differenza>0).map((stato,index)=>/*#__PURE__*/_jsxs(Box,{sx:{display:'flex',justifyContent:'space-between',p:1,mb:1,border:'1px solid #e0e0e0',borderRadius:1,borderLeft:`4px solid ${STATUS_COLORS.success}`},children:[/*#__PURE__*/_jsx(Typography,{variant:\"body2\",children:stato.stato_full}),/*#__PURE__*/_jsx(Chip,{label:`+${stato.differenza.toFixed(0)}m`,color:\"success\",size:\"small\"})]},index))]}),/*#__PURE__*/_jsxs(Grid,{item:true,xs:12,md:6,children:[/*#__PURE__*/_jsx(Typography,{variant:\"body2\",gutterBottom:true,children:/*#__PURE__*/_jsx(\"strong\",{children:\"Stati con Deficit di Metri:\"})}),metriData.filter(stato=>stato.differenza<0).map((stato,index)=>/*#__PURE__*/_jsxs(Box,{sx:{display:'flex',justifyContent:'space-between',p:1,mb:1,border:'1px solid #e0e0e0',borderRadius:1,borderLeft:`4px solid ${STATUS_COLORS.error}`},children:[/*#__PURE__*/_jsx(Typography,{variant:\"body2\",children:stato.stato_full}),/*#__PURE__*/_jsx(Chip,{label:`${stato.differenza.toFixed(0)}m`,color:\"error\",size:\"small\"})]},index))]})]})]})})]})]});};export default CaviStatoChart;", "map": {"version": 3, "names": ["React", "<PERSON><PERSON><PERSON>", "Bar", "XAxis", "YA<PERSON>s", "Cartesian<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "Legend", "ResponsiveContainer", "<PERSON><PERSON><PERSON>", "Pie", "Cell", "ComposedChart", "Line", "Box", "Typography", "Grid", "Paper", "Chip", "jsx", "_jsx", "jsxs", "_jsxs", "COLORS", "STATUS_COLORS", "primary", "secondary", "success", "warning", "info", "error", "Cavi<PERSON>tato<PERSON>hart", "_ref", "data", "cavi_per_stato", "statoData", "map", "stato", "_stato$stato", "stato_short", "length", "substring", "color", "efficienza", "metri_te<PERSON>ci", "metri_reali", "totali", "reduce", "acc", "cavi", "num_cavi", "pieData", "name", "value", "metriData", "stato_full", "differenza", "CustomTooltip", "_ref2", "active", "payload", "label", "sx", "p", "border", "children", "variant", "entry", "index", "style", "toFixed", "renderCustomizedLabel", "_ref3", "cx", "cy", "midAngle", "innerRadius", "outerRadius", "percent", "RADIAN", "Math", "PI", "radius", "x", "cos", "y", "sin", "fill", "textAnchor", "dominantBaseline", "fontSize", "fontWeight", "mt", "gutterBottom", "container", "spacing", "item", "xs", "sm", "textAlign", "borderRadius", "md", "height", "align", "width", "labelLine", "dataKey", "content", "margin", "top", "right", "left", "bottom", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "angle", "_stato$metri_teorici", "_stato$metri_reali", "_stato$efficienza", "lg", "borderLeft", "backgroundColor", "mb", "size", "filter", "display", "justifyContent"], "sources": ["C:/Users/<USER>/PycharmProjects/CMS/webapp/frontend/src/components/charts/CaviStatoChart.js"], "sourcesContent": ["import React from 'react';\nimport {\n  <PERSON><PERSON><PERSON>,\n  <PERSON>,\n  <PERSON><PERSON><PERSON><PERSON>,\n  <PERSON><PERSON><PERSON><PERSON>,\n  CartesianGrid,\n  <PERSON><PERSON><PERSON>,\n  Legend,\n  ResponsiveContainer,\n  <PERSON><PERSON><PERSON>,\n  Pie,\n  Cell,\n  ComposedChart,\n  Line\n} from 'recharts';\nimport { Box, Typography, Grid, Paper, Chip } from '@mui/material';\n\nconst COLORS = {\n  'Installato': '#2e7d32',\n  'Da Installare': '#ed6c02',\n  'In Progresso': '#1976d2',\n  'Spare': '#9c27b0',\n  'Sospeso': '#d32f2f'\n};\n\nconst STATUS_COLORS = {\n  primary: '#1976d2',\n  secondary: '#dc004e',\n  success: '#2e7d32',\n  warning: '#ed6c02',\n  info: '#0288d1',\n  error: '#d32f2f'\n};\n\nconst CaviStatoChart = ({ data }) => {\n  if (!data || !data.cavi_per_stato) return null;\n\n  // Prepara dati per i grafici\n  const statoData = data.cavi_per_stato.map(stato => ({\n    ...stato,\n    stato_short: stato.stato?.length > 12 ? stato.stato.substring(0, 12) + '...' : stato.stato,\n    color: COLORS[stato.stato] || STATUS_COLORS.info,\n    efficienza: stato.metri_teorici > 0 ? (stato.metri_reali / stato.metri_teorici * 100) : 0\n  }));\n\n  // Calcola totali\n  const totali = statoData.reduce((acc, stato) => {\n    acc.cavi += stato.num_cavi || 0;\n    acc.metri_teorici += stato.metri_teorici || 0;\n    acc.metri_reali += stato.metri_reali || 0;\n    return acc;\n  }, { cavi: 0, metri_teorici: 0, metri_reali: 0 });\n\n  // Dati per grafico a torta\n  const pieData = statoData.map(stato => ({\n    name: stato.stato,\n    value: stato.num_cavi,\n    color: stato.color\n  }));\n\n  // Dati per confronto metri\n  const metriData = statoData.map(stato => ({\n    stato: stato.stato_short,\n    stato_full: stato.stato,\n    metri_teorici: stato.metri_teorici || 0,\n    metri_reali: stato.metri_reali || 0,\n    differenza: (stato.metri_reali || 0) - (stato.metri_teorici || 0)\n  }));\n\n  const CustomTooltip = ({ active, payload, label }) => {\n    if (active && payload && payload.length) {\n      return (\n        <Paper sx={{ p: 1, border: '1px solid #ccc' }}>\n          <Typography variant=\"body2\">{`${label}`}</Typography>\n          {payload.map((entry, index) => (\n            <Typography key={index} variant=\"body2\" style={{ color: entry.color }}>\n              {`${entry.name}: ${typeof entry.value === 'number' ? entry.value.toFixed(2) : entry.value}`}\n            </Typography>\n          ))}\n        </Paper>\n      );\n    }\n    return null;\n  };\n\n  const renderCustomizedLabel = ({ cx, cy, midAngle, innerRadius, outerRadius, percent }) => {\n    if (percent < 0.05) return null;\n    \n    const RADIAN = Math.PI / 180;\n    const radius = innerRadius + (outerRadius - innerRadius) * 0.5;\n    const x = cx + radius * Math.cos(-midAngle * RADIAN);\n    const y = cy + radius * Math.sin(-midAngle * RADIAN);\n\n    return (\n      <text \n        x={x} \n        y={y} \n        fill=\"white\" \n        textAnchor={x > cx ? 'start' : 'end'} \n        dominantBaseline=\"central\"\n        fontSize=\"12\"\n        fontWeight=\"bold\"\n      >\n        {`${(percent * 100).toFixed(0)}%`}\n      </text>\n    );\n  };\n\n  return (\n    <Box sx={{ mt: 3 }}>\n      <Typography variant=\"h6\" gutterBottom>\n        Analisi Cavi per Stato di Installazione\n      </Typography>\n      \n      <Grid container spacing={3}>\n        {/* Statistiche Generali */}\n        <Grid item xs={12}>\n          <Paper sx={{ p: 2 }}>\n            <Typography variant=\"subtitle1\" gutterBottom>\n              Riepilogo Generale\n            </Typography>\n            <Grid container spacing={2}>\n              <Grid item xs={12} sm={4}>\n                <Box sx={{ textAlign: 'center', p: 1, border: '1px solid #e0e0e0', borderRadius: 1 }}>\n                  <Typography variant=\"h6\" color=\"primary\">\n                    {totali.cavi}\n                  </Typography>\n                  <Typography variant=\"body2\">Cavi Totali</Typography>\n                </Box>\n              </Grid>\n              <Grid item xs={12} sm={4}>\n                <Box sx={{ textAlign: 'center', p: 1, border: '1px solid #e0e0e0', borderRadius: 1 }}>\n                  <Typography variant=\"h6\" color=\"info.main\">\n                    {totali.metri_teorici.toFixed(0)}m\n                  </Typography>\n                  <Typography variant=\"body2\">Metri Teorici</Typography>\n                </Box>\n              </Grid>\n              <Grid item xs={12} sm={4}>\n                <Box sx={{ textAlign: 'center', p: 1, border: '1px solid #e0e0e0', borderRadius: 1 }}>\n                  <Typography variant=\"h6\" color=\"success.main\">\n                    {totali.metri_reali.toFixed(0)}m\n                  </Typography>\n                  <Typography variant=\"body2\">Metri Reali</Typography>\n                </Box>\n              </Grid>\n            </Grid>\n          </Paper>\n        </Grid>\n\n        {/* Grafico a torta - Distribuzione Cavi */}\n        <Grid item xs={12} md={6}>\n          <Paper sx={{ p: 2, height: 350 }}>\n            <Typography variant=\"subtitle1\" gutterBottom align=\"center\">\n              Distribuzione Cavi per Stato\n            </Typography>\n            <ResponsiveContainer width=\"100%\" height={280}>\n              <PieChart>\n                <Pie\n                  data={pieData}\n                  cx=\"50%\"\n                  cy=\"50%\"\n                  labelLine={false}\n                  label={renderCustomizedLabel}\n                  outerRadius={80}\n                  fill=\"#8884d8\"\n                  dataKey=\"value\"\n                >\n                  {pieData.map((entry, index) => (\n                    <Cell key={`cell-${index}`} fill={entry.color} />\n                  ))}\n                </Pie>\n                <Tooltip content={<CustomTooltip />} />\n                <Legend />\n              </PieChart>\n            </ResponsiveContainer>\n          </Paper>\n        </Grid>\n\n        {/* Grafico a barre - Numero Cavi per Stato */}\n        <Grid item xs={12} md={6}>\n          <Paper sx={{ p: 2, height: 350 }}>\n            <Typography variant=\"subtitle1\" gutterBottom align=\"center\">\n              Numero Cavi per Stato\n            </Typography>\n            <ResponsiveContainer width=\"100%\" height={280}>\n              <BarChart data={statoData} margin={{ top: 20, right: 30, left: 20, bottom: 5 }}>\n                <CartesianGrid strokeDasharray=\"3 3\" />\n                <XAxis dataKey=\"stato_short\" angle={-45} textAnchor=\"end\" height={80} />\n                <YAxis />\n                <Tooltip content={<CustomTooltip />} />\n                <Legend />\n                <Bar dataKey=\"num_cavi\" fill={STATUS_COLORS.primary} name=\"Numero Cavi\" />\n              </BarChart>\n            </ResponsiveContainer>\n          </Paper>\n        </Grid>\n\n        {/* Grafico confronto metri */}\n        <Grid item xs={12}>\n          <Paper sx={{ p: 2, height: 350 }}>\n            <Typography variant=\"subtitle1\" gutterBottom align=\"center\">\n              Confronto Metri Teorici vs Reali per Stato\n            </Typography>\n            <ResponsiveContainer width=\"100%\" height={280}>\n              <BarChart data={metriData} margin={{ top: 20, right: 30, left: 20, bottom: 5 }}>\n                <CartesianGrid strokeDasharray=\"3 3\" />\n                <XAxis dataKey=\"stato\" angle={-45} textAnchor=\"end\" height={80} />\n                <YAxis />\n                <Tooltip content={<CustomTooltip />} />\n                <Legend />\n                <Bar dataKey=\"metri_teorici\" fill={STATUS_COLORS.info} name=\"Metri Teorici\" />\n                <Bar dataKey=\"metri_reali\" fill={STATUS_COLORS.success} name=\"Metri Reali\" />\n              </BarChart>\n            </ResponsiveContainer>\n          </Paper>\n        </Grid>\n\n        {/* Dettaglio per Stato */}\n        <Grid item xs={12}>\n          <Paper sx={{ p: 2 }}>\n            <Typography variant=\"subtitle1\" gutterBottom>\n              Dettaglio per Stato\n            </Typography>\n            <Grid container spacing={2}>\n              {statoData.map((stato, index) => (\n                <Grid item xs={12} sm={6} md={4} lg={3} key={index}>\n                  <Box sx={{ \n                    textAlign: 'center', \n                    p: 2, \n                    border: '1px solid #e0e0e0', \n                    borderRadius: 1,\n                    borderLeft: `4px solid ${stato.color}`\n                  }}>\n                    <Chip \n                      label={stato.stato}\n                      style={{ backgroundColor: stato.color, color: 'white' }}\n                      sx={{ mb: 1 }}\n                    />\n                    <Typography variant=\"h6\">{stato.num_cavi}</Typography>\n                    <Typography variant=\"body2\">cavi</Typography>\n                    <Typography variant=\"body2\">\n                      Teorici: <strong>{stato.metri_teorici?.toFixed(0)}m</strong>\n                    </Typography>\n                    <Typography variant=\"body2\">\n                      Reali: <strong>{stato.metri_reali?.toFixed(0)}m</strong>\n                    </Typography>\n                    <Typography variant=\"body2\">\n                      Efficienza: <strong>{stato.efficienza?.toFixed(1)}%</strong>\n                    </Typography>\n                    {stato.metri_reali > stato.metri_teorici && (\n                      <Chip \n                        label={`+${(stato.metri_reali - stato.metri_teorici).toFixed(0)}m`}\n                        color=\"warning\"\n                        size=\"small\"\n                        sx={{ mt: 1 }}\n                      />\n                    )}\n                    {stato.metri_reali < stato.metri_teorici && (\n                      <Chip \n                        label={`-${(stato.metri_teorici - stato.metri_reali).toFixed(0)}m`}\n                        color=\"error\"\n                        size=\"small\"\n                        sx={{ mt: 1 }}\n                      />\n                    )}\n                  </Box>\n                </Grid>\n              ))}\n            </Grid>\n          </Paper>\n        </Grid>\n\n        {/* Analisi Efficienza */}\n        <Grid item xs={12}>\n          <Paper sx={{ p: 2 }}>\n            <Typography variant=\"subtitle1\" gutterBottom>\n              Analisi Efficienza Installazione\n            </Typography>\n            <Grid container spacing={2}>\n              <Grid item xs={12} md={6}>\n                <Typography variant=\"body2\" gutterBottom>\n                  <strong>Stati con Surplus di Metri:</strong>\n                </Typography>\n                {metriData\n                  .filter(stato => stato.differenza > 0)\n                  .map((stato, index) => (\n                    <Box key={index} sx={{ \n                      display: 'flex', \n                      justifyContent: 'space-between', \n                      p: 1, \n                      mb: 1,\n                      border: '1px solid #e0e0e0', \n                      borderRadius: 1,\n                      borderLeft: `4px solid ${STATUS_COLORS.success}`\n                    }}>\n                      <Typography variant=\"body2\">{stato.stato_full}</Typography>\n                      <Chip \n                        label={`+${stato.differenza.toFixed(0)}m`}\n                        color=\"success\"\n                        size=\"small\"\n                      />\n                    </Box>\n                  ))}\n              </Grid>\n              <Grid item xs={12} md={6}>\n                <Typography variant=\"body2\" gutterBottom>\n                  <strong>Stati con Deficit di Metri:</strong>\n                </Typography>\n                {metriData\n                  .filter(stato => stato.differenza < 0)\n                  .map((stato, index) => (\n                    <Box key={index} sx={{ \n                      display: 'flex', \n                      justifyContent: 'space-between', \n                      p: 1, \n                      mb: 1,\n                      border: '1px solid #e0e0e0', \n                      borderRadius: 1,\n                      borderLeft: `4px solid ${STATUS_COLORS.error}`\n                    }}>\n                      <Typography variant=\"body2\">{stato.stato_full}</Typography>\n                      <Chip \n                        label={`${stato.differenza.toFixed(0)}m`}\n                        color=\"error\"\n                        size=\"small\"\n                      />\n                    </Box>\n                  ))}\n              </Grid>\n            </Grid>\n          </Paper>\n        </Grid>\n      </Grid>\n    </Box>\n  );\n};\n\nexport default CaviStatoChart;\n"], "mappings": "AAAA,MAAO,CAAAA,KAAK,KAAM,OAAO,CACzB,OACEC,QAAQ,CACRC,GAAG,CACHC,KAAK,CACLC,KAAK,CACLC,aAAa,CACbC,OAAO,CACPC,MAAM,CACNC,mBAAmB,CACnBC,QAAQ,CACRC,GAAG,CACHC,IAAI,CACJC,aAAa,CACbC,IAAI,KACC,UAAU,CACjB,OAASC,GAAG,CAAEC,UAAU,CAAEC,IAAI,CAAEC,KAAK,CAAEC,IAAI,KAAQ,eAAe,CAAC,OAAAC,GAAA,IAAAC,IAAA,CAAAC,IAAA,IAAAC,KAAA,yBAEnE,KAAM,CAAAC,MAAM,CAAG,CACb,YAAY,CAAE,SAAS,CACvB,eAAe,CAAE,SAAS,CAC1B,cAAc,CAAE,SAAS,CACzB,OAAO,CAAE,SAAS,CAClB,SAAS,CAAE,SACb,CAAC,CAED,KAAM,CAAAC,aAAa,CAAG,CACpBC,OAAO,CAAE,SAAS,CAClBC,SAAS,CAAE,SAAS,CACpBC,OAAO,CAAE,SAAS,CAClBC,OAAO,CAAE,SAAS,CAClBC,IAAI,CAAE,SAAS,CACfC,KAAK,CAAE,SACT,CAAC,CAED,KAAM,CAAAC,cAAc,CAAGC,IAAA,EAAc,IAAb,CAAEC,IAAK,CAAC,CAAAD,IAAA,CAC9B,GAAI,CAACC,IAAI,EAAI,CAACA,IAAI,CAACC,cAAc,CAAE,MAAO,KAAI,CAE9C;AACA,KAAM,CAAAC,SAAS,CAAGF,IAAI,CAACC,cAAc,CAACE,GAAG,CAACC,KAAK,OAAAC,YAAA,OAAK,CAClD,GAAGD,KAAK,CACRE,WAAW,CAAE,EAAAD,YAAA,CAAAD,KAAK,CAACA,KAAK,UAAAC,YAAA,iBAAXA,YAAA,CAAaE,MAAM,EAAG,EAAE,CAAGH,KAAK,CAACA,KAAK,CAACI,SAAS,CAAC,CAAC,CAAE,EAAE,CAAC,CAAG,KAAK,CAAGJ,KAAK,CAACA,KAAK,CAC1FK,KAAK,CAAEnB,MAAM,CAACc,KAAK,CAACA,KAAK,CAAC,EAAIb,aAAa,CAACK,IAAI,CAChDc,UAAU,CAAEN,KAAK,CAACO,aAAa,CAAG,CAAC,CAAIP,KAAK,CAACQ,WAAW,CAAGR,KAAK,CAACO,aAAa,CAAG,GAAG,CAAI,CAC1F,CAAC,EAAC,CAAC,CAEH;AACA,KAAM,CAAAE,MAAM,CAAGX,SAAS,CAACY,MAAM,CAAC,CAACC,GAAG,CAAEX,KAAK,GAAK,CAC9CW,GAAG,CAACC,IAAI,EAAIZ,KAAK,CAACa,QAAQ,EAAI,CAAC,CAC/BF,GAAG,CAACJ,aAAa,EAAIP,KAAK,CAACO,aAAa,EAAI,CAAC,CAC7CI,GAAG,CAACH,WAAW,EAAIR,KAAK,CAACQ,WAAW,EAAI,CAAC,CACzC,MAAO,CAAAG,GAAG,CACZ,CAAC,CAAE,CAAEC,IAAI,CAAE,CAAC,CAAEL,aAAa,CAAE,CAAC,CAAEC,WAAW,CAAE,CAAE,CAAC,CAAC,CAEjD;AACA,KAAM,CAAAM,OAAO,CAAGhB,SAAS,CAACC,GAAG,CAACC,KAAK,GAAK,CACtCe,IAAI,CAAEf,KAAK,CAACA,KAAK,CACjBgB,KAAK,CAAEhB,KAAK,CAACa,QAAQ,CACrBR,KAAK,CAAEL,KAAK,CAACK,KACf,CAAC,CAAC,CAAC,CAEH;AACA,KAAM,CAAAY,SAAS,CAAGnB,SAAS,CAACC,GAAG,CAACC,KAAK,GAAK,CACxCA,KAAK,CAAEA,KAAK,CAACE,WAAW,CACxBgB,UAAU,CAAElB,KAAK,CAACA,KAAK,CACvBO,aAAa,CAAEP,KAAK,CAACO,aAAa,EAAI,CAAC,CACvCC,WAAW,CAAER,KAAK,CAACQ,WAAW,EAAI,CAAC,CACnCW,UAAU,CAAE,CAACnB,KAAK,CAACQ,WAAW,EAAI,CAAC,GAAKR,KAAK,CAACO,aAAa,EAAI,CAAC,CAClE,CAAC,CAAC,CAAC,CAEH,KAAM,CAAAa,aAAa,CAAGC,KAAA,EAAgC,IAA/B,CAAEC,MAAM,CAAEC,OAAO,CAAEC,KAAM,CAAC,CAAAH,KAAA,CAC/C,GAAIC,MAAM,EAAIC,OAAO,EAAIA,OAAO,CAACpB,MAAM,CAAE,CACvC,mBACElB,KAAA,CAACL,KAAK,EAAC6C,EAAE,CAAE,CAAEC,CAAC,CAAE,CAAC,CAAEC,MAAM,CAAE,gBAAiB,CAAE,CAAAC,QAAA,eAC5C7C,IAAA,CAACL,UAAU,EAACmD,OAAO,CAAC,OAAO,CAAAD,QAAA,CAAE,GAAGJ,KAAK,EAAE,CAAa,CAAC,CACpDD,OAAO,CAACxB,GAAG,CAAC,CAAC+B,KAAK,CAAEC,KAAK,gBACxBhD,IAAA,CAACL,UAAU,EAAamD,OAAO,CAAC,OAAO,CAACG,KAAK,CAAE,CAAE3B,KAAK,CAAEyB,KAAK,CAACzB,KAAM,CAAE,CAAAuB,QAAA,CACnE,GAAGE,KAAK,CAACf,IAAI,KAAK,MAAO,CAAAe,KAAK,CAACd,KAAK,GAAK,QAAQ,CAAGc,KAAK,CAACd,KAAK,CAACiB,OAAO,CAAC,CAAC,CAAC,CAAGH,KAAK,CAACd,KAAK,EAAE,EAD5Ee,KAEL,CACb,CAAC,EACG,CAAC,CAEZ,CACA,MAAO,KAAI,CACb,CAAC,CAED,KAAM,CAAAG,qBAAqB,CAAGC,KAAA,EAA6D,IAA5D,CAAEC,EAAE,CAAEC,EAAE,CAAEC,QAAQ,CAAEC,WAAW,CAAEC,WAAW,CAAEC,OAAQ,CAAC,CAAAN,KAAA,CACpF,GAAIM,OAAO,CAAG,IAAI,CAAE,MAAO,KAAI,CAE/B,KAAM,CAAAC,MAAM,CAAGC,IAAI,CAACC,EAAE,CAAG,GAAG,CAC5B,KAAM,CAAAC,MAAM,CAAGN,WAAW,CAAG,CAACC,WAAW,CAAGD,WAAW,EAAI,GAAG,CAC9D,KAAM,CAAAO,CAAC,CAAGV,EAAE,CAAGS,MAAM,CAAGF,IAAI,CAACI,GAAG,CAAC,CAACT,QAAQ,CAAGI,MAAM,CAAC,CACpD,KAAM,CAAAM,CAAC,CAAGX,EAAE,CAAGQ,MAAM,CAAGF,IAAI,CAACM,GAAG,CAAC,CAACX,QAAQ,CAAGI,MAAM,CAAC,CAEpD,mBACE3D,IAAA,SACE+D,CAAC,CAAEA,CAAE,CACLE,CAAC,CAAEA,CAAE,CACLE,IAAI,CAAC,OAAO,CACZC,UAAU,CAAEL,CAAC,CAAGV,EAAE,CAAG,OAAO,CAAG,KAAM,CACrCgB,gBAAgB,CAAC,SAAS,CAC1BC,QAAQ,CAAC,IAAI,CACbC,UAAU,CAAC,MAAM,CAAA1B,QAAA,CAEhB,GAAG,CAACa,OAAO,CAAG,GAAG,EAAER,OAAO,CAAC,CAAC,CAAC,GAAG,CAC7B,CAAC,CAEX,CAAC,CAED,mBACEhD,KAAA,CAACR,GAAG,EAACgD,EAAE,CAAE,CAAE8B,EAAE,CAAE,CAAE,CAAE,CAAA3B,QAAA,eACjB7C,IAAA,CAACL,UAAU,EAACmD,OAAO,CAAC,IAAI,CAAC2B,YAAY,MAAA5B,QAAA,CAAC,yCAEtC,CAAY,CAAC,cAEb3C,KAAA,CAACN,IAAI,EAAC8E,SAAS,MAACC,OAAO,CAAE,CAAE,CAAA9B,QAAA,eAEzB7C,IAAA,CAACJ,IAAI,EAACgF,IAAI,MAACC,EAAE,CAAE,EAAG,CAAAhC,QAAA,cAChB3C,KAAA,CAACL,KAAK,EAAC6C,EAAE,CAAE,CAAEC,CAAC,CAAE,CAAE,CAAE,CAAAE,QAAA,eAClB7C,IAAA,CAACL,UAAU,EAACmD,OAAO,CAAC,WAAW,CAAC2B,YAAY,MAAA5B,QAAA,CAAC,oBAE7C,CAAY,CAAC,cACb3C,KAAA,CAACN,IAAI,EAAC8E,SAAS,MAACC,OAAO,CAAE,CAAE,CAAA9B,QAAA,eACzB7C,IAAA,CAACJ,IAAI,EAACgF,IAAI,MAACC,EAAE,CAAE,EAAG,CAACC,EAAE,CAAE,CAAE,CAAAjC,QAAA,cACvB3C,KAAA,CAACR,GAAG,EAACgD,EAAE,CAAE,CAAEqC,SAAS,CAAE,QAAQ,CAAEpC,CAAC,CAAE,CAAC,CAAEC,MAAM,CAAE,mBAAmB,CAAEoC,YAAY,CAAE,CAAE,CAAE,CAAAnC,QAAA,eACnF7C,IAAA,CAACL,UAAU,EAACmD,OAAO,CAAC,IAAI,CAACxB,KAAK,CAAC,SAAS,CAAAuB,QAAA,CACrCnB,MAAM,CAACG,IAAI,CACF,CAAC,cACb7B,IAAA,CAACL,UAAU,EAACmD,OAAO,CAAC,OAAO,CAAAD,QAAA,CAAC,aAAW,CAAY,CAAC,EACjD,CAAC,CACF,CAAC,cACP7C,IAAA,CAACJ,IAAI,EAACgF,IAAI,MAACC,EAAE,CAAE,EAAG,CAACC,EAAE,CAAE,CAAE,CAAAjC,QAAA,cACvB3C,KAAA,CAACR,GAAG,EAACgD,EAAE,CAAE,CAAEqC,SAAS,CAAE,QAAQ,CAAEpC,CAAC,CAAE,CAAC,CAAEC,MAAM,CAAE,mBAAmB,CAAEoC,YAAY,CAAE,CAAE,CAAE,CAAAnC,QAAA,eACnF3C,KAAA,CAACP,UAAU,EAACmD,OAAO,CAAC,IAAI,CAACxB,KAAK,CAAC,WAAW,CAAAuB,QAAA,EACvCnB,MAAM,CAACF,aAAa,CAAC0B,OAAO,CAAC,CAAC,CAAC,CAAC,GACnC,EAAY,CAAC,cACblD,IAAA,CAACL,UAAU,EAACmD,OAAO,CAAC,OAAO,CAAAD,QAAA,CAAC,eAAa,CAAY,CAAC,EACnD,CAAC,CACF,CAAC,cACP7C,IAAA,CAACJ,IAAI,EAACgF,IAAI,MAACC,EAAE,CAAE,EAAG,CAACC,EAAE,CAAE,CAAE,CAAAjC,QAAA,cACvB3C,KAAA,CAACR,GAAG,EAACgD,EAAE,CAAE,CAAEqC,SAAS,CAAE,QAAQ,CAAEpC,CAAC,CAAE,CAAC,CAAEC,MAAM,CAAE,mBAAmB,CAAEoC,YAAY,CAAE,CAAE,CAAE,CAAAnC,QAAA,eACnF3C,KAAA,CAACP,UAAU,EAACmD,OAAO,CAAC,IAAI,CAACxB,KAAK,CAAC,cAAc,CAAAuB,QAAA,EAC1CnB,MAAM,CAACD,WAAW,CAACyB,OAAO,CAAC,CAAC,CAAC,CAAC,GACjC,EAAY,CAAC,cACblD,IAAA,CAACL,UAAU,EAACmD,OAAO,CAAC,OAAO,CAAAD,QAAA,CAAC,aAAW,CAAY,CAAC,EACjD,CAAC,CACF,CAAC,EACH,CAAC,EACF,CAAC,CACJ,CAAC,cAGP7C,IAAA,CAACJ,IAAI,EAACgF,IAAI,MAACC,EAAE,CAAE,EAAG,CAACI,EAAE,CAAE,CAAE,CAAApC,QAAA,cACvB3C,KAAA,CAACL,KAAK,EAAC6C,EAAE,CAAE,CAAEC,CAAC,CAAE,CAAC,CAAEuC,MAAM,CAAE,GAAI,CAAE,CAAArC,QAAA,eAC/B7C,IAAA,CAACL,UAAU,EAACmD,OAAO,CAAC,WAAW,CAAC2B,YAAY,MAACU,KAAK,CAAC,QAAQ,CAAAtC,QAAA,CAAC,8BAE5D,CAAY,CAAC,cACb7C,IAAA,CAACZ,mBAAmB,EAACgG,KAAK,CAAC,MAAM,CAACF,MAAM,CAAE,GAAI,CAAArC,QAAA,cAC5C3C,KAAA,CAACb,QAAQ,EAAAwD,QAAA,eACP7C,IAAA,CAACV,GAAG,EACFuB,IAAI,CAAEkB,OAAQ,CACdsB,EAAE,CAAC,KAAK,CACRC,EAAE,CAAC,KAAK,CACR+B,SAAS,CAAE,KAAM,CACjB5C,KAAK,CAAEU,qBAAsB,CAC7BM,WAAW,CAAE,EAAG,CAChBU,IAAI,CAAC,SAAS,CACdmB,OAAO,CAAC,OAAO,CAAAzC,QAAA,CAEdd,OAAO,CAACf,GAAG,CAAC,CAAC+B,KAAK,CAAEC,KAAK,gBACxBhD,IAAA,CAACT,IAAI,EAAuB4E,IAAI,CAAEpB,KAAK,CAACzB,KAAM,EAAnC,QAAQ0B,KAAK,EAAwB,CACjD,CAAC,CACC,CAAC,cACNhD,IAAA,CAACd,OAAO,EAACqG,OAAO,cAAEvF,IAAA,CAACqC,aAAa,GAAE,CAAE,CAAE,CAAC,cACvCrC,IAAA,CAACb,MAAM,GAAE,CAAC,EACF,CAAC,CACQ,CAAC,EACjB,CAAC,CACJ,CAAC,cAGPa,IAAA,CAACJ,IAAI,EAACgF,IAAI,MAACC,EAAE,CAAE,EAAG,CAACI,EAAE,CAAE,CAAE,CAAApC,QAAA,cACvB3C,KAAA,CAACL,KAAK,EAAC6C,EAAE,CAAE,CAAEC,CAAC,CAAE,CAAC,CAAEuC,MAAM,CAAE,GAAI,CAAE,CAAArC,QAAA,eAC/B7C,IAAA,CAACL,UAAU,EAACmD,OAAO,CAAC,WAAW,CAAC2B,YAAY,MAACU,KAAK,CAAC,QAAQ,CAAAtC,QAAA,CAAC,uBAE5D,CAAY,CAAC,cACb7C,IAAA,CAACZ,mBAAmB,EAACgG,KAAK,CAAC,MAAM,CAACF,MAAM,CAAE,GAAI,CAAArC,QAAA,cAC5C3C,KAAA,CAACrB,QAAQ,EAACgC,IAAI,CAAEE,SAAU,CAACyE,MAAM,CAAE,CAAEC,GAAG,CAAE,EAAE,CAAEC,KAAK,CAAE,EAAE,CAAEC,IAAI,CAAE,EAAE,CAAEC,MAAM,CAAE,CAAE,CAAE,CAAA/C,QAAA,eAC7E7C,IAAA,CAACf,aAAa,EAAC4G,eAAe,CAAC,KAAK,CAAE,CAAC,cACvC7F,IAAA,CAACjB,KAAK,EAACuG,OAAO,CAAC,aAAa,CAACQ,KAAK,CAAE,CAAC,EAAG,CAAC1B,UAAU,CAAC,KAAK,CAACc,MAAM,CAAE,EAAG,CAAE,CAAC,cACxElF,IAAA,CAAChB,KAAK,GAAE,CAAC,cACTgB,IAAA,CAACd,OAAO,EAACqG,OAAO,cAAEvF,IAAA,CAACqC,aAAa,GAAE,CAAE,CAAE,CAAC,cACvCrC,IAAA,CAACb,MAAM,GAAE,CAAC,cACVa,IAAA,CAAClB,GAAG,EAACwG,OAAO,CAAC,UAAU,CAACnB,IAAI,CAAE/D,aAAa,CAACC,OAAQ,CAAC2B,IAAI,CAAC,aAAa,CAAE,CAAC,EAClE,CAAC,CACQ,CAAC,EACjB,CAAC,CACJ,CAAC,cAGPhC,IAAA,CAACJ,IAAI,EAACgF,IAAI,MAACC,EAAE,CAAE,EAAG,CAAAhC,QAAA,cAChB3C,KAAA,CAACL,KAAK,EAAC6C,EAAE,CAAE,CAAEC,CAAC,CAAE,CAAC,CAAEuC,MAAM,CAAE,GAAI,CAAE,CAAArC,QAAA,eAC/B7C,IAAA,CAACL,UAAU,EAACmD,OAAO,CAAC,WAAW,CAAC2B,YAAY,MAACU,KAAK,CAAC,QAAQ,CAAAtC,QAAA,CAAC,4CAE5D,CAAY,CAAC,cACb7C,IAAA,CAACZ,mBAAmB,EAACgG,KAAK,CAAC,MAAM,CAACF,MAAM,CAAE,GAAI,CAAArC,QAAA,cAC5C3C,KAAA,CAACrB,QAAQ,EAACgC,IAAI,CAAEqB,SAAU,CAACsD,MAAM,CAAE,CAAEC,GAAG,CAAE,EAAE,CAAEC,KAAK,CAAE,EAAE,CAAEC,IAAI,CAAE,EAAE,CAAEC,MAAM,CAAE,CAAE,CAAE,CAAA/C,QAAA,eAC7E7C,IAAA,CAACf,aAAa,EAAC4G,eAAe,CAAC,KAAK,CAAE,CAAC,cACvC7F,IAAA,CAACjB,KAAK,EAACuG,OAAO,CAAC,OAAO,CAACQ,KAAK,CAAE,CAAC,EAAG,CAAC1B,UAAU,CAAC,KAAK,CAACc,MAAM,CAAE,EAAG,CAAE,CAAC,cAClElF,IAAA,CAAChB,KAAK,GAAE,CAAC,cACTgB,IAAA,CAACd,OAAO,EAACqG,OAAO,cAAEvF,IAAA,CAACqC,aAAa,GAAE,CAAE,CAAE,CAAC,cACvCrC,IAAA,CAACb,MAAM,GAAE,CAAC,cACVa,IAAA,CAAClB,GAAG,EAACwG,OAAO,CAAC,eAAe,CAACnB,IAAI,CAAE/D,aAAa,CAACK,IAAK,CAACuB,IAAI,CAAC,eAAe,CAAE,CAAC,cAC9EhC,IAAA,CAAClB,GAAG,EAACwG,OAAO,CAAC,aAAa,CAACnB,IAAI,CAAE/D,aAAa,CAACG,OAAQ,CAACyB,IAAI,CAAC,aAAa,CAAE,CAAC,EACrE,CAAC,CACQ,CAAC,EACjB,CAAC,CACJ,CAAC,cAGPhC,IAAA,CAACJ,IAAI,EAACgF,IAAI,MAACC,EAAE,CAAE,EAAG,CAAAhC,QAAA,cAChB3C,KAAA,CAACL,KAAK,EAAC6C,EAAE,CAAE,CAAEC,CAAC,CAAE,CAAE,CAAE,CAAAE,QAAA,eAClB7C,IAAA,CAACL,UAAU,EAACmD,OAAO,CAAC,WAAW,CAAC2B,YAAY,MAAA5B,QAAA,CAAC,qBAE7C,CAAY,CAAC,cACb7C,IAAA,CAACJ,IAAI,EAAC8E,SAAS,MAACC,OAAO,CAAE,CAAE,CAAA9B,QAAA,CACxB9B,SAAS,CAACC,GAAG,CAAC,CAACC,KAAK,CAAE+B,KAAK,QAAA+C,oBAAA,CAAAC,kBAAA,CAAAC,iBAAA,oBAC1BjG,IAAA,CAACJ,IAAI,EAACgF,IAAI,MAACC,EAAE,CAAE,EAAG,CAACC,EAAE,CAAE,CAAE,CAACG,EAAE,CAAE,CAAE,CAACiB,EAAE,CAAE,CAAE,CAAArD,QAAA,cACrC3C,KAAA,CAACR,GAAG,EAACgD,EAAE,CAAE,CACPqC,SAAS,CAAE,QAAQ,CACnBpC,CAAC,CAAE,CAAC,CACJC,MAAM,CAAE,mBAAmB,CAC3BoC,YAAY,CAAE,CAAC,CACfmB,UAAU,CAAE,aAAalF,KAAK,CAACK,KAAK,EACtC,CAAE,CAAAuB,QAAA,eACA7C,IAAA,CAACF,IAAI,EACH2C,KAAK,CAAExB,KAAK,CAACA,KAAM,CACnBgC,KAAK,CAAE,CAAEmD,eAAe,CAAEnF,KAAK,CAACK,KAAK,CAAEA,KAAK,CAAE,OAAQ,CAAE,CACxDoB,EAAE,CAAE,CAAE2D,EAAE,CAAE,CAAE,CAAE,CACf,CAAC,cACFrG,IAAA,CAACL,UAAU,EAACmD,OAAO,CAAC,IAAI,CAAAD,QAAA,CAAE5B,KAAK,CAACa,QAAQ,CAAa,CAAC,cACtD9B,IAAA,CAACL,UAAU,EAACmD,OAAO,CAAC,OAAO,CAAAD,QAAA,CAAC,MAAI,CAAY,CAAC,cAC7C3C,KAAA,CAACP,UAAU,EAACmD,OAAO,CAAC,OAAO,CAAAD,QAAA,EAAC,WACjB,cAAA3C,KAAA,WAAA2C,QAAA,GAAAkD,oBAAA,CAAS9E,KAAK,CAACO,aAAa,UAAAuE,oBAAA,iBAAnBA,oBAAA,CAAqB7C,OAAO,CAAC,CAAC,CAAC,CAAC,GAAC,EAAQ,CAAC,EAClD,CAAC,cACbhD,KAAA,CAACP,UAAU,EAACmD,OAAO,CAAC,OAAO,CAAAD,QAAA,EAAC,SACnB,cAAA3C,KAAA,WAAA2C,QAAA,GAAAmD,kBAAA,CAAS/E,KAAK,CAACQ,WAAW,UAAAuE,kBAAA,iBAAjBA,kBAAA,CAAmB9C,OAAO,CAAC,CAAC,CAAC,CAAC,GAAC,EAAQ,CAAC,EAC9C,CAAC,cACbhD,KAAA,CAACP,UAAU,EAACmD,OAAO,CAAC,OAAO,CAAAD,QAAA,EAAC,cACd,cAAA3C,KAAA,WAAA2C,QAAA,GAAAoD,iBAAA,CAAShF,KAAK,CAACM,UAAU,UAAA0E,iBAAA,iBAAhBA,iBAAA,CAAkB/C,OAAO,CAAC,CAAC,CAAC,CAAC,GAAC,EAAQ,CAAC,EAClD,CAAC,CACZjC,KAAK,CAACQ,WAAW,CAAGR,KAAK,CAACO,aAAa,eACtCxB,IAAA,CAACF,IAAI,EACH2C,KAAK,CAAE,IAAI,CAACxB,KAAK,CAACQ,WAAW,CAAGR,KAAK,CAACO,aAAa,EAAE0B,OAAO,CAAC,CAAC,CAAC,GAAI,CACnE5B,KAAK,CAAC,SAAS,CACfgF,IAAI,CAAC,OAAO,CACZ5D,EAAE,CAAE,CAAE8B,EAAE,CAAE,CAAE,CAAE,CACf,CACF,CACAvD,KAAK,CAACQ,WAAW,CAAGR,KAAK,CAACO,aAAa,eACtCxB,IAAA,CAACF,IAAI,EACH2C,KAAK,CAAE,IAAI,CAACxB,KAAK,CAACO,aAAa,CAAGP,KAAK,CAACQ,WAAW,EAAEyB,OAAO,CAAC,CAAC,CAAC,GAAI,CACnE5B,KAAK,CAAC,OAAO,CACbgF,IAAI,CAAC,OAAO,CACZ5D,EAAE,CAAE,CAAE8B,EAAE,CAAE,CAAE,CAAE,CACf,CACF,EACE,CAAC,EAxCqCxB,KAyCvC,CAAC,EACR,CAAC,CACE,CAAC,EACF,CAAC,CACJ,CAAC,cAGPhD,IAAA,CAACJ,IAAI,EAACgF,IAAI,MAACC,EAAE,CAAE,EAAG,CAAAhC,QAAA,cAChB3C,KAAA,CAACL,KAAK,EAAC6C,EAAE,CAAE,CAAEC,CAAC,CAAE,CAAE,CAAE,CAAAE,QAAA,eAClB7C,IAAA,CAACL,UAAU,EAACmD,OAAO,CAAC,WAAW,CAAC2B,YAAY,MAAA5B,QAAA,CAAC,kCAE7C,CAAY,CAAC,cACb3C,KAAA,CAACN,IAAI,EAAC8E,SAAS,MAACC,OAAO,CAAE,CAAE,CAAA9B,QAAA,eACzB3C,KAAA,CAACN,IAAI,EAACgF,IAAI,MAACC,EAAE,CAAE,EAAG,CAACI,EAAE,CAAE,CAAE,CAAApC,QAAA,eACvB7C,IAAA,CAACL,UAAU,EAACmD,OAAO,CAAC,OAAO,CAAC2B,YAAY,MAAA5B,QAAA,cACtC7C,IAAA,WAAA6C,QAAA,CAAQ,6BAA2B,CAAQ,CAAC,CAClC,CAAC,CACZX,SAAS,CACPqE,MAAM,CAACtF,KAAK,EAAIA,KAAK,CAACmB,UAAU,CAAG,CAAC,CAAC,CACrCpB,GAAG,CAAC,CAACC,KAAK,CAAE+B,KAAK,gBAChB9C,KAAA,CAACR,GAAG,EAAagD,EAAE,CAAE,CACnB8D,OAAO,CAAE,MAAM,CACfC,cAAc,CAAE,eAAe,CAC/B9D,CAAC,CAAE,CAAC,CACJ0D,EAAE,CAAE,CAAC,CACLzD,MAAM,CAAE,mBAAmB,CAC3BoC,YAAY,CAAE,CAAC,CACfmB,UAAU,CAAE,aAAa/F,aAAa,CAACG,OAAO,EAChD,CAAE,CAAAsC,QAAA,eACA7C,IAAA,CAACL,UAAU,EAACmD,OAAO,CAAC,OAAO,CAAAD,QAAA,CAAE5B,KAAK,CAACkB,UAAU,CAAa,CAAC,cAC3DnC,IAAA,CAACF,IAAI,EACH2C,KAAK,CAAE,IAAIxB,KAAK,CAACmB,UAAU,CAACc,OAAO,CAAC,CAAC,CAAC,GAAI,CAC1C5B,KAAK,CAAC,SAAS,CACfgF,IAAI,CAAC,OAAO,CACb,CAAC,GAdMtD,KAeL,CACN,CAAC,EACA,CAAC,cACP9C,KAAA,CAACN,IAAI,EAACgF,IAAI,MAACC,EAAE,CAAE,EAAG,CAACI,EAAE,CAAE,CAAE,CAAApC,QAAA,eACvB7C,IAAA,CAACL,UAAU,EAACmD,OAAO,CAAC,OAAO,CAAC2B,YAAY,MAAA5B,QAAA,cACtC7C,IAAA,WAAA6C,QAAA,CAAQ,6BAA2B,CAAQ,CAAC,CAClC,CAAC,CACZX,SAAS,CACPqE,MAAM,CAACtF,KAAK,EAAIA,KAAK,CAACmB,UAAU,CAAG,CAAC,CAAC,CACrCpB,GAAG,CAAC,CAACC,KAAK,CAAE+B,KAAK,gBAChB9C,KAAA,CAACR,GAAG,EAAagD,EAAE,CAAE,CACnB8D,OAAO,CAAE,MAAM,CACfC,cAAc,CAAE,eAAe,CAC/B9D,CAAC,CAAE,CAAC,CACJ0D,EAAE,CAAE,CAAC,CACLzD,MAAM,CAAE,mBAAmB,CAC3BoC,YAAY,CAAE,CAAC,CACfmB,UAAU,CAAE,aAAa/F,aAAa,CAACM,KAAK,EAC9C,CAAE,CAAAmC,QAAA,eACA7C,IAAA,CAACL,UAAU,EAACmD,OAAO,CAAC,OAAO,CAAAD,QAAA,CAAE5B,KAAK,CAACkB,UAAU,CAAa,CAAC,cAC3DnC,IAAA,CAACF,IAAI,EACH2C,KAAK,CAAE,GAAGxB,KAAK,CAACmB,UAAU,CAACc,OAAO,CAAC,CAAC,CAAC,GAAI,CACzC5B,KAAK,CAAC,OAAO,CACbgF,IAAI,CAAC,OAAO,CACb,CAAC,GAdMtD,KAeL,CACN,CAAC,EACA,CAAC,EACH,CAAC,EACF,CAAC,CACJ,CAAC,EACH,CAAC,EACJ,CAAC,CAEV,CAAC,CAED,cAAe,CAAArC,cAAc", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}