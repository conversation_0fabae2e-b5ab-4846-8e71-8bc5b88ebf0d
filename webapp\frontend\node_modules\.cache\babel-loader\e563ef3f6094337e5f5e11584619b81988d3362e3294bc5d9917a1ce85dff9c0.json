{"ast": null, "code": "const formatRelativeLocale = {\n  lastWeek: \"'të' eeee 'e shkuar në' p\",\n  yesterday: \"'dje në' p\",\n  today: \"'sot në' p\",\n  tomorrow: \"'nesër në' p\",\n  nextWeek: \"eeee 'at' p\",\n  other: \"P\"\n};\nexport const formatRelative = (token, _date, _baseDate, _options) => formatRelativeLocale[token];", "map": {"version": 3, "names": ["formatRelativeLocale", "lastWeek", "yesterday", "today", "tomorrow", "nextWeek", "other", "formatRelative", "token", "_date", "_baseDate", "_options"], "sources": ["C:/CMS/webapp/frontend/node_modules/date-fns/locale/sq/_lib/formatRelative.js"], "sourcesContent": ["const formatRelativeLocale = {\n  lastWeek: \"'të' eeee 'e shkuar në' p\",\n  yesterday: \"'dje në' p\",\n  today: \"'sot në' p\",\n  tomorrow: \"'nesër në' p\",\n  nextWeek: \"eeee 'at' p\",\n  other: \"P\",\n};\n\nexport const formatRelative = (token, _date, _baseDate, _options) =>\n  formatRelativeLocale[token];\n"], "mappings": "AAAA,MAAMA,oBAAoB,GAAG;EAC3BC,QAAQ,EAAE,2BAA2B;EACrCC,SAAS,EAAE,YAAY;EACvBC,KAAK,EAAE,YAAY;EACnBC,QAAQ,EAAE,cAAc;EACxBC,QAAQ,EAAE,aAAa;EACvBC,KAAK,EAAE;AACT,CAAC;AAED,OAAO,MAAMC,cAAc,GAAGA,CAACC,KAAK,EAAEC,KAAK,EAAEC,SAAS,EAAEC,QAAQ,KAC9DX,oBAAoB,CAACQ,KAAK,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}