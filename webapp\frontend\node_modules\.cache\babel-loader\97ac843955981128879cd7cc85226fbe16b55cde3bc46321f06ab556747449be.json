{"ast": null, "code": "var _jsxFileName = \"C:\\\\CMS\\\\webapp\\\\frontend\\\\src\\\\components\\\\comande\\\\ComandeList.js\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect } from 'react';\nimport { Box, Card, CardContent, Typography, Button, Table, TableBody, TableCell, TableContainer, TableHead, TableRow, Paper, Chip, IconButton, Dialog, DialogTitle, DialogContent, DialogActions, TextField, MenuItem, Alert, CircularProgress, Tooltip, Grid, List, ListItem, ListItemText, Divider } from '@mui/material';\nimport { Add as AddIcon, Edit as EditIcon, Delete as DeleteIcon, Visibility as ViewIcon, Assignment as AssignIcon, Refresh as RefreshIcon, People as PeopleIcon } from '@mui/icons-material';\nimport comandeService from '../../services/comandeService';\nimport CreaComandaConCavi from './CreaComandaConCavi';\nimport { jsxDEV as _jsxDEV, Fragment as _Fragment } from \"react/jsx-dev-runtime\";\nconst ComandeList = ({\n  cantiereId,\n  cantiereName\n}) => {\n  _s();\n  const [comande, setComande] = useState([]);\n  const [loading, setLoading] = useState(true);\n  const [error, setError] = useState(null);\n  const [openDialog, setOpenDialog] = useState(false);\n  const [selectedComanda, setSelectedComanda] = useState(null);\n  const [dialogMode, setDialogMode] = useState('edit'); // 'edit', 'view', 'assign'\n  const [formData, setFormData] = useState({\n    tipo_comanda: 'POSA',\n    descrizione: '',\n    responsabile: '',\n    data_scadenza: '',\n    priorita: 'NORMALE',\n    note_capo_cantiere: ''\n  });\n  const [statistiche, setStatistiche] = useState(null);\n  const [caviAssegnazione, setCaviAssegnazione] = useState('');\n  const [openCreaConCavi, setOpenCreaConCavi] = useState(false);\n\n  // Carica le comande al mount del componente\n  useEffect(() => {\n    if (cantiereId) {\n      loadComande();\n      loadStatistiche();\n    }\n  }, [cantiereId]);\n  const loadComande = async () => {\n    try {\n      setLoading(true);\n      const response = await comandeService.getComande(cantiereId);\n      setComande(response.comande || []);\n      setError(null);\n    } catch (err) {\n      console.error('Errore nel caricamento delle comande:', err);\n      setError('Errore nel caricamento delle comande');\n    } finally {\n      setLoading(false);\n    }\n  };\n  const loadStatistiche = async () => {\n    try {\n      const stats = await comandeService.getStatisticheComande(cantiereId);\n      setStatistiche(stats);\n    } catch (err) {\n      console.error('Errore nel caricamento delle statistiche:', err);\n    }\n  };\n  const handleOpenDialog = (mode, comanda = null) => {\n    setDialogMode(mode);\n    setSelectedComanda(comanda);\n    if (mode === 'edit' && comanda) {\n      setFormData({\n        tipo_comanda: comanda.tipo_comanda,\n        descrizione: comanda.descrizione || '',\n        responsabile: comanda.responsabile || '',\n        data_scadenza: comanda.data_scadenza || '',\n        priorita: comanda.priorita || 'NORMALE',\n        note_capo_cantiere: comanda.note_capo_cantiere || ''\n      });\n    }\n    setOpenDialog(true);\n  };\n  const handleCloseDialog = () => {\n    setOpenDialog(false);\n    setSelectedComanda(null);\n    setCaviAssegnazione('');\n    setFormData({\n      tipo_comanda: 'POSA',\n      descrizione: '',\n      responsabile: '',\n      data_scadenza: '',\n      priorita: 'NORMALE',\n      note_capo_cantiere: ''\n    });\n  };\n  const handleSubmit = async () => {\n    try {\n      if (dialogMode === 'edit') {\n        // Modifica comanda esistente\n        const response = await comandeService.updateComanda(selectedComanda.codice_comanda, formData);\n        console.log('Comanda aggiornata:', response);\n      } else if (dialogMode === 'assign') {\n        // Assegnazione cavi\n        if (!caviAssegnazione.trim()) {\n          setError('Inserisci almeno un ID cavo');\n          return;\n        }\n        const listaIdCavi = caviAssegnazione.split(',').map(id => id.trim()).filter(id => id);\n        await comandeService.assegnaCavi(selectedComanda.codice_comanda, listaIdCavi);\n      }\n      handleCloseDialog();\n      loadComande();\n      loadStatistiche();\n    } catch (err) {\n      console.error('Errore nel salvataggio:', err);\n      setError('Errore nel salvataggio della comanda');\n    }\n  };\n  const handleDelete = async codiceComanda => {\n    if (window.confirm('Sei sicuro di voler eliminare questa comanda?')) {\n      try {\n        await comandeService.deleteComanda(codiceComanda);\n        loadComande();\n        loadStatistiche();\n      } catch (err) {\n        console.error('Errore nell\\'eliminazione:', err);\n        setError('Errore nell\\'eliminazione della comanda');\n      }\n    }\n  };\n  const getStatoColor = stato => {\n    switch (stato) {\n      case 'CREATA':\n        return 'default';\n      case 'ASSEGNATA':\n        return 'primary';\n      case 'IN_CORSO':\n        return 'warning';\n      case 'COMPLETATA':\n        return 'success';\n      case 'ANNULLATA':\n        return 'error';\n      default:\n        return 'default';\n    }\n  };\n  const getTipoComandaLabel = tipo => {\n    switch (tipo) {\n      case 'POSA':\n        return 'Posa';\n      case 'COLLEGAMENTO_PARTENZA':\n        return 'Collegamento Partenza';\n      case 'COLLEGAMENTO_ARRIVO':\n        return 'Collegamento Arrivo';\n      case 'CERTIFICAZIONE':\n        return 'Certificazione';\n      case 'TESTING':\n        return 'Testing/Certificazione';\n      default:\n        return tipo;\n    }\n  };\n  const getPrioritaColor = priorita => {\n    switch (priorita) {\n      case 'BASSA':\n        return 'default';\n      case 'NORMALE':\n        return 'primary';\n      case 'ALTA':\n        return 'warning';\n      case 'URGENTE':\n        return 'error';\n      default:\n        return 'default';\n    }\n  };\n  if (loading) {\n    return /*#__PURE__*/_jsxDEV(Box, {\n      display: \"flex\",\n      justifyContent: \"center\",\n      alignItems: \"center\",\n      minHeight: \"400px\",\n      children: /*#__PURE__*/_jsxDEV(CircularProgress, {}, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 206,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 205,\n      columnNumber: 7\n    }, this);\n  }\n  return /*#__PURE__*/_jsxDEV(Box, {\n    children: [/*#__PURE__*/_jsxDEV(Box, {\n      mb: 3,\n      children: statistiche && /*#__PURE__*/_jsxDEV(Grid, {\n        container: true,\n        spacing: 2,\n        mb: 2,\n        children: [/*#__PURE__*/_jsxDEV(Grid, {\n          item: true,\n          xs: 12,\n          sm: 6,\n          md: 2,\n          children: /*#__PURE__*/_jsxDEV(Card, {\n            children: /*#__PURE__*/_jsxDEV(CardContent, {\n              children: [/*#__PURE__*/_jsxDEV(Typography, {\n                color: \"textSecondary\",\n                gutterBottom: true,\n                children: \"Totale\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 221,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                variant: \"h5\",\n                children: statistiche.totale_comande\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 224,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 220,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 219,\n            columnNumber: 15\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 218,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(Grid, {\n          item: true,\n          xs: 12,\n          sm: 6,\n          md: 2,\n          children: /*#__PURE__*/_jsxDEV(Card, {\n            children: /*#__PURE__*/_jsxDEV(CardContent, {\n              children: [/*#__PURE__*/_jsxDEV(Typography, {\n                color: \"textSecondary\",\n                gutterBottom: true,\n                children: \"Create\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 233,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                variant: \"h5\",\n                children: statistiche.comande_create\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 236,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 232,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 231,\n            columnNumber: 15\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 230,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(Grid, {\n          item: true,\n          xs: 12,\n          sm: 6,\n          md: 2,\n          children: /*#__PURE__*/_jsxDEV(Card, {\n            children: /*#__PURE__*/_jsxDEV(CardContent, {\n              children: [/*#__PURE__*/_jsxDEV(Typography, {\n                color: \"textSecondary\",\n                gutterBottom: true,\n                children: \"Assegnate\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 245,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                variant: \"h5\",\n                children: statistiche.comande_assegnate\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 248,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 244,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 243,\n            columnNumber: 15\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 242,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(Grid, {\n          item: true,\n          xs: 12,\n          sm: 6,\n          md: 2,\n          children: /*#__PURE__*/_jsxDEV(Card, {\n            children: /*#__PURE__*/_jsxDEV(CardContent, {\n              children: [/*#__PURE__*/_jsxDEV(Typography, {\n                color: \"textSecondary\",\n                gutterBottom: true,\n                children: \"In Corso\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 257,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                variant: \"h5\",\n                children: statistiche.comande_in_corso\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 260,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 256,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 255,\n            columnNumber: 15\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 254,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(Grid, {\n          item: true,\n          xs: 12,\n          sm: 6,\n          md: 2,\n          children: /*#__PURE__*/_jsxDEV(Card, {\n            children: /*#__PURE__*/_jsxDEV(CardContent, {\n              children: [/*#__PURE__*/_jsxDEV(Typography, {\n                color: \"textSecondary\",\n                gutterBottom: true,\n                children: \"Completate\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 269,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                variant: \"h5\",\n                children: statistiche.comande_completate\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 272,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 268,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 267,\n            columnNumber: 15\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 266,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(Grid, {\n          item: true,\n          xs: 12,\n          sm: 6,\n          md: 2,\n          children: /*#__PURE__*/_jsxDEV(Card, {\n            children: /*#__PURE__*/_jsxDEV(CardContent, {\n              children: [/*#__PURE__*/_jsxDEV(Typography, {\n                color: \"textSecondary\",\n                gutterBottom: true,\n                children: \"% Completamento\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 281,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                variant: \"h5\",\n                children: [statistiche.percentuale_completamento_medio.toFixed(1), \"%\"]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 284,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 280,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 279,\n            columnNumber: 15\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 278,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 217,\n        columnNumber: 11\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 214,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Box, {\n      display: \"flex\",\n      justifyContent: \"space-between\",\n      alignItems: \"center\",\n      mb: 2,\n      flexWrap: \"wrap\",\n      gap: 1,\n      children: [/*#__PURE__*/_jsxDEV(Box, {\n        display: \"flex\",\n        gap: 1,\n        flexWrap: \"wrap\",\n        children: [/*#__PURE__*/_jsxDEV(Button, {\n          variant: \"contained\",\n          startIcon: /*#__PURE__*/_jsxDEV(AddIcon, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 299,\n            columnNumber: 24\n          }, this),\n          onClick: () => setOpenCreaConCavi(true),\n          color: \"primary\",\n          children: \"Nuova Comanda\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 297,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Button, {\n          variant: \"outlined\",\n          startIcon: /*#__PURE__*/_jsxDEV(AssignIcon, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 310,\n            columnNumber: 24\n          }, this),\n          onClick: () => {\n            if (comande.length === 0) {\n              setError('Nessuna comanda disponibile per l\\'assegnazione');\n              return;\n            }\n            // Apri dialog per selezionare comanda\n            setError('Seleziona una comanda dalla tabella e clicca sull\\'icona \"Assegna Cavi\"');\n          },\n          disabled: comande.length === 0,\n          children: \"Assegna Cavi\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 308,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 296,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Button, {\n        variant: \"outlined\",\n        startIcon: /*#__PURE__*/_jsxDEV(RefreshIcon, {}, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 327,\n          columnNumber: 22\n        }, this),\n        onClick: () => {\n          loadComande();\n          loadStatistiche();\n        },\n        children: \"Aggiorna\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 325,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 295,\n      columnNumber: 7\n    }, this), error && /*#__PURE__*/_jsxDEV(Alert, {\n      severity: \"error\",\n      sx: {\n        mb: 2\n      },\n      children: error\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 339,\n      columnNumber: 9\n    }, this), /*#__PURE__*/_jsxDEV(TableContainer, {\n      component: Paper,\n      children: /*#__PURE__*/_jsxDEV(Table, {\n        children: [/*#__PURE__*/_jsxDEV(TableHead, {\n          children: /*#__PURE__*/_jsxDEV(TableRow, {\n            children: [/*#__PURE__*/_jsxDEV(TableCell, {\n              children: \"Codice\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 349,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n              children: \"Tipo\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 350,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n              children: \"Priorit\\xE0\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 351,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n              children: \"Responsabile\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 352,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n              children: \"Data Creazione\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 353,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n              children: \"Stato\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 354,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n              children: \"Cavi Assegnati\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 355,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n              children: \"Completamento\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 356,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n              children: \"Azioni\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 357,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 348,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 347,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(TableBody, {\n          children: comande.map(comanda => /*#__PURE__*/_jsxDEV(TableRow, {\n            children: [/*#__PURE__*/_jsxDEV(TableCell, {\n              children: /*#__PURE__*/_jsxDEV(Typography, {\n                variant: \"body2\",\n                fontWeight: \"bold\",\n                children: comanda.codice_comanda\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 364,\n                columnNumber: 19\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 363,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n              children: /*#__PURE__*/_jsxDEV(Chip, {\n                label: getTipoComandaLabel(comanda.tipo_comanda),\n                size: \"small\",\n                variant: \"outlined\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 369,\n                columnNumber: 19\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 368,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n              children: /*#__PURE__*/_jsxDEV(Chip, {\n                label: comanda.priorita || 'NORMALE',\n                size: \"small\",\n                color: getPrioritaColor(comanda.priorita || 'NORMALE')\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 376,\n                columnNumber: 19\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 375,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n              children: comanda.responsabile\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 382,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n              children: new Date(comanda.data_creazione).toLocaleDateString('it-IT')\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 383,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n              children: /*#__PURE__*/_jsxDEV(Chip, {\n                label: comanda.stato,\n                color: getStatoColor(comanda.stato),\n                size: \"small\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 387,\n                columnNumber: 19\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 386,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n              children: comanda.numero_cavi_assegnati || 0\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 393,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n              children: comanda.percentuale_completamento ? `${comanda.percentuale_completamento.toFixed(1)}%` : '0%'\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 394,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n              children: [/*#__PURE__*/_jsxDEV(Tooltip, {\n                title: \"Visualizza\",\n                children: /*#__PURE__*/_jsxDEV(IconButton, {\n                  size: \"small\",\n                  onClick: () => handleOpenDialog('view', comanda),\n                  children: /*#__PURE__*/_jsxDEV(ViewIcon, {}, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 404,\n                    columnNumber: 23\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 400,\n                  columnNumber: 21\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 399,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(Tooltip, {\n                title: \"Modifica\",\n                children: /*#__PURE__*/_jsxDEV(IconButton, {\n                  size: \"small\",\n                  onClick: () => handleOpenDialog('edit', comanda),\n                  children: /*#__PURE__*/_jsxDEV(EditIcon, {}, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 412,\n                    columnNumber: 23\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 408,\n                  columnNumber: 21\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 407,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(Tooltip, {\n                title: \"Assegna Cavi\",\n                children: /*#__PURE__*/_jsxDEV(IconButton, {\n                  size: \"small\",\n                  onClick: () => handleOpenDialog('assign', comanda),\n                  color: \"primary\",\n                  children: /*#__PURE__*/_jsxDEV(AssignIcon, {}, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 421,\n                    columnNumber: 23\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 416,\n                  columnNumber: 21\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 415,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(Tooltip, {\n                title: \"Elimina\",\n                children: /*#__PURE__*/_jsxDEV(IconButton, {\n                  size: \"small\",\n                  onClick: () => handleDelete(comanda.codice_comanda),\n                  color: \"error\",\n                  children: /*#__PURE__*/_jsxDEV(DeleteIcon, {}, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 430,\n                    columnNumber: 23\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 425,\n                  columnNumber: 21\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 424,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 398,\n              columnNumber: 17\n            }, this)]\n          }, comanda.codice_comanda, true, {\n            fileName: _jsxFileName,\n            lineNumber: 362,\n            columnNumber: 15\n          }, this))\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 360,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 346,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 345,\n      columnNumber: 7\n    }, this), comande.length === 0 && !loading && /*#__PURE__*/_jsxDEV(Box, {\n      textAlign: \"center\",\n      py: 4,\n      children: [/*#__PURE__*/_jsxDEV(Typography, {\n        variant: \"h6\",\n        color: \"textSecondary\",\n        children: \"Nessuna comanda trovata\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 442,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(Typography, {\n        variant: \"body2\",\n        color: \"textSecondary\",\n        children: \"Clicca su \\\"Nuova Comanda\\\" per iniziare\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 445,\n        columnNumber: 11\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 441,\n      columnNumber: 9\n    }, this), /*#__PURE__*/_jsxDEV(Dialog, {\n      open: openDialog,\n      onClose: handleCloseDialog,\n      maxWidth: \"sm\",\n      fullWidth: true,\n      children: [/*#__PURE__*/_jsxDEV(DialogTitle, {\n        children: [dialogMode === 'edit' && 'Modifica Comanda', dialogMode === 'view' && 'Dettagli Comanda', dialogMode === 'assign' && `Assegna Cavi - ${selectedComanda === null || selectedComanda === void 0 ? void 0 : selectedComanda.codice_comanda}`]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 453,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(DialogContent, {\n        children: /*#__PURE__*/_jsxDEV(Box, {\n          sx: {\n            pt: 1\n          },\n          children: dialogMode === 'assign' ? /*#__PURE__*/_jsxDEV(_Fragment, {\n            children: [/*#__PURE__*/_jsxDEV(Alert, {\n              severity: \"info\",\n              sx: {\n                mb: 2\n              },\n              children: \"Inserisci gli ID dei cavi da assegnare alla comanda, separati da virgola.\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 462,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(TextField, {\n              fullWidth: true,\n              label: \"ID Cavi (separati da virgola)\",\n              value: caviAssegnazione,\n              onChange: e => setCaviAssegnazione(e.target.value),\n              margin: \"normal\",\n              placeholder: \"es: CAVO001, CAVO002, CAVO003\",\n              helperText: \"Esempio: CAVO001, CAVO002, CAVO003\",\n              multiline: true,\n              rows: 3\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 465,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true) : dialogMode === 'view' && selectedComanda ? /*#__PURE__*/_jsxDEV(_Fragment, {\n            children: /*#__PURE__*/_jsxDEV(List, {\n              children: [/*#__PURE__*/_jsxDEV(ListItem, {\n                children: /*#__PURE__*/_jsxDEV(ListItemText, {\n                  primary: \"Codice Comanda\",\n                  secondary: selectedComanda.codice_comanda\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 481,\n                  columnNumber: 21\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 480,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(Divider, {}, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 486,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(ListItem, {\n                children: /*#__PURE__*/_jsxDEV(ListItemText, {\n                  primary: \"Tipo\",\n                  secondary: getTipoComandaLabel(selectedComanda.tipo_comanda)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 488,\n                  columnNumber: 21\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 487,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(Divider, {}, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 493,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(ListItem, {\n                children: /*#__PURE__*/_jsxDEV(ListItemText, {\n                  primary: \"Stato\",\n                  secondary: /*#__PURE__*/_jsxDEV(Chip, {\n                    label: selectedComanda.stato,\n                    color: getStatoColor(selectedComanda.stato),\n                    size: \"small\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 498,\n                    columnNumber: 25\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 495,\n                  columnNumber: 21\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 494,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(Divider, {}, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 506,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(ListItem, {\n                children: /*#__PURE__*/_jsxDEV(ListItemText, {\n                  primary: \"Descrizione\",\n                  secondary: selectedComanda.descrizione || 'Nessuna descrizione'\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 508,\n                  columnNumber: 21\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 507,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(Divider, {}, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 513,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(ListItem, {\n                children: /*#__PURE__*/_jsxDEV(ListItemText, {\n                  primary: \"Priorit\\xE0\",\n                  secondary: /*#__PURE__*/_jsxDEV(Chip, {\n                    label: selectedComanda.priorita || 'NORMALE',\n                    color: getPrioritaColor(selectedComanda.priorita || 'NORMALE'),\n                    size: \"small\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 518,\n                    columnNumber: 25\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 515,\n                  columnNumber: 21\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 514,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(Divider, {}, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 526,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(ListItem, {\n                children: /*#__PURE__*/_jsxDEV(ListItemText, {\n                  primary: \"Responsabile\",\n                  secondary: selectedComanda.responsabile || 'Non assegnato'\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 528,\n                  columnNumber: 21\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 527,\n                columnNumber: 19\n              }, this), selectedComanda.note_capo_cantiere && /*#__PURE__*/_jsxDEV(_Fragment, {\n                children: [/*#__PURE__*/_jsxDEV(Divider, {}, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 535,\n                  columnNumber: 23\n                }, this), /*#__PURE__*/_jsxDEV(ListItem, {\n                  children: /*#__PURE__*/_jsxDEV(ListItemText, {\n                    primary: \"Note Capo Cantiere\",\n                    secondary: selectedComanda.note_capo_cantiere\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 537,\n                    columnNumber: 25\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 536,\n                  columnNumber: 23\n                }, this)]\n              }, void 0, true), /*#__PURE__*/_jsxDEV(Divider, {}, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 544,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(ListItem, {\n                children: /*#__PURE__*/_jsxDEV(ListItemText, {\n                  primary: \"Data Creazione\",\n                  secondary: new Date(selectedComanda.data_creazione).toLocaleDateString('it-IT')\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 546,\n                  columnNumber: 21\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 545,\n                columnNumber: 19\n              }, this), selectedComanda.data_scadenza && /*#__PURE__*/_jsxDEV(_Fragment, {\n                children: [/*#__PURE__*/_jsxDEV(Divider, {}, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 553,\n                  columnNumber: 23\n                }, this), /*#__PURE__*/_jsxDEV(ListItem, {\n                  children: /*#__PURE__*/_jsxDEV(ListItemText, {\n                    primary: \"Data Scadenza\",\n                    secondary: new Date(selectedComanda.data_scadenza).toLocaleDateString('it-IT')\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 555,\n                    columnNumber: 25\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 554,\n                  columnNumber: 23\n                }, this)]\n              }, void 0, true), /*#__PURE__*/_jsxDEV(Divider, {}, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 562,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(ListItem, {\n                children: /*#__PURE__*/_jsxDEV(ListItemText, {\n                  primary: \"Cavi Assegnati\",\n                  secondary: selectedComanda.numero_cavi_assegnati || 0\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 564,\n                  columnNumber: 21\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 563,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(Divider, {}, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 569,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(ListItem, {\n                children: /*#__PURE__*/_jsxDEV(ListItemText, {\n                  primary: \"Completamento\",\n                  secondary: `${(selectedComanda.percentuale_completamento || 0).toFixed(1)}%`\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 571,\n                  columnNumber: 21\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 570,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 479,\n              columnNumber: 17\n            }, this)\n          }, void 0, false) : /*#__PURE__*/_jsxDEV(_Fragment, {\n            children: [/*#__PURE__*/_jsxDEV(TextField, {\n              fullWidth: true,\n              select: true,\n              label: \"Tipo Comanda\",\n              value: formData.tipo_comanda,\n              onChange: e => setFormData({\n                ...formData,\n                tipo_comanda: e.target.value\n              }),\n              margin: \"normal\",\n              disabled: dialogMode === 'view',\n              children: [/*#__PURE__*/_jsxDEV(MenuItem, {\n                value: \"POSA\",\n                children: \"Posa\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 589,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(MenuItem, {\n                value: \"COLLEGAMENTO_PARTENZA\",\n                children: \"Collegamento Partenza\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 590,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(MenuItem, {\n                value: \"COLLEGAMENTO_ARRIVO\",\n                children: \"Collegamento Arrivo\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 591,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(MenuItem, {\n                value: \"CERTIFICAZIONE\",\n                children: \"Certificazione\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 592,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(MenuItem, {\n                value: \"TESTING\",\n                children: \"Testing\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 593,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 580,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(TextField, {\n              fullWidth: true,\n              select: true,\n              label: \"Priorit\\xE0\",\n              value: formData.priorita,\n              onChange: e => setFormData({\n                ...formData,\n                priorita: e.target.value\n              }),\n              margin: \"normal\",\n              disabled: dialogMode === 'view',\n              children: [/*#__PURE__*/_jsxDEV(MenuItem, {\n                value: \"BASSA\",\n                children: \"Bassa\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 605,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(MenuItem, {\n                value: \"NORMALE\",\n                children: \"Normale\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 606,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(MenuItem, {\n                value: \"ALTA\",\n                children: \"Alta\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 607,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(MenuItem, {\n                value: \"URGENTE\",\n                children: \"Urgente\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 608,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 596,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(TextField, {\n              fullWidth: true,\n              label: \"Descrizione\",\n              value: formData.descrizione,\n              onChange: e => setFormData({\n                ...formData,\n                descrizione: e.target.value\n              }),\n              margin: \"normal\",\n              multiline: true,\n              rows: 3,\n              disabled: dialogMode === 'view'\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 611,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(TextField, {\n              fullWidth: true,\n              label: \"Responsabile\",\n              value: formData.responsabile,\n              onChange: e => setFormData({\n                ...formData,\n                responsabile: e.target.value\n              }),\n              margin: \"normal\",\n              disabled: dialogMode === 'view',\n              required: true,\n              helperText: \"Chi eseguir\\xE0 il lavoro (obbligatorio)\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 622,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(TextField, {\n              fullWidth: true,\n              label: \"Note Capo Cantiere\",\n              value: formData.note_capo_cantiere,\n              onChange: e => setFormData({\n                ...formData,\n                note_capo_cantiere: e.target.value\n              }),\n              margin: \"normal\",\n              multiline: true,\n              rows: 2,\n              disabled: dialogMode === 'view',\n              helperText: \"Istruzioni specifiche per il responsabile\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 633,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(TextField, {\n              fullWidth: true,\n              label: \"Data Scadenza\",\n              type: \"date\",\n              value: formData.data_scadenza,\n              onChange: e => setFormData({\n                ...formData,\n                data_scadenza: e.target.value\n              }),\n              margin: \"normal\",\n              InputLabelProps: {\n                shrink: true\n              },\n              disabled: dialogMode === 'view'\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 645,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 459,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 458,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(DialogActions, {\n        children: [/*#__PURE__*/_jsxDEV(Button, {\n          onClick: handleCloseDialog,\n          children: dialogMode === 'view' ? 'Chiudi' : 'Annulla'\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 660,\n          columnNumber: 11\n        }, this), dialogMode !== 'view' && /*#__PURE__*/_jsxDEV(Button, {\n          onClick: handleSubmit,\n          variant: \"contained\",\n          children: dialogMode === 'edit' ? 'Salva' : dialogMode === 'assign' ? 'Assegna Cavi' : 'Salva'\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 664,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 659,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 452,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(CreaComandaConCavi, {\n      cantiereId: cantiereId,\n      open: openCreaConCavi,\n      onClose: () => setOpenCreaConCavi(false),\n      onSuccess: () => {\n        loadComande();\n        loadStatistiche();\n        setOpenCreaConCavi(false);\n      }\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 673,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 212,\n    columnNumber: 5\n  }, this);\n};\n_s(ComandeList, \"QPUjCI4cWD/etZ98b3eSWzRDnOg=\");\n_c = ComandeList;\nexport default ComandeList;\nvar _c;\n$RefreshReg$(_c, \"ComandeList\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "Box", "Card", "<PERSON><PERSON><PERSON><PERSON>", "Typography", "<PERSON><PERSON>", "Table", "TableBody", "TableCell", "TableContainer", "TableHead", "TableRow", "Paper", "Chip", "IconButton", "Dialog", "DialogTitle", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "DialogActions", "TextField", "MenuItem", "<PERSON><PERSON>", "CircularProgress", "<PERSON><PERSON><PERSON>", "Grid", "List", "ListItem", "ListItemText", "Divider", "Add", "AddIcon", "Edit", "EditIcon", "Delete", "DeleteIcon", "Visibility", "ViewIcon", "Assignment", "AssignIcon", "Refresh", "RefreshIcon", "People", "PeopleIcon", "comandeService", "CreaComandaConCavi", "jsxDEV", "_jsxDEV", "Fragment", "_Fragment", "ComandeList", "cantiereId", "cantiereName", "_s", "comande", "setComande", "loading", "setLoading", "error", "setError", "openDialog", "setOpenDialog", "selectedComanda", "setSelectedComanda", "dialogMode", "setDialogMode", "formData", "setFormData", "tipo_comanda", "descrizione", "responsabile", "data_scadenza", "priorita", "note_capo_cantiere", "statistiche", "setStatistiche", "caviAssegnazione", "setCaviAssegnazione", "openCreaConCavi", "setOpenCreaConCavi", "loadComande", "loadStatistiche", "response", "getComande", "err", "console", "stats", "getStatisticheComande", "handleOpenDialog", "mode", "comanda", "handleCloseDialog", "handleSubmit", "updateComanda", "codice_comanda", "log", "trim", "listaIdCavi", "split", "map", "id", "filter", "assegnaCavi", "handleDelete", "codiceComanda", "window", "confirm", "deleteComanda", "getStatoColor", "stato", "getTipoComandaLabel", "tipo", "getPrioritaColor", "display", "justifyContent", "alignItems", "minHeight", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "mb", "container", "spacing", "item", "xs", "sm", "md", "color", "gutterBottom", "variant", "totale_comande", "comande_create", "comande_assegnate", "comande_in_corso", "comande_completate", "percentuale_completamento_medio", "toFixed", "flexWrap", "gap", "startIcon", "onClick", "length", "disabled", "severity", "sx", "component", "fontWeight", "label", "size", "Date", "data_creazione", "toLocaleDateString", "numero_cavi_assegnati", "percentuale_completamento", "title", "textAlign", "py", "open", "onClose", "max<PERSON><PERSON><PERSON>", "fullWidth", "pt", "value", "onChange", "e", "target", "margin", "placeholder", "helperText", "multiline", "rows", "primary", "secondary", "select", "required", "type", "InputLabelProps", "shrink", "onSuccess", "_c", "$RefreshReg$"], "sources": ["C:/CMS/webapp/frontend/src/components/comande/ComandeList.js"], "sourcesContent": ["import React, { useState, useEffect } from 'react';\nimport {\n  <PERSON>,\n  Card,\n  CardContent,\n  Typo<PERSON>,\n  Button,\n  Table,\n  TableBody,\n  TableCell,\n  TableContainer,\n  TableHead,\n  TableRow,\n  Paper,\n  Chip,\n  IconButton,\n  Dialog,\n  DialogTitle,\n  DialogContent,\n  DialogActions,\n  TextField,\n  MenuItem,\n  Alert,\n  CircularProgress,\n  Tooltip,\n  Grid,\n  List,\n  ListItem,\n  ListItemText,\n  Divider\n} from '@mui/material';\nimport {\n  Add as AddIcon,\n  Edit as EditIcon,\n  Delete as DeleteIcon,\n  Visibility as ViewIcon,\n  Assignment as AssignIcon,\n  Refresh as RefreshIcon,\n  People as PeopleIcon\n} from '@mui/icons-material';\nimport comandeService from '../../services/comandeService';\nimport CreaComandaConCavi from './CreaComandaConCavi';\n\nconst ComandeList = ({ cantiereId, cantiereName }) => {\n  const [comande, setComande] = useState([]);\n  const [loading, setLoading] = useState(true);\n  const [error, setError] = useState(null);\n  const [openDialog, setOpenDialog] = useState(false);\n  const [selectedComanda, setSelectedComanda] = useState(null);\n  const [dialogMode, setDialogMode] = useState('edit'); // 'edit', 'view', 'assign'\n  const [formData, setFormData] = useState({\n    tipo_comanda: 'POSA',\n    descrizione: '',\n    responsabile: '',\n    data_scadenza: '',\n    priorita: 'NORMALE',\n    note_capo_cantiere: ''\n  });\n  const [statistiche, setStatistiche] = useState(null);\n  const [caviAssegnazione, setCaviAssegnazione] = useState('');\n  const [openCreaConCavi, setOpenCreaConCavi] = useState(false);\n\n\n\n  // Carica le comande al mount del componente\n  useEffect(() => {\n    if (cantiereId) {\n      loadComande();\n      loadStatistiche();\n    }\n  }, [cantiereId]);\n\n  const loadComande = async () => {\n    try {\n      setLoading(true);\n      const response = await comandeService.getComande(cantiereId);\n      setComande(response.comande || []);\n      setError(null);\n    } catch (err) {\n      console.error('Errore nel caricamento delle comande:', err);\n      setError('Errore nel caricamento delle comande');\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  const loadStatistiche = async () => {\n    try {\n      const stats = await comandeService.getStatisticheComande(cantiereId);\n      setStatistiche(stats);\n    } catch (err) {\n      console.error('Errore nel caricamento delle statistiche:', err);\n    }\n  };\n\n\n\n  const handleOpenDialog = (mode, comanda = null) => {\n    setDialogMode(mode);\n    setSelectedComanda(comanda);\n\n    if (mode === 'edit' && comanda) {\n      setFormData({\n        tipo_comanda: comanda.tipo_comanda,\n        descrizione: comanda.descrizione || '',\n        responsabile: comanda.responsabile || '',\n        data_scadenza: comanda.data_scadenza || '',\n        priorita: comanda.priorita || 'NORMALE',\n        note_capo_cantiere: comanda.note_capo_cantiere || ''\n      });\n    }\n\n    setOpenDialog(true);\n  };\n\n  const handleCloseDialog = () => {\n    setOpenDialog(false);\n    setSelectedComanda(null);\n    setCaviAssegnazione('');\n    setFormData({\n      tipo_comanda: 'POSA',\n      descrizione: '',\n      responsabile: '',\n      data_scadenza: '',\n      priorita: 'NORMALE',\n      note_capo_cantiere: ''\n    });\n  };\n\n  const handleSubmit = async () => {\n    try {\n      if (dialogMode === 'edit') {\n        // Modifica comanda esistente\n        const response = await comandeService.updateComanda(selectedComanda.codice_comanda, formData);\n        console.log('Comanda aggiornata:', response);\n      } else if (dialogMode === 'assign') {\n        // Assegnazione cavi\n        if (!caviAssegnazione.trim()) {\n          setError('Inserisci almeno un ID cavo');\n          return;\n        }\n\n        const listaIdCavi = caviAssegnazione.split(',').map(id => id.trim()).filter(id => id);\n        await comandeService.assegnaCavi(selectedComanda.codice_comanda, listaIdCavi);\n      }\n\n      handleCloseDialog();\n      loadComande();\n      loadStatistiche();\n    } catch (err) {\n      console.error('Errore nel salvataggio:', err);\n      setError('Errore nel salvataggio della comanda');\n    }\n  };\n\n  const handleDelete = async (codiceComanda) => {\n    if (window.confirm('Sei sicuro di voler eliminare questa comanda?')) {\n      try {\n        await comandeService.deleteComanda(codiceComanda);\n        loadComande();\n        loadStatistiche();\n      } catch (err) {\n        console.error('Errore nell\\'eliminazione:', err);\n        setError('Errore nell\\'eliminazione della comanda');\n      }\n    }\n  };\n\n\n\n  const getStatoColor = (stato) => {\n    switch (stato) {\n      case 'CREATA': return 'default';\n      case 'ASSEGNATA': return 'primary';\n      case 'IN_CORSO': return 'warning';\n      case 'COMPLETATA': return 'success';\n      case 'ANNULLATA': return 'error';\n      default: return 'default';\n    }\n  };\n\n  const getTipoComandaLabel = (tipo) => {\n    switch (tipo) {\n      case 'POSA': return 'Posa';\n      case 'COLLEGAMENTO_PARTENZA': return 'Collegamento Partenza';\n      case 'COLLEGAMENTO_ARRIVO': return 'Collegamento Arrivo';\n      case 'CERTIFICAZIONE': return 'Certificazione';\n      case 'TESTING': return 'Testing/Certificazione';\n      default: return tipo;\n    }\n  };\n\n  const getPrioritaColor = (priorita) => {\n    switch (priorita) {\n      case 'BASSA': return 'default';\n      case 'NORMALE': return 'primary';\n      case 'ALTA': return 'warning';\n      case 'URGENTE': return 'error';\n      default: return 'default';\n    }\n  };\n\n  if (loading) {\n    return (\n      <Box display=\"flex\" justifyContent=\"center\" alignItems=\"center\" minHeight=\"400px\">\n        <CircularProgress />\n      </Box>\n    );\n  }\n\n  return (\n    <Box>\n      {/* Header con statistiche */}\n      <Box mb={3}>\n        \n        {statistiche && (\n          <Grid container spacing={2} mb={2}>\n            <Grid item xs={12} sm={6} md={2}>\n              <Card>\n                <CardContent>\n                  <Typography color=\"textSecondary\" gutterBottom>\n                    Totale\n                  </Typography>\n                  <Typography variant=\"h5\">\n                    {statistiche.totale_comande}\n                  </Typography>\n                </CardContent>\n              </Card>\n            </Grid>\n            <Grid item xs={12} sm={6} md={2}>\n              <Card>\n                <CardContent>\n                  <Typography color=\"textSecondary\" gutterBottom>\n                    Create\n                  </Typography>\n                  <Typography variant=\"h5\">\n                    {statistiche.comande_create}\n                  </Typography>\n                </CardContent>\n              </Card>\n            </Grid>\n            <Grid item xs={12} sm={6} md={2}>\n              <Card>\n                <CardContent>\n                  <Typography color=\"textSecondary\" gutterBottom>\n                    Assegnate\n                  </Typography>\n                  <Typography variant=\"h5\">\n                    {statistiche.comande_assegnate}\n                  </Typography>\n                </CardContent>\n              </Card>\n            </Grid>\n            <Grid item xs={12} sm={6} md={2}>\n              <Card>\n                <CardContent>\n                  <Typography color=\"textSecondary\" gutterBottom>\n                    In Corso\n                  </Typography>\n                  <Typography variant=\"h5\">\n                    {statistiche.comande_in_corso}\n                  </Typography>\n                </CardContent>\n              </Card>\n            </Grid>\n            <Grid item xs={12} sm={6} md={2}>\n              <Card>\n                <CardContent>\n                  <Typography color=\"textSecondary\" gutterBottom>\n                    Completate\n                  </Typography>\n                  <Typography variant=\"h5\">\n                    {statistiche.comande_completate}\n                  </Typography>\n                </CardContent>\n              </Card>\n            </Grid>\n            <Grid item xs={12} sm={6} md={2}>\n              <Card>\n                <CardContent>\n                  <Typography color=\"textSecondary\" gutterBottom>\n                    % Completamento\n                  </Typography>\n                  <Typography variant=\"h5\">\n                    {statistiche.percentuale_completamento_medio.toFixed(1)}%\n                  </Typography>\n                </CardContent>\n              </Card>\n            </Grid>\n          </Grid>\n        )}\n      </Box>\n\n      {/* Toolbar */}\n      <Box display=\"flex\" justifyContent=\"space-between\" alignItems=\"center\" mb={2} flexWrap=\"wrap\" gap={1}>\n        <Box display=\"flex\" gap={1} flexWrap=\"wrap\">\n          <Button\n            variant=\"contained\"\n            startIcon={<AddIcon />}\n            onClick={() => setOpenCreaConCavi(true)}\n            color=\"primary\"\n          >\n            Nuova Comanda\n          </Button>\n\n\n\n          <Button\n            variant=\"outlined\"\n            startIcon={<AssignIcon />}\n            onClick={() => {\n              if (comande.length === 0) {\n                setError('Nessuna comanda disponibile per l\\'assegnazione');\n                return;\n              }\n              // Apri dialog per selezionare comanda\n              setError('Seleziona una comanda dalla tabella e clicca sull\\'icona \"Assegna Cavi\"');\n            }}\n            disabled={comande.length === 0}\n          >\n            Assegna Cavi\n          </Button>\n        </Box>\n\n        <Button\n          variant=\"outlined\"\n          startIcon={<RefreshIcon />}\n          onClick={() => {\n            loadComande();\n            loadStatistiche();\n          }}\n        >\n          Aggiorna\n        </Button>\n      </Box>\n\n      {/* Messaggio di errore */}\n      {error && (\n        <Alert severity=\"error\" sx={{ mb: 2 }}>\n          {error}\n        </Alert>\n      )}\n\n      {/* Tabella comande */}\n      <TableContainer component={Paper}>\n        <Table>\n          <TableHead>\n            <TableRow>\n              <TableCell>Codice</TableCell>\n              <TableCell>Tipo</TableCell>\n              <TableCell>Priorità</TableCell>\n              <TableCell>Responsabile</TableCell>\n              <TableCell>Data Creazione</TableCell>\n              <TableCell>Stato</TableCell>\n              <TableCell>Cavi Assegnati</TableCell>\n              <TableCell>Completamento</TableCell>\n              <TableCell>Azioni</TableCell>\n            </TableRow>\n          </TableHead>\n          <TableBody>\n            {comande.map((comanda) => (\n              <TableRow key={comanda.codice_comanda}>\n                <TableCell>\n                  <Typography variant=\"body2\" fontWeight=\"bold\">\n                    {comanda.codice_comanda}\n                  </Typography>\n                </TableCell>\n                <TableCell>\n                  <Chip\n                    label={getTipoComandaLabel(comanda.tipo_comanda)}\n                    size=\"small\"\n                    variant=\"outlined\"\n                  />\n                </TableCell>\n                <TableCell>\n                  <Chip\n                    label={comanda.priorita || 'NORMALE'}\n                    size=\"small\"\n                    color={getPrioritaColor(comanda.priorita || 'NORMALE')}\n                  />\n                </TableCell>\n                <TableCell>{comanda.responsabile}</TableCell>\n                <TableCell>\n                  {new Date(comanda.data_creazione).toLocaleDateString('it-IT')}\n                </TableCell>\n                <TableCell>\n                  <Chip \n                    label={comanda.stato}\n                    color={getStatoColor(comanda.stato)}\n                    size=\"small\"\n                  />\n                </TableCell>\n                <TableCell>{comanda.numero_cavi_assegnati || 0}</TableCell>\n                <TableCell>\n                  {comanda.percentuale_completamento ? \n                    `${comanda.percentuale_completamento.toFixed(1)}%` : '0%'}\n                </TableCell>\n                <TableCell>\n                  <Tooltip title=\"Visualizza\">\n                    <IconButton \n                      size=\"small\"\n                      onClick={() => handleOpenDialog('view', comanda)}\n                    >\n                      <ViewIcon />\n                    </IconButton>\n                  </Tooltip>\n                  <Tooltip title=\"Modifica\">\n                    <IconButton \n                      size=\"small\"\n                      onClick={() => handleOpenDialog('edit', comanda)}\n                    >\n                      <EditIcon />\n                    </IconButton>\n                  </Tooltip>\n                  <Tooltip title=\"Assegna Cavi\">\n                    <IconButton\n                      size=\"small\"\n                      onClick={() => handleOpenDialog('assign', comanda)}\n                      color=\"primary\"\n                    >\n                      <AssignIcon />\n                    </IconButton>\n                  </Tooltip>\n                  <Tooltip title=\"Elimina\">\n                    <IconButton\n                      size=\"small\"\n                      onClick={() => handleDelete(comanda.codice_comanda)}\n                      color=\"error\"\n                    >\n                      <DeleteIcon />\n                    </IconButton>\n                  </Tooltip>\n                </TableCell>\n              </TableRow>\n            ))}\n          </TableBody>\n        </Table>\n      </TableContainer>\n\n      {comande.length === 0 && !loading && (\n        <Box textAlign=\"center\" py={4}>\n          <Typography variant=\"h6\" color=\"textSecondary\">\n            Nessuna comanda trovata\n          </Typography>\n          <Typography variant=\"body2\" color=\"textSecondary\">\n            Clicca su \"Nuova Comanda\" per iniziare\n          </Typography>\n        </Box>\n      )}\n\n      {/* Dialog per modifica/assegnazione */}\n      <Dialog open={openDialog} onClose={handleCloseDialog} maxWidth=\"sm\" fullWidth>\n        <DialogTitle>\n          {dialogMode === 'edit' && 'Modifica Comanda'}\n          {dialogMode === 'view' && 'Dettagli Comanda'}\n          {dialogMode === 'assign' && `Assegna Cavi - ${selectedComanda?.codice_comanda}`}\n        </DialogTitle>\n        <DialogContent>\n          <Box sx={{ pt: 1 }}>\n            {dialogMode === 'assign' ? (\n              <>\n                <Alert severity=\"info\" sx={{ mb: 2 }}>\n                  Inserisci gli ID dei cavi da assegnare alla comanda, separati da virgola.\n                </Alert>\n                <TextField\n                  fullWidth\n                  label=\"ID Cavi (separati da virgola)\"\n                  value={caviAssegnazione}\n                  onChange={(e) => setCaviAssegnazione(e.target.value)}\n                  margin=\"normal\"\n                  placeholder=\"es: CAVO001, CAVO002, CAVO003\"\n                  helperText=\"Esempio: CAVO001, CAVO002, CAVO003\"\n                  multiline\n                  rows={3}\n                />\n              </>\n            ) : dialogMode === 'view' && selectedComanda ? (\n              <>\n                <List>\n                  <ListItem>\n                    <ListItemText\n                      primary=\"Codice Comanda\"\n                      secondary={selectedComanda.codice_comanda}\n                    />\n                  </ListItem>\n                  <Divider />\n                  <ListItem>\n                    <ListItemText\n                      primary=\"Tipo\"\n                      secondary={getTipoComandaLabel(selectedComanda.tipo_comanda)}\n                    />\n                  </ListItem>\n                  <Divider />\n                  <ListItem>\n                    <ListItemText\n                      primary=\"Stato\"\n                      secondary={\n                        <Chip\n                          label={selectedComanda.stato}\n                          color={getStatoColor(selectedComanda.stato)}\n                          size=\"small\"\n                        />\n                      }\n                    />\n                  </ListItem>\n                  <Divider />\n                  <ListItem>\n                    <ListItemText\n                      primary=\"Descrizione\"\n                      secondary={selectedComanda.descrizione || 'Nessuna descrizione'}\n                    />\n                  </ListItem>\n                  <Divider />\n                  <ListItem>\n                    <ListItemText\n                      primary=\"Priorità\"\n                      secondary={\n                        <Chip\n                          label={selectedComanda.priorita || 'NORMALE'}\n                          color={getPrioritaColor(selectedComanda.priorita || 'NORMALE')}\n                          size=\"small\"\n                        />\n                      }\n                    />\n                  </ListItem>\n                  <Divider />\n                  <ListItem>\n                    <ListItemText\n                      primary=\"Responsabile\"\n                      secondary={selectedComanda.responsabile || 'Non assegnato'}\n                    />\n                  </ListItem>\n                  {selectedComanda.note_capo_cantiere && (\n                    <>\n                      <Divider />\n                      <ListItem>\n                        <ListItemText\n                          primary=\"Note Capo Cantiere\"\n                          secondary={selectedComanda.note_capo_cantiere}\n                        />\n                      </ListItem>\n                    </>\n                  )}\n                  <Divider />\n                  <ListItem>\n                    <ListItemText\n                      primary=\"Data Creazione\"\n                      secondary={new Date(selectedComanda.data_creazione).toLocaleDateString('it-IT')}\n                    />\n                  </ListItem>\n                  {selectedComanda.data_scadenza && (\n                    <>\n                      <Divider />\n                      <ListItem>\n                        <ListItemText\n                          primary=\"Data Scadenza\"\n                          secondary={new Date(selectedComanda.data_scadenza).toLocaleDateString('it-IT')}\n                        />\n                      </ListItem>\n                    </>\n                  )}\n                  <Divider />\n                  <ListItem>\n                    <ListItemText\n                      primary=\"Cavi Assegnati\"\n                      secondary={selectedComanda.numero_cavi_assegnati || 0}\n                    />\n                  </ListItem>\n                  <Divider />\n                  <ListItem>\n                    <ListItemText\n                      primary=\"Completamento\"\n                      secondary={`${(selectedComanda.percentuale_completamento || 0).toFixed(1)}%`}\n                    />\n                  </ListItem>\n                </List>\n              </>\n            ) : (\n              <>\n                <TextField\n                  fullWidth\n                  select\n                  label=\"Tipo Comanda\"\n                  value={formData.tipo_comanda}\n                  onChange={(e) => setFormData({ ...formData, tipo_comanda: e.target.value })}\n                  margin=\"normal\"\n                  disabled={dialogMode === 'view'}\n                >\n                  <MenuItem value=\"POSA\">Posa</MenuItem>\n                  <MenuItem value=\"COLLEGAMENTO_PARTENZA\">Collegamento Partenza</MenuItem>\n                  <MenuItem value=\"COLLEGAMENTO_ARRIVO\">Collegamento Arrivo</MenuItem>\n                  <MenuItem value=\"CERTIFICAZIONE\">Certificazione</MenuItem>\n                  <MenuItem value=\"TESTING\">Testing</MenuItem>\n                </TextField>\n\n                <TextField\n                  fullWidth\n                  select\n                  label=\"Priorità\"\n                  value={formData.priorita}\n                  onChange={(e) => setFormData({ ...formData, priorita: e.target.value })}\n                  margin=\"normal\"\n                  disabled={dialogMode === 'view'}\n                >\n                  <MenuItem value=\"BASSA\">Bassa</MenuItem>\n                  <MenuItem value=\"NORMALE\">Normale</MenuItem>\n                  <MenuItem value=\"ALTA\">Alta</MenuItem>\n                  <MenuItem value=\"URGENTE\">Urgente</MenuItem>\n                </TextField>\n\n                <TextField\n                  fullWidth\n                  label=\"Descrizione\"\n                  value={formData.descrizione}\n                  onChange={(e) => setFormData({ ...formData, descrizione: e.target.value })}\n                  margin=\"normal\"\n                  multiline\n                  rows={3}\n                  disabled={dialogMode === 'view'}\n                />\n\n                <TextField\n                  fullWidth\n                  label=\"Responsabile\"\n                  value={formData.responsabile}\n                  onChange={(e) => setFormData({ ...formData, responsabile: e.target.value })}\n                  margin=\"normal\"\n                  disabled={dialogMode === 'view'}\n                  required\n                  helperText=\"Chi eseguirà il lavoro (obbligatorio)\"\n                />\n\n                <TextField\n                  fullWidth\n                  label=\"Note Capo Cantiere\"\n                  value={formData.note_capo_cantiere}\n                  onChange={(e) => setFormData({ ...formData, note_capo_cantiere: e.target.value })}\n                  margin=\"normal\"\n                  multiline\n                  rows={2}\n                  disabled={dialogMode === 'view'}\n                  helperText=\"Istruzioni specifiche per il responsabile\"\n                />\n\n                <TextField\n                  fullWidth\n                  label=\"Data Scadenza\"\n                  type=\"date\"\n                  value={formData.data_scadenza}\n                  onChange={(e) => setFormData({ ...formData, data_scadenza: e.target.value })}\n                  margin=\"normal\"\n                  InputLabelProps={{ shrink: true }}\n                  disabled={dialogMode === 'view'}\n                />\n              </>\n            )}\n          </Box>\n        </DialogContent>\n        <DialogActions>\n          <Button onClick={handleCloseDialog}>\n            {dialogMode === 'view' ? 'Chiudi' : 'Annulla'}\n          </Button>\n          {dialogMode !== 'view' && (\n            <Button onClick={handleSubmit} variant=\"contained\">\n              {dialogMode === 'edit' ? 'Salva' :\n               dialogMode === 'assign' ? 'Assegna Cavi' : 'Salva'}\n            </Button>\n          )}\n        </DialogActions>\n      </Dialog>\n\n      {/* Dialog per creazione comanda con cavi */}\n      <CreaComandaConCavi\n        cantiereId={cantiereId}\n        open={openCreaConCavi}\n        onClose={() => setOpenCreaConCavi(false)}\n        onSuccess={() => {\n          loadComande();\n          loadStatistiche();\n          setOpenCreaConCavi(false);\n        }}\n      />\n\n\n    </Box>\n  );\n};\n\nexport default ComandeList;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAClD,SACEC,GAAG,EACHC,IAAI,EACJC,WAAW,EACXC,UAAU,EACVC,MAAM,EACNC,KAAK,EACLC,SAAS,EACTC,SAAS,EACTC,cAAc,EACdC,SAAS,EACTC,QAAQ,EACRC,KAAK,EACLC,IAAI,EACJC,UAAU,EACVC,MAAM,EACNC,WAAW,EACXC,aAAa,EACbC,aAAa,EACbC,SAAS,EACTC,QAAQ,EACRC,KAAK,EACLC,gBAAgB,EAChBC,OAAO,EACPC,IAAI,EACJC,IAAI,EACJC,QAAQ,EACRC,YAAY,EACZC,OAAO,QACF,eAAe;AACtB,SACEC,GAAG,IAAIC,OAAO,EACdC,IAAI,IAAIC,QAAQ,EAChBC,MAAM,IAAIC,UAAU,EACpBC,UAAU,IAAIC,QAAQ,EACtBC,UAAU,IAAIC,UAAU,EACxBC,OAAO,IAAIC,WAAW,EACtBC,MAAM,IAAIC,UAAU,QACf,qBAAqB;AAC5B,OAAOC,cAAc,MAAM,+BAA+B;AAC1D,OAAOC,kBAAkB,MAAM,sBAAsB;AAAC,SAAAC,MAAA,IAAAC,OAAA,EAAAC,QAAA,IAAAC,SAAA;AAEtD,MAAMC,WAAW,GAAGA,CAAC;EAAEC,UAAU;EAAEC;AAAa,CAAC,KAAK;EAAAC,EAAA;EACpD,MAAM,CAACC,OAAO,EAAEC,UAAU,CAAC,GAAGvD,QAAQ,CAAC,EAAE,CAAC;EAC1C,MAAM,CAACwD,OAAO,EAAEC,UAAU,CAAC,GAAGzD,QAAQ,CAAC,IAAI,CAAC;EAC5C,MAAM,CAAC0D,KAAK,EAAEC,QAAQ,CAAC,GAAG3D,QAAQ,CAAC,IAAI,CAAC;EACxC,MAAM,CAAC4D,UAAU,EAAEC,aAAa,CAAC,GAAG7D,QAAQ,CAAC,KAAK,CAAC;EACnD,MAAM,CAAC8D,eAAe,EAAEC,kBAAkB,CAAC,GAAG/D,QAAQ,CAAC,IAAI,CAAC;EAC5D,MAAM,CAACgE,UAAU,EAAEC,aAAa,CAAC,GAAGjE,QAAQ,CAAC,MAAM,CAAC,CAAC,CAAC;EACtD,MAAM,CAACkE,QAAQ,EAAEC,WAAW,CAAC,GAAGnE,QAAQ,CAAC;IACvCoE,YAAY,EAAE,MAAM;IACpBC,WAAW,EAAE,EAAE;IACfC,YAAY,EAAE,EAAE;IAChBC,aAAa,EAAE,EAAE;IACjBC,QAAQ,EAAE,SAAS;IACnBC,kBAAkB,EAAE;EACtB,CAAC,CAAC;EACF,MAAM,CAACC,WAAW,EAAEC,cAAc,CAAC,GAAG3E,QAAQ,CAAC,IAAI,CAAC;EACpD,MAAM,CAAC4E,gBAAgB,EAAEC,mBAAmB,CAAC,GAAG7E,QAAQ,CAAC,EAAE,CAAC;EAC5D,MAAM,CAAC8E,eAAe,EAAEC,kBAAkB,CAAC,GAAG/E,QAAQ,CAAC,KAAK,CAAC;;EAI7D;EACAC,SAAS,CAAC,MAAM;IACd,IAAIkD,UAAU,EAAE;MACd6B,WAAW,CAAC,CAAC;MACbC,eAAe,CAAC,CAAC;IACnB;EACF,CAAC,EAAE,CAAC9B,UAAU,CAAC,CAAC;EAEhB,MAAM6B,WAAW,GAAG,MAAAA,CAAA,KAAY;IAC9B,IAAI;MACFvB,UAAU,CAAC,IAAI,CAAC;MAChB,MAAMyB,QAAQ,GAAG,MAAMtC,cAAc,CAACuC,UAAU,CAAChC,UAAU,CAAC;MAC5DI,UAAU,CAAC2B,QAAQ,CAAC5B,OAAO,IAAI,EAAE,CAAC;MAClCK,QAAQ,CAAC,IAAI,CAAC;IAChB,CAAC,CAAC,OAAOyB,GAAG,EAAE;MACZC,OAAO,CAAC3B,KAAK,CAAC,uCAAuC,EAAE0B,GAAG,CAAC;MAC3DzB,QAAQ,CAAC,sCAAsC,CAAC;IAClD,CAAC,SAAS;MACRF,UAAU,CAAC,KAAK,CAAC;IACnB;EACF,CAAC;EAED,MAAMwB,eAAe,GAAG,MAAAA,CAAA,KAAY;IAClC,IAAI;MACF,MAAMK,KAAK,GAAG,MAAM1C,cAAc,CAAC2C,qBAAqB,CAACpC,UAAU,CAAC;MACpEwB,cAAc,CAACW,KAAK,CAAC;IACvB,CAAC,CAAC,OAAOF,GAAG,EAAE;MACZC,OAAO,CAAC3B,KAAK,CAAC,2CAA2C,EAAE0B,GAAG,CAAC;IACjE;EACF,CAAC;EAID,MAAMI,gBAAgB,GAAGA,CAACC,IAAI,EAAEC,OAAO,GAAG,IAAI,KAAK;IACjDzB,aAAa,CAACwB,IAAI,CAAC;IACnB1B,kBAAkB,CAAC2B,OAAO,CAAC;IAE3B,IAAID,IAAI,KAAK,MAAM,IAAIC,OAAO,EAAE;MAC9BvB,WAAW,CAAC;QACVC,YAAY,EAAEsB,OAAO,CAACtB,YAAY;QAClCC,WAAW,EAAEqB,OAAO,CAACrB,WAAW,IAAI,EAAE;QACtCC,YAAY,EAAEoB,OAAO,CAACpB,YAAY,IAAI,EAAE;QACxCC,aAAa,EAAEmB,OAAO,CAACnB,aAAa,IAAI,EAAE;QAC1CC,QAAQ,EAAEkB,OAAO,CAAClB,QAAQ,IAAI,SAAS;QACvCC,kBAAkB,EAAEiB,OAAO,CAACjB,kBAAkB,IAAI;MACpD,CAAC,CAAC;IACJ;IAEAZ,aAAa,CAAC,IAAI,CAAC;EACrB,CAAC;EAED,MAAM8B,iBAAiB,GAAGA,CAAA,KAAM;IAC9B9B,aAAa,CAAC,KAAK,CAAC;IACpBE,kBAAkB,CAAC,IAAI,CAAC;IACxBc,mBAAmB,CAAC,EAAE,CAAC;IACvBV,WAAW,CAAC;MACVC,YAAY,EAAE,MAAM;MACpBC,WAAW,EAAE,EAAE;MACfC,YAAY,EAAE,EAAE;MAChBC,aAAa,EAAE,EAAE;MACjBC,QAAQ,EAAE,SAAS;MACnBC,kBAAkB,EAAE;IACtB,CAAC,CAAC;EACJ,CAAC;EAED,MAAMmB,YAAY,GAAG,MAAAA,CAAA,KAAY;IAC/B,IAAI;MACF,IAAI5B,UAAU,KAAK,MAAM,EAAE;QACzB;QACA,MAAMkB,QAAQ,GAAG,MAAMtC,cAAc,CAACiD,aAAa,CAAC/B,eAAe,CAACgC,cAAc,EAAE5B,QAAQ,CAAC;QAC7FmB,OAAO,CAACU,GAAG,CAAC,qBAAqB,EAAEb,QAAQ,CAAC;MAC9C,CAAC,MAAM,IAAIlB,UAAU,KAAK,QAAQ,EAAE;QAClC;QACA,IAAI,CAACY,gBAAgB,CAACoB,IAAI,CAAC,CAAC,EAAE;UAC5BrC,QAAQ,CAAC,6BAA6B,CAAC;UACvC;QACF;QAEA,MAAMsC,WAAW,GAAGrB,gBAAgB,CAACsB,KAAK,CAAC,GAAG,CAAC,CAACC,GAAG,CAACC,EAAE,IAAIA,EAAE,CAACJ,IAAI,CAAC,CAAC,CAAC,CAACK,MAAM,CAACD,EAAE,IAAIA,EAAE,CAAC;QACrF,MAAMxD,cAAc,CAAC0D,WAAW,CAACxC,eAAe,CAACgC,cAAc,EAAEG,WAAW,CAAC;MAC/E;MAEAN,iBAAiB,CAAC,CAAC;MACnBX,WAAW,CAAC,CAAC;MACbC,eAAe,CAAC,CAAC;IACnB,CAAC,CAAC,OAAOG,GAAG,EAAE;MACZC,OAAO,CAAC3B,KAAK,CAAC,yBAAyB,EAAE0B,GAAG,CAAC;MAC7CzB,QAAQ,CAAC,sCAAsC,CAAC;IAClD;EACF,CAAC;EAED,MAAM4C,YAAY,GAAG,MAAOC,aAAa,IAAK;IAC5C,IAAIC,MAAM,CAACC,OAAO,CAAC,+CAA+C,CAAC,EAAE;MACnE,IAAI;QACF,MAAM9D,cAAc,CAAC+D,aAAa,CAACH,aAAa,CAAC;QACjDxB,WAAW,CAAC,CAAC;QACbC,eAAe,CAAC,CAAC;MACnB,CAAC,CAAC,OAAOG,GAAG,EAAE;QACZC,OAAO,CAAC3B,KAAK,CAAC,4BAA4B,EAAE0B,GAAG,CAAC;QAChDzB,QAAQ,CAAC,yCAAyC,CAAC;MACrD;IACF;EACF,CAAC;EAID,MAAMiD,aAAa,GAAIC,KAAK,IAAK;IAC/B,QAAQA,KAAK;MACX,KAAK,QAAQ;QAAE,OAAO,SAAS;MAC/B,KAAK,WAAW;QAAE,OAAO,SAAS;MAClC,KAAK,UAAU;QAAE,OAAO,SAAS;MACjC,KAAK,YAAY;QAAE,OAAO,SAAS;MACnC,KAAK,WAAW;QAAE,OAAO,OAAO;MAChC;QAAS,OAAO,SAAS;IAC3B;EACF,CAAC;EAED,MAAMC,mBAAmB,GAAIC,IAAI,IAAK;IACpC,QAAQA,IAAI;MACV,KAAK,MAAM;QAAE,OAAO,MAAM;MAC1B,KAAK,uBAAuB;QAAE,OAAO,uBAAuB;MAC5D,KAAK,qBAAqB;QAAE,OAAO,qBAAqB;MACxD,KAAK,gBAAgB;QAAE,OAAO,gBAAgB;MAC9C,KAAK,SAAS;QAAE,OAAO,wBAAwB;MAC/C;QAAS,OAAOA,IAAI;IACtB;EACF,CAAC;EAED,MAAMC,gBAAgB,GAAIxC,QAAQ,IAAK;IACrC,QAAQA,QAAQ;MACd,KAAK,OAAO;QAAE,OAAO,SAAS;MAC9B,KAAK,SAAS;QAAE,OAAO,SAAS;MAChC,KAAK,MAAM;QAAE,OAAO,SAAS;MAC7B,KAAK,SAAS;QAAE,OAAO,OAAO;MAC9B;QAAS,OAAO,SAAS;IAC3B;EACF,CAAC;EAED,IAAIhB,OAAO,EAAE;IACX,oBACET,OAAA,CAAC7C,GAAG;MAAC+G,OAAO,EAAC,MAAM;MAACC,cAAc,EAAC,QAAQ;MAACC,UAAU,EAAC,QAAQ;MAACC,SAAS,EAAC,OAAO;MAAAC,QAAA,eAC/EtE,OAAA,CAACxB,gBAAgB;QAAA+F,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACjB,CAAC;EAEV;EAEA,oBACE1E,OAAA,CAAC7C,GAAG;IAAAmH,QAAA,gBAEFtE,OAAA,CAAC7C,GAAG;MAACwH,EAAE,EAAE,CAAE;MAAAL,QAAA,EAER3C,WAAW,iBACV3B,OAAA,CAACtB,IAAI;QAACkG,SAAS;QAACC,OAAO,EAAE,CAAE;QAACF,EAAE,EAAE,CAAE;QAAAL,QAAA,gBAChCtE,OAAA,CAACtB,IAAI;UAACoG,IAAI;UAACC,EAAE,EAAE,EAAG;UAACC,EAAE,EAAE,CAAE;UAACC,EAAE,EAAE,CAAE;UAAAX,QAAA,eAC9BtE,OAAA,CAAC5C,IAAI;YAAAkH,QAAA,eACHtE,OAAA,CAAC3C,WAAW;cAAAiH,QAAA,gBACVtE,OAAA,CAAC1C,UAAU;gBAAC4H,KAAK,EAAC,eAAe;gBAACC,YAAY;gBAAAb,QAAA,EAAC;cAE/C;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAY,CAAC,eACb1E,OAAA,CAAC1C,UAAU;gBAAC8H,OAAO,EAAC,IAAI;gBAAAd,QAAA,EACrB3C,WAAW,CAAC0D;cAAc;gBAAAd,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACjB,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACF;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACV;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,eACP1E,OAAA,CAACtB,IAAI;UAACoG,IAAI;UAACC,EAAE,EAAE,EAAG;UAACC,EAAE,EAAE,CAAE;UAACC,EAAE,EAAE,CAAE;UAAAX,QAAA,eAC9BtE,OAAA,CAAC5C,IAAI;YAAAkH,QAAA,eACHtE,OAAA,CAAC3C,WAAW;cAAAiH,QAAA,gBACVtE,OAAA,CAAC1C,UAAU;gBAAC4H,KAAK,EAAC,eAAe;gBAACC,YAAY;gBAAAb,QAAA,EAAC;cAE/C;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAY,CAAC,eACb1E,OAAA,CAAC1C,UAAU;gBAAC8H,OAAO,EAAC,IAAI;gBAAAd,QAAA,EACrB3C,WAAW,CAAC2D;cAAc;gBAAAf,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACjB,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACF;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACV;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,eACP1E,OAAA,CAACtB,IAAI;UAACoG,IAAI;UAACC,EAAE,EAAE,EAAG;UAACC,EAAE,EAAE,CAAE;UAACC,EAAE,EAAE,CAAE;UAAAX,QAAA,eAC9BtE,OAAA,CAAC5C,IAAI;YAAAkH,QAAA,eACHtE,OAAA,CAAC3C,WAAW;cAAAiH,QAAA,gBACVtE,OAAA,CAAC1C,UAAU;gBAAC4H,KAAK,EAAC,eAAe;gBAACC,YAAY;gBAAAb,QAAA,EAAC;cAE/C;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAY,CAAC,eACb1E,OAAA,CAAC1C,UAAU;gBAAC8H,OAAO,EAAC,IAAI;gBAAAd,QAAA,EACrB3C,WAAW,CAAC4D;cAAiB;gBAAAhB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACpB,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACF;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACV;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,eACP1E,OAAA,CAACtB,IAAI;UAACoG,IAAI;UAACC,EAAE,EAAE,EAAG;UAACC,EAAE,EAAE,CAAE;UAACC,EAAE,EAAE,CAAE;UAAAX,QAAA,eAC9BtE,OAAA,CAAC5C,IAAI;YAAAkH,QAAA,eACHtE,OAAA,CAAC3C,WAAW;cAAAiH,QAAA,gBACVtE,OAAA,CAAC1C,UAAU;gBAAC4H,KAAK,EAAC,eAAe;gBAACC,YAAY;gBAAAb,QAAA,EAAC;cAE/C;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAY,CAAC,eACb1E,OAAA,CAAC1C,UAAU;gBAAC8H,OAAO,EAAC,IAAI;gBAAAd,QAAA,EACrB3C,WAAW,CAAC6D;cAAgB;gBAAAjB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACnB,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACF;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACV;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,eACP1E,OAAA,CAACtB,IAAI;UAACoG,IAAI;UAACC,EAAE,EAAE,EAAG;UAACC,EAAE,EAAE,CAAE;UAACC,EAAE,EAAE,CAAE;UAAAX,QAAA,eAC9BtE,OAAA,CAAC5C,IAAI;YAAAkH,QAAA,eACHtE,OAAA,CAAC3C,WAAW;cAAAiH,QAAA,gBACVtE,OAAA,CAAC1C,UAAU;gBAAC4H,KAAK,EAAC,eAAe;gBAACC,YAAY;gBAAAb,QAAA,EAAC;cAE/C;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAY,CAAC,eACb1E,OAAA,CAAC1C,UAAU;gBAAC8H,OAAO,EAAC,IAAI;gBAAAd,QAAA,EACrB3C,WAAW,CAAC8D;cAAkB;gBAAAlB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACrB,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACF;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACV;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,eACP1E,OAAA,CAACtB,IAAI;UAACoG,IAAI;UAACC,EAAE,EAAE,EAAG;UAACC,EAAE,EAAE,CAAE;UAACC,EAAE,EAAE,CAAE;UAAAX,QAAA,eAC9BtE,OAAA,CAAC5C,IAAI;YAAAkH,QAAA,eACHtE,OAAA,CAAC3C,WAAW;cAAAiH,QAAA,gBACVtE,OAAA,CAAC1C,UAAU;gBAAC4H,KAAK,EAAC,eAAe;gBAACC,YAAY;gBAAAb,QAAA,EAAC;cAE/C;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAY,CAAC,eACb1E,OAAA,CAAC1C,UAAU;gBAAC8H,OAAO,EAAC,IAAI;gBAAAd,QAAA,GACrB3C,WAAW,CAAC+D,+BAA+B,CAACC,OAAO,CAAC,CAAC,CAAC,EAAC,GAC1D;cAAA;gBAAApB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAY,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACF;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACV;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH;IACP;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACE,CAAC,eAGN1E,OAAA,CAAC7C,GAAG;MAAC+G,OAAO,EAAC,MAAM;MAACC,cAAc,EAAC,eAAe;MAACC,UAAU,EAAC,QAAQ;MAACO,EAAE,EAAE,CAAE;MAACiB,QAAQ,EAAC,MAAM;MAACC,GAAG,EAAE,CAAE;MAAAvB,QAAA,gBACnGtE,OAAA,CAAC7C,GAAG;QAAC+G,OAAO,EAAC,MAAM;QAAC2B,GAAG,EAAE,CAAE;QAACD,QAAQ,EAAC,MAAM;QAAAtB,QAAA,gBACzCtE,OAAA,CAACzC,MAAM;UACL6H,OAAO,EAAC,WAAW;UACnBU,SAAS,eAAE9F,OAAA,CAAChB,OAAO;YAAAuF,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAE;UACvBqB,OAAO,EAAEA,CAAA,KAAM/D,kBAAkB,CAAC,IAAI,CAAE;UACxCkD,KAAK,EAAC,SAAS;UAAAZ,QAAA,EAChB;QAED;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC,eAIT1E,OAAA,CAACzC,MAAM;UACL6H,OAAO,EAAC,UAAU;UAClBU,SAAS,eAAE9F,OAAA,CAACR,UAAU;YAAA+E,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAE;UAC1BqB,OAAO,EAAEA,CAAA,KAAM;YACb,IAAIxF,OAAO,CAACyF,MAAM,KAAK,CAAC,EAAE;cACxBpF,QAAQ,CAAC,iDAAiD,CAAC;cAC3D;YACF;YACA;YACAA,QAAQ,CAAC,yEAAyE,CAAC;UACrF,CAAE;UACFqF,QAAQ,EAAE1F,OAAO,CAACyF,MAAM,KAAK,CAAE;UAAA1B,QAAA,EAChC;QAED;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACN,CAAC,eAEN1E,OAAA,CAACzC,MAAM;QACL6H,OAAO,EAAC,UAAU;QAClBU,SAAS,eAAE9F,OAAA,CAACN,WAAW;UAAA6E,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAE;QAC3BqB,OAAO,EAAEA,CAAA,KAAM;UACb9D,WAAW,CAAC,CAAC;UACbC,eAAe,CAAC,CAAC;QACnB,CAAE;QAAAoC,QAAA,EACH;MAED;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAQ,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACN,CAAC,EAGL/D,KAAK,iBACJX,OAAA,CAACzB,KAAK;MAAC2H,QAAQ,EAAC,OAAO;MAACC,EAAE,EAAE;QAAExB,EAAE,EAAE;MAAE,CAAE;MAAAL,QAAA,EACnC3D;IAAK;MAAA4D,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACD,CACR,eAGD1E,OAAA,CAACrC,cAAc;MAACyI,SAAS,EAAEtI,KAAM;MAAAwG,QAAA,eAC/BtE,OAAA,CAACxC,KAAK;QAAA8G,QAAA,gBACJtE,OAAA,CAACpC,SAAS;UAAA0G,QAAA,eACRtE,OAAA,CAACnC,QAAQ;YAAAyG,QAAA,gBACPtE,OAAA,CAACtC,SAAS;cAAA4G,QAAA,EAAC;YAAM;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAW,CAAC,eAC7B1E,OAAA,CAACtC,SAAS;cAAA4G,QAAA,EAAC;YAAI;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAW,CAAC,eAC3B1E,OAAA,CAACtC,SAAS;cAAA4G,QAAA,EAAC;YAAQ;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAW,CAAC,eAC/B1E,OAAA,CAACtC,SAAS;cAAA4G,QAAA,EAAC;YAAY;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAW,CAAC,eACnC1E,OAAA,CAACtC,SAAS;cAAA4G,QAAA,EAAC;YAAc;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAW,CAAC,eACrC1E,OAAA,CAACtC,SAAS;cAAA4G,QAAA,EAAC;YAAK;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAW,CAAC,eAC5B1E,OAAA,CAACtC,SAAS;cAAA4G,QAAA,EAAC;YAAc;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAW,CAAC,eACrC1E,OAAA,CAACtC,SAAS;cAAA4G,QAAA,EAAC;YAAa;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAW,CAAC,eACpC1E,OAAA,CAACtC,SAAS;cAAA4G,QAAA,EAAC;YAAM;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAW,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACrB;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACF,CAAC,eACZ1E,OAAA,CAACvC,SAAS;UAAA6G,QAAA,EACP/D,OAAO,CAAC6C,GAAG,CAAET,OAAO,iBACnB3C,OAAA,CAACnC,QAAQ;YAAAyG,QAAA,gBACPtE,OAAA,CAACtC,SAAS;cAAA4G,QAAA,eACRtE,OAAA,CAAC1C,UAAU;gBAAC8H,OAAO,EAAC,OAAO;gBAACiB,UAAU,EAAC,MAAM;gBAAA/B,QAAA,EAC1C3B,OAAO,CAACI;cAAc;gBAAAwB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACb;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACJ,CAAC,eACZ1E,OAAA,CAACtC,SAAS;cAAA4G,QAAA,eACRtE,OAAA,CAACjC,IAAI;gBACHuI,KAAK,EAAEvC,mBAAmB,CAACpB,OAAO,CAACtB,YAAY,CAAE;gBACjDkF,IAAI,EAAC,OAAO;gBACZnB,OAAO,EAAC;cAAU;gBAAAb,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACnB;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACO,CAAC,eACZ1E,OAAA,CAACtC,SAAS;cAAA4G,QAAA,eACRtE,OAAA,CAACjC,IAAI;gBACHuI,KAAK,EAAE3D,OAAO,CAAClB,QAAQ,IAAI,SAAU;gBACrC8E,IAAI,EAAC,OAAO;gBACZrB,KAAK,EAAEjB,gBAAgB,CAACtB,OAAO,CAAClB,QAAQ,IAAI,SAAS;cAAE;gBAAA8C,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACxD;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACO,CAAC,eACZ1E,OAAA,CAACtC,SAAS;cAAA4G,QAAA,EAAE3B,OAAO,CAACpB;YAAY;cAAAgD,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAY,CAAC,eAC7C1E,OAAA,CAACtC,SAAS;cAAA4G,QAAA,EACP,IAAIkC,IAAI,CAAC7D,OAAO,CAAC8D,cAAc,CAAC,CAACC,kBAAkB,CAAC,OAAO;YAAC;cAAAnC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACpD,CAAC,eACZ1E,OAAA,CAACtC,SAAS;cAAA4G,QAAA,eACRtE,OAAA,CAACjC,IAAI;gBACHuI,KAAK,EAAE3D,OAAO,CAACmB,KAAM;gBACrBoB,KAAK,EAAErB,aAAa,CAAClB,OAAO,CAACmB,KAAK,CAAE;gBACpCyC,IAAI,EAAC;cAAO;gBAAAhC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACb;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACO,CAAC,eACZ1E,OAAA,CAACtC,SAAS;cAAA4G,QAAA,EAAE3B,OAAO,CAACgE,qBAAqB,IAAI;YAAC;cAAApC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAY,CAAC,eAC3D1E,OAAA,CAACtC,SAAS;cAAA4G,QAAA,EACP3B,OAAO,CAACiE,yBAAyB,GAChC,GAAGjE,OAAO,CAACiE,yBAAyB,CAACjB,OAAO,CAAC,CAAC,CAAC,GAAG,GAAG;YAAI;cAAApB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAClD,CAAC,eACZ1E,OAAA,CAACtC,SAAS;cAAA4G,QAAA,gBACRtE,OAAA,CAACvB,OAAO;gBAACoI,KAAK,EAAC,YAAY;gBAAAvC,QAAA,eACzBtE,OAAA,CAAChC,UAAU;kBACTuI,IAAI,EAAC,OAAO;kBACZR,OAAO,EAAEA,CAAA,KAAMtD,gBAAgB,CAAC,MAAM,EAAEE,OAAO,CAAE;kBAAA2B,QAAA,eAEjDtE,OAAA,CAACV,QAAQ;oBAAAiF,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACF;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACN,CAAC,eACV1E,OAAA,CAACvB,OAAO;gBAACoI,KAAK,EAAC,UAAU;gBAAAvC,QAAA,eACvBtE,OAAA,CAAChC,UAAU;kBACTuI,IAAI,EAAC,OAAO;kBACZR,OAAO,EAAEA,CAAA,KAAMtD,gBAAgB,CAAC,MAAM,EAAEE,OAAO,CAAE;kBAAA2B,QAAA,eAEjDtE,OAAA,CAACd,QAAQ;oBAAAqF,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACF;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACN,CAAC,eACV1E,OAAA,CAACvB,OAAO;gBAACoI,KAAK,EAAC,cAAc;gBAAAvC,QAAA,eAC3BtE,OAAA,CAAChC,UAAU;kBACTuI,IAAI,EAAC,OAAO;kBACZR,OAAO,EAAEA,CAAA,KAAMtD,gBAAgB,CAAC,QAAQ,EAAEE,OAAO,CAAE;kBACnDuC,KAAK,EAAC,SAAS;kBAAAZ,QAAA,eAEftE,OAAA,CAACR,UAAU;oBAAA+E,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACJ;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACN,CAAC,eACV1E,OAAA,CAACvB,OAAO;gBAACoI,KAAK,EAAC,SAAS;gBAAAvC,QAAA,eACtBtE,OAAA,CAAChC,UAAU;kBACTuI,IAAI,EAAC,OAAO;kBACZR,OAAO,EAAEA,CAAA,KAAMvC,YAAY,CAACb,OAAO,CAACI,cAAc,CAAE;kBACpDmC,KAAK,EAAC,OAAO;kBAAAZ,QAAA,eAEbtE,OAAA,CAACZ,UAAU;oBAAAmF,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACJ;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACN,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACD,CAAC;UAAA,GAvEC/B,OAAO,CAACI,cAAc;YAAAwB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAwE3B,CACX;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACO,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACP;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACM,CAAC,EAEhBnE,OAAO,CAACyF,MAAM,KAAK,CAAC,IAAI,CAACvF,OAAO,iBAC/BT,OAAA,CAAC7C,GAAG;MAAC2J,SAAS,EAAC,QAAQ;MAACC,EAAE,EAAE,CAAE;MAAAzC,QAAA,gBAC5BtE,OAAA,CAAC1C,UAAU;QAAC8H,OAAO,EAAC,IAAI;QAACF,KAAK,EAAC,eAAe;QAAAZ,QAAA,EAAC;MAE/C;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAY,CAAC,eACb1E,OAAA,CAAC1C,UAAU;QAAC8H,OAAO,EAAC,OAAO;QAACF,KAAK,EAAC,eAAe;QAAAZ,QAAA,EAAC;MAElD;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAY,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACV,CACN,eAGD1E,OAAA,CAAC/B,MAAM;MAAC+I,IAAI,EAAEnG,UAAW;MAACoG,OAAO,EAAErE,iBAAkB;MAACsE,QAAQ,EAAC,IAAI;MAACC,SAAS;MAAA7C,QAAA,gBAC3EtE,OAAA,CAAC9B,WAAW;QAAAoG,QAAA,GACTrD,UAAU,KAAK,MAAM,IAAI,kBAAkB,EAC3CA,UAAU,KAAK,MAAM,IAAI,kBAAkB,EAC3CA,UAAU,KAAK,QAAQ,IAAI,kBAAkBF,eAAe,aAAfA,eAAe,uBAAfA,eAAe,CAAEgC,cAAc,EAAE;MAAA;QAAAwB,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACpE,CAAC,eACd1E,OAAA,CAAC7B,aAAa;QAAAmG,QAAA,eACZtE,OAAA,CAAC7C,GAAG;UAACgJ,EAAE,EAAE;YAAEiB,EAAE,EAAE;UAAE,CAAE;UAAA9C,QAAA,EAChBrD,UAAU,KAAK,QAAQ,gBACtBjB,OAAA,CAAAE,SAAA;YAAAoE,QAAA,gBACEtE,OAAA,CAACzB,KAAK;cAAC2H,QAAQ,EAAC,MAAM;cAACC,EAAE,EAAE;gBAAExB,EAAE,EAAE;cAAE,CAAE;cAAAL,QAAA,EAAC;YAEtC;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CAAC,eACR1E,OAAA,CAAC3B,SAAS;cACR8I,SAAS;cACTb,KAAK,EAAC,+BAA+B;cACrCe,KAAK,EAAExF,gBAAiB;cACxByF,QAAQ,EAAGC,CAAC,IAAKzF,mBAAmB,CAACyF,CAAC,CAACC,MAAM,CAACH,KAAK,CAAE;cACrDI,MAAM,EAAC,QAAQ;cACfC,WAAW,EAAC,+BAA+B;cAC3CC,UAAU,EAAC,oCAAoC;cAC/CC,SAAS;cACTC,IAAI,EAAE;YAAE;cAAAtD,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACT,CAAC;UAAA,eACF,CAAC,GACDzD,UAAU,KAAK,MAAM,IAAIF,eAAe,gBAC1Cf,OAAA,CAAAE,SAAA;YAAAoE,QAAA,eACEtE,OAAA,CAACrB,IAAI;cAAA2F,QAAA,gBACHtE,OAAA,CAACpB,QAAQ;gBAAA0F,QAAA,eACPtE,OAAA,CAACnB,YAAY;kBACXiJ,OAAO,EAAC,gBAAgB;kBACxBC,SAAS,EAAEhH,eAAe,CAACgC;gBAAe;kBAAAwB,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAC3C;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACM,CAAC,eACX1E,OAAA,CAAClB,OAAO;gBAAAyF,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,eACX1E,OAAA,CAACpB,QAAQ;gBAAA0F,QAAA,eACPtE,OAAA,CAACnB,YAAY;kBACXiJ,OAAO,EAAC,MAAM;kBACdC,SAAS,EAAEhE,mBAAmB,CAAChD,eAAe,CAACM,YAAY;gBAAE;kBAAAkD,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAC9D;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACM,CAAC,eACX1E,OAAA,CAAClB,OAAO;gBAAAyF,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,eACX1E,OAAA,CAACpB,QAAQ;gBAAA0F,QAAA,eACPtE,OAAA,CAACnB,YAAY;kBACXiJ,OAAO,EAAC,OAAO;kBACfC,SAAS,eACP/H,OAAA,CAACjC,IAAI;oBACHuI,KAAK,EAAEvF,eAAe,CAAC+C,KAAM;oBAC7BoB,KAAK,EAAErB,aAAa,CAAC9C,eAAe,CAAC+C,KAAK,CAAE;oBAC5CyC,IAAI,EAAC;kBAAO;oBAAAhC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACb;gBACF;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACF;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACM,CAAC,eACX1E,OAAA,CAAClB,OAAO;gBAAAyF,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,eACX1E,OAAA,CAACpB,QAAQ;gBAAA0F,QAAA,eACPtE,OAAA,CAACnB,YAAY;kBACXiJ,OAAO,EAAC,aAAa;kBACrBC,SAAS,EAAEhH,eAAe,CAACO,WAAW,IAAI;gBAAsB;kBAAAiD,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACjE;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACM,CAAC,eACX1E,OAAA,CAAClB,OAAO;gBAAAyF,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,eACX1E,OAAA,CAACpB,QAAQ;gBAAA0F,QAAA,eACPtE,OAAA,CAACnB,YAAY;kBACXiJ,OAAO,EAAC,aAAU;kBAClBC,SAAS,eACP/H,OAAA,CAACjC,IAAI;oBACHuI,KAAK,EAAEvF,eAAe,CAACU,QAAQ,IAAI,SAAU;oBAC7CyD,KAAK,EAAEjB,gBAAgB,CAAClD,eAAe,CAACU,QAAQ,IAAI,SAAS,CAAE;oBAC/D8E,IAAI,EAAC;kBAAO;oBAAAhC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACb;gBACF;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACF;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACM,CAAC,eACX1E,OAAA,CAAClB,OAAO;gBAAAyF,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,eACX1E,OAAA,CAACpB,QAAQ;gBAAA0F,QAAA,eACPtE,OAAA,CAACnB,YAAY;kBACXiJ,OAAO,EAAC,cAAc;kBACtBC,SAAS,EAAEhH,eAAe,CAACQ,YAAY,IAAI;gBAAgB;kBAAAgD,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAC5D;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACM,CAAC,EACV3D,eAAe,CAACW,kBAAkB,iBACjC1B,OAAA,CAAAE,SAAA;gBAAAoE,QAAA,gBACEtE,OAAA,CAAClB,OAAO;kBAAAyF,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAC,eACX1E,OAAA,CAACpB,QAAQ;kBAAA0F,QAAA,eACPtE,OAAA,CAACnB,YAAY;oBACXiJ,OAAO,EAAC,oBAAoB;oBAC5BC,SAAS,EAAEhH,eAAe,CAACW;kBAAmB;oBAAA6C,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAC/C;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACM,CAAC;cAAA,eACX,CACH,eACD1E,OAAA,CAAClB,OAAO;gBAAAyF,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,eACX1E,OAAA,CAACpB,QAAQ;gBAAA0F,QAAA,eACPtE,OAAA,CAACnB,YAAY;kBACXiJ,OAAO,EAAC,gBAAgB;kBACxBC,SAAS,EAAE,IAAIvB,IAAI,CAACzF,eAAe,CAAC0F,cAAc,CAAC,CAACC,kBAAkB,CAAC,OAAO;gBAAE;kBAAAnC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACjF;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACM,CAAC,EACV3D,eAAe,CAACS,aAAa,iBAC5BxB,OAAA,CAAAE,SAAA;gBAAAoE,QAAA,gBACEtE,OAAA,CAAClB,OAAO;kBAAAyF,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAC,eACX1E,OAAA,CAACpB,QAAQ;kBAAA0F,QAAA,eACPtE,OAAA,CAACnB,YAAY;oBACXiJ,OAAO,EAAC,eAAe;oBACvBC,SAAS,EAAE,IAAIvB,IAAI,CAACzF,eAAe,CAACS,aAAa,CAAC,CAACkF,kBAAkB,CAAC,OAAO;kBAAE;oBAAAnC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAChF;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACM,CAAC;cAAA,eACX,CACH,eACD1E,OAAA,CAAClB,OAAO;gBAAAyF,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,eACX1E,OAAA,CAACpB,QAAQ;gBAAA0F,QAAA,eACPtE,OAAA,CAACnB,YAAY;kBACXiJ,OAAO,EAAC,gBAAgB;kBACxBC,SAAS,EAAEhH,eAAe,CAAC4F,qBAAqB,IAAI;gBAAE;kBAAApC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACvD;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACM,CAAC,eACX1E,OAAA,CAAClB,OAAO;gBAAAyF,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,eACX1E,OAAA,CAACpB,QAAQ;gBAAA0F,QAAA,eACPtE,OAAA,CAACnB,YAAY;kBACXiJ,OAAO,EAAC,eAAe;kBACvBC,SAAS,EAAE,GAAG,CAAChH,eAAe,CAAC6F,yBAAyB,IAAI,CAAC,EAAEjB,OAAO,CAAC,CAAC,CAAC;gBAAI;kBAAApB,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAC9E;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACM,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACP;UAAC,gBACP,CAAC,gBAEH1E,OAAA,CAAAE,SAAA;YAAAoE,QAAA,gBACEtE,OAAA,CAAC3B,SAAS;cACR8I,SAAS;cACTa,MAAM;cACN1B,KAAK,EAAC,cAAc;cACpBe,KAAK,EAAElG,QAAQ,CAACE,YAAa;cAC7BiG,QAAQ,EAAGC,CAAC,IAAKnG,WAAW,CAAC;gBAAE,GAAGD,QAAQ;gBAAEE,YAAY,EAAEkG,CAAC,CAACC,MAAM,CAACH;cAAM,CAAC,CAAE;cAC5EI,MAAM,EAAC,QAAQ;cACfxB,QAAQ,EAAEhF,UAAU,KAAK,MAAO;cAAAqD,QAAA,gBAEhCtE,OAAA,CAAC1B,QAAQ;gBAAC+I,KAAK,EAAC,MAAM;gBAAA/C,QAAA,EAAC;cAAI;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAU,CAAC,eACtC1E,OAAA,CAAC1B,QAAQ;gBAAC+I,KAAK,EAAC,uBAAuB;gBAAA/C,QAAA,EAAC;cAAqB;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAU,CAAC,eACxE1E,OAAA,CAAC1B,QAAQ;gBAAC+I,KAAK,EAAC,qBAAqB;gBAAA/C,QAAA,EAAC;cAAmB;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAU,CAAC,eACpE1E,OAAA,CAAC1B,QAAQ;gBAAC+I,KAAK,EAAC,gBAAgB;gBAAA/C,QAAA,EAAC;cAAc;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAU,CAAC,eAC1D1E,OAAA,CAAC1B,QAAQ;gBAAC+I,KAAK,EAAC,SAAS;gBAAA/C,QAAA,EAAC;cAAO;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAU,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACnC,CAAC,eAEZ1E,OAAA,CAAC3B,SAAS;cACR8I,SAAS;cACTa,MAAM;cACN1B,KAAK,EAAC,aAAU;cAChBe,KAAK,EAAElG,QAAQ,CAACM,QAAS;cACzB6F,QAAQ,EAAGC,CAAC,IAAKnG,WAAW,CAAC;gBAAE,GAAGD,QAAQ;gBAAEM,QAAQ,EAAE8F,CAAC,CAACC,MAAM,CAACH;cAAM,CAAC,CAAE;cACxEI,MAAM,EAAC,QAAQ;cACfxB,QAAQ,EAAEhF,UAAU,KAAK,MAAO;cAAAqD,QAAA,gBAEhCtE,OAAA,CAAC1B,QAAQ;gBAAC+I,KAAK,EAAC,OAAO;gBAAA/C,QAAA,EAAC;cAAK;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAU,CAAC,eACxC1E,OAAA,CAAC1B,QAAQ;gBAAC+I,KAAK,EAAC,SAAS;gBAAA/C,QAAA,EAAC;cAAO;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAU,CAAC,eAC5C1E,OAAA,CAAC1B,QAAQ;gBAAC+I,KAAK,EAAC,MAAM;gBAAA/C,QAAA,EAAC;cAAI;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAU,CAAC,eACtC1E,OAAA,CAAC1B,QAAQ;gBAAC+I,KAAK,EAAC,SAAS;gBAAA/C,QAAA,EAAC;cAAO;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAU,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACnC,CAAC,eAEZ1E,OAAA,CAAC3B,SAAS;cACR8I,SAAS;cACTb,KAAK,EAAC,aAAa;cACnBe,KAAK,EAAElG,QAAQ,CAACG,WAAY;cAC5BgG,QAAQ,EAAGC,CAAC,IAAKnG,WAAW,CAAC;gBAAE,GAAGD,QAAQ;gBAAEG,WAAW,EAAEiG,CAAC,CAACC,MAAM,CAACH;cAAM,CAAC,CAAE;cAC3EI,MAAM,EAAC,QAAQ;cACfG,SAAS;cACTC,IAAI,EAAE,CAAE;cACR5B,QAAQ,EAAEhF,UAAU,KAAK;YAAO;cAAAsD,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACjC,CAAC,eAEF1E,OAAA,CAAC3B,SAAS;cACR8I,SAAS;cACTb,KAAK,EAAC,cAAc;cACpBe,KAAK,EAAElG,QAAQ,CAACI,YAAa;cAC7B+F,QAAQ,EAAGC,CAAC,IAAKnG,WAAW,CAAC;gBAAE,GAAGD,QAAQ;gBAAEI,YAAY,EAAEgG,CAAC,CAACC,MAAM,CAACH;cAAM,CAAC,CAAE;cAC5EI,MAAM,EAAC,QAAQ;cACfxB,QAAQ,EAAEhF,UAAU,KAAK,MAAO;cAChCgH,QAAQ;cACRN,UAAU,EAAC;YAAuC;cAAApD,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACnD,CAAC,eAEF1E,OAAA,CAAC3B,SAAS;cACR8I,SAAS;cACTb,KAAK,EAAC,oBAAoB;cAC1Be,KAAK,EAAElG,QAAQ,CAACO,kBAAmB;cACnC4F,QAAQ,EAAGC,CAAC,IAAKnG,WAAW,CAAC;gBAAE,GAAGD,QAAQ;gBAAEO,kBAAkB,EAAE6F,CAAC,CAACC,MAAM,CAACH;cAAM,CAAC,CAAE;cAClFI,MAAM,EAAC,QAAQ;cACfG,SAAS;cACTC,IAAI,EAAE,CAAE;cACR5B,QAAQ,EAAEhF,UAAU,KAAK,MAAO;cAChC0G,UAAU,EAAC;YAA2C;cAAApD,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACvD,CAAC,eAEF1E,OAAA,CAAC3B,SAAS;cACR8I,SAAS;cACTb,KAAK,EAAC,eAAe;cACrB4B,IAAI,EAAC,MAAM;cACXb,KAAK,EAAElG,QAAQ,CAACK,aAAc;cAC9B8F,QAAQ,EAAGC,CAAC,IAAKnG,WAAW,CAAC;gBAAE,GAAGD,QAAQ;gBAAEK,aAAa,EAAE+F,CAAC,CAACC,MAAM,CAACH;cAAM,CAAC,CAAE;cAC7EI,MAAM,EAAC,QAAQ;cACfU,eAAe,EAAE;gBAAEC,MAAM,EAAE;cAAK,CAAE;cAClCnC,QAAQ,EAAEhF,UAAU,KAAK;YAAO;cAAAsD,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACjC,CAAC;UAAA,eACF;QACH;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACE;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACO,CAAC,eAChB1E,OAAA,CAAC5B,aAAa;QAAAkG,QAAA,gBACZtE,OAAA,CAACzC,MAAM;UAACwI,OAAO,EAAEnD,iBAAkB;UAAA0B,QAAA,EAChCrD,UAAU,KAAK,MAAM,GAAG,QAAQ,GAAG;QAAS;UAAAsD,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACvC,CAAC,EACRzD,UAAU,KAAK,MAAM,iBACpBjB,OAAA,CAACzC,MAAM;UAACwI,OAAO,EAAElD,YAAa;UAACuC,OAAO,EAAC,WAAW;UAAAd,QAAA,EAC/CrD,UAAU,KAAK,MAAM,GAAG,OAAO,GAC/BA,UAAU,KAAK,QAAQ,GAAG,cAAc,GAAG;QAAO;UAAAsD,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC7C,CACT;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACY,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACV,CAAC,eAGT1E,OAAA,CAACF,kBAAkB;MACjBM,UAAU,EAAEA,UAAW;MACvB4G,IAAI,EAAEjF,eAAgB;MACtBkF,OAAO,EAAEA,CAAA,KAAMjF,kBAAkB,CAAC,KAAK,CAAE;MACzCqG,SAAS,EAAEA,CAAA,KAAM;QACfpG,WAAW,CAAC,CAAC;QACbC,eAAe,CAAC,CAAC;QACjBF,kBAAkB,CAAC,KAAK,CAAC;MAC3B;IAAE;MAAAuC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OAGC,CAAC;AAEV,CAAC;AAACpE,EAAA,CAnoBIH,WAAW;AAAAmI,EAAA,GAAXnI,WAAW;AAqoBjB,eAAeA,WAAW;AAAC,IAAAmI,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}