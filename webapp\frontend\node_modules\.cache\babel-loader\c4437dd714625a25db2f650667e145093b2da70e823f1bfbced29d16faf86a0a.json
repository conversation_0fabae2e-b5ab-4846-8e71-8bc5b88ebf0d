{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\PycharmProjects\\\\CMS\\\\webapp\\\\frontend\\\\src\\\\pages\\\\cavi\\\\ReportCaviPageNew.js\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect } from 'react';\nimport { Box, Typography, Paper, Grid, Card, CardContent, CardActions, Button, Chip, Alert, CircularProgress, Divider, IconButton, Tooltip, Dialog, DialogTitle, DialogContent, DialogActions, FormControl, InputLabel, Select, MenuItem, TextField, Accordion, AccordionSummary, AccordionDetails } from '@mui/material';\nimport { Assessment as AssessmentIcon, BarChart as BarChartIcon, PieChart as PieChartIcon, Timeline as TimelineIcon, List as ListIcon, Download as DownloadIcon, Visibility as VisibilityIcon, Refresh as RefreshIcon, ArrowBack as ArrowBackIcon, DateRange as DateRangeIcon, Cable as CableIcon, Inventory as InventoryIcon, ExpandMore as ExpandMoreIcon } from '@mui/icons-material';\nimport { useNavigate, useParams } from 'react-router-dom';\nimport { useAuth } from '../../context/AuthContext';\nimport AdminHomeButton from '../../components/common/AdminHomeButton';\nimport reportService from '../../services/reportService';\nimport { jsxDEV as _jsxDEV, Fragment as _Fragment } from \"react/jsx-dev-runtime\";\nconst ReportCaviPageNew = () => {\n  _s();\n  const navigate = useNavigate();\n  const {\n    cantiereId\n  } = useParams();\n  const {\n    user\n  } = useAuth();\n  const [loading, setLoading] = useState(false);\n  const [error, setError] = useState(null);\n  const [reportData, setReportData] = useState(null);\n  const [selectedReport, setSelectedReport] = useState(null);\n  const [openDialog, setOpenDialog] = useState(false);\n  const [dialogType, setDialogType] = useState('');\n  const [formData, setFormData] = useState({\n    formato: 'video',\n    data_inizio: '',\n    data_fine: '',\n    id_bobina: ''\n  });\n\n  // Configurazione dei report disponibili\n  const reportTypes = [{\n    id: 'progress',\n    title: 'Report Avanzamento',\n    description: 'Panoramica completa dell\\'avanzamento dei lavori con metriche di performance e previsioni',\n    icon: /*#__PURE__*/_jsxDEV(AssessmentIcon, {}, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 74,\n      columnNumber: 13\n    }, this),\n    color: 'primary',\n    features: ['Metri posati vs teorici', 'Percentuale completamento', 'Previsioni timeline', 'Performance giornaliera']\n  }, {\n    id: 'boq',\n    title: 'Bill of Quantities',\n    description: 'Distinta materiali dettagliata con analisi dei consumi e disponibilità',\n    icon: /*#__PURE__*/_jsxDEV(ListIcon, {}, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 82,\n      columnNumber: 13\n    }, this),\n    color: 'secondary',\n    features: ['Materiali per tipologia', 'Consumi vs disponibilità', 'Previsioni acquisti', 'Analisi costi']\n  }, {\n    id: 'bobine',\n    title: 'Report Utilizzo Bobine',\n    description: 'Analisi completa dell\\'utilizzo delle bobine con efficienza e sprechi',\n    icon: /*#__PURE__*/_jsxDEV(InventoryIcon, {}, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 90,\n      columnNumber: 13\n    }, this),\n    color: 'success',\n    features: ['Utilizzo per bobina', 'Efficienza materiali', 'Bobine disponibili', 'Analisi sprechi']\n  }, {\n    id: 'bobina-specifica',\n    title: 'Report Bobina Specifica',\n    description: 'Dettaglio approfondito di una singola bobina con tutti i cavi associati',\n    icon: /*#__PURE__*/_jsxDEV(CableIcon, {}, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 98,\n      columnNumber: 13\n    }, this),\n    color: 'info',\n    features: ['Dettaglio bobina', 'Cavi associati', 'Utilizzo specifico', 'Storico operazioni']\n  }, {\n    id: 'posa-periodo',\n    title: 'Report Posa per Periodo',\n    description: 'Analisi temporale della posa con trend e pattern di lavoro',\n    icon: /*#__PURE__*/_jsxDEV(TimelineIcon, {}, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 106,\n      columnNumber: 13\n    }, this),\n    color: 'warning',\n    features: ['Trend temporali', 'Performance periodiche', 'Analisi stagionali', 'Produttività team']\n  }, {\n    id: 'cavi-stato',\n    title: 'Report Cavi per Stato',\n    description: 'Classificazione dei cavi per stato di installazione con statistiche dettagliate',\n    icon: /*#__PURE__*/_jsxDEV(BarChartIcon, {}, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 114,\n      columnNumber: 13\n    }, this),\n    color: 'error',\n    features: ['Cavi per stato', 'Statistiche installazione', 'Problematiche', 'Azioni richieste']\n  }];\n  const handleReportSelect = reportType => {\n    setSelectedReport(reportType);\n    setDialogType(reportType.id);\n\n    // Imposta valori di default per alcuni report\n    if (reportType.id === 'posa-periodo') {\n      const today = new Date();\n      const lastMonth = new Date();\n      lastMonth.setMonth(today.getMonth() - 1);\n      setFormData({\n        ...formData,\n        data_inizio: lastMonth.toISOString().split('T')[0],\n        data_fine: today.toISOString().split('T')[0]\n      });\n    }\n    setOpenDialog(true);\n  };\n  const handleGenerateReport = async () => {\n    try {\n      setLoading(true);\n      setError(null);\n      let response;\n      switch (dialogType) {\n        case 'progress':\n          response = await reportService.getProgressReport(cantiereId, formData.formato);\n          break;\n        case 'boq':\n          response = await reportService.getBillOfQuantities(cantiereId, formData.formato);\n          break;\n        case 'bobine':\n          response = await reportService.getBobineReport(cantiereId, formData.formato);\n          break;\n        case 'bobina-specifica':\n          if (!formData.id_bobina) {\n            setError('Inserisci l\\'ID della bobina');\n            return;\n          }\n          response = await reportService.getBobinaReport(cantiereId, formData.id_bobina, formData.formato);\n          break;\n        case 'posa-periodo':\n          if (!formData.data_inizio || !formData.data_fine) {\n            setError('Seleziona le date di inizio e fine periodo');\n            return;\n          }\n          response = await reportService.getPosaPerPeriodoReport(cantiereId, formData.data_inizio, formData.data_fine, formData.formato);\n          break;\n        case 'cavi-stato':\n          response = await reportService.getCaviStatoReport(cantiereId, formData.formato);\n          break;\n        default:\n          throw new Error('Tipo di report non riconosciuto');\n      }\n      if (formData.formato === 'video') {\n        setReportData(response.content);\n      } else {\n        // Per PDF/Excel, apri il link di download\n        if (response.file_url) {\n          window.open(response.file_url, '_blank');\n        }\n      }\n      setOpenDialog(false);\n    } catch (err) {\n      console.error('Errore nella generazione del report:', err);\n      setError(err.detail || err.message || 'Errore durante la generazione del report');\n    } finally {\n      setLoading(false);\n    }\n  };\n  const handleCloseDialog = () => {\n    setOpenDialog(false);\n    setError(null);\n    setFormData({\n      formato: 'video',\n      data_inizio: '',\n      data_fine: '',\n      id_bobina: ''\n    });\n  };\n  const renderReportContent = () => {\n    if (!reportData) return null;\n    return /*#__PURE__*/_jsxDEV(Paper, {\n      sx: {\n        p: 3,\n        mt: 3\n      },\n      children: [/*#__PURE__*/_jsxDEV(Box, {\n        sx: {\n          display: 'flex',\n          justifyContent: 'space-between',\n          alignItems: 'center',\n          mb: 2\n        },\n        children: [/*#__PURE__*/_jsxDEV(Typography, {\n          variant: \"h6\",\n          children: [selectedReport === null || selectedReport === void 0 ? void 0 : selectedReport.title, \" - \", reportData.nome_cantiere]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 218,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Button, {\n          startIcon: /*#__PURE__*/_jsxDEV(RefreshIcon, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 222,\n            columnNumber: 24\n          }, this),\n          onClick: () => setReportData(null),\n          variant: \"outlined\",\n          size: \"small\",\n          children: \"Nuovo Report\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 221,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 217,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Divider, {\n        sx: {\n          mb: 3\n        }\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 231,\n        columnNumber: 9\n      }, this), dialogType === 'progress' && renderProgressReport(), dialogType === 'boq' && renderBoqReport(), dialogType === 'bobine' && renderBobineReport(), dialogType === 'bobina-specifica' && renderBobinaSpecificaReport(), dialogType === 'posa-periodo' && renderPosaPeriodoReport(), dialogType === 'cavi-stato' && renderCaviStatoReport()]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 216,\n      columnNumber: 7\n    }, this);\n  };\n  const renderProgressReport = () => /*#__PURE__*/_jsxDEV(Grid, {\n    container: true,\n    spacing: 3,\n    children: [/*#__PURE__*/_jsxDEV(Grid, {\n      item: true,\n      xs: 12,\n      md: 6,\n      children: /*#__PURE__*/_jsxDEV(Card, {\n        children: /*#__PURE__*/_jsxDEV(CardContent, {\n          children: [/*#__PURE__*/_jsxDEV(Typography, {\n            variant: \"h6\",\n            gutterBottom: true,\n            children: \"Avanzamento Generale\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 249,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Typography, {\n            children: [\"Metri Totali: \", /*#__PURE__*/_jsxDEV(\"strong\", {\n              children: [reportData.metri_totali, \"m\"]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 250,\n              columnNumber: 39\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 250,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Typography, {\n            children: [\"Metri Posati: \", /*#__PURE__*/_jsxDEV(\"strong\", {\n              children: [reportData.metri_posati, \"m\"]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 251,\n              columnNumber: 39\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 251,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Typography, {\n            children: [\"Metri Rimanenti: \", /*#__PURE__*/_jsxDEV(\"strong\", {\n              children: [reportData.metri_da_posare, \"m\"]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 252,\n              columnNumber: 42\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 252,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Typography, {\n            children: [\"Avanzamento: \", /*#__PURE__*/_jsxDEV(\"strong\", {\n              children: [reportData.percentuale_avanzamento, \"%\"]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 253,\n              columnNumber: 38\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 253,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 248,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 247,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 246,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Grid, {\n      item: true,\n      xs: 12,\n      md: 6,\n      children: /*#__PURE__*/_jsxDEV(Card, {\n        children: /*#__PURE__*/_jsxDEV(CardContent, {\n          children: [/*#__PURE__*/_jsxDEV(Typography, {\n            variant: \"h6\",\n            gutterBottom: true,\n            children: \"Cavi\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 260,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Typography, {\n            children: [\"Totale Cavi: \", /*#__PURE__*/_jsxDEV(\"strong\", {\n              children: reportData.totale_cavi\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 261,\n              columnNumber: 38\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 261,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Typography, {\n            children: [\"Cavi Posati: \", /*#__PURE__*/_jsxDEV(\"strong\", {\n              children: reportData.cavi_posati\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 262,\n              columnNumber: 38\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 262,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Typography, {\n            children: [\"Cavi Rimanenti: \", /*#__PURE__*/_jsxDEV(\"strong\", {\n              children: reportData.cavi_rimanenti\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 263,\n              columnNumber: 41\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 263,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Typography, {\n            children: [\"Percentuale Cavi: \", /*#__PURE__*/_jsxDEV(\"strong\", {\n              children: [reportData.percentuale_cavi, \"%\"]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 264,\n              columnNumber: 43\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 264,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 259,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 258,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 257,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Grid, {\n      item: true,\n      xs: 12,\n      md: 6,\n      children: /*#__PURE__*/_jsxDEV(Card, {\n        children: /*#__PURE__*/_jsxDEV(CardContent, {\n          children: [/*#__PURE__*/_jsxDEV(Typography, {\n            variant: \"h6\",\n            gutterBottom: true,\n            children: \"Performance\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 271,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Typography, {\n            children: [\"Media Giornaliera: \", /*#__PURE__*/_jsxDEV(\"strong\", {\n              children: [reportData.media_giornaliera, \"m/giorno\"]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 272,\n              columnNumber: 44\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 272,\n            columnNumber: 13\n          }, this), reportData.giorni_stimati && /*#__PURE__*/_jsxDEV(_Fragment, {\n            children: [/*#__PURE__*/_jsxDEV(Typography, {\n              children: [\"Giorni Stimati: \", /*#__PURE__*/_jsxDEV(\"strong\", {\n                children: [reportData.giorni_stimati, \" giorni\"]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 275,\n                columnNumber: 45\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 275,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(Typography, {\n              children: [\"Data Completamento: \", /*#__PURE__*/_jsxDEV(\"strong\", {\n                children: reportData.data_completamento\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 276,\n                columnNumber: 49\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 276,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 270,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 269,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 268,\n      columnNumber: 7\n    }, this), reportData.posa_recente && reportData.posa_recente.length > 0 && /*#__PURE__*/_jsxDEV(Grid, {\n      item: true,\n      xs: 12,\n      md: 6,\n      children: /*#__PURE__*/_jsxDEV(Card, {\n        children: /*#__PURE__*/_jsxDEV(CardContent, {\n          children: [/*#__PURE__*/_jsxDEV(Typography, {\n            variant: \"h6\",\n            gutterBottom: true,\n            children: \"Posa Recente\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 286,\n            columnNumber: 15\n          }, this), reportData.posa_recente.slice(0, 5).map((posa, index) => /*#__PURE__*/_jsxDEV(Typography, {\n            children: [posa.data, \": \", /*#__PURE__*/_jsxDEV(\"strong\", {\n              children: [posa.metri, \"m\"]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 289,\n              columnNumber: 32\n            }, this)]\n          }, index, true, {\n            fileName: _jsxFileName,\n            lineNumber: 288,\n            columnNumber: 17\n          }, this))]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 285,\n          columnNumber: 13\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 284,\n        columnNumber: 11\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 283,\n      columnNumber: 9\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 245,\n    columnNumber: 5\n  }, this);\n  const renderBoqReport = () => {\n    var _reportData$cavi_per_, _reportData$bobine_pe;\n    return /*#__PURE__*/_jsxDEV(Grid, {\n      container: true,\n      spacing: 3,\n      children: [/*#__PURE__*/_jsxDEV(Grid, {\n        item: true,\n        xs: 12,\n        children: [/*#__PURE__*/_jsxDEV(Typography, {\n          variant: \"h6\",\n          gutterBottom: true,\n          children: \"Cavi per Tipologia\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 302,\n          columnNumber: 9\n        }, this), /*#__PURE__*/_jsxDEV(Grid, {\n          container: true,\n          spacing: 2,\n          children: (_reportData$cavi_per_ = reportData.cavi_per_tipo) === null || _reportData$cavi_per_ === void 0 ? void 0 : _reportData$cavi_per_.map((cavo, index) => /*#__PURE__*/_jsxDEV(Grid, {\n            item: true,\n            xs: 12,\n            md: 6,\n            lg: 4,\n            children: /*#__PURE__*/_jsxDEV(Card, {\n              children: /*#__PURE__*/_jsxDEV(CardContent, {\n                children: [/*#__PURE__*/_jsxDEV(Typography, {\n                  variant: \"subtitle1\",\n                  children: cavo.tipologia\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 308,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                  variant: \"body2\",\n                  children: [\"Sezione: \", cavo.sezione]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 309,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                  children: [\"Cavi: \", cavo.num_cavi]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 310,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                  children: [\"Metri Teorici: \", cavo.metri_teorici, \"m\"]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 311,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                  children: [\"Metri Reali: \", cavo.metri_reali, \"m\"]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 312,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                  children: [\"Da Posare: \", cavo.metri_da_posare, \"m\"]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 313,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 307,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 306,\n              columnNumber: 15\n            }, this)\n          }, index, false, {\n            fileName: _jsxFileName,\n            lineNumber: 305,\n            columnNumber: 13\n          }, this))\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 303,\n          columnNumber: 9\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 301,\n        columnNumber: 7\n      }, this), /*#__PURE__*/_jsxDEV(Grid, {\n        item: true,\n        xs: 12,\n        children: [/*#__PURE__*/_jsxDEV(Typography, {\n          variant: \"h6\",\n          gutterBottom: true,\n          children: \"Bobine Disponibili\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 322,\n          columnNumber: 9\n        }, this), /*#__PURE__*/_jsxDEV(Grid, {\n          container: true,\n          spacing: 2,\n          children: (_reportData$bobine_pe = reportData.bobine_per_tipo) === null || _reportData$bobine_pe === void 0 ? void 0 : _reportData$bobine_pe.map((bobina, index) => /*#__PURE__*/_jsxDEV(Grid, {\n            item: true,\n            xs: 12,\n            md: 6,\n            lg: 4,\n            children: /*#__PURE__*/_jsxDEV(Card, {\n              children: /*#__PURE__*/_jsxDEV(CardContent, {\n                children: [/*#__PURE__*/_jsxDEV(Typography, {\n                  variant: \"subtitle1\",\n                  children: bobina.tipologia\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 328,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                  variant: \"body2\",\n                  children: [\"Sezione: \", bobina.sezione]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 329,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                  children: [\"Bobine: \", bobina.num_bobine]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 330,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                  children: [\"Metri Disponibili: \", bobina.metri_disponibili, \"m\"]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 331,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 327,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 326,\n              columnNumber: 15\n            }, this)\n          }, index, false, {\n            fileName: _jsxFileName,\n            lineNumber: 325,\n            columnNumber: 13\n          }, this))\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 323,\n          columnNumber: 9\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 321,\n        columnNumber: 7\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 300,\n      columnNumber: 5\n    }, this);\n  };\n  const renderBobineReport = () => {\n    var _reportData$bobine;\n    return /*#__PURE__*/_jsxDEV(Grid, {\n      container: true,\n      spacing: 3,\n      children: /*#__PURE__*/_jsxDEV(Grid, {\n        item: true,\n        xs: 12,\n        children: [/*#__PURE__*/_jsxDEV(Typography, {\n          variant: \"h6\",\n          gutterBottom: true,\n          children: [\"Bobine del Cantiere (\", reportData.totale_bobine, \" totali)\"]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 344,\n          columnNumber: 9\n        }, this), /*#__PURE__*/_jsxDEV(Grid, {\n          container: true,\n          spacing: 2,\n          children: (_reportData$bobine = reportData.bobine) === null || _reportData$bobine === void 0 ? void 0 : _reportData$bobine.map((bobina, index) => /*#__PURE__*/_jsxDEV(Grid, {\n            item: true,\n            xs: 12,\n            md: 6,\n            lg: 4,\n            children: /*#__PURE__*/_jsxDEV(Card, {\n              children: /*#__PURE__*/_jsxDEV(CardContent, {\n                children: [/*#__PURE__*/_jsxDEV(Typography, {\n                  variant: \"subtitle1\",\n                  children: bobina.id_bobina\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 352,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                  variant: \"body2\",\n                  children: [bobina.tipologia, \" - \", bobina.sezione]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 353,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(Chip, {\n                  label: bobina.stato,\n                  color: bobina.stato === 'DISPONIBILE' ? 'success' : 'warning',\n                  size: \"small\",\n                  sx: {\n                    mb: 1\n                  }\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 354,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                  children: [\"Metri Totali: \", bobina.metri_totali, \"m\"]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 360,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                  children: [\"Metri Residui: \", bobina.metri_residui, \"m\"]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 361,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                  children: [\"Metri Utilizzati: \", bobina.metri_utilizzati, \"m\"]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 362,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                  children: [\"Utilizzo: \", bobina.percentuale_utilizzo, \"%\"]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 363,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 351,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 350,\n              columnNumber: 15\n            }, this)\n          }, index, false, {\n            fileName: _jsxFileName,\n            lineNumber: 349,\n            columnNumber: 13\n          }, this))\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 347,\n          columnNumber: 9\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 343,\n        columnNumber: 7\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 342,\n      columnNumber: 5\n    }, this);\n  };\n  const renderBobinaSpecificaReport = () => {\n    var _reportData$bobina, _reportData$bobina2, _reportData$bobina3, _reportData$bobina4, _reportData$bobina5, _reportData$bobina6, _reportData$bobina7, _reportData$bobina8, _reportData$bobina9, _reportData$cavi_asso;\n    return /*#__PURE__*/_jsxDEV(Grid, {\n      container: true,\n      spacing: 3,\n      children: [/*#__PURE__*/_jsxDEV(Grid, {\n        item: true,\n        xs: 12,\n        md: 6,\n        children: /*#__PURE__*/_jsxDEV(Card, {\n          children: /*#__PURE__*/_jsxDEV(CardContent, {\n            children: [/*#__PURE__*/_jsxDEV(Typography, {\n              variant: \"h6\",\n              gutterBottom: true,\n              children: \"Dettagli Bobina\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 378,\n              columnNumber: 13\n            }, this), /*#__PURE__*/_jsxDEV(Typography, {\n              children: [\"ID: \", /*#__PURE__*/_jsxDEV(\"strong\", {\n                children: (_reportData$bobina = reportData.bobina) === null || _reportData$bobina === void 0 ? void 0 : _reportData$bobina.id_bobina\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 379,\n                columnNumber: 29\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 379,\n              columnNumber: 13\n            }, this), /*#__PURE__*/_jsxDEV(Typography, {\n              children: [\"Tipologia: \", /*#__PURE__*/_jsxDEV(\"strong\", {\n                children: (_reportData$bobina2 = reportData.bobina) === null || _reportData$bobina2 === void 0 ? void 0 : _reportData$bobina2.tipologia\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 380,\n                columnNumber: 36\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 380,\n              columnNumber: 13\n            }, this), /*#__PURE__*/_jsxDEV(Typography, {\n              children: [\"Sezione: \", /*#__PURE__*/_jsxDEV(\"strong\", {\n                children: (_reportData$bobina3 = reportData.bobina) === null || _reportData$bobina3 === void 0 ? void 0 : _reportData$bobina3.sezione\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 381,\n                columnNumber: 34\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 381,\n              columnNumber: 13\n            }, this), /*#__PURE__*/_jsxDEV(Chip, {\n              label: (_reportData$bobina4 = reportData.bobina) === null || _reportData$bobina4 === void 0 ? void 0 : _reportData$bobina4.stato,\n              color: ((_reportData$bobina5 = reportData.bobina) === null || _reportData$bobina5 === void 0 ? void 0 : _reportData$bobina5.stato) === 'DISPONIBILE' ? 'success' : 'warning',\n              sx: {\n                my: 1\n              }\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 382,\n              columnNumber: 13\n            }, this), /*#__PURE__*/_jsxDEV(Typography, {\n              children: [\"Metri Totali: \", /*#__PURE__*/_jsxDEV(\"strong\", {\n                children: [(_reportData$bobina6 = reportData.bobina) === null || _reportData$bobina6 === void 0 ? void 0 : _reportData$bobina6.metri_totali, \"m\"]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 387,\n                columnNumber: 39\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 387,\n              columnNumber: 13\n            }, this), /*#__PURE__*/_jsxDEV(Typography, {\n              children: [\"Metri Residui: \", /*#__PURE__*/_jsxDEV(\"strong\", {\n                children: [(_reportData$bobina7 = reportData.bobina) === null || _reportData$bobina7 === void 0 ? void 0 : _reportData$bobina7.metri_residui, \"m\"]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 388,\n                columnNumber: 40\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 388,\n              columnNumber: 13\n            }, this), /*#__PURE__*/_jsxDEV(Typography, {\n              children: [\"Metri Utilizzati: \", /*#__PURE__*/_jsxDEV(\"strong\", {\n                children: [(_reportData$bobina8 = reportData.bobina) === null || _reportData$bobina8 === void 0 ? void 0 : _reportData$bobina8.metri_utilizzati, \"m\"]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 389,\n                columnNumber: 43\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 389,\n              columnNumber: 13\n            }, this), /*#__PURE__*/_jsxDEV(Typography, {\n              children: [\"Utilizzo: \", /*#__PURE__*/_jsxDEV(\"strong\", {\n                children: [(_reportData$bobina9 = reportData.bobina) === null || _reportData$bobina9 === void 0 ? void 0 : _reportData$bobina9.percentuale_utilizzo, \"%\"]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 390,\n                columnNumber: 35\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 390,\n              columnNumber: 13\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 377,\n            columnNumber: 11\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 376,\n          columnNumber: 9\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 375,\n        columnNumber: 7\n      }, this), /*#__PURE__*/_jsxDEV(Grid, {\n        item: true,\n        xs: 12,\n        md: 6,\n        children: /*#__PURE__*/_jsxDEV(Card, {\n          children: /*#__PURE__*/_jsxDEV(CardContent, {\n            children: [/*#__PURE__*/_jsxDEV(Typography, {\n              variant: \"h6\",\n              gutterBottom: true,\n              children: [\"Cavi Associati (\", reportData.totale_cavi, \")\"]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 398,\n              columnNumber: 13\n            }, this), /*#__PURE__*/_jsxDEV(Box, {\n              sx: {\n                maxHeight: 300,\n                overflow: 'auto'\n              },\n              children: (_reportData$cavi_asso = reportData.cavi_associati) === null || _reportData$cavi_asso === void 0 ? void 0 : _reportData$cavi_asso.map((cavo, index) => /*#__PURE__*/_jsxDEV(Box, {\n                sx: {\n                  mb: 1,\n                  p: 1,\n                  border: '1px solid #e0e0e0',\n                  borderRadius: 1\n                },\n                children: [/*#__PURE__*/_jsxDEV(Typography, {\n                  variant: \"body2\",\n                  children: /*#__PURE__*/_jsxDEV(\"strong\", {\n                    children: cavo.id_cavo\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 404,\n                    columnNumber: 47\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 404,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                  variant: \"caption\",\n                  children: [cavo.sistema, \" - \", cavo.utility, \" - \", cavo.tipologia]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 405,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                  variant: \"caption\",\n                  display: \"block\",\n                  children: [\"Teorici: \", cavo.metri_teorici, \"m | Reali: \", cavo.metri_reali, \"m | Stato: \", cavo.stato]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 408,\n                  columnNumber: 19\n                }, this)]\n              }, index, true, {\n                fileName: _jsxFileName,\n                lineNumber: 403,\n                columnNumber: 17\n              }, this))\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 401,\n              columnNumber: 13\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 397,\n            columnNumber: 11\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 396,\n          columnNumber: 9\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 395,\n        columnNumber: 7\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 374,\n      columnNumber: 5\n    }, this);\n  };\n  const renderPosaPeriodoReport = () => {\n    var _reportData$posa_gior;\n    return /*#__PURE__*/_jsxDEV(Grid, {\n      container: true,\n      spacing: 3,\n      children: [/*#__PURE__*/_jsxDEV(Grid, {\n        item: true,\n        xs: 12,\n        md: 4,\n        children: /*#__PURE__*/_jsxDEV(Card, {\n          children: /*#__PURE__*/_jsxDEV(CardContent, {\n            children: [/*#__PURE__*/_jsxDEV(Typography, {\n              variant: \"h6\",\n              gutterBottom: true,\n              children: \"Statistiche Periodo\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 425,\n              columnNumber: 13\n            }, this), /*#__PURE__*/_jsxDEV(Typography, {\n              children: [\"Periodo: \", /*#__PURE__*/_jsxDEV(\"strong\", {\n                children: [reportData.data_inizio, \" - \", reportData.data_fine]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 426,\n                columnNumber: 34\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 426,\n              columnNumber: 13\n            }, this), /*#__PURE__*/_jsxDEV(Typography, {\n              children: [\"Totale Metri: \", /*#__PURE__*/_jsxDEV(\"strong\", {\n                children: [reportData.totale_metri_periodo, \"m\"]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 427,\n                columnNumber: 39\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 427,\n              columnNumber: 13\n            }, this), /*#__PURE__*/_jsxDEV(Typography, {\n              children: [\"Giorni Attivi: \", /*#__PURE__*/_jsxDEV(\"strong\", {\n                children: reportData.giorni_attivi\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 428,\n                columnNumber: 40\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 428,\n              columnNumber: 13\n            }, this), /*#__PURE__*/_jsxDEV(Typography, {\n              children: [\"Media Giornaliera: \", /*#__PURE__*/_jsxDEV(\"strong\", {\n                children: [reportData.media_giornaliera, \"m/giorno\"]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 429,\n                columnNumber: 44\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 429,\n              columnNumber: 13\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 424,\n            columnNumber: 11\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 423,\n          columnNumber: 9\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 422,\n        columnNumber: 7\n      }, this), /*#__PURE__*/_jsxDEV(Grid, {\n        item: true,\n        xs: 12,\n        md: 8,\n        children: /*#__PURE__*/_jsxDEV(Card, {\n          children: /*#__PURE__*/_jsxDEV(CardContent, {\n            children: [/*#__PURE__*/_jsxDEV(Typography, {\n              variant: \"h6\",\n              gutterBottom: true,\n              children: \"Posa Giornaliera\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 437,\n              columnNumber: 13\n            }, this), /*#__PURE__*/_jsxDEV(Box, {\n              sx: {\n                maxHeight: 300,\n                overflow: 'auto'\n              },\n              children: (_reportData$posa_gior = reportData.posa_giornaliera) === null || _reportData$posa_gior === void 0 ? void 0 : _reportData$posa_gior.map((posa, index) => /*#__PURE__*/_jsxDEV(Box, {\n                sx: {\n                  display: 'flex',\n                  justifyContent: 'space-between',\n                  py: 0.5\n                },\n                children: [/*#__PURE__*/_jsxDEV(Typography, {\n                  children: posa.data\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 441,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                  children: /*#__PURE__*/_jsxDEV(\"strong\", {\n                    children: [posa.metri, \"m\"]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 442,\n                    columnNumber: 31\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 442,\n                  columnNumber: 19\n                }, this)]\n              }, index, true, {\n                fileName: _jsxFileName,\n                lineNumber: 440,\n                columnNumber: 17\n              }, this))\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 438,\n              columnNumber: 13\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 436,\n            columnNumber: 11\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 435,\n          columnNumber: 9\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 434,\n        columnNumber: 7\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 421,\n      columnNumber: 5\n    }, this);\n  };\n  const renderCaviStatoReport = () => {\n    var _reportData$cavi_per_2;\n    return /*#__PURE__*/_jsxDEV(Grid, {\n      container: true,\n      spacing: 3,\n      children: /*#__PURE__*/_jsxDEV(Grid, {\n        item: true,\n        xs: 12,\n        children: [/*#__PURE__*/_jsxDEV(Typography, {\n          variant: \"h6\",\n          gutterBottom: true,\n          children: \"Cavi per Stato di Installazione\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 455,\n          columnNumber: 9\n        }, this), /*#__PURE__*/_jsxDEV(Grid, {\n          container: true,\n          spacing: 2,\n          children: (_reportData$cavi_per_2 = reportData.cavi_per_stato) === null || _reportData$cavi_per_2 === void 0 ? void 0 : _reportData$cavi_per_2.map((stato, index) => /*#__PURE__*/_jsxDEV(Grid, {\n            item: true,\n            xs: 12,\n            md: 6,\n            lg: 3,\n            children: /*#__PURE__*/_jsxDEV(Card, {\n              children: /*#__PURE__*/_jsxDEV(CardContent, {\n                children: [/*#__PURE__*/_jsxDEV(Typography, {\n                  variant: \"h6\",\n                  gutterBottom: true,\n                  children: /*#__PURE__*/_jsxDEV(Chip, {\n                    label: stato.stato,\n                    color: stato.stato === 'Installato' ? 'success' : 'warning',\n                    sx: {\n                      mb: 1\n                    }\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 462,\n                    columnNumber: 21\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 461,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                  children: [\"Numero Cavi: \", /*#__PURE__*/_jsxDEV(\"strong\", {\n                    children: stato.num_cavi\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 468,\n                    columnNumber: 44\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 468,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                  children: [\"Metri Teorici: \", /*#__PURE__*/_jsxDEV(\"strong\", {\n                    children: [stato.metri_teorici, \"m\"]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 469,\n                    columnNumber: 46\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 469,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                  children: [\"Metri Reali: \", /*#__PURE__*/_jsxDEV(\"strong\", {\n                    children: [stato.metri_reali, \"m\"]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 470,\n                    columnNumber: 44\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 470,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 460,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 459,\n              columnNumber: 15\n            }, this)\n          }, index, false, {\n            fileName: _jsxFileName,\n            lineNumber: 458,\n            columnNumber: 13\n          }, this))\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 456,\n          columnNumber: 9\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 454,\n        columnNumber: 7\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 453,\n      columnNumber: 5\n    }, this);\n  };\n  const renderDialog = () => /*#__PURE__*/_jsxDEV(Dialog, {\n    open: openDialog,\n    onClose: handleCloseDialog,\n    maxWidth: \"sm\",\n    fullWidth: true,\n    children: [/*#__PURE__*/_jsxDEV(DialogTitle, {\n      children: selectedReport === null || selectedReport === void 0 ? void 0 : selectedReport.title\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 482,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(DialogContent, {\n      children: [error && /*#__PURE__*/_jsxDEV(Alert, {\n        severity: \"error\",\n        sx: {\n          mb: 2\n        },\n        children: error\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 487,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(Grid, {\n        container: true,\n        spacing: 2,\n        sx: {\n          mt: 1\n        },\n        children: [/*#__PURE__*/_jsxDEV(Grid, {\n          item: true,\n          xs: 12,\n          children: /*#__PURE__*/_jsxDEV(FormControl, {\n            fullWidth: true,\n            children: [/*#__PURE__*/_jsxDEV(InputLabel, {\n              children: \"Formato\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 495,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Select, {\n              value: formData.formato,\n              label: \"Formato\",\n              onChange: e => setFormData({\n                ...formData,\n                formato: e.target.value\n              }),\n              children: [/*#__PURE__*/_jsxDEV(MenuItem, {\n                value: \"video\",\n                children: \"Visualizza a schermo\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 501,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(MenuItem, {\n                value: \"pdf\",\n                children: \"Download PDF\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 502,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(MenuItem, {\n                value: \"excel\",\n                children: \"Download Excel\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 503,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 496,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 494,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 493,\n          columnNumber: 11\n        }, this), dialogType === 'bobina-specifica' && /*#__PURE__*/_jsxDEV(Grid, {\n          item: true,\n          xs: 12,\n          children: /*#__PURE__*/_jsxDEV(TextField, {\n            fullWidth: true,\n            label: \"ID Bobina\",\n            value: formData.id_bobina,\n            onChange: e => setFormData({\n              ...formData,\n              id_bobina: e.target.value\n            }),\n            placeholder: \"Es: 1, 2, A, B...\",\n            helperText: \"Inserisci solo la parte finale dell'ID (es: 1 per C1_B1)\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 510,\n            columnNumber: 15\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 509,\n          columnNumber: 13\n        }, this), dialogType === 'posa-periodo' && /*#__PURE__*/_jsxDEV(_Fragment, {\n          children: [/*#__PURE__*/_jsxDEV(Grid, {\n            item: true,\n            xs: 6,\n            children: /*#__PURE__*/_jsxDEV(TextField, {\n              fullWidth: true,\n              type: \"date\",\n              label: \"Data Inizio\",\n              value: formData.data_inizio,\n              onChange: e => setFormData({\n                ...formData,\n                data_inizio: e.target.value\n              }),\n              InputLabelProps: {\n                shrink: true\n              }\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 524,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 523,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(Grid, {\n            item: true,\n            xs: 6,\n            children: /*#__PURE__*/_jsxDEV(TextField, {\n              fullWidth: true,\n              type: \"date\",\n              label: \"Data Fine\",\n              value: formData.data_fine,\n              onChange: e => setFormData({\n                ...formData,\n                data_fine: e.target.value\n              }),\n              InputLabelProps: {\n                shrink: true\n              }\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 534,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 533,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 492,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 485,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(DialogActions, {\n      children: [/*#__PURE__*/_jsxDEV(Button, {\n        onClick: handleCloseDialog,\n        children: \"Annulla\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 548,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Button, {\n        onClick: handleGenerateReport,\n        variant: \"contained\",\n        disabled: loading,\n        startIcon: loading ? /*#__PURE__*/_jsxDEV(CircularProgress, {\n          size: 20\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 553,\n          columnNumber: 32\n        }, this) : /*#__PURE__*/_jsxDEV(VisibilityIcon, {}, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 553,\n          columnNumber: 65\n        }, this),\n        children: loading ? 'Generazione...' : 'Genera Report'\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 549,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 547,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 481,\n    columnNumber: 5\n  }, this);\n  return /*#__PURE__*/_jsxDEV(Box, {\n    sx: {\n      p: 3\n    },\n    children: [/*#__PURE__*/_jsxDEV(Box, {\n      sx: {\n        display: 'flex',\n        justifyContent: 'space-between',\n        alignItems: 'center',\n        mb: 3\n      },\n      children: [/*#__PURE__*/_jsxDEV(Box, {\n        sx: {\n          display: 'flex',\n          alignItems: 'center',\n          gap: 2\n        },\n        children: [/*#__PURE__*/_jsxDEV(IconButton, {\n          onClick: () => navigate(-1),\n          color: \"primary\",\n          children: /*#__PURE__*/_jsxDEV(ArrowBackIcon, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 567,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 566,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Typography, {\n          variant: \"h4\",\n          component: \"h1\",\n          children: \"Report e Analytics\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 569,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 565,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(AdminHomeButton, {}, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 573,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 564,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Alert, {\n      severity: \"info\",\n      sx: {\n        mb: 3\n      },\n      children: \"Seleziona il tipo di report che desideri generare. Ogni report offre analisi specifiche per monitorare l'avanzamento del progetto e ottimizzare le operazioni.\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 577,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Grid, {\n      container: true,\n      spacing: 3,\n      children: reportTypes.map(report => /*#__PURE__*/_jsxDEV(Grid, {\n        item: true,\n        xs: 12,\n        md: 6,\n        lg: 4,\n        children: /*#__PURE__*/_jsxDEV(Card, {\n          sx: {\n            height: '100%',\n            display: 'flex',\n            flexDirection: 'column',\n            cursor: 'pointer',\n            transition: 'transform 0.2s, box-shadow 0.2s',\n            '&:hover': {\n              transform: 'translateY(-4px)',\n              boxShadow: 4\n            }\n          },\n          onClick: () => handleReportSelect(report),\n          children: [/*#__PURE__*/_jsxDEV(CardContent, {\n            sx: {\n              flexGrow: 1\n            },\n            children: [/*#__PURE__*/_jsxDEV(Box, {\n              sx: {\n                display: 'flex',\n                alignItems: 'center',\n                mb: 2\n              },\n              children: [/*#__PURE__*/_jsxDEV(Box, {\n                sx: {\n                  color: `${report.color}.main`,\n                  mr: 2\n                },\n                children: report.icon\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 602,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                variant: \"h6\",\n                component: \"h2\",\n                children: report.title\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 605,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 601,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(Typography, {\n              variant: \"body2\",\n              color: \"text.secondary\",\n              sx: {\n                mb: 2\n              },\n              children: report.description\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 610,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(Box, {\n              children: report.features.map((feature, index) => /*#__PURE__*/_jsxDEV(Chip, {\n                label: feature,\n                size: \"small\",\n                variant: \"outlined\",\n                sx: {\n                  mr: 0.5,\n                  mb: 0.5\n                }\n              }, index, false, {\n                fileName: _jsxFileName,\n                lineNumber: 616,\n                columnNumber: 21\n              }, this))\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 614,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 600,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(CardActions, {\n            children: /*#__PURE__*/_jsxDEV(Button, {\n              size: \"small\",\n              color: report.color,\n              startIcon: /*#__PURE__*/_jsxDEV(AssessmentIcon, {}, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 631,\n                columnNumber: 30\n              }, this),\n              fullWidth: true,\n              children: \"Genera Report\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 628,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 627,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 586,\n          columnNumber: 13\n        }, this)\n      }, report.id, false, {\n        fileName: _jsxFileName,\n        lineNumber: 585,\n        columnNumber: 11\n      }, this))\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 583,\n      columnNumber: 7\n    }, this), renderReportContent(), renderDialog()]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 562,\n    columnNumber: 5\n  }, this);\n};\n_s(ReportCaviPageNew, \"HffKKuo4NWxac8n/kfIDDf02L24=\", false, function () {\n  return [useNavigate, useParams, useAuth];\n});\n_c = ReportCaviPageNew;\nexport default ReportCaviPageNew;\nvar _c;\n$RefreshReg$(_c, \"ReportCaviPageNew\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "Box", "Typography", "Paper", "Grid", "Card", "<PERSON><PERSON><PERSON><PERSON>", "CardActions", "<PERSON><PERSON>", "Chip", "<PERSON><PERSON>", "CircularProgress", "Divider", "IconButton", "<PERSON><PERSON><PERSON>", "Dialog", "DialogTitle", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "DialogActions", "FormControl", "InputLabel", "Select", "MenuItem", "TextField", "Accordion", "AccordionSummary", "AccordionDetails", "Assessment", "AssessmentIcon", "<PERSON><PERSON><PERSON>", "BarChartIcon", "<PERSON><PERSON><PERSON>", "PieChartIcon", "Timeline", "TimelineIcon", "List", "ListIcon", "Download", "DownloadIcon", "Visibility", "VisibilityIcon", "Refresh", "RefreshIcon", "ArrowBack", "ArrowBackIcon", "DateRange", "DateRangeIcon", "Cable", "CableIcon", "Inventory", "InventoryIcon", "ExpandMore", "ExpandMoreIcon", "useNavigate", "useParams", "useAuth", "AdminHomeButton", "reportService", "jsxDEV", "_jsxDEV", "Fragment", "_Fragment", "ReportCaviPageNew", "_s", "navigate", "cantiereId", "user", "loading", "setLoading", "error", "setError", "reportData", "setReportData", "selectedReport", "setSelectedReport", "openDialog", "setOpenDialog", "dialogType", "setDialogType", "formData", "setFormData", "formato", "data_inizio", "data_fine", "id_bobina", "reportTypes", "id", "title", "description", "icon", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "color", "features", "handleReportSelect", "reportType", "today", "Date", "lastM<PERSON>h", "setMonth", "getMonth", "toISOString", "split", "handleGenerateReport", "response", "getProgressReport", "getBillOfQuantities", "getBobineReport", "getBobinaReport", "getPosaPerPeriodoReport", "getCaviStatoReport", "Error", "content", "file_url", "window", "open", "err", "console", "detail", "message", "handleCloseDialog", "renderReportContent", "sx", "p", "mt", "children", "display", "justifyContent", "alignItems", "mb", "variant", "nome_cantiere", "startIcon", "onClick", "size", "renderProgressReport", "renderBoqReport", "renderBobineReport", "renderBobinaSpecificaReport", "renderPosaPeriodoReport", "renderCaviStatoReport", "container", "spacing", "item", "xs", "md", "gutterBottom", "metri_totali", "metri_posati", "metri_da_posare", "percentuale_avanzamento", "totale_cavi", "cavi_posati", "cavi_rimanenti", "percentuale_cavi", "media_giornaliera", "giorni_stimati", "data_completamento", "posa_recente", "length", "slice", "map", "posa", "index", "data", "metri", "_reportData$cavi_per_", "_reportData$bobine_pe", "cavi_per_tipo", "cavo", "lg", "tipologia", "sezione", "num_cavi", "metri_te<PERSON>ci", "metri_reali", "bobine_per_tipo", "bobina", "num_bobine", "metri_disponibili", "_reportData$bobine", "totale_bobine", "bobine", "label", "stato", "metri_residui", "<PERSON><PERSON>_util<PERSON><PERSON><PERSON>", "percentuale_utilizzo", "_reportData$bobina", "_reportData$bobina2", "_reportData$bobina3", "_reportData$bobina4", "_reportData$bobina5", "_reportData$bobina6", "_reportData$bobina7", "_reportData$bobina8", "_reportData$bobina9", "_reportData$cavi_asso", "my", "maxHeight", "overflow", "cavi_associati", "border", "borderRadius", "id_cavo", "sistema", "utility", "_reportData$posa_gior", "totale_metri_periodo", "giorni_attivi", "posa_giornal<PERSON>", "py", "_reportData$cavi_per_2", "cavi_per_stato", "renderDialog", "onClose", "max<PERSON><PERSON><PERSON>", "fullWidth", "severity", "value", "onChange", "e", "target", "placeholder", "helperText", "type", "InputLabelProps", "shrink", "disabled", "gap", "component", "report", "height", "flexDirection", "cursor", "transition", "transform", "boxShadow", "flexGrow", "mr", "feature", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/PycharmProjects/CMS/webapp/frontend/src/pages/cavi/ReportCaviPageNew.js"], "sourcesContent": ["import React, { useState, useEffect } from 'react';\nimport {\n  Box,\n  Typography,\n  Paper,\n  Grid,\n  Card,\n  CardContent,\n  CardActions,\n  Button,\n  Chip,\n  Alert,\n  CircularProgress,\n  Divider,\n  IconButton,\n  Tooltip,\n  Dialog,\n  DialogTitle,\n  DialogContent,\n  DialogActions,\n  FormControl,\n  InputLabel,\n  Select,\n  MenuItem,\n  TextField,\n  Accordion,\n  AccordionSummary,\n  AccordionDetails\n} from '@mui/material';\nimport {\n  Assessment as AssessmentIcon,\n  Bar<PERSON>hart as BarChartIcon,\n  <PERSON>Chart as PieChartIcon,\n  Timeline as TimelineIcon,\n  List as ListIcon,\n  Download as DownloadIcon,\n  Visibility as VisibilityIcon,\n  Refresh as RefreshIcon,\n  ArrowBack as ArrowBackIcon,\n  DateRange as DateRangeIcon,\n  Cable as CableIcon,\n  Inventory as InventoryIcon,\n  ExpandMore as ExpandMoreIcon\n} from '@mui/icons-material';\nimport { useNavigate, useParams } from 'react-router-dom';\nimport { useAuth } from '../../context/AuthContext';\nimport AdminHomeButton from '../../components/common/AdminHomeButton';\nimport reportService from '../../services/reportService';\n\nconst ReportCaviPageNew = () => {\n  const navigate = useNavigate();\n  const { cantiereId } = useParams();\n  const { user } = useAuth();\n\n  const [loading, setLoading] = useState(false);\n  const [error, setError] = useState(null);\n  const [reportData, setReportData] = useState(null);\n  const [selectedReport, setSelectedReport] = useState(null);\n  const [openDialog, setOpenDialog] = useState(false);\n  const [dialogType, setDialogType] = useState('');\n  const [formData, setFormData] = useState({\n    formato: 'video',\n    data_inizio: '',\n    data_fine: '',\n    id_bobina: ''\n  });\n\n  // Configurazione dei report disponibili\n  const reportTypes = [\n    {\n      id: 'progress',\n      title: 'Report Avanzamento',\n      description: 'Panoramica completa dell\\'avanzamento dei lavori con metriche di performance e previsioni',\n      icon: <AssessmentIcon />,\n      color: 'primary',\n      features: ['Metri posati vs teorici', 'Percentuale completamento', 'Previsioni timeline', 'Performance giornaliera']\n    },\n    {\n      id: 'boq',\n      title: 'Bill of Quantities',\n      description: 'Distinta materiali dettagliata con analisi dei consumi e disponibilità',\n      icon: <ListIcon />,\n      color: 'secondary',\n      features: ['Materiali per tipologia', 'Consumi vs disponibilità', 'Previsioni acquisti', 'Analisi costi']\n    },\n    {\n      id: 'bobine',\n      title: 'Report Utilizzo Bobine',\n      description: 'Analisi completa dell\\'utilizzo delle bobine con efficienza e sprechi',\n      icon: <InventoryIcon />,\n      color: 'success',\n      features: ['Utilizzo per bobina', 'Efficienza materiali', 'Bobine disponibili', 'Analisi sprechi']\n    },\n    {\n      id: 'bobina-specifica',\n      title: 'Report Bobina Specifica',\n      description: 'Dettaglio approfondito di una singola bobina con tutti i cavi associati',\n      icon: <CableIcon />,\n      color: 'info',\n      features: ['Dettaglio bobina', 'Cavi associati', 'Utilizzo specifico', 'Storico operazioni']\n    },\n    {\n      id: 'posa-periodo',\n      title: 'Report Posa per Periodo',\n      description: 'Analisi temporale della posa con trend e pattern di lavoro',\n      icon: <TimelineIcon />,\n      color: 'warning',\n      features: ['Trend temporali', 'Performance periodiche', 'Analisi stagionali', 'Produttività team']\n    },\n    {\n      id: 'cavi-stato',\n      title: 'Report Cavi per Stato',\n      description: 'Classificazione dei cavi per stato di installazione con statistiche dettagliate',\n      icon: <BarChartIcon />,\n      color: 'error',\n      features: ['Cavi per stato', 'Statistiche installazione', 'Problematiche', 'Azioni richieste']\n    }\n  ];\n\n  const handleReportSelect = (reportType) => {\n    setSelectedReport(reportType);\n    setDialogType(reportType.id);\n\n    // Imposta valori di default per alcuni report\n    if (reportType.id === 'posa-periodo') {\n      const today = new Date();\n      const lastMonth = new Date();\n      lastMonth.setMonth(today.getMonth() - 1);\n\n      setFormData({\n        ...formData,\n        data_inizio: lastMonth.toISOString().split('T')[0],\n        data_fine: today.toISOString().split('T')[0]\n      });\n    }\n\n    setOpenDialog(true);\n  };\n\n  const handleGenerateReport = async () => {\n    try {\n      setLoading(true);\n      setError(null);\n\n      let response;\n\n      switch (dialogType) {\n        case 'progress':\n          response = await reportService.getProgressReport(cantiereId, formData.formato);\n          break;\n        case 'boq':\n          response = await reportService.getBillOfQuantities(cantiereId, formData.formato);\n          break;\n        case 'bobine':\n          response = await reportService.getBobineReport(cantiereId, formData.formato);\n          break;\n        case 'bobina-specifica':\n          if (!formData.id_bobina) {\n            setError('Inserisci l\\'ID della bobina');\n            return;\n          }\n          response = await reportService.getBobinaReport(cantiereId, formData.id_bobina, formData.formato);\n          break;\n        case 'posa-periodo':\n          if (!formData.data_inizio || !formData.data_fine) {\n            setError('Seleziona le date di inizio e fine periodo');\n            return;\n          }\n          response = await reportService.getPosaPerPeriodoReport(\n            cantiereId,\n            formData.data_inizio,\n            formData.data_fine,\n            formData.formato\n          );\n          break;\n        case 'cavi-stato':\n          response = await reportService.getCaviStatoReport(cantiereId, formData.formato);\n          break;\n        default:\n          throw new Error('Tipo di report non riconosciuto');\n      }\n\n      if (formData.formato === 'video') {\n        setReportData(response.content);\n      } else {\n        // Per PDF/Excel, apri il link di download\n        if (response.file_url) {\n          window.open(response.file_url, '_blank');\n        }\n      }\n\n      setOpenDialog(false);\n    } catch (err) {\n      console.error('Errore nella generazione del report:', err);\n      setError(err.detail || err.message || 'Errore durante la generazione del report');\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  const handleCloseDialog = () => {\n    setOpenDialog(false);\n    setError(null);\n    setFormData({\n      formato: 'video',\n      data_inizio: '',\n      data_fine: '',\n      id_bobina: ''\n    });\n  };\n\n  const renderReportContent = () => {\n    if (!reportData) return null;\n\n    return (\n      <Paper sx={{ p: 3, mt: 3 }}>\n        <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 2 }}>\n          <Typography variant=\"h6\">\n            {selectedReport?.title} - {reportData.nome_cantiere}\n          </Typography>\n          <Button\n            startIcon={<RefreshIcon />}\n            onClick={() => setReportData(null)}\n            variant=\"outlined\"\n            size=\"small\"\n          >\n            Nuovo Report\n          </Button>\n        </Box>\n\n        <Divider sx={{ mb: 3 }} />\n\n        {/* Renderizza il contenuto specifico del report */}\n        {dialogType === 'progress' && renderProgressReport()}\n        {dialogType === 'boq' && renderBoqReport()}\n        {dialogType === 'bobine' && renderBobineReport()}\n        {dialogType === 'bobina-specifica' && renderBobinaSpecificaReport()}\n        {dialogType === 'posa-periodo' && renderPosaPeriodoReport()}\n        {dialogType === 'cavi-stato' && renderCaviStatoReport()}\n      </Paper>\n    );\n  };\n\n  const renderProgressReport = () => (\n    <Grid container spacing={3}>\n      <Grid item xs={12} md={6}>\n        <Card>\n          <CardContent>\n            <Typography variant=\"h6\" gutterBottom>Avanzamento Generale</Typography>\n            <Typography>Metri Totali: <strong>{reportData.metri_totali}m</strong></Typography>\n            <Typography>Metri Posati: <strong>{reportData.metri_posati}m</strong></Typography>\n            <Typography>Metri Rimanenti: <strong>{reportData.metri_da_posare}m</strong></Typography>\n            <Typography>Avanzamento: <strong>{reportData.percentuale_avanzamento}%</strong></Typography>\n          </CardContent>\n        </Card>\n      </Grid>\n      <Grid item xs={12} md={6}>\n        <Card>\n          <CardContent>\n            <Typography variant=\"h6\" gutterBottom>Cavi</Typography>\n            <Typography>Totale Cavi: <strong>{reportData.totale_cavi}</strong></Typography>\n            <Typography>Cavi Posati: <strong>{reportData.cavi_posati}</strong></Typography>\n            <Typography>Cavi Rimanenti: <strong>{reportData.cavi_rimanenti}</strong></Typography>\n            <Typography>Percentuale Cavi: <strong>{reportData.percentuale_cavi}%</strong></Typography>\n          </CardContent>\n        </Card>\n      </Grid>\n      <Grid item xs={12} md={6}>\n        <Card>\n          <CardContent>\n            <Typography variant=\"h6\" gutterBottom>Performance</Typography>\n            <Typography>Media Giornaliera: <strong>{reportData.media_giornaliera}m/giorno</strong></Typography>\n            {reportData.giorni_stimati && (\n              <>\n                <Typography>Giorni Stimati: <strong>{reportData.giorni_stimati} giorni</strong></Typography>\n                <Typography>Data Completamento: <strong>{reportData.data_completamento}</strong></Typography>\n              </>\n            )}\n          </CardContent>\n        </Card>\n      </Grid>\n      {reportData.posa_recente && reportData.posa_recente.length > 0 && (\n        <Grid item xs={12} md={6}>\n          <Card>\n            <CardContent>\n              <Typography variant=\"h6\" gutterBottom>Posa Recente</Typography>\n              {reportData.posa_recente.slice(0, 5).map((posa, index) => (\n                <Typography key={index}>\n                  {posa.data}: <strong>{posa.metri}m</strong>\n                </Typography>\n              ))}\n            </CardContent>\n          </Card>\n        </Grid>\n      )}\n    </Grid>\n  );\n\n  const renderBoqReport = () => (\n    <Grid container spacing={3}>\n      <Grid item xs={12}>\n        <Typography variant=\"h6\" gutterBottom>Cavi per Tipologia</Typography>\n        <Grid container spacing={2}>\n          {reportData.cavi_per_tipo?.map((cavo, index) => (\n            <Grid item xs={12} md={6} lg={4} key={index}>\n              <Card>\n                <CardContent>\n                  <Typography variant=\"subtitle1\">{cavo.tipologia}</Typography>\n                  <Typography variant=\"body2\">Sezione: {cavo.sezione}</Typography>\n                  <Typography>Cavi: {cavo.num_cavi}</Typography>\n                  <Typography>Metri Teorici: {cavo.metri_teorici}m</Typography>\n                  <Typography>Metri Reali: {cavo.metri_reali}m</Typography>\n                  <Typography>Da Posare: {cavo.metri_da_posare}m</Typography>\n                </CardContent>\n              </Card>\n            </Grid>\n          ))}\n        </Grid>\n      </Grid>\n\n      <Grid item xs={12}>\n        <Typography variant=\"h6\" gutterBottom>Bobine Disponibili</Typography>\n        <Grid container spacing={2}>\n          {reportData.bobine_per_tipo?.map((bobina, index) => (\n            <Grid item xs={12} md={6} lg={4} key={index}>\n              <Card>\n                <CardContent>\n                  <Typography variant=\"subtitle1\">{bobina.tipologia}</Typography>\n                  <Typography variant=\"body2\">Sezione: {bobina.sezione}</Typography>\n                  <Typography>Bobine: {bobina.num_bobine}</Typography>\n                  <Typography>Metri Disponibili: {bobina.metri_disponibili}m</Typography>\n                </CardContent>\n              </Card>\n            </Grid>\n          ))}\n        </Grid>\n      </Grid>\n    </Grid>\n  );\n\n  const renderBobineReport = () => (\n    <Grid container spacing={3}>\n      <Grid item xs={12}>\n        <Typography variant=\"h6\" gutterBottom>\n          Bobine del Cantiere ({reportData.totale_bobine} totali)\n        </Typography>\n        <Grid container spacing={2}>\n          {reportData.bobine?.map((bobina, index) => (\n            <Grid item xs={12} md={6} lg={4} key={index}>\n              <Card>\n                <CardContent>\n                  <Typography variant=\"subtitle1\">{bobina.id_bobina}</Typography>\n                  <Typography variant=\"body2\">{bobina.tipologia} - {bobina.sezione}</Typography>\n                  <Chip\n                    label={bobina.stato}\n                    color={bobina.stato === 'DISPONIBILE' ? 'success' : 'warning'}\n                    size=\"small\"\n                    sx={{ mb: 1 }}\n                  />\n                  <Typography>Metri Totali: {bobina.metri_totali}m</Typography>\n                  <Typography>Metri Residui: {bobina.metri_residui}m</Typography>\n                  <Typography>Metri Utilizzati: {bobina.metri_utilizzati}m</Typography>\n                  <Typography>Utilizzo: {bobina.percentuale_utilizzo}%</Typography>\n                </CardContent>\n              </Card>\n            </Grid>\n          ))}\n        </Grid>\n      </Grid>\n    </Grid>\n  );\n\n  const renderBobinaSpecificaReport = () => (\n    <Grid container spacing={3}>\n      <Grid item xs={12} md={6}>\n        <Card>\n          <CardContent>\n            <Typography variant=\"h6\" gutterBottom>Dettagli Bobina</Typography>\n            <Typography>ID: <strong>{reportData.bobina?.id_bobina}</strong></Typography>\n            <Typography>Tipologia: <strong>{reportData.bobina?.tipologia}</strong></Typography>\n            <Typography>Sezione: <strong>{reportData.bobina?.sezione}</strong></Typography>\n            <Chip\n              label={reportData.bobina?.stato}\n              color={reportData.bobina?.stato === 'DISPONIBILE' ? 'success' : 'warning'}\n              sx={{ my: 1 }}\n            />\n            <Typography>Metri Totali: <strong>{reportData.bobina?.metri_totali}m</strong></Typography>\n            <Typography>Metri Residui: <strong>{reportData.bobina?.metri_residui}m</strong></Typography>\n            <Typography>Metri Utilizzati: <strong>{reportData.bobina?.metri_utilizzati}m</strong></Typography>\n            <Typography>Utilizzo: <strong>{reportData.bobina?.percentuale_utilizzo}%</strong></Typography>\n          </CardContent>\n        </Card>\n      </Grid>\n\n      <Grid item xs={12} md={6}>\n        <Card>\n          <CardContent>\n            <Typography variant=\"h6\" gutterBottom>\n              Cavi Associati ({reportData.totale_cavi})\n            </Typography>\n            <Box sx={{ maxHeight: 300, overflow: 'auto' }}>\n              {reportData.cavi_associati?.map((cavo, index) => (\n                <Box key={index} sx={{ mb: 1, p: 1, border: '1px solid #e0e0e0', borderRadius: 1 }}>\n                  <Typography variant=\"body2\"><strong>{cavo.id_cavo}</strong></Typography>\n                  <Typography variant=\"caption\">\n                    {cavo.sistema} - {cavo.utility} - {cavo.tipologia}\n                  </Typography>\n                  <Typography variant=\"caption\" display=\"block\">\n                    Teorici: {cavo.metri_teorici}m | Reali: {cavo.metri_reali}m | Stato: {cavo.stato}\n                  </Typography>\n                </Box>\n              ))}\n            </Box>\n          </CardContent>\n        </Card>\n      </Grid>\n    </Grid>\n  );\n\n  const renderPosaPeriodoReport = () => (\n    <Grid container spacing={3}>\n      <Grid item xs={12} md={4}>\n        <Card>\n          <CardContent>\n            <Typography variant=\"h6\" gutterBottom>Statistiche Periodo</Typography>\n            <Typography>Periodo: <strong>{reportData.data_inizio} - {reportData.data_fine}</strong></Typography>\n            <Typography>Totale Metri: <strong>{reportData.totale_metri_periodo}m</strong></Typography>\n            <Typography>Giorni Attivi: <strong>{reportData.giorni_attivi}</strong></Typography>\n            <Typography>Media Giornaliera: <strong>{reportData.media_giornaliera}m/giorno</strong></Typography>\n          </CardContent>\n        </Card>\n      </Grid>\n\n      <Grid item xs={12} md={8}>\n        <Card>\n          <CardContent>\n            <Typography variant=\"h6\" gutterBottom>Posa Giornaliera</Typography>\n            <Box sx={{ maxHeight: 300, overflow: 'auto' }}>\n              {reportData.posa_giornaliera?.map((posa, index) => (\n                <Box key={index} sx={{ display: 'flex', justifyContent: 'space-between', py: 0.5 }}>\n                  <Typography>{posa.data}</Typography>\n                  <Typography><strong>{posa.metri}m</strong></Typography>\n                </Box>\n              ))}\n            </Box>\n          </CardContent>\n        </Card>\n      </Grid>\n    </Grid>\n  );\n\n  const renderCaviStatoReport = () => (\n    <Grid container spacing={3}>\n      <Grid item xs={12}>\n        <Typography variant=\"h6\" gutterBottom>Cavi per Stato di Installazione</Typography>\n        <Grid container spacing={2}>\n          {reportData.cavi_per_stato?.map((stato, index) => (\n            <Grid item xs={12} md={6} lg={3} key={index}>\n              <Card>\n                <CardContent>\n                  <Typography variant=\"h6\" gutterBottom>\n                    <Chip\n                      label={stato.stato}\n                      color={stato.stato === 'Installato' ? 'success' : 'warning'}\n                      sx={{ mb: 1 }}\n                    />\n                  </Typography>\n                  <Typography>Numero Cavi: <strong>{stato.num_cavi}</strong></Typography>\n                  <Typography>Metri Teorici: <strong>{stato.metri_teorici}m</strong></Typography>\n                  <Typography>Metri Reali: <strong>{stato.metri_reali}m</strong></Typography>\n                </CardContent>\n              </Card>\n            </Grid>\n          ))}\n        </Grid>\n      </Grid>\n    </Grid>\n  );\n\n  const renderDialog = () => (\n    <Dialog open={openDialog} onClose={handleCloseDialog} maxWidth=\"sm\" fullWidth>\n      <DialogTitle>\n        {selectedReport?.title}\n      </DialogTitle>\n      <DialogContent>\n        {error && (\n          <Alert severity=\"error\" sx={{ mb: 2 }}>\n            {error}\n          </Alert>\n        )}\n\n        <Grid container spacing={2} sx={{ mt: 1 }}>\n          <Grid item xs={12}>\n            <FormControl fullWidth>\n              <InputLabel>Formato</InputLabel>\n              <Select\n                value={formData.formato}\n                label=\"Formato\"\n                onChange={(e) => setFormData({ ...formData, formato: e.target.value })}\n              >\n                <MenuItem value=\"video\">Visualizza a schermo</MenuItem>\n                <MenuItem value=\"pdf\">Download PDF</MenuItem>\n                <MenuItem value=\"excel\">Download Excel</MenuItem>\n              </Select>\n            </FormControl>\n          </Grid>\n\n          {dialogType === 'bobina-specifica' && (\n            <Grid item xs={12}>\n              <TextField\n                fullWidth\n                label=\"ID Bobina\"\n                value={formData.id_bobina}\n                onChange={(e) => setFormData({ ...formData, id_bobina: e.target.value })}\n                placeholder=\"Es: 1, 2, A, B...\"\n                helperText=\"Inserisci solo la parte finale dell'ID (es: 1 per C1_B1)\"\n              />\n            </Grid>\n          )}\n\n          {dialogType === 'posa-periodo' && (\n            <>\n              <Grid item xs={6}>\n                <TextField\n                  fullWidth\n                  type=\"date\"\n                  label=\"Data Inizio\"\n                  value={formData.data_inizio}\n                  onChange={(e) => setFormData({ ...formData, data_inizio: e.target.value })}\n                  InputLabelProps={{ shrink: true }}\n                />\n              </Grid>\n              <Grid item xs={6}>\n                <TextField\n                  fullWidth\n                  type=\"date\"\n                  label=\"Data Fine\"\n                  value={formData.data_fine}\n                  onChange={(e) => setFormData({ ...formData, data_fine: e.target.value })}\n                  InputLabelProps={{ shrink: true }}\n                />\n              </Grid>\n            </>\n          )}\n        </Grid>\n      </DialogContent>\n      <DialogActions>\n        <Button onClick={handleCloseDialog}>Annulla</Button>\n        <Button\n          onClick={handleGenerateReport}\n          variant=\"contained\"\n          disabled={loading}\n          startIcon={loading ? <CircularProgress size={20} /> : <VisibilityIcon />}\n        >\n          {loading ? 'Generazione...' : 'Genera Report'}\n        </Button>\n      </DialogActions>\n    </Dialog>\n  );\n\n  return (\n    <Box sx={{ p: 3 }}>\n      {/* Header */}\n      <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 3 }}>\n        <Box sx={{ display: 'flex', alignItems: 'center', gap: 2 }}>\n          <IconButton onClick={() => navigate(-1)} color=\"primary\">\n            <ArrowBackIcon />\n          </IconButton>\n          <Typography variant=\"h4\" component=\"h1\">\n            Report e Analytics\n          </Typography>\n        </Box>\n        <AdminHomeButton />\n      </Box>\n\n      {/* Descrizione */}\n      <Alert severity=\"info\" sx={{ mb: 3 }}>\n        Seleziona il tipo di report che desideri generare. Ogni report offre analisi specifiche\n        per monitorare l'avanzamento del progetto e ottimizzare le operazioni.\n      </Alert>\n\n      {/* Griglia dei report */}\n      <Grid container spacing={3}>\n        {reportTypes.map((report) => (\n          <Grid item xs={12} md={6} lg={4} key={report.id}>\n            <Card\n              sx={{\n                height: '100%',\n                display: 'flex',\n                flexDirection: 'column',\n                cursor: 'pointer',\n                transition: 'transform 0.2s, box-shadow 0.2s',\n                '&:hover': {\n                  transform: 'translateY(-4px)',\n                  boxShadow: 4\n                }\n              }}\n              onClick={() => handleReportSelect(report)}\n            >\n              <CardContent sx={{ flexGrow: 1 }}>\n                <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>\n                  <Box sx={{ color: `${report.color}.main`, mr: 2 }}>\n                    {report.icon}\n                  </Box>\n                  <Typography variant=\"h6\" component=\"h2\">\n                    {report.title}\n                  </Typography>\n                </Box>\n\n                <Typography variant=\"body2\" color=\"text.secondary\" sx={{ mb: 2 }}>\n                  {report.description}\n                </Typography>\n\n                <Box>\n                  {report.features.map((feature, index) => (\n                    <Chip\n                      key={index}\n                      label={feature}\n                      size=\"small\"\n                      variant=\"outlined\"\n                      sx={{ mr: 0.5, mb: 0.5 }}\n                    />\n                  ))}\n                </Box>\n              </CardContent>\n\n              <CardActions>\n                <Button\n                  size=\"small\"\n                  color={report.color}\n                  startIcon={<AssessmentIcon />}\n                  fullWidth\n                >\n                  Genera Report\n                </Button>\n              </CardActions>\n            </Card>\n          </Grid>\n        ))}\n      </Grid>\n\n      {/* Contenuto del report */}\n      {renderReportContent()}\n\n      {/* Dialog per configurazione report */}\n      {renderDialog()}\n    </Box>\n  );\n};\n\nexport default ReportCaviPageNew;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAClD,SACEC,GAAG,EACHC,UAAU,EACVC,KAAK,EACLC,IAAI,EACJC,IAAI,EACJC,WAAW,EACXC,WAAW,EACXC,MAAM,EACNC,IAAI,EACJC,KAAK,EACLC,gBAAgB,EAChBC,OAAO,EACPC,UAAU,EACVC,OAAO,EACPC,MAAM,EACNC,WAAW,EACXC,aAAa,EACbC,aAAa,EACbC,WAAW,EACXC,UAAU,EACVC,MAAM,EACNC,QAAQ,EACRC,SAAS,EACTC,SAAS,EACTC,gBAAgB,EAChBC,gBAAgB,QACX,eAAe;AACtB,SACEC,UAAU,IAAIC,cAAc,EAC5BC,QAAQ,IAAIC,YAAY,EACxBC,QAAQ,IAAIC,YAAY,EACxBC,QAAQ,IAAIC,YAAY,EACxBC,IAAI,IAAIC,QAAQ,EAChBC,QAAQ,IAAIC,YAAY,EACxBC,UAAU,IAAIC,cAAc,EAC5BC,OAAO,IAAIC,WAAW,EACtBC,SAAS,IAAIC,aAAa,EAC1BC,SAAS,IAAIC,aAAa,EAC1BC,KAAK,IAAIC,SAAS,EAClBC,SAAS,IAAIC,aAAa,EAC1BC,UAAU,IAAIC,cAAc,QACvB,qBAAqB;AAC5B,SAASC,WAAW,EAAEC,SAAS,QAAQ,kBAAkB;AACzD,SAASC,OAAO,QAAQ,2BAA2B;AACnD,OAAOC,eAAe,MAAM,yCAAyC;AACrE,OAAOC,aAAa,MAAM,8BAA8B;AAAC,SAAAC,MAAA,IAAAC,OAAA,EAAAC,QAAA,IAAAC,SAAA;AAEzD,MAAMC,iBAAiB,GAAGA,CAAA,KAAM;EAAAC,EAAA;EAC9B,MAAMC,QAAQ,GAAGX,WAAW,CAAC,CAAC;EAC9B,MAAM;IAAEY;EAAW,CAAC,GAAGX,SAAS,CAAC,CAAC;EAClC,MAAM;IAAEY;EAAK,CAAC,GAAGX,OAAO,CAAC,CAAC;EAE1B,MAAM,CAACY,OAAO,EAAEC,UAAU,CAAC,GAAGrE,QAAQ,CAAC,KAAK,CAAC;EAC7C,MAAM,CAACsE,KAAK,EAAEC,QAAQ,CAAC,GAAGvE,QAAQ,CAAC,IAAI,CAAC;EACxC,MAAM,CAACwE,UAAU,EAAEC,aAAa,CAAC,GAAGzE,QAAQ,CAAC,IAAI,CAAC;EAClD,MAAM,CAAC0E,cAAc,EAAEC,iBAAiB,CAAC,GAAG3E,QAAQ,CAAC,IAAI,CAAC;EAC1D,MAAM,CAAC4E,UAAU,EAAEC,aAAa,CAAC,GAAG7E,QAAQ,CAAC,KAAK,CAAC;EACnD,MAAM,CAAC8E,UAAU,EAAEC,aAAa,CAAC,GAAG/E,QAAQ,CAAC,EAAE,CAAC;EAChD,MAAM,CAACgF,QAAQ,EAAEC,WAAW,CAAC,GAAGjF,QAAQ,CAAC;IACvCkF,OAAO,EAAE,OAAO;IAChBC,WAAW,EAAE,EAAE;IACfC,SAAS,EAAE,EAAE;IACbC,SAAS,EAAE;EACb,CAAC,CAAC;;EAEF;EACA,MAAMC,WAAW,GAAG,CAClB;IACEC,EAAE,EAAE,UAAU;IACdC,KAAK,EAAE,oBAAoB;IAC3BC,WAAW,EAAE,2FAA2F;IACxGC,IAAI,eAAE9B,OAAA,CAAC/B,cAAc;MAAA8D,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC;IACxBC,KAAK,EAAE,SAAS;IAChBC,QAAQ,EAAE,CAAC,yBAAyB,EAAE,2BAA2B,EAAE,qBAAqB,EAAE,yBAAyB;EACrH,CAAC,EACD;IACET,EAAE,EAAE,KAAK;IACTC,KAAK,EAAE,oBAAoB;IAC3BC,WAAW,EAAE,wEAAwE;IACrFC,IAAI,eAAE9B,OAAA,CAACvB,QAAQ;MAAAsD,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC;IAClBC,KAAK,EAAE,WAAW;IAClBC,QAAQ,EAAE,CAAC,yBAAyB,EAAE,0BAA0B,EAAE,qBAAqB,EAAE,eAAe;EAC1G,CAAC,EACD;IACET,EAAE,EAAE,QAAQ;IACZC,KAAK,EAAE,wBAAwB;IAC/BC,WAAW,EAAE,uEAAuE;IACpFC,IAAI,eAAE9B,OAAA,CAACT,aAAa;MAAAwC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC;IACvBC,KAAK,EAAE,SAAS;IAChBC,QAAQ,EAAE,CAAC,qBAAqB,EAAE,sBAAsB,EAAE,oBAAoB,EAAE,iBAAiB;EACnG,CAAC,EACD;IACET,EAAE,EAAE,kBAAkB;IACtBC,KAAK,EAAE,yBAAyB;IAChCC,WAAW,EAAE,yEAAyE;IACtFC,IAAI,eAAE9B,OAAA,CAACX,SAAS;MAAA0C,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC;IACnBC,KAAK,EAAE,MAAM;IACbC,QAAQ,EAAE,CAAC,kBAAkB,EAAE,gBAAgB,EAAE,oBAAoB,EAAE,oBAAoB;EAC7F,CAAC,EACD;IACET,EAAE,EAAE,cAAc;IAClBC,KAAK,EAAE,yBAAyB;IAChCC,WAAW,EAAE,4DAA4D;IACzEC,IAAI,eAAE9B,OAAA,CAACzB,YAAY;MAAAwD,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC;IACtBC,KAAK,EAAE,SAAS;IAChBC,QAAQ,EAAE,CAAC,iBAAiB,EAAE,wBAAwB,EAAE,oBAAoB,EAAE,mBAAmB;EACnG,CAAC,EACD;IACET,EAAE,EAAE,YAAY;IAChBC,KAAK,EAAE,uBAAuB;IAC9BC,WAAW,EAAE,iFAAiF;IAC9FC,IAAI,eAAE9B,OAAA,CAAC7B,YAAY;MAAA4D,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC;IACtBC,KAAK,EAAE,OAAO;IACdC,QAAQ,EAAE,CAAC,gBAAgB,EAAE,2BAA2B,EAAE,eAAe,EAAE,kBAAkB;EAC/F,CAAC,CACF;EAED,MAAMC,kBAAkB,GAAIC,UAAU,IAAK;IACzCvB,iBAAiB,CAACuB,UAAU,CAAC;IAC7BnB,aAAa,CAACmB,UAAU,CAACX,EAAE,CAAC;;IAE5B;IACA,IAAIW,UAAU,CAACX,EAAE,KAAK,cAAc,EAAE;MACpC,MAAMY,KAAK,GAAG,IAAIC,IAAI,CAAC,CAAC;MACxB,MAAMC,SAAS,GAAG,IAAID,IAAI,CAAC,CAAC;MAC5BC,SAAS,CAACC,QAAQ,CAACH,KAAK,CAACI,QAAQ,CAAC,CAAC,GAAG,CAAC,CAAC;MAExCtB,WAAW,CAAC;QACV,GAAGD,QAAQ;QACXG,WAAW,EAAEkB,SAAS,CAACG,WAAW,CAAC,CAAC,CAACC,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC;QAClDrB,SAAS,EAAEe,KAAK,CAACK,WAAW,CAAC,CAAC,CAACC,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC;MAC7C,CAAC,CAAC;IACJ;IAEA5B,aAAa,CAAC,IAAI,CAAC;EACrB,CAAC;EAED,MAAM6B,oBAAoB,GAAG,MAAAA,CAAA,KAAY;IACvC,IAAI;MACFrC,UAAU,CAAC,IAAI,CAAC;MAChBE,QAAQ,CAAC,IAAI,CAAC;MAEd,IAAIoC,QAAQ;MAEZ,QAAQ7B,UAAU;QAChB,KAAK,UAAU;UACb6B,QAAQ,GAAG,MAAMjD,aAAa,CAACkD,iBAAiB,CAAC1C,UAAU,EAAEc,QAAQ,CAACE,OAAO,CAAC;UAC9E;QACF,KAAK,KAAK;UACRyB,QAAQ,GAAG,MAAMjD,aAAa,CAACmD,mBAAmB,CAAC3C,UAAU,EAAEc,QAAQ,CAACE,OAAO,CAAC;UAChF;QACF,KAAK,QAAQ;UACXyB,QAAQ,GAAG,MAAMjD,aAAa,CAACoD,eAAe,CAAC5C,UAAU,EAAEc,QAAQ,CAACE,OAAO,CAAC;UAC5E;QACF,KAAK,kBAAkB;UACrB,IAAI,CAACF,QAAQ,CAACK,SAAS,EAAE;YACvBd,QAAQ,CAAC,8BAA8B,CAAC;YACxC;UACF;UACAoC,QAAQ,GAAG,MAAMjD,aAAa,CAACqD,eAAe,CAAC7C,UAAU,EAAEc,QAAQ,CAACK,SAAS,EAAEL,QAAQ,CAACE,OAAO,CAAC;UAChG;QACF,KAAK,cAAc;UACjB,IAAI,CAACF,QAAQ,CAACG,WAAW,IAAI,CAACH,QAAQ,CAACI,SAAS,EAAE;YAChDb,QAAQ,CAAC,4CAA4C,CAAC;YACtD;UACF;UACAoC,QAAQ,GAAG,MAAMjD,aAAa,CAACsD,uBAAuB,CACpD9C,UAAU,EACVc,QAAQ,CAACG,WAAW,EACpBH,QAAQ,CAACI,SAAS,EAClBJ,QAAQ,CAACE,OACX,CAAC;UACD;QACF,KAAK,YAAY;UACfyB,QAAQ,GAAG,MAAMjD,aAAa,CAACuD,kBAAkB,CAAC/C,UAAU,EAAEc,QAAQ,CAACE,OAAO,CAAC;UAC/E;QACF;UACE,MAAM,IAAIgC,KAAK,CAAC,iCAAiC,CAAC;MACtD;MAEA,IAAIlC,QAAQ,CAACE,OAAO,KAAK,OAAO,EAAE;QAChCT,aAAa,CAACkC,QAAQ,CAACQ,OAAO,CAAC;MACjC,CAAC,MAAM;QACL;QACA,IAAIR,QAAQ,CAACS,QAAQ,EAAE;UACrBC,MAAM,CAACC,IAAI,CAACX,QAAQ,CAACS,QAAQ,EAAE,QAAQ,CAAC;QAC1C;MACF;MAEAvC,aAAa,CAAC,KAAK,CAAC;IACtB,CAAC,CAAC,OAAO0C,GAAG,EAAE;MACZC,OAAO,CAAClD,KAAK,CAAC,sCAAsC,EAAEiD,GAAG,CAAC;MAC1DhD,QAAQ,CAACgD,GAAG,CAACE,MAAM,IAAIF,GAAG,CAACG,OAAO,IAAI,0CAA0C,CAAC;IACnF,CAAC,SAAS;MACRrD,UAAU,CAAC,KAAK,CAAC;IACnB;EACF,CAAC;EAED,MAAMsD,iBAAiB,GAAGA,CAAA,KAAM;IAC9B9C,aAAa,CAAC,KAAK,CAAC;IACpBN,QAAQ,CAAC,IAAI,CAAC;IACdU,WAAW,CAAC;MACVC,OAAO,EAAE,OAAO;MAChBC,WAAW,EAAE,EAAE;MACfC,SAAS,EAAE,EAAE;MACbC,SAAS,EAAE;IACb,CAAC,CAAC;EACJ,CAAC;EAED,MAAMuC,mBAAmB,GAAGA,CAAA,KAAM;IAChC,IAAI,CAACpD,UAAU,EAAE,OAAO,IAAI;IAE5B,oBACEZ,OAAA,CAACxD,KAAK;MAACyH,EAAE,EAAE;QAAEC,CAAC,EAAE,CAAC;QAAEC,EAAE,EAAE;MAAE,CAAE;MAAAC,QAAA,gBACzBpE,OAAA,CAAC1D,GAAG;QAAC2H,EAAE,EAAE;UAAEI,OAAO,EAAE,MAAM;UAAEC,cAAc,EAAE,eAAe;UAAEC,UAAU,EAAE,QAAQ;UAAEC,EAAE,EAAE;QAAE,CAAE;QAAAJ,QAAA,gBACzFpE,OAAA,CAACzD,UAAU;UAACkI,OAAO,EAAC,IAAI;UAAAL,QAAA,GACrBtD,cAAc,aAAdA,cAAc,uBAAdA,cAAc,CAAEc,KAAK,EAAC,KAAG,EAAChB,UAAU,CAAC8D,aAAa;QAAA;UAAA3C,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACzC,CAAC,eACblC,OAAA,CAACnD,MAAM;UACL8H,SAAS,eAAE3E,OAAA,CAACjB,WAAW;YAAAgD,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAE;UAC3B0C,OAAO,EAAEA,CAAA,KAAM/D,aAAa,CAAC,IAAI,CAAE;UACnC4D,OAAO,EAAC,UAAU;UAClBI,IAAI,EAAC,OAAO;UAAAT,QAAA,EACb;QAED;UAAArC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACN,CAAC,eAENlC,OAAA,CAAC/C,OAAO;QAACgH,EAAE,EAAE;UAAEO,EAAE,EAAE;QAAE;MAAE;QAAAzC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC,EAGzBhB,UAAU,KAAK,UAAU,IAAI4D,oBAAoB,CAAC,CAAC,EACnD5D,UAAU,KAAK,KAAK,IAAI6D,eAAe,CAAC,CAAC,EACzC7D,UAAU,KAAK,QAAQ,IAAI8D,kBAAkB,CAAC,CAAC,EAC/C9D,UAAU,KAAK,kBAAkB,IAAI+D,2BAA2B,CAAC,CAAC,EAClE/D,UAAU,KAAK,cAAc,IAAIgE,uBAAuB,CAAC,CAAC,EAC1DhE,UAAU,KAAK,YAAY,IAAIiE,qBAAqB,CAAC,CAAC;IAAA;MAAApD,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAClD,CAAC;EAEZ,CAAC;EAED,MAAM4C,oBAAoB,GAAGA,CAAA,kBAC3B9E,OAAA,CAACvD,IAAI;IAAC2I,SAAS;IAACC,OAAO,EAAE,CAAE;IAAAjB,QAAA,gBACzBpE,OAAA,CAACvD,IAAI;MAAC6I,IAAI;MAACC,EAAE,EAAE,EAAG;MAACC,EAAE,EAAE,CAAE;MAAApB,QAAA,eACvBpE,OAAA,CAACtD,IAAI;QAAA0H,QAAA,eACHpE,OAAA,CAACrD,WAAW;UAAAyH,QAAA,gBACVpE,OAAA,CAACzD,UAAU;YAACkI,OAAO,EAAC,IAAI;YAACgB,YAAY;YAAArB,QAAA,EAAC;UAAoB;YAAArC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAY,CAAC,eACvElC,OAAA,CAACzD,UAAU;YAAA6H,QAAA,GAAC,gBAAc,eAAApE,OAAA;cAAAoE,QAAA,GAASxD,UAAU,CAAC8E,YAAY,EAAC,GAAC;YAAA;cAAA3D,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAY,CAAC,eAClFlC,OAAA,CAACzD,UAAU;YAAA6H,QAAA,GAAC,gBAAc,eAAApE,OAAA;cAAAoE,QAAA,GAASxD,UAAU,CAAC+E,YAAY,EAAC,GAAC;YAAA;cAAA5D,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAY,CAAC,eAClFlC,OAAA,CAACzD,UAAU;YAAA6H,QAAA,GAAC,mBAAiB,eAAApE,OAAA;cAAAoE,QAAA,GAASxD,UAAU,CAACgF,eAAe,EAAC,GAAC;YAAA;cAAA7D,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAY,CAAC,eACxFlC,OAAA,CAACzD,UAAU;YAAA6H,QAAA,GAAC,eAAa,eAAApE,OAAA;cAAAoE,QAAA,GAASxD,UAAU,CAACiF,uBAAuB,EAAC,GAAC;YAAA;cAAA9D,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAY,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACjF;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACV;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,eACPlC,OAAA,CAACvD,IAAI;MAAC6I,IAAI;MAACC,EAAE,EAAE,EAAG;MAACC,EAAE,EAAE,CAAE;MAAApB,QAAA,eACvBpE,OAAA,CAACtD,IAAI;QAAA0H,QAAA,eACHpE,OAAA,CAACrD,WAAW;UAAAyH,QAAA,gBACVpE,OAAA,CAACzD,UAAU;YAACkI,OAAO,EAAC,IAAI;YAACgB,YAAY;YAAArB,QAAA,EAAC;UAAI;YAAArC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAY,CAAC,eACvDlC,OAAA,CAACzD,UAAU;YAAA6H,QAAA,GAAC,eAAa,eAAApE,OAAA;cAAAoE,QAAA,EAASxD,UAAU,CAACkF;YAAW;cAAA/D,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAS,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAY,CAAC,eAC/ElC,OAAA,CAACzD,UAAU;YAAA6H,QAAA,GAAC,eAAa,eAAApE,OAAA;cAAAoE,QAAA,EAASxD,UAAU,CAACmF;YAAW;cAAAhE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAS,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAY,CAAC,eAC/ElC,OAAA,CAACzD,UAAU;YAAA6H,QAAA,GAAC,kBAAgB,eAAApE,OAAA;cAAAoE,QAAA,EAASxD,UAAU,CAACoF;YAAc;cAAAjE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAS,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAY,CAAC,eACrFlC,OAAA,CAACzD,UAAU;YAAA6H,QAAA,GAAC,oBAAkB,eAAApE,OAAA;cAAAoE,QAAA,GAASxD,UAAU,CAACqF,gBAAgB,EAAC,GAAC;YAAA;cAAAlE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAY,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC/E;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACV;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,eACPlC,OAAA,CAACvD,IAAI;MAAC6I,IAAI;MAACC,EAAE,EAAE,EAAG;MAACC,EAAE,EAAE,CAAE;MAAApB,QAAA,eACvBpE,OAAA,CAACtD,IAAI;QAAA0H,QAAA,eACHpE,OAAA,CAACrD,WAAW;UAAAyH,QAAA,gBACVpE,OAAA,CAACzD,UAAU;YAACkI,OAAO,EAAC,IAAI;YAACgB,YAAY;YAAArB,QAAA,EAAC;UAAW;YAAArC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAY,CAAC,eAC9DlC,OAAA,CAACzD,UAAU;YAAA6H,QAAA,GAAC,qBAAmB,eAAApE,OAAA;cAAAoE,QAAA,GAASxD,UAAU,CAACsF,iBAAiB,EAAC,UAAQ;YAAA;cAAAnE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAY,CAAC,EAClGtB,UAAU,CAACuF,cAAc,iBACxBnG,OAAA,CAAAE,SAAA;YAAAkE,QAAA,gBACEpE,OAAA,CAACzD,UAAU;cAAA6H,QAAA,GAAC,kBAAgB,eAAApE,OAAA;gBAAAoE,QAAA,GAASxD,UAAU,CAACuF,cAAc,EAAC,SAAO;cAAA;gBAAApE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAY,CAAC,eAC5FlC,OAAA,CAACzD,UAAU;cAAA6H,QAAA,GAAC,sBAAoB,eAAApE,OAAA;gBAAAoE,QAAA,EAASxD,UAAU,CAACwF;cAAkB;gBAAArE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAS,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAY,CAAC;UAAA,eAC7F,CACH;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACU;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACV;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,EACNtB,UAAU,CAACyF,YAAY,IAAIzF,UAAU,CAACyF,YAAY,CAACC,MAAM,GAAG,CAAC,iBAC5DtG,OAAA,CAACvD,IAAI;MAAC6I,IAAI;MAACC,EAAE,EAAE,EAAG;MAACC,EAAE,EAAE,CAAE;MAAApB,QAAA,eACvBpE,OAAA,CAACtD,IAAI;QAAA0H,QAAA,eACHpE,OAAA,CAACrD,WAAW;UAAAyH,QAAA,gBACVpE,OAAA,CAACzD,UAAU;YAACkI,OAAO,EAAC,IAAI;YAACgB,YAAY;YAAArB,QAAA,EAAC;UAAY;YAAArC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAY,CAAC,EAC9DtB,UAAU,CAACyF,YAAY,CAACE,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC,CAACC,GAAG,CAAC,CAACC,IAAI,EAAEC,KAAK,kBACnD1G,OAAA,CAACzD,UAAU;YAAA6H,QAAA,GACRqC,IAAI,CAACE,IAAI,EAAC,IAAE,eAAA3G,OAAA;cAAAoE,QAAA,GAASqC,IAAI,CAACG,KAAK,EAAC,GAAC;YAAA;cAAA7E,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC;UAAA,GAD5BwE,KAAK;YAAA3E,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAEV,CACb,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACS;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACV;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CACP;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACG,CACP;EAED,MAAM6C,eAAe,GAAGA,CAAA;IAAA,IAAA8B,qBAAA,EAAAC,qBAAA;IAAA,oBACtB9G,OAAA,CAACvD,IAAI;MAAC2I,SAAS;MAACC,OAAO,EAAE,CAAE;MAAAjB,QAAA,gBACzBpE,OAAA,CAACvD,IAAI;QAAC6I,IAAI;QAACC,EAAE,EAAE,EAAG;QAAAnB,QAAA,gBAChBpE,OAAA,CAACzD,UAAU;UAACkI,OAAO,EAAC,IAAI;UAACgB,YAAY;UAAArB,QAAA,EAAC;QAAkB;UAAArC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAY,CAAC,eACrElC,OAAA,CAACvD,IAAI;UAAC2I,SAAS;UAACC,OAAO,EAAE,CAAE;UAAAjB,QAAA,GAAAyC,qBAAA,GACxBjG,UAAU,CAACmG,aAAa,cAAAF,qBAAA,uBAAxBA,qBAAA,CAA0BL,GAAG,CAAC,CAACQ,IAAI,EAAEN,KAAK,kBACzC1G,OAAA,CAACvD,IAAI;YAAC6I,IAAI;YAACC,EAAE,EAAE,EAAG;YAACC,EAAE,EAAE,CAAE;YAACyB,EAAE,EAAE,CAAE;YAAA7C,QAAA,eAC9BpE,OAAA,CAACtD,IAAI;cAAA0H,QAAA,eACHpE,OAAA,CAACrD,WAAW;gBAAAyH,QAAA,gBACVpE,OAAA,CAACzD,UAAU;kBAACkI,OAAO,EAAC,WAAW;kBAAAL,QAAA,EAAE4C,IAAI,CAACE;gBAAS;kBAAAnF,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAa,CAAC,eAC7DlC,OAAA,CAACzD,UAAU;kBAACkI,OAAO,EAAC,OAAO;kBAAAL,QAAA,GAAC,WAAS,EAAC4C,IAAI,CAACG,OAAO;gBAAA;kBAAApF,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAa,CAAC,eAChElC,OAAA,CAACzD,UAAU;kBAAA6H,QAAA,GAAC,QAAM,EAAC4C,IAAI,CAACI,QAAQ;gBAAA;kBAAArF,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAa,CAAC,eAC9ClC,OAAA,CAACzD,UAAU;kBAAA6H,QAAA,GAAC,iBAAe,EAAC4C,IAAI,CAACK,aAAa,EAAC,GAAC;gBAAA;kBAAAtF,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAY,CAAC,eAC7DlC,OAAA,CAACzD,UAAU;kBAAA6H,QAAA,GAAC,eAAa,EAAC4C,IAAI,CAACM,WAAW,EAAC,GAAC;gBAAA;kBAAAvF,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAY,CAAC,eACzDlC,OAAA,CAACzD,UAAU;kBAAA6H,QAAA,GAAC,aAAW,EAAC4C,IAAI,CAACpB,eAAe,EAAC,GAAC;gBAAA;kBAAA7D,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAY,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAChD;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACV;UAAC,GAV6BwE,KAAK;YAAA3E,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAWrC,CACP;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACE,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,eAEPlC,OAAA,CAACvD,IAAI;QAAC6I,IAAI;QAACC,EAAE,EAAE,EAAG;QAAAnB,QAAA,gBAChBpE,OAAA,CAACzD,UAAU;UAACkI,OAAO,EAAC,IAAI;UAACgB,YAAY;UAAArB,QAAA,EAAC;QAAkB;UAAArC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAY,CAAC,eACrElC,OAAA,CAACvD,IAAI;UAAC2I,SAAS;UAACC,OAAO,EAAE,CAAE;UAAAjB,QAAA,GAAA0C,qBAAA,GACxBlG,UAAU,CAAC2G,eAAe,cAAAT,qBAAA,uBAA1BA,qBAAA,CAA4BN,GAAG,CAAC,CAACgB,MAAM,EAAEd,KAAK,kBAC7C1G,OAAA,CAACvD,IAAI;YAAC6I,IAAI;YAACC,EAAE,EAAE,EAAG;YAACC,EAAE,EAAE,CAAE;YAACyB,EAAE,EAAE,CAAE;YAAA7C,QAAA,eAC9BpE,OAAA,CAACtD,IAAI;cAAA0H,QAAA,eACHpE,OAAA,CAACrD,WAAW;gBAAAyH,QAAA,gBACVpE,OAAA,CAACzD,UAAU;kBAACkI,OAAO,EAAC,WAAW;kBAAAL,QAAA,EAAEoD,MAAM,CAACN;gBAAS;kBAAAnF,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAa,CAAC,eAC/DlC,OAAA,CAACzD,UAAU;kBAACkI,OAAO,EAAC,OAAO;kBAAAL,QAAA,GAAC,WAAS,EAACoD,MAAM,CAACL,OAAO;gBAAA;kBAAApF,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAa,CAAC,eAClElC,OAAA,CAACzD,UAAU;kBAAA6H,QAAA,GAAC,UAAQ,EAACoD,MAAM,CAACC,UAAU;gBAAA;kBAAA1F,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAa,CAAC,eACpDlC,OAAA,CAACzD,UAAU;kBAAA6H,QAAA,GAAC,qBAAmB,EAACoD,MAAM,CAACE,iBAAiB,EAAC,GAAC;gBAAA;kBAAA3F,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAY,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC5D;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACV;UAAC,GAR6BwE,KAAK;YAAA3E,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OASrC,CACP;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACE,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC;EAAA,CACR;EAED,MAAM8C,kBAAkB,GAAGA,CAAA;IAAA,IAAA2C,kBAAA;IAAA,oBACzB3H,OAAA,CAACvD,IAAI;MAAC2I,SAAS;MAACC,OAAO,EAAE,CAAE;MAAAjB,QAAA,eACzBpE,OAAA,CAACvD,IAAI;QAAC6I,IAAI;QAACC,EAAE,EAAE,EAAG;QAAAnB,QAAA,gBAChBpE,OAAA,CAACzD,UAAU;UAACkI,OAAO,EAAC,IAAI;UAACgB,YAAY;UAAArB,QAAA,GAAC,uBACf,EAACxD,UAAU,CAACgH,aAAa,EAAC,UACjD;QAAA;UAAA7F,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAY,CAAC,eACblC,OAAA,CAACvD,IAAI;UAAC2I,SAAS;UAACC,OAAO,EAAE,CAAE;UAAAjB,QAAA,GAAAuD,kBAAA,GACxB/G,UAAU,CAACiH,MAAM,cAAAF,kBAAA,uBAAjBA,kBAAA,CAAmBnB,GAAG,CAAC,CAACgB,MAAM,EAAEd,KAAK,kBACpC1G,OAAA,CAACvD,IAAI;YAAC6I,IAAI;YAACC,EAAE,EAAE,EAAG;YAACC,EAAE,EAAE,CAAE;YAACyB,EAAE,EAAE,CAAE;YAAA7C,QAAA,eAC9BpE,OAAA,CAACtD,IAAI;cAAA0H,QAAA,eACHpE,OAAA,CAACrD,WAAW;gBAAAyH,QAAA,gBACVpE,OAAA,CAACzD,UAAU;kBAACkI,OAAO,EAAC,WAAW;kBAAAL,QAAA,EAAEoD,MAAM,CAAC/F;gBAAS;kBAAAM,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAa,CAAC,eAC/DlC,OAAA,CAACzD,UAAU;kBAACkI,OAAO,EAAC,OAAO;kBAAAL,QAAA,GAAEoD,MAAM,CAACN,SAAS,EAAC,KAAG,EAACM,MAAM,CAACL,OAAO;gBAAA;kBAAApF,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAa,CAAC,eAC9ElC,OAAA,CAAClD,IAAI;kBACHgL,KAAK,EAAEN,MAAM,CAACO,KAAM;kBACpB5F,KAAK,EAAEqF,MAAM,CAACO,KAAK,KAAK,aAAa,GAAG,SAAS,GAAG,SAAU;kBAC9DlD,IAAI,EAAC,OAAO;kBACZZ,EAAE,EAAE;oBAAEO,EAAE,EAAE;kBAAE;gBAAE;kBAAAzC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACf,CAAC,eACFlC,OAAA,CAACzD,UAAU;kBAAA6H,QAAA,GAAC,gBAAc,EAACoD,MAAM,CAAC9B,YAAY,EAAC,GAAC;gBAAA;kBAAA3D,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAY,CAAC,eAC7DlC,OAAA,CAACzD,UAAU;kBAAA6H,QAAA,GAAC,iBAAe,EAACoD,MAAM,CAACQ,aAAa,EAAC,GAAC;gBAAA;kBAAAjG,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAY,CAAC,eAC/DlC,OAAA,CAACzD,UAAU;kBAAA6H,QAAA,GAAC,oBAAkB,EAACoD,MAAM,CAACS,gBAAgB,EAAC,GAAC;gBAAA;kBAAAlG,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAY,CAAC,eACrElC,OAAA,CAACzD,UAAU;kBAAA6H,QAAA,GAAC,YAAU,EAACoD,MAAM,CAACU,oBAAoB,EAAC,GAAC;gBAAA;kBAAAnG,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAY,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACtD;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACV;UAAC,GAhB6BwE,KAAK;YAAA3E,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAiBrC,CACP;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACE,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC;EAAA,CACR;EAED,MAAM+C,2BAA2B,GAAGA,CAAA;IAAA,IAAAkD,kBAAA,EAAAC,mBAAA,EAAAC,mBAAA,EAAAC,mBAAA,EAAAC,mBAAA,EAAAC,mBAAA,EAAAC,mBAAA,EAAAC,mBAAA,EAAAC,mBAAA,EAAAC,qBAAA;IAAA,oBAClC5I,OAAA,CAACvD,IAAI;MAAC2I,SAAS;MAACC,OAAO,EAAE,CAAE;MAAAjB,QAAA,gBACzBpE,OAAA,CAACvD,IAAI;QAAC6I,IAAI;QAACC,EAAE,EAAE,EAAG;QAACC,EAAE,EAAE,CAAE;QAAApB,QAAA,eACvBpE,OAAA,CAACtD,IAAI;UAAA0H,QAAA,eACHpE,OAAA,CAACrD,WAAW;YAAAyH,QAAA,gBACVpE,OAAA,CAACzD,UAAU;cAACkI,OAAO,EAAC,IAAI;cAACgB,YAAY;cAAArB,QAAA,EAAC;YAAe;cAAArC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAY,CAAC,eAClElC,OAAA,CAACzD,UAAU;cAAA6H,QAAA,GAAC,MAAI,eAAApE,OAAA;gBAAAoE,QAAA,GAAA+D,kBAAA,GAASvH,UAAU,CAAC4G,MAAM,cAAAW,kBAAA,uBAAjBA,kBAAA,CAAmB1G;cAAS;gBAAAM,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAS,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAY,CAAC,eAC5ElC,OAAA,CAACzD,UAAU;cAAA6H,QAAA,GAAC,aAAW,eAAApE,OAAA;gBAAAoE,QAAA,GAAAgE,mBAAA,GAASxH,UAAU,CAAC4G,MAAM,cAAAY,mBAAA,uBAAjBA,mBAAA,CAAmBlB;cAAS;gBAAAnF,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAS,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAY,CAAC,eACnFlC,OAAA,CAACzD,UAAU;cAAA6H,QAAA,GAAC,WAAS,eAAApE,OAAA;gBAAAoE,QAAA,GAAAiE,mBAAA,GAASzH,UAAU,CAAC4G,MAAM,cAAAa,mBAAA,uBAAjBA,mBAAA,CAAmBlB;cAAO;gBAAApF,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAS,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAY,CAAC,eAC/ElC,OAAA,CAAClD,IAAI;cACHgL,KAAK,GAAAQ,mBAAA,GAAE1H,UAAU,CAAC4G,MAAM,cAAAc,mBAAA,uBAAjBA,mBAAA,CAAmBP,KAAM;cAChC5F,KAAK,EAAE,EAAAoG,mBAAA,GAAA3H,UAAU,CAAC4G,MAAM,cAAAe,mBAAA,uBAAjBA,mBAAA,CAAmBR,KAAK,MAAK,aAAa,GAAG,SAAS,GAAG,SAAU;cAC1E9D,EAAE,EAAE;gBAAE4E,EAAE,EAAE;cAAE;YAAE;cAAA9G,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACf,CAAC,eACFlC,OAAA,CAACzD,UAAU;cAAA6H,QAAA,GAAC,gBAAc,eAAApE,OAAA;gBAAAoE,QAAA,IAAAoE,mBAAA,GAAS5H,UAAU,CAAC4G,MAAM,cAAAgB,mBAAA,uBAAjBA,mBAAA,CAAmB9C,YAAY,EAAC,GAAC;cAAA;gBAAA3D,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAY,CAAC,eAC1FlC,OAAA,CAACzD,UAAU;cAAA6H,QAAA,GAAC,iBAAe,eAAApE,OAAA;gBAAAoE,QAAA,IAAAqE,mBAAA,GAAS7H,UAAU,CAAC4G,MAAM,cAAAiB,mBAAA,uBAAjBA,mBAAA,CAAmBT,aAAa,EAAC,GAAC;cAAA;gBAAAjG,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAY,CAAC,eAC5FlC,OAAA,CAACzD,UAAU;cAAA6H,QAAA,GAAC,oBAAkB,eAAApE,OAAA;gBAAAoE,QAAA,IAAAsE,mBAAA,GAAS9H,UAAU,CAAC4G,MAAM,cAAAkB,mBAAA,uBAAjBA,mBAAA,CAAmBT,gBAAgB,EAAC,GAAC;cAAA;gBAAAlG,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAY,CAAC,eAClGlC,OAAA,CAACzD,UAAU;cAAA6H,QAAA,GAAC,YAAU,eAAApE,OAAA;gBAAAoE,QAAA,IAAAuE,mBAAA,GAAS/H,UAAU,CAAC4G,MAAM,cAAAmB,mBAAA,uBAAjBA,mBAAA,CAAmBT,oBAAoB,EAAC,GAAC;cAAA;gBAAAnG,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAY,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACnF;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACV;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,eAEPlC,OAAA,CAACvD,IAAI;QAAC6I,IAAI;QAACC,EAAE,EAAE,EAAG;QAACC,EAAE,EAAE,CAAE;QAAApB,QAAA,eACvBpE,OAAA,CAACtD,IAAI;UAAA0H,QAAA,eACHpE,OAAA,CAACrD,WAAW;YAAAyH,QAAA,gBACVpE,OAAA,CAACzD,UAAU;cAACkI,OAAO,EAAC,IAAI;cAACgB,YAAY;cAAArB,QAAA,GAAC,kBACpB,EAACxD,UAAU,CAACkF,WAAW,EAAC,GAC1C;YAAA;cAAA/D,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAY,CAAC,eACblC,OAAA,CAAC1D,GAAG;cAAC2H,EAAE,EAAE;gBAAE6E,SAAS,EAAE,GAAG;gBAAEC,QAAQ,EAAE;cAAO,CAAE;cAAA3E,QAAA,GAAAwE,qBAAA,GAC3ChI,UAAU,CAACoI,cAAc,cAAAJ,qBAAA,uBAAzBA,qBAAA,CAA2BpC,GAAG,CAAC,CAACQ,IAAI,EAAEN,KAAK,kBAC1C1G,OAAA,CAAC1D,GAAG;gBAAa2H,EAAE,EAAE;kBAAEO,EAAE,EAAE,CAAC;kBAAEN,CAAC,EAAE,CAAC;kBAAE+E,MAAM,EAAE,mBAAmB;kBAAEC,YAAY,EAAE;gBAAE,CAAE;gBAAA9E,QAAA,gBACjFpE,OAAA,CAACzD,UAAU;kBAACkI,OAAO,EAAC,OAAO;kBAAAL,QAAA,eAACpE,OAAA;oBAAAoE,QAAA,EAAS4C,IAAI,CAACmC;kBAAO;oBAAApH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAS;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAY,CAAC,eACxElC,OAAA,CAACzD,UAAU;kBAACkI,OAAO,EAAC,SAAS;kBAAAL,QAAA,GAC1B4C,IAAI,CAACoC,OAAO,EAAC,KAAG,EAACpC,IAAI,CAACqC,OAAO,EAAC,KAAG,EAACrC,IAAI,CAACE,SAAS;gBAAA;kBAAAnF,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACvC,CAAC,eACblC,OAAA,CAACzD,UAAU;kBAACkI,OAAO,EAAC,SAAS;kBAACJ,OAAO,EAAC,OAAO;kBAAAD,QAAA,GAAC,WACnC,EAAC4C,IAAI,CAACK,aAAa,EAAC,aAAW,EAACL,IAAI,CAACM,WAAW,EAAC,aAAW,EAACN,IAAI,CAACe,KAAK;gBAAA;kBAAAhG,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACtE,CAAC;cAAA,GAPLwE,KAAK;gBAAA3E,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAQV,CACN;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACC,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACK;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACV;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC;EAAA,CACR;EAED,MAAMgD,uBAAuB,GAAGA,CAAA;IAAA,IAAAoE,qBAAA;IAAA,oBAC9BtJ,OAAA,CAACvD,IAAI;MAAC2I,SAAS;MAACC,OAAO,EAAE,CAAE;MAAAjB,QAAA,gBACzBpE,OAAA,CAACvD,IAAI;QAAC6I,IAAI;QAACC,EAAE,EAAE,EAAG;QAACC,EAAE,EAAE,CAAE;QAAApB,QAAA,eACvBpE,OAAA,CAACtD,IAAI;UAAA0H,QAAA,eACHpE,OAAA,CAACrD,WAAW;YAAAyH,QAAA,gBACVpE,OAAA,CAACzD,UAAU;cAACkI,OAAO,EAAC,IAAI;cAACgB,YAAY;cAAArB,QAAA,EAAC;YAAmB;cAAArC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAY,CAAC,eACtElC,OAAA,CAACzD,UAAU;cAAA6H,QAAA,GAAC,WAAS,eAAApE,OAAA;gBAAAoE,QAAA,GAASxD,UAAU,CAACW,WAAW,EAAC,KAAG,EAACX,UAAU,CAACY,SAAS;cAAA;gBAAAO,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAS,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAY,CAAC,eACpGlC,OAAA,CAACzD,UAAU;cAAA6H,QAAA,GAAC,gBAAc,eAAApE,OAAA;gBAAAoE,QAAA,GAASxD,UAAU,CAAC2I,oBAAoB,EAAC,GAAC;cAAA;gBAAAxH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAY,CAAC,eAC1FlC,OAAA,CAACzD,UAAU;cAAA6H,QAAA,GAAC,iBAAe,eAAApE,OAAA;gBAAAoE,QAAA,EAASxD,UAAU,CAAC4I;cAAa;gBAAAzH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAS,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAY,CAAC,eACnFlC,OAAA,CAACzD,UAAU;cAAA6H,QAAA,GAAC,qBAAmB,eAAApE,OAAA;gBAAAoE,QAAA,GAASxD,UAAU,CAACsF,iBAAiB,EAAC,UAAQ;cAAA;gBAAAnE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAY,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACxF;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACV;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,eAEPlC,OAAA,CAACvD,IAAI;QAAC6I,IAAI;QAACC,EAAE,EAAE,EAAG;QAACC,EAAE,EAAE,CAAE;QAAApB,QAAA,eACvBpE,OAAA,CAACtD,IAAI;UAAA0H,QAAA,eACHpE,OAAA,CAACrD,WAAW;YAAAyH,QAAA,gBACVpE,OAAA,CAACzD,UAAU;cAACkI,OAAO,EAAC,IAAI;cAACgB,YAAY;cAAArB,QAAA,EAAC;YAAgB;cAAArC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAY,CAAC,eACnElC,OAAA,CAAC1D,GAAG;cAAC2H,EAAE,EAAE;gBAAE6E,SAAS,EAAE,GAAG;gBAAEC,QAAQ,EAAE;cAAO,CAAE;cAAA3E,QAAA,GAAAkF,qBAAA,GAC3C1I,UAAU,CAAC6I,gBAAgB,cAAAH,qBAAA,uBAA3BA,qBAAA,CAA6B9C,GAAG,CAAC,CAACC,IAAI,EAAEC,KAAK,kBAC5C1G,OAAA,CAAC1D,GAAG;gBAAa2H,EAAE,EAAE;kBAAEI,OAAO,EAAE,MAAM;kBAAEC,cAAc,EAAE,eAAe;kBAAEoF,EAAE,EAAE;gBAAI,CAAE;gBAAAtF,QAAA,gBACjFpE,OAAA,CAACzD,UAAU;kBAAA6H,QAAA,EAAEqC,IAAI,CAACE;gBAAI;kBAAA5E,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAa,CAAC,eACpClC,OAAA,CAACzD,UAAU;kBAAA6H,QAAA,eAACpE,OAAA;oBAAAoE,QAAA,GAASqC,IAAI,CAACG,KAAK,EAAC,GAAC;kBAAA;oBAAA7E,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAQ;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAY,CAAC;cAAA,GAF/CwE,KAAK;gBAAA3E,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAGV,CACN;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACC,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACK;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACV;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC;EAAA,CACR;EAED,MAAMiD,qBAAqB,GAAGA,CAAA;IAAA,IAAAwE,sBAAA;IAAA,oBAC5B3J,OAAA,CAACvD,IAAI;MAAC2I,SAAS;MAACC,OAAO,EAAE,CAAE;MAAAjB,QAAA,eACzBpE,OAAA,CAACvD,IAAI;QAAC6I,IAAI;QAACC,EAAE,EAAE,EAAG;QAAAnB,QAAA,gBAChBpE,OAAA,CAACzD,UAAU;UAACkI,OAAO,EAAC,IAAI;UAACgB,YAAY;UAAArB,QAAA,EAAC;QAA+B;UAAArC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAY,CAAC,eAClFlC,OAAA,CAACvD,IAAI;UAAC2I,SAAS;UAACC,OAAO,EAAE,CAAE;UAAAjB,QAAA,GAAAuF,sBAAA,GACxB/I,UAAU,CAACgJ,cAAc,cAAAD,sBAAA,uBAAzBA,sBAAA,CAA2BnD,GAAG,CAAC,CAACuB,KAAK,EAAErB,KAAK,kBAC3C1G,OAAA,CAACvD,IAAI;YAAC6I,IAAI;YAACC,EAAE,EAAE,EAAG;YAACC,EAAE,EAAE,CAAE;YAACyB,EAAE,EAAE,CAAE;YAAA7C,QAAA,eAC9BpE,OAAA,CAACtD,IAAI;cAAA0H,QAAA,eACHpE,OAAA,CAACrD,WAAW;gBAAAyH,QAAA,gBACVpE,OAAA,CAACzD,UAAU;kBAACkI,OAAO,EAAC,IAAI;kBAACgB,YAAY;kBAAArB,QAAA,eACnCpE,OAAA,CAAClD,IAAI;oBACHgL,KAAK,EAAEC,KAAK,CAACA,KAAM;oBACnB5F,KAAK,EAAE4F,KAAK,CAACA,KAAK,KAAK,YAAY,GAAG,SAAS,GAAG,SAAU;oBAC5D9D,EAAE,EAAE;sBAAEO,EAAE,EAAE;oBAAE;kBAAE;oBAAAzC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACf;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACQ,CAAC,eACblC,OAAA,CAACzD,UAAU;kBAAA6H,QAAA,GAAC,eAAa,eAAApE,OAAA;oBAAAoE,QAAA,EAAS2D,KAAK,CAACX;kBAAQ;oBAAArF,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAS,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAY,CAAC,eACvElC,OAAA,CAACzD,UAAU;kBAAA6H,QAAA,GAAC,iBAAe,eAAApE,OAAA;oBAAAoE,QAAA,GAAS2D,KAAK,CAACV,aAAa,EAAC,GAAC;kBAAA;oBAAAtF,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAQ,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAY,CAAC,eAC/ElC,OAAA,CAACzD,UAAU;kBAAA6H,QAAA,GAAC,eAAa,eAAApE,OAAA;oBAAAoE,QAAA,GAAS2D,KAAK,CAACT,WAAW,EAAC,GAAC;kBAAA;oBAAAvF,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAQ,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAY,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAChE;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACV;UAAC,GAd6BwE,KAAK;YAAA3E,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAerC,CACP;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACE,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC;EAAA,CACR;EAED,MAAM2H,YAAY,GAAGA,CAAA,kBACnB7J,OAAA,CAAC5C,MAAM;IAACsG,IAAI,EAAE1C,UAAW;IAAC8I,OAAO,EAAE/F,iBAAkB;IAACgG,QAAQ,EAAC,IAAI;IAACC,SAAS;IAAA5F,QAAA,gBAC3EpE,OAAA,CAAC3C,WAAW;MAAA+G,QAAA,EACTtD,cAAc,aAAdA,cAAc,uBAAdA,cAAc,CAAEc;IAAK;MAAAG,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACX,CAAC,eACdlC,OAAA,CAAC1C,aAAa;MAAA8G,QAAA,GACX1D,KAAK,iBACJV,OAAA,CAACjD,KAAK;QAACkN,QAAQ,EAAC,OAAO;QAAChG,EAAE,EAAE;UAAEO,EAAE,EAAE;QAAE,CAAE;QAAAJ,QAAA,EACnC1D;MAAK;QAAAqB,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACD,CACR,eAEDlC,OAAA,CAACvD,IAAI;QAAC2I,SAAS;QAACC,OAAO,EAAE,CAAE;QAACpB,EAAE,EAAE;UAAEE,EAAE,EAAE;QAAE,CAAE;QAAAC,QAAA,gBACxCpE,OAAA,CAACvD,IAAI;UAAC6I,IAAI;UAACC,EAAE,EAAE,EAAG;UAAAnB,QAAA,eAChBpE,OAAA,CAACxC,WAAW;YAACwM,SAAS;YAAA5F,QAAA,gBACpBpE,OAAA,CAACvC,UAAU;cAAA2G,QAAA,EAAC;YAAO;cAAArC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAY,CAAC,eAChClC,OAAA,CAACtC,MAAM;cACLwM,KAAK,EAAE9I,QAAQ,CAACE,OAAQ;cACxBwG,KAAK,EAAC,SAAS;cACfqC,QAAQ,EAAGC,CAAC,IAAK/I,WAAW,CAAC;gBAAE,GAAGD,QAAQ;gBAAEE,OAAO,EAAE8I,CAAC,CAACC,MAAM,CAACH;cAAM,CAAC,CAAE;cAAA9F,QAAA,gBAEvEpE,OAAA,CAACrC,QAAQ;gBAACuM,KAAK,EAAC,OAAO;gBAAA9F,QAAA,EAAC;cAAoB;gBAAArC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAU,CAAC,eACvDlC,OAAA,CAACrC,QAAQ;gBAACuM,KAAK,EAAC,KAAK;gBAAA9F,QAAA,EAAC;cAAY;gBAAArC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAU,CAAC,eAC7ClC,OAAA,CAACrC,QAAQ;gBAACuM,KAAK,EAAC,OAAO;gBAAA9F,QAAA,EAAC;cAAc;gBAAArC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAU,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC3C,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACE;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACV,CAAC,EAENhB,UAAU,KAAK,kBAAkB,iBAChClB,OAAA,CAACvD,IAAI;UAAC6I,IAAI;UAACC,EAAE,EAAE,EAAG;UAAAnB,QAAA,eAChBpE,OAAA,CAACpC,SAAS;YACRoM,SAAS;YACTlC,KAAK,EAAC,WAAW;YACjBoC,KAAK,EAAE9I,QAAQ,CAACK,SAAU;YAC1B0I,QAAQ,EAAGC,CAAC,IAAK/I,WAAW,CAAC;cAAE,GAAGD,QAAQ;cAAEK,SAAS,EAAE2I,CAAC,CAACC,MAAM,CAACH;YAAM,CAAC,CAAE;YACzEI,WAAW,EAAC,mBAAmB;YAC/BC,UAAU,EAAC;UAA0D;YAAAxI,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACtE;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACE,CACP,EAEAhB,UAAU,KAAK,cAAc,iBAC5BlB,OAAA,CAAAE,SAAA;UAAAkE,QAAA,gBACEpE,OAAA,CAACvD,IAAI;YAAC6I,IAAI;YAACC,EAAE,EAAE,CAAE;YAAAnB,QAAA,eACfpE,OAAA,CAACpC,SAAS;cACRoM,SAAS;cACTQ,IAAI,EAAC,MAAM;cACX1C,KAAK,EAAC,aAAa;cACnBoC,KAAK,EAAE9I,QAAQ,CAACG,WAAY;cAC5B4I,QAAQ,EAAGC,CAAC,IAAK/I,WAAW,CAAC;gBAAE,GAAGD,QAAQ;gBAAEG,WAAW,EAAE6I,CAAC,CAACC,MAAM,CAACH;cAAM,CAAC,CAAE;cAC3EO,eAAe,EAAE;gBAAEC,MAAM,EAAE;cAAK;YAAE;cAAA3I,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACnC;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACE,CAAC,eACPlC,OAAA,CAACvD,IAAI;YAAC6I,IAAI;YAACC,EAAE,EAAE,CAAE;YAAAnB,QAAA,eACfpE,OAAA,CAACpC,SAAS;cACRoM,SAAS;cACTQ,IAAI,EAAC,MAAM;cACX1C,KAAK,EAAC,WAAW;cACjBoC,KAAK,EAAE9I,QAAQ,CAACI,SAAU;cAC1B2I,QAAQ,EAAGC,CAAC,IAAK/I,WAAW,CAAC;gBAAE,GAAGD,QAAQ;gBAAEI,SAAS,EAAE4I,CAAC,CAACC,MAAM,CAACH;cAAM,CAAC,CAAE;cACzEO,eAAe,EAAE;gBAAEC,MAAM,EAAE;cAAK;YAAE;cAAA3I,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACnC;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACE,CAAC;QAAA,eACP,CACH;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACG,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACM,CAAC,eAChBlC,OAAA,CAACzC,aAAa;MAAA6G,QAAA,gBACZpE,OAAA,CAACnD,MAAM;QAAC+H,OAAO,EAAEb,iBAAkB;QAAAK,QAAA,EAAC;MAAO;QAAArC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAQ,CAAC,eACpDlC,OAAA,CAACnD,MAAM;QACL+H,OAAO,EAAE9B,oBAAqB;QAC9B2B,OAAO,EAAC,WAAW;QACnBkG,QAAQ,EAAEnK,OAAQ;QAClBmE,SAAS,EAAEnE,OAAO,gBAAGR,OAAA,CAAChD,gBAAgB;UAAC6H,IAAI,EAAE;QAAG;UAAA9C,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,gBAAGlC,OAAA,CAACnB,cAAc;UAAAkD,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAE;QAAAkC,QAAA,EAExE5D,OAAO,GAAG,gBAAgB,GAAG;MAAe;QAAAuB,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACvC,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACI,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACV,CACT;EAED,oBACElC,OAAA,CAAC1D,GAAG;IAAC2H,EAAE,EAAE;MAAEC,CAAC,EAAE;IAAE,CAAE;IAAAE,QAAA,gBAEhBpE,OAAA,CAAC1D,GAAG;MAAC2H,EAAE,EAAE;QAAEI,OAAO,EAAE,MAAM;QAAEC,cAAc,EAAE,eAAe;QAAEC,UAAU,EAAE,QAAQ;QAAEC,EAAE,EAAE;MAAE,CAAE;MAAAJ,QAAA,gBACzFpE,OAAA,CAAC1D,GAAG;QAAC2H,EAAE,EAAE;UAAEI,OAAO,EAAE,MAAM;UAAEE,UAAU,EAAE,QAAQ;UAAEqG,GAAG,EAAE;QAAE,CAAE;QAAAxG,QAAA,gBACzDpE,OAAA,CAAC9C,UAAU;UAAC0H,OAAO,EAAEA,CAAA,KAAMvE,QAAQ,CAAC,CAAC,CAAC,CAAE;UAAC8B,KAAK,EAAC,SAAS;UAAAiC,QAAA,eACtDpE,OAAA,CAACf,aAAa;YAAA8C,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACP,CAAC,eACblC,OAAA,CAACzD,UAAU;UAACkI,OAAO,EAAC,IAAI;UAACoG,SAAS,EAAC,IAAI;UAAAzG,QAAA,EAAC;QAExC;UAAArC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAY,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACV,CAAC,eACNlC,OAAA,CAACH,eAAe;QAAAkC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAChB,CAAC,eAGNlC,OAAA,CAACjD,KAAK;MAACkN,QAAQ,EAAC,MAAM;MAAChG,EAAE,EAAE;QAAEO,EAAE,EAAE;MAAE,CAAE;MAAAJ,QAAA,EAAC;IAGtC;MAAArC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAO,CAAC,eAGRlC,OAAA,CAACvD,IAAI;MAAC2I,SAAS;MAACC,OAAO,EAAE,CAAE;MAAAjB,QAAA,EACxB1C,WAAW,CAAC8E,GAAG,CAAEsE,MAAM,iBACtB9K,OAAA,CAACvD,IAAI;QAAC6I,IAAI;QAACC,EAAE,EAAE,EAAG;QAACC,EAAE,EAAE,CAAE;QAACyB,EAAE,EAAE,CAAE;QAAA7C,QAAA,eAC9BpE,OAAA,CAACtD,IAAI;UACHuH,EAAE,EAAE;YACF8G,MAAM,EAAE,MAAM;YACd1G,OAAO,EAAE,MAAM;YACf2G,aAAa,EAAE,QAAQ;YACvBC,MAAM,EAAE,SAAS;YACjBC,UAAU,EAAE,iCAAiC;YAC7C,SAAS,EAAE;cACTC,SAAS,EAAE,kBAAkB;cAC7BC,SAAS,EAAE;YACb;UACF,CAAE;UACFxG,OAAO,EAAEA,CAAA,KAAMvC,kBAAkB,CAACyI,MAAM,CAAE;UAAA1G,QAAA,gBAE1CpE,OAAA,CAACrD,WAAW;YAACsH,EAAE,EAAE;cAAEoH,QAAQ,EAAE;YAAE,CAAE;YAAAjH,QAAA,gBAC/BpE,OAAA,CAAC1D,GAAG;cAAC2H,EAAE,EAAE;gBAAEI,OAAO,EAAE,MAAM;gBAAEE,UAAU,EAAE,QAAQ;gBAAEC,EAAE,EAAE;cAAE,CAAE;cAAAJ,QAAA,gBACxDpE,OAAA,CAAC1D,GAAG;gBAAC2H,EAAE,EAAE;kBAAE9B,KAAK,EAAE,GAAG2I,MAAM,CAAC3I,KAAK,OAAO;kBAAEmJ,EAAE,EAAE;gBAAE,CAAE;gBAAAlH,QAAA,EAC/C0G,MAAM,CAAChJ;cAAI;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACT,CAAC,eACNlC,OAAA,CAACzD,UAAU;gBAACkI,OAAO,EAAC,IAAI;gBAACoG,SAAS,EAAC,IAAI;gBAAAzG,QAAA,EACpC0G,MAAM,CAAClJ;cAAK;gBAAAG,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACV,CAAC,eAENlC,OAAA,CAACzD,UAAU;cAACkI,OAAO,EAAC,OAAO;cAACtC,KAAK,EAAC,gBAAgB;cAAC8B,EAAE,EAAE;gBAAEO,EAAE,EAAE;cAAE,CAAE;cAAAJ,QAAA,EAC9D0G,MAAM,CAACjJ;YAAW;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACT,CAAC,eAEblC,OAAA,CAAC1D,GAAG;cAAA8H,QAAA,EACD0G,MAAM,CAAC1I,QAAQ,CAACoE,GAAG,CAAC,CAAC+E,OAAO,EAAE7E,KAAK,kBAClC1G,OAAA,CAAClD,IAAI;gBAEHgL,KAAK,EAAEyD,OAAQ;gBACf1G,IAAI,EAAC,OAAO;gBACZJ,OAAO,EAAC,UAAU;gBAClBR,EAAE,EAAE;kBAAEqH,EAAE,EAAE,GAAG;kBAAE9G,EAAE,EAAE;gBAAI;cAAE,GAJpBkC,KAAK;gBAAA3E,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAKX,CACF;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACC,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACK,CAAC,eAEdlC,OAAA,CAACpD,WAAW;YAAAwH,QAAA,eACVpE,OAAA,CAACnD,MAAM;cACLgI,IAAI,EAAC,OAAO;cACZ1C,KAAK,EAAE2I,MAAM,CAAC3I,KAAM;cACpBwC,SAAS,eAAE3E,OAAA,CAAC/B,cAAc;gBAAA8D,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAE;cAC9B8H,SAAS;cAAA5F,QAAA,EACV;YAED;cAAArC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACE,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACV;MAAC,GApD6B4I,MAAM,CAACnJ,EAAE;QAAAI,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAqDzC,CACP;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACE,CAAC,EAGN8B,mBAAmB,CAAC,CAAC,EAGrB6F,YAAY,CAAC,CAAC;EAAA;IAAA9H,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACZ,CAAC;AAEV,CAAC;AAAC9B,EAAA,CAvlBID,iBAAiB;EAAA,QACJT,WAAW,EACLC,SAAS,EACfC,OAAO;AAAA;AAAA4L,EAAA,GAHpBrL,iBAAiB;AAylBvB,eAAeA,iBAAiB;AAAC,IAAAqL,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}