{"ast": null, "code": "var _jsxFileName = \"C:\\\\CMS\\\\webapp\\\\frontend\\\\src\\\\components\\\\comande\\\\ComandeListRivoluzionato.js\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect } from 'react';\nimport { useSearchParams } from 'react-router-dom';\nimport { Box, Typography, Button, Paper, Chip, Dialog, DialogTitle, DialogContent, DialogActions, TextField, Alert, CircularProgress, List, ListItem, ListItemText, Stack, MenuItem, Divider } from '@mui/material';\nimport { Add as AddIcon, Assignment as AssignIcon, Person as PersonIcon, CheckCircle as CheckCircleIcon, Verified as VerifiedIcon, People as PeopleIcon, Construction as ConstructionIcon } from '@mui/icons-material';\nimport comandeService from '../../services/comandeService';\nimport responsabiliService from '../../services/responsabiliService';\nimport CreaComandaConCavi from './CreaComandaConCavi';\nimport ResponsabiliListPopup from './ResponsabiliListPopup';\nimport ComandeListTable from './ComandeListTable';\nimport InserimentoMetriDialog from './InserimentoMetriDialog';\nimport { jsxDEV as _jsxDEV, Fragment as _Fragment } from \"react/jsx-dev-runtime\";\nconst ComandeListRivoluzionato = ({\n  cantiereId,\n  cantiereName\n}) => {\n  _s();\n  // Hook per gestire i parametri URL\n  const [searchParams, setSearchParams] = useSearchParams();\n\n  // Stati principali - Responsabili come elemento principale\n  const [loading, setLoading] = useState(true);\n  const [error, setError] = useState(null);\n  const [searchingComanda, setSearchingComanda] = useState(null);\n\n  // Stati comande\n  const [statistiche, setStatistiche] = useState(null);\n  const [allComande, setAllComande] = useState([]);\n  const [loadingComande, setLoadingComande] = useState(false);\n\n  // Stati per popup responsabili\n  const [openResponsabiliPopup, setOpenResponsabiliPopup] = useState(false);\n  const [openCreaConCavi, setOpenCreaConCavi] = useState(false);\n\n  // Stati responsabili\n  const [responsabili, setResponsabili] = useState([]);\n  const [loadingResponsabili, setLoadingResponsabili] = useState(false);\n  const [comandePerResponsabile, setComandePerResponsabile] = useState({});\n  const [openResponsabileDialog, setOpenResponsabileDialog] = useState(false);\n  const [dialogModeResponsabile, setDialogModeResponsabile] = useState('create');\n  const [selectedResponsabile, setSelectedResponsabile] = useState(null);\n  const [formDataResponsabile, setFormDataResponsabile] = useState({\n    nome_responsabile: '',\n    email: '',\n    telefono: ''\n  });\n\n  // Stati per dialog comande\n  const [openComandaDialog, setOpenComandaDialog] = useState(false);\n  const [dialogModeComanda, setDialogModeComanda] = useState('view'); // 'view', 'edit'\n  const [selectedComanda, setSelectedComanda] = useState(null);\n  const [formDataComanda, setFormDataComanda] = useState({\n    tipo_comanda: 'POSA',\n    descrizione: '',\n    responsabile: '',\n    data_scadenza: '',\n    note_capo_cantiere: ''\n  });\n\n  // Stati per dialog inserimento metri\n  const [openInserimentoMetri, setOpenInserimentoMetri] = useState(false);\n  const [comandaPerMetri, setComandaPerMetri] = useState(null);\n  const loadComande = async () => {\n    try {\n      setLoadingComande(true);\n      console.log('🔄 Caricamento comande per cantiere:', cantiereId);\n      const comandeData = await comandeService.getComande(cantiereId);\n      console.log('📋 Dati comande ricevuti:', comandeData);\n      console.log('📋 Tipo dati:', typeof comandeData, 'Array?', Array.isArray(comandeData));\n\n      // Gestisci diversi formati di risposta\n      let comandeArray = [];\n      if (Array.isArray(comandeData)) {\n        comandeArray = comandeData;\n      } else if (comandeData && Array.isArray(comandeData.comande)) {\n        comandeArray = comandeData.comande;\n      } else if (comandeData && Array.isArray(comandeData.data)) {\n        comandeArray = comandeData.data;\n      }\n      console.log('📋 Array comande finale:', comandeArray, 'Lunghezza:', comandeArray.length);\n      setAllComande(comandeArray);\n    } catch (err) {\n      console.error('Errore nel caricamento comande:', err);\n      setError('Errore nel caricamento delle comande');\n    } finally {\n      setLoadingComande(false);\n    }\n  };\n  const loadStatistiche = async () => {\n    try {\n      console.log('🔄 Caricamento statistiche per cantiere:', cantiereId);\n      const stats = await comandeService.getStatisticheComande(cantiereId);\n      console.log('📊 Statistiche ricevute:', stats);\n      setStatistiche(stats);\n    } catch (err) {\n      var _err$response;\n      console.error('❌ Errore nel caricamento delle statistiche:', err);\n      console.error('❌ Dettagli errore:', ((_err$response = err.response) === null || _err$response === void 0 ? void 0 : _err$response.data) || err.message);\n    }\n  };\n  const loadResponsabili = async () => {\n    try {\n      setLoadingResponsabili(true);\n      setError(null);\n      const data = await responsabiliService.getResponsabiliCantiere(cantiereId);\n      setResponsabili(data || []);\n      await loadComandePerResponsabili(data || []);\n    } catch (err) {\n      var _err$response2, _err$response2$data;\n      console.error('Errore nel caricamento dei responsabili:', err);\n      const errorMessage = ((_err$response2 = err.response) === null || _err$response2 === void 0 ? void 0 : (_err$response2$data = _err$response2.data) === null || _err$response2$data === void 0 ? void 0 : _err$response2$data.detail) || err.message || 'Errore nel caricamento dei responsabili';\n      setError(`Errore nel caricamento dei responsabili: ${errorMessage}`);\n    } finally {\n      setLoadingResponsabili(false);\n    }\n  };\n\n  // Carica dati al mount - Focus sui responsabili\n  useEffect(() => {\n    const initializeData = async () => {\n      if (cantiereId) {\n        setLoading(true);\n        try {\n          await Promise.all([loadResponsabili(), loadComande(), loadStatistiche()]);\n        } catch (err) {\n          console.error('Errore nel caricamento iniziale:', err);\n        } finally {\n          setLoading(false);\n        }\n      }\n    };\n    initializeData();\n  }, [cantiereId]); // eslint-disable-line react-hooks/exhaustive-deps\n\n  // Gestione parametro URL per aprire comanda specifica\n  useEffect(() => {\n    const comandaParam = searchParams.get('comanda');\n    console.log('🔍 Controllo parametro URL comanda:', comandaParam);\n    console.log('📊 Stato dati:', {\n      responsabili: responsabili.length,\n      comandePerResponsabile: Object.keys(comandePerResponsabile).length,\n      loading,\n      loadingResponsabili\n    });\n\n    // Imposta lo stato di ricerca\n    if (comandaParam && comandaParam !== searchingComanda) {\n      setSearchingComanda(comandaParam);\n    }\n    if (comandaParam && responsabili.length > 0 && Object.keys(comandePerResponsabile).length > 0) {\n      console.log('🔎 Ricerca comanda tra i responsabili...');\n\n      // Cerca la comanda tra tutti i responsabili\n      let comandaTrovata = null;\n      for (const responsabile of responsabili) {\n        const comandeResp = comandePerResponsabile[responsabile.id_responsabile] || [];\n        console.log(`📋 Responsabile ${responsabile.nome_responsabile}: ${comandeResp.length} comande`);\n        comandaTrovata = comandeResp.find(c => c.codice_comanda === comandaParam);\n        if (comandaTrovata) {\n          console.log('✅ Comanda trovata:', comandaTrovata);\n          break;\n        }\n      }\n      if (comandaTrovata) {\n        console.log('🎯 Apertura automatica comanda da URL:', comandaParam);\n        setSearchingComanda(null); // Rimuovi lo stato di ricerca\n        handleOpenComandaDialog('view', comandaTrovata);\n        // Rimuovi il parametro dall'URL per evitare riaperture\n        setTimeout(() => {\n          setSearchParams(prev => {\n            const newParams = new URLSearchParams(prev);\n            newParams.delete('comanda');\n            return newParams;\n          });\n        }, 100);\n      } else {\n        console.warn('⚠️ Comanda non trovata:', comandaParam);\n        console.log('📋 Comande disponibili:');\n        responsabili.forEach(resp => {\n          const comande = comandePerResponsabile[resp.id_responsabile] || [];\n          comande.forEach(cmd => {\n            console.log(`  - ${cmd.codice_comanda} (${resp.nome_responsabile})`);\n          });\n        });\n\n        // Se non troviamo la comanda, potrebbe essere che i dati non sono completi\n        // Riprova dopo un breve delay\n        if (!loading && !loadingResponsabili) {\n          console.log('🔄 Tentativo di ricaricamento dati...');\n          setTimeout(() => {\n            loadResponsabili();\n          }, 500);\n        }\n      }\n    } else if (comandaParam) {\n      console.log('⏳ Dati non ancora caricati, attendo...');\n\n      // Se abbiamo un parametro ma i dati non sono caricati, forza il caricamento\n      if (!loading && !loadingResponsabili && responsabili.length === 0) {\n        console.log('🚀 Forzatura caricamento responsabili...');\n        loadResponsabili();\n      }\n    }\n  }, [searchParams, responsabili, comandePerResponsabile, loading, loadingResponsabili]); // eslint-disable-line react-hooks/exhaustive-deps\n\n  const loadComandePerResponsabili = async responsabiliList => {\n    try {\n      const comandeMap = {};\n      for (const responsabile of responsabiliList) {\n        try {\n          const response = await comandeService.getComandeByResponsabile(cantiereId, responsabile.nome_responsabile);\n          // Assicurati che sia sempre un array\n          let comande = [];\n          if (response && Array.isArray(response)) {\n            comande = response;\n          } else if (response && response.comande && Array.isArray(response.comande)) {\n            comande = response.comande;\n          } else if (response && response.data && Array.isArray(response.data)) {\n            comande = response.data;\n          }\n          comandeMap[responsabile.id_responsabile] = comande;\n        } catch (err) {\n          console.error(`Errore nel caricamento comande per ${responsabile.nome_responsabile}:`, err);\n          comandeMap[responsabile.id_responsabile] = [];\n        }\n      }\n      setComandePerResponsabile(comandeMap);\n    } catch (err) {\n      console.error('Errore nel caricamento delle comande:', err);\n    }\n  };\n\n  // Gestione responsabili\n  const handleOpenResponsabileDialog = (mode, responsabile = null) => {\n    setDialogModeResponsabile(mode);\n    setSelectedResponsabile(responsabile);\n    if (mode === 'edit' && responsabile) {\n      setFormDataResponsabile({\n        nome_responsabile: responsabile.nome_responsabile || '',\n        email: responsabile.email || '',\n        telefono: responsabile.telefono || ''\n      });\n    } else {\n      setFormDataResponsabile({\n        nome_responsabile: '',\n        email: '',\n        telefono: ''\n      });\n    }\n    setOpenResponsabileDialog(true);\n  };\n  const handleCloseResponsabileDialog = () => {\n    setOpenResponsabileDialog(false);\n    setSelectedResponsabile(null);\n    setError(null);\n  };\n  const handleSubmitResponsabile = async () => {\n    try {\n      setError(null);\n      if (!formDataResponsabile.nome_responsabile.trim()) {\n        setError('Il nome del responsabile è obbligatorio');\n        return;\n      }\n      if (!formDataResponsabile.email && !formDataResponsabile.telefono) {\n        setError('Almeno uno tra email e telefono deve essere specificato');\n        return;\n      }\n      if (dialogModeResponsabile === 'create') {\n        await responsabiliService.createResponsabile(cantiereId, formDataResponsabile);\n      } else if (dialogModeResponsabile === 'edit') {\n        await responsabiliService.updateResponsabile(selectedResponsabile.id_responsabile, formDataResponsabile);\n      }\n      handleCloseResponsabileDialog();\n      await loadResponsabili();\n      await loadComande();\n    } catch (err) {\n      console.error('Errore nel salvataggio:', err);\n      setError(err.detail || 'Errore nel salvataggio del responsabile');\n    }\n  };\n  const handleDeleteResponsabile = async idResponsabile => {\n    if (!window.confirm('Sei sicuro di voler eliminare questo responsabile?')) {\n      return;\n    }\n    try {\n      await responsabiliService.deleteResponsabile(idResponsabile);\n      await loadResponsabili();\n      await loadComande();\n    } catch (err) {\n      console.error('Errore nell\\'eliminazione:', err);\n      setError('Errore nell\\'eliminazione del responsabile');\n    }\n  };\n\n  // Gestione comande\n  const handleOpenComandaDialog = (mode, comanda = null) => {\n    setDialogModeComanda(mode);\n    setSelectedComanda(comanda);\n    if (mode === 'edit' && comanda) {\n      setFormDataComanda({\n        tipo_comanda: comanda.tipo_comanda,\n        descrizione: comanda.descrizione || '',\n        responsabile: comanda.responsabile || '',\n        data_scadenza: comanda.data_scadenza || '',\n        note_capo_cantiere: comanda.note_capo_cantiere || ''\n      });\n    }\n    setOpenComandaDialog(true);\n  };\n  const handleCloseComandaDialog = () => {\n    setOpenComandaDialog(false);\n    setSelectedComanda(null);\n    setFormDataComanda({\n      tipo_comanda: 'POSA',\n      descrizione: '',\n      responsabile: '',\n      data_scadenza: '',\n      note_capo_cantiere: ''\n    });\n  };\n  const handleSubmitComanda = async () => {\n    try {\n      if (dialogModeComanda === 'edit') {\n        await comandeService.updateComanda(selectedComanda.codice_comanda, formDataComanda);\n        handleCloseComandaDialog();\n        await loadResponsabili(); // Ricarica per aggiornare le comande\n        await loadComande();\n        await loadStatistiche();\n      }\n    } catch (err) {\n      console.error('Errore nel salvataggio:', err);\n      setError('Errore nel salvataggio della comanda');\n    }\n  };\n  const handleDeleteComanda = async codiceComanda => {\n    if (!window.confirm('Sei sicuro di voler eliminare questa comanda?')) {\n      return;\n    }\n    try {\n      await comandeService.deleteComanda(codiceComanda);\n      await loadResponsabili(); // Ricarica per aggiornare le comande\n      await loadComande();\n      await loadStatistiche();\n    } catch (err) {\n      console.error('Errore nell\\'eliminazione:', err);\n      setError('Errore nell\\'eliminazione della comanda');\n    }\n  };\n  const getTipoComandaLabel = tipo => {\n    const labels = {\n      'POSA': 'Posa',\n      'COLLEGAMENTO_PARTENZA': 'Coll. Partenza',\n      'COLLEGAMENTO_ARRIVO': 'Coll. Arrivo',\n      'CERTIFICAZIONE': 'Certificazione',\n      'TESTING': 'Testing/Certificazione'\n    };\n    return labels[tipo] || tipo;\n  };\n  const getStatoColor = stato => {\n    const colors = {\n      'CREATA': 'default',\n      'ASSEGNATA': 'primary',\n      'IN_CORSO': 'warning',\n      'COMPLETATA': 'success',\n      'ANNULLATA': 'error'\n    };\n    return colors[stato] || 'default';\n  };\n  if (loading || loadingComande) {\n    return /*#__PURE__*/_jsxDEV(Box, {\n      display: \"flex\",\n      justifyContent: \"center\",\n      alignItems: \"center\",\n      minHeight: \"400px\",\n      children: /*#__PURE__*/_jsxDEV(CircularProgress, {}, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 428,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 427,\n      columnNumber: 7\n    }, this);\n  }\n  return /*#__PURE__*/_jsxDEV(Box, {\n    sx: {\n      p: 3\n    },\n    children: [/*#__PURE__*/_jsxDEV(Box, {\n      mb: 3,\n      children: /*#__PURE__*/_jsxDEV(Typography, {\n        variant: \"h4\",\n        sx: {\n          fontWeight: 600,\n          color: 'primary.main'\n        },\n        children: cantiereName\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 437,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 436,\n      columnNumber: 7\n    }, this), error && /*#__PURE__*/_jsxDEV(Alert, {\n      severity: \"error\",\n      sx: {\n        mb: 2\n      },\n      children: error\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 443,\n      columnNumber: 9\n    }, this), searchingComanda && /*#__PURE__*/_jsxDEV(Alert, {\n      severity: \"info\",\n      sx: {\n        mb: 2\n      },\n      children: [\"\\uD83D\\uDD0D Ricerca comanda \", searchingComanda, \" in corso...\"]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 449,\n      columnNumber: 9\n    }, this), statistiche && /*#__PURE__*/_jsxDEV(Paper, {\n      sx: {\n        p: 2,\n        mb: 3,\n        bgcolor: 'grey.50'\n      },\n      children: /*#__PURE__*/_jsxDEV(Stack, {\n        direction: \"row\",\n        spacing: 4,\n        alignItems: \"center\",\n        justifyContent: \"space-between\",\n        flexWrap: \"wrap\",\n        children: [/*#__PURE__*/_jsxDEV(Stack, {\n          direction: \"row\",\n          alignItems: \"center\",\n          spacing: 1,\n          children: [/*#__PURE__*/_jsxDEV(PersonIcon, {\n            color: \"primary\",\n            fontSize: \"small\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 460,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(Box, {\n            children: [/*#__PURE__*/_jsxDEV(Typography, {\n              variant: \"h6\",\n              fontWeight: \"bold\",\n              sx: {\n                lineHeight: 1\n              },\n              children: statistiche.responsabili_attivi || 0\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 462,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(Typography, {\n              variant: \"caption\",\n              color: \"text.secondary\",\n              children: \"Responsabili\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 465,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 461,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 459,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(Stack, {\n          direction: \"row\",\n          alignItems: \"center\",\n          spacing: 1,\n          children: [/*#__PURE__*/_jsxDEV(AssignIcon, {\n            color: \"info\",\n            fontSize: \"small\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 473,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(Box, {\n            children: [/*#__PURE__*/_jsxDEV(Typography, {\n              variant: \"h6\",\n              fontWeight: \"bold\",\n              sx: {\n                lineHeight: 1\n              },\n              children: statistiche.totale_comande || 0\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 475,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(Typography, {\n              variant: \"caption\",\n              color: \"text.secondary\",\n              children: \"Totale\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 478,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 474,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 472,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(Stack, {\n          direction: \"row\",\n          alignItems: \"center\",\n          spacing: 1,\n          children: [/*#__PURE__*/_jsxDEV(CheckCircleIcon, {\n            color: \"warning\",\n            fontSize: \"small\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 486,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(Box, {\n            children: [/*#__PURE__*/_jsxDEV(Typography, {\n              variant: \"h6\",\n              fontWeight: \"bold\",\n              sx: {\n                lineHeight: 1\n              },\n              children: statistiche.comande_in_corso || 0\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 488,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(Typography, {\n              variant: \"caption\",\n              color: \"text.secondary\",\n              children: \"In Corso\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 491,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 487,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 485,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(Stack, {\n          direction: \"row\",\n          alignItems: \"center\",\n          spacing: 1,\n          children: [/*#__PURE__*/_jsxDEV(VerifiedIcon, {\n            color: \"success\",\n            fontSize: \"small\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 499,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(Box, {\n            children: [/*#__PURE__*/_jsxDEV(Typography, {\n              variant: \"h6\",\n              fontWeight: \"bold\",\n              sx: {\n                lineHeight: 1\n              },\n              children: statistiche.comande_completate || 0\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 501,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(Typography, {\n              variant: \"caption\",\n              color: \"text.secondary\",\n              children: \"Completate\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 504,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 500,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 498,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(Stack, {\n          direction: \"row\",\n          alignItems: \"center\",\n          spacing: 1,\n          children: [/*#__PURE__*/_jsxDEV(Box, {\n            sx: {\n              width: 32,\n              height: 32,\n              borderRadius: '50%',\n              bgcolor: statistiche.comande_completate / (statistiche.totale_comande || 1) >= 0.8 ? 'success.main' : statistiche.comande_completate / (statistiche.totale_comande || 1) >= 0.5 ? 'warning.main' : 'error.main',\n              display: 'flex',\n              alignItems: 'center',\n              justifyContent: 'center'\n            },\n            children: /*#__PURE__*/_jsxDEV(Typography, {\n              variant: \"caption\",\n              fontWeight: \"bold\",\n              color: \"white\",\n              children: [statistiche.totale_comande > 0 ? Math.round(statistiche.comande_completate / statistiche.totale_comande * 100) : 0, \"%\"]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 522,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 512,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(Box, {\n            children: [/*#__PURE__*/_jsxDEV(Typography, {\n              variant: \"body2\",\n              fontWeight: \"medium\",\n              sx: {\n                lineHeight: 1\n              },\n              children: \"Completamento\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 527,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(Typography, {\n              variant: \"caption\",\n              color: \"text.secondary\",\n              children: [statistiche.comande_create || 0, \" create\"]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 530,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 526,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 511,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 457,\n        columnNumber: 11\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 456,\n      columnNumber: 9\n    }, this), /*#__PURE__*/_jsxDEV(Box, {\n      children: /*#__PURE__*/_jsxDEV(Box, {\n        children: [/*#__PURE__*/_jsxDEV(Box, {\n          display: \"flex\",\n          justifyContent: \"space-between\",\n          alignItems: \"center\",\n          mb: 3,\n          children: [/*#__PURE__*/_jsxDEV(Typography, {\n            variant: \"h5\",\n            sx: {\n              fontWeight: 500,\n              color: 'text.primary'\n            },\n            children: \"Gestione Responsabili e Comande\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 544,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Box, {\n            display: \"flex\",\n            gap: 2,\n            children: [/*#__PURE__*/_jsxDEV(Button, {\n              variant: \"outlined\",\n              startIcon: /*#__PURE__*/_jsxDEV(PeopleIcon, {}, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 550,\n                columnNumber: 28\n              }, this),\n              onClick: () => setOpenResponsabiliPopup(true),\n              sx: {\n                textTransform: 'none',\n                fontWeight: 500,\n                px: 3,\n                py: 1,\n                backgroundColor: '#f5f7fa',\n                color: '#2196f3',\n                border: '1px solid #2196f3',\n                '&:hover': {\n                  backgroundColor: 'rgba(33, 150, 243, 0.1)',\n                  borderColor: '#1976d2'\n                }\n              },\n              children: \"Lista Responsabili\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 548,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Button, {\n              variant: \"contained\",\n              startIcon: /*#__PURE__*/_jsxDEV(AddIcon, {}, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 570,\n                columnNumber: 28\n              }, this),\n              onClick: () => handleOpenResponsabileDialog('create'),\n              sx: {\n                textTransform: 'none',\n                fontWeight: 500,\n                px: 3,\n                py: 1,\n                backgroundColor: '#2196f3',\n                color: 'white',\n                '&:hover': {\n                  backgroundColor: '#1976d2'\n                }\n              },\n              children: \"Crea Responsabile\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 568,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 547,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 543,\n          columnNumber: 11\n        }, this), loadingComande ? /*#__PURE__*/_jsxDEV(Box, {\n          display: \"flex\",\n          justifyContent: \"center\",\n          py: 4,\n          children: /*#__PURE__*/_jsxDEV(CircularProgress, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 592,\n            columnNumber: 15\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 591,\n          columnNumber: 13\n        }, this) : allComande.length === 0 ? /*#__PURE__*/_jsxDEV(Paper, {\n          elevation: 0,\n          sx: {\n            p: 6,\n            textAlign: 'center',\n            backgroundColor: 'grey.50',\n            border: '1px dashed',\n            borderColor: 'grey.300'\n          },\n          children: [/*#__PURE__*/_jsxDEV(AssignIcon, {\n            sx: {\n              fontSize: 48,\n              color: 'grey.400',\n              mb: 2\n            }\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 605,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(Typography, {\n            variant: \"h6\",\n            color: \"text.secondary\",\n            gutterBottom: true,\n            children: \"Nessuna comanda disponibile\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 606,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(Typography, {\n            variant: \"body2\",\n            color: \"text.secondary\",\n            sx: {\n              mb: 3\n            },\n            children: \"Crea la prima comanda per iniziare a gestire i lavori\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 609,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(Button, {\n            variant: \"contained\",\n            startIcon: /*#__PURE__*/_jsxDEV(AddIcon, {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 614,\n              columnNumber: 28\n            }, this),\n            onClick: () => setOpenCreaConCavi(true),\n            sx: {\n              textTransform: 'none',\n              backgroundColor: '#2196f3',\n              color: 'white',\n              '&:hover': {\n                backgroundColor: '#1976d2'\n              }\n            },\n            children: \"Crea Prima Comanda\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 612,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 595,\n          columnNumber: 13\n        }, this) : /*#__PURE__*/_jsxDEV(ComandeListTable, {\n          comande: allComande,\n          onViewComanda: handleOpenComandaDialog.bind(null, 'view'),\n          onEditComanda: handleOpenComandaDialog.bind(null, 'edit'),\n          onDeleteComanda: handleDeleteComanda,\n          loading: loadingComande\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 629,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 541,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 540,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Dialog, {\n      open: openResponsabileDialog,\n      onClose: handleCloseResponsabileDialog,\n      maxWidth: \"sm\",\n      fullWidth: true,\n      PaperProps: {\n        sx: {\n          borderRadius: 2\n        }\n      },\n      children: [/*#__PURE__*/_jsxDEV(DialogTitle, {\n        sx: {\n          pb: 1\n        },\n        children: /*#__PURE__*/_jsxDEV(Typography, {\n          variant: \"h6\",\n          sx: {\n            fontWeight: 600\n          },\n          children: dialogModeResponsabile === 'create' ? 'Inserisci Responsabile' : 'Modifica Responsabile'\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 651,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 650,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(DialogContent, {\n        children: /*#__PURE__*/_jsxDEV(Box, {\n          sx: {\n            pt: 2\n          },\n          children: [/*#__PURE__*/_jsxDEV(TextField, {\n            fullWidth: true,\n            label: \"Nome Responsabile\",\n            value: formDataResponsabile.nome_responsabile,\n            onChange: e => setFormDataResponsabile({\n              ...formDataResponsabile,\n              nome_responsabile: e.target.value\n            }),\n            margin: \"normal\",\n            required: true,\n            variant: \"outlined\",\n            sx: {\n              mb: 2\n            }\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 657,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(TextField, {\n            fullWidth: true,\n            label: \"Email\",\n            type: \"email\",\n            value: formDataResponsabile.email,\n            onChange: e => setFormDataResponsabile({\n              ...formDataResponsabile,\n              email: e.target.value\n            }),\n            margin: \"normal\",\n            variant: \"outlined\",\n            helperText: \"Email per notifiche (opzionale se inserisci telefono)\",\n            sx: {\n              mb: 2\n            }\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 668,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(TextField, {\n            fullWidth: true,\n            label: \"Telefono\",\n            value: formDataResponsabile.telefono,\n            onChange: e => setFormDataResponsabile({\n              ...formDataResponsabile,\n              telefono: e.target.value\n            }),\n            margin: \"normal\",\n            variant: \"outlined\",\n            helperText: \"Numero per SMS (opzionale se inserisci email)\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 680,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 656,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 655,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(DialogActions, {\n        sx: {\n          p: 3,\n          pt: 2\n        },\n        children: [/*#__PURE__*/_jsxDEV(Button, {\n          onClick: handleCloseResponsabileDialog,\n          sx: {\n            textTransform: 'none'\n          },\n          children: \"Annulla\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 692,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Button, {\n          onClick: handleSubmitResponsabile,\n          variant: \"contained\",\n          sx: {\n            textTransform: 'none',\n            fontWeight: 500,\n            px: 3\n          },\n          children: dialogModeResponsabile === 'create' ? 'Crea' : 'Salva'\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 698,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 691,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 641,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Dialog, {\n      open: openComandaDialog,\n      onClose: handleCloseComandaDialog,\n      maxWidth: \"md\",\n      fullWidth: true,\n      PaperProps: {\n        sx: {\n          borderRadius: 2\n        }\n      },\n      children: [/*#__PURE__*/_jsxDEV(DialogTitle, {\n        sx: {\n          pb: 1\n        },\n        children: /*#__PURE__*/_jsxDEV(Typography, {\n          variant: \"h6\",\n          sx: {\n            fontWeight: 600\n          },\n          children: dialogModeComanda === 'view' ? 'Dettagli Comanda' : 'Modifica Comanda'\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 723,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 722,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(DialogContent, {\n        children: /*#__PURE__*/_jsxDEV(Box, {\n          sx: {\n            pt: 2\n          },\n          children: dialogModeComanda === 'view' && selectedComanda ? /*#__PURE__*/_jsxDEV(List, {\n            children: [/*#__PURE__*/_jsxDEV(ListItem, {\n              children: /*#__PURE__*/_jsxDEV(ListItemText, {\n                primary: \"Codice Comanda\",\n                secondary: selectedComanda.codice_comanda\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 732,\n                columnNumber: 19\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 731,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(Divider, {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 737,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(ListItem, {\n              children: /*#__PURE__*/_jsxDEV(ListItemText, {\n                primary: \"Tipo\",\n                secondary: getTipoComandaLabel(selectedComanda.tipo_comanda)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 739,\n                columnNumber: 19\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 738,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(Divider, {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 744,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(ListItem, {\n              children: /*#__PURE__*/_jsxDEV(ListItemText, {\n                primary: \"Stato\",\n                secondary: /*#__PURE__*/_jsxDEV(Chip, {\n                  label: selectedComanda.stato,\n                  color: getStatoColor(selectedComanda.stato),\n                  size: \"small\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 749,\n                  columnNumber: 23\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 746,\n                columnNumber: 19\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 745,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(Divider, {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 757,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(ListItem, {\n              children: /*#__PURE__*/_jsxDEV(ListItemText, {\n                primary: \"Descrizione\",\n                secondary: selectedComanda.descrizione || 'Nessuna descrizione'\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 759,\n                columnNumber: 19\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 758,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(Divider, {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 764,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(ListItem, {\n              children: /*#__PURE__*/_jsxDEV(ListItemText, {\n                primary: \"Responsabile\",\n                secondary: selectedComanda.responsabile || 'Non assegnato'\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 766,\n                columnNumber: 19\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 765,\n              columnNumber: 17\n            }, this), selectedComanda.note_capo_cantiere && /*#__PURE__*/_jsxDEV(_Fragment, {\n              children: [/*#__PURE__*/_jsxDEV(Divider, {}, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 773,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(ListItem, {\n                children: /*#__PURE__*/_jsxDEV(ListItemText, {\n                  primary: \"Note Capo Cantiere\",\n                  secondary: selectedComanda.note_capo_cantiere\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 775,\n                  columnNumber: 23\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 774,\n                columnNumber: 21\n              }, this)]\n            }, void 0, true), /*#__PURE__*/_jsxDEV(Divider, {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 782,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(ListItem, {\n              children: /*#__PURE__*/_jsxDEV(ListItemText, {\n                primary: \"Data Creazione\",\n                secondary: new Date(selectedComanda.data_creazione).toLocaleDateString('it-IT')\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 784,\n                columnNumber: 19\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 783,\n              columnNumber: 17\n            }, this), selectedComanda.data_scadenza && /*#__PURE__*/_jsxDEV(_Fragment, {\n              children: [/*#__PURE__*/_jsxDEV(Divider, {}, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 791,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(ListItem, {\n                children: /*#__PURE__*/_jsxDEV(ListItemText, {\n                  primary: \"Data Scadenza\",\n                  secondary: new Date(selectedComanda.data_scadenza).toLocaleDateString('it-IT')\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 793,\n                  columnNumber: 23\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 792,\n                columnNumber: 21\n              }, this)]\n            }, void 0, true), /*#__PURE__*/_jsxDEV(Divider, {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 800,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(ListItem, {\n              children: /*#__PURE__*/_jsxDEV(ListItemText, {\n                primary: \"Cavi Assegnati\",\n                secondary: selectedComanda.numero_cavi_assegnati || 0\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 802,\n                columnNumber: 19\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 801,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(Divider, {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 807,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(ListItem, {\n              children: /*#__PURE__*/_jsxDEV(ListItemText, {\n                primary: \"Completamento\",\n                secondary: `${(selectedComanda.percentuale_completamento || 0).toFixed(1)}%`\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 809,\n                columnNumber: 19\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 808,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 730,\n            columnNumber: 15\n          }, this) : /*#__PURE__*/_jsxDEV(_Fragment, {\n            children: [/*#__PURE__*/_jsxDEV(TextField, {\n              fullWidth: true,\n              select: true,\n              label: \"Tipo Comanda\",\n              value: formDataComanda.tipo_comanda,\n              onChange: e => setFormDataComanda({\n                ...formDataComanda,\n                tipo_comanda: e.target.value\n              }),\n              margin: \"normal\",\n              sx: {\n                mb: 2\n              },\n              children: [/*#__PURE__*/_jsxDEV(MenuItem, {\n                value: \"POSA\",\n                children: \"Posa\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 826,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(MenuItem, {\n                value: \"COLLEGAMENTO_PARTENZA\",\n                children: \"Collegamento Partenza\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 827,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(MenuItem, {\n                value: \"COLLEGAMENTO_ARRIVO\",\n                children: \"Collegamento Arrivo\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 828,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(MenuItem, {\n                value: \"CERTIFICAZIONE\",\n                children: \"Certificazione\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 829,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(MenuItem, {\n                value: \"TESTING\",\n                children: \"Testing\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 830,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 817,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(TextField, {\n              fullWidth: true,\n              label: \"Descrizione\",\n              value: formDataComanda.descrizione,\n              onChange: e => setFormDataComanda({\n                ...formDataComanda,\n                descrizione: e.target.value\n              }),\n              margin: \"normal\",\n              multiline: true,\n              rows: 3,\n              sx: {\n                mb: 2\n              }\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 835,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(TextField, {\n              fullWidth: true,\n              label: \"Responsabile\",\n              value: formDataComanda.responsabile,\n              onChange: e => setFormDataComanda({\n                ...formDataComanda,\n                responsabile: e.target.value\n              }),\n              margin: \"normal\",\n              required: true,\n              helperText: \"Chi eseguir\\xE0 il lavoro (obbligatorio)\",\n              sx: {\n                mb: 2\n              }\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 846,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(TextField, {\n              fullWidth: true,\n              label: \"Note Capo Cantiere\",\n              value: formDataComanda.note_capo_cantiere,\n              onChange: e => setFormDataComanda({\n                ...formDataComanda,\n                note_capo_cantiere: e.target.value\n              }),\n              margin: \"normal\",\n              multiline: true,\n              rows: 2,\n              helperText: \"Istruzioni specifiche per il responsabile\",\n              sx: {\n                mb: 2\n              }\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 857,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(TextField, {\n              fullWidth: true,\n              label: \"Data Scadenza\",\n              type: \"date\",\n              value: formDataComanda.data_scadenza,\n              onChange: e => setFormDataComanda({\n                ...formDataComanda,\n                data_scadenza: e.target.value\n              }),\n              margin: \"normal\",\n              InputLabelProps: {\n                shrink: true\n              }\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 869,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 728,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 727,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(DialogActions, {\n        sx: {\n          p: 3,\n          pt: 2\n        },\n        children: [/*#__PURE__*/_jsxDEV(Button, {\n          onClick: handleCloseComandaDialog,\n          sx: {\n            textTransform: 'none'\n          },\n          children: dialogModeComanda === 'view' ? 'Chiudi' : 'Annulla'\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 885,\n          columnNumber: 11\n        }, this), dialogModeComanda === 'edit' && /*#__PURE__*/_jsxDEV(Button, {\n          onClick: handleSubmitComanda,\n          variant: \"contained\",\n          sx: {\n            textTransform: 'none',\n            fontWeight: 500,\n            px: 3\n          },\n          children: \"Salva Modifiche\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 892,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 884,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 713,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(CreaComandaConCavi, {\n      cantiereId: cantiereId,\n      open: openCreaConCavi,\n      onClose: () => setOpenCreaConCavi(false),\n      onSuccess: (response, successMessage) => {\n        console.log('🎉 Comanda creata, aggiornamento interfaccia...');\n\n        // Mostra messaggio di successo se fornito\n        if (successMessage) {\n          // Potresti aggiungere qui un toast/snackbar per mostrare il messaggio\n          console.log('📢 Successo:', successMessage);\n        }\n\n        // Ricarica tutti i dati per aggiornare l'interfaccia\n        loadComande();\n        loadStatistiche();\n        loadResponsabili();\n        setOpenCreaConCavi(false);\n        console.log('✅ Interfaccia aggiornata');\n      }\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 908,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(ResponsabiliListPopup, {\n      open: openResponsabiliPopup,\n      onClose: () => setOpenResponsabiliPopup(false),\n      responsabili: responsabili,\n      comandePerResponsabile: comandePerResponsabile,\n      onEditResponsabile: responsabile => {\n        setOpenResponsabiliPopup(false);\n        handleOpenResponsabileDialog('edit', responsabile);\n      },\n      onDeleteResponsabile: async idResponsabile => {\n        await handleDeleteResponsabile(idResponsabile);\n        setOpenResponsabiliPopup(false);\n      },\n      loading: loadingResponsabili,\n      error: error\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 932,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 434,\n    columnNumber: 5\n  }, this);\n};\n_s(ComandeListRivoluzionato, \"1nCJLogCYo5BCWX8l5bTwFEVVH8=\", false, function () {\n  return [useSearchParams];\n});\n_c = ComandeListRivoluzionato;\nexport default ComandeListRivoluzionato;\nvar _c;\n$RefreshReg$(_c, \"ComandeListRivoluzionato\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "useSearchParams", "Box", "Typography", "<PERSON><PERSON>", "Paper", "Chip", "Dialog", "DialogTitle", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "DialogActions", "TextField", "<PERSON><PERSON>", "CircularProgress", "List", "ListItem", "ListItemText", "<PERSON><PERSON>", "MenuItem", "Divider", "Add", "AddIcon", "Assignment", "AssignIcon", "Person", "PersonIcon", "CheckCircle", "CheckCircleIcon", "Verified", "VerifiedIcon", "People", "PeopleIcon", "Construction", "ConstructionIcon", "comandeService", "responsabiliService", "CreaComandaConCavi", "ResponsabiliListPopup", "ComandeListTable", "InserimentoMetriDialog", "jsxDEV", "_jsxDEV", "Fragment", "_Fragment", "ComandeListRivoluzionato", "cantiereId", "cantiereName", "_s", "searchParams", "setSearchParams", "loading", "setLoading", "error", "setError", "searchingComanda", "setSearchingComanda", "statistiche", "setStatistiche", "allComande", "setAllComande", "loadingComande", "setLoadingComande", "openResponsabiliPopup", "setOpenResponsabiliPopup", "openCreaConCavi", "setOpenCreaConCavi", "responsabili", "setResponsabili", "loadingResponsabili", "setLoadingResponsabili", "comandePerResponsabile", "setComandePerResponsabile", "openResponsabileDialog", "setOpenResponsabileDialog", "dialogModeResponsabile", "setDialogModeResponsabile", "selectedResponsabile", "setSelectedResponsabile", "formDataResponsabile", "setFormDataResponsabile", "nome_responsabile", "email", "telefono", "openComandaDialog", "setOpenComandaDialog", "dialogModeComanda", "setDialogModeComanda", "selectedComanda", "setSelectedComanda", "formDataComanda", "setFormDataComanda", "tipo_comanda", "descrizione", "responsabile", "data_scadenza", "note_capo_cantiere", "openInserimentoMetri", "setOpenInserimentoMetri", "comandaPerMetri", "setComandaPerMetri", "loadComande", "console", "log", "comandeData", "getComande", "Array", "isArray", "comandeArray", "comande", "data", "length", "err", "loadStatistiche", "stats", "getStatisticheComande", "_err$response", "response", "message", "loadResponsabili", "getResponsabiliCantiere", "loadComandePerResponsabili", "_err$response2", "_err$response2$data", "errorMessage", "detail", "initializeData", "Promise", "all", "comandaParam", "get", "Object", "keys", "comandaTrovata", "comandeResp", "id_responsabile", "find", "c", "codice_comanda", "handleOpenComandaDialog", "setTimeout", "prev", "newParams", "URLSearchParams", "delete", "warn", "for<PERSON>ach", "resp", "cmd", "responsabiliList", "comandeMap", "getComandeByResponsabile", "handleOpenResponsabileDialog", "mode", "handleCloseResponsabileDialog", "handleSubmitResponsabile", "trim", "createResponsabile", "updateResponsabile", "handleDeleteResponsabile", "idResponsabile", "window", "confirm", "deleteResponsabile", "comanda", "handleCloseComandaDialog", "handleSubmitComanda", "updateComanda", "handleDeleteComanda", "codiceComanda", "deleteComanda", "getTipoComandaLabel", "tipo", "labels", "getStatoColor", "stato", "colors", "display", "justifyContent", "alignItems", "minHeight", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "sx", "p", "mb", "variant", "fontWeight", "color", "severity", "bgcolor", "direction", "spacing", "flexWrap", "fontSize", "lineHeight", "responsabili_attivi", "totale_comande", "comande_in_corso", "comande_completate", "width", "height", "borderRadius", "Math", "round", "comande_create", "gap", "startIcon", "onClick", "textTransform", "px", "py", "backgroundColor", "border", "borderColor", "elevation", "textAlign", "gutterBottom", "onViewComanda", "bind", "onEditComanda", "onDeleteComanda", "open", "onClose", "max<PERSON><PERSON><PERSON>", "fullWidth", "PaperProps", "pb", "pt", "label", "value", "onChange", "e", "target", "margin", "required", "type", "helperText", "primary", "secondary", "size", "Date", "data_creazione", "toLocaleDateString", "numero_cavi_assegnati", "percentuale_completamento", "toFixed", "select", "multiline", "rows", "InputLabelProps", "shrink", "onSuccess", "successMessage", "onEditResponsabile", "onDeleteResponsabile", "_c", "$RefreshReg$"], "sources": ["C:/CMS/webapp/frontend/src/components/comande/ComandeListRivoluzionato.js"], "sourcesContent": ["import React, { useState, useEffect } from 'react';\nimport { useSearchParams } from 'react-router-dom';\nimport {\n  Box,\n  Typography,\n  Button,\n  Paper,\n  Chip,\n  Dialog,\n  DialogTitle,\n  DialogContent,\n  DialogActions,\n  TextField,\n  Alert,\n  CircularProgress,\n  List,\n  ListItem,\n  ListItemText,\n  Stack,\n  MenuItem,\n  Divider\n} from '@mui/material';\nimport {\n  Add as AddIcon,\n  Assignment as AssignIcon,\n  Person as PersonIcon,\n  CheckCircle as CheckCircleIcon,\n  Verified as VerifiedIcon,\n  People as PeopleIcon,\n  Construction as ConstructionIcon\n} from '@mui/icons-material';\nimport comandeService from '../../services/comandeService';\nimport responsabiliService from '../../services/responsabiliService';\nimport CreaComandaConCavi from './CreaComandaConCavi';\nimport ResponsabiliListPopup from './ResponsabiliListPopup';\nimport ComandeListTable from './ComandeListTable';\nimport InserimentoMetriDialog from './InserimentoMetriDialog';\n\nconst ComandeListRivoluzionato = ({ cantiereId, cantiereName }) => {\n  // Hook per gestire i parametri URL\n  const [searchParams, setSearchParams] = useSearchParams();\n\n  // Stati principali - Responsabili come elemento principale\n  const [loading, setLoading] = useState(true);\n  const [error, setError] = useState(null);\n  const [searchingComanda, setSearchingComanda] = useState(null);\n\n  // Stati comande\n  const [statistiche, setStatistiche] = useState(null);\n  const [allComande, setAllComande] = useState([]);\n  const [loadingComande, setLoadingComande] = useState(false);\n\n  // Stati per popup responsabili\n  const [openResponsabiliPopup, setOpenResponsabiliPopup] = useState(false);\n  const [openCreaConCavi, setOpenCreaConCavi] = useState(false);\n\n  // Stati responsabili\n  const [responsabili, setResponsabili] = useState([]);\n  const [loadingResponsabili, setLoadingResponsabili] = useState(false);\n  const [comandePerResponsabile, setComandePerResponsabile] = useState({});\n  const [openResponsabileDialog, setOpenResponsabileDialog] = useState(false);\n  const [dialogModeResponsabile, setDialogModeResponsabile] = useState('create');\n  const [selectedResponsabile, setSelectedResponsabile] = useState(null);\n  const [formDataResponsabile, setFormDataResponsabile] = useState({\n    nome_responsabile: '',\n    email: '',\n    telefono: ''\n  });\n\n  // Stati per dialog comande\n  const [openComandaDialog, setOpenComandaDialog] = useState(false);\n  const [dialogModeComanda, setDialogModeComanda] = useState('view'); // 'view', 'edit'\n  const [selectedComanda, setSelectedComanda] = useState(null);\n  const [formDataComanda, setFormDataComanda] = useState({\n    tipo_comanda: 'POSA',\n    descrizione: '',\n    responsabile: '',\n    data_scadenza: '',\n    note_capo_cantiere: ''\n  });\n\n  // Stati per dialog inserimento metri\n  const [openInserimentoMetri, setOpenInserimentoMetri] = useState(false);\n  const [comandaPerMetri, setComandaPerMetri] = useState(null);\n\n  const loadComande = async () => {\n    try {\n      setLoadingComande(true);\n      console.log('🔄 Caricamento comande per cantiere:', cantiereId);\n      const comandeData = await comandeService.getComande(cantiereId);\n      console.log('📋 Dati comande ricevuti:', comandeData);\n      console.log('📋 Tipo dati:', typeof comandeData, 'Array?', Array.isArray(comandeData));\n\n      // Gestisci diversi formati di risposta\n      let comandeArray = [];\n      if (Array.isArray(comandeData)) {\n        comandeArray = comandeData;\n      } else if (comandeData && Array.isArray(comandeData.comande)) {\n        comandeArray = comandeData.comande;\n      } else if (comandeData && Array.isArray(comandeData.data)) {\n        comandeArray = comandeData.data;\n      }\n\n      console.log('📋 Array comande finale:', comandeArray, 'Lunghezza:', comandeArray.length);\n      setAllComande(comandeArray);\n    } catch (err) {\n      console.error('Errore nel caricamento comande:', err);\n      setError('Errore nel caricamento delle comande');\n    } finally {\n      setLoadingComande(false);\n    }\n  };\n\n  const loadStatistiche = async () => {\n    try {\n      console.log('🔄 Caricamento statistiche per cantiere:', cantiereId);\n      const stats = await comandeService.getStatisticheComande(cantiereId);\n      console.log('📊 Statistiche ricevute:', stats);\n      setStatistiche(stats);\n    } catch (err) {\n      console.error('❌ Errore nel caricamento delle statistiche:', err);\n      console.error('❌ Dettagli errore:', err.response?.data || err.message);\n    }\n  };\n\n  const loadResponsabili = async () => {\n    try {\n      setLoadingResponsabili(true);\n      setError(null);\n\n      const data = await responsabiliService.getResponsabiliCantiere(cantiereId);\n      setResponsabili(data || []);\n      await loadComandePerResponsabili(data || []);\n    } catch (err) {\n      console.error('Errore nel caricamento dei responsabili:', err);\n      const errorMessage = err.response?.data?.detail || err.message || 'Errore nel caricamento dei responsabili';\n      setError(`Errore nel caricamento dei responsabili: ${errorMessage}`);\n    } finally {\n      setLoadingResponsabili(false);\n    }\n  };\n\n\n\n  // Carica dati al mount - Focus sui responsabili\n  useEffect(() => {\n    const initializeData = async () => {\n      if (cantiereId) {\n        setLoading(true);\n        try {\n          await Promise.all([\n            loadResponsabili(),\n            loadComande(),\n            loadStatistiche()\n          ]);\n        } catch (err) {\n          console.error('Errore nel caricamento iniziale:', err);\n        } finally {\n          setLoading(false);\n        }\n      }\n    };\n\n    initializeData();\n  }, [cantiereId]); // eslint-disable-line react-hooks/exhaustive-deps\n\n  // Gestione parametro URL per aprire comanda specifica\n  useEffect(() => {\n    const comandaParam = searchParams.get('comanda');\n    console.log('🔍 Controllo parametro URL comanda:', comandaParam);\n    console.log('📊 Stato dati:', {\n      responsabili: responsabili.length,\n      comandePerResponsabile: Object.keys(comandePerResponsabile).length,\n      loading,\n      loadingResponsabili\n    });\n\n    // Imposta lo stato di ricerca\n    if (comandaParam && comandaParam !== searchingComanda) {\n      setSearchingComanda(comandaParam);\n    }\n\n    if (comandaParam && responsabili.length > 0 && Object.keys(comandePerResponsabile).length > 0) {\n      console.log('🔎 Ricerca comanda tra i responsabili...');\n\n      // Cerca la comanda tra tutti i responsabili\n      let comandaTrovata = null;\n\n      for (const responsabile of responsabili) {\n        const comandeResp = comandePerResponsabile[responsabile.id_responsabile] || [];\n        console.log(`📋 Responsabile ${responsabile.nome_responsabile}: ${comandeResp.length} comande`);\n        comandaTrovata = comandeResp.find(c => c.codice_comanda === comandaParam);\n        if (comandaTrovata) {\n          console.log('✅ Comanda trovata:', comandaTrovata);\n          break;\n        }\n      }\n\n      if (comandaTrovata) {\n        console.log('🎯 Apertura automatica comanda da URL:', comandaParam);\n        setSearchingComanda(null); // Rimuovi lo stato di ricerca\n        handleOpenComandaDialog('view', comandaTrovata);\n        // Rimuovi il parametro dall'URL per evitare riaperture\n        setTimeout(() => {\n          setSearchParams(prev => {\n            const newParams = new URLSearchParams(prev);\n            newParams.delete('comanda');\n            return newParams;\n          });\n        }, 100);\n      } else {\n        console.warn('⚠️ Comanda non trovata:', comandaParam);\n        console.log('📋 Comande disponibili:');\n        responsabili.forEach(resp => {\n          const comande = comandePerResponsabile[resp.id_responsabile] || [];\n          comande.forEach(cmd => {\n            console.log(`  - ${cmd.codice_comanda} (${resp.nome_responsabile})`);\n          });\n        });\n\n        // Se non troviamo la comanda, potrebbe essere che i dati non sono completi\n        // Riprova dopo un breve delay\n        if (!loading && !loadingResponsabili) {\n          console.log('🔄 Tentativo di ricaricamento dati...');\n          setTimeout(() => {\n            loadResponsabili();\n          }, 500);\n        }\n      }\n    } else if (comandaParam) {\n      console.log('⏳ Dati non ancora caricati, attendo...');\n\n      // Se abbiamo un parametro ma i dati non sono caricati, forza il caricamento\n      if (!loading && !loadingResponsabili && responsabili.length === 0) {\n        console.log('🚀 Forzatura caricamento responsabili...');\n        loadResponsabili();\n      }\n    }\n  }, [searchParams, responsabili, comandePerResponsabile, loading, loadingResponsabili]); // eslint-disable-line react-hooks/exhaustive-deps\n\n  const loadComandePerResponsabili = async (responsabiliList) => {\n    try {\n      const comandeMap = {};\n      for (const responsabile of responsabiliList) {\n        try {\n          const response = await comandeService.getComandeByResponsabile(cantiereId, responsabile.nome_responsabile);\n          // Assicurati che sia sempre un array\n          let comande = [];\n          if (response && Array.isArray(response)) {\n            comande = response;\n          } else if (response && response.comande && Array.isArray(response.comande)) {\n            comande = response.comande;\n          } else if (response && response.data && Array.isArray(response.data)) {\n            comande = response.data;\n          }\n          comandeMap[responsabile.id_responsabile] = comande;\n        } catch (err) {\n          console.error(`Errore nel caricamento comande per ${responsabile.nome_responsabile}:`, err);\n          comandeMap[responsabile.id_responsabile] = [];\n        }\n      }\n      setComandePerResponsabile(comandeMap);\n    } catch (err) {\n      console.error('Errore nel caricamento delle comande:', err);\n    }\n  };\n\n  // Gestione responsabili\n  const handleOpenResponsabileDialog = (mode, responsabile = null) => {\n    setDialogModeResponsabile(mode);\n    setSelectedResponsabile(responsabile);\n    \n    if (mode === 'edit' && responsabile) {\n      setFormDataResponsabile({\n        nome_responsabile: responsabile.nome_responsabile || '',\n        email: responsabile.email || '',\n        telefono: responsabile.telefono || ''\n      });\n    } else {\n      setFormDataResponsabile({\n        nome_responsabile: '',\n        email: '',\n        telefono: ''\n      });\n    }\n    \n    setOpenResponsabileDialog(true);\n  };\n\n  const handleCloseResponsabileDialog = () => {\n    setOpenResponsabileDialog(false);\n    setSelectedResponsabile(null);\n    setError(null);\n  };\n\n  const handleSubmitResponsabile = async () => {\n    try {\n      setError(null);\n      \n      if (!formDataResponsabile.nome_responsabile.trim()) {\n        setError('Il nome del responsabile è obbligatorio');\n        return;\n      }\n      \n      if (!formDataResponsabile.email && !formDataResponsabile.telefono) {\n        setError('Almeno uno tra email e telefono deve essere specificato');\n        return;\n      }\n\n      if (dialogModeResponsabile === 'create') {\n        await responsabiliService.createResponsabile(cantiereId, formDataResponsabile);\n      } else if (dialogModeResponsabile === 'edit') {\n        await responsabiliService.updateResponsabile(selectedResponsabile.id_responsabile, formDataResponsabile);\n      }\n\n      handleCloseResponsabileDialog();\n      await loadResponsabili();\n      await loadComande();\n    } catch (err) {\n      console.error('Errore nel salvataggio:', err);\n      setError(err.detail || 'Errore nel salvataggio del responsabile');\n    }\n  };\n\n  const handleDeleteResponsabile = async (idResponsabile) => {\n    if (!window.confirm('Sei sicuro di voler eliminare questo responsabile?')) {\n      return;\n    }\n\n    try {\n      await responsabiliService.deleteResponsabile(idResponsabile);\n      await loadResponsabili();\n      await loadComande();\n    } catch (err) {\n      console.error('Errore nell\\'eliminazione:', err);\n      setError('Errore nell\\'eliminazione del responsabile');\n    }\n  };\n\n  // Gestione comande\n  const handleOpenComandaDialog = (mode, comanda = null) => {\n    setDialogModeComanda(mode);\n    setSelectedComanda(comanda);\n\n    if (mode === 'edit' && comanda) {\n      setFormDataComanda({\n        tipo_comanda: comanda.tipo_comanda,\n        descrizione: comanda.descrizione || '',\n        responsabile: comanda.responsabile || '',\n        data_scadenza: comanda.data_scadenza || '',\n        note_capo_cantiere: comanda.note_capo_cantiere || ''\n      });\n    }\n\n    setOpenComandaDialog(true);\n  };\n\n  const handleCloseComandaDialog = () => {\n    setOpenComandaDialog(false);\n    setSelectedComanda(null);\n    setFormDataComanda({\n      tipo_comanda: 'POSA',\n      descrizione: '',\n      responsabile: '',\n      data_scadenza: '',\n      note_capo_cantiere: ''\n    });\n  };\n\n  const handleSubmitComanda = async () => {\n    try {\n      if (dialogModeComanda === 'edit') {\n        await comandeService.updateComanda(selectedComanda.codice_comanda, formDataComanda);\n        handleCloseComandaDialog();\n        await loadResponsabili(); // Ricarica per aggiornare le comande\n        await loadComande();\n        await loadStatistiche();\n      }\n    } catch (err) {\n      console.error('Errore nel salvataggio:', err);\n      setError('Errore nel salvataggio della comanda');\n    }\n  };\n\n  const handleDeleteComanda = async (codiceComanda) => {\n    if (!window.confirm('Sei sicuro di voler eliminare questa comanda?')) {\n      return;\n    }\n\n    try {\n      await comandeService.deleteComanda(codiceComanda);\n      await loadResponsabili(); // Ricarica per aggiornare le comande\n      await loadComande();\n      await loadStatistiche();\n    } catch (err) {\n      console.error('Errore nell\\'eliminazione:', err);\n      setError('Errore nell\\'eliminazione della comanda');\n    }\n  };\n\n  const getTipoComandaLabel = (tipo) => {\n    const labels = {\n      'POSA': 'Posa',\n      'COLLEGAMENTO_PARTENZA': 'Coll. Partenza',\n      'COLLEGAMENTO_ARRIVO': 'Coll. Arrivo',\n      'CERTIFICAZIONE': 'Certificazione',\n      'TESTING': 'Testing/Certificazione'\n    };\n    return labels[tipo] || tipo;\n  };\n\n  const getStatoColor = (stato) => {\n    const colors = {\n      'CREATA': 'default',\n      'ASSEGNATA': 'primary',\n      'IN_CORSO': 'warning',\n      'COMPLETATA': 'success',\n      'ANNULLATA': 'error'\n    };\n    return colors[stato] || 'default';\n  };\n\n\n\n  if (loading || loadingComande) {\n    return (\n      <Box display=\"flex\" justifyContent=\"center\" alignItems=\"center\" minHeight=\"400px\">\n        <CircularProgress />\n      </Box>\n    );\n  }\n\n  return (\n    <Box sx={{ p: 3 }}>\n      {/* Header */}\n      <Box mb={3}>\n        <Typography variant=\"h4\" sx={{ fontWeight: 600, color: 'primary.main' }}>\n          {cantiereName}\n        </Typography>\n      </Box>\n\n      {error && (\n        <Alert severity=\"error\" sx={{ mb: 2 }}>\n          {error}\n        </Alert>\n      )}\n\n      {searchingComanda && (\n        <Alert severity=\"info\" sx={{ mb: 2 }}>\n          🔍 Ricerca comanda {searchingComanda} in corso...\n        </Alert>\n      )}\n\n      {/* Statistiche in stile Visualizza Cavi */}\n      {statistiche && (\n        <Paper sx={{ p: 2, mb: 3, bgcolor: 'grey.50' }}>\n          <Stack direction=\"row\" spacing={4} alignItems=\"center\" justifyContent=\"space-between\" flexWrap=\"wrap\">\n            {/* Responsabili */}\n            <Stack direction=\"row\" alignItems=\"center\" spacing={1}>\n              <PersonIcon color=\"primary\" fontSize=\"small\" />\n              <Box>\n                <Typography variant=\"h6\" fontWeight=\"bold\" sx={{ lineHeight: 1 }}>\n                  {statistiche.responsabili_attivi || 0}\n                </Typography>\n                <Typography variant=\"caption\" color=\"text.secondary\">\n                  Responsabili\n                </Typography>\n              </Box>\n            </Stack>\n\n            {/* Totale Comande */}\n            <Stack direction=\"row\" alignItems=\"center\" spacing={1}>\n              <AssignIcon color=\"info\" fontSize=\"small\" />\n              <Box>\n                <Typography variant=\"h6\" fontWeight=\"bold\" sx={{ lineHeight: 1 }}>\n                  {statistiche.totale_comande || 0}\n                </Typography>\n                <Typography variant=\"caption\" color=\"text.secondary\">\n                  Totale\n                </Typography>\n              </Box>\n            </Stack>\n\n            {/* In Corso */}\n            <Stack direction=\"row\" alignItems=\"center\" spacing={1}>\n              <CheckCircleIcon color=\"warning\" fontSize=\"small\" />\n              <Box>\n                <Typography variant=\"h6\" fontWeight=\"bold\" sx={{ lineHeight: 1 }}>\n                  {statistiche.comande_in_corso || 0}\n                </Typography>\n                <Typography variant=\"caption\" color=\"text.secondary\">\n                  In Corso\n                </Typography>\n              </Box>\n            </Stack>\n\n            {/* Completate */}\n            <Stack direction=\"row\" alignItems=\"center\" spacing={1}>\n              <VerifiedIcon color=\"success\" fontSize=\"small\" />\n              <Box>\n                <Typography variant=\"h6\" fontWeight=\"bold\" sx={{ lineHeight: 1 }}>\n                  {statistiche.comande_completate || 0}\n                </Typography>\n                <Typography variant=\"caption\" color=\"text.secondary\">\n                  Completate\n                </Typography>\n              </Box>\n            </Stack>\n\n            {/* Percentuale completamento */}\n            <Stack direction=\"row\" alignItems=\"center\" spacing={1}>\n              <Box sx={{\n                width: 32,\n                height: 32,\n                borderRadius: '50%',\n                bgcolor: (statistiche.comande_completate / (statistiche.totale_comande || 1)) >= 0.8 ? 'success.main' :\n                         (statistiche.comande_completate / (statistiche.totale_comande || 1)) >= 0.5 ? 'warning.main' : 'error.main',\n                display: 'flex',\n                alignItems: 'center',\n                justifyContent: 'center'\n              }}>\n                <Typography variant=\"caption\" fontWeight=\"bold\" color=\"white\">\n                  {statistiche.totale_comande > 0 ? Math.round((statistiche.comande_completate / statistiche.totale_comande) * 100) : 0}%\n                </Typography>\n              </Box>\n              <Box>\n                <Typography variant=\"body2\" fontWeight=\"medium\" sx={{ lineHeight: 1 }}>\n                  Completamento\n                </Typography>\n                <Typography variant=\"caption\" color=\"text.secondary\">\n                  {statistiche.comande_create || 0} create\n                </Typography>\n              </Box>\n            </Stack>\n          </Stack>\n        </Paper>\n      )}\n\n      {/* Sezione Comande - Elemento Principale */}\n      <Box>\n        <Box>\n          {/* Toolbar Comande */}\n          <Box display=\"flex\" justifyContent=\"space-between\" alignItems=\"center\" mb={3}>\n            <Typography variant=\"h5\" sx={{ fontWeight: 500, color: 'text.primary' }}>\n              Gestione Responsabili e Comande\n            </Typography>\n            <Box display=\"flex\" gap={2}>\n              <Button\n                variant=\"outlined\"\n                startIcon={<PeopleIcon />}\n                onClick={() => setOpenResponsabiliPopup(true)}\n                sx={{\n                  textTransform: 'none',\n                  fontWeight: 500,\n                  px: 3,\n                  py: 1,\n                  backgroundColor: '#f5f7fa',\n                  color: '#2196f3',\n                  border: '1px solid #2196f3',\n                  '&:hover': {\n                    backgroundColor: 'rgba(33, 150, 243, 0.1)',\n                    borderColor: '#1976d2'\n                  }\n                }}\n              >\n                Lista Responsabili\n              </Button>\n              <Button\n                variant=\"contained\"\n                startIcon={<AddIcon />}\n                onClick={() => handleOpenResponsabileDialog('create')}\n                sx={{\n                  textTransform: 'none',\n                  fontWeight: 500,\n                  px: 3,\n                  py: 1,\n                  backgroundColor: '#2196f3',\n                  color: 'white',\n                  '&:hover': {\n                    backgroundColor: '#1976d2'\n                  }\n                }}\n              >\n                Crea Responsabile\n              </Button>\n            </Box>\n          </Box>\n\n          {/* Lista Comande in stile tabella */}\n          {loadingComande ? (\n            <Box display=\"flex\" justifyContent=\"center\" py={4}>\n              <CircularProgress />\n            </Box>\n          ) : allComande.length === 0 ? (\n            <Paper\n              elevation={0}\n              sx={{\n                p: 6,\n                textAlign: 'center',\n                backgroundColor: 'grey.50',\n                border: '1px dashed',\n                borderColor: 'grey.300'\n              }}\n            >\n              <AssignIcon sx={{ fontSize: 48, color: 'grey.400', mb: 2 }} />\n              <Typography variant=\"h6\" color=\"text.secondary\" gutterBottom>\n                Nessuna comanda disponibile\n              </Typography>\n              <Typography variant=\"body2\" color=\"text.secondary\" sx={{ mb: 3 }}>\n                Crea la prima comanda per iniziare a gestire i lavori\n              </Typography>\n              <Button\n                variant=\"contained\"\n                startIcon={<AddIcon />}\n                onClick={() => setOpenCreaConCavi(true)}\n                sx={{\n                  textTransform: 'none',\n                  backgroundColor: '#2196f3',\n                  color: 'white',\n                  '&:hover': {\n                    backgroundColor: '#1976d2'\n                  }\n                }}\n              >\n                Crea Prima Comanda\n              </Button>\n            </Paper>\n          ) : (\n            <ComandeListTable\n              comande={allComande}\n              onViewComanda={handleOpenComandaDialog.bind(null, 'view')}\n              onEditComanda={handleOpenComandaDialog.bind(null, 'edit')}\n              onDeleteComanda={handleDeleteComanda}\n              loading={loadingComande}\n            />\n          )}\n        </Box>\n      </Box>\n\n      {/* Dialog per creazione/modifica responsabile */}\n      <Dialog\n        open={openResponsabileDialog}\n        onClose={handleCloseResponsabileDialog}\n        maxWidth=\"sm\"\n        fullWidth\n        PaperProps={{\n          sx: { borderRadius: 2 }\n        }}\n      >\n        <DialogTitle sx={{ pb: 1 }}>\n          <Typography variant=\"h6\" sx={{ fontWeight: 600 }}>\n            {dialogModeResponsabile === 'create' ? 'Inserisci Responsabile' : 'Modifica Responsabile'}\n          </Typography>\n        </DialogTitle>\n        <DialogContent>\n          <Box sx={{ pt: 2 }}>\n            <TextField\n              fullWidth\n              label=\"Nome Responsabile\"\n              value={formDataResponsabile.nome_responsabile}\n              onChange={(e) => setFormDataResponsabile({ ...formDataResponsabile, nome_responsabile: e.target.value })}\n              margin=\"normal\"\n              required\n              variant=\"outlined\"\n              sx={{ mb: 2 }}\n            />\n\n            <TextField\n              fullWidth\n              label=\"Email\"\n              type=\"email\"\n              value={formDataResponsabile.email}\n              onChange={(e) => setFormDataResponsabile({ ...formDataResponsabile, email: e.target.value })}\n              margin=\"normal\"\n              variant=\"outlined\"\n              helperText=\"Email per notifiche (opzionale se inserisci telefono)\"\n              sx={{ mb: 2 }}\n            />\n\n            <TextField\n              fullWidth\n              label=\"Telefono\"\n              value={formDataResponsabile.telefono}\n              onChange={(e) => setFormDataResponsabile({ ...formDataResponsabile, telefono: e.target.value })}\n              margin=\"normal\"\n              variant=\"outlined\"\n              helperText=\"Numero per SMS (opzionale se inserisci email)\"\n            />\n          </Box>\n        </DialogContent>\n        <DialogActions sx={{ p: 3, pt: 2 }}>\n          <Button\n            onClick={handleCloseResponsabileDialog}\n            sx={{ textTransform: 'none' }}\n          >\n            Annulla\n          </Button>\n          <Button\n            onClick={handleSubmitResponsabile}\n            variant=\"contained\"\n            sx={{\n              textTransform: 'none',\n              fontWeight: 500,\n              px: 3\n            }}\n          >\n            {dialogModeResponsabile === 'create' ? 'Crea' : 'Salva'}\n          </Button>\n        </DialogActions>\n      </Dialog>\n\n      {/* Dialog per visualizzazione/modifica comanda */}\n      <Dialog\n        open={openComandaDialog}\n        onClose={handleCloseComandaDialog}\n        maxWidth=\"md\"\n        fullWidth\n        PaperProps={{\n          sx: { borderRadius: 2 }\n        }}\n      >\n        <DialogTitle sx={{ pb: 1 }}>\n          <Typography variant=\"h6\" sx={{ fontWeight: 600 }}>\n            {dialogModeComanda === 'view' ? 'Dettagli Comanda' : 'Modifica Comanda'}\n          </Typography>\n        </DialogTitle>\n        <DialogContent>\n          <Box sx={{ pt: 2 }}>\n            {dialogModeComanda === 'view' && selectedComanda ? (\n              <List>\n                <ListItem>\n                  <ListItemText\n                    primary=\"Codice Comanda\"\n                    secondary={selectedComanda.codice_comanda}\n                  />\n                </ListItem>\n                <Divider />\n                <ListItem>\n                  <ListItemText\n                    primary=\"Tipo\"\n                    secondary={getTipoComandaLabel(selectedComanda.tipo_comanda)}\n                  />\n                </ListItem>\n                <Divider />\n                <ListItem>\n                  <ListItemText\n                    primary=\"Stato\"\n                    secondary={\n                      <Chip\n                        label={selectedComanda.stato}\n                        color={getStatoColor(selectedComanda.stato)}\n                        size=\"small\"\n                      />\n                    }\n                  />\n                </ListItem>\n                <Divider />\n                <ListItem>\n                  <ListItemText\n                    primary=\"Descrizione\"\n                    secondary={selectedComanda.descrizione || 'Nessuna descrizione'}\n                  />\n                </ListItem>\n                <Divider />\n                <ListItem>\n                  <ListItemText\n                    primary=\"Responsabile\"\n                    secondary={selectedComanda.responsabile || 'Non assegnato'}\n                  />\n                </ListItem>\n                {selectedComanda.note_capo_cantiere && (\n                  <>\n                    <Divider />\n                    <ListItem>\n                      <ListItemText\n                        primary=\"Note Capo Cantiere\"\n                        secondary={selectedComanda.note_capo_cantiere}\n                      />\n                    </ListItem>\n                  </>\n                )}\n                <Divider />\n                <ListItem>\n                  <ListItemText\n                    primary=\"Data Creazione\"\n                    secondary={new Date(selectedComanda.data_creazione).toLocaleDateString('it-IT')}\n                  />\n                </ListItem>\n                {selectedComanda.data_scadenza && (\n                  <>\n                    <Divider />\n                    <ListItem>\n                      <ListItemText\n                        primary=\"Data Scadenza\"\n                        secondary={new Date(selectedComanda.data_scadenza).toLocaleDateString('it-IT')}\n                      />\n                    </ListItem>\n                  </>\n                )}\n                <Divider />\n                <ListItem>\n                  <ListItemText\n                    primary=\"Cavi Assegnati\"\n                    secondary={selectedComanda.numero_cavi_assegnati || 0}\n                  />\n                </ListItem>\n                <Divider />\n                <ListItem>\n                  <ListItemText\n                    primary=\"Completamento\"\n                    secondary={`${(selectedComanda.percentuale_completamento || 0).toFixed(1)}%`}\n                  />\n                </ListItem>\n              </List>\n            ) : (\n              <>\n                <TextField\n                  fullWidth\n                  select\n                  label=\"Tipo Comanda\"\n                  value={formDataComanda.tipo_comanda}\n                  onChange={(e) => setFormDataComanda({ ...formDataComanda, tipo_comanda: e.target.value })}\n                  margin=\"normal\"\n                  sx={{ mb: 2 }}\n                >\n                  <MenuItem value=\"POSA\">Posa</MenuItem>\n                  <MenuItem value=\"COLLEGAMENTO_PARTENZA\">Collegamento Partenza</MenuItem>\n                  <MenuItem value=\"COLLEGAMENTO_ARRIVO\">Collegamento Arrivo</MenuItem>\n                  <MenuItem value=\"CERTIFICAZIONE\">Certificazione</MenuItem>\n                  <MenuItem value=\"TESTING\">Testing</MenuItem>\n                </TextField>\n\n\n\n                <TextField\n                  fullWidth\n                  label=\"Descrizione\"\n                  value={formDataComanda.descrizione}\n                  onChange={(e) => setFormDataComanda({ ...formDataComanda, descrizione: e.target.value })}\n                  margin=\"normal\"\n                  multiline\n                  rows={3}\n                  sx={{ mb: 2 }}\n                />\n\n                <TextField\n                  fullWidth\n                  label=\"Responsabile\"\n                  value={formDataComanda.responsabile}\n                  onChange={(e) => setFormDataComanda({ ...formDataComanda, responsabile: e.target.value })}\n                  margin=\"normal\"\n                  required\n                  helperText=\"Chi eseguirà il lavoro (obbligatorio)\"\n                  sx={{ mb: 2 }}\n                />\n\n                <TextField\n                  fullWidth\n                  label=\"Note Capo Cantiere\"\n                  value={formDataComanda.note_capo_cantiere}\n                  onChange={(e) => setFormDataComanda({ ...formDataComanda, note_capo_cantiere: e.target.value })}\n                  margin=\"normal\"\n                  multiline\n                  rows={2}\n                  helperText=\"Istruzioni specifiche per il responsabile\"\n                  sx={{ mb: 2 }}\n                />\n\n                <TextField\n                  fullWidth\n                  label=\"Data Scadenza\"\n                  type=\"date\"\n                  value={formDataComanda.data_scadenza}\n                  onChange={(e) => setFormDataComanda({ ...formDataComanda, data_scadenza: e.target.value })}\n                  margin=\"normal\"\n                  InputLabelProps={{\n                    shrink: true,\n                  }}\n                />\n              </>\n            )}\n          </Box>\n        </DialogContent>\n        <DialogActions sx={{ p: 3, pt: 2 }}>\n          <Button\n            onClick={handleCloseComandaDialog}\n            sx={{ textTransform: 'none' }}\n          >\n            {dialogModeComanda === 'view' ? 'Chiudi' : 'Annulla'}\n          </Button>\n          {dialogModeComanda === 'edit' && (\n            <Button\n              onClick={handleSubmitComanda}\n              variant=\"contained\"\n              sx={{\n                textTransform: 'none',\n                fontWeight: 500,\n                px: 3\n              }}\n            >\n              Salva Modifiche\n            </Button>\n          )}\n        </DialogActions>\n      </Dialog>\n\n      {/* Dialog CreaComandaConCavi */}\n      <CreaComandaConCavi\n        cantiereId={cantiereId}\n        open={openCreaConCavi}\n        onClose={() => setOpenCreaConCavi(false)}\n        onSuccess={(response, successMessage) => {\n          console.log('🎉 Comanda creata, aggiornamento interfaccia...');\n\n          // Mostra messaggio di successo se fornito\n          if (successMessage) {\n            // Potresti aggiungere qui un toast/snackbar per mostrare il messaggio\n            console.log('📢 Successo:', successMessage);\n          }\n\n          // Ricarica tutti i dati per aggiornare l'interfaccia\n          loadComande();\n          loadStatistiche();\n          loadResponsabili();\n          setOpenCreaConCavi(false);\n\n          console.log('✅ Interfaccia aggiornata');\n        }}\n      />\n\n      {/* Popup Lista Responsabili */}\n      <ResponsabiliListPopup\n        open={openResponsabiliPopup}\n        onClose={() => setOpenResponsabiliPopup(false)}\n        responsabili={responsabili}\n        comandePerResponsabile={comandePerResponsabile}\n        onEditResponsabile={(responsabile) => {\n          setOpenResponsabiliPopup(false);\n          handleOpenResponsabileDialog('edit', responsabile);\n        }}\n        onDeleteResponsabile={async (idResponsabile) => {\n          await handleDeleteResponsabile(idResponsabile);\n          setOpenResponsabiliPopup(false);\n        }}\n        loading={loadingResponsabili}\n        error={error}\n      />\n    </Box>\n  );\n};\n\nexport default ComandeListRivoluzionato;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAClD,SAASC,eAAe,QAAQ,kBAAkB;AAClD,SACEC,GAAG,EACHC,UAAU,EACVC,MAAM,EACNC,KAAK,EACLC,IAAI,EACJC,MAAM,EACNC,WAAW,EACXC,aAAa,EACbC,aAAa,EACbC,SAAS,EACTC,KAAK,EACLC,gBAAgB,EAChBC,IAAI,EACJC,QAAQ,EACRC,YAAY,EACZC,KAAK,EACLC,QAAQ,EACRC,OAAO,QACF,eAAe;AACtB,SACEC,GAAG,IAAIC,OAAO,EACdC,UAAU,IAAIC,UAAU,EACxBC,MAAM,IAAIC,UAAU,EACpBC,WAAW,IAAIC,eAAe,EAC9BC,QAAQ,IAAIC,YAAY,EACxBC,MAAM,IAAIC,UAAU,EACpBC,YAAY,IAAIC,gBAAgB,QAC3B,qBAAqB;AAC5B,OAAOC,cAAc,MAAM,+BAA+B;AAC1D,OAAOC,mBAAmB,MAAM,oCAAoC;AACpE,OAAOC,kBAAkB,MAAM,sBAAsB;AACrD,OAAOC,qBAAqB,MAAM,yBAAyB;AAC3D,OAAOC,gBAAgB,MAAM,oBAAoB;AACjD,OAAOC,sBAAsB,MAAM,0BAA0B;AAAC,SAAAC,MAAA,IAAAC,OAAA,EAAAC,QAAA,IAAAC,SAAA;AAE9D,MAAMC,wBAAwB,GAAGA,CAAC;EAAEC,UAAU;EAAEC;AAAa,CAAC,KAAK;EAAAC,EAAA;EACjE;EACA,MAAM,CAACC,YAAY,EAAEC,eAAe,CAAC,GAAGhD,eAAe,CAAC,CAAC;;EAEzD;EACA,MAAM,CAACiD,OAAO,EAAEC,UAAU,CAAC,GAAGpD,QAAQ,CAAC,IAAI,CAAC;EAC5C,MAAM,CAACqD,KAAK,EAAEC,QAAQ,CAAC,GAAGtD,QAAQ,CAAC,IAAI,CAAC;EACxC,MAAM,CAACuD,gBAAgB,EAAEC,mBAAmB,CAAC,GAAGxD,QAAQ,CAAC,IAAI,CAAC;;EAE9D;EACA,MAAM,CAACyD,WAAW,EAAEC,cAAc,CAAC,GAAG1D,QAAQ,CAAC,IAAI,CAAC;EACpD,MAAM,CAAC2D,UAAU,EAAEC,aAAa,CAAC,GAAG5D,QAAQ,CAAC,EAAE,CAAC;EAChD,MAAM,CAAC6D,cAAc,EAAEC,iBAAiB,CAAC,GAAG9D,QAAQ,CAAC,KAAK,CAAC;;EAE3D;EACA,MAAM,CAAC+D,qBAAqB,EAAEC,wBAAwB,CAAC,GAAGhE,QAAQ,CAAC,KAAK,CAAC;EACzE,MAAM,CAACiE,eAAe,EAAEC,kBAAkB,CAAC,GAAGlE,QAAQ,CAAC,KAAK,CAAC;;EAE7D;EACA,MAAM,CAACmE,YAAY,EAAEC,eAAe,CAAC,GAAGpE,QAAQ,CAAC,EAAE,CAAC;EACpD,MAAM,CAACqE,mBAAmB,EAAEC,sBAAsB,CAAC,GAAGtE,QAAQ,CAAC,KAAK,CAAC;EACrE,MAAM,CAACuE,sBAAsB,EAAEC,yBAAyB,CAAC,GAAGxE,QAAQ,CAAC,CAAC,CAAC,CAAC;EACxE,MAAM,CAACyE,sBAAsB,EAAEC,yBAAyB,CAAC,GAAG1E,QAAQ,CAAC,KAAK,CAAC;EAC3E,MAAM,CAAC2E,sBAAsB,EAAEC,yBAAyB,CAAC,GAAG5E,QAAQ,CAAC,QAAQ,CAAC;EAC9E,MAAM,CAAC6E,oBAAoB,EAAEC,uBAAuB,CAAC,GAAG9E,QAAQ,CAAC,IAAI,CAAC;EACtE,MAAM,CAAC+E,oBAAoB,EAAEC,uBAAuB,CAAC,GAAGhF,QAAQ,CAAC;IAC/DiF,iBAAiB,EAAE,EAAE;IACrBC,KAAK,EAAE,EAAE;IACTC,QAAQ,EAAE;EACZ,CAAC,CAAC;;EAEF;EACA,MAAM,CAACC,iBAAiB,EAAEC,oBAAoB,CAAC,GAAGrF,QAAQ,CAAC,KAAK,CAAC;EACjE,MAAM,CAACsF,iBAAiB,EAAEC,oBAAoB,CAAC,GAAGvF,QAAQ,CAAC,MAAM,CAAC,CAAC,CAAC;EACpE,MAAM,CAACwF,eAAe,EAAEC,kBAAkB,CAAC,GAAGzF,QAAQ,CAAC,IAAI,CAAC;EAC5D,MAAM,CAAC0F,eAAe,EAAEC,kBAAkB,CAAC,GAAG3F,QAAQ,CAAC;IACrD4F,YAAY,EAAE,MAAM;IACpBC,WAAW,EAAE,EAAE;IACfC,YAAY,EAAE,EAAE;IAChBC,aAAa,EAAE,EAAE;IACjBC,kBAAkB,EAAE;EACtB,CAAC,CAAC;;EAEF;EACA,MAAM,CAACC,oBAAoB,EAAEC,uBAAuB,CAAC,GAAGlG,QAAQ,CAAC,KAAK,CAAC;EACvE,MAAM,CAACmG,eAAe,EAAEC,kBAAkB,CAAC,GAAGpG,QAAQ,CAAC,IAAI,CAAC;EAE5D,MAAMqG,WAAW,GAAG,MAAAA,CAAA,KAAY;IAC9B,IAAI;MACFvC,iBAAiB,CAAC,IAAI,CAAC;MACvBwC,OAAO,CAACC,GAAG,CAAC,sCAAsC,EAAEzD,UAAU,CAAC;MAC/D,MAAM0D,WAAW,GAAG,MAAMrE,cAAc,CAACsE,UAAU,CAAC3D,UAAU,CAAC;MAC/DwD,OAAO,CAACC,GAAG,CAAC,2BAA2B,EAAEC,WAAW,CAAC;MACrDF,OAAO,CAACC,GAAG,CAAC,eAAe,EAAE,OAAOC,WAAW,EAAE,QAAQ,EAAEE,KAAK,CAACC,OAAO,CAACH,WAAW,CAAC,CAAC;;MAEtF;MACA,IAAII,YAAY,GAAG,EAAE;MACrB,IAAIF,KAAK,CAACC,OAAO,CAACH,WAAW,CAAC,EAAE;QAC9BI,YAAY,GAAGJ,WAAW;MAC5B,CAAC,MAAM,IAAIA,WAAW,IAAIE,KAAK,CAACC,OAAO,CAACH,WAAW,CAACK,OAAO,CAAC,EAAE;QAC5DD,YAAY,GAAGJ,WAAW,CAACK,OAAO;MACpC,CAAC,MAAM,IAAIL,WAAW,IAAIE,KAAK,CAACC,OAAO,CAACH,WAAW,CAACM,IAAI,CAAC,EAAE;QACzDF,YAAY,GAAGJ,WAAW,CAACM,IAAI;MACjC;MAEAR,OAAO,CAACC,GAAG,CAAC,0BAA0B,EAAEK,YAAY,EAAE,YAAY,EAAEA,YAAY,CAACG,MAAM,CAAC;MACxFnD,aAAa,CAACgD,YAAY,CAAC;IAC7B,CAAC,CAAC,OAAOI,GAAG,EAAE;MACZV,OAAO,CAACjD,KAAK,CAAC,iCAAiC,EAAE2D,GAAG,CAAC;MACrD1D,QAAQ,CAAC,sCAAsC,CAAC;IAClD,CAAC,SAAS;MACRQ,iBAAiB,CAAC,KAAK,CAAC;IAC1B;EACF,CAAC;EAED,MAAMmD,eAAe,GAAG,MAAAA,CAAA,KAAY;IAClC,IAAI;MACFX,OAAO,CAACC,GAAG,CAAC,0CAA0C,EAAEzD,UAAU,CAAC;MACnE,MAAMoE,KAAK,GAAG,MAAM/E,cAAc,CAACgF,qBAAqB,CAACrE,UAAU,CAAC;MACpEwD,OAAO,CAACC,GAAG,CAAC,0BAA0B,EAAEW,KAAK,CAAC;MAC9CxD,cAAc,CAACwD,KAAK,CAAC;IACvB,CAAC,CAAC,OAAOF,GAAG,EAAE;MAAA,IAAAI,aAAA;MACZd,OAAO,CAACjD,KAAK,CAAC,6CAA6C,EAAE2D,GAAG,CAAC;MACjEV,OAAO,CAACjD,KAAK,CAAC,oBAAoB,EAAE,EAAA+D,aAAA,GAAAJ,GAAG,CAACK,QAAQ,cAAAD,aAAA,uBAAZA,aAAA,CAAcN,IAAI,KAAIE,GAAG,CAACM,OAAO,CAAC;IACxE;EACF,CAAC;EAED,MAAMC,gBAAgB,GAAG,MAAAA,CAAA,KAAY;IACnC,IAAI;MACFjD,sBAAsB,CAAC,IAAI,CAAC;MAC5BhB,QAAQ,CAAC,IAAI,CAAC;MAEd,MAAMwD,IAAI,GAAG,MAAM1E,mBAAmB,CAACoF,uBAAuB,CAAC1E,UAAU,CAAC;MAC1EsB,eAAe,CAAC0C,IAAI,IAAI,EAAE,CAAC;MAC3B,MAAMW,0BAA0B,CAACX,IAAI,IAAI,EAAE,CAAC;IAC9C,CAAC,CAAC,OAAOE,GAAG,EAAE;MAAA,IAAAU,cAAA,EAAAC,mBAAA;MACZrB,OAAO,CAACjD,KAAK,CAAC,0CAA0C,EAAE2D,GAAG,CAAC;MAC9D,MAAMY,YAAY,GAAG,EAAAF,cAAA,GAAAV,GAAG,CAACK,QAAQ,cAAAK,cAAA,wBAAAC,mBAAA,GAAZD,cAAA,CAAcZ,IAAI,cAAAa,mBAAA,uBAAlBA,mBAAA,CAAoBE,MAAM,KAAIb,GAAG,CAACM,OAAO,IAAI,yCAAyC;MAC3GhE,QAAQ,CAAC,4CAA4CsE,YAAY,EAAE,CAAC;IACtE,CAAC,SAAS;MACRtD,sBAAsB,CAAC,KAAK,CAAC;IAC/B;EACF,CAAC;;EAID;EACArE,SAAS,CAAC,MAAM;IACd,MAAM6H,cAAc,GAAG,MAAAA,CAAA,KAAY;MACjC,IAAIhF,UAAU,EAAE;QACdM,UAAU,CAAC,IAAI,CAAC;QAChB,IAAI;UACF,MAAM2E,OAAO,CAACC,GAAG,CAAC,CAChBT,gBAAgB,CAAC,CAAC,EAClBlB,WAAW,CAAC,CAAC,EACbY,eAAe,CAAC,CAAC,CAClB,CAAC;QACJ,CAAC,CAAC,OAAOD,GAAG,EAAE;UACZV,OAAO,CAACjD,KAAK,CAAC,kCAAkC,EAAE2D,GAAG,CAAC;QACxD,CAAC,SAAS;UACR5D,UAAU,CAAC,KAAK,CAAC;QACnB;MACF;IACF,CAAC;IAED0E,cAAc,CAAC,CAAC;EAClB,CAAC,EAAE,CAAChF,UAAU,CAAC,CAAC,CAAC,CAAC;;EAElB;EACA7C,SAAS,CAAC,MAAM;IACd,MAAMgI,YAAY,GAAGhF,YAAY,CAACiF,GAAG,CAAC,SAAS,CAAC;IAChD5B,OAAO,CAACC,GAAG,CAAC,qCAAqC,EAAE0B,YAAY,CAAC;IAChE3B,OAAO,CAACC,GAAG,CAAC,gBAAgB,EAAE;MAC5BpC,YAAY,EAAEA,YAAY,CAAC4C,MAAM;MACjCxC,sBAAsB,EAAE4D,MAAM,CAACC,IAAI,CAAC7D,sBAAsB,CAAC,CAACwC,MAAM;MAClE5D,OAAO;MACPkB;IACF,CAAC,CAAC;;IAEF;IACA,IAAI4D,YAAY,IAAIA,YAAY,KAAK1E,gBAAgB,EAAE;MACrDC,mBAAmB,CAACyE,YAAY,CAAC;IACnC;IAEA,IAAIA,YAAY,IAAI9D,YAAY,CAAC4C,MAAM,GAAG,CAAC,IAAIoB,MAAM,CAACC,IAAI,CAAC7D,sBAAsB,CAAC,CAACwC,MAAM,GAAG,CAAC,EAAE;MAC7FT,OAAO,CAACC,GAAG,CAAC,0CAA0C,CAAC;;MAEvD;MACA,IAAI8B,cAAc,GAAG,IAAI;MAEzB,KAAK,MAAMvC,YAAY,IAAI3B,YAAY,EAAE;QACvC,MAAMmE,WAAW,GAAG/D,sBAAsB,CAACuB,YAAY,CAACyC,eAAe,CAAC,IAAI,EAAE;QAC9EjC,OAAO,CAACC,GAAG,CAAC,mBAAmBT,YAAY,CAACb,iBAAiB,KAAKqD,WAAW,CAACvB,MAAM,UAAU,CAAC;QAC/FsB,cAAc,GAAGC,WAAW,CAACE,IAAI,CAACC,CAAC,IAAIA,CAAC,CAACC,cAAc,KAAKT,YAAY,CAAC;QACzE,IAAII,cAAc,EAAE;UAClB/B,OAAO,CAACC,GAAG,CAAC,oBAAoB,EAAE8B,cAAc,CAAC;UACjD;QACF;MACF;MAEA,IAAIA,cAAc,EAAE;QAClB/B,OAAO,CAACC,GAAG,CAAC,wCAAwC,EAAE0B,YAAY,CAAC;QACnEzE,mBAAmB,CAAC,IAAI,CAAC,CAAC,CAAC;QAC3BmF,uBAAuB,CAAC,MAAM,EAAEN,cAAc,CAAC;QAC/C;QACAO,UAAU,CAAC,MAAM;UACf1F,eAAe,CAAC2F,IAAI,IAAI;YACtB,MAAMC,SAAS,GAAG,IAAIC,eAAe,CAACF,IAAI,CAAC;YAC3CC,SAAS,CAACE,MAAM,CAAC,SAAS,CAAC;YAC3B,OAAOF,SAAS;UAClB,CAAC,CAAC;QACJ,CAAC,EAAE,GAAG,CAAC;MACT,CAAC,MAAM;QACLxC,OAAO,CAAC2C,IAAI,CAAC,yBAAyB,EAAEhB,YAAY,CAAC;QACrD3B,OAAO,CAACC,GAAG,CAAC,yBAAyB,CAAC;QACtCpC,YAAY,CAAC+E,OAAO,CAACC,IAAI,IAAI;UAC3B,MAAMtC,OAAO,GAAGtC,sBAAsB,CAAC4E,IAAI,CAACZ,eAAe,CAAC,IAAI,EAAE;UAClE1B,OAAO,CAACqC,OAAO,CAACE,GAAG,IAAI;YACrB9C,OAAO,CAACC,GAAG,CAAC,OAAO6C,GAAG,CAACV,cAAc,KAAKS,IAAI,CAAClE,iBAAiB,GAAG,CAAC;UACtE,CAAC,CAAC;QACJ,CAAC,CAAC;;QAEF;QACA;QACA,IAAI,CAAC9B,OAAO,IAAI,CAACkB,mBAAmB,EAAE;UACpCiC,OAAO,CAACC,GAAG,CAAC,uCAAuC,CAAC;UACpDqC,UAAU,CAAC,MAAM;YACfrB,gBAAgB,CAAC,CAAC;UACpB,CAAC,EAAE,GAAG,CAAC;QACT;MACF;IACF,CAAC,MAAM,IAAIU,YAAY,EAAE;MACvB3B,OAAO,CAACC,GAAG,CAAC,wCAAwC,CAAC;;MAErD;MACA,IAAI,CAACpD,OAAO,IAAI,CAACkB,mBAAmB,IAAIF,YAAY,CAAC4C,MAAM,KAAK,CAAC,EAAE;QACjET,OAAO,CAACC,GAAG,CAAC,0CAA0C,CAAC;QACvDgB,gBAAgB,CAAC,CAAC;MACpB;IACF;EACF,CAAC,EAAE,CAACtE,YAAY,EAAEkB,YAAY,EAAEI,sBAAsB,EAAEpB,OAAO,EAAEkB,mBAAmB,CAAC,CAAC,CAAC,CAAC;;EAExF,MAAMoD,0BAA0B,GAAG,MAAO4B,gBAAgB,IAAK;IAC7D,IAAI;MACF,MAAMC,UAAU,GAAG,CAAC,CAAC;MACrB,KAAK,MAAMxD,YAAY,IAAIuD,gBAAgB,EAAE;QAC3C,IAAI;UACF,MAAMhC,QAAQ,GAAG,MAAMlF,cAAc,CAACoH,wBAAwB,CAACzG,UAAU,EAAEgD,YAAY,CAACb,iBAAiB,CAAC;UAC1G;UACA,IAAI4B,OAAO,GAAG,EAAE;UAChB,IAAIQ,QAAQ,IAAIX,KAAK,CAACC,OAAO,CAACU,QAAQ,CAAC,EAAE;YACvCR,OAAO,GAAGQ,QAAQ;UACpB,CAAC,MAAM,IAAIA,QAAQ,IAAIA,QAAQ,CAACR,OAAO,IAAIH,KAAK,CAACC,OAAO,CAACU,QAAQ,CAACR,OAAO,CAAC,EAAE;YAC1EA,OAAO,GAAGQ,QAAQ,CAACR,OAAO;UAC5B,CAAC,MAAM,IAAIQ,QAAQ,IAAIA,QAAQ,CAACP,IAAI,IAAIJ,KAAK,CAACC,OAAO,CAACU,QAAQ,CAACP,IAAI,CAAC,EAAE;YACpED,OAAO,GAAGQ,QAAQ,CAACP,IAAI;UACzB;UACAwC,UAAU,CAACxD,YAAY,CAACyC,eAAe,CAAC,GAAG1B,OAAO;QACpD,CAAC,CAAC,OAAOG,GAAG,EAAE;UACZV,OAAO,CAACjD,KAAK,CAAC,sCAAsCyC,YAAY,CAACb,iBAAiB,GAAG,EAAE+B,GAAG,CAAC;UAC3FsC,UAAU,CAACxD,YAAY,CAACyC,eAAe,CAAC,GAAG,EAAE;QAC/C;MACF;MACA/D,yBAAyB,CAAC8E,UAAU,CAAC;IACvC,CAAC,CAAC,OAAOtC,GAAG,EAAE;MACZV,OAAO,CAACjD,KAAK,CAAC,uCAAuC,EAAE2D,GAAG,CAAC;IAC7D;EACF,CAAC;;EAED;EACA,MAAMwC,4BAA4B,GAAGA,CAACC,IAAI,EAAE3D,YAAY,GAAG,IAAI,KAAK;IAClElB,yBAAyB,CAAC6E,IAAI,CAAC;IAC/B3E,uBAAuB,CAACgB,YAAY,CAAC;IAErC,IAAI2D,IAAI,KAAK,MAAM,IAAI3D,YAAY,EAAE;MACnCd,uBAAuB,CAAC;QACtBC,iBAAiB,EAAEa,YAAY,CAACb,iBAAiB,IAAI,EAAE;QACvDC,KAAK,EAAEY,YAAY,CAACZ,KAAK,IAAI,EAAE;QAC/BC,QAAQ,EAAEW,YAAY,CAACX,QAAQ,IAAI;MACrC,CAAC,CAAC;IACJ,CAAC,MAAM;MACLH,uBAAuB,CAAC;QACtBC,iBAAiB,EAAE,EAAE;QACrBC,KAAK,EAAE,EAAE;QACTC,QAAQ,EAAE;MACZ,CAAC,CAAC;IACJ;IAEAT,yBAAyB,CAAC,IAAI,CAAC;EACjC,CAAC;EAED,MAAMgF,6BAA6B,GAAGA,CAAA,KAAM;IAC1ChF,yBAAyB,CAAC,KAAK,CAAC;IAChCI,uBAAuB,CAAC,IAAI,CAAC;IAC7BxB,QAAQ,CAAC,IAAI,CAAC;EAChB,CAAC;EAED,MAAMqG,wBAAwB,GAAG,MAAAA,CAAA,KAAY;IAC3C,IAAI;MACFrG,QAAQ,CAAC,IAAI,CAAC;MAEd,IAAI,CAACyB,oBAAoB,CAACE,iBAAiB,CAAC2E,IAAI,CAAC,CAAC,EAAE;QAClDtG,QAAQ,CAAC,yCAAyC,CAAC;QACnD;MACF;MAEA,IAAI,CAACyB,oBAAoB,CAACG,KAAK,IAAI,CAACH,oBAAoB,CAACI,QAAQ,EAAE;QACjE7B,QAAQ,CAAC,yDAAyD,CAAC;QACnE;MACF;MAEA,IAAIqB,sBAAsB,KAAK,QAAQ,EAAE;QACvC,MAAMvC,mBAAmB,CAACyH,kBAAkB,CAAC/G,UAAU,EAAEiC,oBAAoB,CAAC;MAChF,CAAC,MAAM,IAAIJ,sBAAsB,KAAK,MAAM,EAAE;QAC5C,MAAMvC,mBAAmB,CAAC0H,kBAAkB,CAACjF,oBAAoB,CAAC0D,eAAe,EAAExD,oBAAoB,CAAC;MAC1G;MAEA2E,6BAA6B,CAAC,CAAC;MAC/B,MAAMnC,gBAAgB,CAAC,CAAC;MACxB,MAAMlB,WAAW,CAAC,CAAC;IACrB,CAAC,CAAC,OAAOW,GAAG,EAAE;MACZV,OAAO,CAACjD,KAAK,CAAC,yBAAyB,EAAE2D,GAAG,CAAC;MAC7C1D,QAAQ,CAAC0D,GAAG,CAACa,MAAM,IAAI,yCAAyC,CAAC;IACnE;EACF,CAAC;EAED,MAAMkC,wBAAwB,GAAG,MAAOC,cAAc,IAAK;IACzD,IAAI,CAACC,MAAM,CAACC,OAAO,CAAC,oDAAoD,CAAC,EAAE;MACzE;IACF;IAEA,IAAI;MACF,MAAM9H,mBAAmB,CAAC+H,kBAAkB,CAACH,cAAc,CAAC;MAC5D,MAAMzC,gBAAgB,CAAC,CAAC;MACxB,MAAMlB,WAAW,CAAC,CAAC;IACrB,CAAC,CAAC,OAAOW,GAAG,EAAE;MACZV,OAAO,CAACjD,KAAK,CAAC,4BAA4B,EAAE2D,GAAG,CAAC;MAChD1D,QAAQ,CAAC,4CAA4C,CAAC;IACxD;EACF,CAAC;;EAED;EACA,MAAMqF,uBAAuB,GAAGA,CAACc,IAAI,EAAEW,OAAO,GAAG,IAAI,KAAK;IACxD7E,oBAAoB,CAACkE,IAAI,CAAC;IAC1BhE,kBAAkB,CAAC2E,OAAO,CAAC;IAE3B,IAAIX,IAAI,KAAK,MAAM,IAAIW,OAAO,EAAE;MAC9BzE,kBAAkB,CAAC;QACjBC,YAAY,EAAEwE,OAAO,CAACxE,YAAY;QAClCC,WAAW,EAAEuE,OAAO,CAACvE,WAAW,IAAI,EAAE;QACtCC,YAAY,EAAEsE,OAAO,CAACtE,YAAY,IAAI,EAAE;QACxCC,aAAa,EAAEqE,OAAO,CAACrE,aAAa,IAAI,EAAE;QAC1CC,kBAAkB,EAAEoE,OAAO,CAACpE,kBAAkB,IAAI;MACpD,CAAC,CAAC;IACJ;IAEAX,oBAAoB,CAAC,IAAI,CAAC;EAC5B,CAAC;EAED,MAAMgF,wBAAwB,GAAGA,CAAA,KAAM;IACrChF,oBAAoB,CAAC,KAAK,CAAC;IAC3BI,kBAAkB,CAAC,IAAI,CAAC;IACxBE,kBAAkB,CAAC;MACjBC,YAAY,EAAE,MAAM;MACpBC,WAAW,EAAE,EAAE;MACfC,YAAY,EAAE,EAAE;MAChBC,aAAa,EAAE,EAAE;MACjBC,kBAAkB,EAAE;IACtB,CAAC,CAAC;EACJ,CAAC;EAED,MAAMsE,mBAAmB,GAAG,MAAAA,CAAA,KAAY;IACtC,IAAI;MACF,IAAIhF,iBAAiB,KAAK,MAAM,EAAE;QAChC,MAAMnD,cAAc,CAACoI,aAAa,CAAC/E,eAAe,CAACkD,cAAc,EAAEhD,eAAe,CAAC;QACnF2E,wBAAwB,CAAC,CAAC;QAC1B,MAAM9C,gBAAgB,CAAC,CAAC,CAAC,CAAC;QAC1B,MAAMlB,WAAW,CAAC,CAAC;QACnB,MAAMY,eAAe,CAAC,CAAC;MACzB;IACF,CAAC,CAAC,OAAOD,GAAG,EAAE;MACZV,OAAO,CAACjD,KAAK,CAAC,yBAAyB,EAAE2D,GAAG,CAAC;MAC7C1D,QAAQ,CAAC,sCAAsC,CAAC;IAClD;EACF,CAAC;EAED,MAAMkH,mBAAmB,GAAG,MAAOC,aAAa,IAAK;IACnD,IAAI,CAACR,MAAM,CAACC,OAAO,CAAC,+CAA+C,CAAC,EAAE;MACpE;IACF;IAEA,IAAI;MACF,MAAM/H,cAAc,CAACuI,aAAa,CAACD,aAAa,CAAC;MACjD,MAAMlD,gBAAgB,CAAC,CAAC,CAAC,CAAC;MAC1B,MAAMlB,WAAW,CAAC,CAAC;MACnB,MAAMY,eAAe,CAAC,CAAC;IACzB,CAAC,CAAC,OAAOD,GAAG,EAAE;MACZV,OAAO,CAACjD,KAAK,CAAC,4BAA4B,EAAE2D,GAAG,CAAC;MAChD1D,QAAQ,CAAC,yCAAyC,CAAC;IACrD;EACF,CAAC;EAED,MAAMqH,mBAAmB,GAAIC,IAAI,IAAK;IACpC,MAAMC,MAAM,GAAG;MACb,MAAM,EAAE,MAAM;MACd,uBAAuB,EAAE,gBAAgB;MACzC,qBAAqB,EAAE,cAAc;MACrC,gBAAgB,EAAE,gBAAgB;MAClC,SAAS,EAAE;IACb,CAAC;IACD,OAAOA,MAAM,CAACD,IAAI,CAAC,IAAIA,IAAI;EAC7B,CAAC;EAED,MAAME,aAAa,GAAIC,KAAK,IAAK;IAC/B,MAAMC,MAAM,GAAG;MACb,QAAQ,EAAE,SAAS;MACnB,WAAW,EAAE,SAAS;MACtB,UAAU,EAAE,SAAS;MACrB,YAAY,EAAE,SAAS;MACvB,WAAW,EAAE;IACf,CAAC;IACD,OAAOA,MAAM,CAACD,KAAK,CAAC,IAAI,SAAS;EACnC,CAAC;EAID,IAAI5H,OAAO,IAAIU,cAAc,EAAE;IAC7B,oBACEnB,OAAA,CAACvC,GAAG;MAAC8K,OAAO,EAAC,MAAM;MAACC,cAAc,EAAC,QAAQ;MAACC,UAAU,EAAC,QAAQ;MAACC,SAAS,EAAC,OAAO;MAAAC,QAAA,eAC/E3I,OAAA,CAAC5B,gBAAgB;QAAAwK,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACjB,CAAC;EAEV;EAEA,oBACE/I,OAAA,CAACvC,GAAG;IAACuL,EAAE,EAAE;MAAEC,CAAC,EAAE;IAAE,CAAE;IAAAN,QAAA,gBAEhB3I,OAAA,CAACvC,GAAG;MAACyL,EAAE,EAAE,CAAE;MAAAP,QAAA,eACT3I,OAAA,CAACtC,UAAU;QAACyL,OAAO,EAAC,IAAI;QAACH,EAAE,EAAE;UAAEI,UAAU,EAAE,GAAG;UAAEC,KAAK,EAAE;QAAe,CAAE;QAAAV,QAAA,EACrEtI;MAAY;QAAAuI,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACV,CAAC,EAELpI,KAAK,iBACJX,OAAA,CAAC7B,KAAK;MAACmL,QAAQ,EAAC,OAAO;MAACN,EAAE,EAAE;QAAEE,EAAE,EAAE;MAAE,CAAE;MAAAP,QAAA,EACnChI;IAAK;MAAAiI,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACD,CACR,EAEAlI,gBAAgB,iBACfb,OAAA,CAAC7B,KAAK;MAACmL,QAAQ,EAAC,MAAM;MAACN,EAAE,EAAE;QAAEE,EAAE,EAAE;MAAE,CAAE;MAAAP,QAAA,GAAC,+BACjB,EAAC9H,gBAAgB,EAAC,cACvC;IAAA;MAAA+H,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAO,CACR,EAGAhI,WAAW,iBACVf,OAAA,CAACpC,KAAK;MAACoL,EAAE,EAAE;QAAEC,CAAC,EAAE,CAAC;QAAEC,EAAE,EAAE,CAAC;QAAEK,OAAO,EAAE;MAAU,CAAE;MAAAZ,QAAA,eAC7C3I,OAAA,CAACxB,KAAK;QAACgL,SAAS,EAAC,KAAK;QAACC,OAAO,EAAE,CAAE;QAAChB,UAAU,EAAC,QAAQ;QAACD,cAAc,EAAC,eAAe;QAACkB,QAAQ,EAAC,MAAM;QAAAf,QAAA,gBAEnG3I,OAAA,CAACxB,KAAK;UAACgL,SAAS,EAAC,KAAK;UAACf,UAAU,EAAC,QAAQ;UAACgB,OAAO,EAAE,CAAE;UAAAd,QAAA,gBACpD3I,OAAA,CAAChB,UAAU;YAACqK,KAAK,EAAC,SAAS;YAACM,QAAQ,EAAC;UAAO;YAAAf,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,eAC/C/I,OAAA,CAACvC,GAAG;YAAAkL,QAAA,gBACF3I,OAAA,CAACtC,UAAU;cAACyL,OAAO,EAAC,IAAI;cAACC,UAAU,EAAC,MAAM;cAACJ,EAAE,EAAE;gBAAEY,UAAU,EAAE;cAAE,CAAE;cAAAjB,QAAA,EAC9D5H,WAAW,CAAC8I,mBAAmB,IAAI;YAAC;cAAAjB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC3B,CAAC,eACb/I,OAAA,CAACtC,UAAU;cAACyL,OAAO,EAAC,SAAS;cAACE,KAAK,EAAC,gBAAgB;cAAAV,QAAA,EAAC;YAErD;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAY,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACV,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACD,CAAC,eAGR/I,OAAA,CAACxB,KAAK;UAACgL,SAAS,EAAC,KAAK;UAACf,UAAU,EAAC,QAAQ;UAACgB,OAAO,EAAE,CAAE;UAAAd,QAAA,gBACpD3I,OAAA,CAAClB,UAAU;YAACuK,KAAK,EAAC,MAAM;YAACM,QAAQ,EAAC;UAAO;YAAAf,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,eAC5C/I,OAAA,CAACvC,GAAG;YAAAkL,QAAA,gBACF3I,OAAA,CAACtC,UAAU;cAACyL,OAAO,EAAC,IAAI;cAACC,UAAU,EAAC,MAAM;cAACJ,EAAE,EAAE;gBAAEY,UAAU,EAAE;cAAE,CAAE;cAAAjB,QAAA,EAC9D5H,WAAW,CAAC+I,cAAc,IAAI;YAAC;cAAAlB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACtB,CAAC,eACb/I,OAAA,CAACtC,UAAU;cAACyL,OAAO,EAAC,SAAS;cAACE,KAAK,EAAC,gBAAgB;cAAAV,QAAA,EAAC;YAErD;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAY,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACV,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACD,CAAC,eAGR/I,OAAA,CAACxB,KAAK;UAACgL,SAAS,EAAC,KAAK;UAACf,UAAU,EAAC,QAAQ;UAACgB,OAAO,EAAE,CAAE;UAAAd,QAAA,gBACpD3I,OAAA,CAACd,eAAe;YAACmK,KAAK,EAAC,SAAS;YAACM,QAAQ,EAAC;UAAO;YAAAf,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,eACpD/I,OAAA,CAACvC,GAAG;YAAAkL,QAAA,gBACF3I,OAAA,CAACtC,UAAU;cAACyL,OAAO,EAAC,IAAI;cAACC,UAAU,EAAC,MAAM;cAACJ,EAAE,EAAE;gBAAEY,UAAU,EAAE;cAAE,CAAE;cAAAjB,QAAA,EAC9D5H,WAAW,CAACgJ,gBAAgB,IAAI;YAAC;cAAAnB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACxB,CAAC,eACb/I,OAAA,CAACtC,UAAU;cAACyL,OAAO,EAAC,SAAS;cAACE,KAAK,EAAC,gBAAgB;cAAAV,QAAA,EAAC;YAErD;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAY,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACV,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACD,CAAC,eAGR/I,OAAA,CAACxB,KAAK;UAACgL,SAAS,EAAC,KAAK;UAACf,UAAU,EAAC,QAAQ;UAACgB,OAAO,EAAE,CAAE;UAAAd,QAAA,gBACpD3I,OAAA,CAACZ,YAAY;YAACiK,KAAK,EAAC,SAAS;YAACM,QAAQ,EAAC;UAAO;YAAAf,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,eACjD/I,OAAA,CAACvC,GAAG;YAAAkL,QAAA,gBACF3I,OAAA,CAACtC,UAAU;cAACyL,OAAO,EAAC,IAAI;cAACC,UAAU,EAAC,MAAM;cAACJ,EAAE,EAAE;gBAAEY,UAAU,EAAE;cAAE,CAAE;cAAAjB,QAAA,EAC9D5H,WAAW,CAACiJ,kBAAkB,IAAI;YAAC;cAAApB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC1B,CAAC,eACb/I,OAAA,CAACtC,UAAU;cAACyL,OAAO,EAAC,SAAS;cAACE,KAAK,EAAC,gBAAgB;cAAAV,QAAA,EAAC;YAErD;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAY,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACV,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACD,CAAC,eAGR/I,OAAA,CAACxB,KAAK;UAACgL,SAAS,EAAC,KAAK;UAACf,UAAU,EAAC,QAAQ;UAACgB,OAAO,EAAE,CAAE;UAAAd,QAAA,gBACpD3I,OAAA,CAACvC,GAAG;YAACuL,EAAE,EAAE;cACPiB,KAAK,EAAE,EAAE;cACTC,MAAM,EAAE,EAAE;cACVC,YAAY,EAAE,KAAK;cACnBZ,OAAO,EAAGxI,WAAW,CAACiJ,kBAAkB,IAAIjJ,WAAW,CAAC+I,cAAc,IAAI,CAAC,CAAC,IAAK,GAAG,GAAG,cAAc,GAC3F/I,WAAW,CAACiJ,kBAAkB,IAAIjJ,WAAW,CAAC+I,cAAc,IAAI,CAAC,CAAC,IAAK,GAAG,GAAG,cAAc,GAAG,YAAY;cACpHvB,OAAO,EAAE,MAAM;cACfE,UAAU,EAAE,QAAQ;cACpBD,cAAc,EAAE;YAClB,CAAE;YAAAG,QAAA,eACA3I,OAAA,CAACtC,UAAU;cAACyL,OAAO,EAAC,SAAS;cAACC,UAAU,EAAC,MAAM;cAACC,KAAK,EAAC,OAAO;cAAAV,QAAA,GAC1D5H,WAAW,CAAC+I,cAAc,GAAG,CAAC,GAAGM,IAAI,CAACC,KAAK,CAAEtJ,WAAW,CAACiJ,kBAAkB,GAAGjJ,WAAW,CAAC+I,cAAc,GAAI,GAAG,CAAC,GAAG,CAAC,EAAC,GACxH;YAAA;cAAAlB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAY;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACV,CAAC,eACN/I,OAAA,CAACvC,GAAG;YAAAkL,QAAA,gBACF3I,OAAA,CAACtC,UAAU;cAACyL,OAAO,EAAC,OAAO;cAACC,UAAU,EAAC,QAAQ;cAACJ,EAAE,EAAE;gBAAEY,UAAU,EAAE;cAAE,CAAE;cAAAjB,QAAA,EAAC;YAEvE;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAY,CAAC,eACb/I,OAAA,CAACtC,UAAU;cAACyL,OAAO,EAAC,SAAS;cAACE,KAAK,EAAC,gBAAgB;cAAAV,QAAA,GACjD5H,WAAW,CAACuJ,cAAc,IAAI,CAAC,EAAC,SACnC;YAAA;cAAA1B,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAY,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACV,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACD,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CACR,eAGD/I,OAAA,CAACvC,GAAG;MAAAkL,QAAA,eACF3I,OAAA,CAACvC,GAAG;QAAAkL,QAAA,gBAEF3I,OAAA,CAACvC,GAAG;UAAC8K,OAAO,EAAC,MAAM;UAACC,cAAc,EAAC,eAAe;UAACC,UAAU,EAAC,QAAQ;UAACS,EAAE,EAAE,CAAE;UAAAP,QAAA,gBAC3E3I,OAAA,CAACtC,UAAU;YAACyL,OAAO,EAAC,IAAI;YAACH,EAAE,EAAE;cAAEI,UAAU,EAAE,GAAG;cAAEC,KAAK,EAAE;YAAe,CAAE;YAAAV,QAAA,EAAC;UAEzE;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAY,CAAC,eACb/I,OAAA,CAACvC,GAAG;YAAC8K,OAAO,EAAC,MAAM;YAACgC,GAAG,EAAE,CAAE;YAAA5B,QAAA,gBACzB3I,OAAA,CAACrC,MAAM;cACLwL,OAAO,EAAC,UAAU;cAClBqB,SAAS,eAAExK,OAAA,CAACV,UAAU;gBAAAsJ,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAE;cAC1B0B,OAAO,EAAEA,CAAA,KAAMnJ,wBAAwB,CAAC,IAAI,CAAE;cAC9C0H,EAAE,EAAE;gBACF0B,aAAa,EAAE,MAAM;gBACrBtB,UAAU,EAAE,GAAG;gBACfuB,EAAE,EAAE,CAAC;gBACLC,EAAE,EAAE,CAAC;gBACLC,eAAe,EAAE,SAAS;gBAC1BxB,KAAK,EAAE,SAAS;gBAChByB,MAAM,EAAE,mBAAmB;gBAC3B,SAAS,EAAE;kBACTD,eAAe,EAAE,yBAAyB;kBAC1CE,WAAW,EAAE;gBACf;cACF,CAAE;cAAApC,QAAA,EACH;YAED;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC,eACT/I,OAAA,CAACrC,MAAM;cACLwL,OAAO,EAAC,WAAW;cACnBqB,SAAS,eAAExK,OAAA,CAACpB,OAAO;gBAAAgK,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAE;cACvB0B,OAAO,EAAEA,CAAA,KAAM3D,4BAA4B,CAAC,QAAQ,CAAE;cACtDkC,EAAE,EAAE;gBACF0B,aAAa,EAAE,MAAM;gBACrBtB,UAAU,EAAE,GAAG;gBACfuB,EAAE,EAAE,CAAC;gBACLC,EAAE,EAAE,CAAC;gBACLC,eAAe,EAAE,SAAS;gBAC1BxB,KAAK,EAAE,OAAO;gBACd,SAAS,EAAE;kBACTwB,eAAe,EAAE;gBACnB;cACF,CAAE;cAAAlC,QAAA,EACH;YAED;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACN,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,EAGL5H,cAAc,gBACbnB,OAAA,CAACvC,GAAG;UAAC8K,OAAO,EAAC,MAAM;UAACC,cAAc,EAAC,QAAQ;UAACoC,EAAE,EAAE,CAAE;UAAAjC,QAAA,eAChD3I,OAAA,CAAC5B,gBAAgB;YAAAwK,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACjB,CAAC,GACJ9H,UAAU,CAACoD,MAAM,KAAK,CAAC,gBACzBrE,OAAA,CAACpC,KAAK;UACJoN,SAAS,EAAE,CAAE;UACbhC,EAAE,EAAE;YACFC,CAAC,EAAE,CAAC;YACJgC,SAAS,EAAE,QAAQ;YACnBJ,eAAe,EAAE,SAAS;YAC1BC,MAAM,EAAE,YAAY;YACpBC,WAAW,EAAE;UACf,CAAE;UAAApC,QAAA,gBAEF3I,OAAA,CAAClB,UAAU;YAACkK,EAAE,EAAE;cAAEW,QAAQ,EAAE,EAAE;cAAEN,KAAK,EAAE,UAAU;cAAEH,EAAE,EAAE;YAAE;UAAE;YAAAN,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,eAC9D/I,OAAA,CAACtC,UAAU;YAACyL,OAAO,EAAC,IAAI;YAACE,KAAK,EAAC,gBAAgB;YAAC6B,YAAY;YAAAvC,QAAA,EAAC;UAE7D;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAY,CAAC,eACb/I,OAAA,CAACtC,UAAU;YAACyL,OAAO,EAAC,OAAO;YAACE,KAAK,EAAC,gBAAgB;YAACL,EAAE,EAAE;cAAEE,EAAE,EAAE;YAAE,CAAE;YAAAP,QAAA,EAAC;UAElE;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAY,CAAC,eACb/I,OAAA,CAACrC,MAAM;YACLwL,OAAO,EAAC,WAAW;YACnBqB,SAAS,eAAExK,OAAA,CAACpB,OAAO;cAAAgK,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAE;YACvB0B,OAAO,EAAEA,CAAA,KAAMjJ,kBAAkB,CAAC,IAAI,CAAE;YACxCwH,EAAE,EAAE;cACF0B,aAAa,EAAE,MAAM;cACrBG,eAAe,EAAE,SAAS;cAC1BxB,KAAK,EAAE,OAAO;cACd,SAAS,EAAE;gBACTwB,eAAe,EAAE;cACnB;YACF,CAAE;YAAAlC,QAAA,EACH;UAED;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACJ,CAAC,gBAER/I,OAAA,CAACH,gBAAgB;UACfsE,OAAO,EAAElD,UAAW;UACpBkK,aAAa,EAAElF,uBAAuB,CAACmF,IAAI,CAAC,IAAI,EAAE,MAAM,CAAE;UAC1DC,aAAa,EAAEpF,uBAAuB,CAACmF,IAAI,CAAC,IAAI,EAAE,MAAM,CAAE;UAC1DE,eAAe,EAAExD,mBAAoB;UACrCrH,OAAO,EAAEU;QAAe;UAAAyH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACzB,CACF;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACE;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,eAGN/I,OAAA,CAAClC,MAAM;MACLyN,IAAI,EAAExJ,sBAAuB;MAC7ByJ,OAAO,EAAExE,6BAA8B;MACvCyE,QAAQ,EAAC,IAAI;MACbC,SAAS;MACTC,UAAU,EAAE;QACV3C,EAAE,EAAE;UAAEmB,YAAY,EAAE;QAAE;MACxB,CAAE;MAAAxB,QAAA,gBAEF3I,OAAA,CAACjC,WAAW;QAACiL,EAAE,EAAE;UAAE4C,EAAE,EAAE;QAAE,CAAE;QAAAjD,QAAA,eACzB3I,OAAA,CAACtC,UAAU;UAACyL,OAAO,EAAC,IAAI;UAACH,EAAE,EAAE;YAAEI,UAAU,EAAE;UAAI,CAAE;UAAAT,QAAA,EAC9C1G,sBAAsB,KAAK,QAAQ,GAAG,wBAAwB,GAAG;QAAuB;UAAA2G,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC/E;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACF,CAAC,eACd/I,OAAA,CAAChC,aAAa;QAAA2K,QAAA,eACZ3I,OAAA,CAACvC,GAAG;UAACuL,EAAE,EAAE;YAAE6C,EAAE,EAAE;UAAE,CAAE;UAAAlD,QAAA,gBACjB3I,OAAA,CAAC9B,SAAS;YACRwN,SAAS;YACTI,KAAK,EAAC,mBAAmB;YACzBC,KAAK,EAAE1J,oBAAoB,CAACE,iBAAkB;YAC9CyJ,QAAQ,EAAGC,CAAC,IAAK3J,uBAAuB,CAAC;cAAE,GAAGD,oBAAoB;cAAEE,iBAAiB,EAAE0J,CAAC,CAACC,MAAM,CAACH;YAAM,CAAC,CAAE;YACzGI,MAAM,EAAC,QAAQ;YACfC,QAAQ;YACRjD,OAAO,EAAC,UAAU;YAClBH,EAAE,EAAE;cAAEE,EAAE,EAAE;YAAE;UAAE;YAAAN,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACf,CAAC,eAEF/I,OAAA,CAAC9B,SAAS;YACRwN,SAAS;YACTI,KAAK,EAAC,OAAO;YACbO,IAAI,EAAC,OAAO;YACZN,KAAK,EAAE1J,oBAAoB,CAACG,KAAM;YAClCwJ,QAAQ,EAAGC,CAAC,IAAK3J,uBAAuB,CAAC;cAAE,GAAGD,oBAAoB;cAAEG,KAAK,EAAEyJ,CAAC,CAACC,MAAM,CAACH;YAAM,CAAC,CAAE;YAC7FI,MAAM,EAAC,QAAQ;YACfhD,OAAO,EAAC,UAAU;YAClBmD,UAAU,EAAC,uDAAuD;YAClEtD,EAAE,EAAE;cAAEE,EAAE,EAAE;YAAE;UAAE;YAAAN,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACf,CAAC,eAEF/I,OAAA,CAAC9B,SAAS;YACRwN,SAAS;YACTI,KAAK,EAAC,UAAU;YAChBC,KAAK,EAAE1J,oBAAoB,CAACI,QAAS;YACrCuJ,QAAQ,EAAGC,CAAC,IAAK3J,uBAAuB,CAAC;cAAE,GAAGD,oBAAoB;cAAEI,QAAQ,EAAEwJ,CAAC,CAACC,MAAM,CAACH;YAAM,CAAC,CAAE;YAChGI,MAAM,EAAC,QAAQ;YACfhD,OAAO,EAAC,UAAU;YAClBmD,UAAU,EAAC;UAA+C;YAAA1D,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC3D,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACC;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACO,CAAC,eAChB/I,OAAA,CAAC/B,aAAa;QAAC+K,EAAE,EAAE;UAAEC,CAAC,EAAE,CAAC;UAAE4C,EAAE,EAAE;QAAE,CAAE;QAAAlD,QAAA,gBACjC3I,OAAA,CAACrC,MAAM;UACL8M,OAAO,EAAEzD,6BAA8B;UACvCgC,EAAE,EAAE;YAAE0B,aAAa,EAAE;UAAO,CAAE;UAAA/B,QAAA,EAC/B;QAED;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC,eACT/I,OAAA,CAACrC,MAAM;UACL8M,OAAO,EAAExD,wBAAyB;UAClCkC,OAAO,EAAC,WAAW;UACnBH,EAAE,EAAE;YACF0B,aAAa,EAAE,MAAM;YACrBtB,UAAU,EAAE,GAAG;YACfuB,EAAE,EAAE;UACN,CAAE;UAAAhC,QAAA,EAED1G,sBAAsB,KAAK,QAAQ,GAAG,MAAM,GAAG;QAAO;UAAA2G,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACjD,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACI,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACV,CAAC,eAGT/I,OAAA,CAAClC,MAAM;MACLyN,IAAI,EAAE7I,iBAAkB;MACxB8I,OAAO,EAAE7D,wBAAyB;MAClC8D,QAAQ,EAAC,IAAI;MACbC,SAAS;MACTC,UAAU,EAAE;QACV3C,EAAE,EAAE;UAAEmB,YAAY,EAAE;QAAE;MACxB,CAAE;MAAAxB,QAAA,gBAEF3I,OAAA,CAACjC,WAAW;QAACiL,EAAE,EAAE;UAAE4C,EAAE,EAAE;QAAE,CAAE;QAAAjD,QAAA,eACzB3I,OAAA,CAACtC,UAAU;UAACyL,OAAO,EAAC,IAAI;UAACH,EAAE,EAAE;YAAEI,UAAU,EAAE;UAAI,CAAE;UAAAT,QAAA,EAC9C/F,iBAAiB,KAAK,MAAM,GAAG,kBAAkB,GAAG;QAAkB;UAAAgG,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC7D;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACF,CAAC,eACd/I,OAAA,CAAChC,aAAa;QAAA2K,QAAA,eACZ3I,OAAA,CAACvC,GAAG;UAACuL,EAAE,EAAE;YAAE6C,EAAE,EAAE;UAAE,CAAE;UAAAlD,QAAA,EAChB/F,iBAAiB,KAAK,MAAM,IAAIE,eAAe,gBAC9C9C,OAAA,CAAC3B,IAAI;YAAAsK,QAAA,gBACH3I,OAAA,CAAC1B,QAAQ;cAAAqK,QAAA,eACP3I,OAAA,CAACzB,YAAY;gBACXgO,OAAO,EAAC,gBAAgB;gBACxBC,SAAS,EAAE1J,eAAe,CAACkD;cAAe;gBAAA4C,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC3C;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACM,CAAC,eACX/I,OAAA,CAACtB,OAAO;cAAAkK,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,eACX/I,OAAA,CAAC1B,QAAQ;cAAAqK,QAAA,eACP3I,OAAA,CAACzB,YAAY;gBACXgO,OAAO,EAAC,MAAM;gBACdC,SAAS,EAAEvE,mBAAmB,CAACnF,eAAe,CAACI,YAAY;cAAE;gBAAA0F,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC9D;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACM,CAAC,eACX/I,OAAA,CAACtB,OAAO;cAAAkK,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,eACX/I,OAAA,CAAC1B,QAAQ;cAAAqK,QAAA,eACP3I,OAAA,CAACzB,YAAY;gBACXgO,OAAO,EAAC,OAAO;gBACfC,SAAS,eACPxM,OAAA,CAACnC,IAAI;kBACHiO,KAAK,EAAEhJ,eAAe,CAACuF,KAAM;kBAC7BgB,KAAK,EAAEjB,aAAa,CAACtF,eAAe,CAACuF,KAAK,CAAE;kBAC5CoE,IAAI,EAAC;gBAAO;kBAAA7D,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACb;cACF;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACF;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACM,CAAC,eACX/I,OAAA,CAACtB,OAAO;cAAAkK,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,eACX/I,OAAA,CAAC1B,QAAQ;cAAAqK,QAAA,eACP3I,OAAA,CAACzB,YAAY;gBACXgO,OAAO,EAAC,aAAa;gBACrBC,SAAS,EAAE1J,eAAe,CAACK,WAAW,IAAI;cAAsB;gBAAAyF,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACjE;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACM,CAAC,eACX/I,OAAA,CAACtB,OAAO;cAAAkK,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,eACX/I,OAAA,CAAC1B,QAAQ;cAAAqK,QAAA,eACP3I,OAAA,CAACzB,YAAY;gBACXgO,OAAO,EAAC,cAAc;gBACtBC,SAAS,EAAE1J,eAAe,CAACM,YAAY,IAAI;cAAgB;gBAAAwF,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC5D;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACM,CAAC,EACVjG,eAAe,CAACQ,kBAAkB,iBACjCtD,OAAA,CAAAE,SAAA;cAAAyI,QAAA,gBACE3I,OAAA,CAACtB,OAAO;gBAAAkK,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,eACX/I,OAAA,CAAC1B,QAAQ;gBAAAqK,QAAA,eACP3I,OAAA,CAACzB,YAAY;kBACXgO,OAAO,EAAC,oBAAoB;kBAC5BC,SAAS,EAAE1J,eAAe,CAACQ;gBAAmB;kBAAAsF,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAC/C;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACM,CAAC;YAAA,eACX,CACH,eACD/I,OAAA,CAACtB,OAAO;cAAAkK,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,eACX/I,OAAA,CAAC1B,QAAQ;cAAAqK,QAAA,eACP3I,OAAA,CAACzB,YAAY;gBACXgO,OAAO,EAAC,gBAAgB;gBACxBC,SAAS,EAAE,IAAIE,IAAI,CAAC5J,eAAe,CAAC6J,cAAc,CAAC,CAACC,kBAAkB,CAAC,OAAO;cAAE;gBAAAhE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACjF;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACM,CAAC,EACVjG,eAAe,CAACO,aAAa,iBAC5BrD,OAAA,CAAAE,SAAA;cAAAyI,QAAA,gBACE3I,OAAA,CAACtB,OAAO;gBAAAkK,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,eACX/I,OAAA,CAAC1B,QAAQ;gBAAAqK,QAAA,eACP3I,OAAA,CAACzB,YAAY;kBACXgO,OAAO,EAAC,eAAe;kBACvBC,SAAS,EAAE,IAAIE,IAAI,CAAC5J,eAAe,CAACO,aAAa,CAAC,CAACuJ,kBAAkB,CAAC,OAAO;gBAAE;kBAAAhE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAChF;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACM,CAAC;YAAA,eACX,CACH,eACD/I,OAAA,CAACtB,OAAO;cAAAkK,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,eACX/I,OAAA,CAAC1B,QAAQ;cAAAqK,QAAA,eACP3I,OAAA,CAACzB,YAAY;gBACXgO,OAAO,EAAC,gBAAgB;gBACxBC,SAAS,EAAE1J,eAAe,CAAC+J,qBAAqB,IAAI;cAAE;gBAAAjE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACvD;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACM,CAAC,eACX/I,OAAA,CAACtB,OAAO;cAAAkK,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,eACX/I,OAAA,CAAC1B,QAAQ;cAAAqK,QAAA,eACP3I,OAAA,CAACzB,YAAY;gBACXgO,OAAO,EAAC,eAAe;gBACvBC,SAAS,EAAE,GAAG,CAAC1J,eAAe,CAACgK,yBAAyB,IAAI,CAAC,EAAEC,OAAO,CAAC,CAAC,CAAC;cAAI;gBAAAnE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC9E;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACM,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACP,CAAC,gBAEP/I,OAAA,CAAAE,SAAA;YAAAyI,QAAA,gBACE3I,OAAA,CAAC9B,SAAS;cACRwN,SAAS;cACTsB,MAAM;cACNlB,KAAK,EAAC,cAAc;cACpBC,KAAK,EAAE/I,eAAe,CAACE,YAAa;cACpC8I,QAAQ,EAAGC,CAAC,IAAKhJ,kBAAkB,CAAC;gBAAE,GAAGD,eAAe;gBAAEE,YAAY,EAAE+I,CAAC,CAACC,MAAM,CAACH;cAAM,CAAC,CAAE;cAC1FI,MAAM,EAAC,QAAQ;cACfnD,EAAE,EAAE;gBAAEE,EAAE,EAAE;cAAE,CAAE;cAAAP,QAAA,gBAEd3I,OAAA,CAACvB,QAAQ;gBAACsN,KAAK,EAAC,MAAM;gBAAApD,QAAA,EAAC;cAAI;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAU,CAAC,eACtC/I,OAAA,CAACvB,QAAQ;gBAACsN,KAAK,EAAC,uBAAuB;gBAAApD,QAAA,EAAC;cAAqB;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAU,CAAC,eACxE/I,OAAA,CAACvB,QAAQ;gBAACsN,KAAK,EAAC,qBAAqB;gBAAApD,QAAA,EAAC;cAAmB;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAU,CAAC,eACpE/I,OAAA,CAACvB,QAAQ;gBAACsN,KAAK,EAAC,gBAAgB;gBAAApD,QAAA,EAAC;cAAc;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAU,CAAC,eAC1D/I,OAAA,CAACvB,QAAQ;gBAACsN,KAAK,EAAC,SAAS;gBAAApD,QAAA,EAAC;cAAO;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAU,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACnC,CAAC,eAIZ/I,OAAA,CAAC9B,SAAS;cACRwN,SAAS;cACTI,KAAK,EAAC,aAAa;cACnBC,KAAK,EAAE/I,eAAe,CAACG,WAAY;cACnC6I,QAAQ,EAAGC,CAAC,IAAKhJ,kBAAkB,CAAC;gBAAE,GAAGD,eAAe;gBAAEG,WAAW,EAAE8I,CAAC,CAACC,MAAM,CAACH;cAAM,CAAC,CAAE;cACzFI,MAAM,EAAC,QAAQ;cACfc,SAAS;cACTC,IAAI,EAAE,CAAE;cACRlE,EAAE,EAAE;gBAAEE,EAAE,EAAE;cAAE;YAAE;cAAAN,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACf,CAAC,eAEF/I,OAAA,CAAC9B,SAAS;cACRwN,SAAS;cACTI,KAAK,EAAC,cAAc;cACpBC,KAAK,EAAE/I,eAAe,CAACI,YAAa;cACpC4I,QAAQ,EAAGC,CAAC,IAAKhJ,kBAAkB,CAAC;gBAAE,GAAGD,eAAe;gBAAEI,YAAY,EAAE6I,CAAC,CAACC,MAAM,CAACH;cAAM,CAAC,CAAE;cAC1FI,MAAM,EAAC,QAAQ;cACfC,QAAQ;cACRE,UAAU,EAAC,0CAAuC;cAClDtD,EAAE,EAAE;gBAAEE,EAAE,EAAE;cAAE;YAAE;cAAAN,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACf,CAAC,eAEF/I,OAAA,CAAC9B,SAAS;cACRwN,SAAS;cACTI,KAAK,EAAC,oBAAoB;cAC1BC,KAAK,EAAE/I,eAAe,CAACM,kBAAmB;cAC1C0I,QAAQ,EAAGC,CAAC,IAAKhJ,kBAAkB,CAAC;gBAAE,GAAGD,eAAe;gBAAEM,kBAAkB,EAAE2I,CAAC,CAACC,MAAM,CAACH;cAAM,CAAC,CAAE;cAChGI,MAAM,EAAC,QAAQ;cACfc,SAAS;cACTC,IAAI,EAAE,CAAE;cACRZ,UAAU,EAAC,2CAA2C;cACtDtD,EAAE,EAAE;gBAAEE,EAAE,EAAE;cAAE;YAAE;cAAAN,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACf,CAAC,eAEF/I,OAAA,CAAC9B,SAAS;cACRwN,SAAS;cACTI,KAAK,EAAC,eAAe;cACrBO,IAAI,EAAC,MAAM;cACXN,KAAK,EAAE/I,eAAe,CAACK,aAAc;cACrC2I,QAAQ,EAAGC,CAAC,IAAKhJ,kBAAkB,CAAC;gBAAE,GAAGD,eAAe;gBAAEK,aAAa,EAAE4I,CAAC,CAACC,MAAM,CAACH;cAAM,CAAC,CAAE;cAC3FI,MAAM,EAAC,QAAQ;cACfgB,eAAe,EAAE;gBACfC,MAAM,EAAE;cACV;YAAE;cAAAxE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC;UAAA,eACF;QACH;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACE;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACO,CAAC,eAChB/I,OAAA,CAAC/B,aAAa;QAAC+K,EAAE,EAAE;UAAEC,CAAC,EAAE,CAAC;UAAE4C,EAAE,EAAE;QAAE,CAAE;QAAAlD,QAAA,gBACjC3I,OAAA,CAACrC,MAAM;UACL8M,OAAO,EAAE9C,wBAAyB;UAClCqB,EAAE,EAAE;YAAE0B,aAAa,EAAE;UAAO,CAAE;UAAA/B,QAAA,EAE7B/F,iBAAiB,KAAK,MAAM,GAAG,QAAQ,GAAG;QAAS;UAAAgG,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC9C,CAAC,EACRnG,iBAAiB,KAAK,MAAM,iBAC3B5C,OAAA,CAACrC,MAAM;UACL8M,OAAO,EAAE7C,mBAAoB;UAC7BuB,OAAO,EAAC,WAAW;UACnBH,EAAE,EAAE;YACF0B,aAAa,EAAE,MAAM;YACrBtB,UAAU,EAAE,GAAG;YACfuB,EAAE,EAAE;UACN,CAAE;UAAAhC,QAAA,EACH;QAED;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CACT;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACY,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACV,CAAC,eAGT/I,OAAA,CAACL,kBAAkB;MACjBS,UAAU,EAAEA,UAAW;MACvBmL,IAAI,EAAEhK,eAAgB;MACtBiK,OAAO,EAAEA,CAAA,KAAMhK,kBAAkB,CAAC,KAAK,CAAE;MACzC6L,SAAS,EAAEA,CAAC1I,QAAQ,EAAE2I,cAAc,KAAK;QACvC1J,OAAO,CAACC,GAAG,CAAC,iDAAiD,CAAC;;QAE9D;QACA,IAAIyJ,cAAc,EAAE;UAClB;UACA1J,OAAO,CAACC,GAAG,CAAC,cAAc,EAAEyJ,cAAc,CAAC;QAC7C;;QAEA;QACA3J,WAAW,CAAC,CAAC;QACbY,eAAe,CAAC,CAAC;QACjBM,gBAAgB,CAAC,CAAC;QAClBrD,kBAAkB,CAAC,KAAK,CAAC;QAEzBoC,OAAO,CAACC,GAAG,CAAC,0BAA0B,CAAC;MACzC;IAAE;MAAA+E,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,eAGF/I,OAAA,CAACJ,qBAAqB;MACpB2L,IAAI,EAAElK,qBAAsB;MAC5BmK,OAAO,EAAEA,CAAA,KAAMlK,wBAAwB,CAAC,KAAK,CAAE;MAC/CG,YAAY,EAAEA,YAAa;MAC3BI,sBAAsB,EAAEA,sBAAuB;MAC/C0L,kBAAkB,EAAGnK,YAAY,IAAK;QACpC9B,wBAAwB,CAAC,KAAK,CAAC;QAC/BwF,4BAA4B,CAAC,MAAM,EAAE1D,YAAY,CAAC;MACpD,CAAE;MACFoK,oBAAoB,EAAE,MAAOlG,cAAc,IAAK;QAC9C,MAAMD,wBAAwB,CAACC,cAAc,CAAC;QAC9ChG,wBAAwB,CAAC,KAAK,CAAC;MACjC,CAAE;MACFb,OAAO,EAAEkB,mBAAoB;MAC7BhB,KAAK,EAAEA;IAAM;MAAAiI,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACd,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACC,CAAC;AAEV,CAAC;AAACzI,EAAA,CA/4BIH,wBAAwB;EAAA,QAEY3C,eAAe;AAAA;AAAAiQ,EAAA,GAFnDtN,wBAAwB;AAi5B9B,eAAeA,wBAAwB;AAAC,IAAAsN,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}