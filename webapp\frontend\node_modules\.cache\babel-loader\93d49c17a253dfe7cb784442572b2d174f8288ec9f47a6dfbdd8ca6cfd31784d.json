{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\PycharmProjects\\\\CMS\\\\webapp\\\\frontend\\\\src\\\\pages\\\\cavi\\\\posa\\\\AggiungiCavoPage.js\",\n  _s = $RefreshSig$();\nimport React, { useState } from 'react';\nimport { Box, Typography, Paper, Button, IconButton, Alert, Snackbar } from '@mui/material';\nimport { ArrowBack as ArrowBackIcon, Refresh as RefreshIcon, Home as HomeIcon } from '@mui/icons-material';\nimport { useNavigate } from 'react-router-dom';\nimport { useAuth } from '../../../context/AuthContext';\nimport AdminHomeButton from '../../../components/common/AdminHomeButton';\nimport AggiungiCavoForm from '../../../components/cavi/AggiungiCavoForm';\nimport caviService from '../../../services/caviService';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst AggiungiCavoPage = () => {\n  _s();\n  const navigate = useNavigate();\n  const {\n    isImpersonating\n  } = useAuth();\n  const [alertMessage, setAlertMessage] = useState(null);\n  const [alertSeverity, setAlertSeverity] = useState('success');\n  const [openSnackbar, setOpenSnackbar] = useState(false);\n\n  // Recupera l'ID del cantiere selezionato dal localStorage\n  const cantiereId = localStorage.getItem('selectedCantiereId');\n  const cantiereName = localStorage.getItem('selectedCantiereName');\n\n  // Gestisce il ritorno alla pagina dei cantieri\n  const handleBackToCantieri = () => {\n    navigate('/dashboard/cantieri');\n  };\n\n  // Gestisce il ritorno al menu admin (per admin che impersonano utenti)\n  const handleBackToAdmin = () => {\n    navigate('/dashboard/admin');\n  };\n\n  // Gestisce il successo di un'operazione\n  const handleSuccess = message => {\n    setAlertMessage(message);\n    setAlertSeverity('success');\n    setOpenSnackbar(true);\n  };\n\n  // Gestisce l'errore di un'operazione\n  const handleError = message => {\n    setAlertMessage(message);\n    setAlertSeverity('error');\n    setOpenSnackbar(true);\n  };\n\n  // Chiude lo snackbar\n  const handleCloseSnackbar = () => {\n    setOpenSnackbar(false);\n  };\n\n  // Se non c'è un cantiere selezionato, reindirizza alla pagina dei cantieri\n  if (!cantiereId) {\n    navigate('/dashboard/cantieri');\n    return null;\n  }\n  return /*#__PURE__*/_jsxDEV(Box, {\n    children: [/*#__PURE__*/_jsxDEV(Box, {\n      sx: {\n        mb: 3,\n        display: 'flex',\n        alignItems: 'center',\n        justifyContent: 'space-between'\n      },\n      children: [/*#__PURE__*/_jsxDEV(Box, {\n        sx: {\n          display: 'flex',\n          alignItems: 'center'\n        },\n        children: [/*#__PURE__*/_jsxDEV(Typography, {\n          variant: \"h6\",\n          children: \"Aggiungi Nuovo Cavo\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 75,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(IconButton, {\n          onClick: () => window.location.reload(),\n          sx: {\n            ml: 2\n          },\n          color: \"primary\",\n          title: \"Ricarica la pagina\",\n          children: /*#__PURE__*/_jsxDEV(RefreshIcon, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 84,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 78,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 73,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(AdminHomeButton, {}, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 87,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 72,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(AggiungiCavoForm, {\n      cantiereId: cantiereId,\n      onSuccess: handleSuccess,\n      onError: handleError\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 95,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Snackbar, {\n      open: openSnackbar,\n      autoHideDuration: 6000,\n      onClose: handleCloseSnackbar,\n      anchorOrigin: {\n        vertical: 'bottom',\n        horizontal: 'center'\n      },\n      children: /*#__PURE__*/_jsxDEV(Alert, {\n        onClose: handleCloseSnackbar,\n        severity: alertSeverity,\n        sx: {\n          width: '100%'\n        },\n        children: alertMessage\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 107,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 101,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 71,\n    columnNumber: 5\n  }, this);\n};\n_s(AggiungiCavoPage, \"OwbUx2RsBYDYpTx8dGW8kzIcrXM=\", false, function () {\n  return [useNavigate, useAuth];\n});\n_c = AggiungiCavoPage;\nexport default AggiungiCavoPage;\nvar _c;\n$RefreshReg$(_c, \"AggiungiCavoPage\");", "map": {"version": 3, "names": ["React", "useState", "Box", "Typography", "Paper", "<PERSON><PERSON>", "IconButton", "<PERSON><PERSON>", "Snackbar", "ArrowBack", "ArrowBackIcon", "Refresh", "RefreshIcon", "Home", "HomeIcon", "useNavigate", "useAuth", "AdminHomeButton", "AggiungiCavoForm", "caviService", "jsxDEV", "_jsxDEV", "AggiungiCavoPage", "_s", "navigate", "isImpersonating", "alertMessage", "setAlertMessage", "alertSeverity", "setAlertSeverity", "openSnackbar", "setOpenSnackbar", "cantiereId", "localStorage", "getItem", "cantiereName", "handleBackToCantieri", "handleBackToAdmin", "handleSuccess", "message", "handleError", "handleCloseSnackbar", "children", "sx", "mb", "display", "alignItems", "justifyContent", "variant", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "onClick", "window", "location", "reload", "ml", "color", "title", "onSuccess", "onError", "open", "autoHideDuration", "onClose", "anchor<PERSON><PERSON><PERSON>", "vertical", "horizontal", "severity", "width", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/PycharmProjects/CMS/webapp/frontend/src/pages/cavi/posa/AggiungiCavoPage.js"], "sourcesContent": ["import React, { useState } from 'react';\nimport {\n  Box,\n  Typography,\n  Paper,\n  Button,\n  IconButton,\n  Alert,\n  Snackbar\n} from '@mui/material';\nimport {\n  ArrowBack as ArrowBackIcon,\n  Refresh as RefreshIcon,\n  Home as HomeIcon\n} from '@mui/icons-material';\nimport { useNavigate } from 'react-router-dom';\nimport { useAuth } from '../../../context/AuthContext';\nimport AdminHomeButton from '../../../components/common/AdminHomeButton';\nimport AggiungiCavoForm from '../../../components/cavi/AggiungiCavoForm';\nimport caviService from '../../../services/caviService';\n\nconst AggiungiCavoPage = () => {\n  const navigate = useNavigate();\n  const { isImpersonating } = useAuth();\n  const [alertMessage, setAlertMessage] = useState(null);\n  const [alertSeverity, setAlertSeverity] = useState('success');\n  const [openSnackbar, setOpenSnackbar] = useState(false);\n\n  // Recupera l'ID del cantiere selezionato dal localStorage\n  const cantiereId = localStorage.getItem('selectedCantiereId');\n  const cantiereName = localStorage.getItem('selectedCantiereName');\n\n  // Gestisce il ritorno alla pagina dei cantieri\n  const handleBackToCantieri = () => {\n    navigate('/dashboard/cantieri');\n  };\n\n  // Gestisce il ritorno al menu admin (per admin che impersonano utenti)\n  const handleBackToAdmin = () => {\n    navigate('/dashboard/admin');\n  };\n\n\n\n  // Gestisce il successo di un'operazione\n  const handleSuccess = (message) => {\n    setAlertMessage(message);\n    setAlertSeverity('success');\n    setOpenSnackbar(true);\n  };\n\n  // Gestisce l'errore di un'operazione\n  const handleError = (message) => {\n    setAlertMessage(message);\n    setAlertSeverity('error');\n    setOpenSnackbar(true);\n  };\n\n  // Chiude lo snackbar\n  const handleCloseSnackbar = () => {\n    setOpenSnackbar(false);\n  };\n\n  // Se non c'è un cantiere selezionato, reindirizza alla pagina dei cantieri\n  if (!cantiereId) {\n    navigate('/dashboard/cantieri');\n    return null;\n  }\n\n  return (\n    <Box>\n      <Box sx={{ mb: 3, display: 'flex', alignItems: 'center', justifyContent: 'space-between' }}>\n        <Box sx={{ display: 'flex', alignItems: 'center' }}>\n\n          <Typography variant=\"h6\">\n            Aggiungi Nuovo Cavo\n          </Typography>\n          <IconButton\n            onClick={() => window.location.reload()}\n            sx={{ ml: 2 }}\n            color=\"primary\"\n            title=\"Ricarica la pagina\"\n          >\n            <RefreshIcon />\n          </IconButton>\n        </Box>\n        <AdminHomeButton />\n      </Box>\n\n\n\n\n\n      {/* Componente per l'aggiunta del cavo */}\n      <AggiungiCavoForm\n        cantiereId={cantiereId}\n        onSuccess={handleSuccess}\n        onError={handleError}\n      />\n\n      <Snackbar\n        open={openSnackbar}\n        autoHideDuration={6000}\n        onClose={handleCloseSnackbar}\n        anchorOrigin={{ vertical: 'bottom', horizontal: 'center' }}\n      >\n        <Alert onClose={handleCloseSnackbar} severity={alertSeverity} sx={{ width: '100%' }}>\n          {alertMessage}\n        </Alert>\n      </Snackbar>\n    </Box>\n  );\n};\n\nexport default AggiungiCavoPage;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,QAAQ,OAAO;AACvC,SACEC,GAAG,EACHC,UAAU,EACVC,KAAK,EACLC,MAAM,EACNC,UAAU,EACVC,KAAK,EACLC,QAAQ,QACH,eAAe;AACtB,SACEC,SAAS,IAAIC,aAAa,EAC1BC,OAAO,IAAIC,WAAW,EACtBC,IAAI,IAAIC,QAAQ,QACX,qBAAqB;AAC5B,SAASC,WAAW,QAAQ,kBAAkB;AAC9C,SAASC,OAAO,QAAQ,8BAA8B;AACtD,OAAOC,eAAe,MAAM,4CAA4C;AACxE,OAAOC,gBAAgB,MAAM,2CAA2C;AACxE,OAAOC,WAAW,MAAM,+BAA+B;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAExD,MAAMC,gBAAgB,GAAGA,CAAA,KAAM;EAAAC,EAAA;EAC7B,MAAMC,QAAQ,GAAGT,WAAW,CAAC,CAAC;EAC9B,MAAM;IAAEU;EAAgB,CAAC,GAAGT,OAAO,CAAC,CAAC;EACrC,MAAM,CAACU,YAAY,EAAEC,eAAe,CAAC,GAAG1B,QAAQ,CAAC,IAAI,CAAC;EACtD,MAAM,CAAC2B,aAAa,EAAEC,gBAAgB,CAAC,GAAG5B,QAAQ,CAAC,SAAS,CAAC;EAC7D,MAAM,CAAC6B,YAAY,EAAEC,eAAe,CAAC,GAAG9B,QAAQ,CAAC,KAAK,CAAC;;EAEvD;EACA,MAAM+B,UAAU,GAAGC,YAAY,CAACC,OAAO,CAAC,oBAAoB,CAAC;EAC7D,MAAMC,YAAY,GAAGF,YAAY,CAACC,OAAO,CAAC,sBAAsB,CAAC;;EAEjE;EACA,MAAME,oBAAoB,GAAGA,CAAA,KAAM;IACjCZ,QAAQ,CAAC,qBAAqB,CAAC;EACjC,CAAC;;EAED;EACA,MAAMa,iBAAiB,GAAGA,CAAA,KAAM;IAC9Bb,QAAQ,CAAC,kBAAkB,CAAC;EAC9B,CAAC;;EAID;EACA,MAAMc,aAAa,GAAIC,OAAO,IAAK;IACjCZ,eAAe,CAACY,OAAO,CAAC;IACxBV,gBAAgB,CAAC,SAAS,CAAC;IAC3BE,eAAe,CAAC,IAAI,CAAC;EACvB,CAAC;;EAED;EACA,MAAMS,WAAW,GAAID,OAAO,IAAK;IAC/BZ,eAAe,CAACY,OAAO,CAAC;IACxBV,gBAAgB,CAAC,OAAO,CAAC;IACzBE,eAAe,CAAC,IAAI,CAAC;EACvB,CAAC;;EAED;EACA,MAAMU,mBAAmB,GAAGA,CAAA,KAAM;IAChCV,eAAe,CAAC,KAAK,CAAC;EACxB,CAAC;;EAED;EACA,IAAI,CAACC,UAAU,EAAE;IACfR,QAAQ,CAAC,qBAAqB,CAAC;IAC/B,OAAO,IAAI;EACb;EAEA,oBACEH,OAAA,CAACnB,GAAG;IAAAwC,QAAA,gBACFrB,OAAA,CAACnB,GAAG;MAACyC,EAAE,EAAE;QAAEC,EAAE,EAAE,CAAC;QAAEC,OAAO,EAAE,MAAM;QAAEC,UAAU,EAAE,QAAQ;QAAEC,cAAc,EAAE;MAAgB,CAAE;MAAAL,QAAA,gBACzFrB,OAAA,CAACnB,GAAG;QAACyC,EAAE,EAAE;UAAEE,OAAO,EAAE,MAAM;UAAEC,UAAU,EAAE;QAAS,CAAE;QAAAJ,QAAA,gBAEjDrB,OAAA,CAAClB,UAAU;UAAC6C,OAAO,EAAC,IAAI;UAAAN,QAAA,EAAC;QAEzB;UAAAO,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAY,CAAC,eACb/B,OAAA,CAACf,UAAU;UACT+C,OAAO,EAAEA,CAAA,KAAMC,MAAM,CAACC,QAAQ,CAACC,MAAM,CAAC,CAAE;UACxCb,EAAE,EAAE;YAAEc,EAAE,EAAE;UAAE,CAAE;UACdC,KAAK,EAAC,SAAS;UACfC,KAAK,EAAC,oBAAoB;UAAAjB,QAAA,eAE1BrB,OAAA,CAACT,WAAW;YAAAqC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACL,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACV,CAAC,eACN/B,OAAA,CAACJ,eAAe;QAAAgC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAChB,CAAC,eAON/B,OAAA,CAACH,gBAAgB;MACfc,UAAU,EAAEA,UAAW;MACvB4B,SAAS,EAAEtB,aAAc;MACzBuB,OAAO,EAAErB;IAAY;MAAAS,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACtB,CAAC,eAEF/B,OAAA,CAACb,QAAQ;MACPsD,IAAI,EAAEhC,YAAa;MACnBiC,gBAAgB,EAAE,IAAK;MACvBC,OAAO,EAAEvB,mBAAoB;MAC7BwB,YAAY,EAAE;QAAEC,QAAQ,EAAE,QAAQ;QAAEC,UAAU,EAAE;MAAS,CAAE;MAAAzB,QAAA,eAE3DrB,OAAA,CAACd,KAAK;QAACyD,OAAO,EAAEvB,mBAAoB;QAAC2B,QAAQ,EAAExC,aAAc;QAACe,EAAE,EAAE;UAAE0B,KAAK,EAAE;QAAO,CAAE;QAAA3B,QAAA,EACjFhB;MAAY;QAAAuB,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACR;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACA,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACR,CAAC;AAEV,CAAC;AAAC7B,EAAA,CA3FID,gBAAgB;EAAA,QACHP,WAAW,EACAC,OAAO;AAAA;AAAAsD,EAAA,GAF/BhD,gBAAgB;AA6FtB,eAAeA,gBAAgB;AAAC,IAAAgD,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}