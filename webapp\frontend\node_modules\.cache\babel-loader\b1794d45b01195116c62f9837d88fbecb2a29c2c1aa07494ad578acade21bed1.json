{"ast": null, "code": "import { formatDistance } from \"./ja-<PERSON>ra/_lib/formatDistance.mjs\";\nimport { formatLong } from \"./ja-Hira/_lib/formatLong.mjs\";\nimport { formatRelative } from \"./ja-<PERSON>ra/_lib/formatRelative.mjs\";\nimport { localize } from \"./ja-<PERSON>ra/_lib/localize.mjs\";\nimport { match } from \"./ja-Hira/_lib/match.mjs\";\n\n/**\n * @category Locales\n * @summary Japanese (Hiragana) locale.\n * @language Japanese (Hiragana)\n * @iso-639-2 jpn\n * <AUTHOR> [@Eritutteo](https://github.com/Eritutteo)\n */\nexport const jaHira = {\n  code: \"ja-<PERSON>ra\",\n  formatDistance: formatDistance,\n  formatLong: formatLong,\n  formatRelative: formatRelative,\n  localize: localize,\n  match: match,\n  options: {\n    weekStartsOn: 0 /* Sunday */,\n    firstWeekContainsDate: 1\n  }\n};\n\n// Fallback for modularized imports:\nexport default jaHira;", "map": {"version": 3, "names": ["formatDistance", "formatLong", "formatRelative", "localize", "match", "j<PERSON><PERSON><PERSON>", "code", "options", "weekStartsOn", "firstWeekContainsDate"], "sources": ["C:/CMS/webapp/frontend/node_modules/date-fns/locale/ja-Hira.mjs"], "sourcesContent": ["import { formatDistance } from \"./ja-<PERSON>ra/_lib/formatDistance.mjs\";\nimport { formatLong } from \"./ja-Hira/_lib/formatLong.mjs\";\nimport { formatRelative } from \"./ja-Hira/_lib/formatRelative.mjs\";\nimport { localize } from \"./ja-<PERSON>ra/_lib/localize.mjs\";\nimport { match } from \"./ja-Hira/_lib/match.mjs\";\n\n/**\n * @category Locales\n * @summary Japanese (Hiragana) locale.\n * @language Japanese (Hiragana)\n * @iso-639-2 jpn\n * <AUTHOR> [@Eritutteo](https://github.com/Eritutteo)\n */\nexport const jaHira = {\n  code: \"ja-<PERSON>ra\",\n  formatDistance: formatDistance,\n  formatLong: formatLong,\n  formatRelative: formatRelative,\n  localize: localize,\n  match: match,\n  options: {\n    weekStartsOn: 0 /* Sunday */,\n    firstWeekContainsDate: 1,\n  },\n};\n\n// Fallback for modularized imports:\nexport default jaHira;\n"], "mappings": "AAAA,SAASA,cAAc,QAAQ,mCAAmC;AAClE,SAASC,UAAU,QAAQ,+BAA+B;AAC1D,SAASC,cAAc,QAAQ,mCAAmC;AAClE,SAASC,QAAQ,QAAQ,6BAA6B;AACtD,SAASC,KAAK,QAAQ,0BAA0B;;AAEhD;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO,MAAMC,MAAM,GAAG;EACpBC,IAAI,EAAE,SAAS;EACfN,cAAc,EAAEA,cAAc;EAC9BC,UAAU,EAAEA,UAAU;EACtBC,cAAc,EAAEA,cAAc;EAC9BC,QAAQ,EAAEA,QAAQ;EAClBC,KAAK,EAAEA,KAAK;EACZG,OAAO,EAAE;IACPC,YAAY,EAAE,CAAC,CAAC;IAChBC,qBAAqB,EAAE;EACzB;AACF,CAAC;;AAED;AACA,eAAeJ,MAAM", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}