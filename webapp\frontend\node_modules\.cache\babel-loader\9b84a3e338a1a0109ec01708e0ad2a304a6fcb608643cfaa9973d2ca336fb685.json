{"ast": null, "code": "const formatDistanceLocale = {\n  lessThanXSeconds: {\n    one: \"أقل من ثانية\",\n    two: \"أقل من زوز ثواني\",\n    threeToTen: \"أقل من {{count}} ثواني\",\n    other: \"أقل من {{count}} ثانية\"\n  },\n  xSeconds: {\n    one: \"ثانية\",\n    two: \"زوز ثواني\",\n    threeToTen: \"{{count}} ثواني\",\n    other: \"{{count}} ثانية\"\n  },\n  halfAMinute: \"نص دقيقة\",\n  lessThanXMinutes: {\n    one: \"أقل من دقيقة\",\n    two: \"أقل من دقيقتين\",\n    threeToTen: \"أقل من {{count}} دقايق\",\n    other: \"أقل من {{count}} دقيقة\"\n  },\n  xMinutes: {\n    one: \"دقيقة\",\n    two: \"دقيقتين\",\n    threeToTen: \"{{count}} دقايق\",\n    other: \"{{count}} دقيقة\"\n  },\n  aboutXHours: {\n    one: \"ساعة تقريب\",\n    two: \"ساعتين تقريب\",\n    threeToTen: \"{{count}} سوايع تقريب\",\n    other: \"{{count}} ساعة تقريب\"\n  },\n  xHours: {\n    one: \"ساعة\",\n    two: \"ساعتين\",\n    threeToTen: \"{{count}} سوايع\",\n    other: \"{{count}} ساعة\"\n  },\n  xDays: {\n    one: \"نهار\",\n    two: \"نهارين\",\n    threeToTen: \"{{count}} أيام\",\n    other: \"{{count}} يوم\"\n  },\n  aboutXWeeks: {\n    one: \"جمعة تقريب\",\n    two: \"جمعتين تقريب\",\n    threeToTen: \"{{count}} جماع تقريب\",\n    other: \"{{count}} جمعة تقريب\"\n  },\n  xWeeks: {\n    one: \"جمعة\",\n    two: \"جمعتين\",\n    threeToTen: \"{{count}} جماع\",\n    other: \"{{count}} جمعة\"\n  },\n  aboutXMonths: {\n    one: \"شهر تقريب\",\n    two: \"شهرين تقريب\",\n    threeToTen: \"{{count}} أشهرة تقريب\",\n    other: \"{{count}} شهر تقريب\"\n  },\n  xMonths: {\n    one: \"شهر\",\n    two: \"شهرين\",\n    threeToTen: \"{{count}} أشهرة\",\n    other: \"{{count}} شهر\"\n  },\n  aboutXYears: {\n    one: \"عام تقريب\",\n    two: \"عامين تقريب\",\n    threeToTen: \"{{count}} أعوام تقريب\",\n    other: \"{{count}} عام تقريب\"\n  },\n  xYears: {\n    one: \"عام\",\n    two: \"عامين\",\n    threeToTen: \"{{count}} أعوام\",\n    other: \"{{count}} عام\"\n  },\n  overXYears: {\n    one: \"أكثر من عام\",\n    two: \"أكثر من عامين\",\n    threeToTen: \"أكثر من {{count}} أعوام\",\n    other: \"أكثر من {{count}} عام\"\n  },\n  almostXYears: {\n    one: \"عام تقريب\",\n    two: \"عامين تقريب\",\n    threeToTen: \"{{count}} أعوام تقريب\",\n    other: \"{{count}} عام تقريب\"\n  }\n};\nexport const formatDistance = (token, count, options) => {\n  const usageGroup = formatDistanceLocale[token];\n  let result;\n  if (typeof usageGroup === \"string\") {\n    result = usageGroup;\n  } else if (count === 1) {\n    result = usageGroup.one;\n  } else if (count === 2) {\n    result = usageGroup.two;\n  } else if (count <= 10) {\n    result = usageGroup.threeToTen.replace(\"{{count}}\", String(count));\n  } else {\n    result = usageGroup.other.replace(\"{{count}}\", String(count));\n  }\n  if (options?.addSuffix) {\n    if (options.comparison && options.comparison > 0) {\n      return \"في \" + result;\n    } else {\n      return \"عندو \" + result;\n    }\n  }\n  return result;\n};", "map": {"version": 3, "names": ["formatDistanceLocale", "lessThanXSeconds", "one", "two", "threeToTen", "other", "xSeconds", "halfAMinute", "lessThanXMinutes", "xMinutes", "aboutXHours", "xHours", "xDays", "aboutXWeeks", "xWeeks", "aboutXMonths", "xMonths", "aboutXYears", "xYears", "overXYears", "almostXYears", "formatDistance", "token", "count", "options", "usageGroup", "result", "replace", "String", "addSuffix", "comparison"], "sources": ["C:/CMS/webapp/frontend/node_modules/date-fns/locale/ar-TN/_lib/formatDistance.js"], "sourcesContent": ["const formatDistanceLocale = {\n  lessThanXSeconds: {\n    one: \"أقل من ثانية\",\n    two: \"أقل من زوز ثواني\",\n    threeToTen: \"أقل من {{count}} ثواني\",\n    other: \"أقل من {{count}} ثانية\",\n  },\n\n  xSeconds: {\n    one: \"ثانية\",\n    two: \"زوز ثواني\",\n    threeToTen: \"{{count}} ثواني\",\n    other: \"{{count}} ثانية\",\n  },\n\n  halfAMinute: \"نص دقيقة\",\n\n  lessThanXMinutes: {\n    one: \"أقل من دقيقة\",\n    two: \"أقل من دقيقتين\",\n    threeToTen: \"أقل من {{count}} دقايق\",\n    other: \"أقل من {{count}} دقيقة\",\n  },\n\n  xMinutes: {\n    one: \"دقيقة\",\n    two: \"دقيقتين\",\n    threeToTen: \"{{count}} دقايق\",\n    other: \"{{count}} دقيقة\",\n  },\n\n  aboutXHours: {\n    one: \"ساعة تقريب\",\n    two: \"ساعتين تقريب\",\n    threeToTen: \"{{count}} سوايع تقريب\",\n    other: \"{{count}} ساعة تقريب\",\n  },\n\n  xHours: {\n    one: \"ساعة\",\n    two: \"ساعتين\",\n    threeToTen: \"{{count}} سوايع\",\n    other: \"{{count}} ساعة\",\n  },\n\n  xDays: {\n    one: \"نهار\",\n    two: \"نهارين\",\n    threeToTen: \"{{count}} أيام\",\n    other: \"{{count}} يوم\",\n  },\n\n  aboutXWeeks: {\n    one: \"جمعة تقريب\",\n    two: \"جمعتين تقريب\",\n    threeToTen: \"{{count}} جماع تقريب\",\n    other: \"{{count}} جمعة تقريب\",\n  },\n\n  xWeeks: {\n    one: \"جمعة\",\n    two: \"جمعتين\",\n    threeToTen: \"{{count}} جماع\",\n    other: \"{{count}} جمعة\",\n  },\n\n  aboutXMonths: {\n    one: \"شهر تقريب\",\n    two: \"شهرين تقريب\",\n    threeToTen: \"{{count}} أشهرة تقريب\",\n    other: \"{{count}} شهر تقريب\",\n  },\n\n  xMonths: {\n    one: \"شهر\",\n    two: \"شهرين\",\n    threeToTen: \"{{count}} أشهرة\",\n    other: \"{{count}} شهر\",\n  },\n\n  aboutXYears: {\n    one: \"عام تقريب\",\n    two: \"عامين تقريب\",\n    threeToTen: \"{{count}} أعوام تقريب\",\n    other: \"{{count}} عام تقريب\",\n  },\n\n  xYears: {\n    one: \"عام\",\n    two: \"عامين\",\n    threeToTen: \"{{count}} أعوام\",\n    other: \"{{count}} عام\",\n  },\n\n  overXYears: {\n    one: \"أكثر من عام\",\n    two: \"أكثر من عامين\",\n    threeToTen: \"أكثر من {{count}} أعوام\",\n    other: \"أكثر من {{count}} عام\",\n  },\n\n  almostXYears: {\n    one: \"عام تقريب\",\n    two: \"عامين تقريب\",\n    threeToTen: \"{{count}} أعوام تقريب\",\n    other: \"{{count}} عام تقريب\",\n  },\n};\n\nexport const formatDistance = (token, count, options) => {\n  const usageGroup = formatDistanceLocale[token];\n  let result;\n  if (typeof usageGroup === \"string\") {\n    result = usageGroup;\n  } else if (count === 1) {\n    result = usageGroup.one;\n  } else if (count === 2) {\n    result = usageGroup.two;\n  } else if (count <= 10) {\n    result = usageGroup.threeToTen.replace(\"{{count}}\", String(count));\n  } else {\n    result = usageGroup.other.replace(\"{{count}}\", String(count));\n  }\n\n  if (options?.addSuffix) {\n    if (options.comparison && options.comparison > 0) {\n      return \"في \" + result;\n    } else {\n      return \"عندو \" + result;\n    }\n  }\n\n  return result;\n};\n"], "mappings": "AAAA,MAAMA,oBAAoB,GAAG;EAC3BC,gBAAgB,EAAE;IAChBC,GAAG,EAAE,cAAc;IACnBC,GAAG,EAAE,kBAAkB;IACvBC,UAAU,EAAE,wBAAwB;IACpCC,KAAK,EAAE;EACT,CAAC;EAEDC,QAAQ,EAAE;IACRJ,GAAG,EAAE,OAAO;IACZC,GAAG,EAAE,WAAW;IAChBC,UAAU,EAAE,iBAAiB;IAC7BC,KAAK,EAAE;EACT,CAAC;EAEDE,WAAW,EAAE,UAAU;EAEvBC,gBAAgB,EAAE;IAChBN,GAAG,EAAE,cAAc;IACnBC,GAAG,EAAE,gBAAgB;IACrBC,UAAU,EAAE,wBAAwB;IACpCC,KAAK,EAAE;EACT,CAAC;EAEDI,QAAQ,EAAE;IACRP,GAAG,EAAE,OAAO;IACZC,GAAG,EAAE,SAAS;IACdC,UAAU,EAAE,iBAAiB;IAC7BC,KAAK,EAAE;EACT,CAAC;EAEDK,WAAW,EAAE;IACXR,GAAG,EAAE,YAAY;IACjBC,GAAG,EAAE,cAAc;IACnBC,UAAU,EAAE,uBAAuB;IACnCC,KAAK,EAAE;EACT,CAAC;EAEDM,MAAM,EAAE;IACNT,GAAG,EAAE,MAAM;IACXC,GAAG,EAAE,QAAQ;IACbC,UAAU,EAAE,iBAAiB;IAC7BC,KAAK,EAAE;EACT,CAAC;EAEDO,KAAK,EAAE;IACLV,GAAG,EAAE,MAAM;IACXC,GAAG,EAAE,QAAQ;IACbC,UAAU,EAAE,gBAAgB;IAC5BC,KAAK,EAAE;EACT,CAAC;EAEDQ,WAAW,EAAE;IACXX,GAAG,EAAE,YAAY;IACjBC,GAAG,EAAE,cAAc;IACnBC,UAAU,EAAE,sBAAsB;IAClCC,KAAK,EAAE;EACT,CAAC;EAEDS,MAAM,EAAE;IACNZ,GAAG,EAAE,MAAM;IACXC,GAAG,EAAE,QAAQ;IACbC,UAAU,EAAE,gBAAgB;IAC5BC,KAAK,EAAE;EACT,CAAC;EAEDU,YAAY,EAAE;IACZb,GAAG,EAAE,WAAW;IAChBC,GAAG,EAAE,aAAa;IAClBC,UAAU,EAAE,uBAAuB;IACnCC,KAAK,EAAE;EACT,CAAC;EAEDW,OAAO,EAAE;IACPd,GAAG,EAAE,KAAK;IACVC,GAAG,EAAE,OAAO;IACZC,UAAU,EAAE,iBAAiB;IAC7BC,KAAK,EAAE;EACT,CAAC;EAEDY,WAAW,EAAE;IACXf,GAAG,EAAE,WAAW;IAChBC,GAAG,EAAE,aAAa;IAClBC,UAAU,EAAE,uBAAuB;IACnCC,KAAK,EAAE;EACT,CAAC;EAEDa,MAAM,EAAE;IACNhB,GAAG,EAAE,KAAK;IACVC,GAAG,EAAE,OAAO;IACZC,UAAU,EAAE,iBAAiB;IAC7BC,KAAK,EAAE;EACT,CAAC;EAEDc,UAAU,EAAE;IACVjB,GAAG,EAAE,aAAa;IAClBC,GAAG,EAAE,eAAe;IACpBC,UAAU,EAAE,yBAAyB;IACrCC,KAAK,EAAE;EACT,CAAC;EAEDe,YAAY,EAAE;IACZlB,GAAG,EAAE,WAAW;IAChBC,GAAG,EAAE,aAAa;IAClBC,UAAU,EAAE,uBAAuB;IACnCC,KAAK,EAAE;EACT;AACF,CAAC;AAED,OAAO,MAAMgB,cAAc,GAAGA,CAACC,KAAK,EAAEC,KAAK,EAAEC,OAAO,KAAK;EACvD,MAAMC,UAAU,GAAGzB,oBAAoB,CAACsB,KAAK,CAAC;EAC9C,IAAII,MAAM;EACV,IAAI,OAAOD,UAAU,KAAK,QAAQ,EAAE;IAClCC,MAAM,GAAGD,UAAU;EACrB,CAAC,MAAM,IAAIF,KAAK,KAAK,CAAC,EAAE;IACtBG,MAAM,GAAGD,UAAU,CAACvB,GAAG;EACzB,CAAC,MAAM,IAAIqB,KAAK,KAAK,CAAC,EAAE;IACtBG,MAAM,GAAGD,UAAU,CAACtB,GAAG;EACzB,CAAC,MAAM,IAAIoB,KAAK,IAAI,EAAE,EAAE;IACtBG,MAAM,GAAGD,UAAU,CAACrB,UAAU,CAACuB,OAAO,CAAC,WAAW,EAAEC,MAAM,CAACL,KAAK,CAAC,CAAC;EACpE,CAAC,MAAM;IACLG,MAAM,GAAGD,UAAU,CAACpB,KAAK,CAACsB,OAAO,CAAC,WAAW,EAAEC,MAAM,CAACL,KAAK,CAAC,CAAC;EAC/D;EAEA,IAAIC,OAAO,EAAEK,SAAS,EAAE;IACtB,IAAIL,OAAO,CAACM,UAAU,IAAIN,OAAO,CAACM,UAAU,GAAG,CAAC,EAAE;MAChD,OAAO,KAAK,GAAGJ,MAAM;IACvB,CAAC,MAAM;MACL,OAAO,OAAO,GAAGA,MAAM;IACzB;EACF;EAEA,OAAOA,MAAM;AACf,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}