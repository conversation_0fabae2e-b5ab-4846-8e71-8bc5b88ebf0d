{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\PycharmProjects\\\\CMS\\\\webapp\\\\frontend\\\\src\\\\components\\\\cavi\\\\BobineFilterableTable.js\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect } from 'react';\nimport { Box, Typography, Chip, TableRow, TableCell, IconButton, Tooltip } from '@mui/material';\nimport { Edit as EditIcon, Delete as DeleteIcon, History as HistoryIcon, Add as AddIcon } from '@mui/icons-material';\nimport FilterableTable from '../common/FilterableTable';\nimport { REEL_STATES, getReelStateColor } from '../../utils/stateUtils';\n\n/**\n * Componente per visualizzare la lista delle bobine con filtri in stile Excel\n *\n * @param {Object} props - Proprietà del componente\n * @param {Array} props.bobine - Lista delle bobine da visualizzare\n * @param {boolean} props.loading - Indica se i dati sono in caricamento\n * @param {Function} props.onFilteredDataChange - Funzione chiamata quando i dati filtrati cambiano\n * @param {Function} props.onEdit - Funzione chiamata quando si vuole modificare una bobina\n * @param {Function} props.onDelete - Funzione chiamata quando si vuole eliminare una bobina\n * @param {Function} props.onViewHistory - Funzione chiamata quando si vuole visualizzare lo storico di una bobina\n * @param {Function} props.onQuickAdd - Funzione chiamata quando si vuole aggiungere rapidamente cavi a una bobina\n */\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst BobineFilterableTable = ({\n  bobine = [],\n  loading = false,\n  onFilteredDataChange = null,\n  onEdit = null,\n  onDelete = null,\n  onViewHistory = null,\n  onQuickAdd = null\n}) => {\n  _s();\n  const [filteredBobine, setFilteredBobine] = useState(bobine);\n\n  // Aggiorna i dati filtrati quando cambiano le bobine\n  useEffect(() => {\n    setFilteredBobine(bobine);\n  }, [bobine]);\n\n  // Notifica il componente padre quando cambiano i dati filtrati\n  const handleFilteredDataChange = data => {\n    setFilteredBobine(data);\n    if (onFilteredDataChange) {\n      onFilteredDataChange(data);\n    }\n  };\n\n  // Definizione delle colonne\n  const columns = [{\n    field: 'numero_bobina',\n    headerName: 'Numero',\n    dataType: 'text',\n    headerStyle: {\n      fontWeight: 'bold'\n    }\n  }, {\n    field: 'utility',\n    headerName: 'Utility',\n    dataType: 'text'\n  }, {\n    field: 'tipologia',\n    headerName: 'Tipologia',\n    dataType: 'text'\n  },\n  // n_conduttori field is now a spare field (kept in DB but hidden in UI)\n  {\n    field: 'sezione',\n    headerName: 'Formazione',\n    dataType: 'text',\n    align: 'right',\n    cellStyle: {\n      textAlign: 'right'\n    }\n  }, {\n    field: 'metri_totali',\n    headerName: 'Metri Totali',\n    dataType: 'number',\n    align: 'right',\n    cellStyle: {\n      textAlign: 'right'\n    },\n    renderCell: row => row.metri_totali ? row.metri_totali.toFixed(1) : '0'\n  }, {\n    field: 'metri_residui',\n    headerName: 'Metri Residui',\n    dataType: 'number',\n    align: 'right',\n    cellStyle: {\n      textAlign: 'right'\n    },\n    renderCell: row => row.metri_residui ? row.metri_residui.toFixed(1) : '0'\n  }, {\n    field: 'stato_bobina',\n    headerName: 'Stato',\n    dataType: 'text',\n    renderCell: row => {\n      return /*#__PURE__*/_jsxDEV(Chip, {\n        label: row.stato_bobina || 'N/D',\n        size: \"small\",\n        color: getReelStateColor(row.stato_bobina),\n        variant: \"outlined\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 91,\n        columnNumber: 11\n      }, this);\n    }\n  }, {\n    field: 'ubicazione_bobina',\n    headerName: 'Ubicazione',\n    dataType: 'text'\n  }, {\n    field: 'fornitore',\n    headerName: 'Fornitore',\n    dataType: 'text'\n  }, {\n    field: 'n_DDT',\n    headerName: 'N° DDT',\n    dataType: 'text'\n  }, {\n    field: 'data_DDT',\n    headerName: 'Data DDT',\n    dataType: 'text'\n  }, {\n    field: 'actions',\n    headerName: 'Azioni',\n    disableFilter: true,\n    disableSort: true,\n    align: 'center',\n    renderCell: row => /*#__PURE__*/_jsxDEV(Box, {\n      sx: {\n        display: 'flex',\n        justifyContent: 'center'\n      },\n      children: [onEdit && /*#__PURE__*/_jsxDEV(IconButton, {\n        size: \"small\",\n        onClick: () => onEdit(row),\n        title: \"Modifica bobina\",\n        color: \"primary\",\n        children: /*#__PURE__*/_jsxDEV(EditIcon, {\n          fontSize: \"small\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 135,\n          columnNumber: 15\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 129,\n        columnNumber: 13\n      }, this), onDelete && /*#__PURE__*/_jsxDEV(IconButton, {\n        size: \"small\",\n        onClick: () => onDelete(row),\n        title: \"Elimina bobina\",\n        color: \"error\",\n        disabled: row.stato_bobina !== 'Disponibile' || row.metri_residui !== row.metri_totali,\n        children: /*#__PURE__*/_jsxDEV(DeleteIcon, {\n          fontSize: \"small\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 146,\n          columnNumber: 15\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 139,\n        columnNumber: 13\n      }, this), onViewHistory && /*#__PURE__*/_jsxDEV(IconButton, {\n        size: \"small\",\n        onClick: () => onViewHistory(row),\n        title: \"Visualizza storico utilizzo\",\n        color: \"info\",\n        children: /*#__PURE__*/_jsxDEV(HistoryIcon, {\n          fontSize: \"small\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 156,\n          columnNumber: 15\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 150,\n        columnNumber: 13\n      }, this), onQuickAdd && row.stato_bobina !== 'Terminata' && row.stato_bobina !== 'Over' && /*#__PURE__*/_jsxDEV(Tooltip, {\n        title: \"Aggiungi cavi a questa bobina\",\n        children: /*#__PURE__*/_jsxDEV(IconButton, {\n          size: \"small\",\n          onClick: () => onQuickAdd(row),\n          color: \"success\",\n          children: /*#__PURE__*/_jsxDEV(AddIcon, {\n            fontSize: \"small\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 166,\n            columnNumber: 17\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 161,\n          columnNumber: 15\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 160,\n        columnNumber: 13\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 127,\n      columnNumber: 9\n    }, this)\n  }];\n\n  // Renderizza una riga personalizzata\n  const renderRow = (row, index) => {\n    // Determina il colore di sfondo in base allo stato\n    let bgColor = 'inherit';\n    if (row.stato_bobina === REEL_STATES.DISPONIBILE) bgColor = 'rgba(76, 175, 80, 0.1)';else if (row.stato_bobina === REEL_STATES.IN_USO) bgColor = 'rgba(255, 152, 0, 0.1)';else if (row.stato_bobina === REEL_STATES.TERMINATA) bgColor = 'rgba(255, 152, 0, 0.2)';else if (row.stato_bobina === REEL_STATES.OVER) bgColor = 'rgba(244, 67, 54, 0.1)';\n    return /*#__PURE__*/_jsxDEV(TableRow, {\n      sx: {\n        backgroundColor: bgColor,\n        '&:hover': {\n          backgroundColor: 'rgba(0, 0, 0, 0.04)'\n        }\n      },\n      children: columns.map(column => /*#__PURE__*/_jsxDEV(TableCell, {\n        align: column.align || 'left',\n        sx: column.cellStyle,\n        children: column.renderCell ? column.renderCell(row) : row[column.field]\n      }, column.field, false, {\n        fileName: _jsxFileName,\n        lineNumber: 193,\n        columnNumber: 11\n      }, this))\n    }, index, false, {\n      fileName: _jsxFileName,\n      lineNumber: 185,\n      columnNumber: 7\n    }, this);\n  };\n\n  // Calcola le statistiche\n  const calculateStats = () => {\n    if (!filteredBobine.length) return null;\n    const totalBobine = filteredBobine.length;\n    const disponibili = filteredBobine.filter(b => b.stato_bobina === REEL_STATES.DISPONIBILE).length;\n    const inUso = filteredBobine.filter(b => b.stato_bobina === REEL_STATES.IN_USO).length;\n    const terminate = filteredBobine.filter(b => b.stato_bobina === REEL_STATES.TERMINATA).length;\n    const over = filteredBobine.filter(b => b.stato_bobina === REEL_STATES.OVER).length;\n    const metriTotali = filteredBobine.reduce((sum, b) => sum + (b.metri_totali || 0), 0);\n    const metriResidui = filteredBobine.reduce((sum, b) => sum + (b.metri_residui || 0), 0);\n    const metriUtilizzati = metriTotali - metriResidui;\n    const percentualeUtilizzo = metriTotali ? Math.round(metriUtilizzati / metriTotali * 100) : 0;\n    return {\n      totalBobine,\n      disponibili,\n      inUso,\n      terminate,\n      over,\n      metriTotali,\n      metriResidui,\n      metriUtilizzati,\n      percentualeUtilizzo\n    };\n  };\n  const stats = calculateStats();\n  return /*#__PURE__*/_jsxDEV(Box, {\n    children: [stats && /*#__PURE__*/_jsxDEV(Box, {\n      sx: {\n        mb: 3,\n        p: 2,\n        bgcolor: 'background.paper',\n        borderRadius: 1,\n        boxShadow: 1\n      },\n      children: [/*#__PURE__*/_jsxDEV(Typography, {\n        variant: \"subtitle1\",\n        gutterBottom: true,\n        children: [\"Statistiche (\", filteredBobine.length, \" bobine visualizzate)\"]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 240,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(Box, {\n        sx: {\n          display: 'flex',\n          flexWrap: 'wrap',\n          gap: 3\n        },\n        children: [/*#__PURE__*/_jsxDEV(Box, {\n          children: [/*#__PURE__*/_jsxDEV(Typography, {\n            variant: \"body2\",\n            color: \"text.secondary\",\n            children: \"Utilizzo\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 245,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(Typography, {\n            variant: \"h6\",\n            children: [stats.percentualeUtilizzo, \"%\"]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 246,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 244,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(Box, {\n          children: [/*#__PURE__*/_jsxDEV(Typography, {\n            variant: \"body2\",\n            color: \"text.secondary\",\n            children: \"Disponibili\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 249,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(Typography, {\n            variant: \"h6\",\n            color: \"success.main\",\n            children: stats.disponibili\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 250,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 248,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(Box, {\n          children: [/*#__PURE__*/_jsxDEV(Typography, {\n            variant: \"body2\",\n            color: \"text.secondary\",\n            children: \"In uso\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 253,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(Typography, {\n            variant: \"h6\",\n            color: \"warning.main\",\n            children: stats.inUso\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 254,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 252,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(Box, {\n          children: [/*#__PURE__*/_jsxDEV(Typography, {\n            variant: \"body2\",\n            color: \"text.secondary\",\n            children: \"Terminate\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 257,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(Typography, {\n            variant: \"h6\",\n            color: \"warning.main\",\n            children: stats.terminate\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 258,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 256,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(Box, {\n          children: [/*#__PURE__*/_jsxDEV(Typography, {\n            variant: \"body2\",\n            color: \"text.secondary\",\n            children: \"Over\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 261,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(Typography, {\n            variant: \"h6\",\n            color: \"error.main\",\n            children: stats.over\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 262,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 260,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(Box, {\n          children: [/*#__PURE__*/_jsxDEV(Typography, {\n            variant: \"body2\",\n            color: \"text.secondary\",\n            children: \"Metri totali\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 265,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(Typography, {\n            variant: \"h6\",\n            children: [stats.metriTotali.toFixed(1), \" m\"]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 266,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 264,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(Box, {\n          children: [/*#__PURE__*/_jsxDEV(Typography, {\n            variant: \"body2\",\n            color: \"text.secondary\",\n            children: \"Metri residui\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 269,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(Typography, {\n            variant: \"h6\",\n            children: [stats.metriResidui.toFixed(1), \" m\"]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 270,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 268,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(Box, {\n          children: [/*#__PURE__*/_jsxDEV(Typography, {\n            variant: \"body2\",\n            color: \"text.secondary\",\n            children: \"Metri utilizzati\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 273,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(Typography, {\n            variant: \"h6\",\n            children: [stats.metriUtilizzati.toFixed(1), \" m\"]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 274,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 272,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 243,\n        columnNumber: 11\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 239,\n      columnNumber: 9\n    }, this), /*#__PURE__*/_jsxDEV(FilterableTable, {\n      data: bobine,\n      columns: columns,\n      onFilteredDataChange: handleFilteredDataChange,\n      loading: loading,\n      emptyMessage: \"Nessuna bobina disponibile\",\n      renderRow: renderRow\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 280,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 237,\n    columnNumber: 5\n  }, this);\n};\n_s(BobineFilterableTable, \"f3TlITpreTFylm3or0YT9wPW5MI=\");\n_c = BobineFilterableTable;\nexport default BobineFilterableTable;\nvar _c;\n$RefreshReg$(_c, \"BobineFilterableTable\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "Box", "Typography", "Chip", "TableRow", "TableCell", "IconButton", "<PERSON><PERSON><PERSON>", "Edit", "EditIcon", "Delete", "DeleteIcon", "History", "HistoryIcon", "Add", "AddIcon", "FilterableTable", "REEL_STATES", "getReelStateColor", "jsxDEV", "_jsxDEV", "BobineFilterableTable", "bobine", "loading", "onFilteredDataChange", "onEdit", "onDelete", "onViewHistory", "onQuickAdd", "_s", "filteredBobine", "setFilteredBobine", "handleFilteredDataChange", "data", "columns", "field", "headerName", "dataType", "headerStyle", "fontWeight", "align", "cellStyle", "textAlign", "renderCell", "row", "metri_totali", "toFixed", "metri_residui", "label", "stato_bobina", "size", "color", "variant", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "disableFilter", "disableSort", "sx", "display", "justifyContent", "children", "onClick", "title", "fontSize", "disabled", "renderRow", "index", "bgColor", "DISPONIBILE", "IN_USO", "TERMINATA", "OVER", "backgroundColor", "map", "column", "calculateStats", "length", "totalBobine", "disponibili", "filter", "b", "inUso", "terminate", "over", "metriTotali", "reduce", "sum", "metriResidui", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "percentuale<PERSON><PERSON><PERSON><PERSON>", "Math", "round", "stats", "mb", "p", "bgcolor", "borderRadius", "boxShadow", "gutterBottom", "flexWrap", "gap", "emptyMessage", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/PycharmProjects/CMS/webapp/frontend/src/components/cavi/BobineFilterableTable.js"], "sourcesContent": ["import React, { useState, useEffect } from 'react';\nimport { Box, Typography, Chip, TableRow, TableCell, IconButton, Tooltip } from '@mui/material';\nimport { Edit as EditIcon, Delete as DeleteIcon, History as HistoryIcon, Add as AddIcon } from '@mui/icons-material';\nimport FilterableTable from '../common/FilterableTable';\nimport { REEL_STATES, getReelStateColor } from '../../utils/stateUtils';\n\n/**\n * Componente per visualizzare la lista delle bobine con filtri in stile Excel\n *\n * @param {Object} props - Proprietà del componente\n * @param {Array} props.bobine - Lista delle bobine da visualizzare\n * @param {boolean} props.loading - Indica se i dati sono in caricamento\n * @param {Function} props.onFilteredDataChange - Funzione chiamata quando i dati filtrati cambiano\n * @param {Function} props.onEdit - Funzione chiamata quando si vuole modificare una bobina\n * @param {Function} props.onDelete - Funzione chiamata quando si vuole eliminare una bobina\n * @param {Function} props.onViewHistory - Funzione chiamata quando si vuole visualizzare lo storico di una bobina\n * @param {Function} props.onQuickAdd - Funzione chiamata quando si vuole aggiungere rapidamente cavi a una bobina\n */\nconst BobineFilterableTable = ({\n  bobine = [],\n  loading = false,\n  onFilteredDataChange = null,\n  onEdit = null,\n  onDelete = null,\n  onViewHistory = null,\n  onQuickAdd = null\n}) => {\n  const [filteredBobine, setFilteredBobine] = useState(bobine);\n\n  // Aggiorna i dati filtrati quando cambiano le bobine\n  useEffect(() => {\n    setFilteredBobine(bobine);\n  }, [bobine]);\n\n  // Notifica il componente padre quando cambiano i dati filtrati\n  const handleFilteredDataChange = (data) => {\n    setFilteredBobine(data);\n    if (onFilteredDataChange) {\n      onFilteredDataChange(data);\n    }\n  };\n\n  // Definizione delle colonne\n  const columns = [\n    {\n      field: 'numero_bobina',\n      headerName: 'Numero',\n      dataType: 'text',\n      headerStyle: { fontWeight: 'bold' }\n    },\n    {\n      field: 'utility',\n      headerName: 'Utility',\n      dataType: 'text'\n    },\n    {\n      field: 'tipologia',\n      headerName: 'Tipologia',\n      dataType: 'text'\n    },\n    // n_conduttori field is now a spare field (kept in DB but hidden in UI)\n    {\n      field: 'sezione',\n      headerName: 'Formazione',\n      dataType: 'text',\n      align: 'right',\n      cellStyle: { textAlign: 'right' }\n    },\n    {\n      field: 'metri_totali',\n      headerName: 'Metri Totali',\n      dataType: 'number',\n      align: 'right',\n      cellStyle: { textAlign: 'right' },\n      renderCell: (row) => row.metri_totali ? row.metri_totali.toFixed(1) : '0'\n    },\n    {\n      field: 'metri_residui',\n      headerName: 'Metri Residui',\n      dataType: 'number',\n      align: 'right',\n      cellStyle: { textAlign: 'right' },\n      renderCell: (row) => row.metri_residui ? row.metri_residui.toFixed(1) : '0'\n    },\n    {\n      field: 'stato_bobina',\n      headerName: 'Stato',\n      dataType: 'text',\n      renderCell: (row) => {\n        return (\n          <Chip\n            label={row.stato_bobina || 'N/D'}\n            size=\"small\"\n            color={getReelStateColor(row.stato_bobina)}\n            variant=\"outlined\"\n          />\n        );\n      }\n    },\n    {\n      field: 'ubicazione_bobina',\n      headerName: 'Ubicazione',\n      dataType: 'text'\n    },\n    {\n      field: 'fornitore',\n      headerName: 'Fornitore',\n      dataType: 'text'\n    },\n    {\n      field: 'n_DDT',\n      headerName: 'N° DDT',\n      dataType: 'text'\n    },\n    {\n      field: 'data_DDT',\n      headerName: 'Data DDT',\n      dataType: 'text'\n    },\n    {\n      field: 'actions',\n      headerName: 'Azioni',\n      disableFilter: true,\n      disableSort: true,\n      align: 'center',\n      renderCell: (row) => (\n        <Box sx={{ display: 'flex', justifyContent: 'center' }}>\n          {onEdit && (\n            <IconButton\n              size=\"small\"\n              onClick={() => onEdit(row)}\n              title=\"Modifica bobina\"\n              color=\"primary\"\n            >\n              <EditIcon fontSize=\"small\" />\n            </IconButton>\n          )}\n          {onDelete && (\n            <IconButton\n              size=\"small\"\n              onClick={() => onDelete(row)}\n              title=\"Elimina bobina\"\n              color=\"error\"\n              disabled={row.stato_bobina !== 'Disponibile' || row.metri_residui !== row.metri_totali}\n            >\n              <DeleteIcon fontSize=\"small\" />\n            </IconButton>\n          )}\n          {onViewHistory && (\n            <IconButton\n              size=\"small\"\n              onClick={() => onViewHistory(row)}\n              title=\"Visualizza storico utilizzo\"\n              color=\"info\"\n            >\n              <HistoryIcon fontSize=\"small\" />\n            </IconButton>\n          )}\n          {onQuickAdd && row.stato_bobina !== 'Terminata' && row.stato_bobina !== 'Over' && (\n            <Tooltip title=\"Aggiungi cavi a questa bobina\">\n              <IconButton\n                size=\"small\"\n                onClick={() => onQuickAdd(row)}\n                color=\"success\"\n              >\n                <AddIcon fontSize=\"small\" />\n              </IconButton>\n            </Tooltip>\n          )}\n        </Box>\n      )\n    }\n  ];\n\n  // Renderizza una riga personalizzata\n  const renderRow = (row, index) => {\n    // Determina il colore di sfondo in base allo stato\n    let bgColor = 'inherit';\n    if (row.stato_bobina === REEL_STATES.DISPONIBILE) bgColor = 'rgba(76, 175, 80, 0.1)';\n    else if (row.stato_bobina === REEL_STATES.IN_USO) bgColor = 'rgba(255, 152, 0, 0.1)';\n    else if (row.stato_bobina === REEL_STATES.TERMINATA) bgColor = 'rgba(255, 152, 0, 0.2)';\n    else if (row.stato_bobina === REEL_STATES.OVER) bgColor = 'rgba(244, 67, 54, 0.1)';\n\n    return (\n      <TableRow\n        key={index}\n        sx={{\n          backgroundColor: bgColor,\n          '&:hover': { backgroundColor: 'rgba(0, 0, 0, 0.04)' }\n        }}\n      >\n        {columns.map((column) => (\n          <TableCell\n            key={column.field}\n            align={column.align || 'left'}\n            sx={column.cellStyle}\n          >\n            {column.renderCell ? column.renderCell(row) : row[column.field]}\n          </TableCell>\n        ))}\n      </TableRow>\n    );\n  };\n\n  // Calcola le statistiche\n  const calculateStats = () => {\n    if (!filteredBobine.length) return null;\n\n    const totalBobine = filteredBobine.length;\n    const disponibili = filteredBobine.filter(b => b.stato_bobina === REEL_STATES.DISPONIBILE).length;\n    const inUso = filteredBobine.filter(b => b.stato_bobina === REEL_STATES.IN_USO).length;\n    const terminate = filteredBobine.filter(b => b.stato_bobina === REEL_STATES.TERMINATA).length;\n    const over = filteredBobine.filter(b => b.stato_bobina === REEL_STATES.OVER).length;\n\n    const metriTotali = filteredBobine.reduce((sum, b) => sum + (b.metri_totali || 0), 0);\n    const metriResidui = filteredBobine.reduce((sum, b) => sum + (b.metri_residui || 0), 0);\n    const metriUtilizzati = metriTotali - metriResidui;\n\n    const percentualeUtilizzo = metriTotali ? Math.round((metriUtilizzati / metriTotali) * 100) : 0;\n\n    return {\n      totalBobine,\n      disponibili,\n      inUso,\n      terminate,\n      over,\n      metriTotali,\n      metriResidui,\n      metriUtilizzati,\n      percentualeUtilizzo\n    };\n  };\n\n  const stats = calculateStats();\n\n  return (\n    <Box>\n      {stats && (\n        <Box sx={{ mb: 3, p: 2, bgcolor: 'background.paper', borderRadius: 1, boxShadow: 1 }}>\n          <Typography variant=\"subtitle1\" gutterBottom>\n            Statistiche ({filteredBobine.length} bobine visualizzate)\n          </Typography>\n          <Box sx={{ display: 'flex', flexWrap: 'wrap', gap: 3 }}>\n            <Box>\n              <Typography variant=\"body2\" color=\"text.secondary\">Utilizzo</Typography>\n              <Typography variant=\"h6\">{stats.percentualeUtilizzo}%</Typography>\n            </Box>\n            <Box>\n              <Typography variant=\"body2\" color=\"text.secondary\">Disponibili</Typography>\n              <Typography variant=\"h6\" color=\"success.main\">{stats.disponibili}</Typography>\n            </Box>\n            <Box>\n              <Typography variant=\"body2\" color=\"text.secondary\">In uso</Typography>\n              <Typography variant=\"h6\" color=\"warning.main\">{stats.inUso}</Typography>\n            </Box>\n            <Box>\n              <Typography variant=\"body2\" color=\"text.secondary\">Terminate</Typography>\n              <Typography variant=\"h6\" color=\"warning.main\">{stats.terminate}</Typography>\n            </Box>\n            <Box>\n              <Typography variant=\"body2\" color=\"text.secondary\">Over</Typography>\n              <Typography variant=\"h6\" color=\"error.main\">{stats.over}</Typography>\n            </Box>\n            <Box>\n              <Typography variant=\"body2\" color=\"text.secondary\">Metri totali</Typography>\n              <Typography variant=\"h6\">{stats.metriTotali.toFixed(1)} m</Typography>\n            </Box>\n            <Box>\n              <Typography variant=\"body2\" color=\"text.secondary\">Metri residui</Typography>\n              <Typography variant=\"h6\">{stats.metriResidui.toFixed(1)} m</Typography>\n            </Box>\n            <Box>\n              <Typography variant=\"body2\" color=\"text.secondary\">Metri utilizzati</Typography>\n              <Typography variant=\"h6\">{stats.metriUtilizzati.toFixed(1)} m</Typography>\n            </Box>\n          </Box>\n        </Box>\n      )}\n\n      <FilterableTable\n        data={bobine}\n        columns={columns}\n        onFilteredDataChange={handleFilteredDataChange}\n        loading={loading}\n        emptyMessage=\"Nessuna bobina disponibile\"\n        renderRow={renderRow}\n      />\n    </Box>\n  );\n};\n\nexport default BobineFilterableTable;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAClD,SAASC,GAAG,EAAEC,UAAU,EAAEC,IAAI,EAAEC,QAAQ,EAAEC,SAAS,EAAEC,UAAU,EAAEC,OAAO,QAAQ,eAAe;AAC/F,SAASC,IAAI,IAAIC,QAAQ,EAAEC,MAAM,IAAIC,UAAU,EAAEC,OAAO,IAAIC,WAAW,EAAEC,GAAG,IAAIC,OAAO,QAAQ,qBAAqB;AACpH,OAAOC,eAAe,MAAM,2BAA2B;AACvD,SAASC,WAAW,EAAEC,iBAAiB,QAAQ,wBAAwB;;AAEvE;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAXA,SAAAC,MAAA,IAAAC,OAAA;AAYA,MAAMC,qBAAqB,GAAGA,CAAC;EAC7BC,MAAM,GAAG,EAAE;EACXC,OAAO,GAAG,KAAK;EACfC,oBAAoB,GAAG,IAAI;EAC3BC,MAAM,GAAG,IAAI;EACbC,QAAQ,GAAG,IAAI;EACfC,aAAa,GAAG,IAAI;EACpBC,UAAU,GAAG;AACf,CAAC,KAAK;EAAAC,EAAA;EACJ,MAAM,CAACC,cAAc,EAAEC,iBAAiB,CAAC,GAAGhC,QAAQ,CAACuB,MAAM,CAAC;;EAE5D;EACAtB,SAAS,CAAC,MAAM;IACd+B,iBAAiB,CAACT,MAAM,CAAC;EAC3B,CAAC,EAAE,CAACA,MAAM,CAAC,CAAC;;EAEZ;EACA,MAAMU,wBAAwB,GAAIC,IAAI,IAAK;IACzCF,iBAAiB,CAACE,IAAI,CAAC;IACvB,IAAIT,oBAAoB,EAAE;MACxBA,oBAAoB,CAACS,IAAI,CAAC;IAC5B;EACF,CAAC;;EAED;EACA,MAAMC,OAAO,GAAG,CACd;IACEC,KAAK,EAAE,eAAe;IACtBC,UAAU,EAAE,QAAQ;IACpBC,QAAQ,EAAE,MAAM;IAChBC,WAAW,EAAE;MAAEC,UAAU,EAAE;IAAO;EACpC,CAAC,EACD;IACEJ,KAAK,EAAE,SAAS;IAChBC,UAAU,EAAE,SAAS;IACrBC,QAAQ,EAAE;EACZ,CAAC,EACD;IACEF,KAAK,EAAE,WAAW;IAClBC,UAAU,EAAE,WAAW;IACvBC,QAAQ,EAAE;EACZ,CAAC;EACD;EACA;IACEF,KAAK,EAAE,SAAS;IAChBC,UAAU,EAAE,YAAY;IACxBC,QAAQ,EAAE,MAAM;IAChBG,KAAK,EAAE,OAAO;IACdC,SAAS,EAAE;MAAEC,SAAS,EAAE;IAAQ;EAClC,CAAC,EACD;IACEP,KAAK,EAAE,cAAc;IACrBC,UAAU,EAAE,cAAc;IAC1BC,QAAQ,EAAE,QAAQ;IAClBG,KAAK,EAAE,OAAO;IACdC,SAAS,EAAE;MAAEC,SAAS,EAAE;IAAQ,CAAC;IACjCC,UAAU,EAAGC,GAAG,IAAKA,GAAG,CAACC,YAAY,GAAGD,GAAG,CAACC,YAAY,CAACC,OAAO,CAAC,CAAC,CAAC,GAAG;EACxE,CAAC,EACD;IACEX,KAAK,EAAE,eAAe;IACtBC,UAAU,EAAE,eAAe;IAC3BC,QAAQ,EAAE,QAAQ;IAClBG,KAAK,EAAE,OAAO;IACdC,SAAS,EAAE;MAAEC,SAAS,EAAE;IAAQ,CAAC;IACjCC,UAAU,EAAGC,GAAG,IAAKA,GAAG,CAACG,aAAa,GAAGH,GAAG,CAACG,aAAa,CAACD,OAAO,CAAC,CAAC,CAAC,GAAG;EAC1E,CAAC,EACD;IACEX,KAAK,EAAE,cAAc;IACrBC,UAAU,EAAE,OAAO;IACnBC,QAAQ,EAAE,MAAM;IAChBM,UAAU,EAAGC,GAAG,IAAK;MACnB,oBACExB,OAAA,CAACjB,IAAI;QACH6C,KAAK,EAAEJ,GAAG,CAACK,YAAY,IAAI,KAAM;QACjCC,IAAI,EAAC,OAAO;QACZC,KAAK,EAAEjC,iBAAiB,CAAC0B,GAAG,CAACK,YAAY,CAAE;QAC3CG,OAAO,EAAC;MAAU;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACnB,CAAC;IAEN;EACF,CAAC,EACD;IACErB,KAAK,EAAE,mBAAmB;IAC1BC,UAAU,EAAE,YAAY;IACxBC,QAAQ,EAAE;EACZ,CAAC,EACD;IACEF,KAAK,EAAE,WAAW;IAClBC,UAAU,EAAE,WAAW;IACvBC,QAAQ,EAAE;EACZ,CAAC,EACD;IACEF,KAAK,EAAE,OAAO;IACdC,UAAU,EAAE,QAAQ;IACpBC,QAAQ,EAAE;EACZ,CAAC,EACD;IACEF,KAAK,EAAE,UAAU;IACjBC,UAAU,EAAE,UAAU;IACtBC,QAAQ,EAAE;EACZ,CAAC,EACD;IACEF,KAAK,EAAE,SAAS;IAChBC,UAAU,EAAE,QAAQ;IACpBqB,aAAa,EAAE,IAAI;IACnBC,WAAW,EAAE,IAAI;IACjBlB,KAAK,EAAE,QAAQ;IACfG,UAAU,EAAGC,GAAG,iBACdxB,OAAA,CAACnB,GAAG;MAAC0D,EAAE,EAAE;QAAEC,OAAO,EAAE,MAAM;QAAEC,cAAc,EAAE;MAAS,CAAE;MAAAC,QAAA,GACpDrC,MAAM,iBACLL,OAAA,CAACd,UAAU;QACT4C,IAAI,EAAC,OAAO;QACZa,OAAO,EAAEA,CAAA,KAAMtC,MAAM,CAACmB,GAAG,CAAE;QAC3BoB,KAAK,EAAC,iBAAiB;QACvBb,KAAK,EAAC,SAAS;QAAAW,QAAA,eAEf1C,OAAA,CAACX,QAAQ;UAACwD,QAAQ,EAAC;QAAO;UAAAZ,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACnB,CACb,EACA9B,QAAQ,iBACPN,OAAA,CAACd,UAAU;QACT4C,IAAI,EAAC,OAAO;QACZa,OAAO,EAAEA,CAAA,KAAMrC,QAAQ,CAACkB,GAAG,CAAE;QAC7BoB,KAAK,EAAC,gBAAgB;QACtBb,KAAK,EAAC,OAAO;QACbe,QAAQ,EAAEtB,GAAG,CAACK,YAAY,KAAK,aAAa,IAAIL,GAAG,CAACG,aAAa,KAAKH,GAAG,CAACC,YAAa;QAAAiB,QAAA,eAEvF1C,OAAA,CAACT,UAAU;UAACsD,QAAQ,EAAC;QAAO;UAAAZ,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACrB,CACb,EACA7B,aAAa,iBACZP,OAAA,CAACd,UAAU;QACT4C,IAAI,EAAC,OAAO;QACZa,OAAO,EAAEA,CAAA,KAAMpC,aAAa,CAACiB,GAAG,CAAE;QAClCoB,KAAK,EAAC,6BAA6B;QACnCb,KAAK,EAAC,MAAM;QAAAW,QAAA,eAEZ1C,OAAA,CAACP,WAAW;UAACoD,QAAQ,EAAC;QAAO;UAAAZ,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACtB,CACb,EACA5B,UAAU,IAAIgB,GAAG,CAACK,YAAY,KAAK,WAAW,IAAIL,GAAG,CAACK,YAAY,KAAK,MAAM,iBAC5E7B,OAAA,CAACb,OAAO;QAACyD,KAAK,EAAC,+BAA+B;QAAAF,QAAA,eAC5C1C,OAAA,CAACd,UAAU;UACT4C,IAAI,EAAC,OAAO;UACZa,OAAO,EAAEA,CAAA,KAAMnC,UAAU,CAACgB,GAAG,CAAE;UAC/BO,KAAK,EAAC,SAAS;UAAAW,QAAA,eAEf1C,OAAA,CAACL,OAAO;YAACkD,QAAQ,EAAC;UAAO;YAAAZ,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAClB;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACN,CACV;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACE;EAET,CAAC,CACF;;EAED;EACA,MAAMW,SAAS,GAAGA,CAACvB,GAAG,EAAEwB,KAAK,KAAK;IAChC;IACA,IAAIC,OAAO,GAAG,SAAS;IACvB,IAAIzB,GAAG,CAACK,YAAY,KAAKhC,WAAW,CAACqD,WAAW,EAAED,OAAO,GAAG,wBAAwB,CAAC,KAChF,IAAIzB,GAAG,CAACK,YAAY,KAAKhC,WAAW,CAACsD,MAAM,EAAEF,OAAO,GAAG,wBAAwB,CAAC,KAChF,IAAIzB,GAAG,CAACK,YAAY,KAAKhC,WAAW,CAACuD,SAAS,EAAEH,OAAO,GAAG,wBAAwB,CAAC,KACnF,IAAIzB,GAAG,CAACK,YAAY,KAAKhC,WAAW,CAACwD,IAAI,EAAEJ,OAAO,GAAG,wBAAwB;IAElF,oBACEjD,OAAA,CAAChB,QAAQ;MAEPuD,EAAE,EAAE;QACFe,eAAe,EAAEL,OAAO;QACxB,SAAS,EAAE;UAAEK,eAAe,EAAE;QAAsB;MACtD,CAAE;MAAAZ,QAAA,EAED5B,OAAO,CAACyC,GAAG,CAAEC,MAAM,iBAClBxD,OAAA,CAACf,SAAS;QAERmC,KAAK,EAAEoC,MAAM,CAACpC,KAAK,IAAI,MAAO;QAC9BmB,EAAE,EAAEiB,MAAM,CAACnC,SAAU;QAAAqB,QAAA,EAEpBc,MAAM,CAACjC,UAAU,GAAGiC,MAAM,CAACjC,UAAU,CAACC,GAAG,CAAC,GAAGA,GAAG,CAACgC,MAAM,CAACzC,KAAK;MAAC,GAJ1DyC,MAAM,CAACzC,KAAK;QAAAkB,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAKR,CACZ;IAAC,GAdGY,KAAK;MAAAf,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAeF,CAAC;EAEf,CAAC;;EAED;EACA,MAAMqB,cAAc,GAAGA,CAAA,KAAM;IAC3B,IAAI,CAAC/C,cAAc,CAACgD,MAAM,EAAE,OAAO,IAAI;IAEvC,MAAMC,WAAW,GAAGjD,cAAc,CAACgD,MAAM;IACzC,MAAME,WAAW,GAAGlD,cAAc,CAACmD,MAAM,CAACC,CAAC,IAAIA,CAAC,CAACjC,YAAY,KAAKhC,WAAW,CAACqD,WAAW,CAAC,CAACQ,MAAM;IACjG,MAAMK,KAAK,GAAGrD,cAAc,CAACmD,MAAM,CAACC,CAAC,IAAIA,CAAC,CAACjC,YAAY,KAAKhC,WAAW,CAACsD,MAAM,CAAC,CAACO,MAAM;IACtF,MAAMM,SAAS,GAAGtD,cAAc,CAACmD,MAAM,CAACC,CAAC,IAAIA,CAAC,CAACjC,YAAY,KAAKhC,WAAW,CAACuD,SAAS,CAAC,CAACM,MAAM;IAC7F,MAAMO,IAAI,GAAGvD,cAAc,CAACmD,MAAM,CAACC,CAAC,IAAIA,CAAC,CAACjC,YAAY,KAAKhC,WAAW,CAACwD,IAAI,CAAC,CAACK,MAAM;IAEnF,MAAMQ,WAAW,GAAGxD,cAAc,CAACyD,MAAM,CAAC,CAACC,GAAG,EAAEN,CAAC,KAAKM,GAAG,IAAIN,CAAC,CAACrC,YAAY,IAAI,CAAC,CAAC,EAAE,CAAC,CAAC;IACrF,MAAM4C,YAAY,GAAG3D,cAAc,CAACyD,MAAM,CAAC,CAACC,GAAG,EAAEN,CAAC,KAAKM,GAAG,IAAIN,CAAC,CAACnC,aAAa,IAAI,CAAC,CAAC,EAAE,CAAC,CAAC;IACvF,MAAM2C,eAAe,GAAGJ,WAAW,GAAGG,YAAY;IAElD,MAAME,mBAAmB,GAAGL,WAAW,GAAGM,IAAI,CAACC,KAAK,CAAEH,eAAe,GAAGJ,WAAW,GAAI,GAAG,CAAC,GAAG,CAAC;IAE/F,OAAO;MACLP,WAAW;MACXC,WAAW;MACXG,KAAK;MACLC,SAAS;MACTC,IAAI;MACJC,WAAW;MACXG,YAAY;MACZC,eAAe;MACfC;IACF,CAAC;EACH,CAAC;EAED,MAAMG,KAAK,GAAGjB,cAAc,CAAC,CAAC;EAE9B,oBACEzD,OAAA,CAACnB,GAAG;IAAA6D,QAAA,GACDgC,KAAK,iBACJ1E,OAAA,CAACnB,GAAG;MAAC0D,EAAE,EAAE;QAAEoC,EAAE,EAAE,CAAC;QAAEC,CAAC,EAAE,CAAC;QAAEC,OAAO,EAAE,kBAAkB;QAAEC,YAAY,EAAE,CAAC;QAAEC,SAAS,EAAE;MAAE,CAAE;MAAArC,QAAA,gBACnF1C,OAAA,CAAClB,UAAU;QAACkD,OAAO,EAAC,WAAW;QAACgD,YAAY;QAAAtC,QAAA,GAAC,eAC9B,EAAChC,cAAc,CAACgD,MAAM,EAAC,uBACtC;MAAA;QAAAzB,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAY,CAAC,eACbpC,OAAA,CAACnB,GAAG;QAAC0D,EAAE,EAAE;UAAEC,OAAO,EAAE,MAAM;UAAEyC,QAAQ,EAAE,MAAM;UAAEC,GAAG,EAAE;QAAE,CAAE;QAAAxC,QAAA,gBACrD1C,OAAA,CAACnB,GAAG;UAAA6D,QAAA,gBACF1C,OAAA,CAAClB,UAAU;YAACkD,OAAO,EAAC,OAAO;YAACD,KAAK,EAAC,gBAAgB;YAAAW,QAAA,EAAC;UAAQ;YAAAT,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAY,CAAC,eACxEpC,OAAA,CAAClB,UAAU;YAACkD,OAAO,EAAC,IAAI;YAAAU,QAAA,GAAEgC,KAAK,CAACH,mBAAmB,EAAC,GAAC;UAAA;YAAAtC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAY,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC/D,CAAC,eACNpC,OAAA,CAACnB,GAAG;UAAA6D,QAAA,gBACF1C,OAAA,CAAClB,UAAU;YAACkD,OAAO,EAAC,OAAO;YAACD,KAAK,EAAC,gBAAgB;YAAAW,QAAA,EAAC;UAAW;YAAAT,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAY,CAAC,eAC3EpC,OAAA,CAAClB,UAAU;YAACkD,OAAO,EAAC,IAAI;YAACD,KAAK,EAAC,cAAc;YAAAW,QAAA,EAAEgC,KAAK,CAACd;UAAW;YAAA3B,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAa,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC3E,CAAC,eACNpC,OAAA,CAACnB,GAAG;UAAA6D,QAAA,gBACF1C,OAAA,CAAClB,UAAU;YAACkD,OAAO,EAAC,OAAO;YAACD,KAAK,EAAC,gBAAgB;YAAAW,QAAA,EAAC;UAAM;YAAAT,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAY,CAAC,eACtEpC,OAAA,CAAClB,UAAU;YAACkD,OAAO,EAAC,IAAI;YAACD,KAAK,EAAC,cAAc;YAAAW,QAAA,EAAEgC,KAAK,CAACX;UAAK;YAAA9B,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAa,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACrE,CAAC,eACNpC,OAAA,CAACnB,GAAG;UAAA6D,QAAA,gBACF1C,OAAA,CAAClB,UAAU;YAACkD,OAAO,EAAC,OAAO;YAACD,KAAK,EAAC,gBAAgB;YAAAW,QAAA,EAAC;UAAS;YAAAT,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAY,CAAC,eACzEpC,OAAA,CAAClB,UAAU;YAACkD,OAAO,EAAC,IAAI;YAACD,KAAK,EAAC,cAAc;YAAAW,QAAA,EAAEgC,KAAK,CAACV;UAAS;YAAA/B,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAa,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACzE,CAAC,eACNpC,OAAA,CAACnB,GAAG;UAAA6D,QAAA,gBACF1C,OAAA,CAAClB,UAAU;YAACkD,OAAO,EAAC,OAAO;YAACD,KAAK,EAAC,gBAAgB;YAAAW,QAAA,EAAC;UAAI;YAAAT,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAY,CAAC,eACpEpC,OAAA,CAAClB,UAAU;YAACkD,OAAO,EAAC,IAAI;YAACD,KAAK,EAAC,YAAY;YAAAW,QAAA,EAAEgC,KAAK,CAACT;UAAI;YAAAhC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAa,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAClE,CAAC,eACNpC,OAAA,CAACnB,GAAG;UAAA6D,QAAA,gBACF1C,OAAA,CAAClB,UAAU;YAACkD,OAAO,EAAC,OAAO;YAACD,KAAK,EAAC,gBAAgB;YAAAW,QAAA,EAAC;UAAY;YAAAT,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAY,CAAC,eAC5EpC,OAAA,CAAClB,UAAU;YAACkD,OAAO,EAAC,IAAI;YAAAU,QAAA,GAAEgC,KAAK,CAACR,WAAW,CAACxC,OAAO,CAAC,CAAC,CAAC,EAAC,IAAE;UAAA;YAAAO,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAY,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACnE,CAAC,eACNpC,OAAA,CAACnB,GAAG;UAAA6D,QAAA,gBACF1C,OAAA,CAAClB,UAAU;YAACkD,OAAO,EAAC,OAAO;YAACD,KAAK,EAAC,gBAAgB;YAAAW,QAAA,EAAC;UAAa;YAAAT,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAY,CAAC,eAC7EpC,OAAA,CAAClB,UAAU;YAACkD,OAAO,EAAC,IAAI;YAAAU,QAAA,GAAEgC,KAAK,CAACL,YAAY,CAAC3C,OAAO,CAAC,CAAC,CAAC,EAAC,IAAE;UAAA;YAAAO,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAY,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACpE,CAAC,eACNpC,OAAA,CAACnB,GAAG;UAAA6D,QAAA,gBACF1C,OAAA,CAAClB,UAAU;YAACkD,OAAO,EAAC,OAAO;YAACD,KAAK,EAAC,gBAAgB;YAAAW,QAAA,EAAC;UAAgB;YAAAT,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAY,CAAC,eAChFpC,OAAA,CAAClB,UAAU;YAACkD,OAAO,EAAC,IAAI;YAAAU,QAAA,GAAEgC,KAAK,CAACJ,eAAe,CAAC5C,OAAO,CAAC,CAAC,CAAC,EAAC,IAAE;UAAA;YAAAO,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAY,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACvE,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CACN,eAEDpC,OAAA,CAACJ,eAAe;MACdiB,IAAI,EAAEX,MAAO;MACbY,OAAO,EAAEA,OAAQ;MACjBV,oBAAoB,EAAEQ,wBAAyB;MAC/CT,OAAO,EAAEA,OAAQ;MACjBgF,YAAY,EAAC,4BAA4B;MACzCpC,SAAS,EAAEA;IAAU;MAAAd,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACtB,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACC,CAAC;AAEV,CAAC;AAAC3B,EAAA,CA/QIR,qBAAqB;AAAAmF,EAAA,GAArBnF,qBAAqB;AAiR3B,eAAeA,qBAAqB;AAAC,IAAAmF,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}