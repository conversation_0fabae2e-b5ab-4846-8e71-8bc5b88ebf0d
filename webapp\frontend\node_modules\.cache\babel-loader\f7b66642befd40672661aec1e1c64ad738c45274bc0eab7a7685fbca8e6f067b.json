{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\PycharmProjects\\\\CMS\\\\webapp\\\\frontend\\\\src\\\\components\\\\cavi\\\\CavoForm.js\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect } from 'react';\nimport { Box, TextField, Button, Grid, FormControl, InputLabel, Select, MenuItem, FormHelperText, Alert, CircularProgress, Typography, Paper } from '@mui/material';\nimport { Save as SaveIcon, Warning as WarningIcon } from '@mui/icons-material';\nimport { useNavigate } from 'react-router-dom';\nimport { validateCavoData, validateField } from '../../utils/validationUtils';\nimport { redirectToVisualizzaCavi } from '../../utils/navigationUtils';\n\n/**\n * Componente form unificato per l'aggiunta e la modifica di cavi\n *\n * @param {Object} props - Proprietà del componente\n * @param {string} props.mode - Modalità del form: 'add' o 'edit'\n * @param {Object} props.initialData - Dati iniziali del cavo (per la modalità 'edit')\n * @param {string} props.cantiereId - ID del cantiere\n * @param {Function} props.onSubmit - Funzione chiamata alla sottomissione del form\n * @param {Function} props.onSuccess - Funzione chiamata al successo dell'operazione\n * @param {Function} props.onError - Funzione chiamata in caso di errore\n * @param {boolean} props.isDialog - Indica se il form è in un dialog\n * @param {Function} props.onCancel - Funzione chiamata all'annullamento dell'operazione\n */\nimport { jsxDEV as _jsxDEV, Fragment as _Fragment } from \"react/jsx-dev-runtime\";\nconst CavoForm = ({\n  mode = 'add',\n  initialData = {},\n  cantiereId,\n  onSubmit,\n  onSuccess,\n  onError,\n  isDialog = false,\n  onCancel\n}) => {\n  _s();\n  const navigate = useNavigate();\n  const [loading, setLoading] = useState(false);\n  const [loadingRevisione, setLoadingRevisione] = useState(true);\n\n  // Stato iniziale del form\n  const defaultFormData = {\n    id_cavo: '',\n    sistema: '',\n    utility: '',\n    colore_cavo: '',\n    tipologia: '',\n    n_conduttori: '',\n    sezione: '',\n    sh: 'N',\n    ubicazione_partenza: '',\n    utenza_partenza: '',\n    descrizione_utenza_partenza: '',\n    ubicazione_arrivo: '',\n    utenza_arrivo: '',\n    descrizione_utenza_arrivo: '',\n    metri_teorici: '',\n    metratura_reale: '0',\n    responsabile_posa: '',\n    id_bobina: '',\n    stato_installazione: 'Da installare'\n  };\n\n  // Inizializza il form con i dati iniziali o i valori di default\n  const [formData, setFormData] = useState({\n    ...defaultFormData,\n    ...initialData\n  });\n  const [formErrors, setFormErrors] = useState({});\n  const [formWarnings, setFormWarnings] = useState({});\n\n  // Imposta loadingRevisione a false all'avvio\n  useEffect(() => {\n    setLoadingRevisione(false);\n  }, []);\n\n  // Gestisce il cambio dei valori nel form con validazione\n  const handleFormChange = e => {\n    const {\n      name,\n      value\n    } = e.target;\n\n    // Aggiorna il valore nel form\n    setFormData({\n      ...formData,\n      [name]: value\n    });\n\n    // Valida il campo\n    const additionalParams = {};\n    if (name === 'metratura_reale') {\n      additionalParams.metriTeorici = parseFloat(formData.metri_teorici || 0);\n    }\n    const result = validateField(name, value, additionalParams);\n\n    // Aggiorna gli errori\n    setFormErrors(prev => ({\n      ...prev,\n      [name]: !result.valid ? result.message : null\n    }));\n\n    // Aggiorna gli avvisi\n    setFormWarnings(prev => ({\n      ...prev,\n      [name]: result.warning ? result.message : null\n    }));\n  };\n\n  // Gestisce l'annullamento dell'operazione\n  const handleCancel = () => {\n    if (isDialog) {\n      // Se è in un dialog, chiama la funzione onCancel passata come prop\n      if (onCancel) {\n        onCancel();\n      }\n      return;\n    }\n    // Reindirizza alla visualizzazione dei cavi\n    redirectToVisualizzaCavi(navigate);\n  };\n\n  // Gestisce l'invio del form\n  const handleSubmit = async e => {\n    e.preventDefault();\n    setLoading(true);\n    try {\n      // Validazione completa dei dati del cavo\n      console.log('Dati del form prima della validazione:', formData);\n      const validation = validateCavoData(formData);\n      console.log('Risultato validazione:', validation);\n      if (!validation.isValid) {\n        console.error('Errori di validazione:', validation.errors);\n        setFormErrors(validation.errors);\n        setFormWarnings(validation.warnings);\n        setLoading(false);\n        onError('Ci sono errori nel form. Controlla i campi evidenziati in rosso.');\n        return;\n      }\n\n      // Usa i dati validati\n      const validatedData = validation.validatedData;\n\n      // Converti l'ID cavo in maiuscolo\n      validatedData.id_cavo = validatedData.id_cavo.toUpperCase();\n\n      // Chiama la funzione onSubmit passata come prop\n      if (onSubmit) {\n        await onSubmit(validatedData);\n      }\n\n      // Imposta loading a false\n      setLoading(false);\n\n      // Notifica il successo\n      if (onSuccess) {\n        onSuccess(`Cavo ${mode === 'add' ? 'aggiunto' : 'modificato'} con successo`);\n      }\n\n      // Reindirizza solo se non è in un dialog\n      if (!isDialog) {\n        redirectToVisualizzaCavi(navigate);\n      }\n    } catch (error) {\n      console.error(`Errore durante ${mode === \"add\" ? \"l'aggiunta\" : \"la modifica\"} del cavo:`, error);\n      onError(error.message || `Errore durante ${mode === \"add\" ? \"l'aggiunta\" : \"la modifica\"} del cavo`);\n      setLoading(false);\n    }\n  };\n\n  // Mostra un avviso se ci sono warning\n  const hasWarnings = Object.keys(formWarnings).length > 0;\n  return /*#__PURE__*/_jsxDEV(Box, {\n    component: \"form\",\n    onSubmit: handleSubmit,\n    noValidate: true,\n    children: loadingRevisione ? /*#__PURE__*/_jsxDEV(Box, {\n      sx: {\n        display: 'flex',\n        justifyContent: 'center',\n        my: 4\n      },\n      children: /*#__PURE__*/_jsxDEV(CircularProgress, {}, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 187,\n        columnNumber: 11\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 186,\n      columnNumber: 9\n    }, this) : /*#__PURE__*/_jsxDEV(_Fragment, {\n      children: [hasWarnings && /*#__PURE__*/_jsxDEV(_Fragment, {\n        children: [/*#__PURE__*/_jsxDEV(Alert, {\n          severity: \"warning\",\n          icon: /*#__PURE__*/_jsxDEV(WarningIcon, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 195,\n            columnNumber: 23\n          }, this),\n          sx: {\n            mb: isDialog ? 0.5 : 2,\n            py: isDialog ? 0.3 : 1\n          },\n          children: /*#__PURE__*/_jsxDEV(Typography, {\n            variant: \"subtitle2\",\n            sx: {\n              fontSize: isDialog ? '0.8rem' : '0.875rem'\n            },\n            children: \"Ci sono alcuni avvisi nel form. Puoi procedere, ma verifica i campi evidenziati.\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 198,\n            columnNumber: 17\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 193,\n          columnNumber: 15\n        }, this), formWarnings.network_error && /*#__PURE__*/_jsxDEV(Alert, {\n          severity: \"error\",\n          sx: {\n            mb: isDialog ? 0.5 : 3,\n            py: isDialog ? 0.3 : 1\n          },\n          children: /*#__PURE__*/_jsxDEV(Typography, {\n            variant: \"subtitle2\",\n            sx: {\n              fontWeight: 'bold'\n            },\n            children: formWarnings.network_error\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 209,\n            columnNumber: 19\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 205,\n          columnNumber: 17\n        }, this)]\n      }, void 0, true), /*#__PURE__*/_jsxDEV(Paper, {\n        sx: {\n          p: isDialog ? 1.5 : 3,\n          mb: isDialog ? 0.75 : 2,\n          boxShadow: isDialog ? 0 : 1\n        },\n        children: [/*#__PURE__*/_jsxDEV(Typography, {\n          variant: \"h6\",\n          sx: {\n            fontSize: isDialog ? '0.9rem' : '1.25rem',\n            mb: isDialog ? 1 : 1.5,\n            mt: isDialog ? 0.5 : 1,\n            fontWeight: 'bold'\n          },\n          children: \"Informazioni Generali\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 218,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(Grid, {\n          container: true,\n          spacing: isDialog ? 1.5 : 3,\n          children: [/*#__PURE__*/_jsxDEV(Grid, {\n            item: true,\n            xs: 12,\n            sm: 6,\n            children: /*#__PURE__*/_jsxDEV(TextField, {\n              name: \"id_cavo\",\n              label: \"ID Cavo\",\n              fullWidth: true,\n              variant: \"outlined\",\n              value: formData.id_cavo,\n              onChange: handleFormChange,\n              required: true,\n              error: !!formErrors.id_cavo,\n              helperText: formErrors.id_cavo,\n              inputProps: {\n                style: {\n                  textTransform: 'uppercase'\n                }\n              },\n              disabled: mode === 'edit',\n              size: isDialog ? \"small\" : \"medium\",\n              InputLabelProps: {\n                shrink: true,\n                style: {\n                  marginBottom: '4px'\n                }\n              },\n              sx: {\n                mb: 0.5\n              }\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 223,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 222,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(Grid, {\n            item: true,\n            xs: 12,\n            sm: 6,\n            children: /*#__PURE__*/_jsxDEV(TextField, {\n              name: \"utility\",\n              label: \"Utility\",\n              fullWidth: true,\n              variant: \"outlined\",\n              value: formData.utility,\n              onChange: handleFormChange,\n              required: true,\n              error: !!formErrors.utility,\n              helperText: formErrors.utility,\n              size: isDialog ? \"small\" : \"medium\",\n              InputLabelProps: {\n                shrink: true,\n                style: {\n                  marginBottom: '8px'\n                }\n              },\n              sx: {\n                mb: 1\n              }\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 241,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 240,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(Grid, {\n            item: true,\n            xs: 12,\n            sm: 6,\n            children: /*#__PURE__*/_jsxDEV(TextField, {\n              name: \"sistema\",\n              label: \"Sistema\",\n              fullWidth: true,\n              variant: \"outlined\",\n              value: formData.sistema,\n              onChange: handleFormChange,\n              error: !!formErrors.sistema,\n              helperText: formErrors.sistema,\n              size: isDialog ? \"small\" : \"medium\",\n              InputLabelProps: {\n                shrink: true,\n                style: {\n                  marginBottom: '4px'\n                }\n              },\n              sx: {\n                mb: 0.5\n              }\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 257,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 256,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 221,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 217,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(Paper, {\n        sx: {\n          p: isDialog ? 1.5 : 3,\n          mb: isDialog ? 0.75 : 2,\n          boxShadow: isDialog ? 0 : 1\n        },\n        children: [/*#__PURE__*/_jsxDEV(Typography, {\n          variant: \"h6\",\n          sx: {\n            fontSize: isDialog ? '0.9rem' : '1.25rem',\n            mb: isDialog ? 1 : 1.5,\n            mt: isDialog ? 0.5 : 1,\n            fontWeight: 'bold'\n          },\n          children: \"Caratteristiche Tecniche\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 275,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(Grid, {\n          container: true,\n          spacing: isDialog ? 1.5 : 3,\n          children: [/*#__PURE__*/_jsxDEV(Grid, {\n            item: true,\n            xs: 12,\n            sm: 6,\n            children: /*#__PURE__*/_jsxDEV(TextField, {\n              name: \"colore_cavo\",\n              label: \"Colore Cavo\",\n              fullWidth: true,\n              variant: \"outlined\",\n              value: formData.colore_cavo,\n              onChange: handleFormChange,\n              error: !!formErrors.colore_cavo,\n              helperText: formErrors.colore_cavo,\n              size: isDialog ? \"small\" : \"medium\",\n              InputLabelProps: {\n                shrink: true,\n                style: {\n                  marginBottom: '8px'\n                }\n              },\n              sx: {\n                mb: 1\n              }\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 280,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 279,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(Grid, {\n            item: true,\n            xs: 12,\n            sm: 6,\n            children: /*#__PURE__*/_jsxDEV(TextField, {\n              name: \"tipologia\",\n              label: \"Tipologia\",\n              fullWidth: true,\n              variant: \"outlined\",\n              value: formData.tipologia,\n              onChange: handleFormChange,\n              error: !!formErrors.tipologia,\n              helperText: formErrors.tipologia,\n              size: isDialog ? \"small\" : \"medium\",\n              InputLabelProps: {\n                shrink: true,\n                style: {\n                  marginBottom: '8px'\n                }\n              },\n              sx: {\n                mb: 1\n              }\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 295,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 294,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(Grid, {\n            item: true,\n            xs: 12,\n            sm: 4,\n            children: /*#__PURE__*/_jsxDEV(TextField, {\n              name: \"n_conduttori\",\n              label: \"Numero Conduttori\",\n              fullWidth: true,\n              variant: \"outlined\",\n              value: formData.n_conduttori,\n              onChange: handleFormChange,\n              error: !!formErrors.n_conduttori,\n              helperText: formErrors.n_conduttori || formWarnings.n_conduttori,\n              FormHelperTextProps: {\n                style: {\n                  color: formWarnings.n_conduttori ? 'orange' : undefined\n                }\n              },\n              size: isDialog ? \"small\" : \"medium\",\n              InputLabelProps: {\n                shrink: true,\n                style: {\n                  marginBottom: '8px'\n                }\n              },\n              sx: {\n                mb: 1\n              }\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 310,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 309,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(Grid, {\n            item: true,\n            xs: 12,\n            sm: 4,\n            children: /*#__PURE__*/_jsxDEV(TextField, {\n              name: \"sezione\",\n              label: \"Sezione\",\n              fullWidth: true,\n              variant: \"outlined\",\n              value: formData.sezione,\n              onChange: handleFormChange,\n              error: !!formErrors.sezione,\n              helperText: formErrors.sezione || formWarnings.sezione,\n              FormHelperTextProps: {\n                style: {\n                  color: formWarnings.sezione ? 'orange' : undefined\n                }\n              },\n              size: isDialog ? \"small\" : \"medium\",\n              InputLabelProps: {\n                shrink: true,\n                style: {\n                  marginBottom: '8px'\n                }\n              },\n              sx: {\n                mb: 1\n              }\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 328,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 327,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(Grid, {\n            item: true,\n            xs: 12,\n            sm: 4,\n            children: /*#__PURE__*/_jsxDEV(TextField, {\n              select: true,\n              name: \"sh\",\n              label: \"Schermato (S/N)\",\n              fullWidth: true,\n              variant: \"outlined\",\n              value: formData.sh || 'N',\n              onChange: handleFormChange,\n              error: !!formErrors.sh,\n              helperText: formErrors.sh || \"Opzionale (default: N)\",\n              size: isDialog ? \"small\" : \"medium\",\n              InputLabelProps: {\n                shrink: true,\n                style: {\n                  marginBottom: '4px'\n                }\n              },\n              sx: {\n                mb: 0.5\n              },\n              children: [/*#__PURE__*/_jsxDEV(MenuItem, {\n                value: \"S\",\n                children: \"S\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 360,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(MenuItem, {\n                value: \"N\",\n                children: \"N\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 361,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 346,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 345,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 278,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 274,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(Paper, {\n        sx: {\n          p: isDialog ? 1.5 : 3,\n          mb: isDialog ? 0.75 : 2,\n          boxShadow: isDialog ? 0 : 1\n        },\n        children: [/*#__PURE__*/_jsxDEV(Typography, {\n          variant: \"h6\",\n          sx: {\n            fontSize: isDialog ? '0.9rem' : '1.25rem',\n            mb: isDialog ? 1 : 1.5,\n            mt: isDialog ? 0.5 : 1,\n            fontWeight: 'bold'\n          },\n          children: \"Partenza\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 368,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(Grid, {\n          container: true,\n          spacing: isDialog ? 1.5 : 3,\n          children: [/*#__PURE__*/_jsxDEV(Grid, {\n            item: true,\n            xs: 12,\n            sm: 4,\n            children: /*#__PURE__*/_jsxDEV(TextField, {\n              name: \"ubicazione_partenza\",\n              label: \"Ubicazione Partenza\",\n              fullWidth: true,\n              variant: \"outlined\",\n              value: formData.ubicazione_partenza,\n              onChange: handleFormChange,\n              error: !!formErrors.ubicazione_partenza,\n              helperText: formErrors.ubicazione_partenza,\n              size: isDialog ? \"small\" : \"medium\",\n              InputLabelProps: {\n                shrink: true,\n                style: {\n                  marginBottom: '8px'\n                }\n              },\n              sx: {\n                mb: 1\n              }\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 373,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 372,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(Grid, {\n            item: true,\n            xs: 12,\n            sm: 4,\n            children: /*#__PURE__*/_jsxDEV(TextField, {\n              name: \"utenza_partenza\",\n              label: \"Utenza Partenza\",\n              fullWidth: true,\n              variant: \"outlined\",\n              value: formData.utenza_partenza,\n              onChange: handleFormChange,\n              error: !!formErrors.utenza_partenza,\n              helperText: formErrors.utenza_partenza,\n              size: isDialog ? \"small\" : \"medium\",\n              InputLabelProps: {\n                shrink: true,\n                style: {\n                  marginBottom: '4px'\n                }\n              },\n              sx: {\n                mb: 0.5\n              }\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 388,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 387,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(Grid, {\n            item: true,\n            xs: 12,\n            sm: 4,\n            children: /*#__PURE__*/_jsxDEV(TextField, {\n              name: \"descrizione_utenza_partenza\",\n              label: \"Descrizione Utenza Partenza\",\n              fullWidth: true,\n              variant: \"outlined\",\n              value: formData.descrizione_utenza_partenza,\n              onChange: handleFormChange,\n              error: !!formErrors.descrizione_utenza_partenza,\n              helperText: formErrors.descrizione_utenza_partenza,\n              size: isDialog ? \"small\" : \"medium\",\n              InputLabelProps: {\n                shrink: true,\n                style: {\n                  marginBottom: '4px'\n                }\n              },\n              sx: {\n                mb: 0.5\n              }\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 403,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 402,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 371,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 367,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(Paper, {\n        sx: {\n          p: isDialog ? 1.5 : 3,\n          mb: isDialog ? 0.75 : 2,\n          boxShadow: isDialog ? 0 : 1\n        },\n        children: [/*#__PURE__*/_jsxDEV(Typography, {\n          variant: \"h6\",\n          sx: {\n            fontSize: isDialog ? '0.9rem' : '1.25rem',\n            mb: isDialog ? 1 : 1.5,\n            mt: isDialog ? 0.5 : 1,\n            fontWeight: 'bold'\n          },\n          children: \"Arrivo\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 421,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(Grid, {\n          container: true,\n          spacing: isDialog ? 1.5 : 3,\n          children: [/*#__PURE__*/_jsxDEV(Grid, {\n            item: true,\n            xs: 12,\n            sm: 4,\n            children: /*#__PURE__*/_jsxDEV(TextField, {\n              name: \"ubicazione_arrivo\",\n              label: \"Ubicazione Arrivo\",\n              fullWidth: true,\n              variant: \"outlined\",\n              value: formData.ubicazione_arrivo,\n              onChange: handleFormChange,\n              error: !!formErrors.ubicazione_arrivo,\n              helperText: formErrors.ubicazione_arrivo,\n              size: isDialog ? \"small\" : \"medium\",\n              InputLabelProps: {\n                shrink: true,\n                style: {\n                  marginBottom: '4px'\n                }\n              },\n              sx: {\n                mb: 0.5\n              }\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 426,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 425,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(Grid, {\n            item: true,\n            xs: 12,\n            sm: 4,\n            children: /*#__PURE__*/_jsxDEV(TextField, {\n              name: \"utenza_arrivo\",\n              label: \"Utenza Arrivo\",\n              fullWidth: true,\n              variant: \"outlined\",\n              value: formData.utenza_arrivo,\n              onChange: handleFormChange,\n              error: !!formErrors.utenza_arrivo,\n              helperText: formErrors.utenza_arrivo,\n              size: isDialog ? \"small\" : \"medium\",\n              InputLabelProps: {\n                shrink: true,\n                style: {\n                  marginBottom: '4px'\n                }\n              },\n              sx: {\n                mb: 0.5\n              }\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 441,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 440,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(Grid, {\n            item: true,\n            xs: 12,\n            sm: 4,\n            children: /*#__PURE__*/_jsxDEV(TextField, {\n              name: \"descrizione_utenza_arrivo\",\n              label: \"Descrizione Utenza Arrivo\",\n              fullWidth: true,\n              variant: \"outlined\",\n              value: formData.descrizione_utenza_arrivo,\n              onChange: handleFormChange,\n              error: !!formErrors.descrizione_utenza_arrivo,\n              helperText: formErrors.descrizione_utenza_arrivo,\n              size: isDialog ? \"small\" : \"medium\",\n              InputLabelProps: {\n                shrink: true,\n                style: {\n                  marginBottom: '4px'\n                }\n              },\n              sx: {\n                mb: 0.5\n              }\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 456,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 455,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 424,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 420,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(Paper, {\n        sx: {\n          p: isDialog ? 1.5 : 3,\n          mb: isDialog ? 0.75 : 2,\n          boxShadow: isDialog ? 0 : 1\n        },\n        children: [/*#__PURE__*/_jsxDEV(Typography, {\n          variant: \"h6\",\n          sx: {\n            fontSize: isDialog ? '0.9rem' : '1.25rem',\n            mb: isDialog ? 1 : 1.5,\n            mt: isDialog ? 0.5 : 1,\n            fontWeight: 'bold'\n          },\n          children: \"Metratura\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 474,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(Grid, {\n          container: true,\n          spacing: isDialog ? 1.5 : 3,\n          children: /*#__PURE__*/_jsxDEV(Grid, {\n            item: true,\n            xs: 12,\n            sm: 6,\n            children: /*#__PURE__*/_jsxDEV(TextField, {\n              name: \"metri_teorici\",\n              label: \"Metri Teorici\",\n              fullWidth: true,\n              variant: \"outlined\",\n              value: formData.metri_teorici,\n              onChange: handleFormChange,\n              required: true,\n              error: !!formErrors.metri_teorici,\n              helperText: formErrors.metri_teorici,\n              size: isDialog ? \"small\" : \"medium\",\n              InputLabelProps: {\n                shrink: true,\n                style: {\n                  marginBottom: '8px'\n                }\n              },\n              sx: {\n                mb: 1\n              }\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 479,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 478,\n            columnNumber: 15\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 477,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 473,\n        columnNumber: 11\n      }, this), !isDialog && /*#__PURE__*/_jsxDEV(Box, {\n        sx: {\n          mt: 3,\n          display: 'flex',\n          justifyContent: 'center',\n          gap: 2\n        },\n        children: [/*#__PURE__*/_jsxDEV(Button, {\n          variant: \"outlined\",\n          color: \"secondary\",\n          size: \"large\",\n          onClick: handleCancel,\n          disabled: loading,\n          sx: {\n            minWidth: 150\n          },\n          children: \"Annulla\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 499,\n          columnNumber: 15\n        }, this), /*#__PURE__*/_jsxDEV(Button, {\n          type: \"submit\",\n          variant: \"contained\",\n          color: \"primary\",\n          size: \"large\",\n          startIcon: /*#__PURE__*/_jsxDEV(SaveIcon, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 514,\n            columnNumber: 28\n          }, this),\n          disabled: loading,\n          sx: {\n            minWidth: 150\n          },\n          children: loading ? /*#__PURE__*/_jsxDEV(CircularProgress, {\n            size: 24\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 518,\n            columnNumber: 28\n          }, this) : mode === 'add' ? 'Salva Cavo' : 'Salva Modifiche'\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 509,\n          columnNumber: 15\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 498,\n        columnNumber: 13\n      }, this), isDialog && /*#__PURE__*/_jsxDEV(Box, {\n        sx: {\n          mt: 2,\n          display: 'flex',\n          justifyContent: 'center',\n          gap: 3\n        },\n        children: [/*#__PURE__*/_jsxDEV(Button, {\n          variant: \"outlined\",\n          color: \"secondary\",\n          size: \"large\",\n          onClick: handleCancel,\n          disabled: loading,\n          sx: {\n            minWidth: 120\n          },\n          children: \"Annulla\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 525,\n          columnNumber: 15\n        }, this), /*#__PURE__*/_jsxDEV(Button, {\n          type: \"submit\",\n          variant: \"contained\",\n          color: \"primary\",\n          size: \"large\",\n          startIcon: /*#__PURE__*/_jsxDEV(SaveIcon, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 540,\n            columnNumber: 28\n          }, this),\n          disabled: loading,\n          sx: {\n            minWidth: 120\n          },\n          children: loading ? /*#__PURE__*/_jsxDEV(CircularProgress, {\n            size: 24\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 544,\n            columnNumber: 28\n          }, this) : mode === 'add' ? 'Salva Cavo' : 'Salva Modifiche'\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 535,\n          columnNumber: 15\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 524,\n        columnNumber: 13\n      }, this)]\n    }, void 0, true)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 184,\n    columnNumber: 5\n  }, this);\n};\n_s(CavoForm, \"qukpGhyI/RAY3N/npombpcGN9Yw=\", false, function () {\n  return [useNavigate];\n});\n_c = CavoForm;\nexport default CavoForm;\nvar _c;\n$RefreshReg$(_c, \"CavoForm\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "Box", "TextField", "<PERSON><PERSON>", "Grid", "FormControl", "InputLabel", "Select", "MenuItem", "FormHelperText", "<PERSON><PERSON>", "CircularProgress", "Typography", "Paper", "Save", "SaveIcon", "Warning", "WarningIcon", "useNavigate", "validateCavoData", "validateField", "redirectToVisualizzaCavi", "jsxDEV", "_jsxDEV", "Fragment", "_Fragment", "CavoForm", "mode", "initialData", "cantiereId", "onSubmit", "onSuccess", "onError", "isDialog", "onCancel", "_s", "navigate", "loading", "setLoading", "loadingRevisione", "setLoadingRevisione", "defaultFormData", "id_cavo", "sistema", "utility", "colore_cavo", "tipologia", "n_conduttori", "sezione", "sh", "ubicazione_partenza", "utenza_partenza", "descrizione_utenza_partenza", "ubicazione_arrivo", "utenza_arrivo", "descrizione_utenza_arrivo", "metri_te<PERSON>ci", "metratura_reale", "responsabile_posa", "id_bobina", "stato_installazione", "formData", "setFormData", "formErrors", "setFormErrors", "formWarnings", "setForm<PERSON><PERSON>nings", "handleFormChange", "e", "name", "value", "target", "additionalParams", "metriTeorici", "parseFloat", "result", "prev", "valid", "message", "warning", "handleCancel", "handleSubmit", "preventDefault", "console", "log", "validation", "<PERSON><PERSON><PERSON><PERSON>", "error", "errors", "warnings", "validatedData", "toUpperCase", "hasWarnings", "Object", "keys", "length", "component", "noValidate", "children", "sx", "display", "justifyContent", "my", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "severity", "icon", "mb", "py", "variant", "fontSize", "network_error", "fontWeight", "p", "boxShadow", "mt", "container", "spacing", "item", "xs", "sm", "label", "fullWidth", "onChange", "required", "helperText", "inputProps", "style", "textTransform", "disabled", "size", "InputLabelProps", "shrink", "marginBottom", "FormHelperTextProps", "color", "undefined", "select", "gap", "onClick", "min<PERSON><PERSON><PERSON>", "type", "startIcon", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/PycharmProjects/CMS/webapp/frontend/src/components/cavi/CavoForm.js"], "sourcesContent": ["import React, { useState, useEffect } from 'react';\nimport {\n  Box,\n  TextField,\n  Button,\n  Grid,\n  FormControl,\n  InputLabel,\n  Select,\n  MenuItem,\n  FormHelperText,\n  Alert,\n  CircularProgress,\n  Typography,\n  Paper\n} from '@mui/material';\nimport { Save as SaveIcon, Warning as WarningIcon } from '@mui/icons-material';\nimport { useNavigate } from 'react-router-dom';\nimport { validateCavoData, validateField } from '../../utils/validationUtils';\nimport { redirectToVisualizzaCavi } from '../../utils/navigationUtils';\n\n/**\n * Componente form unificato per l'aggiunta e la modifica di cavi\n *\n * @param {Object} props - Proprietà del componente\n * @param {string} props.mode - Modalità del form: 'add' o 'edit'\n * @param {Object} props.initialData - Dati iniziali del cavo (per la modalità 'edit')\n * @param {string} props.cantiereId - ID del cantiere\n * @param {Function} props.onSubmit - Funzione chiamata alla sottomissione del form\n * @param {Function} props.onSuccess - Funzione chiamata al successo dell'operazione\n * @param {Function} props.onError - Funzione chiamata in caso di errore\n * @param {boolean} props.isDialog - Indica se il form è in un dialog\n * @param {Function} props.onCancel - Funzione chiamata all'annullamento dell'operazione\n */\nconst CavoForm = ({\n  mode = 'add',\n  initialData = {},\n  cantiereId,\n  onSubmit,\n  onSuccess,\n  onError,\n  isDialog = false,\n  onCancel\n}) => {\n  const navigate = useNavigate();\n  const [loading, setLoading] = useState(false);\n  const [loadingRevisione, setLoadingRevisione] = useState(true);\n\n  // Stato iniziale del form\n  const defaultFormData = {\n    id_cavo: '',\n    sistema: '',\n    utility: '',\n    colore_cavo: '',\n    tipologia: '',\n    n_conduttori: '',\n    sezione: '',\n    sh: 'N',\n    ubicazione_partenza: '',\n    utenza_partenza: '',\n    descrizione_utenza_partenza: '',\n    ubicazione_arrivo: '',\n    utenza_arrivo: '',\n    descrizione_utenza_arrivo: '',\n    metri_teorici: '',\n    metratura_reale: '0',\n    responsabile_posa: '',\n    id_bobina: '',\n    stato_installazione: 'Da installare'\n  };\n\n  // Inizializza il form con i dati iniziali o i valori di default\n  const [formData, setFormData] = useState({\n    ...defaultFormData,\n    ...initialData\n  });\n\n  const [formErrors, setFormErrors] = useState({});\n  const [formWarnings, setFormWarnings] = useState({});\n\n  // Imposta loadingRevisione a false all'avvio\n  useEffect(() => {\n    setLoadingRevisione(false);\n  }, []);\n\n  // Gestisce il cambio dei valori nel form con validazione\n  const handleFormChange = (e) => {\n    const { name, value } = e.target;\n\n    // Aggiorna il valore nel form\n    setFormData({\n      ...formData,\n      [name]: value\n    });\n\n    // Valida il campo\n    const additionalParams = {};\n    if (name === 'metratura_reale') {\n      additionalParams.metriTeorici = parseFloat(formData.metri_teorici || 0);\n    }\n\n    const result = validateField(name, value, additionalParams);\n\n    // Aggiorna gli errori\n    setFormErrors(prev => ({\n      ...prev,\n      [name]: !result.valid ? result.message : null\n    }));\n\n    // Aggiorna gli avvisi\n    setFormWarnings(prev => ({\n      ...prev,\n      [name]: result.warning ? result.message : null\n    }));\n  };\n\n  // Gestisce l'annullamento dell'operazione\n  const handleCancel = () => {\n    if (isDialog) {\n      // Se è in un dialog, chiama la funzione onCancel passata come prop\n      if (onCancel) {\n        onCancel();\n      }\n      return;\n    }\n    // Reindirizza alla visualizzazione dei cavi\n    redirectToVisualizzaCavi(navigate);\n  };\n\n  // Gestisce l'invio del form\n  const handleSubmit = async (e) => {\n    e.preventDefault();\n    setLoading(true);\n\n    try {\n      // Validazione completa dei dati del cavo\n      console.log('Dati del form prima della validazione:', formData);\n      const validation = validateCavoData(formData);\n      console.log('Risultato validazione:', validation);\n\n      if (!validation.isValid) {\n        console.error('Errori di validazione:', validation.errors);\n        setFormErrors(validation.errors);\n        setFormWarnings(validation.warnings);\n        setLoading(false);\n        onError('Ci sono errori nel form. Controlla i campi evidenziati in rosso.');\n        return;\n      }\n\n      // Usa i dati validati\n      const validatedData = validation.validatedData;\n\n      // Converti l'ID cavo in maiuscolo\n      validatedData.id_cavo = validatedData.id_cavo.toUpperCase();\n\n      // Chiama la funzione onSubmit passata come prop\n      if (onSubmit) {\n        await onSubmit(validatedData);\n      }\n\n      // Imposta loading a false\n      setLoading(false);\n\n      // Notifica il successo\n      if (onSuccess) {\n        onSuccess(`Cavo ${mode === 'add' ? 'aggiunto' : 'modificato'} con successo`);\n      }\n\n      // Reindirizza solo se non è in un dialog\n      if (!isDialog) {\n        redirectToVisualizzaCavi(navigate);\n      }\n    } catch (error) {\n      console.error(`Errore durante ${mode === \"add\" ? \"l'aggiunta\" : \"la modifica\"} del cavo:`, error);\n      onError(error.message || `Errore durante ${mode === \"add\" ? \"l'aggiunta\" : \"la modifica\"} del cavo`);\n      setLoading(false);\n    }\n  };\n\n  // Mostra un avviso se ci sono warning\n  const hasWarnings = Object.keys(formWarnings).length > 0;\n\n  return (\n    <Box component=\"form\" onSubmit={handleSubmit} noValidate>\n      {loadingRevisione ? (\n        <Box sx={{ display: 'flex', justifyContent: 'center', my: 4 }}>\n          <CircularProgress />\n        </Box>\n      ) : (\n        <>\n          {hasWarnings && (\n            <>\n              <Alert\n                severity=\"warning\"\n                icon={<WarningIcon />}\n                sx={{ mb: isDialog ? 0.5 : 2, py: isDialog ? 0.3 : 1 }}\n              >\n                <Typography variant=\"subtitle2\" sx={{ fontSize: isDialog ? '0.8rem' : '0.875rem' }}>\n                  Ci sono alcuni avvisi nel form. Puoi procedere, ma verifica i campi evidenziati.\n                </Typography>\n              </Alert>\n\n              {/* Mostra avvisi specifici */}\n              {formWarnings.network_error && (\n                <Alert\n                  severity=\"error\"\n                  sx={{ mb: isDialog ? 0.5 : 3, py: isDialog ? 0.3 : 1 }}\n                >\n                  <Typography variant=\"subtitle2\" sx={{ fontWeight: 'bold' }}>\n                    {formWarnings.network_error}\n                  </Typography>\n                </Alert>\n              )}\n            </>\n          )}\n\n          <Paper sx={{ p: isDialog ? 1.5 : 3, mb: isDialog ? 0.75 : 2, boxShadow: isDialog ? 0 : 1 }}>\n            <Typography variant=\"h6\" sx={{ fontSize: isDialog ? '0.9rem' : '1.25rem', mb: isDialog ? 1 : 1.5, mt: isDialog ? 0.5 : 1, fontWeight: 'bold' }}>\n              Informazioni Generali\n            </Typography>\n            <Grid container spacing={isDialog ? 1.5 : 3}>\n              <Grid item xs={12} sm={6}>\n                <TextField\n                  name=\"id_cavo\"\n                  label=\"ID Cavo\"\n                  fullWidth\n                  variant=\"outlined\"\n                  value={formData.id_cavo}\n                  onChange={handleFormChange}\n                  required\n                  error={!!formErrors.id_cavo}\n                  helperText={formErrors.id_cavo}\n                  inputProps={{ style: { textTransform: 'uppercase' } }}\n                  disabled={mode === 'edit'}\n                  size={isDialog ? \"small\" : \"medium\"}\n                  InputLabelProps={{ shrink: true, style: { marginBottom: '4px' } }}\n                  sx={{ mb: 0.5 }}\n                />\n              </Grid>\n              <Grid item xs={12} sm={6}>\n                <TextField\n                  name=\"utility\"\n                  label=\"Utility\"\n                  fullWidth\n                  variant=\"outlined\"\n                  value={formData.utility}\n                  onChange={handleFormChange}\n                  required\n                  error={!!formErrors.utility}\n                  helperText={formErrors.utility}\n                  size={isDialog ? \"small\" : \"medium\"}\n                  InputLabelProps={{ shrink: true, style: { marginBottom: '8px' } }}\n                  sx={{ mb: 1 }}\n                />\n              </Grid>\n              <Grid item xs={12} sm={6}>\n                <TextField\n                  name=\"sistema\"\n                  label=\"Sistema\"\n                  fullWidth\n                  variant=\"outlined\"\n                  value={formData.sistema}\n                  onChange={handleFormChange}\n                  error={!!formErrors.sistema}\n                  helperText={formErrors.sistema}\n                  size={isDialog ? \"small\" : \"medium\"}\n                  InputLabelProps={{ shrink: true, style: { marginBottom: '4px' } }}\n                  sx={{ mb: 0.5 }}\n                />\n              </Grid>\n            </Grid>\n          </Paper>\n\n          <Paper sx={{ p: isDialog ? 1.5 : 3, mb: isDialog ? 0.75 : 2, boxShadow: isDialog ? 0 : 1 }}>\n            <Typography variant=\"h6\" sx={{ fontSize: isDialog ? '0.9rem' : '1.25rem', mb: isDialog ? 1 : 1.5, mt: isDialog ? 0.5 : 1, fontWeight: 'bold' }}>\n              Caratteristiche Tecniche\n            </Typography>\n            <Grid container spacing={isDialog ? 1.5 : 3}>\n              <Grid item xs={12} sm={6}>\n                <TextField\n                  name=\"colore_cavo\"\n                  label=\"Colore Cavo\"\n                  fullWidth\n                  variant=\"outlined\"\n                  value={formData.colore_cavo}\n                  onChange={handleFormChange}\n                  error={!!formErrors.colore_cavo}\n                  helperText={formErrors.colore_cavo}\n                  size={isDialog ? \"small\" : \"medium\"}\n                  InputLabelProps={{ shrink: true, style: { marginBottom: '8px' } }}\n                  sx={{ mb: 1 }}\n                />\n              </Grid>\n              <Grid item xs={12} sm={6}>\n                <TextField\n                  name=\"tipologia\"\n                  label=\"Tipologia\"\n                  fullWidth\n                  variant=\"outlined\"\n                  value={formData.tipologia}\n                  onChange={handleFormChange}\n                  error={!!formErrors.tipologia}\n                  helperText={formErrors.tipologia}\n                  size={isDialog ? \"small\" : \"medium\"}\n                  InputLabelProps={{ shrink: true, style: { marginBottom: '8px' } }}\n                  sx={{ mb: 1 }}\n                />\n              </Grid>\n              <Grid item xs={12} sm={4}>\n                <TextField\n                  name=\"n_conduttori\"\n                  label=\"Numero Conduttori\"\n                  fullWidth\n                  variant=\"outlined\"\n                  value={formData.n_conduttori}\n                  onChange={handleFormChange}\n                  error={!!formErrors.n_conduttori}\n                  helperText={formErrors.n_conduttori || formWarnings.n_conduttori}\n                  FormHelperTextProps={{\n                    style: { color: formWarnings.n_conduttori ? 'orange' : undefined }\n                  }}\n                  size={isDialog ? \"small\" : \"medium\"}\n                  InputLabelProps={{ shrink: true, style: { marginBottom: '8px' } }}\n                  sx={{ mb: 1 }}\n                />\n              </Grid>\n              <Grid item xs={12} sm={4}>\n                <TextField\n                  name=\"sezione\"\n                  label=\"Sezione\"\n                  fullWidth\n                  variant=\"outlined\"\n                  value={formData.sezione}\n                  onChange={handleFormChange}\n                  error={!!formErrors.sezione}\n                  helperText={formErrors.sezione || formWarnings.sezione}\n                  FormHelperTextProps={{\n                    style: { color: formWarnings.sezione ? 'orange' : undefined }\n                  }}\n                  size={isDialog ? \"small\" : \"medium\"}\n                  InputLabelProps={{ shrink: true, style: { marginBottom: '8px' } }}\n                  sx={{ mb: 1 }}\n                />\n              </Grid>\n              <Grid item xs={12} sm={4}>\n                <TextField\n                  select\n                  name=\"sh\"\n                  label=\"Schermato (S/N)\"\n                  fullWidth\n                  variant=\"outlined\"\n                  value={formData.sh || 'N'}\n                  onChange={handleFormChange}\n                  error={!!formErrors.sh}\n                  helperText={formErrors.sh || \"Opzionale (default: N)\"}\n                  size={isDialog ? \"small\" : \"medium\"}\n                  InputLabelProps={{ shrink: true, style: { marginBottom: '4px' } }}\n                  sx={{ mb: 0.5 }}\n                >\n                  <MenuItem value=\"S\">S</MenuItem>\n                  <MenuItem value=\"N\">N</MenuItem>\n                </TextField>\n              </Grid>\n            </Grid>\n          </Paper>\n\n          <Paper sx={{ p: isDialog ? 1.5 : 3, mb: isDialog ? 0.75 : 2, boxShadow: isDialog ? 0 : 1 }}>\n            <Typography variant=\"h6\" sx={{ fontSize: isDialog ? '0.9rem' : '1.25rem', mb: isDialog ? 1 : 1.5, mt: isDialog ? 0.5 : 1, fontWeight: 'bold' }}>\n              Partenza\n            </Typography>\n            <Grid container spacing={isDialog ? 1.5 : 3}>\n              <Grid item xs={12} sm={4}>\n                <TextField\n                  name=\"ubicazione_partenza\"\n                  label=\"Ubicazione Partenza\"\n                  fullWidth\n                  variant=\"outlined\"\n                  value={formData.ubicazione_partenza}\n                  onChange={handleFormChange}\n                  error={!!formErrors.ubicazione_partenza}\n                  helperText={formErrors.ubicazione_partenza}\n                  size={isDialog ? \"small\" : \"medium\"}\n                  InputLabelProps={{ shrink: true, style: { marginBottom: '8px' } }}\n                  sx={{ mb: 1 }}\n                />\n              </Grid>\n              <Grid item xs={12} sm={4}>\n                <TextField\n                  name=\"utenza_partenza\"\n                  label=\"Utenza Partenza\"\n                  fullWidth\n                  variant=\"outlined\"\n                  value={formData.utenza_partenza}\n                  onChange={handleFormChange}\n                  error={!!formErrors.utenza_partenza}\n                  helperText={formErrors.utenza_partenza}\n                  size={isDialog ? \"small\" : \"medium\"}\n                  InputLabelProps={{ shrink: true, style: { marginBottom: '4px' } }}\n                  sx={{ mb: 0.5 }}\n                />\n              </Grid>\n              <Grid item xs={12} sm={4}>\n                <TextField\n                  name=\"descrizione_utenza_partenza\"\n                  label=\"Descrizione Utenza Partenza\"\n                  fullWidth\n                  variant=\"outlined\"\n                  value={formData.descrizione_utenza_partenza}\n                  onChange={handleFormChange}\n                  error={!!formErrors.descrizione_utenza_partenza}\n                  helperText={formErrors.descrizione_utenza_partenza}\n                  size={isDialog ? \"small\" : \"medium\"}\n                  InputLabelProps={{ shrink: true, style: { marginBottom: '4px' } }}\n                  sx={{ mb: 0.5 }}\n                />\n              </Grid>\n            </Grid>\n          </Paper>\n\n          <Paper sx={{ p: isDialog ? 1.5 : 3, mb: isDialog ? 0.75 : 2, boxShadow: isDialog ? 0 : 1 }}>\n            <Typography variant=\"h6\" sx={{ fontSize: isDialog ? '0.9rem' : '1.25rem', mb: isDialog ? 1 : 1.5, mt: isDialog ? 0.5 : 1, fontWeight: 'bold' }}>\n              Arrivo\n            </Typography>\n            <Grid container spacing={isDialog ? 1.5 : 3}>\n              <Grid item xs={12} sm={4}>\n                <TextField\n                  name=\"ubicazione_arrivo\"\n                  label=\"Ubicazione Arrivo\"\n                  fullWidth\n                  variant=\"outlined\"\n                  value={formData.ubicazione_arrivo}\n                  onChange={handleFormChange}\n                  error={!!formErrors.ubicazione_arrivo}\n                  helperText={formErrors.ubicazione_arrivo}\n                  size={isDialog ? \"small\" : \"medium\"}\n                  InputLabelProps={{ shrink: true, style: { marginBottom: '4px' } }}\n                  sx={{ mb: 0.5 }}\n                />\n              </Grid>\n              <Grid item xs={12} sm={4}>\n                <TextField\n                  name=\"utenza_arrivo\"\n                  label=\"Utenza Arrivo\"\n                  fullWidth\n                  variant=\"outlined\"\n                  value={formData.utenza_arrivo}\n                  onChange={handleFormChange}\n                  error={!!formErrors.utenza_arrivo}\n                  helperText={formErrors.utenza_arrivo}\n                  size={isDialog ? \"small\" : \"medium\"}\n                  InputLabelProps={{ shrink: true, style: { marginBottom: '4px' } }}\n                  sx={{ mb: 0.5 }}\n                />\n              </Grid>\n              <Grid item xs={12} sm={4}>\n                <TextField\n                  name=\"descrizione_utenza_arrivo\"\n                  label=\"Descrizione Utenza Arrivo\"\n                  fullWidth\n                  variant=\"outlined\"\n                  value={formData.descrizione_utenza_arrivo}\n                  onChange={handleFormChange}\n                  error={!!formErrors.descrizione_utenza_arrivo}\n                  helperText={formErrors.descrizione_utenza_arrivo}\n                  size={isDialog ? \"small\" : \"medium\"}\n                  InputLabelProps={{ shrink: true, style: { marginBottom: '4px' } }}\n                  sx={{ mb: 0.5 }}\n                />\n              </Grid>\n            </Grid>\n          </Paper>\n\n          <Paper sx={{ p: isDialog ? 1.5 : 3, mb: isDialog ? 0.75 : 2, boxShadow: isDialog ? 0 : 1 }}>\n            <Typography variant=\"h6\" sx={{ fontSize: isDialog ? '0.9rem' : '1.25rem', mb: isDialog ? 1 : 1.5, mt: isDialog ? 0.5 : 1, fontWeight: 'bold' }}>\n              Metratura\n            </Typography>\n            <Grid container spacing={isDialog ? 1.5 : 3}>\n              <Grid item xs={12} sm={6}>\n                <TextField\n                  name=\"metri_teorici\"\n                  label=\"Metri Teorici\"\n                  fullWidth\n                  variant=\"outlined\"\n                  value={formData.metri_teorici}\n                  onChange={handleFormChange}\n                  required\n                  error={!!formErrors.metri_teorici}\n                  helperText={formErrors.metri_teorici}\n                  size={isDialog ? \"small\" : \"medium\"}\n                  InputLabelProps={{ shrink: true, style: { marginBottom: '8px' } }}\n                  sx={{ mb: 1 }}\n                />\n              </Grid>\n            </Grid>\n          </Paper>\n\n          {!isDialog && (\n            <Box sx={{ mt: 3, display: 'flex', justifyContent: 'center', gap: 2 }}>\n              <Button\n                variant=\"outlined\"\n                color=\"secondary\"\n                size=\"large\"\n                onClick={handleCancel}\n                disabled={loading}\n                sx={{ minWidth: 150 }}\n              >\n                Annulla\n              </Button>\n              <Button\n                type=\"submit\"\n                variant=\"contained\"\n                color=\"primary\"\n                size=\"large\"\n                startIcon={<SaveIcon />}\n                disabled={loading}\n                sx={{ minWidth: 150 }}\n              >\n                {loading ? <CircularProgress size={24} /> : mode === 'add' ? 'Salva Cavo' : 'Salva Modifiche'}\n              </Button>\n            </Box>\n          )}\n\n          {isDialog && (\n            <Box sx={{ mt: 2, display: 'flex', justifyContent: 'center', gap: 3 }}>\n              <Button\n                variant=\"outlined\"\n                color=\"secondary\"\n                size=\"large\"\n                onClick={handleCancel}\n                disabled={loading}\n                sx={{ minWidth: 120 }}\n              >\n                Annulla\n              </Button>\n              <Button\n                type=\"submit\"\n                variant=\"contained\"\n                color=\"primary\"\n                size=\"large\"\n                startIcon={<SaveIcon />}\n                disabled={loading}\n                sx={{ minWidth: 120 }}\n              >\n                {loading ? <CircularProgress size={24} /> : mode === 'add' ? 'Salva Cavo' : 'Salva Modifiche'}\n              </Button>\n            </Box>\n          )}\n        </>\n      )}\n    </Box>\n  );\n};\n\nexport default CavoForm;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAClD,SACEC,GAAG,EACHC,SAAS,EACTC,MAAM,EACNC,IAAI,EACJC,WAAW,EACXC,UAAU,EACVC,MAAM,EACNC,QAAQ,EACRC,cAAc,EACdC,KAAK,EACLC,gBAAgB,EAChBC,UAAU,EACVC,KAAK,QACA,eAAe;AACtB,SAASC,IAAI,IAAIC,QAAQ,EAAEC,OAAO,IAAIC,WAAW,QAAQ,qBAAqB;AAC9E,SAASC,WAAW,QAAQ,kBAAkB;AAC9C,SAASC,gBAAgB,EAAEC,aAAa,QAAQ,6BAA6B;AAC7E,SAASC,wBAAwB,QAAQ,6BAA6B;;AAEtE;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAZA,SAAAC,MAAA,IAAAC,OAAA,EAAAC,QAAA,IAAAC,SAAA;AAaA,MAAMC,QAAQ,GAAGA,CAAC;EAChBC,IAAI,GAAG,KAAK;EACZC,WAAW,GAAG,CAAC,CAAC;EAChBC,UAAU;EACVC,QAAQ;EACRC,SAAS;EACTC,OAAO;EACPC,QAAQ,GAAG,KAAK;EAChBC;AACF,CAAC,KAAK;EAAAC,EAAA;EACJ,MAAMC,QAAQ,GAAGlB,WAAW,CAAC,CAAC;EAC9B,MAAM,CAACmB,OAAO,EAAEC,UAAU,CAAC,GAAGvC,QAAQ,CAAC,KAAK,CAAC;EAC7C,MAAM,CAACwC,gBAAgB,EAAEC,mBAAmB,CAAC,GAAGzC,QAAQ,CAAC,IAAI,CAAC;;EAE9D;EACA,MAAM0C,eAAe,GAAG;IACtBC,OAAO,EAAE,EAAE;IACXC,OAAO,EAAE,EAAE;IACXC,OAAO,EAAE,EAAE;IACXC,WAAW,EAAE,EAAE;IACfC,SAAS,EAAE,EAAE;IACbC,YAAY,EAAE,EAAE;IAChBC,OAAO,EAAE,EAAE;IACXC,EAAE,EAAE,GAAG;IACPC,mBAAmB,EAAE,EAAE;IACvBC,eAAe,EAAE,EAAE;IACnBC,2BAA2B,EAAE,EAAE;IAC/BC,iBAAiB,EAAE,EAAE;IACrBC,aAAa,EAAE,EAAE;IACjBC,yBAAyB,EAAE,EAAE;IAC7BC,aAAa,EAAE,EAAE;IACjBC,eAAe,EAAE,GAAG;IACpBC,iBAAiB,EAAE,EAAE;IACrBC,SAAS,EAAE,EAAE;IACbC,mBAAmB,EAAE;EACvB,CAAC;;EAED;EACA,MAAM,CAACC,QAAQ,EAAEC,WAAW,CAAC,GAAG/D,QAAQ,CAAC;IACvC,GAAG0C,eAAe;IAClB,GAAGb;EACL,CAAC,CAAC;EAEF,MAAM,CAACmC,UAAU,EAAEC,aAAa,CAAC,GAAGjE,QAAQ,CAAC,CAAC,CAAC,CAAC;EAChD,MAAM,CAACkE,YAAY,EAAEC,eAAe,CAAC,GAAGnE,QAAQ,CAAC,CAAC,CAAC,CAAC;;EAEpD;EACAC,SAAS,CAAC,MAAM;IACdwC,mBAAmB,CAAC,KAAK,CAAC;EAC5B,CAAC,EAAE,EAAE,CAAC;;EAEN;EACA,MAAM2B,gBAAgB,GAAIC,CAAC,IAAK;IAC9B,MAAM;MAAEC,IAAI;MAAEC;IAAM,CAAC,GAAGF,CAAC,CAACG,MAAM;;IAEhC;IACAT,WAAW,CAAC;MACV,GAAGD,QAAQ;MACX,CAACQ,IAAI,GAAGC;IACV,CAAC,CAAC;;IAEF;IACA,MAAME,gBAAgB,GAAG,CAAC,CAAC;IAC3B,IAAIH,IAAI,KAAK,iBAAiB,EAAE;MAC9BG,gBAAgB,CAACC,YAAY,GAAGC,UAAU,CAACb,QAAQ,CAACL,aAAa,IAAI,CAAC,CAAC;IACzE;IAEA,MAAMmB,MAAM,GAAGvD,aAAa,CAACiD,IAAI,EAAEC,KAAK,EAAEE,gBAAgB,CAAC;;IAE3D;IACAR,aAAa,CAACY,IAAI,KAAK;MACrB,GAAGA,IAAI;MACP,CAACP,IAAI,GAAG,CAACM,MAAM,CAACE,KAAK,GAAGF,MAAM,CAACG,OAAO,GAAG;IAC3C,CAAC,CAAC,CAAC;;IAEH;IACAZ,eAAe,CAACU,IAAI,KAAK;MACvB,GAAGA,IAAI;MACP,CAACP,IAAI,GAAGM,MAAM,CAACI,OAAO,GAAGJ,MAAM,CAACG,OAAO,GAAG;IAC5C,CAAC,CAAC,CAAC;EACL,CAAC;;EAED;EACA,MAAME,YAAY,GAAGA,CAAA,KAAM;IACzB,IAAI/C,QAAQ,EAAE;MACZ;MACA,IAAIC,QAAQ,EAAE;QACZA,QAAQ,CAAC,CAAC;MACZ;MACA;IACF;IACA;IACAb,wBAAwB,CAACe,QAAQ,CAAC;EACpC,CAAC;;EAED;EACA,MAAM6C,YAAY,GAAG,MAAOb,CAAC,IAAK;IAChCA,CAAC,CAACc,cAAc,CAAC,CAAC;IAClB5C,UAAU,CAAC,IAAI,CAAC;IAEhB,IAAI;MACF;MACA6C,OAAO,CAACC,GAAG,CAAC,wCAAwC,EAAEvB,QAAQ,CAAC;MAC/D,MAAMwB,UAAU,GAAGlE,gBAAgB,CAAC0C,QAAQ,CAAC;MAC7CsB,OAAO,CAACC,GAAG,CAAC,wBAAwB,EAAEC,UAAU,CAAC;MAEjD,IAAI,CAACA,UAAU,CAACC,OAAO,EAAE;QACvBH,OAAO,CAACI,KAAK,CAAC,wBAAwB,EAAEF,UAAU,CAACG,MAAM,CAAC;QAC1DxB,aAAa,CAACqB,UAAU,CAACG,MAAM,CAAC;QAChCtB,eAAe,CAACmB,UAAU,CAACI,QAAQ,CAAC;QACpCnD,UAAU,CAAC,KAAK,CAAC;QACjBN,OAAO,CAAC,kEAAkE,CAAC;QAC3E;MACF;;MAEA;MACA,MAAM0D,aAAa,GAAGL,UAAU,CAACK,aAAa;;MAE9C;MACAA,aAAa,CAAChD,OAAO,GAAGgD,aAAa,CAAChD,OAAO,CAACiD,WAAW,CAAC,CAAC;;MAE3D;MACA,IAAI7D,QAAQ,EAAE;QACZ,MAAMA,QAAQ,CAAC4D,aAAa,CAAC;MAC/B;;MAEA;MACApD,UAAU,CAAC,KAAK,CAAC;;MAEjB;MACA,IAAIP,SAAS,EAAE;QACbA,SAAS,CAAC,QAAQJ,IAAI,KAAK,KAAK,GAAG,UAAU,GAAG,YAAY,eAAe,CAAC;MAC9E;;MAEA;MACA,IAAI,CAACM,QAAQ,EAAE;QACbZ,wBAAwB,CAACe,QAAQ,CAAC;MACpC;IACF,CAAC,CAAC,OAAOmD,KAAK,EAAE;MACdJ,OAAO,CAACI,KAAK,CAAC,kBAAkB5D,IAAI,KAAK,KAAK,GAAG,YAAY,GAAG,aAAa,YAAY,EAAE4D,KAAK,CAAC;MACjGvD,OAAO,CAACuD,KAAK,CAACT,OAAO,IAAI,kBAAkBnD,IAAI,KAAK,KAAK,GAAG,YAAY,GAAG,aAAa,WAAW,CAAC;MACpGW,UAAU,CAAC,KAAK,CAAC;IACnB;EACF,CAAC;;EAED;EACA,MAAMsD,WAAW,GAAGC,MAAM,CAACC,IAAI,CAAC7B,YAAY,CAAC,CAAC8B,MAAM,GAAG,CAAC;EAExD,oBACExE,OAAA,CAACtB,GAAG;IAAC+F,SAAS,EAAC,MAAM;IAAClE,QAAQ,EAAEmD,YAAa;IAACgB,UAAU;IAAAC,QAAA,EACrD3D,gBAAgB,gBACfhB,OAAA,CAACtB,GAAG;MAACkG,EAAE,EAAE;QAAEC,OAAO,EAAE,MAAM;QAAEC,cAAc,EAAE,QAAQ;QAAEC,EAAE,EAAE;MAAE,CAAE;MAAAJ,QAAA,eAC5D3E,OAAA,CAACZ,gBAAgB;QAAA4F,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACjB,CAAC,gBAENnF,OAAA,CAAAE,SAAA;MAAAyE,QAAA,GACGN,WAAW,iBACVrE,OAAA,CAAAE,SAAA;QAAAyE,QAAA,gBACE3E,OAAA,CAACb,KAAK;UACJiG,QAAQ,EAAC,SAAS;UAClBC,IAAI,eAAErF,OAAA,CAACN,WAAW;YAAAsF,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAE;UACtBP,EAAE,EAAE;YAAEU,EAAE,EAAE5E,QAAQ,GAAG,GAAG,GAAG,CAAC;YAAE6E,EAAE,EAAE7E,QAAQ,GAAG,GAAG,GAAG;UAAE,CAAE;UAAAiE,QAAA,eAEvD3E,OAAA,CAACX,UAAU;YAACmG,OAAO,EAAC,WAAW;YAACZ,EAAE,EAAE;cAAEa,QAAQ,EAAE/E,QAAQ,GAAG,QAAQ,GAAG;YAAW,CAAE;YAAAiE,QAAA,EAAC;UAEpF;YAAAK,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAY;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACR,CAAC,EAGPzC,YAAY,CAACgD,aAAa,iBACzB1F,OAAA,CAACb,KAAK;UACJiG,QAAQ,EAAC,OAAO;UAChBR,EAAE,EAAE;YAAEU,EAAE,EAAE5E,QAAQ,GAAG,GAAG,GAAG,CAAC;YAAE6E,EAAE,EAAE7E,QAAQ,GAAG,GAAG,GAAG;UAAE,CAAE;UAAAiE,QAAA,eAEvD3E,OAAA,CAACX,UAAU;YAACmG,OAAO,EAAC,WAAW;YAACZ,EAAE,EAAE;cAAEe,UAAU,EAAE;YAAO,CAAE;YAAAhB,QAAA,EACxDjC,YAAY,CAACgD;UAAa;YAAAV,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACjB;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACR,CACR;MAAA,eACD,CACH,eAEDnF,OAAA,CAACV,KAAK;QAACsF,EAAE,EAAE;UAAEgB,CAAC,EAAElF,QAAQ,GAAG,GAAG,GAAG,CAAC;UAAE4E,EAAE,EAAE5E,QAAQ,GAAG,IAAI,GAAG,CAAC;UAAEmF,SAAS,EAAEnF,QAAQ,GAAG,CAAC,GAAG;QAAE,CAAE;QAAAiE,QAAA,gBACzF3E,OAAA,CAACX,UAAU;UAACmG,OAAO,EAAC,IAAI;UAACZ,EAAE,EAAE;YAAEa,QAAQ,EAAE/E,QAAQ,GAAG,QAAQ,GAAG,SAAS;YAAE4E,EAAE,EAAE5E,QAAQ,GAAG,CAAC,GAAG,GAAG;YAAEoF,EAAE,EAAEpF,QAAQ,GAAG,GAAG,GAAG,CAAC;YAAEiF,UAAU,EAAE;UAAO,CAAE;UAAAhB,QAAA,EAAC;QAEhJ;UAAAK,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAY,CAAC,eACbnF,OAAA,CAACnB,IAAI;UAACkH,SAAS;UAACC,OAAO,EAAEtF,QAAQ,GAAG,GAAG,GAAG,CAAE;UAAAiE,QAAA,gBAC1C3E,OAAA,CAACnB,IAAI;YAACoH,IAAI;YAACC,EAAE,EAAE,EAAG;YAACC,EAAE,EAAE,CAAE;YAAAxB,QAAA,eACvB3E,OAAA,CAACrB,SAAS;cACRmE,IAAI,EAAC,SAAS;cACdsD,KAAK,EAAC,SAAS;cACfC,SAAS;cACTb,OAAO,EAAC,UAAU;cAClBzC,KAAK,EAAET,QAAQ,CAACnB,OAAQ;cACxBmF,QAAQ,EAAE1D,gBAAiB;cAC3B2D,QAAQ;cACRvC,KAAK,EAAE,CAAC,CAACxB,UAAU,CAACrB,OAAQ;cAC5BqF,UAAU,EAAEhE,UAAU,CAACrB,OAAQ;cAC/BsF,UAAU,EAAE;gBAAEC,KAAK,EAAE;kBAAEC,aAAa,EAAE;gBAAY;cAAE,CAAE;cACtDC,QAAQ,EAAExG,IAAI,KAAK,MAAO;cAC1ByG,IAAI,EAAEnG,QAAQ,GAAG,OAAO,GAAG,QAAS;cACpCoG,eAAe,EAAE;gBAAEC,MAAM,EAAE,IAAI;gBAAEL,KAAK,EAAE;kBAAEM,YAAY,EAAE;gBAAM;cAAE,CAAE;cAClEpC,EAAE,EAAE;gBAAEU,EAAE,EAAE;cAAI;YAAE;cAAAN,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACjB;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACE,CAAC,eACPnF,OAAA,CAACnB,IAAI;YAACoH,IAAI;YAACC,EAAE,EAAE,EAAG;YAACC,EAAE,EAAE,CAAE;YAAAxB,QAAA,eACvB3E,OAAA,CAACrB,SAAS;cACRmE,IAAI,EAAC,SAAS;cACdsD,KAAK,EAAC,SAAS;cACfC,SAAS;cACTb,OAAO,EAAC,UAAU;cAClBzC,KAAK,EAAET,QAAQ,CAACjB,OAAQ;cACxBiF,QAAQ,EAAE1D,gBAAiB;cAC3B2D,QAAQ;cACRvC,KAAK,EAAE,CAAC,CAACxB,UAAU,CAACnB,OAAQ;cAC5BmF,UAAU,EAAEhE,UAAU,CAACnB,OAAQ;cAC/BwF,IAAI,EAAEnG,QAAQ,GAAG,OAAO,GAAG,QAAS;cACpCoG,eAAe,EAAE;gBAAEC,MAAM,EAAE,IAAI;gBAAEL,KAAK,EAAE;kBAAEM,YAAY,EAAE;gBAAM;cAAE,CAAE;cAClEpC,EAAE,EAAE;gBAAEU,EAAE,EAAE;cAAE;YAAE;cAAAN,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACf;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACE,CAAC,eACPnF,OAAA,CAACnB,IAAI;YAACoH,IAAI;YAACC,EAAE,EAAE,EAAG;YAACC,EAAE,EAAE,CAAE;YAAAxB,QAAA,eACvB3E,OAAA,CAACrB,SAAS;cACRmE,IAAI,EAAC,SAAS;cACdsD,KAAK,EAAC,SAAS;cACfC,SAAS;cACTb,OAAO,EAAC,UAAU;cAClBzC,KAAK,EAAET,QAAQ,CAAClB,OAAQ;cACxBkF,QAAQ,EAAE1D,gBAAiB;cAC3BoB,KAAK,EAAE,CAAC,CAACxB,UAAU,CAACpB,OAAQ;cAC5BoF,UAAU,EAAEhE,UAAU,CAACpB,OAAQ;cAC/ByF,IAAI,EAAEnG,QAAQ,GAAG,OAAO,GAAG,QAAS;cACpCoG,eAAe,EAAE;gBAAEC,MAAM,EAAE,IAAI;gBAAEL,KAAK,EAAE;kBAAEM,YAAY,EAAE;gBAAM;cAAE,CAAE;cAClEpC,EAAE,EAAE;gBAAEU,EAAE,EAAE;cAAI;YAAE;cAAAN,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACjB;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACE,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACF,CAAC,eAERnF,OAAA,CAACV,KAAK;QAACsF,EAAE,EAAE;UAAEgB,CAAC,EAAElF,QAAQ,GAAG,GAAG,GAAG,CAAC;UAAE4E,EAAE,EAAE5E,QAAQ,GAAG,IAAI,GAAG,CAAC;UAAEmF,SAAS,EAAEnF,QAAQ,GAAG,CAAC,GAAG;QAAE,CAAE;QAAAiE,QAAA,gBACzF3E,OAAA,CAACX,UAAU;UAACmG,OAAO,EAAC,IAAI;UAACZ,EAAE,EAAE;YAAEa,QAAQ,EAAE/E,QAAQ,GAAG,QAAQ,GAAG,SAAS;YAAE4E,EAAE,EAAE5E,QAAQ,GAAG,CAAC,GAAG,GAAG;YAAEoF,EAAE,EAAEpF,QAAQ,GAAG,GAAG,GAAG,CAAC;YAAEiF,UAAU,EAAE;UAAO,CAAE;UAAAhB,QAAA,EAAC;QAEhJ;UAAAK,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAY,CAAC,eACbnF,OAAA,CAACnB,IAAI;UAACkH,SAAS;UAACC,OAAO,EAAEtF,QAAQ,GAAG,GAAG,GAAG,CAAE;UAAAiE,QAAA,gBAC1C3E,OAAA,CAACnB,IAAI;YAACoH,IAAI;YAACC,EAAE,EAAE,EAAG;YAACC,EAAE,EAAE,CAAE;YAAAxB,QAAA,eACvB3E,OAAA,CAACrB,SAAS;cACRmE,IAAI,EAAC,aAAa;cAClBsD,KAAK,EAAC,aAAa;cACnBC,SAAS;cACTb,OAAO,EAAC,UAAU;cAClBzC,KAAK,EAAET,QAAQ,CAAChB,WAAY;cAC5BgF,QAAQ,EAAE1D,gBAAiB;cAC3BoB,KAAK,EAAE,CAAC,CAACxB,UAAU,CAAClB,WAAY;cAChCkF,UAAU,EAAEhE,UAAU,CAAClB,WAAY;cACnCuF,IAAI,EAAEnG,QAAQ,GAAG,OAAO,GAAG,QAAS;cACpCoG,eAAe,EAAE;gBAAEC,MAAM,EAAE,IAAI;gBAAEL,KAAK,EAAE;kBAAEM,YAAY,EAAE;gBAAM;cAAE,CAAE;cAClEpC,EAAE,EAAE;gBAAEU,EAAE,EAAE;cAAE;YAAE;cAAAN,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACf;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACE,CAAC,eACPnF,OAAA,CAACnB,IAAI;YAACoH,IAAI;YAACC,EAAE,EAAE,EAAG;YAACC,EAAE,EAAE,CAAE;YAAAxB,QAAA,eACvB3E,OAAA,CAACrB,SAAS;cACRmE,IAAI,EAAC,WAAW;cAChBsD,KAAK,EAAC,WAAW;cACjBC,SAAS;cACTb,OAAO,EAAC,UAAU;cAClBzC,KAAK,EAAET,QAAQ,CAACf,SAAU;cAC1B+E,QAAQ,EAAE1D,gBAAiB;cAC3BoB,KAAK,EAAE,CAAC,CAACxB,UAAU,CAACjB,SAAU;cAC9BiF,UAAU,EAAEhE,UAAU,CAACjB,SAAU;cACjCsF,IAAI,EAAEnG,QAAQ,GAAG,OAAO,GAAG,QAAS;cACpCoG,eAAe,EAAE;gBAAEC,MAAM,EAAE,IAAI;gBAAEL,KAAK,EAAE;kBAAEM,YAAY,EAAE;gBAAM;cAAE,CAAE;cAClEpC,EAAE,EAAE;gBAAEU,EAAE,EAAE;cAAE;YAAE;cAAAN,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACf;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACE,CAAC,eACPnF,OAAA,CAACnB,IAAI;YAACoH,IAAI;YAACC,EAAE,EAAE,EAAG;YAACC,EAAE,EAAE,CAAE;YAAAxB,QAAA,eACvB3E,OAAA,CAACrB,SAAS;cACRmE,IAAI,EAAC,cAAc;cACnBsD,KAAK,EAAC,mBAAmB;cACzBC,SAAS;cACTb,OAAO,EAAC,UAAU;cAClBzC,KAAK,EAAET,QAAQ,CAACd,YAAa;cAC7B8E,QAAQ,EAAE1D,gBAAiB;cAC3BoB,KAAK,EAAE,CAAC,CAACxB,UAAU,CAAChB,YAAa;cACjCgF,UAAU,EAAEhE,UAAU,CAAChB,YAAY,IAAIkB,YAAY,CAAClB,YAAa;cACjEyF,mBAAmB,EAAE;gBACnBP,KAAK,EAAE;kBAAEQ,KAAK,EAAExE,YAAY,CAAClB,YAAY,GAAG,QAAQ,GAAG2F;gBAAU;cACnE,CAAE;cACFN,IAAI,EAAEnG,QAAQ,GAAG,OAAO,GAAG,QAAS;cACpCoG,eAAe,EAAE;gBAAEC,MAAM,EAAE,IAAI;gBAAEL,KAAK,EAAE;kBAAEM,YAAY,EAAE;gBAAM;cAAE,CAAE;cAClEpC,EAAE,EAAE;gBAAEU,EAAE,EAAE;cAAE;YAAE;cAAAN,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACf;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACE,CAAC,eACPnF,OAAA,CAACnB,IAAI;YAACoH,IAAI;YAACC,EAAE,EAAE,EAAG;YAACC,EAAE,EAAE,CAAE;YAAAxB,QAAA,eACvB3E,OAAA,CAACrB,SAAS;cACRmE,IAAI,EAAC,SAAS;cACdsD,KAAK,EAAC,SAAS;cACfC,SAAS;cACTb,OAAO,EAAC,UAAU;cAClBzC,KAAK,EAAET,QAAQ,CAACb,OAAQ;cACxB6E,QAAQ,EAAE1D,gBAAiB;cAC3BoB,KAAK,EAAE,CAAC,CAACxB,UAAU,CAACf,OAAQ;cAC5B+E,UAAU,EAAEhE,UAAU,CAACf,OAAO,IAAIiB,YAAY,CAACjB,OAAQ;cACvDwF,mBAAmB,EAAE;gBACnBP,KAAK,EAAE;kBAAEQ,KAAK,EAAExE,YAAY,CAACjB,OAAO,GAAG,QAAQ,GAAG0F;gBAAU;cAC9D,CAAE;cACFN,IAAI,EAAEnG,QAAQ,GAAG,OAAO,GAAG,QAAS;cACpCoG,eAAe,EAAE;gBAAEC,MAAM,EAAE,IAAI;gBAAEL,KAAK,EAAE;kBAAEM,YAAY,EAAE;gBAAM;cAAE,CAAE;cAClEpC,EAAE,EAAE;gBAAEU,EAAE,EAAE;cAAE;YAAE;cAAAN,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACf;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACE,CAAC,eACPnF,OAAA,CAACnB,IAAI;YAACoH,IAAI;YAACC,EAAE,EAAE,EAAG;YAACC,EAAE,EAAE,CAAE;YAAAxB,QAAA,eACvB3E,OAAA,CAACrB,SAAS;cACRyI,MAAM;cACNtE,IAAI,EAAC,IAAI;cACTsD,KAAK,EAAC,iBAAiB;cACvBC,SAAS;cACTb,OAAO,EAAC,UAAU;cAClBzC,KAAK,EAAET,QAAQ,CAACZ,EAAE,IAAI,GAAI;cAC1B4E,QAAQ,EAAE1D,gBAAiB;cAC3BoB,KAAK,EAAE,CAAC,CAACxB,UAAU,CAACd,EAAG;cACvB8E,UAAU,EAAEhE,UAAU,CAACd,EAAE,IAAI,wBAAyB;cACtDmF,IAAI,EAAEnG,QAAQ,GAAG,OAAO,GAAG,QAAS;cACpCoG,eAAe,EAAE;gBAAEC,MAAM,EAAE,IAAI;gBAAEL,KAAK,EAAE;kBAAEM,YAAY,EAAE;gBAAM;cAAE,CAAE;cAClEpC,EAAE,EAAE;gBAAEU,EAAE,EAAE;cAAI,CAAE;cAAAX,QAAA,gBAEhB3E,OAAA,CAACf,QAAQ;gBAAC8D,KAAK,EAAC,GAAG;gBAAA4B,QAAA,EAAC;cAAC;gBAAAK,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAU,CAAC,eAChCnF,OAAA,CAACf,QAAQ;gBAAC8D,KAAK,EAAC,GAAG;gBAAA4B,QAAA,EAAC;cAAC;gBAAAK,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAU,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACvB;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACR,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACF,CAAC,eAERnF,OAAA,CAACV,KAAK;QAACsF,EAAE,EAAE;UAAEgB,CAAC,EAAElF,QAAQ,GAAG,GAAG,GAAG,CAAC;UAAE4E,EAAE,EAAE5E,QAAQ,GAAG,IAAI,GAAG,CAAC;UAAEmF,SAAS,EAAEnF,QAAQ,GAAG,CAAC,GAAG;QAAE,CAAE;QAAAiE,QAAA,gBACzF3E,OAAA,CAACX,UAAU;UAACmG,OAAO,EAAC,IAAI;UAACZ,EAAE,EAAE;YAAEa,QAAQ,EAAE/E,QAAQ,GAAG,QAAQ,GAAG,SAAS;YAAE4E,EAAE,EAAE5E,QAAQ,GAAG,CAAC,GAAG,GAAG;YAAEoF,EAAE,EAAEpF,QAAQ,GAAG,GAAG,GAAG,CAAC;YAAEiF,UAAU,EAAE;UAAO,CAAE;UAAAhB,QAAA,EAAC;QAEhJ;UAAAK,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAY,CAAC,eACbnF,OAAA,CAACnB,IAAI;UAACkH,SAAS;UAACC,OAAO,EAAEtF,QAAQ,GAAG,GAAG,GAAG,CAAE;UAAAiE,QAAA,gBAC1C3E,OAAA,CAACnB,IAAI;YAACoH,IAAI;YAACC,EAAE,EAAE,EAAG;YAACC,EAAE,EAAE,CAAE;YAAAxB,QAAA,eACvB3E,OAAA,CAACrB,SAAS;cACRmE,IAAI,EAAC,qBAAqB;cAC1BsD,KAAK,EAAC,qBAAqB;cAC3BC,SAAS;cACTb,OAAO,EAAC,UAAU;cAClBzC,KAAK,EAAET,QAAQ,CAACX,mBAAoB;cACpC2E,QAAQ,EAAE1D,gBAAiB;cAC3BoB,KAAK,EAAE,CAAC,CAACxB,UAAU,CAACb,mBAAoB;cACxC6E,UAAU,EAAEhE,UAAU,CAACb,mBAAoB;cAC3CkF,IAAI,EAAEnG,QAAQ,GAAG,OAAO,GAAG,QAAS;cACpCoG,eAAe,EAAE;gBAAEC,MAAM,EAAE,IAAI;gBAAEL,KAAK,EAAE;kBAAEM,YAAY,EAAE;gBAAM;cAAE,CAAE;cAClEpC,EAAE,EAAE;gBAAEU,EAAE,EAAE;cAAE;YAAE;cAAAN,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACf;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACE,CAAC,eACPnF,OAAA,CAACnB,IAAI;YAACoH,IAAI;YAACC,EAAE,EAAE,EAAG;YAACC,EAAE,EAAE,CAAE;YAAAxB,QAAA,eACvB3E,OAAA,CAACrB,SAAS;cACRmE,IAAI,EAAC,iBAAiB;cACtBsD,KAAK,EAAC,iBAAiB;cACvBC,SAAS;cACTb,OAAO,EAAC,UAAU;cAClBzC,KAAK,EAAET,QAAQ,CAACV,eAAgB;cAChC0E,QAAQ,EAAE1D,gBAAiB;cAC3BoB,KAAK,EAAE,CAAC,CAACxB,UAAU,CAACZ,eAAgB;cACpC4E,UAAU,EAAEhE,UAAU,CAACZ,eAAgB;cACvCiF,IAAI,EAAEnG,QAAQ,GAAG,OAAO,GAAG,QAAS;cACpCoG,eAAe,EAAE;gBAAEC,MAAM,EAAE,IAAI;gBAAEL,KAAK,EAAE;kBAAEM,YAAY,EAAE;gBAAM;cAAE,CAAE;cAClEpC,EAAE,EAAE;gBAAEU,EAAE,EAAE;cAAI;YAAE;cAAAN,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACjB;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACE,CAAC,eACPnF,OAAA,CAACnB,IAAI;YAACoH,IAAI;YAACC,EAAE,EAAE,EAAG;YAACC,EAAE,EAAE,CAAE;YAAAxB,QAAA,eACvB3E,OAAA,CAACrB,SAAS;cACRmE,IAAI,EAAC,6BAA6B;cAClCsD,KAAK,EAAC,6BAA6B;cACnCC,SAAS;cACTb,OAAO,EAAC,UAAU;cAClBzC,KAAK,EAAET,QAAQ,CAACT,2BAA4B;cAC5CyE,QAAQ,EAAE1D,gBAAiB;cAC3BoB,KAAK,EAAE,CAAC,CAACxB,UAAU,CAACX,2BAA4B;cAChD2E,UAAU,EAAEhE,UAAU,CAACX,2BAA4B;cACnDgF,IAAI,EAAEnG,QAAQ,GAAG,OAAO,GAAG,QAAS;cACpCoG,eAAe,EAAE;gBAAEC,MAAM,EAAE,IAAI;gBAAEL,KAAK,EAAE;kBAAEM,YAAY,EAAE;gBAAM;cAAE,CAAE;cAClEpC,EAAE,EAAE;gBAAEU,EAAE,EAAE;cAAI;YAAE;cAAAN,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACjB;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACE,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACF,CAAC,eAERnF,OAAA,CAACV,KAAK;QAACsF,EAAE,EAAE;UAAEgB,CAAC,EAAElF,QAAQ,GAAG,GAAG,GAAG,CAAC;UAAE4E,EAAE,EAAE5E,QAAQ,GAAG,IAAI,GAAG,CAAC;UAAEmF,SAAS,EAAEnF,QAAQ,GAAG,CAAC,GAAG;QAAE,CAAE;QAAAiE,QAAA,gBACzF3E,OAAA,CAACX,UAAU;UAACmG,OAAO,EAAC,IAAI;UAACZ,EAAE,EAAE;YAAEa,QAAQ,EAAE/E,QAAQ,GAAG,QAAQ,GAAG,SAAS;YAAE4E,EAAE,EAAE5E,QAAQ,GAAG,CAAC,GAAG,GAAG;YAAEoF,EAAE,EAAEpF,QAAQ,GAAG,GAAG,GAAG,CAAC;YAAEiF,UAAU,EAAE;UAAO,CAAE;UAAAhB,QAAA,EAAC;QAEhJ;UAAAK,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAY,CAAC,eACbnF,OAAA,CAACnB,IAAI;UAACkH,SAAS;UAACC,OAAO,EAAEtF,QAAQ,GAAG,GAAG,GAAG,CAAE;UAAAiE,QAAA,gBAC1C3E,OAAA,CAACnB,IAAI;YAACoH,IAAI;YAACC,EAAE,EAAE,EAAG;YAACC,EAAE,EAAE,CAAE;YAAAxB,QAAA,eACvB3E,OAAA,CAACrB,SAAS;cACRmE,IAAI,EAAC,mBAAmB;cACxBsD,KAAK,EAAC,mBAAmB;cACzBC,SAAS;cACTb,OAAO,EAAC,UAAU;cAClBzC,KAAK,EAAET,QAAQ,CAACR,iBAAkB;cAClCwE,QAAQ,EAAE1D,gBAAiB;cAC3BoB,KAAK,EAAE,CAAC,CAACxB,UAAU,CAACV,iBAAkB;cACtC0E,UAAU,EAAEhE,UAAU,CAACV,iBAAkB;cACzC+E,IAAI,EAAEnG,QAAQ,GAAG,OAAO,GAAG,QAAS;cACpCoG,eAAe,EAAE;gBAAEC,MAAM,EAAE,IAAI;gBAAEL,KAAK,EAAE;kBAAEM,YAAY,EAAE;gBAAM;cAAE,CAAE;cAClEpC,EAAE,EAAE;gBAAEU,EAAE,EAAE;cAAI;YAAE;cAAAN,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACjB;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACE,CAAC,eACPnF,OAAA,CAACnB,IAAI;YAACoH,IAAI;YAACC,EAAE,EAAE,EAAG;YAACC,EAAE,EAAE,CAAE;YAAAxB,QAAA,eACvB3E,OAAA,CAACrB,SAAS;cACRmE,IAAI,EAAC,eAAe;cACpBsD,KAAK,EAAC,eAAe;cACrBC,SAAS;cACTb,OAAO,EAAC,UAAU;cAClBzC,KAAK,EAAET,QAAQ,CAACP,aAAc;cAC9BuE,QAAQ,EAAE1D,gBAAiB;cAC3BoB,KAAK,EAAE,CAAC,CAACxB,UAAU,CAACT,aAAc;cAClCyE,UAAU,EAAEhE,UAAU,CAACT,aAAc;cACrC8E,IAAI,EAAEnG,QAAQ,GAAG,OAAO,GAAG,QAAS;cACpCoG,eAAe,EAAE;gBAAEC,MAAM,EAAE,IAAI;gBAAEL,KAAK,EAAE;kBAAEM,YAAY,EAAE;gBAAM;cAAE,CAAE;cAClEpC,EAAE,EAAE;gBAAEU,EAAE,EAAE;cAAI;YAAE;cAAAN,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACjB;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACE,CAAC,eACPnF,OAAA,CAACnB,IAAI;YAACoH,IAAI;YAACC,EAAE,EAAE,EAAG;YAACC,EAAE,EAAE,CAAE;YAAAxB,QAAA,eACvB3E,OAAA,CAACrB,SAAS;cACRmE,IAAI,EAAC,2BAA2B;cAChCsD,KAAK,EAAC,2BAA2B;cACjCC,SAAS;cACTb,OAAO,EAAC,UAAU;cAClBzC,KAAK,EAAET,QAAQ,CAACN,yBAA0B;cAC1CsE,QAAQ,EAAE1D,gBAAiB;cAC3BoB,KAAK,EAAE,CAAC,CAACxB,UAAU,CAACR,yBAA0B;cAC9CwE,UAAU,EAAEhE,UAAU,CAACR,yBAA0B;cACjD6E,IAAI,EAAEnG,QAAQ,GAAG,OAAO,GAAG,QAAS;cACpCoG,eAAe,EAAE;gBAAEC,MAAM,EAAE,IAAI;gBAAEL,KAAK,EAAE;kBAAEM,YAAY,EAAE;gBAAM;cAAE,CAAE;cAClEpC,EAAE,EAAE;gBAAEU,EAAE,EAAE;cAAI;YAAE;cAAAN,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACjB;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACE,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACF,CAAC,eAERnF,OAAA,CAACV,KAAK;QAACsF,EAAE,EAAE;UAAEgB,CAAC,EAAElF,QAAQ,GAAG,GAAG,GAAG,CAAC;UAAE4E,EAAE,EAAE5E,QAAQ,GAAG,IAAI,GAAG,CAAC;UAAEmF,SAAS,EAAEnF,QAAQ,GAAG,CAAC,GAAG;QAAE,CAAE;QAAAiE,QAAA,gBACzF3E,OAAA,CAACX,UAAU;UAACmG,OAAO,EAAC,IAAI;UAACZ,EAAE,EAAE;YAAEa,QAAQ,EAAE/E,QAAQ,GAAG,QAAQ,GAAG,SAAS;YAAE4E,EAAE,EAAE5E,QAAQ,GAAG,CAAC,GAAG,GAAG;YAAEoF,EAAE,EAAEpF,QAAQ,GAAG,GAAG,GAAG,CAAC;YAAEiF,UAAU,EAAE;UAAO,CAAE;UAAAhB,QAAA,EAAC;QAEhJ;UAAAK,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAY,CAAC,eACbnF,OAAA,CAACnB,IAAI;UAACkH,SAAS;UAACC,OAAO,EAAEtF,QAAQ,GAAG,GAAG,GAAG,CAAE;UAAAiE,QAAA,eAC1C3E,OAAA,CAACnB,IAAI;YAACoH,IAAI;YAACC,EAAE,EAAE,EAAG;YAACC,EAAE,EAAE,CAAE;YAAAxB,QAAA,eACvB3E,OAAA,CAACrB,SAAS;cACRmE,IAAI,EAAC,eAAe;cACpBsD,KAAK,EAAC,eAAe;cACrBC,SAAS;cACTb,OAAO,EAAC,UAAU;cAClBzC,KAAK,EAAET,QAAQ,CAACL,aAAc;cAC9BqE,QAAQ,EAAE1D,gBAAiB;cAC3B2D,QAAQ;cACRvC,KAAK,EAAE,CAAC,CAACxB,UAAU,CAACP,aAAc;cAClCuE,UAAU,EAAEhE,UAAU,CAACP,aAAc;cACrC4E,IAAI,EAAEnG,QAAQ,GAAG,OAAO,GAAG,QAAS;cACpCoG,eAAe,EAAE;gBAAEC,MAAM,EAAE,IAAI;gBAAEL,KAAK,EAAE;kBAAEM,YAAY,EAAE;gBAAM;cAAE,CAAE;cAClEpC,EAAE,EAAE;gBAAEU,EAAE,EAAE;cAAE;YAAE;cAAAN,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACf;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACE;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACF,CAAC,EAEP,CAACzE,QAAQ,iBACRV,OAAA,CAACtB,GAAG;QAACkG,EAAE,EAAE;UAAEkB,EAAE,EAAE,CAAC;UAAEjB,OAAO,EAAE,MAAM;UAAEC,cAAc,EAAE,QAAQ;UAAEuC,GAAG,EAAE;QAAE,CAAE;QAAA1C,QAAA,gBACpE3E,OAAA,CAACpB,MAAM;UACL4G,OAAO,EAAC,UAAU;UAClB0B,KAAK,EAAC,WAAW;UACjBL,IAAI,EAAC,OAAO;UACZS,OAAO,EAAE7D,YAAa;UACtBmD,QAAQ,EAAE9F,OAAQ;UAClB8D,EAAE,EAAE;YAAE2C,QAAQ,EAAE;UAAI,CAAE;UAAA5C,QAAA,EACvB;QAED;UAAAK,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC,eACTnF,OAAA,CAACpB,MAAM;UACL4I,IAAI,EAAC,QAAQ;UACbhC,OAAO,EAAC,WAAW;UACnB0B,KAAK,EAAC,SAAS;UACfL,IAAI,EAAC,OAAO;UACZY,SAAS,eAAEzH,OAAA,CAACR,QAAQ;YAAAwF,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAE;UACxByB,QAAQ,EAAE9F,OAAQ;UAClB8D,EAAE,EAAE;YAAE2C,QAAQ,EAAE;UAAI,CAAE;UAAA5C,QAAA,EAErB7D,OAAO,gBAAGd,OAAA,CAACZ,gBAAgB;YAACyH,IAAI,EAAE;UAAG;YAAA7B,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,GAAG/E,IAAI,KAAK,KAAK,GAAG,YAAY,GAAG;QAAiB;UAAA4E,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACvF,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACN,CACN,EAEAzE,QAAQ,iBACPV,OAAA,CAACtB,GAAG;QAACkG,EAAE,EAAE;UAAEkB,EAAE,EAAE,CAAC;UAAEjB,OAAO,EAAE,MAAM;UAAEC,cAAc,EAAE,QAAQ;UAAEuC,GAAG,EAAE;QAAE,CAAE;QAAA1C,QAAA,gBACpE3E,OAAA,CAACpB,MAAM;UACL4G,OAAO,EAAC,UAAU;UAClB0B,KAAK,EAAC,WAAW;UACjBL,IAAI,EAAC,OAAO;UACZS,OAAO,EAAE7D,YAAa;UACtBmD,QAAQ,EAAE9F,OAAQ;UAClB8D,EAAE,EAAE;YAAE2C,QAAQ,EAAE;UAAI,CAAE;UAAA5C,QAAA,EACvB;QAED;UAAAK,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC,eACTnF,OAAA,CAACpB,MAAM;UACL4I,IAAI,EAAC,QAAQ;UACbhC,OAAO,EAAC,WAAW;UACnB0B,KAAK,EAAC,SAAS;UACfL,IAAI,EAAC,OAAO;UACZY,SAAS,eAAEzH,OAAA,CAACR,QAAQ;YAAAwF,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAE;UACxByB,QAAQ,EAAE9F,OAAQ;UAClB8D,EAAE,EAAE;YAAE2C,QAAQ,EAAE;UAAI,CAAE;UAAA5C,QAAA,EAErB7D,OAAO,gBAAGd,OAAA,CAACZ,gBAAgB;YAACyH,IAAI,EAAE;UAAG;YAAA7B,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,GAAG/E,IAAI,KAAK,KAAK,GAAG,YAAY,GAAG;QAAiB;UAAA4E,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACvF,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACN,CACN;IAAA,eACD;EACH;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACE,CAAC;AAEV,CAAC;AAACvE,EAAA,CArgBIT,QAAQ;EAAA,QAUKR,WAAW;AAAA;AAAA+H,EAAA,GAVxBvH,QAAQ;AAugBd,eAAeA,QAAQ;AAAC,IAAAuH,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}