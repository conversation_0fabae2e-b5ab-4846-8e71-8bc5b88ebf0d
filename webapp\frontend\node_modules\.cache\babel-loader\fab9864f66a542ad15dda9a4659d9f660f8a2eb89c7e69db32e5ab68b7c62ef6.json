{"ast": null, "code": "import _extends from \"@babel/runtime/helpers/esm/extends\";\nimport _toPropertyKey from \"@babel/runtime/helpers/esm/toPropertyKey\";\nimport _objectWithoutPropertiesLoose from \"@babel/runtime/helpers/esm/objectWithoutPropertiesLoose\";\nconst _excluded = [\"colorSchemes\", \"components\", \"defaultColorScheme\"];\nimport deepmerge from '@mui/utils/deepmerge';\nimport cssVarsParser from './cssVarsParser';\nfunction prepareCssVars(theme, parserConfig) {\n  // @ts-ignore - ignore components do not exist\n  const {\n      colorSchemes = {},\n      defaultColorScheme = 'light'\n    } = theme,\n    otherTheme = _objectWithoutPropertiesLoose(theme, _excluded);\n  const {\n    vars: rootVars,\n    css: rootCss,\n    varsWithDefaults: rootVarsWithDefaults\n  } = cssVarsParser(otherTheme, parserConfig);\n  let themeVars = rootVarsWithDefaults;\n  const colorSchemesMap = {};\n  const {\n      [defaultColorScheme]: light\n    } = colorSchemes,\n    otherColorSchemes = _objectWithoutPropertiesLoose(colorSchemes, [defaultColorScheme].map(_toPropertyKey));\n  Object.entries(otherColorSchemes || {}).forEach(_ref => {\n    let [key, scheme] = _ref;\n    const {\n      vars,\n      css,\n      varsWithDefaults\n    } = cssVarsParser(scheme, parserConfig);\n    themeVars = deepmerge(themeVars, varsWithDefaults);\n    colorSchemesMap[key] = {\n      css,\n      vars\n    };\n  });\n  if (light) {\n    // default color scheme vars should be merged last to set as default\n    const {\n      css,\n      vars,\n      varsWithDefaults\n    } = cssVarsParser(light, parserConfig);\n    themeVars = deepmerge(themeVars, varsWithDefaults);\n    colorSchemesMap[defaultColorScheme] = {\n      css,\n      vars\n    };\n  }\n  const generateCssVars = colorScheme => {\n    var _parserConfig$getSele2;\n    if (!colorScheme) {\n      var _parserConfig$getSele;\n      const css = _extends({}, rootCss);\n      return {\n        css,\n        vars: rootVars,\n        selector: (parserConfig == null || (_parserConfig$getSele = parserConfig.getSelector) == null ? void 0 : _parserConfig$getSele.call(parserConfig, colorScheme, css)) || ':root'\n      };\n    }\n    const css = _extends({}, colorSchemesMap[colorScheme].css);\n    return {\n      css,\n      vars: colorSchemesMap[colorScheme].vars,\n      selector: (parserConfig == null || (_parserConfig$getSele2 = parserConfig.getSelector) == null ? void 0 : _parserConfig$getSele2.call(parserConfig, colorScheme, css)) || ':root'\n    };\n  };\n  return {\n    vars: themeVars,\n    generateCssVars\n  };\n}\nexport default prepareCssVars;", "map": {"version": 3, "names": ["_extends", "_to<PERSON><PERSON><PERSON><PERSON><PERSON>", "_objectWithoutPropertiesLoose", "_excluded", "deepmerge", "cssVarsParser", "prepareCssVars", "theme", "parserConfig", "colorSchemes", "defaultColorScheme", "otherTheme", "vars", "rootVars", "css", "rootCss", "varsWithDefaults", "rootVarsWithDefaults", "themeVars", "colorSchemesMap", "light", "otherColorSchemes", "map", "Object", "entries", "for<PERSON>ach", "_ref", "key", "scheme", "generateCssVars", "colorScheme", "_parserConfig$getSele2", "_parserConfig$getSele", "selector", "getSelector", "call"], "sources": ["C:/Users/<USER>/PycharmProjects/CMS/webapp/frontend/node_modules/@mui/system/esm/cssVars/prepareCssVars.js"], "sourcesContent": ["import _extends from \"@babel/runtime/helpers/esm/extends\";\nimport _toPropertyKey from \"@babel/runtime/helpers/esm/toPropertyKey\";\nimport _objectWithoutPropertiesLoose from \"@babel/runtime/helpers/esm/objectWithoutPropertiesLoose\";\nconst _excluded = [\"colorSchemes\", \"components\", \"defaultColorScheme\"];\nimport deepmerge from '@mui/utils/deepmerge';\nimport cssVarsParser from './cssVarsParser';\nfunction prepareCssVars(theme, parserConfig) {\n  // @ts-ignore - ignore components do not exist\n  const {\n      colorSchemes = {},\n      defaultColorScheme = 'light'\n    } = theme,\n    otherTheme = _objectWithoutPropertiesLoose(theme, _excluded);\n  const {\n    vars: rootVars,\n    css: rootCss,\n    varsWithDefaults: rootVarsWithDefaults\n  } = cssVarsParser(otherTheme, parserConfig);\n  let themeVars = rootVarsWithDefaults;\n  const colorSchemesMap = {};\n  const {\n      [defaultColorScheme]: light\n    } = colorSchemes,\n    otherColorSchemes = _objectWithoutPropertiesLoose(colorSchemes, [defaultColorScheme].map(_toPropertyKey));\n  Object.entries(otherColorSchemes || {}).forEach(([key, scheme]) => {\n    const {\n      vars,\n      css,\n      varsWithDefaults\n    } = cssVarsParser(scheme, parserConfig);\n    themeVars = deepmerge(themeVars, varsWithDefaults);\n    colorSchemesMap[key] = {\n      css,\n      vars\n    };\n  });\n  if (light) {\n    // default color scheme vars should be merged last to set as default\n    const {\n      css,\n      vars,\n      varsWithDefaults\n    } = cssVarsParser(light, parserConfig);\n    themeVars = deepmerge(themeVars, varsWithDefaults);\n    colorSchemesMap[defaultColorScheme] = {\n      css,\n      vars\n    };\n  }\n  const generateCssVars = colorScheme => {\n    var _parserConfig$getSele2;\n    if (!colorScheme) {\n      var _parserConfig$getSele;\n      const css = _extends({}, rootCss);\n      return {\n        css,\n        vars: rootVars,\n        selector: (parserConfig == null || (_parserConfig$getSele = parserConfig.getSelector) == null ? void 0 : _parserConfig$getSele.call(parserConfig, colorScheme, css)) || ':root'\n      };\n    }\n    const css = _extends({}, colorSchemesMap[colorScheme].css);\n    return {\n      css,\n      vars: colorSchemesMap[colorScheme].vars,\n      selector: (parserConfig == null || (_parserConfig$getSele2 = parserConfig.getSelector) == null ? void 0 : _parserConfig$getSele2.call(parserConfig, colorScheme, css)) || ':root'\n    };\n  };\n  return {\n    vars: themeVars,\n    generateCssVars\n  };\n}\nexport default prepareCssVars;"], "mappings": "AAAA,OAAOA,QAAQ,MAAM,oCAAoC;AACzD,OAAOC,cAAc,MAAM,0CAA0C;AACrE,OAAOC,6BAA6B,MAAM,yDAAyD;AACnG,MAAMC,SAAS,GAAG,CAAC,cAAc,EAAE,YAAY,EAAE,oBAAoB,CAAC;AACtE,OAAOC,SAAS,MAAM,sBAAsB;AAC5C,OAAOC,aAAa,MAAM,iBAAiB;AAC3C,SAASC,cAAcA,CAACC,KAAK,EAAEC,YAAY,EAAE;EAC3C;EACA,MAAM;MACFC,YAAY,GAAG,CAAC,CAAC;MACjBC,kBAAkB,GAAG;IACvB,CAAC,GAAGH,KAAK;IACTI,UAAU,GAAGT,6BAA6B,CAACK,KAAK,EAAEJ,SAAS,CAAC;EAC9D,MAAM;IACJS,IAAI,EAAEC,QAAQ;IACdC,GAAG,EAAEC,OAAO;IACZC,gBAAgB,EAAEC;EACpB,CAAC,GAAGZ,aAAa,CAACM,UAAU,EAAEH,YAAY,CAAC;EAC3C,IAAIU,SAAS,GAAGD,oBAAoB;EACpC,MAAME,eAAe,GAAG,CAAC,CAAC;EAC1B,MAAM;MACF,CAACT,kBAAkB,GAAGU;IACxB,CAAC,GAAGX,YAAY;IAChBY,iBAAiB,GAAGnB,6BAA6B,CAACO,YAAY,EAAE,CAACC,kBAAkB,CAAC,CAACY,GAAG,CAACrB,cAAc,CAAC,CAAC;EAC3GsB,MAAM,CAACC,OAAO,CAACH,iBAAiB,IAAI,CAAC,CAAC,CAAC,CAACI,OAAO,CAACC,IAAA,IAAmB;IAAA,IAAlB,CAACC,GAAG,EAAEC,MAAM,CAAC,GAAAF,IAAA;IAC5D,MAAM;MACJd,IAAI;MACJE,GAAG;MACHE;IACF,CAAC,GAAGX,aAAa,CAACuB,MAAM,EAAEpB,YAAY,CAAC;IACvCU,SAAS,GAAGd,SAAS,CAACc,SAAS,EAAEF,gBAAgB,CAAC;IAClDG,eAAe,CAACQ,GAAG,CAAC,GAAG;MACrBb,GAAG;MACHF;IACF,CAAC;EACH,CAAC,CAAC;EACF,IAAIQ,KAAK,EAAE;IACT;IACA,MAAM;MACJN,GAAG;MACHF,IAAI;MACJI;IACF,CAAC,GAAGX,aAAa,CAACe,KAAK,EAAEZ,YAAY,CAAC;IACtCU,SAAS,GAAGd,SAAS,CAACc,SAAS,EAAEF,gBAAgB,CAAC;IAClDG,eAAe,CAACT,kBAAkB,CAAC,GAAG;MACpCI,GAAG;MACHF;IACF,CAAC;EACH;EACA,MAAMiB,eAAe,GAAGC,WAAW,IAAI;IACrC,IAAIC,sBAAsB;IAC1B,IAAI,CAACD,WAAW,EAAE;MAChB,IAAIE,qBAAqB;MACzB,MAAMlB,GAAG,GAAGd,QAAQ,CAAC,CAAC,CAAC,EAAEe,OAAO,CAAC;MACjC,OAAO;QACLD,GAAG;QACHF,IAAI,EAAEC,QAAQ;QACdoB,QAAQ,EAAE,CAACzB,YAAY,IAAI,IAAI,IAAI,CAACwB,qBAAqB,GAAGxB,YAAY,CAAC0B,WAAW,KAAK,IAAI,GAAG,KAAK,CAAC,GAAGF,qBAAqB,CAACG,IAAI,CAAC3B,YAAY,EAAEsB,WAAW,EAAEhB,GAAG,CAAC,KAAK;MAC1K,CAAC;IACH;IACA,MAAMA,GAAG,GAAGd,QAAQ,CAAC,CAAC,CAAC,EAAEmB,eAAe,CAACW,WAAW,CAAC,CAAChB,GAAG,CAAC;IAC1D,OAAO;MACLA,GAAG;MACHF,IAAI,EAAEO,eAAe,CAACW,WAAW,CAAC,CAAClB,IAAI;MACvCqB,QAAQ,EAAE,CAACzB,YAAY,IAAI,IAAI,IAAI,CAACuB,sBAAsB,GAAGvB,YAAY,CAAC0B,WAAW,KAAK,IAAI,GAAG,KAAK,CAAC,GAAGH,sBAAsB,CAACI,IAAI,CAAC3B,YAAY,EAAEsB,WAAW,EAAEhB,GAAG,CAAC,KAAK;IAC5K,CAAC;EACH,CAAC;EACD,OAAO;IACLF,IAAI,EAAEM,SAAS;IACfW;EACF,CAAC;AACH;AACA,eAAevB,cAAc", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}