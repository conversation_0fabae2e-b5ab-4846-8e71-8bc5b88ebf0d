{"ast": null, "code": "'use client';\n\nimport _extends from \"@babel/runtime/helpers/esm/extends\";\nimport * as React from 'react';\nimport PropTypes from 'prop-types';\nimport { GlobalStyles as SystemGlobalStyles } from '@mui/system';\nimport defaultTheme from '../styles/defaultTheme';\nimport THEME_ID from '../styles/identifier';\nimport { jsx as _jsx } from \"react/jsx-runtime\";\nfunction GlobalStyles(props) {\n  return /*#__PURE__*/_jsx(SystemGlobalStyles, _extends({}, props, {\n    defaultTheme: defaultTheme,\n    themeId: THEME_ID\n  }));\n}\nprocess.env.NODE_ENV !== \"production\" ? GlobalStyles.propTypes /* remove-proptypes */ = {\n  // ┌────────────────────────────── Warning ──────────────────────────────┐\n  // │ These PropTypes are generated from the TypeScript type definitions. │\n  // │    To update them, edit the d.ts file and run `pnpm proptypes`.     │\n  // └─────────────────────────────────────────────────────────────────────┘\n  /**\n   * The styles you want to apply globally.\n   */\n  styles: PropTypes /* @typescript-to-proptypes-ignore */.oneOfType([PropTypes.array, PropTypes.func, PropTypes.number, PropTypes.object, PropTypes.string, PropTypes.bool])\n} : void 0;\nexport default GlobalStyles;", "map": {"version": 3, "names": ["_extends", "React", "PropTypes", "GlobalStyles", "SystemGlobalStyles", "defaultTheme", "THEME_ID", "jsx", "_jsx", "props", "themeId", "process", "env", "NODE_ENV", "propTypes", "styles", "oneOfType", "array", "func", "number", "object", "string", "bool"], "sources": ["C:/Users/<USER>/PycharmProjects/CMS/webapp/frontend/node_modules/@mui/material/GlobalStyles/GlobalStyles.js"], "sourcesContent": ["'use client';\n\nimport _extends from \"@babel/runtime/helpers/esm/extends\";\nimport * as React from 'react';\nimport PropTypes from 'prop-types';\nimport { GlobalStyles as SystemGlobalStyles } from '@mui/system';\nimport defaultTheme from '../styles/defaultTheme';\nimport THEME_ID from '../styles/identifier';\nimport { jsx as _jsx } from \"react/jsx-runtime\";\nfunction GlobalStyles(props) {\n  return /*#__PURE__*/_jsx(SystemGlobalStyles, _extends({}, props, {\n    defaultTheme: defaultTheme,\n    themeId: THEME_ID\n  }));\n}\nprocess.env.NODE_ENV !== \"production\" ? GlobalStyles.propTypes /* remove-proptypes */ = {\n  // ┌────────────────────────────── Warning ──────────────────────────────┐\n  // │ These PropTypes are generated from the TypeScript type definitions. │\n  // │    To update them, edit the d.ts file and run `pnpm proptypes`.     │\n  // └─────────────────────────────────────────────────────────────────────┘\n  /**\n   * The styles you want to apply globally.\n   */\n  styles: PropTypes /* @typescript-to-proptypes-ignore */.oneOfType([PropTypes.array, PropTypes.func, PropTypes.number, PropTypes.object, PropTypes.string, PropTypes.bool])\n} : void 0;\nexport default GlobalStyles;"], "mappings": "AAAA,YAAY;;AAEZ,OAAOA,QAAQ,MAAM,oCAAoC;AACzD,OAAO,KAAKC,KAAK,MAAM,OAAO;AAC9B,OAAOC,SAAS,MAAM,YAAY;AAClC,SAASC,YAAY,IAAIC,kBAAkB,QAAQ,aAAa;AAChE,OAAOC,YAAY,MAAM,wBAAwB;AACjD,OAAOC,QAAQ,MAAM,sBAAsB;AAC3C,SAASC,GAAG,IAAIC,IAAI,QAAQ,mBAAmB;AAC/C,SAASL,YAAYA,CAACM,KAAK,EAAE;EAC3B,OAAO,aAAaD,IAAI,CAACJ,kBAAkB,EAAEJ,QAAQ,CAAC,CAAC,CAAC,EAAES,KAAK,EAAE;IAC/DJ,YAAY,EAAEA,YAAY;IAC1BK,OAAO,EAAEJ;EACX,CAAC,CAAC,CAAC;AACL;AACAK,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,GAAGV,YAAY,CAACW,SAAS,CAAC,yBAAyB;EACtF;EACA;EACA;EACA;EACA;AACF;AACA;EACEC,MAAM,EAAEb,SAAS,CAAC,sCAAsCc,SAAS,CAAC,CAACd,SAAS,CAACe,KAAK,EAAEf,SAAS,CAACgB,IAAI,EAAEhB,SAAS,CAACiB,MAAM,EAAEjB,SAAS,CAACkB,MAAM,EAAElB,SAAS,CAACmB,MAAM,EAAEnB,SAAS,CAACoB,IAAI,CAAC;AAC3K,CAAC,GAAG,KAAK,CAAC;AACV,eAAenB,YAAY", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}