{"ast": null, "code": "import { formatDistance } from \"./fi/_lib/formatDistance.js\";\nimport { formatLong } from \"./fi/_lib/formatLong.js\";\nimport { formatRelative } from \"./fi/_lib/formatRelative.js\";\nimport { localize } from \"./fi/_lib/localize.js\";\nimport { match } from \"./fi/_lib/match.js\";\n\n/**\n * @category Locales\n * @summary Finnish locale.\n * @language Finnish\n * @iso-639-2 fin\n * <AUTHOR> [@Pyppe](https://github.com/Pyppe)\n * <AUTHOR> [@mikolajgrzyb](https://github.com/mikolajgrzyb)\n * <AUTHOR> [@sjuvonen](https://github.com/sjuvonen)\n */\nexport const fi = {\n  code: \"fi\",\n  formatDistance: formatDistance,\n  formatLong: formatLong,\n  formatRelative: formatRelative,\n  localize: localize,\n  match: match,\n  options: {\n    weekStartsOn: 1 /* Monday */,\n    firstWeekContainsDate: 4\n  }\n};\n\n// Fallback for modularized imports:\nexport default fi;", "map": {"version": 3, "names": ["formatDistance", "formatLong", "formatRelative", "localize", "match", "fi", "code", "options", "weekStartsOn", "firstWeekContainsDate"], "sources": ["C:/CMS/webapp/frontend/node_modules/date-fns/locale/fi.js"], "sourcesContent": ["import { formatDistance } from \"./fi/_lib/formatDistance.js\";\nimport { formatLong } from \"./fi/_lib/formatLong.js\";\nimport { formatRelative } from \"./fi/_lib/formatRelative.js\";\nimport { localize } from \"./fi/_lib/localize.js\";\nimport { match } from \"./fi/_lib/match.js\";\n\n/**\n * @category Locales\n * @summary Finnish locale.\n * @language Finnish\n * @iso-639-2 fin\n * <AUTHOR> [@Pyppe](https://github.com/Pyppe)\n * <AUTHOR> [@mikolajgrzyb](https://github.com/mikolajgrzyb)\n * <AUTHOR> [@sjuvonen](https://github.com/sjuvonen)\n */\nexport const fi = {\n  code: \"fi\",\n  formatDistance: formatDistance,\n  formatLong: formatLong,\n  formatRelative: formatRelative,\n  localize: localize,\n  match: match,\n  options: {\n    weekStartsOn: 1 /* Monday */,\n    firstWeekContainsDate: 4,\n  },\n};\n\n// Fallback for modularized imports:\nexport default fi;\n"], "mappings": "AAAA,SAASA,cAAc,QAAQ,6BAA6B;AAC5D,SAASC,UAAU,QAAQ,yBAAyB;AACpD,SAASC,cAAc,QAAQ,6BAA6B;AAC5D,SAASC,QAAQ,QAAQ,uBAAuB;AAChD,SAASC,KAAK,QAAQ,oBAAoB;;AAE1C;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO,MAAMC,EAAE,GAAG;EAChBC,IAAI,EAAE,IAAI;EACVN,cAAc,EAAEA,cAAc;EAC9BC,UAAU,EAAEA,UAAU;EACtBC,cAAc,EAAEA,cAAc;EAC9BC,QAAQ,EAAEA,QAAQ;EAClBC,KAAK,EAAEA,KAAK;EACZG,OAAO,EAAE;IACPC,YAAY,EAAE,CAAC,CAAC;IAChBC,qBAAqB,EAAE;EACzB;AACF,CAAC;;AAED;AACA,eAAeJ,EAAE", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}