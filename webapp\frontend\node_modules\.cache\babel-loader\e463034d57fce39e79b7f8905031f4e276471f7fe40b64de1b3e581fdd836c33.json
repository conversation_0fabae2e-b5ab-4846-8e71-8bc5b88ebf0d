{"ast": null, "code": "import { addMilliseconds } from \"./addMilliseconds.mjs\";\n\n/**\n * @name subMilliseconds\n * @category Millisecond Helpers\n * @summary Subtract the specified number of milliseconds from the given date.\n *\n * @description\n * Subtract the specified number of milliseconds from the given date.\n *\n * @typeParam DateType - The `Date` type, the function operates on. Gets inferred from passed arguments. Allows to use extensions like [`UTCDate`](https://github.com/date-fns/utc).\n *\n * @param date - The date to be changed\n * @param amount - The amount of milliseconds to be subtracted.\n *\n * @returns The new date with the milliseconds subtracted\n *\n * @example\n * // Subtract 750 milliseconds from 10 July 2014 12:45:30.000:\n * const result = subMilliseconds(new Date(2014, 6, 10, 12, 45, 30, 0), 750)\n * //=> Thu Jul 10 2014 12:45:29.250\n */\nexport function subMilliseconds(date, amount) {\n  return addMilliseconds(date, -amount);\n}\n\n// Fallback for modularized imports:\nexport default subMilliseconds;", "map": {"version": 3, "names": ["addMilliseconds", "subMilliseconds", "date", "amount"], "sources": ["C:/CMS/webapp/frontend/node_modules/date-fns/subMilliseconds.mjs"], "sourcesContent": ["import { addMilliseconds } from \"./addMilliseconds.mjs\";\n\n/**\n * @name subMilliseconds\n * @category Millisecond Helpers\n * @summary Subtract the specified number of milliseconds from the given date.\n *\n * @description\n * Subtract the specified number of milliseconds from the given date.\n *\n * @typeParam DateType - The `Date` type, the function operates on. Gets inferred from passed arguments. Allows to use extensions like [`UTCDate`](https://github.com/date-fns/utc).\n *\n * @param date - The date to be changed\n * @param amount - The amount of milliseconds to be subtracted.\n *\n * @returns The new date with the milliseconds subtracted\n *\n * @example\n * // Subtract 750 milliseconds from 10 July 2014 12:45:30.000:\n * const result = subMilliseconds(new Date(2014, 6, 10, 12, 45, 30, 0), 750)\n * //=> Thu Jul 10 2014 12:45:29.250\n */\nexport function subMilliseconds(date, amount) {\n  return addMilliseconds(date, -amount);\n}\n\n// Fallback for modularized imports:\nexport default subMilliseconds;\n"], "mappings": "AAAA,SAASA,eAAe,QAAQ,uBAAuB;;AAEvD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO,SAASC,eAAeA,CAACC,IAAI,EAAEC,MAAM,EAAE;EAC5C,OAAOH,eAAe,CAACE,IAAI,EAAE,CAACC,MAAM,CAAC;AACvC;;AAEA;AACA,eAAeF,eAAe", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}