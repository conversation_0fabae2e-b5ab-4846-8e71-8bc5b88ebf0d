{"ast": null, "code": "import { buildLocalizeFn } from \"../../_lib/buildLocalizeFn.mjs\";\nconst eraValues = {\n  narrow: [\"до н.е.\", \"н.е.\"],\n  abbreviated: [\"до н. е.\", \"н. е.\"],\n  wide: [\"до нашої ери\", \"нашої ери\"]\n};\nconst quarterValues = {\n  narrow: [\"1\", \"2\", \"3\", \"4\"],\n  abbreviated: [\"1-й кв.\", \"2-й кв.\", \"3-й кв.\", \"4-й кв.\"],\n  wide: [\"1-й квартал\", \"2-й квартал\", \"3-й квартал\", \"4-й квартал\"]\n};\nconst monthValues = {\n  // ДСТУ 3582:2013\n  narrow: [\"С\", \"Л\", \"Б\", \"К\", \"Т\", \"Ч\", \"Л\", \"С\", \"В\", \"Ж\", \"Л\", \"Г\"],\n  abbreviated: [\"січ.\", \"лют.\", \"берез.\", \"квіт.\", \"трав.\", \"черв.\", \"лип.\", \"серп.\", \"верес.\", \"жовт.\", \"листоп.\", \"груд.\"],\n  wide: [\"січень\", \"лютий\", \"березень\", \"квітень\", \"травень\", \"червень\", \"липень\", \"серпень\", \"вересень\", \"жовтень\", \"листопад\", \"грудень\"]\n};\nconst formattingMonthValues = {\n  narrow: [\"С\", \"Л\", \"Б\", \"К\", \"Т\", \"Ч\", \"Л\", \"С\", \"В\", \"Ж\", \"Л\", \"Г\"],\n  abbreviated: [\"січ.\", \"лют.\", \"берез.\", \"квіт.\", \"трав.\", \"черв.\", \"лип.\", \"серп.\", \"верес.\", \"жовт.\", \"листоп.\", \"груд.\"],\n  wide: [\"січня\", \"лютого\", \"березня\", \"квітня\", \"травня\", \"червня\", \"липня\", \"серпня\", \"вересня\", \"жовтня\", \"листопада\", \"грудня\"]\n};\nconst dayValues = {\n  narrow: [\"Н\", \"П\", \"В\", \"С\", \"Ч\", \"П\", \"С\"],\n  short: [\"нд\", \"пн\", \"вт\", \"ср\", \"чт\", \"пт\", \"сб\"],\n  abbreviated: [\"нед\", \"пон\", \"вів\", \"сер\", \"чтв\", \"птн\", \"суб\"],\n  wide: [\"неділя\", \"понеділок\", \"вівторок\", \"середа\", \"четвер\", \"п’ятниця\", \"субота\"]\n};\nconst dayPeriodValues = {\n  narrow: {\n    am: \"ДП\",\n    pm: \"ПП\",\n    midnight: \"півн.\",\n    noon: \"пол.\",\n    morning: \"ранок\",\n    afternoon: \"день\",\n    evening: \"веч.\",\n    night: \"ніч\"\n  },\n  abbreviated: {\n    am: \"ДП\",\n    pm: \"ПП\",\n    midnight: \"півн.\",\n    noon: \"пол.\",\n    morning: \"ранок\",\n    afternoon: \"день\",\n    evening: \"веч.\",\n    night: \"ніч\"\n  },\n  wide: {\n    am: \"ДП\",\n    pm: \"ПП\",\n    midnight: \"північ\",\n    noon: \"полудень\",\n    morning: \"ранок\",\n    afternoon: \"день\",\n    evening: \"вечір\",\n    night: \"ніч\"\n  }\n};\nconst formattingDayPeriodValues = {\n  narrow: {\n    am: \"ДП\",\n    pm: \"ПП\",\n    midnight: \"півн.\",\n    noon: \"пол.\",\n    morning: \"ранку\",\n    afternoon: \"дня\",\n    evening: \"веч.\",\n    night: \"ночі\"\n  },\n  abbreviated: {\n    am: \"ДП\",\n    pm: \"ПП\",\n    midnight: \"півн.\",\n    noon: \"пол.\",\n    morning: \"ранку\",\n    afternoon: \"дня\",\n    evening: \"веч.\",\n    night: \"ночі\"\n  },\n  wide: {\n    am: \"ДП\",\n    pm: \"ПП\",\n    midnight: \"північ\",\n    noon: \"полудень\",\n    morning: \"ранку\",\n    afternoon: \"дня\",\n    evening: \"веч.\",\n    night: \"ночі\"\n  }\n};\nconst ordinalNumber = (dirtyNumber, options) => {\n  const unit = String(options?.unit);\n  const number = Number(dirtyNumber);\n  let suffix;\n  if (unit === \"date\") {\n    if (number === 3 || number === 23) {\n      suffix = \"-є\";\n    } else {\n      suffix = \"-е\";\n    }\n  } else if (unit === \"minute\" || unit === \"second\" || unit === \"hour\") {\n    suffix = \"-а\";\n  } else {\n    suffix = \"-й\";\n  }\n  return number + suffix;\n};\nexport const localize = {\n  ordinalNumber,\n  era: buildLocalizeFn({\n    values: eraValues,\n    defaultWidth: \"wide\"\n  }),\n  quarter: buildLocalizeFn({\n    values: quarterValues,\n    defaultWidth: \"wide\",\n    argumentCallback: quarter => quarter - 1\n  }),\n  month: buildLocalizeFn({\n    values: monthValues,\n    defaultWidth: \"wide\",\n    formattingValues: formattingMonthValues,\n    defaultFormattingWidth: \"wide\"\n  }),\n  day: buildLocalizeFn({\n    values: dayValues,\n    defaultWidth: \"wide\"\n  }),\n  dayPeriod: buildLocalizeFn({\n    values: dayPeriodValues,\n    defaultWidth: \"any\",\n    formattingValues: formattingDayPeriodValues,\n    defaultFormattingWidth: \"wide\"\n  })\n};", "map": {"version": 3, "names": ["buildLocalizeFn", "<PERSON><PERSON><PERSON><PERSON>", "narrow", "abbreviated", "wide", "quarterValues", "month<PERSON><PERSON><PERSON>", "formattingMonthValues", "dayV<PERSON><PERSON>", "short", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "am", "pm", "midnight", "noon", "morning", "afternoon", "evening", "night", "formattingDayPeriodValues", "ordinalNumber", "dirtyNumber", "options", "unit", "String", "number", "Number", "suffix", "localize", "era", "values", "defaultWidth", "quarter", "argument<PERSON>allback", "month", "formattingValues", "defaultFormattingWidth", "day", "<PERSON><PERSON><PERSON><PERSON>"], "sources": ["C:/CMS/webapp/frontend/node_modules/date-fns/locale/uk/_lib/localize.mjs"], "sourcesContent": ["import { buildLocalizeFn } from \"../../_lib/buildLocalizeFn.mjs\";\n\nconst eraValues = {\n  narrow: [\"до н.е.\", \"н.е.\"],\n  abbreviated: [\"до н. е.\", \"н. е.\"],\n  wide: [\"до нашої ери\", \"нашої ери\"],\n};\n\nconst quarterValues = {\n  narrow: [\"1\", \"2\", \"3\", \"4\"],\n  abbreviated: [\"1-й кв.\", \"2-й кв.\", \"3-й кв.\", \"4-й кв.\"],\n  wide: [\"1-й квартал\", \"2-й квартал\", \"3-й квартал\", \"4-й квартал\"],\n};\n\nconst monthValues = {\n  // ДСТУ 3582:2013\n  narrow: [\"С\", \"Л\", \"Б\", \"К\", \"Т\", \"Ч\", \"Л\", \"С\", \"В\", \"Ж\", \"Л\", \"Г\"],\n  abbreviated: [\n    \"січ.\",\n    \"лют.\",\n    \"берез.\",\n    \"квіт.\",\n    \"трав.\",\n    \"черв.\",\n    \"лип.\",\n    \"серп.\",\n    \"верес.\",\n    \"жовт.\",\n    \"листоп.\",\n    \"груд.\",\n  ],\n\n  wide: [\n    \"січень\",\n    \"лютий\",\n    \"березень\",\n    \"квітень\",\n    \"травень\",\n    \"червень\",\n    \"липень\",\n    \"серпень\",\n    \"вересень\",\n    \"жовтень\",\n    \"листопад\",\n    \"грудень\",\n  ],\n};\n\nconst formattingMonthValues = {\n  narrow: [\"С\", \"Л\", \"Б\", \"К\", \"Т\", \"Ч\", \"Л\", \"С\", \"В\", \"Ж\", \"Л\", \"Г\"],\n  abbreviated: [\n    \"січ.\",\n    \"лют.\",\n    \"берез.\",\n    \"квіт.\",\n    \"трав.\",\n    \"черв.\",\n    \"лип.\",\n    \"серп.\",\n    \"верес.\",\n    \"жовт.\",\n    \"листоп.\",\n    \"груд.\",\n  ],\n\n  wide: [\n    \"січня\",\n    \"лютого\",\n    \"березня\",\n    \"квітня\",\n    \"травня\",\n    \"червня\",\n    \"липня\",\n    \"серпня\",\n    \"вересня\",\n    \"жовтня\",\n    \"листопада\",\n    \"грудня\",\n  ],\n};\n\nconst dayValues = {\n  narrow: [\"Н\", \"П\", \"В\", \"С\", \"Ч\", \"П\", \"С\"],\n  short: [\"нд\", \"пн\", \"вт\", \"ср\", \"чт\", \"пт\", \"сб\"],\n  abbreviated: [\"нед\", \"пон\", \"вів\", \"сер\", \"чтв\", \"птн\", \"суб\"],\n  wide: [\n    \"неділя\",\n    \"понеділок\",\n    \"вівторок\",\n    \"середа\",\n    \"четвер\",\n    \"п’ятниця\",\n    \"субота\",\n  ],\n};\n\nconst dayPeriodValues = {\n  narrow: {\n    am: \"ДП\",\n    pm: \"ПП\",\n    midnight: \"півн.\",\n    noon: \"пол.\",\n    morning: \"ранок\",\n    afternoon: \"день\",\n    evening: \"веч.\",\n    night: \"ніч\",\n  },\n  abbreviated: {\n    am: \"ДП\",\n    pm: \"ПП\",\n    midnight: \"півн.\",\n    noon: \"пол.\",\n    morning: \"ранок\",\n    afternoon: \"день\",\n    evening: \"веч.\",\n    night: \"ніч\",\n  },\n  wide: {\n    am: \"ДП\",\n    pm: \"ПП\",\n    midnight: \"північ\",\n    noon: \"полудень\",\n    morning: \"ранок\",\n    afternoon: \"день\",\n    evening: \"вечір\",\n    night: \"ніч\",\n  },\n};\n\nconst formattingDayPeriodValues = {\n  narrow: {\n    am: \"ДП\",\n    pm: \"ПП\",\n    midnight: \"півн.\",\n    noon: \"пол.\",\n    morning: \"ранку\",\n    afternoon: \"дня\",\n    evening: \"веч.\",\n    night: \"ночі\",\n  },\n  abbreviated: {\n    am: \"ДП\",\n    pm: \"ПП\",\n    midnight: \"півн.\",\n    noon: \"пол.\",\n    morning: \"ранку\",\n    afternoon: \"дня\",\n    evening: \"веч.\",\n    night: \"ночі\",\n  },\n  wide: {\n    am: \"ДП\",\n    pm: \"ПП\",\n    midnight: \"північ\",\n    noon: \"полудень\",\n    morning: \"ранку\",\n    afternoon: \"дня\",\n    evening: \"веч.\",\n    night: \"ночі\",\n  },\n};\n\nconst ordinalNumber = (dirtyNumber, options) => {\n  const unit = String(options?.unit);\n  const number = Number(dirtyNumber);\n  let suffix;\n\n  if (unit === \"date\") {\n    if (number === 3 || number === 23) {\n      suffix = \"-є\";\n    } else {\n      suffix = \"-е\";\n    }\n  } else if (unit === \"minute\" || unit === \"second\" || unit === \"hour\") {\n    suffix = \"-а\";\n  } else {\n    suffix = \"-й\";\n  }\n\n  return number + suffix;\n};\n\nexport const localize = {\n  ordinalNumber,\n\n  era: buildLocalizeFn({\n    values: eraValues,\n    defaultWidth: \"wide\",\n  }),\n\n  quarter: buildLocalizeFn({\n    values: quarterValues,\n    defaultWidth: \"wide\",\n    argumentCallback: (quarter) => quarter - 1,\n  }),\n\n  month: buildLocalizeFn({\n    values: monthValues,\n    defaultWidth: \"wide\",\n    formattingValues: formattingMonthValues,\n    defaultFormattingWidth: \"wide\",\n  }),\n\n  day: buildLocalizeFn({\n    values: dayValues,\n    defaultWidth: \"wide\",\n  }),\n\n  dayPeriod: buildLocalizeFn({\n    values: dayPeriodValues,\n    defaultWidth: \"any\",\n    formattingValues: formattingDayPeriodValues,\n    defaultFormattingWidth: \"wide\",\n  }),\n};\n"], "mappings": "AAAA,SAASA,eAAe,QAAQ,gCAAgC;AAEhE,MAAMC,SAAS,GAAG;EAChBC,MAAM,EAAE,CAAC,SAAS,EAAE,MAAM,CAAC;EAC3BC,WAAW,EAAE,CAAC,UAAU,EAAE,OAAO,CAAC;EAClCC,IAAI,EAAE,CAAC,cAAc,EAAE,WAAW;AACpC,CAAC;AAED,MAAMC,aAAa,GAAG;EACpBH,MAAM,EAAE,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,CAAC;EAC5BC,WAAW,EAAE,CAAC,SAAS,EAAE,SAAS,EAAE,SAAS,EAAE,SAAS,CAAC;EACzDC,IAAI,EAAE,CAAC,aAAa,EAAE,aAAa,EAAE,aAAa,EAAE,aAAa;AACnE,CAAC;AAED,MAAME,WAAW,GAAG;EAClB;EACAJ,MAAM,EAAE,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,CAAC;EACpEC,WAAW,EAAE,CACX,MAAM,EACN,MAAM,EACN,QAAQ,EACR,OAAO,EACP,OAAO,EACP,OAAO,EACP,MAAM,EACN,OAAO,EACP,QAAQ,EACR,OAAO,EACP,SAAS,EACT,OAAO,CACR;EAEDC,IAAI,EAAE,CACJ,QAAQ,EACR,OAAO,EACP,UAAU,EACV,SAAS,EACT,SAAS,EACT,SAAS,EACT,QAAQ,EACR,SAAS,EACT,UAAU,EACV,SAAS,EACT,UAAU,EACV,SAAS;AAEb,CAAC;AAED,MAAMG,qBAAqB,GAAG;EAC5BL,MAAM,EAAE,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,CAAC;EACpEC,WAAW,EAAE,CACX,MAAM,EACN,MAAM,EACN,QAAQ,EACR,OAAO,EACP,OAAO,EACP,OAAO,EACP,MAAM,EACN,OAAO,EACP,QAAQ,EACR,OAAO,EACP,SAAS,EACT,OAAO,CACR;EAEDC,IAAI,EAAE,CACJ,OAAO,EACP,QAAQ,EACR,SAAS,EACT,QAAQ,EACR,QAAQ,EACR,QAAQ,EACR,OAAO,EACP,QAAQ,EACR,SAAS,EACT,QAAQ,EACR,WAAW,EACX,QAAQ;AAEZ,CAAC;AAED,MAAMI,SAAS,GAAG;EAChBN,MAAM,EAAE,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,CAAC;EAC3CO,KAAK,EAAE,CAAC,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,CAAC;EACjDN,WAAW,EAAE,CAAC,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,CAAC;EAC9DC,IAAI,EAAE,CACJ,QAAQ,EACR,WAAW,EACX,UAAU,EACV,QAAQ,EACR,QAAQ,EACR,UAAU,EACV,QAAQ;AAEZ,CAAC;AAED,MAAMM,eAAe,GAAG;EACtBR,MAAM,EAAE;IACNS,EAAE,EAAE,IAAI;IACRC,EAAE,EAAE,IAAI;IACRC,QAAQ,EAAE,OAAO;IACjBC,IAAI,EAAE,MAAM;IACZC,OAAO,EAAE,OAAO;IAChBC,SAAS,EAAE,MAAM;IACjBC,OAAO,EAAE,MAAM;IACfC,KAAK,EAAE;EACT,CAAC;EACDf,WAAW,EAAE;IACXQ,EAAE,EAAE,IAAI;IACRC,EAAE,EAAE,IAAI;IACRC,QAAQ,EAAE,OAAO;IACjBC,IAAI,EAAE,MAAM;IACZC,OAAO,EAAE,OAAO;IAChBC,SAAS,EAAE,MAAM;IACjBC,OAAO,EAAE,MAAM;IACfC,KAAK,EAAE;EACT,CAAC;EACDd,IAAI,EAAE;IACJO,EAAE,EAAE,IAAI;IACRC,EAAE,EAAE,IAAI;IACRC,QAAQ,EAAE,QAAQ;IAClBC,IAAI,EAAE,UAAU;IAChBC,OAAO,EAAE,OAAO;IAChBC,SAAS,EAAE,MAAM;IACjBC,OAAO,EAAE,OAAO;IAChBC,KAAK,EAAE;EACT;AACF,CAAC;AAED,MAAMC,yBAAyB,GAAG;EAChCjB,MAAM,EAAE;IACNS,EAAE,EAAE,IAAI;IACRC,EAAE,EAAE,IAAI;IACRC,QAAQ,EAAE,OAAO;IACjBC,IAAI,EAAE,MAAM;IACZC,OAAO,EAAE,OAAO;IAChBC,SAAS,EAAE,KAAK;IAChBC,OAAO,EAAE,MAAM;IACfC,KAAK,EAAE;EACT,CAAC;EACDf,WAAW,EAAE;IACXQ,EAAE,EAAE,IAAI;IACRC,EAAE,EAAE,IAAI;IACRC,QAAQ,EAAE,OAAO;IACjBC,IAAI,EAAE,MAAM;IACZC,OAAO,EAAE,OAAO;IAChBC,SAAS,EAAE,KAAK;IAChBC,OAAO,EAAE,MAAM;IACfC,KAAK,EAAE;EACT,CAAC;EACDd,IAAI,EAAE;IACJO,EAAE,EAAE,IAAI;IACRC,EAAE,EAAE,IAAI;IACRC,QAAQ,EAAE,QAAQ;IAClBC,IAAI,EAAE,UAAU;IAChBC,OAAO,EAAE,OAAO;IAChBC,SAAS,EAAE,KAAK;IAChBC,OAAO,EAAE,MAAM;IACfC,KAAK,EAAE;EACT;AACF,CAAC;AAED,MAAME,aAAa,GAAGA,CAACC,WAAW,EAAEC,OAAO,KAAK;EAC9C,MAAMC,IAAI,GAAGC,MAAM,CAACF,OAAO,EAAEC,IAAI,CAAC;EAClC,MAAME,MAAM,GAAGC,MAAM,CAACL,WAAW,CAAC;EAClC,IAAIM,MAAM;EAEV,IAAIJ,IAAI,KAAK,MAAM,EAAE;IACnB,IAAIE,MAAM,KAAK,CAAC,IAAIA,MAAM,KAAK,EAAE,EAAE;MACjCE,MAAM,GAAG,IAAI;IACf,CAAC,MAAM;MACLA,MAAM,GAAG,IAAI;IACf;EACF,CAAC,MAAM,IAAIJ,IAAI,KAAK,QAAQ,IAAIA,IAAI,KAAK,QAAQ,IAAIA,IAAI,KAAK,MAAM,EAAE;IACpEI,MAAM,GAAG,IAAI;EACf,CAAC,MAAM;IACLA,MAAM,GAAG,IAAI;EACf;EAEA,OAAOF,MAAM,GAAGE,MAAM;AACxB,CAAC;AAED,OAAO,MAAMC,QAAQ,GAAG;EACtBR,aAAa;EAEbS,GAAG,EAAE7B,eAAe,CAAC;IACnB8B,MAAM,EAAE7B,SAAS;IACjB8B,YAAY,EAAE;EAChB,CAAC,CAAC;EAEFC,OAAO,EAAEhC,eAAe,CAAC;IACvB8B,MAAM,EAAEzB,aAAa;IACrB0B,YAAY,EAAE,MAAM;IACpBE,gBAAgB,EAAGD,OAAO,IAAKA,OAAO,GAAG;EAC3C,CAAC,CAAC;EAEFE,KAAK,EAAElC,eAAe,CAAC;IACrB8B,MAAM,EAAExB,WAAW;IACnByB,YAAY,EAAE,MAAM;IACpBI,gBAAgB,EAAE5B,qBAAqB;IACvC6B,sBAAsB,EAAE;EAC1B,CAAC,CAAC;EAEFC,GAAG,EAAErC,eAAe,CAAC;IACnB8B,MAAM,EAAEtB,SAAS;IACjBuB,YAAY,EAAE;EAChB,CAAC,CAAC;EAEFO,SAAS,EAAEtC,eAAe,CAAC;IACzB8B,MAAM,EAAEpB,eAAe;IACvBqB,YAAY,EAAE,KAAK;IACnBI,gBAAgB,EAAEhB,yBAAyB;IAC3CiB,sBAAsB,EAAE;EAC1B,CAAC;AACH,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}