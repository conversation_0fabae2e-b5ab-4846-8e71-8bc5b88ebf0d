{"ast": null, "code": "var _jsxFileName = \"C:\\\\CMS\\\\webapp\\\\frontend\\\\src\\\\components\\\\comande\\\\InserimentoMetriDialog.js\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect } from 'react';\nimport { Dialog, DialogTitle, DialogContent, DialogActions, Button, TextField, Grid, Typography, Box, Alert, Chip, Table, TableBody, TableCell, TableContainer, TableHead, TableRow, Paper, IconButton, Tooltip, Autocomplete, FormControl, InputLabel, Select, MenuItem, ListItemText, ListItemIcon } from '@mui/material';\nimport { Save as SaveIcon, Cancel as CancelIcon, Cable as CableIcon, CheckCircle as CheckCircleIcon, Warning as WarningIcon, Storage as BobinaIcon, Error as ErrorIcon, Info as InfoIcon } from '@mui/icons-material';\nimport comandeService from '../../services/comandeService';\nimport caviService from '../../services/caviService';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst InserimentoMetriDialog = ({\n  open,\n  onClose,\n  comanda,\n  onSuccess\n}) => {\n  _s();\n  const [loading, setLoading] = useState(false);\n  const [error, setError] = useState(null);\n  const [cavi, setCavi] = useState([]);\n  const [datiPosa, setDatiPosa] = useState({});\n  const [validationErrors, setValidationErrors] = useState({});\n  const [bobineDisponibili, setBobineDisponibili] = useState([]);\n  const [loadingBobine, setLoadingBobine] = useState(false);\n  useEffect(() => {\n    if (open && comanda) {\n      loadCaviComanda();\n      loadBobineDisponibili();\n    }\n  }, [open, comanda]);\n  const loadCaviComanda = async () => {\n    try {\n      setLoading(true);\n      setError(null);\n      const caviData = await comandeService.getCaviComanda(comanda.codice_comanda);\n      setCavi(caviData);\n\n      // Inizializza i dati di posa per ogni cavo\n      const initialDati = {};\n      caviData.forEach(cavo => {\n        initialDati[cavo.id_cavo] = {\n          metratura_reale: cavo.metratura_reale || cavo.metratura_teorica || 0,\n          data_posa: new Date().toISOString().split('T')[0],\n          responsabile_posa: comanda.responsabile || '',\n          note: '',\n          id_bobina: cavo.id_bobina || '',\n          // Bobina attualmente associata\n          force_over: false // Flag per forzare associazione anche se metri insufficienti\n        };\n      });\n      setDatiPosa(initialDati);\n    } catch (err) {\n      console.error('Errore nel caricamento cavi:', err);\n      setError('Errore nel caricamento dei cavi della comanda');\n    } finally {\n      setLoading(false);\n    }\n  };\n  const loadBobineDisponibili = async () => {\n    try {\n      setLoadingBobine(true);\n\n      // Verifica che ci sia un cantiere valido\n      if (!(comanda !== null && comanda !== void 0 && comanda.id_cantiere)) {\n        console.warn('ID cantiere non disponibile per il caricamento bobine');\n        return;\n      }\n      console.log(`Caricamento bobine per cantiere ${comanda.id_cantiere}`);\n\n      // Carica tutte le bobine disponibili per il cantiere\n      const bobineData = await caviService.getBobineDisponibili(comanda.id_cantiere);\n      setBobineDisponibili(bobineData);\n      console.log(`Bobine caricate: ${bobineData.length}`);\n    } catch (err) {\n      console.error('Errore nel caricamento bobine:', err);\n\n      // Gestione specifica per errori di autenticazione\n      if (err.status === 401) {\n        setError('Sessione scaduta. Effettua nuovamente il login.');\n      } else if (err.isNetworkError) {\n        setError('Impossibile connettersi al server per caricare le bobine.');\n      } else {\n        setError('Errore nel caricamento delle bobine. Alcune funzionalità potrebbero non essere disponibili.');\n      }\n\n      // Imposta array vuoto per evitare errori nell'interfaccia\n      setBobineDisponibili([]);\n    } finally {\n      setLoadingBobine(false);\n    }\n  };\n  const handleMetriChange = (idCavo, value) => {\n    const numericValue = parseFloat(value) || 0;\n\n    // Aggiorna i dati di posa\n    setDatiPosa(prev => {\n      const newDatiPosa = {\n        ...prev,\n        [idCavo]: {\n          ...prev[idCavo],\n          metratura_reale: numericValue\n        }\n      };\n\n      // Validazione metri vs teorico\n      const cavo = cavi.find(c => c.id_cavo === idCavo);\n      let errors = [];\n      if (cavo && numericValue > cavo.metratura_teorica * 1.1) {\n        errors.push('Metratura superiore del 10% rispetto al teorico');\n      }\n\n      // Validazione metri vs bobina selezionata\n      const datiCavo = newDatiPosa[idCavo];\n      if (datiCavo !== null && datiCavo !== void 0 && datiCavo.id_bobina && datiCavo.id_bobina !== 'BOBINA_VUOTA') {\n        const bobina = bobineDisponibili.find(b => b.id_bobina === datiCavo.id_bobina);\n        if (bobina && numericValue > bobina.metri_residui && !datiCavo.force_over) {\n          errors.push(`Bobina ha solo ${bobina.metri_residui}m residui (richiesti ${numericValue}m)`);\n        }\n      }\n\n      // Aggiorna gli errori di validazione\n      setValidationErrors(prevErrors => {\n        if (errors.length > 0) {\n          return {\n            ...prevErrors,\n            [idCavo]: errors.join('; ')\n          };\n        } else {\n          const newErrors = {\n            ...prevErrors\n          };\n          delete newErrors[idCavo];\n          return newErrors;\n        }\n      });\n      return newDatiPosa;\n    });\n  };\n  const handleNoteChange = (idCavo, value) => {\n    setDatiPosa(prev => ({\n      ...prev,\n      [idCavo]: {\n        ...prev[idCavo],\n        note: value\n      }\n    }));\n  };\n  const handleBobinaChange = (idCavo, bobinaId) => {\n    setDatiPosa(prev => {\n      const newDatiPosa = {\n        ...prev,\n        [idCavo]: {\n          ...prev[idCavo],\n          id_bobina: bobinaId,\n          force_over: false // Reset force_over quando cambia bobina\n        }\n      };\n\n      // Rivalidazione dopo cambio bobina\n      const datiCavo = newDatiPosa[idCavo];\n      if (datiCavo !== null && datiCavo !== void 0 && datiCavo.metratura_reale) {\n        // Usa setTimeout per evitare problemi di stato asincrono\n        setTimeout(() => {\n          handleMetriChange(idCavo, datiCavo.metratura_reale);\n        }, 0);\n      }\n      return newDatiPosa;\n    });\n  };\n  const handleForceOverChange = (idCavo, forceOver) => {\n    setDatiPosa(prev => {\n      const newDatiPosa = {\n        ...prev,\n        [idCavo]: {\n          ...prev[idCavo],\n          force_over: forceOver\n        }\n      };\n\n      // Rivalidazione dopo cambio force_over\n      const datiCavo = newDatiPosa[idCavo];\n      if (datiCavo !== null && datiCavo !== void 0 && datiCavo.metratura_reale) {\n        // Usa setTimeout per evitare problemi di stato asincrono\n        setTimeout(() => {\n          handleMetriChange(idCavo, datiCavo.metratura_reale);\n        }, 0);\n      }\n      return newDatiPosa;\n    });\n  };\n  const handleSave = async () => {\n    try {\n      setLoading(true);\n      setError(null);\n\n      // Validazione finale\n      const hasErrors = Object.keys(validationErrors).length > 0;\n      if (hasErrors) {\n        setError('Correggere gli errori di validazione prima di salvare');\n        return;\n      }\n\n      // Salva i dati di posa con associazioni bobine\n      await comandeService.aggiornaDatiPosaConBobine(comanda.codice_comanda, datiPosa);\n      onSuccess === null || onSuccess === void 0 ? void 0 : onSuccess('Dati di posa salvati con successo');\n      onClose();\n    } catch (err) {\n      console.error('Errore nel salvataggio:', err);\n      setError(err.message || 'Errore nel salvataggio dei dati');\n    } finally {\n      setLoading(false);\n    }\n  };\n  const getTipoComandaColor = tipo => {\n    switch (tipo) {\n      case 'POSA':\n        return 'primary';\n      case 'COLLEGAMENTO_PARTENZA':\n        return 'warning';\n      case 'COLLEGAMENTO_ARRIVO':\n        return 'info';\n      case 'CERTIFICAZIONE':\n        return 'success';\n      default:\n        return 'default';\n    }\n  };\n  const getTipoComandaLabel = tipo => {\n    switch (tipo) {\n      case 'POSA':\n        return 'Posa';\n      case 'COLLEGAMENTO_PARTENZA':\n        return 'Coll. Partenza';\n      case 'COLLEGAMENTO_ARRIVO':\n        return 'Coll. Arrivo';\n      case 'CERTIFICAZIONE':\n        return 'Certificazione';\n      default:\n        return tipo;\n    }\n  };\n\n  // Filtra bobine compatibili per un cavo specifico\n  const getBobineCompatibili = cavo => {\n    if (!bobineDisponibili || bobineDisponibili.length === 0) {\n      return [];\n    }\n    return bobineDisponibili.filter(bobina => bobina.tipologia === cavo.tipologia && bobina.sezione === cavo.sezione && bobina.metri_residui > 0);\n  };\n\n  // Verifica se una bobina è compatibile con un cavo\n  const isBobinaCompatibile = (bobina, cavo) => {\n    return bobina.tipologia === cavo.tipologia && bobina.sezione === cavo.sezione;\n  };\n\n  // Ottiene il colore per lo stato della bobina\n  const getBobinaStatusColor = (bobina, metriRichiesti) => {\n    if (!bobina || bobina.id_bobina === 'BOBINA_VUOTA') return 'default';\n    if (metriRichiesti > bobina.metri_residui) return 'error';\n    if (bobina.metri_residui < bobina.metri_totali * 0.1) return 'warning';\n    return 'success';\n  };\n  if (!comanda) return null;\n  return /*#__PURE__*/_jsxDEV(Dialog, {\n    open: open,\n    onClose: onClose,\n    maxWidth: \"lg\",\n    fullWidth: true,\n    PaperProps: {\n      sx: {\n        minHeight: '70vh'\n      }\n    },\n    children: [/*#__PURE__*/_jsxDEV(DialogTitle, {\n      children: /*#__PURE__*/_jsxDEV(Box, {\n        display: \"flex\",\n        alignItems: \"center\",\n        gap: 2,\n        children: [/*#__PURE__*/_jsxDEV(CableIcon, {\n          color: \"primary\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 323,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Box, {\n          children: [/*#__PURE__*/_jsxDEV(Typography, {\n            variant: \"h6\",\n            children: \"Inserimento Metri Posati\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 325,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Box, {\n            display: \"flex\",\n            alignItems: \"center\",\n            gap: 1,\n            mt: 1,\n            children: [/*#__PURE__*/_jsxDEV(Typography, {\n              variant: \"body2\",\n              color: \"textSecondary\",\n              children: [\"Comanda: \", comanda.codice_comanda]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 329,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Chip, {\n              label: getTipoComandaLabel(comanda.tipo_comanda),\n              color: getTipoComandaColor(comanda.tipo_comanda),\n              size: \"small\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 332,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Typography, {\n              variant: \"body2\",\n              color: \"textSecondary\",\n              children: [\"Responsabile: \", comanda.responsabile]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 337,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 328,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 324,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 322,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 321,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(DialogContent, {\n      children: [error && /*#__PURE__*/_jsxDEV(Alert, {\n        severity: \"error\",\n        sx: {\n          mb: 2\n        },\n        children: error\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 347,\n        columnNumber: 11\n      }, this), loading ? /*#__PURE__*/_jsxDEV(Box, {\n        display: \"flex\",\n        justifyContent: \"center\",\n        p: 3,\n        children: /*#__PURE__*/_jsxDEV(Typography, {\n          children: \"Caricamento cavi...\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 354,\n          columnNumber: 13\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 353,\n        columnNumber: 11\n      }, this) : /*#__PURE__*/_jsxDEV(TableContainer, {\n        component: Paper,\n        variant: \"outlined\",\n        children: /*#__PURE__*/_jsxDEV(Table, {\n          size: \"small\",\n          children: [/*#__PURE__*/_jsxDEV(TableHead, {\n            children: /*#__PURE__*/_jsxDEV(TableRow, {\n              children: [/*#__PURE__*/_jsxDEV(TableCell, {\n                children: \"ID Cavo\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 361,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n                children: \"Tipologia\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 362,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n                children: \"Formazione\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 363,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n                children: \"Metri Teorici\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 364,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n                children: \"Metri Reali\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 365,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n                children: \"Bobina\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 366,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n                children: \"Stato\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 367,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n                children: \"Note\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 368,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 360,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 359,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(TableBody, {\n            children: cavi.map(cavo => {\n              var _cavo$metratura_teori, _datiPosa$cavo$id_cav, _datiPosa$cavo$id_cav2, _datiPosa$cavo$id_cav5, _datiPosa$cavo$id_cav6, _datiPosa$cavo$id_cav9, _datiPosa$cavo$id_cav1;\n              return /*#__PURE__*/_jsxDEV(TableRow, {\n                children: [/*#__PURE__*/_jsxDEV(TableCell, {\n                  children: /*#__PURE__*/_jsxDEV(Typography, {\n                    variant: \"body2\",\n                    fontWeight: \"bold\",\n                    children: cavo.id_cavo\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 375,\n                    columnNumber: 23\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 374,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n                  children: cavo.tipologia || 'N/A'\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 379,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n                  children: cavo.formazione || 'N/A'\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 380,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n                  children: /*#__PURE__*/_jsxDEV(Typography, {\n                    variant: \"body2\",\n                    children: [((_cavo$metratura_teori = cavo.metratura_teorica) === null || _cavo$metratura_teori === void 0 ? void 0 : _cavo$metratura_teori.toFixed(1)) || '0.0', \" m\"]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 382,\n                    columnNumber: 23\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 381,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n                  children: /*#__PURE__*/_jsxDEV(TextField, {\n                    type: \"number\",\n                    size: \"small\",\n                    value: ((_datiPosa$cavo$id_cav = datiPosa[cavo.id_cavo]) === null || _datiPosa$cavo$id_cav === void 0 ? void 0 : _datiPosa$cavo$id_cav.metratura_reale) || 0,\n                    onChange: e => handleMetriChange(cavo.id_cavo, e.target.value),\n                    error: !!validationErrors[cavo.id_cavo],\n                    helperText: validationErrors[cavo.id_cavo],\n                    inputProps: {\n                      min: 0,\n                      step: 0.1,\n                      style: {\n                        textAlign: 'right'\n                      }\n                    },\n                    sx: {\n                      width: 100\n                    }\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 387,\n                    columnNumber: 23\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 386,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n                  children: /*#__PURE__*/_jsxDEV(Box, {\n                    sx: {\n                      minWidth: 200\n                    },\n                    children: [/*#__PURE__*/_jsxDEV(Autocomplete, {\n                      size: \"small\",\n                      value: ((_datiPosa$cavo$id_cav2 = datiPosa[cavo.id_cavo]) === null || _datiPosa$cavo$id_cav2 === void 0 ? void 0 : _datiPosa$cavo$id_cav2.id_bobina) === 'BOBINA_VUOTA' ? {\n                        id_bobina: 'BOBINA_VUOTA',\n                        tipologia: 'Vuota',\n                        metri_residui: '∞'\n                      } : bobineDisponibili.find(b => {\n                        var _datiPosa$cavo$id_cav3;\n                        return b.id_bobina === ((_datiPosa$cavo$id_cav3 = datiPosa[cavo.id_cavo]) === null || _datiPosa$cavo$id_cav3 === void 0 ? void 0 : _datiPosa$cavo$id_cav3.id_bobina);\n                      }) || null,\n                      onChange: (event, newValue) => {\n                        handleBobinaChange(cavo.id_cavo, (newValue === null || newValue === void 0 ? void 0 : newValue.id_bobina) || '');\n                      },\n                      options: [{\n                        id_bobina: 'BOBINA_VUOTA',\n                        tipologia: 'Vuota',\n                        metri_residui: '∞'\n                      }, ...getBobineCompatibili(cavo)],\n                      noOptionsText: loadingBobine ? \"Caricamento bobine...\" : bobineDisponibili.length === 0 ? \"Nessuna bobina disponibile (verificare connessione)\" : \"Nessuna bobina compatibile\",\n                      getOptionLabel: option => {\n                        if (option.id_bobina === 'BOBINA_VUOTA') return 'BOBINA_VUOTA';\n                        return `${option.id_bobina} (${option.metri_residui}m)`;\n                      },\n                      renderInput: params => /*#__PURE__*/_jsxDEV(TextField, {\n                        ...params,\n                        placeholder: \"Seleziona bobina...\",\n                        variant: \"outlined\",\n                        size: \"small\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 430,\n                        columnNumber: 29\n                      }, this),\n                      renderOption: (props, option) => {\n                        var _datiPosa$cavo$id_cav4;\n                        return /*#__PURE__*/_jsxDEV(Box, {\n                          component: \"li\",\n                          ...props,\n                          children: [/*#__PURE__*/_jsxDEV(ListItemIcon, {\n                            children: /*#__PURE__*/_jsxDEV(BobinaIcon, {\n                              fontSize: \"small\",\n                              color: option.id_bobina === 'BOBINA_VUOTA' ? 'default' : getBobinaStatusColor(option, ((_datiPosa$cavo$id_cav4 = datiPosa[cavo.id_cavo]) === null || _datiPosa$cavo$id_cav4 === void 0 ? void 0 : _datiPosa$cavo$id_cav4.metratura_reale) || 0)\n                            }, void 0, false, {\n                              fileName: _jsxFileName,\n                              lineNumber: 440,\n                              columnNumber: 33\n                            }, this)\n                          }, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 439,\n                            columnNumber: 31\n                          }, this), /*#__PURE__*/_jsxDEV(ListItemText, {\n                            primary: option.id_bobina === 'BOBINA_VUOTA' ? 'BOBINA_VUOTA' : option.id_bobina,\n                            secondary: option.id_bobina === 'BOBINA_VUOTA' ? 'Nessuna bobina associata' : `${option.tipologia} ${option.sezione} - ${option.metri_residui}m residui`\n                          }, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 449,\n                            columnNumber: 31\n                          }, this)]\n                        }, void 0, true, {\n                          fileName: _jsxFileName,\n                          lineNumber: 438,\n                          columnNumber: 29\n                        }, this);\n                      },\n                      loading: loadingBobine,\n                      disabled: loadingBobine,\n                      sx: {\n                        width: '100%'\n                      }\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 404,\n                      columnNumber: 25\n                    }, this), ((_datiPosa$cavo$id_cav5 = datiPosa[cavo.id_cavo]) === null || _datiPosa$cavo$id_cav5 === void 0 ? void 0 : _datiPosa$cavo$id_cav5.id_bobina) && ((_datiPosa$cavo$id_cav6 = datiPosa[cavo.id_cavo]) === null || _datiPosa$cavo$id_cav6 === void 0 ? void 0 : _datiPosa$cavo$id_cav6.id_bobina) !== 'BOBINA_VUOTA' && (_datiPosa$cavo$id_cav8 => {\n                      const bobina = bobineDisponibili.find(b => {\n                        var _datiPosa$cavo$id_cav7;\n                        return b.id_bobina === ((_datiPosa$cavo$id_cav7 = datiPosa[cavo.id_cavo]) === null || _datiPosa$cavo$id_cav7 === void 0 ? void 0 : _datiPosa$cavo$id_cav7.id_bobina);\n                      });\n                      const metriRichiesti = ((_datiPosa$cavo$id_cav8 = datiPosa[cavo.id_cavo]) === null || _datiPosa$cavo$id_cav8 === void 0 ? void 0 : _datiPosa$cavo$id_cav8.metratura_reale) || 0;\n                      return bobina && metriRichiesti > bobina.metri_residui;\n                    })() && /*#__PURE__*/_jsxDEV(Box, {\n                      sx: {\n                        mt: 1\n                      },\n                      children: /*#__PURE__*/_jsxDEV(Button, {\n                        size: \"small\",\n                        variant: (_datiPosa$cavo$id_cav9 = datiPosa[cavo.id_cavo]) !== null && _datiPosa$cavo$id_cav9 !== void 0 && _datiPosa$cavo$id_cav9.force_over ? \"contained\" : \"outlined\",\n                        color: \"warning\",\n                        onClick: () => {\n                          var _datiPosa$cavo$id_cav0;\n                          return handleForceOverChange(cavo.id_cavo, !((_datiPosa$cavo$id_cav0 = datiPosa[cavo.id_cavo]) !== null && _datiPosa$cavo$id_cav0 !== void 0 && _datiPosa$cavo$id_cav0.force_over));\n                        },\n                        startIcon: /*#__PURE__*/_jsxDEV(WarningIcon, {}, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 477,\n                          columnNumber: 42\n                        }, this),\n                        children: \"Forza Over\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 472,\n                        columnNumber: 29\n                      }, this)\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 471,\n                      columnNumber: 27\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 403,\n                    columnNumber: 23\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 402,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n                  children: cavo.stato_installazione === 'Installato' ? /*#__PURE__*/_jsxDEV(Chip, {\n                    icon: /*#__PURE__*/_jsxDEV(CheckCircleIcon, {}, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 488,\n                      columnNumber: 33\n                    }, this),\n                    label: \"Installato\",\n                    color: \"success\",\n                    size: \"small\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 487,\n                    columnNumber: 25\n                  }, this) : /*#__PURE__*/_jsxDEV(Chip, {\n                    icon: /*#__PURE__*/_jsxDEV(WarningIcon, {}, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 495,\n                      columnNumber: 33\n                    }, this),\n                    label: \"Da Installare\",\n                    color: \"warning\",\n                    size: \"small\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 494,\n                    columnNumber: 25\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 485,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n                  children: /*#__PURE__*/_jsxDEV(TextField, {\n                    size: \"small\",\n                    placeholder: \"Note opzionali...\",\n                    value: ((_datiPosa$cavo$id_cav1 = datiPosa[cavo.id_cavo]) === null || _datiPosa$cavo$id_cav1 === void 0 ? void 0 : _datiPosa$cavo$id_cav1.note) || '',\n                    onChange: e => handleNoteChange(cavo.id_cavo, e.target.value),\n                    sx: {\n                      width: 150\n                    }\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 503,\n                    columnNumber: 23\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 502,\n                  columnNumber: 21\n                }, this)]\n              }, cavo.id_cavo, true, {\n                fileName: _jsxFileName,\n                lineNumber: 373,\n                columnNumber: 19\n              }, this);\n            })\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 371,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 358,\n          columnNumber: 13\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 357,\n        columnNumber: 11\n      }, this), cavi.length === 0 && !loading && /*#__PURE__*/_jsxDEV(Alert, {\n        severity: \"info\",\n        sx: {\n          mt: 2\n        },\n        children: \"Nessun cavo assegnato a questa comanda.\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 519,\n        columnNumber: 11\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 345,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(DialogActions, {\n      sx: {\n        p: 2,\n        gap: 1\n      },\n      children: [/*#__PURE__*/_jsxDEV(Button, {\n        onClick: onClose,\n        startIcon: /*#__PURE__*/_jsxDEV(CancelIcon, {}, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 528,\n          columnNumber: 22\n        }, this),\n        disabled: loading,\n        children: \"Annulla\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 526,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Button, {\n        onClick: handleSave,\n        variant: \"contained\",\n        startIcon: /*#__PURE__*/_jsxDEV(SaveIcon, {}, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 536,\n          columnNumber: 22\n        }, this),\n        disabled: loading || cavi.length === 0,\n        children: \"Salva Dati Posa\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 533,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 525,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 312,\n    columnNumber: 5\n  }, this);\n};\n_s(InserimentoMetriDialog, \"qZFpwoKx7H/Z3PEQZ2tXwtWx1vY=\");\n_c = InserimentoMetriDialog;\nexport default InserimentoMetriDialog;\nvar _c;\n$RefreshReg$(_c, \"InserimentoMetriDialog\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "Dialog", "DialogTitle", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "DialogActions", "<PERSON><PERSON>", "TextField", "Grid", "Typography", "Box", "<PERSON><PERSON>", "Chip", "Table", "TableBody", "TableCell", "TableContainer", "TableHead", "TableRow", "Paper", "IconButton", "<PERSON><PERSON><PERSON>", "Autocomplete", "FormControl", "InputLabel", "Select", "MenuItem", "ListItemText", "ListItemIcon", "Save", "SaveIcon", "Cancel", "CancelIcon", "Cable", "CableIcon", "CheckCircle", "CheckCircleIcon", "Warning", "WarningIcon", "Storage", "BobinaIcon", "Error", "ErrorIcon", "Info", "InfoIcon", "comandeService", "caviService", "jsxDEV", "_jsxDEV", "InserimentoMetriDialog", "open", "onClose", "comanda", "onSuccess", "_s", "loading", "setLoading", "error", "setError", "cavi", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "setDatiPosa", "validationErrors", "setValidationErrors", "bobine<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "setBobineDisponibili", "loadingBobine", "setLoadingBob<PERSON>", "loadCaviComanda", "loadBobineDisponibili", "caviData", "getCaviComanda", "codice_comanda", "initialDati", "for<PERSON>ach", "cavo", "id_cavo", "metratura_reale", "metratura_teorica", "data_posa", "Date", "toISOString", "split", "responsabile_posa", "responsabile", "note", "id_bobina", "force_over", "err", "console", "id_cantiere", "warn", "log", "bobine<PERSON><PERSON>", "getBobineDisponibili", "length", "status", "isNetworkError", "handleMetriChange", "idCavo", "value", "numericValue", "parseFloat", "prev", "newDatiPosa", "find", "c", "errors", "push", "datiCavo", "bobina", "b", "metri_residui", "prevErrors", "join", "newErrors", "handleNoteChange", "handleBobinaChange", "bobina<PERSON>d", "setTimeout", "handleForceOverChange", "forceOver", "handleSave", "hasErrors", "Object", "keys", "aggiornaDatiPosaConBobine", "message", "getTipoComandaColor", "tipo", "getTipoComandaLabel", "getBobineCompatibili", "filter", "tipologia", "sezione", "isBobinaCompatibile", "getBobinaStatusColor", "metriRichiesti", "metri_totali", "max<PERSON><PERSON><PERSON>", "fullWidth", "PaperProps", "sx", "minHeight", "children", "display", "alignItems", "gap", "color", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "variant", "mt", "label", "tipo_comanda", "size", "severity", "mb", "justifyContent", "p", "component", "map", "_cavo$metratura_teori", "_datiPosa$cavo$id_cav", "_datiPosa$cavo$id_cav2", "_datiPosa$cavo$id_cav5", "_datiPosa$cavo$id_cav6", "_datiPosa$cavo$id_cav9", "_datiPosa$cavo$id_cav1", "fontWeight", "formazione", "toFixed", "type", "onChange", "e", "target", "helperText", "inputProps", "min", "step", "style", "textAlign", "width", "min<PERSON><PERSON><PERSON>", "_datiPosa$cavo$id_cav3", "event", "newValue", "options", "noOptionsText", "getOptionLabel", "option", "renderInput", "params", "placeholder", "renderOption", "props", "_datiPosa$cavo$id_cav4", "fontSize", "primary", "secondary", "disabled", "_datiPosa$cavo$id_cav8", "_datiPosa$cavo$id_cav7", "onClick", "_datiPosa$cavo$id_cav0", "startIcon", "stato_installazione", "icon", "_c", "$RefreshReg$"], "sources": ["C:/CMS/webapp/frontend/src/components/comande/InserimentoMetriDialog.js"], "sourcesContent": ["import React, { useState, useEffect } from 'react';\nimport {\n  Dialog,\n  DialogTitle,\n  DialogContent,\n  DialogActions,\n  Button,\n  TextField,\n  Grid,\n  Typography,\n  Box,\n  Alert,\n  Chip,\n  Table,\n  TableBody,\n  TableCell,\n  TableContainer,\n  TableHead,\n  TableRow,\n  Paper,\n  IconButton,\n  Tooltip,\n  Autocomplete,\n  FormControl,\n  InputLabel,\n  Select,\n  MenuItem,\n  ListItemText,\n  ListItemIcon\n} from '@mui/material';\nimport {\n  Save as SaveIcon,\n  Cancel as CancelIcon,\n  Cable as CableIcon,\n  CheckCircle as CheckCircleIcon,\n  Warning as WarningIcon,\n  Storage as BobinaIcon,\n  Error as ErrorIcon,\n  Info as InfoIcon\n} from '@mui/icons-material';\nimport comandeService from '../../services/comandeService';\nimport caviService from '../../services/caviService';\n\nconst InserimentoMetriDialog = ({\n  open,\n  onClose,\n  comanda,\n  onSuccess\n}) => {\n  const [loading, setLoading] = useState(false);\n  const [error, setError] = useState(null);\n  const [cavi, setCavi] = useState([]);\n  const [datiPosa, setDatiPosa] = useState({});\n  const [validationErrors, setValidationErrors] = useState({});\n  const [bobineDisponibili, setBobineDisponibili] = useState([]);\n  const [loadingBobine, setLoadingBobine] = useState(false);\n\n  useEffect(() => {\n    if (open && comanda) {\n      loadCaviComanda();\n      loadBobineDisponibili();\n    }\n  }, [open, comanda]);\n\n  const loadCaviComanda = async () => {\n    try {\n      setLoading(true);\n      setError(null);\n      \n      const caviData = await comandeService.getCaviComanda(comanda.codice_comanda);\n      setCavi(caviData);\n      \n      // Inizializza i dati di posa per ogni cavo\n      const initialDati = {};\n      caviData.forEach(cavo => {\n        initialDati[cavo.id_cavo] = {\n          metratura_reale: cavo.metratura_reale || cavo.metratura_teorica || 0,\n          data_posa: new Date().toISOString().split('T')[0],\n          responsabile_posa: comanda.responsabile || '',\n          note: '',\n          id_bobina: cavo.id_bobina || '', // Bobina attualmente associata\n          force_over: false // Flag per forzare associazione anche se metri insufficienti\n        };\n      });\n      setDatiPosa(initialDati);\n      \n    } catch (err) {\n      console.error('Errore nel caricamento cavi:', err);\n      setError('Errore nel caricamento dei cavi della comanda');\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  const loadBobineDisponibili = async () => {\n    try {\n      setLoadingBobine(true);\n\n      // Verifica che ci sia un cantiere valido\n      if (!comanda?.id_cantiere) {\n        console.warn('ID cantiere non disponibile per il caricamento bobine');\n        return;\n      }\n\n      console.log(`Caricamento bobine per cantiere ${comanda.id_cantiere}`);\n\n      // Carica tutte le bobine disponibili per il cantiere\n      const bobineData = await caviService.getBobineDisponibili(comanda.id_cantiere);\n      setBobineDisponibili(bobineData);\n\n      console.log(`Bobine caricate: ${bobineData.length}`);\n\n    } catch (err) {\n      console.error('Errore nel caricamento bobine:', err);\n\n      // Gestione specifica per errori di autenticazione\n      if (err.status === 401) {\n        setError('Sessione scaduta. Effettua nuovamente il login.');\n      } else if (err.isNetworkError) {\n        setError('Impossibile connettersi al server per caricare le bobine.');\n      } else {\n        setError('Errore nel caricamento delle bobine. Alcune funzionalità potrebbero non essere disponibili.');\n      }\n\n      // Imposta array vuoto per evitare errori nell'interfaccia\n      setBobineDisponibili([]);\n    } finally {\n      setLoadingBobine(false);\n    }\n  };\n\n  const handleMetriChange = (idCavo, value) => {\n    const numericValue = parseFloat(value) || 0;\n\n    // Aggiorna i dati di posa\n    setDatiPosa(prev => {\n      const newDatiPosa = {\n        ...prev,\n        [idCavo]: {\n          ...prev[idCavo],\n          metratura_reale: numericValue\n        }\n      };\n\n      // Validazione metri vs teorico\n      const cavo = cavi.find(c => c.id_cavo === idCavo);\n      let errors = [];\n\n      if (cavo && numericValue > cavo.metratura_teorica * 1.1) {\n        errors.push('Metratura superiore del 10% rispetto al teorico');\n      }\n\n      // Validazione metri vs bobina selezionata\n      const datiCavo = newDatiPosa[idCavo];\n      if (datiCavo?.id_bobina && datiCavo.id_bobina !== 'BOBINA_VUOTA') {\n        const bobina = bobineDisponibili.find(b => b.id_bobina === datiCavo.id_bobina);\n        if (bobina && numericValue > bobina.metri_residui && !datiCavo.force_over) {\n          errors.push(`Bobina ha solo ${bobina.metri_residui}m residui (richiesti ${numericValue}m)`);\n        }\n      }\n\n      // Aggiorna gli errori di validazione\n      setValidationErrors(prevErrors => {\n        if (errors.length > 0) {\n          return {\n            ...prevErrors,\n            [idCavo]: errors.join('; ')\n          };\n        } else {\n          const newErrors = { ...prevErrors };\n          delete newErrors[idCavo];\n          return newErrors;\n        }\n      });\n\n      return newDatiPosa;\n    });\n  };\n\n  const handleNoteChange = (idCavo, value) => {\n    setDatiPosa(prev => ({\n      ...prev,\n      [idCavo]: {\n        ...prev[idCavo],\n        note: value\n      }\n    }));\n  };\n\n  const handleBobinaChange = (idCavo, bobinaId) => {\n    setDatiPosa(prev => {\n      const newDatiPosa = {\n        ...prev,\n        [idCavo]: {\n          ...prev[idCavo],\n          id_bobina: bobinaId,\n          force_over: false // Reset force_over quando cambia bobina\n        }\n      };\n\n      // Rivalidazione dopo cambio bobina\n      const datiCavo = newDatiPosa[idCavo];\n      if (datiCavo?.metratura_reale) {\n        // Usa setTimeout per evitare problemi di stato asincrono\n        setTimeout(() => {\n          handleMetriChange(idCavo, datiCavo.metratura_reale);\n        }, 0);\n      }\n\n      return newDatiPosa;\n    });\n  };\n\n  const handleForceOverChange = (idCavo, forceOver) => {\n    setDatiPosa(prev => {\n      const newDatiPosa = {\n        ...prev,\n        [idCavo]: {\n          ...prev[idCavo],\n          force_over: forceOver\n        }\n      };\n\n      // Rivalidazione dopo cambio force_over\n      const datiCavo = newDatiPosa[idCavo];\n      if (datiCavo?.metratura_reale) {\n        // Usa setTimeout per evitare problemi di stato asincrono\n        setTimeout(() => {\n          handleMetriChange(idCavo, datiCavo.metratura_reale);\n        }, 0);\n      }\n\n      return newDatiPosa;\n    });\n  };\n\n  const handleSave = async () => {\n    try {\n      setLoading(true);\n      setError(null);\n\n      // Validazione finale\n      const hasErrors = Object.keys(validationErrors).length > 0;\n      if (hasErrors) {\n        setError('Correggere gli errori di validazione prima di salvare');\n        return;\n      }\n\n      // Salva i dati di posa con associazioni bobine\n      await comandeService.aggiornaDatiPosaConBobine(comanda.codice_comanda, datiPosa);\n      \n      onSuccess?.('Dati di posa salvati con successo');\n      onClose();\n      \n    } catch (err) {\n      console.error('Errore nel salvataggio:', err);\n      setError(err.message || 'Errore nel salvataggio dei dati');\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  const getTipoComandaColor = (tipo) => {\n    switch (tipo) {\n      case 'POSA': return 'primary';\n      case 'COLLEGAMENTO_PARTENZA': return 'warning';\n      case 'COLLEGAMENTO_ARRIVO': return 'info';\n      case 'CERTIFICAZIONE': return 'success';\n      default: return 'default';\n    }\n  };\n\n  const getTipoComandaLabel = (tipo) => {\n    switch (tipo) {\n      case 'POSA': return 'Posa';\n      case 'COLLEGAMENTO_PARTENZA': return 'Coll. Partenza';\n      case 'COLLEGAMENTO_ARRIVO': return 'Coll. Arrivo';\n      case 'CERTIFICAZIONE': return 'Certificazione';\n      default: return tipo;\n    }\n  };\n\n  // Filtra bobine compatibili per un cavo specifico\n  const getBobineCompatibili = (cavo) => {\n    if (!bobineDisponibili || bobineDisponibili.length === 0) {\n      return [];\n    }\n\n    return bobineDisponibili.filter(bobina =>\n      bobina.tipologia === cavo.tipologia &&\n      bobina.sezione === cavo.sezione &&\n      bobina.metri_residui > 0\n    );\n  };\n\n  // Verifica se una bobina è compatibile con un cavo\n  const isBobinaCompatibile = (bobina, cavo) => {\n    return bobina.tipologia === cavo.tipologia && bobina.sezione === cavo.sezione;\n  };\n\n  // Ottiene il colore per lo stato della bobina\n  const getBobinaStatusColor = (bobina, metriRichiesti) => {\n    if (!bobina || bobina.id_bobina === 'BOBINA_VUOTA') return 'default';\n    if (metriRichiesti > bobina.metri_residui) return 'error';\n    if (bobina.metri_residui < bobina.metri_totali * 0.1) return 'warning';\n    return 'success';\n  };\n\n  if (!comanda) return null;\n\n  return (\n    <Dialog \n      open={open} \n      onClose={onClose}\n      maxWidth=\"lg\"\n      fullWidth\n      PaperProps={{\n        sx: { minHeight: '70vh' }\n      }}\n    >\n      <DialogTitle>\n        <Box display=\"flex\" alignItems=\"center\" gap={2}>\n          <CableIcon color=\"primary\" />\n          <Box>\n            <Typography variant=\"h6\">\n              Inserimento Metri Posati\n            </Typography>\n            <Box display=\"flex\" alignItems=\"center\" gap={1} mt={1}>\n              <Typography variant=\"body2\" color=\"textSecondary\">\n                Comanda: {comanda.codice_comanda}\n              </Typography>\n              <Chip \n                label={getTipoComandaLabel(comanda.tipo_comanda)}\n                color={getTipoComandaColor(comanda.tipo_comanda)}\n                size=\"small\"\n              />\n              <Typography variant=\"body2\" color=\"textSecondary\">\n                Responsabile: {comanda.responsabile}\n              </Typography>\n            </Box>\n          </Box>\n        </Box>\n      </DialogTitle>\n\n      <DialogContent>\n        {error && (\n          <Alert severity=\"error\" sx={{ mb: 2 }}>\n            {error}\n          </Alert>\n        )}\n\n        {loading ? (\n          <Box display=\"flex\" justifyContent=\"center\" p={3}>\n            <Typography>Caricamento cavi...</Typography>\n          </Box>\n        ) : (\n          <TableContainer component={Paper} variant=\"outlined\">\n            <Table size=\"small\">\n              <TableHead>\n                <TableRow>\n                  <TableCell>ID Cavo</TableCell>\n                  <TableCell>Tipologia</TableCell>\n                  <TableCell>Formazione</TableCell>\n                  <TableCell>Metri Teorici</TableCell>\n                  <TableCell>Metri Reali</TableCell>\n                  <TableCell>Bobina</TableCell>\n                  <TableCell>Stato</TableCell>\n                  <TableCell>Note</TableCell>\n                </TableRow>\n              </TableHead>\n              <TableBody>\n                {cavi.map((cavo) => (\n                  <TableRow key={cavo.id_cavo}>\n                    <TableCell>\n                      <Typography variant=\"body2\" fontWeight=\"bold\">\n                        {cavo.id_cavo}\n                      </Typography>\n                    </TableCell>\n                    <TableCell>{cavo.tipologia || 'N/A'}</TableCell>\n                    <TableCell>{cavo.formazione || 'N/A'}</TableCell>\n                    <TableCell>\n                      <Typography variant=\"body2\">\n                        {cavo.metratura_teorica?.toFixed(1) || '0.0'} m\n                      </Typography>\n                    </TableCell>\n                    <TableCell>\n                      <TextField\n                        type=\"number\"\n                        size=\"small\"\n                        value={datiPosa[cavo.id_cavo]?.metratura_reale || 0}\n                        onChange={(e) => handleMetriChange(cavo.id_cavo, e.target.value)}\n                        error={!!validationErrors[cavo.id_cavo]}\n                        helperText={validationErrors[cavo.id_cavo]}\n                        inputProps={{\n                          min: 0,\n                          step: 0.1,\n                          style: { textAlign: 'right' }\n                        }}\n                        sx={{ width: 100 }}\n                      />\n                    </TableCell>\n                    <TableCell>\n                      <Box sx={{ minWidth: 200 }}>\n                        <Autocomplete\n                          size=\"small\"\n                          value={\n                            datiPosa[cavo.id_cavo]?.id_bobina === 'BOBINA_VUOTA'\n                              ? { id_bobina: 'BOBINA_VUOTA', tipologia: 'Vuota', metri_residui: '∞' }\n                              : bobineDisponibili.find(b => b.id_bobina === datiPosa[cavo.id_cavo]?.id_bobina) || null\n                          }\n                          onChange={(event, newValue) => {\n                            handleBobinaChange(cavo.id_cavo, newValue?.id_bobina || '');\n                          }}\n                          options={[\n                            { id_bobina: 'BOBINA_VUOTA', tipologia: 'Vuota', metri_residui: '∞' },\n                            ...getBobineCompatibili(cavo)\n                          ]}\n                          noOptionsText={\n                            loadingBobine\n                              ? \"Caricamento bobine...\"\n                              : bobineDisponibili.length === 0\n                                ? \"Nessuna bobina disponibile (verificare connessione)\"\n                                : \"Nessuna bobina compatibile\"\n                          }\n                          getOptionLabel={(option) => {\n                            if (option.id_bobina === 'BOBINA_VUOTA') return 'BOBINA_VUOTA';\n                            return `${option.id_bobina} (${option.metri_residui}m)`;\n                          }}\n                          renderInput={(params) => (\n                            <TextField\n                              {...params}\n                              placeholder=\"Seleziona bobina...\"\n                              variant=\"outlined\"\n                              size=\"small\"\n                            />\n                          )}\n                          renderOption={(props, option) => (\n                            <Box component=\"li\" {...props}>\n                              <ListItemIcon>\n                                <BobinaIcon\n                                  fontSize=\"small\"\n                                  color={\n                                    option.id_bobina === 'BOBINA_VUOTA'\n                                      ? 'default'\n                                      : getBobinaStatusColor(option, datiPosa[cavo.id_cavo]?.metratura_reale || 0)\n                                  }\n                                />\n                              </ListItemIcon>\n                              <ListItemText\n                                primary={option.id_bobina === 'BOBINA_VUOTA' ? 'BOBINA_VUOTA' : option.id_bobina}\n                                secondary={\n                                  option.id_bobina === 'BOBINA_VUOTA'\n                                    ? 'Nessuna bobina associata'\n                                    : `${option.tipologia} ${option.sezione} - ${option.metri_residui}m residui`\n                                }\n                              />\n                            </Box>\n                          )}\n                          loading={loadingBobine}\n                          disabled={loadingBobine}\n                          sx={{ width: '100%' }}\n                        />\n                        {/* Checkbox per force_over se necessario */}\n                        {datiPosa[cavo.id_cavo]?.id_bobina &&\n                         datiPosa[cavo.id_cavo]?.id_bobina !== 'BOBINA_VUOTA' &&\n                         (() => {\n                           const bobina = bobineDisponibili.find(b => b.id_bobina === datiPosa[cavo.id_cavo]?.id_bobina);\n                           const metriRichiesti = datiPosa[cavo.id_cavo]?.metratura_reale || 0;\n                           return bobina && metriRichiesti > bobina.metri_residui;\n                         })() && (\n                          <Box sx={{ mt: 1 }}>\n                            <Button\n                              size=\"small\"\n                              variant={datiPosa[cavo.id_cavo]?.force_over ? \"contained\" : \"outlined\"}\n                              color=\"warning\"\n                              onClick={() => handleForceOverChange(cavo.id_cavo, !datiPosa[cavo.id_cavo]?.force_over)}\n                              startIcon={<WarningIcon />}\n                            >\n                              Forza Over\n                            </Button>\n                          </Box>\n                        )}\n                      </Box>\n                    </TableCell>\n                    <TableCell>\n                      {cavo.stato_installazione === 'Installato' ? (\n                        <Chip \n                          icon={<CheckCircleIcon />}\n                          label=\"Installato\" \n                          color=\"success\" \n                          size=\"small\" \n                        />\n                      ) : (\n                        <Chip \n                          icon={<WarningIcon />}\n                          label=\"Da Installare\" \n                          color=\"warning\" \n                          size=\"small\" \n                        />\n                      )}\n                    </TableCell>\n                    <TableCell>\n                      <TextField\n                        size=\"small\"\n                        placeholder=\"Note opzionali...\"\n                        value={datiPosa[cavo.id_cavo]?.note || ''}\n                        onChange={(e) => handleNoteChange(cavo.id_cavo, e.target.value)}\n                        sx={{ width: 150 }}\n                      />\n                    </TableCell>\n                  </TableRow>\n                ))}\n              </TableBody>\n            </Table>\n          </TableContainer>\n        )}\n\n        {cavi.length === 0 && !loading && (\n          <Alert severity=\"info\" sx={{ mt: 2 }}>\n            Nessun cavo assegnato a questa comanda.\n          </Alert>\n        )}\n      </DialogContent>\n\n      <DialogActions sx={{ p: 2, gap: 1 }}>\n        <Button\n          onClick={onClose}\n          startIcon={<CancelIcon />}\n          disabled={loading}\n        >\n          Annulla\n        </Button>\n        <Button\n          onClick={handleSave}\n          variant=\"contained\"\n          startIcon={<SaveIcon />}\n          disabled={loading || cavi.length === 0}\n        >\n          Salva Dati Posa\n        </Button>\n      </DialogActions>\n    </Dialog>\n  );\n};\n\nexport default InserimentoMetriDialog;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAClD,SACEC,MAAM,EACNC,WAAW,EACXC,aAAa,EACbC,aAAa,EACbC,MAAM,EACNC,SAAS,EACTC,IAAI,EACJC,UAAU,EACVC,GAAG,EACHC,KAAK,EACLC,IAAI,EACJC,KAAK,EACLC,SAAS,EACTC,SAAS,EACTC,cAAc,EACdC,SAAS,EACTC,QAAQ,EACRC,KAAK,EACLC,UAAU,EACVC,OAAO,EACPC,YAAY,EACZC,WAAW,EACXC,UAAU,EACVC,MAAM,EACNC,QAAQ,EACRC,YAAY,EACZC,YAAY,QACP,eAAe;AACtB,SACEC,IAAI,IAAIC,QAAQ,EAChBC,MAAM,IAAIC,UAAU,EACpBC,KAAK,IAAIC,SAAS,EAClBC,WAAW,IAAIC,eAAe,EAC9BC,OAAO,IAAIC,WAAW,EACtBC,OAAO,IAAIC,UAAU,EACrBC,KAAK,IAAIC,SAAS,EAClBC,IAAI,IAAIC,QAAQ,QACX,qBAAqB;AAC5B,OAAOC,cAAc,MAAM,+BAA+B;AAC1D,OAAOC,WAAW,MAAM,4BAA4B;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAErD,MAAMC,sBAAsB,GAAGA,CAAC;EAC9BC,IAAI;EACJC,OAAO;EACPC,OAAO;EACPC;AACF,CAAC,KAAK;EAAAC,EAAA;EACJ,MAAM,CAACC,OAAO,EAAEC,UAAU,CAAC,GAAGxD,QAAQ,CAAC,KAAK,CAAC;EAC7C,MAAM,CAACyD,KAAK,EAAEC,QAAQ,CAAC,GAAG1D,QAAQ,CAAC,IAAI,CAAC;EACxC,MAAM,CAAC2D,IAAI,EAAEC,OAAO,CAAC,GAAG5D,QAAQ,CAAC,EAAE,CAAC;EACpC,MAAM,CAAC6D,QAAQ,EAAEC,WAAW,CAAC,GAAG9D,QAAQ,CAAC,CAAC,CAAC,CAAC;EAC5C,MAAM,CAAC+D,gBAAgB,EAAEC,mBAAmB,CAAC,GAAGhE,QAAQ,CAAC,CAAC,CAAC,CAAC;EAC5D,MAAM,CAACiE,iBAAiB,EAAEC,oBAAoB,CAAC,GAAGlE,QAAQ,CAAC,EAAE,CAAC;EAC9D,MAAM,CAACmE,aAAa,EAAEC,gBAAgB,CAAC,GAAGpE,QAAQ,CAAC,KAAK,CAAC;EAEzDC,SAAS,CAAC,MAAM;IACd,IAAIiD,IAAI,IAAIE,OAAO,EAAE;MACnBiB,eAAe,CAAC,CAAC;MACjBC,qBAAqB,CAAC,CAAC;IACzB;EACF,CAAC,EAAE,CAACpB,IAAI,EAAEE,OAAO,CAAC,CAAC;EAEnB,MAAMiB,eAAe,GAAG,MAAAA,CAAA,KAAY;IAClC,IAAI;MACFb,UAAU,CAAC,IAAI,CAAC;MAChBE,QAAQ,CAAC,IAAI,CAAC;MAEd,MAAMa,QAAQ,GAAG,MAAM1B,cAAc,CAAC2B,cAAc,CAACpB,OAAO,CAACqB,cAAc,CAAC;MAC5Eb,OAAO,CAACW,QAAQ,CAAC;;MAEjB;MACA,MAAMG,WAAW,GAAG,CAAC,CAAC;MACtBH,QAAQ,CAACI,OAAO,CAACC,IAAI,IAAI;QACvBF,WAAW,CAACE,IAAI,CAACC,OAAO,CAAC,GAAG;UAC1BC,eAAe,EAAEF,IAAI,CAACE,eAAe,IAAIF,IAAI,CAACG,iBAAiB,IAAI,CAAC;UACpEC,SAAS,EAAE,IAAIC,IAAI,CAAC,CAAC,CAACC,WAAW,CAAC,CAAC,CAACC,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC;UACjDC,iBAAiB,EAAEhC,OAAO,CAACiC,YAAY,IAAI,EAAE;UAC7CC,IAAI,EAAE,EAAE;UACRC,SAAS,EAAEX,IAAI,CAACW,SAAS,IAAI,EAAE;UAAE;UACjCC,UAAU,EAAE,KAAK,CAAC;QACpB,CAAC;MACH,CAAC,CAAC;MACF1B,WAAW,CAACY,WAAW,CAAC;IAE1B,CAAC,CAAC,OAAOe,GAAG,EAAE;MACZC,OAAO,CAACjC,KAAK,CAAC,8BAA8B,EAAEgC,GAAG,CAAC;MAClD/B,QAAQ,CAAC,+CAA+C,CAAC;IAC3D,CAAC,SAAS;MACRF,UAAU,CAAC,KAAK,CAAC;IACnB;EACF,CAAC;EAED,MAAMc,qBAAqB,GAAG,MAAAA,CAAA,KAAY;IACxC,IAAI;MACFF,gBAAgB,CAAC,IAAI,CAAC;;MAEtB;MACA,IAAI,EAAChB,OAAO,aAAPA,OAAO,eAAPA,OAAO,CAAEuC,WAAW,GAAE;QACzBD,OAAO,CAACE,IAAI,CAAC,uDAAuD,CAAC;QACrE;MACF;MAEAF,OAAO,CAACG,GAAG,CAAC,mCAAmCzC,OAAO,CAACuC,WAAW,EAAE,CAAC;;MAErE;MACA,MAAMG,UAAU,GAAG,MAAMhD,WAAW,CAACiD,oBAAoB,CAAC3C,OAAO,CAACuC,WAAW,CAAC;MAC9EzB,oBAAoB,CAAC4B,UAAU,CAAC;MAEhCJ,OAAO,CAACG,GAAG,CAAC,oBAAoBC,UAAU,CAACE,MAAM,EAAE,CAAC;IAEtD,CAAC,CAAC,OAAOP,GAAG,EAAE;MACZC,OAAO,CAACjC,KAAK,CAAC,gCAAgC,EAAEgC,GAAG,CAAC;;MAEpD;MACA,IAAIA,GAAG,CAACQ,MAAM,KAAK,GAAG,EAAE;QACtBvC,QAAQ,CAAC,iDAAiD,CAAC;MAC7D,CAAC,MAAM,IAAI+B,GAAG,CAACS,cAAc,EAAE;QAC7BxC,QAAQ,CAAC,2DAA2D,CAAC;MACvE,CAAC,MAAM;QACLA,QAAQ,CAAC,6FAA6F,CAAC;MACzG;;MAEA;MACAQ,oBAAoB,CAAC,EAAE,CAAC;IAC1B,CAAC,SAAS;MACRE,gBAAgB,CAAC,KAAK,CAAC;IACzB;EACF,CAAC;EAED,MAAM+B,iBAAiB,GAAGA,CAACC,MAAM,EAAEC,KAAK,KAAK;IAC3C,MAAMC,YAAY,GAAGC,UAAU,CAACF,KAAK,CAAC,IAAI,CAAC;;IAE3C;IACAvC,WAAW,CAAC0C,IAAI,IAAI;MAClB,MAAMC,WAAW,GAAG;QAClB,GAAGD,IAAI;QACP,CAACJ,MAAM,GAAG;UACR,GAAGI,IAAI,CAACJ,MAAM,CAAC;UACftB,eAAe,EAAEwB;QACnB;MACF,CAAC;;MAED;MACA,MAAM1B,IAAI,GAAGjB,IAAI,CAAC+C,IAAI,CAACC,CAAC,IAAIA,CAAC,CAAC9B,OAAO,KAAKuB,MAAM,CAAC;MACjD,IAAIQ,MAAM,GAAG,EAAE;MAEf,IAAIhC,IAAI,IAAI0B,YAAY,GAAG1B,IAAI,CAACG,iBAAiB,GAAG,GAAG,EAAE;QACvD6B,MAAM,CAACC,IAAI,CAAC,iDAAiD,CAAC;MAChE;;MAEA;MACA,MAAMC,QAAQ,GAAGL,WAAW,CAACL,MAAM,CAAC;MACpC,IAAIU,QAAQ,aAARA,QAAQ,eAARA,QAAQ,CAAEvB,SAAS,IAAIuB,QAAQ,CAACvB,SAAS,KAAK,cAAc,EAAE;QAChE,MAAMwB,MAAM,GAAG9C,iBAAiB,CAACyC,IAAI,CAACM,CAAC,IAAIA,CAAC,CAACzB,SAAS,KAAKuB,QAAQ,CAACvB,SAAS,CAAC;QAC9E,IAAIwB,MAAM,IAAIT,YAAY,GAAGS,MAAM,CAACE,aAAa,IAAI,CAACH,QAAQ,CAACtB,UAAU,EAAE;UACzEoB,MAAM,CAACC,IAAI,CAAC,kBAAkBE,MAAM,CAACE,aAAa,wBAAwBX,YAAY,IAAI,CAAC;QAC7F;MACF;;MAEA;MACAtC,mBAAmB,CAACkD,UAAU,IAAI;QAChC,IAAIN,MAAM,CAACZ,MAAM,GAAG,CAAC,EAAE;UACrB,OAAO;YACL,GAAGkB,UAAU;YACb,CAACd,MAAM,GAAGQ,MAAM,CAACO,IAAI,CAAC,IAAI;UAC5B,CAAC;QACH,CAAC,MAAM;UACL,MAAMC,SAAS,GAAG;YAAE,GAAGF;UAAW,CAAC;UACnC,OAAOE,SAAS,CAAChB,MAAM,CAAC;UACxB,OAAOgB,SAAS;QAClB;MACF,CAAC,CAAC;MAEF,OAAOX,WAAW;IACpB,CAAC,CAAC;EACJ,CAAC;EAED,MAAMY,gBAAgB,GAAGA,CAACjB,MAAM,EAAEC,KAAK,KAAK;IAC1CvC,WAAW,CAAC0C,IAAI,KAAK;MACnB,GAAGA,IAAI;MACP,CAACJ,MAAM,GAAG;QACR,GAAGI,IAAI,CAACJ,MAAM,CAAC;QACfd,IAAI,EAAEe;MACR;IACF,CAAC,CAAC,CAAC;EACL,CAAC;EAED,MAAMiB,kBAAkB,GAAGA,CAAClB,MAAM,EAAEmB,QAAQ,KAAK;IAC/CzD,WAAW,CAAC0C,IAAI,IAAI;MAClB,MAAMC,WAAW,GAAG;QAClB,GAAGD,IAAI;QACP,CAACJ,MAAM,GAAG;UACR,GAAGI,IAAI,CAACJ,MAAM,CAAC;UACfb,SAAS,EAAEgC,QAAQ;UACnB/B,UAAU,EAAE,KAAK,CAAC;QACpB;MACF,CAAC;;MAED;MACA,MAAMsB,QAAQ,GAAGL,WAAW,CAACL,MAAM,CAAC;MACpC,IAAIU,QAAQ,aAARA,QAAQ,eAARA,QAAQ,CAAEhC,eAAe,EAAE;QAC7B;QACA0C,UAAU,CAAC,MAAM;UACfrB,iBAAiB,CAACC,MAAM,EAAEU,QAAQ,CAAChC,eAAe,CAAC;QACrD,CAAC,EAAE,CAAC,CAAC;MACP;MAEA,OAAO2B,WAAW;IACpB,CAAC,CAAC;EACJ,CAAC;EAED,MAAMgB,qBAAqB,GAAGA,CAACrB,MAAM,EAAEsB,SAAS,KAAK;IACnD5D,WAAW,CAAC0C,IAAI,IAAI;MAClB,MAAMC,WAAW,GAAG;QAClB,GAAGD,IAAI;QACP,CAACJ,MAAM,GAAG;UACR,GAAGI,IAAI,CAACJ,MAAM,CAAC;UACfZ,UAAU,EAAEkC;QACd;MACF,CAAC;;MAED;MACA,MAAMZ,QAAQ,GAAGL,WAAW,CAACL,MAAM,CAAC;MACpC,IAAIU,QAAQ,aAARA,QAAQ,eAARA,QAAQ,CAAEhC,eAAe,EAAE;QAC7B;QACA0C,UAAU,CAAC,MAAM;UACfrB,iBAAiB,CAACC,MAAM,EAAEU,QAAQ,CAAChC,eAAe,CAAC;QACrD,CAAC,EAAE,CAAC,CAAC;MACP;MAEA,OAAO2B,WAAW;IACpB,CAAC,CAAC;EACJ,CAAC;EAED,MAAMkB,UAAU,GAAG,MAAAA,CAAA,KAAY;IAC7B,IAAI;MACFnE,UAAU,CAAC,IAAI,CAAC;MAChBE,QAAQ,CAAC,IAAI,CAAC;;MAEd;MACA,MAAMkE,SAAS,GAAGC,MAAM,CAACC,IAAI,CAAC/D,gBAAgB,CAAC,CAACiC,MAAM,GAAG,CAAC;MAC1D,IAAI4B,SAAS,EAAE;QACblE,QAAQ,CAAC,uDAAuD,CAAC;QACjE;MACF;;MAEA;MACA,MAAMb,cAAc,CAACkF,yBAAyB,CAAC3E,OAAO,CAACqB,cAAc,EAAEZ,QAAQ,CAAC;MAEhFR,SAAS,aAATA,SAAS,uBAATA,SAAS,CAAG,mCAAmC,CAAC;MAChDF,OAAO,CAAC,CAAC;IAEX,CAAC,CAAC,OAAOsC,GAAG,EAAE;MACZC,OAAO,CAACjC,KAAK,CAAC,yBAAyB,EAAEgC,GAAG,CAAC;MAC7C/B,QAAQ,CAAC+B,GAAG,CAACuC,OAAO,IAAI,iCAAiC,CAAC;IAC5D,CAAC,SAAS;MACRxE,UAAU,CAAC,KAAK,CAAC;IACnB;EACF,CAAC;EAED,MAAMyE,mBAAmB,GAAIC,IAAI,IAAK;IACpC,QAAQA,IAAI;MACV,KAAK,MAAM;QAAE,OAAO,SAAS;MAC7B,KAAK,uBAAuB;QAAE,OAAO,SAAS;MAC9C,KAAK,qBAAqB;QAAE,OAAO,MAAM;MACzC,KAAK,gBAAgB;QAAE,OAAO,SAAS;MACvC;QAAS,OAAO,SAAS;IAC3B;EACF,CAAC;EAED,MAAMC,mBAAmB,GAAID,IAAI,IAAK;IACpC,QAAQA,IAAI;MACV,KAAK,MAAM;QAAE,OAAO,MAAM;MAC1B,KAAK,uBAAuB;QAAE,OAAO,gBAAgB;MACrD,KAAK,qBAAqB;QAAE,OAAO,cAAc;MACjD,KAAK,gBAAgB;QAAE,OAAO,gBAAgB;MAC9C;QAAS,OAAOA,IAAI;IACtB;EACF,CAAC;;EAED;EACA,MAAME,oBAAoB,GAAIxD,IAAI,IAAK;IACrC,IAAI,CAACX,iBAAiB,IAAIA,iBAAiB,CAAC+B,MAAM,KAAK,CAAC,EAAE;MACxD,OAAO,EAAE;IACX;IAEA,OAAO/B,iBAAiB,CAACoE,MAAM,CAACtB,MAAM,IACpCA,MAAM,CAACuB,SAAS,KAAK1D,IAAI,CAAC0D,SAAS,IACnCvB,MAAM,CAACwB,OAAO,KAAK3D,IAAI,CAAC2D,OAAO,IAC/BxB,MAAM,CAACE,aAAa,GAAG,CACzB,CAAC;EACH,CAAC;;EAED;EACA,MAAMuB,mBAAmB,GAAGA,CAACzB,MAAM,EAAEnC,IAAI,KAAK;IAC5C,OAAOmC,MAAM,CAACuB,SAAS,KAAK1D,IAAI,CAAC0D,SAAS,IAAIvB,MAAM,CAACwB,OAAO,KAAK3D,IAAI,CAAC2D,OAAO;EAC/E,CAAC;;EAED;EACA,MAAME,oBAAoB,GAAGA,CAAC1B,MAAM,EAAE2B,cAAc,KAAK;IACvD,IAAI,CAAC3B,MAAM,IAAIA,MAAM,CAACxB,SAAS,KAAK,cAAc,EAAE,OAAO,SAAS;IACpE,IAAImD,cAAc,GAAG3B,MAAM,CAACE,aAAa,EAAE,OAAO,OAAO;IACzD,IAAIF,MAAM,CAACE,aAAa,GAAGF,MAAM,CAAC4B,YAAY,GAAG,GAAG,EAAE,OAAO,SAAS;IACtE,OAAO,SAAS;EAClB,CAAC;EAED,IAAI,CAACvF,OAAO,EAAE,OAAO,IAAI;EAEzB,oBACEJ,OAAA,CAAC9C,MAAM;IACLgD,IAAI,EAAEA,IAAK;IACXC,OAAO,EAAEA,OAAQ;IACjByF,QAAQ,EAAC,IAAI;IACbC,SAAS;IACTC,UAAU,EAAE;MACVC,EAAE,EAAE;QAAEC,SAAS,EAAE;MAAO;IAC1B,CAAE;IAAAC,QAAA,gBAEFjG,OAAA,CAAC7C,WAAW;MAAA8I,QAAA,eACVjG,OAAA,CAACtC,GAAG;QAACwI,OAAO,EAAC,MAAM;QAACC,UAAU,EAAC,QAAQ;QAACC,GAAG,EAAE,CAAE;QAAAH,QAAA,gBAC7CjG,OAAA,CAACd,SAAS;UAACmH,KAAK,EAAC;QAAS;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,eAC7BzG,OAAA,CAACtC,GAAG;UAAAuI,QAAA,gBACFjG,OAAA,CAACvC,UAAU;YAACiJ,OAAO,EAAC,IAAI;YAAAT,QAAA,EAAC;UAEzB;YAAAK,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAY,CAAC,eACbzG,OAAA,CAACtC,GAAG;YAACwI,OAAO,EAAC,MAAM;YAACC,UAAU,EAAC,QAAQ;YAACC,GAAG,EAAE,CAAE;YAACO,EAAE,EAAE,CAAE;YAAAV,QAAA,gBACpDjG,OAAA,CAACvC,UAAU;cAACiJ,OAAO,EAAC,OAAO;cAACL,KAAK,EAAC,eAAe;cAAAJ,QAAA,GAAC,WACvC,EAAC7F,OAAO,CAACqB,cAAc;YAAA;cAAA6E,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACtB,CAAC,eACbzG,OAAA,CAACpC,IAAI;cACHgJ,KAAK,EAAEzB,mBAAmB,CAAC/E,OAAO,CAACyG,YAAY,CAAE;cACjDR,KAAK,EAAEpB,mBAAmB,CAAC7E,OAAO,CAACyG,YAAY,CAAE;cACjDC,IAAI,EAAC;YAAO;cAAAR,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACb,CAAC,eACFzG,OAAA,CAACvC,UAAU;cAACiJ,OAAO,EAAC,OAAO;cAACL,KAAK,EAAC,eAAe;cAAAJ,QAAA,GAAC,gBAClC,EAAC7F,OAAO,CAACiC,YAAY;YAAA;cAAAiE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACzB,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACV,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACK,CAAC,eAEdzG,OAAA,CAAC5C,aAAa;MAAA6I,QAAA,GACXxF,KAAK,iBACJT,OAAA,CAACrC,KAAK;QAACoJ,QAAQ,EAAC,OAAO;QAAChB,EAAE,EAAE;UAAEiB,EAAE,EAAE;QAAE,CAAE;QAAAf,QAAA,EACnCxF;MAAK;QAAA6F,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACD,CACR,EAEAlG,OAAO,gBACNP,OAAA,CAACtC,GAAG;QAACwI,OAAO,EAAC,MAAM;QAACe,cAAc,EAAC,QAAQ;QAACC,CAAC,EAAE,CAAE;QAAAjB,QAAA,eAC/CjG,OAAA,CAACvC,UAAU;UAAAwI,QAAA,EAAC;QAAmB;UAAAK,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAY;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACzC,CAAC,gBAENzG,OAAA,CAAChC,cAAc;QAACmJ,SAAS,EAAEhJ,KAAM;QAACuI,OAAO,EAAC,UAAU;QAAAT,QAAA,eAClDjG,OAAA,CAACnC,KAAK;UAACiJ,IAAI,EAAC,OAAO;UAAAb,QAAA,gBACjBjG,OAAA,CAAC/B,SAAS;YAAAgI,QAAA,eACRjG,OAAA,CAAC9B,QAAQ;cAAA+H,QAAA,gBACPjG,OAAA,CAACjC,SAAS;gBAAAkI,QAAA,EAAC;cAAO;gBAAAK,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAW,CAAC,eAC9BzG,OAAA,CAACjC,SAAS;gBAAAkI,QAAA,EAAC;cAAS;gBAAAK,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAW,CAAC,eAChCzG,OAAA,CAACjC,SAAS;gBAAAkI,QAAA,EAAC;cAAU;gBAAAK,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAW,CAAC,eACjCzG,OAAA,CAACjC,SAAS;gBAAAkI,QAAA,EAAC;cAAa;gBAAAK,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAW,CAAC,eACpCzG,OAAA,CAACjC,SAAS;gBAAAkI,QAAA,EAAC;cAAW;gBAAAK,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAW,CAAC,eAClCzG,OAAA,CAACjC,SAAS;gBAAAkI,QAAA,EAAC;cAAM;gBAAAK,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAW,CAAC,eAC7BzG,OAAA,CAACjC,SAAS;gBAAAkI,QAAA,EAAC;cAAK;gBAAAK,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAW,CAAC,eAC5BzG,OAAA,CAACjC,SAAS;gBAAAkI,QAAA,EAAC;cAAI;gBAAAK,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAW,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACnB;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACF,CAAC,eACZzG,OAAA,CAAClC,SAAS;YAAAmI,QAAA,EACPtF,IAAI,CAACyG,GAAG,CAAExF,IAAI;cAAA,IAAAyF,qBAAA,EAAAC,qBAAA,EAAAC,sBAAA,EAAAC,sBAAA,EAAAC,sBAAA,EAAAC,sBAAA,EAAAC,sBAAA;cAAA,oBACb3H,OAAA,CAAC9B,QAAQ;gBAAA+H,QAAA,gBACPjG,OAAA,CAACjC,SAAS;kBAAAkI,QAAA,eACRjG,OAAA,CAACvC,UAAU;oBAACiJ,OAAO,EAAC,OAAO;oBAACkB,UAAU,EAAC,MAAM;oBAAA3B,QAAA,EAC1CrE,IAAI,CAACC;kBAAO;oBAAAyE,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACH;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACJ,CAAC,eACZzG,OAAA,CAACjC,SAAS;kBAAAkI,QAAA,EAAErE,IAAI,CAAC0D,SAAS,IAAI;gBAAK;kBAAAgB,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAY,CAAC,eAChDzG,OAAA,CAACjC,SAAS;kBAAAkI,QAAA,EAAErE,IAAI,CAACiG,UAAU,IAAI;gBAAK;kBAAAvB,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAY,CAAC,eACjDzG,OAAA,CAACjC,SAAS;kBAAAkI,QAAA,eACRjG,OAAA,CAACvC,UAAU;oBAACiJ,OAAO,EAAC,OAAO;oBAAAT,QAAA,GACxB,EAAAoB,qBAAA,GAAAzF,IAAI,CAACG,iBAAiB,cAAAsF,qBAAA,uBAAtBA,qBAAA,CAAwBS,OAAO,CAAC,CAAC,CAAC,KAAI,KAAK,EAAC,IAC/C;kBAAA;oBAAAxB,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAY;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACJ,CAAC,eACZzG,OAAA,CAACjC,SAAS;kBAAAkI,QAAA,eACRjG,OAAA,CAACzC,SAAS;oBACRwK,IAAI,EAAC,QAAQ;oBACbjB,IAAI,EAAC,OAAO;oBACZzD,KAAK,EAAE,EAAAiE,qBAAA,GAAAzG,QAAQ,CAACe,IAAI,CAACC,OAAO,CAAC,cAAAyF,qBAAA,uBAAtBA,qBAAA,CAAwBxF,eAAe,KAAI,CAAE;oBACpDkG,QAAQ,EAAGC,CAAC,IAAK9E,iBAAiB,CAACvB,IAAI,CAACC,OAAO,EAAEoG,CAAC,CAACC,MAAM,CAAC7E,KAAK,CAAE;oBACjE5C,KAAK,EAAE,CAAC,CAACM,gBAAgB,CAACa,IAAI,CAACC,OAAO,CAAE;oBACxCsG,UAAU,EAAEpH,gBAAgB,CAACa,IAAI,CAACC,OAAO,CAAE;oBAC3CuG,UAAU,EAAE;sBACVC,GAAG,EAAE,CAAC;sBACNC,IAAI,EAAE,GAAG;sBACTC,KAAK,EAAE;wBAAEC,SAAS,EAAE;sBAAQ;oBAC9B,CAAE;oBACFzC,EAAE,EAAE;sBAAE0C,KAAK,EAAE;oBAAI;kBAAE;oBAAAnC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACpB;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACO,CAAC,eACZzG,OAAA,CAACjC,SAAS;kBAAAkI,QAAA,eACRjG,OAAA,CAACtC,GAAG;oBAACqI,EAAE,EAAE;sBAAE2C,QAAQ,EAAE;oBAAI,CAAE;oBAAAzC,QAAA,gBACzBjG,OAAA,CAAC1B,YAAY;sBACXwI,IAAI,EAAC,OAAO;sBACZzD,KAAK,EACH,EAAAkE,sBAAA,GAAA1G,QAAQ,CAACe,IAAI,CAACC,OAAO,CAAC,cAAA0F,sBAAA,uBAAtBA,sBAAA,CAAwBhF,SAAS,MAAK,cAAc,GAChD;wBAAEA,SAAS,EAAE,cAAc;wBAAE+C,SAAS,EAAE,OAAO;wBAAErB,aAAa,EAAE;sBAAI,CAAC,GACrEhD,iBAAiB,CAACyC,IAAI,CAACM,CAAC;wBAAA,IAAA2E,sBAAA;wBAAA,OAAI3E,CAAC,CAACzB,SAAS,OAAAoG,sBAAA,GAAK9H,QAAQ,CAACe,IAAI,CAACC,OAAO,CAAC,cAAA8G,sBAAA,uBAAtBA,sBAAA,CAAwBpG,SAAS;sBAAA,EAAC,IAAI,IACvF;sBACDyF,QAAQ,EAAEA,CAACY,KAAK,EAAEC,QAAQ,KAAK;wBAC7BvE,kBAAkB,CAAC1C,IAAI,CAACC,OAAO,EAAE,CAAAgH,QAAQ,aAARA,QAAQ,uBAARA,QAAQ,CAAEtG,SAAS,KAAI,EAAE,CAAC;sBAC7D,CAAE;sBACFuG,OAAO,EAAE,CACP;wBAAEvG,SAAS,EAAE,cAAc;wBAAE+C,SAAS,EAAE,OAAO;wBAAErB,aAAa,EAAE;sBAAI,CAAC,EACrE,GAAGmB,oBAAoB,CAACxD,IAAI,CAAC,CAC7B;sBACFmH,aAAa,EACX5H,aAAa,GACT,uBAAuB,GACvBF,iBAAiB,CAAC+B,MAAM,KAAK,CAAC,GAC5B,qDAAqD,GACrD,4BACP;sBACDgG,cAAc,EAAGC,MAAM,IAAK;wBAC1B,IAAIA,MAAM,CAAC1G,SAAS,KAAK,cAAc,EAAE,OAAO,cAAc;wBAC9D,OAAO,GAAG0G,MAAM,CAAC1G,SAAS,KAAK0G,MAAM,CAAChF,aAAa,IAAI;sBACzD,CAAE;sBACFiF,WAAW,EAAGC,MAAM,iBAClBnJ,OAAA,CAACzC,SAAS;wBAAA,GACJ4L,MAAM;wBACVC,WAAW,EAAC,qBAAqB;wBACjC1C,OAAO,EAAC,UAAU;wBAClBI,IAAI,EAAC;sBAAO;wBAAAR,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACb,CACD;sBACF4C,YAAY,EAAEA,CAACC,KAAK,EAAEL,MAAM;wBAAA,IAAAM,sBAAA;wBAAA,oBAC1BvJ,OAAA,CAACtC,GAAG;0BAACyJ,SAAS,EAAC,IAAI;0BAAA,GAAKmC,KAAK;0BAAArD,QAAA,gBAC3BjG,OAAA,CAACpB,YAAY;4BAAAqH,QAAA,eACXjG,OAAA,CAACR,UAAU;8BACTgK,QAAQ,EAAC,OAAO;8BAChBnD,KAAK,EACH4C,MAAM,CAAC1G,SAAS,KAAK,cAAc,GAC/B,SAAS,GACTkD,oBAAoB,CAACwD,MAAM,EAAE,EAAAM,sBAAA,GAAA1I,QAAQ,CAACe,IAAI,CAACC,OAAO,CAAC,cAAA0H,sBAAA,uBAAtBA,sBAAA,CAAwBzH,eAAe,KAAI,CAAC;4BAC9E;8BAAAwE,QAAA,EAAAC,YAAA;8BAAAC,UAAA;8BAAAC,YAAA;4BAAA,OACF;0BAAC;4BAAAH,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OACU,CAAC,eACfzG,OAAA,CAACrB,YAAY;4BACX8K,OAAO,EAAER,MAAM,CAAC1G,SAAS,KAAK,cAAc,GAAG,cAAc,GAAG0G,MAAM,CAAC1G,SAAU;4BACjFmH,SAAS,EACPT,MAAM,CAAC1G,SAAS,KAAK,cAAc,GAC/B,0BAA0B,GAC1B,GAAG0G,MAAM,CAAC3D,SAAS,IAAI2D,MAAM,CAAC1D,OAAO,MAAM0D,MAAM,CAAChF,aAAa;0BACpE;4BAAAqC,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OACF,CAAC;wBAAA;0BAAAH,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OACC,CAAC;sBAAA,CACN;sBACFlG,OAAO,EAAEY,aAAc;sBACvBwI,QAAQ,EAAExI,aAAc;sBACxB4E,EAAE,EAAE;wBAAE0C,KAAK,EAAE;sBAAO;oBAAE;sBAAAnC,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACvB,CAAC,EAED,EAAAe,sBAAA,GAAA3G,QAAQ,CAACe,IAAI,CAACC,OAAO,CAAC,cAAA2F,sBAAA,uBAAtBA,sBAAA,CAAwBjF,SAAS,KACjC,EAAAkF,sBAAA,GAAA5G,QAAQ,CAACe,IAAI,CAACC,OAAO,CAAC,cAAA4F,sBAAA,uBAAtBA,sBAAA,CAAwBlF,SAAS,MAAK,cAAc,IACpD,CAACqH,sBAAA,IAAM;sBACL,MAAM7F,MAAM,GAAG9C,iBAAiB,CAACyC,IAAI,CAACM,CAAC;wBAAA,IAAA6F,sBAAA;wBAAA,OAAI7F,CAAC,CAACzB,SAAS,OAAAsH,sBAAA,GAAKhJ,QAAQ,CAACe,IAAI,CAACC,OAAO,CAAC,cAAAgI,sBAAA,uBAAtBA,sBAAA,CAAwBtH,SAAS;sBAAA,EAAC;sBAC7F,MAAMmD,cAAc,GAAG,EAAAkE,sBAAA,GAAA/I,QAAQ,CAACe,IAAI,CAACC,OAAO,CAAC,cAAA+H,sBAAA,uBAAtBA,sBAAA,CAAwB9H,eAAe,KAAI,CAAC;sBACnE,OAAOiC,MAAM,IAAI2B,cAAc,GAAG3B,MAAM,CAACE,aAAa;oBACxD,CAAC,EAAE,CAAC,iBACHjE,OAAA,CAACtC,GAAG;sBAACqI,EAAE,EAAE;wBAAEY,EAAE,EAAE;sBAAE,CAAE;sBAAAV,QAAA,eACjBjG,OAAA,CAAC1C,MAAM;wBACLwJ,IAAI,EAAC,OAAO;wBACZJ,OAAO,EAAE,CAAAgB,sBAAA,GAAA7G,QAAQ,CAACe,IAAI,CAACC,OAAO,CAAC,cAAA6F,sBAAA,eAAtBA,sBAAA,CAAwBlF,UAAU,GAAG,WAAW,GAAG,UAAW;wBACvE6D,KAAK,EAAC,SAAS;wBACfyD,OAAO,EAAEA,CAAA;0BAAA,IAAAC,sBAAA;0BAAA,OAAMtF,qBAAqB,CAAC7C,IAAI,CAACC,OAAO,EAAE,GAAAkI,sBAAA,GAAClJ,QAAQ,CAACe,IAAI,CAACC,OAAO,CAAC,cAAAkI,sBAAA,eAAtBA,sBAAA,CAAwBvH,UAAU,EAAC;wBAAA,CAAC;wBACxFwH,SAAS,eAAEhK,OAAA,CAACV,WAAW;0BAAAgH,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAE,CAAE;wBAAAR,QAAA,EAC5B;sBAED;wBAAAK,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAQ;oBAAC;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACN,CACN;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACE;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACG,CAAC,eACZzG,OAAA,CAACjC,SAAS;kBAAAkI,QAAA,EACPrE,IAAI,CAACqI,mBAAmB,KAAK,YAAY,gBACxCjK,OAAA,CAACpC,IAAI;oBACHsM,IAAI,eAAElK,OAAA,CAACZ,eAAe;sBAAAkH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAE,CAAE;oBAC1BG,KAAK,EAAC,YAAY;oBAClBP,KAAK,EAAC,SAAS;oBACfS,IAAI,EAAC;kBAAO;oBAAAR,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACb,CAAC,gBAEFzG,OAAA,CAACpC,IAAI;oBACHsM,IAAI,eAAElK,OAAA,CAACV,WAAW;sBAAAgH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAE,CAAE;oBACtBG,KAAK,EAAC,eAAe;oBACrBP,KAAK,EAAC,SAAS;oBACfS,IAAI,EAAC;kBAAO;oBAAAR,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACb;gBACF;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACQ,CAAC,eACZzG,OAAA,CAACjC,SAAS;kBAAAkI,QAAA,eACRjG,OAAA,CAACzC,SAAS;oBACRuJ,IAAI,EAAC,OAAO;oBACZsC,WAAW,EAAC,mBAAmB;oBAC/B/F,KAAK,EAAE,EAAAsE,sBAAA,GAAA9G,QAAQ,CAACe,IAAI,CAACC,OAAO,CAAC,cAAA8F,sBAAA,uBAAtBA,sBAAA,CAAwBrF,IAAI,KAAI,EAAG;oBAC1C0F,QAAQ,EAAGC,CAAC,IAAK5D,gBAAgB,CAACzC,IAAI,CAACC,OAAO,EAAEoG,CAAC,CAACC,MAAM,CAAC7E,KAAK,CAAE;oBAChE0C,EAAE,EAAE;sBAAE0C,KAAK,EAAE;oBAAI;kBAAE;oBAAAnC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACpB;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACO,CAAC;cAAA,GAzIC7E,IAAI,CAACC,OAAO;gBAAAyE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OA0IjB,CAAC;YAAA,CACZ;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACO,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACP;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACM,CACjB,EAEA9F,IAAI,CAACqC,MAAM,KAAK,CAAC,IAAI,CAACzC,OAAO,iBAC5BP,OAAA,CAACrC,KAAK;QAACoJ,QAAQ,EAAC,MAAM;QAAChB,EAAE,EAAE;UAAEY,EAAE,EAAE;QAAE,CAAE;QAAAV,QAAA,EAAC;MAEtC;QAAAK,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAO,CACR;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACY,CAAC,eAEhBzG,OAAA,CAAC3C,aAAa;MAAC0I,EAAE,EAAE;QAAEmB,CAAC,EAAE,CAAC;QAAEd,GAAG,EAAE;MAAE,CAAE;MAAAH,QAAA,gBAClCjG,OAAA,CAAC1C,MAAM;QACLwM,OAAO,EAAE3J,OAAQ;QACjB6J,SAAS,eAAEhK,OAAA,CAAChB,UAAU;UAAAsH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAE;QAC1BkD,QAAQ,EAAEpJ,OAAQ;QAAA0F,QAAA,EACnB;MAED;QAAAK,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAQ,CAAC,eACTzG,OAAA,CAAC1C,MAAM;QACLwM,OAAO,EAAEnF,UAAW;QACpB+B,OAAO,EAAC,WAAW;QACnBsD,SAAS,eAAEhK,OAAA,CAAClB,QAAQ;UAAAwH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAE;QACxBkD,QAAQ,EAAEpJ,OAAO,IAAII,IAAI,CAACqC,MAAM,KAAK,CAAE;QAAAiD,QAAA,EACxC;MAED;QAAAK,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAQ,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACI,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACV,CAAC;AAEb,CAAC;AAACnG,EAAA,CApfIL,sBAAsB;AAAAkK,EAAA,GAAtBlK,sBAAsB;AAsf5B,eAAeA,sBAAsB;AAAC,IAAAkK,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}