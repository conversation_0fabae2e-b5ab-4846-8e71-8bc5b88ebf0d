{"ast": null, "code": "var _jsxFileName = \"C:\\\\CMS\\\\webapp\\\\frontend\\\\src\\\\components\\\\charts\\\\TimelineChart.js\";\nimport React from 'react';\nimport { LineChart, Line, XAxis, YAxis, CartesianGrid, Tooltip, Legend, ResponsiveContainer, BarChart, Bar, ComposedChart, Area, AreaChart } from 'recharts';\nimport { Box, Typography, Grid, Paper, Chip } from '@mui/material';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst COLORS = {\n  primary: '#1976d2',\n  secondary: '#dc004e',\n  success: '#2e7d32',\n  warning: '#ed6c02',\n  info: '#0288d1',\n  error: '#d32f2f'\n};\nconst TimelineChart = ({\n  data\n}) => {\n  var _data$posa_giornalier, _stats$giorno_miglior;\n  if (!data) return null;\n\n  // Prepara dati per il grafico temporale\n  const timelineData = ((_data$posa_giornalier = data.posa_giornaliera) === null || _data$posa_giornalier === void 0 ? void 0 : _data$posa_giornalier.map(posa => ({\n    data: posa.data,\n    metri: posa.metri,\n    data_formatted: new Date(posa.data).toLocaleDateString('it-IT', {\n      day: '2-digit',\n      month: '2-digit'\n    })\n  }))) || [];\n\n  // Calcola media mobile (7 giorni)\n  const timelineWithMovingAverage = timelineData.map((item, index) => {\n    const start = Math.max(0, index - 6);\n    const end = index + 1;\n    const slice = timelineData.slice(start, end);\n    const average = slice.reduce((sum, day) => sum + day.metri, 0) / slice.length;\n    return {\n      ...item,\n      media_mobile: average\n    };\n  });\n\n  // Raggruppa per settimana\n  const weeklyData = [];\n  let currentWeek = null;\n  let weekMeters = 0;\n  let weekDays = 0;\n  timelineData.forEach((day, index) => {\n    const date = new Date(day.data);\n    const weekStart = new Date(date);\n    weekStart.setDate(date.getDate() - date.getDay() + 1); // Lunedì\n    const weekKey = weekStart.toISOString().split('T')[0];\n    if (currentWeek !== weekKey) {\n      if (currentWeek !== null) {\n        weeklyData.push({\n          settimana: `${currentWeek.split('-')[2]}/${currentWeek.split('-')[1]}`,\n          metri_totali: weekMeters,\n          giorni_attivi: weekDays,\n          media_giornaliera: weekDays > 0 ? weekMeters / weekDays : 0\n        });\n      }\n      currentWeek = weekKey;\n      weekMeters = day.metri;\n      weekDays = 1;\n    } else {\n      weekMeters += day.metri;\n      weekDays++;\n    }\n  });\n\n  // Aggiungi l'ultima settimana\n  if (currentWeek !== null) {\n    weeklyData.push({\n      settimana: `${currentWeek.split('-')[2]}/${currentWeek.split('-')[1]}`,\n      metri_totali: weekMeters,\n      giorni_attivi: weekDays,\n      media_giornaliera: weekDays > 0 ? weekMeters / weekDays : 0\n    });\n  }\n\n  // Statistiche del periodo\n  const stats = {\n    totale_metri: data.totale_metri_periodo || 0,\n    giorni_attivi: data.giorni_attivi || 0,\n    media_giornaliera: data.media_giornaliera || 0,\n    giorno_migliore: timelineData.reduce((max, day) => day.metri > max.metri ? day : max, {\n      metri: 0\n    }),\n    giorno_peggiore: timelineData.reduce((min, day) => day.metri < min.metri ? day : min, {\n      metri: Infinity\n    })\n  };\n  const CustomTooltip = ({\n    active,\n    payload,\n    label\n  }) => {\n    if (active && payload && payload.length) {\n      return /*#__PURE__*/_jsxDEV(Paper, {\n        sx: {\n          p: 1,\n          border: '1px solid #ccc'\n        },\n        children: [/*#__PURE__*/_jsxDEV(Typography, {\n          variant: \"body2\",\n          children: `Data: ${label}`\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 107,\n          columnNumber: 11\n        }, this), payload.map((entry, index) => {\n          var _entry$value;\n          return /*#__PURE__*/_jsxDEV(Typography, {\n            variant: \"body2\",\n            style: {\n              color: entry.color\n            },\n            children: `${entry.name}: ${(_entry$value = entry.value) === null || _entry$value === void 0 ? void 0 : _entry$value.toFixed(2)}m`\n          }, index, false, {\n            fileName: _jsxFileName,\n            lineNumber: 109,\n            columnNumber: 13\n          }, this);\n        })]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 106,\n        columnNumber: 9\n      }, this);\n    }\n    return null;\n  };\n  return /*#__PURE__*/_jsxDEV(Box, {\n    sx: {\n      mt: 3,\n      width: '100%'\n    },\n    children: [/*#__PURE__*/_jsxDEV(Typography, {\n      variant: \"h6\",\n      gutterBottom: true,\n      children: [\"Analisi Temporale Posa (\", data.data_inizio, \" - \", data.data_fine, \")\"]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 121,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Grid, {\n      container: true,\n      spacing: 4,\n      children: [/*#__PURE__*/_jsxDEV(Grid, {\n        item: true,\n        xs: 12,\n        children: /*#__PURE__*/_jsxDEV(Paper, {\n          sx: {\n            p: 2\n          },\n          children: [/*#__PURE__*/_jsxDEV(Typography, {\n            variant: \"subtitle1\",\n            gutterBottom: true,\n            children: \"Statistiche del Periodo\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 129,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Grid, {\n            container: true,\n            spacing: 2,\n            children: [/*#__PURE__*/_jsxDEV(Grid, {\n              item: true,\n              xs: 12,\n              sm: 6,\n              md: 3,\n              children: /*#__PURE__*/_jsxDEV(Box, {\n                sx: {\n                  textAlign: 'center',\n                  p: 1,\n                  border: '1px solid #e0e0e0',\n                  borderRadius: 1\n                },\n                children: [/*#__PURE__*/_jsxDEV(Typography, {\n                  variant: \"h6\",\n                  color: \"primary\",\n                  children: [stats.totale_metri.toFixed(0), \"m\"]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 135,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                  variant: \"body2\",\n                  children: \"Metri Totali\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 138,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 134,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 133,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Grid, {\n              item: true,\n              xs: 12,\n              sm: 6,\n              md: 3,\n              children: /*#__PURE__*/_jsxDEV(Box, {\n                sx: {\n                  textAlign: 'center',\n                  p: 1,\n                  border: '1px solid #e0e0e0',\n                  borderRadius: 1\n                },\n                children: [/*#__PURE__*/_jsxDEV(Typography, {\n                  variant: \"h6\",\n                  color: \"success.main\",\n                  children: stats.giorni_attivi\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 143,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                  variant: \"body2\",\n                  children: \"Giorni Attivi\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 146,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 142,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 141,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Grid, {\n              item: true,\n              xs: 12,\n              sm: 6,\n              md: 3,\n              children: /*#__PURE__*/_jsxDEV(Box, {\n                sx: {\n                  textAlign: 'center',\n                  p: 1,\n                  border: '1px solid #e0e0e0',\n                  borderRadius: 1\n                },\n                children: [/*#__PURE__*/_jsxDEV(Typography, {\n                  variant: \"h6\",\n                  color: \"info.main\",\n                  children: [stats.media_giornaliera.toFixed(1), \"m\"]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 151,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                  variant: \"body2\",\n                  children: \"Media Giornaliera\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 154,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 150,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 149,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Grid, {\n              item: true,\n              xs: 12,\n              sm: 6,\n              md: 3,\n              children: /*#__PURE__*/_jsxDEV(Box, {\n                sx: {\n                  textAlign: 'center',\n                  p: 1,\n                  border: '1px solid #e0e0e0',\n                  borderRadius: 1\n                },\n                children: [/*#__PURE__*/_jsxDEV(Typography, {\n                  variant: \"h6\",\n                  color: \"warning.main\",\n                  children: [(_stats$giorno_miglior = stats.giorno_migliore.metri) === null || _stats$giorno_miglior === void 0 ? void 0 : _stats$giorno_miglior.toFixed(0), \"m\"]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 159,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                  variant: \"body2\",\n                  children: \"Giorno Migliore\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 162,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                  variant: \"caption\",\n                  children: stats.giorno_migliore.data_formatted\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 163,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 158,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 157,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 132,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 128,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 127,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Grid, {\n        item: true,\n        xs: 12,\n        children: /*#__PURE__*/_jsxDEV(Paper, {\n          sx: {\n            p: 3,\n            height: 350,\n            borderRadius: 2\n          },\n          children: [/*#__PURE__*/_jsxDEV(Typography, {\n            variant: \"subtitle1\",\n            gutterBottom: true,\n            align: \"center\",\n            children: \"Trend Posa Giornaliera con Media Mobile (7 giorni)\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 175,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(ResponsiveContainer, {\n            width: \"100%\",\n            height: 300,\n            children: /*#__PURE__*/_jsxDEV(ComposedChart, {\n              data: timelineWithMovingAverage,\n              margin: {\n                top: 30,\n                right: 50,\n                left: 40,\n                bottom: 80\n              },\n              children: [/*#__PURE__*/_jsxDEV(CartesianGrid, {\n                strokeDasharray: \"3 3\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 180,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(XAxis, {\n                dataKey: \"data_formatted\",\n                angle: -45,\n                textAnchor: \"end\",\n                height: 80\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 181,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(YAxis, {}, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 187,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(Tooltip, {\n                content: /*#__PURE__*/_jsxDEV(CustomTooltip, {}, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 188,\n                  columnNumber: 35\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 188,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(Legend, {}, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 189,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(Bar, {\n                dataKey: \"metri\",\n                fill: COLORS.primary,\n                name: \"Metri Giornalieri\",\n                opacity: 0.7\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 190,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(Line, {\n                type: \"monotone\",\n                dataKey: \"media_mobile\",\n                stroke: COLORS.error,\n                strokeWidth: 3,\n                name: \"Media Mobile (7gg)\",\n                dot: false\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 191,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 179,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 178,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 174,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 173,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Grid, {\n        item: true,\n        xs: 12,\n        md: 6,\n        children: /*#__PURE__*/_jsxDEV(Paper, {\n          sx: {\n            p: 2,\n            height: 175\n          },\n          children: [/*#__PURE__*/_jsxDEV(Typography, {\n            variant: \"subtitle1\",\n            gutterBottom: true,\n            align: \"center\",\n            children: \"Progresso Cumulativo\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 207,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(ResponsiveContainer, {\n            width: \"100%\",\n            height: 140,\n            children: /*#__PURE__*/_jsxDEV(AreaChart, {\n              data: timelineData.map((item, index) => ({\n                ...item,\n                cumulativo: timelineData.slice(0, index + 1).reduce((sum, day) => sum + day.metri, 0)\n              })),\n              margin: {\n                top: 20,\n                right: 30,\n                left: 20,\n                bottom: 5\n              },\n              children: [/*#__PURE__*/_jsxDEV(CartesianGrid, {\n                strokeDasharray: \"3 3\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 215,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(XAxis, {\n                dataKey: \"data_formatted\",\n                angle: -45,\n                textAnchor: \"end\",\n                height: 80\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 216,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(YAxis, {}, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 217,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(Tooltip, {\n                content: /*#__PURE__*/_jsxDEV(CustomTooltip, {}, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 218,\n                  columnNumber: 35\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 218,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(Area, {\n                type: \"monotone\",\n                dataKey: \"cumulativo\",\n                stroke: COLORS.success,\n                fill: COLORS.success,\n                fillOpacity: 0.6,\n                name: \"Metri Cumulativi\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 219,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 211,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 210,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 206,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 205,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Grid, {\n        item: true,\n        xs: 12,\n        md: 6,\n        children: /*#__PURE__*/_jsxDEV(Paper, {\n          sx: {\n            p: 2,\n            height: 175\n          },\n          children: [/*#__PURE__*/_jsxDEV(Typography, {\n            variant: \"subtitle1\",\n            gutterBottom: true,\n            align: \"center\",\n            children: \"Performance Settimanale\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 235,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(ResponsiveContainer, {\n            width: \"100%\",\n            height: 140,\n            children: /*#__PURE__*/_jsxDEV(BarChart, {\n              data: weeklyData,\n              margin: {\n                top: 20,\n                right: 30,\n                left: 20,\n                bottom: 5\n              },\n              children: [/*#__PURE__*/_jsxDEV(CartesianGrid, {\n                strokeDasharray: \"3 3\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 240,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(XAxis, {\n                dataKey: \"settimana\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 241,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(YAxis, {}, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 242,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(Tooltip, {\n                content: /*#__PURE__*/_jsxDEV(CustomTooltip, {}, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 243,\n                  columnNumber: 35\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 243,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(Legend, {}, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 244,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(Bar, {\n                dataKey: \"metri_totali\",\n                fill: COLORS.info,\n                name: \"Metri Settimanali\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 245,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(Bar, {\n                dataKey: \"media_giornaliera\",\n                fill: COLORS.warning,\n                name: \"Media Giornaliera\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 246,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 239,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 238,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 234,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 233,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Grid, {\n        item: true,\n        xs: 12,\n        children: /*#__PURE__*/_jsxDEV(Paper, {\n          sx: {\n            p: 2\n          },\n          children: [/*#__PURE__*/_jsxDEV(Typography, {\n            variant: \"subtitle1\",\n            gutterBottom: true,\n            children: \"Analisi Performance Dettagliata\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 255,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Grid, {\n            container: true,\n            spacing: 2,\n            children: [/*#__PURE__*/_jsxDEV(Grid, {\n              item: true,\n              xs: 12,\n              md: 6,\n              children: [/*#__PURE__*/_jsxDEV(Typography, {\n                variant: \"subtitle2\",\n                gutterBottom: true,\n                children: \"Top 5 Giorni Migliori\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 261,\n                columnNumber: 17\n              }, this), timelineData.sort((a, b) => b.metri - a.metri).slice(0, 5).map((day, index) => /*#__PURE__*/_jsxDEV(Box, {\n                sx: {\n                  display: 'flex',\n                  justifyContent: 'space-between',\n                  alignItems: 'center',\n                  p: 1,\n                  mb: 1,\n                  border: '1px solid #e0e0e0',\n                  borderRadius: 1,\n                  borderLeft: `4px solid ${COLORS.success}`\n                },\n                children: [/*#__PURE__*/_jsxDEV(Typography, {\n                  variant: \"body2\",\n                  children: day.data\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 278,\n                  columnNumber: 23\n                }, this), /*#__PURE__*/_jsxDEV(Chip, {\n                  label: `${day.metri.toFixed(0)}m`,\n                  color: \"success\",\n                  size: \"small\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 279,\n                  columnNumber: 23\n                }, this)]\n              }, index, true, {\n                fileName: _jsxFileName,\n                lineNumber: 268,\n                columnNumber: 21\n              }, this))]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 260,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Grid, {\n              item: true,\n              xs: 12,\n              md: 6,\n              children: [/*#__PURE__*/_jsxDEV(Typography, {\n                variant: \"subtitle2\",\n                gutterBottom: true,\n                children: \"Top 3 Settimane Migliori\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 290,\n                columnNumber: 17\n              }, this), weeklyData.sort((a, b) => b.metri_totali - a.metri_totali).slice(0, 3).map((week, index) => /*#__PURE__*/_jsxDEV(Box, {\n                sx: {\n                  display: 'flex',\n                  justifyContent: 'space-between',\n                  alignItems: 'center',\n                  p: 1,\n                  mb: 1,\n                  border: '1px solid #e0e0e0',\n                  borderRadius: 1,\n                  borderLeft: `4px solid ${COLORS.info}`\n                },\n                children: [/*#__PURE__*/_jsxDEV(Box, {\n                  children: [/*#__PURE__*/_jsxDEV(Typography, {\n                    variant: \"body2\",\n                    children: [\"Settimana \", week.settimana]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 308,\n                    columnNumber: 25\n                  }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                    variant: \"caption\",\n                    children: [week.giorni_attivi, \" giorni attivi\"]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 309,\n                    columnNumber: 25\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 307,\n                  columnNumber: 23\n                }, this), /*#__PURE__*/_jsxDEV(Box, {\n                  sx: {\n                    textAlign: 'right'\n                  },\n                  children: [/*#__PURE__*/_jsxDEV(Chip, {\n                    label: `${week.metri_totali.toFixed(0)}m`,\n                    color: \"info\",\n                    size: \"small\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 314,\n                    columnNumber: 25\n                  }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                    variant: \"caption\",\n                    display: \"block\",\n                    children: [\"Media: \", week.media_giornaliera.toFixed(1), \"m/g\"]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 319,\n                    columnNumber: 25\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 313,\n                  columnNumber: 23\n                }, this)]\n              }, index, true, {\n                fileName: _jsxFileName,\n                lineNumber: 297,\n                columnNumber: 21\n              }, this))]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 289,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 258,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 254,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 253,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 125,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 120,\n    columnNumber: 5\n  }, this);\n};\n_c = TimelineChart;\nexport default TimelineChart;\nvar _c;\n$RefreshReg$(_c, \"TimelineChart\");", "map": {"version": 3, "names": ["React", "Line<PERSON>hart", "Line", "XAxis", "YA<PERSON>s", "Cartesian<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "Legend", "ResponsiveContainer", "<PERSON><PERSON><PERSON>", "Bar", "ComposedChart", "Area", "AreaChart", "Box", "Typography", "Grid", "Paper", "Chip", "jsxDEV", "_jsxDEV", "COLORS", "primary", "secondary", "success", "warning", "info", "error", "TimelineChart", "data", "_data$posa_giornalier", "_stats$giorno_miglior", "timelineData", "posa_giornal<PERSON>", "map", "posa", "metri", "data_formatted", "Date", "toLocaleDateString", "day", "month", "timelineWithMovingAverage", "item", "index", "start", "Math", "max", "end", "slice", "average", "reduce", "sum", "length", "media_mobile", "weeklyData", "currentWeek", "weekMeters", "weekDays", "for<PERSON>ach", "date", "weekStart", "setDate", "getDate", "getDay", "<PERSON><PERSON><PERSON>", "toISOString", "split", "push", "<PERSON><PERSON><PERSON>", "metri_totali", "giorni_attivi", "media_giornaliera", "stats", "totale_metri", "totale_metri_periodo", "giorno_migliore", "giorno_peggiore", "min", "Infinity", "CustomTooltip", "active", "payload", "label", "sx", "p", "border", "children", "variant", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "entry", "_entry$value", "style", "color", "name", "value", "toFixed", "mt", "width", "gutterBottom", "data_inizio", "data_fine", "container", "spacing", "xs", "sm", "md", "textAlign", "borderRadius", "height", "align", "margin", "top", "right", "left", "bottom", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "dataKey", "angle", "textAnchor", "content", "fill", "opacity", "type", "stroke", "strokeWidth", "dot", "cumulativo", "fillOpacity", "sort", "a", "b", "display", "justifyContent", "alignItems", "mb", "borderLeft", "size", "week", "_c", "$RefreshReg$"], "sources": ["C:/CMS/webapp/frontend/src/components/charts/TimelineChart.js"], "sourcesContent": ["import React from 'react';\nimport {\n  LineChart,\n  Line,\n  XAxis,\n  YAxis,\n  CartesianGrid,\n  Tooltip,\n  Legend,\n  ResponsiveContainer,\n  BarChart,\n  Bar,\n  ComposedChart,\n  Area,\n  AreaChart\n} from 'recharts';\nimport { Box, Typography, Grid, Paper, Chip } from '@mui/material';\n\nconst COLORS = {\n  primary: '#1976d2',\n  secondary: '#dc004e',\n  success: '#2e7d32',\n  warning: '#ed6c02',\n  info: '#0288d1',\n  error: '#d32f2f'\n};\n\nconst TimelineChart = ({ data }) => {\n  if (!data) return null;\n\n  // Prepara dati per il grafico temporale\n  const timelineData = data.posa_giornaliera?.map(posa => ({\n    data: posa.data,\n    metri: posa.metri,\n    data_formatted: new Date(posa.data).toLocaleDateString('it-IT', { \n      day: '2-digit', \n      month: '2-digit' \n    })\n  })) || [];\n\n  // Calcola media mobile (7 giorni)\n  const timelineWithMovingAverage = timelineData.map((item, index) => {\n    const start = Math.max(0, index - 6);\n    const end = index + 1;\n    const slice = timelineData.slice(start, end);\n    const average = slice.reduce((sum, day) => sum + day.metri, 0) / slice.length;\n\n    return {\n      ...item,\n      media_mobile: average\n    };\n  });\n\n  // Raggruppa per settimana\n  const weeklyData = [];\n  let currentWeek = null;\n  let weekMeters = 0;\n  let weekDays = 0;\n\n  timelineData.forEach((day, index) => {\n    const date = new Date(day.data);\n    const weekStart = new Date(date);\n    weekStart.setDate(date.getDate() - date.getDay() + 1); // Lunedì\n    const weekKey = weekStart.toISOString().split('T')[0];\n\n    if (currentWeek !== weekKey) {\n      if (currentWeek !== null) {\n        weeklyData.push({\n          settimana: `${currentWeek.split('-')[2]}/${currentWeek.split('-')[1]}`,\n          metri_totali: weekMeters,\n          giorni_attivi: weekDays,\n          media_giornaliera: weekDays > 0 ? weekMeters / weekDays : 0\n        });\n      }\n      currentWeek = weekKey;\n      weekMeters = day.metri;\n      weekDays = 1;\n    } else {\n      weekMeters += day.metri;\n      weekDays++;\n    }\n  });\n\n  // Aggiungi l'ultima settimana\n  if (currentWeek !== null) {\n    weeklyData.push({\n      settimana: `${currentWeek.split('-')[2]}/${currentWeek.split('-')[1]}`,\n      metri_totali: weekMeters,\n      giorni_attivi: weekDays,\n      media_giornaliera: weekDays > 0 ? weekMeters / weekDays : 0\n    });\n  }\n\n  // Statistiche del periodo\n  const stats = {\n    totale_metri: data.totale_metri_periodo || 0,\n    giorni_attivi: data.giorni_attivi || 0,\n    media_giornaliera: data.media_giornaliera || 0,\n    giorno_migliore: timelineData.reduce((max, day) => day.metri > max.metri ? day : max, { metri: 0 }),\n    giorno_peggiore: timelineData.reduce((min, day) => day.metri < min.metri ? day : min, { metri: Infinity })\n  };\n\n  const CustomTooltip = ({ active, payload, label }) => {\n    if (active && payload && payload.length) {\n      return (\n        <Paper sx={{ p: 1, border: '1px solid #ccc' }}>\n          <Typography variant=\"body2\">{`Data: ${label}`}</Typography>\n          {payload.map((entry, index) => (\n            <Typography key={index} variant=\"body2\" style={{ color: entry.color }}>\n              {`${entry.name}: ${entry.value?.toFixed(2)}m`}\n            </Typography>\n          ))}\n        </Paper>\n      );\n    }\n    return null;\n  };\n\n  return (\n    <Box sx={{ mt: 3, width: '100%' }}>\n      <Typography variant=\"h6\" gutterBottom>\n        Analisi Temporale Posa ({data.data_inizio} - {data.data_fine})\n      </Typography>\n\n      <Grid container spacing={4}>\n        {/* Statistiche Periodo */}\n        <Grid item xs={12}>\n          <Paper sx={{ p: 2 }}>\n            <Typography variant=\"subtitle1\" gutterBottom>\n              Statistiche del Periodo\n            </Typography>\n            <Grid container spacing={2}>\n              <Grid item xs={12} sm={6} md={3}>\n                <Box sx={{ textAlign: 'center', p: 1, border: '1px solid #e0e0e0', borderRadius: 1 }}>\n                  <Typography variant=\"h6\" color=\"primary\">\n                    {stats.totale_metri.toFixed(0)}m\n                  </Typography>\n                  <Typography variant=\"body2\">Metri Totali</Typography>\n                </Box>\n              </Grid>\n              <Grid item xs={12} sm={6} md={3}>\n                <Box sx={{ textAlign: 'center', p: 1, border: '1px solid #e0e0e0', borderRadius: 1 }}>\n                  <Typography variant=\"h6\" color=\"success.main\">\n                    {stats.giorni_attivi}\n                  </Typography>\n                  <Typography variant=\"body2\">Giorni Attivi</Typography>\n                </Box>\n              </Grid>\n              <Grid item xs={12} sm={6} md={3}>\n                <Box sx={{ textAlign: 'center', p: 1, border: '1px solid #e0e0e0', borderRadius: 1 }}>\n                  <Typography variant=\"h6\" color=\"info.main\">\n                    {stats.media_giornaliera.toFixed(1)}m\n                  </Typography>\n                  <Typography variant=\"body2\">Media Giornaliera</Typography>\n                </Box>\n              </Grid>\n              <Grid item xs={12} sm={6} md={3}>\n                <Box sx={{ textAlign: 'center', p: 1, border: '1px solid #e0e0e0', borderRadius: 1 }}>\n                  <Typography variant=\"h6\" color=\"warning.main\">\n                    {stats.giorno_migliore.metri?.toFixed(0)}m\n                  </Typography>\n                  <Typography variant=\"body2\">Giorno Migliore</Typography>\n                  <Typography variant=\"caption\">\n                    {stats.giorno_migliore.data_formatted}\n                  </Typography>\n                </Box>\n              </Grid>\n            </Grid>\n          </Paper>\n        </Grid>\n\n        {/* Grafico Temporale Giornaliero */}\n        <Grid item xs={12}>\n          <Paper sx={{ p: 3, height: 350, borderRadius: 2 }}>\n            <Typography variant=\"subtitle1\" gutterBottom align=\"center\">\n              Trend Posa Giornaliera con Media Mobile (7 giorni)\n            </Typography>\n            <ResponsiveContainer width=\"100%\" height={300}>\n              <ComposedChart data={timelineWithMovingAverage} margin={{ top: 30, right: 50, left: 40, bottom: 80 }}>\n                <CartesianGrid strokeDasharray=\"3 3\" />\n                <XAxis \n                  dataKey=\"data_formatted\" \n                  angle={-45} \n                  textAnchor=\"end\" \n                  height={80}\n                />\n                <YAxis />\n                <Tooltip content={<CustomTooltip />} />\n                <Legend />\n                <Bar dataKey=\"metri\" fill={COLORS.primary} name=\"Metri Giornalieri\" opacity={0.7} />\n                <Line \n                  type=\"monotone\" \n                  dataKey=\"media_mobile\" \n                  stroke={COLORS.error} \n                  strokeWidth={3}\n                  name=\"Media Mobile (7gg)\"\n                  dot={false}\n                />\n              </ComposedChart>\n            </ResponsiveContainer>\n          </Paper>\n        </Grid>\n\n        {/* Grafico Area Cumulativo */}\n        <Grid item xs={12} md={6}>\n          <Paper sx={{ p: 2, height: 175 }}>\n            <Typography variant=\"subtitle1\" gutterBottom align=\"center\">\n              Progresso Cumulativo\n            </Typography>\n            <ResponsiveContainer width=\"100%\" height={140}>\n              <AreaChart data={timelineData.map((item, index) => ({\n                ...item,\n                cumulativo: timelineData.slice(0, index + 1).reduce((sum, day) => sum + day.metri, 0)\n              }))} margin={{ top: 20, right: 30, left: 20, bottom: 5 }}>\n                <CartesianGrid strokeDasharray=\"3 3\" />\n                <XAxis dataKey=\"data_formatted\" angle={-45} textAnchor=\"end\" height={80} />\n                <YAxis />\n                <Tooltip content={<CustomTooltip />} />\n                <Area \n                  type=\"monotone\" \n                  dataKey=\"cumulativo\" \n                  stroke={COLORS.success} \n                  fill={COLORS.success}\n                  fillOpacity={0.6}\n                  name=\"Metri Cumulativi\"\n                />\n              </AreaChart>\n            </ResponsiveContainer>\n          </Paper>\n        </Grid>\n\n        {/* Grafico Settimanale */}\n        <Grid item xs={12} md={6}>\n          <Paper sx={{ p: 2, height: 175 }}>\n            <Typography variant=\"subtitle1\" gutterBottom align=\"center\">\n              Performance Settimanale\n            </Typography>\n            <ResponsiveContainer width=\"100%\" height={140}>\n              <BarChart data={weeklyData} margin={{ top: 20, right: 30, left: 20, bottom: 5 }}>\n                <CartesianGrid strokeDasharray=\"3 3\" />\n                <XAxis dataKey=\"settimana\" />\n                <YAxis />\n                <Tooltip content={<CustomTooltip />} />\n                <Legend />\n                <Bar dataKey=\"metri_totali\" fill={COLORS.info} name=\"Metri Settimanali\" />\n                <Bar dataKey=\"media_giornaliera\" fill={COLORS.warning} name=\"Media Giornaliera\" />\n              </BarChart>\n            </ResponsiveContainer>\n          </Paper>\n        </Grid>\n\n        {/* Analisi Performance */}\n        <Grid item xs={12}>\n          <Paper sx={{ p: 2 }}>\n            <Typography variant=\"subtitle1\" gutterBottom>\n              Analisi Performance Dettagliata\n            </Typography>\n            <Grid container spacing={2}>\n              {/* Top 5 giorni migliori */}\n              <Grid item xs={12} md={6}>\n                <Typography variant=\"subtitle2\" gutterBottom>\n                  Top 5 Giorni Migliori\n                </Typography>\n                {timelineData\n                  .sort((a, b) => b.metri - a.metri)\n                  .slice(0, 5)\n                  .map((day, index) => (\n                    <Box key={index} sx={{ \n                      display: 'flex', \n                      justifyContent: 'space-between', \n                      alignItems: 'center',\n                      p: 1, \n                      mb: 1,\n                      border: '1px solid #e0e0e0', \n                      borderRadius: 1,\n                      borderLeft: `4px solid ${COLORS.success}`\n                    }}>\n                      <Typography variant=\"body2\">{day.data}</Typography>\n                      <Chip \n                        label={`${day.metri.toFixed(0)}m`}\n                        color=\"success\"\n                        size=\"small\"\n                      />\n                    </Box>\n                  ))}\n              </Grid>\n\n              {/* Settimane migliori */}\n              <Grid item xs={12} md={6}>\n                <Typography variant=\"subtitle2\" gutterBottom>\n                  Top 3 Settimane Migliori\n                </Typography>\n                {weeklyData\n                  .sort((a, b) => b.metri_totali - a.metri_totali)\n                  .slice(0, 3)\n                  .map((week, index) => (\n                    <Box key={index} sx={{ \n                      display: 'flex', \n                      justifyContent: 'space-between', \n                      alignItems: 'center',\n                      p: 1, \n                      mb: 1,\n                      border: '1px solid #e0e0e0', \n                      borderRadius: 1,\n                      borderLeft: `4px solid ${COLORS.info}`\n                    }}>\n                      <Box>\n                        <Typography variant=\"body2\">Settimana {week.settimana}</Typography>\n                        <Typography variant=\"caption\">\n                          {week.giorni_attivi} giorni attivi\n                        </Typography>\n                      </Box>\n                      <Box sx={{ textAlign: 'right' }}>\n                        <Chip \n                          label={`${week.metri_totali.toFixed(0)}m`}\n                          color=\"info\"\n                          size=\"small\"\n                        />\n                        <Typography variant=\"caption\" display=\"block\">\n                          Media: {week.media_giornaliera.toFixed(1)}m/g\n                        </Typography>\n                      </Box>\n                    </Box>\n                  ))}\n              </Grid>\n            </Grid>\n          </Paper>\n        </Grid>\n      </Grid>\n    </Box>\n  );\n};\n\nexport default TimelineChart;\n"], "mappings": ";AAAA,OAAOA,KAAK,MAAM,OAAO;AACzB,SACEC,SAAS,EACTC,IAAI,EACJC,KAAK,EACLC,KAAK,EACLC,aAAa,EACbC,OAAO,EACPC,MAAM,EACNC,mBAAmB,EACnBC,QAAQ,EACRC,GAAG,EACHC,aAAa,EACbC,IAAI,EACJC,SAAS,QACJ,UAAU;AACjB,SAASC,GAAG,EAAEC,UAAU,EAAEC,IAAI,EAAEC,KAAK,EAAEC,IAAI,QAAQ,eAAe;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAEnE,MAAMC,MAAM,GAAG;EACbC,OAAO,EAAE,SAAS;EAClBC,SAAS,EAAE,SAAS;EACpBC,OAAO,EAAE,SAAS;EAClBC,OAAO,EAAE,SAAS;EAClBC,IAAI,EAAE,SAAS;EACfC,KAAK,EAAE;AACT,CAAC;AAED,MAAMC,aAAa,GAAGA,CAAC;EAAEC;AAAK,CAAC,KAAK;EAAA,IAAAC,qBAAA,EAAAC,qBAAA;EAClC,IAAI,CAACF,IAAI,EAAE,OAAO,IAAI;;EAEtB;EACA,MAAMG,YAAY,GAAG,EAAAF,qBAAA,GAAAD,IAAI,CAACI,gBAAgB,cAAAH,qBAAA,uBAArBA,qBAAA,CAAuBI,GAAG,CAACC,IAAI,KAAK;IACvDN,IAAI,EAAEM,IAAI,CAACN,IAAI;IACfO,KAAK,EAAED,IAAI,CAACC,KAAK;IACjBC,cAAc,EAAE,IAAIC,IAAI,CAACH,IAAI,CAACN,IAAI,CAAC,CAACU,kBAAkB,CAAC,OAAO,EAAE;MAC9DC,GAAG,EAAE,SAAS;MACdC,KAAK,EAAE;IACT,CAAC;EACH,CAAC,CAAC,CAAC,KAAI,EAAE;;EAET;EACA,MAAMC,yBAAyB,GAAGV,YAAY,CAACE,GAAG,CAAC,CAACS,IAAI,EAAEC,KAAK,KAAK;IAClE,MAAMC,KAAK,GAAGC,IAAI,CAACC,GAAG,CAAC,CAAC,EAAEH,KAAK,GAAG,CAAC,CAAC;IACpC,MAAMI,GAAG,GAAGJ,KAAK,GAAG,CAAC;IACrB,MAAMK,KAAK,GAAGjB,YAAY,CAACiB,KAAK,CAACJ,KAAK,EAAEG,GAAG,CAAC;IAC5C,MAAME,OAAO,GAAGD,KAAK,CAACE,MAAM,CAAC,CAACC,GAAG,EAAEZ,GAAG,KAAKY,GAAG,GAAGZ,GAAG,CAACJ,KAAK,EAAE,CAAC,CAAC,GAAGa,KAAK,CAACI,MAAM;IAE7E,OAAO;MACL,GAAGV,IAAI;MACPW,YAAY,EAAEJ;IAChB,CAAC;EACH,CAAC,CAAC;;EAEF;EACA,MAAMK,UAAU,GAAG,EAAE;EACrB,IAAIC,WAAW,GAAG,IAAI;EACtB,IAAIC,UAAU,GAAG,CAAC;EAClB,IAAIC,QAAQ,GAAG,CAAC;EAEhB1B,YAAY,CAAC2B,OAAO,CAAC,CAACnB,GAAG,EAAEI,KAAK,KAAK;IACnC,MAAMgB,IAAI,GAAG,IAAItB,IAAI,CAACE,GAAG,CAACX,IAAI,CAAC;IAC/B,MAAMgC,SAAS,GAAG,IAAIvB,IAAI,CAACsB,IAAI,CAAC;IAChCC,SAAS,CAACC,OAAO,CAACF,IAAI,CAACG,OAAO,CAAC,CAAC,GAAGH,IAAI,CAACI,MAAM,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC;IACvD,MAAMC,OAAO,GAAGJ,SAAS,CAACK,WAAW,CAAC,CAAC,CAACC,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC;IAErD,IAAIX,WAAW,KAAKS,OAAO,EAAE;MAC3B,IAAIT,WAAW,KAAK,IAAI,EAAE;QACxBD,UAAU,CAACa,IAAI,CAAC;UACdC,SAAS,EAAE,GAAGb,WAAW,CAACW,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,IAAIX,WAAW,CAACW,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,EAAE;UACtEG,YAAY,EAAEb,UAAU;UACxBc,aAAa,EAAEb,QAAQ;UACvBc,iBAAiB,EAAEd,QAAQ,GAAG,CAAC,GAAGD,UAAU,GAAGC,QAAQ,GAAG;QAC5D,CAAC,CAAC;MACJ;MACAF,WAAW,GAAGS,OAAO;MACrBR,UAAU,GAAGjB,GAAG,CAACJ,KAAK;MACtBsB,QAAQ,GAAG,CAAC;IACd,CAAC,MAAM;MACLD,UAAU,IAAIjB,GAAG,CAACJ,KAAK;MACvBsB,QAAQ,EAAE;IACZ;EACF,CAAC,CAAC;;EAEF;EACA,IAAIF,WAAW,KAAK,IAAI,EAAE;IACxBD,UAAU,CAACa,IAAI,CAAC;MACdC,SAAS,EAAE,GAAGb,WAAW,CAACW,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,IAAIX,WAAW,CAACW,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,EAAE;MACtEG,YAAY,EAAEb,UAAU;MACxBc,aAAa,EAAEb,QAAQ;MACvBc,iBAAiB,EAAEd,QAAQ,GAAG,CAAC,GAAGD,UAAU,GAAGC,QAAQ,GAAG;IAC5D,CAAC,CAAC;EACJ;;EAEA;EACA,MAAMe,KAAK,GAAG;IACZC,YAAY,EAAE7C,IAAI,CAAC8C,oBAAoB,IAAI,CAAC;IAC5CJ,aAAa,EAAE1C,IAAI,CAAC0C,aAAa,IAAI,CAAC;IACtCC,iBAAiB,EAAE3C,IAAI,CAAC2C,iBAAiB,IAAI,CAAC;IAC9CI,eAAe,EAAE5C,YAAY,CAACmB,MAAM,CAAC,CAACJ,GAAG,EAAEP,GAAG,KAAKA,GAAG,CAACJ,KAAK,GAAGW,GAAG,CAACX,KAAK,GAAGI,GAAG,GAAGO,GAAG,EAAE;MAAEX,KAAK,EAAE;IAAE,CAAC,CAAC;IACnGyC,eAAe,EAAE7C,YAAY,CAACmB,MAAM,CAAC,CAAC2B,GAAG,EAAEtC,GAAG,KAAKA,GAAG,CAACJ,KAAK,GAAG0C,GAAG,CAAC1C,KAAK,GAAGI,GAAG,GAAGsC,GAAG,EAAE;MAAE1C,KAAK,EAAE2C;IAAS,CAAC;EAC3G,CAAC;EAED,MAAMC,aAAa,GAAGA,CAAC;IAAEC,MAAM;IAAEC,OAAO;IAAEC;EAAM,CAAC,KAAK;IACpD,IAAIF,MAAM,IAAIC,OAAO,IAAIA,OAAO,CAAC7B,MAAM,EAAE;MACvC,oBACEjC,OAAA,CAACH,KAAK;QAACmE,EAAE,EAAE;UAAEC,CAAC,EAAE,CAAC;UAAEC,MAAM,EAAE;QAAiB,CAAE;QAAAC,QAAA,gBAC5CnE,OAAA,CAACL,UAAU;UAACyE,OAAO,EAAC,OAAO;UAAAD,QAAA,EAAE,SAASJ,KAAK;QAAE;UAAAM,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAa,CAAC,EAC1DV,OAAO,CAAChD,GAAG,CAAC,CAAC2D,KAAK,EAAEjD,KAAK;UAAA,IAAAkD,YAAA;UAAA,oBACxB1E,OAAA,CAACL,UAAU;YAAayE,OAAO,EAAC,OAAO;YAACO,KAAK,EAAE;cAAEC,KAAK,EAAEH,KAAK,CAACG;YAAM,CAAE;YAAAT,QAAA,EACnE,GAAGM,KAAK,CAACI,IAAI,MAAAH,YAAA,GAAKD,KAAK,CAACK,KAAK,cAAAJ,YAAA,uBAAXA,YAAA,CAAaK,OAAO,CAAC,CAAC,CAAC;UAAG,GAD9BvD,KAAK;YAAA6C,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAEV,CAAC;QAAA,CACd,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACG,CAAC;IAEZ;IACA,OAAO,IAAI;EACb,CAAC;EAED,oBACExE,OAAA,CAACN,GAAG;IAACsE,EAAE,EAAE;MAAEgB,EAAE,EAAE,CAAC;MAAEC,KAAK,EAAE;IAAO,CAAE;IAAAd,QAAA,gBAChCnE,OAAA,CAACL,UAAU;MAACyE,OAAO,EAAC,IAAI;MAACc,YAAY;MAAAf,QAAA,GAAC,0BACZ,EAAC1D,IAAI,CAAC0E,WAAW,EAAC,KAAG,EAAC1E,IAAI,CAAC2E,SAAS,EAAC,GAC/D;IAAA;MAAAf,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAY,CAAC,eAEbxE,OAAA,CAACJ,IAAI;MAACyF,SAAS;MAACC,OAAO,EAAE,CAAE;MAAAnB,QAAA,gBAEzBnE,OAAA,CAACJ,IAAI;QAAC2B,IAAI;QAACgE,EAAE,EAAE,EAAG;QAAApB,QAAA,eAChBnE,OAAA,CAACH,KAAK;UAACmE,EAAE,EAAE;YAAEC,CAAC,EAAE;UAAE,CAAE;UAAAE,QAAA,gBAClBnE,OAAA,CAACL,UAAU;YAACyE,OAAO,EAAC,WAAW;YAACc,YAAY;YAAAf,QAAA,EAAC;UAE7C;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAY,CAAC,eACbxE,OAAA,CAACJ,IAAI;YAACyF,SAAS;YAACC,OAAO,EAAE,CAAE;YAAAnB,QAAA,gBACzBnE,OAAA,CAACJ,IAAI;cAAC2B,IAAI;cAACgE,EAAE,EAAE,EAAG;cAACC,EAAE,EAAE,CAAE;cAACC,EAAE,EAAE,CAAE;cAAAtB,QAAA,eAC9BnE,OAAA,CAACN,GAAG;gBAACsE,EAAE,EAAE;kBAAE0B,SAAS,EAAE,QAAQ;kBAAEzB,CAAC,EAAE,CAAC;kBAAEC,MAAM,EAAE,mBAAmB;kBAAEyB,YAAY,EAAE;gBAAE,CAAE;gBAAAxB,QAAA,gBACnFnE,OAAA,CAACL,UAAU;kBAACyE,OAAO,EAAC,IAAI;kBAACQ,KAAK,EAAC,SAAS;kBAAAT,QAAA,GACrCd,KAAK,CAACC,YAAY,CAACyB,OAAO,CAAC,CAAC,CAAC,EAAC,GACjC;gBAAA;kBAAAV,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAY,CAAC,eACbxE,OAAA,CAACL,UAAU;kBAACyE,OAAO,EAAC,OAAO;kBAAAD,QAAA,EAAC;gBAAY;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAY,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAClD;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACF,CAAC,eACPxE,OAAA,CAACJ,IAAI;cAAC2B,IAAI;cAACgE,EAAE,EAAE,EAAG;cAACC,EAAE,EAAE,CAAE;cAACC,EAAE,EAAE,CAAE;cAAAtB,QAAA,eAC9BnE,OAAA,CAACN,GAAG;gBAACsE,EAAE,EAAE;kBAAE0B,SAAS,EAAE,QAAQ;kBAAEzB,CAAC,EAAE,CAAC;kBAAEC,MAAM,EAAE,mBAAmB;kBAAEyB,YAAY,EAAE;gBAAE,CAAE;gBAAAxB,QAAA,gBACnFnE,OAAA,CAACL,UAAU;kBAACyE,OAAO,EAAC,IAAI;kBAACQ,KAAK,EAAC,cAAc;kBAAAT,QAAA,EAC1Cd,KAAK,CAACF;gBAAa;kBAAAkB,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACV,CAAC,eACbxE,OAAA,CAACL,UAAU;kBAACyE,OAAO,EAAC,OAAO;kBAAAD,QAAA,EAAC;gBAAa;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAY,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACnD;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACF,CAAC,eACPxE,OAAA,CAACJ,IAAI;cAAC2B,IAAI;cAACgE,EAAE,EAAE,EAAG;cAACC,EAAE,EAAE,CAAE;cAACC,EAAE,EAAE,CAAE;cAAAtB,QAAA,eAC9BnE,OAAA,CAACN,GAAG;gBAACsE,EAAE,EAAE;kBAAE0B,SAAS,EAAE,QAAQ;kBAAEzB,CAAC,EAAE,CAAC;kBAAEC,MAAM,EAAE,mBAAmB;kBAAEyB,YAAY,EAAE;gBAAE,CAAE;gBAAAxB,QAAA,gBACnFnE,OAAA,CAACL,UAAU;kBAACyE,OAAO,EAAC,IAAI;kBAACQ,KAAK,EAAC,WAAW;kBAAAT,QAAA,GACvCd,KAAK,CAACD,iBAAiB,CAAC2B,OAAO,CAAC,CAAC,CAAC,EAAC,GACtC;gBAAA;kBAAAV,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAY,CAAC,eACbxE,OAAA,CAACL,UAAU;kBAACyE,OAAO,EAAC,OAAO;kBAAAD,QAAA,EAAC;gBAAiB;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAY,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACvD;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACF,CAAC,eACPxE,OAAA,CAACJ,IAAI;cAAC2B,IAAI;cAACgE,EAAE,EAAE,EAAG;cAACC,EAAE,EAAE,CAAE;cAACC,EAAE,EAAE,CAAE;cAAAtB,QAAA,eAC9BnE,OAAA,CAACN,GAAG;gBAACsE,EAAE,EAAE;kBAAE0B,SAAS,EAAE,QAAQ;kBAAEzB,CAAC,EAAE,CAAC;kBAAEC,MAAM,EAAE,mBAAmB;kBAAEyB,YAAY,EAAE;gBAAE,CAAE;gBAAAxB,QAAA,gBACnFnE,OAAA,CAACL,UAAU;kBAACyE,OAAO,EAAC,IAAI;kBAACQ,KAAK,EAAC,cAAc;kBAAAT,QAAA,IAAAxD,qBAAA,GAC1C0C,KAAK,CAACG,eAAe,CAACxC,KAAK,cAAAL,qBAAA,uBAA3BA,qBAAA,CAA6BoE,OAAO,CAAC,CAAC,CAAC,EAAC,GAC3C;gBAAA;kBAAAV,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAY,CAAC,eACbxE,OAAA,CAACL,UAAU;kBAACyE,OAAO,EAAC,OAAO;kBAAAD,QAAA,EAAC;gBAAe;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAY,CAAC,eACxDxE,OAAA,CAACL,UAAU;kBAACyE,OAAO,EAAC,SAAS;kBAAAD,QAAA,EAC1Bd,KAAK,CAACG,eAAe,CAACvC;gBAAc;kBAAAoD,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAC3B,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACV;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACF,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACF;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACJ,CAAC,eAGPxE,OAAA,CAACJ,IAAI;QAAC2B,IAAI;QAACgE,EAAE,EAAE,EAAG;QAAApB,QAAA,eAChBnE,OAAA,CAACH,KAAK;UAACmE,EAAE,EAAE;YAAEC,CAAC,EAAE,CAAC;YAAE2B,MAAM,EAAE,GAAG;YAAED,YAAY,EAAE;UAAE,CAAE;UAAAxB,QAAA,gBAChDnE,OAAA,CAACL,UAAU;YAACyE,OAAO,EAAC,WAAW;YAACc,YAAY;YAACW,KAAK,EAAC,QAAQ;YAAA1B,QAAA,EAAC;UAE5D;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAY,CAAC,eACbxE,OAAA,CAACZ,mBAAmB;YAAC6F,KAAK,EAAC,MAAM;YAACW,MAAM,EAAE,GAAI;YAAAzB,QAAA,eAC5CnE,OAAA,CAACT,aAAa;cAACkB,IAAI,EAAEa,yBAA0B;cAACwE,MAAM,EAAE;gBAAEC,GAAG,EAAE,EAAE;gBAAEC,KAAK,EAAE,EAAE;gBAAEC,IAAI,EAAE,EAAE;gBAAEC,MAAM,EAAE;cAAG,CAAE;cAAA/B,QAAA,gBACnGnE,OAAA,CAACf,aAAa;gBAACkH,eAAe,EAAC;cAAK;gBAAA9B,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,eACvCxE,OAAA,CAACjB,KAAK;gBACJqH,OAAO,EAAC,gBAAgB;gBACxBC,KAAK,EAAE,CAAC,EAAG;gBACXC,UAAU,EAAC,KAAK;gBAChBV,MAAM,EAAE;cAAG;gBAAAvB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACZ,CAAC,eACFxE,OAAA,CAAChB,KAAK;gBAAAqF,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,eACTxE,OAAA,CAACd,OAAO;gBAACqH,OAAO,eAAEvG,OAAA,CAAC4D,aAAa;kBAAAS,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE;cAAE;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,eACvCxE,OAAA,CAACb,MAAM;gBAAAkF,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,eACVxE,OAAA,CAACV,GAAG;gBAAC8G,OAAO,EAAC,OAAO;gBAACI,IAAI,EAAEvG,MAAM,CAACC,OAAQ;gBAAC2E,IAAI,EAAC,mBAAmB;gBAAC4B,OAAO,EAAE;cAAI;gBAAApC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,eACpFxE,OAAA,CAAClB,IAAI;gBACH4H,IAAI,EAAC,UAAU;gBACfN,OAAO,EAAC,cAAc;gBACtBO,MAAM,EAAE1G,MAAM,CAACM,KAAM;gBACrBqG,WAAW,EAAE,CAAE;gBACf/B,IAAI,EAAC,oBAAoB;gBACzBgC,GAAG,EAAE;cAAM;gBAAAxC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACZ,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACW;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACG,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACjB;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACJ,CAAC,eAGPxE,OAAA,CAACJ,IAAI;QAAC2B,IAAI;QAACgE,EAAE,EAAE,EAAG;QAACE,EAAE,EAAE,CAAE;QAAAtB,QAAA,eACvBnE,OAAA,CAACH,KAAK;UAACmE,EAAE,EAAE;YAAEC,CAAC,EAAE,CAAC;YAAE2B,MAAM,EAAE;UAAI,CAAE;UAAAzB,QAAA,gBAC/BnE,OAAA,CAACL,UAAU;YAACyE,OAAO,EAAC,WAAW;YAACc,YAAY;YAACW,KAAK,EAAC,QAAQ;YAAA1B,QAAA,EAAC;UAE5D;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAY,CAAC,eACbxE,OAAA,CAACZ,mBAAmB;YAAC6F,KAAK,EAAC,MAAM;YAACW,MAAM,EAAE,GAAI;YAAAzB,QAAA,eAC5CnE,OAAA,CAACP,SAAS;cAACgB,IAAI,EAAEG,YAAY,CAACE,GAAG,CAAC,CAACS,IAAI,EAAEC,KAAK,MAAM;gBAClD,GAAGD,IAAI;gBACPuF,UAAU,EAAElG,YAAY,CAACiB,KAAK,CAAC,CAAC,EAAEL,KAAK,GAAG,CAAC,CAAC,CAACO,MAAM,CAAC,CAACC,GAAG,EAAEZ,GAAG,KAAKY,GAAG,GAAGZ,GAAG,CAACJ,KAAK,EAAE,CAAC;cACtF,CAAC,CAAC,CAAE;cAAC8E,MAAM,EAAE;gBAAEC,GAAG,EAAE,EAAE;gBAAEC,KAAK,EAAE,EAAE;gBAAEC,IAAI,EAAE,EAAE;gBAAEC,MAAM,EAAE;cAAE,CAAE;cAAA/B,QAAA,gBACvDnE,OAAA,CAACf,aAAa;gBAACkH,eAAe,EAAC;cAAK;gBAAA9B,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,eACvCxE,OAAA,CAACjB,KAAK;gBAACqH,OAAO,EAAC,gBAAgB;gBAACC,KAAK,EAAE,CAAC,EAAG;gBAACC,UAAU,EAAC,KAAK;gBAACV,MAAM,EAAE;cAAG;gBAAAvB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,eAC3ExE,OAAA,CAAChB,KAAK;gBAAAqF,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,eACTxE,OAAA,CAACd,OAAO;gBAACqH,OAAO,eAAEvG,OAAA,CAAC4D,aAAa;kBAAAS,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE;cAAE;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,eACvCxE,OAAA,CAACR,IAAI;gBACHkH,IAAI,EAAC,UAAU;gBACfN,OAAO,EAAC,YAAY;gBACpBO,MAAM,EAAE1G,MAAM,CAACG,OAAQ;gBACvBoG,IAAI,EAAEvG,MAAM,CAACG,OAAQ;gBACrB2G,WAAW,EAAE,GAAI;gBACjBlC,IAAI,EAAC;cAAkB;gBAAAR,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACxB,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACO;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACO,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACjB;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACJ,CAAC,eAGPxE,OAAA,CAACJ,IAAI;QAAC2B,IAAI;QAACgE,EAAE,EAAE,EAAG;QAACE,EAAE,EAAE,CAAE;QAAAtB,QAAA,eACvBnE,OAAA,CAACH,KAAK;UAACmE,EAAE,EAAE;YAAEC,CAAC,EAAE,CAAC;YAAE2B,MAAM,EAAE;UAAI,CAAE;UAAAzB,QAAA,gBAC/BnE,OAAA,CAACL,UAAU;YAACyE,OAAO,EAAC,WAAW;YAACc,YAAY;YAACW,KAAK,EAAC,QAAQ;YAAA1B,QAAA,EAAC;UAE5D;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAY,CAAC,eACbxE,OAAA,CAACZ,mBAAmB;YAAC6F,KAAK,EAAC,MAAM;YAACW,MAAM,EAAE,GAAI;YAAAzB,QAAA,eAC5CnE,OAAA,CAACX,QAAQ;cAACoB,IAAI,EAAE0B,UAAW;cAAC2D,MAAM,EAAE;gBAAEC,GAAG,EAAE,EAAE;gBAAEC,KAAK,EAAE,EAAE;gBAAEC,IAAI,EAAE,EAAE;gBAAEC,MAAM,EAAE;cAAE,CAAE;cAAA/B,QAAA,gBAC9EnE,OAAA,CAACf,aAAa;gBAACkH,eAAe,EAAC;cAAK;gBAAA9B,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,eACvCxE,OAAA,CAACjB,KAAK;gBAACqH,OAAO,EAAC;cAAW;gBAAA/B,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,eAC7BxE,OAAA,CAAChB,KAAK;gBAAAqF,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,eACTxE,OAAA,CAACd,OAAO;gBAACqH,OAAO,eAAEvG,OAAA,CAAC4D,aAAa;kBAAAS,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE;cAAE;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,eACvCxE,OAAA,CAACb,MAAM;gBAAAkF,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,eACVxE,OAAA,CAACV,GAAG;gBAAC8G,OAAO,EAAC,cAAc;gBAACI,IAAI,EAAEvG,MAAM,CAACK,IAAK;gBAACuE,IAAI,EAAC;cAAmB;gBAAAR,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,eAC1ExE,OAAA,CAACV,GAAG;gBAAC8G,OAAO,EAAC,mBAAmB;gBAACI,IAAI,EAAEvG,MAAM,CAACI,OAAQ;gBAACwE,IAAI,EAAC;cAAmB;gBAAAR,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC1E;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACQ,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACjB;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACJ,CAAC,eAGPxE,OAAA,CAACJ,IAAI;QAAC2B,IAAI;QAACgE,EAAE,EAAE,EAAG;QAAApB,QAAA,eAChBnE,OAAA,CAACH,KAAK;UAACmE,EAAE,EAAE;YAAEC,CAAC,EAAE;UAAE,CAAE;UAAAE,QAAA,gBAClBnE,OAAA,CAACL,UAAU;YAACyE,OAAO,EAAC,WAAW;YAACc,YAAY;YAAAf,QAAA,EAAC;UAE7C;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAY,CAAC,eACbxE,OAAA,CAACJ,IAAI;YAACyF,SAAS;YAACC,OAAO,EAAE,CAAE;YAAAnB,QAAA,gBAEzBnE,OAAA,CAACJ,IAAI;cAAC2B,IAAI;cAACgE,EAAE,EAAE,EAAG;cAACE,EAAE,EAAE,CAAE;cAAAtB,QAAA,gBACvBnE,OAAA,CAACL,UAAU;gBAACyE,OAAO,EAAC,WAAW;gBAACc,YAAY;gBAAAf,QAAA,EAAC;cAE7C;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAY,CAAC,EACZ5D,YAAY,CACVoG,IAAI,CAAC,CAACC,CAAC,EAAEC,CAAC,KAAKA,CAAC,CAAClG,KAAK,GAAGiG,CAAC,CAACjG,KAAK,CAAC,CACjCa,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC,CACXf,GAAG,CAAC,CAACM,GAAG,EAAEI,KAAK,kBACdxB,OAAA,CAACN,GAAG;gBAAasE,EAAE,EAAE;kBACnBmD,OAAO,EAAE,MAAM;kBACfC,cAAc,EAAE,eAAe;kBAC/BC,UAAU,EAAE,QAAQ;kBACpBpD,CAAC,EAAE,CAAC;kBACJqD,EAAE,EAAE,CAAC;kBACLpD,MAAM,EAAE,mBAAmB;kBAC3ByB,YAAY,EAAE,CAAC;kBACf4B,UAAU,EAAE,aAAatH,MAAM,CAACG,OAAO;gBACzC,CAAE;gBAAA+D,QAAA,gBACAnE,OAAA,CAACL,UAAU;kBAACyE,OAAO,EAAC,OAAO;kBAAAD,QAAA,EAAE/C,GAAG,CAACX;gBAAI;kBAAA4D,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAa,CAAC,eACnDxE,OAAA,CAACF,IAAI;kBACHiE,KAAK,EAAE,GAAG3C,GAAG,CAACJ,KAAK,CAAC+D,OAAO,CAAC,CAAC,CAAC,GAAI;kBAClCH,KAAK,EAAC,SAAS;kBACf4C,IAAI,EAAC;gBAAO;kBAAAnD,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACb,CAAC;cAAA,GAfMhD,KAAK;gBAAA6C,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAgBV,CACN,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACA,CAAC,eAGPxE,OAAA,CAACJ,IAAI;cAAC2B,IAAI;cAACgE,EAAE,EAAE,EAAG;cAACE,EAAE,EAAE,CAAE;cAAAtB,QAAA,gBACvBnE,OAAA,CAACL,UAAU;gBAACyE,OAAO,EAAC,WAAW;gBAACc,YAAY;gBAAAf,QAAA,EAAC;cAE7C;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAY,CAAC,EACZrC,UAAU,CACR6E,IAAI,CAAC,CAACC,CAAC,EAAEC,CAAC,KAAKA,CAAC,CAAChE,YAAY,GAAG+D,CAAC,CAAC/D,YAAY,CAAC,CAC/CrB,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC,CACXf,GAAG,CAAC,CAAC2G,IAAI,EAAEjG,KAAK,kBACfxB,OAAA,CAACN,GAAG;gBAAasE,EAAE,EAAE;kBACnBmD,OAAO,EAAE,MAAM;kBACfC,cAAc,EAAE,eAAe;kBAC/BC,UAAU,EAAE,QAAQ;kBACpBpD,CAAC,EAAE,CAAC;kBACJqD,EAAE,EAAE,CAAC;kBACLpD,MAAM,EAAE,mBAAmB;kBAC3ByB,YAAY,EAAE,CAAC;kBACf4B,UAAU,EAAE,aAAatH,MAAM,CAACK,IAAI;gBACtC,CAAE;gBAAA6D,QAAA,gBACAnE,OAAA,CAACN,GAAG;kBAAAyE,QAAA,gBACFnE,OAAA,CAACL,UAAU;oBAACyE,OAAO,EAAC,OAAO;oBAAAD,QAAA,GAAC,YAAU,EAACsD,IAAI,CAACxE,SAAS;kBAAA;oBAAAoB,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAa,CAAC,eACnExE,OAAA,CAACL,UAAU;oBAACyE,OAAO,EAAC,SAAS;oBAAAD,QAAA,GAC1BsD,IAAI,CAACtE,aAAa,EAAC,gBACtB;kBAAA;oBAAAkB,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAY,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACV,CAAC,eACNxE,OAAA,CAACN,GAAG;kBAACsE,EAAE,EAAE;oBAAE0B,SAAS,EAAE;kBAAQ,CAAE;kBAAAvB,QAAA,gBAC9BnE,OAAA,CAACF,IAAI;oBACHiE,KAAK,EAAE,GAAG0D,IAAI,CAACvE,YAAY,CAAC6B,OAAO,CAAC,CAAC,CAAC,GAAI;oBAC1CH,KAAK,EAAC,MAAM;oBACZ4C,IAAI,EAAC;kBAAO;oBAAAnD,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACb,CAAC,eACFxE,OAAA,CAACL,UAAU;oBAACyE,OAAO,EAAC,SAAS;oBAAC+C,OAAO,EAAC,OAAO;oBAAAhD,QAAA,GAAC,SACrC,EAACsD,IAAI,CAACrE,iBAAiB,CAAC2B,OAAO,CAAC,CAAC,CAAC,EAAC,KAC5C;kBAAA;oBAAAV,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAY,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACV,CAAC;cAAA,GAzBEhD,KAAK;gBAAA6C,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OA0BV,CACN,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACA,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACF;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACJ,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACJ,CAAC;AAEV,CAAC;AAACkD,EAAA,GAhTIlH,aAAa;AAkTnB,eAAeA,aAAa;AAAC,IAAAkH,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}