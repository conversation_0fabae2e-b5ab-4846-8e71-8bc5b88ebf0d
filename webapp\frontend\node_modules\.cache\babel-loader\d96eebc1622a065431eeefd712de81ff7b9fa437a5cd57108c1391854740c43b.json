{"ast": null, "code": "import { toDate } from \"./toDate.mjs\";\n\n/**\n * @name isThursday\n * @category Weekday Helpers\n * @summary Is the given date Thursday?\n *\n * @description\n * Is the given date Thursday?\n *\n * @typeParam DateType - The `Date` type, the function operates on. Gets inferred from passed arguments. Allows to use extensions like [`UTCDate`](https://github.com/date-fns/utc).\n *\n * @param date - The date to check\n *\n * @returns The date is Thursday\n *\n * @example\n * // Is 25 September 2014 Thursday?\n * const result = isThursday(new Date(2014, 8, 25))\n * //=> true\n */\nexport function isThursday(date) {\n  return toDate(date).getDay() === 4;\n}\n\n// Fallback for modularized imports:\nexport default isThursday;", "map": {"version": 3, "names": ["toDate", "isThursday", "date", "getDay"], "sources": ["C:/CMS/webapp/frontend/node_modules/date-fns/isThursday.mjs"], "sourcesContent": ["import { toDate } from \"./toDate.mjs\";\n\n/**\n * @name isThursday\n * @category Weekday Helpers\n * @summary Is the given date Thursday?\n *\n * @description\n * Is the given date Thursday?\n *\n * @typeParam DateType - The `Date` type, the function operates on. Gets inferred from passed arguments. Allows to use extensions like [`UTCDate`](https://github.com/date-fns/utc).\n *\n * @param date - The date to check\n *\n * @returns The date is Thursday\n *\n * @example\n * // Is 25 September 2014 Thursday?\n * const result = isThursday(new Date(2014, 8, 25))\n * //=> true\n */\nexport function isThursday(date) {\n  return toDate(date).getDay() === 4;\n}\n\n// Fallback for modularized imports:\nexport default isThursday;\n"], "mappings": "AAAA,SAASA,MAAM,QAAQ,cAAc;;AAErC;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO,SAASC,UAAUA,CAACC,IAAI,EAAE;EAC/B,OAAOF,MAAM,CAACE,IAAI,CAAC,CAACC,MAAM,CAAC,CAAC,KAAK,CAAC;AACpC;;AAEA;AACA,eAAeF,UAAU", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}