{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\PycharmProjects\\\\CMS\\\\webapp\\\\frontend\\\\src\\\\components\\\\cavi\\\\MetriPosatiForm.js\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect } from 'react';\nimport { useParams, useNavigate } from 'react-router-dom';\nimport { Box, Button, Card, CardContent, CircularProgress, Dialog, DialogActions, DialogContent, DialogContentText, DialogTitle, FormControl, FormHelperText, Grid, InputLabel, MenuItem, Select, TextField, Typography, Paper, Table, TableBody, TableCell, TableContainer, TableHead, TableRow, Chip, Snackbar, Alert } from '@mui/material';\n// Rimosso import di notistack\n\n// Importa il servizio semplificato\nimport caviSimpleService from '../../services/caviSimpleService';\nimport caviService from '../../services/caviService'; // Per ottenere i cavi\n\n/**\n * Componente per l'inserimento dei metri posati di un cavo\n */\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst MetriPosatiForm = () => {\n  _s();\n  // Parametri dalla URL\n  const {\n    cantiereId\n  } = useParams();\n  const navigate = useNavigate();\n  const {\n    enqueueSnackbar\n  } = useSnackbar();\n\n  // Stati del componente\n  const [cavi, setCavi] = useState([]);\n  const [bobine, setBobine] = useState([]);\n  const [selectedCavo, setSelectedCavo] = useState(null);\n  const [formData, setFormData] = useState({\n    id_cavo: '',\n    metri_posati: '',\n    id_bobina: ''\n  });\n  const [errors, setErrors] = useState({});\n  const [loading, setLoading] = useState(false);\n  const [bobineLoading, setBobineLoading] = useState(false);\n  const [confirmDialogOpen, setConfirmDialogOpen] = useState(false);\n  const [confirmDialogMessage, setConfirmDialogMessage] = useState('');\n  const [confirmDialogAction, setConfirmDialogAction] = useState(null);\n\n  // Carica i cavi all'avvio\n  useEffect(() => {\n    loadCavi();\n  }, [cantiereId]);\n\n  // Carica le bobine quando viene selezionato un cavo\n  useEffect(() => {\n    if (selectedCavo) {\n      loadBobine();\n    }\n  }, [selectedCavo]);\n\n  /**\n   * Carica i cavi dal server\n   */\n  const loadCavi = async () => {\n    try {\n      setLoading(true);\n      const caviData = await caviService.getCavi(cantiereId);\n\n      // Filtra solo i cavi che possono essere modificati (Da installare o In corso)\n      const caviModificabili = caviData.filter(cavo => cavo.stato_installazione === 'Da installare' || cavo.stato_installazione === 'In corso');\n      setCavi(caviModificabili);\n    } catch (error) {\n      console.error('Errore nel caricamento dei cavi:', error);\n      enqueueSnackbar('Errore nel caricamento dei cavi: ' + (error.detail || 'Errore sconosciuto'), {\n        variant: 'error'\n      });\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  /**\n   * Carica le bobine compatibili con il cavo selezionato\n   */\n  const loadBobine = async () => {\n    if (!selectedCavo) return;\n    try {\n      setBobineLoading(true);\n\n      // Ottieni le bobine compatibili\n      const bobineCompatibili = await caviSimpleService.getBobineCompatibili(cantiereId, selectedCavo.tipologia, selectedCavo.sezione);\n\n      // Ordina le bobine per metri residui (decrescente)\n      bobineCompatibili.sort((a, b) => b.metri_residui - a.metri_residui);\n      setBobine(bobineCompatibili);\n      console.log(`Caricate ${bobineCompatibili.length} bobine compatibili`);\n    } catch (error) {\n      console.error('Errore nel caricamento delle bobine compatibili:', error);\n      enqueueSnackbar('Errore nel caricamento delle bobine: ' + (error.detail || 'Errore sconosciuto'), {\n        variant: 'error'\n      });\n    } finally {\n      setBobineLoading(false);\n    }\n  };\n\n  /**\n   * Gestisce la selezione di un cavo\n   */\n  const handleCavoSelect = event => {\n    const cavoId = event.target.value;\n    setFormData({\n      ...formData,\n      id_cavo: cavoId,\n      metri_posati: '',\n      id_bobina: ''\n    });\n\n    // Trova il cavo selezionato\n    const cavo = cavi.find(c => c.id_cavo === cavoId);\n    setSelectedCavo(cavo);\n\n    // Resetta gli errori\n    setErrors({});\n  };\n\n  /**\n   * Gestisce i cambiamenti nei campi del form\n   */\n  const handleFormChange = event => {\n    const {\n      name,\n      value\n    } = event.target;\n\n    // Validazione speciale per metri_posati\n    if (name === 'metri_posati') {\n      // Rimuovi caratteri non numerici eccetto il punto\n      const sanitizedValue = value.replace(/[^0-9.]/g, '');\n\n      // Assicurati che ci sia al massimo un punto decimale\n      const parts = sanitizedValue.split('.');\n      const finalValue = parts.length > 1 ? parts[0] + '.' + parts.slice(1).join('') : sanitizedValue;\n      setFormData({\n        ...formData,\n        [name]: finalValue\n      });\n    } else {\n      setFormData({\n        ...formData,\n        [name]: value\n      });\n    }\n\n    // Resetta gli errori per il campo\n    if (errors[name]) {\n      setErrors({\n        ...errors,\n        [name]: null\n      });\n    }\n  };\n\n  /**\n   * Valida il form prima dell'invio\n   */\n  const validateForm = () => {\n    const newErrors = {};\n\n    // Valida id_cavo\n    if (!formData.id_cavo) {\n      newErrors.id_cavo = 'Seleziona un cavo';\n    }\n\n    // Valida metri_posati\n    if (!formData.metri_posati) {\n      newErrors.metri_posati = 'Inserisci i metri posati';\n    } else {\n      const metriPosati = parseFloat(formData.metri_posati);\n      if (isNaN(metriPosati)) {\n        newErrors.metri_posati = 'I metri posati devono essere un numero';\n      } else if (metriPosati < 0) {\n        newErrors.metri_posati = 'I metri posati non possono essere negativi';\n      }\n\n      // Avvisi (non bloccanti)\n      if (selectedCavo && metriPosati > parseFloat(selectedCavo.metri_teorici || 0)) {\n        // Mostra un avviso ma non bloccare\n        console.warn(`I metri posati (${metriPosati}) superano i metri teorici (${selectedCavo.metri_teorici})`);\n      }\n\n      // Verifica se la bobina selezionata ha abbastanza metri\n      if (formData.id_bobina && formData.id_bobina !== 'BOBINA_VUOTA') {\n        const bobina = bobine.find(b => b.id_bobina === formData.id_bobina);\n        if (bobina && metriPosati > parseFloat(bobina.metri_residui || 0)) {\n          // Mostra un avviso ma non bloccare\n          console.warn(`I metri posati (${metriPosati}) superano i metri residui della bobina (${bobina.metri_residui})`);\n        }\n      }\n    }\n\n    // Valida id_bobina solo se ci sono metri posati\n    if (formData.metri_posati && parseFloat(formData.metri_posati) > 0 && !formData.id_bobina) {\n      newErrors.id_bobina = 'Seleziona una bobina o \"BOBINA VUOTA\"';\n    }\n    setErrors(newErrors);\n    return Object.keys(newErrors).length === 0;\n  };\n\n  /**\n   * Gestisce l'invio del form\n   */\n  const handleSubmit = async event => {\n    event.preventDefault();\n\n    // Valida il form\n    if (!validateForm()) {\n      return;\n    }\n\n    // Estrai i dati dal form\n    const {\n      id_cavo,\n      metri_posati,\n      id_bobina\n    } = formData;\n    const metriPosati = parseFloat(metri_posati);\n\n    // Verifica se ci sono avvisi da mostrare\n    let warningMessage = '';\n\n    // Avviso per metri teorici\n    if (selectedCavo && metriPosati > parseFloat(selectedCavo.metri_teorici || 0)) {\n      warningMessage += `I metri posati (${metriPosati}) superano i metri teorici (${selectedCavo.metri_teorici}).\\n`;\n    }\n\n    // Avviso per metri residui della bobina\n    if (id_bobina && id_bobina !== 'BOBINA_VUOTA') {\n      const bobina = bobine.find(b => b.id_bobina === id_bobina);\n      if (bobina && metriPosati > parseFloat(bobina.metri_residui || 0)) {\n        warningMessage += `I metri posati (${metriPosati}) superano i metri residui della bobina (${bobina.metri_residui}).\\n`;\n      }\n    }\n\n    // Se ci sono avvisi, mostra il dialogo di conferma\n    if (warningMessage) {\n      setConfirmDialogMessage(warningMessage + '\\nVuoi continuare comunque?');\n      setConfirmDialogAction(() => () => submitMetriPosati(id_cavo, metriPosati, id_bobina));\n      setConfirmDialogOpen(true);\n      return;\n    }\n\n    // Altrimenti, procedi direttamente\n    await submitMetriPosati(id_cavo, metriPosati, id_bobina);\n  };\n\n  /**\n   * Invia i metri posati al server\n   */\n  const submitMetriPosati = async (id_cavo, metriPosati, id_bobina) => {\n    try {\n      setLoading(true);\n\n      // Chiudi il dialogo di conferma se aperto\n      setConfirmDialogOpen(false);\n\n      // Invia la richiesta al server\n      const response = await caviSimpleService.updateMetriPosati(cantiereId, id_cavo, metriPosati, id_bobina);\n\n      // Mostra un messaggio di successo\n      enqueueSnackbar('Metri posati aggiornati con successo', {\n        variant: 'success'\n      });\n\n      // Resetta il form\n      setFormData({\n        id_cavo: '',\n        metri_posati: '',\n        id_bobina: ''\n      });\n      setSelectedCavo(null);\n\n      // Ricarica i cavi\n      await loadCavi();\n    } catch (error) {\n      console.error('Errore nell\\'aggiornamento dei metri posati:', error);\n      enqueueSnackbar('Errore: ' + (error.detail || 'Errore sconosciuto'), {\n        variant: 'error'\n      });\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  /**\n   * Gestisce la chiusura del dialogo di conferma\n   */\n  const handleConfirmDialogClose = () => {\n    setConfirmDialogOpen(false);\n    setConfirmDialogAction(null);\n  };\n\n  /**\n   * Gestisce la conferma nel dialogo\n   */\n  const handleConfirmDialogConfirm = () => {\n    if (confirmDialogAction) {\n      confirmDialogAction();\n    }\n  };\n  return /*#__PURE__*/_jsxDEV(Box, {\n    sx: {\n      p: 3\n    },\n    children: [/*#__PURE__*/_jsxDEV(Typography, {\n      variant: \"h5\",\n      gutterBottom: true,\n      children: \"Inserimento Metri Posati\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 338,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Card, {\n      sx: {\n        mb: 3\n      },\n      children: /*#__PURE__*/_jsxDEV(CardContent, {\n        children: /*#__PURE__*/_jsxDEV(\"form\", {\n          onSubmit: handleSubmit,\n          children: /*#__PURE__*/_jsxDEV(Grid, {\n            container: true,\n            spacing: 3,\n            children: [/*#__PURE__*/_jsxDEV(Grid, {\n              item: true,\n              xs: 12,\n              md: 6,\n              children: /*#__PURE__*/_jsxDEV(FormControl, {\n                fullWidth: true,\n                error: !!errors.id_cavo,\n                children: [/*#__PURE__*/_jsxDEV(InputLabel, {\n                  id: \"cavo-select-label\",\n                  children: \"Cavo\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 349,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(Select, {\n                  labelId: \"cavo-select-label\",\n                  id: \"cavo-select\",\n                  name: \"id_cavo\",\n                  value: formData.id_cavo,\n                  onChange: handleCavoSelect,\n                  disabled: loading,\n                  children: [/*#__PURE__*/_jsxDEV(MenuItem, {\n                    value: \"\",\n                    children: /*#__PURE__*/_jsxDEV(\"em\", {\n                      children: \"Seleziona un cavo\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 359,\n                      columnNumber: 23\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 358,\n                    columnNumber: 21\n                  }, this), cavi.map(cavo => /*#__PURE__*/_jsxDEV(MenuItem, {\n                    value: cavo.id_cavo,\n                    children: [cavo.id_cavo, \" - \", cavo.tipologia, \" - \", cavo.sezione, \" - \", cavo.utility]\n                  }, cavo.id_cavo, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 362,\n                    columnNumber: 23\n                  }, this))]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 350,\n                  columnNumber: 19\n                }, this), errors.id_cavo && /*#__PURE__*/_jsxDEV(FormHelperText, {\n                  children: errors.id_cavo\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 367,\n                  columnNumber: 38\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 348,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 347,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Grid, {\n              item: true,\n              xs: 12,\n              md: 6,\n              children: /*#__PURE__*/_jsxDEV(TextField, {\n                fullWidth: true,\n                label: \"Metri posati\",\n                name: \"metri_posati\",\n                value: formData.metri_posati,\n                onChange: handleFormChange,\n                error: !!errors.metri_posati,\n                helperText: errors.metri_posati,\n                disabled: !selectedCavo || loading,\n                InputProps: {\n                  endAdornment: /*#__PURE__*/_jsxDEV(Typography, {\n                    variant: \"body2\",\n                    children: \"m\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 383,\n                    columnNumber: 35\n                  }, this)\n                }\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 373,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 372,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Grid, {\n              item: true,\n              xs: 12,\n              children: /*#__PURE__*/_jsxDEV(FormControl, {\n                fullWidth: true,\n                error: !!errors.id_bobina,\n                children: [/*#__PURE__*/_jsxDEV(InputLabel, {\n                  id: \"bobina-select-label\",\n                  children: \"Bobina\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 391,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(Select, {\n                  labelId: \"bobina-select-label\",\n                  id: \"bobina-select\",\n                  name: \"id_bobina\",\n                  value: formData.id_bobina,\n                  onChange: handleFormChange,\n                  disabled: !selectedCavo || bobineLoading || loading,\n                  children: [/*#__PURE__*/_jsxDEV(MenuItem, {\n                    value: \"\",\n                    children: /*#__PURE__*/_jsxDEV(\"em\", {\n                      children: \"Seleziona una bobina\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 401,\n                      columnNumber: 23\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 400,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(MenuItem, {\n                    value: \"BOBINA_VUOTA\",\n                    children: \"BOBINA VUOTA (Cavo gi\\xE0 posato senza riferimento a bobina)\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 403,\n                    columnNumber: 21\n                  }, this), bobine.map(bobina => /*#__PURE__*/_jsxDEV(MenuItem, {\n                    value: bobina.id_bobina,\n                    sx: {\n                      bgcolor: selectedCavo && String(bobina.tipologia || '').trim() === String(selectedCavo.tipologia || '').trim() && String(bobina.sezione || '0').trim() === String(selectedCavo.sezione || '0').trim() ? 'rgba(76, 175, 80, 0.08)' : 'inherit'\n                    },\n                    children: [bobina.id_bobina, \" - \", bobina.tipologia, \" - \", bobina.sezione, \" - \", bobina.metri_residui, \"m residui\", selectedCavo && String(bobina.tipologia || '').trim() === String(selectedCavo.tipologia || '').trim() && String(bobina.sezione || '0').trim() === String(selectedCavo.sezione || '0').trim() && /*#__PURE__*/_jsxDEV(Chip, {\n                      label: \"Compatibile\",\n                      color: \"success\",\n                      size: \"small\",\n                      sx: {\n                        ml: 1\n                      }\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 421,\n                      columnNumber: 29\n                    }, this)]\n                  }, bobina.id_bobina, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 407,\n                    columnNumber: 23\n                  }, this))]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 392,\n                  columnNumber: 19\n                }, this), errors.id_bobina && /*#__PURE__*/_jsxDEV(FormHelperText, {\n                  children: errors.id_bobina\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 432,\n                  columnNumber: 40\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 390,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 389,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Grid, {\n              item: true,\n              xs: 12,\n              children: /*#__PURE__*/_jsxDEV(Box, {\n                sx: {\n                  display: 'flex',\n                  justifyContent: 'flex-end',\n                  gap: 2\n                },\n                children: [/*#__PURE__*/_jsxDEV(Button, {\n                  variant: \"outlined\",\n                  onClick: () => navigate(`/cantieri/${cantiereId}/cavi`),\n                  disabled: loading,\n                  children: \"Annulla\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 439,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(Button, {\n                  type: \"submit\",\n                  variant: \"contained\",\n                  color: \"primary\",\n                  disabled: loading,\n                  startIcon: loading && /*#__PURE__*/_jsxDEV(CircularProgress, {\n                    size: 20,\n                    color: \"inherit\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 451,\n                    columnNumber: 43\n                  }, this),\n                  children: \"Salva\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 446,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 438,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 437,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 345,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 344,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 343,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 342,\n      columnNumber: 7\n    }, this), selectedCavo && /*#__PURE__*/_jsxDEV(Card, {\n      children: /*#__PURE__*/_jsxDEV(CardContent, {\n        children: [/*#__PURE__*/_jsxDEV(Typography, {\n          variant: \"h6\",\n          gutterBottom: true,\n          children: \"Dettagli del cavo selezionato\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 466,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(TableContainer, {\n          component: Paper,\n          children: /*#__PURE__*/_jsxDEV(Table, {\n            size: \"small\",\n            children: [/*#__PURE__*/_jsxDEV(TableHead, {\n              children: /*#__PURE__*/_jsxDEV(TableRow, {\n                children: [/*#__PURE__*/_jsxDEV(TableCell, {\n                  children: \"ID Cavo\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 473,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n                  children: \"Tipologia\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 474,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n                  children: \"Formazione\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 475,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n                  children: \"Utility\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 476,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n                  children: \"Metri teorici\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 477,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n                  children: \"Stato\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 478,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 472,\n                columnNumber: 19\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 471,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(TableBody, {\n              children: /*#__PURE__*/_jsxDEV(TableRow, {\n                children: [/*#__PURE__*/_jsxDEV(TableCell, {\n                  children: selectedCavo.id_cavo\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 483,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n                  children: selectedCavo.tipologia\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 484,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n                  children: selectedCavo.sezione\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 485,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n                  children: selectedCavo.utility\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 486,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n                  children: [selectedCavo.metri_teorici, \"m\"]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 487,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n                  children: selectedCavo.stato_installazione\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 488,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 482,\n                columnNumber: 19\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 481,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 470,\n            columnNumber: 15\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 469,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 465,\n        columnNumber: 11\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 464,\n      columnNumber: 9\n    }, this), /*#__PURE__*/_jsxDEV(Dialog, {\n      open: confirmDialogOpen,\n      onClose: handleConfirmDialogClose,\n      children: [/*#__PURE__*/_jsxDEV(DialogTitle, {\n        children: \"Conferma operazione\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 502,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(DialogContent, {\n        children: /*#__PURE__*/_jsxDEV(DialogContentText, {\n          children: confirmDialogMessage\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 504,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 503,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(DialogActions, {\n        children: [/*#__PURE__*/_jsxDEV(Button, {\n          onClick: handleConfirmDialogClose,\n          color: \"primary\",\n          children: \"Annulla\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 509,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Button, {\n          onClick: handleConfirmDialogConfirm,\n          color: \"primary\",\n          autoFocus: true,\n          children: \"Conferma\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 512,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 508,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 498,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 337,\n    columnNumber: 5\n  }, this);\n};\n_s(MetriPosatiForm, \"L0jyPfZFjl+HnhvY8Wo9LKsAQOU=\", true, function () {\n  return [useParams, useNavigate];\n});\n_c = MetriPosatiForm;\nexport default MetriPosatiForm;\nvar _c;\n$RefreshReg$(_c, \"MetriPosatiForm\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "useParams", "useNavigate", "Box", "<PERSON><PERSON>", "Card", "<PERSON><PERSON><PERSON><PERSON>", "CircularProgress", "Dialog", "DialogActions", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "DialogContentText", "DialogTitle", "FormControl", "FormHelperText", "Grid", "InputLabel", "MenuItem", "Select", "TextField", "Typography", "Paper", "Table", "TableBody", "TableCell", "TableContainer", "TableHead", "TableRow", "Chip", "Snackbar", "<PERSON><PERSON>", "caviSimpleService", "caviService", "jsxDEV", "_jsxDEV", "MetriPosatiForm", "_s", "cantiereId", "navigate", "enqueueSnackbar", "useSnackbar", "cavi", "<PERSON><PERSON><PERSON>", "bobine", "set<PERSON>ob<PERSON>", "selected<PERSON><PERSON><PERSON>", "setSelectedCavo", "formData", "setFormData", "id_cavo", "metri_posati", "id_bobina", "errors", "setErrors", "loading", "setLoading", "bob<PERSON><PERSON><PERSON><PERSON>", "setBobineLoading", "confirmDialogOpen", "setConfirmDialogOpen", "confirmDialogMessage", "setConfirmDialogMessage", "confirmDialogAction", "setConfirmDialogAction", "loadCavi", "loadBobine", "caviData", "get<PERSON><PERSON>", "caviModificabili", "filter", "cavo", "stato_installazione", "error", "console", "detail", "variant", "bobineCom<PERSON><PERSON><PERSON><PERSON>", "getBobineCompatibili", "tipologia", "sezione", "sort", "a", "b", "metri_residui", "log", "length", "handleCavoSelect", "event", "cavoId", "target", "value", "find", "c", "handleFormChange", "name", "sanitizedValue", "replace", "parts", "split", "finalValue", "slice", "join", "validateForm", "newErrors", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "parseFloat", "isNaN", "metri_te<PERSON>ci", "warn", "bobina", "Object", "keys", "handleSubmit", "preventDefault", "warningMessage", "submit<PERSON><PERSON>ri<PERSON><PERSON><PERSON>", "response", "updateMetri<PERSON><PERSON><PERSON>", "handleConfirmDialogClose", "handleConfirmDialogConfirm", "sx", "p", "children", "gutterBottom", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "mb", "onSubmit", "container", "spacing", "item", "xs", "md", "fullWidth", "id", "labelId", "onChange", "disabled", "map", "utility", "label", "helperText", "InputProps", "endAdornment", "bgcolor", "String", "trim", "color", "size", "ml", "display", "justifyContent", "gap", "onClick", "type", "startIcon", "component", "open", "onClose", "autoFocus", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/PycharmProjects/CMS/webapp/frontend/src/components/cavi/MetriPosatiForm.js"], "sourcesContent": ["import React, { useState, useEffect } from 'react';\nimport { useParams, useNavigate } from 'react-router-dom';\nimport {\n  Box,\n  Button,\n  Card,\n  CardContent,\n  CircularProgress,\n  Dialog,\n  DialogActions,\n  DialogContent,\n  DialogContentText,\n  DialogTitle,\n  FormControl,\n  FormHelperText,\n  Grid,\n  InputLabel,\n  MenuItem,\n  Select,\n  TextField,\n  Typography,\n  Paper,\n  Table,\n  TableBody,\n  TableCell,\n  TableContainer,\n  TableHead,\n  TableRow,\n  Chip,\n  Snackbar,\n  Alert\n} from '@mui/material';\n// Rimosso import di notistack\n\n// Importa il servizio semplificato\nimport caviSimpleService from '../../services/caviSimpleService';\nimport caviService from '../../services/caviService'; // Per ottenere i cavi\n\n/**\n * Componente per l'inserimento dei metri posati di un cavo\n */\nconst MetriPosatiForm = () => {\n  // Parametri dalla URL\n  const { cantiereId } = useParams();\n  const navigate = useNavigate();\n  const { enqueueSnackbar } = useSnackbar();\n\n  // Stati del componente\n  const [cavi, setCavi] = useState([]);\n  const [bobine, setBobine] = useState([]);\n  const [selectedCavo, setSelectedCavo] = useState(null);\n  const [formData, setFormData] = useState({\n    id_cavo: '',\n    metri_posati: '',\n    id_bobina: ''\n  });\n  const [errors, setErrors] = useState({});\n  const [loading, setLoading] = useState(false);\n  const [bobineLoading, setBobineLoading] = useState(false);\n  const [confirmDialogOpen, setConfirmDialogOpen] = useState(false);\n  const [confirmDialogMessage, setConfirmDialogMessage] = useState('');\n  const [confirmDialogAction, setConfirmDialogAction] = useState(null);\n\n  // Carica i cavi all'avvio\n  useEffect(() => {\n    loadCavi();\n  }, [cantiereId]);\n\n  // Carica le bobine quando viene selezionato un cavo\n  useEffect(() => {\n    if (selectedCavo) {\n      loadBobine();\n    }\n  }, [selectedCavo]);\n\n  /**\n   * Carica i cavi dal server\n   */\n  const loadCavi = async () => {\n    try {\n      setLoading(true);\n      const caviData = await caviService.getCavi(cantiereId);\n\n      // Filtra solo i cavi che possono essere modificati (Da installare o In corso)\n      const caviModificabili = caviData.filter(cavo =>\n        cavo.stato_installazione === 'Da installare' ||\n        cavo.stato_installazione === 'In corso'\n      );\n\n      setCavi(caviModificabili);\n    } catch (error) {\n      console.error('Errore nel caricamento dei cavi:', error);\n      enqueueSnackbar('Errore nel caricamento dei cavi: ' + (error.detail || 'Errore sconosciuto'), {\n        variant: 'error'\n      });\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  /**\n   * Carica le bobine compatibili con il cavo selezionato\n   */\n  const loadBobine = async () => {\n    if (!selectedCavo) return;\n\n    try {\n      setBobineLoading(true);\n\n      // Ottieni le bobine compatibili\n      const bobineCompatibili = await caviSimpleService.getBobineCompatibili(\n        cantiereId,\n        selectedCavo.tipologia,\n        selectedCavo.sezione\n      );\n\n      // Ordina le bobine per metri residui (decrescente)\n      bobineCompatibili.sort((a, b) => b.metri_residui - a.metri_residui);\n\n      setBobine(bobineCompatibili);\n      console.log(`Caricate ${bobineCompatibili.length} bobine compatibili`);\n    } catch (error) {\n      console.error('Errore nel caricamento delle bobine compatibili:', error);\n      enqueueSnackbar('Errore nel caricamento delle bobine: ' + (error.detail || 'Errore sconosciuto'), {\n        variant: 'error'\n      });\n    } finally {\n      setBobineLoading(false);\n    }\n  };\n\n  /**\n   * Gestisce la selezione di un cavo\n   */\n  const handleCavoSelect = (event) => {\n    const cavoId = event.target.value;\n    setFormData({\n      ...formData,\n      id_cavo: cavoId,\n      metri_posati: '',\n      id_bobina: ''\n    });\n\n    // Trova il cavo selezionato\n    const cavo = cavi.find(c => c.id_cavo === cavoId);\n    setSelectedCavo(cavo);\n\n    // Resetta gli errori\n    setErrors({});\n  };\n\n  /**\n   * Gestisce i cambiamenti nei campi del form\n   */\n  const handleFormChange = (event) => {\n    const { name, value } = event.target;\n\n    // Validazione speciale per metri_posati\n    if (name === 'metri_posati') {\n      // Rimuovi caratteri non numerici eccetto il punto\n      const sanitizedValue = value.replace(/[^0-9.]/g, '');\n\n      // Assicurati che ci sia al massimo un punto decimale\n      const parts = sanitizedValue.split('.');\n      const finalValue = parts.length > 1\n        ? parts[0] + '.' + parts.slice(1).join('')\n        : sanitizedValue;\n\n      setFormData({\n        ...formData,\n        [name]: finalValue\n      });\n    } else {\n      setFormData({\n        ...formData,\n        [name]: value\n      });\n    }\n\n    // Resetta gli errori per il campo\n    if (errors[name]) {\n      setErrors({\n        ...errors,\n        [name]: null\n      });\n    }\n  };\n\n  /**\n   * Valida il form prima dell'invio\n   */\n  const validateForm = () => {\n    const newErrors = {};\n\n    // Valida id_cavo\n    if (!formData.id_cavo) {\n      newErrors.id_cavo = 'Seleziona un cavo';\n    }\n\n    // Valida metri_posati\n    if (!formData.metri_posati) {\n      newErrors.metri_posati = 'Inserisci i metri posati';\n    } else {\n      const metriPosati = parseFloat(formData.metri_posati);\n\n      if (isNaN(metriPosati)) {\n        newErrors.metri_posati = 'I metri posati devono essere un numero';\n      } else if (metriPosati < 0) {\n        newErrors.metri_posati = 'I metri posati non possono essere negativi';\n      }\n\n      // Avvisi (non bloccanti)\n      if (selectedCavo && metriPosati > parseFloat(selectedCavo.metri_teorici || 0)) {\n        // Mostra un avviso ma non bloccare\n        console.warn(`I metri posati (${metriPosati}) superano i metri teorici (${selectedCavo.metri_teorici})`);\n      }\n\n      // Verifica se la bobina selezionata ha abbastanza metri\n      if (formData.id_bobina && formData.id_bobina !== 'BOBINA_VUOTA') {\n        const bobina = bobine.find(b => b.id_bobina === formData.id_bobina);\n        if (bobina && metriPosati > parseFloat(bobina.metri_residui || 0)) {\n          // Mostra un avviso ma non bloccare\n          console.warn(`I metri posati (${metriPosati}) superano i metri residui della bobina (${bobina.metri_residui})`);\n        }\n      }\n    }\n\n    // Valida id_bobina solo se ci sono metri posati\n    if (formData.metri_posati && parseFloat(formData.metri_posati) > 0 && !formData.id_bobina) {\n      newErrors.id_bobina = 'Seleziona una bobina o \"BOBINA VUOTA\"';\n    }\n\n    setErrors(newErrors);\n    return Object.keys(newErrors).length === 0;\n  };\n\n  /**\n   * Gestisce l'invio del form\n   */\n  const handleSubmit = async (event) => {\n    event.preventDefault();\n\n    // Valida il form\n    if (!validateForm()) {\n      return;\n    }\n\n    // Estrai i dati dal form\n    const { id_cavo, metri_posati, id_bobina } = formData;\n    const metriPosati = parseFloat(metri_posati);\n\n    // Verifica se ci sono avvisi da mostrare\n    let warningMessage = '';\n\n    // Avviso per metri teorici\n    if (selectedCavo && metriPosati > parseFloat(selectedCavo.metri_teorici || 0)) {\n      warningMessage += `I metri posati (${metriPosati}) superano i metri teorici (${selectedCavo.metri_teorici}).\\n`;\n    }\n\n    // Avviso per metri residui della bobina\n    if (id_bobina && id_bobina !== 'BOBINA_VUOTA') {\n      const bobina = bobine.find(b => b.id_bobina === id_bobina);\n      if (bobina && metriPosati > parseFloat(bobina.metri_residui || 0)) {\n        warningMessage += `I metri posati (${metriPosati}) superano i metri residui della bobina (${bobina.metri_residui}).\\n`;\n      }\n    }\n\n    // Se ci sono avvisi, mostra il dialogo di conferma\n    if (warningMessage) {\n      setConfirmDialogMessage(warningMessage + '\\nVuoi continuare comunque?');\n      setConfirmDialogAction(() => () => submitMetriPosati(id_cavo, metriPosati, id_bobina));\n      setConfirmDialogOpen(true);\n      return;\n    }\n\n    // Altrimenti, procedi direttamente\n    await submitMetriPosati(id_cavo, metriPosati, id_bobina);\n  };\n\n  /**\n   * Invia i metri posati al server\n   */\n  const submitMetriPosati = async (id_cavo, metriPosati, id_bobina) => {\n    try {\n      setLoading(true);\n\n      // Chiudi il dialogo di conferma se aperto\n      setConfirmDialogOpen(false);\n\n      // Invia la richiesta al server\n      const response = await caviSimpleService.updateMetriPosati(\n        cantiereId,\n        id_cavo,\n        metriPosati,\n        id_bobina\n      );\n\n      // Mostra un messaggio di successo\n      enqueueSnackbar('Metri posati aggiornati con successo', { variant: 'success' });\n\n      // Resetta il form\n      setFormData({\n        id_cavo: '',\n        metri_posati: '',\n        id_bobina: ''\n      });\n      setSelectedCavo(null);\n\n      // Ricarica i cavi\n      await loadCavi();\n    } catch (error) {\n      console.error('Errore nell\\'aggiornamento dei metri posati:', error);\n      enqueueSnackbar('Errore: ' + (error.detail || 'Errore sconosciuto'), { variant: 'error' });\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  /**\n   * Gestisce la chiusura del dialogo di conferma\n   */\n  const handleConfirmDialogClose = () => {\n    setConfirmDialogOpen(false);\n    setConfirmDialogAction(null);\n  };\n\n  /**\n   * Gestisce la conferma nel dialogo\n   */\n  const handleConfirmDialogConfirm = () => {\n    if (confirmDialogAction) {\n      confirmDialogAction();\n    }\n  };\n\n  return (\n    <Box sx={{ p: 3 }}>\n      <Typography variant=\"h5\" gutterBottom>\n        Inserimento Metri Posati\n      </Typography>\n\n      <Card sx={{ mb: 3 }}>\n        <CardContent>\n          <form onSubmit={handleSubmit}>\n            <Grid container spacing={3}>\n              {/* Selezione del cavo */}\n              <Grid item xs={12} md={6}>\n                <FormControl fullWidth error={!!errors.id_cavo}>\n                  <InputLabel id=\"cavo-select-label\">Cavo</InputLabel>\n                  <Select\n                    labelId=\"cavo-select-label\"\n                    id=\"cavo-select\"\n                    name=\"id_cavo\"\n                    value={formData.id_cavo}\n                    onChange={handleCavoSelect}\n                    disabled={loading}\n                  >\n                    <MenuItem value=\"\">\n                      <em>Seleziona un cavo</em>\n                    </MenuItem>\n                    {cavi.map((cavo) => (\n                      <MenuItem key={cavo.id_cavo} value={cavo.id_cavo}>\n                        {cavo.id_cavo} - {cavo.tipologia} - {cavo.sezione} - {cavo.utility}\n                      </MenuItem>\n                    ))}\n                  </Select>\n                  {errors.id_cavo && <FormHelperText>{errors.id_cavo}</FormHelperText>}\n                </FormControl>\n              </Grid>\n\n              {/* Metri posati */}\n              <Grid item xs={12} md={6}>\n                <TextField\n                  fullWidth\n                  label=\"Metri posati\"\n                  name=\"metri_posati\"\n                  value={formData.metri_posati}\n                  onChange={handleFormChange}\n                  error={!!errors.metri_posati}\n                  helperText={errors.metri_posati}\n                  disabled={!selectedCavo || loading}\n                  InputProps={{\n                    endAdornment: <Typography variant=\"body2\">m</Typography>\n                  }}\n                />\n              </Grid>\n\n              {/* Selezione della bobina */}\n              <Grid item xs={12}>\n                <FormControl fullWidth error={!!errors.id_bobina}>\n                  <InputLabel id=\"bobina-select-label\">Bobina</InputLabel>\n                  <Select\n                    labelId=\"bobina-select-label\"\n                    id=\"bobina-select\"\n                    name=\"id_bobina\"\n                    value={formData.id_bobina}\n                    onChange={handleFormChange}\n                    disabled={!selectedCavo || bobineLoading || loading}\n                  >\n                    <MenuItem value=\"\">\n                      <em>Seleziona una bobina</em>\n                    </MenuItem>\n                    <MenuItem value=\"BOBINA_VUOTA\">\n                      BOBINA VUOTA (Cavo già posato senza riferimento a bobina)\n                    </MenuItem>\n                    {bobine.map((bobina) => (\n                      <MenuItem\n                        key={bobina.id_bobina}\n                        value={bobina.id_bobina}\n                        sx={{\n                          bgcolor: selectedCavo &&\n                            String(bobina.tipologia || '').trim() === String(selectedCavo.tipologia || '').trim() &&\n                            String(bobina.sezione || '0').trim() === String(selectedCavo.sezione || '0').trim() ?\n                            'rgba(76, 175, 80, 0.08)' : 'inherit'\n                        }}\n                      >\n                        {bobina.id_bobina} - {bobina.tipologia} - {bobina.sezione} - {bobina.metri_residui}m residui\n                        {selectedCavo &&\n                          String(bobina.tipologia || '').trim() === String(selectedCavo.tipologia || '').trim() &&\n                          String(bobina.sezione || '0').trim() === String(selectedCavo.sezione || '0').trim() && (\n                            <Chip\n                              label=\"Compatibile\"\n                              color=\"success\"\n                              size=\"small\"\n                              sx={{ ml: 1 }}\n                            />\n                          )\n                        }\n                      </MenuItem>\n                    ))}\n                  </Select>\n                  {errors.id_bobina && <FormHelperText>{errors.id_bobina}</FormHelperText>}\n                </FormControl>\n              </Grid>\n\n              {/* Pulsanti */}\n              <Grid item xs={12}>\n                <Box sx={{ display: 'flex', justifyContent: 'flex-end', gap: 2 }}>\n                  <Button\n                    variant=\"outlined\"\n                    onClick={() => navigate(`/cantieri/${cantiereId}/cavi`)}\n                    disabled={loading}\n                  >\n                    Annulla\n                  </Button>\n                  <Button\n                    type=\"submit\"\n                    variant=\"contained\"\n                    color=\"primary\"\n                    disabled={loading}\n                    startIcon={loading && <CircularProgress size={20} color=\"inherit\" />}\n                  >\n                    Salva\n                  </Button>\n                </Box>\n              </Grid>\n            </Grid>\n          </form>\n        </CardContent>\n      </Card>\n\n      {/* Dettagli del cavo selezionato */}\n      {selectedCavo && (\n        <Card>\n          <CardContent>\n            <Typography variant=\"h6\" gutterBottom>\n              Dettagli del cavo selezionato\n            </Typography>\n            <TableContainer component={Paper}>\n              <Table size=\"small\">\n                <TableHead>\n                  <TableRow>\n                    <TableCell>ID Cavo</TableCell>\n                    <TableCell>Tipologia</TableCell>\n                    <TableCell>Formazione</TableCell>\n                    <TableCell>Utility</TableCell>\n                    <TableCell>Metri teorici</TableCell>\n                    <TableCell>Stato</TableCell>\n                  </TableRow>\n                </TableHead>\n                <TableBody>\n                  <TableRow>\n                    <TableCell>{selectedCavo.id_cavo}</TableCell>\n                    <TableCell>{selectedCavo.tipologia}</TableCell>\n                    <TableCell>{selectedCavo.sezione}</TableCell>\n                    <TableCell>{selectedCavo.utility}</TableCell>\n                    <TableCell>{selectedCavo.metri_teorici}m</TableCell>\n                    <TableCell>{selectedCavo.stato_installazione}</TableCell>\n                  </TableRow>\n                </TableBody>\n              </Table>\n            </TableContainer>\n          </CardContent>\n        </Card>\n      )}\n\n      {/* Dialogo di conferma */}\n      <Dialog\n        open={confirmDialogOpen}\n        onClose={handleConfirmDialogClose}\n      >\n        <DialogTitle>Conferma operazione</DialogTitle>\n        <DialogContent>\n          <DialogContentText>\n            {confirmDialogMessage}\n          </DialogContentText>\n        </DialogContent>\n        <DialogActions>\n          <Button onClick={handleConfirmDialogClose} color=\"primary\">\n            Annulla\n          </Button>\n          <Button onClick={handleConfirmDialogConfirm} color=\"primary\" autoFocus>\n            Conferma\n          </Button>\n        </DialogActions>\n      </Dialog>\n    </Box>\n  );\n};\n\nexport default MetriPosatiForm;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAClD,SAASC,SAAS,EAAEC,WAAW,QAAQ,kBAAkB;AACzD,SACEC,GAAG,EACHC,MAAM,EACNC,IAAI,EACJC,WAAW,EACXC,gBAAgB,EAChBC,MAAM,EACNC,aAAa,EACbC,aAAa,EACbC,iBAAiB,EACjBC,WAAW,EACXC,WAAW,EACXC,cAAc,EACdC,IAAI,EACJC,UAAU,EACVC,QAAQ,EACRC,MAAM,EACNC,SAAS,EACTC,UAAU,EACVC,KAAK,EACLC,KAAK,EACLC,SAAS,EACTC,SAAS,EACTC,cAAc,EACdC,SAAS,EACTC,QAAQ,EACRC,IAAI,EACJC,QAAQ,EACRC,KAAK,QACA,eAAe;AACtB;;AAEA;AACA,OAAOC,iBAAiB,MAAM,kCAAkC;AAChE,OAAOC,WAAW,MAAM,4BAA4B,CAAC,CAAC;;AAEtD;AACA;AACA;AAFA,SAAAC,MAAA,IAAAC,OAAA;AAGA,MAAMC,eAAe,GAAGA,CAAA,KAAM;EAAAC,EAAA;EAC5B;EACA,MAAM;IAAEC;EAAW,CAAC,GAAGpC,SAAS,CAAC,CAAC;EAClC,MAAMqC,QAAQ,GAAGpC,WAAW,CAAC,CAAC;EAC9B,MAAM;IAAEqC;EAAgB,CAAC,GAAGC,WAAW,CAAC,CAAC;;EAEzC;EACA,MAAM,CAACC,IAAI,EAAEC,OAAO,CAAC,GAAG3C,QAAQ,CAAC,EAAE,CAAC;EACpC,MAAM,CAAC4C,MAAM,EAAEC,SAAS,CAAC,GAAG7C,QAAQ,CAAC,EAAE,CAAC;EACxC,MAAM,CAAC8C,YAAY,EAAEC,eAAe,CAAC,GAAG/C,QAAQ,CAAC,IAAI,CAAC;EACtD,MAAM,CAACgD,QAAQ,EAAEC,WAAW,CAAC,GAAGjD,QAAQ,CAAC;IACvCkD,OAAO,EAAE,EAAE;IACXC,YAAY,EAAE,EAAE;IAChBC,SAAS,EAAE;EACb,CAAC,CAAC;EACF,MAAM,CAACC,MAAM,EAAEC,SAAS,CAAC,GAAGtD,QAAQ,CAAC,CAAC,CAAC,CAAC;EACxC,MAAM,CAACuD,OAAO,EAAEC,UAAU,CAAC,GAAGxD,QAAQ,CAAC,KAAK,CAAC;EAC7C,MAAM,CAACyD,aAAa,EAAEC,gBAAgB,CAAC,GAAG1D,QAAQ,CAAC,KAAK,CAAC;EACzD,MAAM,CAAC2D,iBAAiB,EAAEC,oBAAoB,CAAC,GAAG5D,QAAQ,CAAC,KAAK,CAAC;EACjE,MAAM,CAAC6D,oBAAoB,EAAEC,uBAAuB,CAAC,GAAG9D,QAAQ,CAAC,EAAE,CAAC;EACpE,MAAM,CAAC+D,mBAAmB,EAAEC,sBAAsB,CAAC,GAAGhE,QAAQ,CAAC,IAAI,CAAC;;EAEpE;EACAC,SAAS,CAAC,MAAM;IACdgE,QAAQ,CAAC,CAAC;EACZ,CAAC,EAAE,CAAC3B,UAAU,CAAC,CAAC;;EAEhB;EACArC,SAAS,CAAC,MAAM;IACd,IAAI6C,YAAY,EAAE;MAChBoB,UAAU,CAAC,CAAC;IACd;EACF,CAAC,EAAE,CAACpB,YAAY,CAAC,CAAC;;EAElB;AACF;AACA;EACE,MAAMmB,QAAQ,GAAG,MAAAA,CAAA,KAAY;IAC3B,IAAI;MACFT,UAAU,CAAC,IAAI,CAAC;MAChB,MAAMW,QAAQ,GAAG,MAAMlC,WAAW,CAACmC,OAAO,CAAC9B,UAAU,CAAC;;MAEtD;MACA,MAAM+B,gBAAgB,GAAGF,QAAQ,CAACG,MAAM,CAACC,IAAI,IAC3CA,IAAI,CAACC,mBAAmB,KAAK,eAAe,IAC5CD,IAAI,CAACC,mBAAmB,KAAK,UAC/B,CAAC;MAED7B,OAAO,CAAC0B,gBAAgB,CAAC;IAC3B,CAAC,CAAC,OAAOI,KAAK,EAAE;MACdC,OAAO,CAACD,KAAK,CAAC,kCAAkC,EAAEA,KAAK,CAAC;MACxDjC,eAAe,CAAC,mCAAmC,IAAIiC,KAAK,CAACE,MAAM,IAAI,oBAAoB,CAAC,EAAE;QAC5FC,OAAO,EAAE;MACX,CAAC,CAAC;IACJ,CAAC,SAAS;MACRpB,UAAU,CAAC,KAAK,CAAC;IACnB;EACF,CAAC;;EAED;AACF;AACA;EACE,MAAMU,UAAU,GAAG,MAAAA,CAAA,KAAY;IAC7B,IAAI,CAACpB,YAAY,EAAE;IAEnB,IAAI;MACFY,gBAAgB,CAAC,IAAI,CAAC;;MAEtB;MACA,MAAMmB,iBAAiB,GAAG,MAAM7C,iBAAiB,CAAC8C,oBAAoB,CACpExC,UAAU,EACVQ,YAAY,CAACiC,SAAS,EACtBjC,YAAY,CAACkC,OACf,CAAC;;MAED;MACAH,iBAAiB,CAACI,IAAI,CAAC,CAACC,CAAC,EAAEC,CAAC,KAAKA,CAAC,CAACC,aAAa,GAAGF,CAAC,CAACE,aAAa,CAAC;MAEnEvC,SAAS,CAACgC,iBAAiB,CAAC;MAC5BH,OAAO,CAACW,GAAG,CAAC,YAAYR,iBAAiB,CAACS,MAAM,qBAAqB,CAAC;IACxE,CAAC,CAAC,OAAOb,KAAK,EAAE;MACdC,OAAO,CAACD,KAAK,CAAC,kDAAkD,EAAEA,KAAK,CAAC;MACxEjC,eAAe,CAAC,uCAAuC,IAAIiC,KAAK,CAACE,MAAM,IAAI,oBAAoB,CAAC,EAAE;QAChGC,OAAO,EAAE;MACX,CAAC,CAAC;IACJ,CAAC,SAAS;MACRlB,gBAAgB,CAAC,KAAK,CAAC;IACzB;EACF,CAAC;;EAED;AACF;AACA;EACE,MAAM6B,gBAAgB,GAAIC,KAAK,IAAK;IAClC,MAAMC,MAAM,GAAGD,KAAK,CAACE,MAAM,CAACC,KAAK;IACjC1C,WAAW,CAAC;MACV,GAAGD,QAAQ;MACXE,OAAO,EAAEuC,MAAM;MACftC,YAAY,EAAE,EAAE;MAChBC,SAAS,EAAE;IACb,CAAC,CAAC;;IAEF;IACA,MAAMmB,IAAI,GAAG7B,IAAI,CAACkD,IAAI,CAACC,CAAC,IAAIA,CAAC,CAAC3C,OAAO,KAAKuC,MAAM,CAAC;IACjD1C,eAAe,CAACwB,IAAI,CAAC;;IAErB;IACAjB,SAAS,CAAC,CAAC,CAAC,CAAC;EACf,CAAC;;EAED;AACF;AACA;EACE,MAAMwC,gBAAgB,GAAIN,KAAK,IAAK;IAClC,MAAM;MAAEO,IAAI;MAAEJ;IAAM,CAAC,GAAGH,KAAK,CAACE,MAAM;;IAEpC;IACA,IAAIK,IAAI,KAAK,cAAc,EAAE;MAC3B;MACA,MAAMC,cAAc,GAAGL,KAAK,CAACM,OAAO,CAAC,UAAU,EAAE,EAAE,CAAC;;MAEpD;MACA,MAAMC,KAAK,GAAGF,cAAc,CAACG,KAAK,CAAC,GAAG,CAAC;MACvC,MAAMC,UAAU,GAAGF,KAAK,CAACZ,MAAM,GAAG,CAAC,GAC/BY,KAAK,CAAC,CAAC,CAAC,GAAG,GAAG,GAAGA,KAAK,CAACG,KAAK,CAAC,CAAC,CAAC,CAACC,IAAI,CAAC,EAAE,CAAC,GACxCN,cAAc;MAElB/C,WAAW,CAAC;QACV,GAAGD,QAAQ;QACX,CAAC+C,IAAI,GAAGK;MACV,CAAC,CAAC;IACJ,CAAC,MAAM;MACLnD,WAAW,CAAC;QACV,GAAGD,QAAQ;QACX,CAAC+C,IAAI,GAAGJ;MACV,CAAC,CAAC;IACJ;;IAEA;IACA,IAAItC,MAAM,CAAC0C,IAAI,CAAC,EAAE;MAChBzC,SAAS,CAAC;QACR,GAAGD,MAAM;QACT,CAAC0C,IAAI,GAAG;MACV,CAAC,CAAC;IACJ;EACF,CAAC;;EAED;AACF;AACA;EACE,MAAMQ,YAAY,GAAGA,CAAA,KAAM;IACzB,MAAMC,SAAS,GAAG,CAAC,CAAC;;IAEpB;IACA,IAAI,CAACxD,QAAQ,CAACE,OAAO,EAAE;MACrBsD,SAAS,CAACtD,OAAO,GAAG,mBAAmB;IACzC;;IAEA;IACA,IAAI,CAACF,QAAQ,CAACG,YAAY,EAAE;MAC1BqD,SAAS,CAACrD,YAAY,GAAG,0BAA0B;IACrD,CAAC,MAAM;MACL,MAAMsD,WAAW,GAAGC,UAAU,CAAC1D,QAAQ,CAACG,YAAY,CAAC;MAErD,IAAIwD,KAAK,CAACF,WAAW,CAAC,EAAE;QACtBD,SAAS,CAACrD,YAAY,GAAG,wCAAwC;MACnE,CAAC,MAAM,IAAIsD,WAAW,GAAG,CAAC,EAAE;QAC1BD,SAAS,CAACrD,YAAY,GAAG,4CAA4C;MACvE;;MAEA;MACA,IAAIL,YAAY,IAAI2D,WAAW,GAAGC,UAAU,CAAC5D,YAAY,CAAC8D,aAAa,IAAI,CAAC,CAAC,EAAE;QAC7E;QACAlC,OAAO,CAACmC,IAAI,CAAC,mBAAmBJ,WAAW,+BAA+B3D,YAAY,CAAC8D,aAAa,GAAG,CAAC;MAC1G;;MAEA;MACA,IAAI5D,QAAQ,CAACI,SAAS,IAAIJ,QAAQ,CAACI,SAAS,KAAK,cAAc,EAAE;QAC/D,MAAM0D,MAAM,GAAGlE,MAAM,CAACgD,IAAI,CAACT,CAAC,IAAIA,CAAC,CAAC/B,SAAS,KAAKJ,QAAQ,CAACI,SAAS,CAAC;QACnE,IAAI0D,MAAM,IAAIL,WAAW,GAAGC,UAAU,CAACI,MAAM,CAAC1B,aAAa,IAAI,CAAC,CAAC,EAAE;UACjE;UACAV,OAAO,CAACmC,IAAI,CAAC,mBAAmBJ,WAAW,4CAA4CK,MAAM,CAAC1B,aAAa,GAAG,CAAC;QACjH;MACF;IACF;;IAEA;IACA,IAAIpC,QAAQ,CAACG,YAAY,IAAIuD,UAAU,CAAC1D,QAAQ,CAACG,YAAY,CAAC,GAAG,CAAC,IAAI,CAACH,QAAQ,CAACI,SAAS,EAAE;MACzFoD,SAAS,CAACpD,SAAS,GAAG,uCAAuC;IAC/D;IAEAE,SAAS,CAACkD,SAAS,CAAC;IACpB,OAAOO,MAAM,CAACC,IAAI,CAACR,SAAS,CAAC,CAAClB,MAAM,KAAK,CAAC;EAC5C,CAAC;;EAED;AACF;AACA;EACE,MAAM2B,YAAY,GAAG,MAAOzB,KAAK,IAAK;IACpCA,KAAK,CAAC0B,cAAc,CAAC,CAAC;;IAEtB;IACA,IAAI,CAACX,YAAY,CAAC,CAAC,EAAE;MACnB;IACF;;IAEA;IACA,MAAM;MAAErD,OAAO;MAAEC,YAAY;MAAEC;IAAU,CAAC,GAAGJ,QAAQ;IACrD,MAAMyD,WAAW,GAAGC,UAAU,CAACvD,YAAY,CAAC;;IAE5C;IACA,IAAIgE,cAAc,GAAG,EAAE;;IAEvB;IACA,IAAIrE,YAAY,IAAI2D,WAAW,GAAGC,UAAU,CAAC5D,YAAY,CAAC8D,aAAa,IAAI,CAAC,CAAC,EAAE;MAC7EO,cAAc,IAAI,mBAAmBV,WAAW,+BAA+B3D,YAAY,CAAC8D,aAAa,MAAM;IACjH;;IAEA;IACA,IAAIxD,SAAS,IAAIA,SAAS,KAAK,cAAc,EAAE;MAC7C,MAAM0D,MAAM,GAAGlE,MAAM,CAACgD,IAAI,CAACT,CAAC,IAAIA,CAAC,CAAC/B,SAAS,KAAKA,SAAS,CAAC;MAC1D,IAAI0D,MAAM,IAAIL,WAAW,GAAGC,UAAU,CAACI,MAAM,CAAC1B,aAAa,IAAI,CAAC,CAAC,EAAE;QACjE+B,cAAc,IAAI,mBAAmBV,WAAW,4CAA4CK,MAAM,CAAC1B,aAAa,MAAM;MACxH;IACF;;IAEA;IACA,IAAI+B,cAAc,EAAE;MAClBrD,uBAAuB,CAACqD,cAAc,GAAG,6BAA6B,CAAC;MACvEnD,sBAAsB,CAAC,MAAM,MAAMoD,iBAAiB,CAAClE,OAAO,EAAEuD,WAAW,EAAErD,SAAS,CAAC,CAAC;MACtFQ,oBAAoB,CAAC,IAAI,CAAC;MAC1B;IACF;;IAEA;IACA,MAAMwD,iBAAiB,CAAClE,OAAO,EAAEuD,WAAW,EAAErD,SAAS,CAAC;EAC1D,CAAC;;EAED;AACF;AACA;EACE,MAAMgE,iBAAiB,GAAG,MAAAA,CAAOlE,OAAO,EAAEuD,WAAW,EAAErD,SAAS,KAAK;IACnE,IAAI;MACFI,UAAU,CAAC,IAAI,CAAC;;MAEhB;MACAI,oBAAoB,CAAC,KAAK,CAAC;;MAE3B;MACA,MAAMyD,QAAQ,GAAG,MAAMrF,iBAAiB,CAACsF,iBAAiB,CACxDhF,UAAU,EACVY,OAAO,EACPuD,WAAW,EACXrD,SACF,CAAC;;MAED;MACAZ,eAAe,CAAC,sCAAsC,EAAE;QAAEoC,OAAO,EAAE;MAAU,CAAC,CAAC;;MAE/E;MACA3B,WAAW,CAAC;QACVC,OAAO,EAAE,EAAE;QACXC,YAAY,EAAE,EAAE;QAChBC,SAAS,EAAE;MACb,CAAC,CAAC;MACFL,eAAe,CAAC,IAAI,CAAC;;MAErB;MACA,MAAMkB,QAAQ,CAAC,CAAC;IAClB,CAAC,CAAC,OAAOQ,KAAK,EAAE;MACdC,OAAO,CAACD,KAAK,CAAC,8CAA8C,EAAEA,KAAK,CAAC;MACpEjC,eAAe,CAAC,UAAU,IAAIiC,KAAK,CAACE,MAAM,IAAI,oBAAoB,CAAC,EAAE;QAAEC,OAAO,EAAE;MAAQ,CAAC,CAAC;IAC5F,CAAC,SAAS;MACRpB,UAAU,CAAC,KAAK,CAAC;IACnB;EACF,CAAC;;EAED;AACF;AACA;EACE,MAAM+D,wBAAwB,GAAGA,CAAA,KAAM;IACrC3D,oBAAoB,CAAC,KAAK,CAAC;IAC3BI,sBAAsB,CAAC,IAAI,CAAC;EAC9B,CAAC;;EAED;AACF;AACA;EACE,MAAMwD,0BAA0B,GAAGA,CAAA,KAAM;IACvC,IAAIzD,mBAAmB,EAAE;MACvBA,mBAAmB,CAAC,CAAC;IACvB;EACF,CAAC;EAED,oBACE5B,OAAA,CAAC/B,GAAG;IAACqH,EAAE,EAAE;MAAEC,CAAC,EAAE;IAAE,CAAE;IAAAC,QAAA,gBAChBxF,OAAA,CAACd,UAAU;MAACuD,OAAO,EAAC,IAAI;MAACgD,YAAY;MAAAD,QAAA,EAAC;IAEtC;MAAAE,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAY,CAAC,eAEb7F,OAAA,CAAC7B,IAAI;MAACmH,EAAE,EAAE;QAAEQ,EAAE,EAAE;MAAE,CAAE;MAAAN,QAAA,eAClBxF,OAAA,CAAC5B,WAAW;QAAAoH,QAAA,eACVxF,OAAA;UAAM+F,QAAQ,EAAEjB,YAAa;UAAAU,QAAA,eAC3BxF,OAAA,CAACnB,IAAI;YAACmH,SAAS;YAACC,OAAO,EAAE,CAAE;YAAAT,QAAA,gBAEzBxF,OAAA,CAACnB,IAAI;cAACqH,IAAI;cAACC,EAAE,EAAE,EAAG;cAACC,EAAE,EAAE,CAAE;cAAAZ,QAAA,eACvBxF,OAAA,CAACrB,WAAW;gBAAC0H,SAAS;gBAAC/D,KAAK,EAAE,CAAC,CAACpB,MAAM,CAACH,OAAQ;gBAAAyE,QAAA,gBAC7CxF,OAAA,CAAClB,UAAU;kBAACwH,EAAE,EAAC,mBAAmB;kBAAAd,QAAA,EAAC;gBAAI;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAY,CAAC,eACpD7F,OAAA,CAAChB,MAAM;kBACLuH,OAAO,EAAC,mBAAmB;kBAC3BD,EAAE,EAAC,aAAa;kBAChB1C,IAAI,EAAC,SAAS;kBACdJ,KAAK,EAAE3C,QAAQ,CAACE,OAAQ;kBACxByF,QAAQ,EAAEpD,gBAAiB;kBAC3BqD,QAAQ,EAAErF,OAAQ;kBAAAoE,QAAA,gBAElBxF,OAAA,CAACjB,QAAQ;oBAACyE,KAAK,EAAC,EAAE;oBAAAgC,QAAA,eAChBxF,OAAA;sBAAAwF,QAAA,EAAI;oBAAiB;sBAAAE,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAI;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAClB,CAAC,EACVtF,IAAI,CAACmG,GAAG,CAAEtE,IAAI,iBACbpC,OAAA,CAACjB,QAAQ;oBAAoByE,KAAK,EAAEpB,IAAI,CAACrB,OAAQ;oBAAAyE,QAAA,GAC9CpD,IAAI,CAACrB,OAAO,EAAC,KAAG,EAACqB,IAAI,CAACQ,SAAS,EAAC,KAAG,EAACR,IAAI,CAACS,OAAO,EAAC,KAAG,EAACT,IAAI,CAACuE,OAAO;kBAAA,GADrDvE,IAAI,CAACrB,OAAO;oBAAA2E,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAEjB,CACX,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACI,CAAC,EACR3E,MAAM,CAACH,OAAO,iBAAIf,OAAA,CAACpB,cAAc;kBAAA4G,QAAA,EAAEtE,MAAM,CAACH;gBAAO;kBAAA2E,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAiB,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACzD;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACV,CAAC,eAGP7F,OAAA,CAACnB,IAAI;cAACqH,IAAI;cAACC,EAAE,EAAE,EAAG;cAACC,EAAE,EAAE,CAAE;cAAAZ,QAAA,eACvBxF,OAAA,CAACf,SAAS;gBACRoH,SAAS;gBACTO,KAAK,EAAC,cAAc;gBACpBhD,IAAI,EAAC,cAAc;gBACnBJ,KAAK,EAAE3C,QAAQ,CAACG,YAAa;gBAC7BwF,QAAQ,EAAE7C,gBAAiB;gBAC3BrB,KAAK,EAAE,CAAC,CAACpB,MAAM,CAACF,YAAa;gBAC7B6F,UAAU,EAAE3F,MAAM,CAACF,YAAa;gBAChCyF,QAAQ,EAAE,CAAC9F,YAAY,IAAIS,OAAQ;gBACnC0F,UAAU,EAAE;kBACVC,YAAY,eAAE/G,OAAA,CAACd,UAAU;oBAACuD,OAAO,EAAC,OAAO;oBAAA+C,QAAA,EAAC;kBAAC;oBAAAE,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAY;gBACzD;cAAE;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACE,CAAC,eAGP7F,OAAA,CAACnB,IAAI;cAACqH,IAAI;cAACC,EAAE,EAAE,EAAG;cAAAX,QAAA,eAChBxF,OAAA,CAACrB,WAAW;gBAAC0H,SAAS;gBAAC/D,KAAK,EAAE,CAAC,CAACpB,MAAM,CAACD,SAAU;gBAAAuE,QAAA,gBAC/CxF,OAAA,CAAClB,UAAU;kBAACwH,EAAE,EAAC,qBAAqB;kBAAAd,QAAA,EAAC;gBAAM;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAY,CAAC,eACxD7F,OAAA,CAAChB,MAAM;kBACLuH,OAAO,EAAC,qBAAqB;kBAC7BD,EAAE,EAAC,eAAe;kBAClB1C,IAAI,EAAC,WAAW;kBAChBJ,KAAK,EAAE3C,QAAQ,CAACI,SAAU;kBAC1BuF,QAAQ,EAAE7C,gBAAiB;kBAC3B8C,QAAQ,EAAE,CAAC9F,YAAY,IAAIW,aAAa,IAAIF,OAAQ;kBAAAoE,QAAA,gBAEpDxF,OAAA,CAACjB,QAAQ;oBAACyE,KAAK,EAAC,EAAE;oBAAAgC,QAAA,eAChBxF,OAAA;sBAAAwF,QAAA,EAAI;oBAAoB;sBAAAE,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAI;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACrB,CAAC,eACX7F,OAAA,CAACjB,QAAQ;oBAACyE,KAAK,EAAC,cAAc;oBAAAgC,QAAA,EAAC;kBAE/B;oBAAAE,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAU,CAAC,EACVpF,MAAM,CAACiG,GAAG,CAAE/B,MAAM,iBACjB3E,OAAA,CAACjB,QAAQ;oBAEPyE,KAAK,EAAEmB,MAAM,CAAC1D,SAAU;oBACxBqE,EAAE,EAAE;sBACF0B,OAAO,EAAErG,YAAY,IACnBsG,MAAM,CAACtC,MAAM,CAAC/B,SAAS,IAAI,EAAE,CAAC,CAACsE,IAAI,CAAC,CAAC,KAAKD,MAAM,CAACtG,YAAY,CAACiC,SAAS,IAAI,EAAE,CAAC,CAACsE,IAAI,CAAC,CAAC,IACrFD,MAAM,CAACtC,MAAM,CAAC9B,OAAO,IAAI,GAAG,CAAC,CAACqE,IAAI,CAAC,CAAC,KAAKD,MAAM,CAACtG,YAAY,CAACkC,OAAO,IAAI,GAAG,CAAC,CAACqE,IAAI,CAAC,CAAC,GACnF,yBAAyB,GAAG;oBAChC,CAAE;oBAAA1B,QAAA,GAEDb,MAAM,CAAC1D,SAAS,EAAC,KAAG,EAAC0D,MAAM,CAAC/B,SAAS,EAAC,KAAG,EAAC+B,MAAM,CAAC9B,OAAO,EAAC,KAAG,EAAC8B,MAAM,CAAC1B,aAAa,EAAC,WACnF,EAACtC,YAAY,IACXsG,MAAM,CAACtC,MAAM,CAAC/B,SAAS,IAAI,EAAE,CAAC,CAACsE,IAAI,CAAC,CAAC,KAAKD,MAAM,CAACtG,YAAY,CAACiC,SAAS,IAAI,EAAE,CAAC,CAACsE,IAAI,CAAC,CAAC,IACrFD,MAAM,CAACtC,MAAM,CAAC9B,OAAO,IAAI,GAAG,CAAC,CAACqE,IAAI,CAAC,CAAC,KAAKD,MAAM,CAACtG,YAAY,CAACkC,OAAO,IAAI,GAAG,CAAC,CAACqE,IAAI,CAAC,CAAC,iBACjFlH,OAAA,CAACN,IAAI;sBACHkH,KAAK,EAAC,aAAa;sBACnBO,KAAK,EAAC,SAAS;sBACfC,IAAI,EAAC,OAAO;sBACZ9B,EAAE,EAAE;wBAAE+B,EAAE,EAAE;sBAAE;oBAAE;sBAAA3B,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACf,CACF;kBAAA,GAnBElB,MAAM,CAAC1D,SAAS;oBAAAyE,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAqBb,CACX,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACI,CAAC,EACR3E,MAAM,CAACD,SAAS,iBAAIjB,OAAA,CAACpB,cAAc;kBAAA4G,QAAA,EAAEtE,MAAM,CAACD;gBAAS;kBAAAyE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAiB,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC7D;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACV,CAAC,eAGP7F,OAAA,CAACnB,IAAI;cAACqH,IAAI;cAACC,EAAE,EAAE,EAAG;cAAAX,QAAA,eAChBxF,OAAA,CAAC/B,GAAG;gBAACqH,EAAE,EAAE;kBAAEgC,OAAO,EAAE,MAAM;kBAAEC,cAAc,EAAE,UAAU;kBAAEC,GAAG,EAAE;gBAAE,CAAE;gBAAAhC,QAAA,gBAC/DxF,OAAA,CAAC9B,MAAM;kBACLuE,OAAO,EAAC,UAAU;kBAClBgF,OAAO,EAAEA,CAAA,KAAMrH,QAAQ,CAAC,aAAaD,UAAU,OAAO,CAAE;kBACxDsG,QAAQ,EAAErF,OAAQ;kBAAAoE,QAAA,EACnB;gBAED;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,eACT7F,OAAA,CAAC9B,MAAM;kBACLwJ,IAAI,EAAC,QAAQ;kBACbjF,OAAO,EAAC,WAAW;kBACnB0E,KAAK,EAAC,SAAS;kBACfV,QAAQ,EAAErF,OAAQ;kBAClBuG,SAAS,EAAEvG,OAAO,iBAAIpB,OAAA,CAAC3B,gBAAgB;oBAAC+I,IAAI,EAAE,EAAG;oBAACD,KAAK,EAAC;kBAAS;oBAAAzB,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE,CAAE;kBAAAL,QAAA,EACtE;gBAED;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACN;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACF,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACI;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACV,CAAC,EAGNlF,YAAY,iBACXX,OAAA,CAAC7B,IAAI;MAAAqH,QAAA,eACHxF,OAAA,CAAC5B,WAAW;QAAAoH,QAAA,gBACVxF,OAAA,CAACd,UAAU;UAACuD,OAAO,EAAC,IAAI;UAACgD,YAAY;UAAAD,QAAA,EAAC;QAEtC;UAAAE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAY,CAAC,eACb7F,OAAA,CAACT,cAAc;UAACqI,SAAS,EAAEzI,KAAM;UAAAqG,QAAA,eAC/BxF,OAAA,CAACZ,KAAK;YAACgI,IAAI,EAAC,OAAO;YAAA5B,QAAA,gBACjBxF,OAAA,CAACR,SAAS;cAAAgG,QAAA,eACRxF,OAAA,CAACP,QAAQ;gBAAA+F,QAAA,gBACPxF,OAAA,CAACV,SAAS;kBAAAkG,QAAA,EAAC;gBAAO;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAW,CAAC,eAC9B7F,OAAA,CAACV,SAAS;kBAAAkG,QAAA,EAAC;gBAAS;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAW,CAAC,eAChC7F,OAAA,CAACV,SAAS;kBAAAkG,QAAA,EAAC;gBAAU;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAW,CAAC,eACjC7F,OAAA,CAACV,SAAS;kBAAAkG,QAAA,EAAC;gBAAO;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAW,CAAC,eAC9B7F,OAAA,CAACV,SAAS;kBAAAkG,QAAA,EAAC;gBAAa;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAW,CAAC,eACpC7F,OAAA,CAACV,SAAS;kBAAAkG,QAAA,EAAC;gBAAK;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAW,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACpB;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACF,CAAC,eACZ7F,OAAA,CAACX,SAAS;cAAAmG,QAAA,eACRxF,OAAA,CAACP,QAAQ;gBAAA+F,QAAA,gBACPxF,OAAA,CAACV,SAAS;kBAAAkG,QAAA,EAAE7E,YAAY,CAACI;gBAAO;kBAAA2E,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAY,CAAC,eAC7C7F,OAAA,CAACV,SAAS;kBAAAkG,QAAA,EAAE7E,YAAY,CAACiC;gBAAS;kBAAA8C,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAY,CAAC,eAC/C7F,OAAA,CAACV,SAAS;kBAAAkG,QAAA,EAAE7E,YAAY,CAACkC;gBAAO;kBAAA6C,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAY,CAAC,eAC7C7F,OAAA,CAACV,SAAS;kBAAAkG,QAAA,EAAE7E,YAAY,CAACgG;gBAAO;kBAAAjB,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAY,CAAC,eAC7C7F,OAAA,CAACV,SAAS;kBAAAkG,QAAA,GAAE7E,YAAY,CAAC8D,aAAa,EAAC,GAAC;gBAAA;kBAAAiB,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAW,CAAC,eACpD7F,OAAA,CAACV,SAAS;kBAAAkG,QAAA,EAAE7E,YAAY,CAAC0B;gBAAmB;kBAAAqD,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAY,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACjD;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACF,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACP;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACM,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACN;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACV,CACP,eAGD7F,OAAA,CAAC1B,MAAM;MACLuJ,IAAI,EAAErG,iBAAkB;MACxBsG,OAAO,EAAE1C,wBAAyB;MAAAI,QAAA,gBAElCxF,OAAA,CAACtB,WAAW;QAAA8G,QAAA,EAAC;MAAmB;QAAAE,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAa,CAAC,eAC9C7F,OAAA,CAACxB,aAAa;QAAAgH,QAAA,eACZxF,OAAA,CAACvB,iBAAiB;UAAA+G,QAAA,EACf9D;QAAoB;UAAAgE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACJ;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACP,CAAC,eAChB7F,OAAA,CAACzB,aAAa;QAAAiH,QAAA,gBACZxF,OAAA,CAAC9B,MAAM;UAACuJ,OAAO,EAAErC,wBAAyB;UAAC+B,KAAK,EAAC,SAAS;UAAA3B,QAAA,EAAC;QAE3D;UAAAE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC,eACT7F,OAAA,CAAC9B,MAAM;UAACuJ,OAAO,EAAEpC,0BAA2B;UAAC8B,KAAK,EAAC,SAAS;UAACY,SAAS;UAAAvC,QAAA,EAAC;QAEvE;UAAAE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACI,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACV,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACN,CAAC;AAEV,CAAC;AAAC3F,EAAA,CA7dID,eAAe;EAAA,QAEIlC,SAAS,EACfC,WAAW;AAAA;AAAAgK,EAAA,GAHxB/H,eAAe;AA+drB,eAAeA,eAAe;AAAC,IAAA+H,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}