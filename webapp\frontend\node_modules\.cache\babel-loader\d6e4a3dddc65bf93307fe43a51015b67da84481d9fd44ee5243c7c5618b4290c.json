{"ast": null, "code": "import { buildLocalizeFn } from \"../../_lib/buildLocalizeFn.mjs\";\nconst eraValues = {\n  narrow: [\"б.з.д.\", \"б.з.\"],\n  abbreviated: [\"б.з.д.\", \"б.з.\"],\n  wide: [\"біздің заманымызға дейін\", \"біздің заманымыз\"]\n};\nconst quarterValues = {\n  narrow: [\"1\", \"2\", \"3\", \"4\"],\n  abbreviated: [\"1-ші тоқ.\", \"2-ші тоқ.\", \"3-ші тоқ.\", \"4-ші тоқ.\"],\n  wide: [\"1-ші тоқсан\", \"2-ші тоқсан\", \"3-ші тоқсан\", \"4-ші тоқсан\"]\n};\nconst monthValues = {\n  narrow: [\"Қ\", \"А\", \"Н\", \"С\", \"М\", \"М\", \"Ш\", \"Т\", \"Қ\", \"Қ\", \"Қ\", \"Ж\"],\n  abbreviated: [\"қаң\", \"ақп\", \"нау\", \"сәу\", \"мам\", \"мау\", \"шіл\", \"там\", \"қыр\", \"қаз\", \"қар\", \"жел\"],\n  wide: [\"қаңтар\", \"ақпан\", \"наурыз\", \"сәуір\", \"мамыр\", \"маусым\", \"шілде\", \"тамыз\", \"қыркүйек\", \"қазан\", \"қараша\", \"желтоқсан\"]\n};\nconst formattingMonthValues = {\n  narrow: [\"Қ\", \"А\", \"Н\", \"С\", \"М\", \"М\", \"Ш\", \"Т\", \"Қ\", \"Қ\", \"Қ\", \"Ж\"],\n  abbreviated: [\"қаң\", \"ақп\", \"нау\", \"сәу\", \"мам\", \"мау\", \"шіл\", \"там\", \"қыр\", \"қаз\", \"қар\", \"жел\"],\n  wide: [\"қаңтар\", \"ақпан\", \"наурыз\", \"сәуір\", \"мамыр\", \"маусым\", \"шілде\", \"тамыз\", \"қыркүйек\", \"қазан\", \"қараша\", \"желтоқсан\"]\n};\nconst dayValues = {\n  narrow: [\"Ж\", \"Д\", \"С\", \"С\", \"Б\", \"Ж\", \"С\"],\n  short: [\"жс\", \"дс\", \"сс\", \"ср\", \"бс\", \"жм\", \"сб\"],\n  abbreviated: [\"жс\", \"дс\", \"сс\", \"ср\", \"бс\", \"жм\", \"сб\"],\n  wide: [\"жексенбі\", \"дүйсенбі\", \"сейсенбі\", \"сәрсенбі\", \"бейсенбі\", \"жұма\", \"сенбі\"]\n};\nconst dayPeriodValues = {\n  narrow: {\n    am: \"ТД\",\n    pm: \"ТК\",\n    midnight: \"түн ортасы\",\n    noon: \"түс\",\n    morning: \"таң\",\n    afternoon: \"күндіз\",\n    evening: \"кеш\",\n    night: \"түн\"\n  },\n  wide: {\n    am: \"ТД\",\n    pm: \"ТК\",\n    midnight: \"түн ортасы\",\n    noon: \"түс\",\n    morning: \"таң\",\n    afternoon: \"күндіз\",\n    evening: \"кеш\",\n    night: \"түн\"\n  }\n};\nconst formattingDayPeriodValues = {\n  narrow: {\n    am: \"ТД\",\n    pm: \"ТК\",\n    midnight: \"түн ортасында\",\n    noon: \"түс\",\n    morning: \"таң\",\n    afternoon: \"күн\",\n    evening: \"кеш\",\n    night: \"түн\"\n  },\n  wide: {\n    am: \"ТД\",\n    pm: \"ТК\",\n    midnight: \"түн ортасында\",\n    noon: \"түсте\",\n    morning: \"таңертең\",\n    afternoon: \"күндіз\",\n    evening: \"кеште\",\n    night: \"түнде\"\n  }\n};\nconst suffixes = {\n  0: \"-ші\",\n  1: \"-ші\",\n  2: \"-ші\",\n  3: \"-ші\",\n  4: \"-ші\",\n  5: \"-ші\",\n  6: \"-шы\",\n  7: \"-ші\",\n  8: \"-ші\",\n  9: \"-шы\",\n  10: \"-шы\",\n  20: \"-шы\",\n  30: \"-шы\",\n  40: \"-шы\",\n  50: \"-ші\",\n  60: \"-шы\",\n  70: \"-ші\",\n  80: \"-ші\",\n  90: \"-шы\",\n  100: \"-ші\"\n};\nconst ordinalNumber = (dirtyNumber, _options) => {\n  const number = Number(dirtyNumber);\n  const mod10 = number % 10;\n  const b = number >= 100 ? 100 : null;\n  const suffix = suffixes[number] || suffixes[mod10] || b && suffixes[b] || \"\";\n  return number + suffix;\n};\nexport const localize = {\n  ordinalNumber,\n  era: buildLocalizeFn({\n    values: eraValues,\n    defaultWidth: \"wide\"\n  }),\n  quarter: buildLocalizeFn({\n    values: quarterValues,\n    defaultWidth: \"wide\",\n    argumentCallback: quarter => quarter - 1\n  }),\n  month: buildLocalizeFn({\n    values: monthValues,\n    defaultWidth: \"wide\",\n    formattingValues: formattingMonthValues,\n    defaultFormattingWidth: \"wide\"\n  }),\n  day: buildLocalizeFn({\n    values: dayValues,\n    defaultWidth: \"wide\"\n  }),\n  dayPeriod: buildLocalizeFn({\n    values: dayPeriodValues,\n    defaultWidth: \"any\",\n    formattingValues: formattingDayPeriodValues,\n    defaultFormattingWidth: \"wide\"\n  })\n};", "map": {"version": 3, "names": ["buildLocalizeFn", "<PERSON><PERSON><PERSON><PERSON>", "narrow", "abbreviated", "wide", "quarterValues", "month<PERSON><PERSON><PERSON>", "formattingMonthValues", "dayV<PERSON><PERSON>", "short", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "am", "pm", "midnight", "noon", "morning", "afternoon", "evening", "night", "formattingDayPeriodValues", "suffixes", "ordinalNumber", "dirtyNumber", "_options", "number", "Number", "mod10", "b", "suffix", "localize", "era", "values", "defaultWidth", "quarter", "argument<PERSON>allback", "month", "formattingValues", "defaultFormattingWidth", "day", "<PERSON><PERSON><PERSON><PERSON>"], "sources": ["C:/CMS/webapp/frontend/node_modules/date-fns/locale/kk/_lib/localize.mjs"], "sourcesContent": ["import { buildLocalizeFn } from \"../../_lib/buildLocalizeFn.mjs\";\n\nconst eraValues = {\n  narrow: [\"б.з.д.\", \"б.з.\"],\n  abbreviated: [\"б.з.д.\", \"б.з.\"],\n  wide: [\"біздің заманымызға дейін\", \"біздің заманымыз\"],\n};\n\nconst quarterValues = {\n  narrow: [\"1\", \"2\", \"3\", \"4\"],\n  abbreviated: [\"1-ші тоқ.\", \"2-ші тоқ.\", \"3-ші тоқ.\", \"4-ші тоқ.\"],\n  wide: [\"1-ші тоқсан\", \"2-ші тоқсан\", \"3-ші тоқсан\", \"4-ші тоқсан\"],\n};\n\nconst monthValues = {\n  narrow: [\"Қ\", \"А\", \"Н\", \"С\", \"М\", \"М\", \"Ш\", \"Т\", \"Қ\", \"Қ\", \"Қ\", \"Ж\"],\n  abbreviated: [\n    \"қаң\",\n    \"ақп\",\n    \"нау\",\n    \"сәу\",\n    \"мам\",\n    \"мау\",\n    \"шіл\",\n    \"там\",\n    \"қыр\",\n    \"қаз\",\n    \"қар\",\n    \"жел\",\n  ],\n\n  wide: [\n    \"қаңтар\",\n    \"ақпан\",\n    \"наурыз\",\n    \"сәуір\",\n    \"мамыр\",\n    \"маусым\",\n    \"шілде\",\n    \"тамыз\",\n    \"қыркүйек\",\n    \"қазан\",\n    \"қараша\",\n    \"желтоқсан\",\n  ],\n};\nconst formattingMonthValues = {\n  narrow: [\"Қ\", \"А\", \"Н\", \"С\", \"М\", \"М\", \"Ш\", \"Т\", \"Қ\", \"Қ\", \"Қ\", \"Ж\"],\n  abbreviated: [\n    \"қаң\",\n    \"ақп\",\n    \"нау\",\n    \"сәу\",\n    \"мам\",\n    \"мау\",\n    \"шіл\",\n    \"там\",\n    \"қыр\",\n    \"қаз\",\n    \"қар\",\n    \"жел\",\n  ],\n\n  wide: [\n    \"қаңтар\",\n    \"ақпан\",\n    \"наурыз\",\n    \"сәуір\",\n    \"мамыр\",\n    \"маусым\",\n    \"шілде\",\n    \"тамыз\",\n    \"қыркүйек\",\n    \"қазан\",\n    \"қараша\",\n    \"желтоқсан\",\n  ],\n};\n\nconst dayValues = {\n  narrow: [\"Ж\", \"Д\", \"С\", \"С\", \"Б\", \"Ж\", \"С\"],\n  short: [\"жс\", \"дс\", \"сс\", \"ср\", \"бс\", \"жм\", \"сб\"],\n  abbreviated: [\"жс\", \"дс\", \"сс\", \"ср\", \"бс\", \"жм\", \"сб\"],\n  wide: [\n    \"жексенбі\",\n    \"дүйсенбі\",\n    \"сейсенбі\",\n    \"сәрсенбі\",\n    \"бейсенбі\",\n    \"жұма\",\n    \"сенбі\",\n  ],\n};\n\nconst dayPeriodValues = {\n  narrow: {\n    am: \"ТД\",\n    pm: \"ТК\",\n    midnight: \"түн ортасы\",\n    noon: \"түс\",\n    morning: \"таң\",\n    afternoon: \"күндіз\",\n    evening: \"кеш\",\n    night: \"түн\",\n  },\n  wide: {\n    am: \"ТД\",\n    pm: \"ТК\",\n    midnight: \"түн ортасы\",\n    noon: \"түс\",\n    morning: \"таң\",\n    afternoon: \"күндіз\",\n    evening: \"кеш\",\n    night: \"түн\",\n  },\n};\n\nconst formattingDayPeriodValues = {\n  narrow: {\n    am: \"ТД\",\n    pm: \"ТК\",\n    midnight: \"түн ортасында\",\n    noon: \"түс\",\n    morning: \"таң\",\n    afternoon: \"күн\",\n    evening: \"кеш\",\n    night: \"түн\",\n  },\n  wide: {\n    am: \"ТД\",\n    pm: \"ТК\",\n    midnight: \"түн ортасында\",\n    noon: \"түсте\",\n    morning: \"таңертең\",\n    afternoon: \"күндіз\",\n    evening: \"кеште\",\n    night: \"түнде\",\n  },\n};\n\nconst suffixes = {\n  0: \"-ші\",\n  1: \"-ші\",\n  2: \"-ші\",\n  3: \"-ші\",\n  4: \"-ші\",\n  5: \"-ші\",\n  6: \"-шы\",\n  7: \"-ші\",\n  8: \"-ші\",\n  9: \"-шы\",\n  10: \"-шы\",\n  20: \"-шы\",\n  30: \"-шы\",\n  40: \"-шы\",\n  50: \"-ші\",\n  60: \"-шы\",\n  70: \"-ші\",\n  80: \"-ші\",\n  90: \"-шы\",\n  100: \"-ші\",\n};\n\nconst ordinalNumber = (dirtyNumber, _options) => {\n  const number = Number(dirtyNumber);\n  const mod10 = number % 10;\n  const b = number >= 100 ? 100 : null;\n  const suffix =\n    suffixes[number] || suffixes[mod10] || (b && suffixes[b]) || \"\";\n\n  return number + suffix;\n};\n\nexport const localize = {\n  ordinalNumber,\n\n  era: buildLocalizeFn({\n    values: eraValues,\n    defaultWidth: \"wide\",\n  }),\n\n  quarter: buildLocalizeFn({\n    values: quarterValues,\n    defaultWidth: \"wide\",\n    argumentCallback: (quarter) => quarter - 1,\n  }),\n\n  month: buildLocalizeFn({\n    values: monthValues,\n    defaultWidth: \"wide\",\n    formattingValues: formattingMonthValues,\n    defaultFormattingWidth: \"wide\",\n  }),\n\n  day: buildLocalizeFn({\n    values: dayValues,\n    defaultWidth: \"wide\",\n  }),\n\n  dayPeriod: buildLocalizeFn({\n    values: dayPeriodValues,\n    defaultWidth: \"any\",\n    formattingValues: formattingDayPeriodValues,\n    defaultFormattingWidth: \"wide\",\n  }),\n};\n"], "mappings": "AAAA,SAASA,eAAe,QAAQ,gCAAgC;AAEhE,MAAMC,SAAS,GAAG;EAChBC,MAAM,EAAE,CAAC,QAAQ,EAAE,MAAM,CAAC;EAC1BC,WAAW,EAAE,CAAC,QAAQ,EAAE,MAAM,CAAC;EAC/BC,IAAI,EAAE,CAAC,0BAA0B,EAAE,kBAAkB;AACvD,CAAC;AAED,MAAMC,aAAa,GAAG;EACpBH,MAAM,EAAE,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,CAAC;EAC5BC,WAAW,EAAE,CAAC,WAAW,EAAE,WAAW,EAAE,WAAW,EAAE,WAAW,CAAC;EACjEC,IAAI,EAAE,CAAC,aAAa,EAAE,aAAa,EAAE,aAAa,EAAE,aAAa;AACnE,CAAC;AAED,MAAME,WAAW,GAAG;EAClBJ,MAAM,EAAE,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,CAAC;EACpEC,WAAW,EAAE,CACX,KAAK,EACL,KAAK,EACL,KAAK,EACL,KAAK,EACL,KAAK,EACL,KAAK,EACL,KAAK,EACL,KAAK,EACL,KAAK,EACL,KAAK,EACL,KAAK,EACL,KAAK,CACN;EAEDC,IAAI,EAAE,CACJ,QAAQ,EACR,OAAO,EACP,QAAQ,EACR,OAAO,EACP,OAAO,EACP,QAAQ,EACR,OAAO,EACP,OAAO,EACP,UAAU,EACV,OAAO,EACP,QAAQ,EACR,WAAW;AAEf,CAAC;AACD,MAAMG,qBAAqB,GAAG;EAC5BL,MAAM,EAAE,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,CAAC;EACpEC,WAAW,EAAE,CACX,KAAK,EACL,KAAK,EACL,KAAK,EACL,KAAK,EACL,KAAK,EACL,KAAK,EACL,KAAK,EACL,KAAK,EACL,KAAK,EACL,KAAK,EACL,KAAK,EACL,KAAK,CACN;EAEDC,IAAI,EAAE,CACJ,QAAQ,EACR,OAAO,EACP,QAAQ,EACR,OAAO,EACP,OAAO,EACP,QAAQ,EACR,OAAO,EACP,OAAO,EACP,UAAU,EACV,OAAO,EACP,QAAQ,EACR,WAAW;AAEf,CAAC;AAED,MAAMI,SAAS,GAAG;EAChBN,MAAM,EAAE,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,CAAC;EAC3CO,KAAK,EAAE,CAAC,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,CAAC;EACjDN,WAAW,EAAE,CAAC,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,CAAC;EACvDC,IAAI,EAAE,CACJ,UAAU,EACV,UAAU,EACV,UAAU,EACV,UAAU,EACV,UAAU,EACV,MAAM,EACN,OAAO;AAEX,CAAC;AAED,MAAMM,eAAe,GAAG;EACtBR,MAAM,EAAE;IACNS,EAAE,EAAE,IAAI;IACRC,EAAE,EAAE,IAAI;IACRC,QAAQ,EAAE,YAAY;IACtBC,IAAI,EAAE,KAAK;IACXC,OAAO,EAAE,KAAK;IACdC,SAAS,EAAE,QAAQ;IACnBC,OAAO,EAAE,KAAK;IACdC,KAAK,EAAE;EACT,CAAC;EACDd,IAAI,EAAE;IACJO,EAAE,EAAE,IAAI;IACRC,EAAE,EAAE,IAAI;IACRC,QAAQ,EAAE,YAAY;IACtBC,IAAI,EAAE,KAAK;IACXC,OAAO,EAAE,KAAK;IACdC,SAAS,EAAE,QAAQ;IACnBC,OAAO,EAAE,KAAK;IACdC,KAAK,EAAE;EACT;AACF,CAAC;AAED,MAAMC,yBAAyB,GAAG;EAChCjB,MAAM,EAAE;IACNS,EAAE,EAAE,IAAI;IACRC,EAAE,EAAE,IAAI;IACRC,QAAQ,EAAE,eAAe;IACzBC,IAAI,EAAE,KAAK;IACXC,OAAO,EAAE,KAAK;IACdC,SAAS,EAAE,KAAK;IAChBC,OAAO,EAAE,KAAK;IACdC,KAAK,EAAE;EACT,CAAC;EACDd,IAAI,EAAE;IACJO,EAAE,EAAE,IAAI;IACRC,EAAE,EAAE,IAAI;IACRC,QAAQ,EAAE,eAAe;IACzBC,IAAI,EAAE,OAAO;IACbC,OAAO,EAAE,UAAU;IACnBC,SAAS,EAAE,QAAQ;IACnBC,OAAO,EAAE,OAAO;IAChBC,KAAK,EAAE;EACT;AACF,CAAC;AAED,MAAME,QAAQ,GAAG;EACf,CAAC,EAAE,KAAK;EACR,CAAC,EAAE,KAAK;EACR,CAAC,EAAE,KAAK;EACR,CAAC,EAAE,KAAK;EACR,CAAC,EAAE,KAAK;EACR,CAAC,EAAE,KAAK;EACR,CAAC,EAAE,KAAK;EACR,CAAC,EAAE,KAAK;EACR,CAAC,EAAE,KAAK;EACR,CAAC,EAAE,KAAK;EACR,EAAE,EAAE,KAAK;EACT,EAAE,EAAE,KAAK;EACT,EAAE,EAAE,KAAK;EACT,EAAE,EAAE,KAAK;EACT,EAAE,EAAE,KAAK;EACT,EAAE,EAAE,KAAK;EACT,EAAE,EAAE,KAAK;EACT,EAAE,EAAE,KAAK;EACT,EAAE,EAAE,KAAK;EACT,GAAG,EAAE;AACP,CAAC;AAED,MAAMC,aAAa,GAAGA,CAACC,WAAW,EAAEC,QAAQ,KAAK;EAC/C,MAAMC,MAAM,GAAGC,MAAM,CAACH,WAAW,CAAC;EAClC,MAAMI,KAAK,GAAGF,MAAM,GAAG,EAAE;EACzB,MAAMG,CAAC,GAAGH,MAAM,IAAI,GAAG,GAAG,GAAG,GAAG,IAAI;EACpC,MAAMI,MAAM,GACVR,QAAQ,CAACI,MAAM,CAAC,IAAIJ,QAAQ,CAACM,KAAK,CAAC,IAAKC,CAAC,IAAIP,QAAQ,CAACO,CAAC,CAAE,IAAI,EAAE;EAEjE,OAAOH,MAAM,GAAGI,MAAM;AACxB,CAAC;AAED,OAAO,MAAMC,QAAQ,GAAG;EACtBR,aAAa;EAEbS,GAAG,EAAE9B,eAAe,CAAC;IACnB+B,MAAM,EAAE9B,SAAS;IACjB+B,YAAY,EAAE;EAChB,CAAC,CAAC;EAEFC,OAAO,EAAEjC,eAAe,CAAC;IACvB+B,MAAM,EAAE1B,aAAa;IACrB2B,YAAY,EAAE,MAAM;IACpBE,gBAAgB,EAAGD,OAAO,IAAKA,OAAO,GAAG;EAC3C,CAAC,CAAC;EAEFE,KAAK,EAAEnC,eAAe,CAAC;IACrB+B,MAAM,EAAEzB,WAAW;IACnB0B,YAAY,EAAE,MAAM;IACpBI,gBAAgB,EAAE7B,qBAAqB;IACvC8B,sBAAsB,EAAE;EAC1B,CAAC,CAAC;EAEFC,GAAG,EAAEtC,eAAe,CAAC;IACnB+B,MAAM,EAAEvB,SAAS;IACjBwB,YAAY,EAAE;EAChB,CAAC,CAAC;EAEFO,SAAS,EAAEvC,eAAe,CAAC;IACzB+B,MAAM,EAAErB,eAAe;IACvBsB,YAAY,EAAE,KAAK;IACnBI,gBAAgB,EAAEjB,yBAAyB;IAC3CkB,sBAAsB,EAAE;EAC1B,CAAC;AACH,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}