{"ast": null, "code": "import _extends from \"@babel/runtime/helpers/esm/extends\";\nimport * as React from 'react';\nimport { useSlotProps } from '@mui/base/utils';\nimport { unstable_composeClasses as composeClasses } from '@mui/utils';\nimport { PickersActionBar } from '../PickersActionBar';\nimport { getPickersLayoutUtilityClass } from './pickersLayoutClasses';\nimport { PickersShortcuts } from '../PickersShortcuts';\nimport { uncapitalizeObjectKeys } from '../internals/utils/slots-migration';\nimport { jsx as _jsx } from \"react/jsx-runtime\";\nfunction toolbarHasView(toolbarProps) {\n  return toolbarProps.view !== null;\n}\nconst useUtilityClasses = ownerState => {\n  const {\n    classes,\n    isLandscape\n  } = ownerState;\n  const slots = {\n    root: ['root', isLandscape && 'landscape'],\n    contentWrapper: ['contentWrapper'],\n    toolbar: ['toolbar'],\n    actionBar: ['actionBar'],\n    tabs: ['tabs'],\n    landscape: ['landscape'],\n    shortcuts: ['shortcuts']\n  };\n  return composeClasses(slots, getPickersLayoutUtilityClass, classes);\n};\nconst usePickerLayout = props => {\n  var _slots$actionBar, _slots$shortcuts;\n  const {\n    wrapperVariant,\n    onAccept,\n    onClear,\n    onCancel,\n    onSetToday,\n    view,\n    views,\n    onViewChange,\n    value,\n    onChange,\n    onSelectShortcut,\n    isValid,\n    isLandscape,\n    disabled,\n    readOnly,\n    children,\n    components,\n    componentsProps,\n    slots: innerSlots,\n    slotProps: innerSlotProps\n    // TODO: Remove this \"as\" hack. It get introduced to mark `value` prop in PickersLayoutProps as not required.\n    // The true type should be\n    // - For pickers value: TDate | null\n    // - For range pickers value: [TDate | null, TDate | null]\n  } = props;\n  const slots = innerSlots != null ? innerSlots : uncapitalizeObjectKeys(components);\n  const slotProps = innerSlotProps != null ? innerSlotProps : componentsProps;\n  const classes = useUtilityClasses(props);\n\n  // Action bar\n\n  const ActionBar = (_slots$actionBar = slots == null ? void 0 : slots.actionBar) != null ? _slots$actionBar : PickersActionBar;\n  const actionBarProps = useSlotProps({\n    elementType: ActionBar,\n    externalSlotProps: slotProps == null ? void 0 : slotProps.actionBar,\n    additionalProps: {\n      onAccept,\n      onClear,\n      onCancel,\n      onSetToday,\n      actions: wrapperVariant === 'desktop' ? [] : ['cancel', 'accept'],\n      className: classes.actionBar\n    },\n    ownerState: _extends({}, props, {\n      wrapperVariant\n    })\n  });\n  const actionBar = /*#__PURE__*/_jsx(ActionBar, _extends({}, actionBarProps));\n\n  // Toolbar\n\n  const Toolbar = slots == null ? void 0 : slots.toolbar;\n  const toolbarProps = useSlotProps({\n    elementType: Toolbar,\n    externalSlotProps: slotProps == null ? void 0 : slotProps.toolbar,\n    additionalProps: {\n      isLandscape,\n      onChange,\n      value,\n      view,\n      onViewChange,\n      views,\n      disabled,\n      readOnly,\n      className: classes.toolbar\n    },\n    ownerState: _extends({}, props, {\n      wrapperVariant\n    })\n  });\n  const toolbar = toolbarHasView(toolbarProps) && !!Toolbar ? /*#__PURE__*/_jsx(Toolbar, _extends({}, toolbarProps)) : null;\n\n  // Content\n\n  const content = children;\n\n  // Tabs\n\n  const Tabs = slots == null ? void 0 : slots.tabs;\n  const tabs = view && Tabs ? /*#__PURE__*/_jsx(Tabs, _extends({\n    view: view,\n    onViewChange: onViewChange,\n    className: classes.tabs\n  }, slotProps == null ? void 0 : slotProps.tabs)) : null;\n\n  // Shortcuts\n\n  const Shortcuts = (_slots$shortcuts = slots == null ? void 0 : slots.shortcuts) != null ? _slots$shortcuts : PickersShortcuts;\n  const shortcutsProps = useSlotProps({\n    elementType: Shortcuts,\n    externalSlotProps: slotProps == null ? void 0 : slotProps.shortcuts,\n    additionalProps: {\n      isValid,\n      isLandscape,\n      onChange: onSelectShortcut,\n      className: classes.shortcuts\n    },\n    ownerState: {\n      isValid,\n      isLandscape,\n      onChange: onSelectShortcut,\n      className: classes.shortcuts,\n      wrapperVariant\n    }\n  });\n  const shortcuts = view && !!Shortcuts ? /*#__PURE__*/_jsx(Shortcuts, _extends({}, shortcutsProps)) : null;\n  return {\n    toolbar,\n    content,\n    tabs,\n    actionBar,\n    shortcuts\n  };\n};\nexport default usePickerLayout;", "map": {"version": 3, "names": ["_extends", "React", "useSlotProps", "unstable_composeClasses", "composeClasses", "PickersActionBar", "getPickersLayoutUtilityClass", "PickersShortcuts", "uncapitalizeObjectKeys", "jsx", "_jsx", "toolbarHasView", "toolbarProps", "view", "useUtilityClasses", "ownerState", "classes", "isLandscape", "slots", "root", "contentWrapper", "toolbar", "actionBar", "tabs", "landscape", "shortcuts", "usePickerLayout", "props", "_slots$actionBar", "_slots$shortcuts", "wrapperVariant", "onAccept", "onClear", "onCancel", "onSetToday", "views", "onViewChange", "value", "onChange", "onSelectShortcut", "<PERSON><PERSON><PERSON><PERSON>", "disabled", "readOnly", "children", "components", "componentsProps", "innerSlots", "slotProps", "innerSlotProps", "ActionBar", "actionBarProps", "elementType", "externalSlotProps", "additionalProps", "actions", "className", "<PERSON><PERSON><PERSON>", "content", "Tabs", "Shortcuts", "shortcutsProps"], "sources": ["C:/CMS/webapp/frontend/node_modules/@mui/x-date-pickers/PickersLayout/usePickerLayout.js"], "sourcesContent": ["import _extends from \"@babel/runtime/helpers/esm/extends\";\nimport * as React from 'react';\nimport { useSlotProps } from '@mui/base/utils';\nimport { unstable_composeClasses as composeClasses } from '@mui/utils';\nimport { PickersActionBar } from '../PickersActionBar';\nimport { getPickersLayoutUtilityClass } from './pickersLayoutClasses';\nimport { PickersShortcuts } from '../PickersShortcuts';\nimport { uncapitalizeObjectKeys } from '../internals/utils/slots-migration';\nimport { jsx as _jsx } from \"react/jsx-runtime\";\nfunction toolbarHasView(toolbarProps) {\n  return toolbarProps.view !== null;\n}\nconst useUtilityClasses = ownerState => {\n  const {\n    classes,\n    isLandscape\n  } = ownerState;\n  const slots = {\n    root: ['root', isLandscape && 'landscape'],\n    contentWrapper: ['contentWrapper'],\n    toolbar: ['toolbar'],\n    actionBar: ['actionBar'],\n    tabs: ['tabs'],\n    landscape: ['landscape'],\n    shortcuts: ['shortcuts']\n  };\n  return composeClasses(slots, getPickersLayoutUtilityClass, classes);\n};\nconst usePickerLayout = props => {\n  var _slots$actionBar, _slots$shortcuts;\n  const {\n    wrapperVariant,\n    onAccept,\n    onClear,\n    onCancel,\n    onSetToday,\n    view,\n    views,\n    onViewChange,\n    value,\n    onChange,\n    onSelectShortcut,\n    isValid,\n    isLandscape,\n    disabled,\n    readOnly,\n    children,\n    components,\n    componentsProps,\n    slots: innerSlots,\n    slotProps: innerSlotProps\n    // TODO: Remove this \"as\" hack. It get introduced to mark `value` prop in PickersLayoutProps as not required.\n    // The true type should be\n    // - For pickers value: TDate | null\n    // - For range pickers value: [TDate | null, TDate | null]\n  } = props;\n  const slots = innerSlots != null ? innerSlots : uncapitalizeObjectKeys(components);\n  const slotProps = innerSlotProps != null ? innerSlotProps : componentsProps;\n  const classes = useUtilityClasses(props);\n\n  // Action bar\n\n  const ActionBar = (_slots$actionBar = slots == null ? void 0 : slots.actionBar) != null ? _slots$actionBar : PickersActionBar;\n  const actionBarProps = useSlotProps({\n    elementType: ActionBar,\n    externalSlotProps: slotProps == null ? void 0 : slotProps.actionBar,\n    additionalProps: {\n      onAccept,\n      onClear,\n      onCancel,\n      onSetToday,\n      actions: wrapperVariant === 'desktop' ? [] : ['cancel', 'accept'],\n      className: classes.actionBar\n    },\n    ownerState: _extends({}, props, {\n      wrapperVariant\n    })\n  });\n  const actionBar = /*#__PURE__*/_jsx(ActionBar, _extends({}, actionBarProps));\n\n  // Toolbar\n\n  const Toolbar = slots == null ? void 0 : slots.toolbar;\n  const toolbarProps = useSlotProps({\n    elementType: Toolbar,\n    externalSlotProps: slotProps == null ? void 0 : slotProps.toolbar,\n    additionalProps: {\n      isLandscape,\n      onChange,\n      value,\n      view,\n      onViewChange,\n      views,\n      disabled,\n      readOnly,\n      className: classes.toolbar\n    },\n    ownerState: _extends({}, props, {\n      wrapperVariant\n    })\n  });\n  const toolbar = toolbarHasView(toolbarProps) && !!Toolbar ? /*#__PURE__*/_jsx(Toolbar, _extends({}, toolbarProps)) : null;\n\n  // Content\n\n  const content = children;\n\n  // Tabs\n\n  const Tabs = slots == null ? void 0 : slots.tabs;\n  const tabs = view && Tabs ? /*#__PURE__*/_jsx(Tabs, _extends({\n    view: view,\n    onViewChange: onViewChange,\n    className: classes.tabs\n  }, slotProps == null ? void 0 : slotProps.tabs)) : null;\n\n  // Shortcuts\n\n  const Shortcuts = (_slots$shortcuts = slots == null ? void 0 : slots.shortcuts) != null ? _slots$shortcuts : PickersShortcuts;\n  const shortcutsProps = useSlotProps({\n    elementType: Shortcuts,\n    externalSlotProps: slotProps == null ? void 0 : slotProps.shortcuts,\n    additionalProps: {\n      isValid,\n      isLandscape,\n      onChange: onSelectShortcut,\n      className: classes.shortcuts\n    },\n    ownerState: {\n      isValid,\n      isLandscape,\n      onChange: onSelectShortcut,\n      className: classes.shortcuts,\n      wrapperVariant\n    }\n  });\n  const shortcuts = view && !!Shortcuts ? /*#__PURE__*/_jsx(Shortcuts, _extends({}, shortcutsProps)) : null;\n  return {\n    toolbar,\n    content,\n    tabs,\n    actionBar,\n    shortcuts\n  };\n};\nexport default usePickerLayout;"], "mappings": "AAAA,OAAOA,QAAQ,MAAM,oCAAoC;AACzD,OAAO,KAAKC,KAAK,MAAM,OAAO;AAC9B,SAASC,YAAY,QAAQ,iBAAiB;AAC9C,SAASC,uBAAuB,IAAIC,cAAc,QAAQ,YAAY;AACtE,SAASC,gBAAgB,QAAQ,qBAAqB;AACtD,SAASC,4BAA4B,QAAQ,wBAAwB;AACrE,SAASC,gBAAgB,QAAQ,qBAAqB;AACtD,SAASC,sBAAsB,QAAQ,oCAAoC;AAC3E,SAASC,GAAG,IAAIC,IAAI,QAAQ,mBAAmB;AAC/C,SAASC,cAAcA,CAACC,YAAY,EAAE;EACpC,OAAOA,YAAY,CAACC,IAAI,KAAK,IAAI;AACnC;AACA,MAAMC,iBAAiB,GAAGC,UAAU,IAAI;EACtC,MAAM;IACJC,OAAO;IACPC;EACF,CAAC,GAAGF,UAAU;EACd,MAAMG,KAAK,GAAG;IACZC,IAAI,EAAE,CAAC,MAAM,EAAEF,WAAW,IAAI,WAAW,CAAC;IAC1CG,cAAc,EAAE,CAAC,gBAAgB,CAAC;IAClCC,OAAO,EAAE,CAAC,SAAS,CAAC;IACpBC,SAAS,EAAE,CAAC,WAAW,CAAC;IACxBC,IAAI,EAAE,CAAC,MAAM,CAAC;IACdC,SAAS,EAAE,CAAC,WAAW,CAAC;IACxBC,SAAS,EAAE,CAAC,WAAW;EACzB,CAAC;EACD,OAAOrB,cAAc,CAACc,KAAK,EAAEZ,4BAA4B,EAAEU,OAAO,CAAC;AACrE,CAAC;AACD,MAAMU,eAAe,GAAGC,KAAK,IAAI;EAC/B,IAAIC,gBAAgB,EAAEC,gBAAgB;EACtC,MAAM;IACJC,cAAc;IACdC,QAAQ;IACRC,OAAO;IACPC,QAAQ;IACRC,UAAU;IACVrB,IAAI;IACJsB,KAAK;IACLC,YAAY;IACZC,KAAK;IACLC,QAAQ;IACRC,gBAAgB;IAChBC,OAAO;IACPvB,WAAW;IACXwB,QAAQ;IACRC,QAAQ;IACRC,QAAQ;IACRC,UAAU;IACVC,eAAe;IACf3B,KAAK,EAAE4B,UAAU;IACjBC,SAAS,EAAEC;IACX;IACA;IACA;IACA;EACF,CAAC,GAAGrB,KAAK;EACT,MAAMT,KAAK,GAAG4B,UAAU,IAAI,IAAI,GAAGA,UAAU,GAAGtC,sBAAsB,CAACoC,UAAU,CAAC;EAClF,MAAMG,SAAS,GAAGC,cAAc,IAAI,IAAI,GAAGA,cAAc,GAAGH,eAAe;EAC3E,MAAM7B,OAAO,GAAGF,iBAAiB,CAACa,KAAK,CAAC;;EAExC;;EAEA,MAAMsB,SAAS,GAAG,CAACrB,gBAAgB,GAAGV,KAAK,IAAI,IAAI,GAAG,KAAK,CAAC,GAAGA,KAAK,CAACI,SAAS,KAAK,IAAI,GAAGM,gBAAgB,GAAGvB,gBAAgB;EAC7H,MAAM6C,cAAc,GAAGhD,YAAY,CAAC;IAClCiD,WAAW,EAAEF,SAAS;IACtBG,iBAAiB,EAAEL,SAAS,IAAI,IAAI,GAAG,KAAK,CAAC,GAAGA,SAAS,CAACzB,SAAS;IACnE+B,eAAe,EAAE;MACftB,QAAQ;MACRC,OAAO;MACPC,QAAQ;MACRC,UAAU;MACVoB,OAAO,EAAExB,cAAc,KAAK,SAAS,GAAG,EAAE,GAAG,CAAC,QAAQ,EAAE,QAAQ,CAAC;MACjEyB,SAAS,EAAEvC,OAAO,CAACM;IACrB,CAAC;IACDP,UAAU,EAAEf,QAAQ,CAAC,CAAC,CAAC,EAAE2B,KAAK,EAAE;MAC9BG;IACF,CAAC;EACH,CAAC,CAAC;EACF,MAAMR,SAAS,GAAG,aAAaZ,IAAI,CAACuC,SAAS,EAAEjD,QAAQ,CAAC,CAAC,CAAC,EAAEkD,cAAc,CAAC,CAAC;;EAE5E;;EAEA,MAAMM,OAAO,GAAGtC,KAAK,IAAI,IAAI,GAAG,KAAK,CAAC,GAAGA,KAAK,CAACG,OAAO;EACtD,MAAMT,YAAY,GAAGV,YAAY,CAAC;IAChCiD,WAAW,EAAEK,OAAO;IACpBJ,iBAAiB,EAAEL,SAAS,IAAI,IAAI,GAAG,KAAK,CAAC,GAAGA,SAAS,CAAC1B,OAAO;IACjEgC,eAAe,EAAE;MACfpC,WAAW;MACXqB,QAAQ;MACRD,KAAK;MACLxB,IAAI;MACJuB,YAAY;MACZD,KAAK;MACLM,QAAQ;MACRC,QAAQ;MACRa,SAAS,EAAEvC,OAAO,CAACK;IACrB,CAAC;IACDN,UAAU,EAAEf,QAAQ,CAAC,CAAC,CAAC,EAAE2B,KAAK,EAAE;MAC9BG;IACF,CAAC;EACH,CAAC,CAAC;EACF,MAAMT,OAAO,GAAGV,cAAc,CAACC,YAAY,CAAC,IAAI,CAAC,CAAC4C,OAAO,GAAG,aAAa9C,IAAI,CAAC8C,OAAO,EAAExD,QAAQ,CAAC,CAAC,CAAC,EAAEY,YAAY,CAAC,CAAC,GAAG,IAAI;;EAEzH;;EAEA,MAAM6C,OAAO,GAAGd,QAAQ;;EAExB;;EAEA,MAAMe,IAAI,GAAGxC,KAAK,IAAI,IAAI,GAAG,KAAK,CAAC,GAAGA,KAAK,CAACK,IAAI;EAChD,MAAMA,IAAI,GAAGV,IAAI,IAAI6C,IAAI,GAAG,aAAahD,IAAI,CAACgD,IAAI,EAAE1D,QAAQ,CAAC;IAC3Da,IAAI,EAAEA,IAAI;IACVuB,YAAY,EAAEA,YAAY;IAC1BmB,SAAS,EAAEvC,OAAO,CAACO;EACrB,CAAC,EAAEwB,SAAS,IAAI,IAAI,GAAG,KAAK,CAAC,GAAGA,SAAS,CAACxB,IAAI,CAAC,CAAC,GAAG,IAAI;;EAEvD;;EAEA,MAAMoC,SAAS,GAAG,CAAC9B,gBAAgB,GAAGX,KAAK,IAAI,IAAI,GAAG,KAAK,CAAC,GAAGA,KAAK,CAACO,SAAS,KAAK,IAAI,GAAGI,gBAAgB,GAAGtB,gBAAgB;EAC7H,MAAMqD,cAAc,GAAG1D,YAAY,CAAC;IAClCiD,WAAW,EAAEQ,SAAS;IACtBP,iBAAiB,EAAEL,SAAS,IAAI,IAAI,GAAG,KAAK,CAAC,GAAGA,SAAS,CAACtB,SAAS;IACnE4B,eAAe,EAAE;MACfb,OAAO;MACPvB,WAAW;MACXqB,QAAQ,EAAEC,gBAAgB;MAC1BgB,SAAS,EAAEvC,OAAO,CAACS;IACrB,CAAC;IACDV,UAAU,EAAE;MACVyB,OAAO;MACPvB,WAAW;MACXqB,QAAQ,EAAEC,gBAAgB;MAC1BgB,SAAS,EAAEvC,OAAO,CAACS,SAAS;MAC5BK;IACF;EACF,CAAC,CAAC;EACF,MAAML,SAAS,GAAGZ,IAAI,IAAI,CAAC,CAAC8C,SAAS,GAAG,aAAajD,IAAI,CAACiD,SAAS,EAAE3D,QAAQ,CAAC,CAAC,CAAC,EAAE4D,cAAc,CAAC,CAAC,GAAG,IAAI;EACzG,OAAO;IACLvC,OAAO;IACPoC,OAAO;IACPlC,IAAI;IACJD,SAAS;IACTG;EACF,CAAC;AACH,CAAC;AACD,eAAeC,eAAe", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}