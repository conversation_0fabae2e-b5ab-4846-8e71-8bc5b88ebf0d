{"ast": null, "code": "import { Parser } from \"../Parser.js\";\nimport { mapValue, parseNDigits } from \"../utils.js\";\nexport class FractionOfSecondParser extends Parser {\n  priority = 30;\n  parse(dateString, token) {\n    const valueCallback = value => Math.trunc(value * Math.pow(10, -token.length + 3));\n    return mapValue(parseNDigits(token.length, dateString), valueCallback);\n  }\n  set(date, _flags, value) {\n    date.setMilliseconds(value);\n    return date;\n  }\n  incompatibleTokens = [\"t\", \"T\"];\n}", "map": {"version": 3, "names": ["<PERSON><PERSON><PERSON>", "mapValue", "parseNDigits", "FractionOfSecondParser", "priority", "parse", "dateString", "token", "valueCallback", "value", "Math", "trunc", "pow", "length", "set", "date", "_flags", "setMilliseconds", "incompatibleTokens"], "sources": ["C:/Users/<USER>/PycharmProjects/CMS/webapp/frontend/node_modules/date-fns/parse/_lib/parsers/FractionOfSecondParser.js"], "sourcesContent": ["import { Parser } from \"../Parser.js\";\n\nimport { mapValue, parseNDigits } from \"../utils.js\";\n\nexport class FractionOfSecondParser extends Parser {\n  priority = 30;\n\n  parse(dateString, token) {\n    const valueCallback = (value) =>\n      Math.trunc(value * Math.pow(10, -token.length + 3));\n    return mapValue(parseNDigits(token.length, dateString), valueCallback);\n  }\n\n  set(date, _flags, value) {\n    date.setMilliseconds(value);\n    return date;\n  }\n\n  incompatibleTokens = [\"t\", \"T\"];\n}\n"], "mappings": "AAAA,SAASA,MAAM,QAAQ,cAAc;AAErC,SAASC,QAAQ,EAAEC,YAAY,QAAQ,aAAa;AAEpD,OAAO,MAAMC,sBAAsB,SAASH,MAAM,CAAC;EACjDI,QAAQ,GAAG,EAAE;EAEbC,KAAKA,CAACC,UAAU,EAAEC,KAAK,EAAE;IACvB,MAAMC,aAAa,GAAIC,KAAK,IAC1BC,IAAI,CAACC,KAAK,CAACF,KAAK,GAAGC,IAAI,CAACE,GAAG,CAAC,EAAE,EAAE,CAACL,KAAK,CAACM,MAAM,GAAG,CAAC,CAAC,CAAC;IACrD,OAAOZ,QAAQ,CAACC,YAAY,CAACK,KAAK,CAACM,MAAM,EAAEP,UAAU,CAAC,EAAEE,aAAa,CAAC;EACxE;EAEAM,GAAGA,CAACC,IAAI,EAAEC,MAAM,EAAEP,KAAK,EAAE;IACvBM,IAAI,CAACE,eAAe,CAACR,KAAK,CAAC;IAC3B,OAAOM,IAAI;EACb;EAEAG,kBAAkB,GAAG,CAAC,GAAG,EAAE,GAAG,CAAC;AACjC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}