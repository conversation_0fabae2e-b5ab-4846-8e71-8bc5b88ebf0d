{"ast": null, "code": "// Same as fr\nimport { formatDistance } from \"./fr/_lib/formatDistance.js\";\nimport { formatRelative } from \"./fr/_lib/formatRelative.js\";\nimport { localize } from \"./fr/_lib/localize.js\";\nimport { match } from \"./fr/_lib/match.js\";\n\n// Unique for fr-CA\nimport { formatLong } from \"./fr-CA/_lib/formatLong.js\";\n\n/**\n * @category Locales\n * @summary French locale (Canada).\n * @language French\n * @iso-639-2 fra\n * <AUTHOR> [@izeau](https://github.com/izeau)\n * <AUTHOR> [@fbonzon](https://github.com/fbonzon)\n * <AUTHOR> [@gpetrioli](https://github.com/gpetrioli)\n */\nexport const frCA = {\n  code: \"fr-CA\",\n  formatDistance: formatDistance,\n  formatLong: formatLong,\n  formatRelative: formatRelative,\n  localize: localize,\n  match: match,\n  // Unique for fr-CA\n  options: {\n    weekStartsOn: 0 /* Sunday */,\n    firstWeekContainsDate: 1\n  }\n};\n\n// Fallback for modularized imports:\nexport default frCA;", "map": {"version": 3, "names": ["formatDistance", "formatRelative", "localize", "match", "formatLong", "frCA", "code", "options", "weekStartsOn", "firstWeekContainsDate"], "sources": ["C:/Users/<USER>/PycharmProjects/CMS/webapp/frontend/node_modules/date-fns/locale/fr-CA.js"], "sourcesContent": ["// Same as fr\nimport { formatDistance } from \"./fr/_lib/formatDistance.js\";\nimport { formatRelative } from \"./fr/_lib/formatRelative.js\";\nimport { localize } from \"./fr/_lib/localize.js\";\nimport { match } from \"./fr/_lib/match.js\";\n\n// Unique for fr-CA\nimport { formatLong } from \"./fr-CA/_lib/formatLong.js\";\n\n/**\n * @category Locales\n * @summary French locale (Canada).\n * @language French\n * @iso-639-2 fra\n * <AUTHOR> [@izeau](https://github.com/izeau)\n * <AUTHOR> [@fbonzon](https://github.com/fbonzon)\n * <AUTHOR> [@gpetrioli](https://github.com/gpetrioli)\n */\nexport const frCA = {\n  code: \"fr-CA\",\n  formatDistance: formatDistance,\n  formatLong: formatLong,\n  formatRelative: formatRelative,\n  localize: localize,\n  match: match,\n\n  // Unique for fr-CA\n  options: {\n    weekStartsOn: 0 /* Sunday */,\n    firstWeekContainsDate: 1,\n  },\n};\n\n// Fallback for modularized imports:\nexport default frCA;\n"], "mappings": "AAAA;AACA,SAASA,cAAc,QAAQ,6BAA6B;AAC5D,SAASC,cAAc,QAAQ,6BAA6B;AAC5D,SAASC,QAAQ,QAAQ,uBAAuB;AAChD,SAASC,KAAK,QAAQ,oBAAoB;;AAE1C;AACA,SAASC,UAAU,QAAQ,4BAA4B;;AAEvD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO,MAAMC,IAAI,GAAG;EAClBC,IAAI,EAAE,OAAO;EACbN,cAAc,EAAEA,cAAc;EAC9BI,UAAU,EAAEA,UAAU;EACtBH,cAAc,EAAEA,cAAc;EAC9BC,QAAQ,EAAEA,QAAQ;EAClBC,KAAK,EAAEA,KAAK;EAEZ;EACAI,OAAO,EAAE;IACPC,YAAY,EAAE,CAAC,CAAC;IAChBC,qBAAqB,EAAE;EACzB;AACF,CAAC;;AAED;AACA,eAAeJ,IAAI", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}