{"ast": null, "code": "// `victory-vendor/d3-scale` (ESM)\n// See upstream license: https://github.com/d3/d3-scale/blob/main/LICENSE\n//\n// Our ESM package uses the underlying installed dependencies of `node_modules/d3-scale`\nexport * from \"d3-scale\";", "map": {"version": 3, "names": [], "sources": ["C:/Users/<USER>/PycharmProjects/CMS/webapp/frontend/node_modules/victory-vendor/es/d3-scale.js"], "sourcesContent": ["\n// `victory-vendor/d3-scale` (ESM)\n// See upstream license: https://github.com/d3/d3-scale/blob/main/LICENSE\n//\n// Our ESM package uses the underlying installed dependencies of `node_modules/d3-scale`\nexport * from \"d3-scale\";\n"], "mappings": "AACA;AACA;AACA;AACA;AACA,cAAc,UAAU", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}