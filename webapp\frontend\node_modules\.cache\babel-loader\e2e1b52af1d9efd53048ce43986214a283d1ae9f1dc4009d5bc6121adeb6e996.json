{"ast": null, "code": "import { formatDistance } from \"./nl/_lib/formatDistance.js\";\nimport { formatLong } from \"./nl/_lib/formatLong.js\";\nimport { formatRelative } from \"./nl/_lib/formatRelative.js\";\nimport { localize } from \"./nl/_lib/localize.js\";\nimport { match } from \"./nl/_lib/match.js\";\n\n/**\n * @category Locales\n * @summary Dutch locale.\n * @language Dutch\n * @iso-639-2 nld\n * <AUTHOR> [@jtangelder](https://github.com/jtangelder)\n * <AUTHOR> [@rubenstolk](https://github.com/rubenstolk)\n * <AUTHOR> [@bitcrumb](https://github.com/bitcrumb)\n * <AUTHOR> [@edorivai](https://github.com/edorivai)\n * <AUTHOR> [@curry684](https://github.com/curry684)\n * <AUTHOR> [@stefanvermaas](https://github.com/stefanvermaas)\n */\nexport const nl = {\n  code: \"nl\",\n  formatDistance: formatDistance,\n  formatLong: formatLong,\n  formatRelative: formatRelative,\n  localize: localize,\n  match: match,\n  options: {\n    weekStartsOn: 1 /* Monday */,\n    firstWeekContainsDate: 4\n  }\n};\n\n// Fallback for modularized imports:\nexport default nl;", "map": {"version": 3, "names": ["formatDistance", "formatLong", "formatRelative", "localize", "match", "nl", "code", "options", "weekStartsOn", "firstWeekContainsDate"], "sources": ["C:/Users/<USER>/PycharmProjects/CMS/webapp/frontend/node_modules/date-fns/locale/nl.js"], "sourcesContent": ["import { formatDistance } from \"./nl/_lib/formatDistance.js\";\nimport { formatLong } from \"./nl/_lib/formatLong.js\";\nimport { formatRelative } from \"./nl/_lib/formatRelative.js\";\nimport { localize } from \"./nl/_lib/localize.js\";\nimport { match } from \"./nl/_lib/match.js\";\n\n/**\n * @category Locales\n * @summary Dutch locale.\n * @language Dutch\n * @iso-639-2 nld\n * <AUTHOR> [@jtangelder](https://github.com/jtangelder)\n * <AUTHOR> [@rubenstolk](https://github.com/rubenstolk)\n * <AUTHOR> [@bitcrumb](https://github.com/bitcrumb)\n * <AUTHOR> [@edorivai](https://github.com/edorivai)\n * <AUTHOR> [@curry684](https://github.com/curry684)\n * <AUTHOR> [@stefanvermaas](https://github.com/stefanvermaas)\n */\nexport const nl = {\n  code: \"nl\",\n  formatDistance: formatDistance,\n  formatLong: formatLong,\n  formatRelative: formatRelative,\n  localize: localize,\n  match: match,\n  options: {\n    weekStartsOn: 1 /* Monday */,\n    firstWeekContainsDate: 4,\n  },\n};\n\n// Fallback for modularized imports:\nexport default nl;\n"], "mappings": "AAAA,SAASA,cAAc,QAAQ,6BAA6B;AAC5D,SAASC,UAAU,QAAQ,yBAAyB;AACpD,SAASC,cAAc,QAAQ,6BAA6B;AAC5D,SAASC,QAAQ,QAAQ,uBAAuB;AAChD,SAASC,KAAK,QAAQ,oBAAoB;;AAE1C;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO,MAAMC,EAAE,GAAG;EAChBC,IAAI,EAAE,IAAI;EACVN,cAAc,EAAEA,cAAc;EAC9BC,UAAU,EAAEA,UAAU;EACtBC,cAAc,EAAEA,cAAc;EAC9BC,QAAQ,EAAEA,QAAQ;EAClBC,KAAK,EAAEA,KAAK;EACZG,OAAO,EAAE;IACPC,YAAY,EAAE,CAAC,CAAC;IAChBC,qBAAqB,EAAE;EACzB;AACF,CAAC;;AAED;AACA,eAAeJ,EAAE", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}