{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\PycharmProjects\\\\CMS\\\\webapp\\\\frontend\\\\src\\\\pages\\\\cavi\\\\parco\\\\CreaBobinaPage.js\",\n  _s = $RefreshSig$();\nimport React from 'react';\nimport { Box, Typography, IconButton, Alert, Snackbar } from '@mui/material';\nimport { ArrowBack as ArrowBackIcon, Refresh as RefreshIcon } from '@mui/icons-material';\nimport { useNavigate } from 'react-router-dom';\nimport { useAuth } from '../../../context/AuthContext';\nimport AdminHomeButton from '../../../components/common/AdminHomeButton';\nimport ParcoCavi from '../../../components/cavi/ParcoCavi';\nimport { useState } from 'react';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst CreaBobinaPage = () => {\n  _s();\n  const navigate = useNavigate();\n  const {\n    selectedCantiere\n  } = useAuth();\n  const [successMessage, setSuccessMessage] = useState('');\n  const [errorMessage, setErrorMessage] = useState('');\n  const [openSuccess, setOpenSuccess] = useState(false);\n  const [openError, setOpenError] = useState(false);\n\n  // Recupera l'ID del cantiere dal localStorage se non è disponibile nel contesto\n  const cantiereId = selectedCantiere ? selectedCantiere.id_cantiere : parseInt(localStorage.getItem('selectedCantiereId'), 10);\n\n  // Verifica se un cantiere è selezionato\n  if (!cantiereId || isNaN(cantiereId)) {\n    return /*#__PURE__*/_jsxDEV(Box, {\n      sx: {\n        p: 3\n      },\n      children: /*#__PURE__*/_jsxDEV(Alert, {\n        severity: \"warning\",\n        children: \"Nessun cantiere selezionato. Seleziona un cantiere per gestire il parco cavi.\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 34,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 33,\n      columnNumber: 7\n    }, this);\n  }\n\n  // Gestisce il ritorno alla pagina dei cavi\n  const handleBackToCantieri = () => {\n    navigate('/dashboard/cavi');\n  };\n\n  // Gestisce i messaggi di successo\n  const handleSuccess = message => {\n    setSuccessMessage(message);\n    setOpenSuccess(true);\n  };\n\n  // Gestisce i messaggi di errore\n  const handleError = message => {\n    setErrorMessage(message);\n    setOpenError(true);\n  };\n\n  // Chiude i messaggi\n  const handleCloseSuccess = () => {\n    setOpenSuccess(false);\n  };\n  const handleCloseError = () => {\n    setOpenError(false);\n  };\n  return /*#__PURE__*/_jsxDEV(Box, {\n    children: [/*#__PURE__*/_jsxDEV(Box, {\n      sx: {\n        mb: 3,\n        display: 'flex',\n        alignItems: 'center',\n        justifyContent: 'space-between'\n      },\n      children: [/*#__PURE__*/_jsxDEV(Box, {\n        sx: {\n          display: 'flex',\n          alignItems: 'center'\n        },\n        children: [/*#__PURE__*/_jsxDEV(IconButton, {\n          onClick: handleBackToCantieri,\n          sx: {\n            mr: 1\n          },\n          children: /*#__PURE__*/_jsxDEV(ArrowBackIcon, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 72,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 71,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Typography, {\n          variant: \"h4\",\n          children: \"Crea Nuova Bobina\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 74,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(IconButton, {\n          onClick: () => window.location.reload(),\n          sx: {\n            ml: 2\n          },\n          color: \"primary\",\n          title: \"Ricarica la pagina\",\n          children: /*#__PURE__*/_jsxDEV(RefreshIcon, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 83,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 77,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 70,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(AdminHomeButton, {}, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 86,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 69,\n      columnNumber: 7\n    }, this), cantiereId && /*#__PURE__*/_jsxDEV(ParcoCavi, {\n      cantiereId: cantiereId,\n      onSuccess: handleSuccess,\n      onError: handleError,\n      initialOption: \"creaBobina\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 90,\n      columnNumber: 9\n    }, this), /*#__PURE__*/_jsxDEV(Snackbar, {\n      open: openSuccess,\n      autoHideDuration: 6000,\n      onClose: handleCloseSuccess,\n      anchorOrigin: {\n        vertical: 'bottom',\n        horizontal: 'center'\n      },\n      children: /*#__PURE__*/_jsxDEV(Alert, {\n        onClose: handleCloseSuccess,\n        severity: \"success\",\n        sx: {\n          width: '100%'\n        },\n        children: successMessage\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 105,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 99,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Snackbar, {\n      open: openError,\n      autoHideDuration: 6000,\n      onClose: handleCloseError,\n      anchorOrigin: {\n        vertical: 'bottom',\n        horizontal: 'center'\n      },\n      children: /*#__PURE__*/_jsxDEV(Alert, {\n        onClose: handleCloseError,\n        severity: \"error\",\n        sx: {\n          width: '100%'\n        },\n        children: errorMessage\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 117,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 111,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 68,\n    columnNumber: 5\n  }, this);\n};\n_s(CreaBobinaPage, \"alPhJDWny/l2XY6014qd2C0Vy3U=\", false, function () {\n  return [useNavigate, useAuth];\n});\n_c = CreaBobinaPage;\nexport default CreaBobinaPage;\nvar _c;\n$RefreshReg$(_c, \"CreaBobinaPage\");", "map": {"version": 3, "names": ["React", "Box", "Typography", "IconButton", "<PERSON><PERSON>", "Snackbar", "ArrowBack", "ArrowBackIcon", "Refresh", "RefreshIcon", "useNavigate", "useAuth", "AdminHomeButton", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "useState", "jsxDEV", "_jsxDEV", "CreaBobinaPage", "_s", "navigate", "selected<PERSON><PERSON><PERSON>", "successMessage", "setSuccessMessage", "errorMessage", "setErrorMessage", "openSuccess", "setOpenSuccess", "openError", "set<PERSON>pen<PERSON>rror", "cantiereId", "id_cantiere", "parseInt", "localStorage", "getItem", "isNaN", "sx", "p", "children", "severity", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "handleBackToCantieri", "handleSuccess", "message", "handleError", "handleCloseSuccess", "handleCloseError", "mb", "display", "alignItems", "justifyContent", "onClick", "mr", "variant", "window", "location", "reload", "ml", "color", "title", "onSuccess", "onError", "initialOption", "open", "autoHideDuration", "onClose", "anchor<PERSON><PERSON><PERSON>", "vertical", "horizontal", "width", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/PycharmProjects/CMS/webapp/frontend/src/pages/cavi/parco/CreaBobinaPage.js"], "sourcesContent": ["import React from 'react';\nimport {\n  Box,\n  Typography,\n  IconButton,\n  Alert,\n  Snackbar\n} from '@mui/material';\nimport {\n  ArrowBack as ArrowBackIcon,\n  Refresh as RefreshIcon\n} from '@mui/icons-material';\nimport { useNavigate } from 'react-router-dom';\nimport { useAuth } from '../../../context/AuthContext';\nimport AdminHomeButton from '../../../components/common/AdminHomeButton';\nimport ParcoCavi from '../../../components/cavi/ParcoCavi';\nimport { useState } from 'react';\n\nconst CreaBobinaPage = () => {\n  const navigate = useNavigate();\n  const { selectedCantiere } = useAuth();\n  const [successMessage, setSuccessMessage] = useState('');\n  const [errorMessage, setErrorMessage] = useState('');\n  const [openSuccess, setOpenSuccess] = useState(false);\n  const [openError, setOpenError] = useState(false);\n\n  // Recupera l'ID del cantiere dal localStorage se non è disponibile nel contesto\n  const cantiereId = selectedCantiere ? selectedCantiere.id_cantiere : parseInt(localStorage.getItem('selectedCantiereId'), 10);\n\n  // Verifica se un cantiere è selezionato\n  if (!cantiereId || isNaN(cantiereId)) {\n    return (\n      <Box sx={{ p: 3 }}>\n        <Alert severity=\"warning\">\n          Nessun cantiere selezionato. Seleziona un cantiere per gestire il parco cavi.\n        </Alert>\n      </Box>\n    );\n  }\n\n  // Gestisce il ritorno alla pagina dei cavi\n  const handleBackToCantieri = () => {\n    navigate('/dashboard/cavi');\n  };\n\n  // Gestisce i messaggi di successo\n  const handleSuccess = (message) => {\n    setSuccessMessage(message);\n    setOpenSuccess(true);\n  };\n\n  // Gestisce i messaggi di errore\n  const handleError = (message) => {\n    setErrorMessage(message);\n    setOpenError(true);\n  };\n\n  // Chiude i messaggi\n  const handleCloseSuccess = () => {\n    setOpenSuccess(false);\n  };\n\n  const handleCloseError = () => {\n    setOpenError(false);\n  };\n\n  return (\n    <Box>\n      <Box sx={{ mb: 3, display: 'flex', alignItems: 'center', justifyContent: 'space-between' }}>\n        <Box sx={{ display: 'flex', alignItems: 'center' }}>\n          <IconButton onClick={handleBackToCantieri} sx={{ mr: 1 }}>\n            <ArrowBackIcon />\n          </IconButton>\n          <Typography variant=\"h4\">\n            Crea Nuova Bobina\n          </Typography>\n          <IconButton\n            onClick={() => window.location.reload()}\n            sx={{ ml: 2 }}\n            color=\"primary\"\n            title=\"Ricarica la pagina\"\n          >\n            <RefreshIcon />\n          </IconButton>\n        </Box>\n        <AdminHomeButton />\n      </Box>\n\n      {cantiereId && (\n        <ParcoCavi\n          cantiereId={cantiereId}\n          onSuccess={handleSuccess}\n          onError={handleError}\n          initialOption=\"creaBobina\"\n        />\n      )}\n\n      {/* Snackbar per i messaggi di successo */}\n      <Snackbar\n        open={openSuccess}\n        autoHideDuration={6000}\n        onClose={handleCloseSuccess}\n        anchorOrigin={{ vertical: 'bottom', horizontal: 'center' }}\n      >\n        <Alert onClose={handleCloseSuccess} severity=\"success\" sx={{ width: '100%' }}>\n          {successMessage}\n        </Alert>\n      </Snackbar>\n\n      {/* Snackbar per i messaggi di errore */}\n      <Snackbar\n        open={openError}\n        autoHideDuration={6000}\n        onClose={handleCloseError}\n        anchorOrigin={{ vertical: 'bottom', horizontal: 'center' }}\n      >\n        <Alert onClose={handleCloseError} severity=\"error\" sx={{ width: '100%' }}>\n          {errorMessage}\n        </Alert>\n      </Snackbar>\n    </Box>\n  );\n};\n\nexport default CreaBobinaPage;\n"], "mappings": ";;AAAA,OAAOA,KAAK,MAAM,OAAO;AACzB,SACEC,GAAG,EACHC,UAAU,EACVC,UAAU,EACVC,KAAK,EACLC,QAAQ,QACH,eAAe;AACtB,SACEC,SAAS,IAAIC,aAAa,EAC1BC,OAAO,IAAIC,WAAW,QACjB,qBAAqB;AAC5B,SAASC,WAAW,QAAQ,kBAAkB;AAC9C,SAASC,OAAO,QAAQ,8BAA8B;AACtD,OAAOC,eAAe,MAAM,4CAA4C;AACxE,OAAOC,SAAS,MAAM,oCAAoC;AAC1D,SAASC,QAAQ,QAAQ,OAAO;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAEjC,MAAMC,cAAc,GAAGA,CAAA,KAAM;EAAAC,EAAA;EAC3B,MAAMC,QAAQ,GAAGT,WAAW,CAAC,CAAC;EAC9B,MAAM;IAAEU;EAAiB,CAAC,GAAGT,OAAO,CAAC,CAAC;EACtC,MAAM,CAACU,cAAc,EAAEC,iBAAiB,CAAC,GAAGR,QAAQ,CAAC,EAAE,CAAC;EACxD,MAAM,CAACS,YAAY,EAAEC,eAAe,CAAC,GAAGV,QAAQ,CAAC,EAAE,CAAC;EACpD,MAAM,CAACW,WAAW,EAAEC,cAAc,CAAC,GAAGZ,QAAQ,CAAC,KAAK,CAAC;EACrD,MAAM,CAACa,SAAS,EAAEC,YAAY,CAAC,GAAGd,QAAQ,CAAC,KAAK,CAAC;;EAEjD;EACA,MAAMe,UAAU,GAAGT,gBAAgB,GAAGA,gBAAgB,CAACU,WAAW,GAAGC,QAAQ,CAACC,YAAY,CAACC,OAAO,CAAC,oBAAoB,CAAC,EAAE,EAAE,CAAC;;EAE7H;EACA,IAAI,CAACJ,UAAU,IAAIK,KAAK,CAACL,UAAU,CAAC,EAAE;IACpC,oBACEb,OAAA,CAACf,GAAG;MAACkC,EAAE,EAAE;QAAEC,CAAC,EAAE;MAAE,CAAE;MAAAC,QAAA,eAChBrB,OAAA,CAACZ,KAAK;QAACkC,QAAQ,EAAC,SAAS;QAAAD,QAAA,EAAC;MAE1B;QAAAE,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAO;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACL,CAAC;EAEV;;EAEA;EACA,MAAMC,oBAAoB,GAAGA,CAAA,KAAM;IACjCxB,QAAQ,CAAC,iBAAiB,CAAC;EAC7B,CAAC;;EAED;EACA,MAAMyB,aAAa,GAAIC,OAAO,IAAK;IACjCvB,iBAAiB,CAACuB,OAAO,CAAC;IAC1BnB,cAAc,CAAC,IAAI,CAAC;EACtB,CAAC;;EAED;EACA,MAAMoB,WAAW,GAAID,OAAO,IAAK;IAC/BrB,eAAe,CAACqB,OAAO,CAAC;IACxBjB,YAAY,CAAC,IAAI,CAAC;EACpB,CAAC;;EAED;EACA,MAAMmB,kBAAkB,GAAGA,CAAA,KAAM;IAC/BrB,cAAc,CAAC,KAAK,CAAC;EACvB,CAAC;EAED,MAAMsB,gBAAgB,GAAGA,CAAA,KAAM;IAC7BpB,YAAY,CAAC,KAAK,CAAC;EACrB,CAAC;EAED,oBACEZ,OAAA,CAACf,GAAG;IAAAoC,QAAA,gBACFrB,OAAA,CAACf,GAAG;MAACkC,EAAE,EAAE;QAAEc,EAAE,EAAE,CAAC;QAAEC,OAAO,EAAE,MAAM;QAAEC,UAAU,EAAE,QAAQ;QAAEC,cAAc,EAAE;MAAgB,CAAE;MAAAf,QAAA,gBACzFrB,OAAA,CAACf,GAAG;QAACkC,EAAE,EAAE;UAAEe,OAAO,EAAE,MAAM;UAAEC,UAAU,EAAE;QAAS,CAAE;QAAAd,QAAA,gBACjDrB,OAAA,CAACb,UAAU;UAACkD,OAAO,EAAEV,oBAAqB;UAACR,EAAE,EAAE;YAAEmB,EAAE,EAAE;UAAE,CAAE;UAAAjB,QAAA,eACvDrB,OAAA,CAACT,aAAa;YAAAgC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACP,CAAC,eACb1B,OAAA,CAACd,UAAU;UAACqD,OAAO,EAAC,IAAI;UAAAlB,QAAA,EAAC;QAEzB;UAAAE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAY,CAAC,eACb1B,OAAA,CAACb,UAAU;UACTkD,OAAO,EAAEA,CAAA,KAAMG,MAAM,CAACC,QAAQ,CAACC,MAAM,CAAC,CAAE;UACxCvB,EAAE,EAAE;YAAEwB,EAAE,EAAE;UAAE,CAAE;UACdC,KAAK,EAAC,SAAS;UACfC,KAAK,EAAC,oBAAoB;UAAAxB,QAAA,eAE1BrB,OAAA,CAACP,WAAW;YAAA8B,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACL,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACV,CAAC,eACN1B,OAAA,CAACJ,eAAe;QAAA2B,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAChB,CAAC,EAELb,UAAU,iBACTb,OAAA,CAACH,SAAS;MACRgB,UAAU,EAAEA,UAAW;MACvBiC,SAAS,EAAElB,aAAc;MACzBmB,OAAO,EAAEjB,WAAY;MACrBkB,aAAa,EAAC;IAAY;MAAAzB,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAC3B,CACF,eAGD1B,OAAA,CAACX,QAAQ;MACP4D,IAAI,EAAExC,WAAY;MAClByC,gBAAgB,EAAE,IAAK;MACvBC,OAAO,EAAEpB,kBAAmB;MAC5BqB,YAAY,EAAE;QAAEC,QAAQ,EAAE,QAAQ;QAAEC,UAAU,EAAE;MAAS,CAAE;MAAAjC,QAAA,eAE3DrB,OAAA,CAACZ,KAAK;QAAC+D,OAAO,EAAEpB,kBAAmB;QAACT,QAAQ,EAAC,SAAS;QAACH,EAAE,EAAE;UAAEoC,KAAK,EAAE;QAAO,CAAE;QAAAlC,QAAA,EAC1EhB;MAAc;QAAAkB,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACV;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACA,CAAC,eAGX1B,OAAA,CAACX,QAAQ;MACP4D,IAAI,EAAEtC,SAAU;MAChBuC,gBAAgB,EAAE,IAAK;MACvBC,OAAO,EAAEnB,gBAAiB;MAC1BoB,YAAY,EAAE;QAAEC,QAAQ,EAAE,QAAQ;QAAEC,UAAU,EAAE;MAAS,CAAE;MAAAjC,QAAA,eAE3DrB,OAAA,CAACZ,KAAK;QAAC+D,OAAO,EAAEnB,gBAAiB;QAACV,QAAQ,EAAC,OAAO;QAACH,EAAE,EAAE;UAAEoC,KAAK,EAAE;QAAO,CAAE;QAAAlC,QAAA,EACtEd;MAAY;QAAAgB,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACR;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACA,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACR,CAAC;AAEV,CAAC;AAACxB,EAAA,CAxGID,cAAc;EAAA,QACDP,WAAW,EACCC,OAAO;AAAA;AAAA6D,EAAA,GAFhCvD,cAAc;AA0GpB,eAAeA,cAAc;AAAC,IAAAuD,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}