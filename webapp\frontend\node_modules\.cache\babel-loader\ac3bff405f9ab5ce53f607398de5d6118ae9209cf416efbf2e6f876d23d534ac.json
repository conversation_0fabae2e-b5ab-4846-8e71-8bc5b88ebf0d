{"ast": null, "code": "import { formatDistance } from \"./bg/_lib/formatDistance.js\";\nimport { formatLong } from \"./bg/_lib/formatLong.js\";\nimport { formatRelative } from \"./bg/_lib/formatRelative.js\";\nimport { localize } from \"./bg/_lib/localize.js\";\nimport { match } from \"./bg/_lib/match.js\";\n\n/**\n * @category Locales\n * @summary Bulgarian locale.\n * @language Bulgarian\n * @iso-639-2 bul\n * <AUTHOR> [@arvigeus](https://github.com/arvigeus)\n * <AUTHOR> [@fintara](https://github.com/fintara)\n */\nexport const bg = {\n  code: \"bg\",\n  formatDistance: formatDistance,\n  formatLong: formatLong,\n  formatRelative: formatRelative,\n  localize: localize,\n  match: match,\n  options: {\n    weekStartsOn: 1 /* Monday */,\n    firstWeekContainsDate: 1\n  }\n};\n\n// Fallback for modularized imports:\nexport default bg;", "map": {"version": 3, "names": ["formatDistance", "formatLong", "formatRelative", "localize", "match", "bg", "code", "options", "weekStartsOn", "firstWeekContainsDate"], "sources": ["C:/CMS/webapp/frontend/node_modules/date-fns/locale/bg.js"], "sourcesContent": ["import { formatDistance } from \"./bg/_lib/formatDistance.js\";\nimport { formatLong } from \"./bg/_lib/formatLong.js\";\nimport { formatRelative } from \"./bg/_lib/formatRelative.js\";\nimport { localize } from \"./bg/_lib/localize.js\";\nimport { match } from \"./bg/_lib/match.js\";\n\n/**\n * @category Locales\n * @summary Bulgarian locale.\n * @language Bulgarian\n * @iso-639-2 bul\n * <AUTHOR> [@arvigeus](https://github.com/arvigeus)\n * <AUTHOR> [@fintara](https://github.com/fintara)\n */\nexport const bg = {\n  code: \"bg\",\n  formatDistance: formatDistance,\n  formatLong: formatLong,\n  formatRelative: formatRelative,\n  localize: localize,\n  match: match,\n  options: {\n    weekStartsOn: 1 /* Monday */,\n    firstWeekContainsDate: 1,\n  },\n};\n\n// Fallback for modularized imports:\nexport default bg;\n"], "mappings": "AAAA,SAASA,cAAc,QAAQ,6BAA6B;AAC5D,SAASC,UAAU,QAAQ,yBAAyB;AACpD,SAASC,cAAc,QAAQ,6BAA6B;AAC5D,SAASC,QAAQ,QAAQ,uBAAuB;AAChD,SAASC,KAAK,QAAQ,oBAAoB;;AAE1C;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO,MAAMC,EAAE,GAAG;EAChBC,IAAI,EAAE,IAAI;EACVN,cAAc,EAAEA,cAAc;EAC9BC,UAAU,EAAEA,UAAU;EACtBC,cAAc,EAAEA,cAAc;EAC9BC,QAAQ,EAAEA,QAAQ;EAClBC,KAAK,EAAEA,KAAK;EACZG,OAAO,EAAE;IACPC,YAAY,EAAE,CAAC,CAAC;IAChBC,qBAAqB,EAAE;EACzB;AACF,CAAC;;AAED;AACA,eAAeJ,EAAE", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}