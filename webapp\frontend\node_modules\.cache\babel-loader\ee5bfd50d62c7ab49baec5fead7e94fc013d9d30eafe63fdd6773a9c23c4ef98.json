{"ast": null, "code": "import { compareAsc } from \"./compareAsc.mjs\";\nimport { differenceInCalendarISOWeekYears } from \"./differenceInCalendarISOWeekYears.mjs\";\nimport { subISOWeekYears } from \"./subISOWeekYears.mjs\";\nimport { toDate } from \"./toDate.mjs\";\n\n/**\n * @name differenceInISOWeekYears\n * @category ISO Week-Numbering Year Helpers\n * @summary Get the number of full ISO week-numbering years between the given dates.\n *\n * @description\n * Get the number of full ISO week-numbering years between the given dates.\n *\n * ISO week-numbering year: http://en.wikipedia.org/wiki/ISO_week_date\n *\n * @typeParam DateType - The `Date` type, the function operates on. Gets inferred from passed arguments. Allows to use extensions like [`UTCDate`](https://github.com/date-fns/utc).\n *\n * @param dateLeft - The later date\n * @param dateRight - The earlier date\n *\n * @returns The number of full ISO week-numbering years\n *\n * @example\n * // How many full ISO week-numbering years are between 1 January 2010 and 1 January 2012?\n * const result = differenceInISOWeekYears(\n *   new Date(2012, 0, 1),\n *   new Date(2010, 0, 1)\n * )\n * //=> 1\n */\nexport function differenceInISOWeekYears(dateLeft, dateRight) {\n  let _dateLeft = toDate(dateLeft);\n  const _dateRight = toDate(dateRight);\n  const sign = compareAsc(_dateLeft, _dateRight);\n  const difference = Math.abs(differenceInCalendarISOWeekYears(_dateLeft, _dateRight));\n  _dateLeft = subISOWeekYears(_dateLeft, sign * difference);\n\n  // Math.abs(diff in full ISO years - diff in calendar ISO years) === 1\n  // if last calendar ISO year is not full\n  // If so, result must be decreased by 1 in absolute value\n  const isLastISOWeekYearNotFull = Number(compareAsc(_dateLeft, _dateRight) === -sign);\n  const result = sign * (difference - isLastISOWeekYearNotFull);\n  // Prevent negative zero\n  return result === 0 ? 0 : result;\n}\n\n// Fallback for modularized imports:\nexport default differenceInISOWeekYears;", "map": {"version": 3, "names": ["compareAsc", "differenceInCalendarISOWeekYears", "subISOWeekYears", "toDate", "differenceInISOWeekYears", "dateLeft", "dateRight", "_dateLeft", "_dateRight", "sign", "difference", "Math", "abs", "isLastISOWeekYearNotFull", "Number", "result"], "sources": ["C:/CMS/webapp/frontend/node_modules/date-fns/differenceInISOWeekYears.mjs"], "sourcesContent": ["import { compareAsc } from \"./compareAsc.mjs\";\nimport { differenceInCalendarISOWeekYears } from \"./differenceInCalendarISOWeekYears.mjs\";\nimport { subISOWeekYears } from \"./subISOWeekYears.mjs\";\nimport { toDate } from \"./toDate.mjs\";\n\n/**\n * @name differenceInISOWeekYears\n * @category ISO Week-Numbering Year Helpers\n * @summary Get the number of full ISO week-numbering years between the given dates.\n *\n * @description\n * Get the number of full ISO week-numbering years between the given dates.\n *\n * ISO week-numbering year: http://en.wikipedia.org/wiki/ISO_week_date\n *\n * @typeParam DateType - The `Date` type, the function operates on. Gets inferred from passed arguments. Allows to use extensions like [`UTCDate`](https://github.com/date-fns/utc).\n *\n * @param dateLeft - The later date\n * @param dateRight - The earlier date\n *\n * @returns The number of full ISO week-numbering years\n *\n * @example\n * // How many full ISO week-numbering years are between 1 January 2010 and 1 January 2012?\n * const result = differenceInISOWeekYears(\n *   new Date(2012, 0, 1),\n *   new Date(2010, 0, 1)\n * )\n * //=> 1\n */\nexport function differenceInISOWeekYears(dateLeft, dateRight) {\n  let _dateLeft = toDate(dateLeft);\n  const _dateRight = toDate(dateRight);\n\n  const sign = compareAsc(_dateLeft, _dateRight);\n  const difference = Math.abs(\n    differenceInCalendarISOWeekYears(_dateLeft, _dateRight),\n  );\n  _dateLeft = subISOWeekYears(_dateLeft, sign * difference);\n\n  // Math.abs(diff in full ISO years - diff in calendar ISO years) === 1\n  // if last calendar ISO year is not full\n  // If so, result must be decreased by 1 in absolute value\n  const isLastISOWeekYearNotFull = Number(\n    compareAsc(_dateLeft, _dateRight) === -sign,\n  );\n  const result = sign * (difference - isLastISOWeekYearNotFull);\n  // Prevent negative zero\n  return result === 0 ? 0 : result;\n}\n\n// Fallback for modularized imports:\nexport default differenceInISOWeekYears;\n"], "mappings": "AAAA,SAASA,UAAU,QAAQ,kBAAkB;AAC7C,SAASC,gCAAgC,QAAQ,wCAAwC;AACzF,SAASC,eAAe,QAAQ,uBAAuB;AACvD,SAASC,MAAM,QAAQ,cAAc;;AAErC;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO,SAASC,wBAAwBA,CAACC,QAAQ,EAAEC,SAAS,EAAE;EAC5D,IAAIC,SAAS,GAAGJ,MAAM,CAACE,QAAQ,CAAC;EAChC,MAAMG,UAAU,GAAGL,MAAM,CAACG,SAAS,CAAC;EAEpC,MAAMG,IAAI,GAAGT,UAAU,CAACO,SAAS,EAAEC,UAAU,CAAC;EAC9C,MAAME,UAAU,GAAGC,IAAI,CAACC,GAAG,CACzBX,gCAAgC,CAACM,SAAS,EAAEC,UAAU,CACxD,CAAC;EACDD,SAAS,GAAGL,eAAe,CAACK,SAAS,EAAEE,IAAI,GAAGC,UAAU,CAAC;;EAEzD;EACA;EACA;EACA,MAAMG,wBAAwB,GAAGC,MAAM,CACrCd,UAAU,CAACO,SAAS,EAAEC,UAAU,CAAC,KAAK,CAACC,IACzC,CAAC;EACD,MAAMM,MAAM,GAAGN,IAAI,IAAIC,UAAU,GAAGG,wBAAwB,CAAC;EAC7D;EACA,OAAOE,MAAM,KAAK,CAAC,GAAG,CAAC,GAAGA,MAAM;AAClC;;AAEA;AACA,eAAeX,wBAAwB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}