{"ast": null, "code": "import { constructNow } from \"./constructNow.mjs\";\nimport { isSameSecond } from \"./isSameSecond.mjs\";\n\n/**\n * @name isThisSecond\n * @category Second Helpers\n * @summary Is the given date in the same second as the current date?\n * @pure false\n *\n * @description\n * Is the given date in the same second as the current date?\n *\n * @typeParam DateType - The `Date` type, the function operates on. Gets inferred from passed arguments. Allows to use extensions like [`UTCDate`](https://github.com/date-fns/utc).\n *\n * @param date - The date to check\n *\n * @returns The date is in this second\n *\n * @example\n * // If now is 25 September 2014 18:30:15.500,\n * // is 25 September 2014 18:30:15.000 in this second?\n * const result = isThisSecond(new Date(2014, 8, 25, 18, 30, 15))\n * //=> true\n */\nexport function isThisSecond(date) {\n  return isSameSecond(date, constructNow(date));\n}\n\n// Fallback for modularized imports:\nexport default isThisSecond;", "map": {"version": 3, "names": ["constructNow", "isSameSecond", "isThisSecond", "date"], "sources": ["C:/CMS/webapp/frontend/node_modules/date-fns/isThisSecond.mjs"], "sourcesContent": ["import { constructNow } from \"./constructNow.mjs\";\nimport { isSameSecond } from \"./isSameSecond.mjs\";\n\n/**\n * @name isThisSecond\n * @category Second Helpers\n * @summary Is the given date in the same second as the current date?\n * @pure false\n *\n * @description\n * Is the given date in the same second as the current date?\n *\n * @typeParam DateType - The `Date` type, the function operates on. Gets inferred from passed arguments. Allows to use extensions like [`UTCDate`](https://github.com/date-fns/utc).\n *\n * @param date - The date to check\n *\n * @returns The date is in this second\n *\n * @example\n * // If now is 25 September 2014 18:30:15.500,\n * // is 25 September 2014 18:30:15.000 in this second?\n * const result = isThisSecond(new Date(2014, 8, 25, 18, 30, 15))\n * //=> true\n */\nexport function isThisSecond(date) {\n  return isSameSecond(date, constructNow(date));\n}\n\n// Fallback for modularized imports:\nexport default isThisSecond;\n"], "mappings": "AAAA,SAASA,YAAY,QAAQ,oBAAoB;AACjD,SAASC,YAAY,QAAQ,oBAAoB;;AAEjD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO,SAASC,YAAYA,CAACC,IAAI,EAAE;EACjC,OAAOF,YAAY,CAACE,IAAI,EAAEH,YAAY,CAACG,IAAI,CAAC,CAAC;AAC/C;;AAEA;AACA,eAAeD,YAAY", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}