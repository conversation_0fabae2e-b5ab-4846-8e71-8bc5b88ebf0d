{"ast": null, "code": "import axios from 'axios';\nimport config from '../config';\nconst API_URL = config.API_URL;\n\n// Crea un'istanza di axios con configurazione personalizzata\nconst axiosInstance = axios.create({\n  baseURL: API_URL,\n  headers: {\n    'Content-Type': 'application/json'\n  },\n  timeout: 60000,\n  // Timeout aumentato a 60 secondi per risolvere problemi di connessione\n  withCredentials: false // Modificato per risolvere problemi CORS\n});\n\n// Configura axios per includere il token in tutte le richieste\naxiosInstance.interceptors.request.use(config => {\n  const token = localStorage.getItem('token');\n  if (token) {\n    config.headers.Authorization = `Bearer ${token}`;\n  }\n  return config;\n}, error => {\n  return Promise.reject(error);\n});\nconst caviService = {\n  // Ottiene la lista dei cavi di un cantiere\n  getCavi: async (cantiereId, tipoCavo = null, filters = {}) => {\n    try {\n      console.log('getCavi chiamato con:', {\n        cantiereId,\n        tipoCavo,\n        filters\n      });\n      console.log('Tipo di cantiereId:', typeof cantiereId);\n\n      // Verifica che cantiereId sia definito\n      if (cantiereId === undefined || cantiereId === null) {\n        console.error('cantiereId è undefined o null');\n        throw new Error('ID cantiere mancante');\n      }\n\n      // Assicurati che cantiereId sia un numero\n      let cantiereIdNum = cantiereId;\n      if (typeof cantiereId === 'string') {\n        cantiereIdNum = parseInt(cantiereId, 10);\n        console.log('cantiereId convertito da stringa a numero:', cantiereIdNum);\n      }\n      if (isNaN(cantiereIdNum)) {\n        console.error('ID cantiere non è un numero valido:', cantiereId);\n        throw new Error(`ID cantiere non valido: ${cantiereId}`);\n      }\n\n      // Log dettagliati per debug\n      console.log(`Caricamento cavi per cantiere ${cantiereIdNum} con tipo_cavo=${tipoCavo}`);\n\n      // Soluzione alternativa per i cavi SPARE\n      if (tipoCavo === 3) {\n        console.log('Caricamento cavi SPARE con query diretta...');\n        try {\n          // Usa una query SQL diretta per ottenere i cavi SPARE\n          const response = await axios.get(`${API_URL}/cavi/spare/${cantiereIdNum}`, {\n            headers: {\n              'Content-Type': 'application/json',\n              'Authorization': `Bearer ${localStorage.getItem('token')}`\n            },\n            timeout: 30000\n          });\n          console.log('Risposta cavi SPARE:', response.data);\n          return response.data;\n        } catch (spareError) {\n          console.error('Errore nel caricamento dei cavi SPARE:', spareError);\n          // Se fallisce, continua con il metodo standard\n        }\n      }\n\n      // Costruisci l'URL con i parametri di query\n      let url = `/cavi/${cantiereIdNum}`;\n      const queryParams = [];\n      if (tipoCavo !== null) {\n        queryParams.push(`tipo_cavo=${tipoCavo}`);\n      }\n\n      // Aggiungi filtri aggiuntivi se presenti\n      if (filters.stato_installazione) {\n        queryParams.push(`stato_installazione=${encodeURIComponent(filters.stato_installazione)}`);\n      }\n      if (filters.tipologia) {\n        queryParams.push(`tipologia=${encodeURIComponent(filters.tipologia)}`);\n      }\n      if (filters.sort_by) {\n        queryParams.push(`sort_by=${encodeURIComponent(filters.sort_by)}`);\n        if (filters.sort_order) {\n          queryParams.push(`sort_order=${encodeURIComponent(filters.sort_order)}`);\n        }\n      }\n\n      // Aggiungi i parametri di query all'URL\n      if (queryParams.length > 0) {\n        url += `?${queryParams.join('&')}`;\n      }\n\n      // Log dettagliato dell'URL e dei parametri\n      console.log('URL API completo:', url);\n      console.log('Parametri di query:', queryParams);\n      console.log(`Chiamata API: GET ${url}`);\n      console.log('Token:', localStorage.getItem('token') ? 'Presente' : 'Mancante');\n      console.log('URL completo:', `${API_URL}${url}`);\n      try {\n        console.log(`Tentativo di chiamata API: GET ${url} con token: ${localStorage.getItem('token') ? 'presente' : 'mancante'}`);\n        console.log('Headers della richiesta:', {\n          'Content-Type': 'application/json',\n          'Authorization': `Bearer ${localStorage.getItem('token')}`\n        });\n\n        // Aggiungi un timeout più lungo per la richiesta\n        const response = await axiosInstance.get(url, {\n          timeout: 60000\n        });\n        console.log(`Risposta API: ${url}`, response.data);\n        console.log('Status della risposta:', response.status);\n        console.log('Headers della risposta:', response.headers);\n        if (Array.isArray(response.data)) {\n          console.log(`Numero di cavi ricevuti: ${response.data.length}`);\n          if (response.data.length > 0) {\n            console.log('Primo cavo ricevuto:', response.data[0]);\n          } else {\n            console.warn(`Nessun cavo trovato per il cantiere ${cantiereIdNum} con tipo ${tipoCavo}`);\n          }\n        } else {\n          console.warn(`Risposta non è un array: ${typeof response.data}`, response.data);\n        }\n        return response.data;\n      } catch (apiError) {\n        var _apiError$response, _apiError$response2, _apiError$response3, _apiError$response4;\n        console.error(`Errore nella chiamata API GET ${url}:`, apiError);\n        console.error('Dettagli errore API:', {\n          message: apiError.message,\n          status: (_apiError$response = apiError.response) === null || _apiError$response === void 0 ? void 0 : _apiError$response.status,\n          statusText: (_apiError$response2 = apiError.response) === null || _apiError$response2 === void 0 ? void 0 : _apiError$response2.statusText,\n          data: (_apiError$response3 = apiError.response) === null || _apiError$response3 === void 0 ? void 0 : _apiError$response3.data,\n          headers: (_apiError$response4 = apiError.response) === null || _apiError$response4 === void 0 ? void 0 : _apiError$response4.headers,\n          code: apiError.code,\n          isAxiosError: apiError.isAxiosError,\n          config: apiError.config ? {\n            url: apiError.config.url,\n            method: apiError.config.method,\n            timeout: apiError.config.timeout,\n            headers: apiError.config.headers\n          } : 'No config'\n        });\n\n        // Gestione specifica per errori di rete\n        if (apiError.code === 'ERR_NETWORK') {\n          console.error('Errore di rete. Verifica che il backend sia in esecuzione e accessibile.');\n          // Prova a fare una richiesta di base per verificare se il backend è raggiungibile\n          try {\n            console.log('Tentativo di test di connessione al backend...');\n            const testResponse = await fetch(API_URL);\n            console.log('Test di connessione al backend:', testResponse.status);\n          } catch (testError) {\n            console.error('Test di connessione al backend fallito:', testError);\n          }\n        }\n        throw apiError;\n      }\n    } catch (error) {\n      var _error$response, _error$response2, _error$response3, _error$response4, _error$response4$data, _error$response5, _error$response6;\n      console.error('Get cavi error:', error);\n      console.error('Error details:', {\n        message: error.message,\n        status: (_error$response = error.response) === null || _error$response === void 0 ? void 0 : _error$response.status,\n        statusText: (_error$response2 = error.response) === null || _error$response2 === void 0 ? void 0 : _error$response2.statusText,\n        data: (_error$response3 = error.response) === null || _error$response3 === void 0 ? void 0 : _error$response3.data,\n        url: `/cavi/${cantiereId}${tipoCavo !== null ? `?tipo_cavo=${tipoCavo}` : ''}`,\n        stack: error.stack\n      });\n\n      // Verifica se l'errore è dovuto a un problema di connessione\n      if (error.code === 'ECONNABORTED' || error.message.includes('timeout') || error.message.includes('Network Error')) {\n        console.error('Errore di connessione o timeout');\n        // Ritorna un array vuoto invece di lanciare un errore\n        console.log('Ritorno array vuoto come fallback');\n        return [];\n      }\n\n      // Crea un errore più informativo\n      const enhancedError = new Error(((_error$response4 = error.response) === null || _error$response4 === void 0 ? void 0 : (_error$response4$data = _error$response4.data) === null || _error$response4$data === void 0 ? void 0 : _error$response4$data.detail) || error.message || 'Errore sconosciuto');\n      enhancedError.status = (_error$response5 = error.response) === null || _error$response5 === void 0 ? void 0 : _error$response5.status;\n      enhancedError.data = (_error$response6 = error.response) === null || _error$response6 === void 0 ? void 0 : _error$response6.data;\n      enhancedError.response = error.response;\n      enhancedError.originalError = error;\n      enhancedError.code = error.code;\n      enhancedError.isAxiosError = error.isAxiosError;\n      throw enhancedError;\n    }\n  },\n  // Crea un nuovo cavo\n  createCavo: async (cantiereId, cavoData) => {\n    try {\n      // Assicurati che cantiereId sia un numero\n      const cantiereIdNum = parseInt(cantiereId, 10);\n      if (isNaN(cantiereIdNum)) {\n        throw new Error(`ID cantiere non valido: ${cantiereId}`);\n      }\n      console.log(`Tentativo di creazione cavo per cantiere ${cantiereIdNum}`);\n      console.log('Dati inviati:', JSON.stringify(cavoData, null, 2));\n\n      // Verifica che il backend sia raggiungibile\n      try {\n        console.log('Verifica connessione al backend...');\n        const pingResponse = await fetch(`${API_URL}/ping`, {\n          method: 'GET'\n        });\n        console.log('Ping al backend:', pingResponse.status, pingResponse.statusText);\n      } catch (pingError) {\n        console.error('Errore durante il ping al backend:', pingError);\n        console.log('Tentativo di continuare comunque...');\n      }\n\n      // Torna a usare axios che gestisce meglio gli errori di rete\n      console.log('Invio richiesta con axios...');\n      const token = localStorage.getItem('token');\n      console.log('Token di autenticazione presente:', !!token);\n\n      // Usa direttamente axios invece di axiosInstance\n      const response = await axios({\n        method: 'post',\n        url: `${API_URL}/cavi/${cantiereIdNum}`,\n        data: cavoData,\n        headers: {\n          'Content-Type': 'application/json',\n          'Authorization': `Bearer ${token}`\n        },\n        timeout: 60000,\n        // 60 secondi\n        withCredentials: false // Disabilita l'invio di credenziali\n      });\n      console.log('Risposta del server:', response.status, response.statusText);\n      console.log('Dati ricevuti:', response.data);\n      return response.data;\n    } catch (error) {\n      console.error('Create cavo error:', error);\n\n      // Gestione dettagliata dell'errore\n      if (error.response) {\n        // Il server ha risposto con un codice di stato diverso da 2xx\n        console.error('Errore dal server:', error.response.status, error.response.statusText);\n        console.error('Dati errore:', error.response.data);\n        throw error.response.data;\n      } else if (error.request) {\n        // La richiesta è stata inviata ma non è stata ricevuta alcuna risposta\n        console.error('Nessuna risposta dal server:', error.request);\n\n        // Prova un approccio alternativo con XMLHttpRequest\n        console.log('Tentativo con XMLHttpRequest...');\n        return new Promise((resolve, reject) => {\n          const xhr = new XMLHttpRequest();\n          xhr.open('POST', `${API_URL}/cavi/${cantiereId}`, true);\n          xhr.setRequestHeader('Content-Type', 'application/json');\n\n          // Ottieni il token di autenticazione\n          const authToken = localStorage.getItem('token');\n          if (authToken) {\n            xhr.setRequestHeader('Authorization', `Bearer ${authToken}`);\n          }\n          xhr.timeout = 60000; // 60 secondi\n\n          xhr.onload = function () {\n            if (xhr.status >= 200 && xhr.status < 300) {\n              console.log('XMLHttpRequest successo:', xhr.responseText);\n              try {\n                const data = JSON.parse(xhr.responseText);\n                resolve(data);\n              } catch (e) {\n                console.warn('Risposta non contiene JSON valido:', e);\n                resolve({\n                  message: 'Operazione completata con successo'\n                });\n              }\n            } else {\n              console.error('XMLHttpRequest errore:', xhr.status, xhr.statusText);\n              reject(new Error(`Errore ${xhr.status}: ${xhr.statusText}`));\n            }\n          };\n          xhr.onerror = function () {\n            console.error('XMLHttpRequest errore di rete');\n            reject(new Error('Errore di rete. Verifica la connessione e riprova.'));\n          };\n          xhr.ontimeout = function () {\n            console.error('XMLHttpRequest timeout');\n            reject(new Error('Timeout della richiesta. Il server non risponde.'));\n          };\n          xhr.send(JSON.stringify(cavoData));\n        });\n      } else {\n        // Si è verificato un errore durante l'impostazione della richiesta\n        console.error('Errore durante l\\'impostazione della richiesta:', error.message);\n        throw new Error(`Errore di connessione: ${error.message}`);\n      }\n    }\n  },\n  // Ottiene un cavo specifico per ID\n  getCavoById: async (cantiereId, cavoId) => {\n    try {\n      // Assicurati che cantiereId sia un numero\n      const cantiereIdNum = parseInt(cantiereId, 10);\n      if (isNaN(cantiereIdNum)) {\n        throw new Error(`ID cantiere non valido: ${cantiereId}`);\n      }\n      const response = await axiosInstance.get(`/cavi/${cantiereIdNum}/${cavoId}`);\n      return response.data;\n    } catch (error) {\n      console.error('Get cavo by ID error:', error);\n      throw error.response ? error.response.data : error;\n    }\n  },\n  // Aggiorna un cavo esistente\n  updateCavo: async (cantiereId, cavoId, cavoData) => {\n    try {\n      // Assicurati che cantiereId sia un numero\n      const cantiereIdNum = parseInt(cantiereId, 10);\n      if (isNaN(cantiereIdNum)) {\n        throw new Error(`ID cantiere non valido: ${cantiereId}`);\n      }\n      console.log(`Inviando richiesta PUT a /cavi/${cantiereIdNum}/${cavoId}`);\n      console.log('Dati inviati:', cavoData);\n\n      // Imposta un timeout più lungo per la richiesta\n      const response = await axiosInstance.put(`/cavi/${cantiereIdNum}/${cavoId}`, cavoData, {\n        timeout: 30000 // 30 secondi\n      });\n      console.log('Risposta ricevuta:', response.data);\n      return response.data;\n    } catch (error) {\n      console.error('Update cavo error:', error);\n\n      // Gestione più dettagliata dell'errore\n      if (error.response) {\n        // Il server ha risposto con un codice di stato diverso da 2xx\n        console.error('Errore dal server:', error.response.status, error.response.statusText);\n        console.error('Dati errore:', error.response.data);\n        throw error.response.data;\n      } else if (error.request) {\n        // La richiesta è stata inviata ma non è stata ricevuta alcuna risposta\n        console.error('Nessuna risposta dal server:', error.request);\n        throw new Error('Nessuna risposta dal server. La modifica potrebbe essere stata salvata comunque.');\n      } else {\n        // Si è verificato un errore durante l'impostazione della richiesta\n        console.error('Errore durante l\\'impostazione della richiesta:', error.message);\n        throw error;\n      }\n    }\n  },\n  // Ottiene la revisione corrente del cantiere\n  getRevisioneCorrente: async cantiereId => {\n    try {\n      // Assicurati che cantiereId sia un numero\n      const cantiereIdNum = parseInt(cantiereId, 10);\n      if (isNaN(cantiereIdNum)) {\n        throw new Error(`ID cantiere non valido: ${cantiereId}`);\n      }\n      const response = await axiosInstance.get(`/cavi/${cantiereIdNum}/revisione-corrente`);\n      return response.data.revisione_corrente;\n    } catch (error) {\n      console.error('Get revisione corrente error:', error);\n      return '00'; // Valore di default in caso di errore\n    }\n  },\n  // Marca un cavo come SPARE\n  markCavoAsSpare: async (cantiereId, cavoId, force = false) => {\n    try {\n      // Assicurati che cantiereId sia un numero\n      const cantiereIdNum = parseInt(cantiereId, 10);\n      if (isNaN(cantiereIdNum)) {\n        throw new Error(`ID cantiere non valido: ${cantiereId}`);\n      }\n\n      // Log dettagliati per debug\n      console.log('Tentativo di marcare cavo come SPARE:', {\n        cantiereId: cantiereIdNum,\n        cavoId,\n        force\n      });\n\n      // Prova prima con l'endpoint POST specifico\n      console.log('URL API (POST):', `${API_URL}/cavi/${cantiereIdNum}/${cavoId}/mark-as-spare`);\n      try {\n        const postResponse = await axios.post(`${API_URL}/cavi/${cantiereIdNum}/${cavoId}/mark-as-spare`, {\n          force: force\n        }, {\n          headers: {\n            'Content-Type': 'application/json',\n            'Authorization': `Bearer ${localStorage.getItem('token')}`\n          },\n          timeout: 30000\n        });\n        console.log('Risposta markCavoAsSpare (POST):', postResponse.data);\n\n        // Verifica che il cavo sia stato effettivamente marcato come SPARE\n        await new Promise(resolve => setTimeout(resolve, 1000));\n\n        // Verifica lo stato del cavo\n        console.log('Verifica dello stato del cavo dopo marcatura SPARE...');\n        const cavoResponse = await axios.get(`${API_URL}/cavi/${cantiereIdNum}/${cavoId}`, {\n          headers: {\n            'Content-Type': 'application/json',\n            'Authorization': `Bearer ${localStorage.getItem('token')}`\n          },\n          timeout: 30000\n        });\n        console.log('Stato del cavo dopo marcatura:', cavoResponse.data);\n\n        // Verifica che modificato_manualmente sia 3\n        if (cavoResponse.data.modificato_manualmente !== 3) {\n          console.error('ERRORE: Il cavo non risulta marcato come SPARE (modificato_manualmente != 3)');\n          throw new Error('Il cavo non risulta marcato come SPARE');\n        }\n        return cavoResponse.data;\n      } catch (postError) {\n        // Se fallisce il POST, prova con DELETE mode=spare\n        console.error('Errore con endpoint POST, tentativo con DELETE mode=spare:', postError);\n        console.log('URL API (DELETE):', `${API_URL}/cavi/${cantiereIdNum}/${cavoId}?mode=spare`);\n        const deleteResponse = await axios.delete(`${API_URL}/cavi/${cantiereIdNum}/${cavoId}`, {\n          headers: {\n            'Content-Type': 'application/json',\n            'Authorization': `Bearer ${localStorage.getItem('token')}`\n          },\n          timeout: 30000,\n          params: {\n            mode: 'spare'\n          }\n        });\n        console.log('Risposta markCavoAsSpare (DELETE mode=spare):', deleteResponse.data);\n\n        // Verifica che il cavo sia stato effettivamente marcato come SPARE\n        await new Promise(resolve => setTimeout(resolve, 1000));\n\n        // Verifica lo stato del cavo\n        console.log('Verifica dello stato del cavo dopo marcatura SPARE con DELETE...');\n        const cavoResponse = await axios.get(`${API_URL}/cavi/${cantiereIdNum}/${cavoId}`, {\n          headers: {\n            'Content-Type': 'application/json',\n            'Authorization': `Bearer ${localStorage.getItem('token')}`\n          },\n          timeout: 30000\n        });\n        console.log('Stato del cavo dopo marcatura con DELETE:', cavoResponse.data);\n\n        // Verifica che modificato_manualmente sia 3\n        if (cavoResponse.data.modificato_manualmente !== 3) {\n          console.error('ERRORE: Il cavo non risulta marcato come SPARE (modificato_manualmente != 3)');\n          throw new Error('Il cavo non risulta marcato come SPARE');\n        }\n        return cavoResponse.data;\n      }\n    } catch (error) {\n      var _error$response7, _error$response8, _error$response9;\n      console.error('Mark cavo as SPARE error:', error);\n      console.error('Dettagli errore:', {\n        message: error.message,\n        status: (_error$response7 = error.response) === null || _error$response7 === void 0 ? void 0 : _error$response7.status,\n        statusText: (_error$response8 = error.response) === null || _error$response8 === void 0 ? void 0 : _error$response8.statusText,\n        data: (_error$response9 = error.response) === null || _error$response9 === void 0 ? void 0 : _error$response9.data,\n        url: `${API_URL}/cavi/${cantiereId}/${cavoId}`,\n        config: error.config\n      });\n      throw error.response ? error.response.data : error;\n    }\n  },\n  // Elimina un cavo o lo marca come SPARE\n  deleteCavo: async (cantiereId, cavoId, mode = null) => {\n    try {\n      // Assicurati che cantiereId sia un numero\n      const cantiereIdNum = parseInt(cantiereId, 10);\n      if (isNaN(cantiereIdNum)) {\n        throw new Error(`ID cantiere non valido: ${cantiereId}`);\n      }\n\n      // Log dettagliati per debug\n      console.log('Tentativo di eliminare/marcare cavo:', {\n        cantiereId: cantiereIdNum,\n        cavoId,\n        mode\n      });\n\n      // Se è specificata la modalità, aggiungi il parametro alla richiesta\n      const requestConfig = {\n        headers: {\n          'Content-Type': 'application/json',\n          'Authorization': `Bearer ${localStorage.getItem('token')}`\n        },\n        timeout: 30000,\n        // Timeout aumentato a 30 secondi\n        params: mode ? {\n          mode\n        } : {}\n      };\n      console.log('URL API:', `${API_URL}/cavi/${cantiereIdNum}/${cavoId}`);\n      console.log('Config:', requestConfig);\n\n      // Usa axios direttamente invece di axiosInstance per avere più controllo\n      const response = await axios.delete(`${API_URL}/cavi/${cantiereIdNum}/${cavoId}`, requestConfig);\n      console.log('Risposta deleteCavo:', response.data);\n      return response.data;\n    } catch (error) {\n      var _error$response0, _error$response1, _error$response10;\n      console.error('Delete cavo error:', error);\n      console.error('Error details:', {\n        message: error.message,\n        status: (_error$response0 = error.response) === null || _error$response0 === void 0 ? void 0 : _error$response0.status,\n        statusText: (_error$response1 = error.response) === null || _error$response1 === void 0 ? void 0 : _error$response1.statusText,\n        data: (_error$response10 = error.response) === null || _error$response10 === void 0 ? void 0 : _error$response10.data,\n        url: `${API_URL}/cavi/${cantiereId}/${cavoId}`,\n        config: error.config\n      });\n\n      // Crea un errore più informativo\n      if (error.response && error.response.data) {\n        throw error.response.data;\n      } else if (error.message) {\n        throw new Error(error.message);\n      } else {\n        throw new Error('Errore durante l\\'eliminazione del cavo');\n      }\n    }\n  },\n  // Aggiorna i metri posati di un cavo\n  updateMetriPosati: async (cantiereId, cavoId, metriPosati) => {\n    try {\n      // Assicurati che cantiereId sia un numero\n      const cantiereIdNum = parseInt(cantiereId, 10);\n      if (isNaN(cantiereIdNum)) {\n        throw new Error(`ID cantiere non valido: ${cantiereId}`);\n      }\n      const response = await axiosInstance.post(`/cavi/${cantiereIdNum}/${cavoId}/metri-posati`, {\n        metri_posati: metriPosati\n      });\n      return response.data;\n    } catch (error) {\n      console.error('Update metri posati error:', error);\n      throw error.response ? error.response.data : error;\n    }\n  },\n  // Modifica la bobina di un cavo posato\n  updateBobina: async (cantiereId, cavoId, idBobina) => {\n    try {\n      // Assicurati che cantiereId sia un numero\n      const cantiereIdNum = parseInt(cantiereId, 10);\n      if (isNaN(cantiereIdNum)) {\n        throw new Error(`ID cantiere non valido: ${cantiereId}`);\n      }\n      const response = await axiosInstance.post(`/cavi/${cantiereIdNum}/${cavoId}/bobina`, {\n        id_bobina: idBobina\n      });\n      return response.data;\n    } catch (error) {\n      console.error('Update bobina error:', error);\n      throw error.response ? error.response.data : error;\n    }\n  },\n  // Ottiene la lista dei cavi installati di un cantiere\n  getCaviInstallati: async cantiereId => {\n    try {\n      // Assicurati che cantiereId sia un numero\n      const cantiereIdNum = parseInt(cantiereId, 10);\n      if (isNaN(cantiereIdNum)) {\n        throw new Error(`ID cantiere non valido: ${cantiereId}`);\n      }\n      const response = await axiosInstance.get(`/cavi/${cantiereIdNum}/installati`);\n      return response.data;\n    } catch (error) {\n      console.error('Get cavi installati error:', error);\n      throw error.response ? error.response.data : error;\n    }\n  },\n  // Ottiene le statistiche dei cavi di un cantiere\n  getCaviStats: async cantiereId => {\n    try {\n      // Assicurati che cantiereId sia un numero\n      const cantiereIdNum = parseInt(cantiereId, 10);\n      if (isNaN(cantiereIdNum)) {\n        throw new Error(`ID cantiere non valido: ${cantiereId}`);\n      }\n      const response = await axiosInstance.get(`/cavi/${cantiereIdNum}/stats`);\n      return response.data;\n    } catch (error) {\n      console.error('Get cavi stats error:', error);\n      throw error.response ? error.response.data : error;\n    }\n  },\n  // Ottiene direttamente i cavi SPARE\n  getCaviSpare: async cantiereId => {\n    try {\n      // Assicurati che cantiereId sia un numero\n      const cantiereIdNum = parseInt(cantiereId, 10);\n      if (isNaN(cantiereIdNum)) {\n        throw new Error(`ID cantiere non valido: ${cantiereId}`);\n      }\n      console.log('Caricamento cavi SPARE...');\n\n      // Prova prima con l'endpoint standard con tipo_cavo=3\n      console.log('URL API (standard):', `${API_URL}/cavi/${cantiereIdNum}?tipo_cavo=3`);\n      try {\n        const response = await axios.get(`${API_URL}/cavi/${cantiereIdNum}`, {\n          headers: {\n            'Content-Type': 'application/json',\n            'Authorization': `Bearer ${localStorage.getItem('token')}`\n          },\n          timeout: 30000,\n          params: {\n            tipo_cavo: 3\n          }\n        });\n        console.log('Risposta getCaviSpare (standard):', response.data ? response.data.length : 0, 'cavi SPARE trovati');\n        if (response.data && response.data.length > 0) {\n          console.log('Primo cavo SPARE:', response.data[0]);\n        }\n        return response.data;\n      } catch (standardError) {\n        console.error('Errore con endpoint standard, tentativo con endpoint dedicato:', standardError);\n\n        // Se fallisce, prova con l'endpoint dedicato\n        console.log('URL API (dedicato):', `${API_URL}/cavi/spare/${cantiereIdNum}`);\n        const dedicatedResponse = await axios.get(`${API_URL}/cavi/spare/${cantiereIdNum}`, {\n          headers: {\n            'Content-Type': 'application/json',\n            'Authorization': `Bearer ${localStorage.getItem('token')}`\n          },\n          timeout: 30000\n        });\n        console.log('Risposta getCaviSpare (dedicato):', dedicatedResponse.data ? dedicatedResponse.data.length : 0, 'cavi SPARE trovati');\n        if (dedicatedResponse.data && dedicatedResponse.data.length > 0) {\n          console.log('Primo cavo SPARE (dedicato):', dedicatedResponse.data[0]);\n        }\n        return dedicatedResponse.data;\n      }\n    } catch (error) {\n      console.error('Get cavi SPARE error:', error);\n      throw error.response ? error.response.data : error;\n    }\n  },\n  // Collega un lato di un cavo\n  collegaCavo: async (cantiereId, cavoId, lato, responsabile) => {\n    try {\n      // Assicurati che cantiereId sia un numero\n      const cantiereIdNum = parseInt(cantiereId, 10);\n      if (isNaN(cantiereIdNum)) {\n        throw new Error(`ID cantiere non valido: ${cantiereId}`);\n      }\n      const response = await axiosInstance.post(`/cavi/${cantiereIdNum}/${cavoId}/collegamento`, {\n        lato: lato,\n        responsabile: responsabile || 'cantiere'\n      });\n      return response.data;\n    } catch (error) {\n      console.error('Collega cavo error:', error);\n      throw error.response ? error.response.data : error;\n    }\n  },\n  // Scollega un lato di un cavo\n  scollegaCavo: async (cantiereId, cavoId, lato) => {\n    try {\n      // Assicurati che cantiereId sia un numero\n      const cantiereIdNum = parseInt(cantiereId, 10);\n      if (isNaN(cantiereIdNum)) {\n        throw new Error(`ID cantiere non valido: ${cantiereId}`);\n      }\n      const response = await axiosInstance.delete(`/cavi/${cantiereIdNum}/${cavoId}/collegamento/${lato}`);\n      return response.data;\n    } catch (error) {\n      console.error('Scollega cavo error:', error);\n      throw error.response ? error.response.data : error;\n    }\n  },\n  // Verifica lo stato di un cavo specifico (debug)\n  debugCavo: async (cantiereId, cavoId) => {\n    try {\n      // Assicurati che cantiereId sia un numero\n      const cantiereIdNum = parseInt(cantiereId, 10);\n      if (isNaN(cantiereIdNum)) {\n        throw new Error(`ID cantiere non valido: ${cantiereId}`);\n      }\n\n      // Ottieni i cavi attivi\n      console.log('Verificando cavo tra i cavi attivi...');\n      const attivi = await axiosInstance.get(`/cavi/${cantiereIdNum}?tipo_cavo=0`);\n      const cavoAttivo = attivi.data.find(c => c.id_cavo === cavoId);\n\n      // Ottieni i cavi SPARE\n      console.log('Verificando cavo tra i cavi SPARE...');\n      const spare = await axiosInstance.get(`/cavi/${cantiereIdNum}?tipo_cavo=3`);\n      const cavoSpare = spare.data.find(c => c.id_cavo === cavoId);\n      return {\n        trovato_tra_attivi: !!cavoAttivo,\n        trovato_tra_spare: !!cavoSpare,\n        cavo_attivo: cavoAttivo,\n        cavo_spare: cavoSpare\n      };\n    } catch (error) {\n      console.error('Debug cavo error:', error);\n      throw error.response ? error.response.data : error;\n    }\n  }\n};\nexport default caviService;", "map": {"version": 3, "names": ["axios", "config", "API_URL", "axiosInstance", "create", "baseURL", "headers", "timeout", "withCredentials", "interceptors", "request", "use", "token", "localStorage", "getItem", "Authorization", "error", "Promise", "reject", "caviService", "get<PERSON><PERSON>", "cantiereId", "tipoCavo", "filters", "console", "log", "undefined", "Error", "cantiereIdNum", "parseInt", "isNaN", "response", "get", "data", "spareError", "url", "queryParams", "push", "stato_installazione", "encodeURIComponent", "tipologia", "sort_by", "sort_order", "length", "join", "status", "Array", "isArray", "warn", "apiError", "_apiError$response", "_apiError$response2", "_apiError$response3", "_apiError$response4", "message", "statusText", "code", "isAxiosError", "method", "testResponse", "fetch", "testError", "_error$response", "_error$response2", "_error$response3", "_error$response4", "_error$response4$data", "_error$response5", "_error$response6", "stack", "includes", "enhancedError", "detail", "originalError", "createCavo", "cavoData", "JSON", "stringify", "pingResponse", "pingError", "resolve", "xhr", "XMLHttpRequest", "open", "setRequestHeader", "authToken", "onload", "responseText", "parse", "e", "onerror", "ontimeout", "send", "getCavoById", "cavoId", "updateCavo", "put", "getRevisioneCorrente", "revisione_corrente", "markCavoAsSpare", "force", "postResponse", "post", "setTimeout", "cavoResponse", "modificato_manualmente", "postError", "deleteResponse", "delete", "params", "mode", "_error$response7", "_error$response8", "_error$response9", "deleteCavo", "requestConfig", "_error$response0", "_error$response1", "_error$response10", "updateMetri<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "metri_posati", "updateBobina", "idBobina", "id_bobina", "getCaviInstallati", "getCaviStats", "getCaviSpare", "tipo_cavo", "standardError", "dedicatedResponse", "collegaCavo", "lato", "responsabile", "scollegaCavo", "debugCavo", "attivi", "cavoAttivo", "find", "c", "id_cavo", "spare", "cavoSpare", "trovato_tra_attivi", "trovato_tra_spare", "cavo_attivo", "cavo_spare"], "sources": ["C:/Users/<USER>/PycharmProjects/CMS/webapp/frontend/src/services/caviService.js"], "sourcesContent": ["import axios from 'axios';\nimport config from '../config';\n\nconst API_URL = config.API_URL;\n\n// Crea un'istanza di axios con configurazione personalizzata\nconst axiosInstance = axios.create({\n  baseURL: API_URL,\n  headers: {\n    'Content-Type': 'application/json'\n  },\n  timeout: 60000, // Timeout aumentato a 60 secondi per risolvere problemi di connessione\n  withCredentials: false // Modificato per risolvere problemi CORS\n});\n\n// Configura axios per includere il token in tutte le richieste\naxiosInstance.interceptors.request.use(\n  (config) => {\n    const token = localStorage.getItem('token');\n    if (token) {\n      config.headers.Authorization = `Bearer ${token}`;\n    }\n    return config;\n  },\n  (error) => {\n    return Promise.reject(error);\n  }\n);\n\nconst caviService = {\n  // Ottiene la lista dei cavi di un cantiere\n  getCavi: async (cantiereId, tipoCavo = null, filters = {}) => {\n    try {\n      console.log('getCavi chiamato con:', { cantiereId, tipoCavo, filters });\n      console.log('Tipo di cantiereId:', typeof cantiereId);\n\n      // Verifica che cantiereId sia definito\n      if (cantiereId === undefined || cantiereId === null) {\n        console.error('cantiereId è undefined o null');\n        throw new Error('ID cantiere mancante');\n      }\n\n      // Assicurati che cantiereId sia un numero\n      let cantiereIdNum = cantiereId;\n      if (typeof cantiereId === 'string') {\n        cantiereIdNum = parseInt(cantiereId, 10);\n        console.log('cantiereId convertito da stringa a numero:', cantiereIdNum);\n      }\n\n      if (isNaN(cantiereIdNum)) {\n        console.error('ID cantiere non è un numero valido:', cantiereId);\n        throw new Error(`ID cantiere non valido: ${cantiereId}`);\n      }\n\n      // Log dettagliati per debug\n      console.log(`Caricamento cavi per cantiere ${cantiereIdNum} con tipo_cavo=${tipoCavo}`);\n\n      // Soluzione alternativa per i cavi SPARE\n      if (tipoCavo === 3) {\n        console.log('Caricamento cavi SPARE con query diretta...');\n        try {\n          // Usa una query SQL diretta per ottenere i cavi SPARE\n          const response = await axios.get(\n            `${API_URL}/cavi/spare/${cantiereIdNum}`,\n            {\n              headers: {\n                'Content-Type': 'application/json',\n                'Authorization': `Bearer ${localStorage.getItem('token')}`\n              },\n              timeout: 30000\n            }\n          );\n\n          console.log('Risposta cavi SPARE:', response.data);\n          return response.data;\n        } catch (spareError) {\n          console.error('Errore nel caricamento dei cavi SPARE:', spareError);\n          // Se fallisce, continua con il metodo standard\n        }\n      }\n\n      // Costruisci l'URL con i parametri di query\n      let url = `/cavi/${cantiereIdNum}`;\n      const queryParams = [];\n\n      if (tipoCavo !== null) {\n        queryParams.push(`tipo_cavo=${tipoCavo}`);\n      }\n\n      // Aggiungi filtri aggiuntivi se presenti\n      if (filters.stato_installazione) {\n        queryParams.push(`stato_installazione=${encodeURIComponent(filters.stato_installazione)}`);\n      }\n\n      if (filters.tipologia) {\n        queryParams.push(`tipologia=${encodeURIComponent(filters.tipologia)}`);\n      }\n\n      if (filters.sort_by) {\n        queryParams.push(`sort_by=${encodeURIComponent(filters.sort_by)}`);\n        if (filters.sort_order) {\n          queryParams.push(`sort_order=${encodeURIComponent(filters.sort_order)}`);\n        }\n      }\n\n      // Aggiungi i parametri di query all'URL\n      if (queryParams.length > 0) {\n        url += `?${queryParams.join('&')}`;\n      }\n\n      // Log dettagliato dell'URL e dei parametri\n      console.log('URL API completo:', url);\n      console.log('Parametri di query:', queryParams);\n\n      console.log(`Chiamata API: GET ${url}`);\n      console.log('Token:', localStorage.getItem('token') ? 'Presente' : 'Mancante');\n      console.log('URL completo:', `${API_URL}${url}`);\n\n      try {\n        console.log(`Tentativo di chiamata API: GET ${url} con token: ${localStorage.getItem('token') ? 'presente' : 'mancante'}`);\n        console.log('Headers della richiesta:', {\n          'Content-Type': 'application/json',\n          'Authorization': `Bearer ${localStorage.getItem('token')}`\n        });\n\n        // Aggiungi un timeout più lungo per la richiesta\n        const response = await axiosInstance.get(url, { timeout: 60000 });\n\n        console.log(`Risposta API: ${url}`, response.data);\n        console.log('Status della risposta:', response.status);\n        console.log('Headers della risposta:', response.headers);\n\n        if (Array.isArray(response.data)) {\n          console.log(`Numero di cavi ricevuti: ${response.data.length}`);\n          if (response.data.length > 0) {\n            console.log('Primo cavo ricevuto:', response.data[0]);\n          } else {\n            console.warn(`Nessun cavo trovato per il cantiere ${cantiereIdNum} con tipo ${tipoCavo}`);\n          }\n        } else {\n          console.warn(`Risposta non è un array: ${typeof response.data}`, response.data);\n        }\n\n        return response.data;\n      } catch (apiError) {\n        console.error(`Errore nella chiamata API GET ${url}:`, apiError);\n        console.error('Dettagli errore API:', {\n          message: apiError.message,\n          status: apiError.response?.status,\n          statusText: apiError.response?.statusText,\n          data: apiError.response?.data,\n          headers: apiError.response?.headers,\n          code: apiError.code,\n          isAxiosError: apiError.isAxiosError,\n          config: apiError.config ? {\n            url: apiError.config.url,\n            method: apiError.config.method,\n            timeout: apiError.config.timeout,\n            headers: apiError.config.headers\n          } : 'No config'\n        });\n\n        // Gestione specifica per errori di rete\n        if (apiError.code === 'ERR_NETWORK') {\n          console.error('Errore di rete. Verifica che il backend sia in esecuzione e accessibile.');\n          // Prova a fare una richiesta di base per verificare se il backend è raggiungibile\n          try {\n            console.log('Tentativo di test di connessione al backend...');\n            const testResponse = await fetch(API_URL);\n            console.log('Test di connessione al backend:', testResponse.status);\n          } catch (testError) {\n            console.error('Test di connessione al backend fallito:', testError);\n          }\n        }\n\n        throw apiError;\n      }\n    } catch (error) {\n      console.error('Get cavi error:', error);\n      console.error('Error details:', {\n        message: error.message,\n        status: error.response?.status,\n        statusText: error.response?.statusText,\n        data: error.response?.data,\n        url: `/cavi/${cantiereId}${tipoCavo !== null ? `?tipo_cavo=${tipoCavo}` : ''}`,\n        stack: error.stack\n      });\n\n      // Verifica se l'errore è dovuto a un problema di connessione\n      if (error.code === 'ECONNABORTED' || error.message.includes('timeout') || error.message.includes('Network Error')) {\n        console.error('Errore di connessione o timeout');\n        // Ritorna un array vuoto invece di lanciare un errore\n        console.log('Ritorno array vuoto come fallback');\n        return [];\n      }\n\n      // Crea un errore più informativo\n      const enhancedError = new Error(error.response?.data?.detail || error.message || 'Errore sconosciuto');\n      enhancedError.status = error.response?.status;\n      enhancedError.data = error.response?.data;\n      enhancedError.response = error.response;\n      enhancedError.originalError = error;\n      enhancedError.code = error.code;\n      enhancedError.isAxiosError = error.isAxiosError;\n\n      throw enhancedError;\n    }\n  },\n\n  // Crea un nuovo cavo\n  createCavo: async (cantiereId, cavoData) => {\n    try {\n      // Assicurati che cantiereId sia un numero\n      const cantiereIdNum = parseInt(cantiereId, 10);\n      if (isNaN(cantiereIdNum)) {\n        throw new Error(`ID cantiere non valido: ${cantiereId}`);\n      }\n\n      console.log(`Tentativo di creazione cavo per cantiere ${cantiereIdNum}`);\n      console.log('Dati inviati:', JSON.stringify(cavoData, null, 2));\n\n      // Verifica che il backend sia raggiungibile\n      try {\n        console.log('Verifica connessione al backend...');\n        const pingResponse = await fetch(`${API_URL}/ping`, { method: 'GET' });\n        console.log('Ping al backend:', pingResponse.status, pingResponse.statusText);\n      } catch (pingError) {\n        console.error('Errore durante il ping al backend:', pingError);\n        console.log('Tentativo di continuare comunque...');\n      }\n\n      // Torna a usare axios che gestisce meglio gli errori di rete\n      console.log('Invio richiesta con axios...');\n      const token = localStorage.getItem('token');\n      console.log('Token di autenticazione presente:', !!token);\n\n      // Usa direttamente axios invece di axiosInstance\n      const response = await axios({\n        method: 'post',\n        url: `${API_URL}/cavi/${cantiereIdNum}`,\n        data: cavoData,\n        headers: {\n          'Content-Type': 'application/json',\n          'Authorization': `Bearer ${token}`\n        },\n        timeout: 60000, // 60 secondi\n        withCredentials: false // Disabilita l'invio di credenziali\n      });\n\n      console.log('Risposta del server:', response.status, response.statusText);\n      console.log('Dati ricevuti:', response.data);\n      return response.data;\n    } catch (error) {\n      console.error('Create cavo error:', error);\n\n      // Gestione dettagliata dell'errore\n      if (error.response) {\n        // Il server ha risposto con un codice di stato diverso da 2xx\n        console.error('Errore dal server:', error.response.status, error.response.statusText);\n        console.error('Dati errore:', error.response.data);\n        throw error.response.data;\n      } else if (error.request) {\n        // La richiesta è stata inviata ma non è stata ricevuta alcuna risposta\n        console.error('Nessuna risposta dal server:', error.request);\n\n        // Prova un approccio alternativo con XMLHttpRequest\n        console.log('Tentativo con XMLHttpRequest...');\n        return new Promise((resolve, reject) => {\n          const xhr = new XMLHttpRequest();\n          xhr.open('POST', `${API_URL}/cavi/${cantiereId}`, true);\n          xhr.setRequestHeader('Content-Type', 'application/json');\n\n          // Ottieni il token di autenticazione\n          const authToken = localStorage.getItem('token');\n          if (authToken) {\n            xhr.setRequestHeader('Authorization', `Bearer ${authToken}`);\n          }\n          xhr.timeout = 60000; // 60 secondi\n\n          xhr.onload = function() {\n            if (xhr.status >= 200 && xhr.status < 300) {\n              console.log('XMLHttpRequest successo:', xhr.responseText);\n              try {\n                const data = JSON.parse(xhr.responseText);\n                resolve(data);\n              } catch (e) {\n                console.warn('Risposta non contiene JSON valido:', e);\n                resolve({ message: 'Operazione completata con successo' });\n              }\n            } else {\n              console.error('XMLHttpRequest errore:', xhr.status, xhr.statusText);\n              reject(new Error(`Errore ${xhr.status}: ${xhr.statusText}`));\n            }\n          };\n\n          xhr.onerror = function() {\n            console.error('XMLHttpRequest errore di rete');\n            reject(new Error('Errore di rete. Verifica la connessione e riprova.'));\n          };\n\n          xhr.ontimeout = function() {\n            console.error('XMLHttpRequest timeout');\n            reject(new Error('Timeout della richiesta. Il server non risponde.'));\n          };\n\n          xhr.send(JSON.stringify(cavoData));\n        });\n      } else {\n        // Si è verificato un errore durante l'impostazione della richiesta\n        console.error('Errore durante l\\'impostazione della richiesta:', error.message);\n        throw new Error(`Errore di connessione: ${error.message}`);\n      }\n    }\n  },\n\n  // Ottiene un cavo specifico per ID\n  getCavoById: async (cantiereId, cavoId) => {\n    try {\n      // Assicurati che cantiereId sia un numero\n      const cantiereIdNum = parseInt(cantiereId, 10);\n      if (isNaN(cantiereIdNum)) {\n        throw new Error(`ID cantiere non valido: ${cantiereId}`);\n      }\n\n      const response = await axiosInstance.get(`/cavi/${cantiereIdNum}/${cavoId}`);\n      return response.data;\n    } catch (error) {\n      console.error('Get cavo by ID error:', error);\n      throw error.response ? error.response.data : error;\n    }\n  },\n\n  // Aggiorna un cavo esistente\n  updateCavo: async (cantiereId, cavoId, cavoData) => {\n    try {\n      // Assicurati che cantiereId sia un numero\n      const cantiereIdNum = parseInt(cantiereId, 10);\n      if (isNaN(cantiereIdNum)) {\n        throw new Error(`ID cantiere non valido: ${cantiereId}`);\n      }\n\n      console.log(`Inviando richiesta PUT a /cavi/${cantiereIdNum}/${cavoId}`);\n      console.log('Dati inviati:', cavoData);\n\n      // Imposta un timeout più lungo per la richiesta\n      const response = await axiosInstance.put(`/cavi/${cantiereIdNum}/${cavoId}`, cavoData, {\n        timeout: 30000, // 30 secondi\n      });\n\n      console.log('Risposta ricevuta:', response.data);\n      return response.data;\n    } catch (error) {\n      console.error('Update cavo error:', error);\n\n      // Gestione più dettagliata dell'errore\n      if (error.response) {\n        // Il server ha risposto con un codice di stato diverso da 2xx\n        console.error('Errore dal server:', error.response.status, error.response.statusText);\n        console.error('Dati errore:', error.response.data);\n        throw error.response.data;\n      } else if (error.request) {\n        // La richiesta è stata inviata ma non è stata ricevuta alcuna risposta\n        console.error('Nessuna risposta dal server:', error.request);\n        throw new Error('Nessuna risposta dal server. La modifica potrebbe essere stata salvata comunque.');\n      } else {\n        // Si è verificato un errore durante l'impostazione della richiesta\n        console.error('Errore durante l\\'impostazione della richiesta:', error.message);\n        throw error;\n      }\n    }\n  },\n\n  // Ottiene la revisione corrente del cantiere\n  getRevisioneCorrente: async (cantiereId) => {\n    try {\n      // Assicurati che cantiereId sia un numero\n      const cantiereIdNum = parseInt(cantiereId, 10);\n      if (isNaN(cantiereIdNum)) {\n        throw new Error(`ID cantiere non valido: ${cantiereId}`);\n      }\n\n      const response = await axiosInstance.get(`/cavi/${cantiereIdNum}/revisione-corrente`);\n      return response.data.revisione_corrente;\n    } catch (error) {\n      console.error('Get revisione corrente error:', error);\n      return '00'; // Valore di default in caso di errore\n    }\n  },\n\n  // Marca un cavo come SPARE\n  markCavoAsSpare: async (cantiereId, cavoId, force = false) => {\n    try {\n      // Assicurati che cantiereId sia un numero\n      const cantiereIdNum = parseInt(cantiereId, 10);\n      if (isNaN(cantiereIdNum)) {\n        throw new Error(`ID cantiere non valido: ${cantiereId}`);\n      }\n\n      // Log dettagliati per debug\n      console.log('Tentativo di marcare cavo come SPARE:', { cantiereId: cantiereIdNum, cavoId, force });\n\n      // Prova prima con l'endpoint POST specifico\n      console.log('URL API (POST):', `${API_URL}/cavi/${cantiereIdNum}/${cavoId}/mark-as-spare`);\n\n      try {\n        const postResponse = await axios.post(\n          `${API_URL}/cavi/${cantiereIdNum}/${cavoId}/mark-as-spare`,\n          { force: force },\n          {\n            headers: {\n              'Content-Type': 'application/json',\n              'Authorization': `Bearer ${localStorage.getItem('token')}`\n            },\n            timeout: 30000\n          }\n        );\n\n        console.log('Risposta markCavoAsSpare (POST):', postResponse.data);\n\n        // Verifica che il cavo sia stato effettivamente marcato come SPARE\n        await new Promise(resolve => setTimeout(resolve, 1000));\n\n        // Verifica lo stato del cavo\n        console.log('Verifica dello stato del cavo dopo marcatura SPARE...');\n        const cavoResponse = await axios.get(\n          `${API_URL}/cavi/${cantiereIdNum}/${cavoId}`,\n          {\n            headers: {\n              'Content-Type': 'application/json',\n              'Authorization': `Bearer ${localStorage.getItem('token')}`\n            },\n            timeout: 30000\n          }\n        );\n\n        console.log('Stato del cavo dopo marcatura:', cavoResponse.data);\n\n        // Verifica che modificato_manualmente sia 3\n        if (cavoResponse.data.modificato_manualmente !== 3) {\n          console.error('ERRORE: Il cavo non risulta marcato come SPARE (modificato_manualmente != 3)');\n          throw new Error('Il cavo non risulta marcato come SPARE');\n        }\n\n        return cavoResponse.data;\n      } catch (postError) {\n        // Se fallisce il POST, prova con DELETE mode=spare\n        console.error('Errore con endpoint POST, tentativo con DELETE mode=spare:', postError);\n        console.log('URL API (DELETE):', `${API_URL}/cavi/${cantiereIdNum}/${cavoId}?mode=spare`);\n\n        const deleteResponse = await axios.delete(\n          `${API_URL}/cavi/${cantiereIdNum}/${cavoId}`,\n          {\n            headers: {\n              'Content-Type': 'application/json',\n              'Authorization': `Bearer ${localStorage.getItem('token')}`\n            },\n            timeout: 30000,\n            params: { mode: 'spare' }\n          }\n        );\n\n        console.log('Risposta markCavoAsSpare (DELETE mode=spare):', deleteResponse.data);\n\n        // Verifica che il cavo sia stato effettivamente marcato come SPARE\n        await new Promise(resolve => setTimeout(resolve, 1000));\n\n        // Verifica lo stato del cavo\n        console.log('Verifica dello stato del cavo dopo marcatura SPARE con DELETE...');\n        const cavoResponse = await axios.get(\n          `${API_URL}/cavi/${cantiereIdNum}/${cavoId}`,\n          {\n            headers: {\n              'Content-Type': 'application/json',\n              'Authorization': `Bearer ${localStorage.getItem('token')}`\n            },\n            timeout: 30000\n          }\n        );\n\n        console.log('Stato del cavo dopo marcatura con DELETE:', cavoResponse.data);\n\n        // Verifica che modificato_manualmente sia 3\n        if (cavoResponse.data.modificato_manualmente !== 3) {\n          console.error('ERRORE: Il cavo non risulta marcato come SPARE (modificato_manualmente != 3)');\n          throw new Error('Il cavo non risulta marcato come SPARE');\n        }\n\n        return cavoResponse.data;\n      }\n    } catch (error) {\n      console.error('Mark cavo as SPARE error:', error);\n      console.error('Dettagli errore:', {\n        message: error.message,\n        status: error.response?.status,\n        statusText: error.response?.statusText,\n        data: error.response?.data,\n        url: `${API_URL}/cavi/${cantiereId}/${cavoId}`,\n        config: error.config\n      });\n      throw error.response ? error.response.data : error;\n    }\n  },\n\n  // Elimina un cavo o lo marca come SPARE\n  deleteCavo: async (cantiereId, cavoId, mode = null) => {\n    try {\n      // Assicurati che cantiereId sia un numero\n      const cantiereIdNum = parseInt(cantiereId, 10);\n      if (isNaN(cantiereIdNum)) {\n        throw new Error(`ID cantiere non valido: ${cantiereId}`);\n      }\n\n      // Log dettagliati per debug\n      console.log('Tentativo di eliminare/marcare cavo:', { cantiereId: cantiereIdNum, cavoId, mode });\n\n      // Se è specificata la modalità, aggiungi il parametro alla richiesta\n      const requestConfig = {\n        headers: {\n          'Content-Type': 'application/json',\n          'Authorization': `Bearer ${localStorage.getItem('token')}`\n        },\n        timeout: 30000, // Timeout aumentato a 30 secondi\n        params: mode ? { mode } : {}\n      };\n\n      console.log('URL API:', `${API_URL}/cavi/${cantiereIdNum}/${cavoId}`);\n      console.log('Config:', requestConfig);\n\n      // Usa axios direttamente invece di axiosInstance per avere più controllo\n      const response = await axios.delete(`${API_URL}/cavi/${cantiereIdNum}/${cavoId}`, requestConfig);\n      console.log('Risposta deleteCavo:', response.data);\n      return response.data;\n    } catch (error) {\n      console.error('Delete cavo error:', error);\n      console.error('Error details:', {\n        message: error.message,\n        status: error.response?.status,\n        statusText: error.response?.statusText,\n        data: error.response?.data,\n        url: `${API_URL}/cavi/${cantiereId}/${cavoId}`,\n        config: error.config\n      });\n\n      // Crea un errore più informativo\n      if (error.response && error.response.data) {\n        throw error.response.data;\n      } else if (error.message) {\n        throw new Error(error.message);\n      } else {\n        throw new Error('Errore durante l\\'eliminazione del cavo');\n      }\n    }\n  },\n\n  // Aggiorna i metri posati di un cavo\n  updateMetriPosati: async (cantiereId, cavoId, metriPosati) => {\n    try {\n      // Assicurati che cantiereId sia un numero\n      const cantiereIdNum = parseInt(cantiereId, 10);\n      if (isNaN(cantiereIdNum)) {\n        throw new Error(`ID cantiere non valido: ${cantiereId}`);\n      }\n\n      const response = await axiosInstance.post(`/cavi/${cantiereIdNum}/${cavoId}/metri-posati`, {\n        metri_posati: metriPosati\n      });\n      return response.data;\n    } catch (error) {\n      console.error('Update metri posati error:', error);\n      throw error.response ? error.response.data : error;\n    }\n  },\n\n  // Modifica la bobina di un cavo posato\n  updateBobina: async (cantiereId, cavoId, idBobina) => {\n    try {\n      // Assicurati che cantiereId sia un numero\n      const cantiereIdNum = parseInt(cantiereId, 10);\n      if (isNaN(cantiereIdNum)) {\n        throw new Error(`ID cantiere non valido: ${cantiereId}`);\n      }\n\n      const response = await axiosInstance.post(`/cavi/${cantiereIdNum}/${cavoId}/bobina`, {\n        id_bobina: idBobina\n      });\n      return response.data;\n    } catch (error) {\n      console.error('Update bobina error:', error);\n      throw error.response ? error.response.data : error;\n    }\n  },\n\n  // Ottiene la lista dei cavi installati di un cantiere\n  getCaviInstallati: async (cantiereId) => {\n    try {\n      // Assicurati che cantiereId sia un numero\n      const cantiereIdNum = parseInt(cantiereId, 10);\n      if (isNaN(cantiereIdNum)) {\n        throw new Error(`ID cantiere non valido: ${cantiereId}`);\n      }\n\n      const response = await axiosInstance.get(`/cavi/${cantiereIdNum}/installati`);\n      return response.data;\n    } catch (error) {\n      console.error('Get cavi installati error:', error);\n      throw error.response ? error.response.data : error;\n    }\n  },\n\n  // Ottiene le statistiche dei cavi di un cantiere\n  getCaviStats: async (cantiereId) => {\n    try {\n      // Assicurati che cantiereId sia un numero\n      const cantiereIdNum = parseInt(cantiereId, 10);\n      if (isNaN(cantiereIdNum)) {\n        throw new Error(`ID cantiere non valido: ${cantiereId}`);\n      }\n\n      const response = await axiosInstance.get(`/cavi/${cantiereIdNum}/stats`);\n      return response.data;\n    } catch (error) {\n      console.error('Get cavi stats error:', error);\n      throw error.response ? error.response.data : error;\n    }\n  },\n\n  // Ottiene direttamente i cavi SPARE\n  getCaviSpare: async (cantiereId) => {\n    try {\n      // Assicurati che cantiereId sia un numero\n      const cantiereIdNum = parseInt(cantiereId, 10);\n      if (isNaN(cantiereIdNum)) {\n        throw new Error(`ID cantiere non valido: ${cantiereId}`);\n      }\n\n      console.log('Caricamento cavi SPARE...');\n\n      // Prova prima con l'endpoint standard con tipo_cavo=3\n      console.log('URL API (standard):', `${API_URL}/cavi/${cantiereIdNum}?tipo_cavo=3`);\n\n      try {\n        const response = await axios.get(\n          `${API_URL}/cavi/${cantiereIdNum}`,\n          {\n            headers: {\n              'Content-Type': 'application/json',\n              'Authorization': `Bearer ${localStorage.getItem('token')}`\n            },\n            timeout: 30000,\n            params: { tipo_cavo: 3 }\n          }\n        );\n\n        console.log('Risposta getCaviSpare (standard):', response.data ? response.data.length : 0, 'cavi SPARE trovati');\n        if (response.data && response.data.length > 0) {\n          console.log('Primo cavo SPARE:', response.data[0]);\n        }\n\n        return response.data;\n      } catch (standardError) {\n        console.error('Errore con endpoint standard, tentativo con endpoint dedicato:', standardError);\n\n        // Se fallisce, prova con l'endpoint dedicato\n        console.log('URL API (dedicato):', `${API_URL}/cavi/spare/${cantiereIdNum}`);\n\n        const dedicatedResponse = await axios.get(\n          `${API_URL}/cavi/spare/${cantiereIdNum}`,\n          {\n            headers: {\n              'Content-Type': 'application/json',\n              'Authorization': `Bearer ${localStorage.getItem('token')}`\n            },\n            timeout: 30000\n          }\n        );\n\n        console.log('Risposta getCaviSpare (dedicato):', dedicatedResponse.data ? dedicatedResponse.data.length : 0, 'cavi SPARE trovati');\n        if (dedicatedResponse.data && dedicatedResponse.data.length > 0) {\n          console.log('Primo cavo SPARE (dedicato):', dedicatedResponse.data[0]);\n        }\n\n        return dedicatedResponse.data;\n      }\n    } catch (error) {\n      console.error('Get cavi SPARE error:', error);\n      throw error.response ? error.response.data : error;\n    }\n  },\n\n  // Collega un lato di un cavo\n  collegaCavo: async (cantiereId, cavoId, lato, responsabile) => {\n    try {\n      // Assicurati che cantiereId sia un numero\n      const cantiereIdNum = parseInt(cantiereId, 10);\n      if (isNaN(cantiereIdNum)) {\n        throw new Error(`ID cantiere non valido: ${cantiereId}`);\n      }\n\n      const response = await axiosInstance.post(`/cavi/${cantiereIdNum}/${cavoId}/collegamento`, {\n        lato: lato,\n        responsabile: responsabile || 'cantiere'\n      });\n      return response.data;\n    } catch (error) {\n      console.error('Collega cavo error:', error);\n      throw error.response ? error.response.data : error;\n    }\n  },\n\n  // Scollega un lato di un cavo\n  scollegaCavo: async (cantiereId, cavoId, lato) => {\n    try {\n      // Assicurati che cantiereId sia un numero\n      const cantiereIdNum = parseInt(cantiereId, 10);\n      if (isNaN(cantiereIdNum)) {\n        throw new Error(`ID cantiere non valido: ${cantiereId}`);\n      }\n\n      const response = await axiosInstance.delete(`/cavi/${cantiereIdNum}/${cavoId}/collegamento/${lato}`);\n      return response.data;\n    } catch (error) {\n      console.error('Scollega cavo error:', error);\n      throw error.response ? error.response.data : error;\n    }\n  },\n\n  // Verifica lo stato di un cavo specifico (debug)\n  debugCavo: async (cantiereId, cavoId) => {\n    try {\n      // Assicurati che cantiereId sia un numero\n      const cantiereIdNum = parseInt(cantiereId, 10);\n      if (isNaN(cantiereIdNum)) {\n        throw new Error(`ID cantiere non valido: ${cantiereId}`);\n      }\n\n      // Ottieni i cavi attivi\n      console.log('Verificando cavo tra i cavi attivi...');\n      const attivi = await axiosInstance.get(`/cavi/${cantiereIdNum}?tipo_cavo=0`);\n      const cavoAttivo = attivi.data.find(c => c.id_cavo === cavoId);\n\n      // Ottieni i cavi SPARE\n      console.log('Verificando cavo tra i cavi SPARE...');\n      const spare = await axiosInstance.get(`/cavi/${cantiereIdNum}?tipo_cavo=3`);\n      const cavoSpare = spare.data.find(c => c.id_cavo === cavoId);\n\n      return {\n        trovato_tra_attivi: !!cavoAttivo,\n        trovato_tra_spare: !!cavoSpare,\n        cavo_attivo: cavoAttivo,\n        cavo_spare: cavoSpare\n      };\n    } catch (error) {\n      console.error('Debug cavo error:', error);\n      throw error.response ? error.response.data : error;\n    }\n  }\n};\n\nexport default caviService;\n"], "mappings": "AAAA,OAAOA,KAAK,MAAM,OAAO;AACzB,OAAOC,MAAM,MAAM,WAAW;AAE9B,MAAMC,OAAO,GAAGD,MAAM,CAACC,OAAO;;AAE9B;AACA,MAAMC,aAAa,GAAGH,KAAK,CAACI,MAAM,CAAC;EACjCC,OAAO,EAAEH,OAAO;EAChBI,OAAO,EAAE;IACP,cAAc,EAAE;EAClB,CAAC;EACDC,OAAO,EAAE,KAAK;EAAE;EAChBC,eAAe,EAAE,KAAK,CAAC;AACzB,CAAC,CAAC;;AAEF;AACAL,aAAa,CAACM,YAAY,CAACC,OAAO,CAACC,GAAG,CACnCV,MAAM,IAAK;EACV,MAAMW,KAAK,GAAGC,YAAY,CAACC,OAAO,CAAC,OAAO,CAAC;EAC3C,IAAIF,KAAK,EAAE;IACTX,MAAM,CAACK,OAAO,CAACS,aAAa,GAAG,UAAUH,KAAK,EAAE;EAClD;EACA,OAAOX,MAAM;AACf,CAAC,EACAe,KAAK,IAAK;EACT,OAAOC,OAAO,CAACC,MAAM,CAACF,KAAK,CAAC;AAC9B,CACF,CAAC;AAED,MAAMG,WAAW,GAAG;EAClB;EACAC,OAAO,EAAE,MAAAA,CAAOC,UAAU,EAAEC,QAAQ,GAAG,IAAI,EAAEC,OAAO,GAAG,CAAC,CAAC,KAAK;IAC5D,IAAI;MACFC,OAAO,CAACC,GAAG,CAAC,uBAAuB,EAAE;QAAEJ,UAAU;QAAEC,QAAQ;QAAEC;MAAQ,CAAC,CAAC;MACvEC,OAAO,CAACC,GAAG,CAAC,qBAAqB,EAAE,OAAOJ,UAAU,CAAC;;MAErD;MACA,IAAIA,UAAU,KAAKK,SAAS,IAAIL,UAAU,KAAK,IAAI,EAAE;QACnDG,OAAO,CAACR,KAAK,CAAC,+BAA+B,CAAC;QAC9C,MAAM,IAAIW,KAAK,CAAC,sBAAsB,CAAC;MACzC;;MAEA;MACA,IAAIC,aAAa,GAAGP,UAAU;MAC9B,IAAI,OAAOA,UAAU,KAAK,QAAQ,EAAE;QAClCO,aAAa,GAAGC,QAAQ,CAACR,UAAU,EAAE,EAAE,CAAC;QACxCG,OAAO,CAACC,GAAG,CAAC,4CAA4C,EAAEG,aAAa,CAAC;MAC1E;MAEA,IAAIE,KAAK,CAACF,aAAa,CAAC,EAAE;QACxBJ,OAAO,CAACR,KAAK,CAAC,qCAAqC,EAAEK,UAAU,CAAC;QAChE,MAAM,IAAIM,KAAK,CAAC,2BAA2BN,UAAU,EAAE,CAAC;MAC1D;;MAEA;MACAG,OAAO,CAACC,GAAG,CAAC,iCAAiCG,aAAa,kBAAkBN,QAAQ,EAAE,CAAC;;MAEvF;MACA,IAAIA,QAAQ,KAAK,CAAC,EAAE;QAClBE,OAAO,CAACC,GAAG,CAAC,6CAA6C,CAAC;QAC1D,IAAI;UACF;UACA,MAAMM,QAAQ,GAAG,MAAM/B,KAAK,CAACgC,GAAG,CAC9B,GAAG9B,OAAO,eAAe0B,aAAa,EAAE,EACxC;YACEtB,OAAO,EAAE;cACP,cAAc,EAAE,kBAAkB;cAClC,eAAe,EAAE,UAAUO,YAAY,CAACC,OAAO,CAAC,OAAO,CAAC;YAC1D,CAAC;YACDP,OAAO,EAAE;UACX,CACF,CAAC;UAEDiB,OAAO,CAACC,GAAG,CAAC,sBAAsB,EAAEM,QAAQ,CAACE,IAAI,CAAC;UAClD,OAAOF,QAAQ,CAACE,IAAI;QACtB,CAAC,CAAC,OAAOC,UAAU,EAAE;UACnBV,OAAO,CAACR,KAAK,CAAC,wCAAwC,EAAEkB,UAAU,CAAC;UACnE;QACF;MACF;;MAEA;MACA,IAAIC,GAAG,GAAG,SAASP,aAAa,EAAE;MAClC,MAAMQ,WAAW,GAAG,EAAE;MAEtB,IAAId,QAAQ,KAAK,IAAI,EAAE;QACrBc,WAAW,CAACC,IAAI,CAAC,aAAaf,QAAQ,EAAE,CAAC;MAC3C;;MAEA;MACA,IAAIC,OAAO,CAACe,mBAAmB,EAAE;QAC/BF,WAAW,CAACC,IAAI,CAAC,uBAAuBE,kBAAkB,CAAChB,OAAO,CAACe,mBAAmB,CAAC,EAAE,CAAC;MAC5F;MAEA,IAAIf,OAAO,CAACiB,SAAS,EAAE;QACrBJ,WAAW,CAACC,IAAI,CAAC,aAAaE,kBAAkB,CAAChB,OAAO,CAACiB,SAAS,CAAC,EAAE,CAAC;MACxE;MAEA,IAAIjB,OAAO,CAACkB,OAAO,EAAE;QACnBL,WAAW,CAACC,IAAI,CAAC,WAAWE,kBAAkB,CAAChB,OAAO,CAACkB,OAAO,CAAC,EAAE,CAAC;QAClE,IAAIlB,OAAO,CAACmB,UAAU,EAAE;UACtBN,WAAW,CAACC,IAAI,CAAC,cAAcE,kBAAkB,CAAChB,OAAO,CAACmB,UAAU,CAAC,EAAE,CAAC;QAC1E;MACF;;MAEA;MACA,IAAIN,WAAW,CAACO,MAAM,GAAG,CAAC,EAAE;QAC1BR,GAAG,IAAI,IAAIC,WAAW,CAACQ,IAAI,CAAC,GAAG,CAAC,EAAE;MACpC;;MAEA;MACApB,OAAO,CAACC,GAAG,CAAC,mBAAmB,EAAEU,GAAG,CAAC;MACrCX,OAAO,CAACC,GAAG,CAAC,qBAAqB,EAAEW,WAAW,CAAC;MAE/CZ,OAAO,CAACC,GAAG,CAAC,qBAAqBU,GAAG,EAAE,CAAC;MACvCX,OAAO,CAACC,GAAG,CAAC,QAAQ,EAAEZ,YAAY,CAACC,OAAO,CAAC,OAAO,CAAC,GAAG,UAAU,GAAG,UAAU,CAAC;MAC9EU,OAAO,CAACC,GAAG,CAAC,eAAe,EAAE,GAAGvB,OAAO,GAAGiC,GAAG,EAAE,CAAC;MAEhD,IAAI;QACFX,OAAO,CAACC,GAAG,CAAC,kCAAkCU,GAAG,eAAetB,YAAY,CAACC,OAAO,CAAC,OAAO,CAAC,GAAG,UAAU,GAAG,UAAU,EAAE,CAAC;QAC1HU,OAAO,CAACC,GAAG,CAAC,0BAA0B,EAAE;UACtC,cAAc,EAAE,kBAAkB;UAClC,eAAe,EAAE,UAAUZ,YAAY,CAACC,OAAO,CAAC,OAAO,CAAC;QAC1D,CAAC,CAAC;;QAEF;QACA,MAAMiB,QAAQ,GAAG,MAAM5B,aAAa,CAAC6B,GAAG,CAACG,GAAG,EAAE;UAAE5B,OAAO,EAAE;QAAM,CAAC,CAAC;QAEjEiB,OAAO,CAACC,GAAG,CAAC,iBAAiBU,GAAG,EAAE,EAAEJ,QAAQ,CAACE,IAAI,CAAC;QAClDT,OAAO,CAACC,GAAG,CAAC,wBAAwB,EAAEM,QAAQ,CAACc,MAAM,CAAC;QACtDrB,OAAO,CAACC,GAAG,CAAC,yBAAyB,EAAEM,QAAQ,CAACzB,OAAO,CAAC;QAExD,IAAIwC,KAAK,CAACC,OAAO,CAAChB,QAAQ,CAACE,IAAI,CAAC,EAAE;UAChCT,OAAO,CAACC,GAAG,CAAC,4BAA4BM,QAAQ,CAACE,IAAI,CAACU,MAAM,EAAE,CAAC;UAC/D,IAAIZ,QAAQ,CAACE,IAAI,CAACU,MAAM,GAAG,CAAC,EAAE;YAC5BnB,OAAO,CAACC,GAAG,CAAC,sBAAsB,EAAEM,QAAQ,CAACE,IAAI,CAAC,CAAC,CAAC,CAAC;UACvD,CAAC,MAAM;YACLT,OAAO,CAACwB,IAAI,CAAC,uCAAuCpB,aAAa,aAAaN,QAAQ,EAAE,CAAC;UAC3F;QACF,CAAC,MAAM;UACLE,OAAO,CAACwB,IAAI,CAAC,4BAA4B,OAAOjB,QAAQ,CAACE,IAAI,EAAE,EAAEF,QAAQ,CAACE,IAAI,CAAC;QACjF;QAEA,OAAOF,QAAQ,CAACE,IAAI;MACtB,CAAC,CAAC,OAAOgB,QAAQ,EAAE;QAAA,IAAAC,kBAAA,EAAAC,mBAAA,EAAAC,mBAAA,EAAAC,mBAAA;QACjB7B,OAAO,CAACR,KAAK,CAAC,iCAAiCmB,GAAG,GAAG,EAAEc,QAAQ,CAAC;QAChEzB,OAAO,CAACR,KAAK,CAAC,sBAAsB,EAAE;UACpCsC,OAAO,EAAEL,QAAQ,CAACK,OAAO;UACzBT,MAAM,GAAAK,kBAAA,GAAED,QAAQ,CAAClB,QAAQ,cAAAmB,kBAAA,uBAAjBA,kBAAA,CAAmBL,MAAM;UACjCU,UAAU,GAAAJ,mBAAA,GAAEF,QAAQ,CAAClB,QAAQ,cAAAoB,mBAAA,uBAAjBA,mBAAA,CAAmBI,UAAU;UACzCtB,IAAI,GAAAmB,mBAAA,GAAEH,QAAQ,CAAClB,QAAQ,cAAAqB,mBAAA,uBAAjBA,mBAAA,CAAmBnB,IAAI;UAC7B3B,OAAO,GAAA+C,mBAAA,GAAEJ,QAAQ,CAAClB,QAAQ,cAAAsB,mBAAA,uBAAjBA,mBAAA,CAAmB/C,OAAO;UACnCkD,IAAI,EAAEP,QAAQ,CAACO,IAAI;UACnBC,YAAY,EAAER,QAAQ,CAACQ,YAAY;UACnCxD,MAAM,EAAEgD,QAAQ,CAAChD,MAAM,GAAG;YACxBkC,GAAG,EAAEc,QAAQ,CAAChD,MAAM,CAACkC,GAAG;YACxBuB,MAAM,EAAET,QAAQ,CAAChD,MAAM,CAACyD,MAAM;YAC9BnD,OAAO,EAAE0C,QAAQ,CAAChD,MAAM,CAACM,OAAO;YAChCD,OAAO,EAAE2C,QAAQ,CAAChD,MAAM,CAACK;UAC3B,CAAC,GAAG;QACN,CAAC,CAAC;;QAEF;QACA,IAAI2C,QAAQ,CAACO,IAAI,KAAK,aAAa,EAAE;UACnChC,OAAO,CAACR,KAAK,CAAC,0EAA0E,CAAC;UACzF;UACA,IAAI;YACFQ,OAAO,CAACC,GAAG,CAAC,gDAAgD,CAAC;YAC7D,MAAMkC,YAAY,GAAG,MAAMC,KAAK,CAAC1D,OAAO,CAAC;YACzCsB,OAAO,CAACC,GAAG,CAAC,iCAAiC,EAAEkC,YAAY,CAACd,MAAM,CAAC;UACrE,CAAC,CAAC,OAAOgB,SAAS,EAAE;YAClBrC,OAAO,CAACR,KAAK,CAAC,yCAAyC,EAAE6C,SAAS,CAAC;UACrE;QACF;QAEA,MAAMZ,QAAQ;MAChB;IACF,CAAC,CAAC,OAAOjC,KAAK,EAAE;MAAA,IAAA8C,eAAA,EAAAC,gBAAA,EAAAC,gBAAA,EAAAC,gBAAA,EAAAC,qBAAA,EAAAC,gBAAA,EAAAC,gBAAA;MACd5C,OAAO,CAACR,KAAK,CAAC,iBAAiB,EAAEA,KAAK,CAAC;MACvCQ,OAAO,CAACR,KAAK,CAAC,gBAAgB,EAAE;QAC9BsC,OAAO,EAAEtC,KAAK,CAACsC,OAAO;QACtBT,MAAM,GAAAiB,eAAA,GAAE9C,KAAK,CAACe,QAAQ,cAAA+B,eAAA,uBAAdA,eAAA,CAAgBjB,MAAM;QAC9BU,UAAU,GAAAQ,gBAAA,GAAE/C,KAAK,CAACe,QAAQ,cAAAgC,gBAAA,uBAAdA,gBAAA,CAAgBR,UAAU;QACtCtB,IAAI,GAAA+B,gBAAA,GAAEhD,KAAK,CAACe,QAAQ,cAAAiC,gBAAA,uBAAdA,gBAAA,CAAgB/B,IAAI;QAC1BE,GAAG,EAAE,SAASd,UAAU,GAAGC,QAAQ,KAAK,IAAI,GAAG,cAAcA,QAAQ,EAAE,GAAG,EAAE,EAAE;QAC9E+C,KAAK,EAAErD,KAAK,CAACqD;MACf,CAAC,CAAC;;MAEF;MACA,IAAIrD,KAAK,CAACwC,IAAI,KAAK,cAAc,IAAIxC,KAAK,CAACsC,OAAO,CAACgB,QAAQ,CAAC,SAAS,CAAC,IAAItD,KAAK,CAACsC,OAAO,CAACgB,QAAQ,CAAC,eAAe,CAAC,EAAE;QACjH9C,OAAO,CAACR,KAAK,CAAC,iCAAiC,CAAC;QAChD;QACAQ,OAAO,CAACC,GAAG,CAAC,mCAAmC,CAAC;QAChD,OAAO,EAAE;MACX;;MAEA;MACA,MAAM8C,aAAa,GAAG,IAAI5C,KAAK,CAAC,EAAAsC,gBAAA,GAAAjD,KAAK,CAACe,QAAQ,cAAAkC,gBAAA,wBAAAC,qBAAA,GAAdD,gBAAA,CAAgBhC,IAAI,cAAAiC,qBAAA,uBAApBA,qBAAA,CAAsBM,MAAM,KAAIxD,KAAK,CAACsC,OAAO,IAAI,oBAAoB,CAAC;MACtGiB,aAAa,CAAC1B,MAAM,IAAAsB,gBAAA,GAAGnD,KAAK,CAACe,QAAQ,cAAAoC,gBAAA,uBAAdA,gBAAA,CAAgBtB,MAAM;MAC7C0B,aAAa,CAACtC,IAAI,IAAAmC,gBAAA,GAAGpD,KAAK,CAACe,QAAQ,cAAAqC,gBAAA,uBAAdA,gBAAA,CAAgBnC,IAAI;MACzCsC,aAAa,CAACxC,QAAQ,GAAGf,KAAK,CAACe,QAAQ;MACvCwC,aAAa,CAACE,aAAa,GAAGzD,KAAK;MACnCuD,aAAa,CAACf,IAAI,GAAGxC,KAAK,CAACwC,IAAI;MAC/Be,aAAa,CAACd,YAAY,GAAGzC,KAAK,CAACyC,YAAY;MAE/C,MAAMc,aAAa;IACrB;EACF,CAAC;EAED;EACAG,UAAU,EAAE,MAAAA,CAAOrD,UAAU,EAAEsD,QAAQ,KAAK;IAC1C,IAAI;MACF;MACA,MAAM/C,aAAa,GAAGC,QAAQ,CAACR,UAAU,EAAE,EAAE,CAAC;MAC9C,IAAIS,KAAK,CAACF,aAAa,CAAC,EAAE;QACxB,MAAM,IAAID,KAAK,CAAC,2BAA2BN,UAAU,EAAE,CAAC;MAC1D;MAEAG,OAAO,CAACC,GAAG,CAAC,4CAA4CG,aAAa,EAAE,CAAC;MACxEJ,OAAO,CAACC,GAAG,CAAC,eAAe,EAAEmD,IAAI,CAACC,SAAS,CAACF,QAAQ,EAAE,IAAI,EAAE,CAAC,CAAC,CAAC;;MAE/D;MACA,IAAI;QACFnD,OAAO,CAACC,GAAG,CAAC,oCAAoC,CAAC;QACjD,MAAMqD,YAAY,GAAG,MAAMlB,KAAK,CAAC,GAAG1D,OAAO,OAAO,EAAE;UAAEwD,MAAM,EAAE;QAAM,CAAC,CAAC;QACtElC,OAAO,CAACC,GAAG,CAAC,kBAAkB,EAAEqD,YAAY,CAACjC,MAAM,EAAEiC,YAAY,CAACvB,UAAU,CAAC;MAC/E,CAAC,CAAC,OAAOwB,SAAS,EAAE;QAClBvD,OAAO,CAACR,KAAK,CAAC,oCAAoC,EAAE+D,SAAS,CAAC;QAC9DvD,OAAO,CAACC,GAAG,CAAC,qCAAqC,CAAC;MACpD;;MAEA;MACAD,OAAO,CAACC,GAAG,CAAC,8BAA8B,CAAC;MAC3C,MAAMb,KAAK,GAAGC,YAAY,CAACC,OAAO,CAAC,OAAO,CAAC;MAC3CU,OAAO,CAACC,GAAG,CAAC,mCAAmC,EAAE,CAAC,CAACb,KAAK,CAAC;;MAEzD;MACA,MAAMmB,QAAQ,GAAG,MAAM/B,KAAK,CAAC;QAC3B0D,MAAM,EAAE,MAAM;QACdvB,GAAG,EAAE,GAAGjC,OAAO,SAAS0B,aAAa,EAAE;QACvCK,IAAI,EAAE0C,QAAQ;QACdrE,OAAO,EAAE;UACP,cAAc,EAAE,kBAAkB;UAClC,eAAe,EAAE,UAAUM,KAAK;QAClC,CAAC;QACDL,OAAO,EAAE,KAAK;QAAE;QAChBC,eAAe,EAAE,KAAK,CAAC;MACzB,CAAC,CAAC;MAEFgB,OAAO,CAACC,GAAG,CAAC,sBAAsB,EAAEM,QAAQ,CAACc,MAAM,EAAEd,QAAQ,CAACwB,UAAU,CAAC;MACzE/B,OAAO,CAACC,GAAG,CAAC,gBAAgB,EAAEM,QAAQ,CAACE,IAAI,CAAC;MAC5C,OAAOF,QAAQ,CAACE,IAAI;IACtB,CAAC,CAAC,OAAOjB,KAAK,EAAE;MACdQ,OAAO,CAACR,KAAK,CAAC,oBAAoB,EAAEA,KAAK,CAAC;;MAE1C;MACA,IAAIA,KAAK,CAACe,QAAQ,EAAE;QAClB;QACAP,OAAO,CAACR,KAAK,CAAC,oBAAoB,EAAEA,KAAK,CAACe,QAAQ,CAACc,MAAM,EAAE7B,KAAK,CAACe,QAAQ,CAACwB,UAAU,CAAC;QACrF/B,OAAO,CAACR,KAAK,CAAC,cAAc,EAAEA,KAAK,CAACe,QAAQ,CAACE,IAAI,CAAC;QAClD,MAAMjB,KAAK,CAACe,QAAQ,CAACE,IAAI;MAC3B,CAAC,MAAM,IAAIjB,KAAK,CAACN,OAAO,EAAE;QACxB;QACAc,OAAO,CAACR,KAAK,CAAC,8BAA8B,EAAEA,KAAK,CAACN,OAAO,CAAC;;QAE5D;QACAc,OAAO,CAACC,GAAG,CAAC,iCAAiC,CAAC;QAC9C,OAAO,IAAIR,OAAO,CAAC,CAAC+D,OAAO,EAAE9D,MAAM,KAAK;UACtC,MAAM+D,GAAG,GAAG,IAAIC,cAAc,CAAC,CAAC;UAChCD,GAAG,CAACE,IAAI,CAAC,MAAM,EAAE,GAAGjF,OAAO,SAASmB,UAAU,EAAE,EAAE,IAAI,CAAC;UACvD4D,GAAG,CAACG,gBAAgB,CAAC,cAAc,EAAE,kBAAkB,CAAC;;UAExD;UACA,MAAMC,SAAS,GAAGxE,YAAY,CAACC,OAAO,CAAC,OAAO,CAAC;UAC/C,IAAIuE,SAAS,EAAE;YACbJ,GAAG,CAACG,gBAAgB,CAAC,eAAe,EAAE,UAAUC,SAAS,EAAE,CAAC;UAC9D;UACAJ,GAAG,CAAC1E,OAAO,GAAG,KAAK,CAAC,CAAC;;UAErB0E,GAAG,CAACK,MAAM,GAAG,YAAW;YACtB,IAAIL,GAAG,CAACpC,MAAM,IAAI,GAAG,IAAIoC,GAAG,CAACpC,MAAM,GAAG,GAAG,EAAE;cACzCrB,OAAO,CAACC,GAAG,CAAC,0BAA0B,EAAEwD,GAAG,CAACM,YAAY,CAAC;cACzD,IAAI;gBACF,MAAMtD,IAAI,GAAG2C,IAAI,CAACY,KAAK,CAACP,GAAG,CAACM,YAAY,CAAC;gBACzCP,OAAO,CAAC/C,IAAI,CAAC;cACf,CAAC,CAAC,OAAOwD,CAAC,EAAE;gBACVjE,OAAO,CAACwB,IAAI,CAAC,oCAAoC,EAAEyC,CAAC,CAAC;gBACrDT,OAAO,CAAC;kBAAE1B,OAAO,EAAE;gBAAqC,CAAC,CAAC;cAC5D;YACF,CAAC,MAAM;cACL9B,OAAO,CAACR,KAAK,CAAC,wBAAwB,EAAEiE,GAAG,CAACpC,MAAM,EAAEoC,GAAG,CAAC1B,UAAU,CAAC;cACnErC,MAAM,CAAC,IAAIS,KAAK,CAAC,UAAUsD,GAAG,CAACpC,MAAM,KAAKoC,GAAG,CAAC1B,UAAU,EAAE,CAAC,CAAC;YAC9D;UACF,CAAC;UAED0B,GAAG,CAACS,OAAO,GAAG,YAAW;YACvBlE,OAAO,CAACR,KAAK,CAAC,+BAA+B,CAAC;YAC9CE,MAAM,CAAC,IAAIS,KAAK,CAAC,oDAAoD,CAAC,CAAC;UACzE,CAAC;UAEDsD,GAAG,CAACU,SAAS,GAAG,YAAW;YACzBnE,OAAO,CAACR,KAAK,CAAC,wBAAwB,CAAC;YACvCE,MAAM,CAAC,IAAIS,KAAK,CAAC,kDAAkD,CAAC,CAAC;UACvE,CAAC;UAEDsD,GAAG,CAACW,IAAI,CAAChB,IAAI,CAACC,SAAS,CAACF,QAAQ,CAAC,CAAC;QACpC,CAAC,CAAC;MACJ,CAAC,MAAM;QACL;QACAnD,OAAO,CAACR,KAAK,CAAC,iDAAiD,EAAEA,KAAK,CAACsC,OAAO,CAAC;QAC/E,MAAM,IAAI3B,KAAK,CAAC,0BAA0BX,KAAK,CAACsC,OAAO,EAAE,CAAC;MAC5D;IACF;EACF,CAAC;EAED;EACAuC,WAAW,EAAE,MAAAA,CAAOxE,UAAU,EAAEyE,MAAM,KAAK;IACzC,IAAI;MACF;MACA,MAAMlE,aAAa,GAAGC,QAAQ,CAACR,UAAU,EAAE,EAAE,CAAC;MAC9C,IAAIS,KAAK,CAACF,aAAa,CAAC,EAAE;QACxB,MAAM,IAAID,KAAK,CAAC,2BAA2BN,UAAU,EAAE,CAAC;MAC1D;MAEA,MAAMU,QAAQ,GAAG,MAAM5B,aAAa,CAAC6B,GAAG,CAAC,SAASJ,aAAa,IAAIkE,MAAM,EAAE,CAAC;MAC5E,OAAO/D,QAAQ,CAACE,IAAI;IACtB,CAAC,CAAC,OAAOjB,KAAK,EAAE;MACdQ,OAAO,CAACR,KAAK,CAAC,uBAAuB,EAAEA,KAAK,CAAC;MAC7C,MAAMA,KAAK,CAACe,QAAQ,GAAGf,KAAK,CAACe,QAAQ,CAACE,IAAI,GAAGjB,KAAK;IACpD;EACF,CAAC;EAED;EACA+E,UAAU,EAAE,MAAAA,CAAO1E,UAAU,EAAEyE,MAAM,EAAEnB,QAAQ,KAAK;IAClD,IAAI;MACF;MACA,MAAM/C,aAAa,GAAGC,QAAQ,CAACR,UAAU,EAAE,EAAE,CAAC;MAC9C,IAAIS,KAAK,CAACF,aAAa,CAAC,EAAE;QACxB,MAAM,IAAID,KAAK,CAAC,2BAA2BN,UAAU,EAAE,CAAC;MAC1D;MAEAG,OAAO,CAACC,GAAG,CAAC,kCAAkCG,aAAa,IAAIkE,MAAM,EAAE,CAAC;MACxEtE,OAAO,CAACC,GAAG,CAAC,eAAe,EAAEkD,QAAQ,CAAC;;MAEtC;MACA,MAAM5C,QAAQ,GAAG,MAAM5B,aAAa,CAAC6F,GAAG,CAAC,SAASpE,aAAa,IAAIkE,MAAM,EAAE,EAAEnB,QAAQ,EAAE;QACrFpE,OAAO,EAAE,KAAK,CAAE;MAClB,CAAC,CAAC;MAEFiB,OAAO,CAACC,GAAG,CAAC,oBAAoB,EAAEM,QAAQ,CAACE,IAAI,CAAC;MAChD,OAAOF,QAAQ,CAACE,IAAI;IACtB,CAAC,CAAC,OAAOjB,KAAK,EAAE;MACdQ,OAAO,CAACR,KAAK,CAAC,oBAAoB,EAAEA,KAAK,CAAC;;MAE1C;MACA,IAAIA,KAAK,CAACe,QAAQ,EAAE;QAClB;QACAP,OAAO,CAACR,KAAK,CAAC,oBAAoB,EAAEA,KAAK,CAACe,QAAQ,CAACc,MAAM,EAAE7B,KAAK,CAACe,QAAQ,CAACwB,UAAU,CAAC;QACrF/B,OAAO,CAACR,KAAK,CAAC,cAAc,EAAEA,KAAK,CAACe,QAAQ,CAACE,IAAI,CAAC;QAClD,MAAMjB,KAAK,CAACe,QAAQ,CAACE,IAAI;MAC3B,CAAC,MAAM,IAAIjB,KAAK,CAACN,OAAO,EAAE;QACxB;QACAc,OAAO,CAACR,KAAK,CAAC,8BAA8B,EAAEA,KAAK,CAACN,OAAO,CAAC;QAC5D,MAAM,IAAIiB,KAAK,CAAC,kFAAkF,CAAC;MACrG,CAAC,MAAM;QACL;QACAH,OAAO,CAACR,KAAK,CAAC,iDAAiD,EAAEA,KAAK,CAACsC,OAAO,CAAC;QAC/E,MAAMtC,KAAK;MACb;IACF;EACF,CAAC;EAED;EACAiF,oBAAoB,EAAE,MAAO5E,UAAU,IAAK;IAC1C,IAAI;MACF;MACA,MAAMO,aAAa,GAAGC,QAAQ,CAACR,UAAU,EAAE,EAAE,CAAC;MAC9C,IAAIS,KAAK,CAACF,aAAa,CAAC,EAAE;QACxB,MAAM,IAAID,KAAK,CAAC,2BAA2BN,UAAU,EAAE,CAAC;MAC1D;MAEA,MAAMU,QAAQ,GAAG,MAAM5B,aAAa,CAAC6B,GAAG,CAAC,SAASJ,aAAa,qBAAqB,CAAC;MACrF,OAAOG,QAAQ,CAACE,IAAI,CAACiE,kBAAkB;IACzC,CAAC,CAAC,OAAOlF,KAAK,EAAE;MACdQ,OAAO,CAACR,KAAK,CAAC,+BAA+B,EAAEA,KAAK,CAAC;MACrD,OAAO,IAAI,CAAC,CAAC;IACf;EACF,CAAC;EAED;EACAmF,eAAe,EAAE,MAAAA,CAAO9E,UAAU,EAAEyE,MAAM,EAAEM,KAAK,GAAG,KAAK,KAAK;IAC5D,IAAI;MACF;MACA,MAAMxE,aAAa,GAAGC,QAAQ,CAACR,UAAU,EAAE,EAAE,CAAC;MAC9C,IAAIS,KAAK,CAACF,aAAa,CAAC,EAAE;QACxB,MAAM,IAAID,KAAK,CAAC,2BAA2BN,UAAU,EAAE,CAAC;MAC1D;;MAEA;MACAG,OAAO,CAACC,GAAG,CAAC,uCAAuC,EAAE;QAAEJ,UAAU,EAAEO,aAAa;QAAEkE,MAAM;QAAEM;MAAM,CAAC,CAAC;;MAElG;MACA5E,OAAO,CAACC,GAAG,CAAC,iBAAiB,EAAE,GAAGvB,OAAO,SAAS0B,aAAa,IAAIkE,MAAM,gBAAgB,CAAC;MAE1F,IAAI;QACF,MAAMO,YAAY,GAAG,MAAMrG,KAAK,CAACsG,IAAI,CACnC,GAAGpG,OAAO,SAAS0B,aAAa,IAAIkE,MAAM,gBAAgB,EAC1D;UAAEM,KAAK,EAAEA;QAAM,CAAC,EAChB;UACE9F,OAAO,EAAE;YACP,cAAc,EAAE,kBAAkB;YAClC,eAAe,EAAE,UAAUO,YAAY,CAACC,OAAO,CAAC,OAAO,CAAC;UAC1D,CAAC;UACDP,OAAO,EAAE;QACX,CACF,CAAC;QAEDiB,OAAO,CAACC,GAAG,CAAC,kCAAkC,EAAE4E,YAAY,CAACpE,IAAI,CAAC;;QAElE;QACA,MAAM,IAAIhB,OAAO,CAAC+D,OAAO,IAAIuB,UAAU,CAACvB,OAAO,EAAE,IAAI,CAAC,CAAC;;QAEvD;QACAxD,OAAO,CAACC,GAAG,CAAC,uDAAuD,CAAC;QACpE,MAAM+E,YAAY,GAAG,MAAMxG,KAAK,CAACgC,GAAG,CAClC,GAAG9B,OAAO,SAAS0B,aAAa,IAAIkE,MAAM,EAAE,EAC5C;UACExF,OAAO,EAAE;YACP,cAAc,EAAE,kBAAkB;YAClC,eAAe,EAAE,UAAUO,YAAY,CAACC,OAAO,CAAC,OAAO,CAAC;UAC1D,CAAC;UACDP,OAAO,EAAE;QACX,CACF,CAAC;QAEDiB,OAAO,CAACC,GAAG,CAAC,gCAAgC,EAAE+E,YAAY,CAACvE,IAAI,CAAC;;QAEhE;QACA,IAAIuE,YAAY,CAACvE,IAAI,CAACwE,sBAAsB,KAAK,CAAC,EAAE;UAClDjF,OAAO,CAACR,KAAK,CAAC,8EAA8E,CAAC;UAC7F,MAAM,IAAIW,KAAK,CAAC,wCAAwC,CAAC;QAC3D;QAEA,OAAO6E,YAAY,CAACvE,IAAI;MAC1B,CAAC,CAAC,OAAOyE,SAAS,EAAE;QAClB;QACAlF,OAAO,CAACR,KAAK,CAAC,4DAA4D,EAAE0F,SAAS,CAAC;QACtFlF,OAAO,CAACC,GAAG,CAAC,mBAAmB,EAAE,GAAGvB,OAAO,SAAS0B,aAAa,IAAIkE,MAAM,aAAa,CAAC;QAEzF,MAAMa,cAAc,GAAG,MAAM3G,KAAK,CAAC4G,MAAM,CACvC,GAAG1G,OAAO,SAAS0B,aAAa,IAAIkE,MAAM,EAAE,EAC5C;UACExF,OAAO,EAAE;YACP,cAAc,EAAE,kBAAkB;YAClC,eAAe,EAAE,UAAUO,YAAY,CAACC,OAAO,CAAC,OAAO,CAAC;UAC1D,CAAC;UACDP,OAAO,EAAE,KAAK;UACdsG,MAAM,EAAE;YAAEC,IAAI,EAAE;UAAQ;QAC1B,CACF,CAAC;QAEDtF,OAAO,CAACC,GAAG,CAAC,+CAA+C,EAAEkF,cAAc,CAAC1E,IAAI,CAAC;;QAEjF;QACA,MAAM,IAAIhB,OAAO,CAAC+D,OAAO,IAAIuB,UAAU,CAACvB,OAAO,EAAE,IAAI,CAAC,CAAC;;QAEvD;QACAxD,OAAO,CAACC,GAAG,CAAC,kEAAkE,CAAC;QAC/E,MAAM+E,YAAY,GAAG,MAAMxG,KAAK,CAACgC,GAAG,CAClC,GAAG9B,OAAO,SAAS0B,aAAa,IAAIkE,MAAM,EAAE,EAC5C;UACExF,OAAO,EAAE;YACP,cAAc,EAAE,kBAAkB;YAClC,eAAe,EAAE,UAAUO,YAAY,CAACC,OAAO,CAAC,OAAO,CAAC;UAC1D,CAAC;UACDP,OAAO,EAAE;QACX,CACF,CAAC;QAEDiB,OAAO,CAACC,GAAG,CAAC,2CAA2C,EAAE+E,YAAY,CAACvE,IAAI,CAAC;;QAE3E;QACA,IAAIuE,YAAY,CAACvE,IAAI,CAACwE,sBAAsB,KAAK,CAAC,EAAE;UAClDjF,OAAO,CAACR,KAAK,CAAC,8EAA8E,CAAC;UAC7F,MAAM,IAAIW,KAAK,CAAC,wCAAwC,CAAC;QAC3D;QAEA,OAAO6E,YAAY,CAACvE,IAAI;MAC1B;IACF,CAAC,CAAC,OAAOjB,KAAK,EAAE;MAAA,IAAA+F,gBAAA,EAAAC,gBAAA,EAAAC,gBAAA;MACdzF,OAAO,CAACR,KAAK,CAAC,2BAA2B,EAAEA,KAAK,CAAC;MACjDQ,OAAO,CAACR,KAAK,CAAC,kBAAkB,EAAE;QAChCsC,OAAO,EAAEtC,KAAK,CAACsC,OAAO;QACtBT,MAAM,GAAAkE,gBAAA,GAAE/F,KAAK,CAACe,QAAQ,cAAAgF,gBAAA,uBAAdA,gBAAA,CAAgBlE,MAAM;QAC9BU,UAAU,GAAAyD,gBAAA,GAAEhG,KAAK,CAACe,QAAQ,cAAAiF,gBAAA,uBAAdA,gBAAA,CAAgBzD,UAAU;QACtCtB,IAAI,GAAAgF,gBAAA,GAAEjG,KAAK,CAACe,QAAQ,cAAAkF,gBAAA,uBAAdA,gBAAA,CAAgBhF,IAAI;QAC1BE,GAAG,EAAE,GAAGjC,OAAO,SAASmB,UAAU,IAAIyE,MAAM,EAAE;QAC9C7F,MAAM,EAAEe,KAAK,CAACf;MAChB,CAAC,CAAC;MACF,MAAMe,KAAK,CAACe,QAAQ,GAAGf,KAAK,CAACe,QAAQ,CAACE,IAAI,GAAGjB,KAAK;IACpD;EACF,CAAC;EAED;EACAkG,UAAU,EAAE,MAAAA,CAAO7F,UAAU,EAAEyE,MAAM,EAAEgB,IAAI,GAAG,IAAI,KAAK;IACrD,IAAI;MACF;MACA,MAAMlF,aAAa,GAAGC,QAAQ,CAACR,UAAU,EAAE,EAAE,CAAC;MAC9C,IAAIS,KAAK,CAACF,aAAa,CAAC,EAAE;QACxB,MAAM,IAAID,KAAK,CAAC,2BAA2BN,UAAU,EAAE,CAAC;MAC1D;;MAEA;MACAG,OAAO,CAACC,GAAG,CAAC,sCAAsC,EAAE;QAAEJ,UAAU,EAAEO,aAAa;QAAEkE,MAAM;QAAEgB;MAAK,CAAC,CAAC;;MAEhG;MACA,MAAMK,aAAa,GAAG;QACpB7G,OAAO,EAAE;UACP,cAAc,EAAE,kBAAkB;UAClC,eAAe,EAAE,UAAUO,YAAY,CAACC,OAAO,CAAC,OAAO,CAAC;QAC1D,CAAC;QACDP,OAAO,EAAE,KAAK;QAAE;QAChBsG,MAAM,EAAEC,IAAI,GAAG;UAAEA;QAAK,CAAC,GAAG,CAAC;MAC7B,CAAC;MAEDtF,OAAO,CAACC,GAAG,CAAC,UAAU,EAAE,GAAGvB,OAAO,SAAS0B,aAAa,IAAIkE,MAAM,EAAE,CAAC;MACrEtE,OAAO,CAACC,GAAG,CAAC,SAAS,EAAE0F,aAAa,CAAC;;MAErC;MACA,MAAMpF,QAAQ,GAAG,MAAM/B,KAAK,CAAC4G,MAAM,CAAC,GAAG1G,OAAO,SAAS0B,aAAa,IAAIkE,MAAM,EAAE,EAAEqB,aAAa,CAAC;MAChG3F,OAAO,CAACC,GAAG,CAAC,sBAAsB,EAAEM,QAAQ,CAACE,IAAI,CAAC;MAClD,OAAOF,QAAQ,CAACE,IAAI;IACtB,CAAC,CAAC,OAAOjB,KAAK,EAAE;MAAA,IAAAoG,gBAAA,EAAAC,gBAAA,EAAAC,iBAAA;MACd9F,OAAO,CAACR,KAAK,CAAC,oBAAoB,EAAEA,KAAK,CAAC;MAC1CQ,OAAO,CAACR,KAAK,CAAC,gBAAgB,EAAE;QAC9BsC,OAAO,EAAEtC,KAAK,CAACsC,OAAO;QACtBT,MAAM,GAAAuE,gBAAA,GAAEpG,KAAK,CAACe,QAAQ,cAAAqF,gBAAA,uBAAdA,gBAAA,CAAgBvE,MAAM;QAC9BU,UAAU,GAAA8D,gBAAA,GAAErG,KAAK,CAACe,QAAQ,cAAAsF,gBAAA,uBAAdA,gBAAA,CAAgB9D,UAAU;QACtCtB,IAAI,GAAAqF,iBAAA,GAAEtG,KAAK,CAACe,QAAQ,cAAAuF,iBAAA,uBAAdA,iBAAA,CAAgBrF,IAAI;QAC1BE,GAAG,EAAE,GAAGjC,OAAO,SAASmB,UAAU,IAAIyE,MAAM,EAAE;QAC9C7F,MAAM,EAAEe,KAAK,CAACf;MAChB,CAAC,CAAC;;MAEF;MACA,IAAIe,KAAK,CAACe,QAAQ,IAAIf,KAAK,CAACe,QAAQ,CAACE,IAAI,EAAE;QACzC,MAAMjB,KAAK,CAACe,QAAQ,CAACE,IAAI;MAC3B,CAAC,MAAM,IAAIjB,KAAK,CAACsC,OAAO,EAAE;QACxB,MAAM,IAAI3B,KAAK,CAACX,KAAK,CAACsC,OAAO,CAAC;MAChC,CAAC,MAAM;QACL,MAAM,IAAI3B,KAAK,CAAC,yCAAyC,CAAC;MAC5D;IACF;EACF,CAAC;EAED;EACA4F,iBAAiB,EAAE,MAAAA,CAAOlG,UAAU,EAAEyE,MAAM,EAAE0B,WAAW,KAAK;IAC5D,IAAI;MACF;MACA,MAAM5F,aAAa,GAAGC,QAAQ,CAACR,UAAU,EAAE,EAAE,CAAC;MAC9C,IAAIS,KAAK,CAACF,aAAa,CAAC,EAAE;QACxB,MAAM,IAAID,KAAK,CAAC,2BAA2BN,UAAU,EAAE,CAAC;MAC1D;MAEA,MAAMU,QAAQ,GAAG,MAAM5B,aAAa,CAACmG,IAAI,CAAC,SAAS1E,aAAa,IAAIkE,MAAM,eAAe,EAAE;QACzF2B,YAAY,EAAED;MAChB,CAAC,CAAC;MACF,OAAOzF,QAAQ,CAACE,IAAI;IACtB,CAAC,CAAC,OAAOjB,KAAK,EAAE;MACdQ,OAAO,CAACR,KAAK,CAAC,4BAA4B,EAAEA,KAAK,CAAC;MAClD,MAAMA,KAAK,CAACe,QAAQ,GAAGf,KAAK,CAACe,QAAQ,CAACE,IAAI,GAAGjB,KAAK;IACpD;EACF,CAAC;EAED;EACA0G,YAAY,EAAE,MAAAA,CAAOrG,UAAU,EAAEyE,MAAM,EAAE6B,QAAQ,KAAK;IACpD,IAAI;MACF;MACA,MAAM/F,aAAa,GAAGC,QAAQ,CAACR,UAAU,EAAE,EAAE,CAAC;MAC9C,IAAIS,KAAK,CAACF,aAAa,CAAC,EAAE;QACxB,MAAM,IAAID,KAAK,CAAC,2BAA2BN,UAAU,EAAE,CAAC;MAC1D;MAEA,MAAMU,QAAQ,GAAG,MAAM5B,aAAa,CAACmG,IAAI,CAAC,SAAS1E,aAAa,IAAIkE,MAAM,SAAS,EAAE;QACnF8B,SAAS,EAAED;MACb,CAAC,CAAC;MACF,OAAO5F,QAAQ,CAACE,IAAI;IACtB,CAAC,CAAC,OAAOjB,KAAK,EAAE;MACdQ,OAAO,CAACR,KAAK,CAAC,sBAAsB,EAAEA,KAAK,CAAC;MAC5C,MAAMA,KAAK,CAACe,QAAQ,GAAGf,KAAK,CAACe,QAAQ,CAACE,IAAI,GAAGjB,KAAK;IACpD;EACF,CAAC;EAED;EACA6G,iBAAiB,EAAE,MAAOxG,UAAU,IAAK;IACvC,IAAI;MACF;MACA,MAAMO,aAAa,GAAGC,QAAQ,CAACR,UAAU,EAAE,EAAE,CAAC;MAC9C,IAAIS,KAAK,CAACF,aAAa,CAAC,EAAE;QACxB,MAAM,IAAID,KAAK,CAAC,2BAA2BN,UAAU,EAAE,CAAC;MAC1D;MAEA,MAAMU,QAAQ,GAAG,MAAM5B,aAAa,CAAC6B,GAAG,CAAC,SAASJ,aAAa,aAAa,CAAC;MAC7E,OAAOG,QAAQ,CAACE,IAAI;IACtB,CAAC,CAAC,OAAOjB,KAAK,EAAE;MACdQ,OAAO,CAACR,KAAK,CAAC,4BAA4B,EAAEA,KAAK,CAAC;MAClD,MAAMA,KAAK,CAACe,QAAQ,GAAGf,KAAK,CAACe,QAAQ,CAACE,IAAI,GAAGjB,KAAK;IACpD;EACF,CAAC;EAED;EACA8G,YAAY,EAAE,MAAOzG,UAAU,IAAK;IAClC,IAAI;MACF;MACA,MAAMO,aAAa,GAAGC,QAAQ,CAACR,UAAU,EAAE,EAAE,CAAC;MAC9C,IAAIS,KAAK,CAACF,aAAa,CAAC,EAAE;QACxB,MAAM,IAAID,KAAK,CAAC,2BAA2BN,UAAU,EAAE,CAAC;MAC1D;MAEA,MAAMU,QAAQ,GAAG,MAAM5B,aAAa,CAAC6B,GAAG,CAAC,SAASJ,aAAa,QAAQ,CAAC;MACxE,OAAOG,QAAQ,CAACE,IAAI;IACtB,CAAC,CAAC,OAAOjB,KAAK,EAAE;MACdQ,OAAO,CAACR,KAAK,CAAC,uBAAuB,EAAEA,KAAK,CAAC;MAC7C,MAAMA,KAAK,CAACe,QAAQ,GAAGf,KAAK,CAACe,QAAQ,CAACE,IAAI,GAAGjB,KAAK;IACpD;EACF,CAAC;EAED;EACA+G,YAAY,EAAE,MAAO1G,UAAU,IAAK;IAClC,IAAI;MACF;MACA,MAAMO,aAAa,GAAGC,QAAQ,CAACR,UAAU,EAAE,EAAE,CAAC;MAC9C,IAAIS,KAAK,CAACF,aAAa,CAAC,EAAE;QACxB,MAAM,IAAID,KAAK,CAAC,2BAA2BN,UAAU,EAAE,CAAC;MAC1D;MAEAG,OAAO,CAACC,GAAG,CAAC,2BAA2B,CAAC;;MAExC;MACAD,OAAO,CAACC,GAAG,CAAC,qBAAqB,EAAE,GAAGvB,OAAO,SAAS0B,aAAa,cAAc,CAAC;MAElF,IAAI;QACF,MAAMG,QAAQ,GAAG,MAAM/B,KAAK,CAACgC,GAAG,CAC9B,GAAG9B,OAAO,SAAS0B,aAAa,EAAE,EAClC;UACEtB,OAAO,EAAE;YACP,cAAc,EAAE,kBAAkB;YAClC,eAAe,EAAE,UAAUO,YAAY,CAACC,OAAO,CAAC,OAAO,CAAC;UAC1D,CAAC;UACDP,OAAO,EAAE,KAAK;UACdsG,MAAM,EAAE;YAAEmB,SAAS,EAAE;UAAE;QACzB,CACF,CAAC;QAEDxG,OAAO,CAACC,GAAG,CAAC,mCAAmC,EAAEM,QAAQ,CAACE,IAAI,GAAGF,QAAQ,CAACE,IAAI,CAACU,MAAM,GAAG,CAAC,EAAE,oBAAoB,CAAC;QAChH,IAAIZ,QAAQ,CAACE,IAAI,IAAIF,QAAQ,CAACE,IAAI,CAACU,MAAM,GAAG,CAAC,EAAE;UAC7CnB,OAAO,CAACC,GAAG,CAAC,mBAAmB,EAAEM,QAAQ,CAACE,IAAI,CAAC,CAAC,CAAC,CAAC;QACpD;QAEA,OAAOF,QAAQ,CAACE,IAAI;MACtB,CAAC,CAAC,OAAOgG,aAAa,EAAE;QACtBzG,OAAO,CAACR,KAAK,CAAC,gEAAgE,EAAEiH,aAAa,CAAC;;QAE9F;QACAzG,OAAO,CAACC,GAAG,CAAC,qBAAqB,EAAE,GAAGvB,OAAO,eAAe0B,aAAa,EAAE,CAAC;QAE5E,MAAMsG,iBAAiB,GAAG,MAAMlI,KAAK,CAACgC,GAAG,CACvC,GAAG9B,OAAO,eAAe0B,aAAa,EAAE,EACxC;UACEtB,OAAO,EAAE;YACP,cAAc,EAAE,kBAAkB;YAClC,eAAe,EAAE,UAAUO,YAAY,CAACC,OAAO,CAAC,OAAO,CAAC;UAC1D,CAAC;UACDP,OAAO,EAAE;QACX,CACF,CAAC;QAEDiB,OAAO,CAACC,GAAG,CAAC,mCAAmC,EAAEyG,iBAAiB,CAACjG,IAAI,GAAGiG,iBAAiB,CAACjG,IAAI,CAACU,MAAM,GAAG,CAAC,EAAE,oBAAoB,CAAC;QAClI,IAAIuF,iBAAiB,CAACjG,IAAI,IAAIiG,iBAAiB,CAACjG,IAAI,CAACU,MAAM,GAAG,CAAC,EAAE;UAC/DnB,OAAO,CAACC,GAAG,CAAC,8BAA8B,EAAEyG,iBAAiB,CAACjG,IAAI,CAAC,CAAC,CAAC,CAAC;QACxE;QAEA,OAAOiG,iBAAiB,CAACjG,IAAI;MAC/B;IACF,CAAC,CAAC,OAAOjB,KAAK,EAAE;MACdQ,OAAO,CAACR,KAAK,CAAC,uBAAuB,EAAEA,KAAK,CAAC;MAC7C,MAAMA,KAAK,CAACe,QAAQ,GAAGf,KAAK,CAACe,QAAQ,CAACE,IAAI,GAAGjB,KAAK;IACpD;EACF,CAAC;EAED;EACAmH,WAAW,EAAE,MAAAA,CAAO9G,UAAU,EAAEyE,MAAM,EAAEsC,IAAI,EAAEC,YAAY,KAAK;IAC7D,IAAI;MACF;MACA,MAAMzG,aAAa,GAAGC,QAAQ,CAACR,UAAU,EAAE,EAAE,CAAC;MAC9C,IAAIS,KAAK,CAACF,aAAa,CAAC,EAAE;QACxB,MAAM,IAAID,KAAK,CAAC,2BAA2BN,UAAU,EAAE,CAAC;MAC1D;MAEA,MAAMU,QAAQ,GAAG,MAAM5B,aAAa,CAACmG,IAAI,CAAC,SAAS1E,aAAa,IAAIkE,MAAM,eAAe,EAAE;QACzFsC,IAAI,EAAEA,IAAI;QACVC,YAAY,EAAEA,YAAY,IAAI;MAChC,CAAC,CAAC;MACF,OAAOtG,QAAQ,CAACE,IAAI;IACtB,CAAC,CAAC,OAAOjB,KAAK,EAAE;MACdQ,OAAO,CAACR,KAAK,CAAC,qBAAqB,EAAEA,KAAK,CAAC;MAC3C,MAAMA,KAAK,CAACe,QAAQ,GAAGf,KAAK,CAACe,QAAQ,CAACE,IAAI,GAAGjB,KAAK;IACpD;EACF,CAAC;EAED;EACAsH,YAAY,EAAE,MAAAA,CAAOjH,UAAU,EAAEyE,MAAM,EAAEsC,IAAI,KAAK;IAChD,IAAI;MACF;MACA,MAAMxG,aAAa,GAAGC,QAAQ,CAACR,UAAU,EAAE,EAAE,CAAC;MAC9C,IAAIS,KAAK,CAACF,aAAa,CAAC,EAAE;QACxB,MAAM,IAAID,KAAK,CAAC,2BAA2BN,UAAU,EAAE,CAAC;MAC1D;MAEA,MAAMU,QAAQ,GAAG,MAAM5B,aAAa,CAACyG,MAAM,CAAC,SAAShF,aAAa,IAAIkE,MAAM,iBAAiBsC,IAAI,EAAE,CAAC;MACpG,OAAOrG,QAAQ,CAACE,IAAI;IACtB,CAAC,CAAC,OAAOjB,KAAK,EAAE;MACdQ,OAAO,CAACR,KAAK,CAAC,sBAAsB,EAAEA,KAAK,CAAC;MAC5C,MAAMA,KAAK,CAACe,QAAQ,GAAGf,KAAK,CAACe,QAAQ,CAACE,IAAI,GAAGjB,KAAK;IACpD;EACF,CAAC;EAED;EACAuH,SAAS,EAAE,MAAAA,CAAOlH,UAAU,EAAEyE,MAAM,KAAK;IACvC,IAAI;MACF;MACA,MAAMlE,aAAa,GAAGC,QAAQ,CAACR,UAAU,EAAE,EAAE,CAAC;MAC9C,IAAIS,KAAK,CAACF,aAAa,CAAC,EAAE;QACxB,MAAM,IAAID,KAAK,CAAC,2BAA2BN,UAAU,EAAE,CAAC;MAC1D;;MAEA;MACAG,OAAO,CAACC,GAAG,CAAC,uCAAuC,CAAC;MACpD,MAAM+G,MAAM,GAAG,MAAMrI,aAAa,CAAC6B,GAAG,CAAC,SAASJ,aAAa,cAAc,CAAC;MAC5E,MAAM6G,UAAU,GAAGD,MAAM,CAACvG,IAAI,CAACyG,IAAI,CAACC,CAAC,IAAIA,CAAC,CAACC,OAAO,KAAK9C,MAAM,CAAC;;MAE9D;MACAtE,OAAO,CAACC,GAAG,CAAC,sCAAsC,CAAC;MACnD,MAAMoH,KAAK,GAAG,MAAM1I,aAAa,CAAC6B,GAAG,CAAC,SAASJ,aAAa,cAAc,CAAC;MAC3E,MAAMkH,SAAS,GAAGD,KAAK,CAAC5G,IAAI,CAACyG,IAAI,CAACC,CAAC,IAAIA,CAAC,CAACC,OAAO,KAAK9C,MAAM,CAAC;MAE5D,OAAO;QACLiD,kBAAkB,EAAE,CAAC,CAACN,UAAU;QAChCO,iBAAiB,EAAE,CAAC,CAACF,SAAS;QAC9BG,WAAW,EAAER,UAAU;QACvBS,UAAU,EAAEJ;MACd,CAAC;IACH,CAAC,CAAC,OAAO9H,KAAK,EAAE;MACdQ,OAAO,CAACR,KAAK,CAAC,mBAAmB,EAAEA,KAAK,CAAC;MACzC,MAAMA,KAAK,CAACe,QAAQ,GAAGf,KAAK,CAACe,QAAQ,CAACE,IAAI,GAAGjB,KAAK;IACpD;EACF;AACF,CAAC;AAED,eAAeG,WAAW", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}