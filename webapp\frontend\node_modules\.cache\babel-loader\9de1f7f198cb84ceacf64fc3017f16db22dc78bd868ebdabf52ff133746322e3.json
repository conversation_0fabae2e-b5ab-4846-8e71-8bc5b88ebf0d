{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\PycharmProjects\\\\CMS\\\\webapp\\\\frontend\\\\src\\\\components\\\\TopNavbar.js\",\n  _s = $RefreshSig$();\nimport React, { useState } from 'react';\nimport { useNavigate, useLocation } from 'react-router-dom';\nimport { AppBar, Toolbar, Box, Button, Menu, MenuItem, Typography, IconButton, Divider, Avatar, Tooltip } from '@mui/material';\nimport { Home as HomeIcon, AdminPanelSettings as AdminIcon, Construction as ConstructionIcon, Cable as CableIcon, Description as ReportIcon, Logout as LogoutIcon, KeyboardArrowDown as ArrowDownIcon } from '@mui/icons-material';\nimport { useAuth } from '../context/AuthContext';\nimport SelectedCantiereDisplay from './common/SelectedCantiereDisplay';\nimport './TopNavbar.css';\nimport { jsxDEV as _jsxDEV, Fragment as _Fragment } from \"react/jsx-dev-runtime\";\nconst TopNavbar = () => {\n  _s();\n  const navigate = useNavigate();\n  const location = useLocation();\n  const {\n    user,\n    logout,\n    isImpersonating,\n    impersonatedUser\n  } = useAuth();\n\n  // Stati per i menu a tendina\n  const [homeAnchorEl, setHomeAnchorEl] = useState(null);\n  const [adminAnchorEl, setAdminAnchorEl] = useState(null);\n  const [cantieriAnchorEl, setCantieriAnchorEl] = useState(null);\n  const [caviAnchorEl, setCaviAnchorEl] = useState(null);\n  const [posaAnchorEl, setPosaAnchorEl] = useState(null);\n  const [parcoAnchorEl, setParcoAnchorEl] = useState(null);\n  const [excelAnchorEl, setExcelAnchorEl] = useState(null);\n  const [reportAnchorEl, setReportAnchorEl] = useState(null);\n  const [certificazioneAnchorEl, setCertificazioneAnchorEl] = useState(null);\n  const [comandeAnchorEl, setComandeAnchorEl] = useState(null);\n\n  // Recupera l'ID del cantiere selezionato dal localStorage\n  const selectedCantiereId = localStorage.getItem('selectedCantiereId');\n  const selectedCantiereName = localStorage.getItem('selectedCantiereName');\n\n  // Funzioni per aprire/chiudere i menu\n  const handleMenuOpen = (event, setAnchorEl) => {\n    setAnchorEl(event.currentTarget);\n  };\n  const handleMenuClose = setAnchorEl => {\n    setAnchorEl(null);\n  };\n\n  // Naviga a un percorso\n  const navigateTo = path => {\n    // Gestione speciale per il percorso Home\n    if (path === '/dashboard') {\n      // Se l'utente è un amministratore che sta impersonando un utente\n      if (isImpersonating) {\n        navigate('/dashboard/admin');\n      }\n      // Se l'utente è un amministratore normale\n      else if ((user === null || user === void 0 ? void 0 : user.role) === 'owner') {\n        navigate('/dashboard/admin');\n      }\n      // Se l'utente è un utente standard\n      else if ((user === null || user === void 0 ? void 0 : user.role) === 'user') {\n        navigate('/dashboard/cantieri');\n      }\n      // Se l'utente è un utente cantiere\n      else if ((user === null || user === void 0 ? void 0 : user.role) === 'cantieri_user') {\n        // Reindirizza direttamente alla pagina di visualizzazione cavi\n        navigate('/dashboard/cavi/visualizza');\n      }\n      // Fallback per altri tipi di utenti\n      else {\n        navigate(path);\n      }\n    } else {\n      navigate(path);\n    }\n\n    // Chiudi tutti i menu\n    handleMenuClose(setHomeAnchorEl);\n    handleMenuClose(setAdminAnchorEl);\n    handleMenuClose(setCantieriAnchorEl);\n    handleMenuClose(setCaviAnchorEl);\n    handleMenuClose(setPosaAnchorEl);\n    handleMenuClose(setParcoAnchorEl);\n    handleMenuClose(setExcelAnchorEl);\n    handleMenuClose(setReportAnchorEl);\n    handleMenuClose(setCertificazioneAnchorEl);\n    handleMenuClose(setComandeAnchorEl);\n  };\n  const handleLogout = () => {\n    logout();\n  };\n\n  // Verifica se un percorso è attivo\n  const isActive = path => {\n    return location.pathname === path;\n  };\n\n  // Verifica se un percorso è parte del percorso attivo (per i sottomenu)\n  const isPartOfActive = path => {\n    return location.pathname.startsWith(path);\n  };\n  return /*#__PURE__*/_jsxDEV(AppBar, {\n    position: \"static\",\n    color: \"default\",\n    elevation: 1,\n    sx: {\n      zIndex: 1100\n    },\n    className: \"excel-style-menu\",\n    children: /*#__PURE__*/_jsxDEV(Toolbar, {\n      variant: \"dense\",\n      children: [/*#__PURE__*/_jsxDEV(Button, {\n        color: \"inherit\",\n        onClick: () => navigateTo('/dashboard'),\n        startIcon: /*#__PURE__*/_jsxDEV(HomeIcon, {}, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 122,\n          columnNumber: 22\n        }, this),\n        sx: {\n          mr: 1\n        },\n        className: isActive('/dashboard') ? 'active-button' : '',\n        children: isImpersonating ? \"Torna al Menu Admin\" : (user === null || user === void 0 ? void 0 : user.role) === 'owner' ? \"Pannello Admin\" : (user === null || user === void 0 ? void 0 : user.role) === 'user' ? \"Lista Cantieri\" : (user === null || user === void 0 ? void 0 : user.role) === 'cantieri_user' ? \"Gestione Cavi\" : \"Home\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 119,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Divider, {\n        orientation: \"vertical\",\n        flexItem: true,\n        sx: {\n          mx: 0.5\n        }\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 131,\n        columnNumber: 9\n      }, this), ((user === null || user === void 0 ? void 0 : user.role) !== 'owner' || (user === null || user === void 0 ? void 0 : user.role) === 'owner' && isImpersonating && impersonatedUser) && /*#__PURE__*/_jsxDEV(_Fragment, {\n        children: [(isImpersonating || (user === null || user === void 0 ? void 0 : user.role) === 'cantieri_user') && /*#__PURE__*/_jsxDEV(Button, {\n          color: \"inherit\",\n          onClick: () => navigateTo('/dashboard/cantieri'),\n          sx: {\n            mr: 1\n          },\n          className: isActive('/dashboard/cantieri') ? 'active-button' : '',\n          children: isImpersonating && impersonatedUser ? `Cantieri di ${impersonatedUser.username}` : \"Lista Cantieri\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 140,\n          columnNumber: 15\n        }, this), selectedCantiereId && /*#__PURE__*/_jsxDEV(_Fragment, {\n          children: [/*#__PURE__*/_jsxDEV(Button, {\n            color: \"inherit\",\n            onClick: () => navigateTo('/dashboard/cavi/visualizza'),\n            sx: {\n              mr: 1\n            },\n            className: isActive('/dashboard/cavi/visualizza') ? 'active-button' : '',\n            children: \"Visualizza Cavi\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 156,\n            columnNumber: 17\n          }, this), /*#__PURE__*/_jsxDEV(Button, {\n            color: \"inherit\",\n            \"aria-controls\": \"posa-menu\",\n            \"aria-haspopup\": \"true\",\n            onClick: e => handleMenuOpen(e, setPosaAnchorEl),\n            endIcon: /*#__PURE__*/_jsxDEV(ArrowDownIcon, {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 171,\n              columnNumber: 28\n            }, this),\n            sx: {\n              mr: 1\n            },\n            className: isPartOfActive('/dashboard/cavi/posa') ? 'active-button' : '',\n            children: \"Posa e Collegamenti\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 166,\n            columnNumber: 17\n          }, this), /*#__PURE__*/_jsxDEV(Button, {\n            color: \"inherit\",\n            \"aria-controls\": \"parco-menu\",\n            \"aria-haspopup\": \"true\",\n            onClick: e => handleMenuOpen(e, setParcoAnchorEl),\n            endIcon: /*#__PURE__*/_jsxDEV(ArrowDownIcon, {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 184,\n              columnNumber: 28\n            }, this),\n            sx: {\n              mr: 1\n            },\n            className: isPartOfActive('/dashboard/cavi/parco') ? 'active-button' : '',\n            children: \"Parco Cavi\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 179,\n            columnNumber: 17\n          }, this), /*#__PURE__*/_jsxDEV(Button, {\n            color: \"inherit\",\n            \"aria-controls\": \"excel-menu\",\n            \"aria-haspopup\": \"true\",\n            onClick: e => handleMenuOpen(e, setExcelAnchorEl),\n            endIcon: /*#__PURE__*/_jsxDEV(ArrowDownIcon, {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 197,\n              columnNumber: 28\n            }, this),\n            sx: {\n              mr: 1\n            },\n            className: isPartOfActive('/dashboard/cavi/excel') ? 'active-button' : '',\n            children: \"Gestione Excel\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 192,\n            columnNumber: 17\n          }, this), /*#__PURE__*/_jsxDEV(Button, {\n            color: \"inherit\",\n            \"aria-controls\": \"report-menu\",\n            \"aria-haspopup\": \"true\",\n            onClick: e => handleMenuOpen(e, setReportAnchorEl),\n            endIcon: /*#__PURE__*/_jsxDEV(ArrowDownIcon, {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 210,\n              columnNumber: 28\n            }, this),\n            sx: {\n              mr: 1\n            },\n            className: isPartOfActive('/dashboard/cavi/report') ? 'active-button' : '',\n            children: \"Report\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 205,\n            columnNumber: 17\n          }, this), /*#__PURE__*/_jsxDEV(Button, {\n            color: \"inherit\",\n            \"aria-controls\": \"certificazione-menu\",\n            \"aria-haspopup\": \"true\",\n            onClick: e => handleMenuOpen(e, setCertificazioneAnchorEl),\n            endIcon: /*#__PURE__*/_jsxDEV(ArrowDownIcon, {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 223,\n              columnNumber: 28\n            }, this),\n            sx: {\n              mr: 1\n            },\n            className: isPartOfActive('/dashboard/cavi/certificazione') ? 'active-button' : '',\n            children: \"Certificazione\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 218,\n            columnNumber: 17\n          }, this), /*#__PURE__*/_jsxDEV(Button, {\n            color: \"inherit\",\n            \"aria-controls\": \"comande-menu\",\n            \"aria-haspopup\": \"true\",\n            onClick: e => handleMenuOpen(e, setComandeAnchorEl),\n            endIcon: /*#__PURE__*/_jsxDEV(ArrowDownIcon, {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 236,\n              columnNumber: 28\n            }, this),\n            sx: {\n              mr: 1\n            },\n            className: isPartOfActive('/dashboard/cavi/comande') ? 'active-button' : '',\n            children: \"Comande\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 231,\n            columnNumber: 17\n          }, this), /*#__PURE__*/_jsxDEV(Menu, {\n            id: \"posa-menu\",\n            anchorEl: posaAnchorEl,\n            keepMounted: true,\n            open: Boolean(posaAnchorEl),\n            onClose: () => handleMenuClose(setPosaAnchorEl),\n            anchorOrigin: {\n              vertical: 'top',\n              horizontal: 'right'\n            },\n            transformOrigin: {\n              vertical: 'top',\n              horizontal: 'left'\n            },\n            className: \"excel-style-submenu\",\n            children: [/*#__PURE__*/_jsxDEV(MenuItem, {\n              onClick: () => navigateTo('/dashboard/cavi/posa/inserisci-metri'),\n              children: \"Inserisci metri posati\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 260,\n              columnNumber: 19\n            }, this), /*#__PURE__*/_jsxDEV(MenuItem, {\n              onClick: () => navigateTo('/dashboard/cavi/posa/modifica-cavo'),\n              children: \"Modifica cavo\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 261,\n              columnNumber: 19\n            }, this), /*#__PURE__*/_jsxDEV(MenuItem, {\n              onClick: () => navigateTo('/dashboard/cavi/posa/aggiungi-cavo'),\n              children: \"Aggiungi nuovo cavo\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 262,\n              columnNumber: 19\n            }, this), /*#__PURE__*/_jsxDEV(MenuItem, {\n              onClick: () => navigateTo('/dashboard/cavi/posa/elimina-cavo'),\n              children: \"Elimina cavo\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 263,\n              columnNumber: 19\n            }, this), /*#__PURE__*/_jsxDEV(MenuItem, {\n              onClick: () => navigateTo('/dashboard/cavi/posa/modifica-bobina'),\n              children: \"Modifica bobina cavo posato\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 264,\n              columnNumber: 19\n            }, this), /*#__PURE__*/_jsxDEV(MenuItem, {\n              onClick: () => navigateTo('/dashboard/cavi/posa/collegamenti'),\n              children: \"Gestisci collegamenti cavo\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 265,\n              columnNumber: 19\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 244,\n            columnNumber: 17\n          }, this), /*#__PURE__*/_jsxDEV(Menu, {\n            id: \"parco-menu\",\n            anchorEl: parcoAnchorEl,\n            keepMounted: true,\n            open: Boolean(parcoAnchorEl),\n            onClose: () => handleMenuClose(setParcoAnchorEl),\n            anchorOrigin: {\n              vertical: 'top',\n              horizontal: 'right'\n            },\n            transformOrigin: {\n              vertical: 'top',\n              horizontal: 'left'\n            },\n            className: \"excel-style-submenu\",\n            children: [/*#__PURE__*/_jsxDEV(MenuItem, {\n              onClick: () => navigateTo('/dashboard/cavi/parco/visualizza'),\n              children: \"Visualizza Bobine\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 285,\n              columnNumber: 19\n            }, this), /*#__PURE__*/_jsxDEV(MenuItem, {\n              onClick: () => navigateTo('/dashboard/cavi/parco/crea'),\n              children: \"Crea Nuova Bobina\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 286,\n              columnNumber: 19\n            }, this), /*#__PURE__*/_jsxDEV(MenuItem, {\n              onClick: () => navigateTo('/dashboard/cavi/parco/modifica'),\n              children: \"Modifica Bobina\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 287,\n              columnNumber: 19\n            }, this), /*#__PURE__*/_jsxDEV(MenuItem, {\n              onClick: () => navigateTo('/dashboard/cavi/parco/elimina'),\n              children: \"Elimina Bobina\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 288,\n              columnNumber: 19\n            }, this), /*#__PURE__*/_jsxDEV(MenuItem, {\n              onClick: () => navigateTo('/dashboard/cavi/parco/storico'),\n              children: \"Storico Utilizzo\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 289,\n              columnNumber: 19\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 269,\n            columnNumber: 17\n          }, this), /*#__PURE__*/_jsxDEV(Menu, {\n            id: \"excel-menu\",\n            anchorEl: excelAnchorEl,\n            keepMounted: true,\n            open: Boolean(excelAnchorEl),\n            onClose: () => handleMenuClose(setExcelAnchorEl),\n            anchorOrigin: {\n              vertical: 'top',\n              horizontal: 'right'\n            },\n            transformOrigin: {\n              vertical: 'top',\n              horizontal: 'left'\n            },\n            className: \"excel-style-submenu\",\n            children: [/*#__PURE__*/_jsxDEV(MenuItem, {\n              onClick: () => navigateTo('/dashboard/cavi/excel/importa-cavi'),\n              children: \"Importa cavi da Excel\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 309,\n              columnNumber: 19\n            }, this), /*#__PURE__*/_jsxDEV(MenuItem, {\n              onClick: () => navigateTo('/dashboard/cavi/excel/importa-bobine'),\n              children: \"Importa parco bobine\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 310,\n              columnNumber: 19\n            }, this), /*#__PURE__*/_jsxDEV(MenuItem, {\n              onClick: () => navigateTo('/dashboard/cavi/excel/esporta-cavi'),\n              children: \"Esporta cavi in Excel\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 311,\n              columnNumber: 19\n            }, this), /*#__PURE__*/_jsxDEV(MenuItem, {\n              onClick: () => navigateTo('/dashboard/cavi/excel/esporta-bobine'),\n              children: \"Esporta parco bobine\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 312,\n              columnNumber: 19\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 293,\n            columnNumber: 17\n          }, this), /*#__PURE__*/_jsxDEV(Menu, {\n            id: \"report-menu\",\n            anchorEl: reportAnchorEl,\n            keepMounted: true,\n            open: Boolean(reportAnchorEl),\n            onClose: () => handleMenuClose(setReportAnchorEl),\n            anchorOrigin: {\n              vertical: 'top',\n              horizontal: 'right'\n            },\n            transformOrigin: {\n              vertical: 'top',\n              horizontal: 'left'\n            },\n            className: \"excel-style-submenu\",\n            children: [/*#__PURE__*/_jsxDEV(MenuItem, {\n              onClick: () => navigateTo('/dashboard/cavi/report/avanzamento'),\n              children: \"Report Avanzamento\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 332,\n              columnNumber: 19\n            }, this), /*#__PURE__*/_jsxDEV(MenuItem, {\n              onClick: () => navigateTo('/dashboard/cavi/report/boq'),\n              children: \"Bill of Quantities\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 333,\n              columnNumber: 19\n            }, this), /*#__PURE__*/_jsxDEV(MenuItem, {\n              onClick: () => navigateTo('/dashboard/cavi/report/utilizzo-bobine'),\n              children: \"Report Utilizzo Bobine\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 334,\n              columnNumber: 19\n            }, this), /*#__PURE__*/_jsxDEV(MenuItem, {\n              onClick: () => navigateTo('/dashboard/cavi/report/statistiche'),\n              children: \"Statistiche Cantiere\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 335,\n              columnNumber: 19\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 316,\n            columnNumber: 17\n          }, this), /*#__PURE__*/_jsxDEV(Menu, {\n            id: \"certificazione-menu\",\n            anchorEl: certificazioneAnchorEl,\n            keepMounted: true,\n            open: Boolean(certificazioneAnchorEl),\n            onClose: () => handleMenuClose(setCertificazioneAnchorEl),\n            anchorOrigin: {\n              vertical: 'top',\n              horizontal: 'right'\n            },\n            transformOrigin: {\n              vertical: 'top',\n              horizontal: 'left'\n            },\n            className: \"excel-style-submenu\",\n            children: [/*#__PURE__*/_jsxDEV(MenuItem, {\n              onClick: () => navigateTo('/dashboard/cavi/certificazione/visualizza'),\n              children: \"Visualizza certificazioni\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 355,\n              columnNumber: 19\n            }, this), /*#__PURE__*/_jsxDEV(MenuItem, {\n              onClick: () => navigateTo('/dashboard/cavi/certificazione/filtra'),\n              children: \"Filtra per cavo\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 356,\n              columnNumber: 19\n            }, this), /*#__PURE__*/_jsxDEV(MenuItem, {\n              onClick: () => navigateTo('/dashboard/cavi/certificazione/crea'),\n              children: \"Crea certificazione\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 357,\n              columnNumber: 19\n            }, this), /*#__PURE__*/_jsxDEV(MenuItem, {\n              onClick: () => navigateTo('/dashboard/cavi/certificazione/modifica'),\n              children: \"Modifica certificazione\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 358,\n              columnNumber: 19\n            }, this), /*#__PURE__*/_jsxDEV(MenuItem, {\n              onClick: () => navigateTo('/dashboard/cavi/certificazione/elimina'),\n              children: \"Elimina certificazione\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 359,\n              columnNumber: 19\n            }, this), /*#__PURE__*/_jsxDEV(MenuItem, {\n              onClick: () => navigateTo('/dashboard/cavi/certificazione/strumenti'),\n              children: \"Gestione strumenti\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 360,\n              columnNumber: 19\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 339,\n            columnNumber: 17\n          }, this), /*#__PURE__*/_jsxDEV(Menu, {\n            id: \"comande-menu\",\n            anchorEl: comandeAnchorEl,\n            keepMounted: true,\n            open: Boolean(comandeAnchorEl),\n            onClose: () => handleMenuClose(setComandeAnchorEl),\n            anchorOrigin: {\n              vertical: 'top',\n              horizontal: 'right'\n            },\n            transformOrigin: {\n              vertical: 'top',\n              horizontal: 'left'\n            },\n            className: \"excel-style-submenu\",\n            children: [/*#__PURE__*/_jsxDEV(MenuItem, {\n              onClick: () => navigateTo('/dashboard/cavi/comande/visualizza'),\n              children: \"Visualizza comande\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 380,\n              columnNumber: 19\n            }, this), /*#__PURE__*/_jsxDEV(MenuItem, {\n              onClick: () => navigateTo('/dashboard/cavi/comande/crea'),\n              children: \"Crea nuova comanda\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 381,\n              columnNumber: 19\n            }, this), /*#__PURE__*/_jsxDEV(MenuItem, {\n              onClick: () => navigateTo('/dashboard/cavi/comande/modifica'),\n              children: \"Modifica comanda\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 382,\n              columnNumber: 19\n            }, this), /*#__PURE__*/_jsxDEV(MenuItem, {\n              onClick: () => navigateTo('/dashboard/cavi/comande/elimina'),\n              children: \"Elimina comanda\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 383,\n              columnNumber: 19\n            }, this), /*#__PURE__*/_jsxDEV(MenuItem, {\n              onClick: () => navigateTo('/dashboard/cavi/comande/stampa'),\n              children: \"Stampa comanda\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 384,\n              columnNumber: 19\n            }, this), /*#__PURE__*/_jsxDEV(MenuItem, {\n              onClick: () => navigateTo('/dashboard/cavi/comande/assegna'),\n              children: \"Assegna comanda a cavo\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 385,\n              columnNumber: 19\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 364,\n            columnNumber: 17\n          }, this)]\n        }, void 0, true)]\n      }, void 0, true), /*#__PURE__*/_jsxDEV(Box, {\n        sx: {\n          flexGrow: 1\n        }\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 393,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Box, {\n        sx: {\n          display: 'flex',\n          alignItems: 'center'\n        },\n        children: [/*#__PURE__*/_jsxDEV(SelectedCantiereDisplay, {}, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 398,\n          columnNumber: 11\n        }, this), isImpersonating && impersonatedUser && /*#__PURE__*/_jsxDEV(Typography, {\n          variant: \"body2\",\n          color: \"textSecondary\",\n          sx: {\n            mr: 2\n          },\n          children: [\"Accesso come: \", /*#__PURE__*/_jsxDEV(\"b\", {\n            children: impersonatedUser.username\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 402,\n            columnNumber: 29\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 401,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(Typography, {\n          variant: \"body2\",\n          sx: {\n            mr: 2\n          },\n          children: (user === null || user === void 0 ? void 0 : user.username) || ''\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 405,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Tooltip, {\n          title: \"Logout\",\n          children: /*#__PURE__*/_jsxDEV(IconButton, {\n            color: \"inherit\",\n            onClick: handleLogout,\n            edge: \"end\",\n            children: /*#__PURE__*/_jsxDEV(LogoutIcon, {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 410,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 409,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 408,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 396,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 117,\n      columnNumber: 7\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 116,\n    columnNumber: 5\n  }, this);\n};\n_s(TopNavbar, \"KcFPmHsEUY/2zSYWn487+PtaWNk=\", false, function () {\n  return [useNavigate, useLocation, useAuth];\n});\n_c = TopNavbar;\nexport default TopNavbar;\nvar _c;\n$RefreshReg$(_c, \"TopNavbar\");", "map": {"version": 3, "names": ["React", "useState", "useNavigate", "useLocation", "AppBar", "<PERSON><PERSON><PERSON>", "Box", "<PERSON><PERSON>", "<PERSON><PERSON>", "MenuItem", "Typography", "IconButton", "Divider", "Avatar", "<PERSON><PERSON><PERSON>", "Home", "HomeIcon", "AdminPanelSettings", "AdminIcon", "Construction", "ConstructionIcon", "Cable", "CableIcon", "Description", "ReportIcon", "Logout", "LogoutIcon", "KeyboardArrowDown", "ArrowDownIcon", "useAuth", "SelectedCantiereDisplay", "jsxDEV", "_jsxDEV", "Fragment", "_Fragment", "TopNavbar", "_s", "navigate", "location", "user", "logout", "isImpersonating", "impersonated<PERSON><PERSON>", "homeAnchorEl", "setHomeAnchorEl", "adminAnchorEl", "setAdminAnchorEl", "cantieriAnchorEl", "setCantieriAnchorEl", "caviAnchorEl", "setCaviAnchorEl", "posaAnchorEl", "setPosaAnchorEl", "parcoAnchorEl", "setParcoAnchorEl", "excelAnchorEl", "setExcelAnchorEl", "reportAnchorEl", "setReportAnchorEl", "certificazioneAnchorEl", "setCertificazioneAnchorEl", "comandeAnchorEl", "setComandeAnchorEl", "selectedCantiereId", "localStorage", "getItem", "selectedCantiereName", "handleMenuOpen", "event", "setAnchorEl", "currentTarget", "handleMenuClose", "navigateTo", "path", "role", "handleLogout", "isActive", "pathname", "isPartOfActive", "startsWith", "position", "color", "elevation", "sx", "zIndex", "className", "children", "variant", "onClick", "startIcon", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "mr", "orientation", "flexItem", "mx", "username", "e", "endIcon", "id", "anchorEl", "keepMounted", "open", "Boolean", "onClose", "anchor<PERSON><PERSON><PERSON>", "vertical", "horizontal", "transform<PERSON><PERSON>in", "flexGrow", "display", "alignItems", "title", "edge", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/PycharmProjects/CMS/webapp/frontend/src/components/TopNavbar.js"], "sourcesContent": ["import React, { useState } from 'react';\nimport { useNavigate, useLocation } from 'react-router-dom';\nimport {\n  AppBar,\n  Toolbar,\n  Box,\n  Button,\n  Menu,\n  MenuItem,\n  Typography,\n  IconButton,\n  Divider,\n  Avatar,\n  Tooltip\n} from '@mui/material';\nimport {\n  Home as HomeIcon,\n  AdminPanelSettings as AdminIcon,\n  Construction as ConstructionIcon,\n  Cable as CableIcon,\n  Description as ReportIcon,\n  Logout as LogoutIcon,\n  KeyboardArrowDown as ArrowDownIcon\n} from '@mui/icons-material';\nimport { useAuth } from '../context/AuthContext';\nimport SelectedCantiereDisplay from './common/SelectedCantiereDisplay';\nimport './TopNavbar.css';\n\nconst TopNavbar = () => {\n  const navigate = useNavigate();\n  const location = useLocation();\n  const { user, logout, isImpersonating, impersonatedUser } = useAuth();\n\n  // Stati per i menu a tendina\n  const [homeAnchorEl, setHomeAnchorEl] = useState(null);\n  const [adminAnchorEl, setAdminAnchorEl] = useState(null);\n  const [cantieriAnchorEl, setCantieriAnchorEl] = useState(null);\n  const [caviAnchorEl, setCaviAnchorEl] = useState(null);\n  const [posaAnchorEl, setPosaAnchorEl] = useState(null);\n  const [parcoAnchorEl, setParcoAnchorEl] = useState(null);\n  const [excelAnchorEl, setExcelAnchorEl] = useState(null);\n  const [reportAnchorEl, setReportAnchorEl] = useState(null);\n  const [certificazioneAnchorEl, setCertificazioneAnchorEl] = useState(null);\n  const [comandeAnchorEl, setComandeAnchorEl] = useState(null);\n\n  // Recupera l'ID del cantiere selezionato dal localStorage\n  const selectedCantiereId = localStorage.getItem('selectedCantiereId');\n  const selectedCantiereName = localStorage.getItem('selectedCantiereName');\n\n  // Funzioni per aprire/chiudere i menu\n  const handleMenuOpen = (event, setAnchorEl) => {\n    setAnchorEl(event.currentTarget);\n  };\n\n  const handleMenuClose = (setAnchorEl) => {\n    setAnchorEl(null);\n  };\n\n  // Naviga a un percorso\n  const navigateTo = (path) => {\n    // Gestione speciale per il percorso Home\n    if (path === '/dashboard') {\n      // Se l'utente è un amministratore che sta impersonando un utente\n      if (isImpersonating) {\n        navigate('/dashboard/admin');\n      }\n      // Se l'utente è un amministratore normale\n      else if (user?.role === 'owner') {\n        navigate('/dashboard/admin');\n      }\n      // Se l'utente è un utente standard\n      else if (user?.role === 'user') {\n        navigate('/dashboard/cantieri');\n      }\n      // Se l'utente è un utente cantiere\n      else if (user?.role === 'cantieri_user') {\n        // Reindirizza direttamente alla pagina di visualizzazione cavi\n        navigate('/dashboard/cavi/visualizza');\n      }\n      // Fallback per altri tipi di utenti\n      else {\n        navigate(path);\n      }\n    } else {\n      navigate(path);\n    }\n\n    // Chiudi tutti i menu\n    handleMenuClose(setHomeAnchorEl);\n    handleMenuClose(setAdminAnchorEl);\n    handleMenuClose(setCantieriAnchorEl);\n    handleMenuClose(setCaviAnchorEl);\n    handleMenuClose(setPosaAnchorEl);\n    handleMenuClose(setParcoAnchorEl);\n    handleMenuClose(setExcelAnchorEl);\n    handleMenuClose(setReportAnchorEl);\n    handleMenuClose(setCertificazioneAnchorEl);\n    handleMenuClose(setComandeAnchorEl);\n  };\n\n  const handleLogout = () => {\n    logout();\n  };\n\n  // Verifica se un percorso è attivo\n  const isActive = (path) => {\n    return location.pathname === path;\n  };\n\n  // Verifica se un percorso è parte del percorso attivo (per i sottomenu)\n  const isPartOfActive = (path) => {\n    return location.pathname.startsWith(path);\n  };\n\n  return (\n    <AppBar position=\"static\" color=\"default\" elevation={1} sx={{ zIndex: 1100 }} className=\"excel-style-menu\">\n      <Toolbar variant=\"dense\">\n        {/* Logo/Home - Testo personalizzato in base al tipo di utente */}\n        <Button\n          color=\"inherit\"\n          onClick={() => navigateTo('/dashboard')}\n          startIcon={<HomeIcon />}\n          sx={{ mr: 1 }}\n          className={isActive('/dashboard') ? 'active-button' : ''}\n        >\n          {isImpersonating ? \"Torna al Menu Admin\" :\n           user?.role === 'owner' ? \"Pannello Admin\" :\n           user?.role === 'user' ? \"Lista Cantieri\" :\n           user?.role === 'cantieri_user' ? \"Gestione Cavi\" : \"Home\"}\n        </Button>\n        <Divider orientation=\"vertical\" flexItem sx={{ mx: 0.5 }} />\n\n        {/* Il menu Amministratore è stato rimosso perché ridondante con il pulsante Home per gli amministratori */}\n\n        {/* Menu per utenti standard e cantieri */}\n        {(user?.role !== 'owner' || (user?.role === 'owner' && isImpersonating && impersonatedUser)) && (\n          <>\n            {/* Pulsante Lista Cantieri solo per utenti che impersonano o utenti cantiere */}\n            {(isImpersonating || user?.role === 'cantieri_user') && (\n              <Button\n                color=\"inherit\"\n                onClick={() => navigateTo('/dashboard/cantieri')}\n                sx={{ mr: 1 }}\n                className={isActive('/dashboard/cantieri') ? 'active-button' : ''}\n              >\n                {isImpersonating && impersonatedUser ? `Cantieri di ${impersonatedUser.username}` : \"Lista Cantieri\"}\n              </Button>\n            )}\n\n            {/* Il cantiere selezionato è stato spostato nella parte destra della barra di navigazione */}\n\n            {/* Menu di gestione cavi - visibile solo se un cantiere è selezionato */}\n            {selectedCantiereId && (\n              <>\n                {/* Visualizza Cavi */}\n                <Button\n                  color=\"inherit\"\n                  onClick={() => navigateTo('/dashboard/cavi/visualizza')}\n                  sx={{ mr: 1 }}\n                  className={isActive('/dashboard/cavi/visualizza') ? 'active-button' : ''}\n                >\n                  Visualizza Cavi\n                </Button>\n\n                {/* Posa e Collegamenti */}\n                <Button\n                  color=\"inherit\"\n                  aria-controls=\"posa-menu\"\n                  aria-haspopup=\"true\"\n                  onClick={(e) => handleMenuOpen(e, setPosaAnchorEl)}\n                  endIcon={<ArrowDownIcon />}\n                  sx={{ mr: 1 }}\n                  className={isPartOfActive('/dashboard/cavi/posa') ? 'active-button' : ''}\n                >\n                  Posa e Collegamenti\n                </Button>\n\n                {/* Parco Cavi */}\n                <Button\n                  color=\"inherit\"\n                  aria-controls=\"parco-menu\"\n                  aria-haspopup=\"true\"\n                  onClick={(e) => handleMenuOpen(e, setParcoAnchorEl)}\n                  endIcon={<ArrowDownIcon />}\n                  sx={{ mr: 1 }}\n                  className={isPartOfActive('/dashboard/cavi/parco') ? 'active-button' : ''}\n                >\n                  Parco Cavi\n                </Button>\n\n                {/* Gestione Excel */}\n                <Button\n                  color=\"inherit\"\n                  aria-controls=\"excel-menu\"\n                  aria-haspopup=\"true\"\n                  onClick={(e) => handleMenuOpen(e, setExcelAnchorEl)}\n                  endIcon={<ArrowDownIcon />}\n                  sx={{ mr: 1 }}\n                  className={isPartOfActive('/dashboard/cavi/excel') ? 'active-button' : ''}\n                >\n                  Gestione Excel\n                </Button>\n\n                {/* Report */}\n                <Button\n                  color=\"inherit\"\n                  aria-controls=\"report-menu\"\n                  aria-haspopup=\"true\"\n                  onClick={(e) => handleMenuOpen(e, setReportAnchorEl)}\n                  endIcon={<ArrowDownIcon />}\n                  sx={{ mr: 1 }}\n                  className={isPartOfActive('/dashboard/cavi/report') ? 'active-button' : ''}\n                >\n                  Report\n                </Button>\n\n                {/* Certificazione Cavi */}\n                <Button\n                  color=\"inherit\"\n                  aria-controls=\"certificazione-menu\"\n                  aria-haspopup=\"true\"\n                  onClick={(e) => handleMenuOpen(e, setCertificazioneAnchorEl)}\n                  endIcon={<ArrowDownIcon />}\n                  sx={{ mr: 1 }}\n                  className={isPartOfActive('/dashboard/cavi/certificazione') ? 'active-button' : ''}\n                >\n                  Certificazione\n                </Button>\n\n                {/* Gestione Comande */}\n                <Button\n                  color=\"inherit\"\n                  aria-controls=\"comande-menu\"\n                  aria-haspopup=\"true\"\n                  onClick={(e) => handleMenuOpen(e, setComandeAnchorEl)}\n                  endIcon={<ArrowDownIcon />}\n                  sx={{ mr: 1 }}\n                  className={isPartOfActive('/dashboard/cavi/comande') ? 'active-button' : ''}\n                >\n                  Comande\n                </Button>\n\n                {/* Sottomenu Posa e Collegamenti */}\n                <Menu\n                  id=\"posa-menu\"\n                  anchorEl={posaAnchorEl}\n                  keepMounted\n                  open={Boolean(posaAnchorEl)}\n                  onClose={() => handleMenuClose(setPosaAnchorEl)}\n                  anchorOrigin={{\n                    vertical: 'top',\n                    horizontal: 'right',\n                  }}\n                  transformOrigin={{\n                    vertical: 'top',\n                    horizontal: 'left',\n                  }}\n                  className=\"excel-style-submenu\"\n                >\n                  <MenuItem onClick={() => navigateTo('/dashboard/cavi/posa/inserisci-metri')}>Inserisci metri posati</MenuItem>\n                  <MenuItem onClick={() => navigateTo('/dashboard/cavi/posa/modifica-cavo')}>Modifica cavo</MenuItem>\n                  <MenuItem onClick={() => navigateTo('/dashboard/cavi/posa/aggiungi-cavo')}>Aggiungi nuovo cavo</MenuItem>\n                  <MenuItem onClick={() => navigateTo('/dashboard/cavi/posa/elimina-cavo')}>Elimina cavo</MenuItem>\n                  <MenuItem onClick={() => navigateTo('/dashboard/cavi/posa/modifica-bobina')}>Modifica bobina cavo posato</MenuItem>\n                  <MenuItem onClick={() => navigateTo('/dashboard/cavi/posa/collegamenti')}>Gestisci collegamenti cavo</MenuItem>\n                </Menu>\n\n                {/* Sottomenu Parco Cavi */}\n                <Menu\n                  id=\"parco-menu\"\n                  anchorEl={parcoAnchorEl}\n                  keepMounted\n                  open={Boolean(parcoAnchorEl)}\n                  onClose={() => handleMenuClose(setParcoAnchorEl)}\n                  anchorOrigin={{\n                    vertical: 'top',\n                    horizontal: 'right',\n                  }}\n                  transformOrigin={{\n                    vertical: 'top',\n                    horizontal: 'left',\n                  }}\n                  className=\"excel-style-submenu\"\n                >\n                  <MenuItem onClick={() => navigateTo('/dashboard/cavi/parco/visualizza')}>Visualizza Bobine</MenuItem>\n                  <MenuItem onClick={() => navigateTo('/dashboard/cavi/parco/crea')}>Crea Nuova Bobina</MenuItem>\n                  <MenuItem onClick={() => navigateTo('/dashboard/cavi/parco/modifica')}>Modifica Bobina</MenuItem>\n                  <MenuItem onClick={() => navigateTo('/dashboard/cavi/parco/elimina')}>Elimina Bobina</MenuItem>\n                  <MenuItem onClick={() => navigateTo('/dashboard/cavi/parco/storico')}>Storico Utilizzo</MenuItem>\n                </Menu>\n\n                {/* Sottomenu Gestione Excel */}\n                <Menu\n                  id=\"excel-menu\"\n                  anchorEl={excelAnchorEl}\n                  keepMounted\n                  open={Boolean(excelAnchorEl)}\n                  onClose={() => handleMenuClose(setExcelAnchorEl)}\n                  anchorOrigin={{\n                    vertical: 'top',\n                    horizontal: 'right',\n                  }}\n                  transformOrigin={{\n                    vertical: 'top',\n                    horizontal: 'left',\n                  }}\n                  className=\"excel-style-submenu\"\n                >\n                  <MenuItem onClick={() => navigateTo('/dashboard/cavi/excel/importa-cavi')}>Importa cavi da Excel</MenuItem>\n                  <MenuItem onClick={() => navigateTo('/dashboard/cavi/excel/importa-bobine')}>Importa parco bobine</MenuItem>\n                  <MenuItem onClick={() => navigateTo('/dashboard/cavi/excel/esporta-cavi')}>Esporta cavi in Excel</MenuItem>\n                  <MenuItem onClick={() => navigateTo('/dashboard/cavi/excel/esporta-bobine')}>Esporta parco bobine</MenuItem>\n                </Menu>\n\n                {/* Sottomenu Report */}\n                <Menu\n                  id=\"report-menu\"\n                  anchorEl={reportAnchorEl}\n                  keepMounted\n                  open={Boolean(reportAnchorEl)}\n                  onClose={() => handleMenuClose(setReportAnchorEl)}\n                  anchorOrigin={{\n                    vertical: 'top',\n                    horizontal: 'right',\n                  }}\n                  transformOrigin={{\n                    vertical: 'top',\n                    horizontal: 'left',\n                  }}\n                  className=\"excel-style-submenu\"\n                >\n                  <MenuItem onClick={() => navigateTo('/dashboard/cavi/report/avanzamento')}>Report Avanzamento</MenuItem>\n                  <MenuItem onClick={() => navigateTo('/dashboard/cavi/report/boq')}>Bill of Quantities</MenuItem>\n                  <MenuItem onClick={() => navigateTo('/dashboard/cavi/report/utilizzo-bobine')}>Report Utilizzo Bobine</MenuItem>\n                  <MenuItem onClick={() => navigateTo('/dashboard/cavi/report/statistiche')}>Statistiche Cantiere</MenuItem>\n                </Menu>\n\n                {/* Sottomenu Certificazione Cavi */}\n                <Menu\n                  id=\"certificazione-menu\"\n                  anchorEl={certificazioneAnchorEl}\n                  keepMounted\n                  open={Boolean(certificazioneAnchorEl)}\n                  onClose={() => handleMenuClose(setCertificazioneAnchorEl)}\n                  anchorOrigin={{\n                    vertical: 'top',\n                    horizontal: 'right',\n                  }}\n                  transformOrigin={{\n                    vertical: 'top',\n                    horizontal: 'left',\n                  }}\n                  className=\"excel-style-submenu\"\n                >\n                  <MenuItem onClick={() => navigateTo('/dashboard/cavi/certificazione/visualizza')}>Visualizza certificazioni</MenuItem>\n                  <MenuItem onClick={() => navigateTo('/dashboard/cavi/certificazione/filtra')}>Filtra per cavo</MenuItem>\n                  <MenuItem onClick={() => navigateTo('/dashboard/cavi/certificazione/crea')}>Crea certificazione</MenuItem>\n                  <MenuItem onClick={() => navigateTo('/dashboard/cavi/certificazione/modifica')}>Modifica certificazione</MenuItem>\n                  <MenuItem onClick={() => navigateTo('/dashboard/cavi/certificazione/elimina')}>Elimina certificazione</MenuItem>\n                  <MenuItem onClick={() => navigateTo('/dashboard/cavi/certificazione/strumenti')}>Gestione strumenti</MenuItem>\n                </Menu>\n\n                {/* Sottomenu Gestione Comande */}\n                <Menu\n                  id=\"comande-menu\"\n                  anchorEl={comandeAnchorEl}\n                  keepMounted\n                  open={Boolean(comandeAnchorEl)}\n                  onClose={() => handleMenuClose(setComandeAnchorEl)}\n                  anchorOrigin={{\n                    vertical: 'top',\n                    horizontal: 'right',\n                  }}\n                  transformOrigin={{\n                    vertical: 'top',\n                    horizontal: 'left',\n                  }}\n                  className=\"excel-style-submenu\"\n                >\n                  <MenuItem onClick={() => navigateTo('/dashboard/cavi/comande/visualizza')}>Visualizza comande</MenuItem>\n                  <MenuItem onClick={() => navigateTo('/dashboard/cavi/comande/crea')}>Crea nuova comanda</MenuItem>\n                  <MenuItem onClick={() => navigateTo('/dashboard/cavi/comande/modifica')}>Modifica comanda</MenuItem>\n                  <MenuItem onClick={() => navigateTo('/dashboard/cavi/comande/elimina')}>Elimina comanda</MenuItem>\n                  <MenuItem onClick={() => navigateTo('/dashboard/cavi/comande/stampa')}>Stampa comanda</MenuItem>\n                  <MenuItem onClick={() => navigateTo('/dashboard/cavi/comande/assegna')}>Assegna comanda a cavo</MenuItem>\n                </Menu>\n              </>\n            )}\n          </>\n        )}\n\n        {/* Spacer */}\n        <Box sx={{ flexGrow: 1 }} />\n\n        {/* Informazioni utente e logout */}\n        <Box sx={{ display: 'flex', alignItems: 'center' }}>\n          {/* Mostra il cantiere selezionato */}\n          <SelectedCantiereDisplay />\n\n          {isImpersonating && impersonatedUser && (\n            <Typography variant=\"body2\" color=\"textSecondary\" sx={{ mr: 2 }}>\n              Accesso come: <b>{impersonatedUser.username}</b>\n            </Typography>\n          )}\n          <Typography variant=\"body2\" sx={{ mr: 2 }}>\n            {user?.username || ''}\n          </Typography>\n          <Tooltip title=\"Logout\">\n            <IconButton color=\"inherit\" onClick={handleLogout} edge=\"end\">\n              <LogoutIcon />\n            </IconButton>\n          </Tooltip>\n        </Box>\n      </Toolbar>\n    </AppBar>\n  );\n};\n\nexport default TopNavbar;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,QAAQ,OAAO;AACvC,SAASC,WAAW,EAAEC,WAAW,QAAQ,kBAAkB;AAC3D,SACEC,MAAM,EACNC,OAAO,EACPC,GAAG,EACHC,MAAM,EACNC,IAAI,EACJC,QAAQ,EACRC,UAAU,EACVC,UAAU,EACVC,OAAO,EACPC,MAAM,EACNC,OAAO,QACF,eAAe;AACtB,SACEC,IAAI,IAAIC,QAAQ,EAChBC,kBAAkB,IAAIC,SAAS,EAC/BC,YAAY,IAAIC,gBAAgB,EAChCC,KAAK,IAAIC,SAAS,EAClBC,WAAW,IAAIC,UAAU,EACzBC,MAAM,IAAIC,UAAU,EACpBC,iBAAiB,IAAIC,aAAa,QAC7B,qBAAqB;AAC5B,SAASC,OAAO,QAAQ,wBAAwB;AAChD,OAAOC,uBAAuB,MAAM,kCAAkC;AACtE,OAAO,iBAAiB;AAAC,SAAAC,MAAA,IAAAC,OAAA,EAAAC,QAAA,IAAAC,SAAA;AAEzB,MAAMC,SAAS,GAAGA,CAAA,KAAM;EAAAC,EAAA;EACtB,MAAMC,QAAQ,GAAGnC,WAAW,CAAC,CAAC;EAC9B,MAAMoC,QAAQ,GAAGnC,WAAW,CAAC,CAAC;EAC9B,MAAM;IAAEoC,IAAI;IAAEC,MAAM;IAAEC,eAAe;IAAEC;EAAiB,CAAC,GAAGb,OAAO,CAAC,CAAC;;EAErE;EACA,MAAM,CAACc,YAAY,EAAEC,eAAe,CAAC,GAAG3C,QAAQ,CAAC,IAAI,CAAC;EACtD,MAAM,CAAC4C,aAAa,EAAEC,gBAAgB,CAAC,GAAG7C,QAAQ,CAAC,IAAI,CAAC;EACxD,MAAM,CAAC8C,gBAAgB,EAAEC,mBAAmB,CAAC,GAAG/C,QAAQ,CAAC,IAAI,CAAC;EAC9D,MAAM,CAACgD,YAAY,EAAEC,eAAe,CAAC,GAAGjD,QAAQ,CAAC,IAAI,CAAC;EACtD,MAAM,CAACkD,YAAY,EAAEC,eAAe,CAAC,GAAGnD,QAAQ,CAAC,IAAI,CAAC;EACtD,MAAM,CAACoD,aAAa,EAAEC,gBAAgB,CAAC,GAAGrD,QAAQ,CAAC,IAAI,CAAC;EACxD,MAAM,CAACsD,aAAa,EAAEC,gBAAgB,CAAC,GAAGvD,QAAQ,CAAC,IAAI,CAAC;EACxD,MAAM,CAACwD,cAAc,EAAEC,iBAAiB,CAAC,GAAGzD,QAAQ,CAAC,IAAI,CAAC;EAC1D,MAAM,CAAC0D,sBAAsB,EAAEC,yBAAyB,CAAC,GAAG3D,QAAQ,CAAC,IAAI,CAAC;EAC1E,MAAM,CAAC4D,eAAe,EAAEC,kBAAkB,CAAC,GAAG7D,QAAQ,CAAC,IAAI,CAAC;;EAE5D;EACA,MAAM8D,kBAAkB,GAAGC,YAAY,CAACC,OAAO,CAAC,oBAAoB,CAAC;EACrE,MAAMC,oBAAoB,GAAGF,YAAY,CAACC,OAAO,CAAC,sBAAsB,CAAC;;EAEzE;EACA,MAAME,cAAc,GAAGA,CAACC,KAAK,EAAEC,WAAW,KAAK;IAC7CA,WAAW,CAACD,KAAK,CAACE,aAAa,CAAC;EAClC,CAAC;EAED,MAAMC,eAAe,GAAIF,WAAW,IAAK;IACvCA,WAAW,CAAC,IAAI,CAAC;EACnB,CAAC;;EAED;EACA,MAAMG,UAAU,GAAIC,IAAI,IAAK;IAC3B;IACA,IAAIA,IAAI,KAAK,YAAY,EAAE;MACzB;MACA,IAAIhC,eAAe,EAAE;QACnBJ,QAAQ,CAAC,kBAAkB,CAAC;MAC9B;MACA;MAAA,KACK,IAAI,CAAAE,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAEmC,IAAI,MAAK,OAAO,EAAE;QAC/BrC,QAAQ,CAAC,kBAAkB,CAAC;MAC9B;MACA;MAAA,KACK,IAAI,CAAAE,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAEmC,IAAI,MAAK,MAAM,EAAE;QAC9BrC,QAAQ,CAAC,qBAAqB,CAAC;MACjC;MACA;MAAA,KACK,IAAI,CAAAE,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAEmC,IAAI,MAAK,eAAe,EAAE;QACvC;QACArC,QAAQ,CAAC,4BAA4B,CAAC;MACxC;MACA;MAAA,KACK;QACHA,QAAQ,CAACoC,IAAI,CAAC;MAChB;IACF,CAAC,MAAM;MACLpC,QAAQ,CAACoC,IAAI,CAAC;IAChB;;IAEA;IACAF,eAAe,CAAC3B,eAAe,CAAC;IAChC2B,eAAe,CAACzB,gBAAgB,CAAC;IACjCyB,eAAe,CAACvB,mBAAmB,CAAC;IACpCuB,eAAe,CAACrB,eAAe,CAAC;IAChCqB,eAAe,CAACnB,eAAe,CAAC;IAChCmB,eAAe,CAACjB,gBAAgB,CAAC;IACjCiB,eAAe,CAACf,gBAAgB,CAAC;IACjCe,eAAe,CAACb,iBAAiB,CAAC;IAClCa,eAAe,CAACX,yBAAyB,CAAC;IAC1CW,eAAe,CAACT,kBAAkB,CAAC;EACrC,CAAC;EAED,MAAMa,YAAY,GAAGA,CAAA,KAAM;IACzBnC,MAAM,CAAC,CAAC;EACV,CAAC;;EAED;EACA,MAAMoC,QAAQ,GAAIH,IAAI,IAAK;IACzB,OAAOnC,QAAQ,CAACuC,QAAQ,KAAKJ,IAAI;EACnC,CAAC;;EAED;EACA,MAAMK,cAAc,GAAIL,IAAI,IAAK;IAC/B,OAAOnC,QAAQ,CAACuC,QAAQ,CAACE,UAAU,CAACN,IAAI,CAAC;EAC3C,CAAC;EAED,oBACEzC,OAAA,CAAC5B,MAAM;IAAC4E,QAAQ,EAAC,QAAQ;IAACC,KAAK,EAAC,SAAS;IAACC,SAAS,EAAE,CAAE;IAACC,EAAE,EAAE;MAAEC,MAAM,EAAE;IAAK,CAAE;IAACC,SAAS,EAAC,kBAAkB;IAAAC,QAAA,eACxGtD,OAAA,CAAC3B,OAAO;MAACkF,OAAO,EAAC,OAAO;MAAAD,QAAA,gBAEtBtD,OAAA,CAACzB,MAAM;QACL0E,KAAK,EAAC,SAAS;QACfO,OAAO,EAAEA,CAAA,KAAMhB,UAAU,CAAC,YAAY,CAAE;QACxCiB,SAAS,eAAEzD,OAAA,CAAChB,QAAQ;UAAA0E,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAE;QACxBV,EAAE,EAAE;UAAEW,EAAE,EAAE;QAAE,CAAE;QACdT,SAAS,EAAET,QAAQ,CAAC,YAAY,CAAC,GAAG,eAAe,GAAG,EAAG;QAAAU,QAAA,EAExD7C,eAAe,GAAG,qBAAqB,GACvC,CAAAF,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAEmC,IAAI,MAAK,OAAO,GAAG,gBAAgB,GACzC,CAAAnC,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAEmC,IAAI,MAAK,MAAM,GAAG,gBAAgB,GACxC,CAAAnC,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAEmC,IAAI,MAAK,eAAe,GAAG,eAAe,GAAG;MAAM;QAAAgB,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACpD,CAAC,eACT7D,OAAA,CAACpB,OAAO;QAACmF,WAAW,EAAC,UAAU;QAACC,QAAQ;QAACb,EAAE,EAAE;UAAEc,EAAE,EAAE;QAAI;MAAE;QAAAP,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC,EAK3D,CAAC,CAAAtD,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAEmC,IAAI,MAAK,OAAO,IAAK,CAAAnC,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAEmC,IAAI,MAAK,OAAO,IAAIjC,eAAe,IAAIC,gBAAiB,kBACzFV,OAAA,CAAAE,SAAA;QAAAoD,QAAA,GAEG,CAAC7C,eAAe,IAAI,CAAAF,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAEmC,IAAI,MAAK,eAAe,kBACjD1C,OAAA,CAACzB,MAAM;UACL0E,KAAK,EAAC,SAAS;UACfO,OAAO,EAAEA,CAAA,KAAMhB,UAAU,CAAC,qBAAqB,CAAE;UACjDW,EAAE,EAAE;YAAEW,EAAE,EAAE;UAAE,CAAE;UACdT,SAAS,EAAET,QAAQ,CAAC,qBAAqB,CAAC,GAAG,eAAe,GAAG,EAAG;UAAAU,QAAA,EAEjE7C,eAAe,IAAIC,gBAAgB,GAAG,eAAeA,gBAAgB,CAACwD,QAAQ,EAAE,GAAG;QAAgB;UAAAR,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC9F,CACT,EAKA9B,kBAAkB,iBACjB/B,OAAA,CAAAE,SAAA;UAAAoD,QAAA,gBAEEtD,OAAA,CAACzB,MAAM;YACL0E,KAAK,EAAC,SAAS;YACfO,OAAO,EAAEA,CAAA,KAAMhB,UAAU,CAAC,4BAA4B,CAAE;YACxDW,EAAE,EAAE;cAAEW,EAAE,EAAE;YAAE,CAAE;YACdT,SAAS,EAAET,QAAQ,CAAC,4BAA4B,CAAC,GAAG,eAAe,GAAG,EAAG;YAAAU,QAAA,EAC1E;UAED;YAAAI,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC,eAGT7D,OAAA,CAACzB,MAAM;YACL0E,KAAK,EAAC,SAAS;YACf,iBAAc,WAAW;YACzB,iBAAc,MAAM;YACpBO,OAAO,EAAGW,CAAC,IAAKhC,cAAc,CAACgC,CAAC,EAAE/C,eAAe,CAAE;YACnDgD,OAAO,eAAEpE,OAAA,CAACJ,aAAa;cAAA8D,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAE;YAC3BV,EAAE,EAAE;cAAEW,EAAE,EAAE;YAAE,CAAE;YACdT,SAAS,EAAEP,cAAc,CAAC,sBAAsB,CAAC,GAAG,eAAe,GAAG,EAAG;YAAAQ,QAAA,EAC1E;UAED;YAAAI,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC,eAGT7D,OAAA,CAACzB,MAAM;YACL0E,KAAK,EAAC,SAAS;YACf,iBAAc,YAAY;YAC1B,iBAAc,MAAM;YACpBO,OAAO,EAAGW,CAAC,IAAKhC,cAAc,CAACgC,CAAC,EAAE7C,gBAAgB,CAAE;YACpD8C,OAAO,eAAEpE,OAAA,CAACJ,aAAa;cAAA8D,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAE;YAC3BV,EAAE,EAAE;cAAEW,EAAE,EAAE;YAAE,CAAE;YACdT,SAAS,EAAEP,cAAc,CAAC,uBAAuB,CAAC,GAAG,eAAe,GAAG,EAAG;YAAAQ,QAAA,EAC3E;UAED;YAAAI,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC,eAGT7D,OAAA,CAACzB,MAAM;YACL0E,KAAK,EAAC,SAAS;YACf,iBAAc,YAAY;YAC1B,iBAAc,MAAM;YACpBO,OAAO,EAAGW,CAAC,IAAKhC,cAAc,CAACgC,CAAC,EAAE3C,gBAAgB,CAAE;YACpD4C,OAAO,eAAEpE,OAAA,CAACJ,aAAa;cAAA8D,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAE;YAC3BV,EAAE,EAAE;cAAEW,EAAE,EAAE;YAAE,CAAE;YACdT,SAAS,EAAEP,cAAc,CAAC,uBAAuB,CAAC,GAAG,eAAe,GAAG,EAAG;YAAAQ,QAAA,EAC3E;UAED;YAAAI,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC,eAGT7D,OAAA,CAACzB,MAAM;YACL0E,KAAK,EAAC,SAAS;YACf,iBAAc,aAAa;YAC3B,iBAAc,MAAM;YACpBO,OAAO,EAAGW,CAAC,IAAKhC,cAAc,CAACgC,CAAC,EAAEzC,iBAAiB,CAAE;YACrD0C,OAAO,eAAEpE,OAAA,CAACJ,aAAa;cAAA8D,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAE;YAC3BV,EAAE,EAAE;cAAEW,EAAE,EAAE;YAAE,CAAE;YACdT,SAAS,EAAEP,cAAc,CAAC,wBAAwB,CAAC,GAAG,eAAe,GAAG,EAAG;YAAAQ,QAAA,EAC5E;UAED;YAAAI,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC,eAGT7D,OAAA,CAACzB,MAAM;YACL0E,KAAK,EAAC,SAAS;YACf,iBAAc,qBAAqB;YACnC,iBAAc,MAAM;YACpBO,OAAO,EAAGW,CAAC,IAAKhC,cAAc,CAACgC,CAAC,EAAEvC,yBAAyB,CAAE;YAC7DwC,OAAO,eAAEpE,OAAA,CAACJ,aAAa;cAAA8D,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAE;YAC3BV,EAAE,EAAE;cAAEW,EAAE,EAAE;YAAE,CAAE;YACdT,SAAS,EAAEP,cAAc,CAAC,gCAAgC,CAAC,GAAG,eAAe,GAAG,EAAG;YAAAQ,QAAA,EACpF;UAED;YAAAI,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC,eAGT7D,OAAA,CAACzB,MAAM;YACL0E,KAAK,EAAC,SAAS;YACf,iBAAc,cAAc;YAC5B,iBAAc,MAAM;YACpBO,OAAO,EAAGW,CAAC,IAAKhC,cAAc,CAACgC,CAAC,EAAErC,kBAAkB,CAAE;YACtDsC,OAAO,eAAEpE,OAAA,CAACJ,aAAa;cAAA8D,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAE;YAC3BV,EAAE,EAAE;cAAEW,EAAE,EAAE;YAAE,CAAE;YACdT,SAAS,EAAEP,cAAc,CAAC,yBAAyB,CAAC,GAAG,eAAe,GAAG,EAAG;YAAAQ,QAAA,EAC7E;UAED;YAAAI,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC,eAGT7D,OAAA,CAACxB,IAAI;YACH6F,EAAE,EAAC,WAAW;YACdC,QAAQ,EAAEnD,YAAa;YACvBoD,WAAW;YACXC,IAAI,EAAEC,OAAO,CAACtD,YAAY,CAAE;YAC5BuD,OAAO,EAAEA,CAAA,KAAMnC,eAAe,CAACnB,eAAe,CAAE;YAChDuD,YAAY,EAAE;cACZC,QAAQ,EAAE,KAAK;cACfC,UAAU,EAAE;YACd,CAAE;YACFC,eAAe,EAAE;cACfF,QAAQ,EAAE,KAAK;cACfC,UAAU,EAAE;YACd,CAAE;YACFxB,SAAS,EAAC,qBAAqB;YAAAC,QAAA,gBAE/BtD,OAAA,CAACvB,QAAQ;cAAC+E,OAAO,EAAEA,CAAA,KAAMhB,UAAU,CAAC,sCAAsC,CAAE;cAAAc,QAAA,EAAC;YAAsB;cAAAI,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAU,CAAC,eAC9G7D,OAAA,CAACvB,QAAQ;cAAC+E,OAAO,EAAEA,CAAA,KAAMhB,UAAU,CAAC,oCAAoC,CAAE;cAAAc,QAAA,EAAC;YAAa;cAAAI,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAU,CAAC,eACnG7D,OAAA,CAACvB,QAAQ;cAAC+E,OAAO,EAAEA,CAAA,KAAMhB,UAAU,CAAC,oCAAoC,CAAE;cAAAc,QAAA,EAAC;YAAmB;cAAAI,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAU,CAAC,eACzG7D,OAAA,CAACvB,QAAQ;cAAC+E,OAAO,EAAEA,CAAA,KAAMhB,UAAU,CAAC,mCAAmC,CAAE;cAAAc,QAAA,EAAC;YAAY;cAAAI,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAU,CAAC,eACjG7D,OAAA,CAACvB,QAAQ;cAAC+E,OAAO,EAAEA,CAAA,KAAMhB,UAAU,CAAC,sCAAsC,CAAE;cAAAc,QAAA,EAAC;YAA2B;cAAAI,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAU,CAAC,eACnH7D,OAAA,CAACvB,QAAQ;cAAC+E,OAAO,EAAEA,CAAA,KAAMhB,UAAU,CAAC,mCAAmC,CAAE;cAAAc,QAAA,EAAC;YAA0B;cAAAI,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAU,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC3G,CAAC,eAGP7D,OAAA,CAACxB,IAAI;YACH6F,EAAE,EAAC,YAAY;YACfC,QAAQ,EAAEjD,aAAc;YACxBkD,WAAW;YACXC,IAAI,EAAEC,OAAO,CAACpD,aAAa,CAAE;YAC7BqD,OAAO,EAAEA,CAAA,KAAMnC,eAAe,CAACjB,gBAAgB,CAAE;YACjDqD,YAAY,EAAE;cACZC,QAAQ,EAAE,KAAK;cACfC,UAAU,EAAE;YACd,CAAE;YACFC,eAAe,EAAE;cACfF,QAAQ,EAAE,KAAK;cACfC,UAAU,EAAE;YACd,CAAE;YACFxB,SAAS,EAAC,qBAAqB;YAAAC,QAAA,gBAE/BtD,OAAA,CAACvB,QAAQ;cAAC+E,OAAO,EAAEA,CAAA,KAAMhB,UAAU,CAAC,kCAAkC,CAAE;cAAAc,QAAA,EAAC;YAAiB;cAAAI,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAU,CAAC,eACrG7D,OAAA,CAACvB,QAAQ;cAAC+E,OAAO,EAAEA,CAAA,KAAMhB,UAAU,CAAC,4BAA4B,CAAE;cAAAc,QAAA,EAAC;YAAiB;cAAAI,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAU,CAAC,eAC/F7D,OAAA,CAACvB,QAAQ;cAAC+E,OAAO,EAAEA,CAAA,KAAMhB,UAAU,CAAC,gCAAgC,CAAE;cAAAc,QAAA,EAAC;YAAe;cAAAI,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAU,CAAC,eACjG7D,OAAA,CAACvB,QAAQ;cAAC+E,OAAO,EAAEA,CAAA,KAAMhB,UAAU,CAAC,+BAA+B,CAAE;cAAAc,QAAA,EAAC;YAAc;cAAAI,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAU,CAAC,eAC/F7D,OAAA,CAACvB,QAAQ;cAAC+E,OAAO,EAAEA,CAAA,KAAMhB,UAAU,CAAC,+BAA+B,CAAE;cAAAc,QAAA,EAAC;YAAgB;cAAAI,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAU,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC7F,CAAC,eAGP7D,OAAA,CAACxB,IAAI;YACH6F,EAAE,EAAC,YAAY;YACfC,QAAQ,EAAE/C,aAAc;YACxBgD,WAAW;YACXC,IAAI,EAAEC,OAAO,CAAClD,aAAa,CAAE;YAC7BmD,OAAO,EAAEA,CAAA,KAAMnC,eAAe,CAACf,gBAAgB,CAAE;YACjDmD,YAAY,EAAE;cACZC,QAAQ,EAAE,KAAK;cACfC,UAAU,EAAE;YACd,CAAE;YACFC,eAAe,EAAE;cACfF,QAAQ,EAAE,KAAK;cACfC,UAAU,EAAE;YACd,CAAE;YACFxB,SAAS,EAAC,qBAAqB;YAAAC,QAAA,gBAE/BtD,OAAA,CAACvB,QAAQ;cAAC+E,OAAO,EAAEA,CAAA,KAAMhB,UAAU,CAAC,oCAAoC,CAAE;cAAAc,QAAA,EAAC;YAAqB;cAAAI,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAU,CAAC,eAC3G7D,OAAA,CAACvB,QAAQ;cAAC+E,OAAO,EAAEA,CAAA,KAAMhB,UAAU,CAAC,sCAAsC,CAAE;cAAAc,QAAA,EAAC;YAAoB;cAAAI,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAU,CAAC,eAC5G7D,OAAA,CAACvB,QAAQ;cAAC+E,OAAO,EAAEA,CAAA,KAAMhB,UAAU,CAAC,oCAAoC,CAAE;cAAAc,QAAA,EAAC;YAAqB;cAAAI,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAU,CAAC,eAC3G7D,OAAA,CAACvB,QAAQ;cAAC+E,OAAO,EAAEA,CAAA,KAAMhB,UAAU,CAAC,sCAAsC,CAAE;cAAAc,QAAA,EAAC;YAAoB;cAAAI,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAU,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACxG,CAAC,eAGP7D,OAAA,CAACxB,IAAI;YACH6F,EAAE,EAAC,aAAa;YAChBC,QAAQ,EAAE7C,cAAe;YACzB8C,WAAW;YACXC,IAAI,EAAEC,OAAO,CAAChD,cAAc,CAAE;YAC9BiD,OAAO,EAAEA,CAAA,KAAMnC,eAAe,CAACb,iBAAiB,CAAE;YAClDiD,YAAY,EAAE;cACZC,QAAQ,EAAE,KAAK;cACfC,UAAU,EAAE;YACd,CAAE;YACFC,eAAe,EAAE;cACfF,QAAQ,EAAE,KAAK;cACfC,UAAU,EAAE;YACd,CAAE;YACFxB,SAAS,EAAC,qBAAqB;YAAAC,QAAA,gBAE/BtD,OAAA,CAACvB,QAAQ;cAAC+E,OAAO,EAAEA,CAAA,KAAMhB,UAAU,CAAC,oCAAoC,CAAE;cAAAc,QAAA,EAAC;YAAkB;cAAAI,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAU,CAAC,eACxG7D,OAAA,CAACvB,QAAQ;cAAC+E,OAAO,EAAEA,CAAA,KAAMhB,UAAU,CAAC,4BAA4B,CAAE;cAAAc,QAAA,EAAC;YAAkB;cAAAI,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAU,CAAC,eAChG7D,OAAA,CAACvB,QAAQ;cAAC+E,OAAO,EAAEA,CAAA,KAAMhB,UAAU,CAAC,wCAAwC,CAAE;cAAAc,QAAA,EAAC;YAAsB;cAAAI,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAU,CAAC,eAChH7D,OAAA,CAACvB,QAAQ;cAAC+E,OAAO,EAAEA,CAAA,KAAMhB,UAAU,CAAC,oCAAoC,CAAE;cAAAc,QAAA,EAAC;YAAoB;cAAAI,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAU,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACtG,CAAC,eAGP7D,OAAA,CAACxB,IAAI;YACH6F,EAAE,EAAC,qBAAqB;YACxBC,QAAQ,EAAE3C,sBAAuB;YACjC4C,WAAW;YACXC,IAAI,EAAEC,OAAO,CAAC9C,sBAAsB,CAAE;YACtC+C,OAAO,EAAEA,CAAA,KAAMnC,eAAe,CAACX,yBAAyB,CAAE;YAC1D+C,YAAY,EAAE;cACZC,QAAQ,EAAE,KAAK;cACfC,UAAU,EAAE;YACd,CAAE;YACFC,eAAe,EAAE;cACfF,QAAQ,EAAE,KAAK;cACfC,UAAU,EAAE;YACd,CAAE;YACFxB,SAAS,EAAC,qBAAqB;YAAAC,QAAA,gBAE/BtD,OAAA,CAACvB,QAAQ;cAAC+E,OAAO,EAAEA,CAAA,KAAMhB,UAAU,CAAC,2CAA2C,CAAE;cAAAc,QAAA,EAAC;YAAyB;cAAAI,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAU,CAAC,eACtH7D,OAAA,CAACvB,QAAQ;cAAC+E,OAAO,EAAEA,CAAA,KAAMhB,UAAU,CAAC,uCAAuC,CAAE;cAAAc,QAAA,EAAC;YAAe;cAAAI,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAU,CAAC,eACxG7D,OAAA,CAACvB,QAAQ;cAAC+E,OAAO,EAAEA,CAAA,KAAMhB,UAAU,CAAC,qCAAqC,CAAE;cAAAc,QAAA,EAAC;YAAmB;cAAAI,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAU,CAAC,eAC1G7D,OAAA,CAACvB,QAAQ;cAAC+E,OAAO,EAAEA,CAAA,KAAMhB,UAAU,CAAC,yCAAyC,CAAE;cAAAc,QAAA,EAAC;YAAuB;cAAAI,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAU,CAAC,eAClH7D,OAAA,CAACvB,QAAQ;cAAC+E,OAAO,EAAEA,CAAA,KAAMhB,UAAU,CAAC,wCAAwC,CAAE;cAAAc,QAAA,EAAC;YAAsB;cAAAI,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAU,CAAC,eAChH7D,OAAA,CAACvB,QAAQ;cAAC+E,OAAO,EAAEA,CAAA,KAAMhB,UAAU,CAAC,0CAA0C,CAAE;cAAAc,QAAA,EAAC;YAAkB;cAAAI,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAU,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC1G,CAAC,eAGP7D,OAAA,CAACxB,IAAI;YACH6F,EAAE,EAAC,cAAc;YACjBC,QAAQ,EAAEzC,eAAgB;YAC1B0C,WAAW;YACXC,IAAI,EAAEC,OAAO,CAAC5C,eAAe,CAAE;YAC/B6C,OAAO,EAAEA,CAAA,KAAMnC,eAAe,CAACT,kBAAkB,CAAE;YACnD6C,YAAY,EAAE;cACZC,QAAQ,EAAE,KAAK;cACfC,UAAU,EAAE;YACd,CAAE;YACFC,eAAe,EAAE;cACfF,QAAQ,EAAE,KAAK;cACfC,UAAU,EAAE;YACd,CAAE;YACFxB,SAAS,EAAC,qBAAqB;YAAAC,QAAA,gBAE/BtD,OAAA,CAACvB,QAAQ;cAAC+E,OAAO,EAAEA,CAAA,KAAMhB,UAAU,CAAC,oCAAoC,CAAE;cAAAc,QAAA,EAAC;YAAkB;cAAAI,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAU,CAAC,eACxG7D,OAAA,CAACvB,QAAQ;cAAC+E,OAAO,EAAEA,CAAA,KAAMhB,UAAU,CAAC,8BAA8B,CAAE;cAAAc,QAAA,EAAC;YAAkB;cAAAI,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAU,CAAC,eAClG7D,OAAA,CAACvB,QAAQ;cAAC+E,OAAO,EAAEA,CAAA,KAAMhB,UAAU,CAAC,kCAAkC,CAAE;cAAAc,QAAA,EAAC;YAAgB;cAAAI,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAU,CAAC,eACpG7D,OAAA,CAACvB,QAAQ;cAAC+E,OAAO,EAAEA,CAAA,KAAMhB,UAAU,CAAC,iCAAiC,CAAE;cAAAc,QAAA,EAAC;YAAe;cAAAI,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAU,CAAC,eAClG7D,OAAA,CAACvB,QAAQ;cAAC+E,OAAO,EAAEA,CAAA,KAAMhB,UAAU,CAAC,gCAAgC,CAAE;cAAAc,QAAA,EAAC;YAAc;cAAAI,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAU,CAAC,eAChG7D,OAAA,CAACvB,QAAQ;cAAC+E,OAAO,EAAEA,CAAA,KAAMhB,UAAU,CAAC,iCAAiC,CAAE;cAAAc,QAAA,EAAC;YAAsB;cAAAI,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAU,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACrG,CAAC;QAAA,eACP,CACH;MAAA,eACD,CACH,eAGD7D,OAAA,CAAC1B,GAAG;QAAC6E,EAAE,EAAE;UAAE4B,QAAQ,EAAE;QAAE;MAAE;QAAArB,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC,eAG5B7D,OAAA,CAAC1B,GAAG;QAAC6E,EAAE,EAAE;UAAE6B,OAAO,EAAE,MAAM;UAAEC,UAAU,EAAE;QAAS,CAAE;QAAA3B,QAAA,gBAEjDtD,OAAA,CAACF,uBAAuB;UAAA4D,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,EAE1BpD,eAAe,IAAIC,gBAAgB,iBAClCV,OAAA,CAACtB,UAAU;UAAC6E,OAAO,EAAC,OAAO;UAACN,KAAK,EAAC,eAAe;UAACE,EAAE,EAAE;YAAEW,EAAE,EAAE;UAAE,CAAE;UAAAR,QAAA,GAAC,gBACjD,eAAAtD,OAAA;YAAAsD,QAAA,EAAI5C,gBAAgB,CAACwD;UAAQ;YAAAR,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACtC,CACb,eACD7D,OAAA,CAACtB,UAAU;UAAC6E,OAAO,EAAC,OAAO;UAACJ,EAAE,EAAE;YAAEW,EAAE,EAAE;UAAE,CAAE;UAAAR,QAAA,EACvC,CAAA/C,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAE2D,QAAQ,KAAI;QAAE;UAAAR,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACX,CAAC,eACb7D,OAAA,CAAClB,OAAO;UAACoG,KAAK,EAAC,QAAQ;UAAA5B,QAAA,eACrBtD,OAAA,CAACrB,UAAU;YAACsE,KAAK,EAAC,SAAS;YAACO,OAAO,EAAEb,YAAa;YAACwC,IAAI,EAAC,KAAK;YAAA7B,QAAA,eAC3DtD,OAAA,CAACN,UAAU;cAAAgE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACJ;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACN,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACP,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACC;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACJ,CAAC;AAEb,CAAC;AAACzD,EAAA,CApYID,SAAS;EAAA,QACIjC,WAAW,EACXC,WAAW,EACgC0B,OAAO;AAAA;AAAAuF,EAAA,GAH/DjF,SAAS;AAsYf,eAAeA,SAAS;AAAC,IAAAiF,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}