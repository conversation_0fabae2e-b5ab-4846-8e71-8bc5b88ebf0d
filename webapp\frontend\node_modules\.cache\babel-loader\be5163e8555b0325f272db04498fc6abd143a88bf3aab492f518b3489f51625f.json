{"ast": null, "code": "var _jsxFileName = \"C:\\\\CMS\\\\webapp\\\\frontend\\\\src\\\\components\\\\comande\\\\CollegamentiDialog.js\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect } from 'react';\nimport { Dialog, DialogTitle, DialogContent, DialogActions, Button, Typography, Box, Alert, Chip, Table, TableBody, TableCell, TableContainer, TableHead, TableRow, Paper, Checkbox, FormControlLabel } from '@mui/material';\nimport { Save as SaveIcon, Cancel as CancelIcon, Link as LinkIcon, CheckCircle as CheckCircleIcon, Warning as WarningIcon } from '@mui/icons-material';\nimport comandeService from '../../services/comandeService';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst CollegamentiDialog = ({\n  open,\n  onClose,\n  comanda,\n  onSuccess\n}) => {\n  _s();\n  const [loading, setLoading] = useState(false);\n  const [error, setError] = useState(null);\n  const [cavi, setCavi] = useState([]);\n  const [caviSelezionati, setCaviSelezionati] = useState({});\n  useEffect(() => {\n    if (open && comanda) {\n      loadCaviComanda();\n    }\n  }, [open, comanda]);\n  const loadCaviComanda = async () => {\n    try {\n      setLoading(true);\n      setError(null);\n      const caviData = await comandeService.getCaviComanda(comanda.codice_comanda);\n      setCavi(caviData);\n\n      // Inizializza la selezione (tutti i cavi selezionati di default)\n      const initialSelection = {};\n      caviData.forEach(cavo => {\n        initialSelection[cavo.id_cavo] = true;\n      });\n      setCaviSelezionati(initialSelection);\n    } catch (err) {\n      console.error('Errore nel caricamento cavi:', err);\n      setError('Errore nel caricamento dei cavi della comanda');\n    } finally {\n      setLoading(false);\n    }\n  };\n  const handleCavoToggle = idCavo => {\n    setCaviSelezionati(prev => ({\n      ...prev,\n      [idCavo]: !prev[idCavo]\n    }));\n  };\n  const handleSave = async () => {\n    try {\n      setLoading(true);\n      setError(null);\n\n      // Prepara i dati di collegamento per i cavi selezionati\n      const datiCollegamento = {};\n      Object.keys(caviSelezionati).forEach(idCavo => {\n        if (caviSelezionati[idCavo]) {\n          datiCollegamento[idCavo] = {\n            responsabile: comanda.responsabile || '',\n            data_collegamento: new Date().toISOString().split('T')[0]\n          };\n        }\n      });\n      if (Object.keys(datiCollegamento).length === 0) {\n        setError('Seleziona almeno un cavo da collegare');\n        return;\n      }\n\n      // Salva i dati di collegamento\n      await comandeService.aggiornaDatiCollegamento(comanda.codice_comanda, datiCollegamento);\n      onSuccess === null || onSuccess === void 0 ? void 0 : onSuccess('Dati di collegamento salvati con successo');\n      onClose();\n    } catch (err) {\n      console.error('Errore nel salvataggio:', err);\n      setError(err.message || 'Errore nel salvataggio dei dati');\n    } finally {\n      setLoading(false);\n    }\n  };\n  const getTipoComandaColor = tipo => {\n    switch (tipo) {\n      case 'COLLEGAMENTO_PARTENZA':\n        return 'secondary';\n      case 'COLLEGAMENTO_ARRIVO':\n        return 'info';\n      default:\n        return 'default';\n    }\n  };\n  const getTipoComandaLabel = tipo => {\n    switch (tipo) {\n      case 'COLLEGAMENTO_PARTENZA':\n        return 'Coll. Partenza';\n      case 'COLLEGAMENTO_ARRIVO':\n        return 'Coll. Arrivo';\n      default:\n        return tipo;\n    }\n  };\n  const getCollegamentoStatus = cavo => {\n    const collegamenti = cavo.collegamenti || 0;\n    const isPartenza = comanda.tipo_comanda === 'COLLEGAMENTO_PARTENZA';\n    if (isPartenza) {\n      return collegamenti & 1 ? 'Già collegato' : 'Da collegare';\n    } else {\n      return collegamenti & 2 ? 'Già collegato' : 'Da collegare';\n    }\n  };\n  const getCollegamentoColor = cavo => {\n    const status = getCollegamentoStatus(cavo);\n    return status === 'Già collegato' ? 'success' : 'warning';\n  };\n  if (!comanda) return null;\n  return /*#__PURE__*/_jsxDEV(Dialog, {\n    open: open,\n    onClose: onClose,\n    maxWidth: \"lg\",\n    fullWidth: true,\n    PaperProps: {\n      sx: {\n        minHeight: '70vh'\n      }\n    },\n    children: [/*#__PURE__*/_jsxDEV(DialogTitle, {\n      children: /*#__PURE__*/_jsxDEV(Box, {\n        display: \"flex\",\n        alignItems: \"center\",\n        gap: 2,\n        children: [/*#__PURE__*/_jsxDEV(LinkIcon, {\n          color: \"primary\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 159,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Box, {\n          children: [/*#__PURE__*/_jsxDEV(Typography, {\n            variant: \"h6\",\n            children: \"Gestione Collegamenti\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 161,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Box, {\n            display: \"flex\",\n            alignItems: \"center\",\n            gap: 1,\n            mt: 1,\n            children: [/*#__PURE__*/_jsxDEV(Typography, {\n              variant: \"body2\",\n              color: \"textSecondary\",\n              children: [\"Comanda: \", comanda.codice_comanda]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 165,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Chip, {\n              label: getTipoComandaLabel(comanda.tipo_comanda),\n              color: getTipoComandaColor(comanda.tipo_comanda),\n              size: \"small\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 168,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Typography, {\n              variant: \"body2\",\n              color: \"textSecondary\",\n              children: [\"Responsabile: \", comanda.responsabile]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 173,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 164,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 160,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 158,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 157,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(DialogContent, {\n      children: [error && /*#__PURE__*/_jsxDEV(Alert, {\n        severity: \"error\",\n        sx: {\n          mb: 2\n        },\n        children: error\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 183,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(Alert, {\n        severity: \"info\",\n        sx: {\n          mb: 2\n        },\n        children: /*#__PURE__*/_jsxDEV(Typography, {\n          variant: \"body2\",\n          children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n            children: \"Istruzioni:\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 190,\n            columnNumber: 13\n          }, this), \" Seleziona i cavi che sono stati collegati fisicamente.\", comanda.tipo_comanda === 'COLLEGAMENTO_PARTENZA' ? ' Questa operazione registrerà il collegamento lato partenza.' : ' Questa operazione registrerà il collegamento lato arrivo.']\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 189,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 188,\n        columnNumber: 9\n      }, this), loading ? /*#__PURE__*/_jsxDEV(Box, {\n        display: \"flex\",\n        justifyContent: \"center\",\n        p: 3,\n        children: /*#__PURE__*/_jsxDEV(Typography, {\n          children: \"Caricamento cavi...\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 200,\n          columnNumber: 13\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 199,\n        columnNumber: 11\n      }, this) : /*#__PURE__*/_jsxDEV(TableContainer, {\n        component: Paper,\n        variant: \"outlined\",\n        children: /*#__PURE__*/_jsxDEV(Table, {\n          size: \"small\",\n          children: [/*#__PURE__*/_jsxDEV(TableHead, {\n            children: /*#__PURE__*/_jsxDEV(TableRow, {\n              children: [/*#__PURE__*/_jsxDEV(TableCell, {\n                children: \"Seleziona\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 207,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n                children: \"ID Cavo\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 208,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n                children: \"Tipologia\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 209,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n                children: \"Formazione\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 210,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n                children: \"Stato Collegamento\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 211,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n                children: \"Stato Installazione\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 212,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 206,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 205,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(TableBody, {\n            children: cavi.map(cavo => /*#__PURE__*/_jsxDEV(TableRow, {\n              children: [/*#__PURE__*/_jsxDEV(TableCell, {\n                children: /*#__PURE__*/_jsxDEV(FormControlLabel, {\n                  control: /*#__PURE__*/_jsxDEV(Checkbox, {\n                    checked: caviSelezionati[cavo.id_cavo] || false,\n                    onChange: () => handleCavoToggle(cavo.id_cavo),\n                    disabled: getCollegamentoStatus(cavo) === 'Già collegato'\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 221,\n                    columnNumber: 27\n                  }, this),\n                  label: \"\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 219,\n                  columnNumber: 23\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 218,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n                children: /*#__PURE__*/_jsxDEV(Typography, {\n                  variant: \"body2\",\n                  fontWeight: \"bold\",\n                  children: cavo.id_cavo\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 231,\n                  columnNumber: 23\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 230,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n                children: cavo.tipologia || 'N/A'\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 235,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n                children: cavo.formazione || 'N/A'\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 236,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n                children: /*#__PURE__*/_jsxDEV(Chip, {\n                  label: getCollegamentoStatus(cavo),\n                  color: getCollegamentoColor(cavo),\n                  size: \"small\",\n                  icon: getCollegamentoStatus(cavo) === 'Già collegato' ? /*#__PURE__*/_jsxDEV(CheckCircleIcon, {}, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 242,\n                    columnNumber: 81\n                  }, this) : /*#__PURE__*/_jsxDEV(WarningIcon, {}, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 242,\n                    columnNumber: 103\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 238,\n                  columnNumber: 23\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 237,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n                children: /*#__PURE__*/_jsxDEV(Chip, {\n                  label: cavo.stato_installazione || 'N/A',\n                  color: cavo.stato_installazione === 'Installato' ? 'success' : 'warning',\n                  size: \"small\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 246,\n                  columnNumber: 23\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 245,\n                columnNumber: 21\n              }, this)]\n            }, cavo.id_cavo, true, {\n              fileName: _jsxFileName,\n              lineNumber: 217,\n              columnNumber: 19\n            }, this))\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 215,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 204,\n          columnNumber: 13\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 203,\n        columnNumber: 11\n      }, this), cavi.length === 0 && !loading && /*#__PURE__*/_jsxDEV(Alert, {\n        severity: \"info\",\n        sx: {\n          mt: 2\n        },\n        children: \"Nessun cavo assegnato a questa comanda.\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 260,\n        columnNumber: 11\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 181,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(DialogActions, {\n      sx: {\n        p: 2,\n        gap: 1\n      },\n      children: [/*#__PURE__*/_jsxDEV(Button, {\n        onClick: onClose,\n        startIcon: /*#__PURE__*/_jsxDEV(CancelIcon, {}, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 269,\n          columnNumber: 22\n        }, this),\n        disabled: loading,\n        children: \"Annulla\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 267,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Button, {\n        onClick: handleSave,\n        variant: \"contained\",\n        startIcon: /*#__PURE__*/_jsxDEV(SaveIcon, {}, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 277,\n          columnNumber: 22\n        }, this),\n        disabled: loading || cavi.length === 0 || Object.values(caviSelezionati).every(v => !v),\n        children: \"Salva Collegamenti\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 274,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 266,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 148,\n    columnNumber: 5\n  }, this);\n};\n_s(CollegamentiDialog, \"Ut4jl/3bJqdv/PKDGHHbY3Rxq2M=\");\n_c = CollegamentiDialog;\nexport default CollegamentiDialog;\nvar _c;\n$RefreshReg$(_c, \"CollegamentiDialog\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "Dialog", "DialogTitle", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "DialogActions", "<PERSON><PERSON>", "Typography", "Box", "<PERSON><PERSON>", "Chip", "Table", "TableBody", "TableCell", "TableContainer", "TableHead", "TableRow", "Paper", "Checkbox", "FormControlLabel", "Save", "SaveIcon", "Cancel", "CancelIcon", "Link", "LinkIcon", "CheckCircle", "CheckCircleIcon", "Warning", "WarningIcon", "comandeService", "jsxDEV", "_jsxDEV", "CollegamentiDialog", "open", "onClose", "comanda", "onSuccess", "_s", "loading", "setLoading", "error", "setError", "cavi", "<PERSON><PERSON><PERSON>", "caviSelezionati", "setCaviSelezionati", "loadCaviComanda", "caviData", "getCaviComanda", "codice_comanda", "initialSelection", "for<PERSON>ach", "cavo", "id_cavo", "err", "console", "handleCavoToggle", "idCavo", "prev", "handleSave", "datiCollegamento", "Object", "keys", "responsabile", "data_collegamento", "Date", "toISOString", "split", "length", "aggiornaDatiCollegamento", "message", "getTipoComandaColor", "tipo", "getTipoComandaLabel", "getCollegamentoStatus", "colle<PERSON>nti", "isPartenza", "tipo_comanda", "getCollegamentoColor", "status", "max<PERSON><PERSON><PERSON>", "fullWidth", "PaperProps", "sx", "minHeight", "children", "display", "alignItems", "gap", "color", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "variant", "mt", "label", "size", "severity", "mb", "justifyContent", "p", "component", "map", "control", "checked", "onChange", "disabled", "fontWeight", "tipologia", "formazione", "icon", "stato_installazione", "onClick", "startIcon", "values", "every", "v", "_c", "$RefreshReg$"], "sources": ["C:/CMS/webapp/frontend/src/components/comande/CollegamentiDialog.js"], "sourcesContent": ["import React, { useState, useEffect } from 'react';\nimport {\n  Dialog,\n  DialogTitle,\n  DialogContent,\n  DialogActions,\n  Button,\n  Typography,\n  Box,\n  Alert,\n  Chip,\n  Table,\n  TableBody,\n  TableCell,\n  TableContainer,\n  TableHead,\n  TableRow,\n  Paper,\n  Checkbox,\n  FormControlLabel\n} from '@mui/material';\nimport {\n  Save as SaveIcon,\n  Cancel as CancelIcon,\n  Link as LinkIcon,\n  CheckCircle as CheckCircleIcon,\n  Warning as WarningIcon\n} from '@mui/icons-material';\nimport comandeService from '../../services/comandeService';\n\nconst CollegamentiDialog = ({\n  open,\n  onClose,\n  comanda,\n  onSuccess\n}) => {\n  const [loading, setLoading] = useState(false);\n  const [error, setError] = useState(null);\n  const [cavi, setCavi] = useState([]);\n  const [caviSelezionati, setCaviSelezionati] = useState({});\n\n  useEffect(() => {\n    if (open && comanda) {\n      loadCaviComanda();\n    }\n  }, [open, comanda]);\n\n  const loadCaviComanda = async () => {\n    try {\n      setLoading(true);\n      setError(null);\n      \n      const caviData = await comandeService.getCaviComanda(comanda.codice_comanda);\n      setCavi(caviData);\n      \n      // Inizializza la selezione (tutti i cavi selezionati di default)\n      const initialSelection = {};\n      caviData.forEach(cavo => {\n        initialSelection[cavo.id_cavo] = true;\n      });\n      setCaviSelezionati(initialSelection);\n      \n    } catch (err) {\n      console.error('Errore nel caricamento cavi:', err);\n      setError('Errore nel caricamento dei cavi della comanda');\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  const handleCavoToggle = (idCavo) => {\n    setCaviSelezionati(prev => ({\n      ...prev,\n      [idCavo]: !prev[idCavo]\n    }));\n  };\n\n  const handleSave = async () => {\n    try {\n      setLoading(true);\n      setError(null);\n\n      // Prepara i dati di collegamento per i cavi selezionati\n      const datiCollegamento = {};\n      Object.keys(caviSelezionati).forEach(idCavo => {\n        if (caviSelezionati[idCavo]) {\n          datiCollegamento[idCavo] = {\n            responsabile: comanda.responsabile || '',\n            data_collegamento: new Date().toISOString().split('T')[0]\n          };\n        }\n      });\n\n      if (Object.keys(datiCollegamento).length === 0) {\n        setError('Seleziona almeno un cavo da collegare');\n        return;\n      }\n\n      // Salva i dati di collegamento\n      await comandeService.aggiornaDatiCollegamento(comanda.codice_comanda, datiCollegamento);\n      \n      onSuccess?.('Dati di collegamento salvati con successo');\n      onClose();\n      \n    } catch (err) {\n      console.error('Errore nel salvataggio:', err);\n      setError(err.message || 'Errore nel salvataggio dei dati');\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  const getTipoComandaColor = (tipo) => {\n    switch (tipo) {\n      case 'COLLEGAMENTO_PARTENZA': return 'secondary';\n      case 'COLLEGAMENTO_ARRIVO': return 'info';\n      default: return 'default';\n    }\n  };\n\n  const getTipoComandaLabel = (tipo) => {\n    switch (tipo) {\n      case 'COLLEGAMENTO_PARTENZA': return 'Coll. Partenza';\n      case 'COLLEGAMENTO_ARRIVO': return 'Coll. Arrivo';\n      default: return tipo;\n    }\n  };\n\n  const getCollegamentoStatus = (cavo) => {\n    const collegamenti = cavo.collegamenti || 0;\n    const isPartenza = comanda.tipo_comanda === 'COLLEGAMENTO_PARTENZA';\n    \n    if (isPartenza) {\n      return collegamenti & 1 ? 'Già collegato' : 'Da collegare';\n    } else {\n      return collegamenti & 2 ? 'Già collegato' : 'Da collegare';\n    }\n  };\n\n  const getCollegamentoColor = (cavo) => {\n    const status = getCollegamentoStatus(cavo);\n    return status === 'Già collegato' ? 'success' : 'warning';\n  };\n\n  if (!comanda) return null;\n\n  return (\n    <Dialog \n      open={open} \n      onClose={onClose}\n      maxWidth=\"lg\"\n      fullWidth\n      PaperProps={{\n        sx: { minHeight: '70vh' }\n      }}\n    >\n      <DialogTitle>\n        <Box display=\"flex\" alignItems=\"center\" gap={2}>\n          <LinkIcon color=\"primary\" />\n          <Box>\n            <Typography variant=\"h6\">\n              Gestione Collegamenti\n            </Typography>\n            <Box display=\"flex\" alignItems=\"center\" gap={1} mt={1}>\n              <Typography variant=\"body2\" color=\"textSecondary\">\n                Comanda: {comanda.codice_comanda}\n              </Typography>\n              <Chip \n                label={getTipoComandaLabel(comanda.tipo_comanda)}\n                color={getTipoComandaColor(comanda.tipo_comanda)}\n                size=\"small\"\n              />\n              <Typography variant=\"body2\" color=\"textSecondary\">\n                Responsabile: {comanda.responsabile}\n              </Typography>\n            </Box>\n          </Box>\n        </Box>\n      </DialogTitle>\n\n      <DialogContent>\n        {error && (\n          <Alert severity=\"error\" sx={{ mb: 2 }}>\n            {error}\n          </Alert>\n        )}\n\n        <Alert severity=\"info\" sx={{ mb: 2 }}>\n          <Typography variant=\"body2\">\n            <strong>Istruzioni:</strong> Seleziona i cavi che sono stati collegati fisicamente.\n            {comanda.tipo_comanda === 'COLLEGAMENTO_PARTENZA' \n              ? ' Questa operazione registrerà il collegamento lato partenza.'\n              : ' Questa operazione registrerà il collegamento lato arrivo.'\n            }\n          </Typography>\n        </Alert>\n\n        {loading ? (\n          <Box display=\"flex\" justifyContent=\"center\" p={3}>\n            <Typography>Caricamento cavi...</Typography>\n          </Box>\n        ) : (\n          <TableContainer component={Paper} variant=\"outlined\">\n            <Table size=\"small\">\n              <TableHead>\n                <TableRow>\n                  <TableCell>Seleziona</TableCell>\n                  <TableCell>ID Cavo</TableCell>\n                  <TableCell>Tipologia</TableCell>\n                  <TableCell>Formazione</TableCell>\n                  <TableCell>Stato Collegamento</TableCell>\n                  <TableCell>Stato Installazione</TableCell>\n                </TableRow>\n              </TableHead>\n              <TableBody>\n                {cavi.map((cavo) => (\n                  <TableRow key={cavo.id_cavo}>\n                    <TableCell>\n                      <FormControlLabel\n                        control={\n                          <Checkbox\n                            checked={caviSelezionati[cavo.id_cavo] || false}\n                            onChange={() => handleCavoToggle(cavo.id_cavo)}\n                            disabled={getCollegamentoStatus(cavo) === 'Già collegato'}\n                          />\n                        }\n                        label=\"\"\n                      />\n                    </TableCell>\n                    <TableCell>\n                      <Typography variant=\"body2\" fontWeight=\"bold\">\n                        {cavo.id_cavo}\n                      </Typography>\n                    </TableCell>\n                    <TableCell>{cavo.tipologia || 'N/A'}</TableCell>\n                    <TableCell>{cavo.formazione || 'N/A'}</TableCell>\n                    <TableCell>\n                      <Chip \n                        label={getCollegamentoStatus(cavo)}\n                        color={getCollegamentoColor(cavo)}\n                        size=\"small\"\n                        icon={getCollegamentoStatus(cavo) === 'Già collegato' ? <CheckCircleIcon /> : <WarningIcon />}\n                      />\n                    </TableCell>\n                    <TableCell>\n                      <Chip \n                        label={cavo.stato_installazione || 'N/A'}\n                        color={cavo.stato_installazione === 'Installato' ? 'success' : 'warning'}\n                        size=\"small\"\n                      />\n                    </TableCell>\n                  </TableRow>\n                ))}\n              </TableBody>\n            </Table>\n          </TableContainer>\n        )}\n\n        {cavi.length === 0 && !loading && (\n          <Alert severity=\"info\" sx={{ mt: 2 }}>\n            Nessun cavo assegnato a questa comanda.\n          </Alert>\n        )}\n      </DialogContent>\n\n      <DialogActions sx={{ p: 2, gap: 1 }}>\n        <Button\n          onClick={onClose}\n          startIcon={<CancelIcon />}\n          disabled={loading}\n        >\n          Annulla\n        </Button>\n        <Button\n          onClick={handleSave}\n          variant=\"contained\"\n          startIcon={<SaveIcon />}\n          disabled={loading || cavi.length === 0 || Object.values(caviSelezionati).every(v => !v)}\n        >\n          Salva Collegamenti\n        </Button>\n      </DialogActions>\n    </Dialog>\n  );\n};\n\nexport default CollegamentiDialog;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAClD,SACEC,MAAM,EACNC,WAAW,EACXC,aAAa,EACbC,aAAa,EACbC,MAAM,EACNC,UAAU,EACVC,GAAG,EACHC,KAAK,EACLC,IAAI,EACJC,KAAK,EACLC,SAAS,EACTC,SAAS,EACTC,cAAc,EACdC,SAAS,EACTC,QAAQ,EACRC,KAAK,EACLC,QAAQ,EACRC,gBAAgB,QACX,eAAe;AACtB,SACEC,IAAI,IAAIC,QAAQ,EAChBC,MAAM,IAAIC,UAAU,EACpBC,IAAI,IAAIC,QAAQ,EAChBC,WAAW,IAAIC,eAAe,EAC9BC,OAAO,IAAIC,WAAW,QACjB,qBAAqB;AAC5B,OAAOC,cAAc,MAAM,+BAA+B;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAE3D,MAAMC,kBAAkB,GAAGA,CAAC;EAC1BC,IAAI;EACJC,OAAO;EACPC,OAAO;EACPC;AACF,CAAC,KAAK;EAAAC,EAAA;EACJ,MAAM,CAACC,OAAO,EAAEC,UAAU,CAAC,GAAGxC,QAAQ,CAAC,KAAK,CAAC;EAC7C,MAAM,CAACyC,KAAK,EAAEC,QAAQ,CAAC,GAAG1C,QAAQ,CAAC,IAAI,CAAC;EACxC,MAAM,CAAC2C,IAAI,EAAEC,OAAO,CAAC,GAAG5C,QAAQ,CAAC,EAAE,CAAC;EACpC,MAAM,CAAC6C,eAAe,EAAEC,kBAAkB,CAAC,GAAG9C,QAAQ,CAAC,CAAC,CAAC,CAAC;EAE1DC,SAAS,CAAC,MAAM;IACd,IAAIiC,IAAI,IAAIE,OAAO,EAAE;MACnBW,eAAe,CAAC,CAAC;IACnB;EACF,CAAC,EAAE,CAACb,IAAI,EAAEE,OAAO,CAAC,CAAC;EAEnB,MAAMW,eAAe,GAAG,MAAAA,CAAA,KAAY;IAClC,IAAI;MACFP,UAAU,CAAC,IAAI,CAAC;MAChBE,QAAQ,CAAC,IAAI,CAAC;MAEd,MAAMM,QAAQ,GAAG,MAAMlB,cAAc,CAACmB,cAAc,CAACb,OAAO,CAACc,cAAc,CAAC;MAC5EN,OAAO,CAACI,QAAQ,CAAC;;MAEjB;MACA,MAAMG,gBAAgB,GAAG,CAAC,CAAC;MAC3BH,QAAQ,CAACI,OAAO,CAACC,IAAI,IAAI;QACvBF,gBAAgB,CAACE,IAAI,CAACC,OAAO,CAAC,GAAG,IAAI;MACvC,CAAC,CAAC;MACFR,kBAAkB,CAACK,gBAAgB,CAAC;IAEtC,CAAC,CAAC,OAAOI,GAAG,EAAE;MACZC,OAAO,CAACf,KAAK,CAAC,8BAA8B,EAAEc,GAAG,CAAC;MAClDb,QAAQ,CAAC,+CAA+C,CAAC;IAC3D,CAAC,SAAS;MACRF,UAAU,CAAC,KAAK,CAAC;IACnB;EACF,CAAC;EAED,MAAMiB,gBAAgB,GAAIC,MAAM,IAAK;IACnCZ,kBAAkB,CAACa,IAAI,KAAK;MAC1B,GAAGA,IAAI;MACP,CAACD,MAAM,GAAG,CAACC,IAAI,CAACD,MAAM;IACxB,CAAC,CAAC,CAAC;EACL,CAAC;EAED,MAAME,UAAU,GAAG,MAAAA,CAAA,KAAY;IAC7B,IAAI;MACFpB,UAAU,CAAC,IAAI,CAAC;MAChBE,QAAQ,CAAC,IAAI,CAAC;;MAEd;MACA,MAAMmB,gBAAgB,GAAG,CAAC,CAAC;MAC3BC,MAAM,CAACC,IAAI,CAAClB,eAAe,CAAC,CAACO,OAAO,CAACM,MAAM,IAAI;QAC7C,IAAIb,eAAe,CAACa,MAAM,CAAC,EAAE;UAC3BG,gBAAgB,CAACH,MAAM,CAAC,GAAG;YACzBM,YAAY,EAAE5B,OAAO,CAAC4B,YAAY,IAAI,EAAE;YACxCC,iBAAiB,EAAE,IAAIC,IAAI,CAAC,CAAC,CAACC,WAAW,CAAC,CAAC,CAACC,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC;UAC1D,CAAC;QACH;MACF,CAAC,CAAC;MAEF,IAAIN,MAAM,CAACC,IAAI,CAACF,gBAAgB,CAAC,CAACQ,MAAM,KAAK,CAAC,EAAE;QAC9C3B,QAAQ,CAAC,uCAAuC,CAAC;QACjD;MACF;;MAEA;MACA,MAAMZ,cAAc,CAACwC,wBAAwB,CAAClC,OAAO,CAACc,cAAc,EAAEW,gBAAgB,CAAC;MAEvFxB,SAAS,aAATA,SAAS,uBAATA,SAAS,CAAG,2CAA2C,CAAC;MACxDF,OAAO,CAAC,CAAC;IAEX,CAAC,CAAC,OAAOoB,GAAG,EAAE;MACZC,OAAO,CAACf,KAAK,CAAC,yBAAyB,EAAEc,GAAG,CAAC;MAC7Cb,QAAQ,CAACa,GAAG,CAACgB,OAAO,IAAI,iCAAiC,CAAC;IAC5D,CAAC,SAAS;MACR/B,UAAU,CAAC,KAAK,CAAC;IACnB;EACF,CAAC;EAED,MAAMgC,mBAAmB,GAAIC,IAAI,IAAK;IACpC,QAAQA,IAAI;MACV,KAAK,uBAAuB;QAAE,OAAO,WAAW;MAChD,KAAK,qBAAqB;QAAE,OAAO,MAAM;MACzC;QAAS,OAAO,SAAS;IAC3B;EACF,CAAC;EAED,MAAMC,mBAAmB,GAAID,IAAI,IAAK;IACpC,QAAQA,IAAI;MACV,KAAK,uBAAuB;QAAE,OAAO,gBAAgB;MACrD,KAAK,qBAAqB;QAAE,OAAO,cAAc;MACjD;QAAS,OAAOA,IAAI;IACtB;EACF,CAAC;EAED,MAAME,qBAAqB,GAAItB,IAAI,IAAK;IACtC,MAAMuB,YAAY,GAAGvB,IAAI,CAACuB,YAAY,IAAI,CAAC;IAC3C,MAAMC,UAAU,GAAGzC,OAAO,CAAC0C,YAAY,KAAK,uBAAuB;IAEnE,IAAID,UAAU,EAAE;MACd,OAAOD,YAAY,GAAG,CAAC,GAAG,eAAe,GAAG,cAAc;IAC5D,CAAC,MAAM;MACL,OAAOA,YAAY,GAAG,CAAC,GAAG,eAAe,GAAG,cAAc;IAC5D;EACF,CAAC;EAED,MAAMG,oBAAoB,GAAI1B,IAAI,IAAK;IACrC,MAAM2B,MAAM,GAAGL,qBAAqB,CAACtB,IAAI,CAAC;IAC1C,OAAO2B,MAAM,KAAK,eAAe,GAAG,SAAS,GAAG,SAAS;EAC3D,CAAC;EAED,IAAI,CAAC5C,OAAO,EAAE,OAAO,IAAI;EAEzB,oBACEJ,OAAA,CAAC9B,MAAM;IACLgC,IAAI,EAAEA,IAAK;IACXC,OAAO,EAAEA,OAAQ;IACjB8C,QAAQ,EAAC,IAAI;IACbC,SAAS;IACTC,UAAU,EAAE;MACVC,EAAE,EAAE;QAAEC,SAAS,EAAE;MAAO;IAC1B,CAAE;IAAAC,QAAA,gBAEFtD,OAAA,CAAC7B,WAAW;MAAAmF,QAAA,eACVtD,OAAA,CAACxB,GAAG;QAAC+E,OAAO,EAAC,MAAM;QAACC,UAAU,EAAC,QAAQ;QAACC,GAAG,EAAE,CAAE;QAAAH,QAAA,gBAC7CtD,OAAA,CAACP,QAAQ;UAACiE,KAAK,EAAC;QAAS;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,eAC5B9D,OAAA,CAACxB,GAAG;UAAA8E,QAAA,gBACFtD,OAAA,CAACzB,UAAU;YAACwF,OAAO,EAAC,IAAI;YAAAT,QAAA,EAAC;UAEzB;YAAAK,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAY,CAAC,eACb9D,OAAA,CAACxB,GAAG;YAAC+E,OAAO,EAAC,MAAM;YAACC,UAAU,EAAC,QAAQ;YAACC,GAAG,EAAE,CAAE;YAACO,EAAE,EAAE,CAAE;YAAAV,QAAA,gBACpDtD,OAAA,CAACzB,UAAU;cAACwF,OAAO,EAAC,OAAO;cAACL,KAAK,EAAC,eAAe;cAAAJ,QAAA,GAAC,WACvC,EAAClD,OAAO,CAACc,cAAc;YAAA;cAAAyC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACtB,CAAC,eACb9D,OAAA,CAACtB,IAAI;cACHuF,KAAK,EAAEvB,mBAAmB,CAACtC,OAAO,CAAC0C,YAAY,CAAE;cACjDY,KAAK,EAAElB,mBAAmB,CAACpC,OAAO,CAAC0C,YAAY,CAAE;cACjDoB,IAAI,EAAC;YAAO;cAAAP,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACb,CAAC,eACF9D,OAAA,CAACzB,UAAU;cAACwF,OAAO,EAAC,OAAO;cAACL,KAAK,EAAC,eAAe;cAAAJ,QAAA,GAAC,gBAClC,EAAClD,OAAO,CAAC4B,YAAY;YAAA;cAAA2B,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACzB,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACV,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACK,CAAC,eAEd9D,OAAA,CAAC5B,aAAa;MAAAkF,QAAA,GACX7C,KAAK,iBACJT,OAAA,CAACvB,KAAK;QAAC0F,QAAQ,EAAC,OAAO;QAACf,EAAE,EAAE;UAAEgB,EAAE,EAAE;QAAE,CAAE;QAAAd,QAAA,EACnC7C;MAAK;QAAAkD,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACD,CACR,eAED9D,OAAA,CAACvB,KAAK;QAAC0F,QAAQ,EAAC,MAAM;QAACf,EAAE,EAAE;UAAEgB,EAAE,EAAE;QAAE,CAAE;QAAAd,QAAA,eACnCtD,OAAA,CAACzB,UAAU;UAACwF,OAAO,EAAC,OAAO;UAAAT,QAAA,gBACzBtD,OAAA;YAAAsD,QAAA,EAAQ;UAAW;YAAAK,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC,2DAC5B,EAAC1D,OAAO,CAAC0C,YAAY,KAAK,uBAAuB,GAC7C,8DAA8D,GAC9D,4DAA4D;QAAA;UAAAa,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAEtD;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACR,CAAC,EAEPvD,OAAO,gBACNP,OAAA,CAACxB,GAAG;QAAC+E,OAAO,EAAC,MAAM;QAACc,cAAc,EAAC,QAAQ;QAACC,CAAC,EAAE,CAAE;QAAAhB,QAAA,eAC/CtD,OAAA,CAACzB,UAAU;UAAA+E,QAAA,EAAC;QAAmB;UAAAK,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAY;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACzC,CAAC,gBAEN9D,OAAA,CAAClB,cAAc;QAACyF,SAAS,EAAEtF,KAAM;QAAC8E,OAAO,EAAC,UAAU;QAAAT,QAAA,eAClDtD,OAAA,CAACrB,KAAK;UAACuF,IAAI,EAAC,OAAO;UAAAZ,QAAA,gBACjBtD,OAAA,CAACjB,SAAS;YAAAuE,QAAA,eACRtD,OAAA,CAAChB,QAAQ;cAAAsE,QAAA,gBACPtD,OAAA,CAACnB,SAAS;gBAAAyE,QAAA,EAAC;cAAS;gBAAAK,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAW,CAAC,eAChC9D,OAAA,CAACnB,SAAS;gBAAAyE,QAAA,EAAC;cAAO;gBAAAK,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAW,CAAC,eAC9B9D,OAAA,CAACnB,SAAS;gBAAAyE,QAAA,EAAC;cAAS;gBAAAK,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAW,CAAC,eAChC9D,OAAA,CAACnB,SAAS;gBAAAyE,QAAA,EAAC;cAAU;gBAAAK,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAW,CAAC,eACjC9D,OAAA,CAACnB,SAAS;gBAAAyE,QAAA,EAAC;cAAkB;gBAAAK,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAW,CAAC,eACzC9D,OAAA,CAACnB,SAAS;gBAAAyE,QAAA,EAAC;cAAmB;gBAAAK,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAW,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAClC;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACF,CAAC,eACZ9D,OAAA,CAACpB,SAAS;YAAA0E,QAAA,EACP3C,IAAI,CAAC6D,GAAG,CAAEnD,IAAI,iBACbrB,OAAA,CAAChB,QAAQ;cAAAsE,QAAA,gBACPtD,OAAA,CAACnB,SAAS;gBAAAyE,QAAA,eACRtD,OAAA,CAACb,gBAAgB;kBACfsF,OAAO,eACLzE,OAAA,CAACd,QAAQ;oBACPwF,OAAO,EAAE7D,eAAe,CAACQ,IAAI,CAACC,OAAO,CAAC,IAAI,KAAM;oBAChDqD,QAAQ,EAAEA,CAAA,KAAMlD,gBAAgB,CAACJ,IAAI,CAACC,OAAO,CAAE;oBAC/CsD,QAAQ,EAAEjC,qBAAqB,CAACtB,IAAI,CAAC,KAAK;kBAAgB;oBAAAsC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAC3D,CACF;kBACDG,KAAK,EAAC;gBAAE;kBAAAN,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACT;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACO,CAAC,eACZ9D,OAAA,CAACnB,SAAS;gBAAAyE,QAAA,eACRtD,OAAA,CAACzB,UAAU;kBAACwF,OAAO,EAAC,OAAO;kBAACc,UAAU,EAAC,MAAM;kBAAAvB,QAAA,EAC1CjC,IAAI,CAACC;gBAAO;kBAAAqC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACJ,CAAC,eACZ9D,OAAA,CAACnB,SAAS;gBAAAyE,QAAA,EAAEjC,IAAI,CAACyD,SAAS,IAAI;cAAK;gBAAAnB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAY,CAAC,eAChD9D,OAAA,CAACnB,SAAS;gBAAAyE,QAAA,EAAEjC,IAAI,CAAC0D,UAAU,IAAI;cAAK;gBAAApB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAY,CAAC,eACjD9D,OAAA,CAACnB,SAAS;gBAAAyE,QAAA,eACRtD,OAAA,CAACtB,IAAI;kBACHuF,KAAK,EAAEtB,qBAAqB,CAACtB,IAAI,CAAE;kBACnCqC,KAAK,EAAEX,oBAAoB,CAAC1B,IAAI,CAAE;kBAClC6C,IAAI,EAAC,OAAO;kBACZc,IAAI,EAAErC,qBAAqB,CAACtB,IAAI,CAAC,KAAK,eAAe,gBAAGrB,OAAA,CAACL,eAAe;oBAAAgE,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE,CAAC,gBAAG9D,OAAA,CAACH,WAAW;oBAAA8D,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE;gBAAE;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAC/F;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACO,CAAC,eACZ9D,OAAA,CAACnB,SAAS;gBAAAyE,QAAA,eACRtD,OAAA,CAACtB,IAAI;kBACHuF,KAAK,EAAE5C,IAAI,CAAC4D,mBAAmB,IAAI,KAAM;kBACzCvB,KAAK,EAAErC,IAAI,CAAC4D,mBAAmB,KAAK,YAAY,GAAG,SAAS,GAAG,SAAU;kBACzEf,IAAI,EAAC;gBAAO;kBAAAP,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACb;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACO,CAAC;YAAA,GAlCCzC,IAAI,CAACC,OAAO;cAAAqC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAmCjB,CACX;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACO,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACP;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACM,CACjB,EAEAnD,IAAI,CAAC0B,MAAM,KAAK,CAAC,IAAI,CAAC9B,OAAO,iBAC5BP,OAAA,CAACvB,KAAK;QAAC0F,QAAQ,EAAC,MAAM;QAACf,EAAE,EAAE;UAAEY,EAAE,EAAE;QAAE,CAAE;QAAAV,QAAA,EAAC;MAEtC;QAAAK,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAO,CACR;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACY,CAAC,eAEhB9D,OAAA,CAAC3B,aAAa;MAAC+E,EAAE,EAAE;QAAEkB,CAAC,EAAE,CAAC;QAAEb,GAAG,EAAE;MAAE,CAAE;MAAAH,QAAA,gBAClCtD,OAAA,CAAC1B,MAAM;QACL4G,OAAO,EAAE/E,OAAQ;QACjBgF,SAAS,eAAEnF,OAAA,CAACT,UAAU;UAAAoE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAE;QAC1Bc,QAAQ,EAAErE,OAAQ;QAAA+C,QAAA,EACnB;MAED;QAAAK,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAQ,CAAC,eACT9D,OAAA,CAAC1B,MAAM;QACL4G,OAAO,EAAEtD,UAAW;QACpBmC,OAAO,EAAC,WAAW;QACnBoB,SAAS,eAAEnF,OAAA,CAACX,QAAQ;UAAAsE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAE;QACxBc,QAAQ,EAAErE,OAAO,IAAII,IAAI,CAAC0B,MAAM,KAAK,CAAC,IAAIP,MAAM,CAACsD,MAAM,CAACvE,eAAe,CAAC,CAACwE,KAAK,CAACC,CAAC,IAAI,CAACA,CAAC,CAAE;QAAAhC,QAAA,EACzF;MAED;QAAAK,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAQ,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACI,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACV,CAAC;AAEb,CAAC;AAACxD,EAAA,CA9PIL,kBAAkB;AAAAsF,EAAA,GAAlBtF,kBAAkB;AAgQxB,eAAeA,kBAAkB;AAAC,IAAAsF,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}