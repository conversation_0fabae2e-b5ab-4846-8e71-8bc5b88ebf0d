{"ast": null, "code": "var _jsxFileName = \"C:\\\\CMS\\\\webapp\\\\frontend\\\\src\\\\components\\\\cavi\\\\InserisciMetriDialogCompleto.js\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect, useCallback } from 'react';\nimport { Dialog, DialogTitle, DialogContent, DialogActions, Button, TextField, Typography, Box, Grid, Paper, Alert, CircularProgress, FormControl, InputLabel, Select, MenuItem, Chip } from '@mui/material';\nimport caviService from '../../services/caviService';\nimport parcoCaviService from '../../services/parcoCaviService';\n\n/**\n * Dialog completo per inserire i metri posati di un cavo preselezionato\n * Basato su InserisciMetriForm ma adattato per dialog con cavo preselezionato\n * \n * @param {Object} props - Proprietà del componente\n * @param {boolean} props.open - Se il dialogo è aperto\n * @param {Function} props.onClose - Funzione chiamata quando il dialogo si chiude\n * @param {Object} props.cavo - Cavo preselezionato\n * @param {string} props.cantiereId - ID del cantiere\n * @param {Function} props.onSuccess - Funzione chiamata al successo dell'operazione\n * @param {Function} props.onError - Funzione chiamata in caso di errore\n * @param {boolean} props.loading - Indica se il salvataggio è in corso\n */\nimport { jsxDEV as _jsxDEV, Fragment as _Fragment } from \"react/jsx-dev-runtime\";\nconst InserisciMetriDialogCompleto = ({\n  open = false,\n  onClose = () => {},\n  cavo = null,\n  cantiereId,\n  onSuccess = () => {},\n  onError = () => {},\n  loading = false\n}) => {\n  _s();\n  // Stati per il form\n  const [formData, setFormData] = useState({\n    metri_posati: '',\n    id_bobina: ''\n  });\n  const [formErrors, setFormErrors] = useState({});\n  const [formWarnings, setFormWarnings] = useState({});\n  const [saving, setSaving] = useState(false);\n\n  // Stati per bobine\n  const [bobine, setBobine] = useState([]);\n  const [bobineLoading, setBobineLoading] = useState(false);\n\n  // Reset quando si apre il dialogo\n  useEffect(() => {\n    if (open && cavo) {\n      var _cavo$metratura_reale;\n      setFormData({\n        metri_posati: ((_cavo$metratura_reale = cavo.metratura_reale) === null || _cavo$metratura_reale === void 0 ? void 0 : _cavo$metratura_reale.toString()) || '',\n        id_bobina: ''\n      });\n      setFormErrors({});\n      setFormWarnings({});\n      setSaving(false);\n\n      // Carica le bobine disponibili\n      loadBobine();\n    }\n  }, [open, cavo, cantiereId, loadBobine]);\n\n  // Carica le bobine disponibili\n  const loadBobine = useCallback(async () => {\n    if (!cantiereId || !cavo) return;\n    try {\n      setBobineLoading(true);\n      console.log('Caricamento bobine per cantiere:', cantiereId, 'e cavo:', cavo.id_cavo);\n      const bobineData = await parcoCaviService.getBobine(cantiereId);\n      console.log('Bobine caricate:', bobineData);\n\n      // Filtra le bobine compatibili con il cavo\n      const bobineCompatibili = bobineData.filter(bobina => {\n        const tipologiaCompatibile = !cavo.tipologia || !bobina.tipologia || cavo.tipologia.toLowerCase() === bobina.tipologia.toLowerCase();\n        const sezioneCompatibile = !cavo.sezione || !bobina.sezione || cavo.sezione.toLowerCase() === bobina.sezione.toLowerCase();\n        return tipologiaCompatibile && sezioneCompatibile && bobina.metri_residui > 0;\n      });\n      console.log('Bobine compatibili filtrate:', bobineCompatibili);\n      setBobine(bobineCompatibili || []);\n    } catch (error) {\n      console.error('Errore nel caricamento delle bobine:', error);\n      setBobine([]);\n    } finally {\n      setBobineLoading(false);\n    }\n  }, [cantiereId, cavo]);\n\n  // Gestisce i cambiamenti del form\n  const handleFormChange = event => {\n    const {\n      name,\n      value\n    } = event.target;\n    setFormData(prev => ({\n      ...prev,\n      [name]: value\n    }));\n\n    // Rimuovi errori quando l'utente inizia a digitare\n    if (formErrors[name]) {\n      setFormErrors(prev => ({\n        ...prev,\n        [name]: ''\n      }));\n    }\n\n    // Validazione in tempo reale per metri posati\n    if (name === 'metri_posati' && value && cavo) {\n      const metri = parseFloat(value);\n      if (!isNaN(metri) && metri > cavo.metri_teorici) {\n        setFormWarnings(prev => ({\n          ...prev,\n          metri_posati: `I metri posati (${metri}m) superano i metri teorici del cavo (${cavo.metri_teorici}m)`\n        }));\n      } else {\n        setFormWarnings(prev => ({\n          ...prev,\n          metri_posati: ''\n        }));\n      }\n    }\n  };\n\n  // Validazione del form\n  const validateForm = () => {\n    const errors = {};\n\n    // Validazione metri posati\n    if (!formData.metri_posati || formData.metri_posati.trim() === '') {\n      errors.metri_posati = 'I metri posati sono obbligatori';\n    } else {\n      const metri = parseFloat(formData.metri_posati);\n      if (isNaN(metri) || metri < 0) {\n        errors.metri_posati = 'Inserire un valore numerico valido maggiore o uguale a 0';\n      }\n    }\n\n    // Validazione bobina\n    if (!formData.id_bobina || formData.id_bobina === '') {\n      // Se non ci sono bobine disponibili, imposta automaticamente BOBINA_VUOTA\n      if (bobine.length === 0 && !bobineLoading) {\n        setFormData(prev => ({\n          ...prev,\n          id_bobina: 'BOBINA_VUOTA'\n        }));\n      } else {\n        errors.id_bobina = 'È necessario selezionare una bobina';\n      }\n    }\n    setFormErrors(errors);\n    return Object.keys(errors).length === 0;\n  };\n\n  // Gestisce il salvataggio\n  const handleSave = async () => {\n    if (!validateForm()) {\n      return;\n    }\n\n    // Dichiara le variabili fuori dal try-catch per poterle usare nel catch\n    const metriPosati = parseFloat(formData.metri_posati);\n    let idBobina = formData.id_bobina;\n\n    // Se non ci sono bobine disponibili, imposta automaticamente BOBINA_VUOTA\n    if (bobine.length === 0 && !bobineLoading) {\n      idBobina = 'BOBINA_VUOTA';\n    }\n\n    // Assicurati che BOBINA_VUOTA venga passato come stringa\n    if (idBobina === 'BOBINA_VUOTA') {\n      idBobina = 'BOBINA_VUOTA';\n    }\n    try {\n      setSaving(true);\n      console.log('Invio richiesta updateMetriPosati con i seguenti parametri:');\n      console.log('- cantiereId:', cantiereId);\n      console.log('- id_cavo:', cavo.id_cavo);\n      console.log('- metri_posati:', metriPosati);\n      console.log('- id_bobina:', idBobina);\n\n      // Chiamata API con la funzione originale completa\n      await caviService.updateMetriPosati(cantiereId, cavo.id_cavo, metriPosati, idBobina, true // Forza sempre a true per evitare blocchi\n      );\n\n      // Successo\n      const successMessage = `Metri posati aggiornati con successo per il cavo ${cavo.id_cavo}: ${metriPosati}m`;\n      onSuccess(successMessage);\n\n      // Chiudi il dialog\n      handleClose();\n    } catch (error) {\n      var _error$response, _error$response$data;\n      console.error('Errore durante l\\'aggiornamento dei metri posati:', error);\n\n      // Gestione speciale per BOBINA_VUOTA\n      if (idBobina === 'BOBINA_VUOTA' && error.success) {\n        const successMessage = `Metri posati aggiornati con successo. Cavo associato a BOBINA VUOTA`;\n        onSuccess(successMessage);\n        handleClose();\n        return;\n      }\n\n      // Gestione errori\n      let errorMessage = 'Errore durante l\\'aggiornamento dei metri posati';\n      if ((_error$response = error.response) !== null && _error$response !== void 0 && (_error$response$data = _error$response.data) !== null && _error$response$data !== void 0 && _error$response$data.detail) {\n        errorMessage = error.response.data.detail;\n      } else if (error.message) {\n        errorMessage = error.message;\n      }\n      onError(errorMessage);\n    } finally {\n      setSaving(false);\n    }\n  };\n  const handleClose = () => {\n    if (!saving && !loading) {\n      setFormErrors({});\n      setFormWarnings({});\n      setFormData({\n        metri_posati: '',\n        id_bobina: ''\n      });\n      onClose();\n    }\n  };\n  const handleKeyPress = event => {\n    if (event.key === 'Enter' && !saving && !loading && formData.metri_posati.trim() && formData.id_bobina) {\n      handleSave();\n    }\n  };\n  if (!cavo) return null;\n  return /*#__PURE__*/_jsxDEV(_Fragment, {\n    children: /*#__PURE__*/_jsxDEV(Dialog, {\n      open: open,\n      onClose: handleClose,\n      maxWidth: \"md\",\n      fullWidth: true,\n      disableEscapeKeyDown: saving || loading,\n      children: [/*#__PURE__*/_jsxDEV(DialogTitle, {\n        children: [\"Inserisci Metri Posati - \", cavo.id_cavo]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 267,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(DialogContent, {\n        dividers: true,\n        children: [/*#__PURE__*/_jsxDEV(Paper, {\n          sx: {\n            p: 2,\n            mb: 3,\n            bgcolor: 'grey.50'\n          },\n          children: /*#__PURE__*/_jsxDEV(Grid, {\n            container: true,\n            spacing: 2,\n            children: [/*#__PURE__*/_jsxDEV(Grid, {\n              item: true,\n              xs: 12,\n              md: 6,\n              children: [/*#__PURE__*/_jsxDEV(Typography, {\n                variant: \"body2\",\n                children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n                  children: \"Tipologia:\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 276,\n                  columnNumber: 45\n                }, this), \" \", cavo.tipologia || 'N/A']\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 276,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                variant: \"body2\",\n                children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n                  children: \"Sezione:\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 277,\n                  columnNumber: 45\n                }, this), \" \", cavo.sezione || 'N/A']\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 277,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                variant: \"body2\",\n                children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n                  children: \"Metri teorici:\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 278,\n                  columnNumber: 45\n                }, this), \" \", cavo.metri_teorici || 'N/A', \" m\"]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 278,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 275,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Grid, {\n              item: true,\n              xs: 12,\n              md: 6,\n              children: [/*#__PURE__*/_jsxDEV(Typography, {\n                variant: \"body2\",\n                children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n                  children: \"Da:\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 281,\n                  columnNumber: 45\n                }, this), \" \", cavo.ubicazione_partenza || 'N/A']\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 281,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                variant: \"body2\",\n                children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n                  children: \"A:\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 282,\n                  columnNumber: 45\n                }, this), \" \", cavo.ubicazione_arrivo || 'N/A']\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 282,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                variant: \"body2\",\n                children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n                  children: \"Attualmente posati:\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 283,\n                  columnNumber: 45\n                }, this), \" \", cavo.metratura_reale || 0, \" m\"]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 283,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 280,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 274,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 273,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Box, {\n          sx: {\n            mb: 3\n          },\n          children: /*#__PURE__*/_jsxDEV(TextField, {\n            autoFocus: true,\n            label: \"Metri Posati\",\n            type: \"number\",\n            fullWidth: true,\n            name: \"metri_posati\",\n            value: formData.metri_posati,\n            onChange: handleFormChange,\n            onKeyPress: handleKeyPress,\n            error: Boolean(formErrors.metri_posati),\n            helperText: formErrors.metri_posati || formWarnings.metri_posati || `Metri teorici: ${cavo.metri_teorici}m`,\n            FormHelperTextProps: {\n              sx: {\n                color: formWarnings.metri_posati && !formErrors.metri_posati ? 'warning.main' : 'error.main'\n              }\n            },\n            disabled: saving || loading,\n            InputProps: {\n              endAdornment: /*#__PURE__*/_jsxDEV(Typography, {\n                variant: \"body2\",\n                color: \"text.secondary\",\n                children: \"m\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 306,\n                columnNumber: 31\n              }, this)\n            },\n            inputProps: {\n              max: 999999,\n              step: 0.1\n            },\n            sx: {\n              mb: 2\n            }\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 290,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 289,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Box, {\n          sx: {\n            mb: 3\n          },\n          children: [/*#__PURE__*/_jsxDEV(FormControl, {\n            fullWidth: true,\n            error: Boolean(formErrors.id_bobina),\n            children: [/*#__PURE__*/_jsxDEV(InputLabel, {\n              children: \"Bobina\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 319,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Select, {\n              name: \"id_bobina\",\n              value: formData.id_bobina,\n              onChange: handleFormChange,\n              label: \"Bobina\",\n              disabled: saving || loading || bobineLoading,\n              children: [/*#__PURE__*/_jsxDEV(MenuItem, {\n                value: \"BOBINA_VUOTA\",\n                children: /*#__PURE__*/_jsxDEV(Box, {\n                  sx: {\n                    display: 'flex',\n                    alignItems: 'center',\n                    gap: 1\n                  },\n                  children: [/*#__PURE__*/_jsxDEV(Chip, {\n                    label: \"VUOTA\",\n                    size: \"small\",\n                    color: \"warning\",\n                    variant: \"outlined\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 330,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                    children: \"BOBINA VUOTA (Posa senza bobina specifica)\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 331,\n                    columnNumber: 21\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 329,\n                  columnNumber: 19\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 328,\n                columnNumber: 17\n              }, this), bobine.map(bobina => /*#__PURE__*/_jsxDEV(MenuItem, {\n                value: bobina.id_bobina,\n                children: /*#__PURE__*/_jsxDEV(Box, {\n                  sx: {\n                    display: 'flex',\n                    alignItems: 'center',\n                    gap: 1\n                  },\n                  children: [/*#__PURE__*/_jsxDEV(Chip, {\n                    label: bobina.metri_residui ? `${bobina.metri_residui.toFixed(1)}m` : '0m',\n                    size: \"small\",\n                    color: bobina.metri_residui > 0 ? 'success' : 'error',\n                    variant: \"outlined\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 339,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                    children: [bobina.id_bobina, \" - \", bobina.tipologia || 'N/A', \" \", bobina.sezione || 'N/A']\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 345,\n                    columnNumber: 23\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 338,\n                  columnNumber: 21\n                }, this)\n              }, bobina.id_bobina, false, {\n                fileName: _jsxFileName,\n                lineNumber: 337,\n                columnNumber: 19\n              }, this))]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 320,\n              columnNumber: 15\n            }, this), formErrors.id_bobina && /*#__PURE__*/_jsxDEV(Typography, {\n              variant: \"caption\",\n              color: \"error\",\n              sx: {\n                mt: 0.5,\n                ml: 1.5\n              },\n              children: formErrors.id_bobina\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 353,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 318,\n            columnNumber: 13\n          }, this), bobineLoading && /*#__PURE__*/_jsxDEV(Box, {\n            sx: {\n              display: 'flex',\n              alignItems: 'center',\n              gap: 1,\n              mt: 1\n            },\n            children: [/*#__PURE__*/_jsxDEV(CircularProgress, {\n              size: 16\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 361,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(Typography, {\n              variant: \"caption\",\n              color: \"text.secondary\",\n              children: \"Caricamento bobine...\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 362,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 360,\n            columnNumber: 15\n          }, this), !bobineLoading && bobine.length === 0 && /*#__PURE__*/_jsxDEV(Alert, {\n            severity: \"info\",\n            sx: {\n              mt: 1\n            },\n            children: \"Nessuna bobina compatibile disponibile. Verr\\xE0 utilizzata BOBINA_VUOTA.\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 369,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 317,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 271,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(DialogActions, {\n        children: [/*#__PURE__*/_jsxDEV(Button, {\n          onClick: handleClose,\n          disabled: saving || loading,\n          children: \"Annulla\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 377,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Button, {\n          onClick: handleSave,\n          variant: \"contained\",\n          disabled: saving || loading || !formData.metri_posati || !formData.id_bobina,\n          startIcon: saving ? /*#__PURE__*/_jsxDEV(CircularProgress, {\n            size: 20\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 387,\n            columnNumber: 33\n          }, this) : null,\n          children: saving ? 'Salvando...' : 'Salva'\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 383,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 376,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 260,\n      columnNumber: 7\n    }, this)\n  }, void 0, false);\n};\n_s(InserisciMetriDialogCompleto, \"/LYlTmOyFop6pNMZObf3dSonLr8=\");\n_c = InserisciMetriDialogCompleto;\nexport default InserisciMetriDialogCompleto;\nvar _c;\n$RefreshReg$(_c, \"InserisciMetriDialogCompleto\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "useCallback", "Dialog", "DialogTitle", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "DialogActions", "<PERSON><PERSON>", "TextField", "Typography", "Box", "Grid", "Paper", "<PERSON><PERSON>", "CircularProgress", "FormControl", "InputLabel", "Select", "MenuItem", "Chip", "caviService", "parcoCaviService", "jsxDEV", "_jsxDEV", "Fragment", "_Fragment", "InserisciMetriDialogCompleto", "open", "onClose", "cavo", "cantiereId", "onSuccess", "onError", "loading", "_s", "formData", "setFormData", "metri_posati", "id_bobina", "formErrors", "setFormErrors", "formWarnings", "setForm<PERSON><PERSON>nings", "saving", "setSaving", "bobine", "set<PERSON>ob<PERSON>", "bob<PERSON><PERSON><PERSON><PERSON>", "setBobineLoading", "_cavo$metratura_reale", "metratura_reale", "toString", "loadBobine", "console", "log", "id_cavo", "bobine<PERSON><PERSON>", "getBobine", "bobineCom<PERSON><PERSON><PERSON><PERSON>", "filter", "bobina", "tipologiaCompatibile", "tipologia", "toLowerCase", "sezioneCompatibile", "sezione", "metri_residui", "error", "handleFormChange", "event", "name", "value", "target", "prev", "metri", "parseFloat", "isNaN", "metri_te<PERSON>ci", "validateForm", "errors", "trim", "length", "Object", "keys", "handleSave", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "idBobina", "updateMetri<PERSON><PERSON><PERSON>", "successMessage", "handleClose", "_error$response", "_error$response$data", "success", "errorMessage", "response", "data", "detail", "message", "handleKeyPress", "key", "children", "max<PERSON><PERSON><PERSON>", "fullWidth", "disableEscapeKeyDown", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "dividers", "sx", "p", "mb", "bgcolor", "container", "spacing", "item", "xs", "md", "variant", "ubicazione_partenza", "ubicazione_arrivo", "autoFocus", "label", "type", "onChange", "onKeyPress", "Boolean", "helperText", "FormHelperTextProps", "color", "disabled", "InputProps", "endAdornment", "inputProps", "max", "step", "display", "alignItems", "gap", "size", "map", "toFixed", "mt", "ml", "severity", "onClick", "startIcon", "_c", "$RefreshReg$"], "sources": ["C:/CMS/webapp/frontend/src/components/cavi/InserisciMetriDialogCompleto.js"], "sourcesContent": ["import React, { useState, useEffect, useCallback } from 'react';\nimport {\n  Dialog,\n  DialogTitle,\n  DialogContent,\n  DialogActions,\n  Button,\n  TextField,\n  Typography,\n  Box,\n  Grid,\n  Paper,\n  Alert,\n  CircularProgress,\n  FormControl,\n  InputLabel,\n  Select,\n  MenuItem,\n  Chip\n} from '@mui/material';\nimport caviService from '../../services/caviService';\nimport parcoCaviService from '../../services/parcoCaviService';\n\n/**\n * Dialog completo per inserire i metri posati di un cavo preselezionato\n * Basato su InserisciMetriForm ma adattato per dialog con cavo preselezionato\n * \n * @param {Object} props - Proprietà del componente\n * @param {boolean} props.open - Se il dialogo è aperto\n * @param {Function} props.onClose - Funzione chiamata quando il dialogo si chiude\n * @param {Object} props.cavo - Cavo preselezionato\n * @param {string} props.cantiereId - ID del cantiere\n * @param {Function} props.onSuccess - Funzione chiamata al successo dell'operazione\n * @param {Function} props.onError - Funzione chiamata in caso di errore\n * @param {boolean} props.loading - Indica se il salvataggio è in corso\n */\nconst InserisciMetriDialogCompleto = ({\n  open = false,\n  onClose = () => {},\n  cavo = null,\n  cantiereId,\n  onSuccess = () => {},\n  onError = () => {},\n  loading = false\n}) => {\n  // Stati per il form\n  const [formData, setFormData] = useState({\n    metri_posati: '',\n    id_bobina: ''\n  });\n  const [formErrors, setFormErrors] = useState({});\n  const [formWarnings, setFormWarnings] = useState({});\n  const [saving, setSaving] = useState(false);\n  \n  // Stati per bobine\n  const [bobine, setBobine] = useState([]);\n  const [bobineLoading, setBobineLoading] = useState(false);\n  \n\n\n  // Reset quando si apre il dialogo\n  useEffect(() => {\n    if (open && cavo) {\n      setFormData({\n        metri_posati: cavo.metratura_reale?.toString() || '',\n        id_bobina: ''\n      });\n      setFormErrors({});\n      setFormWarnings({});\n      setSaving(false);\n\n      // Carica le bobine disponibili\n      loadBobine();\n    }\n  }, [open, cavo, cantiereId, loadBobine]);\n\n  // Carica le bobine disponibili\n  const loadBobine = useCallback(async () => {\n    if (!cantiereId || !cavo) return;\n\n    try {\n      setBobineLoading(true);\n      console.log('Caricamento bobine per cantiere:', cantiereId, 'e cavo:', cavo.id_cavo);\n\n      const bobineData = await parcoCaviService.getBobine(cantiereId);\n      console.log('Bobine caricate:', bobineData);\n\n      // Filtra le bobine compatibili con il cavo\n      const bobineCompatibili = bobineData.filter(bobina => {\n        const tipologiaCompatibile = !cavo.tipologia || !bobina.tipologia ||\n          cavo.tipologia.toLowerCase() === bobina.tipologia.toLowerCase();\n        const sezioneCompatibile = !cavo.sezione || !bobina.sezione ||\n          cavo.sezione.toLowerCase() === bobina.sezione.toLowerCase();\n\n        return tipologiaCompatibile && sezioneCompatibile && bobina.metri_residui > 0;\n      });\n\n      console.log('Bobine compatibili filtrate:', bobineCompatibili);\n      setBobine(bobineCompatibili || []);\n\n    } catch (error) {\n      console.error('Errore nel caricamento delle bobine:', error);\n      setBobine([]);\n    } finally {\n      setBobineLoading(false);\n    }\n  }, [cantiereId, cavo]);\n\n  // Gestisce i cambiamenti del form\n  const handleFormChange = (event) => {\n    const { name, value } = event.target;\n    setFormData(prev => ({\n      ...prev,\n      [name]: value\n    }));\n    \n    // Rimuovi errori quando l'utente inizia a digitare\n    if (formErrors[name]) {\n      setFormErrors(prev => ({\n        ...prev,\n        [name]: ''\n      }));\n    }\n    \n    // Validazione in tempo reale per metri posati\n    if (name === 'metri_posati' && value && cavo) {\n      const metri = parseFloat(value);\n      if (!isNaN(metri) && metri > cavo.metri_teorici) {\n        setFormWarnings(prev => ({\n          ...prev,\n          metri_posati: `I metri posati (${metri}m) superano i metri teorici del cavo (${cavo.metri_teorici}m)`\n        }));\n      } else {\n        setFormWarnings(prev => ({\n          ...prev,\n          metri_posati: ''\n        }));\n      }\n    }\n  };\n\n  // Validazione del form\n  const validateForm = () => {\n    const errors = {};\n    \n    // Validazione metri posati\n    if (!formData.metri_posati || formData.metri_posati.trim() === '') {\n      errors.metri_posati = 'I metri posati sono obbligatori';\n    } else {\n      const metri = parseFloat(formData.metri_posati);\n      if (isNaN(metri) || metri < 0) {\n        errors.metri_posati = 'Inserire un valore numerico valido maggiore o uguale a 0';\n      }\n    }\n    \n    // Validazione bobina\n    if (!formData.id_bobina || formData.id_bobina === '') {\n      // Se non ci sono bobine disponibili, imposta automaticamente BOBINA_VUOTA\n      if (bobine.length === 0 && !bobineLoading) {\n        setFormData(prev => ({ ...prev, id_bobina: 'BOBINA_VUOTA' }));\n      } else {\n        errors.id_bobina = 'È necessario selezionare una bobina';\n      }\n    }\n    \n    setFormErrors(errors);\n    return Object.keys(errors).length === 0;\n  };\n\n  // Gestisce il salvataggio\n  const handleSave = async () => {\n    if (!validateForm()) {\n      return;\n    }\n\n    // Dichiara le variabili fuori dal try-catch per poterle usare nel catch\n    const metriPosati = parseFloat(formData.metri_posati);\n    let idBobina = formData.id_bobina;\n\n    // Se non ci sono bobine disponibili, imposta automaticamente BOBINA_VUOTA\n    if (bobine.length === 0 && !bobineLoading) {\n      idBobina = 'BOBINA_VUOTA';\n    }\n\n    // Assicurati che BOBINA_VUOTA venga passato come stringa\n    if (idBobina === 'BOBINA_VUOTA') {\n      idBobina = 'BOBINA_VUOTA';\n    }\n\n    try {\n      setSaving(true);\n      \n      console.log('Invio richiesta updateMetriPosati con i seguenti parametri:');\n      console.log('- cantiereId:', cantiereId);\n      console.log('- id_cavo:', cavo.id_cavo);\n      console.log('- metri_posati:', metriPosati);\n      console.log('- id_bobina:', idBobina);\n      \n      // Chiamata API con la funzione originale completa\n      await caviService.updateMetriPosati(\n        cantiereId,\n        cavo.id_cavo,\n        metriPosati,\n        idBobina,\n        true // Forza sempre a true per evitare blocchi\n      );\n      \n      // Successo\n      const successMessage = `Metri posati aggiornati con successo per il cavo ${cavo.id_cavo}: ${metriPosati}m`;\n      onSuccess(successMessage);\n      \n      // Chiudi il dialog\n      handleClose();\n      \n    } catch (error) {\n      console.error('Errore durante l\\'aggiornamento dei metri posati:', error);\n      \n      // Gestione speciale per BOBINA_VUOTA\n      if (idBobina === 'BOBINA_VUOTA' && error.success) {\n        const successMessage = `Metri posati aggiornati con successo. Cavo associato a BOBINA VUOTA`;\n        onSuccess(successMessage);\n        handleClose();\n        return;\n      }\n      \n      // Gestione errori\n      let errorMessage = 'Errore durante l\\'aggiornamento dei metri posati';\n      if (error.response?.data?.detail) {\n        errorMessage = error.response.data.detail;\n      } else if (error.message) {\n        errorMessage = error.message;\n      }\n      \n      onError(errorMessage);\n      \n    } finally {\n      setSaving(false);\n    }\n  };\n\n  const handleClose = () => {\n    if (!saving && !loading) {\n      setFormErrors({});\n      setFormWarnings({});\n      setFormData({ metri_posati: '', id_bobina: '' });\n      onClose();\n    }\n  };\n\n  const handleKeyPress = (event) => {\n    if (event.key === 'Enter' && !saving && !loading && formData.metri_posati.trim() && formData.id_bobina) {\n      handleSave();\n    }\n  };\n\n  if (!cavo) return null;\n\n  return (\n    <>\n      <Dialog \n        open={open} \n        onClose={handleClose} \n        maxWidth=\"md\" \n        fullWidth\n        disableEscapeKeyDown={saving || loading}\n      >\n        <DialogTitle>\n          Inserisci Metri Posati - {cavo.id_cavo}\n        </DialogTitle>\n        \n        <DialogContent dividers>\n          {/* Informazioni cavo */}\n          <Paper sx={{ p: 2, mb: 3, bgcolor: 'grey.50' }}>\n            <Grid container spacing={2}>\n              <Grid item xs={12} md={6}>\n                <Typography variant=\"body2\"><strong>Tipologia:</strong> {cavo.tipologia || 'N/A'}</Typography>\n                <Typography variant=\"body2\"><strong>Sezione:</strong> {cavo.sezione || 'N/A'}</Typography>\n                <Typography variant=\"body2\"><strong>Metri teorici:</strong> {cavo.metri_teorici || 'N/A'} m</Typography>\n              </Grid>\n              <Grid item xs={12} md={6}>\n                <Typography variant=\"body2\"><strong>Da:</strong> {cavo.ubicazione_partenza || 'N/A'}</Typography>\n                <Typography variant=\"body2\"><strong>A:</strong> {cavo.ubicazione_arrivo || 'N/A'}</Typography>\n                <Typography variant=\"body2\"><strong>Attualmente posati:</strong> {cavo.metratura_reale || 0} m</Typography>\n              </Grid>\n            </Grid>\n          </Paper>\n\n          {/* Campo metri posati */}\n          <Box sx={{ mb: 3 }}>\n            <TextField\n              autoFocus\n              label=\"Metri Posati\"\n              type=\"number\"\n              fullWidth\n              name=\"metri_posati\"\n              value={formData.metri_posati}\n              onChange={handleFormChange}\n              onKeyPress={handleKeyPress}\n              error={Boolean(formErrors.metri_posati)}\n              helperText={formErrors.metri_posati || formWarnings.metri_posati || `Metri teorici: ${cavo.metri_teorici}m`}\n              FormHelperTextProps={{\n                sx: { color: formWarnings.metri_posati && !formErrors.metri_posati ? 'warning.main' : 'error.main' }\n              }}\n              disabled={saving || loading}\n              InputProps={{\n                endAdornment: <Typography variant=\"body2\" color=\"text.secondary\">m</Typography>\n              }}\n              inputProps={{\n                max: 999999,\n                step: 0.1\n              }}\n              sx={{ mb: 2 }}\n            />\n          </Box>\n\n          {/* Selezione bobina */}\n          <Box sx={{ mb: 3 }}>\n            <FormControl fullWidth error={Boolean(formErrors.id_bobina)}>\n              <InputLabel>Bobina</InputLabel>\n              <Select\n                name=\"id_bobina\"\n                value={formData.id_bobina}\n                onChange={handleFormChange}\n                label=\"Bobina\"\n                disabled={saving || loading || bobineLoading}\n              >\n                {/* Opzione BOBINA_VUOTA sempre disponibile */}\n                <MenuItem value=\"BOBINA_VUOTA\">\n                  <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>\n                    <Chip label=\"VUOTA\" size=\"small\" color=\"warning\" variant=\"outlined\" />\n                    <Typography>BOBINA VUOTA (Posa senza bobina specifica)</Typography>\n                  </Box>\n                </MenuItem>\n\n                {/* Bobine disponibili */}\n                {bobine.map((bobina) => (\n                  <MenuItem key={bobina.id_bobina} value={bobina.id_bobina}>\n                    <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>\n                      <Chip\n                        label={bobina.metri_residui ? `${bobina.metri_residui.toFixed(1)}m` : '0m'}\n                        size=\"small\"\n                        color={bobina.metri_residui > 0 ? 'success' : 'error'}\n                        variant=\"outlined\"\n                      />\n                      <Typography>\n                        {bobina.id_bobina} - {bobina.tipologia || 'N/A'} {bobina.sezione || 'N/A'}\n                      </Typography>\n                    </Box>\n                  </MenuItem>\n                ))}\n              </Select>\n              {formErrors.id_bobina && (\n                <Typography variant=\"caption\" color=\"error\" sx={{ mt: 0.5, ml: 1.5 }}>\n                  {formErrors.id_bobina}\n                </Typography>\n              )}\n            </FormControl>\n\n            {bobineLoading && (\n              <Box sx={{ display: 'flex', alignItems: 'center', gap: 1, mt: 1 }}>\n                <CircularProgress size={16} />\n                <Typography variant=\"caption\" color=\"text.secondary\">\n                  Caricamento bobine...\n                </Typography>\n              </Box>\n            )}\n\n            {!bobineLoading && bobine.length === 0 && (\n              <Alert severity=\"info\" sx={{ mt: 1 }}>\n                Nessuna bobina compatibile disponibile. Verrà utilizzata BOBINA_VUOTA.\n              </Alert>\n            )}\n          </Box>\n        </DialogContent>\n\n        <DialogActions>\n          <Button\n            onClick={handleClose}\n            disabled={saving || loading}\n          >\n            Annulla\n          </Button>\n          <Button\n            onClick={handleSave}\n            variant=\"contained\"\n            disabled={saving || loading || !formData.metri_posati || !formData.id_bobina}\n            startIcon={saving ? <CircularProgress size={20} /> : null}\n          >\n            {saving ? 'Salvando...' : 'Salva'}\n          </Button>\n        </DialogActions>\n      </Dialog>\n    </>\n  );\n};\n\nexport default InserisciMetriDialogCompleto;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,EAAEC,WAAW,QAAQ,OAAO;AAC/D,SACEC,MAAM,EACNC,WAAW,EACXC,aAAa,EACbC,aAAa,EACbC,MAAM,EACNC,SAAS,EACTC,UAAU,EACVC,GAAG,EACHC,IAAI,EACJC,KAAK,EACLC,KAAK,EACLC,gBAAgB,EAChBC,WAAW,EACXC,UAAU,EACVC,MAAM,EACNC,QAAQ,EACRC,IAAI,QACC,eAAe;AACtB,OAAOC,WAAW,MAAM,4BAA4B;AACpD,OAAOC,gBAAgB,MAAM,iCAAiC;;AAE9D;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAZA,SAAAC,MAAA,IAAAC,OAAA,EAAAC,QAAA,IAAAC,SAAA;AAaA,MAAMC,4BAA4B,GAAGA,CAAC;EACpCC,IAAI,GAAG,KAAK;EACZC,OAAO,GAAGA,CAAA,KAAM,CAAC,CAAC;EAClBC,IAAI,GAAG,IAAI;EACXC,UAAU;EACVC,SAAS,GAAGA,CAAA,KAAM,CAAC,CAAC;EACpBC,OAAO,GAAGA,CAAA,KAAM,CAAC,CAAC;EAClBC,OAAO,GAAG;AACZ,CAAC,KAAK;EAAAC,EAAA;EACJ;EACA,MAAM,CAACC,QAAQ,EAAEC,WAAW,CAAC,GAAGpC,QAAQ,CAAC;IACvCqC,YAAY,EAAE,EAAE;IAChBC,SAAS,EAAE;EACb,CAAC,CAAC;EACF,MAAM,CAACC,UAAU,EAAEC,aAAa,CAAC,GAAGxC,QAAQ,CAAC,CAAC,CAAC,CAAC;EAChD,MAAM,CAACyC,YAAY,EAAEC,eAAe,CAAC,GAAG1C,QAAQ,CAAC,CAAC,CAAC,CAAC;EACpD,MAAM,CAAC2C,MAAM,EAAEC,SAAS,CAAC,GAAG5C,QAAQ,CAAC,KAAK,CAAC;;EAE3C;EACA,MAAM,CAAC6C,MAAM,EAAEC,SAAS,CAAC,GAAG9C,QAAQ,CAAC,EAAE,CAAC;EACxC,MAAM,CAAC+C,aAAa,EAAEC,gBAAgB,CAAC,GAAGhD,QAAQ,CAAC,KAAK,CAAC;;EAIzD;EACAC,SAAS,CAAC,MAAM;IACd,IAAI0B,IAAI,IAAIE,IAAI,EAAE;MAAA,IAAAoB,qBAAA;MAChBb,WAAW,CAAC;QACVC,YAAY,EAAE,EAAAY,qBAAA,GAAApB,IAAI,CAACqB,eAAe,cAAAD,qBAAA,uBAApBA,qBAAA,CAAsBE,QAAQ,CAAC,CAAC,KAAI,EAAE;QACpDb,SAAS,EAAE;MACb,CAAC,CAAC;MACFE,aAAa,CAAC,CAAC,CAAC,CAAC;MACjBE,eAAe,CAAC,CAAC,CAAC,CAAC;MACnBE,SAAS,CAAC,KAAK,CAAC;;MAEhB;MACAQ,UAAU,CAAC,CAAC;IACd;EACF,CAAC,EAAE,CAACzB,IAAI,EAAEE,IAAI,EAAEC,UAAU,EAAEsB,UAAU,CAAC,CAAC;;EAExC;EACA,MAAMA,UAAU,GAAGlD,WAAW,CAAC,YAAY;IACzC,IAAI,CAAC4B,UAAU,IAAI,CAACD,IAAI,EAAE;IAE1B,IAAI;MACFmB,gBAAgB,CAAC,IAAI,CAAC;MACtBK,OAAO,CAACC,GAAG,CAAC,kCAAkC,EAAExB,UAAU,EAAE,SAAS,EAAED,IAAI,CAAC0B,OAAO,CAAC;MAEpF,MAAMC,UAAU,GAAG,MAAMnC,gBAAgB,CAACoC,SAAS,CAAC3B,UAAU,CAAC;MAC/DuB,OAAO,CAACC,GAAG,CAAC,kBAAkB,EAAEE,UAAU,CAAC;;MAE3C;MACA,MAAME,iBAAiB,GAAGF,UAAU,CAACG,MAAM,CAACC,MAAM,IAAI;QACpD,MAAMC,oBAAoB,GAAG,CAAChC,IAAI,CAACiC,SAAS,IAAI,CAACF,MAAM,CAACE,SAAS,IAC/DjC,IAAI,CAACiC,SAAS,CAACC,WAAW,CAAC,CAAC,KAAKH,MAAM,CAACE,SAAS,CAACC,WAAW,CAAC,CAAC;QACjE,MAAMC,kBAAkB,GAAG,CAACnC,IAAI,CAACoC,OAAO,IAAI,CAACL,MAAM,CAACK,OAAO,IACzDpC,IAAI,CAACoC,OAAO,CAACF,WAAW,CAAC,CAAC,KAAKH,MAAM,CAACK,OAAO,CAACF,WAAW,CAAC,CAAC;QAE7D,OAAOF,oBAAoB,IAAIG,kBAAkB,IAAIJ,MAAM,CAACM,aAAa,GAAG,CAAC;MAC/E,CAAC,CAAC;MAEFb,OAAO,CAACC,GAAG,CAAC,8BAA8B,EAAEI,iBAAiB,CAAC;MAC9DZ,SAAS,CAACY,iBAAiB,IAAI,EAAE,CAAC;IAEpC,CAAC,CAAC,OAAOS,KAAK,EAAE;MACdd,OAAO,CAACc,KAAK,CAAC,sCAAsC,EAAEA,KAAK,CAAC;MAC5DrB,SAAS,CAAC,EAAE,CAAC;IACf,CAAC,SAAS;MACRE,gBAAgB,CAAC,KAAK,CAAC;IACzB;EACF,CAAC,EAAE,CAAClB,UAAU,EAAED,IAAI,CAAC,CAAC;;EAEtB;EACA,MAAMuC,gBAAgB,GAAIC,KAAK,IAAK;IAClC,MAAM;MAAEC,IAAI;MAAEC;IAAM,CAAC,GAAGF,KAAK,CAACG,MAAM;IACpCpC,WAAW,CAACqC,IAAI,KAAK;MACnB,GAAGA,IAAI;MACP,CAACH,IAAI,GAAGC;IACV,CAAC,CAAC,CAAC;;IAEH;IACA,IAAIhC,UAAU,CAAC+B,IAAI,CAAC,EAAE;MACpB9B,aAAa,CAACiC,IAAI,KAAK;QACrB,GAAGA,IAAI;QACP,CAACH,IAAI,GAAG;MACV,CAAC,CAAC,CAAC;IACL;;IAEA;IACA,IAAIA,IAAI,KAAK,cAAc,IAAIC,KAAK,IAAI1C,IAAI,EAAE;MAC5C,MAAM6C,KAAK,GAAGC,UAAU,CAACJ,KAAK,CAAC;MAC/B,IAAI,CAACK,KAAK,CAACF,KAAK,CAAC,IAAIA,KAAK,GAAG7C,IAAI,CAACgD,aAAa,EAAE;QAC/CnC,eAAe,CAAC+B,IAAI,KAAK;UACvB,GAAGA,IAAI;UACPpC,YAAY,EAAE,mBAAmBqC,KAAK,yCAAyC7C,IAAI,CAACgD,aAAa;QACnG,CAAC,CAAC,CAAC;MACL,CAAC,MAAM;QACLnC,eAAe,CAAC+B,IAAI,KAAK;UACvB,GAAGA,IAAI;UACPpC,YAAY,EAAE;QAChB,CAAC,CAAC,CAAC;MACL;IACF;EACF,CAAC;;EAED;EACA,MAAMyC,YAAY,GAAGA,CAAA,KAAM;IACzB,MAAMC,MAAM,GAAG,CAAC,CAAC;;IAEjB;IACA,IAAI,CAAC5C,QAAQ,CAACE,YAAY,IAAIF,QAAQ,CAACE,YAAY,CAAC2C,IAAI,CAAC,CAAC,KAAK,EAAE,EAAE;MACjED,MAAM,CAAC1C,YAAY,GAAG,iCAAiC;IACzD,CAAC,MAAM;MACL,MAAMqC,KAAK,GAAGC,UAAU,CAACxC,QAAQ,CAACE,YAAY,CAAC;MAC/C,IAAIuC,KAAK,CAACF,KAAK,CAAC,IAAIA,KAAK,GAAG,CAAC,EAAE;QAC7BK,MAAM,CAAC1C,YAAY,GAAG,0DAA0D;MAClF;IACF;;IAEA;IACA,IAAI,CAACF,QAAQ,CAACG,SAAS,IAAIH,QAAQ,CAACG,SAAS,KAAK,EAAE,EAAE;MACpD;MACA,IAAIO,MAAM,CAACoC,MAAM,KAAK,CAAC,IAAI,CAAClC,aAAa,EAAE;QACzCX,WAAW,CAACqC,IAAI,KAAK;UAAE,GAAGA,IAAI;UAAEnC,SAAS,EAAE;QAAe,CAAC,CAAC,CAAC;MAC/D,CAAC,MAAM;QACLyC,MAAM,CAACzC,SAAS,GAAG,qCAAqC;MAC1D;IACF;IAEAE,aAAa,CAACuC,MAAM,CAAC;IACrB,OAAOG,MAAM,CAACC,IAAI,CAACJ,MAAM,CAAC,CAACE,MAAM,KAAK,CAAC;EACzC,CAAC;;EAED;EACA,MAAMG,UAAU,GAAG,MAAAA,CAAA,KAAY;IAC7B,IAAI,CAACN,YAAY,CAAC,CAAC,EAAE;MACnB;IACF;;IAEA;IACA,MAAMO,WAAW,GAAGV,UAAU,CAACxC,QAAQ,CAACE,YAAY,CAAC;IACrD,IAAIiD,QAAQ,GAAGnD,QAAQ,CAACG,SAAS;;IAEjC;IACA,IAAIO,MAAM,CAACoC,MAAM,KAAK,CAAC,IAAI,CAAClC,aAAa,EAAE;MACzCuC,QAAQ,GAAG,cAAc;IAC3B;;IAEA;IACA,IAAIA,QAAQ,KAAK,cAAc,EAAE;MAC/BA,QAAQ,GAAG,cAAc;IAC3B;IAEA,IAAI;MACF1C,SAAS,CAAC,IAAI,CAAC;MAEfS,OAAO,CAACC,GAAG,CAAC,6DAA6D,CAAC;MAC1ED,OAAO,CAACC,GAAG,CAAC,eAAe,EAAExB,UAAU,CAAC;MACxCuB,OAAO,CAACC,GAAG,CAAC,YAAY,EAAEzB,IAAI,CAAC0B,OAAO,CAAC;MACvCF,OAAO,CAACC,GAAG,CAAC,iBAAiB,EAAE+B,WAAW,CAAC;MAC3ChC,OAAO,CAACC,GAAG,CAAC,cAAc,EAAEgC,QAAQ,CAAC;;MAErC;MACA,MAAMlE,WAAW,CAACmE,iBAAiB,CACjCzD,UAAU,EACVD,IAAI,CAAC0B,OAAO,EACZ8B,WAAW,EACXC,QAAQ,EACR,IAAI,CAAC;MACP,CAAC;;MAED;MACA,MAAME,cAAc,GAAG,oDAAoD3D,IAAI,CAAC0B,OAAO,KAAK8B,WAAW,GAAG;MAC1GtD,SAAS,CAACyD,cAAc,CAAC;;MAEzB;MACAC,WAAW,CAAC,CAAC;IAEf,CAAC,CAAC,OAAOtB,KAAK,EAAE;MAAA,IAAAuB,eAAA,EAAAC,oBAAA;MACdtC,OAAO,CAACc,KAAK,CAAC,mDAAmD,EAAEA,KAAK,CAAC;;MAEzE;MACA,IAAImB,QAAQ,KAAK,cAAc,IAAInB,KAAK,CAACyB,OAAO,EAAE;QAChD,MAAMJ,cAAc,GAAG,qEAAqE;QAC5FzD,SAAS,CAACyD,cAAc,CAAC;QACzBC,WAAW,CAAC,CAAC;QACb;MACF;;MAEA;MACA,IAAII,YAAY,GAAG,kDAAkD;MACrE,KAAAH,eAAA,GAAIvB,KAAK,CAAC2B,QAAQ,cAAAJ,eAAA,gBAAAC,oBAAA,GAAdD,eAAA,CAAgBK,IAAI,cAAAJ,oBAAA,eAApBA,oBAAA,CAAsBK,MAAM,EAAE;QAChCH,YAAY,GAAG1B,KAAK,CAAC2B,QAAQ,CAACC,IAAI,CAACC,MAAM;MAC3C,CAAC,MAAM,IAAI7B,KAAK,CAAC8B,OAAO,EAAE;QACxBJ,YAAY,GAAG1B,KAAK,CAAC8B,OAAO;MAC9B;MAEAjE,OAAO,CAAC6D,YAAY,CAAC;IAEvB,CAAC,SAAS;MACRjD,SAAS,CAAC,KAAK,CAAC;IAClB;EACF,CAAC;EAED,MAAM6C,WAAW,GAAGA,CAAA,KAAM;IACxB,IAAI,CAAC9C,MAAM,IAAI,CAACV,OAAO,EAAE;MACvBO,aAAa,CAAC,CAAC,CAAC,CAAC;MACjBE,eAAe,CAAC,CAAC,CAAC,CAAC;MACnBN,WAAW,CAAC;QAAEC,YAAY,EAAE,EAAE;QAAEC,SAAS,EAAE;MAAG,CAAC,CAAC;MAChDV,OAAO,CAAC,CAAC;IACX;EACF,CAAC;EAED,MAAMsE,cAAc,GAAI7B,KAAK,IAAK;IAChC,IAAIA,KAAK,CAAC8B,GAAG,KAAK,OAAO,IAAI,CAACxD,MAAM,IAAI,CAACV,OAAO,IAAIE,QAAQ,CAACE,YAAY,CAAC2C,IAAI,CAAC,CAAC,IAAI7C,QAAQ,CAACG,SAAS,EAAE;MACtG8C,UAAU,CAAC,CAAC;IACd;EACF,CAAC;EAED,IAAI,CAACvD,IAAI,EAAE,OAAO,IAAI;EAEtB,oBACEN,OAAA,CAAAE,SAAA;IAAA2E,QAAA,eACE7E,OAAA,CAACpB,MAAM;MACLwB,IAAI,EAAEA,IAAK;MACXC,OAAO,EAAE6D,WAAY;MACrBY,QAAQ,EAAC,IAAI;MACbC,SAAS;MACTC,oBAAoB,EAAE5D,MAAM,IAAIV,OAAQ;MAAAmE,QAAA,gBAExC7E,OAAA,CAACnB,WAAW;QAAAgG,QAAA,GAAC,2BACc,EAACvE,IAAI,CAAC0B,OAAO;MAAA;QAAAiD,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC3B,CAAC,eAEdpF,OAAA,CAAClB,aAAa;QAACuG,QAAQ;QAAAR,QAAA,gBAErB7E,OAAA,CAACX,KAAK;UAACiG,EAAE,EAAE;YAAEC,CAAC,EAAE,CAAC;YAAEC,EAAE,EAAE,CAAC;YAAEC,OAAO,EAAE;UAAU,CAAE;UAAAZ,QAAA,eAC7C7E,OAAA,CAACZ,IAAI;YAACsG,SAAS;YAACC,OAAO,EAAE,CAAE;YAAAd,QAAA,gBACzB7E,OAAA,CAACZ,IAAI;cAACwG,IAAI;cAACC,EAAE,EAAE,EAAG;cAACC,EAAE,EAAE,CAAE;cAAAjB,QAAA,gBACvB7E,OAAA,CAACd,UAAU;gBAAC6G,OAAO,EAAC,OAAO;gBAAAlB,QAAA,gBAAC7E,OAAA;kBAAA6E,QAAA,EAAQ;gBAAU;kBAAAI,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,KAAC,EAAC9E,IAAI,CAACiC,SAAS,IAAI,KAAK;cAAA;gBAAA0C,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAa,CAAC,eAC9FpF,OAAA,CAACd,UAAU;gBAAC6G,OAAO,EAAC,OAAO;gBAAAlB,QAAA,gBAAC7E,OAAA;kBAAA6E,QAAA,EAAQ;gBAAQ;kBAAAI,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,KAAC,EAAC9E,IAAI,CAACoC,OAAO,IAAI,KAAK;cAAA;gBAAAuC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAa,CAAC,eAC1FpF,OAAA,CAACd,UAAU;gBAAC6G,OAAO,EAAC,OAAO;gBAAAlB,QAAA,gBAAC7E,OAAA;kBAAA6E,QAAA,EAAQ;gBAAc;kBAAAI,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,KAAC,EAAC9E,IAAI,CAACgD,aAAa,IAAI,KAAK,EAAC,IAAE;cAAA;gBAAA2B,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAY,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACpG,CAAC,eACPpF,OAAA,CAACZ,IAAI;cAACwG,IAAI;cAACC,EAAE,EAAE,EAAG;cAACC,EAAE,EAAE,CAAE;cAAAjB,QAAA,gBACvB7E,OAAA,CAACd,UAAU;gBAAC6G,OAAO,EAAC,OAAO;gBAAAlB,QAAA,gBAAC7E,OAAA;kBAAA6E,QAAA,EAAQ;gBAAG;kBAAAI,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,KAAC,EAAC9E,IAAI,CAAC0F,mBAAmB,IAAI,KAAK;cAAA;gBAAAf,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAa,CAAC,eACjGpF,OAAA,CAACd,UAAU;gBAAC6G,OAAO,EAAC,OAAO;gBAAAlB,QAAA,gBAAC7E,OAAA;kBAAA6E,QAAA,EAAQ;gBAAE;kBAAAI,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,KAAC,EAAC9E,IAAI,CAAC2F,iBAAiB,IAAI,KAAK;cAAA;gBAAAhB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAa,CAAC,eAC9FpF,OAAA,CAACd,UAAU;gBAAC6G,OAAO,EAAC,OAAO;gBAAAlB,QAAA,gBAAC7E,OAAA;kBAAA6E,QAAA,EAAQ;gBAAmB;kBAAAI,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,KAAC,EAAC9E,IAAI,CAACqB,eAAe,IAAI,CAAC,EAAC,IAAE;cAAA;gBAAAsD,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAY,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACvG,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACF,CAAC,eAGRpF,OAAA,CAACb,GAAG;UAACmG,EAAE,EAAE;YAAEE,EAAE,EAAE;UAAE,CAAE;UAAAX,QAAA,eACjB7E,OAAA,CAACf,SAAS;YACRiH,SAAS;YACTC,KAAK,EAAC,cAAc;YACpBC,IAAI,EAAC,QAAQ;YACbrB,SAAS;YACThC,IAAI,EAAC,cAAc;YACnBC,KAAK,EAAEpC,QAAQ,CAACE,YAAa;YAC7BuF,QAAQ,EAAExD,gBAAiB;YAC3ByD,UAAU,EAAE3B,cAAe;YAC3B/B,KAAK,EAAE2D,OAAO,CAACvF,UAAU,CAACF,YAAY,CAAE;YACxC0F,UAAU,EAAExF,UAAU,CAACF,YAAY,IAAII,YAAY,CAACJ,YAAY,IAAI,kBAAkBR,IAAI,CAACgD,aAAa,GAAI;YAC5GmD,mBAAmB,EAAE;cACnBnB,EAAE,EAAE;gBAAEoB,KAAK,EAAExF,YAAY,CAACJ,YAAY,IAAI,CAACE,UAAU,CAACF,YAAY,GAAG,cAAc,GAAG;cAAa;YACrG,CAAE;YACF6F,QAAQ,EAAEvF,MAAM,IAAIV,OAAQ;YAC5BkG,UAAU,EAAE;cACVC,YAAY,eAAE7G,OAAA,CAACd,UAAU;gBAAC6G,OAAO,EAAC,OAAO;gBAACW,KAAK,EAAC,gBAAgB;gBAAA7B,QAAA,EAAC;cAAC;gBAAAI,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAY;YAChF,CAAE;YACF0B,UAAU,EAAE;cACVC,GAAG,EAAE,MAAM;cACXC,IAAI,EAAE;YACR,CAAE;YACF1B,EAAE,EAAE;cAAEE,EAAE,EAAE;YAAE;UAAE;YAAAP,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACf;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACC,CAAC,eAGNpF,OAAA,CAACb,GAAG;UAACmG,EAAE,EAAE;YAAEE,EAAE,EAAE;UAAE,CAAE;UAAAX,QAAA,gBACjB7E,OAAA,CAACR,WAAW;YAACuF,SAAS;YAACnC,KAAK,EAAE2D,OAAO,CAACvF,UAAU,CAACD,SAAS,CAAE;YAAA8D,QAAA,gBAC1D7E,OAAA,CAACP,UAAU;cAAAoF,QAAA,EAAC;YAAM;cAAAI,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAY,CAAC,eAC/BpF,OAAA,CAACN,MAAM;cACLqD,IAAI,EAAC,WAAW;cAChBC,KAAK,EAAEpC,QAAQ,CAACG,SAAU;cAC1BsF,QAAQ,EAAExD,gBAAiB;cAC3BsD,KAAK,EAAC,QAAQ;cACdQ,QAAQ,EAAEvF,MAAM,IAAIV,OAAO,IAAIc,aAAc;cAAAqD,QAAA,gBAG7C7E,OAAA,CAACL,QAAQ;gBAACqD,KAAK,EAAC,cAAc;gBAAA6B,QAAA,eAC5B7E,OAAA,CAACb,GAAG;kBAACmG,EAAE,EAAE;oBAAE2B,OAAO,EAAE,MAAM;oBAAEC,UAAU,EAAE,QAAQ;oBAAEC,GAAG,EAAE;kBAAE,CAAE;kBAAAtC,QAAA,gBACzD7E,OAAA,CAACJ,IAAI;oBAACuG,KAAK,EAAC,OAAO;oBAACiB,IAAI,EAAC,OAAO;oBAACV,KAAK,EAAC,SAAS;oBAACX,OAAO,EAAC;kBAAU;oBAAAd,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE,CAAC,eACtEpF,OAAA,CAACd,UAAU;oBAAA2F,QAAA,EAAC;kBAA0C;oBAAAI,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAY,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAChE;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACE,CAAC,EAGV9D,MAAM,CAAC+F,GAAG,CAAEhF,MAAM,iBACjBrC,OAAA,CAACL,QAAQ;gBAAwBqD,KAAK,EAAEX,MAAM,CAACtB,SAAU;gBAAA8D,QAAA,eACvD7E,OAAA,CAACb,GAAG;kBAACmG,EAAE,EAAE;oBAAE2B,OAAO,EAAE,MAAM;oBAAEC,UAAU,EAAE,QAAQ;oBAAEC,GAAG,EAAE;kBAAE,CAAE;kBAAAtC,QAAA,gBACzD7E,OAAA,CAACJ,IAAI;oBACHuG,KAAK,EAAE9D,MAAM,CAACM,aAAa,GAAG,GAAGN,MAAM,CAACM,aAAa,CAAC2E,OAAO,CAAC,CAAC,CAAC,GAAG,GAAG,IAAK;oBAC3EF,IAAI,EAAC,OAAO;oBACZV,KAAK,EAAErE,MAAM,CAACM,aAAa,GAAG,CAAC,GAAG,SAAS,GAAG,OAAQ;oBACtDoD,OAAO,EAAC;kBAAU;oBAAAd,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACnB,CAAC,eACFpF,OAAA,CAACd,UAAU;oBAAA2F,QAAA,GACRxC,MAAM,CAACtB,SAAS,EAAC,KAAG,EAACsB,MAAM,CAACE,SAAS,IAAI,KAAK,EAAC,GAAC,EAACF,MAAM,CAACK,OAAO,IAAI,KAAK;kBAAA;oBAAAuC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAC/D,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACV;cAAC,GAXO/C,MAAM,CAACtB,SAAS;gBAAAkE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAYrB,CACX,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACI,CAAC,EACRpE,UAAU,CAACD,SAAS,iBACnBf,OAAA,CAACd,UAAU;cAAC6G,OAAO,EAAC,SAAS;cAACW,KAAK,EAAC,OAAO;cAACpB,EAAE,EAAE;gBAAEiC,EAAE,EAAE,GAAG;gBAAEC,EAAE,EAAE;cAAI,CAAE;cAAA3C,QAAA,EAClE7D,UAAU,CAACD;YAAS;cAAAkE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACX,CACb;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACU,CAAC,EAEb5D,aAAa,iBACZxB,OAAA,CAACb,GAAG;YAACmG,EAAE,EAAE;cAAE2B,OAAO,EAAE,MAAM;cAAEC,UAAU,EAAE,QAAQ;cAAEC,GAAG,EAAE,CAAC;cAAEI,EAAE,EAAE;YAAE,CAAE;YAAA1C,QAAA,gBAChE7E,OAAA,CAACT,gBAAgB;cAAC6H,IAAI,EAAE;YAAG;cAAAnC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,eAC9BpF,OAAA,CAACd,UAAU;cAAC6G,OAAO,EAAC,SAAS;cAACW,KAAK,EAAC,gBAAgB;cAAA7B,QAAA,EAAC;YAErD;cAAAI,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAY,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACV,CACN,EAEA,CAAC5D,aAAa,IAAIF,MAAM,CAACoC,MAAM,KAAK,CAAC,iBACpC1D,OAAA,CAACV,KAAK;YAACmI,QAAQ,EAAC,MAAM;YAACnC,EAAE,EAAE;cAAEiC,EAAE,EAAE;YAAE,CAAE;YAAA1C,QAAA,EAAC;UAEtC;YAAAI,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAO,CACR;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACE,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACO,CAAC,eAEhBpF,OAAA,CAACjB,aAAa;QAAA8F,QAAA,gBACZ7E,OAAA,CAAChB,MAAM;UACL0I,OAAO,EAAExD,WAAY;UACrByC,QAAQ,EAAEvF,MAAM,IAAIV,OAAQ;UAAAmE,QAAA,EAC7B;QAED;UAAAI,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC,eACTpF,OAAA,CAAChB,MAAM;UACL0I,OAAO,EAAE7D,UAAW;UACpBkC,OAAO,EAAC,WAAW;UACnBY,QAAQ,EAAEvF,MAAM,IAAIV,OAAO,IAAI,CAACE,QAAQ,CAACE,YAAY,IAAI,CAACF,QAAQ,CAACG,SAAU;UAC7E4G,SAAS,EAAEvG,MAAM,gBAAGpB,OAAA,CAACT,gBAAgB;YAAC6H,IAAI,EAAE;UAAG;YAAAnC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,GAAG,IAAK;UAAAP,QAAA,EAEzDzD,MAAM,GAAG,aAAa,GAAG;QAAO;UAAA6D,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC3B,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACI,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACV;EAAC,gBACT,CAAC;AAEP,CAAC;AAACzE,EAAA,CAtWIR,4BAA4B;AAAAyH,EAAA,GAA5BzH,4BAA4B;AAwWlC,eAAeA,4BAA4B;AAAC,IAAAyH,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}