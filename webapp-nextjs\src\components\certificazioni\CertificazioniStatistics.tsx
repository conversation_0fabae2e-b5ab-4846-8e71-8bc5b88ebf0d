'use client'

import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Progress } from '@/components/ui/progress'
import { Badge } from '@/components/ui/badge'
import { 
  FileText, 
  CheckCircle, 
  AlertCircle, 
  Clock, 
  Award,
  TrendingUp,
  Target,
  AlertTriangle
} from 'lucide-react'

interface StatisticsData {
  totali: number
  conformi: number
  non_conformi: number
  bozze: number
  con_riserva: number
}

interface CertificazioniStatisticsProps {
  stats: StatisticsData
  detailed?: boolean
}

export default function CertificazioniStatistics({ stats, detailed = false }: CertificazioniStatisticsProps) {
  const percentualeConformita = stats.totali > 0 ? (stats.conformi / stats.totali) * 100 : 0
  const percentualeCompletamento = stats.totali > 0 ? ((stats.conformi + stats.non_conformi + stats.con_riserva) / stats.totali) * 100 : 0

  const getConformitaColor = (percentage: number) => {
    if (percentage >= 95) return 'text-green-600'
    if (percentage >= 85) return 'text-yellow-600'
    return 'text-red-600'
  }

  const getConformitaIcon = (percentage: number) => {
    if (percentage >= 95) return <CheckCircle className="h-5 w-5 text-green-600" />
    if (percentage >= 85) return <AlertTriangle className="h-5 w-5 text-yellow-600" />
    return <AlertCircle className="h-5 w-5 text-red-600" />
  }

  return (
    <div className="space-y-6">
      {/* Cards Statistiche Principali */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
        <Card>
          <CardContent className="p-4">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm text-slate-600">Totali</p>
                <p className="text-2xl font-bold text-slate-900">{stats.totali}</p>
              </div>
              <FileText className="h-8 w-8 text-blue-500" />
            </div>
          </CardContent>
        </Card>
        
        <Card>
          <CardContent className="p-4">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm text-slate-600">Conformi</p>
                <p className="text-2xl font-bold text-green-600">{stats.conformi}</p>
                <p className="text-xs text-slate-500">
                  {stats.totali > 0 ? `${((stats.conformi / stats.totali) * 100).toFixed(1)}%` : '0%'}
                </p>
              </div>
              <CheckCircle className="h-8 w-8 text-green-500" />
            </div>
          </CardContent>
        </Card>
        
        <Card>
          <CardContent className="p-4">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm text-slate-600">Non Conformi</p>
                <p className="text-2xl font-bold text-red-600">{stats.non_conformi}</p>
                <p className="text-xs text-slate-500">
                  {stats.totali > 0 ? `${((stats.non_conformi / stats.totali) * 100).toFixed(1)}%` : '0%'}
                </p>
              </div>
              <AlertCircle className="h-8 w-8 text-red-500" />
            </div>
          </CardContent>
        </Card>
        
        <Card>
          <CardContent className="p-4">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm text-slate-600">Bozze</p>
                <p className="text-2xl font-bold text-yellow-600">{stats.bozze}</p>
                <p className="text-xs text-slate-500">
                  {stats.totali > 0 ? `${((stats.bozze / stats.totali) * 100).toFixed(1)}%` : '0%'}
                </p>
              </div>
              <Clock className="h-8 w-8 text-yellow-500" />
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Indicatori di Performance */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Target className="h-5 w-5 text-blue-600" />
              Tasso di Conformità
            </CardTitle>
            <CardDescription>
              Percentuale di certificazioni conformi sul totale
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              <div className="flex items-center justify-between">
                <span className="text-sm font-medium">Conformità</span>
                <div className="flex items-center gap-2">
                  {getConformitaIcon(percentualeConformita)}
                  <span className={`text-lg font-bold ${getConformitaColor(percentualeConformita)}`}>
                    {percentualeConformita.toFixed(1)}%
                  </span>
                </div>
              </div>
              <Progress 
                value={percentualeConformita} 
                className="h-2"
              />
              <div className="flex justify-between text-xs text-slate-500">
                <span>Target: 95%</span>
                <span>{stats.conformi} / {stats.totali}</span>
              </div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <TrendingUp className="h-5 w-5 text-green-600" />
              Completamento Certificazioni
            </CardTitle>
            <CardDescription>
              Percentuale di certificazioni completate (non bozze)
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              <div className="flex items-center justify-between">
                <span className="text-sm font-medium">Completamento</span>
                <span className="text-lg font-bold text-blue-600">
                  {percentualeCompletamento.toFixed(1)}%
                </span>
              </div>
              <Progress 
                value={percentualeCompletamento} 
                className="h-2"
              />
              <div className="flex justify-between text-xs text-slate-500">
                <span>Completate: {stats.conformi + stats.non_conformi + stats.con_riserva}</span>
                <span>Bozze: {stats.bozze}</span>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>

      {detailed && (
        <>
          {/* Dettagli Aggiuntivi */}
          <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
            <Card>
              <CardHeader>
                <CardTitle className="text-lg">Distribuzione Stati</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-3">
                  <div className="flex items-center justify-between">
                    <div className="flex items-center gap-2">
                      <div className="w-3 h-3 bg-green-500 rounded-full"></div>
                      <span className="text-sm">Conformi</span>
                    </div>
                    <Badge variant="outline" className="text-green-600 border-green-200">
                      {stats.conformi}
                    </Badge>
                  </div>
                  <div className="flex items-center justify-between">
                    <div className="flex items-center gap-2">
                      <div className="w-3 h-3 bg-red-500 rounded-full"></div>
                      <span className="text-sm">Non Conformi</span>
                    </div>
                    <Badge variant="outline" className="text-red-600 border-red-200">
                      {stats.non_conformi}
                    </Badge>
                  </div>
                  <div className="flex items-center justify-between">
                    <div className="flex items-center gap-2">
                      <div className="w-3 h-3 bg-yellow-500 rounded-full"></div>
                      <span className="text-sm">Con Riserva</span>
                    </div>
                    <Badge variant="outline" className="text-yellow-600 border-yellow-200">
                      {stats.con_riserva}
                    </Badge>
                  </div>
                  <div className="flex items-center justify-between">
                    <div className="flex items-center gap-2">
                      <div className="w-3 h-3 bg-gray-500 rounded-full"></div>
                      <span className="text-sm">Bozze</span>
                    </div>
                    <Badge variant="outline" className="text-gray-600 border-gray-200">
                      {stats.bozze}
                    </Badge>
                  </div>
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle className="text-lg">Indicatori Qualità</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  <div>
                    <div className="flex justify-between text-sm mb-1">
                      <span>Tasso Successo</span>
                      <span className="font-medium">
                        {stats.totali > 0 ? (((stats.conformi + stats.con_riserva) / stats.totali) * 100).toFixed(1) : 0}%
                      </span>
                    </div>
                    <Progress 
                      value={stats.totali > 0 ? ((stats.conformi + stats.con_riserva) / stats.totali) * 100 : 0} 
                      className="h-2"
                    />
                  </div>
                  <div>
                    <div className="flex justify-between text-sm mb-1">
                      <span>Tasso Fallimento</span>
                      <span className="font-medium text-red-600">
                        {stats.totali > 0 ? ((stats.non_conformi / stats.totali) * 100).toFixed(1) : 0}%
                      </span>
                    </div>
                    <Progress 
                      value={stats.totali > 0 ? (stats.non_conformi / stats.totali) * 100 : 0} 
                      className="h-2"
                    />
                  </div>
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle className="text-lg flex items-center gap-2">
                  <Award className="h-5 w-5 text-yellow-500" />
                  Valutazione Complessiva
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="text-center space-y-2">
                  <div className="text-3xl font-bold">
                    {percentualeConformita >= 95 ? '🏆' : 
                     percentualeConformita >= 85 ? '⭐' : 
                     percentualeConformita >= 70 ? '👍' : '⚠️'}
                  </div>
                  <div className="text-lg font-semibold">
                    {percentualeConformita >= 95 ? 'Eccellente' : 
                     percentualeConformita >= 85 ? 'Buono' : 
                     percentualeConformita >= 70 ? 'Sufficiente' : 'Da Migliorare'}
                  </div>
                  <div className="text-sm text-slate-600">
                    {percentualeConformita >= 95 ? 'Qualità certificazioni ottimale' : 
                     percentualeConformita >= 85 ? 'Qualità certificazioni buona' : 
                     percentualeConformita >= 70 ? 'Qualità certificazioni accettabile' : 'Necessario miglioramento qualità'}
                  </div>
                </div>
              </CardContent>
            </Card>
          </div>

          {/* Raccomandazioni */}
          {(percentualeConformita < 95 || stats.bozze > 0) && (
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <AlertTriangle className="h-5 w-5 text-yellow-600" />
                  Raccomandazioni
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-2">
                  {percentualeConformita < 95 && (
                    <div className="flex items-start gap-2 text-sm">
                      <div className="w-2 h-2 bg-yellow-500 rounded-full mt-2"></div>
                      <span>
                        Il tasso di conformità è del {percentualeConformita.toFixed(1)}%. 
                        Obiettivo raccomandato: 95%. Verificare procedure di test e qualità installazioni.
                      </span>
                    </div>
                  )}
                  {stats.bozze > 0 && (
                    <div className="flex items-start gap-2 text-sm">
                      <div className="w-2 h-2 bg-blue-500 rounded-full mt-2"></div>
                      <span>
                        Ci sono {stats.bozze} certificazioni in bozza. 
                        Completare le certificazioni per avere dati accurati.
                      </span>
                    </div>
                  )}
                  {stats.non_conformi > stats.conformi && (
                    <div className="flex items-start gap-2 text-sm">
                      <div className="w-2 h-2 bg-red-500 rounded-full mt-2"></div>
                      <span>
                        Il numero di certificazioni non conformi supera quelle conformi. 
                        Rivedere urgentemente le procedure di installazione e test.
                      </span>
                    </div>
                  )}
                </div>
              </CardContent>
            </Card>
          )}
        </>
      )}
    </div>
  )
}
