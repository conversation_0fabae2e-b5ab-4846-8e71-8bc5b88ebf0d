{"ast": null, "code": "var formatRelativeLocale = {\n  lastWeek: function lastWeek(date) {\n    switch (date.getUTCDay()) {\n      case 0:\n        return \"'prošle nedjelje u' p\";\n      case 3:\n        return \"'prošle srijede u' p\";\n      case 6:\n        return \"'prošle subote u' p\";\n      default:\n        return \"'prošli' EEEE 'u' p\";\n    }\n  },\n  yesterday: \"'juče u' p\",\n  today: \"'danas u' p\",\n  tomorrow: \"'sutra u' p\",\n  nextWeek: function nextWeek(date) {\n    switch (date.getUTCDay()) {\n      case 0:\n        return \"'sljedeće nedjelje u' p\";\n      case 3:\n        return \"'sljedeću srijedu u' p\";\n      case 6:\n        return \"'sljedeću subotu u' p\";\n      default:\n        return \"'sljedeći' EEEE 'u' p\";\n    }\n  },\n  other: 'P'\n};\nvar formatRelative = function formatRelative(token, date, _baseDate, _options) {\n  var format = formatRelativeLocale[token];\n  if (typeof format === 'function') {\n    return format(date);\n  }\n  return format;\n};\nexport default formatRelative;", "map": {"version": 3, "names": ["formatRelativeLocale", "lastWeek", "date", "getUTCDay", "yesterday", "today", "tomorrow", "nextWeek", "other", "formatRelative", "token", "_baseDate", "_options", "format"], "sources": ["C:/CMS/webapp/frontend/node_modules/date-fns/esm/locale/bs/_lib/formatRelative/index.js"], "sourcesContent": ["var formatRelativeLocale = {\n  lastWeek: function lastWeek(date) {\n    switch (date.getUTCDay()) {\n      case 0:\n        return \"'prošle nedjelje u' p\";\n      case 3:\n        return \"'prošle srijede u' p\";\n      case 6:\n        return \"'prošle subote u' p\";\n      default:\n        return \"'prošli' EEEE 'u' p\";\n    }\n  },\n  yesterday: \"'juče u' p\",\n  today: \"'danas u' p\",\n  tomorrow: \"'sutra u' p\",\n  nextWeek: function nextWeek(date) {\n    switch (date.getUTCDay()) {\n      case 0:\n        return \"'sljedeće nedjelje u' p\";\n      case 3:\n        return \"'sljedeću srijedu u' p\";\n      case 6:\n        return \"'sljedeću subotu u' p\";\n      default:\n        return \"'sljedeći' EEEE 'u' p\";\n    }\n  },\n  other: 'P'\n};\nvar formatRelative = function formatRelative(token, date, _baseDate, _options) {\n  var format = formatRelativeLocale[token];\n  if (typeof format === 'function') {\n    return format(date);\n  }\n  return format;\n};\nexport default formatRelative;"], "mappings": "AAAA,IAAIA,oBAAoB,GAAG;EACzBC,QAAQ,EAAE,SAASA,QAAQA,CAACC,IAAI,EAAE;IAChC,QAAQA,IAAI,CAACC,SAAS,CAAC,CAAC;MACtB,KAAK,CAAC;QACJ,OAAO,uBAAuB;MAChC,KAAK,CAAC;QACJ,OAAO,sBAAsB;MAC/B,KAAK,CAAC;QACJ,OAAO,qBAAqB;MAC9B;QACE,OAAO,qBAAqB;IAChC;EACF,CAAC;EACDC,SAAS,EAAE,YAAY;EACvBC,KAAK,EAAE,aAAa;EACpBC,QAAQ,EAAE,aAAa;EACvBC,QAAQ,EAAE,SAASA,QAAQA,CAACL,IAAI,EAAE;IAChC,QAAQA,IAAI,CAACC,SAAS,CAAC,CAAC;MACtB,KAAK,CAAC;QACJ,OAAO,yBAAyB;MAClC,KAAK,CAAC;QACJ,OAAO,wBAAwB;MACjC,KAAK,CAAC;QACJ,OAAO,uBAAuB;MAChC;QACE,OAAO,uBAAuB;IAClC;EACF,CAAC;EACDK,KAAK,EAAE;AACT,CAAC;AACD,IAAIC,cAAc,GAAG,SAASA,cAAcA,CAACC,KAAK,EAAER,IAAI,EAAES,SAAS,EAAEC,QAAQ,EAAE;EAC7E,IAAIC,MAAM,GAAGb,oBAAoB,CAACU,KAAK,CAAC;EACxC,IAAI,OAAOG,MAAM,KAAK,UAAU,EAAE;IAChC,OAAOA,MAAM,CAACX,IAAI,CAAC;EACrB;EACA,OAAOW,MAAM;AACf,CAAC;AACD,eAAeJ,cAAc", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}