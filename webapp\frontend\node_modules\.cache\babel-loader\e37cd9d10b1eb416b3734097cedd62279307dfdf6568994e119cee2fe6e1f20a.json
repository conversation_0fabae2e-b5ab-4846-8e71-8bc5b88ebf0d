{"ast": null, "code": "import axios from 'axios';\nconst API_URL = 'http://localhost:8002/api';\n\n// Crea un'istanza di axios con configurazione personalizzata\nconst axiosInstance = axios.create({\n  baseURL: API_URL,\n  headers: {\n    'Content-Type': 'application/json'\n  }\n});\n\n// Configura axios per includere il token in tutte le richieste\naxiosInstance.interceptors.request.use(config => {\n  const token = localStorage.getItem('token');\n  if (token) {\n    config.headers.Authorization = `Bearer ${token}`;\n  }\n  return config;\n}, error => {\n  return Promise.reject(error);\n});\nconst comandeService = {\n  // Ottiene la lista delle comande di un cantiere\n  getComande: async cantiereId => {\n    try {\n      // Assicurati che cantiereId sia un numero\n      const cantiereIdNum = parseInt(cantiereId, 10);\n      if (isNaN(cantiereIdNum)) {\n        throw new Error(`ID cantiere non valido: ${cantiereId}`);\n      }\n      const response = await axiosInstance.get(`/comande/${cantiereIdNum}`);\n      return response.data;\n    } catch (error) {\n      console.error('Get comande error:', error);\n      throw error.response ? error.response.data : error;\n    }\n  },\n  // Crea una nuova comanda\n  createComanda: async (cantiereId, comandaData) => {\n    try {\n      // Assicurati che cantiereId sia un numero\n      const cantiereIdNum = parseInt(cantiereId, 10);\n      if (isNaN(cantiereIdNum)) {\n        throw new Error(`ID cantiere non valido: ${cantiereId}`);\n      }\n      const response = await axiosInstance.post(`/comande/${cantiereIdNum}`, comandaData);\n      return response.data;\n    } catch (error) {\n      console.error('Create comanda error:', error);\n      throw error.response ? error.response.data : error;\n    }\n  },\n  // Aggiorna una comanda esistente\n  updateComanda: async (cantiereId, idComanda, comandaData) => {\n    try {\n      // Assicurati che cantiereId sia un numero\n      const cantiereIdNum = parseInt(cantiereId, 10);\n      if (isNaN(cantiereIdNum)) {\n        throw new Error(`ID cantiere non valido: ${cantiereId}`);\n      }\n      const response = await axiosInstance.put(`/comande/${cantiereIdNum}/${idComanda}`, comandaData);\n      return response.data;\n    } catch (error) {\n      console.error('Update comanda error:', error);\n      throw error.response ? error.response.data : error;\n    }\n  },\n  // Elimina una comanda\n  deleteComanda: async (cantiereId, idComanda) => {\n    try {\n      // Assicurati che cantiereId sia un numero\n      const cantiereIdNum = parseInt(cantiereId, 10);\n      if (isNaN(cantiereIdNum)) {\n        throw new Error(`ID cantiere non valido: ${cantiereId}`);\n      }\n      const response = await axiosInstance.delete(`/comande/${cantiereIdNum}/${idComanda}`);\n      return response.data;\n    } catch (error) {\n      console.error('Delete comanda error:', error);\n      throw error.response ? error.response.data : error;\n    }\n  },\n  // Assegna una comanda a un cavo\n  assignComandaToCavo: async (cantiereId, idComanda, idCavo) => {\n    try {\n      // Assicurati che cantiereId sia un numero\n      const cantiereIdNum = parseInt(cantiereId, 10);\n      if (isNaN(cantiereIdNum)) {\n        throw new Error(`ID cantiere non valido: ${cantiereId}`);\n      }\n      const response = await axiosInstance.post(`/comande/${cantiereIdNum}/${idComanda}/assign`, {\n        id_cavo: idCavo\n      });\n      return response.data;\n    } catch (error) {\n      console.error('Assign comanda error:', error);\n      throw error.response ? error.response.data : error;\n    }\n  },\n  // Genera PDF di una comanda\n  printComanda: async (cantiereId, idComanda) => {\n    try {\n      // Assicurati che cantiereId sia un numero\n      const cantiereIdNum = parseInt(cantiereId, 10);\n      if (isNaN(cantiereIdNum)) {\n        throw new Error(`ID cantiere non valido: ${cantiereId}`);\n      }\n      const response = await axiosInstance.get(`/comande/${cantiereIdNum}/${idComanda}/pdf`);\n      return response.data;\n    } catch (error) {\n      console.error('Print comanda error:', error);\n      throw error.response ? error.response.data : error;\n    }\n  }\n};\nexport default comandeService;", "map": {"version": 3, "names": ["axios", "API_URL", "axiosInstance", "create", "baseURL", "headers", "interceptors", "request", "use", "config", "token", "localStorage", "getItem", "Authorization", "error", "Promise", "reject", "comandeService", "getComande", "cantiereId", "cantiereIdNum", "parseInt", "isNaN", "Error", "response", "get", "data", "console", "createComanda", "comandaData", "post", "updateComanda", "idComanda", "put", "deleteComanda", "delete", "assignComandaToCavo", "idCavo", "id_cavo", "printComanda"], "sources": ["C:/Users/<USER>/PycharmProjects/CMS/webapp/frontend/src/services/comandeService.js"], "sourcesContent": ["import axios from 'axios';\r\n\r\nconst API_URL = 'http://localhost:8002/api';\r\n\r\n// Crea un'istanza di axios con configurazione personalizzata\r\nconst axiosInstance = axios.create({\r\n  baseURL: API_URL,\r\n  headers: {\r\n    'Content-Type': 'application/json'\r\n  }\r\n});\r\n\r\n// Configura axios per includere il token in tutte le richieste\r\naxiosInstance.interceptors.request.use(\r\n  (config) => {\r\n    const token = localStorage.getItem('token');\r\n    if (token) {\r\n      config.headers.Authorization = `Bearer ${token}`;\r\n    }\r\n    return config;\r\n  },\r\n  (error) => {\r\n    return Promise.reject(error);\r\n  }\r\n);\r\n\r\nconst comandeService = {\r\n  // Ottiene la lista delle comande di un cantiere\r\n  getComande: async (cantiereId) => {\r\n    try {\r\n      // Assicurati che cantiereId sia un numero\r\n      const cantiereIdNum = parseInt(cantiereId, 10);\r\n      if (isNaN(cantiereIdNum)) {\r\n        throw new Error(`ID cantiere non valido: ${cantiereId}`);\r\n      }\r\n\r\n      const response = await axiosInstance.get(`/comande/${cantiereIdNum}`);\r\n      return response.data;\r\n    } catch (error) {\r\n      console.error('Get comande error:', error);\r\n      throw error.response ? error.response.data : error;\r\n    }\r\n  },\r\n\r\n  // Crea una nuova comanda\r\n  createComanda: async (cantiereId, comandaData) => {\r\n    try {\r\n      // Assicurati che cantiereId sia un numero\r\n      const cantiereIdNum = parseInt(cantiereId, 10);\r\n      if (isNaN(cantiereIdNum)) {\r\n        throw new Error(`ID cantiere non valido: ${cantiereId}`);\r\n      }\r\n\r\n      const response = await axiosInstance.post(`/comande/${cantiereIdNum}`, comandaData);\r\n      return response.data;\r\n    } catch (error) {\r\n      console.error('Create comanda error:', error);\r\n      throw error.response ? error.response.data : error;\r\n    }\r\n  },\r\n\r\n  // Aggiorna una comanda esistente\r\n  updateComanda: async (cantiereId, idComanda, comandaData) => {\r\n    try {\r\n      // Assicurati che cantiereId sia un numero\r\n      const cantiereIdNum = parseInt(cantiereId, 10);\r\n      if (isNaN(cantiereIdNum)) {\r\n        throw new Error(`ID cantiere non valido: ${cantiereId}`);\r\n      }\r\n\r\n      const response = await axiosInstance.put(`/comande/${cantiereIdNum}/${idComanda}`, comandaData);\r\n      return response.data;\r\n    } catch (error) {\r\n      console.error('Update comanda error:', error);\r\n      throw error.response ? error.response.data : error;\r\n    }\r\n  },\r\n\r\n  // Elimina una comanda\r\n  deleteComanda: async (cantiereId, idComanda) => {\r\n    try {\r\n      // Assicurati che cantiereId sia un numero\r\n      const cantiereIdNum = parseInt(cantiereId, 10);\r\n      if (isNaN(cantiereIdNum)) {\r\n        throw new Error(`ID cantiere non valido: ${cantiereId}`);\r\n      }\r\n\r\n      const response = await axiosInstance.delete(`/comande/${cantiereIdNum}/${idComanda}`);\r\n      return response.data;\r\n    } catch (error) {\r\n      console.error('Delete comanda error:', error);\r\n      throw error.response ? error.response.data : error;\r\n    }\r\n  },\r\n\r\n  // Assegna una comanda a un cavo\r\n  assignComandaToCavo: async (cantiereId, idComanda, idCavo) => {\r\n    try {\r\n      // Assicurati che cantiereId sia un numero\r\n      const cantiereIdNum = parseInt(cantiereId, 10);\r\n      if (isNaN(cantiereIdNum)) {\r\n        throw new Error(`ID cantiere non valido: ${cantiereId}`);\r\n      }\r\n\r\n      const response = await axiosInstance.post(`/comande/${cantiereIdNum}/${idComanda}/assign`, {\r\n        id_cavo: idCavo\r\n      });\r\n      return response.data;\r\n    } catch (error) {\r\n      console.error('Assign comanda error:', error);\r\n      throw error.response ? error.response.data : error;\r\n    }\r\n  },\r\n\r\n  // Genera PDF di una comanda\r\n  printComanda: async (cantiereId, idComanda) => {\r\n    try {\r\n      // Assicurati che cantiereId sia un numero\r\n      const cantiereIdNum = parseInt(cantiereId, 10);\r\n      if (isNaN(cantiereIdNum)) {\r\n        throw new Error(`ID cantiere non valido: ${cantiereId}`);\r\n      }\r\n\r\n      const response = await axiosInstance.get(`/comande/${cantiereIdNum}/${idComanda}/pdf`);\r\n      return response.data;\r\n    } catch (error) {\r\n      console.error('Print comanda error:', error);\r\n      throw error.response ? error.response.data : error;\r\n    }\r\n  }\r\n};\r\n\r\nexport default comandeService;\r\n"], "mappings": "AAAA,OAAOA,KAAK,MAAM,OAAO;AAEzB,MAAMC,OAAO,GAAG,2BAA2B;;AAE3C;AACA,MAAMC,aAAa,GAAGF,KAAK,CAACG,MAAM,CAAC;EACjCC,OAAO,EAAEH,OAAO;EAChBI,OAAO,EAAE;IACP,cAAc,EAAE;EAClB;AACF,CAAC,CAAC;;AAEF;AACAH,aAAa,CAACI,YAAY,CAACC,OAAO,CAACC,GAAG,CACnCC,MAAM,IAAK;EACV,MAAMC,KAAK,GAAGC,YAAY,CAACC,OAAO,CAAC,OAAO,CAAC;EAC3C,IAAIF,KAAK,EAAE;IACTD,MAAM,CAACJ,OAAO,CAACQ,aAAa,GAAG,UAAUH,KAAK,EAAE;EAClD;EACA,OAAOD,MAAM;AACf,CAAC,EACAK,KAAK,IAAK;EACT,OAAOC,OAAO,CAACC,MAAM,CAACF,KAAK,CAAC;AAC9B,CACF,CAAC;AAED,MAAMG,cAAc,GAAG;EACrB;EACAC,UAAU,EAAE,MAAOC,UAAU,IAAK;IAChC,IAAI;MACF;MACA,MAAMC,aAAa,GAAGC,QAAQ,CAACF,UAAU,EAAE,EAAE,CAAC;MAC9C,IAAIG,KAAK,CAACF,aAAa,CAAC,EAAE;QACxB,MAAM,IAAIG,KAAK,CAAC,2BAA2BJ,UAAU,EAAE,CAAC;MAC1D;MAEA,MAAMK,QAAQ,GAAG,MAAMtB,aAAa,CAACuB,GAAG,CAAC,YAAYL,aAAa,EAAE,CAAC;MACrE,OAAOI,QAAQ,CAACE,IAAI;IACtB,CAAC,CAAC,OAAOZ,KAAK,EAAE;MACda,OAAO,CAACb,KAAK,CAAC,oBAAoB,EAAEA,KAAK,CAAC;MAC1C,MAAMA,KAAK,CAACU,QAAQ,GAAGV,KAAK,CAACU,QAAQ,CAACE,IAAI,GAAGZ,KAAK;IACpD;EACF,CAAC;EAED;EACAc,aAAa,EAAE,MAAAA,CAAOT,UAAU,EAAEU,WAAW,KAAK;IAChD,IAAI;MACF;MACA,MAAMT,aAAa,GAAGC,QAAQ,CAACF,UAAU,EAAE,EAAE,CAAC;MAC9C,IAAIG,KAAK,CAACF,aAAa,CAAC,EAAE;QACxB,MAAM,IAAIG,KAAK,CAAC,2BAA2BJ,UAAU,EAAE,CAAC;MAC1D;MAEA,MAAMK,QAAQ,GAAG,MAAMtB,aAAa,CAAC4B,IAAI,CAAC,YAAYV,aAAa,EAAE,EAAES,WAAW,CAAC;MACnF,OAAOL,QAAQ,CAACE,IAAI;IACtB,CAAC,CAAC,OAAOZ,KAAK,EAAE;MACda,OAAO,CAACb,KAAK,CAAC,uBAAuB,EAAEA,KAAK,CAAC;MAC7C,MAAMA,KAAK,CAACU,QAAQ,GAAGV,KAAK,CAACU,QAAQ,CAACE,IAAI,GAAGZ,KAAK;IACpD;EACF,CAAC;EAED;EACAiB,aAAa,EAAE,MAAAA,CAAOZ,UAAU,EAAEa,SAAS,EAAEH,WAAW,KAAK;IAC3D,IAAI;MACF;MACA,MAAMT,aAAa,GAAGC,QAAQ,CAACF,UAAU,EAAE,EAAE,CAAC;MAC9C,IAAIG,KAAK,CAACF,aAAa,CAAC,EAAE;QACxB,MAAM,IAAIG,KAAK,CAAC,2BAA2BJ,UAAU,EAAE,CAAC;MAC1D;MAEA,MAAMK,QAAQ,GAAG,MAAMtB,aAAa,CAAC+B,GAAG,CAAC,YAAYb,aAAa,IAAIY,SAAS,EAAE,EAAEH,WAAW,CAAC;MAC/F,OAAOL,QAAQ,CAACE,IAAI;IACtB,CAAC,CAAC,OAAOZ,KAAK,EAAE;MACda,OAAO,CAACb,KAAK,CAAC,uBAAuB,EAAEA,KAAK,CAAC;MAC7C,MAAMA,KAAK,CAACU,QAAQ,GAAGV,KAAK,CAACU,QAAQ,CAACE,IAAI,GAAGZ,KAAK;IACpD;EACF,CAAC;EAED;EACAoB,aAAa,EAAE,MAAAA,CAAOf,UAAU,EAAEa,SAAS,KAAK;IAC9C,IAAI;MACF;MACA,MAAMZ,aAAa,GAAGC,QAAQ,CAACF,UAAU,EAAE,EAAE,CAAC;MAC9C,IAAIG,KAAK,CAACF,aAAa,CAAC,EAAE;QACxB,MAAM,IAAIG,KAAK,CAAC,2BAA2BJ,UAAU,EAAE,CAAC;MAC1D;MAEA,MAAMK,QAAQ,GAAG,MAAMtB,aAAa,CAACiC,MAAM,CAAC,YAAYf,aAAa,IAAIY,SAAS,EAAE,CAAC;MACrF,OAAOR,QAAQ,CAACE,IAAI;IACtB,CAAC,CAAC,OAAOZ,KAAK,EAAE;MACda,OAAO,CAACb,KAAK,CAAC,uBAAuB,EAAEA,KAAK,CAAC;MAC7C,MAAMA,KAAK,CAACU,QAAQ,GAAGV,KAAK,CAACU,QAAQ,CAACE,IAAI,GAAGZ,KAAK;IACpD;EACF,CAAC;EAED;EACAsB,mBAAmB,EAAE,MAAAA,CAAOjB,UAAU,EAAEa,SAAS,EAAEK,MAAM,KAAK;IAC5D,IAAI;MACF;MACA,MAAMjB,aAAa,GAAGC,QAAQ,CAACF,UAAU,EAAE,EAAE,CAAC;MAC9C,IAAIG,KAAK,CAACF,aAAa,CAAC,EAAE;QACxB,MAAM,IAAIG,KAAK,CAAC,2BAA2BJ,UAAU,EAAE,CAAC;MAC1D;MAEA,MAAMK,QAAQ,GAAG,MAAMtB,aAAa,CAAC4B,IAAI,CAAC,YAAYV,aAAa,IAAIY,SAAS,SAAS,EAAE;QACzFM,OAAO,EAAED;MACX,CAAC,CAAC;MACF,OAAOb,QAAQ,CAACE,IAAI;IACtB,CAAC,CAAC,OAAOZ,KAAK,EAAE;MACda,OAAO,CAACb,KAAK,CAAC,uBAAuB,EAAEA,KAAK,CAAC;MAC7C,MAAMA,KAAK,CAACU,QAAQ,GAAGV,KAAK,CAACU,QAAQ,CAACE,IAAI,GAAGZ,KAAK;IACpD;EACF,CAAC;EAED;EACAyB,YAAY,EAAE,MAAAA,CAAOpB,UAAU,EAAEa,SAAS,KAAK;IAC7C,IAAI;MACF;MACA,MAAMZ,aAAa,GAAGC,QAAQ,CAACF,UAAU,EAAE,EAAE,CAAC;MAC9C,IAAIG,KAAK,CAACF,aAAa,CAAC,EAAE;QACxB,MAAM,IAAIG,KAAK,CAAC,2BAA2BJ,UAAU,EAAE,CAAC;MAC1D;MAEA,MAAMK,QAAQ,GAAG,MAAMtB,aAAa,CAACuB,GAAG,CAAC,YAAYL,aAAa,IAAIY,SAAS,MAAM,CAAC;MACtF,OAAOR,QAAQ,CAACE,IAAI;IACtB,CAAC,CAAC,OAAOZ,KAAK,EAAE;MACda,OAAO,CAACb,KAAK,CAAC,sBAAsB,EAAEA,KAAK,CAAC;MAC5C,MAAMA,KAAK,CAACU,QAAQ,GAAGV,KAAK,CAACU,QAAQ,CAACE,IAAI,GAAGZ,KAAK;IACpD;EACF;AACF,CAAC;AAED,eAAeG,cAAc", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}