{"ast": null, "code": "import { endOfWeek } from \"./endOfWeek.js\";\n\n/**\n * The {@link endOfISOWeek} function options.\n */\n\n/**\n * @name endOfISOWeek\n * @category ISO Week Helpers\n * @summary Return the end of an ISO week for the given date.\n *\n * @description\n * Return the end of an ISO week for the given date.\n * The result will be in the local timezone.\n *\n * ISO week-numbering year: http://en.wikipedia.org/wiki/ISO_week_date\n *\n * @typeParam DateType - The `Date` type, the function operates on. Gets inferred from passed arguments. Allows to use extensions like [`UTCDate`](https://github.com/date-fns/utc).\n * @typeParam ResultDate - The result `Date` type, it is the type returned from the context function if it is passed, or inferred from the arguments.\n *\n * @param date - The original date\n * @param options - An object with options\n *\n * @returns The end of an ISO week\n *\n * @example\n * // The end of an ISO week for 2 September 2014 11:55:00:\n * const result = endOfISOWeek(new Date(2014, 8, 2, 11, 55, 0))\n * //=> Sun Sep 07 2014 23:59:59.999\n */\nexport function endOfISOWeek(date, options) {\n  return endOfWeek(date, {\n    ...options,\n    weekStartsOn: 1\n  });\n}\n\n// Fallback for modularized imports:\nexport default endOfISOWeek;", "map": {"version": 3, "names": ["endOfWeek", "endOfISOWeek", "date", "options", "weekStartsOn"], "sources": ["C:/CMS/webapp/frontend/node_modules/date-fns/endOfISOWeek.js"], "sourcesContent": ["import { endOfWeek } from \"./endOfWeek.js\";\n\n/**\n * The {@link endOfISOWeek} function options.\n */\n\n/**\n * @name endOfISOWeek\n * @category ISO Week Helpers\n * @summary Return the end of an ISO week for the given date.\n *\n * @description\n * Return the end of an ISO week for the given date.\n * The result will be in the local timezone.\n *\n * ISO week-numbering year: http://en.wikipedia.org/wiki/ISO_week_date\n *\n * @typeParam DateType - The `Date` type, the function operates on. Gets inferred from passed arguments. Allows to use extensions like [`UTCDate`](https://github.com/date-fns/utc).\n * @typeParam ResultDate - The result `Date` type, it is the type returned from the context function if it is passed, or inferred from the arguments.\n *\n * @param date - The original date\n * @param options - An object with options\n *\n * @returns The end of an ISO week\n *\n * @example\n * // The end of an ISO week for 2 September 2014 11:55:00:\n * const result = endOfISOWeek(new Date(2014, 8, 2, 11, 55, 0))\n * //=> Sun Sep 07 2014 23:59:59.999\n */\nexport function endOfISOWeek(date, options) {\n  return endOfWeek(date, { ...options, weekStartsOn: 1 });\n}\n\n// Fallback for modularized imports:\nexport default endOfISOWeek;\n"], "mappings": "AAAA,SAASA,SAAS,QAAQ,gBAAgB;;AAE1C;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO,SAASC,YAAYA,CAACC,IAAI,EAAEC,OAAO,EAAE;EAC1C,OAAOH,SAAS,CAACE,IAAI,EAAE;IAAE,GAAGC,OAAO;IAAEC,YAAY,EAAE;EAAE,CAAC,CAAC;AACzD;;AAEA;AACA,eAAeH,YAAY", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}