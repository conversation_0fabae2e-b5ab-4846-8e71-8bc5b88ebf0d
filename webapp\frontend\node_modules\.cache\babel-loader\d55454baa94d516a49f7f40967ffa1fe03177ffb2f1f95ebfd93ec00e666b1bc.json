{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\PycharmProjects\\\\CMS\\\\webapp\\\\frontend\\\\src\\\\pages\\\\cavi\\\\posa\\\\EliminaCavoPage.js\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect } from 'react';\nimport { Box, Typography, Paper, Button, IconButton, Alert, Snackbar, List, ListItem, ListItemText, ListItemSecondaryAction, Dialog, DialogTitle, DialogContent, DialogContentText, DialogActions, CircularProgress, Divider, Chip, TextField, Radio, RadioGroup, FormControlLabel, FormControl, FormLabel } from '@mui/material';\nimport { ArrowBack as ArrowBackIcon, Refresh as RefreshIcon, Home as HomeIcon, Delete as DeleteIcon, Warning as WarningIcon, Search as SearchIcon } from '@mui/icons-material';\nimport { useNavigate } from 'react-router-dom';\nimport { useAuth } from '../../../context/AuthContext';\nimport AdminHomeButton from '../../../components/common/AdminHomeButton';\nimport caviService from '../../../services/caviService';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst EliminaCavoPage = () => {\n  _s();\n  const navigate = useNavigate();\n  const {\n    isImpersonating\n  } = useAuth();\n  const [alertMessage, setAlertMessage] = useState(null);\n  const [alertSeverity, setAlertSeverity] = useState('success');\n  const [openSnackbar, setOpenSnackbar] = useState(false);\n\n  // Stati per la gestione dei cavi\n  const [cavi, setCavi] = useState([]);\n  const [loading, setLoading] = useState(false);\n  const [selectedCavo, setSelectedCavo] = useState(null);\n  const [openDialog, setOpenDialog] = useState(false);\n  const [dialogType, setDialogType] = useState(''); // 'delete' o 'spare'\n  const [searchTerm, setSearchTerm] = useState('');\n  const [deleteMode, setDeleteMode] = useState('spare'); // 'spare' o 'delete'\n  const [forceSpare, setForceSpare] = useState(false);\n\n  // Recupera l'ID del cantiere selezionato dal localStorage\n  const cantiereId = localStorage.getItem('selectedCantiereId');\n  const cantiereName = localStorage.getItem('selectedCantiereName');\n\n  // Gestisce il ritorno alla pagina dei cantieri\n  const handleBackToCantieri = () => {\n    navigate('/dashboard/cantieri');\n  };\n\n  // Gestisce il ritorno al menu admin (per admin che impersonano utenti)\n  const handleBackToAdmin = () => {\n    navigate('/dashboard/admin');\n  };\n\n  // Gestisce il ritorno alla pagina principale di posa cavi\n  const handleBackToPosa = () => {\n    navigate('/dashboard/cavi/posa');\n  };\n\n  // Gestisce il successo di un'operazione\n  const handleSuccess = message => {\n    setAlertMessage(message);\n    setAlertSeverity('success');\n    setOpenSnackbar(true);\n  };\n\n  // Gestisce l'errore di un'operazione\n  const handleError = message => {\n    setAlertMessage(message);\n    setAlertSeverity('error');\n    setOpenSnackbar(true);\n  };\n\n  // Chiude lo snackbar\n  const handleCloseSnackbar = () => {\n    setOpenSnackbar(false);\n  };\n\n  // Carica i cavi del cantiere\n  const loadCavi = async () => {\n    if (!cantiereId) return;\n    try {\n      setLoading(true);\n      const data = await caviService.getCavi(cantiereId, 0); // Tipo 0 = cavi attivi\n      setCavi(data);\n    } catch (error) {\n      handleError(`Errore nel caricamento dei cavi: ${error.message || 'Errore sconosciuto'}`);\n      console.error('Errore nel caricamento dei cavi:', error);\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  // Carica i cavi all'avvio del componente\n  useEffect(() => {\n    loadCavi();\n  }, [cantiereId]);\n\n  // Gestisce la selezione di un cavo\n  const handleCavoSelect = cavo => {\n    setSelectedCavo(cavo);\n\n    // Determina il tipo di dialogo in base allo stato del cavo\n    const isInstalled = cavo.stato_installazione === 'Installato' || cavo.metratura_reale && cavo.metratura_reale > 0;\n    setDialogType(isInstalled ? 'spare' : 'confirm');\n    setOpenDialog(true);\n  };\n\n  // Chiude il dialogo\n  const handleCloseDialog = () => {\n    setOpenDialog(false);\n    setSelectedCavo(null);\n    setDialogType('');\n    setForceSpare(false);\n  };\n\n  // Gestisce l'eliminazione o la marcatura come SPARE di un cavo\n  const handleDeleteCavo = async () => {\n    if (!selectedCavo) return;\n    try {\n      setLoading(true);\n      if (dialogType === 'spare') {\n        // Marca il cavo come SPARE\n        await caviService.markCavoAsSpare(cantiereId, selectedCavo.id_cavo, forceSpare);\n        handleSuccess(`Cavo ${selectedCavo.id_cavo} marcato come SPARE con successo`);\n      } else {\n        // Elimina il cavo o marcalo come SPARE in base alla modalità selezionata\n        await caviService.deleteCavo(cantiereId, selectedCavo.id_cavo, deleteMode);\n        handleSuccess(`Cavo ${selectedCavo.id_cavo} ${deleteMode === 'spare' ? 'marcato come SPARE' : 'eliminato'} con successo`);\n      }\n\n      // Ricarica la lista dei cavi\n      loadCavi();\n      handleCloseDialog();\n    } catch (error) {\n      handleError(`Errore durante l'operazione: ${error.message || 'Errore sconosciuto'}`);\n      console.error('Errore durante l\\'eliminazione/marcatura del cavo:', error);\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  // Filtra i cavi in base al termine di ricerca\n  const filteredCavi = cavi.filter(cavo => {\n    if (!searchTerm) return true;\n    const searchLower = searchTerm.toLowerCase();\n    return cavo.id_cavo.toLowerCase().includes(searchLower) || cavo.tipologia && cavo.tipologia.toLowerCase().includes(searchLower) || cavo.ubicazione_partenza && cavo.ubicazione_partenza.toLowerCase().includes(searchLower) || cavo.ubicazione_arrivo && cavo.ubicazione_arrivo.toLowerCase().includes(searchLower);\n  });\n\n  // Gestisce il cambio del termine di ricerca\n  const handleSearchChange = event => {\n    setSearchTerm(event.target.value);\n  };\n\n  // Se non c'è un cantiere selezionato, reindirizza alla pagina dei cantieri\n  if (!cantiereId) {\n    navigate('/dashboard/cantieri');\n    return null;\n  }\n  return /*#__PURE__*/_jsxDEV(Box, {\n    children: [/*#__PURE__*/_jsxDEV(Box, {\n      sx: {\n        mb: 3,\n        display: 'flex',\n        alignItems: 'center',\n        justifyContent: 'space-between'\n      },\n      children: [/*#__PURE__*/_jsxDEV(Box, {\n        sx: {\n          display: 'flex',\n          alignItems: 'center'\n        },\n        children: [/*#__PURE__*/_jsxDEV(IconButton, {\n          onClick: handleBackToPosa,\n          sx: {\n            mr: 1\n          },\n          children: /*#__PURE__*/_jsxDEV(ArrowBackIcon, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 193,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 192,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Typography, {\n          variant: \"h4\",\n          children: \"Elimina Cavo\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 195,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(IconButton, {\n          onClick: () => window.location.reload(),\n          sx: {\n            ml: 2\n          },\n          color: \"primary\",\n          title: \"Ricarica la pagina\",\n          children: /*#__PURE__*/_jsxDEV(RefreshIcon, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 204,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 198,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 191,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(AdminHomeButton, {}, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 207,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 190,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Paper, {\n      sx: {\n        mb: 3,\n        p: 2\n      },\n      children: /*#__PURE__*/_jsxDEV(Box, {\n        sx: {\n          display: 'flex',\n          justifyContent: 'space-between',\n          alignItems: 'center'\n        },\n        children: [/*#__PURE__*/_jsxDEV(Typography, {\n          variant: \"h6\",\n          children: [\"Cantiere: \", cantiereName, \" (ID: \", cantiereId, \")\"]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 212,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Button, {\n          variant: \"contained\",\n          color: \"primary\",\n          startIcon: /*#__PURE__*/_jsxDEV(ArrowBackIcon, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 218,\n            columnNumber: 24\n          }, this),\n          onClick: handleBackToPosa,\n          children: \"Torna a Posa e Collegamenti\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 215,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 211,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 210,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Paper, {\n      sx: {\n        p: 3\n      },\n      children: [/*#__PURE__*/_jsxDEV(Typography, {\n        variant: \"h6\",\n        gutterBottom: true,\n        children: \"Elimina Cavo\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 227,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Typography, {\n        variant: \"body1\",\n        paragraph: true,\n        children: \"Questa funzionalit\\xE0 consente di eliminare un cavo dal cantiere.\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 230,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Typography, {\n        variant: \"body2\",\n        color: \"textSecondary\",\n        paragraph: true,\n        children: \"Seleziona un cavo da eliminare. Per i cavi gi\\xE0 posati, verr\\xE0 offerta l'opzione di marcarli come SPARE invece di eliminarli completamente.\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 233,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Box, {\n        sx: {\n          mt: 3,\n          textAlign: 'center'\n        },\n        children: /*#__PURE__*/_jsxDEV(Typography, {\n          variant: \"body1\",\n          color: \"primary\",\n          children: \"Funzionalit\\xE0 in fase di implementazione\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 239,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 238,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 226,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Snackbar, {\n      open: openSnackbar,\n      autoHideDuration: 6000,\n      onClose: handleCloseSnackbar,\n      anchorOrigin: {\n        vertical: 'bottom',\n        horizontal: 'center'\n      },\n      children: /*#__PURE__*/_jsxDEV(Alert, {\n        onClose: handleCloseSnackbar,\n        severity: alertSeverity,\n        sx: {\n          width: '100%'\n        },\n        children: alertMessage\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 251,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 245,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 189,\n    columnNumber: 5\n  }, this);\n};\n_s(EliminaCavoPage, \"RkqJo82ELBhXlgYk2HAn5PhzZeE=\", false, function () {\n  return [useNavigate, useAuth];\n});\n_c = EliminaCavoPage;\nexport default EliminaCavoPage;\nvar _c;\n$RefreshReg$(_c, \"EliminaCavoPage\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "Box", "Typography", "Paper", "<PERSON><PERSON>", "IconButton", "<PERSON><PERSON>", "Snackbar", "List", "ListItem", "ListItemText", "ListItemSecondaryAction", "Dialog", "DialogTitle", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "DialogContentText", "DialogActions", "CircularProgress", "Divider", "Chip", "TextField", "Radio", "RadioGroup", "FormControlLabel", "FormControl", "FormLabel", "ArrowBack", "ArrowBackIcon", "Refresh", "RefreshIcon", "Home", "HomeIcon", "Delete", "DeleteIcon", "Warning", "WarningIcon", "Search", "SearchIcon", "useNavigate", "useAuth", "AdminHomeButton", "caviService", "jsxDEV", "_jsxDEV", "EliminaCavoPage", "_s", "navigate", "isImpersonating", "alertMessage", "setAlertMessage", "alertSeverity", "setAlertSeverity", "openSnackbar", "setOpenSnackbar", "cavi", "<PERSON><PERSON><PERSON>", "loading", "setLoading", "selected<PERSON><PERSON><PERSON>", "setSelectedCavo", "openDialog", "setOpenDialog", "dialogType", "setDialogType", "searchTerm", "setSearchTerm", "deleteMode", "setDeleteMode", "forceSpare", "setForceSpare", "cantiereId", "localStorage", "getItem", "cantiereName", "handleBackToCantieri", "handleBackToAdmin", "handleBackToPosa", "handleSuccess", "message", "handleError", "handleCloseSnackbar", "loadCavi", "data", "get<PERSON><PERSON>", "error", "console", "handleCavoSelect", "cavo", "isInstalled", "stato_installazione", "metratura_reale", "handleCloseDialog", "handleDeleteCavo", "markCavoAsSpare", "id_cavo", "deleteCavo", "filteredCavi", "filter", "searchLower", "toLowerCase", "includes", "tipologia", "ubicazione_partenza", "ubicazione_arrivo", "handleSearchChange", "event", "target", "value", "children", "sx", "mb", "display", "alignItems", "justifyContent", "onClick", "mr", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "variant", "window", "location", "reload", "ml", "color", "title", "p", "startIcon", "gutterBottom", "paragraph", "mt", "textAlign", "open", "autoHideDuration", "onClose", "anchor<PERSON><PERSON><PERSON>", "vertical", "horizontal", "severity", "width", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/PycharmProjects/CMS/webapp/frontend/src/pages/cavi/posa/EliminaCavoPage.js"], "sourcesContent": ["import React, { useState, useEffect } from 'react';\nimport {\n  Box,\n  Typography,\n  Paper,\n  Button,\n  IconButton,\n  Alert,\n  Snackbar,\n  List,\n  ListItem,\n  ListItemText,\n  ListItemSecondaryAction,\n  Dialog,\n  DialogTitle,\n  DialogContent,\n  DialogContentText,\n  DialogActions,\n  CircularProgress,\n  Divider,\n  Chip,\n  TextField,\n  Radio,\n  RadioGroup,\n  FormControlLabel,\n  FormControl,\n  FormLabel\n} from '@mui/material';\nimport {\n  ArrowBack as ArrowBackIcon,\n  Refresh as RefreshIcon,\n  Home as HomeIcon,\n  Delete as DeleteIcon,\n  Warning as WarningIcon,\n  Search as SearchIcon\n} from '@mui/icons-material';\nimport { useNavigate } from 'react-router-dom';\nimport { useAuth } from '../../../context/AuthContext';\nimport AdminHomeButton from '../../../components/common/AdminHomeButton';\nimport caviService from '../../../services/caviService';\n\nconst EliminaCavoPage = () => {\n  const navigate = useNavigate();\n  const { isImpersonating } = useAuth();\n  const [alertMessage, setAlertMessage] = useState(null);\n  const [alertSeverity, setAlertSeverity] = useState('success');\n  const [openSnackbar, setOpenSnackbar] = useState(false);\n\n  // Stati per la gestione dei cavi\n  const [cavi, setCavi] = useState([]);\n  const [loading, setLoading] = useState(false);\n  const [selectedCavo, setSelectedCavo] = useState(null);\n  const [openDialog, setOpenDialog] = useState(false);\n  const [dialogType, setDialogType] = useState(''); // 'delete' o 'spare'\n  const [searchTerm, setSearchTerm] = useState('');\n  const [deleteMode, setDeleteMode] = useState('spare'); // 'spare' o 'delete'\n  const [forceSpare, setForceSpare] = useState(false);\n\n  // Recupera l'ID del cantiere selezionato dal localStorage\n  const cantiereId = localStorage.getItem('selectedCantiereId');\n  const cantiereName = localStorage.getItem('selectedCantiereName');\n\n  // Gestisce il ritorno alla pagina dei cantieri\n  const handleBackToCantieri = () => {\n    navigate('/dashboard/cantieri');\n  };\n\n  // Gestisce il ritorno al menu admin (per admin che impersonano utenti)\n  const handleBackToAdmin = () => {\n    navigate('/dashboard/admin');\n  };\n\n  // Gestisce il ritorno alla pagina principale di posa cavi\n  const handleBackToPosa = () => {\n    navigate('/dashboard/cavi/posa');\n  };\n\n  // Gestisce il successo di un'operazione\n  const handleSuccess = (message) => {\n    setAlertMessage(message);\n    setAlertSeverity('success');\n    setOpenSnackbar(true);\n  };\n\n  // Gestisce l'errore di un'operazione\n  const handleError = (message) => {\n    setAlertMessage(message);\n    setAlertSeverity('error');\n    setOpenSnackbar(true);\n  };\n\n  // Chiude lo snackbar\n  const handleCloseSnackbar = () => {\n    setOpenSnackbar(false);\n  };\n\n  // Carica i cavi del cantiere\n  const loadCavi = async () => {\n    if (!cantiereId) return;\n\n    try {\n      setLoading(true);\n      const data = await caviService.getCavi(cantiereId, 0); // Tipo 0 = cavi attivi\n      setCavi(data);\n    } catch (error) {\n      handleError(`Errore nel caricamento dei cavi: ${error.message || 'Errore sconosciuto'}`);\n      console.error('Errore nel caricamento dei cavi:', error);\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  // Carica i cavi all'avvio del componente\n  useEffect(() => {\n    loadCavi();\n  }, [cantiereId]);\n\n  // Gestisce la selezione di un cavo\n  const handleCavoSelect = (cavo) => {\n    setSelectedCavo(cavo);\n\n    // Determina il tipo di dialogo in base allo stato del cavo\n    const isInstalled = cavo.stato_installazione === 'Installato' || (cavo.metratura_reale && cavo.metratura_reale > 0);\n    setDialogType(isInstalled ? 'spare' : 'confirm');\n    setOpenDialog(true);\n  };\n\n  // Chiude il dialogo\n  const handleCloseDialog = () => {\n    setOpenDialog(false);\n    setSelectedCavo(null);\n    setDialogType('');\n    setForceSpare(false);\n  };\n\n  // Gestisce l'eliminazione o la marcatura come SPARE di un cavo\n  const handleDeleteCavo = async () => {\n    if (!selectedCavo) return;\n\n    try {\n      setLoading(true);\n\n      if (dialogType === 'spare') {\n        // Marca il cavo come SPARE\n        await caviService.markCavoAsSpare(cantiereId, selectedCavo.id_cavo, forceSpare);\n        handleSuccess(`Cavo ${selectedCavo.id_cavo} marcato come SPARE con successo`);\n      } else {\n        // Elimina il cavo o marcalo come SPARE in base alla modalità selezionata\n        await caviService.deleteCavo(cantiereId, selectedCavo.id_cavo, deleteMode);\n        handleSuccess(`Cavo ${selectedCavo.id_cavo} ${deleteMode === 'spare' ? 'marcato come SPARE' : 'eliminato'} con successo`);\n      }\n\n      // Ricarica la lista dei cavi\n      loadCavi();\n      handleCloseDialog();\n    } catch (error) {\n      handleError(`Errore durante l'operazione: ${error.message || 'Errore sconosciuto'}`);\n      console.error('Errore durante l\\'eliminazione/marcatura del cavo:', error);\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  // Filtra i cavi in base al termine di ricerca\n  const filteredCavi = cavi.filter(cavo => {\n    if (!searchTerm) return true;\n\n    const searchLower = searchTerm.toLowerCase();\n    return (\n      cavo.id_cavo.toLowerCase().includes(searchLower) ||\n      (cavo.tipologia && cavo.tipologia.toLowerCase().includes(searchLower)) ||\n      (cavo.ubicazione_partenza && cavo.ubicazione_partenza.toLowerCase().includes(searchLower)) ||\n      (cavo.ubicazione_arrivo && cavo.ubicazione_arrivo.toLowerCase().includes(searchLower))\n    );\n  });\n\n  // Gestisce il cambio del termine di ricerca\n  const handleSearchChange = (event) => {\n    setSearchTerm(event.target.value);\n  };\n\n  // Se non c'è un cantiere selezionato, reindirizza alla pagina dei cantieri\n  if (!cantiereId) {\n    navigate('/dashboard/cantieri');\n    return null;\n  }\n\n  return (\n    <Box>\n      <Box sx={{ mb: 3, display: 'flex', alignItems: 'center', justifyContent: 'space-between' }}>\n        <Box sx={{ display: 'flex', alignItems: 'center' }}>\n          <IconButton onClick={handleBackToPosa} sx={{ mr: 1 }}>\n            <ArrowBackIcon />\n          </IconButton>\n          <Typography variant=\"h4\">\n            Elimina Cavo\n          </Typography>\n          <IconButton\n            onClick={() => window.location.reload()}\n            sx={{ ml: 2 }}\n            color=\"primary\"\n            title=\"Ricarica la pagina\"\n          >\n            <RefreshIcon />\n          </IconButton>\n        </Box>\n        <AdminHomeButton />\n      </Box>\n\n      <Paper sx={{ mb: 3, p: 2 }}>\n        <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>\n          <Typography variant=\"h6\">\n            Cantiere: {cantiereName} (ID: {cantiereId})\n          </Typography>\n          <Button\n            variant=\"contained\"\n            color=\"primary\"\n            startIcon={<ArrowBackIcon />}\n            onClick={handleBackToPosa}\n          >\n            Torna a Posa e Collegamenti\n          </Button>\n        </Box>\n      </Paper>\n\n      <Paper sx={{ p: 3 }}>\n        <Typography variant=\"h6\" gutterBottom>\n          Elimina Cavo\n        </Typography>\n        <Typography variant=\"body1\" paragraph>\n          Questa funzionalità consente di eliminare un cavo dal cantiere.\n        </Typography>\n        <Typography variant=\"body2\" color=\"textSecondary\" paragraph>\n          Seleziona un cavo da eliminare. Per i cavi già posati, verrà offerta l'opzione di marcarli come SPARE invece di eliminarli completamente.\n        </Typography>\n\n        {/* Qui verrà implementato il componente per l'eliminazione del cavo */}\n        <Box sx={{ mt: 3, textAlign: 'center' }}>\n          <Typography variant=\"body1\" color=\"primary\">\n            Funzionalità in fase di implementazione\n          </Typography>\n        </Box>\n      </Paper>\n\n      <Snackbar\n        open={openSnackbar}\n        autoHideDuration={6000}\n        onClose={handleCloseSnackbar}\n        anchorOrigin={{ vertical: 'bottom', horizontal: 'center' }}\n      >\n        <Alert onClose={handleCloseSnackbar} severity={alertSeverity} sx={{ width: '100%' }}>\n          {alertMessage}\n        </Alert>\n      </Snackbar>\n    </Box>\n  );\n};\n\nexport default EliminaCavoPage;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAClD,SACEC,GAAG,EACHC,UAAU,EACVC,KAAK,EACLC,MAAM,EACNC,UAAU,EACVC,KAAK,EACLC,QAAQ,EACRC,IAAI,EACJC,QAAQ,EACRC,YAAY,EACZC,uBAAuB,EACvBC,MAAM,EACNC,WAAW,EACXC,aAAa,EACbC,iBAAiB,EACjBC,aAAa,EACbC,gBAAgB,EAChBC,OAAO,EACPC,IAAI,EACJC,SAAS,EACTC,KAAK,EACLC,UAAU,EACVC,gBAAgB,EAChBC,WAAW,EACXC,SAAS,QACJ,eAAe;AACtB,SACEC,SAAS,IAAIC,aAAa,EAC1BC,OAAO,IAAIC,WAAW,EACtBC,IAAI,IAAIC,QAAQ,EAChBC,MAAM,IAAIC,UAAU,EACpBC,OAAO,IAAIC,WAAW,EACtBC,MAAM,IAAIC,UAAU,QACf,qBAAqB;AAC5B,SAASC,WAAW,QAAQ,kBAAkB;AAC9C,SAASC,OAAO,QAAQ,8BAA8B;AACtD,OAAOC,eAAe,MAAM,4CAA4C;AACxE,OAAOC,WAAW,MAAM,+BAA+B;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAExD,MAAMC,eAAe,GAAGA,CAAA,KAAM;EAAAC,EAAA;EAC5B,MAAMC,QAAQ,GAAGR,WAAW,CAAC,CAAC;EAC9B,MAAM;IAAES;EAAgB,CAAC,GAAGR,OAAO,CAAC,CAAC;EACrC,MAAM,CAACS,YAAY,EAAEC,eAAe,CAAC,GAAGlD,QAAQ,CAAC,IAAI,CAAC;EACtD,MAAM,CAACmD,aAAa,EAAEC,gBAAgB,CAAC,GAAGpD,QAAQ,CAAC,SAAS,CAAC;EAC7D,MAAM,CAACqD,YAAY,EAAEC,eAAe,CAAC,GAAGtD,QAAQ,CAAC,KAAK,CAAC;;EAEvD;EACA,MAAM,CAACuD,IAAI,EAAEC,OAAO,CAAC,GAAGxD,QAAQ,CAAC,EAAE,CAAC;EACpC,MAAM,CAACyD,OAAO,EAAEC,UAAU,CAAC,GAAG1D,QAAQ,CAAC,KAAK,CAAC;EAC7C,MAAM,CAAC2D,YAAY,EAAEC,eAAe,CAAC,GAAG5D,QAAQ,CAAC,IAAI,CAAC;EACtD,MAAM,CAAC6D,UAAU,EAAEC,aAAa,CAAC,GAAG9D,QAAQ,CAAC,KAAK,CAAC;EACnD,MAAM,CAAC+D,UAAU,EAAEC,aAAa,CAAC,GAAGhE,QAAQ,CAAC,EAAE,CAAC,CAAC,CAAC;EAClD,MAAM,CAACiE,UAAU,EAAEC,aAAa,CAAC,GAAGlE,QAAQ,CAAC,EAAE,CAAC;EAChD,MAAM,CAACmE,UAAU,EAAEC,aAAa,CAAC,GAAGpE,QAAQ,CAAC,OAAO,CAAC,CAAC,CAAC;EACvD,MAAM,CAACqE,UAAU,EAAEC,aAAa,CAAC,GAAGtE,QAAQ,CAAC,KAAK,CAAC;;EAEnD;EACA,MAAMuE,UAAU,GAAGC,YAAY,CAACC,OAAO,CAAC,oBAAoB,CAAC;EAC7D,MAAMC,YAAY,GAAGF,YAAY,CAACC,OAAO,CAAC,sBAAsB,CAAC;;EAEjE;EACA,MAAME,oBAAoB,GAAGA,CAAA,KAAM;IACjC5B,QAAQ,CAAC,qBAAqB,CAAC;EACjC,CAAC;;EAED;EACA,MAAM6B,iBAAiB,GAAGA,CAAA,KAAM;IAC9B7B,QAAQ,CAAC,kBAAkB,CAAC;EAC9B,CAAC;;EAED;EACA,MAAM8B,gBAAgB,GAAGA,CAAA,KAAM;IAC7B9B,QAAQ,CAAC,sBAAsB,CAAC;EAClC,CAAC;;EAED;EACA,MAAM+B,aAAa,GAAIC,OAAO,IAAK;IACjC7B,eAAe,CAAC6B,OAAO,CAAC;IACxB3B,gBAAgB,CAAC,SAAS,CAAC;IAC3BE,eAAe,CAAC,IAAI,CAAC;EACvB,CAAC;;EAED;EACA,MAAM0B,WAAW,GAAID,OAAO,IAAK;IAC/B7B,eAAe,CAAC6B,OAAO,CAAC;IACxB3B,gBAAgB,CAAC,OAAO,CAAC;IACzBE,eAAe,CAAC,IAAI,CAAC;EACvB,CAAC;;EAED;EACA,MAAM2B,mBAAmB,GAAGA,CAAA,KAAM;IAChC3B,eAAe,CAAC,KAAK,CAAC;EACxB,CAAC;;EAED;EACA,MAAM4B,QAAQ,GAAG,MAAAA,CAAA,KAAY;IAC3B,IAAI,CAACX,UAAU,EAAE;IAEjB,IAAI;MACFb,UAAU,CAAC,IAAI,CAAC;MAChB,MAAMyB,IAAI,GAAG,MAAMzC,WAAW,CAAC0C,OAAO,CAACb,UAAU,EAAE,CAAC,CAAC,CAAC,CAAC;MACvDf,OAAO,CAAC2B,IAAI,CAAC;IACf,CAAC,CAAC,OAAOE,KAAK,EAAE;MACdL,WAAW,CAAC,oCAAoCK,KAAK,CAACN,OAAO,IAAI,oBAAoB,EAAE,CAAC;MACxFO,OAAO,CAACD,KAAK,CAAC,kCAAkC,EAAEA,KAAK,CAAC;IAC1D,CAAC,SAAS;MACR3B,UAAU,CAAC,KAAK,CAAC;IACnB;EACF,CAAC;;EAED;EACAzD,SAAS,CAAC,MAAM;IACdiF,QAAQ,CAAC,CAAC;EACZ,CAAC,EAAE,CAACX,UAAU,CAAC,CAAC;;EAEhB;EACA,MAAMgB,gBAAgB,GAAIC,IAAI,IAAK;IACjC5B,eAAe,CAAC4B,IAAI,CAAC;;IAErB;IACA,MAAMC,WAAW,GAAGD,IAAI,CAACE,mBAAmB,KAAK,YAAY,IAAKF,IAAI,CAACG,eAAe,IAAIH,IAAI,CAACG,eAAe,GAAG,CAAE;IACnH3B,aAAa,CAACyB,WAAW,GAAG,OAAO,GAAG,SAAS,CAAC;IAChD3B,aAAa,CAAC,IAAI,CAAC;EACrB,CAAC;;EAED;EACA,MAAM8B,iBAAiB,GAAGA,CAAA,KAAM;IAC9B9B,aAAa,CAAC,KAAK,CAAC;IACpBF,eAAe,CAAC,IAAI,CAAC;IACrBI,aAAa,CAAC,EAAE,CAAC;IACjBM,aAAa,CAAC,KAAK,CAAC;EACtB,CAAC;;EAED;EACA,MAAMuB,gBAAgB,GAAG,MAAAA,CAAA,KAAY;IACnC,IAAI,CAAClC,YAAY,EAAE;IAEnB,IAAI;MACFD,UAAU,CAAC,IAAI,CAAC;MAEhB,IAAIK,UAAU,KAAK,OAAO,EAAE;QAC1B;QACA,MAAMrB,WAAW,CAACoD,eAAe,CAACvB,UAAU,EAAEZ,YAAY,CAACoC,OAAO,EAAE1B,UAAU,CAAC;QAC/ES,aAAa,CAAC,QAAQnB,YAAY,CAACoC,OAAO,kCAAkC,CAAC;MAC/E,CAAC,MAAM;QACL;QACA,MAAMrD,WAAW,CAACsD,UAAU,CAACzB,UAAU,EAAEZ,YAAY,CAACoC,OAAO,EAAE5B,UAAU,CAAC;QAC1EW,aAAa,CAAC,QAAQnB,YAAY,CAACoC,OAAO,IAAI5B,UAAU,KAAK,OAAO,GAAG,oBAAoB,GAAG,WAAW,eAAe,CAAC;MAC3H;;MAEA;MACAe,QAAQ,CAAC,CAAC;MACVU,iBAAiB,CAAC,CAAC;IACrB,CAAC,CAAC,OAAOP,KAAK,EAAE;MACdL,WAAW,CAAC,gCAAgCK,KAAK,CAACN,OAAO,IAAI,oBAAoB,EAAE,CAAC;MACpFO,OAAO,CAACD,KAAK,CAAC,oDAAoD,EAAEA,KAAK,CAAC;IAC5E,CAAC,SAAS;MACR3B,UAAU,CAAC,KAAK,CAAC;IACnB;EACF,CAAC;;EAED;EACA,MAAMuC,YAAY,GAAG1C,IAAI,CAAC2C,MAAM,CAACV,IAAI,IAAI;IACvC,IAAI,CAACvB,UAAU,EAAE,OAAO,IAAI;IAE5B,MAAMkC,WAAW,GAAGlC,UAAU,CAACmC,WAAW,CAAC,CAAC;IAC5C,OACEZ,IAAI,CAACO,OAAO,CAACK,WAAW,CAAC,CAAC,CAACC,QAAQ,CAACF,WAAW,CAAC,IAC/CX,IAAI,CAACc,SAAS,IAAId,IAAI,CAACc,SAAS,CAACF,WAAW,CAAC,CAAC,CAACC,QAAQ,CAACF,WAAW,CAAE,IACrEX,IAAI,CAACe,mBAAmB,IAAIf,IAAI,CAACe,mBAAmB,CAACH,WAAW,CAAC,CAAC,CAACC,QAAQ,CAACF,WAAW,CAAE,IACzFX,IAAI,CAACgB,iBAAiB,IAAIhB,IAAI,CAACgB,iBAAiB,CAACJ,WAAW,CAAC,CAAC,CAACC,QAAQ,CAACF,WAAW,CAAE;EAE1F,CAAC,CAAC;;EAEF;EACA,MAAMM,kBAAkB,GAAIC,KAAK,IAAK;IACpCxC,aAAa,CAACwC,KAAK,CAACC,MAAM,CAACC,KAAK,CAAC;EACnC,CAAC;;EAED;EACA,IAAI,CAACrC,UAAU,EAAE;IACfxB,QAAQ,CAAC,qBAAqB,CAAC;IAC/B,OAAO,IAAI;EACb;EAEA,oBACEH,OAAA,CAAC1C,GAAG;IAAA2G,QAAA,gBACFjE,OAAA,CAAC1C,GAAG;MAAC4G,EAAE,EAAE;QAAEC,EAAE,EAAE,CAAC;QAAEC,OAAO,EAAE,MAAM;QAAEC,UAAU,EAAE,QAAQ;QAAEC,cAAc,EAAE;MAAgB,CAAE;MAAAL,QAAA,gBACzFjE,OAAA,CAAC1C,GAAG;QAAC4G,EAAE,EAAE;UAAEE,OAAO,EAAE,MAAM;UAAEC,UAAU,EAAE;QAAS,CAAE;QAAAJ,QAAA,gBACjDjE,OAAA,CAACtC,UAAU;UAAC6G,OAAO,EAAEtC,gBAAiB;UAACiC,EAAE,EAAE;YAAEM,EAAE,EAAE;UAAE,CAAE;UAAAP,QAAA,eACnDjE,OAAA,CAAChB,aAAa;YAAAyF,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACP,CAAC,eACb5E,OAAA,CAACzC,UAAU;UAACsH,OAAO,EAAC,IAAI;UAAAZ,QAAA,EAAC;QAEzB;UAAAQ,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAY,CAAC,eACb5E,OAAA,CAACtC,UAAU;UACT6G,OAAO,EAAEA,CAAA,KAAMO,MAAM,CAACC,QAAQ,CAACC,MAAM,CAAC,CAAE;UACxCd,EAAE,EAAE;YAAEe,EAAE,EAAE;UAAE,CAAE;UACdC,KAAK,EAAC,SAAS;UACfC,KAAK,EAAC,oBAAoB;UAAAlB,QAAA,eAE1BjE,OAAA,CAACd,WAAW;YAAAuF,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACL,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACV,CAAC,eACN5E,OAAA,CAACH,eAAe;QAAA4E,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAChB,CAAC,eAEN5E,OAAA,CAACxC,KAAK;MAAC0G,EAAE,EAAE;QAAEC,EAAE,EAAE,CAAC;QAAEiB,CAAC,EAAE;MAAE,CAAE;MAAAnB,QAAA,eACzBjE,OAAA,CAAC1C,GAAG;QAAC4G,EAAE,EAAE;UAAEE,OAAO,EAAE,MAAM;UAAEE,cAAc,EAAE,eAAe;UAAED,UAAU,EAAE;QAAS,CAAE;QAAAJ,QAAA,gBAClFjE,OAAA,CAACzC,UAAU;UAACsH,OAAO,EAAC,IAAI;UAAAZ,QAAA,GAAC,YACb,EAACnC,YAAY,EAAC,QAAM,EAACH,UAAU,EAAC,GAC5C;QAAA;UAAA8C,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAY,CAAC,eACb5E,OAAA,CAACvC,MAAM;UACLoH,OAAO,EAAC,WAAW;UACnBK,KAAK,EAAC,SAAS;UACfG,SAAS,eAAErF,OAAA,CAAChB,aAAa;YAAAyF,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAE;UAC7BL,OAAO,EAAEtC,gBAAiB;UAAAgC,QAAA,EAC3B;QAED;UAAAQ,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACN;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACD,CAAC,eAER5E,OAAA,CAACxC,KAAK;MAAC0G,EAAE,EAAE;QAAEkB,CAAC,EAAE;MAAE,CAAE;MAAAnB,QAAA,gBAClBjE,OAAA,CAACzC,UAAU;QAACsH,OAAO,EAAC,IAAI;QAACS,YAAY;QAAArB,QAAA,EAAC;MAEtC;QAAAQ,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAY,CAAC,eACb5E,OAAA,CAACzC,UAAU;QAACsH,OAAO,EAAC,OAAO;QAACU,SAAS;QAAAtB,QAAA,EAAC;MAEtC;QAAAQ,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAY,CAAC,eACb5E,OAAA,CAACzC,UAAU;QAACsH,OAAO,EAAC,OAAO;QAACK,KAAK,EAAC,eAAe;QAACK,SAAS;QAAAtB,QAAA,EAAC;MAE5D;QAAAQ,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAY,CAAC,eAGb5E,OAAA,CAAC1C,GAAG;QAAC4G,EAAE,EAAE;UAAEsB,EAAE,EAAE,CAAC;UAAEC,SAAS,EAAE;QAAS,CAAE;QAAAxB,QAAA,eACtCjE,OAAA,CAACzC,UAAU;UAACsH,OAAO,EAAC,OAAO;UAACK,KAAK,EAAC,SAAS;UAAAjB,QAAA,EAAC;QAE5C;UAAAQ,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAY;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACV,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACD,CAAC,eAER5E,OAAA,CAACpC,QAAQ;MACP8H,IAAI,EAAEjF,YAAa;MACnBkF,gBAAgB,EAAE,IAAK;MACvBC,OAAO,EAAEvD,mBAAoB;MAC7BwD,YAAY,EAAE;QAAEC,QAAQ,EAAE,QAAQ;QAAEC,UAAU,EAAE;MAAS,CAAE;MAAA9B,QAAA,eAE3DjE,OAAA,CAACrC,KAAK;QAACiI,OAAO,EAAEvD,mBAAoB;QAAC2D,QAAQ,EAAEzF,aAAc;QAAC2D,EAAE,EAAE;UAAE+B,KAAK,EAAE;QAAO,CAAE;QAAAhC,QAAA,EACjF5D;MAAY;QAAAoE,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACR;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACA,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACR,CAAC;AAEV,CAAC;AAAC1E,EAAA,CAvNID,eAAe;EAAA,QACFN,WAAW,EACAC,OAAO;AAAA;AAAAsG,EAAA,GAF/BjG,eAAe;AAyNrB,eAAeA,eAAe;AAAC,IAAAiG,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}