{"ast": null, "code": "/**\n * Utility per la gestione degli stati dei cavi e delle bobine\n * Implementa le stesse regole di transizione della CLI\n */\n\n// Stati del cavo\nexport const CABLE_STATES = {\n  DA_INSTALLARE: 'Da installare',\n  IN_CORSO: 'In corso',\n  INSTALLATO: 'Installato',\n  SPARE: 'SPARE'\n};\n\n// Stati della bobina\nexport const REEL_STATES = {\n  DISPONIBILE: 'Disponibile',\n  IN_USO: 'In uso',\n  TERMINATA: 'Terminata',\n  OVER: 'Over'\n};\n\n/**\n * Determina lo stato di installazione di un cavo in base ai metri posati\n * @param {number} metriPosati - Metri posati\n * @param {number} metriTeorici - Metri teorici\n * @returns {string} - Stato di installazione\n */\nexport const determineCableState = (metriPosati, metriTeorici) => {\n  if (!metriPosati || parseFloat(metriPosati) <= 0) {\n    return CABLE_STATES.DA_INSTALLARE;\n  }\n\n  // Imposta sempre lo stato a INSTALLATO quando si posano metri\n  // indipendentemente dal confronto con i metri teorici\n  return CABLE_STATES.INSTALLATO;\n};\n\n/**\n * Determina lo stato di una bobina in base ai metri residui e totali\n * @param {number} metriResidui - Metri residui\n * @param {number} metriTotali - Metri totali\n * @returns {string} - Stato della bobina\n */\nexport const determineReelState = (metriResidui, metriTotali) => {\n  if (metriResidui < 0) {\n    return REEL_STATES.OVER;\n  }\n  if (metriResidui === 0) {\n    return REEL_STATES.TERMINATA;\n  }\n  if (metriResidui < metriTotali) {\n    return REEL_STATES.IN_USO;\n  }\n  return REEL_STATES.DISPONIBILE;\n};\n\n/**\n * Verifica se un cavo può essere modificato in base al suo stato\n * @param {Object} cavo - Oggetto cavo\n * @returns {boolean} - True se il cavo può essere modificato, false altrimenti\n */\nexport const canModifyCable = cavo => {\n  // Un cavo può essere modificato se:\n  // 1. È in stato DA_INSTALLARE\n  // 2. Non ha metri posati (metratura_reale = 0)\n  return cavo.stato_installazione === CABLE_STATES.DA_INSTALLARE && (!cavo.metratura_reale || parseFloat(cavo.metratura_reale) === 0);\n};\n\n/**\n * Verifica se un cavo è in stato SPARE\n * @param {Object} cavo - Oggetto cavo\n * @returns {boolean} - True se il cavo è in stato SPARE, false altrimenti\n */\nexport const isCableSpare = cavo => {\n  return cavo.modificato_manualmente === 3 || cavo.stato_installazione === CABLE_STATES.SPARE;\n};\n\n/**\n * Verifica se un cavo è già installato\n * @param {Object} cavo - Oggetto cavo\n * @returns {boolean} - True se il cavo è già installato, false altrimenti\n */\nexport const isCableInstalled = cavo => {\n  return cavo.stato_installazione === CABLE_STATES.INSTALLATO || cavo.metratura_reale && parseFloat(cavo.metratura_reale) > 0;\n};\n\n/**\n * Verifica se una bobina può essere modificata in base al suo stato\n * @param {Object} bobina - Oggetto bobina\n * @returns {boolean} - True se la bobina può essere modificata, false altrimenti\n */\nexport const canModifyReel = bobina => {\n  // Una bobina può essere modificata se:\n  // 1. È in stato DISPONIBILE\n  // 2. Non è in stato TERMINATA o OVER\n  return bobina.stato_bobina === REEL_STATES.DISPONIBILE || bobina.stato_bobina === REEL_STATES.IN_USO;\n};\n\n/**\n * Ottiene il colore associato a uno stato del cavo\n * @param {string} stato - Stato del cavo\n * @returns {string} - Colore associato allo stato\n */\nexport const getCableStateColor = stato => {\n  switch (stato) {\n    case CABLE_STATES.INSTALLATO:\n      return 'success';\n    case CABLE_STATES.IN_CORSO:\n      return 'warning';\n    case CABLE_STATES.SPARE:\n      return 'error';\n    case CABLE_STATES.DA_INSTALLARE:\n    default:\n      return 'default';\n  }\n};\n\n/**\n * Ottiene il colore associato a uno stato della bobina\n * @param {string} stato - Stato della bobina\n * @returns {string} - Colore associato allo stato\n */\nexport const getReelStateColor = stato => {\n  switch (stato) {\n    case REEL_STATES.DISPONIBILE:\n      return 'success';\n    case REEL_STATES.IN_USO:\n      return 'primary';\n    case REEL_STATES.TERMINATA:\n      return 'warning';\n    case REEL_STATES.OVER:\n      return 'error';\n    default:\n      return 'default';\n  }\n};", "map": {"version": 3, "names": ["CABLE_STATES", "DA_INSTALLARE", "IN_CORSO", "INSTALLATO", "SPARE", "REEL_STATES", "DISPONIBILE", "IN_USO", "TERMINATA", "OVER", "determineCableState", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "metriTeorici", "parseFloat", "determineReelState", "metriResidui", "metriTotali", "canModifyCable", "cavo", "stato_installazione", "metratura_reale", "isCableSpare", "modificato_manualmente", "isCableInstalled", "canModifyReel", "bobina", "stato_bobina", "getCableStateColor", "stato", "getReelStateColor"], "sources": ["C:/CMS/webapp/frontend/src/utils/stateUtils.js"], "sourcesContent": ["/**\n * Utility per la gestione degli stati dei cavi e delle bobine\n * Implementa le stesse regole di transizione della CLI\n */\n\n// Stati del cavo\nexport const CABLE_STATES = {\n  DA_INSTALLARE: 'Da installare',\n  IN_CORSO: 'In corso',\n  INSTALLATO: 'Installato',\n  SPARE: 'SPARE'\n};\n\n// Stati della bobina\nexport const REEL_STATES = {\n  DISPONIBILE: 'Disponibile',\n  IN_USO: 'In uso',\n  TERMINATA: 'Terminata',\n  OVER: 'Over'\n};\n\n/**\n * Determina lo stato di installazione di un cavo in base ai metri posati\n * @param {number} metriPosati - Metri posati\n * @param {number} metriTeorici - Metri teorici\n * @returns {string} - Stato di installazione\n */\nexport const determineCableState = (metriPosati, metriTeorici) => {\n  if (!metriPosati || parseFloat(metriPosati) <= 0) {\n    return CABLE_STATES.DA_INSTALLARE;\n  }\n\n  // Imposta sempre lo stato a INSTALLATO quando si posano metri\n  // indipendentemente dal confronto con i metri teorici\n  return CABLE_STATES.INSTALLATO;\n};\n\n/**\n * Determina lo stato di una bobina in base ai metri residui e totali\n * @param {number} metriResidui - Metri residui\n * @param {number} metriTotali - Metri totali\n * @returns {string} - Stato della bobina\n */\nexport const determineReelState = (metriResidui, metriTotali) => {\n  if (metriResidui < 0) {\n    return REEL_STATES.OVER;\n  }\n\n  if (metriResidui === 0) {\n    return REEL_STATES.TERMINATA;\n  }\n\n  if (metriResidui < metriTotali) {\n    return REEL_STATES.IN_USO;\n  }\n\n  return REEL_STATES.DISPONIBILE;\n};\n\n/**\n * Verifica se un cavo può essere modificato in base al suo stato\n * @param {Object} cavo - Oggetto cavo\n * @returns {boolean} - True se il cavo può essere modificato, false altrimenti\n */\nexport const canModifyCable = (cavo) => {\n  // Un cavo può essere modificato se:\n  // 1. È in stato DA_INSTALLARE\n  // 2. Non ha metri posati (metratura_reale = 0)\n  return cavo.stato_installazione === CABLE_STATES.DA_INSTALLARE && \n         (!cavo.metratura_reale || parseFloat(cavo.metratura_reale) === 0);\n};\n\n/**\n * Verifica se un cavo è in stato SPARE\n * @param {Object} cavo - Oggetto cavo\n * @returns {boolean} - True se il cavo è in stato SPARE, false altrimenti\n */\nexport const isCableSpare = (cavo) => {\n  return cavo.modificato_manualmente === 3 || cavo.stato_installazione === CABLE_STATES.SPARE;\n};\n\n/**\n * Verifica se un cavo è già installato\n * @param {Object} cavo - Oggetto cavo\n * @returns {boolean} - True se il cavo è già installato, false altrimenti\n */\nexport const isCableInstalled = (cavo) => {\n  return cavo.stato_installazione === CABLE_STATES.INSTALLATO || \n         (cavo.metratura_reale && parseFloat(cavo.metratura_reale) > 0);\n};\n\n/**\n * Verifica se una bobina può essere modificata in base al suo stato\n * @param {Object} bobina - Oggetto bobina\n * @returns {boolean} - True se la bobina può essere modificata, false altrimenti\n */\nexport const canModifyReel = (bobina) => {\n  // Una bobina può essere modificata se:\n  // 1. È in stato DISPONIBILE\n  // 2. Non è in stato TERMINATA o OVER\n  return bobina.stato_bobina === REEL_STATES.DISPONIBILE || \n         bobina.stato_bobina === REEL_STATES.IN_USO;\n};\n\n/**\n * Ottiene il colore associato a uno stato del cavo\n * @param {string} stato - Stato del cavo\n * @returns {string} - Colore associato allo stato\n */\nexport const getCableStateColor = (stato) => {\n  switch (stato) {\n    case CABLE_STATES.INSTALLATO:\n      return 'success';\n    case CABLE_STATES.IN_CORSO:\n      return 'warning';\n    case CABLE_STATES.SPARE:\n      return 'error';\n    case CABLE_STATES.DA_INSTALLARE:\n    default:\n      return 'default';\n  }\n};\n\n/**\n * Ottiene il colore associato a uno stato della bobina\n * @param {string} stato - Stato della bobina\n * @returns {string} - Colore associato allo stato\n */\nexport const getReelStateColor = (stato) => {\n  switch (stato) {\n    case REEL_STATES.DISPONIBILE:\n      return 'success';\n    case REEL_STATES.IN_USO:\n      return 'primary';\n    case REEL_STATES.TERMINATA:\n      return 'warning';\n    case REEL_STATES.OVER:\n      return 'error';\n    default:\n      return 'default';\n  }\n};\n"], "mappings": "AAAA;AACA;AACA;AACA;;AAEA;AACA,OAAO,MAAMA,YAAY,GAAG;EAC1BC,aAAa,EAAE,eAAe;EAC9BC,QAAQ,EAAE,UAAU;EACpBC,UAAU,EAAE,YAAY;EACxBC,KAAK,EAAE;AACT,CAAC;;AAED;AACA,OAAO,MAAMC,WAAW,GAAG;EACzBC,WAAW,EAAE,aAAa;EAC1BC,MAAM,EAAE,QAAQ;EAChBC,SAAS,EAAE,WAAW;EACtBC,IAAI,EAAE;AACR,CAAC;;AAED;AACA;AACA;AACA;AACA;AACA;AACA,OAAO,MAAMC,mBAAmB,GAAGA,CAACC,WAAW,EAAEC,YAAY,KAAK;EAChE,IAAI,CAACD,WAAW,IAAIE,UAAU,CAACF,WAAW,CAAC,IAAI,CAAC,EAAE;IAChD,OAAOX,YAAY,CAACC,aAAa;EACnC;;EAEA;EACA;EACA,OAAOD,YAAY,CAACG,UAAU;AAChC,CAAC;;AAED;AACA;AACA;AACA;AACA;AACA;AACA,OAAO,MAAMW,kBAAkB,GAAGA,CAACC,YAAY,EAAEC,WAAW,KAAK;EAC/D,IAAID,YAAY,GAAG,CAAC,EAAE;IACpB,OAAOV,WAAW,CAACI,IAAI;EACzB;EAEA,IAAIM,YAAY,KAAK,CAAC,EAAE;IACtB,OAAOV,WAAW,CAACG,SAAS;EAC9B;EAEA,IAAIO,YAAY,GAAGC,WAAW,EAAE;IAC9B,OAAOX,WAAW,CAACE,MAAM;EAC3B;EAEA,OAAOF,WAAW,CAACC,WAAW;AAChC,CAAC;;AAED;AACA;AACA;AACA;AACA;AACA,OAAO,MAAMW,cAAc,GAAIC,IAAI,IAAK;EACtC;EACA;EACA;EACA,OAAOA,IAAI,CAACC,mBAAmB,KAAKnB,YAAY,CAACC,aAAa,KACtD,CAACiB,IAAI,CAACE,eAAe,IAAIP,UAAU,CAACK,IAAI,CAACE,eAAe,CAAC,KAAK,CAAC,CAAC;AAC1E,CAAC;;AAED;AACA;AACA;AACA;AACA;AACA,OAAO,MAAMC,YAAY,GAAIH,IAAI,IAAK;EACpC,OAAOA,IAAI,CAACI,sBAAsB,KAAK,CAAC,IAAIJ,IAAI,CAACC,mBAAmB,KAAKnB,YAAY,CAACI,KAAK;AAC7F,CAAC;;AAED;AACA;AACA;AACA;AACA;AACA,OAAO,MAAMmB,gBAAgB,GAAIL,IAAI,IAAK;EACxC,OAAOA,IAAI,CAACC,mBAAmB,KAAKnB,YAAY,CAACG,UAAU,IACnDe,IAAI,CAACE,eAAe,IAAIP,UAAU,CAACK,IAAI,CAACE,eAAe,CAAC,GAAG,CAAE;AACvE,CAAC;;AAED;AACA;AACA;AACA;AACA;AACA,OAAO,MAAMI,aAAa,GAAIC,MAAM,IAAK;EACvC;EACA;EACA;EACA,OAAOA,MAAM,CAACC,YAAY,KAAKrB,WAAW,CAACC,WAAW,IAC/CmB,MAAM,CAACC,YAAY,KAAKrB,WAAW,CAACE,MAAM;AACnD,CAAC;;AAED;AACA;AACA;AACA;AACA;AACA,OAAO,MAAMoB,kBAAkB,GAAIC,KAAK,IAAK;EAC3C,QAAQA,KAAK;IACX,KAAK5B,YAAY,CAACG,UAAU;MAC1B,OAAO,SAAS;IAClB,KAAKH,YAAY,CAACE,QAAQ;MACxB,OAAO,SAAS;IAClB,KAAKF,YAAY,CAACI,KAAK;MACrB,OAAO,OAAO;IAChB,KAAKJ,YAAY,CAACC,aAAa;IAC/B;MACE,OAAO,SAAS;EACpB;AACF,CAAC;;AAED;AACA;AACA;AACA;AACA;AACA,OAAO,MAAM4B,iBAAiB,GAAID,KAAK,IAAK;EAC1C,QAAQA,KAAK;IACX,KAAKvB,WAAW,CAACC,WAAW;MAC1B,OAAO,SAAS;IAClB,KAAKD,WAAW,CAACE,MAAM;MACrB,OAAO,SAAS;IAClB,KAAKF,WAAW,CAACG,SAAS;MACxB,OAAO,SAAS;IAClB,KAAKH,WAAW,CAACI,IAAI;MACnB,OAAO,OAAO;IAChB;MACE,OAAO,SAAS;EACpB;AACF,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}