{"ast": null, "code": "import { normalizeDates } from \"./_lib/normalizeDates.js\";\n\n/**\n * The {@link isSameYear} function options.\n */\n\n/**\n * @name isSameYear\n * @category Year Helpers\n * @summary Are the given dates in the same year?\n *\n * @description\n * Are the given dates in the same year?\n *\n * @param laterDate - The first date to check\n * @param earlierDate - The second date to check\n * @param options - An object with options\n *\n * @returns The dates are in the same year\n *\n * @example\n * // Are 2 September 2014 and 25 September 2014 in the same year?\n * const result = isSameYear(new Date(2014, 8, 2), new Date(2014, 8, 25))\n * //=> true\n */\nexport function isSameYear(laterDate, earlierDate, options) {\n  const [laterDate_, earlierDate_] = normalizeDates(options?.in, laterDate, earlierDate);\n  return laterDate_.getFullYear() === earlierDate_.getFullYear();\n}\n\n// Fallback for modularized imports:\nexport default isSameYear;", "map": {"version": 3, "names": ["normalizeDates", "isSameYear", "laterDate", "earlierDate", "options", "laterDate_", "earlierDate_", "in", "getFullYear"], "sources": ["C:/Users/<USER>/PycharmProjects/CMS/webapp/frontend/node_modules/date-fns/isSameYear.js"], "sourcesContent": ["import { normalizeDates } from \"./_lib/normalizeDates.js\";\n\n/**\n * The {@link isSameYear} function options.\n */\n\n/**\n * @name isSameYear\n * @category Year Helpers\n * @summary Are the given dates in the same year?\n *\n * @description\n * Are the given dates in the same year?\n *\n * @param laterDate - The first date to check\n * @param earlierDate - The second date to check\n * @param options - An object with options\n *\n * @returns The dates are in the same year\n *\n * @example\n * // Are 2 September 2014 and 25 September 2014 in the same year?\n * const result = isSameYear(new Date(2014, 8, 2), new Date(2014, 8, 25))\n * //=> true\n */\nexport function isSameYear(laterDate, earlierDate, options) {\n  const [laterDate_, earlierDate_] = normalizeDates(\n    options?.in,\n    laterDate,\n    earlierDate,\n  );\n  return laterDate_.getFullYear() === earlierDate_.getFullYear();\n}\n\n// Fallback for modularized imports:\nexport default isSameYear;\n"], "mappings": "AAAA,SAASA,cAAc,QAAQ,0BAA0B;;AAEzD;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO,SAASC,UAAUA,CAACC,SAAS,EAAEC,WAAW,EAAEC,OAAO,EAAE;EAC1D,MAAM,CAACC,UAAU,EAAEC,YAAY,CAAC,GAAGN,cAAc,CAC/CI,OAAO,EAAEG,EAAE,EACXL,SAAS,EACTC,WACF,CAAC;EACD,OAAOE,UAAU,CAACG,WAAW,CAAC,CAAC,KAAKF,YAAY,CAACE,WAAW,CAAC,CAAC;AAChE;;AAEA;AACA,eAAeP,UAAU", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}