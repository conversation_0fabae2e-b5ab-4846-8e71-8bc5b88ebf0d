{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\PycharmProjects\\\\CMS\\\\webapp\\\\frontend\\\\src\\\\pages\\\\cavi\\\\VisualizzaCaviPage.js\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect } from 'react';\nimport { Box, Typography, Paper, Button, Grid, Card, CardContent, Alert, IconButton, Chip, CircularProgress, LinearProgress, Dialog, DialogTitle, DialogContent, DialogActions } from '@mui/material';\nimport InfoIcon from '@mui/icons-material/Info';\nimport RefreshIcon from '@mui/icons-material/Refresh';\nimport { useNavigate } from 'react-router-dom';\nimport { useAuth } from '../../context/AuthContext';\nimport { useGlobalContext } from '../../context/GlobalContext';\nimport PosaCaviCollegamenti from '../../components/cavi/PosaCaviCollegamenti';\nimport caviService from '../../services/caviService';\nimport AggiungiCavoForm from '../../components/cavi/AggiungiCavoForm';\nimport { normalizeInstallationStatus } from '../../utils/validationUtils';\nimport CaviFilterableTable from '../../components/cavi/CaviFilterableTable';\nimport './CaviPage.css';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst VisualizzaCaviPage = () => {\n  _s();\n  var _caviAttivi$, _caviAttivi$2, _caviAttivi$3;\n  const {\n    isImpersonating,\n    user\n  } = useAuth();\n  const {\n    openEliminaCavoDialog,\n    setOpenEliminaCavoDialog,\n    openModificaCavoDialog,\n    setOpenModificaCavoDialog,\n    openAggiungiCavoDialog,\n    setOpenAggiungiCavoDialog\n  } = useGlobalContext();\n  const navigate = useNavigate();\n  const [cantiereId, setCantiereId] = useState(null);\n  const [cantiereName, setCantiereName] = useState('');\n  const [caviAttivi, setCaviAttivi] = useState([]);\n  const [caviSpare, setCaviSpare] = useState([]);\n  const [loading, setLoading] = useState(true);\n  const [error, setError] = useState(null);\n  // Rimosso stato viewMode\n\n  // Stato per il dialogo dei dettagli del cavo\n  const [selectedCavo, setSelectedCavo] = useState(null);\n  const [detailsDialogOpen, setDetailsDialogOpen] = useState(false);\n\n  // Stato per le statistiche\n  const [stats, setStats] = useState({\n    totali: {\n      cavi_attivi: 0,\n      cavi_spare: 0,\n      cavi_totali: 0\n    },\n    metrature: {\n      metri_teorici_totali: 0,\n      metri_reali_totali: 0,\n      percentuale_completamento: 0\n    },\n    stati: []\n  });\n  const [loadingStats, setLoadingStats] = useState(false);\n\n  // Rimosso stato per il debug\n\n  // Funzione per caricare gli stati di installazione disponibili\n  const loadStatiInstallazione = () => {\n    // Usa i valori dell'enum StatoInstallazione\n    setStatiInstallazione(['Installato', 'Da installare', 'In corso']);\n  };\n\n  // Stato per filtri e ordinamento\n  const [filters, setFilters] = useState({\n    stato_installazione: '',\n    tipologia: '',\n    sort_by: '',\n    sort_order: 'asc'\n  });\n\n  // Opzioni per i filtri\n  const [statiInstallazione, setStatiInstallazione] = useState([]);\n  const [tipologieCavi, setTipologieCavi] = useState([]);\n\n  // Rimossa funzione di debug\n\n  // Funzione per caricare i cavi\n  const fetchCavi = async () => {\n    try {\n      setLoading(true);\n      console.log('Caricamento cavi per cantiere:', cantiereId);\n\n      // Verifica che cantiereId sia valido\n      if (!cantiereId) {\n        console.error('fetchCavi: cantiereId non valido:', cantiereId);\n        setError('ID cantiere non valido o mancante. Ricarica la pagina.');\n        setLoading(false);\n        return;\n      }\n\n      // Recupera il cantiereId dal localStorage come fallback\n      let cantiereIdToUse = cantiereId;\n      if (!cantiereIdToUse) {\n        cantiereIdToUse = localStorage.getItem('selectedCantiereId');\n        console.log('Usando cantiereId dal localStorage:', cantiereIdToUse);\n        if (!cantiereIdToUse) {\n          console.error('Impossibile trovare un ID cantiere valido');\n          setError('ID cantiere non trovato. Ricarica la pagina.');\n          setLoading(false);\n          return;\n        }\n      }\n\n      // Carica i cavi attivi\n      console.log('Caricamento cavi attivi (tipo_cavo=0)...');\n      let attivi = [];\n      try {\n        attivi = await caviService.getCavi(cantiereIdToUse, 0, filters);\n        console.log('Cavi attivi caricati:', attivi ? attivi.length : 0);\n      } catch (attiviError) {\n        console.error('Errore nel caricamento dei cavi attivi:', attiviError);\n        // Continua con un array vuoto\n        attivi = [];\n      }\n\n      // Verifica se ci sono cavi con modificato_manualmente = 3 tra i cavi attivi\n      if (attivi && attivi.length > 0) {\n        const caviSpareTraAttivi = attivi.filter(cavo => cavo.modificato_manualmente === 3);\n        if (caviSpareTraAttivi.length > 0) {\n          console.error('ERRORE: Trovati cavi con modificato_manualmente = 3 tra i cavi attivi:', caviSpareTraAttivi);\n        }\n      }\n      setCaviAttivi(attivi || []);\n\n      // Carica i cavi SPARE con la nuova funzione dedicata\n      let spare = [];\n      try {\n        console.log('Caricamento cavi SPARE con funzione dedicata...');\n        spare = await caviService.getCaviSpare(cantiereIdToUse);\n        console.log('Cavi SPARE caricati con funzione dedicata:', spare ? spare.length : 0);\n        if (spare && spare.length > 0) {\n          console.log('Primo cavo SPARE:', spare[0]);\n        }\n      } catch (spareError) {\n        console.error('Errore nel caricamento dei cavi SPARE con funzione dedicata:', spareError);\n        // Se fallisce, prova con il metodo standard\n        try {\n          console.log('Tentativo con metodo standard...');\n          spare = await caviService.getCavi(cantiereIdToUse, 3);\n          console.log('Cavi SPARE caricati con metodo standard:', spare ? spare.length : 0);\n        } catch (standardError) {\n          console.error('Errore anche con metodo standard:', standardError);\n          // Continua con un array vuoto\n          spare = [];\n        }\n      }\n      setCaviSpare(spare || []);\n\n      // Carica le statistiche\n      try {\n        console.log('Caricamento statistiche...');\n        const statsData = await caviService.getCaviStats(cantiereIdToUse);\n        console.log('Statistiche caricate:', statsData);\n\n        // Verifica che statsData abbia la struttura attesa\n        if (!statsData || typeof statsData !== 'object') {\n          console.error('Statistiche non valide:', statsData);\n          // Imposta un oggetto stats con struttura valida ma vuota\n          setStats({\n            totali: {\n              cavi_attivi: 0,\n              cavi_spare: 0,\n              cavi_totali: 0\n            },\n            metrature: {\n              metri_teorici_totali: 0,\n              metri_reali_totali: 0,\n              percentuale_completamento: 0\n            },\n            stati: []\n          });\n        } else {\n          // Assicurati che tutte le proprietà necessarie siano presenti\n          const validStats = {\n            totali: statsData.totali || {\n              cavi_attivi: 0,\n              cavi_spare: 0,\n              cavi_totali: 0\n            },\n            metrature: statsData.metrature || {\n              metri_teorici_totali: 0,\n              metri_reali_totali: 0,\n              percentuale_completamento: 0\n            },\n            stati: statsData.stati || []\n          };\n          setStats(validStats);\n        }\n      } catch (statsError) {\n        console.error('Errore nel caricamento delle statistiche:', statsError);\n        // Continua con statistiche vuote ma con struttura valida\n        setStats({\n          totali: {\n            cavi_attivi: 0,\n            cavi_spare: 0,\n            cavi_totali: 0\n          },\n          metrature: {\n            metri_teorici_totali: 0,\n            metri_reali_totali: 0,\n            percentuale_completamento: 0\n          },\n          stati: []\n        });\n      }\n\n      // Se siamo arrivati qui, rimuovi eventuali messaggi di errore precedenti\n      setError('');\n    } catch (error) {\n      console.error('Errore generale nel caricamento dei cavi:', error);\n      setError(`Errore nel caricamento dei cavi: ${error.message || 'Errore sconosciuto'}`);\n\n      // Prova a ricaricare la pagina dopo un ritardo se l'errore persiste\n      setTimeout(() => {\n        // Verifica se siamo ancora in errore\n        if (document.body.textContent.includes('Errore nel caricamento dei cavi')) {\n          console.log('Errore persistente, tentativo di ricaricamento della pagina...');\n          window.location.reload();\n        }\n      }, 5000); // 5 secondi di ritardo\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  // Carica i dati del cantiere e dei cavi\n  useEffect(() => {\n    // Carica gli stati di installazione all'avvio\n    loadStatiInstallazione();\n    const fetchData = async () => {\n      try {\n        console.log('Inizializzazione VisualizzaCaviPage...');\n\n        // Verifica che l'utente sia autenticato\n        const token = localStorage.getItem('token');\n        console.log('Token presente:', !!token);\n        if (!token) {\n          setError('Sessione scaduta. Effettua nuovamente il login.');\n          setLoading(false);\n          return;\n        }\n\n        // Recupera l'ID del cantiere selezionato dal localStorage\n        let selectedCantiereId = localStorage.getItem('selectedCantiereId');\n        let selectedCantiereName = localStorage.getItem('selectedCantiereName');\n        console.log('Cantiere selezionato dal localStorage:', {\n          selectedCantiereId,\n          selectedCantiereName\n        });\n        console.log('Dati utente:', user);\n\n        // Stampa tutti i dati nel localStorage per debug\n        console.log('DEBUG - Tutti i dati nel localStorage:');\n        for (let i = 0; i < localStorage.length; i++) {\n          const key = localStorage.key(i);\n          console.log(`${key}: ${localStorage.getItem(key)}`);\n        }\n\n        // SOLUZIONE DIRETTA: Ottieni l'ID del cantiere direttamente dal token JWT\n        if ((user === null || user === void 0 ? void 0 : user.role) === 'cantieri_user') {\n          console.log('Utente cantiere rilevato, tentativo di recupero ID cantiere dai dati utente');\n\n          // Verifica se l'utente ha un ID cantiere nei dati utente\n          if (user.cantiere_id) {\n            console.log('Trovato ID cantiere nei dati utente:', user.cantiere_id);\n            selectedCantiereId = user.cantiere_id.toString();\n            selectedCantiereName = user.cantiere_name || `Cantiere ${user.cantiere_id}`;\n\n            // Salva l'ID e il nome del cantiere nel localStorage\n            localStorage.setItem('selectedCantiereId', selectedCantiereId);\n            localStorage.setItem('selectedCantiereName', selectedCantiereName);\n            console.log('Salvato ID cantiere nel localStorage:', selectedCantiereId);\n          } else {\n            // Tentativo di recupero dal token JWT\n            try {\n              console.log('Tentativo di decodifica del token JWT per recuperare l\\'ID cantiere');\n              const token = localStorage.getItem('token');\n              if (token) {\n                // Decodifica il token JWT (senza verifica della firma)\n                const base64Url = token.split('.')[1];\n                const base64 = base64Url.replace(/-/g, '+').replace(/_/g, '/');\n                const jsonPayload = decodeURIComponent(atob(base64).split('').map(c => {\n                  return '%' + ('00' + c.charCodeAt(0).toString(16)).slice(-2);\n                }).join(''));\n                const payload = JSON.parse(jsonPayload);\n                console.log('Payload del token JWT:', payload);\n                if (payload.cantiere_id) {\n                  console.log('Trovato ID cantiere nel token JWT:', payload.cantiere_id);\n                  selectedCantiereId = payload.cantiere_id.toString();\n                  // Usa un nome generico se non disponibile\n                  selectedCantiereName = `Cantiere ${payload.cantiere_id}`;\n\n                  // Salva l'ID e il nome del cantiere nel localStorage\n                  localStorage.setItem('selectedCantiereId', selectedCantiereId);\n                  localStorage.setItem('selectedCantiereName', selectedCantiereName);\n                  console.log('Salvato ID cantiere nel localStorage:', selectedCantiereId);\n                }\n              }\n            } catch (e) {\n              console.error('Errore durante la decodifica del token JWT:', e);\n            }\n          }\n        }\n\n        // SOLUZIONE TEMPORANEA: Se ancora non abbiamo un ID cantiere, usa un valore hardcoded per debug\n        if (!selectedCantiereId || selectedCantiereId === 'undefined' || selectedCantiereId === 'null') {\n          console.warn('ATTENZIONE: Nessun ID cantiere trovato, utilizzo valore hardcoded per debug');\n          // Usa il primo cantiere disponibile (questo è solo per debug)\n          selectedCantiereId = '1'; // Sostituisci con un ID cantiere valido nel tuo database\n          selectedCantiereName = 'Cantiere Debug';\n\n          // Salva l'ID e il nome del cantiere nel localStorage\n          localStorage.setItem('selectedCantiereId', selectedCantiereId);\n          localStorage.setItem('selectedCantiereName', selectedCantiereName);\n          console.log('Salvato ID cantiere hardcoded nel localStorage:', selectedCantiereId);\n        }\n\n        // Verifica finale\n        if (!selectedCantiereId) {\n          setError('Nessun cantiere selezionato. Torna alla pagina dei cantieri.');\n          setLoading(false);\n          return;\n        }\n\n        // Verifica che l'ID del cantiere sia un numero valido\n        const cantiereIdNum = parseInt(selectedCantiereId, 10);\n        console.log('ID cantiere convertito a numero:', cantiereIdNum);\n        if (isNaN(cantiereIdNum)) {\n          setError(`ID cantiere non valido: ${selectedCantiereId}. Torna alla pagina dei cantieri.`);\n          setLoading(false);\n          return;\n        }\n\n        // Usa il numero convertito, non la stringa\n        setCantiereId(cantiereIdNum);\n        setCantiereName(selectedCantiereName || `Cantiere ${cantiereIdNum}`);\n\n        // Carica le statistiche dei cavi\n        try {\n          setLoadingStats(true);\n          console.log('Caricamento statistiche cavi per cantiere:', cantiereIdNum);\n          const statsData = await caviService.getCaviStats(cantiereIdNum);\n          console.log('Statistiche cavi caricate:', statsData);\n          setStats(statsData);\n\n          // Estrai gli stati di installazione e le tipologie per i filtri\n          if (statsData && statsData.stati) {\n            const stati = statsData.stati.map(item => item.stato).filter(stato => stato !== 'Non specificato');\n            setStatiInstallazione(stati);\n          }\n          if (statsData && statsData.tipologie) {\n            const tipologie = statsData.tipologie.map(item => item.tipologia).filter(tipo => tipo !== 'Non specificata');\n            setTipologieCavi(tipologie);\n          }\n          setLoadingStats(false);\n        } catch (statsError) {\n          console.error('Errore nel caricamento delle statistiche:', statsError);\n          setLoadingStats(false);\n          // Non interrompere il flusso se le statistiche falliscono\n        }\n\n        // Carica i cavi attivi con gestione degli errori migliorata\n        console.log('Caricamento cavi attivi per cantiere:', cantiereIdNum);\n        try {\n          // Imposta un timeout per evitare che la richiesta rimanga bloccata\n          const timeoutPromise = new Promise((_, reject) => {\n            setTimeout(() => reject(new Error('Timeout durante il caricamento dei cavi attivi')), 30000); // Aumentato a 30 secondi\n          });\n\n          // Esegui la richiesta con un timeout di sicurezza e applica i filtri\n          console.log('Iniziando chiamata API per cavi attivi con filtri:', filters);\n          const caviPromise = caviService.getCavi(cantiereIdNum, 0, filters);\n          const attivi = await Promise.race([caviPromise, timeoutPromise]);\n          console.log('Cavi attivi caricati:', attivi);\n          console.log('Numero di cavi attivi trovati:', attivi ? attivi.length : 0);\n          if (attivi && attivi.length > 0) {\n            console.log('Primo cavo attivo:', attivi[0]);\n          } else {\n            console.warn('Nessun cavo attivo trovato per il cantiere', cantiereIdNum);\n          }\n          setCaviAttivi(attivi || []);\n        } catch (caviError) {\n          console.error('Errore nel caricamento dei cavi attivi:', caviError);\n          console.error('Dettagli errore cavi attivi:', {\n            message: caviError.message,\n            status: caviError.status,\n            data: caviError.data,\n            stack: caviError.stack,\n            code: caviError.code,\n            name: caviError.name,\n            response: caviError.response ? {\n              status: caviError.response.status,\n              statusText: caviError.response.statusText,\n              data: caviError.response.data\n            } : 'No response'\n          });\n\n          // Non interrompere il flusso, continua con i cavi spare\n          setCaviAttivi([]);\n          console.warn('Continuazione del flusso dopo errore nei cavi attivi');\n\n          // Aggiungi un messaggio di errore visibile all'utente\n          setError(`Errore nel caricamento dei cavi attivi: ${caviError.message}. Controlla la console per maggiori dettagli.`);\n        }\n\n        // Carica i cavi spare con gestione degli errori migliorata\n        console.log('Caricamento cavi spare per cantiere:', cantiereIdNum);\n        try {\n          // Imposta un timeout per evitare che la richiesta rimanga bloccata\n          const timeoutPromise = new Promise((_, reject) => {\n            setTimeout(() => reject(new Error('Timeout durante il caricamento dei cavi spare')), 30000); // Aumentato a 30 secondi\n          });\n\n          // Esegui la richiesta con un timeout di sicurezza\n          console.log('Iniziando chiamata API per cavi spare...');\n          // Non applichiamo i filtri ai cavi spare, solo agli attivi\n          const sparePromise = caviService.getCavi(cantiereIdNum, 3);\n          const spare = await Promise.race([sparePromise, timeoutPromise]);\n          console.log('Cavi spare caricati:', spare);\n          console.log('Numero di cavi spare trovati:', spare ? spare.length : 0);\n          if (spare && spare.length > 0) {\n            console.log('Primo cavo spare:', spare[0]);\n          } else {\n            console.warn('Nessun cavo spare trovato per il cantiere', cantiereIdNum);\n          }\n          setCaviSpare(spare || []);\n        } catch (spareError) {\n          console.error('Errore nel caricamento dei cavi spare:', spareError);\n          console.error('Dettagli errore cavi spare:', {\n            message: spareError.message,\n            status: spareError.status,\n            data: spareError.data,\n            stack: spareError.stack,\n            code: spareError.code,\n            name: spareError.name,\n            response: spareError.response ? {\n              status: spareError.response.status,\n              statusText: spareError.response.statusText,\n              data: spareError.response.data\n            } : 'No response'\n          });\n\n          // Non interrompere il flusso, imposta un array vuoto\n          setCaviSpare([]);\n\n          // Aggiungi un messaggio di errore visibile all'utente se non c'è già un errore per i cavi attivi\n          if (!error) {\n            setError(`Errore nel caricamento dei cavi spare: ${spareError.message}. Controlla la console per maggiori dettagli.`);\n          }\n        }\n\n        // Se siamo arrivati qui, almeno abbiamo caricato l'interfaccia di base\n        setLoading(false);\n      } catch (err) {\n        var _err$response, _err$response2, _err$response3, _err$response4, _err$response5, _err$response5$data;\n        console.error('Errore nel caricamento dei cavi:', err);\n        console.error('Dettagli errore generale:', {\n          message: err.message,\n          status: err.status || ((_err$response = err.response) === null || _err$response === void 0 ? void 0 : _err$response.status),\n          data: err.data || ((_err$response2 = err.response) === null || _err$response2 === void 0 ? void 0 : _err$response2.data),\n          stack: err.stack\n        });\n\n        // Estrai il messaggio di errore dettagliato\n        let errorMessage = 'Errore sconosciuto';\n        if (err.message && err.message.includes('ID cantiere non valido')) {\n          errorMessage = err.message;\n        } else if (err.status === 401 || err.status === 403 || ((_err$response3 = err.response) === null || _err$response3 === void 0 ? void 0 : _err$response3.status) === 401 || ((_err$response4 = err.response) === null || _err$response4 === void 0 ? void 0 : _err$response4.status) === 403) {\n          errorMessage = 'Sessione scaduta o non autorizzata. Effettua nuovamente il login.';\n        } else if ((_err$response5 = err.response) !== null && _err$response5 !== void 0 && (_err$response5$data = _err$response5.data) !== null && _err$response5$data !== void 0 && _err$response5$data.detail) {\n          // Estrai il messaggio di errore dettagliato dall'API\n          errorMessage = `Errore API: ${err.response.data.detail}`;\n        } else if (err.code === 'ERR_NETWORK') {\n          // Errore di rete\n          errorMessage = 'Network Error. Verifica che il backend sia in esecuzione e accessibile.';\n        } else if (err.message) {\n          errorMessage = err.message;\n        }\n        setError(`Impossibile caricare i cavi: ${errorMessage}. Riprova più tardi.`);\n\n        // Imposta array vuoti per evitare errori di rendering\n        setCaviAttivi([]);\n        setCaviSpare([]);\n      } finally {\n        setLoading(false);\n      }\n    };\n    fetchData();\n  }, [filters]); // Ricarica i dati quando cambiano i filtri\n\n  // I filtri sono ora gestiti dal componente CaviFilterableTable\n\n  // Funzione per aprire il dialogo dei dettagli del cavo\n  const handleOpenDetails = cavo => {\n    setSelectedCavo(cavo);\n    setDetailsDialogOpen(true);\n  };\n\n  // Funzione per chiudere il dialogo dei dettagli del cavo\n  const handleCloseDetails = () => {\n    setDetailsDialogOpen(false);\n    setSelectedCavo(null);\n  };\n\n  // Nessuna funzione di navigazione necessaria, tutto è gestito dal menu principale\n\n  // La visualizzazione dei cavi è ora gestita dal componente CaviFilterableTable\n\n  // Rimossa funzione handleViewModeChange\n\n  // Renderizza il dialogo dei dettagli del cavo\n  const renderDetailsDialog = () => {\n    if (!selectedCavo) return null;\n    return /*#__PURE__*/_jsxDEV(Dialog, {\n      open: detailsDialogOpen,\n      onClose: handleCloseDetails,\n      maxWidth: \"md\",\n      fullWidth: true,\n      children: [/*#__PURE__*/_jsxDEV(DialogTitle, {\n        children: [\"Dettagli Cavo: \", selectedCavo.id_cavo]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 504,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(DialogContent, {\n        dividers: true,\n        children: /*#__PURE__*/_jsxDEV(Grid, {\n          container: true,\n          spacing: 2,\n          children: [/*#__PURE__*/_jsxDEV(Grid, {\n            item: true,\n            xs: 12,\n            md: 6,\n            children: [/*#__PURE__*/_jsxDEV(Typography, {\n              variant: \"subtitle1\",\n              gutterBottom: true,\n              children: \"Informazioni Generali\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 510,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Box, {\n              sx: {\n                mb: 2\n              },\n              children: [/*#__PURE__*/_jsxDEV(Typography, {\n                variant: \"body2\",\n                children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n                  children: \"Sistema:\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 512,\n                  columnNumber: 45\n                }, this), \" \", selectedCavo.sistema || 'N/A']\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 512,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                variant: \"body2\",\n                children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n                  children: \"Utility:\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 513,\n                  columnNumber: 45\n                }, this), \" \", selectedCavo.utility || 'N/A']\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 513,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                variant: \"body2\",\n                children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n                  children: \"Tipologia:\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 514,\n                  columnNumber: 45\n                }, this), \" \", selectedCavo.tipologia || 'N/A']\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 514,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                variant: \"body2\",\n                children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n                  children: \"Colore:\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 515,\n                  columnNumber: 45\n                }, this), \" \", selectedCavo.colore_cavo || 'N/A']\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 515,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                variant: \"body2\",\n                children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n                  children: \"N. Conduttori:\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 516,\n                  columnNumber: 45\n                }, this), \" \", selectedCavo.n_conduttori || 'N/A']\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 516,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                variant: \"body2\",\n                children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n                  children: \"Sezione:\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 517,\n                  columnNumber: 45\n                }, this), \" \", selectedCavo.sezione || 'N/A']\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 517,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                variant: \"body2\",\n                children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n                  children: \"SH:\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 518,\n                  columnNumber: 45\n                }, this), \" \", selectedCavo.sh || 'N/A']\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 518,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 511,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Typography, {\n              variant: \"subtitle1\",\n              gutterBottom: true,\n              children: \"Partenza\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 521,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Box, {\n              sx: {\n                mb: 2\n              },\n              children: [/*#__PURE__*/_jsxDEV(Typography, {\n                variant: \"body2\",\n                children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n                  children: \"Ubicazione:\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 523,\n                  columnNumber: 45\n                }, this), \" \", selectedCavo.ubicazione_partenza || 'N/A']\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 523,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                variant: \"body2\",\n                children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n                  children: \"Utenza:\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 524,\n                  columnNumber: 45\n                }, this), \" \", selectedCavo.utenza_partenza || 'N/A']\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 524,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                variant: \"body2\",\n                children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n                  children: \"Descrizione:\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 525,\n                  columnNumber: 45\n                }, this), \" \", selectedCavo.descrizione_utenza_partenza || 'N/A']\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 525,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                variant: \"body2\",\n                children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n                  children: \"Responsabile:\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 526,\n                  columnNumber: 45\n                }, this), \" \", selectedCavo.responsabile_partenza || 'N/A']\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 526,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                variant: \"body2\",\n                children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n                  children: \"Comanda:\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 527,\n                  columnNumber: 45\n                }, this), \" \", selectedCavo.comanda_partenza || 'N/A']\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 527,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 522,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 509,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Grid, {\n            item: true,\n            xs: 12,\n            md: 6,\n            children: [/*#__PURE__*/_jsxDEV(Typography, {\n              variant: \"subtitle1\",\n              gutterBottom: true,\n              children: \"Arrivo\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 532,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Box, {\n              sx: {\n                mb: 2\n              },\n              children: [/*#__PURE__*/_jsxDEV(Typography, {\n                variant: \"body2\",\n                children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n                  children: \"Ubicazione:\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 534,\n                  columnNumber: 45\n                }, this), \" \", selectedCavo.ubicazione_arrivo || 'N/A']\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 534,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                variant: \"body2\",\n                children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n                  children: \"Utenza:\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 535,\n                  columnNumber: 45\n                }, this), \" \", selectedCavo.utenza_arrivo || 'N/A']\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 535,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                variant: \"body2\",\n                children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n                  children: \"Descrizione:\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 536,\n                  columnNumber: 45\n                }, this), \" \", selectedCavo.descrizione_utenza_arrivo || 'N/A']\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 536,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                variant: \"body2\",\n                children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n                  children: \"Responsabile:\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 537,\n                  columnNumber: 45\n                }, this), \" \", selectedCavo.responsabile_arrivo || 'N/A']\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 537,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                variant: \"body2\",\n                children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n                  children: \"Comanda:\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 538,\n                  columnNumber: 45\n                }, this), \" \", selectedCavo.comanda_arrivo || 'N/A']\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 538,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 533,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Typography, {\n              variant: \"subtitle1\",\n              gutterBottom: true,\n              children: \"Installazione\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 541,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Box, {\n              sx: {\n                mb: 2\n              },\n              children: [/*#__PURE__*/_jsxDEV(Typography, {\n                variant: \"body2\",\n                children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n                  children: \"Metri Teorici:\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 543,\n                  columnNumber: 45\n                }, this), \" \", selectedCavo.metri_teorici || 'N/A']\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 543,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                variant: \"body2\",\n                children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n                  children: \"Metratura Reale:\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 544,\n                  columnNumber: 45\n                }, this), \" \", selectedCavo.metratura_reale || '0']\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 544,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                variant: \"body2\",\n                children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n                  children: \"Stato:\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 545,\n                  columnNumber: 45\n                }, this), \" \", normalizeInstallationStatus(selectedCavo.stato_installazione)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 545,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                variant: \"body2\",\n                children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n                  children: \"Collegamenti:\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 546,\n                  columnNumber: 45\n                }, this), \" \", selectedCavo.collegamenti || '0']\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 546,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                variant: \"body2\",\n                children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n                  children: \"Bobina:\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 547,\n                  columnNumber: 45\n                }, this), \" \", selectedCavo.id_bobina || 'N/A']\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 547,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                variant: \"body2\",\n                children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n                  children: \"Responsabile Posa:\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 548,\n                  columnNumber: 45\n                }, this), \" \", selectedCavo.responsabile_posa || 'N/A']\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 548,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                variant: \"body2\",\n                children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n                  children: \"Comanda Posa:\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 549,\n                  columnNumber: 45\n                }, this), \" \", selectedCavo.comanda_posa || 'N/A']\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 549,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                variant: \"body2\",\n                children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n                  children: \"Ultimo Aggiornamento:\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 550,\n                  columnNumber: 45\n                }, this), \" \", new Date(selectedCavo.timestamp).toLocaleString()]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 550,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 542,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 531,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 508,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 507,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(DialogActions, {\n        children: /*#__PURE__*/_jsxDEV(Button, {\n          onClick: handleCloseDetails,\n          children: \"Chiudi\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 556,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 555,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 503,\n      columnNumber: 7\n    }, this);\n  };\n\n  // Il pannello dei filtri è ora gestito dal componente CaviFilterableTable\n\n  // Renderizza il pannello delle statistiche\n  const renderStatsPanel = () => {\n    // Verifica che stats sia definito e abbia la struttura attesa\n    if (!stats || !stats.totali || !stats.metrature || !stats.stati) {\n      return /*#__PURE__*/_jsxDEV(Paper, {\n        sx: {\n          mb: 3,\n          p: 2\n        },\n        children: [/*#__PURE__*/_jsxDEV(Typography, {\n          variant: \"h6\",\n          gutterBottom: true,\n          children: \"Statistiche\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 570,\n          columnNumber: 11\n        }, this), loadingStats ? /*#__PURE__*/_jsxDEV(LinearProgress, {}, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 572,\n          columnNumber: 13\n        }, this) : /*#__PURE__*/_jsxDEV(Typography, {\n          variant: \"body2\",\n          children: \"Nessuna statistica disponibile\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 574,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 569,\n        columnNumber: 9\n      }, this);\n    }\n\n    // Valori predefiniti per evitare errori\n    const totali = stats.totali || {\n      cavi_attivi: 0,\n      cavi_spare: 0,\n      cavi_totali: 0\n    };\n    const metrature = stats.metrature || {\n      metri_teorici_totali: 0,\n      metri_reali_totali: 0,\n      percentuale_completamento: 0\n    };\n    const stati = stats.stati || [];\n    return /*#__PURE__*/_jsxDEV(Paper, {\n      sx: {\n        mb: 3,\n        p: 2\n      },\n      children: [/*#__PURE__*/_jsxDEV(Typography, {\n        variant: \"h6\",\n        gutterBottom: true,\n        children: \"Statistiche\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 587,\n        columnNumber: 9\n      }, this), loadingStats ? /*#__PURE__*/_jsxDEV(LinearProgress, {}, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 589,\n        columnNumber: 11\n      }, this) : /*#__PURE__*/_jsxDEV(Grid, {\n        container: true,\n        spacing: 2,\n        children: [/*#__PURE__*/_jsxDEV(Grid, {\n          item: true,\n          xs: 12,\n          md: 4,\n          children: [/*#__PURE__*/_jsxDEV(Typography, {\n            variant: \"subtitle1\",\n            gutterBottom: true,\n            children: \"Totali\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 593,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(Typography, {\n            variant: \"body2\",\n            children: [\"Cavi Attivi: \", totali.cavi_attivi || 0]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 594,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(Typography, {\n            variant: \"body2\",\n            children: [\"Cavi Spare: \", totali.cavi_spare || 0]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 595,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(Typography, {\n            variant: \"body2\",\n            children: [\"Totale Cavi: \", totali.cavi_totali || 0]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 596,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 592,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(Grid, {\n          item: true,\n          xs: 12,\n          md: 4,\n          children: [/*#__PURE__*/_jsxDEV(Typography, {\n            variant: \"subtitle1\",\n            gutterBottom: true,\n            children: \"Metrature\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 601,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(Typography, {\n            variant: \"body2\",\n            children: [\"Metri Teorici: \", (metrature.metri_teorici_totali || 0).toFixed(2)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 602,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(Typography, {\n            variant: \"body2\",\n            children: [\"Metri Posati: \", (metrature.metri_reali_totali || 0).toFixed(2)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 603,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(Box, {\n            sx: {\n              display: 'flex',\n              alignItems: 'center',\n              mt: 1\n            },\n            children: [/*#__PURE__*/_jsxDEV(Box, {\n              sx: {\n                width: '100%',\n                mr: 1\n              },\n              children: /*#__PURE__*/_jsxDEV(LinearProgress, {\n                variant: \"determinate\",\n                value: metrature.percentuale_completamento || 0,\n                sx: {\n                  height: 10,\n                  borderRadius: 5\n                }\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 606,\n                columnNumber: 19\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 605,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(Box, {\n              sx: {\n                minWidth: 35\n              },\n              children: /*#__PURE__*/_jsxDEV(Typography, {\n                variant: \"body2\",\n                color: \"text.secondary\",\n                children: `${(metrature.percentuale_completamento || 0).toFixed(1)}%`\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 613,\n                columnNumber: 19\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 612,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 604,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 600,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(Grid, {\n          item: true,\n          xs: 12,\n          md: 4,\n          children: [/*#__PURE__*/_jsxDEV(Typography, {\n            variant: \"subtitle1\",\n            gutterBottom: true,\n            children: \"Stati\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 619,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(Box, {\n            sx: {\n              display: 'flex',\n              flexWrap: 'wrap',\n              gap: 1\n            },\n            children: stati.length > 0 ? stati.map((stato, index) => /*#__PURE__*/_jsxDEV(Chip, {\n              label: `${stato.stato || 'N/A'}: ${stato.count || 0}`,\n              size: \"small\",\n              onClick: () => {\n                setFilters(prev => ({\n                  ...prev,\n                  stato_installazione: stato.stato === 'Non specificato' ? '' : stato.stato\n                }));\n              }\n            }, index, false, {\n              fileName: _jsxFileName,\n              lineNumber: 622,\n              columnNumber: 19\n            }, this)) : /*#__PURE__*/_jsxDEV(Typography, {\n              variant: \"body2\",\n              children: \"Nessuno stato disponibile\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 634,\n              columnNumber: 19\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 620,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 618,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 591,\n        columnNumber: 11\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 586,\n      columnNumber: 7\n    }, this);\n  };\n  return /*#__PURE__*/_jsxDEV(Box, {\n    className: \"cavi-page\",\n    children: loading ? /*#__PURE__*/_jsxDEV(Box, {\n      sx: {\n        display: 'flex',\n        flexDirection: 'column',\n        alignItems: 'center',\n        mt: 4\n      },\n      children: [/*#__PURE__*/_jsxDEV(CircularProgress, {\n        size: 40\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 648,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(Typography, {\n        sx: {\n          mt: 2\n        },\n        children: \"Caricamento cavi...\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 649,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(Button, {\n        variant: \"outlined\",\n        color: \"primary\",\n        onClick: () => window.location.reload(),\n        sx: {\n          mt: 2\n        },\n        children: \"Ricarica la pagina\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 650,\n        columnNumber: 11\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 647,\n      columnNumber: 9\n    }, this) : error ? /*#__PURE__*/_jsxDEV(Box, {\n      children: [/*#__PURE__*/_jsxDEV(Alert, {\n        severity: \"error\",\n        sx: {\n          mb: 2\n        },\n        children: [error, error.includes('Network Error') && /*#__PURE__*/_jsxDEV(Typography, {\n          variant: \"body2\",\n          sx: {\n            mt: 1\n          },\n          children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n            children: \"Suggerimento:\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 665,\n            columnNumber: 17\n          }, this), \" Verifica che il server backend sia in esecuzione sulla porta 8001.\", /*#__PURE__*/_jsxDEV(\"br\", {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 666,\n            columnNumber: 17\n          }, this), \"Puoi avviare il backend eseguendo il file \", /*#__PURE__*/_jsxDEV(\"code\", {\n            children: \"run_system.py\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 667,\n            columnNumber: 59\n          }, this), \" nella cartella principale del progetto.\"]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 664,\n          columnNumber: 15\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 661,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(Box, {\n        sx: {\n          display: 'flex',\n          gap: 2\n        },\n        children: /*#__PURE__*/_jsxDEV(Button, {\n          variant: \"contained\",\n          className: \"primary-button\",\n          onClick: () => window.location.reload(),\n          children: \"Ricarica la pagina\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 672,\n          columnNumber: 13\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 671,\n        columnNumber: 11\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 660,\n      columnNumber: 9\n    }, this) : /*#__PURE__*/_jsxDEV(Box, {\n      children: [/*#__PURE__*/_jsxDEV(Box, {\n        sx: {\n          mt: 4\n        },\n        children: /*#__PURE__*/_jsxDEV(Box, {\n          sx: {\n            display: 'flex',\n            justifyContent: 'space-between',\n            alignItems: 'center',\n            mb: 2\n          },\n          children: [/*#__PURE__*/_jsxDEV(Typography, {\n            variant: \"h5\",\n            children: [\"Cavi Attivi \", caviAttivi.length > 0 ? `(${caviAttivi.length})` : '']\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 690,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(Button, {\n            variant: \"outlined\",\n            size: \"small\",\n            onClick: () => window.location.reload(),\n            startIcon: loading ? /*#__PURE__*/_jsxDEV(CircularProgress, {\n              size: 20\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 697,\n              columnNumber: 38\n            }, this) : /*#__PURE__*/_jsxDEV(RefreshIcon, {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 697,\n              columnNumber: 71\n            }, this),\n            disabled: loading,\n            children: \"Aggiorna\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 693,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 689,\n          columnNumber: 13\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 688,\n        columnNumber: 11\n      }, this), caviAttivi.length > 0 ? /*#__PURE__*/_jsxDEV(Box, {\n        sx: {\n          mb: 2\n        },\n        children: [process.env.NODE_ENV === 'development' && /*#__PURE__*/_jsxDEV(Box, {\n          sx: {\n            mb: 2,\n            p: 1,\n            bgcolor: '#f0f0f0',\n            borderRadius: 1,\n            fontSize: '0.8rem',\n            fontFamily: 'monospace',\n            display: 'none'\n          },\n          children: Object.keys(caviAttivi[0]).map(key => /*#__PURE__*/_jsxDEV(\"div\", {\n            children: [key, \": \", JSON.stringify(caviAttivi[0][key])]\n          }, key, true, {\n            fileName: _jsxFileName,\n            lineNumber: 711,\n            columnNumber: 21\n          }, this))\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 709,\n          columnNumber: 17\n        }, this), /*#__PURE__*/_jsxDEV(CaviFilterableTable, {\n          cavi: caviAttivi,\n          loading: loading,\n          onFilteredDataChange: filteredData => console.log('Cavi attivi filtrati:', filteredData.length),\n          revisioneCorrente: ((_caviAttivi$ = caviAttivi[0]) === null || _caviAttivi$ === void 0 ? void 0 : _caviAttivi$.revisione_ufficiale) || ((_caviAttivi$2 = caviAttivi[0]) === null || _caviAttivi$2 === void 0 ? void 0 : _caviAttivi$2.revisione) || ((_caviAttivi$3 = caviAttivi[0]) === null || _caviAttivi$3 === void 0 ? void 0 : _caviAttivi$3.rev)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 715,\n          columnNumber: 15\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 706,\n        columnNumber: 13\n      }, this) : /*#__PURE__*/_jsxDEV(Alert, {\n        severity: \"info\",\n        sx: {\n          mb: 2\n        },\n        children: \"Nessun cavo attivo trovato. I cavi attivi appariranno qui.\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 723,\n        columnNumber: 13\n      }, this), /*#__PURE__*/_jsxDEV(Box, {\n        sx: {\n          mt: 4\n        },\n        children: [/*#__PURE__*/_jsxDEV(Box, {\n          sx: {\n            display: 'flex',\n            justifyContent: 'space-between',\n            alignItems: 'center',\n            mb: 2\n          },\n          children: [/*#__PURE__*/_jsxDEV(Typography, {\n            variant: \"h5\",\n            children: [\"Cavi Spare \", caviSpare.length > 0 ? `(${caviSpare.length})` : '']\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 731,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(Button, {\n            variant: \"outlined\",\n            size: \"small\",\n            onClick: () => window.location.reload(),\n            startIcon: loading ? /*#__PURE__*/_jsxDEV(CircularProgress, {\n              size: 20\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 738,\n              columnNumber: 38\n            }, this) : /*#__PURE__*/_jsxDEV(RefreshIcon, {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 738,\n              columnNumber: 71\n            }, this),\n            disabled: loading,\n            children: \"Aggiorna\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 734,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 730,\n          columnNumber: 13\n        }, this), caviSpare.length > 0 ? /*#__PURE__*/_jsxDEV(CaviFilterableTable, {\n          cavi: caviSpare,\n          loading: loading,\n          onFilteredDataChange: filteredData => console.log('Cavi spare filtrati:', filteredData.length)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 745,\n          columnNumber: 15\n        }, this) : /*#__PURE__*/_jsxDEV(Alert, {\n          severity: \"info\",\n          sx: {\n            mb: 2\n          },\n          children: \"Nessun cavo SPARE trovato. I cavi marcati come SPARE appariranno qui.\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 751,\n          columnNumber: 15\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 729,\n        columnNumber: 11\n      }, this), renderDetailsDialog(), /*#__PURE__*/_jsxDEV(Dialog, {\n        open: openEliminaCavoDialog,\n        onClose: () => setOpenEliminaCavoDialog(false),\n        fullWidth: true,\n        maxWidth: \"md\",\n        children: /*#__PURE__*/_jsxDEV(PosaCaviCollegamenti, {\n          cantiereId: cantiereId,\n          onSuccess: message => {\n            // Chiudi il dialogo\n            setOpenEliminaCavoDialog(false);\n\n            // Se c'è un messaggio, è un'operazione completata con successo\n            if (message) {\n              // Mostra un messaggio di successo\n              console.log('Operazione completata:', message);\n              // Mostra un messaggio all'utente\n              alert(message);\n              // Ricarica i dati dopo un ritardo per dare tempo al database di aggiornarsi\n              setTimeout(() => {\n                console.log('Ricaricamento dati dopo operazione...');\n                try {\n                  // Ricarica i dati invece di ricaricare la pagina\n                  fetchCavi();\n                } catch (error) {\n                  console.error('Errore durante il ricaricamento dei dati:', error);\n                  // Se fallisce, prova a ricaricare la pagina\n                  window.location.reload();\n                }\n              }, 1000);\n            } else {\n              // È un'operazione annullata, non mostrare messaggi\n              console.log('Operazione annullata dall\\'utente');\n            }\n          },\n          onError: message => {\n            // Mostra un messaggio di errore\n            console.error('Errore durante l\\'eliminazione del cavo:', message);\n            // Mostra un alert all'utente\n            alert(`Errore: ${message}`);\n            // Chiudi il dialogo\n            setOpenEliminaCavoDialog(false);\n            // Ricarica comunque i dati per assicurarsi che la vista sia aggiornata\n            fetchCavi();\n          },\n          initialOption: \"eliminaCavo\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 769,\n          columnNumber: 13\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 763,\n        columnNumber: 11\n      }, this), openModificaCavoDialog && console.log('VisualizzaCaviPage - cantiereId prima di aprire il dialog:', cantiereId), /*#__PURE__*/_jsxDEV(Dialog, {\n        open: openModificaCavoDialog,\n        onClose: () => setOpenModificaCavoDialog(false),\n        fullWidth: true,\n        maxWidth: \"md\",\n        children: /*#__PURE__*/_jsxDEV(PosaCaviCollegamenti, {\n          cantiereId: cantiereId,\n          onSuccess: message => {\n            // Chiudi il dialogo\n            setOpenModificaCavoDialog(false);\n\n            // Se c'è un messaggio, è un'operazione completata con successo\n            if (message) {\n              // Mostra un messaggio di successo\n              console.log('Operazione completata:', message);\n              // Mostra un messaggio all'utente\n              alert(message);\n              // Ricarica i dati immediatamente\n              console.log('Ricaricamento dati dopo operazione...');\n              // Ricarica i dati con un ritardo per dare tempo al database di aggiornarsi\n              setTimeout(() => {\n                try {\n                  fetchCavi();\n                } catch (error) {\n                  console.error('Errore durante il ricaricamento dei dati:', error);\n                  // Se fallisce, prova a ricaricare la pagina\n                  window.location.reload();\n                }\n              }, 1000);\n            } else {\n              // È un'operazione annullata, non mostrare messaggi\n              console.log('Operazione annullata dall\\'utente');\n            }\n          },\n          onError: message => {\n            // Mostra un messaggio di errore\n            console.error('Errore durante la modifica del cavo:', message);\n            // Mostra un alert all'utente\n            alert(`Errore: ${message}`);\n            // Chiudi il dialogo\n            setOpenModificaCavoDialog(false);\n            // Ricarica comunque i dati per assicurarsi che la vista sia aggiornata\n            console.log('Ricaricamento dati dopo errore...');\n            fetchCavi();\n          },\n          initialOption: \"modificaCavo\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 822,\n          columnNumber: 13\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 816,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(Dialog, {\n        open: openAggiungiCavoDialog,\n        onClose: () => setOpenAggiungiCavoDialog(false),\n        fullWidth: true,\n        maxWidth: \"md\",\n        children: [/*#__PURE__*/_jsxDEV(DialogTitle, {\n          children: \"Aggiungi Nuovo Cavo\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 873,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(DialogContent, {\n          children: /*#__PURE__*/_jsxDEV(Box, {\n            sx: {\n              mt: 1\n            },\n            children: /*#__PURE__*/_jsxDEV(AggiungiCavoForm, {\n              cantiereId: cantiereId,\n              onSuccess: message => {\n                // Chiudi il dialogo\n                setOpenAggiungiCavoDialog(false);\n                // Mostra un messaggio di successo\n                alert(message);\n                // Ricarica i dati dopo un ritardo per dare tempo al database di aggiornarsi\n                setTimeout(() => {\n                  console.log('Ricaricamento dati dopo operazione...');\n                  try {\n                    // Ricarica i dati invece di ricaricare la pagina\n                    fetchCavi();\n                  } catch (error) {\n                    console.error('Errore durante il ricaricamento dei dati:', error);\n                    // Se fallisce, prova a ricaricare la pagina immediatamente\n                    console.log('Tentativo di ricaricamento della pagina...');\n                    window.location.reload();\n                  }\n                }, 1000);\n              },\n              onError: message => {\n                // Mostra un messaggio di errore\n                console.error('Errore durante l\\'aggiunta del cavo:', message);\n                // Mostra un alert all'utente\n                alert(`Errore: ${message}`);\n              },\n              isDialog: true\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 876,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 875,\n            columnNumber: 15\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 874,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(DialogActions, {\n          children: /*#__PURE__*/_jsxDEV(Button, {\n            onClick: () => setOpenAggiungiCavoDialog(false),\n            children: \"Annulla\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 908,\n            columnNumber: 15\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 907,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 867,\n        columnNumber: 11\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 682,\n      columnNumber: 9\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 645,\n    columnNumber: 5\n  }, this);\n};\n_s(VisualizzaCaviPage, \"+CRkpkGy7mNa5mVGcx256tNnt7w=\", false, function () {\n  return [useAuth, useGlobalContext, useNavigate];\n});\n_c = VisualizzaCaviPage;\nexport default VisualizzaCaviPage;\nvar _c;\n$RefreshReg$(_c, \"VisualizzaCaviPage\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "Box", "Typography", "Paper", "<PERSON><PERSON>", "Grid", "Card", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "IconButton", "Chip", "CircularProgress", "LinearProgress", "Dialog", "DialogTitle", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "DialogActions", "InfoIcon", "RefreshIcon", "useNavigate", "useAuth", "useGlobalContext", "PosaCaviCollegamenti", "caviService", "AggiungiCavoForm", "normalizeInstallationStatus", "CaviFilterableTable", "jsxDEV", "_jsxDEV", "VisualizzaCaviPage", "_s", "_caviAttivi$", "_caviAttivi$2", "_caviAttivi$3", "isImpersonating", "user", "openEliminaCavoDialog", "setOpenEliminaCavoDialog", "openModificaCavoDialog", "setOpenModificaCavoDialog", "openAggiungiCavoDialog", "setOpenAggiungiCavoDialog", "navigate", "cantiereId", "setCantiereId", "cantiereName", "setCantiereName", "caviAttivi", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "caviSpare", "setCaviSpare", "loading", "setLoading", "error", "setError", "selected<PERSON><PERSON><PERSON>", "setSelectedCavo", "detailsDialogOpen", "setDetailsDialogOpen", "stats", "setStats", "totali", "cavi_attivi", "cavi_spare", "cavi_totali", "metrature", "metri_teorici_totali", "metri_reali_totali", "percentuale_completamento", "stati", "loadingStats", "setLoadingStats", "loadStatiInstallazione", "setStatiInstallazione", "filters", "setFilters", "stato_installazione", "tipologia", "sort_by", "sort_order", "statiInstallazione", "tipologieCavi", "setTipologieCavi", "<PERSON><PERSON><PERSON>", "console", "log", "cantiereIdToUse", "localStorage", "getItem", "attivi", "get<PERSON><PERSON>", "length", "attiviError", "caviSpareTra<PERSON>ttivi", "filter", "cavo", "modificato_manualmente", "spare", "getCaviSpare", "spareError", "standardError", "statsData", "getCaviStats", "validStats", "statsError", "message", "setTimeout", "document", "body", "textContent", "includes", "window", "location", "reload", "fetchData", "token", "selectedCantiereId", "selectedCantiereName", "i", "key", "role", "cantiere_id", "toString", "cantiere_name", "setItem", "base64Url", "split", "base64", "replace", "jsonPayload", "decodeURIComponent", "atob", "map", "c", "charCodeAt", "slice", "join", "payload", "JSON", "parse", "e", "warn", "cantiereIdNum", "parseInt", "isNaN", "item", "stato", "tipologie", "tipo", "timeoutPromise", "Promise", "_", "reject", "Error", "caviPromise", "race", "caviError", "status", "data", "stack", "code", "name", "response", "statusText", "sparePromise", "err", "_err$response", "_err$response2", "_err$response3", "_err$response4", "_err$response5", "_err$response5$data", "errorMessage", "detail", "handleOpenDetails", "handleCloseDetails", "renderDetailsDialog", "open", "onClose", "max<PERSON><PERSON><PERSON>", "fullWidth", "children", "id_cavo", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "dividers", "container", "spacing", "xs", "md", "variant", "gutterBottom", "sx", "mb", "sistema", "utility", "colore_cavo", "n_conduttori", "sezione", "sh", "ubicazione_partenza", "utenza_partenza", "descrizione_utenza_partenza", "responsabile_partenza", "comanda_partenza", "ubicazione_arrivo", "utenza_arrivo", "descrizione_utenza_arrivo", "responsabile_arrivo", "comanda_arrivo", "metri_te<PERSON>ci", "metratura_reale", "colle<PERSON>nti", "id_bobina", "responsabile_posa", "comanda_posa", "Date", "timestamp", "toLocaleString", "onClick", "renderStatsPanel", "p", "toFixed", "display", "alignItems", "mt", "width", "mr", "value", "height", "borderRadius", "min<PERSON><PERSON><PERSON>", "color", "flexWrap", "gap", "index", "label", "count", "size", "prev", "className", "flexDirection", "severity", "justifyContent", "startIcon", "disabled", "process", "env", "NODE_ENV", "bgcolor", "fontSize", "fontFamily", "Object", "keys", "stringify", "cavi", "onFilteredDataChange", "filteredData", "revisioneCorrente", "revisione_ufficiale", "revisione", "rev", "onSuccess", "alert", "onError", "initialOption", "isDialog", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/PycharmProjects/CMS/webapp/frontend/src/pages/cavi/VisualizzaCaviPage.js"], "sourcesContent": ["import React, { useState, useEffect } from 'react';\nimport {\n  Box,\n  Typography,\n  Paper,\n  Button,\n  Grid,\n  Card,\n  CardContent,\n  Alert,\n  IconButton,\n  Chip,\n  CircularProgress,\n  LinearProgress,\n  Dialog,\n  DialogTitle,\n  DialogContent,\n  DialogActions\n} from '@mui/material';\nimport InfoIcon from '@mui/icons-material/Info';\nimport RefreshIcon from '@mui/icons-material/Refresh';\nimport { useNavigate } from 'react-router-dom';\nimport { useAuth } from '../../context/AuthContext';\nimport { useGlobalContext } from '../../context/GlobalContext';\nimport PosaCaviCollegamenti from '../../components/cavi/PosaCaviCollegamenti';\nimport caviService from '../../services/caviService';\nimport AggiungiCavoForm from '../../components/cavi/AggiungiCavoForm';\nimport { normalizeInstallationStatus } from '../../utils/validationUtils';\nimport CaviFilterableTable from '../../components/cavi/CaviFilterableTable';\nimport './CaviPage.css';\n\nconst VisualizzaCaviPage = () => {\n  const { isImpersonating, user } = useAuth();\n  const { openEliminaCavoDialog, setOpenEliminaCavoDialog, openModificaCavoDialog, setOpenModificaCavoDialog, openAggiungiCavoDialog, setOpenAggiungiCavoDialog } = useGlobalContext();\n  const navigate = useNavigate();\n  const [cantiereId, setCantiereId] = useState(null);\n  const [cantiereName, setCantiereName] = useState('');\n  const [caviAttivi, setCaviAttivi] = useState([]);\n  const [caviSpare, setCaviSpare] = useState([]);\n  const [loading, setLoading] = useState(true);\n  const [error, setError] = useState(null);\n  // Rimosso stato viewMode\n\n  // Stato per il dialogo dei dettagli del cavo\n  const [selectedCavo, setSelectedCavo] = useState(null);\n  const [detailsDialogOpen, setDetailsDialogOpen] = useState(false);\n\n  // Stato per le statistiche\n  const [stats, setStats] = useState({\n    totali: { cavi_attivi: 0, cavi_spare: 0, cavi_totali: 0 },\n    metrature: { metri_teorici_totali: 0, metri_reali_totali: 0, percentuale_completamento: 0 },\n    stati: []\n  });\n  const [loadingStats, setLoadingStats] = useState(false);\n\n  // Rimosso stato per il debug\n\n  // Funzione per caricare gli stati di installazione disponibili\n  const loadStatiInstallazione = () => {\n    // Usa i valori dell'enum StatoInstallazione\n    setStatiInstallazione(['Installato', 'Da installare', 'In corso']);\n  };\n\n  // Stato per filtri e ordinamento\n  const [filters, setFilters] = useState({\n    stato_installazione: '',\n    tipologia: '',\n    sort_by: '',\n    sort_order: 'asc'\n  });\n\n  // Opzioni per i filtri\n  const [statiInstallazione, setStatiInstallazione] = useState([]);\n  const [tipologieCavi, setTipologieCavi] = useState([]);\n\n  // Rimossa funzione di debug\n\n  // Funzione per caricare i cavi\n  const fetchCavi = async () => {\n    try {\n      setLoading(true);\n      console.log('Caricamento cavi per cantiere:', cantiereId);\n\n      // Verifica che cantiereId sia valido\n      if (!cantiereId) {\n        console.error('fetchCavi: cantiereId non valido:', cantiereId);\n        setError('ID cantiere non valido o mancante. Ricarica la pagina.');\n        setLoading(false);\n        return;\n      }\n\n      // Recupera il cantiereId dal localStorage come fallback\n      let cantiereIdToUse = cantiereId;\n      if (!cantiereIdToUse) {\n        cantiereIdToUse = localStorage.getItem('selectedCantiereId');\n        console.log('Usando cantiereId dal localStorage:', cantiereIdToUse);\n        if (!cantiereIdToUse) {\n          console.error('Impossibile trovare un ID cantiere valido');\n          setError('ID cantiere non trovato. Ricarica la pagina.');\n          setLoading(false);\n          return;\n        }\n      }\n\n      // Carica i cavi attivi\n      console.log('Caricamento cavi attivi (tipo_cavo=0)...');\n      let attivi = [];\n      try {\n        attivi = await caviService.getCavi(cantiereIdToUse, 0, filters);\n        console.log('Cavi attivi caricati:', attivi ? attivi.length : 0);\n      } catch (attiviError) {\n        console.error('Errore nel caricamento dei cavi attivi:', attiviError);\n        // Continua con un array vuoto\n        attivi = [];\n      }\n\n      // Verifica se ci sono cavi con modificato_manualmente = 3 tra i cavi attivi\n      if (attivi && attivi.length > 0) {\n        const caviSpareTraAttivi = attivi.filter(cavo => cavo.modificato_manualmente === 3);\n        if (caviSpareTraAttivi.length > 0) {\n          console.error('ERRORE: Trovati cavi con modificato_manualmente = 3 tra i cavi attivi:', caviSpareTraAttivi);\n        }\n      }\n\n      setCaviAttivi(attivi || []);\n\n      // Carica i cavi SPARE con la nuova funzione dedicata\n      let spare = [];\n      try {\n        console.log('Caricamento cavi SPARE con funzione dedicata...');\n        spare = await caviService.getCaviSpare(cantiereIdToUse);\n        console.log('Cavi SPARE caricati con funzione dedicata:', spare ? spare.length : 0);\n        if (spare && spare.length > 0) {\n          console.log('Primo cavo SPARE:', spare[0]);\n        }\n      } catch (spareError) {\n        console.error('Errore nel caricamento dei cavi SPARE con funzione dedicata:', spareError);\n        // Se fallisce, prova con il metodo standard\n        try {\n          console.log('Tentativo con metodo standard...');\n          spare = await caviService.getCavi(cantiereIdToUse, 3);\n          console.log('Cavi SPARE caricati con metodo standard:', spare ? spare.length : 0);\n        } catch (standardError) {\n          console.error('Errore anche con metodo standard:', standardError);\n          // Continua con un array vuoto\n          spare = [];\n        }\n      }\n      setCaviSpare(spare || []);\n\n      // Carica le statistiche\n      try {\n        console.log('Caricamento statistiche...');\n        const statsData = await caviService.getCaviStats(cantiereIdToUse);\n        console.log('Statistiche caricate:', statsData);\n\n        // Verifica che statsData abbia la struttura attesa\n        if (!statsData || typeof statsData !== 'object') {\n          console.error('Statistiche non valide:', statsData);\n          // Imposta un oggetto stats con struttura valida ma vuota\n          setStats({\n            totali: { cavi_attivi: 0, cavi_spare: 0, cavi_totali: 0 },\n            metrature: { metri_teorici_totali: 0, metri_reali_totali: 0, percentuale_completamento: 0 },\n            stati: []\n          });\n        } else {\n          // Assicurati che tutte le proprietà necessarie siano presenti\n          const validStats = {\n            totali: statsData.totali || { cavi_attivi: 0, cavi_spare: 0, cavi_totali: 0 },\n            metrature: statsData.metrature || { metri_teorici_totali: 0, metri_reali_totali: 0, percentuale_completamento: 0 },\n            stati: statsData.stati || []\n          };\n          setStats(validStats);\n        }\n      } catch (statsError) {\n        console.error('Errore nel caricamento delle statistiche:', statsError);\n        // Continua con statistiche vuote ma con struttura valida\n        setStats({\n          totali: { cavi_attivi: 0, cavi_spare: 0, cavi_totali: 0 },\n          metrature: { metri_teorici_totali: 0, metri_reali_totali: 0, percentuale_completamento: 0 },\n          stati: []\n        });\n      }\n\n      // Se siamo arrivati qui, rimuovi eventuali messaggi di errore precedenti\n      setError('');\n    } catch (error) {\n      console.error('Errore generale nel caricamento dei cavi:', error);\n      setError(`Errore nel caricamento dei cavi: ${error.message || 'Errore sconosciuto'}`);\n\n      // Prova a ricaricare la pagina dopo un ritardo se l'errore persiste\n      setTimeout(() => {\n        // Verifica se siamo ancora in errore\n        if (document.body.textContent.includes('Errore nel caricamento dei cavi')) {\n          console.log('Errore persistente, tentativo di ricaricamento della pagina...');\n          window.location.reload();\n        }\n      }, 5000); // 5 secondi di ritardo\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  // Carica i dati del cantiere e dei cavi\n  useEffect(() => {\n    // Carica gli stati di installazione all'avvio\n    loadStatiInstallazione();\n\n    const fetchData = async () => {\n      try {\n        console.log('Inizializzazione VisualizzaCaviPage...');\n\n        // Verifica che l'utente sia autenticato\n        const token = localStorage.getItem('token');\n        console.log('Token presente:', !!token);\n        if (!token) {\n          setError('Sessione scaduta. Effettua nuovamente il login.');\n          setLoading(false);\n          return;\n        }\n\n        // Recupera l'ID del cantiere selezionato dal localStorage\n        let selectedCantiereId = localStorage.getItem('selectedCantiereId');\n        let selectedCantiereName = localStorage.getItem('selectedCantiereName');\n\n        console.log('Cantiere selezionato dal localStorage:', { selectedCantiereId, selectedCantiereName });\n        console.log('Dati utente:', user);\n\n        // Stampa tutti i dati nel localStorage per debug\n        console.log('DEBUG - Tutti i dati nel localStorage:');\n        for (let i = 0; i < localStorage.length; i++) {\n          const key = localStorage.key(i);\n          console.log(`${key}: ${localStorage.getItem(key)}`);\n        }\n\n        // SOLUZIONE DIRETTA: Ottieni l'ID del cantiere direttamente dal token JWT\n        if (user?.role === 'cantieri_user') {\n          console.log('Utente cantiere rilevato, tentativo di recupero ID cantiere dai dati utente');\n\n          // Verifica se l'utente ha un ID cantiere nei dati utente\n          if (user.cantiere_id) {\n            console.log('Trovato ID cantiere nei dati utente:', user.cantiere_id);\n            selectedCantiereId = user.cantiere_id.toString();\n            selectedCantiereName = user.cantiere_name || `Cantiere ${user.cantiere_id}`;\n\n            // Salva l'ID e il nome del cantiere nel localStorage\n            localStorage.setItem('selectedCantiereId', selectedCantiereId);\n            localStorage.setItem('selectedCantiereName', selectedCantiereName);\n            console.log('Salvato ID cantiere nel localStorage:', selectedCantiereId);\n          } else {\n            // Tentativo di recupero dal token JWT\n            try {\n              console.log('Tentativo di decodifica del token JWT per recuperare l\\'ID cantiere');\n              const token = localStorage.getItem('token');\n              if (token) {\n                // Decodifica il token JWT (senza verifica della firma)\n                const base64Url = token.split('.')[1];\n                const base64 = base64Url.replace(/-/g, '+').replace(/_/g, '/');\n                const jsonPayload = decodeURIComponent(atob(base64).split('').map(c => {\n                  return '%' + ('00' + c.charCodeAt(0).toString(16)).slice(-2);\n                }).join(''));\n\n                const payload = JSON.parse(jsonPayload);\n                console.log('Payload del token JWT:', payload);\n\n                if (payload.cantiere_id) {\n                  console.log('Trovato ID cantiere nel token JWT:', payload.cantiere_id);\n                  selectedCantiereId = payload.cantiere_id.toString();\n                  // Usa un nome generico se non disponibile\n                  selectedCantiereName = `Cantiere ${payload.cantiere_id}`;\n\n                  // Salva l'ID e il nome del cantiere nel localStorage\n                  localStorage.setItem('selectedCantiereId', selectedCantiereId);\n                  localStorage.setItem('selectedCantiereName', selectedCantiereName);\n                  console.log('Salvato ID cantiere nel localStorage:', selectedCantiereId);\n                }\n              }\n            } catch (e) {\n              console.error('Errore durante la decodifica del token JWT:', e);\n            }\n          }\n        }\n\n        // SOLUZIONE TEMPORANEA: Se ancora non abbiamo un ID cantiere, usa un valore hardcoded per debug\n        if (!selectedCantiereId || selectedCantiereId === 'undefined' || selectedCantiereId === 'null') {\n          console.warn('ATTENZIONE: Nessun ID cantiere trovato, utilizzo valore hardcoded per debug');\n          // Usa il primo cantiere disponibile (questo è solo per debug)\n          selectedCantiereId = '1'; // Sostituisci con un ID cantiere valido nel tuo database\n          selectedCantiereName = 'Cantiere Debug';\n\n          // Salva l'ID e il nome del cantiere nel localStorage\n          localStorage.setItem('selectedCantiereId', selectedCantiereId);\n          localStorage.setItem('selectedCantiereName', selectedCantiereName);\n          console.log('Salvato ID cantiere hardcoded nel localStorage:', selectedCantiereId);\n        }\n\n        // Verifica finale\n        if (!selectedCantiereId) {\n          setError('Nessun cantiere selezionato. Torna alla pagina dei cantieri.');\n          setLoading(false);\n          return;\n        }\n\n        // Verifica che l'ID del cantiere sia un numero valido\n        const cantiereIdNum = parseInt(selectedCantiereId, 10);\n        console.log('ID cantiere convertito a numero:', cantiereIdNum);\n        if (isNaN(cantiereIdNum)) {\n          setError(`ID cantiere non valido: ${selectedCantiereId}. Torna alla pagina dei cantieri.`);\n          setLoading(false);\n          return;\n        }\n\n        // Usa il numero convertito, non la stringa\n        setCantiereId(cantiereIdNum);\n        setCantiereName(selectedCantiereName || `Cantiere ${cantiereIdNum}`);\n\n        // Carica le statistiche dei cavi\n        try {\n          setLoadingStats(true);\n          console.log('Caricamento statistiche cavi per cantiere:', cantiereIdNum);\n          const statsData = await caviService.getCaviStats(cantiereIdNum);\n          console.log('Statistiche cavi caricate:', statsData);\n          setStats(statsData);\n\n          // Estrai gli stati di installazione e le tipologie per i filtri\n          if (statsData && statsData.stati) {\n            const stati = statsData.stati.map(item => item.stato).filter(stato => stato !== 'Non specificato');\n            setStatiInstallazione(stati);\n          }\n\n          if (statsData && statsData.tipologie) {\n            const tipologie = statsData.tipologie.map(item => item.tipologia).filter(tipo => tipo !== 'Non specificata');\n            setTipologieCavi(tipologie);\n          }\n\n          setLoadingStats(false);\n        } catch (statsError) {\n          console.error('Errore nel caricamento delle statistiche:', statsError);\n          setLoadingStats(false);\n          // Non interrompere il flusso se le statistiche falliscono\n        }\n\n        // Carica i cavi attivi con gestione degli errori migliorata\n        console.log('Caricamento cavi attivi per cantiere:', cantiereIdNum);\n        try {\n          // Imposta un timeout per evitare che la richiesta rimanga bloccata\n          const timeoutPromise = new Promise((_, reject) => {\n            setTimeout(() => reject(new Error('Timeout durante il caricamento dei cavi attivi')), 30000); // Aumentato a 30 secondi\n          });\n\n          // Esegui la richiesta con un timeout di sicurezza e applica i filtri\n          console.log('Iniziando chiamata API per cavi attivi con filtri:', filters);\n          const caviPromise = caviService.getCavi(cantiereIdNum, 0, filters);\n          const attivi = await Promise.race([caviPromise, timeoutPromise]);\n\n          console.log('Cavi attivi caricati:', attivi);\n          console.log('Numero di cavi attivi trovati:', attivi ? attivi.length : 0);\n          if (attivi && attivi.length > 0) {\n            console.log('Primo cavo attivo:', attivi[0]);\n          } else {\n            console.warn('Nessun cavo attivo trovato per il cantiere', cantiereIdNum);\n          }\n          setCaviAttivi(attivi || []);\n        } catch (caviError) {\n          console.error('Errore nel caricamento dei cavi attivi:', caviError);\n          console.error('Dettagli errore cavi attivi:', {\n            message: caviError.message,\n            status: caviError.status,\n            data: caviError.data,\n            stack: caviError.stack,\n            code: caviError.code,\n            name: caviError.name,\n            response: caviError.response ? {\n              status: caviError.response.status,\n              statusText: caviError.response.statusText,\n              data: caviError.response.data\n            } : 'No response'\n          });\n\n          // Non interrompere il flusso, continua con i cavi spare\n          setCaviAttivi([]);\n          console.warn('Continuazione del flusso dopo errore nei cavi attivi');\n\n          // Aggiungi un messaggio di errore visibile all'utente\n          setError(`Errore nel caricamento dei cavi attivi: ${caviError.message}. Controlla la console per maggiori dettagli.`);\n        }\n\n        // Carica i cavi spare con gestione degli errori migliorata\n        console.log('Caricamento cavi spare per cantiere:', cantiereIdNum);\n        try {\n          // Imposta un timeout per evitare che la richiesta rimanga bloccata\n          const timeoutPromise = new Promise((_, reject) => {\n            setTimeout(() => reject(new Error('Timeout durante il caricamento dei cavi spare')), 30000); // Aumentato a 30 secondi\n          });\n\n          // Esegui la richiesta con un timeout di sicurezza\n          console.log('Iniziando chiamata API per cavi spare...');\n          // Non applichiamo i filtri ai cavi spare, solo agli attivi\n          const sparePromise = caviService.getCavi(cantiereIdNum, 3);\n          const spare = await Promise.race([sparePromise, timeoutPromise]);\n\n          console.log('Cavi spare caricati:', spare);\n          console.log('Numero di cavi spare trovati:', spare ? spare.length : 0);\n          if (spare && spare.length > 0) {\n            console.log('Primo cavo spare:', spare[0]);\n          } else {\n            console.warn('Nessun cavo spare trovato per il cantiere', cantiereIdNum);\n          }\n          setCaviSpare(spare || []);\n        } catch (spareError) {\n          console.error('Errore nel caricamento dei cavi spare:', spareError);\n          console.error('Dettagli errore cavi spare:', {\n            message: spareError.message,\n            status: spareError.status,\n            data: spareError.data,\n            stack: spareError.stack,\n            code: spareError.code,\n            name: spareError.name,\n            response: spareError.response ? {\n              status: spareError.response.status,\n              statusText: spareError.response.statusText,\n              data: spareError.response.data\n            } : 'No response'\n          });\n\n          // Non interrompere il flusso, imposta un array vuoto\n          setCaviSpare([]);\n\n          // Aggiungi un messaggio di errore visibile all'utente se non c'è già un errore per i cavi attivi\n          if (!error) {\n            setError(`Errore nel caricamento dei cavi spare: ${spareError.message}. Controlla la console per maggiori dettagli.`);\n          }\n        }\n\n        // Se siamo arrivati qui, almeno abbiamo caricato l'interfaccia di base\n        setLoading(false);\n\n      } catch (err) {\n        console.error('Errore nel caricamento dei cavi:', err);\n        console.error('Dettagli errore generale:', {\n          message: err.message,\n          status: err.status || err.response?.status,\n          data: err.data || err.response?.data,\n          stack: err.stack\n        });\n\n        // Estrai il messaggio di errore dettagliato\n        let errorMessage = 'Errore sconosciuto';\n\n        if (err.message && err.message.includes('ID cantiere non valido')) {\n          errorMessage = err.message;\n        } else if (err.status === 401 || err.status === 403 ||\n                  err.response?.status === 401 || err.response?.status === 403) {\n          errorMessage = 'Sessione scaduta o non autorizzata. Effettua nuovamente il login.';\n        } else if (err.response?.data?.detail) {\n          // Estrai il messaggio di errore dettagliato dall'API\n          errorMessage = `Errore API: ${err.response.data.detail}`;\n        } else if (err.code === 'ERR_NETWORK') {\n          // Errore di rete\n          errorMessage = 'Network Error. Verifica che il backend sia in esecuzione e accessibile.';\n        } else if (err.message) {\n          errorMessage = err.message;\n        }\n\n        setError(`Impossibile caricare i cavi: ${errorMessage}. Riprova più tardi.`);\n\n        // Imposta array vuoti per evitare errori di rendering\n        setCaviAttivi([]);\n        setCaviSpare([]);\n      } finally {\n        setLoading(false);\n      }\n    };\n\n    fetchData();\n  }, [filters]); // Ricarica i dati quando cambiano i filtri\n\n  // I filtri sono ora gestiti dal componente CaviFilterableTable\n\n  // Funzione per aprire il dialogo dei dettagli del cavo\n  const handleOpenDetails = (cavo) => {\n    setSelectedCavo(cavo);\n    setDetailsDialogOpen(true);\n  };\n\n  // Funzione per chiudere il dialogo dei dettagli del cavo\n  const handleCloseDetails = () => {\n    setDetailsDialogOpen(false);\n    setSelectedCavo(null);\n  };\n\n  // Nessuna funzione di navigazione necessaria, tutto è gestito dal menu principale\n\n  // La visualizzazione dei cavi è ora gestita dal componente CaviFilterableTable\n\n  // Rimossa funzione handleViewModeChange\n\n  // Renderizza il dialogo dei dettagli del cavo\n  const renderDetailsDialog = () => {\n    if (!selectedCavo) return null;\n\n    return (\n      <Dialog open={detailsDialogOpen} onClose={handleCloseDetails} maxWidth=\"md\" fullWidth>\n        <DialogTitle>\n          Dettagli Cavo: {selectedCavo.id_cavo}\n        </DialogTitle>\n        <DialogContent dividers>\n          <Grid container spacing={2}>\n            <Grid item xs={12} md={6}>\n              <Typography variant=\"subtitle1\" gutterBottom>Informazioni Generali</Typography>\n              <Box sx={{ mb: 2 }}>\n                <Typography variant=\"body2\"><strong>Sistema:</strong> {selectedCavo.sistema || 'N/A'}</Typography>\n                <Typography variant=\"body2\"><strong>Utility:</strong> {selectedCavo.utility || 'N/A'}</Typography>\n                <Typography variant=\"body2\"><strong>Tipologia:</strong> {selectedCavo.tipologia || 'N/A'}</Typography>\n                <Typography variant=\"body2\"><strong>Colore:</strong> {selectedCavo.colore_cavo || 'N/A'}</Typography>\n                <Typography variant=\"body2\"><strong>N. Conduttori:</strong> {selectedCavo.n_conduttori || 'N/A'}</Typography>\n                <Typography variant=\"body2\"><strong>Sezione:</strong> {selectedCavo.sezione || 'N/A'}</Typography>\n                <Typography variant=\"body2\"><strong>SH:</strong> {selectedCavo.sh || 'N/A'}</Typography>\n              </Box>\n\n              <Typography variant=\"subtitle1\" gutterBottom>Partenza</Typography>\n              <Box sx={{ mb: 2 }}>\n                <Typography variant=\"body2\"><strong>Ubicazione:</strong> {selectedCavo.ubicazione_partenza || 'N/A'}</Typography>\n                <Typography variant=\"body2\"><strong>Utenza:</strong> {selectedCavo.utenza_partenza || 'N/A'}</Typography>\n                <Typography variant=\"body2\"><strong>Descrizione:</strong> {selectedCavo.descrizione_utenza_partenza || 'N/A'}</Typography>\n                <Typography variant=\"body2\"><strong>Responsabile:</strong> {selectedCavo.responsabile_partenza || 'N/A'}</Typography>\n                <Typography variant=\"body2\"><strong>Comanda:</strong> {selectedCavo.comanda_partenza || 'N/A'}</Typography>\n              </Box>\n            </Grid>\n\n            <Grid item xs={12} md={6}>\n              <Typography variant=\"subtitle1\" gutterBottom>Arrivo</Typography>\n              <Box sx={{ mb: 2 }}>\n                <Typography variant=\"body2\"><strong>Ubicazione:</strong> {selectedCavo.ubicazione_arrivo || 'N/A'}</Typography>\n                <Typography variant=\"body2\"><strong>Utenza:</strong> {selectedCavo.utenza_arrivo || 'N/A'}</Typography>\n                <Typography variant=\"body2\"><strong>Descrizione:</strong> {selectedCavo.descrizione_utenza_arrivo || 'N/A'}</Typography>\n                <Typography variant=\"body2\"><strong>Responsabile:</strong> {selectedCavo.responsabile_arrivo || 'N/A'}</Typography>\n                <Typography variant=\"body2\"><strong>Comanda:</strong> {selectedCavo.comanda_arrivo || 'N/A'}</Typography>\n              </Box>\n\n              <Typography variant=\"subtitle1\" gutterBottom>Installazione</Typography>\n              <Box sx={{ mb: 2 }}>\n                <Typography variant=\"body2\"><strong>Metri Teorici:</strong> {selectedCavo.metri_teorici || 'N/A'}</Typography>\n                <Typography variant=\"body2\"><strong>Metratura Reale:</strong> {selectedCavo.metratura_reale || '0'}</Typography>\n                <Typography variant=\"body2\"><strong>Stato:</strong> {normalizeInstallationStatus(selectedCavo.stato_installazione)}</Typography>\n                <Typography variant=\"body2\"><strong>Collegamenti:</strong> {selectedCavo.collegamenti || '0'}</Typography>\n                <Typography variant=\"body2\"><strong>Bobina:</strong> {selectedCavo.id_bobina || 'N/A'}</Typography>\n                <Typography variant=\"body2\"><strong>Responsabile Posa:</strong> {selectedCavo.responsabile_posa || 'N/A'}</Typography>\n                <Typography variant=\"body2\"><strong>Comanda Posa:</strong> {selectedCavo.comanda_posa || 'N/A'}</Typography>\n                <Typography variant=\"body2\"><strong>Ultimo Aggiornamento:</strong> {new Date(selectedCavo.timestamp).toLocaleString()}</Typography>\n              </Box>\n            </Grid>\n          </Grid>\n        </DialogContent>\n        <DialogActions>\n          <Button onClick={handleCloseDetails}>Chiudi</Button>\n        </DialogActions>\n      </Dialog>\n    );\n  };\n\n  // Il pannello dei filtri è ora gestito dal componente CaviFilterableTable\n\n  // Renderizza il pannello delle statistiche\n  const renderStatsPanel = () => {\n    // Verifica che stats sia definito e abbia la struttura attesa\n    if (!stats || !stats.totali || !stats.metrature || !stats.stati) {\n      return (\n        <Paper sx={{ mb: 3, p: 2 }}>\n          <Typography variant=\"h6\" gutterBottom>Statistiche</Typography>\n          {loadingStats ? (\n            <LinearProgress />\n          ) : (\n            <Typography variant=\"body2\">Nessuna statistica disponibile</Typography>\n          )}\n        </Paper>\n      );\n    }\n\n    // Valori predefiniti per evitare errori\n    const totali = stats.totali || { cavi_attivi: 0, cavi_spare: 0, cavi_totali: 0 };\n    const metrature = stats.metrature || { metri_teorici_totali: 0, metri_reali_totali: 0, percentuale_completamento: 0 };\n    const stati = stats.stati || [];\n\n    return (\n      <Paper sx={{ mb: 3, p: 2 }}>\n        <Typography variant=\"h6\" gutterBottom>Statistiche</Typography>\n        {loadingStats ? (\n          <LinearProgress />\n        ) : (\n          <Grid container spacing={2}>\n            <Grid item xs={12} md={4}>\n              <Typography variant=\"subtitle1\" gutterBottom>Totali</Typography>\n              <Typography variant=\"body2\">Cavi Attivi: {totali.cavi_attivi || 0}</Typography>\n              <Typography variant=\"body2\">Cavi Spare: {totali.cavi_spare || 0}</Typography>\n              <Typography variant=\"body2\">Totale Cavi: {totali.cavi_totali || 0}</Typography>\n              {/* Rimossa visualizzazione della revisione da qui, spostata nel titolo delle statistiche della tabella */}\n            </Grid>\n\n            <Grid item xs={12} md={4}>\n              <Typography variant=\"subtitle1\" gutterBottom>Metrature</Typography>\n              <Typography variant=\"body2\">Metri Teorici: {(metrature.metri_teorici_totali || 0).toFixed(2)}</Typography>\n              <Typography variant=\"body2\">Metri Posati: {(metrature.metri_reali_totali || 0).toFixed(2)}</Typography>\n              <Box sx={{ display: 'flex', alignItems: 'center', mt: 1 }}>\n                <Box sx={{ width: '100%', mr: 1 }}>\n                  <LinearProgress\n                    variant=\"determinate\"\n                    value={metrature.percentuale_completamento || 0}\n                    sx={{ height: 10, borderRadius: 5 }}\n                  />\n                </Box>\n                <Box sx={{ minWidth: 35 }}>\n                  <Typography variant=\"body2\" color=\"text.secondary\">{`${(metrature.percentuale_completamento || 0).toFixed(1)}%`}</Typography>\n                </Box>\n              </Box>\n            </Grid>\n\n            <Grid item xs={12} md={4}>\n              <Typography variant=\"subtitle1\" gutterBottom>Stati</Typography>\n              <Box sx={{ display: 'flex', flexWrap: 'wrap', gap: 1 }}>\n                {stati.length > 0 ? stati.map((stato, index) => (\n                  <Chip\n                    key={index}\n                    label={`${stato.stato || 'N/A'}: ${stato.count || 0}`}\n                    size=\"small\"\n                    onClick={() => {\n                      setFilters(prev => ({\n                        ...prev,\n                        stato_installazione: stato.stato === 'Non specificato' ? '' : stato.stato\n                      }));\n                    }}\n                  />\n                )) : (\n                  <Typography variant=\"body2\">Nessuno stato disponibile</Typography>\n                )}\n              </Box>\n            </Grid>\n          </Grid>\n        )}\n      </Paper>\n    );\n  };\n\n  return (\n    <Box className=\"cavi-page\">\n      {loading ? (\n        <Box sx={{ display: 'flex', flexDirection: 'column', alignItems: 'center', mt: 4 }}>\n          <CircularProgress size={40} />\n          <Typography sx={{ mt: 2 }}>Caricamento cavi...</Typography>\n          <Button\n            variant=\"outlined\"\n            color=\"primary\"\n            onClick={() => window.location.reload()}\n            sx={{ mt: 2 }}\n          >\n            Ricarica la pagina\n          </Button>\n        </Box>\n      ) : error ? (\n        <Box>\n          <Alert severity=\"error\" sx={{ mb: 2 }}>\n            {error}\n            {error.includes('Network Error') && (\n              <Typography variant=\"body2\" sx={{ mt: 1 }}>\n                <strong>Suggerimento:</strong> Verifica che il server backend sia in esecuzione sulla porta 8001.\n                <br />\n                Puoi avviare il backend eseguendo il file <code>run_system.py</code> nella cartella principale del progetto.\n              </Typography>\n            )}\n          </Alert>\n          <Box sx={{ display: 'flex', gap: 2 }}>\n            <Button\n              variant=\"contained\"\n              className=\"primary-button\"\n              onClick={() => window.location.reload()}\n            >\n              Ricarica la pagina\n            </Button>\n          </Box>\n        </Box>\n      ) : (\n        <Box>\n          {/* Rimosso il pulsante di refresh, gli utenti possono usare il refresh del browser */}\n\n          {/* Pannello delle statistiche */}\n\n          {/* Sezione Cavi Attivi */}\n          <Box sx={{ mt: 4 }}>\n            <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 2 }}>\n              <Typography variant=\"h5\">\n                Cavi Attivi {caviAttivi.length > 0 ? `(${caviAttivi.length})` : ''}\n              </Typography>\n              <Button\n                variant=\"outlined\"\n                size=\"small\"\n                onClick={() => window.location.reload()}\n                startIcon={loading ? <CircularProgress size={20} /> : <RefreshIcon />}\n                disabled={loading}\n              >\n                Aggiorna\n              </Button>\n            </Box>\n          </Box>\n\n          {caviAttivi.length > 0 ? (\n            <Box sx={{ mb: 2 }}>\n              {/* Debug: Mostra le proprietà del primo cavo per verificare il nome del campo revisione */}\n              {process.env.NODE_ENV === 'development' && (\n                <Box sx={{ mb: 2, p: 1, bgcolor: '#f0f0f0', borderRadius: 1, fontSize: '0.8rem', fontFamily: 'monospace', display: 'none' }}>\n                  {Object.keys(caviAttivi[0]).map(key => (\n                    <div key={key}>{key}: {JSON.stringify(caviAttivi[0][key])}</div>\n                  ))}\n                </Box>\n              )}\n              <CaviFilterableTable\n                cavi={caviAttivi}\n                loading={loading}\n                onFilteredDataChange={(filteredData) => console.log('Cavi attivi filtrati:', filteredData.length)}\n                revisioneCorrente={caviAttivi[0]?.revisione_ufficiale || caviAttivi[0]?.revisione || caviAttivi[0]?.rev}\n              />\n            </Box>\n          ) : (\n            <Alert severity=\"info\" sx={{ mb: 2 }}>\n              Nessun cavo attivo trovato. I cavi attivi appariranno qui.\n            </Alert>\n          )}\n\n          {/* Sezione Cavi Spare */}\n          <Box sx={{ mt: 4 }}>\n            <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 2 }}>\n              <Typography variant=\"h5\">\n                Cavi Spare {caviSpare.length > 0 ? `(${caviSpare.length})` : ''}\n              </Typography>\n              <Button\n                variant=\"outlined\"\n                size=\"small\"\n                onClick={() => window.location.reload()}\n                startIcon={loading ? <CircularProgress size={20} /> : <RefreshIcon />}\n                disabled={loading}\n              >\n                Aggiorna\n              </Button>\n            </Box>\n            {caviSpare.length > 0 ? (\n              <CaviFilterableTable\n                cavi={caviSpare}\n                loading={loading}\n                onFilteredDataChange={(filteredData) => console.log('Cavi spare filtrati:', filteredData.length)}\n              />\n            ) : (\n              <Alert severity=\"info\" sx={{ mb: 2 }}>\n                Nessun cavo SPARE trovato. I cavi marcati come SPARE appariranno qui.\n              </Alert>\n            )}\n          </Box>\n\n          {/* Rimossa sezione Debug */}\n\n          {/* Dialogo dei dettagli del cavo */}\n          {renderDetailsDialog()}\n\n          {/* Dialogo per l'eliminazione dei cavi */}\n          <Dialog\n            open={openEliminaCavoDialog}\n            onClose={() => setOpenEliminaCavoDialog(false)}\n            fullWidth\n            maxWidth=\"md\"\n          >\n            <PosaCaviCollegamenti\n              cantiereId={cantiereId}\n              onSuccess={(message) => {\n                // Chiudi il dialogo\n                setOpenEliminaCavoDialog(false);\n\n                // Se c'è un messaggio, è un'operazione completata con successo\n                if (message) {\n                  // Mostra un messaggio di successo\n                  console.log('Operazione completata:', message);\n                  // Mostra un messaggio all'utente\n                  alert(message);\n                  // Ricarica i dati dopo un ritardo per dare tempo al database di aggiornarsi\n                  setTimeout(() => {\n                    console.log('Ricaricamento dati dopo operazione...');\n                    try {\n                      // Ricarica i dati invece di ricaricare la pagina\n                      fetchCavi();\n                    } catch (error) {\n                      console.error('Errore durante il ricaricamento dei dati:', error);\n                      // Se fallisce, prova a ricaricare la pagina\n                      window.location.reload();\n                    }\n                  }, 1000);\n                } else {\n                  // È un'operazione annullata, non mostrare messaggi\n                  console.log('Operazione annullata dall\\'utente');\n                }\n              }}\n              onError={(message) => {\n                // Mostra un messaggio di errore\n                console.error('Errore durante l\\'eliminazione del cavo:', message);\n                // Mostra un alert all'utente\n                alert(`Errore: ${message}`);\n                // Chiudi il dialogo\n                setOpenEliminaCavoDialog(false);\n                // Ricarica comunque i dati per assicurarsi che la vista sia aggiornata\n                fetchCavi();\n              }}\n              initialOption=\"eliminaCavo\"\n            />\n          </Dialog>\n\n          {/* Dialogo per la modifica dei cavi */}\n          {/* Log del cantiereId prima di aprire il dialog */}\n          {openModificaCavoDialog && console.log('VisualizzaCaviPage - cantiereId prima di aprire il dialog:', cantiereId)}\n\n          <Dialog\n            open={openModificaCavoDialog}\n            onClose={() => setOpenModificaCavoDialog(false)}\n            fullWidth\n            maxWidth=\"md\"\n          >\n            <PosaCaviCollegamenti\n              cantiereId={cantiereId}\n              onSuccess={(message) => {\n                // Chiudi il dialogo\n                setOpenModificaCavoDialog(false);\n\n                // Se c'è un messaggio, è un'operazione completata con successo\n                if (message) {\n                  // Mostra un messaggio di successo\n                  console.log('Operazione completata:', message);\n                  // Mostra un messaggio all'utente\n                  alert(message);\n                  // Ricarica i dati immediatamente\n                  console.log('Ricaricamento dati dopo operazione...');\n                  // Ricarica i dati con un ritardo per dare tempo al database di aggiornarsi\n                  setTimeout(() => {\n                    try {\n                      fetchCavi();\n                    } catch (error) {\n                      console.error('Errore durante il ricaricamento dei dati:', error);\n                      // Se fallisce, prova a ricaricare la pagina\n                      window.location.reload();\n                    }\n                  }, 1000);\n                } else {\n                  // È un'operazione annullata, non mostrare messaggi\n                  console.log('Operazione annullata dall\\'utente');\n                }\n              }}\n              onError={(message) => {\n                // Mostra un messaggio di errore\n                console.error('Errore durante la modifica del cavo:', message);\n                // Mostra un alert all'utente\n                alert(`Errore: ${message}`);\n                // Chiudi il dialogo\n                setOpenModificaCavoDialog(false);\n                // Ricarica comunque i dati per assicurarsi che la vista sia aggiornata\n                console.log('Ricaricamento dati dopo errore...');\n                fetchCavi();\n              }}\n              initialOption=\"modificaCavo\"\n            />\n          </Dialog>\n\n          {/* Dialogo per l'aggiunta di un nuovo cavo */}\n          <Dialog\n            open={openAggiungiCavoDialog}\n            onClose={() => setOpenAggiungiCavoDialog(false)}\n            fullWidth\n            maxWidth=\"md\"\n          >\n            <DialogTitle>Aggiungi Nuovo Cavo</DialogTitle>\n            <DialogContent>\n              <Box sx={{ mt: 1 }}>\n                <AggiungiCavoForm\n                  cantiereId={cantiereId}\n                  onSuccess={(message) => {\n                    // Chiudi il dialogo\n                    setOpenAggiungiCavoDialog(false);\n                    // Mostra un messaggio di successo\n                    alert(message);\n                    // Ricarica i dati dopo un ritardo per dare tempo al database di aggiornarsi\n                    setTimeout(() => {\n                      console.log('Ricaricamento dati dopo operazione...');\n                      try {\n                        // Ricarica i dati invece di ricaricare la pagina\n                        fetchCavi();\n                      } catch (error) {\n                        console.error('Errore durante il ricaricamento dei dati:', error);\n                        // Se fallisce, prova a ricaricare la pagina immediatamente\n                        console.log('Tentativo di ricaricamento della pagina...');\n                        window.location.reload();\n                      }\n                    }, 1000);\n                  }}\n                  onError={(message) => {\n                    // Mostra un messaggio di errore\n                    console.error('Errore durante l\\'aggiunta del cavo:', message);\n                    // Mostra un alert all'utente\n                    alert(`Errore: ${message}`);\n                  }}\n                  isDialog={true}\n                />\n              </Box>\n            </DialogContent>\n            <DialogActions>\n              <Button onClick={() => setOpenAggiungiCavoDialog(false)}>Annulla</Button>\n            </DialogActions>\n          </Dialog>\n        </Box>\n      )}\n    </Box>\n  );\n};\n\nexport default VisualizzaCaviPage;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAClD,SACEC,GAAG,EACHC,UAAU,EACVC,KAAK,EACLC,MAAM,EACNC,IAAI,EACJC,IAAI,EACJC,WAAW,EACXC,KAAK,EACLC,UAAU,EACVC,IAAI,EACJC,gBAAgB,EAChBC,cAAc,EACdC,MAAM,EACNC,WAAW,EACXC,aAAa,EACbC,aAAa,QACR,eAAe;AACtB,OAAOC,QAAQ,MAAM,0BAA0B;AAC/C,OAAOC,WAAW,MAAM,6BAA6B;AACrD,SAASC,WAAW,QAAQ,kBAAkB;AAC9C,SAASC,OAAO,QAAQ,2BAA2B;AACnD,SAASC,gBAAgB,QAAQ,6BAA6B;AAC9D,OAAOC,oBAAoB,MAAM,4CAA4C;AAC7E,OAAOC,WAAW,MAAM,4BAA4B;AACpD,OAAOC,gBAAgB,MAAM,wCAAwC;AACrE,SAASC,2BAA2B,QAAQ,6BAA6B;AACzE,OAAOC,mBAAmB,MAAM,2CAA2C;AAC3E,OAAO,gBAAgB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAExB,MAAMC,kBAAkB,GAAGA,CAAA,KAAM;EAAAC,EAAA;EAAA,IAAAC,YAAA,EAAAC,aAAA,EAAAC,aAAA;EAC/B,MAAM;IAAEC,eAAe;IAAEC;EAAK,CAAC,GAAGf,OAAO,CAAC,CAAC;EAC3C,MAAM;IAAEgB,qBAAqB;IAAEC,wBAAwB;IAAEC,sBAAsB;IAAEC,yBAAyB;IAAEC,sBAAsB;IAAEC;EAA0B,CAAC,GAAGpB,gBAAgB,CAAC,CAAC;EACpL,MAAMqB,QAAQ,GAAGvB,WAAW,CAAC,CAAC;EAC9B,MAAM,CAACwB,UAAU,EAAEC,aAAa,CAAC,GAAG7C,QAAQ,CAAC,IAAI,CAAC;EAClD,MAAM,CAAC8C,YAAY,EAAEC,eAAe,CAAC,GAAG/C,QAAQ,CAAC,EAAE,CAAC;EACpD,MAAM,CAACgD,UAAU,EAAEC,aAAa,CAAC,GAAGjD,QAAQ,CAAC,EAAE,CAAC;EAChD,MAAM,CAACkD,SAAS,EAAEC,YAAY,CAAC,GAAGnD,QAAQ,CAAC,EAAE,CAAC;EAC9C,MAAM,CAACoD,OAAO,EAAEC,UAAU,CAAC,GAAGrD,QAAQ,CAAC,IAAI,CAAC;EAC5C,MAAM,CAACsD,KAAK,EAAEC,QAAQ,CAAC,GAAGvD,QAAQ,CAAC,IAAI,CAAC;EACxC;;EAEA;EACA,MAAM,CAACwD,YAAY,EAAEC,eAAe,CAAC,GAAGzD,QAAQ,CAAC,IAAI,CAAC;EACtD,MAAM,CAAC0D,iBAAiB,EAAEC,oBAAoB,CAAC,GAAG3D,QAAQ,CAAC,KAAK,CAAC;;EAEjE;EACA,MAAM,CAAC4D,KAAK,EAAEC,QAAQ,CAAC,GAAG7D,QAAQ,CAAC;IACjC8D,MAAM,EAAE;MAAEC,WAAW,EAAE,CAAC;MAAEC,UAAU,EAAE,CAAC;MAAEC,WAAW,EAAE;IAAE,CAAC;IACzDC,SAAS,EAAE;MAAEC,oBAAoB,EAAE,CAAC;MAAEC,kBAAkB,EAAE,CAAC;MAAEC,yBAAyB,EAAE;IAAE,CAAC;IAC3FC,KAAK,EAAE;EACT,CAAC,CAAC;EACF,MAAM,CAACC,YAAY,EAAEC,eAAe,CAAC,GAAGxE,QAAQ,CAAC,KAAK,CAAC;;EAEvD;;EAEA;EACA,MAAMyE,sBAAsB,GAAGA,CAAA,KAAM;IACnC;IACAC,qBAAqB,CAAC,CAAC,YAAY,EAAE,eAAe,EAAE,UAAU,CAAC,CAAC;EACpE,CAAC;;EAED;EACA,MAAM,CAACC,OAAO,EAAEC,UAAU,CAAC,GAAG5E,QAAQ,CAAC;IACrC6E,mBAAmB,EAAE,EAAE;IACvBC,SAAS,EAAE,EAAE;IACbC,OAAO,EAAE,EAAE;IACXC,UAAU,EAAE;EACd,CAAC,CAAC;;EAEF;EACA,MAAM,CAACC,kBAAkB,EAAEP,qBAAqB,CAAC,GAAG1E,QAAQ,CAAC,EAAE,CAAC;EAChE,MAAM,CAACkF,aAAa,EAAEC,gBAAgB,CAAC,GAAGnF,QAAQ,CAAC,EAAE,CAAC;;EAEtD;;EAEA;EACA,MAAMoF,SAAS,GAAG,MAAAA,CAAA,KAAY;IAC5B,IAAI;MACF/B,UAAU,CAAC,IAAI,CAAC;MAChBgC,OAAO,CAACC,GAAG,CAAC,gCAAgC,EAAE1C,UAAU,CAAC;;MAEzD;MACA,IAAI,CAACA,UAAU,EAAE;QACfyC,OAAO,CAAC/B,KAAK,CAAC,mCAAmC,EAAEV,UAAU,CAAC;QAC9DW,QAAQ,CAAC,wDAAwD,CAAC;QAClEF,UAAU,CAAC,KAAK,CAAC;QACjB;MACF;;MAEA;MACA,IAAIkC,eAAe,GAAG3C,UAAU;MAChC,IAAI,CAAC2C,eAAe,EAAE;QACpBA,eAAe,GAAGC,YAAY,CAACC,OAAO,CAAC,oBAAoB,CAAC;QAC5DJ,OAAO,CAACC,GAAG,CAAC,qCAAqC,EAAEC,eAAe,CAAC;QACnE,IAAI,CAACA,eAAe,EAAE;UACpBF,OAAO,CAAC/B,KAAK,CAAC,2CAA2C,CAAC;UAC1DC,QAAQ,CAAC,8CAA8C,CAAC;UACxDF,UAAU,CAAC,KAAK,CAAC;UACjB;QACF;MACF;;MAEA;MACAgC,OAAO,CAACC,GAAG,CAAC,0CAA0C,CAAC;MACvD,IAAII,MAAM,GAAG,EAAE;MACf,IAAI;QACFA,MAAM,GAAG,MAAMlE,WAAW,CAACmE,OAAO,CAACJ,eAAe,EAAE,CAAC,EAAEZ,OAAO,CAAC;QAC/DU,OAAO,CAACC,GAAG,CAAC,uBAAuB,EAAEI,MAAM,GAAGA,MAAM,CAACE,MAAM,GAAG,CAAC,CAAC;MAClE,CAAC,CAAC,OAAOC,WAAW,EAAE;QACpBR,OAAO,CAAC/B,KAAK,CAAC,yCAAyC,EAAEuC,WAAW,CAAC;QACrE;QACAH,MAAM,GAAG,EAAE;MACb;;MAEA;MACA,IAAIA,MAAM,IAAIA,MAAM,CAACE,MAAM,GAAG,CAAC,EAAE;QAC/B,MAAME,kBAAkB,GAAGJ,MAAM,CAACK,MAAM,CAACC,IAAI,IAAIA,IAAI,CAACC,sBAAsB,KAAK,CAAC,CAAC;QACnF,IAAIH,kBAAkB,CAACF,MAAM,GAAG,CAAC,EAAE;UACjCP,OAAO,CAAC/B,KAAK,CAAC,wEAAwE,EAAEwC,kBAAkB,CAAC;QAC7G;MACF;MAEA7C,aAAa,CAACyC,MAAM,IAAI,EAAE,CAAC;;MAE3B;MACA,IAAIQ,KAAK,GAAG,EAAE;MACd,IAAI;QACFb,OAAO,CAACC,GAAG,CAAC,iDAAiD,CAAC;QAC9DY,KAAK,GAAG,MAAM1E,WAAW,CAAC2E,YAAY,CAACZ,eAAe,CAAC;QACvDF,OAAO,CAACC,GAAG,CAAC,4CAA4C,EAAEY,KAAK,GAAGA,KAAK,CAACN,MAAM,GAAG,CAAC,CAAC;QACnF,IAAIM,KAAK,IAAIA,KAAK,CAACN,MAAM,GAAG,CAAC,EAAE;UAC7BP,OAAO,CAACC,GAAG,CAAC,mBAAmB,EAAEY,KAAK,CAAC,CAAC,CAAC,CAAC;QAC5C;MACF,CAAC,CAAC,OAAOE,UAAU,EAAE;QACnBf,OAAO,CAAC/B,KAAK,CAAC,8DAA8D,EAAE8C,UAAU,CAAC;QACzF;QACA,IAAI;UACFf,OAAO,CAACC,GAAG,CAAC,kCAAkC,CAAC;UAC/CY,KAAK,GAAG,MAAM1E,WAAW,CAACmE,OAAO,CAACJ,eAAe,EAAE,CAAC,CAAC;UACrDF,OAAO,CAACC,GAAG,CAAC,0CAA0C,EAAEY,KAAK,GAAGA,KAAK,CAACN,MAAM,GAAG,CAAC,CAAC;QACnF,CAAC,CAAC,OAAOS,aAAa,EAAE;UACtBhB,OAAO,CAAC/B,KAAK,CAAC,mCAAmC,EAAE+C,aAAa,CAAC;UACjE;UACAH,KAAK,GAAG,EAAE;QACZ;MACF;MACA/C,YAAY,CAAC+C,KAAK,IAAI,EAAE,CAAC;;MAEzB;MACA,IAAI;QACFb,OAAO,CAACC,GAAG,CAAC,4BAA4B,CAAC;QACzC,MAAMgB,SAAS,GAAG,MAAM9E,WAAW,CAAC+E,YAAY,CAAChB,eAAe,CAAC;QACjEF,OAAO,CAACC,GAAG,CAAC,uBAAuB,EAAEgB,SAAS,CAAC;;QAE/C;QACA,IAAI,CAACA,SAAS,IAAI,OAAOA,SAAS,KAAK,QAAQ,EAAE;UAC/CjB,OAAO,CAAC/B,KAAK,CAAC,yBAAyB,EAAEgD,SAAS,CAAC;UACnD;UACAzC,QAAQ,CAAC;YACPC,MAAM,EAAE;cAAEC,WAAW,EAAE,CAAC;cAAEC,UAAU,EAAE,CAAC;cAAEC,WAAW,EAAE;YAAE,CAAC;YACzDC,SAAS,EAAE;cAAEC,oBAAoB,EAAE,CAAC;cAAEC,kBAAkB,EAAE,CAAC;cAAEC,yBAAyB,EAAE;YAAE,CAAC;YAC3FC,KAAK,EAAE;UACT,CAAC,CAAC;QACJ,CAAC,MAAM;UACL;UACA,MAAMkC,UAAU,GAAG;YACjB1C,MAAM,EAAEwC,SAAS,CAACxC,MAAM,IAAI;cAAEC,WAAW,EAAE,CAAC;cAAEC,UAAU,EAAE,CAAC;cAAEC,WAAW,EAAE;YAAE,CAAC;YAC7EC,SAAS,EAAEoC,SAAS,CAACpC,SAAS,IAAI;cAAEC,oBAAoB,EAAE,CAAC;cAAEC,kBAAkB,EAAE,CAAC;cAAEC,yBAAyB,EAAE;YAAE,CAAC;YAClHC,KAAK,EAAEgC,SAAS,CAAChC,KAAK,IAAI;UAC5B,CAAC;UACDT,QAAQ,CAAC2C,UAAU,CAAC;QACtB;MACF,CAAC,CAAC,OAAOC,UAAU,EAAE;QACnBpB,OAAO,CAAC/B,KAAK,CAAC,2CAA2C,EAAEmD,UAAU,CAAC;QACtE;QACA5C,QAAQ,CAAC;UACPC,MAAM,EAAE;YAAEC,WAAW,EAAE,CAAC;YAAEC,UAAU,EAAE,CAAC;YAAEC,WAAW,EAAE;UAAE,CAAC;UACzDC,SAAS,EAAE;YAAEC,oBAAoB,EAAE,CAAC;YAAEC,kBAAkB,EAAE,CAAC;YAAEC,yBAAyB,EAAE;UAAE,CAAC;UAC3FC,KAAK,EAAE;QACT,CAAC,CAAC;MACJ;;MAEA;MACAf,QAAQ,CAAC,EAAE,CAAC;IACd,CAAC,CAAC,OAAOD,KAAK,EAAE;MACd+B,OAAO,CAAC/B,KAAK,CAAC,2CAA2C,EAAEA,KAAK,CAAC;MACjEC,QAAQ,CAAC,oCAAoCD,KAAK,CAACoD,OAAO,IAAI,oBAAoB,EAAE,CAAC;;MAErF;MACAC,UAAU,CAAC,MAAM;QACf;QACA,IAAIC,QAAQ,CAACC,IAAI,CAACC,WAAW,CAACC,QAAQ,CAAC,iCAAiC,CAAC,EAAE;UACzE1B,OAAO,CAACC,GAAG,CAAC,gEAAgE,CAAC;UAC7E0B,MAAM,CAACC,QAAQ,CAACC,MAAM,CAAC,CAAC;QAC1B;MACF,CAAC,EAAE,IAAI,CAAC,CAAC,CAAC;IACZ,CAAC,SAAS;MACR7D,UAAU,CAAC,KAAK,CAAC;IACnB;EACF,CAAC;;EAED;EACApD,SAAS,CAAC,MAAM;IACd;IACAwE,sBAAsB,CAAC,CAAC;IAExB,MAAM0C,SAAS,GAAG,MAAAA,CAAA,KAAY;MAC5B,IAAI;QACF9B,OAAO,CAACC,GAAG,CAAC,wCAAwC,CAAC;;QAErD;QACA,MAAM8B,KAAK,GAAG5B,YAAY,CAACC,OAAO,CAAC,OAAO,CAAC;QAC3CJ,OAAO,CAACC,GAAG,CAAC,iBAAiB,EAAE,CAAC,CAAC8B,KAAK,CAAC;QACvC,IAAI,CAACA,KAAK,EAAE;UACV7D,QAAQ,CAAC,iDAAiD,CAAC;UAC3DF,UAAU,CAAC,KAAK,CAAC;UACjB;QACF;;QAEA;QACA,IAAIgE,kBAAkB,GAAG7B,YAAY,CAACC,OAAO,CAAC,oBAAoB,CAAC;QACnE,IAAI6B,oBAAoB,GAAG9B,YAAY,CAACC,OAAO,CAAC,sBAAsB,CAAC;QAEvEJ,OAAO,CAACC,GAAG,CAAC,wCAAwC,EAAE;UAAE+B,kBAAkB;UAAEC;QAAqB,CAAC,CAAC;QACnGjC,OAAO,CAACC,GAAG,CAAC,cAAc,EAAElD,IAAI,CAAC;;QAEjC;QACAiD,OAAO,CAACC,GAAG,CAAC,wCAAwC,CAAC;QACrD,KAAK,IAAIiC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG/B,YAAY,CAACI,MAAM,EAAE2B,CAAC,EAAE,EAAE;UAC5C,MAAMC,GAAG,GAAGhC,YAAY,CAACgC,GAAG,CAACD,CAAC,CAAC;UAC/BlC,OAAO,CAACC,GAAG,CAAC,GAAGkC,GAAG,KAAKhC,YAAY,CAACC,OAAO,CAAC+B,GAAG,CAAC,EAAE,CAAC;QACrD;;QAEA;QACA,IAAI,CAAApF,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAEqF,IAAI,MAAK,eAAe,EAAE;UAClCpC,OAAO,CAACC,GAAG,CAAC,6EAA6E,CAAC;;UAE1F;UACA,IAAIlD,IAAI,CAACsF,WAAW,EAAE;YACpBrC,OAAO,CAACC,GAAG,CAAC,sCAAsC,EAAElD,IAAI,CAACsF,WAAW,CAAC;YACrEL,kBAAkB,GAAGjF,IAAI,CAACsF,WAAW,CAACC,QAAQ,CAAC,CAAC;YAChDL,oBAAoB,GAAGlF,IAAI,CAACwF,aAAa,IAAI,YAAYxF,IAAI,CAACsF,WAAW,EAAE;;YAE3E;YACAlC,YAAY,CAACqC,OAAO,CAAC,oBAAoB,EAAER,kBAAkB,CAAC;YAC9D7B,YAAY,CAACqC,OAAO,CAAC,sBAAsB,EAAEP,oBAAoB,CAAC;YAClEjC,OAAO,CAACC,GAAG,CAAC,uCAAuC,EAAE+B,kBAAkB,CAAC;UAC1E,CAAC,MAAM;YACL;YACA,IAAI;cACFhC,OAAO,CAACC,GAAG,CAAC,qEAAqE,CAAC;cAClF,MAAM8B,KAAK,GAAG5B,YAAY,CAACC,OAAO,CAAC,OAAO,CAAC;cAC3C,IAAI2B,KAAK,EAAE;gBACT;gBACA,MAAMU,SAAS,GAAGV,KAAK,CAACW,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC;gBACrC,MAAMC,MAAM,GAAGF,SAAS,CAACG,OAAO,CAAC,IAAI,EAAE,GAAG,CAAC,CAACA,OAAO,CAAC,IAAI,EAAE,GAAG,CAAC;gBAC9D,MAAMC,WAAW,GAAGC,kBAAkB,CAACC,IAAI,CAACJ,MAAM,CAAC,CAACD,KAAK,CAAC,EAAE,CAAC,CAACM,GAAG,CAACC,CAAC,IAAI;kBACrE,OAAO,GAAG,GAAG,CAAC,IAAI,GAAGA,CAAC,CAACC,UAAU,CAAC,CAAC,CAAC,CAACZ,QAAQ,CAAC,EAAE,CAAC,EAAEa,KAAK,CAAC,CAAC,CAAC,CAAC;gBAC9D,CAAC,CAAC,CAACC,IAAI,CAAC,EAAE,CAAC,CAAC;gBAEZ,MAAMC,OAAO,GAAGC,IAAI,CAACC,KAAK,CAACV,WAAW,CAAC;gBACvC7C,OAAO,CAACC,GAAG,CAAC,wBAAwB,EAAEoD,OAAO,CAAC;gBAE9C,IAAIA,OAAO,CAAChB,WAAW,EAAE;kBACvBrC,OAAO,CAACC,GAAG,CAAC,oCAAoC,EAAEoD,OAAO,CAAChB,WAAW,CAAC;kBACtEL,kBAAkB,GAAGqB,OAAO,CAAChB,WAAW,CAACC,QAAQ,CAAC,CAAC;kBACnD;kBACAL,oBAAoB,GAAG,YAAYoB,OAAO,CAAChB,WAAW,EAAE;;kBAExD;kBACAlC,YAAY,CAACqC,OAAO,CAAC,oBAAoB,EAAER,kBAAkB,CAAC;kBAC9D7B,YAAY,CAACqC,OAAO,CAAC,sBAAsB,EAAEP,oBAAoB,CAAC;kBAClEjC,OAAO,CAACC,GAAG,CAAC,uCAAuC,EAAE+B,kBAAkB,CAAC;gBAC1E;cACF;YACF,CAAC,CAAC,OAAOwB,CAAC,EAAE;cACVxD,OAAO,CAAC/B,KAAK,CAAC,6CAA6C,EAAEuF,CAAC,CAAC;YACjE;UACF;QACF;;QAEA;QACA,IAAI,CAACxB,kBAAkB,IAAIA,kBAAkB,KAAK,WAAW,IAAIA,kBAAkB,KAAK,MAAM,EAAE;UAC9FhC,OAAO,CAACyD,IAAI,CAAC,6EAA6E,CAAC;UAC3F;UACAzB,kBAAkB,GAAG,GAAG,CAAC,CAAC;UAC1BC,oBAAoB,GAAG,gBAAgB;;UAEvC;UACA9B,YAAY,CAACqC,OAAO,CAAC,oBAAoB,EAAER,kBAAkB,CAAC;UAC9D7B,YAAY,CAACqC,OAAO,CAAC,sBAAsB,EAAEP,oBAAoB,CAAC;UAClEjC,OAAO,CAACC,GAAG,CAAC,iDAAiD,EAAE+B,kBAAkB,CAAC;QACpF;;QAEA;QACA,IAAI,CAACA,kBAAkB,EAAE;UACvB9D,QAAQ,CAAC,8DAA8D,CAAC;UACxEF,UAAU,CAAC,KAAK,CAAC;UACjB;QACF;;QAEA;QACA,MAAM0F,aAAa,GAAGC,QAAQ,CAAC3B,kBAAkB,EAAE,EAAE,CAAC;QACtDhC,OAAO,CAACC,GAAG,CAAC,kCAAkC,EAAEyD,aAAa,CAAC;QAC9D,IAAIE,KAAK,CAACF,aAAa,CAAC,EAAE;UACxBxF,QAAQ,CAAC,2BAA2B8D,kBAAkB,mCAAmC,CAAC;UAC1FhE,UAAU,CAAC,KAAK,CAAC;UACjB;QACF;;QAEA;QACAR,aAAa,CAACkG,aAAa,CAAC;QAC5BhG,eAAe,CAACuE,oBAAoB,IAAI,YAAYyB,aAAa,EAAE,CAAC;;QAEpE;QACA,IAAI;UACFvE,eAAe,CAAC,IAAI,CAAC;UACrBa,OAAO,CAACC,GAAG,CAAC,4CAA4C,EAAEyD,aAAa,CAAC;UACxE,MAAMzC,SAAS,GAAG,MAAM9E,WAAW,CAAC+E,YAAY,CAACwC,aAAa,CAAC;UAC/D1D,OAAO,CAACC,GAAG,CAAC,4BAA4B,EAAEgB,SAAS,CAAC;UACpDzC,QAAQ,CAACyC,SAAS,CAAC;;UAEnB;UACA,IAAIA,SAAS,IAAIA,SAAS,CAAChC,KAAK,EAAE;YAChC,MAAMA,KAAK,GAAGgC,SAAS,CAAChC,KAAK,CAAC+D,GAAG,CAACa,IAAI,IAAIA,IAAI,CAACC,KAAK,CAAC,CAACpD,MAAM,CAACoD,KAAK,IAAIA,KAAK,KAAK,iBAAiB,CAAC;YAClGzE,qBAAqB,CAACJ,KAAK,CAAC;UAC9B;UAEA,IAAIgC,SAAS,IAAIA,SAAS,CAAC8C,SAAS,EAAE;YACpC,MAAMA,SAAS,GAAG9C,SAAS,CAAC8C,SAAS,CAACf,GAAG,CAACa,IAAI,IAAIA,IAAI,CAACpE,SAAS,CAAC,CAACiB,MAAM,CAACsD,IAAI,IAAIA,IAAI,KAAK,iBAAiB,CAAC;YAC5GlE,gBAAgB,CAACiE,SAAS,CAAC;UAC7B;UAEA5E,eAAe,CAAC,KAAK,CAAC;QACxB,CAAC,CAAC,OAAOiC,UAAU,EAAE;UACnBpB,OAAO,CAAC/B,KAAK,CAAC,2CAA2C,EAAEmD,UAAU,CAAC;UACtEjC,eAAe,CAAC,KAAK,CAAC;UACtB;QACF;;QAEA;QACAa,OAAO,CAACC,GAAG,CAAC,uCAAuC,EAAEyD,aAAa,CAAC;QACnE,IAAI;UACF;UACA,MAAMO,cAAc,GAAG,IAAIC,OAAO,CAAC,CAACC,CAAC,EAAEC,MAAM,KAAK;YAChD9C,UAAU,CAAC,MAAM8C,MAAM,CAAC,IAAIC,KAAK,CAAC,gDAAgD,CAAC,CAAC,EAAE,KAAK,CAAC,CAAC,CAAC;UAChG,CAAC,CAAC;;UAEF;UACArE,OAAO,CAACC,GAAG,CAAC,oDAAoD,EAAEX,OAAO,CAAC;UAC1E,MAAMgF,WAAW,GAAGnI,WAAW,CAACmE,OAAO,CAACoD,aAAa,EAAE,CAAC,EAAEpE,OAAO,CAAC;UAClE,MAAMe,MAAM,GAAG,MAAM6D,OAAO,CAACK,IAAI,CAAC,CAACD,WAAW,EAAEL,cAAc,CAAC,CAAC;UAEhEjE,OAAO,CAACC,GAAG,CAAC,uBAAuB,EAAEI,MAAM,CAAC;UAC5CL,OAAO,CAACC,GAAG,CAAC,gCAAgC,EAAEI,MAAM,GAAGA,MAAM,CAACE,MAAM,GAAG,CAAC,CAAC;UACzE,IAAIF,MAAM,IAAIA,MAAM,CAACE,MAAM,GAAG,CAAC,EAAE;YAC/BP,OAAO,CAACC,GAAG,CAAC,oBAAoB,EAAEI,MAAM,CAAC,CAAC,CAAC,CAAC;UAC9C,CAAC,MAAM;YACLL,OAAO,CAACyD,IAAI,CAAC,4CAA4C,EAAEC,aAAa,CAAC;UAC3E;UACA9F,aAAa,CAACyC,MAAM,IAAI,EAAE,CAAC;QAC7B,CAAC,CAAC,OAAOmE,SAAS,EAAE;UAClBxE,OAAO,CAAC/B,KAAK,CAAC,yCAAyC,EAAEuG,SAAS,CAAC;UACnExE,OAAO,CAAC/B,KAAK,CAAC,8BAA8B,EAAE;YAC5CoD,OAAO,EAAEmD,SAAS,CAACnD,OAAO;YAC1BoD,MAAM,EAAED,SAAS,CAACC,MAAM;YACxBC,IAAI,EAAEF,SAAS,CAACE,IAAI;YACpBC,KAAK,EAAEH,SAAS,CAACG,KAAK;YACtBC,IAAI,EAAEJ,SAAS,CAACI,IAAI;YACpBC,IAAI,EAAEL,SAAS,CAACK,IAAI;YACpBC,QAAQ,EAAEN,SAAS,CAACM,QAAQ,GAAG;cAC7BL,MAAM,EAAED,SAAS,CAACM,QAAQ,CAACL,MAAM;cACjCM,UAAU,EAAEP,SAAS,CAACM,QAAQ,CAACC,UAAU;cACzCL,IAAI,EAAEF,SAAS,CAACM,QAAQ,CAACJ;YAC3B,CAAC,GAAG;UACN,CAAC,CAAC;;UAEF;UACA9G,aAAa,CAAC,EAAE,CAAC;UACjBoC,OAAO,CAACyD,IAAI,CAAC,sDAAsD,CAAC;;UAEpE;UACAvF,QAAQ,CAAC,2CAA2CsG,SAAS,CAACnD,OAAO,+CAA+C,CAAC;QACvH;;QAEA;QACArB,OAAO,CAACC,GAAG,CAAC,sCAAsC,EAAEyD,aAAa,CAAC;QAClE,IAAI;UACF;UACA,MAAMO,cAAc,GAAG,IAAIC,OAAO,CAAC,CAACC,CAAC,EAAEC,MAAM,KAAK;YAChD9C,UAAU,CAAC,MAAM8C,MAAM,CAAC,IAAIC,KAAK,CAAC,+CAA+C,CAAC,CAAC,EAAE,KAAK,CAAC,CAAC,CAAC;UAC/F,CAAC,CAAC;;UAEF;UACArE,OAAO,CAACC,GAAG,CAAC,0CAA0C,CAAC;UACvD;UACA,MAAM+E,YAAY,GAAG7I,WAAW,CAACmE,OAAO,CAACoD,aAAa,EAAE,CAAC,CAAC;UAC1D,MAAM7C,KAAK,GAAG,MAAMqD,OAAO,CAACK,IAAI,CAAC,CAACS,YAAY,EAAEf,cAAc,CAAC,CAAC;UAEhEjE,OAAO,CAACC,GAAG,CAAC,sBAAsB,EAAEY,KAAK,CAAC;UAC1Cb,OAAO,CAACC,GAAG,CAAC,+BAA+B,EAAEY,KAAK,GAAGA,KAAK,CAACN,MAAM,GAAG,CAAC,CAAC;UACtE,IAAIM,KAAK,IAAIA,KAAK,CAACN,MAAM,GAAG,CAAC,EAAE;YAC7BP,OAAO,CAACC,GAAG,CAAC,mBAAmB,EAAEY,KAAK,CAAC,CAAC,CAAC,CAAC;UAC5C,CAAC,MAAM;YACLb,OAAO,CAACyD,IAAI,CAAC,2CAA2C,EAAEC,aAAa,CAAC;UAC1E;UACA5F,YAAY,CAAC+C,KAAK,IAAI,EAAE,CAAC;QAC3B,CAAC,CAAC,OAAOE,UAAU,EAAE;UACnBf,OAAO,CAAC/B,KAAK,CAAC,wCAAwC,EAAE8C,UAAU,CAAC;UACnEf,OAAO,CAAC/B,KAAK,CAAC,6BAA6B,EAAE;YAC3CoD,OAAO,EAAEN,UAAU,CAACM,OAAO;YAC3BoD,MAAM,EAAE1D,UAAU,CAAC0D,MAAM;YACzBC,IAAI,EAAE3D,UAAU,CAAC2D,IAAI;YACrBC,KAAK,EAAE5D,UAAU,CAAC4D,KAAK;YACvBC,IAAI,EAAE7D,UAAU,CAAC6D,IAAI;YACrBC,IAAI,EAAE9D,UAAU,CAAC8D,IAAI;YACrBC,QAAQ,EAAE/D,UAAU,CAAC+D,QAAQ,GAAG;cAC9BL,MAAM,EAAE1D,UAAU,CAAC+D,QAAQ,CAACL,MAAM;cAClCM,UAAU,EAAEhE,UAAU,CAAC+D,QAAQ,CAACC,UAAU;cAC1CL,IAAI,EAAE3D,UAAU,CAAC+D,QAAQ,CAACJ;YAC5B,CAAC,GAAG;UACN,CAAC,CAAC;;UAEF;UACA5G,YAAY,CAAC,EAAE,CAAC;;UAEhB;UACA,IAAI,CAACG,KAAK,EAAE;YACVC,QAAQ,CAAC,0CAA0C6C,UAAU,CAACM,OAAO,+CAA+C,CAAC;UACvH;QACF;;QAEA;QACArD,UAAU,CAAC,KAAK,CAAC;MAEnB,CAAC,CAAC,OAAOiH,GAAG,EAAE;QAAA,IAAAC,aAAA,EAAAC,cAAA,EAAAC,cAAA,EAAAC,cAAA,EAAAC,cAAA,EAAAC,mBAAA;QACZvF,OAAO,CAAC/B,KAAK,CAAC,kCAAkC,EAAEgH,GAAG,CAAC;QACtDjF,OAAO,CAAC/B,KAAK,CAAC,2BAA2B,EAAE;UACzCoD,OAAO,EAAE4D,GAAG,CAAC5D,OAAO;UACpBoD,MAAM,EAAEQ,GAAG,CAACR,MAAM,MAAAS,aAAA,GAAID,GAAG,CAACH,QAAQ,cAAAI,aAAA,uBAAZA,aAAA,CAAcT,MAAM;UAC1CC,IAAI,EAAEO,GAAG,CAACP,IAAI,MAAAS,cAAA,GAAIF,GAAG,CAACH,QAAQ,cAAAK,cAAA,uBAAZA,cAAA,CAAcT,IAAI;UACpCC,KAAK,EAAEM,GAAG,CAACN;QACb,CAAC,CAAC;;QAEF;QACA,IAAIa,YAAY,GAAG,oBAAoB;QAEvC,IAAIP,GAAG,CAAC5D,OAAO,IAAI4D,GAAG,CAAC5D,OAAO,CAACK,QAAQ,CAAC,wBAAwB,CAAC,EAAE;UACjE8D,YAAY,GAAGP,GAAG,CAAC5D,OAAO;QAC5B,CAAC,MAAM,IAAI4D,GAAG,CAACR,MAAM,KAAK,GAAG,IAAIQ,GAAG,CAACR,MAAM,KAAK,GAAG,IACzC,EAAAW,cAAA,GAAAH,GAAG,CAACH,QAAQ,cAAAM,cAAA,uBAAZA,cAAA,CAAcX,MAAM,MAAK,GAAG,IAAI,EAAAY,cAAA,GAAAJ,GAAG,CAACH,QAAQ,cAAAO,cAAA,uBAAZA,cAAA,CAAcZ,MAAM,MAAK,GAAG,EAAE;UACtEe,YAAY,GAAG,mEAAmE;QACpF,CAAC,MAAM,KAAAF,cAAA,GAAIL,GAAG,CAACH,QAAQ,cAAAQ,cAAA,gBAAAC,mBAAA,GAAZD,cAAA,CAAcZ,IAAI,cAAAa,mBAAA,eAAlBA,mBAAA,CAAoBE,MAAM,EAAE;UACrC;UACAD,YAAY,GAAG,eAAeP,GAAG,CAACH,QAAQ,CAACJ,IAAI,CAACe,MAAM,EAAE;QAC1D,CAAC,MAAM,IAAIR,GAAG,CAACL,IAAI,KAAK,aAAa,EAAE;UACrC;UACAY,YAAY,GAAG,yEAAyE;QAC1F,CAAC,MAAM,IAAIP,GAAG,CAAC5D,OAAO,EAAE;UACtBmE,YAAY,GAAGP,GAAG,CAAC5D,OAAO;QAC5B;QAEAnD,QAAQ,CAAC,gCAAgCsH,YAAY,sBAAsB,CAAC;;QAE5E;QACA5H,aAAa,CAAC,EAAE,CAAC;QACjBE,YAAY,CAAC,EAAE,CAAC;MAClB,CAAC,SAAS;QACRE,UAAU,CAAC,KAAK,CAAC;MACnB;IACF,CAAC;IAED8D,SAAS,CAAC,CAAC;EACb,CAAC,EAAE,CAACxC,OAAO,CAAC,CAAC,CAAC,CAAC;;EAEf;;EAEA;EACA,MAAMoG,iBAAiB,GAAI/E,IAAI,IAAK;IAClCvC,eAAe,CAACuC,IAAI,CAAC;IACrBrC,oBAAoB,CAAC,IAAI,CAAC;EAC5B,CAAC;;EAED;EACA,MAAMqH,kBAAkB,GAAGA,CAAA,KAAM;IAC/BrH,oBAAoB,CAAC,KAAK,CAAC;IAC3BF,eAAe,CAAC,IAAI,CAAC;EACvB,CAAC;;EAED;;EAEA;;EAEA;;EAEA;EACA,MAAMwH,mBAAmB,GAAGA,CAAA,KAAM;IAChC,IAAI,CAACzH,YAAY,EAAE,OAAO,IAAI;IAE9B,oBACE3B,OAAA,CAACf,MAAM;MAACoK,IAAI,EAAExH,iBAAkB;MAACyH,OAAO,EAAEH,kBAAmB;MAACI,QAAQ,EAAC,IAAI;MAACC,SAAS;MAAAC,QAAA,gBACnFzJ,OAAA,CAACd,WAAW;QAAAuK,QAAA,GAAC,iBACI,EAAC9H,YAAY,CAAC+H,OAAO;MAAA;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACzB,CAAC,eACd9J,OAAA,CAACb,aAAa;QAAC4K,QAAQ;QAAAN,QAAA,eACrBzJ,OAAA,CAACvB,IAAI;UAACuL,SAAS;UAACC,OAAO,EAAE,CAAE;UAAAR,QAAA,gBACzBzJ,OAAA,CAACvB,IAAI;YAAC4I,IAAI;YAAC6C,EAAE,EAAE,EAAG;YAACC,EAAE,EAAE,CAAE;YAAAV,QAAA,gBACvBzJ,OAAA,CAAC1B,UAAU;cAAC8L,OAAO,EAAC,WAAW;cAACC,YAAY;cAAAZ,QAAA,EAAC;YAAqB;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAY,CAAC,eAC/E9J,OAAA,CAAC3B,GAAG;cAACiM,EAAE,EAAE;gBAAEC,EAAE,EAAE;cAAE,CAAE;cAAAd,QAAA,gBACjBzJ,OAAA,CAAC1B,UAAU;gBAAC8L,OAAO,EAAC,OAAO;gBAAAX,QAAA,gBAACzJ,OAAA;kBAAAyJ,QAAA,EAAQ;gBAAQ;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,KAAC,EAACnI,YAAY,CAAC6I,OAAO,IAAI,KAAK;cAAA;gBAAAb,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAa,CAAC,eAClG9J,OAAA,CAAC1B,UAAU;gBAAC8L,OAAO,EAAC,OAAO;gBAAAX,QAAA,gBAACzJ,OAAA;kBAAAyJ,QAAA,EAAQ;gBAAQ;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,KAAC,EAACnI,YAAY,CAAC8I,OAAO,IAAI,KAAK;cAAA;gBAAAd,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAa,CAAC,eAClG9J,OAAA,CAAC1B,UAAU;gBAAC8L,OAAO,EAAC,OAAO;gBAAAX,QAAA,gBAACzJ,OAAA;kBAAAyJ,QAAA,EAAQ;gBAAU;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,KAAC,EAACnI,YAAY,CAACsB,SAAS,IAAI,KAAK;cAAA;gBAAA0G,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAa,CAAC,eACtG9J,OAAA,CAAC1B,UAAU;gBAAC8L,OAAO,EAAC,OAAO;gBAAAX,QAAA,gBAACzJ,OAAA;kBAAAyJ,QAAA,EAAQ;gBAAO;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,KAAC,EAACnI,YAAY,CAAC+I,WAAW,IAAI,KAAK;cAAA;gBAAAf,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAa,CAAC,eACrG9J,OAAA,CAAC1B,UAAU;gBAAC8L,OAAO,EAAC,OAAO;gBAAAX,QAAA,gBAACzJ,OAAA;kBAAAyJ,QAAA,EAAQ;gBAAc;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,KAAC,EAACnI,YAAY,CAACgJ,YAAY,IAAI,KAAK;cAAA;gBAAAhB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAa,CAAC,eAC7G9J,OAAA,CAAC1B,UAAU;gBAAC8L,OAAO,EAAC,OAAO;gBAAAX,QAAA,gBAACzJ,OAAA;kBAAAyJ,QAAA,EAAQ;gBAAQ;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,KAAC,EAACnI,YAAY,CAACiJ,OAAO,IAAI,KAAK;cAAA;gBAAAjB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAa,CAAC,eAClG9J,OAAA,CAAC1B,UAAU;gBAAC8L,OAAO,EAAC,OAAO;gBAAAX,QAAA,gBAACzJ,OAAA;kBAAAyJ,QAAA,EAAQ;gBAAG;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,KAAC,EAACnI,YAAY,CAACkJ,EAAE,IAAI,KAAK;cAAA;gBAAAlB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAa,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACrF,CAAC,eAEN9J,OAAA,CAAC1B,UAAU;cAAC8L,OAAO,EAAC,WAAW;cAACC,YAAY;cAAAZ,QAAA,EAAC;YAAQ;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAY,CAAC,eAClE9J,OAAA,CAAC3B,GAAG;cAACiM,EAAE,EAAE;gBAAEC,EAAE,EAAE;cAAE,CAAE;cAAAd,QAAA,gBACjBzJ,OAAA,CAAC1B,UAAU;gBAAC8L,OAAO,EAAC,OAAO;gBAAAX,QAAA,gBAACzJ,OAAA;kBAAAyJ,QAAA,EAAQ;gBAAW;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,KAAC,EAACnI,YAAY,CAACmJ,mBAAmB,IAAI,KAAK;cAAA;gBAAAnB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAa,CAAC,eACjH9J,OAAA,CAAC1B,UAAU;gBAAC8L,OAAO,EAAC,OAAO;gBAAAX,QAAA,gBAACzJ,OAAA;kBAAAyJ,QAAA,EAAQ;gBAAO;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,KAAC,EAACnI,YAAY,CAACoJ,eAAe,IAAI,KAAK;cAAA;gBAAApB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAa,CAAC,eACzG9J,OAAA,CAAC1B,UAAU;gBAAC8L,OAAO,EAAC,OAAO;gBAAAX,QAAA,gBAACzJ,OAAA;kBAAAyJ,QAAA,EAAQ;gBAAY;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,KAAC,EAACnI,YAAY,CAACqJ,2BAA2B,IAAI,KAAK;cAAA;gBAAArB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAa,CAAC,eAC1H9J,OAAA,CAAC1B,UAAU;gBAAC8L,OAAO,EAAC,OAAO;gBAAAX,QAAA,gBAACzJ,OAAA;kBAAAyJ,QAAA,EAAQ;gBAAa;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,KAAC,EAACnI,YAAY,CAACsJ,qBAAqB,IAAI,KAAK;cAAA;gBAAAtB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAa,CAAC,eACrH9J,OAAA,CAAC1B,UAAU;gBAAC8L,OAAO,EAAC,OAAO;gBAAAX,QAAA,gBAACzJ,OAAA;kBAAAyJ,QAAA,EAAQ;gBAAQ;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,KAAC,EAACnI,YAAY,CAACuJ,gBAAgB,IAAI,KAAK;cAAA;gBAAAvB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAa,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACxG,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACF,CAAC,eAEP9J,OAAA,CAACvB,IAAI;YAAC4I,IAAI;YAAC6C,EAAE,EAAE,EAAG;YAACC,EAAE,EAAE,CAAE;YAAAV,QAAA,gBACvBzJ,OAAA,CAAC1B,UAAU;cAAC8L,OAAO,EAAC,WAAW;cAACC,YAAY;cAAAZ,QAAA,EAAC;YAAM;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAY,CAAC,eAChE9J,OAAA,CAAC3B,GAAG;cAACiM,EAAE,EAAE;gBAAEC,EAAE,EAAE;cAAE,CAAE;cAAAd,QAAA,gBACjBzJ,OAAA,CAAC1B,UAAU;gBAAC8L,OAAO,EAAC,OAAO;gBAAAX,QAAA,gBAACzJ,OAAA;kBAAAyJ,QAAA,EAAQ;gBAAW;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,KAAC,EAACnI,YAAY,CAACwJ,iBAAiB,IAAI,KAAK;cAAA;gBAAAxB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAa,CAAC,eAC/G9J,OAAA,CAAC1B,UAAU;gBAAC8L,OAAO,EAAC,OAAO;gBAAAX,QAAA,gBAACzJ,OAAA;kBAAAyJ,QAAA,EAAQ;gBAAO;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,KAAC,EAACnI,YAAY,CAACyJ,aAAa,IAAI,KAAK;cAAA;gBAAAzB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAa,CAAC,eACvG9J,OAAA,CAAC1B,UAAU;gBAAC8L,OAAO,EAAC,OAAO;gBAAAX,QAAA,gBAACzJ,OAAA;kBAAAyJ,QAAA,EAAQ;gBAAY;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,KAAC,EAACnI,YAAY,CAAC0J,yBAAyB,IAAI,KAAK;cAAA;gBAAA1B,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAa,CAAC,eACxH9J,OAAA,CAAC1B,UAAU;gBAAC8L,OAAO,EAAC,OAAO;gBAAAX,QAAA,gBAACzJ,OAAA;kBAAAyJ,QAAA,EAAQ;gBAAa;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,KAAC,EAACnI,YAAY,CAAC2J,mBAAmB,IAAI,KAAK;cAAA;gBAAA3B,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAa,CAAC,eACnH9J,OAAA,CAAC1B,UAAU;gBAAC8L,OAAO,EAAC,OAAO;gBAAAX,QAAA,gBAACzJ,OAAA;kBAAAyJ,QAAA,EAAQ;gBAAQ;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,KAAC,EAACnI,YAAY,CAAC4J,cAAc,IAAI,KAAK;cAAA;gBAAA5B,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAa,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACtG,CAAC,eAEN9J,OAAA,CAAC1B,UAAU;cAAC8L,OAAO,EAAC,WAAW;cAACC,YAAY;cAAAZ,QAAA,EAAC;YAAa;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAY,CAAC,eACvE9J,OAAA,CAAC3B,GAAG;cAACiM,EAAE,EAAE;gBAAEC,EAAE,EAAE;cAAE,CAAE;cAAAd,QAAA,gBACjBzJ,OAAA,CAAC1B,UAAU;gBAAC8L,OAAO,EAAC,OAAO;gBAAAX,QAAA,gBAACzJ,OAAA;kBAAAyJ,QAAA,EAAQ;gBAAc;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,KAAC,EAACnI,YAAY,CAAC6J,aAAa,IAAI,KAAK;cAAA;gBAAA7B,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAa,CAAC,eAC9G9J,OAAA,CAAC1B,UAAU;gBAAC8L,OAAO,EAAC,OAAO;gBAAAX,QAAA,gBAACzJ,OAAA;kBAAAyJ,QAAA,EAAQ;gBAAgB;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,KAAC,EAACnI,YAAY,CAAC8J,eAAe,IAAI,GAAG;cAAA;gBAAA9B,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAa,CAAC,eAChH9J,OAAA,CAAC1B,UAAU;gBAAC8L,OAAO,EAAC,OAAO;gBAAAX,QAAA,gBAACzJ,OAAA;kBAAAyJ,QAAA,EAAQ;gBAAM;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,KAAC,EAACjK,2BAA2B,CAAC8B,YAAY,CAACqB,mBAAmB,CAAC;cAAA;gBAAA2G,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAa,CAAC,eAChI9J,OAAA,CAAC1B,UAAU;gBAAC8L,OAAO,EAAC,OAAO;gBAAAX,QAAA,gBAACzJ,OAAA;kBAAAyJ,QAAA,EAAQ;gBAAa;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,KAAC,EAACnI,YAAY,CAAC+J,YAAY,IAAI,GAAG;cAAA;gBAAA/B,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAa,CAAC,eAC1G9J,OAAA,CAAC1B,UAAU;gBAAC8L,OAAO,EAAC,OAAO;gBAAAX,QAAA,gBAACzJ,OAAA;kBAAAyJ,QAAA,EAAQ;gBAAO;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,KAAC,EAACnI,YAAY,CAACgK,SAAS,IAAI,KAAK;cAAA;gBAAAhC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAa,CAAC,eACnG9J,OAAA,CAAC1B,UAAU;gBAAC8L,OAAO,EAAC,OAAO;gBAAAX,QAAA,gBAACzJ,OAAA;kBAAAyJ,QAAA,EAAQ;gBAAkB;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,KAAC,EAACnI,YAAY,CAACiK,iBAAiB,IAAI,KAAK;cAAA;gBAAAjC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAa,CAAC,eACtH9J,OAAA,CAAC1B,UAAU;gBAAC8L,OAAO,EAAC,OAAO;gBAAAX,QAAA,gBAACzJ,OAAA;kBAAAyJ,QAAA,EAAQ;gBAAa;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,KAAC,EAACnI,YAAY,CAACkK,YAAY,IAAI,KAAK;cAAA;gBAAAlC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAa,CAAC,eAC5G9J,OAAA,CAAC1B,UAAU;gBAAC8L,OAAO,EAAC,OAAO;gBAAAX,QAAA,gBAACzJ,OAAA;kBAAAyJ,QAAA,EAAQ;gBAAqB;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,KAAC,EAAC,IAAIgC,IAAI,CAACnK,YAAY,CAACoK,SAAS,CAAC,CAACC,cAAc,CAAC,CAAC;cAAA;gBAAArC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAa,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAChI,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACF,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACM,CAAC,eAChB9J,OAAA,CAACZ,aAAa;QAAAqK,QAAA,eACZzJ,OAAA,CAACxB,MAAM;UAACyN,OAAO,EAAE9C,kBAAmB;UAAAM,QAAA,EAAC;QAAM;UAAAE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACvC,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACV,CAAC;EAEb,CAAC;;EAED;;EAEA;EACA,MAAMoC,gBAAgB,GAAGA,CAAA,KAAM;IAC7B;IACA,IAAI,CAACnK,KAAK,IAAI,CAACA,KAAK,CAACE,MAAM,IAAI,CAACF,KAAK,CAACM,SAAS,IAAI,CAACN,KAAK,CAACU,KAAK,EAAE;MAC/D,oBACEzC,OAAA,CAACzB,KAAK;QAAC+L,EAAE,EAAE;UAAEC,EAAE,EAAE,CAAC;UAAE4B,CAAC,EAAE;QAAE,CAAE;QAAA1C,QAAA,gBACzBzJ,OAAA,CAAC1B,UAAU;UAAC8L,OAAO,EAAC,IAAI;UAACC,YAAY;UAAAZ,QAAA,EAAC;QAAW;UAAAE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAY,CAAC,EAC7DpH,YAAY,gBACX1C,OAAA,CAAChB,cAAc;UAAA2K,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,gBAElB9J,OAAA,CAAC1B,UAAU;UAAC8L,OAAO,EAAC,OAAO;UAAAX,QAAA,EAAC;QAA8B;UAAAE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAY,CACvE;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACI,CAAC;IAEZ;;IAEA;IACA,MAAM7H,MAAM,GAAGF,KAAK,CAACE,MAAM,IAAI;MAAEC,WAAW,EAAE,CAAC;MAAEC,UAAU,EAAE,CAAC;MAAEC,WAAW,EAAE;IAAE,CAAC;IAChF,MAAMC,SAAS,GAAGN,KAAK,CAACM,SAAS,IAAI;MAAEC,oBAAoB,EAAE,CAAC;MAAEC,kBAAkB,EAAE,CAAC;MAAEC,yBAAyB,EAAE;IAAE,CAAC;IACrH,MAAMC,KAAK,GAAGV,KAAK,CAACU,KAAK,IAAI,EAAE;IAE/B,oBACEzC,OAAA,CAACzB,KAAK;MAAC+L,EAAE,EAAE;QAAEC,EAAE,EAAE,CAAC;QAAE4B,CAAC,EAAE;MAAE,CAAE;MAAA1C,QAAA,gBACzBzJ,OAAA,CAAC1B,UAAU;QAAC8L,OAAO,EAAC,IAAI;QAACC,YAAY;QAAAZ,QAAA,EAAC;MAAW;QAAAE,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAY,CAAC,EAC7DpH,YAAY,gBACX1C,OAAA,CAAChB,cAAc;QAAA2K,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC,gBAElB9J,OAAA,CAACvB,IAAI;QAACuL,SAAS;QAACC,OAAO,EAAE,CAAE;QAAAR,QAAA,gBACzBzJ,OAAA,CAACvB,IAAI;UAAC4I,IAAI;UAAC6C,EAAE,EAAE,EAAG;UAACC,EAAE,EAAE,CAAE;UAAAV,QAAA,gBACvBzJ,OAAA,CAAC1B,UAAU;YAAC8L,OAAO,EAAC,WAAW;YAACC,YAAY;YAAAZ,QAAA,EAAC;UAAM;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAY,CAAC,eAChE9J,OAAA,CAAC1B,UAAU;YAAC8L,OAAO,EAAC,OAAO;YAAAX,QAAA,GAAC,eAAa,EAACxH,MAAM,CAACC,WAAW,IAAI,CAAC;UAAA;YAAAyH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAa,CAAC,eAC/E9J,OAAA,CAAC1B,UAAU;YAAC8L,OAAO,EAAC,OAAO;YAAAX,QAAA,GAAC,cAAY,EAACxH,MAAM,CAACE,UAAU,IAAI,CAAC;UAAA;YAAAwH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAa,CAAC,eAC7E9J,OAAA,CAAC1B,UAAU;YAAC8L,OAAO,EAAC,OAAO;YAAAX,QAAA,GAAC,eAAa,EAACxH,MAAM,CAACG,WAAW,IAAI,CAAC;UAAA;YAAAuH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAa,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAE3E,CAAC,eAEP9J,OAAA,CAACvB,IAAI;UAAC4I,IAAI;UAAC6C,EAAE,EAAE,EAAG;UAACC,EAAE,EAAE,CAAE;UAAAV,QAAA,gBACvBzJ,OAAA,CAAC1B,UAAU;YAAC8L,OAAO,EAAC,WAAW;YAACC,YAAY;YAAAZ,QAAA,EAAC;UAAS;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAY,CAAC,eACnE9J,OAAA,CAAC1B,UAAU;YAAC8L,OAAO,EAAC,OAAO;YAAAX,QAAA,GAAC,iBAAe,EAAC,CAACpH,SAAS,CAACC,oBAAoB,IAAI,CAAC,EAAE8J,OAAO,CAAC,CAAC,CAAC;UAAA;YAAAzC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAa,CAAC,eAC1G9J,OAAA,CAAC1B,UAAU;YAAC8L,OAAO,EAAC,OAAO;YAAAX,QAAA,GAAC,gBAAc,EAAC,CAACpH,SAAS,CAACE,kBAAkB,IAAI,CAAC,EAAE6J,OAAO,CAAC,CAAC,CAAC;UAAA;YAAAzC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAa,CAAC,eACvG9J,OAAA,CAAC3B,GAAG;YAACiM,EAAE,EAAE;cAAE+B,OAAO,EAAE,MAAM;cAAEC,UAAU,EAAE,QAAQ;cAAEC,EAAE,EAAE;YAAE,CAAE;YAAA9C,QAAA,gBACxDzJ,OAAA,CAAC3B,GAAG;cAACiM,EAAE,EAAE;gBAAEkC,KAAK,EAAE,MAAM;gBAAEC,EAAE,EAAE;cAAE,CAAE;cAAAhD,QAAA,eAChCzJ,OAAA,CAAChB,cAAc;gBACboL,OAAO,EAAC,aAAa;gBACrBsC,KAAK,EAAErK,SAAS,CAACG,yBAAyB,IAAI,CAAE;gBAChD8H,EAAE,EAAE;kBAAEqC,MAAM,EAAE,EAAE;kBAAEC,YAAY,EAAE;gBAAE;cAAE;gBAAAjD,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACrC;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACC,CAAC,eACN9J,OAAA,CAAC3B,GAAG;cAACiM,EAAE,EAAE;gBAAEuC,QAAQ,EAAE;cAAG,CAAE;cAAApD,QAAA,eACxBzJ,OAAA,CAAC1B,UAAU;gBAAC8L,OAAO,EAAC,OAAO;gBAAC0C,KAAK,EAAC,gBAAgB;gBAAArD,QAAA,EAAE,GAAG,CAACpH,SAAS,CAACG,yBAAyB,IAAI,CAAC,EAAE4J,OAAO,CAAC,CAAC,CAAC;cAAG;gBAAAzC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAa;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC1H,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACF,CAAC,eAEP9J,OAAA,CAACvB,IAAI;UAAC4I,IAAI;UAAC6C,EAAE,EAAE,EAAG;UAACC,EAAE,EAAE,CAAE;UAAAV,QAAA,gBACvBzJ,OAAA,CAAC1B,UAAU;YAAC8L,OAAO,EAAC,WAAW;YAACC,YAAY;YAAAZ,QAAA,EAAC;UAAK;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAY,CAAC,eAC/D9J,OAAA,CAAC3B,GAAG;YAACiM,EAAE,EAAE;cAAE+B,OAAO,EAAE,MAAM;cAAEU,QAAQ,EAAE,MAAM;cAAEC,GAAG,EAAE;YAAE,CAAE;YAAAvD,QAAA,EACpDhH,KAAK,CAACsB,MAAM,GAAG,CAAC,GAAGtB,KAAK,CAAC+D,GAAG,CAAC,CAACc,KAAK,EAAE2F,KAAK,kBACzCjN,OAAA,CAAClB,IAAI;cAEHoO,KAAK,EAAE,GAAG5F,KAAK,CAACA,KAAK,IAAI,KAAK,KAAKA,KAAK,CAAC6F,KAAK,IAAI,CAAC,EAAG;cACtDC,IAAI,EAAC,OAAO;cACZnB,OAAO,EAAEA,CAAA,KAAM;gBACblJ,UAAU,CAACsK,IAAI,KAAK;kBAClB,GAAGA,IAAI;kBACPrK,mBAAmB,EAAEsE,KAAK,CAACA,KAAK,KAAK,iBAAiB,GAAG,EAAE,GAAGA,KAAK,CAACA;gBACtE,CAAC,CAAC,CAAC;cACL;YAAE,GARG2F,KAAK;cAAAtD,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OASX,CACF,CAAC,gBACA9J,OAAA,CAAC1B,UAAU;cAAC8L,OAAO,EAAC,OAAO;cAAAX,QAAA,EAAC;YAAyB;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAY;UAClE;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACE,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACF,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CACP;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACI,CAAC;EAEZ,CAAC;EAED,oBACE9J,OAAA,CAAC3B,GAAG;IAACiP,SAAS,EAAC,WAAW;IAAA7D,QAAA,EACvBlI,OAAO,gBACNvB,OAAA,CAAC3B,GAAG;MAACiM,EAAE,EAAE;QAAE+B,OAAO,EAAE,MAAM;QAAEkB,aAAa,EAAE,QAAQ;QAAEjB,UAAU,EAAE,QAAQ;QAAEC,EAAE,EAAE;MAAE,CAAE;MAAA9C,QAAA,gBACjFzJ,OAAA,CAACjB,gBAAgB;QAACqO,IAAI,EAAE;MAAG;QAAAzD,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC,eAC9B9J,OAAA,CAAC1B,UAAU;QAACgM,EAAE,EAAE;UAAEiC,EAAE,EAAE;QAAE,CAAE;QAAA9C,QAAA,EAAC;MAAmB;QAAAE,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAY,CAAC,eAC3D9J,OAAA,CAACxB,MAAM;QACL4L,OAAO,EAAC,UAAU;QAClB0C,KAAK,EAAC,SAAS;QACfb,OAAO,EAAEA,CAAA,KAAM9G,MAAM,CAACC,QAAQ,CAACC,MAAM,CAAC,CAAE;QACxCiF,EAAE,EAAE;UAAEiC,EAAE,EAAE;QAAE,CAAE;QAAA9C,QAAA,EACf;MAED;QAAAE,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAQ,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACN,CAAC,GACJrI,KAAK,gBACPzB,OAAA,CAAC3B,GAAG;MAAAoL,QAAA,gBACFzJ,OAAA,CAACpB,KAAK;QAAC4O,QAAQ,EAAC,OAAO;QAAClD,EAAE,EAAE;UAAEC,EAAE,EAAE;QAAE,CAAE;QAAAd,QAAA,GACnChI,KAAK,EACLA,KAAK,CAACyD,QAAQ,CAAC,eAAe,CAAC,iBAC9BlF,OAAA,CAAC1B,UAAU;UAAC8L,OAAO,EAAC,OAAO;UAACE,EAAE,EAAE;YAAEiC,EAAE,EAAE;UAAE,CAAE;UAAA9C,QAAA,gBACxCzJ,OAAA;YAAAyJ,QAAA,EAAQ;UAAa;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC,uEAC9B,eAAA9J,OAAA;YAAA2J,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAK,CAAC,8CACoC,eAAA9J,OAAA;YAAAyJ,QAAA,EAAM;UAAa;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC,4CACtE;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAY,CACb;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACI,CAAC,eACR9J,OAAA,CAAC3B,GAAG;QAACiM,EAAE,EAAE;UAAE+B,OAAO,EAAE,MAAM;UAAEW,GAAG,EAAE;QAAE,CAAE;QAAAvD,QAAA,eACnCzJ,OAAA,CAACxB,MAAM;UACL4L,OAAO,EAAC,WAAW;UACnBkD,SAAS,EAAC,gBAAgB;UAC1BrB,OAAO,EAAEA,CAAA,KAAM9G,MAAM,CAACC,QAAQ,CAACC,MAAM,CAAC,CAAE;UAAAoE,QAAA,EACzC;QAED;UAAAE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACN,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,gBAEN9J,OAAA,CAAC3B,GAAG;MAAAoL,QAAA,gBAMFzJ,OAAA,CAAC3B,GAAG;QAACiM,EAAE,EAAE;UAAEiC,EAAE,EAAE;QAAE,CAAE;QAAA9C,QAAA,eACjBzJ,OAAA,CAAC3B,GAAG;UAACiM,EAAE,EAAE;YAAE+B,OAAO,EAAE,MAAM;YAAEoB,cAAc,EAAE,eAAe;YAAEnB,UAAU,EAAE,QAAQ;YAAE/B,EAAE,EAAE;UAAE,CAAE;UAAAd,QAAA,gBACzFzJ,OAAA,CAAC1B,UAAU;YAAC8L,OAAO,EAAC,IAAI;YAAAX,QAAA,GAAC,cACX,EAACtI,UAAU,CAAC4C,MAAM,GAAG,CAAC,GAAG,IAAI5C,UAAU,CAAC4C,MAAM,GAAG,GAAG,EAAE;UAAA;YAAA4F,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACxD,CAAC,eACb9J,OAAA,CAACxB,MAAM;YACL4L,OAAO,EAAC,UAAU;YAClBgD,IAAI,EAAC,OAAO;YACZnB,OAAO,EAAEA,CAAA,KAAM9G,MAAM,CAACC,QAAQ,CAACC,MAAM,CAAC,CAAE;YACxCqI,SAAS,EAAEnM,OAAO,gBAAGvB,OAAA,CAACjB,gBAAgB;cAACqO,IAAI,EAAE;YAAG;cAAAzD,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,gBAAG9J,OAAA,CAACV,WAAW;cAAAqK,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAE;YACtE6D,QAAQ,EAAEpM,OAAQ;YAAAkI,QAAA,EACnB;UAED;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACN;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,EAEL3I,UAAU,CAAC4C,MAAM,GAAG,CAAC,gBACpB/D,OAAA,CAAC3B,GAAG;QAACiM,EAAE,EAAE;UAAEC,EAAE,EAAE;QAAE,CAAE;QAAAd,QAAA,GAEhBmE,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,aAAa,iBACrC9N,OAAA,CAAC3B,GAAG;UAACiM,EAAE,EAAE;YAAEC,EAAE,EAAE,CAAC;YAAE4B,CAAC,EAAE,CAAC;YAAE4B,OAAO,EAAE,SAAS;YAAEnB,YAAY,EAAE,CAAC;YAAEoB,QAAQ,EAAE,QAAQ;YAAEC,UAAU,EAAE,WAAW;YAAE5B,OAAO,EAAE;UAAO,CAAE;UAAA5C,QAAA,EACzHyE,MAAM,CAACC,IAAI,CAAChN,UAAU,CAAC,CAAC,CAAC,CAAC,CAACqF,GAAG,CAACb,GAAG,iBACjC3F,OAAA;YAAAyJ,QAAA,GAAgB9D,GAAG,EAAC,IAAE,EAACmB,IAAI,CAACsH,SAAS,CAACjN,UAAU,CAAC,CAAC,CAAC,CAACwE,GAAG,CAAC,CAAC;UAAA,GAA/CA,GAAG;YAAAgE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAkD,CAChE;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACC,CACN,eACD9J,OAAA,CAACF,mBAAmB;UAClBuO,IAAI,EAAElN,UAAW;UACjBI,OAAO,EAAEA,OAAQ;UACjB+M,oBAAoB,EAAGC,YAAY,IAAK/K,OAAO,CAACC,GAAG,CAAC,uBAAuB,EAAE8K,YAAY,CAACxK,MAAM,CAAE;UAClGyK,iBAAiB,EAAE,EAAArO,YAAA,GAAAgB,UAAU,CAAC,CAAC,CAAC,cAAAhB,YAAA,uBAAbA,YAAA,CAAesO,mBAAmB,OAAArO,aAAA,GAAIe,UAAU,CAAC,CAAC,CAAC,cAAAf,aAAA,uBAAbA,aAAA,CAAesO,SAAS,OAAArO,aAAA,GAAIc,UAAU,CAAC,CAAC,CAAC,cAAAd,aAAA,uBAAbA,aAAA,CAAesO,GAAG;QAAC;UAAAhF,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACzG,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACC,CAAC,gBAEN9J,OAAA,CAACpB,KAAK;QAAC4O,QAAQ,EAAC,MAAM;QAAClD,EAAE,EAAE;UAAEC,EAAE,EAAE;QAAE,CAAE;QAAAd,QAAA,EAAC;MAEtC;QAAAE,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAO,CACR,eAGD9J,OAAA,CAAC3B,GAAG;QAACiM,EAAE,EAAE;UAAEiC,EAAE,EAAE;QAAE,CAAE;QAAA9C,QAAA,gBACjBzJ,OAAA,CAAC3B,GAAG;UAACiM,EAAE,EAAE;YAAE+B,OAAO,EAAE,MAAM;YAAEoB,cAAc,EAAE,eAAe;YAAEnB,UAAU,EAAE,QAAQ;YAAE/B,EAAE,EAAE;UAAE,CAAE;UAAAd,QAAA,gBACzFzJ,OAAA,CAAC1B,UAAU;YAAC8L,OAAO,EAAC,IAAI;YAAAX,QAAA,GAAC,aACZ,EAACpI,SAAS,CAAC0C,MAAM,GAAG,CAAC,GAAG,IAAI1C,SAAS,CAAC0C,MAAM,GAAG,GAAG,EAAE;UAAA;YAAA4F,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACrD,CAAC,eACb9J,OAAA,CAACxB,MAAM;YACL4L,OAAO,EAAC,UAAU;YAClBgD,IAAI,EAAC,OAAO;YACZnB,OAAO,EAAEA,CAAA,KAAM9G,MAAM,CAACC,QAAQ,CAACC,MAAM,CAAC,CAAE;YACxCqI,SAAS,EAAEnM,OAAO,gBAAGvB,OAAA,CAACjB,gBAAgB;cAACqO,IAAI,EAAE;YAAG;cAAAzD,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,gBAAG9J,OAAA,CAACV,WAAW;cAAAqK,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAE;YACtE6D,QAAQ,EAAEpM,OAAQ;YAAAkI,QAAA,EACnB;UAED;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACN,CAAC,EACLzI,SAAS,CAAC0C,MAAM,GAAG,CAAC,gBACnB/D,OAAA,CAACF,mBAAmB;UAClBuO,IAAI,EAAEhN,SAAU;UAChBE,OAAO,EAAEA,OAAQ;UACjB+M,oBAAoB,EAAGC,YAAY,IAAK/K,OAAO,CAACC,GAAG,CAAC,sBAAsB,EAAE8K,YAAY,CAACxK,MAAM;QAAE;UAAA4F,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAClG,CAAC,gBAEF9J,OAAA,CAACpB,KAAK;UAAC4O,QAAQ,EAAC,MAAM;UAAClD,EAAE,EAAE;YAAEC,EAAE,EAAE;UAAE,CAAE;UAAAd,QAAA,EAAC;QAEtC;UAAAE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAO,CACR;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACE,CAAC,EAKLV,mBAAmB,CAAC,CAAC,eAGtBpJ,OAAA,CAACf,MAAM;QACLoK,IAAI,EAAE7I,qBAAsB;QAC5B8I,OAAO,EAAEA,CAAA,KAAM7I,wBAAwB,CAAC,KAAK,CAAE;QAC/C+I,SAAS;QACTD,QAAQ,EAAC,IAAI;QAAAE,QAAA,eAEbzJ,OAAA,CAACN,oBAAoB;UACnBqB,UAAU,EAAEA,UAAW;UACvB6N,SAAS,EAAG/J,OAAO,IAAK;YACtB;YACApE,wBAAwB,CAAC,KAAK,CAAC;;YAE/B;YACA,IAAIoE,OAAO,EAAE;cACX;cACArB,OAAO,CAACC,GAAG,CAAC,wBAAwB,EAAEoB,OAAO,CAAC;cAC9C;cACAgK,KAAK,CAAChK,OAAO,CAAC;cACd;cACAC,UAAU,CAAC,MAAM;gBACftB,OAAO,CAACC,GAAG,CAAC,uCAAuC,CAAC;gBACpD,IAAI;kBACF;kBACAF,SAAS,CAAC,CAAC;gBACb,CAAC,CAAC,OAAO9B,KAAK,EAAE;kBACd+B,OAAO,CAAC/B,KAAK,CAAC,2CAA2C,EAAEA,KAAK,CAAC;kBACjE;kBACA0D,MAAM,CAACC,QAAQ,CAACC,MAAM,CAAC,CAAC;gBAC1B;cACF,CAAC,EAAE,IAAI,CAAC;YACV,CAAC,MAAM;cACL;cACA7B,OAAO,CAACC,GAAG,CAAC,mCAAmC,CAAC;YAClD;UACF,CAAE;UACFqL,OAAO,EAAGjK,OAAO,IAAK;YACpB;YACArB,OAAO,CAAC/B,KAAK,CAAC,0CAA0C,EAAEoD,OAAO,CAAC;YAClE;YACAgK,KAAK,CAAC,WAAWhK,OAAO,EAAE,CAAC;YAC3B;YACApE,wBAAwB,CAAC,KAAK,CAAC;YAC/B;YACA8C,SAAS,CAAC,CAAC;UACb,CAAE;UACFwL,aAAa,EAAC;QAAa;UAAApF,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC5B;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACI,CAAC,EAIRpJ,sBAAsB,IAAI8C,OAAO,CAACC,GAAG,CAAC,4DAA4D,EAAE1C,UAAU,CAAC,eAEhHf,OAAA,CAACf,MAAM;QACLoK,IAAI,EAAE3I,sBAAuB;QAC7B4I,OAAO,EAAEA,CAAA,KAAM3I,yBAAyB,CAAC,KAAK,CAAE;QAChD6I,SAAS;QACTD,QAAQ,EAAC,IAAI;QAAAE,QAAA,eAEbzJ,OAAA,CAACN,oBAAoB;UACnBqB,UAAU,EAAEA,UAAW;UACvB6N,SAAS,EAAG/J,OAAO,IAAK;YACtB;YACAlE,yBAAyB,CAAC,KAAK,CAAC;;YAEhC;YACA,IAAIkE,OAAO,EAAE;cACX;cACArB,OAAO,CAACC,GAAG,CAAC,wBAAwB,EAAEoB,OAAO,CAAC;cAC9C;cACAgK,KAAK,CAAChK,OAAO,CAAC;cACd;cACArB,OAAO,CAACC,GAAG,CAAC,uCAAuC,CAAC;cACpD;cACAqB,UAAU,CAAC,MAAM;gBACf,IAAI;kBACFvB,SAAS,CAAC,CAAC;gBACb,CAAC,CAAC,OAAO9B,KAAK,EAAE;kBACd+B,OAAO,CAAC/B,KAAK,CAAC,2CAA2C,EAAEA,KAAK,CAAC;kBACjE;kBACA0D,MAAM,CAACC,QAAQ,CAACC,MAAM,CAAC,CAAC;gBAC1B;cACF,CAAC,EAAE,IAAI,CAAC;YACV,CAAC,MAAM;cACL;cACA7B,OAAO,CAACC,GAAG,CAAC,mCAAmC,CAAC;YAClD;UACF,CAAE;UACFqL,OAAO,EAAGjK,OAAO,IAAK;YACpB;YACArB,OAAO,CAAC/B,KAAK,CAAC,sCAAsC,EAAEoD,OAAO,CAAC;YAC9D;YACAgK,KAAK,CAAC,WAAWhK,OAAO,EAAE,CAAC;YAC3B;YACAlE,yBAAyB,CAAC,KAAK,CAAC;YAChC;YACA6C,OAAO,CAACC,GAAG,CAAC,mCAAmC,CAAC;YAChDF,SAAS,CAAC,CAAC;UACb,CAAE;UACFwL,aAAa,EAAC;QAAc;UAAApF,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC7B;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACI,CAAC,eAGT9J,OAAA,CAACf,MAAM;QACLoK,IAAI,EAAEzI,sBAAuB;QAC7B0I,OAAO,EAAEA,CAAA,KAAMzI,yBAAyB,CAAC,KAAK,CAAE;QAChD2I,SAAS;QACTD,QAAQ,EAAC,IAAI;QAAAE,QAAA,gBAEbzJ,OAAA,CAACd,WAAW;UAAAuK,QAAA,EAAC;QAAmB;UAAAE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAa,CAAC,eAC9C9J,OAAA,CAACb,aAAa;UAAAsK,QAAA,eACZzJ,OAAA,CAAC3B,GAAG;YAACiM,EAAE,EAAE;cAAEiC,EAAE,EAAE;YAAE,CAAE;YAAA9C,QAAA,eACjBzJ,OAAA,CAACJ,gBAAgB;cACfmB,UAAU,EAAEA,UAAW;cACvB6N,SAAS,EAAG/J,OAAO,IAAK;gBACtB;gBACAhE,yBAAyB,CAAC,KAAK,CAAC;gBAChC;gBACAgO,KAAK,CAAChK,OAAO,CAAC;gBACd;gBACAC,UAAU,CAAC,MAAM;kBACftB,OAAO,CAACC,GAAG,CAAC,uCAAuC,CAAC;kBACpD,IAAI;oBACF;oBACAF,SAAS,CAAC,CAAC;kBACb,CAAC,CAAC,OAAO9B,KAAK,EAAE;oBACd+B,OAAO,CAAC/B,KAAK,CAAC,2CAA2C,EAAEA,KAAK,CAAC;oBACjE;oBACA+B,OAAO,CAACC,GAAG,CAAC,4CAA4C,CAAC;oBACzD0B,MAAM,CAACC,QAAQ,CAACC,MAAM,CAAC,CAAC;kBAC1B;gBACF,CAAC,EAAE,IAAI,CAAC;cACV,CAAE;cACFyJ,OAAO,EAAGjK,OAAO,IAAK;gBACpB;gBACArB,OAAO,CAAC/B,KAAK,CAAC,sCAAsC,EAAEoD,OAAO,CAAC;gBAC9D;gBACAgK,KAAK,CAAC,WAAWhK,OAAO,EAAE,CAAC;cAC7B,CAAE;cACFmK,QAAQ,EAAE;YAAK;cAAArF,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAChB;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACC;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACO,CAAC,eAChB9J,OAAA,CAACZ,aAAa;UAAAqK,QAAA,eACZzJ,OAAA,CAACxB,MAAM;YAACyN,OAAO,EAAEA,CAAA,KAAMpL,yBAAyB,CAAC,KAAK,CAAE;YAAA4I,QAAA,EAAC;UAAO;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC5D,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACV,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACN;EACN;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACE,CAAC;AAEV,CAAC;AAAC5J,EAAA,CAn3BID,kBAAkB;EAAA,QACYT,OAAO,EACyHC,gBAAgB,EACjKF,WAAW;AAAA;AAAA0P,EAAA,GAHxBhP,kBAAkB;AAq3BxB,eAAeA,kBAAkB;AAAC,IAAAgP,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}