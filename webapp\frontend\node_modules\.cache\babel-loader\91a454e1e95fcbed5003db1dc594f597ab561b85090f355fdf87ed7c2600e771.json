{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\PycharmProjects\\\\CMS\\\\webapp\\\\frontend\\\\src\\\\components\\\\cavi\\\\InserisciMetriForm.js\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect } from 'react';\nimport axios from 'axios';\nimport { Box, Paper, Typography, TextField, Button, Stepper, Step, StepLabel, Grid, FormControl, InputLabel, Select, MenuItem, List, ListItem, ListItemText, ListItemSecondaryAction, Divider, Alert, CircularProgress, FormHelperText, IconButton, Chip, Dialog, DialogTitle, DialogContent, DialogActions } from '@mui/material';\nimport { Search as SearchIcon, Save as SaveIcon, ArrowBack as ArrowBackIcon, ArrowForward as ArrowForwardIcon, Cancel as CancelIcon, CheckCircle as CheckCircleIcon, Warning as WarningIcon, Info as InfoIcon } from '@mui/icons-material';\nimport { useNavigate } from 'react-router-dom';\nimport caviService from '../../services/caviService';\nimport axiosInstance from '../../services/axiosConfig';\nimport IncompatibleReelDialog from './IncompatibleReelDialog';\nimport CavoDetailsView from './CavoDetailsView';\nimport { CABLE_STATES, REEL_STATES, determineCableState, determineReelState, canModifyCable, isCableSpare, isCableInstalled, getCableStateColor, getReelStateColor } from '../../utils/stateUtils';\nimport parcoCaviService from '../../services/parcoCaviService';\nimport { redirectToVisualizzaCavi } from '../../utils/navigationUtils';\n\n/**\n * Componente per l'inserimento dei metri posati di un cavo\n *\n * @param {Object} props - Proprietà del componente\n * @param {string} props.cantiereId - ID del cantiere\n * @param {Function} props.onSuccess - Funzione chiamata al successo dell'operazione\n * @param {Function} props.onError - Funzione chiamata in caso di errore\n */\nimport { jsxDEV as _jsxDEV, Fragment as _Fragment } from \"react/jsx-dev-runtime\";\nconst InserisciMetriForm = ({\n  cantiereId,\n  onSuccess,\n  onError\n}) => {\n  _s();\n  const navigate = useNavigate();\n\n  // Stati per la gestione del form multi-step\n  const [activeStep, setActiveStep] = useState(0);\n  const [loading, setLoading] = useState(false);\n  const [caviLoading, setCaviLoading] = useState(false);\n  const [bobineLoading, setBobineLoading] = useState(false);\n\n  // Stati per i dati\n  const [cavi, setCavi] = useState([]);\n  const [bobine, setBobine] = useState([]);\n  const [selectedCavo, setSelectedCavo] = useState(null);\n  const [cavoIdInput, setCavoIdInput] = useState('');\n\n  // Stati per il form\n  const [formData, setFormData] = useState({\n    id_cavo: '',\n    metri_posati: '',\n    id_bobina: ''\n  });\n\n  // Stati per la validazione\n  const [formErrors, setFormErrors] = useState({});\n  const [formWarnings, setFormWarnings] = useState({});\n\n  // Stati per i dialoghi speciali\n  const [showIncompatibleReelDialog, setShowIncompatibleReelDialog] = useState(false);\n  const [incompatibleReel, setIncompatibleReel] = useState(null);\n  const [showAlreadyLaidDialog, setShowAlreadyLaidDialog] = useState(false);\n  const [alreadyLaidCavo, setAlreadyLaidCavo] = useState(null);\n  const [showCavoDetailsDialog, setShowCavoDetailsDialog] = useState(false);\n\n  // Definizione dei passi del form\n  const steps = ['Seleziona Cavo', 'Associa Bobina', 'Inserisci Metri', 'Conferma'];\n\n  // Carica la lista dei cavi all'avvio\n  useEffect(() => {\n    loadCavi();\n  }, [cantiereId]);\n\n  // Carica la lista delle bobine quando necessario\n  useEffect(() => {\n    if (activeStep === 1) {\n      // Carica le bobine quando si passa al passo \"Associa Bobina\"\n      loadBobine();\n    }\n  }, [activeStep, cantiereId]);\n\n  // Funzione per caricare i cavi\n  const loadCavi = async () => {\n    try {\n      setCaviLoading(true);\n      console.log(`Caricamento cavi per il cantiere ${cantiereId}...`);\n\n      // Carica tutti i cavi, inclusi quelli SPARE e installati\n      try {\n        const caviData = await caviService.getCavi(cantiereId);\n        console.log(`Caricati ${caviData.length} cavi`);\n\n        // Mostra tutti i cavi (da installare, in corso, installati e SPARE)\n        // Questo permette di vedere anche i cavi già installati per eventuale modifica della bobina\n        setCavi(caviData);\n      } catch (loadError) {\n        console.error('Errore nel primo tentativo di caricamento dei cavi:', loadError);\n\n        // Se è un errore di rete, prova un secondo tentativo dopo una breve pausa\n        if (loadError.isNetworkError || !loadError.response || loadError.code === 'ECONNABORTED' || loadError.message && loadError.message.includes('Network Error')) {\n          console.log('Errore di rete, tentativo di recupero dopo 1 secondo...');\n          await new Promise(resolve => setTimeout(resolve, 1000));\n          try {\n            // Secondo tentativo con timeout aumentato\n            const token = localStorage.getItem('token');\n            const API_URL = axiosInstance.defaults.baseURL;\n\n            // Usa l'ID cantiere originale per la richiesta alternativa\n            const retryResponse = await axios.get(`${API_URL}/cavi/${cantiereId}`, {\n              headers: {\n                'Authorization': `Bearer ${token}`\n              },\n              timeout: 30000 // 30 secondi\n            });\n            console.log(`Recupero riuscito al secondo tentativo: ${retryResponse.data.length} cavi`);\n            setCavi(retryResponse.data);\n          } catch (retryError) {\n            console.error('Anche il secondo tentativo è fallito:', retryError);\n            throw retryError;\n          }\n        } else {\n          throw loadError;\n        }\n      }\n    } catch (error) {\n      console.error('Errore nel caricamento dei cavi:', error);\n\n      // Messaggio di errore più dettagliato\n      let errorMessage = 'Errore nel caricamento dei cavi';\n      if (error.isNetworkError) {\n        errorMessage = 'Errore di connessione al server. Verifica la rete e riprova.';\n      } else if (error.detail) {\n        errorMessage = error.detail;\n      } else if (error.message) {\n        errorMessage = error.message;\n      }\n      onError(errorMessage);\n    } finally {\n      setCaviLoading(false);\n    }\n  };\n\n  // Funzione per caricare le bobine\n  const loadBobine = async () => {\n    try {\n      setBobineLoading(true);\n\n      // Carica solo le bobine disponibili o in uso\n      const bobineData = await parcoCaviService.getBobine(cantiereId);\n\n      // Filtra le bobine per stato (disponibile o in uso) e per compatibilità con il cavo selezionato\n      let bobineUtilizzabili = bobineData.filter(bobina => (bobina.stato_bobina === 'Disponibile' || bobina.stato_bobina === 'In Uso') && bobina.stato_bobina !== 'Over' && bobina.stato_bobina !== 'Terminata');\n\n      // Se c'è un cavo selezionato, filtra ulteriormente per caratteristiche del cavo\n      if (selectedCavo) {\n        // Filtra per tipologia, numero conduttori e sezione se disponibili\n        if (selectedCavo.tipologia && selectedCavo.n_conduttori && selectedCavo.sezione) {\n          const bobineCompatibili = bobineUtilizzabili.filter(bobina => bobina.tipologia === selectedCavo.tipologia && bobina.n_conduttori === selectedCavo.n_conduttori && bobina.sezione === selectedCavo.sezione);\n\n          // Se ci sono bobine compatibili, usa quelle, altrimenti mostra tutte le bobine utilizzabili\n          if (bobineCompatibili.length > 0) {\n            bobineUtilizzabili = bobineCompatibili;\n          } else {\n            console.log('Nessuna bobina compatibile trovata, mostro tutte le bobine disponibili');\n          }\n        }\n\n        // Ordina le bobine per metri residui (decrescente)\n        bobineUtilizzabili.sort((a, b) => b.metri_residui - a.metri_residui);\n      }\n      setBobine(bobineUtilizzabili);\n    } catch (error) {\n      console.error('Errore nel caricamento delle bobine:', error);\n      onError('Errore nel caricamento delle bobine: ' + (error.message || 'Errore sconosciuto'));\n    } finally {\n      setBobineLoading(false);\n    }\n  };\n\n  // Gestisce la ricerca di un cavo per ID\n  const handleSearchCavoById = async () => {\n    if (!cavoIdInput.trim()) {\n      onError('Inserisci un ID cavo valido');\n      return;\n    }\n    try {\n      setCaviLoading(true);\n      console.log(`Ricerca cavo con ID: ${cavoIdInput.trim()} nel cantiere ${cantiereId}`);\n\n      // Primo tentativo di ricerca\n      let cavoData;\n      try {\n        cavoData = await caviService.getCavoById(cantiereId, cavoIdInput.trim());\n        console.log('Cavo trovato con successo:', cavoData);\n      } catch (searchError) {\n        console.error('Errore nel primo tentativo di ricerca:', searchError);\n\n        // Se è un errore di rete, prova a cercare il cavo nella lista locale\n        if (searchError.isNetworkError) {\n          console.log('Tentativo di ricerca nella lista locale dei cavi...');\n          const cavoLocale = cavi.find(c => c.id_cavo === cavoIdInput.trim());\n          if (cavoLocale) {\n            console.log('Cavo trovato nella lista locale:', cavoLocale);\n            cavoData = cavoLocale;\n          } else {\n            console.error('Cavo non trovato nemmeno nella lista locale');\n            throw searchError; // Rilancia l'errore originale\n          }\n        } else {\n          throw searchError; // Rilancia l'errore se non è di rete\n        }\n      }\n\n      // Verifica se il cavo è già installato\n      if (cavoData.stato_installazione === 'Installato' || cavoData.metratura_reale && cavoData.metratura_reale > 0) {\n        console.log('Cavo già installato, mostra dialogo:', cavoData);\n        // Mostra il dialogo per cavi già posati\n        setAlreadyLaidCavo(cavoData);\n        setShowAlreadyLaidDialog(true);\n        setCaviLoading(false);\n        return;\n      }\n\n      // Verifica se il cavo è SPARE\n      if (cavoData.modificato_manualmente === 3) {\n        console.log('Cavo SPARE trovato:', cavoData);\n        // Gestione del cavo SPARE - verrà gestito in handleCavoSelect\n      }\n\n      // Seleziona il cavo e passa al passo successivo\n      console.log('Cavo selezionato, passaggio al passo successivo');\n      handleCavoSelect(cavoData);\n    } catch (error) {\n      console.error('Errore nella ricerca del cavo:', error);\n\n      // Messaggio di errore più dettagliato\n      let errorMessage = 'Cavo non trovato o errore nella ricerca';\n      if (error.isNetworkError) {\n        errorMessage = 'Errore di connessione al server. Verifica la rete e riprova.';\n      } else if (error.status === 404) {\n        errorMessage = `Cavo con ID \"${cavoIdInput.trim()}\" non trovato nel cantiere ${cantiereId}`;\n      } else if (error.detail) {\n        errorMessage = error.detail;\n      } else if (error.message) {\n        errorMessage = error.message;\n      }\n      onError(errorMessage);\n    } finally {\n      setCaviLoading(false);\n    }\n  };\n\n  // Gestisce la selezione di un cavo\n  const handleCavoSelect = cavo => {\n    // Verifica se il cavo è già installato\n    if (cavo.stato_installazione === 'Installato' || cavo.metratura_reale && cavo.metratura_reale > 0) {\n      // Mostra il dialogo per cavi già posati\n      setAlreadyLaidCavo(cavo);\n      setShowAlreadyLaidDialog(true);\n      return;\n    }\n    // Verifica se il cavo è SPARE (modificato_manualmente = 3)\n    else if (cavo.modificato_manualmente === 3) {\n      // Chiedi conferma all'utente per riattivare il cavo SPARE\n      if (window.confirm(`Il cavo ${cavo.id_cavo} è marcato come SPARE. Vuoi riattivarlo?`)) {\n        // Riattiva il cavo SPARE (imposta modificato_manualmente = 0)\n        reactivateSpare(cavo.id_cavo).then(() => {\n          // Aggiorna il cavo selezionato con modificato_manualmente = 0\n          const updatedCavo = {\n            ...cavo,\n            modificato_manualmente: 0\n          };\n          setSelectedCavo(updatedCavo);\n          setFormData({\n            ...formData,\n            id_cavo: updatedCavo.id_cavo,\n            metri_posati: ''\n          });\n          // Passa al passo successivo (Associa Bobina)\n          setActiveStep(1);\n        }).catch(error => {\n          console.error('Errore durante la riattivazione del cavo SPARE:', error);\n          onError('Errore durante la riattivazione del cavo SPARE: ' + (error.message || 'Errore sconosciuto'));\n        });\n      } else {\n        // L'utente ha annullato la riattivazione\n        return;\n      }\n    } else {\n      // Cavo normale (non SPARE e non installato)\n      setSelectedCavo(cavo);\n      setFormData({\n        ...formData,\n        id_cavo: cavo.id_cavo,\n        metri_posati: ''\n      });\n      // Passa al passo successivo (Associa Bobina)\n      setActiveStep(1);\n    }\n  };\n\n  // Funzione per riattivare un cavo SPARE\n  const reactivateSpare = async cavoId => {\n    try {\n      // Chiamata API per riattivare il cavo SPARE\n      await caviService.reactivateSpare(cantiereId, cavoId);\n      onSuccess(`Cavo ${cavoId} riattivato con successo`);\n      return true;\n    } catch (error) {\n      console.error('Errore durante la riattivazione del cavo SPARE:', error);\n      onError('Errore durante la riattivazione del cavo SPARE: ' + (error.message || 'Errore sconosciuto'));\n      throw error;\n    }\n  };\n\n  // Gestisce il cambio dei valori nel form\n  const handleFormChange = e => {\n    const {\n      name,\n      value\n    } = e.target;\n    setFormData({\n      ...formData,\n      [name]: value\n    });\n\n    // Validazione in tempo reale\n    validateField(name, value);\n  };\n\n  // Validazione di un campo\n  const validateField = (name, value) => {\n    let error = null;\n    let warning = null;\n    if (name === 'metri_posati') {\n      // Controllo input vuoto\n      if (!value || value.trim() === '') {\n        error = 'Inserire un valore per i metri posati';\n        return false;\n      }\n\n      // Controllo formato numerico\n      if (isNaN(parseFloat(value)) || parseFloat(value) <= 0) {\n        error = 'Inserire un valore numerico positivo';\n        return false;\n      }\n      const metriPosati = parseFloat(value);\n\n      // Controllo metri teorici cavo\n      if (selectedCavo && selectedCavo.metri_teorici && metriPosati > parseFloat(selectedCavo.metri_teorici)) {\n        warning = `I metri posati (${metriPosati}m) superano i metri teorici del cavo (${selectedCavo.metri_teorici}m)`;\n      }\n\n      // Controllo metri residui bobina (se bobina reale)\n      if (formData.id_bobina && formData.id_bobina !== 'BOBINA_VUOTA') {\n        const bobina = bobine.find(b => b.id_bobina === formData.id_bobina);\n        if (bobina && metriPosati > parseFloat(bobina.metri_residui)) {\n          warning = `I metri posati (${metriPosati}m) superano i metri residui della bobina (${bobina.metri_residui}m). La bobina andrà in stato OVER.`;\n        }\n      }\n    }\n\n    // Aggiorna gli errori\n    setFormErrors(prev => ({\n      ...prev,\n      [name]: error\n    }));\n\n    // Aggiorna gli avvisi\n    setFormWarnings(prev => ({\n      ...prev,\n      [name]: warning\n    }));\n    return !error;\n  };\n\n  // Validazione completa del form\n  const validateForm = () => {\n    let isValid = true;\n    const errors = {};\n    const warnings = {};\n\n    // Validazione metri posati\n    if (!formData.metri_posati || formData.metri_posati.trim() === '') {\n      errors.metri_posati = 'Inserire un valore per i metri posati';\n      isValid = false;\n    } else if (isNaN(parseFloat(formData.metri_posati)) || parseFloat(formData.metri_posati) <= 0) {\n      errors.metri_posati = 'Inserire un valore numerico positivo';\n      isValid = false;\n    } else {\n      const metriPosati = parseFloat(formData.metri_posati);\n\n      // Controllo metri teorici cavo\n      if (selectedCavo && selectedCavo.metri_teorici && metriPosati > parseFloat(selectedCavo.metri_teorici)) {\n        warnings.metri_posati = `I metri posati (${metriPosati}m) superano i metri teorici del cavo (${selectedCavo.metri_teorici}m)`;\n\n        // Chiedi conferma all'utente\n        if (!window.confirm(`ATTENZIONE: I metri posati (${metriPosati}m) superano i metri teorici del cavo (${selectedCavo.metri_teorici}m).\\n\\nVuoi continuare comunque?`)) {\n          isValid = false;\n        }\n      }\n\n      // Controllo metri residui bobina (se bobina reale)\n      if (isValid && formData.id_bobina && formData.id_bobina !== 'BOBINA_VUOTA') {\n        const bobina = bobine.find(b => b.id_bobina === formData.id_bobina);\n        if (bobina && metriPosati > parseFloat(bobina.metri_residui)) {\n          warnings.metri_posati = `I metri posati (${metriPosati}m) superano i metri residui della bobina (${bobina.metri_residui}m). La bobina andrà in stato OVER.`;\n\n          // Chiedi conferma all'utente\n          if (!window.confirm(`ATTENZIONE: I metri posati (${metriPosati}m) superano i metri residui della bobina (${bobina.metri_residui}m).\\n\\nQuesto porterà la bobina in stato OVER.\\n\\nVuoi continuare?`)) {\n            isValid = false;\n          }\n        }\n      }\n    }\n    setFormErrors(errors);\n    setFormWarnings(warnings);\n    return isValid;\n  };\n\n  // Gestisce il passaggio al passo successivo\n  const handleNext = () => {\n    if (activeStep === 2) {\n      // Validazione prima di passare al passo successivo (da Inserisci Metri a Conferma)\n      if (!validateForm()) {\n        return;\n      }\n    } else if (activeStep === 1) {\n      // Carica le bobine prima di passare al passo successivo (da Associa Bobina a Inserisci Metri)\n      loadBobine();\n    }\n    setActiveStep(prevActiveStep => prevActiveStep + 1);\n  };\n\n  // Gestisce il ritorno al passo precedente\n  const handleBack = () => {\n    setActiveStep(prevActiveStep => prevActiveStep - 1);\n  };\n\n  // Gestisce il reset del form\n  const handleReset = () => {\n    setActiveStep(0);\n    setSelectedCavo(null);\n    setCavoIdInput('');\n    setFormData({\n      id_cavo: '',\n      metri_posati: '',\n      id_bobina: ''\n    });\n    setFormErrors({});\n    setFormWarnings({});\n  };\n\n  // Determina lo stato di installazione in base ai metri posati\n  // Utilizziamo la funzione dalla utility stateUtils\n  const determineInstallationStatus = (metriPosati, metriTeorici) => {\n    return determineCableState(metriPosati, metriTeorici);\n  };\n\n  // Gestisce l'invio del form\n  const handleSubmit = async () => {\n    try {\n      setLoading(true);\n\n      // Validazione finale\n      if (!validateForm()) {\n        setLoading(false);\n        return;\n      }\n\n      // Prepara i dati da inviare\n      const metriPosati = parseFloat(formData.metri_posati);\n\n      // Gestione speciale per BOBINA_VUOTA\n      let idBobina = formData.id_bobina;\n      // Se non è selezionata alcuna bobina, imposta esplicitamente a null\n      if (!idBobina || idBobina === '') {\n        idBobina = null;\n        console.log('Nessuna bobina selezionata, impostando a null');\n      } else if (idBobina === 'BOBINA_VUOTA') {\n        console.log('Usando BOBINA_VUOTA per il cavo');\n        // Assicurati che BOBINA_VUOTA venga passato come stringa e non come null\n        idBobina = 'BOBINA_VUOTA';\n      } else {\n        console.log(`Usando bobina reale: ${idBobina}`);\n      }\n\n      // Determina lo stato di installazione\n      const statoInstallazione = determineInstallationStatus(metriPosati, selectedCavo.metri_teorici);\n\n      // Verifica se è necessario forzare lo stato OVER della bobina\n      let forceOver = false;\n      if (idBobina && idBobina !== 'BOBINA_VUOTA') {\n        const bobina = bobine.find(b => b.id_bobina === idBobina);\n        if (bobina && metriPosati > parseFloat(bobina.metri_residui)) {\n          forceOver = true;\n          console.log(`Forzando stato OVER per la bobina ${idBobina} (metri posati > metri residui)`);\n        }\n      }\n\n      // Log per debug\n      console.log('Invio dati:', {\n        cantiereId,\n        cavoId: formData.id_cavo,\n        metriPosati,\n        idBobina,\n        forceOver,\n        statoInstallazione\n      });\n\n      // Conferma finale prima dell'invio\n      const confirmMessage = `Confermi l'aggiornamento del cavo ${formData.id_cavo} con ${metriPosati}m posati?`;\n      if (!window.confirm(confirmMessage)) {\n        setLoading(false);\n        return;\n      }\n\n      // Chiamata API\n      console.log('Invio richiesta updateMetriPosati con i seguenti parametri:');\n      console.log('- cantiereId:', cantiereId);\n      console.log('- id_cavo:', formData.id_cavo);\n      console.log('- metri_posati:', metriPosati);\n      console.log('- id_bobina:', idBobina, typeof idBobina);\n      console.log('- forceOver:', forceOver);\n      await caviService.updateMetriPosati(cantiereId, formData.id_cavo, metriPosati, idBobina, forceOver);\n\n      // Messaggio di successo con dettagli sulla bobina\n      let successMessage = `Metri posati aggiornati con successo. Stato cavo: ${statoInstallazione}`;\n      if (idBobina === 'BOBINA_VUOTA') {\n        successMessage += '. Cavo associato a BOBINA VUOTA';\n      } else if (idBobina) {\n        const bobina = bobine.find(b => b.id_bobina === idBobina);\n        if (bobina) {\n          successMessage += `. Cavo associato alla bobina ${idBobina}`;\n        }\n      }\n\n      // Gestione successo\n      onSuccess(successMessage);\n\n      // Reset del form\n      handleReset();\n\n      // Ricarica i cavi\n      loadCavi();\n    } catch (error) {\n      console.error('Errore durante l\\'aggiornamento dei metri posati:', error);\n      onError('Errore durante l\\'aggiornamento dei metri posati: ' + (error.message || 'Errore sconosciuto'));\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  // Renderizza il passo 1: Selezione del cavo\n  const renderStep1 = () => {\n    return /*#__PURE__*/_jsxDEV(Box, {\n      children: [/*#__PURE__*/_jsxDEV(Typography, {\n        variant: \"h6\",\n        gutterBottom: true,\n        children: \"Seleziona un cavo\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 622,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Paper, {\n        sx: {\n          p: 2,\n          mb: 3\n        },\n        children: [/*#__PURE__*/_jsxDEV(Typography, {\n          variant: \"subtitle1\",\n          gutterBottom: true,\n          children: \"Cerca cavo per ID\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 628,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Grid, {\n          container: true,\n          spacing: 2,\n          alignItems: \"center\",\n          children: [/*#__PURE__*/_jsxDEV(Grid, {\n            item: true,\n            xs: 9,\n            children: /*#__PURE__*/_jsxDEV(TextField, {\n              fullWidth: true,\n              label: \"ID Cavo\",\n              variant: \"outlined\",\n              value: cavoIdInput,\n              onChange: e => setCavoIdInput(e.target.value),\n              placeholder: \"Inserisci l'ID del cavo\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 633,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 632,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Grid, {\n            item: true,\n            xs: 3,\n            children: /*#__PURE__*/_jsxDEV(Button, {\n              fullWidth: true,\n              variant: \"contained\",\n              color: \"primary\",\n              onClick: handleSearchCavoById,\n              disabled: caviLoading || !cavoIdInput.trim(),\n              startIcon: caviLoading ? /*#__PURE__*/_jsxDEV(CircularProgress, {\n                size: 20\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 649,\n                columnNumber: 42\n              }, this) : /*#__PURE__*/_jsxDEV(SearchIcon, {}, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 649,\n                columnNumber: 75\n              }, this),\n              children: \"Cerca\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 643,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 642,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 631,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 627,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Paper, {\n        sx: {\n          p: 2\n        },\n        children: [/*#__PURE__*/_jsxDEV(Typography, {\n          variant: \"subtitle1\",\n          gutterBottom: true,\n          children: \"Seleziona dalla lista\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 659,\n          columnNumber: 11\n        }, this), caviLoading ? /*#__PURE__*/_jsxDEV(Box, {\n          sx: {\n            display: 'flex',\n            justifyContent: 'center',\n            my: 4\n          },\n          children: /*#__PURE__*/_jsxDEV(CircularProgress, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 665,\n            columnNumber: 15\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 664,\n          columnNumber: 13\n        }, this) : cavi.length === 0 ? /*#__PURE__*/_jsxDEV(Alert, {\n          severity: \"info\",\n          children: \"Non ci sono cavi disponibili da installare.\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 668,\n          columnNumber: 13\n        }, this) : /*#__PURE__*/_jsxDEV(List, {\n          sx: {\n            maxHeight: '400px',\n            overflow: 'auto'\n          },\n          children: cavi.map(cavo => /*#__PURE__*/_jsxDEV(React.Fragment, {\n            children: [/*#__PURE__*/_jsxDEV(ListItem, {\n              button: true,\n              onClick: () => handleCavoSelect(cavo),\n              children: [/*#__PURE__*/_jsxDEV(ListItemText, {\n                primary: /*#__PURE__*/_jsxDEV(Box, {\n                  sx: {\n                    display: 'flex',\n                    alignItems: 'center'\n                  },\n                  children: [/*#__PURE__*/_jsxDEV(Typography, {\n                    variant: \"subtitle1\",\n                    children: cavo.id_cavo\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 679,\n                    columnNumber: 27\n                  }, this), isCableSpare(cavo) ? /*#__PURE__*/_jsxDEV(Chip, {\n                    size: \"small\",\n                    label: \"SPARE\",\n                    color: \"error\",\n                    sx: {\n                      ml: 1\n                    }\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 681,\n                    columnNumber: 29\n                  }, this) : isCableInstalled(cavo) ? /*#__PURE__*/_jsxDEV(Chip, {\n                    size: \"small\",\n                    label: \"Installato\",\n                    color: \"success\",\n                    sx: {\n                      ml: 1\n                    }\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 688,\n                    columnNumber: 29\n                  }, this) : /*#__PURE__*/_jsxDEV(Chip, {\n                    size: \"small\",\n                    label: cavo.stato_installazione,\n                    color: getCableStateColor(cavo.stato_installazione),\n                    sx: {\n                      ml: 1\n                    }\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 695,\n                    columnNumber: 29\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 678,\n                  columnNumber: 25\n                }, this),\n                secondary: /*#__PURE__*/_jsxDEV(_Fragment, {\n                  children: [/*#__PURE__*/_jsxDEV(Typography, {\n                    variant: \"body2\",\n                    component: \"span\",\n                    children: [cavo.tipologia || 'N/A', \" - \", cavo.n_conduttori || 'N/A', \" x \", cavo.sezione || 'N/A']\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 706,\n                    columnNumber: 27\n                  }, this), /*#__PURE__*/_jsxDEV(\"br\", {}, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 709,\n                    columnNumber: 27\n                  }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                    variant: \"body2\",\n                    component: \"span\",\n                    children: [\"Da: \", cavo.ubicazione_partenza || 'N/A', \" - A: \", cavo.ubicazione_arrivo || 'N/A']\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 710,\n                    columnNumber: 27\n                  }, this), /*#__PURE__*/_jsxDEV(\"br\", {}, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 713,\n                    columnNumber: 27\n                  }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                    variant: \"body2\",\n                    component: \"span\",\n                    children: [\"Metri teorici: \", cavo.metri_teorici || 'N/A', \" - Metri posati: \", cavo.metratura_reale || '0']\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 714,\n                    columnNumber: 27\n                  }, this)]\n                }, void 0, true)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 676,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(ListItemSecondaryAction, {\n                children: /*#__PURE__*/_jsxDEV(IconButton, {\n                  edge: \"end\",\n                  onClick: e => {\n                    e.stopPropagation(); // Prevent triggering the ListItem click\n                    setSelectedCavo(cavo);\n                    setShowCavoDetailsDialog(true);\n                  },\n                  children: /*#__PURE__*/_jsxDEV(InfoIcon, {}, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 726,\n                    columnNumber: 25\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 721,\n                  columnNumber: 23\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 720,\n                columnNumber: 21\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 675,\n              columnNumber: 19\n            }, this), /*#__PURE__*/_jsxDEV(Divider, {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 730,\n              columnNumber: 19\n            }, this)]\n          }, cavo.id_cavo, true, {\n            fileName: _jsxFileName,\n            lineNumber: 674,\n            columnNumber: 17\n          }, this))\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 672,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 658,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 621,\n      columnNumber: 7\n    }, this);\n  };\n\n  // Renderizza il passo 2: Inserimento metri\n  const renderStep2 = () => {\n    if (!selectedCavo) return null;\n    return /*#__PURE__*/_jsxDEV(Box, {\n      children: [/*#__PURE__*/_jsxDEV(Typography, {\n        variant: \"h6\",\n        gutterBottom: true,\n        children: \"Inserisci metri posati\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 746,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(CavoDetailsView, {\n        cavo: selectedCavo,\n        compact: true,\n        title: \"Dettagli del cavo selezionato\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 750,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Paper, {\n        sx: {\n          p: 3\n        },\n        children: [/*#__PURE__*/_jsxDEV(Typography, {\n          variant: \"subtitle1\",\n          gutterBottom: true,\n          children: \"Inserisci i metri posati\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 757,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Box, {\n          sx: {\n            mb: 3,\n            p: 2,\n            bgcolor: '#f5f5f5',\n            borderRadius: 1\n          },\n          children: [/*#__PURE__*/_jsxDEV(Typography, {\n            variant: \"subtitle2\",\n            gutterBottom: true,\n            sx: {\n              fontWeight: 'bold'\n            },\n            children: \"Informazioni importanti:\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 763,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Typography, {\n            variant: \"body2\",\n            children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n              children: \"Metri teorici del cavo:\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 767,\n              columnNumber: 15\n            }, this), \" \", selectedCavo.metri_teorici || 'N/A', \" m\"]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 766,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Typography, {\n            variant: \"body2\",\n            children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n              children: \"Stato attuale del cavo:\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 770,\n              columnNumber: 15\n            }, this), \" \", selectedCavo.stato_installazione || 'N/A']\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 769,\n            columnNumber: 13\n          }, this), formData.id_bobina && formData.id_bobina !== 'BOBINA_VUOTA' && (() => {\n            const bobina = bobine.find(b => b.id_bobina === formData.id_bobina);\n            return bobina ? /*#__PURE__*/_jsxDEV(Typography, {\n              variant: \"body2\",\n              children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n                children: \"Metri residui bobina:\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 776,\n                columnNumber: 19\n              }, this), \" \", bobina.metri_residui || 0, \" m\"]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 775,\n              columnNumber: 17\n            }, this) : null;\n          })()]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 762,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(TextField, {\n          fullWidth: true,\n          label: \"Metri posati\",\n          variant: \"outlined\",\n          name: \"metri_posati\",\n          type: \"number\",\n          value: formData.metri_posati,\n          onChange: handleFormChange,\n          error: !!formErrors.metri_posati,\n          helperText: formErrors.metri_posati || formWarnings.metri_posati,\n          FormHelperTextProps: {\n            sx: {\n              color: formWarnings.metri_posati && !formErrors.metri_posati ? 'warning.main' : 'error.main'\n            }\n          },\n          sx: {\n            mb: 2\n          }\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 782,\n          columnNumber: 11\n        }, this), formWarnings.metri_posati && !formErrors.metri_posati && /*#__PURE__*/_jsxDEV(Alert, {\n          severity: \"warning\",\n          sx: {\n            mb: 2\n          },\n          children: formWarnings.metri_posati\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 799,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(Alert, {\n          severity: \"info\",\n          sx: {\n            mt: 2\n          },\n          children: \"Inserisci i metri di cavo effettivamente posati. Questo valore aggiorna lo stato di installazione del cavo.\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 804,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 756,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 745,\n      columnNumber: 7\n    }, this);\n  };\n\n  // Renderizza il passo 3: Associazione bobina\n  const renderStep3 = () => {\n    // Funzione per estrarre il numero della bobina dall'ID completo\n    const getBobinaNumber = idBobina => {\n      // L'ID completo è nel formato C{id_cantiere}_B{numero_bobina}\n      if (idBobina && idBobina.includes('_B')) {\n        return idBobina.split('_B')[1];\n      }\n      return idBobina;\n    };\n\n    // Funzione per costruire l'ID completo della bobina\n    const buildFullBobinaId = numeroBobina => {\n      return `C${cantiereId}_B${numeroBobina}`;\n    };\n\n    // Verifica se una bobina ha metri residui sufficienti\n    const hasSufficientMeters = bobina => {\n      if (!bobina || !formData.metri_posati) return true;\n      return parseFloat(bobina.metri_residui) >= parseFloat(formData.metri_posati);\n    };\n\n    // Gestisce l'input diretto del numero della bobina\n    const handleBobinaNumberInput = e => {\n      const numeroBobina = e.target.value.trim();\n\n      // Gestione esplicita dell'input 'v' per bobina vuota\n      if (numeroBobina.toLowerCase() === 'v') {\n        setFormData({\n          ...formData,\n          id_bobina: ''\n        });\n        setFormErrors({\n          ...formErrors,\n          id_bobina_input: null\n        });\n        return;\n      }\n      if (numeroBobina) {\n        // Costruisci l'ID completo\n        const idBobinaCompleto = buildFullBobinaId(numeroBobina);\n\n        // Verifica se la bobina esiste\n        const bobinaEsistente = bobine.find(b => b.id_bobina === idBobinaCompleto);\n        if (bobinaEsistente) {\n          // Verifica se la bobina è in stato OVER o TERMINATA\n          if (bobinaEsistente.stato_bobina === 'Over' || bobinaEsistente.stato_bobina === 'Terminata') {\n            setFormErrors({\n              ...formErrors,\n              id_bobina_input: `La bobina ${numeroBobina} è in stato ${bobinaEsistente.stato_bobina} e non può essere utilizzata`\n            });\n            return;\n          }\n\n          // Verifica compatibilità tra cavo e bobina\n          if (selectedCavo && (bobinaEsistente.tipologia !== selectedCavo.tipologia || String(bobinaEsistente.n_conduttori) !== String(selectedCavo.n_conduttori) || String(bobinaEsistente.sezione) !== String(selectedCavo.sezione))) {\n            // Mostra il dialogo per bobine incompatibili\n            setIncompatibleReel(bobinaEsistente);\n            setShowIncompatibleReelDialog(true);\n            return;\n          }\n\n          // Verifica se la bobina ha metri residui sufficienti\n          if (hasSufficientMeters(bobinaEsistente)) {\n            // Se la bobina esiste e ha metri sufficienti, imposta l'ID completo\n            setFormData({\n              ...formData,\n              id_bobina: idBobinaCompleto\n            });\n            setFormErrors({\n              ...formErrors,\n              id_bobina_input: null\n            });\n          } else {\n            // Se la bobina non ha metri sufficienti, mostra un avviso\n            setFormErrors({\n              ...formErrors,\n              id_bobina_input: `La bobina ${numeroBobina} non ha metri residui sufficienti (${bobinaEsistente.metri_residui} m disponibili)`\n            });\n          }\n        } else {\n          // Se la bobina non esiste, mostra un errore\n          setFormErrors({\n            ...formErrors,\n            id_bobina_input: `Bobina ${numeroBobina} non trovata`\n          });\n        }\n      } else {\n        // Se l'input è vuoto, resetta l'ID bobina\n        setFormData({\n          ...formData,\n          id_bobina: ''\n        });\n        setFormErrors({\n          ...formErrors,\n          id_bobina_input: null\n        });\n      }\n    };\n    return /*#__PURE__*/_jsxDEV(Box, {\n      children: [/*#__PURE__*/_jsxDEV(Typography, {\n        variant: \"h6\",\n        gutterBottom: true,\n        children: \"Associa bobina (opzionale)\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 920,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Paper, {\n        sx: {\n          p: 3\n        },\n        children: [/*#__PURE__*/_jsxDEV(Typography, {\n          variant: \"body1\",\n          paragraph: true,\n          children: \"Puoi associare una bobina al cavo selezionato. Questo \\xE8 opzionale, ma consigliato per tenere traccia dell'utilizzo delle bobine.\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 925,\n          columnNumber: 11\n        }, this), bobineLoading ? /*#__PURE__*/_jsxDEV(Box, {\n          sx: {\n            display: 'flex',\n            justifyContent: 'center',\n            my: 4\n          },\n          children: /*#__PURE__*/_jsxDEV(CircularProgress, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 931,\n            columnNumber: 15\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 930,\n          columnNumber: 13\n        }, this) : /*#__PURE__*/_jsxDEV(Box, {\n          children: [/*#__PURE__*/_jsxDEV(Grid, {\n            container: true,\n            spacing: 2,\n            sx: {\n              mb: 3\n            },\n            children: [/*#__PURE__*/_jsxDEV(Grid, {\n              item: true,\n              xs: 12,\n              md: 6,\n              children: /*#__PURE__*/_jsxDEV(TextField, {\n                fullWidth: true,\n                label: \"Inserisci numero bobina\",\n                variant: \"outlined\",\n                placeholder: \"Inserisci solo il numero (Y)\",\n                helperText: formErrors.id_bobina_input || \"Inserisci solo il numero della bobina (parte Y del codice Cx_By)\",\n                error: !!formErrors.id_bobina_input,\n                onBlur: handleBobinaNumberInput\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 938,\n                columnNumber: 19\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 937,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(Grid, {\n              item: true,\n              xs: 12,\n              md: 6,\n              children: [/*#__PURE__*/_jsxDEV(Typography, {\n                variant: \"body2\",\n                sx: {\n                  mt: 1\n                },\n                children: [\"ID Bobina completo: \", formData.id_bobina || '-']\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 949,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(Button, {\n                variant: \"outlined\",\n                color: \"secondary\",\n                onClick: () => {\n                  setFormData({\n                    ...formData,\n                    id_bobina: ''\n                  });\n                  setFormErrors({\n                    ...formErrors,\n                    id_bobina_input: null\n                  });\n                },\n                sx: {\n                  mt: 1\n                },\n                children: \"Nessuna bobina (bobina vuota)\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 953,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 948,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 936,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(FormControl, {\n            fullWidth: true,\n            children: [/*#__PURE__*/_jsxDEV(InputLabel, {\n              id: \"bobina-select-label\",\n              children: \"Seleziona Bobina dalla lista\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 975,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(Select, {\n              labelId: \"bobina-select-label\",\n              id: \"bobina-select\",\n              name: \"id_bobina\",\n              value: formData.id_bobina,\n              label: \"Seleziona Bobina dalla lista\",\n              onChange: handleFormChange,\n              children: [/*#__PURE__*/_jsxDEV(MenuItem, {\n                value: \"\",\n                children: /*#__PURE__*/_jsxDEV(\"em\", {\n                  children: \"Nessuna bobina\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 985,\n                  columnNumber: 21\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 984,\n                columnNumber: 19\n              }, this), bobine.map(bobina => /*#__PURE__*/_jsxDEV(MenuItem, {\n                value: bobina.id_bobina,\n                disabled: bobina.metri_residui < parseFloat(formData.metri_posati),\n                children: [getBobinaNumber(bobina.id_bobina), \" - \", bobina.tipologia || 'N/A', \" - Residui: \", bobina.metri_residui || 0, \" m\"]\n              }, bobina.id_bobina, true, {\n                fileName: _jsxFileName,\n                lineNumber: 988,\n                columnNumber: 21\n              }, this))]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 976,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(FormHelperText, {\n              children: \"Seleziona una bobina con metri residui sufficienti o lascia vuoto per non associare alcuna bobina\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 997,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 974,\n            columnNumber: 15\n          }, this), formData.id_bobina && /*#__PURE__*/_jsxDEV(Box, {\n            sx: {\n              mt: 3,\n              p: 2,\n              bgcolor: 'background.paper',\n              borderRadius: 1,\n              border: '1px solid #e0e0e0'\n            },\n            children: [/*#__PURE__*/_jsxDEV(Typography, {\n              variant: \"subtitle2\",\n              gutterBottom: true,\n              children: \"Dettagli bobina selezionata\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1005,\n              columnNumber: 19\n            }, this), (() => {\n              const bobina = bobine.find(b => b.id_bobina === formData.id_bobina);\n              if (bobina) {\n                return /*#__PURE__*/_jsxDEV(Grid, {\n                  container: true,\n                  spacing: 2,\n                  children: [/*#__PURE__*/_jsxDEV(Grid, {\n                    item: true,\n                    xs: 12,\n                    md: 6,\n                    children: [/*#__PURE__*/_jsxDEV(Typography, {\n                      variant: \"body2\",\n                      children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n                        children: \"Numero:\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 1015,\n                        columnNumber: 31\n                      }, this), \" \", getBobinaNumber(bobina.id_bobina)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1014,\n                      columnNumber: 29\n                    }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                      variant: \"body2\",\n                      children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n                        children: \"Tipologia:\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 1018,\n                        columnNumber: 31\n                      }, this), \" \", bobina.tipologia || 'N/A']\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1017,\n                      columnNumber: 29\n                    }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                      variant: \"body2\",\n                      children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n                        children: \"Conduttori:\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 1021,\n                        columnNumber: 31\n                      }, this), \" \", bobina.n_conduttori || 'N/A', \" x \", bobina.sezione || 'N/A']\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1020,\n                      columnNumber: 29\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1013,\n                    columnNumber: 27\n                  }, this), /*#__PURE__*/_jsxDEV(Grid, {\n                    item: true,\n                    xs: 12,\n                    md: 6,\n                    children: [/*#__PURE__*/_jsxDEV(Typography, {\n                      variant: \"body2\",\n                      children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n                        children: \"Metri totali:\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 1026,\n                        columnNumber: 31\n                      }, this), \" \", bobina.metri_totali || 0, \" m\"]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1025,\n                      columnNumber: 29\n                    }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                      variant: \"body2\",\n                      children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n                        children: \"Metri residui:\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 1029,\n                        columnNumber: 31\n                      }, this), \" \", bobina.metri_residui || 0, \" m\"]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1028,\n                      columnNumber: 29\n                    }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                      variant: \"body2\",\n                      children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n                        children: \"Stato:\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 1032,\n                        columnNumber: 31\n                      }, this), \" \", bobina.stato_bobina || 'N/A']\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1031,\n                      columnNumber: 29\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1024,\n                    columnNumber: 27\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1012,\n                  columnNumber: 25\n                }, this);\n              }\n              return /*#__PURE__*/_jsxDEV(Typography, {\n                variant: \"body2\",\n                color: \"error\",\n                children: \"Bobina non trovata nel database\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1039,\n                columnNumber: 23\n              }, this);\n            })()]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 1004,\n            columnNumber: 17\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 934,\n          columnNumber: 13\n        }, this), bobine.length === 0 && !bobineLoading && /*#__PURE__*/_jsxDEV(Alert, {\n          severity: \"warning\",\n          sx: {\n            mt: 2\n          },\n          children: \"Non ci sono bobine disponibili. Puoi procedere senza associare una bobina o aggiungere prima una nuova bobina.\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 1050,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 924,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 919,\n      columnNumber: 7\n    }, this);\n  };\n\n  // Renderizza il passo 4: Conferma\n  const renderStep4 = () => {\n    // Funzione per estrarre il numero della bobina dall'ID completo\n    const getBobinaNumber = idBobina => {\n      // L'ID completo è nel formato C{id_cantiere}_B{numero_bobina}\n      if (idBobina && idBobina.includes('_B')) {\n        return idBobina.split('_B')[1];\n      }\n      return idBobina;\n    };\n\n    // Ottieni il numero della bobina se presente\n    let numeroBobina = 'Nessuna';\n    let bobinaInfo = null;\n    if (formData.id_bobina === 'BOBINA_VUOTA') {\n      numeroBobina = 'BOBINA VUOTA';\n    } else if (formData.id_bobina) {\n      numeroBobina = getBobinaNumber(formData.id_bobina);\n      // Trova i dettagli della bobina selezionata\n      bobinaInfo = bobine.find(b => b.id_bobina === formData.id_bobina);\n    }\n\n    // Determina lo stato di installazione\n    const statoInstallazione = determineInstallationStatus(parseFloat(formData.metri_posati), selectedCavo.metri_teorici);\n    return /*#__PURE__*/_jsxDEV(Box, {\n      children: [/*#__PURE__*/_jsxDEV(Typography, {\n        variant: \"h6\",\n        gutterBottom: true,\n        children: \"Conferma inserimento\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 1087,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Paper, {\n        sx: {\n          p: 3\n        },\n        children: [/*#__PURE__*/_jsxDEV(Typography, {\n          variant: \"subtitle1\",\n          gutterBottom: true,\n          children: \"Riepilogo dati\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 1092,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(CavoDetailsView, {\n          cavo: selectedCavo,\n          compact: true,\n          title: \"Dettagli del cavo\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 1097,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Box, {\n          sx: {\n            mt: 3,\n            p: 2,\n            bgcolor: '#f5f5f5',\n            borderRadius: 1\n          },\n          children: [/*#__PURE__*/_jsxDEV(Typography, {\n            variant: \"subtitle2\",\n            gutterBottom: true,\n            sx: {\n              fontWeight: 'bold'\n            },\n            children: \"Informazioni sull'operazione:\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 1105,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Grid, {\n            container: true,\n            spacing: 2,\n            children: [/*#__PURE__*/_jsxDEV(Grid, {\n              item: true,\n              xs: 12,\n              md: 6,\n              children: [/*#__PURE__*/_jsxDEV(Typography, {\n                variant: \"body2\",\n                children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n                  children: \"Metri Posati:\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1111,\n                  columnNumber: 19\n                }, this), \" \", formData.metri_posati, \" m\"]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 1110,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                variant: \"body2\",\n                children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n                  children: \"Stato Installazione:\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1114,\n                  columnNumber: 19\n                }, this), \" \", statoInstallazione]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 1113,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 1109,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Grid, {\n              item: true,\n              xs: 12,\n              md: 6,\n              children: [/*#__PURE__*/_jsxDEV(Typography, {\n                variant: \"body2\",\n                children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n                  children: \"Bobina Associata:\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1119,\n                  columnNumber: 19\n                }, this), \" \", numeroBobina]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 1118,\n                columnNumber: 17\n              }, this), bobinaInfo && /*#__PURE__*/_jsxDEV(Typography, {\n                variant: \"body2\",\n                children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n                  children: \"Metri Residui Bobina:\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1123,\n                  columnNumber: 21\n                }, this), \" \", bobinaInfo.metri_residui, \" m\"]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 1122,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 1117,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 1108,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 1104,\n          columnNumber: 11\n        }, this), bobinaInfo && parseFloat(formData.metri_posati) > parseFloat(bobinaInfo.metri_residui) && /*#__PURE__*/_jsxDEV(Alert, {\n          severity: \"warning\",\n          sx: {\n            mt: 3\n          },\n          children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n            children: \"Attenzione:\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 1132,\n            columnNumber: 15\n          }, this), \" I metri posati (\", formData.metri_posati, \"m) superano i metri residui della bobina (\", bobinaInfo.metri_residui, \"m). Questo porter\\xE0 la bobina in stato OVER.\"]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 1131,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(Alert, {\n          severity: \"info\",\n          sx: {\n            mt: 3\n          },\n          children: [\"Conferma per aggiornare i metri posati e lo stato di installazione del cavo.\", formData.id_bobina && formData.id_bobina !== 'BOBINA_VUOTA' && ' I metri posati verranno sottratti dai metri residui della bobina selezionata.']\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 1137,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 1091,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 1086,\n      columnNumber: 7\n    }, this);\n  };\n\n  // Renderizza il contenuto in base al passo attivo\n  const getStepContent = step => {\n    switch (step) {\n      case 0:\n        return renderStep1();\n      // Seleziona Cavo\n      case 1:\n        return renderStep3();\n      // Associa Bobina\n      case 2:\n        return renderStep2();\n      // Inserisci Metri\n      case 3:\n        return renderStep4();\n      // Conferma\n      default:\n        return 'Passo sconosciuto';\n    }\n  };\n\n  // Gestisce la chiusura del dialogo per cavi già posati\n  const handleCloseAlreadyLaidDialog = () => {\n    setShowAlreadyLaidDialog(false);\n    setAlreadyLaidCavo(null);\n  };\n\n  // Gestisce l'opzione di modificare la bobina di un cavo già posato\n  const handleModifyReel = () => {\n    if (alreadyLaidCavo) {\n      navigate(`/dashboard/cavi/modifica-bobina/${cantiereId}/${alreadyLaidCavo.id_cavo}`);\n    }\n    handleCloseAlreadyLaidDialog();\n  };\n\n  // Gestisce l'opzione di selezionare un altro cavo\n  const handleSelectAnotherCable = () => {\n    handleCloseAlreadyLaidDialog();\n    // Reset del form per selezionare un nuovo cavo\n    setActiveStep(0);\n    setSelectedCavo(null);\n    setCavoIdInput('');\n  };\n\n  // Gestisce la chiusura del dialogo per bobine incompatibili\n  const handleCloseIncompatibleReelDialog = () => {\n    setShowIncompatibleReelDialog(false);\n    setIncompatibleReel(null);\n  };\n\n  // Gestisce l'aggiornamento delle caratteristiche del cavo per farle corrispondere a quelle della bobina\n  const handleUpdateCavoToMatchReel = async () => {\n    if (!selectedCavo || !incompatibleReel) return;\n    try {\n      setLoading(true);\n      // Aggiorna le caratteristiche del cavo\n      await caviService.updateCavoToMatchReel(cantiereId, selectedCavo.id_cavo, incompatibleReel);\n\n      // Aggiorna il cavo selezionato con le nuove caratteristiche\n      const updatedCavo = await caviService.getCavoById(cantiereId, selectedCavo.id_cavo);\n      setSelectedCavo(updatedCavo);\n\n      // Imposta la bobina selezionata\n      setFormData({\n        ...formData,\n        id_bobina: incompatibleReel.id_bobina\n      });\n      onSuccess(`Caratteristiche del cavo ${selectedCavo.id_cavo} aggiornate per corrispondere alla bobina ${incompatibleReel.id_bobina}`);\n      handleCloseIncompatibleReelDialog();\n    } catch (error) {\n      console.error('Errore durante l\\'aggiornamento delle caratteristiche del cavo:', error);\n      onError('Errore durante l\\'aggiornamento delle caratteristiche del cavo: ' + (error.message || 'Errore sconosciuto'));\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  // Gestisce la selezione di un'altra bobina\n  const handleSelectAnotherReel = () => {\n    handleCloseIncompatibleReelDialog();\n    // Reset della bobina selezionata\n    setFormData({\n      ...formData,\n      id_bobina: ''\n    });\n  };\n  return /*#__PURE__*/_jsxDEV(Box, {\n    children: [/*#__PURE__*/_jsxDEV(Stepper, {\n      activeStep: activeStep,\n      sx: {\n        mb: 4\n      },\n      children: steps.map(label => /*#__PURE__*/_jsxDEV(Step, {\n        children: /*#__PURE__*/_jsxDEV(StepLabel, {\n          children: label\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 1235,\n          columnNumber: 13\n        }, this)\n      }, label, false, {\n        fileName: _jsxFileName,\n        lineNumber: 1234,\n        columnNumber: 11\n      }, this))\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 1232,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Box, {\n      sx: {\n        mt: 2,\n        mb: 4\n      },\n      children: getStepContent(activeStep)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 1240,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Box, {\n      sx: {\n        display: 'flex',\n        justifyContent: 'space-between',\n        mt: 4\n      },\n      children: [/*#__PURE__*/_jsxDEV(Button, {\n        variant: \"outlined\",\n        color: \"secondary\",\n        onClick: activeStep === 0 ? () => navigate('/dashboard/cavi/posa') : handleBack,\n        startIcon: /*#__PURE__*/_jsxDEV(ArrowBackIcon, {}, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 1249,\n          columnNumber: 22\n        }, this),\n        disabled: loading,\n        children: activeStep === 0 ? 'Annulla' : 'Indietro'\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 1245,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Button, {\n        variant: \"contained\",\n        color: \"primary\",\n        onClick: activeStep === steps.length - 1 ? handleSubmit : handleNext,\n        endIcon: activeStep === steps.length - 1 ? /*#__PURE__*/_jsxDEV(SaveIcon, {}, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 1259,\n          columnNumber: 54\n        }, this) : /*#__PURE__*/_jsxDEV(ArrowForwardIcon, {}, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 1259,\n          columnNumber: 69\n        }, this),\n        disabled: loading || activeStep === 0 && !selectedCavo,\n        children: loading ? /*#__PURE__*/_jsxDEV(CircularProgress, {\n          size: 24\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 1263,\n          columnNumber: 13\n        }, this) : activeStep === steps.length - 1 ? 'Salva' : 'Avanti'\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 1255,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 1244,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Dialog, {\n      open: showAlreadyLaidDialog,\n      onClose: handleCloseAlreadyLaidDialog,\n      maxWidth: \"sm\",\n      fullWidth: true,\n      children: [/*#__PURE__*/_jsxDEV(DialogTitle, {\n        sx: {\n          bgcolor: 'warning.light'\n        },\n        children: /*#__PURE__*/_jsxDEV(Box, {\n          sx: {\n            display: 'flex',\n            alignItems: 'center',\n            gap: 1\n          },\n          children: [/*#__PURE__*/_jsxDEV(WarningIcon, {\n            color: \"warning\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 1276,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Typography, {\n            variant: \"h6\",\n            children: \"Cavo gi\\xE0 posato\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 1277,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 1275,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 1274,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(DialogContent, {\n        children: alreadyLaidCavo && /*#__PURE__*/_jsxDEV(Box, {\n          sx: {\n            mt: 2\n          },\n          children: [/*#__PURE__*/_jsxDEV(Typography, {\n            variant: \"body1\",\n            paragraph: true,\n            children: [\"Il cavo \", /*#__PURE__*/_jsxDEV(\"strong\", {\n              children: alreadyLaidCavo.id_cavo\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1284,\n              columnNumber: 25\n            }, this), \" risulta gi\\xE0 posato (\", alreadyLaidCavo.metratura_reale || 0, \"m).\"]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 1283,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(Typography, {\n            variant: \"body1\",\n            paragraph: true,\n            children: \"Puoi scegliere di:\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 1286,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(Typography, {\n            variant: \"body2\",\n            component: \"ul\",\n            children: [/*#__PURE__*/_jsxDEV(\"li\", {\n              children: \"Modificare la bobina associata al cavo\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1290,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"li\", {\n              children: \"Selezionare un altro cavo\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1291,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"li\", {\n              children: \"Annullare l'operazione\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1292,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 1289,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 1282,\n          columnNumber: 13\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 1280,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(DialogActions, {\n        sx: {\n          p: 2,\n          justifyContent: 'space-between'\n        },\n        children: [/*#__PURE__*/_jsxDEV(Button, {\n          onClick: handleCloseAlreadyLaidDialog,\n          color: \"secondary\",\n          children: \"Annulla operazione\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 1298,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Box, {\n          children: [/*#__PURE__*/_jsxDEV(Button, {\n            onClick: handleSelectAnotherCable,\n            color: \"primary\",\n            sx: {\n              mr: 1\n            },\n            children: \"Seleziona altro cavo\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 1302,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Button, {\n            onClick: handleModifyReel,\n            variant: \"contained\",\n            color: \"primary\",\n            children: \"Modifica bobina\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 1305,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 1301,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 1297,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 1273,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(IncompatibleReelDialog, {\n      open: showIncompatibleReelDialog,\n      onClose: handleCloseIncompatibleReelDialog,\n      cavo: selectedCavo,\n      bobina: incompatibleReel,\n      onUpdateCavo: handleUpdateCavoToMatchReel,\n      onSelectAnotherReel: handleSelectAnotherReel\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 1313,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Dialog, {\n      open: showCavoDetailsDialog,\n      onClose: () => setShowCavoDetailsDialog(false),\n      maxWidth: \"md\",\n      fullWidth: true,\n      children: [/*#__PURE__*/_jsxDEV(DialogTitle, {\n        children: /*#__PURE__*/_jsxDEV(Box, {\n          sx: {\n            display: 'flex',\n            alignItems: 'center',\n            gap: 1\n          },\n          children: [/*#__PURE__*/_jsxDEV(InfoIcon, {\n            color: \"primary\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 1331,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Typography, {\n            variant: \"h6\",\n            children: \"Dettagli Cavo\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 1332,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 1330,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 1329,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(DialogContent, {\n        children: /*#__PURE__*/_jsxDEV(CavoDetailsView, {\n          cavo: selectedCavo\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 1336,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 1335,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(DialogActions, {\n        children: /*#__PURE__*/_jsxDEV(Button, {\n          onClick: () => setShowCavoDetailsDialog(false),\n          color: \"primary\",\n          children: \"Chiudi\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 1339,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 1338,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 1323,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 1231,\n    columnNumber: 5\n  }, this);\n};\n_s(InserisciMetriForm, \"6FkN6PzcU3fEI2WPvKMUCdC6WqU=\", false, function () {\n  return [useNavigate];\n});\n_c = InserisciMetriForm;\nexport default InserisciMetriForm;\nvar _c;\n$RefreshReg$(_c, \"InserisciMetriForm\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "axios", "Box", "Paper", "Typography", "TextField", "<PERSON><PERSON>", "Stepper", "Step", "<PERSON><PERSON><PERSON><PERSON>", "Grid", "FormControl", "InputLabel", "Select", "MenuItem", "List", "ListItem", "ListItemText", "ListItemSecondaryAction", "Divider", "<PERSON><PERSON>", "CircularProgress", "FormHelperText", "IconButton", "Chip", "Dialog", "DialogTitle", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "DialogActions", "Search", "SearchIcon", "Save", "SaveIcon", "ArrowBack", "ArrowBackIcon", "ArrowForward", "ArrowForwardIcon", "Cancel", "CancelIcon", "CheckCircle", "CheckCircleIcon", "Warning", "WarningIcon", "Info", "InfoIcon", "useNavigate", "caviService", "axiosInstance", "IncompatibleReelDialog", "CavoDetailsView", "CABLE_STATES", "REEL_STATES", "determineCableState", "determineReelState", "canModifyCable", "isCableSpare", "isCableInstalled", "getCableStateColor", "getReelStateColor", "parcoCaviService", "redirectToVisualizzaCavi", "jsxDEV", "_jsxDEV", "Fragment", "_Fragment", "InserisciMetriForm", "cantiereId", "onSuccess", "onError", "_s", "navigate", "activeStep", "setActiveStep", "loading", "setLoading", "caviLoading", "setCaviLoading", "bob<PERSON><PERSON><PERSON><PERSON>", "setBobineLoading", "cavi", "<PERSON><PERSON><PERSON>", "bobine", "set<PERSON>ob<PERSON>", "selected<PERSON><PERSON><PERSON>", "setSelectedCavo", "cavoIdInput", "setCavoIdInput", "formData", "setFormData", "id_cavo", "metri_posati", "id_bobina", "formErrors", "setFormErrors", "formWarnings", "setForm<PERSON><PERSON>nings", "showIncompatibleReelDialog", "setShowIncompatibleReelDialog", "incompatibleReel", "setIncompatibleReel", "showAlreadyLaidDialog", "setShowAlreadyLaidDialog", "alreadyLaidCavo", "setAlreadyLaidCavo", "showCavoDetailsDialog", "setShowCavoDetailsDialog", "steps", "loadCavi", "loadBobine", "console", "log", "caviData", "get<PERSON><PERSON>", "length", "loadError", "error", "isNetworkError", "response", "code", "message", "includes", "Promise", "resolve", "setTimeout", "token", "localStorage", "getItem", "API_URL", "defaults", "baseURL", "retryResponse", "get", "headers", "timeout", "data", "retryError", "errorMessage", "detail", "bobine<PERSON><PERSON>", "getBobine", "b<PERSON><PERSON><PERSON><PERSON><PERSON>", "filter", "bobina", "stato_bobina", "tipologia", "n_conduttori", "sezione", "bobineCom<PERSON><PERSON><PERSON><PERSON>", "sort", "a", "b", "metri_residui", "handleSearchCavoById", "trim", "cavoData", "getCavoById", "searchError", "cavoLocale", "find", "c", "stato_installazione", "metratura_reale", "modificato_manualmente", "handleCavoSelect", "status", "cavo", "window", "confirm", "reactivateSpare", "then", "updatedCavo", "catch", "cavoId", "handleFormChange", "e", "name", "value", "target", "validateField", "warning", "isNaN", "parseFloat", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "metri_te<PERSON>ci", "prev", "validateForm", "<PERSON><PERSON><PERSON><PERSON>", "errors", "warnings", "handleNext", "prevActiveStep", "handleBack", "handleReset", "determineInstallationStatus", "metriTeorici", "handleSubmit", "idBobina", "statoInstallazione", "forceOver", "confirmMessage", "updateMetri<PERSON><PERSON><PERSON>", "successMessage", "renderStep1", "children", "variant", "gutterBottom", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "sx", "p", "mb", "container", "spacing", "alignItems", "item", "xs", "fullWidth", "label", "onChange", "placeholder", "color", "onClick", "disabled", "startIcon", "size", "display", "justifyContent", "my", "severity", "maxHeight", "overflow", "map", "button", "primary", "ml", "secondary", "component", "ubicazione_partenza", "ubicazione_arrivo", "edge", "stopPropagation", "renderStep2", "compact", "title", "bgcolor", "borderRadius", "fontWeight", "type", "helperText", "FormHelperTextProps", "mt", "renderStep3", "getBobinaNumber", "split", "buildFullBobinaId", "numeroBobina", "hasSufficientMeters", "handleBobinaNumberInput", "toLowerCase", "id_bobina_input", "idBobinaCompleto", "<PERSON><PERSON><PERSON>", "String", "paragraph", "md", "onBlur", "id", "labelId", "border", "metri_totali", "renderStep4", "bobinaInfo", "getStepContent", "step", "handleCloseAlreadyLaidDialog", "handleModifyReel", "handleSelectAnotherCable", "handleCloseIncompatibleReelDialog", "handleUpdateCavoToMatchReel", "updateCavoToMatchReel", "handleSelectAnotherReel", "endIcon", "open", "onClose", "max<PERSON><PERSON><PERSON>", "gap", "mr", "onUpdateCavo", "onSelectAnotherReel", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/PycharmProjects/CMS/webapp/frontend/src/components/cavi/InserisciMetriForm.js"], "sourcesContent": ["import React, { useState, useEffect } from 'react';\nimport axios from 'axios';\nimport {\n  Box,\n  Paper,\n  Typography,\n  TextField,\n  Button,\n  Stepper,\n  Step,\n  StepLabel,\n  Grid,\n  FormControl,\n  InputLabel,\n  Select,\n  MenuItem,\n  List,\n  ListItem,\n  ListItemText,\n  ListItemSecondaryAction,\n  Divider,\n  Alert,\n  CircularProgress,\n  FormHelperText,\n  IconButton,\n  Chip,\n  Dialog,\n  DialogTitle,\n  DialogContent,\n  DialogActions\n} from '@mui/material';\nimport {\n  Search as SearchIcon,\n  Save as SaveIcon,\n  ArrowBack as ArrowBackIcon,\n  ArrowForward as ArrowForwardIcon,\n  Cancel as CancelIcon,\n  CheckCircle as CheckCircleIcon,\n  Warning as WarningIcon,\n  Info as InfoIcon\n} from '@mui/icons-material';\nimport { useNavigate } from 'react-router-dom';\nimport caviService from '../../services/caviService';\nimport axiosInstance from '../../services/axiosConfig';\nimport IncompatibleReelDialog from './IncompatibleReelDialog';\nimport CavoDetailsView from './CavoDetailsView';\nimport {\n  CABLE_STATES,\n  REEL_STATES,\n  determineCableState,\n  determineReelState,\n  canModifyCable,\n  isCableSpare,\n  isCableInstalled,\n  getCableStateColor,\n  getReelStateColor\n} from '../../utils/stateUtils';\nimport parcoCaviService from '../../services/parcoCaviService';\nimport { redirectToVisualizzaCavi } from '../../utils/navigationUtils';\n\n/**\n * Componente per l'inserimento dei metri posati di un cavo\n *\n * @param {Object} props - Proprietà del componente\n * @param {string} props.cantiereId - ID del cantiere\n * @param {Function} props.onSuccess - Funzione chiamata al successo dell'operazione\n * @param {Function} props.onError - Funzione chiamata in caso di errore\n */\nconst InserisciMetriForm = ({ cantiereId, onSuccess, onError }) => {\n  const navigate = useNavigate();\n\n  // Stati per la gestione del form multi-step\n  const [activeStep, setActiveStep] = useState(0);\n  const [loading, setLoading] = useState(false);\n  const [caviLoading, setCaviLoading] = useState(false);\n  const [bobineLoading, setBobineLoading] = useState(false);\n\n  // Stati per i dati\n  const [cavi, setCavi] = useState([]);\n  const [bobine, setBobine] = useState([]);\n  const [selectedCavo, setSelectedCavo] = useState(null);\n  const [cavoIdInput, setCavoIdInput] = useState('');\n\n  // Stati per il form\n  const [formData, setFormData] = useState({\n    id_cavo: '',\n    metri_posati: '',\n    id_bobina: ''\n  });\n\n  // Stati per la validazione\n  const [formErrors, setFormErrors] = useState({});\n  const [formWarnings, setFormWarnings] = useState({});\n\n  // Stati per i dialoghi speciali\n  const [showIncompatibleReelDialog, setShowIncompatibleReelDialog] = useState(false);\n  const [incompatibleReel, setIncompatibleReel] = useState(null);\n  const [showAlreadyLaidDialog, setShowAlreadyLaidDialog] = useState(false);\n  const [alreadyLaidCavo, setAlreadyLaidCavo] = useState(null);\n  const [showCavoDetailsDialog, setShowCavoDetailsDialog] = useState(false);\n\n  // Definizione dei passi del form\n  const steps = ['Seleziona Cavo', 'Associa Bobina', 'Inserisci Metri', 'Conferma'];\n\n  // Carica la lista dei cavi all'avvio\n  useEffect(() => {\n    loadCavi();\n  }, [cantiereId]);\n\n  // Carica la lista delle bobine quando necessario\n  useEffect(() => {\n    if (activeStep === 1) {\n      // Carica le bobine quando si passa al passo \"Associa Bobina\"\n      loadBobine();\n    }\n  }, [activeStep, cantiereId]);\n\n  // Funzione per caricare i cavi\n  const loadCavi = async () => {\n    try {\n      setCaviLoading(true);\n      console.log(`Caricamento cavi per il cantiere ${cantiereId}...`);\n\n      // Carica tutti i cavi, inclusi quelli SPARE e installati\n      try {\n        const caviData = await caviService.getCavi(cantiereId);\n        console.log(`Caricati ${caviData.length} cavi`);\n\n        // Mostra tutti i cavi (da installare, in corso, installati e SPARE)\n        // Questo permette di vedere anche i cavi già installati per eventuale modifica della bobina\n        setCavi(caviData);\n      } catch (loadError) {\n        console.error('Errore nel primo tentativo di caricamento dei cavi:', loadError);\n\n        // Se è un errore di rete, prova un secondo tentativo dopo una breve pausa\n        if (loadError.isNetworkError || !loadError.response ||\n            loadError.code === 'ECONNABORTED' ||\n            (loadError.message && loadError.message.includes('Network Error'))) {\n\n          console.log('Errore di rete, tentativo di recupero dopo 1 secondo...');\n          await new Promise(resolve => setTimeout(resolve, 1000));\n\n          try {\n            // Secondo tentativo con timeout aumentato\n            const token = localStorage.getItem('token');\n            const API_URL = axiosInstance.defaults.baseURL;\n\n            // Usa l'ID cantiere originale per la richiesta alternativa\n            const retryResponse = await axios.get(\n              `${API_URL}/cavi/${cantiereId}`,\n              {\n                headers: {\n                  'Authorization': `Bearer ${token}`\n                },\n                timeout: 30000 // 30 secondi\n              }\n            );\n\n            console.log(`Recupero riuscito al secondo tentativo: ${retryResponse.data.length} cavi`);\n            setCavi(retryResponse.data);\n          } catch (retryError) {\n            console.error('Anche il secondo tentativo è fallito:', retryError);\n            throw retryError;\n          }\n        } else {\n          throw loadError;\n        }\n      }\n    } catch (error) {\n      console.error('Errore nel caricamento dei cavi:', error);\n\n      // Messaggio di errore più dettagliato\n      let errorMessage = 'Errore nel caricamento dei cavi';\n\n      if (error.isNetworkError) {\n        errorMessage = 'Errore di connessione al server. Verifica la rete e riprova.';\n      } else if (error.detail) {\n        errorMessage = error.detail;\n      } else if (error.message) {\n        errorMessage = error.message;\n      }\n\n      onError(errorMessage);\n    } finally {\n      setCaviLoading(false);\n    }\n  };\n\n  // Funzione per caricare le bobine\n  const loadBobine = async () => {\n    try {\n      setBobineLoading(true);\n\n      // Carica solo le bobine disponibili o in uso\n      const bobineData = await parcoCaviService.getBobine(cantiereId);\n\n      // Filtra le bobine per stato (disponibile o in uso) e per compatibilità con il cavo selezionato\n      let bobineUtilizzabili = bobineData.filter(bobina =>\n        (bobina.stato_bobina === 'Disponibile' || bobina.stato_bobina === 'In Uso') &&\n        bobina.stato_bobina !== 'Over' && bobina.stato_bobina !== 'Terminata'\n      );\n\n      // Se c'è un cavo selezionato, filtra ulteriormente per caratteristiche del cavo\n      if (selectedCavo) {\n        // Filtra per tipologia, numero conduttori e sezione se disponibili\n        if (selectedCavo.tipologia && selectedCavo.n_conduttori && selectedCavo.sezione) {\n          const bobineCompatibili = bobineUtilizzabili.filter(bobina =>\n            bobina.tipologia === selectedCavo.tipologia &&\n            bobina.n_conduttori === selectedCavo.n_conduttori &&\n            bobina.sezione === selectedCavo.sezione\n          );\n\n          // Se ci sono bobine compatibili, usa quelle, altrimenti mostra tutte le bobine utilizzabili\n          if (bobineCompatibili.length > 0) {\n            bobineUtilizzabili = bobineCompatibili;\n          } else {\n            console.log('Nessuna bobina compatibile trovata, mostro tutte le bobine disponibili');\n          }\n        }\n\n        // Ordina le bobine per metri residui (decrescente)\n        bobineUtilizzabili.sort((a, b) => b.metri_residui - a.metri_residui);\n      }\n\n      setBobine(bobineUtilizzabili);\n    } catch (error) {\n      console.error('Errore nel caricamento delle bobine:', error);\n      onError('Errore nel caricamento delle bobine: ' + (error.message || 'Errore sconosciuto'));\n    } finally {\n      setBobineLoading(false);\n    }\n  };\n\n  // Gestisce la ricerca di un cavo per ID\n  const handleSearchCavoById = async () => {\n    if (!cavoIdInput.trim()) {\n      onError('Inserisci un ID cavo valido');\n      return;\n    }\n\n    try {\n      setCaviLoading(true);\n      console.log(`Ricerca cavo con ID: ${cavoIdInput.trim()} nel cantiere ${cantiereId}`);\n\n      // Primo tentativo di ricerca\n      let cavoData;\n      try {\n        cavoData = await caviService.getCavoById(cantiereId, cavoIdInput.trim());\n        console.log('Cavo trovato con successo:', cavoData);\n      } catch (searchError) {\n        console.error('Errore nel primo tentativo di ricerca:', searchError);\n\n        // Se è un errore di rete, prova a cercare il cavo nella lista locale\n        if (searchError.isNetworkError) {\n          console.log('Tentativo di ricerca nella lista locale dei cavi...');\n          const cavoLocale = cavi.find(c => c.id_cavo === cavoIdInput.trim());\n\n          if (cavoLocale) {\n            console.log('Cavo trovato nella lista locale:', cavoLocale);\n            cavoData = cavoLocale;\n          } else {\n            console.error('Cavo non trovato nemmeno nella lista locale');\n            throw searchError; // Rilancia l'errore originale\n          }\n        } else {\n          throw searchError; // Rilancia l'errore se non è di rete\n        }\n      }\n\n      // Verifica se il cavo è già installato\n      if (cavoData.stato_installazione === 'Installato' || (cavoData.metratura_reale && cavoData.metratura_reale > 0)) {\n        console.log('Cavo già installato, mostra dialogo:', cavoData);\n        // Mostra il dialogo per cavi già posati\n        setAlreadyLaidCavo(cavoData);\n        setShowAlreadyLaidDialog(true);\n        setCaviLoading(false);\n        return;\n      }\n\n      // Verifica se il cavo è SPARE\n      if (cavoData.modificato_manualmente === 3) {\n        console.log('Cavo SPARE trovato:', cavoData);\n        // Gestione del cavo SPARE - verrà gestito in handleCavoSelect\n      }\n\n      // Seleziona il cavo e passa al passo successivo\n      console.log('Cavo selezionato, passaggio al passo successivo');\n      handleCavoSelect(cavoData);\n    } catch (error) {\n      console.error('Errore nella ricerca del cavo:', error);\n\n      // Messaggio di errore più dettagliato\n      let errorMessage = 'Cavo non trovato o errore nella ricerca';\n\n      if (error.isNetworkError) {\n        errorMessage = 'Errore di connessione al server. Verifica la rete e riprova.';\n      } else if (error.status === 404) {\n        errorMessage = `Cavo con ID \"${cavoIdInput.trim()}\" non trovato nel cantiere ${cantiereId}`;\n      } else if (error.detail) {\n        errorMessage = error.detail;\n      } else if (error.message) {\n        errorMessage = error.message;\n      }\n\n      onError(errorMessage);\n    } finally {\n      setCaviLoading(false);\n    }\n  };\n\n  // Gestisce la selezione di un cavo\n  const handleCavoSelect = (cavo) => {\n    // Verifica se il cavo è già installato\n    if (cavo.stato_installazione === 'Installato' || (cavo.metratura_reale && cavo.metratura_reale > 0)) {\n      // Mostra il dialogo per cavi già posati\n      setAlreadyLaidCavo(cavo);\n      setShowAlreadyLaidDialog(true);\n      return;\n    }\n    // Verifica se il cavo è SPARE (modificato_manualmente = 3)\n    else if (cavo.modificato_manualmente === 3) {\n      // Chiedi conferma all'utente per riattivare il cavo SPARE\n      if (window.confirm(`Il cavo ${cavo.id_cavo} è marcato come SPARE. Vuoi riattivarlo?`)) {\n        // Riattiva il cavo SPARE (imposta modificato_manualmente = 0)\n        reactivateSpare(cavo.id_cavo).then(() => {\n          // Aggiorna il cavo selezionato con modificato_manualmente = 0\n          const updatedCavo = { ...cavo, modificato_manualmente: 0 };\n          setSelectedCavo(updatedCavo);\n          setFormData({\n            ...formData,\n            id_cavo: updatedCavo.id_cavo,\n            metri_posati: ''\n          });\n          // Passa al passo successivo (Associa Bobina)\n          setActiveStep(1);\n        }).catch(error => {\n          console.error('Errore durante la riattivazione del cavo SPARE:', error);\n          onError('Errore durante la riattivazione del cavo SPARE: ' + (error.message || 'Errore sconosciuto'));\n        });\n      } else {\n        // L'utente ha annullato la riattivazione\n        return;\n      }\n    } else {\n      // Cavo normale (non SPARE e non installato)\n      setSelectedCavo(cavo);\n      setFormData({\n        ...formData,\n        id_cavo: cavo.id_cavo,\n        metri_posati: ''\n      });\n      // Passa al passo successivo (Associa Bobina)\n      setActiveStep(1);\n    }\n  };\n\n  // Funzione per riattivare un cavo SPARE\n  const reactivateSpare = async (cavoId) => {\n    try {\n      // Chiamata API per riattivare il cavo SPARE\n      await caviService.reactivateSpare(cantiereId, cavoId);\n      onSuccess(`Cavo ${cavoId} riattivato con successo`);\n      return true;\n    } catch (error) {\n      console.error('Errore durante la riattivazione del cavo SPARE:', error);\n      onError('Errore durante la riattivazione del cavo SPARE: ' + (error.message || 'Errore sconosciuto'));\n      throw error;\n    }\n  };\n\n  // Gestisce il cambio dei valori nel form\n  const handleFormChange = (e) => {\n    const { name, value } = e.target;\n    setFormData({\n      ...formData,\n      [name]: value\n    });\n\n    // Validazione in tempo reale\n    validateField(name, value);\n  };\n\n  // Validazione di un campo\n  const validateField = (name, value) => {\n    let error = null;\n    let warning = null;\n\n    if (name === 'metri_posati') {\n      // Controllo input vuoto\n      if (!value || value.trim() === '') {\n        error = 'Inserire un valore per i metri posati';\n        return false;\n      }\n\n      // Controllo formato numerico\n      if (isNaN(parseFloat(value)) || parseFloat(value) <= 0) {\n        error = 'Inserire un valore numerico positivo';\n        return false;\n      }\n\n      const metriPosati = parseFloat(value);\n\n      // Controllo metri teorici cavo\n      if (selectedCavo && selectedCavo.metri_teorici && metriPosati > parseFloat(selectedCavo.metri_teorici)) {\n        warning = `I metri posati (${metriPosati}m) superano i metri teorici del cavo (${selectedCavo.metri_teorici}m)`;\n      }\n\n      // Controllo metri residui bobina (se bobina reale)\n      if (formData.id_bobina && formData.id_bobina !== 'BOBINA_VUOTA') {\n        const bobina = bobine.find(b => b.id_bobina === formData.id_bobina);\n        if (bobina && metriPosati > parseFloat(bobina.metri_residui)) {\n          warning = `I metri posati (${metriPosati}m) superano i metri residui della bobina (${bobina.metri_residui}m). La bobina andrà in stato OVER.`;\n        }\n      }\n    }\n\n    // Aggiorna gli errori\n    setFormErrors(prev => ({\n      ...prev,\n      [name]: error\n    }));\n\n    // Aggiorna gli avvisi\n    setFormWarnings(prev => ({\n      ...prev,\n      [name]: warning\n    }));\n\n    return !error;\n  };\n\n  // Validazione completa del form\n  const validateForm = () => {\n    let isValid = true;\n    const errors = {};\n    const warnings = {};\n\n    // Validazione metri posati\n    if (!formData.metri_posati || formData.metri_posati.trim() === '') {\n      errors.metri_posati = 'Inserire un valore per i metri posati';\n      isValid = false;\n    } else if (isNaN(parseFloat(formData.metri_posati)) || parseFloat(formData.metri_posati) <= 0) {\n      errors.metri_posati = 'Inserire un valore numerico positivo';\n      isValid = false;\n    } else {\n      const metriPosati = parseFloat(formData.metri_posati);\n\n      // Controllo metri teorici cavo\n      if (selectedCavo && selectedCavo.metri_teorici && metriPosati > parseFloat(selectedCavo.metri_teorici)) {\n        warnings.metri_posati = `I metri posati (${metriPosati}m) superano i metri teorici del cavo (${selectedCavo.metri_teorici}m)`;\n\n        // Chiedi conferma all'utente\n        if (!window.confirm(`ATTENZIONE: I metri posati (${metriPosati}m) superano i metri teorici del cavo (${selectedCavo.metri_teorici}m).\\n\\nVuoi continuare comunque?`)) {\n          isValid = false;\n        }\n      }\n\n      // Controllo metri residui bobina (se bobina reale)\n      if (isValid && formData.id_bobina && formData.id_bobina !== 'BOBINA_VUOTA') {\n        const bobina = bobine.find(b => b.id_bobina === formData.id_bobina);\n        if (bobina && metriPosati > parseFloat(bobina.metri_residui)) {\n          warnings.metri_posati = `I metri posati (${metriPosati}m) superano i metri residui della bobina (${bobina.metri_residui}m). La bobina andrà in stato OVER.`;\n\n          // Chiedi conferma all'utente\n          if (!window.confirm(`ATTENZIONE: I metri posati (${metriPosati}m) superano i metri residui della bobina (${bobina.metri_residui}m).\\n\\nQuesto porterà la bobina in stato OVER.\\n\\nVuoi continuare?`)) {\n            isValid = false;\n          }\n        }\n      }\n    }\n\n    setFormErrors(errors);\n    setFormWarnings(warnings);\n    return isValid;\n  };\n\n  // Gestisce il passaggio al passo successivo\n  const handleNext = () => {\n    if (activeStep === 2) {\n      // Validazione prima di passare al passo successivo (da Inserisci Metri a Conferma)\n      if (!validateForm()) {\n        return;\n      }\n    } else if (activeStep === 1) {\n      // Carica le bobine prima di passare al passo successivo (da Associa Bobina a Inserisci Metri)\n      loadBobine();\n    }\n\n    setActiveStep((prevActiveStep) => prevActiveStep + 1);\n  };\n\n  // Gestisce il ritorno al passo precedente\n  const handleBack = () => {\n    setActiveStep((prevActiveStep) => prevActiveStep - 1);\n  };\n\n  // Gestisce il reset del form\n  const handleReset = () => {\n    setActiveStep(0);\n    setSelectedCavo(null);\n    setCavoIdInput('');\n    setFormData({\n      id_cavo: '',\n      metri_posati: '',\n      id_bobina: ''\n    });\n    setFormErrors({});\n    setFormWarnings({});\n  };\n\n  // Determina lo stato di installazione in base ai metri posati\n  // Utilizziamo la funzione dalla utility stateUtils\n  const determineInstallationStatus = (metriPosati, metriTeorici) => {\n    return determineCableState(metriPosati, metriTeorici);\n  };\n\n  // Gestisce l'invio del form\n  const handleSubmit = async () => {\n    try {\n      setLoading(true);\n\n      // Validazione finale\n      if (!validateForm()) {\n        setLoading(false);\n        return;\n      }\n\n      // Prepara i dati da inviare\n      const metriPosati = parseFloat(formData.metri_posati);\n\n      // Gestione speciale per BOBINA_VUOTA\n      let idBobina = formData.id_bobina;\n      // Se non è selezionata alcuna bobina, imposta esplicitamente a null\n      if (!idBobina || idBobina === '') {\n        idBobina = null;\n        console.log('Nessuna bobina selezionata, impostando a null');\n      } else if (idBobina === 'BOBINA_VUOTA') {\n        console.log('Usando BOBINA_VUOTA per il cavo');\n        // Assicurati che BOBINA_VUOTA venga passato come stringa e non come null\n        idBobina = 'BOBINA_VUOTA';\n      } else {\n        console.log(`Usando bobina reale: ${idBobina}`);\n      }\n\n      // Determina lo stato di installazione\n      const statoInstallazione = determineInstallationStatus(metriPosati, selectedCavo.metri_teorici);\n\n      // Verifica se è necessario forzare lo stato OVER della bobina\n      let forceOver = false;\n      if (idBobina && idBobina !== 'BOBINA_VUOTA') {\n        const bobina = bobine.find(b => b.id_bobina === idBobina);\n        if (bobina && metriPosati > parseFloat(bobina.metri_residui)) {\n          forceOver = true;\n          console.log(`Forzando stato OVER per la bobina ${idBobina} (metri posati > metri residui)`);\n        }\n      }\n\n      // Log per debug\n      console.log('Invio dati:', {\n        cantiereId,\n        cavoId: formData.id_cavo,\n        metriPosati,\n        idBobina,\n        forceOver,\n        statoInstallazione\n      });\n\n      // Conferma finale prima dell'invio\n      const confirmMessage = `Confermi l'aggiornamento del cavo ${formData.id_cavo} con ${metriPosati}m posati?`;\n      if (!window.confirm(confirmMessage)) {\n        setLoading(false);\n        return;\n      }\n\n      // Chiamata API\n      console.log('Invio richiesta updateMetriPosati con i seguenti parametri:');\n      console.log('- cantiereId:', cantiereId);\n      console.log('- id_cavo:', formData.id_cavo);\n      console.log('- metri_posati:', metriPosati);\n      console.log('- id_bobina:', idBobina, typeof idBobina);\n      console.log('- forceOver:', forceOver);\n\n      await caviService.updateMetriPosati(\n        cantiereId,\n        formData.id_cavo,\n        metriPosati,\n        idBobina,\n        forceOver\n      );\n\n      // Messaggio di successo con dettagli sulla bobina\n      let successMessage = `Metri posati aggiornati con successo. Stato cavo: ${statoInstallazione}`;\n      if (idBobina === 'BOBINA_VUOTA') {\n        successMessage += '. Cavo associato a BOBINA VUOTA';\n      } else if (idBobina) {\n        const bobina = bobine.find(b => b.id_bobina === idBobina);\n        if (bobina) {\n          successMessage += `. Cavo associato alla bobina ${idBobina}`;\n        }\n      }\n\n      // Gestione successo\n      onSuccess(successMessage);\n\n      // Reset del form\n      handleReset();\n\n      // Ricarica i cavi\n      loadCavi();\n    } catch (error) {\n      console.error('Errore durante l\\'aggiornamento dei metri posati:', error);\n      onError('Errore durante l\\'aggiornamento dei metri posati: ' + (error.message || 'Errore sconosciuto'));\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  // Renderizza il passo 1: Selezione del cavo\n  const renderStep1 = () => {\n    return (\n      <Box>\n        <Typography variant=\"h6\" gutterBottom>\n          Seleziona un cavo\n        </Typography>\n\n        {/* Ricerca per ID */}\n        <Paper sx={{ p: 2, mb: 3 }}>\n          <Typography variant=\"subtitle1\" gutterBottom>\n            Cerca cavo per ID\n          </Typography>\n          <Grid container spacing={2} alignItems=\"center\">\n            <Grid item xs={9}>\n              <TextField\n                fullWidth\n                label=\"ID Cavo\"\n                variant=\"outlined\"\n                value={cavoIdInput}\n                onChange={(e) => setCavoIdInput(e.target.value)}\n                placeholder=\"Inserisci l'ID del cavo\"\n              />\n            </Grid>\n            <Grid item xs={3}>\n              <Button\n                fullWidth\n                variant=\"contained\"\n                color=\"primary\"\n                onClick={handleSearchCavoById}\n                disabled={caviLoading || !cavoIdInput.trim()}\n                startIcon={caviLoading ? <CircularProgress size={20} /> : <SearchIcon />}\n              >\n                Cerca\n              </Button>\n            </Grid>\n          </Grid>\n        </Paper>\n\n        {/* Lista cavi */}\n        <Paper sx={{ p: 2 }}>\n          <Typography variant=\"subtitle1\" gutterBottom>\n            Seleziona dalla lista\n          </Typography>\n\n          {caviLoading ? (\n            <Box sx={{ display: 'flex', justifyContent: 'center', my: 4 }}>\n              <CircularProgress />\n            </Box>\n          ) : cavi.length === 0 ? (\n            <Alert severity=\"info\">\n              Non ci sono cavi disponibili da installare.\n            </Alert>\n          ) : (\n            <List sx={{ maxHeight: '400px', overflow: 'auto' }}>\n              {cavi.map((cavo) => (\n                <React.Fragment key={cavo.id_cavo}>\n                  <ListItem button onClick={() => handleCavoSelect(cavo)}>\n                    <ListItemText\n                      primary={\n                        <Box sx={{ display: 'flex', alignItems: 'center' }}>\n                          <Typography variant=\"subtitle1\">{cavo.id_cavo}</Typography>\n                          {isCableSpare(cavo) ? (\n                            <Chip\n                              size=\"small\"\n                              label=\"SPARE\"\n                              color=\"error\"\n                              sx={{ ml: 1 }}\n                            />\n                          ) : isCableInstalled(cavo) ? (\n                            <Chip\n                              size=\"small\"\n                              label=\"Installato\"\n                              color=\"success\"\n                              sx={{ ml: 1 }}\n                            />\n                          ) : (\n                            <Chip\n                              size=\"small\"\n                              label={cavo.stato_installazione}\n                              color={getCableStateColor(cavo.stato_installazione)}\n                              sx={{ ml: 1 }}\n                            />\n                          )}\n                        </Box>\n                      }\n                      secondary={\n                        <>\n                          <Typography variant=\"body2\" component=\"span\">\n                            {cavo.tipologia || 'N/A'} - {cavo.n_conduttori || 'N/A'} x {cavo.sezione || 'N/A'}\n                          </Typography>\n                          <br />\n                          <Typography variant=\"body2\" component=\"span\">\n                            Da: {cavo.ubicazione_partenza || 'N/A'} - A: {cavo.ubicazione_arrivo || 'N/A'}\n                          </Typography>\n                          <br />\n                          <Typography variant=\"body2\" component=\"span\">\n                            Metri teorici: {cavo.metri_teorici || 'N/A'} - Metri posati: {cavo.metratura_reale || '0'}\n                          </Typography>\n                        </>\n                      }\n                    />\n                    <ListItemSecondaryAction>\n                      <IconButton edge=\"end\" onClick={(e) => {\n                        e.stopPropagation(); // Prevent triggering the ListItem click\n                        setSelectedCavo(cavo);\n                        setShowCavoDetailsDialog(true);\n                      }}>\n                        <InfoIcon />\n                      </IconButton>\n                    </ListItemSecondaryAction>\n                  </ListItem>\n                  <Divider />\n                </React.Fragment>\n              ))}\n            </List>\n          )}\n        </Paper>\n      </Box>\n    );\n  };\n\n  // Renderizza il passo 2: Inserimento metri\n  const renderStep2 = () => {\n    if (!selectedCavo) return null;\n\n    return (\n      <Box>\n        <Typography variant=\"h6\" gutterBottom>\n          Inserisci metri posati\n        </Typography>\n\n        <CavoDetailsView\n          cavo={selectedCavo}\n          compact={true}\n          title=\"Dettagli del cavo selezionato\"\n        />\n\n        <Paper sx={{ p: 3 }}>\n          <Typography variant=\"subtitle1\" gutterBottom>\n            Inserisci i metri posati\n          </Typography>\n\n          {/* Informazioni sul cavo e sulla bobina */}\n          <Box sx={{ mb: 3, p: 2, bgcolor: '#f5f5f5', borderRadius: 1 }}>\n            <Typography variant=\"subtitle2\" gutterBottom sx={{ fontWeight: 'bold' }}>\n              Informazioni importanti:\n            </Typography>\n            <Typography variant=\"body2\">\n              <strong>Metri teorici del cavo:</strong> {selectedCavo.metri_teorici || 'N/A'} m\n            </Typography>\n            <Typography variant=\"body2\">\n              <strong>Stato attuale del cavo:</strong> {selectedCavo.stato_installazione || 'N/A'}\n            </Typography>\n            {formData.id_bobina && formData.id_bobina !== 'BOBINA_VUOTA' && (() => {\n              const bobina = bobine.find(b => b.id_bobina === formData.id_bobina);\n              return bobina ? (\n                <Typography variant=\"body2\">\n                  <strong>Metri residui bobina:</strong> {bobina.metri_residui || 0} m\n                </Typography>\n              ) : null;\n            })()}\n          </Box>\n\n          <TextField\n            fullWidth\n            label=\"Metri posati\"\n            variant=\"outlined\"\n            name=\"metri_posati\"\n            type=\"number\"\n            value={formData.metri_posati}\n            onChange={handleFormChange}\n            error={!!formErrors.metri_posati}\n            helperText={formErrors.metri_posati || formWarnings.metri_posati}\n            FormHelperTextProps={{\n              sx: { color: formWarnings.metri_posati && !formErrors.metri_posati ? 'warning.main' : 'error.main' }\n            }}\n            sx={{ mb: 2 }}\n          />\n\n          {formWarnings.metri_posati && !formErrors.metri_posati && (\n            <Alert severity=\"warning\" sx={{ mb: 2 }}>\n              {formWarnings.metri_posati}\n            </Alert>\n          )}\n\n          <Alert severity=\"info\" sx={{ mt: 2 }}>\n            Inserisci i metri di cavo effettivamente posati. Questo valore aggiorna lo stato di installazione del cavo.\n          </Alert>\n        </Paper>\n      </Box>\n    );\n  };\n\n  // Renderizza il passo 3: Associazione bobina\n  const renderStep3 = () => {\n    // Funzione per estrarre il numero della bobina dall'ID completo\n    const getBobinaNumber = (idBobina) => {\n      // L'ID completo è nel formato C{id_cantiere}_B{numero_bobina}\n      if (idBobina && idBobina.includes('_B')) {\n        return idBobina.split('_B')[1];\n      }\n      return idBobina;\n    };\n\n    // Funzione per costruire l'ID completo della bobina\n    const buildFullBobinaId = (numeroBobina) => {\n      return `C${cantiereId}_B${numeroBobina}`;\n    };\n\n    // Verifica se una bobina ha metri residui sufficienti\n    const hasSufficientMeters = (bobina) => {\n      if (!bobina || !formData.metri_posati) return true;\n      return parseFloat(bobina.metri_residui) >= parseFloat(formData.metri_posati);\n    };\n\n    // Gestisce l'input diretto del numero della bobina\n    const handleBobinaNumberInput = (e) => {\n      const numeroBobina = e.target.value.trim();\n      \n      // Gestione esplicita dell'input 'v' per bobina vuota\n      if (numeroBobina.toLowerCase() === 'v') {\n        setFormData({\n          ...formData,\n          id_bobina: ''\n        });\n        setFormErrors({\n          ...formErrors,\n          id_bobina_input: null\n        });\n        return;\n      }\n      \n      if (numeroBobina) {\n        // Costruisci l'ID completo\n        const idBobinaCompleto = buildFullBobinaId(numeroBobina);\n        \n        // Verifica se la bobina esiste\n        const bobinaEsistente = bobine.find(b => b.id_bobina === idBobinaCompleto);\n\n        if (bobinaEsistente) {\n          // Verifica se la bobina è in stato OVER o TERMINATA\n          if (bobinaEsistente.stato_bobina === 'Over' || bobinaEsistente.stato_bobina === 'Terminata') {\n            setFormErrors({\n              ...formErrors,\n              id_bobina_input: `La bobina ${numeroBobina} è in stato ${bobinaEsistente.stato_bobina} e non può essere utilizzata`\n            });\n            return;\n          }\n\n          // Verifica compatibilità tra cavo e bobina\n          if (selectedCavo && (\n              bobinaEsistente.tipologia !== selectedCavo.tipologia ||\n              String(bobinaEsistente.n_conduttori) !== String(selectedCavo.n_conduttori) ||\n              String(bobinaEsistente.sezione) !== String(selectedCavo.sezione)\n          )) {\n            // Mostra il dialogo per bobine incompatibili\n            setIncompatibleReel(bobinaEsistente);\n            setShowIncompatibleReelDialog(true);\n            return;\n          }\n\n          // Verifica se la bobina ha metri residui sufficienti\n          if (hasSufficientMeters(bobinaEsistente)) {\n            // Se la bobina esiste e ha metri sufficienti, imposta l'ID completo\n            setFormData({\n              ...formData,\n              id_bobina: idBobinaCompleto\n            });\n            setFormErrors({\n              ...formErrors,\n              id_bobina_input: null\n            });\n          } else {\n            // Se la bobina non ha metri sufficienti, mostra un avviso\n            setFormErrors({\n              ...formErrors,\n              id_bobina_input: `La bobina ${numeroBobina} non ha metri residui sufficienti (${bobinaEsistente.metri_residui} m disponibili)`\n            });\n          }\n        } else {\n          // Se la bobina non esiste, mostra un errore\n          setFormErrors({\n            ...formErrors,\n            id_bobina_input: `Bobina ${numeroBobina} non trovata`\n          });\n        }\n      } else {\n        // Se l'input è vuoto, resetta l'ID bobina\n        setFormData({\n          ...formData,\n          id_bobina: ''\n        });\n        setFormErrors({\n          ...formErrors,\n          id_bobina_input: null\n        });\n      }\n    };\n\n    return (\n      <Box>\n        <Typography variant=\"h6\" gutterBottom>\n          Associa bobina (opzionale)\n        </Typography>\n\n        <Paper sx={{ p: 3 }}>\n          <Typography variant=\"body1\" paragraph>\n            Puoi associare una bobina al cavo selezionato. Questo è opzionale, ma consigliato per tenere traccia dell'utilizzo delle bobine.\n          </Typography>\n\n          {bobineLoading ? (\n            <Box sx={{ display: 'flex', justifyContent: 'center', my: 4 }}>\n              <CircularProgress />\n            </Box>\n          ) : (\n            <Box>\n              {/* Input diretto del numero della bobina */}\n              <Grid container spacing={2} sx={{ mb: 3 }}>\n                <Grid item xs={12} md={6}>\n                  <TextField\n                    fullWidth\n                    label=\"Inserisci numero bobina\"\n                    variant=\"outlined\"\n                    placeholder=\"Inserisci solo il numero (Y)\"\n                    helperText={formErrors.id_bobina_input || \"Inserisci solo il numero della bobina (parte Y del codice Cx_By)\"}\n                    error={!!formErrors.id_bobina_input}\n                    onBlur={handleBobinaNumberInput}\n                  />\n                </Grid>\n                <Grid item xs={12} md={6}>\n                  <Typography variant=\"body2\" sx={{ mt: 1 }}>\n                    ID Bobina completo: {formData.id_bobina || '-'}\n                  </Typography>\n                  {/* Pulsante esplicito per bobina vuota */}\n                  <Button\n                    variant=\"outlined\"\n                    color=\"secondary\"\n                    onClick={() => {\n                      setFormData({\n                        ...formData,\n                        id_bobina: ''\n                      });\n                      setFormErrors({\n                        ...formErrors,\n                        id_bobina_input: null\n                      });\n                    }}\n                    sx={{ mt: 1 }}\n                  >\n                    Nessuna bobina (bobina vuota)\n                  </Button>\n                </Grid>\n              </Grid>\n\n              {/* Selezione dalla lista */}\n              <FormControl fullWidth>\n                <InputLabel id=\"bobina-select-label\">Seleziona Bobina dalla lista</InputLabel>\n                <Select\n                  labelId=\"bobina-select-label\"\n                  id=\"bobina-select\"\n                  name=\"id_bobina\"\n                  value={formData.id_bobina}\n                  label=\"Seleziona Bobina dalla lista\"\n                  onChange={handleFormChange}\n                >\n                  <MenuItem value=\"\">\n                    <em>Nessuna bobina</em>\n                  </MenuItem>\n                  {bobine.map((bobina) => (\n                    <MenuItem\n                      key={bobina.id_bobina}\n                      value={bobina.id_bobina}\n                      disabled={bobina.metri_residui < parseFloat(formData.metri_posati)}\n                    >\n                      {getBobinaNumber(bobina.id_bobina)} - {bobina.tipologia || 'N/A'} - Residui: {bobina.metri_residui || 0} m\n                    </MenuItem>\n                  ))}\n                </Select>\n                <FormHelperText>\n                  Seleziona una bobina con metri residui sufficienti o lascia vuoto per non associare alcuna bobina\n                </FormHelperText>\n              </FormControl>\n\n              {/* Mostra dettagli della bobina selezionata */}\n              {formData.id_bobina && (\n                <Box sx={{ mt: 3, p: 2, bgcolor: 'background.paper', borderRadius: 1, border: '1px solid #e0e0e0' }}>\n                  <Typography variant=\"subtitle2\" gutterBottom>\n                    Dettagli bobina selezionata\n                  </Typography>\n                  {(() => {\n                    const bobina = bobine.find(b => b.id_bobina === formData.id_bobina);\n                    if (bobina) {\n                      return (\n                        <Grid container spacing={2}>\n                          <Grid item xs={12} md={6}>\n                            <Typography variant=\"body2\">\n                              <strong>Numero:</strong> {getBobinaNumber(bobina.id_bobina)}\n                            </Typography>\n                            <Typography variant=\"body2\">\n                              <strong>Tipologia:</strong> {bobina.tipologia || 'N/A'}\n                            </Typography>\n                            <Typography variant=\"body2\">\n                              <strong>Conduttori:</strong> {bobina.n_conduttori || 'N/A'} x {bobina.sezione || 'N/A'}\n                            </Typography>\n                          </Grid>\n                          <Grid item xs={12} md={6}>\n                            <Typography variant=\"body2\">\n                              <strong>Metri totali:</strong> {bobina.metri_totali || 0} m\n                            </Typography>\n                            <Typography variant=\"body2\">\n                              <strong>Metri residui:</strong> {bobina.metri_residui || 0} m\n                            </Typography>\n                            <Typography variant=\"body2\">\n                              <strong>Stato:</strong> {bobina.stato_bobina || 'N/A'}\n                            </Typography>\n                          </Grid>\n                        </Grid>\n                      );\n                    }\n                    return (\n                      <Typography variant=\"body2\" color=\"error\">\n                        Bobina non trovata nel database\n                      </Typography>\n                    );\n                  })()}\n                </Box>\n              )}\n            </Box>\n          )}\n\n          {bobine.length === 0 && !bobineLoading && (\n            <Alert severity=\"warning\" sx={{ mt: 2 }}>\n              Non ci sono bobine disponibili. Puoi procedere senza associare una bobina o aggiungere prima una nuova bobina.\n            </Alert>\n          )}\n        </Paper>\n      </Box>\n    );\n  };\n\n  // Renderizza il passo 4: Conferma\n  const renderStep4 = () => {\n    // Funzione per estrarre il numero della bobina dall'ID completo\n    const getBobinaNumber = (idBobina) => {\n      // L'ID completo è nel formato C{id_cantiere}_B{numero_bobina}\n      if (idBobina && idBobina.includes('_B')) {\n        return idBobina.split('_B')[1];\n      }\n      return idBobina;\n    };\n\n    // Ottieni il numero della bobina se presente\n    let numeroBobina = 'Nessuna';\n    let bobinaInfo = null;\n\n    if (formData.id_bobina === 'BOBINA_VUOTA') {\n      numeroBobina = 'BOBINA VUOTA';\n    } else if (formData.id_bobina) {\n      numeroBobina = getBobinaNumber(formData.id_bobina);\n      // Trova i dettagli della bobina selezionata\n      bobinaInfo = bobine.find(b => b.id_bobina === formData.id_bobina);\n    }\n\n    // Determina lo stato di installazione\n    const statoInstallazione = determineInstallationStatus(parseFloat(formData.metri_posati), selectedCavo.metri_teorici);\n\n    return (\n      <Box>\n        <Typography variant=\"h6\" gutterBottom>\n          Conferma inserimento\n        </Typography>\n\n        <Paper sx={{ p: 3 }}>\n          <Typography variant=\"subtitle1\" gutterBottom>\n            Riepilogo dati\n          </Typography>\n\n          {/* Dettagli del cavo */}\n          <CavoDetailsView\n            cavo={selectedCavo}\n            compact={true}\n            title=\"Dettagli del cavo\"\n          />\n\n          {/* Informazioni sull'operazione */}\n          <Box sx={{ mt: 3, p: 2, bgcolor: '#f5f5f5', borderRadius: 1 }}>\n            <Typography variant=\"subtitle2\" gutterBottom sx={{ fontWeight: 'bold' }}>\n              Informazioni sull'operazione:\n            </Typography>\n            <Grid container spacing={2}>\n              <Grid item xs={12} md={6}>\n                <Typography variant=\"body2\">\n                  <strong>Metri Posati:</strong> {formData.metri_posati} m\n                </Typography>\n                <Typography variant=\"body2\">\n                  <strong>Stato Installazione:</strong> {statoInstallazione}\n                </Typography>\n              </Grid>\n              <Grid item xs={12} md={6}>\n                <Typography variant=\"body2\">\n                  <strong>Bobina Associata:</strong> {numeroBobina}\n                </Typography>\n                {bobinaInfo && (\n                  <Typography variant=\"body2\">\n                    <strong>Metri Residui Bobina:</strong> {bobinaInfo.metri_residui} m\n                  </Typography>\n                )}\n              </Grid>\n            </Grid>\n          </Box>\n\n          {bobinaInfo && parseFloat(formData.metri_posati) > parseFloat(bobinaInfo.metri_residui) && (\n            <Alert severity=\"warning\" sx={{ mt: 3 }}>\n              <strong>Attenzione:</strong> I metri posati ({formData.metri_posati}m) superano i metri residui della bobina ({bobinaInfo.metri_residui}m).\n              Questo porterà la bobina in stato OVER.\n            </Alert>\n          )}\n\n          <Alert severity=\"info\" sx={{ mt: 3 }}>\n            Conferma per aggiornare i metri posati e lo stato di installazione del cavo.\n            {formData.id_bobina && formData.id_bobina !== 'BOBINA_VUOTA' && ' I metri posati verranno sottratti dai metri residui della bobina selezionata.'}\n          </Alert>\n        </Paper>\n      </Box>\n    );\n  };\n\n  // Renderizza il contenuto in base al passo attivo\n  const getStepContent = (step) => {\n    switch (step) {\n      case 0:\n        return renderStep1(); // Seleziona Cavo\n      case 1:\n        return renderStep3(); // Associa Bobina\n      case 2:\n        return renderStep2(); // Inserisci Metri\n      case 3:\n        return renderStep4(); // Conferma\n      default:\n        return 'Passo sconosciuto';\n    }\n  };\n\n  // Gestisce la chiusura del dialogo per cavi già posati\n  const handleCloseAlreadyLaidDialog = () => {\n    setShowAlreadyLaidDialog(false);\n    setAlreadyLaidCavo(null);\n  };\n\n  // Gestisce l'opzione di modificare la bobina di un cavo già posato\n  const handleModifyReel = () => {\n    if (alreadyLaidCavo) {\n      navigate(`/dashboard/cavi/modifica-bobina/${cantiereId}/${alreadyLaidCavo.id_cavo}`);\n    }\n    handleCloseAlreadyLaidDialog();\n  };\n\n  // Gestisce l'opzione di selezionare un altro cavo\n  const handleSelectAnotherCable = () => {\n    handleCloseAlreadyLaidDialog();\n    // Reset del form per selezionare un nuovo cavo\n    setActiveStep(0);\n    setSelectedCavo(null);\n    setCavoIdInput('');\n  };\n\n  // Gestisce la chiusura del dialogo per bobine incompatibili\n  const handleCloseIncompatibleReelDialog = () => {\n    setShowIncompatibleReelDialog(false);\n    setIncompatibleReel(null);\n  };\n\n  // Gestisce l'aggiornamento delle caratteristiche del cavo per farle corrispondere a quelle della bobina\n  const handleUpdateCavoToMatchReel = async () => {\n    if (!selectedCavo || !incompatibleReel) return;\n\n    try {\n      setLoading(true);\n      // Aggiorna le caratteristiche del cavo\n      await caviService.updateCavoToMatchReel(cantiereId, selectedCavo.id_cavo, incompatibleReel);\n\n      // Aggiorna il cavo selezionato con le nuove caratteristiche\n      const updatedCavo = await caviService.getCavoById(cantiereId, selectedCavo.id_cavo);\n      setSelectedCavo(updatedCavo);\n\n      // Imposta la bobina selezionata\n      setFormData({\n        ...formData,\n        id_bobina: incompatibleReel.id_bobina\n      });\n\n      onSuccess(`Caratteristiche del cavo ${selectedCavo.id_cavo} aggiornate per corrispondere alla bobina ${incompatibleReel.id_bobina}`);\n      handleCloseIncompatibleReelDialog();\n    } catch (error) {\n      console.error('Errore durante l\\'aggiornamento delle caratteristiche del cavo:', error);\n      onError('Errore durante l\\'aggiornamento delle caratteristiche del cavo: ' + (error.message || 'Errore sconosciuto'));\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  // Gestisce la selezione di un'altra bobina\n  const handleSelectAnotherReel = () => {\n    handleCloseIncompatibleReelDialog();\n    // Reset della bobina selezionata\n    setFormData({\n      ...formData,\n      id_bobina: ''\n    });\n  };\n\n  return (\n    <Box>\n      <Stepper activeStep={activeStep} sx={{ mb: 4 }}>\n        {steps.map((label) => (\n          <Step key={label}>\n            <StepLabel>{label}</StepLabel>\n          </Step>\n        ))}\n      </Stepper>\n\n      <Box sx={{ mt: 2, mb: 4 }}>\n        {getStepContent(activeStep)}\n      </Box>\n\n      <Box sx={{ display: 'flex', justifyContent: 'space-between', mt: 4 }}>\n        <Button\n          variant=\"outlined\"\n          color=\"secondary\"\n          onClick={activeStep === 0 ? () => navigate('/dashboard/cavi/posa') : handleBack}\n          startIcon={<ArrowBackIcon />}\n          disabled={loading}\n        >\n          {activeStep === 0 ? 'Annulla' : 'Indietro'}\n        </Button>\n\n        <Button\n          variant=\"contained\"\n          color=\"primary\"\n          onClick={activeStep === steps.length - 1 ? handleSubmit : handleNext}\n          endIcon={activeStep === steps.length - 1 ? <SaveIcon /> : <ArrowForwardIcon />}\n          disabled={loading || (activeStep === 0 && !selectedCavo)}\n        >\n          {loading ? (\n            <CircularProgress size={24} />\n          ) : activeStep === steps.length - 1 ? (\n            'Salva'\n          ) : (\n            'Avanti'\n          )}\n        </Button>\n      </Box>\n\n      {/* Dialogo per cavi già posati */}\n      <Dialog open={showAlreadyLaidDialog} onClose={handleCloseAlreadyLaidDialog} maxWidth=\"sm\" fullWidth>\n        <DialogTitle sx={{ bgcolor: 'warning.light' }}>\n          <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>\n            <WarningIcon color=\"warning\" />\n            <Typography variant=\"h6\">Cavo già posato</Typography>\n          </Box>\n        </DialogTitle>\n        <DialogContent>\n          {alreadyLaidCavo && (\n            <Box sx={{ mt: 2 }}>\n              <Typography variant=\"body1\" paragraph>\n                Il cavo <strong>{alreadyLaidCavo.id_cavo}</strong> risulta già posato ({alreadyLaidCavo.metratura_reale || 0}m).\n              </Typography>\n              <Typography variant=\"body1\" paragraph>\n                Puoi scegliere di:\n              </Typography>\n              <Typography variant=\"body2\" component=\"ul\">\n                <li>Modificare la bobina associata al cavo</li>\n                <li>Selezionare un altro cavo</li>\n                <li>Annullare l'operazione</li>\n              </Typography>\n            </Box>\n          )}\n        </DialogContent>\n        <DialogActions sx={{ p: 2, justifyContent: 'space-between' }}>\n          <Button onClick={handleCloseAlreadyLaidDialog} color=\"secondary\">\n            Annulla operazione\n          </Button>\n          <Box>\n            <Button onClick={handleSelectAnotherCable} color=\"primary\" sx={{ mr: 1 }}>\n              Seleziona altro cavo\n            </Button>\n            <Button onClick={handleModifyReel} variant=\"contained\" color=\"primary\">\n              Modifica bobina\n            </Button>\n          </Box>\n        </DialogActions>\n      </Dialog>\n\n      {/* Dialogo per bobine incompatibili */}\n      <IncompatibleReelDialog\n        open={showIncompatibleReelDialog}\n        onClose={handleCloseIncompatibleReelDialog}\n        cavo={selectedCavo}\n        bobina={incompatibleReel}\n        onUpdateCavo={handleUpdateCavoToMatchReel}\n        onSelectAnotherReel={handleSelectAnotherReel}\n      />\n\n      {/* Dialogo per visualizzare i dettagli del cavo */}\n      <Dialog\n        open={showCavoDetailsDialog}\n        onClose={() => setShowCavoDetailsDialog(false)}\n        maxWidth=\"md\"\n        fullWidth\n      >\n        <DialogTitle>\n          <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>\n            <InfoIcon color=\"primary\" />\n            <Typography variant=\"h6\">Dettagli Cavo</Typography>\n          </Box>\n        </DialogTitle>\n        <DialogContent>\n          <CavoDetailsView cavo={selectedCavo} />\n        </DialogContent>\n        <DialogActions>\n          <Button onClick={() => setShowCavoDetailsDialog(false)} color=\"primary\">\n            Chiudi\n          </Button>\n        </DialogActions>\n      </Dialog>\n    </Box>\n  );\n};\n\nexport default InserisciMetriForm;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAClD,OAAOC,KAAK,MAAM,OAAO;AACzB,SACEC,GAAG,EACHC,KAAK,EACLC,UAAU,EACVC,SAAS,EACTC,MAAM,EACNC,OAAO,EACPC,IAAI,EACJC,SAAS,EACTC,IAAI,EACJC,WAAW,EACXC,UAAU,EACVC,MAAM,EACNC,QAAQ,EACRC,IAAI,EACJC,QAAQ,EACRC,YAAY,EACZC,uBAAuB,EACvBC,OAAO,EACPC,KAAK,EACLC,gBAAgB,EAChBC,cAAc,EACdC,UAAU,EACVC,IAAI,EACJC,MAAM,EACNC,WAAW,EACXC,aAAa,EACbC,aAAa,QACR,eAAe;AACtB,SACEC,MAAM,IAAIC,UAAU,EACpBC,IAAI,IAAIC,QAAQ,EAChBC,SAAS,IAAIC,aAAa,EAC1BC,YAAY,IAAIC,gBAAgB,EAChCC,MAAM,IAAIC,UAAU,EACpBC,WAAW,IAAIC,eAAe,EAC9BC,OAAO,IAAIC,WAAW,EACtBC,IAAI,IAAIC,QAAQ,QACX,qBAAqB;AAC5B,SAASC,WAAW,QAAQ,kBAAkB;AAC9C,OAAOC,WAAW,MAAM,4BAA4B;AACpD,OAAOC,aAAa,MAAM,4BAA4B;AACtD,OAAOC,sBAAsB,MAAM,0BAA0B;AAC7D,OAAOC,eAAe,MAAM,mBAAmB;AAC/C,SACEC,YAAY,EACZC,WAAW,EACXC,mBAAmB,EACnBC,kBAAkB,EAClBC,cAAc,EACdC,YAAY,EACZC,gBAAgB,EAChBC,kBAAkB,EAClBC,iBAAiB,QACZ,wBAAwB;AAC/B,OAAOC,gBAAgB,MAAM,iCAAiC;AAC9D,SAASC,wBAAwB,QAAQ,6BAA6B;;AAEtE;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAPA,SAAAC,MAAA,IAAAC,OAAA,EAAAC,QAAA,IAAAC,SAAA;AAQA,MAAMC,kBAAkB,GAAGA,CAAC;EAAEC,UAAU;EAAEC,SAAS;EAAEC;AAAQ,CAAC,KAAK;EAAAC,EAAA;EACjE,MAAMC,QAAQ,GAAGzB,WAAW,CAAC,CAAC;;EAE9B;EACA,MAAM,CAAC0B,UAAU,EAAEC,aAAa,CAAC,GAAGzE,QAAQ,CAAC,CAAC,CAAC;EAC/C,MAAM,CAAC0E,OAAO,EAAEC,UAAU,CAAC,GAAG3E,QAAQ,CAAC,KAAK,CAAC;EAC7C,MAAM,CAAC4E,WAAW,EAAEC,cAAc,CAAC,GAAG7E,QAAQ,CAAC,KAAK,CAAC;EACrD,MAAM,CAAC8E,aAAa,EAAEC,gBAAgB,CAAC,GAAG/E,QAAQ,CAAC,KAAK,CAAC;;EAEzD;EACA,MAAM,CAACgF,IAAI,EAAEC,OAAO,CAAC,GAAGjF,QAAQ,CAAC,EAAE,CAAC;EACpC,MAAM,CAACkF,MAAM,EAAEC,SAAS,CAAC,GAAGnF,QAAQ,CAAC,EAAE,CAAC;EACxC,MAAM,CAACoF,YAAY,EAAEC,eAAe,CAAC,GAAGrF,QAAQ,CAAC,IAAI,CAAC;EACtD,MAAM,CAACsF,WAAW,EAAEC,cAAc,CAAC,GAAGvF,QAAQ,CAAC,EAAE,CAAC;;EAElD;EACA,MAAM,CAACwF,QAAQ,EAAEC,WAAW,CAAC,GAAGzF,QAAQ,CAAC;IACvC0F,OAAO,EAAE,EAAE;IACXC,YAAY,EAAE,EAAE;IAChBC,SAAS,EAAE;EACb,CAAC,CAAC;;EAEF;EACA,MAAM,CAACC,UAAU,EAAEC,aAAa,CAAC,GAAG9F,QAAQ,CAAC,CAAC,CAAC,CAAC;EAChD,MAAM,CAAC+F,YAAY,EAAEC,eAAe,CAAC,GAAGhG,QAAQ,CAAC,CAAC,CAAC,CAAC;;EAEpD;EACA,MAAM,CAACiG,0BAA0B,EAAEC,6BAA6B,CAAC,GAAGlG,QAAQ,CAAC,KAAK,CAAC;EACnF,MAAM,CAACmG,gBAAgB,EAAEC,mBAAmB,CAAC,GAAGpG,QAAQ,CAAC,IAAI,CAAC;EAC9D,MAAM,CAACqG,qBAAqB,EAAEC,wBAAwB,CAAC,GAAGtG,QAAQ,CAAC,KAAK,CAAC;EACzE,MAAM,CAACuG,eAAe,EAAEC,kBAAkB,CAAC,GAAGxG,QAAQ,CAAC,IAAI,CAAC;EAC5D,MAAM,CAACyG,qBAAqB,EAAEC,wBAAwB,CAAC,GAAG1G,QAAQ,CAAC,KAAK,CAAC;;EAEzE;EACA,MAAM2G,KAAK,GAAG,CAAC,gBAAgB,EAAE,gBAAgB,EAAE,iBAAiB,EAAE,UAAU,CAAC;;EAEjF;EACA1G,SAAS,CAAC,MAAM;IACd2G,QAAQ,CAAC,CAAC;EACZ,CAAC,EAAE,CAACzC,UAAU,CAAC,CAAC;;EAEhB;EACAlE,SAAS,CAAC,MAAM;IACd,IAAIuE,UAAU,KAAK,CAAC,EAAE;MACpB;MACAqC,UAAU,CAAC,CAAC;IACd;EACF,CAAC,EAAE,CAACrC,UAAU,EAAEL,UAAU,CAAC,CAAC;;EAE5B;EACA,MAAMyC,QAAQ,GAAG,MAAAA,CAAA,KAAY;IAC3B,IAAI;MACF/B,cAAc,CAAC,IAAI,CAAC;MACpBiC,OAAO,CAACC,GAAG,CAAC,oCAAoC5C,UAAU,KAAK,CAAC;;MAEhE;MACA,IAAI;QACF,MAAM6C,QAAQ,GAAG,MAAMjE,WAAW,CAACkE,OAAO,CAAC9C,UAAU,CAAC;QACtD2C,OAAO,CAACC,GAAG,CAAC,YAAYC,QAAQ,CAACE,MAAM,OAAO,CAAC;;QAE/C;QACA;QACAjC,OAAO,CAAC+B,QAAQ,CAAC;MACnB,CAAC,CAAC,OAAOG,SAAS,EAAE;QAClBL,OAAO,CAACM,KAAK,CAAC,qDAAqD,EAAED,SAAS,CAAC;;QAE/E;QACA,IAAIA,SAAS,CAACE,cAAc,IAAI,CAACF,SAAS,CAACG,QAAQ,IAC/CH,SAAS,CAACI,IAAI,KAAK,cAAc,IAChCJ,SAAS,CAACK,OAAO,IAAIL,SAAS,CAACK,OAAO,CAACC,QAAQ,CAAC,eAAe,CAAE,EAAE;UAEtEX,OAAO,CAACC,GAAG,CAAC,yDAAyD,CAAC;UACtE,MAAM,IAAIW,OAAO,CAACC,OAAO,IAAIC,UAAU,CAACD,OAAO,EAAE,IAAI,CAAC,CAAC;UAEvD,IAAI;YACF;YACA,MAAME,KAAK,GAAGC,YAAY,CAACC,OAAO,CAAC,OAAO,CAAC;YAC3C,MAAMC,OAAO,GAAGhF,aAAa,CAACiF,QAAQ,CAACC,OAAO;;YAE9C;YACA,MAAMC,aAAa,GAAG,MAAMjI,KAAK,CAACkI,GAAG,CACnC,GAAGJ,OAAO,SAAS7D,UAAU,EAAE,EAC/B;cACEkE,OAAO,EAAE;gBACP,eAAe,EAAE,UAAUR,KAAK;cAClC,CAAC;cACDS,OAAO,EAAE,KAAK,CAAC;YACjB,CACF,CAAC;YAEDxB,OAAO,CAACC,GAAG,CAAC,2CAA2CoB,aAAa,CAACI,IAAI,CAACrB,MAAM,OAAO,CAAC;YACxFjC,OAAO,CAACkD,aAAa,CAACI,IAAI,CAAC;UAC7B,CAAC,CAAC,OAAOC,UAAU,EAAE;YACnB1B,OAAO,CAACM,KAAK,CAAC,uCAAuC,EAAEoB,UAAU,CAAC;YAClE,MAAMA,UAAU;UAClB;QACF,CAAC,MAAM;UACL,MAAMrB,SAAS;QACjB;MACF;IACF,CAAC,CAAC,OAAOC,KAAK,EAAE;MACdN,OAAO,CAACM,KAAK,CAAC,kCAAkC,EAAEA,KAAK,CAAC;;MAExD;MACA,IAAIqB,YAAY,GAAG,iCAAiC;MAEpD,IAAIrB,KAAK,CAACC,cAAc,EAAE;QACxBoB,YAAY,GAAG,8DAA8D;MAC/E,CAAC,MAAM,IAAIrB,KAAK,CAACsB,MAAM,EAAE;QACvBD,YAAY,GAAGrB,KAAK,CAACsB,MAAM;MAC7B,CAAC,MAAM,IAAItB,KAAK,CAACI,OAAO,EAAE;QACxBiB,YAAY,GAAGrB,KAAK,CAACI,OAAO;MAC9B;MAEAnD,OAAO,CAACoE,YAAY,CAAC;IACvB,CAAC,SAAS;MACR5D,cAAc,CAAC,KAAK,CAAC;IACvB;EACF,CAAC;;EAED;EACA,MAAMgC,UAAU,GAAG,MAAAA,CAAA,KAAY;IAC7B,IAAI;MACF9B,gBAAgB,CAAC,IAAI,CAAC;;MAEtB;MACA,MAAM4D,UAAU,GAAG,MAAM/E,gBAAgB,CAACgF,SAAS,CAACzE,UAAU,CAAC;;MAE/D;MACA,IAAI0E,kBAAkB,GAAGF,UAAU,CAACG,MAAM,CAACC,MAAM,IAC/C,CAACA,MAAM,CAACC,YAAY,KAAK,aAAa,IAAID,MAAM,CAACC,YAAY,KAAK,QAAQ,KAC1ED,MAAM,CAACC,YAAY,KAAK,MAAM,IAAID,MAAM,CAACC,YAAY,KAAK,WAC5D,CAAC;;MAED;MACA,IAAI5D,YAAY,EAAE;QAChB;QACA,IAAIA,YAAY,CAAC6D,SAAS,IAAI7D,YAAY,CAAC8D,YAAY,IAAI9D,YAAY,CAAC+D,OAAO,EAAE;UAC/E,MAAMC,iBAAiB,GAAGP,kBAAkB,CAACC,MAAM,CAACC,MAAM,IACxDA,MAAM,CAACE,SAAS,KAAK7D,YAAY,CAAC6D,SAAS,IAC3CF,MAAM,CAACG,YAAY,KAAK9D,YAAY,CAAC8D,YAAY,IACjDH,MAAM,CAACI,OAAO,KAAK/D,YAAY,CAAC+D,OAClC,CAAC;;UAED;UACA,IAAIC,iBAAiB,CAAClC,MAAM,GAAG,CAAC,EAAE;YAChC2B,kBAAkB,GAAGO,iBAAiB;UACxC,CAAC,MAAM;YACLtC,OAAO,CAACC,GAAG,CAAC,wEAAwE,CAAC;UACvF;QACF;;QAEA;QACA8B,kBAAkB,CAACQ,IAAI,CAAC,CAACC,CAAC,EAAEC,CAAC,KAAKA,CAAC,CAACC,aAAa,GAAGF,CAAC,CAACE,aAAa,CAAC;MACtE;MAEArE,SAAS,CAAC0D,kBAAkB,CAAC;IAC/B,CAAC,CAAC,OAAOzB,KAAK,EAAE;MACdN,OAAO,CAACM,KAAK,CAAC,sCAAsC,EAAEA,KAAK,CAAC;MAC5D/C,OAAO,CAAC,uCAAuC,IAAI+C,KAAK,CAACI,OAAO,IAAI,oBAAoB,CAAC,CAAC;IAC5F,CAAC,SAAS;MACRzC,gBAAgB,CAAC,KAAK,CAAC;IACzB;EACF,CAAC;;EAED;EACA,MAAM0E,oBAAoB,GAAG,MAAAA,CAAA,KAAY;IACvC,IAAI,CAACnE,WAAW,CAACoE,IAAI,CAAC,CAAC,EAAE;MACvBrF,OAAO,CAAC,6BAA6B,CAAC;MACtC;IACF;IAEA,IAAI;MACFQ,cAAc,CAAC,IAAI,CAAC;MACpBiC,OAAO,CAACC,GAAG,CAAC,wBAAwBzB,WAAW,CAACoE,IAAI,CAAC,CAAC,iBAAiBvF,UAAU,EAAE,CAAC;;MAEpF;MACA,IAAIwF,QAAQ;MACZ,IAAI;QACFA,QAAQ,GAAG,MAAM5G,WAAW,CAAC6G,WAAW,CAACzF,UAAU,EAAEmB,WAAW,CAACoE,IAAI,CAAC,CAAC,CAAC;QACxE5C,OAAO,CAACC,GAAG,CAAC,4BAA4B,EAAE4C,QAAQ,CAAC;MACrD,CAAC,CAAC,OAAOE,WAAW,EAAE;QACpB/C,OAAO,CAACM,KAAK,CAAC,wCAAwC,EAAEyC,WAAW,CAAC;;QAEpE;QACA,IAAIA,WAAW,CAACxC,cAAc,EAAE;UAC9BP,OAAO,CAACC,GAAG,CAAC,qDAAqD,CAAC;UAClE,MAAM+C,UAAU,GAAG9E,IAAI,CAAC+E,IAAI,CAACC,CAAC,IAAIA,CAAC,CAACtE,OAAO,KAAKJ,WAAW,CAACoE,IAAI,CAAC,CAAC,CAAC;UAEnE,IAAII,UAAU,EAAE;YACdhD,OAAO,CAACC,GAAG,CAAC,kCAAkC,EAAE+C,UAAU,CAAC;YAC3DH,QAAQ,GAAGG,UAAU;UACvB,CAAC,MAAM;YACLhD,OAAO,CAACM,KAAK,CAAC,6CAA6C,CAAC;YAC5D,MAAMyC,WAAW,CAAC,CAAC;UACrB;QACF,CAAC,MAAM;UACL,MAAMA,WAAW,CAAC,CAAC;QACrB;MACF;;MAEA;MACA,IAAIF,QAAQ,CAACM,mBAAmB,KAAK,YAAY,IAAKN,QAAQ,CAACO,eAAe,IAAIP,QAAQ,CAACO,eAAe,GAAG,CAAE,EAAE;QAC/GpD,OAAO,CAACC,GAAG,CAAC,sCAAsC,EAAE4C,QAAQ,CAAC;QAC7D;QACAnD,kBAAkB,CAACmD,QAAQ,CAAC;QAC5BrD,wBAAwB,CAAC,IAAI,CAAC;QAC9BzB,cAAc,CAAC,KAAK,CAAC;QACrB;MACF;;MAEA;MACA,IAAI8E,QAAQ,CAACQ,sBAAsB,KAAK,CAAC,EAAE;QACzCrD,OAAO,CAACC,GAAG,CAAC,qBAAqB,EAAE4C,QAAQ,CAAC;QAC5C;MACF;;MAEA;MACA7C,OAAO,CAACC,GAAG,CAAC,iDAAiD,CAAC;MAC9DqD,gBAAgB,CAACT,QAAQ,CAAC;IAC5B,CAAC,CAAC,OAAOvC,KAAK,EAAE;MACdN,OAAO,CAACM,KAAK,CAAC,gCAAgC,EAAEA,KAAK,CAAC;;MAEtD;MACA,IAAIqB,YAAY,GAAG,yCAAyC;MAE5D,IAAIrB,KAAK,CAACC,cAAc,EAAE;QACxBoB,YAAY,GAAG,8DAA8D;MAC/E,CAAC,MAAM,IAAIrB,KAAK,CAACiD,MAAM,KAAK,GAAG,EAAE;QAC/B5B,YAAY,GAAG,gBAAgBnD,WAAW,CAACoE,IAAI,CAAC,CAAC,8BAA8BvF,UAAU,EAAE;MAC7F,CAAC,MAAM,IAAIiD,KAAK,CAACsB,MAAM,EAAE;QACvBD,YAAY,GAAGrB,KAAK,CAACsB,MAAM;MAC7B,CAAC,MAAM,IAAItB,KAAK,CAACI,OAAO,EAAE;QACxBiB,YAAY,GAAGrB,KAAK,CAACI,OAAO;MAC9B;MAEAnD,OAAO,CAACoE,YAAY,CAAC;IACvB,CAAC,SAAS;MACR5D,cAAc,CAAC,KAAK,CAAC;IACvB;EACF,CAAC;;EAED;EACA,MAAMuF,gBAAgB,GAAIE,IAAI,IAAK;IACjC;IACA,IAAIA,IAAI,CAACL,mBAAmB,KAAK,YAAY,IAAKK,IAAI,CAACJ,eAAe,IAAII,IAAI,CAACJ,eAAe,GAAG,CAAE,EAAE;MACnG;MACA1D,kBAAkB,CAAC8D,IAAI,CAAC;MACxBhE,wBAAwB,CAAC,IAAI,CAAC;MAC9B;IACF;IACA;IAAA,KACK,IAAIgE,IAAI,CAACH,sBAAsB,KAAK,CAAC,EAAE;MAC1C;MACA,IAAII,MAAM,CAACC,OAAO,CAAC,WAAWF,IAAI,CAAC5E,OAAO,0CAA0C,CAAC,EAAE;QACrF;QACA+E,eAAe,CAACH,IAAI,CAAC5E,OAAO,CAAC,CAACgF,IAAI,CAAC,MAAM;UACvC;UACA,MAAMC,WAAW,GAAG;YAAE,GAAGL,IAAI;YAAEH,sBAAsB,EAAE;UAAE,CAAC;UAC1D9E,eAAe,CAACsF,WAAW,CAAC;UAC5BlF,WAAW,CAAC;YACV,GAAGD,QAAQ;YACXE,OAAO,EAAEiF,WAAW,CAACjF,OAAO;YAC5BC,YAAY,EAAE;UAChB,CAAC,CAAC;UACF;UACAlB,aAAa,CAAC,CAAC,CAAC;QAClB,CAAC,CAAC,CAACmG,KAAK,CAACxD,KAAK,IAAI;UAChBN,OAAO,CAACM,KAAK,CAAC,iDAAiD,EAAEA,KAAK,CAAC;UACvE/C,OAAO,CAAC,kDAAkD,IAAI+C,KAAK,CAACI,OAAO,IAAI,oBAAoB,CAAC,CAAC;QACvG,CAAC,CAAC;MACJ,CAAC,MAAM;QACL;QACA;MACF;IACF,CAAC,MAAM;MACL;MACAnC,eAAe,CAACiF,IAAI,CAAC;MACrB7E,WAAW,CAAC;QACV,GAAGD,QAAQ;QACXE,OAAO,EAAE4E,IAAI,CAAC5E,OAAO;QACrBC,YAAY,EAAE;MAChB,CAAC,CAAC;MACF;MACAlB,aAAa,CAAC,CAAC,CAAC;IAClB;EACF,CAAC;;EAED;EACA,MAAMgG,eAAe,GAAG,MAAOI,MAAM,IAAK;IACxC,IAAI;MACF;MACA,MAAM9H,WAAW,CAAC0H,eAAe,CAACtG,UAAU,EAAE0G,MAAM,CAAC;MACrDzG,SAAS,CAAC,QAAQyG,MAAM,0BAA0B,CAAC;MACnD,OAAO,IAAI;IACb,CAAC,CAAC,OAAOzD,KAAK,EAAE;MACdN,OAAO,CAACM,KAAK,CAAC,iDAAiD,EAAEA,KAAK,CAAC;MACvE/C,OAAO,CAAC,kDAAkD,IAAI+C,KAAK,CAACI,OAAO,IAAI,oBAAoB,CAAC,CAAC;MACrG,MAAMJ,KAAK;IACb;EACF,CAAC;;EAED;EACA,MAAM0D,gBAAgB,GAAIC,CAAC,IAAK;IAC9B,MAAM;MAAEC,IAAI;MAAEC;IAAM,CAAC,GAAGF,CAAC,CAACG,MAAM;IAChCzF,WAAW,CAAC;MACV,GAAGD,QAAQ;MACX,CAACwF,IAAI,GAAGC;IACV,CAAC,CAAC;;IAEF;IACAE,aAAa,CAACH,IAAI,EAAEC,KAAK,CAAC;EAC5B,CAAC;;EAED;EACA,MAAME,aAAa,GAAGA,CAACH,IAAI,EAAEC,KAAK,KAAK;IACrC,IAAI7D,KAAK,GAAG,IAAI;IAChB,IAAIgE,OAAO,GAAG,IAAI;IAElB,IAAIJ,IAAI,KAAK,cAAc,EAAE;MAC3B;MACA,IAAI,CAACC,KAAK,IAAIA,KAAK,CAACvB,IAAI,CAAC,CAAC,KAAK,EAAE,EAAE;QACjCtC,KAAK,GAAG,uCAAuC;QAC/C,OAAO,KAAK;MACd;;MAEA;MACA,IAAIiE,KAAK,CAACC,UAAU,CAACL,KAAK,CAAC,CAAC,IAAIK,UAAU,CAACL,KAAK,CAAC,IAAI,CAAC,EAAE;QACtD7D,KAAK,GAAG,sCAAsC;QAC9C,OAAO,KAAK;MACd;MAEA,MAAMmE,WAAW,GAAGD,UAAU,CAACL,KAAK,CAAC;;MAErC;MACA,IAAI7F,YAAY,IAAIA,YAAY,CAACoG,aAAa,IAAID,WAAW,GAAGD,UAAU,CAAClG,YAAY,CAACoG,aAAa,CAAC,EAAE;QACtGJ,OAAO,GAAG,mBAAmBG,WAAW,yCAAyCnG,YAAY,CAACoG,aAAa,IAAI;MACjH;;MAEA;MACA,IAAIhG,QAAQ,CAACI,SAAS,IAAIJ,QAAQ,CAACI,SAAS,KAAK,cAAc,EAAE;QAC/D,MAAMmD,MAAM,GAAG7D,MAAM,CAAC6E,IAAI,CAACR,CAAC,IAAIA,CAAC,CAAC3D,SAAS,KAAKJ,QAAQ,CAACI,SAAS,CAAC;QACnE,IAAImD,MAAM,IAAIwC,WAAW,GAAGD,UAAU,CAACvC,MAAM,CAACS,aAAa,CAAC,EAAE;UAC5D4B,OAAO,GAAG,mBAAmBG,WAAW,6CAA6CxC,MAAM,CAACS,aAAa,oCAAoC;QAC/I;MACF;IACF;;IAEA;IACA1D,aAAa,CAAC2F,IAAI,KAAK;MACrB,GAAGA,IAAI;MACP,CAACT,IAAI,GAAG5D;IACV,CAAC,CAAC,CAAC;;IAEH;IACApB,eAAe,CAACyF,IAAI,KAAK;MACvB,GAAGA,IAAI;MACP,CAACT,IAAI,GAAGI;IACV,CAAC,CAAC,CAAC;IAEH,OAAO,CAAChE,KAAK;EACf,CAAC;;EAED;EACA,MAAMsE,YAAY,GAAGA,CAAA,KAAM;IACzB,IAAIC,OAAO,GAAG,IAAI;IAClB,MAAMC,MAAM,GAAG,CAAC,CAAC;IACjB,MAAMC,QAAQ,GAAG,CAAC,CAAC;;IAEnB;IACA,IAAI,CAACrG,QAAQ,CAACG,YAAY,IAAIH,QAAQ,CAACG,YAAY,CAAC+D,IAAI,CAAC,CAAC,KAAK,EAAE,EAAE;MACjEkC,MAAM,CAACjG,YAAY,GAAG,uCAAuC;MAC7DgG,OAAO,GAAG,KAAK;IACjB,CAAC,MAAM,IAAIN,KAAK,CAACC,UAAU,CAAC9F,QAAQ,CAACG,YAAY,CAAC,CAAC,IAAI2F,UAAU,CAAC9F,QAAQ,CAACG,YAAY,CAAC,IAAI,CAAC,EAAE;MAC7FiG,MAAM,CAACjG,YAAY,GAAG,sCAAsC;MAC5DgG,OAAO,GAAG,KAAK;IACjB,CAAC,MAAM;MACL,MAAMJ,WAAW,GAAGD,UAAU,CAAC9F,QAAQ,CAACG,YAAY,CAAC;;MAErD;MACA,IAAIP,YAAY,IAAIA,YAAY,CAACoG,aAAa,IAAID,WAAW,GAAGD,UAAU,CAAClG,YAAY,CAACoG,aAAa,CAAC,EAAE;QACtGK,QAAQ,CAAClG,YAAY,GAAG,mBAAmB4F,WAAW,yCAAyCnG,YAAY,CAACoG,aAAa,IAAI;;QAE7H;QACA,IAAI,CAACjB,MAAM,CAACC,OAAO,CAAC,+BAA+Be,WAAW,yCAAyCnG,YAAY,CAACoG,aAAa,kCAAkC,CAAC,EAAE;UACpKG,OAAO,GAAG,KAAK;QACjB;MACF;;MAEA;MACA,IAAIA,OAAO,IAAInG,QAAQ,CAACI,SAAS,IAAIJ,QAAQ,CAACI,SAAS,KAAK,cAAc,EAAE;QAC1E,MAAMmD,MAAM,GAAG7D,MAAM,CAAC6E,IAAI,CAACR,CAAC,IAAIA,CAAC,CAAC3D,SAAS,KAAKJ,QAAQ,CAACI,SAAS,CAAC;QACnE,IAAImD,MAAM,IAAIwC,WAAW,GAAGD,UAAU,CAACvC,MAAM,CAACS,aAAa,CAAC,EAAE;UAC5DqC,QAAQ,CAAClG,YAAY,GAAG,mBAAmB4F,WAAW,6CAA6CxC,MAAM,CAACS,aAAa,oCAAoC;;UAE3J;UACA,IAAI,CAACe,MAAM,CAACC,OAAO,CAAC,+BAA+Be,WAAW,6CAA6CxC,MAAM,CAACS,aAAa,oEAAoE,CAAC,EAAE;YACpMmC,OAAO,GAAG,KAAK;UACjB;QACF;MACF;IACF;IAEA7F,aAAa,CAAC8F,MAAM,CAAC;IACrB5F,eAAe,CAAC6F,QAAQ,CAAC;IACzB,OAAOF,OAAO;EAChB,CAAC;;EAED;EACA,MAAMG,UAAU,GAAGA,CAAA,KAAM;IACvB,IAAItH,UAAU,KAAK,CAAC,EAAE;MACpB;MACA,IAAI,CAACkH,YAAY,CAAC,CAAC,EAAE;QACnB;MACF;IACF,CAAC,MAAM,IAAIlH,UAAU,KAAK,CAAC,EAAE;MAC3B;MACAqC,UAAU,CAAC,CAAC;IACd;IAEApC,aAAa,CAAEsH,cAAc,IAAKA,cAAc,GAAG,CAAC,CAAC;EACvD,CAAC;;EAED;EACA,MAAMC,UAAU,GAAGA,CAAA,KAAM;IACvBvH,aAAa,CAAEsH,cAAc,IAAKA,cAAc,GAAG,CAAC,CAAC;EACvD,CAAC;;EAED;EACA,MAAME,WAAW,GAAGA,CAAA,KAAM;IACxBxH,aAAa,CAAC,CAAC,CAAC;IAChBY,eAAe,CAAC,IAAI,CAAC;IACrBE,cAAc,CAAC,EAAE,CAAC;IAClBE,WAAW,CAAC;MACVC,OAAO,EAAE,EAAE;MACXC,YAAY,EAAE,EAAE;MAChBC,SAAS,EAAE;IACb,CAAC,CAAC;IACFE,aAAa,CAAC,CAAC,CAAC,CAAC;IACjBE,eAAe,CAAC,CAAC,CAAC,CAAC;EACrB,CAAC;;EAED;EACA;EACA,MAAMkG,2BAA2B,GAAGA,CAACX,WAAW,EAAEY,YAAY,KAAK;IACjE,OAAO9I,mBAAmB,CAACkI,WAAW,EAAEY,YAAY,CAAC;EACvD,CAAC;;EAED;EACA,MAAMC,YAAY,GAAG,MAAAA,CAAA,KAAY;IAC/B,IAAI;MACFzH,UAAU,CAAC,IAAI,CAAC;;MAEhB;MACA,IAAI,CAAC+G,YAAY,CAAC,CAAC,EAAE;QACnB/G,UAAU,CAAC,KAAK,CAAC;QACjB;MACF;;MAEA;MACA,MAAM4G,WAAW,GAAGD,UAAU,CAAC9F,QAAQ,CAACG,YAAY,CAAC;;MAErD;MACA,IAAI0G,QAAQ,GAAG7G,QAAQ,CAACI,SAAS;MACjC;MACA,IAAI,CAACyG,QAAQ,IAAIA,QAAQ,KAAK,EAAE,EAAE;QAChCA,QAAQ,GAAG,IAAI;QACfvF,OAAO,CAACC,GAAG,CAAC,+CAA+C,CAAC;MAC9D,CAAC,MAAM,IAAIsF,QAAQ,KAAK,cAAc,EAAE;QACtCvF,OAAO,CAACC,GAAG,CAAC,iCAAiC,CAAC;QAC9C;QACAsF,QAAQ,GAAG,cAAc;MAC3B,CAAC,MAAM;QACLvF,OAAO,CAACC,GAAG,CAAC,wBAAwBsF,QAAQ,EAAE,CAAC;MACjD;;MAEA;MACA,MAAMC,kBAAkB,GAAGJ,2BAA2B,CAACX,WAAW,EAAEnG,YAAY,CAACoG,aAAa,CAAC;;MAE/F;MACA,IAAIe,SAAS,GAAG,KAAK;MACrB,IAAIF,QAAQ,IAAIA,QAAQ,KAAK,cAAc,EAAE;QAC3C,MAAMtD,MAAM,GAAG7D,MAAM,CAAC6E,IAAI,CAACR,CAAC,IAAIA,CAAC,CAAC3D,SAAS,KAAKyG,QAAQ,CAAC;QACzD,IAAItD,MAAM,IAAIwC,WAAW,GAAGD,UAAU,CAACvC,MAAM,CAACS,aAAa,CAAC,EAAE;UAC5D+C,SAAS,GAAG,IAAI;UAChBzF,OAAO,CAACC,GAAG,CAAC,qCAAqCsF,QAAQ,iCAAiC,CAAC;QAC7F;MACF;;MAEA;MACAvF,OAAO,CAACC,GAAG,CAAC,aAAa,EAAE;QACzB5C,UAAU;QACV0G,MAAM,EAAErF,QAAQ,CAACE,OAAO;QACxB6F,WAAW;QACXc,QAAQ;QACRE,SAAS;QACTD;MACF,CAAC,CAAC;;MAEF;MACA,MAAME,cAAc,GAAG,qCAAqChH,QAAQ,CAACE,OAAO,QAAQ6F,WAAW,WAAW;MAC1G,IAAI,CAAChB,MAAM,CAACC,OAAO,CAACgC,cAAc,CAAC,EAAE;QACnC7H,UAAU,CAAC,KAAK,CAAC;QACjB;MACF;;MAEA;MACAmC,OAAO,CAACC,GAAG,CAAC,6DAA6D,CAAC;MAC1ED,OAAO,CAACC,GAAG,CAAC,eAAe,EAAE5C,UAAU,CAAC;MACxC2C,OAAO,CAACC,GAAG,CAAC,YAAY,EAAEvB,QAAQ,CAACE,OAAO,CAAC;MAC3CoB,OAAO,CAACC,GAAG,CAAC,iBAAiB,EAAEwE,WAAW,CAAC;MAC3CzE,OAAO,CAACC,GAAG,CAAC,cAAc,EAAEsF,QAAQ,EAAE,OAAOA,QAAQ,CAAC;MACtDvF,OAAO,CAACC,GAAG,CAAC,cAAc,EAAEwF,SAAS,CAAC;MAEtC,MAAMxJ,WAAW,CAAC0J,iBAAiB,CACjCtI,UAAU,EACVqB,QAAQ,CAACE,OAAO,EAChB6F,WAAW,EACXc,QAAQ,EACRE,SACF,CAAC;;MAED;MACA,IAAIG,cAAc,GAAG,qDAAqDJ,kBAAkB,EAAE;MAC9F,IAAID,QAAQ,KAAK,cAAc,EAAE;QAC/BK,cAAc,IAAI,iCAAiC;MACrD,CAAC,MAAM,IAAIL,QAAQ,EAAE;QACnB,MAAMtD,MAAM,GAAG7D,MAAM,CAAC6E,IAAI,CAACR,CAAC,IAAIA,CAAC,CAAC3D,SAAS,KAAKyG,QAAQ,CAAC;QACzD,IAAItD,MAAM,EAAE;UACV2D,cAAc,IAAI,gCAAgCL,QAAQ,EAAE;QAC9D;MACF;;MAEA;MACAjI,SAAS,CAACsI,cAAc,CAAC;;MAEzB;MACAT,WAAW,CAAC,CAAC;;MAEb;MACArF,QAAQ,CAAC,CAAC;IACZ,CAAC,CAAC,OAAOQ,KAAK,EAAE;MACdN,OAAO,CAACM,KAAK,CAAC,mDAAmD,EAAEA,KAAK,CAAC;MACzE/C,OAAO,CAAC,oDAAoD,IAAI+C,KAAK,CAACI,OAAO,IAAI,oBAAoB,CAAC,CAAC;IACzG,CAAC,SAAS;MACR7C,UAAU,CAAC,KAAK,CAAC;IACnB;EACF,CAAC;;EAED;EACA,MAAMgI,WAAW,GAAGA,CAAA,KAAM;IACxB,oBACE5I,OAAA,CAAC5D,GAAG;MAAAyM,QAAA,gBACF7I,OAAA,CAAC1D,UAAU;QAACwM,OAAO,EAAC,IAAI;QAACC,YAAY;QAAAF,QAAA,EAAC;MAEtC;QAAAG,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAY,CAAC,eAGbnJ,OAAA,CAAC3D,KAAK;QAAC+M,EAAE,EAAE;UAAEC,CAAC,EAAE,CAAC;UAAEC,EAAE,EAAE;QAAE,CAAE;QAAAT,QAAA,gBACzB7I,OAAA,CAAC1D,UAAU;UAACwM,OAAO,EAAC,WAAW;UAACC,YAAY;UAAAF,QAAA,EAAC;QAE7C;UAAAG,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAY,CAAC,eACbnJ,OAAA,CAACpD,IAAI;UAAC2M,SAAS;UAACC,OAAO,EAAE,CAAE;UAACC,UAAU,EAAC,QAAQ;UAAAZ,QAAA,gBAC7C7I,OAAA,CAACpD,IAAI;YAAC8M,IAAI;YAACC,EAAE,EAAE,CAAE;YAAAd,QAAA,eACf7I,OAAA,CAACzD,SAAS;cACRqN,SAAS;cACTC,KAAK,EAAC,SAAS;cACff,OAAO,EAAC,UAAU;cAClB5B,KAAK,EAAE3F,WAAY;cACnBuI,QAAQ,EAAG9C,CAAC,IAAKxF,cAAc,CAACwF,CAAC,CAACG,MAAM,CAACD,KAAK,CAAE;cAChD6C,WAAW,EAAC;YAAyB;cAAAf,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACtC;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACE,CAAC,eACPnJ,OAAA,CAACpD,IAAI;YAAC8M,IAAI;YAACC,EAAE,EAAE,CAAE;YAAAd,QAAA,eACf7I,OAAA,CAACxD,MAAM;cACLoN,SAAS;cACTd,OAAO,EAAC,WAAW;cACnBkB,KAAK,EAAC,SAAS;cACfC,OAAO,EAAEvE,oBAAqB;cAC9BwE,QAAQ,EAAErJ,WAAW,IAAI,CAACU,WAAW,CAACoE,IAAI,CAAC,CAAE;cAC7CwE,SAAS,EAAEtJ,WAAW,gBAAGb,OAAA,CAACzC,gBAAgB;gBAAC6M,IAAI,EAAE;cAAG;gBAAApB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,gBAAGnJ,OAAA,CAAChC,UAAU;gBAAAgL,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAE;cAAAN,QAAA,EAC1E;YAED;cAAAG,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACL,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACF,CAAC,eAGRnJ,OAAA,CAAC3D,KAAK;QAAC+M,EAAE,EAAE;UAAEC,CAAC,EAAE;QAAE,CAAE;QAAAR,QAAA,gBAClB7I,OAAA,CAAC1D,UAAU;UAACwM,OAAO,EAAC,WAAW;UAACC,YAAY;UAAAF,QAAA,EAAC;QAE7C;UAAAG,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAY,CAAC,EAEZtI,WAAW,gBACVb,OAAA,CAAC5D,GAAG;UAACgN,EAAE,EAAE;YAAEiB,OAAO,EAAE,MAAM;YAAEC,cAAc,EAAE,QAAQ;YAAEC,EAAE,EAAE;UAAE,CAAE;UAAA1B,QAAA,eAC5D7I,OAAA,CAACzC,gBAAgB;YAAAyL,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACjB,CAAC,GACJlI,IAAI,CAACkC,MAAM,KAAK,CAAC,gBACnBnD,OAAA,CAAC1C,KAAK;UAACkN,QAAQ,EAAC,MAAM;UAAA3B,QAAA,EAAC;QAEvB;UAAAG,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAO,CAAC,gBAERnJ,OAAA,CAAC/C,IAAI;UAACmM,EAAE,EAAE;YAAEqB,SAAS,EAAE,OAAO;YAAEC,QAAQ,EAAE;UAAO,CAAE;UAAA7B,QAAA,EAChD5H,IAAI,CAAC0J,GAAG,CAAEpE,IAAI,iBACbvG,OAAA,CAAChE,KAAK,CAACiE,QAAQ;YAAA4I,QAAA,gBACb7I,OAAA,CAAC9C,QAAQ;cAAC0N,MAAM;cAACX,OAAO,EAAEA,CAAA,KAAM5D,gBAAgB,CAACE,IAAI,CAAE;cAAAsC,QAAA,gBACrD7I,OAAA,CAAC7C,YAAY;gBACX0N,OAAO,eACL7K,OAAA,CAAC5D,GAAG;kBAACgN,EAAE,EAAE;oBAAEiB,OAAO,EAAE,MAAM;oBAAEZ,UAAU,EAAE;kBAAS,CAAE;kBAAAZ,QAAA,gBACjD7I,OAAA,CAAC1D,UAAU;oBAACwM,OAAO,EAAC,WAAW;oBAAAD,QAAA,EAAEtC,IAAI,CAAC5E;kBAAO;oBAAAqH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAa,CAAC,EAC1D1J,YAAY,CAAC8G,IAAI,CAAC,gBACjBvG,OAAA,CAACtC,IAAI;oBACH0M,IAAI,EAAC,OAAO;oBACZP,KAAK,EAAC,OAAO;oBACbG,KAAK,EAAC,OAAO;oBACbZ,EAAE,EAAE;sBAAE0B,EAAE,EAAE;oBAAE;kBAAE;oBAAA9B,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACf,CAAC,GACAzJ,gBAAgB,CAAC6G,IAAI,CAAC,gBACxBvG,OAAA,CAACtC,IAAI;oBACH0M,IAAI,EAAC,OAAO;oBACZP,KAAK,EAAC,YAAY;oBAClBG,KAAK,EAAC,SAAS;oBACfZ,EAAE,EAAE;sBAAE0B,EAAE,EAAE;oBAAE;kBAAE;oBAAA9B,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACf,CAAC,gBAEFnJ,OAAA,CAACtC,IAAI;oBACH0M,IAAI,EAAC,OAAO;oBACZP,KAAK,EAAEtD,IAAI,CAACL,mBAAoB;oBAChC8D,KAAK,EAAErK,kBAAkB,CAAC4G,IAAI,CAACL,mBAAmB,CAAE;oBACpDkD,EAAE,EAAE;sBAAE0B,EAAE,EAAE;oBAAE;kBAAE;oBAAA9B,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACf,CACF;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACE,CACN;gBACD4B,SAAS,eACP/K,OAAA,CAAAE,SAAA;kBAAA2I,QAAA,gBACE7I,OAAA,CAAC1D,UAAU;oBAACwM,OAAO,EAAC,OAAO;oBAACkC,SAAS,EAAC,MAAM;oBAAAnC,QAAA,GACzCtC,IAAI,CAACrB,SAAS,IAAI,KAAK,EAAC,KAAG,EAACqB,IAAI,CAACpB,YAAY,IAAI,KAAK,EAAC,KAAG,EAACoB,IAAI,CAACnB,OAAO,IAAI,KAAK;kBAAA;oBAAA4D,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACvE,CAAC,eACbnJ,OAAA;oBAAAgJ,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAK,CAAC,eACNnJ,OAAA,CAAC1D,UAAU;oBAACwM,OAAO,EAAC,OAAO;oBAACkC,SAAS,EAAC,MAAM;oBAAAnC,QAAA,GAAC,MACvC,EAACtC,IAAI,CAAC0E,mBAAmB,IAAI,KAAK,EAAC,QAAM,EAAC1E,IAAI,CAAC2E,iBAAiB,IAAI,KAAK;kBAAA;oBAAAlC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACnE,CAAC,eACbnJ,OAAA;oBAAAgJ,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAK,CAAC,eACNnJ,OAAA,CAAC1D,UAAU;oBAACwM,OAAO,EAAC,OAAO;oBAACkC,SAAS,EAAC,MAAM;oBAAAnC,QAAA,GAAC,iBAC5B,EAACtC,IAAI,CAACkB,aAAa,IAAI,KAAK,EAAC,mBAAiB,EAAClB,IAAI,CAACJ,eAAe,IAAI,GAAG;kBAAA;oBAAA6C,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAC/E,CAAC;gBAAA,eACb;cACH;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACF,CAAC,eACFnJ,OAAA,CAAC5C,uBAAuB;gBAAAyL,QAAA,eACtB7I,OAAA,CAACvC,UAAU;kBAAC0N,IAAI,EAAC,KAAK;kBAAClB,OAAO,EAAGjD,CAAC,IAAK;oBACrCA,CAAC,CAACoE,eAAe,CAAC,CAAC,CAAC,CAAC;oBACrB9J,eAAe,CAACiF,IAAI,CAAC;oBACrB5D,wBAAwB,CAAC,IAAI,CAAC;kBAChC,CAAE;kBAAAkG,QAAA,eACA7I,OAAA,CAAClB,QAAQ;oBAAAkK,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACF;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACU,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAClB,CAAC,eACXnJ,OAAA,CAAC3C,OAAO;cAAA2L,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC;UAAA,GAxDQ5C,IAAI,CAAC5E,OAAO;YAAAqH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAyDjB,CACjB;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACE,CACP;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACI,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACL,CAAC;EAEV,CAAC;;EAED;EACA,MAAMkC,WAAW,GAAGA,CAAA,KAAM;IACxB,IAAI,CAAChK,YAAY,EAAE,OAAO,IAAI;IAE9B,oBACErB,OAAA,CAAC5D,GAAG;MAAAyM,QAAA,gBACF7I,OAAA,CAAC1D,UAAU;QAACwM,OAAO,EAAC,IAAI;QAACC,YAAY;QAAAF,QAAA,EAAC;MAEtC;QAAAG,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAY,CAAC,eAEbnJ,OAAA,CAACb,eAAe;QACdoH,IAAI,EAAElF,YAAa;QACnBiK,OAAO,EAAE,IAAK;QACdC,KAAK,EAAC;MAA+B;QAAAvC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACtC,CAAC,eAEFnJ,OAAA,CAAC3D,KAAK;QAAC+M,EAAE,EAAE;UAAEC,CAAC,EAAE;QAAE,CAAE;QAAAR,QAAA,gBAClB7I,OAAA,CAAC1D,UAAU;UAACwM,OAAO,EAAC,WAAW;UAACC,YAAY;UAAAF,QAAA,EAAC;QAE7C;UAAAG,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAY,CAAC,eAGbnJ,OAAA,CAAC5D,GAAG;UAACgN,EAAE,EAAE;YAAEE,EAAE,EAAE,CAAC;YAAED,CAAC,EAAE,CAAC;YAAEmC,OAAO,EAAE,SAAS;YAAEC,YAAY,EAAE;UAAE,CAAE;UAAA5C,QAAA,gBAC5D7I,OAAA,CAAC1D,UAAU;YAACwM,OAAO,EAAC,WAAW;YAACC,YAAY;YAACK,EAAE,EAAE;cAAEsC,UAAU,EAAE;YAAO,CAAE;YAAA7C,QAAA,EAAC;UAEzE;YAAAG,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAY,CAAC,eACbnJ,OAAA,CAAC1D,UAAU;YAACwM,OAAO,EAAC,OAAO;YAAAD,QAAA,gBACzB7I,OAAA;cAAA6I,QAAA,EAAQ;YAAuB;cAAAG,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC,KAAC,EAAC9H,YAAY,CAACoG,aAAa,IAAI,KAAK,EAAC,IAChF;UAAA;YAAAuB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAY,CAAC,eACbnJ,OAAA,CAAC1D,UAAU;YAACwM,OAAO,EAAC,OAAO;YAAAD,QAAA,gBACzB7I,OAAA;cAAA6I,QAAA,EAAQ;YAAuB;cAAAG,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC,KAAC,EAAC9H,YAAY,CAAC6E,mBAAmB,IAAI,KAAK;UAAA;YAAA8C,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACzE,CAAC,EACZ1H,QAAQ,CAACI,SAAS,IAAIJ,QAAQ,CAACI,SAAS,KAAK,cAAc,IAAI,CAAC,MAAM;YACrE,MAAMmD,MAAM,GAAG7D,MAAM,CAAC6E,IAAI,CAACR,CAAC,IAAIA,CAAC,CAAC3D,SAAS,KAAKJ,QAAQ,CAACI,SAAS,CAAC;YACnE,OAAOmD,MAAM,gBACXhF,OAAA,CAAC1D,UAAU;cAACwM,OAAO,EAAC,OAAO;cAAAD,QAAA,gBACzB7I,OAAA;gBAAA6I,QAAA,EAAQ;cAAqB;gBAAAG,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC,KAAC,EAACnE,MAAM,CAACS,aAAa,IAAI,CAAC,EAAC,IACpE;YAAA;cAAAuD,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAY,CAAC,GACX,IAAI;UACV,CAAC,EAAE,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACD,CAAC,eAENnJ,OAAA,CAACzD,SAAS;UACRqN,SAAS;UACTC,KAAK,EAAC,cAAc;UACpBf,OAAO,EAAC,UAAU;UAClB7B,IAAI,EAAC,cAAc;UACnB0E,IAAI,EAAC,QAAQ;UACbzE,KAAK,EAAEzF,QAAQ,CAACG,YAAa;UAC7BkI,QAAQ,EAAE/C,gBAAiB;UAC3B1D,KAAK,EAAE,CAAC,CAACvB,UAAU,CAACF,YAAa;UACjCgK,UAAU,EAAE9J,UAAU,CAACF,YAAY,IAAII,YAAY,CAACJ,YAAa;UACjEiK,mBAAmB,EAAE;YACnBzC,EAAE,EAAE;cAAEY,KAAK,EAAEhI,YAAY,CAACJ,YAAY,IAAI,CAACE,UAAU,CAACF,YAAY,GAAG,cAAc,GAAG;YAAa;UACrG,CAAE;UACFwH,EAAE,EAAE;YAAEE,EAAE,EAAE;UAAE;QAAE;UAAAN,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACf,CAAC,EAEDnH,YAAY,CAACJ,YAAY,IAAI,CAACE,UAAU,CAACF,YAAY,iBACpD5B,OAAA,CAAC1C,KAAK;UAACkN,QAAQ,EAAC,SAAS;UAACpB,EAAE,EAAE;YAAEE,EAAE,EAAE;UAAE,CAAE;UAAAT,QAAA,EACrC7G,YAAY,CAACJ;QAAY;UAAAoH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACrB,CACR,eAEDnJ,OAAA,CAAC1C,KAAK;UAACkN,QAAQ,EAAC,MAAM;UAACpB,EAAE,EAAE;YAAE0C,EAAE,EAAE;UAAE,CAAE;UAAAjD,QAAA,EAAC;QAEtC;UAAAG,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAO,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACL,CAAC;EAEV,CAAC;;EAED;EACA,MAAM4C,WAAW,GAAGA,CAAA,KAAM;IACxB;IACA,MAAMC,eAAe,GAAI1D,QAAQ,IAAK;MACpC;MACA,IAAIA,QAAQ,IAAIA,QAAQ,CAAC5E,QAAQ,CAAC,IAAI,CAAC,EAAE;QACvC,OAAO4E,QAAQ,CAAC2D,KAAK,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;MAChC;MACA,OAAO3D,QAAQ;IACjB,CAAC;;IAED;IACA,MAAM4D,iBAAiB,GAAIC,YAAY,IAAK;MAC1C,OAAO,IAAI/L,UAAU,KAAK+L,YAAY,EAAE;IAC1C,CAAC;;IAED;IACA,MAAMC,mBAAmB,GAAIpH,MAAM,IAAK;MACtC,IAAI,CAACA,MAAM,IAAI,CAACvD,QAAQ,CAACG,YAAY,EAAE,OAAO,IAAI;MAClD,OAAO2F,UAAU,CAACvC,MAAM,CAACS,aAAa,CAAC,IAAI8B,UAAU,CAAC9F,QAAQ,CAACG,YAAY,CAAC;IAC9E,CAAC;;IAED;IACA,MAAMyK,uBAAuB,GAAIrF,CAAC,IAAK;MACrC,MAAMmF,YAAY,GAAGnF,CAAC,CAACG,MAAM,CAACD,KAAK,CAACvB,IAAI,CAAC,CAAC;;MAE1C;MACA,IAAIwG,YAAY,CAACG,WAAW,CAAC,CAAC,KAAK,GAAG,EAAE;QACtC5K,WAAW,CAAC;UACV,GAAGD,QAAQ;UACXI,SAAS,EAAE;QACb,CAAC,CAAC;QACFE,aAAa,CAAC;UACZ,GAAGD,UAAU;UACbyK,eAAe,EAAE;QACnB,CAAC,CAAC;QACF;MACF;MAEA,IAAIJ,YAAY,EAAE;QAChB;QACA,MAAMK,gBAAgB,GAAGN,iBAAiB,CAACC,YAAY,CAAC;;QAExD;QACA,MAAMM,eAAe,GAAGtL,MAAM,CAAC6E,IAAI,CAACR,CAAC,IAAIA,CAAC,CAAC3D,SAAS,KAAK2K,gBAAgB,CAAC;QAE1E,IAAIC,eAAe,EAAE;UACnB;UACA,IAAIA,eAAe,CAACxH,YAAY,KAAK,MAAM,IAAIwH,eAAe,CAACxH,YAAY,KAAK,WAAW,EAAE;YAC3FlD,aAAa,CAAC;cACZ,GAAGD,UAAU;cACbyK,eAAe,EAAE,aAAaJ,YAAY,eAAeM,eAAe,CAACxH,YAAY;YACvF,CAAC,CAAC;YACF;UACF;;UAEA;UACA,IAAI5D,YAAY,KACZoL,eAAe,CAACvH,SAAS,KAAK7D,YAAY,CAAC6D,SAAS,IACpDwH,MAAM,CAACD,eAAe,CAACtH,YAAY,CAAC,KAAKuH,MAAM,CAACrL,YAAY,CAAC8D,YAAY,CAAC,IAC1EuH,MAAM,CAACD,eAAe,CAACrH,OAAO,CAAC,KAAKsH,MAAM,CAACrL,YAAY,CAAC+D,OAAO,CAAC,CACnE,EAAE;YACD;YACA/C,mBAAmB,CAACoK,eAAe,CAAC;YACpCtK,6BAA6B,CAAC,IAAI,CAAC;YACnC;UACF;;UAEA;UACA,IAAIiK,mBAAmB,CAACK,eAAe,CAAC,EAAE;YACxC;YACA/K,WAAW,CAAC;cACV,GAAGD,QAAQ;cACXI,SAAS,EAAE2K;YACb,CAAC,CAAC;YACFzK,aAAa,CAAC;cACZ,GAAGD,UAAU;cACbyK,eAAe,EAAE;YACnB,CAAC,CAAC;UACJ,CAAC,MAAM;YACL;YACAxK,aAAa,CAAC;cACZ,GAAGD,UAAU;cACbyK,eAAe,EAAE,aAAaJ,YAAY,sCAAsCM,eAAe,CAAChH,aAAa;YAC/G,CAAC,CAAC;UACJ;QACF,CAAC,MAAM;UACL;UACA1D,aAAa,CAAC;YACZ,GAAGD,UAAU;YACbyK,eAAe,EAAE,UAAUJ,YAAY;UACzC,CAAC,CAAC;QACJ;MACF,CAAC,MAAM;QACL;QACAzK,WAAW,CAAC;UACV,GAAGD,QAAQ;UACXI,SAAS,EAAE;QACb,CAAC,CAAC;QACFE,aAAa,CAAC;UACZ,GAAGD,UAAU;UACbyK,eAAe,EAAE;QACnB,CAAC,CAAC;MACJ;IACF,CAAC;IAED,oBACEvM,OAAA,CAAC5D,GAAG;MAAAyM,QAAA,gBACF7I,OAAA,CAAC1D,UAAU;QAACwM,OAAO,EAAC,IAAI;QAACC,YAAY;QAAAF,QAAA,EAAC;MAEtC;QAAAG,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAY,CAAC,eAEbnJ,OAAA,CAAC3D,KAAK;QAAC+M,EAAE,EAAE;UAAEC,CAAC,EAAE;QAAE,CAAE;QAAAR,QAAA,gBAClB7I,OAAA,CAAC1D,UAAU;UAACwM,OAAO,EAAC,OAAO;UAAC6D,SAAS;UAAA9D,QAAA,EAAC;QAEtC;UAAAG,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAY,CAAC,EAEZpI,aAAa,gBACZf,OAAA,CAAC5D,GAAG;UAACgN,EAAE,EAAE;YAAEiB,OAAO,EAAE,MAAM;YAAEC,cAAc,EAAE,QAAQ;YAAEC,EAAE,EAAE;UAAE,CAAE;UAAA1B,QAAA,eAC5D7I,OAAA,CAACzC,gBAAgB;YAAAyL,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACjB,CAAC,gBAENnJ,OAAA,CAAC5D,GAAG;UAAAyM,QAAA,gBAEF7I,OAAA,CAACpD,IAAI;YAAC2M,SAAS;YAACC,OAAO,EAAE,CAAE;YAACJ,EAAE,EAAE;cAAEE,EAAE,EAAE;YAAE,CAAE;YAAAT,QAAA,gBACxC7I,OAAA,CAACpD,IAAI;cAAC8M,IAAI;cAACC,EAAE,EAAE,EAAG;cAACiD,EAAE,EAAE,CAAE;cAAA/D,QAAA,eACvB7I,OAAA,CAACzD,SAAS;gBACRqN,SAAS;gBACTC,KAAK,EAAC,yBAAyB;gBAC/Bf,OAAO,EAAC,UAAU;gBAClBiB,WAAW,EAAC,8BAA8B;gBAC1C6B,UAAU,EAAE9J,UAAU,CAACyK,eAAe,IAAI,kEAAmE;gBAC7GlJ,KAAK,EAAE,CAAC,CAACvB,UAAU,CAACyK,eAAgB;gBACpCM,MAAM,EAAER;cAAwB;gBAAArD,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACjC;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACE,CAAC,eACPnJ,OAAA,CAACpD,IAAI;cAAC8M,IAAI;cAACC,EAAE,EAAE,EAAG;cAACiD,EAAE,EAAE,CAAE;cAAA/D,QAAA,gBACvB7I,OAAA,CAAC1D,UAAU;gBAACwM,OAAO,EAAC,OAAO;gBAACM,EAAE,EAAE;kBAAE0C,EAAE,EAAE;gBAAE,CAAE;gBAAAjD,QAAA,GAAC,sBACrB,EAACpH,QAAQ,CAACI,SAAS,IAAI,GAAG;cAAA;gBAAAmH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACpC,CAAC,eAEbnJ,OAAA,CAACxD,MAAM;gBACLsM,OAAO,EAAC,UAAU;gBAClBkB,KAAK,EAAC,WAAW;gBACjBC,OAAO,EAAEA,CAAA,KAAM;kBACbvI,WAAW,CAAC;oBACV,GAAGD,QAAQ;oBACXI,SAAS,EAAE;kBACb,CAAC,CAAC;kBACFE,aAAa,CAAC;oBACZ,GAAGD,UAAU;oBACbyK,eAAe,EAAE;kBACnB,CAAC,CAAC;gBACJ,CAAE;gBACFnD,EAAE,EAAE;kBAAE0C,EAAE,EAAE;gBAAE,CAAE;gBAAAjD,QAAA,EACf;cAED;gBAAAG,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACL,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC,eAGPnJ,OAAA,CAACnD,WAAW;YAAC+M,SAAS;YAAAf,QAAA,gBACpB7I,OAAA,CAAClD,UAAU;cAACgQ,EAAE,EAAC,qBAAqB;cAAAjE,QAAA,EAAC;YAA4B;cAAAG,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAY,CAAC,eAC9EnJ,OAAA,CAACjD,MAAM;cACLgQ,OAAO,EAAC,qBAAqB;cAC7BD,EAAE,EAAC,eAAe;cAClB7F,IAAI,EAAC,WAAW;cAChBC,KAAK,EAAEzF,QAAQ,CAACI,SAAU;cAC1BgI,KAAK,EAAC,8BAA8B;cACpCC,QAAQ,EAAE/C,gBAAiB;cAAA8B,QAAA,gBAE3B7I,OAAA,CAAChD,QAAQ;gBAACkK,KAAK,EAAC,EAAE;gBAAA2B,QAAA,eAChB7I,OAAA;kBAAA6I,QAAA,EAAI;gBAAc;kBAAAG,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAI;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACf,CAAC,EACVhI,MAAM,CAACwJ,GAAG,CAAE3F,MAAM,iBACjBhF,OAAA,CAAChD,QAAQ;gBAEPkK,KAAK,EAAElC,MAAM,CAACnD,SAAU;gBACxBqI,QAAQ,EAAElF,MAAM,CAACS,aAAa,GAAG8B,UAAU,CAAC9F,QAAQ,CAACG,YAAY,CAAE;gBAAAiH,QAAA,GAElEmD,eAAe,CAAChH,MAAM,CAACnD,SAAS,CAAC,EAAC,KAAG,EAACmD,MAAM,CAACE,SAAS,IAAI,KAAK,EAAC,cAAY,EAACF,MAAM,CAACS,aAAa,IAAI,CAAC,EAAC,IAC1G;cAAA,GALOT,MAAM,CAACnD,SAAS;gBAAAmH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAKb,CACX,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACI,CAAC,eACTnJ,OAAA,CAACxC,cAAc;cAAAqL,QAAA,EAAC;YAEhB;cAAAG,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAgB,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACN,CAAC,EAGb1H,QAAQ,CAACI,SAAS,iBACjB7B,OAAA,CAAC5D,GAAG;YAACgN,EAAE,EAAE;cAAE0C,EAAE,EAAE,CAAC;cAAEzC,CAAC,EAAE,CAAC;cAAEmC,OAAO,EAAE,kBAAkB;cAAEC,YAAY,EAAE,CAAC;cAAEuB,MAAM,EAAE;YAAoB,CAAE;YAAAnE,QAAA,gBAClG7I,OAAA,CAAC1D,UAAU;cAACwM,OAAO,EAAC,WAAW;cAACC,YAAY;cAAAF,QAAA,EAAC;YAE7C;cAAAG,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAY,CAAC,EACZ,CAAC,MAAM;cACN,MAAMnE,MAAM,GAAG7D,MAAM,CAAC6E,IAAI,CAACR,CAAC,IAAIA,CAAC,CAAC3D,SAAS,KAAKJ,QAAQ,CAACI,SAAS,CAAC;cACnE,IAAImD,MAAM,EAAE;gBACV,oBACEhF,OAAA,CAACpD,IAAI;kBAAC2M,SAAS;kBAACC,OAAO,EAAE,CAAE;kBAAAX,QAAA,gBACzB7I,OAAA,CAACpD,IAAI;oBAAC8M,IAAI;oBAACC,EAAE,EAAE,EAAG;oBAACiD,EAAE,EAAE,CAAE;oBAAA/D,QAAA,gBACvB7I,OAAA,CAAC1D,UAAU;sBAACwM,OAAO,EAAC,OAAO;sBAAAD,QAAA,gBACzB7I,OAAA;wBAAA6I,QAAA,EAAQ;sBAAO;wBAAAG,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAQ,CAAC,KAAC,EAAC6C,eAAe,CAAChH,MAAM,CAACnD,SAAS,CAAC;oBAAA;sBAAAmH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACjD,CAAC,eACbnJ,OAAA,CAAC1D,UAAU;sBAACwM,OAAO,EAAC,OAAO;sBAAAD,QAAA,gBACzB7I,OAAA;wBAAA6I,QAAA,EAAQ;sBAAU;wBAAAG,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAQ,CAAC,KAAC,EAACnE,MAAM,CAACE,SAAS,IAAI,KAAK;oBAAA;sBAAA8D,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAC5C,CAAC,eACbnJ,OAAA,CAAC1D,UAAU;sBAACwM,OAAO,EAAC,OAAO;sBAAAD,QAAA,gBACzB7I,OAAA;wBAAA6I,QAAA,EAAQ;sBAAW;wBAAAG,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAQ,CAAC,KAAC,EAACnE,MAAM,CAACG,YAAY,IAAI,KAAK,EAAC,KAAG,EAACH,MAAM,CAACI,OAAO,IAAI,KAAK;oBAAA;sBAAA4D,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAC5E,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACT,CAAC,eACPnJ,OAAA,CAACpD,IAAI;oBAAC8M,IAAI;oBAACC,EAAE,EAAE,EAAG;oBAACiD,EAAE,EAAE,CAAE;oBAAA/D,QAAA,gBACvB7I,OAAA,CAAC1D,UAAU;sBAACwM,OAAO,EAAC,OAAO;sBAAAD,QAAA,gBACzB7I,OAAA;wBAAA6I,QAAA,EAAQ;sBAAa;wBAAAG,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAQ,CAAC,KAAC,EAACnE,MAAM,CAACiI,YAAY,IAAI,CAAC,EAAC,IAC3D;oBAAA;sBAAAjE,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAY,CAAC,eACbnJ,OAAA,CAAC1D,UAAU;sBAACwM,OAAO,EAAC,OAAO;sBAAAD,QAAA,gBACzB7I,OAAA;wBAAA6I,QAAA,EAAQ;sBAAc;wBAAAG,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAQ,CAAC,KAAC,EAACnE,MAAM,CAACS,aAAa,IAAI,CAAC,EAAC,IAC7D;oBAAA;sBAAAuD,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAY,CAAC,eACbnJ,OAAA,CAAC1D,UAAU;sBAACwM,OAAO,EAAC,OAAO;sBAAAD,QAAA,gBACzB7I,OAAA;wBAAA6I,QAAA,EAAQ;sBAAM;wBAAAG,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAQ,CAAC,KAAC,EAACnE,MAAM,CAACC,YAAY,IAAI,KAAK;oBAAA;sBAAA+D,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAC3C,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACT,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH,CAAC;cAEX;cACA,oBACEnJ,OAAA,CAAC1D,UAAU;gBAACwM,OAAO,EAAC,OAAO;gBAACkB,KAAK,EAAC,OAAO;gBAAAnB,QAAA,EAAC;cAE1C;gBAAAG,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAY,CAAC;YAEjB,CAAC,EAAE,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACD,CACN;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACE,CACN,EAEAhI,MAAM,CAACgC,MAAM,KAAK,CAAC,IAAI,CAACpC,aAAa,iBACpCf,OAAA,CAAC1C,KAAK;UAACkN,QAAQ,EAAC,SAAS;UAACpB,EAAE,EAAE;YAAE0C,EAAE,EAAE;UAAE,CAAE;UAAAjD,QAAA,EAAC;QAEzC;UAAAG,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAO,CACR;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACI,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACL,CAAC;EAEV,CAAC;;EAED;EACA,MAAM+D,WAAW,GAAGA,CAAA,KAAM;IACxB;IACA,MAAMlB,eAAe,GAAI1D,QAAQ,IAAK;MACpC;MACA,IAAIA,QAAQ,IAAIA,QAAQ,CAAC5E,QAAQ,CAAC,IAAI,CAAC,EAAE;QACvC,OAAO4E,QAAQ,CAAC2D,KAAK,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;MAChC;MACA,OAAO3D,QAAQ;IACjB,CAAC;;IAED;IACA,IAAI6D,YAAY,GAAG,SAAS;IAC5B,IAAIgB,UAAU,GAAG,IAAI;IAErB,IAAI1L,QAAQ,CAACI,SAAS,KAAK,cAAc,EAAE;MACzCsK,YAAY,GAAG,cAAc;IAC/B,CAAC,MAAM,IAAI1K,QAAQ,CAACI,SAAS,EAAE;MAC7BsK,YAAY,GAAGH,eAAe,CAACvK,QAAQ,CAACI,SAAS,CAAC;MAClD;MACAsL,UAAU,GAAGhM,MAAM,CAAC6E,IAAI,CAACR,CAAC,IAAIA,CAAC,CAAC3D,SAAS,KAAKJ,QAAQ,CAACI,SAAS,CAAC;IACnE;;IAEA;IACA,MAAM0G,kBAAkB,GAAGJ,2BAA2B,CAACZ,UAAU,CAAC9F,QAAQ,CAACG,YAAY,CAAC,EAAEP,YAAY,CAACoG,aAAa,CAAC;IAErH,oBACEzH,OAAA,CAAC5D,GAAG;MAAAyM,QAAA,gBACF7I,OAAA,CAAC1D,UAAU;QAACwM,OAAO,EAAC,IAAI;QAACC,YAAY;QAAAF,QAAA,EAAC;MAEtC;QAAAG,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAY,CAAC,eAEbnJ,OAAA,CAAC3D,KAAK;QAAC+M,EAAE,EAAE;UAAEC,CAAC,EAAE;QAAE,CAAE;QAAAR,QAAA,gBAClB7I,OAAA,CAAC1D,UAAU;UAACwM,OAAO,EAAC,WAAW;UAACC,YAAY;UAAAF,QAAA,EAAC;QAE7C;UAAAG,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAY,CAAC,eAGbnJ,OAAA,CAACb,eAAe;UACdoH,IAAI,EAAElF,YAAa;UACnBiK,OAAO,EAAE,IAAK;UACdC,KAAK,EAAC;QAAmB;UAAAvC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC1B,CAAC,eAGFnJ,OAAA,CAAC5D,GAAG;UAACgN,EAAE,EAAE;YAAE0C,EAAE,EAAE,CAAC;YAAEzC,CAAC,EAAE,CAAC;YAAEmC,OAAO,EAAE,SAAS;YAAEC,YAAY,EAAE;UAAE,CAAE;UAAA5C,QAAA,gBAC5D7I,OAAA,CAAC1D,UAAU;YAACwM,OAAO,EAAC,WAAW;YAACC,YAAY;YAACK,EAAE,EAAE;cAAEsC,UAAU,EAAE;YAAO,CAAE;YAAA7C,QAAA,EAAC;UAEzE;YAAAG,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAY,CAAC,eACbnJ,OAAA,CAACpD,IAAI;YAAC2M,SAAS;YAACC,OAAO,EAAE,CAAE;YAAAX,QAAA,gBACzB7I,OAAA,CAACpD,IAAI;cAAC8M,IAAI;cAACC,EAAE,EAAE,EAAG;cAACiD,EAAE,EAAE,CAAE;cAAA/D,QAAA,gBACvB7I,OAAA,CAAC1D,UAAU;gBAACwM,OAAO,EAAC,OAAO;gBAAAD,QAAA,gBACzB7I,OAAA;kBAAA6I,QAAA,EAAQ;gBAAa;kBAAAG,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,KAAC,EAAC1H,QAAQ,CAACG,YAAY,EAAC,IACxD;cAAA;gBAAAoH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAY,CAAC,eACbnJ,OAAA,CAAC1D,UAAU;gBAACwM,OAAO,EAAC,OAAO;gBAAAD,QAAA,gBACzB7I,OAAA;kBAAA6I,QAAA,EAAQ;gBAAoB;kBAAAG,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,KAAC,EAACZ,kBAAkB;cAAA;gBAAAS,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC/C,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACT,CAAC,eACPnJ,OAAA,CAACpD,IAAI;cAAC8M,IAAI;cAACC,EAAE,EAAE,EAAG;cAACiD,EAAE,EAAE,CAAE;cAAA/D,QAAA,gBACvB7I,OAAA,CAAC1D,UAAU;gBAACwM,OAAO,EAAC,OAAO;gBAAAD,QAAA,gBACzB7I,OAAA;kBAAA6I,QAAA,EAAQ;gBAAiB;kBAAAG,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,KAAC,EAACgD,YAAY;cAAA;gBAAAnD,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACtC,CAAC,EACZgE,UAAU,iBACTnN,OAAA,CAAC1D,UAAU;gBAACwM,OAAO,EAAC,OAAO;gBAAAD,QAAA,gBACzB7I,OAAA;kBAAA6I,QAAA,EAAQ;gBAAqB;kBAAAG,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,KAAC,EAACgE,UAAU,CAAC1H,aAAa,EAAC,IACnE;cAAA;gBAAAuD,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAY,CACb;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACG,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACJ,CAAC,EAELgE,UAAU,IAAI5F,UAAU,CAAC9F,QAAQ,CAACG,YAAY,CAAC,GAAG2F,UAAU,CAAC4F,UAAU,CAAC1H,aAAa,CAAC,iBACrFzF,OAAA,CAAC1C,KAAK;UAACkN,QAAQ,EAAC,SAAS;UAACpB,EAAE,EAAE;YAAE0C,EAAE,EAAE;UAAE,CAAE;UAAAjD,QAAA,gBACtC7I,OAAA;YAAA6I,QAAA,EAAQ;UAAW;YAAAG,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC,qBAAiB,EAAC1H,QAAQ,CAACG,YAAY,EAAC,4CAA0C,EAACuL,UAAU,CAAC1H,aAAa,EAAC,gDAE1I;QAAA;UAAAuD,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAO,CACR,eAEDnJ,OAAA,CAAC1C,KAAK;UAACkN,QAAQ,EAAC,MAAM;UAACpB,EAAE,EAAE;YAAE0C,EAAE,EAAE;UAAE,CAAE;UAAAjD,QAAA,GAAC,8EAEpC,EAACpH,QAAQ,CAACI,SAAS,IAAIJ,QAAQ,CAACI,SAAS,KAAK,cAAc,IAAI,gFAAgF;QAAA;UAAAmH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC3I,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACL,CAAC;EAEV,CAAC;;EAED;EACA,MAAMiE,cAAc,GAAIC,IAAI,IAAK;IAC/B,QAAQA,IAAI;MACV,KAAK,CAAC;QACJ,OAAOzE,WAAW,CAAC,CAAC;MAAE;MACxB,KAAK,CAAC;QACJ,OAAOmD,WAAW,CAAC,CAAC;MAAE;MACxB,KAAK,CAAC;QACJ,OAAOV,WAAW,CAAC,CAAC;MAAE;MACxB,KAAK,CAAC;QACJ,OAAO6B,WAAW,CAAC,CAAC;MAAE;MACxB;QACE,OAAO,mBAAmB;IAC9B;EACF,CAAC;;EAED;EACA,MAAMI,4BAA4B,GAAGA,CAAA,KAAM;IACzC/K,wBAAwB,CAAC,KAAK,CAAC;IAC/BE,kBAAkB,CAAC,IAAI,CAAC;EAC1B,CAAC;;EAED;EACA,MAAM8K,gBAAgB,GAAGA,CAAA,KAAM;IAC7B,IAAI/K,eAAe,EAAE;MACnBhC,QAAQ,CAAC,mCAAmCJ,UAAU,IAAIoC,eAAe,CAACb,OAAO,EAAE,CAAC;IACtF;IACA2L,4BAA4B,CAAC,CAAC;EAChC,CAAC;;EAED;EACA,MAAME,wBAAwB,GAAGA,CAAA,KAAM;IACrCF,4BAA4B,CAAC,CAAC;IAC9B;IACA5M,aAAa,CAAC,CAAC,CAAC;IAChBY,eAAe,CAAC,IAAI,CAAC;IACrBE,cAAc,CAAC,EAAE,CAAC;EACpB,CAAC;;EAED;EACA,MAAMiM,iCAAiC,GAAGA,CAAA,KAAM;IAC9CtL,6BAA6B,CAAC,KAAK,CAAC;IACpCE,mBAAmB,CAAC,IAAI,CAAC;EAC3B,CAAC;;EAED;EACA,MAAMqL,2BAA2B,GAAG,MAAAA,CAAA,KAAY;IAC9C,IAAI,CAACrM,YAAY,IAAI,CAACe,gBAAgB,EAAE;IAExC,IAAI;MACFxB,UAAU,CAAC,IAAI,CAAC;MAChB;MACA,MAAM5B,WAAW,CAAC2O,qBAAqB,CAACvN,UAAU,EAAEiB,YAAY,CAACM,OAAO,EAAES,gBAAgB,CAAC;;MAE3F;MACA,MAAMwE,WAAW,GAAG,MAAM5H,WAAW,CAAC6G,WAAW,CAACzF,UAAU,EAAEiB,YAAY,CAACM,OAAO,CAAC;MACnFL,eAAe,CAACsF,WAAW,CAAC;;MAE5B;MACAlF,WAAW,CAAC;QACV,GAAGD,QAAQ;QACXI,SAAS,EAAEO,gBAAgB,CAACP;MAC9B,CAAC,CAAC;MAEFxB,SAAS,CAAC,4BAA4BgB,YAAY,CAACM,OAAO,6CAA6CS,gBAAgB,CAACP,SAAS,EAAE,CAAC;MACpI4L,iCAAiC,CAAC,CAAC;IACrC,CAAC,CAAC,OAAOpK,KAAK,EAAE;MACdN,OAAO,CAACM,KAAK,CAAC,iEAAiE,EAAEA,KAAK,CAAC;MACvF/C,OAAO,CAAC,kEAAkE,IAAI+C,KAAK,CAACI,OAAO,IAAI,oBAAoB,CAAC,CAAC;IACvH,CAAC,SAAS;MACR7C,UAAU,CAAC,KAAK,CAAC;IACnB;EACF,CAAC;;EAED;EACA,MAAMgN,uBAAuB,GAAGA,CAAA,KAAM;IACpCH,iCAAiC,CAAC,CAAC;IACnC;IACA/L,WAAW,CAAC;MACV,GAAGD,QAAQ;MACXI,SAAS,EAAE;IACb,CAAC,CAAC;EACJ,CAAC;EAED,oBACE7B,OAAA,CAAC5D,GAAG;IAAAyM,QAAA,gBACF7I,OAAA,CAACvD,OAAO;MAACgE,UAAU,EAAEA,UAAW;MAAC2I,EAAE,EAAE;QAAEE,EAAE,EAAE;MAAE,CAAE;MAAAT,QAAA,EAC5CjG,KAAK,CAAC+H,GAAG,CAAEd,KAAK,iBACf7J,OAAA,CAACtD,IAAI;QAAAmM,QAAA,eACH7I,OAAA,CAACrD,SAAS;UAAAkM,QAAA,EAAEgB;QAAK;UAAAb,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAY;MAAC,GADrBU,KAAK;QAAAb,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAEV,CACP;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACK,CAAC,eAEVnJ,OAAA,CAAC5D,GAAG;MAACgN,EAAE,EAAE;QAAE0C,EAAE,EAAE,CAAC;QAAExC,EAAE,EAAE;MAAE,CAAE;MAAAT,QAAA,EACvBuE,cAAc,CAAC3M,UAAU;IAAC;MAAAuI,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACxB,CAAC,eAENnJ,OAAA,CAAC5D,GAAG;MAACgN,EAAE,EAAE;QAAEiB,OAAO,EAAE,MAAM;QAAEC,cAAc,EAAE,eAAe;QAAEwB,EAAE,EAAE;MAAE,CAAE;MAAAjD,QAAA,gBACnE7I,OAAA,CAACxD,MAAM;QACLsM,OAAO,EAAC,UAAU;QAClBkB,KAAK,EAAC,WAAW;QACjBC,OAAO,EAAExJ,UAAU,KAAK,CAAC,GAAG,MAAMD,QAAQ,CAAC,sBAAsB,CAAC,GAAGyH,UAAW;QAChFkC,SAAS,eAAEnK,OAAA,CAAC5B,aAAa;UAAA4K,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAE;QAC7Be,QAAQ,EAAEvJ,OAAQ;QAAAkI,QAAA,EAEjBpI,UAAU,KAAK,CAAC,GAAG,SAAS,GAAG;MAAU;QAAAuI,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACpC,CAAC,eAETnJ,OAAA,CAACxD,MAAM;QACLsM,OAAO,EAAC,WAAW;QACnBkB,KAAK,EAAC,SAAS;QACfC,OAAO,EAAExJ,UAAU,KAAKmC,KAAK,CAACO,MAAM,GAAG,CAAC,GAAGkF,YAAY,GAAGN,UAAW;QACrE8F,OAAO,EAAEpN,UAAU,KAAKmC,KAAK,CAACO,MAAM,GAAG,CAAC,gBAAGnD,OAAA,CAAC9B,QAAQ;UAAA8K,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,gBAAGnJ,OAAA,CAAC1B,gBAAgB;UAAA0K,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAE;QAC/Ee,QAAQ,EAAEvJ,OAAO,IAAKF,UAAU,KAAK,CAAC,IAAI,CAACY,YAAc;QAAAwH,QAAA,EAExDlI,OAAO,gBACNX,OAAA,CAACzC,gBAAgB;UAAC6M,IAAI,EAAE;QAAG;UAAApB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,GAC5B1I,UAAU,KAAKmC,KAAK,CAACO,MAAM,GAAG,CAAC,GACjC,OAAO,GAEP;MACD;QAAA6F,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACK,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACN,CAAC,eAGNnJ,OAAA,CAACrC,MAAM;MAACmQ,IAAI,EAAExL,qBAAsB;MAACyL,OAAO,EAAET,4BAA6B;MAACU,QAAQ,EAAC,IAAI;MAACpE,SAAS;MAAAf,QAAA,gBACjG7I,OAAA,CAACpC,WAAW;QAACwL,EAAE,EAAE;UAAEoC,OAAO,EAAE;QAAgB,CAAE;QAAA3C,QAAA,eAC5C7I,OAAA,CAAC5D,GAAG;UAACgN,EAAE,EAAE;YAAEiB,OAAO,EAAE,MAAM;YAAEZ,UAAU,EAAE,QAAQ;YAAEwE,GAAG,EAAE;UAAE,CAAE;UAAApF,QAAA,gBACzD7I,OAAA,CAACpB,WAAW;YAACoL,KAAK,EAAC;UAAS;YAAAhB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,eAC/BnJ,OAAA,CAAC1D,UAAU;YAACwM,OAAO,EAAC,IAAI;YAAAD,QAAA,EAAC;UAAe;YAAAG,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAY,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAClD;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACK,CAAC,eACdnJ,OAAA,CAACnC,aAAa;QAAAgL,QAAA,EACXrG,eAAe,iBACdxC,OAAA,CAAC5D,GAAG;UAACgN,EAAE,EAAE;YAAE0C,EAAE,EAAE;UAAE,CAAE;UAAAjD,QAAA,gBACjB7I,OAAA,CAAC1D,UAAU;YAACwM,OAAO,EAAC,OAAO;YAAC6D,SAAS;YAAA9D,QAAA,GAAC,UAC5B,eAAA7I,OAAA;cAAA6I,QAAA,EAASrG,eAAe,CAACb;YAAO;cAAAqH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAS,CAAC,4BAAqB,EAAC3G,eAAe,CAAC2D,eAAe,IAAI,CAAC,EAAC,KAC/G;UAAA;YAAA6C,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAY,CAAC,eACbnJ,OAAA,CAAC1D,UAAU;YAACwM,OAAO,EAAC,OAAO;YAAC6D,SAAS;YAAA9D,QAAA,EAAC;UAEtC;YAAAG,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAY,CAAC,eACbnJ,OAAA,CAAC1D,UAAU;YAACwM,OAAO,EAAC,OAAO;YAACkC,SAAS,EAAC,IAAI;YAAAnC,QAAA,gBACxC7I,OAAA;cAAA6I,QAAA,EAAI;YAAsC;cAAAG,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eAC/CnJ,OAAA;cAAA6I,QAAA,EAAI;YAAyB;cAAAG,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eAClCnJ,OAAA;cAAA6I,QAAA,EAAI;YAAsB;cAAAG,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACrB,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACV;MACN;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACY,CAAC,eAChBnJ,OAAA,CAAClC,aAAa;QAACsL,EAAE,EAAE;UAAEC,CAAC,EAAE,CAAC;UAAEiB,cAAc,EAAE;QAAgB,CAAE;QAAAzB,QAAA,gBAC3D7I,OAAA,CAACxD,MAAM;UAACyN,OAAO,EAAEqD,4BAA6B;UAACtD,KAAK,EAAC,WAAW;UAAAnB,QAAA,EAAC;QAEjE;UAAAG,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC,eACTnJ,OAAA,CAAC5D,GAAG;UAAAyM,QAAA,gBACF7I,OAAA,CAACxD,MAAM;YAACyN,OAAO,EAAEuD,wBAAyB;YAACxD,KAAK,EAAC,SAAS;YAACZ,EAAE,EAAE;cAAE8E,EAAE,EAAE;YAAE,CAAE;YAAArF,QAAA,EAAC;UAE1E;YAAAG,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC,eACTnJ,OAAA,CAACxD,MAAM;YAACyN,OAAO,EAAEsD,gBAAiB;YAACzE,OAAO,EAAC,WAAW;YAACkB,KAAK,EAAC,SAAS;YAAAnB,QAAA,EAAC;UAEvE;YAAAG,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACN,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACO,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACV,CAAC,eAGTnJ,OAAA,CAACd,sBAAsB;MACrB4O,IAAI,EAAE5L,0BAA2B;MACjC6L,OAAO,EAAEN,iCAAkC;MAC3ClH,IAAI,EAAElF,YAAa;MACnB2D,MAAM,EAAE5C,gBAAiB;MACzB+L,YAAY,EAAET,2BAA4B;MAC1CU,mBAAmB,EAAER;IAAwB;MAAA5E,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAC9C,CAAC,eAGFnJ,OAAA,CAACrC,MAAM;MACLmQ,IAAI,EAAEpL,qBAAsB;MAC5BqL,OAAO,EAAEA,CAAA,KAAMpL,wBAAwB,CAAC,KAAK,CAAE;MAC/CqL,QAAQ,EAAC,IAAI;MACbpE,SAAS;MAAAf,QAAA,gBAET7I,OAAA,CAACpC,WAAW;QAAAiL,QAAA,eACV7I,OAAA,CAAC5D,GAAG;UAACgN,EAAE,EAAE;YAAEiB,OAAO,EAAE,MAAM;YAAEZ,UAAU,EAAE,QAAQ;YAAEwE,GAAG,EAAE;UAAE,CAAE;UAAApF,QAAA,gBACzD7I,OAAA,CAAClB,QAAQ;YAACkL,KAAK,EAAC;UAAS;YAAAhB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,eAC5BnJ,OAAA,CAAC1D,UAAU;YAACwM,OAAO,EAAC,IAAI;YAAAD,QAAA,EAAC;UAAa;YAAAG,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAY,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAChD;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACK,CAAC,eACdnJ,OAAA,CAACnC,aAAa;QAAAgL,QAAA,eACZ7I,OAAA,CAACb,eAAe;UAACoH,IAAI,EAAElF;QAAa;UAAA2H,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC1B,CAAC,eAChBnJ,OAAA,CAAClC,aAAa;QAAA+K,QAAA,eACZ7I,OAAA,CAACxD,MAAM;UAACyN,OAAO,EAAEA,CAAA,KAAMtH,wBAAwB,CAAC,KAAK,CAAE;UAACqH,KAAK,EAAC,SAAS;UAAAnB,QAAA,EAAC;QAExE;UAAAG,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACI,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACV,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACN,CAAC;AAEV,CAAC;AAAC5I,EAAA,CA7vCIJ,kBAAkB;EAAA,QACLpB,WAAW;AAAA;AAAAsP,EAAA,GADxBlO,kBAAkB;AA+vCxB,eAAeA,kBAAkB;AAAC,IAAAkO,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}