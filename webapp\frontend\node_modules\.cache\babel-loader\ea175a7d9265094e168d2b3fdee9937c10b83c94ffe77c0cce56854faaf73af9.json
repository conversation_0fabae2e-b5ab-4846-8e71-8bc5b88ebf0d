{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\PycharmProjects\\\\CMS\\\\webapp\\\\frontend\\\\src\\\\App.js\",\n  _s = $RefreshSig$();\nimport React from 'react';\nimport { Routes, Route, Navigate } from 'react-router-dom';\nimport { ThemeProvider, createTheme } from '@mui/material/styles';\nimport CssBaseline from '@mui/material/CssBaseline';\nimport { useAuth } from './context/AuthContext';\nimport { GlobalProvider } from './context/GlobalContext';\nimport LoginPage from './pages/LoginPageNew';\nimport Dashboard from './pages/Dashboard';\nimport ProtectedRoute from './components/ProtectedRoute';\n\n// Tema personalizzato\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst theme = createTheme({\n  palette: {\n    primary: {\n      main: '#1976d2'\n    },\n    secondary: {\n      main: '#dc004e'\n    },\n    info: {\n      main: '#0288d1'\n    },\n    success: {\n      main: '#2e7d32'\n    },\n    warning: {\n      main: '#ed6c02'\n    },\n    error: {\n      main: '#d32f2f'\n    }\n  }\n});\nfunction App() {\n  _s();\n  const {\n    isAuthenticated,\n    loading,\n    user\n  } = useAuth();\n  console.log('App - Stato autenticazione:', {\n    isAuthenticated,\n    loading\n  });\n\n  // Se l'applicazione è in caricamento, mostra un indicatore di caricamento\n  if (loading) {\n    console.log('App - Mostrando indicatore di caricamento');\n    return /*#__PURE__*/_jsxDEV(ThemeProvider, {\n      theme: theme,\n      children: [/*#__PURE__*/_jsxDEV(CssBaseline, {}, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 46,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        style: {\n          display: 'flex',\n          justifyContent: 'center',\n          alignItems: 'center',\n          height: '100vh'\n        },\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          style: {\n            textAlign: 'center'\n          },\n          children: /*#__PURE__*/_jsxDEV(\"div\", {\n            children: \"Caricamento...\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 49,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 48,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 47,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 45,\n      columnNumber: 7\n    }, this);\n  }\n  console.log('App - Rendering principale, isAuthenticated:', isAuthenticated);\n  return /*#__PURE__*/_jsxDEV(ThemeProvider, {\n    theme: theme,\n    children: [/*#__PURE__*/_jsxDEV(CssBaseline, {}, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 60,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Routes, {\n      children: [/*#__PURE__*/_jsxDEV(Route, {\n        path: \"/login\",\n        element: isAuthenticated ? (user === null || user === void 0 ? void 0 : user.role) === 'owner' ? /*#__PURE__*/_jsxDEV(Navigate, {\n          to: \"/dashboard/admin\",\n          replace: true\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 64,\n          columnNumber: 38\n        }, this) : (user === null || user === void 0 ? void 0 : user.role) === 'user' ? /*#__PURE__*/_jsxDEV(Navigate, {\n          to: \"/dashboard/cantieri\",\n          replace: true\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 65,\n          columnNumber: 37\n        }, this) : (user === null || user === void 0 ? void 0 : user.role) === 'cantieri_user' ? /*#__PURE__*/_jsxDEV(Navigate, {\n          to: \"/dashboard/cavi/visualizza\",\n          replace: true\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 66,\n          columnNumber: 46\n        }, this) : /*#__PURE__*/_jsxDEV(Navigate, {\n          to: \"/dashboard\",\n          replace: true\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 67,\n          columnNumber: 13\n        }, this) : /*#__PURE__*/_jsxDEV(LoginPage, {}, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 68,\n          columnNumber: 15\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 62,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Route, {\n        path: \"/dashboard/*\",\n        element: /*#__PURE__*/_jsxDEV(ProtectedRoute, {\n          children: /*#__PURE__*/_jsxDEV(Dashboard, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 74,\n            columnNumber: 15\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 73,\n          columnNumber: 13\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 70,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Route, {\n        path: \"/\",\n        element: isAuthenticated ? (user === null || user === void 0 ? void 0 : user.role) === 'owner' ? /*#__PURE__*/_jsxDEV(Navigate, {\n          to: \"/dashboard/admin\",\n          replace: true\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 82,\n          columnNumber: 40\n        }, this) : (user === null || user === void 0 ? void 0 : user.role) === 'user' ? /*#__PURE__*/_jsxDEV(Navigate, {\n          to: \"/dashboard/cantieri\",\n          replace: true\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 83,\n          columnNumber: 39\n        }, this) : (user === null || user === void 0 ? void 0 : user.role) === 'cantieri_user' ? /*#__PURE__*/_jsxDEV(Navigate, {\n          to: \"/dashboard/cavi/visualizza\",\n          replace: true\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 84,\n          columnNumber: 48\n        }, this) : /*#__PURE__*/_jsxDEV(Navigate, {\n          to: \"/dashboard\",\n          replace: true\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 85,\n          columnNumber: 15\n        }, this) : /*#__PURE__*/_jsxDEV(Navigate, {\n          to: \"/login\",\n          replace: true\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 87,\n          columnNumber: 15\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 78,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 61,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 59,\n    columnNumber: 5\n  }, this);\n}\n_s(App, \"8HNWTDX9ZYet8YmVN2bU91bK+h8=\", false, function () {\n  return [useAuth];\n});\n_c = App;\nexport default App;\nvar _c;\n$RefreshReg$(_c, \"App\");", "map": {"version": 3, "names": ["React", "Routes", "Route", "Navigate", "ThemeProvider", "createTheme", "CssBaseline", "useAuth", "GlobalProvider", "LoginPage", "Dashboard", "ProtectedRoute", "jsxDEV", "_jsxDEV", "theme", "palette", "primary", "main", "secondary", "info", "success", "warning", "error", "App", "_s", "isAuthenticated", "loading", "user", "console", "log", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "style", "display", "justifyContent", "alignItems", "height", "textAlign", "path", "element", "role", "to", "replace", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/PycharmProjects/CMS/webapp/frontend/src/App.js"], "sourcesContent": ["import React from 'react';\nimport { Routes, Route, Navigate } from 'react-router-dom';\nimport { ThemeProvider, createTheme } from '@mui/material/styles';\nimport CssBaseline from '@mui/material/CssBaseline';\n\nimport { useAuth } from './context/AuthContext';\nimport { GlobalProvider } from './context/GlobalContext';\nimport LoginPage from './pages/LoginPageNew';\nimport Dashboard from './pages/Dashboard';\nimport ProtectedRoute from './components/ProtectedRoute';\n\n// Tema personalizzato\nconst theme = createTheme({\n  palette: {\n    primary: {\n      main: '#1976d2',\n    },\n    secondary: {\n      main: '#dc004e',\n    },\n    info: {\n      main: '#0288d1',\n    },\n    success: {\n      main: '#2e7d32',\n    },\n    warning: {\n      main: '#ed6c02',\n    },\n    error: {\n      main: '#d32f2f',\n    },\n  },\n});\n\nfunction App() {\n  const { isAuthenticated, loading, user } = useAuth();\n\n  console.log('App - Stato autenticazione:', { isAuthenticated, loading });\n\n  // Se l'applicazione è in caricamento, mostra un indicatore di caricamento\n  if (loading) {\n    console.log('App - Mostrando indicatore di caricamento');\n    return (\n      <ThemeProvider theme={theme}>\n        <CssBaseline />\n        <div style={{ display: 'flex', justifyContent: 'center', alignItems: 'center', height: '100vh' }}>\n          <div style={{ textAlign: 'center' }}>\n            <div>Caricamento...</div>\n          </div>\n        </div>\n      </ThemeProvider>\n    );\n  }\n\n  console.log('App - Rendering principale, isAuthenticated:', isAuthenticated);\n\n  return (\n    <ThemeProvider theme={theme}>\n      <CssBaseline />\n      <Routes>\n        <Route path=\"/login\" element={\n          isAuthenticated ? (\n            user?.role === 'owner' ? <Navigate to=\"/dashboard/admin\" replace /> :\n            user?.role === 'user' ? <Navigate to=\"/dashboard/cantieri\" replace /> :\n            user?.role === 'cantieri_user' ? <Navigate to=\"/dashboard/cavi/visualizza\" replace /> :\n            <Navigate to=\"/dashboard\" replace />\n          ) : <LoginPage />\n        } />\n        <Route\n          path=\"/dashboard/*\"\n          element={\n            <ProtectedRoute>\n              <Dashboard />\n            </ProtectedRoute>\n          }\n        />\n        <Route\n          path=\"/\"\n          element={\n            isAuthenticated ? (\n              user?.role === 'owner' ? <Navigate to=\"/dashboard/admin\" replace /> :\n              user?.role === 'user' ? <Navigate to=\"/dashboard/cantieri\" replace /> :\n              user?.role === 'cantieri_user' ? <Navigate to=\"/dashboard/cavi/visualizza\" replace /> :\n              <Navigate to=\"/dashboard\" replace />\n            ) : (\n              <Navigate to=\"/login\" replace />\n            )\n          }\n        />\n      </Routes>\n    </ThemeProvider>\n  );\n}\n\nexport default App;\n"], "mappings": ";;AAAA,OAAOA,KAAK,MAAM,OAAO;AACzB,SAASC,MAAM,EAAEC,KAAK,EAAEC,QAAQ,QAAQ,kBAAkB;AAC1D,SAASC,aAAa,EAAEC,WAAW,QAAQ,sBAAsB;AACjE,OAAOC,WAAW,MAAM,2BAA2B;AAEnD,SAASC,OAAO,QAAQ,uBAAuB;AAC/C,SAASC,cAAc,QAAQ,yBAAyB;AACxD,OAAOC,SAAS,MAAM,sBAAsB;AAC5C,OAAOC,SAAS,MAAM,mBAAmB;AACzC,OAAOC,cAAc,MAAM,6BAA6B;;AAExD;AAAA,SAAAC,MAAA,IAAAC,OAAA;AACA,MAAMC,KAAK,GAAGT,WAAW,CAAC;EACxBU,OAAO,EAAE;IACPC,OAAO,EAAE;MACPC,IAAI,EAAE;IACR,CAAC;IACDC,SAAS,EAAE;MACTD,IAAI,EAAE;IACR,CAAC;IACDE,IAAI,EAAE;MACJF,IAAI,EAAE;IACR,CAAC;IACDG,OAAO,EAAE;MACPH,IAAI,EAAE;IACR,CAAC;IACDI,OAAO,EAAE;MACPJ,IAAI,EAAE;IACR,CAAC;IACDK,KAAK,EAAE;MACLL,IAAI,EAAE;IACR;EACF;AACF,CAAC,CAAC;AAEF,SAASM,GAAGA,CAAA,EAAG;EAAAC,EAAA;EACb,MAAM;IAAEC,eAAe;IAAEC,OAAO;IAAEC;EAAK,CAAC,GAAGpB,OAAO,CAAC,CAAC;EAEpDqB,OAAO,CAACC,GAAG,CAAC,6BAA6B,EAAE;IAAEJ,eAAe;IAAEC;EAAQ,CAAC,CAAC;;EAExE;EACA,IAAIA,OAAO,EAAE;IACXE,OAAO,CAACC,GAAG,CAAC,2CAA2C,CAAC;IACxD,oBACEhB,OAAA,CAACT,aAAa;MAACU,KAAK,EAAEA,KAAM;MAAAgB,QAAA,gBAC1BjB,OAAA,CAACP,WAAW;QAAAyB,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC,eACfrB,OAAA;QAAKsB,KAAK,EAAE;UAAEC,OAAO,EAAE,MAAM;UAAEC,cAAc,EAAE,QAAQ;UAAEC,UAAU,EAAE,QAAQ;UAAEC,MAAM,EAAE;QAAQ,CAAE;QAAAT,QAAA,eAC/FjB,OAAA;UAAKsB,KAAK,EAAE;YAAEK,SAAS,EAAE;UAAS,CAAE;UAAAV,QAAA,eAClCjB,OAAA;YAAAiB,QAAA,EAAK;UAAc;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAK;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACtB;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACO,CAAC;EAEpB;EAEAN,OAAO,CAACC,GAAG,CAAC,8CAA8C,EAAEJ,eAAe,CAAC;EAE5E,oBACEZ,OAAA,CAACT,aAAa;IAACU,KAAK,EAAEA,KAAM;IAAAgB,QAAA,gBAC1BjB,OAAA,CAACP,WAAW;MAAAyB,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC,eACfrB,OAAA,CAACZ,MAAM;MAAA6B,QAAA,gBACLjB,OAAA,CAACX,KAAK;QAACuC,IAAI,EAAC,QAAQ;QAACC,OAAO,EAC1BjB,eAAe,GACb,CAAAE,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAEgB,IAAI,MAAK,OAAO,gBAAG9B,OAAA,CAACV,QAAQ;UAACyC,EAAE,EAAC,kBAAkB;UAACC,OAAO;QAAA;UAAAd,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,GACnE,CAAAP,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAEgB,IAAI,MAAK,MAAM,gBAAG9B,OAAA,CAACV,QAAQ;UAACyC,EAAE,EAAC,qBAAqB;UAACC,OAAO;QAAA;UAAAd,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,GACrE,CAAAP,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAEgB,IAAI,MAAK,eAAe,gBAAG9B,OAAA,CAACV,QAAQ;UAACyC,EAAE,EAAC,4BAA4B;UAACC,OAAO;QAAA;UAAAd,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,gBACrFrB,OAAA,CAACV,QAAQ;UAACyC,EAAE,EAAC,YAAY;UAACC,OAAO;QAAA;UAAAd,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,gBAClCrB,OAAA,CAACJ,SAAS;UAAAsB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE;MACjB;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC,eACJrB,OAAA,CAACX,KAAK;QACJuC,IAAI,EAAC,cAAc;QACnBC,OAAO,eACL7B,OAAA,CAACF,cAAc;UAAAmB,QAAA,eACbjB,OAAA,CAACH,SAAS;YAAAqB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACC;MACjB;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACF,CAAC,eACFrB,OAAA,CAACX,KAAK;QACJuC,IAAI,EAAC,GAAG;QACRC,OAAO,EACLjB,eAAe,GACb,CAAAE,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAEgB,IAAI,MAAK,OAAO,gBAAG9B,OAAA,CAACV,QAAQ;UAACyC,EAAE,EAAC,kBAAkB;UAACC,OAAO;QAAA;UAAAd,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,GACnE,CAAAP,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAEgB,IAAI,MAAK,MAAM,gBAAG9B,OAAA,CAACV,QAAQ;UAACyC,EAAE,EAAC,qBAAqB;UAACC,OAAO;QAAA;UAAAd,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,GACrE,CAAAP,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAEgB,IAAI,MAAK,eAAe,gBAAG9B,OAAA,CAACV,QAAQ;UAACyC,EAAE,EAAC,4BAA4B;UAACC,OAAO;QAAA;UAAAd,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,gBACrFrB,OAAA,CAACV,QAAQ;UAACyC,EAAE,EAAC,YAAY;UAACC,OAAO;QAAA;UAAAd,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,gBAEpCrB,OAAA,CAACV,QAAQ;UAACyC,EAAE,EAAC,QAAQ;UAACC,OAAO;QAAA;UAAAd,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE;MAElC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACF,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACI,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACI,CAAC;AAEpB;AAACV,EAAA,CA1DQD,GAAG;EAAA,QACiChB,OAAO;AAAA;AAAAuC,EAAA,GAD3CvB,GAAG;AA4DZ,eAAeA,GAAG;AAAC,IAAAuB,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}