{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\PycharmProjects\\\\CMS\\\\webapp\\\\frontend\\\\src\\\\components\\\\MainMenu.js\",\n  _s = $RefreshSig$();\nimport React from 'react';\nimport { useNavigate, useLocation } from 'react-router-dom';\nimport { List, ListItem, ListItemIcon, ListItemText, Divider, Box, Typography } from '@mui/material';\nimport { Home as HomeIcon, AdminPanelSettings as AdminIcon, Construction as ConstructionIcon, Cable as CableIcon, Description as ReportIcon } from '@mui/icons-material';\nimport { useAuth } from '../context/AuthContext';\nimport { jsxDEV as _jsxDEV, Fragment as _Fragment } from \"react/jsx-dev-runtime\";\nconst MainMenu = () => {\n  _s();\n  const navigate = useNavigate();\n  const location = useLocation();\n  const {\n    user,\n    isImpersonating,\n    impersonatedUser\n  } = useAuth();\n\n  // Verifica se un percorso è attivo\n  const isActive = path => {\n    return location.pathname === path;\n  };\n\n  // Naviga a un percorso\n  const navigateTo = path => {\n    console.log('Navigazione a:', path, 'isImpersonating:', isImpersonating, 'user:', user);\n    // Se l'utente è un amministratore che sta impersonando un utente e sta cliccando su Home,\n    // reindirizza al menu amministratore invece che alla home generica\n    if (path === '/dashboard' && isImpersonating) {\n      console.log('Utente impersonato, reindirizzamento al menu amministratore');\n      navigate('/dashboard/admin');\n    } else {\n      navigate(path);\n    }\n  };\n  return /*#__PURE__*/_jsxDEV(List, {\n    children: [/*#__PURE__*/_jsxDEV(ListItem, {\n      button: true,\n      selected: isImpersonating ? isActive('/dashboard/admin') : isActive('/dashboard'),\n      onClick: () => navigateTo('/dashboard'),\n      children: [/*#__PURE__*/_jsxDEV(ListItemIcon, {\n        children: /*#__PURE__*/_jsxDEV(HomeIcon, {}, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 53,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 52,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(ListItemText, {\n        primary: isImpersonating ? \"Torna al Menu Admin\" : \"Home\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 55,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 47,\n      columnNumber: 7\n    }, this), (user === null || user === void 0 ? void 0 : user.role) === 'owner' && /*#__PURE__*/_jsxDEV(_Fragment, {\n      children: [/*#__PURE__*/_jsxDEV(Divider, {}, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 61,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(ListItem, {\n        button: true,\n        selected: isActive('/dashboard/admin'),\n        onClick: () => navigateTo('/dashboard/admin'),\n        children: [/*#__PURE__*/_jsxDEV(ListItemIcon, {\n          children: /*#__PURE__*/_jsxDEV(AdminIcon, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 68,\n            columnNumber: 15\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 67,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(ListItemText, {\n          primary: \"Amministrazione\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 70,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 62,\n        columnNumber: 11\n      }, this)]\n    }, void 0, true), ((user === null || user === void 0 ? void 0 : user.role) !== 'owner' || (user === null || user === void 0 ? void 0 : user.role) === 'owner' && isImpersonating && impersonatedUser) && /*#__PURE__*/_jsxDEV(_Fragment, {\n      children: [/*#__PURE__*/_jsxDEV(Divider, {}, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 79,\n        columnNumber: 11\n      }, this), isImpersonating && impersonatedUser && /*#__PURE__*/_jsxDEV(Box, {\n        sx: {\n          p: 2,\n          bgcolor: 'rgba(255, 165, 0, 0.1)',\n          borderLeft: '4px solid orange'\n        },\n        children: [/*#__PURE__*/_jsxDEV(Typography, {\n          variant: \"subtitle2\",\n          color: \"textSecondary\",\n          children: \"Accesso come utente:\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 82,\n          columnNumber: 15\n        }, this), /*#__PURE__*/_jsxDEV(Typography, {\n          variant: \"body2\",\n          fontWeight: \"bold\",\n          children: impersonatedUser.username\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 85,\n          columnNumber: 15\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 81,\n        columnNumber: 13\n      }, this), /*#__PURE__*/_jsxDEV(ListItem, {\n        button: true,\n        selected: isActive('/dashboard/cantieri'),\n        onClick: () => navigateTo('/dashboard/cantieri'),\n        children: [/*#__PURE__*/_jsxDEV(ListItemIcon, {\n          children: /*#__PURE__*/_jsxDEV(ConstructionIcon, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 96,\n            columnNumber: 15\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 95,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(ListItemText, {\n          primary: isImpersonating && impersonatedUser ? `Cantieri di ${impersonatedUser.username}` : \"I Miei Cantieri\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 98,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 90,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(ListItem, {\n        button: true,\n        selected: isActive('/dashboard/cavi'),\n        onClick: () => navigateTo('/dashboard/cavi'),\n        children: [/*#__PURE__*/_jsxDEV(ListItemIcon, {\n          children: /*#__PURE__*/_jsxDEV(CableIcon, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 108,\n            columnNumber: 15\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 107,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(ListItemText, {\n          primary: \"Gestione Cavi\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 110,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 102,\n        columnNumber: 11\n      }, this)]\n    }, void 0, true)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 45,\n    columnNumber: 5\n  }, this);\n};\n_s(MainMenu, \"6Ijcm32kq2aMtCZRRapJDvpMW3U=\", false, function () {\n  return [useNavigate, useLocation, useAuth];\n});\n_c = MainMenu;\nexport default MainMenu;\nvar _c;\n$RefreshReg$(_c, \"MainMenu\");", "map": {"version": 3, "names": ["React", "useNavigate", "useLocation", "List", "ListItem", "ListItemIcon", "ListItemText", "Divider", "Box", "Typography", "Home", "HomeIcon", "AdminPanelSettings", "AdminIcon", "Construction", "ConstructionIcon", "Cable", "CableIcon", "Description", "ReportIcon", "useAuth", "jsxDEV", "_jsxDEV", "Fragment", "_Fragment", "MainMenu", "_s", "navigate", "location", "user", "isImpersonating", "impersonated<PERSON><PERSON>", "isActive", "path", "pathname", "navigateTo", "console", "log", "children", "button", "selected", "onClick", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "primary", "role", "sx", "p", "bgcolor", "borderLeft", "variant", "color", "fontWeight", "username", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/PycharmProjects/CMS/webapp/frontend/src/components/MainMenu.js"], "sourcesContent": ["import React from 'react';\nimport { useNavigate, useLocation } from 'react-router-dom';\nimport {\n  List,\n  ListItem,\n  ListItemIcon,\n  ListItemText,\n  Divider,\n  Box,\n  Typography\n} from '@mui/material';\nimport {\n  Home as HomeIcon,\n  AdminPanelSettings as AdminIcon,\n  Construction as ConstructionIcon,\n  Cable as CableIcon,\n  Description as ReportIcon\n} from '@mui/icons-material';\nimport { useAuth } from '../context/AuthContext';\n\nconst MainMenu = () => {\n  const navigate = useNavigate();\n  const location = useLocation();\n  const { user, isImpersonating, impersonatedUser } = useAuth();\n\n  // Verifica se un percorso è attivo\n  const isActive = (path) => {\n    return location.pathname === path;\n  };\n\n  // Naviga a un percorso\n  const navigateTo = (path) => {\n    console.log('Navigazione a:', path, 'isImpersonating:', isImpersonating, 'user:', user);\n    // Se l'utente è un amministratore che sta impersonando un utente e sta cliccando su Home,\n    // reindirizza al menu amministratore invece che alla home generica\n    if (path === '/dashboard' && isImpersonating) {\n      console.log('Utente impersonato, reindirizzamento al menu amministratore');\n      navigate('/dashboard/admin');\n    } else {\n      navigate(path);\n    }\n  };\n\n  return (\n    <List>\n      {/* Home - Se l'utente è un amministratore che sta impersonando un utente, mostra \"Torna al Menu Admin\" */}\n      <ListItem\n        button\n        selected={isImpersonating ? isActive('/dashboard/admin') : isActive('/dashboard')}\n        onClick={() => navigateTo('/dashboard')}\n      >\n        <ListItemIcon>\n          <HomeIcon />\n        </ListItemIcon>\n        <ListItemText primary={isImpersonating ? \"Torna al Menu Admin\" : \"Home\"} />\n      </ListItem>\n\n      {/* Menu Amministratore (solo per admin) */}\n      {user?.role === 'owner' && (\n        <>\n          <Divider />\n          <ListItem\n            button\n            selected={isActive('/dashboard/admin')}\n            onClick={() => navigateTo('/dashboard/admin')}\n          >\n            <ListItemIcon>\n              <AdminIcon />\n            </ListItemIcon>\n            <ListItemText primary=\"Amministrazione\" />\n          </ListItem>\n        </>\n      )}\n\n      {/* Menu per utenti standard e cantieri */}\n      {/* Mostra per utenti standard/cantiere o per admin che sta impersonando un utente */}\n      {(user?.role !== 'owner' || (user?.role === 'owner' && isImpersonating && impersonatedUser)) && (\n        <>\n          <Divider />\n          {isImpersonating && impersonatedUser && (\n            <Box sx={{ p: 2, bgcolor: 'rgba(255, 165, 0, 0.1)', borderLeft: '4px solid orange' }}>\n              <Typography variant=\"subtitle2\" color=\"textSecondary\">\n                Accesso come utente:\n              </Typography>\n              <Typography variant=\"body2\" fontWeight=\"bold\">\n                {impersonatedUser.username}\n              </Typography>\n            </Box>\n          )}\n          <ListItem\n            button\n            selected={isActive('/dashboard/cantieri')}\n            onClick={() => navigateTo('/dashboard/cantieri')}\n          >\n            <ListItemIcon>\n              <ConstructionIcon />\n            </ListItemIcon>\n            <ListItemText primary={isImpersonating && impersonatedUser ? `Cantieri di ${impersonatedUser.username}` : \"I Miei Cantieri\"} />\n          </ListItem>\n\n          {/* Menu Cavi */}\n          <ListItem\n            button\n            selected={isActive('/dashboard/cavi')}\n            onClick={() => navigateTo('/dashboard/cavi')}\n          >\n            <ListItemIcon>\n              <CableIcon />\n            </ListItemIcon>\n            <ListItemText primary=\"Gestione Cavi\" />\n          </ListItem>\n\n          {/* Rimosso il menu Report perché appartiene al menu gestione cavi */}\n        </>\n      )}\n    </List>\n  );\n};\n\nexport default MainMenu;\n"], "mappings": ";;AAAA,OAAOA,KAAK,MAAM,OAAO;AACzB,SAASC,WAAW,EAAEC,WAAW,QAAQ,kBAAkB;AAC3D,SACEC,IAAI,EACJC,QAAQ,EACRC,YAAY,EACZC,YAAY,EACZC,OAAO,EACPC,GAAG,EACHC,UAAU,QACL,eAAe;AACtB,SACEC,IAAI,IAAIC,QAAQ,EAChBC,kBAAkB,IAAIC,SAAS,EAC/BC,YAAY,IAAIC,gBAAgB,EAChCC,KAAK,IAAIC,SAAS,EAClBC,WAAW,IAAIC,UAAU,QACpB,qBAAqB;AAC5B,SAASC,OAAO,QAAQ,wBAAwB;AAAC,SAAAC,MAAA,IAAAC,OAAA,EAAAC,QAAA,IAAAC,SAAA;AAEjD,MAAMC,QAAQ,GAAGA,CAAA,KAAM;EAAAC,EAAA;EACrB,MAAMC,QAAQ,GAAG1B,WAAW,CAAC,CAAC;EAC9B,MAAM2B,QAAQ,GAAG1B,WAAW,CAAC,CAAC;EAC9B,MAAM;IAAE2B,IAAI;IAAEC,eAAe;IAAEC;EAAiB,CAAC,GAAGX,OAAO,CAAC,CAAC;;EAE7D;EACA,MAAMY,QAAQ,GAAIC,IAAI,IAAK;IACzB,OAAOL,QAAQ,CAACM,QAAQ,KAAKD,IAAI;EACnC,CAAC;;EAED;EACA,MAAME,UAAU,GAAIF,IAAI,IAAK;IAC3BG,OAAO,CAACC,GAAG,CAAC,gBAAgB,EAAEJ,IAAI,EAAE,kBAAkB,EAAEH,eAAe,EAAE,OAAO,EAAED,IAAI,CAAC;IACvF;IACA;IACA,IAAII,IAAI,KAAK,YAAY,IAAIH,eAAe,EAAE;MAC5CM,OAAO,CAACC,GAAG,CAAC,6DAA6D,CAAC;MAC1EV,QAAQ,CAAC,kBAAkB,CAAC;IAC9B,CAAC,MAAM;MACLA,QAAQ,CAACM,IAAI,CAAC;IAChB;EACF,CAAC;EAED,oBACEX,OAAA,CAACnB,IAAI;IAAAmC,QAAA,gBAEHhB,OAAA,CAAClB,QAAQ;MACPmC,MAAM;MACNC,QAAQ,EAAEV,eAAe,GAAGE,QAAQ,CAAC,kBAAkB,CAAC,GAAGA,QAAQ,CAAC,YAAY,CAAE;MAClFS,OAAO,EAAEA,CAAA,KAAMN,UAAU,CAAC,YAAY,CAAE;MAAAG,QAAA,gBAExChB,OAAA,CAACjB,YAAY;QAAAiC,QAAA,eACXhB,OAAA,CAACX,QAAQ;UAAA+B,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACA,CAAC,eACfvB,OAAA,CAAChB,YAAY;QAACwC,OAAO,EAAEhB,eAAe,GAAG,qBAAqB,GAAG;MAAO;QAAAY,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACnE,CAAC,EAGV,CAAAhB,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAEkB,IAAI,MAAK,OAAO,iBACrBzB,OAAA,CAAAE,SAAA;MAAAc,QAAA,gBACEhB,OAAA,CAACf,OAAO;QAAAmC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC,eACXvB,OAAA,CAAClB,QAAQ;QACPmC,MAAM;QACNC,QAAQ,EAAER,QAAQ,CAAC,kBAAkB,CAAE;QACvCS,OAAO,EAAEA,CAAA,KAAMN,UAAU,CAAC,kBAAkB,CAAE;QAAAG,QAAA,gBAE9ChB,OAAA,CAACjB,YAAY;UAAAiC,QAAA,eACXhB,OAAA,CAACT,SAAS;YAAA6B,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACD,CAAC,eACfvB,OAAA,CAAChB,YAAY;UAACwC,OAAO,EAAC;QAAiB;UAAAJ,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAClC,CAAC;IAAA,eACX,CACH,EAIA,CAAC,CAAAhB,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAEkB,IAAI,MAAK,OAAO,IAAK,CAAAlB,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAEkB,IAAI,MAAK,OAAO,IAAIjB,eAAe,IAAIC,gBAAiB,kBACzFT,OAAA,CAAAE,SAAA;MAAAc,QAAA,gBACEhB,OAAA,CAACf,OAAO;QAAAmC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC,EACVf,eAAe,IAAIC,gBAAgB,iBAClCT,OAAA,CAACd,GAAG;QAACwC,EAAE,EAAE;UAAEC,CAAC,EAAE,CAAC;UAAEC,OAAO,EAAE,wBAAwB;UAAEC,UAAU,EAAE;QAAmB,CAAE;QAAAb,QAAA,gBACnFhB,OAAA,CAACb,UAAU;UAAC2C,OAAO,EAAC,WAAW;UAACC,KAAK,EAAC,eAAe;UAAAf,QAAA,EAAC;QAEtD;UAAAI,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAY,CAAC,eACbvB,OAAA,CAACb,UAAU;UAAC2C,OAAO,EAAC,OAAO;UAACE,UAAU,EAAC,MAAM;UAAAhB,QAAA,EAC1CP,gBAAgB,CAACwB;QAAQ;UAAAb,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAChB,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACV,CACN,eACDvB,OAAA,CAAClB,QAAQ;QACPmC,MAAM;QACNC,QAAQ,EAAER,QAAQ,CAAC,qBAAqB,CAAE;QAC1CS,OAAO,EAAEA,CAAA,KAAMN,UAAU,CAAC,qBAAqB,CAAE;QAAAG,QAAA,gBAEjDhB,OAAA,CAACjB,YAAY;UAAAiC,QAAA,eACXhB,OAAA,CAACP,gBAAgB;YAAA2B,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACR,CAAC,eACfvB,OAAA,CAAChB,YAAY;UAACwC,OAAO,EAAEhB,eAAe,IAAIC,gBAAgB,GAAG,eAAeA,gBAAgB,CAACwB,QAAQ,EAAE,GAAG;QAAkB;UAAAb,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACvH,CAAC,eAGXvB,OAAA,CAAClB,QAAQ;QACPmC,MAAM;QACNC,QAAQ,EAAER,QAAQ,CAAC,iBAAiB,CAAE;QACtCS,OAAO,EAAEA,CAAA,KAAMN,UAAU,CAAC,iBAAiB,CAAE;QAAAG,QAAA,gBAE7ChB,OAAA,CAACjB,YAAY;UAAAiC,QAAA,eACXhB,OAAA,CAACL,SAAS;YAAAyB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACD,CAAC,eACfvB,OAAA,CAAChB,YAAY;UAACwC,OAAO,EAAC;QAAe;UAAAJ,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAChC,CAAC;IAAA,eAGX,CACH;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACG,CAAC;AAEX,CAAC;AAACnB,EAAA,CAjGID,QAAQ;EAAA,QACKxB,WAAW,EACXC,WAAW,EACwBkB,OAAO;AAAA;AAAAoC,EAAA,GAHvD/B,QAAQ;AAmGd,eAAeA,QAAQ;AAAC,IAAA+B,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}