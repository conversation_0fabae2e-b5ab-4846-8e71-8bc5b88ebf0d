{"ast": null, "code": "export function getCursorRectangle(layout, activeCoordinate, offset, tooltipAxisBandSize) {\n  var halfSize = tooltipAxisBandSize / 2;\n  return {\n    stroke: 'none',\n    fill: '#ccc',\n    x: layout === 'horizontal' ? activeCoordinate.x - halfSize : offset.left + 0.5,\n    y: layout === 'horizontal' ? offset.top + 0.5 : activeCoordinate.y - halfSize,\n    width: layout === 'horizontal' ? tooltipAxisBandSize : offset.width - 1,\n    height: layout === 'horizontal' ? offset.height - 1 : tooltipAxisBandSize\n  };\n}", "map": {"version": 3, "names": ["getCursorRectangle", "layout", "activeCoordinate", "offset", "tooltipAxisBandSize", "halfSize", "stroke", "fill", "x", "left", "y", "top", "width", "height"], "sources": ["C:/Users/<USER>/PycharmProjects/CMS/webapp/frontend/node_modules/recharts/es6/util/cursor/getCursorRectangle.js"], "sourcesContent": ["export function getCursorRectangle(layout, activeCoordinate, offset, tooltipAxisBandSize) {\n  var halfSize = tooltipAxisBandSize / 2;\n  return {\n    stroke: 'none',\n    fill: '#ccc',\n    x: layout === 'horizontal' ? activeCoordinate.x - halfSize : offset.left + 0.5,\n    y: layout === 'horizontal' ? offset.top + 0.5 : activeCoordinate.y - halfSize,\n    width: layout === 'horizontal' ? tooltipAxisBandSize : offset.width - 1,\n    height: layout === 'horizontal' ? offset.height - 1 : tooltipAxisBandSize\n  };\n}"], "mappings": "AAAA,OAAO,SAASA,kBAAkBA,CAACC,MAAM,EAAEC,gBAAgB,EAAEC,MAAM,EAAEC,mBAAmB,EAAE;EACxF,IAAIC,QAAQ,GAAGD,mBAAmB,GAAG,CAAC;EACtC,OAAO;IACLE,MAAM,EAAE,MAAM;IACdC,IAAI,EAAE,MAAM;IACZC,CAAC,EAAEP,MAAM,KAAK,YAAY,GAAGC,gBAAgB,CAACM,CAAC,GAAGH,QAAQ,GAAGF,MAAM,CAACM,IAAI,GAAG,GAAG;IAC9EC,CAAC,EAAET,MAAM,KAAK,YAAY,GAAGE,MAAM,CAACQ,GAAG,GAAG,GAAG,GAAGT,gBAAgB,CAACQ,CAAC,GAAGL,QAAQ;IAC7EO,KAAK,EAAEX,MAAM,KAAK,YAAY,GAAGG,mBAAmB,GAAGD,MAAM,CAACS,KAAK,GAAG,CAAC;IACvEC,MAAM,EAAEZ,MAAM,KAAK,YAAY,GAAGE,MAAM,CAACU,MAAM,GAAG,CAAC,GAAGT;EACxD,CAAC;AACH", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}