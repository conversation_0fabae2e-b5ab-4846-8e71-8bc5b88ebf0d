{"ast": null, "code": "\"use client\";\n\nimport createSvgIcon from './utils/createSvgIcon';\nimport { jsx as _jsx } from \"react/jsx-runtime\";\nexport default createSvgIcon(/*#__PURE__*/_jsx(\"path\", {\n  d: \"M22 14c0-1.95-1.4-3.57-3.25-3.92L17.4 6.05C17 4.82 15.85 4 14.56 4H9.44C8.15 4 7 4.82 6.6 6.05L5.81 8.4 4.41 7l.29-.29c.39-.39.39-1.02 0-1.41a.9959.9959 0 0 0-1.41 0l-2 2c-.39.39-.39 1.02 0 1.41.39.39 1.02.39 1.41 0l.3-.3 1.79 1.79C3.18 10.72 2 12.22 2 14c0 1.5.83 2.79 2.05 3.48C4.28 18.9 5.51 20 7 20c1.3 0 2.4-.84 2.82-2h4.37c.41 1.16 1.51 2 2.82 2 1.49 0 2.72-1.1 2.95-2.52C21.17 16.79 22 15.5 22 14M7 18c-.55 0-1-.45-1-1s.45-1 1-1 1 .45 1 1-.45 1-1 1m4-8H7.41l-.02-.02 1.1-3.3c.14-.41.52-.68.95-.68H11zm2-4h1.56c.43 0 .81.27.95.68l1.1 3.32H13zm4 12c-.55 0-1-.45-1-1s.45-1 1-1 1 .45 1 1-.45 1-1 1\"\n}), 'Toys');", "map": {"version": 3, "names": ["createSvgIcon", "jsx", "_jsx", "d"], "sources": ["C:/Users/<USER>/PycharmProjects/CMS/webapp/frontend/node_modules/@mui/icons-material/esm/Toys.js"], "sourcesContent": ["\"use client\";\n\nimport createSvgIcon from './utils/createSvgIcon';\nimport { jsx as _jsx } from \"react/jsx-runtime\";\nexport default createSvgIcon( /*#__PURE__*/_jsx(\"path\", {\n  d: \"M22 14c0-1.95-1.4-3.57-3.25-3.92L17.4 6.05C17 4.82 15.85 4 14.56 4H9.44C8.15 4 7 4.82 6.6 6.05L5.81 8.4 4.41 7l.29-.29c.39-.39.39-1.02 0-1.41a.9959.9959 0 0 0-1.41 0l-2 2c-.39.39-.39 1.02 0 1.41.39.39 1.02.39 1.41 0l.3-.3 1.79 1.79C3.18 10.72 2 12.22 2 14c0 1.5.83 2.79 2.05 3.48C4.28 18.9 5.51 20 7 20c1.3 0 2.4-.84 2.82-2h4.37c.41 1.16 1.51 2 2.82 2 1.49 0 2.72-1.1 2.95-2.52C21.17 16.79 22 15.5 22 14M7 18c-.55 0-1-.45-1-1s.45-1 1-1 1 .45 1 1-.45 1-1 1m4-8H7.41l-.02-.02 1.1-3.3c.14-.41.52-.68.95-.68H11zm2-4h1.56c.43 0 .81.27.95.68l1.1 3.32H13zm4 12c-.55 0-1-.45-1-1s.45-1 1-1 1 .45 1 1-.45 1-1 1\"\n}), 'Toys');"], "mappings": "AAAA,YAAY;;AAEZ,OAAOA,aAAa,MAAM,uBAAuB;AACjD,SAASC,GAAG,IAAIC,IAAI,QAAQ,mBAAmB;AAC/C,eAAeF,aAAa,CAAE,aAAaE,IAAI,CAAC,MAAM,EAAE;EACtDC,CAAC,EAAE;AACL,CAAC,CAAC,EAAE,MAAM,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}