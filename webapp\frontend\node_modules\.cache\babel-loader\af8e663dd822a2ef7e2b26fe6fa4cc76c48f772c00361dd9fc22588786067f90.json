{"ast": null, "code": "var _jsxFileName = \"C:\\\\CMS\\\\webapp\\\\frontend\\\\src\\\\components\\\\comande\\\\CreaComandaConCavi.js\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect } from 'react';\nimport { Box, Card, CardContent, Typography, Button, Dialog, DialogTitle, DialogContent, DialogActions, TextField, MenuItem, Alert, CircularProgress, Stepper, Step, StepLabel, Table, TableBody, TableCell, TableContainer, TableHead, TableRow, Paper, Checkbox, Chip, Grid, Autocomplete } from '@mui/material';\nimport { Add as AddIcon, Cable as CableIcon, Assignment as AssignmentIcon } from '@mui/icons-material';\nimport comandeService from '../../services/comandeService';\nimport responsabiliService from '../../services/responsabiliService';\nimport { jsxDEV as _jsxDEV, Fragment as _Fragment } from \"react/jsx-dev-runtime\";\nconst CreaComandaConCavi = ({\n  cantiereId,\n  open,\n  onClose,\n  onSuccess,\n  tipoComandaPreselezionato = null,\n  caviPreselezionati = []\n}) => {\n  _s();\n  const [activeStep, setActiveStep] = useState(0);\n  const [loading, setLoading] = useState(false);\n  const [error, setError] = useState(null);\n\n  // Step 1: Selezione tipo comanda\n  const [tipoComanda, setTipoComanda] = useState(tipoComandaPreselezionato || 'POSA');\n\n  // Step 2: Selezione cavi\n  const [caviDisponibili, setCaviDisponibili] = useState([]);\n  const [caviSelezionati, setCaviSelezionati] = useState(caviPreselezionati || []);\n  const [loadingCavi, setLoadingCavi] = useState(false);\n\n  // Step 3: Dettagli comanda\n  const [formData, setFormData] = useState({\n    descrizione: '',\n    responsabile: '',\n    responsabile_email: '',\n    responsabile_telefono: '',\n    data_scadenza: '',\n    priorita: 'NORMALE',\n    note_capo_cantiere: ''\n  });\n\n  // Gestione responsabili\n  const [responsabiliDisponibili, setResponsabiliDisponibili] = useState([]);\n  const [loadingResponsabili, setLoadingResponsabili] = useState(false);\n  const steps = ['Tipo Comanda', 'Selezione Cavi', 'Dettagli Comanda'];\n\n  // Carica cavi disponibili quando cambia il tipo comanda\n  useEffect(() => {\n    if (open && tipoComanda) {\n      loadCaviDisponibili();\n    }\n  }, [open, tipoComanda]);\n\n  // Carica responsabili disponibili quando si apre il dialog\n  useEffect(() => {\n    if (open && cantiereId) {\n      loadResponsabiliDisponibili();\n    }\n  }, [open, cantiereId]);\n\n  // Se ci sono cavi preselezionati, salta al passo dei dettagli\n  useEffect(() => {\n    if (open && caviPreselezionati.length > 0 && tipoComandaPreselezionato) {\n      setActiveStep(2); // Vai direttamente ai dettagli\n    }\n  }, [open, caviPreselezionati, tipoComandaPreselezionato]);\n  const loadCaviDisponibili = async () => {\n    try {\n      setLoadingCavi(true);\n      const response = await comandeService.getCaviDisponibili(cantiereId, tipoComanda);\n      setCaviDisponibili(response.cavi_disponibili || []);\n      setCaviSelezionati([]); // Reset selezione\n      setError(null);\n    } catch (err) {\n      console.error('Errore nel caricamento dei cavi:', err);\n      setError('Errore nel caricamento dei cavi disponibili');\n    } finally {\n      setLoadingCavi(false);\n    }\n  };\n  const loadResponsabiliDisponibili = async () => {\n    try {\n      setLoadingResponsabili(true);\n      const responsabili = await responsabiliService.getResponsabiliCantiere(cantiereId);\n      setResponsabiliDisponibili(responsabili || []);\n    } catch (err) {\n      console.error('Errore nel caricamento dei responsabili:', err);\n      // Non mostrare errore per i responsabili, è opzionale\n    } finally {\n      setLoadingResponsabili(false);\n    }\n  };\n  const handleNext = () => {\n    if (activeStep === 1 && caviSelezionati.length === 0) {\n      setError('Seleziona almeno un cavo');\n      return;\n    }\n    if (activeStep === 2 && !formData.responsabile.trim()) {\n      setError('Il responsabile è obbligatorio');\n      return;\n    }\n    setError(null);\n    setActiveStep(prevStep => prevStep + 1);\n  };\n  const handleBack = () => {\n    setActiveStep(prevStep => prevStep - 1);\n  };\n  const handleCavoToggle = cavo => {\n    const isSelected = caviSelezionati.some(c => c.id_cavo === cavo.id_cavo);\n    if (isSelected) {\n      setCaviSelezionati(caviSelezionati.filter(c => c.id_cavo !== cavo.id_cavo));\n    } else {\n      setCaviSelezionati([...caviSelezionati, cavo]);\n    }\n  };\n  const handleSubmit = async () => {\n    try {\n      setLoading(true);\n      const comandaData = {\n        tipo_comanda: tipoComanda,\n        descrizione: formData.descrizione,\n        responsabile: formData.responsabile,\n        data_scadenza: formData.data_scadenza || null,\n        priorita: formData.priorita,\n        note_capo_cantiere: formData.note_capo_cantiere\n      };\n      const listaIdCavi = caviSelezionati.map(c => c.id_cavo);\n      const response = await comandeService.createComandaConCavi(cantiereId, comandaData, listaIdCavi);\n      onSuccess && onSuccess(response);\n      handleClose();\n    } catch (err) {\n      console.error('Errore nella creazione:', err);\n      setError('Errore nella creazione della comanda');\n    } finally {\n      setLoading(false);\n    }\n  };\n  const handleClose = () => {\n    setActiveStep(0);\n    setTipoComanda(tipoComandaPreselezionato || 'POSA');\n    setCaviDisponibili([]);\n    setCaviSelezionati(caviPreselezionati || []);\n    setFormData({\n      descrizione: '',\n      responsabile: '',\n      responsabile_email: '',\n      responsabile_telefono: '',\n      data_scadenza: '',\n      priorita: 'NORMALE',\n      note_capo_cantiere: ''\n    });\n    setError(null);\n    onClose && onClose();\n  };\n  const getTipoComandaLabel = tipo => {\n    switch (tipo) {\n      case 'POSA':\n        return 'Posa';\n      case 'COLLEGAMENTO_PARTENZA':\n        return 'Collegamento Partenza';\n      case 'COLLEGAMENTO_ARRIVO':\n        return 'Collegamento Arrivo';\n      case 'CERTIFICAZIONE':\n        return 'Certificazione';\n      case 'TESTING':\n        return 'Testing';\n      default:\n        return tipo;\n    }\n  };\n  const renderStepContent = () => {\n    switch (activeStep) {\n      case 0:\n        return /*#__PURE__*/_jsxDEV(Box, {\n          children: [/*#__PURE__*/_jsxDEV(Typography, {\n            variant: \"h6\",\n            gutterBottom: true,\n            children: \"Seleziona il tipo di comanda\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 216,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(TextField, {\n            fullWidth: true,\n            select: true,\n            label: \"Tipo Comanda\",\n            value: tipoComanda,\n            onChange: e => setTipoComanda(e.target.value),\n            margin: \"normal\",\n            children: [/*#__PURE__*/_jsxDEV(MenuItem, {\n              value: \"POSA\",\n              children: \"Posa\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 227,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(MenuItem, {\n              value: \"COLLEGAMENTO_PARTENZA\",\n              children: \"Collegamento Partenza\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 228,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(MenuItem, {\n              value: \"COLLEGAMENTO_ARRIVO\",\n              children: \"Collegamento Arrivo\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 229,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(MenuItem, {\n              value: \"CERTIFICAZIONE\",\n              children: \"Certificazione\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 230,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(MenuItem, {\n              value: \"TESTING\",\n              children: \"Testing\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 231,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 219,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Alert, {\n            severity: \"info\",\n            sx: {\n              mt: 2\n            },\n            children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n              children: getTipoComandaLabel(tipoComanda)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 234,\n              columnNumber: 15\n            }, this), \":\", tipoComanda === 'POSA' && ' Comanda per la posa fisica dei cavi', tipoComanda === 'COLLEGAMENTO_PARTENZA' && ' Comanda per il collegamento lato partenza', tipoComanda === 'COLLEGAMENTO_ARRIVO' && ' Comanda per il collegamento lato arrivo', tipoComanda === 'CERTIFICAZIONE' && ' Comanda per la certificazione dei cavi', tipoComanda === 'TESTING' && ' Comanda per test e certificazione']\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 233,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 215,\n          columnNumber: 11\n        }, this);\n      case 1:\n        return /*#__PURE__*/_jsxDEV(Box, {\n          children: [/*#__PURE__*/_jsxDEV(Typography, {\n            variant: \"h6\",\n            gutterBottom: true,\n            children: [\"Seleziona i cavi per la comanda di \", getTipoComandaLabel(tipoComanda)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 247,\n            columnNumber: 13\n          }, this), loadingCavi ? /*#__PURE__*/_jsxDEV(Box, {\n            display: \"flex\",\n            justifyContent: \"center\",\n            p: 3,\n            children: /*#__PURE__*/_jsxDEV(CircularProgress, {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 253,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 252,\n            columnNumber: 15\n          }, this) : /*#__PURE__*/_jsxDEV(_Fragment, {\n            children: [/*#__PURE__*/_jsxDEV(Box, {\n              mb: 2,\n              children: /*#__PURE__*/_jsxDEV(Chip, {\n                icon: /*#__PURE__*/_jsxDEV(CableIcon, {}, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 259,\n                  columnNumber: 27\n                }, this),\n                label: `${caviSelezionati.length} cavi selezionati di ${caviDisponibili.length} disponibili`,\n                color: caviSelezionati.length > 0 ? 'primary' : 'default'\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 258,\n                columnNumber: 19\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 257,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(TableContainer, {\n              component: Paper,\n              sx: {\n                maxHeight: 400\n              },\n              children: /*#__PURE__*/_jsxDEV(Table, {\n                stickyHeader: true,\n                children: [/*#__PURE__*/_jsxDEV(TableHead, {\n                  children: /*#__PURE__*/_jsxDEV(TableRow, {\n                    children: [/*#__PURE__*/_jsxDEV(TableCell, {\n                      padding: \"checkbox\",\n                      children: \"Sel.\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 269,\n                      columnNumber: 25\n                    }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n                      children: \"ID Cavo\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 270,\n                      columnNumber: 25\n                    }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n                      children: \"Tipologia\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 271,\n                      columnNumber: 25\n                    }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n                      children: \"Sezione\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 272,\n                      columnNumber: 25\n                    }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n                      children: \"Metri\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 273,\n                      columnNumber: 25\n                    }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n                      children: \"Partenza\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 274,\n                      columnNumber: 25\n                    }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n                      children: \"Arrivo\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 275,\n                      columnNumber: 25\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 268,\n                    columnNumber: 23\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 267,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(TableBody, {\n                  children: caviDisponibili.map(cavo => {\n                    const isSelected = caviSelezionati.some(c => c.id_cavo === cavo.id_cavo);\n                    return /*#__PURE__*/_jsxDEV(TableRow, {\n                      hover: true,\n                      onClick: () => handleCavoToggle(cavo),\n                      sx: {\n                        cursor: 'pointer'\n                      },\n                      children: [/*#__PURE__*/_jsxDEV(TableCell, {\n                        padding: \"checkbox\",\n                        children: /*#__PURE__*/_jsxDEV(Checkbox, {\n                          checked: isSelected\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 289,\n                          columnNumber: 31\n                        }, this)\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 288,\n                        columnNumber: 29\n                      }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n                        children: cavo.id_cavo\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 291,\n                        columnNumber: 29\n                      }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n                        children: cavo.tipologia\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 292,\n                        columnNumber: 29\n                      }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n                        children: cavo.sezione\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 293,\n                        columnNumber: 29\n                      }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n                        children: cavo.metri_teorici\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 294,\n                        columnNumber: 29\n                      }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n                        children: cavo.ubicazione_partenza\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 295,\n                        columnNumber: 29\n                      }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n                        children: cavo.ubicazione_arrivo\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 296,\n                        columnNumber: 29\n                      }, this)]\n                    }, cavo.id_cavo, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 282,\n                      columnNumber: 27\n                    }, this);\n                  })\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 278,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 266,\n                columnNumber: 19\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 265,\n              columnNumber: 17\n            }, this), caviDisponibili.length === 0 && /*#__PURE__*/_jsxDEV(Alert, {\n              severity: \"warning\",\n              sx: {\n                mt: 2\n              },\n              children: \"Nessun cavo disponibile per il tipo di comanda selezionato.\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 305,\n              columnNumber: 19\n            }, this)]\n          }, void 0, true)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 246,\n          columnNumber: 11\n        }, this);\n      case 2:\n        return /*#__PURE__*/_jsxDEV(Box, {\n          children: [/*#__PURE__*/_jsxDEV(Typography, {\n            variant: \"h6\",\n            gutterBottom: true,\n            children: \"Dettagli della comanda\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 317,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Grid, {\n            container: true,\n            spacing: 2,\n            children: [/*#__PURE__*/_jsxDEV(Grid, {\n              item: true,\n              xs: 12,\n              children: /*#__PURE__*/_jsxDEV(Autocomplete, {\n                fullWidth: true,\n                freeSolo: true,\n                options: responsabiliDisponibili.map(r => r.nome_responsabile),\n                value: formData.responsabile,\n                onChange: (event, newValue) => {\n                  const responsabile = responsabiliDisponibili.find(r => r.nome_responsabile === newValue);\n                  if (responsabile) {\n                    setFormData({\n                      ...formData,\n                      responsabile: newValue || '',\n                      responsabile_email: responsabile.email || '',\n                      responsabile_telefono: responsabile.telefono || ''\n                    });\n                  } else {\n                    setFormData({\n                      ...formData,\n                      responsabile: newValue || ''\n                    });\n                  }\n                },\n                onInputChange: (event, newInputValue) => {\n                  setFormData({\n                    ...formData,\n                    responsabile: newInputValue\n                  });\n                },\n                renderInput: params => /*#__PURE__*/_jsxDEV(TextField, {\n                  ...params,\n                  label: \"Nome Responsabile\",\n                  margin: \"normal\",\n                  required: true,\n                  helperText: \"Chi eseguir\\xE0 il lavoro (obbligatorio)\",\n                  InputProps: {\n                    ...params.InputProps,\n                    endAdornment: /*#__PURE__*/_jsxDEV(_Fragment, {\n                      children: [loadingResponsabili ? /*#__PURE__*/_jsxDEV(CircularProgress, {\n                        color: \"inherit\",\n                        size: 20\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 355,\n                        columnNumber: 52\n                      }, this) : null, params.InputProps.endAdornment]\n                    }, void 0, true)\n                  }\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 345,\n                  columnNumber: 21\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 323,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 322,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Grid, {\n              item: true,\n              xs: 12,\n              sm: 6,\n              children: /*#__PURE__*/_jsxDEV(TextField, {\n                fullWidth: true,\n                label: \"Email Responsabile\",\n                type: \"email\",\n                value: formData.responsabile_email,\n                onChange: e => setFormData({\n                  ...formData,\n                  responsabile_email: e.target.value\n                }),\n                margin: \"normal\",\n                helperText: \"Email per notifiche (opzionale)\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 366,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 365,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Grid, {\n              item: true,\n              xs: 12,\n              sm: 6,\n              children: /*#__PURE__*/_jsxDEV(TextField, {\n                fullWidth: true,\n                label: \"Telefono Responsabile\",\n                value: formData.responsabile_telefono,\n                onChange: e => setFormData({\n                  ...formData,\n                  responsabile_telefono: e.target.value\n                }),\n                margin: \"normal\",\n                helperText: \"Numero per SMS (opzionale)\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 378,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 377,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Grid, {\n              item: true,\n              xs: 12,\n              sm: 6,\n              children: /*#__PURE__*/_jsxDEV(TextField, {\n                fullWidth: true,\n                select: true,\n                label: \"Priorit\\xE0\",\n                value: formData.priorita,\n                onChange: e => setFormData({\n                  ...formData,\n                  priorita: e.target.value\n                }),\n                margin: \"normal\",\n                children: [/*#__PURE__*/_jsxDEV(MenuItem, {\n                  value: \"BASSA\",\n                  children: \"Bassa\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 397,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(MenuItem, {\n                  value: \"NORMALE\",\n                  children: \"Normale\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 398,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(MenuItem, {\n                  value: \"ALTA\",\n                  children: \"Alta\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 399,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(MenuItem, {\n                  value: \"URGENTE\",\n                  children: \"Urgente\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 400,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 389,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 388,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Grid, {\n              item: true,\n              xs: 12,\n              sm: 6,\n              children: /*#__PURE__*/_jsxDEV(TextField, {\n                fullWidth: true,\n                label: \"Data Scadenza\",\n                type: \"date\",\n                value: formData.data_scadenza,\n                onChange: e => setFormData({\n                  ...formData,\n                  data_scadenza: e.target.value\n                }),\n                margin: \"normal\",\n                InputLabelProps: {\n                  shrink: true\n                }\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 405,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 404,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 321,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(TextField, {\n            fullWidth: true,\n            label: \"Descrizione\",\n            value: formData.descrizione,\n            onChange: e => setFormData({\n              ...formData,\n              descrizione: e.target.value\n            }),\n            margin: \"normal\",\n            multiline: true,\n            rows: 3\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 417,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(TextField, {\n            fullWidth: true,\n            label: \"Note Capo Cantiere\",\n            value: formData.note_capo_cantiere,\n            onChange: e => setFormData({\n              ...formData,\n              note_capo_cantiere: e.target.value\n            }),\n            margin: \"normal\",\n            multiline: true,\n            rows: 2,\n            helperText: \"Istruzioni specifiche per il responsabile\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 427,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(TextField, {\n            fullWidth: true,\n            label: \"Data Scadenza\",\n            type: \"date\",\n            value: formData.data_scadenza,\n            onChange: e => setFormData({\n              ...formData,\n              data_scadenza: e.target.value\n            }),\n            margin: \"normal\",\n            InputLabelProps: {\n              shrink: true\n            }\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 438,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Alert, {\n            severity: \"info\",\n            sx: {\n              mt: 2\n            },\n            children: [/*#__PURE__*/_jsxDEV(Typography, {\n              variant: \"subtitle2\",\n              children: \"Riepilogo:\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 449,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Typography, {\n              variant: \"body2\",\n              children: [\"\\u2022 Tipo: \", getTipoComandaLabel(tipoComanda), /*#__PURE__*/_jsxDEV(\"br\", {}, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 451,\n                columnNumber: 59\n              }, this), \"\\u2022 Cavi selezionati: \", caviSelezionati.length, /*#__PURE__*/_jsxDEV(\"br\", {}, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 452,\n                columnNumber: 61\n              }, this), \"\\u2022 Responsabile: \", formData.responsabile || 'Non specificato']\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 450,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 448,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 316,\n          columnNumber: 11\n        }, this);\n      default:\n        return null;\n    }\n  };\n  return /*#__PURE__*/_jsxDEV(Dialog, {\n    open: open,\n    onClose: handleClose,\n    maxWidth: \"lg\",\n    fullWidth: true,\n    children: [/*#__PURE__*/_jsxDEV(DialogTitle, {\n      children: /*#__PURE__*/_jsxDEV(Box, {\n        display: \"flex\",\n        alignItems: \"center\",\n        gap: 1,\n        children: [/*#__PURE__*/_jsxDEV(AssignmentIcon, {}, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 468,\n          columnNumber: 11\n        }, this), \"Crea Nuova Comanda con Cavi\"]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 467,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 466,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(DialogContent, {\n      children: /*#__PURE__*/_jsxDEV(Box, {\n        sx: {\n          pt: 2\n        },\n        children: [/*#__PURE__*/_jsxDEV(Stepper, {\n          activeStep: activeStep,\n          sx: {\n            mb: 3\n          },\n          children: steps.map(label => /*#__PURE__*/_jsxDEV(Step, {\n            children: /*#__PURE__*/_jsxDEV(StepLabel, {\n              children: label\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 478,\n              columnNumber: 17\n            }, this)\n          }, label, false, {\n            fileName: _jsxFileName,\n            lineNumber: 477,\n            columnNumber: 15\n          }, this))\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 475,\n          columnNumber: 11\n        }, this), error && /*#__PURE__*/_jsxDEV(Alert, {\n          severity: \"error\",\n          sx: {\n            mb: 2\n          },\n          children: error\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 484,\n          columnNumber: 13\n        }, this), renderStepContent()]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 474,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 473,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(DialogActions, {\n      children: [/*#__PURE__*/_jsxDEV(Button, {\n        onClick: handleClose,\n        children: \"Annulla\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 494,\n        columnNumber: 9\n      }, this), activeStep > 0 && /*#__PURE__*/_jsxDEV(Button, {\n        onClick: handleBack,\n        children: \"Indietro\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 499,\n        columnNumber: 11\n      }, this), activeStep < steps.length - 1 ? /*#__PURE__*/_jsxDEV(Button, {\n        onClick: handleNext,\n        variant: \"contained\",\n        disabled: activeStep === 1 && loadingCavi,\n        children: \"Avanti\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 505,\n        columnNumber: 11\n      }, this) : /*#__PURE__*/_jsxDEV(Button, {\n        onClick: handleSubmit,\n        variant: \"contained\",\n        disabled: loading || caviSelezionati.length === 0 || !formData.responsabile.trim(),\n        startIcon: loading ? /*#__PURE__*/_jsxDEV(CircularProgress, {\n          size: 20\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 517,\n          columnNumber: 34\n        }, this) : /*#__PURE__*/_jsxDEV(AddIcon, {}, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 517,\n          columnNumber: 67\n        }, this),\n        children: loading ? 'Creazione...' : 'Crea Comanda'\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 513,\n        columnNumber: 11\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 493,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 465,\n    columnNumber: 5\n  }, this);\n};\n_s(CreaComandaConCavi, \"uqvnVxMXuiS8DatQuCSSYWSN9VU=\");\n_c = CreaComandaConCavi;\nexport default CreaComandaConCavi;\nvar _c;\n$RefreshReg$(_c, \"CreaComandaConCavi\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "Box", "Card", "<PERSON><PERSON><PERSON><PERSON>", "Typography", "<PERSON><PERSON>", "Dialog", "DialogTitle", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "DialogActions", "TextField", "MenuItem", "<PERSON><PERSON>", "CircularProgress", "Stepper", "Step", "<PERSON><PERSON><PERSON><PERSON>", "Table", "TableBody", "TableCell", "TableContainer", "TableHead", "TableRow", "Paper", "Checkbox", "Chip", "Grid", "Autocomplete", "Add", "AddIcon", "Cable", "CableIcon", "Assignment", "AssignmentIcon", "comandeService", "responsabiliService", "jsxDEV", "_jsxDEV", "Fragment", "_Fragment", "CreaComandaConCavi", "cantiereId", "open", "onClose", "onSuccess", "tipoComandaPreselezionato", "caviPreselezionati", "_s", "activeStep", "setActiveStep", "loading", "setLoading", "error", "setError", "tipoComanda", "setTipoComanda", "caviDisponibili", "setCaviDisponibili", "caviSelezionati", "setCaviSelezionati", "loadingCavi", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "formData", "setFormData", "descrizione", "responsabile", "responsabile_email", "responsabile_telefono", "data_scadenza", "priorita", "note_capo_cantiere", "responsabiliDisponibili", "setResponsabiliDisponibili", "loadingResponsabili", "setLoadingResponsabili", "steps", "loadCaviDisponibili", "loadResponsabiliDisponibili", "length", "response", "getCaviDisponibili", "cavi_disponibili", "err", "console", "responsabili", "getResponsabiliCantiere", "handleNext", "trim", "prevStep", "handleBack", "handleCavoToggle", "cavo", "isSelected", "some", "c", "id_cavo", "filter", "handleSubmit", "comandaData", "tipo_comanda", "listaIdCavi", "map", "createComandaConCavi", "handleClose", "getTipoComandaLabel", "tipo", "renderStepContent", "children", "variant", "gutterBottom", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "fullWidth", "select", "label", "value", "onChange", "e", "target", "margin", "severity", "sx", "mt", "display", "justifyContent", "p", "mb", "icon", "color", "component", "maxHeight", "<PERSON><PERSON><PERSON><PERSON>", "padding", "hover", "onClick", "cursor", "checked", "tipologia", "sezione", "metri_te<PERSON>ci", "ubicazione_partenza", "ubicazione_arrivo", "container", "spacing", "item", "xs", "freeSolo", "options", "r", "nome_responsabile", "event", "newValue", "find", "email", "telefono", "onInputChange", "newInputValue", "renderInput", "params", "required", "helperText", "InputProps", "endAdornment", "size", "sm", "type", "InputLabelProps", "shrink", "multiline", "rows", "max<PERSON><PERSON><PERSON>", "alignItems", "gap", "pt", "disabled", "startIcon", "_c", "$RefreshReg$"], "sources": ["C:/CMS/webapp/frontend/src/components/comande/CreaComandaConCavi.js"], "sourcesContent": ["import React, { useState, useEffect } from 'react';\nimport {\n  Box,\n  Card,\n  CardContent,\n  Typo<PERSON>,\n  Button,\n  Dialog,\n  DialogTitle,\n  DialogContent,\n  DialogActions,\n  TextField,\n  MenuItem,\n  Alert,\n  CircularProgress,\n  Stepper,\n  Step,\n  StepLabel,\n  Table,\n  TableBody,\n  TableCell,\n  TableContainer,\n  TableHead,\n  TableRow,\n  Paper,\n  Checkbox,\n  Chip,\n  Grid,\n  Autocomplete\n} from '@mui/material';\nimport {\n  Add as AddIcon,\n  Cable as CableIcon,\n  Assignment as AssignmentIcon\n} from '@mui/icons-material';\nimport comandeService from '../../services/comandeService';\nimport responsabiliService from '../../services/responsabiliService';\n\nconst CreaComandaConCavi = ({\n  cantiereId,\n  open,\n  onClose,\n  onSuccess,\n  tipoComandaPreselezionato = null,\n  caviPreselezionati = []\n}) => {\n  const [activeStep, setActiveStep] = useState(0);\n  const [loading, setLoading] = useState(false);\n  const [error, setError] = useState(null);\n  \n  // Step 1: Selezione tipo comanda\n  const [tipoComanda, setTipoComanda] = useState(tipoComandaPreselezionato || 'POSA');\n  \n  // Step 2: Selezione cavi\n  const [caviDisponibili, setCaviDisponibili] = useState([]);\n  const [caviSelezionati, setCaviSelezionati] = useState(caviPreselezionati || []);\n  const [loadingCavi, setLoadingCavi] = useState(false);\n\n  // Step 3: Dettagli comanda\n  const [formData, setFormData] = useState({\n    descrizione: '',\n    responsabile: '',\n    responsabile_email: '',\n    responsabile_telefono: '',\n    data_scadenza: '',\n    priorita: 'NORMALE',\n    note_capo_cantiere: ''\n  });\n\n  // Gestione responsabili\n  const [responsabiliDisponibili, setResponsabiliDisponibili] = useState([]);\n  const [loadingResponsabili, setLoadingResponsabili] = useState(false);\n\n  const steps = ['Tipo Comanda', 'Selezione Cavi', 'Dettagli Comanda'];\n\n  // Carica cavi disponibili quando cambia il tipo comanda\n  useEffect(() => {\n    if (open && tipoComanda) {\n      loadCaviDisponibili();\n    }\n  }, [open, tipoComanda]);\n\n  // Carica responsabili disponibili quando si apre il dialog\n  useEffect(() => {\n    if (open && cantiereId) {\n      loadResponsabiliDisponibili();\n    }\n  }, [open, cantiereId]);\n\n  // Se ci sono cavi preselezionati, salta al passo dei dettagli\n  useEffect(() => {\n    if (open && caviPreselezionati.length > 0 && tipoComandaPreselezionato) {\n      setActiveStep(2); // Vai direttamente ai dettagli\n    }\n  }, [open, caviPreselezionati, tipoComandaPreselezionato]);\n\n  const loadCaviDisponibili = async () => {\n    try {\n      setLoadingCavi(true);\n      const response = await comandeService.getCaviDisponibili(cantiereId, tipoComanda);\n      setCaviDisponibili(response.cavi_disponibili || []);\n      setCaviSelezionati([]); // Reset selezione\n      setError(null);\n    } catch (err) {\n      console.error('Errore nel caricamento dei cavi:', err);\n      setError('Errore nel caricamento dei cavi disponibili');\n    } finally {\n      setLoadingCavi(false);\n    }\n  };\n\n  const loadResponsabiliDisponibili = async () => {\n    try {\n      setLoadingResponsabili(true);\n      const responsabili = await responsabiliService.getResponsabiliCantiere(cantiereId);\n      setResponsabiliDisponibili(responsabili || []);\n    } catch (err) {\n      console.error('Errore nel caricamento dei responsabili:', err);\n      // Non mostrare errore per i responsabili, è opzionale\n    } finally {\n      setLoadingResponsabili(false);\n    }\n  };\n\n  const handleNext = () => {\n    if (activeStep === 1 && caviSelezionati.length === 0) {\n      setError('Seleziona almeno un cavo');\n      return;\n    }\n    if (activeStep === 2 && !formData.responsabile.trim()) {\n      setError('Il responsabile è obbligatorio');\n      return;\n    }\n    setError(null);\n    setActiveStep((prevStep) => prevStep + 1);\n  };\n\n  const handleBack = () => {\n    setActiveStep((prevStep) => prevStep - 1);\n  };\n\n  const handleCavoToggle = (cavo) => {\n    const isSelected = caviSelezionati.some(c => c.id_cavo === cavo.id_cavo);\n    if (isSelected) {\n      setCaviSelezionati(caviSelezionati.filter(c => c.id_cavo !== cavo.id_cavo));\n    } else {\n      setCaviSelezionati([...caviSelezionati, cavo]);\n    }\n  };\n\n  const handleSubmit = async () => {\n    try {\n      setLoading(true);\n      \n      const comandaData = {\n        tipo_comanda: tipoComanda,\n        descrizione: formData.descrizione,\n        responsabile: formData.responsabile,\n        data_scadenza: formData.data_scadenza || null,\n        priorita: formData.priorita,\n        note_capo_cantiere: formData.note_capo_cantiere\n      };\n\n      const listaIdCavi = caviSelezionati.map(c => c.id_cavo);\n\n      const response = await comandeService.createComandaConCavi(\n        cantiereId,\n        comandaData,\n        listaIdCavi\n      );\n\n      onSuccess && onSuccess(response);\n      handleClose();\n    } catch (err) {\n      console.error('Errore nella creazione:', err);\n      setError('Errore nella creazione della comanda');\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  const handleClose = () => {\n    setActiveStep(0);\n    setTipoComanda(tipoComandaPreselezionato || 'POSA');\n    setCaviDisponibili([]);\n    setCaviSelezionati(caviPreselezionati || []);\n    setFormData({\n      descrizione: '',\n      responsabile: '',\n      responsabile_email: '',\n      responsabile_telefono: '',\n      data_scadenza: '',\n      priorita: 'NORMALE',\n      note_capo_cantiere: ''\n    });\n    setError(null);\n    onClose && onClose();\n  };\n\n  const getTipoComandaLabel = (tipo) => {\n    switch (tipo) {\n      case 'POSA': return 'Posa';\n      case 'COLLEGAMENTO_PARTENZA': return 'Collegamento Partenza';\n      case 'COLLEGAMENTO_ARRIVO': return 'Collegamento Arrivo';\n      case 'CERTIFICAZIONE': return 'Certificazione';\n      case 'TESTING': return 'Testing';\n      default: return tipo;\n    }\n  };\n\n  const renderStepContent = () => {\n    switch (activeStep) {\n      case 0:\n        return (\n          <Box>\n            <Typography variant=\"h6\" gutterBottom>\n              Seleziona il tipo di comanda\n            </Typography>\n            <TextField\n              fullWidth\n              select\n              label=\"Tipo Comanda\"\n              value={tipoComanda}\n              onChange={(e) => setTipoComanda(e.target.value)}\n              margin=\"normal\"\n            >\n              <MenuItem value=\"POSA\">Posa</MenuItem>\n              <MenuItem value=\"COLLEGAMENTO_PARTENZA\">Collegamento Partenza</MenuItem>\n              <MenuItem value=\"COLLEGAMENTO_ARRIVO\">Collegamento Arrivo</MenuItem>\n              <MenuItem value=\"CERTIFICAZIONE\">Certificazione</MenuItem>\n              <MenuItem value=\"TESTING\">Testing</MenuItem>\n            </TextField>\n            <Alert severity=\"info\" sx={{ mt: 2 }}>\n              <strong>{getTipoComandaLabel(tipoComanda)}</strong>: \n              {tipoComanda === 'POSA' && ' Comanda per la posa fisica dei cavi'}\n              {tipoComanda === 'COLLEGAMENTO_PARTENZA' && ' Comanda per il collegamento lato partenza'}\n              {tipoComanda === 'COLLEGAMENTO_ARRIVO' && ' Comanda per il collegamento lato arrivo'}\n              {tipoComanda === 'CERTIFICAZIONE' && ' Comanda per la certificazione dei cavi'}\n              {tipoComanda === 'TESTING' && ' Comanda per test e certificazione'}\n            </Alert>\n          </Box>\n        );\n\n      case 1:\n        return (\n          <Box>\n            <Typography variant=\"h6\" gutterBottom>\n              Seleziona i cavi per la comanda di {getTipoComandaLabel(tipoComanda)}\n            </Typography>\n            \n            {loadingCavi ? (\n              <Box display=\"flex\" justifyContent=\"center\" p={3}>\n                <CircularProgress />\n              </Box>\n            ) : (\n              <>\n                <Box mb={2}>\n                  <Chip \n                    icon={<CableIcon />}\n                    label={`${caviSelezionati.length} cavi selezionati di ${caviDisponibili.length} disponibili`}\n                    color={caviSelezionati.length > 0 ? 'primary' : 'default'}\n                  />\n                </Box>\n                \n                <TableContainer component={Paper} sx={{ maxHeight: 400 }}>\n                  <Table stickyHeader>\n                    <TableHead>\n                      <TableRow>\n                        <TableCell padding=\"checkbox\">Sel.</TableCell>\n                        <TableCell>ID Cavo</TableCell>\n                        <TableCell>Tipologia</TableCell>\n                        <TableCell>Sezione</TableCell>\n                        <TableCell>Metri</TableCell>\n                        <TableCell>Partenza</TableCell>\n                        <TableCell>Arrivo</TableCell>\n                      </TableRow>\n                    </TableHead>\n                    <TableBody>\n                      {caviDisponibili.map((cavo) => {\n                        const isSelected = caviSelezionati.some(c => c.id_cavo === cavo.id_cavo);\n                        return (\n                          <TableRow \n                            key={cavo.id_cavo}\n                            hover\n                            onClick={() => handleCavoToggle(cavo)}\n                            sx={{ cursor: 'pointer' }}\n                          >\n                            <TableCell padding=\"checkbox\">\n                              <Checkbox checked={isSelected} />\n                            </TableCell>\n                            <TableCell>{cavo.id_cavo}</TableCell>\n                            <TableCell>{cavo.tipologia}</TableCell>\n                            <TableCell>{cavo.sezione}</TableCell>\n                            <TableCell>{cavo.metri_teorici}</TableCell>\n                            <TableCell>{cavo.ubicazione_partenza}</TableCell>\n                            <TableCell>{cavo.ubicazione_arrivo}</TableCell>\n                          </TableRow>\n                        );\n                      })}\n                    </TableBody>\n                  </Table>\n                </TableContainer>\n\n                {caviDisponibili.length === 0 && (\n                  <Alert severity=\"warning\" sx={{ mt: 2 }}>\n                    Nessun cavo disponibile per il tipo di comanda selezionato.\n                  </Alert>\n                )}\n              </>\n            )}\n          </Box>\n        );\n\n      case 2:\n        return (\n          <Box>\n            <Typography variant=\"h6\" gutterBottom>\n              Dettagli della comanda\n            </Typography>\n            \n            <Grid container spacing={2}>\n              <Grid item xs={12}>\n                <Autocomplete\n                  fullWidth\n                  freeSolo\n                  options={responsabiliDisponibili.map(r => r.nome_responsabile)}\n                  value={formData.responsabile}\n                  onChange={(event, newValue) => {\n                    const responsabile = responsabiliDisponibili.find(r => r.nome_responsabile === newValue);\n                    if (responsabile) {\n                      setFormData({\n                        ...formData,\n                        responsabile: newValue || '',\n                        responsabile_email: responsabile.email || '',\n                        responsabile_telefono: responsabile.telefono || ''\n                      });\n                    } else {\n                      setFormData({ ...formData, responsabile: newValue || '' });\n                    }\n                  }}\n                  onInputChange={(event, newInputValue) => {\n                    setFormData({ ...formData, responsabile: newInputValue });\n                  }}\n                  renderInput={(params) => (\n                    <TextField\n                      {...params}\n                      label=\"Nome Responsabile\"\n                      margin=\"normal\"\n                      required\n                      helperText=\"Chi eseguirà il lavoro (obbligatorio)\"\n                      InputProps={{\n                        ...params.InputProps,\n                        endAdornment: (\n                          <>\n                            {loadingResponsabili ? <CircularProgress color=\"inherit\" size={20} /> : null}\n                            {params.InputProps.endAdornment}\n                          </>\n                        ),\n                      }}\n                    />\n                  )}\n                />\n              </Grid>\n\n              <Grid item xs={12} sm={6}>\n                <TextField\n                  fullWidth\n                  label=\"Email Responsabile\"\n                  type=\"email\"\n                  value={formData.responsabile_email}\n                  onChange={(e) => setFormData({ ...formData, responsabile_email: e.target.value })}\n                  margin=\"normal\"\n                  helperText=\"Email per notifiche (opzionale)\"\n                />\n              </Grid>\n\n              <Grid item xs={12} sm={6}>\n                <TextField\n                  fullWidth\n                  label=\"Telefono Responsabile\"\n                  value={formData.responsabile_telefono}\n                  onChange={(e) => setFormData({ ...formData, responsabile_telefono: e.target.value })}\n                  margin=\"normal\"\n                  helperText=\"Numero per SMS (opzionale)\"\n                />\n              </Grid>\n\n              <Grid item xs={12} sm={6}>\n                <TextField\n                  fullWidth\n                  select\n                  label=\"Priorità\"\n                  value={formData.priorita}\n                  onChange={(e) => setFormData({ ...formData, priorita: e.target.value })}\n                  margin=\"normal\"\n                >\n                  <MenuItem value=\"BASSA\">Bassa</MenuItem>\n                  <MenuItem value=\"NORMALE\">Normale</MenuItem>\n                  <MenuItem value=\"ALTA\">Alta</MenuItem>\n                  <MenuItem value=\"URGENTE\">Urgente</MenuItem>\n                </TextField>\n              </Grid>\n\n              <Grid item xs={12} sm={6}>\n                <TextField\n                  fullWidth\n                  label=\"Data Scadenza\"\n                  type=\"date\"\n                  value={formData.data_scadenza}\n                  onChange={(e) => setFormData({ ...formData, data_scadenza: e.target.value })}\n                  margin=\"normal\"\n                  InputLabelProps={{ shrink: true }}\n                />\n              </Grid>\n            </Grid>\n\n            <TextField\n              fullWidth\n              label=\"Descrizione\"\n              value={formData.descrizione}\n              onChange={(e) => setFormData({ ...formData, descrizione: e.target.value })}\n              margin=\"normal\"\n              multiline\n              rows={3}\n            />\n\n            <TextField\n              fullWidth\n              label=\"Note Capo Cantiere\"\n              value={formData.note_capo_cantiere}\n              onChange={(e) => setFormData({ ...formData, note_capo_cantiere: e.target.value })}\n              margin=\"normal\"\n              multiline\n              rows={2}\n              helperText=\"Istruzioni specifiche per il responsabile\"\n            />\n\n            <TextField\n              fullWidth\n              label=\"Data Scadenza\"\n              type=\"date\"\n              value={formData.data_scadenza}\n              onChange={(e) => setFormData({ ...formData, data_scadenza: e.target.value })}\n              margin=\"normal\"\n              InputLabelProps={{ shrink: true }}\n            />\n\n            <Alert severity=\"info\" sx={{ mt: 2 }}>\n              <Typography variant=\"subtitle2\">Riepilogo:</Typography>\n              <Typography variant=\"body2\">\n                • Tipo: {getTipoComandaLabel(tipoComanda)}<br/>\n                • Cavi selezionati: {caviSelezionati.length}<br/>\n                • Responsabile: {formData.responsabile || 'Non specificato'}\n              </Typography>\n            </Alert>\n          </Box>\n        );\n\n      default:\n        return null;\n    }\n  };\n\n  return (\n    <Dialog open={open} onClose={handleClose} maxWidth=\"lg\" fullWidth>\n      <DialogTitle>\n        <Box display=\"flex\" alignItems=\"center\" gap={1}>\n          <AssignmentIcon />\n          Crea Nuova Comanda con Cavi\n        </Box>\n      </DialogTitle>\n      \n      <DialogContent>\n        <Box sx={{ pt: 2 }}>\n          <Stepper activeStep={activeStep} sx={{ mb: 3 }}>\n            {steps.map((label) => (\n              <Step key={label}>\n                <StepLabel>{label}</StepLabel>\n              </Step>\n            ))}\n          </Stepper>\n\n          {error && (\n            <Alert severity=\"error\" sx={{ mb: 2 }}>\n              {error}\n            </Alert>\n          )}\n\n          {renderStepContent()}\n        </Box>\n      </DialogContent>\n\n      <DialogActions>\n        <Button onClick={handleClose}>\n          Annulla\n        </Button>\n        \n        {activeStep > 0 && (\n          <Button onClick={handleBack}>\n            Indietro\n          </Button>\n        )}\n        \n        {activeStep < steps.length - 1 ? (\n          <Button \n            onClick={handleNext} \n            variant=\"contained\"\n            disabled={activeStep === 1 && loadingCavi}\n          >\n            Avanti\n          </Button>\n        ) : (\n          <Button \n            onClick={handleSubmit} \n            variant=\"contained\"\n            disabled={loading || caviSelezionati.length === 0 || !formData.responsabile.trim()}\n            startIcon={loading ? <CircularProgress size={20} /> : <AddIcon />}\n          >\n            {loading ? 'Creazione...' : 'Crea Comanda'}\n          </Button>\n        )}\n      </DialogActions>\n    </Dialog>\n  );\n};\n\nexport default CreaComandaConCavi;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAClD,SACEC,GAAG,EACHC,IAAI,EACJC,WAAW,EACXC,UAAU,EACVC,MAAM,EACNC,MAAM,EACNC,WAAW,EACXC,aAAa,EACbC,aAAa,EACbC,SAAS,EACTC,QAAQ,EACRC,KAAK,EACLC,gBAAgB,EAChBC,OAAO,EACPC,IAAI,EACJC,SAAS,EACTC,KAAK,EACLC,SAAS,EACTC,SAAS,EACTC,cAAc,EACdC,SAAS,EACTC,QAAQ,EACRC,KAAK,EACLC,QAAQ,EACRC,IAAI,EACJC,IAAI,EACJC,YAAY,QACP,eAAe;AACtB,SACEC,GAAG,IAAIC,OAAO,EACdC,KAAK,IAAIC,SAAS,EAClBC,UAAU,IAAIC,cAAc,QACvB,qBAAqB;AAC5B,OAAOC,cAAc,MAAM,+BAA+B;AAC1D,OAAOC,mBAAmB,MAAM,oCAAoC;AAAC,SAAAC,MAAA,IAAAC,OAAA,EAAAC,QAAA,IAAAC,SAAA;AAErE,MAAMC,kBAAkB,GAAGA,CAAC;EAC1BC,UAAU;EACVC,IAAI;EACJC,OAAO;EACPC,SAAS;EACTC,yBAAyB,GAAG,IAAI;EAChCC,kBAAkB,GAAG;AACvB,CAAC,KAAK;EAAAC,EAAA;EACJ,MAAM,CAACC,UAAU,EAAEC,aAAa,CAAC,GAAGlD,QAAQ,CAAC,CAAC,CAAC;EAC/C,MAAM,CAACmD,OAAO,EAAEC,UAAU,CAAC,GAAGpD,QAAQ,CAAC,KAAK,CAAC;EAC7C,MAAM,CAACqD,KAAK,EAAEC,QAAQ,CAAC,GAAGtD,QAAQ,CAAC,IAAI,CAAC;;EAExC;EACA,MAAM,CAACuD,WAAW,EAAEC,cAAc,CAAC,GAAGxD,QAAQ,CAAC8C,yBAAyB,IAAI,MAAM,CAAC;;EAEnF;EACA,MAAM,CAACW,eAAe,EAAEC,kBAAkB,CAAC,GAAG1D,QAAQ,CAAC,EAAE,CAAC;EAC1D,MAAM,CAAC2D,eAAe,EAAEC,kBAAkB,CAAC,GAAG5D,QAAQ,CAAC+C,kBAAkB,IAAI,EAAE,CAAC;EAChF,MAAM,CAACc,WAAW,EAAEC,cAAc,CAAC,GAAG9D,QAAQ,CAAC,KAAK,CAAC;;EAErD;EACA,MAAM,CAAC+D,QAAQ,EAAEC,WAAW,CAAC,GAAGhE,QAAQ,CAAC;IACvCiE,WAAW,EAAE,EAAE;IACfC,YAAY,EAAE,EAAE;IAChBC,kBAAkB,EAAE,EAAE;IACtBC,qBAAqB,EAAE,EAAE;IACzBC,aAAa,EAAE,EAAE;IACjBC,QAAQ,EAAE,SAAS;IACnBC,kBAAkB,EAAE;EACtB,CAAC,CAAC;;EAEF;EACA,MAAM,CAACC,uBAAuB,EAAEC,0BAA0B,CAAC,GAAGzE,QAAQ,CAAC,EAAE,CAAC;EAC1E,MAAM,CAAC0E,mBAAmB,EAAEC,sBAAsB,CAAC,GAAG3E,QAAQ,CAAC,KAAK,CAAC;EAErE,MAAM4E,KAAK,GAAG,CAAC,cAAc,EAAE,gBAAgB,EAAE,kBAAkB,CAAC;;EAEpE;EACA3E,SAAS,CAAC,MAAM;IACd,IAAI0C,IAAI,IAAIY,WAAW,EAAE;MACvBsB,mBAAmB,CAAC,CAAC;IACvB;EACF,CAAC,EAAE,CAAClC,IAAI,EAAEY,WAAW,CAAC,CAAC;;EAEvB;EACAtD,SAAS,CAAC,MAAM;IACd,IAAI0C,IAAI,IAAID,UAAU,EAAE;MACtBoC,2BAA2B,CAAC,CAAC;IAC/B;EACF,CAAC,EAAE,CAACnC,IAAI,EAAED,UAAU,CAAC,CAAC;;EAEtB;EACAzC,SAAS,CAAC,MAAM;IACd,IAAI0C,IAAI,IAAII,kBAAkB,CAACgC,MAAM,GAAG,CAAC,IAAIjC,yBAAyB,EAAE;MACtEI,aAAa,CAAC,CAAC,CAAC,CAAC,CAAC;IACpB;EACF,CAAC,EAAE,CAACP,IAAI,EAAEI,kBAAkB,EAAED,yBAAyB,CAAC,CAAC;EAEzD,MAAM+B,mBAAmB,GAAG,MAAAA,CAAA,KAAY;IACtC,IAAI;MACFf,cAAc,CAAC,IAAI,CAAC;MACpB,MAAMkB,QAAQ,GAAG,MAAM7C,cAAc,CAAC8C,kBAAkB,CAACvC,UAAU,EAAEa,WAAW,CAAC;MACjFG,kBAAkB,CAACsB,QAAQ,CAACE,gBAAgB,IAAI,EAAE,CAAC;MACnDtB,kBAAkB,CAAC,EAAE,CAAC,CAAC,CAAC;MACxBN,QAAQ,CAAC,IAAI,CAAC;IAChB,CAAC,CAAC,OAAO6B,GAAG,EAAE;MACZC,OAAO,CAAC/B,KAAK,CAAC,kCAAkC,EAAE8B,GAAG,CAAC;MACtD7B,QAAQ,CAAC,6CAA6C,CAAC;IACzD,CAAC,SAAS;MACRQ,cAAc,CAAC,KAAK,CAAC;IACvB;EACF,CAAC;EAED,MAAMgB,2BAA2B,GAAG,MAAAA,CAAA,KAAY;IAC9C,IAAI;MACFH,sBAAsB,CAAC,IAAI,CAAC;MAC5B,MAAMU,YAAY,GAAG,MAAMjD,mBAAmB,CAACkD,uBAAuB,CAAC5C,UAAU,CAAC;MAClF+B,0BAA0B,CAACY,YAAY,IAAI,EAAE,CAAC;IAChD,CAAC,CAAC,OAAOF,GAAG,EAAE;MACZC,OAAO,CAAC/B,KAAK,CAAC,0CAA0C,EAAE8B,GAAG,CAAC;MAC9D;IACF,CAAC,SAAS;MACRR,sBAAsB,CAAC,KAAK,CAAC;IAC/B;EACF,CAAC;EAED,MAAMY,UAAU,GAAGA,CAAA,KAAM;IACvB,IAAItC,UAAU,KAAK,CAAC,IAAIU,eAAe,CAACoB,MAAM,KAAK,CAAC,EAAE;MACpDzB,QAAQ,CAAC,0BAA0B,CAAC;MACpC;IACF;IACA,IAAIL,UAAU,KAAK,CAAC,IAAI,CAACc,QAAQ,CAACG,YAAY,CAACsB,IAAI,CAAC,CAAC,EAAE;MACrDlC,QAAQ,CAAC,gCAAgC,CAAC;MAC1C;IACF;IACAA,QAAQ,CAAC,IAAI,CAAC;IACdJ,aAAa,CAAEuC,QAAQ,IAAKA,QAAQ,GAAG,CAAC,CAAC;EAC3C,CAAC;EAED,MAAMC,UAAU,GAAGA,CAAA,KAAM;IACvBxC,aAAa,CAAEuC,QAAQ,IAAKA,QAAQ,GAAG,CAAC,CAAC;EAC3C,CAAC;EAED,MAAME,gBAAgB,GAAIC,IAAI,IAAK;IACjC,MAAMC,UAAU,GAAGlC,eAAe,CAACmC,IAAI,CAACC,CAAC,IAAIA,CAAC,CAACC,OAAO,KAAKJ,IAAI,CAACI,OAAO,CAAC;IACxE,IAAIH,UAAU,EAAE;MACdjC,kBAAkB,CAACD,eAAe,CAACsC,MAAM,CAACF,CAAC,IAAIA,CAAC,CAACC,OAAO,KAAKJ,IAAI,CAACI,OAAO,CAAC,CAAC;IAC7E,CAAC,MAAM;MACLpC,kBAAkB,CAAC,CAAC,GAAGD,eAAe,EAAEiC,IAAI,CAAC,CAAC;IAChD;EACF,CAAC;EAED,MAAMM,YAAY,GAAG,MAAAA,CAAA,KAAY;IAC/B,IAAI;MACF9C,UAAU,CAAC,IAAI,CAAC;MAEhB,MAAM+C,WAAW,GAAG;QAClBC,YAAY,EAAE7C,WAAW;QACzBU,WAAW,EAAEF,QAAQ,CAACE,WAAW;QACjCC,YAAY,EAAEH,QAAQ,CAACG,YAAY;QACnCG,aAAa,EAAEN,QAAQ,CAACM,aAAa,IAAI,IAAI;QAC7CC,QAAQ,EAAEP,QAAQ,CAACO,QAAQ;QAC3BC,kBAAkB,EAAER,QAAQ,CAACQ;MAC/B,CAAC;MAED,MAAM8B,WAAW,GAAG1C,eAAe,CAAC2C,GAAG,CAACP,CAAC,IAAIA,CAAC,CAACC,OAAO,CAAC;MAEvD,MAAMhB,QAAQ,GAAG,MAAM7C,cAAc,CAACoE,oBAAoB,CACxD7D,UAAU,EACVyD,WAAW,EACXE,WACF,CAAC;MAEDxD,SAAS,IAAIA,SAAS,CAACmC,QAAQ,CAAC;MAChCwB,WAAW,CAAC,CAAC;IACf,CAAC,CAAC,OAAOrB,GAAG,EAAE;MACZC,OAAO,CAAC/B,KAAK,CAAC,yBAAyB,EAAE8B,GAAG,CAAC;MAC7C7B,QAAQ,CAAC,sCAAsC,CAAC;IAClD,CAAC,SAAS;MACRF,UAAU,CAAC,KAAK,CAAC;IACnB;EACF,CAAC;EAED,MAAMoD,WAAW,GAAGA,CAAA,KAAM;IACxBtD,aAAa,CAAC,CAAC,CAAC;IAChBM,cAAc,CAACV,yBAAyB,IAAI,MAAM,CAAC;IACnDY,kBAAkB,CAAC,EAAE,CAAC;IACtBE,kBAAkB,CAACb,kBAAkB,IAAI,EAAE,CAAC;IAC5CiB,WAAW,CAAC;MACVC,WAAW,EAAE,EAAE;MACfC,YAAY,EAAE,EAAE;MAChBC,kBAAkB,EAAE,EAAE;MACtBC,qBAAqB,EAAE,EAAE;MACzBC,aAAa,EAAE,EAAE;MACjBC,QAAQ,EAAE,SAAS;MACnBC,kBAAkB,EAAE;IACtB,CAAC,CAAC;IACFjB,QAAQ,CAAC,IAAI,CAAC;IACdV,OAAO,IAAIA,OAAO,CAAC,CAAC;EACtB,CAAC;EAED,MAAM6D,mBAAmB,GAAIC,IAAI,IAAK;IACpC,QAAQA,IAAI;MACV,KAAK,MAAM;QAAE,OAAO,MAAM;MAC1B,KAAK,uBAAuB;QAAE,OAAO,uBAAuB;MAC5D,KAAK,qBAAqB;QAAE,OAAO,qBAAqB;MACxD,KAAK,gBAAgB;QAAE,OAAO,gBAAgB;MAC9C,KAAK,SAAS;QAAE,OAAO,SAAS;MAChC;QAAS,OAAOA,IAAI;IACtB;EACF,CAAC;EAED,MAAMC,iBAAiB,GAAGA,CAAA,KAAM;IAC9B,QAAQ1D,UAAU;MAChB,KAAK,CAAC;QACJ,oBACEX,OAAA,CAACpC,GAAG;UAAA0G,QAAA,gBACFtE,OAAA,CAACjC,UAAU;YAACwG,OAAO,EAAC,IAAI;YAACC,YAAY;YAAAF,QAAA,EAAC;UAEtC;YAAAG,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAY,CAAC,eACb5E,OAAA,CAAC3B,SAAS;YACRwG,SAAS;YACTC,MAAM;YACNC,KAAK,EAAC,cAAc;YACpBC,KAAK,EAAE/D,WAAY;YACnBgE,QAAQ,EAAGC,CAAC,IAAKhE,cAAc,CAACgE,CAAC,CAACC,MAAM,CAACH,KAAK,CAAE;YAChDI,MAAM,EAAC,QAAQ;YAAAd,QAAA,gBAEftE,OAAA,CAAC1B,QAAQ;cAAC0G,KAAK,EAAC,MAAM;cAAAV,QAAA,EAAC;YAAI;cAAAG,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAU,CAAC,eACtC5E,OAAA,CAAC1B,QAAQ;cAAC0G,KAAK,EAAC,uBAAuB;cAAAV,QAAA,EAAC;YAAqB;cAAAG,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAU,CAAC,eACxE5E,OAAA,CAAC1B,QAAQ;cAAC0G,KAAK,EAAC,qBAAqB;cAAAV,QAAA,EAAC;YAAmB;cAAAG,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAU,CAAC,eACpE5E,OAAA,CAAC1B,QAAQ;cAAC0G,KAAK,EAAC,gBAAgB;cAAAV,QAAA,EAAC;YAAc;cAAAG,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAU,CAAC,eAC1D5E,OAAA,CAAC1B,QAAQ;cAAC0G,KAAK,EAAC,SAAS;cAAAV,QAAA,EAAC;YAAO;cAAAG,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAU,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACnC,CAAC,eACZ5E,OAAA,CAACzB,KAAK;YAAC8G,QAAQ,EAAC,MAAM;YAACC,EAAE,EAAE;cAAEC,EAAE,EAAE;YAAE,CAAE;YAAAjB,QAAA,gBACnCtE,OAAA;cAAAsE,QAAA,EAASH,mBAAmB,CAAClD,WAAW;YAAC;cAAAwD,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAS,CAAC,KACnD,EAAC3D,WAAW,KAAK,MAAM,IAAI,sCAAsC,EAChEA,WAAW,KAAK,uBAAuB,IAAI,4CAA4C,EACvFA,WAAW,KAAK,qBAAqB,IAAI,0CAA0C,EACnFA,WAAW,KAAK,gBAAgB,IAAI,yCAAyC,EAC7EA,WAAW,KAAK,SAAS,IAAI,oCAAoC;UAAA;YAAAwD,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC7D,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACL,CAAC;MAGV,KAAK,CAAC;QACJ,oBACE5E,OAAA,CAACpC,GAAG;UAAA0G,QAAA,gBACFtE,OAAA,CAACjC,UAAU;YAACwG,OAAO,EAAC,IAAI;YAACC,YAAY;YAAAF,QAAA,GAAC,qCACD,EAACH,mBAAmB,CAAClD,WAAW,CAAC;UAAA;YAAAwD,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC1D,CAAC,EAEZrD,WAAW,gBACVvB,OAAA,CAACpC,GAAG;YAAC4H,OAAO,EAAC,MAAM;YAACC,cAAc,EAAC,QAAQ;YAACC,CAAC,EAAE,CAAE;YAAApB,QAAA,eAC/CtE,OAAA,CAACxB,gBAAgB;cAAAiG,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACjB,CAAC,gBAEN5E,OAAA,CAAAE,SAAA;YAAAoE,QAAA,gBACEtE,OAAA,CAACpC,GAAG;cAAC+H,EAAE,EAAE,CAAE;cAAArB,QAAA,eACTtE,OAAA,CAACZ,IAAI;gBACHwG,IAAI,eAAE5F,OAAA,CAACN,SAAS;kBAAA+E,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAE;gBACpBG,KAAK,EAAE,GAAG1D,eAAe,CAACoB,MAAM,wBAAwBtB,eAAe,CAACsB,MAAM,cAAe;gBAC7FoD,KAAK,EAAExE,eAAe,CAACoB,MAAM,GAAG,CAAC,GAAG,SAAS,GAAG;cAAU;gBAAAgC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC3D;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACC,CAAC,eAEN5E,OAAA,CAACjB,cAAc;cAAC+G,SAAS,EAAE5G,KAAM;cAACoG,EAAE,EAAE;gBAAES,SAAS,EAAE;cAAI,CAAE;cAAAzB,QAAA,eACvDtE,OAAA,CAACpB,KAAK;gBAACoH,YAAY;gBAAA1B,QAAA,gBACjBtE,OAAA,CAAChB,SAAS;kBAAAsF,QAAA,eACRtE,OAAA,CAACf,QAAQ;oBAAAqF,QAAA,gBACPtE,OAAA,CAAClB,SAAS;sBAACmH,OAAO,EAAC,UAAU;sBAAA3B,QAAA,EAAC;oBAAI;sBAAAG,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAW,CAAC,eAC9C5E,OAAA,CAAClB,SAAS;sBAAAwF,QAAA,EAAC;oBAAO;sBAAAG,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAW,CAAC,eAC9B5E,OAAA,CAAClB,SAAS;sBAAAwF,QAAA,EAAC;oBAAS;sBAAAG,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAW,CAAC,eAChC5E,OAAA,CAAClB,SAAS;sBAAAwF,QAAA,EAAC;oBAAO;sBAAAG,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAW,CAAC,eAC9B5E,OAAA,CAAClB,SAAS;sBAAAwF,QAAA,EAAC;oBAAK;sBAAAG,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAW,CAAC,eAC5B5E,OAAA,CAAClB,SAAS;sBAAAwF,QAAA,EAAC;oBAAQ;sBAAAG,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAW,CAAC,eAC/B5E,OAAA,CAAClB,SAAS;sBAAAwF,QAAA,EAAC;oBAAM;sBAAAG,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAW,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACrB;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACF,CAAC,eACZ5E,OAAA,CAACnB,SAAS;kBAAAyF,QAAA,EACPnD,eAAe,CAAC6C,GAAG,CAAEV,IAAI,IAAK;oBAC7B,MAAMC,UAAU,GAAGlC,eAAe,CAACmC,IAAI,CAACC,CAAC,IAAIA,CAAC,CAACC,OAAO,KAAKJ,IAAI,CAACI,OAAO,CAAC;oBACxE,oBACE1D,OAAA,CAACf,QAAQ;sBAEPiH,KAAK;sBACLC,OAAO,EAAEA,CAAA,KAAM9C,gBAAgB,CAACC,IAAI,CAAE;sBACtCgC,EAAE,EAAE;wBAAEc,MAAM,EAAE;sBAAU,CAAE;sBAAA9B,QAAA,gBAE1BtE,OAAA,CAAClB,SAAS;wBAACmH,OAAO,EAAC,UAAU;wBAAA3B,QAAA,eAC3BtE,OAAA,CAACb,QAAQ;0BAACkH,OAAO,EAAE9C;wBAAW;0BAAAkB,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAE;sBAAC;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACxB,CAAC,eACZ5E,OAAA,CAAClB,SAAS;wBAAAwF,QAAA,EAAEhB,IAAI,CAACI;sBAAO;wBAAAe,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAY,CAAC,eACrC5E,OAAA,CAAClB,SAAS;wBAAAwF,QAAA,EAAEhB,IAAI,CAACgD;sBAAS;wBAAA7B,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAY,CAAC,eACvC5E,OAAA,CAAClB,SAAS;wBAAAwF,QAAA,EAAEhB,IAAI,CAACiD;sBAAO;wBAAA9B,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAY,CAAC,eACrC5E,OAAA,CAAClB,SAAS;wBAAAwF,QAAA,EAAEhB,IAAI,CAACkD;sBAAa;wBAAA/B,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAY,CAAC,eAC3C5E,OAAA,CAAClB,SAAS;wBAAAwF,QAAA,EAAEhB,IAAI,CAACmD;sBAAmB;wBAAAhC,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAY,CAAC,eACjD5E,OAAA,CAAClB,SAAS;wBAAAwF,QAAA,EAAEhB,IAAI,CAACoD;sBAAiB;wBAAAjC,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAY,CAAC;oBAAA,GAb1CtB,IAAI,CAACI,OAAO;sBAAAe,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAcT,CAAC;kBAEf,CAAC;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACO,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACP;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACM,CAAC,EAEhBzD,eAAe,CAACsB,MAAM,KAAK,CAAC,iBAC3BzC,OAAA,CAACzB,KAAK;cAAC8G,QAAQ,EAAC,SAAS;cAACC,EAAE,EAAE;gBAAEC,EAAE,EAAE;cAAE,CAAE;cAAAjB,QAAA,EAAC;YAEzC;cAAAG,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CACR;UAAA,eACD,CACH;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACE,CAAC;MAGV,KAAK,CAAC;QACJ,oBACE5E,OAAA,CAACpC,GAAG;UAAA0G,QAAA,gBACFtE,OAAA,CAACjC,UAAU;YAACwG,OAAO,EAAC,IAAI;YAACC,YAAY;YAAAF,QAAA,EAAC;UAEtC;YAAAG,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAY,CAAC,eAEb5E,OAAA,CAACX,IAAI;YAACsH,SAAS;YAACC,OAAO,EAAE,CAAE;YAAAtC,QAAA,gBACzBtE,OAAA,CAACX,IAAI;cAACwH,IAAI;cAACC,EAAE,EAAE,EAAG;cAAAxC,QAAA,eAChBtE,OAAA,CAACV,YAAY;gBACXuF,SAAS;gBACTkC,QAAQ;gBACRC,OAAO,EAAE9E,uBAAuB,CAAC8B,GAAG,CAACiD,CAAC,IAAIA,CAAC,CAACC,iBAAiB,CAAE;gBAC/DlC,KAAK,EAAEvD,QAAQ,CAACG,YAAa;gBAC7BqD,QAAQ,EAAEA,CAACkC,KAAK,EAAEC,QAAQ,KAAK;kBAC7B,MAAMxF,YAAY,GAAGM,uBAAuB,CAACmF,IAAI,CAACJ,CAAC,IAAIA,CAAC,CAACC,iBAAiB,KAAKE,QAAQ,CAAC;kBACxF,IAAIxF,YAAY,EAAE;oBAChBF,WAAW,CAAC;sBACV,GAAGD,QAAQ;sBACXG,YAAY,EAAEwF,QAAQ,IAAI,EAAE;sBAC5BvF,kBAAkB,EAAED,YAAY,CAAC0F,KAAK,IAAI,EAAE;sBAC5CxF,qBAAqB,EAAEF,YAAY,CAAC2F,QAAQ,IAAI;oBAClD,CAAC,CAAC;kBACJ,CAAC,MAAM;oBACL7F,WAAW,CAAC;sBAAE,GAAGD,QAAQ;sBAAEG,YAAY,EAAEwF,QAAQ,IAAI;oBAAG,CAAC,CAAC;kBAC5D;gBACF,CAAE;gBACFI,aAAa,EAAEA,CAACL,KAAK,EAAEM,aAAa,KAAK;kBACvC/F,WAAW,CAAC;oBAAE,GAAGD,QAAQ;oBAAEG,YAAY,EAAE6F;kBAAc,CAAC,CAAC;gBAC3D,CAAE;gBACFC,WAAW,EAAGC,MAAM,iBAClB3H,OAAA,CAAC3B,SAAS;kBAAA,GACJsJ,MAAM;kBACV5C,KAAK,EAAC,mBAAmB;kBACzBK,MAAM,EAAC,QAAQ;kBACfwC,QAAQ;kBACRC,UAAU,EAAC,0CAAuC;kBAClDC,UAAU,EAAE;oBACV,GAAGH,MAAM,CAACG,UAAU;oBACpBC,YAAY,eACV/H,OAAA,CAAAE,SAAA;sBAAAoE,QAAA,GACGlC,mBAAmB,gBAAGpC,OAAA,CAACxB,gBAAgB;wBAACqH,KAAK,EAAC,SAAS;wBAACmC,IAAI,EAAE;sBAAG;wBAAAvD,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAE,CAAC,GAAG,IAAI,EAC3E+C,MAAM,CAACG,UAAU,CAACC,YAAY;oBAAA,eAC/B;kBAEN;gBAAE;kBAAAtD,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH;cACD;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACE,CAAC,eAEP5E,OAAA,CAACX,IAAI;cAACwH,IAAI;cAACC,EAAE,EAAE,EAAG;cAACmB,EAAE,EAAE,CAAE;cAAA3D,QAAA,eACvBtE,OAAA,CAAC3B,SAAS;gBACRwG,SAAS;gBACTE,KAAK,EAAC,oBAAoB;gBAC1BmD,IAAI,EAAC,OAAO;gBACZlD,KAAK,EAAEvD,QAAQ,CAACI,kBAAmB;gBACnCoD,QAAQ,EAAGC,CAAC,IAAKxD,WAAW,CAAC;kBAAE,GAAGD,QAAQ;kBAAEI,kBAAkB,EAAEqD,CAAC,CAACC,MAAM,CAACH;gBAAM,CAAC,CAAE;gBAClFI,MAAM,EAAC,QAAQ;gBACfyC,UAAU,EAAC;cAAiC;gBAAApD,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC7C;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACE,CAAC,eAEP5E,OAAA,CAACX,IAAI;cAACwH,IAAI;cAACC,EAAE,EAAE,EAAG;cAACmB,EAAE,EAAE,CAAE;cAAA3D,QAAA,eACvBtE,OAAA,CAAC3B,SAAS;gBACRwG,SAAS;gBACTE,KAAK,EAAC,uBAAuB;gBAC7BC,KAAK,EAAEvD,QAAQ,CAACK,qBAAsB;gBACtCmD,QAAQ,EAAGC,CAAC,IAAKxD,WAAW,CAAC;kBAAE,GAAGD,QAAQ;kBAAEK,qBAAqB,EAAEoD,CAAC,CAACC,MAAM,CAACH;gBAAM,CAAC,CAAE;gBACrFI,MAAM,EAAC,QAAQ;gBACfyC,UAAU,EAAC;cAA4B;gBAAApD,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACxC;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACE,CAAC,eAEP5E,OAAA,CAACX,IAAI;cAACwH,IAAI;cAACC,EAAE,EAAE,EAAG;cAACmB,EAAE,EAAE,CAAE;cAAA3D,QAAA,eACvBtE,OAAA,CAAC3B,SAAS;gBACRwG,SAAS;gBACTC,MAAM;gBACNC,KAAK,EAAC,aAAU;gBAChBC,KAAK,EAAEvD,QAAQ,CAACO,QAAS;gBACzBiD,QAAQ,EAAGC,CAAC,IAAKxD,WAAW,CAAC;kBAAE,GAAGD,QAAQ;kBAAEO,QAAQ,EAAEkD,CAAC,CAACC,MAAM,CAACH;gBAAM,CAAC,CAAE;gBACxEI,MAAM,EAAC,QAAQ;gBAAAd,QAAA,gBAEftE,OAAA,CAAC1B,QAAQ;kBAAC0G,KAAK,EAAC,OAAO;kBAAAV,QAAA,EAAC;gBAAK;kBAAAG,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAU,CAAC,eACxC5E,OAAA,CAAC1B,QAAQ;kBAAC0G,KAAK,EAAC,SAAS;kBAAAV,QAAA,EAAC;gBAAO;kBAAAG,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAU,CAAC,eAC5C5E,OAAA,CAAC1B,QAAQ;kBAAC0G,KAAK,EAAC,MAAM;kBAAAV,QAAA,EAAC;gBAAI;kBAAAG,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAU,CAAC,eACtC5E,OAAA,CAAC1B,QAAQ;kBAAC0G,KAAK,EAAC,SAAS;kBAAAV,QAAA,EAAC;gBAAO;kBAAAG,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAU,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACnC;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACR,CAAC,eAEP5E,OAAA,CAACX,IAAI;cAACwH,IAAI;cAACC,EAAE,EAAE,EAAG;cAACmB,EAAE,EAAE,CAAE;cAAA3D,QAAA,eACvBtE,OAAA,CAAC3B,SAAS;gBACRwG,SAAS;gBACTE,KAAK,EAAC,eAAe;gBACrBmD,IAAI,EAAC,MAAM;gBACXlD,KAAK,EAAEvD,QAAQ,CAACM,aAAc;gBAC9BkD,QAAQ,EAAGC,CAAC,IAAKxD,WAAW,CAAC;kBAAE,GAAGD,QAAQ;kBAAEM,aAAa,EAAEmD,CAAC,CAACC,MAAM,CAACH;gBAAM,CAAC,CAAE;gBAC7EI,MAAM,EAAC,QAAQ;gBACf+C,eAAe,EAAE;kBAAEC,MAAM,EAAE;gBAAK;cAAE;gBAAA3D,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACnC;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACE,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC,eAEP5E,OAAA,CAAC3B,SAAS;YACRwG,SAAS;YACTE,KAAK,EAAC,aAAa;YACnBC,KAAK,EAAEvD,QAAQ,CAACE,WAAY;YAC5BsD,QAAQ,EAAGC,CAAC,IAAKxD,WAAW,CAAC;cAAE,GAAGD,QAAQ;cAAEE,WAAW,EAAEuD,CAAC,CAACC,MAAM,CAACH;YAAM,CAAC,CAAE;YAC3EI,MAAM,EAAC,QAAQ;YACfiD,SAAS;YACTC,IAAI,EAAE;UAAE;YAAA7D,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACT,CAAC,eAEF5E,OAAA,CAAC3B,SAAS;YACRwG,SAAS;YACTE,KAAK,EAAC,oBAAoB;YAC1BC,KAAK,EAAEvD,QAAQ,CAACQ,kBAAmB;YACnCgD,QAAQ,EAAGC,CAAC,IAAKxD,WAAW,CAAC;cAAE,GAAGD,QAAQ;cAAEQ,kBAAkB,EAAEiD,CAAC,CAACC,MAAM,CAACH;YAAM,CAAC,CAAE;YAClFI,MAAM,EAAC,QAAQ;YACfiD,SAAS;YACTC,IAAI,EAAE,CAAE;YACRT,UAAU,EAAC;UAA2C;YAAApD,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACvD,CAAC,eAEF5E,OAAA,CAAC3B,SAAS;YACRwG,SAAS;YACTE,KAAK,EAAC,eAAe;YACrBmD,IAAI,EAAC,MAAM;YACXlD,KAAK,EAAEvD,QAAQ,CAACM,aAAc;YAC9BkD,QAAQ,EAAGC,CAAC,IAAKxD,WAAW,CAAC;cAAE,GAAGD,QAAQ;cAAEM,aAAa,EAAEmD,CAAC,CAACC,MAAM,CAACH;YAAM,CAAC,CAAE;YAC7EI,MAAM,EAAC,QAAQ;YACf+C,eAAe,EAAE;cAAEC,MAAM,EAAE;YAAK;UAAE;YAAA3D,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACnC,CAAC,eAEF5E,OAAA,CAACzB,KAAK;YAAC8G,QAAQ,EAAC,MAAM;YAACC,EAAE,EAAE;cAAEC,EAAE,EAAE;YAAE,CAAE;YAAAjB,QAAA,gBACnCtE,OAAA,CAACjC,UAAU;cAACwG,OAAO,EAAC,WAAW;cAAAD,QAAA,EAAC;YAAU;cAAAG,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAY,CAAC,eACvD5E,OAAA,CAACjC,UAAU;cAACwG,OAAO,EAAC,OAAO;cAAAD,QAAA,GAAC,eAClB,EAACH,mBAAmB,CAAClD,WAAW,CAAC,eAACjB,OAAA;gBAAAyE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,6BAC3B,EAACvD,eAAe,CAACoB,MAAM,eAACzC,OAAA;gBAAAyE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,yBACjC,EAACnD,QAAQ,CAACG,YAAY,IAAI,iBAAiB;YAAA;cAAA6C,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACjD,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACR,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACL,CAAC;MAGV;QACE,OAAO,IAAI;IACf;EACF,CAAC;EAED,oBACE5E,OAAA,CAAC/B,MAAM;IAACoC,IAAI,EAAEA,IAAK;IAACC,OAAO,EAAE4D,WAAY;IAACqE,QAAQ,EAAC,IAAI;IAAC1D,SAAS;IAAAP,QAAA,gBAC/DtE,OAAA,CAAC9B,WAAW;MAAAoG,QAAA,eACVtE,OAAA,CAACpC,GAAG;QAAC4H,OAAO,EAAC,MAAM;QAACgD,UAAU,EAAC,QAAQ;QAACC,GAAG,EAAE,CAAE;QAAAnE,QAAA,gBAC7CtE,OAAA,CAACJ,cAAc;UAAA6E,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,+BAEpB;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAK;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACK,CAAC,eAEd5E,OAAA,CAAC7B,aAAa;MAAAmG,QAAA,eACZtE,OAAA,CAACpC,GAAG;QAAC0H,EAAE,EAAE;UAAEoD,EAAE,EAAE;QAAE,CAAE;QAAApE,QAAA,gBACjBtE,OAAA,CAACvB,OAAO;UAACkC,UAAU,EAAEA,UAAW;UAAC2E,EAAE,EAAE;YAAEK,EAAE,EAAE;UAAE,CAAE;UAAArB,QAAA,EAC5ChC,KAAK,CAAC0B,GAAG,CAAEe,KAAK,iBACf/E,OAAA,CAACtB,IAAI;YAAA4F,QAAA,eACHtE,OAAA,CAACrB,SAAS;cAAA2F,QAAA,EAAES;YAAK;cAAAN,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAY;UAAC,GADrBG,KAAK;YAAAN,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAEV,CACP;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACK,CAAC,EAET7D,KAAK,iBACJf,OAAA,CAACzB,KAAK;UAAC8G,QAAQ,EAAC,OAAO;UAACC,EAAE,EAAE;YAAEK,EAAE,EAAE;UAAE,CAAE;UAAArB,QAAA,EACnCvD;QAAK;UAAA0D,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACD,CACR,EAEAP,iBAAiB,CAAC,CAAC;MAAA;QAAAI,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACjB;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACO,CAAC,eAEhB5E,OAAA,CAAC5B,aAAa;MAAAkG,QAAA,gBACZtE,OAAA,CAAChC,MAAM;QAACmI,OAAO,EAAEjC,WAAY;QAAAI,QAAA,EAAC;MAE9B;QAAAG,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAQ,CAAC,EAERjE,UAAU,GAAG,CAAC,iBACbX,OAAA,CAAChC,MAAM;QAACmI,OAAO,EAAE/C,UAAW;QAAAkB,QAAA,EAAC;MAE7B;QAAAG,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAQ,CACT,EAEAjE,UAAU,GAAG2B,KAAK,CAACG,MAAM,GAAG,CAAC,gBAC5BzC,OAAA,CAAChC,MAAM;QACLmI,OAAO,EAAElD,UAAW;QACpBsB,OAAO,EAAC,WAAW;QACnBoE,QAAQ,EAAEhI,UAAU,KAAK,CAAC,IAAIY,WAAY;QAAA+C,QAAA,EAC3C;MAED;QAAAG,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAQ,CAAC,gBAET5E,OAAA,CAAChC,MAAM;QACLmI,OAAO,EAAEvC,YAAa;QACtBW,OAAO,EAAC,WAAW;QACnBoE,QAAQ,EAAE9H,OAAO,IAAIQ,eAAe,CAACoB,MAAM,KAAK,CAAC,IAAI,CAAChB,QAAQ,CAACG,YAAY,CAACsB,IAAI,CAAC,CAAE;QACnF0F,SAAS,EAAE/H,OAAO,gBAAGb,OAAA,CAACxB,gBAAgB;UAACwJ,IAAI,EAAE;QAAG;UAAAvD,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,gBAAG5E,OAAA,CAACR,OAAO;UAAAiF,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAE;QAAAN,QAAA,EAEjEzD,OAAO,GAAG,cAAc,GAAG;MAAc;QAAA4D,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACpC,CACT;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACY,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACV,CAAC;AAEb,CAAC;AAAClE,EAAA,CAteIP,kBAAkB;AAAA0I,EAAA,GAAlB1I,kBAAkB;AAwexB,eAAeA,kBAAkB;AAAC,IAAA0I,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}