{"ast": null, "code": "var baseIsNative = require('./_baseIsNative'),\n  getValue = require('./_getValue');\n\n/**\n * Gets the native function at `key` of `object`.\n *\n * @private\n * @param {Object} object The object to query.\n * @param {string} key The key of the method to get.\n * @returns {*} Returns the function if it's native, else `undefined`.\n */\nfunction getNative(object, key) {\n  var value = getValue(object, key);\n  return baseIsNative(value) ? value : undefined;\n}\nmodule.exports = getNative;", "map": {"version": 3, "names": ["baseIsNative", "require", "getValue", "getNative", "object", "key", "value", "undefined", "module", "exports"], "sources": ["C:/Users/<USER>/PycharmProjects/CMS/webapp/frontend/node_modules/lodash/_getNative.js"], "sourcesContent": ["var baseIsNative = require('./_baseIsNative'),\n    getValue = require('./_getValue');\n\n/**\n * Gets the native function at `key` of `object`.\n *\n * @private\n * @param {Object} object The object to query.\n * @param {string} key The key of the method to get.\n * @returns {*} Returns the function if it's native, else `undefined`.\n */\nfunction getNative(object, key) {\n  var value = getValue(object, key);\n  return baseIsNative(value) ? value : undefined;\n}\n\nmodule.exports = getNative;\n"], "mappings": "AAAA,IAAIA,YAAY,GAAGC,OAAO,CAAC,iBAAiB,CAAC;EACzCC,QAAQ,GAAGD,OAAO,CAAC,aAAa,CAAC;;AAErC;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASE,SAASA,CAACC,MAAM,EAAEC,GAAG,EAAE;EAC9B,IAAIC,KAAK,GAAGJ,QAAQ,CAACE,MAAM,EAAEC,GAAG,CAAC;EACjC,OAAOL,YAAY,CAACM,KAAK,CAAC,GAAGA,KAAK,GAAGC,SAAS;AAChD;AAEAC,MAAM,CAACC,OAAO,GAAGN,SAAS", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}