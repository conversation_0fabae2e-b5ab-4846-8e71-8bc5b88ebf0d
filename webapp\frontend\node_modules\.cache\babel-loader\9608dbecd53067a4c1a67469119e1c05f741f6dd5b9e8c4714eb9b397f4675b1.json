{"ast": null, "code": "var _jsxFileName = \"C:\\\\CMS\\\\webapp\\\\frontend\\\\src\\\\components\\\\comande\\\\ComandeListTable.js\",\n  _s = $RefreshSig$();\nimport React, { useState } from 'react';\nimport { Box, Table, TableBody, TableCell, TableContainer, TableHead, TableRow, Paper, Chip, IconButton, Tooltip, Typography, TextField, InputAdornment, Stack } from '@mui/material';\nimport { Edit as EditIcon, Delete as DeleteIcon, Search as SearchIcon, Construction as ConstructionIcon, Print as PrintIcon } from '@mui/icons-material';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst ComandeListTable = ({\n  comande,\n  onEditComanda,\n  onDeleteComanda,\n  onInserimentoMetri,\n  onPrintComanda,\n  loading = false\n}) => {\n  _s();\n  const [searchTerm, setSearchTerm] = useState('');\n\n  // Funzione per ottenere il colore del chip in base al tipo di comanda\n  const getTipoComandaColor = tipo => {\n    const colors = {\n      'POSA': 'primary',\n      'COLLEGAMENTO_PARTENZA': 'secondary',\n      'COLLEGAMENTO_ARRIVO': 'info',\n      'CERTIFICAZIONE': 'success',\n      'TESTING': 'warning'\n    };\n    return colors[tipo] || 'default';\n  };\n\n  // Funzione per ottenere il colore del chip in base allo stato\n  const getStatoColor = stato => {\n    const colors = {\n      'CREATA': 'default',\n      'ASSEGNATA': 'info',\n      'IN_CORSO': 'warning',\n      'COMPLETATA': 'success',\n      'ANNULLATA': 'error'\n    };\n    return colors[stato] || 'default';\n  };\n\n  // Funzione per ottenere l'etichetta del tipo di comanda\n  const getTipoComandaLabel = tipo => {\n    const labels = {\n      'POSA': 'Posa',\n      'COLLEGAMENTO_PARTENZA': 'Coll. Partenza',\n      'COLLEGAMENTO_ARRIVO': 'Coll. Arrivo',\n      'CERTIFICAZIONE': 'Certificazione',\n      'TESTING': 'Testing'\n    };\n    return labels[tipo] || tipo;\n  };\n\n  // Assicurati che comande sia sempre un array\n  const comandeArray = Array.isArray(comande) ? comande : [];\n\n  // Filtro intelligente per la ricerca\n  const filteredComande = comandeArray.filter(comanda => {\n    var _comanda$codice_coman, _comanda$responsabile, _comanda$tipo_comanda, _comanda$stato, _comanda$descrizione, _getTipoComandaLabel;\n    if (!searchTerm) return true;\n    const searchLower = searchTerm.toLowerCase();\n    return ((_comanda$codice_coman = comanda.codice_comanda) === null || _comanda$codice_coman === void 0 ? void 0 : _comanda$codice_coman.toLowerCase().includes(searchLower)) || ((_comanda$responsabile = comanda.responsabile) === null || _comanda$responsabile === void 0 ? void 0 : _comanda$responsabile.toLowerCase().includes(searchLower)) || ((_comanda$tipo_comanda = comanda.tipo_comanda) === null || _comanda$tipo_comanda === void 0 ? void 0 : _comanda$tipo_comanda.toLowerCase().includes(searchLower)) || ((_comanda$stato = comanda.stato) === null || _comanda$stato === void 0 ? void 0 : _comanda$stato.toLowerCase().includes(searchLower)) || ((_comanda$descrizione = comanda.descrizione) === null || _comanda$descrizione === void 0 ? void 0 : _comanda$descrizione.toLowerCase().includes(searchLower)) || ((_getTipoComandaLabel = getTipoComandaLabel(comanda.tipo_comanda)) === null || _getTipoComandaLabel === void 0 ? void 0 : _getTipoComandaLabel.toLowerCase().includes(searchLower));\n  });\n  return /*#__PURE__*/_jsxDEV(Box, {\n    children: [/*#__PURE__*/_jsxDEV(Box, {\n      mb: 3,\n      children: /*#__PURE__*/_jsxDEV(TextField, {\n        fullWidth: true,\n        placeholder: \"Cerca per codice, responsabile, tipo, stato o descrizione...\",\n        value: searchTerm,\n        onChange: e => setSearchTerm(e.target.value),\n        InputProps: {\n          startAdornment: /*#__PURE__*/_jsxDEV(InputAdornment, {\n            position: \"start\",\n            children: /*#__PURE__*/_jsxDEV(SearchIcon, {\n              color: \"action\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 105,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 104,\n            columnNumber: 15\n          }, this)\n        },\n        sx: {\n          '& .MuiOutlinedInput-root': {\n            backgroundColor: '#f5f7fa',\n            '&:hover': {\n              backgroundColor: 'rgba(33, 150, 243, 0.1)'\n            },\n            '&.Mui-focused': {\n              backgroundColor: 'white'\n            }\n          }\n        }\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 97,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 96,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(TableContainer, {\n      component: Paper,\n      elevation: 0,\n      sx: {\n        border: '1px solid',\n        borderColor: 'grey.200',\n        borderRadius: 2\n      },\n      children: /*#__PURE__*/_jsxDEV(Table, {\n        children: [/*#__PURE__*/_jsxDEV(TableHead, {\n          children: /*#__PURE__*/_jsxDEV(TableRow, {\n            sx: {\n              backgroundColor: 'grey.50'\n            },\n            children: [/*#__PURE__*/_jsxDEV(TableCell, {\n              sx: {\n                fontWeight: 600\n              },\n              children: \"Codice\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 136,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n              sx: {\n                fontWeight: 600\n              },\n              children: \"Tipo\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 137,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n              sx: {\n                fontWeight: 600\n              },\n              children: \"Responsabile\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 138,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n              sx: {\n                fontWeight: 600\n              },\n              children: \"Contatti\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 139,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n              sx: {\n                fontWeight: 600\n              },\n              children: \"Stato\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 140,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n              sx: {\n                fontWeight: 600\n              },\n              children: \"Data Creazione\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 141,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n              sx: {\n                fontWeight: 600,\n                textAlign: 'center'\n              },\n              children: \"Cavi\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 142,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n              sx: {\n                fontWeight: 600,\n                textAlign: 'center'\n              },\n              children: \"Completamento\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 143,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n              sx: {\n                fontWeight: 600,\n                textAlign: 'center'\n              },\n              children: \"Azioni\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 144,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 135,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 134,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(TableBody, {\n          children: filteredComande.length === 0 ? /*#__PURE__*/_jsxDEV(TableRow, {\n            children: /*#__PURE__*/_jsxDEV(TableCell, {\n              colSpan: 9,\n              sx: {\n                textAlign: 'center',\n                py: 4\n              },\n              children: /*#__PURE__*/_jsxDEV(Typography, {\n                variant: \"body2\",\n                color: \"text.secondary\",\n                children: searchTerm ? 'Nessuna comanda trovata per la ricerca' : 'Nessuna comanda disponibile'\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 151,\n                columnNumber: 19\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 150,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 149,\n            columnNumber: 15\n          }, this) : filteredComande.map(comanda => /*#__PURE__*/_jsxDEV(TableRow, {\n            sx: {\n              '&:hover': {\n                backgroundColor: 'rgba(33, 150, 243, 0.1)'\n              }\n            },\n            children: [/*#__PURE__*/_jsxDEV(TableCell, {\n              children: /*#__PURE__*/_jsxDEV(Typography, {\n                variant: \"body2\",\n                fontWeight: \"bold\",\n                children: comanda.codice_comanda\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 167,\n                columnNumber: 21\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 166,\n              columnNumber: 19\n            }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n              children: /*#__PURE__*/_jsxDEV(Chip, {\n                label: getTipoComandaLabel(comanda.tipo_comanda),\n                size: \"small\",\n                color: getTipoComandaColor(comanda.tipo_comanda),\n                variant: \"outlined\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 172,\n                columnNumber: 21\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 171,\n              columnNumber: 19\n            }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n              children: /*#__PURE__*/_jsxDEV(Typography, {\n                variant: \"body2\",\n                sx: {\n                  fontWeight: 500\n                },\n                children: comanda.responsabile || 'Non assegnato'\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 180,\n                columnNumber: 21\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 179,\n              columnNumber: 19\n            }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n              children: /*#__PURE__*/_jsxDEV(Box, {\n                children: [comanda.responsabile_telefono && /*#__PURE__*/_jsxDEV(Typography, {\n                  variant: \"body2\",\n                  color: \"text.secondary\",\n                  sx: {\n                    fontSize: '0.875rem'\n                  },\n                  children: [\"\\uD83D\\uDCDE \", comanda.responsabile_telefono]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 187,\n                  columnNumber: 25\n                }, this), comanda.responsabile_email && /*#__PURE__*/_jsxDEV(Typography, {\n                  variant: \"body2\",\n                  color: \"text.secondary\",\n                  sx: {\n                    fontSize: '0.875rem'\n                  },\n                  children: [\"\\u2709\\uFE0F \", comanda.responsabile_email]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 192,\n                  columnNumber: 25\n                }, this), !comanda.responsabile_telefono && !comanda.responsabile_email && /*#__PURE__*/_jsxDEV(Typography, {\n                  variant: \"body2\",\n                  color: \"text.disabled\",\n                  sx: {\n                    fontSize: '0.875rem'\n                  },\n                  children: \"Nessun contatto\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 197,\n                  columnNumber: 25\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 185,\n                columnNumber: 21\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 184,\n              columnNumber: 19\n            }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n              children: /*#__PURE__*/_jsxDEV(Chip, {\n                label: comanda.stato || 'CREATA',\n                size: \"small\",\n                color: getStatoColor(comanda.stato)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 204,\n                columnNumber: 21\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 203,\n              columnNumber: 19\n            }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n              children: /*#__PURE__*/_jsxDEV(Typography, {\n                variant: \"body2\",\n                color: \"text.secondary\",\n                children: comanda.data_creazione ? new Date(comanda.data_creazione).toLocaleDateString('it-IT') : '-'\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 211,\n                columnNumber: 21\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 210,\n              columnNumber: 19\n            }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n              sx: {\n                textAlign: 'center'\n              },\n              children: /*#__PURE__*/_jsxDEV(Typography, {\n                variant: \"body2\",\n                fontWeight: \"bold\",\n                color: \"primary\",\n                children: comanda.numero_cavi_assegnati || 0\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 219,\n                columnNumber: 21\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 218,\n              columnNumber: 19\n            }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n              sx: {\n                textAlign: 'center'\n              },\n              children: /*#__PURE__*/_jsxDEV(Typography, {\n                variant: \"body2\",\n                fontWeight: \"bold\",\n                children: [(comanda.percentuale_completamento || 0).toFixed(1), \"%\"]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 224,\n                columnNumber: 21\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 223,\n              columnNumber: 19\n            }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n              sx: {\n                textAlign: 'center'\n              },\n              children: /*#__PURE__*/_jsxDEV(Stack, {\n                direction: \"row\",\n                spacing: 0.5,\n                justifyContent: \"center\",\n                children: [['POSA', 'COLLEGAMENTO_PARTENZA', 'COLLEGAMENTO_ARRIVO'].includes(comanda.tipo_comanda) && onInserimentoMetri && /*#__PURE__*/_jsxDEV(Tooltip, {\n                  title: comanda.tipo_comanda === 'POSA' ? 'Inserisci Metri Posati' : comanda.tipo_comanda === 'COLLEGAMENTO_PARTENZA' ? 'Gestisci Collegamento Partenza' : 'Gestisci Collegamento Arrivo',\n                  children: /*#__PURE__*/_jsxDEV(IconButton, {\n                    size: \"small\",\n                    onClick: () => onInserimentoMetri(comanda),\n                    sx: {\n                      color: '#2196f3',\n                      '&:hover': {\n                        backgroundColor: 'rgba(33, 150, 243, 0.1)'\n                      }\n                    },\n                    children: /*#__PURE__*/_jsxDEV(ConstructionIcon, {\n                      fontSize: \"small\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 247,\n                      columnNumber: 29\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 237,\n                    columnNumber: 27\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 232,\n                  columnNumber: 25\n                }, this), /*#__PURE__*/_jsxDEV(Tooltip, {\n                  title: \"Stampa Comanda\",\n                  children: /*#__PURE__*/_jsxDEV(IconButton, {\n                    size: \"small\",\n                    onClick: () => onPrintComanda && onPrintComanda(comanda),\n                    sx: {\n                      color: '#4caf50',\n                      '&:hover': {\n                        backgroundColor: 'rgba(76, 175, 80, 0.1)'\n                      }\n                    },\n                    children: /*#__PURE__*/_jsxDEV(PrintIcon, {\n                      fontSize: \"small\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 263,\n                      columnNumber: 27\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 253,\n                    columnNumber: 25\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 252,\n                  columnNumber: 23\n                }, this), /*#__PURE__*/_jsxDEV(Tooltip, {\n                  title: \"Modifica\",\n                  children: /*#__PURE__*/_jsxDEV(IconButton, {\n                    size: \"small\",\n                    onClick: () => onEditComanda(comanda),\n                    sx: {\n                      color: '#6c757d',\n                      '&:hover': {\n                        backgroundColor: '#e9ecef'\n                      }\n                    },\n                    children: /*#__PURE__*/_jsxDEV(EditIcon, {\n                      fontSize: \"small\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 278,\n                      columnNumber: 27\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 268,\n                    columnNumber: 25\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 267,\n                  columnNumber: 23\n                }, this), /*#__PURE__*/_jsxDEV(Tooltip, {\n                  title: \"Elimina\",\n                  children: /*#__PURE__*/_jsxDEV(IconButton, {\n                    size: \"small\",\n                    onClick: () => onDeleteComanda(comanda.codice_comanda),\n                    sx: {\n                      color: '#6c757d',\n                      '&:hover': {\n                        backgroundColor: '#e9ecef'\n                      }\n                    },\n                    children: /*#__PURE__*/_jsxDEV(DeleteIcon, {\n                      fontSize: \"small\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 292,\n                      columnNumber: 27\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 282,\n                    columnNumber: 25\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 281,\n                  columnNumber: 23\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 229,\n                columnNumber: 21\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 228,\n              columnNumber: 19\n            }, this)]\n          }, comanda.codice_comanda, true, {\n            fileName: _jsxFileName,\n            lineNumber: 158,\n            columnNumber: 17\n          }, this))\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 147,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 133,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 124,\n      columnNumber: 7\n    }, this), searchTerm && /*#__PURE__*/_jsxDEV(Box, {\n      mt: 2,\n      children: /*#__PURE__*/_jsxDEV(Typography, {\n        variant: \"caption\",\n        color: \"text.secondary\",\n        children: [\"Mostrando \", filteredComande.length, \" di \", comandeArray.length, \" comande\"]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 307,\n        columnNumber: 11\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 306,\n      columnNumber: 9\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 94,\n    columnNumber: 5\n  }, this);\n};\n_s(ComandeListTable, \"a1cMJ8t0eYFnsCEdGcHtaGJdbCM=\");\n_c = ComandeListTable;\nexport default ComandeListTable;\nvar _c;\n$RefreshReg$(_c, \"ComandeListTable\");", "map": {"version": 3, "names": ["React", "useState", "Box", "Table", "TableBody", "TableCell", "TableContainer", "TableHead", "TableRow", "Paper", "Chip", "IconButton", "<PERSON><PERSON><PERSON>", "Typography", "TextField", "InputAdornment", "<PERSON><PERSON>", "Edit", "EditIcon", "Delete", "DeleteIcon", "Search", "SearchIcon", "Construction", "ConstructionIcon", "Print", "PrintIcon", "jsxDEV", "_jsxDEV", "ComandeListTable", "comande", "onEditComanda", "onDeleteComanda", "onInserimentoMetri", "onPrintComanda", "loading", "_s", "searchTerm", "setSearchTerm", "getTipoComandaColor", "tipo", "colors", "getStatoColor", "stato", "getTipoComandaLabel", "labels", "comandeArray", "Array", "isArray", "filteredComande", "filter", "comanda", "_comanda$codice_coman", "_comanda$responsabile", "_comanda$tipo_comanda", "_comanda$stato", "_comanda$descrizione", "_getTipoComandaLabel", "searchLower", "toLowerCase", "codice_comanda", "includes", "responsabile", "tipo_comanda", "descrizione", "children", "mb", "fullWidth", "placeholder", "value", "onChange", "e", "target", "InputProps", "startAdornment", "position", "color", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "sx", "backgroundColor", "component", "elevation", "border", "borderColor", "borderRadius", "fontWeight", "textAlign", "length", "colSpan", "py", "variant", "map", "label", "size", "responsabile_telefono", "fontSize", "responsabile_email", "data_creazione", "Date", "toLocaleDateString", "numero_cavi_assegnati", "percentuale_completamento", "toFixed", "direction", "spacing", "justifyContent", "title", "onClick", "mt", "_c", "$RefreshReg$"], "sources": ["C:/CMS/webapp/frontend/src/components/comande/ComandeListTable.js"], "sourcesContent": ["import React, { useState } from 'react';\nimport {\n  Box,\n  Table,\n  TableBody,\n  TableCell,\n  TableContainer,\n  TableHead,\n  TableRow,\n  Paper,\n  Chip,\n  IconButton,\n  Tooltip,\n  Typography,\n  TextField,\n  InputAdornment,\n  Stack\n} from '@mui/material';\nimport {\n  Edit as EditIcon,\n  Delete as DeleteIcon,\n  Search as SearchIcon,\n  Construction as ConstructionIcon,\n  Print as PrintIcon\n} from '@mui/icons-material';\n\nconst ComandeListTable = ({\n  comande,\n  onEditComanda,\n  onDeleteComanda,\n  onInserimentoMetri,\n  onPrintComanda,\n  loading = false\n}) => {\n  const [searchTerm, setSearchTerm] = useState('');\n\n  // Funzione per ottenere il colore del chip in base al tipo di comanda\n  const getTipoComandaColor = (tipo) => {\n    const colors = {\n      'POSA': 'primary',\n      'COLLEGAMENTO_PARTENZA': 'secondary',\n      'COLLEGAMENTO_ARRIVO': 'info',\n      'CERTIFICAZIONE': 'success',\n      'TESTING': 'warning'\n    };\n    return colors[tipo] || 'default';\n  };\n\n  // Funzione per ottenere il colore del chip in base allo stato\n  const getStatoColor = (stato) => {\n    const colors = {\n      'CREATA': 'default',\n      'ASSEGNATA': 'info',\n      'IN_CORSO': 'warning',\n      'COMPLETATA': 'success',\n      'ANNULLATA': 'error'\n    };\n    return colors[stato] || 'default';\n  };\n\n\n\n  // Funzione per ottenere l'etichetta del tipo di comanda\n  const getTipoComandaLabel = (tipo) => {\n    const labels = {\n      'POSA': 'Posa',\n      'COLLEGAMENTO_PARTENZA': 'Coll. Partenza',\n      'COLLEGAMENTO_ARRIVO': 'Coll. Arrivo',\n      'CERTIFICAZIONE': 'Certificazione',\n      'TESTING': 'Testing'\n    };\n    return labels[tipo] || tipo;\n  };\n\n  // Assicurati che comande sia sempre un array\n  const comandeArray = Array.isArray(comande) ? comande : [];\n\n  // Filtro intelligente per la ricerca\n  const filteredComande = comandeArray.filter(comanda => {\n    if (!searchTerm) return true;\n\n    const searchLower = searchTerm.toLowerCase();\n    return (\n      comanda.codice_comanda?.toLowerCase().includes(searchLower) ||\n      comanda.responsabile?.toLowerCase().includes(searchLower) ||\n      comanda.tipo_comanda?.toLowerCase().includes(searchLower) ||\n      comanda.stato?.toLowerCase().includes(searchLower) ||\n      comanda.descrizione?.toLowerCase().includes(searchLower) ||\n      getTipoComandaLabel(comanda.tipo_comanda)?.toLowerCase().includes(searchLower)\n    );\n  });\n\n  return (\n    <Box>\n      {/* Campo di ricerca */}\n      <Box mb={3}>\n        <TextField\n          fullWidth\n          placeholder=\"Cerca per codice, responsabile, tipo, stato o descrizione...\"\n          value={searchTerm}\n          onChange={(e) => setSearchTerm(e.target.value)}\n          InputProps={{\n            startAdornment: (\n              <InputAdornment position=\"start\">\n                <SearchIcon color=\"action\" />\n              </InputAdornment>\n            ),\n          }}\n          sx={{\n            '& .MuiOutlinedInput-root': {\n              backgroundColor: '#f5f7fa',\n              '&:hover': {\n                backgroundColor: 'rgba(33, 150, 243, 0.1)',\n              },\n              '&.Mui-focused': {\n                backgroundColor: 'white',\n              }\n            }\n          }}\n        />\n      </Box>\n\n      {/* Tabella comande */}\n      <TableContainer \n        component={Paper} \n        elevation={0} \n        sx={{ \n          border: '1px solid', \n          borderColor: 'grey.200',\n          borderRadius: 2\n        }}\n      >\n        <Table>\n          <TableHead>\n            <TableRow sx={{ backgroundColor: 'grey.50' }}>\n              <TableCell sx={{ fontWeight: 600 }}>Codice</TableCell>\n              <TableCell sx={{ fontWeight: 600 }}>Tipo</TableCell>\n              <TableCell sx={{ fontWeight: 600 }}>Responsabile</TableCell>\n              <TableCell sx={{ fontWeight: 600 }}>Contatti</TableCell>\n              <TableCell sx={{ fontWeight: 600 }}>Stato</TableCell>\n              <TableCell sx={{ fontWeight: 600 }}>Data Creazione</TableCell>\n              <TableCell sx={{ fontWeight: 600, textAlign: 'center' }}>Cavi</TableCell>\n              <TableCell sx={{ fontWeight: 600, textAlign: 'center' }}>Completamento</TableCell>\n              <TableCell sx={{ fontWeight: 600, textAlign: 'center' }}>Azioni</TableCell>\n            </TableRow>\n          </TableHead>\n          <TableBody>\n            {filteredComande.length === 0 ? (\n              <TableRow>\n                <TableCell colSpan={9} sx={{ textAlign: 'center', py: 4 }}>\n                  <Typography variant=\"body2\" color=\"text.secondary\">\n                    {searchTerm ? 'Nessuna comanda trovata per la ricerca' : 'Nessuna comanda disponibile'}\n                  </Typography>\n                </TableCell>\n              </TableRow>\n            ) : (\n              filteredComande.map((comanda) => (\n                <TableRow \n                  key={comanda.codice_comanda}\n                  sx={{ \n                    '&:hover': { \n                      backgroundColor: 'rgba(33, 150, 243, 0.1)' \n                    } \n                  }}\n                >\n                  <TableCell>\n                    <Typography variant=\"body2\" fontWeight=\"bold\">\n                      {comanda.codice_comanda}\n                    </Typography>\n                  </TableCell>\n                  <TableCell>\n                    <Chip\n                      label={getTipoComandaLabel(comanda.tipo_comanda)}\n                      size=\"small\"\n                      color={getTipoComandaColor(comanda.tipo_comanda)}\n                      variant=\"outlined\"\n                    />\n                  </TableCell>\n                  <TableCell>\n                    <Typography variant=\"body2\" sx={{ fontWeight: 500 }}>\n                      {comanda.responsabile || 'Non assegnato'}\n                    </Typography>\n                  </TableCell>\n                  <TableCell>\n                    <Box>\n                      {comanda.responsabile_telefono && (\n                        <Typography variant=\"body2\" color=\"text.secondary\" sx={{ fontSize: '0.875rem' }}>\n                          📞 {comanda.responsabile_telefono}\n                        </Typography>\n                      )}\n                      {comanda.responsabile_email && (\n                        <Typography variant=\"body2\" color=\"text.secondary\" sx={{ fontSize: '0.875rem' }}>\n                          ✉️ {comanda.responsabile_email}\n                        </Typography>\n                      )}\n                      {!comanda.responsabile_telefono && !comanda.responsabile_email && (\n                        <Typography variant=\"body2\" color=\"text.disabled\" sx={{ fontSize: '0.875rem' }}>\n                          Nessun contatto\n                        </Typography>\n                      )}\n                    </Box>\n                  </TableCell>\n                  <TableCell>\n                    <Chip\n                      label={comanda.stato || 'CREATA'}\n                      size=\"small\"\n                      color={getStatoColor(comanda.stato)}\n                    />\n                  </TableCell>\n                  <TableCell>\n                    <Typography variant=\"body2\" color=\"text.secondary\">\n                      {comanda.data_creazione ? \n                        new Date(comanda.data_creazione).toLocaleDateString('it-IT') : \n                        '-'\n                      }\n                    </Typography>\n                  </TableCell>\n                  <TableCell sx={{ textAlign: 'center' }}>\n                    <Typography variant=\"body2\" fontWeight=\"bold\" color=\"primary\">\n                      {comanda.numero_cavi_assegnati || 0}\n                    </Typography>\n                  </TableCell>\n                  <TableCell sx={{ textAlign: 'center' }}>\n                    <Typography variant=\"body2\" fontWeight=\"bold\">\n                      {(comanda.percentuale_completamento || 0).toFixed(1)}%\n                    </Typography>\n                  </TableCell>\n                  <TableCell sx={{ textAlign: 'center' }}>\n                    <Stack direction=\"row\" spacing={0.5} justifyContent=\"center\">\n                      {/* Pulsante Gestione Workflow - per comande POSA, COLLEGAMENTO_PARTENZA, COLLEGAMENTO_ARRIVO */}\n                      {['POSA', 'COLLEGAMENTO_PARTENZA', 'COLLEGAMENTO_ARRIVO'].includes(comanda.tipo_comanda) && onInserimentoMetri && (\n                        <Tooltip title={\n                          comanda.tipo_comanda === 'POSA' ? 'Inserisci Metri Posati' :\n                          comanda.tipo_comanda === 'COLLEGAMENTO_PARTENZA' ? 'Gestisci Collegamento Partenza' :\n                          'Gestisci Collegamento Arrivo'\n                        }>\n                          <IconButton\n                            size=\"small\"\n                            onClick={() => onInserimentoMetri(comanda)}\n                            sx={{\n                              color: '#2196f3',\n                              '&:hover': {\n                                backgroundColor: 'rgba(33, 150, 243, 0.1)'\n                              }\n                            }}\n                          >\n                            <ConstructionIcon fontSize=\"small\" />\n                          </IconButton>\n                        </Tooltip>\n                      )}\n\n                      <Tooltip title=\"Stampa Comanda\">\n                        <IconButton\n                          size=\"small\"\n                          onClick={() => onPrintComanda && onPrintComanda(comanda)}\n                          sx={{\n                            color: '#4caf50',\n                            '&:hover': {\n                              backgroundColor: 'rgba(76, 175, 80, 0.1)'\n                            }\n                          }}\n                        >\n                          <PrintIcon fontSize=\"small\" />\n                        </IconButton>\n                      </Tooltip>\n\n                      <Tooltip title=\"Modifica\">\n                        <IconButton\n                          size=\"small\"\n                          onClick={() => onEditComanda(comanda)}\n                          sx={{\n                            color: '#6c757d',\n                            '&:hover': {\n                              backgroundColor: '#e9ecef'\n                            }\n                          }}\n                        >\n                          <EditIcon fontSize=\"small\" />\n                        </IconButton>\n                      </Tooltip>\n                      <Tooltip title=\"Elimina\">\n                        <IconButton\n                          size=\"small\"\n                          onClick={() => onDeleteComanda(comanda.codice_comanda)}\n                          sx={{\n                            color: '#6c757d',\n                            '&:hover': {\n                              backgroundColor: '#e9ecef'\n                            }\n                          }}\n                        >\n                          <DeleteIcon fontSize=\"small\" />\n                        </IconButton>\n                      </Tooltip>\n                    </Stack>\n                  </TableCell>\n                </TableRow>\n              ))\n            )}\n          </TableBody>\n        </Table>\n      </TableContainer>\n\n      {/* Informazioni sui risultati della ricerca */}\n      {searchTerm && (\n        <Box mt={2}>\n          <Typography variant=\"caption\" color=\"text.secondary\">\n            Mostrando {filteredComande.length} di {comandeArray.length} comande\n          </Typography>\n        </Box>\n      )}\n    </Box>\n  );\n};\n\nexport default ComandeListTable;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,QAAQ,OAAO;AACvC,SACEC,GAAG,EACHC,KAAK,EACLC,SAAS,EACTC,SAAS,EACTC,cAAc,EACdC,SAAS,EACTC,QAAQ,EACRC,KAAK,EACLC,IAAI,EACJC,UAAU,EACVC,OAAO,EACPC,UAAU,EACVC,SAAS,EACTC,cAAc,EACdC,KAAK,QACA,eAAe;AACtB,SACEC,IAAI,IAAIC,QAAQ,EAChBC,MAAM,IAAIC,UAAU,EACpBC,MAAM,IAAIC,UAAU,EACpBC,YAAY,IAAIC,gBAAgB,EAChCC,KAAK,IAAIC,SAAS,QACb,qBAAqB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAE7B,MAAMC,gBAAgB,GAAGA,CAAC;EACxBC,OAAO;EACPC,aAAa;EACbC,eAAe;EACfC,kBAAkB;EAClBC,cAAc;EACdC,OAAO,GAAG;AACZ,CAAC,KAAK;EAAAC,EAAA;EACJ,MAAM,CAACC,UAAU,EAAEC,aAAa,CAAC,GAAGrC,QAAQ,CAAC,EAAE,CAAC;;EAEhD;EACA,MAAMsC,mBAAmB,GAAIC,IAAI,IAAK;IACpC,MAAMC,MAAM,GAAG;MACb,MAAM,EAAE,SAAS;MACjB,uBAAuB,EAAE,WAAW;MACpC,qBAAqB,EAAE,MAAM;MAC7B,gBAAgB,EAAE,SAAS;MAC3B,SAAS,EAAE;IACb,CAAC;IACD,OAAOA,MAAM,CAACD,IAAI,CAAC,IAAI,SAAS;EAClC,CAAC;;EAED;EACA,MAAME,aAAa,GAAIC,KAAK,IAAK;IAC/B,MAAMF,MAAM,GAAG;MACb,QAAQ,EAAE,SAAS;MACnB,WAAW,EAAE,MAAM;MACnB,UAAU,EAAE,SAAS;MACrB,YAAY,EAAE,SAAS;MACvB,WAAW,EAAE;IACf,CAAC;IACD,OAAOA,MAAM,CAACE,KAAK,CAAC,IAAI,SAAS;EACnC,CAAC;;EAID;EACA,MAAMC,mBAAmB,GAAIJ,IAAI,IAAK;IACpC,MAAMK,MAAM,GAAG;MACb,MAAM,EAAE,MAAM;MACd,uBAAuB,EAAE,gBAAgB;MACzC,qBAAqB,EAAE,cAAc;MACrC,gBAAgB,EAAE,gBAAgB;MAClC,SAAS,EAAE;IACb,CAAC;IACD,OAAOA,MAAM,CAACL,IAAI,CAAC,IAAIA,IAAI;EAC7B,CAAC;;EAED;EACA,MAAMM,YAAY,GAAGC,KAAK,CAACC,OAAO,CAAClB,OAAO,CAAC,GAAGA,OAAO,GAAG,EAAE;;EAE1D;EACA,MAAMmB,eAAe,GAAGH,YAAY,CAACI,MAAM,CAACC,OAAO,IAAI;IAAA,IAAAC,qBAAA,EAAAC,qBAAA,EAAAC,qBAAA,EAAAC,cAAA,EAAAC,oBAAA,EAAAC,oBAAA;IACrD,IAAI,CAACpB,UAAU,EAAE,OAAO,IAAI;IAE5B,MAAMqB,WAAW,GAAGrB,UAAU,CAACsB,WAAW,CAAC,CAAC;IAC5C,OACE,EAAAP,qBAAA,GAAAD,OAAO,CAACS,cAAc,cAAAR,qBAAA,uBAAtBA,qBAAA,CAAwBO,WAAW,CAAC,CAAC,CAACE,QAAQ,CAACH,WAAW,CAAC,OAAAL,qBAAA,GAC3DF,OAAO,CAACW,YAAY,cAAAT,qBAAA,uBAApBA,qBAAA,CAAsBM,WAAW,CAAC,CAAC,CAACE,QAAQ,CAACH,WAAW,CAAC,OAAAJ,qBAAA,GACzDH,OAAO,CAACY,YAAY,cAAAT,qBAAA,uBAApBA,qBAAA,CAAsBK,WAAW,CAAC,CAAC,CAACE,QAAQ,CAACH,WAAW,CAAC,OAAAH,cAAA,GACzDJ,OAAO,CAACR,KAAK,cAAAY,cAAA,uBAAbA,cAAA,CAAeI,WAAW,CAAC,CAAC,CAACE,QAAQ,CAACH,WAAW,CAAC,OAAAF,oBAAA,GAClDL,OAAO,CAACa,WAAW,cAAAR,oBAAA,uBAAnBA,oBAAA,CAAqBG,WAAW,CAAC,CAAC,CAACE,QAAQ,CAACH,WAAW,CAAC,OAAAD,oBAAA,GACxDb,mBAAmB,CAACO,OAAO,CAACY,YAAY,CAAC,cAAAN,oBAAA,uBAAzCA,oBAAA,CAA2CE,WAAW,CAAC,CAAC,CAACE,QAAQ,CAACH,WAAW,CAAC;EAElF,CAAC,CAAC;EAEF,oBACE9B,OAAA,CAAC1B,GAAG;IAAA+D,QAAA,gBAEFrC,OAAA,CAAC1B,GAAG;MAACgE,EAAE,EAAE,CAAE;MAAAD,QAAA,eACTrC,OAAA,CAACd,SAAS;QACRqD,SAAS;QACTC,WAAW,EAAC,8DAA8D;QAC1EC,KAAK,EAAEhC,UAAW;QAClBiC,QAAQ,EAAGC,CAAC,IAAKjC,aAAa,CAACiC,CAAC,CAACC,MAAM,CAACH,KAAK,CAAE;QAC/CI,UAAU,EAAE;UACVC,cAAc,eACZ9C,OAAA,CAACb,cAAc;YAAC4D,QAAQ,EAAC,OAAO;YAAAV,QAAA,eAC9BrC,OAAA,CAACN,UAAU;cAACsD,KAAK,EAAC;YAAQ;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACf;QAEpB,CAAE;QACFC,EAAE,EAAE;UACF,0BAA0B,EAAE;YAC1BC,eAAe,EAAE,SAAS;YAC1B,SAAS,EAAE;cACTA,eAAe,EAAE;YACnB,CAAC;YACD,eAAe,EAAE;cACfA,eAAe,EAAE;YACnB;UACF;QACF;MAAE;QAAAL,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACC,CAAC,eAGNpD,OAAA,CAACtB,cAAc;MACb6E,SAAS,EAAE1E,KAAM;MACjB2E,SAAS,EAAE,CAAE;MACbH,EAAE,EAAE;QACFI,MAAM,EAAE,WAAW;QACnBC,WAAW,EAAE,UAAU;QACvBC,YAAY,EAAE;MAChB,CAAE;MAAAtB,QAAA,eAEFrC,OAAA,CAACzB,KAAK;QAAA8D,QAAA,gBACJrC,OAAA,CAACrB,SAAS;UAAA0D,QAAA,eACRrC,OAAA,CAACpB,QAAQ;YAACyE,EAAE,EAAE;cAAEC,eAAe,EAAE;YAAU,CAAE;YAAAjB,QAAA,gBAC3CrC,OAAA,CAACvB,SAAS;cAAC4E,EAAE,EAAE;gBAAEO,UAAU,EAAE;cAAI,CAAE;cAAAvB,QAAA,EAAC;YAAM;cAAAY,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAW,CAAC,eACtDpD,OAAA,CAACvB,SAAS;cAAC4E,EAAE,EAAE;gBAAEO,UAAU,EAAE;cAAI,CAAE;cAAAvB,QAAA,EAAC;YAAI;cAAAY,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAW,CAAC,eACpDpD,OAAA,CAACvB,SAAS;cAAC4E,EAAE,EAAE;gBAAEO,UAAU,EAAE;cAAI,CAAE;cAAAvB,QAAA,EAAC;YAAY;cAAAY,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAW,CAAC,eAC5DpD,OAAA,CAACvB,SAAS;cAAC4E,EAAE,EAAE;gBAAEO,UAAU,EAAE;cAAI,CAAE;cAAAvB,QAAA,EAAC;YAAQ;cAAAY,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAW,CAAC,eACxDpD,OAAA,CAACvB,SAAS;cAAC4E,EAAE,EAAE;gBAAEO,UAAU,EAAE;cAAI,CAAE;cAAAvB,QAAA,EAAC;YAAK;cAAAY,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAW,CAAC,eACrDpD,OAAA,CAACvB,SAAS;cAAC4E,EAAE,EAAE;gBAAEO,UAAU,EAAE;cAAI,CAAE;cAAAvB,QAAA,EAAC;YAAc;cAAAY,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAW,CAAC,eAC9DpD,OAAA,CAACvB,SAAS;cAAC4E,EAAE,EAAE;gBAAEO,UAAU,EAAE,GAAG;gBAAEC,SAAS,EAAE;cAAS,CAAE;cAAAxB,QAAA,EAAC;YAAI;cAAAY,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAW,CAAC,eACzEpD,OAAA,CAACvB,SAAS;cAAC4E,EAAE,EAAE;gBAAEO,UAAU,EAAE,GAAG;gBAAEC,SAAS,EAAE;cAAS,CAAE;cAAAxB,QAAA,EAAC;YAAa;cAAAY,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAW,CAAC,eAClFpD,OAAA,CAACvB,SAAS;cAAC4E,EAAE,EAAE;gBAAEO,UAAU,EAAE,GAAG;gBAAEC,SAAS,EAAE;cAAS,CAAE;cAAAxB,QAAA,EAAC;YAAM;cAAAY,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAW,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACnE;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACF,CAAC,eACZpD,OAAA,CAACxB,SAAS;UAAA6D,QAAA,EACPhB,eAAe,CAACyC,MAAM,KAAK,CAAC,gBAC3B9D,OAAA,CAACpB,QAAQ;YAAAyD,QAAA,eACPrC,OAAA,CAACvB,SAAS;cAACsF,OAAO,EAAE,CAAE;cAACV,EAAE,EAAE;gBAAEQ,SAAS,EAAE,QAAQ;gBAAEG,EAAE,EAAE;cAAE,CAAE;cAAA3B,QAAA,eACxDrC,OAAA,CAACf,UAAU;gBAACgF,OAAO,EAAC,OAAO;gBAACjB,KAAK,EAAC,gBAAgB;gBAAAX,QAAA,EAC/C5B,UAAU,GAAG,wCAAwC,GAAG;cAA6B;gBAAAwC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC5E;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACJ;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACJ,CAAC,GAEX/B,eAAe,CAAC6C,GAAG,CAAE3C,OAAO,iBAC1BvB,OAAA,CAACpB,QAAQ;YAEPyE,EAAE,EAAE;cACF,SAAS,EAAE;gBACTC,eAAe,EAAE;cACnB;YACF,CAAE;YAAAjB,QAAA,gBAEFrC,OAAA,CAACvB,SAAS;cAAA4D,QAAA,eACRrC,OAAA,CAACf,UAAU;gBAACgF,OAAO,EAAC,OAAO;gBAACL,UAAU,EAAC,MAAM;gBAAAvB,QAAA,EAC1Cd,OAAO,CAACS;cAAc;gBAAAiB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACb;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACJ,CAAC,eACZpD,OAAA,CAACvB,SAAS;cAAA4D,QAAA,eACRrC,OAAA,CAAClB,IAAI;gBACHqF,KAAK,EAAEnD,mBAAmB,CAACO,OAAO,CAACY,YAAY,CAAE;gBACjDiC,IAAI,EAAC,OAAO;gBACZpB,KAAK,EAAErC,mBAAmB,CAACY,OAAO,CAACY,YAAY,CAAE;gBACjD8B,OAAO,EAAC;cAAU;gBAAAhB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACnB;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACO,CAAC,eACZpD,OAAA,CAACvB,SAAS;cAAA4D,QAAA,eACRrC,OAAA,CAACf,UAAU;gBAACgF,OAAO,EAAC,OAAO;gBAACZ,EAAE,EAAE;kBAAEO,UAAU,EAAE;gBAAI,CAAE;gBAAAvB,QAAA,EACjDd,OAAO,CAACW,YAAY,IAAI;cAAe;gBAAAe,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC9B;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACJ,CAAC,eACZpD,OAAA,CAACvB,SAAS;cAAA4D,QAAA,eACRrC,OAAA,CAAC1B,GAAG;gBAAA+D,QAAA,GACDd,OAAO,CAAC8C,qBAAqB,iBAC5BrE,OAAA,CAACf,UAAU;kBAACgF,OAAO,EAAC,OAAO;kBAACjB,KAAK,EAAC,gBAAgB;kBAACK,EAAE,EAAE;oBAAEiB,QAAQ,EAAE;kBAAW,CAAE;kBAAAjC,QAAA,GAAC,eAC5E,EAACd,OAAO,CAAC8C,qBAAqB;gBAAA;kBAAApB,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACvB,CACb,EACA7B,OAAO,CAACgD,kBAAkB,iBACzBvE,OAAA,CAACf,UAAU;kBAACgF,OAAO,EAAC,OAAO;kBAACjB,KAAK,EAAC,gBAAgB;kBAACK,EAAE,EAAE;oBAAEiB,QAAQ,EAAE;kBAAW,CAAE;kBAAAjC,QAAA,GAAC,eAC5E,EAACd,OAAO,CAACgD,kBAAkB;gBAAA;kBAAAtB,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACpB,CACb,EACA,CAAC7B,OAAO,CAAC8C,qBAAqB,IAAI,CAAC9C,OAAO,CAACgD,kBAAkB,iBAC5DvE,OAAA,CAACf,UAAU;kBAACgF,OAAO,EAAC,OAAO;kBAACjB,KAAK,EAAC,eAAe;kBAACK,EAAE,EAAE;oBAAEiB,QAAQ,EAAE;kBAAW,CAAE;kBAAAjC,QAAA,EAAC;gBAEhF;kBAAAY,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAY,CACb;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACE;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACG,CAAC,eACZpD,OAAA,CAACvB,SAAS;cAAA4D,QAAA,eACRrC,OAAA,CAAClB,IAAI;gBACHqF,KAAK,EAAE5C,OAAO,CAACR,KAAK,IAAI,QAAS;gBACjCqD,IAAI,EAAC,OAAO;gBACZpB,KAAK,EAAElC,aAAa,CAACS,OAAO,CAACR,KAAK;cAAE;gBAAAkC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACrC;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACO,CAAC,eACZpD,OAAA,CAACvB,SAAS;cAAA4D,QAAA,eACRrC,OAAA,CAACf,UAAU;gBAACgF,OAAO,EAAC,OAAO;gBAACjB,KAAK,EAAC,gBAAgB;gBAAAX,QAAA,EAC/Cd,OAAO,CAACiD,cAAc,GACrB,IAAIC,IAAI,CAAClD,OAAO,CAACiD,cAAc,CAAC,CAACE,kBAAkB,CAAC,OAAO,CAAC,GAC5D;cAAG;gBAAAzB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAEK;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACJ,CAAC,eACZpD,OAAA,CAACvB,SAAS;cAAC4E,EAAE,EAAE;gBAAEQ,SAAS,EAAE;cAAS,CAAE;cAAAxB,QAAA,eACrCrC,OAAA,CAACf,UAAU;gBAACgF,OAAO,EAAC,OAAO;gBAACL,UAAU,EAAC,MAAM;gBAACZ,KAAK,EAAC,SAAS;gBAAAX,QAAA,EAC1Dd,OAAO,CAACoD,qBAAqB,IAAI;cAAC;gBAAA1B,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACzB;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACJ,CAAC,eACZpD,OAAA,CAACvB,SAAS;cAAC4E,EAAE,EAAE;gBAAEQ,SAAS,EAAE;cAAS,CAAE;cAAAxB,QAAA,eACrCrC,OAAA,CAACf,UAAU;gBAACgF,OAAO,EAAC,OAAO;gBAACL,UAAU,EAAC,MAAM;gBAAAvB,QAAA,GAC1C,CAACd,OAAO,CAACqD,yBAAyB,IAAI,CAAC,EAAEC,OAAO,CAAC,CAAC,CAAC,EAAC,GACvD;cAAA;gBAAA5B,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAY;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACJ,CAAC,eACZpD,OAAA,CAACvB,SAAS;cAAC4E,EAAE,EAAE;gBAAEQ,SAAS,EAAE;cAAS,CAAE;cAAAxB,QAAA,eACrCrC,OAAA,CAACZ,KAAK;gBAAC0F,SAAS,EAAC,KAAK;gBAACC,OAAO,EAAE,GAAI;gBAACC,cAAc,EAAC,QAAQ;gBAAA3C,QAAA,GAEzD,CAAC,MAAM,EAAE,uBAAuB,EAAE,qBAAqB,CAAC,CAACJ,QAAQ,CAACV,OAAO,CAACY,YAAY,CAAC,IAAI9B,kBAAkB,iBAC5GL,OAAA,CAAChB,OAAO;kBAACiG,KAAK,EACZ1D,OAAO,CAACY,YAAY,KAAK,MAAM,GAAG,wBAAwB,GAC1DZ,OAAO,CAACY,YAAY,KAAK,uBAAuB,GAAG,gCAAgC,GACnF,8BACD;kBAAAE,QAAA,eACCrC,OAAA,CAACjB,UAAU;oBACTqF,IAAI,EAAC,OAAO;oBACZc,OAAO,EAAEA,CAAA,KAAM7E,kBAAkB,CAACkB,OAAO,CAAE;oBAC3C8B,EAAE,EAAE;sBACFL,KAAK,EAAE,SAAS;sBAChB,SAAS,EAAE;wBACTM,eAAe,EAAE;sBACnB;oBACF,CAAE;oBAAAjB,QAAA,eAEFrC,OAAA,CAACJ,gBAAgB;sBAAC0E,QAAQ,EAAC;oBAAO;sBAAArB,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAE;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAC3B;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACN,CACV,eAEDpD,OAAA,CAAChB,OAAO;kBAACiG,KAAK,EAAC,gBAAgB;kBAAA5C,QAAA,eAC7BrC,OAAA,CAACjB,UAAU;oBACTqF,IAAI,EAAC,OAAO;oBACZc,OAAO,EAAEA,CAAA,KAAM5E,cAAc,IAAIA,cAAc,CAACiB,OAAO,CAAE;oBACzD8B,EAAE,EAAE;sBACFL,KAAK,EAAE,SAAS;sBAChB,SAAS,EAAE;wBACTM,eAAe,EAAE;sBACnB;oBACF,CAAE;oBAAAjB,QAAA,eAEFrC,OAAA,CAACF,SAAS;sBAACwE,QAAQ,EAAC;oBAAO;sBAAArB,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAE;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACpB;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACN,CAAC,eAEVpD,OAAA,CAAChB,OAAO;kBAACiG,KAAK,EAAC,UAAU;kBAAA5C,QAAA,eACvBrC,OAAA,CAACjB,UAAU;oBACTqF,IAAI,EAAC,OAAO;oBACZc,OAAO,EAAEA,CAAA,KAAM/E,aAAa,CAACoB,OAAO,CAAE;oBACtC8B,EAAE,EAAE;sBACFL,KAAK,EAAE,SAAS;sBAChB,SAAS,EAAE;wBACTM,eAAe,EAAE;sBACnB;oBACF,CAAE;oBAAAjB,QAAA,eAEFrC,OAAA,CAACV,QAAQ;sBAACgF,QAAQ,EAAC;oBAAO;sBAAArB,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAE;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACnB;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACN,CAAC,eACVpD,OAAA,CAAChB,OAAO;kBAACiG,KAAK,EAAC,SAAS;kBAAA5C,QAAA,eACtBrC,OAAA,CAACjB,UAAU;oBACTqF,IAAI,EAAC,OAAO;oBACZc,OAAO,EAAEA,CAAA,KAAM9E,eAAe,CAACmB,OAAO,CAACS,cAAc,CAAE;oBACvDqB,EAAE,EAAE;sBACFL,KAAK,EAAE,SAAS;sBAChB,SAAS,EAAE;wBACTM,eAAe,EAAE;sBACnB;oBACF,CAAE;oBAAAjB,QAAA,eAEFrC,OAAA,CAACR,UAAU;sBAAC8E,QAAQ,EAAC;oBAAO;sBAAArB,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAE;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACrB;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACN,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACL;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACC,CAAC;UAAA,GAzIP7B,OAAO,CAACS,cAAc;YAAAiB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OA0InB,CACX;QACF;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACQ,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACP;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACM,CAAC,EAGhB3C,UAAU,iBACTT,OAAA,CAAC1B,GAAG;MAAC6G,EAAE,EAAE,CAAE;MAAA9C,QAAA,eACTrC,OAAA,CAACf,UAAU;QAACgF,OAAO,EAAC,SAAS;QAACjB,KAAK,EAAC,gBAAgB;QAAAX,QAAA,GAAC,YACzC,EAAChB,eAAe,CAACyC,MAAM,EAAC,MAAI,EAAC5C,YAAY,CAAC4C,MAAM,EAAC,UAC7D;MAAA;QAAAb,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAY;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACV,CACN;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACE,CAAC;AAEV,CAAC;AAAC5C,EAAA,CA/RIP,gBAAgB;AAAAmF,EAAA,GAAhBnF,gBAAgB;AAiStB,eAAeA,gBAAgB;AAAC,IAAAmF,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}