{"ast": null, "code": "/**\n * Utility per la navigazione nell'applicazione\n *//**\n * Reindirizza alla pagina di visualizzazione cavi con un ritardo opzionale\n * @param {function} navigate - Funzione di navigazione di React Router\n * @param {number} delay - <PERSON>rdo in millisecondi prima del reindirizzamento (default: 0)\n */export const redirectToVisualizzaCavi=function(navigate){let delay=arguments.length>1&&arguments[1]!==undefined?arguments[1]:0;console.log('Tentativo di reindirizzamento a /dashboard/cavi/visualizza');// Funzione di reindirizzamento\nconst doRedirect=()=>{try{// Prima prova con navigate\nnavigate('/dashboard/cavi/visualizza');console.log('Reindirizzamento con navigate eseguito');}catch(error){console.error('Errore durante il reindirizzamento con navigate:',error);// Se fallisce, usa window.location come fallback\ntry{window.location.href='/dashboard/cavi/visualizza';console.log('Reindirizzamento con window.location eseguito');}catch(locationError){console.error('Errore anche con window.location:',locationError);// Ultimo tentativo: ricarica la pagina\nwindow.location.reload();}}};// Esegui con o senza ritardo\nif(delay>0){console.log(`Reindirizzamento programmato con ritardo di ${delay}ms`);setTimeout(doRedirect,delay);}else{doRedirect();}};/**\n * Ricarica la pagina corrente con un ritardo opzionale\n * @param {number} delay - Ritardo in millisecondi prima del ricaricamento (default: 0)\n */export const reloadPage=function(){let delay=arguments.length>0&&arguments[0]!==undefined?arguments[0]:0;if(delay>0){setTimeout(()=>{window.location.reload();},delay);}else{window.location.reload();}};", "map": {"version": 3, "names": ["redirectToVisualizzaCavi", "navigate", "delay", "arguments", "length", "undefined", "console", "log", "doRedirect", "error", "window", "location", "href", "locationError", "reload", "setTimeout", "reloadPage"], "sources": ["C:/Users/<USER>/PycharmProjects/CMS/webapp/frontend/src/utils/navigationUtils.js"], "sourcesContent": ["/**\n * Utility per la navigazione nell'applicazione\n */\n\n/**\n * Reindirizza alla pagina di visualizzazione cavi con un ritardo opzionale\n * @param {function} navigate - Funzione di navigazione di React Router\n * @param {number} delay - Ritardo in millisecondi prima del reindirizzamento (default: 0)\n */\nexport const redirectToVisualizzaCavi = (navigate, delay = 0) => {\n  console.log('Tentativo di reindirizzamento a /dashboard/cavi/visualizza');\n\n  // Funzione di reindirizzamento\n  const doRedirect = () => {\n    try {\n      // Prima prova con navigate\n      navigate('/dashboard/cavi/visualizza');\n      console.log('Reindirizzamento con navigate eseguito');\n    } catch (error) {\n      console.error('Errore durante il reindirizzamento con navigate:', error);\n      // Se fallisce, usa window.location come fallback\n      try {\n        window.location.href = '/dashboard/cavi/visualizza';\n        console.log('Reindirizzamento con window.location eseguito');\n      } catch (locationError) {\n        console.error('Errore anche con window.location:', locationError);\n        // Ultimo tentativo: ricarica la pagina\n        window.location.reload();\n      }\n    }\n  };\n\n  // Esegui con o senza ritardo\n  if (delay > 0) {\n    console.log(`Reindirizzamento programmato con ritardo di ${delay}ms`);\n    setTimeout(doRedirect, delay);\n  } else {\n    doRedirect();\n  }\n};\n\n/**\n * Ricarica la pagina corrente con un ritardo opzionale\n * @param {number} delay - Ritardo in millisecondi prima del ricaricamento (default: 0)\n */\nexport const reloadPage = (delay = 0) => {\n  if (delay > 0) {\n    setTimeout(() => {\n      window.location.reload();\n    }, delay);\n  } else {\n    window.location.reload();\n  }\n};\n"], "mappings": "AAAA;AACA;AACA,GAEA;AACA;AACA;AACA;AACA,GACA,MAAO,MAAM,CAAAA,wBAAwB,CAAG,QAAAA,CAACC,QAAQ,CAAgB,IAAd,CAAAC,KAAK,CAAAC,SAAA,CAAAC,MAAA,IAAAD,SAAA,MAAAE,SAAA,CAAAF,SAAA,IAAG,CAAC,CAC1DG,OAAO,CAACC,GAAG,CAAC,4DAA4D,CAAC,CAEzE;AACA,KAAM,CAAAC,UAAU,CAAGA,CAAA,GAAM,CACvB,GAAI,CACF;AACAP,QAAQ,CAAC,4BAA4B,CAAC,CACtCK,OAAO,CAACC,GAAG,CAAC,wCAAwC,CAAC,CACvD,CAAE,MAAOE,KAAK,CAAE,CACdH,OAAO,CAACG,KAAK,CAAC,kDAAkD,CAAEA,KAAK,CAAC,CACxE;AACA,GAAI,CACFC,MAAM,CAACC,QAAQ,CAACC,IAAI,CAAG,4BAA4B,CACnDN,OAAO,CAACC,GAAG,CAAC,+CAA+C,CAAC,CAC9D,CAAE,MAAOM,aAAa,CAAE,CACtBP,OAAO,CAACG,KAAK,CAAC,mCAAmC,CAAEI,aAAa,CAAC,CACjE;AACAH,MAAM,CAACC,QAAQ,CAACG,MAAM,CAAC,CAAC,CAC1B,CACF,CACF,CAAC,CAED;AACA,GAAIZ,KAAK,CAAG,CAAC,CAAE,CACbI,OAAO,CAACC,GAAG,CAAC,+CAA+CL,KAAK,IAAI,CAAC,CACrEa,UAAU,CAACP,UAAU,CAAEN,KAAK,CAAC,CAC/B,CAAC,IAAM,CACLM,UAAU,CAAC,CAAC,CACd,CACF,CAAC,CAED;AACA;AACA;AACA,GACA,MAAO,MAAM,CAAAQ,UAAU,CAAG,QAAAA,CAAA,CAAe,IAAd,CAAAd,KAAK,CAAAC,SAAA,CAAAC,MAAA,IAAAD,SAAA,MAAAE,SAAA,CAAAF,SAAA,IAAG,CAAC,CAClC,GAAID,KAAK,CAAG,CAAC,CAAE,CACba,UAAU,CAAC,IAAM,CACfL,MAAM,CAACC,QAAQ,CAACG,MAAM,CAAC,CAAC,CAC1B,CAAC,CAAEZ,KAAK,CAAC,CACX,CAAC,IAAM,CACLQ,MAAM,CAACC,QAAQ,CAACG,MAAM,CAAC,CAAC,CAC1B,CACF,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}