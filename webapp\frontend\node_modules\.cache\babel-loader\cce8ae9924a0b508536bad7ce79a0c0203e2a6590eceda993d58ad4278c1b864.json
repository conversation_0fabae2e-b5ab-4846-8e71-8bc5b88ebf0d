{"ast": null, "code": "import { addMilliseconds } from \"./addMilliseconds.mjs\";\nimport { millisecondsInMinute } from \"./constants.mjs\";\n\n/**\n * @name addMinutes\n * @category Minute Helpers\n * @summary Add the specified number of minutes to the given date.\n *\n * @description\n * Add the specified number of minutes to the given date.\n *\n * @typeParam DateType - The `Date` type, the function operates on. Gets inferred from passed arguments. Allows to use extensions like [`UTCDate`](https://github.com/date-fns/utc).\n *\n * @param date - The date to be changed\n * @param amount - The amount of minutes to be added.\n *\n * @returns The new date with the minutes added\n *\n * @example\n * // Add 30 minutes to 10 July 2014 12:00:00:\n * const result = addMinutes(new Date(2014, 6, 10, 12, 0), 30)\n * //=> Thu Jul 10 2014 12:30:00\n */\nexport function addMinutes(date, amount) {\n  return addMilliseconds(date, amount * millisecondsInMinute);\n}\n\n// Fallback for modularized imports:\nexport default addMinutes;", "map": {"version": 3, "names": ["addMilliseconds", "millisecondsInMinute", "addMinutes", "date", "amount"], "sources": ["C:/CMS/webapp/frontend/node_modules/date-fns/addMinutes.mjs"], "sourcesContent": ["import { addMilliseconds } from \"./addMilliseconds.mjs\";\nimport { millisecondsInMinute } from \"./constants.mjs\";\n\n/**\n * @name addMinutes\n * @category Minute Helpers\n * @summary Add the specified number of minutes to the given date.\n *\n * @description\n * Add the specified number of minutes to the given date.\n *\n * @typeParam DateType - The `Date` type, the function operates on. Gets inferred from passed arguments. Allows to use extensions like [`UTCDate`](https://github.com/date-fns/utc).\n *\n * @param date - The date to be changed\n * @param amount - The amount of minutes to be added.\n *\n * @returns The new date with the minutes added\n *\n * @example\n * // Add 30 minutes to 10 July 2014 12:00:00:\n * const result = addMinutes(new Date(2014, 6, 10, 12, 0), 30)\n * //=> Thu Jul 10 2014 12:30:00\n */\nexport function addMinutes(date, amount) {\n  return addMilliseconds(date, amount * millisecondsInMinute);\n}\n\n// Fallback for modularized imports:\nexport default addMinutes;\n"], "mappings": "AAAA,SAASA,eAAe,QAAQ,uBAAuB;AACvD,SAASC,oBAAoB,QAAQ,iBAAiB;;AAEtD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO,SAASC,UAAUA,CAACC,IAAI,EAAEC,MAAM,EAAE;EACvC,OAAOJ,eAAe,CAACG,IAAI,EAAEC,MAAM,GAAGH,oBAAoB,CAAC;AAC7D;;AAEA;AACA,eAAeC,UAAU", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}