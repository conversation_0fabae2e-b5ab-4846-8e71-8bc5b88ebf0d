{"ast": null, "code": "const lightGreen = {\n  50: '#f1f8e9',\n  100: '#dcedc8',\n  200: '#c5e1a5',\n  300: '#aed581',\n  400: '#9ccc65',\n  500: '#8bc34a',\n  600: '#7cb342',\n  700: '#689f38',\n  800: '#558b2f',\n  900: '#33691e',\n  A100: '#ccff90',\n  A200: '#b2ff59',\n  A400: '#76ff03',\n  A700: '#64dd17'\n};\nexport default lightGreen;", "map": {"version": 3, "names": ["lightGreen", "A100", "A200", "A400", "A700"], "sources": ["C:/Users/<USER>/PycharmProjects/CMS/webapp/frontend/node_modules/@mui/material/colors/lightGreen.js"], "sourcesContent": ["const lightGreen = {\n  50: '#f1f8e9',\n  100: '#dcedc8',\n  200: '#c5e1a5',\n  300: '#aed581',\n  400: '#9ccc65',\n  500: '#8bc34a',\n  600: '#7cb342',\n  700: '#689f38',\n  800: '#558b2f',\n  900: '#33691e',\n  A100: '#ccff90',\n  A200: '#b2ff59',\n  A400: '#76ff03',\n  A700: '#64dd17'\n};\nexport default lightGreen;"], "mappings": "AAAA,MAAMA,UAAU,GAAG;EACjB,EAAE,EAAE,SAAS;EACb,GAAG,EAAE,SAAS;EACd,GAAG,EAAE,SAAS;EACd,GAAG,EAAE,SAAS;EACd,GAAG,EAAE,SAAS;EACd,GAAG,EAAE,SAAS;EACd,GAAG,EAAE,SAAS;EACd,GAAG,EAAE,SAAS;EACd,GAAG,EAAE,SAAS;EACd,GAAG,EAAE,SAAS;EACdC,IAAI,EAAE,SAAS;EACfC,IAAI,EAAE,SAAS;EACfC,IAAI,EAAE,SAAS;EACfC,IAAI,EAAE;AACR,CAAC;AACD,eAAeJ,UAAU", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}