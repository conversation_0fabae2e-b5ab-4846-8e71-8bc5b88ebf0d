{"ast": null, "code": "import { toDate } from \"./toDate.mjs\";\n\n/**\n * @name getMonth\n * @category Month Helpers\n * @summary Get the month of the given date.\n *\n * @description\n * Get the month of the given date.\n *\n * @typeParam DateType - The `Date` type, the function operates on. Gets inferred from passed arguments. Allows to use extensions like [`UTCDate`](https://github.com/date-fns/utc).\n *\n * @param date - The given date\n *\n * @returns The month index (0-11)\n *\n * @example\n * // Which month is 29 February 2012?\n * const result = getMonth(new Date(2012, 1, 29))\n * //=> 1\n */\nexport function getMonth(date) {\n  const _date = toDate(date);\n  const month = _date.getMonth();\n  return month;\n}\n\n// Fallback for modularized imports:\nexport default getMonth;", "map": {"version": 3, "names": ["toDate", "getMonth", "date", "_date", "month"], "sources": ["C:/CMS/webapp/frontend/node_modules/date-fns/getMonth.mjs"], "sourcesContent": ["import { toDate } from \"./toDate.mjs\";\n\n/**\n * @name getMonth\n * @category Month Helpers\n * @summary Get the month of the given date.\n *\n * @description\n * Get the month of the given date.\n *\n * @typeParam DateType - The `Date` type, the function operates on. Gets inferred from passed arguments. Allows to use extensions like [`UTCDate`](https://github.com/date-fns/utc).\n *\n * @param date - The given date\n *\n * @returns The month index (0-11)\n *\n * @example\n * // Which month is 29 February 2012?\n * const result = getMonth(new Date(2012, 1, 29))\n * //=> 1\n */\nexport function getMonth(date) {\n  const _date = toDate(date);\n  const month = _date.getMonth();\n  return month;\n}\n\n// Fallback for modularized imports:\nexport default getMonth;\n"], "mappings": "AAAA,SAASA,MAAM,QAAQ,cAAc;;AAErC;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO,SAASC,QAAQA,CAACC,IAAI,EAAE;EAC7B,MAAMC,KAAK,GAAGH,MAAM,CAACE,IAAI,CAAC;EAC1B,MAAME,KAAK,GAAGD,KAAK,CAACF,QAAQ,CAAC,CAAC;EAC9B,OAAOG,KAAK;AACd;;AAEA;AACA,eAAeH,QAAQ", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}