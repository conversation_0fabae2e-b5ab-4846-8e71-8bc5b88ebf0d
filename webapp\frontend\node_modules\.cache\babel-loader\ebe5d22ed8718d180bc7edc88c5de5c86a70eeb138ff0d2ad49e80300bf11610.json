{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\PycharmProjects\\\\CMS\\\\webapp\\\\frontend\\\\src\\\\components\\\\cavi\\\\PosaCaviCollegamenti.js\",\n  _s = $RefreshSig$();\nimport React, { useState } from 'react';\nimport { Box, Typography, Button, Paper, Divider, List, ListItem, ListItemText, ListItemIcon, ListItemButton, Dialog, DialogTitle, DialogContent, DialogContentText, DialogActions, TextField, FormControl, InputLabel, Select, MenuItem, Grid, Alert, CircularProgress, FormHelperText, Radio, RadioGroup, FormControlLabel, FormLabel } from '@mui/material';\nimport { Add as AddIcon, Edit as EditIcon, Delete as DeleteIcon, Cable as CableIcon, Save as SaveIcon, Warning as WarningIcon } from '@mui/icons-material';\nimport { useNavigate } from 'react-router-dom';\nimport caviService from '../../services/caviService';\nimport { validateCavoData, validateField, isEmpty } from '../../utils/validationUtils';\nimport { redirectToVisualizzaCavi } from '../../utils/navigationUtils';\nimport AggiungiCavoForm from './AggiungiCavoForm';\nimport { jsxDEV as _jsxDEV, Fragment as _Fragment } from \"react/jsx-dev-runtime\";\nconst PosaCaviCollegamenti = ({\n  cantiereId: propCantiereId,\n  onSuccess,\n  onError,\n  initialOption = null\n}) => {\n  _s();\n  // Log del cantiereId all'avvio\n  console.log('PosaCaviCollegamenti - cantiereId da props:', propCantiereId);\n\n  // Aggiungi navigate per la navigazione programmatica\n  const navigate = useNavigate();\n\n  // Se cantiereId non è definito nelle props, prova a recuperarlo dal localStorage\n  const cantiereId = propCantiereId || parseInt(localStorage.getItem('selectedCantiereId'), 10);\n  console.log('PosaCaviCollegamenti - cantiereId effettivo:', cantiereId);\n  const [loading, setLoading] = useState(false);\n  const [selectedOption, setSelectedOption] = useState(null);\n  const [openDialog, setOpenDialog] = useState(false);\n  const [dialogType, setDialogType] = useState('');\n  const [selectedCavo, setSelectedCavo] = useState(null);\n  const [formData, setFormData] = useState({\n    id_cavo: '',\n    metri_posati: '',\n    id_bobina: ''\n  });\n  const [cavoIdInput, setCavoIdInput] = useState('');\n  const [formErrors, setFormErrors] = useState({});\n  const [formWarnings, setFormWarnings] = useState({});\n  const [cavi, setCavi] = useState([]);\n  const [caviLoading, setCaviLoading] = useState(false);\n  const [deleteMode, setDeleteMode] = useState('spare'); // 'spare' o 'delete'\n\n  // Inizializza il componente con l'opzione iniziale se specificata\n  React.useEffect(() => {\n    if (initialOption) {\n      // Imposta direttamente le opzioni invece di chiamare handleOptionSelect\n      // per evitare dipendenze circolari\n      setSelectedOption(initialOption);\n      if (initialOption === 'eliminaCavo') {\n        loadCavi('eliminaCavo');\n        setDialogType('eliminaCavo');\n        setOpenDialog(true);\n      } else if (initialOption === 'modificaCavo') {\n        loadCavi('modificaCavo');\n        setDialogType('selezionaCavo');\n        setOpenDialog(true);\n      } else if (initialOption === 'aggiungiCavo') {\n        setDialogType('aggiungiCavo');\n        setOpenDialog(true);\n      }\n    }\n    // eslint-disable-next-line react-hooks/exhaustive-deps\n  }, [initialOption]);\n\n  // Carica i cavi attivi per la selezione\n  const loadCavi = async operationType => {\n    try {\n      setCaviLoading(true);\n\n      // Verifica che cantiereId sia valido\n      if (!cantiereId) {\n        throw new Error('ID cantiere non valido o mancante');\n      }\n      console.log('Caricamento cavi per cantiere:', cantiereId);\n      const caviData = await caviService.getCavi(cantiereId, 0);\n\n      // Filtra i cavi in base al tipo di operazione\n      if (operationType === 'modificaCavo') {\n        // Per modifica cavo, mostra solo i cavi non posati (metratura_reale = 0 e stato != Installato)\n        const caviNonPosati = caviData.filter(cavo => parseFloat(cavo.metratura_reale) === 0 && cavo.stato_installazione !== 'Installato');\n        setCavi(caviNonPosati);\n      } else {\n        // Per altre operazioni, mostra tutti i cavi\n        setCavi(caviData);\n      }\n    } catch (error) {\n      console.error('Errore nel caricamento dei cavi:', error);\n\n      // Gestione più dettagliata dell'errore\n      let errorMessage = 'Errore nel caricamento dei cavi';\n      if (error.response) {\n        // Errore dal server con risposta\n        errorMessage += `: ${error.response.status} ${error.response.statusText}`;\n        if (error.response.data && error.response.data.detail) {\n          errorMessage += ` - ${error.response.data.detail}`;\n        }\n      } else if (error.request) {\n        // Errore di rete senza risposta dal server\n        errorMessage += ': Nessuna risposta dal server. Verifica la connessione di rete.';\n      } else if (error.message) {\n        // Errore con messaggio\n        errorMessage += `: ${error.message}`;\n      }\n      onError(errorMessage);\n    } finally {\n      setCaviLoading(false);\n    }\n  };\n\n  // Gestisce la selezione di un'opzione dal menu\n  const handleOptionSelect = option => {\n    setSelectedOption(option);\n    if (option === 'inserisciMetri' || option === 'modificaBobina') {\n      loadCavi(option);\n      setDialogType(option);\n      setOpenDialog(true);\n    } else if (option === 'aggiungiCavo') {\n      // Apri il dialog per aggiungere un nuovo cavo\n      setDialogType('aggiungiCavo');\n      setOpenDialog(true);\n    } else if (option === 'modificaCavo') {\n      loadCavi('modificaCavo');\n      setDialogType('selezionaCavo');\n      setOpenDialog(true);\n    } else if (option === 'eliminaCavo') {\n      loadCavi('eliminaCavo');\n      setDialogType('eliminaCavo');\n      setOpenDialog(true);\n    } else if (option === 'collegamentoCavo') {\n      // Reindirizza alla pagina di gestione collegamenti\n      window.location.href = `/dashboard/cantieri/${cantiereId}/cavi/collegamenti`;\n    }\n  };\n\n  // Gestisce la chiusura del dialog\n  const handleCloseDialog = () => {\n    console.log('Chiusura dialog...');\n    // Reset dello stato del componente\n    setOpenDialog(false);\n    setSelectedCavo(null);\n    setFormData({\n      id_cavo: '',\n      metri_posati: '',\n      id_bobina: ''\n    });\n    setCavoIdInput('');\n    setFormErrors({});\n    setFormWarnings({});\n    setDeleteMode('spare'); // Reset alla modalità predefinita\n    setLoading(false); // Assicurati che loading sia false quando chiudi il dialog\n\n    // Se è stato specificato un initialOption, notifica il genitore che il dialogo è stato chiuso\n    // ma senza messaggio di errore\n    if (initialOption && onSuccess) {\n      // Chiama onSuccess ma senza messaggio per evitare l'alert\n      onSuccess(null);\n    }\n  };\n\n  // Gestisce la selezione di un cavo\n  const handleCavoSelect = cavo => {\n    setSelectedCavo(cavo);\n    if (dialogType === 'inserisciMetri') {\n      setFormData({\n        ...formData,\n        id_cavo: cavo.id_cavo,\n        metri_posati: ''\n      });\n    } else if (dialogType === 'modificaBobina') {\n      setFormData({\n        ...formData,\n        id_cavo: cavo.id_cavo,\n        id_bobina: cavo.id_bobina || ''\n      });\n    } else if (dialogType === 'selezionaCavo') {\n      setDialogType('modificaCavo');\n      setFormData({\n        ...cavo,\n        metri_teorici: cavo.metri_teorici || '',\n        metratura_reale: cavo.metratura_reale || '0'\n      });\n    }\n  };\n\n  // Gestisce la ricerca di un cavo per ID\n  const handleSearchCavoById = async () => {\n    if (!cavoIdInput.trim()) {\n      onError('Inserisci un ID cavo valido');\n      return;\n    }\n    try {\n      setCaviLoading(true);\n\n      // Verifica che cantiereId sia valido\n      if (!cantiereId) {\n        throw new Error('ID cantiere non valido o mancante');\n      }\n      console.log('Ricerca cavo con ID:', cavoIdInput, 'per cantiere:', cantiereId);\n      const caviData = await caviService.getCavi(cantiereId, 0);\n      const cavo = caviData.find(c => c.id_cavo === cavoIdInput.trim());\n      if (!cavo) {\n        onError(`Cavo con ID ${cavoIdInput} non trovato`);\n        return;\n      }\n\n      // Verifica se stiamo cercando un cavo per modificarlo\n      if (dialogType === 'selezionaCavo') {\n        // Verifica che il cavo non sia già posato\n        if (parseFloat(cavo.metratura_reale) > 0 || cavo.stato_installazione === 'Installato') {\n          onError(`Il cavo ${cavo.id_cavo} risulta già posato. Utilizza l'opzione \"Modifica bobina cavo posato\" per modificarlo.`);\n          return;\n        }\n      }\n\n      // Seleziona il cavo trovato\n      handleCavoSelect(cavo);\n    } catch (error) {\n      console.error('Errore nel caricamento dei cavi:', error);\n\n      // Gestione più dettagliata dell'errore\n      let errorMessage = 'Errore nel caricamento dei cavi';\n      if (error.response) {\n        // Errore dal server con risposta\n        errorMessage += `: ${error.response.status} ${error.response.statusText}`;\n        if (error.response.data && error.response.data.detail) {\n          errorMessage += ` - ${error.response.data.detail}`;\n        }\n      } else if (error.request) {\n        // Errore di rete senza risposta dal server\n        errorMessage += ': Nessuna risposta dal server. Verifica la connessione di rete.';\n      } else if (error.message) {\n        // Errore con messaggio\n        errorMessage += `: ${error.message}`;\n      }\n      onError(errorMessage);\n    } finally {\n      setCaviLoading(false);\n    }\n  };\n\n  // Gestisce il cambio dell'input dell'ID cavo\n  const handleCavoIdInputChange = e => {\n    setCavoIdInput(e.target.value);\n  };\n\n  // Gestisce il cambio dei valori nel form con validazione\n  const handleFormChange = e => {\n    const {\n      name,\n      value\n    } = e.target;\n\n    // Aggiorna il valore nel form\n    setFormData({\n      ...formData,\n      [name]: value\n    });\n\n    // Valida il campo\n    if (dialogType === 'modificaCavo') {\n      const additionalParams = {};\n      if (name === 'metratura_reale') {\n        additionalParams.metriTeorici = parseFloat(formData.metri_teorici || 0);\n      }\n      const result = validateField(name, value, additionalParams);\n\n      // Aggiorna gli errori\n      setFormErrors(prev => ({\n        ...prev,\n        [name]: !result.valid ? result.message : null\n      }));\n\n      // Aggiorna gli avvisi\n      setFormWarnings(prev => ({\n        ...prev,\n        [name]: result.warning ? result.message : null\n      }));\n    }\n  };\n\n  // Gestisce il salvataggio del form con validazione\n  const handleSave = async () => {\n    try {\n      setLoading(true);\n      if (dialogType === 'inserisciMetri') {\n        // Valida i metri posati\n        if (isEmpty(formData.metri_posati) || isNaN(parseFloat(formData.metri_posati))) {\n          setFormErrors({\n            metri_posati: 'Inserire un valore numerico valido'\n          });\n          setLoading(false);\n          return;\n        }\n        try {\n          await caviService.updateMetriPosati(cantiereId, formData.id_cavo, parseFloat(formData.metri_posati));\n          // Prima chiama onSuccess, poi chiudi il dialog\n          onSuccess('Metri posati aggiornati con successo');\n          // Chiudi il dialog\n          handleCloseDialog();\n          // Reindirizza alla pagina di visualizzazione cavi con un ritardo maggiore\n          setTimeout(() => {\n            redirectToVisualizzaCavi(navigate, 1000);\n          }, 500);\n        } catch (error) {\n          console.error('Errore durante l\\'aggiornamento dei metri posati:', error);\n          onError('Errore durante l\\'aggiornamento dei metri posati: ' + (error.message || 'Errore sconosciuto'));\n          setLoading(false);\n        }\n      } else if (dialogType === 'modificaBobina') {\n        try {\n          await caviService.updateBobina(cantiereId, formData.id_cavo, formData.id_bobina);\n          // Prima chiama onSuccess, poi chiudi il dialog\n          onSuccess('Bobina aggiornata con successo');\n          // Chiudi il dialog\n          handleCloseDialog();\n          // Reindirizza alla pagina di visualizzazione cavi con un ritardo maggiore\n          setTimeout(() => {\n            redirectToVisualizzaCavi(navigate, 1000);\n          }, 500);\n        } catch (error) {\n          console.error('Errore durante l\\'aggiornamento della bobina:', error);\n          onError('Errore durante l\\'aggiornamento della bobina: ' + (error.message || 'Errore sconosciuto'));\n          setLoading(false);\n        }\n      } else if (dialogType === 'modificaCavo') {\n        // Validazione completa dei dati del cavo\n        const validation = validateCavoData(formData);\n        if (!validation.isValid) {\n          setFormErrors(validation.errors);\n          setFormWarnings(validation.warnings);\n          setLoading(false);\n          return;\n        }\n\n        // Usa i dati validati\n        const validatedData = validation.validatedData;\n\n        // Rimuovi i campi di sistema che non devono essere modificati\n        const dataToSend = {\n          ...validatedData\n        };\n        delete dataToSend.id_bobina; // Rimuovi id_bobina perché è un campo di sistema\n        delete dataToSend.metratura_reale; // Rimuovi metratura_reale perché è un campo di sistema\n        delete dataToSend.modificato_manualmente; // Rimuovi modificato_manualmente perché è un campo di sistema\n        delete dataToSend.timestamp; // Rimuovi timestamp perché è un campo di sistema\n        delete dataToSend.stato_installazione; // Rimuovi stato_installazione perché è un campo di sistema per cavi non posati\n\n        // Imposta modificato_manualmente a 1 per indicare che il cavo è stato modificato manualmente\n        dataToSend.modificato_manualmente = 1;\n        console.log('Dati inviati al server:', dataToSend);\n        try {\n          console.log('Invio dati al server per aggiornamento cavo:', dataToSend);\n          const result = await caviService.updateCavo(cantiereId, dataToSend.id_cavo, dataToSend);\n          console.log('Risposta dal server dopo aggiornamento cavo:', result);\n\n          // Prima chiama onSuccess, poi chiudi il dialog\n          onSuccess('Cavo modificato con successo');\n          // Chiudi il dialog\n          handleCloseDialog();\n          // Reindirizza alla pagina di visualizzazione cavi con un ritardo maggiore\n          setTimeout(() => {\n            redirectToVisualizzaCavi(navigate, 1000);\n          }, 500);\n          return;\n        } catch (error) {\n          console.error('Errore durante l\\'aggiornamento del cavo:', error);\n\n          // Gestione più dettagliata dell'errore\n          let errorMessage = 'Errore durante l\\'aggiornamento del cavo';\n          if (error.response) {\n            // Errore dal server con risposta\n            errorMessage += `: ${error.response.status} ${error.response.statusText}`;\n            if (error.response.data && error.response.data.detail) {\n              errorMessage += ` - ${error.response.data.detail}`;\n            }\n          } else if (error.request) {\n            // Errore di rete senza risposta dal server\n            errorMessage += ': Nessuna risposta dal server. Verifica la connessione di rete.';\n\n            // Anche se c'è un errore di rete, la modifica potrebbe essere stata salvata\n            // Quindi consideriamo l'operazione come riuscita\n            console.log('Considerando l\\'operazione come riuscita nonostante l\\'errore di rete');\n            // Prima chiama onSuccess, poi chiudi il dialog\n            onSuccess('Cavo modificato con successo (la connessione potrebbe essere instabile)');\n            // Chiudi il dialog\n            handleCloseDialog();\n            // Reindirizza alla pagina di visualizzazione cavi con un ritardo maggiore\n            setTimeout(() => {\n              redirectToVisualizzaCavi(navigate, 1000);\n            }, 500);\n            return;\n          } else if (error.message) {\n            // Errore con messaggio\n            errorMessage += `: ${error.message}`;\n\n            // Se il messaggio indica che la modifica potrebbe essere stata salvata comunque\n            if (error.message.includes('La modifica potrebbe essere stata salvata')) {\n              console.log('Considerando l\\'operazione come riuscita nonostante l\\'errore');\n              handleCloseDialog();\n              onSuccess('Cavo modificato con successo (la connessione potrebbe essere instabile)');\n              // Reindirizza alla pagina di visualizzazione cavi con un ritardo maggiore\n              redirectToVisualizzaCavi(navigate, 1000);\n              return;\n            }\n          }\n          onError(errorMessage);\n          setLoading(false);\n          return;\n        }\n\n        // Mostra avvisi se presenti\n        if (Object.keys(validation.warnings).length > 0) {\n          const warningMessages = Object.values(validation.warnings).join('\\n');\n          console.warn('Avvisi durante il salvataggio:', warningMessages);\n        }\n      } else if (dialogType === 'eliminaCavo') {\n        // Verifica se il cavo è installato\n        const isInstalled = selectedCavo.stato_installazione === 'Installato' || selectedCavo.metratura_reale && selectedCavo.metratura_reale > 0;\n        if (isInstalled) {\n          // Se è installato, marca solo come SPARE\n          console.log('Marcando cavo installato come SPARE:', selectedCavo.id_cavo);\n          try {\n            // Prima prova con markCavoAsSpare\n            const result = await caviService.markCavoAsSpare(cantiereId, selectedCavo.id_cavo);\n            console.log('Risultato marcatura SPARE:', result);\n            console.log('Nuovo valore modificato_manualmente:', result.modificato_manualmente);\n            // Chiudi il dialog prima di chiamare onSuccess\n            handleCloseDialog();\n            onSuccess(`Cavo ${selectedCavo.id_cavo} marcato come SPARE con successo`);\n            // Reindirizza alla pagina di visualizzazione cavi con un ritardo\n            redirectToVisualizzaCavi(navigate, 500);\n          } catch (markError) {\n            console.error('Errore con markCavoAsSpare, tentativo con deleteCavo mode=spare:', markError);\n            // Se fallisce, prova con deleteCavo mode=spare\n            const result = await caviService.deleteCavo(cantiereId, selectedCavo.id_cavo, 'spare');\n            console.log('Risultato marcatura SPARE con deleteCavo:', result);\n            // Chiudi il dialog prima di chiamare onSuccess\n            handleCloseDialog();\n            onSuccess(`Cavo ${selectedCavo.id_cavo} marcato come SPARE con successo`);\n          }\n        } else {\n          // Se non è installato, usa la modalità selezionata (SPARE o DELETE)\n          console.log('Eliminando cavo non installato con modalità:', deleteMode);\n          const result = await caviService.deleteCavo(cantiereId, selectedCavo.id_cavo, deleteMode);\n          console.log('Risultato eliminazione/marcatura:', result);\n          // Chiudi il dialog prima di chiamare onSuccess\n          handleCloseDialog();\n          onSuccess(`Cavo ${selectedCavo.id_cavo} ${deleteMode === 'spare' ? 'marcato come SPARE' : 'eliminato'} con successo`);\n          // Reindirizza alla pagina di visualizzazione cavi con un ritardo\n          redirectToVisualizzaCavi(navigate, 500);\n        }\n      }\n\n      // Non chiamare handleCloseDialog() qui, perché il dialog verrà chiuso dal genitore\n      // quando viene chiamato onSuccess()\n    } catch (error) {\n      console.error('Errore durante l\\'operazione:', error);\n\n      // Gestione più dettagliata dell'errore\n      let errorMessage = 'Errore sconosciuto';\n      if (error.detail) {\n        // Errore dal backend con dettaglio\n        errorMessage = error.detail;\n      } else if (error.message) {\n        // Errore con messaggio\n        errorMessage = error.message;\n      } else if (typeof error === 'string') {\n        // Errore come stringa\n        errorMessage = error;\n      }\n      onError('Errore durante l\\'operazione: ' + errorMessage);\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  // Renderizza il dialog in base al tipo\n  const renderDialog = () => {\n    if (dialogType === 'aggiungiCavo') {\n      return /*#__PURE__*/_jsxDEV(Dialog, {\n        open: openDialog,\n        onClose: handleCloseDialog,\n        maxWidth: \"md\",\n        fullWidth: true,\n        children: [/*#__PURE__*/_jsxDEV(DialogTitle, {\n          children: \"Aggiungi Nuovo Cavo\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 537,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(DialogContent, {\n          children: /*#__PURE__*/_jsxDEV(Box, {\n            sx: {\n              mt: 1\n            },\n            children: /*#__PURE__*/_jsxDEV(AggiungiCavoForm, {\n              cantiereId: cantiereId,\n              onSuccess: message => {\n                onSuccess(message);\n                handleCloseDialog();\n              },\n              onError: onError,\n              isDialog: true\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 540,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 539,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 538,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(DialogActions, {\n          children: /*#__PURE__*/_jsxDEV(Button, {\n            onClick: handleCloseDialog,\n            children: \"Annulla\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 552,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 551,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 536,\n        columnNumber: 9\n      }, this);\n    } else if (dialogType === 'inserisciMetri') {\n      return /*#__PURE__*/_jsxDEV(Dialog, {\n        open: openDialog,\n        onClose: handleCloseDialog,\n        maxWidth: \"md\",\n        fullWidth: true,\n        children: [/*#__PURE__*/_jsxDEV(DialogTitle, {\n          children: \"Inserisci Metri Posati\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 559,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(DialogContent, {\n          children: caviLoading ? /*#__PURE__*/_jsxDEV(CircularProgress, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 562,\n            columnNumber: 15\n          }, this) : cavi.length === 0 ? /*#__PURE__*/_jsxDEV(Alert, {\n            severity: \"info\",\n            children: \"Nessun cavo disponibile\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 564,\n            columnNumber: 15\n          }, this) : !selectedCavo ? /*#__PURE__*/_jsxDEV(Box, {\n            children: [/*#__PURE__*/_jsxDEV(Typography, {\n              variant: \"subtitle1\",\n              gutterBottom: true,\n              children: \"Seleziona un cavo:\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 567,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(List, {\n              children: cavi.map(cavo => /*#__PURE__*/_jsxDEV(ListItem, {\n                button: true,\n                onClick: () => handleCavoSelect(cavo),\n                children: /*#__PURE__*/_jsxDEV(ListItemText, {\n                  primary: cavo.id_cavo,\n                  secondary: `${cavo.tipologia || 'N/A'} - Da: ${cavo.ubicazione_partenza || 'N/A'} A: ${cavo.ubicazione_arrivo || 'N/A'}`\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 577,\n                  columnNumber: 23\n                }, this)\n              }, cavo.id_cavo, false, {\n                fileName: _jsxFileName,\n                lineNumber: 572,\n                columnNumber: 21\n              }, this))\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 570,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 566,\n            columnNumber: 15\n          }, this) : /*#__PURE__*/_jsxDEV(Box, {\n            sx: {\n              mt: 2\n            },\n            children: [/*#__PURE__*/_jsxDEV(Typography, {\n              variant: \"subtitle1\",\n              gutterBottom: true,\n              children: [\"Cavo selezionato: \", selectedCavo.id_cavo]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 587,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(Typography, {\n              variant: \"body2\",\n              gutterBottom: true,\n              children: [\"Metri teorici: \", selectedCavo.metri_teorici || 'N/A']\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 590,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(Typography, {\n              variant: \"body2\",\n              gutterBottom: true,\n              children: [\"Metratura attuale: \", selectedCavo.metratura_reale || '0']\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 593,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(TextField, {\n              margin: \"dense\",\n              name: \"metri_posati\",\n              label: \"Metri posati da aggiungere\",\n              type: \"number\",\n              fullWidth: true,\n              variant: \"outlined\",\n              value: formData.metri_posati,\n              onChange: handleFormChange,\n              required: true,\n              error: !!formErrors.metri_posati,\n              helperText: formErrors.metri_posati,\n              sx: {\n                mt: 2\n              }\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 596,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 586,\n            columnNumber: 15\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 560,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(DialogActions, {\n          children: [/*#__PURE__*/_jsxDEV(Button, {\n            onClick: handleCloseDialog,\n            children: \"Annulla\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 614,\n            columnNumber: 13\n          }, this), selectedCavo && /*#__PURE__*/_jsxDEV(Button, {\n            onClick: handleSave,\n            disabled: loading || !formData.metri_posati,\n            startIcon: loading ? /*#__PURE__*/_jsxDEV(CircularProgress, {\n              size: 20\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 619,\n              columnNumber: 38\n            }, this) : /*#__PURE__*/_jsxDEV(SaveIcon, {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 619,\n              columnNumber: 71\n            }, this),\n            children: \"Salva\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 616,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 613,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 558,\n        columnNumber: 9\n      }, this);\n    } else if (dialogType === 'modificaBobina') {\n      return /*#__PURE__*/_jsxDEV(Dialog, {\n        open: openDialog,\n        onClose: handleCloseDialog,\n        maxWidth: \"md\",\n        fullWidth: true,\n        children: [/*#__PURE__*/_jsxDEV(DialogTitle, {\n          children: \"Modifica Bobina Cavo Posato\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 630,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(DialogContent, {\n          children: caviLoading ? /*#__PURE__*/_jsxDEV(CircularProgress, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 633,\n            columnNumber: 15\n          }, this) : cavi.length === 0 ? /*#__PURE__*/_jsxDEV(Alert, {\n            severity: \"info\",\n            children: \"Nessun cavo disponibile\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 635,\n            columnNumber: 15\n          }, this) : !selectedCavo ? /*#__PURE__*/_jsxDEV(Box, {\n            children: [/*#__PURE__*/_jsxDEV(Typography, {\n              variant: \"subtitle1\",\n              gutterBottom: true,\n              children: \"Seleziona un cavo:\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 638,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(List, {\n              children: cavi.map(cavo => /*#__PURE__*/_jsxDEV(ListItem, {\n                button: true,\n                onClick: () => handleCavoSelect(cavo),\n                children: /*#__PURE__*/_jsxDEV(ListItemText, {\n                  primary: cavo.id_cavo,\n                  secondary: `Bobina attuale: ${cavo.id_bobina || 'Non assegnata'}`\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 648,\n                  columnNumber: 23\n                }, this)\n              }, cavo.id_cavo, false, {\n                fileName: _jsxFileName,\n                lineNumber: 643,\n                columnNumber: 21\n              }, this))\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 641,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 637,\n            columnNumber: 15\n          }, this) : /*#__PURE__*/_jsxDEV(Box, {\n            sx: {\n              mt: 2\n            },\n            children: [/*#__PURE__*/_jsxDEV(Typography, {\n              variant: \"subtitle1\",\n              gutterBottom: true,\n              children: [\"Cavo selezionato: \", selectedCavo.id_cavo]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 658,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(Typography, {\n              variant: \"body2\",\n              gutterBottom: true,\n              children: [\"Bobina attuale: \", selectedCavo.id_bobina || 'Non assegnata']\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 661,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(TextField, {\n              margin: \"dense\",\n              name: \"id_bobina\",\n              label: \"ID Bobina\",\n              fullWidth: true,\n              variant: \"outlined\",\n              value: formData.id_bobina,\n              onChange: handleFormChange,\n              sx: {\n                mt: 2\n              }\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 664,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 657,\n            columnNumber: 15\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 631,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(DialogActions, {\n          children: [/*#__PURE__*/_jsxDEV(Button, {\n            onClick: handleCloseDialog,\n            children: \"Annulla\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 678,\n            columnNumber: 13\n          }, this), selectedCavo && /*#__PURE__*/_jsxDEV(Button, {\n            onClick: handleSave,\n            disabled: loading,\n            startIcon: loading ? /*#__PURE__*/_jsxDEV(CircularProgress, {\n              size: 20\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 683,\n              columnNumber: 38\n            }, this) : /*#__PURE__*/_jsxDEV(SaveIcon, {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 683,\n              columnNumber: 71\n            }, this),\n            children: \"Salva\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 680,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 677,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 629,\n        columnNumber: 9\n      }, this);\n    } else if (dialogType === 'selezionaCavo') {\n      return /*#__PURE__*/_jsxDEV(Dialog, {\n        open: openDialog,\n        onClose: handleCloseDialog,\n        maxWidth: \"md\",\n        fullWidth: true,\n        children: [/*#__PURE__*/_jsxDEV(DialogTitle, {\n          children: \"Modifica Cavo\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 694,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(DialogContent, {\n          children: [/*#__PURE__*/_jsxDEV(Alert, {\n            severity: \"info\",\n            sx: {\n              mb: 2\n            },\n            children: \"Puoi modificare solo i cavi non ancora posati (metratura = 0 e stato diverso da \\\"Installato\\\"). Per modificare cavi gi\\xE0 posati, utilizzare l'opzione \\\"Modifica bobina cavo posato\\\".\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 696,\n            columnNumber: 13\n          }, this), caviLoading ? /*#__PURE__*/_jsxDEV(CircularProgress, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 702,\n            columnNumber: 15\n          }, this) : !selectedCavo ? /*#__PURE__*/_jsxDEV(Box, {\n            sx: {\n              p: 2\n            },\n            children: [/*#__PURE__*/_jsxDEV(Typography, {\n              variant: \"subtitle1\",\n              gutterBottom: true,\n              children: \"Inserisci l'ID del cavo da modificare:\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 705,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(Box, {\n              sx: {\n                display: 'flex',\n                alignItems: 'center',\n                mt: 2\n              },\n              children: [/*#__PURE__*/_jsxDEV(TextField, {\n                fullWidth: true,\n                label: \"ID Cavo\",\n                variant: \"outlined\",\n                value: cavoIdInput,\n                onChange: handleCavoIdInputChange,\n                placeholder: \"Inserisci l'ID del cavo\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 709,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(Button, {\n                variant: \"contained\",\n                color: \"primary\",\n                onClick: handleSearchCavoById,\n                disabled: caviLoading || !cavoIdInput.trim(),\n                sx: {\n                  ml: 2,\n                  minWidth: '120px'\n                },\n                children: caviLoading ? /*#__PURE__*/_jsxDEV(CircularProgress, {\n                  size: 24\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 724,\n                  columnNumber: 36\n                }, this) : \"Cerca\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 717,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 708,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 704,\n            columnNumber: 15\n          }, this) : null]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 695,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(DialogActions, {\n          children: /*#__PURE__*/_jsxDEV(Button, {\n            onClick: handleCloseDialog,\n            children: \"Annulla\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 731,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 730,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 693,\n        columnNumber: 9\n      }, this);\n    } else if (dialogType === 'eliminaCavo') {\n      // Verifica se il cavo selezionato è installato\n      const isInstalled = selectedCavo && (selectedCavo.stato_installazione === 'Installato' || selectedCavo.metratura_reale && selectedCavo.metratura_reale > 0);\n      return /*#__PURE__*/_jsxDEV(Dialog, {\n        open: openDialog,\n        onClose: handleCloseDialog,\n        maxWidth: \"md\",\n        fullWidth: true,\n        children: [/*#__PURE__*/_jsxDEV(DialogTitle, {\n          children: !selectedCavo ? 'Elimina Cavo' : isInstalled ? 'Marca Cavo come SPARE' : 'Elimina Cavo'\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 741,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(DialogContent, {\n          children: caviLoading ? /*#__PURE__*/_jsxDEV(CircularProgress, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 747,\n            columnNumber: 15\n          }, this) : !selectedCavo ? /*#__PURE__*/_jsxDEV(Box, {\n            sx: {\n              p: 2\n            },\n            children: [/*#__PURE__*/_jsxDEV(Typography, {\n              variant: \"subtitle1\",\n              gutterBottom: true,\n              children: \"Inserisci l'ID del cavo da eliminare:\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 750,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(Box, {\n              sx: {\n                display: 'flex',\n                alignItems: 'center',\n                mt: 2\n              },\n              children: [/*#__PURE__*/_jsxDEV(TextField, {\n                fullWidth: true,\n                label: \"ID Cavo\",\n                variant: \"outlined\",\n                value: cavoIdInput,\n                onChange: handleCavoIdInputChange,\n                placeholder: \"Inserisci l'ID del cavo\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 754,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(Button, {\n                variant: \"contained\",\n                color: \"primary\",\n                onClick: handleSearchCavoById,\n                disabled: caviLoading || !cavoIdInput.trim(),\n                sx: {\n                  ml: 2,\n                  minWidth: '120px'\n                },\n                children: caviLoading ? /*#__PURE__*/_jsxDEV(CircularProgress, {\n                  size: 24\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 769,\n                  columnNumber: 36\n                }, this) : \"Cerca\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 762,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 753,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 749,\n            columnNumber: 15\n          }, this) : dialogType === 'eliminaCavo' && isInstalled ? /*#__PURE__*/_jsxDEV(_Fragment, {\n            children: [/*#__PURE__*/_jsxDEV(DialogContentText, {\n              children: [\"Il cavo \", /*#__PURE__*/_jsxDEV(\"strong\", {\n                children: selectedCavo.id_cavo\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 776,\n                columnNumber: 27\n              }, this), \" risulta installato o parzialmente posato.\", selectedCavo.metratura_reale > 0 && /*#__PURE__*/_jsxDEV(_Fragment, {\n                children: [\" Metri posati: \", /*#__PURE__*/_jsxDEV(\"strong\", {\n                  children: [selectedCavo.metratura_reale, \" m\"]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 778,\n                  columnNumber: 38\n                }, this), \".\"]\n              }, void 0, true)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 775,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(DialogContentText, {\n              sx: {\n                mt: 2\n              },\n              children: \"Non \\xE8 possibile eliminarlo definitivamente. Vuoi marcarlo come SPARE/consumato?\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 781,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true) : dialogType === 'eliminaCavo' ? /*#__PURE__*/_jsxDEV(_Fragment, {\n            children: [/*#__PURE__*/_jsxDEV(DialogContentText, {\n              children: [\"Stai per eliminare il cavo \", /*#__PURE__*/_jsxDEV(\"strong\", {\n                children: selectedCavo.id_cavo\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 788,\n                columnNumber: 46\n              }, this), \".\"]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 787,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(FormControl, {\n              component: \"fieldset\",\n              sx: {\n                mt: 2\n              },\n              children: [/*#__PURE__*/_jsxDEV(FormLabel, {\n                component: \"legend\",\n                children: \"Scegli l'operazione da eseguire:\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 792,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(RadioGroup, {\n                value: deleteMode,\n                onChange: e => setDeleteMode(e.target.value),\n                children: [/*#__PURE__*/_jsxDEV(FormControlLabel, {\n                  value: \"spare\",\n                  control: /*#__PURE__*/_jsxDEV(Radio, {}, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 799,\n                    columnNumber: 32\n                  }, this),\n                  label: \"Marca come SPARE (mantiene il cavo nel database ma lo contrassegna come non attivo)\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 797,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(FormControlLabel, {\n                  value: \"delete\",\n                  control: /*#__PURE__*/_jsxDEV(Radio, {}, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 804,\n                    columnNumber: 32\n                  }, this),\n                  label: \"Elimina definitivamente (rimuove completamente il cavo dal database)\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 802,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 793,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 791,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true) : null\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 745,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(DialogActions, {\n          children: [/*#__PURE__*/_jsxDEV(Button, {\n            onClick: handleCloseDialog,\n            children: \"Annulla\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 813,\n            columnNumber: 13\n          }, this), dialogType === 'eliminaCavo' && selectedCavo && /*#__PURE__*/_jsxDEV(Button, {\n            onClick: handleSave,\n            disabled: loading,\n            color: isInstalled ? \"warning\" : \"error\",\n            startIcon: loading ? /*#__PURE__*/_jsxDEV(CircularProgress, {\n              size: 20\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 819,\n              columnNumber: 38\n            }, this) : isInstalled ? /*#__PURE__*/_jsxDEV(WarningIcon, {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 819,\n              columnNumber: 85\n            }, this) : /*#__PURE__*/_jsxDEV(DeleteIcon, {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 819,\n              columnNumber: 103\n            }, this),\n            children: isInstalled ? \"Marca come SPARE\" : deleteMode === 'spare' ? \"Marca come SPARE\" : \"Elimina definitivamente\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 815,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 812,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 740,\n        columnNumber: 9\n      }, this);\n    } else if (dialogType === 'modificaCavo') {\n      return /*#__PURE__*/_jsxDEV(Dialog, {\n        open: openDialog,\n        onClose: handleCloseDialog,\n        maxWidth: \"md\",\n        fullWidth: true,\n        children: [/*#__PURE__*/_jsxDEV(DialogTitle, {\n          children: \"Modifica Cavo\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 830,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(DialogContent, {\n          children: [Object.keys(formErrors).length > 0 && /*#__PURE__*/_jsxDEV(Alert, {\n            severity: \"error\",\n            sx: {\n              mb: 2\n            },\n            children: /*#__PURE__*/_jsxDEV(Typography, {\n              variant: \"subtitle2\",\n              children: \"Ci sono errori nel form. Correggi i campi evidenziati prima di salvare.\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 834,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 833,\n            columnNumber: 15\n          }, this), Object.keys(formWarnings).length > 0 && /*#__PURE__*/_jsxDEV(Alert, {\n            severity: \"warning\",\n            icon: /*#__PURE__*/_jsxDEV(WarningIcon, {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 840,\n              columnNumber: 47\n            }, this),\n            sx: {\n              mb: 2\n            },\n            children: /*#__PURE__*/_jsxDEV(Typography, {\n              variant: \"subtitle2\",\n              children: \"Ci sono alcuni avvisi nel form. Puoi procedere, ma verifica i campi evidenziati.\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 841,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 840,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(Paper, {\n            sx: {\n              p: 2,\n              mb: 2,\n              boxShadow: 0\n            },\n            children: [/*#__PURE__*/_jsxDEV(Typography, {\n              variant: \"h6\",\n              gutterBottom: true,\n              sx: {\n                fontSize: '1.1rem'\n              },\n              children: \"Informazioni Generali\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 848,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Grid, {\n              container: true,\n              spacing: 1,\n              children: [/*#__PURE__*/_jsxDEV(Grid, {\n                item: true,\n                xs: 12,\n                sm: 6,\n                children: /*#__PURE__*/_jsxDEV(TextField, {\n                  name: \"id_cavo\",\n                  label: \"ID Cavo\",\n                  fullWidth: true,\n                  variant: \"outlined\",\n                  value: formData.id_cavo,\n                  disabled: true\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 853,\n                  columnNumber: 19\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 852,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(Grid, {\n                item: true,\n                xs: 12,\n                sm: 6,\n                children: /*#__PURE__*/_jsxDEV(TextField, {\n                  name: \"revisione_ufficiale\",\n                  label: \"Revisione Ufficiale\",\n                  fullWidth: true,\n                  variant: \"outlined\",\n                  value: formData.revisione_ufficiale,\n                  onChange: handleFormChange,\n                  required: true,\n                  error: !!formErrors.revisione_ufficiale,\n                  helperText: formErrors.revisione_ufficiale\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 863,\n                  columnNumber: 19\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 862,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(Grid, {\n                item: true,\n                xs: 12,\n                sm: 6,\n                children: /*#__PURE__*/_jsxDEV(TextField, {\n                  name: \"sistema\",\n                  label: \"Sistema\",\n                  fullWidth: true,\n                  variant: \"outlined\",\n                  value: formData.sistema,\n                  onChange: handleFormChange,\n                  error: !!formErrors.sistema,\n                  helperText: formErrors.sistema\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 876,\n                  columnNumber: 19\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 875,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(Grid, {\n                item: true,\n                xs: 12,\n                sm: 6,\n                children: /*#__PURE__*/_jsxDEV(TextField, {\n                  name: \"utility\",\n                  label: \"Utility\",\n                  fullWidth: true,\n                  variant: \"outlined\",\n                  value: formData.utility,\n                  onChange: handleFormChange,\n                  error: !!formErrors.utility,\n                  helperText: formErrors.utility\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 888,\n                  columnNumber: 19\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 887,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 851,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 847,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Paper, {\n            sx: {\n              p: 2,\n              mb: 2,\n              boxShadow: 0\n            },\n            children: [/*#__PURE__*/_jsxDEV(Typography, {\n              variant: \"h6\",\n              gutterBottom: true,\n              sx: {\n                fontSize: '1.1rem'\n              },\n              children: \"Caratteristiche Tecniche\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 904,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Grid, {\n              container: true,\n              spacing: 1,\n              children: [/*#__PURE__*/_jsxDEV(Grid, {\n                item: true,\n                xs: 12,\n                sm: 6,\n                children: /*#__PURE__*/_jsxDEV(TextField, {\n                  name: \"colore_cavo\",\n                  label: \"Colore Cavo\",\n                  fullWidth: true,\n                  variant: \"outlined\",\n                  value: formData.colore_cavo,\n                  onChange: handleFormChange,\n                  error: !!formErrors.colore_cavo,\n                  helperText: formErrors.colore_cavo\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 909,\n                  columnNumber: 19\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 908,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(Grid, {\n                item: true,\n                xs: 12,\n                sm: 6,\n                children: /*#__PURE__*/_jsxDEV(TextField, {\n                  name: \"tipologia\",\n                  label: \"Tipologia\",\n                  fullWidth: true,\n                  variant: \"outlined\",\n                  value: formData.tipologia,\n                  onChange: handleFormChange,\n                  error: !!formErrors.tipologia,\n                  helperText: formErrors.tipologia\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 921,\n                  columnNumber: 19\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 920,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(Grid, {\n                item: true,\n                xs: 12,\n                sm: 4,\n                children: /*#__PURE__*/_jsxDEV(TextField, {\n                  name: \"n_conduttori\",\n                  label: \"Numero Conduttori\",\n                  fullWidth: true,\n                  variant: \"outlined\",\n                  value: formData.n_conduttori,\n                  onChange: handleFormChange,\n                  error: !!formErrors.n_conduttori,\n                  helperText: formErrors.n_conduttori || formWarnings.n_conduttori,\n                  FormHelperTextProps: {\n                    style: {\n                      color: formWarnings.n_conduttori ? 'orange' : undefined\n                    }\n                  }\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 933,\n                  columnNumber: 19\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 932,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(Grid, {\n                item: true,\n                xs: 12,\n                sm: 4,\n                children: /*#__PURE__*/_jsxDEV(TextField, {\n                  name: \"sezione\",\n                  label: \"Sezione\",\n                  fullWidth: true,\n                  variant: \"outlined\",\n                  value: formData.sezione,\n                  onChange: handleFormChange,\n                  error: !!formErrors.sezione,\n                  helperText: formErrors.sezione || formWarnings.sezione,\n                  FormHelperTextProps: {\n                    style: {\n                      color: formWarnings.sezione ? 'orange' : undefined\n                    }\n                  }\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 948,\n                  columnNumber: 19\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 947,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(Grid, {\n                item: true,\n                xs: 12,\n                sm: 4,\n                children: /*#__PURE__*/_jsxDEV(FormControl, {\n                  fullWidth: true,\n                  children: [/*#__PURE__*/_jsxDEV(InputLabel, {\n                    id: \"sh-label\",\n                    children: \"Schermato (S/N)\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 964,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(Select, {\n                    labelId: \"sh-label\",\n                    name: \"sh\",\n                    value: formData.sh || '',\n                    label: \"Schermato (S/N)\",\n                    onChange: handleFormChange,\n                    children: [/*#__PURE__*/_jsxDEV(MenuItem, {\n                      value: \"S\",\n                      children: \"S\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 972,\n                      columnNumber: 23\n                    }, this), /*#__PURE__*/_jsxDEV(MenuItem, {\n                      value: \"N\",\n                      children: \"N\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 973,\n                      columnNumber: 23\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 965,\n                    columnNumber: 21\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 963,\n                  columnNumber: 19\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 962,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 907,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 903,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Paper, {\n            sx: {\n              p: 2,\n              mb: 2,\n              boxShadow: 0\n            },\n            children: [/*#__PURE__*/_jsxDEV(Typography, {\n              variant: \"h6\",\n              gutterBottom: true,\n              sx: {\n                fontSize: '1.1rem'\n              },\n              children: \"Ubicazione Partenza\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 982,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Grid, {\n              container: true,\n              spacing: 1,\n              children: [/*#__PURE__*/_jsxDEV(Grid, {\n                item: true,\n                xs: 12,\n                sm: 4,\n                children: /*#__PURE__*/_jsxDEV(TextField, {\n                  name: \"ubicazione_partenza\",\n                  label: \"Ubicazione Partenza\",\n                  fullWidth: true,\n                  variant: \"outlined\",\n                  value: formData.ubicazione_partenza,\n                  onChange: handleFormChange,\n                  error: !!formErrors.ubicazione_partenza,\n                  helperText: formErrors.ubicazione_partenza\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 987,\n                  columnNumber: 19\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 986,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(Grid, {\n                item: true,\n                xs: 12,\n                sm: 4,\n                children: /*#__PURE__*/_jsxDEV(TextField, {\n                  name: \"utenza_partenza\",\n                  label: \"Utenza Partenza\",\n                  fullWidth: true,\n                  variant: \"outlined\",\n                  value: formData.utenza_partenza,\n                  onChange: handleFormChange,\n                  error: !!formErrors.utenza_partenza,\n                  helperText: formErrors.utenza_partenza\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 999,\n                  columnNumber: 19\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 998,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(Grid, {\n                item: true,\n                xs: 12,\n                sm: 4,\n                children: /*#__PURE__*/_jsxDEV(TextField, {\n                  name: \"descrizione_utenza_partenza\",\n                  label: \"Descrizione Utenza Partenza\",\n                  fullWidth: true,\n                  variant: \"outlined\",\n                  value: formData.descrizione_utenza_partenza,\n                  onChange: handleFormChange,\n                  error: !!formErrors.descrizione_utenza_partenza,\n                  helperText: formErrors.descrizione_utenza_partenza\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1011,\n                  columnNumber: 19\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1010,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 985,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 981,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Paper, {\n            sx: {\n              p: 2,\n              mb: 2,\n              boxShadow: 0\n            },\n            children: [/*#__PURE__*/_jsxDEV(Typography, {\n              variant: \"h6\",\n              gutterBottom: true,\n              sx: {\n                fontSize: '1.1rem'\n              },\n              children: \"Ubicazione Arrivo\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1027,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Grid, {\n              container: true,\n              spacing: 1,\n              children: [/*#__PURE__*/_jsxDEV(Grid, {\n                item: true,\n                xs: 12,\n                sm: 4,\n                children: /*#__PURE__*/_jsxDEV(TextField, {\n                  name: \"ubicazione_arrivo\",\n                  label: \"Ubicazione Arrivo\",\n                  fullWidth: true,\n                  variant: \"outlined\",\n                  value: formData.ubicazione_arrivo,\n                  onChange: handleFormChange,\n                  error: !!formErrors.ubicazione_arrivo,\n                  helperText: formErrors.ubicazione_arrivo\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1032,\n                  columnNumber: 19\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1031,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(Grid, {\n                item: true,\n                xs: 12,\n                sm: 4,\n                children: /*#__PURE__*/_jsxDEV(TextField, {\n                  name: \"utenza_arrivo\",\n                  label: \"Utenza Arrivo\",\n                  fullWidth: true,\n                  variant: \"outlined\",\n                  value: formData.utenza_arrivo,\n                  onChange: handleFormChange,\n                  error: !!formErrors.utenza_arrivo,\n                  helperText: formErrors.utenza_arrivo\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1044,\n                  columnNumber: 19\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1043,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(Grid, {\n                item: true,\n                xs: 12,\n                sm: 4,\n                children: /*#__PURE__*/_jsxDEV(TextField, {\n                  name: \"descrizione_utenza_arrivo\",\n                  label: \"Descrizione Utenza Arrivo\",\n                  fullWidth: true,\n                  variant: \"outlined\",\n                  value: formData.descrizione_utenza_arrivo,\n                  onChange: handleFormChange,\n                  error: !!formErrors.descrizione_utenza_arrivo,\n                  helperText: formErrors.descrizione_utenza_arrivo\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1056,\n                  columnNumber: 19\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1055,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 1030,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 1026,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Paper, {\n            sx: {\n              p: 2,\n              mb: 2,\n              boxShadow: 0\n            },\n            children: [/*#__PURE__*/_jsxDEV(Typography, {\n              variant: \"h6\",\n              gutterBottom: true,\n              sx: {\n                fontSize: '1.1rem'\n              },\n              children: \"Metratura\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1072,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Grid, {\n              container: true,\n              spacing: 1,\n              children: /*#__PURE__*/_jsxDEV(Grid, {\n                item: true,\n                xs: 12,\n                sm: 6,\n                children: /*#__PURE__*/_jsxDEV(TextField, {\n                  name: \"metri_teorici\",\n                  label: \"Metri Teorici\",\n                  type: \"number\",\n                  fullWidth: true,\n                  variant: \"outlined\",\n                  value: formData.metri_teorici,\n                  onChange: handleFormChange,\n                  required: true,\n                  error: !!formErrors.metri_teorici,\n                  helperText: formErrors.metri_teorici\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1077,\n                  columnNumber: 19\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1076,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1075,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 1071,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 831,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(DialogActions, {\n          children: [/*#__PURE__*/_jsxDEV(Button, {\n            onClick: handleCloseDialog,\n            children: \"Annulla\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 1096,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Button, {\n            onClick: handleSave,\n            disabled: loading,\n            startIcon: loading ? /*#__PURE__*/_jsxDEV(CircularProgress, {\n              size: 20\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1100,\n              columnNumber: 36\n            }, this) : /*#__PURE__*/_jsxDEV(SaveIcon, {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1100,\n              columnNumber: 69\n            }, this),\n            children: \"Salva\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 1097,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 1095,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 829,\n        columnNumber: 9\n      }, this);\n    }\n    return null;\n  };\n  return /*#__PURE__*/_jsxDEV(Box, {\n    sx: {\n      display: 'flex'\n    },\n    children: [/*#__PURE__*/_jsxDEV(Box, {\n      sx: {\n        width: '280px',\n        mr: 3\n      },\n      children: /*#__PURE__*/_jsxDEV(Paper, {\n        sx: {\n          p: 2,\n          mb: 3\n        },\n        children: [/*#__PURE__*/_jsxDEV(Typography, {\n          variant: \"h6\",\n          gutterBottom: true,\n          children: \"Posa Cavi e Collegamenti\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 1117,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Divider, {\n          sx: {\n            mb: 2\n          }\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 1120,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(List, {\n          component: \"nav\",\n          dense: true,\n          children: [/*#__PURE__*/_jsxDEV(ListItemButton, {\n            onClick: () => handleOptionSelect('inserisciMetri'),\n            children: [/*#__PURE__*/_jsxDEV(ListItemIcon, {\n              children: /*#__PURE__*/_jsxDEV(CableIcon, {}, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1124,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1123,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(ListItemText, {\n              primary: \"1. Inserisci metri posati\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1126,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 1122,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(ListItemButton, {\n            onClick: () => handleOptionSelect('modificaBobina'),\n            children: [/*#__PURE__*/_jsxDEV(ListItemIcon, {\n              children: /*#__PURE__*/_jsxDEV(EditIcon, {}, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1131,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1130,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(ListItemText, {\n              primary: \"2. Modifica bobina cavo posato\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1133,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 1129,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(ListItemButton, {\n            onClick: () => handleOptionSelect('aggiungiCavo'),\n            children: [/*#__PURE__*/_jsxDEV(ListItemIcon, {\n              children: /*#__PURE__*/_jsxDEV(AddIcon, {}, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1138,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1137,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(ListItemText, {\n              primary: \"3. Aggiungi nuovo cavo\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1140,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 1136,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(ListItemButton, {\n            onClick: () => handleOptionSelect('modificaCavo'),\n            children: [/*#__PURE__*/_jsxDEV(ListItemIcon, {\n              children: /*#__PURE__*/_jsxDEV(EditIcon, {}, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1145,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1144,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(ListItemText, {\n              primary: \"4. Modifica cavo\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1147,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 1143,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(ListItemButton, {\n            onClick: () => handleOptionSelect('eliminaCavo'),\n            children: [/*#__PURE__*/_jsxDEV(ListItemIcon, {\n              children: /*#__PURE__*/_jsxDEV(DeleteIcon, {}, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1152,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1151,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(ListItemText, {\n              primary: \"5. Elimina cavo\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1154,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 1150,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(ListItemButton, {\n            onClick: () => handleOptionSelect('collegamentoCavo'),\n            children: [/*#__PURE__*/_jsxDEV(ListItemIcon, {\n              children: /*#__PURE__*/_jsxDEV(CableIcon, {}, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1159,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1158,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(ListItemText, {\n              primary: \"6. Collegamento cavo\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1161,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 1157,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 1121,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 1116,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 1115,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Box, {\n      sx: {\n        flexGrow: 1\n      },\n      children: /*#__PURE__*/_jsxDEV(Paper, {\n        sx: {\n          p: 3,\n          minHeight: '300px',\n          display: 'flex',\n          alignItems: 'center',\n          justifyContent: 'center'\n        },\n        children: [!selectedOption && /*#__PURE__*/_jsxDEV(Typography, {\n          variant: \"body1\",\n          children: \"Seleziona un'opzione dal menu a sinistra per iniziare.\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 1171,\n          columnNumber: 13\n        }, this), selectedOption && !openDialog && /*#__PURE__*/_jsxDEV(Box, {\n          sx: {\n            textAlign: 'center'\n          },\n          children: [/*#__PURE__*/_jsxDEV(Typography, {\n            variant: \"h6\",\n            gutterBottom: true,\n            children: [selectedOption === 'inserisciMetri' && 'Inserisci metri posati', selectedOption === 'modificaCavo' && 'Modifica cavo', selectedOption === 'aggiungiCavo' && 'Aggiungi nuovo cavo', selectedOption === 'eliminaCavo' && 'Elimina cavo', selectedOption === 'modificaBobina' && 'Modifica bobina cavo posato', selectedOption === 'collegamentoCavo' && 'Collegamento cavo']\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 1177,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(Typography, {\n            variant: \"body1\",\n            children: \"Caricamento in corso...\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 1185,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(CircularProgress, {\n            sx: {\n              mt: 2\n            }\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 1188,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 1176,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 1169,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 1168,\n      columnNumber: 7\n    }, this), renderDialog()]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 1113,\n    columnNumber: 5\n  }, this);\n};\n_s(PosaCaviCollegamenti, \"iT4/SZ0cNA98E3RS5ZcI0FqlIJU=\", false, function () {\n  return [useNavigate];\n});\n_c = PosaCaviCollegamenti;\nexport default PosaCaviCollegamenti;\nvar _c;\n$RefreshReg$(_c, \"PosaCaviCollegamenti\");", "map": {"version": 3, "names": ["React", "useState", "Box", "Typography", "<PERSON><PERSON>", "Paper", "Divider", "List", "ListItem", "ListItemText", "ListItemIcon", "ListItemButton", "Dialog", "DialogTitle", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "DialogContentText", "DialogActions", "TextField", "FormControl", "InputLabel", "Select", "MenuItem", "Grid", "<PERSON><PERSON>", "CircularProgress", "FormHelperText", "Radio", "RadioGroup", "FormControlLabel", "FormLabel", "Add", "AddIcon", "Edit", "EditIcon", "Delete", "DeleteIcon", "Cable", "CableIcon", "Save", "SaveIcon", "Warning", "WarningIcon", "useNavigate", "caviService", "validateCavoData", "validateField", "isEmpty", "redirectToVisualizzaCavi", "AggiungiCavoForm", "jsxDEV", "_jsxDEV", "Fragment", "_Fragment", "PosaCaviCollegamenti", "cantiereId", "propCantiereId", "onSuccess", "onError", "initialOption", "_s", "console", "log", "navigate", "parseInt", "localStorage", "getItem", "loading", "setLoading", "selectedOption", "setSelectedOption", "openDialog", "setOpenDialog", "dialogType", "setDialogType", "selected<PERSON><PERSON><PERSON>", "setSelectedCavo", "formData", "setFormData", "id_cavo", "metri_posati", "id_bobina", "cavoIdInput", "setCavoIdInput", "formErrors", "setFormErrors", "formWarnings", "setForm<PERSON><PERSON>nings", "cavi", "<PERSON><PERSON><PERSON>", "caviLoading", "setCaviLoading", "deleteMode", "setDeleteMode", "useEffect", "loadCavi", "operationType", "Error", "caviData", "get<PERSON><PERSON>", "cavi<PERSON><PERSON><PERSON><PERSON><PERSON>", "filter", "cavo", "parseFloat", "metratura_reale", "stato_installazione", "error", "errorMessage", "response", "status", "statusText", "data", "detail", "request", "message", "handleOptionSelect", "option", "window", "location", "href", "handleCloseDialog", "handleCavoSelect", "metri_te<PERSON>ci", "handleSearchCavoById", "trim", "find", "c", "handleCavoIdInputChange", "e", "target", "value", "handleFormChange", "name", "additionalParams", "metriTeorici", "result", "prev", "valid", "warning", "handleSave", "isNaN", "updateMetri<PERSON><PERSON><PERSON>", "setTimeout", "updateBobina", "validation", "<PERSON><PERSON><PERSON><PERSON>", "errors", "warnings", "validatedData", "dataToSend", "modificato_manualmente", "timestamp", "updateCavo", "includes", "Object", "keys", "length", "warningMessages", "values", "join", "warn", "isInstalled", "markCavoAsSpare", "<PERSON><PERSON><PERSON><PERSON>", "deleteCavo", "renderDialog", "open", "onClose", "max<PERSON><PERSON><PERSON>", "fullWidth", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "sx", "mt", "isDialog", "onClick", "severity", "variant", "gutterBottom", "map", "button", "primary", "secondary", "tipologia", "ubicazione_partenza", "ubicazione_arrivo", "margin", "label", "type", "onChange", "required", "helperText", "disabled", "startIcon", "size", "mb", "p", "display", "alignItems", "placeholder", "color", "ml", "min<PERSON><PERSON><PERSON>", "component", "control", "icon", "boxShadow", "fontSize", "container", "spacing", "item", "xs", "sm", "revisione_ufficiale", "sistema", "utility", "colore_cavo", "n_conduttori", "FormHelperTextProps", "style", "undefined", "sezione", "id", "labelId", "sh", "utenza_partenza", "descrizione_utenza_partenza", "utenza_arrivo", "descrizione_utenza_arrivo", "width", "mr", "dense", "flexGrow", "minHeight", "justifyContent", "textAlign", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/PycharmProjects/CMS/webapp/frontend/src/components/cavi/PosaCaviCollegamenti.js"], "sourcesContent": ["import React, { useState } from 'react';\nimport {\n  Box,\n  Typography,\n  Button,\n  Paper,\n  Divider,\n  List,\n  ListItem,\n  ListItemText,\n  ListItemIcon,\n  ListItemButton,\n  Dialog,\n  DialogTitle,\n  DialogContent,\n  DialogContentText,\n  DialogActions,\n  TextField,\n  FormControl,\n  InputLabel,\n  Select,\n  MenuItem,\n  Grid,\n  Alert,\n  CircularProgress,\n  FormHelperText,\n  Radio,\n  RadioGroup,\n  FormControlLabel,\n  FormLabel\n} from '@mui/material';\nimport {\n  Add as AddIcon,\n  Edit as EditIcon,\n  Delete as DeleteIcon,\n  Cable as CableIcon,\n  Save as SaveIcon,\n  Warning as WarningIcon\n} from '@mui/icons-material';\nimport { useNavigate } from 'react-router-dom';\nimport caviService from '../../services/caviService';\nimport { validateCavoData, validateField, isEmpty } from '../../utils/validationUtils';\nimport { redirectToVisualizzaCavi } from '../../utils/navigationUtils';\nimport AggiungiCavoForm from './AggiungiCavoForm';\n\nconst PosaCaviCollegamenti = ({ cantiereId: propCantiereId, onSuccess, onError, initialOption = null }) => {\n  // Log del cantiereId all'avvio\n  console.log('PosaCaviCollegamenti - cantiereId da props:', propCantiereId);\n\n  // Aggiungi navigate per la navigazione programmatica\n  const navigate = useNavigate();\n\n  // Se cantiereId non è definito nelle props, prova a recuperarlo dal localStorage\n  const cantiereId = propCantiereId || parseInt(localStorage.getItem('selectedCantiereId'), 10);\n  console.log('PosaCaviCollegamenti - cantiereId effettivo:', cantiereId);\n  const [loading, setLoading] = useState(false);\n  const [selectedOption, setSelectedOption] = useState(null);\n  const [openDialog, setOpenDialog] = useState(false);\n  const [dialogType, setDialogType] = useState('');\n  const [selectedCavo, setSelectedCavo] = useState(null);\n  const [formData, setFormData] = useState({\n    id_cavo: '',\n    metri_posati: '',\n    id_bobina: ''\n  });\n  const [cavoIdInput, setCavoIdInput] = useState('');\n  const [formErrors, setFormErrors] = useState({});\n  const [formWarnings, setFormWarnings] = useState({});\n  const [cavi, setCavi] = useState([]);\n  const [caviLoading, setCaviLoading] = useState(false);\n  const [deleteMode, setDeleteMode] = useState('spare'); // 'spare' o 'delete'\n\n  // Inizializza il componente con l'opzione iniziale se specificata\n  React.useEffect(() => {\n    if (initialOption) {\n      // Imposta direttamente le opzioni invece di chiamare handleOptionSelect\n      // per evitare dipendenze circolari\n      setSelectedOption(initialOption);\n\n      if (initialOption === 'eliminaCavo') {\n        loadCavi('eliminaCavo');\n        setDialogType('eliminaCavo');\n        setOpenDialog(true);\n      } else if (initialOption === 'modificaCavo') {\n        loadCavi('modificaCavo');\n        setDialogType('selezionaCavo');\n        setOpenDialog(true);\n      } else if (initialOption === 'aggiungiCavo') {\n        setDialogType('aggiungiCavo');\n        setOpenDialog(true);\n      }\n    }\n    // eslint-disable-next-line react-hooks/exhaustive-deps\n  }, [initialOption]);\n\n  // Carica i cavi attivi per la selezione\n  const loadCavi = async (operationType) => {\n    try {\n      setCaviLoading(true);\n\n      // Verifica che cantiereId sia valido\n      if (!cantiereId) {\n        throw new Error('ID cantiere non valido o mancante');\n      }\n\n      console.log('Caricamento cavi per cantiere:', cantiereId);\n      const caviData = await caviService.getCavi(cantiereId, 0);\n\n      // Filtra i cavi in base al tipo di operazione\n      if (operationType === 'modificaCavo') {\n        // Per modifica cavo, mostra solo i cavi non posati (metratura_reale = 0 e stato != Installato)\n        const caviNonPosati = caviData.filter(cavo =>\n          parseFloat(cavo.metratura_reale) === 0 &&\n          cavo.stato_installazione !== 'Installato'\n        );\n        setCavi(caviNonPosati);\n      } else {\n        // Per altre operazioni, mostra tutti i cavi\n        setCavi(caviData);\n      }\n    } catch (error) {\n      console.error('Errore nel caricamento dei cavi:', error);\n\n      // Gestione più dettagliata dell'errore\n      let errorMessage = 'Errore nel caricamento dei cavi';\n\n      if (error.response) {\n        // Errore dal server con risposta\n        errorMessage += `: ${error.response.status} ${error.response.statusText}`;\n        if (error.response.data && error.response.data.detail) {\n          errorMessage += ` - ${error.response.data.detail}`;\n        }\n      } else if (error.request) {\n        // Errore di rete senza risposta dal server\n        errorMessage += ': Nessuna risposta dal server. Verifica la connessione di rete.';\n      } else if (error.message) {\n        // Errore con messaggio\n        errorMessage += `: ${error.message}`;\n      }\n\n      onError(errorMessage);\n    } finally {\n      setCaviLoading(false);\n    }\n  };\n\n  // Gestisce la selezione di un'opzione dal menu\n  const handleOptionSelect = (option) => {\n    setSelectedOption(option);\n\n    if (option === 'inserisciMetri' || option === 'modificaBobina') {\n      loadCavi(option);\n      setDialogType(option);\n      setOpenDialog(true);\n    } else if (option === 'aggiungiCavo') {\n      // Apri il dialog per aggiungere un nuovo cavo\n      setDialogType('aggiungiCavo');\n      setOpenDialog(true);\n    } else if (option === 'modificaCavo') {\n      loadCavi('modificaCavo');\n      setDialogType('selezionaCavo');\n      setOpenDialog(true);\n    } else if (option === 'eliminaCavo') {\n      loadCavi('eliminaCavo');\n      setDialogType('eliminaCavo');\n      setOpenDialog(true);\n    } else if (option === 'collegamentoCavo') {\n      // Reindirizza alla pagina di gestione collegamenti\n      window.location.href = `/dashboard/cantieri/${cantiereId}/cavi/collegamenti`;\n    }\n  };\n\n  // Gestisce la chiusura del dialog\n  const handleCloseDialog = () => {\n    console.log('Chiusura dialog...');\n    // Reset dello stato del componente\n    setOpenDialog(false);\n    setSelectedCavo(null);\n    setFormData({\n      id_cavo: '',\n      metri_posati: '',\n      id_bobina: ''\n    });\n    setCavoIdInput('');\n    setFormErrors({});\n    setFormWarnings({});\n    setDeleteMode('spare'); // Reset alla modalità predefinita\n    setLoading(false); // Assicurati che loading sia false quando chiudi il dialog\n\n    // Se è stato specificato un initialOption, notifica il genitore che il dialogo è stato chiuso\n    // ma senza messaggio di errore\n    if (initialOption && onSuccess) {\n      // Chiama onSuccess ma senza messaggio per evitare l'alert\n      onSuccess(null);\n    }\n  };\n\n  // Gestisce la selezione di un cavo\n  const handleCavoSelect = (cavo) => {\n    setSelectedCavo(cavo);\n    if (dialogType === 'inserisciMetri') {\n      setFormData({\n        ...formData,\n        id_cavo: cavo.id_cavo,\n        metri_posati: ''\n      });\n    } else if (dialogType === 'modificaBobina') {\n      setFormData({\n        ...formData,\n        id_cavo: cavo.id_cavo,\n        id_bobina: cavo.id_bobina || ''\n      });\n    } else if (dialogType === 'selezionaCavo') {\n      setDialogType('modificaCavo');\n      setFormData({\n        ...cavo,\n        metri_teorici: cavo.metri_teorici || '',\n        metratura_reale: cavo.metratura_reale || '0'\n      });\n    }\n  };\n\n  // Gestisce la ricerca di un cavo per ID\n  const handleSearchCavoById = async () => {\n    if (!cavoIdInput.trim()) {\n      onError('Inserisci un ID cavo valido');\n      return;\n    }\n\n    try {\n      setCaviLoading(true);\n\n      // Verifica che cantiereId sia valido\n      if (!cantiereId) {\n        throw new Error('ID cantiere non valido o mancante');\n      }\n\n      console.log('Ricerca cavo con ID:', cavoIdInput, 'per cantiere:', cantiereId);\n      const caviData = await caviService.getCavi(cantiereId, 0);\n      const cavo = caviData.find(c => c.id_cavo === cavoIdInput.trim());\n\n      if (!cavo) {\n        onError(`Cavo con ID ${cavoIdInput} non trovato`);\n        return;\n      }\n\n      // Verifica se stiamo cercando un cavo per modificarlo\n      if (dialogType === 'selezionaCavo') {\n        // Verifica che il cavo non sia già posato\n        if (parseFloat(cavo.metratura_reale) > 0 || cavo.stato_installazione === 'Installato') {\n          onError(`Il cavo ${cavo.id_cavo} risulta già posato. Utilizza l'opzione \"Modifica bobina cavo posato\" per modificarlo.`);\n          return;\n        }\n      }\n\n      // Seleziona il cavo trovato\n      handleCavoSelect(cavo);\n    } catch (error) {\n      console.error('Errore nel caricamento dei cavi:', error);\n\n      // Gestione più dettagliata dell'errore\n      let errorMessage = 'Errore nel caricamento dei cavi';\n\n      if (error.response) {\n        // Errore dal server con risposta\n        errorMessage += `: ${error.response.status} ${error.response.statusText}`;\n        if (error.response.data && error.response.data.detail) {\n          errorMessage += ` - ${error.response.data.detail}`;\n        }\n      } else if (error.request) {\n        // Errore di rete senza risposta dal server\n        errorMessage += ': Nessuna risposta dal server. Verifica la connessione di rete.';\n      } else if (error.message) {\n        // Errore con messaggio\n        errorMessage += `: ${error.message}`;\n      }\n\n      onError(errorMessage);\n    } finally {\n      setCaviLoading(false);\n    }\n  };\n\n  // Gestisce il cambio dell'input dell'ID cavo\n  const handleCavoIdInputChange = (e) => {\n    setCavoIdInput(e.target.value);\n  };\n\n  // Gestisce il cambio dei valori nel form con validazione\n  const handleFormChange = (e) => {\n    const { name, value } = e.target;\n\n    // Aggiorna il valore nel form\n    setFormData({\n      ...formData,\n      [name]: value\n    });\n\n    // Valida il campo\n    if (dialogType === 'modificaCavo') {\n      const additionalParams = {};\n      if (name === 'metratura_reale') {\n        additionalParams.metriTeorici = parseFloat(formData.metri_teorici || 0);\n      }\n\n      const result = validateField(name, value, additionalParams);\n\n      // Aggiorna gli errori\n      setFormErrors(prev => ({\n        ...prev,\n        [name]: !result.valid ? result.message : null\n      }));\n\n      // Aggiorna gli avvisi\n      setFormWarnings(prev => ({\n        ...prev,\n        [name]: result.warning ? result.message : null\n      }));\n    }\n  };\n\n  // Gestisce il salvataggio del form con validazione\n  const handleSave = async () => {\n    try {\n      setLoading(true);\n\n      if (dialogType === 'inserisciMetri') {\n        // Valida i metri posati\n        if (isEmpty(formData.metri_posati) || isNaN(parseFloat(formData.metri_posati))) {\n          setFormErrors({ metri_posati: 'Inserire un valore numerico valido' });\n          setLoading(false);\n          return;\n        }\n\n        try {\n          await caviService.updateMetriPosati(\n            cantiereId,\n            formData.id_cavo,\n            parseFloat(formData.metri_posati)\n          );\n          // Prima chiama onSuccess, poi chiudi il dialog\n          onSuccess('Metri posati aggiornati con successo');\n          // Chiudi il dialog\n          handleCloseDialog();\n          // Reindirizza alla pagina di visualizzazione cavi con un ritardo maggiore\n          setTimeout(() => {\n            redirectToVisualizzaCavi(navigate, 1000);\n          }, 500);\n        } catch (error) {\n          console.error('Errore durante l\\'aggiornamento dei metri posati:', error);\n          onError('Errore durante l\\'aggiornamento dei metri posati: ' + (error.message || 'Errore sconosciuto'));\n          setLoading(false);\n        }\n      } else if (dialogType === 'modificaBobina') {\n        try {\n          await caviService.updateBobina(\n            cantiereId,\n            formData.id_cavo,\n            formData.id_bobina\n          );\n          // Prima chiama onSuccess, poi chiudi il dialog\n          onSuccess('Bobina aggiornata con successo');\n          // Chiudi il dialog\n          handleCloseDialog();\n          // Reindirizza alla pagina di visualizzazione cavi con un ritardo maggiore\n          setTimeout(() => {\n            redirectToVisualizzaCavi(navigate, 1000);\n          }, 500);\n        } catch (error) {\n          console.error('Errore durante l\\'aggiornamento della bobina:', error);\n          onError('Errore durante l\\'aggiornamento della bobina: ' + (error.message || 'Errore sconosciuto'));\n          setLoading(false);\n        }\n      } else if (dialogType === 'modificaCavo') {\n        // Validazione completa dei dati del cavo\n        const validation = validateCavoData(formData);\n\n        if (!validation.isValid) {\n          setFormErrors(validation.errors);\n          setFormWarnings(validation.warnings);\n          setLoading(false);\n          return;\n        }\n\n        // Usa i dati validati\n        const validatedData = validation.validatedData;\n\n        // Rimuovi i campi di sistema che non devono essere modificati\n        const dataToSend = { ...validatedData };\n        delete dataToSend.id_bobina; // Rimuovi id_bobina perché è un campo di sistema\n        delete dataToSend.metratura_reale; // Rimuovi metratura_reale perché è un campo di sistema\n        delete dataToSend.modificato_manualmente; // Rimuovi modificato_manualmente perché è un campo di sistema\n        delete dataToSend.timestamp; // Rimuovi timestamp perché è un campo di sistema\n        delete dataToSend.stato_installazione; // Rimuovi stato_installazione perché è un campo di sistema per cavi non posati\n\n        // Imposta modificato_manualmente a 1 per indicare che il cavo è stato modificato manualmente\n        dataToSend.modificato_manualmente = 1;\n\n        console.log('Dati inviati al server:', dataToSend);\n\n        try {\n          console.log('Invio dati al server per aggiornamento cavo:', dataToSend);\n          const result = await caviService.updateCavo(cantiereId, dataToSend.id_cavo, dataToSend);\n          console.log('Risposta dal server dopo aggiornamento cavo:', result);\n\n          // Prima chiama onSuccess, poi chiudi il dialog\n          onSuccess('Cavo modificato con successo');\n          // Chiudi il dialog\n          handleCloseDialog();\n          // Reindirizza alla pagina di visualizzazione cavi con un ritardo maggiore\n          setTimeout(() => {\n            redirectToVisualizzaCavi(navigate, 1000);\n          }, 500);\n          return;\n        } catch (error) {\n          console.error('Errore durante l\\'aggiornamento del cavo:', error);\n\n          // Gestione più dettagliata dell'errore\n          let errorMessage = 'Errore durante l\\'aggiornamento del cavo';\n\n          if (error.response) {\n            // Errore dal server con risposta\n            errorMessage += `: ${error.response.status} ${error.response.statusText}`;\n            if (error.response.data && error.response.data.detail) {\n              errorMessage += ` - ${error.response.data.detail}`;\n            }\n          } else if (error.request) {\n            // Errore di rete senza risposta dal server\n            errorMessage += ': Nessuna risposta dal server. Verifica la connessione di rete.';\n\n            // Anche se c'è un errore di rete, la modifica potrebbe essere stata salvata\n            // Quindi consideriamo l'operazione come riuscita\n            console.log('Considerando l\\'operazione come riuscita nonostante l\\'errore di rete');\n            // Prima chiama onSuccess, poi chiudi il dialog\n            onSuccess('Cavo modificato con successo (la connessione potrebbe essere instabile)');\n            // Chiudi il dialog\n            handleCloseDialog();\n            // Reindirizza alla pagina di visualizzazione cavi con un ritardo maggiore\n            setTimeout(() => {\n              redirectToVisualizzaCavi(navigate, 1000);\n            }, 500);\n            return;\n          } else if (error.message) {\n            // Errore con messaggio\n            errorMessage += `: ${error.message}`;\n\n            // Se il messaggio indica che la modifica potrebbe essere stata salvata comunque\n            if (error.message.includes('La modifica potrebbe essere stata salvata')) {\n              console.log('Considerando l\\'operazione come riuscita nonostante l\\'errore');\n              handleCloseDialog();\n              onSuccess('Cavo modificato con successo (la connessione potrebbe essere instabile)');\n              // Reindirizza alla pagina di visualizzazione cavi con un ritardo maggiore\n              redirectToVisualizzaCavi(navigate, 1000);\n              return;\n            }\n          }\n\n          onError(errorMessage);\n          setLoading(false);\n          return;\n        }\n\n        // Mostra avvisi se presenti\n        if (Object.keys(validation.warnings).length > 0) {\n          const warningMessages = Object.values(validation.warnings).join('\\n');\n          console.warn('Avvisi durante il salvataggio:', warningMessages);\n        }\n      } else if (dialogType === 'eliminaCavo') {\n        // Verifica se il cavo è installato\n        const isInstalled = selectedCavo.stato_installazione === 'Installato' || (selectedCavo.metratura_reale && selectedCavo.metratura_reale > 0);\n\n        if (isInstalled) {\n          // Se è installato, marca solo come SPARE\n          console.log('Marcando cavo installato come SPARE:', selectedCavo.id_cavo);\n          try {\n            // Prima prova con markCavoAsSpare\n            const result = await caviService.markCavoAsSpare(cantiereId, selectedCavo.id_cavo);\n            console.log('Risultato marcatura SPARE:', result);\n            console.log('Nuovo valore modificato_manualmente:', result.modificato_manualmente);\n            // Chiudi il dialog prima di chiamare onSuccess\n            handleCloseDialog();\n            onSuccess(`Cavo ${selectedCavo.id_cavo} marcato come SPARE con successo`);\n            // Reindirizza alla pagina di visualizzazione cavi con un ritardo\n            redirectToVisualizzaCavi(navigate, 500);\n          } catch (markError) {\n            console.error('Errore con markCavoAsSpare, tentativo con deleteCavo mode=spare:', markError);\n            // Se fallisce, prova con deleteCavo mode=spare\n            const result = await caviService.deleteCavo(cantiereId, selectedCavo.id_cavo, 'spare');\n            console.log('Risultato marcatura SPARE con deleteCavo:', result);\n            // Chiudi il dialog prima di chiamare onSuccess\n            handleCloseDialog();\n            onSuccess(`Cavo ${selectedCavo.id_cavo} marcato come SPARE con successo`);\n          }\n        } else {\n          // Se non è installato, usa la modalità selezionata (SPARE o DELETE)\n          console.log('Eliminando cavo non installato con modalità:', deleteMode);\n          const result = await caviService.deleteCavo(cantiereId, selectedCavo.id_cavo, deleteMode);\n          console.log('Risultato eliminazione/marcatura:', result);\n          // Chiudi il dialog prima di chiamare onSuccess\n          handleCloseDialog();\n          onSuccess(`Cavo ${selectedCavo.id_cavo} ${deleteMode === 'spare' ? 'marcato come SPARE' : 'eliminato'} con successo`);\n          // Reindirizza alla pagina di visualizzazione cavi con un ritardo\n          redirectToVisualizzaCavi(navigate, 500);\n        }\n      }\n\n      // Non chiamare handleCloseDialog() qui, perché il dialog verrà chiuso dal genitore\n      // quando viene chiamato onSuccess()\n    } catch (error) {\n      console.error('Errore durante l\\'operazione:', error);\n\n      // Gestione più dettagliata dell'errore\n      let errorMessage = 'Errore sconosciuto';\n\n      if (error.detail) {\n        // Errore dal backend con dettaglio\n        errorMessage = error.detail;\n      } else if (error.message) {\n        // Errore con messaggio\n        errorMessage = error.message;\n      } else if (typeof error === 'string') {\n        // Errore come stringa\n        errorMessage = error;\n      }\n\n      onError('Errore durante l\\'operazione: ' + errorMessage);\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  // Renderizza il dialog in base al tipo\n  const renderDialog = () => {\n    if (dialogType === 'aggiungiCavo') {\n      return (\n        <Dialog open={openDialog} onClose={handleCloseDialog} maxWidth=\"md\" fullWidth>\n          <DialogTitle>Aggiungi Nuovo Cavo</DialogTitle>\n          <DialogContent>\n            <Box sx={{ mt: 1 }}>\n              <AggiungiCavoForm\n                cantiereId={cantiereId}\n                onSuccess={(message) => {\n                  onSuccess(message);\n                  handleCloseDialog();\n                }}\n                onError={onError}\n                isDialog={true}\n              />\n            </Box>\n          </DialogContent>\n          <DialogActions>\n            <Button onClick={handleCloseDialog}>Annulla</Button>\n          </DialogActions>\n        </Dialog>\n      );\n    } else if (dialogType === 'inserisciMetri') {\n      return (\n        <Dialog open={openDialog} onClose={handleCloseDialog} maxWidth=\"md\" fullWidth>\n          <DialogTitle>Inserisci Metri Posati</DialogTitle>\n          <DialogContent>\n            {caviLoading ? (\n              <CircularProgress />\n            ) : cavi.length === 0 ? (\n              <Alert severity=\"info\">Nessun cavo disponibile</Alert>\n            ) : !selectedCavo ? (\n              <Box>\n                <Typography variant=\"subtitle1\" gutterBottom>\n                  Seleziona un cavo:\n                </Typography>\n                <List>\n                  {cavi.map((cavo) => (\n                    <ListItem\n                      button\n                      key={cavo.id_cavo}\n                      onClick={() => handleCavoSelect(cavo)}\n                    >\n                      <ListItemText\n                        primary={cavo.id_cavo}\n                        secondary={`${cavo.tipologia || 'N/A'} - Da: ${cavo.ubicazione_partenza || 'N/A'} A: ${cavo.ubicazione_arrivo || 'N/A'}`}\n                      />\n                    </ListItem>\n                  ))}\n                </List>\n              </Box>\n            ) : (\n              <Box sx={{ mt: 2 }}>\n                <Typography variant=\"subtitle1\" gutterBottom>\n                  Cavo selezionato: {selectedCavo.id_cavo}\n                </Typography>\n                <Typography variant=\"body2\" gutterBottom>\n                  Metri teorici: {selectedCavo.metri_teorici || 'N/A'}\n                </Typography>\n                <Typography variant=\"body2\" gutterBottom>\n                  Metratura attuale: {selectedCavo.metratura_reale || '0'}\n                </Typography>\n                <TextField\n                  margin=\"dense\"\n                  name=\"metri_posati\"\n                  label=\"Metri posati da aggiungere\"\n                  type=\"number\"\n                  fullWidth\n                  variant=\"outlined\"\n                  value={formData.metri_posati}\n                  onChange={handleFormChange}\n                  required\n                  error={!!formErrors.metri_posati}\n                  helperText={formErrors.metri_posati}\n                  sx={{ mt: 2 }}\n                />\n              </Box>\n            )}\n          </DialogContent>\n          <DialogActions>\n            <Button onClick={handleCloseDialog}>Annulla</Button>\n            {selectedCavo && (\n              <Button\n                onClick={handleSave}\n                disabled={loading || !formData.metri_posati}\n                startIcon={loading ? <CircularProgress size={20} /> : <SaveIcon />}\n              >\n                Salva\n              </Button>\n            )}\n          </DialogActions>\n        </Dialog>\n      );\n    } else if (dialogType === 'modificaBobina') {\n      return (\n        <Dialog open={openDialog} onClose={handleCloseDialog} maxWidth=\"md\" fullWidth>\n          <DialogTitle>Modifica Bobina Cavo Posato</DialogTitle>\n          <DialogContent>\n            {caviLoading ? (\n              <CircularProgress />\n            ) : cavi.length === 0 ? (\n              <Alert severity=\"info\">Nessun cavo disponibile</Alert>\n            ) : !selectedCavo ? (\n              <Box>\n                <Typography variant=\"subtitle1\" gutterBottom>\n                  Seleziona un cavo:\n                </Typography>\n                <List>\n                  {cavi.map((cavo) => (\n                    <ListItem\n                      button\n                      key={cavo.id_cavo}\n                      onClick={() => handleCavoSelect(cavo)}\n                    >\n                      <ListItemText\n                        primary={cavo.id_cavo}\n                        secondary={`Bobina attuale: ${cavo.id_bobina || 'Non assegnata'}`}\n                      />\n                    </ListItem>\n                  ))}\n                </List>\n              </Box>\n            ) : (\n              <Box sx={{ mt: 2 }}>\n                <Typography variant=\"subtitle1\" gutterBottom>\n                  Cavo selezionato: {selectedCavo.id_cavo}\n                </Typography>\n                <Typography variant=\"body2\" gutterBottom>\n                  Bobina attuale: {selectedCavo.id_bobina || 'Non assegnata'}\n                </Typography>\n                <TextField\n                  margin=\"dense\"\n                  name=\"id_bobina\"\n                  label=\"ID Bobina\"\n                  fullWidth\n                  variant=\"outlined\"\n                  value={formData.id_bobina}\n                  onChange={handleFormChange}\n                  sx={{ mt: 2 }}\n                />\n              </Box>\n            )}\n          </DialogContent>\n          <DialogActions>\n            <Button onClick={handleCloseDialog}>Annulla</Button>\n            {selectedCavo && (\n              <Button\n                onClick={handleSave}\n                disabled={loading}\n                startIcon={loading ? <CircularProgress size={20} /> : <SaveIcon />}\n              >\n                Salva\n              </Button>\n            )}\n          </DialogActions>\n        </Dialog>\n      );\n    } else if (dialogType === 'selezionaCavo') {\n      return (\n        <Dialog open={openDialog} onClose={handleCloseDialog} maxWidth=\"md\" fullWidth>\n          <DialogTitle>Modifica Cavo</DialogTitle>\n          <DialogContent>\n            <Alert severity=\"info\" sx={{ mb: 2 }}>\n              Puoi modificare solo i cavi non ancora posati (metratura = 0 e stato diverso da \"Installato\").\n              Per modificare cavi già posati, utilizzare l'opzione \"Modifica bobina cavo posato\".\n            </Alert>\n\n            {caviLoading ? (\n              <CircularProgress />\n            ) : !selectedCavo ? (\n              <Box sx={{ p: 2 }}>\n                <Typography variant=\"subtitle1\" gutterBottom>\n                  Inserisci l'ID del cavo da modificare:\n                </Typography>\n                <Box sx={{ display: 'flex', alignItems: 'center', mt: 2 }}>\n                  <TextField\n                    fullWidth\n                    label=\"ID Cavo\"\n                    variant=\"outlined\"\n                    value={cavoIdInput}\n                    onChange={handleCavoIdInputChange}\n                    placeholder=\"Inserisci l'ID del cavo\"\n                  />\n                  <Button\n                    variant=\"contained\"\n                    color=\"primary\"\n                    onClick={handleSearchCavoById}\n                    disabled={caviLoading || !cavoIdInput.trim()}\n                    sx={{ ml: 2, minWidth: '120px' }}\n                  >\n                    {caviLoading ? <CircularProgress size={24} /> : \"Cerca\"}\n                  </Button>\n                </Box>\n              </Box>\n            ) : null}\n          </DialogContent>\n          <DialogActions>\n            <Button onClick={handleCloseDialog}>Annulla</Button>\n          </DialogActions>\n        </Dialog>\n      );\n    } else if (dialogType === 'eliminaCavo') {\n      // Verifica se il cavo selezionato è installato\n      const isInstalled = selectedCavo && (selectedCavo.stato_installazione === 'Installato' || (selectedCavo.metratura_reale && selectedCavo.metratura_reale > 0));\n\n      return (\n        <Dialog open={openDialog} onClose={handleCloseDialog} maxWidth=\"md\" fullWidth>\n          <DialogTitle>\n            {!selectedCavo ? 'Elimina Cavo' :\n             isInstalled ? 'Marca Cavo come SPARE' : 'Elimina Cavo'}\n          </DialogTitle>\n          <DialogContent>\n            {caviLoading ? (\n              <CircularProgress />\n            ) : !selectedCavo ? (\n              <Box sx={{ p: 2 }}>\n                <Typography variant=\"subtitle1\" gutterBottom>\n                  Inserisci l'ID del cavo da eliminare:\n                </Typography>\n                <Box sx={{ display: 'flex', alignItems: 'center', mt: 2 }}>\n                  <TextField\n                    fullWidth\n                    label=\"ID Cavo\"\n                    variant=\"outlined\"\n                    value={cavoIdInput}\n                    onChange={handleCavoIdInputChange}\n                    placeholder=\"Inserisci l'ID del cavo\"\n                  />\n                  <Button\n                    variant=\"contained\"\n                    color=\"primary\"\n                    onClick={handleSearchCavoById}\n                    disabled={caviLoading || !cavoIdInput.trim()}\n                    sx={{ ml: 2, minWidth: '120px' }}\n                  >\n                    {caviLoading ? <CircularProgress size={24} /> : \"Cerca\"}\n                  </Button>\n                </Box>\n              </Box>\n            ) : dialogType === 'eliminaCavo' && isInstalled ? (\n              <>\n                <DialogContentText>\n                  Il cavo <strong>{selectedCavo.id_cavo}</strong> risulta installato o parzialmente posato.\n                  {selectedCavo.metratura_reale > 0 && (\n                    <> Metri posati: <strong>{selectedCavo.metratura_reale} m</strong>.</>\n                  )}\n                </DialogContentText>\n                <DialogContentText sx={{ mt: 2 }}>\n                  Non è possibile eliminarlo definitivamente. Vuoi marcarlo come SPARE/consumato?\n                </DialogContentText>\n              </>\n            ) : dialogType === 'eliminaCavo' ? (\n              <>\n                <DialogContentText>\n                  Stai per eliminare il cavo <strong>{selectedCavo.id_cavo}</strong>.\n                </DialogContentText>\n\n                <FormControl component=\"fieldset\" sx={{ mt: 2 }}>\n                  <FormLabel component=\"legend\">Scegli l'operazione da eseguire:</FormLabel>\n                  <RadioGroup\n                    value={deleteMode}\n                    onChange={(e) => setDeleteMode(e.target.value)}\n                  >\n                    <FormControlLabel\n                      value=\"spare\"\n                      control={<Radio />}\n                      label=\"Marca come SPARE (mantiene il cavo nel database ma lo contrassegna come non attivo)\"\n                    />\n                    <FormControlLabel\n                      value=\"delete\"\n                      control={<Radio />}\n                      label=\"Elimina definitivamente (rimuove completamente il cavo dal database)\"\n                    />\n                  </RadioGroup>\n                </FormControl>\n              </>\n            ) : null}\n          </DialogContent>\n          <DialogActions>\n            <Button onClick={handleCloseDialog}>Annulla</Button>\n            {dialogType === 'eliminaCavo' && selectedCavo && (\n              <Button\n                onClick={handleSave}\n                disabled={loading}\n                color={isInstalled ? \"warning\" : \"error\"}\n                startIcon={loading ? <CircularProgress size={20} /> : isInstalled ? <WarningIcon /> : <DeleteIcon />}\n              >\n                {isInstalled ? \"Marca come SPARE\" : (deleteMode === 'spare' ? \"Marca come SPARE\" : \"Elimina definitivamente\")}\n              </Button>\n            )}\n          </DialogActions>\n        </Dialog>\n      );\n    } else if (dialogType === 'modificaCavo') {\n      return (\n        <Dialog open={openDialog} onClose={handleCloseDialog} maxWidth=\"md\" fullWidth>\n          <DialogTitle>Modifica Cavo</DialogTitle>\n          <DialogContent>\n            {Object.keys(formErrors).length > 0 && (\n              <Alert severity=\"error\" sx={{ mb: 2 }}>\n                <Typography variant=\"subtitle2\">\n                  Ci sono errori nel form. Correggi i campi evidenziati prima di salvare.\n                </Typography>\n              </Alert>\n            )}\n            {Object.keys(formWarnings).length > 0 && (\n              <Alert severity=\"warning\" icon={<WarningIcon />} sx={{ mb: 2 }}>\n                <Typography variant=\"subtitle2\">\n                  Ci sono alcuni avvisi nel form. Puoi procedere, ma verifica i campi evidenziati.\n                </Typography>\n              </Alert>\n            )}\n            {/* Sezione Informazioni Generali */}\n            <Paper sx={{ p: 2, mb: 2, boxShadow: 0 }}>\n              <Typography variant=\"h6\" gutterBottom sx={{ fontSize: '1.1rem' }}>\n                Informazioni Generali\n              </Typography>\n              <Grid container spacing={1}>\n                <Grid item xs={12} sm={6}>\n                  <TextField\n                    name=\"id_cavo\"\n                    label=\"ID Cavo\"\n                    fullWidth\n                    variant=\"outlined\"\n                    value={formData.id_cavo}\n                    disabled\n                  />\n                </Grid>\n                <Grid item xs={12} sm={6}>\n                  <TextField\n                    name=\"revisione_ufficiale\"\n                    label=\"Revisione Ufficiale\"\n                    fullWidth\n                    variant=\"outlined\"\n                    value={formData.revisione_ufficiale}\n                    onChange={handleFormChange}\n                    required\n                    error={!!formErrors.revisione_ufficiale}\n                    helperText={formErrors.revisione_ufficiale}\n                  />\n                </Grid>\n                <Grid item xs={12} sm={6}>\n                  <TextField\n                    name=\"sistema\"\n                    label=\"Sistema\"\n                    fullWidth\n                    variant=\"outlined\"\n                    value={formData.sistema}\n                    onChange={handleFormChange}\n                    error={!!formErrors.sistema}\n                    helperText={formErrors.sistema}\n                  />\n                </Grid>\n                <Grid item xs={12} sm={6}>\n                  <TextField\n                    name=\"utility\"\n                    label=\"Utility\"\n                    fullWidth\n                    variant=\"outlined\"\n                    value={formData.utility}\n                    onChange={handleFormChange}\n                    error={!!formErrors.utility}\n                    helperText={formErrors.utility}\n                  />\n                </Grid>\n              </Grid>\n            </Paper>\n\n            {/* Sezione Caratteristiche Tecniche */}\n            <Paper sx={{ p: 2, mb: 2, boxShadow: 0 }}>\n              <Typography variant=\"h6\" gutterBottom sx={{ fontSize: '1.1rem' }}>\n                Caratteristiche Tecniche\n              </Typography>\n              <Grid container spacing={1}>\n                <Grid item xs={12} sm={6}>\n                  <TextField\n                    name=\"colore_cavo\"\n                    label=\"Colore Cavo\"\n                    fullWidth\n                    variant=\"outlined\"\n                    value={formData.colore_cavo}\n                    onChange={handleFormChange}\n                    error={!!formErrors.colore_cavo}\n                    helperText={formErrors.colore_cavo}\n                  />\n                </Grid>\n                <Grid item xs={12} sm={6}>\n                  <TextField\n                    name=\"tipologia\"\n                    label=\"Tipologia\"\n                    fullWidth\n                    variant=\"outlined\"\n                    value={formData.tipologia}\n                    onChange={handleFormChange}\n                    error={!!formErrors.tipologia}\n                    helperText={formErrors.tipologia}\n                  />\n                </Grid>\n                <Grid item xs={12} sm={4}>\n                  <TextField\n                    name=\"n_conduttori\"\n                    label=\"Numero Conduttori\"\n                    fullWidth\n                    variant=\"outlined\"\n                    value={formData.n_conduttori}\n                    onChange={handleFormChange}\n                    error={!!formErrors.n_conduttori}\n                    helperText={formErrors.n_conduttori || formWarnings.n_conduttori}\n                    FormHelperTextProps={{\n                      style: { color: formWarnings.n_conduttori ? 'orange' : undefined }\n                    }}\n                  />\n                </Grid>\n                <Grid item xs={12} sm={4}>\n                  <TextField\n                    name=\"sezione\"\n                    label=\"Sezione\"\n                    fullWidth\n                    variant=\"outlined\"\n                    value={formData.sezione}\n                    onChange={handleFormChange}\n                    error={!!formErrors.sezione}\n                    helperText={formErrors.sezione || formWarnings.sezione}\n                    FormHelperTextProps={{\n                      style: { color: formWarnings.sezione ? 'orange' : undefined }\n                    }}\n                  />\n                </Grid>\n                <Grid item xs={12} sm={4}>\n                  <FormControl fullWidth>\n                    <InputLabel id=\"sh-label\">Schermato (S/N)</InputLabel>\n                    <Select\n                      labelId=\"sh-label\"\n                      name=\"sh\"\n                      value={formData.sh || ''}\n                      label=\"Schermato (S/N)\"\n                      onChange={handleFormChange}\n                    >\n                      <MenuItem value=\"S\">S</MenuItem>\n                      <MenuItem value=\"N\">N</MenuItem>\n                    </Select>\n                  </FormControl>\n                </Grid>\n              </Grid>\n            </Paper>\n\n            {/* Sezione Ubicazione Partenza */}\n            <Paper sx={{ p: 2, mb: 2, boxShadow: 0 }}>\n              <Typography variant=\"h6\" gutterBottom sx={{ fontSize: '1.1rem' }}>\n                Ubicazione Partenza\n              </Typography>\n              <Grid container spacing={1}>\n                <Grid item xs={12} sm={4}>\n                  <TextField\n                    name=\"ubicazione_partenza\"\n                    label=\"Ubicazione Partenza\"\n                    fullWidth\n                    variant=\"outlined\"\n                    value={formData.ubicazione_partenza}\n                    onChange={handleFormChange}\n                    error={!!formErrors.ubicazione_partenza}\n                    helperText={formErrors.ubicazione_partenza}\n                  />\n                </Grid>\n                <Grid item xs={12} sm={4}>\n                  <TextField\n                    name=\"utenza_partenza\"\n                    label=\"Utenza Partenza\"\n                    fullWidth\n                    variant=\"outlined\"\n                    value={formData.utenza_partenza}\n                    onChange={handleFormChange}\n                    error={!!formErrors.utenza_partenza}\n                    helperText={formErrors.utenza_partenza}\n                  />\n                </Grid>\n                <Grid item xs={12} sm={4}>\n                  <TextField\n                    name=\"descrizione_utenza_partenza\"\n                    label=\"Descrizione Utenza Partenza\"\n                    fullWidth\n                    variant=\"outlined\"\n                    value={formData.descrizione_utenza_partenza}\n                    onChange={handleFormChange}\n                    error={!!formErrors.descrizione_utenza_partenza}\n                    helperText={formErrors.descrizione_utenza_partenza}\n                  />\n                </Grid>\n              </Grid>\n            </Paper>\n\n            {/* Sezione Ubicazione Arrivo */}\n            <Paper sx={{ p: 2, mb: 2, boxShadow: 0 }}>\n              <Typography variant=\"h6\" gutterBottom sx={{ fontSize: '1.1rem' }}>\n                Ubicazione Arrivo\n              </Typography>\n              <Grid container spacing={1}>\n                <Grid item xs={12} sm={4}>\n                  <TextField\n                    name=\"ubicazione_arrivo\"\n                    label=\"Ubicazione Arrivo\"\n                    fullWidth\n                    variant=\"outlined\"\n                    value={formData.ubicazione_arrivo}\n                    onChange={handleFormChange}\n                    error={!!formErrors.ubicazione_arrivo}\n                    helperText={formErrors.ubicazione_arrivo}\n                  />\n                </Grid>\n                <Grid item xs={12} sm={4}>\n                  <TextField\n                    name=\"utenza_arrivo\"\n                    label=\"Utenza Arrivo\"\n                    fullWidth\n                    variant=\"outlined\"\n                    value={formData.utenza_arrivo}\n                    onChange={handleFormChange}\n                    error={!!formErrors.utenza_arrivo}\n                    helperText={formErrors.utenza_arrivo}\n                  />\n                </Grid>\n                <Grid item xs={12} sm={4}>\n                  <TextField\n                    name=\"descrizione_utenza_arrivo\"\n                    label=\"Descrizione Utenza Arrivo\"\n                    fullWidth\n                    variant=\"outlined\"\n                    value={formData.descrizione_utenza_arrivo}\n                    onChange={handleFormChange}\n                    error={!!formErrors.descrizione_utenza_arrivo}\n                    helperText={formErrors.descrizione_utenza_arrivo}\n                  />\n                </Grid>\n              </Grid>\n            </Paper>\n\n            {/* Sezione Metratura */}\n            <Paper sx={{ p: 2, mb: 2, boxShadow: 0 }}>\n              <Typography variant=\"h6\" gutterBottom sx={{ fontSize: '1.1rem' }}>\n                Metratura\n              </Typography>\n              <Grid container spacing={1}>\n                <Grid item xs={12} sm={6}>\n                  <TextField\n                    name=\"metri_teorici\"\n                    label=\"Metri Teorici\"\n                    type=\"number\"\n                    fullWidth\n                    variant=\"outlined\"\n                    value={formData.metri_teorici}\n                    onChange={handleFormChange}\n                    required\n                    error={!!formErrors.metri_teorici}\n                    helperText={formErrors.metri_teorici}\n                  />\n                </Grid>\n                {/* I campi metratura_reale e responsabile_posa sono stati rimossi perché sono campi di sistema */}\n              </Grid>\n            </Paper>\n            {/* Campo ID Bobina e Stato Installazione sono campi di sistema gestiti automaticamente */}\n          </DialogContent>\n          <DialogActions>\n            <Button onClick={handleCloseDialog}>Annulla</Button>\n            <Button\n              onClick={handleSave}\n              disabled={loading}\n              startIcon={loading ? <CircularProgress size={20} /> : <SaveIcon />}\n            >\n              Salva\n            </Button>\n          </DialogActions>\n        </Dialog>\n      );\n    }\n\n    return null;\n  };\n\n  return (\n    <Box sx={{ display: 'flex' }}>\n      {/* Menu a cascata nella sidebar */}\n      <Box sx={{ width: '280px', mr: 3 }}>\n        <Paper sx={{ p: 2, mb: 3 }}>\n          <Typography variant=\"h6\" gutterBottom>\n            Posa Cavi e Collegamenti\n          </Typography>\n          <Divider sx={{ mb: 2 }} />\n          <List component=\"nav\" dense>\n            <ListItemButton onClick={() => handleOptionSelect('inserisciMetri')}>\n              <ListItemIcon>\n                <CableIcon />\n              </ListItemIcon>\n              <ListItemText primary=\"1. Inserisci metri posati\" />\n            </ListItemButton>\n\n            <ListItemButton onClick={() => handleOptionSelect('modificaBobina')}>\n              <ListItemIcon>\n                <EditIcon />\n              </ListItemIcon>\n              <ListItemText primary=\"2. Modifica bobina cavo posato\" />\n            </ListItemButton>\n\n            <ListItemButton onClick={() => handleOptionSelect('aggiungiCavo')}>\n              <ListItemIcon>\n                <AddIcon />\n              </ListItemIcon>\n              <ListItemText primary=\"3. Aggiungi nuovo cavo\" />\n            </ListItemButton>\n\n            <ListItemButton onClick={() => handleOptionSelect('modificaCavo')}>\n              <ListItemIcon>\n                <EditIcon />\n              </ListItemIcon>\n              <ListItemText primary=\"4. Modifica cavo\" />\n            </ListItemButton>\n\n            <ListItemButton onClick={() => handleOptionSelect('eliminaCavo')}>\n              <ListItemIcon>\n                <DeleteIcon />\n              </ListItemIcon>\n              <ListItemText primary=\"5. Elimina cavo\" />\n            </ListItemButton>\n\n            <ListItemButton onClick={() => handleOptionSelect('collegamentoCavo')}>\n              <ListItemIcon>\n                <CableIcon />\n              </ListItemIcon>\n              <ListItemText primary=\"6. Collegamento cavo\" />\n            </ListItemButton>\n          </List>\n        </Paper>\n      </Box>\n\n      {/* Area principale per il contenuto */}\n      <Box sx={{ flexGrow: 1 }}>\n        <Paper sx={{ p: 3, minHeight: '300px', display: 'flex', alignItems: 'center', justifyContent: 'center' }}>\n          {!selectedOption && (\n            <Typography variant=\"body1\">\n              Seleziona un'opzione dal menu a sinistra per iniziare.\n            </Typography>\n          )}\n          {selectedOption && !openDialog && (\n            <Box sx={{ textAlign: 'center' }}>\n              <Typography variant=\"h6\" gutterBottom>\n                {selectedOption === 'inserisciMetri' && 'Inserisci metri posati'}\n                {selectedOption === 'modificaCavo' && 'Modifica cavo'}\n                {selectedOption === 'aggiungiCavo' && 'Aggiungi nuovo cavo'}\n                {selectedOption === 'eliminaCavo' && 'Elimina cavo'}\n                {selectedOption === 'modificaBobina' && 'Modifica bobina cavo posato'}\n                {selectedOption === 'collegamentoCavo' && 'Collegamento cavo'}\n              </Typography>\n              <Typography variant=\"body1\">\n                Caricamento in corso...\n              </Typography>\n              <CircularProgress sx={{ mt: 2 }} />\n            </Box>\n          )}\n        </Paper>\n      </Box>\n\n      {renderDialog()}\n    </Box>\n  );\n};\n\nexport default PosaCaviCollegamenti;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,QAAQ,OAAO;AACvC,SACEC,GAAG,EACHC,UAAU,EACVC,MAAM,EACNC,KAAK,EACLC,OAAO,EACPC,IAAI,EACJC,QAAQ,EACRC,YAAY,EACZC,YAAY,EACZC,cAAc,EACdC,MAAM,EACNC,WAAW,EACXC,aAAa,EACbC,iBAAiB,EACjBC,aAAa,EACbC,SAAS,EACTC,WAAW,EACXC,UAAU,EACVC,MAAM,EACNC,QAAQ,EACRC,IAAI,EACJC,KAAK,EACLC,gBAAgB,EAChBC,cAAc,EACdC,KAAK,EACLC,UAAU,EACVC,gBAAgB,EAChBC,SAAS,QACJ,eAAe;AACtB,SACEC,GAAG,IAAIC,OAAO,EACdC,IAAI,IAAIC,QAAQ,EAChBC,MAAM,IAAIC,UAAU,EACpBC,KAAK,IAAIC,SAAS,EAClBC,IAAI,IAAIC,QAAQ,EAChBC,OAAO,IAAIC,WAAW,QACjB,qBAAqB;AAC5B,SAASC,WAAW,QAAQ,kBAAkB;AAC9C,OAAOC,WAAW,MAAM,4BAA4B;AACpD,SAASC,gBAAgB,EAAEC,aAAa,EAAEC,OAAO,QAAQ,6BAA6B;AACtF,SAASC,wBAAwB,QAAQ,6BAA6B;AACtE,OAAOC,gBAAgB,MAAM,oBAAoB;AAAC,SAAAC,MAAA,IAAAC,OAAA,EAAAC,QAAA,IAAAC,SAAA;AAElD,MAAMC,oBAAoB,GAAGA,CAAC;EAAEC,UAAU,EAAEC,cAAc;EAAEC,SAAS;EAAEC,OAAO;EAAEC,aAAa,GAAG;AAAK,CAAC,KAAK;EAAAC,EAAA;EACzG;EACAC,OAAO,CAACC,GAAG,CAAC,6CAA6C,EAAEN,cAAc,CAAC;;EAE1E;EACA,MAAMO,QAAQ,GAAGpB,WAAW,CAAC,CAAC;;EAE9B;EACA,MAAMY,UAAU,GAAGC,cAAc,IAAIQ,QAAQ,CAACC,YAAY,CAACC,OAAO,CAAC,oBAAoB,CAAC,EAAE,EAAE,CAAC;EAC7FL,OAAO,CAACC,GAAG,CAAC,8CAA8C,EAAEP,UAAU,CAAC;EACvE,MAAM,CAACY,OAAO,EAAEC,UAAU,CAAC,GAAGlE,QAAQ,CAAC,KAAK,CAAC;EAC7C,MAAM,CAACmE,cAAc,EAAEC,iBAAiB,CAAC,GAAGpE,QAAQ,CAAC,IAAI,CAAC;EAC1D,MAAM,CAACqE,UAAU,EAAEC,aAAa,CAAC,GAAGtE,QAAQ,CAAC,KAAK,CAAC;EACnD,MAAM,CAACuE,UAAU,EAAEC,aAAa,CAAC,GAAGxE,QAAQ,CAAC,EAAE,CAAC;EAChD,MAAM,CAACyE,YAAY,EAAEC,eAAe,CAAC,GAAG1E,QAAQ,CAAC,IAAI,CAAC;EACtD,MAAM,CAAC2E,QAAQ,EAAEC,WAAW,CAAC,GAAG5E,QAAQ,CAAC;IACvC6E,OAAO,EAAE,EAAE;IACXC,YAAY,EAAE,EAAE;IAChBC,SAAS,EAAE;EACb,CAAC,CAAC;EACF,MAAM,CAACC,WAAW,EAAEC,cAAc,CAAC,GAAGjF,QAAQ,CAAC,EAAE,CAAC;EAClD,MAAM,CAACkF,UAAU,EAAEC,aAAa,CAAC,GAAGnF,QAAQ,CAAC,CAAC,CAAC,CAAC;EAChD,MAAM,CAACoF,YAAY,EAAEC,eAAe,CAAC,GAAGrF,QAAQ,CAAC,CAAC,CAAC,CAAC;EACpD,MAAM,CAACsF,IAAI,EAAEC,OAAO,CAAC,GAAGvF,QAAQ,CAAC,EAAE,CAAC;EACpC,MAAM,CAACwF,WAAW,EAAEC,cAAc,CAAC,GAAGzF,QAAQ,CAAC,KAAK,CAAC;EACrD,MAAM,CAAC0F,UAAU,EAAEC,aAAa,CAAC,GAAG3F,QAAQ,CAAC,OAAO,CAAC,CAAC,CAAC;;EAEvD;EACAD,KAAK,CAAC6F,SAAS,CAAC,MAAM;IACpB,IAAInC,aAAa,EAAE;MACjB;MACA;MACAW,iBAAiB,CAACX,aAAa,CAAC;MAEhC,IAAIA,aAAa,KAAK,aAAa,EAAE;QACnCoC,QAAQ,CAAC,aAAa,CAAC;QACvBrB,aAAa,CAAC,aAAa,CAAC;QAC5BF,aAAa,CAAC,IAAI,CAAC;MACrB,CAAC,MAAM,IAAIb,aAAa,KAAK,cAAc,EAAE;QAC3CoC,QAAQ,CAAC,cAAc,CAAC;QACxBrB,aAAa,CAAC,eAAe,CAAC;QAC9BF,aAAa,CAAC,IAAI,CAAC;MACrB,CAAC,MAAM,IAAIb,aAAa,KAAK,cAAc,EAAE;QAC3Ce,aAAa,CAAC,cAAc,CAAC;QAC7BF,aAAa,CAAC,IAAI,CAAC;MACrB;IACF;IACA;EACF,CAAC,EAAE,CAACb,aAAa,CAAC,CAAC;;EAEnB;EACA,MAAMoC,QAAQ,GAAG,MAAOC,aAAa,IAAK;IACxC,IAAI;MACFL,cAAc,CAAC,IAAI,CAAC;;MAEpB;MACA,IAAI,CAACpC,UAAU,EAAE;QACf,MAAM,IAAI0C,KAAK,CAAC,mCAAmC,CAAC;MACtD;MAEApC,OAAO,CAACC,GAAG,CAAC,gCAAgC,EAAEP,UAAU,CAAC;MACzD,MAAM2C,QAAQ,GAAG,MAAMtD,WAAW,CAACuD,OAAO,CAAC5C,UAAU,EAAE,CAAC,CAAC;;MAEzD;MACA,IAAIyC,aAAa,KAAK,cAAc,EAAE;QACpC;QACA,MAAMI,aAAa,GAAGF,QAAQ,CAACG,MAAM,CAACC,IAAI,IACxCC,UAAU,CAACD,IAAI,CAACE,eAAe,CAAC,KAAK,CAAC,IACtCF,IAAI,CAACG,mBAAmB,KAAK,YAC/B,CAAC;QACDhB,OAAO,CAACW,aAAa,CAAC;MACxB,CAAC,MAAM;QACL;QACAX,OAAO,CAACS,QAAQ,CAAC;MACnB;IACF,CAAC,CAAC,OAAOQ,KAAK,EAAE;MACd7C,OAAO,CAAC6C,KAAK,CAAC,kCAAkC,EAAEA,KAAK,CAAC;;MAExD;MACA,IAAIC,YAAY,GAAG,iCAAiC;MAEpD,IAAID,KAAK,CAACE,QAAQ,EAAE;QAClB;QACAD,YAAY,IAAI,KAAKD,KAAK,CAACE,QAAQ,CAACC,MAAM,IAAIH,KAAK,CAACE,QAAQ,CAACE,UAAU,EAAE;QACzE,IAAIJ,KAAK,CAACE,QAAQ,CAACG,IAAI,IAAIL,KAAK,CAACE,QAAQ,CAACG,IAAI,CAACC,MAAM,EAAE;UACrDL,YAAY,IAAI,MAAMD,KAAK,CAACE,QAAQ,CAACG,IAAI,CAACC,MAAM,EAAE;QACpD;MACF,CAAC,MAAM,IAAIN,KAAK,CAACO,OAAO,EAAE;QACxB;QACAN,YAAY,IAAI,iEAAiE;MACnF,CAAC,MAAM,IAAID,KAAK,CAACQ,OAAO,EAAE;QACxB;QACAP,YAAY,IAAI,KAAKD,KAAK,CAACQ,OAAO,EAAE;MACtC;MAEAxD,OAAO,CAACiD,YAAY,CAAC;IACvB,CAAC,SAAS;MACRhB,cAAc,CAAC,KAAK,CAAC;IACvB;EACF,CAAC;;EAED;EACA,MAAMwB,kBAAkB,GAAIC,MAAM,IAAK;IACrC9C,iBAAiB,CAAC8C,MAAM,CAAC;IAEzB,IAAIA,MAAM,KAAK,gBAAgB,IAAIA,MAAM,KAAK,gBAAgB,EAAE;MAC9DrB,QAAQ,CAACqB,MAAM,CAAC;MAChB1C,aAAa,CAAC0C,MAAM,CAAC;MACrB5C,aAAa,CAAC,IAAI,CAAC;IACrB,CAAC,MAAM,IAAI4C,MAAM,KAAK,cAAc,EAAE;MACpC;MACA1C,aAAa,CAAC,cAAc,CAAC;MAC7BF,aAAa,CAAC,IAAI,CAAC;IACrB,CAAC,MAAM,IAAI4C,MAAM,KAAK,cAAc,EAAE;MACpCrB,QAAQ,CAAC,cAAc,CAAC;MACxBrB,aAAa,CAAC,eAAe,CAAC;MAC9BF,aAAa,CAAC,IAAI,CAAC;IACrB,CAAC,MAAM,IAAI4C,MAAM,KAAK,aAAa,EAAE;MACnCrB,QAAQ,CAAC,aAAa,CAAC;MACvBrB,aAAa,CAAC,aAAa,CAAC;MAC5BF,aAAa,CAAC,IAAI,CAAC;IACrB,CAAC,MAAM,IAAI4C,MAAM,KAAK,kBAAkB,EAAE;MACxC;MACAC,MAAM,CAACC,QAAQ,CAACC,IAAI,GAAG,uBAAuBhE,UAAU,oBAAoB;IAC9E;EACF,CAAC;;EAED;EACA,MAAMiE,iBAAiB,GAAGA,CAAA,KAAM;IAC9B3D,OAAO,CAACC,GAAG,CAAC,oBAAoB,CAAC;IACjC;IACAU,aAAa,CAAC,KAAK,CAAC;IACpBI,eAAe,CAAC,IAAI,CAAC;IACrBE,WAAW,CAAC;MACVC,OAAO,EAAE,EAAE;MACXC,YAAY,EAAE,EAAE;MAChBC,SAAS,EAAE;IACb,CAAC,CAAC;IACFE,cAAc,CAAC,EAAE,CAAC;IAClBE,aAAa,CAAC,CAAC,CAAC,CAAC;IACjBE,eAAe,CAAC,CAAC,CAAC,CAAC;IACnBM,aAAa,CAAC,OAAO,CAAC,CAAC,CAAC;IACxBzB,UAAU,CAAC,KAAK,CAAC,CAAC,CAAC;;IAEnB;IACA;IACA,IAAIT,aAAa,IAAIF,SAAS,EAAE;MAC9B;MACAA,SAAS,CAAC,IAAI,CAAC;IACjB;EACF,CAAC;;EAED;EACA,MAAMgE,gBAAgB,GAAInB,IAAI,IAAK;IACjC1B,eAAe,CAAC0B,IAAI,CAAC;IACrB,IAAI7B,UAAU,KAAK,gBAAgB,EAAE;MACnCK,WAAW,CAAC;QACV,GAAGD,QAAQ;QACXE,OAAO,EAAEuB,IAAI,CAACvB,OAAO;QACrBC,YAAY,EAAE;MAChB,CAAC,CAAC;IACJ,CAAC,MAAM,IAAIP,UAAU,KAAK,gBAAgB,EAAE;MAC1CK,WAAW,CAAC;QACV,GAAGD,QAAQ;QACXE,OAAO,EAAEuB,IAAI,CAACvB,OAAO;QACrBE,SAAS,EAAEqB,IAAI,CAACrB,SAAS,IAAI;MAC/B,CAAC,CAAC;IACJ,CAAC,MAAM,IAAIR,UAAU,KAAK,eAAe,EAAE;MACzCC,aAAa,CAAC,cAAc,CAAC;MAC7BI,WAAW,CAAC;QACV,GAAGwB,IAAI;QACPoB,aAAa,EAAEpB,IAAI,CAACoB,aAAa,IAAI,EAAE;QACvClB,eAAe,EAAEF,IAAI,CAACE,eAAe,IAAI;MAC3C,CAAC,CAAC;IACJ;EACF,CAAC;;EAED;EACA,MAAMmB,oBAAoB,GAAG,MAAAA,CAAA,KAAY;IACvC,IAAI,CAACzC,WAAW,CAAC0C,IAAI,CAAC,CAAC,EAAE;MACvBlE,OAAO,CAAC,6BAA6B,CAAC;MACtC;IACF;IAEA,IAAI;MACFiC,cAAc,CAAC,IAAI,CAAC;;MAEpB;MACA,IAAI,CAACpC,UAAU,EAAE;QACf,MAAM,IAAI0C,KAAK,CAAC,mCAAmC,CAAC;MACtD;MAEApC,OAAO,CAACC,GAAG,CAAC,sBAAsB,EAAEoB,WAAW,EAAE,eAAe,EAAE3B,UAAU,CAAC;MAC7E,MAAM2C,QAAQ,GAAG,MAAMtD,WAAW,CAACuD,OAAO,CAAC5C,UAAU,EAAE,CAAC,CAAC;MACzD,MAAM+C,IAAI,GAAGJ,QAAQ,CAAC2B,IAAI,CAACC,CAAC,IAAIA,CAAC,CAAC/C,OAAO,KAAKG,WAAW,CAAC0C,IAAI,CAAC,CAAC,CAAC;MAEjE,IAAI,CAACtB,IAAI,EAAE;QACT5C,OAAO,CAAC,eAAewB,WAAW,cAAc,CAAC;QACjD;MACF;;MAEA;MACA,IAAIT,UAAU,KAAK,eAAe,EAAE;QAClC;QACA,IAAI8B,UAAU,CAACD,IAAI,CAACE,eAAe,CAAC,GAAG,CAAC,IAAIF,IAAI,CAACG,mBAAmB,KAAK,YAAY,EAAE;UACrF/C,OAAO,CAAC,WAAW4C,IAAI,CAACvB,OAAO,wFAAwF,CAAC;UACxH;QACF;MACF;;MAEA;MACA0C,gBAAgB,CAACnB,IAAI,CAAC;IACxB,CAAC,CAAC,OAAOI,KAAK,EAAE;MACd7C,OAAO,CAAC6C,KAAK,CAAC,kCAAkC,EAAEA,KAAK,CAAC;;MAExD;MACA,IAAIC,YAAY,GAAG,iCAAiC;MAEpD,IAAID,KAAK,CAACE,QAAQ,EAAE;QAClB;QACAD,YAAY,IAAI,KAAKD,KAAK,CAACE,QAAQ,CAACC,MAAM,IAAIH,KAAK,CAACE,QAAQ,CAACE,UAAU,EAAE;QACzE,IAAIJ,KAAK,CAACE,QAAQ,CAACG,IAAI,IAAIL,KAAK,CAACE,QAAQ,CAACG,IAAI,CAACC,MAAM,EAAE;UACrDL,YAAY,IAAI,MAAMD,KAAK,CAACE,QAAQ,CAACG,IAAI,CAACC,MAAM,EAAE;QACpD;MACF,CAAC,MAAM,IAAIN,KAAK,CAACO,OAAO,EAAE;QACxB;QACAN,YAAY,IAAI,iEAAiE;MACnF,CAAC,MAAM,IAAID,KAAK,CAACQ,OAAO,EAAE;QACxB;QACAP,YAAY,IAAI,KAAKD,KAAK,CAACQ,OAAO,EAAE;MACtC;MAEAxD,OAAO,CAACiD,YAAY,CAAC;IACvB,CAAC,SAAS;MACRhB,cAAc,CAAC,KAAK,CAAC;IACvB;EACF,CAAC;;EAED;EACA,MAAMoC,uBAAuB,GAAIC,CAAC,IAAK;IACrC7C,cAAc,CAAC6C,CAAC,CAACC,MAAM,CAACC,KAAK,CAAC;EAChC,CAAC;;EAED;EACA,MAAMC,gBAAgB,GAAIH,CAAC,IAAK;IAC9B,MAAM;MAAEI,IAAI;MAAEF;IAAM,CAAC,GAAGF,CAAC,CAACC,MAAM;;IAEhC;IACAnD,WAAW,CAAC;MACV,GAAGD,QAAQ;MACX,CAACuD,IAAI,GAAGF;IACV,CAAC,CAAC;;IAEF;IACA,IAAIzD,UAAU,KAAK,cAAc,EAAE;MACjC,MAAM4D,gBAAgB,GAAG,CAAC,CAAC;MAC3B,IAAID,IAAI,KAAK,iBAAiB,EAAE;QAC9BC,gBAAgB,CAACC,YAAY,GAAG/B,UAAU,CAAC1B,QAAQ,CAAC6C,aAAa,IAAI,CAAC,CAAC;MACzE;MAEA,MAAMa,MAAM,GAAGzF,aAAa,CAACsF,IAAI,EAAEF,KAAK,EAAEG,gBAAgB,CAAC;;MAE3D;MACAhD,aAAa,CAACmD,IAAI,KAAK;QACrB,GAAGA,IAAI;QACP,CAACJ,IAAI,GAAG,CAACG,MAAM,CAACE,KAAK,GAAGF,MAAM,CAACrB,OAAO,GAAG;MAC3C,CAAC,CAAC,CAAC;;MAEH;MACA3B,eAAe,CAACiD,IAAI,KAAK;QACvB,GAAGA,IAAI;QACP,CAACJ,IAAI,GAAGG,MAAM,CAACG,OAAO,GAAGH,MAAM,CAACrB,OAAO,GAAG;MAC5C,CAAC,CAAC,CAAC;IACL;EACF,CAAC;;EAED;EACA,MAAMyB,UAAU,GAAG,MAAAA,CAAA,KAAY;IAC7B,IAAI;MACFvE,UAAU,CAAC,IAAI,CAAC;MAEhB,IAAIK,UAAU,KAAK,gBAAgB,EAAE;QACnC;QACA,IAAI1B,OAAO,CAAC8B,QAAQ,CAACG,YAAY,CAAC,IAAI4D,KAAK,CAACrC,UAAU,CAAC1B,QAAQ,CAACG,YAAY,CAAC,CAAC,EAAE;UAC9EK,aAAa,CAAC;YAAEL,YAAY,EAAE;UAAqC,CAAC,CAAC;UACrEZ,UAAU,CAAC,KAAK,CAAC;UACjB;QACF;QAEA,IAAI;UACF,MAAMxB,WAAW,CAACiG,iBAAiB,CACjCtF,UAAU,EACVsB,QAAQ,CAACE,OAAO,EAChBwB,UAAU,CAAC1B,QAAQ,CAACG,YAAY,CAClC,CAAC;UACD;UACAvB,SAAS,CAAC,sCAAsC,CAAC;UACjD;UACA+D,iBAAiB,CAAC,CAAC;UACnB;UACAsB,UAAU,CAAC,MAAM;YACf9F,wBAAwB,CAACe,QAAQ,EAAE,IAAI,CAAC;UAC1C,CAAC,EAAE,GAAG,CAAC;QACT,CAAC,CAAC,OAAO2C,KAAK,EAAE;UACd7C,OAAO,CAAC6C,KAAK,CAAC,mDAAmD,EAAEA,KAAK,CAAC;UACzEhD,OAAO,CAAC,oDAAoD,IAAIgD,KAAK,CAACQ,OAAO,IAAI,oBAAoB,CAAC,CAAC;UACvG9C,UAAU,CAAC,KAAK,CAAC;QACnB;MACF,CAAC,MAAM,IAAIK,UAAU,KAAK,gBAAgB,EAAE;QAC1C,IAAI;UACF,MAAM7B,WAAW,CAACmG,YAAY,CAC5BxF,UAAU,EACVsB,QAAQ,CAACE,OAAO,EAChBF,QAAQ,CAACI,SACX,CAAC;UACD;UACAxB,SAAS,CAAC,gCAAgC,CAAC;UAC3C;UACA+D,iBAAiB,CAAC,CAAC;UACnB;UACAsB,UAAU,CAAC,MAAM;YACf9F,wBAAwB,CAACe,QAAQ,EAAE,IAAI,CAAC;UAC1C,CAAC,EAAE,GAAG,CAAC;QACT,CAAC,CAAC,OAAO2C,KAAK,EAAE;UACd7C,OAAO,CAAC6C,KAAK,CAAC,+CAA+C,EAAEA,KAAK,CAAC;UACrEhD,OAAO,CAAC,gDAAgD,IAAIgD,KAAK,CAACQ,OAAO,IAAI,oBAAoB,CAAC,CAAC;UACnG9C,UAAU,CAAC,KAAK,CAAC;QACnB;MACF,CAAC,MAAM,IAAIK,UAAU,KAAK,cAAc,EAAE;QACxC;QACA,MAAMuE,UAAU,GAAGnG,gBAAgB,CAACgC,QAAQ,CAAC;QAE7C,IAAI,CAACmE,UAAU,CAACC,OAAO,EAAE;UACvB5D,aAAa,CAAC2D,UAAU,CAACE,MAAM,CAAC;UAChC3D,eAAe,CAACyD,UAAU,CAACG,QAAQ,CAAC;UACpC/E,UAAU,CAAC,KAAK,CAAC;UACjB;QACF;;QAEA;QACA,MAAMgF,aAAa,GAAGJ,UAAU,CAACI,aAAa;;QAE9C;QACA,MAAMC,UAAU,GAAG;UAAE,GAAGD;QAAc,CAAC;QACvC,OAAOC,UAAU,CAACpE,SAAS,CAAC,CAAC;QAC7B,OAAOoE,UAAU,CAAC7C,eAAe,CAAC,CAAC;QACnC,OAAO6C,UAAU,CAACC,sBAAsB,CAAC,CAAC;QAC1C,OAAOD,UAAU,CAACE,SAAS,CAAC,CAAC;QAC7B,OAAOF,UAAU,CAAC5C,mBAAmB,CAAC,CAAC;;QAEvC;QACA4C,UAAU,CAACC,sBAAsB,GAAG,CAAC;QAErCzF,OAAO,CAACC,GAAG,CAAC,yBAAyB,EAAEuF,UAAU,CAAC;QAElD,IAAI;UACFxF,OAAO,CAACC,GAAG,CAAC,8CAA8C,EAAEuF,UAAU,CAAC;UACvE,MAAMd,MAAM,GAAG,MAAM3F,WAAW,CAAC4G,UAAU,CAACjG,UAAU,EAAE8F,UAAU,CAACtE,OAAO,EAAEsE,UAAU,CAAC;UACvFxF,OAAO,CAACC,GAAG,CAAC,8CAA8C,EAAEyE,MAAM,CAAC;;UAEnE;UACA9E,SAAS,CAAC,8BAA8B,CAAC;UACzC;UACA+D,iBAAiB,CAAC,CAAC;UACnB;UACAsB,UAAU,CAAC,MAAM;YACf9F,wBAAwB,CAACe,QAAQ,EAAE,IAAI,CAAC;UAC1C,CAAC,EAAE,GAAG,CAAC;UACP;QACF,CAAC,CAAC,OAAO2C,KAAK,EAAE;UACd7C,OAAO,CAAC6C,KAAK,CAAC,2CAA2C,EAAEA,KAAK,CAAC;;UAEjE;UACA,IAAIC,YAAY,GAAG,0CAA0C;UAE7D,IAAID,KAAK,CAACE,QAAQ,EAAE;YAClB;YACAD,YAAY,IAAI,KAAKD,KAAK,CAACE,QAAQ,CAACC,MAAM,IAAIH,KAAK,CAACE,QAAQ,CAACE,UAAU,EAAE;YACzE,IAAIJ,KAAK,CAACE,QAAQ,CAACG,IAAI,IAAIL,KAAK,CAACE,QAAQ,CAACG,IAAI,CAACC,MAAM,EAAE;cACrDL,YAAY,IAAI,MAAMD,KAAK,CAACE,QAAQ,CAACG,IAAI,CAACC,MAAM,EAAE;YACpD;UACF,CAAC,MAAM,IAAIN,KAAK,CAACO,OAAO,EAAE;YACxB;YACAN,YAAY,IAAI,iEAAiE;;YAEjF;YACA;YACA9C,OAAO,CAACC,GAAG,CAAC,uEAAuE,CAAC;YACpF;YACAL,SAAS,CAAC,yEAAyE,CAAC;YACpF;YACA+D,iBAAiB,CAAC,CAAC;YACnB;YACAsB,UAAU,CAAC,MAAM;cACf9F,wBAAwB,CAACe,QAAQ,EAAE,IAAI,CAAC;YAC1C,CAAC,EAAE,GAAG,CAAC;YACP;UACF,CAAC,MAAM,IAAI2C,KAAK,CAACQ,OAAO,EAAE;YACxB;YACAP,YAAY,IAAI,KAAKD,KAAK,CAACQ,OAAO,EAAE;;YAEpC;YACA,IAAIR,KAAK,CAACQ,OAAO,CAACuC,QAAQ,CAAC,2CAA2C,CAAC,EAAE;cACvE5F,OAAO,CAACC,GAAG,CAAC,+DAA+D,CAAC;cAC5E0D,iBAAiB,CAAC,CAAC;cACnB/D,SAAS,CAAC,yEAAyE,CAAC;cACpF;cACAT,wBAAwB,CAACe,QAAQ,EAAE,IAAI,CAAC;cACxC;YACF;UACF;UAEAL,OAAO,CAACiD,YAAY,CAAC;UACrBvC,UAAU,CAAC,KAAK,CAAC;UACjB;QACF;;QAEA;QACA,IAAIsF,MAAM,CAACC,IAAI,CAACX,UAAU,CAACG,QAAQ,CAAC,CAACS,MAAM,GAAG,CAAC,EAAE;UAC/C,MAAMC,eAAe,GAAGH,MAAM,CAACI,MAAM,CAACd,UAAU,CAACG,QAAQ,CAAC,CAACY,IAAI,CAAC,IAAI,CAAC;UACrElG,OAAO,CAACmG,IAAI,CAAC,gCAAgC,EAAEH,eAAe,CAAC;QACjE;MACF,CAAC,MAAM,IAAIpF,UAAU,KAAK,aAAa,EAAE;QACvC;QACA,MAAMwF,WAAW,GAAGtF,YAAY,CAAC8B,mBAAmB,KAAK,YAAY,IAAK9B,YAAY,CAAC6B,eAAe,IAAI7B,YAAY,CAAC6B,eAAe,GAAG,CAAE;QAE3I,IAAIyD,WAAW,EAAE;UACf;UACApG,OAAO,CAACC,GAAG,CAAC,sCAAsC,EAAEa,YAAY,CAACI,OAAO,CAAC;UACzE,IAAI;YACF;YACA,MAAMwD,MAAM,GAAG,MAAM3F,WAAW,CAACsH,eAAe,CAAC3G,UAAU,EAAEoB,YAAY,CAACI,OAAO,CAAC;YAClFlB,OAAO,CAACC,GAAG,CAAC,4BAA4B,EAAEyE,MAAM,CAAC;YACjD1E,OAAO,CAACC,GAAG,CAAC,sCAAsC,EAAEyE,MAAM,CAACe,sBAAsB,CAAC;YAClF;YACA9B,iBAAiB,CAAC,CAAC;YACnB/D,SAAS,CAAC,QAAQkB,YAAY,CAACI,OAAO,kCAAkC,CAAC;YACzE;YACA/B,wBAAwB,CAACe,QAAQ,EAAE,GAAG,CAAC;UACzC,CAAC,CAAC,OAAOoG,SAAS,EAAE;YAClBtG,OAAO,CAAC6C,KAAK,CAAC,kEAAkE,EAAEyD,SAAS,CAAC;YAC5F;YACA,MAAM5B,MAAM,GAAG,MAAM3F,WAAW,CAACwH,UAAU,CAAC7G,UAAU,EAAEoB,YAAY,CAACI,OAAO,EAAE,OAAO,CAAC;YACtFlB,OAAO,CAACC,GAAG,CAAC,2CAA2C,EAAEyE,MAAM,CAAC;YAChE;YACAf,iBAAiB,CAAC,CAAC;YACnB/D,SAAS,CAAC,QAAQkB,YAAY,CAACI,OAAO,kCAAkC,CAAC;UAC3E;QACF,CAAC,MAAM;UACL;UACAlB,OAAO,CAACC,GAAG,CAAC,8CAA8C,EAAE8B,UAAU,CAAC;UACvE,MAAM2C,MAAM,GAAG,MAAM3F,WAAW,CAACwH,UAAU,CAAC7G,UAAU,EAAEoB,YAAY,CAACI,OAAO,EAAEa,UAAU,CAAC;UACzF/B,OAAO,CAACC,GAAG,CAAC,mCAAmC,EAAEyE,MAAM,CAAC;UACxD;UACAf,iBAAiB,CAAC,CAAC;UACnB/D,SAAS,CAAC,QAAQkB,YAAY,CAACI,OAAO,IAAIa,UAAU,KAAK,OAAO,GAAG,oBAAoB,GAAG,WAAW,eAAe,CAAC;UACrH;UACA5C,wBAAwB,CAACe,QAAQ,EAAE,GAAG,CAAC;QACzC;MACF;;MAEA;MACA;IACF,CAAC,CAAC,OAAO2C,KAAK,EAAE;MACd7C,OAAO,CAAC6C,KAAK,CAAC,+BAA+B,EAAEA,KAAK,CAAC;;MAErD;MACA,IAAIC,YAAY,GAAG,oBAAoB;MAEvC,IAAID,KAAK,CAACM,MAAM,EAAE;QAChB;QACAL,YAAY,GAAGD,KAAK,CAACM,MAAM;MAC7B,CAAC,MAAM,IAAIN,KAAK,CAACQ,OAAO,EAAE;QACxB;QACAP,YAAY,GAAGD,KAAK,CAACQ,OAAO;MAC9B,CAAC,MAAM,IAAI,OAAOR,KAAK,KAAK,QAAQ,EAAE;QACpC;QACAC,YAAY,GAAGD,KAAK;MACtB;MAEAhD,OAAO,CAAC,gCAAgC,GAAGiD,YAAY,CAAC;IAC1D,CAAC,SAAS;MACRvC,UAAU,CAAC,KAAK,CAAC;IACnB;EACF,CAAC;;EAED;EACA,MAAMiG,YAAY,GAAGA,CAAA,KAAM;IACzB,IAAI5F,UAAU,KAAK,cAAc,EAAE;MACjC,oBACEtB,OAAA,CAACtC,MAAM;QAACyJ,IAAI,EAAE/F,UAAW;QAACgG,OAAO,EAAE/C,iBAAkB;QAACgD,QAAQ,EAAC,IAAI;QAACC,SAAS;QAAAC,QAAA,gBAC3EvH,OAAA,CAACrC,WAAW;UAAA4J,QAAA,EAAC;QAAmB;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAa,CAAC,eAC9C3H,OAAA,CAACpC,aAAa;UAAA2J,QAAA,eACZvH,OAAA,CAAChD,GAAG;YAAC4K,EAAE,EAAE;cAAEC,EAAE,EAAE;YAAE,CAAE;YAAAN,QAAA,eACjBvH,OAAA,CAACF,gBAAgB;cACfM,UAAU,EAAEA,UAAW;cACvBE,SAAS,EAAGyD,OAAO,IAAK;gBACtBzD,SAAS,CAACyD,OAAO,CAAC;gBAClBM,iBAAiB,CAAC,CAAC;cACrB,CAAE;cACF9D,OAAO,EAAEA,OAAQ;cACjBuH,QAAQ,EAAE;YAAK;cAAAN,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAChB;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACC;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACO,CAAC,eAChB3H,OAAA,CAAClC,aAAa;UAAAyJ,QAAA,eACZvH,OAAA,CAAC9C,MAAM;YAAC6K,OAAO,EAAE1D,iBAAkB;YAAAkD,QAAA,EAAC;UAAO;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACvC,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACV,CAAC;IAEb,CAAC,MAAM,IAAIrG,UAAU,KAAK,gBAAgB,EAAE;MAC1C,oBACEtB,OAAA,CAACtC,MAAM;QAACyJ,IAAI,EAAE/F,UAAW;QAACgG,OAAO,EAAE/C,iBAAkB;QAACgD,QAAQ,EAAC,IAAI;QAACC,SAAS;QAAAC,QAAA,gBAC3EvH,OAAA,CAACrC,WAAW;UAAA4J,QAAA,EAAC;QAAsB;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAa,CAAC,eACjD3H,OAAA,CAACpC,aAAa;UAAA2J,QAAA,EACXhF,WAAW,gBACVvC,OAAA,CAAC1B,gBAAgB;YAAAkJ,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,GAClBtF,IAAI,CAACoE,MAAM,KAAK,CAAC,gBACnBzG,OAAA,CAAC3B,KAAK;YAAC2J,QAAQ,EAAC,MAAM;YAAAT,QAAA,EAAC;UAAuB;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAO,CAAC,GACpD,CAACnG,YAAY,gBACfxB,OAAA,CAAChD,GAAG;YAAAuK,QAAA,gBACFvH,OAAA,CAAC/C,UAAU;cAACgL,OAAO,EAAC,WAAW;cAACC,YAAY;cAAAX,QAAA,EAAC;YAE7C;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAY,CAAC,eACb3H,OAAA,CAAC3C,IAAI;cAAAkK,QAAA,EACFlF,IAAI,CAAC8F,GAAG,CAAEhF,IAAI,iBACbnD,OAAA,CAAC1C,QAAQ;gBACP8K,MAAM;gBAENL,OAAO,EAAEA,CAAA,KAAMzD,gBAAgB,CAACnB,IAAI,CAAE;gBAAAoE,QAAA,eAEtCvH,OAAA,CAACzC,YAAY;kBACX8K,OAAO,EAAElF,IAAI,CAACvB,OAAQ;kBACtB0G,SAAS,EAAE,GAAGnF,IAAI,CAACoF,SAAS,IAAI,KAAK,UAAUpF,IAAI,CAACqF,mBAAmB,IAAI,KAAK,OAAOrF,IAAI,CAACsF,iBAAiB,IAAI,KAAK;gBAAG;kBAAAjB,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAC1H;cAAC,GANGxE,IAAI,CAACvB,OAAO;gBAAA4F,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAOT,CACX;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACE,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACJ,CAAC,gBAEN3H,OAAA,CAAChD,GAAG;YAAC4K,EAAE,EAAE;cAAEC,EAAE,EAAE;YAAE,CAAE;YAAAN,QAAA,gBACjBvH,OAAA,CAAC/C,UAAU;cAACgL,OAAO,EAAC,WAAW;cAACC,YAAY;cAAAX,QAAA,GAAC,oBACzB,EAAC/F,YAAY,CAACI,OAAO;YAAA;cAAA4F,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC7B,CAAC,eACb3H,OAAA,CAAC/C,UAAU;cAACgL,OAAO,EAAC,OAAO;cAACC,YAAY;cAAAX,QAAA,GAAC,iBACxB,EAAC/F,YAAY,CAAC+C,aAAa,IAAI,KAAK;YAAA;cAAAiD,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACzC,CAAC,eACb3H,OAAA,CAAC/C,UAAU;cAACgL,OAAO,EAAC,OAAO;cAACC,YAAY;cAAAX,QAAA,GAAC,qBACpB,EAAC/F,YAAY,CAAC6B,eAAe,IAAI,GAAG;YAAA;cAAAmE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC7C,CAAC,eACb3H,OAAA,CAACjC,SAAS;cACR2K,MAAM,EAAC,OAAO;cACdzD,IAAI,EAAC,cAAc;cACnB0D,KAAK,EAAC,4BAA4B;cAClCC,IAAI,EAAC,QAAQ;cACbtB,SAAS;cACTW,OAAO,EAAC,UAAU;cAClBlD,KAAK,EAAErD,QAAQ,CAACG,YAAa;cAC7BgH,QAAQ,EAAE7D,gBAAiB;cAC3B8D,QAAQ;cACRvF,KAAK,EAAE,CAAC,CAACtB,UAAU,CAACJ,YAAa;cACjCkH,UAAU,EAAE9G,UAAU,CAACJ,YAAa;cACpC+F,EAAE,EAAE;gBAAEC,EAAE,EAAE;cAAE;YAAE;cAAAL,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACf,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACC;QACN;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACY,CAAC,eAChB3H,OAAA,CAAClC,aAAa;UAAAyJ,QAAA,gBACZvH,OAAA,CAAC9C,MAAM;YAAC6K,OAAO,EAAE1D,iBAAkB;YAAAkD,QAAA,EAAC;UAAO;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC,EACnDnG,YAAY,iBACXxB,OAAA,CAAC9C,MAAM;YACL6K,OAAO,EAAEvC,UAAW;YACpBwD,QAAQ,EAAEhI,OAAO,IAAI,CAACU,QAAQ,CAACG,YAAa;YAC5CoH,SAAS,EAAEjI,OAAO,gBAAGhB,OAAA,CAAC1B,gBAAgB;cAAC4K,IAAI,EAAE;YAAG;cAAA1B,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,gBAAG3H,OAAA,CAACX,QAAQ;cAAAmI,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAE;YAAAJ,QAAA,EACpE;UAED;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CACT;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACY,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACV,CAAC;IAEb,CAAC,MAAM,IAAIrG,UAAU,KAAK,gBAAgB,EAAE;MAC1C,oBACEtB,OAAA,CAACtC,MAAM;QAACyJ,IAAI,EAAE/F,UAAW;QAACgG,OAAO,EAAE/C,iBAAkB;QAACgD,QAAQ,EAAC,IAAI;QAACC,SAAS;QAAAC,QAAA,gBAC3EvH,OAAA,CAACrC,WAAW;UAAA4J,QAAA,EAAC;QAA2B;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAa,CAAC,eACtD3H,OAAA,CAACpC,aAAa;UAAA2J,QAAA,EACXhF,WAAW,gBACVvC,OAAA,CAAC1B,gBAAgB;YAAAkJ,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,GAClBtF,IAAI,CAACoE,MAAM,KAAK,CAAC,gBACnBzG,OAAA,CAAC3B,KAAK;YAAC2J,QAAQ,EAAC,MAAM;YAAAT,QAAA,EAAC;UAAuB;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAO,CAAC,GACpD,CAACnG,YAAY,gBACfxB,OAAA,CAAChD,GAAG;YAAAuK,QAAA,gBACFvH,OAAA,CAAC/C,UAAU;cAACgL,OAAO,EAAC,WAAW;cAACC,YAAY;cAAAX,QAAA,EAAC;YAE7C;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAY,CAAC,eACb3H,OAAA,CAAC3C,IAAI;cAAAkK,QAAA,EACFlF,IAAI,CAAC8F,GAAG,CAAEhF,IAAI,iBACbnD,OAAA,CAAC1C,QAAQ;gBACP8K,MAAM;gBAENL,OAAO,EAAEA,CAAA,KAAMzD,gBAAgB,CAACnB,IAAI,CAAE;gBAAAoE,QAAA,eAEtCvH,OAAA,CAACzC,YAAY;kBACX8K,OAAO,EAAElF,IAAI,CAACvB,OAAQ;kBACtB0G,SAAS,EAAE,mBAAmBnF,IAAI,CAACrB,SAAS,IAAI,eAAe;gBAAG;kBAAA0F,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACnE;cAAC,GANGxE,IAAI,CAACvB,OAAO;gBAAA4F,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAOT,CACX;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACE,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACJ,CAAC,gBAEN3H,OAAA,CAAChD,GAAG;YAAC4K,EAAE,EAAE;cAAEC,EAAE,EAAE;YAAE,CAAE;YAAAN,QAAA,gBACjBvH,OAAA,CAAC/C,UAAU;cAACgL,OAAO,EAAC,WAAW;cAACC,YAAY;cAAAX,QAAA,GAAC,oBACzB,EAAC/F,YAAY,CAACI,OAAO;YAAA;cAAA4F,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC7B,CAAC,eACb3H,OAAA,CAAC/C,UAAU;cAACgL,OAAO,EAAC,OAAO;cAACC,YAAY;cAAAX,QAAA,GAAC,kBACvB,EAAC/F,YAAY,CAACM,SAAS,IAAI,eAAe;YAAA;cAAA0F,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAChD,CAAC,eACb3H,OAAA,CAACjC,SAAS;cACR2K,MAAM,EAAC,OAAO;cACdzD,IAAI,EAAC,WAAW;cAChB0D,KAAK,EAAC,WAAW;cACjBrB,SAAS;cACTW,OAAO,EAAC,UAAU;cAClBlD,KAAK,EAAErD,QAAQ,CAACI,SAAU;cAC1B+G,QAAQ,EAAE7D,gBAAiB;cAC3B4C,EAAE,EAAE;gBAAEC,EAAE,EAAE;cAAE;YAAE;cAAAL,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACf,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACC;QACN;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACY,CAAC,eAChB3H,OAAA,CAAClC,aAAa;UAAAyJ,QAAA,gBACZvH,OAAA,CAAC9C,MAAM;YAAC6K,OAAO,EAAE1D,iBAAkB;YAAAkD,QAAA,EAAC;UAAO;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC,EACnDnG,YAAY,iBACXxB,OAAA,CAAC9C,MAAM;YACL6K,OAAO,EAAEvC,UAAW;YACpBwD,QAAQ,EAAEhI,OAAQ;YAClBiI,SAAS,EAAEjI,OAAO,gBAAGhB,OAAA,CAAC1B,gBAAgB;cAAC4K,IAAI,EAAE;YAAG;cAAA1B,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,gBAAG3H,OAAA,CAACX,QAAQ;cAAAmI,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAE;YAAAJ,QAAA,EACpE;UAED;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CACT;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACY,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACV,CAAC;IAEb,CAAC,MAAM,IAAIrG,UAAU,KAAK,eAAe,EAAE;MACzC,oBACEtB,OAAA,CAACtC,MAAM;QAACyJ,IAAI,EAAE/F,UAAW;QAACgG,OAAO,EAAE/C,iBAAkB;QAACgD,QAAQ,EAAC,IAAI;QAACC,SAAS;QAAAC,QAAA,gBAC3EvH,OAAA,CAACrC,WAAW;UAAA4J,QAAA,EAAC;QAAa;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAa,CAAC,eACxC3H,OAAA,CAACpC,aAAa;UAAA2J,QAAA,gBACZvH,OAAA,CAAC3B,KAAK;YAAC2J,QAAQ,EAAC,MAAM;YAACJ,EAAE,EAAE;cAAEuB,EAAE,EAAE;YAAE,CAAE;YAAA5B,QAAA,EAAC;UAGtC;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAO,CAAC,EAEPpF,WAAW,gBACVvC,OAAA,CAAC1B,gBAAgB;YAAAkJ,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,GAClB,CAACnG,YAAY,gBACfxB,OAAA,CAAChD,GAAG;YAAC4K,EAAE,EAAE;cAAEwB,CAAC,EAAE;YAAE,CAAE;YAAA7B,QAAA,gBAChBvH,OAAA,CAAC/C,UAAU;cAACgL,OAAO,EAAC,WAAW;cAACC,YAAY;cAAAX,QAAA,EAAC;YAE7C;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAY,CAAC,eACb3H,OAAA,CAAChD,GAAG;cAAC4K,EAAE,EAAE;gBAAEyB,OAAO,EAAE,MAAM;gBAAEC,UAAU,EAAE,QAAQ;gBAAEzB,EAAE,EAAE;cAAE,CAAE;cAAAN,QAAA,gBACxDvH,OAAA,CAACjC,SAAS;gBACRuJ,SAAS;gBACTqB,KAAK,EAAC,SAAS;gBACfV,OAAO,EAAC,UAAU;gBAClBlD,KAAK,EAAEhD,WAAY;gBACnB8G,QAAQ,EAAEjE,uBAAwB;gBAClC2E,WAAW,EAAC;cAAyB;gBAAA/B,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACtC,CAAC,eACF3H,OAAA,CAAC9C,MAAM;gBACL+K,OAAO,EAAC,WAAW;gBACnBuB,KAAK,EAAC,SAAS;gBACfzB,OAAO,EAAEvD,oBAAqB;gBAC9BwE,QAAQ,EAAEzG,WAAW,IAAI,CAACR,WAAW,CAAC0C,IAAI,CAAC,CAAE;gBAC7CmD,EAAE,EAAE;kBAAE6B,EAAE,EAAE,CAAC;kBAAEC,QAAQ,EAAE;gBAAQ,CAAE;gBAAAnC,QAAA,EAEhChF,WAAW,gBAAGvC,OAAA,CAAC1B,gBAAgB;kBAAC4K,IAAI,EAAE;gBAAG;kBAAA1B,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAC,GAAG;cAAO;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACjD,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACN,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC,GACJ,IAAI;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACK,CAAC,eAChB3H,OAAA,CAAClC,aAAa;UAAAyJ,QAAA,eACZvH,OAAA,CAAC9C,MAAM;YAAC6K,OAAO,EAAE1D,iBAAkB;YAAAkD,QAAA,EAAC;UAAO;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACvC,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACV,CAAC;IAEb,CAAC,MAAM,IAAIrG,UAAU,KAAK,aAAa,EAAE;MACvC;MACA,MAAMwF,WAAW,GAAGtF,YAAY,KAAKA,YAAY,CAAC8B,mBAAmB,KAAK,YAAY,IAAK9B,YAAY,CAAC6B,eAAe,IAAI7B,YAAY,CAAC6B,eAAe,GAAG,CAAE,CAAC;MAE7J,oBACErD,OAAA,CAACtC,MAAM;QAACyJ,IAAI,EAAE/F,UAAW;QAACgG,OAAO,EAAE/C,iBAAkB;QAACgD,QAAQ,EAAC,IAAI;QAACC,SAAS;QAAAC,QAAA,gBAC3EvH,OAAA,CAACrC,WAAW;UAAA4J,QAAA,EACT,CAAC/F,YAAY,GAAG,cAAc,GAC9BsF,WAAW,GAAG,uBAAuB,GAAG;QAAc;UAAAU,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC5C,CAAC,eACd3H,OAAA,CAACpC,aAAa;UAAA2J,QAAA,EACXhF,WAAW,gBACVvC,OAAA,CAAC1B,gBAAgB;YAAAkJ,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,GAClB,CAACnG,YAAY,gBACfxB,OAAA,CAAChD,GAAG;YAAC4K,EAAE,EAAE;cAAEwB,CAAC,EAAE;YAAE,CAAE;YAAA7B,QAAA,gBAChBvH,OAAA,CAAC/C,UAAU;cAACgL,OAAO,EAAC,WAAW;cAACC,YAAY;cAAAX,QAAA,EAAC;YAE7C;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAY,CAAC,eACb3H,OAAA,CAAChD,GAAG;cAAC4K,EAAE,EAAE;gBAAEyB,OAAO,EAAE,MAAM;gBAAEC,UAAU,EAAE,QAAQ;gBAAEzB,EAAE,EAAE;cAAE,CAAE;cAAAN,QAAA,gBACxDvH,OAAA,CAACjC,SAAS;gBACRuJ,SAAS;gBACTqB,KAAK,EAAC,SAAS;gBACfV,OAAO,EAAC,UAAU;gBAClBlD,KAAK,EAAEhD,WAAY;gBACnB8G,QAAQ,EAAEjE,uBAAwB;gBAClC2E,WAAW,EAAC;cAAyB;gBAAA/B,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACtC,CAAC,eACF3H,OAAA,CAAC9C,MAAM;gBACL+K,OAAO,EAAC,WAAW;gBACnBuB,KAAK,EAAC,SAAS;gBACfzB,OAAO,EAAEvD,oBAAqB;gBAC9BwE,QAAQ,EAAEzG,WAAW,IAAI,CAACR,WAAW,CAAC0C,IAAI,CAAC,CAAE;gBAC7CmD,EAAE,EAAE;kBAAE6B,EAAE,EAAE,CAAC;kBAAEC,QAAQ,EAAE;gBAAQ,CAAE;gBAAAnC,QAAA,EAEhChF,WAAW,gBAAGvC,OAAA,CAAC1B,gBAAgB;kBAAC4K,IAAI,EAAE;gBAAG;kBAAA1B,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAC,GAAG;cAAO;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACjD,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACN,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC,GACJrG,UAAU,KAAK,aAAa,IAAIwF,WAAW,gBAC7C9G,OAAA,CAAAE,SAAA;YAAAqH,QAAA,gBACEvH,OAAA,CAACnC,iBAAiB;cAAA0J,QAAA,GAAC,UACT,eAAAvH,OAAA;gBAAAuH,QAAA,EAAS/F,YAAY,CAACI;cAAO;gBAAA4F,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAS,CAAC,8CAC/C,EAACnG,YAAY,CAAC6B,eAAe,GAAG,CAAC,iBAC/BrD,OAAA,CAAAE,SAAA;gBAAAqH,QAAA,GAAE,iBAAe,eAAAvH,OAAA;kBAAAuH,QAAA,GAAS/F,YAAY,CAAC6B,eAAe,EAAC,IAAE;gBAAA;kBAAAmE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,KAAC;cAAA,eAAE,CACtE;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACgB,CAAC,eACpB3H,OAAA,CAACnC,iBAAiB;cAAC+J,EAAE,EAAE;gBAAEC,EAAE,EAAE;cAAE,CAAE;cAAAN,QAAA,EAAC;YAElC;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAmB,CAAC;UAAA,eACpB,CAAC,GACDrG,UAAU,KAAK,aAAa,gBAC9BtB,OAAA,CAAAE,SAAA;YAAAqH,QAAA,gBACEvH,OAAA,CAACnC,iBAAiB;cAAA0J,QAAA,GAAC,6BACU,eAAAvH,OAAA;gBAAAuH,QAAA,EAAS/F,YAAY,CAACI;cAAO;gBAAA4F,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAS,CAAC,KACpE;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAmB,CAAC,eAEpB3H,OAAA,CAAChC,WAAW;cAAC2L,SAAS,EAAC,UAAU;cAAC/B,EAAE,EAAE;gBAAEC,EAAE,EAAE;cAAE,CAAE;cAAAN,QAAA,gBAC9CvH,OAAA,CAACrB,SAAS;gBAACgL,SAAS,EAAC,QAAQ;gBAAApC,QAAA,EAAC;cAAgC;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAW,CAAC,eAC1E3H,OAAA,CAACvB,UAAU;gBACTsG,KAAK,EAAEtC,UAAW;gBAClBoG,QAAQ,EAAGhE,CAAC,IAAKnC,aAAa,CAACmC,CAAC,CAACC,MAAM,CAACC,KAAK,CAAE;gBAAAwC,QAAA,gBAE/CvH,OAAA,CAACtB,gBAAgB;kBACfqG,KAAK,EAAC,OAAO;kBACb6E,OAAO,eAAE5J,OAAA,CAACxB,KAAK;oBAAAgJ,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE,CAAE;kBACnBgB,KAAK,EAAC;gBAAqF;kBAAAnB,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAC5F,CAAC,eACF3H,OAAA,CAACtB,gBAAgB;kBACfqG,KAAK,EAAC,QAAQ;kBACd6E,OAAO,eAAE5J,OAAA,CAACxB,KAAK;oBAAAgJ,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE,CAAE;kBACnBgB,KAAK,EAAC;gBAAsE;kBAAAnB,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAC7E,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACQ,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACF,CAAC;UAAA,eACd,CAAC,GACD;QAAI;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACK,CAAC,eAChB3H,OAAA,CAAClC,aAAa;UAAAyJ,QAAA,gBACZvH,OAAA,CAAC9C,MAAM;YAAC6K,OAAO,EAAE1D,iBAAkB;YAAAkD,QAAA,EAAC;UAAO;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC,EACnDrG,UAAU,KAAK,aAAa,IAAIE,YAAY,iBAC3CxB,OAAA,CAAC9C,MAAM;YACL6K,OAAO,EAAEvC,UAAW;YACpBwD,QAAQ,EAAEhI,OAAQ;YAClBwI,KAAK,EAAE1C,WAAW,GAAG,SAAS,GAAG,OAAQ;YACzCmC,SAAS,EAAEjI,OAAO,gBAAGhB,OAAA,CAAC1B,gBAAgB;cAAC4K,IAAI,EAAE;YAAG;cAAA1B,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,GAAGb,WAAW,gBAAG9G,OAAA,CAACT,WAAW;cAAAiI,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,gBAAG3H,OAAA,CAACf,UAAU;cAAAuI,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAE;YAAAJ,QAAA,EAEpGT,WAAW,GAAG,kBAAkB,GAAIrE,UAAU,KAAK,OAAO,GAAG,kBAAkB,GAAG;UAA0B;YAAA+E,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACvG,CACT;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACY,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACV,CAAC;IAEb,CAAC,MAAM,IAAIrG,UAAU,KAAK,cAAc,EAAE;MACxC,oBACEtB,OAAA,CAACtC,MAAM;QAACyJ,IAAI,EAAE/F,UAAW;QAACgG,OAAO,EAAE/C,iBAAkB;QAACgD,QAAQ,EAAC,IAAI;QAACC,SAAS;QAAAC,QAAA,gBAC3EvH,OAAA,CAACrC,WAAW;UAAA4J,QAAA,EAAC;QAAa;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAa,CAAC,eACxC3H,OAAA,CAACpC,aAAa;UAAA2J,QAAA,GACXhB,MAAM,CAACC,IAAI,CAACvE,UAAU,CAAC,CAACwE,MAAM,GAAG,CAAC,iBACjCzG,OAAA,CAAC3B,KAAK;YAAC2J,QAAQ,EAAC,OAAO;YAACJ,EAAE,EAAE;cAAEuB,EAAE,EAAE;YAAE,CAAE;YAAA5B,QAAA,eACpCvH,OAAA,CAAC/C,UAAU;cAACgL,OAAO,EAAC,WAAW;cAAAV,QAAA,EAAC;YAEhC;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAY;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACR,CACR,EACApB,MAAM,CAACC,IAAI,CAACrE,YAAY,CAAC,CAACsE,MAAM,GAAG,CAAC,iBACnCzG,OAAA,CAAC3B,KAAK;YAAC2J,QAAQ,EAAC,SAAS;YAAC6B,IAAI,eAAE7J,OAAA,CAACT,WAAW;cAAAiI,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAE;YAACC,EAAE,EAAE;cAAEuB,EAAE,EAAE;YAAE,CAAE;YAAA5B,QAAA,eAC7DvH,OAAA,CAAC/C,UAAU;cAACgL,OAAO,EAAC,WAAW;cAAAV,QAAA,EAAC;YAEhC;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAY;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACR,CACR,eAED3H,OAAA,CAAC7C,KAAK;YAACyK,EAAE,EAAE;cAAEwB,CAAC,EAAE,CAAC;cAAED,EAAE,EAAE,CAAC;cAAEW,SAAS,EAAE;YAAE,CAAE;YAAAvC,QAAA,gBACvCvH,OAAA,CAAC/C,UAAU;cAACgL,OAAO,EAAC,IAAI;cAACC,YAAY;cAACN,EAAE,EAAE;gBAAEmC,QAAQ,EAAE;cAAS,CAAE;cAAAxC,QAAA,EAAC;YAElE;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAY,CAAC,eACb3H,OAAA,CAAC5B,IAAI;cAAC4L,SAAS;cAACC,OAAO,EAAE,CAAE;cAAA1C,QAAA,gBACzBvH,OAAA,CAAC5B,IAAI;gBAAC8L,IAAI;gBAACC,EAAE,EAAE,EAAG;gBAACC,EAAE,EAAE,CAAE;gBAAA7C,QAAA,eACvBvH,OAAA,CAACjC,SAAS;kBACRkH,IAAI,EAAC,SAAS;kBACd0D,KAAK,EAAC,SAAS;kBACfrB,SAAS;kBACTW,OAAO,EAAC,UAAU;kBAClBlD,KAAK,EAAErD,QAAQ,CAACE,OAAQ;kBACxBoH,QAAQ;gBAAA;kBAAAxB,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACT;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACE,CAAC,eACP3H,OAAA,CAAC5B,IAAI;gBAAC8L,IAAI;gBAACC,EAAE,EAAE,EAAG;gBAACC,EAAE,EAAE,CAAE;gBAAA7C,QAAA,eACvBvH,OAAA,CAACjC,SAAS;kBACRkH,IAAI,EAAC,qBAAqB;kBAC1B0D,KAAK,EAAC,qBAAqB;kBAC3BrB,SAAS;kBACTW,OAAO,EAAC,UAAU;kBAClBlD,KAAK,EAAErD,QAAQ,CAAC2I,mBAAoB;kBACpCxB,QAAQ,EAAE7D,gBAAiB;kBAC3B8D,QAAQ;kBACRvF,KAAK,EAAE,CAAC,CAACtB,UAAU,CAACoI,mBAAoB;kBACxCtB,UAAU,EAAE9G,UAAU,CAACoI;gBAAoB;kBAAA7C,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAC5C;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACE,CAAC,eACP3H,OAAA,CAAC5B,IAAI;gBAAC8L,IAAI;gBAACC,EAAE,EAAE,EAAG;gBAACC,EAAE,EAAE,CAAE;gBAAA7C,QAAA,eACvBvH,OAAA,CAACjC,SAAS;kBACRkH,IAAI,EAAC,SAAS;kBACd0D,KAAK,EAAC,SAAS;kBACfrB,SAAS;kBACTW,OAAO,EAAC,UAAU;kBAClBlD,KAAK,EAAErD,QAAQ,CAAC4I,OAAQ;kBACxBzB,QAAQ,EAAE7D,gBAAiB;kBAC3BzB,KAAK,EAAE,CAAC,CAACtB,UAAU,CAACqI,OAAQ;kBAC5BvB,UAAU,EAAE9G,UAAU,CAACqI;gBAAQ;kBAAA9C,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAChC;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACE,CAAC,eACP3H,OAAA,CAAC5B,IAAI;gBAAC8L,IAAI;gBAACC,EAAE,EAAE,EAAG;gBAACC,EAAE,EAAE,CAAE;gBAAA7C,QAAA,eACvBvH,OAAA,CAACjC,SAAS;kBACRkH,IAAI,EAAC,SAAS;kBACd0D,KAAK,EAAC,SAAS;kBACfrB,SAAS;kBACTW,OAAO,EAAC,UAAU;kBAClBlD,KAAK,EAAErD,QAAQ,CAAC6I,OAAQ;kBACxB1B,QAAQ,EAAE7D,gBAAiB;kBAC3BzB,KAAK,EAAE,CAAC,CAACtB,UAAU,CAACsI,OAAQ;kBAC5BxB,UAAU,EAAE9G,UAAU,CAACsI;gBAAQ;kBAAA/C,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAChC;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACE,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACF,CAAC,eAGR3H,OAAA,CAAC7C,KAAK;YAACyK,EAAE,EAAE;cAAEwB,CAAC,EAAE,CAAC;cAAED,EAAE,EAAE,CAAC;cAAEW,SAAS,EAAE;YAAE,CAAE;YAAAvC,QAAA,gBACvCvH,OAAA,CAAC/C,UAAU;cAACgL,OAAO,EAAC,IAAI;cAACC,YAAY;cAACN,EAAE,EAAE;gBAAEmC,QAAQ,EAAE;cAAS,CAAE;cAAAxC,QAAA,EAAC;YAElE;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAY,CAAC,eACb3H,OAAA,CAAC5B,IAAI;cAAC4L,SAAS;cAACC,OAAO,EAAE,CAAE;cAAA1C,QAAA,gBACzBvH,OAAA,CAAC5B,IAAI;gBAAC8L,IAAI;gBAACC,EAAE,EAAE,EAAG;gBAACC,EAAE,EAAE,CAAE;gBAAA7C,QAAA,eACvBvH,OAAA,CAACjC,SAAS;kBACRkH,IAAI,EAAC,aAAa;kBAClB0D,KAAK,EAAC,aAAa;kBACnBrB,SAAS;kBACTW,OAAO,EAAC,UAAU;kBAClBlD,KAAK,EAAErD,QAAQ,CAAC8I,WAAY;kBAC5B3B,QAAQ,EAAE7D,gBAAiB;kBAC3BzB,KAAK,EAAE,CAAC,CAACtB,UAAU,CAACuI,WAAY;kBAChCzB,UAAU,EAAE9G,UAAU,CAACuI;gBAAY;kBAAAhD,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACpC;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACE,CAAC,eACP3H,OAAA,CAAC5B,IAAI;gBAAC8L,IAAI;gBAACC,EAAE,EAAE,EAAG;gBAACC,EAAE,EAAE,CAAE;gBAAA7C,QAAA,eACvBvH,OAAA,CAACjC,SAAS;kBACRkH,IAAI,EAAC,WAAW;kBAChB0D,KAAK,EAAC,WAAW;kBACjBrB,SAAS;kBACTW,OAAO,EAAC,UAAU;kBAClBlD,KAAK,EAAErD,QAAQ,CAAC6G,SAAU;kBAC1BM,QAAQ,EAAE7D,gBAAiB;kBAC3BzB,KAAK,EAAE,CAAC,CAACtB,UAAU,CAACsG,SAAU;kBAC9BQ,UAAU,EAAE9G,UAAU,CAACsG;gBAAU;kBAAAf,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAClC;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACE,CAAC,eACP3H,OAAA,CAAC5B,IAAI;gBAAC8L,IAAI;gBAACC,EAAE,EAAE,EAAG;gBAACC,EAAE,EAAE,CAAE;gBAAA7C,QAAA,eACvBvH,OAAA,CAACjC,SAAS;kBACRkH,IAAI,EAAC,cAAc;kBACnB0D,KAAK,EAAC,mBAAmB;kBACzBrB,SAAS;kBACTW,OAAO,EAAC,UAAU;kBAClBlD,KAAK,EAAErD,QAAQ,CAAC+I,YAAa;kBAC7B5B,QAAQ,EAAE7D,gBAAiB;kBAC3BzB,KAAK,EAAE,CAAC,CAACtB,UAAU,CAACwI,YAAa;kBACjC1B,UAAU,EAAE9G,UAAU,CAACwI,YAAY,IAAItI,YAAY,CAACsI,YAAa;kBACjEC,mBAAmB,EAAE;oBACnBC,KAAK,EAAE;sBAAEnB,KAAK,EAAErH,YAAY,CAACsI,YAAY,GAAG,QAAQ,GAAGG;oBAAU;kBACnE;gBAAE;kBAAApD,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACE,CAAC,eACP3H,OAAA,CAAC5B,IAAI;gBAAC8L,IAAI;gBAACC,EAAE,EAAE,EAAG;gBAACC,EAAE,EAAE,CAAE;gBAAA7C,QAAA,eACvBvH,OAAA,CAACjC,SAAS;kBACRkH,IAAI,EAAC,SAAS;kBACd0D,KAAK,EAAC,SAAS;kBACfrB,SAAS;kBACTW,OAAO,EAAC,UAAU;kBAClBlD,KAAK,EAAErD,QAAQ,CAACmJ,OAAQ;kBACxBhC,QAAQ,EAAE7D,gBAAiB;kBAC3BzB,KAAK,EAAE,CAAC,CAACtB,UAAU,CAAC4I,OAAQ;kBAC5B9B,UAAU,EAAE9G,UAAU,CAAC4I,OAAO,IAAI1I,YAAY,CAAC0I,OAAQ;kBACvDH,mBAAmB,EAAE;oBACnBC,KAAK,EAAE;sBAAEnB,KAAK,EAAErH,YAAY,CAAC0I,OAAO,GAAG,QAAQ,GAAGD;oBAAU;kBAC9D;gBAAE;kBAAApD,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACE,CAAC,eACP3H,OAAA,CAAC5B,IAAI;gBAAC8L,IAAI;gBAACC,EAAE,EAAE,EAAG;gBAACC,EAAE,EAAE,CAAE;gBAAA7C,QAAA,eACvBvH,OAAA,CAAChC,WAAW;kBAACsJ,SAAS;kBAAAC,QAAA,gBACpBvH,OAAA,CAAC/B,UAAU;oBAAC6M,EAAE,EAAC,UAAU;oBAAAvD,QAAA,EAAC;kBAAe;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAY,CAAC,eACtD3H,OAAA,CAAC9B,MAAM;oBACL6M,OAAO,EAAC,UAAU;oBAClB9F,IAAI,EAAC,IAAI;oBACTF,KAAK,EAAErD,QAAQ,CAACsJ,EAAE,IAAI,EAAG;oBACzBrC,KAAK,EAAC,iBAAiB;oBACvBE,QAAQ,EAAE7D,gBAAiB;oBAAAuC,QAAA,gBAE3BvH,OAAA,CAAC7B,QAAQ;sBAAC4G,KAAK,EAAC,GAAG;sBAAAwC,QAAA,EAAC;oBAAC;sBAAAC,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAU,CAAC,eAChC3H,OAAA,CAAC7B,QAAQ;sBAAC4G,KAAK,EAAC,GAAG;sBAAAwC,QAAA,EAAC;oBAAC;sBAAAC,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAU,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAC1B,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACE;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACV,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACF,CAAC,eAGR3H,OAAA,CAAC7C,KAAK;YAACyK,EAAE,EAAE;cAAEwB,CAAC,EAAE,CAAC;cAAED,EAAE,EAAE,CAAC;cAAEW,SAAS,EAAE;YAAE,CAAE;YAAAvC,QAAA,gBACvCvH,OAAA,CAAC/C,UAAU;cAACgL,OAAO,EAAC,IAAI;cAACC,YAAY;cAACN,EAAE,EAAE;gBAAEmC,QAAQ,EAAE;cAAS,CAAE;cAAAxC,QAAA,EAAC;YAElE;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAY,CAAC,eACb3H,OAAA,CAAC5B,IAAI;cAAC4L,SAAS;cAACC,OAAO,EAAE,CAAE;cAAA1C,QAAA,gBACzBvH,OAAA,CAAC5B,IAAI;gBAAC8L,IAAI;gBAACC,EAAE,EAAE,EAAG;gBAACC,EAAE,EAAE,CAAE;gBAAA7C,QAAA,eACvBvH,OAAA,CAACjC,SAAS;kBACRkH,IAAI,EAAC,qBAAqB;kBAC1B0D,KAAK,EAAC,qBAAqB;kBAC3BrB,SAAS;kBACTW,OAAO,EAAC,UAAU;kBAClBlD,KAAK,EAAErD,QAAQ,CAAC8G,mBAAoB;kBACpCK,QAAQ,EAAE7D,gBAAiB;kBAC3BzB,KAAK,EAAE,CAAC,CAACtB,UAAU,CAACuG,mBAAoB;kBACxCO,UAAU,EAAE9G,UAAU,CAACuG;gBAAoB;kBAAAhB,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAC5C;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACE,CAAC,eACP3H,OAAA,CAAC5B,IAAI;gBAAC8L,IAAI;gBAACC,EAAE,EAAE,EAAG;gBAACC,EAAE,EAAE,CAAE;gBAAA7C,QAAA,eACvBvH,OAAA,CAACjC,SAAS;kBACRkH,IAAI,EAAC,iBAAiB;kBACtB0D,KAAK,EAAC,iBAAiB;kBACvBrB,SAAS;kBACTW,OAAO,EAAC,UAAU;kBAClBlD,KAAK,EAAErD,QAAQ,CAACuJ,eAAgB;kBAChCpC,QAAQ,EAAE7D,gBAAiB;kBAC3BzB,KAAK,EAAE,CAAC,CAACtB,UAAU,CAACgJ,eAAgB;kBACpClC,UAAU,EAAE9G,UAAU,CAACgJ;gBAAgB;kBAAAzD,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACxC;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACE,CAAC,eACP3H,OAAA,CAAC5B,IAAI;gBAAC8L,IAAI;gBAACC,EAAE,EAAE,EAAG;gBAACC,EAAE,EAAE,CAAE;gBAAA7C,QAAA,eACvBvH,OAAA,CAACjC,SAAS;kBACRkH,IAAI,EAAC,6BAA6B;kBAClC0D,KAAK,EAAC,6BAA6B;kBACnCrB,SAAS;kBACTW,OAAO,EAAC,UAAU;kBAClBlD,KAAK,EAAErD,QAAQ,CAACwJ,2BAA4B;kBAC5CrC,QAAQ,EAAE7D,gBAAiB;kBAC3BzB,KAAK,EAAE,CAAC,CAACtB,UAAU,CAACiJ,2BAA4B;kBAChDnC,UAAU,EAAE9G,UAAU,CAACiJ;gBAA4B;kBAAA1D,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACpD;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACE,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACF,CAAC,eAGR3H,OAAA,CAAC7C,KAAK;YAACyK,EAAE,EAAE;cAAEwB,CAAC,EAAE,CAAC;cAAED,EAAE,EAAE,CAAC;cAAEW,SAAS,EAAE;YAAE,CAAE;YAAAvC,QAAA,gBACvCvH,OAAA,CAAC/C,UAAU;cAACgL,OAAO,EAAC,IAAI;cAACC,YAAY;cAACN,EAAE,EAAE;gBAAEmC,QAAQ,EAAE;cAAS,CAAE;cAAAxC,QAAA,EAAC;YAElE;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAY,CAAC,eACb3H,OAAA,CAAC5B,IAAI;cAAC4L,SAAS;cAACC,OAAO,EAAE,CAAE;cAAA1C,QAAA,gBACzBvH,OAAA,CAAC5B,IAAI;gBAAC8L,IAAI;gBAACC,EAAE,EAAE,EAAG;gBAACC,EAAE,EAAE,CAAE;gBAAA7C,QAAA,eACvBvH,OAAA,CAACjC,SAAS;kBACRkH,IAAI,EAAC,mBAAmB;kBACxB0D,KAAK,EAAC,mBAAmB;kBACzBrB,SAAS;kBACTW,OAAO,EAAC,UAAU;kBAClBlD,KAAK,EAAErD,QAAQ,CAAC+G,iBAAkB;kBAClCI,QAAQ,EAAE7D,gBAAiB;kBAC3BzB,KAAK,EAAE,CAAC,CAACtB,UAAU,CAACwG,iBAAkB;kBACtCM,UAAU,EAAE9G,UAAU,CAACwG;gBAAkB;kBAAAjB,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAC1C;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACE,CAAC,eACP3H,OAAA,CAAC5B,IAAI;gBAAC8L,IAAI;gBAACC,EAAE,EAAE,EAAG;gBAACC,EAAE,EAAE,CAAE;gBAAA7C,QAAA,eACvBvH,OAAA,CAACjC,SAAS;kBACRkH,IAAI,EAAC,eAAe;kBACpB0D,KAAK,EAAC,eAAe;kBACrBrB,SAAS;kBACTW,OAAO,EAAC,UAAU;kBAClBlD,KAAK,EAAErD,QAAQ,CAACyJ,aAAc;kBAC9BtC,QAAQ,EAAE7D,gBAAiB;kBAC3BzB,KAAK,EAAE,CAAC,CAACtB,UAAU,CAACkJ,aAAc;kBAClCpC,UAAU,EAAE9G,UAAU,CAACkJ;gBAAc;kBAAA3D,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACtC;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACE,CAAC,eACP3H,OAAA,CAAC5B,IAAI;gBAAC8L,IAAI;gBAACC,EAAE,EAAE,EAAG;gBAACC,EAAE,EAAE,CAAE;gBAAA7C,QAAA,eACvBvH,OAAA,CAACjC,SAAS;kBACRkH,IAAI,EAAC,2BAA2B;kBAChC0D,KAAK,EAAC,2BAA2B;kBACjCrB,SAAS;kBACTW,OAAO,EAAC,UAAU;kBAClBlD,KAAK,EAAErD,QAAQ,CAAC0J,yBAA0B;kBAC1CvC,QAAQ,EAAE7D,gBAAiB;kBAC3BzB,KAAK,EAAE,CAAC,CAACtB,UAAU,CAACmJ,yBAA0B;kBAC9CrC,UAAU,EAAE9G,UAAU,CAACmJ;gBAA0B;kBAAA5D,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAClD;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACE,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACF,CAAC,eAGR3H,OAAA,CAAC7C,KAAK;YAACyK,EAAE,EAAE;cAAEwB,CAAC,EAAE,CAAC;cAAED,EAAE,EAAE,CAAC;cAAEW,SAAS,EAAE;YAAE,CAAE;YAAAvC,QAAA,gBACvCvH,OAAA,CAAC/C,UAAU;cAACgL,OAAO,EAAC,IAAI;cAACC,YAAY;cAACN,EAAE,EAAE;gBAAEmC,QAAQ,EAAE;cAAS,CAAE;cAAAxC,QAAA,EAAC;YAElE;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAY,CAAC,eACb3H,OAAA,CAAC5B,IAAI;cAAC4L,SAAS;cAACC,OAAO,EAAE,CAAE;cAAA1C,QAAA,eACzBvH,OAAA,CAAC5B,IAAI;gBAAC8L,IAAI;gBAACC,EAAE,EAAE,EAAG;gBAACC,EAAE,EAAE,CAAE;gBAAA7C,QAAA,eACvBvH,OAAA,CAACjC,SAAS;kBACRkH,IAAI,EAAC,eAAe;kBACpB0D,KAAK,EAAC,eAAe;kBACrBC,IAAI,EAAC,QAAQ;kBACbtB,SAAS;kBACTW,OAAO,EAAC,UAAU;kBAClBlD,KAAK,EAAErD,QAAQ,CAAC6C,aAAc;kBAC9BsE,QAAQ,EAAE7D,gBAAiB;kBAC3B8D,QAAQ;kBACRvF,KAAK,EAAE,CAAC,CAACtB,UAAU,CAACsC,aAAc;kBAClCwE,UAAU,EAAE9G,UAAU,CAACsC;gBAAc;kBAAAiD,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACtC;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACE;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAEH,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACF,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAEK,CAAC,eAChB3H,OAAA,CAAClC,aAAa;UAAAyJ,QAAA,gBACZvH,OAAA,CAAC9C,MAAM;YAAC6K,OAAO,EAAE1D,iBAAkB;YAAAkD,QAAA,EAAC;UAAO;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC,eACpD3H,OAAA,CAAC9C,MAAM;YACL6K,OAAO,EAAEvC,UAAW;YACpBwD,QAAQ,EAAEhI,OAAQ;YAClBiI,SAAS,EAAEjI,OAAO,gBAAGhB,OAAA,CAAC1B,gBAAgB;cAAC4K,IAAI,EAAE;YAAG;cAAA1B,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,gBAAG3H,OAAA,CAACX,QAAQ;cAAAmI,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAE;YAAAJ,QAAA,EACpE;UAED;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACI,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACV,CAAC;IAEb;IAEA,OAAO,IAAI;EACb,CAAC;EAED,oBACE3H,OAAA,CAAChD,GAAG;IAAC4K,EAAE,EAAE;MAAEyB,OAAO,EAAE;IAAO,CAAE;IAAA9B,QAAA,gBAE3BvH,OAAA,CAAChD,GAAG;MAAC4K,EAAE,EAAE;QAAEyD,KAAK,EAAE,OAAO;QAAEC,EAAE,EAAE;MAAE,CAAE;MAAA/D,QAAA,eACjCvH,OAAA,CAAC7C,KAAK;QAACyK,EAAE,EAAE;UAAEwB,CAAC,EAAE,CAAC;UAAED,EAAE,EAAE;QAAE,CAAE;QAAA5B,QAAA,gBACzBvH,OAAA,CAAC/C,UAAU;UAACgL,OAAO,EAAC,IAAI;UAACC,YAAY;UAAAX,QAAA,EAAC;QAEtC;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAY,CAAC,eACb3H,OAAA,CAAC5C,OAAO;UAACwK,EAAE,EAAE;YAAEuB,EAAE,EAAE;UAAE;QAAE;UAAA3B,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,eAC1B3H,OAAA,CAAC3C,IAAI;UAACsM,SAAS,EAAC,KAAK;UAAC4B,KAAK;UAAAhE,QAAA,gBACzBvH,OAAA,CAACvC,cAAc;YAACsK,OAAO,EAAEA,CAAA,KAAM/D,kBAAkB,CAAC,gBAAgB,CAAE;YAAAuD,QAAA,gBAClEvH,OAAA,CAACxC,YAAY;cAAA+J,QAAA,eACXvH,OAAA,CAACb,SAAS;gBAAAqI,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACD,CAAC,eACf3H,OAAA,CAACzC,YAAY;cAAC8K,OAAO,EAAC;YAA2B;cAAAb,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACtC,CAAC,eAEjB3H,OAAA,CAACvC,cAAc;YAACsK,OAAO,EAAEA,CAAA,KAAM/D,kBAAkB,CAAC,gBAAgB,CAAE;YAAAuD,QAAA,gBAClEvH,OAAA,CAACxC,YAAY;cAAA+J,QAAA,eACXvH,OAAA,CAACjB,QAAQ;gBAAAyI,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACA,CAAC,eACf3H,OAAA,CAACzC,YAAY;cAAC8K,OAAO,EAAC;YAAgC;cAAAb,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC3C,CAAC,eAEjB3H,OAAA,CAACvC,cAAc;YAACsK,OAAO,EAAEA,CAAA,KAAM/D,kBAAkB,CAAC,cAAc,CAAE;YAAAuD,QAAA,gBAChEvH,OAAA,CAACxC,YAAY;cAAA+J,QAAA,eACXvH,OAAA,CAACnB,OAAO;gBAAA2I,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACC,CAAC,eACf3H,OAAA,CAACzC,YAAY;cAAC8K,OAAO,EAAC;YAAwB;cAAAb,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACnC,CAAC,eAEjB3H,OAAA,CAACvC,cAAc;YAACsK,OAAO,EAAEA,CAAA,KAAM/D,kBAAkB,CAAC,cAAc,CAAE;YAAAuD,QAAA,gBAChEvH,OAAA,CAACxC,YAAY;cAAA+J,QAAA,eACXvH,OAAA,CAACjB,QAAQ;gBAAAyI,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACA,CAAC,eACf3H,OAAA,CAACzC,YAAY;cAAC8K,OAAO,EAAC;YAAkB;cAAAb,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC7B,CAAC,eAEjB3H,OAAA,CAACvC,cAAc;YAACsK,OAAO,EAAEA,CAAA,KAAM/D,kBAAkB,CAAC,aAAa,CAAE;YAAAuD,QAAA,gBAC/DvH,OAAA,CAACxC,YAAY;cAAA+J,QAAA,eACXvH,OAAA,CAACf,UAAU;gBAAAuI,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACF,CAAC,eACf3H,OAAA,CAACzC,YAAY;cAAC8K,OAAO,EAAC;YAAiB;cAAAb,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC5B,CAAC,eAEjB3H,OAAA,CAACvC,cAAc;YAACsK,OAAO,EAAEA,CAAA,KAAM/D,kBAAkB,CAAC,kBAAkB,CAAE;YAAAuD,QAAA,gBACpEvH,OAAA,CAACxC,YAAY;cAAA+J,QAAA,eACXvH,OAAA,CAACb,SAAS;gBAAAqI,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACD,CAAC,eACf3H,OAAA,CAACzC,YAAY;cAAC8K,OAAO,EAAC;YAAsB;cAAAb,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACjC,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACb,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACF;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACL,CAAC,eAGN3H,OAAA,CAAChD,GAAG;MAAC4K,EAAE,EAAE;QAAE4D,QAAQ,EAAE;MAAE,CAAE;MAAAjE,QAAA,eACvBvH,OAAA,CAAC7C,KAAK;QAACyK,EAAE,EAAE;UAAEwB,CAAC,EAAE,CAAC;UAAEqC,SAAS,EAAE,OAAO;UAAEpC,OAAO,EAAE,MAAM;UAAEC,UAAU,EAAE,QAAQ;UAAEoC,cAAc,EAAE;QAAS,CAAE;QAAAnE,QAAA,GACtG,CAACrG,cAAc,iBACdlB,OAAA,CAAC/C,UAAU;UAACgL,OAAO,EAAC,OAAO;UAAAV,QAAA,EAAC;QAE5B;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAY,CACb,EACAzG,cAAc,IAAI,CAACE,UAAU,iBAC5BpB,OAAA,CAAChD,GAAG;UAAC4K,EAAE,EAAE;YAAE+D,SAAS,EAAE;UAAS,CAAE;UAAApE,QAAA,gBAC/BvH,OAAA,CAAC/C,UAAU;YAACgL,OAAO,EAAC,IAAI;YAACC,YAAY;YAAAX,QAAA,GAClCrG,cAAc,KAAK,gBAAgB,IAAI,wBAAwB,EAC/DA,cAAc,KAAK,cAAc,IAAI,eAAe,EACpDA,cAAc,KAAK,cAAc,IAAI,qBAAqB,EAC1DA,cAAc,KAAK,aAAa,IAAI,cAAc,EAClDA,cAAc,KAAK,gBAAgB,IAAI,6BAA6B,EACpEA,cAAc,KAAK,kBAAkB,IAAI,mBAAmB;UAAA;YAAAsG,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACnD,CAAC,eACb3H,OAAA,CAAC/C,UAAU;YAACgL,OAAO,EAAC,OAAO;YAAAV,QAAA,EAAC;UAE5B;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAY,CAAC,eACb3H,OAAA,CAAC1B,gBAAgB;YAACsJ,EAAE,EAAE;cAAEC,EAAE,EAAE;YAAE;UAAE;YAAAL,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAChC,CACN;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACI;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACL,CAAC,EAELT,YAAY,CAAC,CAAC;EAAA;IAAAM,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACZ,CAAC;AAEV,CAAC;AAAClH,EAAA,CA/nCIN,oBAAoB;EAAA,QAKPX,WAAW;AAAA;AAAAoM,EAAA,GALxBzL,oBAAoB;AAioC1B,eAAeA,oBAAoB;AAAC,IAAAyL,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}