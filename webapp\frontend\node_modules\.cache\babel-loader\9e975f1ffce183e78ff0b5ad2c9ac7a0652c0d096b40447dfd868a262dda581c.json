{"ast": null, "code": "import axios from 'axios';\nconst API_URL = 'http://localhost:8000/api';\n\n// Crea un'istanza di axios con configurazione personalizzata\nconst axiosInstance = axios.create({\n  baseURL: API_URL,\n  headers: {\n    'Content-Type': 'application/json'\n  }\n});\n\n// Configura axios per includere il token in tutte le richieste\naxiosInstance.interceptors.request.use(config => {\n  const token = localStorage.getItem('token');\n  if (token) {\n    config.headers.Authorization = `Bearer ${token}`;\n  }\n  return config;\n}, error => {\n  return Promise.reject(error);\n});\n\n// Gestione degli errori di autenticazione\naxiosInstance.interceptors.response.use(response => response, error => {\n  if (error.response && error.response.status === 401) {\n    // Se la risposta è 401 Unauthorized, effettua il logout\n    localStorage.removeItem('token');\n    window.location.href = '/login';\n  }\n  return Promise.reject(error);\n});\nconst authService = {\n  // Login standard (admin o utente standard)\n  login: async (credentials, loginType) => {\n    try {\n      if (loginType === 'standard') {\n        // Converti le credenziali nel formato richiesto da OAuth2\n        const formData = new FormData();\n        formData.append('username', credentials.username);\n        formData.append('password', credentials.password);\n\n        // Usa axios direttamente per il login perché richiede FormData\n        const response = await axios.post(`${API_URL}/auth/login`, formData);\n        return response.data;\n      } else if (loginType === 'cantiere') {\n        // Login cantiere\n        const response = await axiosInstance.post('/auth/login/cantiere', {\n          codice_univoco: credentials.codice_univoco,\n          password: credentials.password\n        });\n        return response.data;\n      } else {\n        throw new Error('Tipo di login non valido');\n      }\n    } catch (error) {\n      console.error('Login error:', error);\n      throw error.response ? error.response.data : error;\n    }\n  },\n  // Login cantiere\n  loginCantiere: async credentials => {\n    try {\n      const response = await axiosInstance.post('/auth/login/cantiere', {\n        codice_univoco: credentials.codice_univoco,\n        password: credentials.password\n      });\n      return response.data;\n    } catch (error) {\n      console.error('Login cantiere error:', error);\n      throw error.response ? error.response.data : error;\n    }\n  },\n  // Verifica la validità del token\n  checkToken: async () => {\n    try {\n      const response = await axiosInstance.post('/auth/test-token');\n      return {\n        id: response.data.user_id,\n        username: response.data.username,\n        role: response.data.role\n      };\n    } catch (error) {\n      console.error('Check token error:', error);\n      throw error.response ? error.response.data : error;\n    }\n  },\n  // Impersona un altro utente (solo per admin)\n  impersonateUser: async userId => {\n    try {\n      const response = await axiosInstance.post('/auth/impersonate', {\n        user_id: userId\n      });\n      return response.data;\n    } catch (error) {\n      console.error('Impersonate user error:', error);\n      throw error.response ? error.response.data : error;\n    }\n  }\n};\nexport default authService;", "map": {"version": 3, "names": ["axios", "API_URL", "axiosInstance", "create", "baseURL", "headers", "interceptors", "request", "use", "config", "token", "localStorage", "getItem", "Authorization", "error", "Promise", "reject", "response", "status", "removeItem", "window", "location", "href", "authService", "login", "credentials", "loginType", "formData", "FormData", "append", "username", "password", "post", "data", "codice_univoco", "Error", "console", "loginCantiere", "checkToken", "id", "user_id", "role", "impersonate<PERSON><PERSON>", "userId"], "sources": ["C:/Users/<USER>/PycharmProjects/CMS/webapp/frontend/src/services/authService.js"], "sourcesContent": ["import axios from 'axios';\n\nconst API_URL = 'http://localhost:8000/api';\n\n// Crea un'istanza di axios con configurazione personalizzata\nconst axiosInstance = axios.create({\n  baseURL: API_URL,\n  headers: {\n    'Content-Type': 'application/json'\n  }\n});\n\n// Configura axios per includere il token in tutte le richieste\naxiosInstance.interceptors.request.use(\n  (config) => {\n    const token = localStorage.getItem('token');\n    if (token) {\n      config.headers.Authorization = `Bearer ${token}`;\n    }\n    return config;\n  },\n  (error) => {\n    return Promise.reject(error);\n  }\n);\n\n// Gestione degli errori di autenticazione\naxiosInstance.interceptors.response.use(\n  (response) => response,\n  (error) => {\n    if (error.response && error.response.status === 401) {\n      // Se la risposta è 401 Unauthorized, effettua il logout\n      localStorage.removeItem('token');\n      window.location.href = '/login';\n    }\n    return Promise.reject(error);\n  }\n);\n\nconst authService = {\n  // Login standard (admin o utente standard)\n  login: async (credentials, loginType) => {\n    try {\n      if (loginType === 'standard') {\n        // Converti le credenziali nel formato richiesto da OAuth2\n        const formData = new FormData();\n        formData.append('username', credentials.username);\n        formData.append('password', credentials.password);\n\n        // Usa axios direttamente per il login perché richiede FormData\n        const response = await axios.post(`${API_URL}/auth/login`, formData);\n        return response.data;\n      } else if (loginType === 'cantiere') {\n        // Login cantiere\n        const response = await axiosInstance.post('/auth/login/cantiere', {\n          codice_univoco: credentials.codice_univoco,\n          password: credentials.password\n        });\n        return response.data;\n      } else {\n        throw new Error('Tipo di login non valido');\n      }\n    } catch (error) {\n      console.error('Login error:', error);\n      throw error.response ? error.response.data : error;\n    }\n  },\n\n  // Login cantiere\n  loginCantiere: async (credentials) => {\n    try {\n      const response = await axiosInstance.post('/auth/login/cantiere', {\n        codice_univoco: credentials.codice_univoco,\n        password: credentials.password\n      });\n      return response.data;\n    } catch (error) {\n      console.error('Login cantiere error:', error);\n      throw error.response ? error.response.data : error;\n    }\n  },\n\n  // Verifica la validità del token\n  checkToken: async () => {\n    try {\n      const response = await axiosInstance.post('/auth/test-token');\n      return {\n        id: response.data.user_id,\n        username: response.data.username,\n        role: response.data.role\n      };\n    } catch (error) {\n      console.error('Check token error:', error);\n      throw error.response ? error.response.data : error;\n    }\n  },\n\n  // Impersona un altro utente (solo per admin)\n  impersonateUser: async (userId) => {\n    try {\n      const response = await axiosInstance.post('/auth/impersonate', {\n        user_id: userId\n      });\n      return response.data;\n    } catch (error) {\n      console.error('Impersonate user error:', error);\n      throw error.response ? error.response.data : error;\n    }\n  }\n};\n\nexport default authService;\n"], "mappings": "AAAA,OAAOA,KAAK,MAAM,OAAO;AAEzB,MAAMC,OAAO,GAAG,2BAA2B;;AAE3C;AACA,MAAMC,aAAa,GAAGF,KAAK,CAACG,MAAM,CAAC;EACjCC,OAAO,EAAEH,OAAO;EAChBI,OAAO,EAAE;IACP,cAAc,EAAE;EAClB;AACF,CAAC,CAAC;;AAEF;AACAH,aAAa,CAACI,YAAY,CAACC,OAAO,CAACC,GAAG,CACnCC,MAAM,IAAK;EACV,MAAMC,KAAK,GAAGC,YAAY,CAACC,OAAO,CAAC,OAAO,CAAC;EAC3C,IAAIF,KAAK,EAAE;IACTD,MAAM,CAACJ,OAAO,CAACQ,aAAa,GAAG,UAAUH,KAAK,EAAE;EAClD;EACA,OAAOD,MAAM;AACf,CAAC,EACAK,KAAK,IAAK;EACT,OAAOC,OAAO,CAACC,MAAM,CAACF,KAAK,CAAC;AAC9B,CACF,CAAC;;AAED;AACAZ,aAAa,CAACI,YAAY,CAACW,QAAQ,CAACT,GAAG,CACpCS,QAAQ,IAAKA,QAAQ,EACrBH,KAAK,IAAK;EACT,IAAIA,KAAK,CAACG,QAAQ,IAAIH,KAAK,CAACG,QAAQ,CAACC,MAAM,KAAK,GAAG,EAAE;IACnD;IACAP,YAAY,CAACQ,UAAU,CAAC,OAAO,CAAC;IAChCC,MAAM,CAACC,QAAQ,CAACC,IAAI,GAAG,QAAQ;EACjC;EACA,OAAOP,OAAO,CAACC,MAAM,CAACF,KAAK,CAAC;AAC9B,CACF,CAAC;AAED,MAAMS,WAAW,GAAG;EAClB;EACAC,KAAK,EAAE,MAAAA,CAAOC,WAAW,EAAEC,SAAS,KAAK;IACvC,IAAI;MACF,IAAIA,SAAS,KAAK,UAAU,EAAE;QAC5B;QACA,MAAMC,QAAQ,GAAG,IAAIC,QAAQ,CAAC,CAAC;QAC/BD,QAAQ,CAACE,MAAM,CAAC,UAAU,EAAEJ,WAAW,CAACK,QAAQ,CAAC;QACjDH,QAAQ,CAACE,MAAM,CAAC,UAAU,EAAEJ,WAAW,CAACM,QAAQ,CAAC;;QAEjD;QACA,MAAMd,QAAQ,GAAG,MAAMjB,KAAK,CAACgC,IAAI,CAAC,GAAG/B,OAAO,aAAa,EAAE0B,QAAQ,CAAC;QACpE,OAAOV,QAAQ,CAACgB,IAAI;MACtB,CAAC,MAAM,IAAIP,SAAS,KAAK,UAAU,EAAE;QACnC;QACA,MAAMT,QAAQ,GAAG,MAAMf,aAAa,CAAC8B,IAAI,CAAC,sBAAsB,EAAE;UAChEE,cAAc,EAAET,WAAW,CAACS,cAAc;UAC1CH,QAAQ,EAAEN,WAAW,CAACM;QACxB,CAAC,CAAC;QACF,OAAOd,QAAQ,CAACgB,IAAI;MACtB,CAAC,MAAM;QACL,MAAM,IAAIE,KAAK,CAAC,0BAA0B,CAAC;MAC7C;IACF,CAAC,CAAC,OAAOrB,KAAK,EAAE;MACdsB,OAAO,CAACtB,KAAK,CAAC,cAAc,EAAEA,KAAK,CAAC;MACpC,MAAMA,KAAK,CAACG,QAAQ,GAAGH,KAAK,CAACG,QAAQ,CAACgB,IAAI,GAAGnB,KAAK;IACpD;EACF,CAAC;EAED;EACAuB,aAAa,EAAE,MAAOZ,WAAW,IAAK;IACpC,IAAI;MACF,MAAMR,QAAQ,GAAG,MAAMf,aAAa,CAAC8B,IAAI,CAAC,sBAAsB,EAAE;QAChEE,cAAc,EAAET,WAAW,CAACS,cAAc;QAC1CH,QAAQ,EAAEN,WAAW,CAACM;MACxB,CAAC,CAAC;MACF,OAAOd,QAAQ,CAACgB,IAAI;IACtB,CAAC,CAAC,OAAOnB,KAAK,EAAE;MACdsB,OAAO,CAACtB,KAAK,CAAC,uBAAuB,EAAEA,KAAK,CAAC;MAC7C,MAAMA,KAAK,CAACG,QAAQ,GAAGH,KAAK,CAACG,QAAQ,CAACgB,IAAI,GAAGnB,KAAK;IACpD;EACF,CAAC;EAED;EACAwB,UAAU,EAAE,MAAAA,CAAA,KAAY;IACtB,IAAI;MACF,MAAMrB,QAAQ,GAAG,MAAMf,aAAa,CAAC8B,IAAI,CAAC,kBAAkB,CAAC;MAC7D,OAAO;QACLO,EAAE,EAAEtB,QAAQ,CAACgB,IAAI,CAACO,OAAO;QACzBV,QAAQ,EAAEb,QAAQ,CAACgB,IAAI,CAACH,QAAQ;QAChCW,IAAI,EAAExB,QAAQ,CAACgB,IAAI,CAACQ;MACtB,CAAC;IACH,CAAC,CAAC,OAAO3B,KAAK,EAAE;MACdsB,OAAO,CAACtB,KAAK,CAAC,oBAAoB,EAAEA,KAAK,CAAC;MAC1C,MAAMA,KAAK,CAACG,QAAQ,GAAGH,KAAK,CAACG,QAAQ,CAACgB,IAAI,GAAGnB,KAAK;IACpD;EACF,CAAC;EAED;EACA4B,eAAe,EAAE,MAAOC,MAAM,IAAK;IACjC,IAAI;MACF,MAAM1B,QAAQ,GAAG,MAAMf,aAAa,CAAC8B,IAAI,CAAC,mBAAmB,EAAE;QAC7DQ,OAAO,EAAEG;MACX,CAAC,CAAC;MACF,OAAO1B,QAAQ,CAACgB,IAAI;IACtB,CAAC,CAAC,OAAOnB,KAAK,EAAE;MACdsB,OAAO,CAACtB,KAAK,CAAC,yBAAyB,EAAEA,KAAK,CAAC;MAC/C,MAAMA,KAAK,CAACG,QAAQ,GAAGH,KAAK,CAACG,QAAQ,CAACgB,IAAI,GAAGnB,KAAK;IACpD;EACF;AACF,CAAC;AAED,eAAeS,WAAW", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}