{"ast": null, "code": "import axios from 'axios';\nimport config from '../config';\nimport axiosInstance from './axiosConfig';\nconst API_URL = config.API_URL;\nconst reportService = {\n  // Ottiene il report di avanzamento\n  getProgressReport: async (cantiereId, formato = 'pdf') => {\n    try {\n      // Assicurati che cantiereId sia un numero\n      const cantiereIdNum = parseInt(cantiereId, 10);\n      if (isNaN(cantiereIdNum)) {\n        throw new Error(`ID cantiere non valido: ${cantiereId}`);\n      }\n      const response = await axiosInstance.get(`/reports/${cantiereIdNum}/progress?formato=${formato}`);\n      return response.data;\n    } catch (error) {\n      console.error('Get progress report error:', error);\n      throw error.response ? error.response.data : error;\n    }\n  },\n  // Ottiene la distinta materiali (Bill of Quantities)\n  getBillOfQuantities: async (cantiereId, formato = 'pdf') => {\n    try {\n      // Assicurati che cantiereId sia un numero\n      const cantiereIdNum = parseInt(cantiereId, 10);\n      if (isNaN(cantiereIdNum)) {\n        throw new Error(`ID cantiere non valido: ${cantiereId}`);\n      }\n      const response = await axiosInstance.get(`/reports/${cantiereIdNum}/boq?formato=${formato}`);\n      return response.data;\n    } catch (error) {\n      console.error('Get bill of quantities error:', error);\n      throw error.response ? error.response.data : error;\n    }\n  },\n  // Ottiene il report di utilizzo bobine\n  getBobineReport: async (cantiereId, formato = 'pdf') => {\n    try {\n      // Assicurati che cantiereId sia un numero\n      const cantiereIdNum = parseInt(cantiereId, 10);\n      if (isNaN(cantiereIdNum)) {\n        throw new Error(`ID cantiere non valido: ${cantiereId}`);\n      }\n      const response = await axiosInstance.get(`/reports/${cantiereIdNum}/bobine?formato=${formato}`);\n      return response.data;\n    } catch (error) {\n      console.error('Get bobine report error:', error);\n      throw error.response ? error.response.data : error;\n    }\n  },\n  // Ottiene il report di una bobina specifica\n  getBobinaReport: async (cantiereId, idBobina, formato = 'pdf') => {\n    try {\n      // Assicurati che cantiereId sia un numero\n      const cantiereIdNum = parseInt(cantiereId, 10);\n      if (isNaN(cantiereIdNum)) {\n        throw new Error(`ID cantiere non valido: ${cantiereId}`);\n      }\n      const response = await axiosInstance.get(`/reports/${cantiereIdNum}/bobina/${idBobina}?formato=${formato}`);\n      return response.data;\n    } catch (error) {\n      console.error('Get bobina report error:', error);\n      throw error.response ? error.response.data : error;\n    }\n  },\n  // Ottiene il report di posa per periodo\n  getPosaPerPeriodoReport: async (cantiereId, dataInizio, dataFine, formato = 'pdf') => {\n    try {\n      // Assicurati che cantiereId sia un numero\n      const cantiereIdNum = parseInt(cantiereId, 10);\n      if (isNaN(cantiereIdNum)) {\n        throw new Error(`ID cantiere non valido: ${cantiereId}`);\n      }\n      const response = await axiosInstance.get(`/reports/${cantiereIdNum}/posa-periodo?data_inizio=${dataInizio}&data_fine=${dataFine}&formato=${formato}`);\n      return response.data;\n    } catch (error) {\n      console.error('Get posa per periodo report error:', error);\n      throw error.response ? error.response.data : error;\n    }\n  },\n  // Ottiene il report dei cavi per stato\n  getCaviStatoReport: async (cantiereId, formato = 'pdf') => {\n    try {\n      const cantiereIdNum = parseInt(cantiereId, 10);\n      if (isNaN(cantiereIdNum)) {\n        throw new Error(`ID cantiere non valido: ${cantiereId}`);\n      }\n      const response = await axiosInstance.get(`/reports/${cantiereIdNum}/cavi-stato?formato=${formato}`);\n      return response.data;\n    } catch (error) {\n      console.error('Get cavi stato report error:', error);\n      throw error.response ? error.response.data : error;\n    }\n  }\n};\nexport default reportService;", "map": {"version": 3, "names": ["axios", "config", "axiosInstance", "API_URL", "reportService", "getProgressReport", "cantiereId", "formato", "cantiereIdNum", "parseInt", "isNaN", "Error", "response", "get", "data", "error", "console", "getBillOfQuantities", "getBobineReport", "getBobinaReport", "idBobina", "getPosaPerPeriodoReport", "dataInizio", "dataFine", "getCaviStatoReport"], "sources": ["C:/Users/<USER>/PycharmProjects/CMS/webapp/frontend/src/services/reportService.js"], "sourcesContent": ["import axios from 'axios';\r\nimport config from '../config';\r\nimport axiosInstance from './axiosConfig';\r\n\r\nconst API_URL = config.API_URL;\r\n\r\nconst reportService = {\r\n  // Ottiene il report di avanzamento\r\n  getProgressReport: async (cantiereId, formato = 'pdf') => {\r\n    try {\r\n      // Assicurati che cantiereId sia un numero\r\n      const cantiereIdNum = parseInt(cantiereId, 10);\r\n      if (isNaN(cantiereIdNum)) {\r\n        throw new Error(`ID cantiere non valido: ${cantiereId}`);\r\n      }\r\n\r\n      const response = await axiosInstance.get(`/reports/${cantiereIdNum}/progress?formato=${formato}`);\r\n      return response.data;\r\n    } catch (error) {\r\n      console.error('Get progress report error:', error);\r\n      throw error.response ? error.response.data : error;\r\n    }\r\n  },\r\n\r\n  // Ottiene la distinta materiali (Bill of Quantities)\r\n  getBillOfQuantities: async (cantiereId, formato = 'pdf') => {\r\n    try {\r\n      // Assicurati che cantiereId sia un numero\r\n      const cantiereIdNum = parseInt(cantiereId, 10);\r\n      if (isNaN(cantiereIdNum)) {\r\n        throw new Error(`ID cantiere non valido: ${cantiereId}`);\r\n      }\r\n\r\n      const response = await axiosInstance.get(`/reports/${cantiereIdNum}/boq?formato=${formato}`);\r\n      return response.data;\r\n    } catch (error) {\r\n      console.error('Get bill of quantities error:', error);\r\n      throw error.response ? error.response.data : error;\r\n    }\r\n  },\r\n\r\n  // Ottiene il report di utilizzo bobine\r\n  getBobineReport: async (cantiereId, formato = 'pdf') => {\r\n    try {\r\n      // Assicurati che cantiereId sia un numero\r\n      const cantiereIdNum = parseInt(cantiereId, 10);\r\n      if (isNaN(cantiereIdNum)) {\r\n        throw new Error(`ID cantiere non valido: ${cantiereId}`);\r\n      }\r\n\r\n      const response = await axiosInstance.get(`/reports/${cantiereIdNum}/bobine?formato=${formato}`);\r\n      return response.data;\r\n    } catch (error) {\r\n      console.error('Get bobine report error:', error);\r\n      throw error.response ? error.response.data : error;\r\n    }\r\n  },\r\n\r\n  // Ottiene il report di una bobina specifica\r\n  getBobinaReport: async (cantiereId, idBobina, formato = 'pdf') => {\r\n    try {\r\n      // Assicurati che cantiereId sia un numero\r\n      const cantiereIdNum = parseInt(cantiereId, 10);\r\n      if (isNaN(cantiereIdNum)) {\r\n        throw new Error(`ID cantiere non valido: ${cantiereId}`);\r\n      }\r\n\r\n      const response = await axiosInstance.get(`/reports/${cantiereIdNum}/bobina/${idBobina}?formato=${formato}`);\r\n      return response.data;\r\n    } catch (error) {\r\n      console.error('Get bobina report error:', error);\r\n      throw error.response ? error.response.data : error;\r\n    }\r\n  },\r\n\r\n  // Ottiene il report di posa per periodo\r\n  getPosaPerPeriodoReport: async (cantiereId, dataInizio, dataFine, formato = 'pdf') => {\r\n    try {\r\n      // Assicurati che cantiereId sia un numero\r\n      const cantiereIdNum = parseInt(cantiereId, 10);\r\n      if (isNaN(cantiereIdNum)) {\r\n        throw new Error(`ID cantiere non valido: ${cantiereId}`);\r\n      }\r\n\r\n      const response = await axiosInstance.get(\r\n        `/reports/${cantiereIdNum}/posa-periodo?data_inizio=${dataInizio}&data_fine=${dataFine}&formato=${formato}`\r\n      );\r\n      return response.data;\r\n    } catch (error) {\r\n      console.error('Get posa per periodo report error:', error);\r\n      throw error.response ? error.response.data : error;\r\n    }\r\n  },\r\n\r\n  // Ottiene il report dei cavi per stato\r\n  getCaviStatoReport: async (cantiereId, formato = 'pdf') => {\r\n    try {\r\n      const cantiereIdNum = parseInt(cantiereId, 10);\r\n      if (isNaN(cantiereIdNum)) {\r\n        throw new Error(`ID cantiere non valido: ${cantiereId}`);\r\n      }\r\n\r\n      const response = await axiosInstance.get(`/reports/${cantiereIdNum}/cavi-stato?formato=${formato}`);\r\n      return response.data;\r\n    } catch (error) {\r\n      console.error('Get cavi stato report error:', error);\r\n      throw error.response ? error.response.data : error;\r\n    }\r\n  }\r\n};\r\n\r\nexport default reportService;\r\n"], "mappings": "AAAA,OAAOA,KAAK,MAAM,OAAO;AACzB,OAAOC,MAAM,MAAM,WAAW;AAC9B,OAAOC,aAAa,MAAM,eAAe;AAEzC,MAAMC,OAAO,GAAGF,MAAM,CAACE,OAAO;AAE9B,MAAMC,aAAa,GAAG;EACpB;EACAC,iBAAiB,EAAE,MAAAA,CAAOC,UAAU,EAAEC,OAAO,GAAG,KAAK,KAAK;IACxD,IAAI;MACF;MACA,MAAMC,aAAa,GAAGC,QAAQ,CAACH,UAAU,EAAE,EAAE,CAAC;MAC9C,IAAII,KAAK,CAACF,aAAa,CAAC,EAAE;QACxB,MAAM,IAAIG,KAAK,CAAC,2BAA2BL,UAAU,EAAE,CAAC;MAC1D;MAEA,MAAMM,QAAQ,GAAG,MAAMV,aAAa,CAACW,GAAG,CAAC,YAAYL,aAAa,qBAAqBD,OAAO,EAAE,CAAC;MACjG,OAAOK,QAAQ,CAACE,IAAI;IACtB,CAAC,CAAC,OAAOC,KAAK,EAAE;MACdC,OAAO,CAACD,KAAK,CAAC,4BAA4B,EAAEA,KAAK,CAAC;MAClD,MAAMA,KAAK,CAACH,QAAQ,GAAGG,KAAK,CAACH,QAAQ,CAACE,IAAI,GAAGC,KAAK;IACpD;EACF,CAAC;EAED;EACAE,mBAAmB,EAAE,MAAAA,CAAOX,UAAU,EAAEC,OAAO,GAAG,KAAK,KAAK;IAC1D,IAAI;MACF;MACA,MAAMC,aAAa,GAAGC,QAAQ,CAACH,UAAU,EAAE,EAAE,CAAC;MAC9C,IAAII,KAAK,CAACF,aAAa,CAAC,EAAE;QACxB,MAAM,IAAIG,KAAK,CAAC,2BAA2BL,UAAU,EAAE,CAAC;MAC1D;MAEA,MAAMM,QAAQ,GAAG,MAAMV,aAAa,CAACW,GAAG,CAAC,YAAYL,aAAa,gBAAgBD,OAAO,EAAE,CAAC;MAC5F,OAAOK,QAAQ,CAACE,IAAI;IACtB,CAAC,CAAC,OAAOC,KAAK,EAAE;MACdC,OAAO,CAACD,KAAK,CAAC,+BAA+B,EAAEA,KAAK,CAAC;MACrD,MAAMA,KAAK,CAACH,QAAQ,GAAGG,KAAK,CAACH,QAAQ,CAACE,IAAI,GAAGC,KAAK;IACpD;EACF,CAAC;EAED;EACAG,eAAe,EAAE,MAAAA,CAAOZ,UAAU,EAAEC,OAAO,GAAG,KAAK,KAAK;IACtD,IAAI;MACF;MACA,MAAMC,aAAa,GAAGC,QAAQ,CAACH,UAAU,EAAE,EAAE,CAAC;MAC9C,IAAII,KAAK,CAACF,aAAa,CAAC,EAAE;QACxB,MAAM,IAAIG,KAAK,CAAC,2BAA2BL,UAAU,EAAE,CAAC;MAC1D;MAEA,MAAMM,QAAQ,GAAG,MAAMV,aAAa,CAACW,GAAG,CAAC,YAAYL,aAAa,mBAAmBD,OAAO,EAAE,CAAC;MAC/F,OAAOK,QAAQ,CAACE,IAAI;IACtB,CAAC,CAAC,OAAOC,KAAK,EAAE;MACdC,OAAO,CAACD,KAAK,CAAC,0BAA0B,EAAEA,KAAK,CAAC;MAChD,MAAMA,KAAK,CAACH,QAAQ,GAAGG,KAAK,CAACH,QAAQ,CAACE,IAAI,GAAGC,KAAK;IACpD;EACF,CAAC;EAED;EACAI,eAAe,EAAE,MAAAA,CAAOb,UAAU,EAAEc,QAAQ,EAAEb,OAAO,GAAG,KAAK,KAAK;IAChE,IAAI;MACF;MACA,MAAMC,aAAa,GAAGC,QAAQ,CAACH,UAAU,EAAE,EAAE,CAAC;MAC9C,IAAII,KAAK,CAACF,aAAa,CAAC,EAAE;QACxB,MAAM,IAAIG,KAAK,CAAC,2BAA2BL,UAAU,EAAE,CAAC;MAC1D;MAEA,MAAMM,QAAQ,GAAG,MAAMV,aAAa,CAACW,GAAG,CAAC,YAAYL,aAAa,WAAWY,QAAQ,YAAYb,OAAO,EAAE,CAAC;MAC3G,OAAOK,QAAQ,CAACE,IAAI;IACtB,CAAC,CAAC,OAAOC,KAAK,EAAE;MACdC,OAAO,CAACD,KAAK,CAAC,0BAA0B,EAAEA,KAAK,CAAC;MAChD,MAAMA,KAAK,CAACH,QAAQ,GAAGG,KAAK,CAACH,QAAQ,CAACE,IAAI,GAAGC,KAAK;IACpD;EACF,CAAC;EAED;EACAM,uBAAuB,EAAE,MAAAA,CAAOf,UAAU,EAAEgB,UAAU,EAAEC,QAAQ,EAAEhB,OAAO,GAAG,KAAK,KAAK;IACpF,IAAI;MACF;MACA,MAAMC,aAAa,GAAGC,QAAQ,CAACH,UAAU,EAAE,EAAE,CAAC;MAC9C,IAAII,KAAK,CAACF,aAAa,CAAC,EAAE;QACxB,MAAM,IAAIG,KAAK,CAAC,2BAA2BL,UAAU,EAAE,CAAC;MAC1D;MAEA,MAAMM,QAAQ,GAAG,MAAMV,aAAa,CAACW,GAAG,CACtC,YAAYL,aAAa,6BAA6Bc,UAAU,cAAcC,QAAQ,YAAYhB,OAAO,EAC3G,CAAC;MACD,OAAOK,QAAQ,CAACE,IAAI;IACtB,CAAC,CAAC,OAAOC,KAAK,EAAE;MACdC,OAAO,CAACD,KAAK,CAAC,oCAAoC,EAAEA,KAAK,CAAC;MAC1D,MAAMA,KAAK,CAACH,QAAQ,GAAGG,KAAK,CAACH,QAAQ,CAACE,IAAI,GAAGC,KAAK;IACpD;EACF,CAAC;EAED;EACAS,kBAAkB,EAAE,MAAAA,CAAOlB,UAAU,EAAEC,OAAO,GAAG,KAAK,KAAK;IACzD,IAAI;MACF,MAAMC,aAAa,GAAGC,QAAQ,CAACH,UAAU,EAAE,EAAE,CAAC;MAC9C,IAAII,KAAK,CAACF,aAAa,CAAC,EAAE;QACxB,MAAM,IAAIG,KAAK,CAAC,2BAA2BL,UAAU,EAAE,CAAC;MAC1D;MAEA,MAAMM,QAAQ,GAAG,MAAMV,aAAa,CAACW,GAAG,CAAC,YAAYL,aAAa,uBAAuBD,OAAO,EAAE,CAAC;MACnG,OAAOK,QAAQ,CAACE,IAAI;IACtB,CAAC,CAAC,OAAOC,KAAK,EAAE;MACdC,OAAO,CAACD,KAAK,CAAC,8BAA8B,EAAEA,KAAK,CAAC;MACpD,MAAMA,KAAK,CAACH,QAAQ,GAAGG,KAAK,CAACH,QAAQ,CAACE,IAAI,GAAGC,KAAK;IACpD;EACF;AACF,CAAC;AAED,eAAeX,aAAa", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}