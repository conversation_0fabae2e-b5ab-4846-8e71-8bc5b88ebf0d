{"ast": null, "code": "/**\n * A specialized version of `_.every` for arrays without support for\n * iteratee shorthands.\n *\n * @private\n * @param {Array} [array] The array to iterate over.\n * @param {Function} predicate The function invoked per iteration.\n * @returns {boolean} Returns `true` if all elements pass the predicate check,\n *  else `false`.\n */\nfunction arrayEvery(array, predicate) {\n  var index = -1,\n    length = array == null ? 0 : array.length;\n  while (++index < length) {\n    if (!predicate(array[index], index, array)) {\n      return false;\n    }\n  }\n  return true;\n}\nmodule.exports = arrayEvery;", "map": {"version": 3, "names": ["arrayEvery", "array", "predicate", "index", "length", "module", "exports"], "sources": ["C:/Users/<USER>/PycharmProjects/CMS/webapp/frontend/node_modules/lodash/_arrayEvery.js"], "sourcesContent": ["/**\n * A specialized version of `_.every` for arrays without support for\n * iteratee shorthands.\n *\n * @private\n * @param {Array} [array] The array to iterate over.\n * @param {Function} predicate The function invoked per iteration.\n * @returns {boolean} Returns `true` if all elements pass the predicate check,\n *  else `false`.\n */\nfunction arrayEvery(array, predicate) {\n  var index = -1,\n      length = array == null ? 0 : array.length;\n\n  while (++index < length) {\n    if (!predicate(array[index], index, array)) {\n      return false;\n    }\n  }\n  return true;\n}\n\nmodule.exports = arrayEvery;\n"], "mappings": "AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASA,UAAUA,CAACC,KAAK,EAAEC,SAAS,EAAE;EACpC,IAAIC,KAAK,GAAG,CAAC,CAAC;IACVC,MAAM,GAAGH,KAAK,IAAI,IAAI,GAAG,CAAC,GAAGA,KAAK,CAACG,MAAM;EAE7C,OAAO,EAAED,KAAK,GAAGC,MAAM,EAAE;IACvB,IAAI,CAACF,SAAS,CAACD,KAAK,CAACE,KAAK,CAAC,EAAEA,KAAK,EAAEF,KAAK,CAAC,EAAE;MAC1C,OAAO,KAAK;IACd;EACF;EACA,OAAO,IAAI;AACb;AAEAI,MAAM,CAACC,OAAO,GAAGN,UAAU", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}