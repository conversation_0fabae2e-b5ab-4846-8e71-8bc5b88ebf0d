{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\PycharmProjects\\\\CMS\\\\webapp\\\\frontend\\\\src\\\\components\\\\cavi\\\\ModificaBobinaForm.js\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect } from 'react';\nimport { Box, Paper, Typography, TextField, Button, Grid, FormControl, InputLabel, Select, MenuItem, Divider, Alert, CircularProgress, Dialog, DialogTitle, DialogContent, DialogActions, Table, TableBody, TableCell, TableContainer, TableHead, TableRow, Radio, RadioGroup, FormControlLabel, IconButton, InputAdornment } from '@mui/material';\nimport { Search as SearchIcon, Save as SaveIcon, Cancel as CancelIcon, Warning as WarningIcon, Info as InfoIcon } from '@mui/icons-material';\nimport { useNavigate } from 'react-router-dom';\nimport caviService from '../../services/caviService';\nimport parcoCaviService from '../../services/parcoCaviService';\nimport CavoDetailsView from './CavoDetailsView';\nimport { CABLE_STATES, REEL_STATES, determineCableState, determineReelState, canModifyCable, isCableSpare, isCableInstalled, getCableStateColor, getReelStateColor } from '../../utils/stateUtils';\n\n/**\n * Componente per la modifica della bobina di un cavo già posato\n *\n * @param {Object} props - Proprietà del componente\n * @param {string} props.cantiereId - ID del cantiere\n * @param {Function} props.onSuccess - Funzione chiamata al successo dell'operazione\n * @param {Function} props.onError - Funzione chiamata in caso di errore\n */\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst ModificaBobinaForm = ({\n  cantiereId,\n  onSuccess,\n  onError\n}) => {\n  _s();\n  const navigate = useNavigate();\n\n  // Stati per la gestione del form\n  const [loading, setLoading] = useState(false);\n  const [caviLoading, setCaviLoading] = useState(false);\n  const [bobineLoading, setBobineLoading] = useState(false);\n  const [searchResults, setSearchResults] = useState([]);\n  const [showSearchResults, setShowSearchResults] = useState(false);\n\n  // Stati per i dati\n  const [cavi, setCavi] = useState([]);\n  const [bobine, setBobine] = useState([]);\n  const [compatibleBobine, setCompatibleBobine] = useState([]);\n  const [selectedCavo, setSelectedCavo] = useState(null);\n  const [cavoIdInput, setCavoIdInput] = useState('');\n  const [selectedOption, setSelectedOption] = useState('');\n  const [selectedBobinaId, setSelectedBobinaId] = useState('');\n\n  // Stati per i dialoghi\n  const [showConfirmDialog, setShowConfirmDialog] = useState(false);\n  const [confirmDialogMessage, setConfirmDialogMessage] = useState('');\n  const [confirmDialogAction, setConfirmDialogAction] = useState(null);\n  const [showCavoDetailsDialog, setShowCavoDetailsDialog] = useState(false);\n\n  // Carica i cavi all'avvio\n  useEffect(() => {\n    loadCavi();\n  }, [cantiereId]);\n\n  // Carica le bobine quando viene selezionato un cavo\n  useEffect(() => {\n    if (selectedCavo) {\n      loadBobine();\n    }\n  }, [selectedCavo]);\n\n  // Funzione per caricare i cavi\n  const loadCavi = async () => {\n    try {\n      setCaviLoading(true);\n      console.log(`Caricamento cavi per il cantiere ${cantiereId}...`);\n\n      // Carica solo i cavi installati (con metratura_reale > 0)\n      const caviData = await caviService.getCavi(cantiereId, null, 'Installato');\n      console.log(`Caricati ${caviData.length} cavi installati`);\n\n      // Filtra i cavi che hanno metratura_reale > 0\n      const caviInstallati = caviData.filter(cavo => parseFloat(cavo.metratura_reale) > 0 || cavo.stato_installazione === 'Installato');\n      setCavi(caviInstallati);\n      console.log(`Filtrati ${caviInstallati.length} cavi effettivamente installati`);\n    } catch (error) {\n      console.error('Errore durante il caricamento dei cavi:', error);\n      onError('Errore durante il caricamento dei cavi: ' + (error.detail || error.message || 'Errore sconosciuto'));\n    } finally {\n      setCaviLoading(false);\n    }\n  };\n\n  // Funzione per caricare le bobine\n  const loadBobine = async () => {\n    if (!selectedCavo) return;\n    try {\n      setBobineLoading(true);\n      console.log(`Caricamento bobine per il cantiere ${cantiereId}...`);\n\n      // Carica tutte le bobine disponibili\n      const bobineData = await parcoCaviService.getBobine(cantiereId);\n      console.log(`Caricati ${bobineData.length} bobine`);\n\n      // Filtra le bobine compatibili\n      const compatibleBobineData = bobineData.filter(bobina => bobina.tipologia === selectedCavo.tipologia && bobina.sezione === selectedCavo.sezione && (bobina.stato_bobina === 'Disponibile' || bobina.stato_bobina === 'In uso'));\n      setBobine(bobineData);\n      setCompatibleBobine(compatibleBobineData);\n      console.log(`Filtrate ${compatibleBobineData.length} bobine compatibili`);\n    } catch (error) {\n      console.error('Errore durante il caricamento delle bobine:', error);\n      onError('Errore durante il caricamento delle bobine: ' + (error.detail || error.message || 'Errore sconosciuto'));\n    } finally {\n      setBobineLoading(false);\n    }\n  };\n\n  // Gestisce la ricerca di un cavo per ID\n  const handleSearchCavoById = async () => {\n    if (!cavoIdInput.trim()) {\n      onError('Inserisci un ID cavo valido');\n      return;\n    }\n    try {\n      setLoading(true);\n      const cavo = await caviService.getCavoById(cantiereId, cavoIdInput.trim());\n\n      // Verifica che il cavo sia installato\n      if (parseFloat(cavo.metratura_reale) <= 0 && cavo.stato_installazione !== 'Installato') {\n        onError('Il cavo selezionato non risulta installato');\n        setLoading(false);\n        return;\n      }\n      setSelectedCavo(cavo);\n      setSelectedOption(''); // Reset dell'opzione selezionata\n      setSelectedBobinaId(''); // Reset della bobina selezionata\n      setShowSearchResults(false);\n    } catch (error) {\n      console.error('Errore durante la ricerca del cavo:', error);\n      onError('Errore durante la ricerca del cavo: ' + (error.detail || error.message || 'Errore sconosciuto'));\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  // Gestisce la selezione di un cavo dalla lista\n  const handleSelectCavo = cavo => {\n    setSelectedCavo(cavo);\n    setSelectedOption(''); // Reset dell'opzione selezionata\n    setSelectedBobinaId(''); // Reset della bobina selezionata\n    setShowSearchResults(false);\n  };\n\n  // Gestisce il cambio dell'opzione selezionata\n  const handleOptionChange = event => {\n    setSelectedOption(event.target.value);\n    setSelectedBobinaId(''); // Reset della bobina selezionata quando cambia l'opzione\n  };\n\n  // Gestisce la selezione di una bobina\n  const handleSelectBobina = bobinaId => {\n    setSelectedBobinaId(bobinaId);\n  };\n\n  // Gestisce il salvataggio delle modifiche\n  const handleSave = () => {\n    if (!selectedCavo) {\n      onError('Seleziona un cavo prima di procedere');\n      return;\n    }\n    if (!selectedOption) {\n      onError('Seleziona un\\'opzione prima di procedere');\n      return;\n    }\n\n    // Verifica che sia stata selezionata una bobina se l'opzione è \"assegnaNuova\"\n    if (selectedOption === 'assegnaNuova' && !selectedBobinaId) {\n      onError('Seleziona una bobina prima di procedere');\n      return;\n    }\n\n    // Prepara il messaggio di conferma in base all'opzione selezionata\n    let message = '';\n    let action = null;\n    if (selectedOption === 'assegnaNuova') {\n      const bobina = bobine.find(b => b.id_bobina === selectedBobinaId);\n      message = `Sei sicuro di voler assegnare la bobina ${getBobinaNumber(selectedBobinaId)} al cavo ${selectedCavo.id_cavo}?`;\n      action = () => updateBobina(selectedBobinaId);\n    } else if (selectedOption === 'rimuoviBobina') {\n      message = `Sei sicuro di voler assegnare una bobina vuota al cavo ${selectedCavo.id_cavo}?`;\n      action = () => updateBobina('BOBINA_VUOTA');\n    } else if (selectedOption === 'annullaInstallazione') {\n      message = `ATTENZIONE: Questa operazione annullerà l'installazione del cavo ${selectedCavo.id_cavo}. Tutti i metri posati saranno restituiti alla bobina originale. Sei sicuro di voler procedere?`;\n      action = () => annullaInstallazione();\n    }\n    setConfirmDialogMessage(message);\n    setConfirmDialogAction(() => action);\n    setShowConfirmDialog(true);\n  };\n\n  // Funzione per aggiornare la bobina di un cavo\n  const updateBobina = async bobinaId => {\n    try {\n      setLoading(true);\n      await caviService.updateBobina(cantiereId, selectedCavo.id_cavo, bobinaId);\n      onSuccess(`Bobina ${bobinaId === 'BOBINA_VUOTA' ? 'vuota assegnata' : 'assegnata'} con successo`);\n\n      // Reset del form\n      setSelectedCavo(null);\n      setSelectedOption('');\n      setSelectedBobinaId('');\n      setCavoIdInput('');\n\n      // Ricarica i dati\n      loadCavi();\n    } catch (error) {\n      console.error('Errore durante l\\'aggiornamento della bobina:', error);\n      onError('Errore durante l\\'aggiornamento della bobina: ' + (error.detail || error.message || 'Errore sconosciuto'));\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  // Funzione per annullare l'installazione di un cavo\n  const annullaInstallazione = async () => {\n    try {\n      setLoading(true);\n\n      // Chiamata all'API per annullare l'installazione\n      await caviService.cancelInstallation(cantiereId, selectedCavo.id_cavo);\n      onSuccess(`Installazione del cavo ${selectedCavo.id_cavo} annullata con successo`);\n\n      // Reset del form\n      setSelectedCavo(null);\n      setSelectedOption('');\n      setSelectedBobinaId('');\n      setCavoIdInput('');\n\n      // Ricarica i dati\n      loadCavi();\n    } catch (error) {\n      console.error('Errore durante l\\'annullamento dell\\'installazione:', error);\n      onError('Errore durante l\\'annullamento dell\\'installazione: ' + (error.detail || error.message || 'Errore sconosciuto'));\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  // Funzione per estrarre il numero della bobina dall'ID completo\n  const getBobinaNumber = idBobina => {\n    if (idBobina === 'BOBINA_VUOTA') return 'BOBINA VUOTA';\n\n    // L'ID completo è nel formato C{id_cantiere}_B{numero_bobina}\n    if (idBobina && idBobina.includes('_B')) {\n      return idBobina.split('_B')[1];\n    }\n    return idBobina;\n  };\n\n  // Renderizza il form per la selezione del cavo\n  const renderCavoSelectionForm = () => /*#__PURE__*/_jsxDEV(Paper, {\n    sx: {\n      p: 3,\n      mb: 3\n    },\n    children: [/*#__PURE__*/_jsxDEV(Typography, {\n      variant: \"h6\",\n      gutterBottom: true,\n      children: \"Seleziona un cavo posato\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 307,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Grid, {\n      container: true,\n      spacing: 2,\n      alignItems: \"center\",\n      children: [/*#__PURE__*/_jsxDEV(Grid, {\n        item: true,\n        xs: 12,\n        sm: 6,\n        children: /*#__PURE__*/_jsxDEV(TextField, {\n          fullWidth: true,\n          label: \"ID Cavo\",\n          value: cavoIdInput,\n          onChange: e => setCavoIdInput(e.target.value),\n          InputProps: {\n            endAdornment: /*#__PURE__*/_jsxDEV(InputAdornment, {\n              position: \"end\",\n              children: /*#__PURE__*/_jsxDEV(IconButton, {\n                onClick: handleSearchCavoById,\n                disabled: loading || !cavoIdInput.trim(),\n                children: /*#__PURE__*/_jsxDEV(SearchIcon, {}, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 325,\n                  columnNumber: 21\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 321,\n                columnNumber: 19\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 320,\n              columnNumber: 17\n            }, this)\n          }\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 313,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 312,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Grid, {\n        item: true,\n        xs: 12,\n        sm: 6,\n        children: /*#__PURE__*/_jsxDEV(Button, {\n          variant: \"outlined\",\n          onClick: () => setShowSearchResults(!showSearchResults),\n          disabled: caviLoading || cavi.length === 0,\n          fullWidth: true,\n          children: showSearchResults ? 'Nascondi lista cavi' : 'Mostra lista cavi'\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 333,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 332,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 311,\n      columnNumber: 7\n    }, this), caviLoading && /*#__PURE__*/_jsxDEV(Box, {\n      sx: {\n        display: 'flex',\n        justifyContent: 'center',\n        mt: 2\n      },\n      children: /*#__PURE__*/_jsxDEV(CircularProgress, {}, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 346,\n        columnNumber: 11\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 345,\n      columnNumber: 9\n    }, this), showSearchResults && cavi.length > 0 && /*#__PURE__*/_jsxDEV(TableContainer, {\n      component: Paper,\n      sx: {\n        mt: 2,\n        maxHeight: 300\n      },\n      children: /*#__PURE__*/_jsxDEV(Table, {\n        stickyHeader: true,\n        size: \"small\",\n        children: [/*#__PURE__*/_jsxDEV(TableHead, {\n          children: /*#__PURE__*/_jsxDEV(TableRow, {\n            children: [/*#__PURE__*/_jsxDEV(TableCell, {\n              children: \"ID Cavo\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 355,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n              children: \"Tipologia\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 356,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n              children: \"Formazione\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 357,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n              children: \"Metri Posati\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 358,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n              children: \"Bobina\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 359,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n              children: \"Azioni\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 360,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 354,\n            columnNumber: 15\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 353,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(TableBody, {\n          children: cavi.map(cavo => /*#__PURE__*/_jsxDEV(TableRow, {\n            hover: true,\n            children: [/*#__PURE__*/_jsxDEV(TableCell, {\n              children: cavo.id_cavo\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 366,\n              columnNumber: 19\n            }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n              children: cavo.tipologia\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 367,\n              columnNumber: 19\n            }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n              children: cavo.sezione\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 368,\n              columnNumber: 19\n            }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n              children: cavo.metratura_reale\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 369,\n              columnNumber: 19\n            }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n              children: getBobinaNumber(cavo.id_bobina || '')\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 370,\n              columnNumber: 19\n            }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n              children: /*#__PURE__*/_jsxDEV(Button, {\n                size: \"small\",\n                onClick: () => handleSelectCavo(cavo),\n                variant: \"contained\",\n                color: \"primary\",\n                children: \"Seleziona\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 372,\n                columnNumber: 21\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 371,\n              columnNumber: 19\n            }, this)]\n          }, cavo.id_cavo, true, {\n            fileName: _jsxFileName,\n            lineNumber: 365,\n            columnNumber: 17\n          }, this))\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 363,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 352,\n        columnNumber: 11\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 351,\n      columnNumber: 9\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 306,\n    columnNumber: 5\n  }, this);\n\n  // Renderizza i dettagli del cavo selezionato\n  const renderSelectedCavoDetails = () => {\n    if (!selectedCavo) return null;\n    return /*#__PURE__*/_jsxDEV(Paper, {\n      sx: {\n        p: 3,\n        mb: 3\n      },\n      children: [/*#__PURE__*/_jsxDEV(Box, {\n        sx: {\n          display: 'flex',\n          justifyContent: 'space-between',\n          alignItems: 'center',\n          mb: 2\n        },\n        children: [/*#__PURE__*/_jsxDEV(Typography, {\n          variant: \"h6\",\n          children: \"Dettagli del cavo selezionato\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 397,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Button, {\n          variant: \"outlined\",\n          size: \"small\",\n          onClick: () => setShowCavoDetailsDialog(true),\n          children: \"Visualizza dettagli completi\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 400,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 396,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Grid, {\n        container: true,\n        spacing: 2,\n        children: [/*#__PURE__*/_jsxDEV(Grid, {\n          item: true,\n          xs: 12,\n          sm: 6,\n          md: 4,\n          children: [/*#__PURE__*/_jsxDEV(Typography, {\n            variant: \"subtitle2\",\n            children: \"ID Cavo:\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 411,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Typography, {\n            variant: \"body1\",\n            children: selectedCavo.id_cavo\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 412,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 410,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Grid, {\n          item: true,\n          xs: 12,\n          sm: 6,\n          md: 4,\n          children: [/*#__PURE__*/_jsxDEV(Typography, {\n            variant: \"subtitle2\",\n            children: \"Tipologia:\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 415,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Typography, {\n            variant: \"body1\",\n            children: selectedCavo.tipologia\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 416,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 414,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Grid, {\n          item: true,\n          xs: 12,\n          sm: 6,\n          md: 4,\n          children: [/*#__PURE__*/_jsxDEV(Typography, {\n            variant: \"subtitle2\",\n            children: \"Formazione:\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 419,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Typography, {\n            variant: \"body1\",\n            children: selectedCavo.sezione\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 420,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 418,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Grid, {\n          item: true,\n          xs: 12,\n          sm: 6,\n          md: 4,\n          children: [/*#__PURE__*/_jsxDEV(Typography, {\n            variant: \"subtitle2\",\n            children: \"Metri Posati:\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 423,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Typography, {\n            variant: \"body1\",\n            children: selectedCavo.metratura_reale\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 424,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 422,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Grid, {\n          item: true,\n          xs: 12,\n          sm: 6,\n          md: 4,\n          children: [/*#__PURE__*/_jsxDEV(Typography, {\n            variant: \"subtitle2\",\n            children: \"Bobina Attuale:\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 427,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Typography, {\n            variant: \"body1\",\n            children: selectedCavo.id_bobina ? getBobinaNumber(selectedCavo.id_bobina) : 'Nessuna bobina'\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 428,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 426,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Grid, {\n          item: true,\n          xs: 12,\n          sm: 6,\n          md: 4,\n          children: [/*#__PURE__*/_jsxDEV(Typography, {\n            variant: \"subtitle2\",\n            children: \"Stato:\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 433,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Typography, {\n            variant: \"body1\",\n            children: selectedCavo.stato_installazione\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 434,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 432,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 409,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 395,\n      columnNumber: 7\n    }, this);\n  };\n\n  // Renderizza le opzioni di modifica\n  const renderModificaOptions = () => {\n    if (!selectedCavo) return null;\n    return /*#__PURE__*/_jsxDEV(Paper, {\n      sx: {\n        p: 3,\n        mb: 3\n      },\n      children: [/*#__PURE__*/_jsxDEV(Typography, {\n        variant: \"h6\",\n        gutterBottom: true,\n        children: \"Opzioni di modifica\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 447,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(RadioGroup, {\n        value: selectedOption,\n        onChange: handleOptionChange,\n        children: [/*#__PURE__*/_jsxDEV(FormControlLabel, {\n          value: \"assegnaNuova\",\n          control: /*#__PURE__*/_jsxDEV(Radio, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 457,\n            columnNumber: 22\n          }, this),\n          label: \"Assegna nuova bobina\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 455,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(FormControlLabel, {\n          value: \"rimuoviBobina\",\n          control: /*#__PURE__*/_jsxDEV(Radio, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 462,\n            columnNumber: 22\n          }, this),\n          label: \"Assegna bobina vuota\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 460,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(FormControlLabel, {\n          value: \"annullaInstallazione\",\n          control: /*#__PURE__*/_jsxDEV(Radio, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 467,\n            columnNumber: 22\n          }, this),\n          label: \"Annulla installazione\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 465,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 451,\n        columnNumber: 9\n      }, this), selectedOption === 'assegnaNuova' && renderBobineSelection(), selectedOption === 'rimuoviBobina' && /*#__PURE__*/_jsxDEV(Alert, {\n        severity: \"info\",\n        sx: {\n          mt: 2\n        },\n        children: \"Questa operazione assegner\\xE0 una \\\"BOBINA_VUOTA\\\" al cavo, mantenendolo nello stato posato. I metri posati rimarranno invariati e la bobina attuale (se presente) riavr\\xE0 i suoi metri restituiti.\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 475,\n        columnNumber: 11\n      }, this), selectedOption === 'annullaInstallazione' && /*#__PURE__*/_jsxDEV(Alert, {\n        severity: \"warning\",\n        sx: {\n          mt: 2\n        },\n        children: [/*#__PURE__*/_jsxDEV(Typography, {\n          variant: \"body2\",\n          fontWeight: \"bold\",\n          children: \"ATTENZIONE: Questa operazione annuller\\xE0 completamente l'installazione del cavo.\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 483,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(Typography, {\n          variant: \"body2\",\n          children: [\"- Il cavo torner\\xE0 allo stato \\\"Da installare\\\"\", /*#__PURE__*/_jsxDEV(\"br\", {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 487,\n            columnNumber: 59\n          }, this), \"- La metratura reale sar\\xE0 azzerata\", /*#__PURE__*/_jsxDEV(\"br\", {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 488,\n            columnNumber: 49\n          }, this), \"- L'associazione con la bobina sar\\xE0 rimossa\", /*#__PURE__*/_jsxDEV(\"br\", {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 489,\n            columnNumber: 58\n          }, this), \"- I metri posati saranno restituiti alla bobina originale (se presente)\"]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 486,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 482,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(Box, {\n        sx: {\n          mt: 3,\n          display: 'flex',\n          justifyContent: 'flex-end'\n        },\n        children: /*#__PURE__*/_jsxDEV(Button, {\n          variant: \"contained\",\n          color: \"primary\",\n          startIcon: /*#__PURE__*/_jsxDEV(SaveIcon, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 499,\n            columnNumber: 24\n          }, this),\n          onClick: handleSave,\n          disabled: loading || !selectedOption || selectedOption === 'assegnaNuova' && !selectedBobinaId,\n          children: \"Salva modifiche\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 496,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 495,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 446,\n      columnNumber: 7\n    }, this);\n  };\n\n  // Renderizza la selezione delle bobine\n  const renderBobineSelection = () => {\n    if (bobineLoading) {\n      return /*#__PURE__*/_jsxDEV(Box, {\n        sx: {\n          display: 'flex',\n          justifyContent: 'center',\n          mt: 2\n        },\n        children: /*#__PURE__*/_jsxDEV(CircularProgress, {}, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 515,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 514,\n        columnNumber: 9\n      }, this);\n    }\n    if (compatibleBobine.length === 0) {\n      return /*#__PURE__*/_jsxDEV(Alert, {\n        severity: \"warning\",\n        sx: {\n          mt: 2\n        },\n        children: \"Non ci sono bobine compatibili disponibili.\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 522,\n        columnNumber: 9\n      }, this);\n    }\n    return /*#__PURE__*/_jsxDEV(Box, {\n      sx: {\n        mt: 2\n      },\n      children: [/*#__PURE__*/_jsxDEV(Typography, {\n        variant: \"subtitle1\",\n        gutterBottom: true,\n        children: \"Bobine compatibili disponibili:\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 530,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(TableContainer, {\n        component: Paper,\n        sx: {\n          maxHeight: 300\n        },\n        children: /*#__PURE__*/_jsxDEV(Table, {\n          stickyHeader: true,\n          size: \"small\",\n          children: [/*#__PURE__*/_jsxDEV(TableHead, {\n            children: /*#__PURE__*/_jsxDEV(TableRow, {\n              children: [/*#__PURE__*/_jsxDEV(TableCell, {\n                padding: \"checkbox\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 538,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n                children: \"ID Bobina\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 539,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n                children: \"Tipologia\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 540,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n                children: \"Formazione\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 541,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n                children: \"Metri Residui\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 542,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n                children: \"Stato\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 543,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 537,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 536,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(TableBody, {\n            children: compatibleBobine.map(bobina => /*#__PURE__*/_jsxDEV(TableRow, {\n              hover: true,\n              selected: selectedBobinaId === bobina.id_bobina,\n              onClick: () => handleSelectBobina(bobina.id_bobina),\n              children: [/*#__PURE__*/_jsxDEV(TableCell, {\n                padding: \"checkbox\",\n                children: /*#__PURE__*/_jsxDEV(Radio, {\n                  checked: selectedBobinaId === bobina.id_bobina,\n                  onChange: () => handleSelectBobina(bobina.id_bobina)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 555,\n                  columnNumber: 21\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 554,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n                children: getBobinaNumber(bobina.id_bobina)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 560,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n                children: bobina.tipologia\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 561,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n                children: bobina.sezione\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 562,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n                children: bobina.metri_residui\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 563,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n                children: bobina.stato_bobina\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 564,\n                columnNumber: 19\n              }, this)]\n            }, bobina.id_bobina, true, {\n              fileName: _jsxFileName,\n              lineNumber: 548,\n              columnNumber: 17\n            }, this))\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 546,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 535,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 534,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 529,\n      columnNumber: 7\n    }, this);\n  };\n  return /*#__PURE__*/_jsxDEV(Box, {\n    children: [renderCavoSelectionForm(), renderSelectedCavoDetails(), renderModificaOptions(), /*#__PURE__*/_jsxDEV(Dialog, {\n      open: showCavoDetailsDialog,\n      onClose: () => setShowCavoDetailsDialog(false),\n      maxWidth: \"md\",\n      fullWidth: true,\n      children: [/*#__PURE__*/_jsxDEV(DialogTitle, {\n        children: \"Dettagli completi del cavo\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 592,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(DialogContent, {\n        children: selectedCavo && /*#__PURE__*/_jsxDEV(CavoDetailsView, {\n          cavo: selectedCavo\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 594,\n          columnNumber: 28\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 593,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(DialogActions, {\n        children: /*#__PURE__*/_jsxDEV(Button, {\n          onClick: () => setShowCavoDetailsDialog(false),\n          children: \"Chiudi\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 597,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 596,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 586,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Dialog, {\n      open: showConfirmDialog,\n      onClose: () => setShowConfirmDialog(false),\n      children: [/*#__PURE__*/_jsxDEV(DialogTitle, {\n        children: \"Conferma operazione\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 606,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(DialogContent, {\n        children: /*#__PURE__*/_jsxDEV(Typography, {\n          children: confirmDialogMessage\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 608,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 607,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(DialogActions, {\n        children: [/*#__PURE__*/_jsxDEV(Button, {\n          onClick: () => setShowConfirmDialog(false),\n          color: \"primary\",\n          children: \"Annulla\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 611,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Button, {\n          onClick: () => {\n            setShowConfirmDialog(false);\n            if (confirmDialogAction) confirmDialogAction();\n          },\n          color: \"primary\",\n          variant: \"contained\",\n          autoFocus: true,\n          children: \"Conferma\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 617,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 610,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 602,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 575,\n    columnNumber: 5\n  }, this);\n};\n_s(ModificaBobinaForm, \"9sZebqhT2FlVREop/01IQsqJRGg=\", false, function () {\n  return [useNavigate];\n});\n_c = ModificaBobinaForm;\nexport default ModificaBobinaForm;\nvar _c;\n$RefreshReg$(_c, \"ModificaBobinaForm\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "Box", "Paper", "Typography", "TextField", "<PERSON><PERSON>", "Grid", "FormControl", "InputLabel", "Select", "MenuItem", "Divider", "<PERSON><PERSON>", "CircularProgress", "Dialog", "DialogTitle", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "DialogActions", "Table", "TableBody", "TableCell", "TableContainer", "TableHead", "TableRow", "Radio", "RadioGroup", "FormControlLabel", "IconButton", "InputAdornment", "Search", "SearchIcon", "Save", "SaveIcon", "Cancel", "CancelIcon", "Warning", "WarningIcon", "Info", "InfoIcon", "useNavigate", "caviService", "parcoCaviService", "CavoDetailsView", "CABLE_STATES", "REEL_STATES", "determineCableState", "determineReelState", "canModifyCable", "isCableSpare", "isCableInstalled", "getCableStateColor", "getReelStateColor", "jsxDEV", "_jsxDEV", "ModificaBobinaForm", "cantiereId", "onSuccess", "onError", "_s", "navigate", "loading", "setLoading", "caviLoading", "setCaviLoading", "bob<PERSON><PERSON><PERSON><PERSON>", "setBobineLoading", "searchResults", "setSearchResults", "showSearchResults", "setShowSearchResults", "cavi", "<PERSON><PERSON><PERSON>", "bobine", "set<PERSON>ob<PERSON>", "compatibleBobine", "setCompatibleBobine", "selected<PERSON><PERSON><PERSON>", "setSelectedCavo", "cavoIdInput", "setCavoIdInput", "selectedOption", "setSelectedOption", "selectedBobinaId", "setSelectedBobinaId", "showConfirmDialog", "setShowConfirmDialog", "confirmDialogMessage", "setConfirmDialogMessage", "confirmDialogAction", "setConfirmDialogAction", "showCavoDetailsDialog", "setShowCavoDetailsDialog", "loadCavi", "loadBobine", "console", "log", "caviData", "get<PERSON><PERSON>", "length", "caviInstallati", "filter", "cavo", "parseFloat", "metratura_reale", "stato_installazione", "error", "detail", "message", "bobine<PERSON><PERSON>", "getBobine", "compatibleBobineData", "bobina", "tipologia", "sezione", "stato_bobina", "handleSearchCavoById", "trim", "getCavoById", "handleSelectCavo", "handleOptionChange", "event", "target", "value", "handleSelectBobina", "bobina<PERSON>d", "handleSave", "action", "find", "b", "id_bobina", "getBobinaNumber", "id_cavo", "updateBobina", "annullaInstallazione", "cancelInstallation", "idBobina", "includes", "split", "renderCavoSelectionForm", "sx", "p", "mb", "children", "variant", "gutterBottom", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "container", "spacing", "alignItems", "item", "xs", "sm", "fullWidth", "label", "onChange", "e", "InputProps", "endAdornment", "position", "onClick", "disabled", "display", "justifyContent", "mt", "component", "maxHeight", "<PERSON><PERSON><PERSON><PERSON>", "size", "map", "hover", "color", "renderSelectedCavoDetails", "md", "renderModificaOptions", "control", "renderBobineSelection", "severity", "fontWeight", "startIcon", "padding", "selected", "checked", "metri_residui", "open", "onClose", "max<PERSON><PERSON><PERSON>", "autoFocus", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/PycharmProjects/CMS/webapp/frontend/src/components/cavi/ModificaBobinaForm.js"], "sourcesContent": ["import React, { useState, useEffect } from 'react';\nimport {\n  Box,\n  Paper,\n  Typography,\n  TextField,\n  Button,\n  Grid,\n  FormControl,\n  InputLabel,\n  Select,\n  MenuItem,\n  Divider,\n  Alert,\n  CircularProgress,\n  Dialog,\n  DialogTitle,\n  DialogContent,\n  DialogActions,\n  Table,\n  TableBody,\n  TableCell,\n  TableContainer,\n  TableHead,\n  TableRow,\n  Radio,\n  RadioGroup,\n  FormControlLabel,\n  IconButton,\n  InputAdornment\n} from '@mui/material';\nimport {\n  Search as SearchIcon,\n  Save as SaveIcon,\n  Cancel as CancelIcon,\n  Warning as WarningIcon,\n  Info as InfoIcon\n} from '@mui/icons-material';\nimport { useNavigate } from 'react-router-dom';\nimport caviService from '../../services/caviService';\nimport parcoCaviService from '../../services/parcoCaviService';\nimport CavoDetailsView from './CavoDetailsView';\nimport {\n  CABLE_STATES,\n  REEL_STATES,\n  determineCableState,\n  determineReelState,\n  canModifyCable,\n  isCableSpare,\n  isCableInstalled,\n  getCableStateColor,\n  getReelStateColor\n} from '../../utils/stateUtils';\n\n/**\n * Componente per la modifica della bobina di un cavo già posato\n *\n * @param {Object} props - Proprietà del componente\n * @param {string} props.cantiereId - ID del cantiere\n * @param {Function} props.onSuccess - Funzione chiamata al successo dell'operazione\n * @param {Function} props.onError - Funzione chiamata in caso di errore\n */\nconst ModificaBobinaForm = ({ cantiereId, onSuccess, onError }) => {\n  const navigate = useNavigate();\n\n  // Stati per la gestione del form\n  const [loading, setLoading] = useState(false);\n  const [caviLoading, setCaviLoading] = useState(false);\n  const [bobineLoading, setBobineLoading] = useState(false);\n  const [searchResults, setSearchResults] = useState([]);\n  const [showSearchResults, setShowSearchResults] = useState(false);\n\n  // Stati per i dati\n  const [cavi, setCavi] = useState([]);\n  const [bobine, setBobine] = useState([]);\n  const [compatibleBobine, setCompatibleBobine] = useState([]);\n  const [selectedCavo, setSelectedCavo] = useState(null);\n  const [cavoIdInput, setCavoIdInput] = useState('');\n  const [selectedOption, setSelectedOption] = useState('');\n  const [selectedBobinaId, setSelectedBobinaId] = useState('');\n\n  // Stati per i dialoghi\n  const [showConfirmDialog, setShowConfirmDialog] = useState(false);\n  const [confirmDialogMessage, setConfirmDialogMessage] = useState('');\n  const [confirmDialogAction, setConfirmDialogAction] = useState(null);\n  const [showCavoDetailsDialog, setShowCavoDetailsDialog] = useState(false);\n\n  // Carica i cavi all'avvio\n  useEffect(() => {\n    loadCavi();\n  }, [cantiereId]);\n\n  // Carica le bobine quando viene selezionato un cavo\n  useEffect(() => {\n    if (selectedCavo) {\n      loadBobine();\n    }\n  }, [selectedCavo]);\n\n  // Funzione per caricare i cavi\n  const loadCavi = async () => {\n    try {\n      setCaviLoading(true);\n      console.log(`Caricamento cavi per il cantiere ${cantiereId}...`);\n\n      // Carica solo i cavi installati (con metratura_reale > 0)\n      const caviData = await caviService.getCavi(cantiereId, null, 'Installato');\n      console.log(`Caricati ${caviData.length} cavi installati`);\n\n      // Filtra i cavi che hanno metratura_reale > 0\n      const caviInstallati = caviData.filter(cavo =>\n        parseFloat(cavo.metratura_reale) > 0 || cavo.stato_installazione === 'Installato'\n      );\n\n      setCavi(caviInstallati);\n      console.log(`Filtrati ${caviInstallati.length} cavi effettivamente installati`);\n    } catch (error) {\n      console.error('Errore durante il caricamento dei cavi:', error);\n      onError('Errore durante il caricamento dei cavi: ' + (error.detail || error.message || 'Errore sconosciuto'));\n    } finally {\n      setCaviLoading(false);\n    }\n  };\n\n  // Funzione per caricare le bobine\n  const loadBobine = async () => {\n    if (!selectedCavo) return;\n\n    try {\n      setBobineLoading(true);\n      console.log(`Caricamento bobine per il cantiere ${cantiereId}...`);\n\n      // Carica tutte le bobine disponibili\n      const bobineData = await parcoCaviService.getBobine(cantiereId);\n      console.log(`Caricati ${bobineData.length} bobine`);\n\n      // Filtra le bobine compatibili\n      const compatibleBobineData = bobineData.filter(bobina =>\n        bobina.tipologia === selectedCavo.tipologia &&\n        bobina.sezione === selectedCavo.sezione &&\n        (bobina.stato_bobina === 'Disponibile' || bobina.stato_bobina === 'In uso')\n      );\n\n      setBobine(bobineData);\n      setCompatibleBobine(compatibleBobineData);\n      console.log(`Filtrate ${compatibleBobineData.length} bobine compatibili`);\n    } catch (error) {\n      console.error('Errore durante il caricamento delle bobine:', error);\n      onError('Errore durante il caricamento delle bobine: ' + (error.detail || error.message || 'Errore sconosciuto'));\n    } finally {\n      setBobineLoading(false);\n    }\n  };\n\n  // Gestisce la ricerca di un cavo per ID\n  const handleSearchCavoById = async () => {\n    if (!cavoIdInput.trim()) {\n      onError('Inserisci un ID cavo valido');\n      return;\n    }\n\n    try {\n      setLoading(true);\n      const cavo = await caviService.getCavoById(cantiereId, cavoIdInput.trim());\n\n      // Verifica che il cavo sia installato\n      if (parseFloat(cavo.metratura_reale) <= 0 && cavo.stato_installazione !== 'Installato') {\n        onError('Il cavo selezionato non risulta installato');\n        setLoading(false);\n        return;\n      }\n\n      setSelectedCavo(cavo);\n      setSelectedOption(''); // Reset dell'opzione selezionata\n      setSelectedBobinaId(''); // Reset della bobina selezionata\n      setShowSearchResults(false);\n    } catch (error) {\n      console.error('Errore durante la ricerca del cavo:', error);\n      onError('Errore durante la ricerca del cavo: ' + (error.detail || error.message || 'Errore sconosciuto'));\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  // Gestisce la selezione di un cavo dalla lista\n  const handleSelectCavo = (cavo) => {\n    setSelectedCavo(cavo);\n    setSelectedOption(''); // Reset dell'opzione selezionata\n    setSelectedBobinaId(''); // Reset della bobina selezionata\n    setShowSearchResults(false);\n  };\n\n  // Gestisce il cambio dell'opzione selezionata\n  const handleOptionChange = (event) => {\n    setSelectedOption(event.target.value);\n    setSelectedBobinaId(''); // Reset della bobina selezionata quando cambia l'opzione\n  };\n\n  // Gestisce la selezione di una bobina\n  const handleSelectBobina = (bobinaId) => {\n    setSelectedBobinaId(bobinaId);\n  };\n\n  // Gestisce il salvataggio delle modifiche\n  const handleSave = () => {\n    if (!selectedCavo) {\n      onError('Seleziona un cavo prima di procedere');\n      return;\n    }\n\n    if (!selectedOption) {\n      onError('Seleziona un\\'opzione prima di procedere');\n      return;\n    }\n\n    // Verifica che sia stata selezionata una bobina se l'opzione è \"assegnaNuova\"\n    if (selectedOption === 'assegnaNuova' && !selectedBobinaId) {\n      onError('Seleziona una bobina prima di procedere');\n      return;\n    }\n\n    // Prepara il messaggio di conferma in base all'opzione selezionata\n    let message = '';\n    let action = null;\n\n    if (selectedOption === 'assegnaNuova') {\n      const bobina = bobine.find(b => b.id_bobina === selectedBobinaId);\n      message = `Sei sicuro di voler assegnare la bobina ${getBobinaNumber(selectedBobinaId)} al cavo ${selectedCavo.id_cavo}?`;\n      action = () => updateBobina(selectedBobinaId);\n    } else if (selectedOption === 'rimuoviBobina') {\n      message = `Sei sicuro di voler assegnare una bobina vuota al cavo ${selectedCavo.id_cavo}?`;\n      action = () => updateBobina('BOBINA_VUOTA');\n    } else if (selectedOption === 'annullaInstallazione') {\n      message = `ATTENZIONE: Questa operazione annullerà l'installazione del cavo ${selectedCavo.id_cavo}. Tutti i metri posati saranno restituiti alla bobina originale. Sei sicuro di voler procedere?`;\n      action = () => annullaInstallazione();\n    }\n\n    setConfirmDialogMessage(message);\n    setConfirmDialogAction(() => action);\n    setShowConfirmDialog(true);\n  };\n\n  // Funzione per aggiornare la bobina di un cavo\n  const updateBobina = async (bobinaId) => {\n    try {\n      setLoading(true);\n      await caviService.updateBobina(cantiereId, selectedCavo.id_cavo, bobinaId);\n\n      onSuccess(`Bobina ${bobinaId === 'BOBINA_VUOTA' ? 'vuota assegnata' : 'assegnata'} con successo`);\n\n      // Reset del form\n      setSelectedCavo(null);\n      setSelectedOption('');\n      setSelectedBobinaId('');\n      setCavoIdInput('');\n\n      // Ricarica i dati\n      loadCavi();\n    } catch (error) {\n      console.error('Errore durante l\\'aggiornamento della bobina:', error);\n      onError('Errore durante l\\'aggiornamento della bobina: ' + (error.detail || error.message || 'Errore sconosciuto'));\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  // Funzione per annullare l'installazione di un cavo\n  const annullaInstallazione = async () => {\n    try {\n      setLoading(true);\n\n      // Chiamata all'API per annullare l'installazione\n      await caviService.cancelInstallation(cantiereId, selectedCavo.id_cavo);\n\n      onSuccess(`Installazione del cavo ${selectedCavo.id_cavo} annullata con successo`);\n\n      // Reset del form\n      setSelectedCavo(null);\n      setSelectedOption('');\n      setSelectedBobinaId('');\n      setCavoIdInput('');\n\n      // Ricarica i dati\n      loadCavi();\n    } catch (error) {\n      console.error('Errore durante l\\'annullamento dell\\'installazione:', error);\n      onError('Errore durante l\\'annullamento dell\\'installazione: ' + (error.detail || error.message || 'Errore sconosciuto'));\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  // Funzione per estrarre il numero della bobina dall'ID completo\n  const getBobinaNumber = (idBobina) => {\n    if (idBobina === 'BOBINA_VUOTA') return 'BOBINA VUOTA';\n\n    // L'ID completo è nel formato C{id_cantiere}_B{numero_bobina}\n    if (idBobina && idBobina.includes('_B')) {\n      return idBobina.split('_B')[1];\n    }\n    return idBobina;\n  };\n\n  // Renderizza il form per la selezione del cavo\n  const renderCavoSelectionForm = () => (\n    <Paper sx={{ p: 3, mb: 3 }}>\n      <Typography variant=\"h6\" gutterBottom>\n        Seleziona un cavo posato\n      </Typography>\n\n      <Grid container spacing={2} alignItems=\"center\">\n        <Grid item xs={12} sm={6}>\n          <TextField\n            fullWidth\n            label=\"ID Cavo\"\n            value={cavoIdInput}\n            onChange={(e) => setCavoIdInput(e.target.value)}\n            InputProps={{\n              endAdornment: (\n                <InputAdornment position=\"end\">\n                  <IconButton\n                    onClick={handleSearchCavoById}\n                    disabled={loading || !cavoIdInput.trim()}\n                  >\n                    <SearchIcon />\n                  </IconButton>\n                </InputAdornment>\n              )\n            }}\n          />\n        </Grid>\n        <Grid item xs={12} sm={6}>\n          <Button\n            variant=\"outlined\"\n            onClick={() => setShowSearchResults(!showSearchResults)}\n            disabled={caviLoading || cavi.length === 0}\n            fullWidth\n          >\n            {showSearchResults ? 'Nascondi lista cavi' : 'Mostra lista cavi'}\n          </Button>\n        </Grid>\n      </Grid>\n\n      {caviLoading && (\n        <Box sx={{ display: 'flex', justifyContent: 'center', mt: 2 }}>\n          <CircularProgress />\n        </Box>\n      )}\n\n      {showSearchResults && cavi.length > 0 && (\n        <TableContainer component={Paper} sx={{ mt: 2, maxHeight: 300 }}>\n          <Table stickyHeader size=\"small\">\n            <TableHead>\n              <TableRow>\n                <TableCell>ID Cavo</TableCell>\n                <TableCell>Tipologia</TableCell>\n                <TableCell>Formazione</TableCell>\n                <TableCell>Metri Posati</TableCell>\n                <TableCell>Bobina</TableCell>\n                <TableCell>Azioni</TableCell>\n              </TableRow>\n            </TableHead>\n            <TableBody>\n              {cavi.map((cavo) => (\n                <TableRow key={cavo.id_cavo} hover>\n                  <TableCell>{cavo.id_cavo}</TableCell>\n                  <TableCell>{cavo.tipologia}</TableCell>\n                  <TableCell>{cavo.sezione}</TableCell>\n                  <TableCell>{cavo.metratura_reale}</TableCell>\n                  <TableCell>{getBobinaNumber(cavo.id_bobina || '')}</TableCell>\n                  <TableCell>\n                    <Button\n                      size=\"small\"\n                      onClick={() => handleSelectCavo(cavo)}\n                      variant=\"contained\"\n                      color=\"primary\"\n                    >\n                      Seleziona\n                    </Button>\n                  </TableCell>\n                </TableRow>\n              ))}\n            </TableBody>\n          </Table>\n        </TableContainer>\n      )}\n    </Paper>\n  );\n\n  // Renderizza i dettagli del cavo selezionato\n  const renderSelectedCavoDetails = () => {\n    if (!selectedCavo) return null;\n\n    return (\n      <Paper sx={{ p: 3, mb: 3 }}>\n        <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 2 }}>\n          <Typography variant=\"h6\">\n            Dettagli del cavo selezionato\n          </Typography>\n          <Button\n            variant=\"outlined\"\n            size=\"small\"\n            onClick={() => setShowCavoDetailsDialog(true)}\n          >\n            Visualizza dettagli completi\n          </Button>\n        </Box>\n\n        <Grid container spacing={2}>\n          <Grid item xs={12} sm={6} md={4}>\n            <Typography variant=\"subtitle2\">ID Cavo:</Typography>\n            <Typography variant=\"body1\">{selectedCavo.id_cavo}</Typography>\n          </Grid>\n          <Grid item xs={12} sm={6} md={4}>\n            <Typography variant=\"subtitle2\">Tipologia:</Typography>\n            <Typography variant=\"body1\">{selectedCavo.tipologia}</Typography>\n          </Grid>\n          <Grid item xs={12} sm={6} md={4}>\n            <Typography variant=\"subtitle2\">Formazione:</Typography>\n            <Typography variant=\"body1\">{selectedCavo.sezione}</Typography>\n          </Grid>\n          <Grid item xs={12} sm={6} md={4}>\n            <Typography variant=\"subtitle2\">Metri Posati:</Typography>\n            <Typography variant=\"body1\">{selectedCavo.metratura_reale}</Typography>\n          </Grid>\n          <Grid item xs={12} sm={6} md={4}>\n            <Typography variant=\"subtitle2\">Bobina Attuale:</Typography>\n            <Typography variant=\"body1\">\n              {selectedCavo.id_bobina ? getBobinaNumber(selectedCavo.id_bobina) : 'Nessuna bobina'}\n            </Typography>\n          </Grid>\n          <Grid item xs={12} sm={6} md={4}>\n            <Typography variant=\"subtitle2\">Stato:</Typography>\n            <Typography variant=\"body1\">{selectedCavo.stato_installazione}</Typography>\n          </Grid>\n        </Grid>\n      </Paper>\n    );\n  };\n\n  // Renderizza le opzioni di modifica\n  const renderModificaOptions = () => {\n    if (!selectedCavo) return null;\n\n    return (\n      <Paper sx={{ p: 3, mb: 3 }}>\n        <Typography variant=\"h6\" gutterBottom>\n          Opzioni di modifica\n        </Typography>\n\n        <RadioGroup\n          value={selectedOption}\n          onChange={handleOptionChange}\n        >\n          <FormControlLabel\n            value=\"assegnaNuova\"\n            control={<Radio />}\n            label=\"Assegna nuova bobina\"\n          />\n          <FormControlLabel\n            value=\"rimuoviBobina\"\n            control={<Radio />}\n            label=\"Assegna bobina vuota\"\n          />\n          <FormControlLabel\n            value=\"annullaInstallazione\"\n            control={<Radio />}\n            label=\"Annulla installazione\"\n          />\n        </RadioGroup>\n\n        {selectedOption === 'assegnaNuova' && renderBobineSelection()}\n\n        {selectedOption === 'rimuoviBobina' && (\n          <Alert severity=\"info\" sx={{ mt: 2 }}>\n            Questa operazione assegnerà una \"BOBINA_VUOTA\" al cavo, mantenendolo nello stato posato.\n            I metri posati rimarranno invariati e la bobina attuale (se presente) riavrà i suoi metri restituiti.\n          </Alert>\n        )}\n\n        {selectedOption === 'annullaInstallazione' && (\n          <Alert severity=\"warning\" sx={{ mt: 2 }}>\n            <Typography variant=\"body2\" fontWeight=\"bold\">\n              ATTENZIONE: Questa operazione annullerà completamente l'installazione del cavo.\n            </Typography>\n            <Typography variant=\"body2\">\n              - Il cavo tornerà allo stato \"Da installare\"<br />\n              - La metratura reale sarà azzerata<br />\n              - L'associazione con la bobina sarà rimossa<br />\n              - I metri posati saranno restituiti alla bobina originale (se presente)\n            </Typography>\n          </Alert>\n        )}\n\n        <Box sx={{ mt: 3, display: 'flex', justifyContent: 'flex-end' }}>\n          <Button\n            variant=\"contained\"\n            color=\"primary\"\n            startIcon={<SaveIcon />}\n            onClick={handleSave}\n            disabled={loading || !selectedOption || (selectedOption === 'assegnaNuova' && !selectedBobinaId)}\n          >\n            Salva modifiche\n          </Button>\n        </Box>\n      </Paper>\n    );\n  };\n\n  // Renderizza la selezione delle bobine\n  const renderBobineSelection = () => {\n    if (bobineLoading) {\n      return (\n        <Box sx={{ display: 'flex', justifyContent: 'center', mt: 2 }}>\n          <CircularProgress />\n        </Box>\n      );\n    }\n\n    if (compatibleBobine.length === 0) {\n      return (\n        <Alert severity=\"warning\" sx={{ mt: 2 }}>\n          Non ci sono bobine compatibili disponibili.\n        </Alert>\n      );\n    }\n\n    return (\n      <Box sx={{ mt: 2 }}>\n        <Typography variant=\"subtitle1\" gutterBottom>\n          Bobine compatibili disponibili:\n        </Typography>\n\n        <TableContainer component={Paper} sx={{ maxHeight: 300 }}>\n          <Table stickyHeader size=\"small\">\n            <TableHead>\n              <TableRow>\n                <TableCell padding=\"checkbox\"></TableCell>\n                <TableCell>ID Bobina</TableCell>\n                <TableCell>Tipologia</TableCell>\n                <TableCell>Formazione</TableCell>\n                <TableCell>Metri Residui</TableCell>\n                <TableCell>Stato</TableCell>\n              </TableRow>\n            </TableHead>\n            <TableBody>\n              {compatibleBobine.map((bobina) => (\n                <TableRow\n                  key={bobina.id_bobina}\n                  hover\n                  selected={selectedBobinaId === bobina.id_bobina}\n                  onClick={() => handleSelectBobina(bobina.id_bobina)}\n                >\n                  <TableCell padding=\"checkbox\">\n                    <Radio\n                      checked={selectedBobinaId === bobina.id_bobina}\n                      onChange={() => handleSelectBobina(bobina.id_bobina)}\n                    />\n                  </TableCell>\n                  <TableCell>{getBobinaNumber(bobina.id_bobina)}</TableCell>\n                  <TableCell>{bobina.tipologia}</TableCell>\n                  <TableCell>{bobina.sezione}</TableCell>\n                  <TableCell>{bobina.metri_residui}</TableCell>\n                  <TableCell>{bobina.stato_bobina}</TableCell>\n                </TableRow>\n              ))}\n            </TableBody>\n          </Table>\n        </TableContainer>\n      </Box>\n    );\n  };\n\n  return (\n    <Box>\n      {/* Form per la selezione del cavo */}\n      {renderCavoSelectionForm()}\n\n      {/* Dettagli del cavo selezionato */}\n      {renderSelectedCavoDetails()}\n\n      {/* Opzioni di modifica */}\n      {renderModificaOptions()}\n\n      {/* Dialog per la visualizzazione dei dettagli completi del cavo */}\n      <Dialog\n        open={showCavoDetailsDialog}\n        onClose={() => setShowCavoDetailsDialog(false)}\n        maxWidth=\"md\"\n        fullWidth\n      >\n        <DialogTitle>Dettagli completi del cavo</DialogTitle>\n        <DialogContent>\n          {selectedCavo && <CavoDetailsView cavo={selectedCavo} />}\n        </DialogContent>\n        <DialogActions>\n          <Button onClick={() => setShowCavoDetailsDialog(false)}>Chiudi</Button>\n        </DialogActions>\n      </Dialog>\n\n      {/* Dialog di conferma */}\n      <Dialog\n        open={showConfirmDialog}\n        onClose={() => setShowConfirmDialog(false)}\n      >\n        <DialogTitle>Conferma operazione</DialogTitle>\n        <DialogContent>\n          <Typography>{confirmDialogMessage}</Typography>\n        </DialogContent>\n        <DialogActions>\n          <Button\n            onClick={() => setShowConfirmDialog(false)}\n            color=\"primary\"\n          >\n            Annulla\n          </Button>\n          <Button\n            onClick={() => {\n              setShowConfirmDialog(false);\n              if (confirmDialogAction) confirmDialogAction();\n            }}\n            color=\"primary\"\n            variant=\"contained\"\n            autoFocus\n          >\n            Conferma\n          </Button>\n        </DialogActions>\n      </Dialog>\n    </Box>\n  );\n};\n\nexport default ModificaBobinaForm;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAClD,SACEC,GAAG,EACHC,KAAK,EACLC,UAAU,EACVC,SAAS,EACTC,MAAM,EACNC,IAAI,EACJC,WAAW,EACXC,UAAU,EACVC,MAAM,EACNC,QAAQ,EACRC,OAAO,EACPC,KAAK,EACLC,gBAAgB,EAChBC,MAAM,EACNC,WAAW,EACXC,aAAa,EACbC,aAAa,EACbC,KAAK,EACLC,SAAS,EACTC,SAAS,EACTC,cAAc,EACdC,SAAS,EACTC,QAAQ,EACRC,KAAK,EACLC,UAAU,EACVC,gBAAgB,EAChBC,UAAU,EACVC,cAAc,QACT,eAAe;AACtB,SACEC,MAAM,IAAIC,UAAU,EACpBC,IAAI,IAAIC,QAAQ,EAChBC,MAAM,IAAIC,UAAU,EACpBC,OAAO,IAAIC,WAAW,EACtBC,IAAI,IAAIC,QAAQ,QACX,qBAAqB;AAC5B,SAASC,WAAW,QAAQ,kBAAkB;AAC9C,OAAOC,WAAW,MAAM,4BAA4B;AACpD,OAAOC,gBAAgB,MAAM,iCAAiC;AAC9D,OAAOC,eAAe,MAAM,mBAAmB;AAC/C,SACEC,YAAY,EACZC,WAAW,EACXC,mBAAmB,EACnBC,kBAAkB,EAClBC,cAAc,EACdC,YAAY,EACZC,gBAAgB,EAChBC,kBAAkB,EAClBC,iBAAiB,QACZ,wBAAwB;;AAE/B;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAPA,SAAAC,MAAA,IAAAC,OAAA;AAQA,MAAMC,kBAAkB,GAAGA,CAAC;EAAEC,UAAU;EAAEC,SAAS;EAAEC;AAAQ,CAAC,KAAK;EAAAC,EAAA;EACjE,MAAMC,QAAQ,GAAGpB,WAAW,CAAC,CAAC;;EAE9B;EACA,MAAM,CAACqB,OAAO,EAAEC,UAAU,CAAC,GAAG9D,QAAQ,CAAC,KAAK,CAAC;EAC7C,MAAM,CAAC+D,WAAW,EAAEC,cAAc,CAAC,GAAGhE,QAAQ,CAAC,KAAK,CAAC;EACrD,MAAM,CAACiE,aAAa,EAAEC,gBAAgB,CAAC,GAAGlE,QAAQ,CAAC,KAAK,CAAC;EACzD,MAAM,CAACmE,aAAa,EAAEC,gBAAgB,CAAC,GAAGpE,QAAQ,CAAC,EAAE,CAAC;EACtD,MAAM,CAACqE,iBAAiB,EAAEC,oBAAoB,CAAC,GAAGtE,QAAQ,CAAC,KAAK,CAAC;;EAEjE;EACA,MAAM,CAACuE,IAAI,EAAEC,OAAO,CAAC,GAAGxE,QAAQ,CAAC,EAAE,CAAC;EACpC,MAAM,CAACyE,MAAM,EAAEC,SAAS,CAAC,GAAG1E,QAAQ,CAAC,EAAE,CAAC;EACxC,MAAM,CAAC2E,gBAAgB,EAAEC,mBAAmB,CAAC,GAAG5E,QAAQ,CAAC,EAAE,CAAC;EAC5D,MAAM,CAAC6E,YAAY,EAAEC,eAAe,CAAC,GAAG9E,QAAQ,CAAC,IAAI,CAAC;EACtD,MAAM,CAAC+E,WAAW,EAAEC,cAAc,CAAC,GAAGhF,QAAQ,CAAC,EAAE,CAAC;EAClD,MAAM,CAACiF,cAAc,EAAEC,iBAAiB,CAAC,GAAGlF,QAAQ,CAAC,EAAE,CAAC;EACxD,MAAM,CAACmF,gBAAgB,EAAEC,mBAAmB,CAAC,GAAGpF,QAAQ,CAAC,EAAE,CAAC;;EAE5D;EACA,MAAM,CAACqF,iBAAiB,EAAEC,oBAAoB,CAAC,GAAGtF,QAAQ,CAAC,KAAK,CAAC;EACjE,MAAM,CAACuF,oBAAoB,EAAEC,uBAAuB,CAAC,GAAGxF,QAAQ,CAAC,EAAE,CAAC;EACpE,MAAM,CAACyF,mBAAmB,EAAEC,sBAAsB,CAAC,GAAG1F,QAAQ,CAAC,IAAI,CAAC;EACpE,MAAM,CAAC2F,qBAAqB,EAAEC,wBAAwB,CAAC,GAAG5F,QAAQ,CAAC,KAAK,CAAC;;EAEzE;EACAC,SAAS,CAAC,MAAM;IACd4F,QAAQ,CAAC,CAAC;EACZ,CAAC,EAAE,CAACrC,UAAU,CAAC,CAAC;;EAEhB;EACAvD,SAAS,CAAC,MAAM;IACd,IAAI4E,YAAY,EAAE;MAChBiB,UAAU,CAAC,CAAC;IACd;EACF,CAAC,EAAE,CAACjB,YAAY,CAAC,CAAC;;EAElB;EACA,MAAMgB,QAAQ,GAAG,MAAAA,CAAA,KAAY;IAC3B,IAAI;MACF7B,cAAc,CAAC,IAAI,CAAC;MACpB+B,OAAO,CAACC,GAAG,CAAC,oCAAoCxC,UAAU,KAAK,CAAC;;MAEhE;MACA,MAAMyC,QAAQ,GAAG,MAAMxD,WAAW,CAACyD,OAAO,CAAC1C,UAAU,EAAE,IAAI,EAAE,YAAY,CAAC;MAC1EuC,OAAO,CAACC,GAAG,CAAC,YAAYC,QAAQ,CAACE,MAAM,kBAAkB,CAAC;;MAE1D;MACA,MAAMC,cAAc,GAAGH,QAAQ,CAACI,MAAM,CAACC,IAAI,IACzCC,UAAU,CAACD,IAAI,CAACE,eAAe,CAAC,GAAG,CAAC,IAAIF,IAAI,CAACG,mBAAmB,KAAK,YACvE,CAAC;MAEDjC,OAAO,CAAC4B,cAAc,CAAC;MACvBL,OAAO,CAACC,GAAG,CAAC,YAAYI,cAAc,CAACD,MAAM,iCAAiC,CAAC;IACjF,CAAC,CAAC,OAAOO,KAAK,EAAE;MACdX,OAAO,CAACW,KAAK,CAAC,yCAAyC,EAAEA,KAAK,CAAC;MAC/DhD,OAAO,CAAC,0CAA0C,IAAIgD,KAAK,CAACC,MAAM,IAAID,KAAK,CAACE,OAAO,IAAI,oBAAoB,CAAC,CAAC;IAC/G,CAAC,SAAS;MACR5C,cAAc,CAAC,KAAK,CAAC;IACvB;EACF,CAAC;;EAED;EACA,MAAM8B,UAAU,GAAG,MAAAA,CAAA,KAAY;IAC7B,IAAI,CAACjB,YAAY,EAAE;IAEnB,IAAI;MACFX,gBAAgB,CAAC,IAAI,CAAC;MACtB6B,OAAO,CAACC,GAAG,CAAC,sCAAsCxC,UAAU,KAAK,CAAC;;MAElE;MACA,MAAMqD,UAAU,GAAG,MAAMnE,gBAAgB,CAACoE,SAAS,CAACtD,UAAU,CAAC;MAC/DuC,OAAO,CAACC,GAAG,CAAC,YAAYa,UAAU,CAACV,MAAM,SAAS,CAAC;;MAEnD;MACA,MAAMY,oBAAoB,GAAGF,UAAU,CAACR,MAAM,CAACW,MAAM,IACnDA,MAAM,CAACC,SAAS,KAAKpC,YAAY,CAACoC,SAAS,IAC3CD,MAAM,CAACE,OAAO,KAAKrC,YAAY,CAACqC,OAAO,KACtCF,MAAM,CAACG,YAAY,KAAK,aAAa,IAAIH,MAAM,CAACG,YAAY,KAAK,QAAQ,CAC5E,CAAC;MAEDzC,SAAS,CAACmC,UAAU,CAAC;MACrBjC,mBAAmB,CAACmC,oBAAoB,CAAC;MACzChB,OAAO,CAACC,GAAG,CAAC,YAAYe,oBAAoB,CAACZ,MAAM,qBAAqB,CAAC;IAC3E,CAAC,CAAC,OAAOO,KAAK,EAAE;MACdX,OAAO,CAACW,KAAK,CAAC,6CAA6C,EAAEA,KAAK,CAAC;MACnEhD,OAAO,CAAC,8CAA8C,IAAIgD,KAAK,CAACC,MAAM,IAAID,KAAK,CAACE,OAAO,IAAI,oBAAoB,CAAC,CAAC;IACnH,CAAC,SAAS;MACR1C,gBAAgB,CAAC,KAAK,CAAC;IACzB;EACF,CAAC;;EAED;EACA,MAAMkD,oBAAoB,GAAG,MAAAA,CAAA,KAAY;IACvC,IAAI,CAACrC,WAAW,CAACsC,IAAI,CAAC,CAAC,EAAE;MACvB3D,OAAO,CAAC,6BAA6B,CAAC;MACtC;IACF;IAEA,IAAI;MACFI,UAAU,CAAC,IAAI,CAAC;MAChB,MAAMwC,IAAI,GAAG,MAAM7D,WAAW,CAAC6E,WAAW,CAAC9D,UAAU,EAAEuB,WAAW,CAACsC,IAAI,CAAC,CAAC,CAAC;;MAE1E;MACA,IAAId,UAAU,CAACD,IAAI,CAACE,eAAe,CAAC,IAAI,CAAC,IAAIF,IAAI,CAACG,mBAAmB,KAAK,YAAY,EAAE;QACtF/C,OAAO,CAAC,4CAA4C,CAAC;QACrDI,UAAU,CAAC,KAAK,CAAC;QACjB;MACF;MAEAgB,eAAe,CAACwB,IAAI,CAAC;MACrBpB,iBAAiB,CAAC,EAAE,CAAC,CAAC,CAAC;MACvBE,mBAAmB,CAAC,EAAE,CAAC,CAAC,CAAC;MACzBd,oBAAoB,CAAC,KAAK,CAAC;IAC7B,CAAC,CAAC,OAAOoC,KAAK,EAAE;MACdX,OAAO,CAACW,KAAK,CAAC,qCAAqC,EAAEA,KAAK,CAAC;MAC3DhD,OAAO,CAAC,sCAAsC,IAAIgD,KAAK,CAACC,MAAM,IAAID,KAAK,CAACE,OAAO,IAAI,oBAAoB,CAAC,CAAC;IAC3G,CAAC,SAAS;MACR9C,UAAU,CAAC,KAAK,CAAC;IACnB;EACF,CAAC;;EAED;EACA,MAAMyD,gBAAgB,GAAIjB,IAAI,IAAK;IACjCxB,eAAe,CAACwB,IAAI,CAAC;IACrBpB,iBAAiB,CAAC,EAAE,CAAC,CAAC,CAAC;IACvBE,mBAAmB,CAAC,EAAE,CAAC,CAAC,CAAC;IACzBd,oBAAoB,CAAC,KAAK,CAAC;EAC7B,CAAC;;EAED;EACA,MAAMkD,kBAAkB,GAAIC,KAAK,IAAK;IACpCvC,iBAAiB,CAACuC,KAAK,CAACC,MAAM,CAACC,KAAK,CAAC;IACrCvC,mBAAmB,CAAC,EAAE,CAAC,CAAC,CAAC;EAC3B,CAAC;;EAED;EACA,MAAMwC,kBAAkB,GAAIC,QAAQ,IAAK;IACvCzC,mBAAmB,CAACyC,QAAQ,CAAC;EAC/B,CAAC;;EAED;EACA,MAAMC,UAAU,GAAGA,CAAA,KAAM;IACvB,IAAI,CAACjD,YAAY,EAAE;MACjBnB,OAAO,CAAC,sCAAsC,CAAC;MAC/C;IACF;IAEA,IAAI,CAACuB,cAAc,EAAE;MACnBvB,OAAO,CAAC,0CAA0C,CAAC;MACnD;IACF;;IAEA;IACA,IAAIuB,cAAc,KAAK,cAAc,IAAI,CAACE,gBAAgB,EAAE;MAC1DzB,OAAO,CAAC,yCAAyC,CAAC;MAClD;IACF;;IAEA;IACA,IAAIkD,OAAO,GAAG,EAAE;IAChB,IAAImB,MAAM,GAAG,IAAI;IAEjB,IAAI9C,cAAc,KAAK,cAAc,EAAE;MACrC,MAAM+B,MAAM,GAAGvC,MAAM,CAACuD,IAAI,CAACC,CAAC,IAAIA,CAAC,CAACC,SAAS,KAAK/C,gBAAgB,CAAC;MACjEyB,OAAO,GAAG,2CAA2CuB,eAAe,CAAChD,gBAAgB,CAAC,YAAYN,YAAY,CAACuD,OAAO,GAAG;MACzHL,MAAM,GAAGA,CAAA,KAAMM,YAAY,CAAClD,gBAAgB,CAAC;IAC/C,CAAC,MAAM,IAAIF,cAAc,KAAK,eAAe,EAAE;MAC7C2B,OAAO,GAAG,0DAA0D/B,YAAY,CAACuD,OAAO,GAAG;MAC3FL,MAAM,GAAGA,CAAA,KAAMM,YAAY,CAAC,cAAc,CAAC;IAC7C,CAAC,MAAM,IAAIpD,cAAc,KAAK,sBAAsB,EAAE;MACpD2B,OAAO,GAAG,oEAAoE/B,YAAY,CAACuD,OAAO,iGAAiG;MACnML,MAAM,GAAGA,CAAA,KAAMO,oBAAoB,CAAC,CAAC;IACvC;IAEA9C,uBAAuB,CAACoB,OAAO,CAAC;IAChClB,sBAAsB,CAAC,MAAMqC,MAAM,CAAC;IACpCzC,oBAAoB,CAAC,IAAI,CAAC;EAC5B,CAAC;;EAED;EACA,MAAM+C,YAAY,GAAG,MAAOR,QAAQ,IAAK;IACvC,IAAI;MACF/D,UAAU,CAAC,IAAI,CAAC;MAChB,MAAMrB,WAAW,CAAC4F,YAAY,CAAC7E,UAAU,EAAEqB,YAAY,CAACuD,OAAO,EAAEP,QAAQ,CAAC;MAE1EpE,SAAS,CAAC,UAAUoE,QAAQ,KAAK,cAAc,GAAG,iBAAiB,GAAG,WAAW,eAAe,CAAC;;MAEjG;MACA/C,eAAe,CAAC,IAAI,CAAC;MACrBI,iBAAiB,CAAC,EAAE,CAAC;MACrBE,mBAAmB,CAAC,EAAE,CAAC;MACvBJ,cAAc,CAAC,EAAE,CAAC;;MAElB;MACAa,QAAQ,CAAC,CAAC;IACZ,CAAC,CAAC,OAAOa,KAAK,EAAE;MACdX,OAAO,CAACW,KAAK,CAAC,+CAA+C,EAAEA,KAAK,CAAC;MACrEhD,OAAO,CAAC,gDAAgD,IAAIgD,KAAK,CAACC,MAAM,IAAID,KAAK,CAACE,OAAO,IAAI,oBAAoB,CAAC,CAAC;IACrH,CAAC,SAAS;MACR9C,UAAU,CAAC,KAAK,CAAC;IACnB;EACF,CAAC;;EAED;EACA,MAAMwE,oBAAoB,GAAG,MAAAA,CAAA,KAAY;IACvC,IAAI;MACFxE,UAAU,CAAC,IAAI,CAAC;;MAEhB;MACA,MAAMrB,WAAW,CAAC8F,kBAAkB,CAAC/E,UAAU,EAAEqB,YAAY,CAACuD,OAAO,CAAC;MAEtE3E,SAAS,CAAC,0BAA0BoB,YAAY,CAACuD,OAAO,yBAAyB,CAAC;;MAElF;MACAtD,eAAe,CAAC,IAAI,CAAC;MACrBI,iBAAiB,CAAC,EAAE,CAAC;MACrBE,mBAAmB,CAAC,EAAE,CAAC;MACvBJ,cAAc,CAAC,EAAE,CAAC;;MAElB;MACAa,QAAQ,CAAC,CAAC;IACZ,CAAC,CAAC,OAAOa,KAAK,EAAE;MACdX,OAAO,CAACW,KAAK,CAAC,qDAAqD,EAAEA,KAAK,CAAC;MAC3EhD,OAAO,CAAC,sDAAsD,IAAIgD,KAAK,CAACC,MAAM,IAAID,KAAK,CAACE,OAAO,IAAI,oBAAoB,CAAC,CAAC;IAC3H,CAAC,SAAS;MACR9C,UAAU,CAAC,KAAK,CAAC;IACnB;EACF,CAAC;;EAED;EACA,MAAMqE,eAAe,GAAIK,QAAQ,IAAK;IACpC,IAAIA,QAAQ,KAAK,cAAc,EAAE,OAAO,cAAc;;IAEtD;IACA,IAAIA,QAAQ,IAAIA,QAAQ,CAACC,QAAQ,CAAC,IAAI,CAAC,EAAE;MACvC,OAAOD,QAAQ,CAACE,KAAK,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;IAChC;IACA,OAAOF,QAAQ;EACjB,CAAC;;EAED;EACA,MAAMG,uBAAuB,GAAGA,CAAA,kBAC9BrF,OAAA,CAACnD,KAAK;IAACyI,EAAE,EAAE;MAAEC,CAAC,EAAE,CAAC;MAAEC,EAAE,EAAE;IAAE,CAAE;IAAAC,QAAA,gBACzBzF,OAAA,CAAClD,UAAU;MAAC4I,OAAO,EAAC,IAAI;MAACC,YAAY;MAAAF,QAAA,EAAC;IAEtC;MAAAG,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAY,CAAC,eAEb/F,OAAA,CAAC/C,IAAI;MAAC+I,SAAS;MAACC,OAAO,EAAE,CAAE;MAACC,UAAU,EAAC,QAAQ;MAAAT,QAAA,gBAC7CzF,OAAA,CAAC/C,IAAI;QAACkJ,IAAI;QAACC,EAAE,EAAE,EAAG;QAACC,EAAE,EAAE,CAAE;QAAAZ,QAAA,eACvBzF,OAAA,CAACjD,SAAS;UACRuJ,SAAS;UACTC,KAAK,EAAC,SAAS;UACflC,KAAK,EAAE5C,WAAY;UACnB+E,QAAQ,EAAGC,CAAC,IAAK/E,cAAc,CAAC+E,CAAC,CAACrC,MAAM,CAACC,KAAK,CAAE;UAChDqC,UAAU,EAAE;YACVC,YAAY,eACV3G,OAAA,CAACzB,cAAc;cAACqI,QAAQ,EAAC,KAAK;cAAAnB,QAAA,eAC5BzF,OAAA,CAAC1B,UAAU;gBACTuI,OAAO,EAAE/C,oBAAqB;gBAC9BgD,QAAQ,EAAEvG,OAAO,IAAI,CAACkB,WAAW,CAACsC,IAAI,CAAC,CAAE;gBAAA0B,QAAA,eAEzCzF,OAAA,CAACvB,UAAU;kBAAAmH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACJ;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACC;UAEpB;QAAE;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACE,CAAC,eACP/F,OAAA,CAAC/C,IAAI;QAACkJ,IAAI;QAACC,EAAE,EAAE,EAAG;QAACC,EAAE,EAAE,CAAE;QAAAZ,QAAA,eACvBzF,OAAA,CAAChD,MAAM;UACL0I,OAAO,EAAC,UAAU;UAClBmB,OAAO,EAAEA,CAAA,KAAM7F,oBAAoB,CAAC,CAACD,iBAAiB,CAAE;UACxD+F,QAAQ,EAAErG,WAAW,IAAIQ,IAAI,CAAC4B,MAAM,KAAK,CAAE;UAC3CyD,SAAS;UAAAb,QAAA,EAER1E,iBAAiB,GAAG,qBAAqB,GAAG;QAAmB;UAAA6E,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC1D;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACL,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,EAENtF,WAAW,iBACVT,OAAA,CAACpD,GAAG;MAAC0I,EAAE,EAAE;QAAEyB,OAAO,EAAE,MAAM;QAAEC,cAAc,EAAE,QAAQ;QAAEC,EAAE,EAAE;MAAE,CAAE;MAAAxB,QAAA,eAC5DzF,OAAA,CAACxC,gBAAgB;QAAAoI,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACjB,CACN,EAEAhF,iBAAiB,IAAIE,IAAI,CAAC4B,MAAM,GAAG,CAAC,iBACnC7C,OAAA,CAAChC,cAAc;MAACkJ,SAAS,EAAErK,KAAM;MAACyI,EAAE,EAAE;QAAE2B,EAAE,EAAE,CAAC;QAAEE,SAAS,EAAE;MAAI,CAAE;MAAA1B,QAAA,eAC9DzF,OAAA,CAACnC,KAAK;QAACuJ,YAAY;QAACC,IAAI,EAAC,OAAO;QAAA5B,QAAA,gBAC9BzF,OAAA,CAAC/B,SAAS;UAAAwH,QAAA,eACRzF,OAAA,CAAC9B,QAAQ;YAAAuH,QAAA,gBACPzF,OAAA,CAACjC,SAAS;cAAA0H,QAAA,EAAC;YAAO;cAAAG,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAW,CAAC,eAC9B/F,OAAA,CAACjC,SAAS;cAAA0H,QAAA,EAAC;YAAS;cAAAG,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAW,CAAC,eAChC/F,OAAA,CAACjC,SAAS;cAAA0H,QAAA,EAAC;YAAU;cAAAG,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAW,CAAC,eACjC/F,OAAA,CAACjC,SAAS;cAAA0H,QAAA,EAAC;YAAY;cAAAG,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAW,CAAC,eACnC/F,OAAA,CAACjC,SAAS;cAAA0H,QAAA,EAAC;YAAM;cAAAG,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAW,CAAC,eAC7B/F,OAAA,CAACjC,SAAS;cAAA0H,QAAA,EAAC;YAAM;cAAAG,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAW,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACrB;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACF,CAAC,eACZ/F,OAAA,CAAClC,SAAS;UAAA2H,QAAA,EACPxE,IAAI,CAACqG,GAAG,CAAEtE,IAAI,iBACbhD,OAAA,CAAC9B,QAAQ;YAAoBqJ,KAAK;YAAA9B,QAAA,gBAChCzF,OAAA,CAACjC,SAAS;cAAA0H,QAAA,EAAEzC,IAAI,CAAC8B;YAAO;cAAAc,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAY,CAAC,eACrC/F,OAAA,CAACjC,SAAS;cAAA0H,QAAA,EAAEzC,IAAI,CAACW;YAAS;cAAAiC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAY,CAAC,eACvC/F,OAAA,CAACjC,SAAS;cAAA0H,QAAA,EAAEzC,IAAI,CAACY;YAAO;cAAAgC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAY,CAAC,eACrC/F,OAAA,CAACjC,SAAS;cAAA0H,QAAA,EAAEzC,IAAI,CAACE;YAAe;cAAA0C,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAY,CAAC,eAC7C/F,OAAA,CAACjC,SAAS;cAAA0H,QAAA,EAAEZ,eAAe,CAAC7B,IAAI,CAAC4B,SAAS,IAAI,EAAE;YAAC;cAAAgB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAY,CAAC,eAC9D/F,OAAA,CAACjC,SAAS;cAAA0H,QAAA,eACRzF,OAAA,CAAChD,MAAM;gBACLqK,IAAI,EAAC,OAAO;gBACZR,OAAO,EAAEA,CAAA,KAAM5C,gBAAgB,CAACjB,IAAI,CAAE;gBACtC0C,OAAO,EAAC,WAAW;gBACnB8B,KAAK,EAAC,SAAS;gBAAA/B,QAAA,EAChB;cAED;gBAAAG,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACA,CAAC;UAAA,GAfC/C,IAAI,CAAC8B,OAAO;YAAAc,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAgBjB,CACX;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACO,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACP;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACM,CACjB;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACI,CACR;;EAED;EACA,MAAM0B,yBAAyB,GAAGA,CAAA,KAAM;IACtC,IAAI,CAAClG,YAAY,EAAE,OAAO,IAAI;IAE9B,oBACEvB,OAAA,CAACnD,KAAK;MAACyI,EAAE,EAAE;QAAEC,CAAC,EAAE,CAAC;QAAEC,EAAE,EAAE;MAAE,CAAE;MAAAC,QAAA,gBACzBzF,OAAA,CAACpD,GAAG;QAAC0I,EAAE,EAAE;UAAEyB,OAAO,EAAE,MAAM;UAAEC,cAAc,EAAE,eAAe;UAAEd,UAAU,EAAE,QAAQ;UAAEV,EAAE,EAAE;QAAE,CAAE;QAAAC,QAAA,gBACzFzF,OAAA,CAAClD,UAAU;UAAC4I,OAAO,EAAC,IAAI;UAAAD,QAAA,EAAC;QAEzB;UAAAG,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAY,CAAC,eACb/F,OAAA,CAAChD,MAAM;UACL0I,OAAO,EAAC,UAAU;UAClB2B,IAAI,EAAC,OAAO;UACZR,OAAO,EAAEA,CAAA,KAAMvE,wBAAwB,CAAC,IAAI,CAAE;UAAAmD,QAAA,EAC/C;QAED;UAAAG,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACN,CAAC,eAEN/F,OAAA,CAAC/C,IAAI;QAAC+I,SAAS;QAACC,OAAO,EAAE,CAAE;QAAAR,QAAA,gBACzBzF,OAAA,CAAC/C,IAAI;UAACkJ,IAAI;UAACC,EAAE,EAAE,EAAG;UAACC,EAAE,EAAE,CAAE;UAACqB,EAAE,EAAE,CAAE;UAAAjC,QAAA,gBAC9BzF,OAAA,CAAClD,UAAU;YAAC4I,OAAO,EAAC,WAAW;YAAAD,QAAA,EAAC;UAAQ;YAAAG,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAY,CAAC,eACrD/F,OAAA,CAAClD,UAAU;YAAC4I,OAAO,EAAC,OAAO;YAAAD,QAAA,EAAElE,YAAY,CAACuD;UAAO;YAAAc,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAa,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC3D,CAAC,eACP/F,OAAA,CAAC/C,IAAI;UAACkJ,IAAI;UAACC,EAAE,EAAE,EAAG;UAACC,EAAE,EAAE,CAAE;UAACqB,EAAE,EAAE,CAAE;UAAAjC,QAAA,gBAC9BzF,OAAA,CAAClD,UAAU;YAAC4I,OAAO,EAAC,WAAW;YAAAD,QAAA,EAAC;UAAU;YAAAG,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAY,CAAC,eACvD/F,OAAA,CAAClD,UAAU;YAAC4I,OAAO,EAAC,OAAO;YAAAD,QAAA,EAAElE,YAAY,CAACoC;UAAS;YAAAiC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAa,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC7D,CAAC,eACP/F,OAAA,CAAC/C,IAAI;UAACkJ,IAAI;UAACC,EAAE,EAAE,EAAG;UAACC,EAAE,EAAE,CAAE;UAACqB,EAAE,EAAE,CAAE;UAAAjC,QAAA,gBAC9BzF,OAAA,CAAClD,UAAU;YAAC4I,OAAO,EAAC,WAAW;YAAAD,QAAA,EAAC;UAAW;YAAAG,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAY,CAAC,eACxD/F,OAAA,CAAClD,UAAU;YAAC4I,OAAO,EAAC,OAAO;YAAAD,QAAA,EAAElE,YAAY,CAACqC;UAAO;YAAAgC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAa,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC3D,CAAC,eACP/F,OAAA,CAAC/C,IAAI;UAACkJ,IAAI;UAACC,EAAE,EAAE,EAAG;UAACC,EAAE,EAAE,CAAE;UAACqB,EAAE,EAAE,CAAE;UAAAjC,QAAA,gBAC9BzF,OAAA,CAAClD,UAAU;YAAC4I,OAAO,EAAC,WAAW;YAAAD,QAAA,EAAC;UAAa;YAAAG,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAY,CAAC,eAC1D/F,OAAA,CAAClD,UAAU;YAAC4I,OAAO,EAAC,OAAO;YAAAD,QAAA,EAAElE,YAAY,CAAC2B;UAAe;YAAA0C,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAa,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACnE,CAAC,eACP/F,OAAA,CAAC/C,IAAI;UAACkJ,IAAI;UAACC,EAAE,EAAE,EAAG;UAACC,EAAE,EAAE,CAAE;UAACqB,EAAE,EAAE,CAAE;UAAAjC,QAAA,gBAC9BzF,OAAA,CAAClD,UAAU;YAAC4I,OAAO,EAAC,WAAW;YAAAD,QAAA,EAAC;UAAe;YAAAG,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAY,CAAC,eAC5D/F,OAAA,CAAClD,UAAU;YAAC4I,OAAO,EAAC,OAAO;YAAAD,QAAA,EACxBlE,YAAY,CAACqD,SAAS,GAAGC,eAAe,CAACtD,YAAY,CAACqD,SAAS,CAAC,GAAG;UAAgB;YAAAgB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC1E,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACT,CAAC,eACP/F,OAAA,CAAC/C,IAAI;UAACkJ,IAAI;UAACC,EAAE,EAAE,EAAG;UAACC,EAAE,EAAE,CAAE;UAACqB,EAAE,EAAE,CAAE;UAAAjC,QAAA,gBAC9BzF,OAAA,CAAClD,UAAU;YAAC4I,OAAO,EAAC,WAAW;YAAAD,QAAA,EAAC;UAAM;YAAAG,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAY,CAAC,eACnD/F,OAAA,CAAClD,UAAU;YAAC4I,OAAO,EAAC,OAAO;YAAAD,QAAA,EAAElE,YAAY,CAAC4B;UAAmB;YAAAyC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAa,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACvE,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACF,CAAC;EAEZ,CAAC;;EAED;EACA,MAAM4B,qBAAqB,GAAGA,CAAA,KAAM;IAClC,IAAI,CAACpG,YAAY,EAAE,OAAO,IAAI;IAE9B,oBACEvB,OAAA,CAACnD,KAAK;MAACyI,EAAE,EAAE;QAAEC,CAAC,EAAE,CAAC;QAAEC,EAAE,EAAE;MAAE,CAAE;MAAAC,QAAA,gBACzBzF,OAAA,CAAClD,UAAU;QAAC4I,OAAO,EAAC,IAAI;QAACC,YAAY;QAAAF,QAAA,EAAC;MAEtC;QAAAG,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAY,CAAC,eAEb/F,OAAA,CAAC5B,UAAU;QACTiG,KAAK,EAAE1C,cAAe;QACtB6E,QAAQ,EAAEtC,kBAAmB;QAAAuB,QAAA,gBAE7BzF,OAAA,CAAC3B,gBAAgB;UACfgG,KAAK,EAAC,cAAc;UACpBuD,OAAO,eAAE5H,OAAA,CAAC7B,KAAK;YAAAyH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAE;UACnBQ,KAAK,EAAC;QAAsB;UAAAX,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC7B,CAAC,eACF/F,OAAA,CAAC3B,gBAAgB;UACfgG,KAAK,EAAC,eAAe;UACrBuD,OAAO,eAAE5H,OAAA,CAAC7B,KAAK;YAAAyH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAE;UACnBQ,KAAK,EAAC;QAAsB;UAAAX,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC7B,CAAC,eACF/F,OAAA,CAAC3B,gBAAgB;UACfgG,KAAK,EAAC,sBAAsB;UAC5BuD,OAAO,eAAE5H,OAAA,CAAC7B,KAAK;YAAAyH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAE;UACnBQ,KAAK,EAAC;QAAuB;UAAAX,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC9B,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACQ,CAAC,EAEZpE,cAAc,KAAK,cAAc,IAAIkG,qBAAqB,CAAC,CAAC,EAE5DlG,cAAc,KAAK,eAAe,iBACjC3B,OAAA,CAACzC,KAAK;QAACuK,QAAQ,EAAC,MAAM;QAACxC,EAAE,EAAE;UAAE2B,EAAE,EAAE;QAAE,CAAE;QAAAxB,QAAA,EAAC;MAGtC;QAAAG,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAO,CACR,EAEApE,cAAc,KAAK,sBAAsB,iBACxC3B,OAAA,CAACzC,KAAK;QAACuK,QAAQ,EAAC,SAAS;QAACxC,EAAE,EAAE;UAAE2B,EAAE,EAAE;QAAE,CAAE;QAAAxB,QAAA,gBACtCzF,OAAA,CAAClD,UAAU;UAAC4I,OAAO,EAAC,OAAO;UAACqC,UAAU,EAAC,MAAM;UAAAtC,QAAA,EAAC;QAE9C;UAAAG,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAY,CAAC,eACb/F,OAAA,CAAClD,UAAU;UAAC4I,OAAO,EAAC,OAAO;UAAAD,QAAA,GAAC,mDACkB,eAAAzF,OAAA;YAAA4F,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAK,CAAC,yCAChB,eAAA/F,OAAA;YAAA4F,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAK,CAAC,kDACG,eAAA/F,OAAA;YAAA4F,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAK,CAAC,2EAEnD;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAY,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACR,CACR,eAED/F,OAAA,CAACpD,GAAG;QAAC0I,EAAE,EAAE;UAAE2B,EAAE,EAAE,CAAC;UAAEF,OAAO,EAAE,MAAM;UAAEC,cAAc,EAAE;QAAW,CAAE;QAAAvB,QAAA,eAC9DzF,OAAA,CAAChD,MAAM;UACL0I,OAAO,EAAC,WAAW;UACnB8B,KAAK,EAAC,SAAS;UACfQ,SAAS,eAAEhI,OAAA,CAACrB,QAAQ;YAAAiH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAE;UACxBc,OAAO,EAAErC,UAAW;UACpBsC,QAAQ,EAAEvG,OAAO,IAAI,CAACoB,cAAc,IAAKA,cAAc,KAAK,cAAc,IAAI,CAACE,gBAAkB;UAAA4D,QAAA,EAClG;QAED;UAAAG,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACN,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACD,CAAC;EAEZ,CAAC;;EAED;EACA,MAAM8B,qBAAqB,GAAGA,CAAA,KAAM;IAClC,IAAIlH,aAAa,EAAE;MACjB,oBACEX,OAAA,CAACpD,GAAG;QAAC0I,EAAE,EAAE;UAAEyB,OAAO,EAAE,MAAM;UAAEC,cAAc,EAAE,QAAQ;UAAEC,EAAE,EAAE;QAAE,CAAE;QAAAxB,QAAA,eAC5DzF,OAAA,CAACxC,gBAAgB;UAAAoI,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACjB,CAAC;IAEV;IAEA,IAAI1E,gBAAgB,CAACwB,MAAM,KAAK,CAAC,EAAE;MACjC,oBACE7C,OAAA,CAACzC,KAAK;QAACuK,QAAQ,EAAC,SAAS;QAACxC,EAAE,EAAE;UAAE2B,EAAE,EAAE;QAAE,CAAE;QAAAxB,QAAA,EAAC;MAEzC;QAAAG,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAO,CAAC;IAEZ;IAEA,oBACE/F,OAAA,CAACpD,GAAG;MAAC0I,EAAE,EAAE;QAAE2B,EAAE,EAAE;MAAE,CAAE;MAAAxB,QAAA,gBACjBzF,OAAA,CAAClD,UAAU;QAAC4I,OAAO,EAAC,WAAW;QAACC,YAAY;QAAAF,QAAA,EAAC;MAE7C;QAAAG,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAY,CAAC,eAEb/F,OAAA,CAAChC,cAAc;QAACkJ,SAAS,EAAErK,KAAM;QAACyI,EAAE,EAAE;UAAE6B,SAAS,EAAE;QAAI,CAAE;QAAA1B,QAAA,eACvDzF,OAAA,CAACnC,KAAK;UAACuJ,YAAY;UAACC,IAAI,EAAC,OAAO;UAAA5B,QAAA,gBAC9BzF,OAAA,CAAC/B,SAAS;YAAAwH,QAAA,eACRzF,OAAA,CAAC9B,QAAQ;cAAAuH,QAAA,gBACPzF,OAAA,CAACjC,SAAS;gBAACkK,OAAO,EAAC;cAAU;gBAAArC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAY,CAAC,eAC1C/F,OAAA,CAACjC,SAAS;gBAAA0H,QAAA,EAAC;cAAS;gBAAAG,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAW,CAAC,eAChC/F,OAAA,CAACjC,SAAS;gBAAA0H,QAAA,EAAC;cAAS;gBAAAG,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAW,CAAC,eAChC/F,OAAA,CAACjC,SAAS;gBAAA0H,QAAA,EAAC;cAAU;gBAAAG,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAW,CAAC,eACjC/F,OAAA,CAACjC,SAAS;gBAAA0H,QAAA,EAAC;cAAa;gBAAAG,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAW,CAAC,eACpC/F,OAAA,CAACjC,SAAS;gBAAA0H,QAAA,EAAC;cAAK;gBAAAG,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAW,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACpB;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACF,CAAC,eACZ/F,OAAA,CAAClC,SAAS;YAAA2H,QAAA,EACPpE,gBAAgB,CAACiG,GAAG,CAAE5D,MAAM,iBAC3B1D,OAAA,CAAC9B,QAAQ;cAEPqJ,KAAK;cACLW,QAAQ,EAAErG,gBAAgB,KAAK6B,MAAM,CAACkB,SAAU;cAChDiC,OAAO,EAAEA,CAAA,KAAMvC,kBAAkB,CAACZ,MAAM,CAACkB,SAAS,CAAE;cAAAa,QAAA,gBAEpDzF,OAAA,CAACjC,SAAS;gBAACkK,OAAO,EAAC,UAAU;gBAAAxC,QAAA,eAC3BzF,OAAA,CAAC7B,KAAK;kBACJgK,OAAO,EAAEtG,gBAAgB,KAAK6B,MAAM,CAACkB,SAAU;kBAC/C4B,QAAQ,EAAEA,CAAA,KAAMlC,kBAAkB,CAACZ,MAAM,CAACkB,SAAS;gBAAE;kBAAAgB,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACtD;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACO,CAAC,eACZ/F,OAAA,CAACjC,SAAS;gBAAA0H,QAAA,EAAEZ,eAAe,CAACnB,MAAM,CAACkB,SAAS;cAAC;gBAAAgB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAY,CAAC,eAC1D/F,OAAA,CAACjC,SAAS;gBAAA0H,QAAA,EAAE/B,MAAM,CAACC;cAAS;gBAAAiC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAY,CAAC,eACzC/F,OAAA,CAACjC,SAAS;gBAAA0H,QAAA,EAAE/B,MAAM,CAACE;cAAO;gBAAAgC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAY,CAAC,eACvC/F,OAAA,CAACjC,SAAS;gBAAA0H,QAAA,EAAE/B,MAAM,CAAC0E;cAAa;gBAAAxC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAY,CAAC,eAC7C/F,OAAA,CAACjC,SAAS;gBAAA0H,QAAA,EAAE/B,MAAM,CAACG;cAAY;gBAAA+B,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAY,CAAC;YAAA,GAfvCrC,MAAM,CAACkB,SAAS;cAAAgB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAgBb,CACX;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACO,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACP;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACM,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACd,CAAC;EAEV,CAAC;EAED,oBACE/F,OAAA,CAACpD,GAAG;IAAA6I,QAAA,GAEDJ,uBAAuB,CAAC,CAAC,EAGzBoC,yBAAyB,CAAC,CAAC,EAG3BE,qBAAqB,CAAC,CAAC,eAGxB3H,OAAA,CAACvC,MAAM;MACL4K,IAAI,EAAEhG,qBAAsB;MAC5BiG,OAAO,EAAEA,CAAA,KAAMhG,wBAAwB,CAAC,KAAK,CAAE;MAC/CiG,QAAQ,EAAC,IAAI;MACbjC,SAAS;MAAAb,QAAA,gBAETzF,OAAA,CAACtC,WAAW;QAAA+H,QAAA,EAAC;MAA0B;QAAAG,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAa,CAAC,eACrD/F,OAAA,CAACrC,aAAa;QAAA8H,QAAA,EACXlE,YAAY,iBAAIvB,OAAA,CAACX,eAAe;UAAC2D,IAAI,EAAEzB;QAAa;UAAAqE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC3C,CAAC,eAChB/F,OAAA,CAACpC,aAAa;QAAA6H,QAAA,eACZzF,OAAA,CAAChD,MAAM;UAAC6J,OAAO,EAAEA,CAAA,KAAMvE,wBAAwB,CAAC,KAAK,CAAE;UAAAmD,QAAA,EAAC;QAAM;UAAAG,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC1D,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACV,CAAC,eAGT/F,OAAA,CAACvC,MAAM;MACL4K,IAAI,EAAEtG,iBAAkB;MACxBuG,OAAO,EAAEA,CAAA,KAAMtG,oBAAoB,CAAC,KAAK,CAAE;MAAAyD,QAAA,gBAE3CzF,OAAA,CAACtC,WAAW;QAAA+H,QAAA,EAAC;MAAmB;QAAAG,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAa,CAAC,eAC9C/F,OAAA,CAACrC,aAAa;QAAA8H,QAAA,eACZzF,OAAA,CAAClD,UAAU;UAAA2I,QAAA,EAAExD;QAAoB;UAAA2D,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAa;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAClC,CAAC,eAChB/F,OAAA,CAACpC,aAAa;QAAA6H,QAAA,gBACZzF,OAAA,CAAChD,MAAM;UACL6J,OAAO,EAAEA,CAAA,KAAM7E,oBAAoB,CAAC,KAAK,CAAE;UAC3CwF,KAAK,EAAC,SAAS;UAAA/B,QAAA,EAChB;QAED;UAAAG,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC,eACT/F,OAAA,CAAChD,MAAM;UACL6J,OAAO,EAAEA,CAAA,KAAM;YACb7E,oBAAoB,CAAC,KAAK,CAAC;YAC3B,IAAIG,mBAAmB,EAAEA,mBAAmB,CAAC,CAAC;UAChD,CAAE;UACFqF,KAAK,EAAC,SAAS;UACf9B,OAAO,EAAC,WAAW;UACnB8C,SAAS;UAAA/C,QAAA,EACV;QAED;UAAAG,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACI,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACV,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACN,CAAC;AAEV,CAAC;AAAC1F,EAAA,CAzjBIJ,kBAAkB;EAAA,QACLf,WAAW;AAAA;AAAAuJ,EAAA,GADxBxI,kBAAkB;AA2jBxB,eAAeA,kBAAkB;AAAC,IAAAwI,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}