{"ast": null, "code": "var _jsxFileName = \"C:\\\\CMS\\\\webapp\\\\frontend\\\\src\\\\components\\\\cantieri\\\\PasswordManagementDialog.js\",\n  _s = $RefreshSig$();\nimport React, { useState } from 'react';\nimport { Dialog, DialogTitle, DialogContent, DialogActions, Button, TextField, Box, Typography, Alert, IconButton, InputAdornment, Tabs, Tab, Divider } from '@mui/material';\nimport { Visibility as VisibilityIcon, VisibilityOff as VisibilityOffIcon, Lock as LockIcon, Key as KeyIcon, ContentCopy as ContentCopyIcon } from '@mui/icons-material';\nimport cantieriService from '../../services/cantieriService';\nimport HoldToViewButton from './HoldToViewButton';\n\n/**\n * Dialog per la gestione delle password del cantiere\n * Permette di visualizzare e modificare la password\n */\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst PasswordManagementDialog = ({\n  open,\n  onClose,\n  cantiere,\n  onPasswordChanged = null\n}) => {\n  _s();\n  const [activeTab, setActiveTab] = useState(0);\n  const [loading, setLoading] = useState(false);\n  const [error, setError] = useState('');\n  const [success, setSuccess] = useState('');\n\n  // Stati per visualizzazione password\n  const [viewPasswordData, setViewPasswordData] = useState({\n    passwordAttuale: '',\n    passwordRivelata: '',\n    showPassword: false\n  });\n\n  // Stati per cambio password\n  const [changePasswordData, setChangePasswordData] = useState({\n    passwordAttuale: '',\n    passwordNuova: '',\n    confermaPassword: '',\n    showCurrentPassword: false,\n    showNewPassword: false,\n    showConfirmPassword: false\n  });\n\n  // Reset dei dati quando si apre/chiude il dialog\n  React.useEffect(() => {\n    if (open) {\n      setActiveTab(0);\n      setError('');\n      setSuccess('');\n      setViewPasswordData({\n        passwordAttuale: '',\n        passwordRivelata: '',\n        showPassword: false\n      });\n      setChangePasswordData({\n        passwordAttuale: '',\n        passwordNuova: '',\n        confermaPassword: '',\n        showCurrentPassword: false,\n        showNewPassword: false,\n        showConfirmPassword: false\n      });\n    }\n  }, [open]);\n\n  // Gestisce il cambio di tab\n  const handleTabChange = (event, newValue) => {\n    setActiveTab(newValue);\n    setError('');\n    setSuccess('');\n  };\n\n  // Gestisce la visualizzazione della password con verifica\n  const handleViewPassword = async () => {\n    if (!viewPasswordData.passwordAttuale.trim()) {\n      setError('Inserisci la password attuale');\n      return;\n    }\n    setLoading(true);\n    setError('');\n    try {\n      const response = await cantieriService.verifyCantierePassword(cantiere.id_cantiere, viewPasswordData.passwordAttuale);\n      if (response.password_corretta) {\n        setViewPasswordData(prev => ({\n          ...prev,\n          passwordRivelata: response.password_cantiere,\n          showPassword: true\n        }));\n        setSuccess('Password verificata correttamente');\n      } else {\n        setError('Password non corretta');\n        setViewPasswordData(prev => ({\n          ...prev,\n          passwordRivelata: '',\n          showPassword: false\n        }));\n      }\n    } catch (err) {\n      console.error('Errore nella verifica password:', err);\n      setError(err.detail || 'Errore nella verifica della password');\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  // Gestisce la visualizzazione diretta della password (senza verifica)\n  const handleViewPasswordDirect = async () => {\n    setLoading(true);\n    setError('');\n    try {\n      const response = await cantieriService.viewCantierePasswordDirect(cantiere.id_cantiere);\n      setViewPasswordData(prev => ({\n        ...prev,\n        passwordRivelata: response.password_cantiere,\n        showPassword: true,\n        passwordAttuale: '' // Pulisce il campo password attuale\n      }));\n      setSuccess('Password recuperata con successo');\n    } catch (err) {\n      console.error('Errore nel recupero password:', err);\n      if (err.detail && err.detail.includes('hashata')) {\n        setError('La password è hashata e non può essere recuperata. Utilizza la funzione di cambio password.');\n      } else {\n        setError(err.detail || 'Errore nel recupero della password');\n      }\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  // Gestisce il cambio password\n  const handleChangePassword = async () => {\n    // Validazioni\n    if (!changePasswordData.passwordAttuale.trim()) {\n      setError('Inserisci la password attuale');\n      return;\n    }\n    if (!changePasswordData.passwordNuova.trim()) {\n      setError('Inserisci la nuova password');\n      return;\n    }\n    if (changePasswordData.passwordNuova !== changePasswordData.confermaPassword) {\n      setError('Le nuove password non coincidono');\n      return;\n    }\n    if (changePasswordData.passwordNuova.length < 3) {\n      setError('La nuova password deve essere di almeno 3 caratteri');\n      return;\n    }\n    setLoading(true);\n    setError('');\n    try {\n      const response = await cantieriService.changeCantierePassword(cantiere.id_cantiere, changePasswordData.passwordAttuale, changePasswordData.passwordNuova, changePasswordData.confermaPassword);\n      if (response.success) {\n        setSuccess('Password cambiata con successo!');\n        setChangePasswordData({\n          passwordAttuale: '',\n          passwordNuova: '',\n          confermaPassword: '',\n          showCurrentPassword: false,\n          showNewPassword: false,\n          showConfirmPassword: false\n        });\n\n        // Notifica il componente padre\n        if (onPasswordChanged) {\n          onPasswordChanged();\n        }\n      } else {\n        setError(response.message || 'Errore nel cambio password');\n      }\n    } catch (err) {\n      console.error('Errore nel cambio password:', err);\n      setError(err.detail || 'Errore nel cambio password');\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  // Copia la password negli appunti\n  const handleCopyPassword = () => {\n    if (viewPasswordData.passwordRivelata) {\n      navigator.clipboard.writeText(viewPasswordData.passwordRivelata);\n      setSuccess('Password copiata negli appunti');\n    }\n  };\n\n  // Gestisce la chiusura del dialog\n  const handleClose = () => {\n    setError('');\n    setSuccess('');\n    onClose();\n  };\n  if (!cantiere) return null;\n  return /*#__PURE__*/_jsxDEV(Dialog, {\n    open: open,\n    onClose: handleClose,\n    maxWidth: \"sm\",\n    fullWidth: true,\n    children: [/*#__PURE__*/_jsxDEV(DialogTitle, {\n      children: /*#__PURE__*/_jsxDEV(Box, {\n        sx: {\n          display: 'flex',\n          alignItems: 'center',\n          gap: 1\n        },\n        children: [/*#__PURE__*/_jsxDEV(LockIcon, {}, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 243,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Typography, {\n          variant: \"h6\",\n          children: [\"Gestione Password - \", cantiere.nome]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 244,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 242,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 241,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(DialogContent, {\n      children: [/*#__PURE__*/_jsxDEV(Box, {\n        sx: {\n          borderBottom: 1,\n          borderColor: 'divider',\n          mb: 2\n        },\n        children: /*#__PURE__*/_jsxDEV(Tabs, {\n          value: activeTab,\n          onChange: handleTabChange,\n          children: [/*#__PURE__*/_jsxDEV(Tab, {\n            icon: /*#__PURE__*/_jsxDEV(VisibilityIcon, {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 254,\n              columnNumber: 21\n            }, this),\n            label: \"Visualizza Password\",\n            iconPosition: \"start\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 253,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Tab, {\n            icon: /*#__PURE__*/_jsxDEV(KeyIcon, {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 259,\n              columnNumber: 21\n            }, this),\n            label: \"Cambia Password\",\n            iconPosition: \"start\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 258,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 252,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 251,\n        columnNumber: 9\n      }, this), error && /*#__PURE__*/_jsxDEV(Alert, {\n        severity: \"error\",\n        sx: {\n          mb: 2\n        },\n        children: error\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 267,\n        columnNumber: 11\n      }, this), success && /*#__PURE__*/_jsxDEV(Alert, {\n        severity: \"success\",\n        sx: {\n          mb: 2\n        },\n        children: success\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 273,\n        columnNumber: 11\n      }, this), activeTab === 0 && /*#__PURE__*/_jsxDEV(Box, {\n        children: [/*#__PURE__*/_jsxDEV(Typography, {\n          variant: \"body2\",\n          color: \"text.secondary\",\n          sx: {\n            mb: 2\n          },\n          children: \"Inserisci la password attuale per visualizzarla\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 281,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(TextField, {\n          fullWidth: true,\n          label: \"Password Attuale\",\n          type: viewPasswordData.showPassword ? 'text' : 'password',\n          value: viewPasswordData.passwordAttuale,\n          onChange: e => setViewPasswordData(prev => ({\n            ...prev,\n            passwordAttuale: e.target.value\n          })),\n          sx: {\n            mb: 2\n          },\n          InputProps: {\n            endAdornment: /*#__PURE__*/_jsxDEV(InputAdornment, {\n              position: \"end\",\n              children: /*#__PURE__*/_jsxDEV(IconButton, {\n                onClick: () => setViewPasswordData(prev => ({\n                  ...prev,\n                  showPassword: !prev.showPassword\n                })),\n                edge: \"end\",\n                children: viewPasswordData.showPassword ? /*#__PURE__*/_jsxDEV(VisibilityOffIcon, {}, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 305,\n                  columnNumber: 56\n                }, this) : /*#__PURE__*/_jsxDEV(VisibilityIcon, {}, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 305,\n                  columnNumber: 80\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 298,\n                columnNumber: 21\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 297,\n              columnNumber: 19\n            }, this)\n          }\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 285,\n          columnNumber: 13\n        }, this), viewPasswordData.passwordRivelata && /*#__PURE__*/_jsxDEV(Box, {\n          sx: {\n            mt: 2,\n            p: 2,\n            bgcolor: 'success.light',\n            borderRadius: 1\n          },\n          children: [/*#__PURE__*/_jsxDEV(Typography, {\n            variant: \"subtitle2\",\n            gutterBottom: true,\n            children: \"Password del Cantiere:\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 314,\n            columnNumber: 17\n          }, this), /*#__PURE__*/_jsxDEV(Box, {\n            sx: {\n              display: 'flex',\n              alignItems: 'center',\n              gap: 1\n            },\n            children: [/*#__PURE__*/_jsxDEV(Typography, {\n              variant: \"h6\",\n              sx: {\n                fontFamily: 'monospace',\n                bgcolor: 'background.paper',\n                p: 1,\n                borderRadius: 1,\n                flex: 1\n              },\n              children: viewPasswordData.passwordRivelata\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 318,\n              columnNumber: 19\n            }, this), /*#__PURE__*/_jsxDEV(IconButton, {\n              onClick: handleCopyPassword,\n              title: \"Copia password\",\n              children: /*#__PURE__*/_jsxDEV(ContentCopyIcon, {}, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 334,\n                columnNumber: 21\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 330,\n              columnNumber: 19\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 317,\n            columnNumber: 17\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 313,\n          columnNumber: 15\n        }, this), /*#__PURE__*/_jsxDEV(Box, {\n          sx: {\n            mt: 3,\n            display: 'flex',\n            justifyContent: 'center'\n          },\n          children: /*#__PURE__*/_jsxDEV(Button, {\n            variant: \"contained\",\n            onClick: handleViewPassword,\n            disabled: loading || !viewPasswordData.passwordAttuale.trim(),\n            startIcon: /*#__PURE__*/_jsxDEV(VisibilityIcon, {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 345,\n              columnNumber: 28\n            }, this),\n            children: loading ? 'Verifica...' : 'Visualizza Password'\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 341,\n            columnNumber: 15\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 340,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 280,\n        columnNumber: 11\n      }, this), activeTab === 1 && /*#__PURE__*/_jsxDEV(Box, {\n        children: [/*#__PURE__*/_jsxDEV(Typography, {\n          variant: \"body2\",\n          color: \"text.secondary\",\n          sx: {\n            mb: 2\n          },\n          children: \"Inserisci la password attuale e la nuova password\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 356,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(TextField, {\n          fullWidth: true,\n          label: \"Password Attuale\",\n          type: changePasswordData.showCurrentPassword ? 'text' : 'password',\n          value: changePasswordData.passwordAttuale,\n          onChange: e => setChangePasswordData(prev => ({\n            ...prev,\n            passwordAttuale: e.target.value\n          })),\n          sx: {\n            mb: 2\n          },\n          InputProps: {\n            endAdornment: /*#__PURE__*/_jsxDEV(InputAdornment, {\n              position: \"end\",\n              children: /*#__PURE__*/_jsxDEV(IconButton, {\n                onClick: () => setChangePasswordData(prev => ({\n                  ...prev,\n                  showCurrentPassword: !prev.showCurrentPassword\n                })),\n                edge: \"end\",\n                children: changePasswordData.showCurrentPassword ? /*#__PURE__*/_jsxDEV(VisibilityOffIcon, {}, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 380,\n                  columnNumber: 65\n                }, this) : /*#__PURE__*/_jsxDEV(VisibilityIcon, {}, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 380,\n                  columnNumber: 89\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 373,\n                columnNumber: 21\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 372,\n              columnNumber: 19\n            }, this)\n          }\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 360,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(Divider, {\n          sx: {\n            my: 2\n          }\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 387,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(TextField, {\n          fullWidth: true,\n          label: \"Nuova Password\",\n          type: changePasswordData.showNewPassword ? 'text' : 'password',\n          value: changePasswordData.passwordNuova,\n          onChange: e => setChangePasswordData(prev => ({\n            ...prev,\n            passwordNuova: e.target.value\n          })),\n          sx: {\n            mb: 2\n          },\n          InputProps: {\n            endAdornment: /*#__PURE__*/_jsxDEV(InputAdornment, {\n              position: \"end\",\n              children: /*#__PURE__*/_jsxDEV(IconButton, {\n                onClick: () => setChangePasswordData(prev => ({\n                  ...prev,\n                  showNewPassword: !prev.showNewPassword\n                })),\n                edge: \"end\",\n                children: changePasswordData.showNewPassword ? /*#__PURE__*/_jsxDEV(VisibilityOffIcon, {}, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 409,\n                  columnNumber: 61\n                }, this) : /*#__PURE__*/_jsxDEV(VisibilityIcon, {}, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 409,\n                  columnNumber: 85\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 402,\n                columnNumber: 21\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 401,\n              columnNumber: 19\n            }, this)\n          }\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 389,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(TextField, {\n          fullWidth: true,\n          label: \"Conferma Nuova Password\",\n          type: changePasswordData.showConfirmPassword ? 'text' : 'password',\n          value: changePasswordData.confermaPassword,\n          onChange: e => setChangePasswordData(prev => ({\n            ...prev,\n            confermaPassword: e.target.value\n          })),\n          sx: {\n            mb: 2\n          },\n          InputProps: {\n            endAdornment: /*#__PURE__*/_jsxDEV(InputAdornment, {\n              position: \"end\",\n              children: /*#__PURE__*/_jsxDEV(IconButton, {\n                onClick: () => setChangePasswordData(prev => ({\n                  ...prev,\n                  showConfirmPassword: !prev.showConfirmPassword\n                })),\n                edge: \"end\",\n                children: changePasswordData.showConfirmPassword ? /*#__PURE__*/_jsxDEV(VisibilityOffIcon, {}, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 436,\n                  columnNumber: 65\n                }, this) : /*#__PURE__*/_jsxDEV(VisibilityIcon, {}, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 436,\n                  columnNumber: 89\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 429,\n                columnNumber: 21\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 428,\n              columnNumber: 19\n            }, this)\n          }\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 416,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(Box, {\n          sx: {\n            mt: 3,\n            display: 'flex',\n            justifyContent: 'center'\n          },\n          children: /*#__PURE__*/_jsxDEV(Button, {\n            variant: \"contained\",\n            onClick: handleChangePassword,\n            disabled: loading || !changePasswordData.passwordAttuale.trim() || !changePasswordData.passwordNuova.trim(),\n            startIcon: /*#__PURE__*/_jsxDEV(KeyIcon, {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 448,\n              columnNumber: 28\n            }, this),\n            children: loading ? 'Cambio...' : 'Cambia Password'\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 444,\n            columnNumber: 15\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 443,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 355,\n        columnNumber: 11\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 250,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(DialogActions, {\n      children: /*#__PURE__*/_jsxDEV(Button, {\n        onClick: handleClose,\n        children: \"Chiudi\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 458,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 457,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 235,\n    columnNumber: 5\n  }, this);\n};\n_s(PasswordManagementDialog, \"OfmEJ5Yi0eqw12JlRV3NXlgCVok=\");\n_c = PasswordManagementDialog;\nexport default PasswordManagementDialog;\nvar _c;\n$RefreshReg$(_c, \"PasswordManagementDialog\");", "map": {"version": 3, "names": ["React", "useState", "Dialog", "DialogTitle", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "DialogActions", "<PERSON><PERSON>", "TextField", "Box", "Typography", "<PERSON><PERSON>", "IconButton", "InputAdornment", "Tabs", "Tab", "Divider", "Visibility", "VisibilityIcon", "VisibilityOff", "VisibilityOffIcon", "Lock", "LockIcon", "Key", "KeyIcon", "ContentCopy", "ContentCopyIcon", "cantieriService", "HoldToViewButton", "jsxDEV", "_jsxDEV", "PasswordManagementDialog", "open", "onClose", "cantiere", "onPasswordChanged", "_s", "activeTab", "setActiveTab", "loading", "setLoading", "error", "setError", "success", "setSuccess", "viewPasswordData", "setViewPasswordData", "passwordAttuale", "passwordRivelata", "showPassword", "changePasswordData", "setChangePasswordData", "passwordNuova", "confermaPassword", "showCurrentPassword", "showNewPassword", "showConfirmPassword", "useEffect", "handleTabChange", "event", "newValue", "handleViewPassword", "trim", "response", "verifyCantierePassword", "id_cantiere", "password_corretta", "prev", "password_cantiere", "err", "console", "detail", "handleViewPasswordDirect", "viewCantierePasswordDirect", "includes", "handleChangePassword", "length", "changeCantierePassword", "message", "handleCopyPassword", "navigator", "clipboard", "writeText", "handleClose", "max<PERSON><PERSON><PERSON>", "fullWidth", "children", "sx", "display", "alignItems", "gap", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "variant", "nome", "borderBottom", "borderColor", "mb", "value", "onChange", "icon", "label", "iconPosition", "severity", "color", "type", "e", "target", "InputProps", "endAdornment", "position", "onClick", "edge", "mt", "p", "bgcolor", "borderRadius", "gutterBottom", "fontFamily", "flex", "title", "justifyContent", "disabled", "startIcon", "my", "_c", "$RefreshReg$"], "sources": ["C:/CMS/webapp/frontend/src/components/cantieri/PasswordManagementDialog.js"], "sourcesContent": ["import React, { useState } from 'react';\nimport {\n  Dialog,\n  DialogTitle,\n  DialogContent,\n  <PERSON>alogActions,\n  Button,\n  TextField,\n  Box,\n  Typography,\n  Alert,\n  IconButton,\n  InputAdornment,\n  Tabs,\n  Tab,\n  Divider\n} from '@mui/material';\nimport {\n  Visibility as VisibilityIcon,\n  VisibilityOff as VisibilityOffIcon,\n  Lock as LockIcon,\n  Key as KeyIcon,\n  ContentCopy as ContentCopyIcon\n} from '@mui/icons-material';\nimport cantieriService from '../../services/cantieriService';\nimport HoldToViewButton from './HoldToViewButton';\n\n/**\n * Dialog per la gestione delle password del cantiere\n * Permette di visualizzare e modificare la password\n */\nconst PasswordManagementDialog = ({\n  open,\n  onClose,\n  cantiere,\n  onPasswordChanged = null\n}) => {\n  const [activeTab, setActiveTab] = useState(0);\n  const [loading, setLoading] = useState(false);\n  const [error, setError] = useState('');\n  const [success, setSuccess] = useState('');\n  \n  // Stati per visualizzazione password\n  const [viewPasswordData, setViewPasswordData] = useState({\n    passwordAttuale: '',\n    passwordRivelata: '',\n    showPassword: false\n  });\n  \n  // Stati per cambio password\n  const [changePasswordData, setChangePasswordData] = useState({\n    passwordAttuale: '',\n    passwordNuova: '',\n    confermaPassword: '',\n    showCurrentPassword: false,\n    showNewPassword: false,\n    showConfirmPassword: false\n  });\n\n  // Reset dei dati quando si apre/chiude il dialog\n  React.useEffect(() => {\n    if (open) {\n      setActiveTab(0);\n      setError('');\n      setSuccess('');\n      setViewPasswordData({\n        passwordAttuale: '',\n        passwordRivelata: '',\n        showPassword: false\n      });\n      setChangePasswordData({\n        passwordAttuale: '',\n        passwordNuova: '',\n        confermaPassword: '',\n        showCurrentPassword: false,\n        showNewPassword: false,\n        showConfirmPassword: false\n      });\n    }\n  }, [open]);\n\n  // Gestisce il cambio di tab\n  const handleTabChange = (event, newValue) => {\n    setActiveTab(newValue);\n    setError('');\n    setSuccess('');\n  };\n\n  // Gestisce la visualizzazione della password con verifica\n  const handleViewPassword = async () => {\n    if (!viewPasswordData.passwordAttuale.trim()) {\n      setError('Inserisci la password attuale');\n      return;\n    }\n\n    setLoading(true);\n    setError('');\n\n    try {\n      const response = await cantieriService.verifyCantierePassword(\n        cantiere.id_cantiere,\n        viewPasswordData.passwordAttuale\n      );\n\n      if (response.password_corretta) {\n        setViewPasswordData(prev => ({\n          ...prev,\n          passwordRivelata: response.password_cantiere,\n          showPassword: true\n        }));\n        setSuccess('Password verificata correttamente');\n      } else {\n        setError('Password non corretta');\n        setViewPasswordData(prev => ({\n          ...prev,\n          passwordRivelata: '',\n          showPassword: false\n        }));\n      }\n    } catch (err) {\n      console.error('Errore nella verifica password:', err);\n      setError(err.detail || 'Errore nella verifica della password');\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  // Gestisce la visualizzazione diretta della password (senza verifica)\n  const handleViewPasswordDirect = async () => {\n    setLoading(true);\n    setError('');\n\n    try {\n      const response = await cantieriService.viewCantierePasswordDirect(\n        cantiere.id_cantiere\n      );\n\n      setViewPasswordData(prev => ({\n        ...prev,\n        passwordRivelata: response.password_cantiere,\n        showPassword: true,\n        passwordAttuale: '' // Pulisce il campo password attuale\n      }));\n      setSuccess('Password recuperata con successo');\n    } catch (err) {\n      console.error('Errore nel recupero password:', err);\n      if (err.detail && err.detail.includes('hashata')) {\n        setError('La password è hashata e non può essere recuperata. Utilizza la funzione di cambio password.');\n      } else {\n        setError(err.detail || 'Errore nel recupero della password');\n      }\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  // Gestisce il cambio password\n  const handleChangePassword = async () => {\n    // Validazioni\n    if (!changePasswordData.passwordAttuale.trim()) {\n      setError('Inserisci la password attuale');\n      return;\n    }\n    \n    if (!changePasswordData.passwordNuova.trim()) {\n      setError('Inserisci la nuova password');\n      return;\n    }\n    \n    if (changePasswordData.passwordNuova !== changePasswordData.confermaPassword) {\n      setError('Le nuove password non coincidono');\n      return;\n    }\n    \n    if (changePasswordData.passwordNuova.length < 3) {\n      setError('La nuova password deve essere di almeno 3 caratteri');\n      return;\n    }\n\n    setLoading(true);\n    setError('');\n    \n    try {\n      const response = await cantieriService.changeCantierePassword(\n        cantiere.id_cantiere,\n        changePasswordData.passwordAttuale,\n        changePasswordData.passwordNuova,\n        changePasswordData.confermaPassword\n      );\n      \n      if (response.success) {\n        setSuccess('Password cambiata con successo!');\n        setChangePasswordData({\n          passwordAttuale: '',\n          passwordNuova: '',\n          confermaPassword: '',\n          showCurrentPassword: false,\n          showNewPassword: false,\n          showConfirmPassword: false\n        });\n        \n        // Notifica il componente padre\n        if (onPasswordChanged) {\n          onPasswordChanged();\n        }\n      } else {\n        setError(response.message || 'Errore nel cambio password');\n      }\n    } catch (err) {\n      console.error('Errore nel cambio password:', err);\n      setError(err.detail || 'Errore nel cambio password');\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  // Copia la password negli appunti\n  const handleCopyPassword = () => {\n    if (viewPasswordData.passwordRivelata) {\n      navigator.clipboard.writeText(viewPasswordData.passwordRivelata);\n      setSuccess('Password copiata negli appunti');\n    }\n  };\n\n  // Gestisce la chiusura del dialog\n  const handleClose = () => {\n    setError('');\n    setSuccess('');\n    onClose();\n  };\n\n  if (!cantiere) return null;\n\n  return (\n    <Dialog \n      open={open} \n      onClose={handleClose}\n      maxWidth=\"sm\"\n      fullWidth\n    >\n      <DialogTitle>\n        <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>\n          <LockIcon />\n          <Typography variant=\"h6\">\n            Gestione Password - {cantiere.nome}\n          </Typography>\n        </Box>\n      </DialogTitle>\n      \n      <DialogContent>\n        <Box sx={{ borderBottom: 1, borderColor: 'divider', mb: 2 }}>\n          <Tabs value={activeTab} onChange={handleTabChange}>\n            <Tab \n              icon={<VisibilityIcon />} \n              label=\"Visualizza Password\" \n              iconPosition=\"start\"\n            />\n            <Tab \n              icon={<KeyIcon />} \n              label=\"Cambia Password\" \n              iconPosition=\"start\"\n            />\n          </Tabs>\n        </Box>\n\n        {error && (\n          <Alert severity=\"error\" sx={{ mb: 2 }}>\n            {error}\n          </Alert>\n        )}\n        \n        {success && (\n          <Alert severity=\"success\" sx={{ mb: 2 }}>\n            {success}\n          </Alert>\n        )}\n\n        {/* Tab Visualizza Password */}\n        {activeTab === 0 && (\n          <Box>\n            <Typography variant=\"body2\" color=\"text.secondary\" sx={{ mb: 2 }}>\n              Inserisci la password attuale per visualizzarla\n            </Typography>\n            \n            <TextField\n              fullWidth\n              label=\"Password Attuale\"\n              type={viewPasswordData.showPassword ? 'text' : 'password'}\n              value={viewPasswordData.passwordAttuale}\n              onChange={(e) => setViewPasswordData(prev => ({\n                ...prev,\n                passwordAttuale: e.target.value\n              }))}\n              sx={{ mb: 2 }}\n              InputProps={{\n                endAdornment: (\n                  <InputAdornment position=\"end\">\n                    <IconButton\n                      onClick={() => setViewPasswordData(prev => ({\n                        ...prev,\n                        showPassword: !prev.showPassword\n                      }))}\n                      edge=\"end\"\n                    >\n                      {viewPasswordData.showPassword ? <VisibilityOffIcon /> : <VisibilityIcon />}\n                    </IconButton>\n                  </InputAdornment>\n                )\n              }}\n            />\n            \n            {viewPasswordData.passwordRivelata && (\n              <Box sx={{ mt: 2, p: 2, bgcolor: 'success.light', borderRadius: 1 }}>\n                <Typography variant=\"subtitle2\" gutterBottom>\n                  Password del Cantiere:\n                </Typography>\n                <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>\n                  <Typography \n                    variant=\"h6\" \n                    sx={{ \n                      fontFamily: 'monospace',\n                      bgcolor: 'background.paper',\n                      p: 1,\n                      borderRadius: 1,\n                      flex: 1\n                    }}\n                  >\n                    {viewPasswordData.passwordRivelata}\n                  </Typography>\n                  <IconButton \n                    onClick={handleCopyPassword}\n                    title=\"Copia password\"\n                  >\n                    <ContentCopyIcon />\n                  </IconButton>\n                </Box>\n              </Box>\n            )}\n            \n            <Box sx={{ mt: 3, display: 'flex', justifyContent: 'center' }}>\n              <Button\n                variant=\"contained\"\n                onClick={handleViewPassword}\n                disabled={loading || !viewPasswordData.passwordAttuale.trim()}\n                startIcon={<VisibilityIcon />}\n              >\n                {loading ? 'Verifica...' : 'Visualizza Password'}\n              </Button>\n            </Box>\n          </Box>\n        )}\n\n        {/* Tab Cambia Password */}\n        {activeTab === 1 && (\n          <Box>\n            <Typography variant=\"body2\" color=\"text.secondary\" sx={{ mb: 2 }}>\n              Inserisci la password attuale e la nuova password\n            </Typography>\n            \n            <TextField\n              fullWidth\n              label=\"Password Attuale\"\n              type={changePasswordData.showCurrentPassword ? 'text' : 'password'}\n              value={changePasswordData.passwordAttuale}\n              onChange={(e) => setChangePasswordData(prev => ({\n                ...prev,\n                passwordAttuale: e.target.value\n              }))}\n              sx={{ mb: 2 }}\n              InputProps={{\n                endAdornment: (\n                  <InputAdornment position=\"end\">\n                    <IconButton\n                      onClick={() => setChangePasswordData(prev => ({\n                        ...prev,\n                        showCurrentPassword: !prev.showCurrentPassword\n                      }))}\n                      edge=\"end\"\n                    >\n                      {changePasswordData.showCurrentPassword ? <VisibilityOffIcon /> : <VisibilityIcon />}\n                    </IconButton>\n                  </InputAdornment>\n                )\n              }}\n            />\n            \n            <Divider sx={{ my: 2 }} />\n            \n            <TextField\n              fullWidth\n              label=\"Nuova Password\"\n              type={changePasswordData.showNewPassword ? 'text' : 'password'}\n              value={changePasswordData.passwordNuova}\n              onChange={(e) => setChangePasswordData(prev => ({\n                ...prev,\n                passwordNuova: e.target.value\n              }))}\n              sx={{ mb: 2 }}\n              InputProps={{\n                endAdornment: (\n                  <InputAdornment position=\"end\">\n                    <IconButton\n                      onClick={() => setChangePasswordData(prev => ({\n                        ...prev,\n                        showNewPassword: !prev.showNewPassword\n                      }))}\n                      edge=\"end\"\n                    >\n                      {changePasswordData.showNewPassword ? <VisibilityOffIcon /> : <VisibilityIcon />}\n                    </IconButton>\n                  </InputAdornment>\n                )\n              }}\n            />\n            \n            <TextField\n              fullWidth\n              label=\"Conferma Nuova Password\"\n              type={changePasswordData.showConfirmPassword ? 'text' : 'password'}\n              value={changePasswordData.confermaPassword}\n              onChange={(e) => setChangePasswordData(prev => ({\n                ...prev,\n                confermaPassword: e.target.value\n              }))}\n              sx={{ mb: 2 }}\n              InputProps={{\n                endAdornment: (\n                  <InputAdornment position=\"end\">\n                    <IconButton\n                      onClick={() => setChangePasswordData(prev => ({\n                        ...prev,\n                        showConfirmPassword: !prev.showConfirmPassword\n                      }))}\n                      edge=\"end\"\n                    >\n                      {changePasswordData.showConfirmPassword ? <VisibilityOffIcon /> : <VisibilityIcon />}\n                    </IconButton>\n                  </InputAdornment>\n                )\n              }}\n            />\n            \n            <Box sx={{ mt: 3, display: 'flex', justifyContent: 'center' }}>\n              <Button\n                variant=\"contained\"\n                onClick={handleChangePassword}\n                disabled={loading || !changePasswordData.passwordAttuale.trim() || !changePasswordData.passwordNuova.trim()}\n                startIcon={<KeyIcon />}\n              >\n                {loading ? 'Cambio...' : 'Cambia Password'}\n              </Button>\n            </Box>\n          </Box>\n        )}\n      </DialogContent>\n      \n      <DialogActions>\n        <Button onClick={handleClose}>\n          Chiudi\n        </Button>\n      </DialogActions>\n    </Dialog>\n  );\n};\n\nexport default PasswordManagementDialog;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,QAAQ,OAAO;AACvC,SACEC,MAAM,EACNC,WAAW,EACXC,aAAa,EACbC,aAAa,EACbC,MAAM,EACNC,SAAS,EACTC,GAAG,EACHC,UAAU,EACVC,KAAK,EACLC,UAAU,EACVC,cAAc,EACdC,IAAI,EACJC,GAAG,EACHC,OAAO,QACF,eAAe;AACtB,SACEC,UAAU,IAAIC,cAAc,EAC5BC,aAAa,IAAIC,iBAAiB,EAClCC,IAAI,IAAIC,QAAQ,EAChBC,GAAG,IAAIC,OAAO,EACdC,WAAW,IAAIC,eAAe,QACzB,qBAAqB;AAC5B,OAAOC,eAAe,MAAM,gCAAgC;AAC5D,OAAOC,gBAAgB,MAAM,oBAAoB;;AAEjD;AACA;AACA;AACA;AAHA,SAAAC,MAAA,IAAAC,OAAA;AAIA,MAAMC,wBAAwB,GAAGA,CAAC;EAChCC,IAAI;EACJC,OAAO;EACPC,QAAQ;EACRC,iBAAiB,GAAG;AACtB,CAAC,KAAK;EAAAC,EAAA;EACJ,MAAM,CAACC,SAAS,EAAEC,YAAY,CAAC,GAAGpC,QAAQ,CAAC,CAAC,CAAC;EAC7C,MAAM,CAACqC,OAAO,EAAEC,UAAU,CAAC,GAAGtC,QAAQ,CAAC,KAAK,CAAC;EAC7C,MAAM,CAACuC,KAAK,EAAEC,QAAQ,CAAC,GAAGxC,QAAQ,CAAC,EAAE,CAAC;EACtC,MAAM,CAACyC,OAAO,EAAEC,UAAU,CAAC,GAAG1C,QAAQ,CAAC,EAAE,CAAC;;EAE1C;EACA,MAAM,CAAC2C,gBAAgB,EAAEC,mBAAmB,CAAC,GAAG5C,QAAQ,CAAC;IACvD6C,eAAe,EAAE,EAAE;IACnBC,gBAAgB,EAAE,EAAE;IACpBC,YAAY,EAAE;EAChB,CAAC,CAAC;;EAEF;EACA,MAAM,CAACC,kBAAkB,EAAEC,qBAAqB,CAAC,GAAGjD,QAAQ,CAAC;IAC3D6C,eAAe,EAAE,EAAE;IACnBK,aAAa,EAAE,EAAE;IACjBC,gBAAgB,EAAE,EAAE;IACpBC,mBAAmB,EAAE,KAAK;IAC1BC,eAAe,EAAE,KAAK;IACtBC,mBAAmB,EAAE;EACvB,CAAC,CAAC;;EAEF;EACAvD,KAAK,CAACwD,SAAS,CAAC,MAAM;IACpB,IAAIzB,IAAI,EAAE;MACRM,YAAY,CAAC,CAAC,CAAC;MACfI,QAAQ,CAAC,EAAE,CAAC;MACZE,UAAU,CAAC,EAAE,CAAC;MACdE,mBAAmB,CAAC;QAClBC,eAAe,EAAE,EAAE;QACnBC,gBAAgB,EAAE,EAAE;QACpBC,YAAY,EAAE;MAChB,CAAC,CAAC;MACFE,qBAAqB,CAAC;QACpBJ,eAAe,EAAE,EAAE;QACnBK,aAAa,EAAE,EAAE;QACjBC,gBAAgB,EAAE,EAAE;QACpBC,mBAAmB,EAAE,KAAK;QAC1BC,eAAe,EAAE,KAAK;QACtBC,mBAAmB,EAAE;MACvB,CAAC,CAAC;IACJ;EACF,CAAC,EAAE,CAACxB,IAAI,CAAC,CAAC;;EAEV;EACA,MAAM0B,eAAe,GAAGA,CAACC,KAAK,EAAEC,QAAQ,KAAK;IAC3CtB,YAAY,CAACsB,QAAQ,CAAC;IACtBlB,QAAQ,CAAC,EAAE,CAAC;IACZE,UAAU,CAAC,EAAE,CAAC;EAChB,CAAC;;EAED;EACA,MAAMiB,kBAAkB,GAAG,MAAAA,CAAA,KAAY;IACrC,IAAI,CAAChB,gBAAgB,CAACE,eAAe,CAACe,IAAI,CAAC,CAAC,EAAE;MAC5CpB,QAAQ,CAAC,+BAA+B,CAAC;MACzC;IACF;IAEAF,UAAU,CAAC,IAAI,CAAC;IAChBE,QAAQ,CAAC,EAAE,CAAC;IAEZ,IAAI;MACF,MAAMqB,QAAQ,GAAG,MAAMpC,eAAe,CAACqC,sBAAsB,CAC3D9B,QAAQ,CAAC+B,WAAW,EACpBpB,gBAAgB,CAACE,eACnB,CAAC;MAED,IAAIgB,QAAQ,CAACG,iBAAiB,EAAE;QAC9BpB,mBAAmB,CAACqB,IAAI,KAAK;UAC3B,GAAGA,IAAI;UACPnB,gBAAgB,EAAEe,QAAQ,CAACK,iBAAiB;UAC5CnB,YAAY,EAAE;QAChB,CAAC,CAAC,CAAC;QACHL,UAAU,CAAC,mCAAmC,CAAC;MACjD,CAAC,MAAM;QACLF,QAAQ,CAAC,uBAAuB,CAAC;QACjCI,mBAAmB,CAACqB,IAAI,KAAK;UAC3B,GAAGA,IAAI;UACPnB,gBAAgB,EAAE,EAAE;UACpBC,YAAY,EAAE;QAChB,CAAC,CAAC,CAAC;MACL;IACF,CAAC,CAAC,OAAOoB,GAAG,EAAE;MACZC,OAAO,CAAC7B,KAAK,CAAC,iCAAiC,EAAE4B,GAAG,CAAC;MACrD3B,QAAQ,CAAC2B,GAAG,CAACE,MAAM,IAAI,sCAAsC,CAAC;IAChE,CAAC,SAAS;MACR/B,UAAU,CAAC,KAAK,CAAC;IACnB;EACF,CAAC;;EAED;EACA,MAAMgC,wBAAwB,GAAG,MAAAA,CAAA,KAAY;IAC3ChC,UAAU,CAAC,IAAI,CAAC;IAChBE,QAAQ,CAAC,EAAE,CAAC;IAEZ,IAAI;MACF,MAAMqB,QAAQ,GAAG,MAAMpC,eAAe,CAAC8C,0BAA0B,CAC/DvC,QAAQ,CAAC+B,WACX,CAAC;MAEDnB,mBAAmB,CAACqB,IAAI,KAAK;QAC3B,GAAGA,IAAI;QACPnB,gBAAgB,EAAEe,QAAQ,CAACK,iBAAiB;QAC5CnB,YAAY,EAAE,IAAI;QAClBF,eAAe,EAAE,EAAE,CAAC;MACtB,CAAC,CAAC,CAAC;MACHH,UAAU,CAAC,kCAAkC,CAAC;IAChD,CAAC,CAAC,OAAOyB,GAAG,EAAE;MACZC,OAAO,CAAC7B,KAAK,CAAC,+BAA+B,EAAE4B,GAAG,CAAC;MACnD,IAAIA,GAAG,CAACE,MAAM,IAAIF,GAAG,CAACE,MAAM,CAACG,QAAQ,CAAC,SAAS,CAAC,EAAE;QAChDhC,QAAQ,CAAC,6FAA6F,CAAC;MACzG,CAAC,MAAM;QACLA,QAAQ,CAAC2B,GAAG,CAACE,MAAM,IAAI,oCAAoC,CAAC;MAC9D;IACF,CAAC,SAAS;MACR/B,UAAU,CAAC,KAAK,CAAC;IACnB;EACF,CAAC;;EAED;EACA,MAAMmC,oBAAoB,GAAG,MAAAA,CAAA,KAAY;IACvC;IACA,IAAI,CAACzB,kBAAkB,CAACH,eAAe,CAACe,IAAI,CAAC,CAAC,EAAE;MAC9CpB,QAAQ,CAAC,+BAA+B,CAAC;MACzC;IACF;IAEA,IAAI,CAACQ,kBAAkB,CAACE,aAAa,CAACU,IAAI,CAAC,CAAC,EAAE;MAC5CpB,QAAQ,CAAC,6BAA6B,CAAC;MACvC;IACF;IAEA,IAAIQ,kBAAkB,CAACE,aAAa,KAAKF,kBAAkB,CAACG,gBAAgB,EAAE;MAC5EX,QAAQ,CAAC,kCAAkC,CAAC;MAC5C;IACF;IAEA,IAAIQ,kBAAkB,CAACE,aAAa,CAACwB,MAAM,GAAG,CAAC,EAAE;MAC/ClC,QAAQ,CAAC,qDAAqD,CAAC;MAC/D;IACF;IAEAF,UAAU,CAAC,IAAI,CAAC;IAChBE,QAAQ,CAAC,EAAE,CAAC;IAEZ,IAAI;MACF,MAAMqB,QAAQ,GAAG,MAAMpC,eAAe,CAACkD,sBAAsB,CAC3D3C,QAAQ,CAAC+B,WAAW,EACpBf,kBAAkB,CAACH,eAAe,EAClCG,kBAAkB,CAACE,aAAa,EAChCF,kBAAkB,CAACG,gBACrB,CAAC;MAED,IAAIU,QAAQ,CAACpB,OAAO,EAAE;QACpBC,UAAU,CAAC,iCAAiC,CAAC;QAC7CO,qBAAqB,CAAC;UACpBJ,eAAe,EAAE,EAAE;UACnBK,aAAa,EAAE,EAAE;UACjBC,gBAAgB,EAAE,EAAE;UACpBC,mBAAmB,EAAE,KAAK;UAC1BC,eAAe,EAAE,KAAK;UACtBC,mBAAmB,EAAE;QACvB,CAAC,CAAC;;QAEF;QACA,IAAIrB,iBAAiB,EAAE;UACrBA,iBAAiB,CAAC,CAAC;QACrB;MACF,CAAC,MAAM;QACLO,QAAQ,CAACqB,QAAQ,CAACe,OAAO,IAAI,4BAA4B,CAAC;MAC5D;IACF,CAAC,CAAC,OAAOT,GAAG,EAAE;MACZC,OAAO,CAAC7B,KAAK,CAAC,6BAA6B,EAAE4B,GAAG,CAAC;MACjD3B,QAAQ,CAAC2B,GAAG,CAACE,MAAM,IAAI,4BAA4B,CAAC;IACtD,CAAC,SAAS;MACR/B,UAAU,CAAC,KAAK,CAAC;IACnB;EACF,CAAC;;EAED;EACA,MAAMuC,kBAAkB,GAAGA,CAAA,KAAM;IAC/B,IAAIlC,gBAAgB,CAACG,gBAAgB,EAAE;MACrCgC,SAAS,CAACC,SAAS,CAACC,SAAS,CAACrC,gBAAgB,CAACG,gBAAgB,CAAC;MAChEJ,UAAU,CAAC,gCAAgC,CAAC;IAC9C;EACF,CAAC;;EAED;EACA,MAAMuC,WAAW,GAAGA,CAAA,KAAM;IACxBzC,QAAQ,CAAC,EAAE,CAAC;IACZE,UAAU,CAAC,EAAE,CAAC;IACdX,OAAO,CAAC,CAAC;EACX,CAAC;EAED,IAAI,CAACC,QAAQ,EAAE,OAAO,IAAI;EAE1B,oBACEJ,OAAA,CAAC3B,MAAM;IACL6B,IAAI,EAAEA,IAAK;IACXC,OAAO,EAAEkD,WAAY;IACrBC,QAAQ,EAAC,IAAI;IACbC,SAAS;IAAAC,QAAA,gBAETxD,OAAA,CAAC1B,WAAW;MAAAkF,QAAA,eACVxD,OAAA,CAACrB,GAAG;QAAC8E,EAAE,EAAE;UAAEC,OAAO,EAAE,MAAM;UAAEC,UAAU,EAAE,QAAQ;UAAEC,GAAG,EAAE;QAAE,CAAE;QAAAJ,QAAA,gBACzDxD,OAAA,CAACR,QAAQ;UAAAqE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,eACZhE,OAAA,CAACpB,UAAU;UAACqF,OAAO,EAAC,IAAI;UAAAT,QAAA,GAAC,sBACH,EAACpD,QAAQ,CAAC8D,IAAI;QAAA;UAAAL,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACxB,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACV;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACK,CAAC,eAEdhE,OAAA,CAACzB,aAAa;MAAAiF,QAAA,gBACZxD,OAAA,CAACrB,GAAG;QAAC8E,EAAE,EAAE;UAAEU,YAAY,EAAE,CAAC;UAAEC,WAAW,EAAE,SAAS;UAAEC,EAAE,EAAE;QAAE,CAAE;QAAAb,QAAA,eAC1DxD,OAAA,CAAChB,IAAI;UAACsF,KAAK,EAAE/D,SAAU;UAACgE,QAAQ,EAAE3C,eAAgB;UAAA4B,QAAA,gBAChDxD,OAAA,CAACf,GAAG;YACFuF,IAAI,eAAExE,OAAA,CAACZ,cAAc;cAAAyE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAE;YACzBS,KAAK,EAAC,qBAAqB;YAC3BC,YAAY,EAAC;UAAO;YAAAb,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACrB,CAAC,eACFhE,OAAA,CAACf,GAAG;YACFuF,IAAI,eAAExE,OAAA,CAACN,OAAO;cAAAmE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAE;YAClBS,KAAK,EAAC,iBAAiB;YACvBC,YAAY,EAAC;UAAO;YAAAb,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACrB,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACE;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACJ,CAAC,EAELrD,KAAK,iBACJX,OAAA,CAACnB,KAAK;QAAC8F,QAAQ,EAAC,OAAO;QAAClB,EAAE,EAAE;UAAEY,EAAE,EAAE;QAAE,CAAE;QAAAb,QAAA,EACnC7C;MAAK;QAAAkD,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACD,CACR,EAEAnD,OAAO,iBACNb,OAAA,CAACnB,KAAK;QAAC8F,QAAQ,EAAC,SAAS;QAAClB,EAAE,EAAE;UAAEY,EAAE,EAAE;QAAE,CAAE;QAAAb,QAAA,EACrC3C;MAAO;QAAAgD,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CACR,EAGAzD,SAAS,KAAK,CAAC,iBACdP,OAAA,CAACrB,GAAG;QAAA6E,QAAA,gBACFxD,OAAA,CAACpB,UAAU;UAACqF,OAAO,EAAC,OAAO;UAACW,KAAK,EAAC,gBAAgB;UAACnB,EAAE,EAAE;YAAEY,EAAE,EAAE;UAAE,CAAE;UAAAb,QAAA,EAAC;QAElE;UAAAK,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAY,CAAC,eAEbhE,OAAA,CAACtB,SAAS;UACR6E,SAAS;UACTkB,KAAK,EAAC,kBAAkB;UACxBI,IAAI,EAAE9D,gBAAgB,CAACI,YAAY,GAAG,MAAM,GAAG,UAAW;UAC1DmD,KAAK,EAAEvD,gBAAgB,CAACE,eAAgB;UACxCsD,QAAQ,EAAGO,CAAC,IAAK9D,mBAAmB,CAACqB,IAAI,KAAK;YAC5C,GAAGA,IAAI;YACPpB,eAAe,EAAE6D,CAAC,CAACC,MAAM,CAACT;UAC5B,CAAC,CAAC,CAAE;UACJb,EAAE,EAAE;YAAEY,EAAE,EAAE;UAAE,CAAE;UACdW,UAAU,EAAE;YACVC,YAAY,eACVjF,OAAA,CAACjB,cAAc;cAACmG,QAAQ,EAAC,KAAK;cAAA1B,QAAA,eAC5BxD,OAAA,CAAClB,UAAU;gBACTqG,OAAO,EAAEA,CAAA,KAAMnE,mBAAmB,CAACqB,IAAI,KAAK;kBAC1C,GAAGA,IAAI;kBACPlB,YAAY,EAAE,CAACkB,IAAI,CAAClB;gBACtB,CAAC,CAAC,CAAE;gBACJiE,IAAI,EAAC,KAAK;gBAAA5B,QAAA,EAETzC,gBAAgB,CAACI,YAAY,gBAAGnB,OAAA,CAACV,iBAAiB;kBAAAuE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAC,gBAAGhE,OAAA,CAACZ,cAAc;kBAAAyE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACjE;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACC;UAEpB;QAAE;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,EAEDjD,gBAAgB,CAACG,gBAAgB,iBAChClB,OAAA,CAACrB,GAAG;UAAC8E,EAAE,EAAE;YAAE4B,EAAE,EAAE,CAAC;YAAEC,CAAC,EAAE,CAAC;YAAEC,OAAO,EAAE,eAAe;YAAEC,YAAY,EAAE;UAAE,CAAE;UAAAhC,QAAA,gBAClExD,OAAA,CAACpB,UAAU;YAACqF,OAAO,EAAC,WAAW;YAACwB,YAAY;YAAAjC,QAAA,EAAC;UAE7C;YAAAK,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAY,CAAC,eACbhE,OAAA,CAACrB,GAAG;YAAC8E,EAAE,EAAE;cAAEC,OAAO,EAAE,MAAM;cAAEC,UAAU,EAAE,QAAQ;cAAEC,GAAG,EAAE;YAAE,CAAE;YAAAJ,QAAA,gBACzDxD,OAAA,CAACpB,UAAU;cACTqF,OAAO,EAAC,IAAI;cACZR,EAAE,EAAE;gBACFiC,UAAU,EAAE,WAAW;gBACvBH,OAAO,EAAE,kBAAkB;gBAC3BD,CAAC,EAAE,CAAC;gBACJE,YAAY,EAAE,CAAC;gBACfG,IAAI,EAAE;cACR,CAAE;cAAAnC,QAAA,EAEDzC,gBAAgB,CAACG;YAAgB;cAAA2C,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACxB,CAAC,eACbhE,OAAA,CAAClB,UAAU;cACTqG,OAAO,EAAElC,kBAAmB;cAC5B2C,KAAK,EAAC,gBAAgB;cAAApC,QAAA,eAEtBxD,OAAA,CAACJ,eAAe;gBAAAiE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACT,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACV,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CACN,eAEDhE,OAAA,CAACrB,GAAG;UAAC8E,EAAE,EAAE;YAAE4B,EAAE,EAAE,CAAC;YAAE3B,OAAO,EAAE,MAAM;YAAEmC,cAAc,EAAE;UAAS,CAAE;UAAArC,QAAA,eAC5DxD,OAAA,CAACvB,MAAM;YACLwF,OAAO,EAAC,WAAW;YACnBkB,OAAO,EAAEpD,kBAAmB;YAC5B+D,QAAQ,EAAErF,OAAO,IAAI,CAACM,gBAAgB,CAACE,eAAe,CAACe,IAAI,CAAC,CAAE;YAC9D+D,SAAS,eAAE/F,OAAA,CAACZ,cAAc;cAAAyE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAE;YAAAR,QAAA,EAE7B/C,OAAO,GAAG,aAAa,GAAG;UAAqB;YAAAoD,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC1C;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACN,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CACN,EAGAzD,SAAS,KAAK,CAAC,iBACdP,OAAA,CAACrB,GAAG;QAAA6E,QAAA,gBACFxD,OAAA,CAACpB,UAAU;UAACqF,OAAO,EAAC,OAAO;UAACW,KAAK,EAAC,gBAAgB;UAACnB,EAAE,EAAE;YAAEY,EAAE,EAAE;UAAE,CAAE;UAAAb,QAAA,EAAC;QAElE;UAAAK,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAY,CAAC,eAEbhE,OAAA,CAACtB,SAAS;UACR6E,SAAS;UACTkB,KAAK,EAAC,kBAAkB;UACxBI,IAAI,EAAEzD,kBAAkB,CAACI,mBAAmB,GAAG,MAAM,GAAG,UAAW;UACnE8C,KAAK,EAAElD,kBAAkB,CAACH,eAAgB;UAC1CsD,QAAQ,EAAGO,CAAC,IAAKzD,qBAAqB,CAACgB,IAAI,KAAK;YAC9C,GAAGA,IAAI;YACPpB,eAAe,EAAE6D,CAAC,CAACC,MAAM,CAACT;UAC5B,CAAC,CAAC,CAAE;UACJb,EAAE,EAAE;YAAEY,EAAE,EAAE;UAAE,CAAE;UACdW,UAAU,EAAE;YACVC,YAAY,eACVjF,OAAA,CAACjB,cAAc;cAACmG,QAAQ,EAAC,KAAK;cAAA1B,QAAA,eAC5BxD,OAAA,CAAClB,UAAU;gBACTqG,OAAO,EAAEA,CAAA,KAAM9D,qBAAqB,CAACgB,IAAI,KAAK;kBAC5C,GAAGA,IAAI;kBACPb,mBAAmB,EAAE,CAACa,IAAI,CAACb;gBAC7B,CAAC,CAAC,CAAE;gBACJ4D,IAAI,EAAC,KAAK;gBAAA5B,QAAA,EAETpC,kBAAkB,CAACI,mBAAmB,gBAAGxB,OAAA,CAACV,iBAAiB;kBAAAuE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAC,gBAAGhE,OAAA,CAACZ,cAAc;kBAAAyE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC1E;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACC;UAEpB;QAAE;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,eAEFhE,OAAA,CAACd,OAAO;UAACuE,EAAE,EAAE;YAAEuC,EAAE,EAAE;UAAE;QAAE;UAAAnC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,eAE1BhE,OAAA,CAACtB,SAAS;UACR6E,SAAS;UACTkB,KAAK,EAAC,gBAAgB;UACtBI,IAAI,EAAEzD,kBAAkB,CAACK,eAAe,GAAG,MAAM,GAAG,UAAW;UAC/D6C,KAAK,EAAElD,kBAAkB,CAACE,aAAc;UACxCiD,QAAQ,EAAGO,CAAC,IAAKzD,qBAAqB,CAACgB,IAAI,KAAK;YAC9C,GAAGA,IAAI;YACPf,aAAa,EAAEwD,CAAC,CAACC,MAAM,CAACT;UAC1B,CAAC,CAAC,CAAE;UACJb,EAAE,EAAE;YAAEY,EAAE,EAAE;UAAE,CAAE;UACdW,UAAU,EAAE;YACVC,YAAY,eACVjF,OAAA,CAACjB,cAAc;cAACmG,QAAQ,EAAC,KAAK;cAAA1B,QAAA,eAC5BxD,OAAA,CAAClB,UAAU;gBACTqG,OAAO,EAAEA,CAAA,KAAM9D,qBAAqB,CAACgB,IAAI,KAAK;kBAC5C,GAAGA,IAAI;kBACPZ,eAAe,EAAE,CAACY,IAAI,CAACZ;gBACzB,CAAC,CAAC,CAAE;gBACJ2D,IAAI,EAAC,KAAK;gBAAA5B,QAAA,EAETpC,kBAAkB,CAACK,eAAe,gBAAGzB,OAAA,CAACV,iBAAiB;kBAAAuE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAC,gBAAGhE,OAAA,CAACZ,cAAc;kBAAAyE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACtE;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACC;UAEpB;QAAE;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,eAEFhE,OAAA,CAACtB,SAAS;UACR6E,SAAS;UACTkB,KAAK,EAAC,yBAAyB;UAC/BI,IAAI,EAAEzD,kBAAkB,CAACM,mBAAmB,GAAG,MAAM,GAAG,UAAW;UACnE4C,KAAK,EAAElD,kBAAkB,CAACG,gBAAiB;UAC3CgD,QAAQ,EAAGO,CAAC,IAAKzD,qBAAqB,CAACgB,IAAI,KAAK;YAC9C,GAAGA,IAAI;YACPd,gBAAgB,EAAEuD,CAAC,CAACC,MAAM,CAACT;UAC7B,CAAC,CAAC,CAAE;UACJb,EAAE,EAAE;YAAEY,EAAE,EAAE;UAAE,CAAE;UACdW,UAAU,EAAE;YACVC,YAAY,eACVjF,OAAA,CAACjB,cAAc;cAACmG,QAAQ,EAAC,KAAK;cAAA1B,QAAA,eAC5BxD,OAAA,CAAClB,UAAU;gBACTqG,OAAO,EAAEA,CAAA,KAAM9D,qBAAqB,CAACgB,IAAI,KAAK;kBAC5C,GAAGA,IAAI;kBACPX,mBAAmB,EAAE,CAACW,IAAI,CAACX;gBAC7B,CAAC,CAAC,CAAE;gBACJ0D,IAAI,EAAC,KAAK;gBAAA5B,QAAA,EAETpC,kBAAkB,CAACM,mBAAmB,gBAAG1B,OAAA,CAACV,iBAAiB;kBAAAuE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAC,gBAAGhE,OAAA,CAACZ,cAAc;kBAAAyE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC1E;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACC;UAEpB;QAAE;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,eAEFhE,OAAA,CAACrB,GAAG;UAAC8E,EAAE,EAAE;YAAE4B,EAAE,EAAE,CAAC;YAAE3B,OAAO,EAAE,MAAM;YAAEmC,cAAc,EAAE;UAAS,CAAE;UAAArC,QAAA,eAC5DxD,OAAA,CAACvB,MAAM;YACLwF,OAAO,EAAC,WAAW;YACnBkB,OAAO,EAAEtC,oBAAqB;YAC9BiD,QAAQ,EAAErF,OAAO,IAAI,CAACW,kBAAkB,CAACH,eAAe,CAACe,IAAI,CAAC,CAAC,IAAI,CAACZ,kBAAkB,CAACE,aAAa,CAACU,IAAI,CAAC,CAAE;YAC5G+D,SAAS,eAAE/F,OAAA,CAACN,OAAO;cAAAmE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAE;YAAAR,QAAA,EAEtB/C,OAAO,GAAG,WAAW,GAAG;UAAiB;YAAAoD,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACpC;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACN,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CACN;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACY,CAAC,eAEhBhE,OAAA,CAACxB,aAAa;MAAAgF,QAAA,eACZxD,OAAA,CAACvB,MAAM;QAAC0G,OAAO,EAAE9B,WAAY;QAAAG,QAAA,EAAC;MAE9B;QAAAK,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAQ;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACI,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACV,CAAC;AAEb,CAAC;AAAC1D,EAAA,CAhbIL,wBAAwB;AAAAgG,EAAA,GAAxBhG,wBAAwB;AAkb9B,eAAeA,wBAAwB;AAAC,IAAAgG,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}