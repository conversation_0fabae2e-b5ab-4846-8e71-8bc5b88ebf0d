{"ast": null, "code": "import _extends from \"@babel/runtime/helpers/esm/extends\";\nimport * as React from 'react';\nimport useEventCallback from '@mui/utils/useEventCallback';\nimport { useUtils } from '../useUtils';\nimport { changeSectionValueFormat, cleanDigitSectionValue, doesSectionFormatHaveLeadingZeros, getDateSectionConfigFromFormatToken, getDaysInWeekStr, getLetterEditingOptions } from './useField.utils';\n\n/**\n * The letter editing and the numeric editing each define a `CharacterEditingApplier`.\n * This function decides what the new section value should be and if the focus should switch to the next section.\n *\n * If it returns `null`, then the section value is not updated and the focus does not move.\n */\n\n/**\n * Function called by `applyQuery` which decides:\n * - what is the new section value ?\n * - should the query used to get this value be stored for the next key press ?\n *\n * If it returns `{ sectionValue: string; shouldGoToNextSection: boolean }`,\n * Then we store the query and update the section with the new value.\n *\n * If it returns `{ saveQuery: true` },\n * Then we store the query and don't update the section.\n *\n * If it returns `{ saveQuery: false },\n * Then we do nothing.\n */\n\nconst QUERY_LIFE_DURATION_MS = 5000;\nconst isQueryResponseWithoutValue = response => response.saveQuery != null;\n\n/**\n * Update the active section value when the user pressed a key that is not a navigation key (arrow key for example).\n * This hook has two main editing behaviors\n *\n * 1. The numeric editing when the user presses a digit\n * 2. The letter editing when the user presses another key\n */\nexport const useFieldCharacterEditing = ({\n  sections,\n  updateSectionValue,\n  sectionsValueBoundaries,\n  setTempAndroidValueStr,\n  timezone\n}) => {\n  const utils = useUtils();\n  const [query, setQuery] = React.useState(null);\n  const resetQuery = useEventCallback(() => setQuery(null));\n  React.useEffect(() => {\n    var _sections$query$secti;\n    if (query != null && ((_sections$query$secti = sections[query.sectionIndex]) == null ? void 0 : _sections$query$secti.type) !== query.sectionType) {\n      resetQuery();\n    }\n  }, [sections, query, resetQuery]);\n  React.useEffect(() => {\n    if (query != null) {\n      const timeout = setTimeout(() => resetQuery(), QUERY_LIFE_DURATION_MS);\n      return () => {\n        window.clearTimeout(timeout);\n      };\n    }\n    return () => {};\n  }, [query, resetQuery]);\n  const applyQuery = ({\n    keyPressed,\n    sectionIndex\n  }, getFirstSectionValueMatchingWithQuery, isValidQueryValue) => {\n    const cleanKeyPressed = keyPressed.toLowerCase();\n    const activeSection = sections[sectionIndex];\n\n    // The current query targets the section being editing\n    // We can try to concatenated value\n    if (query != null && (!isValidQueryValue || isValidQueryValue(query.value)) && query.sectionIndex === sectionIndex) {\n      const concatenatedQueryValue = `${query.value}${cleanKeyPressed}`;\n      const queryResponse = getFirstSectionValueMatchingWithQuery(concatenatedQueryValue, activeSection);\n      if (!isQueryResponseWithoutValue(queryResponse)) {\n        setQuery({\n          sectionIndex,\n          value: concatenatedQueryValue,\n          sectionType: activeSection.type\n        });\n        return queryResponse;\n      }\n    }\n    const queryResponse = getFirstSectionValueMatchingWithQuery(cleanKeyPressed, activeSection);\n    if (isQueryResponseWithoutValue(queryResponse) && !queryResponse.saveQuery) {\n      resetQuery();\n      return null;\n    }\n    setQuery({\n      sectionIndex,\n      value: cleanKeyPressed,\n      sectionType: activeSection.type\n    });\n    if (isQueryResponseWithoutValue(queryResponse)) {\n      return null;\n    }\n    return queryResponse;\n  };\n  const applyLetterEditing = params => {\n    const findMatchingOptions = (format, options, queryValue) => {\n      const matchingValues = options.filter(option => option.toLowerCase().startsWith(queryValue));\n      if (matchingValues.length === 0) {\n        return {\n          saveQuery: false\n        };\n      }\n      return {\n        sectionValue: matchingValues[0],\n        shouldGoToNextSection: matchingValues.length === 1\n      };\n    };\n    const testQueryOnFormatAndFallbackFormat = (queryValue, activeSection, fallbackFormat, formatFallbackValue) => {\n      const getOptions = format => getLetterEditingOptions(utils, timezone, activeSection.type, format);\n      if (activeSection.contentType === 'letter') {\n        return findMatchingOptions(activeSection.format, getOptions(activeSection.format), queryValue);\n      }\n\n      // When editing a digit-format month / weekDay and the user presses a letter,\n      // We can support the letter editing by using the letter-format month / weekDay and re-formatting the result.\n      // We just have to make sure that the default month / weekDay format is a letter format,\n      if (fallbackFormat && formatFallbackValue != null && getDateSectionConfigFromFormatToken(utils, fallbackFormat).contentType === 'letter') {\n        const fallbackOptions = getOptions(fallbackFormat);\n        const response = findMatchingOptions(fallbackFormat, fallbackOptions, queryValue);\n        if (isQueryResponseWithoutValue(response)) {\n          return {\n            saveQuery: false\n          };\n        }\n        return _extends({}, response, {\n          sectionValue: formatFallbackValue(response.sectionValue, fallbackOptions)\n        });\n      }\n      return {\n        saveQuery: false\n      };\n    };\n    const getFirstSectionValueMatchingWithQuery = (queryValue, activeSection) => {\n      switch (activeSection.type) {\n        case 'month':\n          {\n            const formatFallbackValue = fallbackValue => changeSectionValueFormat(utils, fallbackValue, utils.formats.month, activeSection.format);\n            return testQueryOnFormatAndFallbackFormat(queryValue, activeSection, utils.formats.month, formatFallbackValue);\n          }\n        case 'weekDay':\n          {\n            const formatFallbackValue = (fallbackValue, fallbackOptions) => fallbackOptions.indexOf(fallbackValue).toString();\n            return testQueryOnFormatAndFallbackFormat(queryValue, activeSection, utils.formats.weekday, formatFallbackValue);\n          }\n        case 'meridiem':\n          {\n            return testQueryOnFormatAndFallbackFormat(queryValue, activeSection);\n          }\n        default:\n          {\n            return {\n              saveQuery: false\n            };\n          }\n      }\n    };\n    return applyQuery(params, getFirstSectionValueMatchingWithQuery);\n  };\n  const applyNumericEditing = params => {\n    const getNewSectionValue = (queryValue, section) => {\n      const queryValueNumber = Number(`${queryValue}`);\n      const sectionBoundaries = sectionsValueBoundaries[section.type]({\n        currentDate: null,\n        format: section.format,\n        contentType: section.contentType\n      });\n      if (queryValueNumber > sectionBoundaries.maximum) {\n        return {\n          saveQuery: false\n        };\n      }\n\n      // If the user types `0` on a month section,\n      // It is below the minimum, but we want to store the `0` in the query,\n      // So that when he pressed `1`, it will store `01` and move to the next section.\n      if (queryValueNumber < sectionBoundaries.minimum) {\n        return {\n          saveQuery: true\n        };\n      }\n      const shouldGoToNextSection = Number(`${queryValue}0`) > sectionBoundaries.maximum || queryValue.length === sectionBoundaries.maximum.toString().length;\n      const newSectionValue = cleanDigitSectionValue(utils, timezone, queryValueNumber, sectionBoundaries, section);\n      return {\n        sectionValue: newSectionValue,\n        shouldGoToNextSection\n      };\n    };\n    const getFirstSectionValueMatchingWithQuery = (queryValue, activeSection) => {\n      if (activeSection.contentType === 'digit' || activeSection.contentType === 'digit-with-letter') {\n        return getNewSectionValue(queryValue, activeSection);\n      }\n\n      // When editing a letter-format month and the user presses a digit,\n      // We can support the numeric editing by using the digit-format month and re-formatting the result.\n      if (activeSection.type === 'month') {\n        const hasLeadingZerosInFormat = doesSectionFormatHaveLeadingZeros(utils, timezone, 'digit', 'month', 'MM');\n        const response = getNewSectionValue(queryValue, {\n          type: activeSection.type,\n          format: 'MM',\n          hasLeadingZerosInFormat,\n          hasLeadingZerosInInput: true,\n          contentType: 'digit',\n          maxLength: 2\n        });\n        if (isQueryResponseWithoutValue(response)) {\n          return response;\n        }\n        const formattedValue = changeSectionValueFormat(utils, response.sectionValue, 'MM', activeSection.format);\n        return _extends({}, response, {\n          sectionValue: formattedValue\n        });\n      }\n\n      // When editing a letter-format weekDay and the user presses a digit,\n      // We can support the numeric editing by returning the nth day in the week day array.\n      if (activeSection.type === 'weekDay') {\n        const response = getNewSectionValue(queryValue, activeSection);\n        if (isQueryResponseWithoutValue(response)) {\n          return response;\n        }\n        const formattedValue = getDaysInWeekStr(utils, timezone, activeSection.format)[Number(response.sectionValue) - 1];\n        return _extends({}, response, {\n          sectionValue: formattedValue\n        });\n      }\n      return {\n        saveQuery: false\n      };\n    };\n    return applyQuery(params, getFirstSectionValueMatchingWithQuery, queryValue => !Number.isNaN(Number(queryValue)));\n  };\n  const applyCharacterEditing = useEventCallback(params => {\n    const activeSection = sections[params.sectionIndex];\n    const isNumericEditing = params.keyPressed !== ' ' && !Number.isNaN(Number(params.keyPressed));\n    const response = isNumericEditing ? applyNumericEditing(params) : applyLetterEditing(params);\n    if (response == null) {\n      setTempAndroidValueStr(null);\n    } else {\n      updateSectionValue({\n        activeSection,\n        newSectionValue: response.sectionValue,\n        shouldGoToNextSection: response.shouldGoToNextSection\n      });\n    }\n  });\n  return {\n    applyCharacterEditing,\n    resetCharacterQuery: resetQuery\n  };\n};", "map": {"version": 3, "names": ["_extends", "React", "useEventCallback", "useUtils", "changeSectionValueFormat", "cleanDigitSectionValue", "doesSectionFormatHaveLeadingZeros", "getDateSectionConfigFromFormatToken", "getDaysInWeekStr", "getLetterEditingOptions", "QUERY_LIFE_DURATION_MS", "isQueryResponseWithoutValue", "response", "saveQuery", "useFieldCharacterEditing", "sections", "updateSectionValue", "sectionsValueBoundaries", "setTempAndroidValueStr", "timezone", "utils", "query", "<PERSON><PERSON><PERSON><PERSON>", "useState", "reset<PERSON><PERSON>y", "useEffect", "_sections$query$secti", "sectionIndex", "type", "sectionType", "timeout", "setTimeout", "window", "clearTimeout", "<PERSON><PERSON><PERSON><PERSON>", "keyPressed", "getFirstSectionValueMatchingWithQuery", "isValidQuery<PERSON>ue", "cleanKeyPressed", "toLowerCase", "activeSection", "value", "concatenatedQueryValue", "queryResponse", "applyLetterEditing", "params", "findMatchingOptions", "format", "options", "queryValue", "matchingV<PERSON>ues", "filter", "option", "startsWith", "length", "sectionValue", "shouldGoToNextSection", "testQueryOnFormatAndFallbackFormat", "fallbackFormat", "formatFallbackValue", "getOptions", "contentType", "fallbackOptions", "fallback<PERSON><PERSON><PERSON>", "formats", "month", "indexOf", "toString", "weekday", "applyNumericEditing", "getNewSectionValue", "section", "queryValueNumber", "Number", "sectionBoundaries", "currentDate", "maximum", "minimum", "newSectionValue", "hasLeadingZerosInFormat", "hasLeadingZerosInInput", "max<PERSON><PERSON><PERSON>", "formattedValue", "isNaN", "applyCharacterEditing", "isNumericEditing", "resetCharacterQuery"], "sources": ["C:/CMS/webapp/frontend/node_modules/@mui/x-date-pickers/internals/hooks/useField/useFieldCharacterEditing.js"], "sourcesContent": ["import _extends from \"@babel/runtime/helpers/esm/extends\";\nimport * as React from 'react';\nimport useEventCallback from '@mui/utils/useEventCallback';\nimport { useUtils } from '../useUtils';\nimport { changeSectionValueFormat, cleanDigitSectionValue, doesSectionFormatHaveLeadingZeros, getDateSectionConfigFromFormatToken, getDaysInWeekStr, getLetterEditingOptions } from './useField.utils';\n\n/**\n * The letter editing and the numeric editing each define a `CharacterEditingApplier`.\n * This function decides what the new section value should be and if the focus should switch to the next section.\n *\n * If it returns `null`, then the section value is not updated and the focus does not move.\n */\n\n/**\n * Function called by `applyQuery` which decides:\n * - what is the new section value ?\n * - should the query used to get this value be stored for the next key press ?\n *\n * If it returns `{ sectionValue: string; shouldGoToNextSection: boolean }`,\n * Then we store the query and update the section with the new value.\n *\n * If it returns `{ saveQuery: true` },\n * Then we store the query and don't update the section.\n *\n * If it returns `{ saveQuery: false },\n * Then we do nothing.\n */\n\nconst QUERY_LIFE_DURATION_MS = 5000;\nconst isQueryResponseWithoutValue = response => response.saveQuery != null;\n\n/**\n * Update the active section value when the user pressed a key that is not a navigation key (arrow key for example).\n * This hook has two main editing behaviors\n *\n * 1. The numeric editing when the user presses a digit\n * 2. The letter editing when the user presses another key\n */\nexport const useFieldCharacterEditing = ({\n  sections,\n  updateSectionValue,\n  sectionsValueBoundaries,\n  setTempAndroidValueStr,\n  timezone\n}) => {\n  const utils = useUtils();\n  const [query, setQuery] = React.useState(null);\n  const resetQuery = useEventCallback(() => setQuery(null));\n  React.useEffect(() => {\n    var _sections$query$secti;\n    if (query != null && ((_sections$query$secti = sections[query.sectionIndex]) == null ? void 0 : _sections$query$secti.type) !== query.sectionType) {\n      resetQuery();\n    }\n  }, [sections, query, resetQuery]);\n  React.useEffect(() => {\n    if (query != null) {\n      const timeout = setTimeout(() => resetQuery(), QUERY_LIFE_DURATION_MS);\n      return () => {\n        window.clearTimeout(timeout);\n      };\n    }\n    return () => {};\n  }, [query, resetQuery]);\n  const applyQuery = ({\n    keyPressed,\n    sectionIndex\n  }, getFirstSectionValueMatchingWithQuery, isValidQueryValue) => {\n    const cleanKeyPressed = keyPressed.toLowerCase();\n    const activeSection = sections[sectionIndex];\n\n    // The current query targets the section being editing\n    // We can try to concatenated value\n    if (query != null && (!isValidQueryValue || isValidQueryValue(query.value)) && query.sectionIndex === sectionIndex) {\n      const concatenatedQueryValue = `${query.value}${cleanKeyPressed}`;\n      const queryResponse = getFirstSectionValueMatchingWithQuery(concatenatedQueryValue, activeSection);\n      if (!isQueryResponseWithoutValue(queryResponse)) {\n        setQuery({\n          sectionIndex,\n          value: concatenatedQueryValue,\n          sectionType: activeSection.type\n        });\n        return queryResponse;\n      }\n    }\n    const queryResponse = getFirstSectionValueMatchingWithQuery(cleanKeyPressed, activeSection);\n    if (isQueryResponseWithoutValue(queryResponse) && !queryResponse.saveQuery) {\n      resetQuery();\n      return null;\n    }\n    setQuery({\n      sectionIndex,\n      value: cleanKeyPressed,\n      sectionType: activeSection.type\n    });\n    if (isQueryResponseWithoutValue(queryResponse)) {\n      return null;\n    }\n    return queryResponse;\n  };\n  const applyLetterEditing = params => {\n    const findMatchingOptions = (format, options, queryValue) => {\n      const matchingValues = options.filter(option => option.toLowerCase().startsWith(queryValue));\n      if (matchingValues.length === 0) {\n        return {\n          saveQuery: false\n        };\n      }\n      return {\n        sectionValue: matchingValues[0],\n        shouldGoToNextSection: matchingValues.length === 1\n      };\n    };\n    const testQueryOnFormatAndFallbackFormat = (queryValue, activeSection, fallbackFormat, formatFallbackValue) => {\n      const getOptions = format => getLetterEditingOptions(utils, timezone, activeSection.type, format);\n      if (activeSection.contentType === 'letter') {\n        return findMatchingOptions(activeSection.format, getOptions(activeSection.format), queryValue);\n      }\n\n      // When editing a digit-format month / weekDay and the user presses a letter,\n      // We can support the letter editing by using the letter-format month / weekDay and re-formatting the result.\n      // We just have to make sure that the default month / weekDay format is a letter format,\n      if (fallbackFormat && formatFallbackValue != null && getDateSectionConfigFromFormatToken(utils, fallbackFormat).contentType === 'letter') {\n        const fallbackOptions = getOptions(fallbackFormat);\n        const response = findMatchingOptions(fallbackFormat, fallbackOptions, queryValue);\n        if (isQueryResponseWithoutValue(response)) {\n          return {\n            saveQuery: false\n          };\n        }\n        return _extends({}, response, {\n          sectionValue: formatFallbackValue(response.sectionValue, fallbackOptions)\n        });\n      }\n      return {\n        saveQuery: false\n      };\n    };\n    const getFirstSectionValueMatchingWithQuery = (queryValue, activeSection) => {\n      switch (activeSection.type) {\n        case 'month':\n          {\n            const formatFallbackValue = fallbackValue => changeSectionValueFormat(utils, fallbackValue, utils.formats.month, activeSection.format);\n            return testQueryOnFormatAndFallbackFormat(queryValue, activeSection, utils.formats.month, formatFallbackValue);\n          }\n        case 'weekDay':\n          {\n            const formatFallbackValue = (fallbackValue, fallbackOptions) => fallbackOptions.indexOf(fallbackValue).toString();\n            return testQueryOnFormatAndFallbackFormat(queryValue, activeSection, utils.formats.weekday, formatFallbackValue);\n          }\n        case 'meridiem':\n          {\n            return testQueryOnFormatAndFallbackFormat(queryValue, activeSection);\n          }\n        default:\n          {\n            return {\n              saveQuery: false\n            };\n          }\n      }\n    };\n    return applyQuery(params, getFirstSectionValueMatchingWithQuery);\n  };\n  const applyNumericEditing = params => {\n    const getNewSectionValue = (queryValue, section) => {\n      const queryValueNumber = Number(`${queryValue}`);\n      const sectionBoundaries = sectionsValueBoundaries[section.type]({\n        currentDate: null,\n        format: section.format,\n        contentType: section.contentType\n      });\n      if (queryValueNumber > sectionBoundaries.maximum) {\n        return {\n          saveQuery: false\n        };\n      }\n\n      // If the user types `0` on a month section,\n      // It is below the minimum, but we want to store the `0` in the query,\n      // So that when he pressed `1`, it will store `01` and move to the next section.\n      if (queryValueNumber < sectionBoundaries.minimum) {\n        return {\n          saveQuery: true\n        };\n      }\n      const shouldGoToNextSection = Number(`${queryValue}0`) > sectionBoundaries.maximum || queryValue.length === sectionBoundaries.maximum.toString().length;\n      const newSectionValue = cleanDigitSectionValue(utils, timezone, queryValueNumber, sectionBoundaries, section);\n      return {\n        sectionValue: newSectionValue,\n        shouldGoToNextSection\n      };\n    };\n    const getFirstSectionValueMatchingWithQuery = (queryValue, activeSection) => {\n      if (activeSection.contentType === 'digit' || activeSection.contentType === 'digit-with-letter') {\n        return getNewSectionValue(queryValue, activeSection);\n      }\n\n      // When editing a letter-format month and the user presses a digit,\n      // We can support the numeric editing by using the digit-format month and re-formatting the result.\n      if (activeSection.type === 'month') {\n        const hasLeadingZerosInFormat = doesSectionFormatHaveLeadingZeros(utils, timezone, 'digit', 'month', 'MM');\n        const response = getNewSectionValue(queryValue, {\n          type: activeSection.type,\n          format: 'MM',\n          hasLeadingZerosInFormat,\n          hasLeadingZerosInInput: true,\n          contentType: 'digit',\n          maxLength: 2\n        });\n        if (isQueryResponseWithoutValue(response)) {\n          return response;\n        }\n        const formattedValue = changeSectionValueFormat(utils, response.sectionValue, 'MM', activeSection.format);\n        return _extends({}, response, {\n          sectionValue: formattedValue\n        });\n      }\n\n      // When editing a letter-format weekDay and the user presses a digit,\n      // We can support the numeric editing by returning the nth day in the week day array.\n      if (activeSection.type === 'weekDay') {\n        const response = getNewSectionValue(queryValue, activeSection);\n        if (isQueryResponseWithoutValue(response)) {\n          return response;\n        }\n        const formattedValue = getDaysInWeekStr(utils, timezone, activeSection.format)[Number(response.sectionValue) - 1];\n        return _extends({}, response, {\n          sectionValue: formattedValue\n        });\n      }\n      return {\n        saveQuery: false\n      };\n    };\n    return applyQuery(params, getFirstSectionValueMatchingWithQuery, queryValue => !Number.isNaN(Number(queryValue)));\n  };\n  const applyCharacterEditing = useEventCallback(params => {\n    const activeSection = sections[params.sectionIndex];\n    const isNumericEditing = params.keyPressed !== ' ' && !Number.isNaN(Number(params.keyPressed));\n    const response = isNumericEditing ? applyNumericEditing(params) : applyLetterEditing(params);\n    if (response == null) {\n      setTempAndroidValueStr(null);\n    } else {\n      updateSectionValue({\n        activeSection,\n        newSectionValue: response.sectionValue,\n        shouldGoToNextSection: response.shouldGoToNextSection\n      });\n    }\n  });\n  return {\n    applyCharacterEditing,\n    resetCharacterQuery: resetQuery\n  };\n};"], "mappings": "AAAA,OAAOA,QAAQ,MAAM,oCAAoC;AACzD,OAAO,KAAKC,KAAK,MAAM,OAAO;AAC9B,OAAOC,gBAAgB,MAAM,6BAA6B;AAC1D,SAASC,QAAQ,QAAQ,aAAa;AACtC,SAASC,wBAAwB,EAAEC,sBAAsB,EAAEC,iCAAiC,EAAEC,mCAAmC,EAAEC,gBAAgB,EAAEC,uBAAuB,QAAQ,kBAAkB;;AAEtM;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA,MAAMC,sBAAsB,GAAG,IAAI;AACnC,MAAMC,2BAA2B,GAAGC,QAAQ,IAAIA,QAAQ,CAACC,SAAS,IAAI,IAAI;;AAE1E;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO,MAAMC,wBAAwB,GAAGA,CAAC;EACvCC,QAAQ;EACRC,kBAAkB;EAClBC,uBAAuB;EACvBC,sBAAsB;EACtBC;AACF,CAAC,KAAK;EACJ,MAAMC,KAAK,GAAGjB,QAAQ,CAAC,CAAC;EACxB,MAAM,CAACkB,KAAK,EAAEC,QAAQ,CAAC,GAAGrB,KAAK,CAACsB,QAAQ,CAAC,IAAI,CAAC;EAC9C,MAAMC,UAAU,GAAGtB,gBAAgB,CAAC,MAAMoB,QAAQ,CAAC,IAAI,CAAC,CAAC;EACzDrB,KAAK,CAACwB,SAAS,CAAC,MAAM;IACpB,IAAIC,qBAAqB;IACzB,IAAIL,KAAK,IAAI,IAAI,IAAI,CAAC,CAACK,qBAAqB,GAAGX,QAAQ,CAACM,KAAK,CAACM,YAAY,CAAC,KAAK,IAAI,GAAG,KAAK,CAAC,GAAGD,qBAAqB,CAACE,IAAI,MAAMP,KAAK,CAACQ,WAAW,EAAE;MACjJL,UAAU,CAAC,CAAC;IACd;EACF,CAAC,EAAE,CAACT,QAAQ,EAAEM,KAAK,EAAEG,UAAU,CAAC,CAAC;EACjCvB,KAAK,CAACwB,SAAS,CAAC,MAAM;IACpB,IAAIJ,KAAK,IAAI,IAAI,EAAE;MACjB,MAAMS,OAAO,GAAGC,UAAU,CAAC,MAAMP,UAAU,CAAC,CAAC,EAAEd,sBAAsB,CAAC;MACtE,OAAO,MAAM;QACXsB,MAAM,CAACC,YAAY,CAACH,OAAO,CAAC;MAC9B,CAAC;IACH;IACA,OAAO,MAAM,CAAC,CAAC;EACjB,CAAC,EAAE,CAACT,KAAK,EAAEG,UAAU,CAAC,CAAC;EACvB,MAAMU,UAAU,GAAGA,CAAC;IAClBC,UAAU;IACVR;EACF,CAAC,EAAES,qCAAqC,EAAEC,iBAAiB,KAAK;IAC9D,MAAMC,eAAe,GAAGH,UAAU,CAACI,WAAW,CAAC,CAAC;IAChD,MAAMC,aAAa,GAAGzB,QAAQ,CAACY,YAAY,CAAC;;IAE5C;IACA;IACA,IAAIN,KAAK,IAAI,IAAI,KAAK,CAACgB,iBAAiB,IAAIA,iBAAiB,CAAChB,KAAK,CAACoB,KAAK,CAAC,CAAC,IAAIpB,KAAK,CAACM,YAAY,KAAKA,YAAY,EAAE;MAClH,MAAMe,sBAAsB,GAAG,GAAGrB,KAAK,CAACoB,KAAK,GAAGH,eAAe,EAAE;MACjE,MAAMK,aAAa,GAAGP,qCAAqC,CAACM,sBAAsB,EAAEF,aAAa,CAAC;MAClG,IAAI,CAAC7B,2BAA2B,CAACgC,aAAa,CAAC,EAAE;QAC/CrB,QAAQ,CAAC;UACPK,YAAY;UACZc,KAAK,EAAEC,sBAAsB;UAC7Bb,WAAW,EAAEW,aAAa,CAACZ;QAC7B,CAAC,CAAC;QACF,OAAOe,aAAa;MACtB;IACF;IACA,MAAMA,aAAa,GAAGP,qCAAqC,CAACE,eAAe,EAAEE,aAAa,CAAC;IAC3F,IAAI7B,2BAA2B,CAACgC,aAAa,CAAC,IAAI,CAACA,aAAa,CAAC9B,SAAS,EAAE;MAC1EW,UAAU,CAAC,CAAC;MACZ,OAAO,IAAI;IACb;IACAF,QAAQ,CAAC;MACPK,YAAY;MACZc,KAAK,EAAEH,eAAe;MACtBT,WAAW,EAAEW,aAAa,CAACZ;IAC7B,CAAC,CAAC;IACF,IAAIjB,2BAA2B,CAACgC,aAAa,CAAC,EAAE;MAC9C,OAAO,IAAI;IACb;IACA,OAAOA,aAAa;EACtB,CAAC;EACD,MAAMC,kBAAkB,GAAGC,MAAM,IAAI;IACnC,MAAMC,mBAAmB,GAAGA,CAACC,MAAM,EAAEC,OAAO,EAAEC,UAAU,KAAK;MAC3D,MAAMC,cAAc,GAAGF,OAAO,CAACG,MAAM,CAACC,MAAM,IAAIA,MAAM,CAACb,WAAW,CAAC,CAAC,CAACc,UAAU,CAACJ,UAAU,CAAC,CAAC;MAC5F,IAAIC,cAAc,CAACI,MAAM,KAAK,CAAC,EAAE;QAC/B,OAAO;UACLzC,SAAS,EAAE;QACb,CAAC;MACH;MACA,OAAO;QACL0C,YAAY,EAAEL,cAAc,CAAC,CAAC,CAAC;QAC/BM,qBAAqB,EAAEN,cAAc,CAACI,MAAM,KAAK;MACnD,CAAC;IACH,CAAC;IACD,MAAMG,kCAAkC,GAAGA,CAACR,UAAU,EAAET,aAAa,EAAEkB,cAAc,EAAEC,mBAAmB,KAAK;MAC7G,MAAMC,UAAU,GAAGb,MAAM,IAAItC,uBAAuB,CAACW,KAAK,EAAED,QAAQ,EAAEqB,aAAa,CAACZ,IAAI,EAAEmB,MAAM,CAAC;MACjG,IAAIP,aAAa,CAACqB,WAAW,KAAK,QAAQ,EAAE;QAC1C,OAAOf,mBAAmB,CAACN,aAAa,CAACO,MAAM,EAAEa,UAAU,CAACpB,aAAa,CAACO,MAAM,CAAC,EAAEE,UAAU,CAAC;MAChG;;MAEA;MACA;MACA;MACA,IAAIS,cAAc,IAAIC,mBAAmB,IAAI,IAAI,IAAIpD,mCAAmC,CAACa,KAAK,EAAEsC,cAAc,CAAC,CAACG,WAAW,KAAK,QAAQ,EAAE;QACxI,MAAMC,eAAe,GAAGF,UAAU,CAACF,cAAc,CAAC;QAClD,MAAM9C,QAAQ,GAAGkC,mBAAmB,CAACY,cAAc,EAAEI,eAAe,EAAEb,UAAU,CAAC;QACjF,IAAItC,2BAA2B,CAACC,QAAQ,CAAC,EAAE;UACzC,OAAO;YACLC,SAAS,EAAE;UACb,CAAC;QACH;QACA,OAAOb,QAAQ,CAAC,CAAC,CAAC,EAAEY,QAAQ,EAAE;UAC5B2C,YAAY,EAAEI,mBAAmB,CAAC/C,QAAQ,CAAC2C,YAAY,EAAEO,eAAe;QAC1E,CAAC,CAAC;MACJ;MACA,OAAO;QACLjD,SAAS,EAAE;MACb,CAAC;IACH,CAAC;IACD,MAAMuB,qCAAqC,GAAGA,CAACa,UAAU,EAAET,aAAa,KAAK;MAC3E,QAAQA,aAAa,CAACZ,IAAI;QACxB,KAAK,OAAO;UACV;YACE,MAAM+B,mBAAmB,GAAGI,aAAa,IAAI3D,wBAAwB,CAACgB,KAAK,EAAE2C,aAAa,EAAE3C,KAAK,CAAC4C,OAAO,CAACC,KAAK,EAAEzB,aAAa,CAACO,MAAM,CAAC;YACtI,OAAOU,kCAAkC,CAACR,UAAU,EAAET,aAAa,EAAEpB,KAAK,CAAC4C,OAAO,CAACC,KAAK,EAAEN,mBAAmB,CAAC;UAChH;QACF,KAAK,SAAS;UACZ;YACE,MAAMA,mBAAmB,GAAGA,CAACI,aAAa,EAAED,eAAe,KAAKA,eAAe,CAACI,OAAO,CAACH,aAAa,CAAC,CAACI,QAAQ,CAAC,CAAC;YACjH,OAAOV,kCAAkC,CAACR,UAAU,EAAET,aAAa,EAAEpB,KAAK,CAAC4C,OAAO,CAACI,OAAO,EAAET,mBAAmB,CAAC;UAClH;QACF,KAAK,UAAU;UACb;YACE,OAAOF,kCAAkC,CAACR,UAAU,EAAET,aAAa,CAAC;UACtE;QACF;UACE;YACE,OAAO;cACL3B,SAAS,EAAE;YACb,CAAC;UACH;MACJ;IACF,CAAC;IACD,OAAOqB,UAAU,CAACW,MAAM,EAAET,qCAAqC,CAAC;EAClE,CAAC;EACD,MAAMiC,mBAAmB,GAAGxB,MAAM,IAAI;IACpC,MAAMyB,kBAAkB,GAAGA,CAACrB,UAAU,EAAEsB,OAAO,KAAK;MAClD,MAAMC,gBAAgB,GAAGC,MAAM,CAAC,GAAGxB,UAAU,EAAE,CAAC;MAChD,MAAMyB,iBAAiB,GAAGzD,uBAAuB,CAACsD,OAAO,CAAC3C,IAAI,CAAC,CAAC;QAC9D+C,WAAW,EAAE,IAAI;QACjB5B,MAAM,EAAEwB,OAAO,CAACxB,MAAM;QACtBc,WAAW,EAAEU,OAAO,CAACV;MACvB,CAAC,CAAC;MACF,IAAIW,gBAAgB,GAAGE,iBAAiB,CAACE,OAAO,EAAE;QAChD,OAAO;UACL/D,SAAS,EAAE;QACb,CAAC;MACH;;MAEA;MACA;MACA;MACA,IAAI2D,gBAAgB,GAAGE,iBAAiB,CAACG,OAAO,EAAE;QAChD,OAAO;UACLhE,SAAS,EAAE;QACb,CAAC;MACH;MACA,MAAM2C,qBAAqB,GAAGiB,MAAM,CAAC,GAAGxB,UAAU,GAAG,CAAC,GAAGyB,iBAAiB,CAACE,OAAO,IAAI3B,UAAU,CAACK,MAAM,KAAKoB,iBAAiB,CAACE,OAAO,CAACT,QAAQ,CAAC,CAAC,CAACb,MAAM;MACvJ,MAAMwB,eAAe,GAAGzE,sBAAsB,CAACe,KAAK,EAAED,QAAQ,EAAEqD,gBAAgB,EAAEE,iBAAiB,EAAEH,OAAO,CAAC;MAC7G,OAAO;QACLhB,YAAY,EAAEuB,eAAe;QAC7BtB;MACF,CAAC;IACH,CAAC;IACD,MAAMpB,qCAAqC,GAAGA,CAACa,UAAU,EAAET,aAAa,KAAK;MAC3E,IAAIA,aAAa,CAACqB,WAAW,KAAK,OAAO,IAAIrB,aAAa,CAACqB,WAAW,KAAK,mBAAmB,EAAE;QAC9F,OAAOS,kBAAkB,CAACrB,UAAU,EAAET,aAAa,CAAC;MACtD;;MAEA;MACA;MACA,IAAIA,aAAa,CAACZ,IAAI,KAAK,OAAO,EAAE;QAClC,MAAMmD,uBAAuB,GAAGzE,iCAAiC,CAACc,KAAK,EAAED,QAAQ,EAAE,OAAO,EAAE,OAAO,EAAE,IAAI,CAAC;QAC1G,MAAMP,QAAQ,GAAG0D,kBAAkB,CAACrB,UAAU,EAAE;UAC9CrB,IAAI,EAAEY,aAAa,CAACZ,IAAI;UACxBmB,MAAM,EAAE,IAAI;UACZgC,uBAAuB;UACvBC,sBAAsB,EAAE,IAAI;UAC5BnB,WAAW,EAAE,OAAO;UACpBoB,SAAS,EAAE;QACb,CAAC,CAAC;QACF,IAAItE,2BAA2B,CAACC,QAAQ,CAAC,EAAE;UACzC,OAAOA,QAAQ;QACjB;QACA,MAAMsE,cAAc,GAAG9E,wBAAwB,CAACgB,KAAK,EAAER,QAAQ,CAAC2C,YAAY,EAAE,IAAI,EAAEf,aAAa,CAACO,MAAM,CAAC;QACzG,OAAO/C,QAAQ,CAAC,CAAC,CAAC,EAAEY,QAAQ,EAAE;UAC5B2C,YAAY,EAAE2B;QAChB,CAAC,CAAC;MACJ;;MAEA;MACA;MACA,IAAI1C,aAAa,CAACZ,IAAI,KAAK,SAAS,EAAE;QACpC,MAAMhB,QAAQ,GAAG0D,kBAAkB,CAACrB,UAAU,EAAET,aAAa,CAAC;QAC9D,IAAI7B,2BAA2B,CAACC,QAAQ,CAAC,EAAE;UACzC,OAAOA,QAAQ;QACjB;QACA,MAAMsE,cAAc,GAAG1E,gBAAgB,CAACY,KAAK,EAAED,QAAQ,EAAEqB,aAAa,CAACO,MAAM,CAAC,CAAC0B,MAAM,CAAC7D,QAAQ,CAAC2C,YAAY,CAAC,GAAG,CAAC,CAAC;QACjH,OAAOvD,QAAQ,CAAC,CAAC,CAAC,EAAEY,QAAQ,EAAE;UAC5B2C,YAAY,EAAE2B;QAChB,CAAC,CAAC;MACJ;MACA,OAAO;QACLrE,SAAS,EAAE;MACb,CAAC;IACH,CAAC;IACD,OAAOqB,UAAU,CAACW,MAAM,EAAET,qCAAqC,EAAEa,UAAU,IAAI,CAACwB,MAAM,CAACU,KAAK,CAACV,MAAM,CAACxB,UAAU,CAAC,CAAC,CAAC;EACnH,CAAC;EACD,MAAMmC,qBAAqB,GAAGlF,gBAAgB,CAAC2C,MAAM,IAAI;IACvD,MAAML,aAAa,GAAGzB,QAAQ,CAAC8B,MAAM,CAAClB,YAAY,CAAC;IACnD,MAAM0D,gBAAgB,GAAGxC,MAAM,CAACV,UAAU,KAAK,GAAG,IAAI,CAACsC,MAAM,CAACU,KAAK,CAACV,MAAM,CAAC5B,MAAM,CAACV,UAAU,CAAC,CAAC;IAC9F,MAAMvB,QAAQ,GAAGyE,gBAAgB,GAAGhB,mBAAmB,CAACxB,MAAM,CAAC,GAAGD,kBAAkB,CAACC,MAAM,CAAC;IAC5F,IAAIjC,QAAQ,IAAI,IAAI,EAAE;MACpBM,sBAAsB,CAAC,IAAI,CAAC;IAC9B,CAAC,MAAM;MACLF,kBAAkB,CAAC;QACjBwB,aAAa;QACbsC,eAAe,EAAElE,QAAQ,CAAC2C,YAAY;QACtCC,qBAAqB,EAAE5C,QAAQ,CAAC4C;MAClC,CAAC,CAAC;IACJ;EACF,CAAC,CAAC;EACF,OAAO;IACL4B,qBAAqB;IACrBE,mBAAmB,EAAE9D;EACvB,CAAC;AACH,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}