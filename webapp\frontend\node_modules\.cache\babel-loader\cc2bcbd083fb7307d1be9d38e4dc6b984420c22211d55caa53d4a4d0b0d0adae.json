{"ast": null, "code": "var _jsxFileName = \"C:\\\\CMS\\\\webapp\\\\frontend\\\\src\\\\components\\\\comande\\\\ComandeListRivoluzionato.js\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect } from 'react';\nimport { useSearchParams } from 'react-router-dom';\nimport { Box, Typography, Button, Paper, Chip, Dialog, DialogTitle, DialogContent, DialogActions, TextField, Alert, CircularProgress, List, ListItem, ListItemText, Stack, MenuItem, Divider } from '@mui/material';\nimport { Add as AddIcon, Assignment as AssignIcon, Person as PersonIcon, CheckCircle as CheckCircleIcon, Verified as VerifiedIcon, People as PeopleIcon, Construction as ConstructionIcon } from '@mui/icons-material';\nimport comandeService from '../../services/comandeService';\nimport responsabiliService from '../../services/responsabiliService';\nimport CreaComandaConCavi from './CreaComandaConCavi';\nimport ResponsabiliListPopup from './ResponsabiliListPopup';\nimport ComandeListTable from './ComandeListTable';\nimport InserimentoMetriDialog from './InserimentoMetriDialog';\nimport CollegamentiDialog from './CollegamentiDialog';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst ComandeListRivoluzionato = ({\n  cantiereId,\n  cantiereName\n}) => {\n  _s();\n  // Hook per gestire i parametri URL\n  const [searchParams, setSearchParams] = useSearchParams();\n\n  // Stati principali - Responsabili come elemento principale\n  const [loading, setLoading] = useState(true);\n  const [error, setError] = useState(null);\n  const [searchingComanda, setSearchingComanda] = useState(null);\n\n  // Stati comande\n  const [statistiche, setStatistiche] = useState(null);\n  const [allComande, setAllComande] = useState([]);\n  const [loadingComande, setLoadingComande] = useState(false);\n\n  // Stati per popup responsabili\n  const [openResponsabiliPopup, setOpenResponsabiliPopup] = useState(false);\n  const [openCreaConCavi, setOpenCreaConCavi] = useState(false);\n\n  // Stati responsabili\n  const [responsabili, setResponsabili] = useState([]);\n  const [loadingResponsabili, setLoadingResponsabili] = useState(false);\n  const [comandePerResponsabile, setComandePerResponsabile] = useState({});\n  const [openResponsabileDialog, setOpenResponsabileDialog] = useState(false);\n  const [dialogModeResponsabile, setDialogModeResponsabile] = useState('create');\n  const [selectedResponsabile, setSelectedResponsabile] = useState(null);\n  const [formDataResponsabile, setFormDataResponsabile] = useState({\n    nome_responsabile: '',\n    email: '',\n    telefono: ''\n  });\n\n  // Stati per dialog comande\n  const [openComandaDialog, setOpenComandaDialog] = useState(false);\n  const [selectedComanda, setSelectedComanda] = useState(null);\n  const [formDataComanda, setFormDataComanda] = useState({\n    tipo_comanda: 'POSA',\n    descrizione: '',\n    responsabile: '',\n    data_scadenza: '',\n    note_capo_cantiere: ''\n  });\n\n  // Stati per dialog inserimento metri\n  const [openInserimentoMetri, setOpenInserimentoMetri] = useState(false);\n  const [comandaPerMetri, setComandaPerMetri] = useState(null);\n\n  // Stati per dialog collegamenti\n  const [openCollegamenti, setOpenCollegamenti] = useState(false);\n  const [comandaPerCollegamenti, setComandaPerCollegamenti] = useState(null);\n  const loadComande = async () => {\n    try {\n      setLoadingComande(true);\n      console.log('🔄 Caricamento comande per cantiere:', cantiereId);\n      const startTime = performance.now();\n      const comandeData = await comandeService.getComande(cantiereId);\n\n      // Gestisci diversi formati di risposta\n      let comandeArray = [];\n      if (Array.isArray(comandeData)) {\n        comandeArray = comandeData;\n      } else if (comandeData && Array.isArray(comandeData.comande)) {\n        comandeArray = comandeData.comande;\n      } else if (comandeData && Array.isArray(comandeData.data)) {\n        comandeArray = comandeData.data;\n      }\n      const endTime = performance.now();\n      console.log(`✅ ${comandeArray.length} comande caricate in ${(endTime - startTime).toFixed(2)}ms`);\n      setAllComande(comandeArray);\n    } catch (err) {\n      console.error('❌ Errore nel caricamento comande:', err);\n      setError('Errore nel caricamento delle comande');\n      setAllComande([]);\n    } finally {\n      setLoadingComande(false);\n    }\n  };\n  const loadStatistiche = async () => {\n    try {\n      console.log('🔄 Caricamento statistiche per cantiere:', cantiereId);\n      const startTime = performance.now();\n      const stats = await comandeService.getStatisticheComande(cantiereId);\n      const endTime = performance.now();\n      console.log(`📊 Statistiche caricate in ${(endTime - startTime).toFixed(2)}ms:`, stats);\n      setStatistiche(stats);\n    } catch (err) {\n      var _err$response;\n      console.error('❌ Errore nel caricamento delle statistiche:', err);\n      console.error('❌ Dettagli errore:', ((_err$response = err.response) === null || _err$response === void 0 ? void 0 : _err$response.data) || err.message);\n      // Imposta statistiche vuote in caso di errore\n      setStatistiche({\n        responsabili_attivi: 0,\n        totale_comande: 0,\n        comande_create: 0,\n        comande_in_corso: 0,\n        comande_completate: 0,\n        comande_annullate: 0\n      });\n    }\n  };\n  const loadResponsabili = async () => {\n    try {\n      setLoadingResponsabili(true);\n      setError(null);\n      console.log('🔄 Caricamento responsabili per cantiere:', cantiereId);\n      const startTime = performance.now();\n      const data = await responsabiliService.getResponsabiliCantiere(cantiereId);\n      const endTime = performance.now();\n      console.log(`✅ ${(data || []).length} responsabili caricati in ${(endTime - startTime).toFixed(2)}ms`);\n      setResponsabili(data || []);\n      return data || [];\n    } catch (err) {\n      var _err$response2, _err$response2$data;\n      console.error('❌ Errore nel caricamento dei responsabili:', err);\n      const errorMessage = ((_err$response2 = err.response) === null || _err$response2 === void 0 ? void 0 : (_err$response2$data = _err$response2.data) === null || _err$response2$data === void 0 ? void 0 : _err$response2$data.detail) || err.message || 'Errore nel caricamento dei responsabili';\n      setError(`Errore nel caricamento dei responsabili: ${errorMessage}`);\n      setResponsabili([]);\n      return [];\n    } finally {\n      setLoadingResponsabili(false);\n    }\n  };\n\n  // Caricamento ottimizzato - Tutto in parallelo\n  useEffect(() => {\n    const initializeData = async () => {\n      if (cantiereId) {\n        setLoading(true);\n        console.log('🚀 Inizializzazione dati cantiere:', cantiereId);\n        const totalStartTime = performance.now();\n        try {\n          // OTTIMIZZAZIONE: Carica tutto in parallelo per massimizzare le performance\n          const [responsabiliData, comandeData, statisticheData] = await Promise.all([responsabiliService.getResponsabiliCantiere(cantiereId), comandeService.getComande(cantiereId), comandeService.getStatisticheComande(cantiereId)]);\n\n          // Imposta i dati\n          setResponsabili(responsabiliData || []);\n\n          // Gestisci formato comande\n          let comandeArray = [];\n          if (Array.isArray(comandeData)) {\n            comandeArray = comandeData;\n          } else if (comandeData && Array.isArray(comandeData.comande)) {\n            comandeArray = comandeData.comande;\n          } else if (comandeData && Array.isArray(comandeData.data)) {\n            comandeArray = comandeData.data;\n          }\n          setAllComande(comandeArray);\n\n          // Raggruppa comande per responsabile (senza chiamate API aggiuntive)\n          const comandeMap = {};\n          (responsabiliData || []).forEach(responsabile => {\n            comandeMap[responsabile.id_responsabile] = [];\n          });\n          comandeArray.forEach(comanda => {\n            const responsabile = (responsabiliData || []).find(r => r.nome_responsabile === comanda.responsabile);\n            if (responsabile) {\n              comandeMap[responsabile.id_responsabile].push(comanda);\n            }\n          });\n          setComandePerResponsabile(comandeMap);\n\n          // Imposta statistiche\n          setStatistiche(statisticheData || {\n            responsabili_attivi: 0,\n            totale_comande: 0,\n            comande_create: 0,\n            comande_in_corso: 0,\n            comande_completate: 0,\n            comande_annullate: 0\n          });\n          const totalEndTime = performance.now();\n          console.log(`✅ Inizializzazione completata in ${(totalEndTime - totalStartTime).toFixed(2)}ms`);\n          console.log(`📊 Dati caricati: ${(responsabiliData || []).length} responsabili, ${comandeArray.length} comande`);\n        } catch (err) {\n          console.error('❌ Errore nel caricamento iniziale:', err);\n          setError('Errore nel caricamento dei dati');\n          // Imposta valori di fallback\n          setResponsabili([]);\n          setAllComande([]);\n          setComandePerResponsabile({});\n          setStatistiche({\n            responsabili_attivi: 0,\n            totale_comande: 0,\n            comande_create: 0,\n            comande_in_corso: 0,\n            comande_completate: 0,\n            comande_annullate: 0\n          });\n        } finally {\n          setLoading(false);\n          setLoadingComande(false);\n          setLoadingResponsabili(false);\n        }\n      }\n    };\n    initializeData();\n  }, [cantiereId]); // eslint-disable-line react-hooks/exhaustive-deps\n\n  // Gestione parametro URL per aprire comanda specifica\n  useEffect(() => {\n    const comandaParam = searchParams.get('comanda');\n    console.log('🔍 Controllo parametro URL comanda:', comandaParam);\n    console.log('📊 Stato dati:', {\n      responsabili: responsabili.length,\n      comandePerResponsabile: Object.keys(comandePerResponsabile).length,\n      loading,\n      loadingResponsabili\n    });\n\n    // Imposta lo stato di ricerca\n    if (comandaParam && comandaParam !== searchingComanda) {\n      setSearchingComanda(comandaParam);\n    }\n    if (comandaParam && responsabili.length > 0 && Object.keys(comandePerResponsabile).length > 0) {\n      console.log('🔎 Ricerca comanda tra i responsabili...');\n\n      // Cerca la comanda tra tutti i responsabili\n      let comandaTrovata = null;\n      for (const responsabile of responsabili) {\n        const comandeResp = comandePerResponsabile[responsabile.id_responsabile] || [];\n        console.log(`📋 Responsabile ${responsabile.nome_responsabile}: ${comandeResp.length} comande`);\n        comandaTrovata = comandeResp.find(c => c.codice_comanda === comandaParam);\n        if (comandaTrovata) {\n          console.log('✅ Comanda trovata:', comandaTrovata);\n          break;\n        }\n      }\n      if (comandaTrovata) {\n        console.log('🎯 Apertura automatica comanda da URL:', comandaParam);\n        setSearchingComanda(null); // Rimuovi lo stato di ricerca\n\n        // Per comande di workflow (POSA, COLLEGAMENTO_PARTENZA, COLLEGAMENTO_ARRIVO) provenienti da visualizza cavi,\n        // apri l'inserimento dati specifico. Per altre comande, apri il dialog di modifica generale\n        const isWorkflowCommand = ['POSA', 'COLLEGAMENTO_PARTENZA', 'COLLEGAMENTO_ARRIVO'].includes(comandaTrovata.tipo_comanda);\n        if (isWorkflowCommand) {\n          console.log(`📏 Apertura gestione per comanda ${comandaTrovata.tipo_comanda} da visualizza cavi:`, comandaParam);\n          handleOpenInserimentoMetri(comandaTrovata);\n        } else {\n          console.log('✏️ Apertura modifica per comanda non-workflow:', comandaParam);\n          handleOpenComandaDialog(comandaTrovata);\n        }\n\n        // Rimuovi il parametro dall'URL per evitare riaperture\n        setTimeout(() => {\n          setSearchParams(prev => {\n            const newParams = new URLSearchParams(prev);\n            newParams.delete('comanda');\n            return newParams;\n          });\n        }, 100);\n      } else {\n        console.warn('⚠️ Comanda non trovata:', comandaParam);\n        console.log('📋 Comande disponibili:');\n        responsabili.forEach(resp => {\n          const comande = comandePerResponsabile[resp.id_responsabile] || [];\n          comande.forEach(cmd => {\n            console.log(`  - ${cmd.codice_comanda} (${resp.nome_responsabile})`);\n          });\n        });\n\n        // Se non troviamo la comanda, potrebbe essere che i dati non sono completi\n        // Riprova dopo un breve delay\n        if (!loading && !loadingResponsabili) {\n          console.log('🔄 Tentativo di ricaricamento dati...');\n          setTimeout(() => {\n            loadResponsabili();\n          }, 500);\n        }\n      }\n    } else if (comandaParam) {\n      console.log('⏳ Dati non ancora caricati, attendo...');\n\n      // Se abbiamo un parametro ma i dati non sono caricati, forza il caricamento\n      if (!loading && !loadingResponsabili && responsabili.length === 0) {\n        console.log('🚀 Forzatura caricamento responsabili...');\n        loadResponsabili();\n      }\n    }\n  }, [searchParams, responsabili, comandePerResponsabile, loading, loadingResponsabili]); // eslint-disable-line react-hooks/exhaustive-deps\n\n  const loadComandePerResponsabili = async responsabiliList => {\n    try {\n      console.log('🚀 Caricamento comande per responsabili ottimizzato...');\n      const startTime = performance.now();\n\n      // OTTIMIZZAZIONE: Carica tutte le comande una sola volta invece di fare N chiamate\n      const allComande = await comandeService.getComande(cantiereId);\n\n      // Gestisci diversi formati di risposta\n      let comandeArray = [];\n      if (Array.isArray(allComande)) {\n        comandeArray = allComande;\n      } else if (allComande && Array.isArray(allComande.comande)) {\n        comandeArray = allComande.comande;\n      } else if (allComande && Array.isArray(allComande.data)) {\n        comandeArray = allComande.data;\n      }\n\n      // Raggruppa le comande per responsabile\n      const comandeMap = {};\n\n      // Inizializza tutti i responsabili con array vuoto\n      responsabiliList.forEach(responsabile => {\n        comandeMap[responsabile.id_responsabile] = [];\n      });\n\n      // Raggruppa le comande per responsabile\n      comandeArray.forEach(comanda => {\n        const responsabile = responsabiliList.find(r => r.nome_responsabile === comanda.responsabile);\n        if (responsabile) {\n          comandeMap[responsabile.id_responsabile].push(comanda);\n        }\n      });\n      const endTime = performance.now();\n      console.log(`✅ Comande caricate in ${(endTime - startTime).toFixed(2)}ms (1 chiamata invece di ${responsabiliList.length})`);\n      setComandePerResponsabile(comandeMap);\n    } catch (err) {\n      console.error('❌ Errore nel caricamento delle comande:', err);\n      // Fallback: inizializza con array vuoti\n      const comandeMap = {};\n      responsabiliList.forEach(responsabile => {\n        comandeMap[responsabile.id_responsabile] = [];\n      });\n      setComandePerResponsabile(comandeMap);\n    }\n  };\n\n  // Gestione responsabili\n  const handleOpenResponsabileDialog = (mode, responsabile = null) => {\n    setDialogModeResponsabile(mode);\n    setSelectedResponsabile(responsabile);\n    if (mode === 'edit' && responsabile) {\n      setFormDataResponsabile({\n        nome_responsabile: responsabile.nome_responsabile || '',\n        email: responsabile.email || '',\n        telefono: responsabile.telefono || ''\n      });\n    } else {\n      setFormDataResponsabile({\n        nome_responsabile: '',\n        email: '',\n        telefono: ''\n      });\n    }\n    setOpenResponsabileDialog(true);\n  };\n  const handleCloseResponsabileDialog = () => {\n    setOpenResponsabileDialog(false);\n    setSelectedResponsabile(null);\n    setError(null);\n  };\n  const handleSubmitResponsabile = async () => {\n    try {\n      setError(null);\n      if (!formDataResponsabile.nome_responsabile.trim()) {\n        setError('Il nome del responsabile è obbligatorio');\n        return;\n      }\n      if (!formDataResponsabile.email && !formDataResponsabile.telefono) {\n        setError('Almeno uno tra email e telefono deve essere specificato');\n        return;\n      }\n      if (dialogModeResponsabile === 'create') {\n        await responsabiliService.createResponsabile(cantiereId, formDataResponsabile);\n      } else if (dialogModeResponsabile === 'edit') {\n        await responsabiliService.updateResponsabile(selectedResponsabile.id_responsabile, formDataResponsabile);\n      }\n      handleCloseResponsabileDialog();\n      await loadResponsabili();\n      await loadComande();\n    } catch (err) {\n      console.error('Errore nel salvataggio:', err);\n      setError(err.detail || 'Errore nel salvataggio del responsabile');\n    }\n  };\n  const handleDeleteResponsabile = async idResponsabile => {\n    if (!window.confirm('Sei sicuro di voler eliminare questo responsabile?')) {\n      return;\n    }\n    try {\n      await responsabiliService.deleteResponsabile(idResponsabile);\n      await loadResponsabili();\n      await loadComande();\n    } catch (err) {\n      console.error('Errore nell\\'eliminazione:', err);\n      setError('Errore nell\\'eliminazione del responsabile');\n    }\n  };\n\n  // Gestione comande\n  const handleOpenComandaDialog = comanda => {\n    setSelectedComanda(comanda);\n    if (comanda) {\n      setFormDataComanda({\n        tipo_comanda: comanda.tipo_comanda,\n        descrizione: comanda.descrizione || '',\n        responsabile: comanda.responsabile || '',\n        data_scadenza: comanda.data_scadenza || '',\n        note_capo_cantiere: comanda.note_capo_cantiere || ''\n      });\n    }\n    setOpenComandaDialog(true);\n  };\n  const handleCloseComandaDialog = () => {\n    setOpenComandaDialog(false);\n    setSelectedComanda(null);\n    setFormDataComanda({\n      tipo_comanda: 'POSA',\n      descrizione: '',\n      responsabile: '',\n      data_scadenza: '',\n      note_capo_cantiere: ''\n    });\n  };\n  const handleSubmitComanda = async () => {\n    try {\n      await comandeService.updateComanda(selectedComanda.codice_comanda, formDataComanda);\n      handleCloseComandaDialog();\n      await loadResponsabili(); // Ricarica per aggiornare le comande\n      await loadComande();\n      await loadStatistiche();\n    } catch (err) {\n      console.error('Errore nel salvataggio:', err);\n      setError('Errore nel salvataggio della comanda');\n    }\n  };\n  const handleDeleteComanda = async codiceComanda => {\n    if (!window.confirm('Sei sicuro di voler eliminare questa comanda?')) {\n      return;\n    }\n    try {\n      await comandeService.deleteComanda(codiceComanda);\n      await loadResponsabili(); // Ricarica per aggiornare le comande\n      await loadComande();\n      await loadStatistiche();\n    } catch (err) {\n      console.error('Errore nell\\'eliminazione:', err);\n      setError('Errore nell\\'eliminazione della comanda');\n    }\n  };\n\n  // Gestione inserimento metri e collegamenti\n  const handleOpenInserimentoMetri = comanda => {\n    if (comanda.tipo_comanda === 'POSA') {\n      // Per POSA: apri dialog inserimento metri\n      setComandaPerMetri(comanda);\n      setOpenInserimentoMetri(true);\n    } else if (['COLLEGAMENTO_PARTENZA', 'COLLEGAMENTO_ARRIVO'].includes(comanda.tipo_comanda)) {\n      // Per COLLEGAMENTI: apri dialog collegamenti\n      setComandaPerCollegamenti(comanda);\n      setOpenCollegamenti(true);\n    } else {\n      // Per altri tipi: apri dialog inserimento metri (fallback)\n      setComandaPerMetri(comanda);\n      setOpenInserimentoMetri(true);\n    }\n  };\n  const handleCloseInserimentoMetri = () => {\n    setOpenInserimentoMetri(false);\n    setComandaPerMetri(null);\n  };\n  const handleCloseCollegamenti = () => {\n    setOpenCollegamenti(false);\n    setComandaPerCollegamenti(null);\n  };\n  const handleSuccessInserimentoMetri = async message => {\n    console.log('✅ Successo inserimento metri:', message);\n    setError(null);\n\n    // Ricarica tutti i dati per aggiornare le statistiche\n    await Promise.all([loadResponsabili(), loadComande(), loadStatistiche()]);\n\n    // Mostra un messaggio di successo (opzionale)\n    // Potresti aggiungere uno stato per i messaggi di successo se necessario\n  };\n  const handleSuccessCollegamenti = async message => {\n    console.log('✅ Successo collegamenti:', message);\n    setError(null);\n\n    // Ricarica tutti i dati per aggiornare le statistiche\n    await Promise.all([loadResponsabili(), loadComande(), loadStatistiche()]);\n\n    // Mostra un messaggio di successo (opzionale)\n    // Potresti aggiungere uno stato per i messaggi di successo se necessario\n  };\n\n  // Gestione stampa comanda\n  const handlePrintComanda = comanda => {\n    console.log('🖨️ Stampa comanda:', comanda.codice_comanda);\n\n    // Per ora mostra un dialog di selezione formato\n    const formato = window.prompt(`Seleziona il formato di stampa per la comanda ${comanda.codice_comanda}:\\n\\n` + '1 - A4 (Formato standard)\\n' + '2 - A3 (Formato esteso)\\n\\n' + 'Inserisci 1 o 2:', '1');\n    if (formato === '1' || formato === '2') {\n      const formatoNome = formato === '1' ? 'A4' : 'A3';\n      console.log(`📄 Generazione comanda ${comanda.codice_comanda} in formato ${formatoNome}`);\n\n      // TODO: Implementare la generazione del PDF\n      alert(`Funzionalità in sviluppo!\\n\\nComanda: ${comanda.codice_comanda}\\nFormato: ${formatoNome}\\nTipo: ${comanda.tipo_comanda}\\nResponsabile: ${comanda.responsabile}\\nCavi: ${comanda.numero_cavi_assegnati || 0}`);\n    }\n  };\n  const getTipoComandaLabel = tipo => {\n    const labels = {\n      'POSA': 'Posa',\n      'COLLEGAMENTO_PARTENZA': 'Coll. Partenza',\n      'COLLEGAMENTO_ARRIVO': 'Coll. Arrivo',\n      'CERTIFICAZIONE': 'Certificazione'\n    };\n    return labels[tipo] || tipo;\n  };\n  const getStatoColor = stato => {\n    const colors = {\n      'CREATA': 'default',\n      'ASSEGNATA': 'primary',\n      'IN_CORSO': 'warning',\n      'COMPLETATA': 'success',\n      'ANNULLATA': 'error'\n    };\n    return colors[stato] || 'default';\n  };\n  if (loading || loadingComande) {\n    return /*#__PURE__*/_jsxDEV(Box, {\n      display: \"flex\",\n      justifyContent: \"center\",\n      alignItems: \"center\",\n      minHeight: \"400px\",\n      children: /*#__PURE__*/_jsxDEV(CircularProgress, {}, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 627,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 626,\n      columnNumber: 7\n    }, this);\n  }\n  return /*#__PURE__*/_jsxDEV(Box, {\n    sx: {\n      p: 3\n    },\n    children: [/*#__PURE__*/_jsxDEV(Box, {\n      mb: 3,\n      children: /*#__PURE__*/_jsxDEV(Typography, {\n        variant: \"h4\",\n        sx: {\n          fontWeight: 600,\n          color: 'primary.main'\n        },\n        children: cantiereName\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 636,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 635,\n      columnNumber: 7\n    }, this), error && /*#__PURE__*/_jsxDEV(Alert, {\n      severity: \"error\",\n      sx: {\n        mb: 2\n      },\n      children: error\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 642,\n      columnNumber: 9\n    }, this), searchingComanda && /*#__PURE__*/_jsxDEV(Alert, {\n      severity: \"info\",\n      sx: {\n        mb: 2\n      },\n      children: [\"\\uD83D\\uDD0D Ricerca comanda \", searchingComanda, \" in corso...\"]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 648,\n      columnNumber: 9\n    }, this), statistiche && /*#__PURE__*/_jsxDEV(Paper, {\n      sx: {\n        p: 2,\n        mb: 3,\n        bgcolor: 'grey.50'\n      },\n      children: /*#__PURE__*/_jsxDEV(Stack, {\n        direction: \"row\",\n        spacing: 4,\n        alignItems: \"center\",\n        justifyContent: \"space-between\",\n        flexWrap: \"wrap\",\n        children: [/*#__PURE__*/_jsxDEV(Stack, {\n          direction: \"row\",\n          alignItems: \"center\",\n          spacing: 1,\n          children: [/*#__PURE__*/_jsxDEV(PersonIcon, {\n            color: \"primary\",\n            fontSize: \"small\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 659,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(Box, {\n            children: [/*#__PURE__*/_jsxDEV(Typography, {\n              variant: \"h6\",\n              fontWeight: \"bold\",\n              sx: {\n                lineHeight: 1\n              },\n              children: statistiche.responsabili_attivi || 0\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 661,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(Typography, {\n              variant: \"caption\",\n              color: \"text.secondary\",\n              children: \"Responsabili\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 664,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 660,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 658,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(Stack, {\n          direction: \"row\",\n          alignItems: \"center\",\n          spacing: 1,\n          children: [/*#__PURE__*/_jsxDEV(AssignIcon, {\n            color: \"info\",\n            fontSize: \"small\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 672,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(Box, {\n            children: [/*#__PURE__*/_jsxDEV(Typography, {\n              variant: \"h6\",\n              fontWeight: \"bold\",\n              sx: {\n                lineHeight: 1\n              },\n              children: statistiche.totale_comande || 0\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 674,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(Typography, {\n              variant: \"caption\",\n              color: \"text.secondary\",\n              children: \"Totale\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 677,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 673,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 671,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(Stack, {\n          direction: \"row\",\n          alignItems: \"center\",\n          spacing: 1,\n          children: [/*#__PURE__*/_jsxDEV(CheckCircleIcon, {\n            color: \"warning\",\n            fontSize: \"small\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 685,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(Box, {\n            children: [/*#__PURE__*/_jsxDEV(Typography, {\n              variant: \"h6\",\n              fontWeight: \"bold\",\n              sx: {\n                lineHeight: 1\n              },\n              children: statistiche.comande_in_corso || 0\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 687,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(Typography, {\n              variant: \"caption\",\n              color: \"text.secondary\",\n              children: \"In Corso\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 690,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 686,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 684,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(Stack, {\n          direction: \"row\",\n          alignItems: \"center\",\n          spacing: 1,\n          children: [/*#__PURE__*/_jsxDEV(VerifiedIcon, {\n            color: \"success\",\n            fontSize: \"small\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 698,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(Box, {\n            children: [/*#__PURE__*/_jsxDEV(Typography, {\n              variant: \"h6\",\n              fontWeight: \"bold\",\n              sx: {\n                lineHeight: 1\n              },\n              children: statistiche.comande_completate || 0\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 700,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(Typography, {\n              variant: \"caption\",\n              color: \"text.secondary\",\n              children: \"Completate\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 703,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 699,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 697,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(Stack, {\n          direction: \"row\",\n          alignItems: \"center\",\n          spacing: 1,\n          children: [/*#__PURE__*/_jsxDEV(Box, {\n            sx: {\n              width: 32,\n              height: 32,\n              borderRadius: '50%',\n              bgcolor: statistiche.comande_completate / (statistiche.totale_comande || 1) >= 0.8 ? 'success.main' : statistiche.comande_completate / (statistiche.totale_comande || 1) >= 0.5 ? 'warning.main' : 'error.main',\n              display: 'flex',\n              alignItems: 'center',\n              justifyContent: 'center'\n            },\n            children: /*#__PURE__*/_jsxDEV(Typography, {\n              variant: \"caption\",\n              fontWeight: \"bold\",\n              color: \"white\",\n              children: [statistiche.totale_comande > 0 ? Math.round(statistiche.comande_completate / statistiche.totale_comande * 100) : 0, \"%\"]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 721,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 711,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(Box, {\n            children: [/*#__PURE__*/_jsxDEV(Typography, {\n              variant: \"body2\",\n              fontWeight: \"medium\",\n              sx: {\n                lineHeight: 1\n              },\n              children: \"Completamento\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 726,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(Typography, {\n              variant: \"caption\",\n              color: \"text.secondary\",\n              children: [statistiche.comande_create || 0, \" create\"]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 729,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 725,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 710,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 656,\n        columnNumber: 11\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 655,\n      columnNumber: 9\n    }, this), /*#__PURE__*/_jsxDEV(Box, {\n      children: /*#__PURE__*/_jsxDEV(Box, {\n        children: [/*#__PURE__*/_jsxDEV(Box, {\n          display: \"flex\",\n          justifyContent: \"space-between\",\n          alignItems: \"center\",\n          mb: 3,\n          children: [/*#__PURE__*/_jsxDEV(Typography, {\n            variant: \"h5\",\n            sx: {\n              fontWeight: 500,\n              color: 'text.primary'\n            },\n            children: \"Gestione Responsabili e Comande\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 743,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Box, {\n            display: \"flex\",\n            gap: 2,\n            children: [/*#__PURE__*/_jsxDEV(Button, {\n              variant: \"outlined\",\n              startIcon: /*#__PURE__*/_jsxDEV(PeopleIcon, {}, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 749,\n                columnNumber: 28\n              }, this),\n              onClick: () => setOpenResponsabiliPopup(true),\n              sx: {\n                textTransform: 'none',\n                fontWeight: 500,\n                px: 3,\n                py: 1,\n                backgroundColor: '#f5f7fa',\n                color: '#2196f3',\n                border: '1px solid #2196f3',\n                '&:hover': {\n                  backgroundColor: 'rgba(33, 150, 243, 0.1)',\n                  borderColor: '#1976d2'\n                }\n              },\n              children: \"Lista Responsabili\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 747,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Button, {\n              variant: \"contained\",\n              startIcon: /*#__PURE__*/_jsxDEV(AddIcon, {}, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 769,\n                columnNumber: 28\n              }, this),\n              onClick: () => handleOpenResponsabileDialog('create'),\n              sx: {\n                textTransform: 'none',\n                fontWeight: 500,\n                px: 3,\n                py: 1,\n                backgroundColor: '#2196f3',\n                color: 'white',\n                '&:hover': {\n                  backgroundColor: '#1976d2'\n                }\n              },\n              children: \"Crea Responsabile\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 767,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 746,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 742,\n          columnNumber: 11\n        }, this), loadingComande ? /*#__PURE__*/_jsxDEV(Box, {\n          display: \"flex\",\n          justifyContent: \"center\",\n          py: 4,\n          children: /*#__PURE__*/_jsxDEV(CircularProgress, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 791,\n            columnNumber: 15\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 790,\n          columnNumber: 13\n        }, this) : allComande.length === 0 ? /*#__PURE__*/_jsxDEV(Paper, {\n          elevation: 0,\n          sx: {\n            p: 6,\n            textAlign: 'center',\n            backgroundColor: 'grey.50',\n            border: '1px dashed',\n            borderColor: 'grey.300'\n          },\n          children: [/*#__PURE__*/_jsxDEV(AssignIcon, {\n            sx: {\n              fontSize: 48,\n              color: 'grey.400',\n              mb: 2\n            }\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 804,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(Typography, {\n            variant: \"h6\",\n            color: \"text.secondary\",\n            gutterBottom: true,\n            children: \"Nessuna comanda disponibile\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 805,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(Typography, {\n            variant: \"body2\",\n            color: \"text.secondary\",\n            sx: {\n              mb: 3\n            },\n            children: \"Crea la prima comanda per iniziare a gestire i lavori\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 808,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(Button, {\n            variant: \"contained\",\n            startIcon: /*#__PURE__*/_jsxDEV(AddIcon, {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 813,\n              columnNumber: 28\n            }, this),\n            onClick: () => setOpenCreaConCavi(true),\n            sx: {\n              textTransform: 'none',\n              backgroundColor: '#2196f3',\n              color: 'white',\n              '&:hover': {\n                backgroundColor: '#1976d2'\n              }\n            },\n            children: \"Crea Prima Comanda\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 811,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 794,\n          columnNumber: 13\n        }, this) : /*#__PURE__*/_jsxDEV(ComandeListTable, {\n          comande: allComande,\n          onEditComanda: handleOpenComandaDialog,\n          onDeleteComanda: handleDeleteComanda,\n          onInserimentoMetri: handleOpenInserimentoMetri,\n          onPrintComanda: handlePrintComanda,\n          loading: loadingComande\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 828,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 740,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 739,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Dialog, {\n      open: openResponsabileDialog,\n      onClose: handleCloseResponsabileDialog,\n      maxWidth: \"sm\",\n      fullWidth: true,\n      PaperProps: {\n        sx: {\n          borderRadius: 2\n        }\n      },\n      children: [/*#__PURE__*/_jsxDEV(DialogTitle, {\n        sx: {\n          pb: 1\n        },\n        children: /*#__PURE__*/_jsxDEV(Typography, {\n          variant: \"h6\",\n          sx: {\n            fontWeight: 600\n          },\n          children: dialogModeResponsabile === 'create' ? 'Inserisci Responsabile' : 'Modifica Responsabile'\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 851,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 850,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(DialogContent, {\n        children: /*#__PURE__*/_jsxDEV(Box, {\n          sx: {\n            pt: 2\n          },\n          children: [/*#__PURE__*/_jsxDEV(TextField, {\n            fullWidth: true,\n            label: \"Nome Responsabile\",\n            value: formDataResponsabile.nome_responsabile,\n            onChange: e => setFormDataResponsabile({\n              ...formDataResponsabile,\n              nome_responsabile: e.target.value\n            }),\n            margin: \"normal\",\n            required: true,\n            variant: \"outlined\",\n            sx: {\n              mb: 2\n            }\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 857,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(TextField, {\n            fullWidth: true,\n            label: \"Email\",\n            type: \"email\",\n            value: formDataResponsabile.email,\n            onChange: e => setFormDataResponsabile({\n              ...formDataResponsabile,\n              email: e.target.value\n            }),\n            margin: \"normal\",\n            variant: \"outlined\",\n            helperText: \"Email per notifiche (opzionale se inserisci telefono)\",\n            sx: {\n              mb: 2\n            }\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 868,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(TextField, {\n            fullWidth: true,\n            label: \"Telefono\",\n            value: formDataResponsabile.telefono,\n            onChange: e => setFormDataResponsabile({\n              ...formDataResponsabile,\n              telefono: e.target.value\n            }),\n            margin: \"normal\",\n            variant: \"outlined\",\n            helperText: \"Numero per SMS (opzionale se inserisci email)\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 880,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 856,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 855,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(DialogActions, {\n        sx: {\n          p: 3,\n          pt: 2\n        },\n        children: [/*#__PURE__*/_jsxDEV(Button, {\n          onClick: handleCloseResponsabileDialog,\n          sx: {\n            textTransform: 'none'\n          },\n          children: \"Annulla\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 892,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Button, {\n          onClick: handleSubmitResponsabile,\n          variant: \"contained\",\n          sx: {\n            textTransform: 'none',\n            fontWeight: 500,\n            px: 3\n          },\n          children: dialogModeResponsabile === 'create' ? 'Crea' : 'Salva'\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 898,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 891,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 841,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Dialog, {\n      open: openComandaDialog,\n      onClose: handleCloseComandaDialog,\n      maxWidth: \"md\",\n      fullWidth: true,\n      PaperProps: {\n        sx: {\n          borderRadius: 2\n        }\n      },\n      children: [/*#__PURE__*/_jsxDEV(DialogTitle, {\n        sx: {\n          pb: 1\n        },\n        children: [/*#__PURE__*/_jsxDEV(Typography, {\n          variant: \"h6\",\n          sx: {\n            fontWeight: 600\n          },\n          children: [\"Modifica Comanda \", selectedComanda === null || selectedComanda === void 0 ? void 0 : selectedComanda.codice_comanda]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 923,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Typography, {\n          variant: \"body2\",\n          color: \"text.secondary\",\n          children: [\"Stato: \", selectedComanda === null || selectedComanda === void 0 ? void 0 : selectedComanda.stato, \" \\u2022 Cavi assegnati: \", (selectedComanda === null || selectedComanda === void 0 ? void 0 : selectedComanda.numero_cavi_assegnati) || 0]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 926,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 922,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(DialogContent, {\n        children: /*#__PURE__*/_jsxDEV(Box, {\n          sx: {\n            pt: 2\n          },\n          children: [/*#__PURE__*/_jsxDEV(Alert, {\n            severity: \"warning\",\n            sx: {\n              mb: 2\n            },\n            children: /*#__PURE__*/_jsxDEV(Typography, {\n              variant: \"body2\",\n              children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n                children: \"Attenzione:\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 934,\n                columnNumber: 17\n              }, this), \" Modificare il tipo di comanda riassegner\\xE0 automaticamente tutti i cavi al nuovo tipo. Assicurati che i cavi siano compatibili con il nuovo tipo di operazione.\"]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 933,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 932,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(TextField, {\n            fullWidth: true,\n            select: true,\n            label: \"Tipo Comanda\",\n            value: formDataComanda.tipo_comanda,\n            onChange: e => setFormDataComanda({\n              ...formDataComanda,\n              tipo_comanda: e.target.value\n            }),\n            margin: \"normal\",\n            sx: {\n              mb: 2\n            },\n            children: [/*#__PURE__*/_jsxDEV(MenuItem, {\n              value: \"POSA\",\n              children: \"Posa\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 948,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(MenuItem, {\n              value: \"COLLEGAMENTO_PARTENZA\",\n              children: \"Collegamento Partenza\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 949,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(MenuItem, {\n              value: \"COLLEGAMENTO_ARRIVO\",\n              children: \"Collegamento Arrivo\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 950,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(MenuItem, {\n              value: \"CERTIFICAZIONE\",\n              children: \"Certificazione\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 951,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 939,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(TextField, {\n            fullWidth: true,\n            label: \"Descrizione\",\n            value: formDataComanda.descrizione,\n            onChange: e => setFormDataComanda({\n              ...formDataComanda,\n              descrizione: e.target.value\n            }),\n            margin: \"normal\",\n            multiline: true,\n            rows: 3,\n            sx: {\n              mb: 2\n            }\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 954,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(TextField, {\n            fullWidth: true,\n            label: \"Responsabile\",\n            value: formDataComanda.responsabile,\n            onChange: e => setFormDataComanda({\n              ...formDataComanda,\n              responsabile: e.target.value\n            }),\n            margin: \"normal\",\n            required: true,\n            helperText: \"Chi eseguir\\xE0 il lavoro (obbligatorio)\",\n            sx: {\n              mb: 2\n            }\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 965,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(TextField, {\n            fullWidth: true,\n            label: \"Note Capo Cantiere\",\n            value: formDataComanda.note_capo_cantiere,\n            onChange: e => setFormDataComanda({\n              ...formDataComanda,\n              note_capo_cantiere: e.target.value\n            }),\n            margin: \"normal\",\n            multiline: true,\n            rows: 2,\n            helperText: \"Istruzioni specifiche per il responsabile\",\n            sx: {\n              mb: 2\n            }\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 976,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(TextField, {\n            fullWidth: true,\n            label: \"Data Scadenza\",\n            type: \"date\",\n            value: formDataComanda.data_scadenza,\n            onChange: e => setFormDataComanda({\n              ...formDataComanda,\n              data_scadenza: e.target.value\n            }),\n            margin: \"normal\",\n            InputLabelProps: {\n              shrink: true\n            }\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 988,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 931,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 930,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(DialogActions, {\n        sx: {\n          p: 3,\n          pt: 2\n        },\n        children: [/*#__PURE__*/_jsxDEV(Button, {\n          onClick: handleCloseComandaDialog,\n          sx: {\n            textTransform: 'none'\n          },\n          children: \"Annulla\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 1002,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Button, {\n          onClick: handleSubmitComanda,\n          variant: \"contained\",\n          sx: {\n            textTransform: 'none',\n            fontWeight: 500,\n            px: 3\n          },\n          children: \"Salva Modifiche\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 1008,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 1001,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 913,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(CreaComandaConCavi, {\n      cantiereId: cantiereId,\n      open: openCreaConCavi,\n      onClose: () => setOpenCreaConCavi(false),\n      onSuccess: (response, successMessage) => {\n        console.log('🎉 Comanda creata, aggiornamento interfaccia...');\n\n        // Mostra messaggio di successo se fornito\n        if (successMessage) {\n          // Potresti aggiungere qui un toast/snackbar per mostrare il messaggio\n          console.log('📢 Successo:', successMessage);\n        }\n\n        // Ricarica tutti i dati per aggiornare l'interfaccia\n        loadComande();\n        loadStatistiche();\n        loadResponsabili();\n        setOpenCreaConCavi(false);\n        console.log('✅ Interfaccia aggiornata');\n      }\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 1023,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(ResponsabiliListPopup, {\n      open: openResponsabiliPopup,\n      onClose: () => setOpenResponsabiliPopup(false),\n      responsabili: responsabili,\n      comandePerResponsabile: comandePerResponsabile,\n      onEditResponsabile: responsabile => {\n        setOpenResponsabiliPopup(false);\n        handleOpenResponsabileDialog('edit', responsabile);\n      },\n      onDeleteResponsabile: async idResponsabile => {\n        await handleDeleteResponsabile(idResponsabile);\n        setOpenResponsabiliPopup(false);\n      },\n      loading: loadingResponsabili,\n      error: error\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 1047,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(InserimentoMetriDialog, {\n      open: openInserimentoMetri,\n      onClose: handleCloseInserimentoMetri,\n      comanda: comandaPerMetri,\n      onSuccess: handleSuccessInserimentoMetri\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 1065,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(CollegamentiDialog, {\n      open: openCollegamenti,\n      onClose: handleCloseCollegamenti,\n      comanda: comandaPerCollegamenti,\n      onSuccess: handleSuccessCollegamenti\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 1073,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 633,\n    columnNumber: 5\n  }, this);\n};\n_s(ComandeListRivoluzionato, \"z85HBrPxMvigfS49H7k5EdvaEB0=\", false, function () {\n  return [useSearchParams];\n});\n_c = ComandeListRivoluzionato;\nexport default ComandeListRivoluzionato;\nvar _c;\n$RefreshReg$(_c, \"ComandeListRivoluzionato\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "useSearchParams", "Box", "Typography", "<PERSON><PERSON>", "Paper", "Chip", "Dialog", "DialogTitle", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "DialogActions", "TextField", "<PERSON><PERSON>", "CircularProgress", "List", "ListItem", "ListItemText", "<PERSON><PERSON>", "MenuItem", "Divider", "Add", "AddIcon", "Assignment", "AssignIcon", "Person", "PersonIcon", "CheckCircle", "CheckCircleIcon", "Verified", "VerifiedIcon", "People", "PeopleIcon", "Construction", "ConstructionIcon", "comandeService", "responsabiliService", "CreaComandaConCavi", "ResponsabiliListPopup", "ComandeListTable", "InserimentoMetriDialog", "CollegamentiDialog", "jsxDEV", "_jsxDEV", "ComandeListRivoluzionato", "cantiereId", "cantiereName", "_s", "searchParams", "setSearchParams", "loading", "setLoading", "error", "setError", "searchingComanda", "setSearchingComanda", "statistiche", "setStatistiche", "allComande", "setAllComande", "loadingComande", "setLoadingComande", "openResponsabiliPopup", "setOpenResponsabiliPopup", "openCreaConCavi", "setOpenCreaConCavi", "responsabili", "setResponsabili", "loadingResponsabili", "setLoadingResponsabili", "comandePerResponsabile", "setComandePerResponsabile", "openResponsabileDialog", "setOpenResponsabileDialog", "dialogModeResponsabile", "setDialogModeResponsabile", "selectedResponsabile", "setSelectedResponsabile", "formDataResponsabile", "setFormDataResponsabile", "nome_responsabile", "email", "telefono", "openComandaDialog", "setOpenComandaDialog", "selectedComanda", "setSelectedComanda", "formDataComanda", "setFormDataComanda", "tipo_comanda", "descrizione", "responsabile", "data_scadenza", "note_capo_cantiere", "openInserimentoMetri", "setOpenInserimentoMetri", "comandaPerMetri", "setComandaPerMetri", "openCollegamenti", "setOpenCollegamenti", "comandaPerCollegamenti", "setComandaPerCollegamenti", "loadComande", "console", "log", "startTime", "performance", "now", "comandeData", "getComande", "comandeArray", "Array", "isArray", "comande", "data", "endTime", "length", "toFixed", "err", "loadStatistiche", "stats", "getStatisticheComande", "_err$response", "response", "message", "responsabili_attivi", "totale_comande", "comande_create", "comande_in_corso", "comande_completate", "comande_annullate", "loadResponsabili", "getResponsabiliCantiere", "_err$response2", "_err$response2$data", "errorMessage", "detail", "initializeData", "totalStartTime", "responsabiliData", "statisticheData", "Promise", "all", "comandeMap", "for<PERSON>ach", "id_responsabile", "comanda", "find", "r", "push", "totalEndTime", "comandaParam", "get", "Object", "keys", "comandaTrovata", "comandeResp", "c", "codice_comanda", "isWorkflowCommand", "includes", "handleOpenInserimentoMetri", "handleOpenComandaDialog", "setTimeout", "prev", "newParams", "URLSearchParams", "delete", "warn", "resp", "cmd", "loadComandePerResponsabili", "responsabiliList", "handleOpenResponsabileDialog", "mode", "handleCloseResponsabileDialog", "handleSubmitResponsabile", "trim", "createResponsabile", "updateResponsabile", "handleDeleteResponsabile", "idResponsabile", "window", "confirm", "deleteResponsabile", "handleCloseComandaDialog", "handleSubmitComanda", "updateComanda", "handleDeleteComanda", "codiceComanda", "deleteComanda", "handleCloseInserimentoMetri", "handleCloseCollegamenti", "handleSuccessInserimentoMetri", "handleSuccessCollegamenti", "handlePrintComanda", "formato", "prompt", "formatoNome", "alert", "numero_cavi_assegnati", "getTipoComandaLabel", "tipo", "labels", "getStatoColor", "stato", "colors", "display", "justifyContent", "alignItems", "minHeight", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "sx", "p", "mb", "variant", "fontWeight", "color", "severity", "bgcolor", "direction", "spacing", "flexWrap", "fontSize", "lineHeight", "width", "height", "borderRadius", "Math", "round", "gap", "startIcon", "onClick", "textTransform", "px", "py", "backgroundColor", "border", "borderColor", "elevation", "textAlign", "gutterBottom", "onEditComanda", "onDeleteComanda", "onInserimentoMetri", "onPrintComanda", "open", "onClose", "max<PERSON><PERSON><PERSON>", "fullWidth", "PaperProps", "pb", "pt", "label", "value", "onChange", "e", "target", "margin", "required", "type", "helperText", "select", "multiline", "rows", "InputLabelProps", "shrink", "onSuccess", "successMessage", "onEditResponsabile", "onDeleteResponsabile", "_c", "$RefreshReg$"], "sources": ["C:/CMS/webapp/frontend/src/components/comande/ComandeListRivoluzionato.js"], "sourcesContent": ["import React, { useState, useEffect } from 'react';\nimport { useSearchParams } from 'react-router-dom';\nimport {\n  Box,\n  Typography,\n  Button,\n  Paper,\n  Chip,\n  Dialog,\n  DialogTitle,\n  DialogContent,\n  DialogActions,\n  TextField,\n  Alert,\n  CircularProgress,\n  List,\n  ListItem,\n  ListItemText,\n  Stack,\n  MenuItem,\n  Divider\n} from '@mui/material';\nimport {\n  Add as AddIcon,\n  Assignment as AssignIcon,\n  Person as PersonIcon,\n  CheckCircle as CheckCircleIcon,\n  Verified as VerifiedIcon,\n  People as PeopleIcon,\n  Construction as ConstructionIcon\n} from '@mui/icons-material';\nimport comandeService from '../../services/comandeService';\nimport responsabiliService from '../../services/responsabiliService';\nimport CreaComandaConCavi from './CreaComandaConCavi';\nimport ResponsabiliListPopup from './ResponsabiliListPopup';\nimport ComandeListTable from './ComandeListTable';\nimport InserimentoMetriDialog from './InserimentoMetriDialog';\nimport CollegamentiDialog from './CollegamentiDialog';\n\nconst ComandeListRivoluzionato = ({ cantiereId, cantiereName }) => {\n  // Hook per gestire i parametri URL\n  const [searchParams, setSearchParams] = useSearchParams();\n\n  // Stati principali - Responsabili come elemento principale\n  const [loading, setLoading] = useState(true);\n  const [error, setError] = useState(null);\n  const [searchingComanda, setSearchingComanda] = useState(null);\n\n  // Stati comande\n  const [statistiche, setStatistiche] = useState(null);\n  const [allComande, setAllComande] = useState([]);\n  const [loadingComande, setLoadingComande] = useState(false);\n\n  // Stati per popup responsabili\n  const [openResponsabiliPopup, setOpenResponsabiliPopup] = useState(false);\n  const [openCreaConCavi, setOpenCreaConCavi] = useState(false);\n\n  // Stati responsabili\n  const [responsabili, setResponsabili] = useState([]);\n  const [loadingResponsabili, setLoadingResponsabili] = useState(false);\n  const [comandePerResponsabile, setComandePerResponsabile] = useState({});\n  const [openResponsabileDialog, setOpenResponsabileDialog] = useState(false);\n  const [dialogModeResponsabile, setDialogModeResponsabile] = useState('create');\n  const [selectedResponsabile, setSelectedResponsabile] = useState(null);\n  const [formDataResponsabile, setFormDataResponsabile] = useState({\n    nome_responsabile: '',\n    email: '',\n    telefono: ''\n  });\n\n  // Stati per dialog comande\n  const [openComandaDialog, setOpenComandaDialog] = useState(false);\n  const [selectedComanda, setSelectedComanda] = useState(null);\n  const [formDataComanda, setFormDataComanda] = useState({\n    tipo_comanda: 'POSA',\n    descrizione: '',\n    responsabile: '',\n    data_scadenza: '',\n    note_capo_cantiere: ''\n  });\n\n  // Stati per dialog inserimento metri\n  const [openInserimentoMetri, setOpenInserimentoMetri] = useState(false);\n  const [comandaPerMetri, setComandaPerMetri] = useState(null);\n\n  // Stati per dialog collegamenti\n  const [openCollegamenti, setOpenCollegamenti] = useState(false);\n  const [comandaPerCollegamenti, setComandaPerCollegamenti] = useState(null);\n\n  const loadComande = async () => {\n    try {\n      setLoadingComande(true);\n      console.log('🔄 Caricamento comande per cantiere:', cantiereId);\n      const startTime = performance.now();\n\n      const comandeData = await comandeService.getComande(cantiereId);\n\n      // Gestisci diversi formati di risposta\n      let comandeArray = [];\n      if (Array.isArray(comandeData)) {\n        comandeArray = comandeData;\n      } else if (comandeData && Array.isArray(comandeData.comande)) {\n        comandeArray = comandeData.comande;\n      } else if (comandeData && Array.isArray(comandeData.data)) {\n        comandeArray = comandeData.data;\n      }\n\n      const endTime = performance.now();\n      console.log(`✅ ${comandeArray.length} comande caricate in ${(endTime - startTime).toFixed(2)}ms`);\n\n      setAllComande(comandeArray);\n    } catch (err) {\n      console.error('❌ Errore nel caricamento comande:', err);\n      setError('Errore nel caricamento delle comande');\n      setAllComande([]);\n    } finally {\n      setLoadingComande(false);\n    }\n  };\n\n  const loadStatistiche = async () => {\n    try {\n      console.log('🔄 Caricamento statistiche per cantiere:', cantiereId);\n      const startTime = performance.now();\n\n      const stats = await comandeService.getStatisticheComande(cantiereId);\n\n      const endTime = performance.now();\n      console.log(`📊 Statistiche caricate in ${(endTime - startTime).toFixed(2)}ms:`, stats);\n\n      setStatistiche(stats);\n    } catch (err) {\n      console.error('❌ Errore nel caricamento delle statistiche:', err);\n      console.error('❌ Dettagli errore:', err.response?.data || err.message);\n      // Imposta statistiche vuote in caso di errore\n      setStatistiche({\n        responsabili_attivi: 0,\n        totale_comande: 0,\n        comande_create: 0,\n        comande_in_corso: 0,\n        comande_completate: 0,\n        comande_annullate: 0\n      });\n    }\n  };\n\n  const loadResponsabili = async () => {\n    try {\n      setLoadingResponsabili(true);\n      setError(null);\n      console.log('🔄 Caricamento responsabili per cantiere:', cantiereId);\n      const startTime = performance.now();\n\n      const data = await responsabiliService.getResponsabiliCantiere(cantiereId);\n\n      const endTime = performance.now();\n      console.log(`✅ ${(data || []).length} responsabili caricati in ${(endTime - startTime).toFixed(2)}ms`);\n\n      setResponsabili(data || []);\n      return data || [];\n    } catch (err) {\n      console.error('❌ Errore nel caricamento dei responsabili:', err);\n      const errorMessage = err.response?.data?.detail || err.message || 'Errore nel caricamento dei responsabili';\n      setError(`Errore nel caricamento dei responsabili: ${errorMessage}`);\n      setResponsabili([]);\n      return [];\n    } finally {\n      setLoadingResponsabili(false);\n    }\n  };\n\n\n\n  // Caricamento ottimizzato - Tutto in parallelo\n  useEffect(() => {\n    const initializeData = async () => {\n      if (cantiereId) {\n        setLoading(true);\n        console.log('🚀 Inizializzazione dati cantiere:', cantiereId);\n        const totalStartTime = performance.now();\n\n        try {\n          // OTTIMIZZAZIONE: Carica tutto in parallelo per massimizzare le performance\n          const [responsabiliData, comandeData, statisticheData] = await Promise.all([\n            responsabiliService.getResponsabiliCantiere(cantiereId),\n            comandeService.getComande(cantiereId),\n            comandeService.getStatisticheComande(cantiereId)\n          ]);\n\n          // Imposta i dati\n          setResponsabili(responsabiliData || []);\n\n          // Gestisci formato comande\n          let comandeArray = [];\n          if (Array.isArray(comandeData)) {\n            comandeArray = comandeData;\n          } else if (comandeData && Array.isArray(comandeData.comande)) {\n            comandeArray = comandeData.comande;\n          } else if (comandeData && Array.isArray(comandeData.data)) {\n            comandeArray = comandeData.data;\n          }\n          setAllComande(comandeArray);\n\n          // Raggruppa comande per responsabile (senza chiamate API aggiuntive)\n          const comandeMap = {};\n          (responsabiliData || []).forEach(responsabile => {\n            comandeMap[responsabile.id_responsabile] = [];\n          });\n          comandeArray.forEach(comanda => {\n            const responsabile = (responsabiliData || []).find(r => r.nome_responsabile === comanda.responsabile);\n            if (responsabile) {\n              comandeMap[responsabile.id_responsabile].push(comanda);\n            }\n          });\n          setComandePerResponsabile(comandeMap);\n\n          // Imposta statistiche\n          setStatistiche(statisticheData || {\n            responsabili_attivi: 0,\n            totale_comande: 0,\n            comande_create: 0,\n            comande_in_corso: 0,\n            comande_completate: 0,\n            comande_annullate: 0\n          });\n\n          const totalEndTime = performance.now();\n          console.log(`✅ Inizializzazione completata in ${(totalEndTime - totalStartTime).toFixed(2)}ms`);\n          console.log(`📊 Dati caricati: ${(responsabiliData || []).length} responsabili, ${comandeArray.length} comande`);\n\n        } catch (err) {\n          console.error('❌ Errore nel caricamento iniziale:', err);\n          setError('Errore nel caricamento dei dati');\n          // Imposta valori di fallback\n          setResponsabili([]);\n          setAllComande([]);\n          setComandePerResponsabile({});\n          setStatistiche({\n            responsabili_attivi: 0,\n            totale_comande: 0,\n            comande_create: 0,\n            comande_in_corso: 0,\n            comande_completate: 0,\n            comande_annullate: 0\n          });\n        } finally {\n          setLoading(false);\n          setLoadingComande(false);\n          setLoadingResponsabili(false);\n        }\n      }\n    };\n\n    initializeData();\n  }, [cantiereId]); // eslint-disable-line react-hooks/exhaustive-deps\n\n  // Gestione parametro URL per aprire comanda specifica\n  useEffect(() => {\n    const comandaParam = searchParams.get('comanda');\n    console.log('🔍 Controllo parametro URL comanda:', comandaParam);\n    console.log('📊 Stato dati:', {\n      responsabili: responsabili.length,\n      comandePerResponsabile: Object.keys(comandePerResponsabile).length,\n      loading,\n      loadingResponsabili\n    });\n\n    // Imposta lo stato di ricerca\n    if (comandaParam && comandaParam !== searchingComanda) {\n      setSearchingComanda(comandaParam);\n    }\n\n    if (comandaParam && responsabili.length > 0 && Object.keys(comandePerResponsabile).length > 0) {\n      console.log('🔎 Ricerca comanda tra i responsabili...');\n\n      // Cerca la comanda tra tutti i responsabili\n      let comandaTrovata = null;\n\n      for (const responsabile of responsabili) {\n        const comandeResp = comandePerResponsabile[responsabile.id_responsabile] || [];\n        console.log(`📋 Responsabile ${responsabile.nome_responsabile}: ${comandeResp.length} comande`);\n        comandaTrovata = comandeResp.find(c => c.codice_comanda === comandaParam);\n        if (comandaTrovata) {\n          console.log('✅ Comanda trovata:', comandaTrovata);\n          break;\n        }\n      }\n\n      if (comandaTrovata) {\n        console.log('🎯 Apertura automatica comanda da URL:', comandaParam);\n        setSearchingComanda(null); // Rimuovi lo stato di ricerca\n\n        // Per comande di workflow (POSA, COLLEGAMENTO_PARTENZA, COLLEGAMENTO_ARRIVO) provenienti da visualizza cavi,\n        // apri l'inserimento dati specifico. Per altre comande, apri il dialog di modifica generale\n        const isWorkflowCommand = ['POSA', 'COLLEGAMENTO_PARTENZA', 'COLLEGAMENTO_ARRIVO'].includes(comandaTrovata.tipo_comanda);\n\n        if (isWorkflowCommand) {\n          console.log(`📏 Apertura gestione per comanda ${comandaTrovata.tipo_comanda} da visualizza cavi:`, comandaParam);\n          handleOpenInserimentoMetri(comandaTrovata);\n        } else {\n          console.log('✏️ Apertura modifica per comanda non-workflow:', comandaParam);\n          handleOpenComandaDialog(comandaTrovata);\n        }\n\n        // Rimuovi il parametro dall'URL per evitare riaperture\n        setTimeout(() => {\n          setSearchParams(prev => {\n            const newParams = new URLSearchParams(prev);\n            newParams.delete('comanda');\n            return newParams;\n          });\n        }, 100);\n      } else {\n        console.warn('⚠️ Comanda non trovata:', comandaParam);\n        console.log('📋 Comande disponibili:');\n        responsabili.forEach(resp => {\n          const comande = comandePerResponsabile[resp.id_responsabile] || [];\n          comande.forEach(cmd => {\n            console.log(`  - ${cmd.codice_comanda} (${resp.nome_responsabile})`);\n          });\n        });\n\n        // Se non troviamo la comanda, potrebbe essere che i dati non sono completi\n        // Riprova dopo un breve delay\n        if (!loading && !loadingResponsabili) {\n          console.log('🔄 Tentativo di ricaricamento dati...');\n          setTimeout(() => {\n            loadResponsabili();\n          }, 500);\n        }\n      }\n    } else if (comandaParam) {\n      console.log('⏳ Dati non ancora caricati, attendo...');\n\n      // Se abbiamo un parametro ma i dati non sono caricati, forza il caricamento\n      if (!loading && !loadingResponsabili && responsabili.length === 0) {\n        console.log('🚀 Forzatura caricamento responsabili...');\n        loadResponsabili();\n      }\n    }\n  }, [searchParams, responsabili, comandePerResponsabile, loading, loadingResponsabili]); // eslint-disable-line react-hooks/exhaustive-deps\n\n  const loadComandePerResponsabili = async (responsabiliList) => {\n    try {\n      console.log('🚀 Caricamento comande per responsabili ottimizzato...');\n      const startTime = performance.now();\n\n      // OTTIMIZZAZIONE: Carica tutte le comande una sola volta invece di fare N chiamate\n      const allComande = await comandeService.getComande(cantiereId);\n\n      // Gestisci diversi formati di risposta\n      let comandeArray = [];\n      if (Array.isArray(allComande)) {\n        comandeArray = allComande;\n      } else if (allComande && Array.isArray(allComande.comande)) {\n        comandeArray = allComande.comande;\n      } else if (allComande && Array.isArray(allComande.data)) {\n        comandeArray = allComande.data;\n      }\n\n      // Raggruppa le comande per responsabile\n      const comandeMap = {};\n\n      // Inizializza tutti i responsabili con array vuoto\n      responsabiliList.forEach(responsabile => {\n        comandeMap[responsabile.id_responsabile] = [];\n      });\n\n      // Raggruppa le comande per responsabile\n      comandeArray.forEach(comanda => {\n        const responsabile = responsabiliList.find(r => r.nome_responsabile === comanda.responsabile);\n        if (responsabile) {\n          comandeMap[responsabile.id_responsabile].push(comanda);\n        }\n      });\n\n      const endTime = performance.now();\n      console.log(`✅ Comande caricate in ${(endTime - startTime).toFixed(2)}ms (1 chiamata invece di ${responsabiliList.length})`);\n\n      setComandePerResponsabile(comandeMap);\n    } catch (err) {\n      console.error('❌ Errore nel caricamento delle comande:', err);\n      // Fallback: inizializza con array vuoti\n      const comandeMap = {};\n      responsabiliList.forEach(responsabile => {\n        comandeMap[responsabile.id_responsabile] = [];\n      });\n      setComandePerResponsabile(comandeMap);\n    }\n  };\n\n  // Gestione responsabili\n  const handleOpenResponsabileDialog = (mode, responsabile = null) => {\n    setDialogModeResponsabile(mode);\n    setSelectedResponsabile(responsabile);\n    \n    if (mode === 'edit' && responsabile) {\n      setFormDataResponsabile({\n        nome_responsabile: responsabile.nome_responsabile || '',\n        email: responsabile.email || '',\n        telefono: responsabile.telefono || ''\n      });\n    } else {\n      setFormDataResponsabile({\n        nome_responsabile: '',\n        email: '',\n        telefono: ''\n      });\n    }\n    \n    setOpenResponsabileDialog(true);\n  };\n\n  const handleCloseResponsabileDialog = () => {\n    setOpenResponsabileDialog(false);\n    setSelectedResponsabile(null);\n    setError(null);\n  };\n\n  const handleSubmitResponsabile = async () => {\n    try {\n      setError(null);\n      \n      if (!formDataResponsabile.nome_responsabile.trim()) {\n        setError('Il nome del responsabile è obbligatorio');\n        return;\n      }\n      \n      if (!formDataResponsabile.email && !formDataResponsabile.telefono) {\n        setError('Almeno uno tra email e telefono deve essere specificato');\n        return;\n      }\n\n      if (dialogModeResponsabile === 'create') {\n        await responsabiliService.createResponsabile(cantiereId, formDataResponsabile);\n      } else if (dialogModeResponsabile === 'edit') {\n        await responsabiliService.updateResponsabile(selectedResponsabile.id_responsabile, formDataResponsabile);\n      }\n\n      handleCloseResponsabileDialog();\n      await loadResponsabili();\n      await loadComande();\n    } catch (err) {\n      console.error('Errore nel salvataggio:', err);\n      setError(err.detail || 'Errore nel salvataggio del responsabile');\n    }\n  };\n\n  const handleDeleteResponsabile = async (idResponsabile) => {\n    if (!window.confirm('Sei sicuro di voler eliminare questo responsabile?')) {\n      return;\n    }\n\n    try {\n      await responsabiliService.deleteResponsabile(idResponsabile);\n      await loadResponsabili();\n      await loadComande();\n    } catch (err) {\n      console.error('Errore nell\\'eliminazione:', err);\n      setError('Errore nell\\'eliminazione del responsabile');\n    }\n  };\n\n  // Gestione comande\n  const handleOpenComandaDialog = (comanda) => {\n    setSelectedComanda(comanda);\n\n    if (comanda) {\n      setFormDataComanda({\n        tipo_comanda: comanda.tipo_comanda,\n        descrizione: comanda.descrizione || '',\n        responsabile: comanda.responsabile || '',\n        data_scadenza: comanda.data_scadenza || '',\n        note_capo_cantiere: comanda.note_capo_cantiere || ''\n      });\n    }\n\n    setOpenComandaDialog(true);\n  };\n\n  const handleCloseComandaDialog = () => {\n    setOpenComandaDialog(false);\n    setSelectedComanda(null);\n    setFormDataComanda({\n      tipo_comanda: 'POSA',\n      descrizione: '',\n      responsabile: '',\n      data_scadenza: '',\n      note_capo_cantiere: ''\n    });\n  };\n\n  const handleSubmitComanda = async () => {\n    try {\n      await comandeService.updateComanda(selectedComanda.codice_comanda, formDataComanda);\n      handleCloseComandaDialog();\n      await loadResponsabili(); // Ricarica per aggiornare le comande\n      await loadComande();\n      await loadStatistiche();\n    } catch (err) {\n      console.error('Errore nel salvataggio:', err);\n      setError('Errore nel salvataggio della comanda');\n    }\n  };\n\n  const handleDeleteComanda = async (codiceComanda) => {\n    if (!window.confirm('Sei sicuro di voler eliminare questa comanda?')) {\n      return;\n    }\n\n    try {\n      await comandeService.deleteComanda(codiceComanda);\n      await loadResponsabili(); // Ricarica per aggiornare le comande\n      await loadComande();\n      await loadStatistiche();\n    } catch (err) {\n      console.error('Errore nell\\'eliminazione:', err);\n      setError('Errore nell\\'eliminazione della comanda');\n    }\n  };\n\n  // Gestione inserimento metri e collegamenti\n  const handleOpenInserimentoMetri = (comanda) => {\n    if (comanda.tipo_comanda === 'POSA') {\n      // Per POSA: apri dialog inserimento metri\n      setComandaPerMetri(comanda);\n      setOpenInserimentoMetri(true);\n    } else if (['COLLEGAMENTO_PARTENZA', 'COLLEGAMENTO_ARRIVO'].includes(comanda.tipo_comanda)) {\n      // Per COLLEGAMENTI: apri dialog collegamenti\n      setComandaPerCollegamenti(comanda);\n      setOpenCollegamenti(true);\n    } else {\n      // Per altri tipi: apri dialog inserimento metri (fallback)\n      setComandaPerMetri(comanda);\n      setOpenInserimentoMetri(true);\n    }\n  };\n\n  const handleCloseInserimentoMetri = () => {\n    setOpenInserimentoMetri(false);\n    setComandaPerMetri(null);\n  };\n\n  const handleCloseCollegamenti = () => {\n    setOpenCollegamenti(false);\n    setComandaPerCollegamenti(null);\n  };\n\n  const handleSuccessInserimentoMetri = async (message) => {\n    console.log('✅ Successo inserimento metri:', message);\n    setError(null);\n\n    // Ricarica tutti i dati per aggiornare le statistiche\n    await Promise.all([\n      loadResponsabili(),\n      loadComande(),\n      loadStatistiche()\n    ]);\n\n    // Mostra un messaggio di successo (opzionale)\n    // Potresti aggiungere uno stato per i messaggi di successo se necessario\n  };\n\n  const handleSuccessCollegamenti = async (message) => {\n    console.log('✅ Successo collegamenti:', message);\n    setError(null);\n\n    // Ricarica tutti i dati per aggiornare le statistiche\n    await Promise.all([\n      loadResponsabili(),\n      loadComande(),\n      loadStatistiche()\n    ]);\n\n    // Mostra un messaggio di successo (opzionale)\n    // Potresti aggiungere uno stato per i messaggi di successo se necessario\n  };\n\n  // Gestione stampa comanda\n  const handlePrintComanda = (comanda) => {\n    console.log('🖨️ Stampa comanda:', comanda.codice_comanda);\n\n    // Per ora mostra un dialog di selezione formato\n    const formato = window.prompt(\n      `Seleziona il formato di stampa per la comanda ${comanda.codice_comanda}:\\n\\n` +\n      '1 - A4 (Formato standard)\\n' +\n      '2 - A3 (Formato esteso)\\n\\n' +\n      'Inserisci 1 o 2:',\n      '1'\n    );\n\n    if (formato === '1' || formato === '2') {\n      const formatoNome = formato === '1' ? 'A4' : 'A3';\n      console.log(`📄 Generazione comanda ${comanda.codice_comanda} in formato ${formatoNome}`);\n\n      // TODO: Implementare la generazione del PDF\n      alert(`Funzionalità in sviluppo!\\n\\nComanda: ${comanda.codice_comanda}\\nFormato: ${formatoNome}\\nTipo: ${comanda.tipo_comanda}\\nResponsabile: ${comanda.responsabile}\\nCavi: ${comanda.numero_cavi_assegnati || 0}`);\n    }\n  };\n\n  const getTipoComandaLabel = (tipo) => {\n    const labels = {\n      'POSA': 'Posa',\n      'COLLEGAMENTO_PARTENZA': 'Coll. Partenza',\n      'COLLEGAMENTO_ARRIVO': 'Coll. Arrivo',\n      'CERTIFICAZIONE': 'Certificazione'\n    };\n    return labels[tipo] || tipo;\n  };\n\n  const getStatoColor = (stato) => {\n    const colors = {\n      'CREATA': 'default',\n      'ASSEGNATA': 'primary',\n      'IN_CORSO': 'warning',\n      'COMPLETATA': 'success',\n      'ANNULLATA': 'error'\n    };\n    return colors[stato] || 'default';\n  };\n\n\n\n  if (loading || loadingComande) {\n    return (\n      <Box display=\"flex\" justifyContent=\"center\" alignItems=\"center\" minHeight=\"400px\">\n        <CircularProgress />\n      </Box>\n    );\n  }\n\n  return (\n    <Box sx={{ p: 3 }}>\n      {/* Header */}\n      <Box mb={3}>\n        <Typography variant=\"h4\" sx={{ fontWeight: 600, color: 'primary.main' }}>\n          {cantiereName}\n        </Typography>\n      </Box>\n\n      {error && (\n        <Alert severity=\"error\" sx={{ mb: 2 }}>\n          {error}\n        </Alert>\n      )}\n\n      {searchingComanda && (\n        <Alert severity=\"info\" sx={{ mb: 2 }}>\n          🔍 Ricerca comanda {searchingComanda} in corso...\n        </Alert>\n      )}\n\n      {/* Statistiche in stile Visualizza Cavi */}\n      {statistiche && (\n        <Paper sx={{ p: 2, mb: 3, bgcolor: 'grey.50' }}>\n          <Stack direction=\"row\" spacing={4} alignItems=\"center\" justifyContent=\"space-between\" flexWrap=\"wrap\">\n            {/* Responsabili */}\n            <Stack direction=\"row\" alignItems=\"center\" spacing={1}>\n              <PersonIcon color=\"primary\" fontSize=\"small\" />\n              <Box>\n                <Typography variant=\"h6\" fontWeight=\"bold\" sx={{ lineHeight: 1 }}>\n                  {statistiche.responsabili_attivi || 0}\n                </Typography>\n                <Typography variant=\"caption\" color=\"text.secondary\">\n                  Responsabili\n                </Typography>\n              </Box>\n            </Stack>\n\n            {/* Totale Comande */}\n            <Stack direction=\"row\" alignItems=\"center\" spacing={1}>\n              <AssignIcon color=\"info\" fontSize=\"small\" />\n              <Box>\n                <Typography variant=\"h6\" fontWeight=\"bold\" sx={{ lineHeight: 1 }}>\n                  {statistiche.totale_comande || 0}\n                </Typography>\n                <Typography variant=\"caption\" color=\"text.secondary\">\n                  Totale\n                </Typography>\n              </Box>\n            </Stack>\n\n            {/* In Corso */}\n            <Stack direction=\"row\" alignItems=\"center\" spacing={1}>\n              <CheckCircleIcon color=\"warning\" fontSize=\"small\" />\n              <Box>\n                <Typography variant=\"h6\" fontWeight=\"bold\" sx={{ lineHeight: 1 }}>\n                  {statistiche.comande_in_corso || 0}\n                </Typography>\n                <Typography variant=\"caption\" color=\"text.secondary\">\n                  In Corso\n                </Typography>\n              </Box>\n            </Stack>\n\n            {/* Completate */}\n            <Stack direction=\"row\" alignItems=\"center\" spacing={1}>\n              <VerifiedIcon color=\"success\" fontSize=\"small\" />\n              <Box>\n                <Typography variant=\"h6\" fontWeight=\"bold\" sx={{ lineHeight: 1 }}>\n                  {statistiche.comande_completate || 0}\n                </Typography>\n                <Typography variant=\"caption\" color=\"text.secondary\">\n                  Completate\n                </Typography>\n              </Box>\n            </Stack>\n\n            {/* Percentuale completamento */}\n            <Stack direction=\"row\" alignItems=\"center\" spacing={1}>\n              <Box sx={{\n                width: 32,\n                height: 32,\n                borderRadius: '50%',\n                bgcolor: (statistiche.comande_completate / (statistiche.totale_comande || 1)) >= 0.8 ? 'success.main' :\n                         (statistiche.comande_completate / (statistiche.totale_comande || 1)) >= 0.5 ? 'warning.main' : 'error.main',\n                display: 'flex',\n                alignItems: 'center',\n                justifyContent: 'center'\n              }}>\n                <Typography variant=\"caption\" fontWeight=\"bold\" color=\"white\">\n                  {statistiche.totale_comande > 0 ? Math.round((statistiche.comande_completate / statistiche.totale_comande) * 100) : 0}%\n                </Typography>\n              </Box>\n              <Box>\n                <Typography variant=\"body2\" fontWeight=\"medium\" sx={{ lineHeight: 1 }}>\n                  Completamento\n                </Typography>\n                <Typography variant=\"caption\" color=\"text.secondary\">\n                  {statistiche.comande_create || 0} create\n                </Typography>\n              </Box>\n            </Stack>\n          </Stack>\n        </Paper>\n      )}\n\n      {/* Sezione Comande - Elemento Principale */}\n      <Box>\n        <Box>\n          {/* Toolbar Comande */}\n          <Box display=\"flex\" justifyContent=\"space-between\" alignItems=\"center\" mb={3}>\n            <Typography variant=\"h5\" sx={{ fontWeight: 500, color: 'text.primary' }}>\n              Gestione Responsabili e Comande\n            </Typography>\n            <Box display=\"flex\" gap={2}>\n              <Button\n                variant=\"outlined\"\n                startIcon={<PeopleIcon />}\n                onClick={() => setOpenResponsabiliPopup(true)}\n                sx={{\n                  textTransform: 'none',\n                  fontWeight: 500,\n                  px: 3,\n                  py: 1,\n                  backgroundColor: '#f5f7fa',\n                  color: '#2196f3',\n                  border: '1px solid #2196f3',\n                  '&:hover': {\n                    backgroundColor: 'rgba(33, 150, 243, 0.1)',\n                    borderColor: '#1976d2'\n                  }\n                }}\n              >\n                Lista Responsabili\n              </Button>\n              <Button\n                variant=\"contained\"\n                startIcon={<AddIcon />}\n                onClick={() => handleOpenResponsabileDialog('create')}\n                sx={{\n                  textTransform: 'none',\n                  fontWeight: 500,\n                  px: 3,\n                  py: 1,\n                  backgroundColor: '#2196f3',\n                  color: 'white',\n                  '&:hover': {\n                    backgroundColor: '#1976d2'\n                  }\n                }}\n              >\n                Crea Responsabile\n              </Button>\n            </Box>\n          </Box>\n\n          {/* Lista Comande in stile tabella */}\n          {loadingComande ? (\n            <Box display=\"flex\" justifyContent=\"center\" py={4}>\n              <CircularProgress />\n            </Box>\n          ) : allComande.length === 0 ? (\n            <Paper\n              elevation={0}\n              sx={{\n                p: 6,\n                textAlign: 'center',\n                backgroundColor: 'grey.50',\n                border: '1px dashed',\n                borderColor: 'grey.300'\n              }}\n            >\n              <AssignIcon sx={{ fontSize: 48, color: 'grey.400', mb: 2 }} />\n              <Typography variant=\"h6\" color=\"text.secondary\" gutterBottom>\n                Nessuna comanda disponibile\n              </Typography>\n              <Typography variant=\"body2\" color=\"text.secondary\" sx={{ mb: 3 }}>\n                Crea la prima comanda per iniziare a gestire i lavori\n              </Typography>\n              <Button\n                variant=\"contained\"\n                startIcon={<AddIcon />}\n                onClick={() => setOpenCreaConCavi(true)}\n                sx={{\n                  textTransform: 'none',\n                  backgroundColor: '#2196f3',\n                  color: 'white',\n                  '&:hover': {\n                    backgroundColor: '#1976d2'\n                  }\n                }}\n              >\n                Crea Prima Comanda\n              </Button>\n            </Paper>\n          ) : (\n            <ComandeListTable\n              comande={allComande}\n              onEditComanda={handleOpenComandaDialog}\n              onDeleteComanda={handleDeleteComanda}\n              onInserimentoMetri={handleOpenInserimentoMetri}\n              onPrintComanda={handlePrintComanda}\n              loading={loadingComande}\n            />\n          )}\n        </Box>\n      </Box>\n\n      {/* Dialog per creazione/modifica responsabile */}\n      <Dialog\n        open={openResponsabileDialog}\n        onClose={handleCloseResponsabileDialog}\n        maxWidth=\"sm\"\n        fullWidth\n        PaperProps={{\n          sx: { borderRadius: 2 }\n        }}\n      >\n        <DialogTitle sx={{ pb: 1 }}>\n          <Typography variant=\"h6\" sx={{ fontWeight: 600 }}>\n            {dialogModeResponsabile === 'create' ? 'Inserisci Responsabile' : 'Modifica Responsabile'}\n          </Typography>\n        </DialogTitle>\n        <DialogContent>\n          <Box sx={{ pt: 2 }}>\n            <TextField\n              fullWidth\n              label=\"Nome Responsabile\"\n              value={formDataResponsabile.nome_responsabile}\n              onChange={(e) => setFormDataResponsabile({ ...formDataResponsabile, nome_responsabile: e.target.value })}\n              margin=\"normal\"\n              required\n              variant=\"outlined\"\n              sx={{ mb: 2 }}\n            />\n\n            <TextField\n              fullWidth\n              label=\"Email\"\n              type=\"email\"\n              value={formDataResponsabile.email}\n              onChange={(e) => setFormDataResponsabile({ ...formDataResponsabile, email: e.target.value })}\n              margin=\"normal\"\n              variant=\"outlined\"\n              helperText=\"Email per notifiche (opzionale se inserisci telefono)\"\n              sx={{ mb: 2 }}\n            />\n\n            <TextField\n              fullWidth\n              label=\"Telefono\"\n              value={formDataResponsabile.telefono}\n              onChange={(e) => setFormDataResponsabile({ ...formDataResponsabile, telefono: e.target.value })}\n              margin=\"normal\"\n              variant=\"outlined\"\n              helperText=\"Numero per SMS (opzionale se inserisci email)\"\n            />\n          </Box>\n        </DialogContent>\n        <DialogActions sx={{ p: 3, pt: 2 }}>\n          <Button\n            onClick={handleCloseResponsabileDialog}\n            sx={{ textTransform: 'none' }}\n          >\n            Annulla\n          </Button>\n          <Button\n            onClick={handleSubmitResponsabile}\n            variant=\"contained\"\n            sx={{\n              textTransform: 'none',\n              fontWeight: 500,\n              px: 3\n            }}\n          >\n            {dialogModeResponsabile === 'create' ? 'Crea' : 'Salva'}\n          </Button>\n        </DialogActions>\n      </Dialog>\n\n      {/* Dialog per modifica comanda */}\n      <Dialog\n        open={openComandaDialog}\n        onClose={handleCloseComandaDialog}\n        maxWidth=\"md\"\n        fullWidth\n        PaperProps={{\n          sx: { borderRadius: 2 }\n        }}\n      >\n        <DialogTitle sx={{ pb: 1 }}>\n          <Typography variant=\"h6\" sx={{ fontWeight: 600 }}>\n            Modifica Comanda {selectedComanda?.codice_comanda}\n          </Typography>\n          <Typography variant=\"body2\" color=\"text.secondary\">\n            Stato: {selectedComanda?.stato} • Cavi assegnati: {selectedComanda?.numero_cavi_assegnati || 0}\n          </Typography>\n        </DialogTitle>\n        <DialogContent>\n          <Box sx={{ pt: 2 }}>\n            <Alert severity=\"warning\" sx={{ mb: 2 }}>\n              <Typography variant=\"body2\">\n                <strong>Attenzione:</strong> Modificare il tipo di comanda riassegnerà automaticamente tutti i cavi al nuovo tipo.\n                Assicurati che i cavi siano compatibili con il nuovo tipo di operazione.\n              </Typography>\n            </Alert>\n\n            <TextField\n              fullWidth\n              select\n              label=\"Tipo Comanda\"\n              value={formDataComanda.tipo_comanda}\n              onChange={(e) => setFormDataComanda({ ...formDataComanda, tipo_comanda: e.target.value })}\n              margin=\"normal\"\n              sx={{ mb: 2 }}\n            >\n              <MenuItem value=\"POSA\">Posa</MenuItem>\n              <MenuItem value=\"COLLEGAMENTO_PARTENZA\">Collegamento Partenza</MenuItem>\n              <MenuItem value=\"COLLEGAMENTO_ARRIVO\">Collegamento Arrivo</MenuItem>\n              <MenuItem value=\"CERTIFICAZIONE\">Certificazione</MenuItem>\n            </TextField>\n\n            <TextField\n              fullWidth\n              label=\"Descrizione\"\n              value={formDataComanda.descrizione}\n              onChange={(e) => setFormDataComanda({ ...formDataComanda, descrizione: e.target.value })}\n              margin=\"normal\"\n              multiline\n              rows={3}\n              sx={{ mb: 2 }}\n            />\n\n            <TextField\n              fullWidth\n              label=\"Responsabile\"\n              value={formDataComanda.responsabile}\n              onChange={(e) => setFormDataComanda({ ...formDataComanda, responsabile: e.target.value })}\n              margin=\"normal\"\n              required\n              helperText=\"Chi eseguirà il lavoro (obbligatorio)\"\n              sx={{ mb: 2 }}\n            />\n\n            <TextField\n              fullWidth\n              label=\"Note Capo Cantiere\"\n              value={formDataComanda.note_capo_cantiere}\n              onChange={(e) => setFormDataComanda({ ...formDataComanda, note_capo_cantiere: e.target.value })}\n              margin=\"normal\"\n              multiline\n              rows={2}\n              helperText=\"Istruzioni specifiche per il responsabile\"\n              sx={{ mb: 2 }}\n            />\n\n            <TextField\n              fullWidth\n              label=\"Data Scadenza\"\n              type=\"date\"\n              value={formDataComanda.data_scadenza}\n              onChange={(e) => setFormDataComanda({ ...formDataComanda, data_scadenza: e.target.value })}\n              margin=\"normal\"\n              InputLabelProps={{\n                shrink: true,\n              }}\n            />\n          </Box>\n        </DialogContent>\n        <DialogActions sx={{ p: 3, pt: 2 }}>\n          <Button\n            onClick={handleCloseComandaDialog}\n            sx={{ textTransform: 'none' }}\n          >\n            Annulla\n          </Button>\n          <Button\n            onClick={handleSubmitComanda}\n            variant=\"contained\"\n            sx={{\n              textTransform: 'none',\n              fontWeight: 500,\n              px: 3\n            }}\n          >\n            Salva Modifiche\n          </Button>\n        </DialogActions>\n      </Dialog>\n\n      {/* Dialog CreaComandaConCavi */}\n      <CreaComandaConCavi\n        cantiereId={cantiereId}\n        open={openCreaConCavi}\n        onClose={() => setOpenCreaConCavi(false)}\n        onSuccess={(response, successMessage) => {\n          console.log('🎉 Comanda creata, aggiornamento interfaccia...');\n\n          // Mostra messaggio di successo se fornito\n          if (successMessage) {\n            // Potresti aggiungere qui un toast/snackbar per mostrare il messaggio\n            console.log('📢 Successo:', successMessage);\n          }\n\n          // Ricarica tutti i dati per aggiornare l'interfaccia\n          loadComande();\n          loadStatistiche();\n          loadResponsabili();\n          setOpenCreaConCavi(false);\n\n          console.log('✅ Interfaccia aggiornata');\n        }}\n      />\n\n      {/* Popup Lista Responsabili */}\n      <ResponsabiliListPopup\n        open={openResponsabiliPopup}\n        onClose={() => setOpenResponsabiliPopup(false)}\n        responsabili={responsabili}\n        comandePerResponsabile={comandePerResponsabile}\n        onEditResponsabile={(responsabile) => {\n          setOpenResponsabiliPopup(false);\n          handleOpenResponsabileDialog('edit', responsabile);\n        }}\n        onDeleteResponsabile={async (idResponsabile) => {\n          await handleDeleteResponsabile(idResponsabile);\n          setOpenResponsabiliPopup(false);\n        }}\n        loading={loadingResponsabili}\n        error={error}\n      />\n\n      {/* Dialog Inserimento Metri */}\n      <InserimentoMetriDialog\n        open={openInserimentoMetri}\n        onClose={handleCloseInserimentoMetri}\n        comanda={comandaPerMetri}\n        onSuccess={handleSuccessInserimentoMetri}\n      />\n\n      {/* Dialog Collegamenti */}\n      <CollegamentiDialog\n        open={openCollegamenti}\n        onClose={handleCloseCollegamenti}\n        comanda={comandaPerCollegamenti}\n        onSuccess={handleSuccessCollegamenti}\n      />\n    </Box>\n  );\n};\n\nexport default ComandeListRivoluzionato;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAClD,SAASC,eAAe,QAAQ,kBAAkB;AAClD,SACEC,GAAG,EACHC,UAAU,EACVC,MAAM,EACNC,KAAK,EACLC,IAAI,EACJC,MAAM,EACNC,WAAW,EACXC,aAAa,EACbC,aAAa,EACbC,SAAS,EACTC,KAAK,EACLC,gBAAgB,EAChBC,IAAI,EACJC,QAAQ,EACRC,YAAY,EACZC,KAAK,EACLC,QAAQ,EACRC,OAAO,QACF,eAAe;AACtB,SACEC,GAAG,IAAIC,OAAO,EACdC,UAAU,IAAIC,UAAU,EACxBC,MAAM,IAAIC,UAAU,EACpBC,WAAW,IAAIC,eAAe,EAC9BC,QAAQ,IAAIC,YAAY,EACxBC,MAAM,IAAIC,UAAU,EACpBC,YAAY,IAAIC,gBAAgB,QAC3B,qBAAqB;AAC5B,OAAOC,cAAc,MAAM,+BAA+B;AAC1D,OAAOC,mBAAmB,MAAM,oCAAoC;AACpE,OAAOC,kBAAkB,MAAM,sBAAsB;AACrD,OAAOC,qBAAqB,MAAM,yBAAyB;AAC3D,OAAOC,gBAAgB,MAAM,oBAAoB;AACjD,OAAOC,sBAAsB,MAAM,0BAA0B;AAC7D,OAAOC,kBAAkB,MAAM,sBAAsB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAEtD,MAAMC,wBAAwB,GAAGA,CAAC;EAAEC,UAAU;EAAEC;AAAa,CAAC,KAAK;EAAAC,EAAA;EACjE;EACA,MAAM,CAACC,YAAY,EAAEC,eAAe,CAAC,GAAG/C,eAAe,CAAC,CAAC;;EAEzD;EACA,MAAM,CAACgD,OAAO,EAAEC,UAAU,CAAC,GAAGnD,QAAQ,CAAC,IAAI,CAAC;EAC5C,MAAM,CAACoD,KAAK,EAAEC,QAAQ,CAAC,GAAGrD,QAAQ,CAAC,IAAI,CAAC;EACxC,MAAM,CAACsD,gBAAgB,EAAEC,mBAAmB,CAAC,GAAGvD,QAAQ,CAAC,IAAI,CAAC;;EAE9D;EACA,MAAM,CAACwD,WAAW,EAAEC,cAAc,CAAC,GAAGzD,QAAQ,CAAC,IAAI,CAAC;EACpD,MAAM,CAAC0D,UAAU,EAAEC,aAAa,CAAC,GAAG3D,QAAQ,CAAC,EAAE,CAAC;EAChD,MAAM,CAAC4D,cAAc,EAAEC,iBAAiB,CAAC,GAAG7D,QAAQ,CAAC,KAAK,CAAC;;EAE3D;EACA,MAAM,CAAC8D,qBAAqB,EAAEC,wBAAwB,CAAC,GAAG/D,QAAQ,CAAC,KAAK,CAAC;EACzE,MAAM,CAACgE,eAAe,EAAEC,kBAAkB,CAAC,GAAGjE,QAAQ,CAAC,KAAK,CAAC;;EAE7D;EACA,MAAM,CAACkE,YAAY,EAAEC,eAAe,CAAC,GAAGnE,QAAQ,CAAC,EAAE,CAAC;EACpD,MAAM,CAACoE,mBAAmB,EAAEC,sBAAsB,CAAC,GAAGrE,QAAQ,CAAC,KAAK,CAAC;EACrE,MAAM,CAACsE,sBAAsB,EAAEC,yBAAyB,CAAC,GAAGvE,QAAQ,CAAC,CAAC,CAAC,CAAC;EACxE,MAAM,CAACwE,sBAAsB,EAAEC,yBAAyB,CAAC,GAAGzE,QAAQ,CAAC,KAAK,CAAC;EAC3E,MAAM,CAAC0E,sBAAsB,EAAEC,yBAAyB,CAAC,GAAG3E,QAAQ,CAAC,QAAQ,CAAC;EAC9E,MAAM,CAAC4E,oBAAoB,EAAEC,uBAAuB,CAAC,GAAG7E,QAAQ,CAAC,IAAI,CAAC;EACtE,MAAM,CAAC8E,oBAAoB,EAAEC,uBAAuB,CAAC,GAAG/E,QAAQ,CAAC;IAC/DgF,iBAAiB,EAAE,EAAE;IACrBC,KAAK,EAAE,EAAE;IACTC,QAAQ,EAAE;EACZ,CAAC,CAAC;;EAEF;EACA,MAAM,CAACC,iBAAiB,EAAEC,oBAAoB,CAAC,GAAGpF,QAAQ,CAAC,KAAK,CAAC;EACjE,MAAM,CAACqF,eAAe,EAAEC,kBAAkB,CAAC,GAAGtF,QAAQ,CAAC,IAAI,CAAC;EAC5D,MAAM,CAACuF,eAAe,EAAEC,kBAAkB,CAAC,GAAGxF,QAAQ,CAAC;IACrDyF,YAAY,EAAE,MAAM;IACpBC,WAAW,EAAE,EAAE;IACfC,YAAY,EAAE,EAAE;IAChBC,aAAa,EAAE,EAAE;IACjBC,kBAAkB,EAAE;EACtB,CAAC,CAAC;;EAEF;EACA,MAAM,CAACC,oBAAoB,EAAEC,uBAAuB,CAAC,GAAG/F,QAAQ,CAAC,KAAK,CAAC;EACvE,MAAM,CAACgG,eAAe,EAAEC,kBAAkB,CAAC,GAAGjG,QAAQ,CAAC,IAAI,CAAC;;EAE5D;EACA,MAAM,CAACkG,gBAAgB,EAAEC,mBAAmB,CAAC,GAAGnG,QAAQ,CAAC,KAAK,CAAC;EAC/D,MAAM,CAACoG,sBAAsB,EAAEC,yBAAyB,CAAC,GAAGrG,QAAQ,CAAC,IAAI,CAAC;EAE1E,MAAMsG,WAAW,GAAG,MAAAA,CAAA,KAAY;IAC9B,IAAI;MACFzC,iBAAiB,CAAC,IAAI,CAAC;MACvB0C,OAAO,CAACC,GAAG,CAAC,sCAAsC,EAAE3D,UAAU,CAAC;MAC/D,MAAM4D,SAAS,GAAGC,WAAW,CAACC,GAAG,CAAC,CAAC;MAEnC,MAAMC,WAAW,GAAG,MAAMzE,cAAc,CAAC0E,UAAU,CAAChE,UAAU,CAAC;;MAE/D;MACA,IAAIiE,YAAY,GAAG,EAAE;MACrB,IAAIC,KAAK,CAACC,OAAO,CAACJ,WAAW,CAAC,EAAE;QAC9BE,YAAY,GAAGF,WAAW;MAC5B,CAAC,MAAM,IAAIA,WAAW,IAAIG,KAAK,CAACC,OAAO,CAACJ,WAAW,CAACK,OAAO,CAAC,EAAE;QAC5DH,YAAY,GAAGF,WAAW,CAACK,OAAO;MACpC,CAAC,MAAM,IAAIL,WAAW,IAAIG,KAAK,CAACC,OAAO,CAACJ,WAAW,CAACM,IAAI,CAAC,EAAE;QACzDJ,YAAY,GAAGF,WAAW,CAACM,IAAI;MACjC;MAEA,MAAMC,OAAO,GAAGT,WAAW,CAACC,GAAG,CAAC,CAAC;MACjCJ,OAAO,CAACC,GAAG,CAAC,KAAKM,YAAY,CAACM,MAAM,wBAAwB,CAACD,OAAO,GAAGV,SAAS,EAAEY,OAAO,CAAC,CAAC,CAAC,IAAI,CAAC;MAEjG1D,aAAa,CAACmD,YAAY,CAAC;IAC7B,CAAC,CAAC,OAAOQ,GAAG,EAAE;MACZf,OAAO,CAACnD,KAAK,CAAC,mCAAmC,EAAEkE,GAAG,CAAC;MACvDjE,QAAQ,CAAC,sCAAsC,CAAC;MAChDM,aAAa,CAAC,EAAE,CAAC;IACnB,CAAC,SAAS;MACRE,iBAAiB,CAAC,KAAK,CAAC;IAC1B;EACF,CAAC;EAED,MAAM0D,eAAe,GAAG,MAAAA,CAAA,KAAY;IAClC,IAAI;MACFhB,OAAO,CAACC,GAAG,CAAC,0CAA0C,EAAE3D,UAAU,CAAC;MACnE,MAAM4D,SAAS,GAAGC,WAAW,CAACC,GAAG,CAAC,CAAC;MAEnC,MAAMa,KAAK,GAAG,MAAMrF,cAAc,CAACsF,qBAAqB,CAAC5E,UAAU,CAAC;MAEpE,MAAMsE,OAAO,GAAGT,WAAW,CAACC,GAAG,CAAC,CAAC;MACjCJ,OAAO,CAACC,GAAG,CAAC,8BAA8B,CAACW,OAAO,GAAGV,SAAS,EAAEY,OAAO,CAAC,CAAC,CAAC,KAAK,EAAEG,KAAK,CAAC;MAEvF/D,cAAc,CAAC+D,KAAK,CAAC;IACvB,CAAC,CAAC,OAAOF,GAAG,EAAE;MAAA,IAAAI,aAAA;MACZnB,OAAO,CAACnD,KAAK,CAAC,6CAA6C,EAAEkE,GAAG,CAAC;MACjEf,OAAO,CAACnD,KAAK,CAAC,oBAAoB,EAAE,EAAAsE,aAAA,GAAAJ,GAAG,CAACK,QAAQ,cAAAD,aAAA,uBAAZA,aAAA,CAAcR,IAAI,KAAII,GAAG,CAACM,OAAO,CAAC;MACtE;MACAnE,cAAc,CAAC;QACboE,mBAAmB,EAAE,CAAC;QACtBC,cAAc,EAAE,CAAC;QACjBC,cAAc,EAAE,CAAC;QACjBC,gBAAgB,EAAE,CAAC;QACnBC,kBAAkB,EAAE,CAAC;QACrBC,iBAAiB,EAAE;MACrB,CAAC,CAAC;IACJ;EACF,CAAC;EAED,MAAMC,gBAAgB,GAAG,MAAAA,CAAA,KAAY;IACnC,IAAI;MACF9D,sBAAsB,CAAC,IAAI,CAAC;MAC5BhB,QAAQ,CAAC,IAAI,CAAC;MACdkD,OAAO,CAACC,GAAG,CAAC,2CAA2C,EAAE3D,UAAU,CAAC;MACpE,MAAM4D,SAAS,GAAGC,WAAW,CAACC,GAAG,CAAC,CAAC;MAEnC,MAAMO,IAAI,GAAG,MAAM9E,mBAAmB,CAACgG,uBAAuB,CAACvF,UAAU,CAAC;MAE1E,MAAMsE,OAAO,GAAGT,WAAW,CAACC,GAAG,CAAC,CAAC;MACjCJ,OAAO,CAACC,GAAG,CAAC,KAAK,CAACU,IAAI,IAAI,EAAE,EAAEE,MAAM,6BAA6B,CAACD,OAAO,GAAGV,SAAS,EAAEY,OAAO,CAAC,CAAC,CAAC,IAAI,CAAC;MAEtGlD,eAAe,CAAC+C,IAAI,IAAI,EAAE,CAAC;MAC3B,OAAOA,IAAI,IAAI,EAAE;IACnB,CAAC,CAAC,OAAOI,GAAG,EAAE;MAAA,IAAAe,cAAA,EAAAC,mBAAA;MACZ/B,OAAO,CAACnD,KAAK,CAAC,4CAA4C,EAAEkE,GAAG,CAAC;MAChE,MAAMiB,YAAY,GAAG,EAAAF,cAAA,GAAAf,GAAG,CAACK,QAAQ,cAAAU,cAAA,wBAAAC,mBAAA,GAAZD,cAAA,CAAcnB,IAAI,cAAAoB,mBAAA,uBAAlBA,mBAAA,CAAoBE,MAAM,KAAIlB,GAAG,CAACM,OAAO,IAAI,yCAAyC;MAC3GvE,QAAQ,CAAC,4CAA4CkF,YAAY,EAAE,CAAC;MACpEpE,eAAe,CAAC,EAAE,CAAC;MACnB,OAAO,EAAE;IACX,CAAC,SAAS;MACRE,sBAAsB,CAAC,KAAK,CAAC;IAC/B;EACF,CAAC;;EAID;EACApE,SAAS,CAAC,MAAM;IACd,MAAMwI,cAAc,GAAG,MAAAA,CAAA,KAAY;MACjC,IAAI5F,UAAU,EAAE;QACdM,UAAU,CAAC,IAAI,CAAC;QAChBoD,OAAO,CAACC,GAAG,CAAC,oCAAoC,EAAE3D,UAAU,CAAC;QAC7D,MAAM6F,cAAc,GAAGhC,WAAW,CAACC,GAAG,CAAC,CAAC;QAExC,IAAI;UACF;UACA,MAAM,CAACgC,gBAAgB,EAAE/B,WAAW,EAAEgC,eAAe,CAAC,GAAG,MAAMC,OAAO,CAACC,GAAG,CAAC,CACzE1G,mBAAmB,CAACgG,uBAAuB,CAACvF,UAAU,CAAC,EACvDV,cAAc,CAAC0E,UAAU,CAAChE,UAAU,CAAC,EACrCV,cAAc,CAACsF,qBAAqB,CAAC5E,UAAU,CAAC,CACjD,CAAC;;UAEF;UACAsB,eAAe,CAACwE,gBAAgB,IAAI,EAAE,CAAC;;UAEvC;UACA,IAAI7B,YAAY,GAAG,EAAE;UACrB,IAAIC,KAAK,CAACC,OAAO,CAACJ,WAAW,CAAC,EAAE;YAC9BE,YAAY,GAAGF,WAAW;UAC5B,CAAC,MAAM,IAAIA,WAAW,IAAIG,KAAK,CAACC,OAAO,CAACJ,WAAW,CAACK,OAAO,CAAC,EAAE;YAC5DH,YAAY,GAAGF,WAAW,CAACK,OAAO;UACpC,CAAC,MAAM,IAAIL,WAAW,IAAIG,KAAK,CAACC,OAAO,CAACJ,WAAW,CAACM,IAAI,CAAC,EAAE;YACzDJ,YAAY,GAAGF,WAAW,CAACM,IAAI;UACjC;UACAvD,aAAa,CAACmD,YAAY,CAAC;;UAE3B;UACA,MAAMiC,UAAU,GAAG,CAAC,CAAC;UACrB,CAACJ,gBAAgB,IAAI,EAAE,EAAEK,OAAO,CAACrD,YAAY,IAAI;YAC/CoD,UAAU,CAACpD,YAAY,CAACsD,eAAe,CAAC,GAAG,EAAE;UAC/C,CAAC,CAAC;UACFnC,YAAY,CAACkC,OAAO,CAACE,OAAO,IAAI;YAC9B,MAAMvD,YAAY,GAAG,CAACgD,gBAAgB,IAAI,EAAE,EAAEQ,IAAI,CAACC,CAAC,IAAIA,CAAC,CAACpE,iBAAiB,KAAKkE,OAAO,CAACvD,YAAY,CAAC;YACrG,IAAIA,YAAY,EAAE;cAChBoD,UAAU,CAACpD,YAAY,CAACsD,eAAe,CAAC,CAACI,IAAI,CAACH,OAAO,CAAC;YACxD;UACF,CAAC,CAAC;UACF3E,yBAAyB,CAACwE,UAAU,CAAC;;UAErC;UACAtF,cAAc,CAACmF,eAAe,IAAI;YAChCf,mBAAmB,EAAE,CAAC;YACtBC,cAAc,EAAE,CAAC;YACjBC,cAAc,EAAE,CAAC;YACjBC,gBAAgB,EAAE,CAAC;YACnBC,kBAAkB,EAAE,CAAC;YACrBC,iBAAiB,EAAE;UACrB,CAAC,CAAC;UAEF,MAAMoB,YAAY,GAAG5C,WAAW,CAACC,GAAG,CAAC,CAAC;UACtCJ,OAAO,CAACC,GAAG,CAAC,oCAAoC,CAAC8C,YAAY,GAAGZ,cAAc,EAAErB,OAAO,CAAC,CAAC,CAAC,IAAI,CAAC;UAC/Fd,OAAO,CAACC,GAAG,CAAC,qBAAqB,CAACmC,gBAAgB,IAAI,EAAE,EAAEvB,MAAM,kBAAkBN,YAAY,CAACM,MAAM,UAAU,CAAC;QAElH,CAAC,CAAC,OAAOE,GAAG,EAAE;UACZf,OAAO,CAACnD,KAAK,CAAC,oCAAoC,EAAEkE,GAAG,CAAC;UACxDjE,QAAQ,CAAC,iCAAiC,CAAC;UAC3C;UACAc,eAAe,CAAC,EAAE,CAAC;UACnBR,aAAa,CAAC,EAAE,CAAC;UACjBY,yBAAyB,CAAC,CAAC,CAAC,CAAC;UAC7Bd,cAAc,CAAC;YACboE,mBAAmB,EAAE,CAAC;YACtBC,cAAc,EAAE,CAAC;YACjBC,cAAc,EAAE,CAAC;YACjBC,gBAAgB,EAAE,CAAC;YACnBC,kBAAkB,EAAE,CAAC;YACrBC,iBAAiB,EAAE;UACrB,CAAC,CAAC;QACJ,CAAC,SAAS;UACR/E,UAAU,CAAC,KAAK,CAAC;UACjBU,iBAAiB,CAAC,KAAK,CAAC;UACxBQ,sBAAsB,CAAC,KAAK,CAAC;QAC/B;MACF;IACF,CAAC;IAEDoE,cAAc,CAAC,CAAC;EAClB,CAAC,EAAE,CAAC5F,UAAU,CAAC,CAAC,CAAC,CAAC;;EAElB;EACA5C,SAAS,CAAC,MAAM;IACd,MAAMsJ,YAAY,GAAGvG,YAAY,CAACwG,GAAG,CAAC,SAAS,CAAC;IAChDjD,OAAO,CAACC,GAAG,CAAC,qCAAqC,EAAE+C,YAAY,CAAC;IAChEhD,OAAO,CAACC,GAAG,CAAC,gBAAgB,EAAE;MAC5BtC,YAAY,EAAEA,YAAY,CAACkD,MAAM;MACjC9C,sBAAsB,EAAEmF,MAAM,CAACC,IAAI,CAACpF,sBAAsB,CAAC,CAAC8C,MAAM;MAClElE,OAAO;MACPkB;IACF,CAAC,CAAC;;IAEF;IACA,IAAImF,YAAY,IAAIA,YAAY,KAAKjG,gBAAgB,EAAE;MACrDC,mBAAmB,CAACgG,YAAY,CAAC;IACnC;IAEA,IAAIA,YAAY,IAAIrF,YAAY,CAACkD,MAAM,GAAG,CAAC,IAAIqC,MAAM,CAACC,IAAI,CAACpF,sBAAsB,CAAC,CAAC8C,MAAM,GAAG,CAAC,EAAE;MAC7Fb,OAAO,CAACC,GAAG,CAAC,0CAA0C,CAAC;;MAEvD;MACA,IAAImD,cAAc,GAAG,IAAI;MAEzB,KAAK,MAAMhE,YAAY,IAAIzB,YAAY,EAAE;QACvC,MAAM0F,WAAW,GAAGtF,sBAAsB,CAACqB,YAAY,CAACsD,eAAe,CAAC,IAAI,EAAE;QAC9E1C,OAAO,CAACC,GAAG,CAAC,mBAAmBb,YAAY,CAACX,iBAAiB,KAAK4E,WAAW,CAACxC,MAAM,UAAU,CAAC;QAC/FuC,cAAc,GAAGC,WAAW,CAACT,IAAI,CAACU,CAAC,IAAIA,CAAC,CAACC,cAAc,KAAKP,YAAY,CAAC;QACzE,IAAII,cAAc,EAAE;UAClBpD,OAAO,CAACC,GAAG,CAAC,oBAAoB,EAAEmD,cAAc,CAAC;UACjD;QACF;MACF;MAEA,IAAIA,cAAc,EAAE;QAClBpD,OAAO,CAACC,GAAG,CAAC,wCAAwC,EAAE+C,YAAY,CAAC;QACnEhG,mBAAmB,CAAC,IAAI,CAAC,CAAC,CAAC;;QAE3B;QACA;QACA,MAAMwG,iBAAiB,GAAG,CAAC,MAAM,EAAE,uBAAuB,EAAE,qBAAqB,CAAC,CAACC,QAAQ,CAACL,cAAc,CAAClE,YAAY,CAAC;QAExH,IAAIsE,iBAAiB,EAAE;UACrBxD,OAAO,CAACC,GAAG,CAAC,oCAAoCmD,cAAc,CAAClE,YAAY,sBAAsB,EAAE8D,YAAY,CAAC;UAChHU,0BAA0B,CAACN,cAAc,CAAC;QAC5C,CAAC,MAAM;UACLpD,OAAO,CAACC,GAAG,CAAC,gDAAgD,EAAE+C,YAAY,CAAC;UAC3EW,uBAAuB,CAACP,cAAc,CAAC;QACzC;;QAEA;QACAQ,UAAU,CAAC,MAAM;UACflH,eAAe,CAACmH,IAAI,IAAI;YACtB,MAAMC,SAAS,GAAG,IAAIC,eAAe,CAACF,IAAI,CAAC;YAC3CC,SAAS,CAACE,MAAM,CAAC,SAAS,CAAC;YAC3B,OAAOF,SAAS;UAClB,CAAC,CAAC;QACJ,CAAC,EAAE,GAAG,CAAC;MACT,CAAC,MAAM;QACL9D,OAAO,CAACiE,IAAI,CAAC,yBAAyB,EAAEjB,YAAY,CAAC;QACrDhD,OAAO,CAACC,GAAG,CAAC,yBAAyB,CAAC;QACtCtC,YAAY,CAAC8E,OAAO,CAACyB,IAAI,IAAI;UAC3B,MAAMxD,OAAO,GAAG3C,sBAAsB,CAACmG,IAAI,CAACxB,eAAe,CAAC,IAAI,EAAE;UAClEhC,OAAO,CAAC+B,OAAO,CAAC0B,GAAG,IAAI;YACrBnE,OAAO,CAACC,GAAG,CAAC,OAAOkE,GAAG,CAACZ,cAAc,KAAKW,IAAI,CAACzF,iBAAiB,GAAG,CAAC;UACtE,CAAC,CAAC;QACJ,CAAC,CAAC;;QAEF;QACA;QACA,IAAI,CAAC9B,OAAO,IAAI,CAACkB,mBAAmB,EAAE;UACpCmC,OAAO,CAACC,GAAG,CAAC,uCAAuC,CAAC;UACpD2D,UAAU,CAAC,MAAM;YACfhC,gBAAgB,CAAC,CAAC;UACpB,CAAC,EAAE,GAAG,CAAC;QACT;MACF;IACF,CAAC,MAAM,IAAIoB,YAAY,EAAE;MACvBhD,OAAO,CAACC,GAAG,CAAC,wCAAwC,CAAC;;MAErD;MACA,IAAI,CAACtD,OAAO,IAAI,CAACkB,mBAAmB,IAAIF,YAAY,CAACkD,MAAM,KAAK,CAAC,EAAE;QACjEb,OAAO,CAACC,GAAG,CAAC,0CAA0C,CAAC;QACvD2B,gBAAgB,CAAC,CAAC;MACpB;IACF;EACF,CAAC,EAAE,CAACnF,YAAY,EAAEkB,YAAY,EAAEI,sBAAsB,EAAEpB,OAAO,EAAEkB,mBAAmB,CAAC,CAAC,CAAC,CAAC;;EAExF,MAAMuG,0BAA0B,GAAG,MAAOC,gBAAgB,IAAK;IAC7D,IAAI;MACFrE,OAAO,CAACC,GAAG,CAAC,wDAAwD,CAAC;MACrE,MAAMC,SAAS,GAAGC,WAAW,CAACC,GAAG,CAAC,CAAC;;MAEnC;MACA,MAAMjD,UAAU,GAAG,MAAMvB,cAAc,CAAC0E,UAAU,CAAChE,UAAU,CAAC;;MAE9D;MACA,IAAIiE,YAAY,GAAG,EAAE;MACrB,IAAIC,KAAK,CAACC,OAAO,CAACtD,UAAU,CAAC,EAAE;QAC7BoD,YAAY,GAAGpD,UAAU;MAC3B,CAAC,MAAM,IAAIA,UAAU,IAAIqD,KAAK,CAACC,OAAO,CAACtD,UAAU,CAACuD,OAAO,CAAC,EAAE;QAC1DH,YAAY,GAAGpD,UAAU,CAACuD,OAAO;MACnC,CAAC,MAAM,IAAIvD,UAAU,IAAIqD,KAAK,CAACC,OAAO,CAACtD,UAAU,CAACwD,IAAI,CAAC,EAAE;QACvDJ,YAAY,GAAGpD,UAAU,CAACwD,IAAI;MAChC;;MAEA;MACA,MAAM6B,UAAU,GAAG,CAAC,CAAC;;MAErB;MACA6B,gBAAgB,CAAC5B,OAAO,CAACrD,YAAY,IAAI;QACvCoD,UAAU,CAACpD,YAAY,CAACsD,eAAe,CAAC,GAAG,EAAE;MAC/C,CAAC,CAAC;;MAEF;MACAnC,YAAY,CAACkC,OAAO,CAACE,OAAO,IAAI;QAC9B,MAAMvD,YAAY,GAAGiF,gBAAgB,CAACzB,IAAI,CAACC,CAAC,IAAIA,CAAC,CAACpE,iBAAiB,KAAKkE,OAAO,CAACvD,YAAY,CAAC;QAC7F,IAAIA,YAAY,EAAE;UAChBoD,UAAU,CAACpD,YAAY,CAACsD,eAAe,CAAC,CAACI,IAAI,CAACH,OAAO,CAAC;QACxD;MACF,CAAC,CAAC;MAEF,MAAM/B,OAAO,GAAGT,WAAW,CAACC,GAAG,CAAC,CAAC;MACjCJ,OAAO,CAACC,GAAG,CAAC,yBAAyB,CAACW,OAAO,GAAGV,SAAS,EAAEY,OAAO,CAAC,CAAC,CAAC,4BAA4BuD,gBAAgB,CAACxD,MAAM,GAAG,CAAC;MAE5H7C,yBAAyB,CAACwE,UAAU,CAAC;IACvC,CAAC,CAAC,OAAOzB,GAAG,EAAE;MACZf,OAAO,CAACnD,KAAK,CAAC,yCAAyC,EAAEkE,GAAG,CAAC;MAC7D;MACA,MAAMyB,UAAU,GAAG,CAAC,CAAC;MACrB6B,gBAAgB,CAAC5B,OAAO,CAACrD,YAAY,IAAI;QACvCoD,UAAU,CAACpD,YAAY,CAACsD,eAAe,CAAC,GAAG,EAAE;MAC/C,CAAC,CAAC;MACF1E,yBAAyB,CAACwE,UAAU,CAAC;IACvC;EACF,CAAC;;EAED;EACA,MAAM8B,4BAA4B,GAAGA,CAACC,IAAI,EAAEnF,YAAY,GAAG,IAAI,KAAK;IAClEhB,yBAAyB,CAACmG,IAAI,CAAC;IAC/BjG,uBAAuB,CAACc,YAAY,CAAC;IAErC,IAAImF,IAAI,KAAK,MAAM,IAAInF,YAAY,EAAE;MACnCZ,uBAAuB,CAAC;QACtBC,iBAAiB,EAAEW,YAAY,CAACX,iBAAiB,IAAI,EAAE;QACvDC,KAAK,EAAEU,YAAY,CAACV,KAAK,IAAI,EAAE;QAC/BC,QAAQ,EAAES,YAAY,CAACT,QAAQ,IAAI;MACrC,CAAC,CAAC;IACJ,CAAC,MAAM;MACLH,uBAAuB,CAAC;QACtBC,iBAAiB,EAAE,EAAE;QACrBC,KAAK,EAAE,EAAE;QACTC,QAAQ,EAAE;MACZ,CAAC,CAAC;IACJ;IAEAT,yBAAyB,CAAC,IAAI,CAAC;EACjC,CAAC;EAED,MAAMsG,6BAA6B,GAAGA,CAAA,KAAM;IAC1CtG,yBAAyB,CAAC,KAAK,CAAC;IAChCI,uBAAuB,CAAC,IAAI,CAAC;IAC7BxB,QAAQ,CAAC,IAAI,CAAC;EAChB,CAAC;EAED,MAAM2H,wBAAwB,GAAG,MAAAA,CAAA,KAAY;IAC3C,IAAI;MACF3H,QAAQ,CAAC,IAAI,CAAC;MAEd,IAAI,CAACyB,oBAAoB,CAACE,iBAAiB,CAACiG,IAAI,CAAC,CAAC,EAAE;QAClD5H,QAAQ,CAAC,yCAAyC,CAAC;QACnD;MACF;MAEA,IAAI,CAACyB,oBAAoB,CAACG,KAAK,IAAI,CAACH,oBAAoB,CAACI,QAAQ,EAAE;QACjE7B,QAAQ,CAAC,yDAAyD,CAAC;QACnE;MACF;MAEA,IAAIqB,sBAAsB,KAAK,QAAQ,EAAE;QACvC,MAAMtC,mBAAmB,CAAC8I,kBAAkB,CAACrI,UAAU,EAAEiC,oBAAoB,CAAC;MAChF,CAAC,MAAM,IAAIJ,sBAAsB,KAAK,MAAM,EAAE;QAC5C,MAAMtC,mBAAmB,CAAC+I,kBAAkB,CAACvG,oBAAoB,CAACqE,eAAe,EAAEnE,oBAAoB,CAAC;MAC1G;MAEAiG,6BAA6B,CAAC,CAAC;MAC/B,MAAM5C,gBAAgB,CAAC,CAAC;MACxB,MAAM7B,WAAW,CAAC,CAAC;IACrB,CAAC,CAAC,OAAOgB,GAAG,EAAE;MACZf,OAAO,CAACnD,KAAK,CAAC,yBAAyB,EAAEkE,GAAG,CAAC;MAC7CjE,QAAQ,CAACiE,GAAG,CAACkB,MAAM,IAAI,yCAAyC,CAAC;IACnE;EACF,CAAC;EAED,MAAM4C,wBAAwB,GAAG,MAAOC,cAAc,IAAK;IACzD,IAAI,CAACC,MAAM,CAACC,OAAO,CAAC,oDAAoD,CAAC,EAAE;MACzE;IACF;IAEA,IAAI;MACF,MAAMnJ,mBAAmB,CAACoJ,kBAAkB,CAACH,cAAc,CAAC;MAC5D,MAAMlD,gBAAgB,CAAC,CAAC;MACxB,MAAM7B,WAAW,CAAC,CAAC;IACrB,CAAC,CAAC,OAAOgB,GAAG,EAAE;MACZf,OAAO,CAACnD,KAAK,CAAC,4BAA4B,EAAEkE,GAAG,CAAC;MAChDjE,QAAQ,CAAC,4CAA4C,CAAC;IACxD;EACF,CAAC;;EAED;EACA,MAAM6G,uBAAuB,GAAIhB,OAAO,IAAK;IAC3C5D,kBAAkB,CAAC4D,OAAO,CAAC;IAE3B,IAAIA,OAAO,EAAE;MACX1D,kBAAkB,CAAC;QACjBC,YAAY,EAAEyD,OAAO,CAACzD,YAAY;QAClCC,WAAW,EAAEwD,OAAO,CAACxD,WAAW,IAAI,EAAE;QACtCC,YAAY,EAAEuD,OAAO,CAACvD,YAAY,IAAI,EAAE;QACxCC,aAAa,EAAEsD,OAAO,CAACtD,aAAa,IAAI,EAAE;QAC1CC,kBAAkB,EAAEqD,OAAO,CAACrD,kBAAkB,IAAI;MACpD,CAAC,CAAC;IACJ;IAEAT,oBAAoB,CAAC,IAAI,CAAC;EAC5B,CAAC;EAED,MAAMqG,wBAAwB,GAAGA,CAAA,KAAM;IACrCrG,oBAAoB,CAAC,KAAK,CAAC;IAC3BE,kBAAkB,CAAC,IAAI,CAAC;IACxBE,kBAAkB,CAAC;MACjBC,YAAY,EAAE,MAAM;MACpBC,WAAW,EAAE,EAAE;MACfC,YAAY,EAAE,EAAE;MAChBC,aAAa,EAAE,EAAE;MACjBC,kBAAkB,EAAE;IACtB,CAAC,CAAC;EACJ,CAAC;EAED,MAAM6F,mBAAmB,GAAG,MAAAA,CAAA,KAAY;IACtC,IAAI;MACF,MAAMvJ,cAAc,CAACwJ,aAAa,CAACtG,eAAe,CAACyE,cAAc,EAAEvE,eAAe,CAAC;MACnFkG,wBAAwB,CAAC,CAAC;MAC1B,MAAMtD,gBAAgB,CAAC,CAAC,CAAC,CAAC;MAC1B,MAAM7B,WAAW,CAAC,CAAC;MACnB,MAAMiB,eAAe,CAAC,CAAC;IACzB,CAAC,CAAC,OAAOD,GAAG,EAAE;MACZf,OAAO,CAACnD,KAAK,CAAC,yBAAyB,EAAEkE,GAAG,CAAC;MAC7CjE,QAAQ,CAAC,sCAAsC,CAAC;IAClD;EACF,CAAC;EAED,MAAMuI,mBAAmB,GAAG,MAAOC,aAAa,IAAK;IACnD,IAAI,CAACP,MAAM,CAACC,OAAO,CAAC,+CAA+C,CAAC,EAAE;MACpE;IACF;IAEA,IAAI;MACF,MAAMpJ,cAAc,CAAC2J,aAAa,CAACD,aAAa,CAAC;MACjD,MAAM1D,gBAAgB,CAAC,CAAC,CAAC,CAAC;MAC1B,MAAM7B,WAAW,CAAC,CAAC;MACnB,MAAMiB,eAAe,CAAC,CAAC;IACzB,CAAC,CAAC,OAAOD,GAAG,EAAE;MACZf,OAAO,CAACnD,KAAK,CAAC,4BAA4B,EAAEkE,GAAG,CAAC;MAChDjE,QAAQ,CAAC,yCAAyC,CAAC;IACrD;EACF,CAAC;;EAED;EACA,MAAM4G,0BAA0B,GAAIf,OAAO,IAAK;IAC9C,IAAIA,OAAO,CAACzD,YAAY,KAAK,MAAM,EAAE;MACnC;MACAQ,kBAAkB,CAACiD,OAAO,CAAC;MAC3BnD,uBAAuB,CAAC,IAAI,CAAC;IAC/B,CAAC,MAAM,IAAI,CAAC,uBAAuB,EAAE,qBAAqB,CAAC,CAACiE,QAAQ,CAACd,OAAO,CAACzD,YAAY,CAAC,EAAE;MAC1F;MACAY,yBAAyB,CAAC6C,OAAO,CAAC;MAClC/C,mBAAmB,CAAC,IAAI,CAAC;IAC3B,CAAC,MAAM;MACL;MACAF,kBAAkB,CAACiD,OAAO,CAAC;MAC3BnD,uBAAuB,CAAC,IAAI,CAAC;IAC/B;EACF,CAAC;EAED,MAAMgG,2BAA2B,GAAGA,CAAA,KAAM;IACxChG,uBAAuB,CAAC,KAAK,CAAC;IAC9BE,kBAAkB,CAAC,IAAI,CAAC;EAC1B,CAAC;EAED,MAAM+F,uBAAuB,GAAGA,CAAA,KAAM;IACpC7F,mBAAmB,CAAC,KAAK,CAAC;IAC1BE,yBAAyB,CAAC,IAAI,CAAC;EACjC,CAAC;EAED,MAAM4F,6BAA6B,GAAG,MAAOrE,OAAO,IAAK;IACvDrB,OAAO,CAACC,GAAG,CAAC,+BAA+B,EAAEoB,OAAO,CAAC;IACrDvE,QAAQ,CAAC,IAAI,CAAC;;IAEd;IACA,MAAMwF,OAAO,CAACC,GAAG,CAAC,CAChBX,gBAAgB,CAAC,CAAC,EAClB7B,WAAW,CAAC,CAAC,EACbiB,eAAe,CAAC,CAAC,CAClB,CAAC;;IAEF;IACA;EACF,CAAC;EAED,MAAM2E,yBAAyB,GAAG,MAAOtE,OAAO,IAAK;IACnDrB,OAAO,CAACC,GAAG,CAAC,0BAA0B,EAAEoB,OAAO,CAAC;IAChDvE,QAAQ,CAAC,IAAI,CAAC;;IAEd;IACA,MAAMwF,OAAO,CAACC,GAAG,CAAC,CAChBX,gBAAgB,CAAC,CAAC,EAClB7B,WAAW,CAAC,CAAC,EACbiB,eAAe,CAAC,CAAC,CAClB,CAAC;;IAEF;IACA;EACF,CAAC;;EAED;EACA,MAAM4E,kBAAkB,GAAIjD,OAAO,IAAK;IACtC3C,OAAO,CAACC,GAAG,CAAC,qBAAqB,EAAE0C,OAAO,CAACY,cAAc,CAAC;;IAE1D;IACA,MAAMsC,OAAO,GAAGd,MAAM,CAACe,MAAM,CAC3B,iDAAiDnD,OAAO,CAACY,cAAc,OAAO,GAC9E,6BAA6B,GAC7B,6BAA6B,GAC7B,kBAAkB,EAClB,GACF,CAAC;IAED,IAAIsC,OAAO,KAAK,GAAG,IAAIA,OAAO,KAAK,GAAG,EAAE;MACtC,MAAME,WAAW,GAAGF,OAAO,KAAK,GAAG,GAAG,IAAI,GAAG,IAAI;MACjD7F,OAAO,CAACC,GAAG,CAAC,0BAA0B0C,OAAO,CAACY,cAAc,eAAewC,WAAW,EAAE,CAAC;;MAEzF;MACAC,KAAK,CAAC,yCAAyCrD,OAAO,CAACY,cAAc,cAAcwC,WAAW,WAAWpD,OAAO,CAACzD,YAAY,mBAAmByD,OAAO,CAACvD,YAAY,WAAWuD,OAAO,CAACsD,qBAAqB,IAAI,CAAC,EAAE,CAAC;IACtN;EACF,CAAC;EAED,MAAMC,mBAAmB,GAAIC,IAAI,IAAK;IACpC,MAAMC,MAAM,GAAG;MACb,MAAM,EAAE,MAAM;MACd,uBAAuB,EAAE,gBAAgB;MACzC,qBAAqB,EAAE,cAAc;MACrC,gBAAgB,EAAE;IACpB,CAAC;IACD,OAAOA,MAAM,CAACD,IAAI,CAAC,IAAIA,IAAI;EAC7B,CAAC;EAED,MAAME,aAAa,GAAIC,KAAK,IAAK;IAC/B,MAAMC,MAAM,GAAG;MACb,QAAQ,EAAE,SAAS;MACnB,WAAW,EAAE,SAAS;MACtB,UAAU,EAAE,SAAS;MACrB,YAAY,EAAE,SAAS;MACvB,WAAW,EAAE;IACf,CAAC;IACD,OAAOA,MAAM,CAACD,KAAK,CAAC,IAAI,SAAS;EACnC,CAAC;EAID,IAAI3J,OAAO,IAAIU,cAAc,EAAE;IAC7B,oBACEjB,OAAA,CAACxC,GAAG;MAAC4M,OAAO,EAAC,MAAM;MAACC,cAAc,EAAC,QAAQ;MAACC,UAAU,EAAC,QAAQ;MAACC,SAAS,EAAC,OAAO;MAAAC,QAAA,eAC/ExK,OAAA,CAAC7B,gBAAgB;QAAAsM,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACjB,CAAC;EAEV;EAEA,oBACE5K,OAAA,CAACxC,GAAG;IAACqN,EAAE,EAAE;MAAEC,CAAC,EAAE;IAAE,CAAE;IAAAN,QAAA,gBAEhBxK,OAAA,CAACxC,GAAG;MAACuN,EAAE,EAAE,CAAE;MAAAP,QAAA,eACTxK,OAAA,CAACvC,UAAU;QAACuN,OAAO,EAAC,IAAI;QAACH,EAAE,EAAE;UAAEI,UAAU,EAAE,GAAG;UAAEC,KAAK,EAAE;QAAe,CAAE;QAAAV,QAAA,EACrErK;MAAY;QAAAsK,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACV,CAAC,EAELnK,KAAK,iBACJT,OAAA,CAAC9B,KAAK;MAACiN,QAAQ,EAAC,OAAO;MAACN,EAAE,EAAE;QAAEE,EAAE,EAAE;MAAE,CAAE;MAAAP,QAAA,EACnC/J;IAAK;MAAAgK,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACD,CACR,EAEAjK,gBAAgB,iBACfX,OAAA,CAAC9B,KAAK;MAACiN,QAAQ,EAAC,MAAM;MAACN,EAAE,EAAE;QAAEE,EAAE,EAAE;MAAE,CAAE;MAAAP,QAAA,GAAC,+BACjB,EAAC7J,gBAAgB,EAAC,cACvC;IAAA;MAAA8J,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAO,CACR,EAGA/J,WAAW,iBACVb,OAAA,CAACrC,KAAK;MAACkN,EAAE,EAAE;QAAEC,CAAC,EAAE,CAAC;QAAEC,EAAE,EAAE,CAAC;QAAEK,OAAO,EAAE;MAAU,CAAE;MAAAZ,QAAA,eAC7CxK,OAAA,CAACzB,KAAK;QAAC8M,SAAS,EAAC,KAAK;QAACC,OAAO,EAAE,CAAE;QAAChB,UAAU,EAAC,QAAQ;QAACD,cAAc,EAAC,eAAe;QAACkB,QAAQ,EAAC,MAAM;QAAAf,QAAA,gBAEnGxK,OAAA,CAACzB,KAAK;UAAC8M,SAAS,EAAC,KAAK;UAACf,UAAU,EAAC,QAAQ;UAACgB,OAAO,EAAE,CAAE;UAAAd,QAAA,gBACpDxK,OAAA,CAACjB,UAAU;YAACmM,KAAK,EAAC,SAAS;YAACM,QAAQ,EAAC;UAAO;YAAAf,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,eAC/C5K,OAAA,CAACxC,GAAG;YAAAgN,QAAA,gBACFxK,OAAA,CAACvC,UAAU;cAACuN,OAAO,EAAC,IAAI;cAACC,UAAU,EAAC,MAAM;cAACJ,EAAE,EAAE;gBAAEY,UAAU,EAAE;cAAE,CAAE;cAAAjB,QAAA,EAC9D3J,WAAW,CAACqE,mBAAmB,IAAI;YAAC;cAAAuF,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC3B,CAAC,eACb5K,OAAA,CAACvC,UAAU;cAACuN,OAAO,EAAC,SAAS;cAACE,KAAK,EAAC,gBAAgB;cAAAV,QAAA,EAAC;YAErD;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAY,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACV,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACD,CAAC,eAGR5K,OAAA,CAACzB,KAAK;UAAC8M,SAAS,EAAC,KAAK;UAACf,UAAU,EAAC,QAAQ;UAACgB,OAAO,EAAE,CAAE;UAAAd,QAAA,gBACpDxK,OAAA,CAACnB,UAAU;YAACqM,KAAK,EAAC,MAAM;YAACM,QAAQ,EAAC;UAAO;YAAAf,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,eAC5C5K,OAAA,CAACxC,GAAG;YAAAgN,QAAA,gBACFxK,OAAA,CAACvC,UAAU;cAACuN,OAAO,EAAC,IAAI;cAACC,UAAU,EAAC,MAAM;cAACJ,EAAE,EAAE;gBAAEY,UAAU,EAAE;cAAE,CAAE;cAAAjB,QAAA,EAC9D3J,WAAW,CAACsE,cAAc,IAAI;YAAC;cAAAsF,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACtB,CAAC,eACb5K,OAAA,CAACvC,UAAU;cAACuN,OAAO,EAAC,SAAS;cAACE,KAAK,EAAC,gBAAgB;cAAAV,QAAA,EAAC;YAErD;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAY,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACV,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACD,CAAC,eAGR5K,OAAA,CAACzB,KAAK;UAAC8M,SAAS,EAAC,KAAK;UAACf,UAAU,EAAC,QAAQ;UAACgB,OAAO,EAAE,CAAE;UAAAd,QAAA,gBACpDxK,OAAA,CAACf,eAAe;YAACiM,KAAK,EAAC,SAAS;YAACM,QAAQ,EAAC;UAAO;YAAAf,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,eACpD5K,OAAA,CAACxC,GAAG;YAAAgN,QAAA,gBACFxK,OAAA,CAACvC,UAAU;cAACuN,OAAO,EAAC,IAAI;cAACC,UAAU,EAAC,MAAM;cAACJ,EAAE,EAAE;gBAAEY,UAAU,EAAE;cAAE,CAAE;cAAAjB,QAAA,EAC9D3J,WAAW,CAACwE,gBAAgB,IAAI;YAAC;cAAAoF,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACxB,CAAC,eACb5K,OAAA,CAACvC,UAAU;cAACuN,OAAO,EAAC,SAAS;cAACE,KAAK,EAAC,gBAAgB;cAAAV,QAAA,EAAC;YAErD;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAY,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACV,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACD,CAAC,eAGR5K,OAAA,CAACzB,KAAK;UAAC8M,SAAS,EAAC,KAAK;UAACf,UAAU,EAAC,QAAQ;UAACgB,OAAO,EAAE,CAAE;UAAAd,QAAA,gBACpDxK,OAAA,CAACb,YAAY;YAAC+L,KAAK,EAAC,SAAS;YAACM,QAAQ,EAAC;UAAO;YAAAf,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,eACjD5K,OAAA,CAACxC,GAAG;YAAAgN,QAAA,gBACFxK,OAAA,CAACvC,UAAU;cAACuN,OAAO,EAAC,IAAI;cAACC,UAAU,EAAC,MAAM;cAACJ,EAAE,EAAE;gBAAEY,UAAU,EAAE;cAAE,CAAE;cAAAjB,QAAA,EAC9D3J,WAAW,CAACyE,kBAAkB,IAAI;YAAC;cAAAmF,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC1B,CAAC,eACb5K,OAAA,CAACvC,UAAU;cAACuN,OAAO,EAAC,SAAS;cAACE,KAAK,EAAC,gBAAgB;cAAAV,QAAA,EAAC;YAErD;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAY,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACV,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACD,CAAC,eAGR5K,OAAA,CAACzB,KAAK;UAAC8M,SAAS,EAAC,KAAK;UAACf,UAAU,EAAC,QAAQ;UAACgB,OAAO,EAAE,CAAE;UAAAd,QAAA,gBACpDxK,OAAA,CAACxC,GAAG;YAACqN,EAAE,EAAE;cACPa,KAAK,EAAE,EAAE;cACTC,MAAM,EAAE,EAAE;cACVC,YAAY,EAAE,KAAK;cACnBR,OAAO,EAAGvK,WAAW,CAACyE,kBAAkB,IAAIzE,WAAW,CAACsE,cAAc,IAAI,CAAC,CAAC,IAAK,GAAG,GAAG,cAAc,GAC3FtE,WAAW,CAACyE,kBAAkB,IAAIzE,WAAW,CAACsE,cAAc,IAAI,CAAC,CAAC,IAAK,GAAG,GAAG,cAAc,GAAG,YAAY;cACpHiF,OAAO,EAAE,MAAM;cACfE,UAAU,EAAE,QAAQ;cACpBD,cAAc,EAAE;YAClB,CAAE;YAAAG,QAAA,eACAxK,OAAA,CAACvC,UAAU;cAACuN,OAAO,EAAC,SAAS;cAACC,UAAU,EAAC,MAAM;cAACC,KAAK,EAAC,OAAO;cAAAV,QAAA,GAC1D3J,WAAW,CAACsE,cAAc,GAAG,CAAC,GAAG0G,IAAI,CAACC,KAAK,CAAEjL,WAAW,CAACyE,kBAAkB,GAAGzE,WAAW,CAACsE,cAAc,GAAI,GAAG,CAAC,GAAG,CAAC,EAAC,GACxH;YAAA;cAAAsF,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAY;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACV,CAAC,eACN5K,OAAA,CAACxC,GAAG;YAAAgN,QAAA,gBACFxK,OAAA,CAACvC,UAAU;cAACuN,OAAO,EAAC,OAAO;cAACC,UAAU,EAAC,QAAQ;cAACJ,EAAE,EAAE;gBAAEY,UAAU,EAAE;cAAE,CAAE;cAAAjB,QAAA,EAAC;YAEvE;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAY,CAAC,eACb5K,OAAA,CAACvC,UAAU;cAACuN,OAAO,EAAC,SAAS;cAACE,KAAK,EAAC,gBAAgB;cAAAV,QAAA,GACjD3J,WAAW,CAACuE,cAAc,IAAI,CAAC,EAAC,SACnC;YAAA;cAAAqF,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAY,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACV,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACD,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CACR,eAGD5K,OAAA,CAACxC,GAAG;MAAAgN,QAAA,eACFxK,OAAA,CAACxC,GAAG;QAAAgN,QAAA,gBAEFxK,OAAA,CAACxC,GAAG;UAAC4M,OAAO,EAAC,MAAM;UAACC,cAAc,EAAC,eAAe;UAACC,UAAU,EAAC,QAAQ;UAACS,EAAE,EAAE,CAAE;UAAAP,QAAA,gBAC3ExK,OAAA,CAACvC,UAAU;YAACuN,OAAO,EAAC,IAAI;YAACH,EAAE,EAAE;cAAEI,UAAU,EAAE,GAAG;cAAEC,KAAK,EAAE;YAAe,CAAE;YAAAV,QAAA,EAAC;UAEzE;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAY,CAAC,eACb5K,OAAA,CAACxC,GAAG;YAAC4M,OAAO,EAAC,MAAM;YAAC2B,GAAG,EAAE,CAAE;YAAAvB,QAAA,gBACzBxK,OAAA,CAACtC,MAAM;cACLsN,OAAO,EAAC,UAAU;cAClBgB,SAAS,eAAEhM,OAAA,CAACX,UAAU;gBAAAoL,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAE;cAC1BqB,OAAO,EAAEA,CAAA,KAAM7K,wBAAwB,CAAC,IAAI,CAAE;cAC9CyJ,EAAE,EAAE;gBACFqB,aAAa,EAAE,MAAM;gBACrBjB,UAAU,EAAE,GAAG;gBACfkB,EAAE,EAAE,CAAC;gBACLC,EAAE,EAAE,CAAC;gBACLC,eAAe,EAAE,SAAS;gBAC1BnB,KAAK,EAAE,SAAS;gBAChBoB,MAAM,EAAE,mBAAmB;gBAC3B,SAAS,EAAE;kBACTD,eAAe,EAAE,yBAAyB;kBAC1CE,WAAW,EAAE;gBACf;cACF,CAAE;cAAA/B,QAAA,EACH;YAED;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC,eACT5K,OAAA,CAACtC,MAAM;cACLsN,OAAO,EAAC,WAAW;cACnBgB,SAAS,eAAEhM,OAAA,CAACrB,OAAO;gBAAA8L,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAE;cACvBqB,OAAO,EAAEA,CAAA,KAAM/D,4BAA4B,CAAC,QAAQ,CAAE;cACtD2C,EAAE,EAAE;gBACFqB,aAAa,EAAE,MAAM;gBACrBjB,UAAU,EAAE,GAAG;gBACfkB,EAAE,EAAE,CAAC;gBACLC,EAAE,EAAE,CAAC;gBACLC,eAAe,EAAE,SAAS;gBAC1BnB,KAAK,EAAE,OAAO;gBACd,SAAS,EAAE;kBACTmB,eAAe,EAAE;gBACnB;cACF,CAAE;cAAA7B,QAAA,EACH;YAED;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACN,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,EAGL3J,cAAc,gBACbjB,OAAA,CAACxC,GAAG;UAAC4M,OAAO,EAAC,MAAM;UAACC,cAAc,EAAC,QAAQ;UAAC+B,EAAE,EAAE,CAAE;UAAA5B,QAAA,eAChDxK,OAAA,CAAC7B,gBAAgB;YAAAsM,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACjB,CAAC,GACJ7J,UAAU,CAAC0D,MAAM,KAAK,CAAC,gBACzBzE,OAAA,CAACrC,KAAK;UACJ6O,SAAS,EAAE,CAAE;UACb3B,EAAE,EAAE;YACFC,CAAC,EAAE,CAAC;YACJ2B,SAAS,EAAE,QAAQ;YACnBJ,eAAe,EAAE,SAAS;YAC1BC,MAAM,EAAE,YAAY;YACpBC,WAAW,EAAE;UACf,CAAE;UAAA/B,QAAA,gBAEFxK,OAAA,CAACnB,UAAU;YAACgM,EAAE,EAAE;cAAEW,QAAQ,EAAE,EAAE;cAAEN,KAAK,EAAE,UAAU;cAAEH,EAAE,EAAE;YAAE;UAAE;YAAAN,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,eAC9D5K,OAAA,CAACvC,UAAU;YAACuN,OAAO,EAAC,IAAI;YAACE,KAAK,EAAC,gBAAgB;YAACwB,YAAY;YAAAlC,QAAA,EAAC;UAE7D;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAY,CAAC,eACb5K,OAAA,CAACvC,UAAU;YAACuN,OAAO,EAAC,OAAO;YAACE,KAAK,EAAC,gBAAgB;YAACL,EAAE,EAAE;cAAEE,EAAE,EAAE;YAAE,CAAE;YAAAP,QAAA,EAAC;UAElE;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAY,CAAC,eACb5K,OAAA,CAACtC,MAAM;YACLsN,OAAO,EAAC,WAAW;YACnBgB,SAAS,eAAEhM,OAAA,CAACrB,OAAO;cAAA8L,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAE;YACvBqB,OAAO,EAAEA,CAAA,KAAM3K,kBAAkB,CAAC,IAAI,CAAE;YACxCuJ,EAAE,EAAE;cACFqB,aAAa,EAAE,MAAM;cACrBG,eAAe,EAAE,SAAS;cAC1BnB,KAAK,EAAE,OAAO;cACd,SAAS,EAAE;gBACTmB,eAAe,EAAE;cACnB;YACF,CAAE;YAAA7B,QAAA,EACH;UAED;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACJ,CAAC,gBAER5K,OAAA,CAACJ,gBAAgB;UACf0E,OAAO,EAAEvD,UAAW;UACpB4L,aAAa,EAAEpF,uBAAwB;UACvCqF,eAAe,EAAE3D,mBAAoB;UACrC4D,kBAAkB,EAAEvF,0BAA2B;UAC/CwF,cAAc,EAAEtD,kBAAmB;UACnCjJ,OAAO,EAAEU;QAAe;UAAAwJ,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACzB,CACF;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACE;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,eAGN5K,OAAA,CAACnC,MAAM;MACLkP,IAAI,EAAElL,sBAAuB;MAC7BmL,OAAO,EAAE5E,6BAA8B;MACvC6E,QAAQ,EAAC,IAAI;MACbC,SAAS;MACTC,UAAU,EAAE;QACVtC,EAAE,EAAE;UAAEe,YAAY,EAAE;QAAE;MACxB,CAAE;MAAApB,QAAA,gBAEFxK,OAAA,CAAClC,WAAW;QAAC+M,EAAE,EAAE;UAAEuC,EAAE,EAAE;QAAE,CAAE;QAAA5C,QAAA,eACzBxK,OAAA,CAACvC,UAAU;UAACuN,OAAO,EAAC,IAAI;UAACH,EAAE,EAAE;YAAEI,UAAU,EAAE;UAAI,CAAE;UAAAT,QAAA,EAC9CzI,sBAAsB,KAAK,QAAQ,GAAG,wBAAwB,GAAG;QAAuB;UAAA0I,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC/E;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACF,CAAC,eACd5K,OAAA,CAACjC,aAAa;QAAAyM,QAAA,eACZxK,OAAA,CAACxC,GAAG;UAACqN,EAAE,EAAE;YAAEwC,EAAE,EAAE;UAAE,CAAE;UAAA7C,QAAA,gBACjBxK,OAAA,CAAC/B,SAAS;YACRiP,SAAS;YACTI,KAAK,EAAC,mBAAmB;YACzBC,KAAK,EAAEpL,oBAAoB,CAACE,iBAAkB;YAC9CmL,QAAQ,EAAGC,CAAC,IAAKrL,uBAAuB,CAAC;cAAE,GAAGD,oBAAoB;cAAEE,iBAAiB,EAAEoL,CAAC,CAACC,MAAM,CAACH;YAAM,CAAC,CAAE;YACzGI,MAAM,EAAC,QAAQ;YACfC,QAAQ;YACR5C,OAAO,EAAC,UAAU;YAClBH,EAAE,EAAE;cAAEE,EAAE,EAAE;YAAE;UAAE;YAAAN,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACf,CAAC,eAEF5K,OAAA,CAAC/B,SAAS;YACRiP,SAAS;YACTI,KAAK,EAAC,OAAO;YACbO,IAAI,EAAC,OAAO;YACZN,KAAK,EAAEpL,oBAAoB,CAACG,KAAM;YAClCkL,QAAQ,EAAGC,CAAC,IAAKrL,uBAAuB,CAAC;cAAE,GAAGD,oBAAoB;cAAEG,KAAK,EAAEmL,CAAC,CAACC,MAAM,CAACH;YAAM,CAAC,CAAE;YAC7FI,MAAM,EAAC,QAAQ;YACf3C,OAAO,EAAC,UAAU;YAClB8C,UAAU,EAAC,uDAAuD;YAClEjD,EAAE,EAAE;cAAEE,EAAE,EAAE;YAAE;UAAE;YAAAN,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACf,CAAC,eAEF5K,OAAA,CAAC/B,SAAS;YACRiP,SAAS;YACTI,KAAK,EAAC,UAAU;YAChBC,KAAK,EAAEpL,oBAAoB,CAACI,QAAS;YACrCiL,QAAQ,EAAGC,CAAC,IAAKrL,uBAAuB,CAAC;cAAE,GAAGD,oBAAoB;cAAEI,QAAQ,EAAEkL,CAAC,CAACC,MAAM,CAACH;YAAM,CAAC,CAAE;YAChGI,MAAM,EAAC,QAAQ;YACf3C,OAAO,EAAC,UAAU;YAClB8C,UAAU,EAAC;UAA+C;YAAArD,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC3D,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACC;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACO,CAAC,eAChB5K,OAAA,CAAChC,aAAa;QAAC6M,EAAE,EAAE;UAAEC,CAAC,EAAE,CAAC;UAAEuC,EAAE,EAAE;QAAE,CAAE;QAAA7C,QAAA,gBACjCxK,OAAA,CAACtC,MAAM;UACLuO,OAAO,EAAE7D,6BAA8B;UACvCyC,EAAE,EAAE;YAAEqB,aAAa,EAAE;UAAO,CAAE;UAAA1B,QAAA,EAC/B;QAED;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC,eACT5K,OAAA,CAACtC,MAAM;UACLuO,OAAO,EAAE5D,wBAAyB;UAClC2C,OAAO,EAAC,WAAW;UACnBH,EAAE,EAAE;YACFqB,aAAa,EAAE,MAAM;YACrBjB,UAAU,EAAE,GAAG;YACfkB,EAAE,EAAE;UACN,CAAE;UAAA3B,QAAA,EAEDzI,sBAAsB,KAAK,QAAQ,GAAG,MAAM,GAAG;QAAO;UAAA0I,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACjD,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACI,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACV,CAAC,eAGT5K,OAAA,CAACnC,MAAM;MACLkP,IAAI,EAAEvK,iBAAkB;MACxBwK,OAAO,EAAElE,wBAAyB;MAClCmE,QAAQ,EAAC,IAAI;MACbC,SAAS;MACTC,UAAU,EAAE;QACVtC,EAAE,EAAE;UAAEe,YAAY,EAAE;QAAE;MACxB,CAAE;MAAApB,QAAA,gBAEFxK,OAAA,CAAClC,WAAW;QAAC+M,EAAE,EAAE;UAAEuC,EAAE,EAAE;QAAE,CAAE;QAAA5C,QAAA,gBACzBxK,OAAA,CAACvC,UAAU;UAACuN,OAAO,EAAC,IAAI;UAACH,EAAE,EAAE;YAAEI,UAAU,EAAE;UAAI,CAAE;UAAAT,QAAA,GAAC,mBAC/B,EAAC9H,eAAe,aAAfA,eAAe,uBAAfA,eAAe,CAAEyE,cAAc;QAAA;UAAAsD,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACvC,CAAC,eACb5K,OAAA,CAACvC,UAAU;UAACuN,OAAO,EAAC,OAAO;UAACE,KAAK,EAAC,gBAAgB;UAAAV,QAAA,GAAC,SAC1C,EAAC9H,eAAe,aAAfA,eAAe,uBAAfA,eAAe,CAAEwH,KAAK,EAAC,0BAAmB,EAAC,CAAAxH,eAAe,aAAfA,eAAe,uBAAfA,eAAe,CAAEmH,qBAAqB,KAAI,CAAC;QAAA;UAAAY,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACpF,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACF,CAAC,eACd5K,OAAA,CAACjC,aAAa;QAAAyM,QAAA,eACZxK,OAAA,CAACxC,GAAG;UAACqN,EAAE,EAAE;YAAEwC,EAAE,EAAE;UAAE,CAAE;UAAA7C,QAAA,gBACjBxK,OAAA,CAAC9B,KAAK;YAACiN,QAAQ,EAAC,SAAS;YAACN,EAAE,EAAE;cAAEE,EAAE,EAAE;YAAE,CAAE;YAAAP,QAAA,eACtCxK,OAAA,CAACvC,UAAU;cAACuN,OAAO,EAAC,OAAO;cAAAR,QAAA,gBACzBxK,OAAA;gBAAAwK,QAAA,EAAQ;cAAW;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC,sKAE9B;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAY;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACR,CAAC,eAER5K,OAAA,CAAC/B,SAAS;YACRiP,SAAS;YACTa,MAAM;YACNT,KAAK,EAAC,cAAc;YACpBC,KAAK,EAAE3K,eAAe,CAACE,YAAa;YACpC0K,QAAQ,EAAGC,CAAC,IAAK5K,kBAAkB,CAAC;cAAE,GAAGD,eAAe;cAAEE,YAAY,EAAE2K,CAAC,CAACC,MAAM,CAACH;YAAM,CAAC,CAAE;YAC1FI,MAAM,EAAC,QAAQ;YACf9C,EAAE,EAAE;cAAEE,EAAE,EAAE;YAAE,CAAE;YAAAP,QAAA,gBAEdxK,OAAA,CAACxB,QAAQ;cAAC+O,KAAK,EAAC,MAAM;cAAA/C,QAAA,EAAC;YAAI;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAU,CAAC,eACtC5K,OAAA,CAACxB,QAAQ;cAAC+O,KAAK,EAAC,uBAAuB;cAAA/C,QAAA,EAAC;YAAqB;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAU,CAAC,eACxE5K,OAAA,CAACxB,QAAQ;cAAC+O,KAAK,EAAC,qBAAqB;cAAA/C,QAAA,EAAC;YAAmB;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAU,CAAC,eACpE5K,OAAA,CAACxB,QAAQ;cAAC+O,KAAK,EAAC,gBAAgB;cAAA/C,QAAA,EAAC;YAAc;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAU,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACjD,CAAC,eAEZ5K,OAAA,CAAC/B,SAAS;YACRiP,SAAS;YACTI,KAAK,EAAC,aAAa;YACnBC,KAAK,EAAE3K,eAAe,CAACG,WAAY;YACnCyK,QAAQ,EAAGC,CAAC,IAAK5K,kBAAkB,CAAC;cAAE,GAAGD,eAAe;cAAEG,WAAW,EAAE0K,CAAC,CAACC,MAAM,CAACH;YAAM,CAAC,CAAE;YACzFI,MAAM,EAAC,QAAQ;YACfK,SAAS;YACTC,IAAI,EAAE,CAAE;YACRpD,EAAE,EAAE;cAAEE,EAAE,EAAE;YAAE;UAAE;YAAAN,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACf,CAAC,eAEF5K,OAAA,CAAC/B,SAAS;YACRiP,SAAS;YACTI,KAAK,EAAC,cAAc;YACpBC,KAAK,EAAE3K,eAAe,CAACI,YAAa;YACpCwK,QAAQ,EAAGC,CAAC,IAAK5K,kBAAkB,CAAC;cAAE,GAAGD,eAAe;cAAEI,YAAY,EAAEyK,CAAC,CAACC,MAAM,CAACH;YAAM,CAAC,CAAE;YAC1FI,MAAM,EAAC,QAAQ;YACfC,QAAQ;YACRE,UAAU,EAAC,0CAAuC;YAClDjD,EAAE,EAAE;cAAEE,EAAE,EAAE;YAAE;UAAE;YAAAN,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACf,CAAC,eAEF5K,OAAA,CAAC/B,SAAS;YACRiP,SAAS;YACTI,KAAK,EAAC,oBAAoB;YAC1BC,KAAK,EAAE3K,eAAe,CAACM,kBAAmB;YAC1CsK,QAAQ,EAAGC,CAAC,IAAK5K,kBAAkB,CAAC;cAAE,GAAGD,eAAe;cAAEM,kBAAkB,EAAEuK,CAAC,CAACC,MAAM,CAACH;YAAM,CAAC,CAAE;YAChGI,MAAM,EAAC,QAAQ;YACfK,SAAS;YACTC,IAAI,EAAE,CAAE;YACRH,UAAU,EAAC,2CAA2C;YACtDjD,EAAE,EAAE;cAAEE,EAAE,EAAE;YAAE;UAAE;YAAAN,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACf,CAAC,eAEF5K,OAAA,CAAC/B,SAAS;YACRiP,SAAS;YACTI,KAAK,EAAC,eAAe;YACrBO,IAAI,EAAC,MAAM;YACXN,KAAK,EAAE3K,eAAe,CAACK,aAAc;YACrCuK,QAAQ,EAAGC,CAAC,IAAK5K,kBAAkB,CAAC;cAAE,GAAGD,eAAe;cAAEK,aAAa,EAAEwK,CAAC,CAACC,MAAM,CAACH;YAAM,CAAC,CAAE;YAC3FI,MAAM,EAAC,QAAQ;YACfO,eAAe,EAAE;cACfC,MAAM,EAAE;YACV;UAAE;YAAA1D,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACC;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACO,CAAC,eAChB5K,OAAA,CAAChC,aAAa;QAAC6M,EAAE,EAAE;UAAEC,CAAC,EAAE,CAAC;UAAEuC,EAAE,EAAE;QAAE,CAAE;QAAA7C,QAAA,gBACjCxK,OAAA,CAACtC,MAAM;UACLuO,OAAO,EAAEnD,wBAAyB;UAClC+B,EAAE,EAAE;YAAEqB,aAAa,EAAE;UAAO,CAAE;UAAA1B,QAAA,EAC/B;QAED;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC,eACT5K,OAAA,CAACtC,MAAM;UACLuO,OAAO,EAAElD,mBAAoB;UAC7BiC,OAAO,EAAC,WAAW;UACnBH,EAAE,EAAE;YACFqB,aAAa,EAAE,MAAM;YACrBjB,UAAU,EAAE,GAAG;YACfkB,EAAE,EAAE;UACN,CAAE;UAAA3B,QAAA,EACH;QAED;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACI,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACV,CAAC,eAGT5K,OAAA,CAACN,kBAAkB;MACjBQ,UAAU,EAAEA,UAAW;MACvB6M,IAAI,EAAE1L,eAAgB;MACtB2L,OAAO,EAAEA,CAAA,KAAM1L,kBAAkB,CAAC,KAAK,CAAE;MACzC8M,SAAS,EAAEA,CAACpJ,QAAQ,EAAEqJ,cAAc,KAAK;QACvCzK,OAAO,CAACC,GAAG,CAAC,iDAAiD,CAAC;;QAE9D;QACA,IAAIwK,cAAc,EAAE;UAClB;UACAzK,OAAO,CAACC,GAAG,CAAC,cAAc,EAAEwK,cAAc,CAAC;QAC7C;;QAEA;QACA1K,WAAW,CAAC,CAAC;QACbiB,eAAe,CAAC,CAAC;QACjBY,gBAAgB,CAAC,CAAC;QAClBlE,kBAAkB,CAAC,KAAK,CAAC;QAEzBsC,OAAO,CAACC,GAAG,CAAC,0BAA0B,CAAC;MACzC;IAAE;MAAA4G,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,eAGF5K,OAAA,CAACL,qBAAqB;MACpBoN,IAAI,EAAE5L,qBAAsB;MAC5B6L,OAAO,EAAEA,CAAA,KAAM5L,wBAAwB,CAAC,KAAK,CAAE;MAC/CG,YAAY,EAAEA,YAAa;MAC3BI,sBAAsB,EAAEA,sBAAuB;MAC/C2M,kBAAkB,EAAGtL,YAAY,IAAK;QACpC5B,wBAAwB,CAAC,KAAK,CAAC;QAC/B8G,4BAA4B,CAAC,MAAM,EAAElF,YAAY,CAAC;MACpD,CAAE;MACFuL,oBAAoB,EAAE,MAAO7F,cAAc,IAAK;QAC9C,MAAMD,wBAAwB,CAACC,cAAc,CAAC;QAC9CtH,wBAAwB,CAAC,KAAK,CAAC;MACjC,CAAE;MACFb,OAAO,EAAEkB,mBAAoB;MAC7BhB,KAAK,EAAEA;IAAM;MAAAgK,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACd,CAAC,eAGF5K,OAAA,CAACH,sBAAsB;MACrBkN,IAAI,EAAE5J,oBAAqB;MAC3B6J,OAAO,EAAE5D,2BAA4B;MACrC7C,OAAO,EAAElD,eAAgB;MACzB+K,SAAS,EAAE9E;IAA8B;MAAAmB,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAC1C,CAAC,eAGF5K,OAAA,CAACF,kBAAkB;MACjBiN,IAAI,EAAExJ,gBAAiB;MACvByJ,OAAO,EAAE3D,uBAAwB;MACjC9C,OAAO,EAAE9C,sBAAuB;MAChC2K,SAAS,EAAE7E;IAA0B;MAAAkB,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACtC,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACC,CAAC;AAEV,CAAC;AAACxK,EAAA,CAjhCIH,wBAAwB;EAAA,QAEY1C,eAAe;AAAA;AAAAiR,EAAA,GAFnDvO,wBAAwB;AAmhC9B,eAAeA,wBAAwB;AAAC,IAAAuO,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}