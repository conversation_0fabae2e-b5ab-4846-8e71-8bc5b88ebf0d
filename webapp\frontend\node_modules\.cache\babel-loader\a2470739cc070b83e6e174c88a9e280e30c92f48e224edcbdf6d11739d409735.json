{"ast": null, "code": "import { getMonthsInYear } from \"../../utils/date-utils.js\";\nexport const getDateSectionConfigFromFormatToken = (utils, formatToken) => {\n  const config = utils.formatTokenMap[formatToken];\n  if (config == null) {\n    throw new Error([`MUI X: The token \"${formatToken}\" is not supported by the Date and Time Pickers.`, 'Please try using another token or open an issue on https://github.com/mui/mui-x/issues/new/choose if you think it should be supported.'].join('\\n'));\n  }\n  if (typeof config === 'string') {\n    return {\n      type: config,\n      contentType: config === 'meridiem' ? 'letter' : 'digit',\n      maxLength: undefined\n    };\n  }\n  return {\n    type: config.sectionType,\n    contentType: config.contentType,\n    maxLength: config.maxLength\n  };\n};\nexport const getDaysInWeekStr = (utils, format) => {\n  const elements = [];\n  const now = utils.date(undefined, 'default');\n  const startDate = utils.startOfWeek(now);\n  const endDate = utils.endOfWeek(now);\n  let current = startDate;\n  while (utils.isBefore(current, endDate)) {\n    elements.push(current);\n    current = utils.addDays(current, 1);\n  }\n  return elements.map(weekDay => utils.formatByString(weekDay, format));\n};\nexport const getLetterEditingOptions = (utils, timezone, sectionType, format) => {\n  switch (sectionType) {\n    case 'month':\n      {\n        return getMonthsInYear(utils, utils.date(undefined, timezone)).map(month => utils.formatByString(month, format));\n      }\n    case 'weekDay':\n      {\n        return getDaysInWeekStr(utils, format);\n      }\n    case 'meridiem':\n      {\n        const now = utils.date(undefined, timezone);\n        return [utils.startOfDay(now), utils.endOfDay(now)].map(date => utils.formatByString(date, format));\n      }\n    default:\n      {\n        return [];\n      }\n  }\n};\n\n// This format should be the same on all the adapters\n// If some adapter does not respect this convention, then we will need to hardcode the format on each adapter.\nexport const FORMAT_SECONDS_NO_LEADING_ZEROS = 's';\nconst NON_LOCALIZED_DIGITS = ['0', '1', '2', '3', '4', '5', '6', '7', '8', '9'];\nexport const getLocalizedDigits = utils => {\n  const today = utils.date(undefined);\n  const formattedZero = utils.formatByString(utils.setSeconds(today, 0), FORMAT_SECONDS_NO_LEADING_ZEROS);\n  if (formattedZero === '0') {\n    return NON_LOCALIZED_DIGITS;\n  }\n  return Array.from({\n    length: 10\n  }).map((_, index) => utils.formatByString(utils.setSeconds(today, index), FORMAT_SECONDS_NO_LEADING_ZEROS));\n};\nexport const removeLocalizedDigits = (valueStr, localizedDigits) => {\n  if (localizedDigits[0] === '0') {\n    return valueStr;\n  }\n  const digits = [];\n  let currentFormattedDigit = '';\n  for (let i = 0; i < valueStr.length; i += 1) {\n    currentFormattedDigit += valueStr[i];\n    const matchingDigitIndex = localizedDigits.indexOf(currentFormattedDigit);\n    if (matchingDigitIndex > -1) {\n      digits.push(matchingDigitIndex.toString());\n      currentFormattedDigit = '';\n    }\n  }\n  return digits.join('');\n};\nexport const applyLocalizedDigits = (valueStr, localizedDigits) => {\n  if (localizedDigits[0] === '0') {\n    return valueStr;\n  }\n  return valueStr.split('').map(char => localizedDigits[Number(char)]).join('');\n};\nexport const isStringNumber = (valueStr, localizedDigits) => {\n  const nonLocalizedValueStr = removeLocalizedDigits(valueStr, localizedDigits);\n  // `Number(' ')` returns `0` even if ' ' is not a valid number.\n  return nonLocalizedValueStr !== ' ' && !Number.isNaN(Number(nonLocalizedValueStr));\n};\n\n/**\n * Make sure the value of a digit section have the right amount of leading zeros.\n * E.g.: `03` => `3`\n * Warning: Should only be called with non-localized digits. Call `removeLocalizedDigits` with your value if needed.\n */\nexport const cleanLeadingZeros = (valueStr, size) => {\n  // Remove the leading zeros and then add back as many as needed.\n  return Number(valueStr).toString().padStart(size, '0');\n};\nexport const cleanDigitSectionValue = (utils, value, sectionBoundaries, localizedDigits, section) => {\n  if (process.env.NODE_ENV !== 'production') {\n    if (section.type !== 'day' && section.contentType === 'digit-with-letter') {\n      throw new Error([`MUI X: The token \"${section.format}\" is a digit format with letter in it.'\n             This type of format is only supported for 'day' sections`].join('\\n'));\n    }\n  }\n  if (section.type === 'day' && section.contentType === 'digit-with-letter') {\n    const date = utils.setDate(sectionBoundaries.longestMonth, value);\n    return utils.formatByString(date, section.format);\n  }\n\n  // queryValue without leading `0` (`01` => `1`)\n  let valueStr = value.toString();\n  if (section.hasLeadingZerosInInput) {\n    valueStr = cleanLeadingZeros(valueStr, section.maxLength);\n  }\n  return applyLocalizedDigits(valueStr, localizedDigits);\n};\nexport const getSectionVisibleValue = (section, target, localizedDigits) => {\n  let value = section.value || section.placeholder;\n  const hasLeadingZeros = target === 'non-input' ? section.hasLeadingZerosInFormat : section.hasLeadingZerosInInput;\n  if (target === 'non-input' && section.hasLeadingZerosInInput && !section.hasLeadingZerosInFormat) {\n    value = Number(removeLocalizedDigits(value, localizedDigits)).toString();\n  }\n\n  // In the input, we add an empty character at the end of each section without leading zeros.\n  // This makes sure that `onChange` will always be fired.\n  // Otherwise, when your input value equals `1/dd/yyyy` (format `M/DD/YYYY` on DayJs),\n  // If you press `1`, on the first section, the new value is also `1/dd/yyyy`,\n  // So the browser will not fire the input `onChange`.\n  const shouldAddInvisibleSpace = ['input-rtl', 'input-ltr'].includes(target) && section.contentType === 'digit' && !hasLeadingZeros && value.length === 1;\n  if (shouldAddInvisibleSpace) {\n    value = `${value}\\u200e`;\n  }\n  if (target === 'input-rtl') {\n    value = `\\u2068${value}\\u2069`;\n  }\n  return value;\n};\nexport const changeSectionValueFormat = (utils, valueStr, currentFormat, newFormat) => {\n  if (process.env.NODE_ENV !== 'production') {\n    if (getDateSectionConfigFromFormatToken(utils, currentFormat).type === 'weekDay') {\n      throw new Error(\"changeSectionValueFormat doesn't support week day formats\");\n    }\n  }\n  return utils.formatByString(utils.parse(valueStr, currentFormat), newFormat);\n};\nconst isFourDigitYearFormat = (utils, format) => utils.formatByString(utils.date(undefined, 'system'), format).length === 4;\nexport const doesSectionFormatHaveLeadingZeros = (utils, contentType, sectionType, format) => {\n  if (contentType !== 'digit') {\n    return false;\n  }\n  const now = utils.date(undefined, 'default');\n  switch (sectionType) {\n    // We can't use `changeSectionValueFormat`, because  `utils.parse('1', 'YYYY')` returns `1971` instead of `1`.\n    case 'year':\n      {\n        // Remove once https://github.com/iamkun/dayjs/pull/2847 is merged and bump dayjs version\n        if (utils.lib === 'dayjs' && format === 'YY') {\n          return true;\n        }\n        return utils.formatByString(utils.setYear(now, 1), format).startsWith('0');\n      }\n    case 'month':\n      {\n        return utils.formatByString(utils.startOfYear(now), format).length > 1;\n      }\n    case 'day':\n      {\n        return utils.formatByString(utils.startOfMonth(now), format).length > 1;\n      }\n    case 'weekDay':\n      {\n        return utils.formatByString(utils.startOfWeek(now), format).length > 1;\n      }\n    case 'hours':\n      {\n        return utils.formatByString(utils.setHours(now, 1), format).length > 1;\n      }\n    case 'minutes':\n      {\n        return utils.formatByString(utils.setMinutes(now, 1), format).length > 1;\n      }\n    case 'seconds':\n      {\n        return utils.formatByString(utils.setSeconds(now, 1), format).length > 1;\n      }\n    default:\n      {\n        throw new Error('Invalid section type');\n      }\n  }\n};\n\n/**\n * Some date libraries like `dayjs` don't support parsing from date with escaped characters.\n * To make sure that the parsing works, we are building a format and a date without any separator.\n */\nexport const getDateFromDateSections = (utils, sections, localizedDigits) => {\n  // If we have both a day and a weekDay section,\n  // Then we skip the weekDay in the parsing because libraries like dayjs can't parse complicated formats containing a weekDay.\n  // dayjs(dayjs().format('dddd MMMM D YYYY'), 'dddd MMMM D YYYY')) // returns `Invalid Date` even if the format is valid.\n  const shouldSkipWeekDays = sections.some(section => section.type === 'day');\n  const sectionFormats = [];\n  const sectionValues = [];\n  for (let i = 0; i < sections.length; i += 1) {\n    const section = sections[i];\n    const shouldSkip = shouldSkipWeekDays && section.type === 'weekDay';\n    if (!shouldSkip) {\n      sectionFormats.push(section.format);\n      sectionValues.push(getSectionVisibleValue(section, 'non-input', localizedDigits));\n    }\n  }\n  const formatWithoutSeparator = sectionFormats.join(' ');\n  const dateWithoutSeparatorStr = sectionValues.join(' ');\n  return utils.parse(dateWithoutSeparatorStr, formatWithoutSeparator);\n};\nexport const createDateStrForV7HiddenInputFromSections = sections => sections.map(section => {\n  return `${section.startSeparator}${section.value || section.placeholder}${section.endSeparator}`;\n}).join('');\nexport const createDateStrForV6InputFromSections = (sections, localizedDigits, isRtl) => {\n  const formattedSections = sections.map(section => {\n    const dateValue = getSectionVisibleValue(section, isRtl ? 'input-rtl' : 'input-ltr', localizedDigits);\n    return `${section.startSeparator}${dateValue}${section.endSeparator}`;\n  });\n  const dateStr = formattedSections.join('');\n  if (!isRtl) {\n    return dateStr;\n  }\n\n  // \\u2066: start left-to-right isolation\n  // \\u2067: start right-to-left isolation\n  // \\u2068: start first strong character isolation\n  // \\u2069: pop isolation\n  // wrap into an isolated group such that separators can split the string in smaller ones by adding \\u2069\\u2068\n  return `\\u2066${dateStr}\\u2069`;\n};\nexport const getSectionsBoundaries = (utils, localizedDigits, timezone) => {\n  const today = utils.date(undefined, timezone);\n  const endOfYear = utils.endOfYear(today);\n  const endOfDay = utils.endOfDay(today);\n  const {\n    maxDaysInMonth,\n    longestMonth\n  } = getMonthsInYear(utils, today).reduce((acc, month) => {\n    const daysInMonth = utils.getDaysInMonth(month);\n    if (daysInMonth > acc.maxDaysInMonth) {\n      return {\n        maxDaysInMonth: daysInMonth,\n        longestMonth: month\n      };\n    }\n    return acc;\n  }, {\n    maxDaysInMonth: 0,\n    longestMonth: null\n  });\n  return {\n    year: ({\n      format\n    }) => ({\n      minimum: 0,\n      maximum: isFourDigitYearFormat(utils, format) ? 9999 : 99\n    }),\n    month: () => ({\n      minimum: 1,\n      // Assumption: All years have the same amount of months\n      maximum: utils.getMonth(endOfYear) + 1\n    }),\n    day: ({\n      currentDate\n    }) => ({\n      minimum: 1,\n      maximum: utils.isValid(currentDate) ? utils.getDaysInMonth(currentDate) : maxDaysInMonth,\n      longestMonth: longestMonth\n    }),\n    weekDay: ({\n      format,\n      contentType\n    }) => {\n      if (contentType === 'digit') {\n        const daysInWeek = getDaysInWeekStr(utils, format).map(Number);\n        return {\n          minimum: Math.min(...daysInWeek),\n          maximum: Math.max(...daysInWeek)\n        };\n      }\n      return {\n        minimum: 1,\n        maximum: 7\n      };\n    },\n    hours: ({\n      format\n    }) => {\n      const lastHourInDay = utils.getHours(endOfDay);\n      const hasMeridiem = removeLocalizedDigits(utils.formatByString(utils.endOfDay(today), format), localizedDigits) !== lastHourInDay.toString();\n      if (hasMeridiem) {\n        return {\n          minimum: 1,\n          maximum: Number(removeLocalizedDigits(utils.formatByString(utils.startOfDay(today), format), localizedDigits))\n        };\n      }\n      return {\n        minimum: 0,\n        maximum: lastHourInDay\n      };\n    },\n    minutes: () => ({\n      minimum: 0,\n      // Assumption: All years have the same amount of minutes\n      maximum: utils.getMinutes(endOfDay)\n    }),\n    seconds: () => ({\n      minimum: 0,\n      // Assumption: All years have the same amount of seconds\n      maximum: utils.getSeconds(endOfDay)\n    }),\n    meridiem: () => ({\n      minimum: 0,\n      maximum: 1\n    }),\n    empty: () => ({\n      minimum: 0,\n      maximum: 0\n    })\n  };\n};\nlet warnedOnceInvalidSection = false;\nexport const validateSections = (sections, valueType) => {\n  if (process.env.NODE_ENV !== 'production') {\n    if (!warnedOnceInvalidSection) {\n      const supportedSections = ['empty'];\n      if (['date', 'date-time'].includes(valueType)) {\n        supportedSections.push('weekDay', 'day', 'month', 'year');\n      }\n      if (['time', 'date-time'].includes(valueType)) {\n        supportedSections.push('hours', 'minutes', 'seconds', 'meridiem');\n      }\n      const invalidSection = sections.find(section => !supportedSections.includes(section.type));\n      if (invalidSection) {\n        console.warn(`MUI X: The field component you are using is not compatible with the \"${invalidSection.type}\" date section.`, `The supported date sections are [\"${supportedSections.join('\", \"')}\"]\\`.`);\n        warnedOnceInvalidSection = true;\n      }\n    }\n  }\n};\nconst transferDateSectionValue = (utils, section, dateToTransferFrom, dateToTransferTo) => {\n  switch (section.type) {\n    case 'year':\n      {\n        return utils.setYear(dateToTransferTo, utils.getYear(dateToTransferFrom));\n      }\n    case 'month':\n      {\n        return utils.setMonth(dateToTransferTo, utils.getMonth(dateToTransferFrom));\n      }\n    case 'weekDay':\n      {\n        let dayInWeekStrOfActiveDate = utils.formatByString(dateToTransferFrom, section.format);\n        if (section.hasLeadingZerosInInput) {\n          dayInWeekStrOfActiveDate = cleanLeadingZeros(dayInWeekStrOfActiveDate, section.maxLength);\n        }\n        const formattedDaysInWeek = getDaysInWeekStr(utils, section.format);\n        const dayInWeekOfActiveDate = formattedDaysInWeek.indexOf(dayInWeekStrOfActiveDate);\n        const dayInWeekOfNewSectionValue = formattedDaysInWeek.indexOf(section.value);\n        const diff = dayInWeekOfNewSectionValue - dayInWeekOfActiveDate;\n        return utils.addDays(dateToTransferFrom, diff);\n      }\n    case 'day':\n      {\n        return utils.setDate(dateToTransferTo, utils.getDate(dateToTransferFrom));\n      }\n    case 'meridiem':\n      {\n        const isAM = utils.getHours(dateToTransferFrom) < 12;\n        const mergedDateHours = utils.getHours(dateToTransferTo);\n        if (isAM && mergedDateHours >= 12) {\n          return utils.addHours(dateToTransferTo, -12);\n        }\n        if (!isAM && mergedDateHours < 12) {\n          return utils.addHours(dateToTransferTo, 12);\n        }\n        return dateToTransferTo;\n      }\n    case 'hours':\n      {\n        return utils.setHours(dateToTransferTo, utils.getHours(dateToTransferFrom));\n      }\n    case 'minutes':\n      {\n        return utils.setMinutes(dateToTransferTo, utils.getMinutes(dateToTransferFrom));\n      }\n    case 'seconds':\n      {\n        return utils.setSeconds(dateToTransferTo, utils.getSeconds(dateToTransferFrom));\n      }\n    default:\n      {\n        return dateToTransferTo;\n      }\n  }\n};\nconst reliableSectionModificationOrder = {\n  year: 1,\n  month: 2,\n  day: 3,\n  weekDay: 4,\n  hours: 5,\n  minutes: 6,\n  seconds: 7,\n  meridiem: 8,\n  empty: 9\n};\nexport const mergeDateIntoReferenceDate = (utils, dateToTransferFrom, sections, referenceDate, shouldLimitToEditedSections) =>\n// cloning sections before sort to avoid mutating it\n[...sections].sort((a, b) => reliableSectionModificationOrder[a.type] - reliableSectionModificationOrder[b.type]).reduce((mergedDate, section) => {\n  if (!shouldLimitToEditedSections || section.modified) {\n    return transferDateSectionValue(utils, section, dateToTransferFrom, mergedDate);\n  }\n  return mergedDate;\n}, referenceDate);\nexport const isAndroid = () => navigator.userAgent.toLowerCase().includes('android');\n\n// TODO v9: Remove\nexport const getSectionOrder = (sections, shouldApplyRTL) => {\n  const neighbors = {};\n  if (!shouldApplyRTL) {\n    sections.forEach((_, index) => {\n      const leftIndex = index === 0 ? null : index - 1;\n      const rightIndex = index === sections.length - 1 ? null : index + 1;\n      neighbors[index] = {\n        leftIndex,\n        rightIndex\n      };\n    });\n    return {\n      neighbors,\n      startIndex: 0,\n      endIndex: sections.length - 1\n    };\n  }\n  const rtl2ltr = {};\n  const ltr2rtl = {};\n  let groupedSectionsStart = 0;\n  let groupedSectionsEnd = 0;\n  let RTLIndex = sections.length - 1;\n  while (RTLIndex >= 0) {\n    groupedSectionsEnd = sections.findIndex(\n    // eslint-disable-next-line @typescript-eslint/no-loop-func\n    (section, index) => index >= groupedSectionsStart && section.endSeparator?.includes(' ') &&\n    // Special case where the spaces were not there in the initial input\n    section.endSeparator !== ' / ');\n    if (groupedSectionsEnd === -1) {\n      groupedSectionsEnd = sections.length - 1;\n    }\n    for (let i = groupedSectionsEnd; i >= groupedSectionsStart; i -= 1) {\n      ltr2rtl[i] = RTLIndex;\n      rtl2ltr[RTLIndex] = i;\n      RTLIndex -= 1;\n    }\n    groupedSectionsStart = groupedSectionsEnd + 1;\n  }\n  sections.forEach((_, index) => {\n    const rtlIndex = ltr2rtl[index];\n    const leftIndex = rtlIndex === 0 ? null : rtl2ltr[rtlIndex - 1];\n    const rightIndex = rtlIndex === sections.length - 1 ? null : rtl2ltr[rtlIndex + 1];\n    neighbors[index] = {\n      leftIndex,\n      rightIndex\n    };\n  });\n  return {\n    neighbors,\n    startIndex: rtl2ltr[0],\n    endIndex: rtl2ltr[sections.length - 1]\n  };\n};\nexport const parseSelectedSections = (selectedSections, sections) => {\n  if (selectedSections == null) {\n    return null;\n  }\n  if (selectedSections === 'all') {\n    return 'all';\n  }\n  if (typeof selectedSections === 'string') {\n    const index = sections.findIndex(section => section.type === selectedSections);\n    return index === -1 ? null : index;\n  }\n  return selectedSections;\n};", "map": {"version": 3, "names": ["getMonthsInYear", "getDateSectionConfigFromFormatToken", "utils", "formatToken", "config", "formatTokenMap", "Error", "join", "type", "contentType", "max<PERSON><PERSON><PERSON>", "undefined", "sectionType", "getDaysInWeekStr", "format", "elements", "now", "date", "startDate", "startOfWeek", "endDate", "endOfWeek", "current", "isBefore", "push", "addDays", "map", "weekDay", "formatByString", "getLetterEditingOptions", "timezone", "month", "startOfDay", "endOfDay", "FORMAT_SECONDS_NO_LEADING_ZEROS", "NON_LOCALIZED_DIGITS", "getLocalizedDigits", "today", "formattedZero", "setSeconds", "Array", "from", "length", "_", "index", "removeLocalizedDigits", "valueStr", "localizedDigits", "digits", "currentFormattedDigit", "i", "matchingDigitIndex", "indexOf", "toString", "applyLocalizedDigits", "split", "char", "Number", "isStringNumber", "nonLocalizedValueStr", "isNaN", "cleanLeadingZeros", "size", "padStart", "cleanDigitSectionValue", "value", "sectionBoundaries", "section", "process", "env", "NODE_ENV", "setDate", "longestMonth", "hasLeadingZerosInInput", "getSectionVisibleValue", "target", "placeholder", "hasLeadingZeros", "hasLeadingZerosInFormat", "shouldAddInvisibleSpace", "includes", "changeSectionValueFormat", "currentFormat", "newFormat", "parse", "isFourDigitYearFormat", "doesSectionFormatHaveLeadingZeros", "lib", "setYear", "startsWith", "startOfYear", "startOfMonth", "setHours", "setMinutes", "getDateFromDateSections", "sections", "shouldSkipWeekDays", "some", "sectionFormats", "sectionValues", "shouldSkip", "formatWithoutSeparator", "dateWithoutSeparatorStr", "createDateStrForV7HiddenInputFromSections", "startSeparator", "endSeparator", "createDateStrForV6InputFromSections", "isRtl", "formattedSections", "dateValue", "dateStr", "getSectionsBoundaries", "endOfYear", "maxDaysInMonth", "reduce", "acc", "daysInMonth", "getDaysInMonth", "year", "minimum", "maximum", "getMonth", "day", "currentDate", "<PERSON><PERSON><PERSON><PERSON>", "daysInWeek", "Math", "min", "max", "hours", "lastHourInDay", "getHours", "hasMeridiem", "minutes", "getMinutes", "seconds", "getSeconds", "meridiem", "empty", "warnedOnceInvalidSection", "validateSections", "valueType", "supportedSections", "invalidSection", "find", "console", "warn", "transferDateSectionValue", "dateToTransferFrom", "dateToTransferTo", "getYear", "setMonth", "dayInWeekStrOfActiveDate", "formattedDaysInWeek", "dayInWeekOfActiveDate", "dayInWeekOfNewSectionValue", "diff", "getDate", "isAM", "mergedDateHours", "addHours", "reliableSectionModificationOrder", "mergeDateIntoReferenceDate", "referenceDate", "shouldLimitToEditedSections", "sort", "a", "b", "mergedDate", "modified", "isAndroid", "navigator", "userAgent", "toLowerCase", "getSectionOrder", "shouldApplyRTL", "neighbors", "for<PERSON>ach", "leftIndex", "rightIndex", "startIndex", "endIndex", "rtl2ltr", "ltr2rtl", "groupedSectionsStart", "groupedSectionsEnd", "RTLIndex", "findIndex", "rtlIndex", "parseSelectedSections", "selectedSections"], "sources": ["C:/Users/<USER>/PycharmProjects/CMS/webapp/frontend/node_modules/@mui/x-date-pickers/esm/internals/hooks/useField/useField.utils.js"], "sourcesContent": ["import { getMonthsInYear } from \"../../utils/date-utils.js\";\nexport const getDateSectionConfigFromFormatToken = (utils, formatToken) => {\n  const config = utils.formatTokenMap[formatToken];\n  if (config == null) {\n    throw new Error([`MUI X: The token \"${formatToken}\" is not supported by the Date and Time Pickers.`, 'Please try using another token or open an issue on https://github.com/mui/mui-x/issues/new/choose if you think it should be supported.'].join('\\n'));\n  }\n  if (typeof config === 'string') {\n    return {\n      type: config,\n      contentType: config === 'meridiem' ? 'letter' : 'digit',\n      maxLength: undefined\n    };\n  }\n  return {\n    type: config.sectionType,\n    contentType: config.contentType,\n    maxLength: config.maxLength\n  };\n};\nexport const getDaysInWeekStr = (utils, format) => {\n  const elements = [];\n  const now = utils.date(undefined, 'default');\n  const startDate = utils.startOfWeek(now);\n  const endDate = utils.endOfWeek(now);\n  let current = startDate;\n  while (utils.isBefore(current, endDate)) {\n    elements.push(current);\n    current = utils.addDays(current, 1);\n  }\n  return elements.map(weekDay => utils.formatByString(weekDay, format));\n};\nexport const getLetterEditingOptions = (utils, timezone, sectionType, format) => {\n  switch (sectionType) {\n    case 'month':\n      {\n        return getMonthsInYear(utils, utils.date(undefined, timezone)).map(month => utils.formatByString(month, format));\n      }\n    case 'weekDay':\n      {\n        return getDaysInWeekStr(utils, format);\n      }\n    case 'meridiem':\n      {\n        const now = utils.date(undefined, timezone);\n        return [utils.startOfDay(now), utils.endOfDay(now)].map(date => utils.formatByString(date, format));\n      }\n    default:\n      {\n        return [];\n      }\n  }\n};\n\n// This format should be the same on all the adapters\n// If some adapter does not respect this convention, then we will need to hardcode the format on each adapter.\nexport const FORMAT_SECONDS_NO_LEADING_ZEROS = 's';\nconst NON_LOCALIZED_DIGITS = ['0', '1', '2', '3', '4', '5', '6', '7', '8', '9'];\nexport const getLocalizedDigits = utils => {\n  const today = utils.date(undefined);\n  const formattedZero = utils.formatByString(utils.setSeconds(today, 0), FORMAT_SECONDS_NO_LEADING_ZEROS);\n  if (formattedZero === '0') {\n    return NON_LOCALIZED_DIGITS;\n  }\n  return Array.from({\n    length: 10\n  }).map((_, index) => utils.formatByString(utils.setSeconds(today, index), FORMAT_SECONDS_NO_LEADING_ZEROS));\n};\nexport const removeLocalizedDigits = (valueStr, localizedDigits) => {\n  if (localizedDigits[0] === '0') {\n    return valueStr;\n  }\n  const digits = [];\n  let currentFormattedDigit = '';\n  for (let i = 0; i < valueStr.length; i += 1) {\n    currentFormattedDigit += valueStr[i];\n    const matchingDigitIndex = localizedDigits.indexOf(currentFormattedDigit);\n    if (matchingDigitIndex > -1) {\n      digits.push(matchingDigitIndex.toString());\n      currentFormattedDigit = '';\n    }\n  }\n  return digits.join('');\n};\nexport const applyLocalizedDigits = (valueStr, localizedDigits) => {\n  if (localizedDigits[0] === '0') {\n    return valueStr;\n  }\n  return valueStr.split('').map(char => localizedDigits[Number(char)]).join('');\n};\nexport const isStringNumber = (valueStr, localizedDigits) => {\n  const nonLocalizedValueStr = removeLocalizedDigits(valueStr, localizedDigits);\n  // `Number(' ')` returns `0` even if ' ' is not a valid number.\n  return nonLocalizedValueStr !== ' ' && !Number.isNaN(Number(nonLocalizedValueStr));\n};\n\n/**\n * Make sure the value of a digit section have the right amount of leading zeros.\n * E.g.: `03` => `3`\n * Warning: Should only be called with non-localized digits. Call `removeLocalizedDigits` with your value if needed.\n */\nexport const cleanLeadingZeros = (valueStr, size) => {\n  // Remove the leading zeros and then add back as many as needed.\n  return Number(valueStr).toString().padStart(size, '0');\n};\nexport const cleanDigitSectionValue = (utils, value, sectionBoundaries, localizedDigits, section) => {\n  if (process.env.NODE_ENV !== 'production') {\n    if (section.type !== 'day' && section.contentType === 'digit-with-letter') {\n      throw new Error([`MUI X: The token \"${section.format}\" is a digit format with letter in it.'\n             This type of format is only supported for 'day' sections`].join('\\n'));\n    }\n  }\n  if (section.type === 'day' && section.contentType === 'digit-with-letter') {\n    const date = utils.setDate(sectionBoundaries.longestMonth, value);\n    return utils.formatByString(date, section.format);\n  }\n\n  // queryValue without leading `0` (`01` => `1`)\n  let valueStr = value.toString();\n  if (section.hasLeadingZerosInInput) {\n    valueStr = cleanLeadingZeros(valueStr, section.maxLength);\n  }\n  return applyLocalizedDigits(valueStr, localizedDigits);\n};\nexport const getSectionVisibleValue = (section, target, localizedDigits) => {\n  let value = section.value || section.placeholder;\n  const hasLeadingZeros = target === 'non-input' ? section.hasLeadingZerosInFormat : section.hasLeadingZerosInInput;\n  if (target === 'non-input' && section.hasLeadingZerosInInput && !section.hasLeadingZerosInFormat) {\n    value = Number(removeLocalizedDigits(value, localizedDigits)).toString();\n  }\n\n  // In the input, we add an empty character at the end of each section without leading zeros.\n  // This makes sure that `onChange` will always be fired.\n  // Otherwise, when your input value equals `1/dd/yyyy` (format `M/DD/YYYY` on DayJs),\n  // If you press `1`, on the first section, the new value is also `1/dd/yyyy`,\n  // So the browser will not fire the input `onChange`.\n  const shouldAddInvisibleSpace = ['input-rtl', 'input-ltr'].includes(target) && section.contentType === 'digit' && !hasLeadingZeros && value.length === 1;\n  if (shouldAddInvisibleSpace) {\n    value = `${value}\\u200e`;\n  }\n  if (target === 'input-rtl') {\n    value = `\\u2068${value}\\u2069`;\n  }\n  return value;\n};\nexport const changeSectionValueFormat = (utils, valueStr, currentFormat, newFormat) => {\n  if (process.env.NODE_ENV !== 'production') {\n    if (getDateSectionConfigFromFormatToken(utils, currentFormat).type === 'weekDay') {\n      throw new Error(\"changeSectionValueFormat doesn't support week day formats\");\n    }\n  }\n  return utils.formatByString(utils.parse(valueStr, currentFormat), newFormat);\n};\nconst isFourDigitYearFormat = (utils, format) => utils.formatByString(utils.date(undefined, 'system'), format).length === 4;\nexport const doesSectionFormatHaveLeadingZeros = (utils, contentType, sectionType, format) => {\n  if (contentType !== 'digit') {\n    return false;\n  }\n  const now = utils.date(undefined, 'default');\n  switch (sectionType) {\n    // We can't use `changeSectionValueFormat`, because  `utils.parse('1', 'YYYY')` returns `1971` instead of `1`.\n    case 'year':\n      {\n        // Remove once https://github.com/iamkun/dayjs/pull/2847 is merged and bump dayjs version\n        if (utils.lib === 'dayjs' && format === 'YY') {\n          return true;\n        }\n        return utils.formatByString(utils.setYear(now, 1), format).startsWith('0');\n      }\n    case 'month':\n      {\n        return utils.formatByString(utils.startOfYear(now), format).length > 1;\n      }\n    case 'day':\n      {\n        return utils.formatByString(utils.startOfMonth(now), format).length > 1;\n      }\n    case 'weekDay':\n      {\n        return utils.formatByString(utils.startOfWeek(now), format).length > 1;\n      }\n    case 'hours':\n      {\n        return utils.formatByString(utils.setHours(now, 1), format).length > 1;\n      }\n    case 'minutes':\n      {\n        return utils.formatByString(utils.setMinutes(now, 1), format).length > 1;\n      }\n    case 'seconds':\n      {\n        return utils.formatByString(utils.setSeconds(now, 1), format).length > 1;\n      }\n    default:\n      {\n        throw new Error('Invalid section type');\n      }\n  }\n};\n\n/**\n * Some date libraries like `dayjs` don't support parsing from date with escaped characters.\n * To make sure that the parsing works, we are building a format and a date without any separator.\n */\nexport const getDateFromDateSections = (utils, sections, localizedDigits) => {\n  // If we have both a day and a weekDay section,\n  // Then we skip the weekDay in the parsing because libraries like dayjs can't parse complicated formats containing a weekDay.\n  // dayjs(dayjs().format('dddd MMMM D YYYY'), 'dddd MMMM D YYYY')) // returns `Invalid Date` even if the format is valid.\n  const shouldSkipWeekDays = sections.some(section => section.type === 'day');\n  const sectionFormats = [];\n  const sectionValues = [];\n  for (let i = 0; i < sections.length; i += 1) {\n    const section = sections[i];\n    const shouldSkip = shouldSkipWeekDays && section.type === 'weekDay';\n    if (!shouldSkip) {\n      sectionFormats.push(section.format);\n      sectionValues.push(getSectionVisibleValue(section, 'non-input', localizedDigits));\n    }\n  }\n  const formatWithoutSeparator = sectionFormats.join(' ');\n  const dateWithoutSeparatorStr = sectionValues.join(' ');\n  return utils.parse(dateWithoutSeparatorStr, formatWithoutSeparator);\n};\nexport const createDateStrForV7HiddenInputFromSections = sections => sections.map(section => {\n  return `${section.startSeparator}${section.value || section.placeholder}${section.endSeparator}`;\n}).join('');\nexport const createDateStrForV6InputFromSections = (sections, localizedDigits, isRtl) => {\n  const formattedSections = sections.map(section => {\n    const dateValue = getSectionVisibleValue(section, isRtl ? 'input-rtl' : 'input-ltr', localizedDigits);\n    return `${section.startSeparator}${dateValue}${section.endSeparator}`;\n  });\n  const dateStr = formattedSections.join('');\n  if (!isRtl) {\n    return dateStr;\n  }\n\n  // \\u2066: start left-to-right isolation\n  // \\u2067: start right-to-left isolation\n  // \\u2068: start first strong character isolation\n  // \\u2069: pop isolation\n  // wrap into an isolated group such that separators can split the string in smaller ones by adding \\u2069\\u2068\n  return `\\u2066${dateStr}\\u2069`;\n};\nexport const getSectionsBoundaries = (utils, localizedDigits, timezone) => {\n  const today = utils.date(undefined, timezone);\n  const endOfYear = utils.endOfYear(today);\n  const endOfDay = utils.endOfDay(today);\n  const {\n    maxDaysInMonth,\n    longestMonth\n  } = getMonthsInYear(utils, today).reduce((acc, month) => {\n    const daysInMonth = utils.getDaysInMonth(month);\n    if (daysInMonth > acc.maxDaysInMonth) {\n      return {\n        maxDaysInMonth: daysInMonth,\n        longestMonth: month\n      };\n    }\n    return acc;\n  }, {\n    maxDaysInMonth: 0,\n    longestMonth: null\n  });\n  return {\n    year: ({\n      format\n    }) => ({\n      minimum: 0,\n      maximum: isFourDigitYearFormat(utils, format) ? 9999 : 99\n    }),\n    month: () => ({\n      minimum: 1,\n      // Assumption: All years have the same amount of months\n      maximum: utils.getMonth(endOfYear) + 1\n    }),\n    day: ({\n      currentDate\n    }) => ({\n      minimum: 1,\n      maximum: utils.isValid(currentDate) ? utils.getDaysInMonth(currentDate) : maxDaysInMonth,\n      longestMonth: longestMonth\n    }),\n    weekDay: ({\n      format,\n      contentType\n    }) => {\n      if (contentType === 'digit') {\n        const daysInWeek = getDaysInWeekStr(utils, format).map(Number);\n        return {\n          minimum: Math.min(...daysInWeek),\n          maximum: Math.max(...daysInWeek)\n        };\n      }\n      return {\n        minimum: 1,\n        maximum: 7\n      };\n    },\n    hours: ({\n      format\n    }) => {\n      const lastHourInDay = utils.getHours(endOfDay);\n      const hasMeridiem = removeLocalizedDigits(utils.formatByString(utils.endOfDay(today), format), localizedDigits) !== lastHourInDay.toString();\n      if (hasMeridiem) {\n        return {\n          minimum: 1,\n          maximum: Number(removeLocalizedDigits(utils.formatByString(utils.startOfDay(today), format), localizedDigits))\n        };\n      }\n      return {\n        minimum: 0,\n        maximum: lastHourInDay\n      };\n    },\n    minutes: () => ({\n      minimum: 0,\n      // Assumption: All years have the same amount of minutes\n      maximum: utils.getMinutes(endOfDay)\n    }),\n    seconds: () => ({\n      minimum: 0,\n      // Assumption: All years have the same amount of seconds\n      maximum: utils.getSeconds(endOfDay)\n    }),\n    meridiem: () => ({\n      minimum: 0,\n      maximum: 1\n    }),\n    empty: () => ({\n      minimum: 0,\n      maximum: 0\n    })\n  };\n};\nlet warnedOnceInvalidSection = false;\nexport const validateSections = (sections, valueType) => {\n  if (process.env.NODE_ENV !== 'production') {\n    if (!warnedOnceInvalidSection) {\n      const supportedSections = ['empty'];\n      if (['date', 'date-time'].includes(valueType)) {\n        supportedSections.push('weekDay', 'day', 'month', 'year');\n      }\n      if (['time', 'date-time'].includes(valueType)) {\n        supportedSections.push('hours', 'minutes', 'seconds', 'meridiem');\n      }\n      const invalidSection = sections.find(section => !supportedSections.includes(section.type));\n      if (invalidSection) {\n        console.warn(`MUI X: The field component you are using is not compatible with the \"${invalidSection.type}\" date section.`, `The supported date sections are [\"${supportedSections.join('\", \"')}\"]\\`.`);\n        warnedOnceInvalidSection = true;\n      }\n    }\n  }\n};\nconst transferDateSectionValue = (utils, section, dateToTransferFrom, dateToTransferTo) => {\n  switch (section.type) {\n    case 'year':\n      {\n        return utils.setYear(dateToTransferTo, utils.getYear(dateToTransferFrom));\n      }\n    case 'month':\n      {\n        return utils.setMonth(dateToTransferTo, utils.getMonth(dateToTransferFrom));\n      }\n    case 'weekDay':\n      {\n        let dayInWeekStrOfActiveDate = utils.formatByString(dateToTransferFrom, section.format);\n        if (section.hasLeadingZerosInInput) {\n          dayInWeekStrOfActiveDate = cleanLeadingZeros(dayInWeekStrOfActiveDate, section.maxLength);\n        }\n        const formattedDaysInWeek = getDaysInWeekStr(utils, section.format);\n        const dayInWeekOfActiveDate = formattedDaysInWeek.indexOf(dayInWeekStrOfActiveDate);\n        const dayInWeekOfNewSectionValue = formattedDaysInWeek.indexOf(section.value);\n        const diff = dayInWeekOfNewSectionValue - dayInWeekOfActiveDate;\n        return utils.addDays(dateToTransferFrom, diff);\n      }\n    case 'day':\n      {\n        return utils.setDate(dateToTransferTo, utils.getDate(dateToTransferFrom));\n      }\n    case 'meridiem':\n      {\n        const isAM = utils.getHours(dateToTransferFrom) < 12;\n        const mergedDateHours = utils.getHours(dateToTransferTo);\n        if (isAM && mergedDateHours >= 12) {\n          return utils.addHours(dateToTransferTo, -12);\n        }\n        if (!isAM && mergedDateHours < 12) {\n          return utils.addHours(dateToTransferTo, 12);\n        }\n        return dateToTransferTo;\n      }\n    case 'hours':\n      {\n        return utils.setHours(dateToTransferTo, utils.getHours(dateToTransferFrom));\n      }\n    case 'minutes':\n      {\n        return utils.setMinutes(dateToTransferTo, utils.getMinutes(dateToTransferFrom));\n      }\n    case 'seconds':\n      {\n        return utils.setSeconds(dateToTransferTo, utils.getSeconds(dateToTransferFrom));\n      }\n    default:\n      {\n        return dateToTransferTo;\n      }\n  }\n};\nconst reliableSectionModificationOrder = {\n  year: 1,\n  month: 2,\n  day: 3,\n  weekDay: 4,\n  hours: 5,\n  minutes: 6,\n  seconds: 7,\n  meridiem: 8,\n  empty: 9\n};\nexport const mergeDateIntoReferenceDate = (utils, dateToTransferFrom, sections, referenceDate, shouldLimitToEditedSections) =>\n// cloning sections before sort to avoid mutating it\n[...sections].sort((a, b) => reliableSectionModificationOrder[a.type] - reliableSectionModificationOrder[b.type]).reduce((mergedDate, section) => {\n  if (!shouldLimitToEditedSections || section.modified) {\n    return transferDateSectionValue(utils, section, dateToTransferFrom, mergedDate);\n  }\n  return mergedDate;\n}, referenceDate);\nexport const isAndroid = () => navigator.userAgent.toLowerCase().includes('android');\n\n// TODO v9: Remove\nexport const getSectionOrder = (sections, shouldApplyRTL) => {\n  const neighbors = {};\n  if (!shouldApplyRTL) {\n    sections.forEach((_, index) => {\n      const leftIndex = index === 0 ? null : index - 1;\n      const rightIndex = index === sections.length - 1 ? null : index + 1;\n      neighbors[index] = {\n        leftIndex,\n        rightIndex\n      };\n    });\n    return {\n      neighbors,\n      startIndex: 0,\n      endIndex: sections.length - 1\n    };\n  }\n  const rtl2ltr = {};\n  const ltr2rtl = {};\n  let groupedSectionsStart = 0;\n  let groupedSectionsEnd = 0;\n  let RTLIndex = sections.length - 1;\n  while (RTLIndex >= 0) {\n    groupedSectionsEnd = sections.findIndex(\n    // eslint-disable-next-line @typescript-eslint/no-loop-func\n    (section, index) => index >= groupedSectionsStart && section.endSeparator?.includes(' ') &&\n    // Special case where the spaces were not there in the initial input\n    section.endSeparator !== ' / ');\n    if (groupedSectionsEnd === -1) {\n      groupedSectionsEnd = sections.length - 1;\n    }\n    for (let i = groupedSectionsEnd; i >= groupedSectionsStart; i -= 1) {\n      ltr2rtl[i] = RTLIndex;\n      rtl2ltr[RTLIndex] = i;\n      RTLIndex -= 1;\n    }\n    groupedSectionsStart = groupedSectionsEnd + 1;\n  }\n  sections.forEach((_, index) => {\n    const rtlIndex = ltr2rtl[index];\n    const leftIndex = rtlIndex === 0 ? null : rtl2ltr[rtlIndex - 1];\n    const rightIndex = rtlIndex === sections.length - 1 ? null : rtl2ltr[rtlIndex + 1];\n    neighbors[index] = {\n      leftIndex,\n      rightIndex\n    };\n  });\n  return {\n    neighbors,\n    startIndex: rtl2ltr[0],\n    endIndex: rtl2ltr[sections.length - 1]\n  };\n};\nexport const parseSelectedSections = (selectedSections, sections) => {\n  if (selectedSections == null) {\n    return null;\n  }\n  if (selectedSections === 'all') {\n    return 'all';\n  }\n  if (typeof selectedSections === 'string') {\n    const index = sections.findIndex(section => section.type === selectedSections);\n    return index === -1 ? null : index;\n  }\n  return selectedSections;\n};"], "mappings": "AAAA,SAASA,eAAe,QAAQ,2BAA2B;AAC3D,OAAO,MAAMC,mCAAmC,GAAGA,CAACC,KAAK,EAAEC,WAAW,KAAK;EACzE,MAAMC,MAAM,GAAGF,KAAK,CAACG,cAAc,CAACF,WAAW,CAAC;EAChD,IAAIC,MAAM,IAAI,IAAI,EAAE;IAClB,MAAM,IAAIE,KAAK,CAAC,CAAC,qBAAqBH,WAAW,kDAAkD,EAAE,wIAAwI,CAAC,CAACI,IAAI,CAAC,IAAI,CAAC,CAAC;EAC5P;EACA,IAAI,OAAOH,MAAM,KAAK,QAAQ,EAAE;IAC9B,OAAO;MACLI,IAAI,EAAEJ,MAAM;MACZK,WAAW,EAAEL,MAAM,KAAK,UAAU,GAAG,QAAQ,GAAG,OAAO;MACvDM,SAAS,EAAEC;IACb,CAAC;EACH;EACA,OAAO;IACLH,IAAI,EAAEJ,MAAM,CAACQ,WAAW;IACxBH,WAAW,EAAEL,MAAM,CAACK,WAAW;IAC/BC,SAAS,EAAEN,MAAM,CAACM;EACpB,CAAC;AACH,CAAC;AACD,OAAO,MAAMG,gBAAgB,GAAGA,CAACX,KAAK,EAAEY,MAAM,KAAK;EACjD,MAAMC,QAAQ,GAAG,EAAE;EACnB,MAAMC,GAAG,GAAGd,KAAK,CAACe,IAAI,CAACN,SAAS,EAAE,SAAS,CAAC;EAC5C,MAAMO,SAAS,GAAGhB,KAAK,CAACiB,WAAW,CAACH,GAAG,CAAC;EACxC,MAAMI,OAAO,GAAGlB,KAAK,CAACmB,SAAS,CAACL,GAAG,CAAC;EACpC,IAAIM,OAAO,GAAGJ,SAAS;EACvB,OAAOhB,KAAK,CAACqB,QAAQ,CAACD,OAAO,EAAEF,OAAO,CAAC,EAAE;IACvCL,QAAQ,CAACS,IAAI,CAACF,OAAO,CAAC;IACtBA,OAAO,GAAGpB,KAAK,CAACuB,OAAO,CAACH,OAAO,EAAE,CAAC,CAAC;EACrC;EACA,OAAOP,QAAQ,CAACW,GAAG,CAACC,OAAO,IAAIzB,KAAK,CAAC0B,cAAc,CAACD,OAAO,EAAEb,MAAM,CAAC,CAAC;AACvE,CAAC;AACD,OAAO,MAAMe,uBAAuB,GAAGA,CAAC3B,KAAK,EAAE4B,QAAQ,EAAElB,WAAW,EAAEE,MAAM,KAAK;EAC/E,QAAQF,WAAW;IACjB,KAAK,OAAO;MACV;QACE,OAAOZ,eAAe,CAACE,KAAK,EAAEA,KAAK,CAACe,IAAI,CAACN,SAAS,EAAEmB,QAAQ,CAAC,CAAC,CAACJ,GAAG,CAACK,KAAK,IAAI7B,KAAK,CAAC0B,cAAc,CAACG,KAAK,EAAEjB,MAAM,CAAC,CAAC;MAClH;IACF,KAAK,SAAS;MACZ;QACE,OAAOD,gBAAgB,CAACX,KAAK,EAAEY,MAAM,CAAC;MACxC;IACF,KAAK,UAAU;MACb;QACE,MAAME,GAAG,GAAGd,KAAK,CAACe,IAAI,CAACN,SAAS,EAAEmB,QAAQ,CAAC;QAC3C,OAAO,CAAC5B,KAAK,CAAC8B,UAAU,CAAChB,GAAG,CAAC,EAAEd,KAAK,CAAC+B,QAAQ,CAACjB,GAAG,CAAC,CAAC,CAACU,GAAG,CAACT,IAAI,IAAIf,KAAK,CAAC0B,cAAc,CAACX,IAAI,EAAEH,MAAM,CAAC,CAAC;MACrG;IACF;MACE;QACE,OAAO,EAAE;MACX;EACJ;AACF,CAAC;;AAED;AACA;AACA,OAAO,MAAMoB,+BAA+B,GAAG,GAAG;AAClD,MAAMC,oBAAoB,GAAG,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,CAAC;AAC/E,OAAO,MAAMC,kBAAkB,GAAGlC,KAAK,IAAI;EACzC,MAAMmC,KAAK,GAAGnC,KAAK,CAACe,IAAI,CAACN,SAAS,CAAC;EACnC,MAAM2B,aAAa,GAAGpC,KAAK,CAAC0B,cAAc,CAAC1B,KAAK,CAACqC,UAAU,CAACF,KAAK,EAAE,CAAC,CAAC,EAAEH,+BAA+B,CAAC;EACvG,IAAII,aAAa,KAAK,GAAG,EAAE;IACzB,OAAOH,oBAAoB;EAC7B;EACA,OAAOK,KAAK,CAACC,IAAI,CAAC;IAChBC,MAAM,EAAE;EACV,CAAC,CAAC,CAAChB,GAAG,CAAC,CAACiB,CAAC,EAAEC,KAAK,KAAK1C,KAAK,CAAC0B,cAAc,CAAC1B,KAAK,CAACqC,UAAU,CAACF,KAAK,EAAEO,KAAK,CAAC,EAAEV,+BAA+B,CAAC,CAAC;AAC7G,CAAC;AACD,OAAO,MAAMW,qBAAqB,GAAGA,CAACC,QAAQ,EAAEC,eAAe,KAAK;EAClE,IAAIA,eAAe,CAAC,CAAC,CAAC,KAAK,GAAG,EAAE;IAC9B,OAAOD,QAAQ;EACjB;EACA,MAAME,MAAM,GAAG,EAAE;EACjB,IAAIC,qBAAqB,GAAG,EAAE;EAC9B,KAAK,IAAIC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGJ,QAAQ,CAACJ,MAAM,EAAEQ,CAAC,IAAI,CAAC,EAAE;IAC3CD,qBAAqB,IAAIH,QAAQ,CAACI,CAAC,CAAC;IACpC,MAAMC,kBAAkB,GAAGJ,eAAe,CAACK,OAAO,CAACH,qBAAqB,CAAC;IACzE,IAAIE,kBAAkB,GAAG,CAAC,CAAC,EAAE;MAC3BH,MAAM,CAACxB,IAAI,CAAC2B,kBAAkB,CAACE,QAAQ,CAAC,CAAC,CAAC;MAC1CJ,qBAAqB,GAAG,EAAE;IAC5B;EACF;EACA,OAAOD,MAAM,CAACzC,IAAI,CAAC,EAAE,CAAC;AACxB,CAAC;AACD,OAAO,MAAM+C,oBAAoB,GAAGA,CAACR,QAAQ,EAAEC,eAAe,KAAK;EACjE,IAAIA,eAAe,CAAC,CAAC,CAAC,KAAK,GAAG,EAAE;IAC9B,OAAOD,QAAQ;EACjB;EACA,OAAOA,QAAQ,CAACS,KAAK,CAAC,EAAE,CAAC,CAAC7B,GAAG,CAAC8B,IAAI,IAAIT,eAAe,CAACU,MAAM,CAACD,IAAI,CAAC,CAAC,CAAC,CAACjD,IAAI,CAAC,EAAE,CAAC;AAC/E,CAAC;AACD,OAAO,MAAMmD,cAAc,GAAGA,CAACZ,QAAQ,EAAEC,eAAe,KAAK;EAC3D,MAAMY,oBAAoB,GAAGd,qBAAqB,CAACC,QAAQ,EAAEC,eAAe,CAAC;EAC7E;EACA,OAAOY,oBAAoB,KAAK,GAAG,IAAI,CAACF,MAAM,CAACG,KAAK,CAACH,MAAM,CAACE,oBAAoB,CAAC,CAAC;AACpF,CAAC;;AAED;AACA;AACA;AACA;AACA;AACA,OAAO,MAAME,iBAAiB,GAAGA,CAACf,QAAQ,EAAEgB,IAAI,KAAK;EACnD;EACA,OAAOL,MAAM,CAACX,QAAQ,CAAC,CAACO,QAAQ,CAAC,CAAC,CAACU,QAAQ,CAACD,IAAI,EAAE,GAAG,CAAC;AACxD,CAAC;AACD,OAAO,MAAME,sBAAsB,GAAGA,CAAC9D,KAAK,EAAE+D,KAAK,EAAEC,iBAAiB,EAAEnB,eAAe,EAAEoB,OAAO,KAAK;EACnG,IAAIC,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,EAAE;IACzC,IAAIH,OAAO,CAAC3D,IAAI,KAAK,KAAK,IAAI2D,OAAO,CAAC1D,WAAW,KAAK,mBAAmB,EAAE;MACzE,MAAM,IAAIH,KAAK,CAAC,CAAC,qBAAqB6D,OAAO,CAACrD,MAAM;AAC1D,sEAAsE,CAAC,CAACP,IAAI,CAAC,IAAI,CAAC,CAAC;IAC/E;EACF;EACA,IAAI4D,OAAO,CAAC3D,IAAI,KAAK,KAAK,IAAI2D,OAAO,CAAC1D,WAAW,KAAK,mBAAmB,EAAE;IACzE,MAAMQ,IAAI,GAAGf,KAAK,CAACqE,OAAO,CAACL,iBAAiB,CAACM,YAAY,EAAEP,KAAK,CAAC;IACjE,OAAO/D,KAAK,CAAC0B,cAAc,CAACX,IAAI,EAAEkD,OAAO,CAACrD,MAAM,CAAC;EACnD;;EAEA;EACA,IAAIgC,QAAQ,GAAGmB,KAAK,CAACZ,QAAQ,CAAC,CAAC;EAC/B,IAAIc,OAAO,CAACM,sBAAsB,EAAE;IAClC3B,QAAQ,GAAGe,iBAAiB,CAACf,QAAQ,EAAEqB,OAAO,CAACzD,SAAS,CAAC;EAC3D;EACA,OAAO4C,oBAAoB,CAACR,QAAQ,EAAEC,eAAe,CAAC;AACxD,CAAC;AACD,OAAO,MAAM2B,sBAAsB,GAAGA,CAACP,OAAO,EAAEQ,MAAM,EAAE5B,eAAe,KAAK;EAC1E,IAAIkB,KAAK,GAAGE,OAAO,CAACF,KAAK,IAAIE,OAAO,CAACS,WAAW;EAChD,MAAMC,eAAe,GAAGF,MAAM,KAAK,WAAW,GAAGR,OAAO,CAACW,uBAAuB,GAAGX,OAAO,CAACM,sBAAsB;EACjH,IAAIE,MAAM,KAAK,WAAW,IAAIR,OAAO,CAACM,sBAAsB,IAAI,CAACN,OAAO,CAACW,uBAAuB,EAAE;IAChGb,KAAK,GAAGR,MAAM,CAACZ,qBAAqB,CAACoB,KAAK,EAAElB,eAAe,CAAC,CAAC,CAACM,QAAQ,CAAC,CAAC;EAC1E;;EAEA;EACA;EACA;EACA;EACA;EACA,MAAM0B,uBAAuB,GAAG,CAAC,WAAW,EAAE,WAAW,CAAC,CAACC,QAAQ,CAACL,MAAM,CAAC,IAAIR,OAAO,CAAC1D,WAAW,KAAK,OAAO,IAAI,CAACoE,eAAe,IAAIZ,KAAK,CAACvB,MAAM,KAAK,CAAC;EACxJ,IAAIqC,uBAAuB,EAAE;IAC3Bd,KAAK,GAAG,GAAGA,KAAK,QAAQ;EAC1B;EACA,IAAIU,MAAM,KAAK,WAAW,EAAE;IAC1BV,KAAK,GAAG,SAASA,KAAK,QAAQ;EAChC;EACA,OAAOA,KAAK;AACd,CAAC;AACD,OAAO,MAAMgB,wBAAwB,GAAGA,CAAC/E,KAAK,EAAE4C,QAAQ,EAAEoC,aAAa,EAAEC,SAAS,KAAK;EACrF,IAAIf,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,EAAE;IACzC,IAAIrE,mCAAmC,CAACC,KAAK,EAAEgF,aAAa,CAAC,CAAC1E,IAAI,KAAK,SAAS,EAAE;MAChF,MAAM,IAAIF,KAAK,CAAC,2DAA2D,CAAC;IAC9E;EACF;EACA,OAAOJ,KAAK,CAAC0B,cAAc,CAAC1B,KAAK,CAACkF,KAAK,CAACtC,QAAQ,EAAEoC,aAAa,CAAC,EAAEC,SAAS,CAAC;AAC9E,CAAC;AACD,MAAME,qBAAqB,GAAGA,CAACnF,KAAK,EAAEY,MAAM,KAAKZ,KAAK,CAAC0B,cAAc,CAAC1B,KAAK,CAACe,IAAI,CAACN,SAAS,EAAE,QAAQ,CAAC,EAAEG,MAAM,CAAC,CAAC4B,MAAM,KAAK,CAAC;AAC3H,OAAO,MAAM4C,iCAAiC,GAAGA,CAACpF,KAAK,EAAEO,WAAW,EAAEG,WAAW,EAAEE,MAAM,KAAK;EAC5F,IAAIL,WAAW,KAAK,OAAO,EAAE;IAC3B,OAAO,KAAK;EACd;EACA,MAAMO,GAAG,GAAGd,KAAK,CAACe,IAAI,CAACN,SAAS,EAAE,SAAS,CAAC;EAC5C,QAAQC,WAAW;IACjB;IACA,KAAK,MAAM;MACT;QACE;QACA,IAAIV,KAAK,CAACqF,GAAG,KAAK,OAAO,IAAIzE,MAAM,KAAK,IAAI,EAAE;UAC5C,OAAO,IAAI;QACb;QACA,OAAOZ,KAAK,CAAC0B,cAAc,CAAC1B,KAAK,CAACsF,OAAO,CAACxE,GAAG,EAAE,CAAC,CAAC,EAAEF,MAAM,CAAC,CAAC2E,UAAU,CAAC,GAAG,CAAC;MAC5E;IACF,KAAK,OAAO;MACV;QACE,OAAOvF,KAAK,CAAC0B,cAAc,CAAC1B,KAAK,CAACwF,WAAW,CAAC1E,GAAG,CAAC,EAAEF,MAAM,CAAC,CAAC4B,MAAM,GAAG,CAAC;MACxE;IACF,KAAK,KAAK;MACR;QACE,OAAOxC,KAAK,CAAC0B,cAAc,CAAC1B,KAAK,CAACyF,YAAY,CAAC3E,GAAG,CAAC,EAAEF,MAAM,CAAC,CAAC4B,MAAM,GAAG,CAAC;MACzE;IACF,KAAK,SAAS;MACZ;QACE,OAAOxC,KAAK,CAAC0B,cAAc,CAAC1B,KAAK,CAACiB,WAAW,CAACH,GAAG,CAAC,EAAEF,MAAM,CAAC,CAAC4B,MAAM,GAAG,CAAC;MACxE;IACF,KAAK,OAAO;MACV;QACE,OAAOxC,KAAK,CAAC0B,cAAc,CAAC1B,KAAK,CAAC0F,QAAQ,CAAC5E,GAAG,EAAE,CAAC,CAAC,EAAEF,MAAM,CAAC,CAAC4B,MAAM,GAAG,CAAC;MACxE;IACF,KAAK,SAAS;MACZ;QACE,OAAOxC,KAAK,CAAC0B,cAAc,CAAC1B,KAAK,CAAC2F,UAAU,CAAC7E,GAAG,EAAE,CAAC,CAAC,EAAEF,MAAM,CAAC,CAAC4B,MAAM,GAAG,CAAC;MAC1E;IACF,KAAK,SAAS;MACZ;QACE,OAAOxC,KAAK,CAAC0B,cAAc,CAAC1B,KAAK,CAACqC,UAAU,CAACvB,GAAG,EAAE,CAAC,CAAC,EAAEF,MAAM,CAAC,CAAC4B,MAAM,GAAG,CAAC;MAC1E;IACF;MACE;QACE,MAAM,IAAIpC,KAAK,CAAC,sBAAsB,CAAC;MACzC;EACJ;AACF,CAAC;;AAED;AACA;AACA;AACA;AACA,OAAO,MAAMwF,uBAAuB,GAAGA,CAAC5F,KAAK,EAAE6F,QAAQ,EAAEhD,eAAe,KAAK;EAC3E;EACA;EACA;EACA,MAAMiD,kBAAkB,GAAGD,QAAQ,CAACE,IAAI,CAAC9B,OAAO,IAAIA,OAAO,CAAC3D,IAAI,KAAK,KAAK,CAAC;EAC3E,MAAM0F,cAAc,GAAG,EAAE;EACzB,MAAMC,aAAa,GAAG,EAAE;EACxB,KAAK,IAAIjD,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG6C,QAAQ,CAACrD,MAAM,EAAEQ,CAAC,IAAI,CAAC,EAAE;IAC3C,MAAMiB,OAAO,GAAG4B,QAAQ,CAAC7C,CAAC,CAAC;IAC3B,MAAMkD,UAAU,GAAGJ,kBAAkB,IAAI7B,OAAO,CAAC3D,IAAI,KAAK,SAAS;IACnE,IAAI,CAAC4F,UAAU,EAAE;MACfF,cAAc,CAAC1E,IAAI,CAAC2C,OAAO,CAACrD,MAAM,CAAC;MACnCqF,aAAa,CAAC3E,IAAI,CAACkD,sBAAsB,CAACP,OAAO,EAAE,WAAW,EAAEpB,eAAe,CAAC,CAAC;IACnF;EACF;EACA,MAAMsD,sBAAsB,GAAGH,cAAc,CAAC3F,IAAI,CAAC,GAAG,CAAC;EACvD,MAAM+F,uBAAuB,GAAGH,aAAa,CAAC5F,IAAI,CAAC,GAAG,CAAC;EACvD,OAAOL,KAAK,CAACkF,KAAK,CAACkB,uBAAuB,EAAED,sBAAsB,CAAC;AACrE,CAAC;AACD,OAAO,MAAME,yCAAyC,GAAGR,QAAQ,IAAIA,QAAQ,CAACrE,GAAG,CAACyC,OAAO,IAAI;EAC3F,OAAO,GAAGA,OAAO,CAACqC,cAAc,GAAGrC,OAAO,CAACF,KAAK,IAAIE,OAAO,CAACS,WAAW,GAAGT,OAAO,CAACsC,YAAY,EAAE;AAClG,CAAC,CAAC,CAAClG,IAAI,CAAC,EAAE,CAAC;AACX,OAAO,MAAMmG,mCAAmC,GAAGA,CAACX,QAAQ,EAAEhD,eAAe,EAAE4D,KAAK,KAAK;EACvF,MAAMC,iBAAiB,GAAGb,QAAQ,CAACrE,GAAG,CAACyC,OAAO,IAAI;IAChD,MAAM0C,SAAS,GAAGnC,sBAAsB,CAACP,OAAO,EAAEwC,KAAK,GAAG,WAAW,GAAG,WAAW,EAAE5D,eAAe,CAAC;IACrG,OAAO,GAAGoB,OAAO,CAACqC,cAAc,GAAGK,SAAS,GAAG1C,OAAO,CAACsC,YAAY,EAAE;EACvE,CAAC,CAAC;EACF,MAAMK,OAAO,GAAGF,iBAAiB,CAACrG,IAAI,CAAC,EAAE,CAAC;EAC1C,IAAI,CAACoG,KAAK,EAAE;IACV,OAAOG,OAAO;EAChB;;EAEA;EACA;EACA;EACA;EACA;EACA,OAAO,SAASA,OAAO,QAAQ;AACjC,CAAC;AACD,OAAO,MAAMC,qBAAqB,GAAGA,CAAC7G,KAAK,EAAE6C,eAAe,EAAEjB,QAAQ,KAAK;EACzE,MAAMO,KAAK,GAAGnC,KAAK,CAACe,IAAI,CAACN,SAAS,EAAEmB,QAAQ,CAAC;EAC7C,MAAMkF,SAAS,GAAG9G,KAAK,CAAC8G,SAAS,CAAC3E,KAAK,CAAC;EACxC,MAAMJ,QAAQ,GAAG/B,KAAK,CAAC+B,QAAQ,CAACI,KAAK,CAAC;EACtC,MAAM;IACJ4E,cAAc;IACdzC;EACF,CAAC,GAAGxE,eAAe,CAACE,KAAK,EAAEmC,KAAK,CAAC,CAAC6E,MAAM,CAAC,CAACC,GAAG,EAAEpF,KAAK,KAAK;IACvD,MAAMqF,WAAW,GAAGlH,KAAK,CAACmH,cAAc,CAACtF,KAAK,CAAC;IAC/C,IAAIqF,WAAW,GAAGD,GAAG,CAACF,cAAc,EAAE;MACpC,OAAO;QACLA,cAAc,EAAEG,WAAW;QAC3B5C,YAAY,EAAEzC;MAChB,CAAC;IACH;IACA,OAAOoF,GAAG;EACZ,CAAC,EAAE;IACDF,cAAc,EAAE,CAAC;IACjBzC,YAAY,EAAE;EAChB,CAAC,CAAC;EACF,OAAO;IACL8C,IAAI,EAAEA,CAAC;MACLxG;IACF,CAAC,MAAM;MACLyG,OAAO,EAAE,CAAC;MACVC,OAAO,EAAEnC,qBAAqB,CAACnF,KAAK,EAAEY,MAAM,CAAC,GAAG,IAAI,GAAG;IACzD,CAAC,CAAC;IACFiB,KAAK,EAAEA,CAAA,MAAO;MACZwF,OAAO,EAAE,CAAC;MACV;MACAC,OAAO,EAAEtH,KAAK,CAACuH,QAAQ,CAACT,SAAS,CAAC,GAAG;IACvC,CAAC,CAAC;IACFU,GAAG,EAAEA,CAAC;MACJC;IACF,CAAC,MAAM;MACLJ,OAAO,EAAE,CAAC;MACVC,OAAO,EAAEtH,KAAK,CAAC0H,OAAO,CAACD,WAAW,CAAC,GAAGzH,KAAK,CAACmH,cAAc,CAACM,WAAW,CAAC,GAAGV,cAAc;MACxFzC,YAAY,EAAEA;IAChB,CAAC,CAAC;IACF7C,OAAO,EAAEA,CAAC;MACRb,MAAM;MACNL;IACF,CAAC,KAAK;MACJ,IAAIA,WAAW,KAAK,OAAO,EAAE;QAC3B,MAAMoH,UAAU,GAAGhH,gBAAgB,CAACX,KAAK,EAAEY,MAAM,CAAC,CAACY,GAAG,CAAC+B,MAAM,CAAC;QAC9D,OAAO;UACL8D,OAAO,EAAEO,IAAI,CAACC,GAAG,CAAC,GAAGF,UAAU,CAAC;UAChCL,OAAO,EAAEM,IAAI,CAACE,GAAG,CAAC,GAAGH,UAAU;QACjC,CAAC;MACH;MACA,OAAO;QACLN,OAAO,EAAE,CAAC;QACVC,OAAO,EAAE;MACX,CAAC;IACH,CAAC;IACDS,KAAK,EAAEA,CAAC;MACNnH;IACF,CAAC,KAAK;MACJ,MAAMoH,aAAa,GAAGhI,KAAK,CAACiI,QAAQ,CAAClG,QAAQ,CAAC;MAC9C,MAAMmG,WAAW,GAAGvF,qBAAqB,CAAC3C,KAAK,CAAC0B,cAAc,CAAC1B,KAAK,CAAC+B,QAAQ,CAACI,KAAK,CAAC,EAAEvB,MAAM,CAAC,EAAEiC,eAAe,CAAC,KAAKmF,aAAa,CAAC7E,QAAQ,CAAC,CAAC;MAC5I,IAAI+E,WAAW,EAAE;QACf,OAAO;UACLb,OAAO,EAAE,CAAC;UACVC,OAAO,EAAE/D,MAAM,CAACZ,qBAAqB,CAAC3C,KAAK,CAAC0B,cAAc,CAAC1B,KAAK,CAAC8B,UAAU,CAACK,KAAK,CAAC,EAAEvB,MAAM,CAAC,EAAEiC,eAAe,CAAC;QAC/G,CAAC;MACH;MACA,OAAO;QACLwE,OAAO,EAAE,CAAC;QACVC,OAAO,EAAEU;MACX,CAAC;IACH,CAAC;IACDG,OAAO,EAAEA,CAAA,MAAO;MACdd,OAAO,EAAE,CAAC;MACV;MACAC,OAAO,EAAEtH,KAAK,CAACoI,UAAU,CAACrG,QAAQ;IACpC,CAAC,CAAC;IACFsG,OAAO,EAAEA,CAAA,MAAO;MACdhB,OAAO,EAAE,CAAC;MACV;MACAC,OAAO,EAAEtH,KAAK,CAACsI,UAAU,CAACvG,QAAQ;IACpC,CAAC,CAAC;IACFwG,QAAQ,EAAEA,CAAA,MAAO;MACflB,OAAO,EAAE,CAAC;MACVC,OAAO,EAAE;IACX,CAAC,CAAC;IACFkB,KAAK,EAAEA,CAAA,MAAO;MACZnB,OAAO,EAAE,CAAC;MACVC,OAAO,EAAE;IACX,CAAC;EACH,CAAC;AACH,CAAC;AACD,IAAImB,wBAAwB,GAAG,KAAK;AACpC,OAAO,MAAMC,gBAAgB,GAAGA,CAAC7C,QAAQ,EAAE8C,SAAS,KAAK;EACvD,IAAIzE,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,EAAE;IACzC,IAAI,CAACqE,wBAAwB,EAAE;MAC7B,MAAMG,iBAAiB,GAAG,CAAC,OAAO,CAAC;MACnC,IAAI,CAAC,MAAM,EAAE,WAAW,CAAC,CAAC9D,QAAQ,CAAC6D,SAAS,CAAC,EAAE;QAC7CC,iBAAiB,CAACtH,IAAI,CAAC,SAAS,EAAE,KAAK,EAAE,OAAO,EAAE,MAAM,CAAC;MAC3D;MACA,IAAI,CAAC,MAAM,EAAE,WAAW,CAAC,CAACwD,QAAQ,CAAC6D,SAAS,CAAC,EAAE;QAC7CC,iBAAiB,CAACtH,IAAI,CAAC,OAAO,EAAE,SAAS,EAAE,SAAS,EAAE,UAAU,CAAC;MACnE;MACA,MAAMuH,cAAc,GAAGhD,QAAQ,CAACiD,IAAI,CAAC7E,OAAO,IAAI,CAAC2E,iBAAiB,CAAC9D,QAAQ,CAACb,OAAO,CAAC3D,IAAI,CAAC,CAAC;MAC1F,IAAIuI,cAAc,EAAE;QAClBE,OAAO,CAACC,IAAI,CAAC,wEAAwEH,cAAc,CAACvI,IAAI,iBAAiB,EAAE,qCAAqCsI,iBAAiB,CAACvI,IAAI,CAAC,MAAM,CAAC,OAAO,CAAC;QACtMoI,wBAAwB,GAAG,IAAI;MACjC;IACF;EACF;AACF,CAAC;AACD,MAAMQ,wBAAwB,GAAGA,CAACjJ,KAAK,EAAEiE,OAAO,EAAEiF,kBAAkB,EAAEC,gBAAgB,KAAK;EACzF,QAAQlF,OAAO,CAAC3D,IAAI;IAClB,KAAK,MAAM;MACT;QACE,OAAON,KAAK,CAACsF,OAAO,CAAC6D,gBAAgB,EAAEnJ,KAAK,CAACoJ,OAAO,CAACF,kBAAkB,CAAC,CAAC;MAC3E;IACF,KAAK,OAAO;MACV;QACE,OAAOlJ,KAAK,CAACqJ,QAAQ,CAACF,gBAAgB,EAAEnJ,KAAK,CAACuH,QAAQ,CAAC2B,kBAAkB,CAAC,CAAC;MAC7E;IACF,KAAK,SAAS;MACZ;QACE,IAAII,wBAAwB,GAAGtJ,KAAK,CAAC0B,cAAc,CAACwH,kBAAkB,EAAEjF,OAAO,CAACrD,MAAM,CAAC;QACvF,IAAIqD,OAAO,CAACM,sBAAsB,EAAE;UAClC+E,wBAAwB,GAAG3F,iBAAiB,CAAC2F,wBAAwB,EAAErF,OAAO,CAACzD,SAAS,CAAC;QAC3F;QACA,MAAM+I,mBAAmB,GAAG5I,gBAAgB,CAACX,KAAK,EAAEiE,OAAO,CAACrD,MAAM,CAAC;QACnE,MAAM4I,qBAAqB,GAAGD,mBAAmB,CAACrG,OAAO,CAACoG,wBAAwB,CAAC;QACnF,MAAMG,0BAA0B,GAAGF,mBAAmB,CAACrG,OAAO,CAACe,OAAO,CAACF,KAAK,CAAC;QAC7E,MAAM2F,IAAI,GAAGD,0BAA0B,GAAGD,qBAAqB;QAC/D,OAAOxJ,KAAK,CAACuB,OAAO,CAAC2H,kBAAkB,EAAEQ,IAAI,CAAC;MAChD;IACF,KAAK,KAAK;MACR;QACE,OAAO1J,KAAK,CAACqE,OAAO,CAAC8E,gBAAgB,EAAEnJ,KAAK,CAAC2J,OAAO,CAACT,kBAAkB,CAAC,CAAC;MAC3E;IACF,KAAK,UAAU;MACb;QACE,MAAMU,IAAI,GAAG5J,KAAK,CAACiI,QAAQ,CAACiB,kBAAkB,CAAC,GAAG,EAAE;QACpD,MAAMW,eAAe,GAAG7J,KAAK,CAACiI,QAAQ,CAACkB,gBAAgB,CAAC;QACxD,IAAIS,IAAI,IAAIC,eAAe,IAAI,EAAE,EAAE;UACjC,OAAO7J,KAAK,CAAC8J,QAAQ,CAACX,gBAAgB,EAAE,CAAC,EAAE,CAAC;QAC9C;QACA,IAAI,CAACS,IAAI,IAAIC,eAAe,GAAG,EAAE,EAAE;UACjC,OAAO7J,KAAK,CAAC8J,QAAQ,CAACX,gBAAgB,EAAE,EAAE,CAAC;QAC7C;QACA,OAAOA,gBAAgB;MACzB;IACF,KAAK,OAAO;MACV;QACE,OAAOnJ,KAAK,CAAC0F,QAAQ,CAACyD,gBAAgB,EAAEnJ,KAAK,CAACiI,QAAQ,CAACiB,kBAAkB,CAAC,CAAC;MAC7E;IACF,KAAK,SAAS;MACZ;QACE,OAAOlJ,KAAK,CAAC2F,UAAU,CAACwD,gBAAgB,EAAEnJ,KAAK,CAACoI,UAAU,CAACc,kBAAkB,CAAC,CAAC;MACjF;IACF,KAAK,SAAS;MACZ;QACE,OAAOlJ,KAAK,CAACqC,UAAU,CAAC8G,gBAAgB,EAAEnJ,KAAK,CAACsI,UAAU,CAACY,kBAAkB,CAAC,CAAC;MACjF;IACF;MACE;QACE,OAAOC,gBAAgB;MACzB;EACJ;AACF,CAAC;AACD,MAAMY,gCAAgC,GAAG;EACvC3C,IAAI,EAAE,CAAC;EACPvF,KAAK,EAAE,CAAC;EACR2F,GAAG,EAAE,CAAC;EACN/F,OAAO,EAAE,CAAC;EACVsG,KAAK,EAAE,CAAC;EACRI,OAAO,EAAE,CAAC;EACVE,OAAO,EAAE,CAAC;EACVE,QAAQ,EAAE,CAAC;EACXC,KAAK,EAAE;AACT,CAAC;AACD,OAAO,MAAMwB,0BAA0B,GAAGA,CAAChK,KAAK,EAAEkJ,kBAAkB,EAAErD,QAAQ,EAAEoE,aAAa,EAAEC,2BAA2B;AAC1H;AACA,CAAC,GAAGrE,QAAQ,CAAC,CAACsE,IAAI,CAAC,CAACC,CAAC,EAAEC,CAAC,KAAKN,gCAAgC,CAACK,CAAC,CAAC9J,IAAI,CAAC,GAAGyJ,gCAAgC,CAACM,CAAC,CAAC/J,IAAI,CAAC,CAAC,CAAC0G,MAAM,CAAC,CAACsD,UAAU,EAAErG,OAAO,KAAK;EAChJ,IAAI,CAACiG,2BAA2B,IAAIjG,OAAO,CAACsG,QAAQ,EAAE;IACpD,OAAOtB,wBAAwB,CAACjJ,KAAK,EAAEiE,OAAO,EAAEiF,kBAAkB,EAAEoB,UAAU,CAAC;EACjF;EACA,OAAOA,UAAU;AACnB,CAAC,EAAEL,aAAa,CAAC;AACjB,OAAO,MAAMO,SAAS,GAAGA,CAAA,KAAMC,SAAS,CAACC,SAAS,CAACC,WAAW,CAAC,CAAC,CAAC7F,QAAQ,CAAC,SAAS,CAAC;;AAEpF;AACA,OAAO,MAAM8F,eAAe,GAAGA,CAAC/E,QAAQ,EAAEgF,cAAc,KAAK;EAC3D,MAAMC,SAAS,GAAG,CAAC,CAAC;EACpB,IAAI,CAACD,cAAc,EAAE;IACnBhF,QAAQ,CAACkF,OAAO,CAAC,CAACtI,CAAC,EAAEC,KAAK,KAAK;MAC7B,MAAMsI,SAAS,GAAGtI,KAAK,KAAK,CAAC,GAAG,IAAI,GAAGA,KAAK,GAAG,CAAC;MAChD,MAAMuI,UAAU,GAAGvI,KAAK,KAAKmD,QAAQ,CAACrD,MAAM,GAAG,CAAC,GAAG,IAAI,GAAGE,KAAK,GAAG,CAAC;MACnEoI,SAAS,CAACpI,KAAK,CAAC,GAAG;QACjBsI,SAAS;QACTC;MACF,CAAC;IACH,CAAC,CAAC;IACF,OAAO;MACLH,SAAS;MACTI,UAAU,EAAE,CAAC;MACbC,QAAQ,EAAEtF,QAAQ,CAACrD,MAAM,GAAG;IAC9B,CAAC;EACH;EACA,MAAM4I,OAAO,GAAG,CAAC,CAAC;EAClB,MAAMC,OAAO,GAAG,CAAC,CAAC;EAClB,IAAIC,oBAAoB,GAAG,CAAC;EAC5B,IAAIC,kBAAkB,GAAG,CAAC;EAC1B,IAAIC,QAAQ,GAAG3F,QAAQ,CAACrD,MAAM,GAAG,CAAC;EAClC,OAAOgJ,QAAQ,IAAI,CAAC,EAAE;IACpBD,kBAAkB,GAAG1F,QAAQ,CAAC4F,SAAS;IACvC;IACA,CAACxH,OAAO,EAAEvB,KAAK,KAAKA,KAAK,IAAI4I,oBAAoB,IAAIrH,OAAO,CAACsC,YAAY,EAAEzB,QAAQ,CAAC,GAAG,CAAC;IACxF;IACAb,OAAO,CAACsC,YAAY,KAAK,KAAK,CAAC;IAC/B,IAAIgF,kBAAkB,KAAK,CAAC,CAAC,EAAE;MAC7BA,kBAAkB,GAAG1F,QAAQ,CAACrD,MAAM,GAAG,CAAC;IAC1C;IACA,KAAK,IAAIQ,CAAC,GAAGuI,kBAAkB,EAAEvI,CAAC,IAAIsI,oBAAoB,EAAEtI,CAAC,IAAI,CAAC,EAAE;MAClEqI,OAAO,CAACrI,CAAC,CAAC,GAAGwI,QAAQ;MACrBJ,OAAO,CAACI,QAAQ,CAAC,GAAGxI,CAAC;MACrBwI,QAAQ,IAAI,CAAC;IACf;IACAF,oBAAoB,GAAGC,kBAAkB,GAAG,CAAC;EAC/C;EACA1F,QAAQ,CAACkF,OAAO,CAAC,CAACtI,CAAC,EAAEC,KAAK,KAAK;IAC7B,MAAMgJ,QAAQ,GAAGL,OAAO,CAAC3I,KAAK,CAAC;IAC/B,MAAMsI,SAAS,GAAGU,QAAQ,KAAK,CAAC,GAAG,IAAI,GAAGN,OAAO,CAACM,QAAQ,GAAG,CAAC,CAAC;IAC/D,MAAMT,UAAU,GAAGS,QAAQ,KAAK7F,QAAQ,CAACrD,MAAM,GAAG,CAAC,GAAG,IAAI,GAAG4I,OAAO,CAACM,QAAQ,GAAG,CAAC,CAAC;IAClFZ,SAAS,CAACpI,KAAK,CAAC,GAAG;MACjBsI,SAAS;MACTC;IACF,CAAC;EACH,CAAC,CAAC;EACF,OAAO;IACLH,SAAS;IACTI,UAAU,EAAEE,OAAO,CAAC,CAAC,CAAC;IACtBD,QAAQ,EAAEC,OAAO,CAACvF,QAAQ,CAACrD,MAAM,GAAG,CAAC;EACvC,CAAC;AACH,CAAC;AACD,OAAO,MAAMmJ,qBAAqB,GAAGA,CAACC,gBAAgB,EAAE/F,QAAQ,KAAK;EACnE,IAAI+F,gBAAgB,IAAI,IAAI,EAAE;IAC5B,OAAO,IAAI;EACb;EACA,IAAIA,gBAAgB,KAAK,KAAK,EAAE;IAC9B,OAAO,KAAK;EACd;EACA,IAAI,OAAOA,gBAAgB,KAAK,QAAQ,EAAE;IACxC,MAAMlJ,KAAK,GAAGmD,QAAQ,CAAC4F,SAAS,CAACxH,OAAO,IAAIA,OAAO,CAAC3D,IAAI,KAAKsL,gBAAgB,CAAC;IAC9E,OAAOlJ,KAAK,KAAK,CAAC,CAAC,GAAG,IAAI,GAAGA,KAAK;EACpC;EACA,OAAOkJ,gBAAgB;AACzB,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}