{"ast": null, "code": "var _jsxFileName = \"C:\\\\CMS\\\\webapp\\\\frontend\\\\src\\\\components\\\\cavi\\\\CaviFilterableTable.js\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect } from 'react';\nimport { Box, Typography, Chip, TableRow, TableCell, Checkbox, IconButton, Button } from '@mui/material';\nimport { CheckBox as CheckBoxIcon, CheckBoxOutlineBlank as CheckBoxOutlineBlankIcon, Clear as ClearIcon, Straighten as RulerIcon, Settings as SettingsIcon, PlayArrow as StartIcon } from '@mui/icons-material';\nimport FilterableTable from '../common/FilterableTable';\nimport SmartCaviFilter from './SmartCaviFilter';\nimport ContextMenu from '../common/ContextMenu';\nimport useContextMenu from '../../hooks/useContextMenu';\nimport { formatDate } from '../../utils/dateUtils';\n\n/**\n * Componente per visualizzare la lista dei cavi con filtri in stile Excel\n *\n * @param {Object} props - Proprietà del componente\n * @param {Array} props.cavi - Lista dei cavi da visualizzare\n * @param {boolean} props.loading - Indica se i dati sono in caricamento\n * @param {Function} props.onFilteredDataChange - Funzione chiamata quando i dati filtrati cambiano\n * @param {string} props.revisioneCorrente - Revisione corrente da mostrare nelle statistiche\n * @param {boolean} props.selectionEnabled - Abilita la selezione dei cavi\n * @param {Array} props.selectedCavi - Array degli ID dei cavi selezionati\n * @param {Function} props.onSelectionChange - Funzione chiamata quando cambia la selezione\n * @param {Array} props.contextMenuItems - Array di elementi per il menu contestuale\n * @param {Function} props.onContextMenuAction - Funzione chiamata quando si clicca su un elemento del menu contestuale\n * @param {Function} props.onStatusAction - Funzione chiamata quando si clicca sul pulsante stato\n */\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst CaviFilterableTable = ({\n  cavi = [],\n  loading = false,\n  onFilteredDataChange = null,\n  revisioneCorrente = null,\n  selectionEnabled = false,\n  selectedCavi = [],\n  onSelectionChange = null,\n  contextMenuItems = [],\n  onContextMenuAction = null,\n  onStatusAction = null\n}) => {\n  _s();\n  const [filteredCavi, setFilteredCavi] = useState(cavi);\n  const [smartFilteredCavi, setSmartFilteredCavi] = useState(cavi);\n\n  // Hook per il menu contestuale\n  const {\n    contextMenu,\n    handleContextMenu,\n    closeContextMenu\n  } = useContextMenu();\n\n  // Aggiorna i dati filtrati quando cambiano i cavi\n  useEffect(() => {\n    setFilteredCavi(cavi);\n    setSmartFilteredCavi(cavi);\n  }, [cavi]);\n\n  // Notifica il componente padre quando cambiano i dati filtrati\n  const handleFilteredDataChange = data => {\n    setFilteredCavi(data);\n    if (onFilteredDataChange) {\n      onFilteredDataChange(data);\n    }\n  };\n\n  // Gestisce il cambio dei dati dal filtro intelligente\n  const handleSmartFilterChange = data => {\n    console.log('CaviFilterableTable - Smart filter change:', {\n      originalCount: cavi.length,\n      filteredCount: data.length,\n      filteredIds: data.map(c => c.id_cavo)\n    });\n    setSmartFilteredCavi(data);\n    // Il filtro intelligente ha la priorità sui filtri Excel-like\n    setFilteredCavi(data);\n    if (onFilteredDataChange) {\n      onFilteredDataChange(data);\n    }\n  };\n\n  // Gestisce la selezione di un singolo cavo\n  const handleCavoToggle = cavoId => {\n    if (!selectionEnabled || !onSelectionChange) return;\n    const isSelected = selectedCavi.includes(cavoId);\n    let newSelection;\n    if (isSelected) {\n      // Rimuovi dalla selezione\n      newSelection = selectedCavi.filter(id => id !== cavoId);\n      console.log(`Cavo ${cavoId} deselezionato`);\n    } else {\n      // Aggiungi alla selezione\n      newSelection = [...selectedCavi, cavoId];\n      console.log(`Cavo ${cavoId} selezionato`);\n    }\n    onSelectionChange(newSelection);\n\n    // Feedback visivo rapido (opzionale - può essere rimosso se troppo invasivo)\n    // Potresti aggiungere qui un piccolo toast o animazione\n  };\n\n  // Seleziona tutti i cavi visibili (filtrati)\n  const handleSelectAll = () => {\n    if (!selectionEnabled || !onSelectionChange) return;\n    const visibleCaviIds = filteredCavi.map(cavo => cavo.id_cavo);\n    const allSelected = visibleCaviIds.every(id => selectedCavi.includes(id));\n    if (allSelected) {\n      // Deseleziona tutti i cavi visibili\n      const newSelection = selectedCavi.filter(id => !visibleCaviIds.includes(id));\n      onSelectionChange(newSelection);\n    } else {\n      // Seleziona tutti i cavi visibili\n      const newSelection = [...new Set([...selectedCavi, ...visibleCaviIds])];\n      onSelectionChange(newSelection);\n    }\n  };\n\n  // Deseleziona tutti i cavi\n  const handleClearSelection = () => {\n    if (!selectionEnabled || !onSelectionChange) return;\n    onSelectionChange([]);\n  };\n\n  // Definizione delle colonne\n  const columns = [\n  // Colonna di selezione (solo se abilitata)\n  ...(selectionEnabled ? [{\n    field: 'selection',\n    headerName: '',\n    disableFilter: true,\n    disableSort: true,\n    width: 50,\n    align: 'center',\n    headerStyle: {\n      width: '50px',\n      padding: '4px'\n    },\n    cellStyle: {\n      width: '50px',\n      padding: '4px',\n      textAlign: 'center'\n    },\n    renderHeader: () => {\n      const visibleCaviIds = filteredCavi.map(cavo => cavo.id_cavo);\n      const allSelected = visibleCaviIds.length > 0 && visibleCaviIds.every(id => selectedCavi.includes(id));\n      const someSelected = visibleCaviIds.some(id => selectedCavi.includes(id));\n      return /*#__PURE__*/_jsxDEV(Checkbox, {\n        checked: allSelected,\n        indeterminate: someSelected && !allSelected,\n        onChange: handleSelectAll,\n        size: \"small\",\n        title: allSelected ? \"Deseleziona tutti\" : \"Seleziona tutti\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 146,\n        columnNumber: 11\n      }, this);\n    },\n    renderCell: row => /*#__PURE__*/_jsxDEV(Checkbox, {\n      checked: selectedCavi.includes(row.id_cavo),\n      onChange: () => handleCavoToggle(row.id_cavo),\n      size: \"small\",\n      onClick: e => e.stopPropagation()\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 156,\n      columnNumber: 9\n    }, this)\n  }] : []), {\n    field: 'id_cavo',\n    headerName: 'ID Cavo',\n    dataType: 'text',\n    headerStyle: {\n      fontWeight: 'bold'\n    }\n  },\n  // Colonna Revisione rimossa e spostata nella tabella delle statistiche\n  {\n    field: 'sistema',\n    headerName: 'Sistema',\n    dataType: 'text'\n  }, {\n    field: 'utility',\n    headerName: 'Utility',\n    dataType: 'text'\n  }, {\n    field: 'tipologia',\n    headerName: 'Tipologia',\n    dataType: 'text'\n  },\n  // n_conduttori field is now a spare field (kept in DB but hidden in UI)\n  {\n    field: 'sezione',\n    headerName: 'Formazione',\n    dataType: 'text',\n    align: 'right',\n    cellStyle: {\n      textAlign: 'right'\n    }\n  }, {\n    field: 'metri_teorici',\n    headerName: 'Metri Teorici',\n    dataType: 'number',\n    align: 'right',\n    cellStyle: {\n      textAlign: 'right'\n    },\n    renderCell: row => row.metri_teorici ? row.metri_teorici.toFixed(1) : '0'\n  }, {\n    field: 'metratura_reale',\n    headerName: 'Metri Reali',\n    dataType: 'number',\n    align: 'right',\n    cellStyle: {\n      textAlign: 'right'\n    },\n    renderCell: row => row.metratura_reale ? row.metratura_reale.toFixed(1) : '0'\n  }, {\n    field: 'stato_installazione',\n    headerName: 'Stato',\n    dataType: 'text',\n    renderCell: row => {\n      var _row$stato_installazi, _row$stato_installazi2;\n      // Determina colore, icona e azione in base allo stato\n      let color = 'default';\n      let icon = null;\n      let actionLabel = '';\n      let actionType = '';\n\n      // Normalizza lo stato per gestire diverse varianti\n      const statoNormalizzato = (_row$stato_installazi = row.stato_installazione) === null || _row$stato_installazi === void 0 ? void 0 : _row$stato_installazi.toUpperCase();\n      if (statoNormalizzato === 'INSTALLATO' || row.stato_installazione === 'Installato') {\n        color = 'success';\n        icon = /*#__PURE__*/_jsxDEV(SettingsIcon, {\n          fontSize: \"small\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 226,\n          columnNumber: 18\n        }, this);\n        actionLabel = 'Modifica Bobina';\n        actionType = 'modify_reel';\n      } else if (statoNormalizzato === 'IN_CORSO' || row.stato_installazione === 'In corso') {\n        color = 'warning';\n        icon = /*#__PURE__*/_jsxDEV(RulerIcon, {\n          fontSize: \"small\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 231,\n          columnNumber: 18\n        }, this);\n        actionLabel = 'Inserisci Metri Posati';\n        actionType = 'insert_meters';\n      } else if (statoNormalizzato === 'DA_INSTALLARE' || row.stato_installazione === 'Da installare' || !row.stato_installazione) {\n        color = 'error';\n        icon = /*#__PURE__*/_jsxDEV(StartIcon, {\n          fontSize: \"small\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 236,\n          columnNumber: 18\n        }, this);\n        actionLabel = 'Inserisci Metri Posati';\n        actionType = 'insert_meters';\n      }\n\n      // Debug: verifica se onStatusAction è definito e se c'è un'azione per questo stato\n      console.log('🔍 Rendering pulsante stato per cavo:', row.id_cavo, {\n        stato: row.stato_installazione,\n        statoType: typeof row.stato_installazione,\n        statoLength: (_row$stato_installazi2 = row.stato_installazione) === null || _row$stato_installazi2 === void 0 ? void 0 : _row$stato_installazi2.length,\n        onStatusAction: !!onStatusAction,\n        onStatusActionType: typeof onStatusAction,\n        actionType,\n        actionLabel,\n        hasAction: !!actionType,\n        willBeClickable: !!(onStatusAction && actionType && actionLabel),\n        // Debug completo del cavo\n        fullCavo: row\n      });\n\n      // Determina se il pulsante deve essere cliccabile\n      const isClickable = onStatusAction && actionType && actionLabel;\n      return /*#__PURE__*/_jsxDEV(Chip, {\n        label: row.stato_installazione || 'N/D',\n        size: \"small\",\n        color: color,\n        variant: \"outlined\",\n        icon: icon,\n        onClick: isClickable ? e => {\n          e.stopPropagation();\n          console.log('🔥 CLICK su pulsante stato!', {\n            cavoId: row.id_cavo,\n            stato: row.stato_installazione,\n            actionType,\n            actionLabel\n          });\n          onStatusAction(row, actionType, actionLabel);\n        } : undefined,\n        sx: {\n          cursor: isClickable ? 'pointer' : 'default',\n          transition: 'all 0.2s ease',\n          '&:hover': isClickable ? {\n            transform: 'scale(1.05)',\n            boxShadow: '0 2px 8px rgba(0,0,0,0.15)',\n            backgroundColor: `${color}.light`\n          } : {},\n          // Aggiungi un bordo più marcato per i pulsanti cliccabili\n          border: isClickable ? '2px solid currentColor' : '1px solid currentColor'\n        },\n        title: isClickable ? actionLabel : 'Nessuna azione disponibile'\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 260,\n        columnNumber: 11\n      }, this);\n    }\n  }, {\n    field: 'id_bobina',\n    headerName: 'Bobina',\n    dataType: 'text',\n    renderCell: row => {\n      // Gestione differenziata per null e BOBINA_VUOTA\n      if (row.id_bobina === null) {\n        // Per cavi non posati (id_bobina è null)\n        return '-';\n      } else if (row.id_bobina === 'BOBINA_VUOTA') {\n        // Per cavi posati senza bobina specifica\n        return 'BOBINA VUOTA';\n      } else if (!row.id_bobina) {\n        // Per altri casi in cui id_bobina è falsy (undefined, stringa vuota)\n        return '-';\n      }\n\n      // Estrai solo il numero della bobina (parte dopo '_B')\n      const match = row.id_bobina.match(/_B(.+)$/);\n      return match ? match[1] : row.id_bobina;\n    }\n  }, {\n    field: 'timestamp',\n    headerName: 'Data Modifica',\n    dataType: 'date',\n    renderCell: row => formatDate(row.timestamp)\n  }, {\n    field: 'collegamenti',\n    headerName: 'Collegamenti',\n    dataType: 'number',\n    align: 'center',\n    cellStyle: {\n      textAlign: 'center'\n    },\n    renderCell: row => {\n      let color = 'default';\n      if (row.collegamenti === 2) color = 'success';else if (row.collegamenti === 1) color = 'warning';else color = 'error';\n      return /*#__PURE__*/_jsxDEV(Chip, {\n        label: row.collegamenti,\n        size: \"small\",\n        color: color,\n        variant: \"outlined\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 333,\n        columnNumber: 11\n      }, this);\n    }\n  }];\n\n  // Renderizza una riga personalizzata\n  const renderRow = (row, index) => {\n    var _row$stato_installazi3;\n    // Determina il colore di sfondo in base allo stato\n    let bgColor = 'inherit';\n    const statoNormalizzato = (_row$stato_installazi3 = row.stato_installazione) === null || _row$stato_installazi3 === void 0 ? void 0 : _row$stato_installazi3.toUpperCase();\n    if (statoNormalizzato === 'INSTALLATO' || row.stato_installazione === 'Installato') {\n      bgColor = 'rgba(76, 175, 80, 0.1)';\n    } else if (statoNormalizzato === 'IN_CORSO' || row.stato_installazione === 'In corso') {\n      bgColor = 'rgba(255, 152, 0, 0.1)';\n    }\n\n    // Se la selezione è abilitata, evidenzia le righe selezionate\n    const isSelected = selectionEnabled && selectedCavi.includes(row.id_cavo);\n    if (isSelected) {\n      bgColor = 'rgba(25, 118, 210, 0.4)'; // Blu molto più marcato per le righe selezionate\n    }\n    return /*#__PURE__*/_jsxDEV(TableRow, {\n      selected: isSelected,\n      hover: true,\n      onClick: selectionEnabled ? () => handleCavoToggle(row.id_cavo) : undefined,\n      onContextMenu: e => contextMenuItems.length > 0 ? handleContextMenu(e, row) : undefined,\n      sx: {\n        backgroundColor: `${bgColor} !important`,\n        cursor: selectionEnabled ? 'pointer' : 'default',\n        transition: 'all 0.2s ease',\n        border: isSelected ? '3px solid #1976d2' : '2px solid transparent',\n        '&:hover': {\n          backgroundColor: selectionEnabled ? isSelected ? 'rgba(25, 118, 210, 0.5) !important' : 'rgba(25, 118, 210, 0.2) !important' : 'rgba(0, 0, 0, 0.04) !important',\n          transform: selectionEnabled ? 'scale(1.01)' : 'none',\n          boxShadow: selectionEnabled ? '0 2px 8px rgba(0,0,0,0.1)' : 'none'\n        }\n      },\n      children: columns.map(column => /*#__PURE__*/_jsxDEV(TableCell, {\n        align: column.align || 'left',\n        sx: column.cellStyle,\n        children: column.renderCell ? column.renderCell(row) : row[column.field]\n      }, column.field, false, {\n        fileName: _jsxFileName,\n        lineNumber: 383,\n        columnNumber: 11\n      }, this))\n    }, index, false, {\n      fileName: _jsxFileName,\n      lineNumber: 362,\n      columnNumber: 7\n    }, this);\n  };\n  return /*#__PURE__*/_jsxDEV(Box, {\n    children: [/*#__PURE__*/_jsxDEV(SmartCaviFilter, {\n      cavi: cavi,\n      onFilteredDataChange: handleSmartFilterChange,\n      loading: loading\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 400,\n      columnNumber: 7\n    }, this), selectionEnabled && /*#__PURE__*/_jsxDEV(Box, {\n      sx: {\n        mb: 2,\n        p: 2,\n        backgroundColor: selectedCavi.length > 0 ? 'rgba(25, 118, 210, 0.15)' : 'rgba(25, 118, 210, 0.08)',\n        borderRadius: 2,\n        border: selectedCavi.length > 0 ? '2px solid rgba(25, 118, 210, 0.5)' : '2px solid rgba(25, 118, 210, 0.25)',\n        transition: 'all 0.3s ease',\n        boxShadow: selectedCavi.length > 0 ? '0 4px 12px rgba(25, 118, 210, 0.25)' : '0 2px 6px rgba(25, 118, 210, 0.1)'\n      },\n      children: /*#__PURE__*/_jsxDEV(Box, {\n        sx: {\n          display: 'flex',\n          justifyContent: 'space-between',\n          alignItems: 'center'\n        },\n        children: [/*#__PURE__*/_jsxDEV(Box, {\n          sx: {\n            display: 'flex',\n            alignItems: 'center',\n            gap: 1\n          },\n          children: [selectedCavi.length > 0 && /*#__PURE__*/_jsxDEV(CheckBoxIcon, {\n            color: \"primary\",\n            fontSize: \"small\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 420,\n            columnNumber: 17\n          }, this), /*#__PURE__*/_jsxDEV(Typography, {\n            variant: \"body1\",\n            sx: {\n              fontWeight: 600,\n              color: selectedCavi.length > 0 ? 'primary.main' : 'text.primary'\n            },\n            children: selectedCavi.length > 0 ? `${selectedCavi.length} cavi selezionati` : 'Modalità selezione attiva - Click sui cavi per selezionarli'\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 422,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 418,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(Box, {\n          sx: {\n            display: 'flex',\n            gap: 1\n          },\n          children: [/*#__PURE__*/_jsxDEV(Button, {\n            variant: \"outlined\",\n            size: \"small\",\n            onClick: handleSelectAll,\n            disabled: filteredCavi.length === 0,\n            children: filteredCavi.length > 0 && filteredCavi.every(cavo => selectedCavi.includes(cavo.id_cavo)) ? 'Deseleziona tutti' : 'Seleziona tutti'\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 431,\n            columnNumber: 15\n          }, this), selectedCavi.length > 0 && /*#__PURE__*/_jsxDEV(Button, {\n            variant: \"outlined\",\n            size: \"small\",\n            startIcon: /*#__PURE__*/_jsxDEV(ClearIcon, {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 447,\n              columnNumber: 30\n            }, this),\n            onClick: handleClearSelection,\n            color: \"error\",\n            children: \"Cancella selezione\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 444,\n            columnNumber: 17\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 430,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 417,\n        columnNumber: 11\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 408,\n      columnNumber: 9\n    }, this), /*#__PURE__*/_jsxDEV(FilterableTable, {\n      data: smartFilteredCavi,\n      columns: columns,\n      onFilteredDataChange: handleFilteredDataChange,\n      loading: loading,\n      emptyMessage: \"Nessun cavo disponibile\",\n      renderRow: renderRow\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 460,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(ContextMenu, {\n      open: contextMenu.open,\n      anchorPosition: contextMenu.anchorPosition,\n      onClose: closeContextMenu,\n      menuItems: typeof contextMenuItems === 'function' ? contextMenuItems(contextMenu.contextData) : contextMenuItems,\n      contextData: contextMenu.contextData\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 470,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 398,\n    columnNumber: 5\n  }, this);\n};\n_s(CaviFilterableTable, \"8HPaO1mqoR5Gc08zbVuoa6vh04g=\", false, function () {\n  return [useContextMenu];\n});\n_c = CaviFilterableTable;\nexport default CaviFilterableTable;\nvar _c;\n$RefreshReg$(_c, \"CaviFilterableTable\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "Box", "Typography", "Chip", "TableRow", "TableCell", "Checkbox", "IconButton", "<PERSON><PERSON>", "CheckBox", "CheckBoxIcon", "CheckBoxOutlineBlank", "CheckBoxOutlineBlankIcon", "Clear", "ClearIcon", "<PERSON>en", "RulerIcon", "Settings", "SettingsIcon", "PlayArrow", "StartIcon", "FilterableTable", "SmartCaviFilter", "ContextMenu", "useContextMenu", "formatDate", "jsxDEV", "_jsxDEV", "CaviFilterableTable", "cavi", "loading", "onFilteredDataChange", "revisioneCorrente", "selectionEnabled", "<PERSON><PERSON><PERSON>", "onSelectionChange", "contextMenuItems", "onContextMenuAction", "onStatusAction", "_s", "filteredCavi", "setFilteredCavi", "smartFilteredCavi", "setSmartFilteredCavi", "contextMenu", "handleContextMenu", "closeContextMenu", "handleFilteredDataChange", "data", "handleSmartFilterChange", "console", "log", "originalCount", "length", "filteredCount", "filteredIds", "map", "c", "id_cavo", "handleCavoToggle", "cavoId", "isSelected", "includes", "newSelection", "filter", "id", "handleSelectAll", "visibleCaviIds", "cavo", "allSelected", "every", "Set", "handleClearSelection", "columns", "field", "headerName", "disableFilter", "disableSort", "width", "align", "headerStyle", "padding", "cellStyle", "textAlign", "renderHeader", "someSelected", "some", "checked", "indeterminate", "onChange", "size", "title", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "renderCell", "row", "onClick", "e", "stopPropagation", "dataType", "fontWeight", "metri_te<PERSON>ci", "toFixed", "metratura_reale", "_row$stato_installazi", "_row$stato_installazi2", "color", "icon", "actionLabel", "actionType", "statoNormalizzato", "stato_installazione", "toUpperCase", "fontSize", "stato", "statoType", "stato<PERSON>ength", "onStatusActionType", "hasAction", "willBeClickable", "fullCavo", "isClickable", "label", "variant", "undefined", "sx", "cursor", "transition", "transform", "boxShadow", "backgroundColor", "border", "id_bobina", "match", "timestamp", "colle<PERSON>nti", "renderRow", "index", "_row$stato_installazi3", "bgColor", "selected", "hover", "onContextMenu", "children", "column", "mb", "p", "borderRadius", "display", "justifyContent", "alignItems", "gap", "disabled", "startIcon", "emptyMessage", "open", "anchorPosition", "onClose", "menuItems", "contextData", "_c", "$RefreshReg$"], "sources": ["C:/CMS/webapp/frontend/src/components/cavi/CaviFilterableTable.js"], "sourcesContent": ["import React, { useState, useEffect } from 'react';\nimport { Box, Typography, Chip, TableRow, TableCell, Checkbox, IconButton, Button } from '@mui/material';\nimport {\n  CheckBox as CheckBoxIcon,\n  CheckBoxOutlineBlank as CheckBoxOutlineBlankIcon,\n  Clear as ClearIcon,\n  Straighten as RulerIcon,\n  Settings as SettingsIcon,\n  PlayArrow as StartIcon\n} from '@mui/icons-material';\nimport FilterableTable from '../common/FilterableTable';\nimport SmartCaviFilter from './SmartCaviFilter';\nimport ContextMenu from '../common/ContextMenu';\nimport useContextMenu from '../../hooks/useContextMenu';\nimport { formatDate } from '../../utils/dateUtils';\n\n/**\n * Componente per visualizzare la lista dei cavi con filtri in stile Excel\n *\n * @param {Object} props - Proprietà del componente\n * @param {Array} props.cavi - Lista dei cavi da visualizzare\n * @param {boolean} props.loading - Indica se i dati sono in caricamento\n * @param {Function} props.onFilteredDataChange - Funzione chiamata quando i dati filtrati cambiano\n * @param {string} props.revisioneCorrente - Revisione corrente da mostrare nelle statistiche\n * @param {boolean} props.selectionEnabled - Abilita la selezione dei cavi\n * @param {Array} props.selectedCavi - Array degli ID dei cavi selezionati\n * @param {Function} props.onSelectionChange - Funzione chiamata quando cambia la selezione\n * @param {Array} props.contextMenuItems - Array di elementi per il menu contestuale\n * @param {Function} props.onContextMenuAction - Funzione chiamata quando si clicca su un elemento del menu contestuale\n * @param {Function} props.onStatusAction - Funzione chiamata quando si clicca sul pulsante stato\n */\nconst CaviFilterableTable = ({\n  cavi = [],\n  loading = false,\n  onFilteredDataChange = null,\n  revisioneCorrente = null,\n  selectionEnabled = false,\n  selectedCavi = [],\n  onSelectionChange = null,\n  contextMenuItems = [],\n  onContextMenuAction = null,\n  onStatusAction = null\n}) => {\n  const [filteredCavi, setFilteredCavi] = useState(cavi);\n  const [smartFilteredCavi, setSmartFilteredCavi] = useState(cavi);\n\n  // Hook per il menu contestuale\n  const { contextMenu, handleContextMenu, closeContextMenu } = useContextMenu();\n\n  // Aggiorna i dati filtrati quando cambiano i cavi\n  useEffect(() => {\n    setFilteredCavi(cavi);\n    setSmartFilteredCavi(cavi);\n  }, [cavi]);\n\n  // Notifica il componente padre quando cambiano i dati filtrati\n  const handleFilteredDataChange = (data) => {\n    setFilteredCavi(data);\n    if (onFilteredDataChange) {\n      onFilteredDataChange(data);\n    }\n  };\n\n  // Gestisce il cambio dei dati dal filtro intelligente\n  const handleSmartFilterChange = (data) => {\n    console.log('CaviFilterableTable - Smart filter change:', {\n      originalCount: cavi.length,\n      filteredCount: data.length,\n      filteredIds: data.map(c => c.id_cavo)\n    });\n    setSmartFilteredCavi(data);\n    // Il filtro intelligente ha la priorità sui filtri Excel-like\n    setFilteredCavi(data);\n    if (onFilteredDataChange) {\n      onFilteredDataChange(data);\n    }\n  };\n\n  // Gestisce la selezione di un singolo cavo\n  const handleCavoToggle = (cavoId) => {\n    if (!selectionEnabled || !onSelectionChange) return;\n\n    const isSelected = selectedCavi.includes(cavoId);\n    let newSelection;\n\n    if (isSelected) {\n      // Rimuovi dalla selezione\n      newSelection = selectedCavi.filter(id => id !== cavoId);\n      console.log(`Cavo ${cavoId} deselezionato`);\n    } else {\n      // Aggiungi alla selezione\n      newSelection = [...selectedCavi, cavoId];\n      console.log(`Cavo ${cavoId} selezionato`);\n    }\n\n    onSelectionChange(newSelection);\n\n    // Feedback visivo rapido (opzionale - può essere rimosso se troppo invasivo)\n    // Potresti aggiungere qui un piccolo toast o animazione\n  };\n\n  // Seleziona tutti i cavi visibili (filtrati)\n  const handleSelectAll = () => {\n    if (!selectionEnabled || !onSelectionChange) return;\n\n    const visibleCaviIds = filteredCavi.map(cavo => cavo.id_cavo);\n    const allSelected = visibleCaviIds.every(id => selectedCavi.includes(id));\n\n    if (allSelected) {\n      // Deseleziona tutti i cavi visibili\n      const newSelection = selectedCavi.filter(id => !visibleCaviIds.includes(id));\n      onSelectionChange(newSelection);\n    } else {\n      // Seleziona tutti i cavi visibili\n      const newSelection = [...new Set([...selectedCavi, ...visibleCaviIds])];\n      onSelectionChange(newSelection);\n    }\n  };\n\n  // Deseleziona tutti i cavi\n  const handleClearSelection = () => {\n    if (!selectionEnabled || !onSelectionChange) return;\n    onSelectionChange([]);\n  };\n\n\n\n  // Definizione delle colonne\n  const columns = [\n    // Colonna di selezione (solo se abilitata)\n    ...(selectionEnabled ? [{\n      field: 'selection',\n      headerName: '',\n      disableFilter: true,\n      disableSort: true,\n      width: 50,\n      align: 'center',\n      headerStyle: { width: '50px', padding: '4px' },\n      cellStyle: { width: '50px', padding: '4px', textAlign: 'center' },\n      renderHeader: () => {\n        const visibleCaviIds = filteredCavi.map(cavo => cavo.id_cavo);\n        const allSelected = visibleCaviIds.length > 0 && visibleCaviIds.every(id => selectedCavi.includes(id));\n        const someSelected = visibleCaviIds.some(id => selectedCavi.includes(id));\n\n        return (\n          <Checkbox\n            checked={allSelected}\n            indeterminate={someSelected && !allSelected}\n            onChange={handleSelectAll}\n            size=\"small\"\n            title={allSelected ? \"Deseleziona tutti\" : \"Seleziona tutti\"}\n          />\n        );\n      },\n      renderCell: (row) => (\n        <Checkbox\n          checked={selectedCavi.includes(row.id_cavo)}\n          onChange={() => handleCavoToggle(row.id_cavo)}\n          size=\"small\"\n          onClick={(e) => e.stopPropagation()}\n        />\n      )\n    }] : []),\n    {\n      field: 'id_cavo',\n      headerName: 'ID Cavo',\n      dataType: 'text',\n      headerStyle: { fontWeight: 'bold' }\n    },\n    // Colonna Revisione rimossa e spostata nella tabella delle statistiche\n    {\n      field: 'sistema',\n      headerName: 'Sistema',\n      dataType: 'text'\n    },\n    {\n      field: 'utility',\n      headerName: 'Utility',\n      dataType: 'text'\n    },\n    {\n      field: 'tipologia',\n      headerName: 'Tipologia',\n      dataType: 'text'\n    },\n    // n_conduttori field is now a spare field (kept in DB but hidden in UI)\n    {\n      field: 'sezione',\n      headerName: 'Formazione',\n      dataType: 'text',\n      align: 'right',\n      cellStyle: { textAlign: 'right' }\n    },\n    {\n      field: 'metri_teorici',\n      headerName: 'Metri Teorici',\n      dataType: 'number',\n      align: 'right',\n      cellStyle: { textAlign: 'right' },\n      renderCell: (row) => row.metri_teorici ? row.metri_teorici.toFixed(1) : '0'\n    },\n    {\n      field: 'metratura_reale',\n      headerName: 'Metri Reali',\n      dataType: 'number',\n      align: 'right',\n      cellStyle: { textAlign: 'right' },\n      renderCell: (row) => row.metratura_reale ? row.metratura_reale.toFixed(1) : '0'\n    },\n    {\n      field: 'stato_installazione',\n      headerName: 'Stato',\n      dataType: 'text',\n      renderCell: (row) => {\n        // Determina colore, icona e azione in base allo stato\n        let color = 'default';\n        let icon = null;\n        let actionLabel = '';\n        let actionType = '';\n\n        // Normalizza lo stato per gestire diverse varianti\n        const statoNormalizzato = row.stato_installazione?.toUpperCase();\n\n        if (statoNormalizzato === 'INSTALLATO' || row.stato_installazione === 'Installato') {\n          color = 'success';\n          icon = <SettingsIcon fontSize=\"small\" />;\n          actionLabel = 'Modifica Bobina';\n          actionType = 'modify_reel';\n        } else if (statoNormalizzato === 'IN_CORSO' || row.stato_installazione === 'In corso') {\n          color = 'warning';\n          icon = <RulerIcon fontSize=\"small\" />;\n          actionLabel = 'Inserisci Metri Posati';\n          actionType = 'insert_meters';\n        } else if (statoNormalizzato === 'DA_INSTALLARE' || row.stato_installazione === 'Da installare' || !row.stato_installazione) {\n          color = 'error';\n          icon = <StartIcon fontSize=\"small\" />;\n          actionLabel = 'Inserisci Metri Posati';\n          actionType = 'insert_meters';\n        }\n\n        // Debug: verifica se onStatusAction è definito e se c'è un'azione per questo stato\n        console.log('🔍 Rendering pulsante stato per cavo:', row.id_cavo, {\n          stato: row.stato_installazione,\n          statoType: typeof row.stato_installazione,\n          statoLength: row.stato_installazione?.length,\n          onStatusAction: !!onStatusAction,\n          onStatusActionType: typeof onStatusAction,\n          actionType,\n          actionLabel,\n          hasAction: !!actionType,\n          willBeClickable: !!(onStatusAction && actionType && actionLabel),\n          // Debug completo del cavo\n          fullCavo: row\n        });\n\n        // Determina se il pulsante deve essere cliccabile\n        const isClickable = onStatusAction && actionType && actionLabel;\n\n        return (\n          <Chip\n            label={row.stato_installazione || 'N/D'}\n            size=\"small\"\n            color={color}\n            variant=\"outlined\"\n            icon={icon}\n            onClick={isClickable ? (e) => {\n              e.stopPropagation();\n              console.log('🔥 CLICK su pulsante stato!', {\n                cavoId: row.id_cavo,\n                stato: row.stato_installazione,\n                actionType,\n                actionLabel\n              });\n              onStatusAction(row, actionType, actionLabel);\n            } : undefined}\n            sx={{\n              cursor: isClickable ? 'pointer' : 'default',\n              transition: 'all 0.2s ease',\n              '&:hover': isClickable ? {\n                transform: 'scale(1.05)',\n                boxShadow: '0 2px 8px rgba(0,0,0,0.15)',\n                backgroundColor: `${color}.light`\n              } : {},\n              // Aggiungi un bordo più marcato per i pulsanti cliccabili\n              border: isClickable ? '2px solid currentColor' : '1px solid currentColor'\n            }}\n            title={isClickable ? actionLabel : 'Nessuna azione disponibile'}\n          />\n        );\n      }\n    },\n    {\n      field: 'id_bobina',\n      headerName: 'Bobina',\n      dataType: 'text',\n      renderCell: (row) => {\n        // Gestione differenziata per null e BOBINA_VUOTA\n        if (row.id_bobina === null) {\n          // Per cavi non posati (id_bobina è null)\n          return '-';\n        } else if (row.id_bobina === 'BOBINA_VUOTA') {\n          // Per cavi posati senza bobina specifica\n          return 'BOBINA VUOTA';\n        } else if (!row.id_bobina) {\n          // Per altri casi in cui id_bobina è falsy (undefined, stringa vuota)\n          return '-';\n        }\n\n        // Estrai solo il numero della bobina (parte dopo '_B')\n        const match = row.id_bobina.match(/_B(.+)$/);\n        return match ? match[1] : row.id_bobina;\n      }\n    },\n    {\n      field: 'timestamp',\n      headerName: 'Data Modifica',\n      dataType: 'date',\n      renderCell: (row) => formatDate(row.timestamp)\n    },\n    {\n      field: 'collegamenti',\n      headerName: 'Collegamenti',\n      dataType: 'number',\n      align: 'center',\n      cellStyle: { textAlign: 'center' },\n      renderCell: (row) => {\n        let color = 'default';\n        if (row.collegamenti === 2) color = 'success';\n        else if (row.collegamenti === 1) color = 'warning';\n        else color = 'error';\n\n        return (\n          <Chip\n            label={row.collegamenti}\n            size=\"small\"\n            color={color}\n            variant=\"outlined\"\n          />\n        );\n      }\n    }\n  ];\n\n  // Renderizza una riga personalizzata\n  const renderRow = (row, index) => {\n    // Determina il colore di sfondo in base allo stato\n    let bgColor = 'inherit';\n    const statoNormalizzato = row.stato_installazione?.toUpperCase();\n    if (statoNormalizzato === 'INSTALLATO' || row.stato_installazione === 'Installato') {\n      bgColor = 'rgba(76, 175, 80, 0.1)';\n    } else if (statoNormalizzato === 'IN_CORSO' || row.stato_installazione === 'In corso') {\n      bgColor = 'rgba(255, 152, 0, 0.1)';\n    }\n\n    // Se la selezione è abilitata, evidenzia le righe selezionate\n    const isSelected = selectionEnabled && selectedCavi.includes(row.id_cavo);\n    if (isSelected) {\n      bgColor = 'rgba(25, 118, 210, 0.4)'; // Blu molto più marcato per le righe selezionate\n    }\n\n    return (\n      <TableRow\n        key={index}\n        selected={isSelected}\n        hover\n        onClick={selectionEnabled ? () => handleCavoToggle(row.id_cavo) : undefined}\n        onContextMenu={(e) => contextMenuItems.length > 0 ? handleContextMenu(e, row) : undefined}\n        sx={{\n          backgroundColor: `${bgColor} !important`,\n          cursor: selectionEnabled ? 'pointer' : 'default',\n          transition: 'all 0.2s ease',\n          border: isSelected ? '3px solid #1976d2' : '2px solid transparent',\n          '&:hover': {\n            backgroundColor: selectionEnabled\n              ? (isSelected ? 'rgba(25, 118, 210, 0.5) !important' : 'rgba(25, 118, 210, 0.2) !important')\n              : 'rgba(0, 0, 0, 0.04) !important',\n            transform: selectionEnabled ? 'scale(1.01)' : 'none',\n            boxShadow: selectionEnabled ? '0 2px 8px rgba(0,0,0,0.1)' : 'none'\n          }\n        }}\n      >\n        {columns.map((column) => (\n          <TableCell\n            key={column.field}\n            align={column.align || 'left'}\n            sx={column.cellStyle}\n          >\n            {column.renderCell ? column.renderCell(row) : row[column.field]}\n          </TableCell>\n        ))}\n      </TableRow>\n    );\n  };\n\n\n\n  return (\n    <Box>\n      {/* Filtro intelligente */}\n      <SmartCaviFilter\n        cavi={cavi}\n        onFilteredDataChange={handleSmartFilterChange}\n        loading={loading}\n      />\n\n      {/* Pannello di controllo selezione */}\n      {selectionEnabled && (\n        <Box sx={{\n          mb: 2,\n          p: 2,\n          backgroundColor: selectedCavi.length > 0 ? 'rgba(25, 118, 210, 0.15)' : 'rgba(25, 118, 210, 0.08)',\n          borderRadius: 2,\n          border: selectedCavi.length > 0 ? '2px solid rgba(25, 118, 210, 0.5)' : '2px solid rgba(25, 118, 210, 0.25)',\n          transition: 'all 0.3s ease',\n          boxShadow: selectedCavi.length > 0 ? '0 4px 12px rgba(25, 118, 210, 0.25)' : '0 2px 6px rgba(25, 118, 210, 0.1)'\n        }}>\n          <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>\n            <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>\n              {selectedCavi.length > 0 && (\n                <CheckBoxIcon color=\"primary\" fontSize=\"small\" />\n              )}\n              <Typography variant=\"body1\" sx={{ fontWeight: 600, color: selectedCavi.length > 0 ? 'primary.main' : 'text.primary' }}>\n                {selectedCavi.length > 0\n                  ? `${selectedCavi.length} cavi selezionati`\n                  : 'Modalità selezione attiva - Click sui cavi per selezionarli'\n                }\n              </Typography>\n            </Box>\n\n            <Box sx={{ display: 'flex', gap: 1 }}>\n              <Button\n                variant=\"outlined\"\n                size=\"small\"\n                onClick={handleSelectAll}\n                disabled={filteredCavi.length === 0}\n              >\n                {filteredCavi.length > 0 && filteredCavi.every(cavo => selectedCavi.includes(cavo.id_cavo))\n                  ? 'Deseleziona tutti'\n                  : 'Seleziona tutti'\n                }\n              </Button>\n\n              {selectedCavi.length > 0 && (\n                <Button\n                  variant=\"outlined\"\n                  size=\"small\"\n                  startIcon={<ClearIcon />}\n                  onClick={handleClearSelection}\n                  color=\"error\"\n                >\n                  Cancella selezione\n                </Button>\n              )}\n            </Box>\n          </Box>\n        </Box>\n      )}\n\n      {/* Tabella con filtri Excel-like sui dati già filtrati dal filtro intelligente */}\n      <FilterableTable\n        data={smartFilteredCavi}\n        columns={columns}\n        onFilteredDataChange={handleFilteredDataChange}\n        loading={loading}\n        emptyMessage=\"Nessun cavo disponibile\"\n        renderRow={renderRow}\n      />\n\n      {/* Menu contestuale */}\n      <ContextMenu\n        open={contextMenu.open}\n        anchorPosition={contextMenu.anchorPosition}\n        onClose={closeContextMenu}\n        menuItems={typeof contextMenuItems === 'function' ? contextMenuItems(contextMenu.contextData) : contextMenuItems}\n        contextData={contextMenu.contextData}\n      />\n    </Box>\n  );\n};\n\nexport default CaviFilterableTable;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAClD,SAASC,GAAG,EAAEC,UAAU,EAAEC,IAAI,EAAEC,QAAQ,EAAEC,SAAS,EAAEC,QAAQ,EAAEC,UAAU,EAAEC,MAAM,QAAQ,eAAe;AACxG,SACEC,QAAQ,IAAIC,YAAY,EACxBC,oBAAoB,IAAIC,wBAAwB,EAChDC,KAAK,IAAIC,SAAS,EAClBC,UAAU,IAAIC,SAAS,EACvBC,QAAQ,IAAIC,YAAY,EACxBC,SAAS,IAAIC,SAAS,QACjB,qBAAqB;AAC5B,OAAOC,eAAe,MAAM,2BAA2B;AACvD,OAAOC,eAAe,MAAM,mBAAmB;AAC/C,OAAOC,WAAW,MAAM,uBAAuB;AAC/C,OAAOC,cAAc,MAAM,4BAA4B;AACvD,SAASC,UAAU,QAAQ,uBAAuB;;AAElD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAdA,SAAAC,MAAA,IAAAC,OAAA;AAeA,MAAMC,mBAAmB,GAAGA,CAAC;EAC3BC,IAAI,GAAG,EAAE;EACTC,OAAO,GAAG,KAAK;EACfC,oBAAoB,GAAG,IAAI;EAC3BC,iBAAiB,GAAG,IAAI;EACxBC,gBAAgB,GAAG,KAAK;EACxBC,YAAY,GAAG,EAAE;EACjBC,iBAAiB,GAAG,IAAI;EACxBC,gBAAgB,GAAG,EAAE;EACrBC,mBAAmB,GAAG,IAAI;EAC1BC,cAAc,GAAG;AACnB,CAAC,KAAK;EAAAC,EAAA;EACJ,MAAM,CAACC,YAAY,EAAEC,eAAe,CAAC,GAAG1C,QAAQ,CAAC8B,IAAI,CAAC;EACtD,MAAM,CAACa,iBAAiB,EAAEC,oBAAoB,CAAC,GAAG5C,QAAQ,CAAC8B,IAAI,CAAC;;EAEhE;EACA,MAAM;IAAEe,WAAW;IAAEC,iBAAiB;IAAEC;EAAiB,CAAC,GAAGtB,cAAc,CAAC,CAAC;;EAE7E;EACAxB,SAAS,CAAC,MAAM;IACdyC,eAAe,CAACZ,IAAI,CAAC;IACrBc,oBAAoB,CAACd,IAAI,CAAC;EAC5B,CAAC,EAAE,CAACA,IAAI,CAAC,CAAC;;EAEV;EACA,MAAMkB,wBAAwB,GAAIC,IAAI,IAAK;IACzCP,eAAe,CAACO,IAAI,CAAC;IACrB,IAAIjB,oBAAoB,EAAE;MACxBA,oBAAoB,CAACiB,IAAI,CAAC;IAC5B;EACF,CAAC;;EAED;EACA,MAAMC,uBAAuB,GAAID,IAAI,IAAK;IACxCE,OAAO,CAACC,GAAG,CAAC,4CAA4C,EAAE;MACxDC,aAAa,EAAEvB,IAAI,CAACwB,MAAM;MAC1BC,aAAa,EAAEN,IAAI,CAACK,MAAM;MAC1BE,WAAW,EAAEP,IAAI,CAACQ,GAAG,CAACC,CAAC,IAAIA,CAAC,CAACC,OAAO;IACtC,CAAC,CAAC;IACFf,oBAAoB,CAACK,IAAI,CAAC;IAC1B;IACAP,eAAe,CAACO,IAAI,CAAC;IACrB,IAAIjB,oBAAoB,EAAE;MACxBA,oBAAoB,CAACiB,IAAI,CAAC;IAC5B;EACF,CAAC;;EAED;EACA,MAAMW,gBAAgB,GAAIC,MAAM,IAAK;IACnC,IAAI,CAAC3B,gBAAgB,IAAI,CAACE,iBAAiB,EAAE;IAE7C,MAAM0B,UAAU,GAAG3B,YAAY,CAAC4B,QAAQ,CAACF,MAAM,CAAC;IAChD,IAAIG,YAAY;IAEhB,IAAIF,UAAU,EAAE;MACd;MACAE,YAAY,GAAG7B,YAAY,CAAC8B,MAAM,CAACC,EAAE,IAAIA,EAAE,KAAKL,MAAM,CAAC;MACvDV,OAAO,CAACC,GAAG,CAAC,QAAQS,MAAM,gBAAgB,CAAC;IAC7C,CAAC,MAAM;MACL;MACAG,YAAY,GAAG,CAAC,GAAG7B,YAAY,EAAE0B,MAAM,CAAC;MACxCV,OAAO,CAACC,GAAG,CAAC,QAAQS,MAAM,cAAc,CAAC;IAC3C;IAEAzB,iBAAiB,CAAC4B,YAAY,CAAC;;IAE/B;IACA;EACF,CAAC;;EAED;EACA,MAAMG,eAAe,GAAGA,CAAA,KAAM;IAC5B,IAAI,CAACjC,gBAAgB,IAAI,CAACE,iBAAiB,EAAE;IAE7C,MAAMgC,cAAc,GAAG3B,YAAY,CAACgB,GAAG,CAACY,IAAI,IAAIA,IAAI,CAACV,OAAO,CAAC;IAC7D,MAAMW,WAAW,GAAGF,cAAc,CAACG,KAAK,CAACL,EAAE,IAAI/B,YAAY,CAAC4B,QAAQ,CAACG,EAAE,CAAC,CAAC;IAEzE,IAAII,WAAW,EAAE;MACf;MACA,MAAMN,YAAY,GAAG7B,YAAY,CAAC8B,MAAM,CAACC,EAAE,IAAI,CAACE,cAAc,CAACL,QAAQ,CAACG,EAAE,CAAC,CAAC;MAC5E9B,iBAAiB,CAAC4B,YAAY,CAAC;IACjC,CAAC,MAAM;MACL;MACA,MAAMA,YAAY,GAAG,CAAC,GAAG,IAAIQ,GAAG,CAAC,CAAC,GAAGrC,YAAY,EAAE,GAAGiC,cAAc,CAAC,CAAC,CAAC;MACvEhC,iBAAiB,CAAC4B,YAAY,CAAC;IACjC;EACF,CAAC;;EAED;EACA,MAAMS,oBAAoB,GAAGA,CAAA,KAAM;IACjC,IAAI,CAACvC,gBAAgB,IAAI,CAACE,iBAAiB,EAAE;IAC7CA,iBAAiB,CAAC,EAAE,CAAC;EACvB,CAAC;;EAID;EACA,MAAMsC,OAAO,GAAG;EACd;EACA,IAAIxC,gBAAgB,GAAG,CAAC;IACtByC,KAAK,EAAE,WAAW;IAClBC,UAAU,EAAE,EAAE;IACdC,aAAa,EAAE,IAAI;IACnBC,WAAW,EAAE,IAAI;IACjBC,KAAK,EAAE,EAAE;IACTC,KAAK,EAAE,QAAQ;IACfC,WAAW,EAAE;MAAEF,KAAK,EAAE,MAAM;MAAEG,OAAO,EAAE;IAAM,CAAC;IAC9CC,SAAS,EAAE;MAAEJ,KAAK,EAAE,MAAM;MAAEG,OAAO,EAAE,KAAK;MAAEE,SAAS,EAAE;IAAS,CAAC;IACjEC,YAAY,EAAEA,CAAA,KAAM;MAClB,MAAMjB,cAAc,GAAG3B,YAAY,CAACgB,GAAG,CAACY,IAAI,IAAIA,IAAI,CAACV,OAAO,CAAC;MAC7D,MAAMW,WAAW,GAAGF,cAAc,CAACd,MAAM,GAAG,CAAC,IAAIc,cAAc,CAACG,KAAK,CAACL,EAAE,IAAI/B,YAAY,CAAC4B,QAAQ,CAACG,EAAE,CAAC,CAAC;MACtG,MAAMoB,YAAY,GAAGlB,cAAc,CAACmB,IAAI,CAACrB,EAAE,IAAI/B,YAAY,CAAC4B,QAAQ,CAACG,EAAE,CAAC,CAAC;MAEzE,oBACEtC,OAAA,CAACrB,QAAQ;QACPiF,OAAO,EAAElB,WAAY;QACrBmB,aAAa,EAAEH,YAAY,IAAI,CAAChB,WAAY;QAC5CoB,QAAQ,EAAEvB,eAAgB;QAC1BwB,IAAI,EAAC,OAAO;QACZC,KAAK,EAAEtB,WAAW,GAAG,mBAAmB,GAAG;MAAkB;QAAAuB,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC9D,CAAC;IAEN,CAAC;IACDC,UAAU,EAAGC,GAAG,iBACdtE,OAAA,CAACrB,QAAQ;MACPiF,OAAO,EAAErD,YAAY,CAAC4B,QAAQ,CAACmC,GAAG,CAACvC,OAAO,CAAE;MAC5C+B,QAAQ,EAAEA,CAAA,KAAM9B,gBAAgB,CAACsC,GAAG,CAACvC,OAAO,CAAE;MAC9CgC,IAAI,EAAC,OAAO;MACZQ,OAAO,EAAGC,CAAC,IAAKA,CAAC,CAACC,eAAe,CAAC;IAAE;MAAAR,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACrC;EAEL,CAAC,CAAC,GAAG,EAAE,CAAC,EACR;IACErB,KAAK,EAAE,SAAS;IAChBC,UAAU,EAAE,SAAS;IACrB0B,QAAQ,EAAE,MAAM;IAChBrB,WAAW,EAAE;MAAEsB,UAAU,EAAE;IAAO;EACpC,CAAC;EACD;EACA;IACE5B,KAAK,EAAE,SAAS;IAChBC,UAAU,EAAE,SAAS;IACrB0B,QAAQ,EAAE;EACZ,CAAC,EACD;IACE3B,KAAK,EAAE,SAAS;IAChBC,UAAU,EAAE,SAAS;IACrB0B,QAAQ,EAAE;EACZ,CAAC,EACD;IACE3B,KAAK,EAAE,WAAW;IAClBC,UAAU,EAAE,WAAW;IACvB0B,QAAQ,EAAE;EACZ,CAAC;EACD;EACA;IACE3B,KAAK,EAAE,SAAS;IAChBC,UAAU,EAAE,YAAY;IACxB0B,QAAQ,EAAE,MAAM;IAChBtB,KAAK,EAAE,OAAO;IACdG,SAAS,EAAE;MAAEC,SAAS,EAAE;IAAQ;EAClC,CAAC,EACD;IACET,KAAK,EAAE,eAAe;IACtBC,UAAU,EAAE,eAAe;IAC3B0B,QAAQ,EAAE,QAAQ;IAClBtB,KAAK,EAAE,OAAO;IACdG,SAAS,EAAE;MAAEC,SAAS,EAAE;IAAQ,CAAC;IACjCa,UAAU,EAAGC,GAAG,IAAKA,GAAG,CAACM,aAAa,GAAGN,GAAG,CAACM,aAAa,CAACC,OAAO,CAAC,CAAC,CAAC,GAAG;EAC1E,CAAC,EACD;IACE9B,KAAK,EAAE,iBAAiB;IACxBC,UAAU,EAAE,aAAa;IACzB0B,QAAQ,EAAE,QAAQ;IAClBtB,KAAK,EAAE,OAAO;IACdG,SAAS,EAAE;MAAEC,SAAS,EAAE;IAAQ,CAAC;IACjCa,UAAU,EAAGC,GAAG,IAAKA,GAAG,CAACQ,eAAe,GAAGR,GAAG,CAACQ,eAAe,CAACD,OAAO,CAAC,CAAC,CAAC,GAAG;EAC9E,CAAC,EACD;IACE9B,KAAK,EAAE,qBAAqB;IAC5BC,UAAU,EAAE,OAAO;IACnB0B,QAAQ,EAAE,MAAM;IAChBL,UAAU,EAAGC,GAAG,IAAK;MAAA,IAAAS,qBAAA,EAAAC,sBAAA;MACnB;MACA,IAAIC,KAAK,GAAG,SAAS;MACrB,IAAIC,IAAI,GAAG,IAAI;MACf,IAAIC,WAAW,GAAG,EAAE;MACpB,IAAIC,UAAU,GAAG,EAAE;;MAEnB;MACA,MAAMC,iBAAiB,IAAAN,qBAAA,GAAGT,GAAG,CAACgB,mBAAmB,cAAAP,qBAAA,uBAAvBA,qBAAA,CAAyBQ,WAAW,CAAC,CAAC;MAEhE,IAAIF,iBAAiB,KAAK,YAAY,IAAIf,GAAG,CAACgB,mBAAmB,KAAK,YAAY,EAAE;QAClFL,KAAK,GAAG,SAAS;QACjBC,IAAI,gBAAGlF,OAAA,CAACT,YAAY;UAACiG,QAAQ,EAAC;QAAO;UAAAvB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC;QACxCe,WAAW,GAAG,iBAAiB;QAC/BC,UAAU,GAAG,aAAa;MAC5B,CAAC,MAAM,IAAIC,iBAAiB,KAAK,UAAU,IAAIf,GAAG,CAACgB,mBAAmB,KAAK,UAAU,EAAE;QACrFL,KAAK,GAAG,SAAS;QACjBC,IAAI,gBAAGlF,OAAA,CAACX,SAAS;UAACmG,QAAQ,EAAC;QAAO;UAAAvB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC;QACrCe,WAAW,GAAG,wBAAwB;QACtCC,UAAU,GAAG,eAAe;MAC9B,CAAC,MAAM,IAAIC,iBAAiB,KAAK,eAAe,IAAIf,GAAG,CAACgB,mBAAmB,KAAK,eAAe,IAAI,CAAChB,GAAG,CAACgB,mBAAmB,EAAE;QAC3HL,KAAK,GAAG,OAAO;QACfC,IAAI,gBAAGlF,OAAA,CAACP,SAAS;UAAC+F,QAAQ,EAAC;QAAO;UAAAvB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC;QACrCe,WAAW,GAAG,wBAAwB;QACtCC,UAAU,GAAG,eAAe;MAC9B;;MAEA;MACA7D,OAAO,CAACC,GAAG,CAAC,uCAAuC,EAAE8C,GAAG,CAACvC,OAAO,EAAE;QAChE0D,KAAK,EAAEnB,GAAG,CAACgB,mBAAmB;QAC9BI,SAAS,EAAE,OAAOpB,GAAG,CAACgB,mBAAmB;QACzCK,WAAW,GAAAX,sBAAA,GAAEV,GAAG,CAACgB,mBAAmB,cAAAN,sBAAA,uBAAvBA,sBAAA,CAAyBtD,MAAM;QAC5Cf,cAAc,EAAE,CAAC,CAACA,cAAc;QAChCiF,kBAAkB,EAAE,OAAOjF,cAAc;QACzCyE,UAAU;QACVD,WAAW;QACXU,SAAS,EAAE,CAAC,CAACT,UAAU;QACvBU,eAAe,EAAE,CAAC,EAAEnF,cAAc,IAAIyE,UAAU,IAAID,WAAW,CAAC;QAChE;QACAY,QAAQ,EAAEzB;MACZ,CAAC,CAAC;;MAEF;MACA,MAAM0B,WAAW,GAAGrF,cAAc,IAAIyE,UAAU,IAAID,WAAW;MAE/D,oBACEnF,OAAA,CAACxB,IAAI;QACHyH,KAAK,EAAE3B,GAAG,CAACgB,mBAAmB,IAAI,KAAM;QACxCvB,IAAI,EAAC,OAAO;QACZkB,KAAK,EAAEA,KAAM;QACbiB,OAAO,EAAC,UAAU;QAClBhB,IAAI,EAAEA,IAAK;QACXX,OAAO,EAAEyB,WAAW,GAAIxB,CAAC,IAAK;UAC5BA,CAAC,CAACC,eAAe,CAAC,CAAC;UACnBlD,OAAO,CAACC,GAAG,CAAC,6BAA6B,EAAE;YACzCS,MAAM,EAAEqC,GAAG,CAACvC,OAAO;YACnB0D,KAAK,EAAEnB,GAAG,CAACgB,mBAAmB;YAC9BF,UAAU;YACVD;UACF,CAAC,CAAC;UACFxE,cAAc,CAAC2D,GAAG,EAAEc,UAAU,EAAED,WAAW,CAAC;QAC9C,CAAC,GAAGgB,SAAU;QACdC,EAAE,EAAE;UACFC,MAAM,EAAEL,WAAW,GAAG,SAAS,GAAG,SAAS;UAC3CM,UAAU,EAAE,eAAe;UAC3B,SAAS,EAAEN,WAAW,GAAG;YACvBO,SAAS,EAAE,aAAa;YACxBC,SAAS,EAAE,4BAA4B;YACvCC,eAAe,EAAE,GAAGxB,KAAK;UAC3B,CAAC,GAAG,CAAC,CAAC;UACN;UACAyB,MAAM,EAAEV,WAAW,GAAG,wBAAwB,GAAG;QACnD,CAAE;QACFhC,KAAK,EAAEgC,WAAW,GAAGb,WAAW,GAAG;MAA6B;QAAAlB,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACjE,CAAC;IAEN;EACF,CAAC,EACD;IACErB,KAAK,EAAE,WAAW;IAClBC,UAAU,EAAE,QAAQ;IACpB0B,QAAQ,EAAE,MAAM;IAChBL,UAAU,EAAGC,GAAG,IAAK;MACnB;MACA,IAAIA,GAAG,CAACqC,SAAS,KAAK,IAAI,EAAE;QAC1B;QACA,OAAO,GAAG;MACZ,CAAC,MAAM,IAAIrC,GAAG,CAACqC,SAAS,KAAK,cAAc,EAAE;QAC3C;QACA,OAAO,cAAc;MACvB,CAAC,MAAM,IAAI,CAACrC,GAAG,CAACqC,SAAS,EAAE;QACzB;QACA,OAAO,GAAG;MACZ;;MAEA;MACA,MAAMC,KAAK,GAAGtC,GAAG,CAACqC,SAAS,CAACC,KAAK,CAAC,SAAS,CAAC;MAC5C,OAAOA,KAAK,GAAGA,KAAK,CAAC,CAAC,CAAC,GAAGtC,GAAG,CAACqC,SAAS;IACzC;EACF,CAAC,EACD;IACE5D,KAAK,EAAE,WAAW;IAClBC,UAAU,EAAE,eAAe;IAC3B0B,QAAQ,EAAE,MAAM;IAChBL,UAAU,EAAGC,GAAG,IAAKxE,UAAU,CAACwE,GAAG,CAACuC,SAAS;EAC/C,CAAC,EACD;IACE9D,KAAK,EAAE,cAAc;IACrBC,UAAU,EAAE,cAAc;IAC1B0B,QAAQ,EAAE,QAAQ;IAClBtB,KAAK,EAAE,QAAQ;IACfG,SAAS,EAAE;MAAEC,SAAS,EAAE;IAAS,CAAC;IAClCa,UAAU,EAAGC,GAAG,IAAK;MACnB,IAAIW,KAAK,GAAG,SAAS;MACrB,IAAIX,GAAG,CAACwC,YAAY,KAAK,CAAC,EAAE7B,KAAK,GAAG,SAAS,CAAC,KACzC,IAAIX,GAAG,CAACwC,YAAY,KAAK,CAAC,EAAE7B,KAAK,GAAG,SAAS,CAAC,KAC9CA,KAAK,GAAG,OAAO;MAEpB,oBACEjF,OAAA,CAACxB,IAAI;QACHyH,KAAK,EAAE3B,GAAG,CAACwC,YAAa;QACxB/C,IAAI,EAAC,OAAO;QACZkB,KAAK,EAAEA,KAAM;QACbiB,OAAO,EAAC;MAAU;QAAAjC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACnB,CAAC;IAEN;EACF,CAAC,CACF;;EAED;EACA,MAAM2C,SAAS,GAAGA,CAACzC,GAAG,EAAE0C,KAAK,KAAK;IAAA,IAAAC,sBAAA;IAChC;IACA,IAAIC,OAAO,GAAG,SAAS;IACvB,MAAM7B,iBAAiB,IAAA4B,sBAAA,GAAG3C,GAAG,CAACgB,mBAAmB,cAAA2B,sBAAA,uBAAvBA,sBAAA,CAAyB1B,WAAW,CAAC,CAAC;IAChE,IAAIF,iBAAiB,KAAK,YAAY,IAAIf,GAAG,CAACgB,mBAAmB,KAAK,YAAY,EAAE;MAClF4B,OAAO,GAAG,wBAAwB;IACpC,CAAC,MAAM,IAAI7B,iBAAiB,KAAK,UAAU,IAAIf,GAAG,CAACgB,mBAAmB,KAAK,UAAU,EAAE;MACrF4B,OAAO,GAAG,wBAAwB;IACpC;;IAEA;IACA,MAAMhF,UAAU,GAAG5B,gBAAgB,IAAIC,YAAY,CAAC4B,QAAQ,CAACmC,GAAG,CAACvC,OAAO,CAAC;IACzE,IAAIG,UAAU,EAAE;MACdgF,OAAO,GAAG,yBAAyB,CAAC,CAAC;IACvC;IAEA,oBACElH,OAAA,CAACvB,QAAQ;MAEP0I,QAAQ,EAAEjF,UAAW;MACrBkF,KAAK;MACL7C,OAAO,EAAEjE,gBAAgB,GAAG,MAAM0B,gBAAgB,CAACsC,GAAG,CAACvC,OAAO,CAAC,GAAGoE,SAAU;MAC5EkB,aAAa,EAAG7C,CAAC,IAAK/D,gBAAgB,CAACiB,MAAM,GAAG,CAAC,GAAGR,iBAAiB,CAACsD,CAAC,EAAEF,GAAG,CAAC,GAAG6B,SAAU;MAC1FC,EAAE,EAAE;QACFK,eAAe,EAAE,GAAGS,OAAO,aAAa;QACxCb,MAAM,EAAE/F,gBAAgB,GAAG,SAAS,GAAG,SAAS;QAChDgG,UAAU,EAAE,eAAe;QAC3BI,MAAM,EAAExE,UAAU,GAAG,mBAAmB,GAAG,uBAAuB;QAClE,SAAS,EAAE;UACTuE,eAAe,EAAEnG,gBAAgB,GAC5B4B,UAAU,GAAG,oCAAoC,GAAG,oCAAoC,GACzF,gCAAgC;UACpCqE,SAAS,EAAEjG,gBAAgB,GAAG,aAAa,GAAG,MAAM;UACpDkG,SAAS,EAAElG,gBAAgB,GAAG,2BAA2B,GAAG;QAC9D;MACF,CAAE;MAAAgH,QAAA,EAEDxE,OAAO,CAACjB,GAAG,CAAE0F,MAAM,iBAClBvH,OAAA,CAACtB,SAAS;QAER0E,KAAK,EAAEmE,MAAM,CAACnE,KAAK,IAAI,MAAO;QAC9BgD,EAAE,EAAEmB,MAAM,CAAChE,SAAU;QAAA+D,QAAA,EAEpBC,MAAM,CAAClD,UAAU,GAAGkD,MAAM,CAAClD,UAAU,CAACC,GAAG,CAAC,GAAGA,GAAG,CAACiD,MAAM,CAACxE,KAAK;MAAC,GAJ1DwE,MAAM,CAACxE,KAAK;QAAAkB,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAKR,CACZ;IAAC,GA3BG4C,KAAK;MAAA/C,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OA4BF,CAAC;EAEf,CAAC;EAID,oBACEpE,OAAA,CAAC1B,GAAG;IAAAgJ,QAAA,gBAEFtH,OAAA,CAACL,eAAe;MACdO,IAAI,EAAEA,IAAK;MACXE,oBAAoB,EAAEkB,uBAAwB;MAC9CnB,OAAO,EAAEA;IAAQ;MAAA8D,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAClB,CAAC,EAGD9D,gBAAgB,iBACfN,OAAA,CAAC1B,GAAG;MAAC8H,EAAE,EAAE;QACPoB,EAAE,EAAE,CAAC;QACLC,CAAC,EAAE,CAAC;QACJhB,eAAe,EAAElG,YAAY,CAACmB,MAAM,GAAG,CAAC,GAAG,0BAA0B,GAAG,0BAA0B;QAClGgG,YAAY,EAAE,CAAC;QACfhB,MAAM,EAAEnG,YAAY,CAACmB,MAAM,GAAG,CAAC,GAAG,mCAAmC,GAAG,oCAAoC;QAC5G4E,UAAU,EAAE,eAAe;QAC3BE,SAAS,EAAEjG,YAAY,CAACmB,MAAM,GAAG,CAAC,GAAG,qCAAqC,GAAG;MAC/E,CAAE;MAAA4F,QAAA,eACAtH,OAAA,CAAC1B,GAAG;QAAC8H,EAAE,EAAE;UAAEuB,OAAO,EAAE,MAAM;UAAEC,cAAc,EAAE,eAAe;UAAEC,UAAU,EAAE;QAAS,CAAE;QAAAP,QAAA,gBAClFtH,OAAA,CAAC1B,GAAG;UAAC8H,EAAE,EAAE;YAAEuB,OAAO,EAAE,MAAM;YAAEE,UAAU,EAAE,QAAQ;YAAEC,GAAG,EAAE;UAAE,CAAE;UAAAR,QAAA,GACxD/G,YAAY,CAACmB,MAAM,GAAG,CAAC,iBACtB1B,OAAA,CAACjB,YAAY;YAACkG,KAAK,EAAC,SAAS;YAACO,QAAQ,EAAC;UAAO;YAAAvB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CACjD,eACDpE,OAAA,CAACzB,UAAU;YAAC2H,OAAO,EAAC,OAAO;YAACE,EAAE,EAAE;cAAEzB,UAAU,EAAE,GAAG;cAAEM,KAAK,EAAE1E,YAAY,CAACmB,MAAM,GAAG,CAAC,GAAG,cAAc,GAAG;YAAe,CAAE;YAAA4F,QAAA,EACnH/G,YAAY,CAACmB,MAAM,GAAG,CAAC,GACpB,GAAGnB,YAAY,CAACmB,MAAM,mBAAmB,GACzC;UAA6D;YAAAuC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAEvD,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACV,CAAC,eAENpE,OAAA,CAAC1B,GAAG;UAAC8H,EAAE,EAAE;YAAEuB,OAAO,EAAE,MAAM;YAAEG,GAAG,EAAE;UAAE,CAAE;UAAAR,QAAA,gBACnCtH,OAAA,CAACnB,MAAM;YACLqH,OAAO,EAAC,UAAU;YAClBnC,IAAI,EAAC,OAAO;YACZQ,OAAO,EAAEhC,eAAgB;YACzBwF,QAAQ,EAAElH,YAAY,CAACa,MAAM,KAAK,CAAE;YAAA4F,QAAA,EAEnCzG,YAAY,CAACa,MAAM,GAAG,CAAC,IAAIb,YAAY,CAAC8B,KAAK,CAACF,IAAI,IAAIlC,YAAY,CAAC4B,QAAQ,CAACM,IAAI,CAACV,OAAO,CAAC,CAAC,GACvF,mBAAmB,GACnB;UAAiB;YAAAkC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAEf,CAAC,EAER7D,YAAY,CAACmB,MAAM,GAAG,CAAC,iBACtB1B,OAAA,CAACnB,MAAM;YACLqH,OAAO,EAAC,UAAU;YAClBnC,IAAI,EAAC,OAAO;YACZiE,SAAS,eAAEhI,OAAA,CAACb,SAAS;cAAA8E,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAE;YACzBG,OAAO,EAAE1B,oBAAqB;YAC9BoC,KAAK,EAAC,OAAO;YAAAqC,QAAA,EACd;UAED;YAAArD,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CACT;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACE,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CACN,eAGDpE,OAAA,CAACN,eAAe;MACd2B,IAAI,EAAEN,iBAAkB;MACxB+B,OAAO,EAAEA,OAAQ;MACjB1C,oBAAoB,EAAEgB,wBAAyB;MAC/CjB,OAAO,EAAEA,OAAQ;MACjB8H,YAAY,EAAC,yBAAyB;MACtClB,SAAS,EAAEA;IAAU;MAAA9C,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACtB,CAAC,eAGFpE,OAAA,CAACJ,WAAW;MACVsI,IAAI,EAAEjH,WAAW,CAACiH,IAAK;MACvBC,cAAc,EAAElH,WAAW,CAACkH,cAAe;MAC3CC,OAAO,EAAEjH,gBAAiB;MAC1BkH,SAAS,EAAE,OAAO5H,gBAAgB,KAAK,UAAU,GAAGA,gBAAgB,CAACQ,WAAW,CAACqH,WAAW,CAAC,GAAG7H,gBAAiB;MACjH6H,WAAW,EAAErH,WAAW,CAACqH;IAAY;MAAArE,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACtC,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACC,CAAC;AAEV,CAAC;AAACxD,EAAA,CA/bIX,mBAAmB;EAAA,QAgBsCJ,cAAc;AAAA;AAAA0I,EAAA,GAhBvEtI,mBAAmB;AAiczB,eAAeA,mBAAmB;AAAC,IAAAsI,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}