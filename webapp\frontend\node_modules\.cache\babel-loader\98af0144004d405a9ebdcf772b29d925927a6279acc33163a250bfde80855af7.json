{"ast": null, "code": "const formatRelativeLocale = {\n  lastWeek: \"'o' eeee 'pasado á' LT\",\n  yesterday: \"'onte á' p\",\n  today: \"'hoxe á' p\",\n  tomorrow: \"'mañá á' p\",\n  nextWeek: \"eeee 'á' p\",\n  other: \"P\"\n};\nconst formatRelativeLocalePlural = {\n  lastWeek: \"'o' eeee 'pasado ás' p\",\n  yesterday: \"'onte ás' p\",\n  today: \"'hoxe ás' p\",\n  tomorrow: \"'mañá ás' p\",\n  nextWeek: \"eeee 'ás' p\",\n  other: \"P\"\n};\nexport const formatRelative = (token, date, _baseDate, _options) => {\n  if (date.getHours() !== 1) {\n    return formatRelativeLocalePlural[token];\n  }\n  return formatRelativeLocale[token];\n};", "map": {"version": 3, "names": ["formatRelativeLocale", "lastWeek", "yesterday", "today", "tomorrow", "nextWeek", "other", "formatRelativeLocalePlural", "formatRelative", "token", "date", "_baseDate", "_options", "getHours"], "sources": ["C:/CMS/webapp/frontend/node_modules/date-fns/locale/gl/_lib/formatRelative.mjs"], "sourcesContent": ["const formatRelativeLocale = {\n  lastWeek: \"'o' eeee 'pasado á' LT\",\n  yesterday: \"'onte á' p\",\n  today: \"'hoxe á' p\",\n  tomorrow: \"'mañá á' p\",\n  nextWeek: \"eeee 'á' p\",\n  other: \"P\",\n};\n\nconst formatRelativeLocalePlural = {\n  lastWeek: \"'o' eeee 'pasado ás' p\",\n  yesterday: \"'onte ás' p\",\n  today: \"'hoxe ás' p\",\n  tomorrow: \"'mañá ás' p\",\n  nextWeek: \"eeee 'ás' p\",\n  other: \"P\",\n};\n\nexport const formatRelative = (token, date, _baseDate, _options) => {\n  if (date.getHours() !== 1) {\n    return formatRelativeLocalePlural[token];\n  }\n  return formatRelativeLocale[token];\n};\n"], "mappings": "AAAA,MAAMA,oBAAoB,GAAG;EAC3BC,QAAQ,EAAE,wBAAwB;EAClCC,SAAS,EAAE,YAAY;EACvBC,KAAK,EAAE,YAAY;EACnBC,QAAQ,EAAE,YAAY;EACtBC,QAAQ,EAAE,YAAY;EACtBC,KAAK,EAAE;AACT,CAAC;AAED,MAAMC,0BAA0B,GAAG;EACjCN,QAAQ,EAAE,wBAAwB;EAClCC,SAAS,EAAE,aAAa;EACxBC,KAAK,EAAE,aAAa;EACpBC,QAAQ,EAAE,aAAa;EACvBC,QAAQ,EAAE,aAAa;EACvBC,KAAK,EAAE;AACT,CAAC;AAED,OAAO,MAAME,cAAc,GAAGA,CAACC,KAAK,EAAEC,IAAI,EAAEC,SAAS,EAAEC,QAAQ,KAAK;EAClE,IAAIF,IAAI,CAACG,QAAQ,CAAC,CAAC,KAAK,CAAC,EAAE;IACzB,OAAON,0BAA0B,CAACE,KAAK,CAAC;EAC1C;EACA,OAAOT,oBAAoB,CAACS,KAAK,CAAC;AACpC,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}