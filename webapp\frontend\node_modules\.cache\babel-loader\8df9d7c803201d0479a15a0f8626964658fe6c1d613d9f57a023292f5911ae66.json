{"ast": null, "code": "import { unstable_generateUtilityClass as generateUtilityClass, unstable_generateUtilityClasses as generateUtilityClasses } from '@mui/utils';\nexport const getPickersFadeTransitionGroupUtilityClass = slot => generateUtilityClass('MuiPickersFadeTransitionGroup', slot);\nexport const pickersFadeTransitionGroupClasses = generateUtilityClasses('MuiPickersFadeTransitionGroup', ['root']);", "map": {"version": 3, "names": ["unstable_generateUtilityClass", "generateUtilityClass", "unstable_generateUtilityClasses", "generateUtilityClasses", "getPickersFadeTransitionGroupUtilityClass", "slot", "pickersFadeTransitionGroupClasses"], "sources": ["C:/CMS/webapp/frontend/node_modules/@mui/x-date-pickers/esm/DateCalendar/pickersFadeTransitionGroupClasses.js"], "sourcesContent": ["import { unstable_generateUtilityClass as generateUtilityClass, unstable_generateUtilityClasses as generateUtilityClasses } from '@mui/utils';\nexport const getPickersFadeTransitionGroupUtilityClass = slot => generateUtilityClass('MuiPickersFadeTransitionGroup', slot);\nexport const pickersFadeTransitionGroupClasses = generateUtilityClasses('MuiPickersFadeTransitionGroup', ['root']);"], "mappings": "AAAA,SAASA,6BAA6B,IAAIC,oBAAoB,EAAEC,+BAA+B,IAAIC,sBAAsB,QAAQ,YAAY;AAC7I,OAAO,MAAMC,yCAAyC,GAAGC,IAAI,IAAIJ,oBAAoB,CAAC,+BAA+B,EAAEI,IAAI,CAAC;AAC5H,OAAO,MAAMC,iCAAiC,GAAGH,sBAAsB,CAAC,+BAA+B,EAAE,CAAC,MAAM,CAAC,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}