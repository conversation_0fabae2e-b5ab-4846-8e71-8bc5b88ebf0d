{"ast": null, "code": "import React,{useEffect}from'react';import{Box,Typography,Grid,Card,CardContent,CardActionArea,Avatar,CircularProgress}from'@mui/material';import{useNavigate}from'react-router-dom';import{AdminPanelSettings as AdminIcon,Construction as ConstructionIcon,Cable as CableIcon,Description as ReportIcon}from'@mui/icons-material';import{useAuth}from'../context/AuthContext';import{jsx as _jsx,jsxs as _jsxs}from\"react/jsx-runtime\";const HomePage=()=>{const{user,isImpersonating}=useAuth();const navigate=useNavigate();// Reindirizza automaticamente in base al tipo di utente\nuseEffect(()=>{// Breve timeout per evitare reindirizzamenti troppo rapidi\nconst redirectTimer=setTimeout(()=>{// Se l'utente è un amministratore che sta impersonando un utente\nif(isImpersonating){navigate('/dashboard/admin');}// Se l'utente è un amministratore normale\nelse if((user===null||user===void 0?void 0:user.role)==='owner'){navigate('/dashboard/admin');}// Se l'utente è un utente standard\nelse if((user===null||user===void 0?void 0:user.role)==='user'){navigate('/dashboard/cantieri');}// Se l'utente è un utente cantiere\nelse if((user===null||user===void 0?void 0:user.role)==='cantieri_user'){// Reindirizza direttamente alla pagina di visualizzazione cavi\nnavigate('/dashboard/cavi/visualizza');}},300);return()=>clearTimeout(redirectTimer);},[user,isImpersonating,navigate]);// Naviga a un percorso\nconst navigateTo=path=>{navigate(path);};// Mostra un indicatore di caricamento durante il reindirizzamento\nreturn/*#__PURE__*/_jsxs(Box,{sx:{display:'flex',flexDirection:'column',alignItems:'center',justifyContent:'center',minHeight:'50vh'},children:[/*#__PURE__*/_jsx(Typography,{variant:\"h4\",gutterBottom:true,children:\"Benvenuto nel Sistema di Gestione Cantieri\"}),/*#__PURE__*/_jsx(Typography,{variant:\"body1\",paragraph:true,children:\"Reindirizzamento in corso...\"}),/*#__PURE__*/_jsx(CircularProgress,{sx:{mt:3}})]});};export default HomePage;", "map": {"version": 3, "names": ["React", "useEffect", "Box", "Typography", "Grid", "Card", "<PERSON><PERSON><PERSON><PERSON>", "CardActionArea", "Avatar", "CircularProgress", "useNavigate", "AdminPanelSettings", "AdminIcon", "Construction", "ConstructionIcon", "Cable", "CableIcon", "Description", "ReportIcon", "useAuth", "jsx", "_jsx", "jsxs", "_jsxs", "HomePage", "user", "isImpersonating", "navigate", "redirectTimer", "setTimeout", "role", "clearTimeout", "navigateTo", "path", "sx", "display", "flexDirection", "alignItems", "justifyContent", "minHeight", "children", "variant", "gutterBottom", "paragraph", "mt"], "sources": ["C:/Users/<USER>/PycharmProjects/CMS/webapp/frontend/src/pages/HomePage.js"], "sourcesContent": ["import React, { useEffect } from 'react';\nimport { Box, Typography, Grid, Card, CardContent, CardActionArea, Avatar, CircularProgress } from '@mui/material';\nimport { useNavigate } from 'react-router-dom';\nimport {\n  AdminPanelSettings as AdminIcon,\n  Construction as ConstructionIcon,\n  Cable as CableIcon,\n  Description as ReportIcon\n} from '@mui/icons-material';\nimport { useAuth } from '../context/AuthContext';\n\nconst HomePage = () => {\n  const { user, isImpersonating } = useAuth();\n  const navigate = useNavigate();\n\n  // Reindirizza automaticamente in base al tipo di utente\n  useEffect(() => {\n    // Breve timeout per evitare reindirizzamenti troppo rapidi\n    const redirectTimer = setTimeout(() => {\n      // Se l'utente è un amministratore che sta impersonando un utente\n      if (isImpersonating) {\n        navigate('/dashboard/admin');\n      }\n      // Se l'utente è un amministratore normale\n      else if (user?.role === 'owner') {\n        navigate('/dashboard/admin');\n      }\n      // Se l'utente è un utente standard\n      else if (user?.role === 'user') {\n        navigate('/dashboard/cantieri');\n      }\n      // Se l'utente è un utente cantiere\n      else if (user?.role === 'cantieri_user') {\n        // Reindirizza direttamente alla pagina di visualizzazione cavi\n        navigate('/dashboard/cavi/visualizza');\n      }\n    }, 300);\n\n    return () => clearTimeout(redirectTimer);\n  }, [user, isImpersonating, navigate]);\n\n  // Naviga a un percorso\n  const navigateTo = (path) => {\n    navigate(path);\n  };\n\n  // Mostra un indicatore di caricamento durante il reindirizzamento\n  return (\n    <Box sx={{ display: 'flex', flexDirection: 'column', alignItems: 'center', justifyContent: 'center', minHeight: '50vh' }}>\n      <Typography variant=\"h4\" gutterBottom>\n        Benvenuto nel Sistema di Gestione Cantieri\n      </Typography>\n\n      <Typography variant=\"body1\" paragraph>\n        Reindirizzamento in corso...\n      </Typography>\n\n      <CircularProgress sx={{ mt: 3 }} />\n    </Box>\n  );\n};\n\nexport default HomePage;\n"], "mappings": "AAAA,MAAO,CAAAA,KAAK,EAAIC,SAAS,KAAQ,OAAO,CACxC,OAASC,GAAG,CAAEC,UAAU,CAAEC,IAAI,CAAEC,IAAI,CAAEC,WAAW,CAAEC,cAAc,CAAEC,MAAM,CAAEC,gBAAgB,KAAQ,eAAe,CAClH,OAASC,WAAW,KAAQ,kBAAkB,CAC9C,OACEC,kBAAkB,GAAI,CAAAC,SAAS,CAC/BC,YAAY,GAAI,CAAAC,gBAAgB,CAChCC,KAAK,GAAI,CAAAC,SAAS,CAClBC,WAAW,GAAI,CAAAC,UAAU,KACpB,qBAAqB,CAC5B,OAASC,OAAO,KAAQ,wBAAwB,CAAC,OAAAC,GAAA,IAAAC,IAAA,CAAAC,IAAA,IAAAC,KAAA,yBAEjD,KAAM,CAAAC,QAAQ,CAAGA,CAAA,GAAM,CACrB,KAAM,CAAEC,IAAI,CAAEC,eAAgB,CAAC,CAAGP,OAAO,CAAC,CAAC,CAC3C,KAAM,CAAAQ,QAAQ,CAAGjB,WAAW,CAAC,CAAC,CAE9B;AACAT,SAAS,CAAC,IAAM,CACd;AACA,KAAM,CAAA2B,aAAa,CAAGC,UAAU,CAAC,IAAM,CACrC;AACA,GAAIH,eAAe,CAAE,CACnBC,QAAQ,CAAC,kBAAkB,CAAC,CAC9B,CACA;AAAA,IACK,IAAI,CAAAF,IAAI,SAAJA,IAAI,iBAAJA,IAAI,CAAEK,IAAI,IAAK,OAAO,CAAE,CAC/BH,QAAQ,CAAC,kBAAkB,CAAC,CAC9B,CACA;AAAA,IACK,IAAI,CAAAF,IAAI,SAAJA,IAAI,iBAAJA,IAAI,CAAEK,IAAI,IAAK,MAAM,CAAE,CAC9BH,QAAQ,CAAC,qBAAqB,CAAC,CACjC,CACA;AAAA,IACK,IAAI,CAAAF,IAAI,SAAJA,IAAI,iBAAJA,IAAI,CAAEK,IAAI,IAAK,eAAe,CAAE,CACvC;AACAH,QAAQ,CAAC,4BAA4B,CAAC,CACxC,CACF,CAAC,CAAE,GAAG,CAAC,CAEP,MAAO,IAAMI,YAAY,CAACH,aAAa,CAAC,CAC1C,CAAC,CAAE,CAACH,IAAI,CAAEC,eAAe,CAAEC,QAAQ,CAAC,CAAC,CAErC;AACA,KAAM,CAAAK,UAAU,CAAIC,IAAI,EAAK,CAC3BN,QAAQ,CAACM,IAAI,CAAC,CAChB,CAAC,CAED;AACA,mBACEV,KAAA,CAACrB,GAAG,EAACgC,EAAE,CAAE,CAAEC,OAAO,CAAE,MAAM,CAAEC,aAAa,CAAE,QAAQ,CAAEC,UAAU,CAAE,QAAQ,CAAEC,cAAc,CAAE,QAAQ,CAAEC,SAAS,CAAE,MAAO,CAAE,CAAAC,QAAA,eACvHnB,IAAA,CAAClB,UAAU,EAACsC,OAAO,CAAC,IAAI,CAACC,YAAY,MAAAF,QAAA,CAAC,4CAEtC,CAAY,CAAC,cAEbnB,IAAA,CAAClB,UAAU,EAACsC,OAAO,CAAC,OAAO,CAACE,SAAS,MAAAH,QAAA,CAAC,8BAEtC,CAAY,CAAC,cAEbnB,IAAA,CAACZ,gBAAgB,EAACyB,EAAE,CAAE,CAAEU,EAAE,CAAE,CAAE,CAAE,CAAE,CAAC,EAChC,CAAC,CAEV,CAAC,CAED,cAAe,CAAApB,QAAQ", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}