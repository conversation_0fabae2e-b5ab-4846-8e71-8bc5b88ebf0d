{"ast": null, "code": "var formatRelativeLocale = {\n  lastWeek: \"'i' EEEE's kl.' p\",\n  yesterday: \"'igår kl.' p\",\n  today: \"'idag kl.' p\",\n  tomorrow: \"'imorgon kl.' p\",\n  nextWeek: \"EEEE 'kl.' p\",\n  other: 'P'\n};\nvar formatRelative = function formatRelative(token, _date, _baseDate, _options) {\n  return formatRelativeLocale[token];\n};\nexport default formatRelative;", "map": {"version": 3, "names": ["formatRelativeLocale", "lastWeek", "yesterday", "today", "tomorrow", "nextWeek", "other", "formatRelative", "token", "_date", "_baseDate", "_options"], "sources": ["C:/CMS/webapp/frontend/node_modules/date-fns/esm/locale/sv/_lib/formatRelative/index.js"], "sourcesContent": ["var formatRelativeLocale = {\n  lastWeek: \"'i' EEEE's kl.' p\",\n  yesterday: \"'igår kl.' p\",\n  today: \"'idag kl.' p\",\n  tomorrow: \"'imorgon kl.' p\",\n  nextWeek: \"EEEE 'kl.' p\",\n  other: 'P'\n};\nvar formatRelative = function formatRelative(token, _date, _baseDate, _options) {\n  return formatRelativeLocale[token];\n};\nexport default formatRelative;"], "mappings": "AAAA,IAAIA,oBAAoB,GAAG;EACzBC,QAAQ,EAAE,mBAAmB;EAC7BC,SAAS,EAAE,cAAc;EACzBC,KAAK,EAAE,cAAc;EACrBC,QAAQ,EAAE,iBAAiB;EAC3BC,QAAQ,EAAE,cAAc;EACxBC,KAAK,EAAE;AACT,CAAC;AACD,IAAIC,cAAc,GAAG,SAASA,cAAcA,CAACC,KAAK,EAAEC,KAAK,EAAEC,SAAS,EAAEC,QAAQ,EAAE;EAC9E,OAAOX,oBAAoB,CAACQ,KAAK,CAAC;AACpC,CAAC;AACD,eAAeD,cAAc", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}