{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\PycharmProjects\\\\CMS\\\\webapp\\\\frontend\\\\src\\\\components\\\\certificazioni\\\\StrumentiList.jsx\",\n  _s = $RefreshSig$();\nimport React, { useState } from 'react';\nimport { Paper, Table, TableBody, TableCell, TableContainer, TableHead, TableRow, IconButton, Typography, Box, Chip, Dialog, DialogTitle, DialogContent, DialogActions, Button } from '@mui/material';\nimport { Edit as EditIcon, Delete as DeleteIcon, Warning as WarningIcon } from '@mui/icons-material';\nimport { apiService } from '../../services/apiService';\nimport { jsxDEV as _jsxDEV, Fragment as _Fragment } from \"react/jsx-dev-runtime\";\nfunction StrumentiList({\n  strumenti,\n  onEdit,\n  onDelete,\n  cantiereId\n}) {\n  _s();\n  const [showDeleteDialog, setShowDeleteDialog] = useState(false);\n  const [strumentoToDelete, setStrumentoToDelete] = useState(null);\n  const [loading, setLoading] = useState(false);\n  const handleDeleteClick = strumento => {\n    setStrumentoToDelete(strumento);\n    setShowDeleteDialog(true);\n  };\n  const handleDeleteConfirm = async () => {\n    try {\n      setLoading(true);\n      await apiService.deleteStrumento(cantiereId, strumentoToDelete.id_strumento);\n      setShowDeleteDialog(false);\n      setStrumentoToDelete(null);\n      onDelete();\n    } catch (error) {\n      console.error('Errore nell\\'eliminazione:', error);\n      // TODO: Mostrare errore all'utente\n    } finally {\n      setLoading(false);\n    }\n  };\n  const formatDate = dateString => {\n    if (!dateString) return '-';\n    return new Date(dateString).toLocaleDateString('it-IT');\n  };\n  const isCalibrationExpired = dataScadenza => {\n    if (!dataScadenza) return false;\n    const today = new Date();\n    const scadenza = new Date(dataScadenza);\n    return scadenza < today;\n  };\n  const isCalibrationExpiringSoon = dataScadenza => {\n    if (!dataScadenza) return false;\n    const today = new Date();\n    const scadenza = new Date(dataScadenza);\n    const diffTime = scadenza - today;\n    const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));\n    return diffDays <= 30 && diffDays > 0;\n  };\n  const getCalibrationStatus = dataScadenza => {\n    if (isCalibrationExpired(dataScadenza)) {\n      return {\n        label: 'Scaduto',\n        color: 'error'\n      };\n    }\n    if (isCalibrationExpiringSoon(dataScadenza)) {\n      return {\n        label: 'In scadenza',\n        color: 'warning'\n      };\n    }\n    return {\n      label: 'Valido',\n      color: 'success'\n    };\n  };\n  if (strumenti.length === 0) {\n    return /*#__PURE__*/_jsxDEV(Paper, {\n      sx: {\n        p: 3,\n        textAlign: 'center'\n      },\n      children: [/*#__PURE__*/_jsxDEV(Typography, {\n        variant: \"h6\",\n        color: \"text.secondary\",\n        children: \"Nessuno strumento certificato trovato\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 87,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Typography, {\n        variant: \"body2\",\n        color: \"text.secondary\",\n        sx: {\n          mt: 1\n        },\n        children: \"Clicca su \\\"Nuovo Strumento\\\" per aggiungere il primo strumento\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 90,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 86,\n      columnNumber: 7\n    }, this);\n  }\n  return /*#__PURE__*/_jsxDEV(_Fragment, {\n    children: [/*#__PURE__*/_jsxDEV(TableContainer, {\n      component: Paper,\n      children: /*#__PURE__*/_jsxDEV(Table, {\n        children: [/*#__PURE__*/_jsxDEV(TableHead, {\n          children: /*#__PURE__*/_jsxDEV(TableRow, {\n            children: [/*#__PURE__*/_jsxDEV(TableCell, {\n              children: /*#__PURE__*/_jsxDEV(\"strong\", {\n                children: \"Nome\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 103,\n                columnNumber: 26\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 103,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n              children: /*#__PURE__*/_jsxDEV(\"strong\", {\n                children: \"Marca\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 104,\n                columnNumber: 26\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 104,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n              children: /*#__PURE__*/_jsxDEV(\"strong\", {\n                children: \"Modello\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 105,\n                columnNumber: 26\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 105,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n              children: /*#__PURE__*/_jsxDEV(\"strong\", {\n                children: \"N\\xB0 Serie\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 106,\n                columnNumber: 26\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 106,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n              children: /*#__PURE__*/_jsxDEV(\"strong\", {\n                children: \"Data Calibrazione\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 107,\n                columnNumber: 26\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 107,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n              children: /*#__PURE__*/_jsxDEV(\"strong\", {\n                children: \"Scadenza Calibrazione\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 108,\n                columnNumber: 26\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 108,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n              children: /*#__PURE__*/_jsxDEV(\"strong\", {\n                children: \"Stato\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 109,\n                columnNumber: 26\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 109,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n              children: /*#__PURE__*/_jsxDEV(\"strong\", {\n                children: \"Azioni\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 110,\n                columnNumber: 26\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 110,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 102,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 101,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(TableBody, {\n          children: strumenti.map(strumento => {\n            const calibrationStatus = getCalibrationStatus(strumento.data_scadenza_calibrazione);\n            return /*#__PURE__*/_jsxDEV(TableRow, {\n              hover: true,\n              children: [/*#__PURE__*/_jsxDEV(TableCell, {\n                children: /*#__PURE__*/_jsxDEV(Typography, {\n                  variant: \"body2\",\n                  fontWeight: \"bold\",\n                  children: strumento.nome\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 120,\n                  columnNumber: 21\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 119,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n                children: strumento.marca\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 124,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n                children: strumento.modello\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 125,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n                children: /*#__PURE__*/_jsxDEV(Typography, {\n                  variant: \"body2\",\n                  fontFamily: \"monospace\",\n                  children: strumento.numero_serie\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 127,\n                  columnNumber: 21\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 126,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n                children: formatDate(strumento.data_calibrazione)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 131,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n                children: /*#__PURE__*/_jsxDEV(Box, {\n                  sx: {\n                    display: 'flex',\n                    alignItems: 'center',\n                    gap: 1\n                  },\n                  children: [formatDate(strumento.data_scadenza_calibrazione), isCalibrationExpired(strumento.data_scadenza_calibrazione) && /*#__PURE__*/_jsxDEV(WarningIcon, {\n                    color: \"error\",\n                    fontSize: \"small\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 136,\n                    columnNumber: 25\n                  }, this), isCalibrationExpiringSoon(strumento.data_scadenza_calibrazione) && /*#__PURE__*/_jsxDEV(WarningIcon, {\n                    color: \"warning\",\n                    fontSize: \"small\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 139,\n                    columnNumber: 25\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 133,\n                  columnNumber: 21\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 132,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n                children: /*#__PURE__*/_jsxDEV(Chip, {\n                  label: calibrationStatus.label,\n                  color: calibrationStatus.color,\n                  size: \"small\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 144,\n                  columnNumber: 21\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 143,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n                children: /*#__PURE__*/_jsxDEV(Box, {\n                  sx: {\n                    display: 'flex',\n                    gap: 0.5\n                  },\n                  children: [/*#__PURE__*/_jsxDEV(IconButton, {\n                    size: \"small\",\n                    onClick: () => onEdit(strumento),\n                    title: \"Modifica\",\n                    children: /*#__PURE__*/_jsxDEV(EditIcon, {\n                      fontSize: \"small\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 157,\n                      columnNumber: 25\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 152,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(IconButton, {\n                    size: \"small\",\n                    onClick: () => handleDeleteClick(strumento),\n                    title: \"Elimina\",\n                    color: \"error\",\n                    children: /*#__PURE__*/_jsxDEV(DeleteIcon, {\n                      fontSize: \"small\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 165,\n                      columnNumber: 25\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 159,\n                    columnNumber: 23\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 151,\n                  columnNumber: 21\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 150,\n                columnNumber: 19\n              }, this)]\n            }, strumento.id_strumento, true, {\n              fileName: _jsxFileName,\n              lineNumber: 118,\n              columnNumber: 17\n            }, this);\n          })\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 113,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 100,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 99,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Dialog, {\n      open: showDeleteDialog,\n      onClose: () => setShowDeleteDialog(false),\n      children: [/*#__PURE__*/_jsxDEV(DialogTitle, {\n        children: \"Conferma Eliminazione\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 181,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(DialogContent, {\n        children: [/*#__PURE__*/_jsxDEV(Typography, {\n          children: [\"Sei sicuro di voler eliminare lo strumento \\\"\", strumentoToDelete === null || strumentoToDelete === void 0 ? void 0 : strumentoToDelete.nome, \" \", strumentoToDelete === null || strumentoToDelete === void 0 ? void 0 : strumentoToDelete.marca, \" \", strumentoToDelete === null || strumentoToDelete === void 0 ? void 0 : strumentoToDelete.modello, \"\\\"?\"]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 183,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Typography, {\n          variant: \"body2\",\n          color: \"text.secondary\",\n          sx: {\n            mt: 1\n          },\n          children: \"Questa operazione non pu\\xF2 essere annullata. Lo strumento non potr\\xE0 essere eliminato se \\xE8 utilizzato in certificazioni esistenti.\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 186,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 182,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(DialogActions, {\n        children: [/*#__PURE__*/_jsxDEV(Button, {\n          onClick: () => setShowDeleteDialog(false),\n          children: \"Annulla\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 191,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Button, {\n          onClick: handleDeleteConfirm,\n          color: \"error\",\n          disabled: loading,\n          children: \"Elimina\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 194,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 190,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 177,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true);\n}\n_s(StrumentiList, \"ofzQtc98/ciPMs3LeTqdG4i9GQY=\");\n_c = StrumentiList;\nexport default StrumentiList;\nvar _c;\n$RefreshReg$(_c, \"StrumentiList\");", "map": {"version": 3, "names": ["React", "useState", "Paper", "Table", "TableBody", "TableCell", "TableContainer", "TableHead", "TableRow", "IconButton", "Typography", "Box", "Chip", "Dialog", "DialogTitle", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "DialogActions", "<PERSON><PERSON>", "Edit", "EditIcon", "Delete", "DeleteIcon", "Warning", "WarningIcon", "apiService", "jsxDEV", "_jsxDEV", "Fragment", "_Fragment", "StrumentiList", "strumenti", "onEdit", "onDelete", "cantiereId", "_s", "showDeleteDialog", "setShowDeleteDialog", "strumentoToDelete", "setStrumentoToDelete", "loading", "setLoading", "handleDeleteClick", "strumento", "handleDeleteConfirm", "deleteStrumento", "id_strumento", "error", "console", "formatDate", "dateString", "Date", "toLocaleDateString", "isCalibrationExpired", "dataScadenza", "today", "scadenza", "isCalibrationExpiringSoon", "diffTime", "diffDays", "Math", "ceil", "getCalibrationStatus", "label", "color", "length", "sx", "p", "textAlign", "children", "variant", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "mt", "component", "map", "calibrationStatus", "data_scadenza_calibrazione", "hover", "fontWeight", "nome", "marca", "modello", "fontFamily", "numero_serie", "data_calibrazione", "display", "alignItems", "gap", "fontSize", "size", "onClick", "title", "open", "onClose", "disabled", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/PycharmProjects/CMS/webapp/frontend/src/components/certificazioni/StrumentiList.jsx"], "sourcesContent": ["import React, { useState } from 'react';\nimport {\n  Paper,\n  Table,\n  TableBody,\n  TableCell,\n  TableContainer,\n  TableHead,\n  TableRow,\n  IconButton,\n  Typography,\n  Box,\n  Chip,\n  Dialog,\n  DialogTitle,\n  DialogContent,\n  DialogActions,\n  Button\n} from '@mui/material';\nimport {\n  Edit as EditIcon,\n  Delete as DeleteIcon,\n  Warning as WarningIcon\n} from '@mui/icons-material';\n\nimport { apiService } from '../../services/apiService';\n\nfunction StrumentiList({ strumenti, onEdit, onDelete, cantiereId }) {\n  const [showDeleteDialog, setShowDeleteDialog] = useState(false);\n  const [strumentoToDelete, setStrumentoToDelete] = useState(null);\n  const [loading, setLoading] = useState(false);\n\n  const handleDeleteClick = (strumento) => {\n    setStrumentoToDelete(strumento);\n    setShowDeleteDialog(true);\n  };\n\n  const handleDeleteConfirm = async () => {\n    try {\n      setLoading(true);\n      await apiService.deleteStrumento(cantiereId, strumentoToDelete.id_strumento);\n      setShowDeleteDialog(false);\n      setStrumentoToDelete(null);\n      onDelete();\n    } catch (error) {\n      console.error('Errore nell\\'eliminazione:', error);\n      // TODO: Mostrare errore all'utente\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  const formatDate = (dateString) => {\n    if (!dateString) return '-';\n    return new Date(dateString).toLocaleDateString('it-IT');\n  };\n\n  const isCalibrationExpired = (dataScadenza) => {\n    if (!dataScadenza) return false;\n    const today = new Date();\n    const scadenza = new Date(dataScadenza);\n    return scadenza < today;\n  };\n\n  const isCalibrationExpiringSoon = (dataScadenza) => {\n    if (!dataScadenza) return false;\n    const today = new Date();\n    const scadenza = new Date(dataScadenza);\n    const diffTime = scadenza - today;\n    const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));\n    return diffDays <= 30 && diffDays > 0;\n  };\n\n  const getCalibrationStatus = (dataScadenza) => {\n    if (isCalibrationExpired(dataScadenza)) {\n      return { label: 'Scaduto', color: 'error' };\n    }\n    if (isCalibrationExpiringSoon(dataScadenza)) {\n      return { label: 'In scadenza', color: 'warning' };\n    }\n    return { label: 'Valido', color: 'success' };\n  };\n\n  if (strumenti.length === 0) {\n    return (\n      <Paper sx={{ p: 3, textAlign: 'center' }}>\n        <Typography variant=\"h6\" color=\"text.secondary\">\n          Nessuno strumento certificato trovato\n        </Typography>\n        <Typography variant=\"body2\" color=\"text.secondary\" sx={{ mt: 1 }}>\n          Clicca su \"Nuovo Strumento\" per aggiungere il primo strumento\n        </Typography>\n      </Paper>\n    );\n  }\n\n  return (\n    <>\n      <TableContainer component={Paper}>\n        <Table>\n          <TableHead>\n            <TableRow>\n              <TableCell><strong>Nome</strong></TableCell>\n              <TableCell><strong>Marca</strong></TableCell>\n              <TableCell><strong>Modello</strong></TableCell>\n              <TableCell><strong>N° Serie</strong></TableCell>\n              <TableCell><strong>Data Calibrazione</strong></TableCell>\n              <TableCell><strong>Scadenza Calibrazione</strong></TableCell>\n              <TableCell><strong>Stato</strong></TableCell>\n              <TableCell><strong>Azioni</strong></TableCell>\n            </TableRow>\n          </TableHead>\n          <TableBody>\n            {strumenti.map((strumento) => {\n              const calibrationStatus = getCalibrationStatus(strumento.data_scadenza_calibrazione);\n              \n              return (\n                <TableRow key={strumento.id_strumento} hover>\n                  <TableCell>\n                    <Typography variant=\"body2\" fontWeight=\"bold\">\n                      {strumento.nome}\n                    </Typography>\n                  </TableCell>\n                  <TableCell>{strumento.marca}</TableCell>\n                  <TableCell>{strumento.modello}</TableCell>\n                  <TableCell>\n                    <Typography variant=\"body2\" fontFamily=\"monospace\">\n                      {strumento.numero_serie}\n                    </Typography>\n                  </TableCell>\n                  <TableCell>{formatDate(strumento.data_calibrazione)}</TableCell>\n                  <TableCell>\n                    <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>\n                      {formatDate(strumento.data_scadenza_calibrazione)}\n                      {isCalibrationExpired(strumento.data_scadenza_calibrazione) && (\n                        <WarningIcon color=\"error\" fontSize=\"small\" />\n                      )}\n                      {isCalibrationExpiringSoon(strumento.data_scadenza_calibrazione) && (\n                        <WarningIcon color=\"warning\" fontSize=\"small\" />\n                      )}\n                    </Box>\n                  </TableCell>\n                  <TableCell>\n                    <Chip\n                      label={calibrationStatus.label}\n                      color={calibrationStatus.color}\n                      size=\"small\"\n                    />\n                  </TableCell>\n                  <TableCell>\n                    <Box sx={{ display: 'flex', gap: 0.5 }}>\n                      <IconButton\n                        size=\"small\"\n                        onClick={() => onEdit(strumento)}\n                        title=\"Modifica\"\n                      >\n                        <EditIcon fontSize=\"small\" />\n                      </IconButton>\n                      <IconButton\n                        size=\"small\"\n                        onClick={() => handleDeleteClick(strumento)}\n                        title=\"Elimina\"\n                        color=\"error\"\n                      >\n                        <DeleteIcon fontSize=\"small\" />\n                      </IconButton>\n                    </Box>\n                  </TableCell>\n                </TableRow>\n              );\n            })}\n          </TableBody>\n        </Table>\n      </TableContainer>\n\n      {/* Dialog Conferma Eliminazione */}\n      <Dialog\n        open={showDeleteDialog}\n        onClose={() => setShowDeleteDialog(false)}\n      >\n        <DialogTitle>Conferma Eliminazione</DialogTitle>\n        <DialogContent>\n          <Typography>\n            Sei sicuro di voler eliminare lo strumento \"{strumentoToDelete?.nome} {strumentoToDelete?.marca} {strumentoToDelete?.modello}\"?\n          </Typography>\n          <Typography variant=\"body2\" color=\"text.secondary\" sx={{ mt: 1 }}>\n            Questa operazione non può essere annullata. Lo strumento non potrà essere eliminato se è utilizzato in certificazioni esistenti.\n          </Typography>\n        </DialogContent>\n        <DialogActions>\n          <Button onClick={() => setShowDeleteDialog(false)}>\n            Annulla\n          </Button>\n          <Button \n            onClick={handleDeleteConfirm} \n            color=\"error\" \n            disabled={loading}\n          >\n            Elimina\n          </Button>\n        </DialogActions>\n      </Dialog>\n    </>\n  );\n}\n\nexport default StrumentiList;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,QAAQ,OAAO;AACvC,SACEC,KAAK,EACLC,KAAK,EACLC,SAAS,EACTC,SAAS,EACTC,cAAc,EACdC,SAAS,EACTC,QAAQ,EACRC,UAAU,EACVC,UAAU,EACVC,GAAG,EACHC,IAAI,EACJC,MAAM,EACNC,WAAW,EACXC,aAAa,EACbC,aAAa,EACbC,MAAM,QACD,eAAe;AACtB,SACEC,IAAI,IAAIC,QAAQ,EAChBC,MAAM,IAAIC,UAAU,EACpBC,OAAO,IAAIC,WAAW,QACjB,qBAAqB;AAE5B,SAASC,UAAU,QAAQ,2BAA2B;AAAC,SAAAC,MAAA,IAAAC,OAAA,EAAAC,QAAA,IAAAC,SAAA;AAEvD,SAASC,aAAaA,CAAC;EAAEC,SAAS;EAAEC,MAAM;EAAEC,QAAQ;EAAEC;AAAW,CAAC,EAAE;EAAAC,EAAA;EAClE,MAAM,CAACC,gBAAgB,EAAEC,mBAAmB,CAAC,GAAGnC,QAAQ,CAAC,KAAK,CAAC;EAC/D,MAAM,CAACoC,iBAAiB,EAAEC,oBAAoB,CAAC,GAAGrC,QAAQ,CAAC,IAAI,CAAC;EAChE,MAAM,CAACsC,OAAO,EAAEC,UAAU,CAAC,GAAGvC,QAAQ,CAAC,KAAK,CAAC;EAE7C,MAAMwC,iBAAiB,GAAIC,SAAS,IAAK;IACvCJ,oBAAoB,CAACI,SAAS,CAAC;IAC/BN,mBAAmB,CAAC,IAAI,CAAC;EAC3B,CAAC;EAED,MAAMO,mBAAmB,GAAG,MAAAA,CAAA,KAAY;IACtC,IAAI;MACFH,UAAU,CAAC,IAAI,CAAC;MAChB,MAAMhB,UAAU,CAACoB,eAAe,CAACX,UAAU,EAAEI,iBAAiB,CAACQ,YAAY,CAAC;MAC5ET,mBAAmB,CAAC,KAAK,CAAC;MAC1BE,oBAAoB,CAAC,IAAI,CAAC;MAC1BN,QAAQ,CAAC,CAAC;IACZ,CAAC,CAAC,OAAOc,KAAK,EAAE;MACdC,OAAO,CAACD,KAAK,CAAC,4BAA4B,EAAEA,KAAK,CAAC;MAClD;IACF,CAAC,SAAS;MACRN,UAAU,CAAC,KAAK,CAAC;IACnB;EACF,CAAC;EAED,MAAMQ,UAAU,GAAIC,UAAU,IAAK;IACjC,IAAI,CAACA,UAAU,EAAE,OAAO,GAAG;IAC3B,OAAO,IAAIC,IAAI,CAACD,UAAU,CAAC,CAACE,kBAAkB,CAAC,OAAO,CAAC;EACzD,CAAC;EAED,MAAMC,oBAAoB,GAAIC,YAAY,IAAK;IAC7C,IAAI,CAACA,YAAY,EAAE,OAAO,KAAK;IAC/B,MAAMC,KAAK,GAAG,IAAIJ,IAAI,CAAC,CAAC;IACxB,MAAMK,QAAQ,GAAG,IAAIL,IAAI,CAACG,YAAY,CAAC;IACvC,OAAOE,QAAQ,GAAGD,KAAK;EACzB,CAAC;EAED,MAAME,yBAAyB,GAAIH,YAAY,IAAK;IAClD,IAAI,CAACA,YAAY,EAAE,OAAO,KAAK;IAC/B,MAAMC,KAAK,GAAG,IAAIJ,IAAI,CAAC,CAAC;IACxB,MAAMK,QAAQ,GAAG,IAAIL,IAAI,CAACG,YAAY,CAAC;IACvC,MAAMI,QAAQ,GAAGF,QAAQ,GAAGD,KAAK;IACjC,MAAMI,QAAQ,GAAGC,IAAI,CAACC,IAAI,CAACH,QAAQ,IAAI,IAAI,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,CAAC,CAAC;IAC5D,OAAOC,QAAQ,IAAI,EAAE,IAAIA,QAAQ,GAAG,CAAC;EACvC,CAAC;EAED,MAAMG,oBAAoB,GAAIR,YAAY,IAAK;IAC7C,IAAID,oBAAoB,CAACC,YAAY,CAAC,EAAE;MACtC,OAAO;QAAES,KAAK,EAAE,SAAS;QAAEC,KAAK,EAAE;MAAQ,CAAC;IAC7C;IACA,IAAIP,yBAAyB,CAACH,YAAY,CAAC,EAAE;MAC3C,OAAO;QAAES,KAAK,EAAE,aAAa;QAAEC,KAAK,EAAE;MAAU,CAAC;IACnD;IACA,OAAO;MAAED,KAAK,EAAE,QAAQ;MAAEC,KAAK,EAAE;IAAU,CAAC;EAC9C,CAAC;EAED,IAAIjC,SAAS,CAACkC,MAAM,KAAK,CAAC,EAAE;IAC1B,oBACEtC,OAAA,CAACxB,KAAK;MAAC+D,EAAE,EAAE;QAAEC,CAAC,EAAE,CAAC;QAAEC,SAAS,EAAE;MAAS,CAAE;MAAAC,QAAA,gBACvC1C,OAAA,CAAChB,UAAU;QAAC2D,OAAO,EAAC,IAAI;QAACN,KAAK,EAAC,gBAAgB;QAAAK,QAAA,EAAC;MAEhD;QAAAE,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAY,CAAC,eACb/C,OAAA,CAAChB,UAAU;QAAC2D,OAAO,EAAC,OAAO;QAACN,KAAK,EAAC,gBAAgB;QAACE,EAAE,EAAE;UAAES,EAAE,EAAE;QAAE,CAAE;QAAAN,QAAA,EAAC;MAElE;QAAAE,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAY,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACR,CAAC;EAEZ;EAEA,oBACE/C,OAAA,CAAAE,SAAA;IAAAwC,QAAA,gBACE1C,OAAA,CAACpB,cAAc;MAACqE,SAAS,EAAEzE,KAAM;MAAAkE,QAAA,eAC/B1C,OAAA,CAACvB,KAAK;QAAAiE,QAAA,gBACJ1C,OAAA,CAACnB,SAAS;UAAA6D,QAAA,eACR1C,OAAA,CAAClB,QAAQ;YAAA4D,QAAA,gBACP1C,OAAA,CAACrB,SAAS;cAAA+D,QAAA,eAAC1C,OAAA;gBAAA0C,QAAA,EAAQ;cAAI;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAW,CAAC,eAC5C/C,OAAA,CAACrB,SAAS;cAAA+D,QAAA,eAAC1C,OAAA;gBAAA0C,QAAA,EAAQ;cAAK;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAW,CAAC,eAC7C/C,OAAA,CAACrB,SAAS;cAAA+D,QAAA,eAAC1C,OAAA;gBAAA0C,QAAA,EAAQ;cAAO;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAW,CAAC,eAC/C/C,OAAA,CAACrB,SAAS;cAAA+D,QAAA,eAAC1C,OAAA;gBAAA0C,QAAA,EAAQ;cAAQ;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAW,CAAC,eAChD/C,OAAA,CAACrB,SAAS;cAAA+D,QAAA,eAAC1C,OAAA;gBAAA0C,QAAA,EAAQ;cAAiB;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAW,CAAC,eACzD/C,OAAA,CAACrB,SAAS;cAAA+D,QAAA,eAAC1C,OAAA;gBAAA0C,QAAA,EAAQ;cAAqB;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAW,CAAC,eAC7D/C,OAAA,CAACrB,SAAS;cAAA+D,QAAA,eAAC1C,OAAA;gBAAA0C,QAAA,EAAQ;cAAK;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAW,CAAC,eAC7C/C,OAAA,CAACrB,SAAS;cAAA+D,QAAA,eAAC1C,OAAA;gBAAA0C,QAAA,EAAQ;cAAM;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAW,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACtC;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACF,CAAC,eACZ/C,OAAA,CAACtB,SAAS;UAAAgE,QAAA,EACPtC,SAAS,CAAC8C,GAAG,CAAElC,SAAS,IAAK;YAC5B,MAAMmC,iBAAiB,GAAGhB,oBAAoB,CAACnB,SAAS,CAACoC,0BAA0B,CAAC;YAEpF,oBACEpD,OAAA,CAAClB,QAAQ;cAA8BuE,KAAK;cAAAX,QAAA,gBAC1C1C,OAAA,CAACrB,SAAS;gBAAA+D,QAAA,eACR1C,OAAA,CAAChB,UAAU;kBAAC2D,OAAO,EAAC,OAAO;kBAACW,UAAU,EAAC,MAAM;kBAAAZ,QAAA,EAC1C1B,SAAS,CAACuC;gBAAI;kBAAAX,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACL;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACJ,CAAC,eACZ/C,OAAA,CAACrB,SAAS;gBAAA+D,QAAA,EAAE1B,SAAS,CAACwC;cAAK;gBAAAZ,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAY,CAAC,eACxC/C,OAAA,CAACrB,SAAS;gBAAA+D,QAAA,EAAE1B,SAAS,CAACyC;cAAO;gBAAAb,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAY,CAAC,eAC1C/C,OAAA,CAACrB,SAAS;gBAAA+D,QAAA,eACR1C,OAAA,CAAChB,UAAU;kBAAC2D,OAAO,EAAC,OAAO;kBAACe,UAAU,EAAC,WAAW;kBAAAhB,QAAA,EAC/C1B,SAAS,CAAC2C;gBAAY;kBAAAf,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACb;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACJ,CAAC,eACZ/C,OAAA,CAACrB,SAAS;gBAAA+D,QAAA,EAAEpB,UAAU,CAACN,SAAS,CAAC4C,iBAAiB;cAAC;gBAAAhB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAY,CAAC,eAChE/C,OAAA,CAACrB,SAAS;gBAAA+D,QAAA,eACR1C,OAAA,CAACf,GAAG;kBAACsD,EAAE,EAAE;oBAAEsB,OAAO,EAAE,MAAM;oBAAEC,UAAU,EAAE,QAAQ;oBAAEC,GAAG,EAAE;kBAAE,CAAE;kBAAArB,QAAA,GACxDpB,UAAU,CAACN,SAAS,CAACoC,0BAA0B,CAAC,EAChD1B,oBAAoB,CAACV,SAAS,CAACoC,0BAA0B,CAAC,iBACzDpD,OAAA,CAACH,WAAW;oBAACwC,KAAK,EAAC,OAAO;oBAAC2B,QAAQ,EAAC;kBAAO;oBAAApB,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE,CAC9C,EACAjB,yBAAyB,CAACd,SAAS,CAACoC,0BAA0B,CAAC,iBAC9DpD,OAAA,CAACH,WAAW;oBAACwC,KAAK,EAAC,SAAS;oBAAC2B,QAAQ,EAAC;kBAAO;oBAAApB,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE,CAChD;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACE;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACG,CAAC,eACZ/C,OAAA,CAACrB,SAAS;gBAAA+D,QAAA,eACR1C,OAAA,CAACd,IAAI;kBACHkD,KAAK,EAAEe,iBAAiB,CAACf,KAAM;kBAC/BC,KAAK,EAAEc,iBAAiB,CAACd,KAAM;kBAC/B4B,IAAI,EAAC;gBAAO;kBAAArB,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACb;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACO,CAAC,eACZ/C,OAAA,CAACrB,SAAS;gBAAA+D,QAAA,eACR1C,OAAA,CAACf,GAAG;kBAACsD,EAAE,EAAE;oBAAEsB,OAAO,EAAE,MAAM;oBAAEE,GAAG,EAAE;kBAAI,CAAE;kBAAArB,QAAA,gBACrC1C,OAAA,CAACjB,UAAU;oBACTkF,IAAI,EAAC,OAAO;oBACZC,OAAO,EAAEA,CAAA,KAAM7D,MAAM,CAACW,SAAS,CAAE;oBACjCmD,KAAK,EAAC,UAAU;oBAAAzB,QAAA,eAEhB1C,OAAA,CAACP,QAAQ;sBAACuE,QAAQ,EAAC;oBAAO;sBAAApB,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAE;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACnB,CAAC,eACb/C,OAAA,CAACjB,UAAU;oBACTkF,IAAI,EAAC,OAAO;oBACZC,OAAO,EAAEA,CAAA,KAAMnD,iBAAiB,CAACC,SAAS,CAAE;oBAC5CmD,KAAK,EAAC,SAAS;oBACf9B,KAAK,EAAC,OAAO;oBAAAK,QAAA,eAEb1C,OAAA,CAACL,UAAU;sBAACqE,QAAQ,EAAC;oBAAO;sBAAApB,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAE;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACrB,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACV;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACG,CAAC;YAAA,GAlDC/B,SAAS,CAACG,YAAY;cAAAyB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAmD3B,CAAC;UAEf,CAAC;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACO,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACP;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACM,CAAC,eAGjB/C,OAAA,CAACb,MAAM;MACLiF,IAAI,EAAE3D,gBAAiB;MACvB4D,OAAO,EAAEA,CAAA,KAAM3D,mBAAmB,CAAC,KAAK,CAAE;MAAAgC,QAAA,gBAE1C1C,OAAA,CAACZ,WAAW;QAAAsD,QAAA,EAAC;MAAqB;QAAAE,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAa,CAAC,eAChD/C,OAAA,CAACX,aAAa;QAAAqD,QAAA,gBACZ1C,OAAA,CAAChB,UAAU;UAAA0D,QAAA,GAAC,+CACkC,EAAC/B,iBAAiB,aAAjBA,iBAAiB,uBAAjBA,iBAAiB,CAAE4C,IAAI,EAAC,GAAC,EAAC5C,iBAAiB,aAAjBA,iBAAiB,uBAAjBA,iBAAiB,CAAE6C,KAAK,EAAC,GAAC,EAAC7C,iBAAiB,aAAjBA,iBAAiB,uBAAjBA,iBAAiB,CAAE8C,OAAO,EAAC,KAC/H;QAAA;UAAAb,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAY,CAAC,eACb/C,OAAA,CAAChB,UAAU;UAAC2D,OAAO,EAAC,OAAO;UAACN,KAAK,EAAC,gBAAgB;UAACE,EAAE,EAAE;YAAES,EAAE,EAAE;UAAE,CAAE;UAAAN,QAAA,EAAC;QAElE;UAAAE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAY,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACA,CAAC,eAChB/C,OAAA,CAACV,aAAa;QAAAoD,QAAA,gBACZ1C,OAAA,CAACT,MAAM;UAAC2E,OAAO,EAAEA,CAAA,KAAMxD,mBAAmB,CAAC,KAAK,CAAE;UAAAgC,QAAA,EAAC;QAEnD;UAAAE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC,eACT/C,OAAA,CAACT,MAAM;UACL2E,OAAO,EAAEjD,mBAAoB;UAC7BoB,KAAK,EAAC,OAAO;UACbiC,QAAQ,EAAEzD,OAAQ;UAAA6B,QAAA,EACnB;QAED;UAAAE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACI,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACV,CAAC;EAAA,eACT,CAAC;AAEP;AAACvC,EAAA,CAjLQL,aAAa;AAAAoE,EAAA,GAAbpE,aAAa;AAmLtB,eAAeA,aAAa;AAAC,IAAAoE,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}