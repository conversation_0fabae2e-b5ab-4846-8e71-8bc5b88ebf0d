'use client'

import { useState } from 'react'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Badge } from '@/components/ui/badge'
import { Input } from '@/components/ui/input'
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '@/components/ui/table'
import { Alert, AlertDescription } from '@/components/ui/alert'
import { 
  BarChart3, 
  Search, 
  Plus, 
  Edit, 
  Trash2, 
  AlertCircle,
  CheckCircle,
  FileText,
  Loader2,
  Download,
  RefreshCw
} from 'lucide-react'
import { RapportoGeneraleCollaudo } from '@/types/certificazioni'
import { rapportiGeneraliApi } from '@/lib/api'

interface RapportiGeneraliManagerProps {
  cantiereId: number
  rapporti: RapportoGeneraleCollaudo[]
  onUpdate: () => void
}

export default function RapportiGeneraliManager({ cantiereId, rapporti, onUpdate }: RapportiGeneraliManagerProps) {
  const [searchTerm, setSearchTerm] = useState('')
  const [isLoading, setIsLoading] = useState(false)
  const [error, setError] = useState('')

  const handleCreateRapporto = () => {
    // TODO: Implementare form per nuovo rapporto
    console.log('Crea nuovo rapporto')
  }

  const handleEditRapporto = (rapporto: RapportoGeneraleCollaudo) => {
    // TODO: Implementare form per modifica rapporto
    console.log('Modifica rapporto', rapporto)
  }

  const handleDeleteRapporto = async (id: number) => {
    if (!confirm('Sei sicuro di voler eliminare questo rapporto?')) return
    
    try {
      setIsLoading(true)
      await rapportiGeneraliApi.deleteRapporto(cantiereId, id)
      onUpdate()
    } catch (error: any) {
      setError(error.response?.data?.detail || 'Errore durante l\'eliminazione')
    } finally {
      setIsLoading(false)
    }
  }

  const handleAggiornaStatistiche = async (id: number) => {
    try {
      setIsLoading(true)
      await rapportiGeneraliApi.aggiornaStatistiche(cantiereId, id)
      onUpdate()
    } catch (error: any) {
      setError(error.response?.data?.detail || 'Errore durante l\'aggiornamento')
    } finally {
      setIsLoading(false)
    }
  }

  const getStatoBadge = (stato: string) => {
    switch (stato?.toLowerCase()) {
      case 'completato':
        return <Badge className="bg-green-100 text-green-800 border-green-200">Completato</Badge>
      case 'approvato':
        return <Badge className="bg-blue-100 text-blue-800 border-blue-200">Approvato</Badge>
      case 'bozza':
        return <Badge className="bg-gray-100 text-gray-800 border-gray-200">Bozza</Badge>
      default:
        return <Badge className="bg-gray-100 text-gray-800 border-gray-200">Da Verificare</Badge>
    }
  }

  const getConformitaBadge = (rapporto: RapportoGeneraleCollaudo) => {
    if (rapporto.numero_cavi_totali === 0) {
      return <Badge variant="outline">Nessun Cavo</Badge>
    }

    const percentuale = (rapporto.numero_cavi_conformi / rapporto.numero_cavi_totali) * 100

    if (percentuale === 100) {
      return <Badge className="bg-green-100 text-green-800 border-green-200">100% Conforme</Badge>
    } else if (percentuale >= 90) {
      return <Badge className="bg-yellow-100 text-yellow-800 border-yellow-200">{percentuale.toFixed(1)}% Conforme</Badge>
    } else {
      return <Badge className="bg-red-100 text-red-800 border-red-200">{percentuale.toFixed(1)}% Conforme</Badge>
    }
  }

  const filteredRapporti = rapporti.filter(rapporto => {
    const searchLower = searchTerm.toLowerCase()
    return (
      rapporto.numero_rapporto?.toLowerCase().includes(searchLower) ||
      rapporto.nome_progetto?.toLowerCase().includes(searchLower) ||
      rapporto.cliente_finale?.toLowerCase().includes(searchLower)
    )
  })

  const stats = {
    totali: rapporti.length,
    completati: rapporti.filter(r => r.stato_rapporto === 'COMPLETATO').length,
    approvati: rapporti.filter(r => r.stato_rapporto === 'APPROVATO').length,
    bozze: rapporti.filter(r => r.stato_rapporto === 'BOZZA').length,
    cavi_totali: rapporti.reduce((sum, r) => sum + r.numero_cavi_totali, 0),
    cavi_conformi: rapporti.reduce((sum, r) => sum + r.numero_cavi_conformi, 0)
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h2 className="text-2xl font-bold text-slate-900 flex items-center gap-3">
            <BarChart3 className="h-6 w-6 text-blue-600" />
            Rapporti Generali di Collaudo
          </h2>
          <p className="text-slate-600 mt-1">Gestione rapporti generali CEI 64-8</p>
        </div>
        
        <Button onClick={handleCreateRapporto}>
          <Plus className="h-4 w-4 mr-2" />
          Nuovo Rapporto
        </Button>
      </div>

      {/* Statistics */}
      <div className="grid grid-cols-1 md:grid-cols-5 gap-4">
        <Card>
          <CardContent className="p-4">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm text-slate-600">Totali</p>
                <p className="text-2xl font-bold text-slate-900">{stats.totali}</p>
              </div>
              <FileText className="h-8 w-8 text-blue-500" />
            </div>
          </CardContent>
        </Card>
        
        <Card>
          <CardContent className="p-4">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm text-slate-600">Completati</p>
                <p className="text-2xl font-bold text-green-600">{stats.completati}</p>
              </div>
              <CheckCircle className="h-8 w-8 text-green-500" />
            </div>
          </CardContent>
        </Card>
        
        <Card>
          <CardContent className="p-4">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm text-slate-600">Approvati</p>
                <p className="text-2xl font-bold text-blue-600">{stats.approvati}</p>
              </div>
              <BarChart3 className="h-8 w-8 text-blue-500" />
            </div>
          </CardContent>
        </Card>
        
        <Card>
          <CardContent className="p-4">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm text-slate-600">Cavi Totali</p>
                <p className="text-2xl font-bold text-slate-900">{stats.cavi_totali}</p>
              </div>
              <FileText className="h-8 w-8 text-slate-500" />
            </div>
          </CardContent>
        </Card>
        
        <Card>
          <CardContent className="p-4">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm text-slate-600">Conformità</p>
                <p className="text-2xl font-bold text-green-600">
                  {stats.cavi_totali > 0 ? ((stats.cavi_conformi / stats.cavi_totali) * 100).toFixed(1) : 0}%
                </p>
              </div>
              <CheckCircle className="h-8 w-8 text-green-500" />
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Search */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Search className="h-5 w-5" />
            Ricerca Rapporti
          </CardTitle>
        </CardHeader>
        <CardContent>
          <Input
            placeholder="Cerca per numero rapporto, progetto o cliente..."
            value={searchTerm}
            onChange={(e) => setSearchTerm(e.target.value)}
          />
        </CardContent>
      </Card>

      {/* Rapporti Table */}
      <Card>
        <CardHeader>
          <CardTitle>Elenco Rapporti ({filteredRapporti.length})</CardTitle>
          <CardDescription>
            Gestione rapporti generali di collaudo
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="rounded-md border">
            <Table>
              <TableHeader>
                <TableRow>
                  <TableHead>Numero Rapporto</TableHead>
                  <TableHead>Progetto</TableHead>
                  <TableHead>Cliente</TableHead>
                  <TableHead>Data</TableHead>
                  <TableHead>Cavi</TableHead>
                  <TableHead>Conformità</TableHead>
                  <TableHead>Stato</TableHead>
                  <TableHead>Azioni</TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                {isLoading ? (
                  <TableRow>
                    <TableCell colSpan={8} className="text-center py-8">
                      <div className="flex items-center justify-center gap-2">
                        <Loader2 className="h-4 w-4 animate-spin" />
                        Caricamento...
                      </div>
                    </TableCell>
                  </TableRow>
                ) : filteredRapporti.length === 0 ? (
                  <TableRow>
                    <TableCell colSpan={8} className="text-center py-8 text-slate-500">
                      Nessun rapporto trovato
                    </TableCell>
                  </TableRow>
                ) : (
                  filteredRapporti.map((rapporto) => (
                    <TableRow key={rapporto.id_rapporto}>
                      <TableCell className="font-medium">{rapporto.numero_rapporto}</TableCell>
                      <TableCell>
                        <div>
                          <div className="font-medium">{rapporto.nome_progetto || '-'}</div>
                          <div className="text-sm text-slate-500">{rapporto.societa_installatrice || '-'}</div>
                        </div>
                      </TableCell>
                      <TableCell>{rapporto.cliente_finale || '-'}</TableCell>
                      <TableCell>
                        {new Date(rapporto.data_rapporto).toLocaleDateString('it-IT')}
                      </TableCell>
                      <TableCell>
                        <div className="text-center">
                          <div className="font-medium">{rapporto.numero_cavi_totali}</div>
                          <div className="text-xs text-slate-500">
                            {rapporto.numero_cavi_conformi}C / {rapporto.numero_cavi_non_conformi}NC
                          </div>
                        </div>
                      </TableCell>
                      <TableCell>{getConformitaBadge(rapporto)}</TableCell>
                      <TableCell>{getStatoBadge(rapporto.stato_rapporto)}</TableCell>
                      <TableCell>
                        <div className="flex gap-1">
                          <Button 
                            variant="ghost" 
                            size="sm"
                            onClick={() => handleEditRapporto(rapporto)}
                            title="Visualizza/Modifica"
                          >
                            <Edit className="h-4 w-4" />
                          </Button>
                          <Button 
                            variant="ghost" 
                            size="sm"
                            onClick={() => handleAggiornaStatistiche(rapporto.id_rapporto)}
                            title="Aggiorna Statistiche"
                          >
                            <RefreshCw className="h-4 w-4" />
                          </Button>
                          <Button 
                            variant="ghost" 
                            size="sm"
                            onClick={() => {}}
                            title="Genera PDF"
                          >
                            <Download className="h-4 w-4" />
                          </Button>
                          <Button 
                            variant="ghost" 
                            size="sm"
                            onClick={() => handleDeleteRapporto(rapporto.id_rapporto)}
                            title="Elimina"
                          >
                            <Trash2 className="h-4 w-4" />
                          </Button>
                        </div>
                      </TableCell>
                    </TableRow>
                  ))
                )}
              </TableBody>
            </Table>
          </div>
        </CardContent>
      </Card>

      {error && (
        <Alert className="border-red-200 bg-red-50">
          <AlertCircle className="h-4 w-4 text-red-600" />
          <AlertDescription className="text-red-800">{error}</AlertDescription>
        </Alert>
      )}
    </div>
  )
}
