{"ast": null, "code": "import buildLocalizeFn from \"../../../_lib/buildLocalizeFn/index.js\";\nvar eraValues = {\n  narrow: ['f.Kr.', 'e.Kr.'],\n  abbreviated: ['f.Kr.', 'e.Kr.'],\n  wide: ['f<PERSON>ir <PERSON>', 'eftir <PERSON>']\n};\nvar quarterValues = {\n  narrow: ['1', '2', '3', '4'],\n  abbreviated: ['1F', '2F', '3F', '4F'],\n  wide: ['1. fjórðungur', '2. fjórðungur', '3. fjórðungur', '4. fjórðungur']\n};\nvar monthValues = {\n  narrow: ['J', 'F', 'M', 'A', 'M', 'J', 'J', 'Á', 'S', 'Ó', 'N', 'D'],\n  abbreviated: ['jan.', 'feb.', 'mars', 'apríl', 'maí', 'jún<PERSON>', 'júlí', 'ágúst', 'sept.', 'okt.', 'nóv.', 'des.'],\n  wide: ['jan<PERSON>ar', 'febrúar', 'mars', 'apríl', 'maí', 'j<PERSON>í', 'júlí', 'ágúst', 'september', 'október', 'nóvember', 'desember']\n};\nvar dayValues = {\n  narrow: ['S', 'M', 'Þ', 'M', 'F', 'F', 'L'],\n  short: ['Su', 'Má', 'Þr', 'Mi', 'Fi', 'Fö', 'La'],\n  abbreviated: ['sun.', 'mán.', 'þri.', 'mið.', 'fim.', 'fös.', 'lau.'],\n  wide: ['sunnudagur', 'mánudagur', 'þriðjudagur', 'miðvikudagur', 'fimmtudagur', 'föstudagur', 'laugardagur']\n};\nvar dayPeriodValues = {\n  narrow: {\n    am: 'f',\n    pm: 'e',\n    midnight: 'miðnætti',\n    noon: 'hádegi',\n    morning: 'morgunn',\n    afternoon: 'síðdegi',\n    evening: 'kvöld',\n    night: 'nótt'\n  },\n  abbreviated: {\n    am: 'f.h.',\n    pm: 'e.h.',\n    midnight: 'miðnætti',\n    noon: 'hádegi',\n    morning: 'morgunn',\n    afternoon: 'síðdegi',\n    evening: 'kvöld',\n    night: 'nótt'\n  },\n  wide: {\n    am: 'fyrir hádegi',\n    pm: 'eftir hádegi',\n    midnight: 'miðnætti',\n    noon: 'hádegi',\n    morning: 'morgunn',\n    afternoon: 'síðdegi',\n    evening: 'kvöld',\n    night: 'nótt'\n  }\n};\nvar formattingDayPeriodValues = {\n  narrow: {\n    am: 'f',\n    pm: 'e',\n    midnight: 'á miðnætti',\n    noon: 'á hádegi',\n    morning: 'að morgni',\n    afternoon: 'síðdegis',\n    evening: 'um kvöld',\n    night: 'um nótt'\n  },\n  abbreviated: {\n    am: 'f.h.',\n    pm: 'e.h.',\n    midnight: 'á miðnætti',\n    noon: 'á hádegi',\n    morning: 'að morgni',\n    afternoon: 'síðdegis',\n    evening: 'um kvöld',\n    night: 'um nótt'\n  },\n  wide: {\n    am: 'fyrir hádegi',\n    pm: 'eftir hádegi',\n    midnight: 'á miðnætti',\n    noon: 'á hádegi',\n    morning: 'að morgni',\n    afternoon: 'síðdegis',\n    evening: 'um kvöld',\n    night: 'um nótt'\n  }\n};\nvar ordinalNumber = function ordinalNumber(dirtyNumber, _options) {\n  var number = Number(dirtyNumber);\n  return number + '.';\n};\nvar localize = {\n  ordinalNumber: ordinalNumber,\n  era: buildLocalizeFn({\n    values: eraValues,\n    defaultWidth: 'wide'\n  }),\n  quarter: buildLocalizeFn({\n    values: quarterValues,\n    defaultWidth: 'wide',\n    argumentCallback: function argumentCallback(quarter) {\n      return quarter - 1;\n    }\n  }),\n  month: buildLocalizeFn({\n    values: monthValues,\n    defaultWidth: 'wide'\n  }),\n  day: buildLocalizeFn({\n    values: dayValues,\n    defaultWidth: 'wide'\n  }),\n  dayPeriod: buildLocalizeFn({\n    values: dayPeriodValues,\n    defaultWidth: 'wide',\n    formattingValues: formattingDayPeriodValues,\n    defaultFormattingWidth: 'wide'\n  })\n};\nexport default localize;", "map": {"version": 3, "names": ["buildLocalizeFn", "<PERSON><PERSON><PERSON><PERSON>", "narrow", "abbreviated", "wide", "quarterValues", "month<PERSON><PERSON><PERSON>", "dayV<PERSON><PERSON>", "short", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "am", "pm", "midnight", "noon", "morning", "afternoon", "evening", "night", "formattingDayPeriodValues", "ordinalNumber", "dirtyNumber", "_options", "number", "Number", "localize", "era", "values", "defaultWidth", "quarter", "argument<PERSON>allback", "month", "day", "<PERSON><PERSON><PERSON><PERSON>", "formattingValues", "defaultFormattingWidth"], "sources": ["C:/CMS/webapp/frontend/node_modules/date-fns/esm/locale/is/_lib/localize/index.js"], "sourcesContent": ["import buildLocalizeFn from \"../../../_lib/buildLocalizeFn/index.js\";\nvar eraValues = {\n  narrow: ['f.Kr.', 'e.Kr.'],\n  abbreviated: ['f.Kr.', 'e.Kr.'],\n  wide: ['f<PERSON>ir <PERSON>', 'eftir <PERSON>']\n};\nvar quarterValues = {\n  narrow: ['1', '2', '3', '4'],\n  abbreviated: ['1F', '2F', '3F', '4F'],\n  wide: ['1. fjórðungur', '2. fjórðungur', '3. fjórðungur', '4. fjórðungur']\n};\nvar monthValues = {\n  narrow: ['J', 'F', 'M', 'A', 'M', 'J', 'J', 'Á', 'S', 'Ó', 'N', 'D'],\n  abbreviated: ['jan.', 'feb.', 'mars', 'apríl', 'maí', 'jún<PERSON>', 'júlí', 'ágúst', 'sept.', 'okt.', 'nóv.', 'des.'],\n  wide: ['jan<PERSON>ar', 'febrúar', 'mars', 'apríl', 'maí', 'j<PERSON>í', 'júlí', 'ágúst', 'september', 'október', 'nóvember', 'desember']\n};\nvar dayValues = {\n  narrow: ['S', 'M', 'Þ', 'M', 'F', 'F', 'L'],\n  short: ['Su', 'Má', 'Þr', 'Mi', 'Fi', 'Fö', 'La'],\n  abbreviated: ['sun.', 'mán.', 'þri.', 'mið.', 'fim.', 'fös.', 'lau.'],\n  wide: ['sunnudagur', 'mánudagur', 'þriðjudagur', 'miðvikudagur', 'fimmtudagur', 'föstudagur', 'laugardagur']\n};\nvar dayPeriodValues = {\n  narrow: {\n    am: 'f',\n    pm: 'e',\n    midnight: 'miðnætti',\n    noon: 'hádegi',\n    morning: 'morgunn',\n    afternoon: 'síðdegi',\n    evening: 'kvöld',\n    night: 'nótt'\n  },\n  abbreviated: {\n    am: 'f.h.',\n    pm: 'e.h.',\n    midnight: 'miðnætti',\n    noon: 'hádegi',\n    morning: 'morgunn',\n    afternoon: 'síðdegi',\n    evening: 'kvöld',\n    night: 'nótt'\n  },\n  wide: {\n    am: 'fyrir hádegi',\n    pm: 'eftir hádegi',\n    midnight: 'miðnætti',\n    noon: 'hádegi',\n    morning: 'morgunn',\n    afternoon: 'síðdegi',\n    evening: 'kvöld',\n    night: 'nótt'\n  }\n};\nvar formattingDayPeriodValues = {\n  narrow: {\n    am: 'f',\n    pm: 'e',\n    midnight: 'á miðnætti',\n    noon: 'á hádegi',\n    morning: 'að morgni',\n    afternoon: 'síðdegis',\n    evening: 'um kvöld',\n    night: 'um nótt'\n  },\n  abbreviated: {\n    am: 'f.h.',\n    pm: 'e.h.',\n    midnight: 'á miðnætti',\n    noon: 'á hádegi',\n    morning: 'að morgni',\n    afternoon: 'síðdegis',\n    evening: 'um kvöld',\n    night: 'um nótt'\n  },\n  wide: {\n    am: 'fyrir hádegi',\n    pm: 'eftir hádegi',\n    midnight: 'á miðnætti',\n    noon: 'á hádegi',\n    morning: 'að morgni',\n    afternoon: 'síðdegis',\n    evening: 'um kvöld',\n    night: 'um nótt'\n  }\n};\nvar ordinalNumber = function ordinalNumber(dirtyNumber, _options) {\n  var number = Number(dirtyNumber);\n  return number + '.';\n};\nvar localize = {\n  ordinalNumber: ordinalNumber,\n  era: buildLocalizeFn({\n    values: eraValues,\n    defaultWidth: 'wide'\n  }),\n  quarter: buildLocalizeFn({\n    values: quarterValues,\n    defaultWidth: 'wide',\n    argumentCallback: function argumentCallback(quarter) {\n      return quarter - 1;\n    }\n  }),\n  month: buildLocalizeFn({\n    values: monthValues,\n    defaultWidth: 'wide'\n  }),\n  day: buildLocalizeFn({\n    values: dayValues,\n    defaultWidth: 'wide'\n  }),\n  dayPeriod: buildLocalizeFn({\n    values: dayPeriodValues,\n    defaultWidth: 'wide',\n    formattingValues: formattingDayPeriodValues,\n    defaultFormattingWidth: 'wide'\n  })\n};\nexport default localize;"], "mappings": "AAAA,OAAOA,eAAe,MAAM,wCAAwC;AACpE,IAAIC,SAAS,GAAG;EACdC,MAAM,EAAE,CAAC,OAAO,EAAE,OAAO,CAAC;EAC1BC,WAAW,EAAE,CAAC,OAAO,EAAE,OAAO,CAAC;EAC/BC,IAAI,EAAE,CAAC,aAAa,EAAE,aAAa;AACrC,CAAC;AACD,IAAIC,aAAa,GAAG;EAClBH,MAAM,EAAE,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,CAAC;EAC5BC,WAAW,EAAE,CAAC,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,CAAC;EACrCC,IAAI,EAAE,CAAC,eAAe,EAAE,eAAe,EAAE,eAAe,EAAE,eAAe;AAC3E,CAAC;AACD,IAAIE,WAAW,GAAG;EAChBJ,MAAM,EAAE,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,CAAC;EACpEC,WAAW,EAAE,CAAC,MAAM,EAAE,MAAM,EAAE,MAAM,EAAE,OAAO,EAAE,KAAK,EAAE,MAAM,EAAE,MAAM,EAAE,OAAO,EAAE,OAAO,EAAE,MAAM,EAAE,MAAM,EAAE,MAAM,CAAC;EAC/GC,IAAI,EAAE,CAAC,QAAQ,EAAE,SAAS,EAAE,MAAM,EAAE,OAAO,EAAE,KAAK,EAAE,MAAM,EAAE,MAAM,EAAE,OAAO,EAAE,WAAW,EAAE,SAAS,EAAE,UAAU,EAAE,UAAU;AAC7H,CAAC;AACD,IAAIG,SAAS,GAAG;EACdL,MAAM,EAAE,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,CAAC;EAC3CM,KAAK,EAAE,CAAC,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,CAAC;EACjDL,WAAW,EAAE,CAAC,MAAM,EAAE,MAAM,EAAE,MAAM,EAAE,MAAM,EAAE,MAAM,EAAE,MAAM,EAAE,MAAM,CAAC;EACrEC,IAAI,EAAE,CAAC,YAAY,EAAE,WAAW,EAAE,aAAa,EAAE,cAAc,EAAE,aAAa,EAAE,YAAY,EAAE,aAAa;AAC7G,CAAC;AACD,IAAIK,eAAe,GAAG;EACpBP,MAAM,EAAE;IACNQ,EAAE,EAAE,GAAG;IACPC,EAAE,EAAE,GAAG;IACPC,QAAQ,EAAE,UAAU;IACpBC,IAAI,EAAE,QAAQ;IACdC,OAAO,EAAE,SAAS;IAClBC,SAAS,EAAE,SAAS;IACpBC,OAAO,EAAE,OAAO;IAChBC,KAAK,EAAE;EACT,CAAC;EACDd,WAAW,EAAE;IACXO,EAAE,EAAE,MAAM;IACVC,EAAE,EAAE,MAAM;IACVC,QAAQ,EAAE,UAAU;IACpBC,IAAI,EAAE,QAAQ;IACdC,OAAO,EAAE,SAAS;IAClBC,SAAS,EAAE,SAAS;IACpBC,OAAO,EAAE,OAAO;IAChBC,KAAK,EAAE;EACT,CAAC;EACDb,IAAI,EAAE;IACJM,EAAE,EAAE,cAAc;IAClBC,EAAE,EAAE,cAAc;IAClBC,QAAQ,EAAE,UAAU;IACpBC,IAAI,EAAE,QAAQ;IACdC,OAAO,EAAE,SAAS;IAClBC,SAAS,EAAE,SAAS;IACpBC,OAAO,EAAE,OAAO;IAChBC,KAAK,EAAE;EACT;AACF,CAAC;AACD,IAAIC,yBAAyB,GAAG;EAC9BhB,MAAM,EAAE;IACNQ,EAAE,EAAE,GAAG;IACPC,EAAE,EAAE,GAAG;IACPC,QAAQ,EAAE,YAAY;IACtBC,IAAI,EAAE,UAAU;IAChBC,OAAO,EAAE,WAAW;IACpBC,SAAS,EAAE,UAAU;IACrBC,OAAO,EAAE,UAAU;IACnBC,KAAK,EAAE;EACT,CAAC;EACDd,WAAW,EAAE;IACXO,EAAE,EAAE,MAAM;IACVC,EAAE,EAAE,MAAM;IACVC,QAAQ,EAAE,YAAY;IACtBC,IAAI,EAAE,UAAU;IAChBC,OAAO,EAAE,WAAW;IACpBC,SAAS,EAAE,UAAU;IACrBC,OAAO,EAAE,UAAU;IACnBC,KAAK,EAAE;EACT,CAAC;EACDb,IAAI,EAAE;IACJM,EAAE,EAAE,cAAc;IAClBC,EAAE,EAAE,cAAc;IAClBC,QAAQ,EAAE,YAAY;IACtBC,IAAI,EAAE,UAAU;IAChBC,OAAO,EAAE,WAAW;IACpBC,SAAS,EAAE,UAAU;IACrBC,OAAO,EAAE,UAAU;IACnBC,KAAK,EAAE;EACT;AACF,CAAC;AACD,IAAIE,aAAa,GAAG,SAASA,aAAaA,CAACC,WAAW,EAAEC,QAAQ,EAAE;EAChE,IAAIC,MAAM,GAAGC,MAAM,CAACH,WAAW,CAAC;EAChC,OAAOE,MAAM,GAAG,GAAG;AACrB,CAAC;AACD,IAAIE,QAAQ,GAAG;EACbL,aAAa,EAAEA,aAAa;EAC5BM,GAAG,EAAEzB,eAAe,CAAC;IACnB0B,MAAM,EAAEzB,SAAS;IACjB0B,YAAY,EAAE;EAChB,CAAC,CAAC;EACFC,OAAO,EAAE5B,eAAe,CAAC;IACvB0B,MAAM,EAAErB,aAAa;IACrBsB,YAAY,EAAE,MAAM;IACpBE,gBAAgB,EAAE,SAASA,gBAAgBA,CAACD,OAAO,EAAE;MACnD,OAAOA,OAAO,GAAG,CAAC;IACpB;EACF,CAAC,CAAC;EACFE,KAAK,EAAE9B,eAAe,CAAC;IACrB0B,MAAM,EAAEpB,WAAW;IACnBqB,YAAY,EAAE;EAChB,CAAC,CAAC;EACFI,GAAG,EAAE/B,eAAe,CAAC;IACnB0B,MAAM,EAAEnB,SAAS;IACjBoB,YAAY,EAAE;EAChB,CAAC,CAAC;EACFK,SAAS,EAAEhC,eAAe,CAAC;IACzB0B,MAAM,EAAEjB,eAAe;IACvBkB,YAAY,EAAE,MAAM;IACpBM,gBAAgB,EAAEf,yBAAyB;IAC3CgB,sBAAsB,EAAE;EAC1B,CAAC;AACH,CAAC;AACD,eAAeV,QAAQ", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}