{"ast": null, "code": "const accusativeWeekdays = [\"vas<PERSON>rna<PERSON>\", \"hétfőn\", \"kedden\", \"szerdán\", \"csü<PERSON><PERSON>rtökön\", \"pénteken\", \"szombaton\"];\nfunction week(isFuture) {\n  return date => {\n    const weekday = accusativeWeekdays[date.getDay()];\n    const prefix = isFuture ? \"\" : \"'múlt' \";\n    return `${prefix}'${weekday}' p'-kor'`;\n  };\n}\nconst formatRelativeLocale = {\n  lastWeek: week(false),\n  yesterday: \"'tegnap' p'-kor'\",\n  today: \"'ma' p'-kor'\",\n  tomorrow: \"'holnap' p'-kor'\",\n  nextWeek: week(true),\n  other: \"P\"\n};\nexport const formatRelative = (token, date) => {\n  const format = formatRelativeLocale[token];\n  if (typeof format === \"function\") {\n    return format(date);\n  }\n  return format;\n};", "map": {"version": 3, "names": ["accusativeWeekdays", "week", "isFuture", "date", "weekday", "getDay", "prefix", "formatRelativeLocale", "lastWeek", "yesterday", "today", "tomorrow", "nextWeek", "other", "formatRelative", "token", "format"], "sources": ["C:/Users/<USER>/PycharmProjects/CMS/webapp/frontend/node_modules/date-fns/locale/hu/_lib/formatRelative.js"], "sourcesContent": ["const accusativeWeekdays = [\n  \"vas<PERSON>rna<PERSON>\",\n  \"hétfőn\",\n  \"kedden\",\n  \"szerdán\",\n  \"csü<PERSON><PERSON>rtökön\",\n  \"pénteken\",\n  \"szombaton\",\n];\n\nfunction week(isFuture) {\n  return (date) => {\n    const weekday = accusativeWeekdays[date.getDay()];\n    const prefix = isFuture ? \"\" : \"'múlt' \";\n    return `${prefix}'${weekday}' p'-kor'`;\n  };\n}\nconst formatRelativeLocale = {\n  lastWeek: week(false),\n  yesterday: \"'tegnap' p'-kor'\",\n  today: \"'ma' p'-kor'\",\n  tomorrow: \"'holnap' p'-kor'\",\n  nextWeek: week(true),\n  other: \"P\",\n};\n\nexport const formatRelative = (token, date) => {\n  const format = formatRelativeLocale[token];\n\n  if (typeof format === \"function\") {\n    return format(date);\n  }\n\n  return format;\n};\n"], "mappings": "AAAA,MAAMA,kBAAkB,GAAG,CACzB,UAAU,EACV,QAAQ,EACR,QAAQ,EACR,SAAS,EACT,aAAa,EACb,UAAU,EACV,WAAW,CACZ;AAED,SAASC,IAAIA,CAACC,QAAQ,EAAE;EACtB,OAAQC,IAAI,IAAK;IACf,MAAMC,OAAO,GAAGJ,kBAAkB,CAACG,IAAI,CAACE,MAAM,CAAC,CAAC,CAAC;IACjD,MAAMC,MAAM,GAAGJ,QAAQ,GAAG,EAAE,GAAG,SAAS;IACxC,OAAO,GAAGI,MAAM,IAAIF,OAAO,WAAW;EACxC,CAAC;AACH;AACA,MAAMG,oBAAoB,GAAG;EAC3BC,QAAQ,EAAEP,IAAI,CAAC,KAAK,CAAC;EACrBQ,SAAS,EAAE,kBAAkB;EAC7BC,KAAK,EAAE,cAAc;EACrBC,QAAQ,EAAE,kBAAkB;EAC5BC,QAAQ,EAAEX,IAAI,CAAC,IAAI,CAAC;EACpBY,KAAK,EAAE;AACT,CAAC;AAED,OAAO,MAAMC,cAAc,GAAGA,CAACC,KAAK,EAAEZ,IAAI,KAAK;EAC7C,MAAMa,MAAM,GAAGT,oBAAoB,CAACQ,KAAK,CAAC;EAE1C,IAAI,OAAOC,MAAM,KAAK,UAAU,EAAE;IAChC,OAAOA,MAAM,CAACb,IAAI,CAAC;EACrB;EAEA,OAAOa,MAAM;AACf,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}