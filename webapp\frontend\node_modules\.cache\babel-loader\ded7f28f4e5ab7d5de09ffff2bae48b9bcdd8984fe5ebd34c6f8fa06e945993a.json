{"ast": null, "code": "import { toDate } from \"./toDate.js\";\n\n/**\n * The {@link fromUnixTime} function options.\n */\n\n/**\n * @name fromUnixTime\n * @category Timestamp Helpers\n * @summary Create a date from a Unix timestamp.\n *\n * @description\n * Create a date from a Unix timestamp (in seconds). Decimal values will be discarded.\n *\n * @param unixTime - The given Unix timestamp (in seconds)\n * @param options - An object with options. Allows to pass a context.\n *\n * @typeParam DateType - The `Date` type, the function operates on. Gets inferred from passed arguments. Allows to use extensions like [`UTCDate`](https://github.com/date-fns/utc).\n *\n * @returns The date\n *\n * @example\n * // Create the date 29 February 2012 11:45:05:\n * const result = fromUnixTime(1330515905)\n * //=> Wed Feb 29 2012 11:45:05\n */\nexport function fromUnixTime(unixTime, options) {\n  return toDate(unixTime * 1000, options?.in);\n}\n\n// Fallback for modularized imports:\nexport default fromUnixTime;", "map": {"version": 3, "names": ["toDate", "fromUnixTime", "unixTime", "options", "in"], "sources": ["C:/Users/<USER>/PycharmProjects/CMS/webapp/frontend/node_modules/date-fns/fromUnixTime.js"], "sourcesContent": ["import { toDate } from \"./toDate.js\";\n\n/**\n * The {@link fromUnixTime} function options.\n */\n\n/**\n * @name fromUnixTime\n * @category Timestamp Helpers\n * @summary Create a date from a Unix timestamp.\n *\n * @description\n * Create a date from a Unix timestamp (in seconds). Decimal values will be discarded.\n *\n * @param unixTime - The given Unix timestamp (in seconds)\n * @param options - An object with options. Allows to pass a context.\n *\n * @typeParam DateType - The `Date` type, the function operates on. Gets inferred from passed arguments. Allows to use extensions like [`UTCDate`](https://github.com/date-fns/utc).\n *\n * @returns The date\n *\n * @example\n * // Create the date 29 February 2012 11:45:05:\n * const result = fromUnixTime(1330515905)\n * //=> Wed Feb 29 2012 11:45:05\n */\nexport function fromUnixTime(unixTime, options) {\n  return toDate(unixTime * 1000, options?.in);\n}\n\n// Fallback for modularized imports:\nexport default fromUnixTime;\n"], "mappings": "AAAA,SAASA,MAAM,QAAQ,aAAa;;AAEpC;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO,SAASC,YAAYA,CAACC,QAAQ,EAAEC,OAAO,EAAE;EAC9C,OAAOH,MAAM,CAACE,QAAQ,GAAG,IAAI,EAAEC,OAAO,EAAEC,EAAE,CAAC;AAC7C;;AAEA;AACA,eAAeH,YAAY", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}