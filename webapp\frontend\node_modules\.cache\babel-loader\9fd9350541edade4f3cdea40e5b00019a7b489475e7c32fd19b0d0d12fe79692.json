{"ast": null, "code": "import toDate from \"../../toDate/index.js\";\nimport requiredArgs from \"../requiredArgs/index.js\";\nexport default function startOfUTCISOWeek(dirtyDate) {\n  requiredArgs(1, arguments);\n  var weekStartsOn = 1;\n  var date = toDate(dirtyDate);\n  var day = date.getUTCDay();\n  var diff = (day < weekStartsOn ? 7 : 0) + day - weekStartsOn;\n  date.setUTCDate(date.getUTCDate() - diff);\n  date.setUTCHours(0, 0, 0, 0);\n  return date;\n}", "map": {"version": 3, "names": ["toDate", "requiredArgs", "startOfUTCISOWeek", "dirtyDate", "arguments", "weekStartsOn", "date", "day", "getUTCDay", "diff", "setUTCDate", "getUTCDate", "setUTCHours"], "sources": ["C:/CMS/webapp/frontend/node_modules/date-fns/esm/_lib/startOfUTCISOWeek/index.js"], "sourcesContent": ["import toDate from \"../../toDate/index.js\";\nimport requiredArgs from \"../requiredArgs/index.js\";\nexport default function startOfUTCISOWeek(dirtyDate) {\n  requiredArgs(1, arguments);\n  var weekStartsOn = 1;\n  var date = toDate(dirtyDate);\n  var day = date.getUTCDay();\n  var diff = (day < weekStartsOn ? 7 : 0) + day - weekStartsOn;\n  date.setUTCDate(date.getUTCDate() - diff);\n  date.setUTCHours(0, 0, 0, 0);\n  return date;\n}"], "mappings": "AAAA,OAAOA,MAAM,MAAM,uBAAuB;AAC1C,OAAOC,YAAY,MAAM,0BAA0B;AACnD,eAAe,SAASC,iBAAiBA,CAACC,SAAS,EAAE;EACnDF,YAAY,CAAC,CAAC,EAAEG,SAAS,CAAC;EAC1B,IAAIC,YAAY,GAAG,CAAC;EACpB,IAAIC,IAAI,GAAGN,MAAM,CAACG,SAAS,CAAC;EAC5B,IAAII,GAAG,GAAGD,IAAI,CAACE,SAAS,CAAC,CAAC;EAC1B,IAAIC,IAAI,GAAG,CAACF,GAAG,GAAGF,YAAY,GAAG,CAAC,GAAG,CAAC,IAAIE,GAAG,GAAGF,YAAY;EAC5DC,IAAI,CAACI,UAAU,CAACJ,IAAI,CAACK,UAAU,CAAC,CAAC,GAAGF,IAAI,CAAC;EACzCH,IAAI,CAACM,WAAW,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC;EAC5B,OAAON,IAAI;AACb", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}