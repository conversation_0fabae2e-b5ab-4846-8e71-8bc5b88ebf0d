{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\PycharmProjects\\\\CMS\\\\webapp\\\\frontend\\\\src\\\\components\\\\cavi\\\\PosaCaviCollegamenti.js\",\n  _s = $RefreshSig$();\nimport React, { useState } from 'react';\nimport { Box, Typography, Button, Paper, Divider, List, ListItem, ListItemText, ListItemIcon, ListItemButton, Dialog, DialogTitle, DialogContent, DialogContentText, DialogActions, TextField, FormControl, InputLabel, Select, MenuItem, Grid, Alert, CircularProgress, FormHelperText, Radio, RadioGroup, FormControlLabel, FormLabel } from '@mui/material';\nimport { Add as AddIcon, Edit as EditIcon, Delete as DeleteIcon, Cable as CableIcon, Save as SaveIcon, Warning as WarningIcon } from '@mui/icons-material';\nimport caviService from '../../services/caviService';\nimport { validateCavoData, validateField, isEmpty } from '../../utils/validationUtils';\nimport { jsxDEV as _jsxDEV, Fragment as _Fragment } from \"react/jsx-dev-runtime\";\nconst PosaCaviCollegamenti = ({\n  cantiereId,\n  onSuccess,\n  onError,\n  initialOption = null\n}) => {\n  _s();\n  const [loading, setLoading] = useState(false);\n  const [selectedOption, setSelectedOption] = useState(null);\n  const [openDialog, setOpenDialog] = useState(false);\n  const [dialogType, setDialogType] = useState('');\n  const [selectedCavo, setSelectedCavo] = useState(null);\n  const [formData, setFormData] = useState({\n    id_cavo: '',\n    metri_posati: '',\n    id_bobina: ''\n  });\n  const [cavoIdInput, setCavoIdInput] = useState('');\n  const [formErrors, setFormErrors] = useState({});\n  const [formWarnings, setFormWarnings] = useState({});\n  const [cavi, setCavi] = useState([]);\n  const [caviLoading, setCaviLoading] = useState(false);\n  const [deleteMode, setDeleteMode] = useState('spare'); // 'spare' o 'delete'\n\n  // Inizializza il componente con l'opzione iniziale se specificata\n  React.useEffect(() => {\n    if (initialOption) {\n      // Imposta direttamente le opzioni invece di chiamare handleOptionSelect\n      // per evitare dipendenze circolari\n      setSelectedOption(initialOption);\n      if (initialOption === 'eliminaCavo') {\n        loadCavi('eliminaCavo');\n        setDialogType('eliminaCavo');\n        setOpenDialog(true);\n      } else if (initialOption === 'modificaCavo') {\n        loadCavi('modificaCavo');\n        setDialogType('selezionaCavo');\n        setOpenDialog(true);\n      }\n    }\n    // eslint-disable-next-line react-hooks/exhaustive-deps\n  }, [initialOption]);\n\n  // Carica i cavi attivi per la selezione\n  const loadCavi = async operationType => {\n    try {\n      setCaviLoading(true);\n      const caviData = await caviService.getCavi(cantiereId, 0);\n\n      // Filtra i cavi in base al tipo di operazione\n      if (operationType === 'modificaCavo') {\n        // Per modifica cavo, mostra solo i cavi non posati (metratura_reale = 0 e stato != Installato)\n        const caviNonPosati = caviData.filter(cavo => parseFloat(cavo.metratura_reale) === 0 && cavo.stato_installazione !== 'Installato');\n        setCavi(caviNonPosati);\n      } else {\n        // Per altre operazioni, mostra tutti i cavi\n        setCavi(caviData);\n      }\n    } catch (error) {\n      onError('Errore nel caricamento dei cavi');\n      console.error('Errore nel caricamento dei cavi:', error);\n    } finally {\n      setCaviLoading(false);\n    }\n  };\n\n  // Gestisce la selezione di un'opzione dal menu\n  const handleOptionSelect = option => {\n    setSelectedOption(option);\n    if (option === 'inserisciMetri' || option === 'modificaBobina') {\n      loadCavi(option);\n      setDialogType(option);\n      setOpenDialog(true);\n    } else if (option === 'aggiungiCavo') {\n      // Reindirizza alla pagina di aggiunta cavo\n      window.location.href = `/dashboard/cantieri/${cantiereId}/cavi/posa/aggiungi-cavo`;\n    } else if (option === 'modificaCavo') {\n      loadCavi('modificaCavo');\n      setDialogType('selezionaCavo');\n      setOpenDialog(true);\n    } else if (option === 'eliminaCavo') {\n      loadCavi('eliminaCavo');\n      setDialogType('eliminaCavo');\n      setOpenDialog(true);\n    } else if (option === 'collegamentoCavo') {\n      // Reindirizza alla pagina di gestione collegamenti\n      window.location.href = `/dashboard/cantieri/${cantiereId}/cavi/collegamenti`;\n    }\n  };\n\n  // Gestisce la chiusura del dialog\n  const handleCloseDialog = () => {\n    // Reset dello stato del componente\n    setOpenDialog(false);\n    setSelectedCavo(null);\n    setFormData({\n      id_cavo: '',\n      metri_posati: '',\n      id_bobina: ''\n    });\n    setCavoIdInput('');\n    setFormErrors({});\n    setFormWarnings({});\n    setDeleteMode('spare'); // Reset alla modalità predefinita\n\n    // Se è stato specificato un initialOption, notifica il genitore che il dialogo è stato chiuso\n    // ma senza messaggio di errore\n    if (initialOption && onSuccess) {\n      // Chiama onSuccess ma senza messaggio per evitare l'alert\n      onSuccess(null);\n    }\n  };\n\n  // Gestisce la selezione di un cavo\n  const handleCavoSelect = cavo => {\n    setSelectedCavo(cavo);\n    if (dialogType === 'inserisciMetri') {\n      setFormData({\n        ...formData,\n        id_cavo: cavo.id_cavo,\n        metri_posati: ''\n      });\n    } else if (dialogType === 'modificaBobina') {\n      setFormData({\n        ...formData,\n        id_cavo: cavo.id_cavo,\n        id_bobina: cavo.id_bobina || ''\n      });\n    } else if (dialogType === 'selezionaCavo') {\n      setDialogType('modificaCavo');\n      setFormData({\n        ...cavo,\n        metri_teorici: cavo.metri_teorici || '',\n        metratura_reale: cavo.metratura_reale || '0'\n      });\n    }\n  };\n\n  // Gestisce la ricerca di un cavo per ID\n  const handleSearchCavoById = async () => {\n    if (!cavoIdInput.trim()) {\n      onError('Inserisci un ID cavo valido');\n      return;\n    }\n    try {\n      setCaviLoading(true);\n      const caviData = await caviService.getCavi(cantiereId, 0);\n      const cavo = caviData.find(c => c.id_cavo === cavoIdInput.trim());\n      if (!cavo) {\n        onError(`Cavo con ID ${cavoIdInput} non trovato`);\n        return;\n      }\n\n      // Verifica se stiamo cercando un cavo per modificarlo\n      if (dialogType === 'selezionaCavo') {\n        // Verifica che il cavo non sia già posato\n        if (parseFloat(cavo.metratura_reale) > 0 || cavo.stato_installazione === 'Installato') {\n          onError(`Il cavo ${cavo.id_cavo} risulta già posato. Utilizza l'opzione \"Modifica bobina cavo posato\" per modificarlo.`);\n          return;\n        }\n      }\n\n      // Seleziona il cavo trovato\n      handleCavoSelect(cavo);\n    } catch (error) {\n      onError('Errore nel caricamento dei cavi');\n      console.error('Errore nel caricamento dei cavi:', error);\n    } finally {\n      setCaviLoading(false);\n    }\n  };\n\n  // Gestisce il cambio dell'input dell'ID cavo\n  const handleCavoIdInputChange = e => {\n    setCavoIdInput(e.target.value);\n  };\n\n  // Gestisce il cambio dei valori nel form con validazione\n  const handleFormChange = e => {\n    const {\n      name,\n      value\n    } = e.target;\n\n    // Aggiorna il valore nel form\n    setFormData({\n      ...formData,\n      [name]: value\n    });\n\n    // Valida il campo\n    if (dialogType === 'modificaCavo') {\n      const additionalParams = {};\n      if (name === 'metratura_reale') {\n        additionalParams.metriTeorici = parseFloat(formData.metri_teorici || 0);\n      }\n      const result = validateField(name, value, additionalParams);\n\n      // Aggiorna gli errori\n      setFormErrors(prev => ({\n        ...prev,\n        [name]: !result.valid ? result.message : null\n      }));\n\n      // Aggiorna gli avvisi\n      setFormWarnings(prev => ({\n        ...prev,\n        [name]: result.warning ? result.message : null\n      }));\n    }\n  };\n\n  // Gestisce il salvataggio del form con validazione\n  const handleSave = async () => {\n    try {\n      setLoading(true);\n      if (dialogType === 'inserisciMetri') {\n        // Valida i metri posati\n        if (isEmpty(formData.metri_posati) || isNaN(parseFloat(formData.metri_posati))) {\n          setFormErrors({\n            metri_posati: 'Inserire un valore numerico valido'\n          });\n          setLoading(false);\n          return;\n        }\n        await caviService.updateMetriPosati(cantiereId, formData.id_cavo, parseFloat(formData.metri_posati));\n        onSuccess('Metri posati aggiornati con successo');\n      } else if (dialogType === 'modificaBobina') {\n        await caviService.updateBobina(cantiereId, formData.id_cavo, formData.id_bobina);\n        onSuccess('Bobina aggiornata con successo');\n      } else if (dialogType === 'modificaCavo') {\n        // Validazione completa dei dati del cavo\n        const validation = validateCavoData(formData);\n        if (!validation.isValid) {\n          setFormErrors(validation.errors);\n          setFormWarnings(validation.warnings);\n          setLoading(false);\n          return;\n        }\n\n        // Usa i dati validati\n        const validatedData = validation.validatedData;\n        await caviService.updateCavo(cantiereId, validatedData.id_cavo, validatedData);\n        onSuccess('Cavo modificato con successo');\n\n        // Mostra avvisi se presenti\n        if (Object.keys(validation.warnings).length > 0) {\n          const warningMessages = Object.values(validation.warnings).join('\\n');\n          console.warn('Avvisi durante il salvataggio:', warningMessages);\n        }\n      } else if (dialogType === 'eliminaCavo') {\n        // Verifica se il cavo è installato\n        const isInstalled = selectedCavo.stato_installazione === 'Installato' || selectedCavo.metratura_reale && selectedCavo.metratura_reale > 0;\n        if (isInstalled) {\n          // Se è installato, marca solo come SPARE\n          console.log('Marcando cavo installato come SPARE:', selectedCavo.id_cavo);\n          try {\n            // Prima prova con markCavoAsSpare\n            const result = await caviService.markCavoAsSpare(cantiereId, selectedCavo.id_cavo);\n            console.log('Risultato marcatura SPARE:', result);\n            console.log('Nuovo valore modificato_manualmente:', result.modificato_manualmente);\n            onSuccess(`Cavo ${selectedCavo.id_cavo} marcato come SPARE con successo`);\n          } catch (markError) {\n            console.error('Errore con markCavoAsSpare, tentativo con deleteCavo mode=spare:', markError);\n            // Se fallisce, prova con deleteCavo mode=spare\n            const result = await caviService.deleteCavo(cantiereId, selectedCavo.id_cavo, 'spare');\n            console.log('Risultato marcatura SPARE con deleteCavo:', result);\n            onSuccess(`Cavo ${selectedCavo.id_cavo} marcato come SPARE con successo`);\n          }\n        } else {\n          // Se non è installato, usa la modalità selezionata (SPARE o DELETE)\n          console.log('Eliminando cavo non installato con modalità:', deleteMode);\n          const result = await caviService.deleteCavo(cantiereId, selectedCavo.id_cavo, deleteMode);\n          console.log('Risultato eliminazione/marcatura:', result);\n          onSuccess(`Cavo ${selectedCavo.id_cavo} ${deleteMode === 'spare' ? 'marcato come SPARE' : 'eliminato'} con successo`);\n        }\n      }\n      handleCloseDialog();\n    } catch (error) {\n      console.error('Errore durante l\\'operazione:', error);\n\n      // Gestione più dettagliata dell'errore\n      let errorMessage = 'Errore sconosciuto';\n      if (error.detail) {\n        // Errore dal backend con dettaglio\n        errorMessage = error.detail;\n      } else if (error.message) {\n        // Errore con messaggio\n        errorMessage = error.message;\n      } else if (typeof error === 'string') {\n        // Errore come stringa\n        errorMessage = error;\n      }\n      onError('Errore durante l\\'operazione: ' + errorMessage);\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  // Renderizza il dialog in base al tipo\n  const renderDialog = () => {\n    if (dialogType === 'inserisciMetri') {\n      return /*#__PURE__*/_jsxDEV(Dialog, {\n        open: openDialog,\n        onClose: handleCloseDialog,\n        maxWidth: \"md\",\n        fullWidth: true,\n        children: [/*#__PURE__*/_jsxDEV(DialogTitle, {\n          children: \"Inserisci Metri Posati\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 359,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(DialogContent, {\n          children: caviLoading ? /*#__PURE__*/_jsxDEV(CircularProgress, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 362,\n            columnNumber: 15\n          }, this) : cavi.length === 0 ? /*#__PURE__*/_jsxDEV(Alert, {\n            severity: \"info\",\n            children: \"Nessun cavo disponibile\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 364,\n            columnNumber: 15\n          }, this) : !selectedCavo ? /*#__PURE__*/_jsxDEV(Box, {\n            children: [/*#__PURE__*/_jsxDEV(Typography, {\n              variant: \"subtitle1\",\n              gutterBottom: true,\n              children: \"Seleziona un cavo:\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 367,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(List, {\n              children: cavi.map(cavo => /*#__PURE__*/_jsxDEV(ListItem, {\n                button: true,\n                onClick: () => handleCavoSelect(cavo),\n                children: /*#__PURE__*/_jsxDEV(ListItemText, {\n                  primary: cavo.id_cavo,\n                  secondary: `${cavo.tipologia || 'N/A'} - Da: ${cavo.ubicazione_partenza || 'N/A'} A: ${cavo.ubicazione_arrivo || 'N/A'}`\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 377,\n                  columnNumber: 23\n                }, this)\n              }, cavo.id_cavo, false, {\n                fileName: _jsxFileName,\n                lineNumber: 372,\n                columnNumber: 21\n              }, this))\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 370,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 366,\n            columnNumber: 15\n          }, this) : /*#__PURE__*/_jsxDEV(Box, {\n            sx: {\n              mt: 2\n            },\n            children: [/*#__PURE__*/_jsxDEV(Typography, {\n              variant: \"subtitle1\",\n              gutterBottom: true,\n              children: [\"Cavo selezionato: \", selectedCavo.id_cavo]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 387,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(Typography, {\n              variant: \"body2\",\n              gutterBottom: true,\n              children: [\"Metri teorici: \", selectedCavo.metri_teorici || 'N/A']\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 390,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(Typography, {\n              variant: \"body2\",\n              gutterBottom: true,\n              children: [\"Metratura attuale: \", selectedCavo.metratura_reale || '0']\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 393,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(TextField, {\n              margin: \"dense\",\n              name: \"metri_posati\",\n              label: \"Metri posati da aggiungere\",\n              type: \"number\",\n              fullWidth: true,\n              variant: \"outlined\",\n              value: formData.metri_posati,\n              onChange: handleFormChange,\n              required: true,\n              error: !!formErrors.metri_posati,\n              helperText: formErrors.metri_posati,\n              sx: {\n                mt: 2\n              }\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 396,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 386,\n            columnNumber: 15\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 360,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(DialogActions, {\n          children: [/*#__PURE__*/_jsxDEV(Button, {\n            onClick: handleCloseDialog,\n            children: \"Annulla\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 414,\n            columnNumber: 13\n          }, this), selectedCavo && /*#__PURE__*/_jsxDEV(Button, {\n            onClick: handleSave,\n            disabled: loading || !formData.metri_posati,\n            startIcon: loading ? /*#__PURE__*/_jsxDEV(CircularProgress, {\n              size: 20\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 419,\n              columnNumber: 38\n            }, this) : /*#__PURE__*/_jsxDEV(SaveIcon, {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 419,\n              columnNumber: 71\n            }, this),\n            children: \"Salva\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 416,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 413,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 358,\n        columnNumber: 9\n      }, this);\n    } else if (dialogType === 'modificaBobina') {\n      return /*#__PURE__*/_jsxDEV(Dialog, {\n        open: openDialog,\n        onClose: handleCloseDialog,\n        maxWidth: \"md\",\n        fullWidth: true,\n        children: [/*#__PURE__*/_jsxDEV(DialogTitle, {\n          children: \"Modifica Bobina Cavo Posato\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 430,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(DialogContent, {\n          children: caviLoading ? /*#__PURE__*/_jsxDEV(CircularProgress, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 433,\n            columnNumber: 15\n          }, this) : cavi.length === 0 ? /*#__PURE__*/_jsxDEV(Alert, {\n            severity: \"info\",\n            children: \"Nessun cavo disponibile\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 435,\n            columnNumber: 15\n          }, this) : !selectedCavo ? /*#__PURE__*/_jsxDEV(Box, {\n            children: [/*#__PURE__*/_jsxDEV(Typography, {\n              variant: \"subtitle1\",\n              gutterBottom: true,\n              children: \"Seleziona un cavo:\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 438,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(List, {\n              children: cavi.map(cavo => /*#__PURE__*/_jsxDEV(ListItem, {\n                button: true,\n                onClick: () => handleCavoSelect(cavo),\n                children: /*#__PURE__*/_jsxDEV(ListItemText, {\n                  primary: cavo.id_cavo,\n                  secondary: `Bobina attuale: ${cavo.id_bobina || 'Non assegnata'}`\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 448,\n                  columnNumber: 23\n                }, this)\n              }, cavo.id_cavo, false, {\n                fileName: _jsxFileName,\n                lineNumber: 443,\n                columnNumber: 21\n              }, this))\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 441,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 437,\n            columnNumber: 15\n          }, this) : /*#__PURE__*/_jsxDEV(Box, {\n            sx: {\n              mt: 2\n            },\n            children: [/*#__PURE__*/_jsxDEV(Typography, {\n              variant: \"subtitle1\",\n              gutterBottom: true,\n              children: [\"Cavo selezionato: \", selectedCavo.id_cavo]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 458,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(Typography, {\n              variant: \"body2\",\n              gutterBottom: true,\n              children: [\"Bobina attuale: \", selectedCavo.id_bobina || 'Non assegnata']\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 461,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(TextField, {\n              margin: \"dense\",\n              name: \"id_bobina\",\n              label: \"ID Bobina\",\n              fullWidth: true,\n              variant: \"outlined\",\n              value: formData.id_bobina,\n              onChange: handleFormChange,\n              sx: {\n                mt: 2\n              }\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 464,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 457,\n            columnNumber: 15\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 431,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(DialogActions, {\n          children: [/*#__PURE__*/_jsxDEV(Button, {\n            onClick: handleCloseDialog,\n            children: \"Annulla\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 478,\n            columnNumber: 13\n          }, this), selectedCavo && /*#__PURE__*/_jsxDEV(Button, {\n            onClick: handleSave,\n            disabled: loading,\n            startIcon: loading ? /*#__PURE__*/_jsxDEV(CircularProgress, {\n              size: 20\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 483,\n              columnNumber: 38\n            }, this) : /*#__PURE__*/_jsxDEV(SaveIcon, {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 483,\n              columnNumber: 71\n            }, this),\n            children: \"Salva\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 480,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 477,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 429,\n        columnNumber: 9\n      }, this);\n    } else if (dialogType === 'selezionaCavo') {\n      return /*#__PURE__*/_jsxDEV(Dialog, {\n        open: openDialog,\n        onClose: handleCloseDialog,\n        maxWidth: \"md\",\n        fullWidth: true,\n        children: [/*#__PURE__*/_jsxDEV(DialogTitle, {\n          children: \"Modifica Cavo\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 494,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(DialogContent, {\n          children: [/*#__PURE__*/_jsxDEV(Alert, {\n            severity: \"info\",\n            sx: {\n              mb: 2\n            },\n            children: \"Puoi modificare solo i cavi non ancora posati (metratura = 0 e stato diverso da \\\"Installato\\\"). Per modificare cavi gi\\xE0 posati, utilizzare l'opzione \\\"Modifica bobina cavo posato\\\".\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 496,\n            columnNumber: 13\n          }, this), caviLoading ? /*#__PURE__*/_jsxDEV(CircularProgress, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 502,\n            columnNumber: 15\n          }, this) : !selectedCavo ? /*#__PURE__*/_jsxDEV(Box, {\n            sx: {\n              p: 2\n            },\n            children: [/*#__PURE__*/_jsxDEV(Typography, {\n              variant: \"subtitle1\",\n              gutterBottom: true,\n              children: \"Inserisci l'ID del cavo da modificare:\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 505,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(Box, {\n              sx: {\n                display: 'flex',\n                alignItems: 'center',\n                mt: 2\n              },\n              children: [/*#__PURE__*/_jsxDEV(TextField, {\n                fullWidth: true,\n                label: \"ID Cavo\",\n                variant: \"outlined\",\n                value: cavoIdInput,\n                onChange: handleCavoIdInputChange,\n                placeholder: \"Inserisci l'ID del cavo\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 509,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(Button, {\n                variant: \"contained\",\n                color: \"primary\",\n                onClick: handleSearchCavoById,\n                disabled: caviLoading || !cavoIdInput.trim(),\n                sx: {\n                  ml: 2,\n                  minWidth: '120px'\n                },\n                children: caviLoading ? /*#__PURE__*/_jsxDEV(CircularProgress, {\n                  size: 24\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 524,\n                  columnNumber: 36\n                }, this) : \"Cerca\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 517,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 508,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 504,\n            columnNumber: 15\n          }, this) : null]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 495,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(DialogActions, {\n          children: /*#__PURE__*/_jsxDEV(Button, {\n            onClick: handleCloseDialog,\n            children: \"Annulla\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 531,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 530,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 493,\n        columnNumber: 9\n      }, this);\n    } else if (dialogType === 'eliminaCavo') {\n      // Verifica se il cavo selezionato è installato\n      const isInstalled = selectedCavo && (selectedCavo.stato_installazione === 'Installato' || selectedCavo.metratura_reale && selectedCavo.metratura_reale > 0);\n      return /*#__PURE__*/_jsxDEV(Dialog, {\n        open: openDialog,\n        onClose: handleCloseDialog,\n        maxWidth: \"md\",\n        fullWidth: true,\n        children: [/*#__PURE__*/_jsxDEV(DialogTitle, {\n          children: !selectedCavo ? 'Elimina Cavo' : isInstalled ? 'Marca Cavo come SPARE' : 'Elimina Cavo'\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 541,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(DialogContent, {\n          children: caviLoading ? /*#__PURE__*/_jsxDEV(CircularProgress, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 547,\n            columnNumber: 15\n          }, this) : !selectedCavo ? /*#__PURE__*/_jsxDEV(Box, {\n            sx: {\n              p: 2\n            },\n            children: [/*#__PURE__*/_jsxDEV(Typography, {\n              variant: \"subtitle1\",\n              gutterBottom: true,\n              children: \"Inserisci l'ID del cavo da eliminare:\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 550,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(Box, {\n              sx: {\n                display: 'flex',\n                alignItems: 'center',\n                mt: 2\n              },\n              children: [/*#__PURE__*/_jsxDEV(TextField, {\n                fullWidth: true,\n                label: \"ID Cavo\",\n                variant: \"outlined\",\n                value: cavoIdInput,\n                onChange: handleCavoIdInputChange,\n                placeholder: \"Inserisci l'ID del cavo\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 554,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(Button, {\n                variant: \"contained\",\n                color: \"primary\",\n                onClick: handleSearchCavoById,\n                disabled: caviLoading || !cavoIdInput.trim(),\n                sx: {\n                  ml: 2,\n                  minWidth: '120px'\n                },\n                children: caviLoading ? /*#__PURE__*/_jsxDEV(CircularProgress, {\n                  size: 24\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 569,\n                  columnNumber: 36\n                }, this) : \"Cerca\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 562,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 553,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 549,\n            columnNumber: 15\n          }, this) : dialogType === 'eliminaCavo' && isInstalled ? /*#__PURE__*/_jsxDEV(_Fragment, {\n            children: [/*#__PURE__*/_jsxDEV(DialogContentText, {\n              children: [\"Il cavo \", /*#__PURE__*/_jsxDEV(\"strong\", {\n                children: selectedCavo.id_cavo\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 576,\n                columnNumber: 27\n              }, this), \" risulta installato o parzialmente posato.\", selectedCavo.metratura_reale > 0 && /*#__PURE__*/_jsxDEV(_Fragment, {\n                children: [\" Metri posati: \", /*#__PURE__*/_jsxDEV(\"strong\", {\n                  children: [selectedCavo.metratura_reale, \" m\"]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 578,\n                  columnNumber: 38\n                }, this), \".\"]\n              }, void 0, true)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 575,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(DialogContentText, {\n              sx: {\n                mt: 2\n              },\n              children: \"Non \\xE8 possibile eliminarlo definitivamente. Vuoi marcarlo come SPARE/consumato?\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 581,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true) : dialogType === 'eliminaCavo' ? /*#__PURE__*/_jsxDEV(_Fragment, {\n            children: [/*#__PURE__*/_jsxDEV(DialogContentText, {\n              children: [\"Stai per eliminare il cavo \", /*#__PURE__*/_jsxDEV(\"strong\", {\n                children: selectedCavo.id_cavo\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 588,\n                columnNumber: 46\n              }, this), \".\"]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 587,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(FormControl, {\n              component: \"fieldset\",\n              sx: {\n                mt: 2\n              },\n              children: [/*#__PURE__*/_jsxDEV(FormLabel, {\n                component: \"legend\",\n                children: \"Scegli l'operazione da eseguire:\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 592,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(RadioGroup, {\n                value: deleteMode,\n                onChange: e => setDeleteMode(e.target.value),\n                children: [/*#__PURE__*/_jsxDEV(FormControlLabel, {\n                  value: \"spare\",\n                  control: /*#__PURE__*/_jsxDEV(Radio, {}, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 599,\n                    columnNumber: 32\n                  }, this),\n                  label: \"Marca come SPARE (mantiene il cavo nel database ma lo contrassegna come non attivo)\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 597,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(FormControlLabel, {\n                  value: \"delete\",\n                  control: /*#__PURE__*/_jsxDEV(Radio, {}, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 604,\n                    columnNumber: 32\n                  }, this),\n                  label: \"Elimina definitivamente (rimuove completamente il cavo dal database)\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 602,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 593,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 591,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true) : null\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 545,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(DialogActions, {\n          children: [/*#__PURE__*/_jsxDEV(Button, {\n            onClick: handleCloseDialog,\n            children: \"Annulla\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 613,\n            columnNumber: 13\n          }, this), dialogType === 'eliminaCavo' && selectedCavo && /*#__PURE__*/_jsxDEV(Button, {\n            onClick: handleSave,\n            disabled: loading,\n            color: isInstalled ? \"warning\" : \"error\",\n            startIcon: loading ? /*#__PURE__*/_jsxDEV(CircularProgress, {\n              size: 20\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 619,\n              columnNumber: 38\n            }, this) : isInstalled ? /*#__PURE__*/_jsxDEV(WarningIcon, {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 619,\n              columnNumber: 85\n            }, this) : /*#__PURE__*/_jsxDEV(DeleteIcon, {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 619,\n              columnNumber: 103\n            }, this),\n            children: isInstalled ? \"Marca come SPARE\" : deleteMode === 'spare' ? \"Marca come SPARE\" : \"Elimina definitivamente\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 615,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 612,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 540,\n        columnNumber: 9\n      }, this);\n    } else if (dialogType === 'modificaCavo') {\n      return /*#__PURE__*/_jsxDEV(Dialog, {\n        open: openDialog,\n        onClose: handleCloseDialog,\n        maxWidth: \"md\",\n        fullWidth: true,\n        children: [/*#__PURE__*/_jsxDEV(DialogTitle, {\n          children: \"Modifica Cavo\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 630,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(DialogContent, {\n          children: /*#__PURE__*/_jsxDEV(Grid, {\n            container: true,\n            spacing: 2,\n            sx: {\n              mt: 1\n            },\n            children: [/*#__PURE__*/_jsxDEV(Grid, {\n              item: true,\n              xs: 12,\n              sm: 6,\n              children: /*#__PURE__*/_jsxDEV(TextField, {\n                name: \"id_cavo\",\n                label: \"ID Cavo\",\n                fullWidth: true,\n                variant: \"outlined\",\n                value: formData.id_cavo,\n                disabled: true\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 634,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 633,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Grid, {\n              item: true,\n              xs: 12,\n              sm: 6,\n              children: /*#__PURE__*/_jsxDEV(TextField, {\n                name: \"revisione_ufficiale\",\n                label: \"Revisione Ufficiale\",\n                fullWidth: true,\n                variant: \"outlined\",\n                value: formData.revisione_ufficiale,\n                onChange: handleFormChange\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 644,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 643,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Grid, {\n              item: true,\n              xs: 12,\n              sm: 6,\n              children: /*#__PURE__*/_jsxDEV(TextField, {\n                name: \"sistema\",\n                label: \"Sistema\",\n                fullWidth: true,\n                variant: \"outlined\",\n                value: formData.sistema,\n                onChange: handleFormChange\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 654,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 653,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Grid, {\n              item: true,\n              xs: 12,\n              sm: 6,\n              children: /*#__PURE__*/_jsxDEV(TextField, {\n                name: \"utility\",\n                label: \"Utility\",\n                fullWidth: true,\n                variant: \"outlined\",\n                value: formData.utility,\n                onChange: handleFormChange\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 664,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 663,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Grid, {\n              item: true,\n              xs: 12,\n              sm: 6,\n              children: /*#__PURE__*/_jsxDEV(TextField, {\n                name: \"colore_cavo\",\n                label: \"Colore Cavo\",\n                fullWidth: true,\n                variant: \"outlined\",\n                value: formData.colore_cavo,\n                onChange: handleFormChange\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 674,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 673,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Grid, {\n              item: true,\n              xs: 12,\n              sm: 6,\n              children: /*#__PURE__*/_jsxDEV(TextField, {\n                name: \"tipologia\",\n                label: \"Tipologia\",\n                fullWidth: true,\n                variant: \"outlined\",\n                value: formData.tipologia,\n                onChange: handleFormChange\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 684,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 683,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Grid, {\n              item: true,\n              xs: 12,\n              sm: 6,\n              children: /*#__PURE__*/_jsxDEV(TextField, {\n                name: \"n_conduttori\",\n                label: \"Numero Conduttori\",\n                fullWidth: true,\n                variant: \"outlined\",\n                value: formData.n_conduttori,\n                onChange: handleFormChange\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 694,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 693,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Grid, {\n              item: true,\n              xs: 12,\n              sm: 6,\n              children: /*#__PURE__*/_jsxDEV(TextField, {\n                name: \"sezione\",\n                label: \"Sezione\",\n                fullWidth: true,\n                variant: \"outlined\",\n                value: formData.sezione,\n                onChange: handleFormChange\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 704,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 703,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Grid, {\n              item: true,\n              xs: 12,\n              sm: 6,\n              children: /*#__PURE__*/_jsxDEV(TextField, {\n                name: \"sh\",\n                label: \"SH\",\n                fullWidth: true,\n                variant: \"outlined\",\n                value: formData.sh || formData.SH,\n                onChange: handleFormChange\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 714,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 713,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Grid, {\n              item: true,\n              xs: 12,\n              sm: 6,\n              children: /*#__PURE__*/_jsxDEV(TextField, {\n                name: \"ubicazione_partenza\",\n                label: \"Ubicazione Partenza\",\n                fullWidth: true,\n                variant: \"outlined\",\n                value: formData.ubicazione_partenza,\n                onChange: handleFormChange\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 724,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 723,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Grid, {\n              item: true,\n              xs: 12,\n              sm: 6,\n              children: /*#__PURE__*/_jsxDEV(TextField, {\n                name: \"utenza_partenza\",\n                label: \"Utenza Partenza\",\n                fullWidth: true,\n                variant: \"outlined\",\n                value: formData.utenza_partenza,\n                onChange: handleFormChange\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 734,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 733,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Grid, {\n              item: true,\n              xs: 12,\n              sm: 6,\n              children: /*#__PURE__*/_jsxDEV(TextField, {\n                name: \"descrizione_utenza_partenza\",\n                label: \"Descrizione Utenza Partenza\",\n                fullWidth: true,\n                variant: \"outlined\",\n                value: formData.descrizione_utenza_partenza,\n                onChange: handleFormChange\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 744,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 743,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Grid, {\n              item: true,\n              xs: 12,\n              sm: 6,\n              children: /*#__PURE__*/_jsxDEV(TextField, {\n                name: \"ubicazione_arrivo\",\n                label: \"Ubicazione Arrivo\",\n                fullWidth: true,\n                variant: \"outlined\",\n                value: formData.ubicazione_arrivo,\n                onChange: handleFormChange\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 754,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 753,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Grid, {\n              item: true,\n              xs: 12,\n              sm: 6,\n              children: /*#__PURE__*/_jsxDEV(TextField, {\n                name: \"utenza_arrivo\",\n                label: \"Utenza Arrivo\",\n                fullWidth: true,\n                variant: \"outlined\",\n                value: formData.utenza_arrivo,\n                onChange: handleFormChange\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 764,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 763,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Grid, {\n              item: true,\n              xs: 12,\n              sm: 6,\n              children: /*#__PURE__*/_jsxDEV(TextField, {\n                name: \"descrizione_utenza_arrivo\",\n                label: \"Descrizione Utenza Arrivo\",\n                fullWidth: true,\n                variant: \"outlined\",\n                value: formData.descrizione_utenza_arrivo,\n                onChange: handleFormChange\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 774,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 773,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Grid, {\n              item: true,\n              xs: 12,\n              sm: 6,\n              children: /*#__PURE__*/_jsxDEV(TextField, {\n                name: \"metri_teorici\",\n                label: \"Metri Teorici\",\n                type: \"number\",\n                fullWidth: true,\n                variant: \"outlined\",\n                value: formData.metri_teorici,\n                onChange: handleFormChange\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 784,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 783,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Grid, {\n              item: true,\n              xs: 12,\n              sm: 6,\n              children: /*#__PURE__*/_jsxDEV(FormControl, {\n                fullWidth: true,\n                children: [/*#__PURE__*/_jsxDEV(InputLabel, {\n                  children: \"Stato Installazione\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 797,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(Select, {\n                  name: \"stato_installazione\",\n                  value: formData.stato_installazione,\n                  label: \"Stato Installazione\",\n                  onChange: handleFormChange,\n                  children: [/*#__PURE__*/_jsxDEV(MenuItem, {\n                    value: \"Da installare\",\n                    children: \"Da installare\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 804,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(MenuItem, {\n                    value: \"In corso\",\n                    children: \"In corso\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 805,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(MenuItem, {\n                    value: \"Installato\",\n                    children: \"Installato\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 806,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(MenuItem, {\n                    value: \"SPARE\",\n                    children: \"SPARE\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 807,\n                    columnNumber: 21\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 798,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 796,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 795,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 632,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 631,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(DialogActions, {\n          children: [/*#__PURE__*/_jsxDEV(Button, {\n            onClick: handleCloseDialog,\n            children: \"Annulla\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 814,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Button, {\n            onClick: handleSave,\n            disabled: loading,\n            startIcon: loading ? /*#__PURE__*/_jsxDEV(CircularProgress, {\n              size: 20\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 818,\n              columnNumber: 36\n            }, this) : /*#__PURE__*/_jsxDEV(SaveIcon, {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 818,\n              columnNumber: 69\n            }, this),\n            children: \"Salva\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 815,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 813,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 629,\n        columnNumber: 9\n      }, this);\n    }\n    return null;\n  };\n  return /*#__PURE__*/_jsxDEV(Box, {\n    sx: {\n      display: 'flex'\n    },\n    children: [/*#__PURE__*/_jsxDEV(Box, {\n      sx: {\n        width: '280px',\n        mr: 3\n      },\n      children: /*#__PURE__*/_jsxDEV(Paper, {\n        sx: {\n          p: 2,\n          mb: 3\n        },\n        children: [/*#__PURE__*/_jsxDEV(Typography, {\n          variant: \"h6\",\n          gutterBottom: true,\n          children: \"Posa Cavi e Collegamenti\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 835,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Divider, {\n          sx: {\n            mb: 2\n          }\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 838,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(List, {\n          component: \"nav\",\n          dense: true,\n          children: [/*#__PURE__*/_jsxDEV(ListItemButton, {\n            onClick: () => handleOptionSelect('inserisciMetri'),\n            children: [/*#__PURE__*/_jsxDEV(ListItemIcon, {\n              children: /*#__PURE__*/_jsxDEV(CableIcon, {}, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 842,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 841,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(ListItemText, {\n              primary: \"1. Inserisci metri posati\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 844,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 840,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(ListItemButton, {\n            onClick: () => handleOptionSelect('modificaBobina'),\n            children: [/*#__PURE__*/_jsxDEV(ListItemIcon, {\n              children: /*#__PURE__*/_jsxDEV(EditIcon, {}, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 849,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 848,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(ListItemText, {\n              primary: \"2. Modifica bobina cavo posato\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 851,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 847,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(ListItemButton, {\n            onClick: () => handleOptionSelect('aggiungiCavo'),\n            children: [/*#__PURE__*/_jsxDEV(ListItemIcon, {\n              children: /*#__PURE__*/_jsxDEV(AddIcon, {}, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 856,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 855,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(ListItemText, {\n              primary: \"3. Aggiungi nuovo cavo\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 858,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 854,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(ListItemButton, {\n            onClick: () => handleOptionSelect('modificaCavo'),\n            children: [/*#__PURE__*/_jsxDEV(ListItemIcon, {\n              children: /*#__PURE__*/_jsxDEV(EditIcon, {}, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 863,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 862,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(ListItemText, {\n              primary: \"4. Modifica cavo\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 865,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 861,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(ListItemButton, {\n            onClick: () => handleOptionSelect('eliminaCavo'),\n            children: [/*#__PURE__*/_jsxDEV(ListItemIcon, {\n              children: /*#__PURE__*/_jsxDEV(DeleteIcon, {}, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 870,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 869,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(ListItemText, {\n              primary: \"5. Elimina cavo\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 872,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 868,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(ListItemButton, {\n            onClick: () => handleOptionSelect('collegamentoCavo'),\n            children: [/*#__PURE__*/_jsxDEV(ListItemIcon, {\n              children: /*#__PURE__*/_jsxDEV(CableIcon, {}, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 877,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 876,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(ListItemText, {\n              primary: \"6. Collegamento cavo\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 879,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 875,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 839,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 834,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 833,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Box, {\n      sx: {\n        flexGrow: 1\n      },\n      children: /*#__PURE__*/_jsxDEV(Paper, {\n        sx: {\n          p: 3,\n          minHeight: '300px',\n          display: 'flex',\n          alignItems: 'center',\n          justifyContent: 'center'\n        },\n        children: [!selectedOption && /*#__PURE__*/_jsxDEV(Typography, {\n          variant: \"body1\",\n          children: \"Seleziona un'opzione dal menu a sinistra per iniziare.\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 889,\n          columnNumber: 13\n        }, this), selectedOption && !openDialog && /*#__PURE__*/_jsxDEV(Box, {\n          sx: {\n            textAlign: 'center'\n          },\n          children: [/*#__PURE__*/_jsxDEV(Typography, {\n            variant: \"h6\",\n            gutterBottom: true,\n            children: [selectedOption === 'inserisciMetri' && 'Inserisci metri posati', selectedOption === 'modificaCavo' && 'Modifica cavo', selectedOption === 'aggiungiCavo' && 'Aggiungi nuovo cavo', selectedOption === 'eliminaCavo' && 'Elimina cavo', selectedOption === 'modificaBobina' && 'Modifica bobina cavo posato', selectedOption === 'collegamentoCavo' && 'Collegamento cavo']\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 895,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(Typography, {\n            variant: \"body1\",\n            children: \"Caricamento in corso...\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 903,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(CircularProgress, {\n            sx: {\n              mt: 2\n            }\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 906,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 894,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 887,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 886,\n      columnNumber: 7\n    }, this), renderDialog()]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 831,\n    columnNumber: 5\n  }, this);\n};\n_s(PosaCaviCollegamenti, \"rJgpKvklhCAha+37suBEac3Umps=\");\n_c = PosaCaviCollegamenti;\nexport default PosaCaviCollegamenti;\nvar _c;\n$RefreshReg$(_c, \"PosaCaviCollegamenti\");", "map": {"version": 3, "names": ["React", "useState", "Box", "Typography", "<PERSON><PERSON>", "Paper", "Divider", "List", "ListItem", "ListItemText", "ListItemIcon", "ListItemButton", "Dialog", "DialogTitle", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "DialogContentText", "DialogActions", "TextField", "FormControl", "InputLabel", "Select", "MenuItem", "Grid", "<PERSON><PERSON>", "CircularProgress", "FormHelperText", "Radio", "RadioGroup", "FormControlLabel", "FormLabel", "Add", "AddIcon", "Edit", "EditIcon", "Delete", "DeleteIcon", "Cable", "CableIcon", "Save", "SaveIcon", "Warning", "WarningIcon", "caviService", "validateCavoData", "validateField", "isEmpty", "jsxDEV", "_jsxDEV", "Fragment", "_Fragment", "PosaCaviCollegamenti", "cantiereId", "onSuccess", "onError", "initialOption", "_s", "loading", "setLoading", "selectedOption", "setSelectedOption", "openDialog", "setOpenDialog", "dialogType", "setDialogType", "selected<PERSON><PERSON><PERSON>", "setSelectedCavo", "formData", "setFormData", "id_cavo", "metri_posati", "id_bobina", "cavoIdInput", "setCavoIdInput", "formErrors", "setFormErrors", "formWarnings", "setForm<PERSON><PERSON>nings", "cavi", "<PERSON><PERSON><PERSON>", "caviLoading", "setCaviLoading", "deleteMode", "setDeleteMode", "useEffect", "loadCavi", "operationType", "caviData", "get<PERSON><PERSON>", "cavi<PERSON><PERSON><PERSON><PERSON><PERSON>", "filter", "cavo", "parseFloat", "metratura_reale", "stato_installazione", "error", "console", "handleOptionSelect", "option", "window", "location", "href", "handleCloseDialog", "handleCavoSelect", "metri_te<PERSON>ci", "handleSearchCavoById", "trim", "find", "c", "handleCavoIdInputChange", "e", "target", "value", "handleFormChange", "name", "additionalParams", "metriTeorici", "result", "prev", "valid", "message", "warning", "handleSave", "isNaN", "updateMetri<PERSON><PERSON><PERSON>", "updateBobina", "validation", "<PERSON><PERSON><PERSON><PERSON>", "errors", "warnings", "validatedData", "updateCavo", "Object", "keys", "length", "warningMessages", "values", "join", "warn", "isInstalled", "log", "markCavoAsSpare", "modificato_manualmente", "<PERSON><PERSON><PERSON><PERSON>", "deleteCavo", "errorMessage", "detail", "renderDialog", "open", "onClose", "max<PERSON><PERSON><PERSON>", "fullWidth", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "severity", "variant", "gutterBottom", "map", "button", "onClick", "primary", "secondary", "tipologia", "ubicazione_partenza", "ubicazione_arrivo", "sx", "mt", "margin", "label", "type", "onChange", "required", "helperText", "disabled", "startIcon", "size", "mb", "p", "display", "alignItems", "placeholder", "color", "ml", "min<PERSON><PERSON><PERSON>", "component", "control", "container", "spacing", "item", "xs", "sm", "revisione_ufficiale", "sistema", "utility", "colore_cavo", "n_conduttori", "sezione", "sh", "SH", "utenza_partenza", "descrizione_utenza_partenza", "utenza_arrivo", "descrizione_utenza_arrivo", "width", "mr", "dense", "flexGrow", "minHeight", "justifyContent", "textAlign", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/PycharmProjects/CMS/webapp/frontend/src/components/cavi/PosaCaviCollegamenti.js"], "sourcesContent": ["import React, { useState } from 'react';\nimport {\n  Box,\n  Typography,\n  Button,\n  Paper,\n  Divider,\n  List,\n  ListItem,\n  ListItemText,\n  ListItemIcon,\n  ListItemButton,\n  Dialog,\n  DialogTitle,\n  DialogContent,\n  DialogContentText,\n  DialogActions,\n  TextField,\n  FormControl,\n  InputLabel,\n  Select,\n  MenuItem,\n  Grid,\n  Alert,\n  CircularProgress,\n  FormHelperText,\n  Radio,\n  RadioGroup,\n  FormControlLabel,\n  FormLabel\n} from '@mui/material';\nimport {\n  Add as AddIcon,\n  Edit as EditIcon,\n  Delete as DeleteIcon,\n  Cable as CableIcon,\n  Save as SaveIcon,\n  Warning as WarningIcon\n} from '@mui/icons-material';\nimport caviService from '../../services/caviService';\nimport { validateCavoData, validateField, isEmpty } from '../../utils/validationUtils';\n\nconst PosaCaviCollegamenti = ({ cantiereId, onSuccess, onError, initialOption = null }) => {\n  const [loading, setLoading] = useState(false);\n  const [selectedOption, setSelectedOption] = useState(null);\n  const [openDialog, setOpenDialog] = useState(false);\n  const [dialogType, setDialogType] = useState('');\n  const [selectedCavo, setSelectedCavo] = useState(null);\n  const [formData, setFormData] = useState({\n    id_cavo: '',\n    metri_posati: '',\n    id_bobina: ''\n  });\n  const [cavoIdInput, setCavoIdInput] = useState('');\n  const [formErrors, setFormErrors] = useState({});\n  const [formWarnings, setFormWarnings] = useState({});\n  const [cavi, setCavi] = useState([]);\n  const [caviLoading, setCaviLoading] = useState(false);\n  const [deleteMode, setDeleteMode] = useState('spare'); // 'spare' o 'delete'\n\n  // Inizializza il componente con l'opzione iniziale se specificata\n  React.useEffect(() => {\n    if (initialOption) {\n      // Imposta direttamente le opzioni invece di chiamare handleOptionSelect\n      // per evitare dipendenze circolari\n      setSelectedOption(initialOption);\n\n      if (initialOption === 'eliminaCavo') {\n        loadCavi('eliminaCavo');\n        setDialogType('eliminaCavo');\n        setOpenDialog(true);\n      } else if (initialOption === 'modificaCavo') {\n        loadCavi('modificaCavo');\n        setDialogType('selezionaCavo');\n        setOpenDialog(true);\n      }\n    }\n    // eslint-disable-next-line react-hooks/exhaustive-deps\n  }, [initialOption]);\n\n  // Carica i cavi attivi per la selezione\n  const loadCavi = async (operationType) => {\n    try {\n      setCaviLoading(true);\n      const caviData = await caviService.getCavi(cantiereId, 0);\n\n      // Filtra i cavi in base al tipo di operazione\n      if (operationType === 'modificaCavo') {\n        // Per modifica cavo, mostra solo i cavi non posati (metratura_reale = 0 e stato != Installato)\n        const caviNonPosati = caviData.filter(cavo =>\n          parseFloat(cavo.metratura_reale) === 0 &&\n          cavo.stato_installazione !== 'Installato'\n        );\n        setCavi(caviNonPosati);\n      } else {\n        // Per altre operazioni, mostra tutti i cavi\n        setCavi(caviData);\n      }\n    } catch (error) {\n      onError('Errore nel caricamento dei cavi');\n      console.error('Errore nel caricamento dei cavi:', error);\n    } finally {\n      setCaviLoading(false);\n    }\n  };\n\n  // Gestisce la selezione di un'opzione dal menu\n  const handleOptionSelect = (option) => {\n    setSelectedOption(option);\n\n    if (option === 'inserisciMetri' || option === 'modificaBobina') {\n      loadCavi(option);\n      setDialogType(option);\n      setOpenDialog(true);\n    } else if (option === 'aggiungiCavo') {\n      // Reindirizza alla pagina di aggiunta cavo\n      window.location.href = `/dashboard/cantieri/${cantiereId}/cavi/posa/aggiungi-cavo`;\n    } else if (option === 'modificaCavo') {\n      loadCavi('modificaCavo');\n      setDialogType('selezionaCavo');\n      setOpenDialog(true);\n    } else if (option === 'eliminaCavo') {\n      loadCavi('eliminaCavo');\n      setDialogType('eliminaCavo');\n      setOpenDialog(true);\n    } else if (option === 'collegamentoCavo') {\n      // Reindirizza alla pagina di gestione collegamenti\n      window.location.href = `/dashboard/cantieri/${cantiereId}/cavi/collegamenti`;\n    }\n  };\n\n  // Gestisce la chiusura del dialog\n  const handleCloseDialog = () => {\n    // Reset dello stato del componente\n    setOpenDialog(false);\n    setSelectedCavo(null);\n    setFormData({\n      id_cavo: '',\n      metri_posati: '',\n      id_bobina: ''\n    });\n    setCavoIdInput('');\n    setFormErrors({});\n    setFormWarnings({});\n    setDeleteMode('spare'); // Reset alla modalità predefinita\n\n    // Se è stato specificato un initialOption, notifica il genitore che il dialogo è stato chiuso\n    // ma senza messaggio di errore\n    if (initialOption && onSuccess) {\n      // Chiama onSuccess ma senza messaggio per evitare l'alert\n      onSuccess(null);\n    }\n  };\n\n  // Gestisce la selezione di un cavo\n  const handleCavoSelect = (cavo) => {\n    setSelectedCavo(cavo);\n    if (dialogType === 'inserisciMetri') {\n      setFormData({\n        ...formData,\n        id_cavo: cavo.id_cavo,\n        metri_posati: ''\n      });\n    } else if (dialogType === 'modificaBobina') {\n      setFormData({\n        ...formData,\n        id_cavo: cavo.id_cavo,\n        id_bobina: cavo.id_bobina || ''\n      });\n    } else if (dialogType === 'selezionaCavo') {\n      setDialogType('modificaCavo');\n      setFormData({\n        ...cavo,\n        metri_teorici: cavo.metri_teorici || '',\n        metratura_reale: cavo.metratura_reale || '0'\n      });\n    }\n  };\n\n  // Gestisce la ricerca di un cavo per ID\n  const handleSearchCavoById = async () => {\n    if (!cavoIdInput.trim()) {\n      onError('Inserisci un ID cavo valido');\n      return;\n    }\n\n    try {\n      setCaviLoading(true);\n      const caviData = await caviService.getCavi(cantiereId, 0);\n      const cavo = caviData.find(c => c.id_cavo === cavoIdInput.trim());\n\n      if (!cavo) {\n        onError(`Cavo con ID ${cavoIdInput} non trovato`);\n        return;\n      }\n\n      // Verifica se stiamo cercando un cavo per modificarlo\n      if (dialogType === 'selezionaCavo') {\n        // Verifica che il cavo non sia già posato\n        if (parseFloat(cavo.metratura_reale) > 0 || cavo.stato_installazione === 'Installato') {\n          onError(`Il cavo ${cavo.id_cavo} risulta già posato. Utilizza l'opzione \"Modifica bobina cavo posato\" per modificarlo.`);\n          return;\n        }\n      }\n\n      // Seleziona il cavo trovato\n      handleCavoSelect(cavo);\n    } catch (error) {\n      onError('Errore nel caricamento dei cavi');\n      console.error('Errore nel caricamento dei cavi:', error);\n    } finally {\n      setCaviLoading(false);\n    }\n  };\n\n  // Gestisce il cambio dell'input dell'ID cavo\n  const handleCavoIdInputChange = (e) => {\n    setCavoIdInput(e.target.value);\n  };\n\n  // Gestisce il cambio dei valori nel form con validazione\n  const handleFormChange = (e) => {\n    const { name, value } = e.target;\n\n    // Aggiorna il valore nel form\n    setFormData({\n      ...formData,\n      [name]: value\n    });\n\n    // Valida il campo\n    if (dialogType === 'modificaCavo') {\n      const additionalParams = {};\n      if (name === 'metratura_reale') {\n        additionalParams.metriTeorici = parseFloat(formData.metri_teorici || 0);\n      }\n\n      const result = validateField(name, value, additionalParams);\n\n      // Aggiorna gli errori\n      setFormErrors(prev => ({\n        ...prev,\n        [name]: !result.valid ? result.message : null\n      }));\n\n      // Aggiorna gli avvisi\n      setFormWarnings(prev => ({\n        ...prev,\n        [name]: result.warning ? result.message : null\n      }));\n    }\n  };\n\n  // Gestisce il salvataggio del form con validazione\n  const handleSave = async () => {\n    try {\n      setLoading(true);\n\n      if (dialogType === 'inserisciMetri') {\n        // Valida i metri posati\n        if (isEmpty(formData.metri_posati) || isNaN(parseFloat(formData.metri_posati))) {\n          setFormErrors({ metri_posati: 'Inserire un valore numerico valido' });\n          setLoading(false);\n          return;\n        }\n\n        await caviService.updateMetriPosati(\n          cantiereId,\n          formData.id_cavo,\n          parseFloat(formData.metri_posati)\n        );\n        onSuccess('Metri posati aggiornati con successo');\n      } else if (dialogType === 'modificaBobina') {\n        await caviService.updateBobina(\n          cantiereId,\n          formData.id_cavo,\n          formData.id_bobina\n        );\n        onSuccess('Bobina aggiornata con successo');\n      } else if (dialogType === 'modificaCavo') {\n        // Validazione completa dei dati del cavo\n        const validation = validateCavoData(formData);\n\n        if (!validation.isValid) {\n          setFormErrors(validation.errors);\n          setFormWarnings(validation.warnings);\n          setLoading(false);\n          return;\n        }\n\n        // Usa i dati validati\n        const validatedData = validation.validatedData;\n        await caviService.updateCavo(cantiereId, validatedData.id_cavo, validatedData);\n        onSuccess('Cavo modificato con successo');\n\n        // Mostra avvisi se presenti\n        if (Object.keys(validation.warnings).length > 0) {\n          const warningMessages = Object.values(validation.warnings).join('\\n');\n          console.warn('Avvisi durante il salvataggio:', warningMessages);\n        }\n      } else if (dialogType === 'eliminaCavo') {\n        // Verifica se il cavo è installato\n        const isInstalled = selectedCavo.stato_installazione === 'Installato' || (selectedCavo.metratura_reale && selectedCavo.metratura_reale > 0);\n\n        if (isInstalled) {\n          // Se è installato, marca solo come SPARE\n          console.log('Marcando cavo installato come SPARE:', selectedCavo.id_cavo);\n          try {\n            // Prima prova con markCavoAsSpare\n            const result = await caviService.markCavoAsSpare(cantiereId, selectedCavo.id_cavo);\n            console.log('Risultato marcatura SPARE:', result);\n            console.log('Nuovo valore modificato_manualmente:', result.modificato_manualmente);\n            onSuccess(`Cavo ${selectedCavo.id_cavo} marcato come SPARE con successo`);\n          } catch (markError) {\n            console.error('Errore con markCavoAsSpare, tentativo con deleteCavo mode=spare:', markError);\n            // Se fallisce, prova con deleteCavo mode=spare\n            const result = await caviService.deleteCavo(cantiereId, selectedCavo.id_cavo, 'spare');\n            console.log('Risultato marcatura SPARE con deleteCavo:', result);\n            onSuccess(`Cavo ${selectedCavo.id_cavo} marcato come SPARE con successo`);\n          }\n        } else {\n          // Se non è installato, usa la modalità selezionata (SPARE o DELETE)\n          console.log('Eliminando cavo non installato con modalità:', deleteMode);\n          const result = await caviService.deleteCavo(cantiereId, selectedCavo.id_cavo, deleteMode);\n          console.log('Risultato eliminazione/marcatura:', result);\n          onSuccess(`Cavo ${selectedCavo.id_cavo} ${deleteMode === 'spare' ? 'marcato come SPARE' : 'eliminato'} con successo`);\n        }\n      }\n\n      handleCloseDialog();\n    } catch (error) {\n      console.error('Errore durante l\\'operazione:', error);\n\n      // Gestione più dettagliata dell'errore\n      let errorMessage = 'Errore sconosciuto';\n\n      if (error.detail) {\n        // Errore dal backend con dettaglio\n        errorMessage = error.detail;\n      } else if (error.message) {\n        // Errore con messaggio\n        errorMessage = error.message;\n      } else if (typeof error === 'string') {\n        // Errore come stringa\n        errorMessage = error;\n      }\n\n      onError('Errore durante l\\'operazione: ' + errorMessage);\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  // Renderizza il dialog in base al tipo\n  const renderDialog = () => {\n    if (dialogType === 'inserisciMetri') {\n      return (\n        <Dialog open={openDialog} onClose={handleCloseDialog} maxWidth=\"md\" fullWidth>\n          <DialogTitle>Inserisci Metri Posati</DialogTitle>\n          <DialogContent>\n            {caviLoading ? (\n              <CircularProgress />\n            ) : cavi.length === 0 ? (\n              <Alert severity=\"info\">Nessun cavo disponibile</Alert>\n            ) : !selectedCavo ? (\n              <Box>\n                <Typography variant=\"subtitle1\" gutterBottom>\n                  Seleziona un cavo:\n                </Typography>\n                <List>\n                  {cavi.map((cavo) => (\n                    <ListItem\n                      button\n                      key={cavo.id_cavo}\n                      onClick={() => handleCavoSelect(cavo)}\n                    >\n                      <ListItemText\n                        primary={cavo.id_cavo}\n                        secondary={`${cavo.tipologia || 'N/A'} - Da: ${cavo.ubicazione_partenza || 'N/A'} A: ${cavo.ubicazione_arrivo || 'N/A'}`}\n                      />\n                    </ListItem>\n                  ))}\n                </List>\n              </Box>\n            ) : (\n              <Box sx={{ mt: 2 }}>\n                <Typography variant=\"subtitle1\" gutterBottom>\n                  Cavo selezionato: {selectedCavo.id_cavo}\n                </Typography>\n                <Typography variant=\"body2\" gutterBottom>\n                  Metri teorici: {selectedCavo.metri_teorici || 'N/A'}\n                </Typography>\n                <Typography variant=\"body2\" gutterBottom>\n                  Metratura attuale: {selectedCavo.metratura_reale || '0'}\n                </Typography>\n                <TextField\n                  margin=\"dense\"\n                  name=\"metri_posati\"\n                  label=\"Metri posati da aggiungere\"\n                  type=\"number\"\n                  fullWidth\n                  variant=\"outlined\"\n                  value={formData.metri_posati}\n                  onChange={handleFormChange}\n                  required\n                  error={!!formErrors.metri_posati}\n                  helperText={formErrors.metri_posati}\n                  sx={{ mt: 2 }}\n                />\n              </Box>\n            )}\n          </DialogContent>\n          <DialogActions>\n            <Button onClick={handleCloseDialog}>Annulla</Button>\n            {selectedCavo && (\n              <Button\n                onClick={handleSave}\n                disabled={loading || !formData.metri_posati}\n                startIcon={loading ? <CircularProgress size={20} /> : <SaveIcon />}\n              >\n                Salva\n              </Button>\n            )}\n          </DialogActions>\n        </Dialog>\n      );\n    } else if (dialogType === 'modificaBobina') {\n      return (\n        <Dialog open={openDialog} onClose={handleCloseDialog} maxWidth=\"md\" fullWidth>\n          <DialogTitle>Modifica Bobina Cavo Posato</DialogTitle>\n          <DialogContent>\n            {caviLoading ? (\n              <CircularProgress />\n            ) : cavi.length === 0 ? (\n              <Alert severity=\"info\">Nessun cavo disponibile</Alert>\n            ) : !selectedCavo ? (\n              <Box>\n                <Typography variant=\"subtitle1\" gutterBottom>\n                  Seleziona un cavo:\n                </Typography>\n                <List>\n                  {cavi.map((cavo) => (\n                    <ListItem\n                      button\n                      key={cavo.id_cavo}\n                      onClick={() => handleCavoSelect(cavo)}\n                    >\n                      <ListItemText\n                        primary={cavo.id_cavo}\n                        secondary={`Bobina attuale: ${cavo.id_bobina || 'Non assegnata'}`}\n                      />\n                    </ListItem>\n                  ))}\n                </List>\n              </Box>\n            ) : (\n              <Box sx={{ mt: 2 }}>\n                <Typography variant=\"subtitle1\" gutterBottom>\n                  Cavo selezionato: {selectedCavo.id_cavo}\n                </Typography>\n                <Typography variant=\"body2\" gutterBottom>\n                  Bobina attuale: {selectedCavo.id_bobina || 'Non assegnata'}\n                </Typography>\n                <TextField\n                  margin=\"dense\"\n                  name=\"id_bobina\"\n                  label=\"ID Bobina\"\n                  fullWidth\n                  variant=\"outlined\"\n                  value={formData.id_bobina}\n                  onChange={handleFormChange}\n                  sx={{ mt: 2 }}\n                />\n              </Box>\n            )}\n          </DialogContent>\n          <DialogActions>\n            <Button onClick={handleCloseDialog}>Annulla</Button>\n            {selectedCavo && (\n              <Button\n                onClick={handleSave}\n                disabled={loading}\n                startIcon={loading ? <CircularProgress size={20} /> : <SaveIcon />}\n              >\n                Salva\n              </Button>\n            )}\n          </DialogActions>\n        </Dialog>\n      );\n    } else if (dialogType === 'selezionaCavo') {\n      return (\n        <Dialog open={openDialog} onClose={handleCloseDialog} maxWidth=\"md\" fullWidth>\n          <DialogTitle>Modifica Cavo</DialogTitle>\n          <DialogContent>\n            <Alert severity=\"info\" sx={{ mb: 2 }}>\n              Puoi modificare solo i cavi non ancora posati (metratura = 0 e stato diverso da \"Installato\").\n              Per modificare cavi già posati, utilizzare l'opzione \"Modifica bobina cavo posato\".\n            </Alert>\n\n            {caviLoading ? (\n              <CircularProgress />\n            ) : !selectedCavo ? (\n              <Box sx={{ p: 2 }}>\n                <Typography variant=\"subtitle1\" gutterBottom>\n                  Inserisci l'ID del cavo da modificare:\n                </Typography>\n                <Box sx={{ display: 'flex', alignItems: 'center', mt: 2 }}>\n                  <TextField\n                    fullWidth\n                    label=\"ID Cavo\"\n                    variant=\"outlined\"\n                    value={cavoIdInput}\n                    onChange={handleCavoIdInputChange}\n                    placeholder=\"Inserisci l'ID del cavo\"\n                  />\n                  <Button\n                    variant=\"contained\"\n                    color=\"primary\"\n                    onClick={handleSearchCavoById}\n                    disabled={caviLoading || !cavoIdInput.trim()}\n                    sx={{ ml: 2, minWidth: '120px' }}\n                  >\n                    {caviLoading ? <CircularProgress size={24} /> : \"Cerca\"}\n                  </Button>\n                </Box>\n              </Box>\n            ) : null}\n          </DialogContent>\n          <DialogActions>\n            <Button onClick={handleCloseDialog}>Annulla</Button>\n          </DialogActions>\n        </Dialog>\n      );\n    } else if (dialogType === 'eliminaCavo') {\n      // Verifica se il cavo selezionato è installato\n      const isInstalled = selectedCavo && (selectedCavo.stato_installazione === 'Installato' || (selectedCavo.metratura_reale && selectedCavo.metratura_reale > 0));\n\n      return (\n        <Dialog open={openDialog} onClose={handleCloseDialog} maxWidth=\"md\" fullWidth>\n          <DialogTitle>\n            {!selectedCavo ? 'Elimina Cavo' :\n             isInstalled ? 'Marca Cavo come SPARE' : 'Elimina Cavo'}\n          </DialogTitle>\n          <DialogContent>\n            {caviLoading ? (\n              <CircularProgress />\n            ) : !selectedCavo ? (\n              <Box sx={{ p: 2 }}>\n                <Typography variant=\"subtitle1\" gutterBottom>\n                  Inserisci l'ID del cavo da eliminare:\n                </Typography>\n                <Box sx={{ display: 'flex', alignItems: 'center', mt: 2 }}>\n                  <TextField\n                    fullWidth\n                    label=\"ID Cavo\"\n                    variant=\"outlined\"\n                    value={cavoIdInput}\n                    onChange={handleCavoIdInputChange}\n                    placeholder=\"Inserisci l'ID del cavo\"\n                  />\n                  <Button\n                    variant=\"contained\"\n                    color=\"primary\"\n                    onClick={handleSearchCavoById}\n                    disabled={caviLoading || !cavoIdInput.trim()}\n                    sx={{ ml: 2, minWidth: '120px' }}\n                  >\n                    {caviLoading ? <CircularProgress size={24} /> : \"Cerca\"}\n                  </Button>\n                </Box>\n              </Box>\n            ) : dialogType === 'eliminaCavo' && isInstalled ? (\n              <>\n                <DialogContentText>\n                  Il cavo <strong>{selectedCavo.id_cavo}</strong> risulta installato o parzialmente posato.\n                  {selectedCavo.metratura_reale > 0 && (\n                    <> Metri posati: <strong>{selectedCavo.metratura_reale} m</strong>.</>\n                  )}\n                </DialogContentText>\n                <DialogContentText sx={{ mt: 2 }}>\n                  Non è possibile eliminarlo definitivamente. Vuoi marcarlo come SPARE/consumato?\n                </DialogContentText>\n              </>\n            ) : dialogType === 'eliminaCavo' ? (\n              <>\n                <DialogContentText>\n                  Stai per eliminare il cavo <strong>{selectedCavo.id_cavo}</strong>.\n                </DialogContentText>\n\n                <FormControl component=\"fieldset\" sx={{ mt: 2 }}>\n                  <FormLabel component=\"legend\">Scegli l'operazione da eseguire:</FormLabel>\n                  <RadioGroup\n                    value={deleteMode}\n                    onChange={(e) => setDeleteMode(e.target.value)}\n                  >\n                    <FormControlLabel\n                      value=\"spare\"\n                      control={<Radio />}\n                      label=\"Marca come SPARE (mantiene il cavo nel database ma lo contrassegna come non attivo)\"\n                    />\n                    <FormControlLabel\n                      value=\"delete\"\n                      control={<Radio />}\n                      label=\"Elimina definitivamente (rimuove completamente il cavo dal database)\"\n                    />\n                  </RadioGroup>\n                </FormControl>\n              </>\n            ) : null}\n          </DialogContent>\n          <DialogActions>\n            <Button onClick={handleCloseDialog}>Annulla</Button>\n            {dialogType === 'eliminaCavo' && selectedCavo && (\n              <Button\n                onClick={handleSave}\n                disabled={loading}\n                color={isInstalled ? \"warning\" : \"error\"}\n                startIcon={loading ? <CircularProgress size={20} /> : isInstalled ? <WarningIcon /> : <DeleteIcon />}\n              >\n                {isInstalled ? \"Marca come SPARE\" : (deleteMode === 'spare' ? \"Marca come SPARE\" : \"Elimina definitivamente\")}\n              </Button>\n            )}\n          </DialogActions>\n        </Dialog>\n      );\n    } else if (dialogType === 'modificaCavo') {\n      return (\n        <Dialog open={openDialog} onClose={handleCloseDialog} maxWidth=\"md\" fullWidth>\n          <DialogTitle>Modifica Cavo</DialogTitle>\n          <DialogContent>\n            <Grid container spacing={2} sx={{ mt: 1 }}>\n              <Grid item xs={12} sm={6}>\n                <TextField\n                  name=\"id_cavo\"\n                  label=\"ID Cavo\"\n                  fullWidth\n                  variant=\"outlined\"\n                  value={formData.id_cavo}\n                  disabled\n                />\n              </Grid>\n              <Grid item xs={12} sm={6}>\n                <TextField\n                  name=\"revisione_ufficiale\"\n                  label=\"Revisione Ufficiale\"\n                  fullWidth\n                  variant=\"outlined\"\n                  value={formData.revisione_ufficiale}\n                  onChange={handleFormChange}\n                />\n              </Grid>\n              <Grid item xs={12} sm={6}>\n                <TextField\n                  name=\"sistema\"\n                  label=\"Sistema\"\n                  fullWidth\n                  variant=\"outlined\"\n                  value={formData.sistema}\n                  onChange={handleFormChange}\n                />\n              </Grid>\n              <Grid item xs={12} sm={6}>\n                <TextField\n                  name=\"utility\"\n                  label=\"Utility\"\n                  fullWidth\n                  variant=\"outlined\"\n                  value={formData.utility}\n                  onChange={handleFormChange}\n                />\n              </Grid>\n              <Grid item xs={12} sm={6}>\n                <TextField\n                  name=\"colore_cavo\"\n                  label=\"Colore Cavo\"\n                  fullWidth\n                  variant=\"outlined\"\n                  value={formData.colore_cavo}\n                  onChange={handleFormChange}\n                />\n              </Grid>\n              <Grid item xs={12} sm={6}>\n                <TextField\n                  name=\"tipologia\"\n                  label=\"Tipologia\"\n                  fullWidth\n                  variant=\"outlined\"\n                  value={formData.tipologia}\n                  onChange={handleFormChange}\n                />\n              </Grid>\n              <Grid item xs={12} sm={6}>\n                <TextField\n                  name=\"n_conduttori\"\n                  label=\"Numero Conduttori\"\n                  fullWidth\n                  variant=\"outlined\"\n                  value={formData.n_conduttori}\n                  onChange={handleFormChange}\n                />\n              </Grid>\n              <Grid item xs={12} sm={6}>\n                <TextField\n                  name=\"sezione\"\n                  label=\"Sezione\"\n                  fullWidth\n                  variant=\"outlined\"\n                  value={formData.sezione}\n                  onChange={handleFormChange}\n                />\n              </Grid>\n              <Grid item xs={12} sm={6}>\n                <TextField\n                  name=\"sh\"\n                  label=\"SH\"\n                  fullWidth\n                  variant=\"outlined\"\n                  value={formData.sh || formData.SH}\n                  onChange={handleFormChange}\n                />\n              </Grid>\n              <Grid item xs={12} sm={6}>\n                <TextField\n                  name=\"ubicazione_partenza\"\n                  label=\"Ubicazione Partenza\"\n                  fullWidth\n                  variant=\"outlined\"\n                  value={formData.ubicazione_partenza}\n                  onChange={handleFormChange}\n                />\n              </Grid>\n              <Grid item xs={12} sm={6}>\n                <TextField\n                  name=\"utenza_partenza\"\n                  label=\"Utenza Partenza\"\n                  fullWidth\n                  variant=\"outlined\"\n                  value={formData.utenza_partenza}\n                  onChange={handleFormChange}\n                />\n              </Grid>\n              <Grid item xs={12} sm={6}>\n                <TextField\n                  name=\"descrizione_utenza_partenza\"\n                  label=\"Descrizione Utenza Partenza\"\n                  fullWidth\n                  variant=\"outlined\"\n                  value={formData.descrizione_utenza_partenza}\n                  onChange={handleFormChange}\n                />\n              </Grid>\n              <Grid item xs={12} sm={6}>\n                <TextField\n                  name=\"ubicazione_arrivo\"\n                  label=\"Ubicazione Arrivo\"\n                  fullWidth\n                  variant=\"outlined\"\n                  value={formData.ubicazione_arrivo}\n                  onChange={handleFormChange}\n                />\n              </Grid>\n              <Grid item xs={12} sm={6}>\n                <TextField\n                  name=\"utenza_arrivo\"\n                  label=\"Utenza Arrivo\"\n                  fullWidth\n                  variant=\"outlined\"\n                  value={formData.utenza_arrivo}\n                  onChange={handleFormChange}\n                />\n              </Grid>\n              <Grid item xs={12} sm={6}>\n                <TextField\n                  name=\"descrizione_utenza_arrivo\"\n                  label=\"Descrizione Utenza Arrivo\"\n                  fullWidth\n                  variant=\"outlined\"\n                  value={formData.descrizione_utenza_arrivo}\n                  onChange={handleFormChange}\n                />\n              </Grid>\n              <Grid item xs={12} sm={6}>\n                <TextField\n                  name=\"metri_teorici\"\n                  label=\"Metri Teorici\"\n                  type=\"number\"\n                  fullWidth\n                  variant=\"outlined\"\n                  value={formData.metri_teorici}\n                  onChange={handleFormChange}\n                />\n              </Grid>\n              {/* Campo ID Bobina rimosso perché è un campo di sistema */}\n              <Grid item xs={12} sm={6}>\n                <FormControl fullWidth>\n                  <InputLabel>Stato Installazione</InputLabel>\n                  <Select\n                    name=\"stato_installazione\"\n                    value={formData.stato_installazione}\n                    label=\"Stato Installazione\"\n                    onChange={handleFormChange}\n                  >\n                    <MenuItem value=\"Da installare\">Da installare</MenuItem>\n                    <MenuItem value=\"In corso\">In corso</MenuItem>\n                    <MenuItem value=\"Installato\">Installato</MenuItem>\n                    <MenuItem value=\"SPARE\">SPARE</MenuItem>\n                  </Select>\n                </FormControl>\n              </Grid>\n            </Grid>\n          </DialogContent>\n          <DialogActions>\n            <Button onClick={handleCloseDialog}>Annulla</Button>\n            <Button\n              onClick={handleSave}\n              disabled={loading}\n              startIcon={loading ? <CircularProgress size={20} /> : <SaveIcon />}\n            >\n              Salva\n            </Button>\n          </DialogActions>\n        </Dialog>\n      );\n    }\n\n    return null;\n  };\n\n  return (\n    <Box sx={{ display: 'flex' }}>\n      {/* Menu a cascata nella sidebar */}\n      <Box sx={{ width: '280px', mr: 3 }}>\n        <Paper sx={{ p: 2, mb: 3 }}>\n          <Typography variant=\"h6\" gutterBottom>\n            Posa Cavi e Collegamenti\n          </Typography>\n          <Divider sx={{ mb: 2 }} />\n          <List component=\"nav\" dense>\n            <ListItemButton onClick={() => handleOptionSelect('inserisciMetri')}>\n              <ListItemIcon>\n                <CableIcon />\n              </ListItemIcon>\n              <ListItemText primary=\"1. Inserisci metri posati\" />\n            </ListItemButton>\n\n            <ListItemButton onClick={() => handleOptionSelect('modificaBobina')}>\n              <ListItemIcon>\n                <EditIcon />\n              </ListItemIcon>\n              <ListItemText primary=\"2. Modifica bobina cavo posato\" />\n            </ListItemButton>\n\n            <ListItemButton onClick={() => handleOptionSelect('aggiungiCavo')}>\n              <ListItemIcon>\n                <AddIcon />\n              </ListItemIcon>\n              <ListItemText primary=\"3. Aggiungi nuovo cavo\" />\n            </ListItemButton>\n\n            <ListItemButton onClick={() => handleOptionSelect('modificaCavo')}>\n              <ListItemIcon>\n                <EditIcon />\n              </ListItemIcon>\n              <ListItemText primary=\"4. Modifica cavo\" />\n            </ListItemButton>\n\n            <ListItemButton onClick={() => handleOptionSelect('eliminaCavo')}>\n              <ListItemIcon>\n                <DeleteIcon />\n              </ListItemIcon>\n              <ListItemText primary=\"5. Elimina cavo\" />\n            </ListItemButton>\n\n            <ListItemButton onClick={() => handleOptionSelect('collegamentoCavo')}>\n              <ListItemIcon>\n                <CableIcon />\n              </ListItemIcon>\n              <ListItemText primary=\"6. Collegamento cavo\" />\n            </ListItemButton>\n          </List>\n        </Paper>\n      </Box>\n\n      {/* Area principale per il contenuto */}\n      <Box sx={{ flexGrow: 1 }}>\n        <Paper sx={{ p: 3, minHeight: '300px', display: 'flex', alignItems: 'center', justifyContent: 'center' }}>\n          {!selectedOption && (\n            <Typography variant=\"body1\">\n              Seleziona un'opzione dal menu a sinistra per iniziare.\n            </Typography>\n          )}\n          {selectedOption && !openDialog && (\n            <Box sx={{ textAlign: 'center' }}>\n              <Typography variant=\"h6\" gutterBottom>\n                {selectedOption === 'inserisciMetri' && 'Inserisci metri posati'}\n                {selectedOption === 'modificaCavo' && 'Modifica cavo'}\n                {selectedOption === 'aggiungiCavo' && 'Aggiungi nuovo cavo'}\n                {selectedOption === 'eliminaCavo' && 'Elimina cavo'}\n                {selectedOption === 'modificaBobina' && 'Modifica bobina cavo posato'}\n                {selectedOption === 'collegamentoCavo' && 'Collegamento cavo'}\n              </Typography>\n              <Typography variant=\"body1\">\n                Caricamento in corso...\n              </Typography>\n              <CircularProgress sx={{ mt: 2 }} />\n            </Box>\n          )}\n        </Paper>\n      </Box>\n\n      {renderDialog()}\n    </Box>\n  );\n};\n\nexport default PosaCaviCollegamenti;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,QAAQ,OAAO;AACvC,SACEC,GAAG,EACHC,UAAU,EACVC,MAAM,EACNC,KAAK,EACLC,OAAO,EACPC,IAAI,EACJC,QAAQ,EACRC,YAAY,EACZC,YAAY,EACZC,cAAc,EACdC,MAAM,EACNC,WAAW,EACXC,aAAa,EACbC,iBAAiB,EACjBC,aAAa,EACbC,SAAS,EACTC,WAAW,EACXC,UAAU,EACVC,MAAM,EACNC,QAAQ,EACRC,IAAI,EACJC,KAAK,EACLC,gBAAgB,EAChBC,cAAc,EACdC,KAAK,EACLC,UAAU,EACVC,gBAAgB,EAChBC,SAAS,QACJ,eAAe;AACtB,SACEC,GAAG,IAAIC,OAAO,EACdC,IAAI,IAAIC,QAAQ,EAChBC,MAAM,IAAIC,UAAU,EACpBC,KAAK,IAAIC,SAAS,EAClBC,IAAI,IAAIC,QAAQ,EAChBC,OAAO,IAAIC,WAAW,QACjB,qBAAqB;AAC5B,OAAOC,WAAW,MAAM,4BAA4B;AACpD,SAASC,gBAAgB,EAAEC,aAAa,EAAEC,OAAO,QAAQ,6BAA6B;AAAC,SAAAC,MAAA,IAAAC,OAAA,EAAAC,QAAA,IAAAC,SAAA;AAEvF,MAAMC,oBAAoB,GAAGA,CAAC;EAAEC,UAAU;EAAEC,SAAS;EAAEC,OAAO;EAAEC,aAAa,GAAG;AAAK,CAAC,KAAK;EAAAC,EAAA;EACzF,MAAM,CAACC,OAAO,EAAEC,UAAU,CAAC,GAAGxD,QAAQ,CAAC,KAAK,CAAC;EAC7C,MAAM,CAACyD,cAAc,EAAEC,iBAAiB,CAAC,GAAG1D,QAAQ,CAAC,IAAI,CAAC;EAC1D,MAAM,CAAC2D,UAAU,EAAEC,aAAa,CAAC,GAAG5D,QAAQ,CAAC,KAAK,CAAC;EACnD,MAAM,CAAC6D,UAAU,EAAEC,aAAa,CAAC,GAAG9D,QAAQ,CAAC,EAAE,CAAC;EAChD,MAAM,CAAC+D,YAAY,EAAEC,eAAe,CAAC,GAAGhE,QAAQ,CAAC,IAAI,CAAC;EACtD,MAAM,CAACiE,QAAQ,EAAEC,WAAW,CAAC,GAAGlE,QAAQ,CAAC;IACvCmE,OAAO,EAAE,EAAE;IACXC,YAAY,EAAE,EAAE;IAChBC,SAAS,EAAE;EACb,CAAC,CAAC;EACF,MAAM,CAACC,WAAW,EAAEC,cAAc,CAAC,GAAGvE,QAAQ,CAAC,EAAE,CAAC;EAClD,MAAM,CAACwE,UAAU,EAAEC,aAAa,CAAC,GAAGzE,QAAQ,CAAC,CAAC,CAAC,CAAC;EAChD,MAAM,CAAC0E,YAAY,EAAEC,eAAe,CAAC,GAAG3E,QAAQ,CAAC,CAAC,CAAC,CAAC;EACpD,MAAM,CAAC4E,IAAI,EAAEC,OAAO,CAAC,GAAG7E,QAAQ,CAAC,EAAE,CAAC;EACpC,MAAM,CAAC8E,WAAW,EAAEC,cAAc,CAAC,GAAG/E,QAAQ,CAAC,KAAK,CAAC;EACrD,MAAM,CAACgF,UAAU,EAAEC,aAAa,CAAC,GAAGjF,QAAQ,CAAC,OAAO,CAAC,CAAC,CAAC;;EAEvD;EACAD,KAAK,CAACmF,SAAS,CAAC,MAAM;IACpB,IAAI7B,aAAa,EAAE;MACjB;MACA;MACAK,iBAAiB,CAACL,aAAa,CAAC;MAEhC,IAAIA,aAAa,KAAK,aAAa,EAAE;QACnC8B,QAAQ,CAAC,aAAa,CAAC;QACvBrB,aAAa,CAAC,aAAa,CAAC;QAC5BF,aAAa,CAAC,IAAI,CAAC;MACrB,CAAC,MAAM,IAAIP,aAAa,KAAK,cAAc,EAAE;QAC3C8B,QAAQ,CAAC,cAAc,CAAC;QACxBrB,aAAa,CAAC,eAAe,CAAC;QAC9BF,aAAa,CAAC,IAAI,CAAC;MACrB;IACF;IACA;EACF,CAAC,EAAE,CAACP,aAAa,CAAC,CAAC;;EAEnB;EACA,MAAM8B,QAAQ,GAAG,MAAOC,aAAa,IAAK;IACxC,IAAI;MACFL,cAAc,CAAC,IAAI,CAAC;MACpB,MAAMM,QAAQ,GAAG,MAAM5C,WAAW,CAAC6C,OAAO,CAACpC,UAAU,EAAE,CAAC,CAAC;;MAEzD;MACA,IAAIkC,aAAa,KAAK,cAAc,EAAE;QACpC;QACA,MAAMG,aAAa,GAAGF,QAAQ,CAACG,MAAM,CAACC,IAAI,IACxCC,UAAU,CAACD,IAAI,CAACE,eAAe,CAAC,KAAK,CAAC,IACtCF,IAAI,CAACG,mBAAmB,KAAK,YAC/B,CAAC;QACDf,OAAO,CAACU,aAAa,CAAC;MACxB,CAAC,MAAM;QACL;QACAV,OAAO,CAACQ,QAAQ,CAAC;MACnB;IACF,CAAC,CAAC,OAAOQ,KAAK,EAAE;MACdzC,OAAO,CAAC,iCAAiC,CAAC;MAC1C0C,OAAO,CAACD,KAAK,CAAC,kCAAkC,EAAEA,KAAK,CAAC;IAC1D,CAAC,SAAS;MACRd,cAAc,CAAC,KAAK,CAAC;IACvB;EACF,CAAC;;EAED;EACA,MAAMgB,kBAAkB,GAAIC,MAAM,IAAK;IACrCtC,iBAAiB,CAACsC,MAAM,CAAC;IAEzB,IAAIA,MAAM,KAAK,gBAAgB,IAAIA,MAAM,KAAK,gBAAgB,EAAE;MAC9Db,QAAQ,CAACa,MAAM,CAAC;MAChBlC,aAAa,CAACkC,MAAM,CAAC;MACrBpC,aAAa,CAAC,IAAI,CAAC;IACrB,CAAC,MAAM,IAAIoC,MAAM,KAAK,cAAc,EAAE;MACpC;MACAC,MAAM,CAACC,QAAQ,CAACC,IAAI,GAAG,uBAAuBjD,UAAU,0BAA0B;IACpF,CAAC,MAAM,IAAI8C,MAAM,KAAK,cAAc,EAAE;MACpCb,QAAQ,CAAC,cAAc,CAAC;MACxBrB,aAAa,CAAC,eAAe,CAAC;MAC9BF,aAAa,CAAC,IAAI,CAAC;IACrB,CAAC,MAAM,IAAIoC,MAAM,KAAK,aAAa,EAAE;MACnCb,QAAQ,CAAC,aAAa,CAAC;MACvBrB,aAAa,CAAC,aAAa,CAAC;MAC5BF,aAAa,CAAC,IAAI,CAAC;IACrB,CAAC,MAAM,IAAIoC,MAAM,KAAK,kBAAkB,EAAE;MACxC;MACAC,MAAM,CAACC,QAAQ,CAACC,IAAI,GAAG,uBAAuBjD,UAAU,oBAAoB;IAC9E;EACF,CAAC;;EAED;EACA,MAAMkD,iBAAiB,GAAGA,CAAA,KAAM;IAC9B;IACAxC,aAAa,CAAC,KAAK,CAAC;IACpBI,eAAe,CAAC,IAAI,CAAC;IACrBE,WAAW,CAAC;MACVC,OAAO,EAAE,EAAE;MACXC,YAAY,EAAE,EAAE;MAChBC,SAAS,EAAE;IACb,CAAC,CAAC;IACFE,cAAc,CAAC,EAAE,CAAC;IAClBE,aAAa,CAAC,CAAC,CAAC,CAAC;IACjBE,eAAe,CAAC,CAAC,CAAC,CAAC;IACnBM,aAAa,CAAC,OAAO,CAAC,CAAC,CAAC;;IAExB;IACA;IACA,IAAI5B,aAAa,IAAIF,SAAS,EAAE;MAC9B;MACAA,SAAS,CAAC,IAAI,CAAC;IACjB;EACF,CAAC;;EAED;EACA,MAAMkD,gBAAgB,GAAIZ,IAAI,IAAK;IACjCzB,eAAe,CAACyB,IAAI,CAAC;IACrB,IAAI5B,UAAU,KAAK,gBAAgB,EAAE;MACnCK,WAAW,CAAC;QACV,GAAGD,QAAQ;QACXE,OAAO,EAAEsB,IAAI,CAACtB,OAAO;QACrBC,YAAY,EAAE;MAChB,CAAC,CAAC;IACJ,CAAC,MAAM,IAAIP,UAAU,KAAK,gBAAgB,EAAE;MAC1CK,WAAW,CAAC;QACV,GAAGD,QAAQ;QACXE,OAAO,EAAEsB,IAAI,CAACtB,OAAO;QACrBE,SAAS,EAAEoB,IAAI,CAACpB,SAAS,IAAI;MAC/B,CAAC,CAAC;IACJ,CAAC,MAAM,IAAIR,UAAU,KAAK,eAAe,EAAE;MACzCC,aAAa,CAAC,cAAc,CAAC;MAC7BI,WAAW,CAAC;QACV,GAAGuB,IAAI;QACPa,aAAa,EAAEb,IAAI,CAACa,aAAa,IAAI,EAAE;QACvCX,eAAe,EAAEF,IAAI,CAACE,eAAe,IAAI;MAC3C,CAAC,CAAC;IACJ;EACF,CAAC;;EAED;EACA,MAAMY,oBAAoB,GAAG,MAAAA,CAAA,KAAY;IACvC,IAAI,CAACjC,WAAW,CAACkC,IAAI,CAAC,CAAC,EAAE;MACvBpD,OAAO,CAAC,6BAA6B,CAAC;MACtC;IACF;IAEA,IAAI;MACF2B,cAAc,CAAC,IAAI,CAAC;MACpB,MAAMM,QAAQ,GAAG,MAAM5C,WAAW,CAAC6C,OAAO,CAACpC,UAAU,EAAE,CAAC,CAAC;MACzD,MAAMuC,IAAI,GAAGJ,QAAQ,CAACoB,IAAI,CAACC,CAAC,IAAIA,CAAC,CAACvC,OAAO,KAAKG,WAAW,CAACkC,IAAI,CAAC,CAAC,CAAC;MAEjE,IAAI,CAACf,IAAI,EAAE;QACTrC,OAAO,CAAC,eAAekB,WAAW,cAAc,CAAC;QACjD;MACF;;MAEA;MACA,IAAIT,UAAU,KAAK,eAAe,EAAE;QAClC;QACA,IAAI6B,UAAU,CAACD,IAAI,CAACE,eAAe,CAAC,GAAG,CAAC,IAAIF,IAAI,CAACG,mBAAmB,KAAK,YAAY,EAAE;UACrFxC,OAAO,CAAC,WAAWqC,IAAI,CAACtB,OAAO,wFAAwF,CAAC;UACxH;QACF;MACF;;MAEA;MACAkC,gBAAgB,CAACZ,IAAI,CAAC;IACxB,CAAC,CAAC,OAAOI,KAAK,EAAE;MACdzC,OAAO,CAAC,iCAAiC,CAAC;MAC1C0C,OAAO,CAACD,KAAK,CAAC,kCAAkC,EAAEA,KAAK,CAAC;IAC1D,CAAC,SAAS;MACRd,cAAc,CAAC,KAAK,CAAC;IACvB;EACF,CAAC;;EAED;EACA,MAAM4B,uBAAuB,GAAIC,CAAC,IAAK;IACrCrC,cAAc,CAACqC,CAAC,CAACC,MAAM,CAACC,KAAK,CAAC;EAChC,CAAC;;EAED;EACA,MAAMC,gBAAgB,GAAIH,CAAC,IAAK;IAC9B,MAAM;MAAEI,IAAI;MAAEF;IAAM,CAAC,GAAGF,CAAC,CAACC,MAAM;;IAEhC;IACA3C,WAAW,CAAC;MACV,GAAGD,QAAQ;MACX,CAAC+C,IAAI,GAAGF;IACV,CAAC,CAAC;;IAEF;IACA,IAAIjD,UAAU,KAAK,cAAc,EAAE;MACjC,MAAMoD,gBAAgB,GAAG,CAAC,CAAC;MAC3B,IAAID,IAAI,KAAK,iBAAiB,EAAE;QAC9BC,gBAAgB,CAACC,YAAY,GAAGxB,UAAU,CAACzB,QAAQ,CAACqC,aAAa,IAAI,CAAC,CAAC;MACzE;MAEA,MAAMa,MAAM,GAAGxE,aAAa,CAACqE,IAAI,EAAEF,KAAK,EAAEG,gBAAgB,CAAC;;MAE3D;MACAxC,aAAa,CAAC2C,IAAI,KAAK;QACrB,GAAGA,IAAI;QACP,CAACJ,IAAI,GAAG,CAACG,MAAM,CAACE,KAAK,GAAGF,MAAM,CAACG,OAAO,GAAG;MAC3C,CAAC,CAAC,CAAC;;MAEH;MACA3C,eAAe,CAACyC,IAAI,KAAK;QACvB,GAAGA,IAAI;QACP,CAACJ,IAAI,GAAGG,MAAM,CAACI,OAAO,GAAGJ,MAAM,CAACG,OAAO,GAAG;MAC5C,CAAC,CAAC,CAAC;IACL;EACF,CAAC;;EAED;EACA,MAAME,UAAU,GAAG,MAAAA,CAAA,KAAY;IAC7B,IAAI;MACFhE,UAAU,CAAC,IAAI,CAAC;MAEhB,IAAIK,UAAU,KAAK,gBAAgB,EAAE;QACnC;QACA,IAAIjB,OAAO,CAACqB,QAAQ,CAACG,YAAY,CAAC,IAAIqD,KAAK,CAAC/B,UAAU,CAACzB,QAAQ,CAACG,YAAY,CAAC,CAAC,EAAE;UAC9EK,aAAa,CAAC;YAAEL,YAAY,EAAE;UAAqC,CAAC,CAAC;UACrEZ,UAAU,CAAC,KAAK,CAAC;UACjB;QACF;QAEA,MAAMf,WAAW,CAACiF,iBAAiB,CACjCxE,UAAU,EACVe,QAAQ,CAACE,OAAO,EAChBuB,UAAU,CAACzB,QAAQ,CAACG,YAAY,CAClC,CAAC;QACDjB,SAAS,CAAC,sCAAsC,CAAC;MACnD,CAAC,MAAM,IAAIU,UAAU,KAAK,gBAAgB,EAAE;QAC1C,MAAMpB,WAAW,CAACkF,YAAY,CAC5BzE,UAAU,EACVe,QAAQ,CAACE,OAAO,EAChBF,QAAQ,CAACI,SACX,CAAC;QACDlB,SAAS,CAAC,gCAAgC,CAAC;MAC7C,CAAC,MAAM,IAAIU,UAAU,KAAK,cAAc,EAAE;QACxC;QACA,MAAM+D,UAAU,GAAGlF,gBAAgB,CAACuB,QAAQ,CAAC;QAE7C,IAAI,CAAC2D,UAAU,CAACC,OAAO,EAAE;UACvBpD,aAAa,CAACmD,UAAU,CAACE,MAAM,CAAC;UAChCnD,eAAe,CAACiD,UAAU,CAACG,QAAQ,CAAC;UACpCvE,UAAU,CAAC,KAAK,CAAC;UACjB;QACF;;QAEA;QACA,MAAMwE,aAAa,GAAGJ,UAAU,CAACI,aAAa;QAC9C,MAAMvF,WAAW,CAACwF,UAAU,CAAC/E,UAAU,EAAE8E,aAAa,CAAC7D,OAAO,EAAE6D,aAAa,CAAC;QAC9E7E,SAAS,CAAC,8BAA8B,CAAC;;QAEzC;QACA,IAAI+E,MAAM,CAACC,IAAI,CAACP,UAAU,CAACG,QAAQ,CAAC,CAACK,MAAM,GAAG,CAAC,EAAE;UAC/C,MAAMC,eAAe,GAAGH,MAAM,CAACI,MAAM,CAACV,UAAU,CAACG,QAAQ,CAAC,CAACQ,IAAI,CAAC,IAAI,CAAC;UACrEzC,OAAO,CAAC0C,IAAI,CAAC,gCAAgC,EAAEH,eAAe,CAAC;QACjE;MACF,CAAC,MAAM,IAAIxE,UAAU,KAAK,aAAa,EAAE;QACvC;QACA,MAAM4E,WAAW,GAAG1E,YAAY,CAAC6B,mBAAmB,KAAK,YAAY,IAAK7B,YAAY,CAAC4B,eAAe,IAAI5B,YAAY,CAAC4B,eAAe,GAAG,CAAE;QAE3I,IAAI8C,WAAW,EAAE;UACf;UACA3C,OAAO,CAAC4C,GAAG,CAAC,sCAAsC,EAAE3E,YAAY,CAACI,OAAO,CAAC;UACzE,IAAI;YACF;YACA,MAAMgD,MAAM,GAAG,MAAM1E,WAAW,CAACkG,eAAe,CAACzF,UAAU,EAAEa,YAAY,CAACI,OAAO,CAAC;YAClF2B,OAAO,CAAC4C,GAAG,CAAC,4BAA4B,EAAEvB,MAAM,CAAC;YACjDrB,OAAO,CAAC4C,GAAG,CAAC,sCAAsC,EAAEvB,MAAM,CAACyB,sBAAsB,CAAC;YAClFzF,SAAS,CAAC,QAAQY,YAAY,CAACI,OAAO,kCAAkC,CAAC;UAC3E,CAAC,CAAC,OAAO0E,SAAS,EAAE;YAClB/C,OAAO,CAACD,KAAK,CAAC,kEAAkE,EAAEgD,SAAS,CAAC;YAC5F;YACA,MAAM1B,MAAM,GAAG,MAAM1E,WAAW,CAACqG,UAAU,CAAC5F,UAAU,EAAEa,YAAY,CAACI,OAAO,EAAE,OAAO,CAAC;YACtF2B,OAAO,CAAC4C,GAAG,CAAC,2CAA2C,EAAEvB,MAAM,CAAC;YAChEhE,SAAS,CAAC,QAAQY,YAAY,CAACI,OAAO,kCAAkC,CAAC;UAC3E;QACF,CAAC,MAAM;UACL;UACA2B,OAAO,CAAC4C,GAAG,CAAC,8CAA8C,EAAE1D,UAAU,CAAC;UACvE,MAAMmC,MAAM,GAAG,MAAM1E,WAAW,CAACqG,UAAU,CAAC5F,UAAU,EAAEa,YAAY,CAACI,OAAO,EAAEa,UAAU,CAAC;UACzFc,OAAO,CAAC4C,GAAG,CAAC,mCAAmC,EAAEvB,MAAM,CAAC;UACxDhE,SAAS,CAAC,QAAQY,YAAY,CAACI,OAAO,IAAIa,UAAU,KAAK,OAAO,GAAG,oBAAoB,GAAG,WAAW,eAAe,CAAC;QACvH;MACF;MAEAoB,iBAAiB,CAAC,CAAC;IACrB,CAAC,CAAC,OAAOP,KAAK,EAAE;MACdC,OAAO,CAACD,KAAK,CAAC,+BAA+B,EAAEA,KAAK,CAAC;;MAErD;MACA,IAAIkD,YAAY,GAAG,oBAAoB;MAEvC,IAAIlD,KAAK,CAACmD,MAAM,EAAE;QAChB;QACAD,YAAY,GAAGlD,KAAK,CAACmD,MAAM;MAC7B,CAAC,MAAM,IAAInD,KAAK,CAACyB,OAAO,EAAE;QACxB;QACAyB,YAAY,GAAGlD,KAAK,CAACyB,OAAO;MAC9B,CAAC,MAAM,IAAI,OAAOzB,KAAK,KAAK,QAAQ,EAAE;QACpC;QACAkD,YAAY,GAAGlD,KAAK;MACtB;MAEAzC,OAAO,CAAC,gCAAgC,GAAG2F,YAAY,CAAC;IAC1D,CAAC,SAAS;MACRvF,UAAU,CAAC,KAAK,CAAC;IACnB;EACF,CAAC;;EAED;EACA,MAAMyF,YAAY,GAAGA,CAAA,KAAM;IACzB,IAAIpF,UAAU,KAAK,gBAAgB,EAAE;MACnC,oBACEf,OAAA,CAACnC,MAAM;QAACuI,IAAI,EAAEvF,UAAW;QAACwF,OAAO,EAAE/C,iBAAkB;QAACgD,QAAQ,EAAC,IAAI;QAACC,SAAS;QAAAC,QAAA,gBAC3ExG,OAAA,CAAClC,WAAW;UAAA0I,QAAA,EAAC;QAAsB;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAa,CAAC,eACjD5G,OAAA,CAACjC,aAAa;UAAAyI,QAAA,EACXxE,WAAW,gBACVhC,OAAA,CAACvB,gBAAgB;YAAAgI,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,GAClB9E,IAAI,CAACwD,MAAM,KAAK,CAAC,gBACnBtF,OAAA,CAACxB,KAAK;YAACqI,QAAQ,EAAC,MAAM;YAAAL,QAAA,EAAC;UAAuB;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAO,CAAC,GACpD,CAAC3F,YAAY,gBACfjB,OAAA,CAAC7C,GAAG;YAAAqJ,QAAA,gBACFxG,OAAA,CAAC5C,UAAU;cAAC0J,OAAO,EAAC,WAAW;cAACC,YAAY;cAAAP,QAAA,EAAC;YAE7C;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAY,CAAC,eACb5G,OAAA,CAACxC,IAAI;cAAAgJ,QAAA,EACF1E,IAAI,CAACkF,GAAG,CAAErE,IAAI,iBACb3C,OAAA,CAACvC,QAAQ;gBACPwJ,MAAM;gBAENC,OAAO,EAAEA,CAAA,KAAM3D,gBAAgB,CAACZ,IAAI,CAAE;gBAAA6D,QAAA,eAEtCxG,OAAA,CAACtC,YAAY;kBACXyJ,OAAO,EAAExE,IAAI,CAACtB,OAAQ;kBACtB+F,SAAS,EAAE,GAAGzE,IAAI,CAAC0E,SAAS,IAAI,KAAK,UAAU1E,IAAI,CAAC2E,mBAAmB,IAAI,KAAK,OAAO3E,IAAI,CAAC4E,iBAAiB,IAAI,KAAK;gBAAG;kBAAAd,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAC1H;cAAC,GANGjE,IAAI,CAACtB,OAAO;gBAAAoF,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAOT,CACX;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACE,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACJ,CAAC,gBAEN5G,OAAA,CAAC7C,GAAG;YAACqK,EAAE,EAAE;cAAEC,EAAE,EAAE;YAAE,CAAE;YAAAjB,QAAA,gBACjBxG,OAAA,CAAC5C,UAAU;cAAC0J,OAAO,EAAC,WAAW;cAACC,YAAY;cAAAP,QAAA,GAAC,oBACzB,EAACvF,YAAY,CAACI,OAAO;YAAA;cAAAoF,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC7B,CAAC,eACb5G,OAAA,CAAC5C,UAAU;cAAC0J,OAAO,EAAC,OAAO;cAACC,YAAY;cAAAP,QAAA,GAAC,iBACxB,EAACvF,YAAY,CAACuC,aAAa,IAAI,KAAK;YAAA;cAAAiD,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACzC,CAAC,eACb5G,OAAA,CAAC5C,UAAU;cAAC0J,OAAO,EAAC,OAAO;cAACC,YAAY;cAAAP,QAAA,GAAC,qBACpB,EAACvF,YAAY,CAAC4B,eAAe,IAAI,GAAG;YAAA;cAAA4D,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC7C,CAAC,eACb5G,OAAA,CAAC9B,SAAS;cACRwJ,MAAM,EAAC,OAAO;cACdxD,IAAI,EAAC,cAAc;cACnByD,KAAK,EAAC,4BAA4B;cAClCC,IAAI,EAAC,QAAQ;cACbrB,SAAS;cACTO,OAAO,EAAC,UAAU;cAClB9C,KAAK,EAAE7C,QAAQ,CAACG,YAAa;cAC7BuG,QAAQ,EAAE5D,gBAAiB;cAC3B6D,QAAQ;cACR/E,KAAK,EAAE,CAAC,CAACrB,UAAU,CAACJ,YAAa;cACjCyG,UAAU,EAAErG,UAAU,CAACJ,YAAa;cACpCkG,EAAE,EAAE;gBAAEC,EAAE,EAAE;cAAE;YAAE;cAAAhB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACf,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACC;QACN;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACY,CAAC,eAChB5G,OAAA,CAAC/B,aAAa;UAAAuI,QAAA,gBACZxG,OAAA,CAAC3C,MAAM;YAAC6J,OAAO,EAAE5D,iBAAkB;YAAAkD,QAAA,EAAC;UAAO;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC,EACnD3F,YAAY,iBACXjB,OAAA,CAAC3C,MAAM;YACL6J,OAAO,EAAExC,UAAW;YACpBsD,QAAQ,EAAEvH,OAAO,IAAI,CAACU,QAAQ,CAACG,YAAa;YAC5C2G,SAAS,EAAExH,OAAO,gBAAGT,OAAA,CAACvB,gBAAgB;cAACyJ,IAAI,EAAE;YAAG;cAAAzB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,gBAAG5G,OAAA,CAACR,QAAQ;cAAAiH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAE;YAAAJ,QAAA,EACpE;UAED;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CACT;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACY,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACV,CAAC;IAEb,CAAC,MAAM,IAAI7F,UAAU,KAAK,gBAAgB,EAAE;MAC1C,oBACEf,OAAA,CAACnC,MAAM;QAACuI,IAAI,EAAEvF,UAAW;QAACwF,OAAO,EAAE/C,iBAAkB;QAACgD,QAAQ,EAAC,IAAI;QAACC,SAAS;QAAAC,QAAA,gBAC3ExG,OAAA,CAAClC,WAAW;UAAA0I,QAAA,EAAC;QAA2B;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAa,CAAC,eACtD5G,OAAA,CAACjC,aAAa;UAAAyI,QAAA,EACXxE,WAAW,gBACVhC,OAAA,CAACvB,gBAAgB;YAAAgI,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,GAClB9E,IAAI,CAACwD,MAAM,KAAK,CAAC,gBACnBtF,OAAA,CAACxB,KAAK;YAACqI,QAAQ,EAAC,MAAM;YAAAL,QAAA,EAAC;UAAuB;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAO,CAAC,GACpD,CAAC3F,YAAY,gBACfjB,OAAA,CAAC7C,GAAG;YAAAqJ,QAAA,gBACFxG,OAAA,CAAC5C,UAAU;cAAC0J,OAAO,EAAC,WAAW;cAACC,YAAY;cAAAP,QAAA,EAAC;YAE7C;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAY,CAAC,eACb5G,OAAA,CAACxC,IAAI;cAAAgJ,QAAA,EACF1E,IAAI,CAACkF,GAAG,CAAErE,IAAI,iBACb3C,OAAA,CAACvC,QAAQ;gBACPwJ,MAAM;gBAENC,OAAO,EAAEA,CAAA,KAAM3D,gBAAgB,CAACZ,IAAI,CAAE;gBAAA6D,QAAA,eAEtCxG,OAAA,CAACtC,YAAY;kBACXyJ,OAAO,EAAExE,IAAI,CAACtB,OAAQ;kBACtB+F,SAAS,EAAE,mBAAmBzE,IAAI,CAACpB,SAAS,IAAI,eAAe;gBAAG;kBAAAkF,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACnE;cAAC,GANGjE,IAAI,CAACtB,OAAO;gBAAAoF,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAOT,CACX;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACE,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACJ,CAAC,gBAEN5G,OAAA,CAAC7C,GAAG;YAACqK,EAAE,EAAE;cAAEC,EAAE,EAAE;YAAE,CAAE;YAAAjB,QAAA,gBACjBxG,OAAA,CAAC5C,UAAU;cAAC0J,OAAO,EAAC,WAAW;cAACC,YAAY;cAAAP,QAAA,GAAC,oBACzB,EAACvF,YAAY,CAACI,OAAO;YAAA;cAAAoF,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC7B,CAAC,eACb5G,OAAA,CAAC5C,UAAU;cAAC0J,OAAO,EAAC,OAAO;cAACC,YAAY;cAAAP,QAAA,GAAC,kBACvB,EAACvF,YAAY,CAACM,SAAS,IAAI,eAAe;YAAA;cAAAkF,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAChD,CAAC,eACb5G,OAAA,CAAC9B,SAAS;cACRwJ,MAAM,EAAC,OAAO;cACdxD,IAAI,EAAC,WAAW;cAChByD,KAAK,EAAC,WAAW;cACjBpB,SAAS;cACTO,OAAO,EAAC,UAAU;cAClB9C,KAAK,EAAE7C,QAAQ,CAACI,SAAU;cAC1BsG,QAAQ,EAAE5D,gBAAiB;cAC3BuD,EAAE,EAAE;gBAAEC,EAAE,EAAE;cAAE;YAAE;cAAAhB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACf,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACC;QACN;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACY,CAAC,eAChB5G,OAAA,CAAC/B,aAAa;UAAAuI,QAAA,gBACZxG,OAAA,CAAC3C,MAAM;YAAC6J,OAAO,EAAE5D,iBAAkB;YAAAkD,QAAA,EAAC;UAAO;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC,EACnD3F,YAAY,iBACXjB,OAAA,CAAC3C,MAAM;YACL6J,OAAO,EAAExC,UAAW;YACpBsD,QAAQ,EAAEvH,OAAQ;YAClBwH,SAAS,EAAExH,OAAO,gBAAGT,OAAA,CAACvB,gBAAgB;cAACyJ,IAAI,EAAE;YAAG;cAAAzB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,gBAAG5G,OAAA,CAACR,QAAQ;cAAAiH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAE;YAAAJ,QAAA,EACpE;UAED;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CACT;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACY,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACV,CAAC;IAEb,CAAC,MAAM,IAAI7F,UAAU,KAAK,eAAe,EAAE;MACzC,oBACEf,OAAA,CAACnC,MAAM;QAACuI,IAAI,EAAEvF,UAAW;QAACwF,OAAO,EAAE/C,iBAAkB;QAACgD,QAAQ,EAAC,IAAI;QAACC,SAAS;QAAAC,QAAA,gBAC3ExG,OAAA,CAAClC,WAAW;UAAA0I,QAAA,EAAC;QAAa;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAa,CAAC,eACxC5G,OAAA,CAACjC,aAAa;UAAAyI,QAAA,gBACZxG,OAAA,CAACxB,KAAK;YAACqI,QAAQ,EAAC,MAAM;YAACW,EAAE,EAAE;cAAEW,EAAE,EAAE;YAAE,CAAE;YAAA3B,QAAA,EAAC;UAGtC;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAO,CAAC,EAEP5E,WAAW,gBACVhC,OAAA,CAACvB,gBAAgB;YAAAgI,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,GAClB,CAAC3F,YAAY,gBACfjB,OAAA,CAAC7C,GAAG;YAACqK,EAAE,EAAE;cAAEY,CAAC,EAAE;YAAE,CAAE;YAAA5B,QAAA,gBAChBxG,OAAA,CAAC5C,UAAU;cAAC0J,OAAO,EAAC,WAAW;cAACC,YAAY;cAAAP,QAAA,EAAC;YAE7C;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAY,CAAC,eACb5G,OAAA,CAAC7C,GAAG;cAACqK,EAAE,EAAE;gBAAEa,OAAO,EAAE,MAAM;gBAAEC,UAAU,EAAE,QAAQ;gBAAEb,EAAE,EAAE;cAAE,CAAE;cAAAjB,QAAA,gBACxDxG,OAAA,CAAC9B,SAAS;gBACRqI,SAAS;gBACToB,KAAK,EAAC,SAAS;gBACfb,OAAO,EAAC,UAAU;gBAClB9C,KAAK,EAAExC,WAAY;gBACnBqG,QAAQ,EAAEhE,uBAAwB;gBAClC0E,WAAW,EAAC;cAAyB;gBAAA9B,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACtC,CAAC,eACF5G,OAAA,CAAC3C,MAAM;gBACLyJ,OAAO,EAAC,WAAW;gBACnB0B,KAAK,EAAC,SAAS;gBACftB,OAAO,EAAEzD,oBAAqB;gBAC9BuE,QAAQ,EAAEhG,WAAW,IAAI,CAACR,WAAW,CAACkC,IAAI,CAAC,CAAE;gBAC7C8D,EAAE,EAAE;kBAAEiB,EAAE,EAAE,CAAC;kBAAEC,QAAQ,EAAE;gBAAQ,CAAE;gBAAAlC,QAAA,EAEhCxE,WAAW,gBAAGhC,OAAA,CAACvB,gBAAgB;kBAACyJ,IAAI,EAAE;gBAAG;kBAAAzB,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAC,GAAG;cAAO;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACjD,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACN,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC,GACJ,IAAI;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACK,CAAC,eAChB5G,OAAA,CAAC/B,aAAa;UAAAuI,QAAA,eACZxG,OAAA,CAAC3C,MAAM;YAAC6J,OAAO,EAAE5D,iBAAkB;YAAAkD,QAAA,EAAC;UAAO;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACvC,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACV,CAAC;IAEb,CAAC,MAAM,IAAI7F,UAAU,KAAK,aAAa,EAAE;MACvC;MACA,MAAM4E,WAAW,GAAG1E,YAAY,KAAKA,YAAY,CAAC6B,mBAAmB,KAAK,YAAY,IAAK7B,YAAY,CAAC4B,eAAe,IAAI5B,YAAY,CAAC4B,eAAe,GAAG,CAAE,CAAC;MAE7J,oBACE7C,OAAA,CAACnC,MAAM;QAACuI,IAAI,EAAEvF,UAAW;QAACwF,OAAO,EAAE/C,iBAAkB;QAACgD,QAAQ,EAAC,IAAI;QAACC,SAAS;QAAAC,QAAA,gBAC3ExG,OAAA,CAAClC,WAAW;UAAA0I,QAAA,EACT,CAACvF,YAAY,GAAG,cAAc,GAC9B0E,WAAW,GAAG,uBAAuB,GAAG;QAAc;UAAAc,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC5C,CAAC,eACd5G,OAAA,CAACjC,aAAa;UAAAyI,QAAA,EACXxE,WAAW,gBACVhC,OAAA,CAACvB,gBAAgB;YAAAgI,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,GAClB,CAAC3F,YAAY,gBACfjB,OAAA,CAAC7C,GAAG;YAACqK,EAAE,EAAE;cAAEY,CAAC,EAAE;YAAE,CAAE;YAAA5B,QAAA,gBAChBxG,OAAA,CAAC5C,UAAU;cAAC0J,OAAO,EAAC,WAAW;cAACC,YAAY;cAAAP,QAAA,EAAC;YAE7C;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAY,CAAC,eACb5G,OAAA,CAAC7C,GAAG;cAACqK,EAAE,EAAE;gBAAEa,OAAO,EAAE,MAAM;gBAAEC,UAAU,EAAE,QAAQ;gBAAEb,EAAE,EAAE;cAAE,CAAE;cAAAjB,QAAA,gBACxDxG,OAAA,CAAC9B,SAAS;gBACRqI,SAAS;gBACToB,KAAK,EAAC,SAAS;gBACfb,OAAO,EAAC,UAAU;gBAClB9C,KAAK,EAAExC,WAAY;gBACnBqG,QAAQ,EAAEhE,uBAAwB;gBAClC0E,WAAW,EAAC;cAAyB;gBAAA9B,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACtC,CAAC,eACF5G,OAAA,CAAC3C,MAAM;gBACLyJ,OAAO,EAAC,WAAW;gBACnB0B,KAAK,EAAC,SAAS;gBACftB,OAAO,EAAEzD,oBAAqB;gBAC9BuE,QAAQ,EAAEhG,WAAW,IAAI,CAACR,WAAW,CAACkC,IAAI,CAAC,CAAE;gBAC7C8D,EAAE,EAAE;kBAAEiB,EAAE,EAAE,CAAC;kBAAEC,QAAQ,EAAE;gBAAQ,CAAE;gBAAAlC,QAAA,EAEhCxE,WAAW,gBAAGhC,OAAA,CAACvB,gBAAgB;kBAACyJ,IAAI,EAAE;gBAAG;kBAAAzB,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAC,GAAG;cAAO;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACjD,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACN,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC,GACJ7F,UAAU,KAAK,aAAa,IAAI4E,WAAW,gBAC7C3F,OAAA,CAAAE,SAAA;YAAAsG,QAAA,gBACExG,OAAA,CAAChC,iBAAiB;cAAAwI,QAAA,GAAC,UACT,eAAAxG,OAAA;gBAAAwG,QAAA,EAASvF,YAAY,CAACI;cAAO;gBAAAoF,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAS,CAAC,8CAC/C,EAAC3F,YAAY,CAAC4B,eAAe,GAAG,CAAC,iBAC/B7C,OAAA,CAAAE,SAAA;gBAAAsG,QAAA,GAAE,iBAAe,eAAAxG,OAAA;kBAAAwG,QAAA,GAASvF,YAAY,CAAC4B,eAAe,EAAC,IAAE;gBAAA;kBAAA4D,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,KAAC;cAAA,eAAE,CACtE;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACgB,CAAC,eACpB5G,OAAA,CAAChC,iBAAiB;cAACwJ,EAAE,EAAE;gBAAEC,EAAE,EAAE;cAAE,CAAE;cAAAjB,QAAA,EAAC;YAElC;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAmB,CAAC;UAAA,eACpB,CAAC,GACD7F,UAAU,KAAK,aAAa,gBAC9Bf,OAAA,CAAAE,SAAA;YAAAsG,QAAA,gBACExG,OAAA,CAAChC,iBAAiB;cAAAwI,QAAA,GAAC,6BACU,eAAAxG,OAAA;gBAAAwG,QAAA,EAASvF,YAAY,CAACI;cAAO;gBAAAoF,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAS,CAAC,KACpE;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAmB,CAAC,eAEpB5G,OAAA,CAAC7B,WAAW;cAACwK,SAAS,EAAC,UAAU;cAACnB,EAAE,EAAE;gBAAEC,EAAE,EAAE;cAAE,CAAE;cAAAjB,QAAA,gBAC9CxG,OAAA,CAAClB,SAAS;gBAAC6J,SAAS,EAAC,QAAQ;gBAAAnC,QAAA,EAAC;cAAgC;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAW,CAAC,eAC1E5G,OAAA,CAACpB,UAAU;gBACToF,KAAK,EAAE9B,UAAW;gBAClB2F,QAAQ,EAAG/D,CAAC,IAAK3B,aAAa,CAAC2B,CAAC,CAACC,MAAM,CAACC,KAAK,CAAE;gBAAAwC,QAAA,gBAE/CxG,OAAA,CAACnB,gBAAgB;kBACfmF,KAAK,EAAC,OAAO;kBACb4E,OAAO,eAAE5I,OAAA,CAACrB,KAAK;oBAAA8H,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE,CAAE;kBACnBe,KAAK,EAAC;gBAAqF;kBAAAlB,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAC5F,CAAC,eACF5G,OAAA,CAACnB,gBAAgB;kBACfmF,KAAK,EAAC,QAAQ;kBACd4E,OAAO,eAAE5I,OAAA,CAACrB,KAAK;oBAAA8H,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE,CAAE;kBACnBe,KAAK,EAAC;gBAAsE;kBAAAlB,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAC7E,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACQ,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACF,CAAC;UAAA,eACd,CAAC,GACD;QAAI;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACK,CAAC,eAChB5G,OAAA,CAAC/B,aAAa;UAAAuI,QAAA,gBACZxG,OAAA,CAAC3C,MAAM;YAAC6J,OAAO,EAAE5D,iBAAkB;YAAAkD,QAAA,EAAC;UAAO;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC,EACnD7F,UAAU,KAAK,aAAa,IAAIE,YAAY,iBAC3CjB,OAAA,CAAC3C,MAAM;YACL6J,OAAO,EAAExC,UAAW;YACpBsD,QAAQ,EAAEvH,OAAQ;YAClB+H,KAAK,EAAE7C,WAAW,GAAG,SAAS,GAAG,OAAQ;YACzCsC,SAAS,EAAExH,OAAO,gBAAGT,OAAA,CAACvB,gBAAgB;cAACyJ,IAAI,EAAE;YAAG;cAAAzB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,GAAGjB,WAAW,gBAAG3F,OAAA,CAACN,WAAW;cAAA+G,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,gBAAG5G,OAAA,CAACZ,UAAU;cAAAqH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAE;YAAAJ,QAAA,EAEpGb,WAAW,GAAG,kBAAkB,GAAIzD,UAAU,KAAK,OAAO,GAAG,kBAAkB,GAAG;UAA0B;YAAAuE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACvG,CACT;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACY,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACV,CAAC;IAEb,CAAC,MAAM,IAAI7F,UAAU,KAAK,cAAc,EAAE;MACxC,oBACEf,OAAA,CAACnC,MAAM;QAACuI,IAAI,EAAEvF,UAAW;QAACwF,OAAO,EAAE/C,iBAAkB;QAACgD,QAAQ,EAAC,IAAI;QAACC,SAAS;QAAAC,QAAA,gBAC3ExG,OAAA,CAAClC,WAAW;UAAA0I,QAAA,EAAC;QAAa;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAa,CAAC,eACxC5G,OAAA,CAACjC,aAAa;UAAAyI,QAAA,eACZxG,OAAA,CAACzB,IAAI;YAACsK,SAAS;YAACC,OAAO,EAAE,CAAE;YAACtB,EAAE,EAAE;cAAEC,EAAE,EAAE;YAAE,CAAE;YAAAjB,QAAA,gBACxCxG,OAAA,CAACzB,IAAI;cAACwK,IAAI;cAACC,EAAE,EAAE,EAAG;cAACC,EAAE,EAAE,CAAE;cAAAzC,QAAA,eACvBxG,OAAA,CAAC9B,SAAS;gBACRgG,IAAI,EAAC,SAAS;gBACdyD,KAAK,EAAC,SAAS;gBACfpB,SAAS;gBACTO,OAAO,EAAC,UAAU;gBAClB9C,KAAK,EAAE7C,QAAQ,CAACE,OAAQ;gBACxB2G,QAAQ;cAAA;gBAAAvB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACT;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACE,CAAC,eACP5G,OAAA,CAACzB,IAAI;cAACwK,IAAI;cAACC,EAAE,EAAE,EAAG;cAACC,EAAE,EAAE,CAAE;cAAAzC,QAAA,eACvBxG,OAAA,CAAC9B,SAAS;gBACRgG,IAAI,EAAC,qBAAqB;gBAC1ByD,KAAK,EAAC,qBAAqB;gBAC3BpB,SAAS;gBACTO,OAAO,EAAC,UAAU;gBAClB9C,KAAK,EAAE7C,QAAQ,CAAC+H,mBAAoB;gBACpCrB,QAAQ,EAAE5D;cAAiB;gBAAAwC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC5B;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACE,CAAC,eACP5G,OAAA,CAACzB,IAAI;cAACwK,IAAI;cAACC,EAAE,EAAE,EAAG;cAACC,EAAE,EAAE,CAAE;cAAAzC,QAAA,eACvBxG,OAAA,CAAC9B,SAAS;gBACRgG,IAAI,EAAC,SAAS;gBACdyD,KAAK,EAAC,SAAS;gBACfpB,SAAS;gBACTO,OAAO,EAAC,UAAU;gBAClB9C,KAAK,EAAE7C,QAAQ,CAACgI,OAAQ;gBACxBtB,QAAQ,EAAE5D;cAAiB;gBAAAwC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC5B;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACE,CAAC,eACP5G,OAAA,CAACzB,IAAI;cAACwK,IAAI;cAACC,EAAE,EAAE,EAAG;cAACC,EAAE,EAAE,CAAE;cAAAzC,QAAA,eACvBxG,OAAA,CAAC9B,SAAS;gBACRgG,IAAI,EAAC,SAAS;gBACdyD,KAAK,EAAC,SAAS;gBACfpB,SAAS;gBACTO,OAAO,EAAC,UAAU;gBAClB9C,KAAK,EAAE7C,QAAQ,CAACiI,OAAQ;gBACxBvB,QAAQ,EAAE5D;cAAiB;gBAAAwC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC5B;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACE,CAAC,eACP5G,OAAA,CAACzB,IAAI;cAACwK,IAAI;cAACC,EAAE,EAAE,EAAG;cAACC,EAAE,EAAE,CAAE;cAAAzC,QAAA,eACvBxG,OAAA,CAAC9B,SAAS;gBACRgG,IAAI,EAAC,aAAa;gBAClByD,KAAK,EAAC,aAAa;gBACnBpB,SAAS;gBACTO,OAAO,EAAC,UAAU;gBAClB9C,KAAK,EAAE7C,QAAQ,CAACkI,WAAY;gBAC5BxB,QAAQ,EAAE5D;cAAiB;gBAAAwC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC5B;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACE,CAAC,eACP5G,OAAA,CAACzB,IAAI;cAACwK,IAAI;cAACC,EAAE,EAAE,EAAG;cAACC,EAAE,EAAE,CAAE;cAAAzC,QAAA,eACvBxG,OAAA,CAAC9B,SAAS;gBACRgG,IAAI,EAAC,WAAW;gBAChByD,KAAK,EAAC,WAAW;gBACjBpB,SAAS;gBACTO,OAAO,EAAC,UAAU;gBAClB9C,KAAK,EAAE7C,QAAQ,CAACkG,SAAU;gBAC1BQ,QAAQ,EAAE5D;cAAiB;gBAAAwC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC5B;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACE,CAAC,eACP5G,OAAA,CAACzB,IAAI;cAACwK,IAAI;cAACC,EAAE,EAAE,EAAG;cAACC,EAAE,EAAE,CAAE;cAAAzC,QAAA,eACvBxG,OAAA,CAAC9B,SAAS;gBACRgG,IAAI,EAAC,cAAc;gBACnByD,KAAK,EAAC,mBAAmB;gBACzBpB,SAAS;gBACTO,OAAO,EAAC,UAAU;gBAClB9C,KAAK,EAAE7C,QAAQ,CAACmI,YAAa;gBAC7BzB,QAAQ,EAAE5D;cAAiB;gBAAAwC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC5B;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACE,CAAC,eACP5G,OAAA,CAACzB,IAAI;cAACwK,IAAI;cAACC,EAAE,EAAE,EAAG;cAACC,EAAE,EAAE,CAAE;cAAAzC,QAAA,eACvBxG,OAAA,CAAC9B,SAAS;gBACRgG,IAAI,EAAC,SAAS;gBACdyD,KAAK,EAAC,SAAS;gBACfpB,SAAS;gBACTO,OAAO,EAAC,UAAU;gBAClB9C,KAAK,EAAE7C,QAAQ,CAACoI,OAAQ;gBACxB1B,QAAQ,EAAE5D;cAAiB;gBAAAwC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC5B;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACE,CAAC,eACP5G,OAAA,CAACzB,IAAI;cAACwK,IAAI;cAACC,EAAE,EAAE,EAAG;cAACC,EAAE,EAAE,CAAE;cAAAzC,QAAA,eACvBxG,OAAA,CAAC9B,SAAS;gBACRgG,IAAI,EAAC,IAAI;gBACTyD,KAAK,EAAC,IAAI;gBACVpB,SAAS;gBACTO,OAAO,EAAC,UAAU;gBAClB9C,KAAK,EAAE7C,QAAQ,CAACqI,EAAE,IAAIrI,QAAQ,CAACsI,EAAG;gBAClC5B,QAAQ,EAAE5D;cAAiB;gBAAAwC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC5B;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACE,CAAC,eACP5G,OAAA,CAACzB,IAAI;cAACwK,IAAI;cAACC,EAAE,EAAE,EAAG;cAACC,EAAE,EAAE,CAAE;cAAAzC,QAAA,eACvBxG,OAAA,CAAC9B,SAAS;gBACRgG,IAAI,EAAC,qBAAqB;gBAC1ByD,KAAK,EAAC,qBAAqB;gBAC3BpB,SAAS;gBACTO,OAAO,EAAC,UAAU;gBAClB9C,KAAK,EAAE7C,QAAQ,CAACmG,mBAAoB;gBACpCO,QAAQ,EAAE5D;cAAiB;gBAAAwC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC5B;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACE,CAAC,eACP5G,OAAA,CAACzB,IAAI;cAACwK,IAAI;cAACC,EAAE,EAAE,EAAG;cAACC,EAAE,EAAE,CAAE;cAAAzC,QAAA,eACvBxG,OAAA,CAAC9B,SAAS;gBACRgG,IAAI,EAAC,iBAAiB;gBACtByD,KAAK,EAAC,iBAAiB;gBACvBpB,SAAS;gBACTO,OAAO,EAAC,UAAU;gBAClB9C,KAAK,EAAE7C,QAAQ,CAACuI,eAAgB;gBAChC7B,QAAQ,EAAE5D;cAAiB;gBAAAwC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC5B;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACE,CAAC,eACP5G,OAAA,CAACzB,IAAI;cAACwK,IAAI;cAACC,EAAE,EAAE,EAAG;cAACC,EAAE,EAAE,CAAE;cAAAzC,QAAA,eACvBxG,OAAA,CAAC9B,SAAS;gBACRgG,IAAI,EAAC,6BAA6B;gBAClCyD,KAAK,EAAC,6BAA6B;gBACnCpB,SAAS;gBACTO,OAAO,EAAC,UAAU;gBAClB9C,KAAK,EAAE7C,QAAQ,CAACwI,2BAA4B;gBAC5C9B,QAAQ,EAAE5D;cAAiB;gBAAAwC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC5B;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACE,CAAC,eACP5G,OAAA,CAACzB,IAAI;cAACwK,IAAI;cAACC,EAAE,EAAE,EAAG;cAACC,EAAE,EAAE,CAAE;cAAAzC,QAAA,eACvBxG,OAAA,CAAC9B,SAAS;gBACRgG,IAAI,EAAC,mBAAmB;gBACxByD,KAAK,EAAC,mBAAmB;gBACzBpB,SAAS;gBACTO,OAAO,EAAC,UAAU;gBAClB9C,KAAK,EAAE7C,QAAQ,CAACoG,iBAAkB;gBAClCM,QAAQ,EAAE5D;cAAiB;gBAAAwC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC5B;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACE,CAAC,eACP5G,OAAA,CAACzB,IAAI;cAACwK,IAAI;cAACC,EAAE,EAAE,EAAG;cAACC,EAAE,EAAE,CAAE;cAAAzC,QAAA,eACvBxG,OAAA,CAAC9B,SAAS;gBACRgG,IAAI,EAAC,eAAe;gBACpByD,KAAK,EAAC,eAAe;gBACrBpB,SAAS;gBACTO,OAAO,EAAC,UAAU;gBAClB9C,KAAK,EAAE7C,QAAQ,CAACyI,aAAc;gBAC9B/B,QAAQ,EAAE5D;cAAiB;gBAAAwC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC5B;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACE,CAAC,eACP5G,OAAA,CAACzB,IAAI;cAACwK,IAAI;cAACC,EAAE,EAAE,EAAG;cAACC,EAAE,EAAE,CAAE;cAAAzC,QAAA,eACvBxG,OAAA,CAAC9B,SAAS;gBACRgG,IAAI,EAAC,2BAA2B;gBAChCyD,KAAK,EAAC,2BAA2B;gBACjCpB,SAAS;gBACTO,OAAO,EAAC,UAAU;gBAClB9C,KAAK,EAAE7C,QAAQ,CAAC0I,yBAA0B;gBAC1ChC,QAAQ,EAAE5D;cAAiB;gBAAAwC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC5B;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACE,CAAC,eACP5G,OAAA,CAACzB,IAAI;cAACwK,IAAI;cAACC,EAAE,EAAE,EAAG;cAACC,EAAE,EAAE,CAAE;cAAAzC,QAAA,eACvBxG,OAAA,CAAC9B,SAAS;gBACRgG,IAAI,EAAC,eAAe;gBACpByD,KAAK,EAAC,eAAe;gBACrBC,IAAI,EAAC,QAAQ;gBACbrB,SAAS;gBACTO,OAAO,EAAC,UAAU;gBAClB9C,KAAK,EAAE7C,QAAQ,CAACqC,aAAc;gBAC9BqE,QAAQ,EAAE5D;cAAiB;gBAAAwC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC5B;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACE,CAAC,eAEP5G,OAAA,CAACzB,IAAI;cAACwK,IAAI;cAACC,EAAE,EAAE,EAAG;cAACC,EAAE,EAAE,CAAE;cAAAzC,QAAA,eACvBxG,OAAA,CAAC7B,WAAW;gBAACoI,SAAS;gBAAAC,QAAA,gBACpBxG,OAAA,CAAC5B,UAAU;kBAAAoI,QAAA,EAAC;gBAAmB;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAY,CAAC,eAC5C5G,OAAA,CAAC3B,MAAM;kBACL6F,IAAI,EAAC,qBAAqB;kBAC1BF,KAAK,EAAE7C,QAAQ,CAAC2B,mBAAoB;kBACpC6E,KAAK,EAAC,qBAAqB;kBAC3BE,QAAQ,EAAE5D,gBAAiB;kBAAAuC,QAAA,gBAE3BxG,OAAA,CAAC1B,QAAQ;oBAAC0F,KAAK,EAAC,eAAe;oBAAAwC,QAAA,EAAC;kBAAa;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAU,CAAC,eACxD5G,OAAA,CAAC1B,QAAQ;oBAAC0F,KAAK,EAAC,UAAU;oBAAAwC,QAAA,EAAC;kBAAQ;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAU,CAAC,eAC9C5G,OAAA,CAAC1B,QAAQ;oBAAC0F,KAAK,EAAC,YAAY;oBAAAwC,QAAA,EAAC;kBAAU;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAU,CAAC,eAClD5G,OAAA,CAAC1B,QAAQ;oBAAC0F,KAAK,EAAC,OAAO;oBAAAwC,QAAA,EAAC;kBAAK;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAU,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAClC,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACE;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACV,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACM,CAAC,eAChB5G,OAAA,CAAC/B,aAAa;UAAAuI,QAAA,gBACZxG,OAAA,CAAC3C,MAAM;YAAC6J,OAAO,EAAE5D,iBAAkB;YAAAkD,QAAA,EAAC;UAAO;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC,eACpD5G,OAAA,CAAC3C,MAAM;YACL6J,OAAO,EAAExC,UAAW;YACpBsD,QAAQ,EAAEvH,OAAQ;YAClBwH,SAAS,EAAExH,OAAO,gBAAGT,OAAA,CAACvB,gBAAgB;cAACyJ,IAAI,EAAE;YAAG;cAAAzB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,gBAAG5G,OAAA,CAACR,QAAQ;cAAAiH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAE;YAAAJ,QAAA,EACpE;UAED;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACI,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACV,CAAC;IAEb;IAEA,OAAO,IAAI;EACb,CAAC;EAED,oBACE5G,OAAA,CAAC7C,GAAG;IAACqK,EAAE,EAAE;MAAEa,OAAO,EAAE;IAAO,CAAE;IAAA7B,QAAA,gBAE3BxG,OAAA,CAAC7C,GAAG;MAACqK,EAAE,EAAE;QAAEsC,KAAK,EAAE,OAAO;QAAEC,EAAE,EAAE;MAAE,CAAE;MAAAvD,QAAA,eACjCxG,OAAA,CAAC1C,KAAK;QAACkK,EAAE,EAAE;UAAEY,CAAC,EAAE,CAAC;UAAED,EAAE,EAAE;QAAE,CAAE;QAAA3B,QAAA,gBACzBxG,OAAA,CAAC5C,UAAU;UAAC0J,OAAO,EAAC,IAAI;UAACC,YAAY;UAAAP,QAAA,EAAC;QAEtC;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAY,CAAC,eACb5G,OAAA,CAACzC,OAAO;UAACiK,EAAE,EAAE;YAAEW,EAAE,EAAE;UAAE;QAAE;UAAA1B,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,eAC1B5G,OAAA,CAACxC,IAAI;UAACmL,SAAS,EAAC,KAAK;UAACqB,KAAK;UAAAxD,QAAA,gBACzBxG,OAAA,CAACpC,cAAc;YAACsJ,OAAO,EAAEA,CAAA,KAAMjE,kBAAkB,CAAC,gBAAgB,CAAE;YAAAuD,QAAA,gBAClExG,OAAA,CAACrC,YAAY;cAAA6I,QAAA,eACXxG,OAAA,CAACV,SAAS;gBAAAmH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACD,CAAC,eACf5G,OAAA,CAACtC,YAAY;cAACyJ,OAAO,EAAC;YAA2B;cAAAV,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACtC,CAAC,eAEjB5G,OAAA,CAACpC,cAAc;YAACsJ,OAAO,EAAEA,CAAA,KAAMjE,kBAAkB,CAAC,gBAAgB,CAAE;YAAAuD,QAAA,gBAClExG,OAAA,CAACrC,YAAY;cAAA6I,QAAA,eACXxG,OAAA,CAACd,QAAQ;gBAAAuH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACA,CAAC,eACf5G,OAAA,CAACtC,YAAY;cAACyJ,OAAO,EAAC;YAAgC;cAAAV,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC3C,CAAC,eAEjB5G,OAAA,CAACpC,cAAc;YAACsJ,OAAO,EAAEA,CAAA,KAAMjE,kBAAkB,CAAC,cAAc,CAAE;YAAAuD,QAAA,gBAChExG,OAAA,CAACrC,YAAY;cAAA6I,QAAA,eACXxG,OAAA,CAAChB,OAAO;gBAAAyH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACC,CAAC,eACf5G,OAAA,CAACtC,YAAY;cAACyJ,OAAO,EAAC;YAAwB;cAAAV,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACnC,CAAC,eAEjB5G,OAAA,CAACpC,cAAc;YAACsJ,OAAO,EAAEA,CAAA,KAAMjE,kBAAkB,CAAC,cAAc,CAAE;YAAAuD,QAAA,gBAChExG,OAAA,CAACrC,YAAY;cAAA6I,QAAA,eACXxG,OAAA,CAACd,QAAQ;gBAAAuH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACA,CAAC,eACf5G,OAAA,CAACtC,YAAY;cAACyJ,OAAO,EAAC;YAAkB;cAAAV,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC7B,CAAC,eAEjB5G,OAAA,CAACpC,cAAc;YAACsJ,OAAO,EAAEA,CAAA,KAAMjE,kBAAkB,CAAC,aAAa,CAAE;YAAAuD,QAAA,gBAC/DxG,OAAA,CAACrC,YAAY;cAAA6I,QAAA,eACXxG,OAAA,CAACZ,UAAU;gBAAAqH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACF,CAAC,eACf5G,OAAA,CAACtC,YAAY;cAACyJ,OAAO,EAAC;YAAiB;cAAAV,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC5B,CAAC,eAEjB5G,OAAA,CAACpC,cAAc;YAACsJ,OAAO,EAAEA,CAAA,KAAMjE,kBAAkB,CAAC,kBAAkB,CAAE;YAAAuD,QAAA,gBACpExG,OAAA,CAACrC,YAAY;cAAA6I,QAAA,eACXxG,OAAA,CAACV,SAAS;gBAAAmH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACD,CAAC,eACf5G,OAAA,CAACtC,YAAY;cAACyJ,OAAO,EAAC;YAAsB;cAAAV,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACjC,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACb,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACF;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACL,CAAC,eAGN5G,OAAA,CAAC7C,GAAG;MAACqK,EAAE,EAAE;QAAEyC,QAAQ,EAAE;MAAE,CAAE;MAAAzD,QAAA,eACvBxG,OAAA,CAAC1C,KAAK;QAACkK,EAAE,EAAE;UAAEY,CAAC,EAAE,CAAC;UAAE8B,SAAS,EAAE,OAAO;UAAE7B,OAAO,EAAE,MAAM;UAAEC,UAAU,EAAE,QAAQ;UAAE6B,cAAc,EAAE;QAAS,CAAE;QAAA3D,QAAA,GACtG,CAAC7F,cAAc,iBACdX,OAAA,CAAC5C,UAAU;UAAC0J,OAAO,EAAC,OAAO;UAAAN,QAAA,EAAC;QAE5B;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAY,CACb,EACAjG,cAAc,IAAI,CAACE,UAAU,iBAC5Bb,OAAA,CAAC7C,GAAG;UAACqK,EAAE,EAAE;YAAE4C,SAAS,EAAE;UAAS,CAAE;UAAA5D,QAAA,gBAC/BxG,OAAA,CAAC5C,UAAU;YAAC0J,OAAO,EAAC,IAAI;YAACC,YAAY;YAAAP,QAAA,GAClC7F,cAAc,KAAK,gBAAgB,IAAI,wBAAwB,EAC/DA,cAAc,KAAK,cAAc,IAAI,eAAe,EACpDA,cAAc,KAAK,cAAc,IAAI,qBAAqB,EAC1DA,cAAc,KAAK,aAAa,IAAI,cAAc,EAClDA,cAAc,KAAK,gBAAgB,IAAI,6BAA6B,EACpEA,cAAc,KAAK,kBAAkB,IAAI,mBAAmB;UAAA;YAAA8F,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACnD,CAAC,eACb5G,OAAA,CAAC5C,UAAU;YAAC0J,OAAO,EAAC,OAAO;YAAAN,QAAA,EAAC;UAE5B;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAY,CAAC,eACb5G,OAAA,CAACvB,gBAAgB;YAAC+I,EAAE,EAAE;cAAEC,EAAE,EAAE;YAAE;UAAE;YAAAhB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAChC,CACN;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACI;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACL,CAAC,EAELT,YAAY,CAAC,CAAC;EAAA;IAAAM,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACZ,CAAC;AAEV,CAAC;AAACpG,EAAA,CAx2BIL,oBAAoB;AAAAkK,EAAA,GAApBlK,oBAAoB;AA02B1B,eAAeA,oBAAoB;AAAC,IAAAkK,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}