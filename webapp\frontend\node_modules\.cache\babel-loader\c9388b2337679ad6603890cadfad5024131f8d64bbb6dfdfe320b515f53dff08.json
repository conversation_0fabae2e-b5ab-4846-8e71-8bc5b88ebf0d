{"ast": null, "code": "const formatDistanceLocale = {\n  lessThanXSeconds: {\n    one: \"بىر سىكۇنت ئىچىدە\",\n    other: \"سىكۇنت ئىچىدە {{count}}\"\n  },\n  xSeconds: {\n    one: \"بىر سىكۇنت\",\n    other: \"سىكۇنت {{count}}\"\n  },\n  halfAMinute: \"يىرىم مىنۇت\",\n  lessThanXMinutes: {\n    one: \"بىر مىنۇت ئىچىدە\",\n    other: \"مىنۇت ئىچىدە {{count}}\"\n  },\n  xMinutes: {\n    one: \"بىر مىنۇت\",\n    other: \"مىنۇت {{count}}\"\n  },\n  aboutXHours: {\n    one: \"تەخمىنەن بىر سائەت\",\n    other: \"سائەت {{count}} تەخمىنەن\"\n  },\n  xHours: {\n    one: \"بىر سائەت\",\n    other: \"سائەت {{count}}\"\n  },\n  xDays: {\n    one: \"بىر كۈن\",\n    other: \"كۈن {{count}}\"\n  },\n  aboutXWeeks: {\n    one: \"تەخمىنەن بىرھەپتە\",\n    other: \"ھەپتە {{count}} تەخمىنەن\"\n  },\n  xWeeks: {\n    one: \"بىرھەپتە\",\n    other: \"ھەپتە {{count}}\"\n  },\n  aboutXMonths: {\n    one: \"تەخمىنەن بىر ئاي\",\n    other: \"ئاي {{count}} تەخمىنەن\"\n  },\n  xMonths: {\n    one: \"بىر ئاي\",\n    other: \"ئاي {{count}}\"\n  },\n  aboutXYears: {\n    one: \"تەخمىنەن بىر يىل\",\n    other: \"يىل {{count}} تەخمىنەن\"\n  },\n  xYears: {\n    one: \"بىر يىل\",\n    other: \"يىل {{count}}\"\n  },\n  overXYears: {\n    one: \"بىر يىلدىن ئارتۇق\",\n    other: \"يىلدىن ئارتۇق {{count}}\"\n  },\n  almostXYears: {\n    one: \"ئاساسەن بىر يىل\",\n    other: \"يىل {{count}} ئاساسەن\"\n  }\n};\nexport const formatDistance = (token, count, options) => {\n  let result;\n  const tokenValue = formatDistanceLocale[token];\n  if (typeof tokenValue === \"string\") {\n    result = tokenValue;\n  } else if (count === 1) {\n    result = tokenValue.one;\n  } else {\n    result = tokenValue.other.replace(\"{{count}}\", String(count));\n  }\n  if (options?.addSuffix) {\n    if (options.comparison && options.comparison > 0) {\n      return result;\n    } else {\n      return result + \" بولدى\";\n    }\n  }\n  return result;\n};", "map": {"version": 3, "names": ["formatDistanceLocale", "lessThanXSeconds", "one", "other", "xSeconds", "halfAMinute", "lessThanXMinutes", "xMinutes", "aboutXHours", "xHours", "xDays", "aboutXWeeks", "xWeeks", "aboutXMonths", "xMonths", "aboutXYears", "xYears", "overXYears", "almostXYears", "formatDistance", "token", "count", "options", "result", "tokenValue", "replace", "String", "addSuffix", "comparison"], "sources": ["C:/Users/<USER>/PycharmProjects/CMS/webapp/frontend/node_modules/date-fns/locale/ug/_lib/formatDistance.js"], "sourcesContent": ["const formatDistanceLocale = {\n  lessThanXSeconds: {\n    one: \"بىر سىكۇنت ئىچىدە\",\n    other: \"سىكۇنت ئىچىدە {{count}}\",\n  },\n\n  xSeconds: {\n    one: \"بىر سىكۇنت\",\n    other: \"سىكۇنت {{count}}\",\n  },\n\n  halfAMinute: \"يىرىم مىنۇت\",\n\n  lessThanXMinutes: {\n    one: \"بىر مىنۇت ئىچىدە\",\n    other: \"مىنۇت ئىچىدە {{count}}\",\n  },\n\n  xMinutes: {\n    one: \"بىر مىنۇت\",\n    other: \"مىنۇت {{count}}\",\n  },\n\n  aboutXHours: {\n    one: \"تەخمىنەن بىر سائەت\",\n    other: \"سائەت {{count}} تەخمىنەن\",\n  },\n\n  xHours: {\n    one: \"بىر سائەت\",\n    other: \"سائەت {{count}}\",\n  },\n\n  xDays: {\n    one: \"بىر كۈن\",\n    other: \"كۈن {{count}}\",\n  },\n\n  aboutXWeeks: {\n    one: \"تەخمىنەن بىرھەپتە\",\n    other: \"ھەپتە {{count}} تەخمىنەن\",\n  },\n\n  xWeeks: {\n    one: \"بىرھەپتە\",\n    other: \"ھەپتە {{count}}\",\n  },\n\n  aboutXMonths: {\n    one: \"تەخمىنەن بىر ئاي\",\n    other: \"ئاي {{count}} تەخمىنەن\",\n  },\n\n  xMonths: {\n    one: \"بىر ئاي\",\n    other: \"ئاي {{count}}\",\n  },\n\n  aboutXYears: {\n    one: \"تەخمىنەن بىر يىل\",\n    other: \"يىل {{count}} تەخمىنەن\",\n  },\n\n  xYears: {\n    one: \"بىر يىل\",\n    other: \"يىل {{count}}\",\n  },\n\n  overXYears: {\n    one: \"بىر يىلدىن ئارتۇق\",\n    other: \"يىلدىن ئارتۇق {{count}}\",\n  },\n\n  almostXYears: {\n    one: \"ئاساسەن بىر يىل\",\n    other: \"يىل {{count}} ئاساسەن\",\n  },\n};\n\nexport const formatDistance = (token, count, options) => {\n  let result;\n\n  const tokenValue = formatDistanceLocale[token];\n  if (typeof tokenValue === \"string\") {\n    result = tokenValue;\n  } else if (count === 1) {\n    result = tokenValue.one;\n  } else {\n    result = tokenValue.other.replace(\"{{count}}\", String(count));\n  }\n\n  if (options?.addSuffix) {\n    if (options.comparison && options.comparison > 0) {\n      return result;\n    } else {\n      return result + \" بولدى\";\n    }\n  }\n\n  return result;\n};\n"], "mappings": "AAAA,MAAMA,oBAAoB,GAAG;EAC3BC,gBAAgB,EAAE;IAChBC,GAAG,EAAE,mBAAmB;IACxBC,KAAK,EAAE;EACT,CAAC;EAEDC,QAAQ,EAAE;IACRF,GAAG,EAAE,YAAY;IACjBC,KAAK,EAAE;EACT,CAAC;EAEDE,WAAW,EAAE,aAAa;EAE1BC,gBAAgB,EAAE;IAChBJ,GAAG,EAAE,kBAAkB;IACvBC,KAAK,EAAE;EACT,CAAC;EAEDI,QAAQ,EAAE;IACRL,GAAG,EAAE,WAAW;IAChBC,KAAK,EAAE;EACT,CAAC;EAEDK,WAAW,EAAE;IACXN,GAAG,EAAE,oBAAoB;IACzBC,KAAK,EAAE;EACT,CAAC;EAEDM,MAAM,EAAE;IACNP,GAAG,EAAE,WAAW;IAChBC,KAAK,EAAE;EACT,CAAC;EAEDO,KAAK,EAAE;IACLR,GAAG,EAAE,SAAS;IACdC,KAAK,EAAE;EACT,CAAC;EAEDQ,WAAW,EAAE;IACXT,GAAG,EAAE,mBAAmB;IACxBC,KAAK,EAAE;EACT,CAAC;EAEDS,MAAM,EAAE;IACNV,GAAG,EAAE,UAAU;IACfC,KAAK,EAAE;EACT,CAAC;EAEDU,YAAY,EAAE;IACZX,GAAG,EAAE,kBAAkB;IACvBC,KAAK,EAAE;EACT,CAAC;EAEDW,OAAO,EAAE;IACPZ,GAAG,EAAE,SAAS;IACdC,KAAK,EAAE;EACT,CAAC;EAEDY,WAAW,EAAE;IACXb,GAAG,EAAE,kBAAkB;IACvBC,KAAK,EAAE;EACT,CAAC;EAEDa,MAAM,EAAE;IACNd,GAAG,EAAE,SAAS;IACdC,KAAK,EAAE;EACT,CAAC;EAEDc,UAAU,EAAE;IACVf,GAAG,EAAE,mBAAmB;IACxBC,KAAK,EAAE;EACT,CAAC;EAEDe,YAAY,EAAE;IACZhB,GAAG,EAAE,iBAAiB;IACtBC,KAAK,EAAE;EACT;AACF,CAAC;AAED,OAAO,MAAMgB,cAAc,GAAGA,CAACC,KAAK,EAAEC,KAAK,EAAEC,OAAO,KAAK;EACvD,IAAIC,MAAM;EAEV,MAAMC,UAAU,GAAGxB,oBAAoB,CAACoB,KAAK,CAAC;EAC9C,IAAI,OAAOI,UAAU,KAAK,QAAQ,EAAE;IAClCD,MAAM,GAAGC,UAAU;EACrB,CAAC,MAAM,IAAIH,KAAK,KAAK,CAAC,EAAE;IACtBE,MAAM,GAAGC,UAAU,CAACtB,GAAG;EACzB,CAAC,MAAM;IACLqB,MAAM,GAAGC,UAAU,CAACrB,KAAK,CAACsB,OAAO,CAAC,WAAW,EAAEC,MAAM,CAACL,KAAK,CAAC,CAAC;EAC/D;EAEA,IAAIC,OAAO,EAAEK,SAAS,EAAE;IACtB,IAAIL,OAAO,CAACM,UAAU,IAAIN,OAAO,CAACM,UAAU,GAAG,CAAC,EAAE;MAChD,OAAOL,MAAM;IACf,CAAC,MAAM;MACL,OAAOA,MAAM,GAAG,QAAQ;IAC1B;EACF;EAEA,OAAOA,MAAM;AACf,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}