{"ast": null, "code": "import * as React from 'react';\nconst ThemeContext = /*#__PURE__*/React.createContext(null);\nif (process.env.NODE_ENV !== 'production') {\n  ThemeContext.displayName = 'ThemeContext';\n}\nexport default ThemeContext;", "map": {"version": 3, "names": ["React", "ThemeContext", "createContext", "process", "env", "NODE_ENV", "displayName"], "sources": ["C:/CMS/webapp/frontend/node_modules/@mui/private-theming/useTheme/ThemeContext.js"], "sourcesContent": ["import * as React from 'react';\nconst ThemeContext = /*#__PURE__*/React.createContext(null);\nif (process.env.NODE_ENV !== 'production') {\n  ThemeContext.displayName = 'ThemeContext';\n}\nexport default ThemeContext;"], "mappings": "AAAA,OAAO,KAAKA,KAAK,MAAM,OAAO;AAC9B,MAAMC,YAAY,GAAG,aAAaD,KAAK,CAACE,aAAa,CAAC,IAAI,CAAC;AAC3D,IAAIC,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,EAAE;EACzCJ,YAAY,CAACK,WAAW,GAAG,cAAc;AAC3C;AACA,eAAeL,YAAY", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}