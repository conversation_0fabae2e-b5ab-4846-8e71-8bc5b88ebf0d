{"ast": null, "code": "\"use client\";\n\nimport createSvgIcon from './utils/createSvgIcon';\nimport { jsx as _jsx } from \"react/jsx-runtime\";\nexport default createSvgIcon([/*#__PURE__*/_jsx(\"path\", {\n  d: \"M6.41 6 5 7.41 9.58 12 5 16.59 6.41 18l6-6z\"\n}, \"0\"), /*#__PURE__*/_jsx(\"path\", {\n  d: \"m13 6-1.41 1.41L16.17 12l-4.58 4.59L13 18l6-6z\"\n}, \"1\")], 'KeyboardDoubleArrowRightOutlined');", "map": {"version": 3, "names": ["createSvgIcon", "jsx", "_jsx", "d"], "sources": ["C:/Users/<USER>/PycharmProjects/CMS/webapp/frontend/node_modules/@mui/icons-material/esm/KeyboardDoubleArrowRightOutlined.js"], "sourcesContent": ["\"use client\";\n\nimport createSvgIcon from './utils/createSvgIcon';\nimport { jsx as _jsx } from \"react/jsx-runtime\";\nexport default createSvgIcon([/*#__PURE__*/_jsx(\"path\", {\n  d: \"M6.41 6 5 7.41 9.58 12 5 16.59 6.41 18l6-6z\"\n}, \"0\"), /*#__PURE__*/_jsx(\"path\", {\n  d: \"m13 6-1.41 1.41L16.17 12l-4.58 4.59L13 18l6-6z\"\n}, \"1\")], 'KeyboardDoubleArrowRightOutlined');"], "mappings": "AAAA,YAAY;;AAEZ,OAAOA,aAAa,MAAM,uBAAuB;AACjD,SAASC,GAAG,IAAIC,IAAI,QAAQ,mBAAmB;AAC/C,eAAeF,aAAa,CAAC,CAAC,aAAaE,IAAI,CAAC,MAAM,EAAE;EACtDC,CAAC,EAAE;AACL,CAAC,EAAE,GAAG,CAAC,EAAE,aAAaD,IAAI,CAAC,MAAM,EAAE;EACjCC,CAAC,EAAE;AACL,CAAC,EAAE,GAAG,CAAC,CAAC,EAAE,kCAAkC,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}