{"ast": null, "code": "var _jsxFileName = \"C:\\\\CMS\\\\webapp\\\\frontend\\\\src\\\\pages\\\\UserPage.js\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect } from 'react';\nimport { Box, Typography, Paper, Button, Grid, Card, CardContent, CardActions, Dialog, DialogActions, DialogContent, DialogContentText, DialogTitle, TextField, Snackbar, Alert, IconButton, ToggleButton, ToggleButtonGroup } from '@mui/material';\nimport { Construction as ConstructionIcon, Delete as DeleteIcon, Add as AddIcon, ContentCopy as ContentCopyIcon, Info as InfoIcon, ViewList as ViewListIcon, ViewModule as ViewModuleIcon } from '@mui/icons-material';\nimport CantieriFilterableTable from '../components/cantieri/CantieriFilterableTable';\nimport EditCantiereDialog from '../components/cantieri/EditCantiereDialog';\nimport PasswordManagementDialog from '../components/cantieri/PasswordManagementDialog';\nimport { useAuth } from '../context/AuthContext';\nimport { useNavigate } from 'react-router-dom';\nimport cantieriService from '../services/cantieriService';\nimport './UserPage.css';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst UserPage = () => {\n  _s();\n  const {\n    user,\n    isImpersonating,\n    impersonatedUser,\n    selectCantiere\n  } = useAuth();\n  const navigate = useNavigate();\n  const [cantieri, setCantieri] = useState([]);\n  const [loading, setLoading] = useState(true);\n  const [error, setError] = useState(null);\n  const [openCreateDialog, setOpenCreateDialog] = useState(false);\n  const [openDeleteDialog, setOpenDeleteDialog] = useState(false);\n  const [openEditDialog, setOpenEditDialog] = useState(false);\n  const [openPasswordDialog, setOpenPasswordDialog] = useState(false);\n  const [selectedCantiere, setSelectedCantiere] = useState(null);\n  const [newCantiereData, setNewCantiereData] = useState({\n    nome: '',\n    descrizione: '',\n    password_cantiere: '',\n    conferma_password: '',\n    progetto_commessa: '',\n    codice_progetto: '',\n    riferimenti_normativi: '',\n    documentazione_progetto: ''\n  });\n  const [notification, setNotification] = useState({\n    open: false,\n    message: '',\n    severity: 'success'\n  });\n  const [viewMode, setViewMode] = useState('table'); // 'table' o 'cards'\n\n  // Carica i cantieri dell'utente corrente o dell'utente impersonato\n  useEffect(() => {\n    const fetchCantieri = async () => {\n      try {\n        setLoading(true);\n        let data;\n\n        // Se l'amministratore sta impersonando un utente, carica i cantieri di quell'utente\n        if ((user === null || user === void 0 ? void 0 : user.role) === 'owner' && isImpersonating && impersonatedUser) {\n          // Carica i cantieri dell'utente impersonato\n          data = await cantieriService.getUserCantieri(impersonatedUser.id);\n        } else {\n          // Altrimenti carica i cantieri dell'utente corrente\n          data = await cantieriService.getMyCantieri();\n        }\n        setCantieri(data);\n      } catch (err) {\n        console.error('Errore nel caricamento dei cantieri:', err);\n        setError('Impossibile caricare i cantieri. Riprova più tardi.');\n      } finally {\n        setLoading(false);\n      }\n    };\n    fetchCantieri();\n  }, [user, isImpersonating, impersonatedUser]);\n\n  // Gestisce l'apertura del dialog per creare un nuovo cantiere\n  const handleOpenCreateDialog = () => {\n    setNewCantiereData({\n      nome: '',\n      descrizione: '',\n      password_cantiere: '',\n      conferma_password: '',\n      progetto_commessa: '',\n      codice_progetto: '',\n      riferimenti_normativi: '',\n      documentazione_progetto: ''\n    });\n    setOpenCreateDialog(true);\n  };\n\n  // Gestisce la chiusura del dialog per creare un nuovo cantiere\n  const handleCloseCreateDialog = () => {\n    setOpenCreateDialog(false);\n  };\n\n  // Gestisce l'apertura del dialog per eliminare un cantiere\n  const handleOpenDeleteDialog = cantiere => {\n    setSelectedCantiere(cantiere);\n    setOpenDeleteDialog(true);\n  };\n\n  // Gestisce la chiusura del dialog per eliminare un cantiere\n  const handleCloseDeleteDialog = () => {\n    setOpenDeleteDialog(false);\n    setSelectedCantiere(null);\n  };\n\n  // Gestisce la modifica dei campi del form per creare un nuovo cantiere\n  const handleInputChange = e => {\n    const {\n      name,\n      value\n    } = e.target;\n    setNewCantiereData({\n      ...newCantiereData,\n      [name]: value\n    });\n  };\n\n  // Gestisce la creazione di un nuovo cantiere\n  const handleCreateCantiere = async () => {\n    // Verifica che i campi obbligatori siano compilati\n    if (!newCantiereData.nome || !newCantiereData.password_cantiere) {\n      setNotification({\n        open: true,\n        message: 'Il nome e la password sono obbligatori',\n        severity: 'error'\n      });\n      return;\n    }\n\n    // Verifica che le password coincidano\n    if (newCantiereData.password_cantiere !== newCantiereData.conferma_password) {\n      setNotification({\n        open: true,\n        message: 'Le password non coincidono',\n        severity: 'error'\n      });\n      return;\n    }\n    try {\n      const createdCantiere = await cantieriService.createCantiere({\n        nome: newCantiereData.nome,\n        descrizione: newCantiereData.descrizione,\n        password_cantiere: newCantiereData.password_cantiere,\n        progetto_commessa: newCantiereData.progetto_commessa || null,\n        codice_progetto: newCantiereData.codice_progetto || null,\n        riferimenti_normativi: newCantiereData.riferimenti_normativi || null,\n        documentazione_progetto: newCantiereData.documentazione_progetto || null\n      });\n\n      // Aggiorna la lista dei cantieri\n      setCantieri([...cantieri, createdCantiere]);\n\n      // Chiudi il dialog\n      handleCloseCreateDialog();\n\n      // Mostra una notifica di successo con la password\n      setNotification({\n        open: true,\n        message: `Cantiere ${createdCantiere.nome} creato con successo!\\nCodice univoco: ${createdCantiere.codice_univoco}\\nPassword: ${newCantiereData.password_cantiere}\\n\\nSalva queste informazioni in un luogo sicuro!`,\n        severity: 'success'\n      });\n    } catch (err) {\n      console.error('Errore nella creazione del cantiere:', err);\n      setNotification({\n        open: true,\n        message: 'Errore nella creazione del cantiere',\n        severity: 'error'\n      });\n    }\n  };\n\n  // Gestisce l'eliminazione di un cantiere\n  const handleDeleteCantiere = async () => {\n    if (!selectedCantiere) return;\n    try {\n      await cantieriService.deleteCantiere(selectedCantiere.id_cantiere);\n\n      // Aggiorna la lista dei cantieri\n      setCantieri(cantieri.filter(c => c.id_cantiere !== selectedCantiere.id_cantiere));\n\n      // Chiudi il dialog\n      handleCloseDeleteDialog();\n\n      // Mostra una notifica di successo\n      setNotification({\n        open: true,\n        message: `Cantiere ${selectedCantiere.nome} eliminato con successo!`,\n        severity: 'success'\n      });\n    } catch (err) {\n      console.error('Errore nell\\'eliminazione del cantiere:', err);\n      setNotification({\n        open: true,\n        message: 'Errore nell\\'eliminazione del cantiere',\n        severity: 'error'\n      });\n    }\n  };\n\n  // Gestisce la selezione di un cantiere per aprire direttamente la pagina di gestione cavi\n  const handleSelectCantiere = cantiere => {\n    console.log('Selezionato cantiere:', cantiere);\n\n    // Salva il cantiere selezionato nel contesto di autenticazione e nel localStorage\n    selectCantiere(cantiere);\n\n    // Naviga direttamente alla pagina di visualizzazione cavi\n    navigate('/dashboard/cavi/visualizza');\n  };\n\n  // Gestisce la chiusura della notifica\n  const handleCloseNotification = () => {\n    setNotification({\n      ...notification,\n      open: false\n    });\n  };\n\n  // Gestisce il cambio di modalità di visualizzazione\n  const handleViewModeChange = (event, newViewMode) => {\n    if (newViewMode !== null) {\n      setViewMode(newViewMode);\n    }\n  };\n\n  // Gestisce la copia del codice univoco\n  const handleCopyCode = codiceUnivoco => {\n    navigator.clipboard.writeText(codiceUnivoco);\n    setNotification({\n      open: true,\n      message: 'Codice univoco copiato negli appunti',\n      severity: 'success'\n    });\n  };\n\n  // Gestisce l'apertura del dialog di modifica cantiere\n  const handleOpenEditDialog = cantiere => {\n    setSelectedCantiere(cantiere);\n    setOpenEditDialog(true);\n  };\n\n  // Gestisce la chiusura del dialog di modifica cantiere\n  const handleCloseEditDialog = () => {\n    setOpenEditDialog(false);\n    setSelectedCantiere(null);\n  };\n\n  // Gestisce l'apertura del dialog di gestione password\n  const handleOpenPasswordDialog = cantiere => {\n    setSelectedCantiere(cantiere);\n    setOpenPasswordDialog(true);\n  };\n\n  // Gestisce la chiusura del dialog di gestione password\n  const handleClosePasswordDialog = () => {\n    setOpenPasswordDialog(false);\n    setSelectedCantiere(null);\n  };\n\n  // Gestisce l'aggiornamento di un cantiere\n  const handleCantiereUpdated = updatedCantiere => {\n    setCantieri(prevCantieri => prevCantieri.map(cantiere => cantiere.id_cantiere === updatedCantiere.id_cantiere ? updatedCantiere : cantiere));\n    setNotification({\n      open: true,\n      message: 'Cantiere aggiornato con successo!',\n      severity: 'success'\n    });\n  };\n  return /*#__PURE__*/_jsxDEV(Box, {\n    className: \"cantieri-page\",\n    children: [/*#__PURE__*/_jsxDEV(Typography, {\n      variant: \"h4\",\n      gutterBottom: true,\n      children: isImpersonating && impersonatedUser ? `Cantieri di ${impersonatedUser.username}` : \"I Miei Cantieri\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 299,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Box, {\n      sx: {\n        mb: 3,\n        display: 'flex',\n        justifyContent: 'space-between',\n        alignItems: 'center'\n      },\n      children: [/*#__PURE__*/_jsxDEV(Typography, {\n        variant: \"body1\",\n        children: isImpersonating && impersonatedUser ? `Visualizza e gestisci i cantieri di ${impersonatedUser.username}` : \"Visualizza e gestisci i tuoi cantieri\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 304,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Box, {\n        sx: {\n          display: 'flex',\n          alignItems: 'center',\n          gap: 2\n        },\n        children: [cantieri.length > 0 && /*#__PURE__*/_jsxDEV(ToggleButtonGroup, {\n          value: viewMode,\n          exclusive: true,\n          onChange: handleViewModeChange,\n          size: \"small\",\n          children: [/*#__PURE__*/_jsxDEV(ToggleButton, {\n            value: \"table\",\n            \"aria-label\": \"vista tabella\",\n            children: /*#__PURE__*/_jsxDEV(ViewListIcon, {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 318,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 317,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(ToggleButton, {\n            value: \"cards\",\n            \"aria-label\": \"vista schede\",\n            children: /*#__PURE__*/_jsxDEV(ViewModuleIcon, {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 321,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 320,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 311,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(Button, {\n          variant: \"contained\",\n          className: \"primary-button\",\n          startIcon: /*#__PURE__*/_jsxDEV(AddIcon, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 328,\n            columnNumber: 24\n          }, this),\n          onClick: handleOpenCreateDialog,\n          children: \"Nuovo Cantiere\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 325,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 309,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 303,\n      columnNumber: 7\n    }, this), loading ? /*#__PURE__*/_jsxDEV(Typography, {\n      children: \"Caricamento cantieri...\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 337,\n      columnNumber: 9\n    }, this) : error ? /*#__PURE__*/_jsxDEV(Alert, {\n      severity: \"error\",\n      children: error\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 339,\n      columnNumber: 9\n    }, this) : cantieri.length === 0 ? /*#__PURE__*/_jsxDEV(Paper, {\n      sx: {\n        p: 3,\n        textAlign: 'center'\n      },\n      children: [/*#__PURE__*/_jsxDEV(Typography, {\n        variant: \"h6\",\n        children: \"Nessun cantiere trovato\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 342,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(Typography, {\n        variant: \"body2\",\n        color: \"text.secondary\",\n        children: \"Crea un nuovo cantiere per iniziare\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 343,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(Button, {\n        variant: \"contained\",\n        className: \"primary-button\",\n        startIcon: /*#__PURE__*/_jsxDEV(AddIcon, {}, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 349,\n          columnNumber: 24\n        }, this),\n        onClick: handleOpenCreateDialog,\n        sx: {\n          mt: 2\n        },\n        children: \"Nuovo Cantiere\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 346,\n        columnNumber: 11\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 341,\n      columnNumber: 9\n    }, this) : viewMode === 'table' ? /*#__PURE__*/_jsxDEV(CantieriFilterableTable, {\n      cantieri: cantieri,\n      loading: loading,\n      onManageCavi: handleSelectCantiere,\n      onEdit: handleOpenEditDialog,\n      onDelete: handleOpenDeleteDialog,\n      onCopyCode: handleCopyCode,\n      onManagePassword: handleOpenPasswordDialog\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 357,\n      columnNumber: 9\n    }, this) : /*#__PURE__*/_jsxDEV(Grid, {\n      container: true,\n      spacing: 3,\n      children: cantieri.map(cantiere => /*#__PURE__*/_jsxDEV(Grid, {\n        item: true,\n        xs: 12,\n        sm: 6,\n        md: 4,\n        children: /*#__PURE__*/_jsxDEV(Card, {\n          children: [/*#__PURE__*/_jsxDEV(CardContent, {\n            children: [/*#__PURE__*/_jsxDEV(Box, {\n              className: \"cantiere-header\",\n              children: [/*#__PURE__*/_jsxDEV(ConstructionIcon, {}, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 373,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                variant: \"h6\",\n                component: \"div\",\n                children: cantiere.nome\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 374,\n                columnNumber: 21\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 372,\n              columnNumber: 19\n            }, this), /*#__PURE__*/_jsxDEV(Typography, {\n              variant: \"body2\",\n              color: \"text.secondary\",\n              sx: {\n                mb: 1\n              },\n              children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n                children: \"Descrizione:\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 379,\n                columnNumber: 21\n              }, this), \" \", cantiere.descrizione || 'N/A']\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 378,\n              columnNumber: 19\n            }, this), /*#__PURE__*/_jsxDEV(Box, {\n              sx: {\n                display: 'flex',\n                alignItems: 'center',\n                mb: 1\n              },\n              children: [/*#__PURE__*/_jsxDEV(Typography, {\n                variant: \"body2\",\n                color: \"text.secondary\",\n                sx: {\n                  mr: 1\n                },\n                children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n                  children: \"Password:\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 383,\n                  columnNumber: 23\n                }, this), \" ********\"]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 382,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(IconButton, {\n                size: \"small\",\n                onClick: () => {\n                  setNotification({\n                    open: true,\n                    message: 'Per motivi di sicurezza, la password è visibile solo al momento della creazione del cantiere.',\n                    severity: 'info'\n                  });\n                },\n                title: \"Informazioni sulla password\",\n                children: /*#__PURE__*/_jsxDEV(InfoIcon, {\n                  fontSize: \"small\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 396,\n                  columnNumber: 23\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 385,\n                columnNumber: 21\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 381,\n              columnNumber: 19\n            }, this), /*#__PURE__*/_jsxDEV(Box, {\n              sx: {\n                display: 'flex',\n                alignItems: 'center',\n                mb: 1\n              },\n              children: [/*#__PURE__*/_jsxDEV(Typography, {\n                variant: \"body2\",\n                color: \"text.secondary\",\n                sx: {\n                  mr: 1\n                },\n                children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n                  children: \"Codice Univoco:\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 401,\n                  columnNumber: 23\n                }, this), \" \", cantiere.codice_univoco || 'N/A']\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 400,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(IconButton, {\n                size: \"small\",\n                onClick: () => handleCopyCode(cantiere.codice_univoco),\n                title: \"Copia codice univoco\",\n                children: /*#__PURE__*/_jsxDEV(ContentCopyIcon, {\n                  fontSize: \"small\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 408,\n                  columnNumber: 23\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 403,\n                columnNumber: 21\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 399,\n              columnNumber: 19\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 371,\n            columnNumber: 17\n          }, this), /*#__PURE__*/_jsxDEV(CardActions, {\n            children: [/*#__PURE__*/_jsxDEV(Button, {\n              variant: \"contained\",\n              className: \"primary-button\",\n              onClick: () => handleSelectCantiere(cantiere),\n              children: \"Gestione Cavi\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 413,\n              columnNumber: 19\n            }, this), /*#__PURE__*/_jsxDEV(Button, {\n              variant: \"contained\",\n              className: \"error-button\",\n              startIcon: /*#__PURE__*/_jsxDEV(DeleteIcon, {}, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 423,\n                columnNumber: 32\n              }, this),\n              onClick: () => handleOpenDeleteDialog(cantiere),\n              children: \"Elimina\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 420,\n              columnNumber: 19\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 412,\n            columnNumber: 17\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 370,\n          columnNumber: 15\n        }, this)\n      }, cantiere.id_cantiere, false, {\n        fileName: _jsxFileName,\n        lineNumber: 369,\n        columnNumber: 13\n      }, this))\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 367,\n      columnNumber: 9\n    }, this), /*#__PURE__*/_jsxDEV(Dialog, {\n      open: openCreateDialog,\n      onClose: handleCloseCreateDialog,\n      children: [/*#__PURE__*/_jsxDEV(DialogTitle, {\n        children: \"Crea Nuovo Cantiere\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 437,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(DialogContent, {\n        children: [/*#__PURE__*/_jsxDEV(DialogContentText, {\n          children: \"Inserisci i dati per creare un nuovo cantiere.\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 439,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(TextField, {\n          autoFocus: true,\n          margin: \"dense\",\n          name: \"nome\",\n          label: \"Nome Cantiere\",\n          type: \"text\",\n          fullWidth: true,\n          variant: \"outlined\",\n          value: newCantiereData.nome,\n          onChange: handleInputChange,\n          required: true\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 442,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(TextField, {\n          margin: \"dense\",\n          name: \"descrizione\",\n          label: \"Descrizione\",\n          type: \"text\",\n          fullWidth: true,\n          variant: \"outlined\",\n          value: newCantiereData.descrizione,\n          onChange: handleInputChange\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 454,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(TextField, {\n          margin: \"dense\",\n          name: \"password_cantiere\",\n          label: \"Password\",\n          type: \"password\",\n          fullWidth: true,\n          variant: \"outlined\",\n          value: newCantiereData.password_cantiere,\n          onChange: handleInputChange,\n          required: true\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 464,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(TextField, {\n          margin: \"dense\",\n          name: \"conferma_password\",\n          label: \"Conferma Password\",\n          type: \"password\",\n          fullWidth: true,\n          variant: \"outlined\",\n          value: newCantiereData.conferma_password,\n          onChange: handleInputChange,\n          required: true\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 475,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Box, {\n          sx: {\n            mt: 3,\n            mb: 2\n          },\n          children: /*#__PURE__*/_jsxDEV(Typography, {\n            variant: \"subtitle1\",\n            gutterBottom: true,\n            sx: {\n              fontWeight: 'bold',\n              color: 'primary.main'\n            },\n            children: \"Informazioni Generali del Progetto (Opzionali)\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 489,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 488,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(TextField, {\n          margin: \"dense\",\n          name: \"progetto_commessa\",\n          label: \"Progetto/Commessa\",\n          type: \"text\",\n          fullWidth: true,\n          variant: \"outlined\",\n          value: newCantiereData.progetto_commessa,\n          onChange: handleInputChange,\n          placeholder: \"Es. Ampliamento Impianto XPTO\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 494,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(TextField, {\n          margin: \"dense\",\n          name: \"codice_progetto\",\n          label: \"Codice Progetto\",\n          type: \"text\",\n          fullWidth: true,\n          variant: \"outlined\",\n          value: newCantiereData.codice_progetto,\n          onChange: handleInputChange,\n          placeholder: \"Es. PROJ-2025-001\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 506,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(TextField, {\n          margin: \"dense\",\n          name: \"riferimenti_normativi\",\n          label: \"Riferimenti Normativi\",\n          type: \"text\",\n          fullWidth: true,\n          variant: \"outlined\",\n          value: newCantiereData.riferimenti_normativi,\n          onChange: handleInputChange,\n          placeholder: \"Es. CEI 64-8 Parte 6; Specifica Cliente XYZ Rev.2\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 518,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(TextField, {\n          margin: \"dense\",\n          name: \"documentazione_progetto\",\n          label: \"Documentazione di Progetto\",\n          type: \"text\",\n          fullWidth: true,\n          variant: \"outlined\",\n          multiline: true,\n          rows: 2,\n          value: newCantiereData.documentazione_progetto,\n          onChange: handleInputChange,\n          placeholder: \"Es. Schemi elettrici DWG-001, Layout impianto LAY-002\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 530,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 438,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(DialogActions, {\n        children: [/*#__PURE__*/_jsxDEV(Button, {\n          variant: \"contained\",\n          onClick: handleCloseCreateDialog,\n          children: \"Annulla\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 546,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Button, {\n          onClick: handleCreateCantiere,\n          variant: \"contained\",\n          className: \"primary-button\",\n          children: \"Crea\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 547,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 545,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 436,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Dialog, {\n      open: openDeleteDialog,\n      onClose: handleCloseDeleteDialog,\n      children: [/*#__PURE__*/_jsxDEV(DialogTitle, {\n        children: \"Elimina Cantiere\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 555,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(DialogContent, {\n        children: /*#__PURE__*/_jsxDEV(DialogContentText, {\n          children: [\"ATTENZIONE: Sei sicuro di voler eliminare il cantiere \\\"\", selectedCantiere === null || selectedCantiere === void 0 ? void 0 : selectedCantiere.nome, \"\\\" e tutti i suoi dati? Questa operazione non pu\\xF2 essere annullata.\"]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 557,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 556,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(DialogActions, {\n        children: [/*#__PURE__*/_jsxDEV(Button, {\n          variant: \"contained\",\n          onClick: handleCloseDeleteDialog,\n          children: \"Annulla\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 563,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Button, {\n          onClick: handleDeleteCantiere,\n          variant: \"contained\",\n          className: \"error-button\",\n          children: \"Elimina\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 564,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 562,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 554,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(EditCantiereDialog, {\n      open: openEditDialog,\n      onClose: handleCloseEditDialog,\n      cantiere: selectedCantiere,\n      onCantiereUpdated: handleCantiereUpdated\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 571,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(PasswordManagementDialog, {\n      open: openPasswordDialog,\n      onClose: handleClosePasswordDialog,\n      cantiere: selectedCantiere\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 579,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Snackbar, {\n      open: notification.open,\n      autoHideDuration: 6000,\n      onClose: handleCloseNotification,\n      anchorOrigin: {\n        vertical: 'bottom',\n        horizontal: 'center'\n      },\n      children: /*#__PURE__*/_jsxDEV(Alert, {\n        onClose: handleCloseNotification,\n        severity: notification.severity,\n        sx: {\n          width: '100%'\n        },\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          style: {\n            whiteSpace: 'pre-line'\n          },\n          children: notification.message\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 593,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 592,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 586,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 298,\n    columnNumber: 5\n  }, this);\n};\n_s(UserPage, \"w4/MeeoKB/1aBzkWozZ96YqVjEA=\", false, function () {\n  return [useAuth, useNavigate];\n});\n_c = UserPage;\nexport default UserPage;\nvar _c;\n$RefreshReg$(_c, \"UserPage\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "Box", "Typography", "Paper", "<PERSON><PERSON>", "Grid", "Card", "<PERSON><PERSON><PERSON><PERSON>", "CardActions", "Dialog", "DialogActions", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "DialogContentText", "DialogTitle", "TextField", "Snackbar", "<PERSON><PERSON>", "IconButton", "ToggleButton", "ToggleButtonGroup", "Construction", "ConstructionIcon", "Delete", "DeleteIcon", "Add", "AddIcon", "ContentCopy", "ContentCopyIcon", "Info", "InfoIcon", "ViewList", "ViewListIcon", "ViewModule", "ViewModuleIcon", "CantieriFilterableTable", "EditCantiereDialog", "PasswordManagementDialog", "useAuth", "useNavigate", "cantieriService", "jsxDEV", "_jsxDEV", "UserPage", "_s", "user", "isImpersonating", "impersonated<PERSON><PERSON>", "selectCantiere", "navigate", "cantieri", "set<PERSON><PERSON><PERSON>", "loading", "setLoading", "error", "setError", "openCreateDialog", "setOpenCreateDialog", "openDeleteDialog", "setOpenDeleteDialog", "openEditDialog", "setOpenEditDialog", "openPasswordDialog", "setOpenPasswordDialog", "selected<PERSON><PERSON><PERSON>", "setSelectedCantiere", "newCantiereData", "setNewCantiereData", "nome", "descrizione", "password_cantiere", "conferma_password", "progetto_commessa", "codice_progetto", "riferimenti_normativi", "documentazione_progetto", "notification", "setNotification", "open", "message", "severity", "viewMode", "setViewMode", "fetchCantieri", "data", "role", "getUserCantieri", "id", "getMyCantieri", "err", "console", "handleOpenCreateDialog", "handleCloseCreateDialog", "handleOpenDeleteDialog", "cantiere", "handleCloseDeleteDialog", "handleInputChange", "e", "name", "value", "target", "handleCreateCantiere", "createdCantiere", "createCantiere", "codice_univoco", "handleDeleteCantiere", "deleteCantiere", "id_cantiere", "filter", "c", "handleSelectCantiere", "log", "handleCloseNotification", "handleViewModeChange", "event", "newViewMode", "handleCopyCode", "codiceUnivoco", "navigator", "clipboard", "writeText", "handleOpenEditDialog", "handleCloseEditDialog", "handleOpenPasswordDialog", "handleClosePasswordDialog", "handleCantiereUpdated", "updatedCantiere", "prevCantieri", "map", "className", "children", "variant", "gutterBottom", "username", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "sx", "mb", "display", "justifyContent", "alignItems", "gap", "length", "exclusive", "onChange", "size", "startIcon", "onClick", "p", "textAlign", "color", "mt", "onManageCavi", "onEdit", "onDelete", "onCopyCode", "onManagePassword", "container", "spacing", "item", "xs", "sm", "md", "component", "mr", "title", "fontSize", "onClose", "autoFocus", "margin", "label", "type", "fullWidth", "required", "fontWeight", "placeholder", "multiline", "rows", "onCantiereUpdated", "autoHideDuration", "anchor<PERSON><PERSON><PERSON>", "vertical", "horizontal", "width", "style", "whiteSpace", "_c", "$RefreshReg$"], "sources": ["C:/CMS/webapp/frontend/src/pages/UserPage.js"], "sourcesContent": ["import React, { useState, useEffect } from 'react';\nimport {\n  Box,\n  Typography,\n  Paper,\n  Button,\n  Grid,\n  Card,\n  CardContent,\n  CardActions,\n  Dialog,\n  DialogActions,\n  DialogContent,\n  DialogContentText,\n  DialogTitle,\n  TextField,\n  Snackbar,\n  Alert,\n  IconButton,\n  ToggleButton,\n  ToggleButtonGroup\n} from '@mui/material';\nimport {\n  Construction as ConstructionIcon,\n  Delete as DeleteIcon,\n  Add as AddIcon,\n  ContentCopy as ContentCopyIcon,\n  Info as InfoIcon,\n  ViewList as ViewListIcon,\n  ViewModule as ViewModuleIcon\n} from '@mui/icons-material';\nimport CantieriFilterableTable from '../components/cantieri/CantieriFilterableTable';\nimport EditCantiereDialog from '../components/cantieri/EditCantiereDialog';\nimport PasswordManagementDialog from '../components/cantieri/PasswordManagementDialog';\nimport { useAuth } from '../context/AuthContext';\nimport { useNavigate } from 'react-router-dom';\nimport cantieriService from '../services/cantieriService';\nimport './UserPage.css';\n\nconst UserPage = () => {\n  const { user, isImpersonating, impersonatedUser, selectCantiere } = useAuth();\n  const navigate = useNavigate();\n  const [cantieri, setCantieri] = useState([]);\n  const [loading, setLoading] = useState(true);\n  const [error, setError] = useState(null);\n  const [openCreateDialog, setOpenCreateDialog] = useState(false);\n  const [openDeleteDialog, setOpenDeleteDialog] = useState(false);\n  const [openEditDialog, setOpenEditDialog] = useState(false);\n  const [openPasswordDialog, setOpenPasswordDialog] = useState(false);\n  const [selectedCantiere, setSelectedCantiere] = useState(null);\n  const [newCantiereData, setNewCantiereData] = useState({\n    nome: '',\n    descrizione: '',\n    password_cantiere: '',\n    conferma_password: '',\n    progetto_commessa: '',\n    codice_progetto: '',\n    riferimenti_normativi: '',\n    documentazione_progetto: ''\n  });\n  const [notification, setNotification] = useState({\n    open: false,\n    message: '',\n    severity: 'success'\n  });\n  const [viewMode, setViewMode] = useState('table'); // 'table' o 'cards'\n\n  // Carica i cantieri dell'utente corrente o dell'utente impersonato\n  useEffect(() => {\n    const fetchCantieri = async () => {\n      try {\n        setLoading(true);\n        let data;\n\n        // Se l'amministratore sta impersonando un utente, carica i cantieri di quell'utente\n        if (user?.role === 'owner' && isImpersonating && impersonatedUser) {\n          // Carica i cantieri dell'utente impersonato\n          data = await cantieriService.getUserCantieri(impersonatedUser.id);\n        } else {\n          // Altrimenti carica i cantieri dell'utente corrente\n          data = await cantieriService.getMyCantieri();\n        }\n\n        setCantieri(data);\n      } catch (err) {\n        console.error('Errore nel caricamento dei cantieri:', err);\n        setError('Impossibile caricare i cantieri. Riprova più tardi.');\n      } finally {\n        setLoading(false);\n      }\n    };\n\n    fetchCantieri();\n  }, [user, isImpersonating, impersonatedUser]);\n\n  // Gestisce l'apertura del dialog per creare un nuovo cantiere\n  const handleOpenCreateDialog = () => {\n    setNewCantiereData({\n      nome: '',\n      descrizione: '',\n      password_cantiere: '',\n      conferma_password: '',\n      progetto_commessa: '',\n      codice_progetto: '',\n      riferimenti_normativi: '',\n      documentazione_progetto: ''\n    });\n    setOpenCreateDialog(true);\n  };\n\n  // Gestisce la chiusura del dialog per creare un nuovo cantiere\n  const handleCloseCreateDialog = () => {\n    setOpenCreateDialog(false);\n  };\n\n  // Gestisce l'apertura del dialog per eliminare un cantiere\n  const handleOpenDeleteDialog = (cantiere) => {\n    setSelectedCantiere(cantiere);\n    setOpenDeleteDialog(true);\n  };\n\n  // Gestisce la chiusura del dialog per eliminare un cantiere\n  const handleCloseDeleteDialog = () => {\n    setOpenDeleteDialog(false);\n    setSelectedCantiere(null);\n  };\n\n  // Gestisce la modifica dei campi del form per creare un nuovo cantiere\n  const handleInputChange = (e) => {\n    const { name, value } = e.target;\n    setNewCantiereData({\n      ...newCantiereData,\n      [name]: value\n    });\n  };\n\n  // Gestisce la creazione di un nuovo cantiere\n  const handleCreateCantiere = async () => {\n    // Verifica che i campi obbligatori siano compilati\n    if (!newCantiereData.nome || !newCantiereData.password_cantiere) {\n      setNotification({\n        open: true,\n        message: 'Il nome e la password sono obbligatori',\n        severity: 'error'\n      });\n      return;\n    }\n\n    // Verifica che le password coincidano\n    if (newCantiereData.password_cantiere !== newCantiereData.conferma_password) {\n      setNotification({\n        open: true,\n        message: 'Le password non coincidono',\n        severity: 'error'\n      });\n      return;\n    }\n\n    try {\n      const createdCantiere = await cantieriService.createCantiere({\n        nome: newCantiereData.nome,\n        descrizione: newCantiereData.descrizione,\n        password_cantiere: newCantiereData.password_cantiere,\n        progetto_commessa: newCantiereData.progetto_commessa || null,\n        codice_progetto: newCantiereData.codice_progetto || null,\n        riferimenti_normativi: newCantiereData.riferimenti_normativi || null,\n        documentazione_progetto: newCantiereData.documentazione_progetto || null\n      });\n\n      // Aggiorna la lista dei cantieri\n      setCantieri([...cantieri, createdCantiere]);\n\n      // Chiudi il dialog\n      handleCloseCreateDialog();\n\n      // Mostra una notifica di successo con la password\n      setNotification({\n        open: true,\n        message: `Cantiere ${createdCantiere.nome} creato con successo!\\nCodice univoco: ${createdCantiere.codice_univoco}\\nPassword: ${newCantiereData.password_cantiere}\\n\\nSalva queste informazioni in un luogo sicuro!`,\n        severity: 'success'\n      });\n    } catch (err) {\n      console.error('Errore nella creazione del cantiere:', err);\n      setNotification({\n        open: true,\n        message: 'Errore nella creazione del cantiere',\n        severity: 'error'\n      });\n    }\n  };\n\n  // Gestisce l'eliminazione di un cantiere\n  const handleDeleteCantiere = async () => {\n    if (!selectedCantiere) return;\n\n    try {\n      await cantieriService.deleteCantiere(selectedCantiere.id_cantiere);\n\n      // Aggiorna la lista dei cantieri\n      setCantieri(cantieri.filter(c => c.id_cantiere !== selectedCantiere.id_cantiere));\n\n      // Chiudi il dialog\n      handleCloseDeleteDialog();\n\n      // Mostra una notifica di successo\n      setNotification({\n        open: true,\n        message: `Cantiere ${selectedCantiere.nome} eliminato con successo!`,\n        severity: 'success'\n      });\n    } catch (err) {\n      console.error('Errore nell\\'eliminazione del cantiere:', err);\n      setNotification({\n        open: true,\n        message: 'Errore nell\\'eliminazione del cantiere',\n        severity: 'error'\n      });\n    }\n  };\n\n  // Gestisce la selezione di un cantiere per aprire direttamente la pagina di gestione cavi\n  const handleSelectCantiere = (cantiere) => {\n    console.log('Selezionato cantiere:', cantiere);\n\n    // Salva il cantiere selezionato nel contesto di autenticazione e nel localStorage\n    selectCantiere(cantiere);\n\n    // Naviga direttamente alla pagina di visualizzazione cavi\n    navigate('/dashboard/cavi/visualizza');\n  };\n\n  // Gestisce la chiusura della notifica\n  const handleCloseNotification = () => {\n    setNotification({\n      ...notification,\n      open: false\n    });\n  };\n\n  // Gestisce il cambio di modalità di visualizzazione\n  const handleViewModeChange = (event, newViewMode) => {\n    if (newViewMode !== null) {\n      setViewMode(newViewMode);\n    }\n  };\n\n  // Gestisce la copia del codice univoco\n  const handleCopyCode = (codiceUnivoco) => {\n    navigator.clipboard.writeText(codiceUnivoco);\n    setNotification({\n      open: true,\n      message: 'Codice univoco copiato negli appunti',\n      severity: 'success'\n    });\n  };\n\n  // Gestisce l'apertura del dialog di modifica cantiere\n  const handleOpenEditDialog = (cantiere) => {\n    setSelectedCantiere(cantiere);\n    setOpenEditDialog(true);\n  };\n\n  // Gestisce la chiusura del dialog di modifica cantiere\n  const handleCloseEditDialog = () => {\n    setOpenEditDialog(false);\n    setSelectedCantiere(null);\n  };\n\n  // Gestisce l'apertura del dialog di gestione password\n  const handleOpenPasswordDialog = (cantiere) => {\n    setSelectedCantiere(cantiere);\n    setOpenPasswordDialog(true);\n  };\n\n  // Gestisce la chiusura del dialog di gestione password\n  const handleClosePasswordDialog = () => {\n    setOpenPasswordDialog(false);\n    setSelectedCantiere(null);\n  };\n\n  // Gestisce l'aggiornamento di un cantiere\n  const handleCantiereUpdated = (updatedCantiere) => {\n    setCantieri(prevCantieri =>\n      prevCantieri.map(cantiere =>\n        cantiere.id_cantiere === updatedCantiere.id_cantiere\n          ? updatedCantiere\n          : cantiere\n      )\n    );\n    setNotification({\n      open: true,\n      message: 'Cantiere aggiornato con successo!',\n      severity: 'success'\n    });\n  };\n\n  return (\n    <Box className=\"cantieri-page\">\n      <Typography variant=\"h4\" gutterBottom>\n        {isImpersonating && impersonatedUser ? `Cantieri di ${impersonatedUser.username}` : \"I Miei Cantieri\"}\n      </Typography>\n\n      <Box sx={{ mb: 3, display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>\n        <Typography variant=\"body1\">\n          {isImpersonating && impersonatedUser\n            ? `Visualizza e gestisci i cantieri di ${impersonatedUser.username}`\n            : \"Visualizza e gestisci i tuoi cantieri\"}\n        </Typography>\n        <Box sx={{ display: 'flex', alignItems: 'center', gap: 2 }}>\n          {cantieri.length > 0 && (\n            <ToggleButtonGroup\n              value={viewMode}\n              exclusive\n              onChange={handleViewModeChange}\n              size=\"small\"\n            >\n              <ToggleButton value=\"table\" aria-label=\"vista tabella\">\n                <ViewListIcon />\n              </ToggleButton>\n              <ToggleButton value=\"cards\" aria-label=\"vista schede\">\n                <ViewModuleIcon />\n              </ToggleButton>\n            </ToggleButtonGroup>\n          )}\n          <Button\n            variant=\"contained\"\n            className=\"primary-button\"\n            startIcon={<AddIcon />}\n            onClick={handleOpenCreateDialog}\n          >\n            Nuovo Cantiere\n          </Button>\n        </Box>\n      </Box>\n\n      {loading ? (\n        <Typography>Caricamento cantieri...</Typography>\n      ) : error ? (\n        <Alert severity=\"error\">{error}</Alert>\n      ) : cantieri.length === 0 ? (\n        <Paper sx={{ p: 3, textAlign: 'center' }}>\n          <Typography variant=\"h6\">Nessun cantiere trovato</Typography>\n          <Typography variant=\"body2\" color=\"text.secondary\">\n            Crea un nuovo cantiere per iniziare\n          </Typography>\n          <Button\n            variant=\"contained\"\n            className=\"primary-button\"\n            startIcon={<AddIcon />}\n            onClick={handleOpenCreateDialog}\n            sx={{ mt: 2 }}\n          >\n            Nuovo Cantiere\n          </Button>\n        </Paper>\n      ) : viewMode === 'table' ? (\n        <CantieriFilterableTable\n          cantieri={cantieri}\n          loading={loading}\n          onManageCavi={handleSelectCantiere}\n          onEdit={handleOpenEditDialog}\n          onDelete={handleOpenDeleteDialog}\n          onCopyCode={handleCopyCode}\n          onManagePassword={handleOpenPasswordDialog}\n        />\n      ) : (\n        <Grid container spacing={3}>\n          {cantieri.map((cantiere) => (\n            <Grid item xs={12} sm={6} md={4} key={cantiere.id_cantiere}>\n              <Card>\n                <CardContent>\n                  <Box className=\"cantiere-header\">\n                    <ConstructionIcon />\n                    <Typography variant=\"h6\" component=\"div\">\n                      {cantiere.nome}\n                    </Typography>\n                  </Box>\n                  <Typography variant=\"body2\" color=\"text.secondary\" sx={{ mb: 1 }}>\n                    <strong>Descrizione:</strong> {cantiere.descrizione || 'N/A'}\n                  </Typography>\n                  <Box sx={{ display: 'flex', alignItems: 'center', mb: 1 }}>\n                    <Typography variant=\"body2\" color=\"text.secondary\" sx={{ mr: 1 }}>\n                      <strong>Password:</strong> ********\n                    </Typography>\n                    <IconButton\n                      size=\"small\"\n                      onClick={() => {\n                        setNotification({\n                          open: true,\n                          message: 'Per motivi di sicurezza, la password è visibile solo al momento della creazione del cantiere.',\n                          severity: 'info'\n                        });\n                      }}\n                      title=\"Informazioni sulla password\"\n                    >\n                      <InfoIcon fontSize=\"small\" />\n                    </IconButton>\n                  </Box>\n                  <Box sx={{ display: 'flex', alignItems: 'center', mb: 1 }}>\n                    <Typography variant=\"body2\" color=\"text.secondary\" sx={{ mr: 1 }}>\n                      <strong>Codice Univoco:</strong> {cantiere.codice_univoco || 'N/A'}\n                    </Typography>\n                    <IconButton\n                      size=\"small\"\n                      onClick={() => handleCopyCode(cantiere.codice_univoco)}\n                      title=\"Copia codice univoco\"\n                    >\n                      <ContentCopyIcon fontSize=\"small\" />\n                    </IconButton>\n                  </Box>\n                </CardContent>\n                <CardActions>\n                  <Button\n                    variant=\"contained\"\n                    className=\"primary-button\"\n                    onClick={() => handleSelectCantiere(cantiere)}\n                  >\n                    Gestione Cavi\n                  </Button>\n                  <Button\n                    variant=\"contained\"\n                    className=\"error-button\"\n                    startIcon={<DeleteIcon />}\n                    onClick={() => handleOpenDeleteDialog(cantiere)}\n                  >\n                    Elimina\n                  </Button>\n                </CardActions>\n              </Card>\n            </Grid>\n          ))}\n        </Grid>\n      )}\n\n      {/* Dialog per creare un nuovo cantiere */}\n      <Dialog open={openCreateDialog} onClose={handleCloseCreateDialog}>\n        <DialogTitle>Crea Nuovo Cantiere</DialogTitle>\n        <DialogContent>\n          <DialogContentText>\n            Inserisci i dati per creare un nuovo cantiere.\n          </DialogContentText>\n          <TextField\n            autoFocus\n            margin=\"dense\"\n            name=\"nome\"\n            label=\"Nome Cantiere\"\n            type=\"text\"\n            fullWidth\n            variant=\"outlined\"\n            value={newCantiereData.nome}\n            onChange={handleInputChange}\n            required\n          />\n          <TextField\n            margin=\"dense\"\n            name=\"descrizione\"\n            label=\"Descrizione\"\n            type=\"text\"\n            fullWidth\n            variant=\"outlined\"\n            value={newCantiereData.descrizione}\n            onChange={handleInputChange}\n          />\n          <TextField\n            margin=\"dense\"\n            name=\"password_cantiere\"\n            label=\"Password\"\n            type=\"password\"\n            fullWidth\n            variant=\"outlined\"\n            value={newCantiereData.password_cantiere}\n            onChange={handleInputChange}\n            required\n          />\n          <TextField\n            margin=\"dense\"\n            name=\"conferma_password\"\n            label=\"Conferma Password\"\n            type=\"password\"\n            fullWidth\n            variant=\"outlined\"\n            value={newCantiereData.conferma_password}\n            onChange={handleInputChange}\n            required\n          />\n\n          {/* Sezione Informazioni Generali del Progetto */}\n          <Box sx={{ mt: 3, mb: 2 }}>\n            <Typography variant=\"subtitle1\" gutterBottom sx={{ fontWeight: 'bold', color: 'primary.main' }}>\n              Informazioni Generali del Progetto (Opzionali)\n            </Typography>\n          </Box>\n\n          <TextField\n            margin=\"dense\"\n            name=\"progetto_commessa\"\n            label=\"Progetto/Commessa\"\n            type=\"text\"\n            fullWidth\n            variant=\"outlined\"\n            value={newCantiereData.progetto_commessa}\n            onChange={handleInputChange}\n            placeholder=\"Es. Ampliamento Impianto XPTO\"\n          />\n\n          <TextField\n            margin=\"dense\"\n            name=\"codice_progetto\"\n            label=\"Codice Progetto\"\n            type=\"text\"\n            fullWidth\n            variant=\"outlined\"\n            value={newCantiereData.codice_progetto}\n            onChange={handleInputChange}\n            placeholder=\"Es. PROJ-2025-001\"\n          />\n\n          <TextField\n            margin=\"dense\"\n            name=\"riferimenti_normativi\"\n            label=\"Riferimenti Normativi\"\n            type=\"text\"\n            fullWidth\n            variant=\"outlined\"\n            value={newCantiereData.riferimenti_normativi}\n            onChange={handleInputChange}\n            placeholder=\"Es. CEI 64-8 Parte 6; Specifica Cliente XYZ Rev.2\"\n          />\n\n          <TextField\n            margin=\"dense\"\n            name=\"documentazione_progetto\"\n            label=\"Documentazione di Progetto\"\n            type=\"text\"\n            fullWidth\n            variant=\"outlined\"\n            multiline\n            rows={2}\n            value={newCantiereData.documentazione_progetto}\n            onChange={handleInputChange}\n            placeholder=\"Es. Schemi elettrici DWG-001, Layout impianto LAY-002\"\n          />\n\n        </DialogContent>\n        <DialogActions>\n          <Button variant=\"contained\" onClick={handleCloseCreateDialog}>Annulla</Button>\n          <Button onClick={handleCreateCantiere} variant=\"contained\" className=\"primary-button\">\n            Crea\n          </Button>\n        </DialogActions>\n      </Dialog>\n\n      {/* Dialog per eliminare un cantiere */}\n      <Dialog open={openDeleteDialog} onClose={handleCloseDeleteDialog}>\n        <DialogTitle>Elimina Cantiere</DialogTitle>\n        <DialogContent>\n          <DialogContentText>\n            ATTENZIONE: Sei sicuro di voler eliminare il cantiere \"{selectedCantiere?.nome}\" e tutti i suoi dati?\n            Questa operazione non può essere annullata.\n          </DialogContentText>\n        </DialogContent>\n        <DialogActions>\n          <Button variant=\"contained\" onClick={handleCloseDeleteDialog}>Annulla</Button>\n          <Button onClick={handleDeleteCantiere} variant=\"contained\" className=\"error-button\">\n            Elimina\n          </Button>\n        </DialogActions>\n      </Dialog>\n\n      {/* Dialog per modificare un cantiere */}\n      <EditCantiereDialog\n        open={openEditDialog}\n        onClose={handleCloseEditDialog}\n        cantiere={selectedCantiere}\n        onCantiereUpdated={handleCantiereUpdated}\n      />\n\n      {/* Dialog per gestire la password */}\n      <PasswordManagementDialog\n        open={openPasswordDialog}\n        onClose={handleClosePasswordDialog}\n        cantiere={selectedCantiere}\n      />\n\n      {/* Notifica */}\n      <Snackbar\n        open={notification.open}\n        autoHideDuration={6000}\n        onClose={handleCloseNotification}\n        anchorOrigin={{ vertical: 'bottom', horizontal: 'center' }}\n      >\n        <Alert onClose={handleCloseNotification} severity={notification.severity} sx={{ width: '100%' }}>\n          <div style={{ whiteSpace: 'pre-line' }}>{notification.message}</div>\n        </Alert>\n      </Snackbar>\n    </Box>\n  );\n};\n\nexport default UserPage;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAClD,SACEC,GAAG,EACHC,UAAU,EACVC,KAAK,EACLC,MAAM,EACNC,IAAI,EACJC,IAAI,EACJC,WAAW,EACXC,WAAW,EACXC,MAAM,EACNC,aAAa,EACbC,aAAa,EACbC,iBAAiB,EACjBC,WAAW,EACXC,SAAS,EACTC,QAAQ,EACRC,KAAK,EACLC,UAAU,EACVC,YAAY,EACZC,iBAAiB,QACZ,eAAe;AACtB,SACEC,YAAY,IAAIC,gBAAgB,EAChCC,MAAM,IAAIC,UAAU,EACpBC,GAAG,IAAIC,OAAO,EACdC,WAAW,IAAIC,eAAe,EAC9BC,IAAI,IAAIC,QAAQ,EAChBC,QAAQ,IAAIC,YAAY,EACxBC,UAAU,IAAIC,cAAc,QACvB,qBAAqB;AAC5B,OAAOC,uBAAuB,MAAM,gDAAgD;AACpF,OAAOC,kBAAkB,MAAM,2CAA2C;AAC1E,OAAOC,wBAAwB,MAAM,iDAAiD;AACtF,SAASC,OAAO,QAAQ,wBAAwB;AAChD,SAASC,WAAW,QAAQ,kBAAkB;AAC9C,OAAOC,eAAe,MAAM,6BAA6B;AACzD,OAAO,gBAAgB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAExB,MAAMC,QAAQ,GAAGA,CAAA,KAAM;EAAAC,EAAA;EACrB,MAAM;IAAEC,IAAI;IAAEC,eAAe;IAAEC,gBAAgB;IAAEC;EAAe,CAAC,GAAGV,OAAO,CAAC,CAAC;EAC7E,MAAMW,QAAQ,GAAGV,WAAW,CAAC,CAAC;EAC9B,MAAM,CAACW,QAAQ,EAAEC,WAAW,CAAC,GAAGnD,QAAQ,CAAC,EAAE,CAAC;EAC5C,MAAM,CAACoD,OAAO,EAAEC,UAAU,CAAC,GAAGrD,QAAQ,CAAC,IAAI,CAAC;EAC5C,MAAM,CAACsD,KAAK,EAAEC,QAAQ,CAAC,GAAGvD,QAAQ,CAAC,IAAI,CAAC;EACxC,MAAM,CAACwD,gBAAgB,EAAEC,mBAAmB,CAAC,GAAGzD,QAAQ,CAAC,KAAK,CAAC;EAC/D,MAAM,CAAC0D,gBAAgB,EAAEC,mBAAmB,CAAC,GAAG3D,QAAQ,CAAC,KAAK,CAAC;EAC/D,MAAM,CAAC4D,cAAc,EAAEC,iBAAiB,CAAC,GAAG7D,QAAQ,CAAC,KAAK,CAAC;EAC3D,MAAM,CAAC8D,kBAAkB,EAAEC,qBAAqB,CAAC,GAAG/D,QAAQ,CAAC,KAAK,CAAC;EACnE,MAAM,CAACgE,gBAAgB,EAAEC,mBAAmB,CAAC,GAAGjE,QAAQ,CAAC,IAAI,CAAC;EAC9D,MAAM,CAACkE,eAAe,EAAEC,kBAAkB,CAAC,GAAGnE,QAAQ,CAAC;IACrDoE,IAAI,EAAE,EAAE;IACRC,WAAW,EAAE,EAAE;IACfC,iBAAiB,EAAE,EAAE;IACrBC,iBAAiB,EAAE,EAAE;IACrBC,iBAAiB,EAAE,EAAE;IACrBC,eAAe,EAAE,EAAE;IACnBC,qBAAqB,EAAE,EAAE;IACzBC,uBAAuB,EAAE;EAC3B,CAAC,CAAC;EACF,MAAM,CAACC,YAAY,EAAEC,eAAe,CAAC,GAAG7E,QAAQ,CAAC;IAC/C8E,IAAI,EAAE,KAAK;IACXC,OAAO,EAAE,EAAE;IACXC,QAAQ,EAAE;EACZ,CAAC,CAAC;EACF,MAAM,CAACC,QAAQ,EAAEC,WAAW,CAAC,GAAGlF,QAAQ,CAAC,OAAO,CAAC,CAAC,CAAC;;EAEnD;EACAC,SAAS,CAAC,MAAM;IACd,MAAMkF,aAAa,GAAG,MAAAA,CAAA,KAAY;MAChC,IAAI;QACF9B,UAAU,CAAC,IAAI,CAAC;QAChB,IAAI+B,IAAI;;QAER;QACA,IAAI,CAAAvC,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAEwC,IAAI,MAAK,OAAO,IAAIvC,eAAe,IAAIC,gBAAgB,EAAE;UACjE;UACAqC,IAAI,GAAG,MAAM5C,eAAe,CAAC8C,eAAe,CAACvC,gBAAgB,CAACwC,EAAE,CAAC;QACnE,CAAC,MAAM;UACL;UACAH,IAAI,GAAG,MAAM5C,eAAe,CAACgD,aAAa,CAAC,CAAC;QAC9C;QAEArC,WAAW,CAACiC,IAAI,CAAC;MACnB,CAAC,CAAC,OAAOK,GAAG,EAAE;QACZC,OAAO,CAACpC,KAAK,CAAC,sCAAsC,EAAEmC,GAAG,CAAC;QAC1DlC,QAAQ,CAAC,qDAAqD,CAAC;MACjE,CAAC,SAAS;QACRF,UAAU,CAAC,KAAK,CAAC;MACnB;IACF,CAAC;IAED8B,aAAa,CAAC,CAAC;EACjB,CAAC,EAAE,CAACtC,IAAI,EAAEC,eAAe,EAAEC,gBAAgB,CAAC,CAAC;;EAE7C;EACA,MAAM4C,sBAAsB,GAAGA,CAAA,KAAM;IACnCxB,kBAAkB,CAAC;MACjBC,IAAI,EAAE,EAAE;MACRC,WAAW,EAAE,EAAE;MACfC,iBAAiB,EAAE,EAAE;MACrBC,iBAAiB,EAAE,EAAE;MACrBC,iBAAiB,EAAE,EAAE;MACrBC,eAAe,EAAE,EAAE;MACnBC,qBAAqB,EAAE,EAAE;MACzBC,uBAAuB,EAAE;IAC3B,CAAC,CAAC;IACFlB,mBAAmB,CAAC,IAAI,CAAC;EAC3B,CAAC;;EAED;EACA,MAAMmC,uBAAuB,GAAGA,CAAA,KAAM;IACpCnC,mBAAmB,CAAC,KAAK,CAAC;EAC5B,CAAC;;EAED;EACA,MAAMoC,sBAAsB,GAAIC,QAAQ,IAAK;IAC3C7B,mBAAmB,CAAC6B,QAAQ,CAAC;IAC7BnC,mBAAmB,CAAC,IAAI,CAAC;EAC3B,CAAC;;EAED;EACA,MAAMoC,uBAAuB,GAAGA,CAAA,KAAM;IACpCpC,mBAAmB,CAAC,KAAK,CAAC;IAC1BM,mBAAmB,CAAC,IAAI,CAAC;EAC3B,CAAC;;EAED;EACA,MAAM+B,iBAAiB,GAAIC,CAAC,IAAK;IAC/B,MAAM;MAAEC,IAAI;MAAEC;IAAM,CAAC,GAAGF,CAAC,CAACG,MAAM;IAChCjC,kBAAkB,CAAC;MACjB,GAAGD,eAAe;MAClB,CAACgC,IAAI,GAAGC;IACV,CAAC,CAAC;EACJ,CAAC;;EAED;EACA,MAAME,oBAAoB,GAAG,MAAAA,CAAA,KAAY;IACvC;IACA,IAAI,CAACnC,eAAe,CAACE,IAAI,IAAI,CAACF,eAAe,CAACI,iBAAiB,EAAE;MAC/DO,eAAe,CAAC;QACdC,IAAI,EAAE,IAAI;QACVC,OAAO,EAAE,wCAAwC;QACjDC,QAAQ,EAAE;MACZ,CAAC,CAAC;MACF;IACF;;IAEA;IACA,IAAId,eAAe,CAACI,iBAAiB,KAAKJ,eAAe,CAACK,iBAAiB,EAAE;MAC3EM,eAAe,CAAC;QACdC,IAAI,EAAE,IAAI;QACVC,OAAO,EAAE,4BAA4B;QACrCC,QAAQ,EAAE;MACZ,CAAC,CAAC;MACF;IACF;IAEA,IAAI;MACF,MAAMsB,eAAe,GAAG,MAAM9D,eAAe,CAAC+D,cAAc,CAAC;QAC3DnC,IAAI,EAAEF,eAAe,CAACE,IAAI;QAC1BC,WAAW,EAAEH,eAAe,CAACG,WAAW;QACxCC,iBAAiB,EAAEJ,eAAe,CAACI,iBAAiB;QACpDE,iBAAiB,EAAEN,eAAe,CAACM,iBAAiB,IAAI,IAAI;QAC5DC,eAAe,EAAEP,eAAe,CAACO,eAAe,IAAI,IAAI;QACxDC,qBAAqB,EAAER,eAAe,CAACQ,qBAAqB,IAAI,IAAI;QACpEC,uBAAuB,EAAET,eAAe,CAACS,uBAAuB,IAAI;MACtE,CAAC,CAAC;;MAEF;MACAxB,WAAW,CAAC,CAAC,GAAGD,QAAQ,EAAEoD,eAAe,CAAC,CAAC;;MAE3C;MACAV,uBAAuB,CAAC,CAAC;;MAEzB;MACAf,eAAe,CAAC;QACdC,IAAI,EAAE,IAAI;QACVC,OAAO,EAAE,YAAYuB,eAAe,CAAClC,IAAI,0CAA0CkC,eAAe,CAACE,cAAc,eAAetC,eAAe,CAACI,iBAAiB,mDAAmD;QACpNU,QAAQ,EAAE;MACZ,CAAC,CAAC;IACJ,CAAC,CAAC,OAAOS,GAAG,EAAE;MACZC,OAAO,CAACpC,KAAK,CAAC,sCAAsC,EAAEmC,GAAG,CAAC;MAC1DZ,eAAe,CAAC;QACdC,IAAI,EAAE,IAAI;QACVC,OAAO,EAAE,qCAAqC;QAC9CC,QAAQ,EAAE;MACZ,CAAC,CAAC;IACJ;EACF,CAAC;;EAED;EACA,MAAMyB,oBAAoB,GAAG,MAAAA,CAAA,KAAY;IACvC,IAAI,CAACzC,gBAAgB,EAAE;IAEvB,IAAI;MACF,MAAMxB,eAAe,CAACkE,cAAc,CAAC1C,gBAAgB,CAAC2C,WAAW,CAAC;;MAElE;MACAxD,WAAW,CAACD,QAAQ,CAAC0D,MAAM,CAACC,CAAC,IAAIA,CAAC,CAACF,WAAW,KAAK3C,gBAAgB,CAAC2C,WAAW,CAAC,CAAC;;MAEjF;MACAZ,uBAAuB,CAAC,CAAC;;MAEzB;MACAlB,eAAe,CAAC;QACdC,IAAI,EAAE,IAAI;QACVC,OAAO,EAAE,YAAYf,gBAAgB,CAACI,IAAI,0BAA0B;QACpEY,QAAQ,EAAE;MACZ,CAAC,CAAC;IACJ,CAAC,CAAC,OAAOS,GAAG,EAAE;MACZC,OAAO,CAACpC,KAAK,CAAC,yCAAyC,EAAEmC,GAAG,CAAC;MAC7DZ,eAAe,CAAC;QACdC,IAAI,EAAE,IAAI;QACVC,OAAO,EAAE,wCAAwC;QACjDC,QAAQ,EAAE;MACZ,CAAC,CAAC;IACJ;EACF,CAAC;;EAED;EACA,MAAM8B,oBAAoB,GAAIhB,QAAQ,IAAK;IACzCJ,OAAO,CAACqB,GAAG,CAAC,uBAAuB,EAAEjB,QAAQ,CAAC;;IAE9C;IACA9C,cAAc,CAAC8C,QAAQ,CAAC;;IAExB;IACA7C,QAAQ,CAAC,4BAA4B,CAAC;EACxC,CAAC;;EAED;EACA,MAAM+D,uBAAuB,GAAGA,CAAA,KAAM;IACpCnC,eAAe,CAAC;MACd,GAAGD,YAAY;MACfE,IAAI,EAAE;IACR,CAAC,CAAC;EACJ,CAAC;;EAED;EACA,MAAMmC,oBAAoB,GAAGA,CAACC,KAAK,EAAEC,WAAW,KAAK;IACnD,IAAIA,WAAW,KAAK,IAAI,EAAE;MACxBjC,WAAW,CAACiC,WAAW,CAAC;IAC1B;EACF,CAAC;;EAED;EACA,MAAMC,cAAc,GAAIC,aAAa,IAAK;IACxCC,SAAS,CAACC,SAAS,CAACC,SAAS,CAACH,aAAa,CAAC;IAC5CxC,eAAe,CAAC;MACdC,IAAI,EAAE,IAAI;MACVC,OAAO,EAAE,sCAAsC;MAC/CC,QAAQ,EAAE;IACZ,CAAC,CAAC;EACJ,CAAC;;EAED;EACA,MAAMyC,oBAAoB,GAAI3B,QAAQ,IAAK;IACzC7B,mBAAmB,CAAC6B,QAAQ,CAAC;IAC7BjC,iBAAiB,CAAC,IAAI,CAAC;EACzB,CAAC;;EAED;EACA,MAAM6D,qBAAqB,GAAGA,CAAA,KAAM;IAClC7D,iBAAiB,CAAC,KAAK,CAAC;IACxBI,mBAAmB,CAAC,IAAI,CAAC;EAC3B,CAAC;;EAED;EACA,MAAM0D,wBAAwB,GAAI7B,QAAQ,IAAK;IAC7C7B,mBAAmB,CAAC6B,QAAQ,CAAC;IAC7B/B,qBAAqB,CAAC,IAAI,CAAC;EAC7B,CAAC;;EAED;EACA,MAAM6D,yBAAyB,GAAGA,CAAA,KAAM;IACtC7D,qBAAqB,CAAC,KAAK,CAAC;IAC5BE,mBAAmB,CAAC,IAAI,CAAC;EAC3B,CAAC;;EAED;EACA,MAAM4D,qBAAqB,GAAIC,eAAe,IAAK;IACjD3E,WAAW,CAAC4E,YAAY,IACtBA,YAAY,CAACC,GAAG,CAAClC,QAAQ,IACvBA,QAAQ,CAACa,WAAW,KAAKmB,eAAe,CAACnB,WAAW,GAChDmB,eAAe,GACfhC,QACN,CACF,CAAC;IACDjB,eAAe,CAAC;MACdC,IAAI,EAAE,IAAI;MACVC,OAAO,EAAE,mCAAmC;MAC5CC,QAAQ,EAAE;IACZ,CAAC,CAAC;EACJ,CAAC;EAED,oBACEtC,OAAA,CAACxC,GAAG;IAAC+H,SAAS,EAAC,eAAe;IAAAC,QAAA,gBAC5BxF,OAAA,CAACvC,UAAU;MAACgI,OAAO,EAAC,IAAI;MAACC,YAAY;MAAAF,QAAA,EAClCpF,eAAe,IAAIC,gBAAgB,GAAG,eAAeA,gBAAgB,CAACsF,QAAQ,EAAE,GAAG;IAAiB;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAC3F,CAAC,eAEb/F,OAAA,CAACxC,GAAG;MAACwI,EAAE,EAAE;QAAEC,EAAE,EAAE,CAAC;QAAEC,OAAO,EAAE,MAAM;QAAEC,cAAc,EAAE,eAAe;QAAEC,UAAU,EAAE;MAAS,CAAE;MAAAZ,QAAA,gBACzFxF,OAAA,CAACvC,UAAU;QAACgI,OAAO,EAAC,OAAO;QAAAD,QAAA,EACxBpF,eAAe,IAAIC,gBAAgB,GAChC,uCAAuCA,gBAAgB,CAACsF,QAAQ,EAAE,GAClE;MAAuC;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACjC,CAAC,eACb/F,OAAA,CAACxC,GAAG;QAACwI,EAAE,EAAE;UAAEE,OAAO,EAAE,MAAM;UAAEE,UAAU,EAAE,QAAQ;UAAEC,GAAG,EAAE;QAAE,CAAE;QAAAb,QAAA,GACxDhF,QAAQ,CAAC8F,MAAM,GAAG,CAAC,iBAClBtG,OAAA,CAACtB,iBAAiB;UAChB+E,KAAK,EAAElB,QAAS;UAChBgE,SAAS;UACTC,QAAQ,EAAEjC,oBAAqB;UAC/BkC,IAAI,EAAC,OAAO;UAAAjB,QAAA,gBAEZxF,OAAA,CAACvB,YAAY;YAACgF,KAAK,EAAC,OAAO;YAAC,cAAW,eAAe;YAAA+B,QAAA,eACpDxF,OAAA,CAACV,YAAY;cAAAsG,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACJ,CAAC,eACf/F,OAAA,CAACvB,YAAY;YAACgF,KAAK,EAAC,OAAO;YAAC,cAAW,cAAc;YAAA+B,QAAA,eACnDxF,OAAA,CAACR,cAAc;cAAAoG,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACN,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACE,CACpB,eACD/F,OAAA,CAACrC,MAAM;UACL8H,OAAO,EAAC,WAAW;UACnBF,SAAS,EAAC,gBAAgB;UAC1BmB,SAAS,eAAE1G,OAAA,CAAChB,OAAO;YAAA4G,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAE;UACvBY,OAAO,EAAE1D,sBAAuB;UAAAuC,QAAA,EACjC;QAED;UAAAI,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACN,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,EAELrF,OAAO,gBACNV,OAAA,CAACvC,UAAU;MAAA+H,QAAA,EAAC;IAAuB;MAAAI,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAY,CAAC,GAC9CnF,KAAK,gBACPZ,OAAA,CAACzB,KAAK;MAAC+D,QAAQ,EAAC,OAAO;MAAAkD,QAAA,EAAE5E;IAAK;MAAAgF,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAQ,CAAC,GACrCvF,QAAQ,CAAC8F,MAAM,KAAK,CAAC,gBACvBtG,OAAA,CAACtC,KAAK;MAACsI,EAAE,EAAE;QAAEY,CAAC,EAAE,CAAC;QAAEC,SAAS,EAAE;MAAS,CAAE;MAAArB,QAAA,gBACvCxF,OAAA,CAACvC,UAAU;QAACgI,OAAO,EAAC,IAAI;QAAAD,QAAA,EAAC;MAAuB;QAAAI,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAY,CAAC,eAC7D/F,OAAA,CAACvC,UAAU;QAACgI,OAAO,EAAC,OAAO;QAACqB,KAAK,EAAC,gBAAgB;QAAAtB,QAAA,EAAC;MAEnD;QAAAI,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAY,CAAC,eACb/F,OAAA,CAACrC,MAAM;QACL8H,OAAO,EAAC,WAAW;QACnBF,SAAS,EAAC,gBAAgB;QAC1BmB,SAAS,eAAE1G,OAAA,CAAChB,OAAO;UAAA4G,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAE;QACvBY,OAAO,EAAE1D,sBAAuB;QAChC+C,EAAE,EAAE;UAAEe,EAAE,EAAE;QAAE,CAAE;QAAAvB,QAAA,EACf;MAED;QAAAI,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAQ,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACJ,CAAC,GACNxD,QAAQ,KAAK,OAAO,gBACtBvC,OAAA,CAACP,uBAAuB;MACtBe,QAAQ,EAAEA,QAAS;MACnBE,OAAO,EAAEA,OAAQ;MACjBsG,YAAY,EAAE5C,oBAAqB;MACnC6C,MAAM,EAAElC,oBAAqB;MAC7BmC,QAAQ,EAAE/D,sBAAuB;MACjCgE,UAAU,EAAEzC,cAAe;MAC3B0C,gBAAgB,EAAEnC;IAAyB;MAAAW,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAC5C,CAAC,gBAEF/F,OAAA,CAACpC,IAAI;MAACyJ,SAAS;MAACC,OAAO,EAAE,CAAE;MAAA9B,QAAA,EACxBhF,QAAQ,CAAC8E,GAAG,CAAElC,QAAQ,iBACrBpD,OAAA,CAACpC,IAAI;QAAC2J,IAAI;QAACC,EAAE,EAAE,EAAG;QAACC,EAAE,EAAE,CAAE;QAACC,EAAE,EAAE,CAAE;QAAAlC,QAAA,eAC9BxF,OAAA,CAACnC,IAAI;UAAA2H,QAAA,gBACHxF,OAAA,CAAClC,WAAW;YAAA0H,QAAA,gBACVxF,OAAA,CAACxC,GAAG;cAAC+H,SAAS,EAAC,iBAAiB;cAAAC,QAAA,gBAC9BxF,OAAA,CAACpB,gBAAgB;gBAAAgH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,eACpB/F,OAAA,CAACvC,UAAU;gBAACgI,OAAO,EAAC,IAAI;gBAACkC,SAAS,EAAC,KAAK;gBAAAnC,QAAA,EACrCpC,QAAQ,CAAC1B;cAAI;gBAAAkE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACJ,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACV,CAAC,eACN/F,OAAA,CAACvC,UAAU;cAACgI,OAAO,EAAC,OAAO;cAACqB,KAAK,EAAC,gBAAgB;cAACd,EAAE,EAAE;gBAAEC,EAAE,EAAE;cAAE,CAAE;cAAAT,QAAA,gBAC/DxF,OAAA;gBAAAwF,QAAA,EAAQ;cAAY;gBAAAI,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC,KAAC,EAAC3C,QAAQ,CAACzB,WAAW,IAAI,KAAK;YAAA;cAAAiE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAClD,CAAC,eACb/F,OAAA,CAACxC,GAAG;cAACwI,EAAE,EAAE;gBAAEE,OAAO,EAAE,MAAM;gBAAEE,UAAU,EAAE,QAAQ;gBAAEH,EAAE,EAAE;cAAE,CAAE;cAAAT,QAAA,gBACxDxF,OAAA,CAACvC,UAAU;gBAACgI,OAAO,EAAC,OAAO;gBAACqB,KAAK,EAAC,gBAAgB;gBAACd,EAAE,EAAE;kBAAE4B,EAAE,EAAE;gBAAE,CAAE;gBAAApC,QAAA,gBAC/DxF,OAAA;kBAAAwF,QAAA,EAAQ;gBAAS;kBAAAI,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,aAC5B;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAY,CAAC,eACb/F,OAAA,CAACxB,UAAU;gBACTiI,IAAI,EAAC,OAAO;gBACZE,OAAO,EAAEA,CAAA,KAAM;kBACbxE,eAAe,CAAC;oBACdC,IAAI,EAAE,IAAI;oBACVC,OAAO,EAAE,+FAA+F;oBACxGC,QAAQ,EAAE;kBACZ,CAAC,CAAC;gBACJ,CAAE;gBACFuF,KAAK,EAAC,6BAA6B;gBAAArC,QAAA,eAEnCxF,OAAA,CAACZ,QAAQ;kBAAC0I,QAAQ,EAAC;gBAAO;kBAAAlC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACnB,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACV,CAAC,eACN/F,OAAA,CAACxC,GAAG;cAACwI,EAAE,EAAE;gBAAEE,OAAO,EAAE,MAAM;gBAAEE,UAAU,EAAE,QAAQ;gBAAEH,EAAE,EAAE;cAAE,CAAE;cAAAT,QAAA,gBACxDxF,OAAA,CAACvC,UAAU;gBAACgI,OAAO,EAAC,OAAO;gBAACqB,KAAK,EAAC,gBAAgB;gBAACd,EAAE,EAAE;kBAAE4B,EAAE,EAAE;gBAAE,CAAE;gBAAApC,QAAA,gBAC/DxF,OAAA;kBAAAwF,QAAA,EAAQ;gBAAe;kBAAAI,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,KAAC,EAAC3C,QAAQ,CAACU,cAAc,IAAI,KAAK;cAAA;gBAAA8B,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACxD,CAAC,eACb/F,OAAA,CAACxB,UAAU;gBACTiI,IAAI,EAAC,OAAO;gBACZE,OAAO,EAAEA,CAAA,KAAMjC,cAAc,CAACtB,QAAQ,CAACU,cAAc,CAAE;gBACvD+D,KAAK,EAAC,sBAAsB;gBAAArC,QAAA,eAE5BxF,OAAA,CAACd,eAAe;kBAAC4I,QAAQ,EAAC;gBAAO;kBAAAlC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC1B,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACV,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACK,CAAC,eACd/F,OAAA,CAACjC,WAAW;YAAAyH,QAAA,gBACVxF,OAAA,CAACrC,MAAM;cACL8H,OAAO,EAAC,WAAW;cACnBF,SAAS,EAAC,gBAAgB;cAC1BoB,OAAO,EAAEA,CAAA,KAAMvC,oBAAoB,CAAChB,QAAQ,CAAE;cAAAoC,QAAA,EAC/C;YAED;cAAAI,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC,eACT/F,OAAA,CAACrC,MAAM;cACL8H,OAAO,EAAC,WAAW;cACnBF,SAAS,EAAC,cAAc;cACxBmB,SAAS,eAAE1G,OAAA,CAAClB,UAAU;gBAAA8G,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAE;cAC1BY,OAAO,EAAEA,CAAA,KAAMxD,sBAAsB,CAACC,QAAQ,CAAE;cAAAoC,QAAA,EACjD;YAED;cAAAI,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACE,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACV;MAAC,GA5D6B3C,QAAQ,CAACa,WAAW;QAAA2B,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OA6DpD,CACP;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACE,CACP,eAGD/F,OAAA,CAAChC,MAAM;MAACoE,IAAI,EAAEtB,gBAAiB;MAACiH,OAAO,EAAE7E,uBAAwB;MAAAsC,QAAA,gBAC/DxF,OAAA,CAAC5B,WAAW;QAAAoH,QAAA,EAAC;MAAmB;QAAAI,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAa,CAAC,eAC9C/F,OAAA,CAAC9B,aAAa;QAAAsH,QAAA,gBACZxF,OAAA,CAAC7B,iBAAiB;UAAAqH,QAAA,EAAC;QAEnB;UAAAI,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAmB,CAAC,eACpB/F,OAAA,CAAC3B,SAAS;UACR2J,SAAS;UACTC,MAAM,EAAC,OAAO;UACdzE,IAAI,EAAC,MAAM;UACX0E,KAAK,EAAC,eAAe;UACrBC,IAAI,EAAC,MAAM;UACXC,SAAS;UACT3C,OAAO,EAAC,UAAU;UAClBhC,KAAK,EAAEjC,eAAe,CAACE,IAAK;UAC5B8E,QAAQ,EAAElD,iBAAkB;UAC5B+E,QAAQ;QAAA;UAAAzC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACT,CAAC,eACF/F,OAAA,CAAC3B,SAAS;UACR4J,MAAM,EAAC,OAAO;UACdzE,IAAI,EAAC,aAAa;UAClB0E,KAAK,EAAC,aAAa;UACnBC,IAAI,EAAC,MAAM;UACXC,SAAS;UACT3C,OAAO,EAAC,UAAU;UAClBhC,KAAK,EAAEjC,eAAe,CAACG,WAAY;UACnC6E,QAAQ,EAAElD;QAAkB;UAAAsC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC7B,CAAC,eACF/F,OAAA,CAAC3B,SAAS;UACR4J,MAAM,EAAC,OAAO;UACdzE,IAAI,EAAC,mBAAmB;UACxB0E,KAAK,EAAC,UAAU;UAChBC,IAAI,EAAC,UAAU;UACfC,SAAS;UACT3C,OAAO,EAAC,UAAU;UAClBhC,KAAK,EAAEjC,eAAe,CAACI,iBAAkB;UACzC4E,QAAQ,EAAElD,iBAAkB;UAC5B+E,QAAQ;QAAA;UAAAzC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACT,CAAC,eACF/F,OAAA,CAAC3B,SAAS;UACR4J,MAAM,EAAC,OAAO;UACdzE,IAAI,EAAC,mBAAmB;UACxB0E,KAAK,EAAC,mBAAmB;UACzBC,IAAI,EAAC,UAAU;UACfC,SAAS;UACT3C,OAAO,EAAC,UAAU;UAClBhC,KAAK,EAAEjC,eAAe,CAACK,iBAAkB;UACzC2E,QAAQ,EAAElD,iBAAkB;UAC5B+E,QAAQ;QAAA;UAAAzC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACT,CAAC,eAGF/F,OAAA,CAACxC,GAAG;UAACwI,EAAE,EAAE;YAAEe,EAAE,EAAE,CAAC;YAAEd,EAAE,EAAE;UAAE,CAAE;UAAAT,QAAA,eACxBxF,OAAA,CAACvC,UAAU;YAACgI,OAAO,EAAC,WAAW;YAACC,YAAY;YAACM,EAAE,EAAE;cAAEsC,UAAU,EAAE,MAAM;cAAExB,KAAK,EAAE;YAAe,CAAE;YAAAtB,QAAA,EAAC;UAEhG;YAAAI,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAY;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACV,CAAC,eAEN/F,OAAA,CAAC3B,SAAS;UACR4J,MAAM,EAAC,OAAO;UACdzE,IAAI,EAAC,mBAAmB;UACxB0E,KAAK,EAAC,mBAAmB;UACzBC,IAAI,EAAC,MAAM;UACXC,SAAS;UACT3C,OAAO,EAAC,UAAU;UAClBhC,KAAK,EAAEjC,eAAe,CAACM,iBAAkB;UACzC0E,QAAQ,EAAElD,iBAAkB;UAC5BiF,WAAW,EAAC;QAA+B;UAAA3C,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC5C,CAAC,eAEF/F,OAAA,CAAC3B,SAAS;UACR4J,MAAM,EAAC,OAAO;UACdzE,IAAI,EAAC,iBAAiB;UACtB0E,KAAK,EAAC,iBAAiB;UACvBC,IAAI,EAAC,MAAM;UACXC,SAAS;UACT3C,OAAO,EAAC,UAAU;UAClBhC,KAAK,EAAEjC,eAAe,CAACO,eAAgB;UACvCyE,QAAQ,EAAElD,iBAAkB;UAC5BiF,WAAW,EAAC;QAAmB;UAAA3C,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAChC,CAAC,eAEF/F,OAAA,CAAC3B,SAAS;UACR4J,MAAM,EAAC,OAAO;UACdzE,IAAI,EAAC,uBAAuB;UAC5B0E,KAAK,EAAC,uBAAuB;UAC7BC,IAAI,EAAC,MAAM;UACXC,SAAS;UACT3C,OAAO,EAAC,UAAU;UAClBhC,KAAK,EAAEjC,eAAe,CAACQ,qBAAsB;UAC7CwE,QAAQ,EAAElD,iBAAkB;UAC5BiF,WAAW,EAAC;QAAmD;UAAA3C,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAChE,CAAC,eAEF/F,OAAA,CAAC3B,SAAS;UACR4J,MAAM,EAAC,OAAO;UACdzE,IAAI,EAAC,yBAAyB;UAC9B0E,KAAK,EAAC,4BAA4B;UAClCC,IAAI,EAAC,MAAM;UACXC,SAAS;UACT3C,OAAO,EAAC,UAAU;UAClB+C,SAAS;UACTC,IAAI,EAAE,CAAE;UACRhF,KAAK,EAAEjC,eAAe,CAACS,uBAAwB;UAC/CuE,QAAQ,EAAElD,iBAAkB;UAC5BiF,WAAW,EAAC;QAAuD;UAAA3C,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACpE,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAEW,CAAC,eAChB/F,OAAA,CAAC/B,aAAa;QAAAuH,QAAA,gBACZxF,OAAA,CAACrC,MAAM;UAAC8H,OAAO,EAAC,WAAW;UAACkB,OAAO,EAAEzD,uBAAwB;UAAAsC,QAAA,EAAC;QAAO;UAAAI,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC,eAC9E/F,OAAA,CAACrC,MAAM;UAACgJ,OAAO,EAAEhD,oBAAqB;UAAC8B,OAAO,EAAC,WAAW;UAACF,SAAS,EAAC,gBAAgB;UAAAC,QAAA,EAAC;QAEtF;UAAAI,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACI,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACV,CAAC,eAGT/F,OAAA,CAAChC,MAAM;MAACoE,IAAI,EAAEpB,gBAAiB;MAAC+G,OAAO,EAAE1E,uBAAwB;MAAAmC,QAAA,gBAC/DxF,OAAA,CAAC5B,WAAW;QAAAoH,QAAA,EAAC;MAAgB;QAAAI,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAa,CAAC,eAC3C/F,OAAA,CAAC9B,aAAa;QAAAsH,QAAA,eACZxF,OAAA,CAAC7B,iBAAiB;UAAAqH,QAAA,GAAC,0DACsC,EAAClE,gBAAgB,aAAhBA,gBAAgB,uBAAhBA,gBAAgB,CAAEI,IAAI,EAAC,wEAEjF;QAAA;UAAAkE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAmB;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACP,CAAC,eAChB/F,OAAA,CAAC/B,aAAa;QAAAuH,QAAA,gBACZxF,OAAA,CAACrC,MAAM;UAAC8H,OAAO,EAAC,WAAW;UAACkB,OAAO,EAAEtD,uBAAwB;UAAAmC,QAAA,EAAC;QAAO;UAAAI,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC,eAC9E/F,OAAA,CAACrC,MAAM;UAACgJ,OAAO,EAAE5C,oBAAqB;UAAC0B,OAAO,EAAC,WAAW;UAACF,SAAS,EAAC,cAAc;UAAAC,QAAA,EAAC;QAEpF;UAAAI,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACI,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACV,CAAC,eAGT/F,OAAA,CAACN,kBAAkB;MACjB0C,IAAI,EAAElB,cAAe;MACrB6G,OAAO,EAAE/C,qBAAsB;MAC/B5B,QAAQ,EAAE9B,gBAAiB;MAC3BoH,iBAAiB,EAAEvD;IAAsB;MAAAS,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAC1C,CAAC,eAGF/F,OAAA,CAACL,wBAAwB;MACvByC,IAAI,EAAEhB,kBAAmB;MACzB2G,OAAO,EAAE7C,yBAA0B;MACnC9B,QAAQ,EAAE9B;IAAiB;MAAAsE,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAC5B,CAAC,eAGF/F,OAAA,CAAC1B,QAAQ;MACP8D,IAAI,EAAEF,YAAY,CAACE,IAAK;MACxBuG,gBAAgB,EAAE,IAAK;MACvBZ,OAAO,EAAEzD,uBAAwB;MACjCsE,YAAY,EAAE;QAAEC,QAAQ,EAAE,QAAQ;QAAEC,UAAU,EAAE;MAAS,CAAE;MAAAtD,QAAA,eAE3DxF,OAAA,CAACzB,KAAK;QAACwJ,OAAO,EAAEzD,uBAAwB;QAAChC,QAAQ,EAAEJ,YAAY,CAACI,QAAS;QAAC0D,EAAE,EAAE;UAAE+C,KAAK,EAAE;QAAO,CAAE;QAAAvD,QAAA,eAC9FxF,OAAA;UAAKgJ,KAAK,EAAE;YAAEC,UAAU,EAAE;UAAW,CAAE;UAAAzD,QAAA,EAAEtD,YAAY,CAACG;QAAO;UAAAuD,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC/D;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACA,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACR,CAAC;AAEV,CAAC;AAAC7F,EAAA,CA9iBID,QAAQ;EAAA,QACwDL,OAAO,EAC1DC,WAAW;AAAA;AAAAqJ,EAAA,GAFxBjJ,QAAQ;AAgjBd,eAAeA,QAAQ;AAAC,IAAAiJ,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}