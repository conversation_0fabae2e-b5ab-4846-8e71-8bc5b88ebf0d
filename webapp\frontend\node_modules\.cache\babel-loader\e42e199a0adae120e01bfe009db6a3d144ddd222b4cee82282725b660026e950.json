{"ast": null, "code": "/* eslint no-restricted-syntax: 0, prefer-template: 0, guard-for-in: 0\n   ---\n   These rules are preventing the performance optimizations below.\n */\n\n/**\n * Compose classes from multiple sources.\n *\n * @example\n * ```tsx\n * const slots = {\n *  root: ['root', 'primary'],\n *  label: ['label'],\n * };\n *\n * const getUtilityClass = (slot) => `MuiButton-${slot}`;\n *\n * const classes = {\n *   root: 'my-root-class',\n * };\n *\n * const output = composeClasses(slots, getUtilityClass, classes);\n * // {\n * //   root: 'MuiButton-root MuiButton-primary my-root-class',\n * //   label: 'MuiButton-label',\n * // }\n * ```\n *\n * @param slots a list of classes for each possible slot\n * @param getUtilityClass a function to resolve the class based on the slot name\n * @param classes the input classes from props\n * @returns the resolved classes for all slots\n */\nexport default function composeClasses(slots, getUtilityClass, classes = undefined) {\n  const output = {};\n  for (const slotName in slots) {\n    const slot = slots[slotName];\n    let buffer = '';\n    let start = true;\n    for (let i = 0; i < slot.length; i += 1) {\n      const value = slot[i];\n      if (value) {\n        buffer += (start === true ? '' : ' ') + getUtilityClass(value);\n        start = false;\n        if (classes && classes[value]) {\n          buffer += ' ' + classes[value];\n        }\n      }\n    }\n    output[slotName] = buffer;\n  }\n  return output;\n}", "map": {"version": 3, "names": ["composeClasses", "slots", "getUtilityClass", "classes", "undefined", "output", "slotName", "slot", "buffer", "start", "i", "length", "value"], "sources": ["C:/Users/<USER>/PycharmProjects/CMS/webapp/frontend/node_modules/@mui/x-date-pickers/node_modules/@mui/utils/esm/composeClasses/composeClasses.js"], "sourcesContent": ["/* eslint no-restricted-syntax: 0, prefer-template: 0, guard-for-in: 0\n   ---\n   These rules are preventing the performance optimizations below.\n */\n\n/**\n * Compose classes from multiple sources.\n *\n * @example\n * ```tsx\n * const slots = {\n *  root: ['root', 'primary'],\n *  label: ['label'],\n * };\n *\n * const getUtilityClass = (slot) => `MuiButton-${slot}`;\n *\n * const classes = {\n *   root: 'my-root-class',\n * };\n *\n * const output = composeClasses(slots, getUtilityClass, classes);\n * // {\n * //   root: 'MuiButton-root MuiButton-primary my-root-class',\n * //   label: 'MuiButton-label',\n * // }\n * ```\n *\n * @param slots a list of classes for each possible slot\n * @param getUtilityClass a function to resolve the class based on the slot name\n * @param classes the input classes from props\n * @returns the resolved classes for all slots\n */\nexport default function composeClasses(slots, getUtilityClass, classes = undefined) {\n  const output = {};\n  for (const slotName in slots) {\n    const slot = slots[slotName];\n    let buffer = '';\n    let start = true;\n    for (let i = 0; i < slot.length; i += 1) {\n      const value = slot[i];\n      if (value) {\n        buffer += (start === true ? '' : ' ') + getUtilityClass(value);\n        start = false;\n        if (classes && classes[value]) {\n          buffer += ' ' + classes[value];\n        }\n      }\n    }\n    output[slotName] = buffer;\n  }\n  return output;\n}"], "mappings": "AAAA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,eAAe,SAASA,cAAcA,CAACC,KAAK,EAAEC,eAAe,EAAEC,OAAO,GAAGC,SAAS,EAAE;EAClF,MAAMC,MAAM,GAAG,CAAC,CAAC;EACjB,KAAK,MAAMC,QAAQ,IAAIL,KAAK,EAAE;IAC5B,MAAMM,IAAI,GAAGN,KAAK,CAACK,QAAQ,CAAC;IAC5B,IAAIE,MAAM,GAAG,EAAE;IACf,IAAIC,KAAK,GAAG,IAAI;IAChB,KAAK,IAAIC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGH,IAAI,CAACI,MAAM,EAAED,CAAC,IAAI,CAAC,EAAE;MACvC,MAAME,KAAK,GAAGL,IAAI,CAACG,CAAC,CAAC;MACrB,IAAIE,KAAK,EAAE;QACTJ,MAAM,IAAI,CAACC,KAAK,KAAK,IAAI,GAAG,EAAE,GAAG,GAAG,IAAIP,eAAe,CAACU,KAAK,CAAC;QAC9DH,KAAK,GAAG,KAAK;QACb,IAAIN,OAAO,IAAIA,OAAO,CAACS,KAAK,CAAC,EAAE;UAC7BJ,MAAM,IAAI,GAAG,GAAGL,OAAO,CAACS,KAAK,CAAC;QAChC;MACF;IACF;IACAP,MAAM,CAACC,QAAQ,CAAC,GAAGE,MAAM;EAC3B;EACA,OAAOH,MAAM;AACf", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}