{"ast": null, "code": "import axios from 'axios';\nimport config from '../config';\nimport axiosInstance from './axiosConfig';\nconst API_URL = config.API_URL;\nconst excelService = {\n  // Importa cavi da Excel\n  importCavi: async (cantiereId, formData) => {\n    try {\n      // Assicurati che cantiereId sia un numero\n      const cantiereIdNum = parseInt(cantiereId, 10);\n      if (isNaN(cantiereIdNum)) {\n        throw new Error(`ID cantiere non valido: ${cantiereId}`);\n      }\n\n      // Modifica la configurazione per l'upload di file\n      const config = {\n        headers: {\n          'Content-Type': 'multipart/form-data',\n          'Authorization': `Bearer ${localStorage.getItem('token')}`\n        }\n      };\n      const response = await axios.post(`${API_URL}/excel/${cantiereIdNum}/import-cavi`, formData, config);\n      return response.data;\n    } catch (error) {\n      console.error('Import cavi error:', error);\n      throw error.response ? error.response.data : error;\n    }\n  },\n  // Importa parco bobine da Excel\n  importParcoBobine: async (cantiereId, formData) => {\n    try {\n      // Assicurati che cantiereId sia un numero\n      const cantiereIdNum = parseInt(cantiereId, 10);\n      if (isNaN(cantiereIdNum)) {\n        throw new Error(`ID cantiere non valido: ${cantiereId}`);\n      }\n\n      // Modifica la configurazione per l'upload di file\n      const config = {\n        headers: {\n          'Content-Type': 'multipart/form-data',\n          'Authorization': `Bearer ${localStorage.getItem('token')}`\n        }\n      };\n      const response = await axios.post(`${API_URL}/excel/${cantiereIdNum}/import-parco-bobine`, formData, config);\n      return response.data;\n    } catch (error) {\n      console.error('Import parco bobine error:', error);\n      throw error.response ? error.response.data : error;\n    }\n  },\n  // Crea template Excel per cavi\n  createCaviTemplate: async () => {\n    try {\n      // Usa window.open per avviare direttamente il download\n      window.open(`${API_URL}/excel/template-cavi`, '_blank');\n      return {\n        success: true\n      };\n    } catch (error) {\n      console.error('Create cavi template error:', error);\n      throw error.response ? error.response.data : error;\n    }\n  },\n  // Crea template Excel per parco bobine\n  createParcoBobineTemplate: async () => {\n    try {\n      // Usa window.open per avviare direttamente il download\n      window.open(`${API_URL}/excel/template-parco-bobine`, '_blank');\n      return {\n        success: true\n      };\n    } catch (error) {\n      console.error('Create parco bobine template error:', error);\n      throw error.response ? error.response.data : error;\n    }\n  },\n  // Funzione helper per scaricare automaticamente un file\n  downloadFile: async (url, filename) => {\n    try {\n      // Metodo 1: Prova con fetch e blob (migliore per CORS)\n      try {\n        const response = await fetch(url);\n        if (response.ok) {\n          const blob = await response.blob();\n          const blobUrl = window.URL.createObjectURL(blob);\n          const link = document.createElement('a');\n          link.href = blobUrl;\n          link.download = filename;\n          link.style.display = 'none';\n          document.body.appendChild(link);\n          link.click();\n          document.body.removeChild(link);\n\n          // Pulisci l'URL del blob\n          window.URL.revokeObjectURL(blobUrl);\n          console.log(`Download completato per: ${filename}`);\n          return true;\n        }\n      } catch (fetchError) {\n        console.warn('Fetch download fallito, provo con link diretto:', fetchError);\n      }\n\n      // Metodo 2: Fallback con link diretto\n      const link = document.createElement('a');\n      link.href = url;\n      link.download = filename;\n      link.target = '_blank';\n      link.style.display = 'none';\n      document.body.appendChild(link);\n      link.click();\n      document.body.removeChild(link);\n      console.log(`Download avviato per: ${filename}`);\n      return true;\n    } catch (error) {\n      console.error('Errore durante il download automatico:', error);\n      return false;\n    }\n  },\n  // Esporta cavi in Excel\n  exportCavi: async cantiereId => {\n    try {\n      // Assicurati che cantiereId sia un numero\n      const cantiereIdNum = parseInt(cantiereId, 10);\n      if (isNaN(cantiereIdNum)) {\n        throw new Error(`ID cantiere non valido: ${cantiereId}`);\n      }\n      const response = await axiosInstance.get(`/excel/${cantiereIdNum}/export-cavi`);\n\n      // Se la risposta contiene un file_url, avvia automaticamente il download\n      if (response.data && response.data.file_url) {\n        await excelService.downloadFile(response.data.file_url, `export_cavi_cantiere_${cantiereIdNum}.xlsx`);\n      }\n      return response.data;\n    } catch (error) {\n      console.error('Export cavi error:', error);\n      throw error.response ? error.response.data : error;\n    }\n  },\n  // Esporta parco bobine in Excel\n  exportParcoBobine: async cantiereId => {\n    try {\n      // Assicurati che cantiereId sia un numero\n      const cantiereIdNum = parseInt(cantiereId, 10);\n      if (isNaN(cantiereIdNum)) {\n        throw new Error(`ID cantiere non valido: ${cantiereId}`);\n      }\n      const response = await axiosInstance.get(`/excel/${cantiereIdNum}/export-parco-bobine`);\n\n      // Se la risposta contiene un file_url, avvia automaticamente il download\n      if (response.data && response.data.file_url) {\n        await excelService.downloadFile(response.data.file_url, `export_parco_bobine_cantiere_${cantiereIdNum}.xlsx`);\n      }\n      return response.data;\n    } catch (error) {\n      console.error('Export parco bobine error:', error);\n      throw error.response ? error.response.data : error;\n    }\n  }\n};\nexport default excelService;", "map": {"version": 3, "names": ["axios", "config", "axiosInstance", "API_URL", "excelService", "importCavi", "cantiereId", "formData", "cantiereIdNum", "parseInt", "isNaN", "Error", "headers", "localStorage", "getItem", "response", "post", "data", "error", "console", "importParcoBobine", "createCaviTemplate", "window", "open", "success", "createParcoBobineTemplate", "downloadFile", "url", "filename", "fetch", "ok", "blob", "blobUrl", "URL", "createObjectURL", "link", "document", "createElement", "href", "download", "style", "display", "body", "append<PERSON><PERSON><PERSON>", "click", "<PERSON><PERSON><PERSON><PERSON>", "revokeObjectURL", "log", "fetchError", "warn", "target", "exportCavi", "get", "file_url", "exportParcoBobine"], "sources": ["C:/Users/<USER>/PycharmProjects/CMS/webapp/frontend/src/services/excelService.js"], "sourcesContent": ["import axios from 'axios';\r\nimport config from '../config';\r\nimport axiosInstance from './axiosConfig';\r\n\r\nconst API_URL = config.API_URL;\r\n\r\nconst excelService = {\r\n  // Importa cavi da Excel\r\n  importCavi: async (cantiereId, formData) => {\r\n    try {\r\n      // Assicurati che cantiereId sia un numero\r\n      const cantiereIdNum = parseInt(cantiereId, 10);\r\n      if (isNaN(cantiereIdNum)) {\r\n        throw new Error(`ID cantiere non valido: ${cantiereId}`);\r\n      }\r\n\r\n      // Modifica la configurazione per l'upload di file\r\n      const config = {\r\n        headers: {\r\n          'Content-Type': 'multipart/form-data',\r\n          'Authorization': `Bearer ${localStorage.getItem('token')}`\r\n        }\r\n      };\r\n\r\n      const response = await axios.post(`${API_URL}/excel/${cantiereIdNum}/import-cavi`, formData, config);\r\n      return response.data;\r\n    } catch (error) {\r\n      console.error('Import cavi error:', error);\r\n      throw error.response ? error.response.data : error;\r\n    }\r\n  },\r\n\r\n  // Importa parco bobine da Excel\r\n  importParcoBobine: async (cantiereId, formData) => {\r\n    try {\r\n      // Assicurati che cantiereId sia un numero\r\n      const cantiereIdNum = parseInt(cantiereId, 10);\r\n      if (isNaN(cantiereIdNum)) {\r\n        throw new Error(`ID cantiere non valido: ${cantiereId}`);\r\n      }\r\n\r\n      // Modifica la configurazione per l'upload di file\r\n      const config = {\r\n        headers: {\r\n          'Content-Type': 'multipart/form-data',\r\n          'Authorization': `Bearer ${localStorage.getItem('token')}`\r\n        }\r\n      };\r\n\r\n      const response = await axios.post(`${API_URL}/excel/${cantiereIdNum}/import-parco-bobine`, formData, config);\r\n      return response.data;\r\n    } catch (error) {\r\n      console.error('Import parco bobine error:', error);\r\n      throw error.response ? error.response.data : error;\r\n    }\r\n  },\r\n\r\n  // Crea template Excel per cavi\r\n  createCaviTemplate: async () => {\r\n    try {\r\n      // Usa window.open per avviare direttamente il download\r\n      window.open(`${API_URL}/excel/template-cavi`, '_blank');\r\n      return { success: true };\r\n    } catch (error) {\r\n      console.error('Create cavi template error:', error);\r\n      throw error.response ? error.response.data : error;\r\n    }\r\n  },\r\n\r\n  // Crea template Excel per parco bobine\r\n  createParcoBobineTemplate: async () => {\r\n    try {\r\n      // Usa window.open per avviare direttamente il download\r\n      window.open(`${API_URL}/excel/template-parco-bobine`, '_blank');\r\n      return { success: true };\r\n    } catch (error) {\r\n      console.error('Create parco bobine template error:', error);\r\n      throw error.response ? error.response.data : error;\r\n    }\r\n  },\r\n\r\n  // Funzione helper per scaricare automaticamente un file\r\n  downloadFile: async (url, filename) => {\r\n    try {\r\n      // Metodo 1: Prova con fetch e blob (migliore per CORS)\r\n      try {\r\n        const response = await fetch(url);\r\n        if (response.ok) {\r\n          const blob = await response.blob();\r\n          const blobUrl = window.URL.createObjectURL(blob);\r\n\r\n          const link = document.createElement('a');\r\n          link.href = blobUrl;\r\n          link.download = filename;\r\n          link.style.display = 'none';\r\n\r\n          document.body.appendChild(link);\r\n          link.click();\r\n          document.body.removeChild(link);\r\n\r\n          // Pulisci l'URL del blob\r\n          window.URL.revokeObjectURL(blobUrl);\r\n\r\n          console.log(`Download completato per: ${filename}`);\r\n          return true;\r\n        }\r\n      } catch (fetchError) {\r\n        console.warn('Fetch download fallito, provo con link diretto:', fetchError);\r\n      }\r\n\r\n      // Metodo 2: Fallback con link diretto\r\n      const link = document.createElement('a');\r\n      link.href = url;\r\n      link.download = filename;\r\n      link.target = '_blank';\r\n      link.style.display = 'none';\r\n\r\n      document.body.appendChild(link);\r\n      link.click();\r\n      document.body.removeChild(link);\r\n\r\n      console.log(`Download avviato per: ${filename}`);\r\n      return true;\r\n    } catch (error) {\r\n      console.error('Errore durante il download automatico:', error);\r\n      return false;\r\n    }\r\n  },\r\n\r\n  // Esporta cavi in Excel\r\n  exportCavi: async (cantiereId) => {\r\n    try {\r\n      // Assicurati che cantiereId sia un numero\r\n      const cantiereIdNum = parseInt(cantiereId, 10);\r\n      if (isNaN(cantiereIdNum)) {\r\n        throw new Error(`ID cantiere non valido: ${cantiereId}`);\r\n      }\r\n\r\n      const response = await axiosInstance.get(`/excel/${cantiereIdNum}/export-cavi`);\r\n\r\n      // Se la risposta contiene un file_url, avvia automaticamente il download\r\n      if (response.data && response.data.file_url) {\r\n        await excelService.downloadFile(response.data.file_url, `export_cavi_cantiere_${cantiereIdNum}.xlsx`);\r\n      }\r\n\r\n      return response.data;\r\n    } catch (error) {\r\n      console.error('Export cavi error:', error);\r\n      throw error.response ? error.response.data : error;\r\n    }\r\n  },\r\n\r\n  // Esporta parco bobine in Excel\r\n  exportParcoBobine: async (cantiereId) => {\r\n    try {\r\n      // Assicurati che cantiereId sia un numero\r\n      const cantiereIdNum = parseInt(cantiereId, 10);\r\n      if (isNaN(cantiereIdNum)) {\r\n        throw new Error(`ID cantiere non valido: ${cantiereId}`);\r\n      }\r\n\r\n      const response = await axiosInstance.get(`/excel/${cantiereIdNum}/export-parco-bobine`);\r\n\r\n      // Se la risposta contiene un file_url, avvia automaticamente il download\r\n      if (response.data && response.data.file_url) {\r\n        await excelService.downloadFile(response.data.file_url, `export_parco_bobine_cantiere_${cantiereIdNum}.xlsx`);\r\n      }\r\n\r\n      return response.data;\r\n    } catch (error) {\r\n      console.error('Export parco bobine error:', error);\r\n      throw error.response ? error.response.data : error;\r\n    }\r\n  }\r\n};\r\n\r\nexport default excelService;\r\n"], "mappings": "AAAA,OAAOA,KAAK,MAAM,OAAO;AACzB,OAAOC,MAAM,MAAM,WAAW;AAC9B,OAAOC,aAAa,MAAM,eAAe;AAEzC,MAAMC,OAAO,GAAGF,MAAM,CAACE,OAAO;AAE9B,MAAMC,YAAY,GAAG;EACnB;EACAC,UAAU,EAAE,MAAAA,CAAOC,UAAU,EAAEC,QAAQ,KAAK;IAC1C,IAAI;MACF;MACA,MAAMC,aAAa,GAAGC,QAAQ,CAACH,UAAU,EAAE,EAAE,CAAC;MAC9C,IAAII,KAAK,CAACF,aAAa,CAAC,EAAE;QACxB,MAAM,IAAIG,KAAK,CAAC,2BAA2BL,UAAU,EAAE,CAAC;MAC1D;;MAEA;MACA,MAAML,MAAM,GAAG;QACbW,OAAO,EAAE;UACP,cAAc,EAAE,qBAAqB;UACrC,eAAe,EAAE,UAAUC,YAAY,CAACC,OAAO,CAAC,OAAO,CAAC;QAC1D;MACF,CAAC;MAED,MAAMC,QAAQ,GAAG,MAAMf,KAAK,CAACgB,IAAI,CAAC,GAAGb,OAAO,UAAUK,aAAa,cAAc,EAAED,QAAQ,EAAEN,MAAM,CAAC;MACpG,OAAOc,QAAQ,CAACE,IAAI;IACtB,CAAC,CAAC,OAAOC,KAAK,EAAE;MACdC,OAAO,CAACD,KAAK,CAAC,oBAAoB,EAAEA,KAAK,CAAC;MAC1C,MAAMA,KAAK,CAACH,QAAQ,GAAGG,KAAK,CAACH,QAAQ,CAACE,IAAI,GAAGC,KAAK;IACpD;EACF,CAAC;EAED;EACAE,iBAAiB,EAAE,MAAAA,CAAOd,UAAU,EAAEC,QAAQ,KAAK;IACjD,IAAI;MACF;MACA,MAAMC,aAAa,GAAGC,QAAQ,CAACH,UAAU,EAAE,EAAE,CAAC;MAC9C,IAAII,KAAK,CAACF,aAAa,CAAC,EAAE;QACxB,MAAM,IAAIG,KAAK,CAAC,2BAA2BL,UAAU,EAAE,CAAC;MAC1D;;MAEA;MACA,MAAML,MAAM,GAAG;QACbW,OAAO,EAAE;UACP,cAAc,EAAE,qBAAqB;UACrC,eAAe,EAAE,UAAUC,YAAY,CAACC,OAAO,CAAC,OAAO,CAAC;QAC1D;MACF,CAAC;MAED,MAAMC,QAAQ,GAAG,MAAMf,KAAK,CAACgB,IAAI,CAAC,GAAGb,OAAO,UAAUK,aAAa,sBAAsB,EAAED,QAAQ,EAAEN,MAAM,CAAC;MAC5G,OAAOc,QAAQ,CAACE,IAAI;IACtB,CAAC,CAAC,OAAOC,KAAK,EAAE;MACdC,OAAO,CAACD,KAAK,CAAC,4BAA4B,EAAEA,KAAK,CAAC;MAClD,MAAMA,KAAK,CAACH,QAAQ,GAAGG,KAAK,CAACH,QAAQ,CAACE,IAAI,GAAGC,KAAK;IACpD;EACF,CAAC;EAED;EACAG,kBAAkB,EAAE,MAAAA,CAAA,KAAY;IAC9B,IAAI;MACF;MACAC,MAAM,CAACC,IAAI,CAAC,GAAGpB,OAAO,sBAAsB,EAAE,QAAQ,CAAC;MACvD,OAAO;QAAEqB,OAAO,EAAE;MAAK,CAAC;IAC1B,CAAC,CAAC,OAAON,KAAK,EAAE;MACdC,OAAO,CAACD,KAAK,CAAC,6BAA6B,EAAEA,KAAK,CAAC;MACnD,MAAMA,KAAK,CAACH,QAAQ,GAAGG,KAAK,CAACH,QAAQ,CAACE,IAAI,GAAGC,KAAK;IACpD;EACF,CAAC;EAED;EACAO,yBAAyB,EAAE,MAAAA,CAAA,KAAY;IACrC,IAAI;MACF;MACAH,MAAM,CAACC,IAAI,CAAC,GAAGpB,OAAO,8BAA8B,EAAE,QAAQ,CAAC;MAC/D,OAAO;QAAEqB,OAAO,EAAE;MAAK,CAAC;IAC1B,CAAC,CAAC,OAAON,KAAK,EAAE;MACdC,OAAO,CAACD,KAAK,CAAC,qCAAqC,EAAEA,KAAK,CAAC;MAC3D,MAAMA,KAAK,CAACH,QAAQ,GAAGG,KAAK,CAACH,QAAQ,CAACE,IAAI,GAAGC,KAAK;IACpD;EACF,CAAC;EAED;EACAQ,YAAY,EAAE,MAAAA,CAAOC,GAAG,EAAEC,QAAQ,KAAK;IACrC,IAAI;MACF;MACA,IAAI;QACF,MAAMb,QAAQ,GAAG,MAAMc,KAAK,CAACF,GAAG,CAAC;QACjC,IAAIZ,QAAQ,CAACe,EAAE,EAAE;UACf,MAAMC,IAAI,GAAG,MAAMhB,QAAQ,CAACgB,IAAI,CAAC,CAAC;UAClC,MAAMC,OAAO,GAAGV,MAAM,CAACW,GAAG,CAACC,eAAe,CAACH,IAAI,CAAC;UAEhD,MAAMI,IAAI,GAAGC,QAAQ,CAACC,aAAa,CAAC,GAAG,CAAC;UACxCF,IAAI,CAACG,IAAI,GAAGN,OAAO;UACnBG,IAAI,CAACI,QAAQ,GAAGX,QAAQ;UACxBO,IAAI,CAACK,KAAK,CAACC,OAAO,GAAG,MAAM;UAE3BL,QAAQ,CAACM,IAAI,CAACC,WAAW,CAACR,IAAI,CAAC;UAC/BA,IAAI,CAACS,KAAK,CAAC,CAAC;UACZR,QAAQ,CAACM,IAAI,CAACG,WAAW,CAACV,IAAI,CAAC;;UAE/B;UACAb,MAAM,CAACW,GAAG,CAACa,eAAe,CAACd,OAAO,CAAC;UAEnCb,OAAO,CAAC4B,GAAG,CAAC,4BAA4BnB,QAAQ,EAAE,CAAC;UACnD,OAAO,IAAI;QACb;MACF,CAAC,CAAC,OAAOoB,UAAU,EAAE;QACnB7B,OAAO,CAAC8B,IAAI,CAAC,iDAAiD,EAAED,UAAU,CAAC;MAC7E;;MAEA;MACA,MAAMb,IAAI,GAAGC,QAAQ,CAACC,aAAa,CAAC,GAAG,CAAC;MACxCF,IAAI,CAACG,IAAI,GAAGX,GAAG;MACfQ,IAAI,CAACI,QAAQ,GAAGX,QAAQ;MACxBO,IAAI,CAACe,MAAM,GAAG,QAAQ;MACtBf,IAAI,CAACK,KAAK,CAACC,OAAO,GAAG,MAAM;MAE3BL,QAAQ,CAACM,IAAI,CAACC,WAAW,CAACR,IAAI,CAAC;MAC/BA,IAAI,CAACS,KAAK,CAAC,CAAC;MACZR,QAAQ,CAACM,IAAI,CAACG,WAAW,CAACV,IAAI,CAAC;MAE/BhB,OAAO,CAAC4B,GAAG,CAAC,yBAAyBnB,QAAQ,EAAE,CAAC;MAChD,OAAO,IAAI;IACb,CAAC,CAAC,OAAOV,KAAK,EAAE;MACdC,OAAO,CAACD,KAAK,CAAC,wCAAwC,EAAEA,KAAK,CAAC;MAC9D,OAAO,KAAK;IACd;EACF,CAAC;EAED;EACAiC,UAAU,EAAE,MAAO7C,UAAU,IAAK;IAChC,IAAI;MACF;MACA,MAAME,aAAa,GAAGC,QAAQ,CAACH,UAAU,EAAE,EAAE,CAAC;MAC9C,IAAII,KAAK,CAACF,aAAa,CAAC,EAAE;QACxB,MAAM,IAAIG,KAAK,CAAC,2BAA2BL,UAAU,EAAE,CAAC;MAC1D;MAEA,MAAMS,QAAQ,GAAG,MAAMb,aAAa,CAACkD,GAAG,CAAC,UAAU5C,aAAa,cAAc,CAAC;;MAE/E;MACA,IAAIO,QAAQ,CAACE,IAAI,IAAIF,QAAQ,CAACE,IAAI,CAACoC,QAAQ,EAAE;QAC3C,MAAMjD,YAAY,CAACsB,YAAY,CAACX,QAAQ,CAACE,IAAI,CAACoC,QAAQ,EAAE,wBAAwB7C,aAAa,OAAO,CAAC;MACvG;MAEA,OAAOO,QAAQ,CAACE,IAAI;IACtB,CAAC,CAAC,OAAOC,KAAK,EAAE;MACdC,OAAO,CAACD,KAAK,CAAC,oBAAoB,EAAEA,KAAK,CAAC;MAC1C,MAAMA,KAAK,CAACH,QAAQ,GAAGG,KAAK,CAACH,QAAQ,CAACE,IAAI,GAAGC,KAAK;IACpD;EACF,CAAC;EAED;EACAoC,iBAAiB,EAAE,MAAOhD,UAAU,IAAK;IACvC,IAAI;MACF;MACA,MAAME,aAAa,GAAGC,QAAQ,CAACH,UAAU,EAAE,EAAE,CAAC;MAC9C,IAAII,KAAK,CAACF,aAAa,CAAC,EAAE;QACxB,MAAM,IAAIG,KAAK,CAAC,2BAA2BL,UAAU,EAAE,CAAC;MAC1D;MAEA,MAAMS,QAAQ,GAAG,MAAMb,aAAa,CAACkD,GAAG,CAAC,UAAU5C,aAAa,sBAAsB,CAAC;;MAEvF;MACA,IAAIO,QAAQ,CAACE,IAAI,IAAIF,QAAQ,CAACE,IAAI,CAACoC,QAAQ,EAAE;QAC3C,MAAMjD,YAAY,CAACsB,YAAY,CAACX,QAAQ,CAACE,IAAI,CAACoC,QAAQ,EAAE,gCAAgC7C,aAAa,OAAO,CAAC;MAC/G;MAEA,OAAOO,QAAQ,CAACE,IAAI;IACtB,CAAC,CAAC,OAAOC,KAAK,EAAE;MACdC,OAAO,CAACD,KAAK,CAAC,4BAA4B,EAAEA,KAAK,CAAC;MAClD,MAAMA,KAAK,CAACH,QAAQ,GAAGG,KAAK,CAACH,QAAQ,CAACE,IAAI,GAAGC,KAAK;IACpD;EACF;AACF,CAAC;AAED,eAAed,YAAY", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}