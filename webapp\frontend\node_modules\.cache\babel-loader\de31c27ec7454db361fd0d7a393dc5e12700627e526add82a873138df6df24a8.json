{"ast": null, "code": "'use client';\n\nimport _extends from \"@babel/runtime/helpers/esm/extends\";\nimport _objectWithoutPropertiesLoose from \"@babel/runtime/helpers/esm/objectWithoutPropertiesLoose\";\nconst _excluded = [\"className\"];\nimport * as React from 'react';\nimport PropTypes from 'prop-types';\nimport clsx from 'clsx';\nimport composeClasses from '@mui/utils/composeClasses';\nimport { styled } from '../zero-styled';\nimport { useDefaultProps } from '../DefaultPropsProvider';\nimport { getAccordionDetailsUtilityClass } from './accordionDetailsClasses';\nimport { jsx as _jsx } from \"react/jsx-runtime\";\nconst useUtilityClasses = ownerState => {\n  const {\n    classes\n  } = ownerState;\n  const slots = {\n    root: ['root']\n  };\n  return composeClasses(slots, getAccordionDetailsUtilityClass, classes);\n};\nconst AccordionDetailsRoot = styled('div', {\n  name: 'MuiAccordionDetails',\n  slot: 'Root',\n  overridesResolver: (props, styles) => styles.root\n})(({\n  theme\n}) => ({\n  padding: theme.spacing(1, 2, 2)\n}));\nconst AccordionDetails = /*#__PURE__*/React.forwardRef(function AccordionDetails(inProps, ref) {\n  const props = useDefaultProps({\n    props: inProps,\n    name: 'MuiAccordionDetails'\n  });\n  const {\n      className\n    } = props,\n    other = _objectWithoutPropertiesLoose(props, _excluded);\n  const ownerState = props;\n  const classes = useUtilityClasses(ownerState);\n  return /*#__PURE__*/_jsx(AccordionDetailsRoot, _extends({\n    className: clsx(classes.root, className),\n    ref: ref,\n    ownerState: ownerState\n  }, other));\n});\nprocess.env.NODE_ENV !== \"production\" ? AccordionDetails.propTypes /* remove-proptypes */ = {\n  // ┌────────────────────────────── Warning ──────────────────────────────┐\n  // │ These PropTypes are generated from the TypeScript type definitions. │\n  // │    To update them, edit the d.ts file and run `pnpm proptypes`.     │\n  // └─────────────────────────────────────────────────────────────────────┘\n  /**\n   * The content of the component.\n   */\n  children: PropTypes.node,\n  /**\n   * Override or extend the styles applied to the component.\n   */\n  classes: PropTypes.object,\n  /**\n   * @ignore\n   */\n  className: PropTypes.string,\n  /**\n   * The system prop that allows defining system overrides as well as additional CSS styles.\n   */\n  sx: PropTypes.oneOfType([PropTypes.arrayOf(PropTypes.oneOfType([PropTypes.func, PropTypes.object, PropTypes.bool])), PropTypes.func, PropTypes.object])\n} : void 0;\nexport default AccordionDetails;", "map": {"version": 3, "names": ["_extends", "_objectWithoutPropertiesLoose", "_excluded", "React", "PropTypes", "clsx", "composeClasses", "styled", "useDefaultProps", "getAccordionDetailsUtilityClass", "jsx", "_jsx", "useUtilityClasses", "ownerState", "classes", "slots", "root", "AccordionDetailsRoot", "name", "slot", "overridesResolver", "props", "styles", "theme", "padding", "spacing", "AccordionDetails", "forwardRef", "inProps", "ref", "className", "other", "process", "env", "NODE_ENV", "propTypes", "children", "node", "object", "string", "sx", "oneOfType", "arrayOf", "func", "bool"], "sources": ["C:/Users/<USER>/PycharmProjects/CMS/webapp/frontend/node_modules/@mui/material/AccordionDetails/AccordionDetails.js"], "sourcesContent": ["'use client';\n\nimport _extends from \"@babel/runtime/helpers/esm/extends\";\nimport _objectWithoutPropertiesLoose from \"@babel/runtime/helpers/esm/objectWithoutPropertiesLoose\";\nconst _excluded = [\"className\"];\nimport * as React from 'react';\nimport PropTypes from 'prop-types';\nimport clsx from 'clsx';\nimport composeClasses from '@mui/utils/composeClasses';\nimport { styled } from '../zero-styled';\nimport { useDefaultProps } from '../DefaultPropsProvider';\nimport { getAccordionDetailsUtilityClass } from './accordionDetailsClasses';\nimport { jsx as _jsx } from \"react/jsx-runtime\";\nconst useUtilityClasses = ownerState => {\n  const {\n    classes\n  } = ownerState;\n  const slots = {\n    root: ['root']\n  };\n  return composeClasses(slots, getAccordionDetailsUtilityClass, classes);\n};\nconst AccordionDetailsRoot = styled('div', {\n  name: 'MuiAccordionDetails',\n  slot: 'Root',\n  overridesResolver: (props, styles) => styles.root\n})(({\n  theme\n}) => ({\n  padding: theme.spacing(1, 2, 2)\n}));\nconst AccordionDetails = /*#__PURE__*/React.forwardRef(function AccordionDetails(inProps, ref) {\n  const props = useDefaultProps({\n    props: inProps,\n    name: 'MuiAccordionDetails'\n  });\n  const {\n      className\n    } = props,\n    other = _objectWithoutPropertiesLoose(props, _excluded);\n  const ownerState = props;\n  const classes = useUtilityClasses(ownerState);\n  return /*#__PURE__*/_jsx(AccordionDetailsRoot, _extends({\n    className: clsx(classes.root, className),\n    ref: ref,\n    ownerState: ownerState\n  }, other));\n});\nprocess.env.NODE_ENV !== \"production\" ? AccordionDetails.propTypes /* remove-proptypes */ = {\n  // ┌────────────────────────────── Warning ──────────────────────────────┐\n  // │ These PropTypes are generated from the TypeScript type definitions. │\n  // │    To update them, edit the d.ts file and run `pnpm proptypes`.     │\n  // └─────────────────────────────────────────────────────────────────────┘\n  /**\n   * The content of the component.\n   */\n  children: PropTypes.node,\n  /**\n   * Override or extend the styles applied to the component.\n   */\n  classes: PropTypes.object,\n  /**\n   * @ignore\n   */\n  className: PropTypes.string,\n  /**\n   * The system prop that allows defining system overrides as well as additional CSS styles.\n   */\n  sx: PropTypes.oneOfType([PropTypes.arrayOf(PropTypes.oneOfType([PropTypes.func, PropTypes.object, PropTypes.bool])), PropTypes.func, PropTypes.object])\n} : void 0;\nexport default AccordionDetails;"], "mappings": "AAAA,YAAY;;AAEZ,OAAOA,QAAQ,MAAM,oCAAoC;AACzD,OAAOC,6BAA6B,MAAM,yDAAyD;AACnG,MAAMC,SAAS,GAAG,CAAC,WAAW,CAAC;AAC/B,OAAO,KAAKC,KAAK,MAAM,OAAO;AAC9B,OAAOC,SAAS,MAAM,YAAY;AAClC,OAAOC,IAAI,MAAM,MAAM;AACvB,OAAOC,cAAc,MAAM,2BAA2B;AACtD,SAASC,MAAM,QAAQ,gBAAgB;AACvC,SAASC,eAAe,QAAQ,yBAAyB;AACzD,SAASC,+BAA+B,QAAQ,2BAA2B;AAC3E,SAASC,GAAG,IAAIC,IAAI,QAAQ,mBAAmB;AAC/C,MAAMC,iBAAiB,GAAGC,UAAU,IAAI;EACtC,MAAM;IACJC;EACF,CAAC,GAAGD,UAAU;EACd,MAAME,KAAK,GAAG;IACZC,IAAI,EAAE,CAAC,MAAM;EACf,CAAC;EACD,OAAOV,cAAc,CAACS,KAAK,EAAEN,+BAA+B,EAAEK,OAAO,CAAC;AACxE,CAAC;AACD,MAAMG,oBAAoB,GAAGV,MAAM,CAAC,KAAK,EAAE;EACzCW,IAAI,EAAE,qBAAqB;EAC3BC,IAAI,EAAE,MAAM;EACZC,iBAAiB,EAAEA,CAACC,KAAK,EAAEC,MAAM,KAAKA,MAAM,CAACN;AAC/C,CAAC,CAAC,CAAC,CAAC;EACFO;AACF,CAAC,MAAM;EACLC,OAAO,EAAED,KAAK,CAACE,OAAO,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC;AAChC,CAAC,CAAC,CAAC;AACH,MAAMC,gBAAgB,GAAG,aAAavB,KAAK,CAACwB,UAAU,CAAC,SAASD,gBAAgBA,CAACE,OAAO,EAAEC,GAAG,EAAE;EAC7F,MAAMR,KAAK,GAAGb,eAAe,CAAC;IAC5Ba,KAAK,EAAEO,OAAO;IACdV,IAAI,EAAE;EACR,CAAC,CAAC;EACF,MAAM;MACFY;IACF,CAAC,GAAGT,KAAK;IACTU,KAAK,GAAG9B,6BAA6B,CAACoB,KAAK,EAAEnB,SAAS,CAAC;EACzD,MAAMW,UAAU,GAAGQ,KAAK;EACxB,MAAMP,OAAO,GAAGF,iBAAiB,CAACC,UAAU,CAAC;EAC7C,OAAO,aAAaF,IAAI,CAACM,oBAAoB,EAAEjB,QAAQ,CAAC;IACtD8B,SAAS,EAAEzB,IAAI,CAACS,OAAO,CAACE,IAAI,EAAEc,SAAS,CAAC;IACxCD,GAAG,EAAEA,GAAG;IACRhB,UAAU,EAAEA;EACd,CAAC,EAAEkB,KAAK,CAAC,CAAC;AACZ,CAAC,CAAC;AACFC,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,GAAGR,gBAAgB,CAACS,SAAS,CAAC,yBAAyB;EAC1F;EACA;EACA;EACA;EACA;AACF;AACA;EACEC,QAAQ,EAAEhC,SAAS,CAACiC,IAAI;EACxB;AACF;AACA;EACEvB,OAAO,EAAEV,SAAS,CAACkC,MAAM;EACzB;AACF;AACA;EACER,SAAS,EAAE1B,SAAS,CAACmC,MAAM;EAC3B;AACF;AACA;EACEC,EAAE,EAAEpC,SAAS,CAACqC,SAAS,CAAC,CAACrC,SAAS,CAACsC,OAAO,CAACtC,SAAS,CAACqC,SAAS,CAAC,CAACrC,SAAS,CAACuC,IAAI,EAAEvC,SAAS,CAACkC,MAAM,EAAElC,SAAS,CAACwC,IAAI,CAAC,CAAC,CAAC,EAAExC,SAAS,CAACuC,IAAI,EAAEvC,SAAS,CAACkC,MAAM,CAAC;AACxJ,CAAC,GAAG,KAAK,CAAC;AACV,eAAeZ,gBAAgB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}