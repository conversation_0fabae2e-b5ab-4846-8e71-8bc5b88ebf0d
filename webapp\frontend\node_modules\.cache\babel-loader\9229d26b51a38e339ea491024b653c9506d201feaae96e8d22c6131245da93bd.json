{"ast": null, "code": "import { formatDistance } from \"./de/_lib/formatDistance.js\";\nimport { formatLong } from \"./de/_lib/formatLong.js\";\nimport { formatRelative } from \"./de/_lib/formatRelative.js\";\nimport { match } from \"./de/_lib/match.js\";\n\n// difference to 'de' locale\nimport { localize } from \"./de-AT/_lib/localize.js\";\n\n/**\n * @category Locales\n * @summary German locale (Austria).\n * @language German\n * @iso-639-2 deu\n * <AUTHOR> [@cstenglein](https://github.com/cstenglein)\n */\nexport const deAT = {\n  code: \"de-AT\",\n  formatDistance: formatDistance,\n  formatLong: formatLong,\n  formatRelative: formatRelative,\n  localize: localize,\n  match: match,\n  options: {\n    weekStartsOn: 1 /* Monday */,\n    firstWeekContainsDate: 4\n  }\n};\n\n// Fallback for modularized imports:\nexport default deAT;", "map": {"version": 3, "names": ["formatDistance", "formatLong", "formatRelative", "match", "localize", "deAT", "code", "options", "weekStartsOn", "firstWeekContainsDate"], "sources": ["C:/CMS/webapp/frontend/node_modules/date-fns/locale/de-AT.js"], "sourcesContent": ["import { formatDistance } from \"./de/_lib/formatDistance.js\";\nimport { formatLong } from \"./de/_lib/formatLong.js\";\nimport { formatRelative } from \"./de/_lib/formatRelative.js\";\nimport { match } from \"./de/_lib/match.js\";\n\n// difference to 'de' locale\nimport { localize } from \"./de-AT/_lib/localize.js\";\n\n/**\n * @category Locales\n * @summary German locale (Austria).\n * @language German\n * @iso-639-2 deu\n * <AUTHOR> [@cstenglein](https://github.com/cstenglein)\n */\nexport const deAT = {\n  code: \"de-AT\",\n  formatDistance: formatDistance,\n  formatLong: formatLong,\n  formatRelative: formatRelative,\n  localize: localize,\n  match: match,\n  options: {\n    weekStartsOn: 1 /* Monday */,\n    firstWeekContainsDate: 4,\n  },\n};\n\n// Fallback for modularized imports:\nexport default deAT;\n"], "mappings": "AAAA,SAASA,cAAc,QAAQ,6BAA6B;AAC5D,SAASC,UAAU,QAAQ,yBAAyB;AACpD,SAASC,cAAc,QAAQ,6BAA6B;AAC5D,SAASC,KAAK,QAAQ,oBAAoB;;AAE1C;AACA,SAASC,QAAQ,QAAQ,0BAA0B;;AAEnD;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO,MAAMC,IAAI,GAAG;EAClBC,IAAI,EAAE,OAAO;EACbN,cAAc,EAAEA,cAAc;EAC9BC,UAAU,EAAEA,UAAU;EACtBC,cAAc,EAAEA,cAAc;EAC9BE,QAAQ,EAAEA,QAAQ;EAClBD,KAAK,EAAEA,KAAK;EACZI,OAAO,EAAE;IACPC,YAAY,EAAE,CAAC,CAAC;IAChBC,qBAAqB,EAAE;EACzB;AACF,CAAC;;AAED;AACA,eAAeJ,IAAI", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}