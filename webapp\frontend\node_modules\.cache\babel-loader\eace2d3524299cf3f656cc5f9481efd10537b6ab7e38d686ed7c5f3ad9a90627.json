{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\PycharmProjects\\\\CMS\\\\webapp\\\\frontend\\\\src\\\\components\\\\TopNavbar.js\",\n  _s = $RefreshSig$();\nimport React, { useState } from 'react';\nimport { useNavigate, useLocation } from 'react-router-dom';\nimport { AppBar, Toolbar, Box, Button, Menu, MenuItem, Typography, IconButton, Divider, Avatar, Tooltip, Snackbar, Alert } from '@mui/material';\nimport { Home as HomeIcon, AdminPanelSettings as AdminIcon, Construction as ConstructionIcon, Cable as CableIcon, Description as ReportIcon, Logout as LogoutIcon, KeyboardArrowDown as ArrowDownIcon, FileUpload as FileUploadIcon, FileDownload as FileDownloadIcon } from '@mui/icons-material';\nimport { useAuth } from '../context/AuthContext';\nimport { useGlobalContext } from '../context/GlobalContext';\nimport SelectedCantiereDisplay from './common/SelectedCantiereDisplay';\nimport ExcelPopup from './cavi/ExcelPopup';\nimport './TopNavbar.css';\nimport { jsxDEV as _jsxDEV, Fragment as _Fragment } from \"react/jsx-dev-runtime\";\nconst TopNavbar = () => {\n  _s();\n  const navigate = useNavigate();\n  const location = useLocation();\n  const {\n    user,\n    logout,\n    isImpersonating,\n    impersonatedUser\n  } = useAuth();\n  const {\n    setOpenEliminaCavoDialog,\n    setOpenModificaCavoDialog,\n    setOpenAggiungiCavoDialog\n  } = useGlobalContext();\n\n  // Stato per il popup Excel\n  const [excelPopupOpen, setExcelPopupOpen] = useState(false);\n  const [excelOperationType, setExcelOperationType] = useState('');\n\n  // Stato per gli snackbar\n  const [snackbar, setSnackbar] = useState({\n    open: false,\n    message: '',\n    severity: 'success'\n  });\n\n  // Recupera l'ID del cantiere dal localStorage\n  const cantiereId = parseInt(localStorage.getItem('selectedCantiereId'), 10);\n\n  // Stati per i menu a tendina\n  const [homeAnchorEl, setHomeAnchorEl] = useState(null);\n  const [adminAnchorEl, setAdminAnchorEl] = useState(null);\n  const [cantieriAnchorEl, setCantieriAnchorEl] = useState(null);\n  const [caviAnchorEl, setCaviAnchorEl] = useState(null);\n  const [posaAnchorEl, setPosaAnchorEl] = useState(null);\n  const [parcoAnchorEl, setParcoAnchorEl] = useState(null);\n  const [excelAnchorEl, setExcelAnchorEl] = useState(null);\n  const [reportAnchorEl, setReportAnchorEl] = useState(null);\n  const [certificazioneAnchorEl, setCertificazioneAnchorEl] = useState(null);\n  const [comandeAnchorEl, setComandeAnchorEl] = useState(null);\n\n  // Recupera l'ID del cantiere selezionato dal localStorage\n  const selectedCantiereId = localStorage.getItem('selectedCantiereId');\n  const selectedCantiereName = localStorage.getItem('selectedCantiereName');\n\n  // Funzioni per aprire/chiudere i menu\n  const handleMenuOpen = (event, setAnchorEl) => {\n    setAnchorEl(event.currentTarget);\n  };\n  const handleMenuClose = setAnchorEl => {\n    setAnchorEl(null);\n  };\n\n  // Gestisce l'apertura del popup Excel\n  const handleOpenExcelPopup = operationType => {\n    setExcelOperationType(operationType);\n    setExcelPopupOpen(true);\n    handleMenuClose(setExcelAnchorEl);\n  };\n\n  // Gestisce la creazione diretta dei template senza popup\n  const handleCreateTemplateDirect = async templateType => {\n    try {\n      handleMenuClose(setExcelAnchorEl);\n      if (templateType === 'cavi') {\n        const excelService = await import('../services/excelService');\n        await excelService.default.createCaviTemplate();\n        showSnackbar('Template Excel per cavi creato e download avviato! Controlla la cartella Download del tuo browser.', 'success');\n      } else if (templateType === 'parco-bobine') {\n        const excelService = await import('../services/excelService');\n        await excelService.default.createParcoBobineTemplate();\n        showSnackbar('Template Excel per parco bobine creato e download avviato! Controlla la cartella Download del tuo browser.', 'success');\n      }\n    } catch (error) {\n      console.error(`Errore nella creazione del template ${templateType}:`, error);\n      showSnackbar(`Errore nella creazione del template ${templateType}: ${error.message || 'Errore sconosciuto'}`, 'error');\n    }\n  };\n\n  // Gestisce la chiusura del popup Excel\n  const handleCloseExcelPopup = () => {\n    setExcelPopupOpen(false);\n  };\n\n  // Gestisce il successo delle operazioni Excel\n  const handleExcelSuccess = message => {\n    setSnackbar({\n      open: true,\n      message,\n      severity: 'success'\n    });\n  };\n\n  // Gestisce gli errori delle operazioni Excel\n  const handleExcelError = message => {\n    setSnackbar({\n      open: true,\n      message,\n      severity: 'error'\n    });\n  };\n\n  // Gestisce la chiusura dello snackbar\n  const handleCloseSnackbar = () => {\n    setSnackbar({\n      ...snackbar,\n      open: false\n    });\n  };\n\n  // Naviga a un percorso\n  const navigateTo = path => {\n    // Gestione speciale per il percorso Home\n    if (path === '/dashboard') {\n      // Se l'utente è un amministratore che sta impersonando un utente\n      if (isImpersonating) {\n        navigate('/dashboard/admin');\n      }\n      // Se l'utente è un amministratore normale\n      else if ((user === null || user === void 0 ? void 0 : user.role) === 'owner') {\n        navigate('/dashboard/admin');\n      }\n      // Se l'utente è un utente standard\n      else if ((user === null || user === void 0 ? void 0 : user.role) === 'user') {\n        navigate('/dashboard/cantieri');\n      }\n      // Se l'utente è un utente cantiere\n      else if ((user === null || user === void 0 ? void 0 : user.role) === 'cantieri_user') {\n        // Reindirizza direttamente alla pagina di visualizzazione cavi\n        navigate('/dashboard/cavi/visualizza');\n      }\n      // Fallback per altri tipi di utenti\n      else {\n        navigate(path);\n      }\n    } else {\n      navigate(path);\n    }\n\n    // Chiudi tutti i menu\n    handleMenuClose(setHomeAnchorEl);\n    handleMenuClose(setAdminAnchorEl);\n    handleMenuClose(setCantieriAnchorEl);\n    handleMenuClose(setCaviAnchorEl);\n    handleMenuClose(setPosaAnchorEl);\n    handleMenuClose(setParcoAnchorEl);\n    handleMenuClose(setExcelAnchorEl);\n    handleMenuClose(setReportAnchorEl);\n    handleMenuClose(setCertificazioneAnchorEl);\n    handleMenuClose(setComandeAnchorEl);\n  };\n  const handleLogout = () => {\n    logout();\n  };\n\n  // Verifica se un percorso è attivo\n  const isActive = path => {\n    return location.pathname === path;\n  };\n\n  // Verifica se un percorso è parte del percorso attivo (per i sottomenu)\n  const isPartOfActive = path => {\n    return location.pathname.startsWith(path);\n  };\n  return /*#__PURE__*/_jsxDEV(AppBar, {\n    position: \"static\",\n    color: \"default\",\n    elevation: 2,\n    sx: {\n      zIndex: 1100,\n      width: '100%',\n      overflowX: 'hidden'\n    },\n    className: \"excel-style-menu\",\n    children: [/*#__PURE__*/_jsxDEV(Toolbar, {\n      sx: {\n        overflowX: 'hidden',\n        height: '60px'\n      },\n      children: [/*#__PURE__*/_jsxDEV(Button, {\n        color: \"inherit\",\n        onClick: () => navigateTo('/dashboard'),\n        startIcon: /*#__PURE__*/_jsxDEV(HomeIcon, {}, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 201,\n          columnNumber: 22\n        }, this),\n        sx: {\n          mr: 1\n        },\n        className: isActive('/dashboard') ? 'active-button' : '',\n        children: isImpersonating ? \"Torna al Menu Admin\" : (user === null || user === void 0 ? void 0 : user.role) === 'owner' ? \"Pannello Admin\" : (user === null || user === void 0 ? void 0 : user.role) === 'user' ? \"Lista Cantieri\" : (user === null || user === void 0 ? void 0 : user.role) === 'cantieri_user' ? \"Gestione Cavi\" : \"Home\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 198,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Divider, {\n        orientation: \"vertical\",\n        flexItem: true,\n        sx: {\n          mx: 0.5\n        }\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 210,\n        columnNumber: 9\n      }, this), ((user === null || user === void 0 ? void 0 : user.role) !== 'owner' || (user === null || user === void 0 ? void 0 : user.role) === 'owner' && isImpersonating && impersonatedUser) && /*#__PURE__*/_jsxDEV(_Fragment, {\n        children: [isImpersonating && /*#__PURE__*/_jsxDEV(Button, {\n          color: \"inherit\",\n          onClick: () => navigateTo('/dashboard/cantieri'),\n          sx: {\n            mr: 1\n          },\n          className: isActive('/dashboard/cantieri') ? 'active-button' : '',\n          children: isImpersonating && impersonatedUser ? `Cantieri di ${impersonatedUser.username}` : \"Lista Cantieri\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 219,\n          columnNumber: 15\n        }, this), selectedCantiereId && /*#__PURE__*/_jsxDEV(_Fragment, {\n          children: [(user === null || user === void 0 ? void 0 : user.role) !== 'cantieri_user' && /*#__PURE__*/_jsxDEV(Button, {\n            color: \"inherit\",\n            onClick: () => navigateTo('/dashboard/cavi/visualizza'),\n            sx: {\n              mr: 1\n            },\n            className: isActive('/dashboard/cavi/visualizza') ? 'active-button' : '',\n            children: \"Visualizza Cavi\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 236,\n            columnNumber: 19\n          }, this), /*#__PURE__*/_jsxDEV(Button, {\n            color: \"inherit\",\n            \"aria-controls\": \"posa-menu\",\n            \"aria-haspopup\": \"true\",\n            onClick: e => handleMenuOpen(e, setPosaAnchorEl),\n            endIcon: /*#__PURE__*/_jsxDEV(ArrowDownIcon, {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 252,\n              columnNumber: 28\n            }, this),\n            sx: {\n              mr: 1\n            },\n            className: isPartOfActive('/dashboard/cavi/posa') ? 'active-button' : '',\n            children: \"Posa e Collegamenti\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 247,\n            columnNumber: 17\n          }, this), /*#__PURE__*/_jsxDEV(Button, {\n            color: \"inherit\",\n            \"aria-controls\": \"parco-menu\",\n            \"aria-haspopup\": \"true\",\n            onClick: () => navigateTo('/dashboard/cavi/parco/visualizza'),\n            sx: {\n              mr: 1\n            },\n            className: isPartOfActive('/dashboard/cavi/parco') ? 'active-button' : '',\n            children: \"Parco Cavi\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 260,\n            columnNumber: 17\n          }, this), /*#__PURE__*/_jsxDEV(Button, {\n            color: \"inherit\",\n            \"aria-controls\": \"excel-menu\",\n            \"aria-haspopup\": \"true\",\n            onClick: e => handleMenuOpen(e, setExcelAnchorEl),\n            endIcon: /*#__PURE__*/_jsxDEV(ArrowDownIcon, {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 277,\n              columnNumber: 28\n            }, this),\n            sx: {\n              mr: 1\n            },\n            className: isPartOfActive('/dashboard/cavi/excel') ? 'active-button' : '',\n            children: \"Gestione Excel\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 272,\n            columnNumber: 17\n          }, this), /*#__PURE__*/_jsxDEV(Button, {\n            color: \"inherit\",\n            \"aria-controls\": \"report-menu\",\n            \"aria-haspopup\": \"true\",\n            onClick: e => handleMenuOpen(e, setReportAnchorEl),\n            endIcon: /*#__PURE__*/_jsxDEV(ArrowDownIcon, {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 290,\n              columnNumber: 28\n            }, this),\n            sx: {\n              mr: 1\n            },\n            className: isPartOfActive('/dashboard/cavi/report') ? 'active-button' : '',\n            children: \"Report\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 285,\n            columnNumber: 17\n          }, this), /*#__PURE__*/_jsxDEV(Button, {\n            color: \"inherit\",\n            \"aria-controls\": \"certificazione-menu\",\n            \"aria-haspopup\": \"true\",\n            onClick: e => handleMenuOpen(e, setCertificazioneAnchorEl),\n            endIcon: /*#__PURE__*/_jsxDEV(ArrowDownIcon, {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 303,\n              columnNumber: 28\n            }, this),\n            sx: {\n              mr: 1\n            },\n            className: isPartOfActive('/dashboard/cavi/certificazione') ? 'active-button' : '',\n            children: \"Certificazione Cavi\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 298,\n            columnNumber: 17\n          }, this), /*#__PURE__*/_jsxDEV(Button, {\n            color: \"inherit\",\n            \"aria-controls\": \"comande-menu\",\n            \"aria-haspopup\": \"true\",\n            onClick: e => handleMenuOpen(e, setComandeAnchorEl),\n            endIcon: /*#__PURE__*/_jsxDEV(ArrowDownIcon, {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 316,\n              columnNumber: 28\n            }, this),\n            sx: {\n              mr: 1\n            },\n            className: isPartOfActive('/dashboard/cavi/comande') ? 'active-button' : '',\n            children: \"Gestione Comande\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 311,\n            columnNumber: 17\n          }, this), /*#__PURE__*/_jsxDEV(Menu, {\n            id: \"posa-menu\",\n            anchorEl: posaAnchorEl,\n            keepMounted: true,\n            open: Boolean(posaAnchorEl),\n            onClose: () => handleMenuClose(setPosaAnchorEl),\n            anchorOrigin: {\n              vertical: 'bottom',\n              horizontal: 'center'\n            },\n            transformOrigin: {\n              vertical: 'top',\n              horizontal: 'center'\n            },\n            className: \"excel-style-submenu\",\n            elevation: 3,\n            sx: {\n              mt: 0.5\n            },\n            children: [/*#__PURE__*/_jsxDEV(MenuItem, {\n              onClick: () => navigateTo('/dashboard/cavi/posa/inserisci-metri'),\n              children: \"Inserisci metri posati\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 342,\n              columnNumber: 19\n            }, this), /*#__PURE__*/_jsxDEV(MenuItem, {\n              onClick: () => {\n                // Apre il dialogo di modifica cavi invece di navigare alla pagina\n                setOpenModificaCavoDialog(true);\n                // Chiude il menu\n                handleMenuClose(setPosaAnchorEl);\n                // Naviga alla pagina visualizza cavi\n                navigateTo('/dashboard/cavi/visualizza');\n              },\n              children: \"Modifica cavo\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 343,\n              columnNumber: 19\n            }, this), /*#__PURE__*/_jsxDEV(MenuItem, {\n              onClick: () => {\n                // Apre il dialogo di aggiunta cavi invece di navigare alla pagina\n                setOpenAggiungiCavoDialog(true);\n                // Chiude il menu\n                handleMenuClose(setPosaAnchorEl);\n                // Naviga alla pagina visualizza cavi\n                navigateTo('/dashboard/cavi/visualizza');\n              },\n              children: \"Aggiungi nuovo cavo\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 351,\n              columnNumber: 19\n            }, this), /*#__PURE__*/_jsxDEV(MenuItem, {\n              onClick: () => {\n                // Apre il dialogo di eliminazione cavi invece di navigare alla pagina\n                setOpenEliminaCavoDialog(true);\n                // Chiude il menu\n                handleMenuClose(setPosaAnchorEl);\n                // Naviga alla pagina visualizza cavi\n                navigateTo('/dashboard/cavi/visualizza');\n              },\n              children: \"Elimina cavo\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 359,\n              columnNumber: 19\n            }, this), /*#__PURE__*/_jsxDEV(MenuItem, {\n              onClick: () => navigateTo('/dashboard/cavi/posa/modifica-bobina'),\n              children: \"Modifica bobina cavo posato\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 367,\n              columnNumber: 19\n            }, this), /*#__PURE__*/_jsxDEV(MenuItem, {\n              onClick: () => navigateTo('/dashboard/cavi/posa/collegamenti'),\n              children: \"Gestisci collegamenti cavo\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 368,\n              columnNumber: 19\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 324,\n            columnNumber: 17\n          }, this), /*#__PURE__*/_jsxDEV(Menu, {\n            id: \"parco-menu\",\n            anchorEl: parcoAnchorEl,\n            keepMounted: true,\n            open: Boolean(parcoAnchorEl),\n            onClose: () => handleMenuClose(setParcoAnchorEl),\n            anchorOrigin: {\n              vertical: 'bottom',\n              horizontal: 'center'\n            },\n            transformOrigin: {\n              vertical: 'top',\n              horizontal: 'center'\n            },\n            className: \"excel-style-submenu\",\n            elevation: 3,\n            sx: {\n              mt: 0.5\n            },\n            children: /*#__PURE__*/_jsxDEV(MenuItem, {\n              onClick: () => navigateTo('/dashboard/cavi/parco/storico'),\n              children: \"Visualizza Storico Utilizzo\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 390,\n              columnNumber: 19\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 372,\n            columnNumber: 17\n          }, this), /*#__PURE__*/_jsxDEV(Menu, {\n            id: \"excel-menu\",\n            anchorEl: excelAnchorEl,\n            keepMounted: true,\n            open: Boolean(excelAnchorEl),\n            onClose: () => handleMenuClose(setExcelAnchorEl),\n            anchorOrigin: {\n              vertical: 'bottom',\n              horizontal: 'center'\n            },\n            transformOrigin: {\n              vertical: 'top',\n              horizontal: 'center'\n            },\n            className: \"excel-style-submenu\",\n            elevation: 3,\n            sx: {\n              mt: 0.5\n            },\n            children: [/*#__PURE__*/_jsxDEV(MenuItem, {\n              onClick: () => handleOpenExcelPopup('importaCavi'),\n              children: [/*#__PURE__*/_jsxDEV(FileUploadIcon, {\n                fontSize: \"small\",\n                sx: {\n                  mr: 1\n                }\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 413,\n                columnNumber: 21\n              }, this), \"Importa Cavi\"]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 412,\n              columnNumber: 19\n            }, this), /*#__PURE__*/_jsxDEV(MenuItem, {\n              onClick: () => handleOpenExcelPopup('importaParcoBobine'),\n              children: [/*#__PURE__*/_jsxDEV(FileUploadIcon, {\n                fontSize: \"small\",\n                sx: {\n                  mr: 1\n                }\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 417,\n                columnNumber: 21\n              }, this), \"Importa Parco Bobine\"]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 416,\n              columnNumber: 19\n            }, this), /*#__PURE__*/_jsxDEV(Divider, {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 420,\n              columnNumber: 19\n            }, this), /*#__PURE__*/_jsxDEV(MenuItem, {\n              onClick: () => handleOpenExcelPopup('creaTemplateCavi'),\n              children: [/*#__PURE__*/_jsxDEV(FileDownloadIcon, {\n                fontSize: \"small\",\n                sx: {\n                  mr: 1\n                }\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 422,\n                columnNumber: 21\n              }, this), \"Template Cavi\"]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 421,\n              columnNumber: 19\n            }, this), /*#__PURE__*/_jsxDEV(MenuItem, {\n              onClick: () => handleOpenExcelPopup('creaTemplateParcoBobine'),\n              children: [/*#__PURE__*/_jsxDEV(FileDownloadIcon, {\n                fontSize: \"small\",\n                sx: {\n                  mr: 1\n                }\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 426,\n                columnNumber: 21\n              }, this), \"Template Parco Bobine\"]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 425,\n              columnNumber: 19\n            }, this), /*#__PURE__*/_jsxDEV(Divider, {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 429,\n              columnNumber: 19\n            }, this), /*#__PURE__*/_jsxDEV(MenuItem, {\n              onClick: () => handleOpenExcelPopup('esportaCavi'),\n              children: [/*#__PURE__*/_jsxDEV(FileDownloadIcon, {\n                fontSize: \"small\",\n                sx: {\n                  mr: 1\n                }\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 431,\n                columnNumber: 21\n              }, this), \"Esporta Cavi\"]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 430,\n              columnNumber: 19\n            }, this), /*#__PURE__*/_jsxDEV(MenuItem, {\n              onClick: () => handleOpenExcelPopup('esportaParcoBobine'),\n              children: [/*#__PURE__*/_jsxDEV(FileDownloadIcon, {\n                fontSize: \"small\",\n                sx: {\n                  mr: 1\n                }\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 435,\n                columnNumber: 21\n              }, this), \"Esporta Parco Bobine\"]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 434,\n              columnNumber: 19\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 394,\n            columnNumber: 17\n          }, this), /*#__PURE__*/_jsxDEV(Menu, {\n            id: \"report-menu\",\n            anchorEl: reportAnchorEl,\n            keepMounted: true,\n            open: Boolean(reportAnchorEl),\n            onClose: () => handleMenuClose(setReportAnchorEl),\n            anchorOrigin: {\n              vertical: 'bottom',\n              horizontal: 'center'\n            },\n            transformOrigin: {\n              vertical: 'top',\n              horizontal: 'center'\n            },\n            className: \"excel-style-submenu\",\n            elevation: 3,\n            sx: {\n              mt: 0.5\n            },\n            children: [/*#__PURE__*/_jsxDEV(MenuItem, {\n              onClick: () => navigateTo('/dashboard/cavi/report/avanzamento'),\n              children: \"Report Avanzamento\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 459,\n              columnNumber: 19\n            }, this), /*#__PURE__*/_jsxDEV(MenuItem, {\n              onClick: () => navigateTo('/dashboard/cavi/report/boq'),\n              children: \"Bill of Quantities\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 460,\n              columnNumber: 19\n            }, this), /*#__PURE__*/_jsxDEV(MenuItem, {\n              onClick: () => navigateTo('/dashboard/cavi/report/utilizzo-bobine'),\n              children: \"Report Utilizzo Bobine\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 461,\n              columnNumber: 19\n            }, this), /*#__PURE__*/_jsxDEV(MenuItem, {\n              onClick: () => navigateTo('/dashboard/cavi/report/statistiche'),\n              children: \"Statistiche Cantiere\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 462,\n              columnNumber: 19\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 441,\n            columnNumber: 17\n          }, this), /*#__PURE__*/_jsxDEV(Menu, {\n            id: \"certificazione-menu\",\n            anchorEl: certificazioneAnchorEl,\n            keepMounted: true,\n            open: Boolean(certificazioneAnchorEl),\n            onClose: () => handleMenuClose(setCertificazioneAnchorEl),\n            anchorOrigin: {\n              vertical: 'bottom',\n              horizontal: 'center'\n            },\n            transformOrigin: {\n              vertical: 'top',\n              horizontal: 'center'\n            },\n            className: \"excel-style-submenu\",\n            elevation: 3,\n            sx: {\n              mt: 0.5\n            },\n            children: [/*#__PURE__*/_jsxDEV(MenuItem, {\n              onClick: () => navigateTo('/dashboard/cavi/certificazione/visualizza'),\n              children: \"Visualizza certificazioni\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 484,\n              columnNumber: 19\n            }, this), /*#__PURE__*/_jsxDEV(MenuItem, {\n              onClick: () => navigateTo('/dashboard/cavi/certificazione/filtra'),\n              children: \"Filtra per cavo\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 485,\n              columnNumber: 19\n            }, this), /*#__PURE__*/_jsxDEV(MenuItem, {\n              onClick: () => navigateTo('/dashboard/cavi/certificazione/crea'),\n              children: \"Crea certificazione\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 486,\n              columnNumber: 19\n            }, this), /*#__PURE__*/_jsxDEV(MenuItem, {\n              onClick: () => navigateTo('/dashboard/cavi/certificazione/modifica'),\n              children: \"Modifica certificazione\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 487,\n              columnNumber: 19\n            }, this), /*#__PURE__*/_jsxDEV(MenuItem, {\n              onClick: () => navigateTo('/dashboard/cavi/certificazione/elimina'),\n              children: \"Elimina certificazione\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 488,\n              columnNumber: 19\n            }, this), /*#__PURE__*/_jsxDEV(MenuItem, {\n              onClick: () => navigateTo('/dashboard/cavi/certificazione/strumenti'),\n              children: \"Gestione strumenti\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 489,\n              columnNumber: 19\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 466,\n            columnNumber: 17\n          }, this), /*#__PURE__*/_jsxDEV(Menu, {\n            id: \"comande-menu\",\n            anchorEl: comandeAnchorEl,\n            keepMounted: true,\n            open: Boolean(comandeAnchorEl),\n            onClose: () => handleMenuClose(setComandeAnchorEl),\n            anchorOrigin: {\n              vertical: 'bottom',\n              horizontal: 'center'\n            },\n            transformOrigin: {\n              vertical: 'top',\n              horizontal: 'center'\n            },\n            className: \"excel-style-submenu\",\n            elevation: 3,\n            sx: {\n              mt: 0.5\n            },\n            children: [/*#__PURE__*/_jsxDEV(MenuItem, {\n              onClick: () => navigateTo('/dashboard/cavi/comande/visualizza'),\n              children: \"Visualizza comande\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 511,\n              columnNumber: 19\n            }, this), /*#__PURE__*/_jsxDEV(MenuItem, {\n              onClick: () => navigateTo('/dashboard/cavi/comande/crea'),\n              children: \"Crea nuova comanda\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 512,\n              columnNumber: 19\n            }, this), /*#__PURE__*/_jsxDEV(MenuItem, {\n              onClick: () => navigateTo('/dashboard/cavi/comande/modifica'),\n              children: \"Modifica comanda\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 513,\n              columnNumber: 19\n            }, this), /*#__PURE__*/_jsxDEV(MenuItem, {\n              onClick: () => navigateTo('/dashboard/cavi/comande/elimina'),\n              children: \"Elimina comanda\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 514,\n              columnNumber: 19\n            }, this), /*#__PURE__*/_jsxDEV(MenuItem, {\n              onClick: () => navigateTo('/dashboard/cavi/comande/stampa'),\n              children: \"Stampa comanda\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 515,\n              columnNumber: 19\n            }, this), /*#__PURE__*/_jsxDEV(MenuItem, {\n              onClick: () => navigateTo('/dashboard/cavi/comande/assegna'),\n              children: \"Assegna comanda a cavo\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 516,\n              columnNumber: 19\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 493,\n            columnNumber: 17\n          }, this)]\n        }, void 0, true)]\n      }, void 0, true), /*#__PURE__*/_jsxDEV(Box, {\n        sx: {\n          flexGrow: 1\n        }\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 524,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Box, {\n        sx: {\n          display: 'flex',\n          alignItems: 'center',\n          height: '100%'\n        },\n        children: [/*#__PURE__*/_jsxDEV(SelectedCantiereDisplay, {}, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 529,\n          columnNumber: 11\n        }, this), isImpersonating && impersonatedUser && /*#__PURE__*/_jsxDEV(Typography, {\n          variant: \"body2\",\n          color: \"textSecondary\",\n          sx: {\n            mr: 2.5,\n            fontSize: '1rem'\n          },\n          children: [\"Accesso come: \", /*#__PURE__*/_jsxDEV(\"b\", {\n            children: impersonatedUser.username\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 533,\n            columnNumber: 29\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 532,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(Typography, {\n          variant: \"body2\",\n          sx: {\n            mr: 2.5,\n            fontWeight: 500,\n            fontSize: '1rem'\n          },\n          children: (user === null || user === void 0 ? void 0 : user.username) || ''\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 536,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Tooltip, {\n          title: \"Logout\",\n          children: /*#__PURE__*/_jsxDEV(IconButton, {\n            color: \"inherit\",\n            onClick: handleLogout,\n            edge: \"end\",\n            sx: {\n              '&:hover': {\n                backgroundColor: '#e9ecef'\n              },\n              padding: '10px'\n            },\n            children: /*#__PURE__*/_jsxDEV(LogoutIcon, {\n              fontSize: \"medium\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 546,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 540,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 539,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 527,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 196,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(ExcelPopup, {\n      open: excelPopupOpen,\n      onClose: handleCloseExcelPopup,\n      operationType: excelOperationType,\n      cantiereId: cantiereId,\n      onSuccess: handleExcelSuccess,\n      onError: handleExcelError\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 553,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Snackbar, {\n      open: snackbar.open,\n      autoHideDuration: 6000,\n      onClose: handleCloseSnackbar,\n      anchorOrigin: {\n        vertical: 'bottom',\n        horizontal: 'center'\n      },\n      children: /*#__PURE__*/_jsxDEV(Alert, {\n        onClose: handleCloseSnackbar,\n        severity: snackbar.severity,\n        variant: \"filled\",\n        sx: {\n          width: '100%'\n        },\n        children: snackbar.message\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 569,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 563,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 195,\n    columnNumber: 5\n  }, this);\n};\n_s(TopNavbar, \"2+2maNY3lzG8Ct+UOH0OhTdt41A=\", false, function () {\n  return [useNavigate, useLocation, useAuth, useGlobalContext];\n});\n_c = TopNavbar;\nexport default TopNavbar;\nvar _c;\n$RefreshReg$(_c, \"TopNavbar\");", "map": {"version": 3, "names": ["React", "useState", "useNavigate", "useLocation", "AppBar", "<PERSON><PERSON><PERSON>", "Box", "<PERSON><PERSON>", "<PERSON><PERSON>", "MenuItem", "Typography", "IconButton", "Divider", "Avatar", "<PERSON><PERSON><PERSON>", "Snackbar", "<PERSON><PERSON>", "Home", "HomeIcon", "AdminPanelSettings", "AdminIcon", "Construction", "ConstructionIcon", "Cable", "CableIcon", "Description", "ReportIcon", "Logout", "LogoutIcon", "KeyboardArrowDown", "ArrowDownIcon", "FileUpload", "FileUploadIcon", "FileDownload", "FileDownloadIcon", "useAuth", "useGlobalContext", "SelectedCantiereDisplay", "ExcelPopup", "jsxDEV", "_jsxDEV", "Fragment", "_Fragment", "TopNavbar", "_s", "navigate", "location", "user", "logout", "isImpersonating", "impersonated<PERSON><PERSON>", "setOpenEliminaCavoDialog", "setOpenModificaCavoDialog", "setOpenAggiungiCavoDialog", "excelPopupOpen", "setExcelPopupOpen", "excelOperationType", "setExcelOperationType", "snackbar", "setSnackbar", "open", "message", "severity", "cantiereId", "parseInt", "localStorage", "getItem", "homeAnchorEl", "setHomeAnchorEl", "adminAnchorEl", "setAdminAnchorEl", "cantieriAnchorEl", "setCantieriAnchorEl", "caviAnchorEl", "setCaviAnchorEl", "posaAnchorEl", "setPosaAnchorEl", "parcoAnchorEl", "setParcoAnchorEl", "excelAnchorEl", "setExcelAnchorEl", "reportAnchorEl", "setReportAnchorEl", "certificazioneAnchorEl", "setCertificazioneAnchorEl", "comandeAnchorEl", "setComandeAnchorEl", "selectedCantiereId", "selectedCantiereName", "handleMenuOpen", "event", "setAnchorEl", "currentTarget", "handleMenuClose", "handleOpenExcelPopup", "operationType", "handleCreateTemplateDirect", "templateType", "excelService", "default", "createCaviTemplate", "showSnackbar", "createParcoBobineTemplate", "error", "console", "handleCloseExcelPopup", "handleExcelSuccess", "handleExcelError", "handleCloseSnackbar", "navigateTo", "path", "role", "handleLogout", "isActive", "pathname", "isPartOfActive", "startsWith", "position", "color", "elevation", "sx", "zIndex", "width", "overflowX", "className", "children", "height", "onClick", "startIcon", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "mr", "orientation", "flexItem", "mx", "username", "e", "endIcon", "id", "anchorEl", "keepMounted", "Boolean", "onClose", "anchor<PERSON><PERSON><PERSON>", "vertical", "horizontal", "transform<PERSON><PERSON>in", "mt", "fontSize", "flexGrow", "display", "alignItems", "variant", "fontWeight", "title", "edge", "backgroundColor", "padding", "onSuccess", "onError", "autoHideDuration", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/PycharmProjects/CMS/webapp/frontend/src/components/TopNavbar.js"], "sourcesContent": ["import React, { useState } from 'react';\nimport { useNavigate, useLocation } from 'react-router-dom';\nimport {\n  AppBar,\n  Toolbar,\n  Box,\n  Button,\n  Menu,\n  MenuItem,\n  Typography,\n  IconButton,\n  Divider,\n  Avatar,\n  Tooltip,\n  Snackbar,\n  Alert\n} from '@mui/material';\nimport {\n  Home as HomeIcon,\n  AdminPanelSettings as AdminIcon,\n  Construction as ConstructionIcon,\n  Cable as CableIcon,\n  Description as ReportIcon,\n  Logout as LogoutIcon,\n  KeyboardArrowDown as ArrowDownIcon,\n  FileUpload as FileUploadIcon,\n  FileDownload as FileDownloadIcon\n} from '@mui/icons-material';\nimport { useAuth } from '../context/AuthContext';\nimport { useGlobalContext } from '../context/GlobalContext';\nimport SelectedCantiereDisplay from './common/SelectedCantiereDisplay';\nimport ExcelPopup from './cavi/ExcelPopup';\nimport './TopNavbar.css';\n\nconst TopNavbar = () => {\n  const navigate = useNavigate();\n  const location = useLocation();\n  const { user, logout, isImpersonating, impersonatedUser } = useAuth();\n  const { setOpenEliminaCavoDialog, setOpenModificaCavoDialog, setOpenAggiungiCavoDialog } = useGlobalContext();\n\n  // Stato per il popup Excel\n  const [excelPopupOpen, setExcelPopupOpen] = useState(false);\n  const [excelOperationType, setExcelOperationType] = useState('');\n\n  // Stato per gli snackbar\n  const [snackbar, setSnackbar] = useState({\n    open: false,\n    message: '',\n    severity: 'success'\n  });\n\n  // Recupera l'ID del cantiere dal localStorage\n  const cantiereId = parseInt(localStorage.getItem('selectedCantiereId'), 10);\n\n  // Stati per i menu a tendina\n  const [homeAnchorEl, setHomeAnchorEl] = useState(null);\n  const [adminAnchorEl, setAdminAnchorEl] = useState(null);\n  const [cantieriAnchorEl, setCantieriAnchorEl] = useState(null);\n  const [caviAnchorEl, setCaviAnchorEl] = useState(null);\n  const [posaAnchorEl, setPosaAnchorEl] = useState(null);\n  const [parcoAnchorEl, setParcoAnchorEl] = useState(null);\n  const [excelAnchorEl, setExcelAnchorEl] = useState(null);\n  const [reportAnchorEl, setReportAnchorEl] = useState(null);\n  const [certificazioneAnchorEl, setCertificazioneAnchorEl] = useState(null);\n  const [comandeAnchorEl, setComandeAnchorEl] = useState(null);\n\n  // Recupera l'ID del cantiere selezionato dal localStorage\n  const selectedCantiereId = localStorage.getItem('selectedCantiereId');\n  const selectedCantiereName = localStorage.getItem('selectedCantiereName');\n\n  // Funzioni per aprire/chiudere i menu\n  const handleMenuOpen = (event, setAnchorEl) => {\n    setAnchorEl(event.currentTarget);\n  };\n\n  const handleMenuClose = (setAnchorEl) => {\n    setAnchorEl(null);\n  };\n\n  // Gestisce l'apertura del popup Excel\n  const handleOpenExcelPopup = (operationType) => {\n    setExcelOperationType(operationType);\n    setExcelPopupOpen(true);\n    handleMenuClose(setExcelAnchorEl);\n  };\n\n  // Gestisce la creazione diretta dei template senza popup\n  const handleCreateTemplateDirect = async (templateType) => {\n    try {\n      handleMenuClose(setExcelAnchorEl);\n\n      if (templateType === 'cavi') {\n        const excelService = await import('../services/excelService');\n        await excelService.default.createCaviTemplate();\n        showSnackbar('Template Excel per cavi creato e download avviato! Controlla la cartella Download del tuo browser.', 'success');\n      } else if (templateType === 'parco-bobine') {\n        const excelService = await import('../services/excelService');\n        await excelService.default.createParcoBobineTemplate();\n        showSnackbar('Template Excel per parco bobine creato e download avviato! Controlla la cartella Download del tuo browser.', 'success');\n      }\n    } catch (error) {\n      console.error(`Errore nella creazione del template ${templateType}:`, error);\n      showSnackbar(`Errore nella creazione del template ${templateType}: ${error.message || 'Errore sconosciuto'}`, 'error');\n    }\n  };\n\n  // Gestisce la chiusura del popup Excel\n  const handleCloseExcelPopup = () => {\n    setExcelPopupOpen(false);\n  };\n\n  // Gestisce il successo delle operazioni Excel\n  const handleExcelSuccess = (message) => {\n    setSnackbar({\n      open: true,\n      message,\n      severity: 'success'\n    });\n  };\n\n  // Gestisce gli errori delle operazioni Excel\n  const handleExcelError = (message) => {\n    setSnackbar({\n      open: true,\n      message,\n      severity: 'error'\n    });\n  };\n\n  // Gestisce la chiusura dello snackbar\n  const handleCloseSnackbar = () => {\n    setSnackbar({\n      ...snackbar,\n      open: false\n    });\n  };\n\n  // Naviga a un percorso\n  const navigateTo = (path) => {\n    // Gestione speciale per il percorso Home\n    if (path === '/dashboard') {\n      // Se l'utente è un amministratore che sta impersonando un utente\n      if (isImpersonating) {\n        navigate('/dashboard/admin');\n      }\n      // Se l'utente è un amministratore normale\n      else if (user?.role === 'owner') {\n        navigate('/dashboard/admin');\n      }\n      // Se l'utente è un utente standard\n      else if (user?.role === 'user') {\n        navigate('/dashboard/cantieri');\n      }\n      // Se l'utente è un utente cantiere\n      else if (user?.role === 'cantieri_user') {\n        // Reindirizza direttamente alla pagina di visualizzazione cavi\n        navigate('/dashboard/cavi/visualizza');\n      }\n      // Fallback per altri tipi di utenti\n      else {\n        navigate(path);\n      }\n    } else {\n      navigate(path);\n    }\n\n    // Chiudi tutti i menu\n    handleMenuClose(setHomeAnchorEl);\n    handleMenuClose(setAdminAnchorEl);\n    handleMenuClose(setCantieriAnchorEl);\n    handleMenuClose(setCaviAnchorEl);\n    handleMenuClose(setPosaAnchorEl);\n    handleMenuClose(setParcoAnchorEl);\n    handleMenuClose(setExcelAnchorEl);\n    handleMenuClose(setReportAnchorEl);\n    handleMenuClose(setCertificazioneAnchorEl);\n    handleMenuClose(setComandeAnchorEl);\n  };\n\n  const handleLogout = () => {\n    logout();\n  };\n\n  // Verifica se un percorso è attivo\n  const isActive = (path) => {\n    return location.pathname === path;\n  };\n\n  // Verifica se un percorso è parte del percorso attivo (per i sottomenu)\n  const isPartOfActive = (path) => {\n    return location.pathname.startsWith(path);\n  };\n\n  return (\n    <AppBar position=\"static\" color=\"default\" elevation={2} sx={{ zIndex: 1100, width: '100%', overflowX: 'hidden' }} className=\"excel-style-menu\">\n      <Toolbar sx={{ overflowX: 'hidden', height: '60px' }}>\n        {/* Logo/Home - Testo personalizzato in base al tipo di utente */}\n        <Button\n          color=\"inherit\"\n          onClick={() => navigateTo('/dashboard')}\n          startIcon={<HomeIcon />}\n          sx={{ mr: 1 }}\n          className={isActive('/dashboard') ? 'active-button' : ''}\n        >\n          {isImpersonating ? \"Torna al Menu Admin\" :\n           user?.role === 'owner' ? \"Pannello Admin\" :\n           user?.role === 'user' ? \"Lista Cantieri\" :\n           user?.role === 'cantieri_user' ? \"Gestione Cavi\" : \"Home\"}\n        </Button>\n        <Divider orientation=\"vertical\" flexItem sx={{ mx: 0.5 }} />\n\n        {/* Il menu Amministratore è stato rimosso perché ridondante con il pulsante Home per gli amministratori */}\n\n        {/* Menu per utenti standard e cantieri */}\n        {(user?.role !== 'owner' || (user?.role === 'owner' && isImpersonating && impersonatedUser)) && (\n          <>\n            {/* Pulsante Lista Cantieri solo per utenti che impersonano */}\n            {isImpersonating && (\n              <Button\n                color=\"inherit\"\n                onClick={() => navigateTo('/dashboard/cantieri')}\n                sx={{ mr: 1 }}\n                className={isActive('/dashboard/cantieri') ? 'active-button' : ''}\n              >\n                {isImpersonating && impersonatedUser ? `Cantieri di ${impersonatedUser.username}` : \"Lista Cantieri\"}\n              </Button>\n            )}\n\n            {/* Il cantiere selezionato è stato spostato nella parte destra della barra di navigazione */}\n\n            {/* Menu di gestione cavi - visibile solo se un cantiere è selezionato */}\n            {selectedCantiereId && (\n              <>\n                {/* Visualizza Cavi - nascosto per utenti cantiere perché ridondante con il tasto Home */}\n                {user?.role !== 'cantieri_user' && (\n                  <Button\n                    color=\"inherit\"\n                    onClick={() => navigateTo('/dashboard/cavi/visualizza')}\n                    sx={{ mr: 1 }}\n                    className={isActive('/dashboard/cavi/visualizza') ? 'active-button' : ''}\n                  >\n                    Visualizza Cavi\n                  </Button>\n                )}\n\n                {/* Posa e Collegamenti */}\n                <Button\n                  color=\"inherit\"\n                  aria-controls=\"posa-menu\"\n                  aria-haspopup=\"true\"\n                  onClick={(e) => handleMenuOpen(e, setPosaAnchorEl)}\n                  endIcon={<ArrowDownIcon />}\n                  sx={{ mr: 1 }}\n                  className={isPartOfActive('/dashboard/cavi/posa') ? 'active-button' : ''}\n                >\n                  Posa e Collegamenti\n                </Button>\n\n                {/* Parco Cavi */}\n                <Button\n                  color=\"inherit\"\n                  aria-controls=\"parco-menu\"\n                  aria-haspopup=\"true\"\n                  onClick={() => navigateTo('/dashboard/cavi/parco/visualizza')}\n                  sx={{ mr: 1 }}\n                  className={isPartOfActive('/dashboard/cavi/parco') ? 'active-button' : ''}\n                >\n                  Parco Cavi\n                </Button>\n\n                {/* Gestione Excel */}\n                <Button\n                  color=\"inherit\"\n                  aria-controls=\"excel-menu\"\n                  aria-haspopup=\"true\"\n                  onClick={(e) => handleMenuOpen(e, setExcelAnchorEl)}\n                  endIcon={<ArrowDownIcon />}\n                  sx={{ mr: 1 }}\n                  className={isPartOfActive('/dashboard/cavi/excel') ? 'active-button' : ''}\n                >\n                  Gestione Excel\n                </Button>\n\n                {/* Report */}\n                <Button\n                  color=\"inherit\"\n                  aria-controls=\"report-menu\"\n                  aria-haspopup=\"true\"\n                  onClick={(e) => handleMenuOpen(e, setReportAnchorEl)}\n                  endIcon={<ArrowDownIcon />}\n                  sx={{ mr: 1 }}\n                  className={isPartOfActive('/dashboard/cavi/report') ? 'active-button' : ''}\n                >\n                  Report\n                </Button>\n\n                {/* Certificazione Cavi */}\n                <Button\n                  color=\"inherit\"\n                  aria-controls=\"certificazione-menu\"\n                  aria-haspopup=\"true\"\n                  onClick={(e) => handleMenuOpen(e, setCertificazioneAnchorEl)}\n                  endIcon={<ArrowDownIcon />}\n                  sx={{ mr: 1 }}\n                  className={isPartOfActive('/dashboard/cavi/certificazione') ? 'active-button' : ''}\n                >\n                  Certificazione Cavi\n                </Button>\n\n                {/* Gestione Comande */}\n                <Button\n                  color=\"inherit\"\n                  aria-controls=\"comande-menu\"\n                  aria-haspopup=\"true\"\n                  onClick={(e) => handleMenuOpen(e, setComandeAnchorEl)}\n                  endIcon={<ArrowDownIcon />}\n                  sx={{ mr: 1 }}\n                  className={isPartOfActive('/dashboard/cavi/comande') ? 'active-button' : ''}\n                >\n                  Gestione Comande\n                </Button>\n\n                {/* Sottomenu Posa e Collegamenti */}\n                <Menu\n                  id=\"posa-menu\"\n                  anchorEl={posaAnchorEl}\n                  keepMounted\n                  open={Boolean(posaAnchorEl)}\n                  onClose={() => handleMenuClose(setPosaAnchorEl)}\n                  anchorOrigin={{\n                    vertical: 'bottom',\n                    horizontal: 'center',\n                  }}\n                  transformOrigin={{\n                    vertical: 'top',\n                    horizontal: 'center',\n                  }}\n                  className=\"excel-style-submenu\"\n                  elevation={3}\n                  sx={{ mt: 0.5 }}\n                >\n                  <MenuItem onClick={() => navigateTo('/dashboard/cavi/posa/inserisci-metri')}>Inserisci metri posati</MenuItem>\n                  <MenuItem onClick={() => {\n                    // Apre il dialogo di modifica cavi invece di navigare alla pagina\n                    setOpenModificaCavoDialog(true);\n                    // Chiude il menu\n                    handleMenuClose(setPosaAnchorEl);\n                    // Naviga alla pagina visualizza cavi\n                    navigateTo('/dashboard/cavi/visualizza');\n                  }}>Modifica cavo</MenuItem>\n                  <MenuItem onClick={() => {\n                    // Apre il dialogo di aggiunta cavi invece di navigare alla pagina\n                    setOpenAggiungiCavoDialog(true);\n                    // Chiude il menu\n                    handleMenuClose(setPosaAnchorEl);\n                    // Naviga alla pagina visualizza cavi\n                    navigateTo('/dashboard/cavi/visualizza');\n                  }}>Aggiungi nuovo cavo</MenuItem>\n                  <MenuItem onClick={() => {\n                    // Apre il dialogo di eliminazione cavi invece di navigare alla pagina\n                    setOpenEliminaCavoDialog(true);\n                    // Chiude il menu\n                    handleMenuClose(setPosaAnchorEl);\n                    // Naviga alla pagina visualizza cavi\n                    navigateTo('/dashboard/cavi/visualizza');\n                  }}>Elimina cavo</MenuItem>\n                  <MenuItem onClick={() => navigateTo('/dashboard/cavi/posa/modifica-bobina')}>Modifica bobina cavo posato</MenuItem>\n                  <MenuItem onClick={() => navigateTo('/dashboard/cavi/posa/collegamenti')}>Gestisci collegamenti cavo</MenuItem>\n                </Menu>\n\n                {/* Sottomenu Parco Cavi */}\n                <Menu\n                  id=\"parco-menu\"\n                  anchorEl={parcoAnchorEl}\n                  keepMounted\n                  open={Boolean(parcoAnchorEl)}\n                  onClose={() => handleMenuClose(setParcoAnchorEl)}\n                  anchorOrigin={{\n                    vertical: 'bottom',\n                    horizontal: 'center',\n                  }}\n                  transformOrigin={{\n                    vertical: 'top',\n                    horizontal: 'center',\n                  }}\n                  className=\"excel-style-submenu\"\n                  elevation={3}\n                  sx={{ mt: 0.5 }}\n                >\n                  <MenuItem onClick={() => navigateTo('/dashboard/cavi/parco/storico')}>Visualizza Storico Utilizzo</MenuItem>\n                </Menu>\n\n                {/* Sottomenu Gestione Excel */}\n                <Menu\n                  id=\"excel-menu\"\n                  anchorEl={excelAnchorEl}\n                  keepMounted\n                  open={Boolean(excelAnchorEl)}\n                  onClose={() => handleMenuClose(setExcelAnchorEl)}\n                  anchorOrigin={{\n                    vertical: 'bottom',\n                    horizontal: 'center',\n                  }}\n                  transformOrigin={{\n                    vertical: 'top',\n                    horizontal: 'center',\n                  }}\n                  className=\"excel-style-submenu\"\n                  elevation={3}\n                  sx={{ mt: 0.5 }}\n                >\n                  <MenuItem onClick={() => handleOpenExcelPopup('importaCavi')}>\n                    <FileUploadIcon fontSize=\"small\" sx={{ mr: 1 }} />\n                    Importa Cavi\n                  </MenuItem>\n                  <MenuItem onClick={() => handleOpenExcelPopup('importaParcoBobine')}>\n                    <FileUploadIcon fontSize=\"small\" sx={{ mr: 1 }} />\n                    Importa Parco Bobine\n                  </MenuItem>\n                  <Divider />\n                  <MenuItem onClick={() => handleOpenExcelPopup('creaTemplateCavi')}>\n                    <FileDownloadIcon fontSize=\"small\" sx={{ mr: 1 }} />\n                    Template Cavi\n                  </MenuItem>\n                  <MenuItem onClick={() => handleOpenExcelPopup('creaTemplateParcoBobine')}>\n                    <FileDownloadIcon fontSize=\"small\" sx={{ mr: 1 }} />\n                    Template Parco Bobine\n                  </MenuItem>\n                  <Divider />\n                  <MenuItem onClick={() => handleOpenExcelPopup('esportaCavi')}>\n                    <FileDownloadIcon fontSize=\"small\" sx={{ mr: 1 }} />\n                    Esporta Cavi\n                  </MenuItem>\n                  <MenuItem onClick={() => handleOpenExcelPopup('esportaParcoBobine')}>\n                    <FileDownloadIcon fontSize=\"small\" sx={{ mr: 1 }} />\n                    Esporta Parco Bobine\n                  </MenuItem>\n                </Menu>\n\n                {/* Sottomenu Report */}\n                <Menu\n                  id=\"report-menu\"\n                  anchorEl={reportAnchorEl}\n                  keepMounted\n                  open={Boolean(reportAnchorEl)}\n                  onClose={() => handleMenuClose(setReportAnchorEl)}\n                  anchorOrigin={{\n                    vertical: 'bottom',\n                    horizontal: 'center',\n                  }}\n                  transformOrigin={{\n                    vertical: 'top',\n                    horizontal: 'center',\n                  }}\n                  className=\"excel-style-submenu\"\n                  elevation={3}\n                  sx={{ mt: 0.5 }}\n                >\n                  <MenuItem onClick={() => navigateTo('/dashboard/cavi/report/avanzamento')}>Report Avanzamento</MenuItem>\n                  <MenuItem onClick={() => navigateTo('/dashboard/cavi/report/boq')}>Bill of Quantities</MenuItem>\n                  <MenuItem onClick={() => navigateTo('/dashboard/cavi/report/utilizzo-bobine')}>Report Utilizzo Bobine</MenuItem>\n                  <MenuItem onClick={() => navigateTo('/dashboard/cavi/report/statistiche')}>Statistiche Cantiere</MenuItem>\n                </Menu>\n\n                {/* Sottomenu Certificazione Cavi */}\n                <Menu\n                  id=\"certificazione-menu\"\n                  anchorEl={certificazioneAnchorEl}\n                  keepMounted\n                  open={Boolean(certificazioneAnchorEl)}\n                  onClose={() => handleMenuClose(setCertificazioneAnchorEl)}\n                  anchorOrigin={{\n                    vertical: 'bottom',\n                    horizontal: 'center',\n                  }}\n                  transformOrigin={{\n                    vertical: 'top',\n                    horizontal: 'center',\n                  }}\n                  className=\"excel-style-submenu\"\n                  elevation={3}\n                  sx={{ mt: 0.5 }}\n                >\n                  <MenuItem onClick={() => navigateTo('/dashboard/cavi/certificazione/visualizza')}>Visualizza certificazioni</MenuItem>\n                  <MenuItem onClick={() => navigateTo('/dashboard/cavi/certificazione/filtra')}>Filtra per cavo</MenuItem>\n                  <MenuItem onClick={() => navigateTo('/dashboard/cavi/certificazione/crea')}>Crea certificazione</MenuItem>\n                  <MenuItem onClick={() => navigateTo('/dashboard/cavi/certificazione/modifica')}>Modifica certificazione</MenuItem>\n                  <MenuItem onClick={() => navigateTo('/dashboard/cavi/certificazione/elimina')}>Elimina certificazione</MenuItem>\n                  <MenuItem onClick={() => navigateTo('/dashboard/cavi/certificazione/strumenti')}>Gestione strumenti</MenuItem>\n                </Menu>\n\n                {/* Sottomenu Gestione Comande */}\n                <Menu\n                  id=\"comande-menu\"\n                  anchorEl={comandeAnchorEl}\n                  keepMounted\n                  open={Boolean(comandeAnchorEl)}\n                  onClose={() => handleMenuClose(setComandeAnchorEl)}\n                  anchorOrigin={{\n                    vertical: 'bottom',\n                    horizontal: 'center',\n                  }}\n                  transformOrigin={{\n                    vertical: 'top',\n                    horizontal: 'center',\n                  }}\n                  className=\"excel-style-submenu\"\n                  elevation={3}\n                  sx={{ mt: 0.5 }}\n                >\n                  <MenuItem onClick={() => navigateTo('/dashboard/cavi/comande/visualizza')}>Visualizza comande</MenuItem>\n                  <MenuItem onClick={() => navigateTo('/dashboard/cavi/comande/crea')}>Crea nuova comanda</MenuItem>\n                  <MenuItem onClick={() => navigateTo('/dashboard/cavi/comande/modifica')}>Modifica comanda</MenuItem>\n                  <MenuItem onClick={() => navigateTo('/dashboard/cavi/comande/elimina')}>Elimina comanda</MenuItem>\n                  <MenuItem onClick={() => navigateTo('/dashboard/cavi/comande/stampa')}>Stampa comanda</MenuItem>\n                  <MenuItem onClick={() => navigateTo('/dashboard/cavi/comande/assegna')}>Assegna comanda a cavo</MenuItem>\n                </Menu>\n              </>\n            )}\n          </>\n        )}\n\n        {/* Spacer */}\n        <Box sx={{ flexGrow: 1 }} />\n\n        {/* Informazioni utente e logout */}\n        <Box sx={{ display: 'flex', alignItems: 'center', height: '100%' }}>\n          {/* Mostra il cantiere selezionato */}\n          <SelectedCantiereDisplay />\n\n          {isImpersonating && impersonatedUser && (\n            <Typography variant=\"body2\" color=\"textSecondary\" sx={{ mr: 2.5, fontSize: '1rem' }}>\n              Accesso come: <b>{impersonatedUser.username}</b>\n            </Typography>\n          )}\n          <Typography variant=\"body2\" sx={{ mr: 2.5, fontWeight: 500, fontSize: '1rem' }}>\n            {user?.username || ''}\n          </Typography>\n          <Tooltip title=\"Logout\">\n            <IconButton\n              color=\"inherit\"\n              onClick={handleLogout}\n              edge=\"end\"\n              sx={{ '&:hover': { backgroundColor: '#e9ecef' }, padding: '10px' }}\n            >\n              <LogoutIcon fontSize=\"medium\" />\n            </IconButton>\n          </Tooltip>\n        </Box>\n      </Toolbar>\n\n      {/* Excel Popup */}\n      <ExcelPopup\n        open={excelPopupOpen}\n        onClose={handleCloseExcelPopup}\n        operationType={excelOperationType}\n        cantiereId={cantiereId}\n        onSuccess={handleExcelSuccess}\n        onError={handleExcelError}\n      />\n\n      {/* Snackbar per messaggi di successo/errore */}\n      <Snackbar\n        open={snackbar.open}\n        autoHideDuration={6000}\n        onClose={handleCloseSnackbar}\n        anchorOrigin={{ vertical: 'bottom', horizontal: 'center' }}\n      >\n        <Alert\n          onClose={handleCloseSnackbar}\n          severity={snackbar.severity}\n          variant=\"filled\"\n          sx={{ width: '100%' }}\n        >\n          {snackbar.message}\n        </Alert>\n      </Snackbar>\n    </AppBar>\n  );\n};\n\nexport default TopNavbar;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,QAAQ,OAAO;AACvC,SAASC,WAAW,EAAEC,WAAW,QAAQ,kBAAkB;AAC3D,SACEC,MAAM,EACNC,OAAO,EACPC,GAAG,EACHC,MAAM,EACNC,IAAI,EACJC,QAAQ,EACRC,UAAU,EACVC,UAAU,EACVC,OAAO,EACPC,MAAM,EACNC,OAAO,EACPC,QAAQ,EACRC,KAAK,QACA,eAAe;AACtB,SACEC,IAAI,IAAIC,QAAQ,EAChBC,kBAAkB,IAAIC,SAAS,EAC/BC,YAAY,IAAIC,gBAAgB,EAChCC,KAAK,IAAIC,SAAS,EAClBC,WAAW,IAAIC,UAAU,EACzBC,MAAM,IAAIC,UAAU,EACpBC,iBAAiB,IAAIC,aAAa,EAClCC,UAAU,IAAIC,cAAc,EAC5BC,YAAY,IAAIC,gBAAgB,QAC3B,qBAAqB;AAC5B,SAASC,OAAO,QAAQ,wBAAwB;AAChD,SAASC,gBAAgB,QAAQ,0BAA0B;AAC3D,OAAOC,uBAAuB,MAAM,kCAAkC;AACtE,OAAOC,UAAU,MAAM,mBAAmB;AAC1C,OAAO,iBAAiB;AAAC,SAAAC,MAAA,IAAAC,OAAA,EAAAC,QAAA,IAAAC,SAAA;AAEzB,MAAMC,SAAS,GAAGA,CAAA,KAAM;EAAAC,EAAA;EACtB,MAAMC,QAAQ,GAAG3C,WAAW,CAAC,CAAC;EAC9B,MAAM4C,QAAQ,GAAG3C,WAAW,CAAC,CAAC;EAC9B,MAAM;IAAE4C,IAAI;IAAEC,MAAM;IAAEC,eAAe;IAAEC;EAAiB,CAAC,GAAGf,OAAO,CAAC,CAAC;EACrE,MAAM;IAAEgB,wBAAwB;IAAEC,yBAAyB;IAAEC;EAA0B,CAAC,GAAGjB,gBAAgB,CAAC,CAAC;;EAE7G;EACA,MAAM,CAACkB,cAAc,EAAEC,iBAAiB,CAAC,GAAGtD,QAAQ,CAAC,KAAK,CAAC;EAC3D,MAAM,CAACuD,kBAAkB,EAAEC,qBAAqB,CAAC,GAAGxD,QAAQ,CAAC,EAAE,CAAC;;EAEhE;EACA,MAAM,CAACyD,QAAQ,EAAEC,WAAW,CAAC,GAAG1D,QAAQ,CAAC;IACvC2D,IAAI,EAAE,KAAK;IACXC,OAAO,EAAE,EAAE;IACXC,QAAQ,EAAE;EACZ,CAAC,CAAC;;EAEF;EACA,MAAMC,UAAU,GAAGC,QAAQ,CAACC,YAAY,CAACC,OAAO,CAAC,oBAAoB,CAAC,EAAE,EAAE,CAAC;;EAE3E;EACA,MAAM,CAACC,YAAY,EAAEC,eAAe,CAAC,GAAGnE,QAAQ,CAAC,IAAI,CAAC;EACtD,MAAM,CAACoE,aAAa,EAAEC,gBAAgB,CAAC,GAAGrE,QAAQ,CAAC,IAAI,CAAC;EACxD,MAAM,CAACsE,gBAAgB,EAAEC,mBAAmB,CAAC,GAAGvE,QAAQ,CAAC,IAAI,CAAC;EAC9D,MAAM,CAACwE,YAAY,EAAEC,eAAe,CAAC,GAAGzE,QAAQ,CAAC,IAAI,CAAC;EACtD,MAAM,CAAC0E,YAAY,EAAEC,eAAe,CAAC,GAAG3E,QAAQ,CAAC,IAAI,CAAC;EACtD,MAAM,CAAC4E,aAAa,EAAEC,gBAAgB,CAAC,GAAG7E,QAAQ,CAAC,IAAI,CAAC;EACxD,MAAM,CAAC8E,aAAa,EAAEC,gBAAgB,CAAC,GAAG/E,QAAQ,CAAC,IAAI,CAAC;EACxD,MAAM,CAACgF,cAAc,EAAEC,iBAAiB,CAAC,GAAGjF,QAAQ,CAAC,IAAI,CAAC;EAC1D,MAAM,CAACkF,sBAAsB,EAAEC,yBAAyB,CAAC,GAAGnF,QAAQ,CAAC,IAAI,CAAC;EAC1E,MAAM,CAACoF,eAAe,EAAEC,kBAAkB,CAAC,GAAGrF,QAAQ,CAAC,IAAI,CAAC;;EAE5D;EACA,MAAMsF,kBAAkB,GAAGtB,YAAY,CAACC,OAAO,CAAC,oBAAoB,CAAC;EACrE,MAAMsB,oBAAoB,GAAGvB,YAAY,CAACC,OAAO,CAAC,sBAAsB,CAAC;;EAEzE;EACA,MAAMuB,cAAc,GAAGA,CAACC,KAAK,EAAEC,WAAW,KAAK;IAC7CA,WAAW,CAACD,KAAK,CAACE,aAAa,CAAC;EAClC,CAAC;EAED,MAAMC,eAAe,GAAIF,WAAW,IAAK;IACvCA,WAAW,CAAC,IAAI,CAAC;EACnB,CAAC;;EAED;EACA,MAAMG,oBAAoB,GAAIC,aAAa,IAAK;IAC9CtC,qBAAqB,CAACsC,aAAa,CAAC;IACpCxC,iBAAiB,CAAC,IAAI,CAAC;IACvBsC,eAAe,CAACb,gBAAgB,CAAC;EACnC,CAAC;;EAED;EACA,MAAMgB,0BAA0B,GAAG,MAAOC,YAAY,IAAK;IACzD,IAAI;MACFJ,eAAe,CAACb,gBAAgB,CAAC;MAEjC,IAAIiB,YAAY,KAAK,MAAM,EAAE;QAC3B,MAAMC,YAAY,GAAG,MAAM,MAAM,CAAC,0BAA0B,CAAC;QAC7D,MAAMA,YAAY,CAACC,OAAO,CAACC,kBAAkB,CAAC,CAAC;QAC/CC,YAAY,CAAC,oGAAoG,EAAE,SAAS,CAAC;MAC/H,CAAC,MAAM,IAAIJ,YAAY,KAAK,cAAc,EAAE;QAC1C,MAAMC,YAAY,GAAG,MAAM,MAAM,CAAC,0BAA0B,CAAC;QAC7D,MAAMA,YAAY,CAACC,OAAO,CAACG,yBAAyB,CAAC,CAAC;QACtDD,YAAY,CAAC,4GAA4G,EAAE,SAAS,CAAC;MACvI;IACF,CAAC,CAAC,OAAOE,KAAK,EAAE;MACdC,OAAO,CAACD,KAAK,CAAC,uCAAuCN,YAAY,GAAG,EAAEM,KAAK,CAAC;MAC5EF,YAAY,CAAC,uCAAuCJ,YAAY,KAAKM,KAAK,CAAC1C,OAAO,IAAI,oBAAoB,EAAE,EAAE,OAAO,CAAC;IACxH;EACF,CAAC;;EAED;EACA,MAAM4C,qBAAqB,GAAGA,CAAA,KAAM;IAClClD,iBAAiB,CAAC,KAAK,CAAC;EAC1B,CAAC;;EAED;EACA,MAAMmD,kBAAkB,GAAI7C,OAAO,IAAK;IACtCF,WAAW,CAAC;MACVC,IAAI,EAAE,IAAI;MACVC,OAAO;MACPC,QAAQ,EAAE;IACZ,CAAC,CAAC;EACJ,CAAC;;EAED;EACA,MAAM6C,gBAAgB,GAAI9C,OAAO,IAAK;IACpCF,WAAW,CAAC;MACVC,IAAI,EAAE,IAAI;MACVC,OAAO;MACPC,QAAQ,EAAE;IACZ,CAAC,CAAC;EACJ,CAAC;;EAED;EACA,MAAM8C,mBAAmB,GAAGA,CAAA,KAAM;IAChCjD,WAAW,CAAC;MACV,GAAGD,QAAQ;MACXE,IAAI,EAAE;IACR,CAAC,CAAC;EACJ,CAAC;;EAED;EACA,MAAMiD,UAAU,GAAIC,IAAI,IAAK;IAC3B;IACA,IAAIA,IAAI,KAAK,YAAY,EAAE;MACzB;MACA,IAAI7D,eAAe,EAAE;QACnBJ,QAAQ,CAAC,kBAAkB,CAAC;MAC9B;MACA;MAAA,KACK,IAAI,CAAAE,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAEgE,IAAI,MAAK,OAAO,EAAE;QAC/BlE,QAAQ,CAAC,kBAAkB,CAAC;MAC9B;MACA;MAAA,KACK,IAAI,CAAAE,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAEgE,IAAI,MAAK,MAAM,EAAE;QAC9BlE,QAAQ,CAAC,qBAAqB,CAAC;MACjC;MACA;MAAA,KACK,IAAI,CAAAE,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAEgE,IAAI,MAAK,eAAe,EAAE;QACvC;QACAlE,QAAQ,CAAC,4BAA4B,CAAC;MACxC;MACA;MAAA,KACK;QACHA,QAAQ,CAACiE,IAAI,CAAC;MAChB;IACF,CAAC,MAAM;MACLjE,QAAQ,CAACiE,IAAI,CAAC;IAChB;;IAEA;IACAjB,eAAe,CAACzB,eAAe,CAAC;IAChCyB,eAAe,CAACvB,gBAAgB,CAAC;IACjCuB,eAAe,CAACrB,mBAAmB,CAAC;IACpCqB,eAAe,CAACnB,eAAe,CAAC;IAChCmB,eAAe,CAACjB,eAAe,CAAC;IAChCiB,eAAe,CAACf,gBAAgB,CAAC;IACjCe,eAAe,CAACb,gBAAgB,CAAC;IACjCa,eAAe,CAACX,iBAAiB,CAAC;IAClCW,eAAe,CAACT,yBAAyB,CAAC;IAC1CS,eAAe,CAACP,kBAAkB,CAAC;EACrC,CAAC;EAED,MAAM0B,YAAY,GAAGA,CAAA,KAAM;IACzBhE,MAAM,CAAC,CAAC;EACV,CAAC;;EAED;EACA,MAAMiE,QAAQ,GAAIH,IAAI,IAAK;IACzB,OAAOhE,QAAQ,CAACoE,QAAQ,KAAKJ,IAAI;EACnC,CAAC;;EAED;EACA,MAAMK,cAAc,GAAIL,IAAI,IAAK;IAC/B,OAAOhE,QAAQ,CAACoE,QAAQ,CAACE,UAAU,CAACN,IAAI,CAAC;EAC3C,CAAC;EAED,oBACEtE,OAAA,CAACpC,MAAM;IAACiH,QAAQ,EAAC,QAAQ;IAACC,KAAK,EAAC,SAAS;IAACC,SAAS,EAAE,CAAE;IAACC,EAAE,EAAE;MAAEC,MAAM,EAAE,IAAI;MAAEC,KAAK,EAAE,MAAM;MAAEC,SAAS,EAAE;IAAS,CAAE;IAACC,SAAS,EAAC,kBAAkB;IAAAC,QAAA,gBAC5IrF,OAAA,CAACnC,OAAO;MAACmH,EAAE,EAAE;QAAEG,SAAS,EAAE,QAAQ;QAAEG,MAAM,EAAE;MAAO,CAAE;MAAAD,QAAA,gBAEnDrF,OAAA,CAACjC,MAAM;QACL+G,KAAK,EAAC,SAAS;QACfS,OAAO,EAAEA,CAAA,KAAMlB,UAAU,CAAC,YAAY,CAAE;QACxCmB,SAAS,eAAExF,OAAA,CAACtB,QAAQ;UAAA+G,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAE;QACxBZ,EAAE,EAAE;UAAEa,EAAE,EAAE;QAAE,CAAE;QACdT,SAAS,EAAEX,QAAQ,CAAC,YAAY,CAAC,GAAG,eAAe,GAAG,EAAG;QAAAY,QAAA,EAExD5E,eAAe,GAAG,qBAAqB,GACvC,CAAAF,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAEgE,IAAI,MAAK,OAAO,GAAG,gBAAgB,GACzC,CAAAhE,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAEgE,IAAI,MAAK,MAAM,GAAG,gBAAgB,GACxC,CAAAhE,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAEgE,IAAI,MAAK,eAAe,GAAG,eAAe,GAAG;MAAM;QAAAkB,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACpD,CAAC,eACT5F,OAAA,CAAC5B,OAAO;QAAC0H,WAAW,EAAC,UAAU;QAACC,QAAQ;QAACf,EAAE,EAAE;UAAEgB,EAAE,EAAE;QAAI;MAAE;QAAAP,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC,EAK3D,CAAC,CAAArF,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAEgE,IAAI,MAAK,OAAO,IAAK,CAAAhE,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAEgE,IAAI,MAAK,OAAO,IAAI9D,eAAe,IAAIC,gBAAiB,kBACzFV,OAAA,CAAAE,SAAA;QAAAmF,QAAA,GAEG5E,eAAe,iBACdT,OAAA,CAACjC,MAAM;UACL+G,KAAK,EAAC,SAAS;UACfS,OAAO,EAAEA,CAAA,KAAMlB,UAAU,CAAC,qBAAqB,CAAE;UACjDW,EAAE,EAAE;YAAEa,EAAE,EAAE;UAAE,CAAE;UACdT,SAAS,EAAEX,QAAQ,CAAC,qBAAqB,CAAC,GAAG,eAAe,GAAG,EAAG;UAAAY,QAAA,EAEjE5E,eAAe,IAAIC,gBAAgB,GAAG,eAAeA,gBAAgB,CAACuF,QAAQ,EAAE,GAAG;QAAgB;UAAAR,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC9F,CACT,EAKA7C,kBAAkB,iBACjB/C,OAAA,CAAAE,SAAA;UAAAmF,QAAA,GAEG,CAAA9E,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAEgE,IAAI,MAAK,eAAe,iBAC7BvE,OAAA,CAACjC,MAAM;YACL+G,KAAK,EAAC,SAAS;YACfS,OAAO,EAAEA,CAAA,KAAMlB,UAAU,CAAC,4BAA4B,CAAE;YACxDW,EAAE,EAAE;cAAEa,EAAE,EAAE;YAAE,CAAE;YACdT,SAAS,EAAEX,QAAQ,CAAC,4BAA4B,CAAC,GAAG,eAAe,GAAG,EAAG;YAAAY,QAAA,EAC1E;UAED;YAAAI,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CACT,eAGD5F,OAAA,CAACjC,MAAM;YACL+G,KAAK,EAAC,SAAS;YACf,iBAAc,WAAW;YACzB,iBAAc,MAAM;YACpBS,OAAO,EAAGW,CAAC,IAAKjD,cAAc,CAACiD,CAAC,EAAE9D,eAAe,CAAE;YACnD+D,OAAO,eAAEnG,OAAA,CAACV,aAAa;cAAAmG,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAE;YAC3BZ,EAAE,EAAE;cAAEa,EAAE,EAAE;YAAE,CAAE;YACdT,SAAS,EAAET,cAAc,CAAC,sBAAsB,CAAC,GAAG,eAAe,GAAG,EAAG;YAAAU,QAAA,EAC1E;UAED;YAAAI,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC,eAGT5F,OAAA,CAACjC,MAAM;YACL+G,KAAK,EAAC,SAAS;YACf,iBAAc,YAAY;YAC1B,iBAAc,MAAM;YACpBS,OAAO,EAAEA,CAAA,KAAMlB,UAAU,CAAC,kCAAkC,CAAE;YAC9DW,EAAE,EAAE;cAAEa,EAAE,EAAE;YAAE,CAAE;YACdT,SAAS,EAAET,cAAc,CAAC,uBAAuB,CAAC,GAAG,eAAe,GAAG,EAAG;YAAAU,QAAA,EAC3E;UAED;YAAAI,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC,eAGT5F,OAAA,CAACjC,MAAM;YACL+G,KAAK,EAAC,SAAS;YACf,iBAAc,YAAY;YAC1B,iBAAc,MAAM;YACpBS,OAAO,EAAGW,CAAC,IAAKjD,cAAc,CAACiD,CAAC,EAAE1D,gBAAgB,CAAE;YACpD2D,OAAO,eAAEnG,OAAA,CAACV,aAAa;cAAAmG,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAE;YAC3BZ,EAAE,EAAE;cAAEa,EAAE,EAAE;YAAE,CAAE;YACdT,SAAS,EAAET,cAAc,CAAC,uBAAuB,CAAC,GAAG,eAAe,GAAG,EAAG;YAAAU,QAAA,EAC3E;UAED;YAAAI,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC,eAGT5F,OAAA,CAACjC,MAAM;YACL+G,KAAK,EAAC,SAAS;YACf,iBAAc,aAAa;YAC3B,iBAAc,MAAM;YACpBS,OAAO,EAAGW,CAAC,IAAKjD,cAAc,CAACiD,CAAC,EAAExD,iBAAiB,CAAE;YACrDyD,OAAO,eAAEnG,OAAA,CAACV,aAAa;cAAAmG,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAE;YAC3BZ,EAAE,EAAE;cAAEa,EAAE,EAAE;YAAE,CAAE;YACdT,SAAS,EAAET,cAAc,CAAC,wBAAwB,CAAC,GAAG,eAAe,GAAG,EAAG;YAAAU,QAAA,EAC5E;UAED;YAAAI,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC,eAGT5F,OAAA,CAACjC,MAAM;YACL+G,KAAK,EAAC,SAAS;YACf,iBAAc,qBAAqB;YACnC,iBAAc,MAAM;YACpBS,OAAO,EAAGW,CAAC,IAAKjD,cAAc,CAACiD,CAAC,EAAEtD,yBAAyB,CAAE;YAC7DuD,OAAO,eAAEnG,OAAA,CAACV,aAAa;cAAAmG,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAE;YAC3BZ,EAAE,EAAE;cAAEa,EAAE,EAAE;YAAE,CAAE;YACdT,SAAS,EAAET,cAAc,CAAC,gCAAgC,CAAC,GAAG,eAAe,GAAG,EAAG;YAAAU,QAAA,EACpF;UAED;YAAAI,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC,eAGT5F,OAAA,CAACjC,MAAM;YACL+G,KAAK,EAAC,SAAS;YACf,iBAAc,cAAc;YAC5B,iBAAc,MAAM;YACpBS,OAAO,EAAGW,CAAC,IAAKjD,cAAc,CAACiD,CAAC,EAAEpD,kBAAkB,CAAE;YACtDqD,OAAO,eAAEnG,OAAA,CAACV,aAAa;cAAAmG,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAE;YAC3BZ,EAAE,EAAE;cAAEa,EAAE,EAAE;YAAE,CAAE;YACdT,SAAS,EAAET,cAAc,CAAC,yBAAyB,CAAC,GAAG,eAAe,GAAG,EAAG;YAAAU,QAAA,EAC7E;UAED;YAAAI,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC,eAGT5F,OAAA,CAAChC,IAAI;YACHoI,EAAE,EAAC,WAAW;YACdC,QAAQ,EAAElE,YAAa;YACvBmE,WAAW;YACXlF,IAAI,EAAEmF,OAAO,CAACpE,YAAY,CAAE;YAC5BqE,OAAO,EAAEA,CAAA,KAAMnD,eAAe,CAACjB,eAAe,CAAE;YAChDqE,YAAY,EAAE;cACZC,QAAQ,EAAE,QAAQ;cAClBC,UAAU,EAAE;YACd,CAAE;YACFC,eAAe,EAAE;cACfF,QAAQ,EAAE,KAAK;cACfC,UAAU,EAAE;YACd,CAAE;YACFvB,SAAS,EAAC,qBAAqB;YAC/BL,SAAS,EAAE,CAAE;YACbC,EAAE,EAAE;cAAE6B,EAAE,EAAE;YAAI,CAAE;YAAAxB,QAAA,gBAEhBrF,OAAA,CAAC/B,QAAQ;cAACsH,OAAO,EAAEA,CAAA,KAAMlB,UAAU,CAAC,sCAAsC,CAAE;cAAAgB,QAAA,EAAC;YAAsB;cAAAI,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAU,CAAC,eAC9G5F,OAAA,CAAC/B,QAAQ;cAACsH,OAAO,EAAEA,CAAA,KAAM;gBACvB;gBACA3E,yBAAyB,CAAC,IAAI,CAAC;gBAC/B;gBACAyC,eAAe,CAACjB,eAAe,CAAC;gBAChC;gBACAiC,UAAU,CAAC,4BAA4B,CAAC;cAC1C,CAAE;cAAAgB,QAAA,EAAC;YAAa;cAAAI,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAU,CAAC,eAC3B5F,OAAA,CAAC/B,QAAQ;cAACsH,OAAO,EAAEA,CAAA,KAAM;gBACvB;gBACA1E,yBAAyB,CAAC,IAAI,CAAC;gBAC/B;gBACAwC,eAAe,CAACjB,eAAe,CAAC;gBAChC;gBACAiC,UAAU,CAAC,4BAA4B,CAAC;cAC1C,CAAE;cAAAgB,QAAA,EAAC;YAAmB;cAAAI,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAU,CAAC,eACjC5F,OAAA,CAAC/B,QAAQ;cAACsH,OAAO,EAAEA,CAAA,KAAM;gBACvB;gBACA5E,wBAAwB,CAAC,IAAI,CAAC;gBAC9B;gBACA0C,eAAe,CAACjB,eAAe,CAAC;gBAChC;gBACAiC,UAAU,CAAC,4BAA4B,CAAC;cAC1C,CAAE;cAAAgB,QAAA,EAAC;YAAY;cAAAI,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAU,CAAC,eAC1B5F,OAAA,CAAC/B,QAAQ;cAACsH,OAAO,EAAEA,CAAA,KAAMlB,UAAU,CAAC,sCAAsC,CAAE;cAAAgB,QAAA,EAAC;YAA2B;cAAAI,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAU,CAAC,eACnH5F,OAAA,CAAC/B,QAAQ;cAACsH,OAAO,EAAEA,CAAA,KAAMlB,UAAU,CAAC,mCAAmC,CAAE;cAAAgB,QAAA,EAAC;YAA0B;cAAAI,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAU,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC3G,CAAC,eAGP5F,OAAA,CAAChC,IAAI;YACHoI,EAAE,EAAC,YAAY;YACfC,QAAQ,EAAEhE,aAAc;YACxBiE,WAAW;YACXlF,IAAI,EAAEmF,OAAO,CAAClE,aAAa,CAAE;YAC7BmE,OAAO,EAAEA,CAAA,KAAMnD,eAAe,CAACf,gBAAgB,CAAE;YACjDmE,YAAY,EAAE;cACZC,QAAQ,EAAE,QAAQ;cAClBC,UAAU,EAAE;YACd,CAAE;YACFC,eAAe,EAAE;cACfF,QAAQ,EAAE,KAAK;cACfC,UAAU,EAAE;YACd,CAAE;YACFvB,SAAS,EAAC,qBAAqB;YAC/BL,SAAS,EAAE,CAAE;YACbC,EAAE,EAAE;cAAE6B,EAAE,EAAE;YAAI,CAAE;YAAAxB,QAAA,eAEhBrF,OAAA,CAAC/B,QAAQ;cAACsH,OAAO,EAAEA,CAAA,KAAMlB,UAAU,CAAC,+BAA+B,CAAE;cAAAgB,QAAA,EAAC;YAA2B;cAAAI,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAU;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACxG,CAAC,eAGP5F,OAAA,CAAChC,IAAI;YACHoI,EAAE,EAAC,YAAY;YACfC,QAAQ,EAAE9D,aAAc;YACxB+D,WAAW;YACXlF,IAAI,EAAEmF,OAAO,CAAChE,aAAa,CAAE;YAC7BiE,OAAO,EAAEA,CAAA,KAAMnD,eAAe,CAACb,gBAAgB,CAAE;YACjDiE,YAAY,EAAE;cACZC,QAAQ,EAAE,QAAQ;cAClBC,UAAU,EAAE;YACd,CAAE;YACFC,eAAe,EAAE;cACfF,QAAQ,EAAE,KAAK;cACfC,UAAU,EAAE;YACd,CAAE;YACFvB,SAAS,EAAC,qBAAqB;YAC/BL,SAAS,EAAE,CAAE;YACbC,EAAE,EAAE;cAAE6B,EAAE,EAAE;YAAI,CAAE;YAAAxB,QAAA,gBAEhBrF,OAAA,CAAC/B,QAAQ;cAACsH,OAAO,EAAEA,CAAA,KAAMjC,oBAAoB,CAAC,aAAa,CAAE;cAAA+B,QAAA,gBAC3DrF,OAAA,CAACR,cAAc;gBAACsH,QAAQ,EAAC,OAAO;gBAAC9B,EAAE,EAAE;kBAAEa,EAAE,EAAE;gBAAE;cAAE;gBAAAJ,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,gBAEpD;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAU,CAAC,eACX5F,OAAA,CAAC/B,QAAQ;cAACsH,OAAO,EAAEA,CAAA,KAAMjC,oBAAoB,CAAC,oBAAoB,CAAE;cAAA+B,QAAA,gBAClErF,OAAA,CAACR,cAAc;gBAACsH,QAAQ,EAAC,OAAO;gBAAC9B,EAAE,EAAE;kBAAEa,EAAE,EAAE;gBAAE;cAAE;gBAAAJ,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,wBAEpD;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAU,CAAC,eACX5F,OAAA,CAAC5B,OAAO;cAAAqH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,eACX5F,OAAA,CAAC/B,QAAQ;cAACsH,OAAO,EAAEA,CAAA,KAAMjC,oBAAoB,CAAC,kBAAkB,CAAE;cAAA+B,QAAA,gBAChErF,OAAA,CAACN,gBAAgB;gBAACoH,QAAQ,EAAC,OAAO;gBAAC9B,EAAE,EAAE;kBAAEa,EAAE,EAAE;gBAAE;cAAE;gBAAAJ,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,iBAEtD;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAU,CAAC,eACX5F,OAAA,CAAC/B,QAAQ;cAACsH,OAAO,EAAEA,CAAA,KAAMjC,oBAAoB,CAAC,yBAAyB,CAAE;cAAA+B,QAAA,gBACvErF,OAAA,CAACN,gBAAgB;gBAACoH,QAAQ,EAAC,OAAO;gBAAC9B,EAAE,EAAE;kBAAEa,EAAE,EAAE;gBAAE;cAAE;gBAAAJ,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,yBAEtD;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAU,CAAC,eACX5F,OAAA,CAAC5B,OAAO;cAAAqH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,eACX5F,OAAA,CAAC/B,QAAQ;cAACsH,OAAO,EAAEA,CAAA,KAAMjC,oBAAoB,CAAC,aAAa,CAAE;cAAA+B,QAAA,gBAC3DrF,OAAA,CAACN,gBAAgB;gBAACoH,QAAQ,EAAC,OAAO;gBAAC9B,EAAE,EAAE;kBAAEa,EAAE,EAAE;gBAAE;cAAE;gBAAAJ,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,gBAEtD;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAU,CAAC,eACX5F,OAAA,CAAC/B,QAAQ;cAACsH,OAAO,EAAEA,CAAA,KAAMjC,oBAAoB,CAAC,oBAAoB,CAAE;cAAA+B,QAAA,gBAClErF,OAAA,CAACN,gBAAgB;gBAACoH,QAAQ,EAAC,OAAO;gBAAC9B,EAAE,EAAE;kBAAEa,EAAE,EAAE;gBAAE;cAAE;gBAAAJ,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,wBAEtD;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAU,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACP,CAAC,eAGP5F,OAAA,CAAChC,IAAI;YACHoI,EAAE,EAAC,aAAa;YAChBC,QAAQ,EAAE5D,cAAe;YACzB6D,WAAW;YACXlF,IAAI,EAAEmF,OAAO,CAAC9D,cAAc,CAAE;YAC9B+D,OAAO,EAAEA,CAAA,KAAMnD,eAAe,CAACX,iBAAiB,CAAE;YAClD+D,YAAY,EAAE;cACZC,QAAQ,EAAE,QAAQ;cAClBC,UAAU,EAAE;YACd,CAAE;YACFC,eAAe,EAAE;cACfF,QAAQ,EAAE,KAAK;cACfC,UAAU,EAAE;YACd,CAAE;YACFvB,SAAS,EAAC,qBAAqB;YAC/BL,SAAS,EAAE,CAAE;YACbC,EAAE,EAAE;cAAE6B,EAAE,EAAE;YAAI,CAAE;YAAAxB,QAAA,gBAEhBrF,OAAA,CAAC/B,QAAQ;cAACsH,OAAO,EAAEA,CAAA,KAAMlB,UAAU,CAAC,oCAAoC,CAAE;cAAAgB,QAAA,EAAC;YAAkB;cAAAI,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAU,CAAC,eACxG5F,OAAA,CAAC/B,QAAQ;cAACsH,OAAO,EAAEA,CAAA,KAAMlB,UAAU,CAAC,4BAA4B,CAAE;cAAAgB,QAAA,EAAC;YAAkB;cAAAI,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAU,CAAC,eAChG5F,OAAA,CAAC/B,QAAQ;cAACsH,OAAO,EAAEA,CAAA,KAAMlB,UAAU,CAAC,wCAAwC,CAAE;cAAAgB,QAAA,EAAC;YAAsB;cAAAI,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAU,CAAC,eAChH5F,OAAA,CAAC/B,QAAQ;cAACsH,OAAO,EAAEA,CAAA,KAAMlB,UAAU,CAAC,oCAAoC,CAAE;cAAAgB,QAAA,EAAC;YAAoB;cAAAI,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAU,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACtG,CAAC,eAGP5F,OAAA,CAAChC,IAAI;YACHoI,EAAE,EAAC,qBAAqB;YACxBC,QAAQ,EAAE1D,sBAAuB;YACjC2D,WAAW;YACXlF,IAAI,EAAEmF,OAAO,CAAC5D,sBAAsB,CAAE;YACtC6D,OAAO,EAAEA,CAAA,KAAMnD,eAAe,CAACT,yBAAyB,CAAE;YAC1D6D,YAAY,EAAE;cACZC,QAAQ,EAAE,QAAQ;cAClBC,UAAU,EAAE;YACd,CAAE;YACFC,eAAe,EAAE;cACfF,QAAQ,EAAE,KAAK;cACfC,UAAU,EAAE;YACd,CAAE;YACFvB,SAAS,EAAC,qBAAqB;YAC/BL,SAAS,EAAE,CAAE;YACbC,EAAE,EAAE;cAAE6B,EAAE,EAAE;YAAI,CAAE;YAAAxB,QAAA,gBAEhBrF,OAAA,CAAC/B,QAAQ;cAACsH,OAAO,EAAEA,CAAA,KAAMlB,UAAU,CAAC,2CAA2C,CAAE;cAAAgB,QAAA,EAAC;YAAyB;cAAAI,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAU,CAAC,eACtH5F,OAAA,CAAC/B,QAAQ;cAACsH,OAAO,EAAEA,CAAA,KAAMlB,UAAU,CAAC,uCAAuC,CAAE;cAAAgB,QAAA,EAAC;YAAe;cAAAI,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAU,CAAC,eACxG5F,OAAA,CAAC/B,QAAQ;cAACsH,OAAO,EAAEA,CAAA,KAAMlB,UAAU,CAAC,qCAAqC,CAAE;cAAAgB,QAAA,EAAC;YAAmB;cAAAI,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAU,CAAC,eAC1G5F,OAAA,CAAC/B,QAAQ;cAACsH,OAAO,EAAEA,CAAA,KAAMlB,UAAU,CAAC,yCAAyC,CAAE;cAAAgB,QAAA,EAAC;YAAuB;cAAAI,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAU,CAAC,eAClH5F,OAAA,CAAC/B,QAAQ;cAACsH,OAAO,EAAEA,CAAA,KAAMlB,UAAU,CAAC,wCAAwC,CAAE;cAAAgB,QAAA,EAAC;YAAsB;cAAAI,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAU,CAAC,eAChH5F,OAAA,CAAC/B,QAAQ;cAACsH,OAAO,EAAEA,CAAA,KAAMlB,UAAU,CAAC,0CAA0C,CAAE;cAAAgB,QAAA,EAAC;YAAkB;cAAAI,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAU,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC1G,CAAC,eAGP5F,OAAA,CAAChC,IAAI;YACHoI,EAAE,EAAC,cAAc;YACjBC,QAAQ,EAAExD,eAAgB;YAC1ByD,WAAW;YACXlF,IAAI,EAAEmF,OAAO,CAAC1D,eAAe,CAAE;YAC/B2D,OAAO,EAAEA,CAAA,KAAMnD,eAAe,CAACP,kBAAkB,CAAE;YACnD2D,YAAY,EAAE;cACZC,QAAQ,EAAE,QAAQ;cAClBC,UAAU,EAAE;YACd,CAAE;YACFC,eAAe,EAAE;cACfF,QAAQ,EAAE,KAAK;cACfC,UAAU,EAAE;YACd,CAAE;YACFvB,SAAS,EAAC,qBAAqB;YAC/BL,SAAS,EAAE,CAAE;YACbC,EAAE,EAAE;cAAE6B,EAAE,EAAE;YAAI,CAAE;YAAAxB,QAAA,gBAEhBrF,OAAA,CAAC/B,QAAQ;cAACsH,OAAO,EAAEA,CAAA,KAAMlB,UAAU,CAAC,oCAAoC,CAAE;cAAAgB,QAAA,EAAC;YAAkB;cAAAI,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAU,CAAC,eACxG5F,OAAA,CAAC/B,QAAQ;cAACsH,OAAO,EAAEA,CAAA,KAAMlB,UAAU,CAAC,8BAA8B,CAAE;cAAAgB,QAAA,EAAC;YAAkB;cAAAI,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAU,CAAC,eAClG5F,OAAA,CAAC/B,QAAQ;cAACsH,OAAO,EAAEA,CAAA,KAAMlB,UAAU,CAAC,kCAAkC,CAAE;cAAAgB,QAAA,EAAC;YAAgB;cAAAI,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAU,CAAC,eACpG5F,OAAA,CAAC/B,QAAQ;cAACsH,OAAO,EAAEA,CAAA,KAAMlB,UAAU,CAAC,iCAAiC,CAAE;cAAAgB,QAAA,EAAC;YAAe;cAAAI,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAU,CAAC,eAClG5F,OAAA,CAAC/B,QAAQ;cAACsH,OAAO,EAAEA,CAAA,KAAMlB,UAAU,CAAC,gCAAgC,CAAE;cAAAgB,QAAA,EAAC;YAAc;cAAAI,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAU,CAAC,eAChG5F,OAAA,CAAC/B,QAAQ;cAACsH,OAAO,EAAEA,CAAA,KAAMlB,UAAU,CAAC,iCAAiC,CAAE;cAAAgB,QAAA,EAAC;YAAsB;cAAAI,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAU,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACrG,CAAC;QAAA,eACP,CACH;MAAA,eACD,CACH,eAGD5F,OAAA,CAAClC,GAAG;QAACkH,EAAE,EAAE;UAAE+B,QAAQ,EAAE;QAAE;MAAE;QAAAtB,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC,eAG5B5F,OAAA,CAAClC,GAAG;QAACkH,EAAE,EAAE;UAAEgC,OAAO,EAAE,MAAM;UAAEC,UAAU,EAAE,QAAQ;UAAE3B,MAAM,EAAE;QAAO,CAAE;QAAAD,QAAA,gBAEjErF,OAAA,CAACH,uBAAuB;UAAA4F,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,EAE1BnF,eAAe,IAAIC,gBAAgB,iBAClCV,OAAA,CAAC9B,UAAU;UAACgJ,OAAO,EAAC,OAAO;UAACpC,KAAK,EAAC,eAAe;UAACE,EAAE,EAAE;YAAEa,EAAE,EAAE,GAAG;YAAEiB,QAAQ,EAAE;UAAO,CAAE;UAAAzB,QAAA,GAAC,gBACrE,eAAArF,OAAA;YAAAqF,QAAA,EAAI3E,gBAAgB,CAACuF;UAAQ;YAAAR,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACtC,CACb,eACD5F,OAAA,CAAC9B,UAAU;UAACgJ,OAAO,EAAC,OAAO;UAAClC,EAAE,EAAE;YAAEa,EAAE,EAAE,GAAG;YAAEsB,UAAU,EAAE,GAAG;YAAEL,QAAQ,EAAE;UAAO,CAAE;UAAAzB,QAAA,EAC5E,CAAA9E,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAE0F,QAAQ,KAAI;QAAE;UAAAR,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACX,CAAC,eACb5F,OAAA,CAAC1B,OAAO;UAAC8I,KAAK,EAAC,QAAQ;UAAA/B,QAAA,eACrBrF,OAAA,CAAC7B,UAAU;YACT2G,KAAK,EAAC,SAAS;YACfS,OAAO,EAAEf,YAAa;YACtB6C,IAAI,EAAC,KAAK;YACVrC,EAAE,EAAE;cAAE,SAAS,EAAE;gBAAEsC,eAAe,EAAE;cAAU,CAAC;cAAEC,OAAO,EAAE;YAAO,CAAE;YAAAlC,QAAA,eAEnErF,OAAA,CAACZ,UAAU;cAAC0H,QAAQ,EAAC;YAAQ;cAAArB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACtB;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACN,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACP,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACC,CAAC,eAGV5F,OAAA,CAACF,UAAU;MACTsB,IAAI,EAAEN,cAAe;MACrB0F,OAAO,EAAEvC,qBAAsB;MAC/BV,aAAa,EAAEvC,kBAAmB;MAClCO,UAAU,EAAEA,UAAW;MACvBiG,SAAS,EAAEtD,kBAAmB;MAC9BuD,OAAO,EAAEtD;IAAiB;MAAAsB,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAC3B,CAAC,eAGF5F,OAAA,CAACzB,QAAQ;MACP6C,IAAI,EAAEF,QAAQ,CAACE,IAAK;MACpBsG,gBAAgB,EAAE,IAAK;MACvBlB,OAAO,EAAEpC,mBAAoB;MAC7BqC,YAAY,EAAE;QAAEC,QAAQ,EAAE,QAAQ;QAAEC,UAAU,EAAE;MAAS,CAAE;MAAAtB,QAAA,eAE3DrF,OAAA,CAACxB,KAAK;QACJgI,OAAO,EAAEpC,mBAAoB;QAC7B9C,QAAQ,EAAEJ,QAAQ,CAACI,QAAS;QAC5B4F,OAAO,EAAC,QAAQ;QAChBlC,EAAE,EAAE;UAAEE,KAAK,EAAE;QAAO,CAAE;QAAAG,QAAA,EAErBnE,QAAQ,CAACG;MAAO;QAAAoE,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACZ;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACA,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACL,CAAC;AAEb,CAAC;AAACxF,EAAA,CAjiBID,SAAS;EAAA,QACIzC,WAAW,EACXC,WAAW,EACgCgC,OAAO,EACwBC,gBAAgB;AAAA;AAAA+H,EAAA,GAJvGxH,SAAS;AAmiBf,eAAeA,SAAS;AAAC,IAAAwH,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}