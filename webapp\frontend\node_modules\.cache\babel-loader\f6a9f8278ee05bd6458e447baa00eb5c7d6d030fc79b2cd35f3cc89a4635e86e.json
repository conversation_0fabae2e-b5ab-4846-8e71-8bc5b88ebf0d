{"ast": null, "code": "'use client';\n\nexport { default } from './Divider';\nexport { default as dividerClasses } from './dividerClasses';\nexport * from './dividerClasses';", "map": {"version": 3, "names": ["default", "dividerClasses"], "sources": ["C:/CMS/webapp/frontend/node_modules/@mui/material/Divider/index.js"], "sourcesContent": ["'use client';\n\nexport { default } from './Divider';\nexport { default as dividerClasses } from './dividerClasses';\nexport * from './dividerClasses';"], "mappings": "AAAA,YAAY;;AAEZ,SAASA,OAAO,QAAQ,WAAW;AACnC,SAASA,OAAO,IAAIC,cAAc,QAAQ,kBAAkB;AAC5D,cAAc,kBAAkB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}