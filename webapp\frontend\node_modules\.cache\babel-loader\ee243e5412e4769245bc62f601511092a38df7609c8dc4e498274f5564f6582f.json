{"ast": null, "code": "import { buildLocalizeFn } from \"../../_lib/buildLocalizeFn.mjs\";\nconst eraValues = {\n  narrow: [\"ق\", \"ب\"],\n  abbreviated: [\"ق.م.\", \"ب.م.\"],\n  wide: [\"قبل از میلاد\", \"بعد از میلاد\"]\n};\nconst quarterValues = {\n  narrow: [\"1\", \"2\", \"3\", \"4\"],\n  abbreviated: [\"س‌م1\", \"س‌م2\", \"س‌م3\", \"س‌م4\"],\n  wide: [\"سه‌ماهه 1\", \"سه‌ماهه 2\", \"سه‌ماهه 3\", \"سه‌ماهه 4\"]\n};\n\n// Note: in English, the names of days of the week and months are capitalized.\n// If you are making a new locale based on this one, check if the same is true for the language you're working on.\n// Generally, formatted dates should look like they are in the middle of a sentence,\n// e.g. in Spanish language the weekdays and months should be in the lowercase.\nconst monthValues = {\n  narrow: [\"ژ\", \"ف\", \"م\", \"آ\", \"م\", \"ج\", \"ج\", \"آ\", \"س\", \"ا\", \"ن\", \"د\"],\n  abbreviated: [\"ژانـ\", \"فور\", \"مارس\", \"آپر\", \"می\", \"جون\", \"جولـ\", \"آگو\", \"سپتـ\", \"اکتـ\", \"نوامـ\", \"دسامـ\"],\n  wide: [\"ژانویه\", \"فوریه\", \"مارس\", \"آپریل\", \"می\", \"جون\", \"جولای\", \"آگوست\", \"سپتامبر\", \"اکتبر\", \"نوامبر\", \"دسامبر\"]\n};\nconst dayValues = {\n  narrow: [\"ی\", \"د\", \"س\", \"چ\", \"پ\", \"ج\", \"ش\"],\n  short: [\"1ش\", \"2ش\", \"3ش\", \"4ش\", \"5ش\", \"ج\", \"ش\"],\n  abbreviated: [\"یکشنبه\", \"دوشنبه\", \"سه‌شنبه\", \"چهارشنبه\", \"پنجشنبه\", \"جمعه\", \"شنبه\"],\n  wide: [\"یکشنبه\", \"دوشنبه\", \"سه‌شنبه\", \"چهارشنبه\", \"پنجشنبه\", \"جمعه\", \"شنبه\"]\n};\nconst dayPeriodValues = {\n  narrow: {\n    am: \"ق\",\n    pm: \"ب\",\n    midnight: \"ن\",\n    noon: \"ظ\",\n    morning: \"ص\",\n    afternoon: \"ب.ظ.\",\n    evening: \"ع\",\n    night: \"ش\"\n  },\n  abbreviated: {\n    am: \"ق.ظ.\",\n    pm: \"ب.ظ.\",\n    midnight: \"نیمه‌شب\",\n    noon: \"ظهر\",\n    morning: \"صبح\",\n    afternoon: \"بعدازظهر\",\n    evening: \"عصر\",\n    night: \"شب\"\n  },\n  wide: {\n    am: \"قبل‌ازظهر\",\n    pm: \"بعدازظهر\",\n    midnight: \"نیمه‌شب\",\n    noon: \"ظهر\",\n    morning: \"صبح\",\n    afternoon: \"بعدازظهر\",\n    evening: \"عصر\",\n    night: \"شب\"\n  }\n};\nconst formattingDayPeriodValues = {\n  narrow: {\n    am: \"ق\",\n    pm: \"ب\",\n    midnight: \"ن\",\n    noon: \"ظ\",\n    morning: \"ص\",\n    afternoon: \"ب.ظ.\",\n    evening: \"ع\",\n    night: \"ش\"\n  },\n  abbreviated: {\n    am: \"ق.ظ.\",\n    pm: \"ب.ظ.\",\n    midnight: \"نیمه‌شب\",\n    noon: \"ظهر\",\n    morning: \"صبح\",\n    afternoon: \"بعدازظهر\",\n    evening: \"عصر\",\n    night: \"شب\"\n  },\n  wide: {\n    am: \"قبل‌ازظهر\",\n    pm: \"بعدازظهر\",\n    midnight: \"نیمه‌شب\",\n    noon: \"ظهر\",\n    morning: \"صبح\",\n    afternoon: \"بعدازظهر\",\n    evening: \"عصر\",\n    night: \"شب\"\n  }\n};\nconst ordinalNumber = (dirtyNumber, _options) => {\n  return String(dirtyNumber);\n};\nexport const localize = {\n  ordinalNumber,\n  era: buildLocalizeFn({\n    values: eraValues,\n    defaultWidth: \"wide\"\n  }),\n  quarter: buildLocalizeFn({\n    values: quarterValues,\n    defaultWidth: \"wide\",\n    argumentCallback: quarter => quarter - 1\n  }),\n  month: buildLocalizeFn({\n    values: monthValues,\n    defaultWidth: \"wide\"\n  }),\n  day: buildLocalizeFn({\n    values: dayValues,\n    defaultWidth: \"wide\"\n  }),\n  dayPeriod: buildLocalizeFn({\n    values: dayPeriodValues,\n    defaultWidth: \"wide\",\n    formattingValues: formattingDayPeriodValues,\n    defaultFormattingWidth: \"wide\"\n  })\n};", "map": {"version": 3, "names": ["buildLocalizeFn", "<PERSON><PERSON><PERSON><PERSON>", "narrow", "abbreviated", "wide", "quarterValues", "month<PERSON><PERSON><PERSON>", "dayV<PERSON><PERSON>", "short", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "am", "pm", "midnight", "noon", "morning", "afternoon", "evening", "night", "formattingDayPeriodValues", "ordinalNumber", "dirtyNumber", "_options", "String", "localize", "era", "values", "defaultWidth", "quarter", "argument<PERSON>allback", "month", "day", "<PERSON><PERSON><PERSON><PERSON>", "formattingValues", "defaultFormattingWidth"], "sources": ["C:/CMS/webapp/frontend/node_modules/date-fns/locale/fa-IR/_lib/localize.mjs"], "sourcesContent": ["import { buildLocalizeFn } from \"../../_lib/buildLocalizeFn.mjs\";\n\nconst eraValues = {\n  narrow: [\"ق\", \"ب\"],\n  abbreviated: [\"ق.م.\", \"ب.م.\"],\n  wide: [\"قبل از میلاد\", \"بعد از میلاد\"],\n};\n\nconst quarterValues = {\n  narrow: [\"1\", \"2\", \"3\", \"4\"],\n  abbreviated: [\"س‌م1\", \"س‌م2\", \"س‌م3\", \"س‌م4\"],\n  wide: [\"سه‌ماهه 1\", \"سه‌ماهه 2\", \"سه‌ماهه 3\", \"سه‌ماهه 4\"],\n};\n\n// Note: in English, the names of days of the week and months are capitalized.\n// If you are making a new locale based on this one, check if the same is true for the language you're working on.\n// Generally, formatted dates should look like they are in the middle of a sentence,\n// e.g. in Spanish language the weekdays and months should be in the lowercase.\nconst monthValues = {\n  narrow: [\"ژ\", \"ف\", \"م\", \"آ\", \"م\", \"ج\", \"ج\", \"آ\", \"س\", \"ا\", \"ن\", \"د\"],\n  abbreviated: [\n    \"ژانـ\",\n    \"فور\",\n    \"مارس\",\n    \"آپر\",\n    \"می\",\n    \"جون\",\n    \"جولـ\",\n    \"آگو\",\n    \"سپتـ\",\n    \"اکتـ\",\n    \"نوامـ\",\n    \"دسامـ\",\n  ],\n\n  wide: [\n    \"ژانویه\",\n    \"فوریه\",\n    \"مارس\",\n    \"آپریل\",\n    \"می\",\n    \"جون\",\n    \"جولای\",\n    \"آگوست\",\n    \"سپتامبر\",\n    \"اکتبر\",\n    \"نوامبر\",\n    \"دسامبر\",\n  ],\n};\n\nconst dayValues = {\n  narrow: [\"ی\", \"د\", \"س\", \"چ\", \"پ\", \"ج\", \"ش\"],\n  short: [\"1ش\", \"2ش\", \"3ش\", \"4ش\", \"5ش\", \"ج\", \"ش\"],\n  abbreviated: [\n    \"یکشنبه\",\n    \"دوشنبه\",\n    \"سه‌شنبه\",\n    \"چهارشنبه\",\n    \"پنجشنبه\",\n    \"جمعه\",\n    \"شنبه\",\n  ],\n\n  wide: [\"یکشنبه\", \"دوشنبه\", \"سه‌شنبه\", \"چهارشنبه\", \"پنجشنبه\", \"جمعه\", \"شنبه\"],\n};\n\nconst dayPeriodValues = {\n  narrow: {\n    am: \"ق\",\n    pm: \"ب\",\n    midnight: \"ن\",\n    noon: \"ظ\",\n    morning: \"ص\",\n    afternoon: \"ب.ظ.\",\n    evening: \"ع\",\n    night: \"ش\",\n  },\n  abbreviated: {\n    am: \"ق.ظ.\",\n    pm: \"ب.ظ.\",\n    midnight: \"نیمه‌شب\",\n    noon: \"ظهر\",\n    morning: \"صبح\",\n    afternoon: \"بعدازظهر\",\n    evening: \"عصر\",\n    night: \"شب\",\n  },\n  wide: {\n    am: \"قبل‌ازظهر\",\n    pm: \"بعدازظهر\",\n    midnight: \"نیمه‌شب\",\n    noon: \"ظهر\",\n    morning: \"صبح\",\n    afternoon: \"بعدازظهر\",\n    evening: \"عصر\",\n    night: \"شب\",\n  },\n};\n\nconst formattingDayPeriodValues = {\n  narrow: {\n    am: \"ق\",\n    pm: \"ب\",\n    midnight: \"ن\",\n    noon: \"ظ\",\n    morning: \"ص\",\n    afternoon: \"ب.ظ.\",\n    evening: \"ع\",\n    night: \"ش\",\n  },\n  abbreviated: {\n    am: \"ق.ظ.\",\n    pm: \"ب.ظ.\",\n    midnight: \"نیمه‌شب\",\n    noon: \"ظهر\",\n    morning: \"صبح\",\n    afternoon: \"بعدازظهر\",\n    evening: \"عصر\",\n    night: \"شب\",\n  },\n  wide: {\n    am: \"قبل‌ازظهر\",\n    pm: \"بعدازظهر\",\n    midnight: \"نیمه‌شب\",\n    noon: \"ظهر\",\n    morning: \"صبح\",\n    afternoon: \"بعدازظهر\",\n    evening: \"عصر\",\n    night: \"شب\",\n  },\n};\n\nconst ordinalNumber = (dirtyNumber, _options) => {\n  return String(dirtyNumber);\n};\n\nexport const localize = {\n  ordinalNumber,\n\n  era: buildLocalizeFn({\n    values: eraValues,\n    defaultWidth: \"wide\",\n  }),\n\n  quarter: buildLocalizeFn({\n    values: quarterValues,\n    defaultWidth: \"wide\",\n    argumentCallback: (quarter) => quarter - 1,\n  }),\n\n  month: buildLocalizeFn({\n    values: monthValues,\n    defaultWidth: \"wide\",\n  }),\n\n  day: buildLocalizeFn({\n    values: dayValues,\n    defaultWidth: \"wide\",\n  }),\n\n  dayPeriod: buildLocalizeFn({\n    values: dayPeriodValues,\n    defaultWidth: \"wide\",\n    formattingValues: formattingDayPeriodValues,\n    defaultFormattingWidth: \"wide\",\n  }),\n};\n"], "mappings": "AAAA,SAASA,eAAe,QAAQ,gCAAgC;AAEhE,MAAMC,SAAS,GAAG;EAChBC,MAAM,EAAE,CAAC,GAAG,EAAE,GAAG,CAAC;EAClBC,WAAW,EAAE,CAAC,MAAM,EAAE,MAAM,CAAC;EAC7BC,IAAI,EAAE,CAAC,cAAc,EAAE,cAAc;AACvC,CAAC;AAED,MAAMC,aAAa,GAAG;EACpBH,MAAM,EAAE,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,CAAC;EAC5BC,WAAW,EAAE,CAAC,MAAM,EAAE,MAAM,EAAE,MAAM,EAAE,MAAM,CAAC;EAC7CC,IAAI,EAAE,CAAC,WAAW,EAAE,WAAW,EAAE,WAAW,EAAE,WAAW;AAC3D,CAAC;;AAED;AACA;AACA;AACA;AACA,MAAME,WAAW,GAAG;EAClBJ,MAAM,EAAE,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,CAAC;EACpEC,WAAW,EAAE,CACX,MAAM,EACN,KAAK,EACL,MAAM,EACN,KAAK,EACL,IAAI,EACJ,KAAK,EACL,MAAM,EACN,KAAK,EACL,MAAM,EACN,MAAM,EACN,OAAO,EACP,OAAO,CACR;EAEDC,IAAI,EAAE,CACJ,QAAQ,EACR,OAAO,EACP,MAAM,EACN,OAAO,EACP,IAAI,EACJ,KAAK,EACL,OAAO,EACP,OAAO,EACP,SAAS,EACT,OAAO,EACP,QAAQ,EACR,QAAQ;AAEZ,CAAC;AAED,MAAMG,SAAS,GAAG;EAChBL,MAAM,EAAE,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,CAAC;EAC3CM,KAAK,EAAE,CAAC,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,GAAG,EAAE,GAAG,CAAC;EAC/CL,WAAW,EAAE,CACX,QAAQ,EACR,QAAQ,EACR,SAAS,EACT,UAAU,EACV,SAAS,EACT,MAAM,EACN,MAAM,CACP;EAEDC,IAAI,EAAE,CAAC,QAAQ,EAAE,QAAQ,EAAE,SAAS,EAAE,UAAU,EAAE,SAAS,EAAE,MAAM,EAAE,MAAM;AAC7E,CAAC;AAED,MAAMK,eAAe,GAAG;EACtBP,MAAM,EAAE;IACNQ,EAAE,EAAE,GAAG;IACPC,EAAE,EAAE,GAAG;IACPC,QAAQ,EAAE,GAAG;IACbC,IAAI,EAAE,GAAG;IACTC,OAAO,EAAE,GAAG;IACZC,SAAS,EAAE,MAAM;IACjBC,OAAO,EAAE,GAAG;IACZC,KAAK,EAAE;EACT,CAAC;EACDd,WAAW,EAAE;IACXO,EAAE,EAAE,MAAM;IACVC,EAAE,EAAE,MAAM;IACVC,QAAQ,EAAE,SAAS;IACnBC,IAAI,EAAE,KAAK;IACXC,OAAO,EAAE,KAAK;IACdC,SAAS,EAAE,UAAU;IACrBC,OAAO,EAAE,KAAK;IACdC,KAAK,EAAE;EACT,CAAC;EACDb,IAAI,EAAE;IACJM,EAAE,EAAE,WAAW;IACfC,EAAE,EAAE,UAAU;IACdC,QAAQ,EAAE,SAAS;IACnBC,IAAI,EAAE,KAAK;IACXC,OAAO,EAAE,KAAK;IACdC,SAAS,EAAE,UAAU;IACrBC,OAAO,EAAE,KAAK;IACdC,KAAK,EAAE;EACT;AACF,CAAC;AAED,MAAMC,yBAAyB,GAAG;EAChChB,MAAM,EAAE;IACNQ,EAAE,EAAE,GAAG;IACPC,EAAE,EAAE,GAAG;IACPC,QAAQ,EAAE,GAAG;IACbC,IAAI,EAAE,GAAG;IACTC,OAAO,EAAE,GAAG;IACZC,SAAS,EAAE,MAAM;IACjBC,OAAO,EAAE,GAAG;IACZC,KAAK,EAAE;EACT,CAAC;EACDd,WAAW,EAAE;IACXO,EAAE,EAAE,MAAM;IACVC,EAAE,EAAE,MAAM;IACVC,QAAQ,EAAE,SAAS;IACnBC,IAAI,EAAE,KAAK;IACXC,OAAO,EAAE,KAAK;IACdC,SAAS,EAAE,UAAU;IACrBC,OAAO,EAAE,KAAK;IACdC,KAAK,EAAE;EACT,CAAC;EACDb,IAAI,EAAE;IACJM,EAAE,EAAE,WAAW;IACfC,EAAE,EAAE,UAAU;IACdC,QAAQ,EAAE,SAAS;IACnBC,IAAI,EAAE,KAAK;IACXC,OAAO,EAAE,KAAK;IACdC,SAAS,EAAE,UAAU;IACrBC,OAAO,EAAE,KAAK;IACdC,KAAK,EAAE;EACT;AACF,CAAC;AAED,MAAME,aAAa,GAAGA,CAACC,WAAW,EAAEC,QAAQ,KAAK;EAC/C,OAAOC,MAAM,CAACF,WAAW,CAAC;AAC5B,CAAC;AAED,OAAO,MAAMG,QAAQ,GAAG;EACtBJ,aAAa;EAEbK,GAAG,EAAExB,eAAe,CAAC;IACnByB,MAAM,EAAExB,SAAS;IACjByB,YAAY,EAAE;EAChB,CAAC,CAAC;EAEFC,OAAO,EAAE3B,eAAe,CAAC;IACvByB,MAAM,EAAEpB,aAAa;IACrBqB,YAAY,EAAE,MAAM;IACpBE,gBAAgB,EAAGD,OAAO,IAAKA,OAAO,GAAG;EAC3C,CAAC,CAAC;EAEFE,KAAK,EAAE7B,eAAe,CAAC;IACrByB,MAAM,EAAEnB,WAAW;IACnBoB,YAAY,EAAE;EAChB,CAAC,CAAC;EAEFI,GAAG,EAAE9B,eAAe,CAAC;IACnByB,MAAM,EAAElB,SAAS;IACjBmB,YAAY,EAAE;EAChB,CAAC,CAAC;EAEFK,SAAS,EAAE/B,eAAe,CAAC;IACzByB,MAAM,EAAEhB,eAAe;IACvBiB,YAAY,EAAE,MAAM;IACpBM,gBAAgB,EAAEd,yBAAyB;IAC3Ce,sBAAsB,EAAE;EAC1B,CAAC;AACH,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}