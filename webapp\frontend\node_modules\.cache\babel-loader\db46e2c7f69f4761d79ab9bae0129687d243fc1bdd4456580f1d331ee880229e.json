{"ast": null, "code": "import { addYears } from \"./addYears.mjs\";\n\n/**\n * @name subYears\n * @category Year Helpers\n * @summary Subtract the specified number of years from the given date.\n *\n * @description\n * Subtract the specified number of years from the given date.\n *\n * @typeParam DateType - The `Date` type, the function operates on. Gets inferred from passed arguments. Allows to use extensions like [`UTCDate`](https://github.com/date-fns/utc).\n *\n * @param date - The date to be changed\n * @param amount - The amount of years to be subtracted.\n *\n * @returns The new date with the years subtracted\n *\n * @example\n * // Subtract 5 years from 1 September 2014:\n * const result = subYears(new Date(2014, 8, 1), 5)\n * //=> Tue Sep 01 2009 00:00:00\n */\nexport function subYears(date, amount) {\n  return addYears(date, -amount);\n}\n\n// Fallback for modularized imports:\nexport default subYears;", "map": {"version": 3, "names": ["addYears", "subYears", "date", "amount"], "sources": ["C:/CMS/webapp/frontend/node_modules/date-fns/subYears.mjs"], "sourcesContent": ["import { addYears } from \"./addYears.mjs\";\n\n/**\n * @name subYears\n * @category Year Helpers\n * @summary Subtract the specified number of years from the given date.\n *\n * @description\n * Subtract the specified number of years from the given date.\n *\n * @typeParam DateType - The `Date` type, the function operates on. Gets inferred from passed arguments. Allows to use extensions like [`UTCDate`](https://github.com/date-fns/utc).\n *\n * @param date - The date to be changed\n * @param amount - The amount of years to be subtracted.\n *\n * @returns The new date with the years subtracted\n *\n * @example\n * // Subtract 5 years from 1 September 2014:\n * const result = subYears(new Date(2014, 8, 1), 5)\n * //=> Tue Sep 01 2009 00:00:00\n */\nexport function subYears(date, amount) {\n  return addYears(date, -amount);\n}\n\n// Fallback for modularized imports:\nexport default subYears;\n"], "mappings": "AAAA,SAASA,QAAQ,QAAQ,gBAAgB;;AAEzC;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO,SAASC,QAAQA,CAACC,IAAI,EAAEC,MAAM,EAAE;EACrC,OAAOH,QAAQ,CAACE,IAAI,EAAE,CAACC,MAAM,CAAC;AAChC;;AAEA;AACA,eAAeF,QAAQ", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}