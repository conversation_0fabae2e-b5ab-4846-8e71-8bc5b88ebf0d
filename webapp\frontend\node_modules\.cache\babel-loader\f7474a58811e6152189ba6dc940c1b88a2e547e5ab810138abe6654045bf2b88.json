{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\PycharmProjects\\\\CMS\\\\webapp\\\\frontend\\\\src\\\\components\\\\admin\\\\ImpersonateUser.js\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect } from 'react';\nimport { useNavigate } from 'react-router-dom';\nimport { Box, Typography, Button, FormControl, InputLabel, Select, MenuItem, Alert, CircularProgress } from '@mui/material';\nimport { Refresh as RefreshIcon, Login as LoginIcon } from '@mui/icons-material';\nimport userService from '../../services/userService';\nimport authService from '../../services/authService';\nimport { useAuth } from '../../context/AuthContext';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst ImpersonateUser = () => {\n  _s();\n  const navigate = useNavigate();\n  const [users, setUsers] = useState([]);\n  const [selectedUserId, setSelectedUserId] = useState('');\n  const [loading, setLoading] = useState(true);\n  const [error, setError] = useState('');\n  const {\n    setUser,\n    setToken\n  } = useAuth();\n\n  // Carica gli utenti\n  const loadUsers = async () => {\n    setLoading(true);\n    try {\n      const data = await userService.getUsers();\n      // Filtra solo gli utenti attivi e non admin\n      const activeUsers = data.filter(user => user.abilitato && user.ruolo !== 'owner');\n      setUsers(activeUsers);\n      setError('');\n    } catch (err) {\n      setError(err.detail || 'Errore durante il caricamento degli utenti');\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  // Carica gli utenti all'avvio del componente\n  useEffect(() => {\n    loadUsers();\n  }, []);\n\n  // Gestisce il cambio dell'utente selezionato\n  const handleUserChange = event => {\n    setSelectedUserId(event.target.value);\n  };\n\n  // Gestisce l'accesso come utente selezionato\n  const handleImpersonate = async () => {\n    if (!selectedUserId) {\n      setError('Seleziona un utente');\n      return;\n    }\n    setLoading(true);\n    try {\n      const response = await authService.impersonateUser(selectedUserId);\n\n      // Salva il nuovo token e aggiorna l'utente corrente\n      localStorage.setItem('token', response.access_token);\n\n      // Aggiorna il contesto di autenticazione\n      setToken(response.access_token);\n      setUser({\n        id: response.user_id,\n        username: response.username,\n        role: response.role\n      });\n      console.log('Impersonificazione riuscita:', {\n        token: response.access_token,\n        user: {\n          id: response.user_id,\n          username: response.username,\n          role: response.role\n        }\n      });\n\n      // Reindirizza alla dashboard usando il router di React dopo un breve ritardo\n      // per assicurarsi che il contesto di autenticazione sia aggiornato\n      setTimeout(() => {\n        navigate('/dashboard');\n      }, 500);\n    } catch (err) {\n      setError(err.detail || 'Errore durante l\\'accesso come utente selezionato');\n    } finally {\n      setLoading(false);\n    }\n  };\n  return /*#__PURE__*/_jsxDEV(Box, {\n    children: [/*#__PURE__*/_jsxDEV(Box, {\n      sx: {\n        display: 'flex',\n        justifyContent: 'space-between',\n        alignItems: 'center',\n        mb: 2\n      },\n      children: [/*#__PURE__*/_jsxDEV(Typography, {\n        variant: \"h6\",\n        children: \"Accedi come Utente\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 102,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Button, {\n        variant: \"outlined\",\n        startIcon: /*#__PURE__*/_jsxDEV(RefreshIcon, {}, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 105,\n          columnNumber: 22\n        }, this),\n        onClick: loadUsers,\n        disabled: loading,\n        children: \"Aggiorna\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 103,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 101,\n      columnNumber: 7\n    }, this), error && /*#__PURE__*/_jsxDEV(Alert, {\n      severity: \"error\",\n      sx: {\n        mb: 2\n      },\n      children: error\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 114,\n      columnNumber: 9\n    }, this), loading ? /*#__PURE__*/_jsxDEV(CircularProgress, {}, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 120,\n      columnNumber: 9\n    }, this) : users.length === 0 ? /*#__PURE__*/_jsxDEV(Typography, {\n      children: \"Nessun utente disponibile\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 122,\n      columnNumber: 9\n    }, this) : /*#__PURE__*/_jsxDEV(Box, {\n      sx: {\n        mt: 2\n      },\n      children: [/*#__PURE__*/_jsxDEV(FormControl, {\n        fullWidth: true,\n        sx: {\n          mb: 2\n        },\n        children: [/*#__PURE__*/_jsxDEV(InputLabel, {\n          id: \"user-select-label\",\n          children: \"Seleziona Utente\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 126,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(Select, {\n          labelId: \"user-select-label\",\n          id: \"user-select\",\n          value: selectedUserId,\n          label: \"Seleziona Utente\",\n          onChange: handleUserChange,\n          children: users.map(user => /*#__PURE__*/_jsxDEV(MenuItem, {\n            value: user.id_utente,\n            children: [user.username, \" (\", user.ruolo, \")\"]\n          }, user.id_utente, true, {\n            fileName: _jsxFileName,\n            lineNumber: 135,\n            columnNumber: 17\n          }, this))\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 127,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 125,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(Button, {\n        variant: \"contained\",\n        color: \"primary\",\n        startIcon: /*#__PURE__*/_jsxDEV(LoginIcon, {}, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 145,\n          columnNumber: 24\n        }, this),\n        onClick: handleImpersonate,\n        disabled: !selectedUserId || loading,\n        fullWidth: true,\n        children: \"Accedi come Utente Selezionato\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 142,\n        columnNumber: 11\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 124,\n      columnNumber: 9\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 100,\n    columnNumber: 5\n  }, this);\n};\n_s(ImpersonateUser, \"rThWzyd7zahj/By8lvd6HuP1xKU=\", false, function () {\n  return [useNavigate, useAuth];\n});\n_c = ImpersonateUser;\nexport default ImpersonateUser;\nvar _c;\n$RefreshReg$(_c, \"ImpersonateUser\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "useNavigate", "Box", "Typography", "<PERSON><PERSON>", "FormControl", "InputLabel", "Select", "MenuItem", "<PERSON><PERSON>", "CircularProgress", "Refresh", "RefreshIcon", "<PERSON><PERSON>", "LoginIcon", "userService", "authService", "useAuth", "jsxDEV", "_jsxDEV", "ImpersonateUser", "_s", "navigate", "users", "setUsers", "selectedUserId", "setSelectedUserId", "loading", "setLoading", "error", "setError", "setUser", "setToken", "loadUsers", "data", "getUsers", "activeUsers", "filter", "user", "abilitato", "ruolo", "err", "detail", "handleUserChange", "event", "target", "value", "handleImpersonate", "response", "impersonate<PERSON><PERSON>", "localStorage", "setItem", "access_token", "id", "user_id", "username", "role", "console", "log", "token", "setTimeout", "children", "sx", "display", "justifyContent", "alignItems", "mb", "variant", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "startIcon", "onClick", "disabled", "severity", "length", "mt", "fullWidth", "labelId", "label", "onChange", "map", "id_utente", "color", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/PycharmProjects/CMS/webapp/frontend/src/components/admin/ImpersonateUser.js"], "sourcesContent": ["import React, { useState, useEffect } from 'react';\nimport { useNavigate } from 'react-router-dom';\nimport {\n  Box,\n  Typography,\n  Button,\n  FormControl,\n  InputLabel,\n  Select,\n  MenuItem,\n  Alert,\n  CircularProgress\n} from '@mui/material';\nimport {\n  Refresh as RefreshIcon,\n  Login as LoginIcon\n} from '@mui/icons-material';\nimport userService from '../../services/userService';\nimport authService from '../../services/authService';\nimport { useAuth } from '../../context/AuthContext';\n\nconst ImpersonateUser = () => {\n  const navigate = useNavigate();\n  const [users, setUsers] = useState([]);\n  const [selectedUserId, setSelectedUserId] = useState('');\n  const [loading, setLoading] = useState(true);\n  const [error, setError] = useState('');\n  const { setUser, setToken } = useAuth();\n\n  // Carica gli utenti\n  const loadUsers = async () => {\n    setLoading(true);\n    try {\n      const data = await userService.getUsers();\n      // Filtra solo gli utenti attivi e non admin\n      const activeUsers = data.filter(user => user.abilitato && user.ruolo !== 'owner');\n      setUsers(activeUsers);\n      setError('');\n    } catch (err) {\n      setError(err.detail || 'Errore durante il caricamento degli utenti');\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  // Carica gli utenti all'avvio del componente\n  useEffect(() => {\n    loadUsers();\n  }, []);\n\n  // Gestisce il cambio dell'utente selezionato\n  const handleUserChange = (event) => {\n    setSelectedUserId(event.target.value);\n  };\n\n  // Gestisce l'accesso come utente selezionato\n  const handleImpersonate = async () => {\n    if (!selectedUserId) {\n      setError('Seleziona un utente');\n      return;\n    }\n\n    setLoading(true);\n    try {\n      const response = await authService.impersonateUser(selectedUserId);\n\n      // Salva il nuovo token e aggiorna l'utente corrente\n      localStorage.setItem('token', response.access_token);\n\n      // Aggiorna il contesto di autenticazione\n      setToken(response.access_token);\n      setUser({\n        id: response.user_id,\n        username: response.username,\n        role: response.role\n      });\n\n      console.log('Impersonificazione riuscita:', {\n        token: response.access_token,\n        user: {\n          id: response.user_id,\n          username: response.username,\n          role: response.role\n        }\n      });\n\n      // Reindirizza alla dashboard usando il router di React dopo un breve ritardo\n      // per assicurarsi che il contesto di autenticazione sia aggiornato\n      setTimeout(() => {\n        navigate('/dashboard');\n      }, 500);\n    } catch (err) {\n      setError(err.detail || 'Errore durante l\\'accesso come utente selezionato');\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  return (\n    <Box>\n      <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 2 }}>\n        <Typography variant=\"h6\">Accedi come Utente</Typography>\n        <Button\n          variant=\"outlined\"\n          startIcon={<RefreshIcon />}\n          onClick={loadUsers}\n          disabled={loading}\n        >\n          Aggiorna\n        </Button>\n      </Box>\n\n      {error && (\n        <Alert severity=\"error\" sx={{ mb: 2 }}>\n          {error}\n        </Alert>\n      )}\n\n      {loading ? (\n        <CircularProgress />\n      ) : users.length === 0 ? (\n        <Typography>Nessun utente disponibile</Typography>\n      ) : (\n        <Box sx={{ mt: 2 }}>\n          <FormControl fullWidth sx={{ mb: 2 }}>\n            <InputLabel id=\"user-select-label\">Seleziona Utente</InputLabel>\n            <Select\n              labelId=\"user-select-label\"\n              id=\"user-select\"\n              value={selectedUserId}\n              label=\"Seleziona Utente\"\n              onChange={handleUserChange}\n            >\n              {users.map((user) => (\n                <MenuItem key={user.id_utente} value={user.id_utente}>\n                  {user.username} ({user.ruolo})\n                </MenuItem>\n              ))}\n            </Select>\n          </FormControl>\n\n          <Button\n            variant=\"contained\"\n            color=\"primary\"\n            startIcon={<LoginIcon />}\n            onClick={handleImpersonate}\n            disabled={!selectedUserId || loading}\n            fullWidth\n          >\n            Accedi come Utente Selezionato\n          </Button>\n        </Box>\n      )}\n    </Box>\n  );\n};\n\nexport default ImpersonateUser;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAClD,SAASC,WAAW,QAAQ,kBAAkB;AAC9C,SACEC,GAAG,EACHC,UAAU,EACVC,MAAM,EACNC,WAAW,EACXC,UAAU,EACVC,MAAM,EACNC,QAAQ,EACRC,KAAK,EACLC,gBAAgB,QACX,eAAe;AACtB,SACEC,OAAO,IAAIC,WAAW,EACtBC,KAAK,IAAIC,SAAS,QACb,qBAAqB;AAC5B,OAAOC,WAAW,MAAM,4BAA4B;AACpD,OAAOC,WAAW,MAAM,4BAA4B;AACpD,SAASC,OAAO,QAAQ,2BAA2B;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAEpD,MAAMC,eAAe,GAAGA,CAAA,KAAM;EAAAC,EAAA;EAC5B,MAAMC,QAAQ,GAAGrB,WAAW,CAAC,CAAC;EAC9B,MAAM,CAACsB,KAAK,EAAEC,QAAQ,CAAC,GAAGzB,QAAQ,CAAC,EAAE,CAAC;EACtC,MAAM,CAAC0B,cAAc,EAAEC,iBAAiB,CAAC,GAAG3B,QAAQ,CAAC,EAAE,CAAC;EACxD,MAAM,CAAC4B,OAAO,EAAEC,UAAU,CAAC,GAAG7B,QAAQ,CAAC,IAAI,CAAC;EAC5C,MAAM,CAAC8B,KAAK,EAAEC,QAAQ,CAAC,GAAG/B,QAAQ,CAAC,EAAE,CAAC;EACtC,MAAM;IAAEgC,OAAO;IAAEC;EAAS,CAAC,GAAGf,OAAO,CAAC,CAAC;;EAEvC;EACA,MAAMgB,SAAS,GAAG,MAAAA,CAAA,KAAY;IAC5BL,UAAU,CAAC,IAAI,CAAC;IAChB,IAAI;MACF,MAAMM,IAAI,GAAG,MAAMnB,WAAW,CAACoB,QAAQ,CAAC,CAAC;MACzC;MACA,MAAMC,WAAW,GAAGF,IAAI,CAACG,MAAM,CAACC,IAAI,IAAIA,IAAI,CAACC,SAAS,IAAID,IAAI,CAACE,KAAK,KAAK,OAAO,CAAC;MACjFhB,QAAQ,CAACY,WAAW,CAAC;MACrBN,QAAQ,CAAC,EAAE,CAAC;IACd,CAAC,CAAC,OAAOW,GAAG,EAAE;MACZX,QAAQ,CAACW,GAAG,CAACC,MAAM,IAAI,4CAA4C,CAAC;IACtE,CAAC,SAAS;MACRd,UAAU,CAAC,KAAK,CAAC;IACnB;EACF,CAAC;;EAED;EACA5B,SAAS,CAAC,MAAM;IACdiC,SAAS,CAAC,CAAC;EACb,CAAC,EAAE,EAAE,CAAC;;EAEN;EACA,MAAMU,gBAAgB,GAAIC,KAAK,IAAK;IAClClB,iBAAiB,CAACkB,KAAK,CAACC,MAAM,CAACC,KAAK,CAAC;EACvC,CAAC;;EAED;EACA,MAAMC,iBAAiB,GAAG,MAAAA,CAAA,KAAY;IACpC,IAAI,CAACtB,cAAc,EAAE;MACnBK,QAAQ,CAAC,qBAAqB,CAAC;MAC/B;IACF;IAEAF,UAAU,CAAC,IAAI,CAAC;IAChB,IAAI;MACF,MAAMoB,QAAQ,GAAG,MAAMhC,WAAW,CAACiC,eAAe,CAACxB,cAAc,CAAC;;MAElE;MACAyB,YAAY,CAACC,OAAO,CAAC,OAAO,EAAEH,QAAQ,CAACI,YAAY,CAAC;;MAEpD;MACApB,QAAQ,CAACgB,QAAQ,CAACI,YAAY,CAAC;MAC/BrB,OAAO,CAAC;QACNsB,EAAE,EAAEL,QAAQ,CAACM,OAAO;QACpBC,QAAQ,EAAEP,QAAQ,CAACO,QAAQ;QAC3BC,IAAI,EAAER,QAAQ,CAACQ;MACjB,CAAC,CAAC;MAEFC,OAAO,CAACC,GAAG,CAAC,8BAA8B,EAAE;QAC1CC,KAAK,EAAEX,QAAQ,CAACI,YAAY;QAC5Bd,IAAI,EAAE;UACJe,EAAE,EAAEL,QAAQ,CAACM,OAAO;UACpBC,QAAQ,EAAEP,QAAQ,CAACO,QAAQ;UAC3BC,IAAI,EAAER,QAAQ,CAACQ;QACjB;MACF,CAAC,CAAC;;MAEF;MACA;MACAI,UAAU,CAAC,MAAM;QACftC,QAAQ,CAAC,YAAY,CAAC;MACxB,CAAC,EAAE,GAAG,CAAC;IACT,CAAC,CAAC,OAAOmB,GAAG,EAAE;MACZX,QAAQ,CAACW,GAAG,CAACC,MAAM,IAAI,mDAAmD,CAAC;IAC7E,CAAC,SAAS;MACRd,UAAU,CAAC,KAAK,CAAC;IACnB;EACF,CAAC;EAED,oBACET,OAAA,CAACjB,GAAG;IAAA2D,QAAA,gBACF1C,OAAA,CAACjB,GAAG;MAAC4D,EAAE,EAAE;QAAEC,OAAO,EAAE,MAAM;QAAEC,cAAc,EAAE,eAAe;QAAEC,UAAU,EAAE,QAAQ;QAAEC,EAAE,EAAE;MAAE,CAAE;MAAAL,QAAA,gBACzF1C,OAAA,CAAChB,UAAU;QAACgE,OAAO,EAAC,IAAI;QAAAN,QAAA,EAAC;MAAkB;QAAAO,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAY,CAAC,eACxDpD,OAAA,CAACf,MAAM;QACL+D,OAAO,EAAC,UAAU;QAClBK,SAAS,eAAErD,OAAA,CAACP,WAAW;UAAAwD,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAE;QAC3BE,OAAO,EAAExC,SAAU;QACnByC,QAAQ,EAAE/C,OAAQ;QAAAkC,QAAA,EACnB;MAED;QAAAO,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAQ,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACN,CAAC,EAEL1C,KAAK,iBACJV,OAAA,CAACV,KAAK;MAACkE,QAAQ,EAAC,OAAO;MAACb,EAAE,EAAE;QAAEI,EAAE,EAAE;MAAE,CAAE;MAAAL,QAAA,EACnChC;IAAK;MAAAuC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACD,CACR,EAEA5C,OAAO,gBACNR,OAAA,CAACT,gBAAgB;MAAA0D,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC,GAClBhD,KAAK,CAACqD,MAAM,KAAK,CAAC,gBACpBzD,OAAA,CAAChB,UAAU;MAAA0D,QAAA,EAAC;IAAyB;MAAAO,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAY,CAAC,gBAElDpD,OAAA,CAACjB,GAAG;MAAC4D,EAAE,EAAE;QAAEe,EAAE,EAAE;MAAE,CAAE;MAAAhB,QAAA,gBACjB1C,OAAA,CAACd,WAAW;QAACyE,SAAS;QAAChB,EAAE,EAAE;UAAEI,EAAE,EAAE;QAAE,CAAE;QAAAL,QAAA,gBACnC1C,OAAA,CAACb,UAAU;UAAC+C,EAAE,EAAC,mBAAmB;UAAAQ,QAAA,EAAC;QAAgB;UAAAO,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAY,CAAC,eAChEpD,OAAA,CAACZ,MAAM;UACLwE,OAAO,EAAC,mBAAmB;UAC3B1B,EAAE,EAAC,aAAa;UAChBP,KAAK,EAAErB,cAAe;UACtBuD,KAAK,EAAC,kBAAkB;UACxBC,QAAQ,EAAEtC,gBAAiB;UAAAkB,QAAA,EAE1BtC,KAAK,CAAC2D,GAAG,CAAE5C,IAAI,iBACdnB,OAAA,CAACX,QAAQ;YAAsBsC,KAAK,EAAER,IAAI,CAAC6C,SAAU;YAAAtB,QAAA,GAClDvB,IAAI,CAACiB,QAAQ,EAAC,IAAE,EAACjB,IAAI,CAACE,KAAK,EAAC,GAC/B;UAAA,GAFeF,IAAI,CAAC6C,SAAS;YAAAf,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAEnB,CACX;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACI,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACE,CAAC,eAEdpD,OAAA,CAACf,MAAM;QACL+D,OAAO,EAAC,WAAW;QACnBiB,KAAK,EAAC,SAAS;QACfZ,SAAS,eAAErD,OAAA,CAACL,SAAS;UAAAsD,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAE;QACzBE,OAAO,EAAE1B,iBAAkB;QAC3B2B,QAAQ,EAAE,CAACjD,cAAc,IAAIE,OAAQ;QACrCmD,SAAS;QAAAjB,QAAA,EACV;MAED;QAAAO,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAQ,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACN,CACN;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACE,CAAC;AAEV,CAAC;AAAClD,EAAA,CAtIID,eAAe;EAAA,QACFnB,WAAW,EAKEgB,OAAO;AAAA;AAAAoE,EAAA,GANjCjE,eAAe;AAwIrB,eAAeA,eAAe;AAAC,IAAAiE,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}