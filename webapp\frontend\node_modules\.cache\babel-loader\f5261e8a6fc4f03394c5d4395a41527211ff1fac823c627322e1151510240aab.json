{"ast": null, "code": "export function getRoundingMethod(method) {\n  return number => {\n    const round = method ? Math[method] : Math.trunc;\n    const result = round(number);\n    // Prevent negative zero\n    return result === 0 ? 0 : result;\n  };\n}", "map": {"version": 3, "names": ["getRoundingMethod", "method", "number", "round", "Math", "trunc", "result"], "sources": ["C:/CMS/webapp/frontend/node_modules/date-fns/_lib/getRoundingMethod.mjs"], "sourcesContent": ["export function getRoundingMethod(method) {\n  return (number) => {\n    const round = method ? Math[method] : Math.trunc;\n    const result = round(number);\n    // Prevent negative zero\n    return result === 0 ? 0 : result;\n  };\n}\n"], "mappings": "AAAA,OAAO,SAASA,iBAAiBA,CAACC,MAAM,EAAE;EACxC,OAAQC,MAAM,IAAK;IACjB,MAAMC,KAAK,GAAGF,MAAM,GAAGG,IAAI,CAACH,MAAM,CAAC,GAAGG,IAAI,CAACC,KAAK;IAChD,MAAMC,MAAM,GAAGH,KAAK,CAACD,MAAM,CAAC;IAC5B;IACA,OAAOI,MAAM,KAAK,CAAC,GAAG,CAAC,GAAGA,MAAM;EAClC,CAAC;AACH", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}