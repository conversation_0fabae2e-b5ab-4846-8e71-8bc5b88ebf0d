{"ast": null, "code": "var _jsxFileName = \"C:\\\\CMS\\\\webapp\\\\frontend\\\\src\\\\pages\\\\cavi\\\\CertificazioneCaviPage.js\",\n  _s = $RefreshSig$();\nimport React, { useEffect, useRef } from 'react';\nimport { Box, IconButton, Alert } from '@mui/material';\nimport { ArrowBack as ArrowBackIcon, Refresh as RefreshIcon } from '@mui/icons-material';\nimport { useNavigate, useLocation } from 'react-router-dom';\nimport { useAuth } from '../../context/AuthContext';\nimport AdminHomeButton from '../../components/common/AdminHomeButton';\nimport CertificazioneCaviImproved from '../../components/cavi/CertificazioneCaviImproved';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst CertificazioneCaviPage = () => {\n  _s();\n  const {\n    isImpersonating\n  } = useAuth();\n  const navigate = useNavigate();\n  const location = useLocation();\n  const certificazioneRef = useRef();\n\n  // Recupera l'ID del cantiere dal localStorage\n  const cantiereId = parseInt(localStorage.getItem('selectedCantiereId'), 10);\n  const cantiereName = localStorage.getItem('selectedCantiereName') || `Cantiere ${cantiereId}`;\n\n  // Con la nuova interfaccia unificata, non abbiamo più bisogno di gestire route specifiche\n  useEffect(() => {\n    console.log('CertificazioneCaviPage caricata per cantiere:', cantiereId);\n  }, [location.pathname, cantiereId]);\n\n  // Torna alla lista dei cantieri\n  const handleBackToCantieri = () => {\n    navigate('/dashboard/cantieri');\n  };\n\n  // Gestisce le notifiche\n  const handleSuccess = message => {\n    // Qui puoi implementare una notifica di successo se necessario\n    console.log('Successo:', message);\n  };\n  const handleError = message => {\n    // Qui puoi implementare una notifica di errore se necessario\n    console.error('Errore:', message);\n  };\n  if (!cantiereId || isNaN(cantiereId)) {\n    return /*#__PURE__*/_jsxDEV(Box, {\n      children: [/*#__PURE__*/_jsxDEV(Alert, {\n        severity: \"error\",\n        sx: {\n          mb: 2\n        },\n        children: \"Nessun cantiere selezionato o ID cantiere non valido. Torna alla pagina dei cantieri.\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 52,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Button, {\n        variant: \"contained\",\n        startIcon: /*#__PURE__*/_jsxDEV(ArrowBackIcon, {}, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 57,\n          columnNumber: 22\n        }, this),\n        onClick: handleBackToCantieri,\n        children: \"Torna ai Cantieri\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 55,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 51,\n      columnNumber: 7\n    }, this);\n  }\n  return /*#__PURE__*/_jsxDEV(Box, {\n    children: [/*#__PURE__*/_jsxDEV(Box, {\n      sx: {\n        mb: 3,\n        display: 'flex',\n        alignItems: 'center',\n        justifyContent: 'space-between'\n      },\n      children: [/*#__PURE__*/_jsxDEV(Box, {\n        sx: {\n          display: 'flex',\n          alignItems: 'center'\n        },\n        children: [/*#__PURE__*/_jsxDEV(IconButton, {\n          onClick: handleBackToCantieri,\n          sx: {\n            mr: 1\n          },\n          children: /*#__PURE__*/_jsxDEV(ArrowBackIcon, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 71,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 70,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(IconButton, {\n          onClick: () => window.location.reload(),\n          sx: {\n            ml: 2\n          },\n          color: \"primary\",\n          title: \"Ricarica la pagina\",\n          children: /*#__PURE__*/_jsxDEV(RefreshIcon, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 79,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 73,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 69,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(AdminHomeButton, {}, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 82,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 68,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(CertificazioneCaviImproved, {\n      ref: certificazioneRef,\n      cantiereId: cantiereId,\n      onSuccess: handleSuccess,\n      onError: handleError\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 87,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 67,\n    columnNumber: 5\n  }, this);\n};\n_s(CertificazioneCaviPage, \"qChaAFd9ewnxuqu6/g1V8modph0=\", false, function () {\n  return [useAuth, useNavigate, useLocation];\n});\n_c = CertificazioneCaviPage;\nexport default CertificazioneCaviPage;\nvar _c;\n$RefreshReg$(_c, \"CertificazioneCaviPage\");", "map": {"version": 3, "names": ["React", "useEffect", "useRef", "Box", "IconButton", "<PERSON><PERSON>", "ArrowBack", "ArrowBackIcon", "Refresh", "RefreshIcon", "useNavigate", "useLocation", "useAuth", "AdminHomeButton", "CertificazioneCaviImproved", "jsxDEV", "_jsxDEV", "CertificazioneCaviPage", "_s", "isImpersonating", "navigate", "location", "certificazioneRef", "cantiereId", "parseInt", "localStorage", "getItem", "cantiereName", "console", "log", "pathname", "handleBackToCantieri", "handleSuccess", "message", "handleError", "error", "isNaN", "children", "severity", "sx", "mb", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "<PERSON><PERSON>", "variant", "startIcon", "onClick", "display", "alignItems", "justifyContent", "mr", "window", "reload", "ml", "color", "title", "ref", "onSuccess", "onError", "_c", "$RefreshReg$"], "sources": ["C:/CMS/webapp/frontend/src/pages/cavi/CertificazioneCaviPage.js"], "sourcesContent": ["import React, { useEffect, useRef } from 'react';\nimport {\n  Box,\n  IconButton,\n  Alert\n} from '@mui/material';\nimport {\n  ArrowBack as ArrowBackIcon,\n  Refresh as RefreshIcon\n} from '@mui/icons-material';\nimport { useNavigate, useLocation } from 'react-router-dom';\nimport { useAuth } from '../../context/AuthContext';\nimport AdminHomeButton from '../../components/common/AdminHomeButton';\nimport CertificazioneCaviImproved from '../../components/cavi/CertificazioneCaviImproved';\n\nconst CertificazioneCaviPage = () => {\n  const { isImpersonating } = useAuth();\n  const navigate = useNavigate();\n  const location = useLocation();\n  const certificazioneRef = useRef();\n\n  // Recupera l'ID del cantiere dal localStorage\n  const cantiereId = parseInt(localStorage.getItem('selectedCantiereId'), 10);\n  const cantiereName = localStorage.getItem('selectedCantiereName') || `Cantiere ${cantiereId}`;\n\n  // Con la nuova interfaccia unificata, non abbiamo più bisogno di gestire route specifiche\n  useEffect(() => {\n    console.log('CertificazioneCaviPage caricata per cantiere:', cantiereId);\n  }, [location.pathname, cantiereId]);\n\n  // Torna alla lista dei cantieri\n  const handleBackToCantieri = () => {\n    navigate('/dashboard/cantieri');\n  };\n\n\n\n  // Gestisce le notifiche\n  const handleSuccess = (message) => {\n    // Qui puoi implementare una notifica di successo se necessario\n    console.log('Successo:', message);\n  };\n\n  const handleError = (message) => {\n    // Qui puoi implementare una notifica di errore se necessario\n    console.error('Errore:', message);\n  };\n\n  if (!cantiereId || isNaN(cantiereId)) {\n    return (\n      <Box>\n        <Alert severity=\"error\" sx={{ mb: 2 }}>\n          Nessun cantiere selezionato o ID cantiere non valido. Torna alla pagina dei cantieri.\n        </Alert>\n        <Button\n          variant=\"contained\"\n          startIcon={<ArrowBackIcon />}\n          onClick={handleBackToCantieri}\n        >\n          Torna ai Cantieri\n        </Button>\n      </Box>\n    );\n  }\n\n  return (\n    <Box>\n      <Box sx={{ mb: 3, display: 'flex', alignItems: 'center', justifyContent: 'space-between' }}>\n        <Box sx={{ display: 'flex', alignItems: 'center' }}>\n          <IconButton onClick={handleBackToCantieri} sx={{ mr: 1 }}>\n            <ArrowBackIcon />\n          </IconButton>\n          <IconButton\n            onClick={() => window.location.reload()}\n            sx={{ ml: 2 }}\n            color=\"primary\"\n            title=\"Ricarica la pagina\"\n          >\n            <RefreshIcon />\n          </IconButton>\n        </Box>\n        <AdminHomeButton />\n      </Box>\n\n\n\n      <CertificazioneCaviImproved\n        ref={certificazioneRef}\n        cantiereId={cantiereId}\n        onSuccess={handleSuccess}\n        onError={handleError}\n      />\n    </Box>\n  );\n};\n\nexport default CertificazioneCaviPage;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,SAAS,EAAEC,MAAM,QAAQ,OAAO;AAChD,SACEC,GAAG,EACHC,UAAU,EACVC,KAAK,QACA,eAAe;AACtB,SACEC,SAAS,IAAIC,aAAa,EAC1BC,OAAO,IAAIC,WAAW,QACjB,qBAAqB;AAC5B,SAASC,WAAW,EAAEC,WAAW,QAAQ,kBAAkB;AAC3D,SAASC,OAAO,QAAQ,2BAA2B;AACnD,OAAOC,eAAe,MAAM,yCAAyC;AACrE,OAAOC,0BAA0B,MAAM,kDAAkD;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAE1F,MAAMC,sBAAsB,GAAGA,CAAA,KAAM;EAAAC,EAAA;EACnC,MAAM;IAAEC;EAAgB,CAAC,GAAGP,OAAO,CAAC,CAAC;EACrC,MAAMQ,QAAQ,GAAGV,WAAW,CAAC,CAAC;EAC9B,MAAMW,QAAQ,GAAGV,WAAW,CAAC,CAAC;EAC9B,MAAMW,iBAAiB,GAAGpB,MAAM,CAAC,CAAC;;EAElC;EACA,MAAMqB,UAAU,GAAGC,QAAQ,CAACC,YAAY,CAACC,OAAO,CAAC,oBAAoB,CAAC,EAAE,EAAE,CAAC;EAC3E,MAAMC,YAAY,GAAGF,YAAY,CAACC,OAAO,CAAC,sBAAsB,CAAC,IAAI,YAAYH,UAAU,EAAE;;EAE7F;EACAtB,SAAS,CAAC,MAAM;IACd2B,OAAO,CAACC,GAAG,CAAC,+CAA+C,EAAEN,UAAU,CAAC;EAC1E,CAAC,EAAE,CAACF,QAAQ,CAACS,QAAQ,EAAEP,UAAU,CAAC,CAAC;;EAEnC;EACA,MAAMQ,oBAAoB,GAAGA,CAAA,KAAM;IACjCX,QAAQ,CAAC,qBAAqB,CAAC;EACjC,CAAC;;EAID;EACA,MAAMY,aAAa,GAAIC,OAAO,IAAK;IACjC;IACAL,OAAO,CAACC,GAAG,CAAC,WAAW,EAAEI,OAAO,CAAC;EACnC,CAAC;EAED,MAAMC,WAAW,GAAID,OAAO,IAAK;IAC/B;IACAL,OAAO,CAACO,KAAK,CAAC,SAAS,EAAEF,OAAO,CAAC;EACnC,CAAC;EAED,IAAI,CAACV,UAAU,IAAIa,KAAK,CAACb,UAAU,CAAC,EAAE;IACpC,oBACEP,OAAA,CAACb,GAAG;MAAAkC,QAAA,gBACFrB,OAAA,CAACX,KAAK;QAACiC,QAAQ,EAAC,OAAO;QAACC,EAAE,EAAE;UAAEC,EAAE,EAAE;QAAE,CAAE;QAAAH,QAAA,EAAC;MAEvC;QAAAI,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAO,CAAC,eACR5B,OAAA,CAAC6B,MAAM;QACLC,OAAO,EAAC,WAAW;QACnBC,SAAS,eAAE/B,OAAA,CAACT,aAAa;UAAAkC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAE;QAC7BI,OAAO,EAAEjB,oBAAqB;QAAAM,QAAA,EAC/B;MAED;QAAAI,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAQ,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACN,CAAC;EAEV;EAEA,oBACE5B,OAAA,CAACb,GAAG;IAAAkC,QAAA,gBACFrB,OAAA,CAACb,GAAG;MAACoC,EAAE,EAAE;QAAEC,EAAE,EAAE,CAAC;QAAES,OAAO,EAAE,MAAM;QAAEC,UAAU,EAAE,QAAQ;QAAEC,cAAc,EAAE;MAAgB,CAAE;MAAAd,QAAA,gBACzFrB,OAAA,CAACb,GAAG;QAACoC,EAAE,EAAE;UAAEU,OAAO,EAAE,MAAM;UAAEC,UAAU,EAAE;QAAS,CAAE;QAAAb,QAAA,gBACjDrB,OAAA,CAACZ,UAAU;UAAC4C,OAAO,EAAEjB,oBAAqB;UAACQ,EAAE,EAAE;YAAEa,EAAE,EAAE;UAAE,CAAE;UAAAf,QAAA,eACvDrB,OAAA,CAACT,aAAa;YAAAkC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACP,CAAC,eACb5B,OAAA,CAACZ,UAAU;UACT4C,OAAO,EAAEA,CAAA,KAAMK,MAAM,CAAChC,QAAQ,CAACiC,MAAM,CAAC,CAAE;UACxCf,EAAE,EAAE;YAAEgB,EAAE,EAAE;UAAE,CAAE;UACdC,KAAK,EAAC,SAAS;UACfC,KAAK,EAAC,oBAAoB;UAAApB,QAAA,eAE1BrB,OAAA,CAACP,WAAW;YAAAgC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACL,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACV,CAAC,eACN5B,OAAA,CAACH,eAAe;QAAA4B,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAChB,CAAC,eAIN5B,OAAA,CAACF,0BAA0B;MACzB4C,GAAG,EAAEpC,iBAAkB;MACvBC,UAAU,EAAEA,UAAW;MACvBoC,SAAS,EAAE3B,aAAc;MACzB4B,OAAO,EAAE1B;IAAY;MAAAO,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACtB,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACC,CAAC;AAEV,CAAC;AAAC1B,EAAA,CA/EID,sBAAsB;EAAA,QACEL,OAAO,EAClBF,WAAW,EACXC,WAAW;AAAA;AAAAkD,EAAA,GAHxB5C,sBAAsB;AAiF5B,eAAeA,sBAAsB;AAAC,IAAA4C,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}