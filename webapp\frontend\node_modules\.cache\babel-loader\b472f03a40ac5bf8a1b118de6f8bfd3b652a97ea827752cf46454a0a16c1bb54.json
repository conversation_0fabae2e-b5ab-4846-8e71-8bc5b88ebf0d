{"ast": null, "code": "'use client';\n\nexport { default } from './Typography';\nexport { default as typographyClasses } from './typographyClasses';\nexport * from './typographyClasses';", "map": {"version": 3, "names": ["default", "typographyClasses"], "sources": ["C:/Users/<USER>/PycharmProjects/CMS/webapp/frontend/node_modules/@mui/material/Typography/index.js"], "sourcesContent": ["'use client';\n\nexport { default } from './Typography';\nexport { default as typographyClasses } from './typographyClasses';\nexport * from './typographyClasses';"], "mappings": "AAAA,YAAY;;AAEZ,SAASA,OAAO,QAAQ,cAAc;AACtC,SAASA,OAAO,IAAIC,iBAAiB,QAAQ,qBAAqB;AAClE,cAAc,qBAAqB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}