{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\PycharmProjects\\\\CMS\\\\webapp\\\\frontend\\\\src\\\\components\\\\cavi\\\\MetriPosatiForm.js\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect } from 'react';\nimport { useParams, useNavigate } from 'react-router-dom';\nimport { Box, Button, Card, CardContent, CircularProgress, Dialog, DialogActions, DialogContent, DialogContentText, DialogTitle, FormControl, FormHelperText, Grid, InputLabel, MenuItem, Select, TextField, Typography, Paper, Table, TableBody, TableCell, TableContainer, TableHead, TableRow, Chip, Snackbar, Alert } from '@mui/material';\n// Rimosso import di notistack\n\n// Importa il servizio semplificato\nimport caviSimpleService from '../../services/caviSimpleService';\nimport caviService from '../../services/caviService'; // Per ottenere i cavi\n\n/**\n * Componente per l'inserimento dei metri posati di un cavo\n */\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst MetriPosatiForm = () => {\n  _s();\n  // Parametri dalla URL\n  const {\n    cantiereId\n  } = useParams();\n  const navigate = useNavigate();\n  // Stato per le notifiche\n  const [notification, setNotification] = useState({\n    open: false,\n    message: '',\n    severity: 'success'\n  });\n\n  // Stati del componente\n  const [cavi, setCavi] = useState([]);\n  const [bobine, setBobine] = useState([]);\n  const [selectedCavo, setSelectedCavo] = useState(null);\n  const [formData, setFormData] = useState({\n    id_cavo: '',\n    metri_posati: '',\n    id_bobina: ''\n  });\n  const [errors, setErrors] = useState({});\n  const [loading, setLoading] = useState(false);\n  const [bobineLoading, setBobineLoading] = useState(false);\n  const [confirmDialogOpen, setConfirmDialogOpen] = useState(false);\n  const [confirmDialogMessage, setConfirmDialogMessage] = useState('');\n  const [confirmDialogAction, setConfirmDialogAction] = useState(null);\n\n  // Funzione per mostrare una notifica\n  const showNotification = (message, severity = 'success') => {\n    setNotification({\n      open: true,\n      message,\n      severity\n    });\n  };\n\n  // Funzione per chiudere la notifica\n  const handleCloseNotification = () => {\n    setNotification(prev => ({\n      ...prev,\n      open: false\n    }));\n  };\n\n  // Carica i cavi all'avvio\n  useEffect(() => {\n    loadCavi();\n  }, [cantiereId]);\n\n  // Carica le bobine quando viene selezionato un cavo\n  useEffect(() => {\n    if (selectedCavo) {\n      loadBobine();\n    }\n  }, [selectedCavo]);\n\n  /**\n   * Carica i cavi dal server\n   */\n  const loadCavi = async () => {\n    try {\n      setLoading(true);\n      const caviData = await caviService.getCavi(cantiereId);\n\n      // Filtra solo i cavi che possono essere modificati (Da installare o In corso)\n      const caviModificabili = caviData.filter(cavo => cavo.stato_installazione === 'Da installare' || cavo.stato_installazione === 'In corso');\n      setCavi(caviModificabili);\n    } catch (error) {\n      console.error('Errore nel caricamento dei cavi:', error);\n      showNotification('Errore nel caricamento dei cavi: ' + (error.detail || 'Errore sconosciuto'), 'error');\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  /**\n   * Carica le bobine compatibili con il cavo selezionato\n   */\n  const loadBobine = async () => {\n    if (!selectedCavo) return;\n    try {\n      setBobineLoading(true);\n\n      // Ottieni le bobine compatibili\n      const bobineCompatibili = await caviSimpleService.getBobineCompatibili(cantiereId, selectedCavo.tipologia, selectedCavo.sezione);\n\n      // Ordina le bobine per metri residui (decrescente)\n      bobineCompatibili.sort((a, b) => b.metri_residui - a.metri_residui);\n      setBobine(bobineCompatibili);\n      console.log(`Caricate ${bobineCompatibili.length} bobine compatibili`);\n    } catch (error) {\n      console.error('Errore nel caricamento delle bobine compatibili:', error);\n      showNotification('Errore nel caricamento delle bobine: ' + (error.detail || 'Errore sconosciuto'), 'error');\n    } finally {\n      setBobineLoading(false);\n    }\n  };\n\n  /**\n   * Gestisce la selezione di un cavo\n   */\n  const handleCavoSelect = event => {\n    const cavoId = event.target.value;\n    setFormData({\n      ...formData,\n      id_cavo: cavoId,\n      metri_posati: '',\n      id_bobina: ''\n    });\n\n    // Trova il cavo selezionato\n    const cavo = cavi.find(c => c.id_cavo === cavoId);\n    setSelectedCavo(cavo);\n\n    // Resetta gli errori\n    setErrors({});\n  };\n\n  /**\n   * Gestisce i cambiamenti nei campi del form\n   */\n  const handleFormChange = event => {\n    const {\n      name,\n      value\n    } = event.target;\n\n    // Validazione speciale per metri_posati\n    if (name === 'metri_posati') {\n      // Rimuovi caratteri non numerici eccetto il punto\n      const sanitizedValue = value.replace(/[^0-9.]/g, '');\n\n      // Assicurati che ci sia al massimo un punto decimale\n      const parts = sanitizedValue.split('.');\n      const finalValue = parts.length > 1 ? parts[0] + '.' + parts.slice(1).join('') : sanitizedValue;\n      setFormData({\n        ...formData,\n        [name]: finalValue\n      });\n    } else {\n      setFormData({\n        ...formData,\n        [name]: value\n      });\n    }\n\n    // Resetta gli errori per il campo\n    if (errors[name]) {\n      setErrors({\n        ...errors,\n        [name]: null\n      });\n    }\n  };\n\n  /**\n   * Valida il form prima dell'invio\n   */\n  const validateForm = () => {\n    const newErrors = {};\n\n    // Valida id_cavo\n    if (!formData.id_cavo) {\n      newErrors.id_cavo = 'Seleziona un cavo';\n    }\n\n    // Valida metri_posati\n    if (!formData.metri_posati) {\n      newErrors.metri_posati = 'Inserisci i metri posati';\n    } else {\n      const metriPosati = parseFloat(formData.metri_posati);\n      if (isNaN(metriPosati)) {\n        newErrors.metri_posati = 'I metri posati devono essere un numero';\n      } else if (metriPosati < 0) {\n        newErrors.metri_posati = 'I metri posati non possono essere negativi';\n      }\n\n      // Avvisi (non bloccanti)\n      if (selectedCavo && metriPosati > parseFloat(selectedCavo.metri_teorici || 0)) {\n        // Mostra un avviso ma non bloccare\n        console.warn(`I metri posati (${metriPosati}) superano i metri teorici (${selectedCavo.metri_teorici})`);\n      }\n\n      // Verifica se la bobina selezionata ha abbastanza metri\n      if (formData.id_bobina && formData.id_bobina !== 'BOBINA_VUOTA') {\n        const bobina = bobine.find(b => b.id_bobina === formData.id_bobina);\n        if (bobina && metriPosati > parseFloat(bobina.metri_residui || 0)) {\n          // Mostra un avviso ma non bloccare\n          console.warn(`I metri posati (${metriPosati}) superano i metri residui della bobina (${bobina.metri_residui})`);\n        }\n      }\n    }\n\n    // Valida id_bobina solo se ci sono metri posati\n    if (formData.metri_posati && parseFloat(formData.metri_posati) > 0 && !formData.id_bobina) {\n      newErrors.id_bobina = 'Seleziona una bobina o \"BOBINA VUOTA\"';\n    }\n    setErrors(newErrors);\n    return Object.keys(newErrors).length === 0;\n  };\n\n  /**\n   * Gestisce l'invio del form\n   */\n  const handleSubmit = async event => {\n    event.preventDefault();\n\n    // Valida il form\n    if (!validateForm()) {\n      return;\n    }\n\n    // Estrai i dati dal form\n    const {\n      id_cavo,\n      metri_posati,\n      id_bobina\n    } = formData;\n    const metriPosati = parseFloat(metri_posati);\n\n    // Verifica se ci sono avvisi da mostrare\n    let warningMessage = '';\n\n    // Avviso per metri teorici\n    if (selectedCavo && metriPosati > parseFloat(selectedCavo.metri_teorici || 0)) {\n      warningMessage += `I metri posati (${metriPosati}) superano i metri teorici (${selectedCavo.metri_teorici}).\\n`;\n    }\n\n    // Avviso per metri residui della bobina\n    if (id_bobina && id_bobina !== 'BOBINA_VUOTA') {\n      const bobina = bobine.find(b => b.id_bobina === id_bobina);\n      if (bobina && metriPosati > parseFloat(bobina.metri_residui || 0)) {\n        warningMessage += `I metri posati (${metriPosati}) superano i metri residui della bobina (${bobina.metri_residui}).\\n`;\n      }\n    }\n\n    // Se ci sono avvisi, mostra il dialogo di conferma\n    if (warningMessage) {\n      setConfirmDialogMessage(warningMessage + '\\nVuoi continuare comunque?');\n      setConfirmDialogAction(() => () => submitMetriPosati(id_cavo, metriPosati, id_bobina));\n      setConfirmDialogOpen(true);\n      return;\n    }\n\n    // Altrimenti, procedi direttamente\n    await submitMetriPosati(id_cavo, metriPosati, id_bobina);\n  };\n\n  /**\n   * Invia i metri posati al server\n   */\n  const submitMetriPosati = async (id_cavo, metriPosati, id_bobina) => {\n    try {\n      setLoading(true);\n\n      // Chiudi il dialogo di conferma se aperto\n      setConfirmDialogOpen(false);\n\n      // Invia la richiesta al server\n      const response = await caviSimpleService.updateMetriPosati(cantiereId, id_cavo, metriPosati, id_bobina);\n\n      // Mostra un messaggio di successo\n      enqueueSnackbar('Metri posati aggiornati con successo', {\n        variant: 'success'\n      });\n\n      // Resetta il form\n      setFormData({\n        id_cavo: '',\n        metri_posati: '',\n        id_bobina: ''\n      });\n      setSelectedCavo(null);\n\n      // Ricarica i cavi\n      await loadCavi();\n    } catch (error) {\n      console.error('Errore nell\\'aggiornamento dei metri posati:', error);\n      enqueueSnackbar('Errore: ' + (error.detail || 'Errore sconosciuto'), {\n        variant: 'error'\n      });\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  /**\n   * Gestisce la chiusura del dialogo di conferma\n   */\n  const handleConfirmDialogClose = () => {\n    setConfirmDialogOpen(false);\n    setConfirmDialogAction(null);\n  };\n\n  /**\n   * Gestisce la conferma nel dialogo\n   */\n  const handleConfirmDialogConfirm = () => {\n    if (confirmDialogAction) {\n      confirmDialogAction();\n    }\n  };\n  return /*#__PURE__*/_jsxDEV(Box, {\n    sx: {\n      p: 3\n    },\n    children: [/*#__PURE__*/_jsxDEV(Typography, {\n      variant: \"h5\",\n      gutterBottom: true,\n      children: \"Inserimento Metri Posati\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 345,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Card, {\n      sx: {\n        mb: 3\n      },\n      children: /*#__PURE__*/_jsxDEV(CardContent, {\n        children: /*#__PURE__*/_jsxDEV(\"form\", {\n          onSubmit: handleSubmit,\n          children: /*#__PURE__*/_jsxDEV(Grid, {\n            container: true,\n            spacing: 3,\n            children: [/*#__PURE__*/_jsxDEV(Grid, {\n              item: true,\n              xs: 12,\n              md: 6,\n              children: /*#__PURE__*/_jsxDEV(FormControl, {\n                fullWidth: true,\n                error: !!errors.id_cavo,\n                children: [/*#__PURE__*/_jsxDEV(InputLabel, {\n                  id: \"cavo-select-label\",\n                  children: \"Cavo\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 356,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(Select, {\n                  labelId: \"cavo-select-label\",\n                  id: \"cavo-select\",\n                  name: \"id_cavo\",\n                  value: formData.id_cavo,\n                  onChange: handleCavoSelect,\n                  disabled: loading,\n                  children: [/*#__PURE__*/_jsxDEV(MenuItem, {\n                    value: \"\",\n                    children: /*#__PURE__*/_jsxDEV(\"em\", {\n                      children: \"Seleziona un cavo\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 366,\n                      columnNumber: 23\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 365,\n                    columnNumber: 21\n                  }, this), cavi.map(cavo => /*#__PURE__*/_jsxDEV(MenuItem, {\n                    value: cavo.id_cavo,\n                    children: [cavo.id_cavo, \" - \", cavo.tipologia, \" - \", cavo.sezione, \" - \", cavo.utility]\n                  }, cavo.id_cavo, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 369,\n                    columnNumber: 23\n                  }, this))]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 357,\n                  columnNumber: 19\n                }, this), errors.id_cavo && /*#__PURE__*/_jsxDEV(FormHelperText, {\n                  children: errors.id_cavo\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 374,\n                  columnNumber: 38\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 355,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 354,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Grid, {\n              item: true,\n              xs: 12,\n              md: 6,\n              children: /*#__PURE__*/_jsxDEV(TextField, {\n                fullWidth: true,\n                label: \"Metri posati\",\n                name: \"metri_posati\",\n                value: formData.metri_posati,\n                onChange: handleFormChange,\n                error: !!errors.metri_posati,\n                helperText: errors.metri_posati,\n                disabled: !selectedCavo || loading,\n                InputProps: {\n                  endAdornment: /*#__PURE__*/_jsxDEV(Typography, {\n                    variant: \"body2\",\n                    children: \"m\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 390,\n                    columnNumber: 35\n                  }, this)\n                }\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 380,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 379,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Grid, {\n              item: true,\n              xs: 12,\n              children: /*#__PURE__*/_jsxDEV(FormControl, {\n                fullWidth: true,\n                error: !!errors.id_bobina,\n                children: [/*#__PURE__*/_jsxDEV(InputLabel, {\n                  id: \"bobina-select-label\",\n                  children: \"Bobina\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 398,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(Select, {\n                  labelId: \"bobina-select-label\",\n                  id: \"bobina-select\",\n                  name: \"id_bobina\",\n                  value: formData.id_bobina,\n                  onChange: handleFormChange,\n                  disabled: !selectedCavo || bobineLoading || loading,\n                  children: [/*#__PURE__*/_jsxDEV(MenuItem, {\n                    value: \"\",\n                    children: /*#__PURE__*/_jsxDEV(\"em\", {\n                      children: \"Seleziona una bobina\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 408,\n                      columnNumber: 23\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 407,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(MenuItem, {\n                    value: \"BOBINA_VUOTA\",\n                    children: \"BOBINA VUOTA (Cavo gi\\xE0 posato senza riferimento a bobina)\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 410,\n                    columnNumber: 21\n                  }, this), bobine.map(bobina => /*#__PURE__*/_jsxDEV(MenuItem, {\n                    value: bobina.id_bobina,\n                    sx: {\n                      bgcolor: selectedCavo && String(bobina.tipologia || '').trim() === String(selectedCavo.tipologia || '').trim() && String(bobina.sezione || '0').trim() === String(selectedCavo.sezione || '0').trim() ? 'rgba(76, 175, 80, 0.08)' : 'inherit'\n                    },\n                    children: [bobina.id_bobina, \" - \", bobina.tipologia, \" - \", bobina.sezione, \" - \", bobina.metri_residui, \"m residui\", selectedCavo && String(bobina.tipologia || '').trim() === String(selectedCavo.tipologia || '').trim() && String(bobina.sezione || '0').trim() === String(selectedCavo.sezione || '0').trim() && /*#__PURE__*/_jsxDEV(Chip, {\n                      label: \"Compatibile\",\n                      color: \"success\",\n                      size: \"small\",\n                      sx: {\n                        ml: 1\n                      }\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 428,\n                      columnNumber: 29\n                    }, this)]\n                  }, bobina.id_bobina, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 414,\n                    columnNumber: 23\n                  }, this))]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 399,\n                  columnNumber: 19\n                }, this), errors.id_bobina && /*#__PURE__*/_jsxDEV(FormHelperText, {\n                  children: errors.id_bobina\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 439,\n                  columnNumber: 40\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 397,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 396,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Grid, {\n              item: true,\n              xs: 12,\n              children: /*#__PURE__*/_jsxDEV(Box, {\n                sx: {\n                  display: 'flex',\n                  justifyContent: 'flex-end',\n                  gap: 2\n                },\n                children: [/*#__PURE__*/_jsxDEV(Button, {\n                  variant: \"outlined\",\n                  onClick: () => navigate(`/cantieri/${cantiereId}/cavi`),\n                  disabled: loading,\n                  children: \"Annulla\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 446,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(Button, {\n                  type: \"submit\",\n                  variant: \"contained\",\n                  color: \"primary\",\n                  disabled: loading,\n                  startIcon: loading && /*#__PURE__*/_jsxDEV(CircularProgress, {\n                    size: 20,\n                    color: \"inherit\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 458,\n                    columnNumber: 43\n                  }, this),\n                  children: \"Salva\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 453,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 445,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 444,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 352,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 351,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 350,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 349,\n      columnNumber: 7\n    }, this), selectedCavo && /*#__PURE__*/_jsxDEV(Card, {\n      children: /*#__PURE__*/_jsxDEV(CardContent, {\n        children: [/*#__PURE__*/_jsxDEV(Typography, {\n          variant: \"h6\",\n          gutterBottom: true,\n          children: \"Dettagli del cavo selezionato\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 473,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(TableContainer, {\n          component: Paper,\n          children: /*#__PURE__*/_jsxDEV(Table, {\n            size: \"small\",\n            children: [/*#__PURE__*/_jsxDEV(TableHead, {\n              children: /*#__PURE__*/_jsxDEV(TableRow, {\n                children: [/*#__PURE__*/_jsxDEV(TableCell, {\n                  children: \"ID Cavo\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 480,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n                  children: \"Tipologia\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 481,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n                  children: \"Formazione\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 482,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n                  children: \"Utility\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 483,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n                  children: \"Metri teorici\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 484,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n                  children: \"Stato\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 485,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 479,\n                columnNumber: 19\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 478,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(TableBody, {\n              children: /*#__PURE__*/_jsxDEV(TableRow, {\n                children: [/*#__PURE__*/_jsxDEV(TableCell, {\n                  children: selectedCavo.id_cavo\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 490,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n                  children: selectedCavo.tipologia\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 491,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n                  children: selectedCavo.sezione\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 492,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n                  children: selectedCavo.utility\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 493,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n                  children: [selectedCavo.metri_teorici, \"m\"]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 494,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n                  children: selectedCavo.stato_installazione\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 495,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 489,\n                columnNumber: 19\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 488,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 477,\n            columnNumber: 15\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 476,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 472,\n        columnNumber: 11\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 471,\n      columnNumber: 9\n    }, this), /*#__PURE__*/_jsxDEV(Dialog, {\n      open: confirmDialogOpen,\n      onClose: handleConfirmDialogClose,\n      children: [/*#__PURE__*/_jsxDEV(DialogTitle, {\n        children: \"Conferma operazione\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 509,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(DialogContent, {\n        children: /*#__PURE__*/_jsxDEV(DialogContentText, {\n          children: confirmDialogMessage\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 511,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 510,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(DialogActions, {\n        children: [/*#__PURE__*/_jsxDEV(Button, {\n          onClick: handleConfirmDialogClose,\n          color: \"primary\",\n          children: \"Annulla\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 516,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Button, {\n          onClick: handleConfirmDialogConfirm,\n          color: \"primary\",\n          autoFocus: true,\n          children: \"Conferma\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 519,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 515,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 505,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 344,\n    columnNumber: 5\n  }, this);\n};\n_s(MetriPosatiForm, \"aslCWZQthbZ0/KIKKwbwaAzOXiM=\", false, function () {\n  return [useParams, useNavigate];\n});\n_c = MetriPosatiForm;\nexport default MetriPosatiForm;\nvar _c;\n$RefreshReg$(_c, \"MetriPosatiForm\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "useParams", "useNavigate", "Box", "<PERSON><PERSON>", "Card", "<PERSON><PERSON><PERSON><PERSON>", "CircularProgress", "Dialog", "DialogActions", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "DialogContentText", "DialogTitle", "FormControl", "FormHelperText", "Grid", "InputLabel", "MenuItem", "Select", "TextField", "Typography", "Paper", "Table", "TableBody", "TableCell", "TableContainer", "TableHead", "TableRow", "Chip", "Snackbar", "<PERSON><PERSON>", "caviSimpleService", "caviService", "jsxDEV", "_jsxDEV", "MetriPosatiForm", "_s", "cantiereId", "navigate", "notification", "setNotification", "open", "message", "severity", "cavi", "<PERSON><PERSON><PERSON>", "bobine", "set<PERSON>ob<PERSON>", "selected<PERSON><PERSON><PERSON>", "setSelectedCavo", "formData", "setFormData", "id_cavo", "metri_posati", "id_bobina", "errors", "setErrors", "loading", "setLoading", "bob<PERSON><PERSON><PERSON><PERSON>", "setBobineLoading", "confirmDialogOpen", "setConfirmDialogOpen", "confirmDialogMessage", "setConfirmDialogMessage", "confirmDialogAction", "setConfirmDialogAction", "showNotification", "handleCloseNotification", "prev", "loadCavi", "loadBobine", "caviData", "get<PERSON><PERSON>", "caviModificabili", "filter", "cavo", "stato_installazione", "error", "console", "detail", "bobineCom<PERSON><PERSON><PERSON><PERSON>", "getBobineCompatibili", "tipologia", "sezione", "sort", "a", "b", "metri_residui", "log", "length", "handleCavoSelect", "event", "cavoId", "target", "value", "find", "c", "handleFormChange", "name", "sanitizedValue", "replace", "parts", "split", "finalValue", "slice", "join", "validateForm", "newErrors", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "parseFloat", "isNaN", "metri_te<PERSON>ci", "warn", "bobina", "Object", "keys", "handleSubmit", "preventDefault", "warningMessage", "submit<PERSON><PERSON>ri<PERSON><PERSON><PERSON>", "response", "updateMetri<PERSON><PERSON><PERSON>", "enqueueSnackbar", "variant", "handleConfirmDialogClose", "handleConfirmDialogConfirm", "sx", "p", "children", "gutterBottom", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "mb", "onSubmit", "container", "spacing", "item", "xs", "md", "fullWidth", "id", "labelId", "onChange", "disabled", "map", "utility", "label", "helperText", "InputProps", "endAdornment", "bgcolor", "String", "trim", "color", "size", "ml", "display", "justifyContent", "gap", "onClick", "type", "startIcon", "component", "onClose", "autoFocus", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/PycharmProjects/CMS/webapp/frontend/src/components/cavi/MetriPosatiForm.js"], "sourcesContent": ["import React, { useState, useEffect } from 'react';\nimport { useParams, useNavigate } from 'react-router-dom';\nimport {\n  Box,\n  Button,\n  Card,\n  CardContent,\n  CircularProgress,\n  Dialog,\n  DialogActions,\n  DialogContent,\n  DialogContentText,\n  DialogTitle,\n  FormControl,\n  FormHelperText,\n  Grid,\n  InputLabel,\n  MenuItem,\n  Select,\n  TextField,\n  Typography,\n  Paper,\n  Table,\n  TableBody,\n  TableCell,\n  TableContainer,\n  TableHead,\n  TableRow,\n  Chip,\n  Snackbar,\n  Alert\n} from '@mui/material';\n// Rimosso import di notistack\n\n// Importa il servizio semplificato\nimport caviSimpleService from '../../services/caviSimpleService';\nimport caviService from '../../services/caviService'; // Per ottenere i cavi\n\n/**\n * Componente per l'inserimento dei metri posati di un cavo\n */\nconst MetriPosatiForm = () => {\n  // Parametri dalla URL\n  const { cantiereId } = useParams();\n  const navigate = useNavigate();\n  // Stato per le notifiche\n  const [notification, setNotification] = useState({ open: false, message: '', severity: 'success' });\n\n  // Stati del componente\n  const [cavi, setCavi] = useState([]);\n  const [bobine, setBobine] = useState([]);\n  const [selectedCavo, setSelectedCavo] = useState(null);\n  const [formData, setFormData] = useState({\n    id_cavo: '',\n    metri_posati: '',\n    id_bobina: ''\n  });\n  const [errors, setErrors] = useState({});\n  const [loading, setLoading] = useState(false);\n  const [bobineLoading, setBobineLoading] = useState(false);\n  const [confirmDialogOpen, setConfirmDialogOpen] = useState(false);\n  const [confirmDialogMessage, setConfirmDialogMessage] = useState('');\n  const [confirmDialogAction, setConfirmDialogAction] = useState(null);\n\n  // Funzione per mostrare una notifica\n  const showNotification = (message, severity = 'success') => {\n    setNotification({ open: true, message, severity });\n  };\n\n  // Funzione per chiudere la notifica\n  const handleCloseNotification = () => {\n    setNotification(prev => ({ ...prev, open: false }));\n  };\n\n  // Carica i cavi all'avvio\n  useEffect(() => {\n    loadCavi();\n  }, [cantiereId]);\n\n  // Carica le bobine quando viene selezionato un cavo\n  useEffect(() => {\n    if (selectedCavo) {\n      loadBobine();\n    }\n  }, [selectedCavo]);\n\n  /**\n   * Carica i cavi dal server\n   */\n  const loadCavi = async () => {\n    try {\n      setLoading(true);\n      const caviData = await caviService.getCavi(cantiereId);\n\n      // Filtra solo i cavi che possono essere modificati (Da installare o In corso)\n      const caviModificabili = caviData.filter(cavo =>\n        cavo.stato_installazione === 'Da installare' ||\n        cavo.stato_installazione === 'In corso'\n      );\n\n      setCavi(caviModificabili);\n    } catch (error) {\n      console.error('Errore nel caricamento dei cavi:', error);\n      showNotification('Errore nel caricamento dei cavi: ' + (error.detail || 'Errore sconosciuto'), 'error');\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  /**\n   * Carica le bobine compatibili con il cavo selezionato\n   */\n  const loadBobine = async () => {\n    if (!selectedCavo) return;\n\n    try {\n      setBobineLoading(true);\n\n      // Ottieni le bobine compatibili\n      const bobineCompatibili = await caviSimpleService.getBobineCompatibili(\n        cantiereId,\n        selectedCavo.tipologia,\n        selectedCavo.sezione\n      );\n\n      // Ordina le bobine per metri residui (decrescente)\n      bobineCompatibili.sort((a, b) => b.metri_residui - a.metri_residui);\n\n      setBobine(bobineCompatibili);\n      console.log(`Caricate ${bobineCompatibili.length} bobine compatibili`);\n    } catch (error) {\n      console.error('Errore nel caricamento delle bobine compatibili:', error);\n      showNotification('Errore nel caricamento delle bobine: ' + (error.detail || 'Errore sconosciuto'), 'error');\n    } finally {\n      setBobineLoading(false);\n    }\n  };\n\n  /**\n   * Gestisce la selezione di un cavo\n   */\n  const handleCavoSelect = (event) => {\n    const cavoId = event.target.value;\n    setFormData({\n      ...formData,\n      id_cavo: cavoId,\n      metri_posati: '',\n      id_bobina: ''\n    });\n\n    // Trova il cavo selezionato\n    const cavo = cavi.find(c => c.id_cavo === cavoId);\n    setSelectedCavo(cavo);\n\n    // Resetta gli errori\n    setErrors({});\n  };\n\n  /**\n   * Gestisce i cambiamenti nei campi del form\n   */\n  const handleFormChange = (event) => {\n    const { name, value } = event.target;\n\n    // Validazione speciale per metri_posati\n    if (name === 'metri_posati') {\n      // Rimuovi caratteri non numerici eccetto il punto\n      const sanitizedValue = value.replace(/[^0-9.]/g, '');\n\n      // Assicurati che ci sia al massimo un punto decimale\n      const parts = sanitizedValue.split('.');\n      const finalValue = parts.length > 1\n        ? parts[0] + '.' + parts.slice(1).join('')\n        : sanitizedValue;\n\n      setFormData({\n        ...formData,\n        [name]: finalValue\n      });\n    } else {\n      setFormData({\n        ...formData,\n        [name]: value\n      });\n    }\n\n    // Resetta gli errori per il campo\n    if (errors[name]) {\n      setErrors({\n        ...errors,\n        [name]: null\n      });\n    }\n  };\n\n  /**\n   * Valida il form prima dell'invio\n   */\n  const validateForm = () => {\n    const newErrors = {};\n\n    // Valida id_cavo\n    if (!formData.id_cavo) {\n      newErrors.id_cavo = 'Seleziona un cavo';\n    }\n\n    // Valida metri_posati\n    if (!formData.metri_posati) {\n      newErrors.metri_posati = 'Inserisci i metri posati';\n    } else {\n      const metriPosati = parseFloat(formData.metri_posati);\n\n      if (isNaN(metriPosati)) {\n        newErrors.metri_posati = 'I metri posati devono essere un numero';\n      } else if (metriPosati < 0) {\n        newErrors.metri_posati = 'I metri posati non possono essere negativi';\n      }\n\n      // Avvisi (non bloccanti)\n      if (selectedCavo && metriPosati > parseFloat(selectedCavo.metri_teorici || 0)) {\n        // Mostra un avviso ma non bloccare\n        console.warn(`I metri posati (${metriPosati}) superano i metri teorici (${selectedCavo.metri_teorici})`);\n      }\n\n      // Verifica se la bobina selezionata ha abbastanza metri\n      if (formData.id_bobina && formData.id_bobina !== 'BOBINA_VUOTA') {\n        const bobina = bobine.find(b => b.id_bobina === formData.id_bobina);\n        if (bobina && metriPosati > parseFloat(bobina.metri_residui || 0)) {\n          // Mostra un avviso ma non bloccare\n          console.warn(`I metri posati (${metriPosati}) superano i metri residui della bobina (${bobina.metri_residui})`);\n        }\n      }\n    }\n\n    // Valida id_bobina solo se ci sono metri posati\n    if (formData.metri_posati && parseFloat(formData.metri_posati) > 0 && !formData.id_bobina) {\n      newErrors.id_bobina = 'Seleziona una bobina o \"BOBINA VUOTA\"';\n    }\n\n    setErrors(newErrors);\n    return Object.keys(newErrors).length === 0;\n  };\n\n  /**\n   * Gestisce l'invio del form\n   */\n  const handleSubmit = async (event) => {\n    event.preventDefault();\n\n    // Valida il form\n    if (!validateForm()) {\n      return;\n    }\n\n    // Estrai i dati dal form\n    const { id_cavo, metri_posati, id_bobina } = formData;\n    const metriPosati = parseFloat(metri_posati);\n\n    // Verifica se ci sono avvisi da mostrare\n    let warningMessage = '';\n\n    // Avviso per metri teorici\n    if (selectedCavo && metriPosati > parseFloat(selectedCavo.metri_teorici || 0)) {\n      warningMessage += `I metri posati (${metriPosati}) superano i metri teorici (${selectedCavo.metri_teorici}).\\n`;\n    }\n\n    // Avviso per metri residui della bobina\n    if (id_bobina && id_bobina !== 'BOBINA_VUOTA') {\n      const bobina = bobine.find(b => b.id_bobina === id_bobina);\n      if (bobina && metriPosati > parseFloat(bobina.metri_residui || 0)) {\n        warningMessage += `I metri posati (${metriPosati}) superano i metri residui della bobina (${bobina.metri_residui}).\\n`;\n      }\n    }\n\n    // Se ci sono avvisi, mostra il dialogo di conferma\n    if (warningMessage) {\n      setConfirmDialogMessage(warningMessage + '\\nVuoi continuare comunque?');\n      setConfirmDialogAction(() => () => submitMetriPosati(id_cavo, metriPosati, id_bobina));\n      setConfirmDialogOpen(true);\n      return;\n    }\n\n    // Altrimenti, procedi direttamente\n    await submitMetriPosati(id_cavo, metriPosati, id_bobina);\n  };\n\n  /**\n   * Invia i metri posati al server\n   */\n  const submitMetriPosati = async (id_cavo, metriPosati, id_bobina) => {\n    try {\n      setLoading(true);\n\n      // Chiudi il dialogo di conferma se aperto\n      setConfirmDialogOpen(false);\n\n      // Invia la richiesta al server\n      const response = await caviSimpleService.updateMetriPosati(\n        cantiereId,\n        id_cavo,\n        metriPosati,\n        id_bobina\n      );\n\n      // Mostra un messaggio di successo\n      enqueueSnackbar('Metri posati aggiornati con successo', { variant: 'success' });\n\n      // Resetta il form\n      setFormData({\n        id_cavo: '',\n        metri_posati: '',\n        id_bobina: ''\n      });\n      setSelectedCavo(null);\n\n      // Ricarica i cavi\n      await loadCavi();\n    } catch (error) {\n      console.error('Errore nell\\'aggiornamento dei metri posati:', error);\n      enqueueSnackbar('Errore: ' + (error.detail || 'Errore sconosciuto'), { variant: 'error' });\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  /**\n   * Gestisce la chiusura del dialogo di conferma\n   */\n  const handleConfirmDialogClose = () => {\n    setConfirmDialogOpen(false);\n    setConfirmDialogAction(null);\n  };\n\n  /**\n   * Gestisce la conferma nel dialogo\n   */\n  const handleConfirmDialogConfirm = () => {\n    if (confirmDialogAction) {\n      confirmDialogAction();\n    }\n  };\n\n  return (\n    <Box sx={{ p: 3 }}>\n      <Typography variant=\"h5\" gutterBottom>\n        Inserimento Metri Posati\n      </Typography>\n\n      <Card sx={{ mb: 3 }}>\n        <CardContent>\n          <form onSubmit={handleSubmit}>\n            <Grid container spacing={3}>\n              {/* Selezione del cavo */}\n              <Grid item xs={12} md={6}>\n                <FormControl fullWidth error={!!errors.id_cavo}>\n                  <InputLabel id=\"cavo-select-label\">Cavo</InputLabel>\n                  <Select\n                    labelId=\"cavo-select-label\"\n                    id=\"cavo-select\"\n                    name=\"id_cavo\"\n                    value={formData.id_cavo}\n                    onChange={handleCavoSelect}\n                    disabled={loading}\n                  >\n                    <MenuItem value=\"\">\n                      <em>Seleziona un cavo</em>\n                    </MenuItem>\n                    {cavi.map((cavo) => (\n                      <MenuItem key={cavo.id_cavo} value={cavo.id_cavo}>\n                        {cavo.id_cavo} - {cavo.tipologia} - {cavo.sezione} - {cavo.utility}\n                      </MenuItem>\n                    ))}\n                  </Select>\n                  {errors.id_cavo && <FormHelperText>{errors.id_cavo}</FormHelperText>}\n                </FormControl>\n              </Grid>\n\n              {/* Metri posati */}\n              <Grid item xs={12} md={6}>\n                <TextField\n                  fullWidth\n                  label=\"Metri posati\"\n                  name=\"metri_posati\"\n                  value={formData.metri_posati}\n                  onChange={handleFormChange}\n                  error={!!errors.metri_posati}\n                  helperText={errors.metri_posati}\n                  disabled={!selectedCavo || loading}\n                  InputProps={{\n                    endAdornment: <Typography variant=\"body2\">m</Typography>\n                  }}\n                />\n              </Grid>\n\n              {/* Selezione della bobina */}\n              <Grid item xs={12}>\n                <FormControl fullWidth error={!!errors.id_bobina}>\n                  <InputLabel id=\"bobina-select-label\">Bobina</InputLabel>\n                  <Select\n                    labelId=\"bobina-select-label\"\n                    id=\"bobina-select\"\n                    name=\"id_bobina\"\n                    value={formData.id_bobina}\n                    onChange={handleFormChange}\n                    disabled={!selectedCavo || bobineLoading || loading}\n                  >\n                    <MenuItem value=\"\">\n                      <em>Seleziona una bobina</em>\n                    </MenuItem>\n                    <MenuItem value=\"BOBINA_VUOTA\">\n                      BOBINA VUOTA (Cavo già posato senza riferimento a bobina)\n                    </MenuItem>\n                    {bobine.map((bobina) => (\n                      <MenuItem\n                        key={bobina.id_bobina}\n                        value={bobina.id_bobina}\n                        sx={{\n                          bgcolor: selectedCavo &&\n                            String(bobina.tipologia || '').trim() === String(selectedCavo.tipologia || '').trim() &&\n                            String(bobina.sezione || '0').trim() === String(selectedCavo.sezione || '0').trim() ?\n                            'rgba(76, 175, 80, 0.08)' : 'inherit'\n                        }}\n                      >\n                        {bobina.id_bobina} - {bobina.tipologia} - {bobina.sezione} - {bobina.metri_residui}m residui\n                        {selectedCavo &&\n                          String(bobina.tipologia || '').trim() === String(selectedCavo.tipologia || '').trim() &&\n                          String(bobina.sezione || '0').trim() === String(selectedCavo.sezione || '0').trim() && (\n                            <Chip\n                              label=\"Compatibile\"\n                              color=\"success\"\n                              size=\"small\"\n                              sx={{ ml: 1 }}\n                            />\n                          )\n                        }\n                      </MenuItem>\n                    ))}\n                  </Select>\n                  {errors.id_bobina && <FormHelperText>{errors.id_bobina}</FormHelperText>}\n                </FormControl>\n              </Grid>\n\n              {/* Pulsanti */}\n              <Grid item xs={12}>\n                <Box sx={{ display: 'flex', justifyContent: 'flex-end', gap: 2 }}>\n                  <Button\n                    variant=\"outlined\"\n                    onClick={() => navigate(`/cantieri/${cantiereId}/cavi`)}\n                    disabled={loading}\n                  >\n                    Annulla\n                  </Button>\n                  <Button\n                    type=\"submit\"\n                    variant=\"contained\"\n                    color=\"primary\"\n                    disabled={loading}\n                    startIcon={loading && <CircularProgress size={20} color=\"inherit\" />}\n                  >\n                    Salva\n                  </Button>\n                </Box>\n              </Grid>\n            </Grid>\n          </form>\n        </CardContent>\n      </Card>\n\n      {/* Dettagli del cavo selezionato */}\n      {selectedCavo && (\n        <Card>\n          <CardContent>\n            <Typography variant=\"h6\" gutterBottom>\n              Dettagli del cavo selezionato\n            </Typography>\n            <TableContainer component={Paper}>\n              <Table size=\"small\">\n                <TableHead>\n                  <TableRow>\n                    <TableCell>ID Cavo</TableCell>\n                    <TableCell>Tipologia</TableCell>\n                    <TableCell>Formazione</TableCell>\n                    <TableCell>Utility</TableCell>\n                    <TableCell>Metri teorici</TableCell>\n                    <TableCell>Stato</TableCell>\n                  </TableRow>\n                </TableHead>\n                <TableBody>\n                  <TableRow>\n                    <TableCell>{selectedCavo.id_cavo}</TableCell>\n                    <TableCell>{selectedCavo.tipologia}</TableCell>\n                    <TableCell>{selectedCavo.sezione}</TableCell>\n                    <TableCell>{selectedCavo.utility}</TableCell>\n                    <TableCell>{selectedCavo.metri_teorici}m</TableCell>\n                    <TableCell>{selectedCavo.stato_installazione}</TableCell>\n                  </TableRow>\n                </TableBody>\n              </Table>\n            </TableContainer>\n          </CardContent>\n        </Card>\n      )}\n\n      {/* Dialogo di conferma */}\n      <Dialog\n        open={confirmDialogOpen}\n        onClose={handleConfirmDialogClose}\n      >\n        <DialogTitle>Conferma operazione</DialogTitle>\n        <DialogContent>\n          <DialogContentText>\n            {confirmDialogMessage}\n          </DialogContentText>\n        </DialogContent>\n        <DialogActions>\n          <Button onClick={handleConfirmDialogClose} color=\"primary\">\n            Annulla\n          </Button>\n          <Button onClick={handleConfirmDialogConfirm} color=\"primary\" autoFocus>\n            Conferma\n          </Button>\n        </DialogActions>\n      </Dialog>\n    </Box>\n  );\n};\n\nexport default MetriPosatiForm;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAClD,SAASC,SAAS,EAAEC,WAAW,QAAQ,kBAAkB;AACzD,SACEC,GAAG,EACHC,MAAM,EACNC,IAAI,EACJC,WAAW,EACXC,gBAAgB,EAChBC,MAAM,EACNC,aAAa,EACbC,aAAa,EACbC,iBAAiB,EACjBC,WAAW,EACXC,WAAW,EACXC,cAAc,EACdC,IAAI,EACJC,UAAU,EACVC,QAAQ,EACRC,MAAM,EACNC,SAAS,EACTC,UAAU,EACVC,KAAK,EACLC,KAAK,EACLC,SAAS,EACTC,SAAS,EACTC,cAAc,EACdC,SAAS,EACTC,QAAQ,EACRC,IAAI,EACJC,QAAQ,EACRC,KAAK,QACA,eAAe;AACtB;;AAEA;AACA,OAAOC,iBAAiB,MAAM,kCAAkC;AAChE,OAAOC,WAAW,MAAM,4BAA4B,CAAC,CAAC;;AAEtD;AACA;AACA;AAFA,SAAAC,MAAA,IAAAC,OAAA;AAGA,MAAMC,eAAe,GAAGA,CAAA,KAAM;EAAAC,EAAA;EAC5B;EACA,MAAM;IAAEC;EAAW,CAAC,GAAGpC,SAAS,CAAC,CAAC;EAClC,MAAMqC,QAAQ,GAAGpC,WAAW,CAAC,CAAC;EAC9B;EACA,MAAM,CAACqC,YAAY,EAAEC,eAAe,CAAC,GAAGzC,QAAQ,CAAC;IAAE0C,IAAI,EAAE,KAAK;IAAEC,OAAO,EAAE,EAAE;IAAEC,QAAQ,EAAE;EAAU,CAAC,CAAC;;EAEnG;EACA,MAAM,CAACC,IAAI,EAAEC,OAAO,CAAC,GAAG9C,QAAQ,CAAC,EAAE,CAAC;EACpC,MAAM,CAAC+C,MAAM,EAAEC,SAAS,CAAC,GAAGhD,QAAQ,CAAC,EAAE,CAAC;EACxC,MAAM,CAACiD,YAAY,EAAEC,eAAe,CAAC,GAAGlD,QAAQ,CAAC,IAAI,CAAC;EACtD,MAAM,CAACmD,QAAQ,EAAEC,WAAW,CAAC,GAAGpD,QAAQ,CAAC;IACvCqD,OAAO,EAAE,EAAE;IACXC,YAAY,EAAE,EAAE;IAChBC,SAAS,EAAE;EACb,CAAC,CAAC;EACF,MAAM,CAACC,MAAM,EAAEC,SAAS,CAAC,GAAGzD,QAAQ,CAAC,CAAC,CAAC,CAAC;EACxC,MAAM,CAAC0D,OAAO,EAAEC,UAAU,CAAC,GAAG3D,QAAQ,CAAC,KAAK,CAAC;EAC7C,MAAM,CAAC4D,aAAa,EAAEC,gBAAgB,CAAC,GAAG7D,QAAQ,CAAC,KAAK,CAAC;EACzD,MAAM,CAAC8D,iBAAiB,EAAEC,oBAAoB,CAAC,GAAG/D,QAAQ,CAAC,KAAK,CAAC;EACjE,MAAM,CAACgE,oBAAoB,EAAEC,uBAAuB,CAAC,GAAGjE,QAAQ,CAAC,EAAE,CAAC;EACpE,MAAM,CAACkE,mBAAmB,EAAEC,sBAAsB,CAAC,GAAGnE,QAAQ,CAAC,IAAI,CAAC;;EAEpE;EACA,MAAMoE,gBAAgB,GAAGA,CAACzB,OAAO,EAAEC,QAAQ,GAAG,SAAS,KAAK;IAC1DH,eAAe,CAAC;MAAEC,IAAI,EAAE,IAAI;MAAEC,OAAO;MAAEC;IAAS,CAAC,CAAC;EACpD,CAAC;;EAED;EACA,MAAMyB,uBAAuB,GAAGA,CAAA,KAAM;IACpC5B,eAAe,CAAC6B,IAAI,KAAK;MAAE,GAAGA,IAAI;MAAE5B,IAAI,EAAE;IAAM,CAAC,CAAC,CAAC;EACrD,CAAC;;EAED;EACAzC,SAAS,CAAC,MAAM;IACdsE,QAAQ,CAAC,CAAC;EACZ,CAAC,EAAE,CAACjC,UAAU,CAAC,CAAC;;EAEhB;EACArC,SAAS,CAAC,MAAM;IACd,IAAIgD,YAAY,EAAE;MAChBuB,UAAU,CAAC,CAAC;IACd;EACF,CAAC,EAAE,CAACvB,YAAY,CAAC,CAAC;;EAElB;AACF;AACA;EACE,MAAMsB,QAAQ,GAAG,MAAAA,CAAA,KAAY;IAC3B,IAAI;MACFZ,UAAU,CAAC,IAAI,CAAC;MAChB,MAAMc,QAAQ,GAAG,MAAMxC,WAAW,CAACyC,OAAO,CAACpC,UAAU,CAAC;;MAEtD;MACA,MAAMqC,gBAAgB,GAAGF,QAAQ,CAACG,MAAM,CAACC,IAAI,IAC3CA,IAAI,CAACC,mBAAmB,KAAK,eAAe,IAC5CD,IAAI,CAACC,mBAAmB,KAAK,UAC/B,CAAC;MAEDhC,OAAO,CAAC6B,gBAAgB,CAAC;IAC3B,CAAC,CAAC,OAAOI,KAAK,EAAE;MACdC,OAAO,CAACD,KAAK,CAAC,kCAAkC,EAAEA,KAAK,CAAC;MACxDX,gBAAgB,CAAC,mCAAmC,IAAIW,KAAK,CAACE,MAAM,IAAI,oBAAoB,CAAC,EAAE,OAAO,CAAC;IACzG,CAAC,SAAS;MACRtB,UAAU,CAAC,KAAK,CAAC;IACnB;EACF,CAAC;;EAED;AACF;AACA;EACE,MAAMa,UAAU,GAAG,MAAAA,CAAA,KAAY;IAC7B,IAAI,CAACvB,YAAY,EAAE;IAEnB,IAAI;MACFY,gBAAgB,CAAC,IAAI,CAAC;;MAEtB;MACA,MAAMqB,iBAAiB,GAAG,MAAMlD,iBAAiB,CAACmD,oBAAoB,CACpE7C,UAAU,EACVW,YAAY,CAACmC,SAAS,EACtBnC,YAAY,CAACoC,OACf,CAAC;;MAED;MACAH,iBAAiB,CAACI,IAAI,CAAC,CAACC,CAAC,EAAEC,CAAC,KAAKA,CAAC,CAACC,aAAa,GAAGF,CAAC,CAACE,aAAa,CAAC;MAEnEzC,SAAS,CAACkC,iBAAiB,CAAC;MAC5BF,OAAO,CAACU,GAAG,CAAC,YAAYR,iBAAiB,CAACS,MAAM,qBAAqB,CAAC;IACxE,CAAC,CAAC,OAAOZ,KAAK,EAAE;MACdC,OAAO,CAACD,KAAK,CAAC,kDAAkD,EAAEA,KAAK,CAAC;MACxEX,gBAAgB,CAAC,uCAAuC,IAAIW,KAAK,CAACE,MAAM,IAAI,oBAAoB,CAAC,EAAE,OAAO,CAAC;IAC7G,CAAC,SAAS;MACRpB,gBAAgB,CAAC,KAAK,CAAC;IACzB;EACF,CAAC;;EAED;AACF;AACA;EACE,MAAM+B,gBAAgB,GAAIC,KAAK,IAAK;IAClC,MAAMC,MAAM,GAAGD,KAAK,CAACE,MAAM,CAACC,KAAK;IACjC5C,WAAW,CAAC;MACV,GAAGD,QAAQ;MACXE,OAAO,EAAEyC,MAAM;MACfxC,YAAY,EAAE,EAAE;MAChBC,SAAS,EAAE;IACb,CAAC,CAAC;;IAEF;IACA,MAAMsB,IAAI,GAAGhC,IAAI,CAACoD,IAAI,CAACC,CAAC,IAAIA,CAAC,CAAC7C,OAAO,KAAKyC,MAAM,CAAC;IACjD5C,eAAe,CAAC2B,IAAI,CAAC;;IAErB;IACApB,SAAS,CAAC,CAAC,CAAC,CAAC;EACf,CAAC;;EAED;AACF;AACA;EACE,MAAM0C,gBAAgB,GAAIN,KAAK,IAAK;IAClC,MAAM;MAAEO,IAAI;MAAEJ;IAAM,CAAC,GAAGH,KAAK,CAACE,MAAM;;IAEpC;IACA,IAAIK,IAAI,KAAK,cAAc,EAAE;MAC3B;MACA,MAAMC,cAAc,GAAGL,KAAK,CAACM,OAAO,CAAC,UAAU,EAAE,EAAE,CAAC;;MAEpD;MACA,MAAMC,KAAK,GAAGF,cAAc,CAACG,KAAK,CAAC,GAAG,CAAC;MACvC,MAAMC,UAAU,GAAGF,KAAK,CAACZ,MAAM,GAAG,CAAC,GAC/BY,KAAK,CAAC,CAAC,CAAC,GAAG,GAAG,GAAGA,KAAK,CAACG,KAAK,CAAC,CAAC,CAAC,CAACC,IAAI,CAAC,EAAE,CAAC,GACxCN,cAAc;MAElBjD,WAAW,CAAC;QACV,GAAGD,QAAQ;QACX,CAACiD,IAAI,GAAGK;MACV,CAAC,CAAC;IACJ,CAAC,MAAM;MACLrD,WAAW,CAAC;QACV,GAAGD,QAAQ;QACX,CAACiD,IAAI,GAAGJ;MACV,CAAC,CAAC;IACJ;;IAEA;IACA,IAAIxC,MAAM,CAAC4C,IAAI,CAAC,EAAE;MAChB3C,SAAS,CAAC;QACR,GAAGD,MAAM;QACT,CAAC4C,IAAI,GAAG;MACV,CAAC,CAAC;IACJ;EACF,CAAC;;EAED;AACF;AACA;EACE,MAAMQ,YAAY,GAAGA,CAAA,KAAM;IACzB,MAAMC,SAAS,GAAG,CAAC,CAAC;;IAEpB;IACA,IAAI,CAAC1D,QAAQ,CAACE,OAAO,EAAE;MACrBwD,SAAS,CAACxD,OAAO,GAAG,mBAAmB;IACzC;;IAEA;IACA,IAAI,CAACF,QAAQ,CAACG,YAAY,EAAE;MAC1BuD,SAAS,CAACvD,YAAY,GAAG,0BAA0B;IACrD,CAAC,MAAM;MACL,MAAMwD,WAAW,GAAGC,UAAU,CAAC5D,QAAQ,CAACG,YAAY,CAAC;MAErD,IAAI0D,KAAK,CAACF,WAAW,CAAC,EAAE;QACtBD,SAAS,CAACvD,YAAY,GAAG,wCAAwC;MACnE,CAAC,MAAM,IAAIwD,WAAW,GAAG,CAAC,EAAE;QAC1BD,SAAS,CAACvD,YAAY,GAAG,4CAA4C;MACvE;;MAEA;MACA,IAAIL,YAAY,IAAI6D,WAAW,GAAGC,UAAU,CAAC9D,YAAY,CAACgE,aAAa,IAAI,CAAC,CAAC,EAAE;QAC7E;QACAjC,OAAO,CAACkC,IAAI,CAAC,mBAAmBJ,WAAW,+BAA+B7D,YAAY,CAACgE,aAAa,GAAG,CAAC;MAC1G;;MAEA;MACA,IAAI9D,QAAQ,CAACI,SAAS,IAAIJ,QAAQ,CAACI,SAAS,KAAK,cAAc,EAAE;QAC/D,MAAM4D,MAAM,GAAGpE,MAAM,CAACkD,IAAI,CAACT,CAAC,IAAIA,CAAC,CAACjC,SAAS,KAAKJ,QAAQ,CAACI,SAAS,CAAC;QACnE,IAAI4D,MAAM,IAAIL,WAAW,GAAGC,UAAU,CAACI,MAAM,CAAC1B,aAAa,IAAI,CAAC,CAAC,EAAE;UACjE;UACAT,OAAO,CAACkC,IAAI,CAAC,mBAAmBJ,WAAW,4CAA4CK,MAAM,CAAC1B,aAAa,GAAG,CAAC;QACjH;MACF;IACF;;IAEA;IACA,IAAItC,QAAQ,CAACG,YAAY,IAAIyD,UAAU,CAAC5D,QAAQ,CAACG,YAAY,CAAC,GAAG,CAAC,IAAI,CAACH,QAAQ,CAACI,SAAS,EAAE;MACzFsD,SAAS,CAACtD,SAAS,GAAG,uCAAuC;IAC/D;IAEAE,SAAS,CAACoD,SAAS,CAAC;IACpB,OAAOO,MAAM,CAACC,IAAI,CAACR,SAAS,CAAC,CAAClB,MAAM,KAAK,CAAC;EAC5C,CAAC;;EAED;AACF;AACA;EACE,MAAM2B,YAAY,GAAG,MAAOzB,KAAK,IAAK;IACpCA,KAAK,CAAC0B,cAAc,CAAC,CAAC;;IAEtB;IACA,IAAI,CAACX,YAAY,CAAC,CAAC,EAAE;MACnB;IACF;;IAEA;IACA,MAAM;MAAEvD,OAAO;MAAEC,YAAY;MAAEC;IAAU,CAAC,GAAGJ,QAAQ;IACrD,MAAM2D,WAAW,GAAGC,UAAU,CAACzD,YAAY,CAAC;;IAE5C;IACA,IAAIkE,cAAc,GAAG,EAAE;;IAEvB;IACA,IAAIvE,YAAY,IAAI6D,WAAW,GAAGC,UAAU,CAAC9D,YAAY,CAACgE,aAAa,IAAI,CAAC,CAAC,EAAE;MAC7EO,cAAc,IAAI,mBAAmBV,WAAW,+BAA+B7D,YAAY,CAACgE,aAAa,MAAM;IACjH;;IAEA;IACA,IAAI1D,SAAS,IAAIA,SAAS,KAAK,cAAc,EAAE;MAC7C,MAAM4D,MAAM,GAAGpE,MAAM,CAACkD,IAAI,CAACT,CAAC,IAAIA,CAAC,CAACjC,SAAS,KAAKA,SAAS,CAAC;MAC1D,IAAI4D,MAAM,IAAIL,WAAW,GAAGC,UAAU,CAACI,MAAM,CAAC1B,aAAa,IAAI,CAAC,CAAC,EAAE;QACjE+B,cAAc,IAAI,mBAAmBV,WAAW,4CAA4CK,MAAM,CAAC1B,aAAa,MAAM;MACxH;IACF;;IAEA;IACA,IAAI+B,cAAc,EAAE;MAClBvD,uBAAuB,CAACuD,cAAc,GAAG,6BAA6B,CAAC;MACvErD,sBAAsB,CAAC,MAAM,MAAMsD,iBAAiB,CAACpE,OAAO,EAAEyD,WAAW,EAAEvD,SAAS,CAAC,CAAC;MACtFQ,oBAAoB,CAAC,IAAI,CAAC;MAC1B;IACF;;IAEA;IACA,MAAM0D,iBAAiB,CAACpE,OAAO,EAAEyD,WAAW,EAAEvD,SAAS,CAAC;EAC1D,CAAC;;EAED;AACF;AACA;EACE,MAAMkE,iBAAiB,GAAG,MAAAA,CAAOpE,OAAO,EAAEyD,WAAW,EAAEvD,SAAS,KAAK;IACnE,IAAI;MACFI,UAAU,CAAC,IAAI,CAAC;;MAEhB;MACAI,oBAAoB,CAAC,KAAK,CAAC;;MAE3B;MACA,MAAM2D,QAAQ,GAAG,MAAM1F,iBAAiB,CAAC2F,iBAAiB,CACxDrF,UAAU,EACVe,OAAO,EACPyD,WAAW,EACXvD,SACF,CAAC;;MAED;MACAqE,eAAe,CAAC,sCAAsC,EAAE;QAAEC,OAAO,EAAE;MAAU,CAAC,CAAC;;MAE/E;MACAzE,WAAW,CAAC;QACVC,OAAO,EAAE,EAAE;QACXC,YAAY,EAAE,EAAE;QAChBC,SAAS,EAAE;MACb,CAAC,CAAC;MACFL,eAAe,CAAC,IAAI,CAAC;;MAErB;MACA,MAAMqB,QAAQ,CAAC,CAAC;IAClB,CAAC,CAAC,OAAOQ,KAAK,EAAE;MACdC,OAAO,CAACD,KAAK,CAAC,8CAA8C,EAAEA,KAAK,CAAC;MACpE6C,eAAe,CAAC,UAAU,IAAI7C,KAAK,CAACE,MAAM,IAAI,oBAAoB,CAAC,EAAE;QAAE4C,OAAO,EAAE;MAAQ,CAAC,CAAC;IAC5F,CAAC,SAAS;MACRlE,UAAU,CAAC,KAAK,CAAC;IACnB;EACF,CAAC;;EAED;AACF;AACA;EACE,MAAMmE,wBAAwB,GAAGA,CAAA,KAAM;IACrC/D,oBAAoB,CAAC,KAAK,CAAC;IAC3BI,sBAAsB,CAAC,IAAI,CAAC;EAC9B,CAAC;;EAED;AACF;AACA;EACE,MAAM4D,0BAA0B,GAAGA,CAAA,KAAM;IACvC,IAAI7D,mBAAmB,EAAE;MACvBA,mBAAmB,CAAC,CAAC;IACvB;EACF,CAAC;EAED,oBACE/B,OAAA,CAAC/B,GAAG;IAAC4H,EAAE,EAAE;MAAEC,CAAC,EAAE;IAAE,CAAE;IAAAC,QAAA,gBAChB/F,OAAA,CAACd,UAAU;MAACwG,OAAO,EAAC,IAAI;MAACM,YAAY;MAAAD,QAAA,EAAC;IAEtC;MAAAE,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAY,CAAC,eAEbpG,OAAA,CAAC7B,IAAI;MAAC0H,EAAE,EAAE;QAAEQ,EAAE,EAAE;MAAE,CAAE;MAAAN,QAAA,eAClB/F,OAAA,CAAC5B,WAAW;QAAA2H,QAAA,eACV/F,OAAA;UAAMsG,QAAQ,EAAEnB,YAAa;UAAAY,QAAA,eAC3B/F,OAAA,CAACnB,IAAI;YAAC0H,SAAS;YAACC,OAAO,EAAE,CAAE;YAAAT,QAAA,gBAEzB/F,OAAA,CAACnB,IAAI;cAAC4H,IAAI;cAACC,EAAE,EAAE,EAAG;cAACC,EAAE,EAAE,CAAE;cAAAZ,QAAA,eACvB/F,OAAA,CAACrB,WAAW;gBAACiI,SAAS;gBAAChE,KAAK,EAAE,CAAC,CAACvB,MAAM,CAACH,OAAQ;gBAAA6E,QAAA,gBAC7C/F,OAAA,CAAClB,UAAU;kBAAC+H,EAAE,EAAC,mBAAmB;kBAAAd,QAAA,EAAC;gBAAI;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAY,CAAC,eACpDpG,OAAA,CAAChB,MAAM;kBACL8H,OAAO,EAAC,mBAAmB;kBAC3BD,EAAE,EAAC,aAAa;kBAChB5C,IAAI,EAAC,SAAS;kBACdJ,KAAK,EAAE7C,QAAQ,CAACE,OAAQ;kBACxB6F,QAAQ,EAAEtD,gBAAiB;kBAC3BuD,QAAQ,EAAEzF,OAAQ;kBAAAwE,QAAA,gBAElB/F,OAAA,CAACjB,QAAQ;oBAAC8E,KAAK,EAAC,EAAE;oBAAAkC,QAAA,eAChB/F,OAAA;sBAAA+F,QAAA,EAAI;oBAAiB;sBAAAE,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAI;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAClB,CAAC,EACV1F,IAAI,CAACuG,GAAG,CAAEvE,IAAI,iBACb1C,OAAA,CAACjB,QAAQ;oBAAoB8E,KAAK,EAAEnB,IAAI,CAACxB,OAAQ;oBAAA6E,QAAA,GAC9CrD,IAAI,CAACxB,OAAO,EAAC,KAAG,EAACwB,IAAI,CAACO,SAAS,EAAC,KAAG,EAACP,IAAI,CAACQ,OAAO,EAAC,KAAG,EAACR,IAAI,CAACwE,OAAO;kBAAA,GADrDxE,IAAI,CAACxB,OAAO;oBAAA+E,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAEjB,CACX,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACI,CAAC,EACR/E,MAAM,CAACH,OAAO,iBAAIlB,OAAA,CAACpB,cAAc;kBAAAmH,QAAA,EAAE1E,MAAM,CAACH;gBAAO;kBAAA+E,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAiB,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACzD;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACV,CAAC,eAGPpG,OAAA,CAACnB,IAAI;cAAC4H,IAAI;cAACC,EAAE,EAAE,EAAG;cAACC,EAAE,EAAE,CAAE;cAAAZ,QAAA,eACvB/F,OAAA,CAACf,SAAS;gBACR2H,SAAS;gBACTO,KAAK,EAAC,cAAc;gBACpBlD,IAAI,EAAC,cAAc;gBACnBJ,KAAK,EAAE7C,QAAQ,CAACG,YAAa;gBAC7B4F,QAAQ,EAAE/C,gBAAiB;gBAC3BpB,KAAK,EAAE,CAAC,CAACvB,MAAM,CAACF,YAAa;gBAC7BiG,UAAU,EAAE/F,MAAM,CAACF,YAAa;gBAChC6F,QAAQ,EAAE,CAAClG,YAAY,IAAIS,OAAQ;gBACnC8F,UAAU,EAAE;kBACVC,YAAY,eAAEtH,OAAA,CAACd,UAAU;oBAACwG,OAAO,EAAC,OAAO;oBAAAK,QAAA,EAAC;kBAAC;oBAAAE,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAY;gBACzD;cAAE;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACE,CAAC,eAGPpG,OAAA,CAACnB,IAAI;cAAC4H,IAAI;cAACC,EAAE,EAAE,EAAG;cAAAX,QAAA,eAChB/F,OAAA,CAACrB,WAAW;gBAACiI,SAAS;gBAAChE,KAAK,EAAE,CAAC,CAACvB,MAAM,CAACD,SAAU;gBAAA2E,QAAA,gBAC/C/F,OAAA,CAAClB,UAAU;kBAAC+H,EAAE,EAAC,qBAAqB;kBAAAd,QAAA,EAAC;gBAAM;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAY,CAAC,eACxDpG,OAAA,CAAChB,MAAM;kBACL8H,OAAO,EAAC,qBAAqB;kBAC7BD,EAAE,EAAC,eAAe;kBAClB5C,IAAI,EAAC,WAAW;kBAChBJ,KAAK,EAAE7C,QAAQ,CAACI,SAAU;kBAC1B2F,QAAQ,EAAE/C,gBAAiB;kBAC3BgD,QAAQ,EAAE,CAAClG,YAAY,IAAIW,aAAa,IAAIF,OAAQ;kBAAAwE,QAAA,gBAEpD/F,OAAA,CAACjB,QAAQ;oBAAC8E,KAAK,EAAC,EAAE;oBAAAkC,QAAA,eAChB/F,OAAA;sBAAA+F,QAAA,EAAI;oBAAoB;sBAAAE,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAI;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACrB,CAAC,eACXpG,OAAA,CAACjB,QAAQ;oBAAC8E,KAAK,EAAC,cAAc;oBAAAkC,QAAA,EAAC;kBAE/B;oBAAAE,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAU,CAAC,EACVxF,MAAM,CAACqG,GAAG,CAAEjC,MAAM,iBACjBhF,OAAA,CAACjB,QAAQ;oBAEP8E,KAAK,EAAEmB,MAAM,CAAC5D,SAAU;oBACxByE,EAAE,EAAE;sBACF0B,OAAO,EAAEzG,YAAY,IACnB0G,MAAM,CAACxC,MAAM,CAAC/B,SAAS,IAAI,EAAE,CAAC,CAACwE,IAAI,CAAC,CAAC,KAAKD,MAAM,CAAC1G,YAAY,CAACmC,SAAS,IAAI,EAAE,CAAC,CAACwE,IAAI,CAAC,CAAC,IACrFD,MAAM,CAACxC,MAAM,CAAC9B,OAAO,IAAI,GAAG,CAAC,CAACuE,IAAI,CAAC,CAAC,KAAKD,MAAM,CAAC1G,YAAY,CAACoC,OAAO,IAAI,GAAG,CAAC,CAACuE,IAAI,CAAC,CAAC,GACnF,yBAAyB,GAAG;oBAChC,CAAE;oBAAA1B,QAAA,GAEDf,MAAM,CAAC5D,SAAS,EAAC,KAAG,EAAC4D,MAAM,CAAC/B,SAAS,EAAC,KAAG,EAAC+B,MAAM,CAAC9B,OAAO,EAAC,KAAG,EAAC8B,MAAM,CAAC1B,aAAa,EAAC,WACnF,EAACxC,YAAY,IACX0G,MAAM,CAACxC,MAAM,CAAC/B,SAAS,IAAI,EAAE,CAAC,CAACwE,IAAI,CAAC,CAAC,KAAKD,MAAM,CAAC1G,YAAY,CAACmC,SAAS,IAAI,EAAE,CAAC,CAACwE,IAAI,CAAC,CAAC,IACrFD,MAAM,CAACxC,MAAM,CAAC9B,OAAO,IAAI,GAAG,CAAC,CAACuE,IAAI,CAAC,CAAC,KAAKD,MAAM,CAAC1G,YAAY,CAACoC,OAAO,IAAI,GAAG,CAAC,CAACuE,IAAI,CAAC,CAAC,iBACjFzH,OAAA,CAACN,IAAI;sBACHyH,KAAK,EAAC,aAAa;sBACnBO,KAAK,EAAC,SAAS;sBACfC,IAAI,EAAC,OAAO;sBACZ9B,EAAE,EAAE;wBAAE+B,EAAE,EAAE;sBAAE;oBAAE;sBAAA3B,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACf,CACF;kBAAA,GAnBEpB,MAAM,CAAC5D,SAAS;oBAAA6E,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAqBb,CACX,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACI,CAAC,EACR/E,MAAM,CAACD,SAAS,iBAAIpB,OAAA,CAACpB,cAAc;kBAAAmH,QAAA,EAAE1E,MAAM,CAACD;gBAAS;kBAAA6E,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAiB,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC7D;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACV,CAAC,eAGPpG,OAAA,CAACnB,IAAI;cAAC4H,IAAI;cAACC,EAAE,EAAE,EAAG;cAAAX,QAAA,eAChB/F,OAAA,CAAC/B,GAAG;gBAAC4H,EAAE,EAAE;kBAAEgC,OAAO,EAAE,MAAM;kBAAEC,cAAc,EAAE,UAAU;kBAAEC,GAAG,EAAE;gBAAE,CAAE;gBAAAhC,QAAA,gBAC/D/F,OAAA,CAAC9B,MAAM;kBACLwH,OAAO,EAAC,UAAU;kBAClBsC,OAAO,EAAEA,CAAA,KAAM5H,QAAQ,CAAC,aAAaD,UAAU,OAAO,CAAE;kBACxD6G,QAAQ,EAAEzF,OAAQ;kBAAAwE,QAAA,EACnB;gBAED;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,eACTpG,OAAA,CAAC9B,MAAM;kBACL+J,IAAI,EAAC,QAAQ;kBACbvC,OAAO,EAAC,WAAW;kBACnBgC,KAAK,EAAC,SAAS;kBACfV,QAAQ,EAAEzF,OAAQ;kBAClB2G,SAAS,EAAE3G,OAAO,iBAAIvB,OAAA,CAAC3B,gBAAgB;oBAACsJ,IAAI,EAAE,EAAG;oBAACD,KAAK,EAAC;kBAAS;oBAAAzB,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE,CAAE;kBAAAL,QAAA,EACtE;gBAED;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACN;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACF,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACI;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACV,CAAC,EAGNtF,YAAY,iBACXd,OAAA,CAAC7B,IAAI;MAAA4H,QAAA,eACH/F,OAAA,CAAC5B,WAAW;QAAA2H,QAAA,gBACV/F,OAAA,CAACd,UAAU;UAACwG,OAAO,EAAC,IAAI;UAACM,YAAY;UAAAD,QAAA,EAAC;QAEtC;UAAAE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAY,CAAC,eACbpG,OAAA,CAACT,cAAc;UAAC4I,SAAS,EAAEhJ,KAAM;UAAA4G,QAAA,eAC/B/F,OAAA,CAACZ,KAAK;YAACuI,IAAI,EAAC,OAAO;YAAA5B,QAAA,gBACjB/F,OAAA,CAACR,SAAS;cAAAuG,QAAA,eACR/F,OAAA,CAACP,QAAQ;gBAAAsG,QAAA,gBACP/F,OAAA,CAACV,SAAS;kBAAAyG,QAAA,EAAC;gBAAO;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAW,CAAC,eAC9BpG,OAAA,CAACV,SAAS;kBAAAyG,QAAA,EAAC;gBAAS;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAW,CAAC,eAChCpG,OAAA,CAACV,SAAS;kBAAAyG,QAAA,EAAC;gBAAU;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAW,CAAC,eACjCpG,OAAA,CAACV,SAAS;kBAAAyG,QAAA,EAAC;gBAAO;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAW,CAAC,eAC9BpG,OAAA,CAACV,SAAS;kBAAAyG,QAAA,EAAC;gBAAa;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAW,CAAC,eACpCpG,OAAA,CAACV,SAAS;kBAAAyG,QAAA,EAAC;gBAAK;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAW,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACpB;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACF,CAAC,eACZpG,OAAA,CAACX,SAAS;cAAA0G,QAAA,eACR/F,OAAA,CAACP,QAAQ;gBAAAsG,QAAA,gBACP/F,OAAA,CAACV,SAAS;kBAAAyG,QAAA,EAAEjF,YAAY,CAACI;gBAAO;kBAAA+E,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAY,CAAC,eAC7CpG,OAAA,CAACV,SAAS;kBAAAyG,QAAA,EAAEjF,YAAY,CAACmC;gBAAS;kBAAAgD,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAY,CAAC,eAC/CpG,OAAA,CAACV,SAAS;kBAAAyG,QAAA,EAAEjF,YAAY,CAACoC;gBAAO;kBAAA+C,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAY,CAAC,eAC7CpG,OAAA,CAACV,SAAS;kBAAAyG,QAAA,EAAEjF,YAAY,CAACoG;gBAAO;kBAAAjB,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAY,CAAC,eAC7CpG,OAAA,CAACV,SAAS;kBAAAyG,QAAA,GAAEjF,YAAY,CAACgE,aAAa,EAAC,GAAC;gBAAA;kBAAAmB,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAW,CAAC,eACpDpG,OAAA,CAACV,SAAS;kBAAAyG,QAAA,EAAEjF,YAAY,CAAC6B;gBAAmB;kBAAAsD,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAY,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACjD;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACF,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACP;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACM,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACN;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACV,CACP,eAGDpG,OAAA,CAAC1B,MAAM;MACLiC,IAAI,EAAEoB,iBAAkB;MACxByG,OAAO,EAAEzC,wBAAyB;MAAAI,QAAA,gBAElC/F,OAAA,CAACtB,WAAW;QAAAqH,QAAA,EAAC;MAAmB;QAAAE,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAa,CAAC,eAC9CpG,OAAA,CAACxB,aAAa;QAAAuH,QAAA,eACZ/F,OAAA,CAACvB,iBAAiB;UAAAsH,QAAA,EACflE;QAAoB;UAAAoE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACJ;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACP,CAAC,eAChBpG,OAAA,CAACzB,aAAa;QAAAwH,QAAA,gBACZ/F,OAAA,CAAC9B,MAAM;UAAC8J,OAAO,EAAErC,wBAAyB;UAAC+B,KAAK,EAAC,SAAS;UAAA3B,QAAA,EAAC;QAE3D;UAAAE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC,eACTpG,OAAA,CAAC9B,MAAM;UAAC8J,OAAO,EAAEpC,0BAA2B;UAAC8B,KAAK,EAAC,SAAS;UAACW,SAAS;UAAAtC,QAAA,EAAC;QAEvE;UAAAE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACI,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACV,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACN,CAAC;AAEV,CAAC;AAAClG,EAAA,CApeID,eAAe;EAAA,QAEIlC,SAAS,EACfC,WAAW;AAAA;AAAAsK,EAAA,GAHxBrI,eAAe;AAserB,eAAeA,eAAe;AAAC,IAAAqI,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}