{"ast": null, "code": "const formatDistanceLocale = {\n  lessThanXSeconds: {\n    regular: {\n      one: \"1 секундтан аз\",\n      singularNominative: \"{{count}} секундтан аз\",\n      singularGenitive: \"{{count}} секундтан аз\",\n      pluralGenitive: \"{{count}} секундтан аз\"\n    },\n    future: {\n      one: \"бір секундтан кейін\",\n      singularNominative: \"{{count}} секундтан кейін\",\n      singularGenitive: \"{{count}} секундтан кейін\",\n      pluralGenitive: \"{{count}} секундтан кейін\"\n    }\n  },\n  xSeconds: {\n    regular: {\n      singularNominative: \"{{count}} секунд\",\n      singularGenitive: \"{{count}} секунд\",\n      pluralGenitive: \"{{count}} секунд\"\n    },\n    past: {\n      singularNominative: \"{{count}} секунд бұрын\",\n      singularGenitive: \"{{count}} секунд бұрын\",\n      pluralGenitive: \"{{count}} секунд бұрын\"\n    },\n    future: {\n      singularNominative: \"{{count}} секундтан кейін\",\n      singularGenitive: \"{{count}} секундтан кейін\",\n      pluralGenitive: \"{{count}} секундтан кейін\"\n    }\n  },\n  halfAMinute: options => {\n    if (options?.addSuffix) {\n      if (options.comparison && options.comparison > 0) {\n        return \"жарты минут ішінде\";\n      } else {\n        return \"жарты минут бұрын\";\n      }\n    }\n    return \"жарты минут\";\n  },\n  lessThanXMinutes: {\n    regular: {\n      one: \"1 минуттан аз\",\n      singularNominative: \"{{count}} минуттан аз\",\n      singularGenitive: \"{{count}} минуттан аз\",\n      pluralGenitive: \"{{count}} минуттан аз\"\n    },\n    future: {\n      one: \"минуттан кем \",\n      singularNominative: \"{{count}} минуттан кем\",\n      singularGenitive: \"{{count}} минуттан кем\",\n      pluralGenitive: \"{{count}} минуттан кем\"\n    }\n  },\n  xMinutes: {\n    regular: {\n      singularNominative: \"{{count}} минут\",\n      singularGenitive: \"{{count}} минут\",\n      pluralGenitive: \"{{count}} минут\"\n    },\n    past: {\n      singularNominative: \"{{count}} минут бұрын\",\n      singularGenitive: \"{{count}} минут бұрын\",\n      pluralGenitive: \"{{count}} минут бұрын\"\n    },\n    future: {\n      singularNominative: \"{{count}} минуттан кейін\",\n      singularGenitive: \"{{count}} минуттан кейін\",\n      pluralGenitive: \"{{count}} минуттан кейін\"\n    }\n  },\n  aboutXHours: {\n    regular: {\n      singularNominative: \"шамамен {{count}} сағат\",\n      singularGenitive: \"шамамен {{count}} сағат\",\n      pluralGenitive: \"шамамен {{count}} сағат\"\n    },\n    future: {\n      singularNominative: \"шамамен {{count}} сағаттан кейін\",\n      singularGenitive: \"шамамен {{count}} сағаттан кейін\",\n      pluralGenitive: \"шамамен {{count}} сағаттан кейін\"\n    }\n  },\n  xHours: {\n    regular: {\n      singularNominative: \"{{count}} сағат\",\n      singularGenitive: \"{{count}} сағат\",\n      pluralGenitive: \"{{count}} сағат\"\n    }\n  },\n  xDays: {\n    regular: {\n      singularNominative: \"{{count}} күн\",\n      singularGenitive: \"{{count}} күн\",\n      pluralGenitive: \"{{count}} күн\"\n    },\n    future: {\n      singularNominative: \"{{count}} күннен кейін\",\n      singularGenitive: \"{{count}} күннен кейін\",\n      pluralGenitive: \"{{count}} күннен кейін\"\n    }\n  },\n  aboutXWeeks: {\n    type: \"weeks\",\n    one: \"шамамен 1 апта\",\n    other: \"шамамен {{count}} апта\"\n  },\n  xWeeks: {\n    type: \"weeks\",\n    one: \"1 апта\",\n    other: \"{{count}} апта\"\n  },\n  aboutXMonths: {\n    regular: {\n      singularNominative: \"шамамен {{count}} ай\",\n      singularGenitive: \"шамамен {{count}} ай\",\n      pluralGenitive: \"шамамен {{count}} ай\"\n    },\n    future: {\n      singularNominative: \"шамамен {{count}} айдан кейін\",\n      singularGenitive: \"шамамен {{count}} айдан кейін\",\n      pluralGenitive: \"шамамен {{count}} айдан кейін\"\n    }\n  },\n  xMonths: {\n    regular: {\n      singularNominative: \"{{count}} ай\",\n      singularGenitive: \"{{count}} ай\",\n      pluralGenitive: \"{{count}} ай\"\n    }\n  },\n  aboutXYears: {\n    regular: {\n      singularNominative: \"шамамен {{count}} жыл\",\n      singularGenitive: \"шамамен {{count}} жыл\",\n      pluralGenitive: \"шамамен {{count}} жыл\"\n    },\n    future: {\n      singularNominative: \"шамамен {{count}} жылдан кейін\",\n      singularGenitive: \"шамамен {{count}} жылдан кейін\",\n      pluralGenitive: \"шамамен {{count}} жылдан кейін\"\n    }\n  },\n  xYears: {\n    regular: {\n      singularNominative: \"{{count}} жыл\",\n      singularGenitive: \"{{count}} жыл\",\n      pluralGenitive: \"{{count}} жыл\"\n    },\n    future: {\n      singularNominative: \"{{count}} жылдан кейін\",\n      singularGenitive: \"{{count}} жылдан кейін\",\n      pluralGenitive: \"{{count}} жылдан кейін\"\n    }\n  },\n  overXYears: {\n    regular: {\n      singularNominative: \"{{count}} жылдан астам\",\n      singularGenitive: \"{{count}} жылдан астам\",\n      pluralGenitive: \"{{count}} жылдан астам\"\n    },\n    future: {\n      singularNominative: \"{{count}} жылдан астам\",\n      singularGenitive: \"{{count}} жылдан астам\",\n      pluralGenitive: \"{{count}} жылдан астам\"\n    }\n  },\n  almostXYears: {\n    regular: {\n      singularNominative: \"{{count}} жылға жақын\",\n      singularGenitive: \"{{count}} жылға жақын\",\n      pluralGenitive: \"{{count}} жылға жақын\"\n    },\n    future: {\n      singularNominative: \"{{count}} жылдан кейін\",\n      singularGenitive: \"{{count}} жылдан кейін\",\n      pluralGenitive: \"{{count}} жылдан кейін\"\n    }\n  }\n};\nfunction declension(scheme, count) {\n  // scheme for count=1 exists\n  if (scheme.one && count === 1) return scheme.one;\n  const rem10 = count % 10;\n  const rem100 = count % 100;\n\n  // 1, 21, 31, ...\n  if (rem10 === 1 && rem100 !== 11) {\n    return scheme.singularNominative.replace(\"{{count}}\", String(count));\n\n    // 2, 3, 4, 22, 23, 24, 32 ...\n  } else if (rem10 >= 2 && rem10 <= 4 && (rem100 < 10 || rem100 > 20)) {\n    return scheme.singularGenitive.replace(\"{{count}}\", String(count));\n\n    // 5, 6, 7, 8, 9, 10, 11, ...\n  } else {\n    return scheme.pluralGenitive.replace(\"{{count}}\", String(count));\n  }\n}\nexport const formatDistance = (token, count, options) => {\n  const tokenValue = formatDistanceLocale[token];\n  if (typeof tokenValue === \"function\") return tokenValue(options);\n  if (tokenValue.type === \"weeks\") {\n    return count === 1 ? tokenValue.one : tokenValue.other.replace(\"{{count}}\", String(count));\n  }\n  if (options?.addSuffix) {\n    if (options.comparison && options.comparison > 0) {\n      if (tokenValue.future) {\n        return declension(tokenValue.future, count);\n      } else {\n        return declension(tokenValue.regular, count) + \" кейін\";\n      }\n    } else {\n      if (tokenValue.past) {\n        return declension(tokenValue.past, count);\n      } else {\n        return declension(tokenValue.regular, count) + \" бұрын\";\n      }\n    }\n  } else {\n    return declension(tokenValue.regular, count);\n  }\n};", "map": {"version": 3, "names": ["formatDistanceLocale", "lessThanXSeconds", "regular", "one", "singularNominative", "singularGenitive", "pluralGenitive", "future", "xSeconds", "past", "halfAMinute", "options", "addSuffix", "comparison", "lessThanXMinutes", "xMinutes", "aboutXHours", "xHours", "xDays", "aboutXWeeks", "type", "other", "xWeeks", "aboutXMonths", "xMonths", "aboutXYears", "xYears", "overXYears", "almostXYears", "declension", "scheme", "count", "rem10", "rem100", "replace", "String", "formatDistance", "token", "tokenValue"], "sources": ["C:/CMS/webapp/frontend/node_modules/date-fns/locale/kk/_lib/formatDistance.js"], "sourcesContent": ["const formatDistanceLocale = {\n  lessThanXSeconds: {\n    regular: {\n      one: \"1 секундтан аз\",\n      singularNominative: \"{{count}} секундтан аз\",\n      singularGenitive: \"{{count}} секундтан аз\",\n      pluralGenitive: \"{{count}} секундтан аз\",\n    },\n    future: {\n      one: \"бір секундтан кейін\",\n      singularNominative: \"{{count}} секундтан кейін\",\n      singularGenitive: \"{{count}} секундтан кейін\",\n      pluralGenitive: \"{{count}} секундтан кейін\",\n    },\n  },\n\n  xSeconds: {\n    regular: {\n      singularNominative: \"{{count}} секунд\",\n      singularGenitive: \"{{count}} секунд\",\n      pluralGenitive: \"{{count}} секунд\",\n    },\n    past: {\n      singularNominative: \"{{count}} секунд бұрын\",\n      singularGenitive: \"{{count}} секунд бұрын\",\n      pluralGenitive: \"{{count}} секунд бұрын\",\n    },\n    future: {\n      singularNominative: \"{{count}} секундтан кейін\",\n      singularGenitive: \"{{count}} секундтан кейін\",\n      pluralGenitive: \"{{count}} секундтан кейін\",\n    },\n  },\n\n  halfAMinute: (options) => {\n    if (options?.addSuffix) {\n      if (options.comparison && options.comparison > 0) {\n        return \"жарты минут ішінде\";\n      } else {\n        return \"жарты минут бұрын\";\n      }\n    }\n\n    return \"жарты минут\";\n  },\n\n  lessThanXMinutes: {\n    regular: {\n      one: \"1 минуттан аз\",\n      singularNominative: \"{{count}} минуттан аз\",\n      singularGenitive: \"{{count}} минуттан аз\",\n      pluralGenitive: \"{{count}} минуттан аз\",\n    },\n    future: {\n      one: \"минуттан кем \",\n      singularNominative: \"{{count}} минуттан кем\",\n      singularGenitive: \"{{count}} минуттан кем\",\n      pluralGenitive: \"{{count}} минуттан кем\",\n    },\n  },\n\n  xMinutes: {\n    regular: {\n      singularNominative: \"{{count}} минут\",\n      singularGenitive: \"{{count}} минут\",\n      pluralGenitive: \"{{count}} минут\",\n    },\n    past: {\n      singularNominative: \"{{count}} минут бұрын\",\n      singularGenitive: \"{{count}} минут бұрын\",\n      pluralGenitive: \"{{count}} минут бұрын\",\n    },\n    future: {\n      singularNominative: \"{{count}} минуттан кейін\",\n      singularGenitive: \"{{count}} минуттан кейін\",\n      pluralGenitive: \"{{count}} минуттан кейін\",\n    },\n  },\n\n  aboutXHours: {\n    regular: {\n      singularNominative: \"шамамен {{count}} сағат\",\n      singularGenitive: \"шамамен {{count}} сағат\",\n      pluralGenitive: \"шамамен {{count}} сағат\",\n    },\n    future: {\n      singularNominative: \"шамамен {{count}} сағаттан кейін\",\n      singularGenitive: \"шамамен {{count}} сағаттан кейін\",\n      pluralGenitive: \"шамамен {{count}} сағаттан кейін\",\n    },\n  },\n\n  xHours: {\n    regular: {\n      singularNominative: \"{{count}} сағат\",\n      singularGenitive: \"{{count}} сағат\",\n      pluralGenitive: \"{{count}} сағат\",\n    },\n  },\n\n  xDays: {\n    regular: {\n      singularNominative: \"{{count}} күн\",\n      singularGenitive: \"{{count}} күн\",\n      pluralGenitive: \"{{count}} күн\",\n    },\n    future: {\n      singularNominative: \"{{count}} күннен кейін\",\n      singularGenitive: \"{{count}} күннен кейін\",\n      pluralGenitive: \"{{count}} күннен кейін\",\n    },\n  },\n\n  aboutXWeeks: {\n    type: \"weeks\",\n    one: \"шамамен 1 апта\",\n    other: \"шамамен {{count}} апта\",\n  },\n\n  xWeeks: {\n    type: \"weeks\",\n    one: \"1 апта\",\n    other: \"{{count}} апта\",\n  },\n\n  aboutXMonths: {\n    regular: {\n      singularNominative: \"шамамен {{count}} ай\",\n      singularGenitive: \"шамамен {{count}} ай\",\n      pluralGenitive: \"шамамен {{count}} ай\",\n    },\n    future: {\n      singularNominative: \"шамамен {{count}} айдан кейін\",\n      singularGenitive: \"шамамен {{count}} айдан кейін\",\n      pluralGenitive: \"шамамен {{count}} айдан кейін\",\n    },\n  },\n\n  xMonths: {\n    regular: {\n      singularNominative: \"{{count}} ай\",\n      singularGenitive: \"{{count}} ай\",\n      pluralGenitive: \"{{count}} ай\",\n    },\n  },\n\n  aboutXYears: {\n    regular: {\n      singularNominative: \"шамамен {{count}} жыл\",\n      singularGenitive: \"шамамен {{count}} жыл\",\n      pluralGenitive: \"шамамен {{count}} жыл\",\n    },\n    future: {\n      singularNominative: \"шамамен {{count}} жылдан кейін\",\n      singularGenitive: \"шамамен {{count}} жылдан кейін\",\n      pluralGenitive: \"шамамен {{count}} жылдан кейін\",\n    },\n  },\n\n  xYears: {\n    regular: {\n      singularNominative: \"{{count}} жыл\",\n      singularGenitive: \"{{count}} жыл\",\n      pluralGenitive: \"{{count}} жыл\",\n    },\n    future: {\n      singularNominative: \"{{count}} жылдан кейін\",\n      singularGenitive: \"{{count}} жылдан кейін\",\n      pluralGenitive: \"{{count}} жылдан кейін\",\n    },\n  },\n\n  overXYears: {\n    regular: {\n      singularNominative: \"{{count}} жылдан астам\",\n      singularGenitive: \"{{count}} жылдан астам\",\n      pluralGenitive: \"{{count}} жылдан астам\",\n    },\n    future: {\n      singularNominative: \"{{count}} жылдан астам\",\n      singularGenitive: \"{{count}} жылдан астам\",\n      pluralGenitive: \"{{count}} жылдан астам\",\n    },\n  },\n\n  almostXYears: {\n    regular: {\n      singularNominative: \"{{count}} жылға жақын\",\n      singularGenitive: \"{{count}} жылға жақын\",\n      pluralGenitive: \"{{count}} жылға жақын\",\n    },\n    future: {\n      singularNominative: \"{{count}} жылдан кейін\",\n      singularGenitive: \"{{count}} жылдан кейін\",\n      pluralGenitive: \"{{count}} жылдан кейін\",\n    },\n  },\n};\n\nfunction declension(scheme, count) {\n  // scheme for count=1 exists\n  if (scheme.one && count === 1) return scheme.one;\n\n  const rem10 = count % 10;\n  const rem100 = count % 100;\n\n  // 1, 21, 31, ...\n  if (rem10 === 1 && rem100 !== 11) {\n    return scheme.singularNominative.replace(\"{{count}}\", String(count));\n\n    // 2, 3, 4, 22, 23, 24, 32 ...\n  } else if (rem10 >= 2 && rem10 <= 4 && (rem100 < 10 || rem100 > 20)) {\n    return scheme.singularGenitive.replace(\"{{count}}\", String(count));\n\n    // 5, 6, 7, 8, 9, 10, 11, ...\n  } else {\n    return scheme.pluralGenitive.replace(\"{{count}}\", String(count));\n  }\n}\n\nexport const formatDistance = (token, count, options) => {\n  const tokenValue = formatDistanceLocale[token];\n\n  if (typeof tokenValue === \"function\") return tokenValue(options);\n\n  if (tokenValue.type === \"weeks\") {\n    return count === 1\n      ? tokenValue.one\n      : tokenValue.other.replace(\"{{count}}\", String(count));\n  }\n\n  if (options?.addSuffix) {\n    if (options.comparison && options.comparison > 0) {\n      if (tokenValue.future) {\n        return declension(tokenValue.future, count);\n      } else {\n        return declension(tokenValue.regular, count) + \" кейін\";\n      }\n    } else {\n      if (tokenValue.past) {\n        return declension(tokenValue.past, count);\n      } else {\n        return declension(tokenValue.regular, count) + \" бұрын\";\n      }\n    }\n  } else {\n    return declension(tokenValue.regular, count);\n  }\n};\n"], "mappings": "AAAA,MAAMA,oBAAoB,GAAG;EAC3BC,gBAAgB,EAAE;IAChBC,OAAO,EAAE;MACPC,GAAG,EAAE,gBAAgB;MACrBC,kBAAkB,EAAE,wBAAwB;MAC5CC,gBAAgB,EAAE,wBAAwB;MAC1CC,cAAc,EAAE;IAClB,CAAC;IACDC,MAAM,EAAE;MACNJ,GAAG,EAAE,qBAAqB;MAC1BC,kBAAkB,EAAE,2BAA2B;MAC/CC,gBAAgB,EAAE,2BAA2B;MAC7CC,cAAc,EAAE;IAClB;EACF,CAAC;EAEDE,QAAQ,EAAE;IACRN,OAAO,EAAE;MACPE,kBAAkB,EAAE,kBAAkB;MACtCC,gBAAgB,EAAE,kBAAkB;MACpCC,cAAc,EAAE;IAClB,CAAC;IACDG,IAAI,EAAE;MACJL,kBAAkB,EAAE,wBAAwB;MAC5CC,gBAAgB,EAAE,wBAAwB;MAC1CC,cAAc,EAAE;IAClB,CAAC;IACDC,MAAM,EAAE;MACNH,kBAAkB,EAAE,2BAA2B;MAC/CC,gBAAgB,EAAE,2BAA2B;MAC7CC,cAAc,EAAE;IAClB;EACF,CAAC;EAEDI,WAAW,EAAGC,OAAO,IAAK;IACxB,IAAIA,OAAO,EAAEC,SAAS,EAAE;MACtB,IAAID,OAAO,CAACE,UAAU,IAAIF,OAAO,CAACE,UAAU,GAAG,CAAC,EAAE;QAChD,OAAO,oBAAoB;MAC7B,CAAC,MAAM;QACL,OAAO,mBAAmB;MAC5B;IACF;IAEA,OAAO,aAAa;EACtB,CAAC;EAEDC,gBAAgB,EAAE;IAChBZ,OAAO,EAAE;MACPC,GAAG,EAAE,eAAe;MACpBC,kBAAkB,EAAE,uBAAuB;MAC3CC,gBAAgB,EAAE,uBAAuB;MACzCC,cAAc,EAAE;IAClB,CAAC;IACDC,MAAM,EAAE;MACNJ,GAAG,EAAE,eAAe;MACpBC,kBAAkB,EAAE,wBAAwB;MAC5CC,gBAAgB,EAAE,wBAAwB;MAC1CC,cAAc,EAAE;IAClB;EACF,CAAC;EAEDS,QAAQ,EAAE;IACRb,OAAO,EAAE;MACPE,kBAAkB,EAAE,iBAAiB;MACrCC,gBAAgB,EAAE,iBAAiB;MACnCC,cAAc,EAAE;IAClB,CAAC;IACDG,IAAI,EAAE;MACJL,kBAAkB,EAAE,uBAAuB;MAC3CC,gBAAgB,EAAE,uBAAuB;MACzCC,cAAc,EAAE;IAClB,CAAC;IACDC,MAAM,EAAE;MACNH,kBAAkB,EAAE,0BAA0B;MAC9CC,gBAAgB,EAAE,0BAA0B;MAC5CC,cAAc,EAAE;IAClB;EACF,CAAC;EAEDU,WAAW,EAAE;IACXd,OAAO,EAAE;MACPE,kBAAkB,EAAE,yBAAyB;MAC7CC,gBAAgB,EAAE,yBAAyB;MAC3CC,cAAc,EAAE;IAClB,CAAC;IACDC,MAAM,EAAE;MACNH,kBAAkB,EAAE,kCAAkC;MACtDC,gBAAgB,EAAE,kCAAkC;MACpDC,cAAc,EAAE;IAClB;EACF,CAAC;EAEDW,MAAM,EAAE;IACNf,OAAO,EAAE;MACPE,kBAAkB,EAAE,iBAAiB;MACrCC,gBAAgB,EAAE,iBAAiB;MACnCC,cAAc,EAAE;IAClB;EACF,CAAC;EAEDY,KAAK,EAAE;IACLhB,OAAO,EAAE;MACPE,kBAAkB,EAAE,eAAe;MACnCC,gBAAgB,EAAE,eAAe;MACjCC,cAAc,EAAE;IAClB,CAAC;IACDC,MAAM,EAAE;MACNH,kBAAkB,EAAE,wBAAwB;MAC5CC,gBAAgB,EAAE,wBAAwB;MAC1CC,cAAc,EAAE;IAClB;EACF,CAAC;EAEDa,WAAW,EAAE;IACXC,IAAI,EAAE,OAAO;IACbjB,GAAG,EAAE,gBAAgB;IACrBkB,KAAK,EAAE;EACT,CAAC;EAEDC,MAAM,EAAE;IACNF,IAAI,EAAE,OAAO;IACbjB,GAAG,EAAE,QAAQ;IACbkB,KAAK,EAAE;EACT,CAAC;EAEDE,YAAY,EAAE;IACZrB,OAAO,EAAE;MACPE,kBAAkB,EAAE,sBAAsB;MAC1CC,gBAAgB,EAAE,sBAAsB;MACxCC,cAAc,EAAE;IAClB,CAAC;IACDC,MAAM,EAAE;MACNH,kBAAkB,EAAE,+BAA+B;MACnDC,gBAAgB,EAAE,+BAA+B;MACjDC,cAAc,EAAE;IAClB;EACF,CAAC;EAEDkB,OAAO,EAAE;IACPtB,OAAO,EAAE;MACPE,kBAAkB,EAAE,cAAc;MAClCC,gBAAgB,EAAE,cAAc;MAChCC,cAAc,EAAE;IAClB;EACF,CAAC;EAEDmB,WAAW,EAAE;IACXvB,OAAO,EAAE;MACPE,kBAAkB,EAAE,uBAAuB;MAC3CC,gBAAgB,EAAE,uBAAuB;MACzCC,cAAc,EAAE;IAClB,CAAC;IACDC,MAAM,EAAE;MACNH,kBAAkB,EAAE,gCAAgC;MACpDC,gBAAgB,EAAE,gCAAgC;MAClDC,cAAc,EAAE;IAClB;EACF,CAAC;EAEDoB,MAAM,EAAE;IACNxB,OAAO,EAAE;MACPE,kBAAkB,EAAE,eAAe;MACnCC,gBAAgB,EAAE,eAAe;MACjCC,cAAc,EAAE;IAClB,CAAC;IACDC,MAAM,EAAE;MACNH,kBAAkB,EAAE,wBAAwB;MAC5CC,gBAAgB,EAAE,wBAAwB;MAC1CC,cAAc,EAAE;IAClB;EACF,CAAC;EAEDqB,UAAU,EAAE;IACVzB,OAAO,EAAE;MACPE,kBAAkB,EAAE,wBAAwB;MAC5CC,gBAAgB,EAAE,wBAAwB;MAC1CC,cAAc,EAAE;IAClB,CAAC;IACDC,MAAM,EAAE;MACNH,kBAAkB,EAAE,wBAAwB;MAC5CC,gBAAgB,EAAE,wBAAwB;MAC1CC,cAAc,EAAE;IAClB;EACF,CAAC;EAEDsB,YAAY,EAAE;IACZ1B,OAAO,EAAE;MACPE,kBAAkB,EAAE,uBAAuB;MAC3CC,gBAAgB,EAAE,uBAAuB;MACzCC,cAAc,EAAE;IAClB,CAAC;IACDC,MAAM,EAAE;MACNH,kBAAkB,EAAE,wBAAwB;MAC5CC,gBAAgB,EAAE,wBAAwB;MAC1CC,cAAc,EAAE;IAClB;EACF;AACF,CAAC;AAED,SAASuB,UAAUA,CAACC,MAAM,EAAEC,KAAK,EAAE;EACjC;EACA,IAAID,MAAM,CAAC3B,GAAG,IAAI4B,KAAK,KAAK,CAAC,EAAE,OAAOD,MAAM,CAAC3B,GAAG;EAEhD,MAAM6B,KAAK,GAAGD,KAAK,GAAG,EAAE;EACxB,MAAME,MAAM,GAAGF,KAAK,GAAG,GAAG;;EAE1B;EACA,IAAIC,KAAK,KAAK,CAAC,IAAIC,MAAM,KAAK,EAAE,EAAE;IAChC,OAAOH,MAAM,CAAC1B,kBAAkB,CAAC8B,OAAO,CAAC,WAAW,EAAEC,MAAM,CAACJ,KAAK,CAAC,CAAC;;IAEpE;EACF,CAAC,MAAM,IAAIC,KAAK,IAAI,CAAC,IAAIA,KAAK,IAAI,CAAC,KAAKC,MAAM,GAAG,EAAE,IAAIA,MAAM,GAAG,EAAE,CAAC,EAAE;IACnE,OAAOH,MAAM,CAACzB,gBAAgB,CAAC6B,OAAO,CAAC,WAAW,EAAEC,MAAM,CAACJ,KAAK,CAAC,CAAC;;IAElE;EACF,CAAC,MAAM;IACL,OAAOD,MAAM,CAACxB,cAAc,CAAC4B,OAAO,CAAC,WAAW,EAAEC,MAAM,CAACJ,KAAK,CAAC,CAAC;EAClE;AACF;AAEA,OAAO,MAAMK,cAAc,GAAGA,CAACC,KAAK,EAAEN,KAAK,EAAEpB,OAAO,KAAK;EACvD,MAAM2B,UAAU,GAAGtC,oBAAoB,CAACqC,KAAK,CAAC;EAE9C,IAAI,OAAOC,UAAU,KAAK,UAAU,EAAE,OAAOA,UAAU,CAAC3B,OAAO,CAAC;EAEhE,IAAI2B,UAAU,CAAClB,IAAI,KAAK,OAAO,EAAE;IAC/B,OAAOW,KAAK,KAAK,CAAC,GACdO,UAAU,CAACnC,GAAG,GACdmC,UAAU,CAACjB,KAAK,CAACa,OAAO,CAAC,WAAW,EAAEC,MAAM,CAACJ,KAAK,CAAC,CAAC;EAC1D;EAEA,IAAIpB,OAAO,EAAEC,SAAS,EAAE;IACtB,IAAID,OAAO,CAACE,UAAU,IAAIF,OAAO,CAACE,UAAU,GAAG,CAAC,EAAE;MAChD,IAAIyB,UAAU,CAAC/B,MAAM,EAAE;QACrB,OAAOsB,UAAU,CAACS,UAAU,CAAC/B,MAAM,EAAEwB,KAAK,CAAC;MAC7C,CAAC,MAAM;QACL,OAAOF,UAAU,CAACS,UAAU,CAACpC,OAAO,EAAE6B,KAAK,CAAC,GAAG,QAAQ;MACzD;IACF,CAAC,MAAM;MACL,IAAIO,UAAU,CAAC7B,IAAI,EAAE;QACnB,OAAOoB,UAAU,CAACS,UAAU,CAAC7B,IAAI,EAAEsB,KAAK,CAAC;MAC3C,CAAC,MAAM;QACL,OAAOF,UAAU,CAACS,UAAU,CAACpC,OAAO,EAAE6B,KAAK,CAAC,GAAG,QAAQ;MACzD;IACF;EACF,CAAC,MAAM;IACL,OAAOF,UAAU,CAACS,UAAU,CAACpC,OAAO,EAAE6B,KAAK,CAAC;EAC9C;AACF,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}