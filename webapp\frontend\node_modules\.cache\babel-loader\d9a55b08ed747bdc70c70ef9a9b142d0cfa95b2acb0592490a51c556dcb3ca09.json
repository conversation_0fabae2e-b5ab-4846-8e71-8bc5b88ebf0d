{"ast": null, "code": "/**\n * Utility per la gestione degli stati dei cavi e delle bobine\n * Implementa le stesse regole di transizione della CLI\n */// Stati del cavo\nexport const CABLE_STATES={DA_INSTALLARE:'Da installare',IN_CORSO:'In corso',INSTALLATO:'Installato',SPARE:'SPARE'};// Stati della bobina\nexport const REEL_STATES={DISPONIBILE:'Disponibile',IN_USO:'In uso',TERMINATA:'Terminata',OVER:'Over'};/**\n * Determina lo stato di installazione di un cavo in base ai metri posati\n * @param {number} metriPosati - Metri posati\n * @param {number} metriTeorici - Metri teorici\n * @returns {string} - Stato di installazione\n */export const determineCableState=(metriPosati,metriTeorici)=>{if(!metriPosati||parseFloat(metriPosati)<=0){return CABLE_STATES.DA_INSTALLARE;}// Imposta sempre lo stato a INSTALLATO quando si posano metri\n// indipendentemente dal confronto con i metri teorici\nreturn CABLE_STATES.INSTALLATO;};/**\n * Determina lo stato di una bobina in base ai metri residui e totali\n * @param {number} metriResidui - Metri residui\n * @param {number} metriTotali - Metri totali\n * @returns {string} - Stato della bobina\n */export const determineReelState=(metriResidui,metriTotali)=>{if(metriResidui<0){return REEL_STATES.OVER;}if(metriResidui===0){return REEL_STATES.TERMINATA;}if(metriResidui<metriTotali){return REEL_STATES.IN_USO;}return REEL_STATES.DISPONIBILE;};/**\n * Verifica se un cavo può essere modificato in base al suo stato\n * @param {Object} cavo - Oggetto cavo\n * @returns {boolean} - True se il cavo può essere modificato, false altrimenti\n */export const canModifyCable=cavo=>{// Un cavo può essere modificato se:\n// 1. È in stato DA_INSTALLARE\n// 2. Non ha metri posati (metratura_reale = 0)\nreturn cavo.stato_installazione===CABLE_STATES.DA_INSTALLARE&&(!cavo.metratura_reale||parseFloat(cavo.metratura_reale)===0);};/**\n * Verifica se un cavo è in stato SPARE\n * @param {Object} cavo - Oggetto cavo\n * @returns {boolean} - True se il cavo è in stato SPARE, false altrimenti\n */export const isCableSpare=cavo=>{return cavo.modificato_manualmente===3||cavo.stato_installazione===CABLE_STATES.SPARE;};/**\n * Verifica se un cavo è già installato\n * @param {Object} cavo - Oggetto cavo\n * @returns {boolean} - True se il cavo è già installato, false altrimenti\n */export const isCableInstalled=cavo=>{return cavo.stato_installazione===CABLE_STATES.INSTALLATO||cavo.metratura_reale&&parseFloat(cavo.metratura_reale)>0;};/**\n * Verifica se una bobina può essere modificata in base al suo stato\n * @param {Object} bobina - Oggetto bobina\n * @returns {boolean} - True se la bobina può essere modificata, false altrimenti\n */export const canModifyReel=bobina=>{// Una bobina può essere modificata se:\n// 1. È in stato DISPONIBILE\n// 2. Non è in stato TERMINATA o OVER\nreturn bobina.stato_bobina===REEL_STATES.DISPONIBILE||bobina.stato_bobina===REEL_STATES.IN_USO;};/**\n * Ottiene il colore associato a uno stato del cavo\n * @param {string} stato - Stato del cavo\n * @returns {string} - Colore associato allo stato\n */export const getCableStateColor=stato=>{switch(stato){case CABLE_STATES.INSTALLATO:return'success';case CABLE_STATES.IN_CORSO:return'warning';case CABLE_STATES.SPARE:return'error';case CABLE_STATES.DA_INSTALLARE:default:return'default';}};/**\n * Ottiene il colore associato a uno stato della bobina\n * @param {string} stato - Stato della bobina\n * @returns {string} - Colore associato allo stato\n */export const getReelStateColor=stato=>{switch(stato){case REEL_STATES.DISPONIBILE:return'success';case REEL_STATES.IN_USO:return'primary';case REEL_STATES.TERMINATA:return'warning';case REEL_STATES.OVER:return'error';default:return'default';}};", "map": {"version": 3, "names": ["CABLE_STATES", "DA_INSTALLARE", "IN_CORSO", "INSTALLATO", "SPARE", "REEL_STATES", "DISPONIBILE", "IN_USO", "TERMINATA", "OVER", "determineCableState", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "metriTeorici", "parseFloat", "determineReelState", "metriResidui", "metriTotali", "canModifyCable", "cavo", "stato_installazione", "metratura_reale", "isCableSpare", "modificato_manualmente", "isCableInstalled", "canModifyReel", "bobina", "stato_bobina", "getCableStateColor", "stato", "getReelStateColor"], "sources": ["C:/Users/<USER>/PycharmProjects/CMS/webapp/frontend/src/utils/stateUtils.js"], "sourcesContent": ["/**\n * Utility per la gestione degli stati dei cavi e delle bobine\n * Implementa le stesse regole di transizione della CLI\n */\n\n// Stati del cavo\nexport const CABLE_STATES = {\n  DA_INSTALLARE: 'Da installare',\n  IN_CORSO: 'In corso',\n  INSTALLATO: 'Installato',\n  SPARE: 'SPARE'\n};\n\n// Stati della bobina\nexport const REEL_STATES = {\n  DISPONIBILE: 'Disponibile',\n  IN_USO: 'In uso',\n  TERMINATA: 'Terminata',\n  OVER: 'Over'\n};\n\n/**\n * Determina lo stato di installazione di un cavo in base ai metri posati\n * @param {number} metriPosati - Metri posati\n * @param {number} metriTeorici - Metri teorici\n * @returns {string} - Stato di installazione\n */\nexport const determineCableState = (metriPosati, metriTeorici) => {\n  if (!metriPosati || parseFloat(metriPosati) <= 0) {\n    return CABLE_STATES.DA_INSTALLARE;\n  }\n\n  // Imposta sempre lo stato a INSTALLATO quando si posano metri\n  // indipendentemente dal confronto con i metri teorici\n  return CABLE_STATES.INSTALLATO;\n};\n\n/**\n * Determina lo stato di una bobina in base ai metri residui e totali\n * @param {number} metriResidui - Metri residui\n * @param {number} metriTotali - Metri totali\n * @returns {string} - Stato della bobina\n */\nexport const determineReelState = (metriResidui, metriTotali) => {\n  if (metriResidui < 0) {\n    return REEL_STATES.OVER;\n  }\n\n  if (metriResidui === 0) {\n    return REEL_STATES.TERMINATA;\n  }\n\n  if (metriResidui < metriTotali) {\n    return REEL_STATES.IN_USO;\n  }\n\n  return REEL_STATES.DISPONIBILE;\n};\n\n/**\n * Verifica se un cavo può essere modificato in base al suo stato\n * @param {Object} cavo - Oggetto cavo\n * @returns {boolean} - True se il cavo può essere modificato, false altrimenti\n */\nexport const canModifyCable = (cavo) => {\n  // Un cavo può essere modificato se:\n  // 1. È in stato DA_INSTALLARE\n  // 2. Non ha metri posati (metratura_reale = 0)\n  return cavo.stato_installazione === CABLE_STATES.DA_INSTALLARE && \n         (!cavo.metratura_reale || parseFloat(cavo.metratura_reale) === 0);\n};\n\n/**\n * Verifica se un cavo è in stato SPARE\n * @param {Object} cavo - Oggetto cavo\n * @returns {boolean} - True se il cavo è in stato SPARE, false altrimenti\n */\nexport const isCableSpare = (cavo) => {\n  return cavo.modificato_manualmente === 3 || cavo.stato_installazione === CABLE_STATES.SPARE;\n};\n\n/**\n * Verifica se un cavo è già installato\n * @param {Object} cavo - Oggetto cavo\n * @returns {boolean} - True se il cavo è già installato, false altrimenti\n */\nexport const isCableInstalled = (cavo) => {\n  return cavo.stato_installazione === CABLE_STATES.INSTALLATO || \n         (cavo.metratura_reale && parseFloat(cavo.metratura_reale) > 0);\n};\n\n/**\n * Verifica se una bobina può essere modificata in base al suo stato\n * @param {Object} bobina - Oggetto bobina\n * @returns {boolean} - True se la bobina può essere modificata, false altrimenti\n */\nexport const canModifyReel = (bobina) => {\n  // Una bobina può essere modificata se:\n  // 1. È in stato DISPONIBILE\n  // 2. Non è in stato TERMINATA o OVER\n  return bobina.stato_bobina === REEL_STATES.DISPONIBILE || \n         bobina.stato_bobina === REEL_STATES.IN_USO;\n};\n\n/**\n * Ottiene il colore associato a uno stato del cavo\n * @param {string} stato - Stato del cavo\n * @returns {string} - Colore associato allo stato\n */\nexport const getCableStateColor = (stato) => {\n  switch (stato) {\n    case CABLE_STATES.INSTALLATO:\n      return 'success';\n    case CABLE_STATES.IN_CORSO:\n      return 'warning';\n    case CABLE_STATES.SPARE:\n      return 'error';\n    case CABLE_STATES.DA_INSTALLARE:\n    default:\n      return 'default';\n  }\n};\n\n/**\n * Ottiene il colore associato a uno stato della bobina\n * @param {string} stato - Stato della bobina\n * @returns {string} - Colore associato allo stato\n */\nexport const getReelStateColor = (stato) => {\n  switch (stato) {\n    case REEL_STATES.DISPONIBILE:\n      return 'success';\n    case REEL_STATES.IN_USO:\n      return 'primary';\n    case REEL_STATES.TERMINATA:\n      return 'warning';\n    case REEL_STATES.OVER:\n      return 'error';\n    default:\n      return 'default';\n  }\n};\n"], "mappings": "AAAA;AACA;AACA;AACA,GAEA;AACA,MAAO,MAAM,CAAAA,YAAY,CAAG,CAC1BC,aAAa,CAAE,eAAe,CAC9BC,QAAQ,CAAE,UAAU,CACpBC,UAAU,CAAE,YAAY,CACxBC,KAAK,CAAE,OACT,CAAC,CAED;AACA,MAAO,MAAM,CAAAC,WAAW,CAAG,CACzBC,WAAW,CAAE,aAAa,CAC1BC,MAAM,CAAE,QAAQ,CAChBC,SAAS,CAAE,WAAW,CACtBC,IAAI,CAAE,MACR,CAAC,CAED;AACA;AACA;AACA;AACA;AACA,GACA,MAAO,MAAM,CAAAC,mBAAmB,CAAGA,CAACC,WAAW,CAAEC,YAAY,GAAK,CAChE,GAAI,CAACD,WAAW,EAAIE,UAAU,CAACF,WAAW,CAAC,EAAI,CAAC,CAAE,CAChD,MAAO,CAAAX,YAAY,CAACC,aAAa,CACnC,CAEA;AACA;AACA,MAAO,CAAAD,YAAY,CAACG,UAAU,CAChC,CAAC,CAED;AACA;AACA;AACA;AACA;AACA,GACA,MAAO,MAAM,CAAAW,kBAAkB,CAAGA,CAACC,YAAY,CAAEC,WAAW,GAAK,CAC/D,GAAID,YAAY,CAAG,CAAC,CAAE,CACpB,MAAO,CAAAV,WAAW,CAACI,IAAI,CACzB,CAEA,GAAIM,YAAY,GAAK,CAAC,CAAE,CACtB,MAAO,CAAAV,WAAW,CAACG,SAAS,CAC9B,CAEA,GAAIO,YAAY,CAAGC,WAAW,CAAE,CAC9B,MAAO,CAAAX,WAAW,CAACE,MAAM,CAC3B,CAEA,MAAO,CAAAF,WAAW,CAACC,WAAW,CAChC,CAAC,CAED;AACA;AACA;AACA;AACA,GACA,MAAO,MAAM,CAAAW,cAAc,CAAIC,IAAI,EAAK,CACtC;AACA;AACA;AACA,MAAO,CAAAA,IAAI,CAACC,mBAAmB,GAAKnB,YAAY,CAACC,aAAa,GACtD,CAACiB,IAAI,CAACE,eAAe,EAAIP,UAAU,CAACK,IAAI,CAACE,eAAe,CAAC,GAAK,CAAC,CAAC,CAC1E,CAAC,CAED;AACA;AACA;AACA;AACA,GACA,MAAO,MAAM,CAAAC,YAAY,CAAIH,IAAI,EAAK,CACpC,MAAO,CAAAA,IAAI,CAACI,sBAAsB,GAAK,CAAC,EAAIJ,IAAI,CAACC,mBAAmB,GAAKnB,YAAY,CAACI,KAAK,CAC7F,CAAC,CAED;AACA;AACA;AACA;AACA,GACA,MAAO,MAAM,CAAAmB,gBAAgB,CAAIL,IAAI,EAAK,CACxC,MAAO,CAAAA,IAAI,CAACC,mBAAmB,GAAKnB,YAAY,CAACG,UAAU,EACnDe,IAAI,CAACE,eAAe,EAAIP,UAAU,CAACK,IAAI,CAACE,eAAe,CAAC,CAAG,CAAE,CACvE,CAAC,CAED;AACA;AACA;AACA;AACA,GACA,MAAO,MAAM,CAAAI,aAAa,CAAIC,MAAM,EAAK,CACvC;AACA;AACA;AACA,MAAO,CAAAA,MAAM,CAACC,YAAY,GAAKrB,WAAW,CAACC,WAAW,EAC/CmB,MAAM,CAACC,YAAY,GAAKrB,WAAW,CAACE,MAAM,CACnD,CAAC,CAED;AACA;AACA;AACA;AACA,GACA,MAAO,MAAM,CAAAoB,kBAAkB,CAAIC,KAAK,EAAK,CAC3C,OAAQA,KAAK,EACX,IAAK,CAAA5B,YAAY,CAACG,UAAU,CAC1B,MAAO,SAAS,CAClB,IAAK,CAAAH,YAAY,CAACE,QAAQ,CACxB,MAAO,SAAS,CAClB,IAAK,CAAAF,YAAY,CAACI,KAAK,CACrB,MAAO,OAAO,CAChB,IAAK,CAAAJ,YAAY,CAACC,aAAa,CAC/B,QACE,MAAO,SAAS,CACpB,CACF,CAAC,CAED;AACA;AACA;AACA;AACA,GACA,MAAO,MAAM,CAAA4B,iBAAiB,CAAID,KAAK,EAAK,CAC1C,OAAQA,KAAK,EACX,IAAK,CAAAvB,WAAW,CAACC,WAAW,CAC1B,MAAO,SAAS,CAClB,IAAK,CAAAD,WAAW,CAACE,MAAM,CACrB,MAAO,SAAS,CAClB,IAAK,CAAAF,WAAW,CAACG,SAAS,CACxB,MAAO,SAAS,CAClB,IAAK,CAAAH,WAAW,CAACI,IAAI,CACnB,MAAO,OAAO,CAChB,QACE,MAAO,SAAS,CACpB,CACF,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}