{"ast": null, "code": "import axios from'axios';const API_URL='http://localhost:8000/api';// Crea un'istanza di axios con configurazione personalizzata\nconst axiosInstance=axios.create({baseURL:API_URL,headers:{'Content-Type':'application/json'}});// Configura axios per includere il token in tutte le richieste\naxiosInstance.interceptors.request.use(config=>{const token=localStorage.getItem('token');if(token){config.headers.Authorization=`Bearer ${token}`;}return config;},error=>{return Promise.reject(error);});const userService={// Ottiene la lista di tutti gli utenti\ngetUsers:async()=>{try{const response=await axiosInstance.get('/users');return response.data;}catch(error){console.error('Get users error:',error);throw error.response?error.response.data:error;}},// Crea un nuovo utente\ncreateUser:async userData=>{try{const response=await axiosInstance.post('/users',userData);return response.data;}catch(error){console.error('Create user error:',error);throw error.response?error.response.data:error;}},// Aggiorna un utente esistente\nupdateUser:async(userId,userData)=>{try{const response=await axiosInstance.put(`/users/${userId}`,userData);return response.data;}catch(error){console.error('Update user error:',error);throw error.response?error.response.data:error;}},// Elimina un utente\ndeleteUser:async userId=>{try{const response=await axiosInstance.delete(`/users/${userId}`);return response.data;}catch(error){console.error('Delete user error:',error);throw error.response?error.response.data:error;}},// Abilita/disabilita un utente\ntoggleUserStatus:async userId=>{try{const response=await axiosInstance.get(`/users/toggle/${userId}`);return response.data;}catch(error){console.error('Toggle user status error:',error);throw error.response?error.response.data:error;}},// Ottiene una visualizzazione raw del database\ngetDbRaw:async()=>{try{const response=await axiosInstance.get('/users/db-raw');return response.data;}catch(error){console.error('Get DB raw error:',error);throw error.response?error.response.data:error;}}};export default userService;", "map": {"version": 3, "names": ["axios", "API_URL", "axiosInstance", "create", "baseURL", "headers", "interceptors", "request", "use", "config", "token", "localStorage", "getItem", "Authorization", "error", "Promise", "reject", "userService", "getUsers", "response", "get", "data", "console", "createUser", "userData", "post", "updateUser", "userId", "put", "deleteUser", "delete", "toggleUserStatus", "getDbRaw"], "sources": ["C:/Users/<USER>/PycharmProjects/CMS/webapp/frontend/src/services/userService.js"], "sourcesContent": ["import axios from 'axios';\n\nconst API_URL = 'http://localhost:8000/api';\n\n// Crea un'istanza di axios con configurazione personalizzata\nconst axiosInstance = axios.create({\n  baseURL: API_URL,\n  headers: {\n    'Content-Type': 'application/json'\n  }\n});\n\n// Configura axios per includere il token in tutte le richieste\naxiosInstance.interceptors.request.use(\n  (config) => {\n    const token = localStorage.getItem('token');\n    if (token) {\n      config.headers.Authorization = `Bearer ${token}`;\n    }\n    return config;\n  },\n  (error) => {\n    return Promise.reject(error);\n  }\n);\n\nconst userService = {\n  // Ottiene la lista di tutti gli utenti\n  getUsers: async () => {\n    try {\n      const response = await axiosInstance.get('/users');\n      return response.data;\n    } catch (error) {\n      console.error('Get users error:', error);\n      throw error.response ? error.response.data : error;\n    }\n  },\n\n  // Crea un nuovo utente\n  createUser: async (userData) => {\n    try {\n      const response = await axiosInstance.post('/users', userData);\n      return response.data;\n    } catch (error) {\n      console.error('Create user error:', error);\n      throw error.response ? error.response.data : error;\n    }\n  },\n\n  // Aggiorna un utente esistente\n  updateUser: async (userId, userData) => {\n    try {\n      const response = await axiosInstance.put(`/users/${userId}`, userData);\n      return response.data;\n    } catch (error) {\n      console.error('Update user error:', error);\n      throw error.response ? error.response.data : error;\n    }\n  },\n\n  // Elimina un utente\n  deleteUser: async (userId) => {\n    try {\n      const response = await axiosInstance.delete(`/users/${userId}`);\n      return response.data;\n    } catch (error) {\n      console.error('Delete user error:', error);\n      throw error.response ? error.response.data : error;\n    }\n  },\n\n  // Abilita/disabilita un utente\n  toggleUserStatus: async (userId) => {\n    try {\n      const response = await axiosInstance.get(`/users/toggle/${userId}`);\n      return response.data;\n    } catch (error) {\n      console.error('Toggle user status error:', error);\n      throw error.response ? error.response.data : error;\n    }\n  },\n\n  // Ottiene una visualizzazione raw del database\n  getDbRaw: async () => {\n    try {\n      const response = await axiosInstance.get('/users/db-raw');\n      return response.data;\n    } catch (error) {\n      console.error('Get DB raw error:', error);\n      throw error.response ? error.response.data : error;\n    }\n  }\n};\n\nexport default userService;\n"], "mappings": "AAAA,MAAO,CAAAA,KAAK,KAAM,OAAO,CAEzB,KAAM,CAAAC,OAAO,CAAG,2BAA2B,CAE3C;AACA,KAAM,CAAAC,aAAa,CAAGF,KAAK,CAACG,MAAM,CAAC,CACjCC,OAAO,CAAEH,OAAO,CAChBI,OAAO,CAAE,CACP,cAAc,CAAE,kBAClB,CACF,CAAC,CAAC,CAEF;AACAH,aAAa,CAACI,YAAY,CAACC,OAAO,CAACC,GAAG,CACnCC,MAAM,EAAK,CACV,KAAM,CAAAC,KAAK,CAAGC,YAAY,CAACC,OAAO,CAAC,OAAO,CAAC,CAC3C,GAAIF,KAAK,CAAE,CACTD,MAAM,CAACJ,OAAO,CAACQ,aAAa,CAAG,UAAUH,KAAK,EAAE,CAClD,CACA,MAAO,CAAAD,MAAM,CACf,CAAC,CACAK,KAAK,EAAK,CACT,MAAO,CAAAC,OAAO,CAACC,MAAM,CAACF,KAAK,CAAC,CAC9B,CACF,CAAC,CAED,KAAM,CAAAG,WAAW,CAAG,CAClB;AACAC,QAAQ,CAAE,KAAAA,CAAA,GAAY,CACpB,GAAI,CACF,KAAM,CAAAC,QAAQ,CAAG,KAAM,CAAAjB,aAAa,CAACkB,GAAG,CAAC,QAAQ,CAAC,CAClD,MAAO,CAAAD,QAAQ,CAACE,IAAI,CACtB,CAAE,MAAOP,KAAK,CAAE,CACdQ,OAAO,CAACR,KAAK,CAAC,kBAAkB,CAAEA,KAAK,CAAC,CACxC,KAAM,CAAAA,KAAK,CAACK,QAAQ,CAAGL,KAAK,CAACK,QAAQ,CAACE,IAAI,CAAGP,KAAK,CACpD,CACF,CAAC,CAED;AACAS,UAAU,CAAE,KAAO,CAAAC,QAAQ,EAAK,CAC9B,GAAI,CACF,KAAM,CAAAL,QAAQ,CAAG,KAAM,CAAAjB,aAAa,CAACuB,IAAI,CAAC,QAAQ,CAAED,QAAQ,CAAC,CAC7D,MAAO,CAAAL,QAAQ,CAACE,IAAI,CACtB,CAAE,MAAOP,KAAK,CAAE,CACdQ,OAAO,CAACR,KAAK,CAAC,oBAAoB,CAAEA,KAAK,CAAC,CAC1C,KAAM,CAAAA,KAAK,CAACK,QAAQ,CAAGL,KAAK,CAACK,QAAQ,CAACE,IAAI,CAAGP,KAAK,CACpD,CACF,CAAC,CAED;AACAY,UAAU,CAAE,KAAAA,CAAOC,MAAM,CAAEH,QAAQ,GAAK,CACtC,GAAI,CACF,KAAM,CAAAL,QAAQ,CAAG,KAAM,CAAAjB,aAAa,CAAC0B,GAAG,CAAC,UAAUD,MAAM,EAAE,CAAEH,QAAQ,CAAC,CACtE,MAAO,CAAAL,QAAQ,CAACE,IAAI,CACtB,CAAE,MAAOP,KAAK,CAAE,CACdQ,OAAO,CAACR,KAAK,CAAC,oBAAoB,CAAEA,KAAK,CAAC,CAC1C,KAAM,CAAAA,KAAK,CAACK,QAAQ,CAAGL,KAAK,CAACK,QAAQ,CAACE,IAAI,CAAGP,KAAK,CACpD,CACF,CAAC,CAED;AACAe,UAAU,CAAE,KAAO,CAAAF,MAAM,EAAK,CAC5B,GAAI,CACF,KAAM,CAAAR,QAAQ,CAAG,KAAM,CAAAjB,aAAa,CAAC4B,MAAM,CAAC,UAAUH,MAAM,EAAE,CAAC,CAC/D,MAAO,CAAAR,QAAQ,CAACE,IAAI,CACtB,CAAE,MAAOP,KAAK,CAAE,CACdQ,OAAO,CAACR,KAAK,CAAC,oBAAoB,CAAEA,KAAK,CAAC,CAC1C,KAAM,CAAAA,KAAK,CAACK,QAAQ,CAAGL,KAAK,CAACK,QAAQ,CAACE,IAAI,CAAGP,KAAK,CACpD,CACF,CAAC,CAED;AACAiB,gBAAgB,CAAE,KAAO,CAAAJ,MAAM,EAAK,CAClC,GAAI,CACF,KAAM,CAAAR,QAAQ,CAAG,KAAM,CAAAjB,aAAa,CAACkB,GAAG,CAAC,iBAAiBO,MAAM,EAAE,CAAC,CACnE,MAAO,CAAAR,QAAQ,CAACE,IAAI,CACtB,CAAE,MAAOP,KAAK,CAAE,CACdQ,OAAO,CAACR,KAAK,CAAC,2BAA2B,CAAEA,KAAK,CAAC,CACjD,KAAM,CAAAA,KAAK,CAACK,QAAQ,CAAGL,KAAK,CAACK,QAAQ,CAACE,IAAI,CAAGP,KAAK,CACpD,CACF,CAAC,CAED;AACAkB,QAAQ,CAAE,KAAAA,CAAA,GAAY,CACpB,GAAI,CACF,KAAM,CAAAb,QAAQ,CAAG,KAAM,CAAAjB,aAAa,CAACkB,GAAG,CAAC,eAAe,CAAC,CACzD,MAAO,CAAAD,QAAQ,CAACE,IAAI,CACtB,CAAE,MAAOP,KAAK,CAAE,CACdQ,OAAO,CAACR,KAAK,CAAC,mBAAmB,CAAEA,KAAK,CAAC,CACzC,KAAM,CAAAA,KAAK,CAACK,QAAQ,CAAGL,KAAK,CAACK,QAAQ,CAACE,IAAI,CAAGP,KAAK,CACpD,CACF,CACF,CAAC,CAED,cAAe,CAAAG,WAAW", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}