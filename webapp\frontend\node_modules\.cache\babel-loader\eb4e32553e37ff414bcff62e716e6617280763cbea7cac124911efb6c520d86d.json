{"ast": null, "code": "import axios from 'axios';\nconst API_URL = 'http://localhost:8000/api';\n\n// Crea un'istanza di axios con configurazione personalizzata\nconst axiosInstance = axios.create({\n  baseURL: API_URL,\n  headers: {\n    'Content-Type': 'application/json'\n  }\n});\n\n// Configura axios per includere il token in tutte le richieste\naxiosInstance.interceptors.request.use(config => {\n  const token = localStorage.getItem('token');\n  if (token) {\n    config.headers.Authorization = `Bearer ${token}`;\n  }\n  return config;\n}, error => {\n  return Promise.reject(error);\n});\nconst excelService = {\n  // Importa cavi da Excel\n  importCavi: async (cantiereId, formData) => {\n    try {\n      // Modifica la configurazione per l'upload di file\n      const config = {\n        headers: {\n          'Content-Type': 'multipart/form-data',\n          'Authorization': `Bearer ${localStorage.getItem('token')}`\n        }\n      };\n      const response = await axios.post(`${API_URL}/excel/${cantiereId}/import-cavi`, formData, config);\n      return response.data;\n    } catch (error) {\n      console.error('Import cavi error:', error);\n      throw error.response ? error.response.data : error;\n    }\n  },\n  // Importa parco bobine da Excel\n  importParcoBobine: async (cantiereId, formData) => {\n    try {\n      // Modifica la configurazione per l'upload di file\n      const config = {\n        headers: {\n          'Content-Type': 'multipart/form-data',\n          'Authorization': `Bearer ${localStorage.getItem('token')}`\n        }\n      };\n      const response = await axios.post(`${API_URL}/excel/${cantiereId}/import-parco-bobine`, formData, config);\n      return response.data;\n    } catch (error) {\n      console.error('Import parco bobine error:', error);\n      throw error.response ? error.response.data : error;\n    }\n  },\n  // Crea template Excel per cavi\n  createCaviTemplate: async () => {\n    try {\n      const response = await axiosInstance.get('/excel/template-cavi');\n      return response.data;\n    } catch (error) {\n      console.error('Create cavi template error:', error);\n      throw error.response ? error.response.data : error;\n    }\n  },\n  // Crea template Excel per parco bobine\n  createParcoBobineTemplate: async () => {\n    try {\n      const response = await axiosInstance.get('/excel/template-parco-bobine');\n      return response.data;\n    } catch (error) {\n      console.error('Create parco bobine template error:', error);\n      throw error.response ? error.response.data : error;\n    }\n  },\n  // Esporta cavi in Excel\n  exportCavi: async cantiereId => {\n    try {\n      const response = await axiosInstance.get(`/excel/${cantiereId}/export-cavi`);\n      return response.data;\n    } catch (error) {\n      console.error('Export cavi error:', error);\n      throw error.response ? error.response.data : error;\n    }\n  },\n  // Esporta parco bobine in Excel\n  exportParcoBobine: async cantiereId => {\n    try {\n      const response = await axiosInstance.get(`/excel/${cantiereId}/export-parco-bobine`);\n      return response.data;\n    } catch (error) {\n      console.error('Export parco bobine error:', error);\n      throw error.response ? error.response.data : error;\n    }\n  }\n};\nexport default excelService;", "map": {"version": 3, "names": ["axios", "API_URL", "axiosInstance", "create", "baseURL", "headers", "interceptors", "request", "use", "config", "token", "localStorage", "getItem", "Authorization", "error", "Promise", "reject", "excelService", "importCavi", "cantiereId", "formData", "response", "post", "data", "console", "importParcoBobine", "createCaviTemplate", "get", "createParcoBobineTemplate", "exportCavi", "exportParcoBobine"], "sources": ["C:/Users/<USER>/PycharmProjects/CMS/webapp/frontend/src/services/excelService.js"], "sourcesContent": ["import axios from 'axios';\n\nconst API_URL = 'http://localhost:8000/api';\n\n// Crea un'istanza di axios con configurazione personalizzata\nconst axiosInstance = axios.create({\n  baseURL: API_URL,\n  headers: {\n    'Content-Type': 'application/json'\n  }\n});\n\n// Configura axios per includere il token in tutte le richieste\naxiosInstance.interceptors.request.use(\n  (config) => {\n    const token = localStorage.getItem('token');\n    if (token) {\n      config.headers.Authorization = `Bearer ${token}`;\n    }\n    return config;\n  },\n  (error) => {\n    return Promise.reject(error);\n  }\n);\n\nconst excelService = {\n  // Importa cavi da Excel\n  importCavi: async (cantiereId, formData) => {\n    try {\n      // Modifica la configurazione per l'upload di file\n      const config = {\n        headers: {\n          'Content-Type': 'multipart/form-data',\n          'Authorization': `Bearer ${localStorage.getItem('token')}`\n        }\n      };\n      \n      const response = await axios.post(`${API_URL}/excel/${cantiereId}/import-cavi`, formData, config);\n      return response.data;\n    } catch (error) {\n      console.error('Import cavi error:', error);\n      throw error.response ? error.response.data : error;\n    }\n  },\n\n  // Importa parco bobine da Excel\n  importParcoBobine: async (cantiereId, formData) => {\n    try {\n      // Modifica la configurazione per l'upload di file\n      const config = {\n        headers: {\n          'Content-Type': 'multipart/form-data',\n          'Authorization': `Bearer ${localStorage.getItem('token')}`\n        }\n      };\n      \n      const response = await axios.post(`${API_URL}/excel/${cantiereId}/import-parco-bobine`, formData, config);\n      return response.data;\n    } catch (error) {\n      console.error('Import parco bobine error:', error);\n      throw error.response ? error.response.data : error;\n    }\n  },\n\n  // Crea template Excel per cavi\n  createCaviTemplate: async () => {\n    try {\n      const response = await axiosInstance.get('/excel/template-cavi');\n      return response.data;\n    } catch (error) {\n      console.error('Create cavi template error:', error);\n      throw error.response ? error.response.data : error;\n    }\n  },\n\n  // Crea template Excel per parco bobine\n  createParcoBobineTemplate: async () => {\n    try {\n      const response = await axiosInstance.get('/excel/template-parco-bobine');\n      return response.data;\n    } catch (error) {\n      console.error('Create parco bobine template error:', error);\n      throw error.response ? error.response.data : error;\n    }\n  },\n\n  // Esporta cavi in Excel\n  exportCavi: async (cantiereId) => {\n    try {\n      const response = await axiosInstance.get(`/excel/${cantiereId}/export-cavi`);\n      return response.data;\n    } catch (error) {\n      console.error('Export cavi error:', error);\n      throw error.response ? error.response.data : error;\n    }\n  },\n\n  // Esporta parco bobine in Excel\n  exportParcoBobine: async (cantiereId) => {\n    try {\n      const response = await axiosInstance.get(`/excel/${cantiereId}/export-parco-bobine`);\n      return response.data;\n    } catch (error) {\n      console.error('Export parco bobine error:', error);\n      throw error.response ? error.response.data : error;\n    }\n  }\n};\n\nexport default excelService;\n"], "mappings": "AAAA,OAAOA,KAAK,MAAM,OAAO;AAEzB,MAAMC,OAAO,GAAG,2BAA2B;;AAE3C;AACA,MAAMC,aAAa,GAAGF,KAAK,CAACG,MAAM,CAAC;EACjCC,OAAO,EAAEH,OAAO;EAChBI,OAAO,EAAE;IACP,cAAc,EAAE;EAClB;AACF,CAAC,CAAC;;AAEF;AACAH,aAAa,CAACI,YAAY,CAACC,OAAO,CAACC,GAAG,CACnCC,MAAM,IAAK;EACV,MAAMC,KAAK,GAAGC,YAAY,CAACC,OAAO,CAAC,OAAO,CAAC;EAC3C,IAAIF,KAAK,EAAE;IACTD,MAAM,CAACJ,OAAO,CAACQ,aAAa,GAAG,UAAUH,KAAK,EAAE;EAClD;EACA,OAAOD,MAAM;AACf,CAAC,EACAK,KAAK,IAAK;EACT,OAAOC,OAAO,CAACC,MAAM,CAACF,KAAK,CAAC;AAC9B,CACF,CAAC;AAED,MAAMG,YAAY,GAAG;EACnB;EACAC,UAAU,EAAE,MAAAA,CAAOC,UAAU,EAAEC,QAAQ,KAAK;IAC1C,IAAI;MACF;MACA,MAAMX,MAAM,GAAG;QACbJ,OAAO,EAAE;UACP,cAAc,EAAE,qBAAqB;UACrC,eAAe,EAAE,UAAUM,YAAY,CAACC,OAAO,CAAC,OAAO,CAAC;QAC1D;MACF,CAAC;MAED,MAAMS,QAAQ,GAAG,MAAMrB,KAAK,CAACsB,IAAI,CAAC,GAAGrB,OAAO,UAAUkB,UAAU,cAAc,EAAEC,QAAQ,EAAEX,MAAM,CAAC;MACjG,OAAOY,QAAQ,CAACE,IAAI;IACtB,CAAC,CAAC,OAAOT,KAAK,EAAE;MACdU,OAAO,CAACV,KAAK,CAAC,oBAAoB,EAAEA,KAAK,CAAC;MAC1C,MAAMA,KAAK,CAACO,QAAQ,GAAGP,KAAK,CAACO,QAAQ,CAACE,IAAI,GAAGT,KAAK;IACpD;EACF,CAAC;EAED;EACAW,iBAAiB,EAAE,MAAAA,CAAON,UAAU,EAAEC,QAAQ,KAAK;IACjD,IAAI;MACF;MACA,MAAMX,MAAM,GAAG;QACbJ,OAAO,EAAE;UACP,cAAc,EAAE,qBAAqB;UACrC,eAAe,EAAE,UAAUM,YAAY,CAACC,OAAO,CAAC,OAAO,CAAC;QAC1D;MACF,CAAC;MAED,MAAMS,QAAQ,GAAG,MAAMrB,KAAK,CAACsB,IAAI,CAAC,GAAGrB,OAAO,UAAUkB,UAAU,sBAAsB,EAAEC,QAAQ,EAAEX,MAAM,CAAC;MACzG,OAAOY,QAAQ,CAACE,IAAI;IACtB,CAAC,CAAC,OAAOT,KAAK,EAAE;MACdU,OAAO,CAACV,KAAK,CAAC,4BAA4B,EAAEA,KAAK,CAAC;MAClD,MAAMA,KAAK,CAACO,QAAQ,GAAGP,KAAK,CAACO,QAAQ,CAACE,IAAI,GAAGT,KAAK;IACpD;EACF,CAAC;EAED;EACAY,kBAAkB,EAAE,MAAAA,CAAA,KAAY;IAC9B,IAAI;MACF,MAAML,QAAQ,GAAG,MAAMnB,aAAa,CAACyB,GAAG,CAAC,sBAAsB,CAAC;MAChE,OAAON,QAAQ,CAACE,IAAI;IACtB,CAAC,CAAC,OAAOT,KAAK,EAAE;MACdU,OAAO,CAACV,KAAK,CAAC,6BAA6B,EAAEA,KAAK,CAAC;MACnD,MAAMA,KAAK,CAACO,QAAQ,GAAGP,KAAK,CAACO,QAAQ,CAACE,IAAI,GAAGT,KAAK;IACpD;EACF,CAAC;EAED;EACAc,yBAAyB,EAAE,MAAAA,CAAA,KAAY;IACrC,IAAI;MACF,MAAMP,QAAQ,GAAG,MAAMnB,aAAa,CAACyB,GAAG,CAAC,8BAA8B,CAAC;MACxE,OAAON,QAAQ,CAACE,IAAI;IACtB,CAAC,CAAC,OAAOT,KAAK,EAAE;MACdU,OAAO,CAACV,KAAK,CAAC,qCAAqC,EAAEA,KAAK,CAAC;MAC3D,MAAMA,KAAK,CAACO,QAAQ,GAAGP,KAAK,CAACO,QAAQ,CAACE,IAAI,GAAGT,KAAK;IACpD;EACF,CAAC;EAED;EACAe,UAAU,EAAE,MAAOV,UAAU,IAAK;IAChC,IAAI;MACF,MAAME,QAAQ,GAAG,MAAMnB,aAAa,CAACyB,GAAG,CAAC,UAAUR,UAAU,cAAc,CAAC;MAC5E,OAAOE,QAAQ,CAACE,IAAI;IACtB,CAAC,CAAC,OAAOT,KAAK,EAAE;MACdU,OAAO,CAACV,KAAK,CAAC,oBAAoB,EAAEA,KAAK,CAAC;MAC1C,MAAMA,KAAK,CAACO,QAAQ,GAAGP,KAAK,CAACO,QAAQ,CAACE,IAAI,GAAGT,KAAK;IACpD;EACF,CAAC;EAED;EACAgB,iBAAiB,EAAE,MAAOX,UAAU,IAAK;IACvC,IAAI;MACF,MAAME,QAAQ,GAAG,MAAMnB,aAAa,CAACyB,GAAG,CAAC,UAAUR,UAAU,sBAAsB,CAAC;MACpF,OAAOE,QAAQ,CAACE,IAAI;IACtB,CAAC,CAAC,OAAOT,KAAK,EAAE;MACdU,OAAO,CAACV,KAAK,CAAC,4BAA4B,EAAEA,KAAK,CAAC;MAClD,MAAMA,KAAK,CAACO,QAAQ,GAAGP,KAAK,CAACO,QAAQ,CAACE,IAAI,GAAGT,KAAK;IACpD;EACF;AACF,CAAC;AAED,eAAeG,YAAY", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}