{"ast": null, "code": "import { buildLocalizeFn } from \"../../_lib/buildLocalizeFn.mjs\";\nconst eraValues = {\n  narrow: [\"Î\", \"D\"],\n  abbreviated: [\"Î.d.C.\", \"D.C.\"],\n  wide: [\"Înainte de Cristos\", \"Du<PERSON><PERSON> Cristos\"]\n};\nconst quarterValues = {\n  narrow: [\"1\", \"2\", \"3\", \"4\"],\n  abbreviated: [\"T1\", \"T2\", \"T3\", \"T4\"],\n  wide: [\"primul trimestru\", \"al doilea trimestru\", \"al treilea trimestru\", \"al patrulea trimestru\"]\n};\nconst monthValues = {\n  narrow: [\"I\", \"F\", \"M\", \"A\", \"M\", \"I\", \"I\", \"A\", \"S\", \"O\", \"N\", \"D\"],\n  abbreviated: [\"ian\", \"feb\", \"mar\", \"apr\", \"mai\", \"iun\", \"iul\", \"aug\", \"sep\", \"oct\", \"noi\", \"dec\"],\n  wide: [\"ianuarie\", \"februarie\", \"martie\", \"aprilie\", \"mai\", \"iunie\", \"iulie\", \"august\", \"septembrie\", \"octombrie\", \"noiembrie\", \"decembrie\"]\n};\nconst dayValues = {\n  narrow: [\"d\", \"l\", \"m\", \"m\", \"j\", \"v\", \"s\"],\n  short: [\"du\", \"lu\", \"ma\", \"mi\", \"jo\", \"vi\", \"sâ\"],\n  abbreviated: [\"dum\", \"lun\", \"mar\", \"mie\", \"joi\", \"vin\", \"sâm\"],\n  wide: [\"duminică\", \"luni\", \"marți\", \"miercuri\", \"joi\", \"vineri\", \"sâmbătă\"]\n};\nconst dayPeriodValues = {\n  narrow: {\n    am: \"a\",\n    pm: \"p\",\n    midnight: \"mn\",\n    noon: \"ami\",\n    morning: \"dim\",\n    afternoon: \"da\",\n    evening: \"s\",\n    night: \"n\"\n  },\n  abbreviated: {\n    am: \"AM\",\n    pm: \"PM\",\n    midnight: \"miezul nopții\",\n    noon: \"amiază\",\n    morning: \"dimineață\",\n    afternoon: \"după-amiază\",\n    evening: \"seară\",\n    night: \"noapte\"\n  },\n  wide: {\n    am: \"a.m.\",\n    pm: \"p.m.\",\n    midnight: \"miezul nopții\",\n    noon: \"amiază\",\n    morning: \"dimineață\",\n    afternoon: \"după-amiază\",\n    evening: \"seară\",\n    night: \"noapte\"\n  }\n};\nconst formattingDayPeriodValues = {\n  narrow: {\n    am: \"a\",\n    pm: \"p\",\n    midnight: \"mn\",\n    noon: \"amiază\",\n    morning: \"dimineață\",\n    afternoon: \"după-amiază\",\n    evening: \"seară\",\n    night: \"noapte\"\n  },\n  abbreviated: {\n    am: \"AM\",\n    pm: \"PM\",\n    midnight: \"miezul nopții\",\n    noon: \"amiază\",\n    morning: \"dimineață\",\n    afternoon: \"după-amiază\",\n    evening: \"seară\",\n    night: \"noapte\"\n  },\n  wide: {\n    am: \"a.m.\",\n    pm: \"p.m.\",\n    midnight: \"miezul nopții\",\n    noon: \"amiază\",\n    morning: \"dimineață\",\n    afternoon: \"după-amiază\",\n    evening: \"seară\",\n    night: \"noapte\"\n  }\n};\nconst ordinalNumber = (dirtyNumber, _options) => {\n  return String(dirtyNumber);\n};\nexport const localize = {\n  ordinalNumber,\n  era: buildLocalizeFn({\n    values: eraValues,\n    defaultWidth: \"wide\"\n  }),\n  quarter: buildLocalizeFn({\n    values: quarterValues,\n    defaultWidth: \"wide\",\n    argumentCallback: quarter => quarter - 1\n  }),\n  month: buildLocalizeFn({\n    values: monthValues,\n    defaultWidth: \"wide\"\n  }),\n  day: buildLocalizeFn({\n    values: dayValues,\n    defaultWidth: \"wide\"\n  }),\n  dayPeriod: buildLocalizeFn({\n    values: dayPeriodValues,\n    defaultWidth: \"wide\",\n    formattingValues: formattingDayPeriodValues,\n    defaultFormattingWidth: \"wide\"\n  })\n};", "map": {"version": 3, "names": ["buildLocalizeFn", "<PERSON><PERSON><PERSON><PERSON>", "narrow", "abbreviated", "wide", "quarterValues", "month<PERSON><PERSON><PERSON>", "dayV<PERSON><PERSON>", "short", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "am", "pm", "midnight", "noon", "morning", "afternoon", "evening", "night", "formattingDayPeriodValues", "ordinalNumber", "dirtyNumber", "_options", "String", "localize", "era", "values", "defaultWidth", "quarter", "argument<PERSON>allback", "month", "day", "<PERSON><PERSON><PERSON><PERSON>", "formattingValues", "defaultFormattingWidth"], "sources": ["C:/CMS/webapp/frontend/node_modules/date-fns/locale/ro/_lib/localize.mjs"], "sourcesContent": ["import { buildLocalizeFn } from \"../../_lib/buildLocalizeFn.mjs\";\n\nconst eraValues = {\n  narrow: [\"Î\", \"D\"],\n  abbreviated: [\"Î.d.C.\", \"D.C.\"],\n  wide: [\"Înainte de Cristos\", \"Du<PERSON><PERSON> Cristos\"],\n};\n\nconst quarterValues = {\n  narrow: [\"1\", \"2\", \"3\", \"4\"],\n  abbreviated: [\"T1\", \"T2\", \"T3\", \"T4\"],\n  wide: [\n    \"primul trimestru\",\n    \"al doilea trimestru\",\n    \"al treilea trimestru\",\n    \"al patrulea trimestru\",\n  ],\n};\n\nconst monthValues = {\n  narrow: [\"I\", \"F\", \"M\", \"A\", \"M\", \"I\", \"I\", \"A\", \"S\", \"O\", \"N\", \"D\"],\n  abbreviated: [\n    \"ian\",\n    \"feb\",\n    \"mar\",\n    \"apr\",\n    \"mai\",\n    \"iun\",\n    \"iul\",\n    \"aug\",\n    \"sep\",\n    \"oct\",\n    \"noi\",\n    \"dec\",\n  ],\n\n  wide: [\n    \"ianuarie\",\n    \"februarie\",\n    \"martie\",\n    \"aprilie\",\n    \"mai\",\n    \"iunie\",\n    \"iulie\",\n    \"august\",\n    \"septembrie\",\n    \"octombrie\",\n    \"noiembrie\",\n    \"decembrie\",\n  ],\n};\n\nconst dayValues = {\n  narrow: [\"d\", \"l\", \"m\", \"m\", \"j\", \"v\", \"s\"],\n  short: [\"du\", \"lu\", \"ma\", \"mi\", \"jo\", \"vi\", \"sâ\"],\n  abbreviated: [\"dum\", \"lun\", \"mar\", \"mie\", \"joi\", \"vin\", \"sâm\"],\n  wide: [\"duminică\", \"luni\", \"marți\", \"miercuri\", \"joi\", \"vineri\", \"sâmbătă\"],\n};\n\nconst dayPeriodValues = {\n  narrow: {\n    am: \"a\",\n    pm: \"p\",\n    midnight: \"mn\",\n    noon: \"ami\",\n    morning: \"dim\",\n    afternoon: \"da\",\n    evening: \"s\",\n    night: \"n\",\n  },\n  abbreviated: {\n    am: \"AM\",\n    pm: \"PM\",\n    midnight: \"miezul nopții\",\n    noon: \"amiază\",\n    morning: \"dimineață\",\n    afternoon: \"după-amiază\",\n    evening: \"seară\",\n    night: \"noapte\",\n  },\n  wide: {\n    am: \"a.m.\",\n    pm: \"p.m.\",\n    midnight: \"miezul nopții\",\n    noon: \"amiază\",\n    morning: \"dimineață\",\n    afternoon: \"după-amiază\",\n    evening: \"seară\",\n    night: \"noapte\",\n  },\n};\n\nconst formattingDayPeriodValues = {\n  narrow: {\n    am: \"a\",\n    pm: \"p\",\n    midnight: \"mn\",\n    noon: \"amiază\",\n    morning: \"dimineață\",\n    afternoon: \"după-amiază\",\n    evening: \"seară\",\n    night: \"noapte\",\n  },\n  abbreviated: {\n    am: \"AM\",\n    pm: \"PM\",\n    midnight: \"miezul nopții\",\n    noon: \"amiază\",\n    morning: \"dimineață\",\n    afternoon: \"după-amiază\",\n    evening: \"seară\",\n    night: \"noapte\",\n  },\n  wide: {\n    am: \"a.m.\",\n    pm: \"p.m.\",\n    midnight: \"miezul nopții\",\n    noon: \"amiază\",\n    morning: \"dimineață\",\n    afternoon: \"după-amiază\",\n    evening: \"seară\",\n    night: \"noapte\",\n  },\n};\n\nconst ordinalNumber = (dirtyNumber, _options) => {\n  return String(dirtyNumber);\n};\n\nexport const localize = {\n  ordinalNumber,\n\n  era: buildLocalizeFn({\n    values: eraValues,\n    defaultWidth: \"wide\",\n  }),\n\n  quarter: buildLocalizeFn({\n    values: quarterValues,\n    defaultWidth: \"wide\",\n    argumentCallback: (quarter) => quarter - 1,\n  }),\n\n  month: buildLocalizeFn({\n    values: monthValues,\n    defaultWidth: \"wide\",\n  }),\n\n  day: buildLocalizeFn({\n    values: dayValues,\n    defaultWidth: \"wide\",\n  }),\n\n  dayPeriod: buildLocalizeFn({\n    values: dayPeriodValues,\n    defaultWidth: \"wide\",\n    formattingValues: formattingDayPeriodValues,\n    defaultFormattingWidth: \"wide\",\n  }),\n};\n"], "mappings": "AAAA,SAASA,eAAe,QAAQ,gCAAgC;AAEhE,MAAMC,SAAS,GAAG;EAChBC,MAAM,EAAE,CAAC,GAAG,EAAE,GAAG,CAAC;EAClBC,WAAW,EAAE,CAAC,QAAQ,EAAE,MAAM,CAAC;EAC/BC,IAAI,EAAE,CAAC,oBAAoB,EAAE,cAAc;AAC7C,CAAC;AAED,MAAMC,aAAa,GAAG;EACpBH,MAAM,EAAE,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,CAAC;EAC5BC,WAAW,EAAE,CAAC,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,CAAC;EACrCC,IAAI,EAAE,CACJ,kBAAkB,EAClB,qBAAqB,EACrB,sBAAsB,EACtB,uBAAuB;AAE3B,CAAC;AAED,MAAME,WAAW,GAAG;EAClBJ,MAAM,EAAE,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,CAAC;EACpEC,WAAW,EAAE,CACX,KAAK,EACL,KAAK,EACL,KAAK,EACL,KAAK,EACL,KAAK,EACL,KAAK,EACL,KAAK,EACL,KAAK,EACL,KAAK,EACL,KAAK,EACL,KAAK,EACL,KAAK,CACN;EAEDC,IAAI,EAAE,CACJ,UAAU,EACV,WAAW,EACX,QAAQ,EACR,SAAS,EACT,KAAK,EACL,OAAO,EACP,OAAO,EACP,QAAQ,EACR,YAAY,EACZ,WAAW,EACX,WAAW,EACX,WAAW;AAEf,CAAC;AAED,MAAMG,SAAS,GAAG;EAChBL,MAAM,EAAE,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,CAAC;EAC3CM,KAAK,EAAE,CAAC,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,CAAC;EACjDL,WAAW,EAAE,CAAC,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,CAAC;EAC9DC,IAAI,EAAE,CAAC,UAAU,EAAE,MAAM,EAAE,OAAO,EAAE,UAAU,EAAE,KAAK,EAAE,QAAQ,EAAE,SAAS;AAC5E,CAAC;AAED,MAAMK,eAAe,GAAG;EACtBP,MAAM,EAAE;IACNQ,EAAE,EAAE,GAAG;IACPC,EAAE,EAAE,GAAG;IACPC,QAAQ,EAAE,IAAI;IACdC,IAAI,EAAE,KAAK;IACXC,OAAO,EAAE,KAAK;IACdC,SAAS,EAAE,IAAI;IACfC,OAAO,EAAE,GAAG;IACZC,KAAK,EAAE;EACT,CAAC;EACDd,WAAW,EAAE;IACXO,EAAE,EAAE,IAAI;IACRC,EAAE,EAAE,IAAI;IACRC,QAAQ,EAAE,eAAe;IACzBC,IAAI,EAAE,QAAQ;IACdC,OAAO,EAAE,WAAW;IACpBC,SAAS,EAAE,aAAa;IACxBC,OAAO,EAAE,OAAO;IAChBC,KAAK,EAAE;EACT,CAAC;EACDb,IAAI,EAAE;IACJM,EAAE,EAAE,MAAM;IACVC,EAAE,EAAE,MAAM;IACVC,QAAQ,EAAE,eAAe;IACzBC,IAAI,EAAE,QAAQ;IACdC,OAAO,EAAE,WAAW;IACpBC,SAAS,EAAE,aAAa;IACxBC,OAAO,EAAE,OAAO;IAChBC,KAAK,EAAE;EACT;AACF,CAAC;AAED,MAAMC,yBAAyB,GAAG;EAChChB,MAAM,EAAE;IACNQ,EAAE,EAAE,GAAG;IACPC,EAAE,EAAE,GAAG;IACPC,QAAQ,EAAE,IAAI;IACdC,IAAI,EAAE,QAAQ;IACdC,OAAO,EAAE,WAAW;IACpBC,SAAS,EAAE,aAAa;IACxBC,OAAO,EAAE,OAAO;IAChBC,KAAK,EAAE;EACT,CAAC;EACDd,WAAW,EAAE;IACXO,EAAE,EAAE,IAAI;IACRC,EAAE,EAAE,IAAI;IACRC,QAAQ,EAAE,eAAe;IACzBC,IAAI,EAAE,QAAQ;IACdC,OAAO,EAAE,WAAW;IACpBC,SAAS,EAAE,aAAa;IACxBC,OAAO,EAAE,OAAO;IAChBC,KAAK,EAAE;EACT,CAAC;EACDb,IAAI,EAAE;IACJM,EAAE,EAAE,MAAM;IACVC,EAAE,EAAE,MAAM;IACVC,QAAQ,EAAE,eAAe;IACzBC,IAAI,EAAE,QAAQ;IACdC,OAAO,EAAE,WAAW;IACpBC,SAAS,EAAE,aAAa;IACxBC,OAAO,EAAE,OAAO;IAChBC,KAAK,EAAE;EACT;AACF,CAAC;AAED,MAAME,aAAa,GAAGA,CAACC,WAAW,EAAEC,QAAQ,KAAK;EAC/C,OAAOC,MAAM,CAACF,WAAW,CAAC;AAC5B,CAAC;AAED,OAAO,MAAMG,QAAQ,GAAG;EACtBJ,aAAa;EAEbK,GAAG,EAAExB,eAAe,CAAC;IACnByB,MAAM,EAAExB,SAAS;IACjByB,YAAY,EAAE;EAChB,CAAC,CAAC;EAEFC,OAAO,EAAE3B,eAAe,CAAC;IACvByB,MAAM,EAAEpB,aAAa;IACrBqB,YAAY,EAAE,MAAM;IACpBE,gBAAgB,EAAGD,OAAO,IAAKA,OAAO,GAAG;EAC3C,CAAC,CAAC;EAEFE,KAAK,EAAE7B,eAAe,CAAC;IACrByB,MAAM,EAAEnB,WAAW;IACnBoB,YAAY,EAAE;EAChB,CAAC,CAAC;EAEFI,GAAG,EAAE9B,eAAe,CAAC;IACnByB,MAAM,EAAElB,SAAS;IACjBmB,YAAY,EAAE;EAChB,CAAC,CAAC;EAEFK,SAAS,EAAE/B,eAAe,CAAC;IACzByB,MAAM,EAAEhB,eAAe;IACvBiB,YAAY,EAAE,MAAM;IACpBM,gBAAgB,EAAEd,yBAAyB;IAC3Ce,sBAAsB,EAAE;EAC1B,CAAC;AACH,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}