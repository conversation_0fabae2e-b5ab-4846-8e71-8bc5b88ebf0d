{"ast": null, "code": "import { constructNow } from \"./constructNow.mjs\";\nimport { isSameMonth } from \"./isSameMonth.mjs\";\n\n/**\n * @name isThisMonth\n * @category Month Helpers\n * @summary Is the given date in the same month as the current date?\n * @pure false\n *\n * @description\n * Is the given date in the same month as the current date?\n *\n * @typeParam DateType - The `Date` type, the function operates on. Gets inferred from passed arguments. Allows to use extensions like [`UTCDate`](https://github.com/date-fns/utc).\n *\n * @param date - The date to check\n *\n * @returns The date is in this month\n *\n * @example\n * // If today is 25 September 2014, is 15 September 2014 in this month?\n * const result = isThisMonth(new Date(2014, 8, 15))\n * //=> true\n */\n\nexport function isThisMonth(date) {\n  return isSameMonth(date, constructNow(date));\n}\n\n// Fallback for modularized imports:\nexport default isThisMonth;", "map": {"version": 3, "names": ["constructNow", "isSameMonth", "isThis<PERSON><PERSON><PERSON>", "date"], "sources": ["C:/CMS/webapp/frontend/node_modules/date-fns/isThisMonth.mjs"], "sourcesContent": ["import { constructNow } from \"./constructNow.mjs\";\nimport { isSameMonth } from \"./isSameMonth.mjs\";\n\n/**\n * @name isThisMonth\n * @category Month Helpers\n * @summary Is the given date in the same month as the current date?\n * @pure false\n *\n * @description\n * Is the given date in the same month as the current date?\n *\n * @typeParam DateType - The `Date` type, the function operates on. Gets inferred from passed arguments. Allows to use extensions like [`UTCDate`](https://github.com/date-fns/utc).\n *\n * @param date - The date to check\n *\n * @returns The date is in this month\n *\n * @example\n * // If today is 25 September 2014, is 15 September 2014 in this month?\n * const result = isThisMonth(new Date(2014, 8, 15))\n * //=> true\n */\n\nexport function isThisMonth(date) {\n  return isSameMonth(date, constructNow(date));\n}\n\n// Fallback for modularized imports:\nexport default isThisMonth;\n"], "mappings": "AAAA,SAASA,YAAY,QAAQ,oBAAoB;AACjD,SAASC,WAAW,QAAQ,mBAAmB;;AAE/C;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA,OAAO,SAASC,WAAWA,CAACC,IAAI,EAAE;EAChC,OAAOF,WAAW,CAACE,IAAI,EAAEH,YAAY,CAACG,IAAI,CAAC,CAAC;AAC9C;;AAEA;AACA,eAAeD,WAAW", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}