{"ast": null, "code": "import axios from'axios';import config from'../config';import axiosInstance from'./axiosConfig';const API_URL=config.API_URL;const comandeService={// Ottiene la lista delle comande di un cantiere\ngetComande:async cantiereId=>{try{// Assicurati che cantiereId sia un numero\nconst cantiereIdNum=parseInt(cantiereId,10);if(isNaN(cantiereIdNum)){throw new Error(`ID cantiere non valido: ${cantiereId}`);}const response=await axiosInstance.get(`/comande/${cantiereIdNum}`);return response.data;}catch(error){console.error('Get comande error:',error);throw error.response?error.response.data:error;}},// Crea una nuova comanda\ncreateComanda:async(cantiereId,comandaData)=>{try{// Assicurati che cantiereId sia un numero\nconst cantiereIdNum=parseInt(cantiereId,10);if(isNaN(cantiereIdNum)){throw new Error(`ID cantiere non valido: ${cantiereId}`);}const response=await axiosInstance.post(`/comande/${cantiereIdNum}`,comandaData);return response.data;}catch(error){console.error('Create comanda error:',error);throw error.response?error.response.data:error;}},// Aggiorna una comanda esistente\nupdateComanda:async(cantiereId,idComanda,comandaData)=>{try{// Assicurati che cantiereId sia un numero\nconst cantiereIdNum=parseInt(cantiereId,10);if(isNaN(cantiereIdNum)){throw new Error(`ID cantiere non valido: ${cantiereId}`);}const response=await axiosInstance.put(`/comande/${cantiereIdNum}/${idComanda}`,comandaData);return response.data;}catch(error){console.error('Update comanda error:',error);throw error.response?error.response.data:error;}},// Elimina una comanda\ndeleteComanda:async(cantiereId,idComanda)=>{try{// Assicurati che cantiereId sia un numero\nconst cantiereIdNum=parseInt(cantiereId,10);if(isNaN(cantiereIdNum)){throw new Error(`ID cantiere non valido: ${cantiereId}`);}const response=await axiosInstance.delete(`/comande/${cantiereIdNum}/${idComanda}`);return response.data;}catch(error){console.error('Delete comanda error:',error);throw error.response?error.response.data:error;}},// Assegna una comanda a un cavo\nassignComandaToCavo:async(cantiereId,idComanda,idCavo)=>{try{// Assicurati che cantiereId sia un numero\nconst cantiereIdNum=parseInt(cantiereId,10);if(isNaN(cantiereIdNum)){throw new Error(`ID cantiere non valido: ${cantiereId}`);}const response=await axiosInstance.post(`/comande/${cantiereIdNum}/${idComanda}/assign`,{id_cavo:idCavo});return response.data;}catch(error){console.error('Assign comanda error:',error);throw error.response?error.response.data:error;}},// Genera PDF di una comanda\nprintComanda:async(cantiereId,idComanda)=>{try{// Assicurati che cantiereId sia un numero\nconst cantiereIdNum=parseInt(cantiereId,10);if(isNaN(cantiereIdNum)){throw new Error(`ID cantiere non valido: ${cantiereId}`);}const response=await axiosInstance.get(`/comande/${cantiereIdNum}/${idComanda}/pdf`);return response.data;}catch(error){console.error('Print comanda error:',error);throw error.response?error.response.data:error;}}};export default comandeService;", "map": {"version": 3, "names": ["axios", "config", "axiosInstance", "API_URL", "comandeService", "getComande", "cantiereId", "cantiereIdNum", "parseInt", "isNaN", "Error", "response", "get", "data", "error", "console", "createComanda", "comandaData", "post", "updateComanda", "idComanda", "put", "deleteComanda", "delete", "assignComandaToCavo", "idCavo", "id_cavo", "printComanda"], "sources": ["C:/Users/<USER>/PycharmProjects/CMS/webapp/frontend/src/services/comandeService.js"], "sourcesContent": ["import axios from 'axios';\r\nimport config from '../config';\r\nimport axiosInstance from './axiosConfig';\r\n\r\nconst API_URL = config.API_URL;\r\n\r\nconst comandeService = {\r\n  // Ottiene la lista delle comande di un cantiere\r\n  getComande: async (cantiereId) => {\r\n    try {\r\n      // Assicurati che cantiereId sia un numero\r\n      const cantiereIdNum = parseInt(cantiereId, 10);\r\n      if (isNaN(cantiereIdNum)) {\r\n        throw new Error(`ID cantiere non valido: ${cantiereId}`);\r\n      }\r\n\r\n      const response = await axiosInstance.get(`/comande/${cantiereIdNum}`);\r\n      return response.data;\r\n    } catch (error) {\r\n      console.error('Get comande error:', error);\r\n      throw error.response ? error.response.data : error;\r\n    }\r\n  },\r\n\r\n  // Crea una nuova comanda\r\n  createComanda: async (cantiereId, comandaData) => {\r\n    try {\r\n      // Assicurati che cantiereId sia un numero\r\n      const cantiereIdNum = parseInt(cantiereId, 10);\r\n      if (isNaN(cantiereIdNum)) {\r\n        throw new Error(`ID cantiere non valido: ${cantiereId}`);\r\n      }\r\n\r\n      const response = await axiosInstance.post(`/comande/${cantiereIdNum}`, comandaData);\r\n      return response.data;\r\n    } catch (error) {\r\n      console.error('Create comanda error:', error);\r\n      throw error.response ? error.response.data : error;\r\n    }\r\n  },\r\n\r\n  // Aggiorna una comanda esistente\r\n  updateComanda: async (cantiereId, idComanda, comandaData) => {\r\n    try {\r\n      // Assicurati che cantiereId sia un numero\r\n      const cantiereIdNum = parseInt(cantiereId, 10);\r\n      if (isNaN(cantiereIdNum)) {\r\n        throw new Error(`ID cantiere non valido: ${cantiereId}`);\r\n      }\r\n\r\n      const response = await axiosInstance.put(`/comande/${cantiereIdNum}/${idComanda}`, comandaData);\r\n      return response.data;\r\n    } catch (error) {\r\n      console.error('Update comanda error:', error);\r\n      throw error.response ? error.response.data : error;\r\n    }\r\n  },\r\n\r\n  // Elimina una comanda\r\n  deleteComanda: async (cantiereId, idComanda) => {\r\n    try {\r\n      // Assicurati che cantiereId sia un numero\r\n      const cantiereIdNum = parseInt(cantiereId, 10);\r\n      if (isNaN(cantiereIdNum)) {\r\n        throw new Error(`ID cantiere non valido: ${cantiereId}`);\r\n      }\r\n\r\n      const response = await axiosInstance.delete(`/comande/${cantiereIdNum}/${idComanda}`);\r\n      return response.data;\r\n    } catch (error) {\r\n      console.error('Delete comanda error:', error);\r\n      throw error.response ? error.response.data : error;\r\n    }\r\n  },\r\n\r\n  // Assegna una comanda a un cavo\r\n  assignComandaToCavo: async (cantiereId, idComanda, idCavo) => {\r\n    try {\r\n      // Assicurati che cantiereId sia un numero\r\n      const cantiereIdNum = parseInt(cantiereId, 10);\r\n      if (isNaN(cantiereIdNum)) {\r\n        throw new Error(`ID cantiere non valido: ${cantiereId}`);\r\n      }\r\n\r\n      const response = await axiosInstance.post(`/comande/${cantiereIdNum}/${idComanda}/assign`, {\r\n        id_cavo: idCavo\r\n      });\r\n      return response.data;\r\n    } catch (error) {\r\n      console.error('Assign comanda error:', error);\r\n      throw error.response ? error.response.data : error;\r\n    }\r\n  },\r\n\r\n  // Genera PDF di una comanda\r\n  printComanda: async (cantiereId, idComanda) => {\r\n    try {\r\n      // Assicurati che cantiereId sia un numero\r\n      const cantiereIdNum = parseInt(cantiereId, 10);\r\n      if (isNaN(cantiereIdNum)) {\r\n        throw new Error(`ID cantiere non valido: ${cantiereId}`);\r\n      }\r\n\r\n      const response = await axiosInstance.get(`/comande/${cantiereIdNum}/${idComanda}/pdf`);\r\n      return response.data;\r\n    } catch (error) {\r\n      console.error('Print comanda error:', error);\r\n      throw error.response ? error.response.data : error;\r\n    }\r\n  }\r\n};\r\n\r\nexport default comandeService;\r\n"], "mappings": "AAAA,MAAO,CAAAA,KAAK,KAAM,OAAO,CACzB,MAAO,CAAAC,MAAM,KAAM,WAAW,CAC9B,MAAO,CAAAC,aAAa,KAAM,eAAe,CAEzC,KAAM,CAAAC,OAAO,CAAGF,MAAM,CAACE,OAAO,CAE9B,KAAM,CAAAC,cAAc,CAAG,CACrB;AACAC,UAAU,CAAE,KAAO,CAAAC,UAAU,EAAK,CAChC,GAAI,CACF;AACA,KAAM,CAAAC,aAAa,CAAGC,QAAQ,CAACF,UAAU,CAAE,EAAE,CAAC,CAC9C,GAAIG,KAAK,CAACF,aAAa,CAAC,CAAE,CACxB,KAAM,IAAI,CAAAG,KAAK,CAAC,2BAA2BJ,UAAU,EAAE,CAAC,CAC1D,CAEA,KAAM,CAAAK,QAAQ,CAAG,KAAM,CAAAT,aAAa,CAACU,GAAG,CAAC,YAAYL,aAAa,EAAE,CAAC,CACrE,MAAO,CAAAI,QAAQ,CAACE,IAAI,CACtB,CAAE,MAAOC,KAAK,CAAE,CACdC,OAAO,CAACD,KAAK,CAAC,oBAAoB,CAAEA,KAAK,CAAC,CAC1C,KAAM,CAAAA,KAAK,CAACH,QAAQ,CAAGG,KAAK,CAACH,QAAQ,CAACE,IAAI,CAAGC,KAAK,CACpD,CACF,CAAC,CAED;AACAE,aAAa,CAAE,KAAAA,CAAOV,UAAU,CAAEW,WAAW,GAAK,CAChD,GAAI,CACF;AACA,KAAM,CAAAV,aAAa,CAAGC,QAAQ,CAACF,UAAU,CAAE,EAAE,CAAC,CAC9C,GAAIG,KAAK,CAACF,aAAa,CAAC,CAAE,CACxB,KAAM,IAAI,CAAAG,KAAK,CAAC,2BAA2BJ,UAAU,EAAE,CAAC,CAC1D,CAEA,KAAM,CAAAK,QAAQ,CAAG,KAAM,CAAAT,aAAa,CAACgB,IAAI,CAAC,YAAYX,aAAa,EAAE,CAAEU,WAAW,CAAC,CACnF,MAAO,CAAAN,QAAQ,CAACE,IAAI,CACtB,CAAE,MAAOC,KAAK,CAAE,CACdC,OAAO,CAACD,KAAK,CAAC,uBAAuB,CAAEA,KAAK,CAAC,CAC7C,KAAM,CAAAA,KAAK,CAACH,QAAQ,CAAGG,KAAK,CAACH,QAAQ,CAACE,IAAI,CAAGC,KAAK,CACpD,CACF,CAAC,CAED;AACAK,aAAa,CAAE,KAAAA,CAAOb,UAAU,CAAEc,SAAS,CAAEH,WAAW,GAAK,CAC3D,GAAI,CACF;AACA,KAAM,CAAAV,aAAa,CAAGC,QAAQ,CAACF,UAAU,CAAE,EAAE,CAAC,CAC9C,GAAIG,KAAK,CAACF,aAAa,CAAC,CAAE,CACxB,KAAM,IAAI,CAAAG,KAAK,CAAC,2BAA2BJ,UAAU,EAAE,CAAC,CAC1D,CAEA,KAAM,CAAAK,QAAQ,CAAG,KAAM,CAAAT,aAAa,CAACmB,GAAG,CAAC,YAAYd,aAAa,IAAIa,SAAS,EAAE,CAAEH,WAAW,CAAC,CAC/F,MAAO,CAAAN,QAAQ,CAACE,IAAI,CACtB,CAAE,MAAOC,KAAK,CAAE,CACdC,OAAO,CAACD,KAAK,CAAC,uBAAuB,CAAEA,KAAK,CAAC,CAC7C,KAAM,CAAAA,KAAK,CAACH,QAAQ,CAAGG,KAAK,CAACH,QAAQ,CAACE,IAAI,CAAGC,KAAK,CACpD,CACF,CAAC,CAED;AACAQ,aAAa,CAAE,KAAAA,CAAOhB,UAAU,CAAEc,SAAS,GAAK,CAC9C,GAAI,CACF;AACA,KAAM,CAAAb,aAAa,CAAGC,QAAQ,CAACF,UAAU,CAAE,EAAE,CAAC,CAC9C,GAAIG,KAAK,CAACF,aAAa,CAAC,CAAE,CACxB,KAAM,IAAI,CAAAG,KAAK,CAAC,2BAA2BJ,UAAU,EAAE,CAAC,CAC1D,CAEA,KAAM,CAAAK,QAAQ,CAAG,KAAM,CAAAT,aAAa,CAACqB,MAAM,CAAC,YAAYhB,aAAa,IAAIa,SAAS,EAAE,CAAC,CACrF,MAAO,CAAAT,QAAQ,CAACE,IAAI,CACtB,CAAE,MAAOC,KAAK,CAAE,CACdC,OAAO,CAACD,KAAK,CAAC,uBAAuB,CAAEA,KAAK,CAAC,CAC7C,KAAM,CAAAA,KAAK,CAACH,QAAQ,CAAGG,KAAK,CAACH,QAAQ,CAACE,IAAI,CAAGC,KAAK,CACpD,CACF,CAAC,CAED;AACAU,mBAAmB,CAAE,KAAAA,CAAOlB,UAAU,CAAEc,SAAS,CAAEK,MAAM,GAAK,CAC5D,GAAI,CACF;AACA,KAAM,CAAAlB,aAAa,CAAGC,QAAQ,CAACF,UAAU,CAAE,EAAE,CAAC,CAC9C,GAAIG,KAAK,CAACF,aAAa,CAAC,CAAE,CACxB,KAAM,IAAI,CAAAG,KAAK,CAAC,2BAA2BJ,UAAU,EAAE,CAAC,CAC1D,CAEA,KAAM,CAAAK,QAAQ,CAAG,KAAM,CAAAT,aAAa,CAACgB,IAAI,CAAC,YAAYX,aAAa,IAAIa,SAAS,SAAS,CAAE,CACzFM,OAAO,CAAED,MACX,CAAC,CAAC,CACF,MAAO,CAAAd,QAAQ,CAACE,IAAI,CACtB,CAAE,MAAOC,KAAK,CAAE,CACdC,OAAO,CAACD,KAAK,CAAC,uBAAuB,CAAEA,KAAK,CAAC,CAC7C,KAAM,CAAAA,KAAK,CAACH,QAAQ,CAAGG,KAAK,CAACH,QAAQ,CAACE,IAAI,CAAGC,KAAK,CACpD,CACF,CAAC,CAED;AACAa,YAAY,CAAE,KAAAA,CAAOrB,UAAU,CAAEc,SAAS,GAAK,CAC7C,GAAI,CACF;AACA,KAAM,CAAAb,aAAa,CAAGC,QAAQ,CAACF,UAAU,CAAE,EAAE,CAAC,CAC9C,GAAIG,KAAK,CAACF,aAAa,CAAC,CAAE,CACxB,KAAM,IAAI,CAAAG,KAAK,CAAC,2BAA2BJ,UAAU,EAAE,CAAC,CAC1D,CAEA,KAAM,CAAAK,QAAQ,CAAG,KAAM,CAAAT,aAAa,CAACU,GAAG,CAAC,YAAYL,aAAa,IAAIa,SAAS,MAAM,CAAC,CACtF,MAAO,CAAAT,QAAQ,CAACE,IAAI,CACtB,CAAE,MAAOC,KAAK,CAAE,CACdC,OAAO,CAACD,KAAK,CAAC,sBAAsB,CAAEA,KAAK,CAAC,CAC5C,KAAM,CAAAA,KAAK,CAACH,QAAQ,CAAGG,KAAK,CAACH,QAAQ,CAACE,IAAI,CAAGC,KAAK,CACpD,CACF,CACF,CAAC,CAED,cAAe,CAAAV,cAAc", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}