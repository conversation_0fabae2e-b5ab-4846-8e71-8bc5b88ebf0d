{"ast": null, "code": "import React,{useEffect}from'react';import{Box,Typography,Paper,Button,IconButton,Alert,Card,CardContent,CardActions,Grid,Divider}from'@mui/material';import{ArrowBack as ArrowBackIcon,Refresh as RefreshIcon,Home as HomeIcon,ViewList as ViewListIcon,Add as AddIcon,Edit as EditIcon,Delete as DeleteIcon,History as HistoryIcon}from'@mui/icons-material';import{useNavigate}from'react-router-dom';import{useAuth}from'../../context/AuthContext';import AdminHomeButton from'../../components/common/AdminHomeButton';import ParcoCavi from'../../components/cavi/ParcoCavi';import{jsx as _jsx,jsxs as _jsxs}from\"react/jsx-runtime\";const ParcoCaviPage=()=>{const{isImpersonating}=useAuth();const navigate=useNavigate();// Recupera l'ID del cantiere dal localStorage\nconst cantiereId=parseInt(localStorage.getItem('selectedCantiereId'),10);const cantiereName=localStorage.getItem('selectedCantiereName')||`Cantiere ${cantiereId}`;// Reindirizza automaticamente alla pagina di visualizzazione bobine\nuseEffect(()=>{navigate('/dashboard/cavi/parco/visualizza');},[navigate]);// Torna alla lista dei cantieri\nconst handleBackToCantieri=()=>{navigate('/dashboard/cavi');};// Gestisce le notifiche\nconst handleSuccess=message=>{// Qui puoi implementare una notifica di successo se necessario\nconsole.log('Successo:',message);};const handleError=message=>{// Qui puoi implementare una notifica di errore se necessario\nconsole.error('Errore:',message);};if(!cantiereId||isNaN(cantiereId)){return/*#__PURE__*/_jsxs(Box,{children:[/*#__PURE__*/_jsx(Alert,{severity:\"error\",sx:{mb:2},children:\"Nessun cantiere selezionato o ID cantiere non valido. Torna alla pagina dei cantieri.\"}),/*#__PURE__*/_jsx(Button,{variant:\"contained\",startIcon:/*#__PURE__*/_jsx(ArrowBackIcon,{}),onClick:handleBackToCantieri,children:\"Torna ai Cantieri\"})]});}return/*#__PURE__*/_jsxs(Box,{children:[/*#__PURE__*/_jsxs(Box,{sx:{mb:3,display:'flex',alignItems:'center',justifyContent:'space-between'},children:[/*#__PURE__*/_jsxs(Box,{sx:{display:'flex',alignItems:'center'},children:[/*#__PURE__*/_jsx(IconButton,{onClick:handleBackToCantieri,sx:{mr:1},children:/*#__PURE__*/_jsx(ArrowBackIcon,{})}),/*#__PURE__*/_jsx(Typography,{variant:\"h4\",children:\"Parco Cavi\"}),/*#__PURE__*/_jsx(IconButton,{onClick:()=>window.location.reload(),sx:{ml:2},color:\"primary\",title:\"Ricarica la pagina\",children:/*#__PURE__*/_jsx(RefreshIcon,{})})]}),/*#__PURE__*/_jsx(AdminHomeButton,{})]}),/*#__PURE__*/_jsx(Paper,{sx:{mb:3,p:2},children:/*#__PURE__*/_jsxs(Box,{sx:{display:'flex',justifyContent:'space-between',alignItems:'center'},children:[/*#__PURE__*/_jsxs(Typography,{variant:\"h6\",children:[\"Cantiere: \",cantiereName,\" (ID: \",cantiereId,\")\"]}),/*#__PURE__*/_jsx(Button,{variant:\"contained\",color:\"primary\",startIcon:/*#__PURE__*/_jsx(ArrowBackIcon,{}),onClick:handleBackToCantieri,children:\"Torna al Menu Cavi\"})]})}),/*#__PURE__*/_jsxs(Paper,{sx:{p:3,mb:3},children:[/*#__PURE__*/_jsx(Typography,{variant:\"h6\",gutterBottom:true,children:\"Parco Cavi - Gestione Bobine\"}),/*#__PURE__*/_jsx(Typography,{variant:\"body1\",children:\"Seleziona un'opzione dal menu principale nella barra di navigazione in alto per gestire le bobine del parco cavi.\"}),/*#__PURE__*/_jsxs(Box,{sx:{mt:2},children:[/*#__PURE__*/_jsx(Typography,{variant:\"body2\",color:\"textSecondary\",children:\"Opzioni disponibili:\"}),/*#__PURE__*/_jsxs(\"ul\",{children:[/*#__PURE__*/_jsx(\"li\",{children:\"Visualizza Bobine Disponibili\"}),/*#__PURE__*/_jsx(\"li\",{children:\"Crea Nuova Bobina\"}),/*#__PURE__*/_jsx(\"li\",{children:\"Modifica Bobina\"}),/*#__PURE__*/_jsx(\"li\",{children:\"Elimina Bobina\"}),/*#__PURE__*/_jsx(\"li\",{children:\"Visualizza Storico Utilizzo\"})]})]})]})]});};export default ParcoCaviPage;", "map": {"version": 3, "names": ["React", "useEffect", "Box", "Typography", "Paper", "<PERSON><PERSON>", "IconButton", "<PERSON><PERSON>", "Card", "<PERSON><PERSON><PERSON><PERSON>", "CardActions", "Grid", "Divider", "ArrowBack", "ArrowBackIcon", "Refresh", "RefreshIcon", "Home", "HomeIcon", "ViewList", "ViewListIcon", "Add", "AddIcon", "Edit", "EditIcon", "Delete", "DeleteIcon", "History", "HistoryIcon", "useNavigate", "useAuth", "AdminHomeButton", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "jsx", "_jsx", "jsxs", "_jsxs", "ParcoCaviPage", "isImpersonating", "navigate", "cantiereId", "parseInt", "localStorage", "getItem", "cantiereName", "handleBackToCantieri", "handleSuccess", "message", "console", "log", "handleError", "error", "isNaN", "children", "severity", "sx", "mb", "variant", "startIcon", "onClick", "display", "alignItems", "justifyContent", "mr", "window", "location", "reload", "ml", "color", "title", "p", "gutterBottom", "mt"], "sources": ["C:/Users/<USER>/PycharmProjects/CMS/webapp/frontend/src/pages/cavi/ParcoCaviPage.js"], "sourcesContent": ["import React, { useEffect } from 'react';\nimport {\n  Box,\n  Typography,\n  Paper,\n  Button,\n  IconButton,\n  Alert,\n  Card,\n  CardContent,\n  CardActions,\n  Grid,\n  Divider\n} from '@mui/material';\nimport {\n  ArrowBack as ArrowBackIcon,\n  Refresh as RefreshIcon,\n  Home as HomeIcon,\n  ViewList as ViewListIcon,\n  Add as AddIcon,\n  Edit as EditIcon,\n  Delete as DeleteIcon,\n  History as HistoryIcon\n} from '@mui/icons-material';\nimport { useNavigate } from 'react-router-dom';\nimport { useAuth } from '../../context/AuthContext';\nimport AdminHomeButton from '../../components/common/AdminHomeButton';\nimport ParcoCavi from '../../components/cavi/ParcoCavi';\n\nconst ParcoCaviPage = () => {\n  const { isImpersonating } = useAuth();\n  const navigate = useNavigate();\n\n  // Recupera l'ID del cantiere dal localStorage\n  const cantiereId = parseInt(localStorage.getItem('selectedCantiereId'), 10);\n  const cantiereName = localStorage.getItem('selectedCantiereName') || `Cantiere ${cantiereId}`;\n\n  // Reindirizza automaticamente alla pagina di visualizzazione bobine\n  useEffect(() => {\n    navigate('/dashboard/cavi/parco/visualizza');\n  }, [navigate]);\n\n  // Torna alla lista dei cantieri\n  const handleBackToCantieri = () => {\n    navigate('/dashboard/cavi');\n  };\n\n\n\n  // Gestisce le notifiche\n  const handleSuccess = (message) => {\n    // Qui puoi implementare una notifica di successo se necessario\n    console.log('Successo:', message);\n  };\n\n  const handleError = (message) => {\n    // Qui puoi implementare una notifica di errore se necessario\n    console.error('Errore:', message);\n  };\n\n  if (!cantiereId || isNaN(cantiereId)) {\n    return (\n      <Box>\n        <Alert severity=\"error\" sx={{ mb: 2 }}>\n          Nessun cantiere selezionato o ID cantiere non valido. Torna alla pagina dei cantieri.\n        </Alert>\n        <Button\n          variant=\"contained\"\n          startIcon={<ArrowBackIcon />}\n          onClick={handleBackToCantieri}\n        >\n          Torna ai Cantieri\n        </Button>\n      </Box>\n    );\n  }\n\n  return (\n    <Box>\n      <Box sx={{ mb: 3, display: 'flex', alignItems: 'center', justifyContent: 'space-between' }}>\n        <Box sx={{ display: 'flex', alignItems: 'center' }}>\n          <IconButton onClick={handleBackToCantieri} sx={{ mr: 1 }}>\n            <ArrowBackIcon />\n          </IconButton>\n          <Typography variant=\"h4\">\n            Parco Cavi\n          </Typography>\n          <IconButton\n            onClick={() => window.location.reload()}\n            sx={{ ml: 2 }}\n            color=\"primary\"\n            title=\"Ricarica la pagina\"\n          >\n            <RefreshIcon />\n          </IconButton>\n        </Box>\n        <AdminHomeButton />\n      </Box>\n\n      <Paper sx={{ mb: 3, p: 2 }}>\n        <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>\n          <Typography variant=\"h6\">\n            Cantiere: {cantiereName} (ID: {cantiereId})\n          </Typography>\n          <Button\n            variant=\"contained\"\n            color=\"primary\"\n            startIcon={<ArrowBackIcon />}\n            onClick={handleBackToCantieri}\n          >\n            Torna al Menu Cavi\n          </Button>\n        </Box>\n      </Paper>\n\n      <Paper sx={{ p: 3, mb: 3 }}>\n        <Typography variant=\"h6\" gutterBottom>\n          Parco Cavi - Gestione Bobine\n        </Typography>\n        <Typography variant=\"body1\">\n          Seleziona un'opzione dal menu principale nella barra di navigazione in alto per gestire le bobine del parco cavi.\n        </Typography>\n        <Box sx={{ mt: 2 }}>\n          <Typography variant=\"body2\" color=\"textSecondary\">\n            Opzioni disponibili:\n          </Typography>\n          <ul>\n            <li>Visualizza Bobine Disponibili</li>\n            <li>Crea Nuova Bobina</li>\n            <li>Modifica Bobina</li>\n            <li>Elimina Bobina</li>\n            <li>Visualizza Storico Utilizzo</li>\n          </ul>\n        </Box>\n      </Paper>\n    </Box>\n  );\n};\n\nexport default ParcoCaviPage;\n"], "mappings": "AAAA,MAAO,CAAAA,KAAK,EAAIC,SAAS,KAAQ,OAAO,CACxC,OACEC,GAAG,CACHC,UAAU,CACVC,KAAK,CACLC,MAAM,CACNC,UAAU,CACVC,KAAK,CACLC,IAAI,CACJC,WAAW,CACXC,WAAW,CACXC,IAAI,CACJC,OAAO,KACF,eAAe,CACtB,OACEC,SAAS,GAAI,CAAAC,aAAa,CAC1BC,OAAO,GAAI,CAAAC,WAAW,CACtBC,IAAI,GAAI,CAAAC,QAAQ,CAChBC,QAAQ,GAAI,CAAAC,YAAY,CACxBC,GAAG,GAAI,CAAAC,OAAO,CACdC,IAAI,GAAI,CAAAC,QAAQ,CAChBC,MAAM,GAAI,CAAAC,UAAU,CACpBC,OAAO,GAAI,CAAAC,WAAW,KACjB,qBAAqB,CAC5B,OAASC,WAAW,KAAQ,kBAAkB,CAC9C,OAASC,OAAO,KAAQ,2BAA2B,CACnD,MAAO,CAAAC,eAAe,KAAM,yCAAyC,CACrE,MAAO,CAAAC,SAAS,KAAM,iCAAiC,CAAC,OAAAC,GAAA,IAAAC,IAAA,CAAAC,IAAA,IAAAC,KAAA,yBAExD,KAAM,CAAAC,aAAa,CAAGA,CAAA,GAAM,CAC1B,KAAM,CAAEC,eAAgB,CAAC,CAAGR,OAAO,CAAC,CAAC,CACrC,KAAM,CAAAS,QAAQ,CAAGV,WAAW,CAAC,CAAC,CAE9B;AACA,KAAM,CAAAW,UAAU,CAAGC,QAAQ,CAACC,YAAY,CAACC,OAAO,CAAC,oBAAoB,CAAC,CAAE,EAAE,CAAC,CAC3E,KAAM,CAAAC,YAAY,CAAGF,YAAY,CAACC,OAAO,CAAC,sBAAsB,CAAC,EAAI,YAAYH,UAAU,EAAE,CAE7F;AACAvC,SAAS,CAAC,IAAM,CACdsC,QAAQ,CAAC,kCAAkC,CAAC,CAC9C,CAAC,CAAE,CAACA,QAAQ,CAAC,CAAC,CAEd;AACA,KAAM,CAAAM,oBAAoB,CAAGA,CAAA,GAAM,CACjCN,QAAQ,CAAC,iBAAiB,CAAC,CAC7B,CAAC,CAID;AACA,KAAM,CAAAO,aAAa,CAAIC,OAAO,EAAK,CACjC;AACAC,OAAO,CAACC,GAAG,CAAC,WAAW,CAAEF,OAAO,CAAC,CACnC,CAAC,CAED,KAAM,CAAAG,WAAW,CAAIH,OAAO,EAAK,CAC/B;AACAC,OAAO,CAACG,KAAK,CAAC,SAAS,CAAEJ,OAAO,CAAC,CACnC,CAAC,CAED,GAAI,CAACP,UAAU,EAAIY,KAAK,CAACZ,UAAU,CAAC,CAAE,CACpC,mBACEJ,KAAA,CAAClC,GAAG,EAAAmD,QAAA,eACFnB,IAAA,CAAC3B,KAAK,EAAC+C,QAAQ,CAAC,OAAO,CAACC,EAAE,CAAE,CAAEC,EAAE,CAAE,CAAE,CAAE,CAAAH,QAAA,CAAC,uFAEvC,CAAO,CAAC,cACRnB,IAAA,CAAC7B,MAAM,EACLoD,OAAO,CAAC,WAAW,CACnBC,SAAS,cAAExB,IAAA,CAACpB,aAAa,GAAE,CAAE,CAC7B6C,OAAO,CAAEd,oBAAqB,CAAAQ,QAAA,CAC/B,mBAED,CAAQ,CAAC,EACN,CAAC,CAEV,CAEA,mBACEjB,KAAA,CAAClC,GAAG,EAAAmD,QAAA,eACFjB,KAAA,CAAClC,GAAG,EAACqD,EAAE,CAAE,CAAEC,EAAE,CAAE,CAAC,CAAEI,OAAO,CAAE,MAAM,CAAEC,UAAU,CAAE,QAAQ,CAAEC,cAAc,CAAE,eAAgB,CAAE,CAAAT,QAAA,eACzFjB,KAAA,CAAClC,GAAG,EAACqD,EAAE,CAAE,CAAEK,OAAO,CAAE,MAAM,CAAEC,UAAU,CAAE,QAAS,CAAE,CAAAR,QAAA,eACjDnB,IAAA,CAAC5B,UAAU,EAACqD,OAAO,CAAEd,oBAAqB,CAACU,EAAE,CAAE,CAAEQ,EAAE,CAAE,CAAE,CAAE,CAAAV,QAAA,cACvDnB,IAAA,CAACpB,aAAa,GAAE,CAAC,CACP,CAAC,cACboB,IAAA,CAAC/B,UAAU,EAACsD,OAAO,CAAC,IAAI,CAAAJ,QAAA,CAAC,YAEzB,CAAY,CAAC,cACbnB,IAAA,CAAC5B,UAAU,EACTqD,OAAO,CAAEA,CAAA,GAAMK,MAAM,CAACC,QAAQ,CAACC,MAAM,CAAC,CAAE,CACxCX,EAAE,CAAE,CAAEY,EAAE,CAAE,CAAE,CAAE,CACdC,KAAK,CAAC,SAAS,CACfC,KAAK,CAAC,oBAAoB,CAAAhB,QAAA,cAE1BnB,IAAA,CAAClB,WAAW,GAAE,CAAC,CACL,CAAC,EACV,CAAC,cACNkB,IAAA,CAACH,eAAe,GAAE,CAAC,EAChB,CAAC,cAENG,IAAA,CAAC9B,KAAK,EAACmD,EAAE,CAAE,CAAEC,EAAE,CAAE,CAAC,CAAEc,CAAC,CAAE,CAAE,CAAE,CAAAjB,QAAA,cACzBjB,KAAA,CAAClC,GAAG,EAACqD,EAAE,CAAE,CAAEK,OAAO,CAAE,MAAM,CAAEE,cAAc,CAAE,eAAe,CAAED,UAAU,CAAE,QAAS,CAAE,CAAAR,QAAA,eAClFjB,KAAA,CAACjC,UAAU,EAACsD,OAAO,CAAC,IAAI,CAAAJ,QAAA,EAAC,YACb,CAACT,YAAY,CAAC,QAAM,CAACJ,UAAU,CAAC,GAC5C,EAAY,CAAC,cACbN,IAAA,CAAC7B,MAAM,EACLoD,OAAO,CAAC,WAAW,CACnBW,KAAK,CAAC,SAAS,CACfV,SAAS,cAAExB,IAAA,CAACpB,aAAa,GAAE,CAAE,CAC7B6C,OAAO,CAAEd,oBAAqB,CAAAQ,QAAA,CAC/B,oBAED,CAAQ,CAAC,EACN,CAAC,CACD,CAAC,cAERjB,KAAA,CAAChC,KAAK,EAACmD,EAAE,CAAE,CAAEe,CAAC,CAAE,CAAC,CAAEd,EAAE,CAAE,CAAE,CAAE,CAAAH,QAAA,eACzBnB,IAAA,CAAC/B,UAAU,EAACsD,OAAO,CAAC,IAAI,CAACc,YAAY,MAAAlB,QAAA,CAAC,8BAEtC,CAAY,CAAC,cACbnB,IAAA,CAAC/B,UAAU,EAACsD,OAAO,CAAC,OAAO,CAAAJ,QAAA,CAAC,mHAE5B,CAAY,CAAC,cACbjB,KAAA,CAAClC,GAAG,EAACqD,EAAE,CAAE,CAAEiB,EAAE,CAAE,CAAE,CAAE,CAAAnB,QAAA,eACjBnB,IAAA,CAAC/B,UAAU,EAACsD,OAAO,CAAC,OAAO,CAACW,KAAK,CAAC,eAAe,CAAAf,QAAA,CAAC,sBAElD,CAAY,CAAC,cACbjB,KAAA,OAAAiB,QAAA,eACEnB,IAAA,OAAAmB,QAAA,CAAI,+BAA6B,CAAI,CAAC,cACtCnB,IAAA,OAAAmB,QAAA,CAAI,mBAAiB,CAAI,CAAC,cAC1BnB,IAAA,OAAAmB,QAAA,CAAI,iBAAe,CAAI,CAAC,cACxBnB,IAAA,OAAAmB,QAAA,CAAI,gBAAc,CAAI,CAAC,cACvBnB,IAAA,OAAAmB,QAAA,CAAI,6BAA2B,CAAI,CAAC,EAClC,CAAC,EACF,CAAC,EACD,CAAC,EACL,CAAC,CAEV,CAAC,CAED,cAAe,CAAAhB,aAAa", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}