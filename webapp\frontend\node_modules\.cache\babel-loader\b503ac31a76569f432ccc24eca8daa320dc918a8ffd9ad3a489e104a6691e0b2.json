{"ast": null, "code": "import toDate from \"../toDate/index.js\";\nimport requiredArgs from \"../_lib/requiredArgs/index.js\";\n/**\n * @name isSaturday\n * @category Weekday Helpers\n * @summary Is the given date Saturday?\n *\n * @description\n * Is the given date Saturday?\n *\n * @param {Date|Number} date - the date to check\n * @returns {Boolean} the date is Saturday\n * @throws {TypeError} 1 argument required\n *\n * @example\n * // Is 27 September 2014 Saturday?\n * const result = isSaturday(new Date(2014, 8, 27))\n * //=> true\n */\nexport default function isSaturday(dirtyDate) {\n  requiredArgs(1, arguments);\n  return toDate(dirtyDate).getDay() === 6;\n}", "map": {"version": 3, "names": ["toDate", "requiredArgs", "isSaturday", "dirtyDate", "arguments", "getDay"], "sources": ["C:/CMS/webapp/frontend/node_modules/date-fns/esm/isSaturday/index.js"], "sourcesContent": ["import toDate from \"../toDate/index.js\";\nimport requiredArgs from \"../_lib/requiredArgs/index.js\";\n/**\n * @name isSaturday\n * @category Weekday Helpers\n * @summary Is the given date Saturday?\n *\n * @description\n * Is the given date Saturday?\n *\n * @param {Date|Number} date - the date to check\n * @returns {Boolean} the date is Saturday\n * @throws {TypeError} 1 argument required\n *\n * @example\n * // Is 27 September 2014 Saturday?\n * const result = isSaturday(new Date(2014, 8, 27))\n * //=> true\n */\nexport default function isSaturday(dirtyDate) {\n  requiredArgs(1, arguments);\n  return toDate(dirtyDate).getDay() === 6;\n}"], "mappings": "AAAA,OAAOA,MAAM,MAAM,oBAAoB;AACvC,OAAOC,YAAY,MAAM,+BAA+B;AACxD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,eAAe,SAASC,UAAUA,CAACC,SAAS,EAAE;EAC5CF,YAAY,CAAC,CAAC,EAAEG,SAAS,CAAC;EAC1B,OAAOJ,MAAM,CAACG,SAAS,CAAC,CAACE,MAAM,CAAC,CAAC,KAAK,CAAC;AACzC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}