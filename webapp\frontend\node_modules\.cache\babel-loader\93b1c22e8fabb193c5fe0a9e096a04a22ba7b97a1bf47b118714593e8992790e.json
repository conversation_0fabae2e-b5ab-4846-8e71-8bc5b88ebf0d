{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\PycharmProjects\\\\CMS\\\\webapp\\\\frontend\\\\src\\\\components\\\\cavi\\\\InserisciMetriForm.js\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect } from 'react';\nimport axios from 'axios';\nimport { Box, Paper, Typography, TextField, Button, Grid, FormControl, InputLabel, Select, MenuItem, Divider, Alert, CircularProgress, FormHelperText, IconButton, Chip, InputAdornment, Dialog, DialogTitle, DialogContent, DialogActions, Table, TableBody, TableCell, TableContainer, TableHead, TableRow, List, ListItem, ListItemButton, ListItemText, ListItemSecondaryAction } from '@mui/material';\nimport { Search as SearchIcon, Save as SaveIcon, Cancel as CancelIcon, Warning as WarningIcon, Info as InfoIcon, AddCircleOutline as AddCircleOutlineIcon } from '@mui/icons-material';\nimport { useNavigate } from 'react-router-dom';\nimport caviService from '../../services/caviService';\nimport axiosInstance from '../../services/axiosConfig';\nimport IncompatibleReelDialog from './IncompatibleReelDialog';\nimport CavoDetailsView from './CavoDetailsView';\nimport { CABLE_STATES, REEL_STATES, determineCableState, determineReelState, canModifyCable, isCableSpare, isCableInstalled, getCableStateColor, getReelStateColor } from '../../utils/stateUtils';\nimport parcoCaviService from '../../services/parcoCaviService';\nimport { redirectToVisualizzaCavi } from '../../utils/navigationUtils';\n\n/**\n * Componente per l'inserimento dei metri posati di un cavo\n * Versione semplificata con workflow compresso in un'unica pagina\n *\n * @param {Object} props - Proprietà del componente\n * @param {string} props.cantiereId - ID del cantiere\n * @param {Function} props.onSuccess - Funzione chiamata al successo dell'operazione\n * @param {Function} props.onError - Funzione chiamata in caso di errore\n */\nimport { jsxDEV as _jsxDEV, Fragment as _Fragment } from \"react/jsx-dev-runtime\";\nconst InserisciMetriForm = ({\n  cantiereId,\n  onSuccess,\n  onError\n}) => {\n  _s();\n  const navigate = useNavigate();\n\n  // Stati per la gestione del form\n  const [loading, setLoading] = useState(false);\n  const [caviLoading, setCaviLoading] = useState(false);\n  const [bobineLoading, setBobineLoading] = useState(false);\n  const [searchResults, setSearchResults] = useState([]);\n  const [showSearchResults, setShowSearchResults] = useState(false);\n\n  // Stati per i dati\n  const [cavi, setCavi] = useState([]);\n  const [bobine, setBobine] = useState([]);\n  const [selectedCavo, setSelectedCavo] = useState(null);\n  const [cavoIdInput, setCavoIdInput] = useState('');\n\n  // Stato per la gestione dei passi (mantenuto per compatibilità con funzioni esistenti)\n  const [activeStep, setActiveStep] = useState(0);\n\n  // Stati per il form\n  const [formData, setFormData] = useState({\n    id_cavo: '',\n    metri_posati: '',\n    id_bobina: ''\n  });\n\n  // Stati per la validazione\n  const [formErrors, setFormErrors] = useState({});\n  const [formWarnings, setFormWarnings] = useState({});\n\n  // Stati per i dialoghi speciali\n  const [showIncompatibleReelDialog, setShowIncompatibleReelDialog] = useState(false);\n  const [incompatibleReel, setIncompatibleReel] = useState(null); // Mantenuto per compatibilità\n  const [incompatibleReelData, setIncompatibleReelData] = useState({\n    cavo: null,\n    bobina: null\n  });\n  const [showAlreadyLaidDialog, setShowAlreadyLaidDialog] = useState(false);\n\n  // Stati per i filtri delle bobine\n  const [searchText, setSearchText] = useState('');\n  const [alreadyLaidCavo, setAlreadyLaidCavo] = useState(null);\n  const [showCavoDetailsDialog, setShowCavoDetailsDialog] = useState(false);\n\n  // Gestisce la selezione di una bobina\n  const handleSelectBobina = idBobina => {\n    console.log('Bobina selezionata:', idBobina);\n    setFormData({\n      ...formData,\n      id_bobina: idBobina\n    });\n\n    // Reset degli errori\n    setFormErrors(prev => ({\n      ...prev,\n      id_bobina: null\n    }));\n\n    // Forza il re-render per mostrare le informazioni della bobina selezionata\n    const selectedBobina = bobine.find(b => b.id_bobina === idBobina);\n    if (selectedBobina) {\n      console.log('Dettagli bobina selezionata:', selectedBobina);\n    }\n  };\n\n  // Gestisce il cambio del testo di ricerca\n  const handleSearchTextChange = event => {\n    setSearchText(event.target.value);\n  };\n\n  // Carica la lista delle bobine all'avvio\n  useEffect(() => {\n    loadBobine();\n  }, [cantiereId]);\n\n  // Funzione per estrarre il numero della bobina dall'ID completo\n  const getBobinaNumber = idBobina => {\n    // L'ID completo è nel formato C{id_cantiere}_B{numero_bobina}\n    if (idBobina && idBobina.includes('_B')) {\n      return idBobina.split('_B')[1];\n    }\n    return idBobina;\n  };\n\n  // Funzione per caricare i cavi\n  const loadCavi = async () => {\n    try {\n      setCaviLoading(true);\n      console.log(`Caricamento cavi per il cantiere ${cantiereId}...`);\n\n      // Carica tutti i cavi, inclusi quelli SPARE e installati\n      try {\n        const caviData = await caviService.getCavi(cantiereId);\n        console.log(`Caricati ${caviData.length} cavi`);\n\n        // Mostra tutti i cavi (da installare, in corso, installati e SPARE)\n        // Questo permette di vedere anche i cavi già installati per eventuale modifica della bobina\n        setCavi(caviData);\n      } catch (loadError) {\n        console.error('Errore nel primo tentativo di caricamento dei cavi:', loadError);\n\n        // Se è un errore di rete, prova un secondo tentativo dopo una breve pausa\n        if (loadError.isNetworkError || !loadError.response || loadError.code === 'ECONNABORTED' || loadError.message && loadError.message.includes('Network Error')) {\n          console.log('Errore di rete, tentativo di recupero dopo 1 secondo...');\n          await new Promise(resolve => setTimeout(resolve, 1000));\n          try {\n            // Secondo tentativo con timeout aumentato\n            const token = localStorage.getItem('token');\n            const API_URL = axiosInstance.defaults.baseURL;\n\n            // Usa l'ID cantiere originale per la richiesta alternativa\n            const retryResponse = await axios.get(`${API_URL}/cavi/${cantiereId}`, {\n              headers: {\n                'Authorization': `Bearer ${token}`\n              },\n              timeout: 30000 // 30 secondi\n            });\n            console.log(`Recupero riuscito al secondo tentativo: ${retryResponse.data.length} cavi`);\n            setCavi(retryResponse.data);\n          } catch (retryError) {\n            console.error('Anche il secondo tentativo è fallito:', retryError);\n            throw retryError;\n          }\n        } else {\n          throw loadError;\n        }\n      }\n    } catch (error) {\n      console.error('Errore nel caricamento dei cavi:', error);\n\n      // Messaggio di errore più dettagliato\n      let errorMessage = 'Errore nel caricamento dei cavi';\n      if (error.isNetworkError) {\n        errorMessage = 'Errore di connessione al server. Verifica la rete e riprova.';\n      } else if (error.detail) {\n        errorMessage = error.detail;\n      } else if (error.message) {\n        errorMessage = error.message;\n      }\n      onError(errorMessage);\n    } finally {\n      setCaviLoading(false);\n    }\n  };\n\n  // Funzione per caricare le bobine\n  const loadBobine = async () => {\n    try {\n      setBobineLoading(true);\n      console.log('Caricamento bobine iniziato...');\n\n      // Carica tutte le bobine disponibili\n      const bobineData = await parcoCaviService.getBobine(cantiereId);\n      console.log(`Bobine caricate: ${bobineData.length}`);\n\n      // Filtra solo per stato (disponibile o in uso) e metri residui > 0\n      const bobineUtilizzabili = bobineData.filter(bobina => bobina.stato_bobina !== 'Terminata' && bobina.stato_bobina !== 'Over' && bobina.metri_residui > 0);\n      console.log(`Bobine utilizzabili: ${bobineUtilizzabili.length}`);\n\n      // Se c'è un cavo selezionato, evidenzia le bobine compatibili ma mostra tutte\n      if (selectedCavo && selectedCavo.tipologia && selectedCavo.sezione) {\n        console.log('Cavo selezionato, evidenziando bobine compatibili...');\n        console.log('Dati cavo:', {\n          id_cavo: selectedCavo.id_cavo,\n          tipologia: selectedCavo.tipologia,\n          sezione: selectedCavo.sezione\n        });\n\n        // Ordina le bobine: prima le compatibili, poi le altre, tutte ordinate per metri residui\n        const cavoTipologia = String(selectedCavo.tipologia || '').trim().toLowerCase();\n        const cavoSezione = String(selectedCavo.sezione || '0').trim();\n\n        // Identifica le bobine compatibili\n        const bobineCompatibili = [];\n        const bobineNonCompatibili = [];\n\n        // Dividi le bobine in compatibili e non compatibili\n        bobineUtilizzabili.forEach(bobina => {\n          const bobinaTipologia = String(bobina.tipologia || '').trim().toLowerCase();\n          const bobinaSezione = String(bobina.sezione || '0').trim();\n\n          // Verifica compatibilità\n          const tipologiaMatch = bobinaTipologia === cavoTipologia;\n          const sezioneMatch = bobinaSezione === cavoSezione;\n          const isCompatible = tipologiaMatch && sezioneMatch;\n          console.log(`Verifica compatibilità bobina ${bobina.id_bobina}:`, {\n            'Tipologia bobina': `\"${bobina.tipologia}\"`,\n            'Tipologia cavo': `\"${selectedCavo.tipologia}\"`,\n            'Tipologie uguali?': tipologiaMatch,\n            'Sezione bobina': `\"${String(bobina.sezione)}\"`,\n            'Sezione cavo': `\"${String(selectedCavo.sezione)}\"`,\n            'Sezioni uguali?': sezioneMatch,\n            'Stato bobina': bobina.stato_bobina,\n            'Metri residui': bobina.metri_residui,\n            'Compatibile?': isCompatible\n          });\n          if (isCompatible) {\n            bobineCompatibili.push(bobina);\n          } else {\n            bobineNonCompatibili.push(bobina);\n          }\n        });\n        console.log(`Bobine compatibili trovate: ${bobineCompatibili.length}`);\n        console.log(`Bobine non compatibili: ${bobineNonCompatibili.length}`);\n\n        // Log dettagliato delle bobine compatibili\n        if (bobineCompatibili.length > 0) {\n          console.log('Dettaglio bobine compatibili:');\n          bobineCompatibili.forEach(bobina => {\n            console.log(`- ${bobina.id_bobina}: ${bobina.tipologia} / ${bobina.sezione} (${bobina.metri_residui}m)`);\n          });\n        } else {\n          console.log('ATTENZIONE: Nessuna bobina compatibile trovata!');\n        }\n\n        // Ordina entrambi gli array per metri residui (decrescente)\n        bobineCompatibili.sort((a, b) => b.metri_residui - a.metri_residui);\n        bobineNonCompatibili.sort((a, b) => b.metri_residui - a.metri_residui);\n\n        // Concatena gli array: prima le compatibili, poi le non compatibili\n        const bobineOrdinate = [...bobineCompatibili, ...bobineNonCompatibili];\n\n        // Imposta le bobine nel componente\n        setBobine(bobineOrdinate);\n      } else {\n        // Se non c'è un cavo selezionato, mostra tutte le bobine ordinate per metri residui\n        console.log('Nessun cavo selezionato, mostrando tutte le bobine...');\n        bobineUtilizzabili.sort((a, b) => b.metri_residui - a.metri_residui);\n        setBobine(bobineUtilizzabili);\n      }\n    } catch (error) {\n      console.error('Errore nel caricamento delle bobine:', error);\n      onError('Errore nel caricamento delle bobine: ' + (error.message || 'Errore sconosciuto'));\n    } finally {\n      setBobineLoading(false);\n    }\n  };\n\n  // Gestisce la ricerca di un cavo per ID o pattern\n  const handleSearchCavoById = async () => {\n    if (!cavoIdInput.trim()) {\n      onError('Inserisci un ID cavo valido');\n      return;\n    }\n    try {\n      setCaviLoading(true);\n\n      // Dividi l'input in termini di ricerca separati da virgola\n      const searchTerms = cavoIdInput.split(',').map(term => term.trim()).filter(term => term.length > 0);\n      console.log(`Ricerca cavi con ${searchTerms.length} termini: ${searchTerms.join(', ')} nel cantiere ${cantiereId}`);\n\n      // Cerca tutti i cavi\n      const caviData = await caviService.getCavi(cantiereId);\n\n      // Filtra i cavi che corrispondono ad almeno uno dei termini di ricerca\n      const filteredCavi = caviData.filter(cavo => searchTerms.some(term => cavo.id_cavo.toLowerCase().includes(term.toLowerCase())));\n      console.log(`Trovati ${filteredCavi.length} cavi corrispondenti ai termini di ricerca`);\n\n      // Cerca corrispondenze esatte per qualsiasi termine di ricerca\n      const exactMatches = filteredCavi.filter(cavo => searchTerms.some(term => cavo.id_cavo.toLowerCase() === term.toLowerCase()));\n      if (exactMatches.length === 1) {\n        // Se c'è una sola corrispondenza esatta, seleziona direttamente quel cavo\n        console.log('Trovata una corrispondenza esatta:', exactMatches[0]);\n\n        // Verifica se il cavo è già installato\n        if (exactMatches[0].stato_installazione === 'Installato' || exactMatches[0].metratura_reale && exactMatches[0].metratura_reale > 0) {\n          console.log('Cavo già installato, mostra dialogo:', exactMatches[0]);\n          setAlreadyLaidCavo(exactMatches[0]);\n          setShowAlreadyLaidDialog(true);\n          setCaviLoading(false);\n          return;\n        }\n\n        // Verifica se il cavo è SPARE\n        if (exactMatches[0].modificato_manualmente === 3) {\n          console.log('Cavo SPARE trovato:', exactMatches[0]);\n          // Gestione del cavo SPARE - verrà gestito in handleCavoSelect\n        }\n\n        // Seleziona il cavo direttamente\n        handleCavoSelect(exactMatches[0]);\n      } else if (filteredCavi.length > 0) {\n        // Mostra i risultati della ricerca in una tabella\n        setSearchResults(filteredCavi);\n        setShowSearchResults(true);\n      } else {\n        // Nessun cavo trovato\n        onError(`Nessun cavo trovato con i termini \"${searchTerms.join(', ')}\" nel cantiere ${cantiereId}`);\n      }\n    } catch (error) {\n      console.error('Errore nella ricerca del cavo:', error);\n\n      // Messaggio di errore più dettagliato\n      let errorMessage = 'Errore nella ricerca dei cavi';\n      if (error.isNetworkError) {\n        errorMessage = 'Errore di connessione al server. Verifica la rete e riprova.';\n      } else if (error.status === 404) {\n        errorMessage = `Cavo con ID \"${cavoIdInput.trim()}\" non trovato nel cantiere ${cantiereId}`;\n      } else if (error.detail) {\n        errorMessage = error.detail;\n      } else if (error.message) {\n        errorMessage = error.message;\n      }\n      onError(errorMessage);\n    } finally {\n      setCaviLoading(false);\n    }\n  };\n\n  // Gestisce la selezione di un cavo\n  const handleCavoSelect = cavo => {\n    // Verifica se il cavo è già installato\n    if (cavo.stato_installazione === 'Installato' || cavo.metratura_reale && cavo.metratura_reale > 0) {\n      // Mostra il dialogo per cavi già posati\n      setAlreadyLaidCavo(cavo);\n      setShowAlreadyLaidDialog(true);\n      return;\n    }\n    // Verifica se il cavo è SPARE (modificato_manualmente = 3)\n    else if (cavo.modificato_manualmente === 3) {\n      // Chiedi conferma all'utente per riattivare il cavo SPARE\n      if (window.confirm(`Il cavo ${cavo.id_cavo} è marcato come SPARE. Vuoi riattivarlo?`)) {\n        // Riattiva il cavo SPARE (imposta modificato_manualmente = 0)\n        reactivateSpare(cavo.id_cavo).then(() => {\n          // Aggiorna il cavo selezionato con modificato_manualmente = 0\n          const updatedCavo = {\n            ...cavo,\n            modificato_manualmente: 0\n          };\n          setSelectedCavo(updatedCavo);\n          setFormData({\n            ...formData,\n            id_cavo: updatedCavo.id_cavo,\n            metri_posati: ''\n          });\n          // Nascondi i risultati della ricerca\n          setShowSearchResults(false);\n\n          // Carica le bobine compatibili\n          loadBobine();\n        }).catch(error => {\n          console.error('Errore durante la riattivazione del cavo SPARE:', error);\n          onError('Errore durante la riattivazione del cavo SPARE: ' + (error.message || 'Errore sconosciuto'));\n        });\n      } else {\n        // L'utente ha annullato la riattivazione\n        return;\n      }\n    } else {\n      // Cavo normale (non SPARE e non installato)\n      setSelectedCavo(cavo);\n      setFormData({\n        ...formData,\n        id_cavo: cavo.id_cavo,\n        metri_posati: ''\n      });\n      // Nascondi i risultati della ricerca\n      setShowSearchResults(false);\n\n      // Carica le bobine compatibili\n      if (cavo.tipologia && cavo.n_conduttori && cavo.sezione) {\n        console.log(`Caricamento bobine compatibili per il cavo ${cavo.id_cavo}...`);\n        loadBobine();\n      }\n    }\n  };\n\n  // Funzione per riattivare un cavo SPARE\n  const reactivateSpare = async cavoId => {\n    try {\n      // Chiamata API per riattivare il cavo SPARE\n      await caviService.reactivateSpare(cantiereId, cavoId);\n      onSuccess(`Cavo ${cavoId} riattivato con successo`);\n      return true;\n    } catch (error) {\n      console.error('Errore durante la riattivazione del cavo SPARE:', error);\n      onError('Errore durante la riattivazione del cavo SPARE: ' + (error.message || 'Errore sconosciuto'));\n      throw error;\n    }\n  };\n\n  // Gestisce il cambio dei valori nel form\n  const handleFormChange = e => {\n    const {\n      name,\n      value\n    } = e.target;\n\n    // Gestione speciale per il cambio di bobina\n    if (name === 'id_bobina' && value && value !== 'BOBINA_VUOTA') {\n      // Verifica compatibilità tra cavo e bobina\n      const bobina = bobine.find(b => b.id_bobina === value);\n      if (bobina && selectedCavo) {\n        // Nella nuova configurazione, controlliamo solo tipologia e formazione (sezione)\n        // Utilizziamo una comparazione più robusta con trim() e gestione di null/undefined\n        const isCompatible = String(bobina.tipologia || '').trim() === String(selectedCavo.tipologia || '').trim() && String(bobina.sezione || '0').trim() === String(selectedCavo.sezione || '0').trim();\n        if (!isCompatible) {\n          console.log('Bobina incompatibile selezionata:', bobina);\n          console.log('Cavo corrente:', selectedCavo);\n          console.log('Confronto formazione:', {\n            cavoFormazione: String(selectedCavo.sezione || '0'),\n            bobinaFormazione: String(bobina.sezione || '0')\n          });\n\n          // Mostra il dialogo di incompatibilità\n          setIncompatibleReelData({\n            cavo: selectedCavo,\n            bobina: bobina\n          });\n          setShowIncompatibleReelDialog(true);\n\n          // Non aggiornare il valore del form finché l'utente non conferma\n          return;\n        }\n      }\n    }\n    setFormData({\n      ...formData,\n      [name]: value\n    });\n\n    // Validazione in tempo reale\n    validateField(name, value);\n  };\n\n  // Validazione di un campo\n  const validateField = (name, value) => {\n    let error = null;\n    let warning = null;\n    if (name === 'metri_posati') {\n      // Controllo input vuoto\n      if (!value || value.trim() === '') {\n        error = 'Inserire un valore per i metri posati';\n        return false;\n      }\n\n      // Controllo formato numerico\n      if (isNaN(parseFloat(value)) || parseFloat(value) <= 0) {\n        error = 'Inserire un valore numerico positivo';\n        return false;\n      }\n      const metriPosati = parseFloat(value);\n\n      // Controllo metri teorici cavo\n      if (selectedCavo && selectedCavo.metri_teorici && metriPosati > parseFloat(selectedCavo.metri_teorici)) {\n        warning = `I metri posati (${metriPosati}m) superano i metri teorici del cavo (${selectedCavo.metri_teorici}m)`;\n      }\n\n      // Controllo metri residui bobina (se bobina reale)\n      if (formData.id_bobina && formData.id_bobina !== 'BOBINA_VUOTA') {\n        const bobina = bobine.find(b => b.id_bobina === formData.id_bobina);\n        if (bobina && metriPosati > parseFloat(bobina.metri_residui)) {\n          warning = `I metri posati (${metriPosati}m) superano i metri residui della bobina (${bobina.metri_residui}m). La bobina andrà in stato OVER.`;\n        }\n      }\n    } else if (name === 'id_bobina') {\n      // Controllo che sia selezionata una bobina\n      if (!value || value.trim() === '') {\n        error = 'È necessario selezionare una bobina';\n        return false;\n      }\n    }\n\n    // Aggiorna gli errori\n    setFormErrors(prev => ({\n      ...prev,\n      [name]: error\n    }));\n\n    // Aggiorna gli avvisi\n    setFormWarnings(prev => ({\n      ...prev,\n      [name]: warning\n    }));\n    return !error;\n  };\n\n  // Stati per i dialoghi di conferma\n  const [notificationShown, setNotificationShown] = useState(false);\n  const [showConfirmDialog, setShowConfirmDialog] = useState(false);\n  const [confirmDialogProps, setConfirmDialogProps] = useState({\n    title: '',\n    message: '',\n    onConfirm: () => {}\n  });\n\n  // Nota: Lo stato per il dialogo di bobina incompatibile è già dichiarato sopra\n\n  // Validazione completa del form\n  const validateForm = () => {\n    let isValid = true;\n    const errors = {};\n    const warnings = {};\n\n    // Reset della variabile di notifica\n    setNotificationShown(false);\n\n    // Validazione metri posati\n    if (!formData.metri_posati || formData.metri_posati.trim() === '') {\n      errors.metri_posati = 'Inserire un valore per i metri posati';\n      isValid = false;\n    } else if (isNaN(parseFloat(formData.metri_posati)) || parseFloat(formData.metri_posati) <= 0) {\n      errors.metri_posati = 'Inserire un valore numerico positivo';\n      isValid = false;\n    }\n\n    // Validazione bobina (deve essere selezionata)\n    if (!formData.id_bobina || formData.id_bobina.trim() === '') {\n      errors.id_bobina = 'È necessario selezionare una bobina';\n      isValid = false;\n    }\n    console.log('Validazione form:', {\n      isValid,\n      errors,\n      warnings,\n      formData\n    });\n    if (isValid) {\n      const metriPosati = parseFloat(formData.metri_posati);\n\n      // Controllo metri teorici cavo\n      if (selectedCavo && selectedCavo.metri_teorici && metriPosati > parseFloat(selectedCavo.metri_teorici)) {\n        warnings.metri_posati = `I metri posati (${metriPosati}m) superano i metri teorici del cavo (${selectedCavo.metri_teorici}m)`;\n        setNotificationShown(true); // Imposta la variabile per evitare notifiche duplicate\n        // Non mostrare più il popup di conferma, solo l'avviso nel form\n        // Continua con la validazione senza interrompere il flusso\n      }\n\n      // Controllo metri residui bobina (se bobina reale)\n      if (isValid && formData.id_bobina && formData.id_bobina !== 'BOBINA_VUOTA') {\n        const bobina = bobine.find(b => b.id_bobina === formData.id_bobina);\n        if (bobina && metriPosati > parseFloat(bobina.metri_residui)) {\n          warnings.metri_posati = `I metri posati (${metriPosati}m) superano i metri residui della bobina (${bobina.metri_residui}m). La bobina andrà in stato OVER.`;\n          setNotificationShown(true); // Imposta la variabile per evitare notifiche duplicate\n\n          // Mostra il dialogo di conferma invece di window.confirm\n          setConfirmDialogProps({\n            title: 'Attenzione: Bobina in stato OVER',\n            message: `I metri posati (${metriPosati}m) superano i metri residui della bobina (${bobina.metri_residui}m). Questo porterà la bobina in stato OVER. Vuoi continuare?`,\n            onConfirm: () => {\n              // Continua con la validazione\n              handleNext();\n            }\n          });\n          setShowConfirmDialog(true);\n          return false; // Interrompi la validazione fino alla conferma dell'utente\n        }\n      }\n    }\n    setFormErrors(errors);\n    setFormWarnings(warnings);\n    return isValid;\n  };\n\n  // Gestisce il passaggio al passo successivo\n  const handleNext = () => {\n    if (activeStep === 2) {\n      // Validazione prima di passare al passo successivo (da Inserisci Metri a Conferma)\n      if (!validateForm()) {\n        return;\n      }\n    } else if (activeStep === 1) {\n      // Carica le bobine prima di passare al passo successivo (da Associa Bobina a Inserisci Metri)\n      loadBobine();\n    }\n    setActiveStep(prevActiveStep => prevActiveStep + 1);\n  };\n\n  // Gestisce il ritorno al passo precedente\n  const handleBack = () => {\n    setActiveStep(prevActiveStep => prevActiveStep - 1);\n  };\n\n  // Gestisce il reset del form\n  const handleReset = () => {\n    setActiveStep(0);\n    setSelectedCavo(null);\n    setCavoIdInput('');\n    setFormData({\n      id_cavo: '',\n      metri_posati: '',\n      id_bobina: ''\n    });\n    setFormErrors({});\n    setFormWarnings({});\n  };\n\n  // Determina lo stato di installazione in base ai metri posati\n  // Utilizziamo la funzione dalla utility stateUtils\n  const determineInstallationStatus = (metriPosati, metriTeorici) => {\n    return determineCableState(metriPosati, metriTeorici);\n  };\n\n  // Gestisce l'invio del form\n  const handleSubmit = async () => {\n    // Dichiarazione delle variabili al di fuori del blocco try/catch\n    // in modo che siano accessibili anche nel blocco catch\n    let idBobina;\n    let statoInstallazione;\n    let metriPosati;\n    let forceOver = false;\n    try {\n      setLoading(true);\n\n      // Validazione finale\n      if (!selectedCavo || !formData.metri_posati || !formData.id_bobina) {\n        console.log('Validazione fallita:', {\n          selectedCavo: !!selectedCavo,\n          metri_posati: !!formData.metri_posati,\n          id_bobina: !!formData.id_bobina\n        });\n        setLoading(false);\n        return;\n      }\n\n      // Log per debug\n      console.log('Validazione passata, procedendo con il salvataggio:', {\n        selectedCavo: selectedCavo.id_cavo,\n        metri_posati: formData.metri_posati,\n        id_bobina: formData.id_bobina\n      });\n\n      // Prepara i dati da inviare\n      metriPosati = parseFloat(formData.metri_posati);\n\n      // Gestione speciale per BOBINA_VUOTA\n      idBobina = formData.id_bobina;\n\n      // Gestione differenziata per cavi non posati e cavi posati\n      if (!idBobina || idBobina === '') {\n        // È necessario selezionare una bobina, anche BOBINA_VUOTA\n        setFormErrors({\n          ...formErrors,\n          id_bobina: 'È necessario selezionare una bobina'\n        });\n        setLoading(false);\n        return;\n      } else if (idBobina === 'BOBINA_VUOTA') {\n        // Per cavi posati senza bobina specifica\n        console.log('Usando BOBINA_VUOTA per il cavo');\n        // Assicurati che BOBINA_VUOTA venga passato come stringa e non come null\n        idBobina = 'BOBINA_VUOTA';\n      } else {\n        // Per cavi posati con bobina specifica\n        console.log(`Usando bobina reale: ${idBobina}`);\n      }\n\n      // Determina lo stato di installazione\n      statoInstallazione = determineInstallationStatus(metriPosati, selectedCavo.metri_teorici);\n\n      // Imposta sempre forceOver a true per evitare blocchi quando la bobina va in OVER\n      forceOver = true;\n      console.log('Impostando forceOver a true per garantire il completamento dell\\'operazione');\n\n      // Log delle condizioni che richiederebbero forceOver\n      if (idBobina === 'BOBINA_VUOTA') {\n        console.log('Operazione con BOBINA_VUOTA');\n      }\n      // Per bobine reali, verifica i metri residui\n      else if (idBobina && idBobina !== 'BOBINA_VUOTA') {\n        const bobina = bobine.find(b => b.id_bobina === idBobina);\n        if (bobina && metriPosati > parseFloat(bobina.metri_residui)) {\n          console.log(`La bobina ${idBobina} andrà in stato OVER (metri posati ${metriPosati} > metri residui ${bobina.metri_residui})`);\n        }\n      }\n\n      // Verifica anche se i metri posati superano i metri teorici\n      if (selectedCavo && selectedCavo.metri_teorici && metriPosati > parseFloat(selectedCavo.metri_teorici)) {\n        console.log(`I metri posati (${metriPosati}) superano i metri teorici (${selectedCavo.metri_teorici})`);\n      }\n\n      // Log per debug\n      console.log('Invio dati:', {\n        cantiereId,\n        cavoId: formData.id_cavo,\n        metriPosati,\n        idBobina,\n        forceOver,\n        statoInstallazione\n      });\n\n      // Conferma finale prima dell'invio solo se non è già stata mostrata una notifica\n      if (!notificationShown) {\n        const confirmMessage = `Confermi l'aggiornamento del cavo ${formData.id_cavo} con ${metriPosati}m posati?`;\n\n        // Usa il dialogo di conferma invece di window.confirm\n        setConfirmDialogProps({\n          title: 'Conferma aggiornamento',\n          message: confirmMessage,\n          onConfirm: async () => {\n            // Esegui la chiamata API qui invece che nel flusso principale\n            try {\n              setLoading(true);\n\n              // Imposta sempre forceOver a true per evitare blocchi quando la bobina va in OVER\n              console.log('Impostando forceOver a true nel dialogo di conferma per garantire il completamento dell\\'operazione');\n              await caviService.updateMetriPosati(cantiereId, formData.id_cavo, metriPosati, idBobina, true // Forza sempre a true per evitare blocchi\n              );\n\n              // Messaggio di successo con dettagli sulla bobina\n              let successMessage = `Metri posati aggiornati con successo. Stato cavo: ${statoInstallazione}`;\n              if (idBobina === 'BOBINA_VUOTA') {\n                successMessage += '. Cavo associato a BOBINA VUOTA';\n              } else if (idBobina) {\n                const bobina = bobine.find(b => b.id_bobina === idBobina);\n                if (bobina) {\n                  successMessage += `. Cavo associato alla bobina ${idBobina}`;\n                }\n              }\n\n              // Gestione successo\n              onSuccess(successMessage);\n\n              // Reset del form\n              handleReset();\n\n              // Ricarica i cavi\n              loadCavi();\n            } catch (error) {\n              console.error('Errore durante l\\'aggiornamento dei metri posati:', error);\n\n              // Gestione speciale per BOBINA_VUOTA\n              if (idBobina === 'BOBINA_VUOTA' && error.success) {\n                // Se è un \"errore\" di successo per BOBINA_VUOTA, trattiamolo come un successo\n                let successMessage = `Metri posati aggiornati con successo. Stato cavo: ${statoInstallazione}. Cavo associato a BOBINA VUOTA`;\n                onSuccess(successMessage);\n\n                // Reset del form\n                handleReset();\n\n                // Ricarica i cavi\n                loadCavi();\n                return;\n              }\n\n              // Gestione dettagliata degli errori\n              let errorMessage = 'Errore durante l\\'aggiornamento dei metri posati';\n              if (error.response) {\n                var _error$response$data;\n                // Il server ha risposto con un codice di errore\n                const status = error.response.status;\n                const detail = ((_error$response$data = error.response.data) === null || _error$response$data === void 0 ? void 0 : _error$response$data.detail) || error.message;\n                if (status === 400) {\n                  // Errore di validazione\n                  if (detail.includes('metri residui')) {\n                    errorMessage = `La bobina non ha metri residui sufficienti. Usa l'opzione \"BOBINA VUOTA\" o seleziona un'altra bobina.`;\n                  } else if (detail.includes('già posato')) {\n                    errorMessage = `Il cavo risulta già posato. Usa la funzione \"Modifica bobina cavo posato\".`;\n                  } else {\n                    errorMessage = detail;\n                  }\n                } else if (status === 404) {\n                  // Gestione speciale per BOBINA_VUOTA quando la bobina non viene trovata\n                  if (idBobina === 'BOBINA_VUOTA' && detail.includes('non trovata')) {\n                    let successMessage = `Metri posati aggiornati con successo. Stato cavo: ${statoInstallazione}. Cavo associato a BOBINA VUOTA`;\n                    onSuccess(successMessage);\n\n                    // Reset del form\n                    handleReset();\n\n                    // Ricarica i cavi\n                    loadCavi();\n                    return;\n                  } else {\n                    errorMessage = `Cavo o bobina non trovati: ${detail}`;\n                  }\n                } else {\n                  errorMessage = `Errore del server (${status}): ${detail}`;\n                }\n              } else if (error.request) {\n                // La richiesta è stata inviata ma non è stata ricevuta risposta\n                errorMessage = 'Nessuna risposta dal server. Verifica la connessione di rete.';\n              } else if (error.detail && idBobina === 'BOBINA_VUOTA') {\n                // Gestione speciale per errori con BOBINA_VUOTA\n                errorMessage = error.detail;\n                if (error.status === 200 || error.success) {\n                  let successMessage = `Metri posati aggiornati con successo. Stato cavo: ${statoInstallazione}. Cavo associato a BOBINA VUOTA`;\n                  onSuccess(successMessage);\n\n                  // Reset del form\n                  handleReset();\n\n                  // Ricarica i cavi\n                  loadCavi();\n                  return;\n                }\n              } else {\n                // Errore durante la configurazione della richiesta\n                errorMessage = error.message || error.detail || 'Errore sconosciuto';\n              }\n              onError(errorMessage);\n            } finally {\n              setLoading(false);\n            }\n          }\n        });\n        setShowConfirmDialog(true);\n        setLoading(false);\n        return;\n      }\n\n      // Chiamata API\n      console.log('Invio richiesta updateMetriPosati con i seguenti parametri:');\n      console.log('- cantiereId:', cantiereId);\n      console.log('- id_cavo:', formData.id_cavo);\n      console.log('- metri_posati:', metriPosati);\n      console.log('- id_bobina:', idBobina, typeof idBobina);\n      console.log('- forceOver:', forceOver);\n\n      // Imposta sempre forceOver a true per evitare blocchi quando la bobina va in OVER\n      console.log('Impostando forceOver a true nella chiamata diretta per garantire il completamento dell\\'operazione');\n      await caviService.updateMetriPosati(cantiereId, formData.id_cavo, metriPosati, idBobina, true // Forza sempre a true per evitare blocchi\n      );\n\n      // Messaggio di successo con dettagli sulla bobina\n      let successMessage = `Metri posati aggiornati con successo. Stato cavo: ${statoInstallazione}`;\n      if (idBobina === 'BOBINA_VUOTA') {\n        successMessage += '. Cavo associato a BOBINA VUOTA';\n      } else if (idBobina) {\n        const bobina = bobine.find(b => b.id_bobina === idBobina);\n        if (bobina) {\n          successMessage += `. Cavo associato alla bobina ${idBobina}`;\n        }\n      }\n\n      // Gestione successo\n      onSuccess(successMessage);\n\n      // Reset del form\n      handleReset();\n\n      // Ricarica i cavi\n      loadCavi();\n    } catch (error) {\n      console.error('Errore durante l\\'aggiornamento dei metri posati:', error);\n\n      // Gestione speciale per BOBINA_VUOTA\n      if (idBobina === 'BOBINA_VUOTA' && error.success) {\n        // Se è un \"errore\" di successo per BOBINA_VUOTA, trattiamolo come un successo\n        let successMessage = `Metri posati aggiornati con successo. Stato cavo: ${statoInstallazione}. Cavo associato a BOBINA VUOTA`;\n        onSuccess(successMessage);\n\n        // Reset del form\n        handleReset();\n\n        // Ricarica i cavi\n        loadCavi();\n        return;\n      }\n\n      // Gestione dettagliata degli errori\n      let errorMessage = 'Errore durante l\\'aggiornamento dei metri posati';\n      if (error.response) {\n        var _error$response$data2;\n        // Il server ha risposto con un codice di errore\n        const status = error.response.status;\n        const detail = ((_error$response$data2 = error.response.data) === null || _error$response$data2 === void 0 ? void 0 : _error$response$data2.detail) || error.message;\n        if (status === 400) {\n          // Errore di validazione\n          if (detail.includes('metri residui')) {\n            errorMessage = `La bobina non ha metri residui sufficienti. Usa l'opzione \"BOBINA VUOTA\" o seleziona un'altra bobina.`;\n          } else if (detail.includes('già posato')) {\n            errorMessage = `Il cavo risulta già posato. Usa la funzione \"Modifica bobina cavo posato\".`;\n          } else {\n            errorMessage = detail;\n          }\n        } else if (status === 404) {\n          // Gestione speciale per BOBINA_VUOTA quando la bobina non viene trovata\n          if (idBobina === 'BOBINA_VUOTA' && detail.includes('non trovata')) {\n            let successMessage = `Metri posati aggiornati con successo. Stato cavo: ${statoInstallazione}. Cavo associato a BOBINA VUOTA`;\n            onSuccess(successMessage);\n\n            // Reset del form\n            handleReset();\n\n            // Ricarica i cavi\n            loadCavi();\n            return;\n          } else {\n            errorMessage = `Cavo o bobina non trovati: ${detail}`;\n          }\n        } else {\n          errorMessage = `Errore del server (${status}): ${detail}`;\n        }\n      } else if (error.request) {\n        // La richiesta è stata inviata ma non è stata ricevuta risposta\n        errorMessage = 'Nessuna risposta dal server. Verifica la connessione di rete.';\n      } else if (error.detail && idBobina === 'BOBINA_VUOTA') {\n        // Gestione speciale per errori con BOBINA_VUOTA\n        errorMessage = error.detail;\n        if (error.status === 200 || error.success) {\n          let successMessage = `Metri posati aggiornati con successo. Stato cavo: ${statoInstallazione}. Cavo associato a BOBINA VUOTA`;\n          onSuccess(successMessage);\n\n          // Reset del form\n          handleReset();\n\n          // Ricarica i cavi\n          loadCavi();\n          return;\n        }\n      } else {\n        // Errore durante la configurazione della richiesta\n        errorMessage = error.message || error.detail || 'Errore sconosciuto';\n      }\n      onError(errorMessage);\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  // Renderizza il passo 1: Selezione del cavo\n  const renderStep1 = () => {\n    return /*#__PURE__*/_jsxDEV(Box, {\n      children: [/*#__PURE__*/_jsxDEV(Typography, {\n        variant: \"subtitle2\",\n        sx: {\n          mb: 1,\n          fontWeight: 'bold'\n        },\n        children: \"Seleziona un cavo\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 1034,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Paper, {\n        sx: {\n          p: 1.5,\n          mb: 2,\n          width: '100%'\n        },\n        children: /*#__PURE__*/_jsxDEV(Box, {\n          sx: {\n            display: 'flex',\n            alignItems: 'center',\n            width: '100%'\n          },\n          children: [/*#__PURE__*/_jsxDEV(Typography, {\n            variant: \"subtitle2\",\n            sx: {\n              mr: 1,\n              minWidth: '80px'\n            },\n            children: \"Cerca cavo\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 1041,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(TextField, {\n            size: \"small\",\n            label: \"ID Cavo\",\n            variant: \"outlined\",\n            value: cavoIdInput,\n            onChange: e => setCavoIdInput(e.target.value),\n            placeholder: \"Inserisci ID cavo (separati da virgola per ricerca multipla)\",\n            sx: {\n              flexGrow: 0,\n              width: '250px',\n              mr: 1\n            }\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 1044,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Button, {\n            variant: \"contained\",\n            color: \"primary\",\n            onClick: handleSearchCavoById,\n            disabled: caviLoading || !cavoIdInput.trim(),\n            startIcon: caviLoading ? /*#__PURE__*/_jsxDEV(CircularProgress, {\n              size: 16\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1058,\n              columnNumber: 40\n            }, this) : /*#__PURE__*/_jsxDEV(SearchIcon, {\n              fontSize: \"small\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1058,\n              columnNumber: 73\n            }, this),\n            size: \"small\",\n            sx: {\n              minWidth: '80px',\n              height: '36px',\n              mr: 2\n            },\n            children: \"CERCA\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 1053,\n            columnNumber: 13\n          }, this), selectedCavo && /*#__PURE__*/_jsxDEV(Box, {\n            sx: {\n              display: 'flex',\n              alignItems: 'center',\n              flexGrow: 1,\n              flexWrap: 'nowrap',\n              overflow: 'hidden',\n              ml: 4\n            },\n            children: [/*#__PURE__*/_jsxDEV(Box, {\n              sx: {\n                display: 'flex',\n                alignItems: 'center',\n                mr: 2\n              },\n              children: [/*#__PURE__*/_jsxDEV(Typography, {\n                variant: \"subtitle2\",\n                sx: {\n                  fontWeight: 'bold',\n                  whiteSpace: 'nowrap',\n                  mr: 1,\n                  fontSize: '0.95rem'\n                },\n                children: [\"Cavo: \", /*#__PURE__*/_jsxDEV(\"span\", {\n                  style: {\n                    color: '#1976d2'\n                  },\n                  children: selectedCavo.id_cavo\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1070,\n                  columnNumber: 27\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 1069,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(Divider, {\n                orientation: \"vertical\",\n                flexItem: true,\n                sx: {\n                  mx: 1.5\n                }\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1072,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(Box, {\n                sx: {\n                  display: 'flex',\n                  alignItems: 'center',\n                  gap: 3,\n                  flexWrap: 'nowrap',\n                  overflow: 'hidden'\n                },\n                children: [/*#__PURE__*/_jsxDEV(Box, {\n                  sx: {\n                    display: 'flex',\n                    alignItems: 'center',\n                    whiteSpace: 'nowrap'\n                  },\n                  children: [/*#__PURE__*/_jsxDEV(Typography, {\n                    variant: \"body2\",\n                    sx: {\n                      fontWeight: 'medium',\n                      fontSize: '0.9rem',\n                      mr: 0.5\n                    },\n                    children: \"Tipo:\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1075,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                    variant: \"body2\",\n                    sx: {\n                      fontSize: '0.9rem'\n                    },\n                    children: selectedCavo.tipologia || 'N/A'\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1076,\n                    columnNumber: 23\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1074,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(Box, {\n                  sx: {\n                    display: 'flex',\n                    alignItems: 'center',\n                    whiteSpace: 'nowrap'\n                  },\n                  children: [/*#__PURE__*/_jsxDEV(Typography, {\n                    variant: \"body2\",\n                    sx: {\n                      fontWeight: 'medium',\n                      fontSize: '0.9rem',\n                      mr: 0.5\n                    },\n                    children: \"Form:\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1079,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                    variant: \"body2\",\n                    sx: {\n                      fontSize: '0.9rem'\n                    },\n                    children: selectedCavo.sezione || 'N/A'\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1080,\n                    columnNumber: 23\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1078,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(Box, {\n                  sx: {\n                    display: 'flex',\n                    alignItems: 'center',\n                    whiteSpace: 'nowrap'\n                  },\n                  children: [/*#__PURE__*/_jsxDEV(Typography, {\n                    variant: \"body2\",\n                    sx: {\n                      fontWeight: 'medium',\n                      fontSize: '0.9rem',\n                      mr: 0.5\n                    },\n                    children: \"Metri:\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1083,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                    variant: \"body2\",\n                    sx: {\n                      fontSize: '0.9rem'\n                    },\n                    children: [selectedCavo.metri_teorici || 'N/A', \" m\"]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1084,\n                    columnNumber: 23\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1082,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(Box, {\n                  sx: {\n                    display: 'flex',\n                    alignItems: 'center',\n                    whiteSpace: 'nowrap'\n                  },\n                  children: [/*#__PURE__*/_jsxDEV(Typography, {\n                    variant: \"body2\",\n                    sx: {\n                      fontWeight: 'medium',\n                      fontSize: '0.9rem',\n                      mr: 0.5\n                    },\n                    children: \"Stato:\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1087,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(Chip, {\n                    size: \"small\",\n                    label: selectedCavo.stato_installazione || 'N/D',\n                    color: getCableStateColor(selectedCavo.stato_installazione),\n                    sx: {\n                      height: '22px',\n                      '& .MuiChip-label': {\n                        px: 1,\n                        py: 0,\n                        fontSize: '0.85rem'\n                      }\n                    }\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1088,\n                    columnNumber: 23\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1086,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 1073,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 1068,\n              columnNumber: 17\n            }, this), formData.id_bobina && /*#__PURE__*/_jsxDEV(_Fragment, {\n              children: [/*#__PURE__*/_jsxDEV(Divider, {\n                orientation: \"vertical\",\n                flexItem: true,\n                sx: {\n                  mx: 2\n                }\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1100,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(Box, {\n                sx: {\n                  display: 'flex',\n                  alignItems: 'center'\n                },\n                children: [/*#__PURE__*/_jsxDEV(Typography, {\n                  variant: \"subtitle2\",\n                  sx: {\n                    fontWeight: 'bold',\n                    whiteSpace: 'nowrap',\n                    mr: 1,\n                    fontSize: '0.95rem',\n                    color: '#2e7d32'\n                  },\n                  children: [\"Bobina: \", /*#__PURE__*/_jsxDEV(\"span\", {\n                    children: formData.id_bobina === 'BOBINA_VUOTA' ? 'VUOTA' : getBobinaNumber(formData.id_bobina)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1103,\n                    columnNumber: 33\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1102,\n                  columnNumber: 23\n                }, this), (() => {\n                  if (formData.id_bobina === 'BOBINA_VUOTA') {\n                    return /*#__PURE__*/_jsxDEV(Box, {\n                      sx: {\n                        display: 'flex',\n                        alignItems: 'center',\n                        whiteSpace: 'nowrap'\n                      },\n                      children: /*#__PURE__*/_jsxDEV(Typography, {\n                        variant: \"body2\",\n                        sx: {\n                          fontSize: '0.9rem',\n                          color: 'text.secondary',\n                          fontStyle: 'italic'\n                        },\n                        children: \"(Cavo posato senza bobina specifica)\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 1109,\n                        columnNumber: 31\n                      }, this)\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1108,\n                      columnNumber: 29\n                    }, this);\n                  }\n                  const bobina = bobine.find(b => b.id_bobina === formData.id_bobina);\n                  return bobina ? /*#__PURE__*/_jsxDEV(Box, {\n                    sx: {\n                      display: 'flex',\n                      alignItems: 'center',\n                      gap: 3,\n                      flexWrap: 'nowrap',\n                      overflow: 'hidden'\n                    },\n                    children: [/*#__PURE__*/_jsxDEV(Box, {\n                      sx: {\n                        display: 'flex',\n                        alignItems: 'center',\n                        whiteSpace: 'nowrap'\n                      },\n                      children: [/*#__PURE__*/_jsxDEV(Typography, {\n                        variant: \"body2\",\n                        sx: {\n                          fontWeight: 'medium',\n                          fontSize: '0.9rem',\n                          mr: 0.5\n                        },\n                        children: \"Residui:\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 1120,\n                        columnNumber: 31\n                      }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                        variant: \"body2\",\n                        sx: {\n                          fontSize: '0.9rem',\n                          color: bobina.metri_residui < parseFloat(formData.metri_posati || 0) ? 'error.main' : 'success.main',\n                          fontWeight: 'bold'\n                        },\n                        children: [bobina.metri_residui || 0, \" m\"]\n                      }, void 0, true, {\n                        fileName: _jsxFileName,\n                        lineNumber: 1121,\n                        columnNumber: 31\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1119,\n                      columnNumber: 29\n                    }, this), /*#__PURE__*/_jsxDEV(Box, {\n                      sx: {\n                        display: 'flex',\n                        alignItems: 'center',\n                        whiteSpace: 'nowrap'\n                      },\n                      children: [/*#__PURE__*/_jsxDEV(Typography, {\n                        variant: \"body2\",\n                        sx: {\n                          fontWeight: 'medium',\n                          fontSize: '0.9rem',\n                          mr: 0.5\n                        },\n                        children: \"Stato:\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 1126,\n                        columnNumber: 31\n                      }, this), /*#__PURE__*/_jsxDEV(Chip, {\n                        size: \"small\",\n                        label: bobina.stato_bobina || 'N/D',\n                        color: getReelStateColor(bobina.stato_bobina),\n                        sx: {\n                          height: '22px',\n                          '& .MuiChip-label': {\n                            px: 1,\n                            py: 0,\n                            fontSize: '0.85rem'\n                          }\n                        }\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 1127,\n                        columnNumber: 31\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1125,\n                      columnNumber: 29\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1118,\n                    columnNumber: 27\n                  }, this) : null;\n                })()]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 1101,\n                columnNumber: 21\n              }, this)]\n            }, void 0, true)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 1067,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 1040,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 1039,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Paper, {\n        sx: {\n          p: 1.5,\n          width: '100%'\n        },\n        children: [/*#__PURE__*/_jsxDEV(Typography, {\n          variant: \"subtitle2\",\n          sx: {\n            mb: 1\n          },\n          children: \"Seleziona dalla lista\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 1149,\n          columnNumber: 11\n        }, this), caviLoading ? /*#__PURE__*/_jsxDEV(Box, {\n          sx: {\n            display: 'flex',\n            justifyContent: 'center',\n            my: 2\n          },\n          children: /*#__PURE__*/_jsxDEV(CircularProgress, {\n            size: 24\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 1155,\n            columnNumber: 15\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 1154,\n          columnNumber: 13\n        }, this) : cavi.length === 0 ? /*#__PURE__*/_jsxDEV(Alert, {\n          severity: \"info\",\n          sx: {\n            py: 0.5\n          },\n          children: /*#__PURE__*/_jsxDEV(Typography, {\n            variant: \"caption\",\n            children: \"Non ci sono cavi disponibili da installare.\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 1159,\n            columnNumber: 15\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 1158,\n          columnNumber: 13\n        }, this) : /*#__PURE__*/_jsxDEV(TableContainer, {\n          component: Paper,\n          variant: \"outlined\",\n          sx: {\n            maxHeight: '300px',\n            overflow: 'auto',\n            width: '100%'\n          },\n          children: /*#__PURE__*/_jsxDEV(Table, {\n            size: \"small\",\n            stickyHeader: true,\n            children: [/*#__PURE__*/_jsxDEV(TableHead, {\n              children: /*#__PURE__*/_jsxDEV(TableRow, {\n                sx: {\n                  '& th': {\n                    fontWeight: 'bold',\n                    py: 1,\n                    bgcolor: '#f5f5f5'\n                  }\n                },\n                children: [/*#__PURE__*/_jsxDEV(TableCell, {\n                  children: \"ID Cavo\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1166,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n                  children: \"Tipologia\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1167,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n                  children: \"Formazione\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1168,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n                  children: \"Metri\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1169,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n                  children: \"Stato\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1170,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n                  align: \"center\",\n                  sx: {\n                    width: '40px'\n                  },\n                  children: \"Info\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1171,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 1165,\n                columnNumber: 19\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1164,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(TableBody, {\n              children: cavi.map(cavo => /*#__PURE__*/_jsxDEV(TableRow, {\n                hover: true,\n                onClick: () => handleCavoSelect(cavo),\n                sx: {\n                  cursor: 'pointer',\n                  '&:hover': {\n                    bgcolor: '#f1f8e9'\n                  },\n                  '& td': {\n                    py: 0.5\n                  }\n                },\n                children: [/*#__PURE__*/_jsxDEV(TableCell, {\n                  sx: {\n                    fontWeight: 'medium'\n                  },\n                  children: cavo.id_cavo\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1186,\n                  columnNumber: 23\n                }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n                  children: cavo.tipologia || 'N/A'\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1187,\n                  columnNumber: 23\n                }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n                  children: cavo.sezione || 'N/A'\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1188,\n                  columnNumber: 23\n                }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n                  children: cavo.metri_teorici || 'N/A'\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1189,\n                  columnNumber: 23\n                }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n                  children: /*#__PURE__*/_jsxDEV(Chip, {\n                    size: \"small\",\n                    label: isCableSpare(cavo) ? 'SPARE' : isCableInstalled(cavo) ? 'Installato' : cavo.stato_installazione,\n                    color: isCableSpare(cavo) ? 'error' : isCableInstalled(cavo) ? 'success' : getCableStateColor(cavo.stato_installazione),\n                    sx: {\n                      height: '20px',\n                      '& .MuiChip-label': {\n                        px: 1,\n                        py: 0,\n                        fontSize: '0.7rem'\n                      }\n                    }\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1191,\n                    columnNumber: 25\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1190,\n                  columnNumber: 23\n                }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n                  align: \"center\",\n                  children: /*#__PURE__*/_jsxDEV(IconButton, {\n                    size: \"small\",\n                    onClick: e => {\n                      e.stopPropagation();\n                      setSelectedCavo(cavo);\n                      setShowCavoDetailsDialog(true);\n                    },\n                    children: /*#__PURE__*/_jsxDEV(InfoIcon, {\n                      fontSize: \"small\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1207,\n                      columnNumber: 27\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1199,\n                    columnNumber: 25\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1198,\n                  columnNumber: 23\n                }, this)]\n              }, cavo.id_cavo, true, {\n                fileName: _jsxFileName,\n                lineNumber: 1176,\n                columnNumber: 21\n              }, this))\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1174,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 1163,\n            columnNumber: 15\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 1162,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 1148,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 1033,\n      columnNumber: 7\n    }, this);\n  };\n\n  // Renderizza il passo 2: Inserimento metri\n  const renderStep2 = () => {\n    if (!selectedCavo) return null;\n    return /*#__PURE__*/_jsxDEV(Box, {\n      children: [/*#__PURE__*/_jsxDEV(Typography, {\n        variant: \"subtitle2\",\n        sx: {\n          mb: 1,\n          fontWeight: 'bold'\n        },\n        children: \"Inserisci metri posati\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 1227,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Paper, {\n        sx: {\n          p: 2,\n          width: '100%'\n        },\n        children: [/*#__PURE__*/_jsxDEV(Typography, {\n          variant: \"subtitle2\",\n          gutterBottom: true,\n          sx: {\n            fontWeight: 'bold'\n          },\n          children: \"Inserisci i metri posati\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 1232,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Box, {\n          sx: {\n            mt: 2,\n            mb: 2,\n            width: '100%'\n          },\n          children: [/*#__PURE__*/_jsxDEV(Typography, {\n            variant: \"subtitle2\",\n            gutterBottom: true,\n            sx: {\n              fontWeight: 'bold'\n            },\n            children: \"Metratura posata\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 1239,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(TextField, {\n            size: \"small\",\n            label: \"Metri posati\",\n            variant: \"outlined\",\n            name: \"metri_posati\",\n            type: \"number\",\n            value: formData.metri_posati,\n            onChange: handleFormChange,\n            error: !!formErrors.metri_posati,\n            helperText: formErrors.metri_posati || formWarnings.metri_posati,\n            FormHelperTextProps: {\n              sx: {\n                color: formWarnings.metri_posati && !formErrors.metri_posati ? 'warning.main' : 'error.main'\n              }\n            },\n            sx: {\n              mb: 1,\n              width: '200px'\n            },\n            inputProps: {\n              max: 999999,\n              // Limite a 6 cifre\n              step: 0.1\n            }\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 1242,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 1238,\n          columnNumber: 11\n        }, this), formWarnings.metri_posati && !formErrors.metri_posati && !notificationShown && /*#__PURE__*/_jsxDEV(Alert, {\n          severity: \"warning\",\n          sx: {\n            mb: 2\n          },\n          children: formWarnings.metri_posati\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 1264,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(Alert, {\n          severity: \"info\",\n          sx: {\n            mt: 2\n          },\n          children: /*#__PURE__*/_jsxDEV(Typography, {\n            variant: \"body2\",\n            children: \"Inserisci i metri di cavo effettivamente posati. Questo valore aggiorna lo stato di installazione del cavo.\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 1270,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 1269,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 1231,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 1226,\n      columnNumber: 7\n    }, this);\n  };\n\n  // Renderizza il passo 3: Associazione bobina\n  const renderStep3 = () => {\n    // Funzione per costruire l'ID completo della bobina\n    const buildFullBobinaId = numeroBobina => {\n      return `C${cantiereId}_B${numeroBobina}`;\n    };\n\n    // Verifica se una bobina ha metri residui sufficienti\n    const hasSufficientMeters = bobina => {\n      if (!bobina || !formData.metri_posati) return true;\n      return parseFloat(bobina.metri_residui) >= parseFloat(formData.metri_posati);\n    };\n\n    // Filtra le bobine in base al testo di ricerca\n    const bobineFiltrate = bobine.filter(bobina => {\n      // Se non c'è testo di ricerca, mostra tutte le bobine\n      if (!searchText) return true;\n\n      // Dividi il testo di ricerca in termini separati da virgola\n      const searchTerms = searchText.split(',').map(term => term.trim().toLowerCase()).filter(term => term.length > 0);\n\n      // Se ci sono due termini identici (es. \"a,a\"), cerca una corrispondenza esatta\n      if (searchTerms.length === 2 && searchTerms[0] === searchTerms[1]) {\n        const exactMatch = getBobinaNumber(bobina.id_bobina).toLowerCase() === searchTerms[0] || String(bobina.tipologia || '').toLowerCase() === searchTerms[0] || String(bobina.sezione || '').toLowerCase() === searchTerms[0];\n        return exactMatch;\n      }\n\n      // Altrimenti, cerca corrispondenze parziali per ciascun termine\n      return searchTerms.some(term => {\n        // Converti il termine di ricerca in stringa per gestire correttamente i numeri\n        const termStr = String(term);\n\n        // Verifica se il termine è un numero\n        const isNumericTerm = !isNaN(termStr) && !isNaN(parseFloat(termStr));\n\n        // Se il termine è un numero, cerca corrispondenze esatte nei campi numerici\n        if (isNumericTerm) {\n          const numericTerm = parseFloat(termStr);\n          const bobinaSezioneNum = parseFloat(String(bobina.sezione || '0'));\n\n          // Verifica corrispondenza esatta per numeri\n          if (bobinaSezioneNum === numericTerm) {\n            return true;\n          }\n\n          // Verifica anche se il numero è contenuto nell'ID della bobina\n          if (getBobinaNumber(bobina.id_bobina).includes(termStr)) {\n            return true;\n          }\n        }\n\n        // Altrimenti usa la ricerca standard con includes\n        return getBobinaNumber(bobina.id_bobina).toLowerCase().includes(term) || String(bobina.tipologia || '').toLowerCase().includes(term) || String(bobina.sezione || '').toLowerCase().includes(term);\n      });\n    });\n\n    // Separa le bobine compatibili e non compatibili\n    const bobineCompatibili = selectedCavo ? bobineFiltrate.filter(bobina => String(bobina.tipologia || '').trim() === String(selectedCavo.tipologia || '').trim() && String(bobina.sezione || '0').trim() === String(selectedCavo.sezione || '0').trim()) : bobineFiltrate;\n    const bobineNonCompatibili = selectedCavo ? bobineFiltrate.filter(bobina => String(bobina.tipologia || '').trim() !== String(selectedCavo.tipologia || '').trim() || String(bobina.sezione || '0').trim() !== String(selectedCavo.sezione || '0').trim()) : [];\n    return /*#__PURE__*/_jsxDEV(Box, {\n      children: [/*#__PURE__*/_jsxDEV(Box, {\n        sx: {\n          mb: 1\n        },\n        children: [/*#__PURE__*/_jsxDEV(Typography, {\n          variant: \"subtitle1\",\n          sx: {\n            fontWeight: 'bold',\n            display: 'inline',\n            fontSize: '1.1rem'\n          },\n          children: \"Associa bobina\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 1360,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Typography, {\n          variant: \"body2\",\n          sx: {\n            display: 'inline',\n            ml: 1,\n            color: 'text.secondary'\n          },\n          children: \"(Seleziona una bobina da associare al cavo o usa \\\"BOBINA VUOTA\\\" se non desideri associare una bobina specifica)\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 1363,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 1359,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Paper, {\n        sx: {\n          p: 2,\n          width: '100%'\n        },\n        children: [/*#__PURE__*/_jsxDEV(Box, {\n          sx: {\n            mb: 3\n          },\n          children: /*#__PURE__*/_jsxDEV(Box, {\n            sx: {\n              display: 'flex',\n              alignItems: 'center',\n              gap: 2\n            },\n            children: [/*#__PURE__*/_jsxDEV(TextField, {\n              size: \"small\",\n              label: \"Metri posati\",\n              variant: \"outlined\",\n              name: \"metri_posati\",\n              type: \"number\",\n              value: formData.metri_posati,\n              onChange: handleFormChange,\n              error: !!formErrors.metri_posati,\n              helperText: formErrors.metri_posati,\n              sx: {\n                width: '200px'\n              },\n              inputProps: {\n                max: 999999,\n                // Limite a 6 cifre\n                step: 0.1\n              }\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1372,\n              columnNumber: 15\n            }, this), formWarnings.metri_posati && !formErrors.metri_posati && /*#__PURE__*/_jsxDEV(Typography, {\n              variant: \"body2\",\n              sx: {\n                color: 'warning.main',\n                fontWeight: 'medium',\n                fontSize: '0.875rem'\n              },\n              children: formWarnings.metri_posati\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1390,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 1371,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 1370,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Divider, {\n          sx: {\n            my: 2\n          }\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 1406,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Box, {\n          sx: {\n            mb: 2\n          },\n          children: /*#__PURE__*/_jsxDEV(Box, {\n            sx: {\n              display: 'flex',\n              alignItems: 'center',\n              gap: 2,\n              mb: 2\n            },\n            children: [/*#__PURE__*/_jsxDEV(TextField, {\n              size: \"small\",\n              label: \"Cerca\",\n              variant: \"outlined\",\n              value: searchText,\n              onChange: handleSearchTextChange,\n              placeholder: \"ID, tipologia... (usa a,a per selezionare esattamente 'a')\",\n              InputProps: {\n                startAdornment: /*#__PURE__*/_jsxDEV(InputAdornment, {\n                  position: \"start\",\n                  children: /*#__PURE__*/_jsxDEV(SearchIcon, {\n                    fontSize: \"small\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1422,\n                    columnNumber: 23\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1421,\n                  columnNumber: 21\n                }, this),\n                endAdornment: searchText ? /*#__PURE__*/_jsxDEV(InputAdornment, {\n                  position: \"end\",\n                  children: /*#__PURE__*/_jsxDEV(IconButton, {\n                    size: \"small\",\n                    \"aria-label\": \"clear search\",\n                    onClick: () => setSearchText(''),\n                    edge: \"end\",\n                    children: /*#__PURE__*/_jsxDEV(CancelIcon, {\n                      fontSize: \"small\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1433,\n                      columnNumber: 25\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1427,\n                    columnNumber: 23\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1426,\n                  columnNumber: 21\n                }, this) : null\n              },\n              sx: {\n                width: '200px'\n              }\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1412,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Button, {\n              variant: \"outlined\",\n              size: \"small\",\n              onClick: () => {\n                console.log('Selezionata BOBINA_VUOTA');\n                handleSelectBobina('BOBINA_VUOTA');\n              },\n              sx: {\n                height: '40px',\n                fontWeight: formData.id_bobina === 'BOBINA_VUOTA' ? 'bold' : 'normal',\n                bgcolor: formData.id_bobina === 'BOBINA_VUOTA' ? 'rgba(76, 175, 80, 0.12)' : 'inherit',\n                border: formData.id_bobina === 'BOBINA_VUOTA' ? '1px solid #4caf50' : undefined\n              },\n              children: \"BOBINA VUOTA\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1441,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 1410,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 1408,\n          columnNumber: 11\n        }, this), bobineLoading ? /*#__PURE__*/_jsxDEV(Box, {\n          sx: {\n            display: 'flex',\n            justifyContent: 'center',\n            my: 1\n          },\n          children: /*#__PURE__*/_jsxDEV(CircularProgress, {\n            size: 24\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 1459,\n            columnNumber: 15\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 1458,\n          columnNumber: 13\n        }, this) : /*#__PURE__*/_jsxDEV(Box, {\n          children: /*#__PURE__*/_jsxDEV(Grid, {\n            container: true,\n            spacing: 2,\n            children: [/*#__PURE__*/_jsxDEV(Grid, {\n              item: true,\n              xs: 12,\n              md: 6,\n              children: /*#__PURE__*/_jsxDEV(Paper, {\n                variant: \"outlined\",\n                sx: {\n                  p: 2,\n                  height: '100%'\n                },\n                children: [/*#__PURE__*/_jsxDEV(Typography, {\n                  variant: \"subtitle2\",\n                  sx: {\n                    fontWeight: 'bold',\n                    mb: 1\n                  },\n                  children: \"ELENCO BOBINE COMPATIBILI\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1470,\n                  columnNumber: 21\n                }, this), bobineCompatibili.length > 0 ? /*#__PURE__*/_jsxDEV(_Fragment, {\n                  children: [/*#__PURE__*/_jsxDEV(Box, {\n                    sx: {\n                      display: 'flex',\n                      alignItems: 'center',\n                      width: '100%',\n                      py: 0.8,\n                      px: 1.8,\n                      bgcolor: '#f5f5f5',\n                      borderRadius: 1,\n                      mb: 1\n                    },\n                    children: [/*#__PURE__*/_jsxDEV(Box, {\n                      sx: {\n                        width: '60px',\n                        mr: 2\n                      },\n                      children: /*#__PURE__*/_jsxDEV(Typography, {\n                        variant: \"caption\",\n                        sx: {\n                          fontWeight: 'bold',\n                          fontSize: '0.85rem'\n                        },\n                        children: \"ID\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 1478,\n                        columnNumber: 29\n                      }, this)\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1477,\n                      columnNumber: 27\n                    }, this), /*#__PURE__*/_jsxDEV(Box, {\n                      sx: {\n                        width: '120px',\n                        mr: 2\n                      },\n                      children: /*#__PURE__*/_jsxDEV(Typography, {\n                        variant: \"caption\",\n                        sx: {\n                          fontWeight: 'bold',\n                          fontSize: '0.85rem'\n                        },\n                        children: \"Tipo\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 1481,\n                        columnNumber: 29\n                      }, this)\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1480,\n                      columnNumber: 27\n                    }, this), /*#__PURE__*/_jsxDEV(Box, {\n                      sx: {\n                        width: '100px',\n                        mr: 2\n                      },\n                      children: /*#__PURE__*/_jsxDEV(Typography, {\n                        variant: \"caption\",\n                        sx: {\n                          fontWeight: 'bold',\n                          fontSize: '0.85rem'\n                        },\n                        children: \"Form.\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 1484,\n                        columnNumber: 29\n                      }, this)\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1483,\n                      columnNumber: 27\n                    }, this), /*#__PURE__*/_jsxDEV(Box, {\n                      sx: {\n                        width: '100px',\n                        mr: 2\n                      },\n                      children: /*#__PURE__*/_jsxDEV(Typography, {\n                        variant: \"caption\",\n                        sx: {\n                          fontWeight: 'bold',\n                          fontSize: '0.85rem'\n                        },\n                        children: \"Residui\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 1487,\n                        columnNumber: 29\n                      }, this)\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1486,\n                      columnNumber: 27\n                    }, this), /*#__PURE__*/_jsxDEV(Box, {\n                      sx: {\n                        flexGrow: 0\n                      },\n                      children: /*#__PURE__*/_jsxDEV(Typography, {\n                        variant: \"caption\",\n                        sx: {\n                          fontWeight: 'bold',\n                          fontSize: '0.85rem'\n                        },\n                        children: \"Stato\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 1490,\n                        columnNumber: 29\n                      }, this)\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1489,\n                      columnNumber: 27\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1476,\n                    columnNumber: 25\n                  }, this), /*#__PURE__*/_jsxDEV(List, {\n                    sx: {\n                      maxHeight: bobineCompatibili.length > 6 ? '300px' : 'auto',\n                      overflowY: bobineCompatibili.length > 6 ? 'auto' : 'visible',\n                      overflowX: 'hidden',\n                      bgcolor: 'background.paper'\n                    },\n                    children: bobineCompatibili.map(bobina => /*#__PURE__*/_jsxDEV(ListItem, {\n                      disablePadding: true,\n                      secondaryAction: /*#__PURE__*/_jsxDEV(IconButton, {\n                        edge: \"end\",\n                        size: \"small\",\n                        onClick: () => handleSelectBobina(bobina.id_bobina),\n                        children: /*#__PURE__*/_jsxDEV(AddCircleOutlineIcon, {\n                          color: \"primary\"\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 1504,\n                          columnNumber: 33\n                        }, this)\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 1499,\n                        columnNumber: 31\n                      }, this),\n                      sx: {\n                        bgcolor: formData.id_bobina === bobina.id_bobina ? 'rgba(76, 175, 80, 0.12)' : 'inherit',\n                        borderRadius: '4px',\n                        mb: 0.5,\n                        border: formData.id_bobina === bobina.id_bobina ? '1px solid #4caf50' : 'none'\n                      },\n                      children: /*#__PURE__*/_jsxDEV(ListItemButton, {\n                        dense: true,\n                        onClick: () => handleSelectBobina(bobina.id_bobina),\n                        children: /*#__PURE__*/_jsxDEV(Box, {\n                          sx: {\n                            display: 'flex',\n                            alignItems: 'center',\n                            width: '100%',\n                            py: 0.8\n                          },\n                          children: [/*#__PURE__*/_jsxDEV(Box, {\n                            sx: {\n                              width: '60px',\n                              mr: 2\n                            },\n                            children: /*#__PURE__*/_jsxDEV(Typography, {\n                              variant: \"body2\",\n                              sx: {\n                                fontWeight: 'bold',\n                                fontSize: '0.9rem'\n                              },\n                              children: getBobinaNumber(bobina.id_bobina)\n                            }, void 0, false, {\n                              fileName: _jsxFileName,\n                              lineNumber: 1520,\n                              columnNumber: 35\n                            }, this)\n                          }, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 1519,\n                            columnNumber: 33\n                          }, this), /*#__PURE__*/_jsxDEV(Box, {\n                            sx: {\n                              width: '120px',\n                              mr: 2\n                            },\n                            children: /*#__PURE__*/_jsxDEV(Typography, {\n                              variant: \"body2\",\n                              sx: {\n                                fontSize: '0.85rem'\n                              },\n                              children: bobina.tipologia || 'N/A'\n                            }, void 0, false, {\n                              fileName: _jsxFileName,\n                              lineNumber: 1525,\n                              columnNumber: 35\n                            }, this)\n                          }, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 1524,\n                            columnNumber: 33\n                          }, this), /*#__PURE__*/_jsxDEV(Box, {\n                            sx: {\n                              width: '100px',\n                              mr: 2\n                            },\n                            children: /*#__PURE__*/_jsxDEV(Typography, {\n                              variant: \"body2\",\n                              sx: {\n                                fontSize: '0.85rem'\n                              },\n                              children: bobina.sezione || 'N/A'\n                            }, void 0, false, {\n                              fileName: _jsxFileName,\n                              lineNumber: 1530,\n                              columnNumber: 35\n                            }, this)\n                          }, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 1529,\n                            columnNumber: 33\n                          }, this), /*#__PURE__*/_jsxDEV(Box, {\n                            sx: {\n                              width: '100px',\n                              mr: 2\n                            },\n                            children: /*#__PURE__*/_jsxDEV(Typography, {\n                              variant: \"body2\",\n                              sx: {\n                                fontWeight: 'bold',\n                                fontSize: '0.85rem',\n                                color: bobina.metri_residui < parseFloat(formData.metri_posati || 0) ? 'error.main' : 'success.main'\n                              },\n                              children: [bobina.metri_residui || 0, \" m\"]\n                            }, void 0, true, {\n                              fileName: _jsxFileName,\n                              lineNumber: 1535,\n                              columnNumber: 35\n                            }, this)\n                          }, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 1534,\n                            columnNumber: 33\n                          }, this), /*#__PURE__*/_jsxDEV(Box, {\n                            sx: {\n                              flexGrow: 0\n                            },\n                            children: /*#__PURE__*/_jsxDEV(Chip, {\n                              size: \"small\",\n                              label: bobina.stato_bobina || 'N/D',\n                              color: getReelStateColor(bobina.stato_bobina),\n                              variant: \"outlined\",\n                              sx: {\n                                height: 22,\n                                fontSize: '0.8rem',\n                                '& .MuiChip-label': {\n                                  px: 1,\n                                  py: 0\n                                }\n                              }\n                            }, void 0, false, {\n                              fileName: _jsxFileName,\n                              lineNumber: 1540,\n                              columnNumber: 35\n                            }, this)\n                          }, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 1539,\n                            columnNumber: 33\n                          }, this)]\n                        }, void 0, true, {\n                          fileName: _jsxFileName,\n                          lineNumber: 1518,\n                          columnNumber: 31\n                        }, this)\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 1514,\n                        columnNumber: 29\n                      }, this)\n                    }, bobina.id_bobina, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1495,\n                      columnNumber: 27\n                    }, this))\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1493,\n                    columnNumber: 23\n                  }, this)]\n                }, void 0, true) : /*#__PURE__*/_jsxDEV(Alert, {\n                  severity: \"info\",\n                  sx: {\n                    mt: 1\n                  },\n                  children: \"Nessuna bobina compatibile disponibile. Puoi usare BOBINA VUOTA o selezionare una bobina non compatibile.\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1555,\n                  columnNumber: 23\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 1469,\n                columnNumber: 19\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1468,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(Grid, {\n              item: true,\n              xs: 12,\n              md: 6,\n              children: /*#__PURE__*/_jsxDEV(Paper, {\n                variant: \"outlined\",\n                sx: {\n                  p: 2,\n                  height: '100%'\n                },\n                children: [/*#__PURE__*/_jsxDEV(Typography, {\n                  variant: \"subtitle2\",\n                  sx: {\n                    fontWeight: 'bold',\n                    mb: 1\n                  },\n                  children: \"ELENCO BOBINE NON COMPATIBILI\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1565,\n                  columnNumber: 21\n                }, this), bobineNonCompatibili.length > 0 ? /*#__PURE__*/_jsxDEV(_Fragment, {\n                  children: [/*#__PURE__*/_jsxDEV(Box, {\n                    sx: {\n                      display: 'flex',\n                      alignItems: 'center',\n                      width: '100%',\n                      py: 0.8,\n                      px: 1.8,\n                      bgcolor: '#f5f5f5',\n                      borderRadius: 1,\n                      mb: 1\n                    },\n                    children: [/*#__PURE__*/_jsxDEV(Box, {\n                      sx: {\n                        width: '60px',\n                        mr: 2\n                      },\n                      children: /*#__PURE__*/_jsxDEV(Typography, {\n                        variant: \"caption\",\n                        sx: {\n                          fontWeight: 'bold',\n                          fontSize: '0.85rem'\n                        },\n                        children: \"ID\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 1573,\n                        columnNumber: 29\n                      }, this)\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1572,\n                      columnNumber: 27\n                    }, this), /*#__PURE__*/_jsxDEV(Box, {\n                      sx: {\n                        width: '120px',\n                        mr: 2\n                      },\n                      children: /*#__PURE__*/_jsxDEV(Typography, {\n                        variant: \"caption\",\n                        sx: {\n                          fontWeight: 'bold',\n                          fontSize: '0.85rem'\n                        },\n                        children: \"Tipo\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 1576,\n                        columnNumber: 29\n                      }, this)\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1575,\n                      columnNumber: 27\n                    }, this), /*#__PURE__*/_jsxDEV(Box, {\n                      sx: {\n                        width: '100px',\n                        mr: 2\n                      },\n                      children: /*#__PURE__*/_jsxDEV(Typography, {\n                        variant: \"caption\",\n                        sx: {\n                          fontWeight: 'bold',\n                          fontSize: '0.85rem'\n                        },\n                        children: \"Form.\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 1579,\n                        columnNumber: 29\n                      }, this)\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1578,\n                      columnNumber: 27\n                    }, this), /*#__PURE__*/_jsxDEV(Box, {\n                      sx: {\n                        width: '100px',\n                        mr: 2\n                      },\n                      children: /*#__PURE__*/_jsxDEV(Typography, {\n                        variant: \"caption\",\n                        sx: {\n                          fontWeight: 'bold',\n                          fontSize: '0.85rem'\n                        },\n                        children: \"Residui\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 1582,\n                        columnNumber: 29\n                      }, this)\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1581,\n                      columnNumber: 27\n                    }, this), /*#__PURE__*/_jsxDEV(Box, {\n                      sx: {\n                        flexGrow: 0\n                      },\n                      children: /*#__PURE__*/_jsxDEV(Typography, {\n                        variant: \"caption\",\n                        sx: {\n                          fontWeight: 'bold',\n                          fontSize: '0.85rem'\n                        },\n                        children: \"Stato\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 1585,\n                        columnNumber: 29\n                      }, this)\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1584,\n                      columnNumber: 27\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1571,\n                    columnNumber: 25\n                  }, this), /*#__PURE__*/_jsxDEV(List, {\n                    sx: {\n                      maxHeight: bobineNonCompatibili.length > 6 ? '300px' : 'auto',\n                      overflowY: bobineNonCompatibili.length > 6 ? 'auto' : 'visible',\n                      overflowX: 'hidden',\n                      bgcolor: 'background.paper'\n                    },\n                    children: bobineNonCompatibili.map(bobina => /*#__PURE__*/_jsxDEV(ListItem, {\n                      disablePadding: true,\n                      secondaryAction: /*#__PURE__*/_jsxDEV(IconButton, {\n                        edge: \"end\",\n                        size: \"small\",\n                        onClick: () => handleSelectBobina(bobina.id_bobina),\n                        children: /*#__PURE__*/_jsxDEV(AddCircleOutlineIcon, {\n                          color: \"primary\"\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 1599,\n                          columnNumber: 33\n                        }, this)\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 1594,\n                        columnNumber: 31\n                      }, this),\n                      sx: {\n                        bgcolor: formData.id_bobina === bobina.id_bobina ? 'rgba(76, 175, 80, 0.12)' : 'inherit',\n                        borderRadius: '4px',\n                        mb: 0.5,\n                        border: formData.id_bobina === bobina.id_bobina ? '1px solid #4caf50' : 'none'\n                      },\n                      children: /*#__PURE__*/_jsxDEV(ListItemButton, {\n                        dense: true,\n                        onClick: () => handleSelectBobina(bobina.id_bobina),\n                        children: /*#__PURE__*/_jsxDEV(Box, {\n                          sx: {\n                            display: 'flex',\n                            alignItems: 'center',\n                            width: '100%',\n                            py: 0.8\n                          },\n                          children: [/*#__PURE__*/_jsxDEV(Box, {\n                            sx: {\n                              width: '60px',\n                              mr: 2\n                            },\n                            children: /*#__PURE__*/_jsxDEV(Typography, {\n                              variant: \"body2\",\n                              sx: {\n                                fontWeight: 'bold',\n                                fontSize: '0.9rem'\n                              },\n                              children: getBobinaNumber(bobina.id_bobina)\n                            }, void 0, false, {\n                              fileName: _jsxFileName,\n                              lineNumber: 1615,\n                              columnNumber: 35\n                            }, this)\n                          }, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 1614,\n                            columnNumber: 33\n                          }, this), /*#__PURE__*/_jsxDEV(Box, {\n                            sx: {\n                              width: '120px',\n                              mr: 2\n                            },\n                            children: /*#__PURE__*/_jsxDEV(Typography, {\n                              variant: \"body2\",\n                              sx: {\n                                fontSize: '0.85rem'\n                              },\n                              children: bobina.tipologia || 'N/A'\n                            }, void 0, false, {\n                              fileName: _jsxFileName,\n                              lineNumber: 1620,\n                              columnNumber: 35\n                            }, this)\n                          }, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 1619,\n                            columnNumber: 33\n                          }, this), /*#__PURE__*/_jsxDEV(Box, {\n                            sx: {\n                              width: '100px',\n                              mr: 2\n                            },\n                            children: /*#__PURE__*/_jsxDEV(Typography, {\n                              variant: \"body2\",\n                              sx: {\n                                fontSize: '0.85rem'\n                              },\n                              children: bobina.sezione || 'N/A'\n                            }, void 0, false, {\n                              fileName: _jsxFileName,\n                              lineNumber: 1625,\n                              columnNumber: 35\n                            }, this)\n                          }, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 1624,\n                            columnNumber: 33\n                          }, this), /*#__PURE__*/_jsxDEV(Box, {\n                            sx: {\n                              width: '100px',\n                              mr: 2\n                            },\n                            children: /*#__PURE__*/_jsxDEV(Typography, {\n                              variant: \"body2\",\n                              sx: {\n                                fontWeight: 'bold',\n                                fontSize: '0.85rem',\n                                color: bobina.metri_residui < parseFloat(formData.metri_posati || 0) ? 'error.main' : 'success.main'\n                              },\n                              children: [bobina.metri_residui || 0, \" m\"]\n                            }, void 0, true, {\n                              fileName: _jsxFileName,\n                              lineNumber: 1630,\n                              columnNumber: 35\n                            }, this)\n                          }, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 1629,\n                            columnNumber: 33\n                          }, this), /*#__PURE__*/_jsxDEV(Box, {\n                            sx: {\n                              display: 'flex',\n                              gap: 1\n                            },\n                            children: [/*#__PURE__*/_jsxDEV(Chip, {\n                              size: \"small\",\n                              label: bobina.stato_bobina || 'N/D',\n                              color: getReelStateColor(bobina.stato_bobina),\n                              variant: \"outlined\",\n                              sx: {\n                                height: 22,\n                                fontSize: '0.8rem',\n                                '& .MuiChip-label': {\n                                  px: 1,\n                                  py: 0\n                                }\n                              }\n                            }, void 0, false, {\n                              fileName: _jsxFileName,\n                              lineNumber: 1635,\n                              columnNumber: 35\n                            }, this), /*#__PURE__*/_jsxDEV(Chip, {\n                              size: \"small\",\n                              label: \"Non comp.\",\n                              color: \"warning\",\n                              variant: \"outlined\",\n                              sx: {\n                                height: 22,\n                                fontSize: '0.8rem',\n                                '& .MuiChip-label': {\n                                  px: 1,\n                                  py: 0\n                                }\n                              }\n                            }, void 0, false, {\n                              fileName: _jsxFileName,\n                              lineNumber: 1642,\n                              columnNumber: 35\n                            }, this)]\n                          }, void 0, true, {\n                            fileName: _jsxFileName,\n                            lineNumber: 1634,\n                            columnNumber: 33\n                          }, this)]\n                        }, void 0, true, {\n                          fileName: _jsxFileName,\n                          lineNumber: 1613,\n                          columnNumber: 31\n                        }, this)\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 1609,\n                        columnNumber: 29\n                      }, this)\n                    }, bobina.id_bobina, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1590,\n                      columnNumber: 27\n                    }, this))\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1588,\n                    columnNumber: 23\n                  }, this)]\n                }, void 0, true) : /*#__PURE__*/_jsxDEV(Alert, {\n                  severity: \"info\",\n                  sx: {\n                    mt: 1\n                  },\n                  children: \"Nessuna bobina non compatibile disponibile con i filtri attuali.\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1657,\n                  columnNumber: 23\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 1564,\n                columnNumber: 19\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1563,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 1466,\n            columnNumber: 15\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 1462,\n          columnNumber: 13\n        }, this), bobine.length === 0 && !bobineLoading && /*#__PURE__*/_jsxDEV(Alert, {\n          severity: \"warning\",\n          sx: {\n            mt: 2\n          },\n          children: \"Non ci sono bobine disponibili. Puoi procedere senza associare una bobina o aggiungere prima una nuova bobina.\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 1670,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(Box, {\n          sx: {\n            display: 'flex',\n            justifyContent: 'flex-end',\n            mt: 3,\n            gap: 2\n          },\n          children: [/*#__PURE__*/_jsxDEV(Button, {\n            variant: \"outlined\",\n            color: \"secondary\",\n            onClick: handleReset,\n            disabled: loading,\n            children: \"Annulla\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 1677,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Button, {\n            variant: \"contained\",\n            color: \"primary\",\n            onClick: handleSubmit,\n            disabled: loading || !selectedCavo || !formData.metri_posati || !formData.id_bobina,\n            startIcon: loading ? /*#__PURE__*/_jsxDEV(CircularProgress, {\n              size: 20\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1690,\n              columnNumber: 36\n            }, this) : null,\n            children: \"Salva\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 1685,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 1676,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 1368,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 1358,\n      columnNumber: 7\n    }, this);\n  };\n\n  // Renderizza il passo 4: Conferma\n  const renderStep4 = () => {\n    // Ottieni il numero della bobina se presente\n    let numeroBobina = 'Nessuna';\n    let bobinaInfo = null;\n    if (formData.id_bobina === 'BOBINA_VUOTA') {\n      numeroBobina = 'BOBINA VUOTA';\n    } else if (formData.id_bobina) {\n      numeroBobina = getBobinaNumber(formData.id_bobina);\n      // Trova i dettagli della bobina selezionata\n      bobinaInfo = bobine.find(b => b.id_bobina === formData.id_bobina);\n    }\n\n    // Determina lo stato di installazione\n    const statoInstallazione = determineInstallationStatus(parseFloat(formData.metri_posati), selectedCavo.metri_teorici);\n    return /*#__PURE__*/_jsxDEV(Box, {\n      children: [/*#__PURE__*/_jsxDEV(Typography, {\n        variant: \"h6\",\n        gutterBottom: true,\n        children: \"Conferma inserimento\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 1720,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Paper, {\n        sx: {\n          p: 3\n        },\n        children: [/*#__PURE__*/_jsxDEV(Typography, {\n          variant: \"subtitle1\",\n          gutterBottom: true,\n          children: \"Riepilogo dati\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 1725,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(CavoDetailsView, {\n          cavo: selectedCavo,\n          compact: true,\n          title: \"Dettagli del cavo\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 1730,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Box, {\n          sx: {\n            mt: 3,\n            p: 2,\n            bgcolor: '#f5f5f5',\n            borderRadius: 1\n          },\n          children: [/*#__PURE__*/_jsxDEV(Typography, {\n            variant: \"subtitle2\",\n            gutterBottom: true,\n            sx: {\n              fontWeight: 'bold'\n            },\n            children: \"Informazioni sull'operazione:\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 1738,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Grid, {\n            container: true,\n            spacing: 2,\n            children: [/*#__PURE__*/_jsxDEV(Grid, {\n              item: true,\n              xs: 12,\n              md: 6,\n              children: [/*#__PURE__*/_jsxDEV(Typography, {\n                variant: \"body2\",\n                children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n                  children: \"Metri Posati:\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1744,\n                  columnNumber: 19\n                }, this), \" \", formData.metri_posati, \" m\"]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 1743,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                variant: \"body2\",\n                children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n                  children: \"Stato Installazione:\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1747,\n                  columnNumber: 19\n                }, this), \" \", statoInstallazione]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 1746,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 1742,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Grid, {\n              item: true,\n              xs: 12,\n              md: 6,\n              children: [/*#__PURE__*/_jsxDEV(Typography, {\n                variant: \"body2\",\n                children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n                  children: \"Bobina Associata:\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1752,\n                  columnNumber: 19\n                }, this), \" \", numeroBobina]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 1751,\n                columnNumber: 17\n              }, this), bobinaInfo && /*#__PURE__*/_jsxDEV(Typography, {\n                variant: \"body2\",\n                children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n                  children: \"Metri Residui Bobina:\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1756,\n                  columnNumber: 21\n                }, this), \" \", bobinaInfo.metri_residui, \" m\"]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 1755,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 1750,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 1741,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 1737,\n          columnNumber: 11\n        }, this), bobinaInfo && parseFloat(formData.metri_posati) > parseFloat(bobinaInfo.metri_residui) && !notificationShown && /*#__PURE__*/_jsxDEV(Alert, {\n          severity: \"warning\",\n          sx: {\n            mt: 3\n          },\n          children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n            children: \"Attenzione:\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 1765,\n            columnNumber: 15\n          }, this), \" I metri posati (\", formData.metri_posati, \"m) superano i metri residui della bobina (\", bobinaInfo.metri_residui, \"m). Questo porter\\xE0 la bobina in stato OVER.\"]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 1764,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(Alert, {\n          severity: \"info\",\n          sx: {\n            mt: 3\n          },\n          children: [\"Conferma per aggiornare i metri posati e lo stato di installazione del cavo.\", formData.id_bobina && formData.id_bobina !== 'BOBINA_VUOTA' && ' I metri posati verranno sottratti dai metri residui della bobina selezionata.']\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 1770,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 1724,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 1719,\n      columnNumber: 7\n    }, this);\n  };\n\n  // Renderizza il contenuto in base al passo attivo\n  const getStepContent = step => {\n    switch (step) {\n      case 0:\n        return renderStep1();\n      // Seleziona Cavo\n      case 1:\n        return renderStep3();\n      // Associa Bobina\n      case 2:\n        return renderStep2();\n      // Inserisci Metri\n      case 3:\n        return renderStep4();\n      // Conferma\n      default:\n        return 'Passo sconosciuto';\n    }\n  };\n\n  // Gestisce la chiusura del dialogo per cavi già posati\n  const handleCloseAlreadyLaidDialog = () => {\n    setShowAlreadyLaidDialog(false);\n    setAlreadyLaidCavo(null);\n  };\n\n  // Gestisce l'opzione di modificare la bobina di un cavo già posato\n  const handleModifyReel = () => {\n    if (alreadyLaidCavo) {\n      navigate(`/dashboard/cantieri/${cantiereId}/cavi/posa/modifica-bobina/${alreadyLaidCavo.id_cavo}`);\n    }\n    handleCloseAlreadyLaidDialog();\n  };\n\n  // Gestisce l'opzione di selezionare un altro cavo\n  const handleSelectAnotherCable = () => {\n    handleCloseAlreadyLaidDialog();\n    // Reset del form per selezionare un nuovo cavo\n    setSelectedCavo(null);\n    setCavoIdInput('');\n    setShowSearchResults(false);\n  };\n\n  // Gestisce la chiusura del dialogo per bobine incompatibili\n  const handleCloseIncompatibleReelDialog = () => {\n    setShowIncompatibleReelDialog(false);\n    setIncompatibleReelData({\n      cavo: null,\n      bobina: null\n    });\n  };\n\n  // Gestisce l'aggiornamento delle caratteristiche del cavo per farle corrispondere a quelle della bobina\n  const handleUpdateCavoToMatchReel = async () => {\n    const {\n      cavo,\n      bobina\n    } = incompatibleReelData;\n    if (!cavo || !bobina) {\n      console.error('Dati mancanti per aggiornare il cavo:', {\n        cavo,\n        bobina\n      });\n      onError('Dati mancanti per aggiornare il cavo');\n      return;\n    }\n    try {\n      setLoading(true);\n      console.log(`Aggiornamento caratteristiche del cavo ${cavo.id_cavo} per compatibilità con bobina ${bobina.id_bobina}`);\n      console.log('Dati cavo prima dell\\'aggiornamento:', cavo);\n      console.log('Dati bobina:', bobina);\n\n      // Aggiorna le caratteristiche del cavo\n      await caviService.updateCavoForCompatibility(cantiereId, cavo.id_cavo, bobina.id_bobina);\n\n      // Aggiorna il cavo selezionato con le nuove caratteristiche\n      const updatedCavo = await caviService.getCavoById(cantiereId, cavo.id_cavo);\n      console.log('Dati cavo dopo l\\'aggiornamento:', updatedCavo);\n      setSelectedCavo(updatedCavo);\n\n      // Imposta la bobina selezionata\n      setFormData({\n        ...formData,\n        id_bobina: bobina.id_bobina\n      });\n\n      // Ricarica le bobine per aggiornare l'interfaccia\n      await loadBobine();\n      onSuccess(`Caratteristiche del cavo ${cavo.id_cavo} aggiornate per corrispondere alla bobina ${bobina.id_bobina}`);\n      handleCloseIncompatibleReelDialog();\n    } catch (error) {\n      console.error('Errore durante l\\'aggiornamento delle caratteristiche del cavo:', error);\n      onError('Errore durante l\\'aggiornamento delle caratteristiche del cavo: ' + (error.detail || error.message || 'Errore sconosciuto'));\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  // Gestisce l'utilizzo di una bobina incompatibile senza aggiornare le caratteristiche del cavo\n  const handleContinueWithIncompatible = async () => {\n    const {\n      cavo,\n      bobina\n    } = incompatibleReelData;\n    if (!cavo || !bobina) {\n      console.error('Dati mancanti per utilizzare la bobina incompatibile:', {\n        cavo,\n        bobina\n      });\n      onError('Dati mancanti per utilizzare la bobina incompatibile');\n      return;\n    }\n    try {\n      setLoading(true);\n      console.log(`Utilizzo bobina incompatibile ${bobina.id_bobina} con cavo ${cavo.id_cavo} senza aggiornare le caratteristiche`);\n\n      // Imposta la bobina selezionata senza aggiornare le caratteristiche del cavo\n      setFormData({\n        ...formData,\n        id_bobina: bobina.id_bobina\n      });\n      onSuccess(`Bobina incompatibile ${bobina.id_bobina} selezionata per il cavo ${cavo.id_cavo}`);\n      handleCloseIncompatibleReelDialog();\n    } catch (error) {\n      console.error('Errore durante la selezione della bobina incompatibile:', error);\n      onError('Errore durante la selezione della bobina incompatibile: ' + (error.detail || error.message || 'Errore sconosciuto'));\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  // Gestisce la selezione di un'altra bobina\n  const handleSelectAnotherReel = () => {\n    handleCloseIncompatibleReelDialog();\n    // Reset della bobina selezionata\n    setFormData({\n      ...formData,\n      id_bobina: ''\n    });\n  };\n  return /*#__PURE__*/_jsxDEV(Box, {\n    children: [/*#__PURE__*/_jsxDEV(Box, {\n      sx: {\n        mb: 2\n      },\n      children: [/*#__PURE__*/_jsxDEV(Paper, {\n        sx: {\n          p: 1.5,\n          mb: 2,\n          width: '100%'\n        },\n        children: /*#__PURE__*/_jsxDEV(Box, {\n          sx: {\n            display: 'flex',\n            alignItems: 'center',\n            width: '100%'\n          },\n          children: [/*#__PURE__*/_jsxDEV(Typography, {\n            variant: \"subtitle2\",\n            sx: {\n              mr: 1,\n              minWidth: '80px'\n            },\n            children: \"Cerca cavo\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 1913,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(TextField, {\n            size: \"small\",\n            label: \"ID Cavo\",\n            variant: \"outlined\",\n            value: cavoIdInput,\n            onChange: e => setCavoIdInput(e.target.value),\n            placeholder: \"Inserisci l'ID del cavo\",\n            sx: {\n              flexGrow: 0,\n              width: '200px',\n              mr: 1\n            }\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 1916,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Button, {\n            variant: \"contained\",\n            color: \"primary\",\n            onClick: handleSearchCavoById,\n            disabled: caviLoading || !cavoIdInput.trim(),\n            startIcon: caviLoading ? /*#__PURE__*/_jsxDEV(CircularProgress, {\n              size: 16\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1930,\n              columnNumber: 40\n            }, this) : /*#__PURE__*/_jsxDEV(SearchIcon, {\n              fontSize: \"small\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1930,\n              columnNumber: 73\n            }, this),\n            size: \"small\",\n            sx: {\n              minWidth: '80px',\n              height: '36px',\n              mr: 2\n            },\n            children: \"CERCA\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 1925,\n            columnNumber: 13\n          }, this), selectedCavo && /*#__PURE__*/_jsxDEV(Box, {\n            sx: {\n              display: 'flex',\n              alignItems: 'center',\n              flexGrow: 1,\n              flexWrap: 'nowrap',\n              overflow: 'hidden',\n              ml: 4\n            },\n            children: [/*#__PURE__*/_jsxDEV(Typography, {\n              variant: \"subtitle2\",\n              sx: {\n                fontWeight: 'bold',\n                whiteSpace: 'nowrap',\n                mr: 1,\n                fontSize: '0.9rem'\n              },\n              children: [\"Cavo: \", /*#__PURE__*/_jsxDEV(\"span\", {\n                style: {\n                  color: '#1976d2'\n                },\n                children: selectedCavo.id_cavo\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1941,\n                columnNumber: 25\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 1940,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(Divider, {\n              orientation: \"vertical\",\n              flexItem: true,\n              sx: {\n                mx: 1.5\n              }\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1943,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(Box, {\n              sx: {\n                display: 'flex',\n                alignItems: 'center',\n                gap: 3,\n                flexWrap: 'nowrap',\n                overflow: 'hidden'\n              },\n              children: [/*#__PURE__*/_jsxDEV(Box, {\n                sx: {\n                  display: 'flex',\n                  alignItems: 'center',\n                  whiteSpace: 'nowrap'\n                },\n                children: [/*#__PURE__*/_jsxDEV(Typography, {\n                  variant: \"body2\",\n                  sx: {\n                    fontWeight: 'medium',\n                    fontSize: '0.85rem',\n                    mr: 0.5\n                  },\n                  children: \"Tipo:\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1946,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                  variant: \"body2\",\n                  sx: {\n                    fontSize: '0.85rem'\n                  },\n                  children: selectedCavo.tipologia || 'N/A'\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1947,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 1945,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(Box, {\n                sx: {\n                  display: 'flex',\n                  alignItems: 'center',\n                  whiteSpace: 'nowrap'\n                },\n                children: [/*#__PURE__*/_jsxDEV(Typography, {\n                  variant: \"body2\",\n                  sx: {\n                    fontWeight: 'medium',\n                    fontSize: '0.85rem',\n                    mr: 0.5\n                  },\n                  children: \"Form:\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1950,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                  variant: \"body2\",\n                  sx: {\n                    fontSize: '0.85rem'\n                  },\n                  children: selectedCavo.sezione || 'N/A'\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1951,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 1949,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(Box, {\n                sx: {\n                  display: 'flex',\n                  alignItems: 'center',\n                  whiteSpace: 'nowrap'\n                },\n                children: [/*#__PURE__*/_jsxDEV(Typography, {\n                  variant: \"body2\",\n                  sx: {\n                    fontWeight: 'medium',\n                    fontSize: '0.85rem',\n                    mr: 0.5\n                  },\n                  children: \"Metri:\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1954,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                  variant: \"body2\",\n                  sx: {\n                    fontSize: '0.85rem'\n                  },\n                  children: [selectedCavo.metri_teorici || 'N/A', \" m\"]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1955,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 1953,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(Box, {\n                sx: {\n                  display: 'flex',\n                  alignItems: 'center',\n                  whiteSpace: 'nowrap'\n                },\n                children: [/*#__PURE__*/_jsxDEV(Typography, {\n                  variant: \"body2\",\n                  sx: {\n                    fontWeight: 'medium',\n                    fontSize: '0.85rem',\n                    mr: 0.5\n                  },\n                  children: \"Stato:\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1958,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(Chip, {\n                  size: \"small\",\n                  label: selectedCavo.stato_installazione || 'N/D',\n                  color: getCableStateColor(selectedCavo.stato_installazione),\n                  sx: {\n                    height: '20px',\n                    '& .MuiChip-label': {\n                      px: 1,\n                      py: 0,\n                      fontSize: '0.8rem'\n                    }\n                  }\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1959,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 1957,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 1944,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 1939,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 1912,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 1911,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Divider, {\n        sx: {\n          my: 2\n        }\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 1972,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 1910,\n      columnNumber: 7\n    }, this), showSearchResults && searchResults.length > 0 && /*#__PURE__*/_jsxDEV(Paper, {\n      sx: {\n        p: 3,\n        mb: 3\n      },\n      children: [/*#__PURE__*/_jsxDEV(Typography, {\n        variant: \"h6\",\n        gutterBottom: true,\n        children: \"Risultati della ricerca\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 1978,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(TableContainer, {\n        children: /*#__PURE__*/_jsxDEV(Table, {\n          size: \"small\",\n          children: [/*#__PURE__*/_jsxDEV(TableHead, {\n            children: /*#__PURE__*/_jsxDEV(TableRow, {\n              sx: {\n                bgcolor: '#f5f5f5'\n              },\n              children: [/*#__PURE__*/_jsxDEV(TableCell, {\n                children: \"ID Cavo\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1985,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n                children: \"Tipologia\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1986,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n                children: \"Formazione\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1987,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n                children: \"Ubicazione\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1988,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n                children: \"Metri Teorici\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1989,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n                children: \"Stato\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1990,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n                children: \"Azioni\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1991,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 1984,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 1983,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(TableBody, {\n            children: searchResults.map(cavo => /*#__PURE__*/_jsxDEV(TableRow, {\n              children: [/*#__PURE__*/_jsxDEV(TableCell, {\n                children: cavo.id_cavo\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1997,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n                children: cavo.tipologia || 'N/A'\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1998,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n                children: cavo.sezione || 'N/A'\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1999,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n                children: [\"Da: \", cavo.ubicazione_partenza || 'N/A', /*#__PURE__*/_jsxDEV(\"br\", {}, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 2000,\n                  columnNumber: 71\n                }, this), \"A: \", cavo.ubicazione_arrivo || 'N/A']\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 2000,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n                children: [cavo.metri_teorici || 'N/A', \" m\"]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 2001,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n                children: /*#__PURE__*/_jsxDEV(Chip, {\n                  label: cavo.stato_installazione || 'N/D',\n                  size: \"small\",\n                  color: getCableStateColor(cavo.stato_installazione),\n                  variant: \"outlined\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 2003,\n                  columnNumber: 23\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 2002,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n                children: /*#__PURE__*/_jsxDEV(Button, {\n                  size: \"small\",\n                  variant: \"contained\",\n                  color: \"primary\",\n                  onClick: () => handleCavoSelect(cavo),\n                  disabled: isCableInstalled(cavo),\n                  children: \"Seleziona\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 2011,\n                  columnNumber: 23\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 2010,\n                columnNumber: 21\n              }, this)]\n            }, cavo.id_cavo, true, {\n              fileName: _jsxFileName,\n              lineNumber: 1996,\n              columnNumber: 19\n            }, this))\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 1994,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 1982,\n          columnNumber: 13\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 1981,\n        columnNumber: 11\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 1977,\n      columnNumber: 9\n    }, this), selectedCavo && /*#__PURE__*/_jsxDEV(Box, {\n      children: renderStep3()\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 2030,\n      columnNumber: 9\n    }, this), /*#__PURE__*/_jsxDEV(Dialog, {\n      open: showConfirmDialog,\n      onClose: () => setShowConfirmDialog(false),\n      maxWidth: \"sm\",\n      fullWidth: true,\n      children: [/*#__PURE__*/_jsxDEV(DialogTitle, {\n        sx: {\n          bgcolor: 'warning.light'\n        },\n        children: /*#__PURE__*/_jsxDEV(Box, {\n          sx: {\n            display: 'flex',\n            alignItems: 'center',\n            gap: 1\n          },\n          children: [/*#__PURE__*/_jsxDEV(WarningIcon, {\n            color: \"warning\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 2039,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Typography, {\n            variant: \"h6\",\n            children: confirmDialogProps.title\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 2040,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 2038,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 2037,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(DialogContent, {\n        children: /*#__PURE__*/_jsxDEV(Typography, {\n          variant: \"body1\",\n          sx: {\n            mt: 2\n          },\n          children: confirmDialogProps.message\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 2044,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 2043,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(DialogActions, {\n        children: [/*#__PURE__*/_jsxDEV(Button, {\n          onClick: () => setShowConfirmDialog(false),\n          color: \"secondary\",\n          variant: \"outlined\",\n          children: \"Annulla\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 2049,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Button, {\n          onClick: () => {\n            setShowConfirmDialog(false);\n            confirmDialogProps.onConfirm();\n          },\n          color: \"primary\",\n          variant: \"contained\",\n          autoFocus: true,\n          children: \"Conferma\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 2052,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 2048,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 2036,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Dialog, {\n      open: showAlreadyLaidDialog,\n      onClose: handleCloseAlreadyLaidDialog,\n      maxWidth: \"sm\",\n      fullWidth: true,\n      children: [/*#__PURE__*/_jsxDEV(DialogTitle, {\n        sx: {\n          bgcolor: 'warning.light'\n        },\n        children: /*#__PURE__*/_jsxDEV(Box, {\n          sx: {\n            display: 'flex',\n            alignItems: 'center',\n            gap: 1\n          },\n          children: [/*#__PURE__*/_jsxDEV(WarningIcon, {\n            color: \"warning\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 2070,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Typography, {\n            variant: \"h6\",\n            children: \"Cavo gi\\xE0 posato\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 2071,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 2069,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 2068,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(DialogContent, {\n        children: alreadyLaidCavo && /*#__PURE__*/_jsxDEV(Box, {\n          sx: {\n            mt: 2\n          },\n          children: [/*#__PURE__*/_jsxDEV(Typography, {\n            variant: \"body1\",\n            paragraph: true,\n            children: [\"Il cavo \", /*#__PURE__*/_jsxDEV(\"strong\", {\n              children: alreadyLaidCavo.id_cavo\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 2078,\n              columnNumber: 25\n            }, this), \" risulta gi\\xE0 posato (\", alreadyLaidCavo.metratura_reale || 0, \"m).\"]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 2077,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(Typography, {\n            variant: \"body1\",\n            paragraph: true,\n            children: \"Puoi scegliere di:\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 2080,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(Typography, {\n            variant: \"body2\",\n            component: \"ul\",\n            children: [/*#__PURE__*/_jsxDEV(\"li\", {\n              children: \"Modificare la bobina associata al cavo\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 2084,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"li\", {\n              children: \"Selezionare un altro cavo\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 2085,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"li\", {\n              children: \"Annullare l'operazione\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 2086,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 2083,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 2076,\n          columnNumber: 13\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 2074,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(DialogActions, {\n        sx: {\n          p: 2,\n          justifyContent: 'space-between'\n        },\n        children: [/*#__PURE__*/_jsxDEV(Button, {\n          onClick: handleCloseAlreadyLaidDialog,\n          color: \"secondary\",\n          children: \"Annulla operazione\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 2092,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Box, {\n          children: [/*#__PURE__*/_jsxDEV(Button, {\n            onClick: handleSelectAnotherCable,\n            color: \"primary\",\n            sx: {\n              mr: 1\n            },\n            children: \"Seleziona altro cavo\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 2096,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Button, {\n            onClick: handleModifyReel,\n            variant: \"contained\",\n            color: \"primary\",\n            children: \"Modifica bobina\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 2099,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 2095,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 2091,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 2067,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(IncompatibleReelDialog, {\n      open: showIncompatibleReelDialog,\n      onClose: handleCloseIncompatibleReelDialog,\n      cavo: incompatibleReelData.cavo,\n      bobina: incompatibleReelData.bobina,\n      onUpdateCavo: handleUpdateCavoToMatchReel,\n      onSelectAnotherReel: handleSelectAnotherReel,\n      onContinueWithIncompatible: handleContinueWithIncompatible\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 2107,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Dialog, {\n      open: showCavoDetailsDialog,\n      onClose: () => setShowCavoDetailsDialog(false),\n      maxWidth: \"md\",\n      fullWidth: true,\n      children: [/*#__PURE__*/_jsxDEV(DialogTitle, {\n        children: /*#__PURE__*/_jsxDEV(Box, {\n          sx: {\n            display: 'flex',\n            alignItems: 'center',\n            gap: 1\n          },\n          children: [/*#__PURE__*/_jsxDEV(InfoIcon, {\n            color: \"primary\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 2126,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Typography, {\n            variant: \"h6\",\n            children: \"Dettagli Cavo\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 2127,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 2125,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 2124,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(DialogContent, {\n        children: /*#__PURE__*/_jsxDEV(CavoDetailsView, {\n          cavo: selectedCavo\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 2131,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 2130,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(DialogActions, {\n        children: /*#__PURE__*/_jsxDEV(Button, {\n          onClick: () => setShowCavoDetailsDialog(false),\n          color: \"primary\",\n          children: \"Chiudi\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 2134,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 2133,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 2118,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 1908,\n    columnNumber: 5\n  }, this);\n};\n_s(InserisciMetriForm, \"3yQvF7cITe0apgtUg91Zz7kARz8=\", false, function () {\n  return [useNavigate];\n});\n_c = InserisciMetriForm;\nexport default InserisciMetriForm;\nvar _c;\n$RefreshReg$(_c, \"InserisciMetriForm\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "axios", "Box", "Paper", "Typography", "TextField", "<PERSON><PERSON>", "Grid", "FormControl", "InputLabel", "Select", "MenuItem", "Divider", "<PERSON><PERSON>", "CircularProgress", "FormHelperText", "IconButton", "Chip", "InputAdornment", "Dialog", "DialogTitle", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "DialogActions", "Table", "TableBody", "TableCell", "TableContainer", "TableHead", "TableRow", "List", "ListItem", "ListItemButton", "ListItemText", "ListItemSecondaryAction", "Search", "SearchIcon", "Save", "SaveIcon", "Cancel", "CancelIcon", "Warning", "WarningIcon", "Info", "InfoIcon", "AddCircleOutline", "AddCircleOutlineIcon", "useNavigate", "caviService", "axiosInstance", "IncompatibleReelDialog", "CavoDetailsView", "CABLE_STATES", "REEL_STATES", "determineCableState", "determineReelState", "canModifyCable", "isCableSpare", "isCableInstalled", "getCableStateColor", "getReelStateColor", "parcoCaviService", "redirectToVisualizzaCavi", "jsxDEV", "_jsxDEV", "Fragment", "_Fragment", "InserisciMetriForm", "cantiereId", "onSuccess", "onError", "_s", "navigate", "loading", "setLoading", "caviLoading", "setCaviLoading", "bob<PERSON><PERSON><PERSON><PERSON>", "setBobineLoading", "searchResults", "setSearchResults", "showSearchResults", "setShowSearchResults", "cavi", "<PERSON><PERSON><PERSON>", "bobine", "set<PERSON>ob<PERSON>", "selected<PERSON><PERSON><PERSON>", "setSelectedCavo", "cavoIdInput", "setCavoIdInput", "activeStep", "setActiveStep", "formData", "setFormData", "id_cavo", "metri_posati", "id_bobina", "formErrors", "setFormErrors", "formWarnings", "setForm<PERSON><PERSON>nings", "showIncompatibleReelDialog", "setShowIncompatibleReelDialog", "incompatibleReel", "setIncompatibleReel", "incompatibleReelData", "setIncompatibleReelData", "cavo", "bobina", "showAlreadyLaidDialog", "setShowAlreadyLaidDialog", "searchText", "setSearchText", "alreadyLaidCavo", "setAlreadyLaidCavo", "showCavoDetailsDialog", "setShowCavoDetailsDialog", "handleSelectBobina", "idBobina", "console", "log", "prev", "<PERSON><PERSON><PERSON><PERSON>", "find", "b", "handleSearchTextChange", "event", "target", "value", "loadBobine", "getBobinaNumber", "includes", "split", "loadCavi", "caviData", "get<PERSON><PERSON>", "length", "loadError", "error", "isNetworkError", "response", "code", "message", "Promise", "resolve", "setTimeout", "token", "localStorage", "getItem", "API_URL", "defaults", "baseURL", "retryResponse", "get", "headers", "timeout", "data", "retryError", "errorMessage", "detail", "bobine<PERSON><PERSON>", "getBobine", "b<PERSON><PERSON><PERSON><PERSON><PERSON>", "filter", "stato_bobina", "metri_residui", "tipologia", "sezione", "cavoTipologia", "String", "trim", "toLowerCase", "cavoSezione", "bobineCom<PERSON><PERSON><PERSON><PERSON>", "bobineNonCompatibili", "for<PERSON>ach", "bobinaTipologia", "bobinaSezione", "tipologiaMatch", "sezioneMatch", "isCompatible", "push", "sort", "a", "bobineOrdinate", "handleSearchCavoById", "searchTerms", "map", "term", "join", "filteredCavi", "some", "exactMatches", "stato_installazione", "metratura_reale", "modificato_manualmente", "handleCavoSelect", "status", "window", "confirm", "reactivateSpare", "then", "updatedCavo", "catch", "n_conduttori", "cavoId", "handleFormChange", "e", "name", "cavoFormazione", "bobinaFormazione", "validateField", "warning", "isNaN", "parseFloat", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "metri_te<PERSON>ci", "notificationShown", "setNotificationShown", "showConfirmDialog", "setShowConfirmDialog", "confirmDialogProps", "setConfirmDialogProps", "title", "onConfirm", "validateForm", "<PERSON><PERSON><PERSON><PERSON>", "errors", "warnings", "handleNext", "prevActiveStep", "handleBack", "handleReset", "determineInstallationStatus", "metriTeorici", "handleSubmit", "statoInstallazione", "forceOver", "confirmMessage", "updateMetri<PERSON><PERSON><PERSON>", "successMessage", "success", "_error$response$data", "request", "_error$response$data2", "renderStep1", "children", "variant", "sx", "mb", "fontWeight", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "p", "width", "display", "alignItems", "mr", "min<PERSON><PERSON><PERSON>", "size", "label", "onChange", "placeholder", "flexGrow", "color", "onClick", "disabled", "startIcon", "fontSize", "height", "flexWrap", "overflow", "ml", "whiteSpace", "style", "orientation", "flexItem", "mx", "gap", "px", "py", "fontStyle", "justifyContent", "my", "severity", "component", "maxHeight", "<PERSON><PERSON><PERSON><PERSON>", "bgcolor", "align", "hover", "cursor", "stopPropagation", "renderStep2", "gutterBottom", "mt", "type", "helperText", "FormHelperTextProps", "inputProps", "max", "step", "renderStep3", "buildFullBobinaId", "numeroBobina", "hasSufficientMeters", "bobineFiltrate", "exactMatch", "termStr", "isNumericTerm", "numericTerm", "bobinaSezioneNum", "InputProps", "startAdornment", "position", "endAdornment", "edge", "border", "undefined", "container", "spacing", "item", "xs", "md", "borderRadius", "overflowY", "overflowX", "disablePadding", "secondaryAction", "dense", "renderStep4", "bobinaInfo", "compact", "getStepContent", "handleCloseAlreadyLaidDialog", "handleModifyReel", "handleSelectAnotherCable", "handleCloseIncompatibleReelDialog", "handleUpdateCavoToMatchReel", "updateCavoForCompatibility", "getCavoById", "handleContinueWithIncompatible", "handleSelectAnotherReel", "ubicazione_partenza", "ubicazione_arrivo", "open", "onClose", "max<PERSON><PERSON><PERSON>", "fullWidth", "autoFocus", "paragraph", "onUpdateCavo", "onSelectAnotherReel", "onContinueWithIncompatible", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/PycharmProjects/CMS/webapp/frontend/src/components/cavi/InserisciMetriForm.js"], "sourcesContent": ["import React, { useState, useEffect } from 'react';\nimport axios from 'axios';\nimport {\n  Box,\n  Paper,\n  Typography,\n  TextField,\n  Button,\n  Grid,\n  FormControl,\n  InputLabel,\n  Select,\n  MenuItem,\n  Divider,\n  Alert,\n  CircularProgress,\n  FormHelperText,\n  IconButton,\n  Chip,\n  InputAdornment,\n  Dialog,\n  DialogTitle,\n  DialogContent,\n  DialogActions,\n  Table,\n  TableBody,\n  TableCell,\n  TableContainer,\n  TableHead,\n  TableRow,\n  List,\n  ListItem,\n  ListItemButton,\n  ListItemText,\n  ListItemSecondaryAction\n} from '@mui/material';\nimport {\n  Search as SearchIcon,\n  Save as SaveIcon,\n  Cancel as CancelIcon,\n  Warning as WarningIcon,\n  Info as InfoIcon,\n  AddCircleOutline as AddCircleOutlineIcon\n} from '@mui/icons-material';\nimport { useNavigate } from 'react-router-dom';\nimport caviService from '../../services/caviService';\nimport axiosInstance from '../../services/axiosConfig';\nimport IncompatibleReelDialog from './IncompatibleReelDialog';\nimport CavoDetailsView from './CavoDetailsView';\nimport {\n  CABLE_STATES,\n  REEL_STATES,\n  determineCableState,\n  determineReelState,\n  canModifyCable,\n  isCableSpare,\n  isCableInstalled,\n  getCableStateColor,\n  getReelStateColor\n} from '../../utils/stateUtils';\nimport parcoCaviService from '../../services/parcoCaviService';\nimport { redirectToVisualizzaCavi } from '../../utils/navigationUtils';\n\n/**\n * Componente per l'inserimento dei metri posati di un cavo\n * Versione semplificata con workflow compresso in un'unica pagina\n *\n * @param {Object} props - Proprietà del componente\n * @param {string} props.cantiereId - ID del cantiere\n * @param {Function} props.onSuccess - Funzione chiamata al successo dell'operazione\n * @param {Function} props.onError - Funzione chiamata in caso di errore\n */\nconst InserisciMetriForm = ({ cantiereId, onSuccess, onError }) => {\n  const navigate = useNavigate();\n\n  // Stati per la gestione del form\n  const [loading, setLoading] = useState(false);\n  const [caviLoading, setCaviLoading] = useState(false);\n  const [bobineLoading, setBobineLoading] = useState(false);\n  const [searchResults, setSearchResults] = useState([]);\n  const [showSearchResults, setShowSearchResults] = useState(false);\n\n  // Stati per i dati\n  const [cavi, setCavi] = useState([]);\n  const [bobine, setBobine] = useState([]);\n  const [selectedCavo, setSelectedCavo] = useState(null);\n  const [cavoIdInput, setCavoIdInput] = useState('');\n\n  // Stato per la gestione dei passi (mantenuto per compatibilità con funzioni esistenti)\n  const [activeStep, setActiveStep] = useState(0);\n\n  // Stati per il form\n  const [formData, setFormData] = useState({\n    id_cavo: '',\n    metri_posati: '',\n    id_bobina: ''\n  });\n\n  // Stati per la validazione\n  const [formErrors, setFormErrors] = useState({});\n  const [formWarnings, setFormWarnings] = useState({});\n\n  // Stati per i dialoghi speciali\n  const [showIncompatibleReelDialog, setShowIncompatibleReelDialog] = useState(false);\n  const [incompatibleReel, setIncompatibleReel] = useState(null); // Mantenuto per compatibilità\n  const [incompatibleReelData, setIncompatibleReelData] = useState({ cavo: null, bobina: null });\n  const [showAlreadyLaidDialog, setShowAlreadyLaidDialog] = useState(false);\n\n  // Stati per i filtri delle bobine\n  const [searchText, setSearchText] = useState('');\n  const [alreadyLaidCavo, setAlreadyLaidCavo] = useState(null);\n  const [showCavoDetailsDialog, setShowCavoDetailsDialog] = useState(false);\n\n  // Gestisce la selezione di una bobina\n  const handleSelectBobina = (idBobina) => {\n    console.log('Bobina selezionata:', idBobina);\n    setFormData({\n      ...formData,\n      id_bobina: idBobina\n    });\n\n    // Reset degli errori\n    setFormErrors(prev => ({\n      ...prev,\n      id_bobina: null\n    }));\n\n    // Forza il re-render per mostrare le informazioni della bobina selezionata\n    const selectedBobina = bobine.find(b => b.id_bobina === idBobina);\n    if (selectedBobina) {\n      console.log('Dettagli bobina selezionata:', selectedBobina);\n    }\n  };\n\n\n  // Gestisce il cambio del testo di ricerca\n  const handleSearchTextChange = (event) => {\n    setSearchText(event.target.value);\n  };\n\n  // Carica la lista delle bobine all'avvio\n  useEffect(() => {\n    loadBobine();\n  }, [cantiereId]);\n\n  // Funzione per estrarre il numero della bobina dall'ID completo\n  const getBobinaNumber = (idBobina) => {\n    // L'ID completo è nel formato C{id_cantiere}_B{numero_bobina}\n    if (idBobina && idBobina.includes('_B')) {\n      return idBobina.split('_B')[1];\n    }\n    return idBobina;\n  };\n\n  // Funzione per caricare i cavi\n  const loadCavi = async () => {\n    try {\n      setCaviLoading(true);\n      console.log(`Caricamento cavi per il cantiere ${cantiereId}...`);\n\n      // Carica tutti i cavi, inclusi quelli SPARE e installati\n      try {\n        const caviData = await caviService.getCavi(cantiereId);\n        console.log(`Caricati ${caviData.length} cavi`);\n\n        // Mostra tutti i cavi (da installare, in corso, installati e SPARE)\n        // Questo permette di vedere anche i cavi già installati per eventuale modifica della bobina\n        setCavi(caviData);\n      } catch (loadError) {\n        console.error('Errore nel primo tentativo di caricamento dei cavi:', loadError);\n\n        // Se è un errore di rete, prova un secondo tentativo dopo una breve pausa\n        if (loadError.isNetworkError || !loadError.response ||\n            loadError.code === 'ECONNABORTED' ||\n            (loadError.message && loadError.message.includes('Network Error'))) {\n\n          console.log('Errore di rete, tentativo di recupero dopo 1 secondo...');\n          await new Promise(resolve => setTimeout(resolve, 1000));\n\n          try {\n            // Secondo tentativo con timeout aumentato\n            const token = localStorage.getItem('token');\n            const API_URL = axiosInstance.defaults.baseURL;\n\n            // Usa l'ID cantiere originale per la richiesta alternativa\n            const retryResponse = await axios.get(\n              `${API_URL}/cavi/${cantiereId}`,\n              {\n                headers: {\n                  'Authorization': `Bearer ${token}`\n                },\n                timeout: 30000 // 30 secondi\n              }\n            );\n\n            console.log(`Recupero riuscito al secondo tentativo: ${retryResponse.data.length} cavi`);\n            setCavi(retryResponse.data);\n          } catch (retryError) {\n            console.error('Anche il secondo tentativo è fallito:', retryError);\n            throw retryError;\n          }\n        } else {\n          throw loadError;\n        }\n      }\n    } catch (error) {\n      console.error('Errore nel caricamento dei cavi:', error);\n\n      // Messaggio di errore più dettagliato\n      let errorMessage = 'Errore nel caricamento dei cavi';\n\n      if (error.isNetworkError) {\n        errorMessage = 'Errore di connessione al server. Verifica la rete e riprova.';\n      } else if (error.detail) {\n        errorMessage = error.detail;\n      } else if (error.message) {\n        errorMessage = error.message;\n      }\n\n      onError(errorMessage);\n    } finally {\n      setCaviLoading(false);\n    }\n  };\n\n  // Funzione per caricare le bobine\n  const loadBobine = async () => {\n    try {\n      setBobineLoading(true);\n      console.log('Caricamento bobine iniziato...');\n\n      // Carica tutte le bobine disponibili\n      const bobineData = await parcoCaviService.getBobine(cantiereId);\n      console.log(`Bobine caricate: ${bobineData.length}`);\n\n      // Filtra solo per stato (disponibile o in uso) e metri residui > 0\n      const bobineUtilizzabili = bobineData.filter(bobina =>\n        bobina.stato_bobina !== 'Terminata' &&\n        bobina.stato_bobina !== 'Over' &&\n        bobina.metri_residui > 0\n      );\n\n      console.log(`Bobine utilizzabili: ${bobineUtilizzabili.length}`);\n\n      // Se c'è un cavo selezionato, evidenzia le bobine compatibili ma mostra tutte\n      if (selectedCavo && selectedCavo.tipologia && selectedCavo.sezione) {\n        console.log('Cavo selezionato, evidenziando bobine compatibili...');\n        console.log('Dati cavo:', {\n          id_cavo: selectedCavo.id_cavo,\n          tipologia: selectedCavo.tipologia,\n          sezione: selectedCavo.sezione\n        });\n\n        // Ordina le bobine: prima le compatibili, poi le altre, tutte ordinate per metri residui\n        const cavoTipologia = String(selectedCavo.tipologia || '').trim().toLowerCase();\n        const cavoSezione = String(selectedCavo.sezione || '0').trim();\n\n        // Identifica le bobine compatibili\n        const bobineCompatibili = [];\n        const bobineNonCompatibili = [];\n\n        // Dividi le bobine in compatibili e non compatibili\n        bobineUtilizzabili.forEach(bobina => {\n          const bobinaTipologia = String(bobina.tipologia || '').trim().toLowerCase();\n          const bobinaSezione = String(bobina.sezione || '0').trim();\n\n          // Verifica compatibilità\n          const tipologiaMatch = bobinaTipologia === cavoTipologia;\n          const sezioneMatch = bobinaSezione === cavoSezione;\n          const isCompatible = tipologiaMatch && sezioneMatch;\n\n          console.log(`Verifica compatibilità bobina ${bobina.id_bobina}:`, {\n            'Tipologia bobina': `\"${bobina.tipologia}\"`,\n            'Tipologia cavo': `\"${selectedCavo.tipologia}\"`,\n            'Tipologie uguali?': tipologiaMatch,\n            'Sezione bobina': `\"${String(bobina.sezione)}\"`,\n            'Sezione cavo': `\"${String(selectedCavo.sezione)}\"`,\n            'Sezioni uguali?': sezioneMatch,\n            'Stato bobina': bobina.stato_bobina,\n            'Metri residui': bobina.metri_residui,\n            'Compatibile?': isCompatible\n          });\n\n          if (isCompatible) {\n            bobineCompatibili.push(bobina);\n          } else {\n            bobineNonCompatibili.push(bobina);\n          }\n        });\n\n        console.log(`Bobine compatibili trovate: ${bobineCompatibili.length}`);\n        console.log(`Bobine non compatibili: ${bobineNonCompatibili.length}`);\n\n        // Log dettagliato delle bobine compatibili\n        if (bobineCompatibili.length > 0) {\n          console.log('Dettaglio bobine compatibili:');\n          bobineCompatibili.forEach(bobina => {\n            console.log(`- ${bobina.id_bobina}: ${bobina.tipologia} / ${bobina.sezione} (${bobina.metri_residui}m)`);\n          });\n        } else {\n          console.log('ATTENZIONE: Nessuna bobina compatibile trovata!');\n        }\n\n        // Ordina entrambi gli array per metri residui (decrescente)\n        bobineCompatibili.sort((a, b) => b.metri_residui - a.metri_residui);\n        bobineNonCompatibili.sort((a, b) => b.metri_residui - a.metri_residui);\n\n        // Concatena gli array: prima le compatibili, poi le non compatibili\n        const bobineOrdinate = [...bobineCompatibili, ...bobineNonCompatibili];\n\n        // Imposta le bobine nel componente\n        setBobine(bobineOrdinate);\n      } else {\n        // Se non c'è un cavo selezionato, mostra tutte le bobine ordinate per metri residui\n        console.log('Nessun cavo selezionato, mostrando tutte le bobine...');\n        bobineUtilizzabili.sort((a, b) => b.metri_residui - a.metri_residui);\n        setBobine(bobineUtilizzabili);\n      }\n    } catch (error) {\n      console.error('Errore nel caricamento delle bobine:', error);\n      onError('Errore nel caricamento delle bobine: ' + (error.message || 'Errore sconosciuto'));\n    } finally {\n      setBobineLoading(false);\n    }\n  };\n\n  // Gestisce la ricerca di un cavo per ID o pattern\n  const handleSearchCavoById = async () => {\n    if (!cavoIdInput.trim()) {\n      onError('Inserisci un ID cavo valido');\n      return;\n    }\n\n    try {\n      setCaviLoading(true);\n\n      // Dividi l'input in termini di ricerca separati da virgola\n      const searchTerms = cavoIdInput.split(',').map(term => term.trim()).filter(term => term.length > 0);\n      console.log(`Ricerca cavi con ${searchTerms.length} termini: ${searchTerms.join(', ')} nel cantiere ${cantiereId}`);\n\n      // Cerca tutti i cavi\n      const caviData = await caviService.getCavi(cantiereId);\n\n      // Filtra i cavi che corrispondono ad almeno uno dei termini di ricerca\n      const filteredCavi = caviData.filter(cavo => \n        searchTerms.some(term => \n          cavo.id_cavo.toLowerCase().includes(term.toLowerCase())\n        )\n      );\n\n      console.log(`Trovati ${filteredCavi.length} cavi corrispondenti ai termini di ricerca`);\n\n      // Cerca corrispondenze esatte per qualsiasi termine di ricerca\n      const exactMatches = filteredCavi.filter(cavo => \n        searchTerms.some(term => \n          cavo.id_cavo.toLowerCase() === term.toLowerCase()\n        )\n      );\n\n      if (exactMatches.length === 1) {\n        // Se c'è una sola corrispondenza esatta, seleziona direttamente quel cavo\n        console.log('Trovata una corrispondenza esatta:', exactMatches[0]);\n\n        // Verifica se il cavo è già installato\n        if (exactMatches[0].stato_installazione === 'Installato' || \n            (exactMatches[0].metratura_reale && exactMatches[0].metratura_reale > 0)) {\n          console.log('Cavo già installato, mostra dialogo:', exactMatches[0]);\n          setAlreadyLaidCavo(exactMatches[0]);\n          setShowAlreadyLaidDialog(true);\n          setCaviLoading(false);\n          return;\n        }\n\n        // Verifica se il cavo è SPARE\n        if (exactMatches[0].modificato_manualmente === 3) {\n          console.log('Cavo SPARE trovato:', exactMatches[0]);\n          // Gestione del cavo SPARE - verrà gestito in handleCavoSelect\n        }\n\n        // Seleziona il cavo direttamente\n        handleCavoSelect(exactMatches[0]);\n      } else if (filteredCavi.length > 0) {\n        // Mostra i risultati della ricerca in una tabella\n        setSearchResults(filteredCavi);\n        setShowSearchResults(true);\n      } else {\n        // Nessun cavo trovato\n        onError(`Nessun cavo trovato con i termini \"${searchTerms.join(', ')}\" nel cantiere ${cantiereId}`);\n      }\n    } catch (error) {\n      console.error('Errore nella ricerca del cavo:', error);\n\n      // Messaggio di errore più dettagliato\n      let errorMessage = 'Errore nella ricerca dei cavi';\n\n      if (error.isNetworkError) {\n        errorMessage = 'Errore di connessione al server. Verifica la rete e riprova.';\n      } else if (error.status === 404) {\n        errorMessage = `Cavo con ID \"${cavoIdInput.trim()}\" non trovato nel cantiere ${cantiereId}`;\n      } else if (error.detail) {\n        errorMessage = error.detail;\n      } else if (error.message) {\n        errorMessage = error.message;\n      }\n\n      onError(errorMessage);\n    } finally {\n      setCaviLoading(false);\n    }\n  };\n\n  // Gestisce la selezione di un cavo\n  const handleCavoSelect = (cavo) => {\n    // Verifica se il cavo è già installato\n    if (cavo.stato_installazione === 'Installato' || (cavo.metratura_reale && cavo.metratura_reale > 0)) {\n      // Mostra il dialogo per cavi già posati\n      setAlreadyLaidCavo(cavo);\n      setShowAlreadyLaidDialog(true);\n      return;\n    }\n    // Verifica se il cavo è SPARE (modificato_manualmente = 3)\n    else if (cavo.modificato_manualmente === 3) {\n      // Chiedi conferma all'utente per riattivare il cavo SPARE\n      if (window.confirm(`Il cavo ${cavo.id_cavo} è marcato come SPARE. Vuoi riattivarlo?`)) {\n        // Riattiva il cavo SPARE (imposta modificato_manualmente = 0)\n        reactivateSpare(cavo.id_cavo).then(() => {\n          // Aggiorna il cavo selezionato con modificato_manualmente = 0\n          const updatedCavo = { ...cavo, modificato_manualmente: 0 };\n          setSelectedCavo(updatedCavo);\n          setFormData({\n            ...formData,\n            id_cavo: updatedCavo.id_cavo,\n            metri_posati: ''\n          });\n          // Nascondi i risultati della ricerca\n          setShowSearchResults(false);\n\n          // Carica le bobine compatibili\n          loadBobine();\n        }).catch(error => {\n          console.error('Errore durante la riattivazione del cavo SPARE:', error);\n          onError('Errore durante la riattivazione del cavo SPARE: ' + (error.message || 'Errore sconosciuto'));\n        });\n      } else {\n        // L'utente ha annullato la riattivazione\n        return;\n      }\n    } else {\n      // Cavo normale (non SPARE e non installato)\n      setSelectedCavo(cavo);\n      setFormData({\n        ...formData,\n        id_cavo: cavo.id_cavo,\n        metri_posati: ''\n      });\n      // Nascondi i risultati della ricerca\n      setShowSearchResults(false);\n\n      // Carica le bobine compatibili\n      if (cavo.tipologia && cavo.n_conduttori && cavo.sezione) {\n        console.log(`Caricamento bobine compatibili per il cavo ${cavo.id_cavo}...`);\n        loadBobine();\n      }\n    }\n  };\n\n  // Funzione per riattivare un cavo SPARE\n  const reactivateSpare = async (cavoId) => {\n    try {\n      // Chiamata API per riattivare il cavo SPARE\n      await caviService.reactivateSpare(cantiereId, cavoId);\n      onSuccess(`Cavo ${cavoId} riattivato con successo`);\n      return true;\n    } catch (error) {\n      console.error('Errore durante la riattivazione del cavo SPARE:', error);\n      onError('Errore durante la riattivazione del cavo SPARE: ' + (error.message || 'Errore sconosciuto'));\n      throw error;\n    }\n  };\n\n  // Gestisce il cambio dei valori nel form\n  const handleFormChange = (e) => {\n    const { name, value } = e.target;\n\n    // Gestione speciale per il cambio di bobina\n    if (name === 'id_bobina' && value && value !== 'BOBINA_VUOTA') {\n      // Verifica compatibilità tra cavo e bobina\n      const bobina = bobine.find(b => b.id_bobina === value);\n\n      if (bobina && selectedCavo) {\n        // Nella nuova configurazione, controlliamo solo tipologia e formazione (sezione)\n        // Utilizziamo una comparazione più robusta con trim() e gestione di null/undefined\n        const isCompatible =\n          String(bobina.tipologia || '').trim() === String(selectedCavo.tipologia || '').trim() &&\n          String(bobina.sezione || '0').trim() === String(selectedCavo.sezione || '0').trim();\n\n        if (!isCompatible) {\n          console.log('Bobina incompatibile selezionata:', bobina);\n          console.log('Cavo corrente:', selectedCavo);\n          console.log('Confronto formazione:', {\n            cavoFormazione: String(selectedCavo.sezione || '0'),\n            bobinaFormazione: String(bobina.sezione || '0')\n          });\n\n          // Mostra il dialogo di incompatibilità\n          setIncompatibleReelData({\n            cavo: selectedCavo,\n            bobina: bobina\n          });\n          setShowIncompatibleReelDialog(true);\n\n          // Non aggiornare il valore del form finché l'utente non conferma\n          return;\n        }\n      }\n    }\n\n    setFormData({\n      ...formData,\n      [name]: value\n    });\n\n    // Validazione in tempo reale\n    validateField(name, value);\n  };\n\n  // Validazione di un campo\n  const validateField = (name, value) => {\n    let error = null;\n    let warning = null;\n\n    if (name === 'metri_posati') {\n      // Controllo input vuoto\n      if (!value || value.trim() === '') {\n        error = 'Inserire un valore per i metri posati';\n        return false;\n      }\n\n      // Controllo formato numerico\n      if (isNaN(parseFloat(value)) || parseFloat(value) <= 0) {\n        error = 'Inserire un valore numerico positivo';\n        return false;\n      }\n\n      const metriPosati = parseFloat(value);\n\n      // Controllo metri teorici cavo\n      if (selectedCavo && selectedCavo.metri_teorici && metriPosati > parseFloat(selectedCavo.metri_teorici)) {\n        warning = `I metri posati (${metriPosati}m) superano i metri teorici del cavo (${selectedCavo.metri_teorici}m)`;\n      }\n\n      // Controllo metri residui bobina (se bobina reale)\n      if (formData.id_bobina && formData.id_bobina !== 'BOBINA_VUOTA') {\n        const bobina = bobine.find(b => b.id_bobina === formData.id_bobina);\n        if (bobina && metriPosati > parseFloat(bobina.metri_residui)) {\n          warning = `I metri posati (${metriPosati}m) superano i metri residui della bobina (${bobina.metri_residui}m). La bobina andrà in stato OVER.`;\n        }\n      }\n    } else if (name === 'id_bobina') {\n      // Controllo che sia selezionata una bobina\n      if (!value || value.trim() === '') {\n        error = 'È necessario selezionare una bobina';\n        return false;\n      }\n    }\n\n    // Aggiorna gli errori\n    setFormErrors(prev => ({\n      ...prev,\n      [name]: error\n    }));\n\n    // Aggiorna gli avvisi\n    setFormWarnings(prev => ({\n      ...prev,\n      [name]: warning\n    }));\n\n    return !error;\n  };\n\n  // Stati per i dialoghi di conferma\n  const [notificationShown, setNotificationShown] = useState(false);\n  const [showConfirmDialog, setShowConfirmDialog] = useState(false);\n  const [confirmDialogProps, setConfirmDialogProps] = useState({\n    title: '',\n    message: '',\n    onConfirm: () => {}\n  });\n\n  // Nota: Lo stato per il dialogo di bobina incompatibile è già dichiarato sopra\n\n  // Validazione completa del form\n  const validateForm = () => {\n    let isValid = true;\n    const errors = {};\n    const warnings = {};\n\n    // Reset della variabile di notifica\n    setNotificationShown(false);\n\n    // Validazione metri posati\n    if (!formData.metri_posati || formData.metri_posati.trim() === '') {\n      errors.metri_posati = 'Inserire un valore per i metri posati';\n      isValid = false;\n    } else if (isNaN(parseFloat(formData.metri_posati)) || parseFloat(formData.metri_posati) <= 0) {\n      errors.metri_posati = 'Inserire un valore numerico positivo';\n      isValid = false;\n    }\n\n    // Validazione bobina (deve essere selezionata)\n    if (!formData.id_bobina || formData.id_bobina.trim() === '') {\n      errors.id_bobina = 'È necessario selezionare una bobina';\n      isValid = false;\n    }\n\n    console.log('Validazione form:', { isValid, errors, warnings, formData });\n\n    if (isValid) {\n      const metriPosati = parseFloat(formData.metri_posati);\n\n      // Controllo metri teorici cavo\n      if (selectedCavo && selectedCavo.metri_teorici && metriPosati > parseFloat(selectedCavo.metri_teorici)) {\n        warnings.metri_posati = `I metri posati (${metriPosati}m) superano i metri teorici del cavo (${selectedCavo.metri_teorici}m)`;\n        setNotificationShown(true); // Imposta la variabile per evitare notifiche duplicate\n        // Non mostrare più il popup di conferma, solo l'avviso nel form\n        // Continua con la validazione senza interrompere il flusso\n      }\n\n      // Controllo metri residui bobina (se bobina reale)\n      if (isValid && formData.id_bobina && formData.id_bobina !== 'BOBINA_VUOTA') {\n        const bobina = bobine.find(b => b.id_bobina === formData.id_bobina);\n        if (bobina && metriPosati > parseFloat(bobina.metri_residui)) {\n          warnings.metri_posati = `I metri posati (${metriPosati}m) superano i metri residui della bobina (${bobina.metri_residui}m). La bobina andrà in stato OVER.`;\n          setNotificationShown(true); // Imposta la variabile per evitare notifiche duplicate\n\n          // Mostra il dialogo di conferma invece di window.confirm\n          setConfirmDialogProps({\n            title: 'Attenzione: Bobina in stato OVER',\n            message: `I metri posati (${metriPosati}m) superano i metri residui della bobina (${bobina.metri_residui}m). Questo porterà la bobina in stato OVER. Vuoi continuare?`,\n            onConfirm: () => {\n              // Continua con la validazione\n              handleNext();\n            }\n          });\n          setShowConfirmDialog(true);\n          return false; // Interrompi la validazione fino alla conferma dell'utente\n        }\n      }\n    }\n\n    setFormErrors(errors);\n    setFormWarnings(warnings);\n    return isValid;\n  };\n\n  // Gestisce il passaggio al passo successivo\n  const handleNext = () => {\n    if (activeStep === 2) {\n      // Validazione prima di passare al passo successivo (da Inserisci Metri a Conferma)\n      if (!validateForm()) {\n        return;\n      }\n    } else if (activeStep === 1) {\n      // Carica le bobine prima di passare al passo successivo (da Associa Bobina a Inserisci Metri)\n      loadBobine();\n    }\n\n    setActiveStep((prevActiveStep) => prevActiveStep + 1);\n  };\n\n  // Gestisce il ritorno al passo precedente\n  const handleBack = () => {\n    setActiveStep((prevActiveStep) => prevActiveStep - 1);\n  };\n\n  // Gestisce il reset del form\n  const handleReset = () => {\n    setActiveStep(0);\n    setSelectedCavo(null);\n    setCavoIdInput('');\n    setFormData({\n      id_cavo: '',\n      metri_posati: '',\n      id_bobina: ''\n    });\n    setFormErrors({});\n    setFormWarnings({});\n  };\n\n  // Determina lo stato di installazione in base ai metri posati\n  // Utilizziamo la funzione dalla utility stateUtils\n  const determineInstallationStatus = (metriPosati, metriTeorici) => {\n    return determineCableState(metriPosati, metriTeorici);\n  };\n\n  // Gestisce l'invio del form\n  const handleSubmit = async () => {\n    // Dichiarazione delle variabili al di fuori del blocco try/catch\n    // in modo che siano accessibili anche nel blocco catch\n    let idBobina;\n    let statoInstallazione;\n    let metriPosati;\n    let forceOver = false;\n\n    try {\n      setLoading(true);\n\n      // Validazione finale\n      if (!selectedCavo || !formData.metri_posati || !formData.id_bobina) {\n        console.log('Validazione fallita:', {\n          selectedCavo: !!selectedCavo,\n          metri_posati: !!formData.metri_posati,\n          id_bobina: !!formData.id_bobina\n        });\n        setLoading(false);\n        return;\n      }\n\n      // Log per debug\n      console.log('Validazione passata, procedendo con il salvataggio:', {\n        selectedCavo: selectedCavo.id_cavo,\n        metri_posati: formData.metri_posati,\n        id_bobina: formData.id_bobina\n      });\n\n      // Prepara i dati da inviare\n      metriPosati = parseFloat(formData.metri_posati);\n\n      // Gestione speciale per BOBINA_VUOTA\n      idBobina = formData.id_bobina;\n\n      // Gestione differenziata per cavi non posati e cavi posati\n      if (!idBobina || idBobina === '') {\n        // È necessario selezionare una bobina, anche BOBINA_VUOTA\n        setFormErrors({\n          ...formErrors,\n          id_bobina: 'È necessario selezionare una bobina'\n        });\n        setLoading(false);\n        return;\n      } else if (idBobina === 'BOBINA_VUOTA') {\n        // Per cavi posati senza bobina specifica\n        console.log('Usando BOBINA_VUOTA per il cavo');\n        // Assicurati che BOBINA_VUOTA venga passato come stringa e non come null\n        idBobina = 'BOBINA_VUOTA';\n      } else {\n        // Per cavi posati con bobina specifica\n        console.log(`Usando bobina reale: ${idBobina}`);\n      }\n\n      // Determina lo stato di installazione\n      statoInstallazione = determineInstallationStatus(metriPosati, selectedCavo.metri_teorici);\n\n      // Imposta sempre forceOver a true per evitare blocchi quando la bobina va in OVER\n      forceOver = true;\n      console.log('Impostando forceOver a true per garantire il completamento dell\\'operazione');\n\n      // Log delle condizioni che richiederebbero forceOver\n      if (idBobina === 'BOBINA_VUOTA') {\n        console.log('Operazione con BOBINA_VUOTA');\n      }\n      // Per bobine reali, verifica i metri residui\n      else if (idBobina && idBobina !== 'BOBINA_VUOTA') {\n        const bobina = bobine.find(b => b.id_bobina === idBobina);\n        if (bobina && metriPosati > parseFloat(bobina.metri_residui)) {\n          console.log(`La bobina ${idBobina} andrà in stato OVER (metri posati ${metriPosati} > metri residui ${bobina.metri_residui})`);\n        }\n      }\n\n      // Verifica anche se i metri posati superano i metri teorici\n      if (selectedCavo && selectedCavo.metri_teorici && metriPosati > parseFloat(selectedCavo.metri_teorici)) {\n        console.log(`I metri posati (${metriPosati}) superano i metri teorici (${selectedCavo.metri_teorici})`);\n      }\n\n      // Log per debug\n      console.log('Invio dati:', {\n        cantiereId,\n        cavoId: formData.id_cavo,\n        metriPosati,\n        idBobina,\n        forceOver,\n        statoInstallazione\n      });\n\n      // Conferma finale prima dell'invio solo se non è già stata mostrata una notifica\n      if (!notificationShown) {\n        const confirmMessage = `Confermi l'aggiornamento del cavo ${formData.id_cavo} con ${metriPosati}m posati?`;\n\n        // Usa il dialogo di conferma invece di window.confirm\n        setConfirmDialogProps({\n          title: 'Conferma aggiornamento',\n          message: confirmMessage,\n          onConfirm: async () => {\n            // Esegui la chiamata API qui invece che nel flusso principale\n            try {\n              setLoading(true);\n\n              // Imposta sempre forceOver a true per evitare blocchi quando la bobina va in OVER\n              console.log('Impostando forceOver a true nel dialogo di conferma per garantire il completamento dell\\'operazione');\n              await caviService.updateMetriPosati(\n                cantiereId,\n                formData.id_cavo,\n                metriPosati,\n                idBobina,\n                true // Forza sempre a true per evitare blocchi\n              );\n\n              // Messaggio di successo con dettagli sulla bobina\n              let successMessage = `Metri posati aggiornati con successo. Stato cavo: ${statoInstallazione}`;\n              if (idBobina === 'BOBINA_VUOTA') {\n                successMessage += '. Cavo associato a BOBINA VUOTA';\n              } else if (idBobina) {\n                const bobina = bobine.find(b => b.id_bobina === idBobina);\n                if (bobina) {\n                  successMessage += `. Cavo associato alla bobina ${idBobina}`;\n                }\n              }\n\n              // Gestione successo\n              onSuccess(successMessage);\n\n              // Reset del form\n              handleReset();\n\n              // Ricarica i cavi\n              loadCavi();\n            } catch (error) {\n              console.error('Errore durante l\\'aggiornamento dei metri posati:', error);\n\n              // Gestione speciale per BOBINA_VUOTA\n              if (idBobina === 'BOBINA_VUOTA' && error.success) {\n                // Se è un \"errore\" di successo per BOBINA_VUOTA, trattiamolo come un successo\n                let successMessage = `Metri posati aggiornati con successo. Stato cavo: ${statoInstallazione}. Cavo associato a BOBINA VUOTA`;\n                onSuccess(successMessage);\n\n                // Reset del form\n                handleReset();\n\n                // Ricarica i cavi\n                loadCavi();\n                return;\n              }\n\n              // Gestione dettagliata degli errori\n              let errorMessage = 'Errore durante l\\'aggiornamento dei metri posati';\n\n              if (error.response) {\n                // Il server ha risposto con un codice di errore\n                const status = error.response.status;\n                const detail = error.response.data?.detail || error.message;\n\n                if (status === 400) {\n                  // Errore di validazione\n                  if (detail.includes('metri residui')) {\n                    errorMessage = `La bobina non ha metri residui sufficienti. Usa l'opzione \"BOBINA VUOTA\" o seleziona un'altra bobina.`;\n                  } else if (detail.includes('già posato')) {\n                    errorMessage = `Il cavo risulta già posato. Usa la funzione \"Modifica bobina cavo posato\".`;\n                  } else {\n                    errorMessage = detail;\n                  }\n                } else if (status === 404) {\n                  // Gestione speciale per BOBINA_VUOTA quando la bobina non viene trovata\n                  if (idBobina === 'BOBINA_VUOTA' && detail.includes('non trovata')) {\n                    let successMessage = `Metri posati aggiornati con successo. Stato cavo: ${statoInstallazione}. Cavo associato a BOBINA VUOTA`;\n                    onSuccess(successMessage);\n\n                    // Reset del form\n                    handleReset();\n\n                    // Ricarica i cavi\n                    loadCavi();\n                    return;\n                  } else {\n                    errorMessage = `Cavo o bobina non trovati: ${detail}`;\n                  }\n                } else {\n                  errorMessage = `Errore del server (${status}): ${detail}`;\n                }\n              } else if (error.request) {\n                // La richiesta è stata inviata ma non è stata ricevuta risposta\n                errorMessage = 'Nessuna risposta dal server. Verifica la connessione di rete.';\n              } else if (error.detail && idBobina === 'BOBINA_VUOTA') {\n                // Gestione speciale per errori con BOBINA_VUOTA\n                errorMessage = error.detail;\n                if (error.status === 200 || error.success) {\n                  let successMessage = `Metri posati aggiornati con successo. Stato cavo: ${statoInstallazione}. Cavo associato a BOBINA VUOTA`;\n                  onSuccess(successMessage);\n\n                  // Reset del form\n                  handleReset();\n\n                  // Ricarica i cavi\n                  loadCavi();\n                  return;\n                }\n              } else {\n                // Errore durante la configurazione della richiesta\n                errorMessage = error.message || error.detail || 'Errore sconosciuto';\n              }\n\n              onError(errorMessage);\n            } finally {\n              setLoading(false);\n            }\n          }\n        });\n        setShowConfirmDialog(true);\n        setLoading(false);\n        return;\n      }\n\n      // Chiamata API\n      console.log('Invio richiesta updateMetriPosati con i seguenti parametri:');\n      console.log('- cantiereId:', cantiereId);\n      console.log('- id_cavo:', formData.id_cavo);\n      console.log('- metri_posati:', metriPosati);\n      console.log('- id_bobina:', idBobina, typeof idBobina);\n      console.log('- forceOver:', forceOver);\n\n      // Imposta sempre forceOver a true per evitare blocchi quando la bobina va in OVER\n      console.log('Impostando forceOver a true nella chiamata diretta per garantire il completamento dell\\'operazione');\n      await caviService.updateMetriPosati(\n        cantiereId,\n        formData.id_cavo,\n        metriPosati,\n        idBobina,\n        true // Forza sempre a true per evitare blocchi\n      );\n\n      // Messaggio di successo con dettagli sulla bobina\n      let successMessage = `Metri posati aggiornati con successo. Stato cavo: ${statoInstallazione}`;\n      if (idBobina === 'BOBINA_VUOTA') {\n        successMessage += '. Cavo associato a BOBINA VUOTA';\n      } else if (idBobina) {\n        const bobina = bobine.find(b => b.id_bobina === idBobina);\n        if (bobina) {\n          successMessage += `. Cavo associato alla bobina ${idBobina}`;\n        }\n      }\n\n      // Gestione successo\n      onSuccess(successMessage);\n\n      // Reset del form\n      handleReset();\n\n      // Ricarica i cavi\n      loadCavi();\n    } catch (error) {\n      console.error('Errore durante l\\'aggiornamento dei metri posati:', error);\n\n      // Gestione speciale per BOBINA_VUOTA\n      if (idBobina === 'BOBINA_VUOTA' && error.success) {\n        // Se è un \"errore\" di successo per BOBINA_VUOTA, trattiamolo come un successo\n        let successMessage = `Metri posati aggiornati con successo. Stato cavo: ${statoInstallazione}. Cavo associato a BOBINA VUOTA`;\n        onSuccess(successMessage);\n\n        // Reset del form\n        handleReset();\n\n        // Ricarica i cavi\n        loadCavi();\n        return;\n      }\n\n      // Gestione dettagliata degli errori\n      let errorMessage = 'Errore durante l\\'aggiornamento dei metri posati';\n\n      if (error.response) {\n        // Il server ha risposto con un codice di errore\n        const status = error.response.status;\n        const detail = error.response.data?.detail || error.message;\n\n        if (status === 400) {\n          // Errore di validazione\n          if (detail.includes('metri residui')) {\n            errorMessage = `La bobina non ha metri residui sufficienti. Usa l'opzione \"BOBINA VUOTA\" o seleziona un'altra bobina.`;\n          } else if (detail.includes('già posato')) {\n            errorMessage = `Il cavo risulta già posato. Usa la funzione \"Modifica bobina cavo posato\".`;\n          } else {\n            errorMessage = detail;\n          }\n        } else if (status === 404) {\n          // Gestione speciale per BOBINA_VUOTA quando la bobina non viene trovata\n          if (idBobina === 'BOBINA_VUOTA' && detail.includes('non trovata')) {\n            let successMessage = `Metri posati aggiornati con successo. Stato cavo: ${statoInstallazione}. Cavo associato a BOBINA VUOTA`;\n            onSuccess(successMessage);\n\n            // Reset del form\n            handleReset();\n\n            // Ricarica i cavi\n            loadCavi();\n            return;\n          } else {\n            errorMessage = `Cavo o bobina non trovati: ${detail}`;\n          }\n        } else {\n          errorMessage = `Errore del server (${status}): ${detail}`;\n        }\n      } else if (error.request) {\n        // La richiesta è stata inviata ma non è stata ricevuta risposta\n        errorMessage = 'Nessuna risposta dal server. Verifica la connessione di rete.';\n      } else if (error.detail && idBobina === 'BOBINA_VUOTA') {\n        // Gestione speciale per errori con BOBINA_VUOTA\n        errorMessage = error.detail;\n        if (error.status === 200 || error.success) {\n          let successMessage = `Metri posati aggiornati con successo. Stato cavo: ${statoInstallazione}. Cavo associato a BOBINA VUOTA`;\n          onSuccess(successMessage);\n\n          // Reset del form\n          handleReset();\n\n          // Ricarica i cavi\n          loadCavi();\n          return;\n        }\n      } else {\n        // Errore durante la configurazione della richiesta\n        errorMessage = error.message || error.detail || 'Errore sconosciuto';\n      }\n\n      onError(errorMessage);\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  // Renderizza il passo 1: Selezione del cavo\n  const renderStep1 = () => {\n    return (\n      <Box>\n        <Typography variant=\"subtitle2\" sx={{ mb: 1, fontWeight: 'bold' }}>\n          Seleziona un cavo\n        </Typography>\n\n        {/* Ricerca per ID - Versione compatta con dettagli cavo selezionato */}\n        <Paper sx={{ p: 1.5, mb: 2, width: '100%' }}>\n          <Box sx={{ display: 'flex', alignItems: 'center', width: '100%' }}>\n            <Typography variant=\"subtitle2\" sx={{ mr: 1, minWidth: '80px' }}>\n              Cerca cavo\n            </Typography>\n            <TextField\n              size=\"small\"\n              label=\"ID Cavo\"\n              variant=\"outlined\"\n              value={cavoIdInput}\n              onChange={(e) => setCavoIdInput(e.target.value)}\n              placeholder=\"Inserisci ID cavo (separati da virgola per ricerca multipla)\"\n              sx={{ flexGrow: 0, width: '250px', mr: 1 }}\n            />\n            <Button\n              variant=\"contained\"\n              color=\"primary\"\n              onClick={handleSearchCavoById}\n              disabled={caviLoading || !cavoIdInput.trim()}\n              startIcon={caviLoading ? <CircularProgress size={16} /> : <SearchIcon fontSize=\"small\" />}\n              size=\"small\"\n              sx={{ minWidth: '80px', height: '36px', mr: 2 }}\n            >\n              CERCA\n            </Button>\n\n            {/* Dettagli cavo e bobina selezionati in riga singola */}\n            {selectedCavo && (\n              <Box sx={{ display: 'flex', alignItems: 'center', flexGrow: 1, flexWrap: 'nowrap', overflow: 'hidden', ml: 4 }}>\n                <Box sx={{ display: 'flex', alignItems: 'center', mr: 2 }}>\n                  <Typography variant=\"subtitle2\" sx={{ fontWeight: 'bold', whiteSpace: 'nowrap', mr: 1, fontSize: '0.95rem' }}>\n                    Cavo: <span style={{ color: '#1976d2' }}>{selectedCavo.id_cavo}</span>\n                  </Typography>\n                  <Divider orientation=\"vertical\" flexItem sx={{ mx: 1.5 }} />\n                  <Box sx={{ display: 'flex', alignItems: 'center', gap: 3, flexWrap: 'nowrap', overflow: 'hidden' }}>\n                    <Box sx={{ display: 'flex', alignItems: 'center', whiteSpace: 'nowrap' }}>\n                      <Typography variant=\"body2\" sx={{ fontWeight: 'medium', fontSize: '0.9rem', mr: 0.5 }}>Tipo:</Typography>\n                      <Typography variant=\"body2\" sx={{ fontSize: '0.9rem' }}>{selectedCavo.tipologia || 'N/A'}</Typography>\n                    </Box>\n                    <Box sx={{ display: 'flex', alignItems: 'center', whiteSpace: 'nowrap' }}>\n                      <Typography variant=\"body2\" sx={{ fontWeight: 'medium', fontSize: '0.9rem', mr: 0.5 }}>Form:</Typography>\n                      <Typography variant=\"body2\" sx={{ fontSize: '0.9rem' }}>{selectedCavo.sezione || 'N/A'}</Typography>\n                    </Box>\n                    <Box sx={{ display: 'flex', alignItems: 'center', whiteSpace: 'nowrap' }}>\n                      <Typography variant=\"body2\" sx={{ fontWeight: 'medium', fontSize: '0.9rem', mr: 0.5 }}>Metri:</Typography>\n                      <Typography variant=\"body2\" sx={{ fontSize: '0.9rem' }}>{selectedCavo.metri_teorici || 'N/A'} m</Typography>\n                    </Box>\n                    <Box sx={{ display: 'flex', alignItems: 'center', whiteSpace: 'nowrap' }}>\n                      <Typography variant=\"body2\" sx={{ fontWeight: 'medium', fontSize: '0.9rem', mr: 0.5 }}>Stato:</Typography>\n                      <Chip\n                        size=\"small\"\n                        label={selectedCavo.stato_installazione || 'N/D'}\n                        color={getCableStateColor(selectedCavo.stato_installazione)}\n                        sx={{ height: '22px', '& .MuiChip-label': { px: 1, py: 0, fontSize: '0.85rem' } }}\n                      />\n                    </Box>\n                  </Box>\n                </Box>\n\n                {formData.id_bobina && (\n                  <>\n                    <Divider orientation=\"vertical\" flexItem sx={{ mx: 2 }} />\n                    <Box sx={{ display: 'flex', alignItems: 'center' }}>\n                      <Typography variant=\"subtitle2\" sx={{ fontWeight: 'bold', whiteSpace: 'nowrap', mr: 1, fontSize: '0.95rem', color: '#2e7d32' }}>\n                        Bobina: <span>{formData.id_bobina === 'BOBINA_VUOTA' ? 'VUOTA' : getBobinaNumber(formData.id_bobina)}</span>\n                      </Typography>\n                      {(() => {\n                        if (formData.id_bobina === 'BOBINA_VUOTA') {\n                          return (\n                            <Box sx={{ display: 'flex', alignItems: 'center', whiteSpace: 'nowrap' }}>\n                              <Typography variant=\"body2\" sx={{ fontSize: '0.9rem', color: 'text.secondary', fontStyle: 'italic' }}>\n                                (Cavo posato senza bobina specifica)\n                              </Typography>\n                            </Box>\n                          );\n                        }\n\n                        const bobina = bobine.find(b => b.id_bobina === formData.id_bobina);\n                        return bobina ? (\n                          <Box sx={{ display: 'flex', alignItems: 'center', gap: 3, flexWrap: 'nowrap', overflow: 'hidden' }}>\n                            <Box sx={{ display: 'flex', alignItems: 'center', whiteSpace: 'nowrap' }}>\n                              <Typography variant=\"body2\" sx={{ fontWeight: 'medium', fontSize: '0.9rem', mr: 0.5 }}>Residui:</Typography>\n                              <Typography variant=\"body2\" sx={{ fontSize: '0.9rem', color: bobina.metri_residui < parseFloat(formData.metri_posati || 0) ? 'error.main' : 'success.main', fontWeight: 'bold' }}>\n                                {bobina.metri_residui || 0} m\n                              </Typography>\n                            </Box>\n                            <Box sx={{ display: 'flex', alignItems: 'center', whiteSpace: 'nowrap' }}>\n                              <Typography variant=\"body2\" sx={{ fontWeight: 'medium', fontSize: '0.9rem', mr: 0.5 }}>Stato:</Typography>\n                              <Chip\n                                size=\"small\"\n                                label={bobina.stato_bobina || 'N/D'}\n                                color={getReelStateColor(bobina.stato_bobina)}\n                                sx={{ height: '22px', '& .MuiChip-label': { px: 1, py: 0, fontSize: '0.85rem' } }}\n                              />\n                            </Box>\n                          </Box>\n                        ) : null;\n                      })()}\n                    </Box>\n                  </>\n                )}\n\n\n              </Box>\n            )}\n          </Box>\n        </Paper>\n\n        {/* Lista cavi - versione compatta */}\n        <Paper sx={{ p: 1.5, width: '100%' }}>\n          <Typography variant=\"subtitle2\" sx={{ mb: 1 }}>\n            Seleziona dalla lista\n          </Typography>\n\n          {caviLoading ? (\n            <Box sx={{ display: 'flex', justifyContent: 'center', my: 2 }}>\n              <CircularProgress size={24} />\n            </Box>\n          ) : cavi.length === 0 ? (\n            <Alert severity=\"info\" sx={{ py: 0.5 }}>\n              <Typography variant=\"caption\">Non ci sono cavi disponibili da installare.</Typography>\n            </Alert>\n          ) : (\n            <TableContainer component={Paper} variant=\"outlined\" sx={{ maxHeight: '300px', overflow: 'auto', width: '100%' }}>\n              <Table size=\"small\" stickyHeader>\n                <TableHead>\n                  <TableRow sx={{ '& th': { fontWeight: 'bold', py: 1, bgcolor: '#f5f5f5' } }}>\n                    <TableCell>ID Cavo</TableCell>\n                    <TableCell>Tipologia</TableCell>\n                    <TableCell>Formazione</TableCell>\n                    <TableCell>Metri</TableCell>\n                    <TableCell>Stato</TableCell>\n                    <TableCell align=\"center\" sx={{ width: '40px' }}>Info</TableCell>\n                  </TableRow>\n                </TableHead>\n                <TableBody>\n                  {cavi.map((cavo) => (\n                    <TableRow\n                      key={cavo.id_cavo}\n                      hover\n                      onClick={() => handleCavoSelect(cavo)}\n                      sx={{\n                        cursor: 'pointer',\n                        '&:hover': { bgcolor: '#f1f8e9' },\n                        '& td': { py: 0.5 }\n                      }}\n                    >\n                      <TableCell sx={{ fontWeight: 'medium' }}>{cavo.id_cavo}</TableCell>\n                      <TableCell>{cavo.tipologia || 'N/A'}</TableCell>\n                      <TableCell>{cavo.sezione || 'N/A'}</TableCell>\n                      <TableCell>{cavo.metri_teorici || 'N/A'}</TableCell>\n                      <TableCell>\n                        <Chip\n                          size=\"small\"\n                          label={isCableSpare(cavo) ? 'SPARE' : isCableInstalled(cavo) ? 'Installato' : cavo.stato_installazione}\n                          color={isCableSpare(cavo) ? 'error' : isCableInstalled(cavo) ? 'success' : getCableStateColor(cavo.stato_installazione)}\n                          sx={{ height: '20px', '& .MuiChip-label': { px: 1, py: 0, fontSize: '0.7rem' } }}\n                        />\n                      </TableCell>\n                      <TableCell align=\"center\">\n                        <IconButton\n                          size=\"small\"\n                          onClick={(e) => {\n                            e.stopPropagation();\n                            setSelectedCavo(cavo);\n                            setShowCavoDetailsDialog(true);\n                          }}\n                        >\n                          <InfoIcon fontSize=\"small\" />\n                        </IconButton>\n                      </TableCell>\n                    </TableRow>\n                  ))}\n                </TableBody>\n              </Table>\n            </TableContainer>\n          )}\n        </Paper>\n      </Box>\n    );\n  };\n\n  // Renderizza il passo 2: Inserimento metri\n  const renderStep2 = () => {\n    if (!selectedCavo) return null;\n\n    return (\n      <Box>\n        <Typography variant=\"subtitle2\" sx={{ mb: 1, fontWeight: 'bold' }}>\n          Inserisci metri posati\n        </Typography>\n\n        <Paper sx={{ p: 2, width: '100%' }}>\n          <Typography variant=\"subtitle2\" gutterBottom sx={{ fontWeight: 'bold' }}>\n            Inserisci i metri posati\n          </Typography>\n\n\n\n          <Box sx={{ mt: 2, mb: 2, width: '100%' }}>\n            <Typography variant=\"subtitle2\" gutterBottom sx={{ fontWeight: 'bold' }}>\n              Metratura posata\n            </Typography>\n            <TextField\n              size=\"small\"\n              label=\"Metri posati\"\n              variant=\"outlined\"\n              name=\"metri_posati\"\n              type=\"number\"\n              value={formData.metri_posati}\n              onChange={handleFormChange}\n              error={!!formErrors.metri_posati}\n              helperText={formErrors.metri_posati || formWarnings.metri_posati}\n              FormHelperTextProps={{\n                sx: { color: formWarnings.metri_posati && !formErrors.metri_posati ? 'warning.main' : 'error.main' }\n              }}\n              sx={{ mb: 1, width: '200px' }}\n              inputProps={{\n                max: 999999, // Limite a 6 cifre\n                step: 0.1\n              }}\n            />\n          </Box>\n\n          {formWarnings.metri_posati && !formErrors.metri_posati && !notificationShown && (\n            <Alert severity=\"warning\" sx={{ mb: 2 }}>\n              {formWarnings.metri_posati}\n            </Alert>\n          )}\n\n          <Alert severity=\"info\" sx={{ mt: 2 }}>\n            <Typography variant=\"body2\">\n              Inserisci i metri di cavo effettivamente posati. Questo valore aggiorna lo stato di installazione del cavo.\n            </Typography>\n          </Alert>\n        </Paper>\n      </Box>\n    );\n  };\n\n  // Renderizza il passo 3: Associazione bobina\n  const renderStep3 = () => {\n\n    // Funzione per costruire l'ID completo della bobina\n    const buildFullBobinaId = (numeroBobina) => {\n      return `C${cantiereId}_B${numeroBobina}`;\n    };\n\n    // Verifica se una bobina ha metri residui sufficienti\n    const hasSufficientMeters = (bobina) => {\n      if (!bobina || !formData.metri_posati) return true;\n      return parseFloat(bobina.metri_residui) >= parseFloat(formData.metri_posati);\n    };\n\n    // Filtra le bobine in base al testo di ricerca\n    const bobineFiltrate = bobine.filter(bobina => {\n      // Se non c'è testo di ricerca, mostra tutte le bobine\n      if (!searchText) return true;\n\n      // Dividi il testo di ricerca in termini separati da virgola\n      const searchTerms = searchText.split(',').map(term => term.trim().toLowerCase()).filter(term => term.length > 0);\n\n      // Se ci sono due termini identici (es. \"a,a\"), cerca una corrispondenza esatta\n      if (searchTerms.length === 2 && searchTerms[0] === searchTerms[1]) {\n        const exactMatch = \n          getBobinaNumber(bobina.id_bobina).toLowerCase() === searchTerms[0] ||\n          String(bobina.tipologia || '').toLowerCase() === searchTerms[0] ||\n          String(bobina.sezione || '').toLowerCase() === searchTerms[0];\n\n        return exactMatch;\n      }\n\n      // Altrimenti, cerca corrispondenze parziali per ciascun termine\n      return searchTerms.some(term => {\n        // Converti il termine di ricerca in stringa per gestire correttamente i numeri\n        const termStr = String(term);\n\n        // Verifica se il termine è un numero\n        const isNumericTerm = !isNaN(termStr) && !isNaN(parseFloat(termStr));\n\n        // Se il termine è un numero, cerca corrispondenze esatte nei campi numerici\n        if (isNumericTerm) {\n          const numericTerm = parseFloat(termStr);\n          const bobinaSezioneNum = parseFloat(String(bobina.sezione || '0'));\n\n          // Verifica corrispondenza esatta per numeri\n          if (bobinaSezioneNum === numericTerm) {\n            return true;\n          }\n\n          // Verifica anche se il numero è contenuto nell'ID della bobina\n          if (getBobinaNumber(bobina.id_bobina).includes(termStr)) {\n            return true;\n          }\n        }\n\n        // Altrimenti usa la ricerca standard con includes\n        return getBobinaNumber(bobina.id_bobina).toLowerCase().includes(term) ||\n               String(bobina.tipologia || '').toLowerCase().includes(term) ||\n               String(bobina.sezione || '').toLowerCase().includes(term);\n      });\n    });\n\n    // Separa le bobine compatibili e non compatibili\n    const bobineCompatibili = selectedCavo\n      ? bobineFiltrate.filter(bobina =>\n          String(bobina.tipologia || '').trim() === String(selectedCavo.tipologia || '').trim() &&\n          String(bobina.sezione || '0').trim() === String(selectedCavo.sezione || '0').trim())\n      : bobineFiltrate;\n\n    const bobineNonCompatibili = selectedCavo\n      ? bobineFiltrate.filter(bobina =>\n          String(bobina.tipologia || '').trim() !== String(selectedCavo.tipologia || '').trim() ||\n          String(bobina.sezione || '0').trim() !== String(selectedCavo.sezione || '0').trim())\n      : [];\n\n\n\n    return (\n      <Box>\n        <Box sx={{ mb: 1 }}>\n          <Typography variant=\"subtitle1\" sx={{ fontWeight: 'bold', display: 'inline', fontSize: '1.1rem' }}>\n            Associa bobina\n          </Typography>\n          <Typography variant=\"body2\" sx={{ display: 'inline', ml: 1, color: 'text.secondary' }}>\n            (Seleziona una bobina da associare al cavo o usa \"BOBINA VUOTA\" se non desideri associare una bobina specifica)\n          </Typography>\n        </Box>\n\n        <Paper sx={{ p: 2, width: '100%' }}>\n          {/* Campo per l'inserimento dei metri posati */}\n          <Box sx={{ mb: 3 }}>\n            <Box sx={{ display: 'flex', alignItems: 'center', gap: 2 }}>\n              <TextField\n                size=\"small\"\n                label=\"Metri posati\"\n                variant=\"outlined\"\n                name=\"metri_posati\"\n                type=\"number\"\n                value={formData.metri_posati}\n                onChange={handleFormChange}\n                error={!!formErrors.metri_posati}\n                helperText={formErrors.metri_posati}\n                sx={{ width: '200px' }}\n                inputProps={{\n                  max: 999999, // Limite a 6 cifre\n                  step: 0.1\n                }}\n              />\n              {/* Warning accanto al campo */}\n              {formWarnings.metri_posati && !formErrors.metri_posati && (\n                <Typography\n                  variant=\"body2\"\n                  sx={{\n                    color: 'warning.main',\n                    fontWeight: 'medium',\n                    fontSize: '0.875rem'\n                  }}\n                >\n                  {formWarnings.metri_posati}\n                </Typography>\n              )}\n            </Box>\n          </Box>\n\n\n\n          <Divider sx={{ my: 2 }} />\n\n          <Box sx={{ mb: 2 }}>\n\n            <Box sx={{ display: 'flex', alignItems: 'center', gap: 2, mb: 2 }}>\n              {/* Campo di ricerca - versione compatta */}\n              <TextField\n                size=\"small\"\n                label=\"Cerca\"\n                variant=\"outlined\"\n                value={searchText}\n                onChange={handleSearchTextChange}\n                placeholder=\"ID, tipologia... (usa a,a per selezionare esattamente 'a')\"\n                InputProps={{\n                  startAdornment: (\n                    <InputAdornment position=\"start\">\n                      <SearchIcon fontSize=\"small\" />\n                    </InputAdornment>\n                  ),\n                  endAdornment: searchText ? (\n                    <InputAdornment position=\"end\">\n                      <IconButton\n                        size=\"small\"\n                        aria-label=\"clear search\"\n                        onClick={() => setSearchText('')}\n                        edge=\"end\"\n                      >\n                        <CancelIcon fontSize=\"small\" />\n                      </IconButton>\n                    </InputAdornment>\n                  ) : null\n                }}\n                sx={{ width: '200px' }}\n              />\n\n              <Button\n                variant=\"outlined\"\n                size=\"small\"\n                onClick={() => {\n                  console.log('Selezionata BOBINA_VUOTA');\n                  handleSelectBobina('BOBINA_VUOTA');\n                }}\n                sx={{ height: '40px', fontWeight: formData.id_bobina === 'BOBINA_VUOTA' ? 'bold' : 'normal',\n                      bgcolor: formData.id_bobina === 'BOBINA_VUOTA' ? 'rgba(76, 175, 80, 0.12)' : 'inherit',\n                      border: formData.id_bobina === 'BOBINA_VUOTA' ? '1px solid #4caf50' : undefined }}\n              >\n                BOBINA VUOTA\n              </Button>\n            </Box>\n          </Box>\n\n          {bobineLoading ? (\n            <Box sx={{ display: 'flex', justifyContent: 'center', my: 1 }}>\n              <CircularProgress size={24} />\n            </Box>\n          ) : (\n            <Box>\n\n\n              {/* Griglia per le due liste di bobine */}\n              <Grid container spacing={2}>\n                {/* Colonna sinistra: Bobine compatibili */}\n                <Grid item xs={12} md={6}>\n                  <Paper variant=\"outlined\" sx={{ p: 2, height: '100%' }}>\n                    <Typography variant=\"subtitle2\" sx={{ fontWeight: 'bold', mb: 1 }}>\n                      ELENCO BOBINE COMPATIBILI\n                    </Typography>\n\n                    {bobineCompatibili.length > 0 ? (\n                      <>\n                        <Box sx={{ display: 'flex', alignItems: 'center', width: '100%', py: 0.8, px: 1.8, bgcolor: '#f5f5f5', borderRadius: 1, mb: 1 }}>\n                          <Box sx={{ width: '60px', mr: 2 }}>\n                            <Typography variant=\"caption\" sx={{ fontWeight: 'bold', fontSize: '0.85rem' }}>ID</Typography>\n                          </Box>\n                          <Box sx={{ width: '120px', mr: 2 }}>\n                            <Typography variant=\"caption\" sx={{ fontWeight: 'bold', fontSize: '0.85rem' }}>Tipo</Typography>\n                          </Box>\n                          <Box sx={{ width: '100px', mr: 2 }}>\n                            <Typography variant=\"caption\" sx={{ fontWeight: 'bold', fontSize: '0.85rem' }}>Form.</Typography>\n                          </Box>\n                          <Box sx={{ width: '100px', mr: 2 }}>\n                            <Typography variant=\"caption\" sx={{ fontWeight: 'bold', fontSize: '0.85rem' }}>Residui</Typography>\n                          </Box>\n                          <Box sx={{ flexGrow: 0 }}>\n                            <Typography variant=\"caption\" sx={{ fontWeight: 'bold', fontSize: '0.85rem' }}>Stato</Typography>\n                          </Box>\n                        </Box>\n                      <List sx={{ maxHeight: bobineCompatibili.length > 6 ? '300px' : 'auto', overflowY: bobineCompatibili.length > 6 ? 'auto' : 'visible', overflowX: 'hidden', bgcolor: 'background.paper' }}>\n                        {bobineCompatibili.map((bobina) => (\n                          <ListItem\n                            key={bobina.id_bobina}\n                            disablePadding\n                            secondaryAction={\n                              <IconButton\n                                edge=\"end\"\n                                size=\"small\"\n                                onClick={() => handleSelectBobina(bobina.id_bobina)}\n                              >\n                                <AddCircleOutlineIcon color=\"primary\" />\n                              </IconButton>\n                            }\n                            sx={{\n                              bgcolor: formData.id_bobina === bobina.id_bobina ? 'rgba(76, 175, 80, 0.12)' : 'inherit',\n                              borderRadius: '4px',\n                              mb: 0.5,\n                              border: formData.id_bobina === bobina.id_bobina ? '1px solid #4caf50' : 'none',\n                            }}\n                          >\n                            <ListItemButton\n                              dense\n                              onClick={() => handleSelectBobina(bobina.id_bobina)}\n                            >\n                              <Box sx={{ display: 'flex', alignItems: 'center', width: '100%', py: 0.8 }}>\n                                <Box sx={{ width: '60px', mr: 2 }}>\n                                  <Typography variant=\"body2\" sx={{ fontWeight: 'bold', fontSize: '0.9rem' }}>\n                                    {getBobinaNumber(bobina.id_bobina)}\n                                  </Typography>\n                                </Box>\n                                <Box sx={{ width: '120px', mr: 2 }}>\n                                  <Typography variant=\"body2\" sx={{ fontSize: '0.85rem' }}>\n                                    {bobina.tipologia || 'N/A'}\n                                  </Typography>\n                                </Box>\n                                <Box sx={{ width: '100px', mr: 2 }}>\n                                  <Typography variant=\"body2\" sx={{ fontSize: '0.85rem' }}>\n                                    {bobina.sezione || 'N/A'}\n                                  </Typography>\n                                </Box>\n                                <Box sx={{ width: '100px', mr: 2 }}>\n                                  <Typography variant=\"body2\" sx={{ fontWeight: 'bold', fontSize: '0.85rem', color: bobina.metri_residui < parseFloat(formData.metri_posati || 0) ? 'error.main' : 'success.main' }}>\n                                    {bobina.metri_residui || 0} m\n                                  </Typography>\n                                </Box>\n                                <Box sx={{ flexGrow: 0 }}>\n                                  <Chip\n                                    size=\"small\"\n                                    label={bobina.stato_bobina || 'N/D'}\n                                    color={getReelStateColor(bobina.stato_bobina)}\n                                    variant=\"outlined\"\n                                    sx={{ height: 22, fontSize: '0.8rem', '& .MuiChip-label': { px: 1, py: 0 } }}\n                                  />\n                                </Box>\n                              </Box>\n                            </ListItemButton>\n                          </ListItem>\n                        ))}\n                      </List>\n                      </>\n                    ) : (\n                      <Alert severity=\"info\" sx={{ mt: 1 }}>\n                        Nessuna bobina compatibile disponibile. Puoi usare BOBINA VUOTA o selezionare una bobina non compatibile.\n                      </Alert>\n                    )}\n                  </Paper>\n                </Grid>\n\n                {/* Colonna destra: Bobine non compatibili */}\n                <Grid item xs={12} md={6}>\n                  <Paper variant=\"outlined\" sx={{ p: 2, height: '100%' }}>\n                    <Typography variant=\"subtitle2\" sx={{ fontWeight: 'bold', mb: 1 }}>\n                      ELENCO BOBINE NON COMPATIBILI\n                    </Typography>\n\n                    {bobineNonCompatibili.length > 0 ? (\n                      <>\n                        <Box sx={{ display: 'flex', alignItems: 'center', width: '100%', py: 0.8, px: 1.8, bgcolor: '#f5f5f5', borderRadius: 1, mb: 1 }}>\n                          <Box sx={{ width: '60px', mr: 2 }}>\n                            <Typography variant=\"caption\" sx={{ fontWeight: 'bold', fontSize: '0.85rem' }}>ID</Typography>\n                          </Box>\n                          <Box sx={{ width: '120px', mr: 2 }}>\n                            <Typography variant=\"caption\" sx={{ fontWeight: 'bold', fontSize: '0.85rem' }}>Tipo</Typography>\n                          </Box>\n                          <Box sx={{ width: '100px', mr: 2 }}>\n                            <Typography variant=\"caption\" sx={{ fontWeight: 'bold', fontSize: '0.85rem' }}>Form.</Typography>\n                          </Box>\n                          <Box sx={{ width: '100px', mr: 2 }}>\n                            <Typography variant=\"caption\" sx={{ fontWeight: 'bold', fontSize: '0.85rem' }}>Residui</Typography>\n                          </Box>\n                          <Box sx={{ flexGrow: 0 }}>\n                            <Typography variant=\"caption\" sx={{ fontWeight: 'bold', fontSize: '0.85rem' }}>Stato</Typography>\n                          </Box>\n                        </Box>\n                      <List sx={{ maxHeight: bobineNonCompatibili.length > 6 ? '300px' : 'auto', overflowY: bobineNonCompatibili.length > 6 ? 'auto' : 'visible', overflowX: 'hidden', bgcolor: 'background.paper' }}>\n                        {bobineNonCompatibili.map((bobina) => (\n                          <ListItem\n                            key={bobina.id_bobina}\n                            disablePadding\n                            secondaryAction={\n                              <IconButton\n                                edge=\"end\"\n                                size=\"small\"\n                                onClick={() => handleSelectBobina(bobina.id_bobina)}\n                              >\n                                <AddCircleOutlineIcon color=\"primary\" />\n                              </IconButton>\n                            }\n                            sx={{\n                              bgcolor: formData.id_bobina === bobina.id_bobina ? 'rgba(76, 175, 80, 0.12)' : 'inherit',\n                              borderRadius: '4px',\n                              mb: 0.5,\n                              border: formData.id_bobina === bobina.id_bobina ? '1px solid #4caf50' : 'none',\n                            }}\n                          >\n                            <ListItemButton\n                              dense\n                              onClick={() => handleSelectBobina(bobina.id_bobina)}\n                            >\n                              <Box sx={{ display: 'flex', alignItems: 'center', width: '100%', py: 0.8 }}>\n                                <Box sx={{ width: '60px', mr: 2 }}>\n                                  <Typography variant=\"body2\" sx={{ fontWeight: 'bold', fontSize: '0.9rem' }}>\n                                    {getBobinaNumber(bobina.id_bobina)}\n                                  </Typography>\n                                </Box>\n                                <Box sx={{ width: '120px', mr: 2 }}>\n                                  <Typography variant=\"body2\" sx={{ fontSize: '0.85rem' }}>\n                                    {bobina.tipologia || 'N/A'}\n                                  </Typography>\n                                </Box>\n                                <Box sx={{ width: '100px', mr: 2 }}>\n                                  <Typography variant=\"body2\" sx={{ fontSize: '0.85rem' }}>\n                                    {bobina.sezione || 'N/A'}\n                                  </Typography>\n                                </Box>\n                                <Box sx={{ width: '100px', mr: 2 }}>\n                                  <Typography variant=\"body2\" sx={{ fontWeight: 'bold', fontSize: '0.85rem', color: bobina.metri_residui < parseFloat(formData.metri_posati || 0) ? 'error.main' : 'success.main' }}>\n                                    {bobina.metri_residui || 0} m\n                                  </Typography>\n                                </Box>\n                                <Box sx={{ display: 'flex', gap: 1 }}>\n                                  <Chip\n                                    size=\"small\"\n                                    label={bobina.stato_bobina || 'N/D'}\n                                    color={getReelStateColor(bobina.stato_bobina)}\n                                    variant=\"outlined\"\n                                    sx={{ height: 22, fontSize: '0.8rem', '& .MuiChip-label': { px: 1, py: 0 } }}\n                                  />\n                                  <Chip\n                                    size=\"small\"\n                                    label=\"Non comp.\"\n                                    color=\"warning\"\n                                    variant=\"outlined\"\n                                    sx={{ height: 22, fontSize: '0.8rem', '& .MuiChip-label': { px: 1, py: 0 } }}\n                                  />\n                                </Box>\n                              </Box>\n                            </ListItemButton>\n                          </ListItem>\n                        ))}\n                      </List>\n                      </>\n                    ) : (\n                      <Alert severity=\"info\" sx={{ mt: 1 }}>\n                        Nessuna bobina non compatibile disponibile con i filtri attuali.\n                      </Alert>\n                    )}\n                  </Paper>\n                </Grid>\n              </Grid>\n            </Box>\n          )}\n\n\n\n          {bobine.length === 0 && !bobineLoading && (\n            <Alert severity=\"warning\" sx={{ mt: 2 }}>\n              Non ci sono bobine disponibili. Puoi procedere senza associare una bobina o aggiungere prima una nuova bobina.\n            </Alert>\n          )}\n\n          {/* Pulsanti Salva e Annulla */}\n          <Box sx={{ display: 'flex', justifyContent: 'flex-end', mt: 3, gap: 2 }}>\n            <Button\n              variant=\"outlined\"\n              color=\"secondary\"\n              onClick={handleReset}\n              disabled={loading}\n            >\n              Annulla\n            </Button>\n            <Button\n              variant=\"contained\"\n              color=\"primary\"\n              onClick={handleSubmit}\n              disabled={loading || !selectedCavo || !formData.metri_posati || !formData.id_bobina}\n              startIcon={loading ? <CircularProgress size={20} /> : null}\n            >\n              Salva\n            </Button>\n          </Box>\n        </Paper>\n      </Box>\n    );\n  };\n\n  // Renderizza il passo 4: Conferma\n  const renderStep4 = () => {\n\n    // Ottieni il numero della bobina se presente\n    let numeroBobina = 'Nessuna';\n    let bobinaInfo = null;\n\n    if (formData.id_bobina === 'BOBINA_VUOTA') {\n      numeroBobina = 'BOBINA VUOTA';\n    } else if (formData.id_bobina) {\n      numeroBobina = getBobinaNumber(formData.id_bobina);\n      // Trova i dettagli della bobina selezionata\n      bobinaInfo = bobine.find(b => b.id_bobina === formData.id_bobina);\n    }\n\n    // Determina lo stato di installazione\n    const statoInstallazione = determineInstallationStatus(parseFloat(formData.metri_posati), selectedCavo.metri_teorici);\n\n    return (\n      <Box>\n        <Typography variant=\"h6\" gutterBottom>\n          Conferma inserimento\n        </Typography>\n\n        <Paper sx={{ p: 3 }}>\n          <Typography variant=\"subtitle1\" gutterBottom>\n            Riepilogo dati\n          </Typography>\n\n          {/* Dettagli del cavo */}\n          <CavoDetailsView\n            cavo={selectedCavo}\n            compact={true}\n            title=\"Dettagli del cavo\"\n          />\n\n          {/* Informazioni sull'operazione */}\n          <Box sx={{ mt: 3, p: 2, bgcolor: '#f5f5f5', borderRadius: 1 }}>\n            <Typography variant=\"subtitle2\" gutterBottom sx={{ fontWeight: 'bold' }}>\n              Informazioni sull'operazione:\n            </Typography>\n            <Grid container spacing={2}>\n              <Grid item xs={12} md={6}>\n                <Typography variant=\"body2\">\n                  <strong>Metri Posati:</strong> {formData.metri_posati} m\n                </Typography>\n                <Typography variant=\"body2\">\n                  <strong>Stato Installazione:</strong> {statoInstallazione}\n                </Typography>\n              </Grid>\n              <Grid item xs={12} md={6}>\n                <Typography variant=\"body2\">\n                  <strong>Bobina Associata:</strong> {numeroBobina}\n                </Typography>\n                {bobinaInfo && (\n                  <Typography variant=\"body2\">\n                    <strong>Metri Residui Bobina:</strong> {bobinaInfo.metri_residui} m\n                  </Typography>\n                )}\n              </Grid>\n            </Grid>\n          </Box>\n\n          {bobinaInfo && parseFloat(formData.metri_posati) > parseFloat(bobinaInfo.metri_residui) && !notificationShown && (\n            <Alert severity=\"warning\" sx={{ mt: 3 }}>\n              <strong>Attenzione:</strong> I metri posati ({formData.metri_posati}m) superano i metri residui della bobina ({bobinaInfo.metri_residui}m).\n              Questo porterà la bobina in stato OVER.\n            </Alert>\n          )}\n\n          <Alert severity=\"info\" sx={{ mt: 3 }}>\n            Conferma per aggiornare i metri posati e lo stato di installazione del cavo.\n            {formData.id_bobina && formData.id_bobina !== 'BOBINA_VUOTA' && ' I metri posati verranno sottratti dai metri residui della bobina selezionata.'}\n          </Alert>\n        </Paper>\n      </Box>\n    );\n  };\n\n  // Renderizza il contenuto in base al passo attivo\n  const getStepContent = (step) => {\n    switch (step) {\n      case 0:\n        return renderStep1(); // Seleziona Cavo\n      case 1:\n        return renderStep3(); // Associa Bobina\n      case 2:\n        return renderStep2(); // Inserisci Metri\n      case 3:\n        return renderStep4(); // Conferma\n      default:\n        return 'Passo sconosciuto';\n    }\n  };\n\n  // Gestisce la chiusura del dialogo per cavi già posati\n  const handleCloseAlreadyLaidDialog = () => {\n    setShowAlreadyLaidDialog(false);\n    setAlreadyLaidCavo(null);\n  };\n\n\n\n  // Gestisce l'opzione di modificare la bobina di un cavo già posato\n  const handleModifyReel = () => {\n    if (alreadyLaidCavo) {\n      navigate(`/dashboard/cantieri/${cantiereId}/cavi/posa/modifica-bobina/${alreadyLaidCavo.id_cavo}`);\n    }\n    handleCloseAlreadyLaidDialog();\n  };\n\n  // Gestisce l'opzione di selezionare un altro cavo\n  const handleSelectAnotherCable = () => {\n    handleCloseAlreadyLaidDialog();\n    // Reset del form per selezionare un nuovo cavo\n    setSelectedCavo(null);\n    setCavoIdInput('');\n    setShowSearchResults(false);\n  };\n\n  // Gestisce la chiusura del dialogo per bobine incompatibili\n  const handleCloseIncompatibleReelDialog = () => {\n    setShowIncompatibleReelDialog(false);\n    setIncompatibleReelData({ cavo: null, bobina: null });\n  };\n\n  // Gestisce l'aggiornamento delle caratteristiche del cavo per farle corrispondere a quelle della bobina\n  const handleUpdateCavoToMatchReel = async () => {\n    const { cavo, bobina } = incompatibleReelData;\n    if (!cavo || !bobina) {\n      console.error('Dati mancanti per aggiornare il cavo:', { cavo, bobina });\n      onError('Dati mancanti per aggiornare il cavo');\n      return;\n    }\n\n    try {\n      setLoading(true);\n      console.log(`Aggiornamento caratteristiche del cavo ${cavo.id_cavo} per compatibilità con bobina ${bobina.id_bobina}`);\n      console.log('Dati cavo prima dell\\'aggiornamento:', cavo);\n      console.log('Dati bobina:', bobina);\n\n      // Aggiorna le caratteristiche del cavo\n      await caviService.updateCavoForCompatibility(cantiereId, cavo.id_cavo, bobina.id_bobina);\n\n      // Aggiorna il cavo selezionato con le nuove caratteristiche\n      const updatedCavo = await caviService.getCavoById(cantiereId, cavo.id_cavo);\n      console.log('Dati cavo dopo l\\'aggiornamento:', updatedCavo);\n      setSelectedCavo(updatedCavo);\n\n      // Imposta la bobina selezionata\n      setFormData({\n        ...formData,\n        id_bobina: bobina.id_bobina\n      });\n\n      // Ricarica le bobine per aggiornare l'interfaccia\n      await loadBobine();\n\n      onSuccess(`Caratteristiche del cavo ${cavo.id_cavo} aggiornate per corrispondere alla bobina ${bobina.id_bobina}`);\n      handleCloseIncompatibleReelDialog();\n    } catch (error) {\n      console.error('Errore durante l\\'aggiornamento delle caratteristiche del cavo:', error);\n      onError('Errore durante l\\'aggiornamento delle caratteristiche del cavo: ' + (error.detail || error.message || 'Errore sconosciuto'));\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  // Gestisce l'utilizzo di una bobina incompatibile senza aggiornare le caratteristiche del cavo\n  const handleContinueWithIncompatible = async () => {\n    const { cavo, bobina } = incompatibleReelData;\n    if (!cavo || !bobina) {\n      console.error('Dati mancanti per utilizzare la bobina incompatibile:', { cavo, bobina });\n      onError('Dati mancanti per utilizzare la bobina incompatibile');\n      return;\n    }\n\n    try {\n      setLoading(true);\n      console.log(`Utilizzo bobina incompatibile ${bobina.id_bobina} con cavo ${cavo.id_cavo} senza aggiornare le caratteristiche`);\n\n      // Imposta la bobina selezionata senza aggiornare le caratteristiche del cavo\n      setFormData({\n        ...formData,\n        id_bobina: bobina.id_bobina\n      });\n\n      onSuccess(`Bobina incompatibile ${bobina.id_bobina} selezionata per il cavo ${cavo.id_cavo}`);\n      handleCloseIncompatibleReelDialog();\n    } catch (error) {\n      console.error('Errore durante la selezione della bobina incompatibile:', error);\n      onError('Errore durante la selezione della bobina incompatibile: ' + (error.detail || error.message || 'Errore sconosciuto'));\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  // Gestisce la selezione di un'altra bobina\n  const handleSelectAnotherReel = () => {\n    handleCloseIncompatibleReelDialog();\n    // Reset della bobina selezionata\n    setFormData({\n      ...formData,\n      id_bobina: ''\n    });\n  };\n\n  return (\n    <Box>\n      {/* Sezione di ricerca con dettagli cavo selezionato in riga singola */}\n      <Box sx={{ mb: 2 }}>\n        <Paper sx={{ p: 1.5, mb: 2, width: '100%' }}>\n          <Box sx={{ display: 'flex', alignItems: 'center', width: '100%' }}>\n            <Typography variant=\"subtitle2\" sx={{ mr: 1, minWidth: '80px' }}>\n              Cerca cavo\n            </Typography>\n            <TextField\n              size=\"small\"\n              label=\"ID Cavo\"\n              variant=\"outlined\"\n              value={cavoIdInput}\n              onChange={(e) => setCavoIdInput(e.target.value)}\n              placeholder=\"Inserisci l'ID del cavo\"\n              sx={{ flexGrow: 0, width: '200px', mr: 1 }}\n            />\n            <Button\n              variant=\"contained\"\n              color=\"primary\"\n              onClick={handleSearchCavoById}\n              disabled={caviLoading || !cavoIdInput.trim()}\n              startIcon={caviLoading ? <CircularProgress size={16} /> : <SearchIcon fontSize=\"small\" />}\n              size=\"small\"\n              sx={{ minWidth: '80px', height: '36px', mr: 2 }}\n            >\n              CERCA\n            </Button>\n\n            {/* Dettagli cavo selezionato in riga singola */}\n            {selectedCavo && (\n              <Box sx={{ display: 'flex', alignItems: 'center', flexGrow: 1, flexWrap: 'nowrap', overflow: 'hidden', ml: 4 }}>\n                <Typography variant=\"subtitle2\" sx={{ fontWeight: 'bold', whiteSpace: 'nowrap', mr: 1, fontSize: '0.9rem' }}>\n                  Cavo: <span style={{ color: '#1976d2' }}>{selectedCavo.id_cavo}</span>\n                </Typography>\n                <Divider orientation=\"vertical\" flexItem sx={{ mx: 1.5 }} />\n                <Box sx={{ display: 'flex', alignItems: 'center', gap: 3, flexWrap: 'nowrap', overflow: 'hidden' }}>\n                  <Box sx={{ display: 'flex', alignItems: 'center', whiteSpace: 'nowrap' }}>\n                    <Typography variant=\"body2\" sx={{ fontWeight: 'medium', fontSize: '0.85rem', mr: 0.5 }}>Tipo:</Typography>\n                    <Typography variant=\"body2\" sx={{ fontSize: '0.85rem' }}>{selectedCavo.tipologia || 'N/A'}</Typography>\n                  </Box>\n                  <Box sx={{ display: 'flex', alignItems: 'center', whiteSpace: 'nowrap' }}>\n                    <Typography variant=\"body2\" sx={{ fontWeight: 'medium', fontSize: '0.85rem', mr: 0.5 }}>Form:</Typography>\n                    <Typography variant=\"body2\" sx={{ fontSize: '0.85rem' }}>{selectedCavo.sezione || 'N/A'}</Typography>\n                  </Box>\n                  <Box sx={{ display: 'flex', alignItems: 'center', whiteSpace: 'nowrap' }}>\n                    <Typography variant=\"body2\" sx={{ fontWeight: 'medium', fontSize: '0.85rem', mr: 0.5 }}>Metri:</Typography>\n                    <Typography variant=\"body2\" sx={{ fontSize: '0.85rem' }}>{selectedCavo.metri_teorici || 'N/A'} m</Typography>\n                  </Box>\n                  <Box sx={{ display: 'flex', alignItems: 'center', whiteSpace: 'nowrap' }}>\n                    <Typography variant=\"body2\" sx={{ fontWeight: 'medium', fontSize: '0.85rem', mr: 0.5 }}>Stato:</Typography>\n                    <Chip\n                      size=\"small\"\n                      label={selectedCavo.stato_installazione || 'N/D'}\n                      color={getCableStateColor(selectedCavo.stato_installazione)}\n                      sx={{ height: '20px', '& .MuiChip-label': { px: 1, py: 0, fontSize: '0.8rem' } }}\n                    />\n                  </Box>\n                </Box>\n              </Box>\n            )}\n          </Box>\n        </Paper>\n\n        <Divider sx={{ my: 2 }} />\n      </Box>\n\n      {/* Risultati della ricerca */}\n      {showSearchResults && searchResults.length > 0 && (\n        <Paper sx={{ p: 3, mb: 3 }}>\n          <Typography variant=\"h6\" gutterBottom>\n            Risultati della ricerca\n          </Typography>\n          <TableContainer>\n            <Table size=\"small\">\n              <TableHead>\n                <TableRow sx={{ bgcolor: '#f5f5f5' }}>\n                  <TableCell>ID Cavo</TableCell>\n                  <TableCell>Tipologia</TableCell>\n                  <TableCell>Formazione</TableCell>\n                  <TableCell>Ubicazione</TableCell>\n                  <TableCell>Metri Teorici</TableCell>\n                  <TableCell>Stato</TableCell>\n                  <TableCell>Azioni</TableCell>\n                </TableRow>\n              </TableHead>\n              <TableBody>\n                {searchResults.map((cavo) => (\n                  <TableRow key={cavo.id_cavo}>\n                    <TableCell>{cavo.id_cavo}</TableCell>\n                    <TableCell>{cavo.tipologia || 'N/A'}</TableCell>\n                    <TableCell>{cavo.sezione || 'N/A'}</TableCell>\n                    <TableCell>Da: {cavo.ubicazione_partenza || 'N/A'}<br/>A: {cavo.ubicazione_arrivo || 'N/A'}</TableCell>\n                    <TableCell>{cavo.metri_teorici || 'N/A'} m</TableCell>\n                    <TableCell>\n                      <Chip\n                        label={cavo.stato_installazione || 'N/D'}\n                        size=\"small\"\n                        color={getCableStateColor(cavo.stato_installazione)}\n                        variant=\"outlined\"\n                      />\n                    </TableCell>\n                    <TableCell>\n                      <Button\n                        size=\"small\"\n                        variant=\"contained\"\n                        color=\"primary\"\n                        onClick={() => handleCavoSelect(cavo)}\n                        disabled={isCableInstalled(cavo)}\n                      >\n                        Seleziona\n                      </Button>\n                    </TableCell>\n                  </TableRow>\n                ))}\n              </TableBody>\n            </Table>\n          </TableContainer>\n        </Paper>\n      )}\n      {/* Sezione di selezione bobina */}\n      {selectedCavo && (\n        <Box>\n          {renderStep3()}\n        </Box>\n      )}\n\n      {/* Dialogo di conferma generico */}\n      <Dialog open={showConfirmDialog} onClose={() => setShowConfirmDialog(false)} maxWidth=\"sm\" fullWidth>\n        <DialogTitle sx={{ bgcolor: 'warning.light' }}>\n          <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>\n            <WarningIcon color=\"warning\" />\n            <Typography variant=\"h6\">{confirmDialogProps.title}</Typography>\n          </Box>\n        </DialogTitle>\n        <DialogContent>\n          <Typography variant=\"body1\" sx={{ mt: 2 }}>\n            {confirmDialogProps.message}\n          </Typography>\n        </DialogContent>\n        <DialogActions>\n          <Button onClick={() => setShowConfirmDialog(false)} color=\"secondary\" variant=\"outlined\">\n            Annulla\n          </Button>\n          <Button\n            onClick={() => {\n              setShowConfirmDialog(false);\n              confirmDialogProps.onConfirm();\n            }}\n            color=\"primary\"\n            variant=\"contained\"\n            autoFocus\n          >\n            Conferma\n          </Button>\n        </DialogActions>\n      </Dialog>\n\n      {/* Dialogo per cavi già posati */}\n      <Dialog open={showAlreadyLaidDialog} onClose={handleCloseAlreadyLaidDialog} maxWidth=\"sm\" fullWidth>\n        <DialogTitle sx={{ bgcolor: 'warning.light' }}>\n          <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>\n            <WarningIcon color=\"warning\" />\n            <Typography variant=\"h6\">Cavo già posato</Typography>\n          </Box>\n        </DialogTitle>\n        <DialogContent>\n          {alreadyLaidCavo && (\n            <Box sx={{ mt: 2 }}>\n              <Typography variant=\"body1\" paragraph>\n                Il cavo <strong>{alreadyLaidCavo.id_cavo}</strong> risulta già posato ({alreadyLaidCavo.metratura_reale || 0}m).\n              </Typography>\n              <Typography variant=\"body1\" paragraph>\n                Puoi scegliere di:\n              </Typography>\n              <Typography variant=\"body2\" component=\"ul\">\n                <li>Modificare la bobina associata al cavo</li>\n                <li>Selezionare un altro cavo</li>\n                <li>Annullare l'operazione</li>\n              </Typography>\n            </Box>\n          )}\n        </DialogContent>\n        <DialogActions sx={{ p: 2, justifyContent: 'space-between' }}>\n          <Button onClick={handleCloseAlreadyLaidDialog} color=\"secondary\">\n            Annulla operazione\n          </Button>\n          <Box>\n            <Button onClick={handleSelectAnotherCable} color=\"primary\" sx={{ mr: 1 }}>\n              Seleziona altro cavo\n            </Button>\n            <Button onClick={handleModifyReel} variant=\"contained\" color=\"primary\">\n              Modifica bobina\n            </Button>\n          </Box>\n        </DialogActions>\n      </Dialog>\n\n      {/* Dialogo per bobine incompatibili */}\n      <IncompatibleReelDialog\n        open={showIncompatibleReelDialog}\n        onClose={handleCloseIncompatibleReelDialog}\n        cavo={incompatibleReelData.cavo}\n        bobina={incompatibleReelData.bobina}\n        onUpdateCavo={handleUpdateCavoToMatchReel}\n        onSelectAnotherReel={handleSelectAnotherReel}\n        onContinueWithIncompatible={handleContinueWithIncompatible}\n      />\n\n      {/* Dialogo per visualizzare i dettagli del cavo */}\n      <Dialog\n        open={showCavoDetailsDialog}\n        onClose={() => setShowCavoDetailsDialog(false)}\n        maxWidth=\"md\"\n        fullWidth\n      >\n        <DialogTitle>\n          <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>\n            <InfoIcon color=\"primary\" />\n            <Typography variant=\"h6\">Dettagli Cavo</Typography>\n          </Box>\n        </DialogTitle>\n        <DialogContent>\n          <CavoDetailsView cavo={selectedCavo} />\n        </DialogContent>\n        <DialogActions>\n          <Button onClick={() => setShowCavoDetailsDialog(false)} color=\"primary\">\n            Chiudi\n          </Button>\n        </DialogActions>\n      </Dialog>\n    </Box>\n  );\n};\n\nexport default InserisciMetriForm;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAClD,OAAOC,KAAK,MAAM,OAAO;AACzB,SACEC,GAAG,EACHC,KAAK,EACLC,UAAU,EACVC,SAAS,EACTC,MAAM,EACNC,IAAI,EACJC,WAAW,EACXC,UAAU,EACVC,MAAM,EACNC,QAAQ,EACRC,OAAO,EACPC,KAAK,EACLC,gBAAgB,EAChBC,cAAc,EACdC,UAAU,EACVC,IAAI,EACJC,cAAc,EACdC,MAAM,EACNC,WAAW,EACXC,aAAa,EACbC,aAAa,EACbC,KAAK,EACLC,SAAS,EACTC,SAAS,EACTC,cAAc,EACdC,SAAS,EACTC,QAAQ,EACRC,IAAI,EACJC,QAAQ,EACRC,cAAc,EACdC,YAAY,EACZC,uBAAuB,QAClB,eAAe;AACtB,SACEC,MAAM,IAAIC,UAAU,EACpBC,IAAI,IAAIC,QAAQ,EAChBC,MAAM,IAAIC,UAAU,EACpBC,OAAO,IAAIC,WAAW,EACtBC,IAAI,IAAIC,QAAQ,EAChBC,gBAAgB,IAAIC,oBAAoB,QACnC,qBAAqB;AAC5B,SAASC,WAAW,QAAQ,kBAAkB;AAC9C,OAAOC,WAAW,MAAM,4BAA4B;AACpD,OAAOC,aAAa,MAAM,4BAA4B;AACtD,OAAOC,sBAAsB,MAAM,0BAA0B;AAC7D,OAAOC,eAAe,MAAM,mBAAmB;AAC/C,SACEC,YAAY,EACZC,WAAW,EACXC,mBAAmB,EACnBC,kBAAkB,EAClBC,cAAc,EACdC,YAAY,EACZC,gBAAgB,EAChBC,kBAAkB,EAClBC,iBAAiB,QACZ,wBAAwB;AAC/B,OAAOC,gBAAgB,MAAM,iCAAiC;AAC9D,SAASC,wBAAwB,QAAQ,6BAA6B;;AAEtE;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AARA,SAAAC,MAAA,IAAAC,OAAA,EAAAC,QAAA,IAAAC,SAAA;AASA,MAAMC,kBAAkB,GAAGA,CAAC;EAAEC,UAAU;EAAEC,SAAS;EAAEC;AAAQ,CAAC,KAAK;EAAAC,EAAA;EACjE,MAAMC,QAAQ,GAAGzB,WAAW,CAAC,CAAC;;EAE9B;EACA,MAAM,CAAC0B,OAAO,EAAEC,UAAU,CAAC,GAAG1E,QAAQ,CAAC,KAAK,CAAC;EAC7C,MAAM,CAAC2E,WAAW,EAAEC,cAAc,CAAC,GAAG5E,QAAQ,CAAC,KAAK,CAAC;EACrD,MAAM,CAAC6E,aAAa,EAAEC,gBAAgB,CAAC,GAAG9E,QAAQ,CAAC,KAAK,CAAC;EACzD,MAAM,CAAC+E,aAAa,EAAEC,gBAAgB,CAAC,GAAGhF,QAAQ,CAAC,EAAE,CAAC;EACtD,MAAM,CAACiF,iBAAiB,EAAEC,oBAAoB,CAAC,GAAGlF,QAAQ,CAAC,KAAK,CAAC;;EAEjE;EACA,MAAM,CAACmF,IAAI,EAAEC,OAAO,CAAC,GAAGpF,QAAQ,CAAC,EAAE,CAAC;EACpC,MAAM,CAACqF,MAAM,EAAEC,SAAS,CAAC,GAAGtF,QAAQ,CAAC,EAAE,CAAC;EACxC,MAAM,CAACuF,YAAY,EAAEC,eAAe,CAAC,GAAGxF,QAAQ,CAAC,IAAI,CAAC;EACtD,MAAM,CAACyF,WAAW,EAAEC,cAAc,CAAC,GAAG1F,QAAQ,CAAC,EAAE,CAAC;;EAElD;EACA,MAAM,CAAC2F,UAAU,EAAEC,aAAa,CAAC,GAAG5F,QAAQ,CAAC,CAAC,CAAC;;EAE/C;EACA,MAAM,CAAC6F,QAAQ,EAAEC,WAAW,CAAC,GAAG9F,QAAQ,CAAC;IACvC+F,OAAO,EAAE,EAAE;IACXC,YAAY,EAAE,EAAE;IAChBC,SAAS,EAAE;EACb,CAAC,CAAC;;EAEF;EACA,MAAM,CAACC,UAAU,EAAEC,aAAa,CAAC,GAAGnG,QAAQ,CAAC,CAAC,CAAC,CAAC;EAChD,MAAM,CAACoG,YAAY,EAAEC,eAAe,CAAC,GAAGrG,QAAQ,CAAC,CAAC,CAAC,CAAC;;EAEpD;EACA,MAAM,CAACsG,0BAA0B,EAAEC,6BAA6B,CAAC,GAAGvG,QAAQ,CAAC,KAAK,CAAC;EACnF,MAAM,CAACwG,gBAAgB,EAAEC,mBAAmB,CAAC,GAAGzG,QAAQ,CAAC,IAAI,CAAC,CAAC,CAAC;EAChE,MAAM,CAAC0G,oBAAoB,EAAEC,uBAAuB,CAAC,GAAG3G,QAAQ,CAAC;IAAE4G,IAAI,EAAE,IAAI;IAAEC,MAAM,EAAE;EAAK,CAAC,CAAC;EAC9F,MAAM,CAACC,qBAAqB,EAAEC,wBAAwB,CAAC,GAAG/G,QAAQ,CAAC,KAAK,CAAC;;EAEzE;EACA,MAAM,CAACgH,UAAU,EAAEC,aAAa,CAAC,GAAGjH,QAAQ,CAAC,EAAE,CAAC;EAChD,MAAM,CAACkH,eAAe,EAAEC,kBAAkB,CAAC,GAAGnH,QAAQ,CAAC,IAAI,CAAC;EAC5D,MAAM,CAACoH,qBAAqB,EAAEC,wBAAwB,CAAC,GAAGrH,QAAQ,CAAC,KAAK,CAAC;;EAEzE;EACA,MAAMsH,kBAAkB,GAAIC,QAAQ,IAAK;IACvCC,OAAO,CAACC,GAAG,CAAC,qBAAqB,EAAEF,QAAQ,CAAC;IAC5CzB,WAAW,CAAC;MACV,GAAGD,QAAQ;MACXI,SAAS,EAAEsB;IACb,CAAC,CAAC;;IAEF;IACApB,aAAa,CAACuB,IAAI,KAAK;MACrB,GAAGA,IAAI;MACPzB,SAAS,EAAE;IACb,CAAC,CAAC,CAAC;;IAEH;IACA,MAAM0B,cAAc,GAAGtC,MAAM,CAACuC,IAAI,CAACC,CAAC,IAAIA,CAAC,CAAC5B,SAAS,KAAKsB,QAAQ,CAAC;IACjE,IAAII,cAAc,EAAE;MAClBH,OAAO,CAACC,GAAG,CAAC,8BAA8B,EAAEE,cAAc,CAAC;IAC7D;EACF,CAAC;;EAGD;EACA,MAAMG,sBAAsB,GAAIC,KAAK,IAAK;IACxCd,aAAa,CAACc,KAAK,CAACC,MAAM,CAACC,KAAK,CAAC;EACnC,CAAC;;EAED;EACAhI,SAAS,CAAC,MAAM;IACdiI,UAAU,CAAC,CAAC;EACd,CAAC,EAAE,CAAC9D,UAAU,CAAC,CAAC;;EAEhB;EACA,MAAM+D,eAAe,GAAIZ,QAAQ,IAAK;IACpC;IACA,IAAIA,QAAQ,IAAIA,QAAQ,CAACa,QAAQ,CAAC,IAAI,CAAC,EAAE;MACvC,OAAOb,QAAQ,CAACc,KAAK,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;IAChC;IACA,OAAOd,QAAQ;EACjB,CAAC;;EAED;EACA,MAAMe,QAAQ,GAAG,MAAAA,CAAA,KAAY;IAC3B,IAAI;MACF1D,cAAc,CAAC,IAAI,CAAC;MACpB4C,OAAO,CAACC,GAAG,CAAC,oCAAoCrD,UAAU,KAAK,CAAC;;MAEhE;MACA,IAAI;QACF,MAAMmE,QAAQ,GAAG,MAAMvF,WAAW,CAACwF,OAAO,CAACpE,UAAU,CAAC;QACtDoD,OAAO,CAACC,GAAG,CAAC,YAAYc,QAAQ,CAACE,MAAM,OAAO,CAAC;;QAE/C;QACA;QACArD,OAAO,CAACmD,QAAQ,CAAC;MACnB,CAAC,CAAC,OAAOG,SAAS,EAAE;QAClBlB,OAAO,CAACmB,KAAK,CAAC,qDAAqD,EAAED,SAAS,CAAC;;QAE/E;QACA,IAAIA,SAAS,CAACE,cAAc,IAAI,CAACF,SAAS,CAACG,QAAQ,IAC/CH,SAAS,CAACI,IAAI,KAAK,cAAc,IAChCJ,SAAS,CAACK,OAAO,IAAIL,SAAS,CAACK,OAAO,CAACX,QAAQ,CAAC,eAAe,CAAE,EAAE;UAEtEZ,OAAO,CAACC,GAAG,CAAC,yDAAyD,CAAC;UACtE,MAAM,IAAIuB,OAAO,CAACC,OAAO,IAAIC,UAAU,CAACD,OAAO,EAAE,IAAI,CAAC,CAAC;UAEvD,IAAI;YACF;YACA,MAAME,KAAK,GAAGC,YAAY,CAACC,OAAO,CAAC,OAAO,CAAC;YAC3C,MAAMC,OAAO,GAAGrG,aAAa,CAACsG,QAAQ,CAACC,OAAO;;YAE9C;YACA,MAAMC,aAAa,GAAG,MAAMvJ,KAAK,CAACwJ,GAAG,CACnC,GAAGJ,OAAO,SAASlF,UAAU,EAAE,EAC/B;cACEuF,OAAO,EAAE;gBACP,eAAe,EAAE,UAAUR,KAAK;cAClC,CAAC;cACDS,OAAO,EAAE,KAAK,CAAC;YACjB,CACF,CAAC;YAEDpC,OAAO,CAACC,GAAG,CAAC,2CAA2CgC,aAAa,CAACI,IAAI,CAACpB,MAAM,OAAO,CAAC;YACxFrD,OAAO,CAACqE,aAAa,CAACI,IAAI,CAAC;UAC7B,CAAC,CAAC,OAAOC,UAAU,EAAE;YACnBtC,OAAO,CAACmB,KAAK,CAAC,uCAAuC,EAAEmB,UAAU,CAAC;YAClE,MAAMA,UAAU;UAClB;QACF,CAAC,MAAM;UACL,MAAMpB,SAAS;QACjB;MACF;IACF,CAAC,CAAC,OAAOC,KAAK,EAAE;MACdnB,OAAO,CAACmB,KAAK,CAAC,kCAAkC,EAAEA,KAAK,CAAC;;MAExD;MACA,IAAIoB,YAAY,GAAG,iCAAiC;MAEpD,IAAIpB,KAAK,CAACC,cAAc,EAAE;QACxBmB,YAAY,GAAG,8DAA8D;MAC/E,CAAC,MAAM,IAAIpB,KAAK,CAACqB,MAAM,EAAE;QACvBD,YAAY,GAAGpB,KAAK,CAACqB,MAAM;MAC7B,CAAC,MAAM,IAAIrB,KAAK,CAACI,OAAO,EAAE;QACxBgB,YAAY,GAAGpB,KAAK,CAACI,OAAO;MAC9B;MAEAzE,OAAO,CAACyF,YAAY,CAAC;IACvB,CAAC,SAAS;MACRnF,cAAc,CAAC,KAAK,CAAC;IACvB;EACF,CAAC;;EAED;EACA,MAAMsD,UAAU,GAAG,MAAAA,CAAA,KAAY;IAC7B,IAAI;MACFpD,gBAAgB,CAAC,IAAI,CAAC;MACtB0C,OAAO,CAACC,GAAG,CAAC,gCAAgC,CAAC;;MAE7C;MACA,MAAMwC,UAAU,GAAG,MAAMpG,gBAAgB,CAACqG,SAAS,CAAC9F,UAAU,CAAC;MAC/DoD,OAAO,CAACC,GAAG,CAAC,oBAAoBwC,UAAU,CAACxB,MAAM,EAAE,CAAC;;MAEpD;MACA,MAAM0B,kBAAkB,GAAGF,UAAU,CAACG,MAAM,CAACvD,MAAM,IACjDA,MAAM,CAACwD,YAAY,KAAK,WAAW,IACnCxD,MAAM,CAACwD,YAAY,KAAK,MAAM,IAC9BxD,MAAM,CAACyD,aAAa,GAAG,CACzB,CAAC;MAED9C,OAAO,CAACC,GAAG,CAAC,wBAAwB0C,kBAAkB,CAAC1B,MAAM,EAAE,CAAC;;MAEhE;MACA,IAAIlD,YAAY,IAAIA,YAAY,CAACgF,SAAS,IAAIhF,YAAY,CAACiF,OAAO,EAAE;QAClEhD,OAAO,CAACC,GAAG,CAAC,sDAAsD,CAAC;QACnED,OAAO,CAACC,GAAG,CAAC,YAAY,EAAE;UACxB1B,OAAO,EAAER,YAAY,CAACQ,OAAO;UAC7BwE,SAAS,EAAEhF,YAAY,CAACgF,SAAS;UACjCC,OAAO,EAAEjF,YAAY,CAACiF;QACxB,CAAC,CAAC;;QAEF;QACA,MAAMC,aAAa,GAAGC,MAAM,CAACnF,YAAY,CAACgF,SAAS,IAAI,EAAE,CAAC,CAACI,IAAI,CAAC,CAAC,CAACC,WAAW,CAAC,CAAC;QAC/E,MAAMC,WAAW,GAAGH,MAAM,CAACnF,YAAY,CAACiF,OAAO,IAAI,GAAG,CAAC,CAACG,IAAI,CAAC,CAAC;;QAE9D;QACA,MAAMG,iBAAiB,GAAG,EAAE;QAC5B,MAAMC,oBAAoB,GAAG,EAAE;;QAE/B;QACAZ,kBAAkB,CAACa,OAAO,CAACnE,MAAM,IAAI;UACnC,MAAMoE,eAAe,GAAGP,MAAM,CAAC7D,MAAM,CAAC0D,SAAS,IAAI,EAAE,CAAC,CAACI,IAAI,CAAC,CAAC,CAACC,WAAW,CAAC,CAAC;UAC3E,MAAMM,aAAa,GAAGR,MAAM,CAAC7D,MAAM,CAAC2D,OAAO,IAAI,GAAG,CAAC,CAACG,IAAI,CAAC,CAAC;;UAE1D;UACA,MAAMQ,cAAc,GAAGF,eAAe,KAAKR,aAAa;UACxD,MAAMW,YAAY,GAAGF,aAAa,KAAKL,WAAW;UAClD,MAAMQ,YAAY,GAAGF,cAAc,IAAIC,YAAY;UAEnD5D,OAAO,CAACC,GAAG,CAAC,iCAAiCZ,MAAM,CAACZ,SAAS,GAAG,EAAE;YAChE,kBAAkB,EAAE,IAAIY,MAAM,CAAC0D,SAAS,GAAG;YAC3C,gBAAgB,EAAE,IAAIhF,YAAY,CAACgF,SAAS,GAAG;YAC/C,mBAAmB,EAAEY,cAAc;YACnC,gBAAgB,EAAE,IAAIT,MAAM,CAAC7D,MAAM,CAAC2D,OAAO,CAAC,GAAG;YAC/C,cAAc,EAAE,IAAIE,MAAM,CAACnF,YAAY,CAACiF,OAAO,CAAC,GAAG;YACnD,iBAAiB,EAAEY,YAAY;YAC/B,cAAc,EAAEvE,MAAM,CAACwD,YAAY;YACnC,eAAe,EAAExD,MAAM,CAACyD,aAAa;YACrC,cAAc,EAAEe;UAClB,CAAC,CAAC;UAEF,IAAIA,YAAY,EAAE;YAChBP,iBAAiB,CAACQ,IAAI,CAACzE,MAAM,CAAC;UAChC,CAAC,MAAM;YACLkE,oBAAoB,CAACO,IAAI,CAACzE,MAAM,CAAC;UACnC;QACF,CAAC,CAAC;QAEFW,OAAO,CAACC,GAAG,CAAC,+BAA+BqD,iBAAiB,CAACrC,MAAM,EAAE,CAAC;QACtEjB,OAAO,CAACC,GAAG,CAAC,2BAA2BsD,oBAAoB,CAACtC,MAAM,EAAE,CAAC;;QAErE;QACA,IAAIqC,iBAAiB,CAACrC,MAAM,GAAG,CAAC,EAAE;UAChCjB,OAAO,CAACC,GAAG,CAAC,+BAA+B,CAAC;UAC5CqD,iBAAiB,CAACE,OAAO,CAACnE,MAAM,IAAI;YAClCW,OAAO,CAACC,GAAG,CAAC,KAAKZ,MAAM,CAACZ,SAAS,KAAKY,MAAM,CAAC0D,SAAS,MAAM1D,MAAM,CAAC2D,OAAO,KAAK3D,MAAM,CAACyD,aAAa,IAAI,CAAC;UAC1G,CAAC,CAAC;QACJ,CAAC,MAAM;UACL9C,OAAO,CAACC,GAAG,CAAC,iDAAiD,CAAC;QAChE;;QAEA;QACAqD,iBAAiB,CAACS,IAAI,CAAC,CAACC,CAAC,EAAE3D,CAAC,KAAKA,CAAC,CAACyC,aAAa,GAAGkB,CAAC,CAAClB,aAAa,CAAC;QACnES,oBAAoB,CAACQ,IAAI,CAAC,CAACC,CAAC,EAAE3D,CAAC,KAAKA,CAAC,CAACyC,aAAa,GAAGkB,CAAC,CAAClB,aAAa,CAAC;;QAEtE;QACA,MAAMmB,cAAc,GAAG,CAAC,GAAGX,iBAAiB,EAAE,GAAGC,oBAAoB,CAAC;;QAEtE;QACAzF,SAAS,CAACmG,cAAc,CAAC;MAC3B,CAAC,MAAM;QACL;QACAjE,OAAO,CAACC,GAAG,CAAC,uDAAuD,CAAC;QACpE0C,kBAAkB,CAACoB,IAAI,CAAC,CAACC,CAAC,EAAE3D,CAAC,KAAKA,CAAC,CAACyC,aAAa,GAAGkB,CAAC,CAAClB,aAAa,CAAC;QACpEhF,SAAS,CAAC6E,kBAAkB,CAAC;MAC/B;IACF,CAAC,CAAC,OAAOxB,KAAK,EAAE;MACdnB,OAAO,CAACmB,KAAK,CAAC,sCAAsC,EAAEA,KAAK,CAAC;MAC5DrE,OAAO,CAAC,uCAAuC,IAAIqE,KAAK,CAACI,OAAO,IAAI,oBAAoB,CAAC,CAAC;IAC5F,CAAC,SAAS;MACRjE,gBAAgB,CAAC,KAAK,CAAC;IACzB;EACF,CAAC;;EAED;EACA,MAAM4G,oBAAoB,GAAG,MAAAA,CAAA,KAAY;IACvC,IAAI,CAACjG,WAAW,CAACkF,IAAI,CAAC,CAAC,EAAE;MACvBrG,OAAO,CAAC,6BAA6B,CAAC;MACtC;IACF;IAEA,IAAI;MACFM,cAAc,CAAC,IAAI,CAAC;;MAEpB;MACA,MAAM+G,WAAW,GAAGlG,WAAW,CAAC4C,KAAK,CAAC,GAAG,CAAC,CAACuD,GAAG,CAACC,IAAI,IAAIA,IAAI,CAAClB,IAAI,CAAC,CAAC,CAAC,CAACP,MAAM,CAACyB,IAAI,IAAIA,IAAI,CAACpD,MAAM,GAAG,CAAC,CAAC;MACnGjB,OAAO,CAACC,GAAG,CAAC,oBAAoBkE,WAAW,CAAClD,MAAM,aAAakD,WAAW,CAACG,IAAI,CAAC,IAAI,CAAC,iBAAiB1H,UAAU,EAAE,CAAC;;MAEnH;MACA,MAAMmE,QAAQ,GAAG,MAAMvF,WAAW,CAACwF,OAAO,CAACpE,UAAU,CAAC;;MAEtD;MACA,MAAM2H,YAAY,GAAGxD,QAAQ,CAAC6B,MAAM,CAACxD,IAAI,IACvC+E,WAAW,CAACK,IAAI,CAACH,IAAI,IACnBjF,IAAI,CAACb,OAAO,CAAC6E,WAAW,CAAC,CAAC,CAACxC,QAAQ,CAACyD,IAAI,CAACjB,WAAW,CAAC,CAAC,CACxD,CACF,CAAC;MAEDpD,OAAO,CAACC,GAAG,CAAC,WAAWsE,YAAY,CAACtD,MAAM,4CAA4C,CAAC;;MAEvF;MACA,MAAMwD,YAAY,GAAGF,YAAY,CAAC3B,MAAM,CAACxD,IAAI,IAC3C+E,WAAW,CAACK,IAAI,CAACH,IAAI,IACnBjF,IAAI,CAACb,OAAO,CAAC6E,WAAW,CAAC,CAAC,KAAKiB,IAAI,CAACjB,WAAW,CAAC,CAClD,CACF,CAAC;MAED,IAAIqB,YAAY,CAACxD,MAAM,KAAK,CAAC,EAAE;QAC7B;QACAjB,OAAO,CAACC,GAAG,CAAC,oCAAoC,EAAEwE,YAAY,CAAC,CAAC,CAAC,CAAC;;QAElE;QACA,IAAIA,YAAY,CAAC,CAAC,CAAC,CAACC,mBAAmB,KAAK,YAAY,IACnDD,YAAY,CAAC,CAAC,CAAC,CAACE,eAAe,IAAIF,YAAY,CAAC,CAAC,CAAC,CAACE,eAAe,GAAG,CAAE,EAAE;UAC5E3E,OAAO,CAACC,GAAG,CAAC,sCAAsC,EAAEwE,YAAY,CAAC,CAAC,CAAC,CAAC;UACpE9E,kBAAkB,CAAC8E,YAAY,CAAC,CAAC,CAAC,CAAC;UACnClF,wBAAwB,CAAC,IAAI,CAAC;UAC9BnC,cAAc,CAAC,KAAK,CAAC;UACrB;QACF;;QAEA;QACA,IAAIqH,YAAY,CAAC,CAAC,CAAC,CAACG,sBAAsB,KAAK,CAAC,EAAE;UAChD5E,OAAO,CAACC,GAAG,CAAC,qBAAqB,EAAEwE,YAAY,CAAC,CAAC,CAAC,CAAC;UACnD;QACF;;QAEA;QACAI,gBAAgB,CAACJ,YAAY,CAAC,CAAC,CAAC,CAAC;MACnC,CAAC,MAAM,IAAIF,YAAY,CAACtD,MAAM,GAAG,CAAC,EAAE;QAClC;QACAzD,gBAAgB,CAAC+G,YAAY,CAAC;QAC9B7G,oBAAoB,CAAC,IAAI,CAAC;MAC5B,CAAC,MAAM;QACL;QACAZ,OAAO,CAAC,sCAAsCqH,WAAW,CAACG,IAAI,CAAC,IAAI,CAAC,kBAAkB1H,UAAU,EAAE,CAAC;MACrG;IACF,CAAC,CAAC,OAAOuE,KAAK,EAAE;MACdnB,OAAO,CAACmB,KAAK,CAAC,gCAAgC,EAAEA,KAAK,CAAC;;MAEtD;MACA,IAAIoB,YAAY,GAAG,+BAA+B;MAElD,IAAIpB,KAAK,CAACC,cAAc,EAAE;QACxBmB,YAAY,GAAG,8DAA8D;MAC/E,CAAC,MAAM,IAAIpB,KAAK,CAAC2D,MAAM,KAAK,GAAG,EAAE;QAC/BvC,YAAY,GAAG,gBAAgBtE,WAAW,CAACkF,IAAI,CAAC,CAAC,8BAA8BvG,UAAU,EAAE;MAC7F,CAAC,MAAM,IAAIuE,KAAK,CAACqB,MAAM,EAAE;QACvBD,YAAY,GAAGpB,KAAK,CAACqB,MAAM;MAC7B,CAAC,MAAM,IAAIrB,KAAK,CAACI,OAAO,EAAE;QACxBgB,YAAY,GAAGpB,KAAK,CAACI,OAAO;MAC9B;MAEAzE,OAAO,CAACyF,YAAY,CAAC;IACvB,CAAC,SAAS;MACRnF,cAAc,CAAC,KAAK,CAAC;IACvB;EACF,CAAC;;EAED;EACA,MAAMyH,gBAAgB,GAAIzF,IAAI,IAAK;IACjC;IACA,IAAIA,IAAI,CAACsF,mBAAmB,KAAK,YAAY,IAAKtF,IAAI,CAACuF,eAAe,IAAIvF,IAAI,CAACuF,eAAe,GAAG,CAAE,EAAE;MACnG;MACAhF,kBAAkB,CAACP,IAAI,CAAC;MACxBG,wBAAwB,CAAC,IAAI,CAAC;MAC9B;IACF;IACA;IAAA,KACK,IAAIH,IAAI,CAACwF,sBAAsB,KAAK,CAAC,EAAE;MAC1C;MACA,IAAIG,MAAM,CAACC,OAAO,CAAC,WAAW5F,IAAI,CAACb,OAAO,0CAA0C,CAAC,EAAE;QACrF;QACA0G,eAAe,CAAC7F,IAAI,CAACb,OAAO,CAAC,CAAC2G,IAAI,CAAC,MAAM;UACvC;UACA,MAAMC,WAAW,GAAG;YAAE,GAAG/F,IAAI;YAAEwF,sBAAsB,EAAE;UAAE,CAAC;UAC1D5G,eAAe,CAACmH,WAAW,CAAC;UAC5B7G,WAAW,CAAC;YACV,GAAGD,QAAQ;YACXE,OAAO,EAAE4G,WAAW,CAAC5G,OAAO;YAC5BC,YAAY,EAAE;UAChB,CAAC,CAAC;UACF;UACAd,oBAAoB,CAAC,KAAK,CAAC;;UAE3B;UACAgD,UAAU,CAAC,CAAC;QACd,CAAC,CAAC,CAAC0E,KAAK,CAACjE,KAAK,IAAI;UAChBnB,OAAO,CAACmB,KAAK,CAAC,iDAAiD,EAAEA,KAAK,CAAC;UACvErE,OAAO,CAAC,kDAAkD,IAAIqE,KAAK,CAACI,OAAO,IAAI,oBAAoB,CAAC,CAAC;QACvG,CAAC,CAAC;MACJ,CAAC,MAAM;QACL;QACA;MACF;IACF,CAAC,MAAM;MACL;MACAvD,eAAe,CAACoB,IAAI,CAAC;MACrBd,WAAW,CAAC;QACV,GAAGD,QAAQ;QACXE,OAAO,EAAEa,IAAI,CAACb,OAAO;QACrBC,YAAY,EAAE;MAChB,CAAC,CAAC;MACF;MACAd,oBAAoB,CAAC,KAAK,CAAC;;MAE3B;MACA,IAAI0B,IAAI,CAAC2D,SAAS,IAAI3D,IAAI,CAACiG,YAAY,IAAIjG,IAAI,CAAC4D,OAAO,EAAE;QACvDhD,OAAO,CAACC,GAAG,CAAC,8CAA8Cb,IAAI,CAACb,OAAO,KAAK,CAAC;QAC5EmC,UAAU,CAAC,CAAC;MACd;IACF;EACF,CAAC;;EAED;EACA,MAAMuE,eAAe,GAAG,MAAOK,MAAM,IAAK;IACxC,IAAI;MACF;MACA,MAAM9J,WAAW,CAACyJ,eAAe,CAACrI,UAAU,EAAE0I,MAAM,CAAC;MACrDzI,SAAS,CAAC,QAAQyI,MAAM,0BAA0B,CAAC;MACnD,OAAO,IAAI;IACb,CAAC,CAAC,OAAOnE,KAAK,EAAE;MACdnB,OAAO,CAACmB,KAAK,CAAC,iDAAiD,EAAEA,KAAK,CAAC;MACvErE,OAAO,CAAC,kDAAkD,IAAIqE,KAAK,CAACI,OAAO,IAAI,oBAAoB,CAAC,CAAC;MACrG,MAAMJ,KAAK;IACb;EACF,CAAC;;EAED;EACA,MAAMoE,gBAAgB,GAAIC,CAAC,IAAK;IAC9B,MAAM;MAAEC,IAAI;MAAEhF;IAAM,CAAC,GAAG+E,CAAC,CAAChF,MAAM;;IAEhC;IACA,IAAIiF,IAAI,KAAK,WAAW,IAAIhF,KAAK,IAAIA,KAAK,KAAK,cAAc,EAAE;MAC7D;MACA,MAAMpB,MAAM,GAAGxB,MAAM,CAACuC,IAAI,CAACC,CAAC,IAAIA,CAAC,CAAC5B,SAAS,KAAKgC,KAAK,CAAC;MAEtD,IAAIpB,MAAM,IAAItB,YAAY,EAAE;QAC1B;QACA;QACA,MAAM8F,YAAY,GAChBX,MAAM,CAAC7D,MAAM,CAAC0D,SAAS,IAAI,EAAE,CAAC,CAACI,IAAI,CAAC,CAAC,KAAKD,MAAM,CAACnF,YAAY,CAACgF,SAAS,IAAI,EAAE,CAAC,CAACI,IAAI,CAAC,CAAC,IACrFD,MAAM,CAAC7D,MAAM,CAAC2D,OAAO,IAAI,GAAG,CAAC,CAACG,IAAI,CAAC,CAAC,KAAKD,MAAM,CAACnF,YAAY,CAACiF,OAAO,IAAI,GAAG,CAAC,CAACG,IAAI,CAAC,CAAC;QAErF,IAAI,CAACU,YAAY,EAAE;UACjB7D,OAAO,CAACC,GAAG,CAAC,mCAAmC,EAAEZ,MAAM,CAAC;UACxDW,OAAO,CAACC,GAAG,CAAC,gBAAgB,EAAElC,YAAY,CAAC;UAC3CiC,OAAO,CAACC,GAAG,CAAC,uBAAuB,EAAE;YACnCyF,cAAc,EAAExC,MAAM,CAACnF,YAAY,CAACiF,OAAO,IAAI,GAAG,CAAC;YACnD2C,gBAAgB,EAAEzC,MAAM,CAAC7D,MAAM,CAAC2D,OAAO,IAAI,GAAG;UAChD,CAAC,CAAC;;UAEF;UACA7D,uBAAuB,CAAC;YACtBC,IAAI,EAAErB,YAAY;YAClBsB,MAAM,EAAEA;UACV,CAAC,CAAC;UACFN,6BAA6B,CAAC,IAAI,CAAC;;UAEnC;UACA;QACF;MACF;IACF;IAEAT,WAAW,CAAC;MACV,GAAGD,QAAQ;MACX,CAACoH,IAAI,GAAGhF;IACV,CAAC,CAAC;;IAEF;IACAmF,aAAa,CAACH,IAAI,EAAEhF,KAAK,CAAC;EAC5B,CAAC;;EAED;EACA,MAAMmF,aAAa,GAAGA,CAACH,IAAI,EAAEhF,KAAK,KAAK;IACrC,IAAIU,KAAK,GAAG,IAAI;IAChB,IAAI0E,OAAO,GAAG,IAAI;IAElB,IAAIJ,IAAI,KAAK,cAAc,EAAE;MAC3B;MACA,IAAI,CAAChF,KAAK,IAAIA,KAAK,CAAC0C,IAAI,CAAC,CAAC,KAAK,EAAE,EAAE;QACjChC,KAAK,GAAG,uCAAuC;QAC/C,OAAO,KAAK;MACd;;MAEA;MACA,IAAI2E,KAAK,CAACC,UAAU,CAACtF,KAAK,CAAC,CAAC,IAAIsF,UAAU,CAACtF,KAAK,CAAC,IAAI,CAAC,EAAE;QACtDU,KAAK,GAAG,sCAAsC;QAC9C,OAAO,KAAK;MACd;MAEA,MAAM6E,WAAW,GAAGD,UAAU,CAACtF,KAAK,CAAC;;MAErC;MACA,IAAI1C,YAAY,IAAIA,YAAY,CAACkI,aAAa,IAAID,WAAW,GAAGD,UAAU,CAAChI,YAAY,CAACkI,aAAa,CAAC,EAAE;QACtGJ,OAAO,GAAG,mBAAmBG,WAAW,yCAAyCjI,YAAY,CAACkI,aAAa,IAAI;MACjH;;MAEA;MACA,IAAI5H,QAAQ,CAACI,SAAS,IAAIJ,QAAQ,CAACI,SAAS,KAAK,cAAc,EAAE;QAC/D,MAAMY,MAAM,GAAGxB,MAAM,CAACuC,IAAI,CAACC,CAAC,IAAIA,CAAC,CAAC5B,SAAS,KAAKJ,QAAQ,CAACI,SAAS,CAAC;QACnE,IAAIY,MAAM,IAAI2G,WAAW,GAAGD,UAAU,CAAC1G,MAAM,CAACyD,aAAa,CAAC,EAAE;UAC5D+C,OAAO,GAAG,mBAAmBG,WAAW,6CAA6C3G,MAAM,CAACyD,aAAa,oCAAoC;QAC/I;MACF;IACF,CAAC,MAAM,IAAI2C,IAAI,KAAK,WAAW,EAAE;MAC/B;MACA,IAAI,CAAChF,KAAK,IAAIA,KAAK,CAAC0C,IAAI,CAAC,CAAC,KAAK,EAAE,EAAE;QACjChC,KAAK,GAAG,qCAAqC;QAC7C,OAAO,KAAK;MACd;IACF;;IAEA;IACAxC,aAAa,CAACuB,IAAI,KAAK;MACrB,GAAGA,IAAI;MACP,CAACuF,IAAI,GAAGtE;IACV,CAAC,CAAC,CAAC;;IAEH;IACAtC,eAAe,CAACqB,IAAI,KAAK;MACvB,GAAGA,IAAI;MACP,CAACuF,IAAI,GAAGI;IACV,CAAC,CAAC,CAAC;IAEH,OAAO,CAAC1E,KAAK;EACf,CAAC;;EAED;EACA,MAAM,CAAC+E,iBAAiB,EAAEC,oBAAoB,CAAC,GAAG3N,QAAQ,CAAC,KAAK,CAAC;EACjE,MAAM,CAAC4N,iBAAiB,EAAEC,oBAAoB,CAAC,GAAG7N,QAAQ,CAAC,KAAK,CAAC;EACjE,MAAM,CAAC8N,kBAAkB,EAAEC,qBAAqB,CAAC,GAAG/N,QAAQ,CAAC;IAC3DgO,KAAK,EAAE,EAAE;IACTjF,OAAO,EAAE,EAAE;IACXkF,SAAS,EAAEA,CAAA,KAAM,CAAC;EACpB,CAAC,CAAC;;EAEF;;EAEA;EACA,MAAMC,YAAY,GAAGA,CAAA,KAAM;IACzB,IAAIC,OAAO,GAAG,IAAI;IAClB,MAAMC,MAAM,GAAG,CAAC,CAAC;IACjB,MAAMC,QAAQ,GAAG,CAAC,CAAC;;IAEnB;IACAV,oBAAoB,CAAC,KAAK,CAAC;;IAE3B;IACA,IAAI,CAAC9H,QAAQ,CAACG,YAAY,IAAIH,QAAQ,CAACG,YAAY,CAAC2E,IAAI,CAAC,CAAC,KAAK,EAAE,EAAE;MACjEyD,MAAM,CAACpI,YAAY,GAAG,uCAAuC;MAC7DmI,OAAO,GAAG,KAAK;IACjB,CAAC,MAAM,IAAIb,KAAK,CAACC,UAAU,CAAC1H,QAAQ,CAACG,YAAY,CAAC,CAAC,IAAIuH,UAAU,CAAC1H,QAAQ,CAACG,YAAY,CAAC,IAAI,CAAC,EAAE;MAC7FoI,MAAM,CAACpI,YAAY,GAAG,sCAAsC;MAC5DmI,OAAO,GAAG,KAAK;IACjB;;IAEA;IACA,IAAI,CAACtI,QAAQ,CAACI,SAAS,IAAIJ,QAAQ,CAACI,SAAS,CAAC0E,IAAI,CAAC,CAAC,KAAK,EAAE,EAAE;MAC3DyD,MAAM,CAACnI,SAAS,GAAG,qCAAqC;MACxDkI,OAAO,GAAG,KAAK;IACjB;IAEA3G,OAAO,CAACC,GAAG,CAAC,mBAAmB,EAAE;MAAE0G,OAAO;MAAEC,MAAM;MAAEC,QAAQ;MAAExI;IAAS,CAAC,CAAC;IAEzE,IAAIsI,OAAO,EAAE;MACX,MAAMX,WAAW,GAAGD,UAAU,CAAC1H,QAAQ,CAACG,YAAY,CAAC;;MAErD;MACA,IAAIT,YAAY,IAAIA,YAAY,CAACkI,aAAa,IAAID,WAAW,GAAGD,UAAU,CAAChI,YAAY,CAACkI,aAAa,CAAC,EAAE;QACtGY,QAAQ,CAACrI,YAAY,GAAG,mBAAmBwH,WAAW,yCAAyCjI,YAAY,CAACkI,aAAa,IAAI;QAC7HE,oBAAoB,CAAC,IAAI,CAAC,CAAC,CAAC;QAC5B;QACA;MACF;;MAEA;MACA,IAAIQ,OAAO,IAAItI,QAAQ,CAACI,SAAS,IAAIJ,QAAQ,CAACI,SAAS,KAAK,cAAc,EAAE;QAC1E,MAAMY,MAAM,GAAGxB,MAAM,CAACuC,IAAI,CAACC,CAAC,IAAIA,CAAC,CAAC5B,SAAS,KAAKJ,QAAQ,CAACI,SAAS,CAAC;QACnE,IAAIY,MAAM,IAAI2G,WAAW,GAAGD,UAAU,CAAC1G,MAAM,CAACyD,aAAa,CAAC,EAAE;UAC5D+D,QAAQ,CAACrI,YAAY,GAAG,mBAAmBwH,WAAW,6CAA6C3G,MAAM,CAACyD,aAAa,oCAAoC;UAC3JqD,oBAAoB,CAAC,IAAI,CAAC,CAAC,CAAC;;UAE5B;UACAI,qBAAqB,CAAC;YACpBC,KAAK,EAAE,kCAAkC;YACzCjF,OAAO,EAAE,mBAAmByE,WAAW,6CAA6C3G,MAAM,CAACyD,aAAa,8DAA8D;YACtK2D,SAAS,EAAEA,CAAA,KAAM;cACf;cACAK,UAAU,CAAC,CAAC;YACd;UACF,CAAC,CAAC;UACFT,oBAAoB,CAAC,IAAI,CAAC;UAC1B,OAAO,KAAK,CAAC,CAAC;QAChB;MACF;IACF;IAEA1H,aAAa,CAACiI,MAAM,CAAC;IACrB/H,eAAe,CAACgI,QAAQ,CAAC;IACzB,OAAOF,OAAO;EAChB,CAAC;;EAED;EACA,MAAMG,UAAU,GAAGA,CAAA,KAAM;IACvB,IAAI3I,UAAU,KAAK,CAAC,EAAE;MACpB;MACA,IAAI,CAACuI,YAAY,CAAC,CAAC,EAAE;QACnB;MACF;IACF,CAAC,MAAM,IAAIvI,UAAU,KAAK,CAAC,EAAE;MAC3B;MACAuC,UAAU,CAAC,CAAC;IACd;IAEAtC,aAAa,CAAE2I,cAAc,IAAKA,cAAc,GAAG,CAAC,CAAC;EACvD,CAAC;;EAED;EACA,MAAMC,UAAU,GAAGA,CAAA,KAAM;IACvB5I,aAAa,CAAE2I,cAAc,IAAKA,cAAc,GAAG,CAAC,CAAC;EACvD,CAAC;;EAED;EACA,MAAME,WAAW,GAAGA,CAAA,KAAM;IACxB7I,aAAa,CAAC,CAAC,CAAC;IAChBJ,eAAe,CAAC,IAAI,CAAC;IACrBE,cAAc,CAAC,EAAE,CAAC;IAClBI,WAAW,CAAC;MACVC,OAAO,EAAE,EAAE;MACXC,YAAY,EAAE,EAAE;MAChBC,SAAS,EAAE;IACb,CAAC,CAAC;IACFE,aAAa,CAAC,CAAC,CAAC,CAAC;IACjBE,eAAe,CAAC,CAAC,CAAC,CAAC;EACrB,CAAC;;EAED;EACA;EACA,MAAMqI,2BAA2B,GAAGA,CAAClB,WAAW,EAAEmB,YAAY,KAAK;IACjE,OAAOrL,mBAAmB,CAACkK,WAAW,EAAEmB,YAAY,CAAC;EACvD,CAAC;;EAED;EACA,MAAMC,YAAY,GAAG,MAAAA,CAAA,KAAY;IAC/B;IACA;IACA,IAAIrH,QAAQ;IACZ,IAAIsH,kBAAkB;IACtB,IAAIrB,WAAW;IACf,IAAIsB,SAAS,GAAG,KAAK;IAErB,IAAI;MACFpK,UAAU,CAAC,IAAI,CAAC;;MAEhB;MACA,IAAI,CAACa,YAAY,IAAI,CAACM,QAAQ,CAACG,YAAY,IAAI,CAACH,QAAQ,CAACI,SAAS,EAAE;QAClEuB,OAAO,CAACC,GAAG,CAAC,sBAAsB,EAAE;UAClClC,YAAY,EAAE,CAAC,CAACA,YAAY;UAC5BS,YAAY,EAAE,CAAC,CAACH,QAAQ,CAACG,YAAY;UACrCC,SAAS,EAAE,CAAC,CAACJ,QAAQ,CAACI;QACxB,CAAC,CAAC;QACFvB,UAAU,CAAC,KAAK,CAAC;QACjB;MACF;;MAEA;MACA8C,OAAO,CAACC,GAAG,CAAC,qDAAqD,EAAE;QACjElC,YAAY,EAAEA,YAAY,CAACQ,OAAO;QAClCC,YAAY,EAAEH,QAAQ,CAACG,YAAY;QACnCC,SAAS,EAAEJ,QAAQ,CAACI;MACtB,CAAC,CAAC;;MAEF;MACAuH,WAAW,GAAGD,UAAU,CAAC1H,QAAQ,CAACG,YAAY,CAAC;;MAE/C;MACAuB,QAAQ,GAAG1B,QAAQ,CAACI,SAAS;;MAE7B;MACA,IAAI,CAACsB,QAAQ,IAAIA,QAAQ,KAAK,EAAE,EAAE;QAChC;QACApB,aAAa,CAAC;UACZ,GAAGD,UAAU;UACbD,SAAS,EAAE;QACb,CAAC,CAAC;QACFvB,UAAU,CAAC,KAAK,CAAC;QACjB;MACF,CAAC,MAAM,IAAI6C,QAAQ,KAAK,cAAc,EAAE;QACtC;QACAC,OAAO,CAACC,GAAG,CAAC,iCAAiC,CAAC;QAC9C;QACAF,QAAQ,GAAG,cAAc;MAC3B,CAAC,MAAM;QACL;QACAC,OAAO,CAACC,GAAG,CAAC,wBAAwBF,QAAQ,EAAE,CAAC;MACjD;;MAEA;MACAsH,kBAAkB,GAAGH,2BAA2B,CAAClB,WAAW,EAAEjI,YAAY,CAACkI,aAAa,CAAC;;MAEzF;MACAqB,SAAS,GAAG,IAAI;MAChBtH,OAAO,CAACC,GAAG,CAAC,6EAA6E,CAAC;;MAE1F;MACA,IAAIF,QAAQ,KAAK,cAAc,EAAE;QAC/BC,OAAO,CAACC,GAAG,CAAC,6BAA6B,CAAC;MAC5C;MACA;MAAA,KACK,IAAIF,QAAQ,IAAIA,QAAQ,KAAK,cAAc,EAAE;QAChD,MAAMV,MAAM,GAAGxB,MAAM,CAACuC,IAAI,CAACC,CAAC,IAAIA,CAAC,CAAC5B,SAAS,KAAKsB,QAAQ,CAAC;QACzD,IAAIV,MAAM,IAAI2G,WAAW,GAAGD,UAAU,CAAC1G,MAAM,CAACyD,aAAa,CAAC,EAAE;UAC5D9C,OAAO,CAACC,GAAG,CAAC,aAAaF,QAAQ,sCAAsCiG,WAAW,oBAAoB3G,MAAM,CAACyD,aAAa,GAAG,CAAC;QAChI;MACF;;MAEA;MACA,IAAI/E,YAAY,IAAIA,YAAY,CAACkI,aAAa,IAAID,WAAW,GAAGD,UAAU,CAAChI,YAAY,CAACkI,aAAa,CAAC,EAAE;QACtGjG,OAAO,CAACC,GAAG,CAAC,mBAAmB+F,WAAW,+BAA+BjI,YAAY,CAACkI,aAAa,GAAG,CAAC;MACzG;;MAEA;MACAjG,OAAO,CAACC,GAAG,CAAC,aAAa,EAAE;QACzBrD,UAAU;QACV0I,MAAM,EAAEjH,QAAQ,CAACE,OAAO;QACxByH,WAAW;QACXjG,QAAQ;QACRuH,SAAS;QACTD;MACF,CAAC,CAAC;;MAEF;MACA,IAAI,CAACnB,iBAAiB,EAAE;QACtB,MAAMqB,cAAc,GAAG,qCAAqClJ,QAAQ,CAACE,OAAO,QAAQyH,WAAW,WAAW;;QAE1G;QACAO,qBAAqB,CAAC;UACpBC,KAAK,EAAE,wBAAwB;UAC/BjF,OAAO,EAAEgG,cAAc;UACvBd,SAAS,EAAE,MAAAA,CAAA,KAAY;YACrB;YACA,IAAI;cACFvJ,UAAU,CAAC,IAAI,CAAC;;cAEhB;cACA8C,OAAO,CAACC,GAAG,CAAC,qGAAqG,CAAC;cAClH,MAAMzE,WAAW,CAACgM,iBAAiB,CACjC5K,UAAU,EACVyB,QAAQ,CAACE,OAAO,EAChByH,WAAW,EACXjG,QAAQ,EACR,IAAI,CAAC;cACP,CAAC;;cAED;cACA,IAAI0H,cAAc,GAAG,qDAAqDJ,kBAAkB,EAAE;cAC9F,IAAItH,QAAQ,KAAK,cAAc,EAAE;gBAC/B0H,cAAc,IAAI,iCAAiC;cACrD,CAAC,MAAM,IAAI1H,QAAQ,EAAE;gBACnB,MAAMV,MAAM,GAAGxB,MAAM,CAACuC,IAAI,CAACC,CAAC,IAAIA,CAAC,CAAC5B,SAAS,KAAKsB,QAAQ,CAAC;gBACzD,IAAIV,MAAM,EAAE;kBACVoI,cAAc,IAAI,gCAAgC1H,QAAQ,EAAE;gBAC9D;cACF;;cAEA;cACAlD,SAAS,CAAC4K,cAAc,CAAC;;cAEzB;cACAR,WAAW,CAAC,CAAC;;cAEb;cACAnG,QAAQ,CAAC,CAAC;YACZ,CAAC,CAAC,OAAOK,KAAK,EAAE;cACdnB,OAAO,CAACmB,KAAK,CAAC,mDAAmD,EAAEA,KAAK,CAAC;;cAEzE;cACA,IAAIpB,QAAQ,KAAK,cAAc,IAAIoB,KAAK,CAACuG,OAAO,EAAE;gBAChD;gBACA,IAAID,cAAc,GAAG,qDAAqDJ,kBAAkB,iCAAiC;gBAC7HxK,SAAS,CAAC4K,cAAc,CAAC;;gBAEzB;gBACAR,WAAW,CAAC,CAAC;;gBAEb;gBACAnG,QAAQ,CAAC,CAAC;gBACV;cACF;;cAEA;cACA,IAAIyB,YAAY,GAAG,kDAAkD;cAErE,IAAIpB,KAAK,CAACE,QAAQ,EAAE;gBAAA,IAAAsG,oBAAA;gBAClB;gBACA,MAAM7C,MAAM,GAAG3D,KAAK,CAACE,QAAQ,CAACyD,MAAM;gBACpC,MAAMtC,MAAM,GAAG,EAAAmF,oBAAA,GAAAxG,KAAK,CAACE,QAAQ,CAACgB,IAAI,cAAAsF,oBAAA,uBAAnBA,oBAAA,CAAqBnF,MAAM,KAAIrB,KAAK,CAACI,OAAO;gBAE3D,IAAIuD,MAAM,KAAK,GAAG,EAAE;kBAClB;kBACA,IAAItC,MAAM,CAAC5B,QAAQ,CAAC,eAAe,CAAC,EAAE;oBACpC2B,YAAY,GAAG,uGAAuG;kBACxH,CAAC,MAAM,IAAIC,MAAM,CAAC5B,QAAQ,CAAC,YAAY,CAAC,EAAE;oBACxC2B,YAAY,GAAG,4EAA4E;kBAC7F,CAAC,MAAM;oBACLA,YAAY,GAAGC,MAAM;kBACvB;gBACF,CAAC,MAAM,IAAIsC,MAAM,KAAK,GAAG,EAAE;kBACzB;kBACA,IAAI/E,QAAQ,KAAK,cAAc,IAAIyC,MAAM,CAAC5B,QAAQ,CAAC,aAAa,CAAC,EAAE;oBACjE,IAAI6G,cAAc,GAAG,qDAAqDJ,kBAAkB,iCAAiC;oBAC7HxK,SAAS,CAAC4K,cAAc,CAAC;;oBAEzB;oBACAR,WAAW,CAAC,CAAC;;oBAEb;oBACAnG,QAAQ,CAAC,CAAC;oBACV;kBACF,CAAC,MAAM;oBACLyB,YAAY,GAAG,8BAA8BC,MAAM,EAAE;kBACvD;gBACF,CAAC,MAAM;kBACLD,YAAY,GAAG,sBAAsBuC,MAAM,MAAMtC,MAAM,EAAE;gBAC3D;cACF,CAAC,MAAM,IAAIrB,KAAK,CAACyG,OAAO,EAAE;gBACxB;gBACArF,YAAY,GAAG,+DAA+D;cAChF,CAAC,MAAM,IAAIpB,KAAK,CAACqB,MAAM,IAAIzC,QAAQ,KAAK,cAAc,EAAE;gBACtD;gBACAwC,YAAY,GAAGpB,KAAK,CAACqB,MAAM;gBAC3B,IAAIrB,KAAK,CAAC2D,MAAM,KAAK,GAAG,IAAI3D,KAAK,CAACuG,OAAO,EAAE;kBACzC,IAAID,cAAc,GAAG,qDAAqDJ,kBAAkB,iCAAiC;kBAC7HxK,SAAS,CAAC4K,cAAc,CAAC;;kBAEzB;kBACAR,WAAW,CAAC,CAAC;;kBAEb;kBACAnG,QAAQ,CAAC,CAAC;kBACV;gBACF;cACF,CAAC,MAAM;gBACL;gBACAyB,YAAY,GAAGpB,KAAK,CAACI,OAAO,IAAIJ,KAAK,CAACqB,MAAM,IAAI,oBAAoB;cACtE;cAEA1F,OAAO,CAACyF,YAAY,CAAC;YACvB,CAAC,SAAS;cACRrF,UAAU,CAAC,KAAK,CAAC;YACnB;UACF;QACF,CAAC,CAAC;QACFmJ,oBAAoB,CAAC,IAAI,CAAC;QAC1BnJ,UAAU,CAAC,KAAK,CAAC;QACjB;MACF;;MAEA;MACA8C,OAAO,CAACC,GAAG,CAAC,6DAA6D,CAAC;MAC1ED,OAAO,CAACC,GAAG,CAAC,eAAe,EAAErD,UAAU,CAAC;MACxCoD,OAAO,CAACC,GAAG,CAAC,YAAY,EAAE5B,QAAQ,CAACE,OAAO,CAAC;MAC3CyB,OAAO,CAACC,GAAG,CAAC,iBAAiB,EAAE+F,WAAW,CAAC;MAC3ChG,OAAO,CAACC,GAAG,CAAC,cAAc,EAAEF,QAAQ,EAAE,OAAOA,QAAQ,CAAC;MACtDC,OAAO,CAACC,GAAG,CAAC,cAAc,EAAEqH,SAAS,CAAC;;MAEtC;MACAtH,OAAO,CAACC,GAAG,CAAC,oGAAoG,CAAC;MACjH,MAAMzE,WAAW,CAACgM,iBAAiB,CACjC5K,UAAU,EACVyB,QAAQ,CAACE,OAAO,EAChByH,WAAW,EACXjG,QAAQ,EACR,IAAI,CAAC;MACP,CAAC;;MAED;MACA,IAAI0H,cAAc,GAAG,qDAAqDJ,kBAAkB,EAAE;MAC9F,IAAItH,QAAQ,KAAK,cAAc,EAAE;QAC/B0H,cAAc,IAAI,iCAAiC;MACrD,CAAC,MAAM,IAAI1H,QAAQ,EAAE;QACnB,MAAMV,MAAM,GAAGxB,MAAM,CAACuC,IAAI,CAACC,CAAC,IAAIA,CAAC,CAAC5B,SAAS,KAAKsB,QAAQ,CAAC;QACzD,IAAIV,MAAM,EAAE;UACVoI,cAAc,IAAI,gCAAgC1H,QAAQ,EAAE;QAC9D;MACF;;MAEA;MACAlD,SAAS,CAAC4K,cAAc,CAAC;;MAEzB;MACAR,WAAW,CAAC,CAAC;;MAEb;MACAnG,QAAQ,CAAC,CAAC;IACZ,CAAC,CAAC,OAAOK,KAAK,EAAE;MACdnB,OAAO,CAACmB,KAAK,CAAC,mDAAmD,EAAEA,KAAK,CAAC;;MAEzE;MACA,IAAIpB,QAAQ,KAAK,cAAc,IAAIoB,KAAK,CAACuG,OAAO,EAAE;QAChD;QACA,IAAID,cAAc,GAAG,qDAAqDJ,kBAAkB,iCAAiC;QAC7HxK,SAAS,CAAC4K,cAAc,CAAC;;QAEzB;QACAR,WAAW,CAAC,CAAC;;QAEb;QACAnG,QAAQ,CAAC,CAAC;QACV;MACF;;MAEA;MACA,IAAIyB,YAAY,GAAG,kDAAkD;MAErE,IAAIpB,KAAK,CAACE,QAAQ,EAAE;QAAA,IAAAwG,qBAAA;QAClB;QACA,MAAM/C,MAAM,GAAG3D,KAAK,CAACE,QAAQ,CAACyD,MAAM;QACpC,MAAMtC,MAAM,GAAG,EAAAqF,qBAAA,GAAA1G,KAAK,CAACE,QAAQ,CAACgB,IAAI,cAAAwF,qBAAA,uBAAnBA,qBAAA,CAAqBrF,MAAM,KAAIrB,KAAK,CAACI,OAAO;QAE3D,IAAIuD,MAAM,KAAK,GAAG,EAAE;UAClB;UACA,IAAItC,MAAM,CAAC5B,QAAQ,CAAC,eAAe,CAAC,EAAE;YACpC2B,YAAY,GAAG,uGAAuG;UACxH,CAAC,MAAM,IAAIC,MAAM,CAAC5B,QAAQ,CAAC,YAAY,CAAC,EAAE;YACxC2B,YAAY,GAAG,4EAA4E;UAC7F,CAAC,MAAM;YACLA,YAAY,GAAGC,MAAM;UACvB;QACF,CAAC,MAAM,IAAIsC,MAAM,KAAK,GAAG,EAAE;UACzB;UACA,IAAI/E,QAAQ,KAAK,cAAc,IAAIyC,MAAM,CAAC5B,QAAQ,CAAC,aAAa,CAAC,EAAE;YACjE,IAAI6G,cAAc,GAAG,qDAAqDJ,kBAAkB,iCAAiC;YAC7HxK,SAAS,CAAC4K,cAAc,CAAC;;YAEzB;YACAR,WAAW,CAAC,CAAC;;YAEb;YACAnG,QAAQ,CAAC,CAAC;YACV;UACF,CAAC,MAAM;YACLyB,YAAY,GAAG,8BAA8BC,MAAM,EAAE;UACvD;QACF,CAAC,MAAM;UACLD,YAAY,GAAG,sBAAsBuC,MAAM,MAAMtC,MAAM,EAAE;QAC3D;MACF,CAAC,MAAM,IAAIrB,KAAK,CAACyG,OAAO,EAAE;QACxB;QACArF,YAAY,GAAG,+DAA+D;MAChF,CAAC,MAAM,IAAIpB,KAAK,CAACqB,MAAM,IAAIzC,QAAQ,KAAK,cAAc,EAAE;QACtD;QACAwC,YAAY,GAAGpB,KAAK,CAACqB,MAAM;QAC3B,IAAIrB,KAAK,CAAC2D,MAAM,KAAK,GAAG,IAAI3D,KAAK,CAACuG,OAAO,EAAE;UACzC,IAAID,cAAc,GAAG,qDAAqDJ,kBAAkB,iCAAiC;UAC7HxK,SAAS,CAAC4K,cAAc,CAAC;;UAEzB;UACAR,WAAW,CAAC,CAAC;;UAEb;UACAnG,QAAQ,CAAC,CAAC;UACV;QACF;MACF,CAAC,MAAM;QACL;QACAyB,YAAY,GAAGpB,KAAK,CAACI,OAAO,IAAIJ,KAAK,CAACqB,MAAM,IAAI,oBAAoB;MACtE;MAEA1F,OAAO,CAACyF,YAAY,CAAC;IACvB,CAAC,SAAS;MACRrF,UAAU,CAAC,KAAK,CAAC;IACnB;EACF,CAAC;;EAED;EACA,MAAM4K,WAAW,GAAGA,CAAA,KAAM;IACxB,oBACEtL,OAAA,CAAC7D,GAAG;MAAAoP,QAAA,gBACFvL,OAAA,CAAC3D,UAAU;QAACmP,OAAO,EAAC,WAAW;QAACC,EAAE,EAAE;UAAEC,EAAE,EAAE,CAAC;UAAEC,UAAU,EAAE;QAAO,CAAE;QAAAJ,QAAA,EAAC;MAEnE;QAAAK,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAY,CAAC,eAGb/L,OAAA,CAAC5D,KAAK;QAACqP,EAAE,EAAE;UAAEO,CAAC,EAAE,GAAG;UAAEN,EAAE,EAAE,CAAC;UAAEO,KAAK,EAAE;QAAO,CAAE;QAAAV,QAAA,eAC1CvL,OAAA,CAAC7D,GAAG;UAACsP,EAAE,EAAE;YAAES,OAAO,EAAE,MAAM;YAAEC,UAAU,EAAE,QAAQ;YAAEF,KAAK,EAAE;UAAO,CAAE;UAAAV,QAAA,gBAChEvL,OAAA,CAAC3D,UAAU;YAACmP,OAAO,EAAC,WAAW;YAACC,EAAE,EAAE;cAAEW,EAAE,EAAE,CAAC;cAAEC,QAAQ,EAAE;YAAO,CAAE;YAAAd,QAAA,EAAC;UAEjE;YAAAK,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAY,CAAC,eACb/L,OAAA,CAAC1D,SAAS;YACRgQ,IAAI,EAAC,OAAO;YACZC,KAAK,EAAC,SAAS;YACff,OAAO,EAAC,UAAU;YAClBvH,KAAK,EAAExC,WAAY;YACnB+K,QAAQ,EAAGxD,CAAC,IAAKtH,cAAc,CAACsH,CAAC,CAAChF,MAAM,CAACC,KAAK,CAAE;YAChDwI,WAAW,EAAC,8DAA8D;YAC1EhB,EAAE,EAAE;cAAEiB,QAAQ,EAAE,CAAC;cAAET,KAAK,EAAE,OAAO;cAAEG,EAAE,EAAE;YAAE;UAAE;YAAAR,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC5C,CAAC,eACF/L,OAAA,CAACzD,MAAM;YACLiP,OAAO,EAAC,WAAW;YACnBmB,KAAK,EAAC,SAAS;YACfC,OAAO,EAAElF,oBAAqB;YAC9BmF,QAAQ,EAAElM,WAAW,IAAI,CAACc,WAAW,CAACkF,IAAI,CAAC,CAAE;YAC7CmG,SAAS,EAAEnM,WAAW,gBAAGX,OAAA,CAACjD,gBAAgB;cAACuP,IAAI,EAAE;YAAG;cAAAV,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,gBAAG/L,OAAA,CAAC5B,UAAU;cAAC2O,QAAQ,EAAC;YAAO;cAAAnB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAE;YAC1FO,IAAI,EAAC,OAAO;YACZb,EAAE,EAAE;cAAEY,QAAQ,EAAE,MAAM;cAAEW,MAAM,EAAE,MAAM;cAAEZ,EAAE,EAAE;YAAE,CAAE;YAAAb,QAAA,EACjD;UAED;YAAAK,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC,EAGRxK,YAAY,iBACXvB,OAAA,CAAC7D,GAAG;YAACsP,EAAE,EAAE;cAAES,OAAO,EAAE,MAAM;cAAEC,UAAU,EAAE,QAAQ;cAAEO,QAAQ,EAAE,CAAC;cAAEO,QAAQ,EAAE,QAAQ;cAAEC,QAAQ,EAAE,QAAQ;cAAEC,EAAE,EAAE;YAAE,CAAE;YAAA5B,QAAA,gBAC7GvL,OAAA,CAAC7D,GAAG;cAACsP,EAAE,EAAE;gBAAES,OAAO,EAAE,MAAM;gBAAEC,UAAU,EAAE,QAAQ;gBAAEC,EAAE,EAAE;cAAE,CAAE;cAAAb,QAAA,gBACxDvL,OAAA,CAAC3D,UAAU;gBAACmP,OAAO,EAAC,WAAW;gBAACC,EAAE,EAAE;kBAAEE,UAAU,EAAE,MAAM;kBAAEyB,UAAU,EAAE,QAAQ;kBAAEhB,EAAE,EAAE,CAAC;kBAAEW,QAAQ,EAAE;gBAAU,CAAE;gBAAAxB,QAAA,GAAC,QACtG,eAAAvL,OAAA;kBAAMqN,KAAK,EAAE;oBAAEV,KAAK,EAAE;kBAAU,CAAE;kBAAApB,QAAA,EAAEhK,YAAY,CAACQ;gBAAO;kBAAA6J,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAO,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC5D,CAAC,eACb/L,OAAA,CAACnD,OAAO;gBAACyQ,WAAW,EAAC,UAAU;gBAACC,QAAQ;gBAAC9B,EAAE,EAAE;kBAAE+B,EAAE,EAAE;gBAAI;cAAE;gBAAA5B,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,eAC5D/L,OAAA,CAAC7D,GAAG;gBAACsP,EAAE,EAAE;kBAAES,OAAO,EAAE,MAAM;kBAAEC,UAAU,EAAE,QAAQ;kBAAEsB,GAAG,EAAE,CAAC;kBAAER,QAAQ,EAAE,QAAQ;kBAAEC,QAAQ,EAAE;gBAAS,CAAE;gBAAA3B,QAAA,gBACjGvL,OAAA,CAAC7D,GAAG;kBAACsP,EAAE,EAAE;oBAAES,OAAO,EAAE,MAAM;oBAAEC,UAAU,EAAE,QAAQ;oBAAEiB,UAAU,EAAE;kBAAS,CAAE;kBAAA7B,QAAA,gBACvEvL,OAAA,CAAC3D,UAAU;oBAACmP,OAAO,EAAC,OAAO;oBAACC,EAAE,EAAE;sBAAEE,UAAU,EAAE,QAAQ;sBAAEoB,QAAQ,EAAE,QAAQ;sBAAEX,EAAE,EAAE;oBAAI,CAAE;oBAAAb,QAAA,EAAC;kBAAK;oBAAAK,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAY,CAAC,eACzG/L,OAAA,CAAC3D,UAAU;oBAACmP,OAAO,EAAC,OAAO;oBAACC,EAAE,EAAE;sBAAEsB,QAAQ,EAAE;oBAAS,CAAE;oBAAAxB,QAAA,EAAEhK,YAAY,CAACgF,SAAS,IAAI;kBAAK;oBAAAqF,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAa,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACnG,CAAC,eACN/L,OAAA,CAAC7D,GAAG;kBAACsP,EAAE,EAAE;oBAAES,OAAO,EAAE,MAAM;oBAAEC,UAAU,EAAE,QAAQ;oBAAEiB,UAAU,EAAE;kBAAS,CAAE;kBAAA7B,QAAA,gBACvEvL,OAAA,CAAC3D,UAAU;oBAACmP,OAAO,EAAC,OAAO;oBAACC,EAAE,EAAE;sBAAEE,UAAU,EAAE,QAAQ;sBAAEoB,QAAQ,EAAE,QAAQ;sBAAEX,EAAE,EAAE;oBAAI,CAAE;oBAAAb,QAAA,EAAC;kBAAK;oBAAAK,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAY,CAAC,eACzG/L,OAAA,CAAC3D,UAAU;oBAACmP,OAAO,EAAC,OAAO;oBAACC,EAAE,EAAE;sBAAEsB,QAAQ,EAAE;oBAAS,CAAE;oBAAAxB,QAAA,EAAEhK,YAAY,CAACiF,OAAO,IAAI;kBAAK;oBAAAoF,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAa,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACjG,CAAC,eACN/L,OAAA,CAAC7D,GAAG;kBAACsP,EAAE,EAAE;oBAAES,OAAO,EAAE,MAAM;oBAAEC,UAAU,EAAE,QAAQ;oBAAEiB,UAAU,EAAE;kBAAS,CAAE;kBAAA7B,QAAA,gBACvEvL,OAAA,CAAC3D,UAAU;oBAACmP,OAAO,EAAC,OAAO;oBAACC,EAAE,EAAE;sBAAEE,UAAU,EAAE,QAAQ;sBAAEoB,QAAQ,EAAE,QAAQ;sBAAEX,EAAE,EAAE;oBAAI,CAAE;oBAAAb,QAAA,EAAC;kBAAM;oBAAAK,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAY,CAAC,eAC1G/L,OAAA,CAAC3D,UAAU;oBAACmP,OAAO,EAAC,OAAO;oBAACC,EAAE,EAAE;sBAAEsB,QAAQ,EAAE;oBAAS,CAAE;oBAAAxB,QAAA,GAAEhK,YAAY,CAACkI,aAAa,IAAI,KAAK,EAAC,IAAE;kBAAA;oBAAAmC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAY,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACzG,CAAC,eACN/L,OAAA,CAAC7D,GAAG;kBAACsP,EAAE,EAAE;oBAAES,OAAO,EAAE,MAAM;oBAAEC,UAAU,EAAE,QAAQ;oBAAEiB,UAAU,EAAE;kBAAS,CAAE;kBAAA7B,QAAA,gBACvEvL,OAAA,CAAC3D,UAAU;oBAACmP,OAAO,EAAC,OAAO;oBAACC,EAAE,EAAE;sBAAEE,UAAU,EAAE,QAAQ;sBAAEoB,QAAQ,EAAE,QAAQ;sBAAEX,EAAE,EAAE;oBAAI,CAAE;oBAAAb,QAAA,EAAC;kBAAM;oBAAAK,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAY,CAAC,eAC1G/L,OAAA,CAAC9C,IAAI;oBACHoP,IAAI,EAAC,OAAO;oBACZC,KAAK,EAAEhL,YAAY,CAAC2G,mBAAmB,IAAI,KAAM;oBACjDyE,KAAK,EAAEhN,kBAAkB,CAAC4B,YAAY,CAAC2G,mBAAmB,CAAE;oBAC5DuD,EAAE,EAAE;sBAAEuB,MAAM,EAAE,MAAM;sBAAE,kBAAkB,EAAE;wBAAEU,EAAE,EAAE,CAAC;wBAAEC,EAAE,EAAE,CAAC;wBAAEZ,QAAQ,EAAE;sBAAU;oBAAE;kBAAE;oBAAAnB,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACnF,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACC,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC,EAELlK,QAAQ,CAACI,SAAS,iBACjBjC,OAAA,CAAAE,SAAA;cAAAqL,QAAA,gBACEvL,OAAA,CAACnD,OAAO;gBAACyQ,WAAW,EAAC,UAAU;gBAACC,QAAQ;gBAAC9B,EAAE,EAAE;kBAAE+B,EAAE,EAAE;gBAAE;cAAE;gBAAA5B,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,eAC1D/L,OAAA,CAAC7D,GAAG;gBAACsP,EAAE,EAAE;kBAAES,OAAO,EAAE,MAAM;kBAAEC,UAAU,EAAE;gBAAS,CAAE;gBAAAZ,QAAA,gBACjDvL,OAAA,CAAC3D,UAAU;kBAACmP,OAAO,EAAC,WAAW;kBAACC,EAAE,EAAE;oBAAEE,UAAU,EAAE,MAAM;oBAAEyB,UAAU,EAAE,QAAQ;oBAAEhB,EAAE,EAAE,CAAC;oBAAEW,QAAQ,EAAE,SAAS;oBAAEJ,KAAK,EAAE;kBAAU,CAAE;kBAAApB,QAAA,GAAC,UACtH,eAAAvL,OAAA;oBAAAuL,QAAA,EAAO1J,QAAQ,CAACI,SAAS,KAAK,cAAc,GAAG,OAAO,GAAGkC,eAAe,CAACtC,QAAQ,CAACI,SAAS;kBAAC;oBAAA2J,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAO,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAClG,CAAC,EACZ,CAAC,MAAM;kBACN,IAAIlK,QAAQ,CAACI,SAAS,KAAK,cAAc,EAAE;oBACzC,oBACEjC,OAAA,CAAC7D,GAAG;sBAACsP,EAAE,EAAE;wBAAES,OAAO,EAAE,MAAM;wBAAEC,UAAU,EAAE,QAAQ;wBAAEiB,UAAU,EAAE;sBAAS,CAAE;sBAAA7B,QAAA,eACvEvL,OAAA,CAAC3D,UAAU;wBAACmP,OAAO,EAAC,OAAO;wBAACC,EAAE,EAAE;0BAAEsB,QAAQ,EAAE,QAAQ;0BAAEJ,KAAK,EAAE,gBAAgB;0BAAEiB,SAAS,EAAE;wBAAS,CAAE;wBAAArC,QAAA,EAAC;sBAEtG;wBAAAK,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAY;oBAAC;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACV,CAAC;kBAEV;kBAEA,MAAMlJ,MAAM,GAAGxB,MAAM,CAACuC,IAAI,CAACC,CAAC,IAAIA,CAAC,CAAC5B,SAAS,KAAKJ,QAAQ,CAACI,SAAS,CAAC;kBACnE,OAAOY,MAAM,gBACX7C,OAAA,CAAC7D,GAAG;oBAACsP,EAAE,EAAE;sBAAES,OAAO,EAAE,MAAM;sBAAEC,UAAU,EAAE,QAAQ;sBAAEsB,GAAG,EAAE,CAAC;sBAAER,QAAQ,EAAE,QAAQ;sBAAEC,QAAQ,EAAE;oBAAS,CAAE;oBAAA3B,QAAA,gBACjGvL,OAAA,CAAC7D,GAAG;sBAACsP,EAAE,EAAE;wBAAES,OAAO,EAAE,MAAM;wBAAEC,UAAU,EAAE,QAAQ;wBAAEiB,UAAU,EAAE;sBAAS,CAAE;sBAAA7B,QAAA,gBACvEvL,OAAA,CAAC3D,UAAU;wBAACmP,OAAO,EAAC,OAAO;wBAACC,EAAE,EAAE;0BAAEE,UAAU,EAAE,QAAQ;0BAAEoB,QAAQ,EAAE,QAAQ;0BAAEX,EAAE,EAAE;wBAAI,CAAE;wBAAAb,QAAA,EAAC;sBAAQ;wBAAAK,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAY,CAAC,eAC5G/L,OAAA,CAAC3D,UAAU;wBAACmP,OAAO,EAAC,OAAO;wBAACC,EAAE,EAAE;0BAAEsB,QAAQ,EAAE,QAAQ;0BAAEJ,KAAK,EAAE9J,MAAM,CAACyD,aAAa,GAAGiD,UAAU,CAAC1H,QAAQ,CAACG,YAAY,IAAI,CAAC,CAAC,GAAG,YAAY,GAAG,cAAc;0BAAE2J,UAAU,EAAE;wBAAO,CAAE;wBAAAJ,QAAA,GAC9K1I,MAAM,CAACyD,aAAa,IAAI,CAAC,EAAC,IAC7B;sBAAA;wBAAAsF,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAY,CAAC;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACV,CAAC,eACN/L,OAAA,CAAC7D,GAAG;sBAACsP,EAAE,EAAE;wBAAES,OAAO,EAAE,MAAM;wBAAEC,UAAU,EAAE,QAAQ;wBAAEiB,UAAU,EAAE;sBAAS,CAAE;sBAAA7B,QAAA,gBACvEvL,OAAA,CAAC3D,UAAU;wBAACmP,OAAO,EAAC,OAAO;wBAACC,EAAE,EAAE;0BAAEE,UAAU,EAAE,QAAQ;0BAAEoB,QAAQ,EAAE,QAAQ;0BAAEX,EAAE,EAAE;wBAAI,CAAE;wBAAAb,QAAA,EAAC;sBAAM;wBAAAK,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAY,CAAC,eAC1G/L,OAAA,CAAC9C,IAAI;wBACHoP,IAAI,EAAC,OAAO;wBACZC,KAAK,EAAE1J,MAAM,CAACwD,YAAY,IAAI,KAAM;wBACpCsG,KAAK,EAAE/M,iBAAiB,CAACiD,MAAM,CAACwD,YAAY,CAAE;wBAC9CoF,EAAE,EAAE;0BAAEuB,MAAM,EAAE,MAAM;0BAAE,kBAAkB,EAAE;4BAAEU,EAAE,EAAE,CAAC;4BAAEC,EAAE,EAAE,CAAC;4BAAEZ,QAAQ,EAAE;0BAAU;wBAAE;sBAAE;wBAAAnB,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACnF,CAAC;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACC,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACH,CAAC,GACJ,IAAI;gBACV,CAAC,EAAE,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACD,CAAC;YAAA,eACN,CACH;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAGE,CACN;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACE;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACD,CAAC,eAGR/L,OAAA,CAAC5D,KAAK;QAACqP,EAAE,EAAE;UAAEO,CAAC,EAAE,GAAG;UAAEC,KAAK,EAAE;QAAO,CAAE;QAAAV,QAAA,gBACnCvL,OAAA,CAAC3D,UAAU;UAACmP,OAAO,EAAC,WAAW;UAACC,EAAE,EAAE;YAAEC,EAAE,EAAE;UAAE,CAAE;UAAAH,QAAA,EAAC;QAE/C;UAAAK,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAY,CAAC,EAEZpL,WAAW,gBACVX,OAAA,CAAC7D,GAAG;UAACsP,EAAE,EAAE;YAAES,OAAO,EAAE,MAAM;YAAE2B,cAAc,EAAE,QAAQ;YAAEC,EAAE,EAAE;UAAE,CAAE;UAAAvC,QAAA,eAC5DvL,OAAA,CAACjD,gBAAgB;YAACuP,IAAI,EAAE;UAAG;YAAAV,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC3B,CAAC,GACJ5K,IAAI,CAACsD,MAAM,KAAK,CAAC,gBACnBzE,OAAA,CAAClD,KAAK;UAACiR,QAAQ,EAAC,MAAM;UAACtC,EAAE,EAAE;YAAEkC,EAAE,EAAE;UAAI,CAAE;UAAApC,QAAA,eACrCvL,OAAA,CAAC3D,UAAU;YAACmP,OAAO,EAAC,SAAS;YAAAD,QAAA,EAAC;UAA2C;YAAAK,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAY;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACjF,CAAC,gBAER/L,OAAA,CAACrC,cAAc;UAACqQ,SAAS,EAAE5R,KAAM;UAACoP,OAAO,EAAC,UAAU;UAACC,EAAE,EAAE;YAAEwC,SAAS,EAAE,OAAO;YAAEf,QAAQ,EAAE,MAAM;YAAEjB,KAAK,EAAE;UAAO,CAAE;UAAAV,QAAA,eAC/GvL,OAAA,CAACxC,KAAK;YAAC8O,IAAI,EAAC,OAAO;YAAC4B,YAAY;YAAA3C,QAAA,gBAC9BvL,OAAA,CAACpC,SAAS;cAAA2N,QAAA,eACRvL,OAAA,CAACnC,QAAQ;gBAAC4N,EAAE,EAAE;kBAAE,MAAM,EAAE;oBAAEE,UAAU,EAAE,MAAM;oBAAEgC,EAAE,EAAE,CAAC;oBAAEQ,OAAO,EAAE;kBAAU;gBAAE,CAAE;gBAAA5C,QAAA,gBAC1EvL,OAAA,CAACtC,SAAS;kBAAA6N,QAAA,EAAC;gBAAO;kBAAAK,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAW,CAAC,eAC9B/L,OAAA,CAACtC,SAAS;kBAAA6N,QAAA,EAAC;gBAAS;kBAAAK,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAW,CAAC,eAChC/L,OAAA,CAACtC,SAAS;kBAAA6N,QAAA,EAAC;gBAAU;kBAAAK,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAW,CAAC,eACjC/L,OAAA,CAACtC,SAAS;kBAAA6N,QAAA,EAAC;gBAAK;kBAAAK,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAW,CAAC,eAC5B/L,OAAA,CAACtC,SAAS;kBAAA6N,QAAA,EAAC;gBAAK;kBAAAK,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAW,CAAC,eAC5B/L,OAAA,CAACtC,SAAS;kBAAC0Q,KAAK,EAAC,QAAQ;kBAAC3C,EAAE,EAAE;oBAAEQ,KAAK,EAAE;kBAAO,CAAE;kBAAAV,QAAA,EAAC;gBAAI;kBAAAK,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAW,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACzD;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACF,CAAC,eACZ/L,OAAA,CAACvC,SAAS;cAAA8N,QAAA,EACPpK,IAAI,CAACyG,GAAG,CAAEhF,IAAI,iBACb5C,OAAA,CAACnC,QAAQ;gBAEPwQ,KAAK;gBACLzB,OAAO,EAAEA,CAAA,KAAMvE,gBAAgB,CAACzF,IAAI,CAAE;gBACtC6I,EAAE,EAAE;kBACF6C,MAAM,EAAE,SAAS;kBACjB,SAAS,EAAE;oBAAEH,OAAO,EAAE;kBAAU,CAAC;kBACjC,MAAM,EAAE;oBAAER,EAAE,EAAE;kBAAI;gBACpB,CAAE;gBAAApC,QAAA,gBAEFvL,OAAA,CAACtC,SAAS;kBAAC+N,EAAE,EAAE;oBAAEE,UAAU,EAAE;kBAAS,CAAE;kBAAAJ,QAAA,EAAE3I,IAAI,CAACb;gBAAO;kBAAA6J,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAY,CAAC,eACnE/L,OAAA,CAACtC,SAAS;kBAAA6N,QAAA,EAAE3I,IAAI,CAAC2D,SAAS,IAAI;gBAAK;kBAAAqF,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAY,CAAC,eAChD/L,OAAA,CAACtC,SAAS;kBAAA6N,QAAA,EAAE3I,IAAI,CAAC4D,OAAO,IAAI;gBAAK;kBAAAoF,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAY,CAAC,eAC9C/L,OAAA,CAACtC,SAAS;kBAAA6N,QAAA,EAAE3I,IAAI,CAAC6G,aAAa,IAAI;gBAAK;kBAAAmC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAY,CAAC,eACpD/L,OAAA,CAACtC,SAAS;kBAAA6N,QAAA,eACRvL,OAAA,CAAC9C,IAAI;oBACHoP,IAAI,EAAC,OAAO;oBACZC,KAAK,EAAE9M,YAAY,CAACmD,IAAI,CAAC,GAAG,OAAO,GAAGlD,gBAAgB,CAACkD,IAAI,CAAC,GAAG,YAAY,GAAGA,IAAI,CAACsF,mBAAoB;oBACvGyE,KAAK,EAAElN,YAAY,CAACmD,IAAI,CAAC,GAAG,OAAO,GAAGlD,gBAAgB,CAACkD,IAAI,CAAC,GAAG,SAAS,GAAGjD,kBAAkB,CAACiD,IAAI,CAACsF,mBAAmB,CAAE;oBACxHuD,EAAE,EAAE;sBAAEuB,MAAM,EAAE,MAAM;sBAAE,kBAAkB,EAAE;wBAAEU,EAAE,EAAE,CAAC;wBAAEC,EAAE,EAAE,CAAC;wBAAEZ,QAAQ,EAAE;sBAAS;oBAAE;kBAAE;oBAAAnB,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAClF;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACO,CAAC,eACZ/L,OAAA,CAACtC,SAAS;kBAAC0Q,KAAK,EAAC,QAAQ;kBAAA7C,QAAA,eACvBvL,OAAA,CAAC/C,UAAU;oBACTqP,IAAI,EAAC,OAAO;oBACZM,OAAO,EAAG5D,CAAC,IAAK;sBACdA,CAAC,CAACuF,eAAe,CAAC,CAAC;sBACnB/M,eAAe,CAACoB,IAAI,CAAC;sBACrBS,wBAAwB,CAAC,IAAI,CAAC;oBAChC,CAAE;oBAAAkI,QAAA,eAEFvL,OAAA,CAACpB,QAAQ;sBAACmO,QAAQ,EAAC;oBAAO;sBAAAnB,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAE;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACnB;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACJ,CAAC;cAAA,GAhCPnJ,IAAI,CAACb,OAAO;gBAAA6J,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAiCT,CACX;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACO,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACP;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACM,CACjB;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACI,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACL,CAAC;EAEV,CAAC;;EAED;EACA,MAAMyC,WAAW,GAAGA,CAAA,KAAM;IACxB,IAAI,CAACjN,YAAY,EAAE,OAAO,IAAI;IAE9B,oBACEvB,OAAA,CAAC7D,GAAG;MAAAoP,QAAA,gBACFvL,OAAA,CAAC3D,UAAU;QAACmP,OAAO,EAAC,WAAW;QAACC,EAAE,EAAE;UAAEC,EAAE,EAAE,CAAC;UAAEC,UAAU,EAAE;QAAO,CAAE;QAAAJ,QAAA,EAAC;MAEnE;QAAAK,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAY,CAAC,eAEb/L,OAAA,CAAC5D,KAAK;QAACqP,EAAE,EAAE;UAAEO,CAAC,EAAE,CAAC;UAAEC,KAAK,EAAE;QAAO,CAAE;QAAAV,QAAA,gBACjCvL,OAAA,CAAC3D,UAAU;UAACmP,OAAO,EAAC,WAAW;UAACiD,YAAY;UAAChD,EAAE,EAAE;YAAEE,UAAU,EAAE;UAAO,CAAE;UAAAJ,QAAA,EAAC;QAEzE;UAAAK,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAY,CAAC,eAIb/L,OAAA,CAAC7D,GAAG;UAACsP,EAAE,EAAE;YAAEiD,EAAE,EAAE,CAAC;YAAEhD,EAAE,EAAE,CAAC;YAAEO,KAAK,EAAE;UAAO,CAAE;UAAAV,QAAA,gBACvCvL,OAAA,CAAC3D,UAAU;YAACmP,OAAO,EAAC,WAAW;YAACiD,YAAY;YAAChD,EAAE,EAAE;cAAEE,UAAU,EAAE;YAAO,CAAE;YAAAJ,QAAA,EAAC;UAEzE;YAAAK,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAY,CAAC,eACb/L,OAAA,CAAC1D,SAAS;YACRgQ,IAAI,EAAC,OAAO;YACZC,KAAK,EAAC,cAAc;YACpBf,OAAO,EAAC,UAAU;YAClBvC,IAAI,EAAC,cAAc;YACnB0F,IAAI,EAAC,QAAQ;YACb1K,KAAK,EAAEpC,QAAQ,CAACG,YAAa;YAC7BwK,QAAQ,EAAEzD,gBAAiB;YAC3BpE,KAAK,EAAE,CAAC,CAACzC,UAAU,CAACF,YAAa;YACjC4M,UAAU,EAAE1M,UAAU,CAACF,YAAY,IAAII,YAAY,CAACJ,YAAa;YACjE6M,mBAAmB,EAAE;cACnBpD,EAAE,EAAE;gBAAEkB,KAAK,EAAEvK,YAAY,CAACJ,YAAY,IAAI,CAACE,UAAU,CAACF,YAAY,GAAG,cAAc,GAAG;cAAa;YACrG,CAAE;YACFyJ,EAAE,EAAE;cAAEC,EAAE,EAAE,CAAC;cAAEO,KAAK,EAAE;YAAQ,CAAE;YAC9B6C,UAAU,EAAE;cACVC,GAAG,EAAE,MAAM;cAAE;cACbC,IAAI,EAAE;YACR;UAAE;YAAApD,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACC,CAAC,EAEL3J,YAAY,CAACJ,YAAY,IAAI,CAACE,UAAU,CAACF,YAAY,IAAI,CAAC0H,iBAAiB,iBAC1E1J,OAAA,CAAClD,KAAK;UAACiR,QAAQ,EAAC,SAAS;UAACtC,EAAE,EAAE;YAAEC,EAAE,EAAE;UAAE,CAAE;UAAAH,QAAA,EACrCnJ,YAAY,CAACJ;QAAY;UAAA4J,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACrB,CACR,eAED/L,OAAA,CAAClD,KAAK;UAACiR,QAAQ,EAAC,MAAM;UAACtC,EAAE,EAAE;YAAEiD,EAAE,EAAE;UAAE,CAAE;UAAAnD,QAAA,eACnCvL,OAAA,CAAC3D,UAAU;YAACmP,OAAO,EAAC,OAAO;YAAAD,QAAA,EAAC;UAE5B;YAAAK,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAY;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACR,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACL,CAAC;EAEV,CAAC;;EAED;EACA,MAAMkD,WAAW,GAAGA,CAAA,KAAM;IAExB;IACA,MAAMC,iBAAiB,GAAIC,YAAY,IAAK;MAC1C,OAAO,IAAI/O,UAAU,KAAK+O,YAAY,EAAE;IAC1C,CAAC;;IAED;IACA,MAAMC,mBAAmB,GAAIvM,MAAM,IAAK;MACtC,IAAI,CAACA,MAAM,IAAI,CAAChB,QAAQ,CAACG,YAAY,EAAE,OAAO,IAAI;MAClD,OAAOuH,UAAU,CAAC1G,MAAM,CAACyD,aAAa,CAAC,IAAIiD,UAAU,CAAC1H,QAAQ,CAACG,YAAY,CAAC;IAC9E,CAAC;;IAED;IACA,MAAMqN,cAAc,GAAGhO,MAAM,CAAC+E,MAAM,CAACvD,MAAM,IAAI;MAC7C;MACA,IAAI,CAACG,UAAU,EAAE,OAAO,IAAI;;MAE5B;MACA,MAAM2E,WAAW,GAAG3E,UAAU,CAACqB,KAAK,CAAC,GAAG,CAAC,CAACuD,GAAG,CAACC,IAAI,IAAIA,IAAI,CAAClB,IAAI,CAAC,CAAC,CAACC,WAAW,CAAC,CAAC,CAAC,CAACR,MAAM,CAACyB,IAAI,IAAIA,IAAI,CAACpD,MAAM,GAAG,CAAC,CAAC;;MAEhH;MACA,IAAIkD,WAAW,CAAClD,MAAM,KAAK,CAAC,IAAIkD,WAAW,CAAC,CAAC,CAAC,KAAKA,WAAW,CAAC,CAAC,CAAC,EAAE;QACjE,MAAM2H,UAAU,GACdnL,eAAe,CAACtB,MAAM,CAACZ,SAAS,CAAC,CAAC2E,WAAW,CAAC,CAAC,KAAKe,WAAW,CAAC,CAAC,CAAC,IAClEjB,MAAM,CAAC7D,MAAM,CAAC0D,SAAS,IAAI,EAAE,CAAC,CAACK,WAAW,CAAC,CAAC,KAAKe,WAAW,CAAC,CAAC,CAAC,IAC/DjB,MAAM,CAAC7D,MAAM,CAAC2D,OAAO,IAAI,EAAE,CAAC,CAACI,WAAW,CAAC,CAAC,KAAKe,WAAW,CAAC,CAAC,CAAC;QAE/D,OAAO2H,UAAU;MACnB;;MAEA;MACA,OAAO3H,WAAW,CAACK,IAAI,CAACH,IAAI,IAAI;QAC9B;QACA,MAAM0H,OAAO,GAAG7I,MAAM,CAACmB,IAAI,CAAC;;QAE5B;QACA,MAAM2H,aAAa,GAAG,CAAClG,KAAK,CAACiG,OAAO,CAAC,IAAI,CAACjG,KAAK,CAACC,UAAU,CAACgG,OAAO,CAAC,CAAC;;QAEpE;QACA,IAAIC,aAAa,EAAE;UACjB,MAAMC,WAAW,GAAGlG,UAAU,CAACgG,OAAO,CAAC;UACvC,MAAMG,gBAAgB,GAAGnG,UAAU,CAAC7C,MAAM,CAAC7D,MAAM,CAAC2D,OAAO,IAAI,GAAG,CAAC,CAAC;;UAElE;UACA,IAAIkJ,gBAAgB,KAAKD,WAAW,EAAE;YACpC,OAAO,IAAI;UACb;;UAEA;UACA,IAAItL,eAAe,CAACtB,MAAM,CAACZ,SAAS,CAAC,CAACmC,QAAQ,CAACmL,OAAO,CAAC,EAAE;YACvD,OAAO,IAAI;UACb;QACF;;QAEA;QACA,OAAOpL,eAAe,CAACtB,MAAM,CAACZ,SAAS,CAAC,CAAC2E,WAAW,CAAC,CAAC,CAACxC,QAAQ,CAACyD,IAAI,CAAC,IAC9DnB,MAAM,CAAC7D,MAAM,CAAC0D,SAAS,IAAI,EAAE,CAAC,CAACK,WAAW,CAAC,CAAC,CAACxC,QAAQ,CAACyD,IAAI,CAAC,IAC3DnB,MAAM,CAAC7D,MAAM,CAAC2D,OAAO,IAAI,EAAE,CAAC,CAACI,WAAW,CAAC,CAAC,CAACxC,QAAQ,CAACyD,IAAI,CAAC;MAClE,CAAC,CAAC;IACJ,CAAC,CAAC;;IAEF;IACA,MAAMf,iBAAiB,GAAGvF,YAAY,GAClC8N,cAAc,CAACjJ,MAAM,CAACvD,MAAM,IAC1B6D,MAAM,CAAC7D,MAAM,CAAC0D,SAAS,IAAI,EAAE,CAAC,CAACI,IAAI,CAAC,CAAC,KAAKD,MAAM,CAACnF,YAAY,CAACgF,SAAS,IAAI,EAAE,CAAC,CAACI,IAAI,CAAC,CAAC,IACrFD,MAAM,CAAC7D,MAAM,CAAC2D,OAAO,IAAI,GAAG,CAAC,CAACG,IAAI,CAAC,CAAC,KAAKD,MAAM,CAACnF,YAAY,CAACiF,OAAO,IAAI,GAAG,CAAC,CAACG,IAAI,CAAC,CAAC,CAAC,GACtF0I,cAAc;IAElB,MAAMtI,oBAAoB,GAAGxF,YAAY,GACrC8N,cAAc,CAACjJ,MAAM,CAACvD,MAAM,IAC1B6D,MAAM,CAAC7D,MAAM,CAAC0D,SAAS,IAAI,EAAE,CAAC,CAACI,IAAI,CAAC,CAAC,KAAKD,MAAM,CAACnF,YAAY,CAACgF,SAAS,IAAI,EAAE,CAAC,CAACI,IAAI,CAAC,CAAC,IACrFD,MAAM,CAAC7D,MAAM,CAAC2D,OAAO,IAAI,GAAG,CAAC,CAACG,IAAI,CAAC,CAAC,KAAKD,MAAM,CAACnF,YAAY,CAACiF,OAAO,IAAI,GAAG,CAAC,CAACG,IAAI,CAAC,CAAC,CAAC,GACtF,EAAE;IAIN,oBACE3G,OAAA,CAAC7D,GAAG;MAAAoP,QAAA,gBACFvL,OAAA,CAAC7D,GAAG;QAACsP,EAAE,EAAE;UAAEC,EAAE,EAAE;QAAE,CAAE;QAAAH,QAAA,gBACjBvL,OAAA,CAAC3D,UAAU;UAACmP,OAAO,EAAC,WAAW;UAACC,EAAE,EAAE;YAAEE,UAAU,EAAE,MAAM;YAAEO,OAAO,EAAE,QAAQ;YAAEa,QAAQ,EAAE;UAAS,CAAE;UAAAxB,QAAA,EAAC;QAEnG;UAAAK,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAY,CAAC,eACb/L,OAAA,CAAC3D,UAAU;UAACmP,OAAO,EAAC,OAAO;UAACC,EAAE,EAAE;YAAES,OAAO,EAAE,QAAQ;YAAEiB,EAAE,EAAE,CAAC;YAAER,KAAK,EAAE;UAAiB,CAAE;UAAApB,QAAA,EAAC;QAEvF;UAAAK,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAY,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACV,CAAC,eAEN/L,OAAA,CAAC5D,KAAK;QAACqP,EAAE,EAAE;UAAEO,CAAC,EAAE,CAAC;UAAEC,KAAK,EAAE;QAAO,CAAE;QAAAV,QAAA,gBAEjCvL,OAAA,CAAC7D,GAAG;UAACsP,EAAE,EAAE;YAAEC,EAAE,EAAE;UAAE,CAAE;UAAAH,QAAA,eACjBvL,OAAA,CAAC7D,GAAG;YAACsP,EAAE,EAAE;cAAES,OAAO,EAAE,MAAM;cAAEC,UAAU,EAAE,QAAQ;cAAEsB,GAAG,EAAE;YAAE,CAAE;YAAAlC,QAAA,gBACzDvL,OAAA,CAAC1D,SAAS;cACRgQ,IAAI,EAAC,OAAO;cACZC,KAAK,EAAC,cAAc;cACpBf,OAAO,EAAC,UAAU;cAClBvC,IAAI,EAAC,cAAc;cACnB0F,IAAI,EAAC,QAAQ;cACb1K,KAAK,EAAEpC,QAAQ,CAACG,YAAa;cAC7BwK,QAAQ,EAAEzD,gBAAiB;cAC3BpE,KAAK,EAAE,CAAC,CAACzC,UAAU,CAACF,YAAa;cACjC4M,UAAU,EAAE1M,UAAU,CAACF,YAAa;cACpCyJ,EAAE,EAAE;gBAAEQ,KAAK,EAAE;cAAQ,CAAE;cACvB6C,UAAU,EAAE;gBACVC,GAAG,EAAE,MAAM;gBAAE;gBACbC,IAAI,EAAE;cACR;YAAE;cAAApD,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC,EAED3J,YAAY,CAACJ,YAAY,IAAI,CAACE,UAAU,CAACF,YAAY,iBACpDhC,OAAA,CAAC3D,UAAU;cACTmP,OAAO,EAAC,OAAO;cACfC,EAAE,EAAE;gBACFkB,KAAK,EAAE,cAAc;gBACrBhB,UAAU,EAAE,QAAQ;gBACpBoB,QAAQ,EAAE;cACZ,CAAE;cAAAxB,QAAA,EAEDnJ,YAAY,CAACJ;YAAY;cAAA4J,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAChB,CACb;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACE;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,eAIN/L,OAAA,CAACnD,OAAO;UAAC4O,EAAE,EAAE;YAAEqC,EAAE,EAAE;UAAE;QAAE;UAAAlC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,eAE1B/L,OAAA,CAAC7D,GAAG;UAACsP,EAAE,EAAE;YAAEC,EAAE,EAAE;UAAE,CAAE;UAAAH,QAAA,eAEjBvL,OAAA,CAAC7D,GAAG;YAACsP,EAAE,EAAE;cAAES,OAAO,EAAE,MAAM;cAAEC,UAAU,EAAE,QAAQ;cAAEsB,GAAG,EAAE,CAAC;cAAE/B,EAAE,EAAE;YAAE,CAAE;YAAAH,QAAA,gBAEhEvL,OAAA,CAAC1D,SAAS;cACRgQ,IAAI,EAAC,OAAO;cACZC,KAAK,EAAC,OAAO;cACbf,OAAO,EAAC,UAAU;cAClBvH,KAAK,EAAEjB,UAAW;cAClBwJ,QAAQ,EAAE1I,sBAAuB;cACjC2I,WAAW,EAAC,4DAA4D;cACxEkD,UAAU,EAAE;gBACVC,cAAc,eACZ5P,OAAA,CAAC7C,cAAc;kBAAC0S,QAAQ,EAAC,OAAO;kBAAAtE,QAAA,eAC9BvL,OAAA,CAAC5B,UAAU;oBAAC2O,QAAQ,EAAC;kBAAO;oBAAAnB,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACjB,CACjB;gBACD+D,YAAY,EAAE9M,UAAU,gBACtBhD,OAAA,CAAC7C,cAAc;kBAAC0S,QAAQ,EAAC,KAAK;kBAAAtE,QAAA,eAC5BvL,OAAA,CAAC/C,UAAU;oBACTqP,IAAI,EAAC,OAAO;oBACZ,cAAW,cAAc;oBACzBM,OAAO,EAAEA,CAAA,KAAM3J,aAAa,CAAC,EAAE,CAAE;oBACjC8M,IAAI,EAAC,KAAK;oBAAAxE,QAAA,eAEVvL,OAAA,CAACxB,UAAU;sBAACuO,QAAQ,EAAC;oBAAO;sBAAAnB,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAE;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACrB;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACC,CAAC,GACf;cACN,CAAE;cACFN,EAAE,EAAE;gBAAEQ,KAAK,EAAE;cAAQ;YAAE;cAAAL,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACxB,CAAC,eAEF/L,OAAA,CAACzD,MAAM;cACLiP,OAAO,EAAC,UAAU;cAClBc,IAAI,EAAC,OAAO;cACZM,OAAO,EAAEA,CAAA,KAAM;gBACbpJ,OAAO,CAACC,GAAG,CAAC,0BAA0B,CAAC;gBACvCH,kBAAkB,CAAC,cAAc,CAAC;cACpC,CAAE;cACFmI,EAAE,EAAE;gBAAEuB,MAAM,EAAE,MAAM;gBAAErB,UAAU,EAAE9J,QAAQ,CAACI,SAAS,KAAK,cAAc,GAAG,MAAM,GAAG,QAAQ;gBACrFkM,OAAO,EAAEtM,QAAQ,CAACI,SAAS,KAAK,cAAc,GAAG,yBAAyB,GAAG,SAAS;gBACtF+N,MAAM,EAAEnO,QAAQ,CAACI,SAAS,KAAK,cAAc,GAAG,mBAAmB,GAAGgO;cAAU,CAAE;cAAA1E,QAAA,EACzF;YAED;cAAAK,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACN;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,EAELlL,aAAa,gBACZb,OAAA,CAAC7D,GAAG;UAACsP,EAAE,EAAE;YAAES,OAAO,EAAE,MAAM;YAAE2B,cAAc,EAAE,QAAQ;YAAEC,EAAE,EAAE;UAAE,CAAE;UAAAvC,QAAA,eAC5DvL,OAAA,CAACjD,gBAAgB;YAACuP,IAAI,EAAE;UAAG;YAAAV,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC3B,CAAC,gBAEN/L,OAAA,CAAC7D,GAAG;UAAAoP,QAAA,eAIFvL,OAAA,CAACxD,IAAI;YAAC0T,SAAS;YAACC,OAAO,EAAE,CAAE;YAAA5E,QAAA,gBAEzBvL,OAAA,CAACxD,IAAI;cAAC4T,IAAI;cAACC,EAAE,EAAE,EAAG;cAACC,EAAE,EAAE,CAAE;cAAA/E,QAAA,eACvBvL,OAAA,CAAC5D,KAAK;gBAACoP,OAAO,EAAC,UAAU;gBAACC,EAAE,EAAE;kBAAEO,CAAC,EAAE,CAAC;kBAAEgB,MAAM,EAAE;gBAAO,CAAE;gBAAAzB,QAAA,gBACrDvL,OAAA,CAAC3D,UAAU;kBAACmP,OAAO,EAAC,WAAW;kBAACC,EAAE,EAAE;oBAAEE,UAAU,EAAE,MAAM;oBAAED,EAAE,EAAE;kBAAE,CAAE;kBAAAH,QAAA,EAAC;gBAEnE;kBAAAK,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAY,CAAC,EAEZjF,iBAAiB,CAACrC,MAAM,GAAG,CAAC,gBAC3BzE,OAAA,CAAAE,SAAA;kBAAAqL,QAAA,gBACEvL,OAAA,CAAC7D,GAAG;oBAACsP,EAAE,EAAE;sBAAES,OAAO,EAAE,MAAM;sBAAEC,UAAU,EAAE,QAAQ;sBAAEF,KAAK,EAAE,MAAM;sBAAE0B,EAAE,EAAE,GAAG;sBAAED,EAAE,EAAE,GAAG;sBAAES,OAAO,EAAE,SAAS;sBAAEoC,YAAY,EAAE,CAAC;sBAAE7E,EAAE,EAAE;oBAAE,CAAE;oBAAAH,QAAA,gBAC9HvL,OAAA,CAAC7D,GAAG;sBAACsP,EAAE,EAAE;wBAAEQ,KAAK,EAAE,MAAM;wBAAEG,EAAE,EAAE;sBAAE,CAAE;sBAAAb,QAAA,eAChCvL,OAAA,CAAC3D,UAAU;wBAACmP,OAAO,EAAC,SAAS;wBAACC,EAAE,EAAE;0BAAEE,UAAU,EAAE,MAAM;0BAAEoB,QAAQ,EAAE;wBAAU,CAAE;wBAAAxB,QAAA,EAAC;sBAAE;wBAAAK,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAY;oBAAC;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAC3F,CAAC,eACN/L,OAAA,CAAC7D,GAAG;sBAACsP,EAAE,EAAE;wBAAEQ,KAAK,EAAE,OAAO;wBAAEG,EAAE,EAAE;sBAAE,CAAE;sBAAAb,QAAA,eACjCvL,OAAA,CAAC3D,UAAU;wBAACmP,OAAO,EAAC,SAAS;wBAACC,EAAE,EAAE;0BAAEE,UAAU,EAAE,MAAM;0BAAEoB,QAAQ,EAAE;wBAAU,CAAE;wBAAAxB,QAAA,EAAC;sBAAI;wBAAAK,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAY;oBAAC;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAC7F,CAAC,eACN/L,OAAA,CAAC7D,GAAG;sBAACsP,EAAE,EAAE;wBAAEQ,KAAK,EAAE,OAAO;wBAAEG,EAAE,EAAE;sBAAE,CAAE;sBAAAb,QAAA,eACjCvL,OAAA,CAAC3D,UAAU;wBAACmP,OAAO,EAAC,SAAS;wBAACC,EAAE,EAAE;0BAAEE,UAAU,EAAE,MAAM;0BAAEoB,QAAQ,EAAE;wBAAU,CAAE;wBAAAxB,QAAA,EAAC;sBAAK;wBAAAK,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAY;oBAAC;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAC9F,CAAC,eACN/L,OAAA,CAAC7D,GAAG;sBAACsP,EAAE,EAAE;wBAAEQ,KAAK,EAAE,OAAO;wBAAEG,EAAE,EAAE;sBAAE,CAAE;sBAAAb,QAAA,eACjCvL,OAAA,CAAC3D,UAAU;wBAACmP,OAAO,EAAC,SAAS;wBAACC,EAAE,EAAE;0BAAEE,UAAU,EAAE,MAAM;0BAAEoB,QAAQ,EAAE;wBAAU,CAAE;wBAAAxB,QAAA,EAAC;sBAAO;wBAAAK,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAY;oBAAC;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAChG,CAAC,eACN/L,OAAA,CAAC7D,GAAG;sBAACsP,EAAE,EAAE;wBAAEiB,QAAQ,EAAE;sBAAE,CAAE;sBAAAnB,QAAA,eACvBvL,OAAA,CAAC3D,UAAU;wBAACmP,OAAO,EAAC,SAAS;wBAACC,EAAE,EAAE;0BAAEE,UAAU,EAAE,MAAM;0BAAEoB,QAAQ,EAAE;wBAAU,CAAE;wBAAAxB,QAAA,EAAC;sBAAK;wBAAAK,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAY;oBAAC;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAC9F,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACH,CAAC,eACR/L,OAAA,CAAClC,IAAI;oBAAC2N,EAAE,EAAE;sBAAEwC,SAAS,EAAEnH,iBAAiB,CAACrC,MAAM,GAAG,CAAC,GAAG,OAAO,GAAG,MAAM;sBAAE+L,SAAS,EAAE1J,iBAAiB,CAACrC,MAAM,GAAG,CAAC,GAAG,MAAM,GAAG,SAAS;sBAAEgM,SAAS,EAAE,QAAQ;sBAAEtC,OAAO,EAAE;oBAAmB,CAAE;oBAAA5C,QAAA,EACtLzE,iBAAiB,CAACc,GAAG,CAAE/E,MAAM,iBAC5B7C,OAAA,CAACjC,QAAQ;sBAEP2S,cAAc;sBACdC,eAAe,eACb3Q,OAAA,CAAC/C,UAAU;wBACT8S,IAAI,EAAC,KAAK;wBACVzD,IAAI,EAAC,OAAO;wBACZM,OAAO,EAAEA,CAAA,KAAMtJ,kBAAkB,CAACT,MAAM,CAACZ,SAAS,CAAE;wBAAAsJ,QAAA,eAEpDvL,OAAA,CAAClB,oBAAoB;0BAAC6N,KAAK,EAAC;wBAAS;0BAAAf,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAE;sBAAC;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAC9B,CACb;sBACDN,EAAE,EAAE;wBACF0C,OAAO,EAAEtM,QAAQ,CAACI,SAAS,KAAKY,MAAM,CAACZ,SAAS,GAAG,yBAAyB,GAAG,SAAS;wBACxFsO,YAAY,EAAE,KAAK;wBACnB7E,EAAE,EAAE,GAAG;wBACPsE,MAAM,EAAEnO,QAAQ,CAACI,SAAS,KAAKY,MAAM,CAACZ,SAAS,GAAG,mBAAmB,GAAG;sBAC1E,CAAE;sBAAAsJ,QAAA,eAEFvL,OAAA,CAAChC,cAAc;wBACb4S,KAAK;wBACLhE,OAAO,EAAEA,CAAA,KAAMtJ,kBAAkB,CAACT,MAAM,CAACZ,SAAS,CAAE;wBAAAsJ,QAAA,eAEpDvL,OAAA,CAAC7D,GAAG;0BAACsP,EAAE,EAAE;4BAAES,OAAO,EAAE,MAAM;4BAAEC,UAAU,EAAE,QAAQ;4BAAEF,KAAK,EAAE,MAAM;4BAAE0B,EAAE,EAAE;0BAAI,CAAE;0BAAApC,QAAA,gBACzEvL,OAAA,CAAC7D,GAAG;4BAACsP,EAAE,EAAE;8BAAEQ,KAAK,EAAE,MAAM;8BAAEG,EAAE,EAAE;4BAAE,CAAE;4BAAAb,QAAA,eAChCvL,OAAA,CAAC3D,UAAU;8BAACmP,OAAO,EAAC,OAAO;8BAACC,EAAE,EAAE;gCAAEE,UAAU,EAAE,MAAM;gCAAEoB,QAAQ,EAAE;8BAAS,CAAE;8BAAAxB,QAAA,EACxEpH,eAAe,CAACtB,MAAM,CAACZ,SAAS;4BAAC;8BAAA2J,QAAA,EAAAC,YAAA;8BAAAC,UAAA;8BAAAC,YAAA;4BAAA,OACxB;0BAAC;4BAAAH,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OACV,CAAC,eACN/L,OAAA,CAAC7D,GAAG;4BAACsP,EAAE,EAAE;8BAAEQ,KAAK,EAAE,OAAO;8BAAEG,EAAE,EAAE;4BAAE,CAAE;4BAAAb,QAAA,eACjCvL,OAAA,CAAC3D,UAAU;8BAACmP,OAAO,EAAC,OAAO;8BAACC,EAAE,EAAE;gCAAEsB,QAAQ,EAAE;8BAAU,CAAE;8BAAAxB,QAAA,EACrD1I,MAAM,CAAC0D,SAAS,IAAI;4BAAK;8BAAAqF,QAAA,EAAAC,YAAA;8BAAAC,UAAA;8BAAAC,YAAA;4BAAA,OAChB;0BAAC;4BAAAH,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OACV,CAAC,eACN/L,OAAA,CAAC7D,GAAG;4BAACsP,EAAE,EAAE;8BAAEQ,KAAK,EAAE,OAAO;8BAAEG,EAAE,EAAE;4BAAE,CAAE;4BAAAb,QAAA,eACjCvL,OAAA,CAAC3D,UAAU;8BAACmP,OAAO,EAAC,OAAO;8BAACC,EAAE,EAAE;gCAAEsB,QAAQ,EAAE;8BAAU,CAAE;8BAAAxB,QAAA,EACrD1I,MAAM,CAAC2D,OAAO,IAAI;4BAAK;8BAAAoF,QAAA,EAAAC,YAAA;8BAAAC,UAAA;8BAAAC,YAAA;4BAAA,OACd;0BAAC;4BAAAH,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OACV,CAAC,eACN/L,OAAA,CAAC7D,GAAG;4BAACsP,EAAE,EAAE;8BAAEQ,KAAK,EAAE,OAAO;8BAAEG,EAAE,EAAE;4BAAE,CAAE;4BAAAb,QAAA,eACjCvL,OAAA,CAAC3D,UAAU;8BAACmP,OAAO,EAAC,OAAO;8BAACC,EAAE,EAAE;gCAAEE,UAAU,EAAE,MAAM;gCAAEoB,QAAQ,EAAE,SAAS;gCAAEJ,KAAK,EAAE9J,MAAM,CAACyD,aAAa,GAAGiD,UAAU,CAAC1H,QAAQ,CAACG,YAAY,IAAI,CAAC,CAAC,GAAG,YAAY,GAAG;8BAAe,CAAE;8BAAAuJ,QAAA,GAC/K1I,MAAM,CAACyD,aAAa,IAAI,CAAC,EAAC,IAC7B;4BAAA;8BAAAsF,QAAA,EAAAC,YAAA;8BAAAC,UAAA;8BAAAC,YAAA;4BAAA,OAAY;0BAAC;4BAAAH,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OACV,CAAC,eACN/L,OAAA,CAAC7D,GAAG;4BAACsP,EAAE,EAAE;8BAAEiB,QAAQ,EAAE;4BAAE,CAAE;4BAAAnB,QAAA,eACvBvL,OAAA,CAAC9C,IAAI;8BACHoP,IAAI,EAAC,OAAO;8BACZC,KAAK,EAAE1J,MAAM,CAACwD,YAAY,IAAI,KAAM;8BACpCsG,KAAK,EAAE/M,iBAAiB,CAACiD,MAAM,CAACwD,YAAY,CAAE;8BAC9CmF,OAAO,EAAC,UAAU;8BAClBC,EAAE,EAAE;gCAAEuB,MAAM,EAAE,EAAE;gCAAED,QAAQ,EAAE,QAAQ;gCAAE,kBAAkB,EAAE;kCAAEW,EAAE,EAAE,CAAC;kCAAEC,EAAE,EAAE;gCAAE;8BAAE;4BAAE;8BAAA/B,QAAA,EAAAC,YAAA;8BAAAC,UAAA;8BAAAC,YAAA;4BAAA,OAC9E;0BAAC;4BAAAH,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OACC,CAAC;wBAAA;0BAAAH,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OACH;sBAAC;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACQ;oBAAC,GArDZlJ,MAAM,CAACZ,SAAS;sBAAA2J,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAsDb,CACX;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACE,CAAC;gBAAA,eACL,CAAC,gBAEH/L,OAAA,CAAClD,KAAK;kBAACiR,QAAQ,EAAC,MAAM;kBAACtC,EAAE,EAAE;oBAAEiD,EAAE,EAAE;kBAAE,CAAE;kBAAAnD,QAAA,EAAC;gBAEtC;kBAAAK,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAO,CACR;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACI;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACJ,CAAC,eAGP/L,OAAA,CAACxD,IAAI;cAAC4T,IAAI;cAACC,EAAE,EAAE,EAAG;cAACC,EAAE,EAAE,CAAE;cAAA/E,QAAA,eACvBvL,OAAA,CAAC5D,KAAK;gBAACoP,OAAO,EAAC,UAAU;gBAACC,EAAE,EAAE;kBAAEO,CAAC,EAAE,CAAC;kBAAEgB,MAAM,EAAE;gBAAO,CAAE;gBAAAzB,QAAA,gBACrDvL,OAAA,CAAC3D,UAAU;kBAACmP,OAAO,EAAC,WAAW;kBAACC,EAAE,EAAE;oBAAEE,UAAU,EAAE,MAAM;oBAAED,EAAE,EAAE;kBAAE,CAAE;kBAAAH,QAAA,EAAC;gBAEnE;kBAAAK,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAY,CAAC,EAEZhF,oBAAoB,CAACtC,MAAM,GAAG,CAAC,gBAC9BzE,OAAA,CAAAE,SAAA;kBAAAqL,QAAA,gBACEvL,OAAA,CAAC7D,GAAG;oBAACsP,EAAE,EAAE;sBAAES,OAAO,EAAE,MAAM;sBAAEC,UAAU,EAAE,QAAQ;sBAAEF,KAAK,EAAE,MAAM;sBAAE0B,EAAE,EAAE,GAAG;sBAAED,EAAE,EAAE,GAAG;sBAAES,OAAO,EAAE,SAAS;sBAAEoC,YAAY,EAAE,CAAC;sBAAE7E,EAAE,EAAE;oBAAE,CAAE;oBAAAH,QAAA,gBAC9HvL,OAAA,CAAC7D,GAAG;sBAACsP,EAAE,EAAE;wBAAEQ,KAAK,EAAE,MAAM;wBAAEG,EAAE,EAAE;sBAAE,CAAE;sBAAAb,QAAA,eAChCvL,OAAA,CAAC3D,UAAU;wBAACmP,OAAO,EAAC,SAAS;wBAACC,EAAE,EAAE;0BAAEE,UAAU,EAAE,MAAM;0BAAEoB,QAAQ,EAAE;wBAAU,CAAE;wBAAAxB,QAAA,EAAC;sBAAE;wBAAAK,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAY;oBAAC;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAC3F,CAAC,eACN/L,OAAA,CAAC7D,GAAG;sBAACsP,EAAE,EAAE;wBAAEQ,KAAK,EAAE,OAAO;wBAAEG,EAAE,EAAE;sBAAE,CAAE;sBAAAb,QAAA,eACjCvL,OAAA,CAAC3D,UAAU;wBAACmP,OAAO,EAAC,SAAS;wBAACC,EAAE,EAAE;0BAAEE,UAAU,EAAE,MAAM;0BAAEoB,QAAQ,EAAE;wBAAU,CAAE;wBAAAxB,QAAA,EAAC;sBAAI;wBAAAK,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAY;oBAAC;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAC7F,CAAC,eACN/L,OAAA,CAAC7D,GAAG;sBAACsP,EAAE,EAAE;wBAAEQ,KAAK,EAAE,OAAO;wBAAEG,EAAE,EAAE;sBAAE,CAAE;sBAAAb,QAAA,eACjCvL,OAAA,CAAC3D,UAAU;wBAACmP,OAAO,EAAC,SAAS;wBAACC,EAAE,EAAE;0BAAEE,UAAU,EAAE,MAAM;0BAAEoB,QAAQ,EAAE;wBAAU,CAAE;wBAAAxB,QAAA,EAAC;sBAAK;wBAAAK,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAY;oBAAC;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAC9F,CAAC,eACN/L,OAAA,CAAC7D,GAAG;sBAACsP,EAAE,EAAE;wBAAEQ,KAAK,EAAE,OAAO;wBAAEG,EAAE,EAAE;sBAAE,CAAE;sBAAAb,QAAA,eACjCvL,OAAA,CAAC3D,UAAU;wBAACmP,OAAO,EAAC,SAAS;wBAACC,EAAE,EAAE;0BAAEE,UAAU,EAAE,MAAM;0BAAEoB,QAAQ,EAAE;wBAAU,CAAE;wBAAAxB,QAAA,EAAC;sBAAO;wBAAAK,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAY;oBAAC;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAChG,CAAC,eACN/L,OAAA,CAAC7D,GAAG;sBAACsP,EAAE,EAAE;wBAAEiB,QAAQ,EAAE;sBAAE,CAAE;sBAAAnB,QAAA,eACvBvL,OAAA,CAAC3D,UAAU;wBAACmP,OAAO,EAAC,SAAS;wBAACC,EAAE,EAAE;0BAAEE,UAAU,EAAE,MAAM;0BAAEoB,QAAQ,EAAE;wBAAU,CAAE;wBAAAxB,QAAA,EAAC;sBAAK;wBAAAK,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAY;oBAAC;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAC9F,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACH,CAAC,eACR/L,OAAA,CAAClC,IAAI;oBAAC2N,EAAE,EAAE;sBAAEwC,SAAS,EAAElH,oBAAoB,CAACtC,MAAM,GAAG,CAAC,GAAG,OAAO,GAAG,MAAM;sBAAE+L,SAAS,EAAEzJ,oBAAoB,CAACtC,MAAM,GAAG,CAAC,GAAG,MAAM,GAAG,SAAS;sBAAEgM,SAAS,EAAE,QAAQ;sBAAEtC,OAAO,EAAE;oBAAmB,CAAE;oBAAA5C,QAAA,EAC5LxE,oBAAoB,CAACa,GAAG,CAAE/E,MAAM,iBAC/B7C,OAAA,CAACjC,QAAQ;sBAEP2S,cAAc;sBACdC,eAAe,eACb3Q,OAAA,CAAC/C,UAAU;wBACT8S,IAAI,EAAC,KAAK;wBACVzD,IAAI,EAAC,OAAO;wBACZM,OAAO,EAAEA,CAAA,KAAMtJ,kBAAkB,CAACT,MAAM,CAACZ,SAAS,CAAE;wBAAAsJ,QAAA,eAEpDvL,OAAA,CAAClB,oBAAoB;0BAAC6N,KAAK,EAAC;wBAAS;0BAAAf,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAE;sBAAC;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAC9B,CACb;sBACDN,EAAE,EAAE;wBACF0C,OAAO,EAAEtM,QAAQ,CAACI,SAAS,KAAKY,MAAM,CAACZ,SAAS,GAAG,yBAAyB,GAAG,SAAS;wBACxFsO,YAAY,EAAE,KAAK;wBACnB7E,EAAE,EAAE,GAAG;wBACPsE,MAAM,EAAEnO,QAAQ,CAACI,SAAS,KAAKY,MAAM,CAACZ,SAAS,GAAG,mBAAmB,GAAG;sBAC1E,CAAE;sBAAAsJ,QAAA,eAEFvL,OAAA,CAAChC,cAAc;wBACb4S,KAAK;wBACLhE,OAAO,EAAEA,CAAA,KAAMtJ,kBAAkB,CAACT,MAAM,CAACZ,SAAS,CAAE;wBAAAsJ,QAAA,eAEpDvL,OAAA,CAAC7D,GAAG;0BAACsP,EAAE,EAAE;4BAAES,OAAO,EAAE,MAAM;4BAAEC,UAAU,EAAE,QAAQ;4BAAEF,KAAK,EAAE,MAAM;4BAAE0B,EAAE,EAAE;0BAAI,CAAE;0BAAApC,QAAA,gBACzEvL,OAAA,CAAC7D,GAAG;4BAACsP,EAAE,EAAE;8BAAEQ,KAAK,EAAE,MAAM;8BAAEG,EAAE,EAAE;4BAAE,CAAE;4BAAAb,QAAA,eAChCvL,OAAA,CAAC3D,UAAU;8BAACmP,OAAO,EAAC,OAAO;8BAACC,EAAE,EAAE;gCAAEE,UAAU,EAAE,MAAM;gCAAEoB,QAAQ,EAAE;8BAAS,CAAE;8BAAAxB,QAAA,EACxEpH,eAAe,CAACtB,MAAM,CAACZ,SAAS;4BAAC;8BAAA2J,QAAA,EAAAC,YAAA;8BAAAC,UAAA;8BAAAC,YAAA;4BAAA,OACxB;0BAAC;4BAAAH,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OACV,CAAC,eACN/L,OAAA,CAAC7D,GAAG;4BAACsP,EAAE,EAAE;8BAAEQ,KAAK,EAAE,OAAO;8BAAEG,EAAE,EAAE;4BAAE,CAAE;4BAAAb,QAAA,eACjCvL,OAAA,CAAC3D,UAAU;8BAACmP,OAAO,EAAC,OAAO;8BAACC,EAAE,EAAE;gCAAEsB,QAAQ,EAAE;8BAAU,CAAE;8BAAAxB,QAAA,EACrD1I,MAAM,CAAC0D,SAAS,IAAI;4BAAK;8BAAAqF,QAAA,EAAAC,YAAA;8BAAAC,UAAA;8BAAAC,YAAA;4BAAA,OAChB;0BAAC;4BAAAH,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OACV,CAAC,eACN/L,OAAA,CAAC7D,GAAG;4BAACsP,EAAE,EAAE;8BAAEQ,KAAK,EAAE,OAAO;8BAAEG,EAAE,EAAE;4BAAE,CAAE;4BAAAb,QAAA,eACjCvL,OAAA,CAAC3D,UAAU;8BAACmP,OAAO,EAAC,OAAO;8BAACC,EAAE,EAAE;gCAAEsB,QAAQ,EAAE;8BAAU,CAAE;8BAAAxB,QAAA,EACrD1I,MAAM,CAAC2D,OAAO,IAAI;4BAAK;8BAAAoF,QAAA,EAAAC,YAAA;8BAAAC,UAAA;8BAAAC,YAAA;4BAAA,OACd;0BAAC;4BAAAH,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OACV,CAAC,eACN/L,OAAA,CAAC7D,GAAG;4BAACsP,EAAE,EAAE;8BAAEQ,KAAK,EAAE,OAAO;8BAAEG,EAAE,EAAE;4BAAE,CAAE;4BAAAb,QAAA,eACjCvL,OAAA,CAAC3D,UAAU;8BAACmP,OAAO,EAAC,OAAO;8BAACC,EAAE,EAAE;gCAAEE,UAAU,EAAE,MAAM;gCAAEoB,QAAQ,EAAE,SAAS;gCAAEJ,KAAK,EAAE9J,MAAM,CAACyD,aAAa,GAAGiD,UAAU,CAAC1H,QAAQ,CAACG,YAAY,IAAI,CAAC,CAAC,GAAG,YAAY,GAAG;8BAAe,CAAE;8BAAAuJ,QAAA,GAC/K1I,MAAM,CAACyD,aAAa,IAAI,CAAC,EAAC,IAC7B;4BAAA;8BAAAsF,QAAA,EAAAC,YAAA;8BAAAC,UAAA;8BAAAC,YAAA;4BAAA,OAAY;0BAAC;4BAAAH,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OACV,CAAC,eACN/L,OAAA,CAAC7D,GAAG;4BAACsP,EAAE,EAAE;8BAAES,OAAO,EAAE,MAAM;8BAAEuB,GAAG,EAAE;4BAAE,CAAE;4BAAAlC,QAAA,gBACnCvL,OAAA,CAAC9C,IAAI;8BACHoP,IAAI,EAAC,OAAO;8BACZC,KAAK,EAAE1J,MAAM,CAACwD,YAAY,IAAI,KAAM;8BACpCsG,KAAK,EAAE/M,iBAAiB,CAACiD,MAAM,CAACwD,YAAY,CAAE;8BAC9CmF,OAAO,EAAC,UAAU;8BAClBC,EAAE,EAAE;gCAAEuB,MAAM,EAAE,EAAE;gCAAED,QAAQ,EAAE,QAAQ;gCAAE,kBAAkB,EAAE;kCAAEW,EAAE,EAAE,CAAC;kCAAEC,EAAE,EAAE;gCAAE;8BAAE;4BAAE;8BAAA/B,QAAA,EAAAC,YAAA;8BAAAC,UAAA;8BAAAC,YAAA;4BAAA,OAC9E,CAAC,eACF/L,OAAA,CAAC9C,IAAI;8BACHoP,IAAI,EAAC,OAAO;8BACZC,KAAK,EAAC,WAAW;8BACjBI,KAAK,EAAC,SAAS;8BACfnB,OAAO,EAAC,UAAU;8BAClBC,EAAE,EAAE;gCAAEuB,MAAM,EAAE,EAAE;gCAAED,QAAQ,EAAE,QAAQ;gCAAE,kBAAkB,EAAE;kCAAEW,EAAE,EAAE,CAAC;kCAAEC,EAAE,EAAE;gCAAE;8BAAE;4BAAE;8BAAA/B,QAAA,EAAAC,YAAA;8BAAAC,UAAA;8BAAAC,YAAA;4BAAA,OAC9E,CAAC;0BAAA;4BAAAH,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OACC,CAAC;wBAAA;0BAAAH,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OACH;sBAAC;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACQ;oBAAC,GA5DZlJ,MAAM,CAACZ,SAAS;sBAAA2J,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OA6Db,CACX;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACE,CAAC;gBAAA,eACL,CAAC,gBAEH/L,OAAA,CAAClD,KAAK;kBAACiR,QAAQ,EAAC,MAAM;kBAACtC,EAAE,EAAE;oBAAEiD,EAAE,EAAE;kBAAE,CAAE;kBAAAnD,QAAA,EAAC;gBAEtC;kBAAAK,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAO,CACR;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACI;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACJ,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACJ,CACN,EAIA1K,MAAM,CAACoD,MAAM,KAAK,CAAC,IAAI,CAAC5D,aAAa,iBACpCb,OAAA,CAAClD,KAAK;UAACiR,QAAQ,EAAC,SAAS;UAACtC,EAAE,EAAE;YAAEiD,EAAE,EAAE;UAAE,CAAE;UAAAnD,QAAA,EAAC;QAEzC;UAAAK,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAO,CACR,eAGD/L,OAAA,CAAC7D,GAAG;UAACsP,EAAE,EAAE;YAAES,OAAO,EAAE,MAAM;YAAE2B,cAAc,EAAE,UAAU;YAAEa,EAAE,EAAE,CAAC;YAAEjB,GAAG,EAAE;UAAE,CAAE;UAAAlC,QAAA,gBACtEvL,OAAA,CAACzD,MAAM;YACLiP,OAAO,EAAC,UAAU;YAClBmB,KAAK,EAAC,WAAW;YACjBC,OAAO,EAAEnC,WAAY;YACrBoC,QAAQ,EAAEpM,OAAQ;YAAA8K,QAAA,EACnB;UAED;YAAAK,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC,eACT/L,OAAA,CAACzD,MAAM;YACLiP,OAAO,EAAC,WAAW;YACnBmB,KAAK,EAAC,SAAS;YACfC,OAAO,EAAEhC,YAAa;YACtBiC,QAAQ,EAAEpM,OAAO,IAAI,CAACc,YAAY,IAAI,CAACM,QAAQ,CAACG,YAAY,IAAI,CAACH,QAAQ,CAACI,SAAU;YACpF6K,SAAS,EAAErM,OAAO,gBAAGT,OAAA,CAACjD,gBAAgB;cAACuP,IAAI,EAAE;YAAG;cAAAV,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,GAAG,IAAK;YAAAR,QAAA,EAC5D;UAED;YAAAK,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACN,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACD,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACL,CAAC;EAEV,CAAC;;EAED;EACA,MAAM8E,WAAW,GAAGA,CAAA,KAAM;IAExB;IACA,IAAI1B,YAAY,GAAG,SAAS;IAC5B,IAAI2B,UAAU,GAAG,IAAI;IAErB,IAAIjP,QAAQ,CAACI,SAAS,KAAK,cAAc,EAAE;MACzCkN,YAAY,GAAG,cAAc;IAC/B,CAAC,MAAM,IAAItN,QAAQ,CAACI,SAAS,EAAE;MAC7BkN,YAAY,GAAGhL,eAAe,CAACtC,QAAQ,CAACI,SAAS,CAAC;MAClD;MACA6O,UAAU,GAAGzP,MAAM,CAACuC,IAAI,CAACC,CAAC,IAAIA,CAAC,CAAC5B,SAAS,KAAKJ,QAAQ,CAACI,SAAS,CAAC;IACnE;;IAEA;IACA,MAAM4I,kBAAkB,GAAGH,2BAA2B,CAACnB,UAAU,CAAC1H,QAAQ,CAACG,YAAY,CAAC,EAAET,YAAY,CAACkI,aAAa,CAAC;IAErH,oBACEzJ,OAAA,CAAC7D,GAAG;MAAAoP,QAAA,gBACFvL,OAAA,CAAC3D,UAAU;QAACmP,OAAO,EAAC,IAAI;QAACiD,YAAY;QAAAlD,QAAA,EAAC;MAEtC;QAAAK,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAY,CAAC,eAEb/L,OAAA,CAAC5D,KAAK;QAACqP,EAAE,EAAE;UAAEO,CAAC,EAAE;QAAE,CAAE;QAAAT,QAAA,gBAClBvL,OAAA,CAAC3D,UAAU;UAACmP,OAAO,EAAC,WAAW;UAACiD,YAAY;UAAAlD,QAAA,EAAC;QAE7C;UAAAK,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAY,CAAC,eAGb/L,OAAA,CAACb,eAAe;UACdyD,IAAI,EAAErB,YAAa;UACnBwP,OAAO,EAAE,IAAK;UACd/G,KAAK,EAAC;QAAmB;UAAA4B,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC1B,CAAC,eAGF/L,OAAA,CAAC7D,GAAG;UAACsP,EAAE,EAAE;YAAEiD,EAAE,EAAE,CAAC;YAAE1C,CAAC,EAAE,CAAC;YAAEmC,OAAO,EAAE,SAAS;YAAEoC,YAAY,EAAE;UAAE,CAAE;UAAAhF,QAAA,gBAC5DvL,OAAA,CAAC3D,UAAU;YAACmP,OAAO,EAAC,WAAW;YAACiD,YAAY;YAAChD,EAAE,EAAE;cAAEE,UAAU,EAAE;YAAO,CAAE;YAAAJ,QAAA,EAAC;UAEzE;YAAAK,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAY,CAAC,eACb/L,OAAA,CAACxD,IAAI;YAAC0T,SAAS;YAACC,OAAO,EAAE,CAAE;YAAA5E,QAAA,gBACzBvL,OAAA,CAACxD,IAAI;cAAC4T,IAAI;cAACC,EAAE,EAAE,EAAG;cAACC,EAAE,EAAE,CAAE;cAAA/E,QAAA,gBACvBvL,OAAA,CAAC3D,UAAU;gBAACmP,OAAO,EAAC,OAAO;gBAAAD,QAAA,gBACzBvL,OAAA;kBAAAuL,QAAA,EAAQ;gBAAa;kBAAAK,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,KAAC,EAAClK,QAAQ,CAACG,YAAY,EAAC,IACxD;cAAA;gBAAA4J,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAY,CAAC,eACb/L,OAAA,CAAC3D,UAAU;gBAACmP,OAAO,EAAC,OAAO;gBAAAD,QAAA,gBACzBvL,OAAA;kBAAAuL,QAAA,EAAQ;gBAAoB;kBAAAK,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,KAAC,EAAClB,kBAAkB;cAAA;gBAAAe,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC/C,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACT,CAAC,eACP/L,OAAA,CAACxD,IAAI;cAAC4T,IAAI;cAACC,EAAE,EAAE,EAAG;cAACC,EAAE,EAAE,CAAE;cAAA/E,QAAA,gBACvBvL,OAAA,CAAC3D,UAAU;gBAACmP,OAAO,EAAC,OAAO;gBAAAD,QAAA,gBACzBvL,OAAA;kBAAAuL,QAAA,EAAQ;gBAAiB;kBAAAK,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,KAAC,EAACoD,YAAY;cAAA;gBAAAvD,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACtC,CAAC,EACZ+E,UAAU,iBACT9Q,OAAA,CAAC3D,UAAU;gBAACmP,OAAO,EAAC,OAAO;gBAAAD,QAAA,gBACzBvL,OAAA;kBAAAuL,QAAA,EAAQ;gBAAqB;kBAAAK,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,KAAC,EAAC+E,UAAU,CAACxK,aAAa,EAAC,IACnE;cAAA;gBAAAsF,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAY,CACb;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACG,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACJ,CAAC,EAEL+E,UAAU,IAAIvH,UAAU,CAAC1H,QAAQ,CAACG,YAAY,CAAC,GAAGuH,UAAU,CAACuH,UAAU,CAACxK,aAAa,CAAC,IAAI,CAACoD,iBAAiB,iBAC3G1J,OAAA,CAAClD,KAAK;UAACiR,QAAQ,EAAC,SAAS;UAACtC,EAAE,EAAE;YAAEiD,EAAE,EAAE;UAAE,CAAE;UAAAnD,QAAA,gBACtCvL,OAAA;YAAAuL,QAAA,EAAQ;UAAW;YAAAK,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC,qBAAiB,EAAClK,QAAQ,CAACG,YAAY,EAAC,4CAA0C,EAAC8O,UAAU,CAACxK,aAAa,EAAC,gDAE1I;QAAA;UAAAsF,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAO,CACR,eAED/L,OAAA,CAAClD,KAAK;UAACiR,QAAQ,EAAC,MAAM;UAACtC,EAAE,EAAE;YAAEiD,EAAE,EAAE;UAAE,CAAE;UAAAnD,QAAA,GAAC,8EAEpC,EAAC1J,QAAQ,CAACI,SAAS,IAAIJ,QAAQ,CAACI,SAAS,KAAK,cAAc,IAAI,gFAAgF;QAAA;UAAA2J,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC3I,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACL,CAAC;EAEV,CAAC;;EAED;EACA,MAAMiF,cAAc,GAAIhC,IAAI,IAAK;IAC/B,QAAQA,IAAI;MACV,KAAK,CAAC;QACJ,OAAO1D,WAAW,CAAC,CAAC;MAAE;MACxB,KAAK,CAAC;QACJ,OAAO2D,WAAW,CAAC,CAAC;MAAE;MACxB,KAAK,CAAC;QACJ,OAAOT,WAAW,CAAC,CAAC;MAAE;MACxB,KAAK,CAAC;QACJ,OAAOqC,WAAW,CAAC,CAAC;MAAE;MACxB;QACE,OAAO,mBAAmB;IAC9B;EACF,CAAC;;EAED;EACA,MAAMI,4BAA4B,GAAGA,CAAA,KAAM;IACzClO,wBAAwB,CAAC,KAAK,CAAC;IAC/BI,kBAAkB,CAAC,IAAI,CAAC;EAC1B,CAAC;;EAID;EACA,MAAM+N,gBAAgB,GAAGA,CAAA,KAAM;IAC7B,IAAIhO,eAAe,EAAE;MACnB1C,QAAQ,CAAC,uBAAuBJ,UAAU,8BAA8B8C,eAAe,CAACnB,OAAO,EAAE,CAAC;IACpG;IACAkP,4BAA4B,CAAC,CAAC;EAChC,CAAC;;EAED;EACA,MAAME,wBAAwB,GAAGA,CAAA,KAAM;IACrCF,4BAA4B,CAAC,CAAC;IAC9B;IACAzP,eAAe,CAAC,IAAI,CAAC;IACrBE,cAAc,CAAC,EAAE,CAAC;IAClBR,oBAAoB,CAAC,KAAK,CAAC;EAC7B,CAAC;;EAED;EACA,MAAMkQ,iCAAiC,GAAGA,CAAA,KAAM;IAC9C7O,6BAA6B,CAAC,KAAK,CAAC;IACpCI,uBAAuB,CAAC;MAAEC,IAAI,EAAE,IAAI;MAAEC,MAAM,EAAE;IAAK,CAAC,CAAC;EACvD,CAAC;;EAED;EACA,MAAMwO,2BAA2B,GAAG,MAAAA,CAAA,KAAY;IAC9C,MAAM;MAAEzO,IAAI;MAAEC;IAAO,CAAC,GAAGH,oBAAoB;IAC7C,IAAI,CAACE,IAAI,IAAI,CAACC,MAAM,EAAE;MACpBW,OAAO,CAACmB,KAAK,CAAC,uCAAuC,EAAE;QAAE/B,IAAI;QAAEC;MAAO,CAAC,CAAC;MACxEvC,OAAO,CAAC,sCAAsC,CAAC;MAC/C;IACF;IAEA,IAAI;MACFI,UAAU,CAAC,IAAI,CAAC;MAChB8C,OAAO,CAACC,GAAG,CAAC,0CAA0Cb,IAAI,CAACb,OAAO,iCAAiCc,MAAM,CAACZ,SAAS,EAAE,CAAC;MACtHuB,OAAO,CAACC,GAAG,CAAC,sCAAsC,EAAEb,IAAI,CAAC;MACzDY,OAAO,CAACC,GAAG,CAAC,cAAc,EAAEZ,MAAM,CAAC;;MAEnC;MACA,MAAM7D,WAAW,CAACsS,0BAA0B,CAAClR,UAAU,EAAEwC,IAAI,CAACb,OAAO,EAAEc,MAAM,CAACZ,SAAS,CAAC;;MAExF;MACA,MAAM0G,WAAW,GAAG,MAAM3J,WAAW,CAACuS,WAAW,CAACnR,UAAU,EAAEwC,IAAI,CAACb,OAAO,CAAC;MAC3EyB,OAAO,CAACC,GAAG,CAAC,kCAAkC,EAAEkF,WAAW,CAAC;MAC5DnH,eAAe,CAACmH,WAAW,CAAC;;MAE5B;MACA7G,WAAW,CAAC;QACV,GAAGD,QAAQ;QACXI,SAAS,EAAEY,MAAM,CAACZ;MACpB,CAAC,CAAC;;MAEF;MACA,MAAMiC,UAAU,CAAC,CAAC;MAElB7D,SAAS,CAAC,4BAA4BuC,IAAI,CAACb,OAAO,6CAA6Cc,MAAM,CAACZ,SAAS,EAAE,CAAC;MAClHmP,iCAAiC,CAAC,CAAC;IACrC,CAAC,CAAC,OAAOzM,KAAK,EAAE;MACdnB,OAAO,CAACmB,KAAK,CAAC,iEAAiE,EAAEA,KAAK,CAAC;MACvFrE,OAAO,CAAC,kEAAkE,IAAIqE,KAAK,CAACqB,MAAM,IAAIrB,KAAK,CAACI,OAAO,IAAI,oBAAoB,CAAC,CAAC;IACvI,CAAC,SAAS;MACRrE,UAAU,CAAC,KAAK,CAAC;IACnB;EACF,CAAC;;EAED;EACA,MAAM8Q,8BAA8B,GAAG,MAAAA,CAAA,KAAY;IACjD,MAAM;MAAE5O,IAAI;MAAEC;IAAO,CAAC,GAAGH,oBAAoB;IAC7C,IAAI,CAACE,IAAI,IAAI,CAACC,MAAM,EAAE;MACpBW,OAAO,CAACmB,KAAK,CAAC,uDAAuD,EAAE;QAAE/B,IAAI;QAAEC;MAAO,CAAC,CAAC;MACxFvC,OAAO,CAAC,sDAAsD,CAAC;MAC/D;IACF;IAEA,IAAI;MACFI,UAAU,CAAC,IAAI,CAAC;MAChB8C,OAAO,CAACC,GAAG,CAAC,iCAAiCZ,MAAM,CAACZ,SAAS,aAAaW,IAAI,CAACb,OAAO,sCAAsC,CAAC;;MAE7H;MACAD,WAAW,CAAC;QACV,GAAGD,QAAQ;QACXI,SAAS,EAAEY,MAAM,CAACZ;MACpB,CAAC,CAAC;MAEF5B,SAAS,CAAC,wBAAwBwC,MAAM,CAACZ,SAAS,4BAA4BW,IAAI,CAACb,OAAO,EAAE,CAAC;MAC7FqP,iCAAiC,CAAC,CAAC;IACrC,CAAC,CAAC,OAAOzM,KAAK,EAAE;MACdnB,OAAO,CAACmB,KAAK,CAAC,yDAAyD,EAAEA,KAAK,CAAC;MAC/ErE,OAAO,CAAC,0DAA0D,IAAIqE,KAAK,CAACqB,MAAM,IAAIrB,KAAK,CAACI,OAAO,IAAI,oBAAoB,CAAC,CAAC;IAC/H,CAAC,SAAS;MACRrE,UAAU,CAAC,KAAK,CAAC;IACnB;EACF,CAAC;;EAED;EACA,MAAM+Q,uBAAuB,GAAGA,CAAA,KAAM;IACpCL,iCAAiC,CAAC,CAAC;IACnC;IACAtP,WAAW,CAAC;MACV,GAAGD,QAAQ;MACXI,SAAS,EAAE;IACb,CAAC,CAAC;EACJ,CAAC;EAED,oBACEjC,OAAA,CAAC7D,GAAG;IAAAoP,QAAA,gBAEFvL,OAAA,CAAC7D,GAAG;MAACsP,EAAE,EAAE;QAAEC,EAAE,EAAE;MAAE,CAAE;MAAAH,QAAA,gBACjBvL,OAAA,CAAC5D,KAAK;QAACqP,EAAE,EAAE;UAAEO,CAAC,EAAE,GAAG;UAAEN,EAAE,EAAE,CAAC;UAAEO,KAAK,EAAE;QAAO,CAAE;QAAAV,QAAA,eAC1CvL,OAAA,CAAC7D,GAAG;UAACsP,EAAE,EAAE;YAAES,OAAO,EAAE,MAAM;YAAEC,UAAU,EAAE,QAAQ;YAAEF,KAAK,EAAE;UAAO,CAAE;UAAAV,QAAA,gBAChEvL,OAAA,CAAC3D,UAAU;YAACmP,OAAO,EAAC,WAAW;YAACC,EAAE,EAAE;cAAEW,EAAE,EAAE,CAAC;cAAEC,QAAQ,EAAE;YAAO,CAAE;YAAAd,QAAA,EAAC;UAEjE;YAAAK,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAY,CAAC,eACb/L,OAAA,CAAC1D,SAAS;YACRgQ,IAAI,EAAC,OAAO;YACZC,KAAK,EAAC,SAAS;YACff,OAAO,EAAC,UAAU;YAClBvH,KAAK,EAAExC,WAAY;YACnB+K,QAAQ,EAAGxD,CAAC,IAAKtH,cAAc,CAACsH,CAAC,CAAChF,MAAM,CAACC,KAAK,CAAE;YAChDwI,WAAW,EAAC,yBAAyB;YACrChB,EAAE,EAAE;cAAEiB,QAAQ,EAAE,CAAC;cAAET,KAAK,EAAE,OAAO;cAAEG,EAAE,EAAE;YAAE;UAAE;YAAAR,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC5C,CAAC,eACF/L,OAAA,CAACzD,MAAM;YACLiP,OAAO,EAAC,WAAW;YACnBmB,KAAK,EAAC,SAAS;YACfC,OAAO,EAAElF,oBAAqB;YAC9BmF,QAAQ,EAAElM,WAAW,IAAI,CAACc,WAAW,CAACkF,IAAI,CAAC,CAAE;YAC7CmG,SAAS,EAAEnM,WAAW,gBAAGX,OAAA,CAACjD,gBAAgB;cAACuP,IAAI,EAAE;YAAG;cAAAV,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,gBAAG/L,OAAA,CAAC5B,UAAU;cAAC2O,QAAQ,EAAC;YAAO;cAAAnB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAE;YAC1FO,IAAI,EAAC,OAAO;YACZb,EAAE,EAAE;cAAEY,QAAQ,EAAE,MAAM;cAAEW,MAAM,EAAE,MAAM;cAAEZ,EAAE,EAAE;YAAE,CAAE;YAAAb,QAAA,EACjD;UAED;YAAAK,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC,EAGRxK,YAAY,iBACXvB,OAAA,CAAC7D,GAAG;YAACsP,EAAE,EAAE;cAAES,OAAO,EAAE,MAAM;cAAEC,UAAU,EAAE,QAAQ;cAAEO,QAAQ,EAAE,CAAC;cAAEO,QAAQ,EAAE,QAAQ;cAAEC,QAAQ,EAAE,QAAQ;cAAEC,EAAE,EAAE;YAAE,CAAE;YAAA5B,QAAA,gBAC7GvL,OAAA,CAAC3D,UAAU;cAACmP,OAAO,EAAC,WAAW;cAACC,EAAE,EAAE;gBAAEE,UAAU,EAAE,MAAM;gBAAEyB,UAAU,EAAE,QAAQ;gBAAEhB,EAAE,EAAE,CAAC;gBAAEW,QAAQ,EAAE;cAAS,CAAE;cAAAxB,QAAA,GAAC,QACrG,eAAAvL,OAAA;gBAAMqN,KAAK,EAAE;kBAAEV,KAAK,EAAE;gBAAU,CAAE;gBAAApB,QAAA,EAAEhK,YAAY,CAACQ;cAAO;gBAAA6J,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC5D,CAAC,eACb/L,OAAA,CAACnD,OAAO;cAACyQ,WAAW,EAAC,UAAU;cAACC,QAAQ;cAAC9B,EAAE,EAAE;gBAAE+B,EAAE,EAAE;cAAI;YAAE;cAAA5B,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,eAC5D/L,OAAA,CAAC7D,GAAG;cAACsP,EAAE,EAAE;gBAAES,OAAO,EAAE,MAAM;gBAAEC,UAAU,EAAE,QAAQ;gBAAEsB,GAAG,EAAE,CAAC;gBAAER,QAAQ,EAAE,QAAQ;gBAAEC,QAAQ,EAAE;cAAS,CAAE;cAAA3B,QAAA,gBACjGvL,OAAA,CAAC7D,GAAG;gBAACsP,EAAE,EAAE;kBAAES,OAAO,EAAE,MAAM;kBAAEC,UAAU,EAAE,QAAQ;kBAAEiB,UAAU,EAAE;gBAAS,CAAE;gBAAA7B,QAAA,gBACvEvL,OAAA,CAAC3D,UAAU;kBAACmP,OAAO,EAAC,OAAO;kBAACC,EAAE,EAAE;oBAAEE,UAAU,EAAE,QAAQ;oBAAEoB,QAAQ,EAAE,SAAS;oBAAEX,EAAE,EAAE;kBAAI,CAAE;kBAAAb,QAAA,EAAC;gBAAK;kBAAAK,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAY,CAAC,eAC1G/L,OAAA,CAAC3D,UAAU;kBAACmP,OAAO,EAAC,OAAO;kBAACC,EAAE,EAAE;oBAAEsB,QAAQ,EAAE;kBAAU,CAAE;kBAAAxB,QAAA,EAAEhK,YAAY,CAACgF,SAAS,IAAI;gBAAK;kBAAAqF,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAa,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACpG,CAAC,eACN/L,OAAA,CAAC7D,GAAG;gBAACsP,EAAE,EAAE;kBAAES,OAAO,EAAE,MAAM;kBAAEC,UAAU,EAAE,QAAQ;kBAAEiB,UAAU,EAAE;gBAAS,CAAE;gBAAA7B,QAAA,gBACvEvL,OAAA,CAAC3D,UAAU;kBAACmP,OAAO,EAAC,OAAO;kBAACC,EAAE,EAAE;oBAAEE,UAAU,EAAE,QAAQ;oBAAEoB,QAAQ,EAAE,SAAS;oBAAEX,EAAE,EAAE;kBAAI,CAAE;kBAAAb,QAAA,EAAC;gBAAK;kBAAAK,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAY,CAAC,eAC1G/L,OAAA,CAAC3D,UAAU;kBAACmP,OAAO,EAAC,OAAO;kBAACC,EAAE,EAAE;oBAAEsB,QAAQ,EAAE;kBAAU,CAAE;kBAAAxB,QAAA,EAAEhK,YAAY,CAACiF,OAAO,IAAI;gBAAK;kBAAAoF,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAa,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAClG,CAAC,eACN/L,OAAA,CAAC7D,GAAG;gBAACsP,EAAE,EAAE;kBAAES,OAAO,EAAE,MAAM;kBAAEC,UAAU,EAAE,QAAQ;kBAAEiB,UAAU,EAAE;gBAAS,CAAE;gBAAA7B,QAAA,gBACvEvL,OAAA,CAAC3D,UAAU;kBAACmP,OAAO,EAAC,OAAO;kBAACC,EAAE,EAAE;oBAAEE,UAAU,EAAE,QAAQ;oBAAEoB,QAAQ,EAAE,SAAS;oBAAEX,EAAE,EAAE;kBAAI,CAAE;kBAAAb,QAAA,EAAC;gBAAM;kBAAAK,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAY,CAAC,eAC3G/L,OAAA,CAAC3D,UAAU;kBAACmP,OAAO,EAAC,OAAO;kBAACC,EAAE,EAAE;oBAAEsB,QAAQ,EAAE;kBAAU,CAAE;kBAAAxB,QAAA,GAAEhK,YAAY,CAACkI,aAAa,IAAI,KAAK,EAAC,IAAE;gBAAA;kBAAAmC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAY,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC1G,CAAC,eACN/L,OAAA,CAAC7D,GAAG;gBAACsP,EAAE,EAAE;kBAAES,OAAO,EAAE,MAAM;kBAAEC,UAAU,EAAE,QAAQ;kBAAEiB,UAAU,EAAE;gBAAS,CAAE;gBAAA7B,QAAA,gBACvEvL,OAAA,CAAC3D,UAAU;kBAACmP,OAAO,EAAC,OAAO;kBAACC,EAAE,EAAE;oBAAEE,UAAU,EAAE,QAAQ;oBAAEoB,QAAQ,EAAE,SAAS;oBAAEX,EAAE,EAAE;kBAAI,CAAE;kBAAAb,QAAA,EAAC;gBAAM;kBAAAK,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAY,CAAC,eAC3G/L,OAAA,CAAC9C,IAAI;kBACHoP,IAAI,EAAC,OAAO;kBACZC,KAAK,EAAEhL,YAAY,CAAC2G,mBAAmB,IAAI,KAAM;kBACjDyE,KAAK,EAAEhN,kBAAkB,CAAC4B,YAAY,CAAC2G,mBAAmB,CAAE;kBAC5DuD,EAAE,EAAE;oBAAEuB,MAAM,EAAE,MAAM;oBAAE,kBAAkB,EAAE;sBAAEU,EAAE,EAAE,CAAC;sBAAEC,EAAE,EAAE,CAAC;sBAAEZ,QAAQ,EAAE;oBAAS;kBAAE;gBAAE;kBAAAnB,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAClF,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACC,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CACN;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACE;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACD,CAAC,eAER/L,OAAA,CAACnD,OAAO;QAAC4O,EAAE,EAAE;UAAEqC,EAAE,EAAE;QAAE;MAAE;QAAAlC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACvB,CAAC,EAGL9K,iBAAiB,IAAIF,aAAa,CAAC0D,MAAM,GAAG,CAAC,iBAC5CzE,OAAA,CAAC5D,KAAK;MAACqP,EAAE,EAAE;QAAEO,CAAC,EAAE,CAAC;QAAEN,EAAE,EAAE;MAAE,CAAE;MAAAH,QAAA,gBACzBvL,OAAA,CAAC3D,UAAU;QAACmP,OAAO,EAAC,IAAI;QAACiD,YAAY;QAAAlD,QAAA,EAAC;MAEtC;QAAAK,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAY,CAAC,eACb/L,OAAA,CAACrC,cAAc;QAAA4N,QAAA,eACbvL,OAAA,CAACxC,KAAK;UAAC8O,IAAI,EAAC,OAAO;UAAAf,QAAA,gBACjBvL,OAAA,CAACpC,SAAS;YAAA2N,QAAA,eACRvL,OAAA,CAACnC,QAAQ;cAAC4N,EAAE,EAAE;gBAAE0C,OAAO,EAAE;cAAU,CAAE;cAAA5C,QAAA,gBACnCvL,OAAA,CAACtC,SAAS;gBAAA6N,QAAA,EAAC;cAAO;gBAAAK,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAW,CAAC,eAC9B/L,OAAA,CAACtC,SAAS;gBAAA6N,QAAA,EAAC;cAAS;gBAAAK,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAW,CAAC,eAChC/L,OAAA,CAACtC,SAAS;gBAAA6N,QAAA,EAAC;cAAU;gBAAAK,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAW,CAAC,eACjC/L,OAAA,CAACtC,SAAS;gBAAA6N,QAAA,EAAC;cAAU;gBAAAK,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAW,CAAC,eACjC/L,OAAA,CAACtC,SAAS;gBAAA6N,QAAA,EAAC;cAAa;gBAAAK,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAW,CAAC,eACpC/L,OAAA,CAACtC,SAAS;gBAAA6N,QAAA,EAAC;cAAK;gBAAAK,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAW,CAAC,eAC5B/L,OAAA,CAACtC,SAAS;gBAAA6N,QAAA,EAAC;cAAM;gBAAAK,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAW,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACrB;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACF,CAAC,eACZ/L,OAAA,CAACvC,SAAS;YAAA8N,QAAA,EACPxK,aAAa,CAAC6G,GAAG,CAAEhF,IAAI,iBACtB5C,OAAA,CAACnC,QAAQ;cAAA0N,QAAA,gBACPvL,OAAA,CAACtC,SAAS;gBAAA6N,QAAA,EAAE3I,IAAI,CAACb;cAAO;gBAAA6J,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAY,CAAC,eACrC/L,OAAA,CAACtC,SAAS;gBAAA6N,QAAA,EAAE3I,IAAI,CAAC2D,SAAS,IAAI;cAAK;gBAAAqF,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAY,CAAC,eAChD/L,OAAA,CAACtC,SAAS;gBAAA6N,QAAA,EAAE3I,IAAI,CAAC4D,OAAO,IAAI;cAAK;gBAAAoF,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAY,CAAC,eAC9C/L,OAAA,CAACtC,SAAS;gBAAA6N,QAAA,GAAC,MAAI,EAAC3I,IAAI,CAAC8O,mBAAmB,IAAI,KAAK,eAAC1R,OAAA;kBAAA4L,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAI,CAAC,OAAG,EAACnJ,IAAI,CAAC+O,iBAAiB,IAAI,KAAK;cAAA;gBAAA/F,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAY,CAAC,eACvG/L,OAAA,CAACtC,SAAS;gBAAA6N,QAAA,GAAE3I,IAAI,CAAC6G,aAAa,IAAI,KAAK,EAAC,IAAE;cAAA;gBAAAmC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAW,CAAC,eACtD/L,OAAA,CAACtC,SAAS;gBAAA6N,QAAA,eACRvL,OAAA,CAAC9C,IAAI;kBACHqP,KAAK,EAAE3J,IAAI,CAACsF,mBAAmB,IAAI,KAAM;kBACzCoE,IAAI,EAAC,OAAO;kBACZK,KAAK,EAAEhN,kBAAkB,CAACiD,IAAI,CAACsF,mBAAmB,CAAE;kBACpDsD,OAAO,EAAC;gBAAU;kBAAAI,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACnB;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACO,CAAC,eACZ/L,OAAA,CAACtC,SAAS;gBAAA6N,QAAA,eACRvL,OAAA,CAACzD,MAAM;kBACL+P,IAAI,EAAC,OAAO;kBACZd,OAAO,EAAC,WAAW;kBACnBmB,KAAK,EAAC,SAAS;kBACfC,OAAO,EAAEA,CAAA,KAAMvE,gBAAgB,CAACzF,IAAI,CAAE;kBACtCiK,QAAQ,EAAEnN,gBAAgB,CAACkD,IAAI,CAAE;kBAAA2I,QAAA,EAClC;gBAED;kBAAAK,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACA,CAAC;YAAA,GAxBCnJ,IAAI,CAACb,OAAO;cAAA6J,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAyBjB,CACX;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACO,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACP;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACM,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACZ,CACR,EAEAxK,YAAY,iBACXvB,OAAA,CAAC7D,GAAG;MAAAoP,QAAA,EACD0D,WAAW,CAAC;IAAC;MAAArD,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACX,CACN,eAGD/L,OAAA,CAAC5C,MAAM;MAACwU,IAAI,EAAEhI,iBAAkB;MAACiI,OAAO,EAAEA,CAAA,KAAMhI,oBAAoB,CAAC,KAAK,CAAE;MAACiI,QAAQ,EAAC,IAAI;MAACC,SAAS;MAAAxG,QAAA,gBAClGvL,OAAA,CAAC3C,WAAW;QAACoO,EAAE,EAAE;UAAE0C,OAAO,EAAE;QAAgB,CAAE;QAAA5C,QAAA,eAC5CvL,OAAA,CAAC7D,GAAG;UAACsP,EAAE,EAAE;YAAES,OAAO,EAAE,MAAM;YAAEC,UAAU,EAAE,QAAQ;YAAEsB,GAAG,EAAE;UAAE,CAAE;UAAAlC,QAAA,gBACzDvL,OAAA,CAACtB,WAAW;YAACiO,KAAK,EAAC;UAAS;YAAAf,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,eAC/B/L,OAAA,CAAC3D,UAAU;YAACmP,OAAO,EAAC,IAAI;YAAAD,QAAA,EAAEzB,kBAAkB,CAACE;UAAK;YAAA4B,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAa,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC7D;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACK,CAAC,eACd/L,OAAA,CAAC1C,aAAa;QAAAiO,QAAA,eACZvL,OAAA,CAAC3D,UAAU;UAACmP,OAAO,EAAC,OAAO;UAACC,EAAE,EAAE;YAAEiD,EAAE,EAAE;UAAE,CAAE;UAAAnD,QAAA,EACvCzB,kBAAkB,CAAC/E;QAAO;UAAA6G,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACjB;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACA,CAAC,eAChB/L,OAAA,CAACzC,aAAa;QAAAgO,QAAA,gBACZvL,OAAA,CAACzD,MAAM;UAACqQ,OAAO,EAAEA,CAAA,KAAM/C,oBAAoB,CAAC,KAAK,CAAE;UAAC8C,KAAK,EAAC,WAAW;UAACnB,OAAO,EAAC,UAAU;UAAAD,QAAA,EAAC;QAEzF;UAAAK,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC,eACT/L,OAAA,CAACzD,MAAM;UACLqQ,OAAO,EAAEA,CAAA,KAAM;YACb/C,oBAAoB,CAAC,KAAK,CAAC;YAC3BC,kBAAkB,CAACG,SAAS,CAAC,CAAC;UAChC,CAAE;UACF0C,KAAK,EAAC,SAAS;UACfnB,OAAO,EAAC,WAAW;UACnBwG,SAAS;UAAAzG,QAAA,EACV;QAED;UAAAK,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACI,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACV,CAAC,eAGT/L,OAAA,CAAC5C,MAAM;MAACwU,IAAI,EAAE9O,qBAAsB;MAAC+O,OAAO,EAAEZ,4BAA6B;MAACa,QAAQ,EAAC,IAAI;MAACC,SAAS;MAAAxG,QAAA,gBACjGvL,OAAA,CAAC3C,WAAW;QAACoO,EAAE,EAAE;UAAE0C,OAAO,EAAE;QAAgB,CAAE;QAAA5C,QAAA,eAC5CvL,OAAA,CAAC7D,GAAG;UAACsP,EAAE,EAAE;YAAES,OAAO,EAAE,MAAM;YAAEC,UAAU,EAAE,QAAQ;YAAEsB,GAAG,EAAE;UAAE,CAAE;UAAAlC,QAAA,gBACzDvL,OAAA,CAACtB,WAAW;YAACiO,KAAK,EAAC;UAAS;YAAAf,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,eAC/B/L,OAAA,CAAC3D,UAAU;YAACmP,OAAO,EAAC,IAAI;YAAAD,QAAA,EAAC;UAAe;YAAAK,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAY,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAClD;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACK,CAAC,eACd/L,OAAA,CAAC1C,aAAa;QAAAiO,QAAA,EACXrI,eAAe,iBACdlD,OAAA,CAAC7D,GAAG;UAACsP,EAAE,EAAE;YAAEiD,EAAE,EAAE;UAAE,CAAE;UAAAnD,QAAA,gBACjBvL,OAAA,CAAC3D,UAAU;YAACmP,OAAO,EAAC,OAAO;YAACyG,SAAS;YAAA1G,QAAA,GAAC,UAC5B,eAAAvL,OAAA;cAAAuL,QAAA,EAASrI,eAAe,CAACnB;YAAO;cAAA6J,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAS,CAAC,4BAAqB,EAAC7I,eAAe,CAACiF,eAAe,IAAI,CAAC,EAAC,KAC/G;UAAA;YAAAyD,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAY,CAAC,eACb/L,OAAA,CAAC3D,UAAU;YAACmP,OAAO,EAAC,OAAO;YAACyG,SAAS;YAAA1G,QAAA,EAAC;UAEtC;YAAAK,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAY,CAAC,eACb/L,OAAA,CAAC3D,UAAU;YAACmP,OAAO,EAAC,OAAO;YAACwC,SAAS,EAAC,IAAI;YAAAzC,QAAA,gBACxCvL,OAAA;cAAAuL,QAAA,EAAI;YAAsC;cAAAK,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eAC/C/L,OAAA;cAAAuL,QAAA,EAAI;YAAyB;cAAAK,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eAClC/L,OAAA;cAAAuL,QAAA,EAAI;YAAsB;cAAAK,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACrB,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACV;MACN;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACY,CAAC,eAChB/L,OAAA,CAACzC,aAAa;QAACkO,EAAE,EAAE;UAAEO,CAAC,EAAE,CAAC;UAAE6B,cAAc,EAAE;QAAgB,CAAE;QAAAtC,QAAA,gBAC3DvL,OAAA,CAACzD,MAAM;UAACqQ,OAAO,EAAEqE,4BAA6B;UAACtE,KAAK,EAAC,WAAW;UAAApB,QAAA,EAAC;QAEjE;UAAAK,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC,eACT/L,OAAA,CAAC7D,GAAG;UAAAoP,QAAA,gBACFvL,OAAA,CAACzD,MAAM;YAACqQ,OAAO,EAAEuE,wBAAyB;YAACxE,KAAK,EAAC,SAAS;YAAClB,EAAE,EAAE;cAAEW,EAAE,EAAE;YAAE,CAAE;YAAAb,QAAA,EAAC;UAE1E;YAAAK,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC,eACT/L,OAAA,CAACzD,MAAM;YAACqQ,OAAO,EAAEsE,gBAAiB;YAAC1F,OAAO,EAAC,WAAW;YAACmB,KAAK,EAAC,SAAS;YAAApB,QAAA,EAAC;UAEvE;YAAAK,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACN,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACO,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACV,CAAC,eAGT/L,OAAA,CAACd,sBAAsB;MACrB0S,IAAI,EAAEtP,0BAA2B;MACjCuP,OAAO,EAAET,iCAAkC;MAC3CxO,IAAI,EAAEF,oBAAoB,CAACE,IAAK;MAChCC,MAAM,EAAEH,oBAAoB,CAACG,MAAO;MACpCqP,YAAY,EAAEb,2BAA4B;MAC1Cc,mBAAmB,EAAEV,uBAAwB;MAC7CW,0BAA0B,EAAEZ;IAA+B;MAAA5F,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAC5D,CAAC,eAGF/L,OAAA,CAAC5C,MAAM;MACLwU,IAAI,EAAExO,qBAAsB;MAC5ByO,OAAO,EAAEA,CAAA,KAAMxO,wBAAwB,CAAC,KAAK,CAAE;MAC/CyO,QAAQ,EAAC,IAAI;MACbC,SAAS;MAAAxG,QAAA,gBAETvL,OAAA,CAAC3C,WAAW;QAAAkO,QAAA,eACVvL,OAAA,CAAC7D,GAAG;UAACsP,EAAE,EAAE;YAAES,OAAO,EAAE,MAAM;YAAEC,UAAU,EAAE,QAAQ;YAAEsB,GAAG,EAAE;UAAE,CAAE;UAAAlC,QAAA,gBACzDvL,OAAA,CAACpB,QAAQ;YAAC+N,KAAK,EAAC;UAAS;YAAAf,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,eAC5B/L,OAAA,CAAC3D,UAAU;YAACmP,OAAO,EAAC,IAAI;YAAAD,QAAA,EAAC;UAAa;YAAAK,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAY,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAChD;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACK,CAAC,eACd/L,OAAA,CAAC1C,aAAa;QAAAiO,QAAA,eACZvL,OAAA,CAACb,eAAe;UAACyD,IAAI,EAAErB;QAAa;UAAAqK,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC1B,CAAC,eAChB/L,OAAA,CAACzC,aAAa;QAAAgO,QAAA,eACZvL,OAAA,CAACzD,MAAM;UAACqQ,OAAO,EAAEA,CAAA,KAAMvJ,wBAAwB,CAAC,KAAK,CAAE;UAACsJ,KAAK,EAAC,SAAS;UAAApB,QAAA,EAAC;QAExE;UAAAK,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACI,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACV,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACN,CAAC;AAEV,CAAC;AAACxL,EAAA,CAphEIJ,kBAAkB;EAAA,QACLpB,WAAW;AAAA;AAAAsT,EAAA,GADxBlS,kBAAkB;AAshExB,eAAeA,kBAAkB;AAAC,IAAAkS,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}