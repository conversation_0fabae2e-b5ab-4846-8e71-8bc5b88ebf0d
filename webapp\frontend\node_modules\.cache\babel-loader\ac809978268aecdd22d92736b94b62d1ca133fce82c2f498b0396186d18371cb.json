{"ast": null, "code": "var _jsxFileName = \"C:\\\\CMS\\\\webapp\\\\frontend\\\\src\\\\components\\\\cantieri\\\\HoldToViewButton.js\",\n  _s = $RefreshSig$();\nimport React, { useState, useRef, useCallback } from 'react';\nimport { Button, Box, Typography, LinearProgress, Tooltip } from '@mui/material';\nimport { Visibility as VisibilityIcon, Lock as LockIcon } from '@mui/icons-material';\n\n/**\n * Componente pulsante \"Tieni premuto per visualizzare\"\n * Richiede di tenere premuto il pulsante per 2-3 secondi prima di eseguire l'azione\n */\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst HoldToViewButton = ({\n  onComplete,\n  loading = false,\n  disabled = false,\n  holdDuration = null,\n  // Se null, usa un timer casuale\n  children = \"Tieni premuto per visualizzare\",\n  variant = \"outlined\",\n  color = \"primary\",\n  size = \"medium\",\n  fullWidth = false,\n  startIcon = /*#__PURE__*/_jsxDEV(VisibilityIcon, {}, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 28,\n    columnNumber: 15\n  }, this)\n}) => {\n  _s();\n  const [isHolding, setIsHolding] = useState(false);\n  const [progress, setProgress] = useState(0);\n  const [completed, setCompleted] = useState(false);\n  const [currentHoldDuration, setCurrentHoldDuration] = useState(holdDuration);\n  const holdTimerRef = useRef(null);\n  const progressTimerRef = useRef(null);\n  const startTimeRef = useRef(null);\n\n  // Pulisce i timer\n  const clearTimers = useCallback(() => {\n    if (holdTimerRef.current) {\n      clearTimeout(holdTimerRef.current);\n      holdTimerRef.current = null;\n    }\n    if (progressTimerRef.current) {\n      clearInterval(progressTimerRef.current);\n      progressTimerRef.current = null;\n    }\n  }, []);\n\n  // Inizia il hold\n  const startHold = useCallback(() => {\n    if (disabled || loading || completed) return;\n    setIsHolding(true);\n    setProgress(0);\n    startTimeRef.current = Date.now();\n\n    // Timer per il progresso\n    progressTimerRef.current = setInterval(() => {\n      const elapsed = Date.now() - startTimeRef.current;\n      const newProgress = Math.min(elapsed / holdDuration * 100, 100);\n      setProgress(newProgress);\n    }, 50); // Aggiorna ogni 50ms per fluidità\n\n    // Timer per il completamento\n    holdTimerRef.current = setTimeout(() => {\n      setCompleted(true);\n      setIsHolding(false);\n      setProgress(100);\n      clearTimers();\n\n      // Esegui l'azione\n      if (onComplete) {\n        onComplete();\n      }\n\n      // Reset dopo un breve delay\n      setTimeout(() => {\n        setCompleted(false);\n        setProgress(0);\n      }, 1000);\n    }, holdDuration);\n  }, [disabled, loading, completed, holdDuration, onComplete, clearTimers]);\n\n  // Ferma il hold\n  const stopHold = useCallback(() => {\n    if (completed) return;\n    setIsHolding(false);\n    setProgress(0);\n    clearTimers();\n  }, [completed, clearTimers]);\n\n  // Gestisce mouse events\n  const handleMouseDown = useCallback(e => {\n    e.preventDefault();\n    startHold();\n  }, [startHold]);\n  const handleMouseUp = useCallback(() => {\n    stopHold();\n  }, [stopHold]);\n  const handleMouseLeave = useCallback(() => {\n    stopHold();\n  }, [stopHold]);\n\n  // Gestisce touch events per mobile\n  const handleTouchStart = useCallback(e => {\n    e.preventDefault();\n    startHold();\n  }, [startHold]);\n  const handleTouchEnd = useCallback(() => {\n    stopHold();\n  }, [stopHold]);\n\n  // Previene il context menu\n  const handleContextMenu = useCallback(e => {\n    e.preventDefault();\n  }, []);\n\n  // Cleanup al unmount\n  React.useEffect(() => {\n    return () => {\n      clearTimers();\n    };\n  }, [clearTimers]);\n\n  // Determina il colore del pulsante\n  const getButtonColor = () => {\n    if (completed) return 'success';\n    if (isHolding) return 'warning';\n    return color;\n  };\n\n  // Determina l'icona\n  const getIcon = () => {\n    if (completed) return /*#__PURE__*/_jsxDEV(VisibilityIcon, {}, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 140,\n      columnNumber: 27\n    }, this);\n    if (isHolding) return /*#__PURE__*/_jsxDEV(LockIcon, {}, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 141,\n      columnNumber: 27\n    }, this);\n    return startIcon;\n  };\n\n  // Determina il testo\n  const getText = () => {\n    if (completed) return \"Password visualizzata!\";\n    if (isHolding) return \"Continua a tenere premuto...\";\n    return children;\n  };\n  return /*#__PURE__*/_jsxDEV(Tooltip, {\n    title: disabled ? \"Funzione non disponibile\" : \"Tieni premuto per 2-3 secondi per visualizzare la password\",\n    arrow: true,\n    children: /*#__PURE__*/_jsxDEV(Box, {\n      sx: {\n        position: 'relative',\n        width: fullWidth ? '100%' : 'auto'\n      },\n      children: [/*#__PURE__*/_jsxDEV(Button, {\n        variant: variant,\n        color: getButtonColor(),\n        size: size,\n        fullWidth: fullWidth,\n        disabled: disabled || loading,\n        startIcon: getIcon(),\n        onMouseDown: handleMouseDown,\n        onMouseUp: handleMouseUp,\n        onMouseLeave: handleMouseLeave,\n        onTouchStart: handleTouchStart,\n        onTouchEnd: handleTouchEnd,\n        onContextMenu: handleContextMenu,\n        sx: {\n          position: 'relative',\n          overflow: 'hidden',\n          userSelect: 'none',\n          transition: 'all 0.2s ease',\n          '&:active': {\n            transform: 'scale(0.98)'\n          },\n          ...(isHolding && {\n            boxShadow: '0 0 10px rgba(255, 152, 0, 0.5)'\n          }),\n          ...(completed && {\n            boxShadow: '0 0 10px rgba(76, 175, 80, 0.5)'\n          })\n        },\n        children: [/*#__PURE__*/_jsxDEV(Typography, {\n          variant: \"button\",\n          sx: {\n            position: 'relative',\n            zIndex: 2\n          },\n          children: getText()\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 187,\n          columnNumber: 11\n        }, this), (isHolding || completed) && /*#__PURE__*/_jsxDEV(LinearProgress, {\n          variant: \"determinate\",\n          value: progress,\n          sx: {\n            position: 'absolute',\n            bottom: 0,\n            left: 0,\n            right: 0,\n            height: 4,\n            zIndex: 1,\n            '& .MuiLinearProgress-bar': {\n              backgroundColor: completed ? 'success.main' : 'warning.main',\n              transition: 'none'\n            }\n          }\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 193,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 158,\n        columnNumber: 9\n      }, this), isHolding && /*#__PURE__*/_jsxDEV(Box, {\n        sx: {\n          position: 'absolute',\n          top: -2,\n          right: -2,\n          width: 20,\n          height: 20,\n          borderRadius: '50%',\n          background: `conic-gradient(orange ${progress * 3.6}deg, transparent 0deg)`,\n          display: 'flex',\n          alignItems: 'center',\n          justifyContent: 'center',\n          '&::before': {\n            content: '\"\"',\n            width: 14,\n            height: 14,\n            borderRadius: '50%',\n            backgroundColor: 'background.paper'\n          }\n        }\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 214,\n        columnNumber: 11\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 157,\n      columnNumber: 7\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 153,\n    columnNumber: 5\n  }, this);\n};\n_s(HoldToViewButton, \"/8LoDOiIkPcf0WTYoC/+VJDOu38=\");\n_c = HoldToViewButton;\nexport default HoldToViewButton;\nvar _c;\n$RefreshReg$(_c, \"HoldToViewButton\");", "map": {"version": 3, "names": ["React", "useState", "useRef", "useCallback", "<PERSON><PERSON>", "Box", "Typography", "LinearProgress", "<PERSON><PERSON><PERSON>", "Visibility", "VisibilityIcon", "Lock", "LockIcon", "jsxDEV", "_jsxDEV", "HoldToViewButton", "onComplete", "loading", "disabled", "holdDuration", "children", "variant", "color", "size", "fullWidth", "startIcon", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "_s", "isHolding", "setIsHolding", "progress", "setProgress", "completed", "setCompleted", "currentHoldDuration", "setCurrentHoldDuration", "holdTimerRef", "progressTimerRef", "startTimeRef", "clearTimers", "current", "clearTimeout", "clearInterval", "startHold", "Date", "now", "setInterval", "elapsed", "newProgress", "Math", "min", "setTimeout", "stopHold", "handleMouseDown", "e", "preventDefault", "handleMouseUp", "handleMouseLeave", "handleTouchStart", "handleTouchEnd", "handleContextMenu", "useEffect", "getButtonColor", "getIcon", "getText", "title", "arrow", "sx", "position", "width", "onMouseDown", "onMouseUp", "onMouseLeave", "onTouchStart", "onTouchEnd", "onContextMenu", "overflow", "userSelect", "transition", "transform", "boxShadow", "zIndex", "value", "bottom", "left", "right", "height", "backgroundColor", "top", "borderRadius", "background", "display", "alignItems", "justifyContent", "content", "_c", "$RefreshReg$"], "sources": ["C:/CMS/webapp/frontend/src/components/cantieri/HoldToViewButton.js"], "sourcesContent": ["import React, { useState, useRef, useCallback } from 'react';\nimport {\n  Button,\n  Box,\n  Typography,\n  LinearProgress,\n  Tooltip\n} from '@mui/material';\nimport {\n  Visibility as VisibilityIcon,\n  Lock as LockIcon\n} from '@mui/icons-material';\n\n/**\n * Componente pulsante \"Tieni premuto per visualizzare\"\n * Richiede di tenere premuto il pulsante per 2-3 secondi prima di eseguire l'azione\n */\nconst HoldToViewButton = ({\n  onComplete,\n  loading = false,\n  disabled = false,\n  holdDuration = null, // Se null, usa un timer casuale\n  children = \"Tieni premuto per visualizzare\",\n  variant = \"outlined\",\n  color = \"primary\",\n  size = \"medium\",\n  fullWidth = false,\n  startIcon = <VisibilityIcon />\n}) => {\n  const [isHolding, setIsHolding] = useState(false);\n  const [progress, setProgress] = useState(0);\n  const [completed, setCompleted] = useState(false);\n  const [currentHoldDuration, setCurrentHoldDuration] = useState(holdDuration);\n\n  const holdTimerRef = useRef(null);\n  const progressTimerRef = useRef(null);\n  const startTimeRef = useRef(null);\n\n  // Pulisce i timer\n  const clearTimers = useCallback(() => {\n    if (holdTimerRef.current) {\n      clearTimeout(holdTimerRef.current);\n      holdTimerRef.current = null;\n    }\n    if (progressTimerRef.current) {\n      clearInterval(progressTimerRef.current);\n      progressTimerRef.current = null;\n    }\n  }, []);\n\n  // Inizia il hold\n  const startHold = useCallback(() => {\n    if (disabled || loading || completed) return;\n\n    setIsHolding(true);\n    setProgress(0);\n    startTimeRef.current = Date.now();\n\n    // Timer per il progresso\n    progressTimerRef.current = setInterval(() => {\n      const elapsed = Date.now() - startTimeRef.current;\n      const newProgress = Math.min((elapsed / holdDuration) * 100, 100);\n      setProgress(newProgress);\n    }, 50); // Aggiorna ogni 50ms per fluidità\n\n    // Timer per il completamento\n    holdTimerRef.current = setTimeout(() => {\n      setCompleted(true);\n      setIsHolding(false);\n      setProgress(100);\n      clearTimers();\n      \n      // Esegui l'azione\n      if (onComplete) {\n        onComplete();\n      }\n      \n      // Reset dopo un breve delay\n      setTimeout(() => {\n        setCompleted(false);\n        setProgress(0);\n      }, 1000);\n    }, holdDuration);\n  }, [disabled, loading, completed, holdDuration, onComplete, clearTimers]);\n\n  // Ferma il hold\n  const stopHold = useCallback(() => {\n    if (completed) return;\n    \n    setIsHolding(false);\n    setProgress(0);\n    clearTimers();\n  }, [completed, clearTimers]);\n\n  // Gestisce mouse events\n  const handleMouseDown = useCallback((e) => {\n    e.preventDefault();\n    startHold();\n  }, [startHold]);\n\n  const handleMouseUp = useCallback(() => {\n    stopHold();\n  }, [stopHold]);\n\n  const handleMouseLeave = useCallback(() => {\n    stopHold();\n  }, [stopHold]);\n\n  // Gestisce touch events per mobile\n  const handleTouchStart = useCallback((e) => {\n    e.preventDefault();\n    startHold();\n  }, [startHold]);\n\n  const handleTouchEnd = useCallback(() => {\n    stopHold();\n  }, [stopHold]);\n\n  // Previene il context menu\n  const handleContextMenu = useCallback((e) => {\n    e.preventDefault();\n  }, []);\n\n  // Cleanup al unmount\n  React.useEffect(() => {\n    return () => {\n      clearTimers();\n    };\n  }, [clearTimers]);\n\n  // Determina il colore del pulsante\n  const getButtonColor = () => {\n    if (completed) return 'success';\n    if (isHolding) return 'warning';\n    return color;\n  };\n\n  // Determina l'icona\n  const getIcon = () => {\n    if (completed) return <VisibilityIcon />;\n    if (isHolding) return <LockIcon />;\n    return startIcon;\n  };\n\n  // Determina il testo\n  const getText = () => {\n    if (completed) return \"Password visualizzata!\";\n    if (isHolding) return \"Continua a tenere premuto...\";\n    return children;\n  };\n\n  return (\n    <Tooltip \n      title={disabled ? \"Funzione non disponibile\" : \"Tieni premuto per 2-3 secondi per visualizzare la password\"}\n      arrow\n    >\n      <Box sx={{ position: 'relative', width: fullWidth ? '100%' : 'auto' }}>\n        <Button\n          variant={variant}\n          color={getButtonColor()}\n          size={size}\n          fullWidth={fullWidth}\n          disabled={disabled || loading}\n          startIcon={getIcon()}\n          onMouseDown={handleMouseDown}\n          onMouseUp={handleMouseUp}\n          onMouseLeave={handleMouseLeave}\n          onTouchStart={handleTouchStart}\n          onTouchEnd={handleTouchEnd}\n          onContextMenu={handleContextMenu}\n          sx={{\n            position: 'relative',\n            overflow: 'hidden',\n            userSelect: 'none',\n            transition: 'all 0.2s ease',\n            '&:active': {\n              transform: 'scale(0.98)'\n            },\n            ...(isHolding && {\n              boxShadow: '0 0 10px rgba(255, 152, 0, 0.5)'\n            }),\n            ...(completed && {\n              boxShadow: '0 0 10px rgba(76, 175, 80, 0.5)'\n            })\n          }}\n        >\n          <Typography variant=\"button\" sx={{ position: 'relative', zIndex: 2 }}>\n            {getText()}\n          </Typography>\n          \n          {/* Barra di progresso */}\n          {(isHolding || completed) && (\n            <LinearProgress\n              variant=\"determinate\"\n              value={progress}\n              sx={{\n                position: 'absolute',\n                bottom: 0,\n                left: 0,\n                right: 0,\n                height: 4,\n                zIndex: 1,\n                '& .MuiLinearProgress-bar': {\n                  backgroundColor: completed ? 'success.main' : 'warning.main',\n                  transition: 'none'\n                }\n              }}\n            />\n          )}\n        </Button>\n        \n        {/* Indicatore di progresso circolare per mobile */}\n        {isHolding && (\n          <Box\n            sx={{\n              position: 'absolute',\n              top: -2,\n              right: -2,\n              width: 20,\n              height: 20,\n              borderRadius: '50%',\n              background: `conic-gradient(orange ${progress * 3.6}deg, transparent 0deg)`,\n              display: 'flex',\n              alignItems: 'center',\n              justifyContent: 'center',\n              '&::before': {\n                content: '\"\"',\n                width: 14,\n                height: 14,\n                borderRadius: '50%',\n                backgroundColor: 'background.paper'\n              }\n            }}\n          />\n        )}\n      </Box>\n    </Tooltip>\n  );\n};\n\nexport default HoldToViewButton;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,MAAM,EAAEC,WAAW,QAAQ,OAAO;AAC5D,SACEC,MAAM,EACNC,GAAG,EACHC,UAAU,EACVC,cAAc,EACdC,OAAO,QACF,eAAe;AACtB,SACEC,UAAU,IAAIC,cAAc,EAC5BC,IAAI,IAAIC,QAAQ,QACX,qBAAqB;;AAE5B;AACA;AACA;AACA;AAHA,SAAAC,MAAA,IAAAC,OAAA;AAIA,MAAMC,gBAAgB,GAAGA,CAAC;EACxBC,UAAU;EACVC,OAAO,GAAG,KAAK;EACfC,QAAQ,GAAG,KAAK;EAChBC,YAAY,GAAG,IAAI;EAAE;EACrBC,QAAQ,GAAG,gCAAgC;EAC3CC,OAAO,GAAG,UAAU;EACpBC,KAAK,GAAG,SAAS;EACjBC,IAAI,GAAG,QAAQ;EACfC,SAAS,GAAG,KAAK;EACjBC,SAAS,gBAAGX,OAAA,CAACJ,cAAc;IAAAgB,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OAAE;AAC/B,CAAC,KAAK;EAAAC,EAAA;EACJ,MAAM,CAACC,SAAS,EAAEC,YAAY,CAAC,GAAG/B,QAAQ,CAAC,KAAK,CAAC;EACjD,MAAM,CAACgC,QAAQ,EAAEC,WAAW,CAAC,GAAGjC,QAAQ,CAAC,CAAC,CAAC;EAC3C,MAAM,CAACkC,SAAS,EAAEC,YAAY,CAAC,GAAGnC,QAAQ,CAAC,KAAK,CAAC;EACjD,MAAM,CAACoC,mBAAmB,EAAEC,sBAAsB,CAAC,GAAGrC,QAAQ,CAACkB,YAAY,CAAC;EAE5E,MAAMoB,YAAY,GAAGrC,MAAM,CAAC,IAAI,CAAC;EACjC,MAAMsC,gBAAgB,GAAGtC,MAAM,CAAC,IAAI,CAAC;EACrC,MAAMuC,YAAY,GAAGvC,MAAM,CAAC,IAAI,CAAC;;EAEjC;EACA,MAAMwC,WAAW,GAAGvC,WAAW,CAAC,MAAM;IACpC,IAAIoC,YAAY,CAACI,OAAO,EAAE;MACxBC,YAAY,CAACL,YAAY,CAACI,OAAO,CAAC;MAClCJ,YAAY,CAACI,OAAO,GAAG,IAAI;IAC7B;IACA,IAAIH,gBAAgB,CAACG,OAAO,EAAE;MAC5BE,aAAa,CAACL,gBAAgB,CAACG,OAAO,CAAC;MACvCH,gBAAgB,CAACG,OAAO,GAAG,IAAI;IACjC;EACF,CAAC,EAAE,EAAE,CAAC;;EAEN;EACA,MAAMG,SAAS,GAAG3C,WAAW,CAAC,MAAM;IAClC,IAAIe,QAAQ,IAAID,OAAO,IAAIkB,SAAS,EAAE;IAEtCH,YAAY,CAAC,IAAI,CAAC;IAClBE,WAAW,CAAC,CAAC,CAAC;IACdO,YAAY,CAACE,OAAO,GAAGI,IAAI,CAACC,GAAG,CAAC,CAAC;;IAEjC;IACAR,gBAAgB,CAACG,OAAO,GAAGM,WAAW,CAAC,MAAM;MAC3C,MAAMC,OAAO,GAAGH,IAAI,CAACC,GAAG,CAAC,CAAC,GAAGP,YAAY,CAACE,OAAO;MACjD,MAAMQ,WAAW,GAAGC,IAAI,CAACC,GAAG,CAAEH,OAAO,GAAG/B,YAAY,GAAI,GAAG,EAAE,GAAG,CAAC;MACjEe,WAAW,CAACiB,WAAW,CAAC;IAC1B,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC;;IAER;IACAZ,YAAY,CAACI,OAAO,GAAGW,UAAU,CAAC,MAAM;MACtClB,YAAY,CAAC,IAAI,CAAC;MAClBJ,YAAY,CAAC,KAAK,CAAC;MACnBE,WAAW,CAAC,GAAG,CAAC;MAChBQ,WAAW,CAAC,CAAC;;MAEb;MACA,IAAI1B,UAAU,EAAE;QACdA,UAAU,CAAC,CAAC;MACd;;MAEA;MACAsC,UAAU,CAAC,MAAM;QACflB,YAAY,CAAC,KAAK,CAAC;QACnBF,WAAW,CAAC,CAAC,CAAC;MAChB,CAAC,EAAE,IAAI,CAAC;IACV,CAAC,EAAEf,YAAY,CAAC;EAClB,CAAC,EAAE,CAACD,QAAQ,EAAED,OAAO,EAAEkB,SAAS,EAAEhB,YAAY,EAAEH,UAAU,EAAE0B,WAAW,CAAC,CAAC;;EAEzE;EACA,MAAMa,QAAQ,GAAGpD,WAAW,CAAC,MAAM;IACjC,IAAIgC,SAAS,EAAE;IAEfH,YAAY,CAAC,KAAK,CAAC;IACnBE,WAAW,CAAC,CAAC,CAAC;IACdQ,WAAW,CAAC,CAAC;EACf,CAAC,EAAE,CAACP,SAAS,EAAEO,WAAW,CAAC,CAAC;;EAE5B;EACA,MAAMc,eAAe,GAAGrD,WAAW,CAAEsD,CAAC,IAAK;IACzCA,CAAC,CAACC,cAAc,CAAC,CAAC;IAClBZ,SAAS,CAAC,CAAC;EACb,CAAC,EAAE,CAACA,SAAS,CAAC,CAAC;EAEf,MAAMa,aAAa,GAAGxD,WAAW,CAAC,MAAM;IACtCoD,QAAQ,CAAC,CAAC;EACZ,CAAC,EAAE,CAACA,QAAQ,CAAC,CAAC;EAEd,MAAMK,gBAAgB,GAAGzD,WAAW,CAAC,MAAM;IACzCoD,QAAQ,CAAC,CAAC;EACZ,CAAC,EAAE,CAACA,QAAQ,CAAC,CAAC;;EAEd;EACA,MAAMM,gBAAgB,GAAG1D,WAAW,CAAEsD,CAAC,IAAK;IAC1CA,CAAC,CAACC,cAAc,CAAC,CAAC;IAClBZ,SAAS,CAAC,CAAC;EACb,CAAC,EAAE,CAACA,SAAS,CAAC,CAAC;EAEf,MAAMgB,cAAc,GAAG3D,WAAW,CAAC,MAAM;IACvCoD,QAAQ,CAAC,CAAC;EACZ,CAAC,EAAE,CAACA,QAAQ,CAAC,CAAC;;EAEd;EACA,MAAMQ,iBAAiB,GAAG5D,WAAW,CAAEsD,CAAC,IAAK;IAC3CA,CAAC,CAACC,cAAc,CAAC,CAAC;EACpB,CAAC,EAAE,EAAE,CAAC;;EAEN;EACA1D,KAAK,CAACgE,SAAS,CAAC,MAAM;IACpB,OAAO,MAAM;MACXtB,WAAW,CAAC,CAAC;IACf,CAAC;EACH,CAAC,EAAE,CAACA,WAAW,CAAC,CAAC;;EAEjB;EACA,MAAMuB,cAAc,GAAGA,CAAA,KAAM;IAC3B,IAAI9B,SAAS,EAAE,OAAO,SAAS;IAC/B,IAAIJ,SAAS,EAAE,OAAO,SAAS;IAC/B,OAAOT,KAAK;EACd,CAAC;;EAED;EACA,MAAM4C,OAAO,GAAGA,CAAA,KAAM;IACpB,IAAI/B,SAAS,EAAE,oBAAOrB,OAAA,CAACJ,cAAc;MAAAgB,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC;IACxC,IAAIE,SAAS,EAAE,oBAAOjB,OAAA,CAACF,QAAQ;MAAAc,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC;IAClC,OAAOJ,SAAS;EAClB,CAAC;;EAED;EACA,MAAM0C,OAAO,GAAGA,CAAA,KAAM;IACpB,IAAIhC,SAAS,EAAE,OAAO,wBAAwB;IAC9C,IAAIJ,SAAS,EAAE,OAAO,8BAA8B;IACpD,OAAOX,QAAQ;EACjB,CAAC;EAED,oBACEN,OAAA,CAACN,OAAO;IACN4D,KAAK,EAAElD,QAAQ,GAAG,0BAA0B,GAAG,4DAA6D;IAC5GmD,KAAK;IAAAjD,QAAA,eAELN,OAAA,CAACT,GAAG;MAACiE,EAAE,EAAE;QAAEC,QAAQ,EAAE,UAAU;QAAEC,KAAK,EAAEhD,SAAS,GAAG,MAAM,GAAG;MAAO,CAAE;MAAAJ,QAAA,gBACpEN,OAAA,CAACV,MAAM;QACLiB,OAAO,EAAEA,OAAQ;QACjBC,KAAK,EAAE2C,cAAc,CAAC,CAAE;QACxB1C,IAAI,EAAEA,IAAK;QACXC,SAAS,EAAEA,SAAU;QACrBN,QAAQ,EAAEA,QAAQ,IAAID,OAAQ;QAC9BQ,SAAS,EAAEyC,OAAO,CAAC,CAAE;QACrBO,WAAW,EAAEjB,eAAgB;QAC7BkB,SAAS,EAAEf,aAAc;QACzBgB,YAAY,EAAEf,gBAAiB;QAC/BgB,YAAY,EAAEf,gBAAiB;QAC/BgB,UAAU,EAAEf,cAAe;QAC3BgB,aAAa,EAAEf,iBAAkB;QACjCO,EAAE,EAAE;UACFC,QAAQ,EAAE,UAAU;UACpBQ,QAAQ,EAAE,QAAQ;UAClBC,UAAU,EAAE,MAAM;UAClBC,UAAU,EAAE,eAAe;UAC3B,UAAU,EAAE;YACVC,SAAS,EAAE;UACb,CAAC;UACD,IAAInD,SAAS,IAAI;YACfoD,SAAS,EAAE;UACb,CAAC,CAAC;UACF,IAAIhD,SAAS,IAAI;YACfgD,SAAS,EAAE;UACb,CAAC;QACH,CAAE;QAAA/D,QAAA,gBAEFN,OAAA,CAACR,UAAU;UAACe,OAAO,EAAC,QAAQ;UAACiD,EAAE,EAAE;YAAEC,QAAQ,EAAE,UAAU;YAAEa,MAAM,EAAE;UAAE,CAAE;UAAAhE,QAAA,EAClE+C,OAAO,CAAC;QAAC;UAAAzC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACA,CAAC,EAGZ,CAACE,SAAS,IAAII,SAAS,kBACtBrB,OAAA,CAACP,cAAc;UACbc,OAAO,EAAC,aAAa;UACrBgE,KAAK,EAAEpD,QAAS;UAChBqC,EAAE,EAAE;YACFC,QAAQ,EAAE,UAAU;YACpBe,MAAM,EAAE,CAAC;YACTC,IAAI,EAAE,CAAC;YACPC,KAAK,EAAE,CAAC;YACRC,MAAM,EAAE,CAAC;YACTL,MAAM,EAAE,CAAC;YACT,0BAA0B,EAAE;cAC1BM,eAAe,EAAEvD,SAAS,GAAG,cAAc,GAAG,cAAc;cAC5D8C,UAAU,EAAE;YACd;UACF;QAAE;UAAAvD,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CACF;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACK,CAAC,EAGRE,SAAS,iBACRjB,OAAA,CAACT,GAAG;QACFiE,EAAE,EAAE;UACFC,QAAQ,EAAE,UAAU;UACpBoB,GAAG,EAAE,CAAC,CAAC;UACPH,KAAK,EAAE,CAAC,CAAC;UACThB,KAAK,EAAE,EAAE;UACTiB,MAAM,EAAE,EAAE;UACVG,YAAY,EAAE,KAAK;UACnBC,UAAU,EAAE,yBAAyB5D,QAAQ,GAAG,GAAG,wBAAwB;UAC3E6D,OAAO,EAAE,MAAM;UACfC,UAAU,EAAE,QAAQ;UACpBC,cAAc,EAAE,QAAQ;UACxB,WAAW,EAAE;YACXC,OAAO,EAAE,IAAI;YACbzB,KAAK,EAAE,EAAE;YACTiB,MAAM,EAAE,EAAE;YACVG,YAAY,EAAE,KAAK;YACnBF,eAAe,EAAE;UACnB;QACF;MAAE;QAAAhE,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CACF;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACE;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACC,CAAC;AAEd,CAAC;AAACC,EAAA,CA7NIf,gBAAgB;AAAAmF,EAAA,GAAhBnF,gBAAgB;AA+NtB,eAAeA,gBAAgB;AAAC,IAAAmF,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}