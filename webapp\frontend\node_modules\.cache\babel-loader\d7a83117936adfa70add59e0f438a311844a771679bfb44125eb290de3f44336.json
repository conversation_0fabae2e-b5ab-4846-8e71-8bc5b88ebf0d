{"ast": null, "code": "'use client';\n\nimport _extends from \"@babel/runtime/helpers/esm/extends\";\nimport * as React from 'react';\nimport PropTypes from 'prop-types';\nimport { ThemeProvider as MuiThemeProvider, useTheme as usePrivateTheme } from '@mui/private-theming';\nimport exactProp from '@mui/utils/exactProp';\nimport { ThemeContext as StyledEngineThemeContext } from '@mui/styled-engine';\nimport useThemeWithoutDefault from '../useThemeWithoutDefault';\nimport RtlProvider from '../RtlProvider';\nimport DefaultPropsProvider from '../DefaultPropsProvider';\nimport { jsx as _jsx } from \"react/jsx-runtime\";\nconst EMPTY_THEME = {};\nfunction useThemeScoping(themeId, upperTheme, localTheme, isPrivate = false) {\n  return React.useMemo(() => {\n    const resolvedTheme = themeId ? upperTheme[themeId] || upperTheme : upperTheme;\n    if (typeof localTheme === 'function') {\n      const mergedTheme = localTheme(resolvedTheme);\n      const result = themeId ? _extends({}, upperTheme, {\n        [themeId]: mergedTheme\n      }) : mergedTheme;\n      // must return a function for the private theme to NOT merge with the upper theme.\n      // see the test case \"use provided theme from a callback\" in ThemeProvider.test.js\n      if (isPrivate) {\n        return () => result;\n      }\n      return result;\n    }\n    return themeId ? _extends({}, upperTheme, {\n      [themeId]: localTheme\n    }) : _extends({}, upperTheme, localTheme);\n  }, [themeId, upperTheme, localTheme, isPrivate]);\n}\n\n/**\n * This component makes the `theme` available down the React tree.\n * It should preferably be used at **the root of your component tree**.\n *\n * <ThemeProvider theme={theme}> // existing use case\n * <ThemeProvider theme={{ id: theme }}> // theme scoping\n */\nfunction ThemeProvider(props) {\n  const {\n    children,\n    theme: localTheme,\n    themeId\n  } = props;\n  const upperTheme = useThemeWithoutDefault(EMPTY_THEME);\n  const upperPrivateTheme = usePrivateTheme() || EMPTY_THEME;\n  if (process.env.NODE_ENV !== 'production') {\n    if (upperTheme === null && typeof localTheme === 'function' || themeId && upperTheme && !upperTheme[themeId] && typeof localTheme === 'function') {\n      console.error(['MUI: You are providing a theme function prop to the ThemeProvider component:', '<ThemeProvider theme={outerTheme => outerTheme} />', '', 'However, no outer theme is present.', 'Make sure a theme is already injected higher in the React tree ' + 'or provide a theme object.'].join('\\n'));\n    }\n  }\n  const engineTheme = useThemeScoping(themeId, upperTheme, localTheme);\n  const privateTheme = useThemeScoping(themeId, upperPrivateTheme, localTheme, true);\n  const rtlValue = engineTheme.direction === 'rtl';\n  return /*#__PURE__*/_jsx(MuiThemeProvider, {\n    theme: privateTheme,\n    children: /*#__PURE__*/_jsx(StyledEngineThemeContext.Provider, {\n      value: engineTheme,\n      children: /*#__PURE__*/_jsx(RtlProvider, {\n        value: rtlValue,\n        children: /*#__PURE__*/_jsx(DefaultPropsProvider, {\n          value: engineTheme == null ? void 0 : engineTheme.components,\n          children: children\n        })\n      })\n    })\n  });\n}\nprocess.env.NODE_ENV !== \"production\" ? ThemeProvider.propTypes /* remove-proptypes */ = {\n  // ┌────────────────────────────── Warning ──────────────────────────────┐\n  // │ These PropTypes are generated from the TypeScript type definitions. │\n  // │    To update them, edit the d.ts file and run `pnpm proptypes`.     │\n  // └─────────────────────────────────────────────────────────────────────┘\n  /**\n   * Your component tree.\n   */\n  children: PropTypes.node,\n  /**\n   * A theme object. You can provide a function to extend the outer theme.\n   */\n  theme: PropTypes.oneOfType([PropTypes.func, PropTypes.object]).isRequired,\n  /**\n   * The design system's unique id for getting the corresponded theme when there are multiple design systems.\n   */\n  themeId: PropTypes.string\n} : void 0;\nif (process.env.NODE_ENV !== 'production') {\n  process.env.NODE_ENV !== \"production\" ? ThemeProvider.propTypes = exactProp(ThemeProvider.propTypes) : void 0;\n}\nexport default ThemeProvider;", "map": {"version": 3, "names": ["_extends", "React", "PropTypes", "ThemeProvider", "MuiThemeProvider", "useTheme", "usePrivateTheme", "exactProp", "ThemeContext", "StyledEngineThemeContext", "useThemeWithoutDefault", "RtlProvider", "DefaultPropsProvider", "jsx", "_jsx", "EMPTY_THEME", "useThemeScoping", "themeId", "upperTheme", "localTheme", "isPrivate", "useMemo", "resolvedTheme", "mergedTheme", "result", "props", "children", "theme", "upperPrivateTheme", "process", "env", "NODE_ENV", "console", "error", "join", "engineTheme", "privateTheme", "rtlValue", "direction", "Provider", "value", "components", "propTypes", "node", "oneOfType", "func", "object", "isRequired", "string"], "sources": ["C:/CMS/webapp/frontend/node_modules/@mui/system/esm/ThemeProvider/ThemeProvider.js"], "sourcesContent": ["'use client';\n\nimport _extends from \"@babel/runtime/helpers/esm/extends\";\nimport * as React from 'react';\nimport PropTypes from 'prop-types';\nimport { ThemeProvider as MuiThemeProvider, useTheme as usePrivateTheme } from '@mui/private-theming';\nimport exactProp from '@mui/utils/exactProp';\nimport { ThemeContext as StyledEngineThemeContext } from '@mui/styled-engine';\nimport useThemeWithoutDefault from '../useThemeWithoutDefault';\nimport RtlProvider from '../RtlProvider';\nimport DefaultPropsProvider from '../DefaultPropsProvider';\nimport { jsx as _jsx } from \"react/jsx-runtime\";\nconst EMPTY_THEME = {};\nfunction useThemeScoping(themeId, upperTheme, localTheme, isPrivate = false) {\n  return React.useMemo(() => {\n    const resolvedTheme = themeId ? upperTheme[themeId] || upperTheme : upperTheme;\n    if (typeof localTheme === 'function') {\n      const mergedTheme = localTheme(resolvedTheme);\n      const result = themeId ? _extends({}, upperTheme, {\n        [themeId]: mergedTheme\n      }) : mergedTheme;\n      // must return a function for the private theme to NOT merge with the upper theme.\n      // see the test case \"use provided theme from a callback\" in ThemeProvider.test.js\n      if (isPrivate) {\n        return () => result;\n      }\n      return result;\n    }\n    return themeId ? _extends({}, upperTheme, {\n      [themeId]: localTheme\n    }) : _extends({}, upperTheme, localTheme);\n  }, [themeId, upperTheme, localTheme, isPrivate]);\n}\n\n/**\n * This component makes the `theme` available down the React tree.\n * It should preferably be used at **the root of your component tree**.\n *\n * <ThemeProvider theme={theme}> // existing use case\n * <ThemeProvider theme={{ id: theme }}> // theme scoping\n */\nfunction ThemeProvider(props) {\n  const {\n    children,\n    theme: localTheme,\n    themeId\n  } = props;\n  const upperTheme = useThemeWithoutDefault(EMPTY_THEME);\n  const upperPrivateTheme = usePrivateTheme() || EMPTY_THEME;\n  if (process.env.NODE_ENV !== 'production') {\n    if (upperTheme === null && typeof localTheme === 'function' || themeId && upperTheme && !upperTheme[themeId] && typeof localTheme === 'function') {\n      console.error(['MUI: You are providing a theme function prop to the ThemeProvider component:', '<ThemeProvider theme={outerTheme => outerTheme} />', '', 'However, no outer theme is present.', 'Make sure a theme is already injected higher in the React tree ' + 'or provide a theme object.'].join('\\n'));\n    }\n  }\n  const engineTheme = useThemeScoping(themeId, upperTheme, localTheme);\n  const privateTheme = useThemeScoping(themeId, upperPrivateTheme, localTheme, true);\n  const rtlValue = engineTheme.direction === 'rtl';\n  return /*#__PURE__*/_jsx(MuiThemeProvider, {\n    theme: privateTheme,\n    children: /*#__PURE__*/_jsx(StyledEngineThemeContext.Provider, {\n      value: engineTheme,\n      children: /*#__PURE__*/_jsx(RtlProvider, {\n        value: rtlValue,\n        children: /*#__PURE__*/_jsx(DefaultPropsProvider, {\n          value: engineTheme == null ? void 0 : engineTheme.components,\n          children: children\n        })\n      })\n    })\n  });\n}\nprocess.env.NODE_ENV !== \"production\" ? ThemeProvider.propTypes /* remove-proptypes */ = {\n  // ┌────────────────────────────── Warning ──────────────────────────────┐\n  // │ These PropTypes are generated from the TypeScript type definitions. │\n  // │    To update them, edit the d.ts file and run `pnpm proptypes`.     │\n  // └─────────────────────────────────────────────────────────────────────┘\n  /**\n   * Your component tree.\n   */\n  children: PropTypes.node,\n  /**\n   * A theme object. You can provide a function to extend the outer theme.\n   */\n  theme: PropTypes.oneOfType([PropTypes.func, PropTypes.object]).isRequired,\n  /**\n   * The design system's unique id for getting the corresponded theme when there are multiple design systems.\n   */\n  themeId: PropTypes.string\n} : void 0;\nif (process.env.NODE_ENV !== 'production') {\n  process.env.NODE_ENV !== \"production\" ? ThemeProvider.propTypes = exactProp(ThemeProvider.propTypes) : void 0;\n}\nexport default ThemeProvider;"], "mappings": "AAAA,YAAY;;AAEZ,OAAOA,QAAQ,MAAM,oCAAoC;AACzD,OAAO,KAAKC,KAAK,MAAM,OAAO;AAC9B,OAAOC,SAAS,MAAM,YAAY;AAClC,SAASC,aAAa,IAAIC,gBAAgB,EAAEC,QAAQ,IAAIC,eAAe,QAAQ,sBAAsB;AACrG,OAAOC,SAAS,MAAM,sBAAsB;AAC5C,SAASC,YAAY,IAAIC,wBAAwB,QAAQ,oBAAoB;AAC7E,OAAOC,sBAAsB,MAAM,2BAA2B;AAC9D,OAAOC,WAAW,MAAM,gBAAgB;AACxC,OAAOC,oBAAoB,MAAM,yBAAyB;AAC1D,SAASC,GAAG,IAAIC,IAAI,QAAQ,mBAAmB;AAC/C,MAAMC,WAAW,GAAG,CAAC,CAAC;AACtB,SAASC,eAAeA,CAACC,OAAO,EAAEC,UAAU,EAAEC,UAAU,EAAEC,SAAS,GAAG,KAAK,EAAE;EAC3E,OAAOnB,KAAK,CAACoB,OAAO,CAAC,MAAM;IACzB,MAAMC,aAAa,GAAGL,OAAO,GAAGC,UAAU,CAACD,OAAO,CAAC,IAAIC,UAAU,GAAGA,UAAU;IAC9E,IAAI,OAAOC,UAAU,KAAK,UAAU,EAAE;MACpC,MAAMI,WAAW,GAAGJ,UAAU,CAACG,aAAa,CAAC;MAC7C,MAAME,MAAM,GAAGP,OAAO,GAAGjB,QAAQ,CAAC,CAAC,CAAC,EAAEkB,UAAU,EAAE;QAChD,CAACD,OAAO,GAAGM;MACb,CAAC,CAAC,GAAGA,WAAW;MAChB;MACA;MACA,IAAIH,SAAS,EAAE;QACb,OAAO,MAAMI,MAAM;MACrB;MACA,OAAOA,MAAM;IACf;IACA,OAAOP,OAAO,GAAGjB,QAAQ,CAAC,CAAC,CAAC,EAAEkB,UAAU,EAAE;MACxC,CAACD,OAAO,GAAGE;IACb,CAAC,CAAC,GAAGnB,QAAQ,CAAC,CAAC,CAAC,EAAEkB,UAAU,EAAEC,UAAU,CAAC;EAC3C,CAAC,EAAE,CAACF,OAAO,EAAEC,UAAU,EAAEC,UAAU,EAAEC,SAAS,CAAC,CAAC;AAClD;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASjB,aAAaA,CAACsB,KAAK,EAAE;EAC5B,MAAM;IACJC,QAAQ;IACRC,KAAK,EAAER,UAAU;IACjBF;EACF,CAAC,GAAGQ,KAAK;EACT,MAAMP,UAAU,GAAGR,sBAAsB,CAACK,WAAW,CAAC;EACtD,MAAMa,iBAAiB,GAAGtB,eAAe,CAAC,CAAC,IAAIS,WAAW;EAC1D,IAAIc,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,EAAE;IACzC,IAAIb,UAAU,KAAK,IAAI,IAAI,OAAOC,UAAU,KAAK,UAAU,IAAIF,OAAO,IAAIC,UAAU,IAAI,CAACA,UAAU,CAACD,OAAO,CAAC,IAAI,OAAOE,UAAU,KAAK,UAAU,EAAE;MAChJa,OAAO,CAACC,KAAK,CAAC,CAAC,8EAA8E,EAAE,oDAAoD,EAAE,EAAE,EAAE,qCAAqC,EAAE,iEAAiE,GAAG,4BAA4B,CAAC,CAACC,IAAI,CAAC,IAAI,CAAC,CAAC;IAC/S;EACF;EACA,MAAMC,WAAW,GAAGnB,eAAe,CAACC,OAAO,EAAEC,UAAU,EAAEC,UAAU,CAAC;EACpE,MAAMiB,YAAY,GAAGpB,eAAe,CAACC,OAAO,EAAEW,iBAAiB,EAAET,UAAU,EAAE,IAAI,CAAC;EAClF,MAAMkB,QAAQ,GAAGF,WAAW,CAACG,SAAS,KAAK,KAAK;EAChD,OAAO,aAAaxB,IAAI,CAACV,gBAAgB,EAAE;IACzCuB,KAAK,EAAES,YAAY;IACnBV,QAAQ,EAAE,aAAaZ,IAAI,CAACL,wBAAwB,CAAC8B,QAAQ,EAAE;MAC7DC,KAAK,EAAEL,WAAW;MAClBT,QAAQ,EAAE,aAAaZ,IAAI,CAACH,WAAW,EAAE;QACvC6B,KAAK,EAAEH,QAAQ;QACfX,QAAQ,EAAE,aAAaZ,IAAI,CAACF,oBAAoB,EAAE;UAChD4B,KAAK,EAAEL,WAAW,IAAI,IAAI,GAAG,KAAK,CAAC,GAAGA,WAAW,CAACM,UAAU;UAC5Df,QAAQ,EAAEA;QACZ,CAAC;MACH,CAAC;IACH,CAAC;EACH,CAAC,CAAC;AACJ;AACAG,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,GAAG5B,aAAa,CAACuC,SAAS,CAAC,yBAAyB;EACvF;EACA;EACA;EACA;EACA;AACF;AACA;EACEhB,QAAQ,EAAExB,SAAS,CAACyC,IAAI;EACxB;AACF;AACA;EACEhB,KAAK,EAAEzB,SAAS,CAAC0C,SAAS,CAAC,CAAC1C,SAAS,CAAC2C,IAAI,EAAE3C,SAAS,CAAC4C,MAAM,CAAC,CAAC,CAACC,UAAU;EACzE;AACF;AACA;EACE9B,OAAO,EAAEf,SAAS,CAAC8C;AACrB,CAAC,GAAG,KAAK,CAAC;AACV,IAAInB,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,EAAE;EACzCF,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,GAAG5B,aAAa,CAACuC,SAAS,GAAGnC,SAAS,CAACJ,aAAa,CAACuC,SAAS,CAAC,GAAG,KAAK,CAAC;AAC/G;AACA,eAAevC,aAAa", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}