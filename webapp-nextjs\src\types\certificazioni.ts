// Tipi per il sistema di certificazione CABLYS

export interface CertificazioneCavo {
  id_certificazione: number
  id_cantiere: number
  id_cavo: string
  numero_certificato: string
  data_certificazione: string
  id_operatore?: number
  strumento_utilizzato?: string
  id_strumento?: number
  lunghezza_misurata?: number
  valore_continuita?: string
  valore_isolamento?: string
  valore_resistenza?: string
  note?: string
  timestamp_creazione: string
  timestamp_modifica: string
  
  // Campi CEI 64-8
  id_rapporto?: number
  tipo_certificato?: 'SINGOLO' | 'GRUPPO'
  stato_certificato?: 'CONFORME' | 'NON_CONFORME' | 'BOZZA' | 'CONFORME_CON_RISERVA'
  designazione_funzionale?: string
  tensione_nominale?: string
  tensione_prova_isolamento?: number
  durata_prova_isolamento?: number
  valore_minimo_isolamento?: number
  temperatura_prova?: number
  umidita_prova?: number
  esito_complessivo?: string
  
  // Relazioni
  cavo?: any
  operatore?: any
  strumento?: StrumentoCertificato
  rapporto?: RapportoGeneraleCollaudo
  prove_dettagliate?: ProvaDettagliata[]
  non_conformita?: NonConformita[]
}

export interface CertificazioneCavoCreate {
  id_cavo: string
  id_operatore?: number
  strumento_utilizzato?: string
  id_strumento?: number
  lunghezza_misurata?: number
  valore_continuita?: string
  valore_isolamento?: string
  valore_resistenza?: string
  note?: string
  
  // Campi CEI 64-8
  tipo_certificato?: 'SINGOLO' | 'GRUPPO'
  stato_certificato?: 'CONFORME' | 'NON_CONFORME' | 'BOZZA'
  designazione_funzionale?: string
  tensione_nominale?: string
  tensione_prova_isolamento?: number
  durata_prova_isolamento?: number
  valore_minimo_isolamento?: number
  temperatura_prova?: number
  umidita_prova?: number
  esito_complessivo?: string
}

export interface StrumentoCertificato {
  id_strumento: number
  id_cantiere: number
  nome: string
  marca: string
  modello: string
  numero_serie: string
  data_calibrazione: string
  data_scadenza_calibrazione: string
  note?: string
  timestamp_creazione: string
  timestamp_modifica: string
  
  // Campi CEI 64-8
  tipo_strumento?: 'MEGGER' | 'MULTIMETRO' | 'OSCILLOSCOPIO' | 'ALTRO'
  ente_certificatore?: string
  numero_certificato_calibrazione?: string
  range_misura?: string
  precisione?: string
  stato_strumento?: 'ATTIVO' | 'SCADUTO' | 'FUORI_SERVIZIO'
}

export interface StrumentoCertificatoCreate {
  nome: string
  marca: string
  modello: string
  numero_serie: string
  data_calibrazione: string
  data_scadenza_calibrazione: string
  note?: string
  tipo_strumento?: 'MEGGER' | 'MULTIMETRO' | 'OSCILLOSCOPIO' | 'ALTRO'
  ente_certificatore?: string
  numero_certificato_calibrazione?: string
  range_misura?: string
  precisione?: string
  stato_strumento?: 'ATTIVO' | 'SCADUTO' | 'FUORI_SERVIZIO'
}

export interface RapportoGeneraleCollaudo {
  id_rapporto: number
  id_cantiere: number
  numero_rapporto: string
  data_rapporto: string
  id_responsabile_tecnico?: number
  nome_progetto?: string
  cliente_finale?: string
  societa_installatrice?: string
  data_inizio_collaudo?: string
  data_fine_collaudo?: string
  condizioni_ambientali?: string
  temperatura_ambiente?: number
  umidita_relativa?: number
  numero_cavi_totali: number
  numero_cavi_conformi: number
  numero_cavi_non_conformi: number
  numero_cavi_con_riserva: number
  stato_rapporto: 'BOZZA' | 'COMPLETATO' | 'APPROVATO'
  dichiarazione_conformita: boolean
  note?: string
  timestamp_creazione: string
  timestamp_modifica: string
  
  // Relazioni
  certificazioni?: CertificazioneCavo[]
  responsabile_tecnico?: any
}

export interface RapportoGeneraleCollaudoCreate {
  id_responsabile_tecnico?: number
  nome_progetto?: string
  cliente_finale?: string
  societa_installatrice?: string
  data_inizio_collaudo?: string
  data_fine_collaudo?: string
  condizioni_ambientali?: string
  temperatura_ambiente?: number
  umidita_relativa?: number
  dichiarazione_conformita?: boolean
  note?: string
}

export interface ProvaDettagliata {
  id_prova: number
  id_certificazione: number
  tipo_prova: 'CONTINUITA' | 'ISOLAMENTO' | 'RESISTENZA' | 'CAPACITA' | 'ALTRO'
  descrizione_prova?: string
  risultati?: Record<string, any>
  valori_misurati?: Record<string, any>
  valori_attesi?: Record<string, any>
  esito: 'CONFORME' | 'NON_CONFORME'
  note?: string
  timestamp_creazione: string
  timestamp_modifica: string
}

export interface ProvaDettagliataCreate {
  tipo_prova: 'CONTINUITA' | 'ISOLAMENTO' | 'RESISTENZA' | 'CAPACITA' | 'ALTRO'
  descrizione_prova?: string
  risultati?: Record<string, any>
  valori_misurati?: Record<string, any>
  valori_attesi?: Record<string, any>
  esito: 'CONFORME' | 'NON_CONFORME'
  note?: string
}

export interface NonConformita {
  id_nc: number
  id_certificazione?: number
  id_cantiere: number
  codice_nc: string
  tipo_nc: 'CRITICA' | 'MAGGIORE' | 'MINORE'
  descrizione: string
  azione_correttiva?: string
  data_rilevazione: string
  data_chiusura?: string
  stato_nc: 'APERTA' | 'IN_CORSO' | 'CHIUSA'
  responsabile_nc?: string
  note?: string
  timestamp_creazione: string
  timestamp_modifica: string
}

export interface NonConformitaCreate {
  id_certificazione?: number
  tipo_nc: 'CRITICA' | 'MAGGIORE' | 'MINORE'
  descrizione: string
  azione_correttiva?: string
  responsabile_nc?: string
  note?: string
}

// Tipi per statistiche e report
export interface StatisticheCertificazioni {
  totale_certificazioni: number
  certificazioni_conformi: number
  certificazioni_non_conformi: number
  certificazioni_con_riserva: number
  certificazioni_bozza: number
  percentuale_conformita: number
  media_isolamento: number
  cavi_non_certificati: number
  strumenti_scaduti: number
  non_conformita_aperte: number
}

export interface FiltriCertificazioni {
  stato_certificato?: string
  data_da?: string
  data_a?: string
  id_operatore?: number
  id_strumento?: number
  valore_isolamento_min?: number
  valore_isolamento_max?: number
  solo_non_conformi?: boolean
}

// Tipi per workflow e validazioni
export interface ValidazioneCertificazione {
  valida: boolean
  errori: string[]
  avvertimenti: string[]
  prerequisiti_soddisfatti: boolean
  cavo_installato: boolean
  cavo_collegato: boolean
  strumento_valido: boolean
}

export interface WorkflowCertificazione {
  step: 'VERIFICA_PREREQUISITI' | 'COLLEGAMENTO' | 'INSERIMENTO_DATI' | 'VALIDAZIONE' | 'GENERAZIONE_PDF'
  completato: boolean
  errore?: string
  dati?: any
}
