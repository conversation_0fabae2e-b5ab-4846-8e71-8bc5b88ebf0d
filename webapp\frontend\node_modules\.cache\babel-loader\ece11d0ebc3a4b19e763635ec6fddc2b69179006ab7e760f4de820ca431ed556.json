{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\PycharmProjects\\\\CMS\\\\webapp\\\\frontend\\\\src\\\\components\\\\cavi\\\\AggiungiCavoForm.js\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect } from 'react';\nimport { Box, TextField, Button, Grid, FormControl, InputLabel, Select, MenuItem, FormHelperText, Alert, CircularProgress, Typography, Paper } from '@mui/material';\nimport { Save as SaveIcon, Warning as WarningIcon } from '@mui/icons-material';\nimport { useNavigate } from 'react-router-dom';\nimport axios from 'axios';\nimport config from '../../config';\nimport caviService from '../../services/caviService';\nimport { validateCavoData, validateField, isEmpty } from '../../utils/validationUtils';\nimport { redirectToVisualizzaCavi } from '../../utils/navigationUtils';\nimport { jsxDEV as _jsxDEV, Fragment as _Fragment } from \"react/jsx-dev-runtime\";\nconst AggiungiCavoForm = ({\n  cantiereId,\n  onSuccess,\n  onError,\n  isDialog = false\n}) => {\n  _s();\n  const navigate = useNavigate();\n  const [loading, setLoading] = useState(false);\n  const [loadingRevisione, setLoadingRevisione] = useState(true);\n  const [formData, setFormData] = useState({\n    id_cavo: '',\n    revisione_ufficiale: '',\n    sistema: '',\n    utility: '',\n    colore_cavo: '',\n    tipologia: '',\n    n_conduttori: '',\n    sezione: '',\n    sh: '',\n    ubicazione_partenza: '',\n    utenza_partenza: '',\n    descrizione_utenza_partenza: '',\n    ubicazione_arrivo: '',\n    utenza_arrivo: '',\n    descrizione_utenza_arrivo: '',\n    metri_teorici: '',\n    metratura_reale: '0',\n    responsabile_posa: '',\n    id_bobina: '',\n    stato_installazione: 'Da installare'\n  });\n  const [formErrors, setFormErrors] = useState({});\n  const [formWarnings, setFormWarnings] = useState({});\n\n  // Carica la revisione corrente all'avvio\n  useEffect(() => {\n    const loadRevisioneCorrente = async () => {\n      try {\n        setLoadingRevisione(true);\n        const revisione = await caviService.getRevisioneCorrente(cantiereId);\n        setFormData(prev => ({\n          ...prev,\n          revisione_ufficiale: revisione\n        }));\n      } catch (error) {\n        console.error('Errore nel caricamento della revisione corrente:', error);\n        onError('Errore nel caricamento della revisione corrente');\n      } finally {\n        setLoadingRevisione(false);\n      }\n    };\n    loadRevisioneCorrente();\n  }, [cantiereId, onError]);\n\n  // Gestisce il cambio dei valori nel form con validazione\n  const handleFormChange = e => {\n    const {\n      name,\n      value\n    } = e.target;\n\n    // Aggiorna il valore nel form\n    setFormData({\n      ...formData,\n      [name]: value\n    });\n\n    // Valida il campo\n    const additionalParams = {};\n    if (name === 'metratura_reale') {\n      additionalParams.metriTeorici = parseFloat(formData.metri_teorici || 0);\n    }\n    const result = validateField(name, value, additionalParams);\n\n    // Aggiorna gli errori\n    setFormErrors(prev => ({\n      ...prev,\n      [name]: !result.valid ? result.message : null\n    }));\n\n    // Aggiorna gli avvisi\n    setFormWarnings(prev => ({\n      ...prev,\n      [name]: result.warning ? result.message : null\n    }));\n  };\n\n  // Gestisce l'annullamento dell'operazione\n  const handleCancel = () => {\n    if (isDialog) {\n      // Se è in un dialog, non fare nulla (il dialog ha il suo pulsante di annullamento)\n      return;\n    }\n    // Reindirizza alla visualizzazione dei cavi\n    redirectToVisualizzaCavi(navigate);\n  };\n\n  // Gestisce l'invio del form\n  const handleSubmit = async e => {\n    e.preventDefault();\n    setLoading(true);\n    try {\n      // Validazione completa dei dati del cavo\n      console.log('Dati del form prima della validazione:', formData);\n      const validation = validateCavoData(formData);\n      console.log('Risultato validazione:', validation);\n      if (!validation.isValid) {\n        console.error('Errori di validazione:', validation.errors);\n        setFormErrors(validation.errors);\n        setFormWarnings(validation.warnings);\n        setLoading(false);\n        onError('Ci sono errori nel form. Controlla i campi evidenziati in rosso.');\n        return;\n      }\n\n      // Usa i dati validati\n      const validatedData = validation.validatedData;\n\n      // Converti l'ID cavo in maiuscolo\n      validatedData.id_cavo = validatedData.id_cavo.toUpperCase();\n\n      // Assicurati che i campi obbligatori siano presenti (basati sulla definizione della tabella)\n      // Campi obbligatori: id_cavo, id_cantiere, utility, tipologia, n_conduttori, sezione, metri_teorici,\n      // ubicazione_partenza, ubicazione_arrivo, stato_installazione\n\n      // Verifica che i campi obbligatori siano presenti\n      const requiredFields = ['id_cavo', 'utility', 'tipologia', 'n_conduttori', 'sezione', 'metri_teorici', 'ubicazione_partenza', 'ubicazione_arrivo', 'stato_installazione'];\n      const missingFields = requiredFields.filter(field => !validatedData[field]);\n      if (missingFields.length > 0) {\n        throw new Error(`Campi obbligatori mancanti: ${missingFields.join(', ')}`);\n      }\n\n      // Prepara i dati da inviare\n      const dataToSend = {\n        ...validatedData,\n        // Assicurati che i campi obbligatori siano presenti\n        id_cavo: validatedData.id_cavo.toUpperCase(),\n        utility: validatedData.utility,\n        tipologia: validatedData.tipologia,\n        n_conduttori: validatedData.n_conduttori ? validatedData.n_conduttori.toString() : \"0\",\n        // Invia come stringa\n        sezione: validatedData.sezione ? validatedData.sezione.toString() : \"0\",\n        // Invia come stringa\n        metri_teorici: parseFloat(validatedData.metri_teorici) || 0,\n        ubicazione_partenza: validatedData.ubicazione_partenza,\n        ubicazione_arrivo: validatedData.ubicazione_arrivo,\n        stato_installazione: validatedData.stato_installazione || 'Da installare',\n        // Campi opzionali\n        metratura_reale: validatedData.metratura_reale ? parseFloat(validatedData.metratura_reale) : null,\n        id_bobina: validatedData.id_bobina || null,\n        // Altri campi che potrebbero essere utili\n        sistema: validatedData.sistema || null,\n        colore_cavo: validatedData.colore_cavo || null,\n        utenza_partenza: validatedData.utenza_partenza || null,\n        utenza_arrivo: validatedData.utenza_arrivo || null,\n        descrizione_utenza_partenza: validatedData.descrizione_utenza_partenza || null,\n        descrizione_utenza_arrivo: validatedData.descrizione_utenza_arrivo || null,\n        sh: validatedData.sh || 'N',\n        responsabile_posa: validatedData.responsabile_posa || null,\n        note: validatedData.note || null\n      };\n      console.log('Dati da inviare al server dopo la validazione:', dataToSend);\n      try {\n        // Invia i dati al server\n        console.log('Tentativo di invio dati al server...');\n\n        // Verifica che cantiereId sia valido\n        if (!cantiereId) {\n          throw new Error('ID cantiere non valido o mancante');\n        }\n\n        // Usa axios direttamente per avere più controllo\n        const token = localStorage.getItem('token');\n        if (!token) {\n          throw new Error('Token di autenticazione mancante. Effettua nuovamente il login.');\n        }\n        console.log(`Invio richiesta POST a ${config.API_URL}/cavi/${cantiereId}`);\n        console.log('Dati inviati:', JSON.stringify(dataToSend, null, 2));\n        try {\n          // Tenta di inviare la richiesta\n          const response = await axios.post(`${config.API_URL}/cavi/${cantiereId}`, dataToSend, {\n            headers: {\n              'Content-Type': 'application/json',\n              'Authorization': `Bearer ${token}`\n            },\n            timeout: 30000 // Aumentato a 30 secondi per dare più tempo al server\n          });\n          console.log('Risposta dal server:', response.data);\n\n          // Imposta loading a false prima di chiudere il dialog\n          setLoading(false);\n\n          // Notifica il successo per chiudere il dialog o reindirizzare\n          onSuccess('Cavo aggiunto con successo');\n\n          // Reindirizza solo se non è in un dialog\n          if (!isDialog) {\n            console.log('Reindirizzamento a visualizza cavi...');\n            try {\n              // Usa navigate invece di window.location per un reindirizzamento più pulito\n              redirectToVisualizzaCavi(navigate);\n            } catch (navError) {\n              console.error('Errore durante il reindirizzamento:', navError);\n              // Fallback: usa window.location solo se navigate fallisce\n              window.location.href = `/dashboard/cantieri/${cantiereId}/cavi/visualizza`;\n            }\n          }\n        } catch (error) {\n          console.error('Errore durante l\\'invio dei dati al server:', error);\n\n          // Se è un errore di rete, verifica se il cavo è stato inserito senza mostrare subito un errore\n          if (!error.response || error.code === 'ECONNABORTED' || error.message.includes('Network Error')) {\n            // Manteniamo lo stato di caricamento attivo senza mostrare errori\n            console.log('Rilevato possibile errore di rete, verifica in corso...');\n\n            // Attendi un secondo e poi verifica se il cavo è stato inserito\n            setTimeout(async () => {\n              try {\n                // Verifica se il cavo esiste\n                const checkResponse = await axios.get(`${config.API_URL}/cavi/${cantiereId}/check/${dataToSend.id_cavo}`, {\n                  headers: {\n                    'Authorization': `Bearer ${token}`\n                  },\n                  timeout: 5000\n                });\n                if (checkResponse.data && checkResponse.data.exists) {\n                  // Il cavo è stato inserito con successo\n\n                  // Imposta loading a false prima di chiudere il dialog\n                  setLoading(false);\n\n                  // Notifica il successo per chiudere il dialog o reindirizzare\n                  onSuccess('Cavo aggiunto con successo');\n\n                  // Reindirizza solo se non è in un dialog\n                  if (!isDialog) {\n                    console.log('Reindirizzamento a visualizza cavi...');\n                    try {\n                      // Usa navigate invece di window.location per un reindirizzamento più pulito\n                      redirectToVisualizzaCavi(navigate);\n                    } catch (navError) {\n                      console.error('Errore durante il reindirizzamento:', navError);\n                      // Fallback: usa window.location solo se navigate fallisce\n                      window.location.href = `/dashboard/cantieri/${cantiereId}/cavi/visualizza`;\n                    }\n                  }\n                } else {\n                  // Il cavo non è stato inserito, ora mostriamo l'errore\n                  onError('Il cavo non è stato inserito a causa di un problema di connessione. Riprova.');\n                  setLoading(false);\n                }\n              } catch (checkError) {\n                console.error('Errore durante la verifica:', checkError);\n                onError('Impossibile verificare se il cavo è stato inserito. Riprova.');\n                setLoading(false);\n              }\n            }, 1000);\n          } else {\n            // Per altri tipi di errori, mostra il messaggio di errore\n            let errorMessage = 'Errore durante l\\'aggiunta del cavo';\n            if (error.response && error.response.data) {\n              const responseData = error.response.data;\n              if (responseData.detail) {\n                errorMessage = responseData.detail;\n              } else if (typeof responseData === 'string') {\n                errorMessage = responseData;\n              } else {\n                errorMessage = JSON.stringify(responseData);\n              }\n            } else if (error.message) {\n              errorMessage = error.message;\n            }\n            onError(errorMessage);\n            setLoading(false);\n          }\n        }\n      } catch (error) {\n        console.error('Errore durante la preparazione della richiesta:', error);\n        let errorMessage = error.message || 'Errore durante l\\'aggiunta del cavo';\n        onError(errorMessage);\n        setLoading(false);\n      }\n    } catch (error) {\n      console.error('Errore durante l\\'aggiunta del cavo:', error);\n      onError(error.detail || 'Errore durante l\\'aggiunta del cavo');\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  // Mostra un avviso se ci sono warning\n  const hasWarnings = Object.keys(formWarnings).length > 0;\n  return /*#__PURE__*/_jsxDEV(Box, {\n    component: \"form\",\n    onSubmit: handleSubmit,\n    noValidate: true,\n    children: loadingRevisione ? /*#__PURE__*/_jsxDEV(Box, {\n      sx: {\n        display: 'flex',\n        justifyContent: 'center',\n        my: 4\n      },\n      children: /*#__PURE__*/_jsxDEV(CircularProgress, {}, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 329,\n        columnNumber: 11\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 328,\n      columnNumber: 9\n    }, this) : /*#__PURE__*/_jsxDEV(_Fragment, {\n      children: [hasWarnings && /*#__PURE__*/_jsxDEV(_Fragment, {\n        children: [/*#__PURE__*/_jsxDEV(Alert, {\n          severity: \"warning\",\n          icon: /*#__PURE__*/_jsxDEV(WarningIcon, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 337,\n            columnNumber: 23\n          }, this),\n          sx: {\n            mb: isDialog ? 1 : 2,\n            py: isDialog ? 0.5 : 1\n          },\n          children: /*#__PURE__*/_jsxDEV(Typography, {\n            variant: \"subtitle2\",\n            sx: {\n              fontSize: isDialog ? '0.8rem' : '0.875rem'\n            },\n            children: \"Ci sono alcuni avvisi nel form. Puoi procedere, ma verifica i campi evidenziati.\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 340,\n            columnNumber: 17\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 335,\n          columnNumber: 15\n        }, this), formWarnings.network_error && /*#__PURE__*/_jsxDEV(Alert, {\n          severity: \"error\",\n          sx: {\n            mb: isDialog ? 2 : 3,\n            py: isDialog ? 0.5 : 1\n          },\n          children: /*#__PURE__*/_jsxDEV(Typography, {\n            variant: \"subtitle2\",\n            sx: {\n              fontWeight: 'bold'\n            },\n            children: formWarnings.network_error\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 351,\n            columnNumber: 19\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 347,\n          columnNumber: 17\n        }, this)]\n      }, void 0, true), /*#__PURE__*/_jsxDEV(Paper, {\n        sx: {\n          p: isDialog ? 1 : 3,\n          mb: isDialog ? 1 : 3,\n          boxShadow: isDialog ? 0 : 1\n        },\n        children: [/*#__PURE__*/_jsxDEV(Typography, {\n          variant: \"h6\",\n          gutterBottom: true,\n          sx: {\n            fontSize: isDialog ? '0.9rem' : '1.25rem',\n            mb: isDialog ? 0.5 : 1\n          },\n          children: \"Informazioni Generali\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 360,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(Grid, {\n          container: true,\n          spacing: isDialog ? 1 : 2,\n          children: [/*#__PURE__*/_jsxDEV(Grid, {\n            item: true,\n            xs: 12,\n            sm: 6,\n            children: /*#__PURE__*/_jsxDEV(TextField, {\n              name: \"id_cavo\",\n              label: \"ID Cavo\",\n              fullWidth: true,\n              variant: \"outlined\",\n              value: formData.id_cavo,\n              onChange: handleFormChange,\n              required: true,\n              error: !!formErrors.id_cavo,\n              helperText: formErrors.id_cavo,\n              inputProps: {\n                style: {\n                  textTransform: 'uppercase'\n                }\n              }\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 365,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 364,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(Grid, {\n            item: true,\n            xs: 12,\n            sm: 6,\n            children: /*#__PURE__*/_jsxDEV(TextField, {\n              name: \"revisione_ufficiale\",\n              label: \"Revisione Ufficiale\",\n              fullWidth: true,\n              variant: \"outlined\",\n              value: formData.revisione_ufficiale,\n              onChange: handleFormChange,\n              required: true,\n              error: !!formErrors.revisione_ufficiale,\n              helperText: formErrors.revisione_ufficiale\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 379,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 378,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(Grid, {\n            item: true,\n            xs: 12,\n            sm: 6,\n            children: /*#__PURE__*/_jsxDEV(TextField, {\n              name: \"sistema\",\n              label: \"Sistema\",\n              fullWidth: true,\n              variant: \"outlined\",\n              value: formData.sistema,\n              onChange: handleFormChange,\n              error: !!formErrors.sistema,\n              helperText: formErrors.sistema\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 392,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 391,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(Grid, {\n            item: true,\n            xs: 12,\n            sm: 6,\n            children: /*#__PURE__*/_jsxDEV(TextField, {\n              name: \"utility\",\n              label: \"Utility\",\n              fullWidth: true,\n              variant: \"outlined\",\n              value: formData.utility,\n              onChange: handleFormChange,\n              error: !!formErrors.utility,\n              helperText: formErrors.utility\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 404,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 403,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 363,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 359,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(Paper, {\n        sx: {\n          p: isDialog ? 1 : 3,\n          mb: isDialog ? 1 : 3,\n          boxShadow: isDialog ? 0 : 1\n        },\n        children: [/*#__PURE__*/_jsxDEV(Typography, {\n          variant: \"h6\",\n          gutterBottom: true,\n          sx: {\n            fontSize: isDialog ? '0.9rem' : '1.25rem',\n            mb: isDialog ? 0.5 : 1\n          },\n          children: \"Caratteristiche Tecniche\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 419,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(Grid, {\n          container: true,\n          spacing: isDialog ? 1 : 2,\n          children: [/*#__PURE__*/_jsxDEV(Grid, {\n            item: true,\n            xs: 12,\n            sm: 6,\n            children: /*#__PURE__*/_jsxDEV(TextField, {\n              name: \"colore_cavo\",\n              label: \"Colore Cavo\",\n              fullWidth: true,\n              variant: \"outlined\",\n              value: formData.colore_cavo,\n              onChange: handleFormChange,\n              error: !!formErrors.colore_cavo,\n              helperText: formErrors.colore_cavo\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 424,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 423,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(Grid, {\n            item: true,\n            xs: 12,\n            sm: 6,\n            children: /*#__PURE__*/_jsxDEV(TextField, {\n              name: \"tipologia\",\n              label: \"Tipologia\",\n              fullWidth: true,\n              variant: \"outlined\",\n              value: formData.tipologia,\n              onChange: handleFormChange,\n              error: !!formErrors.tipologia,\n              helperText: formErrors.tipologia\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 436,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 435,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(Grid, {\n            item: true,\n            xs: 12,\n            sm: 4,\n            children: /*#__PURE__*/_jsxDEV(TextField, {\n              name: \"n_conduttori\",\n              label: \"Numero Conduttori\",\n              fullWidth: true,\n              variant: \"outlined\",\n              value: formData.n_conduttori,\n              onChange: handleFormChange,\n              error: !!formErrors.n_conduttori,\n              helperText: formErrors.n_conduttori || formWarnings.n_conduttori,\n              FormHelperTextProps: {\n                style: {\n                  color: formWarnings.n_conduttori ? 'orange' : undefined\n                }\n              }\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 448,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 447,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(Grid, {\n            item: true,\n            xs: 12,\n            sm: 4,\n            children: /*#__PURE__*/_jsxDEV(TextField, {\n              name: \"sezione\",\n              label: \"Sezione\",\n              fullWidth: true,\n              variant: \"outlined\",\n              value: formData.sezione,\n              onChange: handleFormChange,\n              error: !!formErrors.sezione,\n              helperText: formErrors.sezione || formWarnings.sezione,\n              FormHelperTextProps: {\n                style: {\n                  color: formWarnings.sezione ? 'orange' : undefined\n                }\n              }\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 463,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 462,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(Grid, {\n            item: true,\n            xs: 12,\n            sm: 4,\n            children: /*#__PURE__*/_jsxDEV(FormControl, {\n              fullWidth: true,\n              error: !!formErrors.sh,\n              children: [/*#__PURE__*/_jsxDEV(InputLabel, {\n                id: \"sh-label\",\n                children: \"Schermato (S/N)\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 479,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(Select, {\n                labelId: \"sh-label\",\n                name: \"sh\",\n                value: formData.sh,\n                label: \"Schermato (S/N)\",\n                onChange: handleFormChange,\n                children: [/*#__PURE__*/_jsxDEV(MenuItem, {\n                  value: \"S\",\n                  children: \"S\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 487,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(MenuItem, {\n                  value: \"N\",\n                  children: \"N\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 488,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 480,\n                columnNumber: 19\n              }, this), formErrors.sh && /*#__PURE__*/_jsxDEV(FormHelperText, {\n                children: formErrors.sh\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 490,\n                columnNumber: 37\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 478,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 477,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 422,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 418,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(Paper, {\n        sx: {\n          p: isDialog ? 1 : 3,\n          mb: isDialog ? 1 : 3,\n          boxShadow: isDialog ? 0 : 1\n        },\n        children: [/*#__PURE__*/_jsxDEV(Typography, {\n          variant: \"h6\",\n          gutterBottom: true,\n          sx: {\n            fontSize: isDialog ? '0.9rem' : '1.25rem',\n            mb: isDialog ? 0.5 : 1\n          },\n          children: \"Ubicazione Partenza\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 497,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(Grid, {\n          container: true,\n          spacing: isDialog ? 1 : 2,\n          children: [/*#__PURE__*/_jsxDEV(Grid, {\n            item: true,\n            xs: 12,\n            sm: 4,\n            children: /*#__PURE__*/_jsxDEV(TextField, {\n              name: \"ubicazione_partenza\",\n              label: \"Ubicazione Partenza\",\n              fullWidth: true,\n              variant: \"outlined\",\n              value: formData.ubicazione_partenza,\n              onChange: handleFormChange,\n              error: !!formErrors.ubicazione_partenza,\n              helperText: formErrors.ubicazione_partenza\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 502,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 501,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(Grid, {\n            item: true,\n            xs: 12,\n            sm: 4,\n            children: /*#__PURE__*/_jsxDEV(TextField, {\n              name: \"utenza_partenza\",\n              label: \"Utenza Partenza\",\n              fullWidth: true,\n              variant: \"outlined\",\n              value: formData.utenza_partenza,\n              onChange: handleFormChange,\n              error: !!formErrors.utenza_partenza,\n              helperText: formErrors.utenza_partenza\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 514,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 513,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(Grid, {\n            item: true,\n            xs: 12,\n            sm: 4,\n            children: /*#__PURE__*/_jsxDEV(TextField, {\n              name: \"descrizione_utenza_partenza\",\n              label: \"Descrizione Utenza Partenza\",\n              fullWidth: true,\n              variant: \"outlined\",\n              value: formData.descrizione_utenza_partenza,\n              onChange: handleFormChange,\n              error: !!formErrors.descrizione_utenza_partenza,\n              helperText: formErrors.descrizione_utenza_partenza\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 526,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 525,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 500,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 496,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(Paper, {\n        sx: {\n          p: isDialog ? 1 : 3,\n          mb: isDialog ? 1 : 3,\n          boxShadow: isDialog ? 0 : 1\n        },\n        children: [/*#__PURE__*/_jsxDEV(Typography, {\n          variant: \"h6\",\n          gutterBottom: true,\n          sx: {\n            fontSize: isDialog ? '0.9rem' : '1.25rem',\n            mb: isDialog ? 0.5 : 1\n          },\n          children: \"Ubicazione Arrivo\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 541,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(Grid, {\n          container: true,\n          spacing: isDialog ? 1 : 2,\n          children: [/*#__PURE__*/_jsxDEV(Grid, {\n            item: true,\n            xs: 12,\n            sm: 4,\n            children: /*#__PURE__*/_jsxDEV(TextField, {\n              name: \"ubicazione_arrivo\",\n              label: \"Ubicazione Arrivo\",\n              fullWidth: true,\n              variant: \"outlined\",\n              value: formData.ubicazione_arrivo,\n              onChange: handleFormChange,\n              error: !!formErrors.ubicazione_arrivo,\n              helperText: formErrors.ubicazione_arrivo\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 546,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 545,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(Grid, {\n            item: true,\n            xs: 12,\n            sm: 4,\n            children: /*#__PURE__*/_jsxDEV(TextField, {\n              name: \"utenza_arrivo\",\n              label: \"Utenza Arrivo\",\n              fullWidth: true,\n              variant: \"outlined\",\n              value: formData.utenza_arrivo,\n              onChange: handleFormChange,\n              error: !!formErrors.utenza_arrivo,\n              helperText: formErrors.utenza_arrivo\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 558,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 557,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(Grid, {\n            item: true,\n            xs: 12,\n            sm: 4,\n            children: /*#__PURE__*/_jsxDEV(TextField, {\n              name: \"descrizione_utenza_arrivo\",\n              label: \"Descrizione Utenza Arrivo\",\n              fullWidth: true,\n              variant: \"outlined\",\n              value: formData.descrizione_utenza_arrivo,\n              onChange: handleFormChange,\n              error: !!formErrors.descrizione_utenza_arrivo,\n              helperText: formErrors.descrizione_utenza_arrivo\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 570,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 569,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 544,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 540,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(Paper, {\n        sx: {\n          p: isDialog ? 2 : 3,\n          mb: isDialog ? 2 : 3,\n          boxShadow: isDialog ? 0 : 1\n        },\n        children: [/*#__PURE__*/_jsxDEV(Typography, {\n          variant: \"h6\",\n          gutterBottom: true,\n          sx: {\n            fontSize: isDialog ? '1.1rem' : '1.25rem'\n          },\n          children: \"Metratura\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 585,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(Grid, {\n          container: true,\n          spacing: isDialog ? 1 : 2,\n          children: /*#__PURE__*/_jsxDEV(Grid, {\n            item: true,\n            xs: 12,\n            sm: 6,\n            children: /*#__PURE__*/_jsxDEV(TextField, {\n              name: \"metri_teorici\",\n              label: \"Metri Teorici\",\n              fullWidth: true,\n              variant: \"outlined\",\n              value: formData.metri_teorici,\n              onChange: handleFormChange,\n              required: true,\n              error: !!formErrors.metri_teorici,\n              helperText: formErrors.metri_teorici\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 590,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 589,\n            columnNumber: 15\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 588,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 584,\n        columnNumber: 11\n      }, this), !isDialog && /*#__PURE__*/_jsxDEV(Box, {\n        sx: {\n          mt: 3,\n          display: 'flex',\n          justifyContent: 'center',\n          gap: 2\n        },\n        children: [/*#__PURE__*/_jsxDEV(Button, {\n          variant: \"outlined\",\n          color: \"secondary\",\n          size: \"large\",\n          onClick: handleCancel,\n          disabled: loading,\n          sx: {\n            minWidth: 150\n          },\n          children: \"Annulla\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 608,\n          columnNumber: 15\n        }, this), /*#__PURE__*/_jsxDEV(Button, {\n          type: \"submit\",\n          variant: \"contained\",\n          color: \"primary\",\n          size: \"large\",\n          startIcon: /*#__PURE__*/_jsxDEV(SaveIcon, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 623,\n            columnNumber: 28\n          }, this),\n          disabled: loading,\n          sx: {\n            minWidth: 150\n          },\n          children: loading ? /*#__PURE__*/_jsxDEV(CircularProgress, {\n            size: 24\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 627,\n            columnNumber: 28\n          }, this) : 'Salva Cavo'\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 618,\n          columnNumber: 15\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 607,\n        columnNumber: 13\n      }, this), isDialog && /*#__PURE__*/_jsxDEV(Box, {\n        sx: {\n          mt: 2,\n          display: 'flex',\n          justifyContent: 'flex-end',\n          gap: 2\n        },\n        children: /*#__PURE__*/_jsxDEV(Button, {\n          type: \"submit\",\n          variant: \"contained\",\n          color: \"primary\",\n          size: \"medium\",\n          startIcon: /*#__PURE__*/_jsxDEV(SaveIcon, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 638,\n            columnNumber: 28\n          }, this),\n          disabled: loading,\n          sx: {\n            minWidth: 120\n          },\n          children: loading ? /*#__PURE__*/_jsxDEV(CircularProgress, {\n            size: 20\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 642,\n            columnNumber: 28\n          }, this) : 'Salva'\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 633,\n          columnNumber: 15\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 632,\n        columnNumber: 13\n      }, this)]\n    }, void 0, true)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 326,\n    columnNumber: 5\n  }, this);\n};\n_s(AggiungiCavoForm, \"Muxm1Wb8jDN/87yWsjtE8U4F0S8=\", false, function () {\n  return [useNavigate];\n});\n_c = AggiungiCavoForm;\nexport default AggiungiCavoForm;\nvar _c;\n$RefreshReg$(_c, \"AggiungiCavoForm\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "Box", "TextField", "<PERSON><PERSON>", "Grid", "FormControl", "InputLabel", "Select", "MenuItem", "FormHelperText", "<PERSON><PERSON>", "CircularProgress", "Typography", "Paper", "Save", "SaveIcon", "Warning", "WarningIcon", "useNavigate", "axios", "config", "caviService", "validateCavoData", "validateField", "isEmpty", "redirectToVisualizzaCavi", "jsxDEV", "_jsxDEV", "Fragment", "_Fragment", "AggiungiCavoForm", "cantiereId", "onSuccess", "onError", "isDialog", "_s", "navigate", "loading", "setLoading", "loadingRevisione", "setLoadingRevisione", "formData", "setFormData", "id_cavo", "revisione_ufficiale", "sistema", "utility", "colore_cavo", "tipologia", "n_conduttori", "sezione", "sh", "ubicazione_partenza", "utenza_partenza", "descrizione_utenza_partenza", "ubicazione_arrivo", "utenza_arrivo", "descrizione_utenza_arrivo", "metri_te<PERSON>ci", "metratura_reale", "responsabile_posa", "id_bobina", "stato_installazione", "formErrors", "setFormErrors", "formWarnings", "setForm<PERSON><PERSON>nings", "loadRevisioneCorrente", "revisione", "getRevisioneCorrente", "prev", "error", "console", "handleFormChange", "e", "name", "value", "target", "additionalParams", "metriTeorici", "parseFloat", "result", "valid", "message", "warning", "handleCancel", "handleSubmit", "preventDefault", "log", "validation", "<PERSON><PERSON><PERSON><PERSON>", "errors", "warnings", "validatedData", "toUpperCase", "requiredFields", "missingFields", "filter", "field", "length", "Error", "join", "dataToSend", "toString", "note", "token", "localStorage", "getItem", "API_URL", "JSON", "stringify", "response", "post", "headers", "timeout", "data", "navError", "window", "location", "href", "code", "includes", "setTimeout", "checkResponse", "get", "exists", "checkError", "errorMessage", "responseData", "detail", "hasWarnings", "Object", "keys", "component", "onSubmit", "noValidate", "children", "sx", "display", "justifyContent", "my", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "severity", "icon", "mb", "py", "variant", "fontSize", "network_error", "fontWeight", "p", "boxShadow", "gutterBottom", "container", "spacing", "item", "xs", "sm", "label", "fullWidth", "onChange", "required", "helperText", "inputProps", "style", "textTransform", "FormHelperTextProps", "color", "undefined", "id", "labelId", "mt", "gap", "size", "onClick", "disabled", "min<PERSON><PERSON><PERSON>", "type", "startIcon", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/PycharmProjects/CMS/webapp/frontend/src/components/cavi/AggiungiCavoForm.js"], "sourcesContent": ["import React, { useState, useEffect } from 'react';\nimport {\n  Box,\n  TextField,\n  Button,\n  Grid,\n  FormControl,\n  InputLabel,\n  Select,\n  MenuItem,\n  FormHelperText,\n  Alert,\n  CircularProgress,\n  Typography,\n  Paper\n} from '@mui/material';\nimport { Save as SaveIcon, Warning as WarningIcon } from '@mui/icons-material';\nimport { useNavigate } from 'react-router-dom';\nimport axios from 'axios';\nimport config from '../../config';\nimport caviService from '../../services/caviService';\nimport { validateCavoData, validateField, isEmpty } from '../../utils/validationUtils';\nimport { redirectToVisualizzaCavi } from '../../utils/navigationUtils';\n\nconst AggiungiCavoForm = ({ cantiereId, onSuccess, onError, isDialog = false }) => {\n  const navigate = useNavigate();\n  const [loading, setLoading] = useState(false);\n  const [loadingRevisione, setLoadingRevisione] = useState(true);\n  const [formData, setFormData] = useState({\n    id_cavo: '',\n    revisione_ufficiale: '',\n    sistema: '',\n    utility: '',\n    colore_cavo: '',\n    tipologia: '',\n    n_conduttori: '',\n    sezione: '',\n    sh: '',\n    ubicazione_partenza: '',\n    utenza_partenza: '',\n    descrizione_utenza_partenza: '',\n    ubicazione_arrivo: '',\n    utenza_arrivo: '',\n    descrizione_utenza_arrivo: '',\n    metri_teorici: '',\n    metratura_reale: '0',\n    responsabile_posa: '',\n    id_bobina: '',\n    stato_installazione: 'Da installare'\n  });\n  const [formErrors, setFormErrors] = useState({});\n  const [formWarnings, setFormWarnings] = useState({});\n\n  // Carica la revisione corrente all'avvio\n  useEffect(() => {\n    const loadRevisioneCorrente = async () => {\n      try {\n        setLoadingRevisione(true);\n        const revisione = await caviService.getRevisioneCorrente(cantiereId);\n        setFormData(prev => ({ ...prev, revisione_ufficiale: revisione }));\n      } catch (error) {\n        console.error('Errore nel caricamento della revisione corrente:', error);\n        onError('Errore nel caricamento della revisione corrente');\n      } finally {\n        setLoadingRevisione(false);\n      }\n    };\n\n    loadRevisioneCorrente();\n  }, [cantiereId, onError]);\n\n  // Gestisce il cambio dei valori nel form con validazione\n  const handleFormChange = (e) => {\n    const { name, value } = e.target;\n\n    // Aggiorna il valore nel form\n    setFormData({\n      ...formData,\n      [name]: value\n    });\n\n    // Valida il campo\n    const additionalParams = {};\n    if (name === 'metratura_reale') {\n      additionalParams.metriTeorici = parseFloat(formData.metri_teorici || 0);\n    }\n\n    const result = validateField(name, value, additionalParams);\n\n    // Aggiorna gli errori\n    setFormErrors(prev => ({\n      ...prev,\n      [name]: !result.valid ? result.message : null\n    }));\n\n    // Aggiorna gli avvisi\n    setFormWarnings(prev => ({\n      ...prev,\n      [name]: result.warning ? result.message : null\n    }));\n  };\n\n  // Gestisce l'annullamento dell'operazione\n  const handleCancel = () => {\n    if (isDialog) {\n      // Se è in un dialog, non fare nulla (il dialog ha il suo pulsante di annullamento)\n      return;\n    }\n    // Reindirizza alla visualizzazione dei cavi\n    redirectToVisualizzaCavi(navigate);\n  };\n\n\n\n  // Gestisce l'invio del form\n  const handleSubmit = async (e) => {\n    e.preventDefault();\n    setLoading(true);\n\n    try {\n      // Validazione completa dei dati del cavo\n      console.log('Dati del form prima della validazione:', formData);\n      const validation = validateCavoData(formData);\n      console.log('Risultato validazione:', validation);\n\n      if (!validation.isValid) {\n        console.error('Errori di validazione:', validation.errors);\n        setFormErrors(validation.errors);\n        setFormWarnings(validation.warnings);\n        setLoading(false);\n        onError('Ci sono errori nel form. Controlla i campi evidenziati in rosso.');\n        return;\n      }\n\n      // Usa i dati validati\n      const validatedData = validation.validatedData;\n\n      // Converti l'ID cavo in maiuscolo\n      validatedData.id_cavo = validatedData.id_cavo.toUpperCase();\n\n      // Assicurati che i campi obbligatori siano presenti (basati sulla definizione della tabella)\n      // Campi obbligatori: id_cavo, id_cantiere, utility, tipologia, n_conduttori, sezione, metri_teorici,\n      // ubicazione_partenza, ubicazione_arrivo, stato_installazione\n\n      // Verifica che i campi obbligatori siano presenti\n      const requiredFields = [\n        'id_cavo', 'utility', 'tipologia', 'n_conduttori', 'sezione', 'metri_teorici',\n        'ubicazione_partenza', 'ubicazione_arrivo', 'stato_installazione'\n      ];\n\n      const missingFields = requiredFields.filter(field => !validatedData[field]);\n      if (missingFields.length > 0) {\n        throw new Error(`Campi obbligatori mancanti: ${missingFields.join(', ')}`);\n      }\n\n      // Prepara i dati da inviare\n      const dataToSend = {\n        ...validatedData,\n        // Assicurati che i campi obbligatori siano presenti\n        id_cavo: validatedData.id_cavo.toUpperCase(),\n        utility: validatedData.utility,\n        tipologia: validatedData.tipologia,\n        n_conduttori: validatedData.n_conduttori ? validatedData.n_conduttori.toString() : \"0\", // Invia come stringa\n        sezione: validatedData.sezione ? validatedData.sezione.toString() : \"0\", // Invia come stringa\n        metri_teorici: parseFloat(validatedData.metri_teorici) || 0,\n        ubicazione_partenza: validatedData.ubicazione_partenza,\n        ubicazione_arrivo: validatedData.ubicazione_arrivo,\n        stato_installazione: validatedData.stato_installazione || 'Da installare',\n\n        // Campi opzionali\n        metratura_reale: validatedData.metratura_reale ? parseFloat(validatedData.metratura_reale) : null,\n        id_bobina: validatedData.id_bobina || null,\n\n        // Altri campi che potrebbero essere utili\n        sistema: validatedData.sistema || null,\n        colore_cavo: validatedData.colore_cavo || null,\n        utenza_partenza: validatedData.utenza_partenza || null,\n        utenza_arrivo: validatedData.utenza_arrivo || null,\n        descrizione_utenza_partenza: validatedData.descrizione_utenza_partenza || null,\n        descrizione_utenza_arrivo: validatedData.descrizione_utenza_arrivo || null,\n        sh: validatedData.sh || 'N',\n        responsabile_posa: validatedData.responsabile_posa || null,\n        note: validatedData.note || null\n      };\n\n      console.log('Dati da inviare al server dopo la validazione:', dataToSend);\n\n      try {\n        // Invia i dati al server\n        console.log('Tentativo di invio dati al server...');\n\n        // Verifica che cantiereId sia valido\n        if (!cantiereId) {\n          throw new Error('ID cantiere non valido o mancante');\n        }\n\n        // Usa axios direttamente per avere più controllo\n        const token = localStorage.getItem('token');\n        if (!token) {\n          throw new Error('Token di autenticazione mancante. Effettua nuovamente il login.');\n        }\n\n        console.log(`Invio richiesta POST a ${config.API_URL}/cavi/${cantiereId}`);\n        console.log('Dati inviati:', JSON.stringify(dataToSend, null, 2));\n\n        try {\n          // Tenta di inviare la richiesta\n          const response = await axios.post(`${config.API_URL}/cavi/${cantiereId}`, dataToSend, {\n            headers: {\n              'Content-Type': 'application/json',\n              'Authorization': `Bearer ${token}`\n            },\n            timeout: 30000 // Aumentato a 30 secondi per dare più tempo al server\n          });\n\n          console.log('Risposta dal server:', response.data);\n\n          // Imposta loading a false prima di chiudere il dialog\n          setLoading(false);\n\n          // Notifica il successo per chiudere il dialog o reindirizzare\n          onSuccess('Cavo aggiunto con successo');\n\n          // Reindirizza solo se non è in un dialog\n          if (!isDialog) {\n            console.log('Reindirizzamento a visualizza cavi...');\n            try {\n              // Usa navigate invece di window.location per un reindirizzamento più pulito\n              redirectToVisualizzaCavi(navigate);\n            } catch (navError) {\n              console.error('Errore durante il reindirizzamento:', navError);\n              // Fallback: usa window.location solo se navigate fallisce\n              window.location.href = `/dashboard/cantieri/${cantiereId}/cavi/visualizza`;\n            }\n          }\n        } catch (error) {\n          console.error('Errore durante l\\'invio dei dati al server:', error);\n\n          // Se è un errore di rete, verifica se il cavo è stato inserito senza mostrare subito un errore\n          if (!error.response || error.code === 'ECONNABORTED' || error.message.includes('Network Error')) {\n            // Manteniamo lo stato di caricamento attivo senza mostrare errori\n            console.log('Rilevato possibile errore di rete, verifica in corso...');\n\n            // Attendi un secondo e poi verifica se il cavo è stato inserito\n            setTimeout(async () => {\n              try {\n                // Verifica se il cavo esiste\n                const checkResponse = await axios.get(`${config.API_URL}/cavi/${cantiereId}/check/${dataToSend.id_cavo}`, {\n                  headers: {\n                    'Authorization': `Bearer ${token}`\n                  },\n                  timeout: 5000\n                });\n\n                if (checkResponse.data && checkResponse.data.exists) {\n                  // Il cavo è stato inserito con successo\n\n                  // Imposta loading a false prima di chiudere il dialog\n                  setLoading(false);\n\n                  // Notifica il successo per chiudere il dialog o reindirizzare\n                  onSuccess('Cavo aggiunto con successo');\n\n                  // Reindirizza solo se non è in un dialog\n                  if (!isDialog) {\n                    console.log('Reindirizzamento a visualizza cavi...');\n                    try {\n                      // Usa navigate invece di window.location per un reindirizzamento più pulito\n                      redirectToVisualizzaCavi(navigate);\n                    } catch (navError) {\n                      console.error('Errore durante il reindirizzamento:', navError);\n                      // Fallback: usa window.location solo se navigate fallisce\n                      window.location.href = `/dashboard/cantieri/${cantiereId}/cavi/visualizza`;\n                    }\n                  }\n                } else {\n                  // Il cavo non è stato inserito, ora mostriamo l'errore\n                  onError('Il cavo non è stato inserito a causa di un problema di connessione. Riprova.');\n                  setLoading(false);\n                }\n              } catch (checkError) {\n                console.error('Errore durante la verifica:', checkError);\n                onError('Impossibile verificare se il cavo è stato inserito. Riprova.');\n                setLoading(false);\n              }\n            }, 1000);\n          } else {\n            // Per altri tipi di errori, mostra il messaggio di errore\n            let errorMessage = 'Errore durante l\\'aggiunta del cavo';\n\n            if (error.response && error.response.data) {\n              const responseData = error.response.data;\n              if (responseData.detail) {\n                errorMessage = responseData.detail;\n              } else if (typeof responseData === 'string') {\n                errorMessage = responseData;\n              } else {\n                errorMessage = JSON.stringify(responseData);\n              }\n            } else if (error.message) {\n              errorMessage = error.message;\n            }\n\n            onError(errorMessage);\n            setLoading(false);\n          }\n        }\n      } catch (error) {\n        console.error('Errore durante la preparazione della richiesta:', error);\n        let errorMessage = error.message || 'Errore durante l\\'aggiunta del cavo';\n        onError(errorMessage);\n        setLoading(false);\n      }\n    } catch (error) {\n      console.error('Errore durante l\\'aggiunta del cavo:', error);\n      onError(error.detail || 'Errore durante l\\'aggiunta del cavo');\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  // Mostra un avviso se ci sono warning\n  const hasWarnings = Object.keys(formWarnings).length > 0;\n\n  return (\n    <Box component=\"form\" onSubmit={handleSubmit} noValidate>\n      {loadingRevisione ? (\n        <Box sx={{ display: 'flex', justifyContent: 'center', my: 4 }}>\n          <CircularProgress />\n        </Box>\n      ) : (\n        <>\n          {hasWarnings && (\n            <>\n              <Alert\n                severity=\"warning\"\n                icon={<WarningIcon />}\n                sx={{ mb: isDialog ? 1 : 2, py: isDialog ? 0.5 : 1 }}\n              >\n                <Typography variant=\"subtitle2\" sx={{ fontSize: isDialog ? '0.8rem' : '0.875rem' }}>\n                  Ci sono alcuni avvisi nel form. Puoi procedere, ma verifica i campi evidenziati.\n                </Typography>\n              </Alert>\n\n              {/* Mostra avvisi specifici */}\n              {formWarnings.network_error && (\n                <Alert\n                  severity=\"error\"\n                  sx={{ mb: isDialog ? 2 : 3, py: isDialog ? 0.5 : 1 }}\n                >\n                  <Typography variant=\"subtitle2\" sx={{ fontWeight: 'bold' }}>\n                    {formWarnings.network_error}\n                  </Typography>\n                </Alert>\n              )}\n            </>\n          )}\n\n          <Paper sx={{ p: isDialog ? 1 : 3, mb: isDialog ? 1 : 3, boxShadow: isDialog ? 0 : 1 }}>\n            <Typography variant=\"h6\" gutterBottom sx={{ fontSize: isDialog ? '0.9rem' : '1.25rem', mb: isDialog ? 0.5 : 1 }}>\n              Informazioni Generali\n            </Typography>\n            <Grid container spacing={isDialog ? 1 : 2}>\n              <Grid item xs={12} sm={6}>\n                <TextField\n                  name=\"id_cavo\"\n                  label=\"ID Cavo\"\n                  fullWidth\n                  variant=\"outlined\"\n                  value={formData.id_cavo}\n                  onChange={handleFormChange}\n                  required\n                  error={!!formErrors.id_cavo}\n                  helperText={formErrors.id_cavo}\n                  inputProps={{ style: { textTransform: 'uppercase' } }}\n                />\n              </Grid>\n              <Grid item xs={12} sm={6}>\n                <TextField\n                  name=\"revisione_ufficiale\"\n                  label=\"Revisione Ufficiale\"\n                  fullWidth\n                  variant=\"outlined\"\n                  value={formData.revisione_ufficiale}\n                  onChange={handleFormChange}\n                  required\n                  error={!!formErrors.revisione_ufficiale}\n                  helperText={formErrors.revisione_ufficiale}\n                />\n              </Grid>\n              <Grid item xs={12} sm={6}>\n                <TextField\n                  name=\"sistema\"\n                  label=\"Sistema\"\n                  fullWidth\n                  variant=\"outlined\"\n                  value={formData.sistema}\n                  onChange={handleFormChange}\n                  error={!!formErrors.sistema}\n                  helperText={formErrors.sistema}\n                />\n              </Grid>\n              <Grid item xs={12} sm={6}>\n                <TextField\n                  name=\"utility\"\n                  label=\"Utility\"\n                  fullWidth\n                  variant=\"outlined\"\n                  value={formData.utility}\n                  onChange={handleFormChange}\n                  error={!!formErrors.utility}\n                  helperText={formErrors.utility}\n                />\n              </Grid>\n            </Grid>\n          </Paper>\n\n          <Paper sx={{ p: isDialog ? 1 : 3, mb: isDialog ? 1 : 3, boxShadow: isDialog ? 0 : 1 }}>\n            <Typography variant=\"h6\" gutterBottom sx={{ fontSize: isDialog ? '0.9rem' : '1.25rem', mb: isDialog ? 0.5 : 1 }}>\n              Caratteristiche Tecniche\n            </Typography>\n            <Grid container spacing={isDialog ? 1 : 2}>\n              <Grid item xs={12} sm={6}>\n                <TextField\n                  name=\"colore_cavo\"\n                  label=\"Colore Cavo\"\n                  fullWidth\n                  variant=\"outlined\"\n                  value={formData.colore_cavo}\n                  onChange={handleFormChange}\n                  error={!!formErrors.colore_cavo}\n                  helperText={formErrors.colore_cavo}\n                />\n              </Grid>\n              <Grid item xs={12} sm={6}>\n                <TextField\n                  name=\"tipologia\"\n                  label=\"Tipologia\"\n                  fullWidth\n                  variant=\"outlined\"\n                  value={formData.tipologia}\n                  onChange={handleFormChange}\n                  error={!!formErrors.tipologia}\n                  helperText={formErrors.tipologia}\n                />\n              </Grid>\n              <Grid item xs={12} sm={4}>\n                <TextField\n                  name=\"n_conduttori\"\n                  label=\"Numero Conduttori\"\n                  fullWidth\n                  variant=\"outlined\"\n                  value={formData.n_conduttori}\n                  onChange={handleFormChange}\n                  error={!!formErrors.n_conduttori}\n                  helperText={formErrors.n_conduttori || formWarnings.n_conduttori}\n                  FormHelperTextProps={{\n                    style: { color: formWarnings.n_conduttori ? 'orange' : undefined }\n                  }}\n                />\n              </Grid>\n              <Grid item xs={12} sm={4}>\n                <TextField\n                  name=\"sezione\"\n                  label=\"Sezione\"\n                  fullWidth\n                  variant=\"outlined\"\n                  value={formData.sezione}\n                  onChange={handleFormChange}\n                  error={!!formErrors.sezione}\n                  helperText={formErrors.sezione || formWarnings.sezione}\n                  FormHelperTextProps={{\n                    style: { color: formWarnings.sezione ? 'orange' : undefined }\n                  }}\n                />\n              </Grid>\n              <Grid item xs={12} sm={4}>\n                <FormControl fullWidth error={!!formErrors.sh}>\n                  <InputLabel id=\"sh-label\">Schermato (S/N)</InputLabel>\n                  <Select\n                    labelId=\"sh-label\"\n                    name=\"sh\"\n                    value={formData.sh}\n                    label=\"Schermato (S/N)\"\n                    onChange={handleFormChange}\n                  >\n                    <MenuItem value=\"S\">S</MenuItem>\n                    <MenuItem value=\"N\">N</MenuItem>\n                  </Select>\n                  {formErrors.sh && <FormHelperText>{formErrors.sh}</FormHelperText>}\n                </FormControl>\n              </Grid>\n            </Grid>\n          </Paper>\n\n          <Paper sx={{ p: isDialog ? 1 : 3, mb: isDialog ? 1 : 3, boxShadow: isDialog ? 0 : 1 }}>\n            <Typography variant=\"h6\" gutterBottom sx={{ fontSize: isDialog ? '0.9rem' : '1.25rem', mb: isDialog ? 0.5 : 1 }}>\n              Ubicazione Partenza\n            </Typography>\n            <Grid container spacing={isDialog ? 1 : 2}>\n              <Grid item xs={12} sm={4}>\n                <TextField\n                  name=\"ubicazione_partenza\"\n                  label=\"Ubicazione Partenza\"\n                  fullWidth\n                  variant=\"outlined\"\n                  value={formData.ubicazione_partenza}\n                  onChange={handleFormChange}\n                  error={!!formErrors.ubicazione_partenza}\n                  helperText={formErrors.ubicazione_partenza}\n                />\n              </Grid>\n              <Grid item xs={12} sm={4}>\n                <TextField\n                  name=\"utenza_partenza\"\n                  label=\"Utenza Partenza\"\n                  fullWidth\n                  variant=\"outlined\"\n                  value={formData.utenza_partenza}\n                  onChange={handleFormChange}\n                  error={!!formErrors.utenza_partenza}\n                  helperText={formErrors.utenza_partenza}\n                />\n              </Grid>\n              <Grid item xs={12} sm={4}>\n                <TextField\n                  name=\"descrizione_utenza_partenza\"\n                  label=\"Descrizione Utenza Partenza\"\n                  fullWidth\n                  variant=\"outlined\"\n                  value={formData.descrizione_utenza_partenza}\n                  onChange={handleFormChange}\n                  error={!!formErrors.descrizione_utenza_partenza}\n                  helperText={formErrors.descrizione_utenza_partenza}\n                />\n              </Grid>\n            </Grid>\n          </Paper>\n\n          <Paper sx={{ p: isDialog ? 1 : 3, mb: isDialog ? 1 : 3, boxShadow: isDialog ? 0 : 1 }}>\n            <Typography variant=\"h6\" gutterBottom sx={{ fontSize: isDialog ? '0.9rem' : '1.25rem', mb: isDialog ? 0.5 : 1 }}>\n              Ubicazione Arrivo\n            </Typography>\n            <Grid container spacing={isDialog ? 1 : 2}>\n              <Grid item xs={12} sm={4}>\n                <TextField\n                  name=\"ubicazione_arrivo\"\n                  label=\"Ubicazione Arrivo\"\n                  fullWidth\n                  variant=\"outlined\"\n                  value={formData.ubicazione_arrivo}\n                  onChange={handleFormChange}\n                  error={!!formErrors.ubicazione_arrivo}\n                  helperText={formErrors.ubicazione_arrivo}\n                />\n              </Grid>\n              <Grid item xs={12} sm={4}>\n                <TextField\n                  name=\"utenza_arrivo\"\n                  label=\"Utenza Arrivo\"\n                  fullWidth\n                  variant=\"outlined\"\n                  value={formData.utenza_arrivo}\n                  onChange={handleFormChange}\n                  error={!!formErrors.utenza_arrivo}\n                  helperText={formErrors.utenza_arrivo}\n                />\n              </Grid>\n              <Grid item xs={12} sm={4}>\n                <TextField\n                  name=\"descrizione_utenza_arrivo\"\n                  label=\"Descrizione Utenza Arrivo\"\n                  fullWidth\n                  variant=\"outlined\"\n                  value={formData.descrizione_utenza_arrivo}\n                  onChange={handleFormChange}\n                  error={!!formErrors.descrizione_utenza_arrivo}\n                  helperText={formErrors.descrizione_utenza_arrivo}\n                />\n              </Grid>\n            </Grid>\n          </Paper>\n\n          <Paper sx={{ p: isDialog ? 2 : 3, mb: isDialog ? 2 : 3, boxShadow: isDialog ? 0 : 1 }}>\n            <Typography variant=\"h6\" gutterBottom sx={{ fontSize: isDialog ? '1.1rem' : '1.25rem' }}>\n              Metratura\n            </Typography>\n            <Grid container spacing={isDialog ? 1 : 2}>\n              <Grid item xs={12} sm={6}>\n                <TextField\n                  name=\"metri_teorici\"\n                  label=\"Metri Teorici\"\n                  fullWidth\n                  variant=\"outlined\"\n                  value={formData.metri_teorici}\n                  onChange={handleFormChange}\n                  required\n                  error={!!formErrors.metri_teorici}\n                  helperText={formErrors.metri_teorici}\n                />\n              </Grid>\n\n            </Grid>\n          </Paper>\n\n          {!isDialog && (\n            <Box sx={{ mt: 3, display: 'flex', justifyContent: 'center', gap: 2 }}>\n              <Button\n                variant=\"outlined\"\n                color=\"secondary\"\n                size=\"large\"\n                onClick={handleCancel}\n                disabled={loading}\n                sx={{ minWidth: 150 }}\n              >\n                Annulla\n              </Button>\n              <Button\n                type=\"submit\"\n                variant=\"contained\"\n                color=\"primary\"\n                size=\"large\"\n                startIcon={<SaveIcon />}\n                disabled={loading}\n                sx={{ minWidth: 150 }}\n              >\n                {loading ? <CircularProgress size={24} /> : 'Salva Cavo'}\n              </Button>\n            </Box>\n          )}\n          {isDialog && (\n            <Box sx={{ mt: 2, display: 'flex', justifyContent: 'flex-end', gap: 2 }}>\n              <Button\n                type=\"submit\"\n                variant=\"contained\"\n                color=\"primary\"\n                size=\"medium\"\n                startIcon={<SaveIcon />}\n                disabled={loading}\n                sx={{ minWidth: 120 }}\n              >\n                {loading ? <CircularProgress size={20} /> : 'Salva'}\n              </Button>\n            </Box>\n          )}\n        </>\n      )}\n    </Box>\n  );\n};\n\nexport default AggiungiCavoForm;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAClD,SACEC,GAAG,EACHC,SAAS,EACTC,MAAM,EACNC,IAAI,EACJC,WAAW,EACXC,UAAU,EACVC,MAAM,EACNC,QAAQ,EACRC,cAAc,EACdC,KAAK,EACLC,gBAAgB,EAChBC,UAAU,EACVC,KAAK,QACA,eAAe;AACtB,SAASC,IAAI,IAAIC,QAAQ,EAAEC,OAAO,IAAIC,WAAW,QAAQ,qBAAqB;AAC9E,SAASC,WAAW,QAAQ,kBAAkB;AAC9C,OAAOC,KAAK,MAAM,OAAO;AACzB,OAAOC,MAAM,MAAM,cAAc;AACjC,OAAOC,WAAW,MAAM,4BAA4B;AACpD,SAASC,gBAAgB,EAAEC,aAAa,EAAEC,OAAO,QAAQ,6BAA6B;AACtF,SAASC,wBAAwB,QAAQ,6BAA6B;AAAC,SAAAC,MAAA,IAAAC,OAAA,EAAAC,QAAA,IAAAC,SAAA;AAEvE,MAAMC,gBAAgB,GAAGA,CAAC;EAAEC,UAAU;EAAEC,SAAS;EAAEC,OAAO;EAAEC,QAAQ,GAAG;AAAM,CAAC,KAAK;EAAAC,EAAA;EACjF,MAAMC,QAAQ,GAAGlB,WAAW,CAAC,CAAC;EAC9B,MAAM,CAACmB,OAAO,EAAEC,UAAU,CAAC,GAAGvC,QAAQ,CAAC,KAAK,CAAC;EAC7C,MAAM,CAACwC,gBAAgB,EAAEC,mBAAmB,CAAC,GAAGzC,QAAQ,CAAC,IAAI,CAAC;EAC9D,MAAM,CAAC0C,QAAQ,EAAEC,WAAW,CAAC,GAAG3C,QAAQ,CAAC;IACvC4C,OAAO,EAAE,EAAE;IACXC,mBAAmB,EAAE,EAAE;IACvBC,OAAO,EAAE,EAAE;IACXC,OAAO,EAAE,EAAE;IACXC,WAAW,EAAE,EAAE;IACfC,SAAS,EAAE,EAAE;IACbC,YAAY,EAAE,EAAE;IAChBC,OAAO,EAAE,EAAE;IACXC,EAAE,EAAE,EAAE;IACNC,mBAAmB,EAAE,EAAE;IACvBC,eAAe,EAAE,EAAE;IACnBC,2BAA2B,EAAE,EAAE;IAC/BC,iBAAiB,EAAE,EAAE;IACrBC,aAAa,EAAE,EAAE;IACjBC,yBAAyB,EAAE,EAAE;IAC7BC,aAAa,EAAE,EAAE;IACjBC,eAAe,EAAE,GAAG;IACpBC,iBAAiB,EAAE,EAAE;IACrBC,SAAS,EAAE,EAAE;IACbC,mBAAmB,EAAE;EACvB,CAAC,CAAC;EACF,MAAM,CAACC,UAAU,EAAEC,aAAa,CAAC,GAAGjE,QAAQ,CAAC,CAAC,CAAC,CAAC;EAChD,MAAM,CAACkE,YAAY,EAAEC,eAAe,CAAC,GAAGnE,QAAQ,CAAC,CAAC,CAAC,CAAC;;EAEpD;EACAC,SAAS,CAAC,MAAM;IACd,MAAMmE,qBAAqB,GAAG,MAAAA,CAAA,KAAY;MACxC,IAAI;QACF3B,mBAAmB,CAAC,IAAI,CAAC;QACzB,MAAM4B,SAAS,GAAG,MAAM/C,WAAW,CAACgD,oBAAoB,CAACtC,UAAU,CAAC;QACpEW,WAAW,CAAC4B,IAAI,KAAK;UAAE,GAAGA,IAAI;UAAE1B,mBAAmB,EAAEwB;QAAU,CAAC,CAAC,CAAC;MACpE,CAAC,CAAC,OAAOG,KAAK,EAAE;QACdC,OAAO,CAACD,KAAK,CAAC,kDAAkD,EAAEA,KAAK,CAAC;QACxEtC,OAAO,CAAC,iDAAiD,CAAC;MAC5D,CAAC,SAAS;QACRO,mBAAmB,CAAC,KAAK,CAAC;MAC5B;IACF,CAAC;IAED2B,qBAAqB,CAAC,CAAC;EACzB,CAAC,EAAE,CAACpC,UAAU,EAAEE,OAAO,CAAC,CAAC;;EAEzB;EACA,MAAMwC,gBAAgB,GAAIC,CAAC,IAAK;IAC9B,MAAM;MAAEC,IAAI;MAAEC;IAAM,CAAC,GAAGF,CAAC,CAACG,MAAM;;IAEhC;IACAnC,WAAW,CAAC;MACV,GAAGD,QAAQ;MACX,CAACkC,IAAI,GAAGC;IACV,CAAC,CAAC;;IAEF;IACA,MAAME,gBAAgB,GAAG,CAAC,CAAC;IAC3B,IAAIH,IAAI,KAAK,iBAAiB,EAAE;MAC9BG,gBAAgB,CAACC,YAAY,GAAGC,UAAU,CAACvC,QAAQ,CAACiB,aAAa,IAAI,CAAC,CAAC;IACzE;IAEA,MAAMuB,MAAM,GAAG1D,aAAa,CAACoD,IAAI,EAAEC,KAAK,EAAEE,gBAAgB,CAAC;;IAE3D;IACAd,aAAa,CAACM,IAAI,KAAK;MACrB,GAAGA,IAAI;MACP,CAACK,IAAI,GAAG,CAACM,MAAM,CAACC,KAAK,GAAGD,MAAM,CAACE,OAAO,GAAG;IAC3C,CAAC,CAAC,CAAC;;IAEH;IACAjB,eAAe,CAACI,IAAI,KAAK;MACvB,GAAGA,IAAI;MACP,CAACK,IAAI,GAAGM,MAAM,CAACG,OAAO,GAAGH,MAAM,CAACE,OAAO,GAAG;IAC5C,CAAC,CAAC,CAAC;EACL,CAAC;;EAED;EACA,MAAME,YAAY,GAAGA,CAAA,KAAM;IACzB,IAAInD,QAAQ,EAAE;MACZ;MACA;IACF;IACA;IACAT,wBAAwB,CAACW,QAAQ,CAAC;EACpC,CAAC;;EAID;EACA,MAAMkD,YAAY,GAAG,MAAOZ,CAAC,IAAK;IAChCA,CAAC,CAACa,cAAc,CAAC,CAAC;IAClBjD,UAAU,CAAC,IAAI,CAAC;IAEhB,IAAI;MACF;MACAkC,OAAO,CAACgB,GAAG,CAAC,wCAAwC,EAAE/C,QAAQ,CAAC;MAC/D,MAAMgD,UAAU,GAAGnE,gBAAgB,CAACmB,QAAQ,CAAC;MAC7C+B,OAAO,CAACgB,GAAG,CAAC,wBAAwB,EAAEC,UAAU,CAAC;MAEjD,IAAI,CAACA,UAAU,CAACC,OAAO,EAAE;QACvBlB,OAAO,CAACD,KAAK,CAAC,wBAAwB,EAAEkB,UAAU,CAACE,MAAM,CAAC;QAC1D3B,aAAa,CAACyB,UAAU,CAACE,MAAM,CAAC;QAChCzB,eAAe,CAACuB,UAAU,CAACG,QAAQ,CAAC;QACpCtD,UAAU,CAAC,KAAK,CAAC;QACjBL,OAAO,CAAC,kEAAkE,CAAC;QAC3E;MACF;;MAEA;MACA,MAAM4D,aAAa,GAAGJ,UAAU,CAACI,aAAa;;MAE9C;MACAA,aAAa,CAAClD,OAAO,GAAGkD,aAAa,CAAClD,OAAO,CAACmD,WAAW,CAAC,CAAC;;MAE3D;MACA;MACA;;MAEA;MACA,MAAMC,cAAc,GAAG,CACrB,SAAS,EAAE,SAAS,EAAE,WAAW,EAAE,cAAc,EAAE,SAAS,EAAE,eAAe,EAC7E,qBAAqB,EAAE,mBAAmB,EAAE,qBAAqB,CAClE;MAED,MAAMC,aAAa,GAAGD,cAAc,CAACE,MAAM,CAACC,KAAK,IAAI,CAACL,aAAa,CAACK,KAAK,CAAC,CAAC;MAC3E,IAAIF,aAAa,CAACG,MAAM,GAAG,CAAC,EAAE;QAC5B,MAAM,IAAIC,KAAK,CAAC,+BAA+BJ,aAAa,CAACK,IAAI,CAAC,IAAI,CAAC,EAAE,CAAC;MAC5E;;MAEA;MACA,MAAMC,UAAU,GAAG;QACjB,GAAGT,aAAa;QAChB;QACAlD,OAAO,EAAEkD,aAAa,CAAClD,OAAO,CAACmD,WAAW,CAAC,CAAC;QAC5ChD,OAAO,EAAE+C,aAAa,CAAC/C,OAAO;QAC9BE,SAAS,EAAE6C,aAAa,CAAC7C,SAAS;QAClCC,YAAY,EAAE4C,aAAa,CAAC5C,YAAY,GAAG4C,aAAa,CAAC5C,YAAY,CAACsD,QAAQ,CAAC,CAAC,GAAG,GAAG;QAAE;QACxFrD,OAAO,EAAE2C,aAAa,CAAC3C,OAAO,GAAG2C,aAAa,CAAC3C,OAAO,CAACqD,QAAQ,CAAC,CAAC,GAAG,GAAG;QAAE;QACzE7C,aAAa,EAAEsB,UAAU,CAACa,aAAa,CAACnC,aAAa,CAAC,IAAI,CAAC;QAC3DN,mBAAmB,EAAEyC,aAAa,CAACzC,mBAAmB;QACtDG,iBAAiB,EAAEsC,aAAa,CAACtC,iBAAiB;QAClDO,mBAAmB,EAAE+B,aAAa,CAAC/B,mBAAmB,IAAI,eAAe;QAEzE;QACAH,eAAe,EAAEkC,aAAa,CAAClC,eAAe,GAAGqB,UAAU,CAACa,aAAa,CAAClC,eAAe,CAAC,GAAG,IAAI;QACjGE,SAAS,EAAEgC,aAAa,CAAChC,SAAS,IAAI,IAAI;QAE1C;QACAhB,OAAO,EAAEgD,aAAa,CAAChD,OAAO,IAAI,IAAI;QACtCE,WAAW,EAAE8C,aAAa,CAAC9C,WAAW,IAAI,IAAI;QAC9CM,eAAe,EAAEwC,aAAa,CAACxC,eAAe,IAAI,IAAI;QACtDG,aAAa,EAAEqC,aAAa,CAACrC,aAAa,IAAI,IAAI;QAClDF,2BAA2B,EAAEuC,aAAa,CAACvC,2BAA2B,IAAI,IAAI;QAC9EG,yBAAyB,EAAEoC,aAAa,CAACpC,yBAAyB,IAAI,IAAI;QAC1EN,EAAE,EAAE0C,aAAa,CAAC1C,EAAE,IAAI,GAAG;QAC3BS,iBAAiB,EAAEiC,aAAa,CAACjC,iBAAiB,IAAI,IAAI;QAC1D4C,IAAI,EAAEX,aAAa,CAACW,IAAI,IAAI;MAC9B,CAAC;MAEDhC,OAAO,CAACgB,GAAG,CAAC,gDAAgD,EAAEc,UAAU,CAAC;MAEzE,IAAI;QACF;QACA9B,OAAO,CAACgB,GAAG,CAAC,sCAAsC,CAAC;;QAEnD;QACA,IAAI,CAACzD,UAAU,EAAE;UACf,MAAM,IAAIqE,KAAK,CAAC,mCAAmC,CAAC;QACtD;;QAEA;QACA,MAAMK,KAAK,GAAGC,YAAY,CAACC,OAAO,CAAC,OAAO,CAAC;QAC3C,IAAI,CAACF,KAAK,EAAE;UACV,MAAM,IAAIL,KAAK,CAAC,iEAAiE,CAAC;QACpF;QAEA5B,OAAO,CAACgB,GAAG,CAAC,0BAA0BpE,MAAM,CAACwF,OAAO,SAAS7E,UAAU,EAAE,CAAC;QAC1EyC,OAAO,CAACgB,GAAG,CAAC,eAAe,EAAEqB,IAAI,CAACC,SAAS,CAACR,UAAU,EAAE,IAAI,EAAE,CAAC,CAAC,CAAC;QAEjE,IAAI;UACF;UACA,MAAMS,QAAQ,GAAG,MAAM5F,KAAK,CAAC6F,IAAI,CAAC,GAAG5F,MAAM,CAACwF,OAAO,SAAS7E,UAAU,EAAE,EAAEuE,UAAU,EAAE;YACpFW,OAAO,EAAE;cACP,cAAc,EAAE,kBAAkB;cAClC,eAAe,EAAE,UAAUR,KAAK;YAClC,CAAC;YACDS,OAAO,EAAE,KAAK,CAAC;UACjB,CAAC,CAAC;UAEF1C,OAAO,CAACgB,GAAG,CAAC,sBAAsB,EAAEuB,QAAQ,CAACI,IAAI,CAAC;;UAElD;UACA7E,UAAU,CAAC,KAAK,CAAC;;UAEjB;UACAN,SAAS,CAAC,4BAA4B,CAAC;;UAEvC;UACA,IAAI,CAACE,QAAQ,EAAE;YACbsC,OAAO,CAACgB,GAAG,CAAC,uCAAuC,CAAC;YACpD,IAAI;cACF;cACA/D,wBAAwB,CAACW,QAAQ,CAAC;YACpC,CAAC,CAAC,OAAOgF,QAAQ,EAAE;cACjB5C,OAAO,CAACD,KAAK,CAAC,qCAAqC,EAAE6C,QAAQ,CAAC;cAC9D;cACAC,MAAM,CAACC,QAAQ,CAACC,IAAI,GAAG,uBAAuBxF,UAAU,kBAAkB;YAC5E;UACF;QACF,CAAC,CAAC,OAAOwC,KAAK,EAAE;UACdC,OAAO,CAACD,KAAK,CAAC,6CAA6C,EAAEA,KAAK,CAAC;;UAEnE;UACA,IAAI,CAACA,KAAK,CAACwC,QAAQ,IAAIxC,KAAK,CAACiD,IAAI,KAAK,cAAc,IAAIjD,KAAK,CAACY,OAAO,CAACsC,QAAQ,CAAC,eAAe,CAAC,EAAE;YAC/F;YACAjD,OAAO,CAACgB,GAAG,CAAC,yDAAyD,CAAC;;YAEtE;YACAkC,UAAU,CAAC,YAAY;cACrB,IAAI;gBACF;gBACA,MAAMC,aAAa,GAAG,MAAMxG,KAAK,CAACyG,GAAG,CAAC,GAAGxG,MAAM,CAACwF,OAAO,SAAS7E,UAAU,UAAUuE,UAAU,CAAC3D,OAAO,EAAE,EAAE;kBACxGsE,OAAO,EAAE;oBACP,eAAe,EAAE,UAAUR,KAAK;kBAClC,CAAC;kBACDS,OAAO,EAAE;gBACX,CAAC,CAAC;gBAEF,IAAIS,aAAa,CAACR,IAAI,IAAIQ,aAAa,CAACR,IAAI,CAACU,MAAM,EAAE;kBACnD;;kBAEA;kBACAvF,UAAU,CAAC,KAAK,CAAC;;kBAEjB;kBACAN,SAAS,CAAC,4BAA4B,CAAC;;kBAEvC;kBACA,IAAI,CAACE,QAAQ,EAAE;oBACbsC,OAAO,CAACgB,GAAG,CAAC,uCAAuC,CAAC;oBACpD,IAAI;sBACF;sBACA/D,wBAAwB,CAACW,QAAQ,CAAC;oBACpC,CAAC,CAAC,OAAOgF,QAAQ,EAAE;sBACjB5C,OAAO,CAACD,KAAK,CAAC,qCAAqC,EAAE6C,QAAQ,CAAC;sBAC9D;sBACAC,MAAM,CAACC,QAAQ,CAACC,IAAI,GAAG,uBAAuBxF,UAAU,kBAAkB;oBAC5E;kBACF;gBACF,CAAC,MAAM;kBACL;kBACAE,OAAO,CAAC,8EAA8E,CAAC;kBACvFK,UAAU,CAAC,KAAK,CAAC;gBACnB;cACF,CAAC,CAAC,OAAOwF,UAAU,EAAE;gBACnBtD,OAAO,CAACD,KAAK,CAAC,6BAA6B,EAAEuD,UAAU,CAAC;gBACxD7F,OAAO,CAAC,8DAA8D,CAAC;gBACvEK,UAAU,CAAC,KAAK,CAAC;cACnB;YACF,CAAC,EAAE,IAAI,CAAC;UACV,CAAC,MAAM;YACL;YACA,IAAIyF,YAAY,GAAG,qCAAqC;YAExD,IAAIxD,KAAK,CAACwC,QAAQ,IAAIxC,KAAK,CAACwC,QAAQ,CAACI,IAAI,EAAE;cACzC,MAAMa,YAAY,GAAGzD,KAAK,CAACwC,QAAQ,CAACI,IAAI;cACxC,IAAIa,YAAY,CAACC,MAAM,EAAE;gBACvBF,YAAY,GAAGC,YAAY,CAACC,MAAM;cACpC,CAAC,MAAM,IAAI,OAAOD,YAAY,KAAK,QAAQ,EAAE;gBAC3CD,YAAY,GAAGC,YAAY;cAC7B,CAAC,MAAM;gBACLD,YAAY,GAAGlB,IAAI,CAACC,SAAS,CAACkB,YAAY,CAAC;cAC7C;YACF,CAAC,MAAM,IAAIzD,KAAK,CAACY,OAAO,EAAE;cACxB4C,YAAY,GAAGxD,KAAK,CAACY,OAAO;YAC9B;YAEAlD,OAAO,CAAC8F,YAAY,CAAC;YACrBzF,UAAU,CAAC,KAAK,CAAC;UACnB;QACF;MACF,CAAC,CAAC,OAAOiC,KAAK,EAAE;QACdC,OAAO,CAACD,KAAK,CAAC,iDAAiD,EAAEA,KAAK,CAAC;QACvE,IAAIwD,YAAY,GAAGxD,KAAK,CAACY,OAAO,IAAI,qCAAqC;QACzElD,OAAO,CAAC8F,YAAY,CAAC;QACrBzF,UAAU,CAAC,KAAK,CAAC;MACnB;IACF,CAAC,CAAC,OAAOiC,KAAK,EAAE;MACdC,OAAO,CAACD,KAAK,CAAC,sCAAsC,EAAEA,KAAK,CAAC;MAC5DtC,OAAO,CAACsC,KAAK,CAAC0D,MAAM,IAAI,qCAAqC,CAAC;IAChE,CAAC,SAAS;MACR3F,UAAU,CAAC,KAAK,CAAC;IACnB;EACF,CAAC;;EAED;EACA,MAAM4F,WAAW,GAAGC,MAAM,CAACC,IAAI,CAACnE,YAAY,CAAC,CAACkC,MAAM,GAAG,CAAC;EAExD,oBACExE,OAAA,CAAC1B,GAAG;IAACoI,SAAS,EAAC,MAAM;IAACC,QAAQ,EAAEhD,YAAa;IAACiD,UAAU;IAAAC,QAAA,EACrDjG,gBAAgB,gBACfZ,OAAA,CAAC1B,GAAG;MAACwI,EAAE,EAAE;QAAEC,OAAO,EAAE,MAAM;QAAEC,cAAc,EAAE,QAAQ;QAAEC,EAAE,EAAE;MAAE,CAAE;MAAAJ,QAAA,eAC5D7G,OAAA,CAAChB,gBAAgB;QAAAkI,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACjB,CAAC,gBAENrH,OAAA,CAAAE,SAAA;MAAA2G,QAAA,GACGN,WAAW,iBACVvG,OAAA,CAAAE,SAAA;QAAA2G,QAAA,gBACE7G,OAAA,CAACjB,KAAK;UACJuI,QAAQ,EAAC,SAAS;UAClBC,IAAI,eAAEvH,OAAA,CAACV,WAAW;YAAA4H,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAE;UACtBP,EAAE,EAAE;YAAEU,EAAE,EAAEjH,QAAQ,GAAG,CAAC,GAAG,CAAC;YAAEkH,EAAE,EAAElH,QAAQ,GAAG,GAAG,GAAG;UAAE,CAAE;UAAAsG,QAAA,eAErD7G,OAAA,CAACf,UAAU;YAACyI,OAAO,EAAC,WAAW;YAACZ,EAAE,EAAE;cAAEa,QAAQ,EAAEpH,QAAQ,GAAG,QAAQ,GAAG;YAAW,CAAE;YAAAsG,QAAA,EAAC;UAEpF;YAAAK,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAY;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACR,CAAC,EAGP/E,YAAY,CAACsF,aAAa,iBACzB5H,OAAA,CAACjB,KAAK;UACJuI,QAAQ,EAAC,OAAO;UAChBR,EAAE,EAAE;YAAEU,EAAE,EAAEjH,QAAQ,GAAG,CAAC,GAAG,CAAC;YAAEkH,EAAE,EAAElH,QAAQ,GAAG,GAAG,GAAG;UAAE,CAAE;UAAAsG,QAAA,eAErD7G,OAAA,CAACf,UAAU;YAACyI,OAAO,EAAC,WAAW;YAACZ,EAAE,EAAE;cAAEe,UAAU,EAAE;YAAO,CAAE;YAAAhB,QAAA,EACxDvE,YAAY,CAACsF;UAAa;YAAAV,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACjB;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACR,CACR;MAAA,eACD,CACH,eAEDrH,OAAA,CAACd,KAAK;QAAC4H,EAAE,EAAE;UAAEgB,CAAC,EAAEvH,QAAQ,GAAG,CAAC,GAAG,CAAC;UAAEiH,EAAE,EAAEjH,QAAQ,GAAG,CAAC,GAAG,CAAC;UAAEwH,SAAS,EAAExH,QAAQ,GAAG,CAAC,GAAG;QAAE,CAAE;QAAAsG,QAAA,gBACpF7G,OAAA,CAACf,UAAU;UAACyI,OAAO,EAAC,IAAI;UAACM,YAAY;UAAClB,EAAE,EAAE;YAAEa,QAAQ,EAAEpH,QAAQ,GAAG,QAAQ,GAAG,SAAS;YAAEiH,EAAE,EAAEjH,QAAQ,GAAG,GAAG,GAAG;UAAE,CAAE;UAAAsG,QAAA,EAAC;QAEjH;UAAAK,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAY,CAAC,eACbrH,OAAA,CAACvB,IAAI;UAACwJ,SAAS;UAACC,OAAO,EAAE3H,QAAQ,GAAG,CAAC,GAAG,CAAE;UAAAsG,QAAA,gBACxC7G,OAAA,CAACvB,IAAI;YAAC0J,IAAI;YAACC,EAAE,EAAE,EAAG;YAACC,EAAE,EAAE,CAAE;YAAAxB,QAAA,eACvB7G,OAAA,CAACzB,SAAS;cACRyE,IAAI,EAAC,SAAS;cACdsF,KAAK,EAAC,SAAS;cACfC,SAAS;cACTb,OAAO,EAAC,UAAU;cAClBzE,KAAK,EAAEnC,QAAQ,CAACE,OAAQ;cACxBwH,QAAQ,EAAE1F,gBAAiB;cAC3B2F,QAAQ;cACR7F,KAAK,EAAE,CAAC,CAACR,UAAU,CAACpB,OAAQ;cAC5B0H,UAAU,EAAEtG,UAAU,CAACpB,OAAQ;cAC/B2H,UAAU,EAAE;gBAAEC,KAAK,EAAE;kBAAEC,aAAa,EAAE;gBAAY;cAAE;YAAE;cAAA3B,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACvD;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACE,CAAC,eACPrH,OAAA,CAACvB,IAAI;YAAC0J,IAAI;YAACC,EAAE,EAAE,EAAG;YAACC,EAAE,EAAE,CAAE;YAAAxB,QAAA,eACvB7G,OAAA,CAACzB,SAAS;cACRyE,IAAI,EAAC,qBAAqB;cAC1BsF,KAAK,EAAC,qBAAqB;cAC3BC,SAAS;cACTb,OAAO,EAAC,UAAU;cAClBzE,KAAK,EAAEnC,QAAQ,CAACG,mBAAoB;cACpCuH,QAAQ,EAAE1F,gBAAiB;cAC3B2F,QAAQ;cACR7F,KAAK,EAAE,CAAC,CAACR,UAAU,CAACnB,mBAAoB;cACxCyH,UAAU,EAAEtG,UAAU,CAACnB;YAAoB;cAAAiG,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC5C;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACE,CAAC,eACPrH,OAAA,CAACvB,IAAI;YAAC0J,IAAI;YAACC,EAAE,EAAE,EAAG;YAACC,EAAE,EAAE,CAAE;YAAAxB,QAAA,eACvB7G,OAAA,CAACzB,SAAS;cACRyE,IAAI,EAAC,SAAS;cACdsF,KAAK,EAAC,SAAS;cACfC,SAAS;cACTb,OAAO,EAAC,UAAU;cAClBzE,KAAK,EAAEnC,QAAQ,CAACI,OAAQ;cACxBsH,QAAQ,EAAE1F,gBAAiB;cAC3BF,KAAK,EAAE,CAAC,CAACR,UAAU,CAAClB,OAAQ;cAC5BwH,UAAU,EAAEtG,UAAU,CAAClB;YAAQ;cAAAgG,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAChC;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACE,CAAC,eACPrH,OAAA,CAACvB,IAAI;YAAC0J,IAAI;YAACC,EAAE,EAAE,EAAG;YAACC,EAAE,EAAE,CAAE;YAAAxB,QAAA,eACvB7G,OAAA,CAACzB,SAAS;cACRyE,IAAI,EAAC,SAAS;cACdsF,KAAK,EAAC,SAAS;cACfC,SAAS;cACTb,OAAO,EAAC,UAAU;cAClBzE,KAAK,EAAEnC,QAAQ,CAACK,OAAQ;cACxBqH,QAAQ,EAAE1F,gBAAiB;cAC3BF,KAAK,EAAE,CAAC,CAACR,UAAU,CAACjB,OAAQ;cAC5BuH,UAAU,EAAEtG,UAAU,CAACjB;YAAQ;cAAA+F,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAChC;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACE,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACF,CAAC,eAERrH,OAAA,CAACd,KAAK;QAAC4H,EAAE,EAAE;UAAEgB,CAAC,EAAEvH,QAAQ,GAAG,CAAC,GAAG,CAAC;UAAEiH,EAAE,EAAEjH,QAAQ,GAAG,CAAC,GAAG,CAAC;UAAEwH,SAAS,EAAExH,QAAQ,GAAG,CAAC,GAAG;QAAE,CAAE;QAAAsG,QAAA,gBACpF7G,OAAA,CAACf,UAAU;UAACyI,OAAO,EAAC,IAAI;UAACM,YAAY;UAAClB,EAAE,EAAE;YAAEa,QAAQ,EAAEpH,QAAQ,GAAG,QAAQ,GAAG,SAAS;YAAEiH,EAAE,EAAEjH,QAAQ,GAAG,GAAG,GAAG;UAAE,CAAE;UAAAsG,QAAA,EAAC;QAEjH;UAAAK,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAY,CAAC,eACbrH,OAAA,CAACvB,IAAI;UAACwJ,SAAS;UAACC,OAAO,EAAE3H,QAAQ,GAAG,CAAC,GAAG,CAAE;UAAAsG,QAAA,gBACxC7G,OAAA,CAACvB,IAAI;YAAC0J,IAAI;YAACC,EAAE,EAAE,EAAG;YAACC,EAAE,EAAE,CAAE;YAAAxB,QAAA,eACvB7G,OAAA,CAACzB,SAAS;cACRyE,IAAI,EAAC,aAAa;cAClBsF,KAAK,EAAC,aAAa;cACnBC,SAAS;cACTb,OAAO,EAAC,UAAU;cAClBzE,KAAK,EAAEnC,QAAQ,CAACM,WAAY;cAC5BoH,QAAQ,EAAE1F,gBAAiB;cAC3BF,KAAK,EAAE,CAAC,CAACR,UAAU,CAAChB,WAAY;cAChCsH,UAAU,EAAEtG,UAAU,CAAChB;YAAY;cAAA8F,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACpC;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACE,CAAC,eACPrH,OAAA,CAACvB,IAAI;YAAC0J,IAAI;YAACC,EAAE,EAAE,EAAG;YAACC,EAAE,EAAE,CAAE;YAAAxB,QAAA,eACvB7G,OAAA,CAACzB,SAAS;cACRyE,IAAI,EAAC,WAAW;cAChBsF,KAAK,EAAC,WAAW;cACjBC,SAAS;cACTb,OAAO,EAAC,UAAU;cAClBzE,KAAK,EAAEnC,QAAQ,CAACO,SAAU;cAC1BmH,QAAQ,EAAE1F,gBAAiB;cAC3BF,KAAK,EAAE,CAAC,CAACR,UAAU,CAACf,SAAU;cAC9BqH,UAAU,EAAEtG,UAAU,CAACf;YAAU;cAAA6F,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAClC;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACE,CAAC,eACPrH,OAAA,CAACvB,IAAI;YAAC0J,IAAI;YAACC,EAAE,EAAE,EAAG;YAACC,EAAE,EAAE,CAAE;YAAAxB,QAAA,eACvB7G,OAAA,CAACzB,SAAS;cACRyE,IAAI,EAAC,cAAc;cACnBsF,KAAK,EAAC,mBAAmB;cACzBC,SAAS;cACTb,OAAO,EAAC,UAAU;cAClBzE,KAAK,EAAEnC,QAAQ,CAACQ,YAAa;cAC7BkH,QAAQ,EAAE1F,gBAAiB;cAC3BF,KAAK,EAAE,CAAC,CAACR,UAAU,CAACd,YAAa;cACjCoH,UAAU,EAAEtG,UAAU,CAACd,YAAY,IAAIgB,YAAY,CAAChB,YAAa;cACjEwH,mBAAmB,EAAE;gBACnBF,KAAK,EAAE;kBAAEG,KAAK,EAAEzG,YAAY,CAAChB,YAAY,GAAG,QAAQ,GAAG0H;gBAAU;cACnE;YAAE;cAAA9B,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACE,CAAC,eACPrH,OAAA,CAACvB,IAAI;YAAC0J,IAAI;YAACC,EAAE,EAAE,EAAG;YAACC,EAAE,EAAE,CAAE;YAAAxB,QAAA,eACvB7G,OAAA,CAACzB,SAAS;cACRyE,IAAI,EAAC,SAAS;cACdsF,KAAK,EAAC,SAAS;cACfC,SAAS;cACTb,OAAO,EAAC,UAAU;cAClBzE,KAAK,EAAEnC,QAAQ,CAACS,OAAQ;cACxBiH,QAAQ,EAAE1F,gBAAiB;cAC3BF,KAAK,EAAE,CAAC,CAACR,UAAU,CAACb,OAAQ;cAC5BmH,UAAU,EAAEtG,UAAU,CAACb,OAAO,IAAIe,YAAY,CAACf,OAAQ;cACvDuH,mBAAmB,EAAE;gBACnBF,KAAK,EAAE;kBAAEG,KAAK,EAAEzG,YAAY,CAACf,OAAO,GAAG,QAAQ,GAAGyH;gBAAU;cAC9D;YAAE;cAAA9B,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACE,CAAC,eACPrH,OAAA,CAACvB,IAAI;YAAC0J,IAAI;YAACC,EAAE,EAAE,EAAG;YAACC,EAAE,EAAE,CAAE;YAAAxB,QAAA,eACvB7G,OAAA,CAACtB,WAAW;cAAC6J,SAAS;cAAC3F,KAAK,EAAE,CAAC,CAACR,UAAU,CAACZ,EAAG;cAAAqF,QAAA,gBAC5C7G,OAAA,CAACrB,UAAU;gBAACsK,EAAE,EAAC,UAAU;gBAAApC,QAAA,EAAC;cAAe;gBAAAK,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAY,CAAC,eACtDrH,OAAA,CAACpB,MAAM;gBACLsK,OAAO,EAAC,UAAU;gBAClBlG,IAAI,EAAC,IAAI;gBACTC,KAAK,EAAEnC,QAAQ,CAACU,EAAG;gBACnB8G,KAAK,EAAC,iBAAiB;gBACvBE,QAAQ,EAAE1F,gBAAiB;gBAAA+D,QAAA,gBAE3B7G,OAAA,CAACnB,QAAQ;kBAACoE,KAAK,EAAC,GAAG;kBAAA4D,QAAA,EAAC;gBAAC;kBAAAK,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAU,CAAC,eAChCrH,OAAA,CAACnB,QAAQ;kBAACoE,KAAK,EAAC,GAAG;kBAAA4D,QAAA,EAAC;gBAAC;kBAAAK,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAU,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC1B,CAAC,EACRjF,UAAU,CAACZ,EAAE,iBAAIxB,OAAA,CAAClB,cAAc;gBAAA+H,QAAA,EAAEzE,UAAU,CAACZ;cAAE;gBAAA0F,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAiB,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACvD;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACV,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACF,CAAC,eAERrH,OAAA,CAACd,KAAK;QAAC4H,EAAE,EAAE;UAAEgB,CAAC,EAAEvH,QAAQ,GAAG,CAAC,GAAG,CAAC;UAAEiH,EAAE,EAAEjH,QAAQ,GAAG,CAAC,GAAG,CAAC;UAAEwH,SAAS,EAAExH,QAAQ,GAAG,CAAC,GAAG;QAAE,CAAE;QAAAsG,QAAA,gBACpF7G,OAAA,CAACf,UAAU;UAACyI,OAAO,EAAC,IAAI;UAACM,YAAY;UAAClB,EAAE,EAAE;YAAEa,QAAQ,EAAEpH,QAAQ,GAAG,QAAQ,GAAG,SAAS;YAAEiH,EAAE,EAAEjH,QAAQ,GAAG,GAAG,GAAG;UAAE,CAAE;UAAAsG,QAAA,EAAC;QAEjH;UAAAK,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAY,CAAC,eACbrH,OAAA,CAACvB,IAAI;UAACwJ,SAAS;UAACC,OAAO,EAAE3H,QAAQ,GAAG,CAAC,GAAG,CAAE;UAAAsG,QAAA,gBACxC7G,OAAA,CAACvB,IAAI;YAAC0J,IAAI;YAACC,EAAE,EAAE,EAAG;YAACC,EAAE,EAAE,CAAE;YAAAxB,QAAA,eACvB7G,OAAA,CAACzB,SAAS;cACRyE,IAAI,EAAC,qBAAqB;cAC1BsF,KAAK,EAAC,qBAAqB;cAC3BC,SAAS;cACTb,OAAO,EAAC,UAAU;cAClBzE,KAAK,EAAEnC,QAAQ,CAACW,mBAAoB;cACpC+G,QAAQ,EAAE1F,gBAAiB;cAC3BF,KAAK,EAAE,CAAC,CAACR,UAAU,CAACX,mBAAoB;cACxCiH,UAAU,EAAEtG,UAAU,CAACX;YAAoB;cAAAyF,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC5C;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACE,CAAC,eACPrH,OAAA,CAACvB,IAAI;YAAC0J,IAAI;YAACC,EAAE,EAAE,EAAG;YAACC,EAAE,EAAE,CAAE;YAAAxB,QAAA,eACvB7G,OAAA,CAACzB,SAAS;cACRyE,IAAI,EAAC,iBAAiB;cACtBsF,KAAK,EAAC,iBAAiB;cACvBC,SAAS;cACTb,OAAO,EAAC,UAAU;cAClBzE,KAAK,EAAEnC,QAAQ,CAACY,eAAgB;cAChC8G,QAAQ,EAAE1F,gBAAiB;cAC3BF,KAAK,EAAE,CAAC,CAACR,UAAU,CAACV,eAAgB;cACpCgH,UAAU,EAAEtG,UAAU,CAACV;YAAgB;cAAAwF,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACxC;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACE,CAAC,eACPrH,OAAA,CAACvB,IAAI;YAAC0J,IAAI;YAACC,EAAE,EAAE,EAAG;YAACC,EAAE,EAAE,CAAE;YAAAxB,QAAA,eACvB7G,OAAA,CAACzB,SAAS;cACRyE,IAAI,EAAC,6BAA6B;cAClCsF,KAAK,EAAC,6BAA6B;cACnCC,SAAS;cACTb,OAAO,EAAC,UAAU;cAClBzE,KAAK,EAAEnC,QAAQ,CAACa,2BAA4B;cAC5C6G,QAAQ,EAAE1F,gBAAiB;cAC3BF,KAAK,EAAE,CAAC,CAACR,UAAU,CAACT,2BAA4B;cAChD+G,UAAU,EAAEtG,UAAU,CAACT;YAA4B;cAAAuF,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACpD;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACE,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACF,CAAC,eAERrH,OAAA,CAACd,KAAK;QAAC4H,EAAE,EAAE;UAAEgB,CAAC,EAAEvH,QAAQ,GAAG,CAAC,GAAG,CAAC;UAAEiH,EAAE,EAAEjH,QAAQ,GAAG,CAAC,GAAG,CAAC;UAAEwH,SAAS,EAAExH,QAAQ,GAAG,CAAC,GAAG;QAAE,CAAE;QAAAsG,QAAA,gBACpF7G,OAAA,CAACf,UAAU;UAACyI,OAAO,EAAC,IAAI;UAACM,YAAY;UAAClB,EAAE,EAAE;YAAEa,QAAQ,EAAEpH,QAAQ,GAAG,QAAQ,GAAG,SAAS;YAAEiH,EAAE,EAAEjH,QAAQ,GAAG,GAAG,GAAG;UAAE,CAAE;UAAAsG,QAAA,EAAC;QAEjH;UAAAK,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAY,CAAC,eACbrH,OAAA,CAACvB,IAAI;UAACwJ,SAAS;UAACC,OAAO,EAAE3H,QAAQ,GAAG,CAAC,GAAG,CAAE;UAAAsG,QAAA,gBACxC7G,OAAA,CAACvB,IAAI;YAAC0J,IAAI;YAACC,EAAE,EAAE,EAAG;YAACC,EAAE,EAAE,CAAE;YAAAxB,QAAA,eACvB7G,OAAA,CAACzB,SAAS;cACRyE,IAAI,EAAC,mBAAmB;cACxBsF,KAAK,EAAC,mBAAmB;cACzBC,SAAS;cACTb,OAAO,EAAC,UAAU;cAClBzE,KAAK,EAAEnC,QAAQ,CAACc,iBAAkB;cAClC4G,QAAQ,EAAE1F,gBAAiB;cAC3BF,KAAK,EAAE,CAAC,CAACR,UAAU,CAACR,iBAAkB;cACtC8G,UAAU,EAAEtG,UAAU,CAACR;YAAkB;cAAAsF,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC1C;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACE,CAAC,eACPrH,OAAA,CAACvB,IAAI;YAAC0J,IAAI;YAACC,EAAE,EAAE,EAAG;YAACC,EAAE,EAAE,CAAE;YAAAxB,QAAA,eACvB7G,OAAA,CAACzB,SAAS;cACRyE,IAAI,EAAC,eAAe;cACpBsF,KAAK,EAAC,eAAe;cACrBC,SAAS;cACTb,OAAO,EAAC,UAAU;cAClBzE,KAAK,EAAEnC,QAAQ,CAACe,aAAc;cAC9B2G,QAAQ,EAAE1F,gBAAiB;cAC3BF,KAAK,EAAE,CAAC,CAACR,UAAU,CAACP,aAAc;cAClC6G,UAAU,EAAEtG,UAAU,CAACP;YAAc;cAAAqF,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACtC;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACE,CAAC,eACPrH,OAAA,CAACvB,IAAI;YAAC0J,IAAI;YAACC,EAAE,EAAE,EAAG;YAACC,EAAE,EAAE,CAAE;YAAAxB,QAAA,eACvB7G,OAAA,CAACzB,SAAS;cACRyE,IAAI,EAAC,2BAA2B;cAChCsF,KAAK,EAAC,2BAA2B;cACjCC,SAAS;cACTb,OAAO,EAAC,UAAU;cAClBzE,KAAK,EAAEnC,QAAQ,CAACgB,yBAA0B;cAC1C0G,QAAQ,EAAE1F,gBAAiB;cAC3BF,KAAK,EAAE,CAAC,CAACR,UAAU,CAACN,yBAA0B;cAC9C4G,UAAU,EAAEtG,UAAU,CAACN;YAA0B;cAAAoF,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAClD;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACE,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACF,CAAC,eAERrH,OAAA,CAACd,KAAK;QAAC4H,EAAE,EAAE;UAAEgB,CAAC,EAAEvH,QAAQ,GAAG,CAAC,GAAG,CAAC;UAAEiH,EAAE,EAAEjH,QAAQ,GAAG,CAAC,GAAG,CAAC;UAAEwH,SAAS,EAAExH,QAAQ,GAAG,CAAC,GAAG;QAAE,CAAE;QAAAsG,QAAA,gBACpF7G,OAAA,CAACf,UAAU;UAACyI,OAAO,EAAC,IAAI;UAACM,YAAY;UAAClB,EAAE,EAAE;YAAEa,QAAQ,EAAEpH,QAAQ,GAAG,QAAQ,GAAG;UAAU,CAAE;UAAAsG,QAAA,EAAC;QAEzF;UAAAK,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAY,CAAC,eACbrH,OAAA,CAACvB,IAAI;UAACwJ,SAAS;UAACC,OAAO,EAAE3H,QAAQ,GAAG,CAAC,GAAG,CAAE;UAAAsG,QAAA,eACxC7G,OAAA,CAACvB,IAAI;YAAC0J,IAAI;YAACC,EAAE,EAAE,EAAG;YAACC,EAAE,EAAE,CAAE;YAAAxB,QAAA,eACvB7G,OAAA,CAACzB,SAAS;cACRyE,IAAI,EAAC,eAAe;cACpBsF,KAAK,EAAC,eAAe;cACrBC,SAAS;cACTb,OAAO,EAAC,UAAU;cAClBzE,KAAK,EAAEnC,QAAQ,CAACiB,aAAc;cAC9ByG,QAAQ,EAAE1F,gBAAiB;cAC3B2F,QAAQ;cACR7F,KAAK,EAAE,CAAC,CAACR,UAAU,CAACL,aAAc;cAClC2G,UAAU,EAAEtG,UAAU,CAACL;YAAc;cAAAmF,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACtC;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACE;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAEH,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACF,CAAC,EAEP,CAAC9G,QAAQ,iBACRP,OAAA,CAAC1B,GAAG;QAACwI,EAAE,EAAE;UAAEqC,EAAE,EAAE,CAAC;UAAEpC,OAAO,EAAE,MAAM;UAAEC,cAAc,EAAE,QAAQ;UAAEoC,GAAG,EAAE;QAAE,CAAE;QAAAvC,QAAA,gBACpE7G,OAAA,CAACxB,MAAM;UACLkJ,OAAO,EAAC,UAAU;UAClBqB,KAAK,EAAC,WAAW;UACjBM,IAAI,EAAC,OAAO;UACZC,OAAO,EAAE5F,YAAa;UACtB6F,QAAQ,EAAE7I,OAAQ;UAClBoG,EAAE,EAAE;YAAE0C,QAAQ,EAAE;UAAI,CAAE;UAAA3C,QAAA,EACvB;QAED;UAAAK,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC,eACTrH,OAAA,CAACxB,MAAM;UACLiL,IAAI,EAAC,QAAQ;UACb/B,OAAO,EAAC,WAAW;UACnBqB,KAAK,EAAC,SAAS;UACfM,IAAI,EAAC,OAAO;UACZK,SAAS,eAAE1J,OAAA,CAACZ,QAAQ;YAAA8H,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAE;UACxBkC,QAAQ,EAAE7I,OAAQ;UAClBoG,EAAE,EAAE;YAAE0C,QAAQ,EAAE;UAAI,CAAE;UAAA3C,QAAA,EAErBnG,OAAO,gBAAGV,OAAA,CAAChB,gBAAgB;YAACqK,IAAI,EAAE;UAAG;YAAAnC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,GAAG;QAAY;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAClD,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACN,CACN,EACA9G,QAAQ,iBACPP,OAAA,CAAC1B,GAAG;QAACwI,EAAE,EAAE;UAAEqC,EAAE,EAAE,CAAC;UAAEpC,OAAO,EAAE,MAAM;UAAEC,cAAc,EAAE,UAAU;UAAEoC,GAAG,EAAE;QAAE,CAAE;QAAAvC,QAAA,eACtE7G,OAAA,CAACxB,MAAM;UACLiL,IAAI,EAAC,QAAQ;UACb/B,OAAO,EAAC,WAAW;UACnBqB,KAAK,EAAC,SAAS;UACfM,IAAI,EAAC,QAAQ;UACbK,SAAS,eAAE1J,OAAA,CAACZ,QAAQ;YAAA8H,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAE;UACxBkC,QAAQ,EAAE7I,OAAQ;UAClBoG,EAAE,EAAE;YAAE0C,QAAQ,EAAE;UAAI,CAAE;UAAA3C,QAAA,EAErBnG,OAAO,gBAAGV,OAAA,CAAChB,gBAAgB;YAACqK,IAAI,EAAE;UAAG;YAAAnC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,GAAG;QAAO;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC7C;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACN,CACN;IAAA,eACD;EACH;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACE,CAAC;AAEV,CAAC;AAAC7G,EAAA,CAjnBIL,gBAAgB;EAAA,QACHZ,WAAW;AAAA;AAAAoK,EAAA,GADxBxJ,gBAAgB;AAmnBtB,eAAeA,gBAAgB;AAAC,IAAAwJ,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}