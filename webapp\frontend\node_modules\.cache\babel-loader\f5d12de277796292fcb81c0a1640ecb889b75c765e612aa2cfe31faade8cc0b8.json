{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\PycharmProjects\\\\CMS\\\\webapp\\\\frontend\\\\src\\\\pages\\\\cavi\\\\VisualizzaCaviPage.js\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect } from 'react';\nimport { Box, Typography, Paper, Button, Grid, Card, CardContent, Alert, IconButton, Table, TableBody, TableCell, TableContainer, TableHead, TableRow, Tabs, Tab, TextField, MenuItem, Select, FormControl, InputLabel, Chip, Divider, CircularProgress, LinearProgress, Dialog, DialogTitle, DialogContent, DialogActions } from '@mui/material';\nimport { ArrowBack as ArrowBackIcon, Refresh as RefreshIcon, Home as HomeIcon, ViewList as ViewListIcon, ViewModule as ViewModuleIcon, FilterList as FilterListIcon, Sort as SortIcon, Info as InfoIcon, Search as SearchIcon, Clear as ClearIcon } from '@mui/icons-material';\nimport { useNavigate } from 'react-router-dom';\nimport { useAuth } from '../../context/AuthContext';\nimport caviService from '../../services/caviService';\nimport { normalizeInstallationStatus } from '../../utils/validationUtils';\nimport './CaviPage.css';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst VisualizzaCaviPage = () => {\n  _s();\n  var _sortOptions$find;\n  const {\n    isImpersonating,\n    user\n  } = useAuth();\n  const navigate = useNavigate();\n  const [cantiereId, setCantiereId] = useState(null);\n  const [cantiereName, setCantiereName] = useState('');\n  const [caviAttivi, setCaviAttivi] = useState([]);\n  const [caviSpare, setCaviSpare] = useState([]);\n  const [loading, setLoading] = useState(true);\n  const [error, setError] = useState(null);\n  const [viewMode, setViewMode] = useState('table'); // 'table' o 'card'\n\n  // Stato per filtri e ordinamento\n  const [filters, setFilters] = useState({\n    stato_installazione: '',\n    tipologia: '',\n    sort_by: '',\n    sort_order: 'asc'\n  });\n\n  // Stato per il dialogo dei dettagli del cavo\n  const [selectedCavo, setSelectedCavo] = useState(null);\n  const [detailsDialogOpen, setDetailsDialogOpen] = useState(false);\n\n  // Stato per le statistiche\n  const [stats, setStats] = useState(null);\n  const [loadingStats, setLoadingStats] = useState(false);\n\n  // Opzioni per i filtri\n  const [statiInstallazione, setStatiInstallazione] = useState([]);\n  const [tipologieCavi, setTipologieCavi] = useState([]);\n\n  // Opzioni di ordinamento\n  const sortOptions = [{\n    value: 'id_cavo',\n    label: 'ID Cavo'\n  }, {\n    value: 'metratura_reale',\n    label: 'Metratura Reale'\n  }, {\n    value: 'metri_teorici',\n    label: 'Metri Teorici'\n  }, {\n    value: 'stato_installazione',\n    label: 'Stato Installazione'\n  }, {\n    value: 'tipologia',\n    label: 'Tipologia'\n  }, {\n    value: 'timestamp',\n    label: 'Data Aggiornamento'\n  }];\n\n  // Stato per il pannello dei filtri - aperto di default per maggiore visibilità\n  const [filtersOpen, setFiltersOpen] = useState(true);\n\n  // Funzione per caricare gli stati di installazione disponibili\n  const loadStatiInstallazione = () => {\n    // Usa i valori dell'enum StatoInstallazione\n    setStatiInstallazione(['Installato', 'Da installare', 'In corso']);\n  };\n\n  // Carica i dati del cantiere e dei cavi\n  useEffect(() => {\n    // Carica gli stati di installazione all'avvio\n    loadStatiInstallazione();\n    const fetchData = async () => {\n      try {\n        console.log('Inizializzazione VisualizzaCaviPage...');\n\n        // Verifica che l'utente sia autenticato\n        const token = localStorage.getItem('token');\n        console.log('Token presente:', !!token);\n        if (!token) {\n          setError('Sessione scaduta. Effettua nuovamente il login.');\n          setLoading(false);\n          return;\n        }\n\n        // Recupera l'ID del cantiere selezionato dal localStorage\n        let selectedCantiereId = localStorage.getItem('selectedCantiereId');\n        let selectedCantiereName = localStorage.getItem('selectedCantiereName');\n        console.log('Cantiere selezionato dal localStorage:', {\n          selectedCantiereId,\n          selectedCantiereName\n        });\n        console.log('Dati utente:', user);\n\n        // Stampa tutti i dati nel localStorage per debug\n        console.log('DEBUG - Tutti i dati nel localStorage:');\n        for (let i = 0; i < localStorage.length; i++) {\n          const key = localStorage.key(i);\n          console.log(`${key}: ${localStorage.getItem(key)}`);\n        }\n\n        // SOLUZIONE DIRETTA: Ottieni l'ID del cantiere direttamente dal token JWT\n        if ((user === null || user === void 0 ? void 0 : user.role) === 'cantieri_user') {\n          console.log('Utente cantiere rilevato, tentativo di recupero ID cantiere dai dati utente');\n\n          // Verifica se l'utente ha un ID cantiere nei dati utente\n          if (user.cantiere_id) {\n            console.log('Trovato ID cantiere nei dati utente:', user.cantiere_id);\n            selectedCantiereId = user.cantiere_id.toString();\n            selectedCantiereName = user.cantiere_name || `Cantiere ${user.cantiere_id}`;\n\n            // Salva l'ID e il nome del cantiere nel localStorage\n            localStorage.setItem('selectedCantiereId', selectedCantiereId);\n            localStorage.setItem('selectedCantiereName', selectedCantiereName);\n            console.log('Salvato ID cantiere nel localStorage:', selectedCantiereId);\n          } else {\n            // Tentativo di recupero dal token JWT\n            try {\n              console.log('Tentativo di decodifica del token JWT per recuperare l\\'ID cantiere');\n              const token = localStorage.getItem('token');\n              if (token) {\n                // Decodifica il token JWT (senza verifica della firma)\n                const base64Url = token.split('.')[1];\n                const base64 = base64Url.replace(/-/g, '+').replace(/_/g, '/');\n                const jsonPayload = decodeURIComponent(atob(base64).split('').map(c => {\n                  return '%' + ('00' + c.charCodeAt(0).toString(16)).slice(-2);\n                }).join(''));\n                const payload = JSON.parse(jsonPayload);\n                console.log('Payload del token JWT:', payload);\n                if (payload.cantiere_id) {\n                  console.log('Trovato ID cantiere nel token JWT:', payload.cantiere_id);\n                  selectedCantiereId = payload.cantiere_id.toString();\n                  // Usa un nome generico se non disponibile\n                  selectedCantiereName = `Cantiere ${payload.cantiere_id}`;\n\n                  // Salva l'ID e il nome del cantiere nel localStorage\n                  localStorage.setItem('selectedCantiereId', selectedCantiereId);\n                  localStorage.setItem('selectedCantiereName', selectedCantiereName);\n                  console.log('Salvato ID cantiere nel localStorage:', selectedCantiereId);\n                }\n              }\n            } catch (e) {\n              console.error('Errore durante la decodifica del token JWT:', e);\n            }\n          }\n        }\n\n        // SOLUZIONE TEMPORANEA: Se ancora non abbiamo un ID cantiere, usa un valore hardcoded per debug\n        if (!selectedCantiereId || selectedCantiereId === 'undefined' || selectedCantiereId === 'null') {\n          console.warn('ATTENZIONE: Nessun ID cantiere trovato, utilizzo valore hardcoded per debug');\n          // Usa il primo cantiere disponibile (questo è solo per debug)\n          selectedCantiereId = '1'; // Sostituisci con un ID cantiere valido nel tuo database\n          selectedCantiereName = 'Cantiere Debug';\n\n          // Salva l'ID e il nome del cantiere nel localStorage\n          localStorage.setItem('selectedCantiereId', selectedCantiereId);\n          localStorage.setItem('selectedCantiereName', selectedCantiereName);\n          console.log('Salvato ID cantiere hardcoded nel localStorage:', selectedCantiereId);\n        }\n\n        // Verifica finale\n        if (!selectedCantiereId) {\n          setError('Nessun cantiere selezionato. Torna alla pagina dei cantieri.');\n          setLoading(false);\n          return;\n        }\n\n        // Verifica che l'ID del cantiere sia un numero valido\n        const cantiereIdNum = parseInt(selectedCantiereId, 10);\n        console.log('ID cantiere convertito a numero:', cantiereIdNum);\n        if (isNaN(cantiereIdNum)) {\n          setError(`ID cantiere non valido: ${selectedCantiereId}. Torna alla pagina dei cantieri.`);\n          setLoading(false);\n          return;\n        }\n\n        // Usa il numero convertito, non la stringa\n        setCantiereId(cantiereIdNum);\n        setCantiereName(selectedCantiereName || `Cantiere ${cantiereIdNum}`);\n\n        // Carica le statistiche dei cavi\n        try {\n          setLoadingStats(true);\n          console.log('Caricamento statistiche cavi per cantiere:', cantiereIdNum);\n          const statsData = await caviService.getCaviStats(cantiereIdNum);\n          console.log('Statistiche cavi caricate:', statsData);\n          setStats(statsData);\n\n          // Estrai gli stati di installazione e le tipologie per i filtri\n          if (statsData && statsData.stati) {\n            const stati = statsData.stati.map(item => item.stato).filter(stato => stato !== 'Non specificato');\n            setStatiInstallazione(stati);\n          }\n          if (statsData && statsData.tipologie) {\n            const tipologie = statsData.tipologie.map(item => item.tipologia).filter(tipo => tipo !== 'Non specificata');\n            setTipologieCavi(tipologie);\n          }\n          setLoadingStats(false);\n        } catch (statsError) {\n          console.error('Errore nel caricamento delle statistiche:', statsError);\n          setLoadingStats(false);\n          // Non interrompere il flusso se le statistiche falliscono\n        }\n\n        // Carica i cavi attivi con gestione degli errori migliorata\n        console.log('Caricamento cavi attivi per cantiere:', cantiereIdNum);\n        try {\n          // Imposta un timeout per evitare che la richiesta rimanga bloccata\n          const timeoutPromise = new Promise((_, reject) => {\n            setTimeout(() => reject(new Error('Timeout durante il caricamento dei cavi attivi')), 30000); // Aumentato a 30 secondi\n          });\n\n          // Esegui la richiesta con un timeout di sicurezza e applica i filtri\n          console.log('Iniziando chiamata API per cavi attivi con filtri:', filters);\n          const caviPromise = caviService.getCavi(cantiereIdNum, 0, filters);\n          const attivi = await Promise.race([caviPromise, timeoutPromise]);\n          console.log('Cavi attivi caricati:', attivi);\n          console.log('Numero di cavi attivi trovati:', attivi ? attivi.length : 0);\n          if (attivi && attivi.length > 0) {\n            console.log('Primo cavo attivo:', attivi[0]);\n          } else {\n            console.warn('Nessun cavo attivo trovato per il cantiere', cantiereIdNum);\n          }\n          setCaviAttivi(attivi || []);\n        } catch (caviError) {\n          console.error('Errore nel caricamento dei cavi attivi:', caviError);\n          console.error('Dettagli errore cavi attivi:', {\n            message: caviError.message,\n            status: caviError.status,\n            data: caviError.data,\n            stack: caviError.stack,\n            code: caviError.code,\n            name: caviError.name,\n            response: caviError.response ? {\n              status: caviError.response.status,\n              statusText: caviError.response.statusText,\n              data: caviError.response.data\n            } : 'No response'\n          });\n\n          // Non interrompere il flusso, continua con i cavi spare\n          setCaviAttivi([]);\n          console.warn('Continuazione del flusso dopo errore nei cavi attivi');\n\n          // Aggiungi un messaggio di errore visibile all'utente\n          setError(`Errore nel caricamento dei cavi attivi: ${caviError.message}. Controlla la console per maggiori dettagli.`);\n        }\n\n        // Carica i cavi spare con gestione degli errori migliorata\n        console.log('Caricamento cavi spare per cantiere:', cantiereIdNum);\n        try {\n          // Imposta un timeout per evitare che la richiesta rimanga bloccata\n          const timeoutPromise = new Promise((_, reject) => {\n            setTimeout(() => reject(new Error('Timeout durante il caricamento dei cavi spare')), 30000); // Aumentato a 30 secondi\n          });\n\n          // Esegui la richiesta con un timeout di sicurezza\n          console.log('Iniziando chiamata API per cavi spare...');\n          // Non applichiamo i filtri ai cavi spare, solo agli attivi\n          const sparePromise = caviService.getCavi(cantiereIdNum, 3);\n          const spare = await Promise.race([sparePromise, timeoutPromise]);\n          console.log('Cavi spare caricati:', spare);\n          console.log('Numero di cavi spare trovati:', spare ? spare.length : 0);\n          if (spare && spare.length > 0) {\n            console.log('Primo cavo spare:', spare[0]);\n          } else {\n            console.warn('Nessun cavo spare trovato per il cantiere', cantiereIdNum);\n          }\n          setCaviSpare(spare || []);\n        } catch (spareError) {\n          console.error('Errore nel caricamento dei cavi spare:', spareError);\n          console.error('Dettagli errore cavi spare:', {\n            message: spareError.message,\n            status: spareError.status,\n            data: spareError.data,\n            stack: spareError.stack,\n            code: spareError.code,\n            name: spareError.name,\n            response: spareError.response ? {\n              status: spareError.response.status,\n              statusText: spareError.response.statusText,\n              data: spareError.response.data\n            } : 'No response'\n          });\n\n          // Non interrompere il flusso, imposta un array vuoto\n          setCaviSpare([]);\n\n          // Aggiungi un messaggio di errore visibile all'utente se non c'è già un errore per i cavi attivi\n          if (!error) {\n            setError(`Errore nel caricamento dei cavi spare: ${spareError.message}. Controlla la console per maggiori dettagli.`);\n          }\n        }\n\n        // Se siamo arrivati qui, almeno abbiamo caricato l'interfaccia di base\n        setLoading(false);\n      } catch (err) {\n        var _err$response, _err$response2, _err$response3, _err$response4, _err$response5, _err$response5$data;\n        console.error('Errore nel caricamento dei cavi:', err);\n        console.error('Dettagli errore generale:', {\n          message: err.message,\n          status: err.status || ((_err$response = err.response) === null || _err$response === void 0 ? void 0 : _err$response.status),\n          data: err.data || ((_err$response2 = err.response) === null || _err$response2 === void 0 ? void 0 : _err$response2.data),\n          stack: err.stack\n        });\n\n        // Estrai il messaggio di errore dettagliato\n        let errorMessage = 'Errore sconosciuto';\n        if (err.message && err.message.includes('ID cantiere non valido')) {\n          errorMessage = err.message;\n        } else if (err.status === 401 || err.status === 403 || ((_err$response3 = err.response) === null || _err$response3 === void 0 ? void 0 : _err$response3.status) === 401 || ((_err$response4 = err.response) === null || _err$response4 === void 0 ? void 0 : _err$response4.status) === 403) {\n          errorMessage = 'Sessione scaduta o non autorizzata. Effettua nuovamente il login.';\n        } else if ((_err$response5 = err.response) !== null && _err$response5 !== void 0 && (_err$response5$data = _err$response5.data) !== null && _err$response5$data !== void 0 && _err$response5$data.detail) {\n          // Estrai il messaggio di errore dettagliato dall'API\n          errorMessage = `Errore API: ${err.response.data.detail}`;\n        } else if (err.code === 'ERR_NETWORK') {\n          // Errore di rete\n          errorMessage = 'Network Error. Verifica che il backend sia in esecuzione e accessibile.';\n        } else if (err.message) {\n          errorMessage = err.message;\n        }\n        setError(`Impossibile caricare i cavi: ${errorMessage}. Riprova più tardi.`);\n\n        // Imposta array vuoti per evitare errori di rendering\n        setCaviAttivi([]);\n        setCaviSpare([]);\n      } finally {\n        setLoading(false);\n      }\n    };\n    fetchData();\n  }, [filters]); // Ricarica i dati quando cambiano i filtri\n\n  // Funzione per gestire il cambio dei filtri\n  const handleFilterChange = event => {\n    const {\n      name,\n      value\n    } = event.target;\n    console.log(`Filtro cambiato: ${name} = ${value}`);\n\n    // Aggiorna lo stato dei filtri\n    const newFilters = {\n      ...filters,\n      [name]: value\n    };\n    console.log('Nuovi filtri:', newFilters);\n    setFilters(newFilters);\n  };\n\n  // Funzione per resettare i filtri\n  const resetFilters = () => {\n    setFilters({\n      stato_installazione: '',\n      tipologia: '',\n      sort_by: '',\n      sort_order: 'asc'\n    });\n  };\n\n  // Funzione per aprire il dialogo dei dettagli del cavo\n  const handleOpenDetails = cavo => {\n    setSelectedCavo(cavo);\n    setDetailsDialogOpen(true);\n  };\n\n  // Funzione per chiudere il dialogo dei dettagli del cavo\n  const handleCloseDetails = () => {\n    setDetailsDialogOpen(false);\n    setSelectedCavo(null);\n  };\n\n  // Nessuna funzione di navigazione necessaria, tutto è gestito dal menu principale\n\n  // Funzione per visualizzare i cavi in formato tabellare\n  const renderCaviTable = cavi => {\n    if (!cavi || cavi.length === 0) {\n      return /*#__PURE__*/_jsxDEV(Alert, {\n        severity: \"info\",\n        children: [\"Nessun cavo trovato in questa categoria.\", /*#__PURE__*/_jsxDEV(Button, {\n          variant: \"text\",\n          color: \"primary\",\n          onClick: () => window.location.reload(),\n          sx: {\n            ml: 2\n          },\n          children: \"Riprova\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 421,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 419,\n        columnNumber: 9\n      }, this);\n    }\n\n    // Funzione per ottenere il colore in base allo stato di installazione\n    const getStatusColor = stato => {\n      // Normalizza lo stato per gestire le variazioni di maiuscole/minuscole\n      const statoUpper = (stato || '').toUpperCase();\n      switch (statoUpper) {\n        case 'POSATO':\n        case 'INSTALLATO':\n          // Valore dall'enum StatoInstallazione\n          return '#4caf50';\n        // Verde\n        case 'COLLEGATO PARTENZA':\n          return '#ff9800';\n        // Arancione\n        case 'COLLEGATO ARRIVO':\n          return '#ff9800';\n        // Arancione\n        case 'COLLEGATO':\n          return '#2196f3';\n        // Blu\n        case 'DA POSARE':\n        case 'DA INSTALLARE':\n          // Valore dall'enum StatoInstallazione\n          return '#f44336';\n        // Rosso\n        case 'IN POSA':\n        case 'IN CORSO': // Valore dall'enum StatoInstallazione\n        case 'IN INSTALLAZIONE':\n          return '#9c27b0';\n        // Viola\n        default:\n          console.log('Stato non riconosciuto:', stato);\n          return '#757575';\n        // Grigio per stati sconosciuti\n      }\n    };\n\n    // Funzione per ottenere lo stato di collegamento\n    const getConnectionStatus = collegamenti => {\n      if (!collegamenti) return 'NON COLLEGATO';\n      switch (collegamenti) {\n        case 1:\n          return 'COLLEGATO PARTENZA';\n        case 2:\n          return 'COLLEGATO ARRIVO';\n        case 3:\n          return 'COLLEGATO';\n        default:\n          return 'NON COLLEGATO';\n      }\n    };\n\n    // Utilizziamo la funzione normalizeInstallationStatus importata da validationUtils\n\n    if (viewMode === 'table') {\n      return /*#__PURE__*/_jsxDEV(TableContainer, {\n        component: Paper,\n        sx: {\n          mt: 2,\n          overflowX: 'auto'\n        },\n        children: /*#__PURE__*/_jsxDEV(Table, {\n          size: \"small\",\n          children: [/*#__PURE__*/_jsxDEV(TableHead, {\n            children: /*#__PURE__*/_jsxDEV(TableRow, {\n              children: [/*#__PURE__*/_jsxDEV(TableCell, {\n                children: \"ID Cavo\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 481,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n                children: \"Utility\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 482,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n                children: \"Tipologia\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 483,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n                children: \"N.Cond\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 484,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n                children: \"Sezione\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 485,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n                children: \"Ubicaz.Part.\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 486,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n                children: \"Ubicaz.Arr.\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 487,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n                children: \"Metri T.\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 488,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n                children: \"Metri R.\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 489,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n                children: \"Stato\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 490,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n                children: \"Collegamenti\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 491,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n                children: \"Bobina\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 492,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n                children: \"Azioni\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 493,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 480,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 479,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(TableBody, {\n            children: cavi.map(cavo => {\n              // Formatta i valori per la visualizzazione come nella CLI\n              const id_cavo = String(cavo.id_cavo).replace('$', '');\n              const utility = cavo.utility || '-';\n              const tipologia = cavo.tipologia || '-';\n\n              // Gestisci n_conduttori come stringa o numero\n              let n_conduttori = '-';\n              try {\n                const n_cond_val = parseInt(cavo.n_conduttori, 10) || 0;\n                n_conduttori = n_cond_val > 0 ? String(n_cond_val) : '-';\n              } catch (e) {\n                n_conduttori = cavo.n_conduttori || '-';\n              }\n\n              // Gestisci sezione come stringa\n              let sezione = '-';\n              const sezione_val = cavo.sezione;\n              if (typeof sezione_val === 'number' && sezione_val === 0) {\n                sezione = '-';\n              } else {\n                sezione = sezione_val ? String(sezione_val) : '-';\n              }\n              const ubicazione_partenza = cavo.ubicazione_partenza || '-';\n              const ubicazione_arrivo = cavo.ubicazione_arrivo || '-';\n              const metri_teorici = cavo.metri_teorici ? `${parseFloat(cavo.metri_teorici).toFixed(2)}` : '-';\n              const metri_reali = cavo.metratura_reale ? `${parseFloat(cavo.metratura_reale).toFixed(2)}` : '-';\n              const stato = cavo.stato_installazione || 'Da installare';\n              const collegamenti = getConnectionStatus(cavo.collegamenti);\n              const bobina = cavo.id_bobina || '-';\n\n              // Determina il colore in base allo stato\n              const statusColor = getStatusColor(stato);\n              return /*#__PURE__*/_jsxDEV(TableRow, {\n                sx: {\n                  '&:hover': {\n                    backgroundColor: '#f5f5f5'\n                  }\n                },\n                onClick: () => handleOpenDetails(cavo),\n                children: [/*#__PURE__*/_jsxDEV(TableCell, {\n                  children: id_cavo\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 538,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n                  children: utility\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 539,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n                  children: tipologia\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 540,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n                  children: n_conduttori\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 541,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n                  children: sezione\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 542,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n                  children: ubicazione_partenza\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 543,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n                  children: ubicazione_arrivo\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 544,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n                  children: metri_teorici\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 545,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n                  children: metri_reali\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 546,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n                  children: /*#__PURE__*/_jsxDEV(Chip, {\n                    label: normalizeInstallationStatus(stato),\n                    size: \"small\",\n                    sx: {\n                      backgroundColor: statusColor,\n                      color: '#fff',\n                      fontWeight: 'bold'\n                    }\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 548,\n                    columnNumber: 23\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 547,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n                  children: collegamenti\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 558,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n                  children: bobina\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 559,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n                  children: /*#__PURE__*/_jsxDEV(IconButton, {\n                    size: \"small\",\n                    onClick: e => {\n                      e.stopPropagation();\n                      handleOpenDetails(cavo);\n                    },\n                    title: \"Visualizza dettagli\",\n                    children: /*#__PURE__*/_jsxDEV(InfoIcon, {\n                      fontSize: \"small\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 569,\n                      columnNumber: 25\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 561,\n                    columnNumber: 23\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 560,\n                  columnNumber: 21\n                }, this)]\n              }, cavo.id_cavo, true, {\n                fileName: _jsxFileName,\n                lineNumber: 533,\n                columnNumber: 19\n              }, this);\n            })\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 496,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 478,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 477,\n        columnNumber: 9\n      }, this);\n    } else {\n      // Visualizzazione a schede (card)\n      return /*#__PURE__*/_jsxDEV(Grid, {\n        container: true,\n        spacing: 2,\n        children: cavi.map(cavo => {\n          const stato = cavo.stato_installazione || 'Da installare';\n          const statusColor = getStatusColor(stato);\n          const collegamenti = getConnectionStatus(cavo.collegamenti);\n          return /*#__PURE__*/_jsxDEV(Grid, {\n            item: true,\n            xs: 12,\n            sm: 6,\n            md: 4,\n            children: /*#__PURE__*/_jsxDEV(Card, {\n              sx: {\n                cursor: 'pointer',\n                '&:hover': {\n                  boxShadow: 6\n                },\n                borderLeft: `5px solid ${statusColor}`\n              },\n              onClick: () => handleOpenDetails(cavo),\n              children: /*#__PURE__*/_jsxDEV(CardContent, {\n                children: [/*#__PURE__*/_jsxDEV(Typography, {\n                  variant: \"h6\",\n                  component: \"div\",\n                  sx: {\n                    display: 'flex',\n                    justifyContent: 'space-between'\n                  },\n                  children: [cavo.id_cavo, /*#__PURE__*/_jsxDEV(Chip, {\n                    label: normalizeInstallationStatus(stato),\n                    size: \"small\",\n                    sx: {\n                      backgroundColor: statusColor,\n                      color: '#fff',\n                      fontWeight: 'bold'\n                    }\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 601,\n                    columnNumber: 23\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 599,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                  variant: \"body2\",\n                  color: \"text.secondary\",\n                  children: [\"Sistema: \", cavo.sistema || 'N/A']\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 611,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                  variant: \"body2\",\n                  color: \"text.secondary\",\n                  children: [\"Tipologia: \", cavo.tipologia || 'N/A']\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 614,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                  variant: \"body2\",\n                  color: \"text.secondary\",\n                  children: [\"Partenza: \", cavo.ubicazione_partenza || 'N/A', \" - \", cavo.utenza_partenza || 'N/A']\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 617,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                  variant: \"body2\",\n                  color: \"text.secondary\",\n                  children: [\"Arrivo: \", cavo.ubicazione_arrivo || 'N/A', \" - \", cavo.utenza_arrivo || 'N/A']\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 620,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                  variant: \"body2\",\n                  color: \"text.secondary\",\n                  children: [\"Metri teorici: \", cavo.metri_teorici || 'N/A']\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 623,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                  variant: \"body2\",\n                  color: \"text.secondary\",\n                  children: [\"Metratura reale: \", cavo.metratura_reale || '0']\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 626,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                  variant: \"body2\",\n                  color: \"text.secondary\",\n                  children: [\"Collegamenti: \", collegamenti]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 629,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                  variant: \"body2\",\n                  color: \"text.secondary\",\n                  children: [\"Bobina: \", cavo.id_bobina || 'N/A']\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 632,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 598,\n                columnNumber: 19\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 590,\n              columnNumber: 17\n            }, this)\n          }, cavo.id_cavo, false, {\n            fileName: _jsxFileName,\n            lineNumber: 589,\n            columnNumber: 15\n          }, this);\n        })\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 582,\n        columnNumber: 9\n      }, this);\n    }\n  };\n\n  // Gestisce il cambio di modalità di visualizzazione\n  const handleViewModeChange = mode => {\n    setViewMode(mode);\n  };\n\n  // Renderizza il dialogo dei dettagli del cavo\n  const renderDetailsDialog = () => {\n    if (!selectedCavo) return null;\n    return /*#__PURE__*/_jsxDEV(Dialog, {\n      open: detailsDialogOpen,\n      onClose: handleCloseDetails,\n      maxWidth: \"md\",\n      fullWidth: true,\n      children: [/*#__PURE__*/_jsxDEV(DialogTitle, {\n        children: [\"Dettagli Cavo: \", selectedCavo.id_cavo]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 656,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(DialogContent, {\n        dividers: true,\n        children: /*#__PURE__*/_jsxDEV(Grid, {\n          container: true,\n          spacing: 2,\n          children: [/*#__PURE__*/_jsxDEV(Grid, {\n            item: true,\n            xs: 12,\n            md: 6,\n            children: [/*#__PURE__*/_jsxDEV(Typography, {\n              variant: \"subtitle1\",\n              gutterBottom: true,\n              children: \"Informazioni Generali\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 662,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Box, {\n              sx: {\n                mb: 2\n              },\n              children: [/*#__PURE__*/_jsxDEV(Typography, {\n                variant: \"body2\",\n                children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n                  children: \"Sistema:\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 664,\n                  columnNumber: 45\n                }, this), \" \", selectedCavo.sistema || 'N/A']\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 664,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                variant: \"body2\",\n                children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n                  children: \"Utility:\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 665,\n                  columnNumber: 45\n                }, this), \" \", selectedCavo.utility || 'N/A']\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 665,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                variant: \"body2\",\n                children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n                  children: \"Tipologia:\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 666,\n                  columnNumber: 45\n                }, this), \" \", selectedCavo.tipologia || 'N/A']\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 666,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                variant: \"body2\",\n                children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n                  children: \"Colore:\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 667,\n                  columnNumber: 45\n                }, this), \" \", selectedCavo.colore_cavo || 'N/A']\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 667,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                variant: \"body2\",\n                children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n                  children: \"N. Conduttori:\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 668,\n                  columnNumber: 45\n                }, this), \" \", selectedCavo.n_conduttori || 'N/A']\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 668,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                variant: \"body2\",\n                children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n                  children: \"Sezione:\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 669,\n                  columnNumber: 45\n                }, this), \" \", selectedCavo.sezione || 'N/A']\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 669,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                variant: \"body2\",\n                children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n                  children: \"SH:\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 670,\n                  columnNumber: 45\n                }, this), \" \", selectedCavo.sh || 'N/A']\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 670,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 663,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Typography, {\n              variant: \"subtitle1\",\n              gutterBottom: true,\n              children: \"Partenza\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 673,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Box, {\n              sx: {\n                mb: 2\n              },\n              children: [/*#__PURE__*/_jsxDEV(Typography, {\n                variant: \"body2\",\n                children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n                  children: \"Ubicazione:\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 675,\n                  columnNumber: 45\n                }, this), \" \", selectedCavo.ubicazione_partenza || 'N/A']\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 675,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                variant: \"body2\",\n                children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n                  children: \"Utenza:\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 676,\n                  columnNumber: 45\n                }, this), \" \", selectedCavo.utenza_partenza || 'N/A']\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 676,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                variant: \"body2\",\n                children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n                  children: \"Descrizione:\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 677,\n                  columnNumber: 45\n                }, this), \" \", selectedCavo.descrizione_utenza_partenza || 'N/A']\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 677,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                variant: \"body2\",\n                children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n                  children: \"Responsabile:\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 678,\n                  columnNumber: 45\n                }, this), \" \", selectedCavo.responsabile_partenza || 'N/A']\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 678,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                variant: \"body2\",\n                children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n                  children: \"Comanda:\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 679,\n                  columnNumber: 45\n                }, this), \" \", selectedCavo.comanda_partenza || 'N/A']\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 679,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 674,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 661,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Grid, {\n            item: true,\n            xs: 12,\n            md: 6,\n            children: [/*#__PURE__*/_jsxDEV(Typography, {\n              variant: \"subtitle1\",\n              gutterBottom: true,\n              children: \"Arrivo\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 684,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Box, {\n              sx: {\n                mb: 2\n              },\n              children: [/*#__PURE__*/_jsxDEV(Typography, {\n                variant: \"body2\",\n                children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n                  children: \"Ubicazione:\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 686,\n                  columnNumber: 45\n                }, this), \" \", selectedCavo.ubicazione_arrivo || 'N/A']\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 686,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                variant: \"body2\",\n                children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n                  children: \"Utenza:\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 687,\n                  columnNumber: 45\n                }, this), \" \", selectedCavo.utenza_arrivo || 'N/A']\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 687,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                variant: \"body2\",\n                children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n                  children: \"Descrizione:\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 688,\n                  columnNumber: 45\n                }, this), \" \", selectedCavo.descrizione_utenza_arrivo || 'N/A']\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 688,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                variant: \"body2\",\n                children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n                  children: \"Responsabile:\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 689,\n                  columnNumber: 45\n                }, this), \" \", selectedCavo.responsabile_arrivo || 'N/A']\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 689,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                variant: \"body2\",\n                children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n                  children: \"Comanda:\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 690,\n                  columnNumber: 45\n                }, this), \" \", selectedCavo.comanda_arrivo || 'N/A']\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 690,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 685,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Typography, {\n              variant: \"subtitle1\",\n              gutterBottom: true,\n              children: \"Installazione\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 693,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Box, {\n              sx: {\n                mb: 2\n              },\n              children: [/*#__PURE__*/_jsxDEV(Typography, {\n                variant: \"body2\",\n                children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n                  children: \"Metri Teorici:\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 695,\n                  columnNumber: 45\n                }, this), \" \", selectedCavo.metri_teorici || 'N/A']\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 695,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                variant: \"body2\",\n                children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n                  children: \"Metratura Reale:\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 696,\n                  columnNumber: 45\n                }, this), \" \", selectedCavo.metratura_reale || '0']\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 696,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                variant: \"body2\",\n                children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n                  children: \"Stato:\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 697,\n                  columnNumber: 45\n                }, this), \" \", normalizeInstallationStatus(selectedCavo.stato_installazione)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 697,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                variant: \"body2\",\n                children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n                  children: \"Collegamenti:\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 698,\n                  columnNumber: 45\n                }, this), \" \", selectedCavo.collegamenti || '0']\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 698,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                variant: \"body2\",\n                children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n                  children: \"Bobina:\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 699,\n                  columnNumber: 45\n                }, this), \" \", selectedCavo.id_bobina || 'N/A']\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 699,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                variant: \"body2\",\n                children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n                  children: \"Responsabile Posa:\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 700,\n                  columnNumber: 45\n                }, this), \" \", selectedCavo.responsabile_posa || 'N/A']\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 700,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                variant: \"body2\",\n                children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n                  children: \"Comanda Posa:\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 701,\n                  columnNumber: 45\n                }, this), \" \", selectedCavo.comanda_posa || 'N/A']\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 701,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                variant: \"body2\",\n                children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n                  children: \"Ultimo Aggiornamento:\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 702,\n                  columnNumber: 45\n                }, this), \" \", new Date(selectedCavo.timestamp).toLocaleString()]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 702,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 694,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 683,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 660,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 659,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(DialogActions, {\n        children: /*#__PURE__*/_jsxDEV(Button, {\n          onClick: handleCloseDetails,\n          children: \"Chiudi\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 708,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 707,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 655,\n      columnNumber: 7\n    }, this);\n  };\n\n  // Renderizza il pannello dei filtri\n  const renderFilterPanel = () => {\n    return /*#__PURE__*/_jsxDEV(Paper, {\n      sx: {\n        mb: 3,\n        p: 2,\n        display: filtersOpen ? 'block' : 'none',\n        border: '1px solid #e0e0e0'\n      },\n      children: [/*#__PURE__*/_jsxDEV(Box, {\n        sx: {\n          display: 'flex',\n          justifyContent: 'space-between',\n          alignItems: 'center',\n          mb: 2\n        },\n        children: [/*#__PURE__*/_jsxDEV(Typography, {\n          variant: \"h6\",\n          children: \"Filtri e Ordinamento\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 719,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Button, {\n          variant: \"outlined\",\n          color: \"primary\",\n          size: \"small\",\n          onClick: resetFilters,\n          startIcon: /*#__PURE__*/_jsxDEV(ClearIcon, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 725,\n            columnNumber: 24\n          }, this),\n          children: \"Resetta Filtri\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 720,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 718,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Divider, {\n        sx: {\n          mb: 2\n        }\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 730,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Grid, {\n        container: true,\n        spacing: 2,\n        children: [/*#__PURE__*/_jsxDEV(Grid, {\n          item: true,\n          xs: 12,\n          sm: 6,\n          md: 3,\n          children: /*#__PURE__*/_jsxDEV(FormControl, {\n            fullWidth: true,\n            size: \"small\",\n            children: [/*#__PURE__*/_jsxDEV(InputLabel, {\n              id: \"stato-installazione-label\",\n              children: \"Stato Installazione\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 734,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Select, {\n              labelId: \"stato-installazione-label\",\n              id: \"stato-installazione\",\n              name: \"stato_installazione\",\n              value: filters.stato_installazione,\n              label: \"Stato Installazione\",\n              onChange: handleFilterChange,\n              children: [/*#__PURE__*/_jsxDEV(MenuItem, {\n                value: \"\",\n                children: \"Tutti\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 743,\n                columnNumber: 17\n              }, this), statiInstallazione.map(stato => /*#__PURE__*/_jsxDEV(MenuItem, {\n                value: stato,\n                children: stato\n              }, stato, false, {\n                fileName: _jsxFileName,\n                lineNumber: 745,\n                columnNumber: 19\n              }, this))]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 735,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 733,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 732,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Grid, {\n          item: true,\n          xs: 12,\n          sm: 6,\n          md: 3,\n          children: /*#__PURE__*/_jsxDEV(FormControl, {\n            fullWidth: true,\n            size: \"small\",\n            children: [/*#__PURE__*/_jsxDEV(InputLabel, {\n              id: \"tipologia-label\",\n              children: \"Tipologia\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 753,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Select, {\n              labelId: \"tipologia-label\",\n              id: \"tipologia\",\n              name: \"tipologia\",\n              value: filters.tipologia,\n              label: \"Tipologia\",\n              onChange: handleFilterChange,\n              children: [/*#__PURE__*/_jsxDEV(MenuItem, {\n                value: \"\",\n                children: \"Tutte\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 762,\n                columnNumber: 17\n              }, this), tipologieCavi.map(tipo => /*#__PURE__*/_jsxDEV(MenuItem, {\n                value: tipo,\n                children: tipo\n              }, tipo, false, {\n                fileName: _jsxFileName,\n                lineNumber: 764,\n                columnNumber: 19\n              }, this))]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 754,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 752,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 751,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Grid, {\n          item: true,\n          xs: 12,\n          sm: 6,\n          md: 3,\n          children: /*#__PURE__*/_jsxDEV(FormControl, {\n            fullWidth: true,\n            size: \"small\",\n            children: [/*#__PURE__*/_jsxDEV(InputLabel, {\n              id: \"sort-by-label\",\n              children: \"Ordina per\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 772,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Select, {\n              labelId: \"sort-by-label\",\n              id: \"sort-by\",\n              name: \"sort_by\",\n              value: filters.sort_by,\n              label: \"Ordina per\",\n              onChange: handleFilterChange,\n              children: [/*#__PURE__*/_jsxDEV(MenuItem, {\n                value: \"\",\n                children: \"Predefinito\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 781,\n                columnNumber: 17\n              }, this), sortOptions.map(option => /*#__PURE__*/_jsxDEV(MenuItem, {\n                value: option.value,\n                children: option.label\n              }, option.value, false, {\n                fileName: _jsxFileName,\n                lineNumber: 783,\n                columnNumber: 19\n              }, this))]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 773,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 771,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 770,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Grid, {\n          item: true,\n          xs: 12,\n          sm: 6,\n          md: 3,\n          children: /*#__PURE__*/_jsxDEV(FormControl, {\n            fullWidth: true,\n            size: \"small\",\n            children: [/*#__PURE__*/_jsxDEV(InputLabel, {\n              id: \"sort-order-label\",\n              children: \"Ordine\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 791,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Select, {\n              labelId: \"sort-order-label\",\n              id: \"sort-order\",\n              name: \"sort_order\",\n              value: filters.sort_order,\n              label: \"Ordine\",\n              onChange: handleFilterChange,\n              disabled: !filters.sort_by,\n              children: [/*#__PURE__*/_jsxDEV(MenuItem, {\n                value: \"asc\",\n                children: \"Crescente\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 801,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(MenuItem, {\n                value: \"desc\",\n                children: \"Decrescente\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 802,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 792,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 790,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 789,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 731,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 717,\n      columnNumber: 7\n    }, this);\n  };\n\n  // Renderizza il pannello delle statistiche\n  const renderStatsPanel = () => {\n    if (!stats) return null;\n    return /*#__PURE__*/_jsxDEV(Paper, {\n      sx: {\n        mb: 3,\n        p: 2\n      },\n      children: [/*#__PURE__*/_jsxDEV(Typography, {\n        variant: \"h6\",\n        gutterBottom: true,\n        children: \"Statistiche\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 819,\n        columnNumber: 9\n      }, this), loadingStats ? /*#__PURE__*/_jsxDEV(LinearProgress, {}, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 821,\n        columnNumber: 11\n      }, this) : /*#__PURE__*/_jsxDEV(Grid, {\n        container: true,\n        spacing: 2,\n        children: [/*#__PURE__*/_jsxDEV(Grid, {\n          item: true,\n          xs: 12,\n          md: 4,\n          children: [/*#__PURE__*/_jsxDEV(Typography, {\n            variant: \"subtitle1\",\n            gutterBottom: true,\n            children: \"Totali\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 825,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(Typography, {\n            variant: \"body2\",\n            children: [\"Cavi Attivi: \", stats.totali.cavi_attivi]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 826,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(Typography, {\n            variant: \"body2\",\n            children: [\"Cavi Spare: \", stats.totali.cavi_spare]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 827,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(Typography, {\n            variant: \"body2\",\n            children: [\"Totale Cavi: \", stats.totali.cavi_totali]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 828,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 824,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(Grid, {\n          item: true,\n          xs: 12,\n          md: 4,\n          children: [/*#__PURE__*/_jsxDEV(Typography, {\n            variant: \"subtitle1\",\n            gutterBottom: true,\n            children: \"Metrature\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 832,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(Typography, {\n            variant: \"body2\",\n            children: [\"Metri Teorici: \", stats.metrature.metri_teorici_totali.toFixed(2)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 833,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(Typography, {\n            variant: \"body2\",\n            children: [\"Metri Posati: \", stats.metrature.metri_reali_totali.toFixed(2)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 834,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(Box, {\n            sx: {\n              display: 'flex',\n              alignItems: 'center',\n              mt: 1\n            },\n            children: [/*#__PURE__*/_jsxDEV(Box, {\n              sx: {\n                width: '100%',\n                mr: 1\n              },\n              children: /*#__PURE__*/_jsxDEV(LinearProgress, {\n                variant: \"determinate\",\n                value: stats.metrature.percentuale_completamento,\n                sx: {\n                  height: 10,\n                  borderRadius: 5\n                }\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 837,\n                columnNumber: 19\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 836,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(Box, {\n              sx: {\n                minWidth: 35\n              },\n              children: /*#__PURE__*/_jsxDEV(Typography, {\n                variant: \"body2\",\n                color: \"text.secondary\",\n                children: `${stats.metrature.percentuale_completamento.toFixed(1)}%`\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 844,\n                columnNumber: 19\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 843,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 835,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 831,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(Grid, {\n          item: true,\n          xs: 12,\n          md: 4,\n          children: [/*#__PURE__*/_jsxDEV(Typography, {\n            variant: \"subtitle1\",\n            gutterBottom: true,\n            children: \"Stati\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 850,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(Box, {\n            sx: {\n              display: 'flex',\n              flexWrap: 'wrap',\n              gap: 1\n            },\n            children: stats.stati.map((stato, index) => /*#__PURE__*/_jsxDEV(Chip, {\n              label: `${stato.stato}: ${stato.count}`,\n              size: \"small\",\n              onClick: () => {\n                setFilters(prev => ({\n                  ...prev,\n                  stato_installazione: stato.stato === 'Non specificato' ? '' : stato.stato\n                }));\n              }\n            }, index, false, {\n              fileName: _jsxFileName,\n              lineNumber: 853,\n              columnNumber: 19\n            }, this))\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 851,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 849,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 823,\n        columnNumber: 11\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 818,\n      columnNumber: 7\n    }, this);\n  };\n  return /*#__PURE__*/_jsxDEV(Box, {\n    className: \"cavi-page\",\n    children: [/*#__PURE__*/_jsxDEV(Paper, {\n      sx: {\n        mb: 3,\n        p: 2\n      },\n      children: /*#__PURE__*/_jsxDEV(Box, {\n        sx: {\n          display: 'flex',\n          justifyContent: 'space-between',\n          alignItems: 'center'\n        },\n        children: [/*#__PURE__*/_jsxDEV(Typography, {\n          variant: \"h5\",\n          children: [\"Visualizzazione Cavi - \", cantiereName]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 877,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Box, {\n          sx: {\n            display: 'flex',\n            alignItems: 'center',\n            gap: 1\n          },\n          children: [/*#__PURE__*/_jsxDEV(IconButton, {\n            onClick: () => setFiltersOpen(!filtersOpen),\n            title: filtersOpen ? \"Nascondi filtri\" : \"Mostra filtri\",\n            color: filtersOpen || filters.stato_installazione || filters.tipologia || filters.sort_by ? \"primary\" : \"default\",\n            sx: {\n              position: 'relative',\n              '&::after': filters.stato_installazione || filters.tipologia || filters.sort_by ? {\n                content: '\"\"',\n                position: 'absolute',\n                top: 5,\n                right: 5,\n                width: 8,\n                height: 8,\n                borderRadius: '50%',\n                backgroundColor: '#f44336'\n              } : {}\n            },\n            children: /*#__PURE__*/_jsxDEV(FilterListIcon, {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 900,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 882,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(IconButton, {\n            onClick: () => window.location.reload(),\n            title: \"Ricarica la pagina\",\n            children: /*#__PURE__*/_jsxDEV(RefreshIcon, {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 907,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 903,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Box, {\n            sx: {\n              display: 'flex',\n              border: '1px solid #ddd',\n              borderRadius: 1\n            },\n            children: [/*#__PURE__*/_jsxDEV(IconButton, {\n              sx: {\n                color: viewMode === 'table' ? '#212529' : '#6c757d'\n              },\n              onClick: () => handleViewModeChange('table'),\n              title: \"Vista tabellare\",\n              children: /*#__PURE__*/_jsxDEV(ViewListIcon, {}, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 916,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 911,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(IconButton, {\n              sx: {\n                color: viewMode === 'card' ? '#212529' : '#6c757d'\n              },\n              onClick: () => handleViewModeChange('card'),\n              title: \"Vista a schede\",\n              children: /*#__PURE__*/_jsxDEV(ViewModuleIcon, {}, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 923,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 918,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 910,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 881,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 876,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 875,\n      columnNumber: 7\n    }, this), renderFilterPanel(), renderStatsPanel(), loading ? /*#__PURE__*/_jsxDEV(Box, {\n      sx: {\n        display: 'flex',\n        flexDirection: 'column',\n        alignItems: 'center',\n        mt: 4\n      },\n      children: [/*#__PURE__*/_jsxDEV(CircularProgress, {\n        size: 40\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 938,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(Typography, {\n        sx: {\n          mt: 2\n        },\n        children: \"Caricamento cavi...\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 939,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(Button, {\n        variant: \"outlined\",\n        color: \"primary\",\n        onClick: () => window.location.reload(),\n        sx: {\n          mt: 2\n        },\n        children: \"Ricarica la pagina\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 940,\n        columnNumber: 11\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 937,\n      columnNumber: 9\n    }, this) : error ? /*#__PURE__*/_jsxDEV(Box, {\n      children: [/*#__PURE__*/_jsxDEV(Alert, {\n        severity: \"error\",\n        sx: {\n          mb: 2\n        },\n        children: [error, error.includes('Network Error') && /*#__PURE__*/_jsxDEV(Typography, {\n          variant: \"body2\",\n          sx: {\n            mt: 1\n          },\n          children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n            children: \"Suggerimento:\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 955,\n            columnNumber: 17\n          }, this), \" Verifica che il server backend sia in esecuzione sulla porta 8001.\", /*#__PURE__*/_jsxDEV(\"br\", {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 956,\n            columnNumber: 17\n          }, this), \"Puoi avviare il backend eseguendo il file \", /*#__PURE__*/_jsxDEV(\"code\", {\n            children: \"run_system.py\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 957,\n            columnNumber: 59\n          }, this), \" nella cartella principale del progetto.\"]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 954,\n          columnNumber: 15\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 951,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(Box, {\n        sx: {\n          display: 'flex',\n          gap: 2\n        },\n        children: /*#__PURE__*/_jsxDEV(Button, {\n          variant: \"contained\",\n          className: \"primary-button\",\n          onClick: () => window.location.reload(),\n          children: \"Ricarica la pagina\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 962,\n          columnNumber: 13\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 961,\n        columnNumber: 11\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 950,\n      columnNumber: 9\n    }, this) : /*#__PURE__*/_jsxDEV(Box, {\n      children: [(filters.stato_installazione || filters.tipologia || filters.sort_by) && /*#__PURE__*/_jsxDEV(Box, {\n        sx: {\n          mb: 2,\n          display: 'flex',\n          flexWrap: 'wrap',\n          gap: 1,\n          alignItems: 'center'\n        },\n        children: [/*#__PURE__*/_jsxDEV(Typography, {\n          variant: \"body2\",\n          sx: {\n            mr: 1\n          },\n          children: \"Filtri attivi:\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 976,\n          columnNumber: 15\n        }, this), filters.stato_installazione && /*#__PURE__*/_jsxDEV(Chip, {\n          label: `Stato: ${filters.stato_installazione}`,\n          size: \"small\",\n          onDelete: () => setFilters(prev => ({\n            ...prev,\n            stato_installazione: ''\n          }))\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 978,\n          columnNumber: 17\n        }, this), filters.tipologia && /*#__PURE__*/_jsxDEV(Chip, {\n          label: `Tipologia: ${filters.tipologia}`,\n          size: \"small\",\n          onDelete: () => setFilters(prev => ({\n            ...prev,\n            tipologia: ''\n          }))\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 985,\n          columnNumber: 17\n        }, this), filters.sort_by && /*#__PURE__*/_jsxDEV(Chip, {\n          label: `Ordinamento: ${((_sortOptions$find = sortOptions.find(opt => opt.value === filters.sort_by)) === null || _sortOptions$find === void 0 ? void 0 : _sortOptions$find.label) || filters.sort_by} (${filters.sort_order === 'asc' ? 'Crescente' : 'Decrescente'})`,\n          size: \"small\",\n          onDelete: () => setFilters(prev => ({\n            ...prev,\n            sort_by: ''\n          }))\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 992,\n          columnNumber: 17\n        }, this), (filters.stato_installazione || filters.tipologia || filters.sort_by) && /*#__PURE__*/_jsxDEV(Button, {\n          variant: \"text\",\n          size: \"small\",\n          startIcon: /*#__PURE__*/_jsxDEV(ClearIcon, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 1002,\n            columnNumber: 30\n          }, this),\n          onClick: resetFilters,\n          children: \"Resetta tutti\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 999,\n          columnNumber: 17\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 975,\n        columnNumber: 13\n      }, this), /*#__PURE__*/_jsxDEV(Box, {\n        sx: {\n          mb: 3\n        },\n        children: [/*#__PURE__*/_jsxDEV(Typography, {\n          variant: \"h5\",\n          gutterBottom: true,\n          children: \"Cavi Attivi\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 1012,\n          columnNumber: 13\n        }, this), renderCaviTable(caviAttivi)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 1011,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(Box, {\n        sx: {\n          mt: 4\n        },\n        children: [/*#__PURE__*/_jsxDEV(Typography, {\n          variant: \"h5\",\n          gutterBottom: true,\n          children: \"Cavi Spare\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 1019,\n          columnNumber: 13\n        }, this), renderCaviTable(caviSpare)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 1018,\n        columnNumber: 11\n      }, this), renderDetailsDialog()]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 972,\n      columnNumber: 9\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 874,\n    columnNumber: 5\n  }, this);\n};\n_s(VisualizzaCaviPage, \"ADIMhAwt7/4FWrHJSSb/nS/TSwY=\", false, function () {\n  return [useAuth, useNavigate];\n});\n_c = VisualizzaCaviPage;\nexport default VisualizzaCaviPage;\nvar _c;\n$RefreshReg$(_c, \"VisualizzaCaviPage\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "Box", "Typography", "Paper", "<PERSON><PERSON>", "Grid", "Card", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "IconButton", "Table", "TableBody", "TableCell", "TableContainer", "TableHead", "TableRow", "Tabs", "Tab", "TextField", "MenuItem", "Select", "FormControl", "InputLabel", "Chip", "Divider", "CircularProgress", "LinearProgress", "Dialog", "DialogTitle", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "DialogActions", "ArrowBack", "ArrowBackIcon", "Refresh", "RefreshIcon", "Home", "HomeIcon", "ViewList", "ViewListIcon", "ViewModule", "ViewModuleIcon", "FilterList", "FilterListIcon", "Sort", "SortIcon", "Info", "InfoIcon", "Search", "SearchIcon", "Clear", "ClearIcon", "useNavigate", "useAuth", "caviService", "normalizeInstallationStatus", "jsxDEV", "_jsxDEV", "VisualizzaCaviPage", "_s", "_sortOptions$find", "isImpersonating", "user", "navigate", "cantiereId", "setCantiereId", "cantiereName", "setCantiereName", "caviAttivi", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "caviSpare", "setCaviSpare", "loading", "setLoading", "error", "setError", "viewMode", "setViewMode", "filters", "setFilters", "stato_installazione", "tipologia", "sort_by", "sort_order", "selected<PERSON><PERSON><PERSON>", "setSelectedCavo", "detailsDialogOpen", "setDetailsDialogOpen", "stats", "setStats", "loadingStats", "setLoadingStats", "statiInstallazione", "setStatiInstallazione", "tipologieCavi", "setTipologieCavi", "sortOptions", "value", "label", "filtersOpen", "setFiltersOpen", "loadStatiInstallazione", "fetchData", "console", "log", "token", "localStorage", "getItem", "selectedCantiereId", "selectedCantiereName", "i", "length", "key", "role", "cantiere_id", "toString", "cantiere_name", "setItem", "base64Url", "split", "base64", "replace", "jsonPayload", "decodeURIComponent", "atob", "map", "c", "charCodeAt", "slice", "join", "payload", "JSON", "parse", "e", "warn", "cantiereIdNum", "parseInt", "isNaN", "statsData", "getCaviStats", "stati", "item", "stato", "filter", "tipologie", "tipo", "statsError", "timeoutPromise", "Promise", "_", "reject", "setTimeout", "Error", "caviPromise", "get<PERSON><PERSON>", "attivi", "race", "caviError", "message", "status", "data", "stack", "code", "name", "response", "statusText", "sparePromise", "spare", "spareError", "err", "_err$response", "_err$response2", "_err$response3", "_err$response4", "_err$response5", "_err$response5$data", "errorMessage", "includes", "detail", "handleFilterChange", "event", "target", "newFilters", "resetFilters", "handleOpenDetails", "cavo", "handleCloseDetails", "renderCaviTable", "cavi", "severity", "children", "variant", "color", "onClick", "window", "location", "reload", "sx", "ml", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "getStatusColor", "statoUpper", "toUpperCase", "getConnectionStatus", "colle<PERSON>nti", "component", "mt", "overflowX", "size", "id_cavo", "String", "utility", "n_conduttori", "n_cond_val", "sezione", "sezione_val", "ubicazione_partenza", "ubicazione_arrivo", "metri_te<PERSON>ci", "parseFloat", "toFixed", "metri_reali", "metratura_reale", "bobina", "id_bobina", "statusColor", "backgroundColor", "fontWeight", "stopPropagation", "title", "fontSize", "container", "spacing", "xs", "sm", "md", "cursor", "boxShadow", "borderLeft", "display", "justifyContent", "sistema", "utenza_partenza", "utenza_arrivo", "handleViewModeChange", "mode", "renderDetailsDialog", "open", "onClose", "max<PERSON><PERSON><PERSON>", "fullWidth", "dividers", "gutterBottom", "mb", "colore_cavo", "sh", "descrizione_utenza_partenza", "responsabile_partenza", "comanda_partenza", "descrizione_utenza_arrivo", "responsabile_arrivo", "comanda_arrivo", "responsabile_posa", "comanda_posa", "Date", "timestamp", "toLocaleString", "renderFilterPanel", "p", "border", "alignItems", "startIcon", "id", "labelId", "onChange", "option", "disabled", "renderStatsPanel", "totali", "cavi_attivi", "cavi_spare", "cavi_totali", "metrature", "metri_teorici_totali", "metri_reali_totali", "width", "mr", "percentuale_completamento", "height", "borderRadius", "min<PERSON><PERSON><PERSON>", "flexWrap", "gap", "index", "count", "prev", "className", "position", "content", "top", "right", "flexDirection", "onDelete", "find", "opt", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/PycharmProjects/CMS/webapp/frontend/src/pages/cavi/VisualizzaCaviPage.js"], "sourcesContent": ["import React, { useState, useEffect } from 'react';\nimport {\n  Box,\n  Typography,\n  Paper,\n  Button,\n  Grid,\n  Card,\n  CardContent,\n  Alert,\n  IconButton,\n  Table,\n  TableBody,\n  TableCell,\n  TableContainer,\n  TableHead,\n  TableRow,\n  Tabs,\n  Tab,\n  TextField,\n  MenuItem,\n  Select,\n  FormControl,\n  InputLabel,\n  Chip,\n  Divider,\n  CircularProgress,\n  LinearProgress,\n  Dialog,\n  DialogTitle,\n  DialogContent,\n  DialogActions\n} from '@mui/material';\nimport {\n  ArrowBack as ArrowBackIcon,\n  Refresh as RefreshIcon,\n  Home as HomeIcon,\n  ViewList as ViewListIcon,\n  ViewModule as ViewModuleIcon,\n  FilterList as FilterListIcon,\n  Sort as SortIcon,\n  Info as InfoIcon,\n  Search as SearchIcon,\n  Clear as ClearIcon\n} from '@mui/icons-material';\nimport { useNavigate } from 'react-router-dom';\nimport { useAuth } from '../../context/AuthContext';\nimport caviService from '../../services/caviService';\nimport { normalizeInstallationStatus } from '../../utils/validationUtils';\nimport './CaviPage.css';\n\nconst VisualizzaCaviPage = () => {\n  const { isImpersonating, user } = useAuth();\n  const navigate = useNavigate();\n  const [cantiereId, setCantiereId] = useState(null);\n  const [cantiereName, setCantiereName] = useState('');\n  const [caviAttivi, setCaviAttivi] = useState([]);\n  const [caviSpare, setCaviSpare] = useState([]);\n  const [loading, setLoading] = useState(true);\n  const [error, setError] = useState(null);\n  const [viewMode, setViewMode] = useState('table'); // 'table' o 'card'\n\n  // Stato per filtri e ordinamento\n  const [filters, setFilters] = useState({\n    stato_installazione: '',\n    tipologia: '',\n    sort_by: '',\n    sort_order: 'asc'\n  });\n\n  // Stato per il dialogo dei dettagli del cavo\n  const [selectedCavo, setSelectedCavo] = useState(null);\n  const [detailsDialogOpen, setDetailsDialogOpen] = useState(false);\n\n  // Stato per le statistiche\n  const [stats, setStats] = useState(null);\n  const [loadingStats, setLoadingStats] = useState(false);\n\n  // Opzioni per i filtri\n  const [statiInstallazione, setStatiInstallazione] = useState([]);\n  const [tipologieCavi, setTipologieCavi] = useState([]);\n\n  // Opzioni di ordinamento\n  const sortOptions = [\n    { value: 'id_cavo', label: 'ID Cavo' },\n    { value: 'metratura_reale', label: 'Metratura Reale' },\n    { value: 'metri_teorici', label: 'Metri Teorici' },\n    { value: 'stato_installazione', label: 'Stato Installazione' },\n    { value: 'tipologia', label: 'Tipologia' },\n    { value: 'timestamp', label: 'Data Aggiornamento' }\n  ];\n\n  // Stato per il pannello dei filtri - aperto di default per maggiore visibilità\n  const [filtersOpen, setFiltersOpen] = useState(true);\n\n  // Funzione per caricare gli stati di installazione disponibili\n  const loadStatiInstallazione = () => {\n    // Usa i valori dell'enum StatoInstallazione\n    setStatiInstallazione(['Installato', 'Da installare', 'In corso']);\n  };\n\n  // Carica i dati del cantiere e dei cavi\n  useEffect(() => {\n    // Carica gli stati di installazione all'avvio\n    loadStatiInstallazione();\n\n    const fetchData = async () => {\n      try {\n        console.log('Inizializzazione VisualizzaCaviPage...');\n\n        // Verifica che l'utente sia autenticato\n        const token = localStorage.getItem('token');\n        console.log('Token presente:', !!token);\n        if (!token) {\n          setError('Sessione scaduta. Effettua nuovamente il login.');\n          setLoading(false);\n          return;\n        }\n\n        // Recupera l'ID del cantiere selezionato dal localStorage\n        let selectedCantiereId = localStorage.getItem('selectedCantiereId');\n        let selectedCantiereName = localStorage.getItem('selectedCantiereName');\n\n        console.log('Cantiere selezionato dal localStorage:', { selectedCantiereId, selectedCantiereName });\n        console.log('Dati utente:', user);\n\n        // Stampa tutti i dati nel localStorage per debug\n        console.log('DEBUG - Tutti i dati nel localStorage:');\n        for (let i = 0; i < localStorage.length; i++) {\n          const key = localStorage.key(i);\n          console.log(`${key}: ${localStorage.getItem(key)}`);\n        }\n\n        // SOLUZIONE DIRETTA: Ottieni l'ID del cantiere direttamente dal token JWT\n        if (user?.role === 'cantieri_user') {\n          console.log('Utente cantiere rilevato, tentativo di recupero ID cantiere dai dati utente');\n\n          // Verifica se l'utente ha un ID cantiere nei dati utente\n          if (user.cantiere_id) {\n            console.log('Trovato ID cantiere nei dati utente:', user.cantiere_id);\n            selectedCantiereId = user.cantiere_id.toString();\n            selectedCantiereName = user.cantiere_name || `Cantiere ${user.cantiere_id}`;\n\n            // Salva l'ID e il nome del cantiere nel localStorage\n            localStorage.setItem('selectedCantiereId', selectedCantiereId);\n            localStorage.setItem('selectedCantiereName', selectedCantiereName);\n            console.log('Salvato ID cantiere nel localStorage:', selectedCantiereId);\n          } else {\n            // Tentativo di recupero dal token JWT\n            try {\n              console.log('Tentativo di decodifica del token JWT per recuperare l\\'ID cantiere');\n              const token = localStorage.getItem('token');\n              if (token) {\n                // Decodifica il token JWT (senza verifica della firma)\n                const base64Url = token.split('.')[1];\n                const base64 = base64Url.replace(/-/g, '+').replace(/_/g, '/');\n                const jsonPayload = decodeURIComponent(atob(base64).split('').map(c => {\n                  return '%' + ('00' + c.charCodeAt(0).toString(16)).slice(-2);\n                }).join(''));\n\n                const payload = JSON.parse(jsonPayload);\n                console.log('Payload del token JWT:', payload);\n\n                if (payload.cantiere_id) {\n                  console.log('Trovato ID cantiere nel token JWT:', payload.cantiere_id);\n                  selectedCantiereId = payload.cantiere_id.toString();\n                  // Usa un nome generico se non disponibile\n                  selectedCantiereName = `Cantiere ${payload.cantiere_id}`;\n\n                  // Salva l'ID e il nome del cantiere nel localStorage\n                  localStorage.setItem('selectedCantiereId', selectedCantiereId);\n                  localStorage.setItem('selectedCantiereName', selectedCantiereName);\n                  console.log('Salvato ID cantiere nel localStorage:', selectedCantiereId);\n                }\n              }\n            } catch (e) {\n              console.error('Errore durante la decodifica del token JWT:', e);\n            }\n          }\n        }\n\n        // SOLUZIONE TEMPORANEA: Se ancora non abbiamo un ID cantiere, usa un valore hardcoded per debug\n        if (!selectedCantiereId || selectedCantiereId === 'undefined' || selectedCantiereId === 'null') {\n          console.warn('ATTENZIONE: Nessun ID cantiere trovato, utilizzo valore hardcoded per debug');\n          // Usa il primo cantiere disponibile (questo è solo per debug)\n          selectedCantiereId = '1'; // Sostituisci con un ID cantiere valido nel tuo database\n          selectedCantiereName = 'Cantiere Debug';\n\n          // Salva l'ID e il nome del cantiere nel localStorage\n          localStorage.setItem('selectedCantiereId', selectedCantiereId);\n          localStorage.setItem('selectedCantiereName', selectedCantiereName);\n          console.log('Salvato ID cantiere hardcoded nel localStorage:', selectedCantiereId);\n        }\n\n        // Verifica finale\n        if (!selectedCantiereId) {\n          setError('Nessun cantiere selezionato. Torna alla pagina dei cantieri.');\n          setLoading(false);\n          return;\n        }\n\n        // Verifica che l'ID del cantiere sia un numero valido\n        const cantiereIdNum = parseInt(selectedCantiereId, 10);\n        console.log('ID cantiere convertito a numero:', cantiereIdNum);\n        if (isNaN(cantiereIdNum)) {\n          setError(`ID cantiere non valido: ${selectedCantiereId}. Torna alla pagina dei cantieri.`);\n          setLoading(false);\n          return;\n        }\n\n        // Usa il numero convertito, non la stringa\n        setCantiereId(cantiereIdNum);\n        setCantiereName(selectedCantiereName || `Cantiere ${cantiereIdNum}`);\n\n        // Carica le statistiche dei cavi\n        try {\n          setLoadingStats(true);\n          console.log('Caricamento statistiche cavi per cantiere:', cantiereIdNum);\n          const statsData = await caviService.getCaviStats(cantiereIdNum);\n          console.log('Statistiche cavi caricate:', statsData);\n          setStats(statsData);\n\n          // Estrai gli stati di installazione e le tipologie per i filtri\n          if (statsData && statsData.stati) {\n            const stati = statsData.stati.map(item => item.stato).filter(stato => stato !== 'Non specificato');\n            setStatiInstallazione(stati);\n          }\n\n          if (statsData && statsData.tipologie) {\n            const tipologie = statsData.tipologie.map(item => item.tipologia).filter(tipo => tipo !== 'Non specificata');\n            setTipologieCavi(tipologie);\n          }\n\n          setLoadingStats(false);\n        } catch (statsError) {\n          console.error('Errore nel caricamento delle statistiche:', statsError);\n          setLoadingStats(false);\n          // Non interrompere il flusso se le statistiche falliscono\n        }\n\n        // Carica i cavi attivi con gestione degli errori migliorata\n        console.log('Caricamento cavi attivi per cantiere:', cantiereIdNum);\n        try {\n          // Imposta un timeout per evitare che la richiesta rimanga bloccata\n          const timeoutPromise = new Promise((_, reject) => {\n            setTimeout(() => reject(new Error('Timeout durante il caricamento dei cavi attivi')), 30000); // Aumentato a 30 secondi\n          });\n\n          // Esegui la richiesta con un timeout di sicurezza e applica i filtri\n          console.log('Iniziando chiamata API per cavi attivi con filtri:', filters);\n          const caviPromise = caviService.getCavi(cantiereIdNum, 0, filters);\n          const attivi = await Promise.race([caviPromise, timeoutPromise]);\n\n          console.log('Cavi attivi caricati:', attivi);\n          console.log('Numero di cavi attivi trovati:', attivi ? attivi.length : 0);\n          if (attivi && attivi.length > 0) {\n            console.log('Primo cavo attivo:', attivi[0]);\n          } else {\n            console.warn('Nessun cavo attivo trovato per il cantiere', cantiereIdNum);\n          }\n          setCaviAttivi(attivi || []);\n        } catch (caviError) {\n          console.error('Errore nel caricamento dei cavi attivi:', caviError);\n          console.error('Dettagli errore cavi attivi:', {\n            message: caviError.message,\n            status: caviError.status,\n            data: caviError.data,\n            stack: caviError.stack,\n            code: caviError.code,\n            name: caviError.name,\n            response: caviError.response ? {\n              status: caviError.response.status,\n              statusText: caviError.response.statusText,\n              data: caviError.response.data\n            } : 'No response'\n          });\n\n          // Non interrompere il flusso, continua con i cavi spare\n          setCaviAttivi([]);\n          console.warn('Continuazione del flusso dopo errore nei cavi attivi');\n\n          // Aggiungi un messaggio di errore visibile all'utente\n          setError(`Errore nel caricamento dei cavi attivi: ${caviError.message}. Controlla la console per maggiori dettagli.`);\n        }\n\n        // Carica i cavi spare con gestione degli errori migliorata\n        console.log('Caricamento cavi spare per cantiere:', cantiereIdNum);\n        try {\n          // Imposta un timeout per evitare che la richiesta rimanga bloccata\n          const timeoutPromise = new Promise((_, reject) => {\n            setTimeout(() => reject(new Error('Timeout durante il caricamento dei cavi spare')), 30000); // Aumentato a 30 secondi\n          });\n\n          // Esegui la richiesta con un timeout di sicurezza\n          console.log('Iniziando chiamata API per cavi spare...');\n          // Non applichiamo i filtri ai cavi spare, solo agli attivi\n          const sparePromise = caviService.getCavi(cantiereIdNum, 3);\n          const spare = await Promise.race([sparePromise, timeoutPromise]);\n\n          console.log('Cavi spare caricati:', spare);\n          console.log('Numero di cavi spare trovati:', spare ? spare.length : 0);\n          if (spare && spare.length > 0) {\n            console.log('Primo cavo spare:', spare[0]);\n          } else {\n            console.warn('Nessun cavo spare trovato per il cantiere', cantiereIdNum);\n          }\n          setCaviSpare(spare || []);\n        } catch (spareError) {\n          console.error('Errore nel caricamento dei cavi spare:', spareError);\n          console.error('Dettagli errore cavi spare:', {\n            message: spareError.message,\n            status: spareError.status,\n            data: spareError.data,\n            stack: spareError.stack,\n            code: spareError.code,\n            name: spareError.name,\n            response: spareError.response ? {\n              status: spareError.response.status,\n              statusText: spareError.response.statusText,\n              data: spareError.response.data\n            } : 'No response'\n          });\n\n          // Non interrompere il flusso, imposta un array vuoto\n          setCaviSpare([]);\n\n          // Aggiungi un messaggio di errore visibile all'utente se non c'è già un errore per i cavi attivi\n          if (!error) {\n            setError(`Errore nel caricamento dei cavi spare: ${spareError.message}. Controlla la console per maggiori dettagli.`);\n          }\n        }\n\n        // Se siamo arrivati qui, almeno abbiamo caricato l'interfaccia di base\n        setLoading(false);\n\n      } catch (err) {\n        console.error('Errore nel caricamento dei cavi:', err);\n        console.error('Dettagli errore generale:', {\n          message: err.message,\n          status: err.status || err.response?.status,\n          data: err.data || err.response?.data,\n          stack: err.stack\n        });\n\n        // Estrai il messaggio di errore dettagliato\n        let errorMessage = 'Errore sconosciuto';\n\n        if (err.message && err.message.includes('ID cantiere non valido')) {\n          errorMessage = err.message;\n        } else if (err.status === 401 || err.status === 403 ||\n                  err.response?.status === 401 || err.response?.status === 403) {\n          errorMessage = 'Sessione scaduta o non autorizzata. Effettua nuovamente il login.';\n        } else if (err.response?.data?.detail) {\n          // Estrai il messaggio di errore dettagliato dall'API\n          errorMessage = `Errore API: ${err.response.data.detail}`;\n        } else if (err.code === 'ERR_NETWORK') {\n          // Errore di rete\n          errorMessage = 'Network Error. Verifica che il backend sia in esecuzione e accessibile.';\n        } else if (err.message) {\n          errorMessage = err.message;\n        }\n\n        setError(`Impossibile caricare i cavi: ${errorMessage}. Riprova più tardi.`);\n\n        // Imposta array vuoti per evitare errori di rendering\n        setCaviAttivi([]);\n        setCaviSpare([]);\n      } finally {\n        setLoading(false);\n      }\n    };\n\n    fetchData();\n  }, [filters]); // Ricarica i dati quando cambiano i filtri\n\n  // Funzione per gestire il cambio dei filtri\n  const handleFilterChange = (event) => {\n    const { name, value } = event.target;\n    console.log(`Filtro cambiato: ${name} = ${value}`);\n\n    // Aggiorna lo stato dei filtri\n    const newFilters = {\n      ...filters,\n      [name]: value\n    };\n\n    console.log('Nuovi filtri:', newFilters);\n    setFilters(newFilters);\n  };\n\n  // Funzione per resettare i filtri\n  const resetFilters = () => {\n    setFilters({\n      stato_installazione: '',\n      tipologia: '',\n      sort_by: '',\n      sort_order: 'asc'\n    });\n  };\n\n  // Funzione per aprire il dialogo dei dettagli del cavo\n  const handleOpenDetails = (cavo) => {\n    setSelectedCavo(cavo);\n    setDetailsDialogOpen(true);\n  };\n\n  // Funzione per chiudere il dialogo dei dettagli del cavo\n  const handleCloseDetails = () => {\n    setDetailsDialogOpen(false);\n    setSelectedCavo(null);\n  };\n\n  // Nessuna funzione di navigazione necessaria, tutto è gestito dal menu principale\n\n  // Funzione per visualizzare i cavi in formato tabellare\n  const renderCaviTable = (cavi) => {\n    if (!cavi || cavi.length === 0) {\n      return (\n        <Alert severity=\"info\">\n          Nessun cavo trovato in questa categoria.\n          <Button\n            variant=\"text\"\n            color=\"primary\"\n            onClick={() => window.location.reload()}\n            sx={{ ml: 2 }}\n          >\n            Riprova\n          </Button>\n        </Alert>\n      );\n    }\n\n    // Funzione per ottenere il colore in base allo stato di installazione\n    const getStatusColor = (stato) => {\n      // Normalizza lo stato per gestire le variazioni di maiuscole/minuscole\n      const statoUpper = (stato || '').toUpperCase();\n\n      switch (statoUpper) {\n        case 'POSATO':\n        case 'INSTALLATO': // Valore dall'enum StatoInstallazione\n          return '#4caf50'; // Verde\n        case 'COLLEGATO PARTENZA':\n          return '#ff9800'; // Arancione\n        case 'COLLEGATO ARRIVO':\n          return '#ff9800'; // Arancione\n        case 'COLLEGATO':\n          return '#2196f3'; // Blu\n        case 'DA POSARE':\n        case 'DA INSTALLARE': // Valore dall'enum StatoInstallazione\n          return '#f44336'; // Rosso\n        case 'IN POSA':\n        case 'IN CORSO': // Valore dall'enum StatoInstallazione\n        case 'IN INSTALLAZIONE':\n          return '#9c27b0'; // Viola\n        default:\n          console.log('Stato non riconosciuto:', stato);\n          return '#757575'; // Grigio per stati sconosciuti\n      }\n    };\n\n    // Funzione per ottenere lo stato di collegamento\n    const getConnectionStatus = (collegamenti) => {\n      if (!collegamenti) return 'NON COLLEGATO';\n\n      switch (collegamenti) {\n        case 1: return 'COLLEGATO PARTENZA';\n        case 2: return 'COLLEGATO ARRIVO';\n        case 3: return 'COLLEGATO';\n        default: return 'NON COLLEGATO';\n      }\n    };\n\n    // Utilizziamo la funzione normalizeInstallationStatus importata da validationUtils\n\n    if (viewMode === 'table') {\n      return (\n        <TableContainer component={Paper} sx={{ mt: 2, overflowX: 'auto' }}>\n          <Table size=\"small\">\n            <TableHead>\n              <TableRow>\n                <TableCell>ID Cavo</TableCell>\n                <TableCell>Utility</TableCell>\n                <TableCell>Tipologia</TableCell>\n                <TableCell>N.Cond</TableCell>\n                <TableCell>Sezione</TableCell>\n                <TableCell>Ubicaz.Part.</TableCell>\n                <TableCell>Ubicaz.Arr.</TableCell>\n                <TableCell>Metri T.</TableCell>\n                <TableCell>Metri R.</TableCell>\n                <TableCell>Stato</TableCell>\n                <TableCell>Collegamenti</TableCell>\n                <TableCell>Bobina</TableCell>\n                <TableCell>Azioni</TableCell>\n              </TableRow>\n            </TableHead>\n            <TableBody>\n              {cavi.map((cavo) => {\n                // Formatta i valori per la visualizzazione come nella CLI\n                const id_cavo = String(cavo.id_cavo).replace('$', '');\n                const utility = cavo.utility || '-';\n                const tipologia = cavo.tipologia || '-';\n\n                // Gestisci n_conduttori come stringa o numero\n                let n_conduttori = '-';\n                try {\n                  const n_cond_val = parseInt(cavo.n_conduttori, 10) || 0;\n                  n_conduttori = n_cond_val > 0 ? String(n_cond_val) : '-';\n                } catch (e) {\n                  n_conduttori = cavo.n_conduttori || '-';\n                }\n\n                // Gestisci sezione come stringa\n                let sezione = '-';\n                const sezione_val = cavo.sezione;\n                if (typeof sezione_val === 'number' && sezione_val === 0) {\n                  sezione = '-';\n                } else {\n                  sezione = sezione_val ? String(sezione_val) : '-';\n                }\n\n                const ubicazione_partenza = cavo.ubicazione_partenza || '-';\n                const ubicazione_arrivo = cavo.ubicazione_arrivo || '-';\n                const metri_teorici = cavo.metri_teorici ? `${parseFloat(cavo.metri_teorici).toFixed(2)}` : '-';\n                const metri_reali = cavo.metratura_reale ? `${parseFloat(cavo.metratura_reale).toFixed(2)}` : '-';\n                const stato = cavo.stato_installazione || 'Da installare';\n                const collegamenti = getConnectionStatus(cavo.collegamenti);\n                const bobina = cavo.id_bobina || '-';\n\n                // Determina il colore in base allo stato\n                const statusColor = getStatusColor(stato);\n\n                return (\n                  <TableRow\n                    key={cavo.id_cavo}\n                    sx={{ '&:hover': { backgroundColor: '#f5f5f5' } }}\n                    onClick={() => handleOpenDetails(cavo)}\n                  >\n                    <TableCell>{id_cavo}</TableCell>\n                    <TableCell>{utility}</TableCell>\n                    <TableCell>{tipologia}</TableCell>\n                    <TableCell>{n_conduttori}</TableCell>\n                    <TableCell>{sezione}</TableCell>\n                    <TableCell>{ubicazione_partenza}</TableCell>\n                    <TableCell>{ubicazione_arrivo}</TableCell>\n                    <TableCell>{metri_teorici}</TableCell>\n                    <TableCell>{metri_reali}</TableCell>\n                    <TableCell>\n                      <Chip\n                        label={normalizeInstallationStatus(stato)}\n                        size=\"small\"\n                        sx={{\n                          backgroundColor: statusColor,\n                          color: '#fff',\n                          fontWeight: 'bold'\n                        }}\n                      />\n                    </TableCell>\n                    <TableCell>{collegamenti}</TableCell>\n                    <TableCell>{bobina}</TableCell>\n                    <TableCell>\n                      <IconButton\n                        size=\"small\"\n                        onClick={(e) => {\n                          e.stopPropagation();\n                          handleOpenDetails(cavo);\n                        }}\n                        title=\"Visualizza dettagli\"\n                      >\n                        <InfoIcon fontSize=\"small\" />\n                      </IconButton>\n                    </TableCell>\n                  </TableRow>\n                );\n              })}\n            </TableBody>\n          </Table>\n        </TableContainer>\n      );\n    } else {\n      // Visualizzazione a schede (card)\n      return (\n        <Grid container spacing={2}>\n          {cavi.map((cavo) => {\n            const stato = cavo.stato_installazione || 'Da installare';\n            const statusColor = getStatusColor(stato);\n            const collegamenti = getConnectionStatus(cavo.collegamenti);\n\n            return (\n              <Grid item xs={12} sm={6} md={4} key={cavo.id_cavo}>\n                <Card\n                  sx={{\n                    cursor: 'pointer',\n                    '&:hover': { boxShadow: 6 },\n                    borderLeft: `5px solid ${statusColor}`\n                  }}\n                  onClick={() => handleOpenDetails(cavo)}\n                >\n                  <CardContent>\n                    <Typography variant=\"h6\" component=\"div\" sx={{ display: 'flex', justifyContent: 'space-between' }}>\n                      {cavo.id_cavo}\n                      <Chip\n                        label={normalizeInstallationStatus(stato)}\n                        size=\"small\"\n                        sx={{\n                          backgroundColor: statusColor,\n                          color: '#fff',\n                          fontWeight: 'bold'\n                        }}\n                      />\n                    </Typography>\n                    <Typography variant=\"body2\" color=\"text.secondary\">\n                      Sistema: {cavo.sistema || 'N/A'}\n                    </Typography>\n                    <Typography variant=\"body2\" color=\"text.secondary\">\n                      Tipologia: {cavo.tipologia || 'N/A'}\n                    </Typography>\n                    <Typography variant=\"body2\" color=\"text.secondary\">\n                      Partenza: {cavo.ubicazione_partenza || 'N/A'} - {cavo.utenza_partenza || 'N/A'}\n                    </Typography>\n                    <Typography variant=\"body2\" color=\"text.secondary\">\n                      Arrivo: {cavo.ubicazione_arrivo || 'N/A'} - {cavo.utenza_arrivo || 'N/A'}\n                    </Typography>\n                    <Typography variant=\"body2\" color=\"text.secondary\">\n                      Metri teorici: {cavo.metri_teorici || 'N/A'}\n                    </Typography>\n                    <Typography variant=\"body2\" color=\"text.secondary\">\n                      Metratura reale: {cavo.metratura_reale || '0'}\n                    </Typography>\n                    <Typography variant=\"body2\" color=\"text.secondary\">\n                      Collegamenti: {collegamenti}\n                    </Typography>\n                    <Typography variant=\"body2\" color=\"text.secondary\">\n                      Bobina: {cavo.id_bobina || 'N/A'}\n                    </Typography>\n                  </CardContent>\n                </Card>\n              </Grid>\n            );\n          })}\n        </Grid>\n      );\n    }\n  };\n\n  // Gestisce il cambio di modalità di visualizzazione\n  const handleViewModeChange = (mode) => {\n    setViewMode(mode);\n  };\n\n  // Renderizza il dialogo dei dettagli del cavo\n  const renderDetailsDialog = () => {\n    if (!selectedCavo) return null;\n\n    return (\n      <Dialog open={detailsDialogOpen} onClose={handleCloseDetails} maxWidth=\"md\" fullWidth>\n        <DialogTitle>\n          Dettagli Cavo: {selectedCavo.id_cavo}\n        </DialogTitle>\n        <DialogContent dividers>\n          <Grid container spacing={2}>\n            <Grid item xs={12} md={6}>\n              <Typography variant=\"subtitle1\" gutterBottom>Informazioni Generali</Typography>\n              <Box sx={{ mb: 2 }}>\n                <Typography variant=\"body2\"><strong>Sistema:</strong> {selectedCavo.sistema || 'N/A'}</Typography>\n                <Typography variant=\"body2\"><strong>Utility:</strong> {selectedCavo.utility || 'N/A'}</Typography>\n                <Typography variant=\"body2\"><strong>Tipologia:</strong> {selectedCavo.tipologia || 'N/A'}</Typography>\n                <Typography variant=\"body2\"><strong>Colore:</strong> {selectedCavo.colore_cavo || 'N/A'}</Typography>\n                <Typography variant=\"body2\"><strong>N. Conduttori:</strong> {selectedCavo.n_conduttori || 'N/A'}</Typography>\n                <Typography variant=\"body2\"><strong>Sezione:</strong> {selectedCavo.sezione || 'N/A'}</Typography>\n                <Typography variant=\"body2\"><strong>SH:</strong> {selectedCavo.sh || 'N/A'}</Typography>\n              </Box>\n\n              <Typography variant=\"subtitle1\" gutterBottom>Partenza</Typography>\n              <Box sx={{ mb: 2 }}>\n                <Typography variant=\"body2\"><strong>Ubicazione:</strong> {selectedCavo.ubicazione_partenza || 'N/A'}</Typography>\n                <Typography variant=\"body2\"><strong>Utenza:</strong> {selectedCavo.utenza_partenza || 'N/A'}</Typography>\n                <Typography variant=\"body2\"><strong>Descrizione:</strong> {selectedCavo.descrizione_utenza_partenza || 'N/A'}</Typography>\n                <Typography variant=\"body2\"><strong>Responsabile:</strong> {selectedCavo.responsabile_partenza || 'N/A'}</Typography>\n                <Typography variant=\"body2\"><strong>Comanda:</strong> {selectedCavo.comanda_partenza || 'N/A'}</Typography>\n              </Box>\n            </Grid>\n\n            <Grid item xs={12} md={6}>\n              <Typography variant=\"subtitle1\" gutterBottom>Arrivo</Typography>\n              <Box sx={{ mb: 2 }}>\n                <Typography variant=\"body2\"><strong>Ubicazione:</strong> {selectedCavo.ubicazione_arrivo || 'N/A'}</Typography>\n                <Typography variant=\"body2\"><strong>Utenza:</strong> {selectedCavo.utenza_arrivo || 'N/A'}</Typography>\n                <Typography variant=\"body2\"><strong>Descrizione:</strong> {selectedCavo.descrizione_utenza_arrivo || 'N/A'}</Typography>\n                <Typography variant=\"body2\"><strong>Responsabile:</strong> {selectedCavo.responsabile_arrivo || 'N/A'}</Typography>\n                <Typography variant=\"body2\"><strong>Comanda:</strong> {selectedCavo.comanda_arrivo || 'N/A'}</Typography>\n              </Box>\n\n              <Typography variant=\"subtitle1\" gutterBottom>Installazione</Typography>\n              <Box sx={{ mb: 2 }}>\n                <Typography variant=\"body2\"><strong>Metri Teorici:</strong> {selectedCavo.metri_teorici || 'N/A'}</Typography>\n                <Typography variant=\"body2\"><strong>Metratura Reale:</strong> {selectedCavo.metratura_reale || '0'}</Typography>\n                <Typography variant=\"body2\"><strong>Stato:</strong> {normalizeInstallationStatus(selectedCavo.stato_installazione)}</Typography>\n                <Typography variant=\"body2\"><strong>Collegamenti:</strong> {selectedCavo.collegamenti || '0'}</Typography>\n                <Typography variant=\"body2\"><strong>Bobina:</strong> {selectedCavo.id_bobina || 'N/A'}</Typography>\n                <Typography variant=\"body2\"><strong>Responsabile Posa:</strong> {selectedCavo.responsabile_posa || 'N/A'}</Typography>\n                <Typography variant=\"body2\"><strong>Comanda Posa:</strong> {selectedCavo.comanda_posa || 'N/A'}</Typography>\n                <Typography variant=\"body2\"><strong>Ultimo Aggiornamento:</strong> {new Date(selectedCavo.timestamp).toLocaleString()}</Typography>\n              </Box>\n            </Grid>\n          </Grid>\n        </DialogContent>\n        <DialogActions>\n          <Button onClick={handleCloseDetails}>Chiudi</Button>\n        </DialogActions>\n      </Dialog>\n    );\n  };\n\n  // Renderizza il pannello dei filtri\n  const renderFilterPanel = () => {\n    return (\n      <Paper sx={{ mb: 3, p: 2, display: filtersOpen ? 'block' : 'none', border: '1px solid #e0e0e0' }}>\n        <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 2 }}>\n          <Typography variant=\"h6\">Filtri e Ordinamento</Typography>\n          <Button\n            variant=\"outlined\"\n            color=\"primary\"\n            size=\"small\"\n            onClick={resetFilters}\n            startIcon={<ClearIcon />}\n          >\n            Resetta Filtri\n          </Button>\n        </Box>\n        <Divider sx={{ mb: 2 }} />\n        <Grid container spacing={2}>\n          <Grid item xs={12} sm={6} md={3}>\n            <FormControl fullWidth size=\"small\">\n              <InputLabel id=\"stato-installazione-label\">Stato Installazione</InputLabel>\n              <Select\n                labelId=\"stato-installazione-label\"\n                id=\"stato-installazione\"\n                name=\"stato_installazione\"\n                value={filters.stato_installazione}\n                label=\"Stato Installazione\"\n                onChange={handleFilterChange}\n              >\n                <MenuItem value=\"\">Tutti</MenuItem>\n                {statiInstallazione.map(stato => (\n                  <MenuItem key={stato} value={stato}>{stato}</MenuItem>\n                ))}\n              </Select>\n            </FormControl>\n          </Grid>\n\n          <Grid item xs={12} sm={6} md={3}>\n            <FormControl fullWidth size=\"small\">\n              <InputLabel id=\"tipologia-label\">Tipologia</InputLabel>\n              <Select\n                labelId=\"tipologia-label\"\n                id=\"tipologia\"\n                name=\"tipologia\"\n                value={filters.tipologia}\n                label=\"Tipologia\"\n                onChange={handleFilterChange}\n              >\n                <MenuItem value=\"\">Tutte</MenuItem>\n                {tipologieCavi.map(tipo => (\n                  <MenuItem key={tipo} value={tipo}>{tipo}</MenuItem>\n                ))}\n              </Select>\n            </FormControl>\n          </Grid>\n\n          <Grid item xs={12} sm={6} md={3}>\n            <FormControl fullWidth size=\"small\">\n              <InputLabel id=\"sort-by-label\">Ordina per</InputLabel>\n              <Select\n                labelId=\"sort-by-label\"\n                id=\"sort-by\"\n                name=\"sort_by\"\n                value={filters.sort_by}\n                label=\"Ordina per\"\n                onChange={handleFilterChange}\n              >\n                <MenuItem value=\"\">Predefinito</MenuItem>\n                {sortOptions.map(option => (\n                  <MenuItem key={option.value} value={option.value}>{option.label}</MenuItem>\n                ))}\n              </Select>\n            </FormControl>\n          </Grid>\n\n          <Grid item xs={12} sm={6} md={3}>\n            <FormControl fullWidth size=\"small\">\n              <InputLabel id=\"sort-order-label\">Ordine</InputLabel>\n              <Select\n                labelId=\"sort-order-label\"\n                id=\"sort-order\"\n                name=\"sort_order\"\n                value={filters.sort_order}\n                label=\"Ordine\"\n                onChange={handleFilterChange}\n                disabled={!filters.sort_by}\n              >\n                <MenuItem value=\"asc\">Crescente</MenuItem>\n                <MenuItem value=\"desc\">Decrescente</MenuItem>\n              </Select>\n            </FormControl>\n          </Grid>\n        </Grid>\n\n        {/* Pulsante di reset spostato in alto */}\n      </Paper>\n    );\n  };\n\n  // Renderizza il pannello delle statistiche\n  const renderStatsPanel = () => {\n    if (!stats) return null;\n\n    return (\n      <Paper sx={{ mb: 3, p: 2 }}>\n        <Typography variant=\"h6\" gutterBottom>Statistiche</Typography>\n        {loadingStats ? (\n          <LinearProgress />\n        ) : (\n          <Grid container spacing={2}>\n            <Grid item xs={12} md={4}>\n              <Typography variant=\"subtitle1\" gutterBottom>Totali</Typography>\n              <Typography variant=\"body2\">Cavi Attivi: {stats.totali.cavi_attivi}</Typography>\n              <Typography variant=\"body2\">Cavi Spare: {stats.totali.cavi_spare}</Typography>\n              <Typography variant=\"body2\">Totale Cavi: {stats.totali.cavi_totali}</Typography>\n            </Grid>\n\n            <Grid item xs={12} md={4}>\n              <Typography variant=\"subtitle1\" gutterBottom>Metrature</Typography>\n              <Typography variant=\"body2\">Metri Teorici: {stats.metrature.metri_teorici_totali.toFixed(2)}</Typography>\n              <Typography variant=\"body2\">Metri Posati: {stats.metrature.metri_reali_totali.toFixed(2)}</Typography>\n              <Box sx={{ display: 'flex', alignItems: 'center', mt: 1 }}>\n                <Box sx={{ width: '100%', mr: 1 }}>\n                  <LinearProgress\n                    variant=\"determinate\"\n                    value={stats.metrature.percentuale_completamento}\n                    sx={{ height: 10, borderRadius: 5 }}\n                  />\n                </Box>\n                <Box sx={{ minWidth: 35 }}>\n                  <Typography variant=\"body2\" color=\"text.secondary\">{`${stats.metrature.percentuale_completamento.toFixed(1)}%`}</Typography>\n                </Box>\n              </Box>\n            </Grid>\n\n            <Grid item xs={12} md={4}>\n              <Typography variant=\"subtitle1\" gutterBottom>Stati</Typography>\n              <Box sx={{ display: 'flex', flexWrap: 'wrap', gap: 1 }}>\n                {stats.stati.map((stato, index) => (\n                  <Chip\n                    key={index}\n                    label={`${stato.stato}: ${stato.count}`}\n                    size=\"small\"\n                    onClick={() => {\n                      setFilters(prev => ({\n                        ...prev,\n                        stato_installazione: stato.stato === 'Non specificato' ? '' : stato.stato\n                      }));\n                    }}\n                  />\n                ))}\n              </Box>\n            </Grid>\n          </Grid>\n        )}\n      </Paper>\n    );\n  };\n\n  return (\n    <Box className=\"cavi-page\">\n      <Paper sx={{ mb: 3, p: 2 }}>\n        <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>\n          <Typography variant=\"h5\">\n            Visualizzazione Cavi - {cantiereName}\n          </Typography>\n\n          <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>\n            <IconButton\n              onClick={() => setFiltersOpen(!filtersOpen)}\n              title={filtersOpen ? \"Nascondi filtri\" : \"Mostra filtri\"}\n              color={filtersOpen || filters.stato_installazione || filters.tipologia || filters.sort_by ? \"primary\" : \"default\"}\n              sx={{\n                position: 'relative',\n                '&::after': (filters.stato_installazione || filters.tipologia || filters.sort_by) ? {\n                  content: '\"\"',\n                  position: 'absolute',\n                  top: 5,\n                  right: 5,\n                  width: 8,\n                  height: 8,\n                  borderRadius: '50%',\n                  backgroundColor: '#f44336',\n                } : {}\n              }}\n            >\n              <FilterListIcon />\n            </IconButton>\n\n            <IconButton\n              onClick={() => window.location.reload()}\n              title=\"Ricarica la pagina\"\n            >\n              <RefreshIcon />\n            </IconButton>\n\n            <Box sx={{ display: 'flex', border: '1px solid #ddd', borderRadius: 1 }}>\n              <IconButton\n                sx={{ color: viewMode === 'table' ? '#212529' : '#6c757d' }}\n                onClick={() => handleViewModeChange('table')}\n                title=\"Vista tabellare\"\n              >\n                <ViewListIcon />\n              </IconButton>\n              <IconButton\n                sx={{ color: viewMode === 'card' ? '#212529' : '#6c757d' }}\n                onClick={() => handleViewModeChange('card')}\n                title=\"Vista a schede\"\n              >\n                <ViewModuleIcon />\n              </IconButton>\n            </Box>\n          </Box>\n        </Box>\n      </Paper>\n\n      {/* Pannello dei filtri */}\n      {renderFilterPanel()}\n\n      {/* Pannello delle statistiche */}\n      {renderStatsPanel()}\n\n      {loading ? (\n        <Box sx={{ display: 'flex', flexDirection: 'column', alignItems: 'center', mt: 4 }}>\n          <CircularProgress size={40} />\n          <Typography sx={{ mt: 2 }}>Caricamento cavi...</Typography>\n          <Button\n            variant=\"outlined\"\n            color=\"primary\"\n            onClick={() => window.location.reload()}\n            sx={{ mt: 2 }}\n          >\n            Ricarica la pagina\n          </Button>\n        </Box>\n      ) : error ? (\n        <Box>\n          <Alert severity=\"error\" sx={{ mb: 2 }}>\n            {error}\n            {error.includes('Network Error') && (\n              <Typography variant=\"body2\" sx={{ mt: 1 }}>\n                <strong>Suggerimento:</strong> Verifica che il server backend sia in esecuzione sulla porta 8001.\n                <br />\n                Puoi avviare il backend eseguendo il file <code>run_system.py</code> nella cartella principale del progetto.\n              </Typography>\n            )}\n          </Alert>\n          <Box sx={{ display: 'flex', gap: 2 }}>\n            <Button\n              variant=\"contained\"\n              className=\"primary-button\"\n              onClick={() => window.location.reload()}\n            >\n              Ricarica la pagina\n            </Button>\n          </Box>\n        </Box>\n      ) : (\n        <Box>\n          {/* Filtri attivi */}\n          {(filters.stato_installazione || filters.tipologia || filters.sort_by) && (\n            <Box sx={{ mb: 2, display: 'flex', flexWrap: 'wrap', gap: 1, alignItems: 'center' }}>\n              <Typography variant=\"body2\" sx={{ mr: 1 }}>Filtri attivi:</Typography>\n              {filters.stato_installazione && (\n                <Chip\n                  label={`Stato: ${filters.stato_installazione}`}\n                  size=\"small\"\n                  onDelete={() => setFilters(prev => ({ ...prev, stato_installazione: '' }))}\n                />\n              )}\n              {filters.tipologia && (\n                <Chip\n                  label={`Tipologia: ${filters.tipologia}`}\n                  size=\"small\"\n                  onDelete={() => setFilters(prev => ({ ...prev, tipologia: '' }))}\n                />\n              )}\n              {filters.sort_by && (\n                <Chip\n                  label={`Ordinamento: ${sortOptions.find(opt => opt.value === filters.sort_by)?.label || filters.sort_by} (${filters.sort_order === 'asc' ? 'Crescente' : 'Decrescente'})`}\n                  size=\"small\"\n                  onDelete={() => setFilters(prev => ({ ...prev, sort_by: '' }))}\n                />\n              )}\n              {(filters.stato_installazione || filters.tipologia || filters.sort_by) && (\n                <Button\n                  variant=\"text\"\n                  size=\"small\"\n                  startIcon={<ClearIcon />}\n                  onClick={resetFilters}\n                >\n                  Resetta tutti\n                </Button>\n              )}\n            </Box>\n          )}\n\n          <Box sx={{ mb: 3 }}>\n            <Typography variant=\"h5\" gutterBottom>\n              Cavi Attivi\n            </Typography>\n            {renderCaviTable(caviAttivi)}\n          </Box>\n\n          <Box sx={{ mt: 4 }}>\n            <Typography variant=\"h5\" gutterBottom>\n              Cavi Spare\n            </Typography>\n            {renderCaviTable(caviSpare)}\n          </Box>\n\n          {/* Dialogo dei dettagli del cavo */}\n          {renderDetailsDialog()}\n        </Box>\n      )}\n    </Box>\n  );\n};\n\nexport default VisualizzaCaviPage;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAClD,SACEC,GAAG,EACHC,UAAU,EACVC,KAAK,EACLC,MAAM,EACNC,IAAI,EACJC,IAAI,EACJC,WAAW,EACXC,KAAK,EACLC,UAAU,EACVC,KAAK,EACLC,SAAS,EACTC,SAAS,EACTC,cAAc,EACdC,SAAS,EACTC,QAAQ,EACRC,IAAI,EACJC,GAAG,EACHC,SAAS,EACTC,QAAQ,EACRC,MAAM,EACNC,WAAW,EACXC,UAAU,EACVC,IAAI,EACJC,OAAO,EACPC,gBAAgB,EAChBC,cAAc,EACdC,MAAM,EACNC,WAAW,EACXC,aAAa,EACbC,aAAa,QACR,eAAe;AACtB,SACEC,SAAS,IAAIC,aAAa,EAC1BC,OAAO,IAAIC,WAAW,EACtBC,IAAI,IAAIC,QAAQ,EAChBC,QAAQ,IAAIC,YAAY,EACxBC,UAAU,IAAIC,cAAc,EAC5BC,UAAU,IAAIC,cAAc,EAC5BC,IAAI,IAAIC,QAAQ,EAChBC,IAAI,IAAIC,QAAQ,EAChBC,MAAM,IAAIC,UAAU,EACpBC,KAAK,IAAIC,SAAS,QACb,qBAAqB;AAC5B,SAASC,WAAW,QAAQ,kBAAkB;AAC9C,SAASC,OAAO,QAAQ,2BAA2B;AACnD,OAAOC,WAAW,MAAM,4BAA4B;AACpD,SAASC,2BAA2B,QAAQ,6BAA6B;AACzE,OAAO,gBAAgB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAExB,MAAMC,kBAAkB,GAAGA,CAAA,KAAM;EAAAC,EAAA;EAAA,IAAAC,iBAAA;EAC/B,MAAM;IAAEC,eAAe;IAAEC;EAAK,CAAC,GAAGT,OAAO,CAAC,CAAC;EAC3C,MAAMU,QAAQ,GAAGX,WAAW,CAAC,CAAC;EAC9B,MAAM,CAACY,UAAU,EAAEC,aAAa,CAAC,GAAGjE,QAAQ,CAAC,IAAI,CAAC;EAClD,MAAM,CAACkE,YAAY,EAAEC,eAAe,CAAC,GAAGnE,QAAQ,CAAC,EAAE,CAAC;EACpD,MAAM,CAACoE,UAAU,EAAEC,aAAa,CAAC,GAAGrE,QAAQ,CAAC,EAAE,CAAC;EAChD,MAAM,CAACsE,SAAS,EAAEC,YAAY,CAAC,GAAGvE,QAAQ,CAAC,EAAE,CAAC;EAC9C,MAAM,CAACwE,OAAO,EAAEC,UAAU,CAAC,GAAGzE,QAAQ,CAAC,IAAI,CAAC;EAC5C,MAAM,CAAC0E,KAAK,EAAEC,QAAQ,CAAC,GAAG3E,QAAQ,CAAC,IAAI,CAAC;EACxC,MAAM,CAAC4E,QAAQ,EAAEC,WAAW,CAAC,GAAG7E,QAAQ,CAAC,OAAO,CAAC,CAAC,CAAC;;EAEnD;EACA,MAAM,CAAC8E,OAAO,EAAEC,UAAU,CAAC,GAAG/E,QAAQ,CAAC;IACrCgF,mBAAmB,EAAE,EAAE;IACvBC,SAAS,EAAE,EAAE;IACbC,OAAO,EAAE,EAAE;IACXC,UAAU,EAAE;EACd,CAAC,CAAC;;EAEF;EACA,MAAM,CAACC,YAAY,EAAEC,eAAe,CAAC,GAAGrF,QAAQ,CAAC,IAAI,CAAC;EACtD,MAAM,CAACsF,iBAAiB,EAAEC,oBAAoB,CAAC,GAAGvF,QAAQ,CAAC,KAAK,CAAC;;EAEjE;EACA,MAAM,CAACwF,KAAK,EAAEC,QAAQ,CAAC,GAAGzF,QAAQ,CAAC,IAAI,CAAC;EACxC,MAAM,CAAC0F,YAAY,EAAEC,eAAe,CAAC,GAAG3F,QAAQ,CAAC,KAAK,CAAC;;EAEvD;EACA,MAAM,CAAC4F,kBAAkB,EAAEC,qBAAqB,CAAC,GAAG7F,QAAQ,CAAC,EAAE,CAAC;EAChE,MAAM,CAAC8F,aAAa,EAAEC,gBAAgB,CAAC,GAAG/F,QAAQ,CAAC,EAAE,CAAC;;EAEtD;EACA,MAAMgG,WAAW,GAAG,CAClB;IAAEC,KAAK,EAAE,SAAS;IAAEC,KAAK,EAAE;EAAU,CAAC,EACtC;IAAED,KAAK,EAAE,iBAAiB;IAAEC,KAAK,EAAE;EAAkB,CAAC,EACtD;IAAED,KAAK,EAAE,eAAe;IAAEC,KAAK,EAAE;EAAgB,CAAC,EAClD;IAAED,KAAK,EAAE,qBAAqB;IAAEC,KAAK,EAAE;EAAsB,CAAC,EAC9D;IAAED,KAAK,EAAE,WAAW;IAAEC,KAAK,EAAE;EAAY,CAAC,EAC1C;IAAED,KAAK,EAAE,WAAW;IAAEC,KAAK,EAAE;EAAqB,CAAC,CACpD;;EAED;EACA,MAAM,CAACC,WAAW,EAAEC,cAAc,CAAC,GAAGpG,QAAQ,CAAC,IAAI,CAAC;;EAEpD;EACA,MAAMqG,sBAAsB,GAAGA,CAAA,KAAM;IACnC;IACAR,qBAAqB,CAAC,CAAC,YAAY,EAAE,eAAe,EAAE,UAAU,CAAC,CAAC;EACpE,CAAC;;EAED;EACA5F,SAAS,CAAC,MAAM;IACd;IACAoG,sBAAsB,CAAC,CAAC;IAExB,MAAMC,SAAS,GAAG,MAAAA,CAAA,KAAY;MAC5B,IAAI;QACFC,OAAO,CAACC,GAAG,CAAC,wCAAwC,CAAC;;QAErD;QACA,MAAMC,KAAK,GAAGC,YAAY,CAACC,OAAO,CAAC,OAAO,CAAC;QAC3CJ,OAAO,CAACC,GAAG,CAAC,iBAAiB,EAAE,CAAC,CAACC,KAAK,CAAC;QACvC,IAAI,CAACA,KAAK,EAAE;UACV9B,QAAQ,CAAC,iDAAiD,CAAC;UAC3DF,UAAU,CAAC,KAAK,CAAC;UACjB;QACF;;QAEA;QACA,IAAImC,kBAAkB,GAAGF,YAAY,CAACC,OAAO,CAAC,oBAAoB,CAAC;QACnE,IAAIE,oBAAoB,GAAGH,YAAY,CAACC,OAAO,CAAC,sBAAsB,CAAC;QAEvEJ,OAAO,CAACC,GAAG,CAAC,wCAAwC,EAAE;UAAEI,kBAAkB;UAAEC;QAAqB,CAAC,CAAC;QACnGN,OAAO,CAACC,GAAG,CAAC,cAAc,EAAE1C,IAAI,CAAC;;QAEjC;QACAyC,OAAO,CAACC,GAAG,CAAC,wCAAwC,CAAC;QACrD,KAAK,IAAIM,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGJ,YAAY,CAACK,MAAM,EAAED,CAAC,EAAE,EAAE;UAC5C,MAAME,GAAG,GAAGN,YAAY,CAACM,GAAG,CAACF,CAAC,CAAC;UAC/BP,OAAO,CAACC,GAAG,CAAC,GAAGQ,GAAG,KAAKN,YAAY,CAACC,OAAO,CAACK,GAAG,CAAC,EAAE,CAAC;QACrD;;QAEA;QACA,IAAI,CAAAlD,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAEmD,IAAI,MAAK,eAAe,EAAE;UAClCV,OAAO,CAACC,GAAG,CAAC,6EAA6E,CAAC;;UAE1F;UACA,IAAI1C,IAAI,CAACoD,WAAW,EAAE;YACpBX,OAAO,CAACC,GAAG,CAAC,sCAAsC,EAAE1C,IAAI,CAACoD,WAAW,CAAC;YACrEN,kBAAkB,GAAG9C,IAAI,CAACoD,WAAW,CAACC,QAAQ,CAAC,CAAC;YAChDN,oBAAoB,GAAG/C,IAAI,CAACsD,aAAa,IAAI,YAAYtD,IAAI,CAACoD,WAAW,EAAE;;YAE3E;YACAR,YAAY,CAACW,OAAO,CAAC,oBAAoB,EAAET,kBAAkB,CAAC;YAC9DF,YAAY,CAACW,OAAO,CAAC,sBAAsB,EAAER,oBAAoB,CAAC;YAClEN,OAAO,CAACC,GAAG,CAAC,uCAAuC,EAAEI,kBAAkB,CAAC;UAC1E,CAAC,MAAM;YACL;YACA,IAAI;cACFL,OAAO,CAACC,GAAG,CAAC,qEAAqE,CAAC;cAClF,MAAMC,KAAK,GAAGC,YAAY,CAACC,OAAO,CAAC,OAAO,CAAC;cAC3C,IAAIF,KAAK,EAAE;gBACT;gBACA,MAAMa,SAAS,GAAGb,KAAK,CAACc,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC;gBACrC,MAAMC,MAAM,GAAGF,SAAS,CAACG,OAAO,CAAC,IAAI,EAAE,GAAG,CAAC,CAACA,OAAO,CAAC,IAAI,EAAE,GAAG,CAAC;gBAC9D,MAAMC,WAAW,GAAGC,kBAAkB,CAACC,IAAI,CAACJ,MAAM,CAAC,CAACD,KAAK,CAAC,EAAE,CAAC,CAACM,GAAG,CAACC,CAAC,IAAI;kBACrE,OAAO,GAAG,GAAG,CAAC,IAAI,GAAGA,CAAC,CAACC,UAAU,CAAC,CAAC,CAAC,CAACZ,QAAQ,CAAC,EAAE,CAAC,EAAEa,KAAK,CAAC,CAAC,CAAC,CAAC;gBAC9D,CAAC,CAAC,CAACC,IAAI,CAAC,EAAE,CAAC,CAAC;gBAEZ,MAAMC,OAAO,GAAGC,IAAI,CAACC,KAAK,CAACV,WAAW,CAAC;gBACvCnB,OAAO,CAACC,GAAG,CAAC,wBAAwB,EAAE0B,OAAO,CAAC;gBAE9C,IAAIA,OAAO,CAAChB,WAAW,EAAE;kBACvBX,OAAO,CAACC,GAAG,CAAC,oCAAoC,EAAE0B,OAAO,CAAChB,WAAW,CAAC;kBACtEN,kBAAkB,GAAGsB,OAAO,CAAChB,WAAW,CAACC,QAAQ,CAAC,CAAC;kBACnD;kBACAN,oBAAoB,GAAG,YAAYqB,OAAO,CAAChB,WAAW,EAAE;;kBAExD;kBACAR,YAAY,CAACW,OAAO,CAAC,oBAAoB,EAAET,kBAAkB,CAAC;kBAC9DF,YAAY,CAACW,OAAO,CAAC,sBAAsB,EAAER,oBAAoB,CAAC;kBAClEN,OAAO,CAACC,GAAG,CAAC,uCAAuC,EAAEI,kBAAkB,CAAC;gBAC1E;cACF;YACF,CAAC,CAAC,OAAOyB,CAAC,EAAE;cACV9B,OAAO,CAAC7B,KAAK,CAAC,6CAA6C,EAAE2D,CAAC,CAAC;YACjE;UACF;QACF;;QAEA;QACA,IAAI,CAACzB,kBAAkB,IAAIA,kBAAkB,KAAK,WAAW,IAAIA,kBAAkB,KAAK,MAAM,EAAE;UAC9FL,OAAO,CAAC+B,IAAI,CAAC,6EAA6E,CAAC;UAC3F;UACA1B,kBAAkB,GAAG,GAAG,CAAC,CAAC;UAC1BC,oBAAoB,GAAG,gBAAgB;;UAEvC;UACAH,YAAY,CAACW,OAAO,CAAC,oBAAoB,EAAET,kBAAkB,CAAC;UAC9DF,YAAY,CAACW,OAAO,CAAC,sBAAsB,EAAER,oBAAoB,CAAC;UAClEN,OAAO,CAACC,GAAG,CAAC,iDAAiD,EAAEI,kBAAkB,CAAC;QACpF;;QAEA;QACA,IAAI,CAACA,kBAAkB,EAAE;UACvBjC,QAAQ,CAAC,8DAA8D,CAAC;UACxEF,UAAU,CAAC,KAAK,CAAC;UACjB;QACF;;QAEA;QACA,MAAM8D,aAAa,GAAGC,QAAQ,CAAC5B,kBAAkB,EAAE,EAAE,CAAC;QACtDL,OAAO,CAACC,GAAG,CAAC,kCAAkC,EAAE+B,aAAa,CAAC;QAC9D,IAAIE,KAAK,CAACF,aAAa,CAAC,EAAE;UACxB5D,QAAQ,CAAC,2BAA2BiC,kBAAkB,mCAAmC,CAAC;UAC1FnC,UAAU,CAAC,KAAK,CAAC;UACjB;QACF;;QAEA;QACAR,aAAa,CAACsE,aAAa,CAAC;QAC5BpE,eAAe,CAAC0C,oBAAoB,IAAI,YAAY0B,aAAa,EAAE,CAAC;;QAEpE;QACA,IAAI;UACF5C,eAAe,CAAC,IAAI,CAAC;UACrBY,OAAO,CAACC,GAAG,CAAC,4CAA4C,EAAE+B,aAAa,CAAC;UACxE,MAAMG,SAAS,GAAG,MAAMpF,WAAW,CAACqF,YAAY,CAACJ,aAAa,CAAC;UAC/DhC,OAAO,CAACC,GAAG,CAAC,4BAA4B,EAAEkC,SAAS,CAAC;UACpDjD,QAAQ,CAACiD,SAAS,CAAC;;UAEnB;UACA,IAAIA,SAAS,IAAIA,SAAS,CAACE,KAAK,EAAE;YAChC,MAAMA,KAAK,GAAGF,SAAS,CAACE,KAAK,CAACf,GAAG,CAACgB,IAAI,IAAIA,IAAI,CAACC,KAAK,CAAC,CAACC,MAAM,CAACD,KAAK,IAAIA,KAAK,KAAK,iBAAiB,CAAC;YAClGjD,qBAAqB,CAAC+C,KAAK,CAAC;UAC9B;UAEA,IAAIF,SAAS,IAAIA,SAAS,CAACM,SAAS,EAAE;YACpC,MAAMA,SAAS,GAAGN,SAAS,CAACM,SAAS,CAACnB,GAAG,CAACgB,IAAI,IAAIA,IAAI,CAAC5D,SAAS,CAAC,CAAC8D,MAAM,CAACE,IAAI,IAAIA,IAAI,KAAK,iBAAiB,CAAC;YAC5GlD,gBAAgB,CAACiD,SAAS,CAAC;UAC7B;UAEArD,eAAe,CAAC,KAAK,CAAC;QACxB,CAAC,CAAC,OAAOuD,UAAU,EAAE;UACnB3C,OAAO,CAAC7B,KAAK,CAAC,2CAA2C,EAAEwE,UAAU,CAAC;UACtEvD,eAAe,CAAC,KAAK,CAAC;UACtB;QACF;;QAEA;QACAY,OAAO,CAACC,GAAG,CAAC,uCAAuC,EAAE+B,aAAa,CAAC;QACnE,IAAI;UACF;UACA,MAAMY,cAAc,GAAG,IAAIC,OAAO,CAAC,CAACC,CAAC,EAAEC,MAAM,KAAK;YAChDC,UAAU,CAAC,MAAMD,MAAM,CAAC,IAAIE,KAAK,CAAC,gDAAgD,CAAC,CAAC,EAAE,KAAK,CAAC,CAAC,CAAC;UAChG,CAAC,CAAC;;UAEF;UACAjD,OAAO,CAACC,GAAG,CAAC,oDAAoD,EAAE1B,OAAO,CAAC;UAC1E,MAAM2E,WAAW,GAAGnG,WAAW,CAACoG,OAAO,CAACnB,aAAa,EAAE,CAAC,EAAEzD,OAAO,CAAC;UAClE,MAAM6E,MAAM,GAAG,MAAMP,OAAO,CAACQ,IAAI,CAAC,CAACH,WAAW,EAAEN,cAAc,CAAC,CAAC;UAEhE5C,OAAO,CAACC,GAAG,CAAC,uBAAuB,EAAEmD,MAAM,CAAC;UAC5CpD,OAAO,CAACC,GAAG,CAAC,gCAAgC,EAAEmD,MAAM,GAAGA,MAAM,CAAC5C,MAAM,GAAG,CAAC,CAAC;UACzE,IAAI4C,MAAM,IAAIA,MAAM,CAAC5C,MAAM,GAAG,CAAC,EAAE;YAC/BR,OAAO,CAACC,GAAG,CAAC,oBAAoB,EAAEmD,MAAM,CAAC,CAAC,CAAC,CAAC;UAC9C,CAAC,MAAM;YACLpD,OAAO,CAAC+B,IAAI,CAAC,4CAA4C,EAAEC,aAAa,CAAC;UAC3E;UACAlE,aAAa,CAACsF,MAAM,IAAI,EAAE,CAAC;QAC7B,CAAC,CAAC,OAAOE,SAAS,EAAE;UAClBtD,OAAO,CAAC7B,KAAK,CAAC,yCAAyC,EAAEmF,SAAS,CAAC;UACnEtD,OAAO,CAAC7B,KAAK,CAAC,8BAA8B,EAAE;YAC5CoF,OAAO,EAAED,SAAS,CAACC,OAAO;YAC1BC,MAAM,EAAEF,SAAS,CAACE,MAAM;YACxBC,IAAI,EAAEH,SAAS,CAACG,IAAI;YACpBC,KAAK,EAAEJ,SAAS,CAACI,KAAK;YACtBC,IAAI,EAAEL,SAAS,CAACK,IAAI;YACpBC,IAAI,EAAEN,SAAS,CAACM,IAAI;YACpBC,QAAQ,EAAEP,SAAS,CAACO,QAAQ,GAAG;cAC7BL,MAAM,EAAEF,SAAS,CAACO,QAAQ,CAACL,MAAM;cACjCM,UAAU,EAAER,SAAS,CAACO,QAAQ,CAACC,UAAU;cACzCL,IAAI,EAAEH,SAAS,CAACO,QAAQ,CAACJ;YAC3B,CAAC,GAAG;UACN,CAAC,CAAC;;UAEF;UACA3F,aAAa,CAAC,EAAE,CAAC;UACjBkC,OAAO,CAAC+B,IAAI,CAAC,sDAAsD,CAAC;;UAEpE;UACA3D,QAAQ,CAAC,2CAA2CkF,SAAS,CAACC,OAAO,+CAA+C,CAAC;QACvH;;QAEA;QACAvD,OAAO,CAACC,GAAG,CAAC,sCAAsC,EAAE+B,aAAa,CAAC;QAClE,IAAI;UACF;UACA,MAAMY,cAAc,GAAG,IAAIC,OAAO,CAAC,CAACC,CAAC,EAAEC,MAAM,KAAK;YAChDC,UAAU,CAAC,MAAMD,MAAM,CAAC,IAAIE,KAAK,CAAC,+CAA+C,CAAC,CAAC,EAAE,KAAK,CAAC,CAAC,CAAC;UAC/F,CAAC,CAAC;;UAEF;UACAjD,OAAO,CAACC,GAAG,CAAC,0CAA0C,CAAC;UACvD;UACA,MAAM8D,YAAY,GAAGhH,WAAW,CAACoG,OAAO,CAACnB,aAAa,EAAE,CAAC,CAAC;UAC1D,MAAMgC,KAAK,GAAG,MAAMnB,OAAO,CAACQ,IAAI,CAAC,CAACU,YAAY,EAAEnB,cAAc,CAAC,CAAC;UAEhE5C,OAAO,CAACC,GAAG,CAAC,sBAAsB,EAAE+D,KAAK,CAAC;UAC1ChE,OAAO,CAACC,GAAG,CAAC,+BAA+B,EAAE+D,KAAK,GAAGA,KAAK,CAACxD,MAAM,GAAG,CAAC,CAAC;UACtE,IAAIwD,KAAK,IAAIA,KAAK,CAACxD,MAAM,GAAG,CAAC,EAAE;YAC7BR,OAAO,CAACC,GAAG,CAAC,mBAAmB,EAAE+D,KAAK,CAAC,CAAC,CAAC,CAAC;UAC5C,CAAC,MAAM;YACLhE,OAAO,CAAC+B,IAAI,CAAC,2CAA2C,EAAEC,aAAa,CAAC;UAC1E;UACAhE,YAAY,CAACgG,KAAK,IAAI,EAAE,CAAC;QAC3B,CAAC,CAAC,OAAOC,UAAU,EAAE;UACnBjE,OAAO,CAAC7B,KAAK,CAAC,wCAAwC,EAAE8F,UAAU,CAAC;UACnEjE,OAAO,CAAC7B,KAAK,CAAC,6BAA6B,EAAE;YAC3CoF,OAAO,EAAEU,UAAU,CAACV,OAAO;YAC3BC,MAAM,EAAES,UAAU,CAACT,MAAM;YACzBC,IAAI,EAAEQ,UAAU,CAACR,IAAI;YACrBC,KAAK,EAAEO,UAAU,CAACP,KAAK;YACvBC,IAAI,EAAEM,UAAU,CAACN,IAAI;YACrBC,IAAI,EAAEK,UAAU,CAACL,IAAI;YACrBC,QAAQ,EAAEI,UAAU,CAACJ,QAAQ,GAAG;cAC9BL,MAAM,EAAES,UAAU,CAACJ,QAAQ,CAACL,MAAM;cAClCM,UAAU,EAAEG,UAAU,CAACJ,QAAQ,CAACC,UAAU;cAC1CL,IAAI,EAAEQ,UAAU,CAACJ,QAAQ,CAACJ;YAC5B,CAAC,GAAG;UACN,CAAC,CAAC;;UAEF;UACAzF,YAAY,CAAC,EAAE,CAAC;;UAEhB;UACA,IAAI,CAACG,KAAK,EAAE;YACVC,QAAQ,CAAC,0CAA0C6F,UAAU,CAACV,OAAO,+CAA+C,CAAC;UACvH;QACF;;QAEA;QACArF,UAAU,CAAC,KAAK,CAAC;MAEnB,CAAC,CAAC,OAAOgG,GAAG,EAAE;QAAA,IAAAC,aAAA,EAAAC,cAAA,EAAAC,cAAA,EAAAC,cAAA,EAAAC,cAAA,EAAAC,mBAAA;QACZxE,OAAO,CAAC7B,KAAK,CAAC,kCAAkC,EAAE+F,GAAG,CAAC;QACtDlE,OAAO,CAAC7B,KAAK,CAAC,2BAA2B,EAAE;UACzCoF,OAAO,EAAEW,GAAG,CAACX,OAAO;UACpBC,MAAM,EAAEU,GAAG,CAACV,MAAM,MAAAW,aAAA,GAAID,GAAG,CAACL,QAAQ,cAAAM,aAAA,uBAAZA,aAAA,CAAcX,MAAM;UAC1CC,IAAI,EAAES,GAAG,CAACT,IAAI,MAAAW,cAAA,GAAIF,GAAG,CAACL,QAAQ,cAAAO,cAAA,uBAAZA,cAAA,CAAcX,IAAI;UACpCC,KAAK,EAAEQ,GAAG,CAACR;QACb,CAAC,CAAC;;QAEF;QACA,IAAIe,YAAY,GAAG,oBAAoB;QAEvC,IAAIP,GAAG,CAACX,OAAO,IAAIW,GAAG,CAACX,OAAO,CAACmB,QAAQ,CAAC,wBAAwB,CAAC,EAAE;UACjED,YAAY,GAAGP,GAAG,CAACX,OAAO;QAC5B,CAAC,MAAM,IAAIW,GAAG,CAACV,MAAM,KAAK,GAAG,IAAIU,GAAG,CAACV,MAAM,KAAK,GAAG,IACzC,EAAAa,cAAA,GAAAH,GAAG,CAACL,QAAQ,cAAAQ,cAAA,uBAAZA,cAAA,CAAcb,MAAM,MAAK,GAAG,IAAI,EAAAc,cAAA,GAAAJ,GAAG,CAACL,QAAQ,cAAAS,cAAA,uBAAZA,cAAA,CAAcd,MAAM,MAAK,GAAG,EAAE;UACtEiB,YAAY,GAAG,mEAAmE;QACpF,CAAC,MAAM,KAAAF,cAAA,GAAIL,GAAG,CAACL,QAAQ,cAAAU,cAAA,gBAAAC,mBAAA,GAAZD,cAAA,CAAcd,IAAI,cAAAe,mBAAA,eAAlBA,mBAAA,CAAoBG,MAAM,EAAE;UACrC;UACAF,YAAY,GAAG,eAAeP,GAAG,CAACL,QAAQ,CAACJ,IAAI,CAACkB,MAAM,EAAE;QAC1D,CAAC,MAAM,IAAIT,GAAG,CAACP,IAAI,KAAK,aAAa,EAAE;UACrC;UACAc,YAAY,GAAG,yEAAyE;QAC1F,CAAC,MAAM,IAAIP,GAAG,CAACX,OAAO,EAAE;UACtBkB,YAAY,GAAGP,GAAG,CAACX,OAAO;QAC5B;QAEAnF,QAAQ,CAAC,gCAAgCqG,YAAY,sBAAsB,CAAC;;QAE5E;QACA3G,aAAa,CAAC,EAAE,CAAC;QACjBE,YAAY,CAAC,EAAE,CAAC;MAClB,CAAC,SAAS;QACRE,UAAU,CAAC,KAAK,CAAC;MACnB;IACF,CAAC;IAED6B,SAAS,CAAC,CAAC;EACb,CAAC,EAAE,CAACxB,OAAO,CAAC,CAAC,CAAC,CAAC;;EAEf;EACA,MAAMqG,kBAAkB,GAAIC,KAAK,IAAK;IACpC,MAAM;MAAEjB,IAAI;MAAElE;IAAM,CAAC,GAAGmF,KAAK,CAACC,MAAM;IACpC9E,OAAO,CAACC,GAAG,CAAC,oBAAoB2D,IAAI,MAAMlE,KAAK,EAAE,CAAC;;IAElD;IACA,MAAMqF,UAAU,GAAG;MACjB,GAAGxG,OAAO;MACV,CAACqF,IAAI,GAAGlE;IACV,CAAC;IAEDM,OAAO,CAACC,GAAG,CAAC,eAAe,EAAE8E,UAAU,CAAC;IACxCvG,UAAU,CAACuG,UAAU,CAAC;EACxB,CAAC;;EAED;EACA,MAAMC,YAAY,GAAGA,CAAA,KAAM;IACzBxG,UAAU,CAAC;MACTC,mBAAmB,EAAE,EAAE;MACvBC,SAAS,EAAE,EAAE;MACbC,OAAO,EAAE,EAAE;MACXC,UAAU,EAAE;IACd,CAAC,CAAC;EACJ,CAAC;;EAED;EACA,MAAMqG,iBAAiB,GAAIC,IAAI,IAAK;IAClCpG,eAAe,CAACoG,IAAI,CAAC;IACrBlG,oBAAoB,CAAC,IAAI,CAAC;EAC5B,CAAC;;EAED;EACA,MAAMmG,kBAAkB,GAAGA,CAAA,KAAM;IAC/BnG,oBAAoB,CAAC,KAAK,CAAC;IAC3BF,eAAe,CAAC,IAAI,CAAC;EACvB,CAAC;;EAED;;EAEA;EACA,MAAMsG,eAAe,GAAIC,IAAI,IAAK;IAChC,IAAI,CAACA,IAAI,IAAIA,IAAI,CAAC7E,MAAM,KAAK,CAAC,EAAE;MAC9B,oBACEtD,OAAA,CAAChD,KAAK;QAACoL,QAAQ,EAAC,MAAM;QAAAC,QAAA,GAAC,0CAErB,eAAArI,OAAA,CAACpD,MAAM;UACL0L,OAAO,EAAC,MAAM;UACdC,KAAK,EAAC,SAAS;UACfC,OAAO,EAAEA,CAAA,KAAMC,MAAM,CAACC,QAAQ,CAACC,MAAM,CAAC,CAAE;UACxCC,EAAE,EAAE;YAAEC,EAAE,EAAE;UAAE,CAAE;UAAAR,QAAA,EACf;QAED;UAAAS,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACJ,CAAC;IAEZ;;IAEA;IACA,MAAMC,cAAc,GAAI7D,KAAK,IAAK;MAChC;MACA,MAAM8D,UAAU,GAAG,CAAC9D,KAAK,IAAI,EAAE,EAAE+D,WAAW,CAAC,CAAC;MAE9C,QAAQD,UAAU;QAChB,KAAK,QAAQ;QACb,KAAK,YAAY;UAAE;UACjB,OAAO,SAAS;QAAE;QACpB,KAAK,oBAAoB;UACvB,OAAO,SAAS;QAAE;QACpB,KAAK,kBAAkB;UACrB,OAAO,SAAS;QAAE;QACpB,KAAK,WAAW;UACd,OAAO,SAAS;QAAE;QACpB,KAAK,WAAW;QAChB,KAAK,eAAe;UAAE;UACpB,OAAO,SAAS;QAAE;QACpB,KAAK,SAAS;QACd,KAAK,UAAU,CAAC,CAAC;QACjB,KAAK,kBAAkB;UACrB,OAAO,SAAS;QAAE;QACpB;UACErG,OAAO,CAACC,GAAG,CAAC,yBAAyB,EAAEsC,KAAK,CAAC;UAC7C,OAAO,SAAS;QAAE;MACtB;IACF,CAAC;;IAED;IACA,MAAMgE,mBAAmB,GAAIC,YAAY,IAAK;MAC5C,IAAI,CAACA,YAAY,EAAE,OAAO,eAAe;MAEzC,QAAQA,YAAY;QAClB,KAAK,CAAC;UAAE,OAAO,oBAAoB;QACnC,KAAK,CAAC;UAAE,OAAO,kBAAkB;QACjC,KAAK,CAAC;UAAE,OAAO,WAAW;QAC1B;UAAS,OAAO,eAAe;MACjC;IACF,CAAC;;IAED;;IAEA,IAAInI,QAAQ,KAAK,OAAO,EAAE;MACxB,oBACEnB,OAAA,CAAC3C,cAAc;QAACkM,SAAS,EAAE5M,KAAM;QAACiM,EAAE,EAAE;UAAEY,EAAE,EAAE,CAAC;UAAEC,SAAS,EAAE;QAAO,CAAE;QAAApB,QAAA,eACjErI,OAAA,CAAC9C,KAAK;UAACwM,IAAI,EAAC,OAAO;UAAArB,QAAA,gBACjBrI,OAAA,CAAC1C,SAAS;YAAA+K,QAAA,eACRrI,OAAA,CAACzC,QAAQ;cAAA8K,QAAA,gBACPrI,OAAA,CAAC5C,SAAS;gBAAAiL,QAAA,EAAC;cAAO;gBAAAS,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAW,CAAC,eAC9BjJ,OAAA,CAAC5C,SAAS;gBAAAiL,QAAA,EAAC;cAAO;gBAAAS,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAW,CAAC,eAC9BjJ,OAAA,CAAC5C,SAAS;gBAAAiL,QAAA,EAAC;cAAS;gBAAAS,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAW,CAAC,eAChCjJ,OAAA,CAAC5C,SAAS;gBAAAiL,QAAA,EAAC;cAAM;gBAAAS,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAW,CAAC,eAC7BjJ,OAAA,CAAC5C,SAAS;gBAAAiL,QAAA,EAAC;cAAO;gBAAAS,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAW,CAAC,eAC9BjJ,OAAA,CAAC5C,SAAS;gBAAAiL,QAAA,EAAC;cAAY;gBAAAS,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAW,CAAC,eACnCjJ,OAAA,CAAC5C,SAAS;gBAAAiL,QAAA,EAAC;cAAW;gBAAAS,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAW,CAAC,eAClCjJ,OAAA,CAAC5C,SAAS;gBAAAiL,QAAA,EAAC;cAAQ;gBAAAS,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAW,CAAC,eAC/BjJ,OAAA,CAAC5C,SAAS;gBAAAiL,QAAA,EAAC;cAAQ;gBAAAS,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAW,CAAC,eAC/BjJ,OAAA,CAAC5C,SAAS;gBAAAiL,QAAA,EAAC;cAAK;gBAAAS,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAW,CAAC,eAC5BjJ,OAAA,CAAC5C,SAAS;gBAAAiL,QAAA,EAAC;cAAY;gBAAAS,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAW,CAAC,eACnCjJ,OAAA,CAAC5C,SAAS;gBAAAiL,QAAA,EAAC;cAAM;gBAAAS,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAW,CAAC,eAC7BjJ,OAAA,CAAC5C,SAAS;gBAAAiL,QAAA,EAAC;cAAM;gBAAAS,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAW,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACrB;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACF,CAAC,eACZjJ,OAAA,CAAC7C,SAAS;YAAAkL,QAAA,EACPF,IAAI,CAAC/D,GAAG,CAAE4D,IAAI,IAAK;cAClB;cACA,MAAM2B,OAAO,GAAGC,MAAM,CAAC5B,IAAI,CAAC2B,OAAO,CAAC,CAAC3F,OAAO,CAAC,GAAG,EAAE,EAAE,CAAC;cACrD,MAAM6F,OAAO,GAAG7B,IAAI,CAAC6B,OAAO,IAAI,GAAG;cACnC,MAAMrI,SAAS,GAAGwG,IAAI,CAACxG,SAAS,IAAI,GAAG;;cAEvC;cACA,IAAIsI,YAAY,GAAG,GAAG;cACtB,IAAI;gBACF,MAAMC,UAAU,GAAGhF,QAAQ,CAACiD,IAAI,CAAC8B,YAAY,EAAE,EAAE,CAAC,IAAI,CAAC;gBACvDA,YAAY,GAAGC,UAAU,GAAG,CAAC,GAAGH,MAAM,CAACG,UAAU,CAAC,GAAG,GAAG;cAC1D,CAAC,CAAC,OAAOnF,CAAC,EAAE;gBACVkF,YAAY,GAAG9B,IAAI,CAAC8B,YAAY,IAAI,GAAG;cACzC;;cAEA;cACA,IAAIE,OAAO,GAAG,GAAG;cACjB,MAAMC,WAAW,GAAGjC,IAAI,CAACgC,OAAO;cAChC,IAAI,OAAOC,WAAW,KAAK,QAAQ,IAAIA,WAAW,KAAK,CAAC,EAAE;gBACxDD,OAAO,GAAG,GAAG;cACf,CAAC,MAAM;gBACLA,OAAO,GAAGC,WAAW,GAAGL,MAAM,CAACK,WAAW,CAAC,GAAG,GAAG;cACnD;cAEA,MAAMC,mBAAmB,GAAGlC,IAAI,CAACkC,mBAAmB,IAAI,GAAG;cAC3D,MAAMC,iBAAiB,GAAGnC,IAAI,CAACmC,iBAAiB,IAAI,GAAG;cACvD,MAAMC,aAAa,GAAGpC,IAAI,CAACoC,aAAa,GAAG,GAAGC,UAAU,CAACrC,IAAI,CAACoC,aAAa,CAAC,CAACE,OAAO,CAAC,CAAC,CAAC,EAAE,GAAG,GAAG;cAC/F,MAAMC,WAAW,GAAGvC,IAAI,CAACwC,eAAe,GAAG,GAAGH,UAAU,CAACrC,IAAI,CAACwC,eAAe,CAAC,CAACF,OAAO,CAAC,CAAC,CAAC,EAAE,GAAG,GAAG;cACjG,MAAMjF,KAAK,GAAG2C,IAAI,CAACzG,mBAAmB,IAAI,eAAe;cACzD,MAAM+H,YAAY,GAAGD,mBAAmB,CAACrB,IAAI,CAACsB,YAAY,CAAC;cAC3D,MAAMmB,MAAM,GAAGzC,IAAI,CAAC0C,SAAS,IAAI,GAAG;;cAEpC;cACA,MAAMC,WAAW,GAAGzB,cAAc,CAAC7D,KAAK,CAAC;cAEzC,oBACErF,OAAA,CAACzC,QAAQ;gBAEPqL,EAAE,EAAE;kBAAE,SAAS,EAAE;oBAAEgC,eAAe,EAAE;kBAAU;gBAAE,CAAE;gBAClDpC,OAAO,EAAEA,CAAA,KAAMT,iBAAiB,CAACC,IAAI,CAAE;gBAAAK,QAAA,gBAEvCrI,OAAA,CAAC5C,SAAS;kBAAAiL,QAAA,EAAEsB;gBAAO;kBAAAb,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAY,CAAC,eAChCjJ,OAAA,CAAC5C,SAAS;kBAAAiL,QAAA,EAAEwB;gBAAO;kBAAAf,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAY,CAAC,eAChCjJ,OAAA,CAAC5C,SAAS;kBAAAiL,QAAA,EAAE7G;gBAAS;kBAAAsH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAY,CAAC,eAClCjJ,OAAA,CAAC5C,SAAS;kBAAAiL,QAAA,EAAEyB;gBAAY;kBAAAhB,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAY,CAAC,eACrCjJ,OAAA,CAAC5C,SAAS;kBAAAiL,QAAA,EAAE2B;gBAAO;kBAAAlB,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAY,CAAC,eAChCjJ,OAAA,CAAC5C,SAAS;kBAAAiL,QAAA,EAAE6B;gBAAmB;kBAAApB,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAY,CAAC,eAC5CjJ,OAAA,CAAC5C,SAAS;kBAAAiL,QAAA,EAAE8B;gBAAiB;kBAAArB,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAY,CAAC,eAC1CjJ,OAAA,CAAC5C,SAAS;kBAAAiL,QAAA,EAAE+B;gBAAa;kBAAAtB,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAY,CAAC,eACtCjJ,OAAA,CAAC5C,SAAS;kBAAAiL,QAAA,EAAEkC;gBAAW;kBAAAzB,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAY,CAAC,eACpCjJ,OAAA,CAAC5C,SAAS;kBAAAiL,QAAA,eACRrI,OAAA,CAACjC,IAAI;oBACH0E,KAAK,EAAE3C,2BAA2B,CAACuF,KAAK,CAAE;oBAC1CqE,IAAI,EAAC,OAAO;oBACZd,EAAE,EAAE;sBACFgC,eAAe,EAAED,WAAW;sBAC5BpC,KAAK,EAAE,MAAM;sBACbsC,UAAU,EAAE;oBACd;kBAAE;oBAAA/B,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACH;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACO,CAAC,eACZjJ,OAAA,CAAC5C,SAAS;kBAAAiL,QAAA,EAAEiB;gBAAY;kBAAAR,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAY,CAAC,eACrCjJ,OAAA,CAAC5C,SAAS;kBAAAiL,QAAA,EAAEoC;gBAAM;kBAAA3B,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAY,CAAC,eAC/BjJ,OAAA,CAAC5C,SAAS;kBAAAiL,QAAA,eACRrI,OAAA,CAAC/C,UAAU;oBACTyM,IAAI,EAAC,OAAO;oBACZlB,OAAO,EAAG5D,CAAC,IAAK;sBACdA,CAAC,CAACkG,eAAe,CAAC,CAAC;sBACnB/C,iBAAiB,CAACC,IAAI,CAAC;oBACzB,CAAE;oBACF+C,KAAK,EAAC,qBAAqB;oBAAA1C,QAAA,eAE3BrI,OAAA,CAACV,QAAQ;sBAAC0L,QAAQ,EAAC;oBAAO;sBAAAlC,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAE;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACnB;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACJ,CAAC;cAAA,GArCPjB,IAAI,CAAC2B,OAAO;gBAAAb,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAsCT,CAAC;YAEf,CAAC;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACO,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACP;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACM,CAAC;IAErB,CAAC,MAAM;MACL;MACA,oBACEjJ,OAAA,CAACnD,IAAI;QAACoO,SAAS;QAACC,OAAO,EAAE,CAAE;QAAA7C,QAAA,EACxBF,IAAI,CAAC/D,GAAG,CAAE4D,IAAI,IAAK;UAClB,MAAM3C,KAAK,GAAG2C,IAAI,CAACzG,mBAAmB,IAAI,eAAe;UACzD,MAAMoJ,WAAW,GAAGzB,cAAc,CAAC7D,KAAK,CAAC;UACzC,MAAMiE,YAAY,GAAGD,mBAAmB,CAACrB,IAAI,CAACsB,YAAY,CAAC;UAE3D,oBACEtJ,OAAA,CAACnD,IAAI;YAACuI,IAAI;YAAC+F,EAAE,EAAE,EAAG;YAACC,EAAE,EAAE,CAAE;YAACC,EAAE,EAAE,CAAE;YAAAhD,QAAA,eAC9BrI,OAAA,CAAClD,IAAI;cACH8L,EAAE,EAAE;gBACF0C,MAAM,EAAE,SAAS;gBACjB,SAAS,EAAE;kBAAEC,SAAS,EAAE;gBAAE,CAAC;gBAC3BC,UAAU,EAAE,aAAab,WAAW;cACtC,CAAE;cACFnC,OAAO,EAAEA,CAAA,KAAMT,iBAAiB,CAACC,IAAI,CAAE;cAAAK,QAAA,eAEvCrI,OAAA,CAACjD,WAAW;gBAAAsL,QAAA,gBACVrI,OAAA,CAACtD,UAAU;kBAAC4L,OAAO,EAAC,IAAI;kBAACiB,SAAS,EAAC,KAAK;kBAACX,EAAE,EAAE;oBAAE6C,OAAO,EAAE,MAAM;oBAAEC,cAAc,EAAE;kBAAgB,CAAE;kBAAArD,QAAA,GAC/FL,IAAI,CAAC2B,OAAO,eACb3J,OAAA,CAACjC,IAAI;oBACH0E,KAAK,EAAE3C,2BAA2B,CAACuF,KAAK,CAAE;oBAC1CqE,IAAI,EAAC,OAAO;oBACZd,EAAE,EAAE;sBACFgC,eAAe,EAAED,WAAW;sBAC5BpC,KAAK,EAAE,MAAM;sBACbsC,UAAU,EAAE;oBACd;kBAAE;oBAAA/B,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACH,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACQ,CAAC,eACbjJ,OAAA,CAACtD,UAAU;kBAAC4L,OAAO,EAAC,OAAO;kBAACC,KAAK,EAAC,gBAAgB;kBAAAF,QAAA,GAAC,WACxC,EAACL,IAAI,CAAC2D,OAAO,IAAI,KAAK;gBAAA;kBAAA7C,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACrB,CAAC,eACbjJ,OAAA,CAACtD,UAAU;kBAAC4L,OAAO,EAAC,OAAO;kBAACC,KAAK,EAAC,gBAAgB;kBAAAF,QAAA,GAAC,aACtC,EAACL,IAAI,CAACxG,SAAS,IAAI,KAAK;gBAAA;kBAAAsH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACzB,CAAC,eACbjJ,OAAA,CAACtD,UAAU;kBAAC4L,OAAO,EAAC,OAAO;kBAACC,KAAK,EAAC,gBAAgB;kBAAAF,QAAA,GAAC,YACvC,EAACL,IAAI,CAACkC,mBAAmB,IAAI,KAAK,EAAC,KAAG,EAAClC,IAAI,CAAC4D,eAAe,IAAI,KAAK;gBAAA;kBAAA9C,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACpE,CAAC,eACbjJ,OAAA,CAACtD,UAAU;kBAAC4L,OAAO,EAAC,OAAO;kBAACC,KAAK,EAAC,gBAAgB;kBAAAF,QAAA,GAAC,UACzC,EAACL,IAAI,CAACmC,iBAAiB,IAAI,KAAK,EAAC,KAAG,EAACnC,IAAI,CAAC6D,aAAa,IAAI,KAAK;gBAAA;kBAAA/C,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAC9D,CAAC,eACbjJ,OAAA,CAACtD,UAAU;kBAAC4L,OAAO,EAAC,OAAO;kBAACC,KAAK,EAAC,gBAAgB;kBAAAF,QAAA,GAAC,iBAClC,EAACL,IAAI,CAACoC,aAAa,IAAI,KAAK;gBAAA;kBAAAtB,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACjC,CAAC,eACbjJ,OAAA,CAACtD,UAAU;kBAAC4L,OAAO,EAAC,OAAO;kBAACC,KAAK,EAAC,gBAAgB;kBAAAF,QAAA,GAAC,mBAChC,EAACL,IAAI,CAACwC,eAAe,IAAI,GAAG;gBAAA;kBAAA1B,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACnC,CAAC,eACbjJ,OAAA,CAACtD,UAAU;kBAAC4L,OAAO,EAAC,OAAO;kBAACC,KAAK,EAAC,gBAAgB;kBAAAF,QAAA,GAAC,gBACnC,EAACiB,YAAY;gBAAA;kBAAAR,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACjB,CAAC,eACbjJ,OAAA,CAACtD,UAAU;kBAAC4L,OAAO,EAAC,OAAO;kBAACC,KAAK,EAAC,gBAAgB;kBAAAF,QAAA,GAAC,UACzC,EAACL,IAAI,CAAC0C,SAAS,IAAI,KAAK;gBAAA;kBAAA5B,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACtB,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACF;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACV;UAAC,GA/C6BjB,IAAI,CAAC2B,OAAO;YAAAb,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAgD5C,CAAC;QAEX,CAAC;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACE,CAAC;IAEX;EACF,CAAC;;EAED;EACA,MAAM6C,oBAAoB,GAAIC,IAAI,IAAK;IACrC3K,WAAW,CAAC2K,IAAI,CAAC;EACnB,CAAC;;EAED;EACA,MAAMC,mBAAmB,GAAGA,CAAA,KAAM;IAChC,IAAI,CAACrK,YAAY,EAAE,OAAO,IAAI;IAE9B,oBACE3B,OAAA,CAAC7B,MAAM;MAAC8N,IAAI,EAAEpK,iBAAkB;MAACqK,OAAO,EAAEjE,kBAAmB;MAACkE,QAAQ,EAAC,IAAI;MAACC,SAAS;MAAA/D,QAAA,gBACnFrI,OAAA,CAAC5B,WAAW;QAAAiK,QAAA,GAAC,iBACI,EAAC1G,YAAY,CAACgI,OAAO;MAAA;QAAAb,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACzB,CAAC,eACdjJ,OAAA,CAAC3B,aAAa;QAACgO,QAAQ;QAAAhE,QAAA,eACrBrI,OAAA,CAACnD,IAAI;UAACoO,SAAS;UAACC,OAAO,EAAE,CAAE;UAAA7C,QAAA,gBACzBrI,OAAA,CAACnD,IAAI;YAACuI,IAAI;YAAC+F,EAAE,EAAE,EAAG;YAACE,EAAE,EAAE,CAAE;YAAAhD,QAAA,gBACvBrI,OAAA,CAACtD,UAAU;cAAC4L,OAAO,EAAC,WAAW;cAACgE,YAAY;cAAAjE,QAAA,EAAC;YAAqB;cAAAS,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAY,CAAC,eAC/EjJ,OAAA,CAACvD,GAAG;cAACmM,EAAE,EAAE;gBAAE2D,EAAE,EAAE;cAAE,CAAE;cAAAlE,QAAA,gBACjBrI,OAAA,CAACtD,UAAU;gBAAC4L,OAAO,EAAC,OAAO;gBAAAD,QAAA,gBAACrI,OAAA;kBAAAqI,QAAA,EAAQ;gBAAQ;kBAAAS,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,KAAC,EAACtH,YAAY,CAACgK,OAAO,IAAI,KAAK;cAAA;gBAAA7C,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAa,CAAC,eAClGjJ,OAAA,CAACtD,UAAU;gBAAC4L,OAAO,EAAC,OAAO;gBAAAD,QAAA,gBAACrI,OAAA;kBAAAqI,QAAA,EAAQ;gBAAQ;kBAAAS,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,KAAC,EAACtH,YAAY,CAACkI,OAAO,IAAI,KAAK;cAAA;gBAAAf,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAa,CAAC,eAClGjJ,OAAA,CAACtD,UAAU;gBAAC4L,OAAO,EAAC,OAAO;gBAAAD,QAAA,gBAACrI,OAAA;kBAAAqI,QAAA,EAAQ;gBAAU;kBAAAS,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,KAAC,EAACtH,YAAY,CAACH,SAAS,IAAI,KAAK;cAAA;gBAAAsH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAa,CAAC,eACtGjJ,OAAA,CAACtD,UAAU;gBAAC4L,OAAO,EAAC,OAAO;gBAAAD,QAAA,gBAACrI,OAAA;kBAAAqI,QAAA,EAAQ;gBAAO;kBAAAS,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,KAAC,EAACtH,YAAY,CAAC6K,WAAW,IAAI,KAAK;cAAA;gBAAA1D,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAa,CAAC,eACrGjJ,OAAA,CAACtD,UAAU;gBAAC4L,OAAO,EAAC,OAAO;gBAAAD,QAAA,gBAACrI,OAAA;kBAAAqI,QAAA,EAAQ;gBAAc;kBAAAS,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,KAAC,EAACtH,YAAY,CAACmI,YAAY,IAAI,KAAK;cAAA;gBAAAhB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAa,CAAC,eAC7GjJ,OAAA,CAACtD,UAAU;gBAAC4L,OAAO,EAAC,OAAO;gBAAAD,QAAA,gBAACrI,OAAA;kBAAAqI,QAAA,EAAQ;gBAAQ;kBAAAS,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,KAAC,EAACtH,YAAY,CAACqI,OAAO,IAAI,KAAK;cAAA;gBAAAlB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAa,CAAC,eAClGjJ,OAAA,CAACtD,UAAU;gBAAC4L,OAAO,EAAC,OAAO;gBAAAD,QAAA,gBAACrI,OAAA;kBAAAqI,QAAA,EAAQ;gBAAG;kBAAAS,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,KAAC,EAACtH,YAAY,CAAC8K,EAAE,IAAI,KAAK;cAAA;gBAAA3D,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAa,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACrF,CAAC,eAENjJ,OAAA,CAACtD,UAAU;cAAC4L,OAAO,EAAC,WAAW;cAACgE,YAAY;cAAAjE,QAAA,EAAC;YAAQ;cAAAS,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAY,CAAC,eAClEjJ,OAAA,CAACvD,GAAG;cAACmM,EAAE,EAAE;gBAAE2D,EAAE,EAAE;cAAE,CAAE;cAAAlE,QAAA,gBACjBrI,OAAA,CAACtD,UAAU;gBAAC4L,OAAO,EAAC,OAAO;gBAAAD,QAAA,gBAACrI,OAAA;kBAAAqI,QAAA,EAAQ;gBAAW;kBAAAS,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,KAAC,EAACtH,YAAY,CAACuI,mBAAmB,IAAI,KAAK;cAAA;gBAAApB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAa,CAAC,eACjHjJ,OAAA,CAACtD,UAAU;gBAAC4L,OAAO,EAAC,OAAO;gBAAAD,QAAA,gBAACrI,OAAA;kBAAAqI,QAAA,EAAQ;gBAAO;kBAAAS,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,KAAC,EAACtH,YAAY,CAACiK,eAAe,IAAI,KAAK;cAAA;gBAAA9C,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAa,CAAC,eACzGjJ,OAAA,CAACtD,UAAU;gBAAC4L,OAAO,EAAC,OAAO;gBAAAD,QAAA,gBAACrI,OAAA;kBAAAqI,QAAA,EAAQ;gBAAY;kBAAAS,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,KAAC,EAACtH,YAAY,CAAC+K,2BAA2B,IAAI,KAAK;cAAA;gBAAA5D,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAa,CAAC,eAC1HjJ,OAAA,CAACtD,UAAU;gBAAC4L,OAAO,EAAC,OAAO;gBAAAD,QAAA,gBAACrI,OAAA;kBAAAqI,QAAA,EAAQ;gBAAa;kBAAAS,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,KAAC,EAACtH,YAAY,CAACgL,qBAAqB,IAAI,KAAK;cAAA;gBAAA7D,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAa,CAAC,eACrHjJ,OAAA,CAACtD,UAAU;gBAAC4L,OAAO,EAAC,OAAO;gBAAAD,QAAA,gBAACrI,OAAA;kBAAAqI,QAAA,EAAQ;gBAAQ;kBAAAS,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,KAAC,EAACtH,YAAY,CAACiL,gBAAgB,IAAI,KAAK;cAAA;gBAAA9D,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAa,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACxG,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACF,CAAC,eAEPjJ,OAAA,CAACnD,IAAI;YAACuI,IAAI;YAAC+F,EAAE,EAAE,EAAG;YAACE,EAAE,EAAE,CAAE;YAAAhD,QAAA,gBACvBrI,OAAA,CAACtD,UAAU;cAAC4L,OAAO,EAAC,WAAW;cAACgE,YAAY;cAAAjE,QAAA,EAAC;YAAM;cAAAS,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAY,CAAC,eAChEjJ,OAAA,CAACvD,GAAG;cAACmM,EAAE,EAAE;gBAAE2D,EAAE,EAAE;cAAE,CAAE;cAAAlE,QAAA,gBACjBrI,OAAA,CAACtD,UAAU;gBAAC4L,OAAO,EAAC,OAAO;gBAAAD,QAAA,gBAACrI,OAAA;kBAAAqI,QAAA,EAAQ;gBAAW;kBAAAS,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,KAAC,EAACtH,YAAY,CAACwI,iBAAiB,IAAI,KAAK;cAAA;gBAAArB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAa,CAAC,eAC/GjJ,OAAA,CAACtD,UAAU;gBAAC4L,OAAO,EAAC,OAAO;gBAAAD,QAAA,gBAACrI,OAAA;kBAAAqI,QAAA,EAAQ;gBAAO;kBAAAS,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,KAAC,EAACtH,YAAY,CAACkK,aAAa,IAAI,KAAK;cAAA;gBAAA/C,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAa,CAAC,eACvGjJ,OAAA,CAACtD,UAAU;gBAAC4L,OAAO,EAAC,OAAO;gBAAAD,QAAA,gBAACrI,OAAA;kBAAAqI,QAAA,EAAQ;gBAAY;kBAAAS,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,KAAC,EAACtH,YAAY,CAACkL,yBAAyB,IAAI,KAAK;cAAA;gBAAA/D,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAa,CAAC,eACxHjJ,OAAA,CAACtD,UAAU;gBAAC4L,OAAO,EAAC,OAAO;gBAAAD,QAAA,gBAACrI,OAAA;kBAAAqI,QAAA,EAAQ;gBAAa;kBAAAS,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,KAAC,EAACtH,YAAY,CAACmL,mBAAmB,IAAI,KAAK;cAAA;gBAAAhE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAa,CAAC,eACnHjJ,OAAA,CAACtD,UAAU;gBAAC4L,OAAO,EAAC,OAAO;gBAAAD,QAAA,gBAACrI,OAAA;kBAAAqI,QAAA,EAAQ;gBAAQ;kBAAAS,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,KAAC,EAACtH,YAAY,CAACoL,cAAc,IAAI,KAAK;cAAA;gBAAAjE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAa,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACtG,CAAC,eAENjJ,OAAA,CAACtD,UAAU;cAAC4L,OAAO,EAAC,WAAW;cAACgE,YAAY;cAAAjE,QAAA,EAAC;YAAa;cAAAS,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAY,CAAC,eACvEjJ,OAAA,CAACvD,GAAG;cAACmM,EAAE,EAAE;gBAAE2D,EAAE,EAAE;cAAE,CAAE;cAAAlE,QAAA,gBACjBrI,OAAA,CAACtD,UAAU;gBAAC4L,OAAO,EAAC,OAAO;gBAAAD,QAAA,gBAACrI,OAAA;kBAAAqI,QAAA,EAAQ;gBAAc;kBAAAS,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,KAAC,EAACtH,YAAY,CAACyI,aAAa,IAAI,KAAK;cAAA;gBAAAtB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAa,CAAC,eAC9GjJ,OAAA,CAACtD,UAAU;gBAAC4L,OAAO,EAAC,OAAO;gBAAAD,QAAA,gBAACrI,OAAA;kBAAAqI,QAAA,EAAQ;gBAAgB;kBAAAS,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,KAAC,EAACtH,YAAY,CAAC6I,eAAe,IAAI,GAAG;cAAA;gBAAA1B,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAa,CAAC,eAChHjJ,OAAA,CAACtD,UAAU;gBAAC4L,OAAO,EAAC,OAAO;gBAAAD,QAAA,gBAACrI,OAAA;kBAAAqI,QAAA,EAAQ;gBAAM;kBAAAS,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,KAAC,EAACnJ,2BAA2B,CAAC6B,YAAY,CAACJ,mBAAmB,CAAC;cAAA;gBAAAuH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAa,CAAC,eAChIjJ,OAAA,CAACtD,UAAU;gBAAC4L,OAAO,EAAC,OAAO;gBAAAD,QAAA,gBAACrI,OAAA;kBAAAqI,QAAA,EAAQ;gBAAa;kBAAAS,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,KAAC,EAACtH,YAAY,CAAC2H,YAAY,IAAI,GAAG;cAAA;gBAAAR,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAa,CAAC,eAC1GjJ,OAAA,CAACtD,UAAU;gBAAC4L,OAAO,EAAC,OAAO;gBAAAD,QAAA,gBAACrI,OAAA;kBAAAqI,QAAA,EAAQ;gBAAO;kBAAAS,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,KAAC,EAACtH,YAAY,CAAC+I,SAAS,IAAI,KAAK;cAAA;gBAAA5B,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAa,CAAC,eACnGjJ,OAAA,CAACtD,UAAU;gBAAC4L,OAAO,EAAC,OAAO;gBAAAD,QAAA,gBAACrI,OAAA;kBAAAqI,QAAA,EAAQ;gBAAkB;kBAAAS,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,KAAC,EAACtH,YAAY,CAACqL,iBAAiB,IAAI,KAAK;cAAA;gBAAAlE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAa,CAAC,eACtHjJ,OAAA,CAACtD,UAAU;gBAAC4L,OAAO,EAAC,OAAO;gBAAAD,QAAA,gBAACrI,OAAA;kBAAAqI,QAAA,EAAQ;gBAAa;kBAAAS,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,KAAC,EAACtH,YAAY,CAACsL,YAAY,IAAI,KAAK;cAAA;gBAAAnE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAa,CAAC,eAC5GjJ,OAAA,CAACtD,UAAU;gBAAC4L,OAAO,EAAC,OAAO;gBAAAD,QAAA,gBAACrI,OAAA;kBAAAqI,QAAA,EAAQ;gBAAqB;kBAAAS,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,KAAC,EAAC,IAAIiE,IAAI,CAACvL,YAAY,CAACwL,SAAS,CAAC,CAACC,cAAc,CAAC,CAAC;cAAA;gBAAAtE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAa,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAChI,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACF,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACM,CAAC,eAChBjJ,OAAA,CAAC1B,aAAa;QAAA+J,QAAA,eACZrI,OAAA,CAACpD,MAAM;UAAC4L,OAAO,EAAEP,kBAAmB;UAAAI,QAAA,EAAC;QAAM;UAAAS,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACvC,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACV,CAAC;EAEb,CAAC;;EAED;EACA,MAAMoE,iBAAiB,GAAGA,CAAA,KAAM;IAC9B,oBACErN,OAAA,CAACrD,KAAK;MAACiM,EAAE,EAAE;QAAE2D,EAAE,EAAE,CAAC;QAAEe,CAAC,EAAE,CAAC;QAAE7B,OAAO,EAAE/I,WAAW,GAAG,OAAO,GAAG,MAAM;QAAE6K,MAAM,EAAE;MAAoB,CAAE;MAAAlF,QAAA,gBAC/FrI,OAAA,CAACvD,GAAG;QAACmM,EAAE,EAAE;UAAE6C,OAAO,EAAE,MAAM;UAAEC,cAAc,EAAE,eAAe;UAAE8B,UAAU,EAAE,QAAQ;UAAEjB,EAAE,EAAE;QAAE,CAAE;QAAAlE,QAAA,gBACzFrI,OAAA,CAACtD,UAAU;UAAC4L,OAAO,EAAC,IAAI;UAAAD,QAAA,EAAC;QAAoB;UAAAS,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAY,CAAC,eAC1DjJ,OAAA,CAACpD,MAAM;UACL0L,OAAO,EAAC,UAAU;UAClBC,KAAK,EAAC,SAAS;UACfmB,IAAI,EAAC,OAAO;UACZlB,OAAO,EAAEV,YAAa;UACtB2F,SAAS,eAAEzN,OAAA,CAACN,SAAS;YAAAoJ,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAE;UAAAZ,QAAA,EAC1B;QAED;UAAAS,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACN,CAAC,eACNjJ,OAAA,CAAChC,OAAO;QAAC4K,EAAE,EAAE;UAAE2D,EAAE,EAAE;QAAE;MAAE;QAAAzD,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC,eAC1BjJ,OAAA,CAACnD,IAAI;QAACoO,SAAS;QAACC,OAAO,EAAE,CAAE;QAAA7C,QAAA,gBACzBrI,OAAA,CAACnD,IAAI;UAACuI,IAAI;UAAC+F,EAAE,EAAE,EAAG;UAACC,EAAE,EAAE,CAAE;UAACC,EAAE,EAAE,CAAE;UAAAhD,QAAA,eAC9BrI,OAAA,CAACnC,WAAW;YAACuO,SAAS;YAAC1C,IAAI,EAAC,OAAO;YAAArB,QAAA,gBACjCrI,OAAA,CAAClC,UAAU;cAAC4P,EAAE,EAAC,2BAA2B;cAAArF,QAAA,EAAC;YAAmB;cAAAS,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAY,CAAC,eAC3EjJ,OAAA,CAACpC,MAAM;cACL+P,OAAO,EAAC,2BAA2B;cACnCD,EAAE,EAAC,qBAAqB;cACxBhH,IAAI,EAAC,qBAAqB;cAC1BlE,KAAK,EAAEnB,OAAO,CAACE,mBAAoB;cACnCkB,KAAK,EAAC,qBAAqB;cAC3BmL,QAAQ,EAAElG,kBAAmB;cAAAW,QAAA,gBAE7BrI,OAAA,CAACrC,QAAQ;gBAAC6E,KAAK,EAAC,EAAE;gBAAA6F,QAAA,EAAC;cAAK;gBAAAS,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAU,CAAC,EAClC9G,kBAAkB,CAACiC,GAAG,CAACiB,KAAK,iBAC3BrF,OAAA,CAACrC,QAAQ;gBAAa6E,KAAK,EAAE6C,KAAM;gBAAAgD,QAAA,EAAEhD;cAAK,GAA3BA,KAAK;gBAAAyD,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAiC,CACtD,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACI,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACE;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACV,CAAC,eAEPjJ,OAAA,CAACnD,IAAI;UAACuI,IAAI;UAAC+F,EAAE,EAAE,EAAG;UAACC,EAAE,EAAE,CAAE;UAACC,EAAE,EAAE,CAAE;UAAAhD,QAAA,eAC9BrI,OAAA,CAACnC,WAAW;YAACuO,SAAS;YAAC1C,IAAI,EAAC,OAAO;YAAArB,QAAA,gBACjCrI,OAAA,CAAClC,UAAU;cAAC4P,EAAE,EAAC,iBAAiB;cAAArF,QAAA,EAAC;YAAS;cAAAS,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAY,CAAC,eACvDjJ,OAAA,CAACpC,MAAM;cACL+P,OAAO,EAAC,iBAAiB;cACzBD,EAAE,EAAC,WAAW;cACdhH,IAAI,EAAC,WAAW;cAChBlE,KAAK,EAAEnB,OAAO,CAACG,SAAU;cACzBiB,KAAK,EAAC,WAAW;cACjBmL,QAAQ,EAAElG,kBAAmB;cAAAW,QAAA,gBAE7BrI,OAAA,CAACrC,QAAQ;gBAAC6E,KAAK,EAAC,EAAE;gBAAA6F,QAAA,EAAC;cAAK;gBAAAS,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAU,CAAC,EAClC5G,aAAa,CAAC+B,GAAG,CAACoB,IAAI,iBACrBxF,OAAA,CAACrC,QAAQ;gBAAY6E,KAAK,EAAEgD,IAAK;gBAAA6C,QAAA,EAAE7C;cAAI,GAAxBA,IAAI;gBAAAsD,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAA+B,CACnD,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACI,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACE;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACV,CAAC,eAEPjJ,OAAA,CAACnD,IAAI;UAACuI,IAAI;UAAC+F,EAAE,EAAE,EAAG;UAACC,EAAE,EAAE,CAAE;UAACC,EAAE,EAAE,CAAE;UAAAhD,QAAA,eAC9BrI,OAAA,CAACnC,WAAW;YAACuO,SAAS;YAAC1C,IAAI,EAAC,OAAO;YAAArB,QAAA,gBACjCrI,OAAA,CAAClC,UAAU;cAAC4P,EAAE,EAAC,eAAe;cAAArF,QAAA,EAAC;YAAU;cAAAS,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAY,CAAC,eACtDjJ,OAAA,CAACpC,MAAM;cACL+P,OAAO,EAAC,eAAe;cACvBD,EAAE,EAAC,SAAS;cACZhH,IAAI,EAAC,SAAS;cACdlE,KAAK,EAAEnB,OAAO,CAACI,OAAQ;cACvBgB,KAAK,EAAC,YAAY;cAClBmL,QAAQ,EAAElG,kBAAmB;cAAAW,QAAA,gBAE7BrI,OAAA,CAACrC,QAAQ;gBAAC6E,KAAK,EAAC,EAAE;gBAAA6F,QAAA,EAAC;cAAW;gBAAAS,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAU,CAAC,EACxC1G,WAAW,CAAC6B,GAAG,CAACyJ,MAAM,iBACrB7N,OAAA,CAACrC,QAAQ;gBAAoB6E,KAAK,EAAEqL,MAAM,CAACrL,KAAM;gBAAA6F,QAAA,EAAEwF,MAAM,CAACpL;cAAK,GAAhDoL,MAAM,CAACrL,KAAK;gBAAAsG,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAA+C,CAC3E,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACI,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACE;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACV,CAAC,eAEPjJ,OAAA,CAACnD,IAAI;UAACuI,IAAI;UAAC+F,EAAE,EAAE,EAAG;UAACC,EAAE,EAAE,CAAE;UAACC,EAAE,EAAE,CAAE;UAAAhD,QAAA,eAC9BrI,OAAA,CAACnC,WAAW;YAACuO,SAAS;YAAC1C,IAAI,EAAC,OAAO;YAAArB,QAAA,gBACjCrI,OAAA,CAAClC,UAAU;cAAC4P,EAAE,EAAC,kBAAkB;cAAArF,QAAA,EAAC;YAAM;cAAAS,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAY,CAAC,eACrDjJ,OAAA,CAACpC,MAAM;cACL+P,OAAO,EAAC,kBAAkB;cAC1BD,EAAE,EAAC,YAAY;cACfhH,IAAI,EAAC,YAAY;cACjBlE,KAAK,EAAEnB,OAAO,CAACK,UAAW;cAC1Be,KAAK,EAAC,QAAQ;cACdmL,QAAQ,EAAElG,kBAAmB;cAC7BoG,QAAQ,EAAE,CAACzM,OAAO,CAACI,OAAQ;cAAA4G,QAAA,gBAE3BrI,OAAA,CAACrC,QAAQ;gBAAC6E,KAAK,EAAC,KAAK;gBAAA6F,QAAA,EAAC;cAAS;gBAAAS,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAU,CAAC,eAC1CjJ,OAAA,CAACrC,QAAQ;gBAAC6E,KAAK,EAAC,MAAM;gBAAA6F,QAAA,EAAC;cAAW;gBAAAS,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAU,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACvC,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACE;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACV,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAGF,CAAC;EAEZ,CAAC;;EAED;EACA,MAAM8E,gBAAgB,GAAGA,CAAA,KAAM;IAC7B,IAAI,CAAChM,KAAK,EAAE,OAAO,IAAI;IAEvB,oBACE/B,OAAA,CAACrD,KAAK;MAACiM,EAAE,EAAE;QAAE2D,EAAE,EAAE,CAAC;QAAEe,CAAC,EAAE;MAAE,CAAE;MAAAjF,QAAA,gBACzBrI,OAAA,CAACtD,UAAU;QAAC4L,OAAO,EAAC,IAAI;QAACgE,YAAY;QAAAjE,QAAA,EAAC;MAAW;QAAAS,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAY,CAAC,EAC7DhH,YAAY,gBACXjC,OAAA,CAAC9B,cAAc;QAAA4K,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC,gBAElBjJ,OAAA,CAACnD,IAAI;QAACoO,SAAS;QAACC,OAAO,EAAE,CAAE;QAAA7C,QAAA,gBACzBrI,OAAA,CAACnD,IAAI;UAACuI,IAAI;UAAC+F,EAAE,EAAE,EAAG;UAACE,EAAE,EAAE,CAAE;UAAAhD,QAAA,gBACvBrI,OAAA,CAACtD,UAAU;YAAC4L,OAAO,EAAC,WAAW;YAACgE,YAAY;YAAAjE,QAAA,EAAC;UAAM;YAAAS,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAY,CAAC,eAChEjJ,OAAA,CAACtD,UAAU;YAAC4L,OAAO,EAAC,OAAO;YAAAD,QAAA,GAAC,eAAa,EAACtG,KAAK,CAACiM,MAAM,CAACC,WAAW;UAAA;YAAAnF,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAa,CAAC,eAChFjJ,OAAA,CAACtD,UAAU;YAAC4L,OAAO,EAAC,OAAO;YAAAD,QAAA,GAAC,cAAY,EAACtG,KAAK,CAACiM,MAAM,CAACE,UAAU;UAAA;YAAApF,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAa,CAAC,eAC9EjJ,OAAA,CAACtD,UAAU;YAAC4L,OAAO,EAAC,OAAO;YAAAD,QAAA,GAAC,eAAa,EAACtG,KAAK,CAACiM,MAAM,CAACG,WAAW;UAAA;YAAArF,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAa,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC5E,CAAC,eAEPjJ,OAAA,CAACnD,IAAI;UAACuI,IAAI;UAAC+F,EAAE,EAAE,EAAG;UAACE,EAAE,EAAE,CAAE;UAAAhD,QAAA,gBACvBrI,OAAA,CAACtD,UAAU;YAAC4L,OAAO,EAAC,WAAW;YAACgE,YAAY;YAAAjE,QAAA,EAAC;UAAS;YAAAS,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAY,CAAC,eACnEjJ,OAAA,CAACtD,UAAU;YAAC4L,OAAO,EAAC,OAAO;YAAAD,QAAA,GAAC,iBAAe,EAACtG,KAAK,CAACqM,SAAS,CAACC,oBAAoB,CAAC/D,OAAO,CAAC,CAAC,CAAC;UAAA;YAAAxB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAa,CAAC,eACzGjJ,OAAA,CAACtD,UAAU;YAAC4L,OAAO,EAAC,OAAO;YAAAD,QAAA,GAAC,gBAAc,EAACtG,KAAK,CAACqM,SAAS,CAACE,kBAAkB,CAAChE,OAAO,CAAC,CAAC,CAAC;UAAA;YAAAxB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAa,CAAC,eACtGjJ,OAAA,CAACvD,GAAG;YAACmM,EAAE,EAAE;cAAE6C,OAAO,EAAE,MAAM;cAAE+B,UAAU,EAAE,QAAQ;cAAEhE,EAAE,EAAE;YAAE,CAAE;YAAAnB,QAAA,gBACxDrI,OAAA,CAACvD,GAAG;cAACmM,EAAE,EAAE;gBAAE2F,KAAK,EAAE,MAAM;gBAAEC,EAAE,EAAE;cAAE,CAAE;cAAAnG,QAAA,eAChCrI,OAAA,CAAC9B,cAAc;gBACboK,OAAO,EAAC,aAAa;gBACrB9F,KAAK,EAAET,KAAK,CAACqM,SAAS,CAACK,yBAA0B;gBACjD7F,EAAE,EAAE;kBAAE8F,MAAM,EAAE,EAAE;kBAAEC,YAAY,EAAE;gBAAE;cAAE;gBAAA7F,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACrC;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACC,CAAC,eACNjJ,OAAA,CAACvD,GAAG;cAACmM,EAAE,EAAE;gBAAEgG,QAAQ,EAAE;cAAG,CAAE;cAAAvG,QAAA,eACxBrI,OAAA,CAACtD,UAAU;gBAAC4L,OAAO,EAAC,OAAO;gBAACC,KAAK,EAAC,gBAAgB;gBAAAF,QAAA,EAAE,GAAGtG,KAAK,CAACqM,SAAS,CAACK,yBAAyB,CAACnE,OAAO,CAAC,CAAC,CAAC;cAAG;gBAAAxB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAa;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACzH,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACF,CAAC,eAEPjJ,OAAA,CAACnD,IAAI;UAACuI,IAAI;UAAC+F,EAAE,EAAE,EAAG;UAACE,EAAE,EAAE,CAAE;UAAAhD,QAAA,gBACvBrI,OAAA,CAACtD,UAAU;YAAC4L,OAAO,EAAC,WAAW;YAACgE,YAAY;YAAAjE,QAAA,EAAC;UAAK;YAAAS,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAY,CAAC,eAC/DjJ,OAAA,CAACvD,GAAG;YAACmM,EAAE,EAAE;cAAE6C,OAAO,EAAE,MAAM;cAAEoD,QAAQ,EAAE,MAAM;cAAEC,GAAG,EAAE;YAAE,CAAE;YAAAzG,QAAA,EACpDtG,KAAK,CAACoD,KAAK,CAACf,GAAG,CAAC,CAACiB,KAAK,EAAE0J,KAAK,kBAC5B/O,OAAA,CAACjC,IAAI;cAEH0E,KAAK,EAAE,GAAG4C,KAAK,CAACA,KAAK,KAAKA,KAAK,CAAC2J,KAAK,EAAG;cACxCtF,IAAI,EAAC,OAAO;cACZlB,OAAO,EAAEA,CAAA,KAAM;gBACblH,UAAU,CAAC2N,IAAI,KAAK;kBAClB,GAAGA,IAAI;kBACP1N,mBAAmB,EAAE8D,KAAK,CAACA,KAAK,KAAK,iBAAiB,GAAG,EAAE,GAAGA,KAAK,CAACA;gBACtE,CAAC,CAAC,CAAC;cACL;YAAE,GARG0J,KAAK;cAAAjG,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OASX,CACF;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACC,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACF,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CACP;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACI,CAAC;EAEZ,CAAC;EAED,oBACEjJ,OAAA,CAACvD,GAAG;IAACyS,SAAS,EAAC,WAAW;IAAA7G,QAAA,gBACxBrI,OAAA,CAACrD,KAAK;MAACiM,EAAE,EAAE;QAAE2D,EAAE,EAAE,CAAC;QAAEe,CAAC,EAAE;MAAE,CAAE;MAAAjF,QAAA,eACzBrI,OAAA,CAACvD,GAAG;QAACmM,EAAE,EAAE;UAAE6C,OAAO,EAAE,MAAM;UAAEC,cAAc,EAAE,eAAe;UAAE8B,UAAU,EAAE;QAAS,CAAE;QAAAnF,QAAA,gBAClFrI,OAAA,CAACtD,UAAU;UAAC4L,OAAO,EAAC,IAAI;UAAAD,QAAA,GAAC,yBACA,EAAC5H,YAAY;QAAA;UAAAqI,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC1B,CAAC,eAEbjJ,OAAA,CAACvD,GAAG;UAACmM,EAAE,EAAE;YAAE6C,OAAO,EAAE,MAAM;YAAE+B,UAAU,EAAE,QAAQ;YAAEsB,GAAG,EAAE;UAAE,CAAE;UAAAzG,QAAA,gBACzDrI,OAAA,CAAC/C,UAAU;YACTuL,OAAO,EAAEA,CAAA,KAAM7F,cAAc,CAAC,CAACD,WAAW,CAAE;YAC5CqI,KAAK,EAAErI,WAAW,GAAG,iBAAiB,GAAG,eAAgB;YACzD6F,KAAK,EAAE7F,WAAW,IAAIrB,OAAO,CAACE,mBAAmB,IAAIF,OAAO,CAACG,SAAS,IAAIH,OAAO,CAACI,OAAO,GAAG,SAAS,GAAG,SAAU;YAClHmH,EAAE,EAAE;cACFuG,QAAQ,EAAE,UAAU;cACpB,UAAU,EAAG9N,OAAO,CAACE,mBAAmB,IAAIF,OAAO,CAACG,SAAS,IAAIH,OAAO,CAACI,OAAO,GAAI;gBAClF2N,OAAO,EAAE,IAAI;gBACbD,QAAQ,EAAE,UAAU;gBACpBE,GAAG,EAAE,CAAC;gBACNC,KAAK,EAAE,CAAC;gBACRf,KAAK,EAAE,CAAC;gBACRG,MAAM,EAAE,CAAC;gBACTC,YAAY,EAAE,KAAK;gBACnB/D,eAAe,EAAE;cACnB,CAAC,GAAG,CAAC;YACP,CAAE;YAAAvC,QAAA,eAEFrI,OAAA,CAACd,cAAc;cAAA4J,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACR,CAAC,eAEbjJ,OAAA,CAAC/C,UAAU;YACTuL,OAAO,EAAEA,CAAA,KAAMC,MAAM,CAACC,QAAQ,CAACC,MAAM,CAAC,CAAE;YACxCoC,KAAK,EAAC,oBAAoB;YAAA1C,QAAA,eAE1BrI,OAAA,CAACtB,WAAW;cAAAoK,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACL,CAAC,eAEbjJ,OAAA,CAACvD,GAAG;YAACmM,EAAE,EAAE;cAAE6C,OAAO,EAAE,MAAM;cAAE8B,MAAM,EAAE,gBAAgB;cAAEoB,YAAY,EAAE;YAAE,CAAE;YAAAtG,QAAA,gBACtErI,OAAA,CAAC/C,UAAU;cACT2L,EAAE,EAAE;gBAAEL,KAAK,EAAEpH,QAAQ,KAAK,OAAO,GAAG,SAAS,GAAG;cAAU,CAAE;cAC5DqH,OAAO,EAAEA,CAAA,KAAMsD,oBAAoB,CAAC,OAAO,CAAE;cAC7Cf,KAAK,EAAC,iBAAiB;cAAA1C,QAAA,eAEvBrI,OAAA,CAAClB,YAAY;gBAAAgK,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACN,CAAC,eACbjJ,OAAA,CAAC/C,UAAU;cACT2L,EAAE,EAAE;gBAAEL,KAAK,EAAEpH,QAAQ,KAAK,MAAM,GAAG,SAAS,GAAG;cAAU,CAAE;cAC3DqH,OAAO,EAAEA,CAAA,KAAMsD,oBAAoB,CAAC,MAAM,CAAE;cAC5Cf,KAAK,EAAC,gBAAgB;cAAA1C,QAAA,eAEtBrI,OAAA,CAAChB,cAAc;gBAAA8J,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACR,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACV,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACD,CAAC,EAGPoE,iBAAiB,CAAC,CAAC,EAGnBU,gBAAgB,CAAC,CAAC,EAElBhN,OAAO,gBACNf,OAAA,CAACvD,GAAG;MAACmM,EAAE,EAAE;QAAE6C,OAAO,EAAE,MAAM;QAAE8D,aAAa,EAAE,QAAQ;QAAE/B,UAAU,EAAE,QAAQ;QAAEhE,EAAE,EAAE;MAAE,CAAE;MAAAnB,QAAA,gBACjFrI,OAAA,CAAC/B,gBAAgB;QAACyL,IAAI,EAAE;MAAG;QAAAZ,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC,eAC9BjJ,OAAA,CAACtD,UAAU;QAACkM,EAAE,EAAE;UAAEY,EAAE,EAAE;QAAE,CAAE;QAAAnB,QAAA,EAAC;MAAmB;QAAAS,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAY,CAAC,eAC3DjJ,OAAA,CAACpD,MAAM;QACL0L,OAAO,EAAC,UAAU;QAClBC,KAAK,EAAC,SAAS;QACfC,OAAO,EAAEA,CAAA,KAAMC,MAAM,CAACC,QAAQ,CAACC,MAAM,CAAC,CAAE;QACxCC,EAAE,EAAE;UAAEY,EAAE,EAAE;QAAE,CAAE;QAAAnB,QAAA,EACf;MAED;QAAAS,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAQ,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACN,CAAC,GACJhI,KAAK,gBACPjB,OAAA,CAACvD,GAAG;MAAA4L,QAAA,gBACFrI,OAAA,CAAChD,KAAK;QAACoL,QAAQ,EAAC,OAAO;QAACQ,EAAE,EAAE;UAAE2D,EAAE,EAAE;QAAE,CAAE;QAAAlE,QAAA,GACnCpH,KAAK,EACLA,KAAK,CAACuG,QAAQ,CAAC,eAAe,CAAC,iBAC9BxH,OAAA,CAACtD,UAAU;UAAC4L,OAAO,EAAC,OAAO;UAACM,EAAE,EAAE;YAAEY,EAAE,EAAE;UAAE,CAAE;UAAAnB,QAAA,gBACxCrI,OAAA;YAAAqI,QAAA,EAAQ;UAAa;YAAAS,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC,uEAC9B,eAAAjJ,OAAA;YAAA8I,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAK,CAAC,8CACoC,eAAAjJ,OAAA;YAAAqI,QAAA,EAAM;UAAa;YAAAS,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC,4CACtE;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAY,CACb;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACI,CAAC,eACRjJ,OAAA,CAACvD,GAAG;QAACmM,EAAE,EAAE;UAAE6C,OAAO,EAAE,MAAM;UAAEqD,GAAG,EAAE;QAAE,CAAE;QAAAzG,QAAA,eACnCrI,OAAA,CAACpD,MAAM;UACL0L,OAAO,EAAC,WAAW;UACnB4G,SAAS,EAAC,gBAAgB;UAC1B1G,OAAO,EAAEA,CAAA,KAAMC,MAAM,CAACC,QAAQ,CAACC,MAAM,CAAC,CAAE;UAAAN,QAAA,EACzC;QAED;UAAAS,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACN,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,gBAENjJ,OAAA,CAACvD,GAAG;MAAA4L,QAAA,GAED,CAAChH,OAAO,CAACE,mBAAmB,IAAIF,OAAO,CAACG,SAAS,IAAIH,OAAO,CAACI,OAAO,kBACnEzB,OAAA,CAACvD,GAAG;QAACmM,EAAE,EAAE;UAAE2D,EAAE,EAAE,CAAC;UAAEd,OAAO,EAAE,MAAM;UAAEoD,QAAQ,EAAE,MAAM;UAAEC,GAAG,EAAE,CAAC;UAAEtB,UAAU,EAAE;QAAS,CAAE;QAAAnF,QAAA,gBAClFrI,OAAA,CAACtD,UAAU;UAAC4L,OAAO,EAAC,OAAO;UAACM,EAAE,EAAE;YAAE4F,EAAE,EAAE;UAAE,CAAE;UAAAnG,QAAA,EAAC;QAAc;UAAAS,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAY,CAAC,EACrE5H,OAAO,CAACE,mBAAmB,iBAC1BvB,OAAA,CAACjC,IAAI;UACH0E,KAAK,EAAE,UAAUpB,OAAO,CAACE,mBAAmB,EAAG;UAC/CmI,IAAI,EAAC,OAAO;UACZ8F,QAAQ,EAAEA,CAAA,KAAMlO,UAAU,CAAC2N,IAAI,KAAK;YAAE,GAAGA,IAAI;YAAE1N,mBAAmB,EAAE;UAAG,CAAC,CAAC;QAAE;UAAAuH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC5E,CACF,EACA5H,OAAO,CAACG,SAAS,iBAChBxB,OAAA,CAACjC,IAAI;UACH0E,KAAK,EAAE,cAAcpB,OAAO,CAACG,SAAS,EAAG;UACzCkI,IAAI,EAAC,OAAO;UACZ8F,QAAQ,EAAEA,CAAA,KAAMlO,UAAU,CAAC2N,IAAI,KAAK;YAAE,GAAGA,IAAI;YAAEzN,SAAS,EAAE;UAAG,CAAC,CAAC;QAAE;UAAAsH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAClE,CACF,EACA5H,OAAO,CAACI,OAAO,iBACdzB,OAAA,CAACjC,IAAI;UACH0E,KAAK,EAAE,gBAAgB,EAAAtC,iBAAA,GAAAoC,WAAW,CAACkN,IAAI,CAACC,GAAG,IAAIA,GAAG,CAAClN,KAAK,KAAKnB,OAAO,CAACI,OAAO,CAAC,cAAAtB,iBAAA,uBAAtDA,iBAAA,CAAwDsC,KAAK,KAAIpB,OAAO,CAACI,OAAO,KAAKJ,OAAO,CAACK,UAAU,KAAK,KAAK,GAAG,WAAW,GAAG,aAAa,GAAI;UAC1KgI,IAAI,EAAC,OAAO;UACZ8F,QAAQ,EAAEA,CAAA,KAAMlO,UAAU,CAAC2N,IAAI,KAAK;YAAE,GAAGA,IAAI;YAAExN,OAAO,EAAE;UAAG,CAAC,CAAC;QAAE;UAAAqH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAChE,CACF,EACA,CAAC5H,OAAO,CAACE,mBAAmB,IAAIF,OAAO,CAACG,SAAS,IAAIH,OAAO,CAACI,OAAO,kBACnEzB,OAAA,CAACpD,MAAM;UACL0L,OAAO,EAAC,MAAM;UACdoB,IAAI,EAAC,OAAO;UACZ+D,SAAS,eAAEzN,OAAA,CAACN,SAAS;YAAAoJ,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAE;UACzBT,OAAO,EAAEV,YAAa;UAAAO,QAAA,EACvB;QAED;UAAAS,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CACT;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACE,CACN,eAEDjJ,OAAA,CAACvD,GAAG;QAACmM,EAAE,EAAE;UAAE2D,EAAE,EAAE;QAAE,CAAE;QAAAlE,QAAA,gBACjBrI,OAAA,CAACtD,UAAU;UAAC4L,OAAO,EAAC,IAAI;UAACgE,YAAY;UAAAjE,QAAA,EAAC;QAEtC;UAAAS,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAY,CAAC,EACZf,eAAe,CAACvH,UAAU,CAAC;MAAA;QAAAmI,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACzB,CAAC,eAENjJ,OAAA,CAACvD,GAAG;QAACmM,EAAE,EAAE;UAAEY,EAAE,EAAE;QAAE,CAAE;QAAAnB,QAAA,gBACjBrI,OAAA,CAACtD,UAAU;UAAC4L,OAAO,EAAC,IAAI;UAACgE,YAAY;UAAAjE,QAAA,EAAC;QAEtC;UAAAS,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAY,CAAC,EACZf,eAAe,CAACrH,SAAS,CAAC;MAAA;QAAAiI,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACxB,CAAC,EAGL+C,mBAAmB,CAAC,CAAC;IAAA;MAAAlD,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACnB,CACN;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACE,CAAC;AAEV,CAAC;AAAC/I,EAAA,CAn9BID,kBAAkB;EAAA,QACYL,OAAO,EACxBD,WAAW;AAAA;AAAAgQ,EAAA,GAFxB1P,kBAAkB;AAq9BxB,eAAeA,kBAAkB;AAAC,IAAA0P,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}