{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\PycharmProjects\\\\CMS\\\\webapp\\\\frontend\\\\src\\\\components\\\\cavi\\\\ConfigurazioneDialog.js\",\n  _s = $RefreshSig$();\nimport React, { useState } from 'react';\nimport { Dialog, DialogTitle, DialogContent, DialogActions, Button, Typography, FormControl, RadioGroup, FormControlLabel, Radio, Box } from '@mui/material';\n\n/**\n * Dialog per la configurazione della numerazione delle bobine.\n * Mostrato solo per il primo inserimento di una bobina in un cantiere.\n *\n * @param {Object} props - Proprietà del componente\n * @param {boolean} props.open - Indica se il dialog è aperto\n * @param {Function} props.onClose - Funzione chiamata alla chiusura del dialog\n * @param {Function} props.onConfirm - Funzione chiamata alla conferma della configurazione\n */\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst ConfigurazioneDialog = ({\n  open,\n  onClose,\n  onConfirm\n}) => {\n  _s();\n  const [configValue, setConfigValue] = useState('s');\n  const handleConfirm = () => {\n    onConfirm(configValue);\n  };\n  return /*#__PURE__*/_jsxDEV(Dialog, {\n    open: open,\n    onClose: onClose,\n    maxWidth: \"sm\",\n    fullWidth: true,\n    children: [/*#__PURE__*/_jsxDEV(DialogTitle, {\n      children: \"Configurazione Numerazione Bobine\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 34,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(DialogContent, {\n      children: [/*#__PURE__*/_jsxDEV(Box, {\n        sx: {\n          mb: 2\n        },\n        children: [/*#__PURE__*/_jsxDEV(Typography, {\n          variant: \"body1\",\n          gutterBottom: true,\n          children: \"Questa \\xE8 la prima bobina per questo cantiere.\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 37,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Typography, {\n          variant: \"body1\",\n          gutterBottom: true,\n          children: \"Scegli come vuoi gestire la numerazione delle bobine:\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 40,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 36,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(FormControl, {\n        component: \"fieldset\",\n        sx: {\n          mt: 2\n        },\n        children: /*#__PURE__*/_jsxDEV(RadioGroup, {\n          value: configValue,\n          onChange: e => setConfigValue(e.target.value),\n          children: [/*#__PURE__*/_jsxDEV(FormControlLabel, {\n            value: \"s\",\n            control: /*#__PURE__*/_jsxDEV(Radio, {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 51,\n              columnNumber: 24\n            }, this),\n            label: \"Usa numeri progressivi (es. 1, 2, 3, ...)\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 49,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(FormControlLabel, {\n            value: \"n\",\n            control: /*#__PURE__*/_jsxDEV(Radio, {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 56,\n              columnNumber: 24\n            }, this),\n            label: \"Inserisci manualmente l'ID della bobina (es. A123, SPEC01, ecc.)\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 54,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 45,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 44,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Box, {\n        sx: {\n          mt: 2\n        },\n        children: /*#__PURE__*/_jsxDEV(Typography, {\n          variant: \"body2\",\n          color: \"text.secondary\",\n          children: \"Nota: Questa configurazione sar\\xE0 utilizzata per tutte le bobine di questo cantiere.\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 62,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 61,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 35,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(DialogActions, {\n      children: [/*#__PURE__*/_jsxDEV(Button, {\n        onClick: onClose,\n        children: \"Annulla\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 68,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Button, {\n        onClick: handleConfirm,\n        variant: \"contained\",\n        color: \"primary\",\n        children: \"Conferma\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 69,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 67,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 33,\n    columnNumber: 5\n  }, this);\n};\n_s(ConfigurazioneDialog, \"ZA2q/i+Z3WSCPTv0o/aYIKLYcT0=\");\n_c = ConfigurazioneDialog;\nexport default ConfigurazioneDialog;\nvar _c;\n$RefreshReg$(_c, \"ConfigurazioneDialog\");", "map": {"version": 3, "names": ["React", "useState", "Dialog", "DialogTitle", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "DialogActions", "<PERSON><PERSON>", "Typography", "FormControl", "RadioGroup", "FormControlLabel", "Radio", "Box", "jsxDEV", "_jsxDEV", "ConfigurazioneDialog", "open", "onClose", "onConfirm", "_s", "config<PERSON><PERSON><PERSON>", "setConfigValue", "handleConfirm", "max<PERSON><PERSON><PERSON>", "fullWidth", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "sx", "mb", "variant", "gutterBottom", "component", "mt", "value", "onChange", "e", "target", "control", "label", "color", "onClick", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/PycharmProjects/CMS/webapp/frontend/src/components/cavi/ConfigurazioneDialog.js"], "sourcesContent": ["import React, { useState } from 'react';\nimport {\n  Dialog,\n  DialogTitle,\n  DialogContent,\n  DialogActions,\n  Button,\n  Typography,\n  FormControl,\n  RadioGroup,\n  FormControlLabel,\n  Radio,\n  Box\n} from '@mui/material';\n\n/**\n * Dialog per la configurazione della numerazione delle bobine.\n * Mostrato solo per il primo inserimento di una bobina in un cantiere.\n *\n * @param {Object} props - Proprietà del componente\n * @param {boolean} props.open - Indica se il dialog è aperto\n * @param {Function} props.onClose - Funzione chiamata alla chiusura del dialog\n * @param {Function} props.onConfirm - Funzione chiamata alla conferma della configurazione\n */\nconst ConfigurazioneDialog = ({ open, onClose, onConfirm }) => {\n  const [configValue, setConfigValue] = useState('s');\n\n  const handleConfirm = () => {\n    onConfirm(configValue);\n  };\n\n  return (\n    <Dialog open={open} onClose={onClose} maxWidth=\"sm\" fullWidth>\n      <DialogTitle>Configurazione Numerazione Bobine</DialogTitle>\n      <DialogContent>\n        <Box sx={{ mb: 2 }}>\n          <Typography variant=\"body1\" gutterBottom>\n            Questa è la prima bobina per questo cantiere.\n          </Typography>\n          <Typography variant=\"body1\" gutterBottom>\n            Scegli come vuoi gestire la numerazione delle bobine:\n          </Typography>\n        </Box>\n        <FormControl component=\"fieldset\" sx={{ mt: 2 }}>\n          <RadioGroup\n            value={configValue}\n            onChange={(e) => setConfigValue(e.target.value)}\n          >\n            <FormControlLabel\n              value=\"s\"\n              control={<Radio />}\n              label=\"Usa numeri progressivi (es. 1, 2, 3, ...)\"\n            />\n            <FormControlLabel\n              value=\"n\"\n              control={<Radio />}\n              label=\"Inserisci manualmente l'ID della bobina (es. A123, SPEC01, ecc.)\"\n            />\n          </RadioGroup>\n        </FormControl>\n        <Box sx={{ mt: 2 }}>\n          <Typography variant=\"body2\" color=\"text.secondary\">\n            Nota: Questa configurazione sarà utilizzata per tutte le bobine di questo cantiere.\n          </Typography>\n        </Box>\n      </DialogContent>\n      <DialogActions>\n        <Button onClick={onClose}>Annulla</Button>\n        <Button onClick={handleConfirm} variant=\"contained\" color=\"primary\">\n          Conferma\n        </Button>\n      </DialogActions>\n    </Dialog>\n  );\n};\n\nexport default ConfigurazioneDialog;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,QAAQ,OAAO;AACvC,SACEC,MAAM,EACNC,WAAW,EACXC,aAAa,EACbC,aAAa,EACbC,MAAM,EACNC,UAAU,EACVC,WAAW,EACXC,UAAU,EACVC,gBAAgB,EAChBC,KAAK,EACLC,GAAG,QACE,eAAe;;AAEtB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AARA,SAAAC,MAAA,IAAAC,OAAA;AASA,MAAMC,oBAAoB,GAAGA,CAAC;EAAEC,IAAI;EAAEC,OAAO;EAAEC;AAAU,CAAC,KAAK;EAAAC,EAAA;EAC7D,MAAM,CAACC,WAAW,EAAEC,cAAc,CAAC,GAAGpB,QAAQ,CAAC,GAAG,CAAC;EAEnD,MAAMqB,aAAa,GAAGA,CAAA,KAAM;IAC1BJ,SAAS,CAACE,WAAW,CAAC;EACxB,CAAC;EAED,oBACEN,OAAA,CAACZ,MAAM;IAACc,IAAI,EAAEA,IAAK;IAACC,OAAO,EAAEA,OAAQ;IAACM,QAAQ,EAAC,IAAI;IAACC,SAAS;IAAAC,QAAA,gBAC3DX,OAAA,CAACX,WAAW;MAAAsB,QAAA,EAAC;IAAiC;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAa,CAAC,eAC5Df,OAAA,CAACV,aAAa;MAAAqB,QAAA,gBACZX,OAAA,CAACF,GAAG;QAACkB,EAAE,EAAE;UAAEC,EAAE,EAAE;QAAE,CAAE;QAAAN,QAAA,gBACjBX,OAAA,CAACP,UAAU;UAACyB,OAAO,EAAC,OAAO;UAACC,YAAY;UAAAR,QAAA,EAAC;QAEzC;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAY,CAAC,eACbf,OAAA,CAACP,UAAU;UAACyB,OAAO,EAAC,OAAO;UAACC,YAAY;UAAAR,QAAA,EAAC;QAEzC;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAY,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACV,CAAC,eACNf,OAAA,CAACN,WAAW;QAAC0B,SAAS,EAAC,UAAU;QAACJ,EAAE,EAAE;UAAEK,EAAE,EAAE;QAAE,CAAE;QAAAV,QAAA,eAC9CX,OAAA,CAACL,UAAU;UACT2B,KAAK,EAAEhB,WAAY;UACnBiB,QAAQ,EAAGC,CAAC,IAAKjB,cAAc,CAACiB,CAAC,CAACC,MAAM,CAACH,KAAK,CAAE;UAAAX,QAAA,gBAEhDX,OAAA,CAACJ,gBAAgB;YACf0B,KAAK,EAAC,GAAG;YACTI,OAAO,eAAE1B,OAAA,CAACH,KAAK;cAAAe,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAE;YACnBY,KAAK,EAAC;UAA2C;YAAAf,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAClD,CAAC,eACFf,OAAA,CAACJ,gBAAgB;YACf0B,KAAK,EAAC,GAAG;YACTI,OAAO,eAAE1B,OAAA,CAACH,KAAK;cAAAe,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAE;YACnBY,KAAK,EAAC;UAAkE;YAAAf,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACzE,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACQ;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACF,CAAC,eACdf,OAAA,CAACF,GAAG;QAACkB,EAAE,EAAE;UAAEK,EAAE,EAAE;QAAE,CAAE;QAAAV,QAAA,eACjBX,OAAA,CAACP,UAAU;UAACyB,OAAO,EAAC,OAAO;UAACU,KAAK,EAAC,gBAAgB;UAAAjB,QAAA,EAAC;QAEnD;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAY;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACV,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACO,CAAC,eAChBf,OAAA,CAACT,aAAa;MAAAoB,QAAA,gBACZX,OAAA,CAACR,MAAM;QAACqC,OAAO,EAAE1B,OAAQ;QAAAQ,QAAA,EAAC;MAAO;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAQ,CAAC,eAC1Cf,OAAA,CAACR,MAAM;QAACqC,OAAO,EAAErB,aAAc;QAACU,OAAO,EAAC,WAAW;QAACU,KAAK,EAAC,SAAS;QAAAjB,QAAA,EAAC;MAEpE;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAQ,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACI,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACV,CAAC;AAEb,CAAC;AAACV,EAAA,CAlDIJ,oBAAoB;AAAA6B,EAAA,GAApB7B,oBAAoB;AAoD1B,eAAeA,oBAAoB;AAAC,IAAA6B,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}