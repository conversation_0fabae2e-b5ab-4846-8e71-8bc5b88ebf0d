{"ast": null, "code": "import { formatDistance } from \"./cy/_lib/formatDistance.mjs\";\nimport { formatLong } from \"./cy/_lib/formatLong.mjs\";\nimport { formatRelative } from \"./cy/_lib/formatRelative.mjs\";\nimport { localize } from \"./cy/_lib/localize.mjs\";\nimport { match } from \"./cy/_lib/match.mjs\";\n\n/**\n * @category Locales\n * @summary Welsh locale.\n * @language Welsh\n * @iso-639-2 cym\n * <AUTHOR> [@elmomalmo](https://github.com/elmomalmo)\n */\nexport const cy = {\n  code: \"cy\",\n  formatDistance: formatDistance,\n  formatLong: formatLong,\n  formatRelative: formatRelative,\n  localize: localize,\n  match: match,\n  options: {\n    weekStartsOn: 0 /* Sunday */,\n    firstWeekContainsDate: 1\n  }\n};\n\n// Fallback for modularized imports:\nexport default cy;", "map": {"version": 3, "names": ["formatDistance", "formatLong", "formatRelative", "localize", "match", "cy", "code", "options", "weekStartsOn", "firstWeekContainsDate"], "sources": ["C:/CMS/webapp/frontend/node_modules/date-fns/locale/cy.mjs"], "sourcesContent": ["import { formatDistance } from \"./cy/_lib/formatDistance.mjs\";\nimport { formatLong } from \"./cy/_lib/formatLong.mjs\";\nimport { formatRelative } from \"./cy/_lib/formatRelative.mjs\";\nimport { localize } from \"./cy/_lib/localize.mjs\";\nimport { match } from \"./cy/_lib/match.mjs\";\n\n/**\n * @category Locales\n * @summary Welsh locale.\n * @language Welsh\n * @iso-639-2 cym\n * <AUTHOR> [@elmomalmo](https://github.com/elmomalmo)\n */\nexport const cy = {\n  code: \"cy\",\n  formatDistance: formatDistance,\n  formatLong: formatLong,\n  formatRelative: formatRelative,\n  localize: localize,\n  match: match,\n  options: {\n    weekStartsOn: 0 /* Sunday */,\n    firstWeekContainsDate: 1,\n  },\n};\n\n// Fallback for modularized imports:\nexport default cy;\n"], "mappings": "AAAA,SAASA,cAAc,QAAQ,8BAA8B;AAC7D,SAASC,UAAU,QAAQ,0BAA0B;AACrD,SAASC,cAAc,QAAQ,8BAA8B;AAC7D,SAASC,QAAQ,QAAQ,wBAAwB;AACjD,SAASC,KAAK,QAAQ,qBAAqB;;AAE3C;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO,MAAMC,EAAE,GAAG;EAChBC,IAAI,EAAE,IAAI;EACVN,cAAc,EAAEA,cAAc;EAC9BC,UAAU,EAAEA,UAAU;EACtBC,cAAc,EAAEA,cAAc;EAC9BC,QAAQ,EAAEA,QAAQ;EAClBC,KAAK,EAAEA,KAAK;EACZG,OAAO,EAAE;IACPC,YAAY,EAAE,CAAC,CAAC;IAChBC,qBAAqB,EAAE;EACzB;AACF,CAAC;;AAED;AACA,eAAeJ,EAAE", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}