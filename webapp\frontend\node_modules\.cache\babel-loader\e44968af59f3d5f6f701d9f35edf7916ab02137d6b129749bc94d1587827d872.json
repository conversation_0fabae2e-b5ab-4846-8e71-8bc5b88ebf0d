{"ast": null, "code": "var translations = {\n  xseconds_other: 'sekundė_sekundžių_sekundes',\n  xminutes_one: 'minutė_minutės_minutę',\n  xminutes_other: 'minutės_minučių_minutes',\n  xhours_one: 'valanda_valandos_valandą',\n  xhours_other: 'valandos_valandų_valandas',\n  xdays_one: 'diena_dienos_dieną',\n  xdays_other: 'dienos_dienų_dienas',\n  xweeks_one: 'savaitė_savaitės_savaitę',\n  xweeks_other: 'savaitės_savaičių_savaites',\n  xmonths_one: 'mėnuo_mėnesio_mėnesį',\n  xmonths_other: 'mėnesiai_mėnesių_mėnesius',\n  xyears_one: 'metai_metų_metus',\n  xyears_other: 'metai_metų_metus',\n  about: 'apie',\n  over: 'daugiau nei',\n  almost: 'beveik',\n  lessthan: 'mažiau nei'\n};\nvar translateSeconds = function translateSeconds(_number, addSuffix, _key, isFuture) {\n  if (!addSuffix) {\n    return 'kelios sekund<PERSON>';\n  } else {\n    return isFuture ? 'keli<PERSON> sekundži<PERSON>' : 'kelias sekundes';\n  }\n};\nvar translateSingular = function translateSingular(_number, addSuffix, key, isFuture) {\n  return !addSuffix ? forms(key)[0] : isFuture ? forms(key)[1] : forms(key)[2];\n};\nvar translate = function translate(number, addSuffix, key, isFuture) {\n  var result = number + ' ';\n  if (number === 1) {\n    return result + translateSingular(number, addSuffix, key, isFuture);\n  } else if (!addSuffix) {\n    return result + (special(number) ? forms(key)[1] : forms(key)[0]);\n  } else {\n    if (isFuture) {\n      return result + forms(key)[1];\n    } else {\n      return result + (special(number) ? forms(key)[1] : forms(key)[2]);\n    }\n  }\n};\nfunction special(number) {\n  return number % 10 === 0 || number > 10 && number < 20;\n}\nfunction forms(key) {\n  return translations[key].split('_');\n}\nvar formatDistanceLocale = {\n  lessThanXSeconds: {\n    one: translateSeconds,\n    other: translate\n  },\n  xSeconds: {\n    one: translateSeconds,\n    other: translate\n  },\n  halfAMinute: 'pusė minutės',\n  lessThanXMinutes: {\n    one: translateSingular,\n    other: translate\n  },\n  xMinutes: {\n    one: translateSingular,\n    other: translate\n  },\n  aboutXHours: {\n    one: translateSingular,\n    other: translate\n  },\n  xHours: {\n    one: translateSingular,\n    other: translate\n  },\n  xDays: {\n    one: translateSingular,\n    other: translate\n  },\n  aboutXWeeks: {\n    one: translateSingular,\n    other: translate\n  },\n  xWeeks: {\n    one: translateSingular,\n    other: translate\n  },\n  aboutXMonths: {\n    one: translateSingular,\n    other: translate\n  },\n  xMonths: {\n    one: translateSingular,\n    other: translate\n  },\n  aboutXYears: {\n    one: translateSingular,\n    other: translate\n  },\n  xYears: {\n    one: translateSingular,\n    other: translate\n  },\n  overXYears: {\n    one: translateSingular,\n    other: translate\n  },\n  almostXYears: {\n    one: translateSingular,\n    other: translate\n  }\n};\nvar formatDistance = function formatDistance(token, count, options) {\n  var adverb = token.match(/about|over|almost|lessthan/i);\n  var unit = adverb ? token.replace(adverb[0], '') : token;\n  var isFuture = (options === null || options === void 0 ? void 0 : options.comparison) !== undefined && options.comparison > 0;\n  var result;\n  var tokenValue = formatDistanceLocale[token];\n  if (typeof tokenValue === 'string') {\n    result = tokenValue;\n  } else if (count === 1) {\n    result = tokenValue.one(count, (options === null || options === void 0 ? void 0 : options.addSuffix) === true, unit.toLowerCase() + '_one', isFuture);\n  } else {\n    result = tokenValue.other(count, (options === null || options === void 0 ? void 0 : options.addSuffix) === true, unit.toLowerCase() + '_other', isFuture);\n  }\n  if (adverb) {\n    var _key2 = adverb[0].toLowerCase();\n    result = translations[_key2] + ' ' + result;\n  }\n  if (options !== null && options !== void 0 && options.addSuffix) {\n    if (options.comparison && options.comparison > 0) {\n      return 'po ' + result;\n    } else {\n      return 'prieš ' + result;\n    }\n  }\n  return result;\n};\nexport default formatDistance;", "map": {"version": 3, "names": ["translations", "xseconds_other", "xminutes_one", "xminutes_other", "xhours_one", "xhours_other", "xdays_one", "xdays_other", "xweeks_one", "xweeks_other", "xmonths_one", "xmonths_other", "xyears_one", "xyears_other", "about", "over", "almost", "lessthan", "translateSeconds", "_number", "addSuffix", "_key", "isFuture", "translateSingular", "key", "forms", "translate", "number", "result", "special", "split", "formatDistanceLocale", "lessThanXSeconds", "one", "other", "xSeconds", "halfAMinute", "lessThanXMinutes", "xMinutes", "aboutXHours", "xHours", "xDays", "aboutXWeeks", "xWeeks", "aboutXMonths", "xMonths", "aboutXYears", "xYears", "overXYears", "almostXYears", "formatDistance", "token", "count", "options", "adverb", "match", "unit", "replace", "comparison", "undefined", "tokenValue", "toLowerCase", "_key2"], "sources": ["C:/CMS/webapp/frontend/node_modules/date-fns/esm/locale/lt/_lib/formatDistance/index.js"], "sourcesContent": ["var translations = {\n  xseconds_other: 'sekundė_sekundžių_sekundes',\n  xminutes_one: 'minutė_minutės_minutę',\n  xminutes_other: 'minutės_minučių_minutes',\n  xhours_one: 'valanda_valandos_valandą',\n  xhours_other: 'valandos_valandų_valandas',\n  xdays_one: 'diena_dienos_dieną',\n  xdays_other: 'dienos_dienų_dienas',\n  xweeks_one: 'savaitė_savaitės_savaitę',\n  xweeks_other: 'savaitės_savaičių_savaites',\n  xmonths_one: 'mėnuo_mėnesio_mėnesį',\n  xmonths_other: 'mėnesiai_mėnesių_mėnesius',\n  xyears_one: 'metai_metų_metus',\n  xyears_other: 'metai_metų_metus',\n  about: 'apie',\n  over: 'daugiau nei',\n  almost: 'beveik',\n  lessthan: 'mažiau nei'\n};\nvar translateSeconds = function translateSeconds(_number, addSuffix, _key, isFuture) {\n  if (!addSuffix) {\n    return 'kelios sekund<PERSON>';\n  } else {\n    return isFuture ? 'keli<PERSON> sekundži<PERSON>' : 'kelias sekundes';\n  }\n};\nvar translateSingular = function translateSingular(_number, addSuffix, key, isFuture) {\n  return !addSuffix ? forms(key)[0] : isFuture ? forms(key)[1] : forms(key)[2];\n};\nvar translate = function translate(number, addSuffix, key, isFuture) {\n  var result = number + ' ';\n  if (number === 1) {\n    return result + translateSingular(number, addSuffix, key, isFuture);\n  } else if (!addSuffix) {\n    return result + (special(number) ? forms(key)[1] : forms(key)[0]);\n  } else {\n    if (isFuture) {\n      return result + forms(key)[1];\n    } else {\n      return result + (special(number) ? forms(key)[1] : forms(key)[2]);\n    }\n  }\n};\nfunction special(number) {\n  return number % 10 === 0 || number > 10 && number < 20;\n}\nfunction forms(key) {\n  return translations[key].split('_');\n}\nvar formatDistanceLocale = {\n  lessThanXSeconds: {\n    one: translateSeconds,\n    other: translate\n  },\n  xSeconds: {\n    one: translateSeconds,\n    other: translate\n  },\n  halfAMinute: 'pusė minutės',\n  lessThanXMinutes: {\n    one: translateSingular,\n    other: translate\n  },\n  xMinutes: {\n    one: translateSingular,\n    other: translate\n  },\n  aboutXHours: {\n    one: translateSingular,\n    other: translate\n  },\n  xHours: {\n    one: translateSingular,\n    other: translate\n  },\n  xDays: {\n    one: translateSingular,\n    other: translate\n  },\n  aboutXWeeks: {\n    one: translateSingular,\n    other: translate\n  },\n  xWeeks: {\n    one: translateSingular,\n    other: translate\n  },\n  aboutXMonths: {\n    one: translateSingular,\n    other: translate\n  },\n  xMonths: {\n    one: translateSingular,\n    other: translate\n  },\n  aboutXYears: {\n    one: translateSingular,\n    other: translate\n  },\n  xYears: {\n    one: translateSingular,\n    other: translate\n  },\n  overXYears: {\n    one: translateSingular,\n    other: translate\n  },\n  almostXYears: {\n    one: translateSingular,\n    other: translate\n  }\n};\nvar formatDistance = function formatDistance(token, count, options) {\n  var adverb = token.match(/about|over|almost|lessthan/i);\n  var unit = adverb ? token.replace(adverb[0], '') : token;\n  var isFuture = (options === null || options === void 0 ? void 0 : options.comparison) !== undefined && options.comparison > 0;\n  var result;\n  var tokenValue = formatDistanceLocale[token];\n  if (typeof tokenValue === 'string') {\n    result = tokenValue;\n  } else if (count === 1) {\n    result = tokenValue.one(count, (options === null || options === void 0 ? void 0 : options.addSuffix) === true, unit.toLowerCase() + '_one', isFuture);\n  } else {\n    result = tokenValue.other(count, (options === null || options === void 0 ? void 0 : options.addSuffix) === true, unit.toLowerCase() + '_other', isFuture);\n  }\n  if (adverb) {\n    var _key2 = adverb[0].toLowerCase();\n    result = translations[_key2] + ' ' + result;\n  }\n  if (options !== null && options !== void 0 && options.addSuffix) {\n    if (options.comparison && options.comparison > 0) {\n      return 'po ' + result;\n    } else {\n      return 'prieš ' + result;\n    }\n  }\n  return result;\n};\nexport default formatDistance;"], "mappings": "AAAA,IAAIA,YAAY,GAAG;EACjBC,cAAc,EAAE,4BAA4B;EAC5CC,YAAY,EAAE,uBAAuB;EACrCC,cAAc,EAAE,yBAAyB;EACzCC,UAAU,EAAE,0BAA0B;EACtCC,YAAY,EAAE,2BAA2B;EACzCC,SAAS,EAAE,oBAAoB;EAC/BC,WAAW,EAAE,qBAAqB;EAClCC,UAAU,EAAE,0BAA0B;EACtCC,YAAY,EAAE,4BAA4B;EAC1CC,WAAW,EAAE,sBAAsB;EACnCC,aAAa,EAAE,2BAA2B;EAC1CC,UAAU,EAAE,kBAAkB;EAC9BC,YAAY,EAAE,kBAAkB;EAChCC,KAAK,EAAE,MAAM;EACbC,IAAI,EAAE,aAAa;EACnBC,MAAM,EAAE,QAAQ;EAChBC,QAAQ,EAAE;AACZ,CAAC;AACD,IAAIC,gBAAgB,GAAG,SAASA,gBAAgBA,CAACC,OAAO,EAAEC,SAAS,EAAEC,IAAI,EAAEC,QAAQ,EAAE;EACnF,IAAI,CAACF,SAAS,EAAE;IACd,OAAO,iBAAiB;EAC1B,CAAC,MAAM;IACL,OAAOE,QAAQ,GAAG,iBAAiB,GAAG,iBAAiB;EACzD;AACF,CAAC;AACD,IAAIC,iBAAiB,GAAG,SAASA,iBAAiBA,CAACJ,OAAO,EAAEC,SAAS,EAAEI,GAAG,EAAEF,QAAQ,EAAE;EACpF,OAAO,CAACF,SAAS,GAAGK,KAAK,CAACD,GAAG,CAAC,CAAC,CAAC,CAAC,GAAGF,QAAQ,GAAGG,KAAK,CAACD,GAAG,CAAC,CAAC,CAAC,CAAC,GAAGC,KAAK,CAACD,GAAG,CAAC,CAAC,CAAC,CAAC;AAC9E,CAAC;AACD,IAAIE,SAAS,GAAG,SAASA,SAASA,CAACC,MAAM,EAAEP,SAAS,EAAEI,GAAG,EAAEF,QAAQ,EAAE;EACnE,IAAIM,MAAM,GAAGD,MAAM,GAAG,GAAG;EACzB,IAAIA,MAAM,KAAK,CAAC,EAAE;IAChB,OAAOC,MAAM,GAAGL,iBAAiB,CAACI,MAAM,EAAEP,SAAS,EAAEI,GAAG,EAAEF,QAAQ,CAAC;EACrE,CAAC,MAAM,IAAI,CAACF,SAAS,EAAE;IACrB,OAAOQ,MAAM,IAAIC,OAAO,CAACF,MAAM,CAAC,GAAGF,KAAK,CAACD,GAAG,CAAC,CAAC,CAAC,CAAC,GAAGC,KAAK,CAACD,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC;EACnE,CAAC,MAAM;IACL,IAAIF,QAAQ,EAAE;MACZ,OAAOM,MAAM,GAAGH,KAAK,CAACD,GAAG,CAAC,CAAC,CAAC,CAAC;IAC/B,CAAC,MAAM;MACL,OAAOI,MAAM,IAAIC,OAAO,CAACF,MAAM,CAAC,GAAGF,KAAK,CAACD,GAAG,CAAC,CAAC,CAAC,CAAC,GAAGC,KAAK,CAACD,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC;IACnE;EACF;AACF,CAAC;AACD,SAASK,OAAOA,CAACF,MAAM,EAAE;EACvB,OAAOA,MAAM,GAAG,EAAE,KAAK,CAAC,IAAIA,MAAM,GAAG,EAAE,IAAIA,MAAM,GAAG,EAAE;AACxD;AACA,SAASF,KAAKA,CAACD,GAAG,EAAE;EAClB,OAAOxB,YAAY,CAACwB,GAAG,CAAC,CAACM,KAAK,CAAC,GAAG,CAAC;AACrC;AACA,IAAIC,oBAAoB,GAAG;EACzBC,gBAAgB,EAAE;IAChBC,GAAG,EAAEf,gBAAgB;IACrBgB,KAAK,EAAER;EACT,CAAC;EACDS,QAAQ,EAAE;IACRF,GAAG,EAAEf,gBAAgB;IACrBgB,KAAK,EAAER;EACT,CAAC;EACDU,WAAW,EAAE,cAAc;EAC3BC,gBAAgB,EAAE;IAChBJ,GAAG,EAAEV,iBAAiB;IACtBW,KAAK,EAAER;EACT,CAAC;EACDY,QAAQ,EAAE;IACRL,GAAG,EAAEV,iBAAiB;IACtBW,KAAK,EAAER;EACT,CAAC;EACDa,WAAW,EAAE;IACXN,GAAG,EAAEV,iBAAiB;IACtBW,KAAK,EAAER;EACT,CAAC;EACDc,MAAM,EAAE;IACNP,GAAG,EAAEV,iBAAiB;IACtBW,KAAK,EAAER;EACT,CAAC;EACDe,KAAK,EAAE;IACLR,GAAG,EAAEV,iBAAiB;IACtBW,KAAK,EAAER;EACT,CAAC;EACDgB,WAAW,EAAE;IACXT,GAAG,EAAEV,iBAAiB;IACtBW,KAAK,EAAER;EACT,CAAC;EACDiB,MAAM,EAAE;IACNV,GAAG,EAAEV,iBAAiB;IACtBW,KAAK,EAAER;EACT,CAAC;EACDkB,YAAY,EAAE;IACZX,GAAG,EAAEV,iBAAiB;IACtBW,KAAK,EAAER;EACT,CAAC;EACDmB,OAAO,EAAE;IACPZ,GAAG,EAAEV,iBAAiB;IACtBW,KAAK,EAAER;EACT,CAAC;EACDoB,WAAW,EAAE;IACXb,GAAG,EAAEV,iBAAiB;IACtBW,KAAK,EAAER;EACT,CAAC;EACDqB,MAAM,EAAE;IACNd,GAAG,EAAEV,iBAAiB;IACtBW,KAAK,EAAER;EACT,CAAC;EACDsB,UAAU,EAAE;IACVf,GAAG,EAAEV,iBAAiB;IACtBW,KAAK,EAAER;EACT,CAAC;EACDuB,YAAY,EAAE;IACZhB,GAAG,EAAEV,iBAAiB;IACtBW,KAAK,EAAER;EACT;AACF,CAAC;AACD,IAAIwB,cAAc,GAAG,SAASA,cAAcA,CAACC,KAAK,EAAEC,KAAK,EAAEC,OAAO,EAAE;EAClE,IAAIC,MAAM,GAAGH,KAAK,CAACI,KAAK,CAAC,6BAA6B,CAAC;EACvD,IAAIC,IAAI,GAAGF,MAAM,GAAGH,KAAK,CAACM,OAAO,CAACH,MAAM,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,GAAGH,KAAK;EACxD,IAAI7B,QAAQ,GAAG,CAAC+B,OAAO,KAAK,IAAI,IAAIA,OAAO,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,OAAO,CAACK,UAAU,MAAMC,SAAS,IAAIN,OAAO,CAACK,UAAU,GAAG,CAAC;EAC7H,IAAI9B,MAAM;EACV,IAAIgC,UAAU,GAAG7B,oBAAoB,CAACoB,KAAK,CAAC;EAC5C,IAAI,OAAOS,UAAU,KAAK,QAAQ,EAAE;IAClChC,MAAM,GAAGgC,UAAU;EACrB,CAAC,MAAM,IAAIR,KAAK,KAAK,CAAC,EAAE;IACtBxB,MAAM,GAAGgC,UAAU,CAAC3B,GAAG,CAACmB,KAAK,EAAE,CAACC,OAAO,KAAK,IAAI,IAAIA,OAAO,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,OAAO,CAACjC,SAAS,MAAM,IAAI,EAAEoC,IAAI,CAACK,WAAW,CAAC,CAAC,GAAG,MAAM,EAAEvC,QAAQ,CAAC;EACvJ,CAAC,MAAM;IACLM,MAAM,GAAGgC,UAAU,CAAC1B,KAAK,CAACkB,KAAK,EAAE,CAACC,OAAO,KAAK,IAAI,IAAIA,OAAO,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,OAAO,CAACjC,SAAS,MAAM,IAAI,EAAEoC,IAAI,CAACK,WAAW,CAAC,CAAC,GAAG,QAAQ,EAAEvC,QAAQ,CAAC;EAC3J;EACA,IAAIgC,MAAM,EAAE;IACV,IAAIQ,KAAK,GAAGR,MAAM,CAAC,CAAC,CAAC,CAACO,WAAW,CAAC,CAAC;IACnCjC,MAAM,GAAG5B,YAAY,CAAC8D,KAAK,CAAC,GAAG,GAAG,GAAGlC,MAAM;EAC7C;EACA,IAAIyB,OAAO,KAAK,IAAI,IAAIA,OAAO,KAAK,KAAK,CAAC,IAAIA,OAAO,CAACjC,SAAS,EAAE;IAC/D,IAAIiC,OAAO,CAACK,UAAU,IAAIL,OAAO,CAACK,UAAU,GAAG,CAAC,EAAE;MAChD,OAAO,KAAK,GAAG9B,MAAM;IACvB,CAAC,MAAM;MACL,OAAO,QAAQ,GAAGA,MAAM;IAC1B;EACF;EACA,OAAOA,MAAM;AACf,CAAC;AACD,eAAesB,cAAc", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}