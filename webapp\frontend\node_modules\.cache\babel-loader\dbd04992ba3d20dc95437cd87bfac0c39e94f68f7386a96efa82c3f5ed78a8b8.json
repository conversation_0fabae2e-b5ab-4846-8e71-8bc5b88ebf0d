{"ast": null, "code": "var _jsxFileName = \"C:\\\\CMS\\\\webapp\\\\frontend\\\\src\\\\pages\\\\cavi\\\\parco\\\\CreaBobinaPage.js\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect } from 'react';\nimport { Box, Typography, Alert, Snackbar } from '@mui/material';\nimport { useNavigate } from 'react-router-dom';\nimport { useAuth } from '../../../context/AuthContext';\nimport AdminHomeButton from '../../../components/common/AdminHomeButton';\nimport ParcoCavi from '../../../components/cavi/ParcoCavi';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst CreaBobinaPage = () => {\n  _s();\n  const navigate = useNavigate();\n  const {\n    selectedCantiere\n  } = useAuth();\n  const [successMessage, setSuccessMessage] = useState('');\n  const [errorMessage, setErrorMessage] = useState('');\n  const [openSuccess, setOpenSuccess] = useState(false);\n  const [openError, setOpenError] = useState(false);\n\n  // Recupera l'ID del cantiere dal localStorage se non è disponibile nel contesto\n  const cantiereId = selectedCantiere ? selectedCantiere.id_cantiere : parseInt(localStorage.getItem('selectedCantiereId'), 10);\n\n  // Aggiungi un event listener per gestire il reindirizzamento dopo la creazione della bobina\n  useEffect(() => {\n    const handleRedirect = event => {\n      // Reindirizza alla pagina di visualizzazione bobine\n      navigate('/dashboard/cavi/parco/visualizza');\n    };\n\n    // Aggiungi l'event listener\n    window.addEventListener('redirectToVisualizzaBobine', handleRedirect);\n\n    // Rimuovi l'event listener quando il componente viene smontato\n    return () => {\n      window.removeEventListener('redirectToVisualizzaBobine', handleRedirect);\n    };\n  }, [navigate]);\n\n  // Verifica se un cantiere è selezionato\n  if (!cantiereId || isNaN(cantiereId)) {\n    return /*#__PURE__*/_jsxDEV(Box, {\n      sx: {\n        p: 3\n      },\n      children: /*#__PURE__*/_jsxDEV(Alert, {\n        severity: \"warning\",\n        children: \"Nessun cantiere selezionato. Seleziona un cantiere per gestire il parco cavi.\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 44,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 43,\n      columnNumber: 7\n    }, this);\n  }\n\n  // Gestisce il ritorno alla pagina dei cavi\n  const handleBackToCantieri = () => {\n    navigate('/dashboard/cavi');\n  };\n\n  // Gestisce i messaggi di successo\n  const handleSuccess = message => {\n    setSuccessMessage(message);\n    setOpenSuccess(true);\n  };\n\n  // Gestisce i messaggi di errore\n  const handleError = message => {\n    setErrorMessage(message);\n    setOpenError(true);\n  };\n\n  // Chiude i messaggi\n  const handleCloseSuccess = () => {\n    setOpenSuccess(false);\n  };\n  const handleCloseError = () => {\n    setOpenError(false);\n  };\n  return /*#__PURE__*/_jsxDEV(Box, {\n    children: [/*#__PURE__*/_jsxDEV(Box, {\n      sx: {\n        mb: 3,\n        display: 'flex',\n        alignItems: 'center',\n        justifyContent: 'flex-end'\n      },\n      children: /*#__PURE__*/_jsxDEV(AdminHomeButton, {}, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 80,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 79,\n      columnNumber: 7\n    }, this), cantiereId && /*#__PURE__*/_jsxDEV(ParcoCavi, {\n      cantiereId: cantiereId,\n      onSuccess: handleSuccess,\n      onError: handleError,\n      initialOption: \"creaBobina\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 84,\n      columnNumber: 9\n    }, this), /*#__PURE__*/_jsxDEV(Snackbar, {\n      open: openSuccess,\n      autoHideDuration: 6000,\n      onClose: handleCloseSuccess,\n      anchorOrigin: {\n        vertical: 'bottom',\n        horizontal: 'center'\n      },\n      children: /*#__PURE__*/_jsxDEV(Alert, {\n        onClose: handleCloseSuccess,\n        severity: \"success\",\n        sx: {\n          width: '100%'\n        },\n        children: successMessage\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 99,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 93,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Snackbar, {\n      open: openError,\n      autoHideDuration: 6000,\n      onClose: handleCloseError,\n      anchorOrigin: {\n        vertical: 'bottom',\n        horizontal: 'center'\n      },\n      children: /*#__PURE__*/_jsxDEV(Alert, {\n        onClose: handleCloseError,\n        severity: \"error\",\n        sx: {\n          width: '100%'\n        },\n        children: errorMessage\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 111,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 105,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 78,\n    columnNumber: 5\n  }, this);\n};\n_s(CreaBobinaPage, \"opSEuA9xlCVBuiqB2I5j/zyo6d4=\", false, function () {\n  return [useNavigate, useAuth];\n});\n_c = CreaBobinaPage;\nexport default CreaBobinaPage;\nvar _c;\n$RefreshReg$(_c, \"CreaBobinaPage\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "Box", "Typography", "<PERSON><PERSON>", "Snackbar", "useNavigate", "useAuth", "AdminHomeButton", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "jsxDEV", "_jsxDEV", "CreaBobinaPage", "_s", "navigate", "selected<PERSON><PERSON><PERSON>", "successMessage", "setSuccessMessage", "errorMessage", "setErrorMessage", "openSuccess", "setOpenSuccess", "openError", "set<PERSON>pen<PERSON>rror", "cantiereId", "id_cantiere", "parseInt", "localStorage", "getItem", "handleRedirect", "event", "window", "addEventListener", "removeEventListener", "isNaN", "sx", "p", "children", "severity", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "handleBackToCantieri", "handleSuccess", "message", "handleError", "handleCloseSuccess", "handleCloseError", "mb", "display", "alignItems", "justifyContent", "onSuccess", "onError", "initialOption", "open", "autoHideDuration", "onClose", "anchor<PERSON><PERSON><PERSON>", "vertical", "horizontal", "width", "_c", "$RefreshReg$"], "sources": ["C:/CMS/webapp/frontend/src/pages/cavi/parco/CreaBobinaPage.js"], "sourcesContent": ["import React, { useState, useEffect } from 'react';\nimport {\n  Box,\n  Typography,\n  Alert,\n  Snackbar\n} from '@mui/material';\nimport { useNavigate } from 'react-router-dom';\nimport { useAuth } from '../../../context/AuthContext';\nimport AdminHomeButton from '../../../components/common/AdminHomeButton';\nimport ParcoCavi from '../../../components/cavi/ParcoCavi';\n\nconst CreaBobinaPage = () => {\n  const navigate = useNavigate();\n  const { selectedCantiere } = useAuth();\n  const [successMessage, setSuccessMessage] = useState('');\n  const [errorMessage, setErrorMessage] = useState('');\n  const [openSuccess, setOpenSuccess] = useState(false);\n  const [openError, setOpenError] = useState(false);\n\n  // Recupera l'ID del cantiere dal localStorage se non è disponibile nel contesto\n  const cantiereId = selectedCantiere ? selectedCantiere.id_cantiere : parseInt(localStorage.getItem('selectedCantiereId'), 10);\n\n  // Aggiungi un event listener per gestire il reindirizzamento dopo la creazione della bobina\n  useEffect(() => {\n    const handleRedirect = (event) => {\n      // Reindirizza alla pagina di visualizzazione bobine\n      navigate('/dashboard/cavi/parco/visualizza');\n    };\n\n    // Aggiungi l'event listener\n    window.addEventListener('redirectToVisualizzaBobine', handleRedirect);\n\n    // Rimuovi l'event listener quando il componente viene smontato\n    return () => {\n      window.removeEventListener('redirectToVisualizzaBobine', handleRedirect);\n    };\n  }, [navigate]);\n\n  // Verifica se un cantiere è selezionato\n  if (!cantiereId || isNaN(cantiereId)) {\n    return (\n      <Box sx={{ p: 3 }}>\n        <Alert severity=\"warning\">\n          Nessun cantiere selezionato. Seleziona un cantiere per gestire il parco cavi.\n        </Alert>\n      </Box>\n    );\n  }\n\n  // Gestisce il ritorno alla pagina dei cavi\n  const handleBackToCantieri = () => {\n    navigate('/dashboard/cavi');\n  };\n\n  // Gestisce i messaggi di successo\n  const handleSuccess = (message) => {\n    setSuccessMessage(message);\n    setOpenSuccess(true);\n  };\n\n  // Gestisce i messaggi di errore\n  const handleError = (message) => {\n    setErrorMessage(message);\n    setOpenError(true);\n  };\n\n  // Chiude i messaggi\n  const handleCloseSuccess = () => {\n    setOpenSuccess(false);\n  };\n\n  const handleCloseError = () => {\n    setOpenError(false);\n  };\n\n  return (\n    <Box>\n      <Box sx={{ mb: 3, display: 'flex', alignItems: 'center', justifyContent: 'flex-end' }}>\n        <AdminHomeButton />\n      </Box>\n\n      {cantiereId && (\n        <ParcoCavi\n          cantiereId={cantiereId}\n          onSuccess={handleSuccess}\n          onError={handleError}\n          initialOption=\"creaBobina\"\n        />\n      )}\n\n      {/* Snackbar per i messaggi di successo */}\n      <Snackbar\n        open={openSuccess}\n        autoHideDuration={6000}\n        onClose={handleCloseSuccess}\n        anchorOrigin={{ vertical: 'bottom', horizontal: 'center' }}\n      >\n        <Alert onClose={handleCloseSuccess} severity=\"success\" sx={{ width: '100%' }}>\n          {successMessage}\n        </Alert>\n      </Snackbar>\n\n      {/* Snackbar per i messaggi di errore */}\n      <Snackbar\n        open={openError}\n        autoHideDuration={6000}\n        onClose={handleCloseError}\n        anchorOrigin={{ vertical: 'bottom', horizontal: 'center' }}\n      >\n        <Alert onClose={handleCloseError} severity=\"error\" sx={{ width: '100%' }}>\n          {errorMessage}\n        </Alert>\n      </Snackbar>\n    </Box>\n  );\n};\n\nexport default CreaBobinaPage;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAClD,SACEC,GAAG,EACHC,UAAU,EACVC,KAAK,EACLC,QAAQ,QACH,eAAe;AACtB,SAASC,WAAW,QAAQ,kBAAkB;AAC9C,SAASC,OAAO,QAAQ,8BAA8B;AACtD,OAAOC,eAAe,MAAM,4CAA4C;AACxE,OAAOC,SAAS,MAAM,oCAAoC;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAE3D,MAAMC,cAAc,GAAGA,CAAA,KAAM;EAAAC,EAAA;EAC3B,MAAMC,QAAQ,GAAGR,WAAW,CAAC,CAAC;EAC9B,MAAM;IAAES;EAAiB,CAAC,GAAGR,OAAO,CAAC,CAAC;EACtC,MAAM,CAACS,cAAc,EAAEC,iBAAiB,CAAC,GAAGjB,QAAQ,CAAC,EAAE,CAAC;EACxD,MAAM,CAACkB,YAAY,EAAEC,eAAe,CAAC,GAAGnB,QAAQ,CAAC,EAAE,CAAC;EACpD,MAAM,CAACoB,WAAW,EAAEC,cAAc,CAAC,GAAGrB,QAAQ,CAAC,KAAK,CAAC;EACrD,MAAM,CAACsB,SAAS,EAAEC,YAAY,CAAC,GAAGvB,QAAQ,CAAC,KAAK,CAAC;;EAEjD;EACA,MAAMwB,UAAU,GAAGT,gBAAgB,GAAGA,gBAAgB,CAACU,WAAW,GAAGC,QAAQ,CAACC,YAAY,CAACC,OAAO,CAAC,oBAAoB,CAAC,EAAE,EAAE,CAAC;;EAE7H;EACA3B,SAAS,CAAC,MAAM;IACd,MAAM4B,cAAc,GAAIC,KAAK,IAAK;MAChC;MACAhB,QAAQ,CAAC,kCAAkC,CAAC;IAC9C,CAAC;;IAED;IACAiB,MAAM,CAACC,gBAAgB,CAAC,4BAA4B,EAAEH,cAAc,CAAC;;IAErE;IACA,OAAO,MAAM;MACXE,MAAM,CAACE,mBAAmB,CAAC,4BAA4B,EAAEJ,cAAc,CAAC;IAC1E,CAAC;EACH,CAAC,EAAE,CAACf,QAAQ,CAAC,CAAC;;EAEd;EACA,IAAI,CAACU,UAAU,IAAIU,KAAK,CAACV,UAAU,CAAC,EAAE;IACpC,oBACEb,OAAA,CAACT,GAAG;MAACiC,EAAE,EAAE;QAAEC,CAAC,EAAE;MAAE,CAAE;MAAAC,QAAA,eAChB1B,OAAA,CAACP,KAAK;QAACkC,QAAQ,EAAC,SAAS;QAAAD,QAAA,EAAC;MAE1B;QAAAE,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAO;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACL,CAAC;EAEV;;EAEA;EACA,MAAMC,oBAAoB,GAAGA,CAAA,KAAM;IACjC7B,QAAQ,CAAC,iBAAiB,CAAC;EAC7B,CAAC;;EAED;EACA,MAAM8B,aAAa,GAAIC,OAAO,IAAK;IACjC5B,iBAAiB,CAAC4B,OAAO,CAAC;IAC1BxB,cAAc,CAAC,IAAI,CAAC;EACtB,CAAC;;EAED;EACA,MAAMyB,WAAW,GAAID,OAAO,IAAK;IAC/B1B,eAAe,CAAC0B,OAAO,CAAC;IACxBtB,YAAY,CAAC,IAAI,CAAC;EACpB,CAAC;;EAED;EACA,MAAMwB,kBAAkB,GAAGA,CAAA,KAAM;IAC/B1B,cAAc,CAAC,KAAK,CAAC;EACvB,CAAC;EAED,MAAM2B,gBAAgB,GAAGA,CAAA,KAAM;IAC7BzB,YAAY,CAAC,KAAK,CAAC;EACrB,CAAC;EAED,oBACEZ,OAAA,CAACT,GAAG;IAAAmC,QAAA,gBACF1B,OAAA,CAACT,GAAG;MAACiC,EAAE,EAAE;QAAEc,EAAE,EAAE,CAAC;QAAEC,OAAO,EAAE,MAAM;QAAEC,UAAU,EAAE,QAAQ;QAAEC,cAAc,EAAE;MAAW,CAAE;MAAAf,QAAA,eACpF1B,OAAA,CAACH,eAAe;QAAA+B,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAChB,CAAC,EAELlB,UAAU,iBACTb,OAAA,CAACF,SAAS;MACRe,UAAU,EAAEA,UAAW;MACvB6B,SAAS,EAAET,aAAc;MACzBU,OAAO,EAAER,WAAY;MACrBS,aAAa,EAAC;IAAY;MAAAhB,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAC3B,CACF,eAGD/B,OAAA,CAACN,QAAQ;MACPmD,IAAI,EAAEpC,WAAY;MAClBqC,gBAAgB,EAAE,IAAK;MACvBC,OAAO,EAAEX,kBAAmB;MAC5BY,YAAY,EAAE;QAAEC,QAAQ,EAAE,QAAQ;QAAEC,UAAU,EAAE;MAAS,CAAE;MAAAxB,QAAA,eAE3D1B,OAAA,CAACP,KAAK;QAACsD,OAAO,EAAEX,kBAAmB;QAACT,QAAQ,EAAC,SAAS;QAACH,EAAE,EAAE;UAAE2B,KAAK,EAAE;QAAO,CAAE;QAAAzB,QAAA,EAC1ErB;MAAc;QAAAuB,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACV;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACA,CAAC,eAGX/B,OAAA,CAACN,QAAQ;MACPmD,IAAI,EAAElC,SAAU;MAChBmC,gBAAgB,EAAE,IAAK;MACvBC,OAAO,EAAEV,gBAAiB;MAC1BW,YAAY,EAAE;QAAEC,QAAQ,EAAE,QAAQ;QAAEC,UAAU,EAAE;MAAS,CAAE;MAAAxB,QAAA,eAE3D1B,OAAA,CAACP,KAAK;QAACsD,OAAO,EAAEV,gBAAiB;QAACV,QAAQ,EAAC,OAAO;QAACH,EAAE,EAAE;UAAE2B,KAAK,EAAE;QAAO,CAAE;QAAAzB,QAAA,EACtEnB;MAAY;QAAAqB,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACR;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACA,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACR,CAAC;AAEV,CAAC;AAAC7B,EAAA,CAxGID,cAAc;EAAA,QACDN,WAAW,EACCC,OAAO;AAAA;AAAAwD,EAAA,GAFhCnD,cAAc;AA0GpB,eAAeA,cAAc;AAAC,IAAAmD,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}