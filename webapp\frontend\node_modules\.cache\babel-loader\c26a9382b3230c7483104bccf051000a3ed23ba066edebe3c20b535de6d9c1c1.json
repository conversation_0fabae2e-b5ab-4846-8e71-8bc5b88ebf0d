{"ast": null, "code": "import { toDate } from \"./toDate.mjs\";\n\n/**\n * @name lastDayOfQuarter\n * @category Quarter Helpers\n * @summary Return the last day of a year quarter for the given date.\n *\n * @description\n * Return the last day of a year quarter for the given date.\n * The result will be in the local timezone.\n *\n * @typeParam DateType - The `Date` type, the function operates on. Gets inferred from passed arguments. Allows to use extensions like [`UTCDate`](https://github.com/date-fns/utc).\n *\n * @param date - The original date\n *\n * @returns The last day of a quarter\n *\n * @example\n * // The last day of a quarter for 2 September 2014 11:55:00:\n * const result = lastDayOfQuarter(new Date(2014, 8, 2, 11, 55, 0))\n * //=> Tue Sep 30 2014 00:00:00\n */\nexport function lastDayOfQuarter(date) {\n  const _date = toDate(date);\n  const currentMonth = _date.getMonth();\n  const month = currentMonth - currentMonth % 3 + 3;\n  _date.setMonth(month, 0);\n  _date.setHours(0, 0, 0, 0);\n  return _date;\n}\n\n// Fallback for modularized imports:\nexport default lastDayOfQuarter;", "map": {"version": 3, "names": ["toDate", "lastDayOfQuarter", "date", "_date", "currentMonth", "getMonth", "month", "setMonth", "setHours"], "sources": ["C:/CMS/webapp/frontend/node_modules/date-fns/lastDayOfQuarter.mjs"], "sourcesContent": ["import { toDate } from \"./toDate.mjs\";\n\n/**\n * @name lastDayOfQuarter\n * @category Quarter Helpers\n * @summary Return the last day of a year quarter for the given date.\n *\n * @description\n * Return the last day of a year quarter for the given date.\n * The result will be in the local timezone.\n *\n * @typeParam DateType - The `Date` type, the function operates on. Gets inferred from passed arguments. Allows to use extensions like [`UTCDate`](https://github.com/date-fns/utc).\n *\n * @param date - The original date\n *\n * @returns The last day of a quarter\n *\n * @example\n * // The last day of a quarter for 2 September 2014 11:55:00:\n * const result = lastDayOfQuarter(new Date(2014, 8, 2, 11, 55, 0))\n * //=> Tue Sep 30 2014 00:00:00\n */\nexport function lastDayOfQuarter(date) {\n  const _date = toDate(date);\n  const currentMonth = _date.getMonth();\n  const month = currentMonth - (currentMonth % 3) + 3;\n  _date.setMonth(month, 0);\n  _date.setHours(0, 0, 0, 0);\n  return _date;\n}\n\n// Fallback for modularized imports:\nexport default lastDayOfQuarter;\n"], "mappings": "AAAA,SAASA,MAAM,QAAQ,cAAc;;AAErC;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO,SAASC,gBAAgBA,CAACC,IAAI,EAAE;EACrC,MAAMC,KAAK,GAAGH,MAAM,CAACE,IAAI,CAAC;EAC1B,MAAME,YAAY,GAAGD,KAAK,CAACE,QAAQ,CAAC,CAAC;EACrC,MAAMC,KAAK,GAAGF,YAAY,GAAIA,YAAY,GAAG,CAAE,GAAG,CAAC;EACnDD,KAAK,CAACI,QAAQ,CAACD,KAAK,EAAE,CAAC,CAAC;EACxBH,KAAK,CAACK,QAAQ,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC;EAC1B,OAAOL,KAAK;AACd;;AAEA;AACA,eAAeF,gBAAgB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}