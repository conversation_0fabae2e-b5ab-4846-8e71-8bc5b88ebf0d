{"ast": null, "code": "'use client';\n\nexport { default } from './Breadcrumbs';\nexport { default as breadcrumbsClasses } from './breadcrumbsClasses';\nexport * from './breadcrumbsClasses';", "map": {"version": 3, "names": ["default", "breadcrumbsClasses"], "sources": ["C:/CMS/webapp/frontend/node_modules/@mui/material/Breadcrumbs/index.js"], "sourcesContent": ["'use client';\n\nexport { default } from './Breadcrumbs';\nexport { default as breadcrumbsClasses } from './breadcrumbsClasses';\nexport * from './breadcrumbsClasses';"], "mappings": "AAAA,YAAY;;AAEZ,SAASA,OAAO,QAAQ,eAAe;AACvC,SAASA,OAAO,IAAIC,kBAAkB,QAAQ,sBAAsB;AACpE,cAAc,sBAAsB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}