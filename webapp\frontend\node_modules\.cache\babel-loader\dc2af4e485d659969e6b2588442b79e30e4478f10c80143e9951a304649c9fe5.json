{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\PycharmProjects\\\\CMS\\\\webapp\\\\frontend\\\\src\\\\components\\\\cavi\\\\CavoDetailsView.js\";\nimport React from 'react';\nimport { Box, Paper, Typography, Grid, Divider, Chip } from '@mui/material';\nimport { getCableStateColor } from '../../utils/stateUtils';\n\n/**\n * Component for displaying detailed information about a cable\n * Implements the same visualization as _visualizza_dettagli_cavo in the CLI\n *\n * @param {Object} props - Component props\n * @param {Object} props.cavo - The cable object to display\n * @param {boolean} props.compact - Whether to display in compact mode (default: false)\n * @param {string} props.title - Optional title for the component\n */\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst CavoDetailsView = ({\n  cavo,\n  compact = false,\n  title = \"Dettagli Cavo\"\n}) => {\n  if (!cavo) return null;\n\n  // Helper function to format a field value\n  const formatValue = value => {\n    if (value === null || value === undefined || value === '') {\n      return 'N/A';\n    }\n    return value;\n  };\n\n  // Define the fields to display\n  const fields = [{\n    label: 'ID Cavo',\n    value: cavo.id_cavo,\n    primary: true\n  }, {\n    label: 'Sistema',\n    value: cavo.sistema\n  }, {\n    label: 'Utility',\n    value: cavo.utility\n  }, {\n    label: 'Colore Cavo',\n    value: cavo.colore_cavo\n  }, {\n    label: 'Tipologia',\n    value: cavo.tipologia\n  }, {\n    label: 'N° Conduttori',\n    value: cavo.n_conduttori\n  }, {\n    label: 'Sezione',\n    value: cavo.sezione\n  }, {\n    label: 'SH',\n    value: cavo.sh\n  }, {\n    label: 'Ubicazione Partenza',\n    value: cavo.ubicazione_partenza\n  }, {\n    label: 'Utenza Partenza',\n    value: cavo.utenza_partenza\n  }, {\n    label: 'Descrizione Utenza Partenza',\n    value: cavo.descrizione_utenza_partenza\n  }, {\n    label: 'Ubicazione Arrivo',\n    value: cavo.ubicazione_arrivo\n  }, {\n    label: 'Utenza Arrivo',\n    value: cavo.utenza_arrivo\n  }, {\n    label: 'Descrizione Utenza Arrivo',\n    value: cavo.descrizione_utenza_arrivo\n  }, {\n    label: 'Metri Teorici',\n    value: cavo.metri_teorici\n  }, {\n    label: 'Stato Installazione',\n    value: cavo.stato_installazione,\n    chip: true,\n    chipColor: getCableStateColor(cavo.stato_installazione)\n  }];\n\n  // Add metratura_reale only if it's greater than 0\n  if (cavo.metratura_reale && parseFloat(cavo.metratura_reale) > 0) {\n    fields.push({\n      label: 'Metratura Reale',\n      value: cavo.metratura_reale + ' m'\n    });\n  }\n\n  // Add bobina information if available\n  if (cavo.id_bobina && cavo.id_bobina !== 'TBD') {\n    fields.push({\n      label: 'Bobina Associata',\n      value: cavo.id_bobina\n    });\n  } else if (cavo.id_bobina === 'BOBINA_VUOTA') {\n    // Quando id_bobina è 'BOBINA_VUOTA', mostra 'BOBINA VUOTA'\n    fields.push({\n      label: 'Bobina Associata',\n      value: 'BOBINA VUOTA'\n    });\n  } else if (cavo.id_bobina === null) {\n    // Quando id_bobina è null (cavi non posati), non mostrare la bobina\n    // Non aggiungere il campo alla lista\n  }\n\n  // Add data_posa if available\n  if (cavo.data_posa) {\n    // Format the date\n    const date = new Date(cavo.data_posa);\n    const formattedDate = date.toLocaleDateString('it-IT', {\n      day: '2-digit',\n      month: '2-digit',\n      year: 'numeric'\n    });\n    fields.push({\n      label: 'Data Posa',\n      value: formattedDate\n    });\n  }\n  return /*#__PURE__*/_jsxDEV(Paper, {\n    sx: {\n      p: compact ? 2 : 3,\n      mb: 3\n    },\n    children: [/*#__PURE__*/_jsxDEV(Typography, {\n      variant: compact ? \"h6\" : \"h5\",\n      gutterBottom: true,\n      children: title\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 87,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Divider, {\n      sx: {\n        mb: 2\n      }\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 91,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Grid, {\n      container: true,\n      spacing: compact ? 1 : 2,\n      children: fields.map((field, index) => /*#__PURE__*/_jsxDEV(Grid, {\n        item: true,\n        xs: 12,\n        sm: compact ? 6 : 4,\n        children: /*#__PURE__*/_jsxDEV(Box, {\n          sx: {\n            mb: compact ? 1 : 2\n          },\n          children: [/*#__PURE__*/_jsxDEV(Typography, {\n            variant: \"caption\",\n            color: \"text.secondary\",\n            children: field.label\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 97,\n            columnNumber: 15\n          }, this), field.chip ? /*#__PURE__*/_jsxDEV(Box, {\n            sx: {\n              mt: 0.5\n            },\n            children: /*#__PURE__*/_jsxDEV(Chip, {\n              label: formatValue(field.value),\n              color: field.chipColor || 'default',\n              size: compact ? \"small\" : \"medium\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 102,\n              columnNumber: 19\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 101,\n            columnNumber: 17\n          }, this) : /*#__PURE__*/_jsxDEV(Typography, {\n            variant: field.primary ? \"subtitle1\" : \"body2\",\n            sx: {\n              fontWeight: field.primary ? 'bold' : 'regular'\n            },\n            children: formatValue(field.value)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 109,\n            columnNumber: 17\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 96,\n          columnNumber: 13\n        }, this)\n      }, index, false, {\n        fileName: _jsxFileName,\n        lineNumber: 95,\n        columnNumber: 11\n      }, this))\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 93,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 86,\n    columnNumber: 5\n  }, this);\n};\n_c = CavoDetailsView;\nexport default CavoDetailsView;\nvar _c;\n$RefreshReg$(_c, \"CavoDetailsView\");", "map": {"version": 3, "names": ["React", "Box", "Paper", "Typography", "Grid", "Divider", "Chip", "getCableStateColor", "jsxDEV", "_jsxDEV", "CavoDetailsView", "cavo", "compact", "title", "formatValue", "value", "undefined", "fields", "label", "id_cavo", "primary", "sistema", "utility", "colore_cavo", "tipologia", "n_conduttori", "sezione", "sh", "ubicazione_partenza", "utenza_partenza", "descrizione_utenza_partenza", "ubicazione_arrivo", "utenza_arrivo", "descrizione_utenza_arrivo", "metri_te<PERSON>ci", "stato_installazione", "chip", "chipColor", "metratura_reale", "parseFloat", "push", "id_bobina", "data_posa", "date", "Date", "formattedDate", "toLocaleDateString", "day", "month", "year", "sx", "p", "mb", "children", "variant", "gutterBottom", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "container", "spacing", "map", "field", "index", "item", "xs", "sm", "color", "mt", "size", "fontWeight", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/PycharmProjects/CMS/webapp/frontend/src/components/cavi/CavoDetailsView.js"], "sourcesContent": ["import React from 'react';\nimport {\n  Box,\n  Paper,\n  Typography,\n  Grid,\n  Divider,\n  Chip\n} from '@mui/material';\nimport { getCableStateColor } from '../../utils/stateUtils';\n\n/**\n * Component for displaying detailed information about a cable\n * Implements the same visualization as _visualizza_dettagli_cavo in the CLI\n *\n * @param {Object} props - Component props\n * @param {Object} props.cavo - The cable object to display\n * @param {boolean} props.compact - Whether to display in compact mode (default: false)\n * @param {string} props.title - Optional title for the component\n */\nconst CavoDetailsView = ({ cavo, compact = false, title = \"Dettagli Cavo\" }) => {\n  if (!cavo) return null;\n\n  // Helper function to format a field value\n  const formatValue = (value) => {\n    if (value === null || value === undefined || value === '') {\n      return 'N/A';\n    }\n    return value;\n  };\n\n  // Define the fields to display\n  const fields = [\n    { label: 'ID Cavo', value: cavo.id_cavo, primary: true },\n    { label: 'Sistema', value: cavo.sistema },\n    { label: 'Utility', value: cavo.utility },\n    { label: 'Colore Cavo', value: cavo.colore_cavo },\n    { label: 'Tipologia', value: cavo.tipologia },\n    { label: 'N° Conduttori', value: cavo.n_conduttori },\n    { label: 'Sezione', value: cavo.sezione },\n    { label: 'SH', value: cavo.sh },\n    { label: 'Ubicazione Partenza', value: cavo.ubicazione_partenza },\n    { label: 'Utenza Partenza', value: cavo.utenza_partenza },\n    { label: 'Descrizione Utenza Partenza', value: cavo.descrizione_utenza_partenza },\n    { label: 'Ubicazione Arrivo', value: cavo.ubicazione_arrivo },\n    { label: 'Utenza Arrivo', value: cavo.utenza_arrivo },\n    { label: 'Descrizione Utenza Arrivo', value: cavo.descrizione_utenza_arrivo },\n    { label: 'Metri Teorici', value: cavo.metri_teorici },\n    {\n      label: 'Stato Installazione',\n      value: cavo.stato_installazione,\n      chip: true,\n      chipColor: getCableStateColor(cavo.stato_installazione)\n    }\n  ];\n\n  // Add metratura_reale only if it's greater than 0\n  if (cavo.metratura_reale && parseFloat(cavo.metratura_reale) > 0) {\n    fields.push({ label: 'Metratura Reale', value: cavo.metratura_reale + ' m' });\n  }\n\n  // Add bobina information if available\n  if (cavo.id_bobina && cavo.id_bobina !== 'TBD') {\n    fields.push({ label: 'Bobina Associata', value: cavo.id_bobina });\n  } else if (cavo.id_bobina === 'BOBINA_VUOTA') {\n    // Quando id_bobina è 'BOBINA_VUOTA', mostra 'BOBINA VUOTA'\n    fields.push({ label: 'Bobina Associata', value: 'BOBINA VUOTA' });\n  } else if (cavo.id_bobina === null) {\n    // Quando id_bobina è null (cavi non posati), non mostrare la bobina\n    // Non aggiungere il campo alla lista\n  }\n\n  // Add data_posa if available\n  if (cavo.data_posa) {\n    // Format the date\n    const date = new Date(cavo.data_posa);\n    const formattedDate = date.toLocaleDateString('it-IT', {\n      day: '2-digit',\n      month: '2-digit',\n      year: 'numeric'\n    });\n    fields.push({ label: 'Data Posa', value: formattedDate });\n  }\n\n  return (\n    <Paper sx={{ p: compact ? 2 : 3, mb: 3 }}>\n      <Typography variant={compact ? \"h6\" : \"h5\"} gutterBottom>\n        {title}\n      </Typography>\n\n      <Divider sx={{ mb: 2 }} />\n\n      <Grid container spacing={compact ? 1 : 2}>\n        {fields.map((field, index) => (\n          <Grid item xs={12} sm={compact ? 6 : 4} key={index}>\n            <Box sx={{ mb: compact ? 1 : 2 }}>\n              <Typography variant=\"caption\" color=\"text.secondary\">\n                {field.label}\n              </Typography>\n              {field.chip ? (\n                <Box sx={{ mt: 0.5 }}>\n                  <Chip\n                    label={formatValue(field.value)}\n                    color={field.chipColor || 'default'}\n                    size={compact ? \"small\" : \"medium\"}\n                  />\n                </Box>\n              ) : (\n                <Typography\n                  variant={field.primary ? \"subtitle1\" : \"body2\"}\n                  sx={{ fontWeight: field.primary ? 'bold' : 'regular' }}\n                >\n                  {formatValue(field.value)}\n                </Typography>\n              )}\n            </Box>\n          </Grid>\n        ))}\n      </Grid>\n    </Paper>\n  );\n};\n\nexport default CavoDetailsView;\n"], "mappings": ";AAAA,OAAOA,KAAK,MAAM,OAAO;AACzB,SACEC,GAAG,EACHC,KAAK,EACLC,UAAU,EACVC,IAAI,EACJC,OAAO,EACPC,IAAI,QACC,eAAe;AACtB,SAASC,kBAAkB,QAAQ,wBAAwB;;AAE3D;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AARA,SAAAC,MAAA,IAAAC,OAAA;AASA,MAAMC,eAAe,GAAGA,CAAC;EAAEC,IAAI;EAAEC,OAAO,GAAG,KAAK;EAAEC,KAAK,GAAG;AAAgB,CAAC,KAAK;EAC9E,IAAI,CAACF,IAAI,EAAE,OAAO,IAAI;;EAEtB;EACA,MAAMG,WAAW,GAAIC,KAAK,IAAK;IAC7B,IAAIA,KAAK,KAAK,IAAI,IAAIA,KAAK,KAAKC,SAAS,IAAID,KAAK,KAAK,EAAE,EAAE;MACzD,OAAO,KAAK;IACd;IACA,OAAOA,KAAK;EACd,CAAC;;EAED;EACA,MAAME,MAAM,GAAG,CACb;IAAEC,KAAK,EAAE,SAAS;IAAEH,KAAK,EAAEJ,IAAI,CAACQ,OAAO;IAAEC,OAAO,EAAE;EAAK,CAAC,EACxD;IAAEF,KAAK,EAAE,SAAS;IAAEH,KAAK,EAAEJ,IAAI,CAACU;EAAQ,CAAC,EACzC;IAAEH,KAAK,EAAE,SAAS;IAAEH,KAAK,EAAEJ,IAAI,CAACW;EAAQ,CAAC,EACzC;IAAEJ,KAAK,EAAE,aAAa;IAAEH,KAAK,EAAEJ,IAAI,CAACY;EAAY,CAAC,EACjD;IAAEL,KAAK,EAAE,WAAW;IAAEH,KAAK,EAAEJ,IAAI,CAACa;EAAU,CAAC,EAC7C;IAAEN,KAAK,EAAE,eAAe;IAAEH,KAAK,EAAEJ,IAAI,CAACc;EAAa,CAAC,EACpD;IAAEP,KAAK,EAAE,SAAS;IAAEH,KAAK,EAAEJ,IAAI,CAACe;EAAQ,CAAC,EACzC;IAAER,KAAK,EAAE,IAAI;IAAEH,KAAK,EAAEJ,IAAI,CAACgB;EAAG,CAAC,EAC/B;IAAET,KAAK,EAAE,qBAAqB;IAAEH,KAAK,EAAEJ,IAAI,CAACiB;EAAoB,CAAC,EACjE;IAAEV,KAAK,EAAE,iBAAiB;IAAEH,KAAK,EAAEJ,IAAI,CAACkB;EAAgB,CAAC,EACzD;IAAEX,KAAK,EAAE,6BAA6B;IAAEH,KAAK,EAAEJ,IAAI,CAACmB;EAA4B,CAAC,EACjF;IAAEZ,KAAK,EAAE,mBAAmB;IAAEH,KAAK,EAAEJ,IAAI,CAACoB;EAAkB,CAAC,EAC7D;IAAEb,KAAK,EAAE,eAAe;IAAEH,KAAK,EAAEJ,IAAI,CAACqB;EAAc,CAAC,EACrD;IAAEd,KAAK,EAAE,2BAA2B;IAAEH,KAAK,EAAEJ,IAAI,CAACsB;EAA0B,CAAC,EAC7E;IAAEf,KAAK,EAAE,eAAe;IAAEH,KAAK,EAAEJ,IAAI,CAACuB;EAAc,CAAC,EACrD;IACEhB,KAAK,EAAE,qBAAqB;IAC5BH,KAAK,EAAEJ,IAAI,CAACwB,mBAAmB;IAC/BC,IAAI,EAAE,IAAI;IACVC,SAAS,EAAE9B,kBAAkB,CAACI,IAAI,CAACwB,mBAAmB;EACxD,CAAC,CACF;;EAED;EACA,IAAIxB,IAAI,CAAC2B,eAAe,IAAIC,UAAU,CAAC5B,IAAI,CAAC2B,eAAe,CAAC,GAAG,CAAC,EAAE;IAChErB,MAAM,CAACuB,IAAI,CAAC;MAAEtB,KAAK,EAAE,iBAAiB;MAAEH,KAAK,EAAEJ,IAAI,CAAC2B,eAAe,GAAG;IAAK,CAAC,CAAC;EAC/E;;EAEA;EACA,IAAI3B,IAAI,CAAC8B,SAAS,IAAI9B,IAAI,CAAC8B,SAAS,KAAK,KAAK,EAAE;IAC9CxB,MAAM,CAACuB,IAAI,CAAC;MAAEtB,KAAK,EAAE,kBAAkB;MAAEH,KAAK,EAAEJ,IAAI,CAAC8B;IAAU,CAAC,CAAC;EACnE,CAAC,MAAM,IAAI9B,IAAI,CAAC8B,SAAS,KAAK,cAAc,EAAE;IAC5C;IACAxB,MAAM,CAACuB,IAAI,CAAC;MAAEtB,KAAK,EAAE,kBAAkB;MAAEH,KAAK,EAAE;IAAe,CAAC,CAAC;EACnE,CAAC,MAAM,IAAIJ,IAAI,CAAC8B,SAAS,KAAK,IAAI,EAAE;IAClC;IACA;EAAA;;EAGF;EACA,IAAI9B,IAAI,CAAC+B,SAAS,EAAE;IAClB;IACA,MAAMC,IAAI,GAAG,IAAIC,IAAI,CAACjC,IAAI,CAAC+B,SAAS,CAAC;IACrC,MAAMG,aAAa,GAAGF,IAAI,CAACG,kBAAkB,CAAC,OAAO,EAAE;MACrDC,GAAG,EAAE,SAAS;MACdC,KAAK,EAAE,SAAS;MAChBC,IAAI,EAAE;IACR,CAAC,CAAC;IACFhC,MAAM,CAACuB,IAAI,CAAC;MAAEtB,KAAK,EAAE,WAAW;MAAEH,KAAK,EAAE8B;IAAc,CAAC,CAAC;EAC3D;EAEA,oBACEpC,OAAA,CAACP,KAAK;IAACgD,EAAE,EAAE;MAAEC,CAAC,EAAEvC,OAAO,GAAG,CAAC,GAAG,CAAC;MAAEwC,EAAE,EAAE;IAAE,CAAE;IAAAC,QAAA,gBACvC5C,OAAA,CAACN,UAAU;MAACmD,OAAO,EAAE1C,OAAO,GAAG,IAAI,GAAG,IAAK;MAAC2C,YAAY;MAAAF,QAAA,EACrDxC;IAAK;MAAA2C,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACI,CAAC,eAEblD,OAAA,CAACJ,OAAO;MAAC6C,EAAE,EAAE;QAAEE,EAAE,EAAE;MAAE;IAAE;MAAAI,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC,eAE1BlD,OAAA,CAACL,IAAI;MAACwD,SAAS;MAACC,OAAO,EAAEjD,OAAO,GAAG,CAAC,GAAG,CAAE;MAAAyC,QAAA,EACtCpC,MAAM,CAAC6C,GAAG,CAAC,CAACC,KAAK,EAAEC,KAAK,kBACvBvD,OAAA,CAACL,IAAI;QAAC6D,IAAI;QAACC,EAAE,EAAE,EAAG;QAACC,EAAE,EAAEvD,OAAO,GAAG,CAAC,GAAG,CAAE;QAAAyC,QAAA,eACrC5C,OAAA,CAACR,GAAG;UAACiD,EAAE,EAAE;YAAEE,EAAE,EAAExC,OAAO,GAAG,CAAC,GAAG;UAAE,CAAE;UAAAyC,QAAA,gBAC/B5C,OAAA,CAACN,UAAU;YAACmD,OAAO,EAAC,SAAS;YAACc,KAAK,EAAC,gBAAgB;YAAAf,QAAA,EACjDU,KAAK,CAAC7C;UAAK;YAAAsC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACF,CAAC,EACZI,KAAK,CAAC3B,IAAI,gBACT3B,OAAA,CAACR,GAAG;YAACiD,EAAE,EAAE;cAAEmB,EAAE,EAAE;YAAI,CAAE;YAAAhB,QAAA,eACnB5C,OAAA,CAACH,IAAI;cACHY,KAAK,EAAEJ,WAAW,CAACiD,KAAK,CAAChD,KAAK,CAAE;cAChCqD,KAAK,EAAEL,KAAK,CAAC1B,SAAS,IAAI,SAAU;cACpCiC,IAAI,EAAE1D,OAAO,GAAG,OAAO,GAAG;YAAS;cAAA4C,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACpC;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACC,CAAC,gBAENlD,OAAA,CAACN,UAAU;YACTmD,OAAO,EAAES,KAAK,CAAC3C,OAAO,GAAG,WAAW,GAAG,OAAQ;YAC/C8B,EAAE,EAAE;cAAEqB,UAAU,EAAER,KAAK,CAAC3C,OAAO,GAAG,MAAM,GAAG;YAAU,CAAE;YAAAiC,QAAA,EAEtDvC,WAAW,CAACiD,KAAK,CAAChD,KAAK;UAAC;YAAAyC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACf,CACb;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACE;MAAC,GArBqCK,KAAK;QAAAR,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAsB5C,CACP;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACE,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACF,CAAC;AAEZ,CAAC;AAACa,EAAA,GArGI9D,eAAe;AAuGrB,eAAeA,eAAe;AAAC,IAAA8D,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}