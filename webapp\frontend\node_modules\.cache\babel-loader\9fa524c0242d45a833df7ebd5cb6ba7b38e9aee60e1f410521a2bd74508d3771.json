{"ast": null, "code": "import { defaultLocale } from \"./_lib/defaultLocale.mjs\";\nimport { getDefaultOptions } from \"./_lib/defaultOptions.mjs\";\nimport { getRoundingMethod } from \"./_lib/getRoundingMethod.mjs\";\nimport { getTimezoneOffsetInMilliseconds } from \"./_lib/getTimezoneOffsetInMilliseconds.mjs\";\nimport { compareAsc } from \"./compareAsc.mjs\";\nimport { millisecondsInMinute, minutesInDay, minutesInMonth, minutesInYear } from \"./constants.mjs\";\nimport { toDate } from \"./toDate.mjs\";\n\n/**\n * The {@link formatDistanceStrict} function options.\n */\n\n/**\n * The unit used to format the distance in {@link formatDistanceStrict}.\n */\n\n/**\n * @name formatDistanceStrict\n * @category Common Helpers\n * @summary Return the distance between the given dates in words.\n *\n * @description\n * Return the distance between the given dates in words, using strict units.\n * This is like `formatDistance`, but does not use helpers like 'almost', 'over',\n * 'less than' and the like.\n *\n * | Distance between dates | Result              |\n * |------------------------|---------------------|\n * | 0 ... 59 secs          | [0..59] seconds     |\n * | 1 ... 59 mins          | [1..59] minutes     |\n * | 1 ... 23 hrs           | [1..23] hours       |\n * | 1 ... 29 days          | [1..29] days        |\n * | 1 ... 11 months        | [1..11] months      |\n * | 1 ... N years          | [1..N]  years       |\n *\n * @typeParam DateType - The `Date` type, the function operates on. Gets inferred from passed arguments. Allows to use extensions like [`UTCDate`](https://github.com/date-fns/utc).\n *\n * @param date - The date\n * @param baseDate - The date to compare with\n * @param options - An object with options\n *\n * @returns The distance in words\n *\n * @throws `date` must not be Invalid Date\n * @throws `baseDate` must not be Invalid Date\n * @throws `options.unit` must be 'second', 'minute', 'hour', 'day', 'month' or 'year'\n * @throws `options.locale` must contain `formatDistance` property\n *\n * @example\n * // What is the distance between 2 July 2014 and 1 January 2015?\n * const result = formatDistanceStrict(new Date(2014, 6, 2), new Date(2015, 0, 2))\n * //=> '6 months'\n *\n * @example\n * // What is the distance between 1 January 2015 00:00:15\n * // and 1 January 2015 00:00:00?\n * const result = formatDistanceStrict(\n *   new Date(2015, 0, 1, 0, 0, 15),\n *   new Date(2015, 0, 1, 0, 0, 0)\n * )\n * //=> '15 seconds'\n *\n * @example\n * // What is the distance from 1 January 2016\n * // to 1 January 2015, with a suffix?\n * const result = formatDistanceStrict(new Date(2015, 0, 1), new Date(2016, 0, 1), {\n *   addSuffix: true\n * })\n * //=> '1 year ago'\n *\n * @example\n * // What is the distance from 1 January 2016\n * // to 1 January 2015, in minutes?\n * const result = formatDistanceStrict(new Date(2016, 0, 1), new Date(2015, 0, 1), {\n *   unit: 'minute'\n * })\n * //=> '525600 minutes'\n *\n * @example\n * // What is the distance from 1 January 2015\n * // to 28 January 2015, in months, rounded up?\n * const result = formatDistanceStrict(new Date(2015, 0, 28), new Date(2015, 0, 1), {\n *   unit: 'month',\n *   roundingMethod: 'ceil'\n * })\n * //=> '1 month'\n *\n * @example\n * // What is the distance between 1 August 2016 and 1 January 2015 in Esperanto?\n * import { eoLocale } from 'date-fns/locale/eo'\n * const result = formatDistanceStrict(new Date(2016, 7, 1), new Date(2015, 0, 1), {\n *   locale: eoLocale\n * })\n * //=> '1 jaro'\n */\n\nexport function formatDistanceStrict(date, baseDate, options) {\n  const defaultOptions = getDefaultOptions();\n  const locale = options?.locale ?? defaultOptions.locale ?? defaultLocale;\n  const comparison = compareAsc(date, baseDate);\n  if (isNaN(comparison)) {\n    throw new RangeError(\"Invalid time value\");\n  }\n  const localizeOptions = Object.assign({}, options, {\n    addSuffix: options?.addSuffix,\n    comparison: comparison\n  });\n  let dateLeft;\n  let dateRight;\n  if (comparison > 0) {\n    dateLeft = toDate(baseDate);\n    dateRight = toDate(date);\n  } else {\n    dateLeft = toDate(date);\n    dateRight = toDate(baseDate);\n  }\n  const roundingMethod = getRoundingMethod(options?.roundingMethod ?? \"round\");\n  const milliseconds = dateRight.getTime() - dateLeft.getTime();\n  const minutes = milliseconds / millisecondsInMinute;\n  const timezoneOffset = getTimezoneOffsetInMilliseconds(dateRight) - getTimezoneOffsetInMilliseconds(dateLeft);\n\n  // Use DST-normalized difference in minutes for years, months and days;\n  // use regular difference in minutes for hours, minutes and seconds.\n  const dstNormalizedMinutes = (milliseconds - timezoneOffset) / millisecondsInMinute;\n  const defaultUnit = options?.unit;\n  let unit;\n  if (!defaultUnit) {\n    if (minutes < 1) {\n      unit = \"second\";\n    } else if (minutes < 60) {\n      unit = \"minute\";\n    } else if (minutes < minutesInDay) {\n      unit = \"hour\";\n    } else if (dstNormalizedMinutes < minutesInMonth) {\n      unit = \"day\";\n    } else if (dstNormalizedMinutes < minutesInYear) {\n      unit = \"month\";\n    } else {\n      unit = \"year\";\n    }\n  } else {\n    unit = defaultUnit;\n  }\n\n  // 0 up to 60 seconds\n  if (unit === \"second\") {\n    const seconds = roundingMethod(milliseconds / 1000);\n    return locale.formatDistance(\"xSeconds\", seconds, localizeOptions);\n\n    // 1 up to 60 mins\n  } else if (unit === \"minute\") {\n    const roundedMinutes = roundingMethod(minutes);\n    return locale.formatDistance(\"xMinutes\", roundedMinutes, localizeOptions);\n\n    // 1 up to 24 hours\n  } else if (unit === \"hour\") {\n    const hours = roundingMethod(minutes / 60);\n    return locale.formatDistance(\"xHours\", hours, localizeOptions);\n\n    // 1 up to 30 days\n  } else if (unit === \"day\") {\n    const days = roundingMethod(dstNormalizedMinutes / minutesInDay);\n    return locale.formatDistance(\"xDays\", days, localizeOptions);\n\n    // 1 up to 12 months\n  } else if (unit === \"month\") {\n    const months = roundingMethod(dstNormalizedMinutes / minutesInMonth);\n    return months === 12 && defaultUnit !== \"month\" ? locale.formatDistance(\"xYears\", 1, localizeOptions) : locale.formatDistance(\"xMonths\", months, localizeOptions);\n\n    // 1 year up to max Date\n  } else {\n    const years = roundingMethod(dstNormalizedMinutes / minutesInYear);\n    return locale.formatDistance(\"xYears\", years, localizeOptions);\n  }\n}\n\n// Fallback for modularized imports:\nexport default formatDistanceStrict;", "map": {"version": 3, "names": ["defaultLocale", "getDefaultOptions", "getRoundingMethod", "getTimezoneOffsetInMilliseconds", "compareAsc", "millisecondsInMinute", "minutesInDay", "minutesInMonth", "minutesInYear", "toDate", "formatDistanceStrict", "date", "baseDate", "options", "defaultOptions", "locale", "comparison", "isNaN", "RangeError", "localizeOptions", "Object", "assign", "addSuffix", "dateLeft", "dateRight", "roundingMethod", "milliseconds", "getTime", "minutes", "timezoneOffset", "dstNormalizedMinutes", "defaultUnit", "unit", "seconds", "formatDistance", "roundedMinutes", "hours", "days", "months", "years"], "sources": ["C:/CMS/webapp/frontend/node_modules/date-fns/formatDistanceStrict.mjs"], "sourcesContent": ["import { defaultLocale } from \"./_lib/defaultLocale.mjs\";\nimport { getDefaultOptions } from \"./_lib/defaultOptions.mjs\";\nimport { getRoundingMethod } from \"./_lib/getRoundingMethod.mjs\";\nimport { getTimezoneOffsetInMilliseconds } from \"./_lib/getTimezoneOffsetInMilliseconds.mjs\";\nimport { compareAsc } from \"./compareAsc.mjs\";\nimport {\n  millisecondsInMinute,\n  minutesInDay,\n  minutesInMonth,\n  minutesInYear,\n} from \"./constants.mjs\";\nimport { toDate } from \"./toDate.mjs\";\n\n/**\n * The {@link formatDistanceStrict} function options.\n */\n\n/**\n * The unit used to format the distance in {@link formatDistanceStrict}.\n */\n\n/**\n * @name formatDistanceStrict\n * @category Common Helpers\n * @summary Return the distance between the given dates in words.\n *\n * @description\n * Return the distance between the given dates in words, using strict units.\n * This is like `formatDistance`, but does not use helpers like 'almost', 'over',\n * 'less than' and the like.\n *\n * | Distance between dates | Result              |\n * |------------------------|---------------------|\n * | 0 ... 59 secs          | [0..59] seconds     |\n * | 1 ... 59 mins          | [1..59] minutes     |\n * | 1 ... 23 hrs           | [1..23] hours       |\n * | 1 ... 29 days          | [1..29] days        |\n * | 1 ... 11 months        | [1..11] months      |\n * | 1 ... N years          | [1..N]  years       |\n *\n * @typeParam DateType - The `Date` type, the function operates on. Gets inferred from passed arguments. Allows to use extensions like [`UTCDate`](https://github.com/date-fns/utc).\n *\n * @param date - The date\n * @param baseDate - The date to compare with\n * @param options - An object with options\n *\n * @returns The distance in words\n *\n * @throws `date` must not be Invalid Date\n * @throws `baseDate` must not be Invalid Date\n * @throws `options.unit` must be 'second', 'minute', 'hour', 'day', 'month' or 'year'\n * @throws `options.locale` must contain `formatDistance` property\n *\n * @example\n * // What is the distance between 2 July 2014 and 1 January 2015?\n * const result = formatDistanceStrict(new Date(2014, 6, 2), new Date(2015, 0, 2))\n * //=> '6 months'\n *\n * @example\n * // What is the distance between 1 January 2015 00:00:15\n * // and 1 January 2015 00:00:00?\n * const result = formatDistanceStrict(\n *   new Date(2015, 0, 1, 0, 0, 15),\n *   new Date(2015, 0, 1, 0, 0, 0)\n * )\n * //=> '15 seconds'\n *\n * @example\n * // What is the distance from 1 January 2016\n * // to 1 January 2015, with a suffix?\n * const result = formatDistanceStrict(new Date(2015, 0, 1), new Date(2016, 0, 1), {\n *   addSuffix: true\n * })\n * //=> '1 year ago'\n *\n * @example\n * // What is the distance from 1 January 2016\n * // to 1 January 2015, in minutes?\n * const result = formatDistanceStrict(new Date(2016, 0, 1), new Date(2015, 0, 1), {\n *   unit: 'minute'\n * })\n * //=> '525600 minutes'\n *\n * @example\n * // What is the distance from 1 January 2015\n * // to 28 January 2015, in months, rounded up?\n * const result = formatDistanceStrict(new Date(2015, 0, 28), new Date(2015, 0, 1), {\n *   unit: 'month',\n *   roundingMethod: 'ceil'\n * })\n * //=> '1 month'\n *\n * @example\n * // What is the distance between 1 August 2016 and 1 January 2015 in Esperanto?\n * import { eoLocale } from 'date-fns/locale/eo'\n * const result = formatDistanceStrict(new Date(2016, 7, 1), new Date(2015, 0, 1), {\n *   locale: eoLocale\n * })\n * //=> '1 jaro'\n */\n\nexport function formatDistanceStrict(date, baseDate, options) {\n  const defaultOptions = getDefaultOptions();\n  const locale = options?.locale ?? defaultOptions.locale ?? defaultLocale;\n\n  const comparison = compareAsc(date, baseDate);\n\n  if (isNaN(comparison)) {\n    throw new RangeError(\"Invalid time value\");\n  }\n\n  const localizeOptions = Object.assign({}, options, {\n    addSuffix: options?.addSuffix,\n    comparison: comparison,\n  });\n\n  let dateLeft;\n  let dateRight;\n  if (comparison > 0) {\n    dateLeft = toDate(baseDate);\n    dateRight = toDate(date);\n  } else {\n    dateLeft = toDate(date);\n    dateRight = toDate(baseDate);\n  }\n\n  const roundingMethod = getRoundingMethod(options?.roundingMethod ?? \"round\");\n\n  const milliseconds = dateRight.getTime() - dateLeft.getTime();\n  const minutes = milliseconds / millisecondsInMinute;\n\n  const timezoneOffset =\n    getTimezoneOffsetInMilliseconds(dateRight) -\n    getTimezoneOffsetInMilliseconds(dateLeft);\n\n  // Use DST-normalized difference in minutes for years, months and days;\n  // use regular difference in minutes for hours, minutes and seconds.\n  const dstNormalizedMinutes =\n    (milliseconds - timezoneOffset) / millisecondsInMinute;\n\n  const defaultUnit = options?.unit;\n  let unit;\n  if (!defaultUnit) {\n    if (minutes < 1) {\n      unit = \"second\";\n    } else if (minutes < 60) {\n      unit = \"minute\";\n    } else if (minutes < minutesInDay) {\n      unit = \"hour\";\n    } else if (dstNormalizedMinutes < minutesInMonth) {\n      unit = \"day\";\n    } else if (dstNormalizedMinutes < minutesInYear) {\n      unit = \"month\";\n    } else {\n      unit = \"year\";\n    }\n  } else {\n    unit = defaultUnit;\n  }\n\n  // 0 up to 60 seconds\n  if (unit === \"second\") {\n    const seconds = roundingMethod(milliseconds / 1000);\n    return locale.formatDistance(\"xSeconds\", seconds, localizeOptions);\n\n    // 1 up to 60 mins\n  } else if (unit === \"minute\") {\n    const roundedMinutes = roundingMethod(minutes);\n    return locale.formatDistance(\"xMinutes\", roundedMinutes, localizeOptions);\n\n    // 1 up to 24 hours\n  } else if (unit === \"hour\") {\n    const hours = roundingMethod(minutes / 60);\n    return locale.formatDistance(\"xHours\", hours, localizeOptions);\n\n    // 1 up to 30 days\n  } else if (unit === \"day\") {\n    const days = roundingMethod(dstNormalizedMinutes / minutesInDay);\n    return locale.formatDistance(\"xDays\", days, localizeOptions);\n\n    // 1 up to 12 months\n  } else if (unit === \"month\") {\n    const months = roundingMethod(dstNormalizedMinutes / minutesInMonth);\n    return months === 12 && defaultUnit !== \"month\"\n      ? locale.formatDistance(\"xYears\", 1, localizeOptions)\n      : locale.formatDistance(\"xMonths\", months, localizeOptions);\n\n    // 1 year up to max Date\n  } else {\n    const years = roundingMethod(dstNormalizedMinutes / minutesInYear);\n    return locale.formatDistance(\"xYears\", years, localizeOptions);\n  }\n}\n\n// Fallback for modularized imports:\nexport default formatDistanceStrict;\n"], "mappings": "AAAA,SAASA,aAAa,QAAQ,0BAA0B;AACxD,SAASC,iBAAiB,QAAQ,2BAA2B;AAC7D,SAASC,iBAAiB,QAAQ,8BAA8B;AAChE,SAASC,+BAA+B,QAAQ,4CAA4C;AAC5F,SAASC,UAAU,QAAQ,kBAAkB;AAC7C,SACEC,oBAAoB,EACpBC,YAAY,EACZC,cAAc,EACdC,aAAa,QACR,iBAAiB;AACxB,SAASC,MAAM,QAAQ,cAAc;;AAErC;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA,OAAO,SAASC,oBAAoBA,CAACC,IAAI,EAAEC,QAAQ,EAAEC,OAAO,EAAE;EAC5D,MAAMC,cAAc,GAAGb,iBAAiB,CAAC,CAAC;EAC1C,MAAMc,MAAM,GAAGF,OAAO,EAAEE,MAAM,IAAID,cAAc,CAACC,MAAM,IAAIf,aAAa;EAExE,MAAMgB,UAAU,GAAGZ,UAAU,CAACO,IAAI,EAAEC,QAAQ,CAAC;EAE7C,IAAIK,KAAK,CAACD,UAAU,CAAC,EAAE;IACrB,MAAM,IAAIE,UAAU,CAAC,oBAAoB,CAAC;EAC5C;EAEA,MAAMC,eAAe,GAAGC,MAAM,CAACC,MAAM,CAAC,CAAC,CAAC,EAAER,OAAO,EAAE;IACjDS,SAAS,EAAET,OAAO,EAAES,SAAS;IAC7BN,UAAU,EAAEA;EACd,CAAC,CAAC;EAEF,IAAIO,QAAQ;EACZ,IAAIC,SAAS;EACb,IAAIR,UAAU,GAAG,CAAC,EAAE;IAClBO,QAAQ,GAAGd,MAAM,CAACG,QAAQ,CAAC;IAC3BY,SAAS,GAAGf,MAAM,CAACE,IAAI,CAAC;EAC1B,CAAC,MAAM;IACLY,QAAQ,GAAGd,MAAM,CAACE,IAAI,CAAC;IACvBa,SAAS,GAAGf,MAAM,CAACG,QAAQ,CAAC;EAC9B;EAEA,MAAMa,cAAc,GAAGvB,iBAAiB,CAACW,OAAO,EAAEY,cAAc,IAAI,OAAO,CAAC;EAE5E,MAAMC,YAAY,GAAGF,SAAS,CAACG,OAAO,CAAC,CAAC,GAAGJ,QAAQ,CAACI,OAAO,CAAC,CAAC;EAC7D,MAAMC,OAAO,GAAGF,YAAY,GAAGrB,oBAAoB;EAEnD,MAAMwB,cAAc,GAClB1B,+BAA+B,CAACqB,SAAS,CAAC,GAC1CrB,+BAA+B,CAACoB,QAAQ,CAAC;;EAE3C;EACA;EACA,MAAMO,oBAAoB,GACxB,CAACJ,YAAY,GAAGG,cAAc,IAAIxB,oBAAoB;EAExD,MAAM0B,WAAW,GAAGlB,OAAO,EAAEmB,IAAI;EACjC,IAAIA,IAAI;EACR,IAAI,CAACD,WAAW,EAAE;IAChB,IAAIH,OAAO,GAAG,CAAC,EAAE;MACfI,IAAI,GAAG,QAAQ;IACjB,CAAC,MAAM,IAAIJ,OAAO,GAAG,EAAE,EAAE;MACvBI,IAAI,GAAG,QAAQ;IACjB,CAAC,MAAM,IAAIJ,OAAO,GAAGtB,YAAY,EAAE;MACjC0B,IAAI,GAAG,MAAM;IACf,CAAC,MAAM,IAAIF,oBAAoB,GAAGvB,cAAc,EAAE;MAChDyB,IAAI,GAAG,KAAK;IACd,CAAC,MAAM,IAAIF,oBAAoB,GAAGtB,aAAa,EAAE;MAC/CwB,IAAI,GAAG,OAAO;IAChB,CAAC,MAAM;MACLA,IAAI,GAAG,MAAM;IACf;EACF,CAAC,MAAM;IACLA,IAAI,GAAGD,WAAW;EACpB;;EAEA;EACA,IAAIC,IAAI,KAAK,QAAQ,EAAE;IACrB,MAAMC,OAAO,GAAGR,cAAc,CAACC,YAAY,GAAG,IAAI,CAAC;IACnD,OAAOX,MAAM,CAACmB,cAAc,CAAC,UAAU,EAAED,OAAO,EAAEd,eAAe,CAAC;;IAElE;EACF,CAAC,MAAM,IAAIa,IAAI,KAAK,QAAQ,EAAE;IAC5B,MAAMG,cAAc,GAAGV,cAAc,CAACG,OAAO,CAAC;IAC9C,OAAOb,MAAM,CAACmB,cAAc,CAAC,UAAU,EAAEC,cAAc,EAAEhB,eAAe,CAAC;;IAEzE;EACF,CAAC,MAAM,IAAIa,IAAI,KAAK,MAAM,EAAE;IAC1B,MAAMI,KAAK,GAAGX,cAAc,CAACG,OAAO,GAAG,EAAE,CAAC;IAC1C,OAAOb,MAAM,CAACmB,cAAc,CAAC,QAAQ,EAAEE,KAAK,EAAEjB,eAAe,CAAC;;IAE9D;EACF,CAAC,MAAM,IAAIa,IAAI,KAAK,KAAK,EAAE;IACzB,MAAMK,IAAI,GAAGZ,cAAc,CAACK,oBAAoB,GAAGxB,YAAY,CAAC;IAChE,OAAOS,MAAM,CAACmB,cAAc,CAAC,OAAO,EAAEG,IAAI,EAAElB,eAAe,CAAC;;IAE5D;EACF,CAAC,MAAM,IAAIa,IAAI,KAAK,OAAO,EAAE;IAC3B,MAAMM,MAAM,GAAGb,cAAc,CAACK,oBAAoB,GAAGvB,cAAc,CAAC;IACpE,OAAO+B,MAAM,KAAK,EAAE,IAAIP,WAAW,KAAK,OAAO,GAC3ChB,MAAM,CAACmB,cAAc,CAAC,QAAQ,EAAE,CAAC,EAAEf,eAAe,CAAC,GACnDJ,MAAM,CAACmB,cAAc,CAAC,SAAS,EAAEI,MAAM,EAAEnB,eAAe,CAAC;;IAE7D;EACF,CAAC,MAAM;IACL,MAAMoB,KAAK,GAAGd,cAAc,CAACK,oBAAoB,GAAGtB,aAAa,CAAC;IAClE,OAAOO,MAAM,CAACmB,cAAc,CAAC,QAAQ,EAAEK,KAAK,EAAEpB,eAAe,CAAC;EAChE;AACF;;AAEA;AACA,eAAeT,oBAAoB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}