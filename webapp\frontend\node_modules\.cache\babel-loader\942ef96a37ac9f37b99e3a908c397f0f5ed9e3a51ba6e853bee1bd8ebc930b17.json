{"ast": null, "code": "'use client';\n\nexport { default } from './FormLabel';\nexport * from './FormLabel';\nexport { default as formLabelClasses } from './formLabelClasses';\nexport * from './formLabelClasses';", "map": {"version": 3, "names": ["default", "formLabelClasses"], "sources": ["C:/Users/<USER>/PycharmProjects/CMS/webapp/frontend/node_modules/@mui/material/FormLabel/index.js"], "sourcesContent": ["'use client';\n\nexport { default } from './FormLabel';\nexport * from './FormLabel';\nexport { default as formLabelClasses } from './formLabelClasses';\nexport * from './formLabelClasses';"], "mappings": "AAAA,YAAY;;AAEZ,SAASA,OAAO,QAAQ,aAAa;AACrC,cAAc,aAAa;AAC3B,SAASA,OAAO,IAAIC,gBAAgB,QAAQ,oBAAoB;AAChE,cAAc,oBAAoB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}