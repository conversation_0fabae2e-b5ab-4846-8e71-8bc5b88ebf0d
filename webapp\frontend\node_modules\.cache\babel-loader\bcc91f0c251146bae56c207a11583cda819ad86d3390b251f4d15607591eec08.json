{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\PycharmProjects\\\\CMS\\\\webapp\\\\frontend\\\\src\\\\components\\\\cavi\\\\ParcoCavi.js\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect } from 'react';\nimport { Box, Typography, Button, Paper, Grid, Card, CardContent, CardActions, Dialog, DialogTitle, DialogContent, DialogActions, TextField, FormControl, InputLabel, Select, MenuItem, List, ListItem, ListItemText, ListItemIcon, ListItemButton, Divider, Alert, CircularProgress, Table, TableBody, TableCell, TableContainer, TableHead, TableRow } from '@mui/material';\nimport { Add as AddIcon, Edit as EditIcon, Delete as DeleteIcon, History as HistoryIcon, Save as SaveIcon, ViewList as ViewListIcon } from '@mui/icons-material';\nimport parcoCaviService from '../../services/parcoCaviService';\nimport ConfigurazioneDialog from './ConfigurazioneDialog';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst ParcoCavi = ({\n  cantiereId,\n  onSuccess,\n  onError,\n  initialOption = null\n}) => {\n  _s();\n  const [loading, setLoading] = useState(false);\n  const [bobine, setBobine] = useState([]);\n  const [selectedOption, setSelectedOption] = useState(initialOption);\n  const [openDialog, setOpenDialog] = useState(false);\n  const [dialogType, setDialogType] = useState('');\n  const [selectedBobina, setSelectedBobina] = useState(null);\n  const [formData, setFormData] = useState({\n    numero_bobina: '',\n    utility: '',\n    tipologia: '',\n    n_conduttori: '',\n    sezione: '',\n    metri_totali: '',\n    metri_residui: '',\n    stato_bobina: 'Disponibile',\n    ubicazione_bobina: '',\n    fornitore: '',\n    n_DDT: '',\n    data_DDT: '',\n    configurazione: ''\n  });\n  const [storicoUtilizzo, setStoricoUtilizzo] = useState([]);\n  const [openConfigDialog, setOpenConfigDialog] = useState(false);\n  const [isFirstInsertion, setIsFirstInsertion] = useState(false);\n\n  // Carica le bobine disponibili\n  const loadBobine = async () => {\n    try {\n      setLoading(true);\n      const data = await parcoCaviService.getBobine(cantiereId);\n      setBobine(data);\n    } catch (error) {\n      onError('Errore nel caricamento delle bobine');\n      console.error('Errore nel caricamento delle bobine:', error);\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  // Carica lo storico utilizzo bobine\n  const loadStoricoUtilizzo = async () => {\n    try {\n      setLoading(true);\n      const data = await parcoCaviService.getStoricoUtilizzo(cantiereId);\n      setStoricoUtilizzo(data);\n    } catch (error) {\n      onError('Errore nel caricamento dello storico utilizzo');\n      console.error('Errore nel caricamento dello storico utilizzo:', error);\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  // Carica i dati all'avvio del componente e gestisce l'opzione iniziale\n  useEffect(() => {\n    if (initialOption) {\n      handleOptionSelect(initialOption);\n    } else {\n      loadBobine();\n    }\n  }, [cantiereId, initialOption]);\n\n  // Verifica se è il primo inserimento di una bobina per un cantiere\n  const checkIfFirstInsertion = async () => {\n    try {\n      setLoading(true);\n      const isFirst = await parcoCaviService.isFirstBobinaInsertion(cantiereId);\n      setIsFirstInsertion(isFirst);\n      if (isFirst) {\n        // Se è il primo inserimento, mostra il dialog di configurazione\n        setOpenConfigDialog(true);\n      } else {\n        // Non è il primo inserimento, procedi con il form normale\n        setDialogType('creaBobina');\n        setFormData({\n          numero_bobina: '',\n          utility: '',\n          tipologia: '',\n          n_conduttori: '',\n          sezione: '',\n          metri_totali: '',\n          metri_residui: '',\n          stato_bobina: 'Disponibile',\n          ubicazione_bobina: '',\n          fornitore: '',\n          n_DDT: '',\n          data_DDT: '',\n          configurazione: ''\n        });\n        setOpenDialog(true);\n      }\n    } catch (error) {\n      onError('Errore nel controllo dell\\'inserimento della prima bobina');\n      console.error('Errore nel controllo dell\\'inserimento della prima bobina:', error);\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  // Gestisce la conferma della configurazione\n  const handleConfigConfirm = configValue => {\n    console.log('Configurazione selezionata:', configValue);\n    setOpenConfigDialog(false);\n\n    // Imposta i valori di default per la bobina\n    const defaultFormData = {\n      numero_bobina: configValue === 's' ? '1' : '',\n      // Se usiamo numeri progressivi, imposta il default a 1\n      utility: '',\n      tipologia: '',\n      n_conduttori: '',\n      sezione: '',\n      metri_totali: '',\n      metri_residui: '',\n      stato_bobina: 'Disponibile',\n      ubicazione_bobina: '',\n      fornitore: '',\n      n_DDT: '',\n      data_DDT: '',\n      configurazione: configValue // Imposta la configurazione scelta\n    };\n    setFormData(defaultFormData);\n    setDialogType('creaBobina');\n    setOpenDialog(true);\n  };\n\n  // Gestisce la selezione di un'opzione dal menu\n  const handleOptionSelect = option => {\n    setSelectedOption(option);\n    if (option === 'visualizzaBobine') {\n      loadBobine();\n    } else if (option === 'creaBobina') {\n      checkIfFirstInsertion();\n    } else if (option === 'modificaBobina') {\n      loadBobine();\n      setDialogType('selezionaBobina');\n      setOpenDialog(true);\n    } else if (option === 'eliminaBobina') {\n      loadBobine();\n      setDialogType('eliminaBobina');\n      setOpenDialog(true);\n    } else if (option === 'visualizzaStorico') {\n      loadStoricoUtilizzo();\n      setDialogType('visualizzaStorico');\n      setOpenDialog(true);\n    }\n  };\n\n  // Gestisce la chiusura del dialog\n  const handleCloseDialog = () => {\n    setOpenDialog(false);\n    setSelectedBobina(null);\n    setFormData({\n      numero_bobina: '',\n      utility: '',\n      tipologia: '',\n      n_conduttori: '',\n      sezione: '',\n      metri_totali: '',\n      metri_residui: '',\n      stato_bobina: 'Disponibile',\n      ubicazione_bobina: '',\n      fornitore: '',\n      n_DDT: '',\n      data_DDT: '',\n      configurazione: ''\n    });\n  };\n\n  // Gestisce la selezione di una bobina\n  const handleBobinaSelect = bobina => {\n    setSelectedBobina(bobina);\n    if (dialogType === 'selezionaBobina') {\n      setDialogType('modificaBobina');\n      setFormData({\n        numero_bobina: bobina.numero_bobina,\n        utility: bobina.utility || '',\n        tipologia: bobina.tipologia || '',\n        n_conduttori: bobina.n_conduttori || '',\n        sezione: bobina.sezione || '',\n        metri_totali: bobina.metri_totali || '',\n        metri_residui: bobina.metri_residui || '',\n        stato_bobina: bobina.stato_bobina || 'DISPONIBILE',\n        ubicazione_bobina: bobina.ubicazione_bobina || '',\n        fornitore: bobina.fornitore || '',\n        n_DDT: bobina.n_DDT || '',\n        data_DDT: bobina.data_DDT || '',\n        configurazione: bobina.configurazione || ''\n      });\n    }\n  };\n\n  // Gestisce il cambio dei valori nel form\n  const handleFormChange = e => {\n    const {\n      name,\n      value\n    } = e.target;\n    setFormData({\n      ...formData,\n      [name]: value\n    });\n  };\n\n  // Gestisce il salvataggio del form\n  const handleSave = async () => {\n    try {\n      setLoading(true);\n      if (dialogType === 'creaBobina') {\n        // Prepara i dati per la creazione della bobina\n        const bobinaData = {\n          ...formData,\n          // Converti i campi numerici da stringa a numero\n          metri_totali: parseFloat(formData.metri_totali),\n          metri_residui: parseFloat(formData.metri_totali),\n          // Inizialmente i metri residui sono uguali ai metri totali\n          // Assicurati che la configurazione sia impostata correttamente\n          configurazione: formData.configurazione || 's'\n        };\n\n        // Log per debug\n        console.log('Dati bobina da inviare:', bobinaData);\n\n        // Invia i dati al backend\n        await parcoCaviService.createBobina(cantiereId, bobinaData);\n        onSuccess('Bobina creata con successo');\n\n        // Ricarica le bobine e chiudi il dialog\n        loadBobine();\n        handleCloseDialog();\n      } else if (dialogType === 'modificaBobina') {\n        // Per la modifica, usa l'ID bobina completo o il numero bobina\n        const bobinaId = selectedBobina.id_bobina || formData.numero_bobina;\n        await parcoCaviService.updateBobina(cantiereId, bobinaId, formData);\n        onSuccess('Bobina modificata con successo');\n      } else if (dialogType === 'eliminaBobina') {\n        // Per l'eliminazione, usa l'ID bobina completo\n        const bobinaId = selectedBobina.id_bobina || selectedBobina.numero_bobina;\n        await parcoCaviService.deleteBobina(cantiereId, bobinaId);\n        onSuccess('Bobina eliminata con successo');\n      }\n      handleCloseDialog();\n      loadBobine(); // Ricarica le bobine dopo l'operazione\n    } catch (error) {\n      onError('Errore durante l\\'operazione: ' + (error.detail || error.message || 'Errore sconosciuto'));\n      console.error('Errore durante l\\'operazione:', error);\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  // Renderizza le bobine in formato card\n  const renderBobineCards = () => {\n    if (bobine.length === 0) {\n      return /*#__PURE__*/_jsxDEV(Alert, {\n        severity: \"info\",\n        children: \"Nessuna bobina disponibile\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 302,\n        columnNumber: 9\n      }, this);\n    }\n    return /*#__PURE__*/_jsxDEV(Grid, {\n      container: true,\n      spacing: 2,\n      children: bobine.map(bobina => /*#__PURE__*/_jsxDEV(Grid, {\n        item: true,\n        xs: 12,\n        sm: 6,\n        md: 4,\n        children: /*#__PURE__*/_jsxDEV(Card, {\n          children: [/*#__PURE__*/_jsxDEV(CardContent, {\n            children: [/*#__PURE__*/_jsxDEV(Typography, {\n              variant: \"h6\",\n              component: \"div\",\n              children: [\"Bobina: \", bobina.numero_bobina]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 312,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(Typography, {\n              variant: \"body2\",\n              color: \"text.secondary\",\n              children: [\"Utility: \", bobina.utility || 'N/A']\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 315,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(Typography, {\n              variant: \"body2\",\n              color: \"text.secondary\",\n              children: [\"Tipologia: \", bobina.tipologia || 'N/A']\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 318,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(Typography, {\n              variant: \"body2\",\n              color: \"text.secondary\",\n              children: [\"N\\xB0 Conduttori: \", bobina.n_conduttori || 'N/A']\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 321,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(Typography, {\n              variant: \"body2\",\n              color: \"text.secondary\",\n              children: [\"Sezione: \", bobina.sezione || 'N/A']\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 324,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(Typography, {\n              variant: \"body2\",\n              color: \"text.secondary\",\n              children: [\"Metri totali: \", bobina.metri_totali || 'N/A', \" m\"]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 327,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(Typography, {\n              variant: \"body2\",\n              color: \"text.secondary\",\n              children: [\"Metri residui: \", bobina.metri_residui || 'N/A', \" m\"]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 330,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(Typography, {\n              variant: \"body2\",\n              color: \"text.secondary\",\n              children: [\"Stato: \", bobina.stato_bobina || 'DISPONIBILE']\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 333,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(Typography, {\n              variant: \"body2\",\n              color: \"text.secondary\",\n              children: [\"Ubicazione: \", bobina.ubicazione_bobina || 'N/A']\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 336,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(Typography, {\n              variant: \"body2\",\n              color: \"text.secondary\",\n              children: [\"Fornitore: \", bobina.fornitore || 'N/A']\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 339,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 311,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(CardActions, {\n            children: [/*#__PURE__*/_jsxDEV(Button, {\n              size: \"small\",\n              startIcon: /*#__PURE__*/_jsxDEV(EditIcon, {}, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 346,\n                columnNumber: 30\n              }, this),\n              onClick: () => {\n                setDialogType('selezionaBobina');\n                handleBobinaSelect(bobina);\n              },\n              children: \"Modifica\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 344,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(Button, {\n              size: \"small\",\n              color: \"error\",\n              startIcon: /*#__PURE__*/_jsxDEV(DeleteIcon, {}, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 357,\n                columnNumber: 30\n              }, this),\n              onClick: () => {\n                setDialogType('eliminaBobina');\n                setSelectedBobina(bobina);\n                setOpenDialog(true);\n              },\n              children: \"Elimina\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 354,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 343,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 310,\n          columnNumber: 13\n        }, this)\n      }, bobina.numero_bobina, false, {\n        fileName: _jsxFileName,\n        lineNumber: 309,\n        columnNumber: 11\n      }, this))\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 307,\n      columnNumber: 7\n    }, this);\n  };\n\n  // Renderizza il dialog in base al tipo\n  const renderDialog = () => {\n    if (dialogType === 'creaBobina' || dialogType === 'modificaBobina') {\n      return /*#__PURE__*/_jsxDEV(Dialog, {\n        open: openDialog,\n        onClose: handleCloseDialog,\n        maxWidth: \"md\",\n        fullWidth: true,\n        children: [/*#__PURE__*/_jsxDEV(DialogTitle, {\n          children: dialogType === 'creaBobina' ? 'Crea Nuova Bobina' : 'Modifica Bobina'\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 379,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(DialogContent, {\n          children: /*#__PURE__*/_jsxDEV(Grid, {\n            container: true,\n            spacing: 2,\n            sx: {\n              mt: 1\n            },\n            children: [/*#__PURE__*/_jsxDEV(Grid, {\n              item: true,\n              xs: 12,\n              sm: 6,\n              children: /*#__PURE__*/_jsxDEV(TextField, {\n                name: \"numero_bobina\",\n                label: \"Numero Bobina\",\n                fullWidth: true,\n                variant: \"outlined\",\n                value: formData.numero_bobina,\n                onChange: handleFormChange,\n                disabled: dialogType === 'modificaBobina' || dialogType === 'creaBobina' && formData.configurazione === 's',\n                required: true,\n                helperText: dialogType === 'creaBobina' && formData.configurazione === 's' ? 'Numero generato automaticamente' : ''\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 385,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 384,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Grid, {\n              item: true,\n              xs: 12,\n              sm: 6,\n              children: /*#__PURE__*/_jsxDEV(TextField, {\n                name: \"utility\",\n                label: \"Utility\",\n                fullWidth: true,\n                variant: \"outlined\",\n                value: formData.utility,\n                onChange: handleFormChange,\n                required: true\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 398,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 397,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Grid, {\n              item: true,\n              xs: 12,\n              sm: 6,\n              children: /*#__PURE__*/_jsxDEV(TextField, {\n                name: \"tipologia\",\n                label: \"Tipologia\",\n                fullWidth: true,\n                variant: \"outlined\",\n                value: formData.tipologia,\n                onChange: handleFormChange,\n                required: true\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 409,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 408,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Grid, {\n              item: true,\n              xs: 12,\n              sm: 6,\n              children: /*#__PURE__*/_jsxDEV(TextField, {\n                name: \"n_conduttori\",\n                label: \"N\\xB0 Conduttori\",\n                fullWidth: true,\n                variant: \"outlined\",\n                value: formData.n_conduttori,\n                onChange: handleFormChange,\n                required: true\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 420,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 419,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Grid, {\n              item: true,\n              xs: 12,\n              sm: 6,\n              children: /*#__PURE__*/_jsxDEV(TextField, {\n                name: \"sezione\",\n                label: \"Sezione\",\n                fullWidth: true,\n                variant: \"outlined\",\n                value: formData.sezione,\n                onChange: handleFormChange,\n                required: true\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 431,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 430,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Grid, {\n              item: true,\n              xs: 12,\n              sm: 6,\n              children: /*#__PURE__*/_jsxDEV(TextField, {\n                name: \"metri_totali\",\n                label: \"Metri Totali\",\n                type: \"number\",\n                fullWidth: true,\n                variant: \"outlined\",\n                value: formData.metri_totali,\n                onChange: handleFormChange,\n                required: true\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 442,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 441,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Grid, {\n              item: true,\n              xs: 12,\n              sm: 6,\n              children: /*#__PURE__*/_jsxDEV(TextField, {\n                name: \"metri_residui\",\n                label: \"Metri Residui\",\n                type: \"number\",\n                fullWidth: true,\n                variant: \"outlined\",\n                value: formData.metri_residui,\n                onChange: handleFormChange,\n                required: true\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 454,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 453,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Grid, {\n              item: true,\n              xs: 12,\n              sm: 6,\n              children: /*#__PURE__*/_jsxDEV(FormControl, {\n                fullWidth: true,\n                children: [/*#__PURE__*/_jsxDEV(InputLabel, {\n                  id: \"stato-bobina-label\",\n                  children: \"Stato Bobina\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 467,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(Select, {\n                  labelId: \"stato-bobina-label\",\n                  name: \"stato_bobina\",\n                  value: formData.stato_bobina,\n                  label: \"Stato Bobina\",\n                  onChange: handleFormChange,\n                  children: [/*#__PURE__*/_jsxDEV(MenuItem, {\n                    value: \"Disponibile\",\n                    children: \"Disponibile\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 475,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(MenuItem, {\n                    value: \"In uso\",\n                    children: \"In uso\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 476,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(MenuItem, {\n                    value: \"Terminata\",\n                    children: \"Terminata\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 477,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(MenuItem, {\n                    value: \"Danneggiata\",\n                    children: \"Danneggiata\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 478,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(MenuItem, {\n                    value: \"Over\",\n                    children: \"Over\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 479,\n                    columnNumber: 21\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 468,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 466,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 465,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Grid, {\n              item: true,\n              xs: 12,\n              sm: 6,\n              children: /*#__PURE__*/_jsxDEV(TextField, {\n                name: \"ubicazione_bobina\",\n                label: \"Ubicazione Bobina\",\n                fullWidth: true,\n                variant: \"outlined\",\n                value: formData.ubicazione_bobina,\n                onChange: handleFormChange\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 484,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 483,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Grid, {\n              item: true,\n              xs: 12,\n              sm: 6,\n              children: /*#__PURE__*/_jsxDEV(TextField, {\n                name: \"fornitore\",\n                label: \"Fornitore\",\n                fullWidth: true,\n                variant: \"outlined\",\n                value: formData.fornitore,\n                onChange: handleFormChange\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 494,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 493,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Grid, {\n              item: true,\n              xs: 12,\n              sm: 6,\n              children: /*#__PURE__*/_jsxDEV(TextField, {\n                name: \"n_DDT\",\n                label: \"Numero DDT\",\n                fullWidth: true,\n                variant: \"outlined\",\n                value: formData.n_DDT,\n                onChange: handleFormChange\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 504,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 503,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Grid, {\n              item: true,\n              xs: 12,\n              sm: 6,\n              children: /*#__PURE__*/_jsxDEV(TextField, {\n                name: \"data_DDT\",\n                label: \"Data DDT (YYYY-MM-DD)\",\n                fullWidth: true,\n                variant: \"outlined\",\n                value: formData.data_DDT,\n                onChange: handleFormChange,\n                placeholder: \"YYYY-MM-DD\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 514,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 513,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Grid, {\n              item: true,\n              xs: 12,\n              sm: 6,\n              children: /*#__PURE__*/_jsxDEV(TextField, {\n                name: \"configurazione\",\n                label: \"Configurazione\",\n                fullWidth: true,\n                variant: \"outlined\",\n                value: formData.configurazione,\n                onChange: handleFormChange,\n                disabled: true,\n                helperText: formData.configurazione === 's' ? 'Usa numeri progressivi' : 'Inserimento manuale ID bobina'\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 525,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 524,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 383,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 382,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(DialogActions, {\n          children: [/*#__PURE__*/_jsxDEV(Button, {\n            onClick: handleCloseDialog,\n            children: \"Annulla\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 539,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Button, {\n            onClick: handleSave,\n            disabled: loading || dialogType === 'creaBobina' && formData.configurazione === 'n' && !formData.numero_bobina,\n            startIcon: loading ? /*#__PURE__*/_jsxDEV(CircularProgress, {\n              size: 20\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 543,\n              columnNumber: 36\n            }, this) : /*#__PURE__*/_jsxDEV(SaveIcon, {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 543,\n              columnNumber: 69\n            }, this),\n            children: \"Salva\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 540,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 538,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 378,\n        columnNumber: 9\n      }, this);\n    } else if (dialogType === 'selezionaBobina') {\n      return /*#__PURE__*/_jsxDEV(Dialog, {\n        open: openDialog,\n        onClose: handleCloseDialog,\n        maxWidth: \"md\",\n        fullWidth: true,\n        children: [/*#__PURE__*/_jsxDEV(DialogTitle, {\n          children: \"Seleziona Bobina da Modificare\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 553,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(DialogContent, {\n          children: loading ? /*#__PURE__*/_jsxDEV(CircularProgress, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 556,\n            columnNumber: 15\n          }, this) : bobine.length === 0 ? /*#__PURE__*/_jsxDEV(Alert, {\n            severity: \"info\",\n            children: \"Nessuna bobina disponibile\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 558,\n            columnNumber: 15\n          }, this) : /*#__PURE__*/_jsxDEV(List, {\n            children: bobine.map(bobina => /*#__PURE__*/_jsxDEV(ListItem, {\n              button: true,\n              onClick: () => handleBobinaSelect(bobina),\n              children: /*#__PURE__*/_jsxDEV(ListItemText, {\n                primary: `Bobina: ${bobina.numero_bobina}`,\n                secondary: `Tipologia: ${bobina.tipologia || 'N/A'} - Utility: ${bobina.utility || 'N/A'} - Residuo: ${bobina.metri_residui || 'N/A'} m`\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 567,\n                columnNumber: 21\n              }, this)\n            }, bobina.numero_bobina, false, {\n              fileName: _jsxFileName,\n              lineNumber: 562,\n              columnNumber: 19\n            }, this))\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 560,\n            columnNumber: 15\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 554,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(DialogActions, {\n          children: /*#__PURE__*/_jsxDEV(Button, {\n            onClick: handleCloseDialog,\n            children: \"Annulla\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 577,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 576,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 552,\n        columnNumber: 9\n      }, this);\n    } else if (dialogType === 'eliminaBobina') {\n      return /*#__PURE__*/_jsxDEV(Dialog, {\n        open: openDialog,\n        onClose: handleCloseDialog,\n        maxWidth: \"sm\",\n        fullWidth: true,\n        children: [/*#__PURE__*/_jsxDEV(DialogTitle, {\n          children: \"Elimina Bobina\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 584,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(DialogContent, {\n          children: !selectedBobina ? loading ? /*#__PURE__*/_jsxDEV(CircularProgress, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 588,\n            columnNumber: 17\n          }, this) : bobine.length === 0 ? /*#__PURE__*/_jsxDEV(Alert, {\n            severity: \"info\",\n            children: \"Nessuna bobina disponibile\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 590,\n            columnNumber: 17\n          }, this) : /*#__PURE__*/_jsxDEV(List, {\n            children: bobine.map(bobina => /*#__PURE__*/_jsxDEV(ListItem, {\n              button: true,\n              onClick: () => setSelectedBobina(bobina),\n              children: /*#__PURE__*/_jsxDEV(ListItemText, {\n                primary: `Bobina: ${bobina.numero_bobina}`,\n                secondary: `Tipologia: ${bobina.tipologia || 'N/A'} - Utility: ${bobina.utility || 'N/A'} - Residuo: ${bobina.metri_residui || 'N/A'} m`\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 599,\n                columnNumber: 23\n              }, this)\n            }, bobina.numero_bobina, false, {\n              fileName: _jsxFileName,\n              lineNumber: 594,\n              columnNumber: 21\n            }, this))\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 592,\n            columnNumber: 17\n          }, this) : /*#__PURE__*/_jsxDEV(Box, {\n            children: [/*#__PURE__*/_jsxDEV(Alert, {\n              severity: \"warning\",\n              sx: {\n                mb: 2\n              },\n              children: [\"Sei sicuro di voler eliminare la bobina \", selectedBobina.numero_bobina, \"?\"]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 609,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(Typography, {\n              variant: \"body1\",\n              children: \"Questa operazione non pu\\xF2 essere annullata.\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 612,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 608,\n            columnNumber: 15\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 585,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(DialogActions, {\n          children: [/*#__PURE__*/_jsxDEV(Button, {\n            onClick: handleCloseDialog,\n            children: \"Annulla\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 619,\n            columnNumber: 13\n          }, this), selectedBobina && /*#__PURE__*/_jsxDEV(Button, {\n            onClick: handleSave,\n            disabled: loading,\n            color: \"error\",\n            startIcon: loading ? /*#__PURE__*/_jsxDEV(CircularProgress, {\n              size: 20\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 625,\n              columnNumber: 38\n            }, this) : /*#__PURE__*/_jsxDEV(DeleteIcon, {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 625,\n              columnNumber: 71\n            }, this),\n            children: \"Elimina\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 621,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 618,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 583,\n        columnNumber: 9\n      }, this);\n    } else if (dialogType === 'visualizzaStorico') {\n      return /*#__PURE__*/_jsxDEV(Dialog, {\n        open: openDialog,\n        onClose: handleCloseDialog,\n        maxWidth: \"lg\",\n        fullWidth: true,\n        children: [/*#__PURE__*/_jsxDEV(DialogTitle, {\n          children: \"Storico Utilizzo Bobine\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 636,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(DialogContent, {\n          children: loading ? /*#__PURE__*/_jsxDEV(CircularProgress, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 639,\n            columnNumber: 15\n          }, this) : storicoUtilizzo.length === 0 ? /*#__PURE__*/_jsxDEV(Alert, {\n            severity: \"info\",\n            children: \"Nessun dato storico disponibile\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 641,\n            columnNumber: 15\n          }, this) : /*#__PURE__*/_jsxDEV(TableContainer, {\n            component: Paper,\n            sx: {\n              mt: 2\n            },\n            children: /*#__PURE__*/_jsxDEV(Table, {\n              size: \"small\",\n              children: [/*#__PURE__*/_jsxDEV(TableHead, {\n                children: /*#__PURE__*/_jsxDEV(TableRow, {\n                  children: [/*#__PURE__*/_jsxDEV(TableCell, {\n                    children: \"Bobina\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 647,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n                    children: \"Utility\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 648,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n                    children: \"Tipologia\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 649,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n                    children: \"N\\xB0 Conduttori\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 650,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n                    children: \"Sezione\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 651,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n                    children: \"Metri Totali\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 652,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n                    children: \"Metri Residui\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 653,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n                    children: \"Cavi Associati\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 654,\n                    columnNumber: 23\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 646,\n                  columnNumber: 21\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 645,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(TableBody, {\n                children: storicoUtilizzo.map((record, index) => /*#__PURE__*/_jsxDEV(TableRow, {\n                  children: [/*#__PURE__*/_jsxDEV(TableCell, {\n                    children: record.numero_bobina\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 660,\n                    columnNumber: 25\n                  }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n                    children: record.utility\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 661,\n                    columnNumber: 25\n                  }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n                    children: record.tipologia\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 662,\n                    columnNumber: 25\n                  }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n                    children: record.n_conduttori\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 663,\n                    columnNumber: 25\n                  }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n                    children: record.sezione\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 664,\n                    columnNumber: 25\n                  }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n                    children: record.metri_totali\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 665,\n                    columnNumber: 25\n                  }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n                    children: record.metri_residui\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 666,\n                    columnNumber: 25\n                  }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n                    children: record.cavi.length\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 667,\n                    columnNumber: 25\n                  }, this)]\n                }, index, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 659,\n                  columnNumber: 23\n                }, this))\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 657,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 644,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 643,\n            columnNumber: 15\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 637,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(DialogActions, {\n          children: /*#__PURE__*/_jsxDEV(Button, {\n            onClick: handleCloseDialog,\n            children: \"Chiudi\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 676,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 675,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 635,\n        columnNumber: 9\n      }, this);\n    }\n    return null;\n  };\n  return /*#__PURE__*/_jsxDEV(Box, {\n    children: [selectedOption === 'visualizzaBobine' && !openDialog ? /*#__PURE__*/_jsxDEV(Paper, {\n      sx: {\n        p: 3\n      },\n      children: [/*#__PURE__*/_jsxDEV(Typography, {\n        variant: \"h6\",\n        gutterBottom: true,\n        children: \"Bobine Disponibili\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 689,\n        columnNumber: 11\n      }, this), loading ? /*#__PURE__*/_jsxDEV(Box, {\n        sx: {\n          display: 'flex',\n          justifyContent: 'center',\n          my: 4\n        },\n        children: /*#__PURE__*/_jsxDEV(CircularProgress, {}, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 694,\n          columnNumber: 15\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 693,\n        columnNumber: 13\n      }, this) : renderBobineCards()]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 688,\n      columnNumber: 9\n    }, this) : !openDialog ? /*#__PURE__*/_jsxDEV(Paper, {\n      sx: {\n        p: 3,\n        minHeight: '300px',\n        display: 'flex',\n        alignItems: 'center',\n        justifyContent: 'center'\n      },\n      children: !selectedOption ? /*#__PURE__*/_jsxDEV(Typography, {\n        variant: \"body1\",\n        children: \"Seleziona un'opzione dal menu principale per iniziare.\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 703,\n        columnNumber: 13\n      }, this) : /*#__PURE__*/_jsxDEV(Box, {\n        sx: {\n          textAlign: 'center'\n        },\n        children: [/*#__PURE__*/_jsxDEV(Typography, {\n          variant: \"h6\",\n          gutterBottom: true,\n          children: [selectedOption === 'creaBobina' && 'Crea Nuova Bobina', selectedOption === 'modificaBobina' && 'Modifica Bobina', selectedOption === 'eliminaBobina' && 'Elimina Bobina', selectedOption === 'visualizzaStorico' && 'Visualizza Storico Utilizzo']\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 708,\n          columnNumber: 15\n        }, this), /*#__PURE__*/_jsxDEV(CircularProgress, {\n          sx: {\n            mt: 2\n          }\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 714,\n          columnNumber: 15\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 707,\n        columnNumber: 13\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 701,\n      columnNumber: 9\n    }, this) : null, renderDialog(), /*#__PURE__*/_jsxDEV(ConfigurazioneDialog, {\n      open: openConfigDialog,\n      onClose: () => setOpenConfigDialog(false),\n      onConfirm: handleConfigConfirm\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 723,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 686,\n    columnNumber: 5\n  }, this);\n};\n_s(ParcoCavi, \"HO6yrtTNSHw9yitZFvkZn7gDYL0=\");\n_c = ParcoCavi;\nexport default ParcoCavi;\nvar _c;\n$RefreshReg$(_c, \"ParcoCavi\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "Box", "Typography", "<PERSON><PERSON>", "Paper", "Grid", "Card", "<PERSON><PERSON><PERSON><PERSON>", "CardActions", "Dialog", "DialogTitle", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "DialogActions", "TextField", "FormControl", "InputLabel", "Select", "MenuItem", "List", "ListItem", "ListItemText", "ListItemIcon", "ListItemButton", "Divider", "<PERSON><PERSON>", "CircularProgress", "Table", "TableBody", "TableCell", "TableContainer", "TableHead", "TableRow", "Add", "AddIcon", "Edit", "EditIcon", "Delete", "DeleteIcon", "History", "HistoryIcon", "Save", "SaveIcon", "ViewList", "ViewListIcon", "parcoCaviService", "ConfigurazioneDialog", "jsxDEV", "_jsxDEV", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "cantiereId", "onSuccess", "onError", "initialOption", "_s", "loading", "setLoading", "bobine", "set<PERSON>ob<PERSON>", "selectedOption", "setSelectedOption", "openDialog", "setOpenDialog", "dialogType", "setDialogType", "<PERSON><PERSON><PERSON><PERSON>", "setSelectedBobina", "formData", "setFormData", "numero_bobina", "utility", "tipologia", "n_conduttori", "sezione", "metri_totali", "metri_residui", "stato_bobina", "ubicazione_bobina", "fornitore", "n_DDT", "data_DDT", "configurazione", "storico<PERSON><PERSON><PERSON><PERSON>", "setStoricoUtilizzo", "openConfigDialog", "setOpenConfigDialog", "isFirstInsertion", "setIsFirstInsertion", "loadBobine", "data", "getBobine", "error", "console", "loadStoricoUtilizzo", "getStoricoUtilizzo", "handleOptionSelect", "checkIfFirstInsertion", "<PERSON><PERSON><PERSON><PERSON>", "isFirstBobinaInsertion", "handleConfigConfirm", "config<PERSON><PERSON><PERSON>", "log", "defaultFormData", "option", "handleCloseDialog", "handleBobinaSelect", "bobina", "handleFormChange", "e", "name", "value", "target", "handleSave", "bobina<PERSON><PERSON>", "parseFloat", "createBobina", "bobina<PERSON>d", "id_bobina", "updateBobina", "deleteBobina", "detail", "message", "renderBobineCards", "length", "severity", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "container", "spacing", "map", "item", "xs", "sm", "md", "variant", "component", "color", "size", "startIcon", "onClick", "renderDialog", "open", "onClose", "max<PERSON><PERSON><PERSON>", "fullWidth", "sx", "mt", "label", "onChange", "disabled", "required", "helperText", "type", "id", "labelId", "placeholder", "button", "primary", "secondary", "mb", "record", "index", "cavi", "p", "gutterBottom", "display", "justifyContent", "my", "minHeight", "alignItems", "textAlign", "onConfirm", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/PycharmProjects/CMS/webapp/frontend/src/components/cavi/ParcoCavi.js"], "sourcesContent": ["import React, { useState, useEffect } from 'react';\nimport {\n  Box,\n  Typography,\n  Button,\n  Paper,\n  Grid,\n  Card,\n  CardContent,\n  CardActions,\n  Dialog,\n  DialogTitle,\n  DialogContent,\n  DialogActions,\n  TextField,\n  FormControl,\n  InputLabel,\n  Select,\n  MenuItem,\n  List,\n  ListItem,\n  ListItemText,\n  ListItemIcon,\n  ListItemButton,\n  Divider,\n  Alert,\n  CircularProgress,\n  Table,\n  TableBody,\n  TableCell,\n  TableContainer,\n  TableHead,\n  TableRow\n} from '@mui/material';\nimport {\n  Add as AddIcon,\n  Edit as EditIcon,\n  Delete as DeleteIcon,\n  History as HistoryIcon,\n  Save as SaveIcon,\n  ViewList as ViewListIcon\n} from '@mui/icons-material';\nimport parcoCaviService from '../../services/parcoCaviService';\nimport ConfigurazioneDialog from './ConfigurazioneDialog';\n\nconst ParcoCavi = ({ cantiereId, onSuccess, onError, initialOption = null }) => {\n  const [loading, setLoading] = useState(false);\n  const [bobine, setBobine] = useState([]);\n  const [selectedOption, setSelectedOption] = useState(initialOption);\n  const [openDialog, setOpenDialog] = useState(false);\n  const [dialogType, setDialogType] = useState('');\n  const [selectedBobina, setSelectedBobina] = useState(null);\n  const [formData, setFormData] = useState({\n    numero_bobina: '',\n    utility: '',\n    tipologia: '',\n    n_conduttori: '',\n    sezione: '',\n    metri_totali: '',\n    metri_residui: '',\n    stato_bobina: 'Disponibile',\n    ubicazione_bobina: '',\n    fornitore: '',\n    n_DDT: '',\n    data_DDT: '',\n    configurazione: ''\n  });\n  const [storicoUtilizzo, setStoricoUtilizzo] = useState([]);\n  const [openConfigDialog, setOpenConfigDialog] = useState(false);\n  const [isFirstInsertion, setIsFirstInsertion] = useState(false);\n\n  // Carica le bobine disponibili\n  const loadBobine = async () => {\n    try {\n      setLoading(true);\n      const data = await parcoCaviService.getBobine(cantiereId);\n      setBobine(data);\n    } catch (error) {\n      onError('Errore nel caricamento delle bobine');\n      console.error('Errore nel caricamento delle bobine:', error);\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  // Carica lo storico utilizzo bobine\n  const loadStoricoUtilizzo = async () => {\n    try {\n      setLoading(true);\n      const data = await parcoCaviService.getStoricoUtilizzo(cantiereId);\n      setStoricoUtilizzo(data);\n    } catch (error) {\n      onError('Errore nel caricamento dello storico utilizzo');\n      console.error('Errore nel caricamento dello storico utilizzo:', error);\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  // Carica i dati all'avvio del componente e gestisce l'opzione iniziale\n  useEffect(() => {\n    if (initialOption) {\n      handleOptionSelect(initialOption);\n    } else {\n      loadBobine();\n    }\n  }, [cantiereId, initialOption]);\n\n  // Verifica se è il primo inserimento di una bobina per un cantiere\n  const checkIfFirstInsertion = async () => {\n    try {\n      setLoading(true);\n      const isFirst = await parcoCaviService.isFirstBobinaInsertion(cantiereId);\n      setIsFirstInsertion(isFirst);\n\n      if (isFirst) {\n        // Se è il primo inserimento, mostra il dialog di configurazione\n        setOpenConfigDialog(true);\n      } else {\n        // Non è il primo inserimento, procedi con il form normale\n        setDialogType('creaBobina');\n        setFormData({\n          numero_bobina: '',\n          utility: '',\n          tipologia: '',\n          n_conduttori: '',\n          sezione: '',\n          metri_totali: '',\n          metri_residui: '',\n          stato_bobina: 'Disponibile',\n          ubicazione_bobina: '',\n          fornitore: '',\n          n_DDT: '',\n          data_DDT: '',\n          configurazione: ''\n        });\n        setOpenDialog(true);\n      }\n    } catch (error) {\n      onError('Errore nel controllo dell\\'inserimento della prima bobina');\n      console.error('Errore nel controllo dell\\'inserimento della prima bobina:', error);\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  // Gestisce la conferma della configurazione\n  const handleConfigConfirm = (configValue) => {\n    console.log('Configurazione selezionata:', configValue);\n    setOpenConfigDialog(false);\n\n    // Imposta i valori di default per la bobina\n    const defaultFormData = {\n      numero_bobina: configValue === 's' ? '1' : '', // Se usiamo numeri progressivi, imposta il default a 1\n      utility: '',\n      tipologia: '',\n      n_conduttori: '',\n      sezione: '',\n      metri_totali: '',\n      metri_residui: '',\n      stato_bobina: 'Disponibile',\n      ubicazione_bobina: '',\n      fornitore: '',\n      n_DDT: '',\n      data_DDT: '',\n      configurazione: configValue // Imposta la configurazione scelta\n    };\n\n    setFormData(defaultFormData);\n    setDialogType('creaBobina');\n    setOpenDialog(true);\n  };\n\n  // Gestisce la selezione di un'opzione dal menu\n  const handleOptionSelect = (option) => {\n    setSelectedOption(option);\n\n    if (option === 'visualizzaBobine') {\n      loadBobine();\n    } else if (option === 'creaBobina') {\n      checkIfFirstInsertion();\n    } else if (option === 'modificaBobina') {\n      loadBobine();\n      setDialogType('selezionaBobina');\n      setOpenDialog(true);\n    } else if (option === 'eliminaBobina') {\n      loadBobine();\n      setDialogType('eliminaBobina');\n      setOpenDialog(true);\n    } else if (option === 'visualizzaStorico') {\n      loadStoricoUtilizzo();\n      setDialogType('visualizzaStorico');\n      setOpenDialog(true);\n    }\n  };\n\n  // Gestisce la chiusura del dialog\n  const handleCloseDialog = () => {\n    setOpenDialog(false);\n    setSelectedBobina(null);\n    setFormData({\n      numero_bobina: '',\n      utility: '',\n      tipologia: '',\n      n_conduttori: '',\n      sezione: '',\n      metri_totali: '',\n      metri_residui: '',\n      stato_bobina: 'Disponibile',\n      ubicazione_bobina: '',\n      fornitore: '',\n      n_DDT: '',\n      data_DDT: '',\n      configurazione: ''\n    });\n  };\n\n  // Gestisce la selezione di una bobina\n  const handleBobinaSelect = (bobina) => {\n    setSelectedBobina(bobina);\n    if (dialogType === 'selezionaBobina') {\n      setDialogType('modificaBobina');\n      setFormData({\n        numero_bobina: bobina.numero_bobina,\n        utility: bobina.utility || '',\n        tipologia: bobina.tipologia || '',\n        n_conduttori: bobina.n_conduttori || '',\n        sezione: bobina.sezione || '',\n        metri_totali: bobina.metri_totali || '',\n        metri_residui: bobina.metri_residui || '',\n        stato_bobina: bobina.stato_bobina || 'DISPONIBILE',\n        ubicazione_bobina: bobina.ubicazione_bobina || '',\n        fornitore: bobina.fornitore || '',\n        n_DDT: bobina.n_DDT || '',\n        data_DDT: bobina.data_DDT || '',\n        configurazione: bobina.configurazione || ''\n      });\n    }\n  };\n\n  // Gestisce il cambio dei valori nel form\n  const handleFormChange = (e) => {\n    const { name, value } = e.target;\n    setFormData({\n      ...formData,\n      [name]: value\n    });\n  };\n\n  // Gestisce il salvataggio del form\n  const handleSave = async () => {\n    try {\n      setLoading(true);\n\n      if (dialogType === 'creaBobina') {\n        // Prepara i dati per la creazione della bobina\n        const bobinaData = {\n          ...formData,\n          // Converti i campi numerici da stringa a numero\n          metri_totali: parseFloat(formData.metri_totali),\n          metri_residui: parseFloat(formData.metri_totali), // Inizialmente i metri residui sono uguali ai metri totali\n          // Assicurati che la configurazione sia impostata correttamente\n          configurazione: formData.configurazione || 's'\n        };\n\n        // Log per debug\n        console.log('Dati bobina da inviare:', bobinaData);\n\n        // Invia i dati al backend\n        await parcoCaviService.createBobina(cantiereId, bobinaData);\n        onSuccess('Bobina creata con successo');\n\n        // Ricarica le bobine e chiudi il dialog\n        loadBobine();\n        handleCloseDialog();\n      } else if (dialogType === 'modificaBobina') {\n        // Per la modifica, usa l'ID bobina completo o il numero bobina\n        const bobinaId = selectedBobina.id_bobina || formData.numero_bobina;\n        await parcoCaviService.updateBobina(cantiereId, bobinaId, formData);\n        onSuccess('Bobina modificata con successo');\n      } else if (dialogType === 'eliminaBobina') {\n        // Per l'eliminazione, usa l'ID bobina completo\n        const bobinaId = selectedBobina.id_bobina || selectedBobina.numero_bobina;\n        await parcoCaviService.deleteBobina(cantiereId, bobinaId);\n        onSuccess('Bobina eliminata con successo');\n      }\n\n      handleCloseDialog();\n      loadBobine(); // Ricarica le bobine dopo l'operazione\n    } catch (error) {\n      onError('Errore durante l\\'operazione: ' + (error.detail || error.message || 'Errore sconosciuto'));\n      console.error('Errore durante l\\'operazione:', error);\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  // Renderizza le bobine in formato card\n  const renderBobineCards = () => {\n    if (bobine.length === 0) {\n      return (\n        <Alert severity=\"info\">Nessuna bobina disponibile</Alert>\n      );\n    }\n\n    return (\n      <Grid container spacing={2}>\n        {bobine.map((bobina) => (\n          <Grid item xs={12} sm={6} md={4} key={bobina.numero_bobina}>\n            <Card>\n              <CardContent>\n                <Typography variant=\"h6\" component=\"div\">\n                  Bobina: {bobina.numero_bobina}\n                </Typography>\n                <Typography variant=\"body2\" color=\"text.secondary\">\n                  Utility: {bobina.utility || 'N/A'}\n                </Typography>\n                <Typography variant=\"body2\" color=\"text.secondary\">\n                  Tipologia: {bobina.tipologia || 'N/A'}\n                </Typography>\n                <Typography variant=\"body2\" color=\"text.secondary\">\n                  N° Conduttori: {bobina.n_conduttori || 'N/A'}\n                </Typography>\n                <Typography variant=\"body2\" color=\"text.secondary\">\n                  Sezione: {bobina.sezione || 'N/A'}\n                </Typography>\n                <Typography variant=\"body2\" color=\"text.secondary\">\n                  Metri totali: {bobina.metri_totali || 'N/A'} m\n                </Typography>\n                <Typography variant=\"body2\" color=\"text.secondary\">\n                  Metri residui: {bobina.metri_residui || 'N/A'} m\n                </Typography>\n                <Typography variant=\"body2\" color=\"text.secondary\">\n                  Stato: {bobina.stato_bobina || 'DISPONIBILE'}\n                </Typography>\n                <Typography variant=\"body2\" color=\"text.secondary\">\n                  Ubicazione: {bobina.ubicazione_bobina || 'N/A'}\n                </Typography>\n                <Typography variant=\"body2\" color=\"text.secondary\">\n                  Fornitore: {bobina.fornitore || 'N/A'}\n                </Typography>\n              </CardContent>\n              <CardActions>\n                <Button\n                  size=\"small\"\n                  startIcon={<EditIcon />}\n                  onClick={() => {\n                    setDialogType('selezionaBobina');\n                    handleBobinaSelect(bobina);\n                  }}\n                >\n                  Modifica\n                </Button>\n                <Button\n                  size=\"small\"\n                  color=\"error\"\n                  startIcon={<DeleteIcon />}\n                  onClick={() => {\n                    setDialogType('eliminaBobina');\n                    setSelectedBobina(bobina);\n                    setOpenDialog(true);\n                  }}\n                >\n                  Elimina\n                </Button>\n              </CardActions>\n            </Card>\n          </Grid>\n        ))}\n      </Grid>\n    );\n  };\n\n  // Renderizza il dialog in base al tipo\n  const renderDialog = () => {\n    if (dialogType === 'creaBobina' || dialogType === 'modificaBobina') {\n      return (\n        <Dialog open={openDialog} onClose={handleCloseDialog} maxWidth=\"md\" fullWidth>\n          <DialogTitle>\n            {dialogType === 'creaBobina' ? 'Crea Nuova Bobina' : 'Modifica Bobina'}\n          </DialogTitle>\n          <DialogContent>\n            <Grid container spacing={2} sx={{ mt: 1 }}>\n              <Grid item xs={12} sm={6}>\n                <TextField\n                  name=\"numero_bobina\"\n                  label=\"Numero Bobina\"\n                  fullWidth\n                  variant=\"outlined\"\n                  value={formData.numero_bobina}\n                  onChange={handleFormChange}\n                  disabled={dialogType === 'modificaBobina' || (dialogType === 'creaBobina' && formData.configurazione === 's')}\n                  required\n                  helperText={dialogType === 'creaBobina' && formData.configurazione === 's' ? 'Numero generato automaticamente' : ''}\n                />\n              </Grid>\n              <Grid item xs={12} sm={6}>\n                <TextField\n                  name=\"utility\"\n                  label=\"Utility\"\n                  fullWidth\n                  variant=\"outlined\"\n                  value={formData.utility}\n                  onChange={handleFormChange}\n                  required\n                />\n              </Grid>\n              <Grid item xs={12} sm={6}>\n                <TextField\n                  name=\"tipologia\"\n                  label=\"Tipologia\"\n                  fullWidth\n                  variant=\"outlined\"\n                  value={formData.tipologia}\n                  onChange={handleFormChange}\n                  required\n                />\n              </Grid>\n              <Grid item xs={12} sm={6}>\n                <TextField\n                  name=\"n_conduttori\"\n                  label=\"N° Conduttori\"\n                  fullWidth\n                  variant=\"outlined\"\n                  value={formData.n_conduttori}\n                  onChange={handleFormChange}\n                  required\n                />\n              </Grid>\n              <Grid item xs={12} sm={6}>\n                <TextField\n                  name=\"sezione\"\n                  label=\"Sezione\"\n                  fullWidth\n                  variant=\"outlined\"\n                  value={formData.sezione}\n                  onChange={handleFormChange}\n                  required\n                />\n              </Grid>\n              <Grid item xs={12} sm={6}>\n                <TextField\n                  name=\"metri_totali\"\n                  label=\"Metri Totali\"\n                  type=\"number\"\n                  fullWidth\n                  variant=\"outlined\"\n                  value={formData.metri_totali}\n                  onChange={handleFormChange}\n                  required\n                />\n              </Grid>\n              <Grid item xs={12} sm={6}>\n                <TextField\n                  name=\"metri_residui\"\n                  label=\"Metri Residui\"\n                  type=\"number\"\n                  fullWidth\n                  variant=\"outlined\"\n                  value={formData.metri_residui}\n                  onChange={handleFormChange}\n                  required\n                />\n              </Grid>\n              <Grid item xs={12} sm={6}>\n                <FormControl fullWidth>\n                  <InputLabel id=\"stato-bobina-label\">Stato Bobina</InputLabel>\n                  <Select\n                    labelId=\"stato-bobina-label\"\n                    name=\"stato_bobina\"\n                    value={formData.stato_bobina}\n                    label=\"Stato Bobina\"\n                    onChange={handleFormChange}\n                  >\n                    <MenuItem value=\"Disponibile\">Disponibile</MenuItem>\n                    <MenuItem value=\"In uso\">In uso</MenuItem>\n                    <MenuItem value=\"Terminata\">Terminata</MenuItem>\n                    <MenuItem value=\"Danneggiata\">Danneggiata</MenuItem>\n                    <MenuItem value=\"Over\">Over</MenuItem>\n                  </Select>\n                </FormControl>\n              </Grid>\n              <Grid item xs={12} sm={6}>\n                <TextField\n                  name=\"ubicazione_bobina\"\n                  label=\"Ubicazione Bobina\"\n                  fullWidth\n                  variant=\"outlined\"\n                  value={formData.ubicazione_bobina}\n                  onChange={handleFormChange}\n                />\n              </Grid>\n              <Grid item xs={12} sm={6}>\n                <TextField\n                  name=\"fornitore\"\n                  label=\"Fornitore\"\n                  fullWidth\n                  variant=\"outlined\"\n                  value={formData.fornitore}\n                  onChange={handleFormChange}\n                />\n              </Grid>\n              <Grid item xs={12} sm={6}>\n                <TextField\n                  name=\"n_DDT\"\n                  label=\"Numero DDT\"\n                  fullWidth\n                  variant=\"outlined\"\n                  value={formData.n_DDT}\n                  onChange={handleFormChange}\n                />\n              </Grid>\n              <Grid item xs={12} sm={6}>\n                <TextField\n                  name=\"data_DDT\"\n                  label=\"Data DDT (YYYY-MM-DD)\"\n                  fullWidth\n                  variant=\"outlined\"\n                  value={formData.data_DDT}\n                  onChange={handleFormChange}\n                  placeholder=\"YYYY-MM-DD\"\n                />\n              </Grid>\n              <Grid item xs={12} sm={6}>\n                <TextField\n                  name=\"configurazione\"\n                  label=\"Configurazione\"\n                  fullWidth\n                  variant=\"outlined\"\n                  value={formData.configurazione}\n                  onChange={handleFormChange}\n                  disabled={true}\n                  helperText={formData.configurazione === 's' ? 'Usa numeri progressivi' : 'Inserimento manuale ID bobina'}\n                />\n              </Grid>\n            </Grid>\n          </DialogContent>\n          <DialogActions>\n            <Button onClick={handleCloseDialog}>Annulla</Button>\n            <Button\n              onClick={handleSave}\n              disabled={loading || (dialogType === 'creaBobina' && formData.configurazione === 'n' && !formData.numero_bobina)}\n              startIcon={loading ? <CircularProgress size={20} /> : <SaveIcon />}\n            >\n              Salva\n            </Button>\n          </DialogActions>\n        </Dialog>\n      );\n    } else if (dialogType === 'selezionaBobina') {\n      return (\n        <Dialog open={openDialog} onClose={handleCloseDialog} maxWidth=\"md\" fullWidth>\n          <DialogTitle>Seleziona Bobina da Modificare</DialogTitle>\n          <DialogContent>\n            {loading ? (\n              <CircularProgress />\n            ) : bobine.length === 0 ? (\n              <Alert severity=\"info\">Nessuna bobina disponibile</Alert>\n            ) : (\n              <List>\n                {bobine.map((bobina) => (\n                  <ListItem\n                    button\n                    key={bobina.numero_bobina}\n                    onClick={() => handleBobinaSelect(bobina)}\n                  >\n                    <ListItemText\n                      primary={`Bobina: ${bobina.numero_bobina}`}\n                      secondary={`Tipologia: ${bobina.tipologia || 'N/A'} - Utility: ${bobina.utility || 'N/A'} - Residuo: ${bobina.metri_residui || 'N/A'} m`}\n                    />\n                  </ListItem>\n                ))}\n              </List>\n            )}\n          </DialogContent>\n          <DialogActions>\n            <Button onClick={handleCloseDialog}>Annulla</Button>\n          </DialogActions>\n        </Dialog>\n      );\n    } else if (dialogType === 'eliminaBobina') {\n      return (\n        <Dialog open={openDialog} onClose={handleCloseDialog} maxWidth=\"sm\" fullWidth>\n          <DialogTitle>Elimina Bobina</DialogTitle>\n          <DialogContent>\n            {!selectedBobina ? (\n              loading ? (\n                <CircularProgress />\n              ) : bobine.length === 0 ? (\n                <Alert severity=\"info\">Nessuna bobina disponibile</Alert>\n              ) : (\n                <List>\n                  {bobine.map((bobina) => (\n                    <ListItem\n                      button\n                      key={bobina.numero_bobina}\n                      onClick={() => setSelectedBobina(bobina)}\n                    >\n                      <ListItemText\n                        primary={`Bobina: ${bobina.numero_bobina}`}\n                        secondary={`Tipologia: ${bobina.tipologia || 'N/A'} - Utility: ${bobina.utility || 'N/A'} - Residuo: ${bobina.metri_residui || 'N/A'} m`}\n                      />\n                    </ListItem>\n                  ))}\n                </List>\n              )\n            ) : (\n              <Box>\n                <Alert severity=\"warning\" sx={{ mb: 2 }}>\n                  Sei sicuro di voler eliminare la bobina {selectedBobina.numero_bobina}?\n                </Alert>\n                <Typography variant=\"body1\">\n                  Questa operazione non può essere annullata.\n                </Typography>\n              </Box>\n            )}\n          </DialogContent>\n          <DialogActions>\n            <Button onClick={handleCloseDialog}>Annulla</Button>\n            {selectedBobina && (\n              <Button\n                onClick={handleSave}\n                disabled={loading}\n                color=\"error\"\n                startIcon={loading ? <CircularProgress size={20} /> : <DeleteIcon />}\n              >\n                Elimina\n              </Button>\n            )}\n          </DialogActions>\n        </Dialog>\n      );\n    } else if (dialogType === 'visualizzaStorico') {\n      return (\n        <Dialog open={openDialog} onClose={handleCloseDialog} maxWidth=\"lg\" fullWidth>\n          <DialogTitle>Storico Utilizzo Bobine</DialogTitle>\n          <DialogContent>\n            {loading ? (\n              <CircularProgress />\n            ) : storicoUtilizzo.length === 0 ? (\n              <Alert severity=\"info\">Nessun dato storico disponibile</Alert>\n            ) : (\n              <TableContainer component={Paper} sx={{ mt: 2 }}>\n                <Table size=\"small\">\n                  <TableHead>\n                    <TableRow>\n                      <TableCell>Bobina</TableCell>\n                      <TableCell>Utility</TableCell>\n                      <TableCell>Tipologia</TableCell>\n                      <TableCell>N° Conduttori</TableCell>\n                      <TableCell>Sezione</TableCell>\n                      <TableCell>Metri Totali</TableCell>\n                      <TableCell>Metri Residui</TableCell>\n                      <TableCell>Cavi Associati</TableCell>\n                    </TableRow>\n                  </TableHead>\n                  <TableBody>\n                    {storicoUtilizzo.map((record, index) => (\n                      <TableRow key={index}>\n                        <TableCell>{record.numero_bobina}</TableCell>\n                        <TableCell>{record.utility}</TableCell>\n                        <TableCell>{record.tipologia}</TableCell>\n                        <TableCell>{record.n_conduttori}</TableCell>\n                        <TableCell>{record.sezione}</TableCell>\n                        <TableCell>{record.metri_totali}</TableCell>\n                        <TableCell>{record.metri_residui}</TableCell>\n                        <TableCell>{record.cavi.length}</TableCell>\n                      </TableRow>\n                    ))}\n                  </TableBody>\n                </Table>\n              </TableContainer>\n            )}\n          </DialogContent>\n          <DialogActions>\n            <Button onClick={handleCloseDialog}>Chiudi</Button>\n          </DialogActions>\n        </Dialog>\n      );\n    }\n\n    return null;\n  };\n\n  return (\n    <Box>\n      {selectedOption === 'visualizzaBobine' && !openDialog ? (\n        <Paper sx={{ p: 3 }}>\n          <Typography variant=\"h6\" gutterBottom>\n            Bobine Disponibili\n          </Typography>\n          {loading ? (\n            <Box sx={{ display: 'flex', justifyContent: 'center', my: 4 }}>\n              <CircularProgress />\n            </Box>\n          ) : (\n            renderBobineCards()\n          )}\n        </Paper>\n      ) : !openDialog ? (\n        <Paper sx={{ p: 3, minHeight: '300px', display: 'flex', alignItems: 'center', justifyContent: 'center' }}>\n          {!selectedOption ? (\n            <Typography variant=\"body1\">\n              Seleziona un'opzione dal menu principale per iniziare.\n            </Typography>\n          ) : (\n            <Box sx={{ textAlign: 'center' }}>\n              <Typography variant=\"h6\" gutterBottom>\n                {selectedOption === 'creaBobina' && 'Crea Nuova Bobina'}\n                {selectedOption === 'modificaBobina' && 'Modifica Bobina'}\n                {selectedOption === 'eliminaBobina' && 'Elimina Bobina'}\n                {selectedOption === 'visualizzaStorico' && 'Visualizza Storico Utilizzo'}\n              </Typography>\n              <CircularProgress sx={{ mt: 2 }} />\n            </Box>\n          )}\n        </Paper>\n      ) : null}\n\n      {renderDialog()}\n\n      {/* Dialog di configurazione per il primo inserimento */}\n      <ConfigurazioneDialog\n        open={openConfigDialog}\n        onClose={() => setOpenConfigDialog(false)}\n        onConfirm={handleConfigConfirm}\n      />\n    </Box>\n  );\n};\n\nexport default ParcoCavi;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAClD,SACEC,GAAG,EACHC,UAAU,EACVC,MAAM,EACNC,KAAK,EACLC,IAAI,EACJC,IAAI,EACJC,WAAW,EACXC,WAAW,EACXC,MAAM,EACNC,WAAW,EACXC,aAAa,EACbC,aAAa,EACbC,SAAS,EACTC,WAAW,EACXC,UAAU,EACVC,MAAM,EACNC,QAAQ,EACRC,IAAI,EACJC,QAAQ,EACRC,YAAY,EACZC,YAAY,EACZC,cAAc,EACdC,OAAO,EACPC,KAAK,EACLC,gBAAgB,EAChBC,KAAK,EACLC,SAAS,EACTC,SAAS,EACTC,cAAc,EACdC,SAAS,EACTC,QAAQ,QACH,eAAe;AACtB,SACEC,GAAG,IAAIC,OAAO,EACdC,IAAI,IAAIC,QAAQ,EAChBC,MAAM,IAAIC,UAAU,EACpBC,OAAO,IAAIC,WAAW,EACtBC,IAAI,IAAIC,QAAQ,EAChBC,QAAQ,IAAIC,YAAY,QACnB,qBAAqB;AAC5B,OAAOC,gBAAgB,MAAM,iCAAiC;AAC9D,OAAOC,oBAAoB,MAAM,wBAAwB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAE1D,MAAMC,SAAS,GAAGA,CAAC;EAAEC,UAAU;EAAEC,SAAS;EAAEC,OAAO;EAAEC,aAAa,GAAG;AAAK,CAAC,KAAK;EAAAC,EAAA;EAC9E,MAAM,CAACC,OAAO,EAAEC,UAAU,CAAC,GAAGxD,QAAQ,CAAC,KAAK,CAAC;EAC7C,MAAM,CAACyD,MAAM,EAAEC,SAAS,CAAC,GAAG1D,QAAQ,CAAC,EAAE,CAAC;EACxC,MAAM,CAAC2D,cAAc,EAAEC,iBAAiB,CAAC,GAAG5D,QAAQ,CAACqD,aAAa,CAAC;EACnE,MAAM,CAACQ,UAAU,EAAEC,aAAa,CAAC,GAAG9D,QAAQ,CAAC,KAAK,CAAC;EACnD,MAAM,CAAC+D,UAAU,EAAEC,aAAa,CAAC,GAAGhE,QAAQ,CAAC,EAAE,CAAC;EAChD,MAAM,CAACiE,cAAc,EAAEC,iBAAiB,CAAC,GAAGlE,QAAQ,CAAC,IAAI,CAAC;EAC1D,MAAM,CAACmE,QAAQ,EAAEC,WAAW,CAAC,GAAGpE,QAAQ,CAAC;IACvCqE,aAAa,EAAE,EAAE;IACjBC,OAAO,EAAE,EAAE;IACXC,SAAS,EAAE,EAAE;IACbC,YAAY,EAAE,EAAE;IAChBC,OAAO,EAAE,EAAE;IACXC,YAAY,EAAE,EAAE;IAChBC,aAAa,EAAE,EAAE;IACjBC,YAAY,EAAE,aAAa;IAC3BC,iBAAiB,EAAE,EAAE;IACrBC,SAAS,EAAE,EAAE;IACbC,KAAK,EAAE,EAAE;IACTC,QAAQ,EAAE,EAAE;IACZC,cAAc,EAAE;EAClB,CAAC,CAAC;EACF,MAAM,CAACC,eAAe,EAAEC,kBAAkB,CAAC,GAAGnF,QAAQ,CAAC,EAAE,CAAC;EAC1D,MAAM,CAACoF,gBAAgB,EAAEC,mBAAmB,CAAC,GAAGrF,QAAQ,CAAC,KAAK,CAAC;EAC/D,MAAM,CAACsF,gBAAgB,EAAEC,mBAAmB,CAAC,GAAGvF,QAAQ,CAAC,KAAK,CAAC;;EAE/D;EACA,MAAMwF,UAAU,GAAG,MAAAA,CAAA,KAAY;IAC7B,IAAI;MACFhC,UAAU,CAAC,IAAI,CAAC;MAChB,MAAMiC,IAAI,GAAG,MAAM5C,gBAAgB,CAAC6C,SAAS,CAACxC,UAAU,CAAC;MACzDQ,SAAS,CAAC+B,IAAI,CAAC;IACjB,CAAC,CAAC,OAAOE,KAAK,EAAE;MACdvC,OAAO,CAAC,qCAAqC,CAAC;MAC9CwC,OAAO,CAACD,KAAK,CAAC,sCAAsC,EAAEA,KAAK,CAAC;IAC9D,CAAC,SAAS;MACRnC,UAAU,CAAC,KAAK,CAAC;IACnB;EACF,CAAC;;EAED;EACA,MAAMqC,mBAAmB,GAAG,MAAAA,CAAA,KAAY;IACtC,IAAI;MACFrC,UAAU,CAAC,IAAI,CAAC;MAChB,MAAMiC,IAAI,GAAG,MAAM5C,gBAAgB,CAACiD,kBAAkB,CAAC5C,UAAU,CAAC;MAClEiC,kBAAkB,CAACM,IAAI,CAAC;IAC1B,CAAC,CAAC,OAAOE,KAAK,EAAE;MACdvC,OAAO,CAAC,+CAA+C,CAAC;MACxDwC,OAAO,CAACD,KAAK,CAAC,gDAAgD,EAAEA,KAAK,CAAC;IACxE,CAAC,SAAS;MACRnC,UAAU,CAAC,KAAK,CAAC;IACnB;EACF,CAAC;;EAED;EACAvD,SAAS,CAAC,MAAM;IACd,IAAIoD,aAAa,EAAE;MACjB0C,kBAAkB,CAAC1C,aAAa,CAAC;IACnC,CAAC,MAAM;MACLmC,UAAU,CAAC,CAAC;IACd;EACF,CAAC,EAAE,CAACtC,UAAU,EAAEG,aAAa,CAAC,CAAC;;EAE/B;EACA,MAAM2C,qBAAqB,GAAG,MAAAA,CAAA,KAAY;IACxC,IAAI;MACFxC,UAAU,CAAC,IAAI,CAAC;MAChB,MAAMyC,OAAO,GAAG,MAAMpD,gBAAgB,CAACqD,sBAAsB,CAAChD,UAAU,CAAC;MACzEqC,mBAAmB,CAACU,OAAO,CAAC;MAE5B,IAAIA,OAAO,EAAE;QACX;QACAZ,mBAAmB,CAAC,IAAI,CAAC;MAC3B,CAAC,MAAM;QACL;QACArB,aAAa,CAAC,YAAY,CAAC;QAC3BI,WAAW,CAAC;UACVC,aAAa,EAAE,EAAE;UACjBC,OAAO,EAAE,EAAE;UACXC,SAAS,EAAE,EAAE;UACbC,YAAY,EAAE,EAAE;UAChBC,OAAO,EAAE,EAAE;UACXC,YAAY,EAAE,EAAE;UAChBC,aAAa,EAAE,EAAE;UACjBC,YAAY,EAAE,aAAa;UAC3BC,iBAAiB,EAAE,EAAE;UACrBC,SAAS,EAAE,EAAE;UACbC,KAAK,EAAE,EAAE;UACTC,QAAQ,EAAE,EAAE;UACZC,cAAc,EAAE;QAClB,CAAC,CAAC;QACFnB,aAAa,CAAC,IAAI,CAAC;MACrB;IACF,CAAC,CAAC,OAAO6B,KAAK,EAAE;MACdvC,OAAO,CAAC,2DAA2D,CAAC;MACpEwC,OAAO,CAACD,KAAK,CAAC,4DAA4D,EAAEA,KAAK,CAAC;IACpF,CAAC,SAAS;MACRnC,UAAU,CAAC,KAAK,CAAC;IACnB;EACF,CAAC;;EAED;EACA,MAAM2C,mBAAmB,GAAIC,WAAW,IAAK;IAC3CR,OAAO,CAACS,GAAG,CAAC,6BAA6B,EAAED,WAAW,CAAC;IACvDf,mBAAmB,CAAC,KAAK,CAAC;;IAE1B;IACA,MAAMiB,eAAe,GAAG;MACtBjC,aAAa,EAAE+B,WAAW,KAAK,GAAG,GAAG,GAAG,GAAG,EAAE;MAAE;MAC/C9B,OAAO,EAAE,EAAE;MACXC,SAAS,EAAE,EAAE;MACbC,YAAY,EAAE,EAAE;MAChBC,OAAO,EAAE,EAAE;MACXC,YAAY,EAAE,EAAE;MAChBC,aAAa,EAAE,EAAE;MACjBC,YAAY,EAAE,aAAa;MAC3BC,iBAAiB,EAAE,EAAE;MACrBC,SAAS,EAAE,EAAE;MACbC,KAAK,EAAE,EAAE;MACTC,QAAQ,EAAE,EAAE;MACZC,cAAc,EAAEmB,WAAW,CAAC;IAC9B,CAAC;IAEDhC,WAAW,CAACkC,eAAe,CAAC;IAC5BtC,aAAa,CAAC,YAAY,CAAC;IAC3BF,aAAa,CAAC,IAAI,CAAC;EACrB,CAAC;;EAED;EACA,MAAMiC,kBAAkB,GAAIQ,MAAM,IAAK;IACrC3C,iBAAiB,CAAC2C,MAAM,CAAC;IAEzB,IAAIA,MAAM,KAAK,kBAAkB,EAAE;MACjCf,UAAU,CAAC,CAAC;IACd,CAAC,MAAM,IAAIe,MAAM,KAAK,YAAY,EAAE;MAClCP,qBAAqB,CAAC,CAAC;IACzB,CAAC,MAAM,IAAIO,MAAM,KAAK,gBAAgB,EAAE;MACtCf,UAAU,CAAC,CAAC;MACZxB,aAAa,CAAC,iBAAiB,CAAC;MAChCF,aAAa,CAAC,IAAI,CAAC;IACrB,CAAC,MAAM,IAAIyC,MAAM,KAAK,eAAe,EAAE;MACrCf,UAAU,CAAC,CAAC;MACZxB,aAAa,CAAC,eAAe,CAAC;MAC9BF,aAAa,CAAC,IAAI,CAAC;IACrB,CAAC,MAAM,IAAIyC,MAAM,KAAK,mBAAmB,EAAE;MACzCV,mBAAmB,CAAC,CAAC;MACrB7B,aAAa,CAAC,mBAAmB,CAAC;MAClCF,aAAa,CAAC,IAAI,CAAC;IACrB;EACF,CAAC;;EAED;EACA,MAAM0C,iBAAiB,GAAGA,CAAA,KAAM;IAC9B1C,aAAa,CAAC,KAAK,CAAC;IACpBI,iBAAiB,CAAC,IAAI,CAAC;IACvBE,WAAW,CAAC;MACVC,aAAa,EAAE,EAAE;MACjBC,OAAO,EAAE,EAAE;MACXC,SAAS,EAAE,EAAE;MACbC,YAAY,EAAE,EAAE;MAChBC,OAAO,EAAE,EAAE;MACXC,YAAY,EAAE,EAAE;MAChBC,aAAa,EAAE,EAAE;MACjBC,YAAY,EAAE,aAAa;MAC3BC,iBAAiB,EAAE,EAAE;MACrBC,SAAS,EAAE,EAAE;MACbC,KAAK,EAAE,EAAE;MACTC,QAAQ,EAAE,EAAE;MACZC,cAAc,EAAE;IAClB,CAAC,CAAC;EACJ,CAAC;;EAED;EACA,MAAMwB,kBAAkB,GAAIC,MAAM,IAAK;IACrCxC,iBAAiB,CAACwC,MAAM,CAAC;IACzB,IAAI3C,UAAU,KAAK,iBAAiB,EAAE;MACpCC,aAAa,CAAC,gBAAgB,CAAC;MAC/BI,WAAW,CAAC;QACVC,aAAa,EAAEqC,MAAM,CAACrC,aAAa;QACnCC,OAAO,EAAEoC,MAAM,CAACpC,OAAO,IAAI,EAAE;QAC7BC,SAAS,EAAEmC,MAAM,CAACnC,SAAS,IAAI,EAAE;QACjCC,YAAY,EAAEkC,MAAM,CAAClC,YAAY,IAAI,EAAE;QACvCC,OAAO,EAAEiC,MAAM,CAACjC,OAAO,IAAI,EAAE;QAC7BC,YAAY,EAAEgC,MAAM,CAAChC,YAAY,IAAI,EAAE;QACvCC,aAAa,EAAE+B,MAAM,CAAC/B,aAAa,IAAI,EAAE;QACzCC,YAAY,EAAE8B,MAAM,CAAC9B,YAAY,IAAI,aAAa;QAClDC,iBAAiB,EAAE6B,MAAM,CAAC7B,iBAAiB,IAAI,EAAE;QACjDC,SAAS,EAAE4B,MAAM,CAAC5B,SAAS,IAAI,EAAE;QACjCC,KAAK,EAAE2B,MAAM,CAAC3B,KAAK,IAAI,EAAE;QACzBC,QAAQ,EAAE0B,MAAM,CAAC1B,QAAQ,IAAI,EAAE;QAC/BC,cAAc,EAAEyB,MAAM,CAACzB,cAAc,IAAI;MAC3C,CAAC,CAAC;IACJ;EACF,CAAC;;EAED;EACA,MAAM0B,gBAAgB,GAAIC,CAAC,IAAK;IAC9B,MAAM;MAAEC,IAAI;MAAEC;IAAM,CAAC,GAAGF,CAAC,CAACG,MAAM;IAChC3C,WAAW,CAAC;MACV,GAAGD,QAAQ;MACX,CAAC0C,IAAI,GAAGC;IACV,CAAC,CAAC;EACJ,CAAC;;EAED;EACA,MAAME,UAAU,GAAG,MAAAA,CAAA,KAAY;IAC7B,IAAI;MACFxD,UAAU,CAAC,IAAI,CAAC;MAEhB,IAAIO,UAAU,KAAK,YAAY,EAAE;QAC/B;QACA,MAAMkD,UAAU,GAAG;UACjB,GAAG9C,QAAQ;UACX;UACAO,YAAY,EAAEwC,UAAU,CAAC/C,QAAQ,CAACO,YAAY,CAAC;UAC/CC,aAAa,EAAEuC,UAAU,CAAC/C,QAAQ,CAACO,YAAY,CAAC;UAAE;UAClD;UACAO,cAAc,EAAEd,QAAQ,CAACc,cAAc,IAAI;QAC7C,CAAC;;QAED;QACAW,OAAO,CAACS,GAAG,CAAC,yBAAyB,EAAEY,UAAU,CAAC;;QAElD;QACA,MAAMpE,gBAAgB,CAACsE,YAAY,CAACjE,UAAU,EAAE+D,UAAU,CAAC;QAC3D9D,SAAS,CAAC,4BAA4B,CAAC;;QAEvC;QACAqC,UAAU,CAAC,CAAC;QACZgB,iBAAiB,CAAC,CAAC;MACrB,CAAC,MAAM,IAAIzC,UAAU,KAAK,gBAAgB,EAAE;QAC1C;QACA,MAAMqD,QAAQ,GAAGnD,cAAc,CAACoD,SAAS,IAAIlD,QAAQ,CAACE,aAAa;QACnE,MAAMxB,gBAAgB,CAACyE,YAAY,CAACpE,UAAU,EAAEkE,QAAQ,EAAEjD,QAAQ,CAAC;QACnEhB,SAAS,CAAC,gCAAgC,CAAC;MAC7C,CAAC,MAAM,IAAIY,UAAU,KAAK,eAAe,EAAE;QACzC;QACA,MAAMqD,QAAQ,GAAGnD,cAAc,CAACoD,SAAS,IAAIpD,cAAc,CAACI,aAAa;QACzE,MAAMxB,gBAAgB,CAAC0E,YAAY,CAACrE,UAAU,EAAEkE,QAAQ,CAAC;QACzDjE,SAAS,CAAC,+BAA+B,CAAC;MAC5C;MAEAqD,iBAAiB,CAAC,CAAC;MACnBhB,UAAU,CAAC,CAAC,CAAC,CAAC;IAChB,CAAC,CAAC,OAAOG,KAAK,EAAE;MACdvC,OAAO,CAAC,gCAAgC,IAAIuC,KAAK,CAAC6B,MAAM,IAAI7B,KAAK,CAAC8B,OAAO,IAAI,oBAAoB,CAAC,CAAC;MACnG7B,OAAO,CAACD,KAAK,CAAC,+BAA+B,EAAEA,KAAK,CAAC;IACvD,CAAC,SAAS;MACRnC,UAAU,CAAC,KAAK,CAAC;IACnB;EACF,CAAC;;EAED;EACA,MAAMkE,iBAAiB,GAAGA,CAAA,KAAM;IAC9B,IAAIjE,MAAM,CAACkE,MAAM,KAAK,CAAC,EAAE;MACvB,oBACE3E,OAAA,CAACvB,KAAK;QAACmG,QAAQ,EAAC,MAAM;QAAAC,QAAA,EAAC;MAA0B;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAO,CAAC;IAE7D;IAEA,oBACEjF,OAAA,CAAC1C,IAAI;MAAC4H,SAAS;MAACC,OAAO,EAAE,CAAE;MAAAN,QAAA,EACxBpE,MAAM,CAAC2E,GAAG,CAAE1B,MAAM,iBACjB1D,OAAA,CAAC1C,IAAI;QAAC+H,IAAI;QAACC,EAAE,EAAE,EAAG;QAACC,EAAE,EAAE,CAAE;QAACC,EAAE,EAAE,CAAE;QAAAX,QAAA,eAC9B7E,OAAA,CAACzC,IAAI;UAAAsH,QAAA,gBACH7E,OAAA,CAACxC,WAAW;YAAAqH,QAAA,gBACV7E,OAAA,CAAC7C,UAAU;cAACsI,OAAO,EAAC,IAAI;cAACC,SAAS,EAAC,KAAK;cAAAb,QAAA,GAAC,UAC/B,EAACnB,MAAM,CAACrC,aAAa;YAAA;cAAAyD,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACnB,CAAC,eACbjF,OAAA,CAAC7C,UAAU;cAACsI,OAAO,EAAC,OAAO;cAACE,KAAK,EAAC,gBAAgB;cAAAd,QAAA,GAAC,WACxC,EAACnB,MAAM,CAACpC,OAAO,IAAI,KAAK;YAAA;cAAAwD,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACvB,CAAC,eACbjF,OAAA,CAAC7C,UAAU;cAACsI,OAAO,EAAC,OAAO;cAACE,KAAK,EAAC,gBAAgB;cAAAd,QAAA,GAAC,aACtC,EAACnB,MAAM,CAACnC,SAAS,IAAI,KAAK;YAAA;cAAAuD,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC3B,CAAC,eACbjF,OAAA,CAAC7C,UAAU;cAACsI,OAAO,EAAC,OAAO;cAACE,KAAK,EAAC,gBAAgB;cAAAd,QAAA,GAAC,oBAClC,EAACnB,MAAM,CAAClC,YAAY,IAAI,KAAK;YAAA;cAAAsD,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAClC,CAAC,eACbjF,OAAA,CAAC7C,UAAU;cAACsI,OAAO,EAAC,OAAO;cAACE,KAAK,EAAC,gBAAgB;cAAAd,QAAA,GAAC,WACxC,EAACnB,MAAM,CAACjC,OAAO,IAAI,KAAK;YAAA;cAAAqD,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACvB,CAAC,eACbjF,OAAA,CAAC7C,UAAU;cAACsI,OAAO,EAAC,OAAO;cAACE,KAAK,EAAC,gBAAgB;cAAAd,QAAA,GAAC,gBACnC,EAACnB,MAAM,CAAChC,YAAY,IAAI,KAAK,EAAC,IAC9C;YAAA;cAAAoD,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAY,CAAC,eACbjF,OAAA,CAAC7C,UAAU;cAACsI,OAAO,EAAC,OAAO;cAACE,KAAK,EAAC,gBAAgB;cAAAd,QAAA,GAAC,iBAClC,EAACnB,MAAM,CAAC/B,aAAa,IAAI,KAAK,EAAC,IAChD;YAAA;cAAAmD,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAY,CAAC,eACbjF,OAAA,CAAC7C,UAAU;cAACsI,OAAO,EAAC,OAAO;cAACE,KAAK,EAAC,gBAAgB;cAAAd,QAAA,GAAC,SAC1C,EAACnB,MAAM,CAAC9B,YAAY,IAAI,aAAa;YAAA;cAAAkD,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAClC,CAAC,eACbjF,OAAA,CAAC7C,UAAU;cAACsI,OAAO,EAAC,OAAO;cAACE,KAAK,EAAC,gBAAgB;cAAAd,QAAA,GAAC,cACrC,EAACnB,MAAM,CAAC7B,iBAAiB,IAAI,KAAK;YAAA;cAAAiD,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACpC,CAAC,eACbjF,OAAA,CAAC7C,UAAU;cAACsI,OAAO,EAAC,OAAO;cAACE,KAAK,EAAC,gBAAgB;cAAAd,QAAA,GAAC,aACtC,EAACnB,MAAM,CAAC5B,SAAS,IAAI,KAAK;YAAA;cAAAgD,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC3B,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACF,CAAC,eACdjF,OAAA,CAACvC,WAAW;YAAAoH,QAAA,gBACV7E,OAAA,CAAC5C,MAAM;cACLwI,IAAI,EAAC,OAAO;cACZC,SAAS,eAAE7F,OAAA,CAACZ,QAAQ;gBAAA0F,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAE;cACxBa,OAAO,EAAEA,CAAA,KAAM;gBACb9E,aAAa,CAAC,iBAAiB,CAAC;gBAChCyC,kBAAkB,CAACC,MAAM,CAAC;cAC5B,CAAE;cAAAmB,QAAA,EACH;YAED;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC,eACTjF,OAAA,CAAC5C,MAAM;cACLwI,IAAI,EAAC,OAAO;cACZD,KAAK,EAAC,OAAO;cACbE,SAAS,eAAE7F,OAAA,CAACV,UAAU;gBAAAwF,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAE;cAC1Ba,OAAO,EAAEA,CAAA,KAAM;gBACb9E,aAAa,CAAC,eAAe,CAAC;gBAC9BE,iBAAiB,CAACwC,MAAM,CAAC;gBACzB5C,aAAa,CAAC,IAAI,CAAC;cACrB,CAAE;cAAA+D,QAAA,EACH;YAED;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACE,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACV;MAAC,GA1D6BvB,MAAM,CAACrC,aAAa;QAAAyD,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OA2DpD,CACP;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACE,CAAC;EAEX,CAAC;;EAED;EACA,MAAMc,YAAY,GAAGA,CAAA,KAAM;IACzB,IAAIhF,UAAU,KAAK,YAAY,IAAIA,UAAU,KAAK,gBAAgB,EAAE;MAClE,oBACEf,OAAA,CAACtC,MAAM;QAACsI,IAAI,EAAEnF,UAAW;QAACoF,OAAO,EAAEzC,iBAAkB;QAAC0C,QAAQ,EAAC,IAAI;QAACC,SAAS;QAAAtB,QAAA,gBAC3E7E,OAAA,CAACrC,WAAW;UAAAkH,QAAA,EACT9D,UAAU,KAAK,YAAY,GAAG,mBAAmB,GAAG;QAAiB;UAAA+D,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC3D,CAAC,eACdjF,OAAA,CAACpC,aAAa;UAAAiH,QAAA,eACZ7E,OAAA,CAAC1C,IAAI;YAAC4H,SAAS;YAACC,OAAO,EAAE,CAAE;YAACiB,EAAE,EAAE;cAAEC,EAAE,EAAE;YAAE,CAAE;YAAAxB,QAAA,gBACxC7E,OAAA,CAAC1C,IAAI;cAAC+H,IAAI;cAACC,EAAE,EAAE,EAAG;cAACC,EAAE,EAAE,CAAE;cAAAV,QAAA,eACvB7E,OAAA,CAAClC,SAAS;gBACR+F,IAAI,EAAC,eAAe;gBACpByC,KAAK,EAAC,eAAe;gBACrBH,SAAS;gBACTV,OAAO,EAAC,UAAU;gBAClB3B,KAAK,EAAE3C,QAAQ,CAACE,aAAc;gBAC9BkF,QAAQ,EAAE5C,gBAAiB;gBAC3B6C,QAAQ,EAAEzF,UAAU,KAAK,gBAAgB,IAAKA,UAAU,KAAK,YAAY,IAAII,QAAQ,CAACc,cAAc,KAAK,GAAK;gBAC9GwE,QAAQ;gBACRC,UAAU,EAAE3F,UAAU,KAAK,YAAY,IAAII,QAAQ,CAACc,cAAc,KAAK,GAAG,GAAG,iCAAiC,GAAG;cAAG;gBAAA6C,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACrH;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACE,CAAC,eACPjF,OAAA,CAAC1C,IAAI;cAAC+H,IAAI;cAACC,EAAE,EAAE,EAAG;cAACC,EAAE,EAAE,CAAE;cAAAV,QAAA,eACvB7E,OAAA,CAAClC,SAAS;gBACR+F,IAAI,EAAC,SAAS;gBACdyC,KAAK,EAAC,SAAS;gBACfH,SAAS;gBACTV,OAAO,EAAC,UAAU;gBAClB3B,KAAK,EAAE3C,QAAQ,CAACG,OAAQ;gBACxBiF,QAAQ,EAAE5C,gBAAiB;gBAC3B8C,QAAQ;cAAA;gBAAA3B,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACT;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACE,CAAC,eACPjF,OAAA,CAAC1C,IAAI;cAAC+H,IAAI;cAACC,EAAE,EAAE,EAAG;cAACC,EAAE,EAAE,CAAE;cAAAV,QAAA,eACvB7E,OAAA,CAAClC,SAAS;gBACR+F,IAAI,EAAC,WAAW;gBAChByC,KAAK,EAAC,WAAW;gBACjBH,SAAS;gBACTV,OAAO,EAAC,UAAU;gBAClB3B,KAAK,EAAE3C,QAAQ,CAACI,SAAU;gBAC1BgF,QAAQ,EAAE5C,gBAAiB;gBAC3B8C,QAAQ;cAAA;gBAAA3B,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACT;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACE,CAAC,eACPjF,OAAA,CAAC1C,IAAI;cAAC+H,IAAI;cAACC,EAAE,EAAE,EAAG;cAACC,EAAE,EAAE,CAAE;cAAAV,QAAA,eACvB7E,OAAA,CAAClC,SAAS;gBACR+F,IAAI,EAAC,cAAc;gBACnByC,KAAK,EAAC,kBAAe;gBACrBH,SAAS;gBACTV,OAAO,EAAC,UAAU;gBAClB3B,KAAK,EAAE3C,QAAQ,CAACK,YAAa;gBAC7B+E,QAAQ,EAAE5C,gBAAiB;gBAC3B8C,QAAQ;cAAA;gBAAA3B,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACT;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACE,CAAC,eACPjF,OAAA,CAAC1C,IAAI;cAAC+H,IAAI;cAACC,EAAE,EAAE,EAAG;cAACC,EAAE,EAAE,CAAE;cAAAV,QAAA,eACvB7E,OAAA,CAAClC,SAAS;gBACR+F,IAAI,EAAC,SAAS;gBACdyC,KAAK,EAAC,SAAS;gBACfH,SAAS;gBACTV,OAAO,EAAC,UAAU;gBAClB3B,KAAK,EAAE3C,QAAQ,CAACM,OAAQ;gBACxB8E,QAAQ,EAAE5C,gBAAiB;gBAC3B8C,QAAQ;cAAA;gBAAA3B,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACT;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACE,CAAC,eACPjF,OAAA,CAAC1C,IAAI;cAAC+H,IAAI;cAACC,EAAE,EAAE,EAAG;cAACC,EAAE,EAAE,CAAE;cAAAV,QAAA,eACvB7E,OAAA,CAAClC,SAAS;gBACR+F,IAAI,EAAC,cAAc;gBACnByC,KAAK,EAAC,cAAc;gBACpBK,IAAI,EAAC,QAAQ;gBACbR,SAAS;gBACTV,OAAO,EAAC,UAAU;gBAClB3B,KAAK,EAAE3C,QAAQ,CAACO,YAAa;gBAC7B6E,QAAQ,EAAE5C,gBAAiB;gBAC3B8C,QAAQ;cAAA;gBAAA3B,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACT;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACE,CAAC,eACPjF,OAAA,CAAC1C,IAAI;cAAC+H,IAAI;cAACC,EAAE,EAAE,EAAG;cAACC,EAAE,EAAE,CAAE;cAAAV,QAAA,eACvB7E,OAAA,CAAClC,SAAS;gBACR+F,IAAI,EAAC,eAAe;gBACpByC,KAAK,EAAC,eAAe;gBACrBK,IAAI,EAAC,QAAQ;gBACbR,SAAS;gBACTV,OAAO,EAAC,UAAU;gBAClB3B,KAAK,EAAE3C,QAAQ,CAACQ,aAAc;gBAC9B4E,QAAQ,EAAE5C,gBAAiB;gBAC3B8C,QAAQ;cAAA;gBAAA3B,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACT;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACE,CAAC,eACPjF,OAAA,CAAC1C,IAAI;cAAC+H,IAAI;cAACC,EAAE,EAAE,EAAG;cAACC,EAAE,EAAE,CAAE;cAAAV,QAAA,eACvB7E,OAAA,CAACjC,WAAW;gBAACoI,SAAS;gBAAAtB,QAAA,gBACpB7E,OAAA,CAAChC,UAAU;kBAAC4I,EAAE,EAAC,oBAAoB;kBAAA/B,QAAA,EAAC;gBAAY;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAY,CAAC,eAC7DjF,OAAA,CAAC/B,MAAM;kBACL4I,OAAO,EAAC,oBAAoB;kBAC5BhD,IAAI,EAAC,cAAc;kBACnBC,KAAK,EAAE3C,QAAQ,CAACS,YAAa;kBAC7B0E,KAAK,EAAC,cAAc;kBACpBC,QAAQ,EAAE5C,gBAAiB;kBAAAkB,QAAA,gBAE3B7E,OAAA,CAAC9B,QAAQ;oBAAC4F,KAAK,EAAC,aAAa;oBAAAe,QAAA,EAAC;kBAAW;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAU,CAAC,eACpDjF,OAAA,CAAC9B,QAAQ;oBAAC4F,KAAK,EAAC,QAAQ;oBAAAe,QAAA,EAAC;kBAAM;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAU,CAAC,eAC1CjF,OAAA,CAAC9B,QAAQ;oBAAC4F,KAAK,EAAC,WAAW;oBAAAe,QAAA,EAAC;kBAAS;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAU,CAAC,eAChDjF,OAAA,CAAC9B,QAAQ;oBAAC4F,KAAK,EAAC,aAAa;oBAAAe,QAAA,EAAC;kBAAW;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAU,CAAC,eACpDjF,OAAA,CAAC9B,QAAQ;oBAAC4F,KAAK,EAAC,MAAM;oBAAAe,QAAA,EAAC;kBAAI;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAU,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAChC,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACE;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACV,CAAC,eACPjF,OAAA,CAAC1C,IAAI;cAAC+H,IAAI;cAACC,EAAE,EAAE,EAAG;cAACC,EAAE,EAAE,CAAE;cAAAV,QAAA,eACvB7E,OAAA,CAAClC,SAAS;gBACR+F,IAAI,EAAC,mBAAmB;gBACxByC,KAAK,EAAC,mBAAmB;gBACzBH,SAAS;gBACTV,OAAO,EAAC,UAAU;gBAClB3B,KAAK,EAAE3C,QAAQ,CAACU,iBAAkB;gBAClC0E,QAAQ,EAAE5C;cAAiB;gBAAAmB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC5B;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACE,CAAC,eACPjF,OAAA,CAAC1C,IAAI;cAAC+H,IAAI;cAACC,EAAE,EAAE,EAAG;cAACC,EAAE,EAAE,CAAE;cAAAV,QAAA,eACvB7E,OAAA,CAAClC,SAAS;gBACR+F,IAAI,EAAC,WAAW;gBAChByC,KAAK,EAAC,WAAW;gBACjBH,SAAS;gBACTV,OAAO,EAAC,UAAU;gBAClB3B,KAAK,EAAE3C,QAAQ,CAACW,SAAU;gBAC1ByE,QAAQ,EAAE5C;cAAiB;gBAAAmB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC5B;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACE,CAAC,eACPjF,OAAA,CAAC1C,IAAI;cAAC+H,IAAI;cAACC,EAAE,EAAE,EAAG;cAACC,EAAE,EAAE,CAAE;cAAAV,QAAA,eACvB7E,OAAA,CAAClC,SAAS;gBACR+F,IAAI,EAAC,OAAO;gBACZyC,KAAK,EAAC,YAAY;gBAClBH,SAAS;gBACTV,OAAO,EAAC,UAAU;gBAClB3B,KAAK,EAAE3C,QAAQ,CAACY,KAAM;gBACtBwE,QAAQ,EAAE5C;cAAiB;gBAAAmB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC5B;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACE,CAAC,eACPjF,OAAA,CAAC1C,IAAI;cAAC+H,IAAI;cAACC,EAAE,EAAE,EAAG;cAACC,EAAE,EAAE,CAAE;cAAAV,QAAA,eACvB7E,OAAA,CAAClC,SAAS;gBACR+F,IAAI,EAAC,UAAU;gBACfyC,KAAK,EAAC,uBAAuB;gBAC7BH,SAAS;gBACTV,OAAO,EAAC,UAAU;gBAClB3B,KAAK,EAAE3C,QAAQ,CAACa,QAAS;gBACzBuE,QAAQ,EAAE5C,gBAAiB;gBAC3BmD,WAAW,EAAC;cAAY;gBAAAhC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACzB;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACE,CAAC,eACPjF,OAAA,CAAC1C,IAAI;cAAC+H,IAAI;cAACC,EAAE,EAAE,EAAG;cAACC,EAAE,EAAE,CAAE;cAAAV,QAAA,eACvB7E,OAAA,CAAClC,SAAS;gBACR+F,IAAI,EAAC,gBAAgB;gBACrByC,KAAK,EAAC,gBAAgB;gBACtBH,SAAS;gBACTV,OAAO,EAAC,UAAU;gBAClB3B,KAAK,EAAE3C,QAAQ,CAACc,cAAe;gBAC/BsE,QAAQ,EAAE5C,gBAAiB;gBAC3B6C,QAAQ,EAAE,IAAK;gBACfE,UAAU,EAAEvF,QAAQ,CAACc,cAAc,KAAK,GAAG,GAAG,wBAAwB,GAAG;cAAgC;gBAAA6C,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC1G;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACE,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACM,CAAC,eAChBjF,OAAA,CAACnC,aAAa;UAAAgH,QAAA,gBACZ7E,OAAA,CAAC5C,MAAM;YAAC0I,OAAO,EAAEtC,iBAAkB;YAAAqB,QAAA,EAAC;UAAO;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC,eACpDjF,OAAA,CAAC5C,MAAM;YACL0I,OAAO,EAAE9B,UAAW;YACpBwC,QAAQ,EAAEjG,OAAO,IAAKQ,UAAU,KAAK,YAAY,IAAII,QAAQ,CAACc,cAAc,KAAK,GAAG,IAAI,CAACd,QAAQ,CAACE,aAAe;YACjHwE,SAAS,EAAEtF,OAAO,gBAAGP,OAAA,CAACtB,gBAAgB;cAACkH,IAAI,EAAE;YAAG;cAAAd,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,gBAAGjF,OAAA,CAACN,QAAQ;cAAAoF,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAE;YAAAJ,QAAA,EACpE;UAED;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACI,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACV,CAAC;IAEb,CAAC,MAAM,IAAIlE,UAAU,KAAK,iBAAiB,EAAE;MAC3C,oBACEf,OAAA,CAACtC,MAAM;QAACsI,IAAI,EAAEnF,UAAW;QAACoF,OAAO,EAAEzC,iBAAkB;QAAC0C,QAAQ,EAAC,IAAI;QAACC,SAAS;QAAAtB,QAAA,gBAC3E7E,OAAA,CAACrC,WAAW;UAAAkH,QAAA,EAAC;QAA8B;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAa,CAAC,eACzDjF,OAAA,CAACpC,aAAa;UAAAiH,QAAA,EACXtE,OAAO,gBACNP,OAAA,CAACtB,gBAAgB;YAAAoG,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,GAClBxE,MAAM,CAACkE,MAAM,KAAK,CAAC,gBACrB3E,OAAA,CAACvB,KAAK;YAACmG,QAAQ,EAAC,MAAM;YAAAC,QAAA,EAAC;UAA0B;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAO,CAAC,gBAEzDjF,OAAA,CAAC7B,IAAI;YAAA0G,QAAA,EACFpE,MAAM,CAAC2E,GAAG,CAAE1B,MAAM,iBACjB1D,OAAA,CAAC5B,QAAQ;cACP2I,MAAM;cAENjB,OAAO,EAAEA,CAAA,KAAMrC,kBAAkB,CAACC,MAAM,CAAE;cAAAmB,QAAA,eAE1C7E,OAAA,CAAC3B,YAAY;gBACX2I,OAAO,EAAE,WAAWtD,MAAM,CAACrC,aAAa,EAAG;gBAC3C4F,SAAS,EAAE,cAAcvD,MAAM,CAACnC,SAAS,IAAI,KAAK,eAAemC,MAAM,CAACpC,OAAO,IAAI,KAAK,eAAeoC,MAAM,CAAC/B,aAAa,IAAI,KAAK;cAAK;gBAAAmD,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC1I;YAAC,GANGvB,MAAM,CAACrC,aAAa;cAAAyD,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAOjB,CACX;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACE;QACP;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACY,CAAC,eAChBjF,OAAA,CAACnC,aAAa;UAAAgH,QAAA,eACZ7E,OAAA,CAAC5C,MAAM;YAAC0I,OAAO,EAAEtC,iBAAkB;YAAAqB,QAAA,EAAC;UAAO;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACvC,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACV,CAAC;IAEb,CAAC,MAAM,IAAIlE,UAAU,KAAK,eAAe,EAAE;MACzC,oBACEf,OAAA,CAACtC,MAAM;QAACsI,IAAI,EAAEnF,UAAW;QAACoF,OAAO,EAAEzC,iBAAkB;QAAC0C,QAAQ,EAAC,IAAI;QAACC,SAAS;QAAAtB,QAAA,gBAC3E7E,OAAA,CAACrC,WAAW;UAAAkH,QAAA,EAAC;QAAc;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAa,CAAC,eACzCjF,OAAA,CAACpC,aAAa;UAAAiH,QAAA,EACX,CAAC5D,cAAc,GACdV,OAAO,gBACLP,OAAA,CAACtB,gBAAgB;YAAAoG,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,GAClBxE,MAAM,CAACkE,MAAM,KAAK,CAAC,gBACrB3E,OAAA,CAACvB,KAAK;YAACmG,QAAQ,EAAC,MAAM;YAAAC,QAAA,EAAC;UAA0B;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAO,CAAC,gBAEzDjF,OAAA,CAAC7B,IAAI;YAAA0G,QAAA,EACFpE,MAAM,CAAC2E,GAAG,CAAE1B,MAAM,iBACjB1D,OAAA,CAAC5B,QAAQ;cACP2I,MAAM;cAENjB,OAAO,EAAEA,CAAA,KAAM5E,iBAAiB,CAACwC,MAAM,CAAE;cAAAmB,QAAA,eAEzC7E,OAAA,CAAC3B,YAAY;gBACX2I,OAAO,EAAE,WAAWtD,MAAM,CAACrC,aAAa,EAAG;gBAC3C4F,SAAS,EAAE,cAAcvD,MAAM,CAACnC,SAAS,IAAI,KAAK,eAAemC,MAAM,CAACpC,OAAO,IAAI,KAAK,eAAeoC,MAAM,CAAC/B,aAAa,IAAI,KAAK;cAAK;gBAAAmD,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC1I;YAAC,GANGvB,MAAM,CAACrC,aAAa;cAAAyD,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAOjB,CACX;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACE,CACP,gBAEDjF,OAAA,CAAC9C,GAAG;YAAA2H,QAAA,gBACF7E,OAAA,CAACvB,KAAK;cAACmG,QAAQ,EAAC,SAAS;cAACwB,EAAE,EAAE;gBAAEc,EAAE,EAAE;cAAE,CAAE;cAAArC,QAAA,GAAC,0CACC,EAAC5D,cAAc,CAACI,aAAa,EAAC,GACxE;YAAA;cAAAyD,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CAAC,eACRjF,OAAA,CAAC7C,UAAU;cAACsI,OAAO,EAAC,OAAO;cAAAZ,QAAA,EAAC;YAE5B;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAY,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACV;QACN;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACY,CAAC,eAChBjF,OAAA,CAACnC,aAAa;UAAAgH,QAAA,gBACZ7E,OAAA,CAAC5C,MAAM;YAAC0I,OAAO,EAAEtC,iBAAkB;YAAAqB,QAAA,EAAC;UAAO;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC,EACnDhE,cAAc,iBACbjB,OAAA,CAAC5C,MAAM;YACL0I,OAAO,EAAE9B,UAAW;YACpBwC,QAAQ,EAAEjG,OAAQ;YAClBoF,KAAK,EAAC,OAAO;YACbE,SAAS,EAAEtF,OAAO,gBAAGP,OAAA,CAACtB,gBAAgB;cAACkH,IAAI,EAAE;YAAG;cAAAd,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,gBAAGjF,OAAA,CAACV,UAAU;cAAAwF,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAE;YAAAJ,QAAA,EACtE;UAED;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CACT;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACY,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACV,CAAC;IAEb,CAAC,MAAM,IAAIlE,UAAU,KAAK,mBAAmB,EAAE;MAC7C,oBACEf,OAAA,CAACtC,MAAM;QAACsI,IAAI,EAAEnF,UAAW;QAACoF,OAAO,EAAEzC,iBAAkB;QAAC0C,QAAQ,EAAC,IAAI;QAACC,SAAS;QAAAtB,QAAA,gBAC3E7E,OAAA,CAACrC,WAAW;UAAAkH,QAAA,EAAC;QAAuB;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAa,CAAC,eAClDjF,OAAA,CAACpC,aAAa;UAAAiH,QAAA,EACXtE,OAAO,gBACNP,OAAA,CAACtB,gBAAgB;YAAAoG,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,GAClB/C,eAAe,CAACyC,MAAM,KAAK,CAAC,gBAC9B3E,OAAA,CAACvB,KAAK;YAACmG,QAAQ,EAAC,MAAM;YAAAC,QAAA,EAAC;UAA+B;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAO,CAAC,gBAE9DjF,OAAA,CAAClB,cAAc;YAAC4G,SAAS,EAAErI,KAAM;YAAC+I,EAAE,EAAE;cAAEC,EAAE,EAAE;YAAE,CAAE;YAAAxB,QAAA,eAC9C7E,OAAA,CAACrB,KAAK;cAACiH,IAAI,EAAC,OAAO;cAAAf,QAAA,gBACjB7E,OAAA,CAACjB,SAAS;gBAAA8F,QAAA,eACR7E,OAAA,CAAChB,QAAQ;kBAAA6F,QAAA,gBACP7E,OAAA,CAACnB,SAAS;oBAAAgG,QAAA,EAAC;kBAAM;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAW,CAAC,eAC7BjF,OAAA,CAACnB,SAAS;oBAAAgG,QAAA,EAAC;kBAAO;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAW,CAAC,eAC9BjF,OAAA,CAACnB,SAAS;oBAAAgG,QAAA,EAAC;kBAAS;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAW,CAAC,eAChCjF,OAAA,CAACnB,SAAS;oBAAAgG,QAAA,EAAC;kBAAa;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAW,CAAC,eACpCjF,OAAA,CAACnB,SAAS;oBAAAgG,QAAA,EAAC;kBAAO;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAW,CAAC,eAC9BjF,OAAA,CAACnB,SAAS;oBAAAgG,QAAA,EAAC;kBAAY;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAW,CAAC,eACnCjF,OAAA,CAACnB,SAAS;oBAAAgG,QAAA,EAAC;kBAAa;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAW,CAAC,eACpCjF,OAAA,CAACnB,SAAS;oBAAAgG,QAAA,EAAC;kBAAc;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAW,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAC7B;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACF,CAAC,eACZjF,OAAA,CAACpB,SAAS;gBAAAiG,QAAA,EACP3C,eAAe,CAACkD,GAAG,CAAC,CAAC+B,MAAM,EAAEC,KAAK,kBACjCpH,OAAA,CAAChB,QAAQ;kBAAA6F,QAAA,gBACP7E,OAAA,CAACnB,SAAS;oBAAAgG,QAAA,EAAEsC,MAAM,CAAC9F;kBAAa;oBAAAyD,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAY,CAAC,eAC7CjF,OAAA,CAACnB,SAAS;oBAAAgG,QAAA,EAAEsC,MAAM,CAAC7F;kBAAO;oBAAAwD,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAY,CAAC,eACvCjF,OAAA,CAACnB,SAAS;oBAAAgG,QAAA,EAAEsC,MAAM,CAAC5F;kBAAS;oBAAAuD,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAY,CAAC,eACzCjF,OAAA,CAACnB,SAAS;oBAAAgG,QAAA,EAAEsC,MAAM,CAAC3F;kBAAY;oBAAAsD,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAY,CAAC,eAC5CjF,OAAA,CAACnB,SAAS;oBAAAgG,QAAA,EAAEsC,MAAM,CAAC1F;kBAAO;oBAAAqD,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAY,CAAC,eACvCjF,OAAA,CAACnB,SAAS;oBAAAgG,QAAA,EAAEsC,MAAM,CAACzF;kBAAY;oBAAAoD,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAY,CAAC,eAC5CjF,OAAA,CAACnB,SAAS;oBAAAgG,QAAA,EAAEsC,MAAM,CAACxF;kBAAa;oBAAAmD,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAY,CAAC,eAC7CjF,OAAA,CAACnB,SAAS;oBAAAgG,QAAA,EAAEsC,MAAM,CAACE,IAAI,CAAC1C;kBAAM;oBAAAG,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAY,CAAC;gBAAA,GAR9BmC,KAAK;kBAAAtC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OASV,CACX;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACO,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACP;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACM;QACjB;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACY,CAAC,eAChBjF,OAAA,CAACnC,aAAa;UAAAgH,QAAA,eACZ7E,OAAA,CAAC5C,MAAM;YAAC0I,OAAO,EAAEtC,iBAAkB;YAAAqB,QAAA,EAAC;UAAM;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACtC,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACV,CAAC;IAEb;IAEA,OAAO,IAAI;EACb,CAAC;EAED,oBACEjF,OAAA,CAAC9C,GAAG;IAAA2H,QAAA,GACDlE,cAAc,KAAK,kBAAkB,IAAI,CAACE,UAAU,gBACnDb,OAAA,CAAC3C,KAAK;MAAC+I,EAAE,EAAE;QAAEkB,CAAC,EAAE;MAAE,CAAE;MAAAzC,QAAA,gBAClB7E,OAAA,CAAC7C,UAAU;QAACsI,OAAO,EAAC,IAAI;QAAC8B,YAAY;QAAA1C,QAAA,EAAC;MAEtC;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAY,CAAC,EACZ1E,OAAO,gBACNP,OAAA,CAAC9C,GAAG;QAACkJ,EAAE,EAAE;UAAEoB,OAAO,EAAE,MAAM;UAAEC,cAAc,EAAE,QAAQ;UAAEC,EAAE,EAAE;QAAE,CAAE;QAAA7C,QAAA,eAC5D7E,OAAA,CAACtB,gBAAgB;UAAAoG,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACjB,CAAC,GAENP,iBAAiB,CAAC,CACnB;IAAA;MAAAI,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACI,CAAC,GACN,CAACpE,UAAU,gBACbb,OAAA,CAAC3C,KAAK;MAAC+I,EAAE,EAAE;QAAEkB,CAAC,EAAE,CAAC;QAAEK,SAAS,EAAE,OAAO;QAAEH,OAAO,EAAE,MAAM;QAAEI,UAAU,EAAE,QAAQ;QAAEH,cAAc,EAAE;MAAS,CAAE;MAAA5C,QAAA,EACtG,CAAClE,cAAc,gBACdX,OAAA,CAAC7C,UAAU;QAACsI,OAAO,EAAC,OAAO;QAAAZ,QAAA,EAAC;MAE5B;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAY,CAAC,gBAEbjF,OAAA,CAAC9C,GAAG;QAACkJ,EAAE,EAAE;UAAEyB,SAAS,EAAE;QAAS,CAAE;QAAAhD,QAAA,gBAC/B7E,OAAA,CAAC7C,UAAU;UAACsI,OAAO,EAAC,IAAI;UAAC8B,YAAY;UAAA1C,QAAA,GAClClE,cAAc,KAAK,YAAY,IAAI,mBAAmB,EACtDA,cAAc,KAAK,gBAAgB,IAAI,iBAAiB,EACxDA,cAAc,KAAK,eAAe,IAAI,gBAAgB,EACtDA,cAAc,KAAK,mBAAmB,IAAI,6BAA6B;QAAA;UAAAmE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC9D,CAAC,eACbjF,OAAA,CAACtB,gBAAgB;UAAC0H,EAAE,EAAE;YAAEC,EAAE,EAAE;UAAE;QAAE;UAAAvB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAChC;IACN;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACI,CAAC,GACN,IAAI,EAEPc,YAAY,CAAC,CAAC,eAGf/F,OAAA,CAACF,oBAAoB;MACnBkG,IAAI,EAAE5D,gBAAiB;MACvB6D,OAAO,EAAEA,CAAA,KAAM5D,mBAAmB,CAAC,KAAK,CAAE;MAC1CyF,SAAS,EAAE3E;IAAoB;MAAA2B,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAChC,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACC,CAAC;AAEV,CAAC;AAAC3E,EAAA,CA5qBIL,SAAS;AAAA8H,EAAA,GAAT9H,SAAS;AA8qBf,eAAeA,SAAS;AAAC,IAAA8H,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}