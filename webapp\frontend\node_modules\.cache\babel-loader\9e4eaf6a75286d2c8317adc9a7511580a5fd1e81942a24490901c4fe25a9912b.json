{"ast": null, "code": "const formatRelativeLocale = {\n  lastWeek: \"'el' eeee 'pasado a la' p\",\n  yesterday: \"'ayer a la' p\",\n  today: \"'hoy a la' p\",\n  tomorrow: \"'mañana a la' p\",\n  nextWeek: \"eeee 'a la' p\",\n  other: \"P\"\n};\nconst formatRelativeLocalePlural = {\n  lastWeek: \"'el' eeee 'pasado a las' p\",\n  yesterday: \"'ayer a las' p\",\n  today: \"'hoy a las' p\",\n  tomorrow: \"'mañana a las' p\",\n  nextWeek: \"eeee 'a las' p\",\n  other: \"P\"\n};\nexport const formatRelative = (token, date, _baseDate, _options) => {\n  if (date.getHours() !== 1) {\n    return formatRelativeLocalePlural[token];\n  } else {\n    return formatRelativeLocale[token];\n  }\n};", "map": {"version": 3, "names": ["formatRelativeLocale", "lastWeek", "yesterday", "today", "tomorrow", "nextWeek", "other", "formatRelativeLocalePlural", "formatRelative", "token", "date", "_baseDate", "_options", "getHours"], "sources": ["C:/CMS/webapp/frontend/node_modules/date-fns/locale/es/_lib/formatRelative.js"], "sourcesContent": ["const formatRelativeLocale = {\n  lastWeek: \"'el' eeee 'pasado a la' p\",\n  yesterday: \"'ayer a la' p\",\n  today: \"'hoy a la' p\",\n  tomorrow: \"'mañana a la' p\",\n  nextWeek: \"eeee 'a la' p\",\n  other: \"P\",\n};\n\nconst formatRelativeLocalePlural = {\n  lastWeek: \"'el' eeee 'pasado a las' p\",\n  yesterday: \"'ayer a las' p\",\n  today: \"'hoy a las' p\",\n  tomorrow: \"'mañana a las' p\",\n  nextWeek: \"eeee 'a las' p\",\n  other: \"P\",\n};\n\nexport const formatRelative = (token, date, _baseDate, _options) => {\n  if (date.getHours() !== 1) {\n    return formatRelativeLocalePlural[token];\n  } else {\n    return formatRelativeLocale[token];\n  }\n};\n"], "mappings": "AAAA,MAAMA,oBAAoB,GAAG;EAC3BC,QAAQ,EAAE,2BAA2B;EACrCC,SAAS,EAAE,eAAe;EAC1BC,KAAK,EAAE,cAAc;EACrBC,QAAQ,EAAE,iBAAiB;EAC3BC,QAAQ,EAAE,eAAe;EACzBC,KAAK,EAAE;AACT,CAAC;AAED,MAAMC,0BAA0B,GAAG;EACjCN,QAAQ,EAAE,4BAA4B;EACtCC,SAAS,EAAE,gBAAgB;EAC3BC,KAAK,EAAE,eAAe;EACtBC,QAAQ,EAAE,kBAAkB;EAC5BC,QAAQ,EAAE,gBAAgB;EAC1BC,KAAK,EAAE;AACT,CAAC;AAED,OAAO,MAAME,cAAc,GAAGA,CAACC,KAAK,EAAEC,IAAI,EAAEC,SAAS,EAAEC,QAAQ,KAAK;EAClE,IAAIF,IAAI,CAACG,QAAQ,CAAC,CAAC,KAAK,CAAC,EAAE;IACzB,OAAON,0BAA0B,CAACE,KAAK,CAAC;EAC1C,CAAC,MAAM;IACL,OAAOT,oBAAoB,CAACS,KAAK,CAAC;EACpC;AACF,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}