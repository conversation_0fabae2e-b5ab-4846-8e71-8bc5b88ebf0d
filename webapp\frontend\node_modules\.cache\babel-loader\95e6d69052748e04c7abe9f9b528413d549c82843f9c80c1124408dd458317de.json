{"ast": null, "code": "import React,{useState}from'react';import{Dialog,DialogTitle,DialogContent,DialogActions,Button,Typography,Box,TextField,CircularProgress,Alert,IconButton,Divider}from'@mui/material';import{Close as CloseIcon,Upload as UploadIcon,Download as DownloadIcon,FileUpload as FileUploadIcon}from'@mui/icons-material';import excelService from'../../services/excelService';import{jsx as _jsx,jsxs as _jsxs,Fragment as _Fragment}from\"react/jsx-runtime\";const ExcelPopup=_ref=>{let{open,onClose,operationType,cantiereId,onSuccess,onError}=_ref;const[loading,setLoading]=useState(false);const[fileInput,setFileInput]=useState(null);const[filePath,setFilePath]=useState('');const[downloadUrl,setDownloadUrl]=useState('');// Reset state when dialog opens or operation changes\nReact.useEffect(()=>{if(open){setFileInput(null);setFilePath('');setDownloadUrl('');setLoading(false);}},[open,operationType]);// Gestisce la chiusura del dialog\nconst handleClose=()=>{setLoading(false);setFileInput(null);setFilePath('');setDownloadUrl('');onClose();};// Gestisce il cambio del file selezionato\nconst handleFileChange=e=>{setFileInput(e.target.files[0]);};// Gestisce l'importazione dei cavi da Excel\nconst handleImportaCavi=async()=>{try{if(!fileInput){onError('Seleziona un file Excel da importare');return;}setLoading(true);const formData=new FormData();formData.append('file',fileInput);await excelService.importCavi(cantiereId,formData);onSuccess('Cavi importati con successo');handleClose();}catch(error){onError('Errore nell\\'importazione dei cavi: '+(error.message||'Errore sconosciuto'));console.error('Errore nell\\'importazione dei cavi:',error);}finally{setLoading(false);}};// Gestisce l'importazione del parco bobine da Excel\nconst handleImportaParcoBobine=async()=>{try{if(!fileInput){onError('Seleziona un file Excel da importare');return;}setLoading(true);const formData=new FormData();formData.append('file',fileInput);await excelService.importParcoBobine(cantiereId,formData);onSuccess('Parco bobine importato con successo');handleClose();}finally{setLoading(false);}};// Gestisce la creazione del template Excel per i cavi\nconst handleCreaTemplateCavi=async()=>{try{setLoading(true);const response=await excelService.createCaviTemplate();if(response&&response.file_url){setDownloadUrl(response.file_url);onSuccess('Template Excel per cavi creato e download avviato! Controlla la cartella Download del tuo browser.');}else{onError('Errore nella creazione del template Excel per cavi');}}catch(error){onError('Errore nella creazione del template Excel per cavi: '+(error.message||'Errore sconosciuto'));console.error('Errore nella creazione del template Excel per cavi:',error);}finally{setLoading(false);}};// Gestisce la creazione del template Excel per il parco bobine\nconst handleCreaTemplateParcoBobine=async()=>{try{setLoading(true);const response=await excelService.createParcoBobineTemplate();if(response&&response.file_url){setDownloadUrl(response.file_url);onSuccess('Template Excel per parco bobine creato e download avviato! Controlla la cartella Download del tuo browser.');}else{onError('Errore nella creazione del template Excel per parco bobine');}}catch(error){onError('Errore nella creazione del template Excel per parco bobine: '+(error.message||'Errore sconosciuto'));console.error('Errore nella creazione del template Excel per parco bobine:',error);}finally{setLoading(false);}};// Gestisce l'esportazione dei cavi in Excel\nconst handleEsportaCavi=async()=>{try{setLoading(true);const response=await excelService.exportCavi(cantiereId);if(response&&response.file_url){setDownloadUrl(response.file_url);onSuccess('Cavi esportati con successo e download avviato! Controlla la cartella Download del tuo browser.');}else{onError('Errore nell\\'esportazione dei cavi');}}catch(error){onError('Errore nell\\'esportazione dei cavi: '+(error.message||'Errore sconosciuto'));console.error('Errore nell\\'esportazione dei cavi:',error);}finally{setLoading(false);}};// Gestisce l'esportazione del parco bobine in Excel\nconst handleEsportaParcoBobine=async()=>{try{setLoading(true);const response=await excelService.exportParcoBobine(cantiereId);if(response&&response.file_url){setDownloadUrl(response.file_url);onSuccess('Parco bobine esportato con successo e download avviato! Controlla la cartella Download del tuo browser.');}else{onError('Errore nell\\'esportazione del parco bobine');}}catch(error){onError('Errore nell\\'esportazione del parco bobine: '+(error.message||'Errore sconosciuto'));console.error('Errore nell\\'esportazione del parco bobine:',error);}finally{setLoading(false);}};// Esegue l'operazione selezionata\nconst executeOperation=()=>{switch(operationType){case'importaCavi':handleImportaCavi();break;case'importaParcoBobine':handleImportaParcoBobine();break;case'creaTemplateCavi':handleCreaTemplateCavi();break;case'creaTemplateParcoBobine':handleCreaTemplateParcoBobine();break;case'esportaCavi':handleEsportaCavi();break;case'esportaParcoBobine':handleEsportaParcoBobine();break;default:break;}};// Determina il titolo del dialog in base all'operazione\nconst getDialogTitle=()=>{switch(operationType){case'importaCavi':return'Importa Cavi da Excel';case'importaParcoBobine':return'Importa Parco Bobine da Excel';case'creaTemplateCavi':return'Crea Template Excel per Cavi';case'creaTemplateParcoBobine':return'Crea Template Excel per Parco Bobine';case'esportaCavi':return'Esporta Cavi in Excel';case'esportaParcoBobine':return'Esporta Parco Bobine in Excel';default:return'Gestione Excel';}};// Determina se mostrare il selettore di file\nconst showFileInput=['importaCavi','importaParcoBobine'].includes(operationType);// Determina se mostrare il link di download\nconst showDownloadLink=downloadUrl&&['creaTemplateCavi','creaTemplateParcoBobine','esportaCavi','esportaParcoBobine'].includes(operationType);// Determina il testo del pulsante di azione\nconst getActionButtonText=()=>{if(showDownloadLink)return'Chiudi';switch(operationType){case'importaCavi':case'importaParcoBobine':return'Importa';case'creaTemplateCavi':case'creaTemplateParcoBobine':return'Crea Template';case'esportaCavi':case'esportaParcoBobine':return'Esporta';default:return'Conferma';}};return/*#__PURE__*/_jsxs(Dialog,{open:open,onClose:handleClose,maxWidth:\"sm\",fullWidth:true,PaperProps:{sx:{borderRadius:'8px',boxShadow:'0 4px 20px rgba(0,0,0,0.1)'}},children:[/*#__PURE__*/_jsxs(DialogTitle,{sx:{display:'flex',justifyContent:'space-between',alignItems:'center',borderBottom:'1px solid #e0e0e0',pb:1},children:[/*#__PURE__*/_jsx(Typography,{variant:\"h6\",children:getDialogTitle()}),/*#__PURE__*/_jsx(IconButton,{onClick:handleClose,size:\"small\",children:/*#__PURE__*/_jsx(CloseIcon,{})})]}),/*#__PURE__*/_jsx(DialogContent,{sx:{pt:3,pb:2},children:loading?/*#__PURE__*/_jsxs(Box,{sx:{display:'flex',flexDirection:'column',alignItems:'center',py:3},children:[/*#__PURE__*/_jsx(CircularProgress,{size:40}),/*#__PURE__*/_jsx(Typography,{variant:\"body1\",sx:{mt:2},children:\"Elaborazione in corso...\"})]}):/*#__PURE__*/_jsxs(Box,{children:[showFileInput&&/*#__PURE__*/_jsxs(_Fragment,{children:[/*#__PURE__*/_jsx(Typography,{variant:\"body2\",paragraph:true,children:\"Seleziona un file Excel da importare:\"}),/*#__PURE__*/_jsxs(Box,{sx:{mb:2},children:[/*#__PURE__*/_jsxs(Button,{variant:\"outlined\",component:\"label\",startIcon:/*#__PURE__*/_jsx(UploadIcon,{}),sx:{mb:1},children:[\"Seleziona File\",/*#__PURE__*/_jsx(\"input\",{type:\"file\",accept:\".xlsx,.xls\",hidden:true,onChange:handleFileChange})]}),fileInput&&/*#__PURE__*/_jsxs(Typography,{variant:\"body2\",sx:{mt:1},children:[\"File selezionato: \",fileInput.name]})]}),/*#__PURE__*/_jsx(Alert,{severity:\"info\",sx:{mb:2},children:/*#__PURE__*/_jsxs(Typography,{variant:\"body2\",children:[\"Assicurati che il file Excel sia nel formato corretto.\",operationType==='importaCavi'&&' Puoi scaricare un template vuoto utilizzando l\\'opzione \"Template Cavi\".',operationType==='importaParcoBobine'&&' Puoi scaricare un template vuoto utilizzando l\\'opzione \"Template Parco Bobine\".']})})]}),['creaTemplateCavi','creaTemplateParcoBobine'].includes(operationType)&&!showDownloadLink&&/*#__PURE__*/_jsxs(Typography,{variant:\"body2\",paragraph:true,children:[\"Clicca su \\\"\",getActionButtonText(),\"\\\" per generare un template Excel vuoto.\"]}),['esportaCavi','esportaParcoBobine'].includes(operationType)&&!showDownloadLink&&/*#__PURE__*/_jsxs(Typography,{variant:\"body2\",paragraph:true,children:[\"Clicca su \\\"\",getActionButtonText(),\"\\\" per esportare i dati in un file Excel.\"]}),showDownloadLink&&/*#__PURE__*/_jsxs(Box,{sx:{mt:2,textAlign:'center'},children:[/*#__PURE__*/_jsx(Alert,{severity:\"success\",sx:{mb:2},children:/*#__PURE__*/_jsx(Typography,{variant:\"body2\",children:\"File generato con successo!\"})}),/*#__PURE__*/_jsx(Button,{variant:\"contained\",color:\"primary\",startIcon:/*#__PURE__*/_jsx(DownloadIcon,{}),href:downloadUrl,target:\"_blank\",rel:\"noopener noreferrer\",sx:{mt:1},children:\"Scarica File\"})]})]})}),/*#__PURE__*/_jsxs(DialogActions,{sx:{px:3,py:2,borderTop:'1px solid #e0e0e0'},children:[/*#__PURE__*/_jsx(Button,{onClick:handleClose,color:\"inherit\",variant:\"outlined\",sx:{mr:1},children:\"Annulla\"}),!showDownloadLink&&/*#__PURE__*/_jsx(Button,{onClick:executeOperation,color:\"primary\",variant:\"contained\",startIcon:showFileInput?/*#__PURE__*/_jsx(FileUploadIcon,{}):/*#__PURE__*/_jsx(DownloadIcon,{}),disabled:loading||showFileInput&&!fileInput,children:getActionButtonText()})]})]});};export default ExcelPopup;", "map": {"version": 3, "names": ["React", "useState", "Dialog", "DialogTitle", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "DialogActions", "<PERSON><PERSON>", "Typography", "Box", "TextField", "CircularProgress", "<PERSON><PERSON>", "IconButton", "Divider", "Close", "CloseIcon", "Upload", "UploadIcon", "Download", "DownloadIcon", "FileUpload", "FileUploadIcon", "excelService", "jsx", "_jsx", "jsxs", "_jsxs", "Fragment", "_Fragment", "ExcelPopup", "_ref", "open", "onClose", "operationType", "cantiereId", "onSuccess", "onError", "loading", "setLoading", "fileInput", "setFileInput", "filePath", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "downloadUrl", "setDownloadUrl", "useEffect", "handleClose", "handleFileChange", "e", "target", "files", "handleImportaCavi", "formData", "FormData", "append", "importCavi", "error", "message", "console", "handleImportaParcoBobine", "importParcoBobine", "handleCreaTemplateCavi", "response", "createCaviTemplate", "file_url", "handleCreaTemplateParcoBobine", "createParcoBobineTemplate", "handleEsportaCavi", "exportCavi", "handleEsportaParcoBobine", "exportParcoBobine", "executeOperation", "getDialogTitle", "showFileInput", "includes", "showDownloadLink", "getActionButtonText", "max<PERSON><PERSON><PERSON>", "fullWidth", "PaperProps", "sx", "borderRadius", "boxShadow", "children", "display", "justifyContent", "alignItems", "borderBottom", "pb", "variant", "onClick", "size", "pt", "flexDirection", "py", "mt", "paragraph", "mb", "component", "startIcon", "type", "accept", "hidden", "onChange", "name", "severity", "textAlign", "color", "href", "rel", "px", "borderTop", "mr", "disabled"], "sources": ["C:/Users/<USER>/PycharmProjects/CMS/webapp/frontend/src/components/cavi/ExcelPopup.js"], "sourcesContent": ["import React, { useState } from 'react';\nimport {\n  <PERSON>alog,\n  DialogTitle,\n  DialogContent,\n  DialogActions,\n  Button,\n  Typography,\n  Box,\n  TextField,\n  CircularProgress,\n  Alert,\n  IconButton,\n  Divider\n} from '@mui/material';\nimport {\n  Close as CloseIcon,\n  Upload as UploadIcon,\n  Download as DownloadIcon,\n  FileUpload as FileUploadIcon\n} from '@mui/icons-material';\nimport excelService from '../../services/excelService';\n\nconst ExcelPopup = ({ open, onClose, operationType, cantiereId, onSuccess, onError }) => {\n  const [loading, setLoading] = useState(false);\n  const [fileInput, setFileInput] = useState(null);\n  const [filePath, setFilePath] = useState('');\n  const [downloadUrl, setDownloadUrl] = useState('');\n\n  // Reset state when dialog opens or operation changes\n  React.useEffect(() => {\n    if (open) {\n      setFileInput(null);\n      setFilePath('');\n      setDownloadUrl('');\n      setLoading(false);\n    }\n  }, [open, operationType]);\n\n  // Gestisce la chiusura del dialog\n  const handleClose = () => {\n    setLoading(false);\n    setFileInput(null);\n    setFilePath('');\n    setDownloadUrl('');\n    onClose();\n  };\n\n  // Gestisce il cambio del file selezionato\n  const handleFileChange = (e) => {\n    setFileInput(e.target.files[0]);\n  };\n\n  // Gestisce l'importazione dei cavi da Excel\n  const handleImportaCavi = async () => {\n    try {\n      if (!fileInput) {\n        onError('Seleziona un file Excel da importare');\n        return;\n      }\n\n      setLoading(true);\n      const formData = new FormData();\n      formData.append('file', fileInput);\n\n      await excelService.importCavi(cantiereId, formData);\n      onSuccess('Cavi importati con successo');\n      handleClose();\n    } catch (error) {\n      onError('Errore nell\\'importazione dei cavi: ' + (error.message || 'Errore sconosciuto'));\n      console.error('Errore nell\\'importazione dei cavi:', error);\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  // Gestisce l'importazione del parco bobine da Excel\n  const handleImportaParcoBobine = async () => {\n    try {\n      if (!fileInput) {\n        onError('Seleziona un file Excel da importare');\n        return;\n      }\n\n      setLoading(true);\n      const formData = new FormData();\n      formData.append('file', fileInput);\n\n      await excelService.importParcoBobine(cantiereId, formData);\n      onSuccess('Parco bobine importato con successo');\n      handleClose();\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  // Gestisce la creazione del template Excel per i cavi\n  const handleCreaTemplateCavi = async () => {\n    try {\n      setLoading(true);\n      const response = await excelService.createCaviTemplate();\n\n      if (response && response.file_url) {\n        setDownloadUrl(response.file_url);\n        onSuccess('Template Excel per cavi creato e download avviato! Controlla la cartella Download del tuo browser.');\n      } else {\n        onError('Errore nella creazione del template Excel per cavi');\n      }\n    } catch (error) {\n      onError('Errore nella creazione del template Excel per cavi: ' + (error.message || 'Errore sconosciuto'));\n      console.error('Errore nella creazione del template Excel per cavi:', error);\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  // Gestisce la creazione del template Excel per il parco bobine\n  const handleCreaTemplateParcoBobine = async () => {\n    try {\n      setLoading(true);\n      const response = await excelService.createParcoBobineTemplate();\n\n      if (response && response.file_url) {\n        setDownloadUrl(response.file_url);\n        onSuccess('Template Excel per parco bobine creato e download avviato! Controlla la cartella Download del tuo browser.');\n      } else {\n        onError('Errore nella creazione del template Excel per parco bobine');\n      }\n    } catch (error) {\n      onError('Errore nella creazione del template Excel per parco bobine: ' + (error.message || 'Errore sconosciuto'));\n      console.error('Errore nella creazione del template Excel per parco bobine:', error);\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  // Gestisce l'esportazione dei cavi in Excel\n  const handleEsportaCavi = async () => {\n    try {\n      setLoading(true);\n      const response = await excelService.exportCavi(cantiereId);\n\n      if (response && response.file_url) {\n        setDownloadUrl(response.file_url);\n        onSuccess('Cavi esportati con successo e download avviato! Controlla la cartella Download del tuo browser.');\n      } else {\n        onError('Errore nell\\'esportazione dei cavi');\n      }\n    } catch (error) {\n      onError('Errore nell\\'esportazione dei cavi: ' + (error.message || 'Errore sconosciuto'));\n      console.error('Errore nell\\'esportazione dei cavi:', error);\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  // Gestisce l'esportazione del parco bobine in Excel\n  const handleEsportaParcoBobine = async () => {\n    try {\n      setLoading(true);\n      const response = await excelService.exportParcoBobine(cantiereId);\n\n      if (response && response.file_url) {\n        setDownloadUrl(response.file_url);\n        onSuccess('Parco bobine esportato con successo e download avviato! Controlla la cartella Download del tuo browser.');\n      } else {\n        onError('Errore nell\\'esportazione del parco bobine');\n      }\n    } catch (error) {\n      onError('Errore nell\\'esportazione del parco bobine: ' + (error.message || 'Errore sconosciuto'));\n      console.error('Errore nell\\'esportazione del parco bobine:', error);\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  // Esegue l'operazione selezionata\n  const executeOperation = () => {\n    switch (operationType) {\n      case 'importaCavi':\n        handleImportaCavi();\n        break;\n      case 'importaParcoBobine':\n        handleImportaParcoBobine();\n        break;\n      case 'creaTemplateCavi':\n        handleCreaTemplateCavi();\n        break;\n      case 'creaTemplateParcoBobine':\n        handleCreaTemplateParcoBobine();\n        break;\n      case 'esportaCavi':\n        handleEsportaCavi();\n        break;\n      case 'esportaParcoBobine':\n        handleEsportaParcoBobine();\n        break;\n      default:\n        break;\n    }\n  };\n\n  // Determina il titolo del dialog in base all'operazione\n  const getDialogTitle = () => {\n    switch (operationType) {\n      case 'importaCavi':\n        return 'Importa Cavi da Excel';\n      case 'importaParcoBobine':\n        return 'Importa Parco Bobine da Excel';\n      case 'creaTemplateCavi':\n        return 'Crea Template Excel per Cavi';\n      case 'creaTemplateParcoBobine':\n        return 'Crea Template Excel per Parco Bobine';\n      case 'esportaCavi':\n        return 'Esporta Cavi in Excel';\n      case 'esportaParcoBobine':\n        return 'Esporta Parco Bobine in Excel';\n      default:\n        return 'Gestione Excel';\n    }\n  };\n\n  // Determina se mostrare il selettore di file\n  const showFileInput = ['importaCavi', 'importaParcoBobine'].includes(operationType);\n\n  // Determina se mostrare il link di download\n  const showDownloadLink = downloadUrl && ['creaTemplateCavi', 'creaTemplateParcoBobine', 'esportaCavi', 'esportaParcoBobine'].includes(operationType);\n\n  // Determina il testo del pulsante di azione\n  const getActionButtonText = () => {\n    if (showDownloadLink) return 'Chiudi';\n\n    switch (operationType) {\n      case 'importaCavi':\n      case 'importaParcoBobine':\n        return 'Importa';\n      case 'creaTemplateCavi':\n      case 'creaTemplateParcoBobine':\n        return 'Crea Template';\n      case 'esportaCavi':\n      case 'esportaParcoBobine':\n        return 'Esporta';\n      default:\n        return 'Conferma';\n    }\n  };\n\n  return (\n    <Dialog\n      open={open}\n      onClose={handleClose}\n      maxWidth=\"sm\"\n      fullWidth\n      PaperProps={{\n        sx: {\n          borderRadius: '8px',\n          boxShadow: '0 4px 20px rgba(0,0,0,0.1)'\n        }\n      }}\n    >\n      <DialogTitle sx={{\n        display: 'flex',\n        justifyContent: 'space-between',\n        alignItems: 'center',\n        borderBottom: '1px solid #e0e0e0',\n        pb: 1\n      }}>\n        <Typography variant=\"h6\">{getDialogTitle()}</Typography>\n        <IconButton onClick={handleClose} size=\"small\">\n          <CloseIcon />\n        </IconButton>\n      </DialogTitle>\n\n      <DialogContent sx={{ pt: 3, pb: 2 }}>\n        {loading ? (\n          <Box sx={{ display: 'flex', flexDirection: 'column', alignItems: 'center', py: 3 }}>\n            <CircularProgress size={40} />\n            <Typography variant=\"body1\" sx={{ mt: 2 }}>\n              Elaborazione in corso...\n            </Typography>\n          </Box>\n        ) : (\n          <Box>\n            {showFileInput && (\n              <>\n                <Typography variant=\"body2\" paragraph>\n                  Seleziona un file Excel da importare:\n                </Typography>\n                <Box sx={{ mb: 2 }}>\n                  <Button\n                    variant=\"outlined\"\n                    component=\"label\"\n                    startIcon={<UploadIcon />}\n                    sx={{ mb: 1 }}\n                  >\n                    Seleziona File\n                    <input\n                      type=\"file\"\n                      accept=\".xlsx,.xls\"\n                      hidden\n                      onChange={handleFileChange}\n                    />\n                  </Button>\n                  {fileInput && (\n                    <Typography variant=\"body2\" sx={{ mt: 1 }}>\n                      File selezionato: {fileInput.name}\n                    </Typography>\n                  )}\n                </Box>\n                <Alert severity=\"info\" sx={{ mb: 2 }}>\n                  <Typography variant=\"body2\">\n                    Assicurati che il file Excel sia nel formato corretto.\n                    {operationType === 'importaCavi' && ' Puoi scaricare un template vuoto utilizzando l\\'opzione \"Template Cavi\".'}\n                    {operationType === 'importaParcoBobine' && ' Puoi scaricare un template vuoto utilizzando l\\'opzione \"Template Parco Bobine\".'}\n                  </Typography>\n                </Alert>\n              </>\n            )}\n\n            {['creaTemplateCavi', 'creaTemplateParcoBobine'].includes(operationType) && !showDownloadLink && (\n              <Typography variant=\"body2\" paragraph>\n                Clicca su \"{getActionButtonText()}\" per generare un template Excel vuoto.\n              </Typography>\n            )}\n\n            {['esportaCavi', 'esportaParcoBobine'].includes(operationType) && !showDownloadLink && (\n              <Typography variant=\"body2\" paragraph>\n                Clicca su \"{getActionButtonText()}\" per esportare i dati in un file Excel.\n              </Typography>\n            )}\n\n            {showDownloadLink && (\n              <Box sx={{ mt: 2, textAlign: 'center' }}>\n                <Alert severity=\"success\" sx={{ mb: 2 }}>\n                  <Typography variant=\"body2\">\n                    File generato con successo!\n                  </Typography>\n                </Alert>\n                <Button\n                  variant=\"contained\"\n                  color=\"primary\"\n                  startIcon={<DownloadIcon />}\n                  href={downloadUrl}\n                  target=\"_blank\"\n                  rel=\"noopener noreferrer\"\n                  sx={{ mt: 1 }}\n                >\n                  Scarica File\n                </Button>\n              </Box>\n            )}\n          </Box>\n        )}\n      </DialogContent>\n\n      <DialogActions sx={{ px: 3, py: 2, borderTop: '1px solid #e0e0e0' }}>\n        <Button\n          onClick={handleClose}\n          color=\"inherit\"\n          variant=\"outlined\"\n          sx={{ mr: 1 }}\n        >\n          Annulla\n        </Button>\n        {!showDownloadLink && (\n          <Button\n            onClick={executeOperation}\n            color=\"primary\"\n            variant=\"contained\"\n            startIcon={showFileInput ? <FileUploadIcon /> : <DownloadIcon />}\n            disabled={loading || (showFileInput && !fileInput)}\n          >\n            {getActionButtonText()}\n          </Button>\n        )}\n      </DialogActions>\n    </Dialog>\n  );\n};\n\nexport default ExcelPopup;\n"], "mappings": "AAAA,MAAO,CAAAA,KAAK,EAAIC,QAAQ,KAAQ,OAAO,CACvC,OACEC,MAAM,CACNC,WAAW,CACXC,aAAa,CACbC,aAAa,CACbC,MAAM,CACNC,UAAU,CACVC,GAAG,CACHC,SAAS,CACTC,gBAAgB,CAChBC,KAAK,CACLC,UAAU,CACVC,OAAO,KACF,eAAe,CACtB,OACEC,KAAK,GAAI,CAAAC,SAAS,CAClBC,MAAM,GAAI,CAAAC,UAAU,CACpBC,QAAQ,GAAI,CAAAC,YAAY,CACxBC,UAAU,GAAI,CAAAC,cAAc,KACvB,qBAAqB,CAC5B,MAAO,CAAAC,YAAY,KAAM,6BAA6B,CAAC,OAAAC,GAAA,IAAAC,IAAA,CAAAC,IAAA,IAAAC,KAAA,CAAAC,QAAA,IAAAC,SAAA,yBAEvD,KAAM,CAAAC,UAAU,CAAGC,IAAA,EAAsE,IAArE,CAAEC,IAAI,CAAEC,OAAO,CAAEC,aAAa,CAAEC,UAAU,CAAEC,SAAS,CAAEC,OAAQ,CAAC,CAAAN,IAAA,CAClF,KAAM,CAACO,OAAO,CAAEC,UAAU,CAAC,CAAGrC,QAAQ,CAAC,KAAK,CAAC,CAC7C,KAAM,CAACsC,SAAS,CAAEC,YAAY,CAAC,CAAGvC,QAAQ,CAAC,IAAI,CAAC,CAChD,KAAM,CAACwC,QAAQ,CAAEC,WAAW,CAAC,CAAGzC,QAAQ,CAAC,EAAE,CAAC,CAC5C,KAAM,CAAC0C,WAAW,CAAEC,cAAc,CAAC,CAAG3C,QAAQ,CAAC,EAAE,CAAC,CAElD;AACAD,KAAK,CAAC6C,SAAS,CAAC,IAAM,CACpB,GAAId,IAAI,CAAE,CACRS,YAAY,CAAC,IAAI,CAAC,CAClBE,WAAW,CAAC,EAAE,CAAC,CACfE,cAAc,CAAC,EAAE,CAAC,CAClBN,UAAU,CAAC,KAAK,CAAC,CACnB,CACF,CAAC,CAAE,CAACP,IAAI,CAAEE,aAAa,CAAC,CAAC,CAEzB;AACA,KAAM,CAAAa,WAAW,CAAGA,CAAA,GAAM,CACxBR,UAAU,CAAC,KAAK,CAAC,CACjBE,YAAY,CAAC,IAAI,CAAC,CAClBE,WAAW,CAAC,EAAE,CAAC,CACfE,cAAc,CAAC,EAAE,CAAC,CAClBZ,OAAO,CAAC,CAAC,CACX,CAAC,CAED;AACA,KAAM,CAAAe,gBAAgB,CAAIC,CAAC,EAAK,CAC9BR,YAAY,CAACQ,CAAC,CAACC,MAAM,CAACC,KAAK,CAAC,CAAC,CAAC,CAAC,CACjC,CAAC,CAED;AACA,KAAM,CAAAC,iBAAiB,CAAG,KAAAA,CAAA,GAAY,CACpC,GAAI,CACF,GAAI,CAACZ,SAAS,CAAE,CACdH,OAAO,CAAC,sCAAsC,CAAC,CAC/C,OACF,CAEAE,UAAU,CAAC,IAAI,CAAC,CAChB,KAAM,CAAAc,QAAQ,CAAG,GAAI,CAAAC,QAAQ,CAAC,CAAC,CAC/BD,QAAQ,CAACE,MAAM,CAAC,MAAM,CAAEf,SAAS,CAAC,CAElC,KAAM,CAAAjB,YAAY,CAACiC,UAAU,CAACrB,UAAU,CAAEkB,QAAQ,CAAC,CACnDjB,SAAS,CAAC,6BAA6B,CAAC,CACxCW,WAAW,CAAC,CAAC,CACf,CAAE,MAAOU,KAAK,CAAE,CACdpB,OAAO,CAAC,sCAAsC,EAAIoB,KAAK,CAACC,OAAO,EAAI,oBAAoB,CAAC,CAAC,CACzFC,OAAO,CAACF,KAAK,CAAC,qCAAqC,CAAEA,KAAK,CAAC,CAC7D,CAAC,OAAS,CACRlB,UAAU,CAAC,KAAK,CAAC,CACnB,CACF,CAAC,CAED;AACA,KAAM,CAAAqB,wBAAwB,CAAG,KAAAA,CAAA,GAAY,CAC3C,GAAI,CACF,GAAI,CAACpB,SAAS,CAAE,CACdH,OAAO,CAAC,sCAAsC,CAAC,CAC/C,OACF,CAEAE,UAAU,CAAC,IAAI,CAAC,CAChB,KAAM,CAAAc,QAAQ,CAAG,GAAI,CAAAC,QAAQ,CAAC,CAAC,CAC/BD,QAAQ,CAACE,MAAM,CAAC,MAAM,CAAEf,SAAS,CAAC,CAElC,KAAM,CAAAjB,YAAY,CAACsC,iBAAiB,CAAC1B,UAAU,CAAEkB,QAAQ,CAAC,CAC1DjB,SAAS,CAAC,qCAAqC,CAAC,CAChDW,WAAW,CAAC,CAAC,CACf,CAAC,OAAS,CACRR,UAAU,CAAC,KAAK,CAAC,CACnB,CACF,CAAC,CAED;AACA,KAAM,CAAAuB,sBAAsB,CAAG,KAAAA,CAAA,GAAY,CACzC,GAAI,CACFvB,UAAU,CAAC,IAAI,CAAC,CAChB,KAAM,CAAAwB,QAAQ,CAAG,KAAM,CAAAxC,YAAY,CAACyC,kBAAkB,CAAC,CAAC,CAExD,GAAID,QAAQ,EAAIA,QAAQ,CAACE,QAAQ,CAAE,CACjCpB,cAAc,CAACkB,QAAQ,CAACE,QAAQ,CAAC,CACjC7B,SAAS,CAAC,oGAAoG,CAAC,CACjH,CAAC,IAAM,CACLC,OAAO,CAAC,oDAAoD,CAAC,CAC/D,CACF,CAAE,MAAOoB,KAAK,CAAE,CACdpB,OAAO,CAAC,sDAAsD,EAAIoB,KAAK,CAACC,OAAO,EAAI,oBAAoB,CAAC,CAAC,CACzGC,OAAO,CAACF,KAAK,CAAC,qDAAqD,CAAEA,KAAK,CAAC,CAC7E,CAAC,OAAS,CACRlB,UAAU,CAAC,KAAK,CAAC,CACnB,CACF,CAAC,CAED;AACA,KAAM,CAAA2B,6BAA6B,CAAG,KAAAA,CAAA,GAAY,CAChD,GAAI,CACF3B,UAAU,CAAC,IAAI,CAAC,CAChB,KAAM,CAAAwB,QAAQ,CAAG,KAAM,CAAAxC,YAAY,CAAC4C,yBAAyB,CAAC,CAAC,CAE/D,GAAIJ,QAAQ,EAAIA,QAAQ,CAACE,QAAQ,CAAE,CACjCpB,cAAc,CAACkB,QAAQ,CAACE,QAAQ,CAAC,CACjC7B,SAAS,CAAC,4GAA4G,CAAC,CACzH,CAAC,IAAM,CACLC,OAAO,CAAC,4DAA4D,CAAC,CACvE,CACF,CAAE,MAAOoB,KAAK,CAAE,CACdpB,OAAO,CAAC,8DAA8D,EAAIoB,KAAK,CAACC,OAAO,EAAI,oBAAoB,CAAC,CAAC,CACjHC,OAAO,CAACF,KAAK,CAAC,6DAA6D,CAAEA,KAAK,CAAC,CACrF,CAAC,OAAS,CACRlB,UAAU,CAAC,KAAK,CAAC,CACnB,CACF,CAAC,CAED;AACA,KAAM,CAAA6B,iBAAiB,CAAG,KAAAA,CAAA,GAAY,CACpC,GAAI,CACF7B,UAAU,CAAC,IAAI,CAAC,CAChB,KAAM,CAAAwB,QAAQ,CAAG,KAAM,CAAAxC,YAAY,CAAC8C,UAAU,CAAClC,UAAU,CAAC,CAE1D,GAAI4B,QAAQ,EAAIA,QAAQ,CAACE,QAAQ,CAAE,CACjCpB,cAAc,CAACkB,QAAQ,CAACE,QAAQ,CAAC,CACjC7B,SAAS,CAAC,iGAAiG,CAAC,CAC9G,CAAC,IAAM,CACLC,OAAO,CAAC,oCAAoC,CAAC,CAC/C,CACF,CAAE,MAAOoB,KAAK,CAAE,CACdpB,OAAO,CAAC,sCAAsC,EAAIoB,KAAK,CAACC,OAAO,EAAI,oBAAoB,CAAC,CAAC,CACzFC,OAAO,CAACF,KAAK,CAAC,qCAAqC,CAAEA,KAAK,CAAC,CAC7D,CAAC,OAAS,CACRlB,UAAU,CAAC,KAAK,CAAC,CACnB,CACF,CAAC,CAED;AACA,KAAM,CAAA+B,wBAAwB,CAAG,KAAAA,CAAA,GAAY,CAC3C,GAAI,CACF/B,UAAU,CAAC,IAAI,CAAC,CAChB,KAAM,CAAAwB,QAAQ,CAAG,KAAM,CAAAxC,YAAY,CAACgD,iBAAiB,CAACpC,UAAU,CAAC,CAEjE,GAAI4B,QAAQ,EAAIA,QAAQ,CAACE,QAAQ,CAAE,CACjCpB,cAAc,CAACkB,QAAQ,CAACE,QAAQ,CAAC,CACjC7B,SAAS,CAAC,yGAAyG,CAAC,CACtH,CAAC,IAAM,CACLC,OAAO,CAAC,4CAA4C,CAAC,CACvD,CACF,CAAE,MAAOoB,KAAK,CAAE,CACdpB,OAAO,CAAC,8CAA8C,EAAIoB,KAAK,CAACC,OAAO,EAAI,oBAAoB,CAAC,CAAC,CACjGC,OAAO,CAACF,KAAK,CAAC,6CAA6C,CAAEA,KAAK,CAAC,CACrE,CAAC,OAAS,CACRlB,UAAU,CAAC,KAAK,CAAC,CACnB,CACF,CAAC,CAED;AACA,KAAM,CAAAiC,gBAAgB,CAAGA,CAAA,GAAM,CAC7B,OAAQtC,aAAa,EACnB,IAAK,aAAa,CAChBkB,iBAAiB,CAAC,CAAC,CACnB,MACF,IAAK,oBAAoB,CACvBQ,wBAAwB,CAAC,CAAC,CAC1B,MACF,IAAK,kBAAkB,CACrBE,sBAAsB,CAAC,CAAC,CACxB,MACF,IAAK,yBAAyB,CAC5BI,6BAA6B,CAAC,CAAC,CAC/B,MACF,IAAK,aAAa,CAChBE,iBAAiB,CAAC,CAAC,CACnB,MACF,IAAK,oBAAoB,CACvBE,wBAAwB,CAAC,CAAC,CAC1B,MACF,QACE,MACJ,CACF,CAAC,CAED;AACA,KAAM,CAAAG,cAAc,CAAGA,CAAA,GAAM,CAC3B,OAAQvC,aAAa,EACnB,IAAK,aAAa,CAChB,MAAO,uBAAuB,CAChC,IAAK,oBAAoB,CACvB,MAAO,+BAA+B,CACxC,IAAK,kBAAkB,CACrB,MAAO,8BAA8B,CACvC,IAAK,yBAAyB,CAC5B,MAAO,sCAAsC,CAC/C,IAAK,aAAa,CAChB,MAAO,uBAAuB,CAChC,IAAK,oBAAoB,CACvB,MAAO,+BAA+B,CACxC,QACE,MAAO,gBAAgB,CAC3B,CACF,CAAC,CAED;AACA,KAAM,CAAAwC,aAAa,CAAG,CAAC,aAAa,CAAE,oBAAoB,CAAC,CAACC,QAAQ,CAACzC,aAAa,CAAC,CAEnF;AACA,KAAM,CAAA0C,gBAAgB,CAAGhC,WAAW,EAAI,CAAC,kBAAkB,CAAE,yBAAyB,CAAE,aAAa,CAAE,oBAAoB,CAAC,CAAC+B,QAAQ,CAACzC,aAAa,CAAC,CAEpJ;AACA,KAAM,CAAA2C,mBAAmB,CAAGA,CAAA,GAAM,CAChC,GAAID,gBAAgB,CAAE,MAAO,QAAQ,CAErC,OAAQ1C,aAAa,EACnB,IAAK,aAAa,CAClB,IAAK,oBAAoB,CACvB,MAAO,SAAS,CAClB,IAAK,kBAAkB,CACvB,IAAK,yBAAyB,CAC5B,MAAO,eAAe,CACxB,IAAK,aAAa,CAClB,IAAK,oBAAoB,CACvB,MAAO,SAAS,CAClB,QACE,MAAO,UAAU,CACrB,CACF,CAAC,CAED,mBACEP,KAAA,CAACxB,MAAM,EACL6B,IAAI,CAAEA,IAAK,CACXC,OAAO,CAAEc,WAAY,CACrB+B,QAAQ,CAAC,IAAI,CACbC,SAAS,MACTC,UAAU,CAAE,CACVC,EAAE,CAAE,CACFC,YAAY,CAAE,KAAK,CACnBC,SAAS,CAAE,4BACb,CACF,CAAE,CAAAC,QAAA,eAEFzD,KAAA,CAACvB,WAAW,EAAC6E,EAAE,CAAE,CACfI,OAAO,CAAE,MAAM,CACfC,cAAc,CAAE,eAAe,CAC/BC,UAAU,CAAE,QAAQ,CACpBC,YAAY,CAAE,mBAAmB,CACjCC,EAAE,CAAE,CACN,CAAE,CAAAL,QAAA,eACA3D,IAAA,CAACjB,UAAU,EAACkF,OAAO,CAAC,IAAI,CAAAN,QAAA,CAAEX,cAAc,CAAC,CAAC,CAAa,CAAC,cACxDhD,IAAA,CAACZ,UAAU,EAAC8E,OAAO,CAAE5C,WAAY,CAAC6C,IAAI,CAAC,OAAO,CAAAR,QAAA,cAC5C3D,IAAA,CAACT,SAAS,GAAE,CAAC,CACH,CAAC,EACF,CAAC,cAEdS,IAAA,CAACpB,aAAa,EAAC4E,EAAE,CAAE,CAAEY,EAAE,CAAE,CAAC,CAAEJ,EAAE,CAAE,CAAE,CAAE,CAAAL,QAAA,CACjC9C,OAAO,cACNX,KAAA,CAAClB,GAAG,EAACwE,EAAE,CAAE,CAAEI,OAAO,CAAE,MAAM,CAAES,aAAa,CAAE,QAAQ,CAAEP,UAAU,CAAE,QAAQ,CAAEQ,EAAE,CAAE,CAAE,CAAE,CAAAX,QAAA,eACjF3D,IAAA,CAACd,gBAAgB,EAACiF,IAAI,CAAE,EAAG,CAAE,CAAC,cAC9BnE,IAAA,CAACjB,UAAU,EAACkF,OAAO,CAAC,OAAO,CAACT,EAAE,CAAE,CAAEe,EAAE,CAAE,CAAE,CAAE,CAAAZ,QAAA,CAAC,0BAE3C,CAAY,CAAC,EACV,CAAC,cAENzD,KAAA,CAAClB,GAAG,EAAA2E,QAAA,EACDV,aAAa,eACZ/C,KAAA,CAAAE,SAAA,EAAAuD,QAAA,eACE3D,IAAA,CAACjB,UAAU,EAACkF,OAAO,CAAC,OAAO,CAACO,SAAS,MAAAb,QAAA,CAAC,uCAEtC,CAAY,CAAC,cACbzD,KAAA,CAAClB,GAAG,EAACwE,EAAE,CAAE,CAAEiB,EAAE,CAAE,CAAE,CAAE,CAAAd,QAAA,eACjBzD,KAAA,CAACpB,MAAM,EACLmF,OAAO,CAAC,UAAU,CAClBS,SAAS,CAAC,OAAO,CACjBC,SAAS,cAAE3E,IAAA,CAACP,UAAU,GAAE,CAAE,CAC1B+D,EAAE,CAAE,CAAEiB,EAAE,CAAE,CAAE,CAAE,CAAAd,QAAA,EACf,gBAEC,cAAA3D,IAAA,UACE4E,IAAI,CAAC,MAAM,CACXC,MAAM,CAAC,YAAY,CACnBC,MAAM,MACNC,QAAQ,CAAExD,gBAAiB,CAC5B,CAAC,EACI,CAAC,CACRR,SAAS,eACRb,KAAA,CAACnB,UAAU,EAACkF,OAAO,CAAC,OAAO,CAACT,EAAE,CAAE,CAAEe,EAAE,CAAE,CAAE,CAAE,CAAAZ,QAAA,EAAC,oBACvB,CAAC5C,SAAS,CAACiE,IAAI,EACvB,CACb,EACE,CAAC,cACNhF,IAAA,CAACb,KAAK,EAAC8F,QAAQ,CAAC,MAAM,CAACzB,EAAE,CAAE,CAAEiB,EAAE,CAAE,CAAE,CAAE,CAAAd,QAAA,cACnCzD,KAAA,CAACnB,UAAU,EAACkF,OAAO,CAAC,OAAO,CAAAN,QAAA,EAAC,wDAE1B,CAAClD,aAAa,GAAK,aAAa,EAAI,2EAA2E,CAC9GA,aAAa,GAAK,oBAAoB,EAAI,mFAAmF,EACpH,CAAC,CACR,CAAC,EACR,CACH,CAEA,CAAC,kBAAkB,CAAE,yBAAyB,CAAC,CAACyC,QAAQ,CAACzC,aAAa,CAAC,EAAI,CAAC0C,gBAAgB,eAC3FjD,KAAA,CAACnB,UAAU,EAACkF,OAAO,CAAC,OAAO,CAACO,SAAS,MAAAb,QAAA,EAAC,cACzB,CAACP,mBAAmB,CAAC,CAAC,CAAC,0CACpC,EAAY,CACb,CAEA,CAAC,aAAa,CAAE,oBAAoB,CAAC,CAACF,QAAQ,CAACzC,aAAa,CAAC,EAAI,CAAC0C,gBAAgB,eACjFjD,KAAA,CAACnB,UAAU,EAACkF,OAAO,CAAC,OAAO,CAACO,SAAS,MAAAb,QAAA,EAAC,cACzB,CAACP,mBAAmB,CAAC,CAAC,CAAC,2CACpC,EAAY,CACb,CAEAD,gBAAgB,eACfjD,KAAA,CAAClB,GAAG,EAACwE,EAAE,CAAE,CAAEe,EAAE,CAAE,CAAC,CAAEW,SAAS,CAAE,QAAS,CAAE,CAAAvB,QAAA,eACtC3D,IAAA,CAACb,KAAK,EAAC8F,QAAQ,CAAC,SAAS,CAACzB,EAAE,CAAE,CAAEiB,EAAE,CAAE,CAAE,CAAE,CAAAd,QAAA,cACtC3D,IAAA,CAACjB,UAAU,EAACkF,OAAO,CAAC,OAAO,CAAAN,QAAA,CAAC,6BAE5B,CAAY,CAAC,CACR,CAAC,cACR3D,IAAA,CAAClB,MAAM,EACLmF,OAAO,CAAC,WAAW,CACnBkB,KAAK,CAAC,SAAS,CACfR,SAAS,cAAE3E,IAAA,CAACL,YAAY,GAAE,CAAE,CAC5ByF,IAAI,CAAEjE,WAAY,CAClBM,MAAM,CAAC,QAAQ,CACf4D,GAAG,CAAC,qBAAqB,CACzB7B,EAAE,CAAE,CAAEe,EAAE,CAAE,CAAE,CAAE,CAAAZ,QAAA,CACf,cAED,CAAQ,CAAC,EACN,CACN,EACE,CACN,CACY,CAAC,cAEhBzD,KAAA,CAACrB,aAAa,EAAC2E,EAAE,CAAE,CAAE8B,EAAE,CAAE,CAAC,CAAEhB,EAAE,CAAE,CAAC,CAAEiB,SAAS,CAAE,mBAAoB,CAAE,CAAA5B,QAAA,eAClE3D,IAAA,CAAClB,MAAM,EACLoF,OAAO,CAAE5C,WAAY,CACrB6D,KAAK,CAAC,SAAS,CACflB,OAAO,CAAC,UAAU,CAClBT,EAAE,CAAE,CAAEgC,EAAE,CAAE,CAAE,CAAE,CAAA7B,QAAA,CACf,SAED,CAAQ,CAAC,CACR,CAACR,gBAAgB,eAChBnD,IAAA,CAAClB,MAAM,EACLoF,OAAO,CAAEnB,gBAAiB,CAC1BoC,KAAK,CAAC,SAAS,CACflB,OAAO,CAAC,WAAW,CACnBU,SAAS,CAAE1B,aAAa,cAAGjD,IAAA,CAACH,cAAc,GAAE,CAAC,cAAGG,IAAA,CAACL,YAAY,GAAE,CAAE,CACjE8F,QAAQ,CAAE5E,OAAO,EAAKoC,aAAa,EAAI,CAAClC,SAAW,CAAA4C,QAAA,CAElDP,mBAAmB,CAAC,CAAC,CAChB,CACT,EACY,CAAC,EACV,CAAC,CAEb,CAAC,CAED,cAAe,CAAA/C,UAAU", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}