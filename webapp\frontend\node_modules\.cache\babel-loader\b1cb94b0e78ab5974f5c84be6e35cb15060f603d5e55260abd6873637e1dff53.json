{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\PycharmProjects\\\\CMS\\\\webapp\\\\frontend\\\\src\\\\pages\\\\CertificazioniPage.jsx\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect } from 'react';\nimport { useParams, useNavigate } from 'react-router-dom';\nimport { Container, Typography, Box, Button, TextField, Paper, Alert, Tabs, Tab } from '@mui/material';\nimport { Add as AddIcon, Assignment as AssignmentIcon, Build as BuildIcon } from '@mui/icons-material';\nimport { useAuth } from '../contexts/AuthContext';\nimport { apiService } from '../services/apiService';\nimport CertificazioniList from '../components/certificazioni/CertificazioniList';\nimport CertificazioneForm from '../components/certificazioni/CertificazioneForm';\nimport StrumentiList from '../components/certificazioni/StrumentiList';\nimport StrumentoForm from '../components/certificazioni/StrumentoForm';\nimport { jsxDEV as _jsxDEV, Fragment as _Fragment } from \"react/jsx-dev-runtime\";\nfunction TabPanel({\n  children,\n  value,\n  index,\n  ...other\n}) {\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    role: \"tabpanel\",\n    hidden: value !== index,\n    id: `certificazioni-tabpanel-${index}`,\n    \"aria-labelledby\": `certificazioni-tab-${index}`,\n    ...other,\n    children: value === index && /*#__PURE__*/_jsxDEV(Box, {\n      sx: {\n        p: 3\n      },\n      children: children\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 33,\n      columnNumber: 9\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 25,\n    columnNumber: 5\n  }, this);\n}\n_c = TabPanel;\nfunction CertificazioniPage() {\n  _s();\n  const {\n    cantiereId\n  } = useParams();\n  const navigate = useNavigate();\n  const {\n    user\n  } = useAuth();\n  const [tabValue, setTabValue] = useState(0);\n  const [cantiere, setCantiere] = useState(null);\n  const [certificazioni, setCertificazioni] = useState([]);\n  const [strumenti, setStrumenti] = useState([]);\n  const [loading, setLoading] = useState(true);\n  const [error, setError] = useState('');\n  const [success, setSuccess] = useState('');\n\n  // Stati per i form\n  const [showCertificazioneForm, setShowCertificazioneForm] = useState(false);\n  const [showStrumentoForm, setShowStrumentoForm] = useState(false);\n  const [editingCertificazione, setEditingCertificazione] = useState(null);\n  const [editingStrumento, setEditingStrumento] = useState(null);\n\n  // Filtro per le certificazioni\n  const [filtroCavo, setFiltroCavo] = useState('');\n  useEffect(() => {\n    loadData();\n  }, [cantiereId]);\n  const loadData = async () => {\n    try {\n      setLoading(true);\n      setError('');\n\n      // Carica informazioni del cantiere\n      const cantiereData = await apiService.getCantiere(cantiereId);\n      setCantiere(cantiereData);\n\n      // Carica certificazioni e strumenti in parallelo\n      const [certificazioniData, strumentiData] = await Promise.all([apiService.getCertificazioni(cantiereId, filtroCavo), apiService.getStrumenti(cantiereId)]);\n      setCertificazioni(certificazioniData);\n      setStrumenti(strumentiData);\n    } catch (err) {\n      var _err$response, _err$response$data;\n      console.error('Errore nel caricamento dei dati:', err);\n      setError('Errore nel caricamento dei dati: ' + (((_err$response = err.response) === null || _err$response === void 0 ? void 0 : (_err$response$data = _err$response.data) === null || _err$response$data === void 0 ? void 0 : _err$response$data.detail) || err.message));\n    } finally {\n      setLoading(false);\n    }\n  };\n  const handleTabChange = (event, newValue) => {\n    setTabValue(newValue);\n    // Reset form states when changing tab\n    setShowCertificazioneForm(false);\n    setShowStrumentoForm(false);\n    setEditingCertificazione(null);\n    setEditingStrumento(null);\n  };\n  const handleFiltroCavoChange = event => {\n    setFiltroCavo(event.target.value);\n  };\n  const handleFiltroCavoSubmit = () => {\n    loadData();\n  };\n  const handleCreateCertificazione = () => {\n    setEditingCertificazione(null);\n    setShowCertificazioneForm(true);\n  };\n  const handleEditCertificazione = certificazione => {\n    setEditingCertificazione(certificazione);\n    setShowCertificazioneForm(true);\n  };\n  const handleCreateStrumento = () => {\n    setEditingStrumento(null);\n    setShowStrumentoForm(true);\n  };\n  const handleEditStrumento = strumento => {\n    setEditingStrumento(strumento);\n    setShowStrumentoForm(true);\n  };\n  const handleCertificazioneSuccess = message => {\n    setSuccess(message);\n    setShowCertificazioneForm(false);\n    setEditingCertificazione(null);\n    loadData();\n    setTimeout(() => setSuccess(''), 3000);\n  };\n  const handleStrumentoSuccess = message => {\n    setSuccess(message);\n    setShowStrumentoForm(false);\n    setEditingStrumento(null);\n    loadData();\n    setTimeout(() => setSuccess(''), 3000);\n  };\n  const handleFormCancel = () => {\n    setShowCertificazioneForm(false);\n    setShowStrumentoForm(false);\n    setEditingCertificazione(null);\n    setEditingStrumento(null);\n  };\n  if (loading) {\n    return /*#__PURE__*/_jsxDEV(Container, {\n      maxWidth: \"lg\",\n      sx: {\n        mt: 4,\n        mb: 4\n      },\n      children: /*#__PURE__*/_jsxDEV(Typography, {\n        children: \"Caricamento...\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 155,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 154,\n      columnNumber: 7\n    }, this);\n  }\n  return /*#__PURE__*/_jsxDEV(Container, {\n    maxWidth: \"lg\",\n    sx: {\n      mt: 4,\n      mb: 4\n    },\n    children: [/*#__PURE__*/_jsxDEV(Box, {\n      sx: {\n        mb: 3\n      },\n      children: [/*#__PURE__*/_jsxDEV(Typography, {\n        variant: \"h4\",\n        component: \"h1\",\n        gutterBottom: true,\n        children: \"Certificazioni Cavi\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 164,\n        columnNumber: 9\n      }, this), cantiere && /*#__PURE__*/_jsxDEV(Typography, {\n        variant: \"h6\",\n        color: \"text.secondary\",\n        children: [cantiere.nome_cantiere, \" - \", cantiere.codice_cantiere]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 168,\n        columnNumber: 11\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 163,\n      columnNumber: 7\n    }, this), error && /*#__PURE__*/_jsxDEV(Alert, {\n      severity: \"error\",\n      sx: {\n        mb: 2\n      },\n      onClose: () => setError(''),\n      children: error\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 176,\n      columnNumber: 9\n    }, this), success && /*#__PURE__*/_jsxDEV(Alert, {\n      severity: \"success\",\n      sx: {\n        mb: 2\n      },\n      onClose: () => setSuccess(''),\n      children: success\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 181,\n      columnNumber: 9\n    }, this), /*#__PURE__*/_jsxDEV(Paper, {\n      sx: {\n        mb: 3\n      },\n      children: /*#__PURE__*/_jsxDEV(Tabs, {\n        value: tabValue,\n        onChange: handleTabChange,\n        indicatorColor: \"primary\",\n        textColor: \"primary\",\n        children: [/*#__PURE__*/_jsxDEV(Tab, {\n          icon: /*#__PURE__*/_jsxDEV(AssignmentIcon, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 195,\n            columnNumber: 19\n          }, this),\n          label: \"Certificazioni\",\n          id: \"certificazioni-tab-0\",\n          \"aria-controls\": \"certificazioni-tabpanel-0\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 194,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Tab, {\n          icon: /*#__PURE__*/_jsxDEV(BuildIcon, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 201,\n            columnNumber: 19\n          }, this),\n          label: \"Strumenti\",\n          id: \"certificazioni-tab-1\",\n          \"aria-controls\": \"certificazioni-tabpanel-1\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 200,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 188,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 187,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(TabPanel, {\n      value: tabValue,\n      index: 0,\n      children: !showCertificazioneForm ? /*#__PURE__*/_jsxDEV(_Fragment, {\n        children: [/*#__PURE__*/_jsxDEV(Box, {\n          sx: {\n            mb: 3,\n            display: 'flex',\n            gap: 2,\n            alignItems: 'center',\n            flexWrap: 'wrap'\n          },\n          children: [/*#__PURE__*/_jsxDEV(TextField, {\n            label: \"Filtra per ID Cavo\",\n            value: filtroCavo,\n            onChange: handleFiltroCavoChange,\n            onKeyPress: e => e.key === 'Enter' && handleFiltroCavoSubmit(),\n            size: \"small\",\n            sx: {\n              minWidth: 200\n            }\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 215,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(Button, {\n            variant: \"outlined\",\n            onClick: handleFiltroCavoSubmit,\n            children: \"Filtra\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 223,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(Button, {\n            variant: \"contained\",\n            startIcon: /*#__PURE__*/_jsxDEV(AddIcon, {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 231,\n              columnNumber: 28\n            }, this),\n            onClick: handleCreateCertificazione,\n            sx: {\n              ml: 'auto'\n            },\n            children: \"Nuova Certificazione\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 229,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 214,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(CertificazioniList, {\n          certificazioni: certificazioni,\n          onEdit: handleEditCertificazione,\n          onDelete: loadData,\n          cantiereId: cantiereId\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 240,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true) : /*#__PURE__*/_jsxDEV(CertificazioneForm, {\n        cantiereId: cantiereId,\n        certificazione: editingCertificazione,\n        strumenti: strumenti,\n        onSuccess: handleCertificazioneSuccess,\n        onCancel: handleFormCancel\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 248,\n        columnNumber: 11\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 210,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(TabPanel, {\n      value: tabValue,\n      index: 1,\n      children: !showStrumentoForm ? /*#__PURE__*/_jsxDEV(_Fragment, {\n        children: [/*#__PURE__*/_jsxDEV(Box, {\n          sx: {\n            mb: 3,\n            display: 'flex',\n            justifyContent: 'flex-end'\n          },\n          children: /*#__PURE__*/_jsxDEV(Button, {\n            variant: \"contained\",\n            startIcon: /*#__PURE__*/_jsxDEV(AddIcon, {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 266,\n              columnNumber: 28\n            }, this),\n            onClick: handleCreateStrumento,\n            children: \"Nuovo Strumento\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 264,\n            columnNumber: 15\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 263,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(StrumentiList, {\n          strumenti: strumenti,\n          onEdit: handleEditStrumento,\n          onDelete: loadData,\n          cantiereId: cantiereId\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 274,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true) : /*#__PURE__*/_jsxDEV(StrumentoForm, {\n        cantiereId: cantiereId,\n        strumento: editingStrumento,\n        onSuccess: handleStrumentoSuccess,\n        onCancel: handleFormCancel\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 282,\n        columnNumber: 11\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 259,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 161,\n    columnNumber: 5\n  }, this);\n}\n_s(CertificazioniPage, \"+5XSbP3EtUdg0HolrWSDj27lq4s=\", false, function () {\n  return [useParams, useNavigate, useAuth];\n});\n_c2 = CertificazioniPage;\nexport default CertificazioniPage;\nvar _c, _c2;\n$RefreshReg$(_c, \"TabPanel\");\n$RefreshReg$(_c2, \"CertificazioniPage\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "useParams", "useNavigate", "Container", "Typography", "Box", "<PERSON><PERSON>", "TextField", "Paper", "<PERSON><PERSON>", "Tabs", "Tab", "Add", "AddIcon", "Assignment", "AssignmentIcon", "Build", "BuildIcon", "useAuth", "apiService", "CertificazioniList", "CertificazioneForm", "StrumentiList", "StrumentoForm", "jsxDEV", "_jsxDEV", "Fragment", "_Fragment", "TabPanel", "children", "value", "index", "other", "role", "hidden", "id", "sx", "p", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "_c", "CertificazioniPage", "_s", "cantiereId", "navigate", "user", "tabValue", "setTabValue", "cantiere", "setCantiere", "certificazioni", "setCertificazioni", "strumenti", "setStrumenti", "loading", "setLoading", "error", "setError", "success", "setSuccess", "showCertificazioneForm", "setShowCertificazioneForm", "showStrumentoForm", "setShowStrumentoForm", "editingCertificazione", "setEditingCertificazione", "editingStrumento", "setEditingStrumento", "filtroCavo", "setFiltroCavo", "loadData", "cantiereData", "getCantiere", "certificazioniData", "strumentiData", "Promise", "all", "getCertificazioni", "getStrumenti", "err", "_err$response", "_err$response$data", "console", "response", "data", "detail", "message", "handleTabChange", "event", "newValue", "handleFiltroCavoChange", "target", "handleFiltroCavoSubmit", "handleCreateCertificazione", "handleEditCertificazione", "certificazione", "handleCreateStrumento", "handleEditStrumento", "strumento", "handleCertificazioneSuccess", "setTimeout", "handleStrumentoSuccess", "handleFormCancel", "max<PERSON><PERSON><PERSON>", "mt", "mb", "variant", "component", "gutterBottom", "color", "nome_cantiere", "codice_cantiere", "severity", "onClose", "onChange", "indicatorColor", "textColor", "icon", "label", "display", "gap", "alignItems", "flexWrap", "onKeyPress", "e", "key", "size", "min<PERSON><PERSON><PERSON>", "onClick", "startIcon", "ml", "onEdit", "onDelete", "onSuccess", "onCancel", "justifyContent", "_c2", "$RefreshReg$"], "sources": ["C:/Users/<USER>/PycharmProjects/CMS/webapp/frontend/src/pages/CertificazioniPage.jsx"], "sourcesContent": ["import React, { useState, useEffect } from 'react';\nimport { useParams, useNavigate } from 'react-router-dom';\nimport { \n  Container, \n  Typography, \n  Box, \n  Button, \n  TextField, \n  Paper,\n  Alert,\n  Tabs,\n  Tab\n} from '@mui/material';\nimport { Add as AddIcon, Assignment as AssignmentIcon, Build as BuildIcon } from '@mui/icons-material';\n\nimport { useAuth } from '../contexts/AuthContext';\nimport { apiService } from '../services/apiService';\nimport CertificazioniList from '../components/certificazioni/CertificazioniList';\nimport CertificazioneForm from '../components/certificazioni/CertificazioneForm';\nimport StrumentiList from '../components/certificazioni/StrumentiList';\nimport StrumentoForm from '../components/certificazioni/StrumentoForm';\n\nfunction TabPanel({ children, value, index, ...other }) {\n  return (\n    <div\n      role=\"tabpanel\"\n      hidden={value !== index}\n      id={`certificazioni-tabpanel-${index}`}\n      aria-labelledby={`certificazioni-tab-${index}`}\n      {...other}\n    >\n      {value === index && (\n        <Box sx={{ p: 3 }}>\n          {children}\n        </Box>\n      )}\n    </div>\n  );\n}\n\nfunction CertificazioniPage() {\n  const { cantiereId } = useParams();\n  const navigate = useNavigate();\n  const { user } = useAuth();\n  \n  const [tabValue, setTabValue] = useState(0);\n  const [cantiere, setCantiere] = useState(null);\n  const [certificazioni, setCertificazioni] = useState([]);\n  const [strumenti, setStrumenti] = useState([]);\n  const [loading, setLoading] = useState(true);\n  const [error, setError] = useState('');\n  const [success, setSuccess] = useState('');\n  \n  // Stati per i form\n  const [showCertificazioneForm, setShowCertificazioneForm] = useState(false);\n  const [showStrumentoForm, setShowStrumentoForm] = useState(false);\n  const [editingCertificazione, setEditingCertificazione] = useState(null);\n  const [editingStrumento, setEditingStrumento] = useState(null);\n  \n  // Filtro per le certificazioni\n  const [filtroCavo, setFiltroCavo] = useState('');\n\n  useEffect(() => {\n    loadData();\n  }, [cantiereId]);\n\n  const loadData = async () => {\n    try {\n      setLoading(true);\n      setError('');\n\n      // Carica informazioni del cantiere\n      const cantiereData = await apiService.getCantiere(cantiereId);\n      setCantiere(cantiereData);\n\n      // Carica certificazioni e strumenti in parallelo\n      const [certificazioniData, strumentiData] = await Promise.all([\n        apiService.getCertificazioni(cantiereId, filtroCavo),\n        apiService.getStrumenti(cantiereId)\n      ]);\n\n      setCertificazioni(certificazioniData);\n      setStrumenti(strumentiData);\n    } catch (err) {\n      console.error('Errore nel caricamento dei dati:', err);\n      setError('Errore nel caricamento dei dati: ' + (err.response?.data?.detail || err.message));\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  const handleTabChange = (event, newValue) => {\n    setTabValue(newValue);\n    // Reset form states when changing tab\n    setShowCertificazioneForm(false);\n    setShowStrumentoForm(false);\n    setEditingCertificazione(null);\n    setEditingStrumento(null);\n  };\n\n  const handleFiltroCavoChange = (event) => {\n    setFiltroCavo(event.target.value);\n  };\n\n  const handleFiltroCavoSubmit = () => {\n    loadData();\n  };\n\n  const handleCreateCertificazione = () => {\n    setEditingCertificazione(null);\n    setShowCertificazioneForm(true);\n  };\n\n  const handleEditCertificazione = (certificazione) => {\n    setEditingCertificazione(certificazione);\n    setShowCertificazioneForm(true);\n  };\n\n  const handleCreateStrumento = () => {\n    setEditingStrumento(null);\n    setShowStrumentoForm(true);\n  };\n\n  const handleEditStrumento = (strumento) => {\n    setEditingStrumento(strumento);\n    setShowStrumentoForm(true);\n  };\n\n  const handleCertificazioneSuccess = (message) => {\n    setSuccess(message);\n    setShowCertificazioneForm(false);\n    setEditingCertificazione(null);\n    loadData();\n    setTimeout(() => setSuccess(''), 3000);\n  };\n\n  const handleStrumentoSuccess = (message) => {\n    setSuccess(message);\n    setShowStrumentoForm(false);\n    setEditingStrumento(null);\n    loadData();\n    setTimeout(() => setSuccess(''), 3000);\n  };\n\n  const handleFormCancel = () => {\n    setShowCertificazioneForm(false);\n    setShowStrumentoForm(false);\n    setEditingCertificazione(null);\n    setEditingStrumento(null);\n  };\n\n  if (loading) {\n    return (\n      <Container maxWidth=\"lg\" sx={{ mt: 4, mb: 4 }}>\n        <Typography>Caricamento...</Typography>\n      </Container>\n    );\n  }\n\n  return (\n    <Container maxWidth=\"lg\" sx={{ mt: 4, mb: 4 }}>\n      {/* Header */}\n      <Box sx={{ mb: 3 }}>\n        <Typography variant=\"h4\" component=\"h1\" gutterBottom>\n          Certificazioni Cavi\n        </Typography>\n        {cantiere && (\n          <Typography variant=\"h6\" color=\"text.secondary\">\n            {cantiere.nome_cantiere} - {cantiere.codice_cantiere}\n          </Typography>\n        )}\n      </Box>\n\n      {/* Messaggi di stato */}\n      {error && (\n        <Alert severity=\"error\" sx={{ mb: 2 }} onClose={() => setError('')}>\n          {error}\n        </Alert>\n      )}\n      {success && (\n        <Alert severity=\"success\" sx={{ mb: 2 }} onClose={() => setSuccess('')}>\n          {success}\n        </Alert>\n      )}\n\n      {/* Tabs */}\n      <Paper sx={{ mb: 3 }}>\n        <Tabs \n          value={tabValue} \n          onChange={handleTabChange}\n          indicatorColor=\"primary\"\n          textColor=\"primary\"\n        >\n          <Tab \n            icon={<AssignmentIcon />} \n            label=\"Certificazioni\" \n            id=\"certificazioni-tab-0\"\n            aria-controls=\"certificazioni-tabpanel-0\"\n          />\n          <Tab \n            icon={<BuildIcon />} \n            label=\"Strumenti\" \n            id=\"certificazioni-tab-1\"\n            aria-controls=\"certificazioni-tabpanel-1\"\n          />\n        </Tabs>\n      </Paper>\n\n      {/* Tab Panel Certificazioni */}\n      <TabPanel value={tabValue} index={0}>\n        {!showCertificazioneForm ? (\n          <>\n            {/* Controlli per le certificazioni */}\n            <Box sx={{ mb: 3, display: 'flex', gap: 2, alignItems: 'center', flexWrap: 'wrap' }}>\n              <TextField\n                label=\"Filtra per ID Cavo\"\n                value={filtroCavo}\n                onChange={handleFiltroCavoChange}\n                onKeyPress={(e) => e.key === 'Enter' && handleFiltroCavoSubmit()}\n                size=\"small\"\n                sx={{ minWidth: 200 }}\n              />\n              <Button \n                variant=\"outlined\" \n                onClick={handleFiltroCavoSubmit}\n              >\n                Filtra\n              </Button>\n              <Button\n                variant=\"contained\"\n                startIcon={<AddIcon />}\n                onClick={handleCreateCertificazione}\n                sx={{ ml: 'auto' }}\n              >\n                Nuova Certificazione\n              </Button>\n            </Box>\n\n            {/* Lista certificazioni */}\n            <CertificazioniList\n              certificazioni={certificazioni}\n              onEdit={handleEditCertificazione}\n              onDelete={loadData}\n              cantiereId={cantiereId}\n            />\n          </>\n        ) : (\n          <CertificazioneForm\n            cantiereId={cantiereId}\n            certificazione={editingCertificazione}\n            strumenti={strumenti}\n            onSuccess={handleCertificazioneSuccess}\n            onCancel={handleFormCancel}\n          />\n        )}\n      </TabPanel>\n\n      {/* Tab Panel Strumenti */}\n      <TabPanel value={tabValue} index={1}>\n        {!showStrumentoForm ? (\n          <>\n            {/* Controlli per gli strumenti */}\n            <Box sx={{ mb: 3, display: 'flex', justifyContent: 'flex-end' }}>\n              <Button\n                variant=\"contained\"\n                startIcon={<AddIcon />}\n                onClick={handleCreateStrumento}\n              >\n                Nuovo Strumento\n              </Button>\n            </Box>\n\n            {/* Lista strumenti */}\n            <StrumentiList\n              strumenti={strumenti}\n              onEdit={handleEditStrumento}\n              onDelete={loadData}\n              cantiereId={cantiereId}\n            />\n          </>\n        ) : (\n          <StrumentoForm\n            cantiereId={cantiereId}\n            strumento={editingStrumento}\n            onSuccess={handleStrumentoSuccess}\n            onCancel={handleFormCancel}\n          />\n        )}\n      </TabPanel>\n    </Container>\n  );\n}\n\nexport default CertificazioniPage;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAClD,SAASC,SAAS,EAAEC,WAAW,QAAQ,kBAAkB;AACzD,SACEC,SAAS,EACTC,UAAU,EACVC,GAAG,EACHC,MAAM,EACNC,SAAS,EACTC,KAAK,EACLC,KAAK,EACLC,IAAI,EACJC,GAAG,QACE,eAAe;AACtB,SAASC,GAAG,IAAIC,OAAO,EAAEC,UAAU,IAAIC,cAAc,EAAEC,KAAK,IAAIC,SAAS,QAAQ,qBAAqB;AAEtG,SAASC,OAAO,QAAQ,yBAAyB;AACjD,SAASC,UAAU,QAAQ,wBAAwB;AACnD,OAAOC,kBAAkB,MAAM,iDAAiD;AAChF,OAAOC,kBAAkB,MAAM,iDAAiD;AAChF,OAAOC,aAAa,MAAM,4CAA4C;AACtE,OAAOC,aAAa,MAAM,4CAA4C;AAAC,SAAAC,MAAA,IAAAC,OAAA,EAAAC,QAAA,IAAAC,SAAA;AAEvE,SAASC,QAAQA,CAAC;EAAEC,QAAQ;EAAEC,KAAK;EAAEC,KAAK;EAAE,GAAGC;AAAM,CAAC,EAAE;EACtD,oBACEP,OAAA;IACEQ,IAAI,EAAC,UAAU;IACfC,MAAM,EAAEJ,KAAK,KAAKC,KAAM;IACxBI,EAAE,EAAE,2BAA2BJ,KAAK,EAAG;IACvC,mBAAiB,sBAAsBA,KAAK,EAAG;IAAA,GAC3CC,KAAK;IAAAH,QAAA,EAERC,KAAK,KAAKC,KAAK,iBACdN,OAAA,CAACpB,GAAG;MAAC+B,EAAE,EAAE;QAAEC,CAAC,EAAE;MAAE,CAAE;MAAAR,QAAA,EACfA;IAAQ;MAAAS,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACN;EACN;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACE,CAAC;AAEV;AAACC,EAAA,GAhBQd,QAAQ;AAkBjB,SAASe,kBAAkBA,CAAA,EAAG;EAAAC,EAAA;EAC5B,MAAM;IAAEC;EAAW,CAAC,GAAG5C,SAAS,CAAC,CAAC;EAClC,MAAM6C,QAAQ,GAAG5C,WAAW,CAAC,CAAC;EAC9B,MAAM;IAAE6C;EAAK,CAAC,GAAG7B,OAAO,CAAC,CAAC;EAE1B,MAAM,CAAC8B,QAAQ,EAAEC,WAAW,CAAC,GAAGlD,QAAQ,CAAC,CAAC,CAAC;EAC3C,MAAM,CAACmD,QAAQ,EAAEC,WAAW,CAAC,GAAGpD,QAAQ,CAAC,IAAI,CAAC;EAC9C,MAAM,CAACqD,cAAc,EAAEC,iBAAiB,CAAC,GAAGtD,QAAQ,CAAC,EAAE,CAAC;EACxD,MAAM,CAACuD,SAAS,EAAEC,YAAY,CAAC,GAAGxD,QAAQ,CAAC,EAAE,CAAC;EAC9C,MAAM,CAACyD,OAAO,EAAEC,UAAU,CAAC,GAAG1D,QAAQ,CAAC,IAAI,CAAC;EAC5C,MAAM,CAAC2D,KAAK,EAAEC,QAAQ,CAAC,GAAG5D,QAAQ,CAAC,EAAE,CAAC;EACtC,MAAM,CAAC6D,OAAO,EAAEC,UAAU,CAAC,GAAG9D,QAAQ,CAAC,EAAE,CAAC;;EAE1C;EACA,MAAM,CAAC+D,sBAAsB,EAAEC,yBAAyB,CAAC,GAAGhE,QAAQ,CAAC,KAAK,CAAC;EAC3E,MAAM,CAACiE,iBAAiB,EAAEC,oBAAoB,CAAC,GAAGlE,QAAQ,CAAC,KAAK,CAAC;EACjE,MAAM,CAACmE,qBAAqB,EAAEC,wBAAwB,CAAC,GAAGpE,QAAQ,CAAC,IAAI,CAAC;EACxE,MAAM,CAACqE,gBAAgB,EAAEC,mBAAmB,CAAC,GAAGtE,QAAQ,CAAC,IAAI,CAAC;;EAE9D;EACA,MAAM,CAACuE,UAAU,EAAEC,aAAa,CAAC,GAAGxE,QAAQ,CAAC,EAAE,CAAC;EAEhDC,SAAS,CAAC,MAAM;IACdwE,QAAQ,CAAC,CAAC;EACZ,CAAC,EAAE,CAAC3B,UAAU,CAAC,CAAC;EAEhB,MAAM2B,QAAQ,GAAG,MAAAA,CAAA,KAAY;IAC3B,IAAI;MACFf,UAAU,CAAC,IAAI,CAAC;MAChBE,QAAQ,CAAC,EAAE,CAAC;;MAEZ;MACA,MAAMc,YAAY,GAAG,MAAMtD,UAAU,CAACuD,WAAW,CAAC7B,UAAU,CAAC;MAC7DM,WAAW,CAACsB,YAAY,CAAC;;MAEzB;MACA,MAAM,CAACE,kBAAkB,EAAEC,aAAa,CAAC,GAAG,MAAMC,OAAO,CAACC,GAAG,CAAC,CAC5D3D,UAAU,CAAC4D,iBAAiB,CAAClC,UAAU,EAAEyB,UAAU,CAAC,EACpDnD,UAAU,CAAC6D,YAAY,CAACnC,UAAU,CAAC,CACpC,CAAC;MAEFQ,iBAAiB,CAACsB,kBAAkB,CAAC;MACrCpB,YAAY,CAACqB,aAAa,CAAC;IAC7B,CAAC,CAAC,OAAOK,GAAG,EAAE;MAAA,IAAAC,aAAA,EAAAC,kBAAA;MACZC,OAAO,CAAC1B,KAAK,CAAC,kCAAkC,EAAEuB,GAAG,CAAC;MACtDtB,QAAQ,CAAC,mCAAmC,IAAI,EAAAuB,aAAA,GAAAD,GAAG,CAACI,QAAQ,cAAAH,aAAA,wBAAAC,kBAAA,GAAZD,aAAA,CAAcI,IAAI,cAAAH,kBAAA,uBAAlBA,kBAAA,CAAoBI,MAAM,KAAIN,GAAG,CAACO,OAAO,CAAC,CAAC;IAC7F,CAAC,SAAS;MACR/B,UAAU,CAAC,KAAK,CAAC;IACnB;EACF,CAAC;EAED,MAAMgC,eAAe,GAAGA,CAACC,KAAK,EAAEC,QAAQ,KAAK;IAC3C1C,WAAW,CAAC0C,QAAQ,CAAC;IACrB;IACA5B,yBAAyB,CAAC,KAAK,CAAC;IAChCE,oBAAoB,CAAC,KAAK,CAAC;IAC3BE,wBAAwB,CAAC,IAAI,CAAC;IAC9BE,mBAAmB,CAAC,IAAI,CAAC;EAC3B,CAAC;EAED,MAAMuB,sBAAsB,GAAIF,KAAK,IAAK;IACxCnB,aAAa,CAACmB,KAAK,CAACG,MAAM,CAAC/D,KAAK,CAAC;EACnC,CAAC;EAED,MAAMgE,sBAAsB,GAAGA,CAAA,KAAM;IACnCtB,QAAQ,CAAC,CAAC;EACZ,CAAC;EAED,MAAMuB,0BAA0B,GAAGA,CAAA,KAAM;IACvC5B,wBAAwB,CAAC,IAAI,CAAC;IAC9BJ,yBAAyB,CAAC,IAAI,CAAC;EACjC,CAAC;EAED,MAAMiC,wBAAwB,GAAIC,cAAc,IAAK;IACnD9B,wBAAwB,CAAC8B,cAAc,CAAC;IACxClC,yBAAyB,CAAC,IAAI,CAAC;EACjC,CAAC;EAED,MAAMmC,qBAAqB,GAAGA,CAAA,KAAM;IAClC7B,mBAAmB,CAAC,IAAI,CAAC;IACzBJ,oBAAoB,CAAC,IAAI,CAAC;EAC5B,CAAC;EAED,MAAMkC,mBAAmB,GAAIC,SAAS,IAAK;IACzC/B,mBAAmB,CAAC+B,SAAS,CAAC;IAC9BnC,oBAAoB,CAAC,IAAI,CAAC;EAC5B,CAAC;EAED,MAAMoC,2BAA2B,GAAIb,OAAO,IAAK;IAC/C3B,UAAU,CAAC2B,OAAO,CAAC;IACnBzB,yBAAyB,CAAC,KAAK,CAAC;IAChCI,wBAAwB,CAAC,IAAI,CAAC;IAC9BK,QAAQ,CAAC,CAAC;IACV8B,UAAU,CAAC,MAAMzC,UAAU,CAAC,EAAE,CAAC,EAAE,IAAI,CAAC;EACxC,CAAC;EAED,MAAM0C,sBAAsB,GAAIf,OAAO,IAAK;IAC1C3B,UAAU,CAAC2B,OAAO,CAAC;IACnBvB,oBAAoB,CAAC,KAAK,CAAC;IAC3BI,mBAAmB,CAAC,IAAI,CAAC;IACzBG,QAAQ,CAAC,CAAC;IACV8B,UAAU,CAAC,MAAMzC,UAAU,CAAC,EAAE,CAAC,EAAE,IAAI,CAAC;EACxC,CAAC;EAED,MAAM2C,gBAAgB,GAAGA,CAAA,KAAM;IAC7BzC,yBAAyB,CAAC,KAAK,CAAC;IAChCE,oBAAoB,CAAC,KAAK,CAAC;IAC3BE,wBAAwB,CAAC,IAAI,CAAC;IAC9BE,mBAAmB,CAAC,IAAI,CAAC;EAC3B,CAAC;EAED,IAAIb,OAAO,EAAE;IACX,oBACE/B,OAAA,CAACtB,SAAS;MAACsG,QAAQ,EAAC,IAAI;MAACrE,EAAE,EAAE;QAAEsE,EAAE,EAAE,CAAC;QAAEC,EAAE,EAAE;MAAE,CAAE;MAAA9E,QAAA,eAC5CJ,OAAA,CAACrB,UAAU;QAAAyB,QAAA,EAAC;MAAc;QAAAS,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAY;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAC9B,CAAC;EAEhB;EAEA,oBACEhB,OAAA,CAACtB,SAAS;IAACsG,QAAQ,EAAC,IAAI;IAACrE,EAAE,EAAE;MAAEsE,EAAE,EAAE,CAAC;MAAEC,EAAE,EAAE;IAAE,CAAE;IAAA9E,QAAA,gBAE5CJ,OAAA,CAACpB,GAAG;MAAC+B,EAAE,EAAE;QAAEuE,EAAE,EAAE;MAAE,CAAE;MAAA9E,QAAA,gBACjBJ,OAAA,CAACrB,UAAU;QAACwG,OAAO,EAAC,IAAI;QAACC,SAAS,EAAC,IAAI;QAACC,YAAY;QAAAjF,QAAA,EAAC;MAErD;QAAAS,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAY,CAAC,EACZS,QAAQ,iBACPzB,OAAA,CAACrB,UAAU;QAACwG,OAAO,EAAC,IAAI;QAACG,KAAK,EAAC,gBAAgB;QAAAlF,QAAA,GAC5CqB,QAAQ,CAAC8D,aAAa,EAAC,KAAG,EAAC9D,QAAQ,CAAC+D,eAAe;MAAA;QAAA3E,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC1C,CACb;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACE,CAAC,EAGLiB,KAAK,iBACJjC,OAAA,CAAChB,KAAK;MAACyG,QAAQ,EAAC,OAAO;MAAC9E,EAAE,EAAE;QAAEuE,EAAE,EAAE;MAAE,CAAE;MAACQ,OAAO,EAAEA,CAAA,KAAMxD,QAAQ,CAAC,EAAE,CAAE;MAAA9B,QAAA,EAChE6B;IAAK;MAAApB,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACD,CACR,EACAmB,OAAO,iBACNnC,OAAA,CAAChB,KAAK;MAACyG,QAAQ,EAAC,SAAS;MAAC9E,EAAE,EAAE;QAAEuE,EAAE,EAAE;MAAE,CAAE;MAACQ,OAAO,EAAEA,CAAA,KAAMtD,UAAU,CAAC,EAAE,CAAE;MAAAhC,QAAA,EACpE+B;IAAO;MAAAtB,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CACR,eAGDhB,OAAA,CAACjB,KAAK;MAAC4B,EAAE,EAAE;QAAEuE,EAAE,EAAE;MAAE,CAAE;MAAA9E,QAAA,eACnBJ,OAAA,CAACf,IAAI;QACHoB,KAAK,EAAEkB,QAAS;QAChBoE,QAAQ,EAAE3B,eAAgB;QAC1B4B,cAAc,EAAC,SAAS;QACxBC,SAAS,EAAC,SAAS;QAAAzF,QAAA,gBAEnBJ,OAAA,CAACd,GAAG;UACF4G,IAAI,eAAE9F,OAAA,CAACV,cAAc;YAAAuB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAE;UACzB+E,KAAK,EAAC,gBAAgB;UACtBrF,EAAE,EAAC,sBAAsB;UACzB,iBAAc;QAA2B;UAAAG,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC1C,CAAC,eACFhB,OAAA,CAACd,GAAG;UACF4G,IAAI,eAAE9F,OAAA,CAACR,SAAS;YAAAqB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAE;UACpB+E,KAAK,EAAC,WAAW;UACjBrF,EAAE,EAAC,sBAAsB;UACzB,iBAAc;QAA2B;UAAAG,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC1C,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACE;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACF,CAAC,eAGRhB,OAAA,CAACG,QAAQ;MAACE,KAAK,EAAEkB,QAAS;MAACjB,KAAK,EAAE,CAAE;MAAAF,QAAA,EACjC,CAACiC,sBAAsB,gBACtBrC,OAAA,CAAAE,SAAA;QAAAE,QAAA,gBAEEJ,OAAA,CAACpB,GAAG;UAAC+B,EAAE,EAAE;YAAEuE,EAAE,EAAE,CAAC;YAAEc,OAAO,EAAE,MAAM;YAAEC,GAAG,EAAE,CAAC;YAAEC,UAAU,EAAE,QAAQ;YAAEC,QAAQ,EAAE;UAAO,CAAE;UAAA/F,QAAA,gBAClFJ,OAAA,CAAClB,SAAS;YACRiH,KAAK,EAAC,oBAAoB;YAC1B1F,KAAK,EAAEwC,UAAW;YAClB8C,QAAQ,EAAExB,sBAAuB;YACjCiC,UAAU,EAAGC,CAAC,IAAKA,CAAC,CAACC,GAAG,KAAK,OAAO,IAAIjC,sBAAsB,CAAC,CAAE;YACjEkC,IAAI,EAAC,OAAO;YACZ5F,EAAE,EAAE;cAAE6F,QAAQ,EAAE;YAAI;UAAE;YAAA3F,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACvB,CAAC,eACFhB,OAAA,CAACnB,MAAM;YACLsG,OAAO,EAAC,UAAU;YAClBsB,OAAO,EAAEpC,sBAAuB;YAAAjE,QAAA,EACjC;UAED;YAAAS,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC,eACThB,OAAA,CAACnB,MAAM;YACLsG,OAAO,EAAC,WAAW;YACnBuB,SAAS,eAAE1G,OAAA,CAACZ,OAAO;cAAAyB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAE;YACvByF,OAAO,EAAEnC,0BAA2B;YACpC3D,EAAE,EAAE;cAAEgG,EAAE,EAAE;YAAO,CAAE;YAAAvG,QAAA,EACpB;UAED;YAAAS,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACN,CAAC,eAGNhB,OAAA,CAACL,kBAAkB;UACjBgC,cAAc,EAAEA,cAAe;UAC/BiF,MAAM,EAAErC,wBAAyB;UACjCsC,QAAQ,EAAE9D,QAAS;UACnB3B,UAAU,EAAEA;QAAW;UAAAP,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACxB,CAAC;MAAA,eACF,CAAC,gBAEHhB,OAAA,CAACJ,kBAAkB;QACjBwB,UAAU,EAAEA,UAAW;QACvBoD,cAAc,EAAE/B,qBAAsB;QACtCZ,SAAS,EAAEA,SAAU;QACrBiF,SAAS,EAAElC,2BAA4B;QACvCmC,QAAQ,EAAEhC;MAAiB;QAAAlE,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC5B;IACF;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACO,CAAC,eAGXhB,OAAA,CAACG,QAAQ;MAACE,KAAK,EAAEkB,QAAS;MAACjB,KAAK,EAAE,CAAE;MAAAF,QAAA,EACjC,CAACmC,iBAAiB,gBACjBvC,OAAA,CAAAE,SAAA;QAAAE,QAAA,gBAEEJ,OAAA,CAACpB,GAAG;UAAC+B,EAAE,EAAE;YAAEuE,EAAE,EAAE,CAAC;YAAEc,OAAO,EAAE,MAAM;YAAEgB,cAAc,EAAE;UAAW,CAAE;UAAA5G,QAAA,eAC9DJ,OAAA,CAACnB,MAAM;YACLsG,OAAO,EAAC,WAAW;YACnBuB,SAAS,eAAE1G,OAAA,CAACZ,OAAO;cAAAyB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAE;YACvByF,OAAO,EAAEhC,qBAAsB;YAAArE,QAAA,EAChC;UAED;YAAAS,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACN,CAAC,eAGNhB,OAAA,CAACH,aAAa;UACZgC,SAAS,EAAEA,SAAU;UACrB+E,MAAM,EAAElC,mBAAoB;UAC5BmC,QAAQ,EAAE9D,QAAS;UACnB3B,UAAU,EAAEA;QAAW;UAAAP,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACxB,CAAC;MAAA,eACF,CAAC,gBAEHhB,OAAA,CAACF,aAAa;QACZsB,UAAU,EAAEA,UAAW;QACvBuD,SAAS,EAAEhC,gBAAiB;QAC5BmE,SAAS,EAAEhC,sBAAuB;QAClCiC,QAAQ,EAAEhC;MAAiB;QAAAlE,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC5B;IACF;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACO,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACF,CAAC;AAEhB;AAACG,EAAA,CA3PQD,kBAAkB;EAAA,QACF1C,SAAS,EACfC,WAAW,EACXgB,OAAO;AAAA;AAAAwH,GAAA,GAHjB/F,kBAAkB;AA6P3B,eAAeA,kBAAkB;AAAC,IAAAD,EAAA,EAAAgG,GAAA;AAAAC,YAAA,CAAAjG,EAAA;AAAAiG,YAAA,CAAAD,GAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}