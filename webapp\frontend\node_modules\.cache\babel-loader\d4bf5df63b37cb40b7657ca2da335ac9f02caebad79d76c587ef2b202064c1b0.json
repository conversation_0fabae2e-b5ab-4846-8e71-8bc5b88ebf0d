{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\PycharmProjects\\\\CMS\\\\webapp\\\\frontend\\\\src\\\\pages\\\\Dashboard.js\",\n  _s = $RefreshSig$();\nimport React from 'react';\nimport { Routes, Route, Navigate } from 'react-router-dom';\nimport { Box, CssBaseline } from '@mui/material';\nimport { useAuth } from '../context/AuthContext';\nimport TopNavbar from '../components/TopNavbar';\nimport HomePage from './HomePage';\nimport AdminPage from './AdminPage';\nimport UserPage from './UserPage';\nimport CaviPage from './CaviPage';\nimport UserExpirationChecker from '../components/admin/UserExpirationChecker';\n\n// Importa le nuove pagine per i cavi\nimport VisualizzaCaviPage from './cavi/VisualizzaCaviPage';\nimport ParcoCaviPage from './cavi/ParcoCaviPage';\nimport ReportCaviPage from './cavi/ReportCaviPage';\nimport ReportCaviPageNew from './cavi/ReportCaviPageNew';\nimport CertificazioneCaviPage from './cavi/CertificazioneCaviPage';\nimport CertificazioniPageDebug from './CertificazioniPageDebug';\nimport GestioneComandeePage from './cavi/GestioneComandeePage';\nimport TestCaviPage from './cavi/TestCaviPage';\nimport TestBobinePage from './TestBobinePage';\n\n// Importa le pagine per Parco Cavi\nimport VisualizzaBobinePage from './cavi/parco/VisualizzaBobinePage';\nimport CreaBobinaPage from './cavi/parco/CreaBobinaPage';\nimport ParcoCaviModificaBobinaPage from './cavi/parco/ModificaBobinaPage';\nimport EliminaBobinaPage from './cavi/parco/EliminaBobinaPage';\nimport StoricoUtilizzoPage from './cavi/parco/StoricoUtilizzoPage';\n\n// Importa le pagine per Posa e Collegamenti\nimport InserisciMetriPage from './cavi/posa/InserisciMetriPage';\nimport MetriPosatiSemplificatoPage from './cavi/posa/MetriPosatiSemplificatoPage';\nimport ModificaCavoPage from './cavi/posa/ModificaCavoPage';\nimport PosaCaviModificaBobinaPage from './cavi/posa/ModificaBobinaPage';\nimport CollegamentiPage from './cavi/posa/CollegamentiPage';\nimport RedirectToCaviVisualizza from '../components/common/RedirectToCaviVisualizza';\n\n// Importa la pagina per il cantiere specifico\nimport CantierePage from './cantieri/CantierePage';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst Dashboard = () => {\n  _s();\n  const {\n    user\n  } = useAuth();\n  return /*#__PURE__*/_jsxDEV(Box, {\n    sx: {\n      display: 'flex',\n      flexDirection: 'column'\n    },\n    children: [/*#__PURE__*/_jsxDEV(UserExpirationChecker, {}, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 49,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(CssBaseline, {}, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 50,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(TopNavbar, {}, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 51,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Box, {\n      component: \"main\",\n      sx: {\n        flexGrow: 1,\n        p: 3,\n        width: '100%',\n        backgroundColor: '#f5f5f5',\n        // Sfondo grigio chiaro per l'area principale\n        minHeight: 'calc(100vh - 40px)',\n        // Altezza minima per coprire l'intera viewport meno l'altezza della navbar\n        overflowX: 'hidden' // Previene scrollbar orizzontale\n      },\n      children: /*#__PURE__*/_jsxDEV(Routes, {\n        children: [/*#__PURE__*/_jsxDEV(Route, {\n          path: \"/\",\n          element: /*#__PURE__*/_jsxDEV(HomePage, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 64,\n            columnNumber: 36\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 64,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Route, {\n          path: \"/admin\",\n          element: /*#__PURE__*/_jsxDEV(AdminPage, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 65,\n            columnNumber: 41\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 65,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Route, {\n          path: \"/cantieri\",\n          element: /*#__PURE__*/_jsxDEV(UserPage, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 66,\n            columnNumber: 44\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 66,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Route, {\n          path: \"/cantieri/:cantiereId\",\n          element: /*#__PURE__*/_jsxDEV(CantierePage, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 67,\n            columnNumber: 56\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 67,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Route, {\n          path: \"/cantieri/:cantiereId/certificazioni\",\n          element: /*#__PURE__*/_jsxDEV(CertificazioniPageDebug, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 68,\n            columnNumber: 71\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 68,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Route, {\n          path: \"/cavi\",\n          element: /*#__PURE__*/_jsxDEV(CaviPage, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 71,\n            columnNumber: 40\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 71,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Route, {\n          path: \"/cavi/visualizza\",\n          element: /*#__PURE__*/_jsxDEV(VisualizzaCaviPage, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 72,\n            columnNumber: 51\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 72,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Route, {\n          path: \"/cavi/posa\",\n          element: /*#__PURE__*/_jsxDEV(Navigate, {\n            to: \"/dashboard/cavi/visualizza\",\n            replace: true\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 73,\n            columnNumber: 45\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 73,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Route, {\n          path: \"/cavi/parco\",\n          element: /*#__PURE__*/_jsxDEV(ParcoCaviPage, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 74,\n            columnNumber: 46\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 74,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Route, {\n          path: \"/cavi/excel\",\n          element: /*#__PURE__*/_jsxDEV(Navigate, {\n            to: \"/dashboard/cavi/visualizza\",\n            replace: true\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 75,\n            columnNumber: 46\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 75,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Route, {\n          path: \"/cavi/report\",\n          element: /*#__PURE__*/_jsxDEV(ReportCaviPageNew, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 76,\n            columnNumber: 47\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 76,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Route, {\n          path: \"/cavi/:cantiereId/report\",\n          element: /*#__PURE__*/_jsxDEV(ReportCaviPageNew, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 77,\n            columnNumber: 59\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 77,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Route, {\n          path: \"/cavi/certificazione\",\n          element: /*#__PURE__*/_jsxDEV(CertificazioneCaviPage, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 79,\n            columnNumber: 55\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 79,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Route, {\n          path: \"/cavi/certificazione/visualizza\",\n          element: /*#__PURE__*/_jsxDEV(CertificazioneCaviPage, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 80,\n            columnNumber: 66\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 80,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Route, {\n          path: \"/cavi/certificazione/filtra\",\n          element: /*#__PURE__*/_jsxDEV(CertificazioneCaviPage, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 81,\n            columnNumber: 62\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 81,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Route, {\n          path: \"/cavi/certificazione/crea\",\n          element: /*#__PURE__*/_jsxDEV(CertificazioneCaviPage, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 82,\n            columnNumber: 60\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 82,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Route, {\n          path: \"/cavi/certificazione/modifica\",\n          element: /*#__PURE__*/_jsxDEV(CertificazioneCaviPage, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 83,\n            columnNumber: 64\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 83,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Route, {\n          path: \"/cavi/certificazione/dettagli\",\n          element: /*#__PURE__*/_jsxDEV(CertificazioneCaviPage, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 84,\n            columnNumber: 64\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 84,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Route, {\n          path: \"/cavi/certificazione/pdf\",\n          element: /*#__PURE__*/_jsxDEV(CertificazioneCaviPage, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 85,\n            columnNumber: 59\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 85,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Route, {\n          path: \"/cavi/certificazione/elimina\",\n          element: /*#__PURE__*/_jsxDEV(CertificazioneCaviPage, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 86,\n            columnNumber: 63\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 86,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Route, {\n          path: \"/cavi/certificazione/strumenti\",\n          element: /*#__PURE__*/_jsxDEV(CertificazioneCaviPage, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 87,\n            columnNumber: 65\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 87,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Route, {\n          path: \"/cavi/comande\",\n          element: /*#__PURE__*/_jsxDEV(GestioneComandeePage, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 88,\n            columnNumber: 48\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 88,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Route, {\n          path: \"/cavi/test\",\n          element: /*#__PURE__*/_jsxDEV(TestCaviPage, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 89,\n            columnNumber: 45\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 89,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Route, {\n          path: \"/cavi/test-bobine/:cantiereId\",\n          element: /*#__PURE__*/_jsxDEV(TestBobinePage, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 92,\n            columnNumber: 64\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 92,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Route, {\n          path: \"/cavi/parco/visualizza\",\n          element: /*#__PURE__*/_jsxDEV(VisualizzaBobinePage, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 95,\n            columnNumber: 57\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 95,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Route, {\n          path: \"/cavi/parco/crea\",\n          element: /*#__PURE__*/_jsxDEV(CreaBobinaPage, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 96,\n            columnNumber: 51\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 96,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Route, {\n          path: \"/cavi/parco/modifica\",\n          element: /*#__PURE__*/_jsxDEV(ParcoCaviModificaBobinaPage, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 97,\n            columnNumber: 55\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 97,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Route, {\n          path: \"/cavi/parco/elimina\",\n          element: /*#__PURE__*/_jsxDEV(EliminaBobinaPage, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 98,\n            columnNumber: 54\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 98,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Route, {\n          path: \"/cavi/parco/storico\",\n          element: /*#__PURE__*/_jsxDEV(StoricoUtilizzoPage, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 99,\n            columnNumber: 54\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 99,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Route, {\n          path: \"/cavi/posa/inserisci-metri\",\n          element: /*#__PURE__*/_jsxDEV(InserisciMetriPage, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 102,\n            columnNumber: 61\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 102,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Route, {\n          path: \"/cavi/posa/metri-posati-semplificato\",\n          element: /*#__PURE__*/_jsxDEV(MetriPosatiSemplificatoPage, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 103,\n            columnNumber: 71\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 103,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Route, {\n          path: \"/cavi/posa/modifica-cavo\",\n          element: /*#__PURE__*/_jsxDEV(ModificaCavoPage, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 105,\n            columnNumber: 59\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 105,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Route, {\n          path: \"/cantieri/:cantiereId/cavi/posa/modifica-cavo\",\n          element: /*#__PURE__*/_jsxDEV(ModificaCavoPage, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 106,\n            columnNumber: 80\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 106,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Route, {\n          path: \"/cavi/posa/modifica-bobina\",\n          element: /*#__PURE__*/_jsxDEV(PosaCaviModificaBobinaPage, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 108,\n            columnNumber: 61\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 108,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Route, {\n          path: \"/cantieri/:cantiereId/cavi/posa/modifica-bobina/:cavoId?\",\n          element: /*#__PURE__*/_jsxDEV(PosaCaviModificaBobinaPage, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 109,\n            columnNumber: 91\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 109,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Route, {\n          path: \"/cavi/posa/collegamenti\",\n          element: /*#__PURE__*/_jsxDEV(CollegamentiPage, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 110,\n            columnNumber: 58\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 110,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Route, {\n          path: \"/cantieri/:cantiereId/cavi/posa/collegamenti\",\n          element: /*#__PURE__*/_jsxDEV(CollegamentiPage, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 111,\n            columnNumber: 79\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 111,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 63,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 52,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 47,\n    columnNumber: 5\n  }, this);\n};\n_s(Dashboard, \"9ep4vdl3mBfipxjmc+tQCDhw6Ik=\", false, function () {\n  return [useAuth];\n});\n_c = Dashboard;\nexport default Dashboard;\nvar _c;\n$RefreshReg$(_c, \"Dashboard\");", "map": {"version": 3, "names": ["React", "Routes", "Route", "Navigate", "Box", "CssBaseline", "useAuth", "TopNavbar", "HomePage", "AdminPage", "UserPage", "CaviPage", "UserExpirationChecker", "VisualizzaCaviPage", "ParcoCaviPage", "ReportCaviPage", "ReportCaviPageNew", "CertificazioneCaviPage", "CertificazioniPageDebug", "GestioneComandeePage", "TestCaviPage", "TestBobinePage", "VisualizzaBobinePage", "CreaBobinaPage", "ParcoCaviModificaBobinaPage", "EliminaBobinaPage", "StoricoUtilizzoPage", "InserisciMetriPage", "MetriPosatiSemplificatoPage", "ModificaCavoPage", "PosaCaviModificaBobinaPage", "CollegamentiPage", "RedirectToCaviVisualizza", "CantierePage", "jsxDEV", "_jsxDEV", "Dashboard", "_s", "user", "sx", "display", "flexDirection", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "component", "flexGrow", "p", "width", "backgroundColor", "minHeight", "overflowX", "path", "element", "to", "replace", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/PycharmProjects/CMS/webapp/frontend/src/pages/Dashboard.js"], "sourcesContent": ["import React from 'react';\nimport { Routes, Route, Navigate } from 'react-router-dom';\nimport { Box, CssBaseline } from '@mui/material';\n\nimport { useAuth } from '../context/AuthContext';\nimport TopNavbar from '../components/TopNavbar';\nimport HomePage from './HomePage';\nimport AdminPage from './AdminPage';\nimport UserPage from './UserPage';\nimport CaviPage from './CaviPage';\nimport UserExpirationChecker from '../components/admin/UserExpirationChecker';\n\n// Importa le nuove pagine per i cavi\nimport VisualizzaCaviPage from './cavi/VisualizzaCaviPage';\nimport ParcoCaviPage from './cavi/ParcoCaviPage';\nimport ReportCaviPage from './cavi/ReportCaviPage';\nimport ReportCaviPageNew from './cavi/ReportCaviPageNew';\nimport CertificazioneCaviPage from './cavi/CertificazioneCaviPage';\nimport CertificazioniPageDebug from './CertificazioniPageDebug';\nimport GestioneComandeePage from './cavi/GestioneComandeePage';\nimport TestCaviPage from './cavi/TestCaviPage';\nimport TestBobinePage from './TestBobinePage';\n\n// Importa le pagine per Parco Cavi\nimport VisualizzaBobinePage from './cavi/parco/VisualizzaBobinePage';\nimport CreaBobinaPage from './cavi/parco/CreaBobinaPage';\nimport ParcoCaviModificaBobinaPage from './cavi/parco/ModificaBobinaPage';\nimport EliminaBobinaPage from './cavi/parco/EliminaBobinaPage';\nimport StoricoUtilizzoPage from './cavi/parco/StoricoUtilizzoPage';\n\n// Importa le pagine per Posa e Collegamenti\nimport InserisciMetriPage from './cavi/posa/InserisciMetriPage';\nimport MetriPosatiSemplificatoPage from './cavi/posa/MetriPosatiSemplificatoPage';\nimport ModificaCavoPage from './cavi/posa/ModificaCavoPage';\n\nimport PosaCaviModificaBobinaPage from './cavi/posa/ModificaBobinaPage';\nimport CollegamentiPage from './cavi/posa/CollegamentiPage';\nimport RedirectToCaviVisualizza from '../components/common/RedirectToCaviVisualizza';\n\n// Importa la pagina per il cantiere specifico\nimport CantierePage from './cantieri/CantierePage';\n\nconst Dashboard = () => {\n  const { user } = useAuth();\n\n  return (\n    <Box sx={{ display: 'flex', flexDirection: 'column' }}>\n      {/* Componente invisibile che verifica gli utenti scaduti */}\n      <UserExpirationChecker />\n      <CssBaseline />\n      <TopNavbar />\n      <Box\n        component=\"main\"\n        sx={{\n          flexGrow: 1,\n          p: 3,\n          width: '100%',\n          backgroundColor: '#f5f5f5', // Sfondo grigio chiaro per l'area principale\n          minHeight: 'calc(100vh - 40px)', // Altezza minima per coprire l'intera viewport meno l'altezza della navbar\n          overflowX: 'hidden' // Previene scrollbar orizzontale\n        }}\n      >\n        <Routes>\n          <Route path=\"/\" element={<HomePage />} />\n          <Route path=\"/admin\" element={<AdminPage />} />\n          <Route path=\"/cantieri\" element={<UserPage />} />\n          <Route path=\"/cantieri/:cantiereId\" element={<CantierePage />} />\n          <Route path=\"/cantieri/:cantiereId/certificazioni\" element={<CertificazioniPageDebug />} />\n\n          {/* Route per la gestione cavi */}\n          <Route path=\"/cavi\" element={<CaviPage />} />\n          <Route path=\"/cavi/visualizza\" element={<VisualizzaCaviPage />} />\n          <Route path=\"/cavi/posa\" element={<Navigate to=\"/dashboard/cavi/visualizza\" replace />} />\n          <Route path=\"/cavi/parco\" element={<ParcoCaviPage />} />\n          <Route path=\"/cavi/excel\" element={<Navigate to=\"/dashboard/cavi/visualizza\" replace />} />\n          <Route path=\"/cavi/report\" element={<ReportCaviPageNew />} />\n          <Route path=\"/cavi/:cantiereId/report\" element={<ReportCaviPageNew />} />\n\n          <Route path=\"/cavi/certificazione\" element={<CertificazioneCaviPage />} />\n          <Route path=\"/cavi/certificazione/visualizza\" element={<CertificazioneCaviPage />} />\n          <Route path=\"/cavi/certificazione/filtra\" element={<CertificazioneCaviPage />} />\n          <Route path=\"/cavi/certificazione/crea\" element={<CertificazioneCaviPage />} />\n          <Route path=\"/cavi/certificazione/modifica\" element={<CertificazioneCaviPage />} />\n          <Route path=\"/cavi/certificazione/dettagli\" element={<CertificazioneCaviPage />} />\n          <Route path=\"/cavi/certificazione/pdf\" element={<CertificazioneCaviPage />} />\n          <Route path=\"/cavi/certificazione/elimina\" element={<CertificazioneCaviPage />} />\n          <Route path=\"/cavi/certificazione/strumenti\" element={<CertificazioneCaviPage />} />\n          <Route path=\"/cavi/comande\" element={<GestioneComandeePage />} />\n          <Route path=\"/cavi/test\" element={<TestCaviPage />} />\n\n          {/* Route per la pagina di test delle bobine */}\n          <Route path=\"/cavi/test-bobine/:cantiereId\" element={<TestBobinePage />} />\n\n          {/* Route per Parco Cavi */}\n          <Route path=\"/cavi/parco/visualizza\" element={<VisualizzaBobinePage />} />\n          <Route path=\"/cavi/parco/crea\" element={<CreaBobinaPage />} />\n          <Route path=\"/cavi/parco/modifica\" element={<ParcoCaviModificaBobinaPage />} />\n          <Route path=\"/cavi/parco/elimina\" element={<EliminaBobinaPage />} />\n          <Route path=\"/cavi/parco/storico\" element={<StoricoUtilizzoPage />} />\n\n          {/* Route per Posa e Collegamenti */}\n          <Route path=\"/cavi/posa/inserisci-metri\" element={<InserisciMetriPage />} />\n          <Route path=\"/cavi/posa/metri-posati-semplificato\" element={<MetriPosatiSemplificatoPage />} />\n          {/* Modifica cavo ora ha una pagina dedicata */}\n          <Route path=\"/cavi/posa/modifica-cavo\" element={<ModificaCavoPage />} />\n          <Route path=\"/cantieri/:cantiereId/cavi/posa/modifica-cavo\" element={<ModificaCavoPage />} />\n\n          <Route path=\"/cavi/posa/modifica-bobina\" element={<PosaCaviModificaBobinaPage />} />\n          <Route path=\"/cantieri/:cantiereId/cavi/posa/modifica-bobina/:cavoId?\" element={<PosaCaviModificaBobinaPage />} />\n          <Route path=\"/cavi/posa/collegamenti\" element={<CollegamentiPage />} />\n          <Route path=\"/cantieri/:cantiereId/cavi/posa/collegamenti\" element={<CollegamentiPage />} />\n\n          {/* Altre route verranno aggiunte man mano che vengono implementate */}\n        </Routes>\n      </Box>\n    </Box>\n  );\n};\n\nexport default Dashboard;\n"], "mappings": ";;AAAA,OAAOA,KAAK,MAAM,OAAO;AACzB,SAASC,MAAM,EAAEC,KAAK,EAAEC,QAAQ,QAAQ,kBAAkB;AAC1D,SAASC,GAAG,EAAEC,WAAW,QAAQ,eAAe;AAEhD,SAASC,OAAO,QAAQ,wBAAwB;AAChD,OAAOC,SAAS,MAAM,yBAAyB;AAC/C,OAAOC,QAAQ,MAAM,YAAY;AACjC,OAAOC,SAAS,MAAM,aAAa;AACnC,OAAOC,QAAQ,MAAM,YAAY;AACjC,OAAOC,QAAQ,MAAM,YAAY;AACjC,OAAOC,qBAAqB,MAAM,2CAA2C;;AAE7E;AACA,OAAOC,kBAAkB,MAAM,2BAA2B;AAC1D,OAAOC,aAAa,MAAM,sBAAsB;AAChD,OAAOC,cAAc,MAAM,uBAAuB;AAClD,OAAOC,iBAAiB,MAAM,0BAA0B;AACxD,OAAOC,sBAAsB,MAAM,+BAA+B;AAClE,OAAOC,uBAAuB,MAAM,2BAA2B;AAC/D,OAAOC,oBAAoB,MAAM,6BAA6B;AAC9D,OAAOC,YAAY,MAAM,qBAAqB;AAC9C,OAAOC,cAAc,MAAM,kBAAkB;;AAE7C;AACA,OAAOC,oBAAoB,MAAM,mCAAmC;AACpE,OAAOC,cAAc,MAAM,6BAA6B;AACxD,OAAOC,2BAA2B,MAAM,iCAAiC;AACzE,OAAOC,iBAAiB,MAAM,gCAAgC;AAC9D,OAAOC,mBAAmB,MAAM,kCAAkC;;AAElE;AACA,OAAOC,kBAAkB,MAAM,gCAAgC;AAC/D,OAAOC,2BAA2B,MAAM,yCAAyC;AACjF,OAAOC,gBAAgB,MAAM,8BAA8B;AAE3D,OAAOC,0BAA0B,MAAM,gCAAgC;AACvE,OAAOC,gBAAgB,MAAM,8BAA8B;AAC3D,OAAOC,wBAAwB,MAAM,+CAA+C;;AAEpF;AACA,OAAOC,YAAY,MAAM,yBAAyB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAEnD,MAAMC,SAAS,GAAGA,CAAA,KAAM;EAAAC,EAAA;EACtB,MAAM;IAAEC;EAAK,CAAC,GAAGhC,OAAO,CAAC,CAAC;EAE1B,oBACE6B,OAAA,CAAC/B,GAAG;IAACmC,EAAE,EAAE;MAAEC,OAAO,EAAE,MAAM;MAAEC,aAAa,EAAE;IAAS,CAAE;IAAAC,QAAA,gBAEpDP,OAAA,CAACvB,qBAAqB;MAAA+B,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC,eACzBX,OAAA,CAAC9B,WAAW;MAAAsC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC,eACfX,OAAA,CAAC5B,SAAS;MAAAoC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC,eACbX,OAAA,CAAC/B,GAAG;MACF2C,SAAS,EAAC,MAAM;MAChBR,EAAE,EAAE;QACFS,QAAQ,EAAE,CAAC;QACXC,CAAC,EAAE,CAAC;QACJC,KAAK,EAAE,MAAM;QACbC,eAAe,EAAE,SAAS;QAAE;QAC5BC,SAAS,EAAE,oBAAoB;QAAE;QACjCC,SAAS,EAAE,QAAQ,CAAC;MACtB,CAAE;MAAAX,QAAA,eAEFP,OAAA,CAAClC,MAAM;QAAAyC,QAAA,gBACLP,OAAA,CAACjC,KAAK;UAACoD,IAAI,EAAC,GAAG;UAACC,OAAO,eAAEpB,OAAA,CAAC3B,QAAQ;YAAAmC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAE;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,eACzCX,OAAA,CAACjC,KAAK;UAACoD,IAAI,EAAC,QAAQ;UAACC,OAAO,eAAEpB,OAAA,CAAC1B,SAAS;YAAAkC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAE;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,eAC/CX,OAAA,CAACjC,KAAK;UAACoD,IAAI,EAAC,WAAW;UAACC,OAAO,eAAEpB,OAAA,CAACzB,QAAQ;YAAAiC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAE;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,eACjDX,OAAA,CAACjC,KAAK;UAACoD,IAAI,EAAC,uBAAuB;UAACC,OAAO,eAAEpB,OAAA,CAACF,YAAY;YAAAU,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAE;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,eACjEX,OAAA,CAACjC,KAAK;UAACoD,IAAI,EAAC,sCAAsC;UAACC,OAAO,eAAEpB,OAAA,CAACjB,uBAAuB;YAAAyB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAE;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,eAG3FX,OAAA,CAACjC,KAAK;UAACoD,IAAI,EAAC,OAAO;UAACC,OAAO,eAAEpB,OAAA,CAACxB,QAAQ;YAAAgC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAE;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,eAC7CX,OAAA,CAACjC,KAAK;UAACoD,IAAI,EAAC,kBAAkB;UAACC,OAAO,eAAEpB,OAAA,CAACtB,kBAAkB;YAAA8B,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAE;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,eAClEX,OAAA,CAACjC,KAAK;UAACoD,IAAI,EAAC,YAAY;UAACC,OAAO,eAAEpB,OAAA,CAAChC,QAAQ;YAACqD,EAAE,EAAC,4BAA4B;YAACC,OAAO;UAAA;YAAAd,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAE;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,eAC1FX,OAAA,CAACjC,KAAK;UAACoD,IAAI,EAAC,aAAa;UAACC,OAAO,eAAEpB,OAAA,CAACrB,aAAa;YAAA6B,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAE;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,eACxDX,OAAA,CAACjC,KAAK;UAACoD,IAAI,EAAC,aAAa;UAACC,OAAO,eAAEpB,OAAA,CAAChC,QAAQ;YAACqD,EAAE,EAAC,4BAA4B;YAACC,OAAO;UAAA;YAAAd,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAE;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,eAC3FX,OAAA,CAACjC,KAAK;UAACoD,IAAI,EAAC,cAAc;UAACC,OAAO,eAAEpB,OAAA,CAACnB,iBAAiB;YAAA2B,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAE;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,eAC7DX,OAAA,CAACjC,KAAK;UAACoD,IAAI,EAAC,0BAA0B;UAACC,OAAO,eAAEpB,OAAA,CAACnB,iBAAiB;YAAA2B,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAE;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,eAEzEX,OAAA,CAACjC,KAAK;UAACoD,IAAI,EAAC,sBAAsB;UAACC,OAAO,eAAEpB,OAAA,CAAClB,sBAAsB;YAAA0B,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAE;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,eAC1EX,OAAA,CAACjC,KAAK;UAACoD,IAAI,EAAC,iCAAiC;UAACC,OAAO,eAAEpB,OAAA,CAAClB,sBAAsB;YAAA0B,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAE;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,eACrFX,OAAA,CAACjC,KAAK;UAACoD,IAAI,EAAC,6BAA6B;UAACC,OAAO,eAAEpB,OAAA,CAAClB,sBAAsB;YAAA0B,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAE;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,eACjFX,OAAA,CAACjC,KAAK;UAACoD,IAAI,EAAC,2BAA2B;UAACC,OAAO,eAAEpB,OAAA,CAAClB,sBAAsB;YAAA0B,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAE;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,eAC/EX,OAAA,CAACjC,KAAK;UAACoD,IAAI,EAAC,+BAA+B;UAACC,OAAO,eAAEpB,OAAA,CAAClB,sBAAsB;YAAA0B,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAE;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,eACnFX,OAAA,CAACjC,KAAK;UAACoD,IAAI,EAAC,+BAA+B;UAACC,OAAO,eAAEpB,OAAA,CAAClB,sBAAsB;YAAA0B,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAE;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,eACnFX,OAAA,CAACjC,KAAK;UAACoD,IAAI,EAAC,0BAA0B;UAACC,OAAO,eAAEpB,OAAA,CAAClB,sBAAsB;YAAA0B,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAE;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,eAC9EX,OAAA,CAACjC,KAAK;UAACoD,IAAI,EAAC,8BAA8B;UAACC,OAAO,eAAEpB,OAAA,CAAClB,sBAAsB;YAAA0B,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAE;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,eAClFX,OAAA,CAACjC,KAAK;UAACoD,IAAI,EAAC,gCAAgC;UAACC,OAAO,eAAEpB,OAAA,CAAClB,sBAAsB;YAAA0B,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAE;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,eACpFX,OAAA,CAACjC,KAAK;UAACoD,IAAI,EAAC,eAAe;UAACC,OAAO,eAAEpB,OAAA,CAAChB,oBAAoB;YAAAwB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAE;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,eACjEX,OAAA,CAACjC,KAAK;UAACoD,IAAI,EAAC,YAAY;UAACC,OAAO,eAAEpB,OAAA,CAACf,YAAY;YAAAuB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAE;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,eAGtDX,OAAA,CAACjC,KAAK;UAACoD,IAAI,EAAC,+BAA+B;UAACC,OAAO,eAAEpB,OAAA,CAACd,cAAc;YAAAsB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAE;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,eAG3EX,OAAA,CAACjC,KAAK;UAACoD,IAAI,EAAC,wBAAwB;UAACC,OAAO,eAAEpB,OAAA,CAACb,oBAAoB;YAAAqB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAE;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,eAC1EX,OAAA,CAACjC,KAAK;UAACoD,IAAI,EAAC,kBAAkB;UAACC,OAAO,eAAEpB,OAAA,CAACZ,cAAc;YAAAoB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAE;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,eAC9DX,OAAA,CAACjC,KAAK;UAACoD,IAAI,EAAC,sBAAsB;UAACC,OAAO,eAAEpB,OAAA,CAACX,2BAA2B;YAAAmB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAE;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,eAC/EX,OAAA,CAACjC,KAAK;UAACoD,IAAI,EAAC,qBAAqB;UAACC,OAAO,eAAEpB,OAAA,CAACV,iBAAiB;YAAAkB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAE;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,eACpEX,OAAA,CAACjC,KAAK;UAACoD,IAAI,EAAC,qBAAqB;UAACC,OAAO,eAAEpB,OAAA,CAACT,mBAAmB;YAAAiB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAE;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,eAGtEX,OAAA,CAACjC,KAAK;UAACoD,IAAI,EAAC,4BAA4B;UAACC,OAAO,eAAEpB,OAAA,CAACR,kBAAkB;YAAAgB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAE;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,eAC5EX,OAAA,CAACjC,KAAK;UAACoD,IAAI,EAAC,sCAAsC;UAACC,OAAO,eAAEpB,OAAA,CAACP,2BAA2B;YAAAe,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAE;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,eAE/FX,OAAA,CAACjC,KAAK;UAACoD,IAAI,EAAC,0BAA0B;UAACC,OAAO,eAAEpB,OAAA,CAACN,gBAAgB;YAAAc,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAE;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,eACxEX,OAAA,CAACjC,KAAK;UAACoD,IAAI,EAAC,+CAA+C;UAACC,OAAO,eAAEpB,OAAA,CAACN,gBAAgB;YAAAc,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAE;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,eAE7FX,OAAA,CAACjC,KAAK;UAACoD,IAAI,EAAC,4BAA4B;UAACC,OAAO,eAAEpB,OAAA,CAACL,0BAA0B;YAAAa,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAE;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,eACpFX,OAAA,CAACjC,KAAK;UAACoD,IAAI,EAAC,0DAA0D;UAACC,OAAO,eAAEpB,OAAA,CAACL,0BAA0B;YAAAa,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAE;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,eAClHX,OAAA,CAACjC,KAAK;UAACoD,IAAI,EAAC,yBAAyB;UAACC,OAAO,eAAEpB,OAAA,CAACJ,gBAAgB;YAAAY,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAE;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,eACvEX,OAAA,CAACjC,KAAK;UAACoD,IAAI,EAAC,8CAA8C;UAACC,OAAO,eAAEpB,OAAA,CAACJ,gBAAgB;YAAAY,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAE;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAGtF;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACN,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CAAC;AAEV,CAAC;AAACT,EAAA,CA3EID,SAAS;EAAA,QACI9B,OAAO;AAAA;AAAAoD,EAAA,GADpBtB,SAAS;AA6Ef,eAAeA,SAAS;AAAC,IAAAsB,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}