{"ast": null, "code": "export function areArraysEqual(array1, array2, itemComparer = (a, b) => a === b) {\n  return array1.length === array2.length && array1.every((value, index) => itemComparer(value, array2[index]));\n}", "map": {"version": 3, "names": ["areArraysEqual", "array1", "array2", "itemComparer", "a", "b", "length", "every", "value", "index"], "sources": ["C:/CMS/webapp/frontend/node_modules/@mui/base/utils/areArraysEqual.js"], "sourcesContent": ["export function areArraysEqual(array1, array2, itemComparer = (a, b) => a === b) {\n  return array1.length === array2.length && array1.every((value, index) => itemComparer(value, array2[index]));\n}"], "mappings": "AAAA,OAAO,SAASA,cAAcA,CAACC,MAAM,EAAEC,MAAM,EAAEC,YAAY,GAAGA,CAACC,CAAC,EAAEC,CAAC,KAAKD,CAAC,KAAKC,CAAC,EAAE;EAC/E,OAAOJ,MAAM,CAACK,MAAM,KAAKJ,MAAM,CAACI,MAAM,IAAIL,MAAM,CAACM,KAAK,CAAC,CAACC,KAAK,EAAEC,KAAK,KAAKN,YAAY,CAACK,KAAK,EAAEN,MAAM,CAACO,KAAK,CAAC,CAAC,CAAC;AAC9G", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}