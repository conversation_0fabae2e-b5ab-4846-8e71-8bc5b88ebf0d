{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\PycharmProjects\\\\CMS\\\\webapp\\\\frontend\\\\src\\\\components\\\\charts\\\\BobineChart.js\";\nimport React from 'react';\nimport { <PERSON><PERSON><PERSON>, <PERSON>, <PERSON>Axis, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>ianG<PERSON>, <PERSON><PERSON><PERSON>, Legend, ResponsiveContainer, <PERSON><PERSON><PERSON>, Pie, Cell, <PERSON>atter<PERSON>hart, <PERSON>atter } from 'recharts';\nimport { Box, Typography, Grid, Paper, Chip } from '@mui/material';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst COLORS = {\n  DISPONIBILE: '#2e7d32',\n  IN_USO: '#ed6c02',\n  TERMINATA: '#d32f2f',\n  OVER: '#9c27b0'\n};\nconst STATUS_COLORS = {\n  primary: '#1976d2',\n  secondary: '#dc004e',\n  success: '#2e7d32',\n  warning: '#ed6c02',\n  info: '#0288d1',\n  error: '#d32f2f'\n};\nconst BobineChart = ({\n  data\n}) => {\n  if (!data || !data.bobine) return null;\n\n  // Raggruppa bobine per stato\n  const bobinePerStato = data.bobine.reduce((acc, bobina) => {\n    const stato = bobina.stato || 'SCONOSCIUTO';\n    if (!acc[stato]) {\n      acc[stato] = {\n        count: 0,\n        metri_totali: 0,\n        metri_residui: 0\n      };\n    }\n    acc[stato].count++;\n    acc[stato].metri_totali += bobina.metri_totali || 0;\n    acc[stato].metri_residui += bobina.metri_residui || 0;\n    return acc;\n  }, {});\n  const statoData = Object.entries(bobinePerStato).map(([stato, info]) => ({\n    stato,\n    count: info.count,\n    metri_totali: info.metri_totali,\n    metri_residui: info.metri_residui,\n    metri_utilizzati: info.metri_totali - info.metri_residui,\n    percentuale_utilizzo: info.metri_totali > 0 ? (info.metri_totali - info.metri_residui) / info.metri_totali * 100 : 0\n  }));\n\n  // Raggruppa bobine per tipologia\n  const bobinePerTipologia = data.bobine.reduce((acc, bobina) => {\n    const tipologia = bobina.tipologia || 'SCONOSCIUTA';\n    if (!acc[tipologia]) {\n      acc[tipologia] = {\n        count: 0,\n        metri_totali: 0,\n        metri_residui: 0\n      };\n    }\n    acc[tipologia].count++;\n    acc[tipologia].metri_totali += bobina.metri_totali || 0;\n    acc[tipologia].metri_residui += bobina.metri_residui || 0;\n    return acc;\n  }, {});\n  const tipologiaData = Object.entries(bobinePerTipologia).map(([tipologia, info]) => ({\n    tipologia: tipologia.length > 10 ? tipologia.substring(0, 10) + '...' : tipologia,\n    tipologia_full: tipologia,\n    count: info.count,\n    metri_totali: info.metri_totali,\n    metri_residui: info.metri_residui,\n    metri_utilizzati: info.metri_totali - info.metri_residui,\n    percentuale_utilizzo: info.metri_totali > 0 ? (info.metri_totali - info.metri_residui) / info.metri_totali * 100 : 0\n  }));\n\n  // Dati per scatter plot efficienza\n  const efficienzaData = data.bobine.map(bobina => ({\n    id: bobina.id_bobina,\n    metri_totali: bobina.metri_totali || 0,\n    percentuale_utilizzo: bobina.percentuale_utilizzo || 0,\n    stato: bobina.stato,\n    tipologia: bobina.tipologia\n  }));\n  const CustomTooltip = ({\n    active,\n    payload,\n    label\n  }) => {\n    if (active && payload && payload.length) {\n      return /*#__PURE__*/_jsxDEV(Paper, {\n        sx: {\n          p: 1,\n          border: '1px solid #ccc'\n        },\n        children: [/*#__PURE__*/_jsxDEV(Typography, {\n          variant: \"body2\",\n          children: `${label}`\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 94,\n          columnNumber: 11\n        }, this), payload.map((entry, index) => /*#__PURE__*/_jsxDEV(Typography, {\n          variant: \"body2\",\n          style: {\n            color: entry.color\n          },\n          children: `${entry.name}: ${entry.value}`\n        }, index, false, {\n          fileName: _jsxFileName,\n          lineNumber: 96,\n          columnNumber: 13\n        }, this))]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 93,\n        columnNumber: 9\n      }, this);\n    }\n    return null;\n  };\n  const ScatterTooltip = ({\n    active,\n    payload\n  }) => {\n    if (active && payload && payload.length) {\n      const data = payload[0].payload;\n      return /*#__PURE__*/_jsxDEV(Paper, {\n        sx: {\n          p: 1,\n          border: '1px solid #ccc'\n        },\n        children: [/*#__PURE__*/_jsxDEV(Typography, {\n          variant: \"body2\",\n          children: `ID: ${data.id}`\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 111,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Typography, {\n          variant: \"body2\",\n          children: `Tipologia: ${data.tipologia}`\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 112,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Typography, {\n          variant: \"body2\",\n          children: `Metri Totali: ${data.metri_totali}m`\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 113,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Typography, {\n          variant: \"body2\",\n          children: `Utilizzo: ${data.percentuale_utilizzo.toFixed(1)}%`\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 114,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Chip, {\n          label: data.stato,\n          size: \"small\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 115,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 110,\n        columnNumber: 9\n      }, this);\n    }\n    return null;\n  };\n  const renderCustomizedLabel = ({\n    cx,\n    cy,\n    midAngle,\n    innerRadius,\n    outerRadius,\n    percent\n  }) => {\n    if (percent < 0.05) return null;\n    const RADIAN = Math.PI / 180;\n    const radius = innerRadius + (outerRadius - innerRadius) * 0.5;\n    const x = cx + radius * Math.cos(-midAngle * RADIAN);\n    const y = cy + radius * Math.sin(-midAngle * RADIAN);\n    return /*#__PURE__*/_jsxDEV(\"text\", {\n      x: x,\n      y: y,\n      fill: \"white\",\n      textAnchor: x > cx ? 'start' : 'end',\n      dominantBaseline: \"central\",\n      fontSize: \"12\",\n      fontWeight: \"bold\",\n      children: `${(percent * 100).toFixed(0)}%`\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 131,\n      columnNumber: 7\n    }, this);\n  };\n  return /*#__PURE__*/_jsxDEV(Box, {\n    sx: {\n      mt: 3\n    },\n    children: [/*#__PURE__*/_jsxDEV(Typography, {\n      variant: \"h6\",\n      gutterBottom: true,\n      children: [\"Analisi Grafiche Bobine (\", data.totale_bobine, \" totali)\"]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 147,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Grid, {\n      container: true,\n      spacing: 3,\n      children: [/*#__PURE__*/_jsxDEV(Grid, {\n        item: true,\n        xs: 12,\n        md: 6,\n        children: /*#__PURE__*/_jsxDEV(Paper, {\n          sx: {\n            p: 2,\n            height: 175\n          },\n          children: [/*#__PURE__*/_jsxDEV(Typography, {\n            variant: \"subtitle1\",\n            gutterBottom: true,\n            align: \"center\",\n            children: \"Distribuzione per Stato\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 155,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(ResponsiveContainer, {\n            width: \"100%\",\n            height: 140,\n            children: /*#__PURE__*/_jsxDEV(PieChart, {\n              children: [/*#__PURE__*/_jsxDEV(Pie, {\n                data: statoData,\n                cx: \"50%\",\n                cy: \"50%\",\n                labelLine: false,\n                label: renderCustomizedLabel,\n                outerRadius: 80,\n                fill: \"#8884d8\",\n                dataKey: \"count\",\n                children: statoData.map((entry, index) => /*#__PURE__*/_jsxDEV(Cell, {\n                  fill: COLORS[entry.stato] || STATUS_COLORS.info\n                }, `cell-${index}`, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 171,\n                  columnNumber: 21\n                }, this))\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 160,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(Tooltip, {\n                content: /*#__PURE__*/_jsxDEV(CustomTooltip, {}, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 174,\n                  columnNumber: 35\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 174,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(Legend, {}, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 175,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 159,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 158,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 154,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 153,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Grid, {\n        item: true,\n        xs: 12,\n        md: 6,\n        children: /*#__PURE__*/_jsxDEV(Paper, {\n          sx: {\n            p: 2,\n            height: 175\n          },\n          children: [/*#__PURE__*/_jsxDEV(Typography, {\n            variant: \"subtitle1\",\n            gutterBottom: true,\n            align: \"center\",\n            children: \"Utilizzo per Tipologia\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 184,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(ResponsiveContainer, {\n            width: \"100%\",\n            height: 140,\n            children: /*#__PURE__*/_jsxDEV(BarChart, {\n              data: tipologiaData,\n              margin: {\n                top: 20,\n                right: 30,\n                left: 20,\n                bottom: 5\n              },\n              children: [/*#__PURE__*/_jsxDEV(CartesianGrid, {\n                strokeDasharray: \"3 3\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 189,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(XAxis, {\n                dataKey: \"tipologia\",\n                angle: -45,\n                textAnchor: \"end\",\n                height: 80\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 190,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(YAxis, {}, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 191,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(Tooltip, {\n                content: /*#__PURE__*/_jsxDEV(CustomTooltip, {}, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 192,\n                  columnNumber: 35\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 192,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(Legend, {}, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 193,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(Bar, {\n                dataKey: \"metri_totali\",\n                fill: STATUS_COLORS.primary,\n                name: \"Metri Totali\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 194,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(Bar, {\n                dataKey: \"metri_utilizzati\",\n                fill: STATUS_COLORS.success,\n                name: \"Metri Utilizzati\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 195,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(Bar, {\n                dataKey: \"metri_residui\",\n                fill: STATUS_COLORS.warning,\n                name: \"Metri Residui\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 196,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 188,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 187,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 183,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 182,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Grid, {\n        item: true,\n        xs: 12,\n        md: 6,\n        children: /*#__PURE__*/_jsxDEV(Paper, {\n          sx: {\n            p: 2,\n            height: 350\n          },\n          children: [/*#__PURE__*/_jsxDEV(Typography, {\n            variant: \"subtitle1\",\n            gutterBottom: true,\n            align: \"center\",\n            children: \"Numero Bobine per Stato\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 205,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(ResponsiveContainer, {\n            width: \"100%\",\n            height: 280,\n            children: /*#__PURE__*/_jsxDEV(BarChart, {\n              data: statoData,\n              margin: {\n                top: 20,\n                right: 30,\n                left: 20,\n                bottom: 5\n              },\n              children: [/*#__PURE__*/_jsxDEV(CartesianGrid, {\n                strokeDasharray: \"3 3\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 210,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(XAxis, {\n                dataKey: \"stato\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 211,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(YAxis, {}, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 212,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(Tooltip, {\n                content: /*#__PURE__*/_jsxDEV(CustomTooltip, {}, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 213,\n                  columnNumber: 35\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 213,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(Legend, {}, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 214,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(Bar, {\n                dataKey: \"count\",\n                fill: STATUS_COLORS.secondary,\n                name: \"Numero Bobine\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 215,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 209,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 208,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 204,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 203,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Grid, {\n        item: true,\n        xs: 12,\n        md: 6,\n        children: /*#__PURE__*/_jsxDEV(Paper, {\n          sx: {\n            p: 2,\n            height: 350\n          },\n          children: [/*#__PURE__*/_jsxDEV(Typography, {\n            variant: \"subtitle1\",\n            gutterBottom: true,\n            align: \"center\",\n            children: \"Efficienza Utilizzo (Metri vs Percentuale)\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 224,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(ResponsiveContainer, {\n            width: \"100%\",\n            height: 280,\n            children: /*#__PURE__*/_jsxDEV(ScatterChart, {\n              margin: {\n                top: 20,\n                right: 20,\n                bottom: 20,\n                left: 20\n              },\n              children: [/*#__PURE__*/_jsxDEV(CartesianGrid, {}, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 229,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(XAxis, {\n                type: \"number\",\n                dataKey: \"metri_totali\",\n                name: \"Metri Totali\",\n                label: {\n                  value: 'Metri Totali',\n                  position: 'insideBottom',\n                  offset: -10\n                }\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 230,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(YAxis, {\n                type: \"number\",\n                dataKey: \"percentuale_utilizzo\",\n                name: \"Utilizzo %\",\n                label: {\n                  value: 'Utilizzo %',\n                  angle: -90,\n                  position: 'insideLeft'\n                }\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 236,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(Tooltip, {\n                content: /*#__PURE__*/_jsxDEV(ScatterTooltip, {}, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 242,\n                  columnNumber: 35\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 242,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(Scatter, {\n                name: \"Bobine\",\n                data: efficienzaData,\n                fill: STATUS_COLORS.info\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 243,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 228,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 227,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 223,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 222,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Grid, {\n        item: true,\n        xs: 12,\n        children: /*#__PURE__*/_jsxDEV(Paper, {\n          sx: {\n            p: 2\n          },\n          children: [/*#__PURE__*/_jsxDEV(Typography, {\n            variant: \"subtitle1\",\n            gutterBottom: true,\n            children: \"Statistiche Riassuntive\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 256,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Grid, {\n            container: true,\n            spacing: 2,\n            children: statoData.map((stato, index) => /*#__PURE__*/_jsxDEV(Grid, {\n              item: true,\n              xs: 12,\n              sm: 6,\n              md: 3,\n              children: /*#__PURE__*/_jsxDEV(Box, {\n                sx: {\n                  textAlign: 'center',\n                  p: 1,\n                  border: '1px solid #e0e0e0',\n                  borderRadius: 1\n                },\n                children: [/*#__PURE__*/_jsxDEV(Chip, {\n                  label: stato.stato,\n                  color: stato.stato === 'DISPONIBILE' ? 'success' : stato.stato === 'TERMINATA' ? 'error' : 'warning',\n                  sx: {\n                    mb: 1\n                  }\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 263,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                  variant: \"h6\",\n                  children: stato.count\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 268,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                  variant: \"body2\",\n                  children: \"bobine\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 269,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                  variant: \"body2\",\n                  children: [stato.metri_residui.toFixed(0), \"m residui\"]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 270,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                  variant: \"body2\",\n                  children: [stato.percentuale_utilizzo.toFixed(1), \"% utilizzo\"]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 273,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 262,\n                columnNumber: 19\n              }, this)\n            }, index, false, {\n              fileName: _jsxFileName,\n              lineNumber: 261,\n              columnNumber: 17\n            }, this))\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 259,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 255,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 254,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 151,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 146,\n    columnNumber: 5\n  }, this);\n};\n_c = BobineChart;\nexport default BobineChart;\nvar _c;\n$RefreshReg$(_c, \"BobineChart\");", "map": {"version": 3, "names": ["React", "<PERSON><PERSON><PERSON>", "Bar", "XAxis", "YA<PERSON>s", "Cartesian<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "Legend", "ResponsiveContainer", "<PERSON><PERSON><PERSON>", "Pie", "Cell", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "Box", "Typography", "Grid", "Paper", "Chip", "jsxDEV", "_jsxDEV", "COLORS", "DISPONIBILE", "IN_USO", "TERMINATA", "OVER", "STATUS_COLORS", "primary", "secondary", "success", "warning", "info", "error", "<PERSON><PERSON><PERSON><PERSON>", "data", "bobine", "bobine<PERSON>er<PERSON><PERSON><PERSON>", "reduce", "acc", "bobina", "stato", "count", "metri_totali", "metri_residui", "statoData", "Object", "entries", "map", "<PERSON><PERSON>_util<PERSON><PERSON><PERSON>", "percentuale_utilizzo", "bobinePerTipologia", "tipologia", "tipologiaData", "length", "substring", "tipologia_full", "efficienzaData", "id", "id_bobina", "CustomTooltip", "active", "payload", "label", "sx", "p", "border", "children", "variant", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "entry", "index", "style", "color", "name", "value", "ScatterTooltip", "toFixed", "size", "renderCustomizedLabel", "cx", "cy", "midAngle", "innerRadius", "outerRadius", "percent", "RADIAN", "Math", "PI", "radius", "x", "cos", "y", "sin", "fill", "textAnchor", "dominantBaseline", "fontSize", "fontWeight", "mt", "gutterBottom", "totale_bobine", "container", "spacing", "item", "xs", "md", "height", "align", "width", "labelLine", "dataKey", "content", "margin", "top", "right", "left", "bottom", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "angle", "type", "position", "offset", "sm", "textAlign", "borderRadius", "mb", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/PycharmProjects/CMS/webapp/frontend/src/components/charts/BobineChart.js"], "sourcesContent": ["import React from 'react';\nimport {\n  <PERSON><PERSON><PERSON>,\n  <PERSON>,\n  <PERSON><PERSON><PERSON><PERSON>,\n  <PERSON><PERSON><PERSON><PERSON>,\n  <PERSON><PERSON>ianG<PERSON>,\n  <PERSON><PERSON><PERSON>,\n  <PERSON>,\n  ResponsiveC<PERSON><PERSON>,\n  <PERSON><PERSON><PERSON>,\n  <PERSON>,\n  <PERSON>,\n  <PERSON><PERSON><PERSON><PERSON><PERSON>,\n  <PERSON><PERSON><PERSON>\n} from 'recharts';\nimport { Box, Typography, Grid, Paper, Chip } from '@mui/material';\n\nconst COLORS = {\n  DISPONIBILE: '#2e7d32',\n  IN_USO: '#ed6c02',\n  TERMINATA: '#d32f2f',\n  OVER: '#9c27b0'\n};\n\nconst STATUS_COLORS = {\n  primary: '#1976d2',\n  secondary: '#dc004e',\n  success: '#2e7d32',\n  warning: '#ed6c02',\n  info: '#0288d1',\n  error: '#d32f2f'\n};\n\nconst BobineChart = ({ data }) => {\n  if (!data || !data.bobine) return null;\n\n  // Raggruppa bobine per stato\n  const bobinePerStato = data.bobine.reduce((acc, bobina) => {\n    const stato = bobina.stato || 'SCONOSCIUTO';\n    if (!acc[stato]) {\n      acc[stato] = { count: 0, metri_totali: 0, metri_residui: 0 };\n    }\n    acc[stato].count++;\n    acc[stato].metri_totali += bobina.metri_totali || 0;\n    acc[stato].metri_residui += bobina.metri_residui || 0;\n    return acc;\n  }, {});\n\n  const statoData = Object.entries(bobinePerStato).map(([stato, info]) => ({\n    stato,\n    count: info.count,\n    metri_totali: info.metri_totali,\n    metri_residui: info.metri_residui,\n    metri_utilizzati: info.metri_totali - info.metri_residui,\n    percentuale_utilizzo: info.metri_totali > 0 ? ((info.metri_totali - info.metri_residui) / info.metri_totali * 100) : 0\n  }));\n\n  // Raggruppa bobine per tipologia\n  const bobinePerTipologia = data.bobine.reduce((acc, bobina) => {\n    const tipologia = bobina.tipologia || 'SCONOSCIUTA';\n    if (!acc[tipologia]) {\n      acc[tipologia] = { count: 0, metri_totali: 0, metri_residui: 0 };\n    }\n    acc[tipologia].count++;\n    acc[tipologia].metri_totali += bobina.metri_totali || 0;\n    acc[tipologia].metri_residui += bobina.metri_residui || 0;\n    return acc;\n  }, {});\n\n  const tipologiaData = Object.entries(bobinePerTipologia).map(([tipologia, info]) => ({\n    tipologia: tipologia.length > 10 ? tipologia.substring(0, 10) + '...' : tipologia,\n    tipologia_full: tipologia,\n    count: info.count,\n    metri_totali: info.metri_totali,\n    metri_residui: info.metri_residui,\n    metri_utilizzati: info.metri_totali - info.metri_residui,\n    percentuale_utilizzo: info.metri_totali > 0 ? ((info.metri_totali - info.metri_residui) / info.metri_totali * 100) : 0\n  }));\n\n  // Dati per scatter plot efficienza\n  const efficienzaData = data.bobine.map(bobina => ({\n    id: bobina.id_bobina,\n    metri_totali: bobina.metri_totali || 0,\n    percentuale_utilizzo: bobina.percentuale_utilizzo || 0,\n    stato: bobina.stato,\n    tipologia: bobina.tipologia\n  }));\n\n  const CustomTooltip = ({ active, payload, label }) => {\n    if (active && payload && payload.length) {\n      return (\n        <Paper sx={{ p: 1, border: '1px solid #ccc' }}>\n          <Typography variant=\"body2\">{`${label}`}</Typography>\n          {payload.map((entry, index) => (\n            <Typography key={index} variant=\"body2\" style={{ color: entry.color }}>\n              {`${entry.name}: ${entry.value}`}\n            </Typography>\n          ))}\n        </Paper>\n      );\n    }\n    return null;\n  };\n\n  const ScatterTooltip = ({ active, payload }) => {\n    if (active && payload && payload.length) {\n      const data = payload[0].payload;\n      return (\n        <Paper sx={{ p: 1, border: '1px solid #ccc' }}>\n          <Typography variant=\"body2\">{`ID: ${data.id}`}</Typography>\n          <Typography variant=\"body2\">{`Tipologia: ${data.tipologia}`}</Typography>\n          <Typography variant=\"body2\">{`Metri Totali: ${data.metri_totali}m`}</Typography>\n          <Typography variant=\"body2\">{`Utilizzo: ${data.percentuale_utilizzo.toFixed(1)}%`}</Typography>\n          <Chip label={data.stato} size=\"small\" />\n        </Paper>\n      );\n    }\n    return null;\n  };\n\n  const renderCustomizedLabel = ({ cx, cy, midAngle, innerRadius, outerRadius, percent }) => {\n    if (percent < 0.05) return null;\n\n    const RADIAN = Math.PI / 180;\n    const radius = innerRadius + (outerRadius - innerRadius) * 0.5;\n    const x = cx + radius * Math.cos(-midAngle * RADIAN);\n    const y = cy + radius * Math.sin(-midAngle * RADIAN);\n\n    return (\n      <text \n        x={x} \n        y={y} \n        fill=\"white\" \n        textAnchor={x > cx ? 'start' : 'end'} \n        dominantBaseline=\"central\"\n        fontSize=\"12\"\n        fontWeight=\"bold\"\n      >\n        {`${(percent * 100).toFixed(0)}%`}\n      </text>\n    );\n  };\n\n  return (\n    <Box sx={{ mt: 3 }}>\n      <Typography variant=\"h6\" gutterBottom>\n        Analisi Grafiche Bobine ({data.totale_bobine} totali)\n      </Typography>\n\n      <Grid container spacing={3}>\n        {/* Grafico a torta - Bobine per Stato */}\n        <Grid item xs={12} md={6}>\n          <Paper sx={{ p: 2, height: 175 }}>\n            <Typography variant=\"subtitle1\" gutterBottom align=\"center\">\n              Distribuzione per Stato\n            </Typography>\n            <ResponsiveContainer width=\"100%\" height={140}>\n              <PieChart>\n                <Pie\n                  data={statoData}\n                  cx=\"50%\"\n                  cy=\"50%\"\n                  labelLine={false}\n                  label={renderCustomizedLabel}\n                  outerRadius={80}\n                  fill=\"#8884d8\"\n                  dataKey=\"count\"\n                >\n                  {statoData.map((entry, index) => (\n                    <Cell key={`cell-${index}`} fill={COLORS[entry.stato] || STATUS_COLORS.info} />\n                  ))}\n                </Pie>\n                <Tooltip content={<CustomTooltip />} />\n                <Legend />\n              </PieChart>\n            </ResponsiveContainer>\n          </Paper>\n        </Grid>\n\n        {/* Grafico a barre - Utilizzo per Tipologia */}\n        <Grid item xs={12} md={6}>\n          <Paper sx={{ p: 2, height: 175 }}>\n            <Typography variant=\"subtitle1\" gutterBottom align=\"center\">\n              Utilizzo per Tipologia\n            </Typography>\n            <ResponsiveContainer width=\"100%\" height={140}>\n              <BarChart data={tipologiaData} margin={{ top: 20, right: 30, left: 20, bottom: 5 }}>\n                <CartesianGrid strokeDasharray=\"3 3\" />\n                <XAxis dataKey=\"tipologia\" angle={-45} textAnchor=\"end\" height={80} />\n                <YAxis />\n                <Tooltip content={<CustomTooltip />} />\n                <Legend />\n                <Bar dataKey=\"metri_totali\" fill={STATUS_COLORS.primary} name=\"Metri Totali\" />\n                <Bar dataKey=\"metri_utilizzati\" fill={STATUS_COLORS.success} name=\"Metri Utilizzati\" />\n                <Bar dataKey=\"metri_residui\" fill={STATUS_COLORS.warning} name=\"Metri Residui\" />\n              </BarChart>\n            </ResponsiveContainer>\n          </Paper>\n        </Grid>\n\n        {/* Grafico a barre - Conteggio per Stato */}\n        <Grid item xs={12} md={6}>\n          <Paper sx={{ p: 2, height: 350 }}>\n            <Typography variant=\"subtitle1\" gutterBottom align=\"center\">\n              Numero Bobine per Stato\n            </Typography>\n            <ResponsiveContainer width=\"100%\" height={280}>\n              <BarChart data={statoData} margin={{ top: 20, right: 30, left: 20, bottom: 5 }}>\n                <CartesianGrid strokeDasharray=\"3 3\" />\n                <XAxis dataKey=\"stato\" />\n                <YAxis />\n                <Tooltip content={<CustomTooltip />} />\n                <Legend />\n                <Bar dataKey=\"count\" fill={STATUS_COLORS.secondary} name=\"Numero Bobine\" />\n              </BarChart>\n            </ResponsiveContainer>\n          </Paper>\n        </Grid>\n\n        {/* Scatter Plot - Efficienza Bobine */}\n        <Grid item xs={12} md={6}>\n          <Paper sx={{ p: 2, height: 350 }}>\n            <Typography variant=\"subtitle1\" gutterBottom align=\"center\">\n              Efficienza Utilizzo (Metri vs Percentuale)\n            </Typography>\n            <ResponsiveContainer width=\"100%\" height={280}>\n              <ScatterChart margin={{ top: 20, right: 20, bottom: 20, left: 20 }}>\n                <CartesianGrid />\n                <XAxis \n                  type=\"number\" \n                  dataKey=\"metri_totali\" \n                  name=\"Metri Totali\"\n                  label={{ value: 'Metri Totali', position: 'insideBottom', offset: -10 }}\n                />\n                <YAxis \n                  type=\"number\" \n                  dataKey=\"percentuale_utilizzo\" \n                  name=\"Utilizzo %\"\n                  label={{ value: 'Utilizzo %', angle: -90, position: 'insideLeft' }}\n                />\n                <Tooltip content={<ScatterTooltip />} />\n                <Scatter \n                  name=\"Bobine\" \n                  data={efficienzaData} \n                  fill={STATUS_COLORS.info}\n                />\n              </ScatterChart>\n            </ResponsiveContainer>\n          </Paper>\n        </Grid>\n\n        {/* Statistiche Riassuntive */}\n        <Grid item xs={12}>\n          <Paper sx={{ p: 2 }}>\n            <Typography variant=\"subtitle1\" gutterBottom>\n              Statistiche Riassuntive\n            </Typography>\n            <Grid container spacing={2}>\n              {statoData.map((stato, index) => (\n                <Grid item xs={12} sm={6} md={3} key={index}>\n                  <Box sx={{ textAlign: 'center', p: 1, border: '1px solid #e0e0e0', borderRadius: 1 }}>\n                    <Chip \n                      label={stato.stato} \n                      color={stato.stato === 'DISPONIBILE' ? 'success' : stato.stato === 'TERMINATA' ? 'error' : 'warning'}\n                      sx={{ mb: 1 }}\n                    />\n                    <Typography variant=\"h6\">{stato.count}</Typography>\n                    <Typography variant=\"body2\">bobine</Typography>\n                    <Typography variant=\"body2\">\n                      {stato.metri_residui.toFixed(0)}m residui\n                    </Typography>\n                    <Typography variant=\"body2\">\n                      {stato.percentuale_utilizzo.toFixed(1)}% utilizzo\n                    </Typography>\n                  </Box>\n                </Grid>\n              ))}\n            </Grid>\n          </Paper>\n        </Grid>\n      </Grid>\n    </Box>\n  );\n};\n\nexport default BobineChart;\n"], "mappings": ";AAAA,OAAOA,KAAK,MAAM,OAAO;AACzB,SACEC,QAAQ,EACRC,GAAG,EACHC,KAAK,EACLC,KAAK,EACLC,aAAa,EACbC,OAAO,EACPC,MAAM,EACNC,mBAAmB,EACnBC,QAAQ,EACRC,GAAG,EACHC,IAAI,EACJC,YAAY,EACZC,OAAO,QACF,UAAU;AACjB,SAASC,GAAG,EAAEC,UAAU,EAAEC,IAAI,EAAEC,KAAK,EAAEC,IAAI,QAAQ,eAAe;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAEnE,MAAMC,MAAM,GAAG;EACbC,WAAW,EAAE,SAAS;EACtBC,MAAM,EAAE,SAAS;EACjBC,SAAS,EAAE,SAAS;EACpBC,IAAI,EAAE;AACR,CAAC;AAED,MAAMC,aAAa,GAAG;EACpBC,OAAO,EAAE,SAAS;EAClBC,SAAS,EAAE,SAAS;EACpBC,OAAO,EAAE,SAAS;EAClBC,OAAO,EAAE,SAAS;EAClBC,IAAI,EAAE,SAAS;EACfC,KAAK,EAAE;AACT,CAAC;AAED,MAAMC,WAAW,GAAGA,CAAC;EAAEC;AAAK,CAAC,KAAK;EAChC,IAAI,CAACA,IAAI,IAAI,CAACA,IAAI,CAACC,MAAM,EAAE,OAAO,IAAI;;EAEtC;EACA,MAAMC,cAAc,GAAGF,IAAI,CAACC,MAAM,CAACE,MAAM,CAAC,CAACC,GAAG,EAAEC,MAAM,KAAK;IACzD,MAAMC,KAAK,GAAGD,MAAM,CAACC,KAAK,IAAI,aAAa;IAC3C,IAAI,CAACF,GAAG,CAACE,KAAK,CAAC,EAAE;MACfF,GAAG,CAACE,KAAK,CAAC,GAAG;QAAEC,KAAK,EAAE,CAAC;QAAEC,YAAY,EAAE,CAAC;QAAEC,aAAa,EAAE;MAAE,CAAC;IAC9D;IACAL,GAAG,CAACE,KAAK,CAAC,CAACC,KAAK,EAAE;IAClBH,GAAG,CAACE,KAAK,CAAC,CAACE,YAAY,IAAIH,MAAM,CAACG,YAAY,IAAI,CAAC;IACnDJ,GAAG,CAACE,KAAK,CAAC,CAACG,aAAa,IAAIJ,MAAM,CAACI,aAAa,IAAI,CAAC;IACrD,OAAOL,GAAG;EACZ,CAAC,EAAE,CAAC,CAAC,CAAC;EAEN,MAAMM,SAAS,GAAGC,MAAM,CAACC,OAAO,CAACV,cAAc,CAAC,CAACW,GAAG,CAAC,CAAC,CAACP,KAAK,EAAET,IAAI,CAAC,MAAM;IACvES,KAAK;IACLC,KAAK,EAAEV,IAAI,CAACU,KAAK;IACjBC,YAAY,EAAEX,IAAI,CAACW,YAAY;IAC/BC,aAAa,EAAEZ,IAAI,CAACY,aAAa;IACjCK,gBAAgB,EAAEjB,IAAI,CAACW,YAAY,GAAGX,IAAI,CAACY,aAAa;IACxDM,oBAAoB,EAAElB,IAAI,CAACW,YAAY,GAAG,CAAC,GAAI,CAACX,IAAI,CAACW,YAAY,GAAGX,IAAI,CAACY,aAAa,IAAIZ,IAAI,CAACW,YAAY,GAAG,GAAG,GAAI;EACvH,CAAC,CAAC,CAAC;;EAEH;EACA,MAAMQ,kBAAkB,GAAGhB,IAAI,CAACC,MAAM,CAACE,MAAM,CAAC,CAACC,GAAG,EAAEC,MAAM,KAAK;IAC7D,MAAMY,SAAS,GAAGZ,MAAM,CAACY,SAAS,IAAI,aAAa;IACnD,IAAI,CAACb,GAAG,CAACa,SAAS,CAAC,EAAE;MACnBb,GAAG,CAACa,SAAS,CAAC,GAAG;QAAEV,KAAK,EAAE,CAAC;QAAEC,YAAY,EAAE,CAAC;QAAEC,aAAa,EAAE;MAAE,CAAC;IAClE;IACAL,GAAG,CAACa,SAAS,CAAC,CAACV,KAAK,EAAE;IACtBH,GAAG,CAACa,SAAS,CAAC,CAACT,YAAY,IAAIH,MAAM,CAACG,YAAY,IAAI,CAAC;IACvDJ,GAAG,CAACa,SAAS,CAAC,CAACR,aAAa,IAAIJ,MAAM,CAACI,aAAa,IAAI,CAAC;IACzD,OAAOL,GAAG;EACZ,CAAC,EAAE,CAAC,CAAC,CAAC;EAEN,MAAMc,aAAa,GAAGP,MAAM,CAACC,OAAO,CAACI,kBAAkB,CAAC,CAACH,GAAG,CAAC,CAAC,CAACI,SAAS,EAAEpB,IAAI,CAAC,MAAM;IACnFoB,SAAS,EAAEA,SAAS,CAACE,MAAM,GAAG,EAAE,GAAGF,SAAS,CAACG,SAAS,CAAC,CAAC,EAAE,EAAE,CAAC,GAAG,KAAK,GAAGH,SAAS;IACjFI,cAAc,EAAEJ,SAAS;IACzBV,KAAK,EAAEV,IAAI,CAACU,KAAK;IACjBC,YAAY,EAAEX,IAAI,CAACW,YAAY;IAC/BC,aAAa,EAAEZ,IAAI,CAACY,aAAa;IACjCK,gBAAgB,EAAEjB,IAAI,CAACW,YAAY,GAAGX,IAAI,CAACY,aAAa;IACxDM,oBAAoB,EAAElB,IAAI,CAACW,YAAY,GAAG,CAAC,GAAI,CAACX,IAAI,CAACW,YAAY,GAAGX,IAAI,CAACY,aAAa,IAAIZ,IAAI,CAACW,YAAY,GAAG,GAAG,GAAI;EACvH,CAAC,CAAC,CAAC;;EAEH;EACA,MAAMc,cAAc,GAAGtB,IAAI,CAACC,MAAM,CAACY,GAAG,CAACR,MAAM,KAAK;IAChDkB,EAAE,EAAElB,MAAM,CAACmB,SAAS;IACpBhB,YAAY,EAAEH,MAAM,CAACG,YAAY,IAAI,CAAC;IACtCO,oBAAoB,EAAEV,MAAM,CAACU,oBAAoB,IAAI,CAAC;IACtDT,KAAK,EAAED,MAAM,CAACC,KAAK;IACnBW,SAAS,EAAEZ,MAAM,CAACY;EACpB,CAAC,CAAC,CAAC;EAEH,MAAMQ,aAAa,GAAGA,CAAC;IAAEC,MAAM;IAAEC,OAAO;IAAEC;EAAM,CAAC,KAAK;IACpD,IAAIF,MAAM,IAAIC,OAAO,IAAIA,OAAO,CAACR,MAAM,EAAE;MACvC,oBACEjC,OAAA,CAACH,KAAK;QAAC8C,EAAE,EAAE;UAAEC,CAAC,EAAE,CAAC;UAAEC,MAAM,EAAE;QAAiB,CAAE;QAAAC,QAAA,gBAC5C9C,OAAA,CAACL,UAAU;UAACoD,OAAO,EAAC,OAAO;UAAAD,QAAA,EAAE,GAAGJ,KAAK;QAAE;UAAAM,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAa,CAAC,EACpDV,OAAO,CAACd,GAAG,CAAC,CAACyB,KAAK,EAAEC,KAAK,kBACxBrD,OAAA,CAACL,UAAU;UAAaoD,OAAO,EAAC,OAAO;UAACO,KAAK,EAAE;YAAEC,KAAK,EAAEH,KAAK,CAACG;UAAM,CAAE;UAAAT,QAAA,EACnE,GAAGM,KAAK,CAACI,IAAI,KAAKJ,KAAK,CAACK,KAAK;QAAE,GADjBJ,KAAK;UAAAL,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAEV,CACb,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACG,CAAC;IAEZ;IACA,OAAO,IAAI;EACb,CAAC;EAED,MAAMO,cAAc,GAAGA,CAAC;IAAElB,MAAM;IAAEC;EAAQ,CAAC,KAAK;IAC9C,IAAID,MAAM,IAAIC,OAAO,IAAIA,OAAO,CAACR,MAAM,EAAE;MACvC,MAAMnB,IAAI,GAAG2B,OAAO,CAAC,CAAC,CAAC,CAACA,OAAO;MAC/B,oBACEzC,OAAA,CAACH,KAAK;QAAC8C,EAAE,EAAE;UAAEC,CAAC,EAAE,CAAC;UAAEC,MAAM,EAAE;QAAiB,CAAE;QAAAC,QAAA,gBAC5C9C,OAAA,CAACL,UAAU;UAACoD,OAAO,EAAC,OAAO;UAAAD,QAAA,EAAE,OAAOhC,IAAI,CAACuB,EAAE;QAAE;UAAAW,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAa,CAAC,eAC3DnD,OAAA,CAACL,UAAU;UAACoD,OAAO,EAAC,OAAO;UAAAD,QAAA,EAAE,cAAchC,IAAI,CAACiB,SAAS;QAAE;UAAAiB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAa,CAAC,eACzEnD,OAAA,CAACL,UAAU;UAACoD,OAAO,EAAC,OAAO;UAAAD,QAAA,EAAE,iBAAiBhC,IAAI,CAACQ,YAAY;QAAG;UAAA0B,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAa,CAAC,eAChFnD,OAAA,CAACL,UAAU;UAACoD,OAAO,EAAC,OAAO;UAAAD,QAAA,EAAE,aAAahC,IAAI,CAACe,oBAAoB,CAAC8B,OAAO,CAAC,CAAC,CAAC;QAAG;UAAAX,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAa,CAAC,eAC/FnD,OAAA,CAACF,IAAI;UAAC4C,KAAK,EAAE5B,IAAI,CAACM,KAAM;UAACwC,IAAI,EAAC;QAAO;UAAAZ,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACnC,CAAC;IAEZ;IACA,OAAO,IAAI;EACb,CAAC;EAED,MAAMU,qBAAqB,GAAGA,CAAC;IAAEC,EAAE;IAAEC,EAAE;IAAEC,QAAQ;IAAEC,WAAW;IAAEC,WAAW;IAAEC;EAAQ,CAAC,KAAK;IACzF,IAAIA,OAAO,GAAG,IAAI,EAAE,OAAO,IAAI;IAE/B,MAAMC,MAAM,GAAGC,IAAI,CAACC,EAAE,GAAG,GAAG;IAC5B,MAAMC,MAAM,GAAGN,WAAW,GAAG,CAACC,WAAW,GAAGD,WAAW,IAAI,GAAG;IAC9D,MAAMO,CAAC,GAAGV,EAAE,GAAGS,MAAM,GAAGF,IAAI,CAACI,GAAG,CAAC,CAACT,QAAQ,GAAGI,MAAM,CAAC;IACpD,MAAMM,CAAC,GAAGX,EAAE,GAAGQ,MAAM,GAAGF,IAAI,CAACM,GAAG,CAAC,CAACX,QAAQ,GAAGI,MAAM,CAAC;IAEpD,oBACEpE,OAAA;MACEwE,CAAC,EAAEA,CAAE;MACLE,CAAC,EAAEA,CAAE;MACLE,IAAI,EAAC,OAAO;MACZC,UAAU,EAAEL,CAAC,GAAGV,EAAE,GAAG,OAAO,GAAG,KAAM;MACrCgB,gBAAgB,EAAC,SAAS;MAC1BC,QAAQ,EAAC,IAAI;MACbC,UAAU,EAAC,MAAM;MAAAlC,QAAA,EAEhB,GAAG,CAACqB,OAAO,GAAG,GAAG,EAAER,OAAO,CAAC,CAAC,CAAC;IAAG;MAAAX,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAC7B,CAAC;EAEX,CAAC;EAED,oBACEnD,OAAA,CAACN,GAAG;IAACiD,EAAE,EAAE;MAAEsC,EAAE,EAAE;IAAE,CAAE;IAAAnC,QAAA,gBACjB9C,OAAA,CAACL,UAAU;MAACoD,OAAO,EAAC,IAAI;MAACmC,YAAY;MAAApC,QAAA,GAAC,2BACX,EAAChC,IAAI,CAACqE,aAAa,EAAC,UAC/C;IAAA;MAAAnC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAY,CAAC,eAEbnD,OAAA,CAACJ,IAAI;MAACwF,SAAS;MAACC,OAAO,EAAE,CAAE;MAAAvC,QAAA,gBAEzB9C,OAAA,CAACJ,IAAI;QAAC0F,IAAI;QAACC,EAAE,EAAE,EAAG;QAACC,EAAE,EAAE,CAAE;QAAA1C,QAAA,eACvB9C,OAAA,CAACH,KAAK;UAAC8C,EAAE,EAAE;YAAEC,CAAC,EAAE,CAAC;YAAE6C,MAAM,EAAE;UAAI,CAAE;UAAA3C,QAAA,gBAC/B9C,OAAA,CAACL,UAAU;YAACoD,OAAO,EAAC,WAAW;YAACmC,YAAY;YAACQ,KAAK,EAAC,QAAQ;YAAA5C,QAAA,EAAC;UAE5D;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAY,CAAC,eACbnD,OAAA,CAACZ,mBAAmB;YAACuG,KAAK,EAAC,MAAM;YAACF,MAAM,EAAE,GAAI;YAAA3C,QAAA,eAC5C9C,OAAA,CAACX,QAAQ;cAAAyD,QAAA,gBACP9C,OAAA,CAACV,GAAG;gBACFwB,IAAI,EAAEU,SAAU;gBAChBsC,EAAE,EAAC,KAAK;gBACRC,EAAE,EAAC,KAAK;gBACR6B,SAAS,EAAE,KAAM;gBACjBlD,KAAK,EAAEmB,qBAAsB;gBAC7BK,WAAW,EAAE,EAAG;gBAChBU,IAAI,EAAC,SAAS;gBACdiB,OAAO,EAAC,OAAO;gBAAA/C,QAAA,EAEdtB,SAAS,CAACG,GAAG,CAAC,CAACyB,KAAK,EAAEC,KAAK,kBAC1BrD,OAAA,CAACT,IAAI;kBAAuBqF,IAAI,EAAE3E,MAAM,CAACmD,KAAK,CAAChC,KAAK,CAAC,IAAId,aAAa,CAACK;gBAAK,GAAjE,QAAQ0C,KAAK,EAAE;kBAAAL,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAoD,CAC/E;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACC,CAAC,eACNnD,OAAA,CAACd,OAAO;gBAAC4G,OAAO,eAAE9F,OAAA,CAACuC,aAAa;kBAAAS,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE;cAAE;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,eACvCnD,OAAA,CAACb,MAAM;gBAAA6D,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACF;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACQ,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACjB;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACJ,CAAC,eAGPnD,OAAA,CAACJ,IAAI;QAAC0F,IAAI;QAACC,EAAE,EAAE,EAAG;QAACC,EAAE,EAAE,CAAE;QAAA1C,QAAA,eACvB9C,OAAA,CAACH,KAAK;UAAC8C,EAAE,EAAE;YAAEC,CAAC,EAAE,CAAC;YAAE6C,MAAM,EAAE;UAAI,CAAE;UAAA3C,QAAA,gBAC/B9C,OAAA,CAACL,UAAU;YAACoD,OAAO,EAAC,WAAW;YAACmC,YAAY;YAACQ,KAAK,EAAC,QAAQ;YAAA5C,QAAA,EAAC;UAE5D;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAY,CAAC,eACbnD,OAAA,CAACZ,mBAAmB;YAACuG,KAAK,EAAC,MAAM;YAACF,MAAM,EAAE,GAAI;YAAA3C,QAAA,eAC5C9C,OAAA,CAACnB,QAAQ;cAACiC,IAAI,EAAEkB,aAAc;cAAC+D,MAAM,EAAE;gBAAEC,GAAG,EAAE,EAAE;gBAAEC,KAAK,EAAE,EAAE;gBAAEC,IAAI,EAAE,EAAE;gBAAEC,MAAM,EAAE;cAAE,CAAE;cAAArD,QAAA,gBACjF9C,OAAA,CAACf,aAAa;gBAACmH,eAAe,EAAC;cAAK;gBAAApD,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,eACvCnD,OAAA,CAACjB,KAAK;gBAAC8G,OAAO,EAAC,WAAW;gBAACQ,KAAK,EAAE,CAAC,EAAG;gBAACxB,UAAU,EAAC,KAAK;gBAACY,MAAM,EAAE;cAAG;gBAAAzC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,eACtEnD,OAAA,CAAChB,KAAK;gBAAAgE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,eACTnD,OAAA,CAACd,OAAO;gBAAC4G,OAAO,eAAE9F,OAAA,CAACuC,aAAa;kBAAAS,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE;cAAE;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,eACvCnD,OAAA,CAACb,MAAM;gBAAA6D,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,eACVnD,OAAA,CAAClB,GAAG;gBAAC+G,OAAO,EAAC,cAAc;gBAACjB,IAAI,EAAEtE,aAAa,CAACC,OAAQ;gBAACiD,IAAI,EAAC;cAAc;gBAAAR,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,eAC/EnD,OAAA,CAAClB,GAAG;gBAAC+G,OAAO,EAAC,kBAAkB;gBAACjB,IAAI,EAAEtE,aAAa,CAACG,OAAQ;gBAAC+C,IAAI,EAAC;cAAkB;gBAAAR,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,eACvFnD,OAAA,CAAClB,GAAG;gBAAC+G,OAAO,EAAC,eAAe;gBAACjB,IAAI,EAAEtE,aAAa,CAACI,OAAQ;gBAAC8C,IAAI,EAAC;cAAe;gBAAAR,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACzE;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACQ,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACjB;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACJ,CAAC,eAGPnD,OAAA,CAACJ,IAAI;QAAC0F,IAAI;QAACC,EAAE,EAAE,EAAG;QAACC,EAAE,EAAE,CAAE;QAAA1C,QAAA,eACvB9C,OAAA,CAACH,KAAK;UAAC8C,EAAE,EAAE;YAAEC,CAAC,EAAE,CAAC;YAAE6C,MAAM,EAAE;UAAI,CAAE;UAAA3C,QAAA,gBAC/B9C,OAAA,CAACL,UAAU;YAACoD,OAAO,EAAC,WAAW;YAACmC,YAAY;YAACQ,KAAK,EAAC,QAAQ;YAAA5C,QAAA,EAAC;UAE5D;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAY,CAAC,eACbnD,OAAA,CAACZ,mBAAmB;YAACuG,KAAK,EAAC,MAAM;YAACF,MAAM,EAAE,GAAI;YAAA3C,QAAA,eAC5C9C,OAAA,CAACnB,QAAQ;cAACiC,IAAI,EAAEU,SAAU;cAACuE,MAAM,EAAE;gBAAEC,GAAG,EAAE,EAAE;gBAAEC,KAAK,EAAE,EAAE;gBAAEC,IAAI,EAAE,EAAE;gBAAEC,MAAM,EAAE;cAAE,CAAE;cAAArD,QAAA,gBAC7E9C,OAAA,CAACf,aAAa;gBAACmH,eAAe,EAAC;cAAK;gBAAApD,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,eACvCnD,OAAA,CAACjB,KAAK;gBAAC8G,OAAO,EAAC;cAAO;gBAAA7C,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,eACzBnD,OAAA,CAAChB,KAAK;gBAAAgE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,eACTnD,OAAA,CAACd,OAAO;gBAAC4G,OAAO,eAAE9F,OAAA,CAACuC,aAAa;kBAAAS,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE;cAAE;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,eACvCnD,OAAA,CAACb,MAAM;gBAAA6D,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,eACVnD,OAAA,CAAClB,GAAG;gBAAC+G,OAAO,EAAC,OAAO;gBAACjB,IAAI,EAAEtE,aAAa,CAACE,SAAU;gBAACgD,IAAI,EAAC;cAAe;gBAAAR,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACnE;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACQ,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACjB;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACJ,CAAC,eAGPnD,OAAA,CAACJ,IAAI;QAAC0F,IAAI;QAACC,EAAE,EAAE,EAAG;QAACC,EAAE,EAAE,CAAE;QAAA1C,QAAA,eACvB9C,OAAA,CAACH,KAAK;UAAC8C,EAAE,EAAE;YAAEC,CAAC,EAAE,CAAC;YAAE6C,MAAM,EAAE;UAAI,CAAE;UAAA3C,QAAA,gBAC/B9C,OAAA,CAACL,UAAU;YAACoD,OAAO,EAAC,WAAW;YAACmC,YAAY;YAACQ,KAAK,EAAC,QAAQ;YAAA5C,QAAA,EAAC;UAE5D;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAY,CAAC,eACbnD,OAAA,CAACZ,mBAAmB;YAACuG,KAAK,EAAC,MAAM;YAACF,MAAM,EAAE,GAAI;YAAA3C,QAAA,eAC5C9C,OAAA,CAACR,YAAY;cAACuG,MAAM,EAAE;gBAAEC,GAAG,EAAE,EAAE;gBAAEC,KAAK,EAAE,EAAE;gBAAEE,MAAM,EAAE,EAAE;gBAAED,IAAI,EAAE;cAAG,CAAE;cAAApD,QAAA,gBACjE9C,OAAA,CAACf,aAAa;gBAAA+D,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,eACjBnD,OAAA,CAACjB,KAAK;gBACJuH,IAAI,EAAC,QAAQ;gBACbT,OAAO,EAAC,cAAc;gBACtBrC,IAAI,EAAC,cAAc;gBACnBd,KAAK,EAAE;kBAAEe,KAAK,EAAE,cAAc;kBAAE8C,QAAQ,EAAE,cAAc;kBAAEC,MAAM,EAAE,CAAC;gBAAG;cAAE;gBAAAxD,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACzE,CAAC,eACFnD,OAAA,CAAChB,KAAK;gBACJsH,IAAI,EAAC,QAAQ;gBACbT,OAAO,EAAC,sBAAsB;gBAC9BrC,IAAI,EAAC,YAAY;gBACjBd,KAAK,EAAE;kBAAEe,KAAK,EAAE,YAAY;kBAAE4C,KAAK,EAAE,CAAC,EAAE;kBAAEE,QAAQ,EAAE;gBAAa;cAAE;gBAAAvD,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACpE,CAAC,eACFnD,OAAA,CAACd,OAAO;gBAAC4G,OAAO,eAAE9F,OAAA,CAAC0D,cAAc;kBAAAV,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE;cAAE;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,eACxCnD,OAAA,CAACP,OAAO;gBACN+D,IAAI,EAAC,QAAQ;gBACb1C,IAAI,EAAEsB,cAAe;gBACrBwC,IAAI,EAAEtE,aAAa,CAACK;cAAK;gBAAAqC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC1B,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACU;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACI,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACjB;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACJ,CAAC,eAGPnD,OAAA,CAACJ,IAAI;QAAC0F,IAAI;QAACC,EAAE,EAAE,EAAG;QAAAzC,QAAA,eAChB9C,OAAA,CAACH,KAAK;UAAC8C,EAAE,EAAE;YAAEC,CAAC,EAAE;UAAE,CAAE;UAAAE,QAAA,gBAClB9C,OAAA,CAACL,UAAU;YAACoD,OAAO,EAAC,WAAW;YAACmC,YAAY;YAAApC,QAAA,EAAC;UAE7C;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAY,CAAC,eACbnD,OAAA,CAACJ,IAAI;YAACwF,SAAS;YAACC,OAAO,EAAE,CAAE;YAAAvC,QAAA,EACxBtB,SAAS,CAACG,GAAG,CAAC,CAACP,KAAK,EAAEiC,KAAK,kBAC1BrD,OAAA,CAACJ,IAAI;cAAC0F,IAAI;cAACC,EAAE,EAAE,EAAG;cAACkB,EAAE,EAAE,CAAE;cAACjB,EAAE,EAAE,CAAE;cAAA1C,QAAA,eAC9B9C,OAAA,CAACN,GAAG;gBAACiD,EAAE,EAAE;kBAAE+D,SAAS,EAAE,QAAQ;kBAAE9D,CAAC,EAAE,CAAC;kBAAEC,MAAM,EAAE,mBAAmB;kBAAE8D,YAAY,EAAE;gBAAE,CAAE;gBAAA7D,QAAA,gBACnF9C,OAAA,CAACF,IAAI;kBACH4C,KAAK,EAAEtB,KAAK,CAACA,KAAM;kBACnBmC,KAAK,EAAEnC,KAAK,CAACA,KAAK,KAAK,aAAa,GAAG,SAAS,GAAGA,KAAK,CAACA,KAAK,KAAK,WAAW,GAAG,OAAO,GAAG,SAAU;kBACrGuB,EAAE,EAAE;oBAAEiE,EAAE,EAAE;kBAAE;gBAAE;kBAAA5D,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACf,CAAC,eACFnD,OAAA,CAACL,UAAU;kBAACoD,OAAO,EAAC,IAAI;kBAAAD,QAAA,EAAE1B,KAAK,CAACC;gBAAK;kBAAA2B,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAa,CAAC,eACnDnD,OAAA,CAACL,UAAU;kBAACoD,OAAO,EAAC,OAAO;kBAAAD,QAAA,EAAC;gBAAM;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAY,CAAC,eAC/CnD,OAAA,CAACL,UAAU;kBAACoD,OAAO,EAAC,OAAO;kBAAAD,QAAA,GACxB1B,KAAK,CAACG,aAAa,CAACoC,OAAO,CAAC,CAAC,CAAC,EAAC,WAClC;gBAAA;kBAAAX,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAY,CAAC,eACbnD,OAAA,CAACL,UAAU;kBAACoD,OAAO,EAAC,OAAO;kBAAAD,QAAA,GACxB1B,KAAK,CAACS,oBAAoB,CAAC8B,OAAO,CAAC,CAAC,CAAC,EAAC,YACzC;gBAAA;kBAAAX,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAY,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACV;YAAC,GAf8BE,KAAK;cAAAL,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAgBrC,CACP;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACE,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACF;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACJ,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACJ,CAAC;AAEV,CAAC;AAAC0D,EAAA,GA1PIhG,WAAW;AA4PjB,eAAeA,WAAW;AAAC,IAAAgG,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}