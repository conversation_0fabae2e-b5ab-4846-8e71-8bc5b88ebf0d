{"ast": null, "code": "import _extends from \"@babel/runtime/helpers/esm/extends\";\nimport _objectWithoutPropertiesLoose from \"@babel/runtime/helpers/esm/objectWithoutPropertiesLoose\";\nconst _excluded = [\"props\", \"steps\"],\n  _excluded2 = [\"ownerState\"];\nimport * as React from 'react';\nimport useSlotProps from '@mui/utils/useSlotProps';\nimport { PickerPopper } from \"../../components/PickerPopper/PickerPopper.js\";\nimport { usePicker } from \"../usePicker/index.js\";\nimport { PickersLayout } from \"../../../PickersLayout/index.js\";\nimport { PickerProvider } from \"../../components/PickerProvider.js\";\nimport { PickerFieldUIContextProvider } from \"../../components/PickerFieldUI.js\";\nimport { createNonRangePickerStepNavigation } from \"../../utils/createNonRangePickerStepNavigation.js\";\n\n/**\n * Hook managing all the single-date desktop pickers:\n * - DesktopDatePicker\n * - DesktopDateTimePicker\n * - DesktopTimePicker\n */\nimport { jsx as _jsx, jsxs as _jsxs } from \"react/jsx-runtime\";\nexport const useDesktopPicker = _ref => {\n  let {\n      props,\n      steps\n    } = _ref,\n    pickerParams = _objectWithoutPropertiesLoose(_ref, _excluded);\n  const {\n    slots,\n    slotProps: innerSlotProps,\n    label,\n    inputRef,\n    localeText\n  } = props;\n  const getStepNavigation = createNonRangePickerStepNavigation({\n    steps\n  });\n  const {\n    providerProps,\n    renderCurrentView,\n    ownerState\n  } = usePicker(_extends({}, pickerParams, {\n    props,\n    localeText,\n    autoFocusView: true,\n    viewContainerRole: 'dialog',\n    variant: 'desktop',\n    getStepNavigation\n  }));\n  const labelId = providerProps.privateContextValue.labelId;\n  const isToolbarHidden = innerSlotProps?.toolbar?.hidden ?? false;\n  const Field = slots.field;\n  const _useSlotProps = useSlotProps({\n      elementType: Field,\n      externalSlotProps: innerSlotProps?.field,\n      additionalProps: _extends({}, isToolbarHidden && {\n        id: labelId\n      }),\n      ownerState\n    }),\n    fieldProps = _objectWithoutPropertiesLoose(_useSlotProps, _excluded2);\n  const Layout = slots.layout ?? PickersLayout;\n  let labelledById = labelId;\n  if (isToolbarHidden) {\n    if (label) {\n      labelledById = `${labelId}-label`;\n    } else {\n      labelledById = undefined;\n    }\n  }\n  const slotProps = _extends({}, innerSlotProps, {\n    toolbar: _extends({}, innerSlotProps?.toolbar, {\n      titleId: labelId\n    }),\n    popper: _extends({\n      'aria-labelledby': labelledById\n    }, innerSlotProps?.popper)\n  });\n  const renderPicker = () => /*#__PURE__*/_jsx(PickerProvider, _extends({}, providerProps, {\n    children: /*#__PURE__*/_jsxs(PickerFieldUIContextProvider, {\n      slots: slots,\n      slotProps: slotProps,\n      inputRef: inputRef,\n      children: [/*#__PURE__*/_jsx(Field, _extends({}, fieldProps)), /*#__PURE__*/_jsx(PickerPopper, {\n        slots: slots,\n        slotProps: slotProps,\n        children: /*#__PURE__*/_jsx(Layout, _extends({}, slotProps?.layout, {\n          slots: slots,\n          slotProps: slotProps,\n          children: renderCurrentView()\n        }))\n      })]\n    })\n  }));\n  return {\n    renderPicker\n  };\n};", "map": {"version": 3, "names": ["_extends", "_objectWithoutPropertiesLoose", "_excluded", "_excluded2", "React", "useSlotProps", "Picker<PERSON><PERSON><PERSON>", "usePicker", "PickersLayout", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "PickerFieldUIContextProvider", "createNonRangePickerStepNavigation", "jsx", "_jsx", "jsxs", "_jsxs", "useDesktopPicker", "_ref", "props", "steps", "pickerParams", "slots", "slotProps", "innerSlotProps", "label", "inputRef", "localeText", "getStepNavigation", "providerProps", "renderCurrentView", "ownerState", "autoFocusView", "viewContainerRole", "variant", "labelId", "privateContextValue", "isToolbarHidden", "toolbar", "hidden", "Field", "field", "_useSlotProps", "elementType", "externalSlotProps", "additionalProps", "id", "fieldProps", "Layout", "layout", "labelledById", "undefined", "titleId", "popper", "renderPicker", "children"], "sources": ["C:/CMS/webapp/frontend/node_modules/@mui/x-date-pickers/esm/internals/hooks/useDesktopPicker/useDesktopPicker.js"], "sourcesContent": ["import _extends from \"@babel/runtime/helpers/esm/extends\";\nimport _objectWithoutPropertiesLoose from \"@babel/runtime/helpers/esm/objectWithoutPropertiesLoose\";\nconst _excluded = [\"props\", \"steps\"],\n  _excluded2 = [\"ownerState\"];\nimport * as React from 'react';\nimport useSlotProps from '@mui/utils/useSlotProps';\nimport { PickerPopper } from \"../../components/PickerPopper/PickerPopper.js\";\nimport { usePicker } from \"../usePicker/index.js\";\nimport { PickersLayout } from \"../../../PickersLayout/index.js\";\nimport { PickerProvider } from \"../../components/PickerProvider.js\";\nimport { PickerFieldUIContextProvider } from \"../../components/PickerFieldUI.js\";\nimport { createNonRangePickerStepNavigation } from \"../../utils/createNonRangePickerStepNavigation.js\";\n\n/**\n * Hook managing all the single-date desktop pickers:\n * - DesktopDatePicker\n * - DesktopDateTimePicker\n * - DesktopTimePicker\n */\nimport { jsx as _jsx, jsxs as _jsxs } from \"react/jsx-runtime\";\nexport const useDesktopPicker = _ref => {\n  let {\n      props,\n      steps\n    } = _ref,\n    pickerParams = _objectWithoutPropertiesLoose(_ref, _excluded);\n  const {\n    slots,\n    slotProps: innerSlotProps,\n    label,\n    inputRef,\n    localeText\n  } = props;\n  const getStepNavigation = createNonRangePickerStepNavigation({\n    steps\n  });\n  const {\n    providerProps,\n    renderCurrentView,\n    ownerState\n  } = usePicker(_extends({}, pickerParams, {\n    props,\n    localeText,\n    autoFocusView: true,\n    viewContainerRole: 'dialog',\n    variant: 'desktop',\n    getStepNavigation\n  }));\n  const labelId = providerProps.privateContextValue.labelId;\n  const isToolbarHidden = innerSlotProps?.toolbar?.hidden ?? false;\n  const Field = slots.field;\n  const _useSlotProps = useSlotProps({\n      elementType: Field,\n      externalSlotProps: innerSlotProps?.field,\n      additionalProps: _extends({}, isToolbarHidden && {\n        id: labelId\n      }),\n      ownerState\n    }),\n    fieldProps = _objectWithoutPropertiesLoose(_useSlotProps, _excluded2);\n  const Layout = slots.layout ?? PickersLayout;\n  let labelledById = labelId;\n  if (isToolbarHidden) {\n    if (label) {\n      labelledById = `${labelId}-label`;\n    } else {\n      labelledById = undefined;\n    }\n  }\n  const slotProps = _extends({}, innerSlotProps, {\n    toolbar: _extends({}, innerSlotProps?.toolbar, {\n      titleId: labelId\n    }),\n    popper: _extends({\n      'aria-labelledby': labelledById\n    }, innerSlotProps?.popper)\n  });\n  const renderPicker = () => /*#__PURE__*/_jsx(PickerProvider, _extends({}, providerProps, {\n    children: /*#__PURE__*/_jsxs(PickerFieldUIContextProvider, {\n      slots: slots,\n      slotProps: slotProps,\n      inputRef: inputRef,\n      children: [/*#__PURE__*/_jsx(Field, _extends({}, fieldProps)), /*#__PURE__*/_jsx(PickerPopper, {\n        slots: slots,\n        slotProps: slotProps,\n        children: /*#__PURE__*/_jsx(Layout, _extends({}, slotProps?.layout, {\n          slots: slots,\n          slotProps: slotProps,\n          children: renderCurrentView()\n        }))\n      })]\n    })\n  }));\n  return {\n    renderPicker\n  };\n};"], "mappings": "AAAA,OAAOA,QAAQ,MAAM,oCAAoC;AACzD,OAAOC,6BAA6B,MAAM,yDAAyD;AACnG,MAAMC,SAAS,GAAG,CAAC,OAAO,EAAE,OAAO,CAAC;EAClCC,UAAU,GAAG,CAAC,YAAY,CAAC;AAC7B,OAAO,KAAKC,KAAK,MAAM,OAAO;AAC9B,OAAOC,YAAY,MAAM,yBAAyB;AAClD,SAASC,YAAY,QAAQ,+CAA+C;AAC5E,SAASC,SAAS,QAAQ,uBAAuB;AACjD,SAASC,aAAa,QAAQ,iCAAiC;AAC/D,SAASC,cAAc,QAAQ,oCAAoC;AACnE,SAASC,4BAA4B,QAAQ,mCAAmC;AAChF,SAASC,kCAAkC,QAAQ,mDAAmD;;AAEtG;AACA;AACA;AACA;AACA;AACA;AACA,SAASC,GAAG,IAAIC,IAAI,EAAEC,IAAI,IAAIC,KAAK,QAAQ,mBAAmB;AAC9D,OAAO,MAAMC,gBAAgB,GAAGC,IAAI,IAAI;EACtC,IAAI;MACAC,KAAK;MACLC;IACF,CAAC,GAAGF,IAAI;IACRG,YAAY,GAAGnB,6BAA6B,CAACgB,IAAI,EAAEf,SAAS,CAAC;EAC/D,MAAM;IACJmB,KAAK;IACLC,SAAS,EAAEC,cAAc;IACzBC,KAAK;IACLC,QAAQ;IACRC;EACF,CAAC,GAAGR,KAAK;EACT,MAAMS,iBAAiB,GAAGhB,kCAAkC,CAAC;IAC3DQ;EACF,CAAC,CAAC;EACF,MAAM;IACJS,aAAa;IACbC,iBAAiB;IACjBC;EACF,CAAC,GAAGvB,SAAS,CAACP,QAAQ,CAAC,CAAC,CAAC,EAAEoB,YAAY,EAAE;IACvCF,KAAK;IACLQ,UAAU;IACVK,aAAa,EAAE,IAAI;IACnBC,iBAAiB,EAAE,QAAQ;IAC3BC,OAAO,EAAE,SAAS;IAClBN;EACF,CAAC,CAAC,CAAC;EACH,MAAMO,OAAO,GAAGN,aAAa,CAACO,mBAAmB,CAACD,OAAO;EACzD,MAAME,eAAe,GAAGb,cAAc,EAAEc,OAAO,EAAEC,MAAM,IAAI,KAAK;EAChE,MAAMC,KAAK,GAAGlB,KAAK,CAACmB,KAAK;EACzB,MAAMC,aAAa,GAAGpC,YAAY,CAAC;MAC/BqC,WAAW,EAAEH,KAAK;MAClBI,iBAAiB,EAAEpB,cAAc,EAAEiB,KAAK;MACxCI,eAAe,EAAE5C,QAAQ,CAAC,CAAC,CAAC,EAAEoC,eAAe,IAAI;QAC/CS,EAAE,EAAEX;MACN,CAAC,CAAC;MACFJ;IACF,CAAC,CAAC;IACFgB,UAAU,GAAG7C,6BAA6B,CAACwC,aAAa,EAAEtC,UAAU,CAAC;EACvE,MAAM4C,MAAM,GAAG1B,KAAK,CAAC2B,MAAM,IAAIxC,aAAa;EAC5C,IAAIyC,YAAY,GAAGf,OAAO;EAC1B,IAAIE,eAAe,EAAE;IACnB,IAAIZ,KAAK,EAAE;MACTyB,YAAY,GAAG,GAAGf,OAAO,QAAQ;IACnC,CAAC,MAAM;MACLe,YAAY,GAAGC,SAAS;IAC1B;EACF;EACA,MAAM5B,SAAS,GAAGtB,QAAQ,CAAC,CAAC,CAAC,EAAEuB,cAAc,EAAE;IAC7Cc,OAAO,EAAErC,QAAQ,CAAC,CAAC,CAAC,EAAEuB,cAAc,EAAEc,OAAO,EAAE;MAC7Cc,OAAO,EAAEjB;IACX,CAAC,CAAC;IACFkB,MAAM,EAAEpD,QAAQ,CAAC;MACf,iBAAiB,EAAEiD;IACrB,CAAC,EAAE1B,cAAc,EAAE6B,MAAM;EAC3B,CAAC,CAAC;EACF,MAAMC,YAAY,GAAGA,CAAA,KAAM,aAAaxC,IAAI,CAACJ,cAAc,EAAET,QAAQ,CAAC,CAAC,CAAC,EAAE4B,aAAa,EAAE;IACvF0B,QAAQ,EAAE,aAAavC,KAAK,CAACL,4BAA4B,EAAE;MACzDW,KAAK,EAAEA,KAAK;MACZC,SAAS,EAAEA,SAAS;MACpBG,QAAQ,EAAEA,QAAQ;MAClB6B,QAAQ,EAAE,CAAC,aAAazC,IAAI,CAAC0B,KAAK,EAAEvC,QAAQ,CAAC,CAAC,CAAC,EAAE8C,UAAU,CAAC,CAAC,EAAE,aAAajC,IAAI,CAACP,YAAY,EAAE;QAC7Fe,KAAK,EAAEA,KAAK;QACZC,SAAS,EAAEA,SAAS;QACpBgC,QAAQ,EAAE,aAAazC,IAAI,CAACkC,MAAM,EAAE/C,QAAQ,CAAC,CAAC,CAAC,EAAEsB,SAAS,EAAE0B,MAAM,EAAE;UAClE3B,KAAK,EAAEA,KAAK;UACZC,SAAS,EAAEA,SAAS;UACpBgC,QAAQ,EAAEzB,iBAAiB,CAAC;QAC9B,CAAC,CAAC;MACJ,CAAC,CAAC;IACJ,CAAC;EACH,CAAC,CAAC,CAAC;EACH,OAAO;IACLwB;EACF,CAAC;AACH,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}