{"ast": null, "code": "const formatRelativeLocale = {\n  lastWeek: \"eeee 'إلي فات مع' p\",\n  yesterday: \"'البارح مع' p\",\n  today: \"'اليوم مع' p\",\n  tomorrow: \"'غدوة مع' p\",\n  nextWeek: \"eeee 'الجمعة الجاية مع' p 'نهار'\",\n  other: \"P\"\n};\nexport const formatRelative = token => formatRelativeLocale[token];", "map": {"version": 3, "names": ["formatRelativeLocale", "lastWeek", "yesterday", "today", "tomorrow", "nextWeek", "other", "formatRelative", "token"], "sources": ["C:/CMS/webapp/frontend/node_modules/date-fns/locale/ar-TN/_lib/formatRelative.js"], "sourcesContent": ["const formatRelativeLocale = {\n  lastWeek: \"eeee 'إلي فات مع' p\",\n  yesterday: \"'البارح مع' p\",\n  today: \"'اليوم مع' p\",\n  tomorrow: \"'غدوة مع' p\",\n  nextWeek: \"eeee 'الجمعة الجاية مع' p 'نهار'\",\n  other: \"P\",\n};\n\nexport const formatRelative = (token) => formatRelativeLocale[token];\n"], "mappings": "AAAA,MAAMA,oBAAoB,GAAG;EAC3BC,QAAQ,EAAE,qBAAqB;EAC/BC,SAAS,EAAE,eAAe;EAC1BC,KAAK,EAAE,cAAc;EACrBC,QAAQ,EAAE,aAAa;EACvBC,QAAQ,EAAE,kCAAkC;EAC5CC,KAAK,EAAE;AACT,CAAC;AAED,OAAO,MAAMC,cAAc,GAAIC,KAAK,IAAKR,oBAAoB,CAACQ,KAAK,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}