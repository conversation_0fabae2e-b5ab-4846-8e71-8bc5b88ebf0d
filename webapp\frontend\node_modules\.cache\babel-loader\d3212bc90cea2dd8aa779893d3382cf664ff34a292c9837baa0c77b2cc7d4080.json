{"ast": null, "code": "var _Scatter;\nfunction _typeof(o) {\n  \"@babel/helpers - typeof\";\n\n  return _typeof = \"function\" == typeof Symbol && \"symbol\" == typeof Symbol.iterator ? function (o) {\n    return typeof o;\n  } : function (o) {\n    return o && \"function\" == typeof Symbol && o.constructor === Symbol && o !== Symbol.prototype ? \"symbol\" : typeof o;\n  }, _typeof(o);\n}\nfunction _extends() {\n  _extends = Object.assign ? Object.assign.bind() : function (target) {\n    for (var i = 1; i < arguments.length; i++) {\n      var source = arguments[i];\n      for (var key in source) {\n        if (Object.prototype.hasOwnProperty.call(source, key)) {\n          target[key] = source[key];\n        }\n      }\n    }\n    return target;\n  };\n  return _extends.apply(this, arguments);\n}\nfunction ownKeys(e, r) {\n  var t = Object.keys(e);\n  if (Object.getOwnPropertySymbols) {\n    var o = Object.getOwnPropertySymbols(e);\n    r && (o = o.filter(function (r) {\n      return Object.getOwnPropertyDescriptor(e, r).enumerable;\n    })), t.push.apply(t, o);\n  }\n  return t;\n}\nfunction _objectSpread(e) {\n  for (var r = 1; r < arguments.length; r++) {\n    var t = null != arguments[r] ? arguments[r] : {};\n    r % 2 ? ownKeys(Object(t), !0).forEach(function (r) {\n      _defineProperty(e, r, t[r]);\n    }) : Object.getOwnPropertyDescriptors ? Object.defineProperties(e, Object.getOwnPropertyDescriptors(t)) : ownKeys(Object(t)).forEach(function (r) {\n      Object.defineProperty(e, r, Object.getOwnPropertyDescriptor(t, r));\n    });\n  }\n  return e;\n}\nfunction _classCallCheck(instance, Constructor) {\n  if (!(instance instanceof Constructor)) {\n    throw new TypeError(\"Cannot call a class as a function\");\n  }\n}\nfunction _defineProperties(target, props) {\n  for (var i = 0; i < props.length; i++) {\n    var descriptor = props[i];\n    descriptor.enumerable = descriptor.enumerable || false;\n    descriptor.configurable = true;\n    if (\"value\" in descriptor) descriptor.writable = true;\n    Object.defineProperty(target, _toPropertyKey(descriptor.key), descriptor);\n  }\n}\nfunction _createClass(Constructor, protoProps, staticProps) {\n  if (protoProps) _defineProperties(Constructor.prototype, protoProps);\n  if (staticProps) _defineProperties(Constructor, staticProps);\n  Object.defineProperty(Constructor, \"prototype\", {\n    writable: false\n  });\n  return Constructor;\n}\nfunction _callSuper(t, o, e) {\n  return o = _getPrototypeOf(o), _possibleConstructorReturn(t, _isNativeReflectConstruct() ? Reflect.construct(o, e || [], _getPrototypeOf(t).constructor) : o.apply(t, e));\n}\nfunction _possibleConstructorReturn(self, call) {\n  if (call && (_typeof(call) === \"object\" || typeof call === \"function\")) {\n    return call;\n  } else if (call !== void 0) {\n    throw new TypeError(\"Derived constructors may only return object or undefined\");\n  }\n  return _assertThisInitialized(self);\n}\nfunction _assertThisInitialized(self) {\n  if (self === void 0) {\n    throw new ReferenceError(\"this hasn't been initialised - super() hasn't been called\");\n  }\n  return self;\n}\nfunction _isNativeReflectConstruct() {\n  try {\n    var t = !Boolean.prototype.valueOf.call(Reflect.construct(Boolean, [], function () {}));\n  } catch (t) {}\n  return (_isNativeReflectConstruct = function _isNativeReflectConstruct() {\n    return !!t;\n  })();\n}\nfunction _getPrototypeOf(o) {\n  _getPrototypeOf = Object.setPrototypeOf ? Object.getPrototypeOf.bind() : function _getPrototypeOf(o) {\n    return o.__proto__ || Object.getPrototypeOf(o);\n  };\n  return _getPrototypeOf(o);\n}\nfunction _inherits(subClass, superClass) {\n  if (typeof superClass !== \"function\" && superClass !== null) {\n    throw new TypeError(\"Super expression must either be null or a function\");\n  }\n  subClass.prototype = Object.create(superClass && superClass.prototype, {\n    constructor: {\n      value: subClass,\n      writable: true,\n      configurable: true\n    }\n  });\n  Object.defineProperty(subClass, \"prototype\", {\n    writable: false\n  });\n  if (superClass) _setPrototypeOf(subClass, superClass);\n}\nfunction _setPrototypeOf(o, p) {\n  _setPrototypeOf = Object.setPrototypeOf ? Object.setPrototypeOf.bind() : function _setPrototypeOf(o, p) {\n    o.__proto__ = p;\n    return o;\n  };\n  return _setPrototypeOf(o, p);\n}\nfunction _defineProperty(obj, key, value) {\n  key = _toPropertyKey(key);\n  if (key in obj) {\n    Object.defineProperty(obj, key, {\n      value: value,\n      enumerable: true,\n      configurable: true,\n      writable: true\n    });\n  } else {\n    obj[key] = value;\n  }\n  return obj;\n}\nfunction _toPropertyKey(t) {\n  var i = _toPrimitive(t, \"string\");\n  return \"symbol\" == _typeof(i) ? i : i + \"\";\n}\nfunction _toPrimitive(t, r) {\n  if (\"object\" != _typeof(t) || !t) return t;\n  var e = t[Symbol.toPrimitive];\n  if (void 0 !== e) {\n    var i = e.call(t, r || \"default\");\n    if (\"object\" != _typeof(i)) return i;\n    throw new TypeError(\"@@toPrimitive must return a primitive value.\");\n  }\n  return (\"string\" === r ? String : Number)(t);\n}\n/**\n * @fileOverview Render a group of scatters\n */\nimport React, { PureComponent } from 'react';\nimport Animate from 'react-smooth';\nimport isNil from 'lodash/isNil';\nimport isEqual from 'lodash/isEqual';\nimport isFunction from 'lodash/isFunction';\nimport clsx from 'clsx';\nimport { Layer } from '../container/Layer';\nimport { LabelList } from '../component/LabelList';\nimport { findAllByType, filterProps } from '../util/ReactUtils';\nimport { Global } from '../util/Global';\nimport { ZAxis } from './ZAxis';\nimport { Curve } from '../shape/Curve';\nimport { ErrorBar } from './ErrorBar';\nimport { Cell } from '../component/Cell';\nimport { uniqueId, interpolateNumber, getLinearRegression } from '../util/DataUtils';\nimport { getValueByDataKey, getCateCoordinateOfLine } from '../util/ChartUtils';\nimport { adaptEventsOfChild } from '../util/types';\nimport { ScatterSymbol } from '../util/ScatterUtils';\nexport var Scatter = /*#__PURE__*/function (_PureComponent) {\n  function Scatter() {\n    var _this;\n    _classCallCheck(this, Scatter);\n    for (var _len = arguments.length, args = new Array(_len), _key = 0; _key < _len; _key++) {\n      args[_key] = arguments[_key];\n    }\n    _this = _callSuper(this, Scatter, [].concat(args));\n    _defineProperty(_this, \"state\", {\n      isAnimationFinished: false\n    });\n    _defineProperty(_this, \"handleAnimationEnd\", function () {\n      _this.setState({\n        isAnimationFinished: true\n      });\n    });\n    _defineProperty(_this, \"handleAnimationStart\", function () {\n      _this.setState({\n        isAnimationFinished: false\n      });\n    });\n    _defineProperty(_this, \"id\", uniqueId('recharts-scatter-'));\n    return _this;\n  }\n  _inherits(Scatter, _PureComponent);\n  return _createClass(Scatter, [{\n    key: \"renderSymbolsStatically\",\n    value: function renderSymbolsStatically(points) {\n      var _this2 = this;\n      var _this$props = this.props,\n        shape = _this$props.shape,\n        activeShape = _this$props.activeShape,\n        activeIndex = _this$props.activeIndex;\n      var baseProps = filterProps(this.props, false);\n      return points.map(function (entry, i) {\n        var isActive = activeIndex === i;\n        var option = isActive ? activeShape : shape;\n        var props = _objectSpread(_objectSpread({}, baseProps), entry);\n        return /*#__PURE__*/React.createElement(Layer, _extends({\n          className: \"recharts-scatter-symbol\"\n          // eslint-disable-next-line react/no-array-index-key\n          ,\n\n          key: \"symbol-\".concat(entry === null || entry === void 0 ? void 0 : entry.cx, \"-\").concat(entry === null || entry === void 0 ? void 0 : entry.cy, \"-\").concat(entry === null || entry === void 0 ? void 0 : entry.size, \"-\").concat(i)\n        }, adaptEventsOfChild(_this2.props, entry, i), {\n          role: \"img\"\n        }), /*#__PURE__*/React.createElement(ScatterSymbol, _extends({\n          option: option,\n          isActive: isActive\n          // eslint-disable-next-line react/no-array-index-key\n          ,\n\n          key: \"symbol-\".concat(i)\n        }, props)));\n      });\n    }\n  }, {\n    key: \"renderSymbolsWithAnimation\",\n    value: function renderSymbolsWithAnimation() {\n      var _this3 = this;\n      var _this$props2 = this.props,\n        points = _this$props2.points,\n        isAnimationActive = _this$props2.isAnimationActive,\n        animationBegin = _this$props2.animationBegin,\n        animationDuration = _this$props2.animationDuration,\n        animationEasing = _this$props2.animationEasing,\n        animationId = _this$props2.animationId;\n      var prevPoints = this.state.prevPoints;\n      return /*#__PURE__*/React.createElement(Animate, {\n        begin: animationBegin,\n        duration: animationDuration,\n        isActive: isAnimationActive,\n        easing: animationEasing,\n        from: {\n          t: 0\n        },\n        to: {\n          t: 1\n        },\n        key: \"pie-\".concat(animationId),\n        onAnimationEnd: this.handleAnimationEnd,\n        onAnimationStart: this.handleAnimationStart\n      }, function (_ref) {\n        var t = _ref.t;\n        var stepData = points.map(function (entry, index) {\n          var prev = prevPoints && prevPoints[index];\n          if (prev) {\n            var interpolatorCx = interpolateNumber(prev.cx, entry.cx);\n            var interpolatorCy = interpolateNumber(prev.cy, entry.cy);\n            var interpolatorSize = interpolateNumber(prev.size, entry.size);\n            return _objectSpread(_objectSpread({}, entry), {}, {\n              cx: interpolatorCx(t),\n              cy: interpolatorCy(t),\n              size: interpolatorSize(t)\n            });\n          }\n          var interpolator = interpolateNumber(0, entry.size);\n          return _objectSpread(_objectSpread({}, entry), {}, {\n            size: interpolator(t)\n          });\n        });\n        return /*#__PURE__*/React.createElement(Layer, null, _this3.renderSymbolsStatically(stepData));\n      });\n    }\n  }, {\n    key: \"renderSymbols\",\n    value: function renderSymbols() {\n      var _this$props3 = this.props,\n        points = _this$props3.points,\n        isAnimationActive = _this$props3.isAnimationActive;\n      var prevPoints = this.state.prevPoints;\n      if (isAnimationActive && points && points.length && (!prevPoints || !isEqual(prevPoints, points))) {\n        return this.renderSymbolsWithAnimation();\n      }\n      return this.renderSymbolsStatically(points);\n    }\n  }, {\n    key: \"renderErrorBar\",\n    value: function renderErrorBar() {\n      var isAnimationActive = this.props.isAnimationActive;\n      if (isAnimationActive && !this.state.isAnimationFinished) {\n        return null;\n      }\n      var _this$props4 = this.props,\n        points = _this$props4.points,\n        xAxis = _this$props4.xAxis,\n        yAxis = _this$props4.yAxis,\n        children = _this$props4.children;\n      var errorBarItems = findAllByType(children, ErrorBar);\n      if (!errorBarItems) {\n        return null;\n      }\n      return errorBarItems.map(function (item, i) {\n        var _item$props = item.props,\n          direction = _item$props.direction,\n          errorDataKey = _item$props.dataKey;\n        return /*#__PURE__*/React.cloneElement(item, {\n          key: \"\".concat(direction, \"-\").concat(errorDataKey, \"-\").concat(points[i]),\n          data: points,\n          xAxis: xAxis,\n          yAxis: yAxis,\n          layout: direction === 'x' ? 'vertical' : 'horizontal',\n          dataPointFormatter: function dataPointFormatter(dataPoint, dataKey) {\n            return {\n              x: dataPoint.cx,\n              y: dataPoint.cy,\n              value: direction === 'x' ? +dataPoint.node.x : +dataPoint.node.y,\n              errorVal: getValueByDataKey(dataPoint, dataKey)\n            };\n          }\n        });\n      });\n    }\n  }, {\n    key: \"renderLine\",\n    value: function renderLine() {\n      var _this$props5 = this.props,\n        points = _this$props5.points,\n        line = _this$props5.line,\n        lineType = _this$props5.lineType,\n        lineJointType = _this$props5.lineJointType;\n      var scatterProps = filterProps(this.props, false);\n      var customLineProps = filterProps(line, false);\n      var linePoints, lineItem;\n      if (lineType === 'joint') {\n        linePoints = points.map(function (entry) {\n          return {\n            x: entry.cx,\n            y: entry.cy\n          };\n        });\n      } else if (lineType === 'fitting') {\n        var _getLinearRegression = getLinearRegression(points),\n          xmin = _getLinearRegression.xmin,\n          xmax = _getLinearRegression.xmax,\n          a = _getLinearRegression.a,\n          b = _getLinearRegression.b;\n        var linearExp = function linearExp(x) {\n          return a * x + b;\n        };\n        linePoints = [{\n          x: xmin,\n          y: linearExp(xmin)\n        }, {\n          x: xmax,\n          y: linearExp(xmax)\n        }];\n      }\n      var lineProps = _objectSpread(_objectSpread(_objectSpread({}, scatterProps), {}, {\n        fill: 'none',\n        stroke: scatterProps && scatterProps.fill\n      }, customLineProps), {}, {\n        points: linePoints\n      });\n      if (/*#__PURE__*/React.isValidElement(line)) {\n        lineItem = /*#__PURE__*/React.cloneElement(line, lineProps);\n      } else if (isFunction(line)) {\n        lineItem = line(lineProps);\n      } else {\n        lineItem = /*#__PURE__*/React.createElement(Curve, _extends({}, lineProps, {\n          type: lineJointType\n        }));\n      }\n      return /*#__PURE__*/React.createElement(Layer, {\n        className: \"recharts-scatter-line\",\n        key: \"recharts-scatter-line\"\n      }, lineItem);\n    }\n  }, {\n    key: \"render\",\n    value: function render() {\n      var _this$props6 = this.props,\n        hide = _this$props6.hide,\n        points = _this$props6.points,\n        line = _this$props6.line,\n        className = _this$props6.className,\n        xAxis = _this$props6.xAxis,\n        yAxis = _this$props6.yAxis,\n        left = _this$props6.left,\n        top = _this$props6.top,\n        width = _this$props6.width,\n        height = _this$props6.height,\n        id = _this$props6.id,\n        isAnimationActive = _this$props6.isAnimationActive;\n      if (hide || !points || !points.length) {\n        return null;\n      }\n      var isAnimationFinished = this.state.isAnimationFinished;\n      var layerClass = clsx('recharts-scatter', className);\n      var needClipX = xAxis && xAxis.allowDataOverflow;\n      var needClipY = yAxis && yAxis.allowDataOverflow;\n      var needClip = needClipX || needClipY;\n      var clipPathId = isNil(id) ? this.id : id;\n      return /*#__PURE__*/React.createElement(Layer, {\n        className: layerClass,\n        clipPath: needClip ? \"url(#clipPath-\".concat(clipPathId, \")\") : null\n      }, needClipX || needClipY ? /*#__PURE__*/React.createElement(\"defs\", null, /*#__PURE__*/React.createElement(\"clipPath\", {\n        id: \"clipPath-\".concat(clipPathId)\n      }, /*#__PURE__*/React.createElement(\"rect\", {\n        x: needClipX ? left : left - width / 2,\n        y: needClipY ? top : top - height / 2,\n        width: needClipX ? width : width * 2,\n        height: needClipY ? height : height * 2\n      }))) : null, line && this.renderLine(), this.renderErrorBar(), /*#__PURE__*/React.createElement(Layer, {\n        key: \"recharts-scatter-symbols\"\n      }, this.renderSymbols()), (!isAnimationActive || isAnimationFinished) && LabelList.renderCallByParent(this.props, points));\n    }\n  }], [{\n    key: \"getDerivedStateFromProps\",\n    value: function getDerivedStateFromProps(nextProps, prevState) {\n      if (nextProps.animationId !== prevState.prevAnimationId) {\n        return {\n          prevAnimationId: nextProps.animationId,\n          curPoints: nextProps.points,\n          prevPoints: prevState.curPoints\n        };\n      }\n      if (nextProps.points !== prevState.curPoints) {\n        return {\n          curPoints: nextProps.points\n        };\n      }\n      return null;\n    }\n  }]);\n}(PureComponent);\n_Scatter = Scatter;\n_defineProperty(Scatter, \"displayName\", 'Scatter');\n_defineProperty(Scatter, \"defaultProps\", {\n  xAxisId: 0,\n  yAxisId: 0,\n  zAxisId: 0,\n  legendType: 'circle',\n  lineType: 'joint',\n  lineJointType: 'linear',\n  data: [],\n  shape: 'circle',\n  hide: false,\n  isAnimationActive: !Global.isSsr,\n  animationBegin: 0,\n  animationDuration: 400,\n  animationEasing: 'linear'\n});\n/**\n * Compose the data of each group\n * @param  {Object} xAxis   The configuration of x-axis\n * @param  {Object} yAxis   The configuration of y-axis\n * @param  {String} dataKey The unique key of a group\n * @return {Array}  Composed data\n */\n_defineProperty(Scatter, \"getComposedData\", function (_ref2) {\n  var xAxis = _ref2.xAxis,\n    yAxis = _ref2.yAxis,\n    zAxis = _ref2.zAxis,\n    item = _ref2.item,\n    displayedData = _ref2.displayedData,\n    xAxisTicks = _ref2.xAxisTicks,\n    yAxisTicks = _ref2.yAxisTicks,\n    offset = _ref2.offset;\n  var tooltipType = item.props.tooltipType;\n  var cells = findAllByType(item.props.children, Cell);\n  var xAxisDataKey = isNil(xAxis.dataKey) ? item.props.dataKey : xAxis.dataKey;\n  var yAxisDataKey = isNil(yAxis.dataKey) ? item.props.dataKey : yAxis.dataKey;\n  var zAxisDataKey = zAxis && zAxis.dataKey;\n  var defaultRangeZ = zAxis ? zAxis.range : ZAxis.defaultProps.range;\n  var defaultZ = defaultRangeZ && defaultRangeZ[0];\n  var xBandSize = xAxis.scale.bandwidth ? xAxis.scale.bandwidth() : 0;\n  var yBandSize = yAxis.scale.bandwidth ? yAxis.scale.bandwidth() : 0;\n  var points = displayedData.map(function (entry, index) {\n    var x = getValueByDataKey(entry, xAxisDataKey);\n    var y = getValueByDataKey(entry, yAxisDataKey);\n    var z = !isNil(zAxisDataKey) && getValueByDataKey(entry, zAxisDataKey) || '-';\n    var tooltipPayload = [{\n      name: isNil(xAxis.dataKey) ? item.props.name : xAxis.name || xAxis.dataKey,\n      unit: xAxis.unit || '',\n      value: x,\n      payload: entry,\n      dataKey: xAxisDataKey,\n      type: tooltipType\n    }, {\n      name: isNil(yAxis.dataKey) ? item.props.name : yAxis.name || yAxis.dataKey,\n      unit: yAxis.unit || '',\n      value: y,\n      payload: entry,\n      dataKey: yAxisDataKey,\n      type: tooltipType\n    }];\n    if (z !== '-') {\n      tooltipPayload.push({\n        name: zAxis.name || zAxis.dataKey,\n        unit: zAxis.unit || '',\n        value: z,\n        payload: entry,\n        dataKey: zAxisDataKey,\n        type: tooltipType\n      });\n    }\n    var cx = getCateCoordinateOfLine({\n      axis: xAxis,\n      ticks: xAxisTicks,\n      bandSize: xBandSize,\n      entry: entry,\n      index: index,\n      dataKey: xAxisDataKey\n    });\n    var cy = getCateCoordinateOfLine({\n      axis: yAxis,\n      ticks: yAxisTicks,\n      bandSize: yBandSize,\n      entry: entry,\n      index: index,\n      dataKey: yAxisDataKey\n    });\n    var size = z !== '-' ? zAxis.scale(z) : defaultZ;\n    var radius = Math.sqrt(Math.max(size, 0) / Math.PI);\n    return _objectSpread(_objectSpread({}, entry), {}, {\n      cx: cx,\n      cy: cy,\n      x: cx - radius,\n      y: cy - radius,\n      xAxis: xAxis,\n      yAxis: yAxis,\n      zAxis: zAxis,\n      width: 2 * radius,\n      height: 2 * radius,\n      size: size,\n      node: {\n        x: x,\n        y: y,\n        z: z\n      },\n      tooltipPayload: tooltipPayload,\n      tooltipPosition: {\n        x: cx,\n        y: cy\n      },\n      payload: entry\n    }, cells && cells[index] && cells[index].props);\n  });\n  return _objectSpread({\n    points: points\n  }, offset);\n});", "map": {"version": 3, "names": ["_<PERSON><PERSON><PERSON>", "_typeof", "o", "Symbol", "iterator", "constructor", "prototype", "_extends", "Object", "assign", "bind", "target", "i", "arguments", "length", "source", "key", "hasOwnProperty", "call", "apply", "ownKeys", "e", "r", "t", "keys", "getOwnPropertySymbols", "filter", "getOwnPropertyDescriptor", "enumerable", "push", "_objectSpread", "for<PERSON>ach", "_defineProperty", "getOwnPropertyDescriptors", "defineProperties", "defineProperty", "_classCallCheck", "instance", "<PERSON><PERSON><PERSON><PERSON>", "TypeError", "_defineProperties", "props", "descriptor", "configurable", "writable", "_to<PERSON><PERSON><PERSON><PERSON><PERSON>", "_createClass", "protoProps", "staticProps", "_callSuper", "_getPrototypeOf", "_possibleConstructorReturn", "_isNativeReflectConstruct", "Reflect", "construct", "self", "_assertThisInitialized", "ReferenceError", "Boolean", "valueOf", "setPrototypeOf", "getPrototypeOf", "__proto__", "_inherits", "subClass", "superClass", "create", "value", "_setPrototypeOf", "p", "obj", "_toPrimitive", "toPrimitive", "String", "Number", "React", "PureComponent", "Animate", "isNil", "isEqual", "isFunction", "clsx", "Layer", "LabelList", "findAllByType", "filterProps", "Global", "ZAxis", "Curve", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Cell", "uniqueId", "interpolateNumber", "getLinearRegression", "getValueByDataKey", "getCateCoordinateOfLine", "adaptEventsOfChild", "ScatterSymbol", "<PERSON><PERSON><PERSON>", "_PureComponent", "_this", "_len", "args", "Array", "_key", "concat", "isAnimationFinished", "setState", "renderSymbolsStatically", "points", "_this2", "_this$props", "shape", "activeShape", "activeIndex", "baseProps", "map", "entry", "isActive", "option", "createElement", "className", "cx", "cy", "size", "role", "renderSymbolsWithAnimation", "_this3", "_this$props2", "isAnimationActive", "animationBegin", "animationDuration", "animationEasing", "animationId", "prevPoints", "state", "begin", "duration", "easing", "from", "to", "onAnimationEnd", "handleAnimationEnd", "onAnimationStart", "handleAnimationStart", "_ref", "stepData", "index", "prev", "interpolatorCx", "interpolatorCy", "interpolatorSize", "interpolator", "renderSymbols", "_this$props3", "renderErrorBar", "_this$props4", "xAxis", "yAxis", "children", "errorBarItems", "item", "_item$props", "direction", "errorData<PERSON>ey", "dataKey", "cloneElement", "data", "layout", "dataPointFormatter", "dataPoint", "x", "y", "node", "errorVal", "renderLine", "_this$props5", "line", "lineType", "lineJointType", "scatterProps", "customLineProps", "linePoints", "lineItem", "_getLinearRegression", "xmin", "xmax", "a", "b", "linearExp", "lineProps", "fill", "stroke", "isValidElement", "type", "render", "_this$props6", "hide", "left", "top", "width", "height", "id", "layerClass", "needClipX", "allowDataOverflow", "needClipY", "needClip", "clipPathId", "clipPath", "renderCallByParent", "getDerivedStateFromProps", "nextProps", "prevState", "prevAnimationId", "curPoints", "xAxisId", "yAxisId", "zAxisId", "legendType", "isSsr", "_ref2", "zAxis", "displayedData", "xAxisTicks", "yAxisTicks", "offset", "tooltipType", "cells", "xAxisDataKey", "yAxisDataKey", "zAxisDataKey", "defaultRangeZ", "range", "defaultProps", "defaultZ", "xBandSize", "scale", "bandwidth", "yBandSize", "z", "tooltipPayload", "name", "unit", "payload", "axis", "ticks", "bandSize", "radius", "Math", "sqrt", "max", "PI", "tooltipPosition"], "sources": ["C:/Users/<USER>/PycharmProjects/CMS/webapp/frontend/node_modules/recharts/es6/cartesian/Scatter.js"], "sourcesContent": ["var _Scatter;\nfunction _typeof(o) { \"@babel/helpers - typeof\"; return _typeof = \"function\" == typeof Symbol && \"symbol\" == typeof Symbol.iterator ? function (o) { return typeof o; } : function (o) { return o && \"function\" == typeof Symbol && o.constructor === Symbol && o !== Symbol.prototype ? \"symbol\" : typeof o; }, _typeof(o); }\nfunction _extends() { _extends = Object.assign ? Object.assign.bind() : function (target) { for (var i = 1; i < arguments.length; i++) { var source = arguments[i]; for (var key in source) { if (Object.prototype.hasOwnProperty.call(source, key)) { target[key] = source[key]; } } } return target; }; return _extends.apply(this, arguments); }\nfunction ownKeys(e, r) { var t = Object.keys(e); if (Object.getOwnPropertySymbols) { var o = Object.getOwnPropertySymbols(e); r && (o = o.filter(function (r) { return Object.getOwnPropertyDescriptor(e, r).enumerable; })), t.push.apply(t, o); } return t; }\nfunction _objectSpread(e) { for (var r = 1; r < arguments.length; r++) { var t = null != arguments[r] ? arguments[r] : {}; r % 2 ? ownKeys(Object(t), !0).forEach(function (r) { _defineProperty(e, r, t[r]); }) : Object.getOwnPropertyDescriptors ? Object.defineProperties(e, Object.getOwnPropertyDescriptors(t)) : ownKeys(Object(t)).forEach(function (r) { Object.defineProperty(e, r, Object.getOwnPropertyDescriptor(t, r)); }); } return e; }\nfunction _classCallCheck(instance, Constructor) { if (!(instance instanceof Constructor)) { throw new TypeError(\"Cannot call a class as a function\"); } }\nfunction _defineProperties(target, props) { for (var i = 0; i < props.length; i++) { var descriptor = props[i]; descriptor.enumerable = descriptor.enumerable || false; descriptor.configurable = true; if (\"value\" in descriptor) descriptor.writable = true; Object.defineProperty(target, _toPropertyKey(descriptor.key), descriptor); } }\nfunction _createClass(Constructor, protoProps, staticProps) { if (protoProps) _defineProperties(Constructor.prototype, protoProps); if (staticProps) _defineProperties(Constructor, staticProps); Object.defineProperty(Constructor, \"prototype\", { writable: false }); return Constructor; }\nfunction _callSuper(t, o, e) { return o = _getPrototypeOf(o), _possibleConstructorReturn(t, _isNativeReflectConstruct() ? Reflect.construct(o, e || [], _getPrototypeOf(t).constructor) : o.apply(t, e)); }\nfunction _possibleConstructorReturn(self, call) { if (call && (_typeof(call) === \"object\" || typeof call === \"function\")) { return call; } else if (call !== void 0) { throw new TypeError(\"Derived constructors may only return object or undefined\"); } return _assertThisInitialized(self); }\nfunction _assertThisInitialized(self) { if (self === void 0) { throw new ReferenceError(\"this hasn't been initialised - super() hasn't been called\"); } return self; }\nfunction _isNativeReflectConstruct() { try { var t = !Boolean.prototype.valueOf.call(Reflect.construct(Boolean, [], function () {})); } catch (t) {} return (_isNativeReflectConstruct = function _isNativeReflectConstruct() { return !!t; })(); }\nfunction _getPrototypeOf(o) { _getPrototypeOf = Object.setPrototypeOf ? Object.getPrototypeOf.bind() : function _getPrototypeOf(o) { return o.__proto__ || Object.getPrototypeOf(o); }; return _getPrototypeOf(o); }\nfunction _inherits(subClass, superClass) { if (typeof superClass !== \"function\" && superClass !== null) { throw new TypeError(\"Super expression must either be null or a function\"); } subClass.prototype = Object.create(superClass && superClass.prototype, { constructor: { value: subClass, writable: true, configurable: true } }); Object.defineProperty(subClass, \"prototype\", { writable: false }); if (superClass) _setPrototypeOf(subClass, superClass); }\nfunction _setPrototypeOf(o, p) { _setPrototypeOf = Object.setPrototypeOf ? Object.setPrototypeOf.bind() : function _setPrototypeOf(o, p) { o.__proto__ = p; return o; }; return _setPrototypeOf(o, p); }\nfunction _defineProperty(obj, key, value) { key = _toPropertyKey(key); if (key in obj) { Object.defineProperty(obj, key, { value: value, enumerable: true, configurable: true, writable: true }); } else { obj[key] = value; } return obj; }\nfunction _toPropertyKey(t) { var i = _toPrimitive(t, \"string\"); return \"symbol\" == _typeof(i) ? i : i + \"\"; }\nfunction _toPrimitive(t, r) { if (\"object\" != _typeof(t) || !t) return t; var e = t[Symbol.toPrimitive]; if (void 0 !== e) { var i = e.call(t, r || \"default\"); if (\"object\" != _typeof(i)) return i; throw new TypeError(\"@@toPrimitive must return a primitive value.\"); } return (\"string\" === r ? String : Number)(t); }\n/**\n * @fileOverview Render a group of scatters\n */\nimport React, { PureComponent } from 'react';\nimport Animate from 'react-smooth';\nimport isNil from 'lodash/isNil';\nimport isEqual from 'lodash/isEqual';\nimport isFunction from 'lodash/isFunction';\nimport clsx from 'clsx';\nimport { Layer } from '../container/Layer';\nimport { LabelList } from '../component/LabelList';\nimport { findAllByType, filterProps } from '../util/ReactUtils';\nimport { Global } from '../util/Global';\nimport { ZAxis } from './ZAxis';\nimport { Curve } from '../shape/Curve';\nimport { ErrorBar } from './ErrorBar';\nimport { Cell } from '../component/Cell';\nimport { uniqueId, interpolateNumber, getLinearRegression } from '../util/DataUtils';\nimport { getValueByDataKey, getCateCoordinateOfLine } from '../util/ChartUtils';\nimport { adaptEventsOfChild } from '../util/types';\nimport { ScatterSymbol } from '../util/ScatterUtils';\nexport var Scatter = /*#__PURE__*/function (_PureComponent) {\n  function Scatter() {\n    var _this;\n    _classCallCheck(this, Scatter);\n    for (var _len = arguments.length, args = new Array(_len), _key = 0; _key < _len; _key++) {\n      args[_key] = arguments[_key];\n    }\n    _this = _callSuper(this, Scatter, [].concat(args));\n    _defineProperty(_this, \"state\", {\n      isAnimationFinished: false\n    });\n    _defineProperty(_this, \"handleAnimationEnd\", function () {\n      _this.setState({\n        isAnimationFinished: true\n      });\n    });\n    _defineProperty(_this, \"handleAnimationStart\", function () {\n      _this.setState({\n        isAnimationFinished: false\n      });\n    });\n    _defineProperty(_this, \"id\", uniqueId('recharts-scatter-'));\n    return _this;\n  }\n  _inherits(Scatter, _PureComponent);\n  return _createClass(Scatter, [{\n    key: \"renderSymbolsStatically\",\n    value: function renderSymbolsStatically(points) {\n      var _this2 = this;\n      var _this$props = this.props,\n        shape = _this$props.shape,\n        activeShape = _this$props.activeShape,\n        activeIndex = _this$props.activeIndex;\n      var baseProps = filterProps(this.props, false);\n      return points.map(function (entry, i) {\n        var isActive = activeIndex === i;\n        var option = isActive ? activeShape : shape;\n        var props = _objectSpread(_objectSpread({}, baseProps), entry);\n        return /*#__PURE__*/React.createElement(Layer, _extends({\n          className: \"recharts-scatter-symbol\"\n          // eslint-disable-next-line react/no-array-index-key\n          ,\n          key: \"symbol-\".concat(entry === null || entry === void 0 ? void 0 : entry.cx, \"-\").concat(entry === null || entry === void 0 ? void 0 : entry.cy, \"-\").concat(entry === null || entry === void 0 ? void 0 : entry.size, \"-\").concat(i)\n        }, adaptEventsOfChild(_this2.props, entry, i), {\n          role: \"img\"\n        }), /*#__PURE__*/React.createElement(ScatterSymbol, _extends({\n          option: option,\n          isActive: isActive\n          // eslint-disable-next-line react/no-array-index-key\n          ,\n          key: \"symbol-\".concat(i)\n        }, props)));\n      });\n    }\n  }, {\n    key: \"renderSymbolsWithAnimation\",\n    value: function renderSymbolsWithAnimation() {\n      var _this3 = this;\n      var _this$props2 = this.props,\n        points = _this$props2.points,\n        isAnimationActive = _this$props2.isAnimationActive,\n        animationBegin = _this$props2.animationBegin,\n        animationDuration = _this$props2.animationDuration,\n        animationEasing = _this$props2.animationEasing,\n        animationId = _this$props2.animationId;\n      var prevPoints = this.state.prevPoints;\n      return /*#__PURE__*/React.createElement(Animate, {\n        begin: animationBegin,\n        duration: animationDuration,\n        isActive: isAnimationActive,\n        easing: animationEasing,\n        from: {\n          t: 0\n        },\n        to: {\n          t: 1\n        },\n        key: \"pie-\".concat(animationId),\n        onAnimationEnd: this.handleAnimationEnd,\n        onAnimationStart: this.handleAnimationStart\n      }, function (_ref) {\n        var t = _ref.t;\n        var stepData = points.map(function (entry, index) {\n          var prev = prevPoints && prevPoints[index];\n          if (prev) {\n            var interpolatorCx = interpolateNumber(prev.cx, entry.cx);\n            var interpolatorCy = interpolateNumber(prev.cy, entry.cy);\n            var interpolatorSize = interpolateNumber(prev.size, entry.size);\n            return _objectSpread(_objectSpread({}, entry), {}, {\n              cx: interpolatorCx(t),\n              cy: interpolatorCy(t),\n              size: interpolatorSize(t)\n            });\n          }\n          var interpolator = interpolateNumber(0, entry.size);\n          return _objectSpread(_objectSpread({}, entry), {}, {\n            size: interpolator(t)\n          });\n        });\n        return /*#__PURE__*/React.createElement(Layer, null, _this3.renderSymbolsStatically(stepData));\n      });\n    }\n  }, {\n    key: \"renderSymbols\",\n    value: function renderSymbols() {\n      var _this$props3 = this.props,\n        points = _this$props3.points,\n        isAnimationActive = _this$props3.isAnimationActive;\n      var prevPoints = this.state.prevPoints;\n      if (isAnimationActive && points && points.length && (!prevPoints || !isEqual(prevPoints, points))) {\n        return this.renderSymbolsWithAnimation();\n      }\n      return this.renderSymbolsStatically(points);\n    }\n  }, {\n    key: \"renderErrorBar\",\n    value: function renderErrorBar() {\n      var isAnimationActive = this.props.isAnimationActive;\n      if (isAnimationActive && !this.state.isAnimationFinished) {\n        return null;\n      }\n      var _this$props4 = this.props,\n        points = _this$props4.points,\n        xAxis = _this$props4.xAxis,\n        yAxis = _this$props4.yAxis,\n        children = _this$props4.children;\n      var errorBarItems = findAllByType(children, ErrorBar);\n      if (!errorBarItems) {\n        return null;\n      }\n      return errorBarItems.map(function (item, i) {\n        var _item$props = item.props,\n          direction = _item$props.direction,\n          errorDataKey = _item$props.dataKey;\n        return /*#__PURE__*/React.cloneElement(item, {\n          key: \"\".concat(direction, \"-\").concat(errorDataKey, \"-\").concat(points[i]),\n          data: points,\n          xAxis: xAxis,\n          yAxis: yAxis,\n          layout: direction === 'x' ? 'vertical' : 'horizontal',\n          dataPointFormatter: function dataPointFormatter(dataPoint, dataKey) {\n            return {\n              x: dataPoint.cx,\n              y: dataPoint.cy,\n              value: direction === 'x' ? +dataPoint.node.x : +dataPoint.node.y,\n              errorVal: getValueByDataKey(dataPoint, dataKey)\n            };\n          }\n        });\n      });\n    }\n  }, {\n    key: \"renderLine\",\n    value: function renderLine() {\n      var _this$props5 = this.props,\n        points = _this$props5.points,\n        line = _this$props5.line,\n        lineType = _this$props5.lineType,\n        lineJointType = _this$props5.lineJointType;\n      var scatterProps = filterProps(this.props, false);\n      var customLineProps = filterProps(line, false);\n      var linePoints, lineItem;\n      if (lineType === 'joint') {\n        linePoints = points.map(function (entry) {\n          return {\n            x: entry.cx,\n            y: entry.cy\n          };\n        });\n      } else if (lineType === 'fitting') {\n        var _getLinearRegression = getLinearRegression(points),\n          xmin = _getLinearRegression.xmin,\n          xmax = _getLinearRegression.xmax,\n          a = _getLinearRegression.a,\n          b = _getLinearRegression.b;\n        var linearExp = function linearExp(x) {\n          return a * x + b;\n        };\n        linePoints = [{\n          x: xmin,\n          y: linearExp(xmin)\n        }, {\n          x: xmax,\n          y: linearExp(xmax)\n        }];\n      }\n      var lineProps = _objectSpread(_objectSpread(_objectSpread({}, scatterProps), {}, {\n        fill: 'none',\n        stroke: scatterProps && scatterProps.fill\n      }, customLineProps), {}, {\n        points: linePoints\n      });\n      if ( /*#__PURE__*/React.isValidElement(line)) {\n        lineItem = /*#__PURE__*/React.cloneElement(line, lineProps);\n      } else if (isFunction(line)) {\n        lineItem = line(lineProps);\n      } else {\n        lineItem = /*#__PURE__*/React.createElement(Curve, _extends({}, lineProps, {\n          type: lineJointType\n        }));\n      }\n      return /*#__PURE__*/React.createElement(Layer, {\n        className: \"recharts-scatter-line\",\n        key: \"recharts-scatter-line\"\n      }, lineItem);\n    }\n  }, {\n    key: \"render\",\n    value: function render() {\n      var _this$props6 = this.props,\n        hide = _this$props6.hide,\n        points = _this$props6.points,\n        line = _this$props6.line,\n        className = _this$props6.className,\n        xAxis = _this$props6.xAxis,\n        yAxis = _this$props6.yAxis,\n        left = _this$props6.left,\n        top = _this$props6.top,\n        width = _this$props6.width,\n        height = _this$props6.height,\n        id = _this$props6.id,\n        isAnimationActive = _this$props6.isAnimationActive;\n      if (hide || !points || !points.length) {\n        return null;\n      }\n      var isAnimationFinished = this.state.isAnimationFinished;\n      var layerClass = clsx('recharts-scatter', className);\n      var needClipX = xAxis && xAxis.allowDataOverflow;\n      var needClipY = yAxis && yAxis.allowDataOverflow;\n      var needClip = needClipX || needClipY;\n      var clipPathId = isNil(id) ? this.id : id;\n      return /*#__PURE__*/React.createElement(Layer, {\n        className: layerClass,\n        clipPath: needClip ? \"url(#clipPath-\".concat(clipPathId, \")\") : null\n      }, needClipX || needClipY ? /*#__PURE__*/React.createElement(\"defs\", null, /*#__PURE__*/React.createElement(\"clipPath\", {\n        id: \"clipPath-\".concat(clipPathId)\n      }, /*#__PURE__*/React.createElement(\"rect\", {\n        x: needClipX ? left : left - width / 2,\n        y: needClipY ? top : top - height / 2,\n        width: needClipX ? width : width * 2,\n        height: needClipY ? height : height * 2\n      }))) : null, line && this.renderLine(), this.renderErrorBar(), /*#__PURE__*/React.createElement(Layer, {\n        key: \"recharts-scatter-symbols\"\n      }, this.renderSymbols()), (!isAnimationActive || isAnimationFinished) && LabelList.renderCallByParent(this.props, points));\n    }\n  }], [{\n    key: \"getDerivedStateFromProps\",\n    value: function getDerivedStateFromProps(nextProps, prevState) {\n      if (nextProps.animationId !== prevState.prevAnimationId) {\n        return {\n          prevAnimationId: nextProps.animationId,\n          curPoints: nextProps.points,\n          prevPoints: prevState.curPoints\n        };\n      }\n      if (nextProps.points !== prevState.curPoints) {\n        return {\n          curPoints: nextProps.points\n        };\n      }\n      return null;\n    }\n  }]);\n}(PureComponent);\n_Scatter = Scatter;\n_defineProperty(Scatter, \"displayName\", 'Scatter');\n_defineProperty(Scatter, \"defaultProps\", {\n  xAxisId: 0,\n  yAxisId: 0,\n  zAxisId: 0,\n  legendType: 'circle',\n  lineType: 'joint',\n  lineJointType: 'linear',\n  data: [],\n  shape: 'circle',\n  hide: false,\n  isAnimationActive: !Global.isSsr,\n  animationBegin: 0,\n  animationDuration: 400,\n  animationEasing: 'linear'\n});\n/**\n * Compose the data of each group\n * @param  {Object} xAxis   The configuration of x-axis\n * @param  {Object} yAxis   The configuration of y-axis\n * @param  {String} dataKey The unique key of a group\n * @return {Array}  Composed data\n */\n_defineProperty(Scatter, \"getComposedData\", function (_ref2) {\n  var xAxis = _ref2.xAxis,\n    yAxis = _ref2.yAxis,\n    zAxis = _ref2.zAxis,\n    item = _ref2.item,\n    displayedData = _ref2.displayedData,\n    xAxisTicks = _ref2.xAxisTicks,\n    yAxisTicks = _ref2.yAxisTicks,\n    offset = _ref2.offset;\n  var tooltipType = item.props.tooltipType;\n  var cells = findAllByType(item.props.children, Cell);\n  var xAxisDataKey = isNil(xAxis.dataKey) ? item.props.dataKey : xAxis.dataKey;\n  var yAxisDataKey = isNil(yAxis.dataKey) ? item.props.dataKey : yAxis.dataKey;\n  var zAxisDataKey = zAxis && zAxis.dataKey;\n  var defaultRangeZ = zAxis ? zAxis.range : ZAxis.defaultProps.range;\n  var defaultZ = defaultRangeZ && defaultRangeZ[0];\n  var xBandSize = xAxis.scale.bandwidth ? xAxis.scale.bandwidth() : 0;\n  var yBandSize = yAxis.scale.bandwidth ? yAxis.scale.bandwidth() : 0;\n  var points = displayedData.map(function (entry, index) {\n    var x = getValueByDataKey(entry, xAxisDataKey);\n    var y = getValueByDataKey(entry, yAxisDataKey);\n    var z = !isNil(zAxisDataKey) && getValueByDataKey(entry, zAxisDataKey) || '-';\n    var tooltipPayload = [{\n      name: isNil(xAxis.dataKey) ? item.props.name : xAxis.name || xAxis.dataKey,\n      unit: xAxis.unit || '',\n      value: x,\n      payload: entry,\n      dataKey: xAxisDataKey,\n      type: tooltipType\n    }, {\n      name: isNil(yAxis.dataKey) ? item.props.name : yAxis.name || yAxis.dataKey,\n      unit: yAxis.unit || '',\n      value: y,\n      payload: entry,\n      dataKey: yAxisDataKey,\n      type: tooltipType\n    }];\n    if (z !== '-') {\n      tooltipPayload.push({\n        name: zAxis.name || zAxis.dataKey,\n        unit: zAxis.unit || '',\n        value: z,\n        payload: entry,\n        dataKey: zAxisDataKey,\n        type: tooltipType\n      });\n    }\n    var cx = getCateCoordinateOfLine({\n      axis: xAxis,\n      ticks: xAxisTicks,\n      bandSize: xBandSize,\n      entry: entry,\n      index: index,\n      dataKey: xAxisDataKey\n    });\n    var cy = getCateCoordinateOfLine({\n      axis: yAxis,\n      ticks: yAxisTicks,\n      bandSize: yBandSize,\n      entry: entry,\n      index: index,\n      dataKey: yAxisDataKey\n    });\n    var size = z !== '-' ? zAxis.scale(z) : defaultZ;\n    var radius = Math.sqrt(Math.max(size, 0) / Math.PI);\n    return _objectSpread(_objectSpread({}, entry), {}, {\n      cx: cx,\n      cy: cy,\n      x: cx - radius,\n      y: cy - radius,\n      xAxis: xAxis,\n      yAxis: yAxis,\n      zAxis: zAxis,\n      width: 2 * radius,\n      height: 2 * radius,\n      size: size,\n      node: {\n        x: x,\n        y: y,\n        z: z\n      },\n      tooltipPayload: tooltipPayload,\n      tooltipPosition: {\n        x: cx,\n        y: cy\n      },\n      payload: entry\n    }, cells && cells[index] && cells[index].props);\n  });\n  return _objectSpread({\n    points: points\n  }, offset);\n});"], "mappings": "AAAA,IAAIA,QAAQ;AACZ,SAASC,OAAOA,CAACC,CAAC,EAAE;EAAE,yBAAyB;;EAAE,OAAOD,OAAO,GAAG,UAAU,IAAI,OAAOE,MAAM,IAAI,QAAQ,IAAI,OAAOA,MAAM,CAACC,QAAQ,GAAG,UAAUF,CAAC,EAAE;IAAE,OAAO,OAAOA,CAAC;EAAE,CAAC,GAAG,UAAUA,CAAC,EAAE;IAAE,OAAOA,CAAC,IAAI,UAAU,IAAI,OAAOC,MAAM,IAAID,CAAC,CAACG,WAAW,KAAKF,MAAM,IAAID,CAAC,KAAKC,MAAM,CAACG,SAAS,GAAG,QAAQ,GAAG,OAAOJ,CAAC;EAAE,CAAC,EAAED,OAAO,CAACC,CAAC,CAAC;AAAE;AAC7T,SAASK,QAAQA,CAAA,EAAG;EAAEA,QAAQ,GAAGC,MAAM,CAACC,MAAM,GAAGD,MAAM,CAACC,MAAM,CAACC,IAAI,CAAC,CAAC,GAAG,UAAUC,MAAM,EAAE;IAAE,KAAK,IAAIC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGC,SAAS,CAACC,MAAM,EAAEF,CAAC,EAAE,EAAE;MAAE,IAAIG,MAAM,GAAGF,SAAS,CAACD,CAAC,CAAC;MAAE,KAAK,IAAII,GAAG,IAAID,MAAM,EAAE;QAAE,IAAIP,MAAM,CAACF,SAAS,CAACW,cAAc,CAACC,IAAI,CAACH,MAAM,EAAEC,GAAG,CAAC,EAAE;UAAEL,MAAM,CAACK,GAAG,CAAC,GAAGD,MAAM,CAACC,GAAG,CAAC;QAAE;MAAE;IAAE;IAAE,OAAOL,MAAM;EAAE,CAAC;EAAE,OAAOJ,QAAQ,CAACY,KAAK,CAAC,IAAI,EAAEN,SAAS,CAAC;AAAE;AAClV,SAASO,OAAOA,CAACC,CAAC,EAAEC,CAAC,EAAE;EAAE,IAAIC,CAAC,GAAGf,MAAM,CAACgB,IAAI,CAACH,CAAC,CAAC;EAAE,IAAIb,MAAM,CAACiB,qBAAqB,EAAE;IAAE,IAAIvB,CAAC,GAAGM,MAAM,CAACiB,qBAAqB,CAACJ,CAAC,CAAC;IAAEC,CAAC,KAAKpB,CAAC,GAAGA,CAAC,CAACwB,MAAM,CAAC,UAAUJ,CAAC,EAAE;MAAE,OAAOd,MAAM,CAACmB,wBAAwB,CAACN,CAAC,EAAEC,CAAC,CAAC,CAACM,UAAU;IAAE,CAAC,CAAC,CAAC,EAAEL,CAAC,CAACM,IAAI,CAACV,KAAK,CAACI,CAAC,EAAErB,CAAC,CAAC;EAAE;EAAE,OAAOqB,CAAC;AAAE;AAC9P,SAASO,aAAaA,CAACT,CAAC,EAAE;EAAE,KAAK,IAAIC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGT,SAAS,CAACC,MAAM,EAAEQ,CAAC,EAAE,EAAE;IAAE,IAAIC,CAAC,GAAG,IAAI,IAAIV,SAAS,CAACS,CAAC,CAAC,GAAGT,SAAS,CAACS,CAAC,CAAC,GAAG,CAAC,CAAC;IAAEA,CAAC,GAAG,CAAC,GAAGF,OAAO,CAACZ,MAAM,CAACe,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAACQ,OAAO,CAAC,UAAUT,CAAC,EAAE;MAAEU,eAAe,CAACX,CAAC,EAAEC,CAAC,EAAEC,CAAC,CAACD,CAAC,CAAC,CAAC;IAAE,CAAC,CAAC,GAAGd,MAAM,CAACyB,yBAAyB,GAAGzB,MAAM,CAAC0B,gBAAgB,CAACb,CAAC,EAAEb,MAAM,CAACyB,yBAAyB,CAACV,CAAC,CAAC,CAAC,GAAGH,OAAO,CAACZ,MAAM,CAACe,CAAC,CAAC,CAAC,CAACQ,OAAO,CAAC,UAAUT,CAAC,EAAE;MAAEd,MAAM,CAAC2B,cAAc,CAACd,CAAC,EAAEC,CAAC,EAAEd,MAAM,CAACmB,wBAAwB,CAACJ,CAAC,EAAED,CAAC,CAAC,CAAC;IAAE,CAAC,CAAC;EAAE;EAAE,OAAOD,CAAC;AAAE;AACtb,SAASe,eAAeA,CAACC,QAAQ,EAAEC,WAAW,EAAE;EAAE,IAAI,EAAED,QAAQ,YAAYC,WAAW,CAAC,EAAE;IAAE,MAAM,IAAIC,SAAS,CAAC,mCAAmC,CAAC;EAAE;AAAE;AACxJ,SAASC,iBAAiBA,CAAC7B,MAAM,EAAE8B,KAAK,EAAE;EAAE,KAAK,IAAI7B,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG6B,KAAK,CAAC3B,MAAM,EAAEF,CAAC,EAAE,EAAE;IAAE,IAAI8B,UAAU,GAAGD,KAAK,CAAC7B,CAAC,CAAC;IAAE8B,UAAU,CAACd,UAAU,GAAGc,UAAU,CAACd,UAAU,IAAI,KAAK;IAAEc,UAAU,CAACC,YAAY,GAAG,IAAI;IAAE,IAAI,OAAO,IAAID,UAAU,EAAEA,UAAU,CAACE,QAAQ,GAAG,IAAI;IAAEpC,MAAM,CAAC2B,cAAc,CAACxB,MAAM,EAAEkC,cAAc,CAACH,UAAU,CAAC1B,GAAG,CAAC,EAAE0B,UAAU,CAAC;EAAE;AAAE;AAC5U,SAASI,YAAYA,CAACR,WAAW,EAAES,UAAU,EAAEC,WAAW,EAAE;EAAE,IAAID,UAAU,EAAEP,iBAAiB,CAACF,WAAW,CAAChC,SAAS,EAAEyC,UAAU,CAAC;EAAE,IAAIC,WAAW,EAAER,iBAAiB,CAACF,WAAW,EAAEU,WAAW,CAAC;EAAExC,MAAM,CAAC2B,cAAc,CAACG,WAAW,EAAE,WAAW,EAAE;IAAEM,QAAQ,EAAE;EAAM,CAAC,CAAC;EAAE,OAAON,WAAW;AAAE;AAC5R,SAASW,UAAUA,CAAC1B,CAAC,EAAErB,CAAC,EAAEmB,CAAC,EAAE;EAAE,OAAOnB,CAAC,GAAGgD,eAAe,CAAChD,CAAC,CAAC,EAAEiD,0BAA0B,CAAC5B,CAAC,EAAE6B,yBAAyB,CAAC,CAAC,GAAGC,OAAO,CAACC,SAAS,CAACpD,CAAC,EAAEmB,CAAC,IAAI,EAAE,EAAE6B,eAAe,CAAC3B,CAAC,CAAC,CAAClB,WAAW,CAAC,GAAGH,CAAC,CAACiB,KAAK,CAACI,CAAC,EAAEF,CAAC,CAAC,CAAC;AAAE;AAC1M,SAAS8B,0BAA0BA,CAACI,IAAI,EAAErC,IAAI,EAAE;EAAE,IAAIA,IAAI,KAAKjB,OAAO,CAACiB,IAAI,CAAC,KAAK,QAAQ,IAAI,OAAOA,IAAI,KAAK,UAAU,CAAC,EAAE;IAAE,OAAOA,IAAI;EAAE,CAAC,MAAM,IAAIA,IAAI,KAAK,KAAK,CAAC,EAAE;IAAE,MAAM,IAAIqB,SAAS,CAAC,0DAA0D,CAAC;EAAE;EAAE,OAAOiB,sBAAsB,CAACD,IAAI,CAAC;AAAE;AAC/R,SAASC,sBAAsBA,CAACD,IAAI,EAAE;EAAE,IAAIA,IAAI,KAAK,KAAK,CAAC,EAAE;IAAE,MAAM,IAAIE,cAAc,CAAC,2DAA2D,CAAC;EAAE;EAAE,OAAOF,IAAI;AAAE;AACrK,SAASH,yBAAyBA,CAAA,EAAG;EAAE,IAAI;IAAE,IAAI7B,CAAC,GAAG,CAACmC,OAAO,CAACpD,SAAS,CAACqD,OAAO,CAACzC,IAAI,CAACmC,OAAO,CAACC,SAAS,CAACI,OAAO,EAAE,EAAE,EAAE,YAAY,CAAC,CAAC,CAAC,CAAC;EAAE,CAAC,CAAC,OAAOnC,CAAC,EAAE,CAAC;EAAE,OAAO,CAAC6B,yBAAyB,GAAG,SAASA,yBAAyBA,CAAA,EAAG;IAAE,OAAO,CAAC,CAAC7B,CAAC;EAAE,CAAC,EAAE,CAAC;AAAE;AAClP,SAAS2B,eAAeA,CAAChD,CAAC,EAAE;EAAEgD,eAAe,GAAG1C,MAAM,CAACoD,cAAc,GAAGpD,MAAM,CAACqD,cAAc,CAACnD,IAAI,CAAC,CAAC,GAAG,SAASwC,eAAeA,CAAChD,CAAC,EAAE;IAAE,OAAOA,CAAC,CAAC4D,SAAS,IAAItD,MAAM,CAACqD,cAAc,CAAC3D,CAAC,CAAC;EAAE,CAAC;EAAE,OAAOgD,eAAe,CAAChD,CAAC,CAAC;AAAE;AACnN,SAAS6D,SAASA,CAACC,QAAQ,EAAEC,UAAU,EAAE;EAAE,IAAI,OAAOA,UAAU,KAAK,UAAU,IAAIA,UAAU,KAAK,IAAI,EAAE;IAAE,MAAM,IAAI1B,SAAS,CAAC,oDAAoD,CAAC;EAAE;EAAEyB,QAAQ,CAAC1D,SAAS,GAAGE,MAAM,CAAC0D,MAAM,CAACD,UAAU,IAAIA,UAAU,CAAC3D,SAAS,EAAE;IAAED,WAAW,EAAE;MAAE8D,KAAK,EAAEH,QAAQ;MAAEpB,QAAQ,EAAE,IAAI;MAAED,YAAY,EAAE;IAAK;EAAE,CAAC,CAAC;EAAEnC,MAAM,CAAC2B,cAAc,CAAC6B,QAAQ,EAAE,WAAW,EAAE;IAAEpB,QAAQ,EAAE;EAAM,CAAC,CAAC;EAAE,IAAIqB,UAAU,EAAEG,eAAe,CAACJ,QAAQ,EAAEC,UAAU,CAAC;AAAE;AACnc,SAASG,eAAeA,CAAClE,CAAC,EAAEmE,CAAC,EAAE;EAAED,eAAe,GAAG5D,MAAM,CAACoD,cAAc,GAAGpD,MAAM,CAACoD,cAAc,CAAClD,IAAI,CAAC,CAAC,GAAG,SAAS0D,eAAeA,CAAClE,CAAC,EAAEmE,CAAC,EAAE;IAAEnE,CAAC,CAAC4D,SAAS,GAAGO,CAAC;IAAE,OAAOnE,CAAC;EAAE,CAAC;EAAE,OAAOkE,eAAe,CAAClE,CAAC,EAAEmE,CAAC,CAAC;AAAE;AACvM,SAASrC,eAAeA,CAACsC,GAAG,EAAEtD,GAAG,EAAEmD,KAAK,EAAE;EAAEnD,GAAG,GAAG6B,cAAc,CAAC7B,GAAG,CAAC;EAAE,IAAIA,GAAG,IAAIsD,GAAG,EAAE;IAAE9D,MAAM,CAAC2B,cAAc,CAACmC,GAAG,EAAEtD,GAAG,EAAE;MAAEmD,KAAK,EAAEA,KAAK;MAAEvC,UAAU,EAAE,IAAI;MAAEe,YAAY,EAAE,IAAI;MAAEC,QAAQ,EAAE;IAAK,CAAC,CAAC;EAAE,CAAC,MAAM;IAAE0B,GAAG,CAACtD,GAAG,CAAC,GAAGmD,KAAK;EAAE;EAAE,OAAOG,GAAG;AAAE;AAC3O,SAASzB,cAAcA,CAACtB,CAAC,EAAE;EAAE,IAAIX,CAAC,GAAG2D,YAAY,CAAChD,CAAC,EAAE,QAAQ,CAAC;EAAE,OAAO,QAAQ,IAAItB,OAAO,CAACW,CAAC,CAAC,GAAGA,CAAC,GAAGA,CAAC,GAAG,EAAE;AAAE;AAC5G,SAAS2D,YAAYA,CAAChD,CAAC,EAAED,CAAC,EAAE;EAAE,IAAI,QAAQ,IAAIrB,OAAO,CAACsB,CAAC,CAAC,IAAI,CAACA,CAAC,EAAE,OAAOA,CAAC;EAAE,IAAIF,CAAC,GAAGE,CAAC,CAACpB,MAAM,CAACqE,WAAW,CAAC;EAAE,IAAI,KAAK,CAAC,KAAKnD,CAAC,EAAE;IAAE,IAAIT,CAAC,GAAGS,CAAC,CAACH,IAAI,CAACK,CAAC,EAAED,CAAC,IAAI,SAAS,CAAC;IAAE,IAAI,QAAQ,IAAIrB,OAAO,CAACW,CAAC,CAAC,EAAE,OAAOA,CAAC;IAAE,MAAM,IAAI2B,SAAS,CAAC,8CAA8C,CAAC;EAAE;EAAE,OAAO,CAAC,QAAQ,KAAKjB,CAAC,GAAGmD,MAAM,GAAGC,MAAM,EAAEnD,CAAC,CAAC;AAAE;AAC3T;AACA;AACA;AACA,OAAOoD,KAAK,IAAIC,aAAa,QAAQ,OAAO;AAC5C,OAAOC,OAAO,MAAM,cAAc;AAClC,OAAOC,KAAK,MAAM,cAAc;AAChC,OAAOC,OAAO,MAAM,gBAAgB;AACpC,OAAOC,UAAU,MAAM,mBAAmB;AAC1C,OAAOC,IAAI,MAAM,MAAM;AACvB,SAASC,KAAK,QAAQ,oBAAoB;AAC1C,SAASC,SAAS,QAAQ,wBAAwB;AAClD,SAASC,aAAa,EAAEC,WAAW,QAAQ,oBAAoB;AAC/D,SAASC,MAAM,QAAQ,gBAAgB;AACvC,SAASC,KAAK,QAAQ,SAAS;AAC/B,SAASC,KAAK,QAAQ,gBAAgB;AACtC,SAASC,QAAQ,QAAQ,YAAY;AACrC,SAASC,IAAI,QAAQ,mBAAmB;AACxC,SAASC,QAAQ,EAAEC,iBAAiB,EAAEC,mBAAmB,QAAQ,mBAAmB;AACpF,SAASC,iBAAiB,EAAEC,uBAAuB,QAAQ,oBAAoB;AAC/E,SAASC,kBAAkB,QAAQ,eAAe;AAClD,SAASC,aAAa,QAAQ,sBAAsB;AACpD,OAAO,IAAIC,OAAO,GAAG,aAAa,UAAUC,cAAc,EAAE;EAC1D,SAASD,OAAOA,CAAA,EAAG;IACjB,IAAIE,KAAK;IACThE,eAAe,CAAC,IAAI,EAAE8D,OAAO,CAAC;IAC9B,KAAK,IAAIG,IAAI,GAAGxF,SAAS,CAACC,MAAM,EAAEwF,IAAI,GAAG,IAAIC,KAAK,CAACF,IAAI,CAAC,EAAEG,IAAI,GAAG,CAAC,EAAEA,IAAI,GAAGH,IAAI,EAAEG,IAAI,EAAE,EAAE;MACvFF,IAAI,CAACE,IAAI,CAAC,GAAG3F,SAAS,CAAC2F,IAAI,CAAC;IAC9B;IACAJ,KAAK,GAAGnD,UAAU,CAAC,IAAI,EAAEiD,OAAO,EAAE,EAAE,CAACO,MAAM,CAACH,IAAI,CAAC,CAAC;IAClDtE,eAAe,CAACoE,KAAK,EAAE,OAAO,EAAE;MAC9BM,mBAAmB,EAAE;IACvB,CAAC,CAAC;IACF1E,eAAe,CAACoE,KAAK,EAAE,oBAAoB,EAAE,YAAY;MACvDA,KAAK,CAACO,QAAQ,CAAC;QACbD,mBAAmB,EAAE;MACvB,CAAC,CAAC;IACJ,CAAC,CAAC;IACF1E,eAAe,CAACoE,KAAK,EAAE,sBAAsB,EAAE,YAAY;MACzDA,KAAK,CAACO,QAAQ,CAAC;QACbD,mBAAmB,EAAE;MACvB,CAAC,CAAC;IACJ,CAAC,CAAC;IACF1E,eAAe,CAACoE,KAAK,EAAE,IAAI,EAAET,QAAQ,CAAC,mBAAmB,CAAC,CAAC;IAC3D,OAAOS,KAAK;EACd;EACArC,SAAS,CAACmC,OAAO,EAAEC,cAAc,CAAC;EAClC,OAAOrD,YAAY,CAACoD,OAAO,EAAE,CAAC;IAC5BlF,GAAG,EAAE,yBAAyB;IAC9BmD,KAAK,EAAE,SAASyC,uBAAuBA,CAACC,MAAM,EAAE;MAC9C,IAAIC,MAAM,GAAG,IAAI;MACjB,IAAIC,WAAW,GAAG,IAAI,CAACtE,KAAK;QAC1BuE,KAAK,GAAGD,WAAW,CAACC,KAAK;QACzBC,WAAW,GAAGF,WAAW,CAACE,WAAW;QACrCC,WAAW,GAAGH,WAAW,CAACG,WAAW;MACvC,IAAIC,SAAS,GAAG9B,WAAW,CAAC,IAAI,CAAC5C,KAAK,EAAE,KAAK,CAAC;MAC9C,OAAOoE,MAAM,CAACO,GAAG,CAAC,UAAUC,KAAK,EAAEzG,CAAC,EAAE;QACpC,IAAI0G,QAAQ,GAAGJ,WAAW,KAAKtG,CAAC;QAChC,IAAI2G,MAAM,GAAGD,QAAQ,GAAGL,WAAW,GAAGD,KAAK;QAC3C,IAAIvE,KAAK,GAAGX,aAAa,CAACA,aAAa,CAAC,CAAC,CAAC,EAAEqF,SAAS,CAAC,EAAEE,KAAK,CAAC;QAC9D,OAAO,aAAa1C,KAAK,CAAC6C,aAAa,CAACtC,KAAK,EAAE3E,QAAQ,CAAC;UACtDkH,SAAS,EAAE;UACX;UAAA;;UAEAzG,GAAG,EAAE,SAAS,CAACyF,MAAM,CAACY,KAAK,KAAK,IAAI,IAAIA,KAAK,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,KAAK,CAACK,EAAE,EAAE,GAAG,CAAC,CAACjB,MAAM,CAACY,KAAK,KAAK,IAAI,IAAIA,KAAK,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,KAAK,CAACM,EAAE,EAAE,GAAG,CAAC,CAAClB,MAAM,CAACY,KAAK,KAAK,IAAI,IAAIA,KAAK,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,KAAK,CAACO,IAAI,EAAE,GAAG,CAAC,CAACnB,MAAM,CAAC7F,CAAC;QACvO,CAAC,EAAEoF,kBAAkB,CAACc,MAAM,CAACrE,KAAK,EAAE4E,KAAK,EAAEzG,CAAC,CAAC,EAAE;UAC7CiH,IAAI,EAAE;QACR,CAAC,CAAC,EAAE,aAAalD,KAAK,CAAC6C,aAAa,CAACvB,aAAa,EAAE1F,QAAQ,CAAC;UAC3DgH,MAAM,EAAEA,MAAM;UACdD,QAAQ,EAAEA;UACV;UAAA;;UAEAtG,GAAG,EAAE,SAAS,CAACyF,MAAM,CAAC7F,CAAC;QACzB,CAAC,EAAE6B,KAAK,CAAC,CAAC,CAAC;MACb,CAAC,CAAC;IACJ;EACF,CAAC,EAAE;IACDzB,GAAG,EAAE,4BAA4B;IACjCmD,KAAK,EAAE,SAAS2D,0BAA0BA,CAAA,EAAG;MAC3C,IAAIC,MAAM,GAAG,IAAI;MACjB,IAAIC,YAAY,GAAG,IAAI,CAACvF,KAAK;QAC3BoE,MAAM,GAAGmB,YAAY,CAACnB,MAAM;QAC5BoB,iBAAiB,GAAGD,YAAY,CAACC,iBAAiB;QAClDC,cAAc,GAAGF,YAAY,CAACE,cAAc;QAC5CC,iBAAiB,GAAGH,YAAY,CAACG,iBAAiB;QAClDC,eAAe,GAAGJ,YAAY,CAACI,eAAe;QAC9CC,WAAW,GAAGL,YAAY,CAACK,WAAW;MACxC,IAAIC,UAAU,GAAG,IAAI,CAACC,KAAK,CAACD,UAAU;MACtC,OAAO,aAAa3D,KAAK,CAAC6C,aAAa,CAAC3C,OAAO,EAAE;QAC/C2D,KAAK,EAAEN,cAAc;QACrBO,QAAQ,EAAEN,iBAAiB;QAC3Bb,QAAQ,EAAEW,iBAAiB;QAC3BS,MAAM,EAAEN,eAAe;QACvBO,IAAI,EAAE;UACJpH,CAAC,EAAE;QACL,CAAC;QACDqH,EAAE,EAAE;UACFrH,CAAC,EAAE;QACL,CAAC;QACDP,GAAG,EAAE,MAAM,CAACyF,MAAM,CAAC4B,WAAW,CAAC;QAC/BQ,cAAc,EAAE,IAAI,CAACC,kBAAkB;QACvCC,gBAAgB,EAAE,IAAI,CAACC;MACzB,CAAC,EAAE,UAAUC,IAAI,EAAE;QACjB,IAAI1H,CAAC,GAAG0H,IAAI,CAAC1H,CAAC;QACd,IAAI2H,QAAQ,GAAGrC,MAAM,CAACO,GAAG,CAAC,UAAUC,KAAK,EAAE8B,KAAK,EAAE;UAChD,IAAIC,IAAI,GAAGd,UAAU,IAAIA,UAAU,CAACa,KAAK,CAAC;UAC1C,IAAIC,IAAI,EAAE;YACR,IAAIC,cAAc,GAAGzD,iBAAiB,CAACwD,IAAI,CAAC1B,EAAE,EAAEL,KAAK,CAACK,EAAE,CAAC;YACzD,IAAI4B,cAAc,GAAG1D,iBAAiB,CAACwD,IAAI,CAACzB,EAAE,EAAEN,KAAK,CAACM,EAAE,CAAC;YACzD,IAAI4B,gBAAgB,GAAG3D,iBAAiB,CAACwD,IAAI,CAACxB,IAAI,EAAEP,KAAK,CAACO,IAAI,CAAC;YAC/D,OAAO9F,aAAa,CAACA,aAAa,CAAC,CAAC,CAAC,EAAEuF,KAAK,CAAC,EAAE,CAAC,CAAC,EAAE;cACjDK,EAAE,EAAE2B,cAAc,CAAC9H,CAAC,CAAC;cACrBoG,EAAE,EAAE2B,cAAc,CAAC/H,CAAC,CAAC;cACrBqG,IAAI,EAAE2B,gBAAgB,CAAChI,CAAC;YAC1B,CAAC,CAAC;UACJ;UACA,IAAIiI,YAAY,GAAG5D,iBAAiB,CAAC,CAAC,EAAEyB,KAAK,CAACO,IAAI,CAAC;UACnD,OAAO9F,aAAa,CAACA,aAAa,CAAC,CAAC,CAAC,EAAEuF,KAAK,CAAC,EAAE,CAAC,CAAC,EAAE;YACjDO,IAAI,EAAE4B,YAAY,CAACjI,CAAC;UACtB,CAAC,CAAC;QACJ,CAAC,CAAC;QACF,OAAO,aAAaoD,KAAK,CAAC6C,aAAa,CAACtC,KAAK,EAAE,IAAI,EAAE6C,MAAM,CAACnB,uBAAuB,CAACsC,QAAQ,CAAC,CAAC;MAChG,CAAC,CAAC;IACJ;EACF,CAAC,EAAE;IACDlI,GAAG,EAAE,eAAe;IACpBmD,KAAK,EAAE,SAASsF,aAAaA,CAAA,EAAG;MAC9B,IAAIC,YAAY,GAAG,IAAI,CAACjH,KAAK;QAC3BoE,MAAM,GAAG6C,YAAY,CAAC7C,MAAM;QAC5BoB,iBAAiB,GAAGyB,YAAY,CAACzB,iBAAiB;MACpD,IAAIK,UAAU,GAAG,IAAI,CAACC,KAAK,CAACD,UAAU;MACtC,IAAIL,iBAAiB,IAAIpB,MAAM,IAAIA,MAAM,CAAC/F,MAAM,KAAK,CAACwH,UAAU,IAAI,CAACvD,OAAO,CAACuD,UAAU,EAAEzB,MAAM,CAAC,CAAC,EAAE;QACjG,OAAO,IAAI,CAACiB,0BAA0B,CAAC,CAAC;MAC1C;MACA,OAAO,IAAI,CAAClB,uBAAuB,CAACC,MAAM,CAAC;IAC7C;EACF,CAAC,EAAE;IACD7F,GAAG,EAAE,gBAAgB;IACrBmD,KAAK,EAAE,SAASwF,cAAcA,CAAA,EAAG;MAC/B,IAAI1B,iBAAiB,GAAG,IAAI,CAACxF,KAAK,CAACwF,iBAAiB;MACpD,IAAIA,iBAAiB,IAAI,CAAC,IAAI,CAACM,KAAK,CAAC7B,mBAAmB,EAAE;QACxD,OAAO,IAAI;MACb;MACA,IAAIkD,YAAY,GAAG,IAAI,CAACnH,KAAK;QAC3BoE,MAAM,GAAG+C,YAAY,CAAC/C,MAAM;QAC5BgD,KAAK,GAAGD,YAAY,CAACC,KAAK;QAC1BC,KAAK,GAAGF,YAAY,CAACE,KAAK;QAC1BC,QAAQ,GAAGH,YAAY,CAACG,QAAQ;MAClC,IAAIC,aAAa,GAAG5E,aAAa,CAAC2E,QAAQ,EAAEtE,QAAQ,CAAC;MACrD,IAAI,CAACuE,aAAa,EAAE;QAClB,OAAO,IAAI;MACb;MACA,OAAOA,aAAa,CAAC5C,GAAG,CAAC,UAAU6C,IAAI,EAAErJ,CAAC,EAAE;QAC1C,IAAIsJ,WAAW,GAAGD,IAAI,CAACxH,KAAK;UAC1B0H,SAAS,GAAGD,WAAW,CAACC,SAAS;UACjCC,YAAY,GAAGF,WAAW,CAACG,OAAO;QACpC,OAAO,aAAa1F,KAAK,CAAC2F,YAAY,CAACL,IAAI,EAAE;UAC3CjJ,GAAG,EAAE,EAAE,CAACyF,MAAM,CAAC0D,SAAS,EAAE,GAAG,CAAC,CAAC1D,MAAM,CAAC2D,YAAY,EAAE,GAAG,CAAC,CAAC3D,MAAM,CAACI,MAAM,CAACjG,CAAC,CAAC,CAAC;UAC1E2J,IAAI,EAAE1D,MAAM;UACZgD,KAAK,EAAEA,KAAK;UACZC,KAAK,EAAEA,KAAK;UACZU,MAAM,EAAEL,SAAS,KAAK,GAAG,GAAG,UAAU,GAAG,YAAY;UACrDM,kBAAkB,EAAE,SAASA,kBAAkBA,CAACC,SAAS,EAAEL,OAAO,EAAE;YAClE,OAAO;cACLM,CAAC,EAAED,SAAS,CAAChD,EAAE;cACfkD,CAAC,EAAEF,SAAS,CAAC/C,EAAE;cACfxD,KAAK,EAAEgG,SAAS,KAAK,GAAG,GAAG,CAACO,SAAS,CAACG,IAAI,CAACF,CAAC,GAAG,CAACD,SAAS,CAACG,IAAI,CAACD,CAAC;cAChEE,QAAQ,EAAEhF,iBAAiB,CAAC4E,SAAS,EAAEL,OAAO;YAChD,CAAC;UACH;QACF,CAAC,CAAC;MACJ,CAAC,CAAC;IACJ;EACF,CAAC,EAAE;IACDrJ,GAAG,EAAE,YAAY;IACjBmD,KAAK,EAAE,SAAS4G,UAAUA,CAAA,EAAG;MAC3B,IAAIC,YAAY,GAAG,IAAI,CAACvI,KAAK;QAC3BoE,MAAM,GAAGmE,YAAY,CAACnE,MAAM;QAC5BoE,IAAI,GAAGD,YAAY,CAACC,IAAI;QACxBC,QAAQ,GAAGF,YAAY,CAACE,QAAQ;QAChCC,aAAa,GAAGH,YAAY,CAACG,aAAa;MAC5C,IAAIC,YAAY,GAAG/F,WAAW,CAAC,IAAI,CAAC5C,KAAK,EAAE,KAAK,CAAC;MACjD,IAAI4I,eAAe,GAAGhG,WAAW,CAAC4F,IAAI,EAAE,KAAK,CAAC;MAC9C,IAAIK,UAAU,EAAEC,QAAQ;MACxB,IAAIL,QAAQ,KAAK,OAAO,EAAE;QACxBI,UAAU,GAAGzE,MAAM,CAACO,GAAG,CAAC,UAAUC,KAAK,EAAE;UACvC,OAAO;YACLsD,CAAC,EAAEtD,KAAK,CAACK,EAAE;YACXkD,CAAC,EAAEvD,KAAK,CAACM;UACX,CAAC;QACH,CAAC,CAAC;MACJ,CAAC,MAAM,IAAIuD,QAAQ,KAAK,SAAS,EAAE;QACjC,IAAIM,oBAAoB,GAAG3F,mBAAmB,CAACgB,MAAM,CAAC;UACpD4E,IAAI,GAAGD,oBAAoB,CAACC,IAAI;UAChCC,IAAI,GAAGF,oBAAoB,CAACE,IAAI;UAChCC,CAAC,GAAGH,oBAAoB,CAACG,CAAC;UAC1BC,CAAC,GAAGJ,oBAAoB,CAACI,CAAC;QAC5B,IAAIC,SAAS,GAAG,SAASA,SAASA,CAAClB,CAAC,EAAE;UACpC,OAAOgB,CAAC,GAAGhB,CAAC,GAAGiB,CAAC;QAClB,CAAC;QACDN,UAAU,GAAG,CAAC;UACZX,CAAC,EAAEc,IAAI;UACPb,CAAC,EAAEiB,SAAS,CAACJ,IAAI;QACnB,CAAC,EAAE;UACDd,CAAC,EAAEe,IAAI;UACPd,CAAC,EAAEiB,SAAS,CAACH,IAAI;QACnB,CAAC,CAAC;MACJ;MACA,IAAII,SAAS,GAAGhK,aAAa,CAACA,aAAa,CAACA,aAAa,CAAC,CAAC,CAAC,EAAEsJ,YAAY,CAAC,EAAE,CAAC,CAAC,EAAE;QAC/EW,IAAI,EAAE,MAAM;QACZC,MAAM,EAAEZ,YAAY,IAAIA,YAAY,CAACW;MACvC,CAAC,EAAEV,eAAe,CAAC,EAAE,CAAC,CAAC,EAAE;QACvBxE,MAAM,EAAEyE;MACV,CAAC,CAAC;MACF,IAAK,aAAa3G,KAAK,CAACsH,cAAc,CAAChB,IAAI,CAAC,EAAE;QAC5CM,QAAQ,GAAG,aAAa5G,KAAK,CAAC2F,YAAY,CAACW,IAAI,EAAEa,SAAS,CAAC;MAC7D,CAAC,MAAM,IAAI9G,UAAU,CAACiG,IAAI,CAAC,EAAE;QAC3BM,QAAQ,GAAGN,IAAI,CAACa,SAAS,CAAC;MAC5B,CAAC,MAAM;QACLP,QAAQ,GAAG,aAAa5G,KAAK,CAAC6C,aAAa,CAAChC,KAAK,EAAEjF,QAAQ,CAAC,CAAC,CAAC,EAAEuL,SAAS,EAAE;UACzEI,IAAI,EAAEf;QACR,CAAC,CAAC,CAAC;MACL;MACA,OAAO,aAAaxG,KAAK,CAAC6C,aAAa,CAACtC,KAAK,EAAE;QAC7CuC,SAAS,EAAE,uBAAuB;QAClCzG,GAAG,EAAE;MACP,CAAC,EAAEuK,QAAQ,CAAC;IACd;EACF,CAAC,EAAE;IACDvK,GAAG,EAAE,QAAQ;IACbmD,KAAK,EAAE,SAASgI,MAAMA,CAAA,EAAG;MACvB,IAAIC,YAAY,GAAG,IAAI,CAAC3J,KAAK;QAC3B4J,IAAI,GAAGD,YAAY,CAACC,IAAI;QACxBxF,MAAM,GAAGuF,YAAY,CAACvF,MAAM;QAC5BoE,IAAI,GAAGmB,YAAY,CAACnB,IAAI;QACxBxD,SAAS,GAAG2E,YAAY,CAAC3E,SAAS;QAClCoC,KAAK,GAAGuC,YAAY,CAACvC,KAAK;QAC1BC,KAAK,GAAGsC,YAAY,CAACtC,KAAK;QAC1BwC,IAAI,GAAGF,YAAY,CAACE,IAAI;QACxBC,GAAG,GAAGH,YAAY,CAACG,GAAG;QACtBC,KAAK,GAAGJ,YAAY,CAACI,KAAK;QAC1BC,MAAM,GAAGL,YAAY,CAACK,MAAM;QAC5BC,EAAE,GAAGN,YAAY,CAACM,EAAE;QACpBzE,iBAAiB,GAAGmE,YAAY,CAACnE,iBAAiB;MACpD,IAAIoE,IAAI,IAAI,CAACxF,MAAM,IAAI,CAACA,MAAM,CAAC/F,MAAM,EAAE;QACrC,OAAO,IAAI;MACb;MACA,IAAI4F,mBAAmB,GAAG,IAAI,CAAC6B,KAAK,CAAC7B,mBAAmB;MACxD,IAAIiG,UAAU,GAAG1H,IAAI,CAAC,kBAAkB,EAAEwC,SAAS,CAAC;MACpD,IAAImF,SAAS,GAAG/C,KAAK,IAAIA,KAAK,CAACgD,iBAAiB;MAChD,IAAIC,SAAS,GAAGhD,KAAK,IAAIA,KAAK,CAAC+C,iBAAiB;MAChD,IAAIE,QAAQ,GAAGH,SAAS,IAAIE,SAAS;MACrC,IAAIE,UAAU,GAAGlI,KAAK,CAAC4H,EAAE,CAAC,GAAG,IAAI,CAACA,EAAE,GAAGA,EAAE;MACzC,OAAO,aAAa/H,KAAK,CAAC6C,aAAa,CAACtC,KAAK,EAAE;QAC7CuC,SAAS,EAAEkF,UAAU;QACrBM,QAAQ,EAAEF,QAAQ,GAAG,gBAAgB,CAACtG,MAAM,CAACuG,UAAU,EAAE,GAAG,CAAC,GAAG;MAClE,CAAC,EAAEJ,SAAS,IAAIE,SAAS,GAAG,aAAanI,KAAK,CAAC6C,aAAa,CAAC,MAAM,EAAE,IAAI,EAAE,aAAa7C,KAAK,CAAC6C,aAAa,CAAC,UAAU,EAAE;QACtHkF,EAAE,EAAE,WAAW,CAACjG,MAAM,CAACuG,UAAU;MACnC,CAAC,EAAE,aAAarI,KAAK,CAAC6C,aAAa,CAAC,MAAM,EAAE;QAC1CmD,CAAC,EAAEiC,SAAS,GAAGN,IAAI,GAAGA,IAAI,GAAGE,KAAK,GAAG,CAAC;QACtC5B,CAAC,EAAEkC,SAAS,GAAGP,GAAG,GAAGA,GAAG,GAAGE,MAAM,GAAG,CAAC;QACrCD,KAAK,EAAEI,SAAS,GAAGJ,KAAK,GAAGA,KAAK,GAAG,CAAC;QACpCC,MAAM,EAAEK,SAAS,GAAGL,MAAM,GAAGA,MAAM,GAAG;MACxC,CAAC,CAAC,CAAC,CAAC,GAAG,IAAI,EAAExB,IAAI,IAAI,IAAI,CAACF,UAAU,CAAC,CAAC,EAAE,IAAI,CAACpB,cAAc,CAAC,CAAC,EAAE,aAAahF,KAAK,CAAC6C,aAAa,CAACtC,KAAK,EAAE;QACrGlE,GAAG,EAAE;MACP,CAAC,EAAE,IAAI,CAACyI,aAAa,CAAC,CAAC,CAAC,EAAE,CAAC,CAACxB,iBAAiB,IAAIvB,mBAAmB,KAAKvB,SAAS,CAAC+H,kBAAkB,CAAC,IAAI,CAACzK,KAAK,EAAEoE,MAAM,CAAC,CAAC;IAC5H;EACF,CAAC,CAAC,EAAE,CAAC;IACH7F,GAAG,EAAE,0BAA0B;IAC/BmD,KAAK,EAAE,SAASgJ,wBAAwBA,CAACC,SAAS,EAAEC,SAAS,EAAE;MAC7D,IAAID,SAAS,CAAC/E,WAAW,KAAKgF,SAAS,CAACC,eAAe,EAAE;QACvD,OAAO;UACLA,eAAe,EAAEF,SAAS,CAAC/E,WAAW;UACtCkF,SAAS,EAAEH,SAAS,CAACvG,MAAM;UAC3ByB,UAAU,EAAE+E,SAAS,CAACE;QACxB,CAAC;MACH;MACA,IAAIH,SAAS,CAACvG,MAAM,KAAKwG,SAAS,CAACE,SAAS,EAAE;QAC5C,OAAO;UACLA,SAAS,EAAEH,SAAS,CAACvG;QACvB,CAAC;MACH;MACA,OAAO,IAAI;IACb;EACF,CAAC,CAAC,CAAC;AACL,CAAC,CAACjC,aAAa,CAAC;AAChB5E,QAAQ,GAAGkG,OAAO;AAClBlE,eAAe,CAACkE,OAAO,EAAE,aAAa,EAAE,SAAS,CAAC;AAClDlE,eAAe,CAACkE,OAAO,EAAE,cAAc,EAAE;EACvCsH,OAAO,EAAE,CAAC;EACVC,OAAO,EAAE,CAAC;EACVC,OAAO,EAAE,CAAC;EACVC,UAAU,EAAE,QAAQ;EACpBzC,QAAQ,EAAE,OAAO;EACjBC,aAAa,EAAE,QAAQ;EACvBZ,IAAI,EAAE,EAAE;EACRvD,KAAK,EAAE,QAAQ;EACfqF,IAAI,EAAE,KAAK;EACXpE,iBAAiB,EAAE,CAAC3C,MAAM,CAACsI,KAAK;EAChC1F,cAAc,EAAE,CAAC;EACjBC,iBAAiB,EAAE,GAAG;EACtBC,eAAe,EAAE;AACnB,CAAC,CAAC;AACF;AACA;AACA;AACA;AACA;AACA;AACA;AACApG,eAAe,CAACkE,OAAO,EAAE,iBAAiB,EAAE,UAAU2H,KAAK,EAAE;EAC3D,IAAIhE,KAAK,GAAGgE,KAAK,CAAChE,KAAK;IACrBC,KAAK,GAAG+D,KAAK,CAAC/D,KAAK;IACnBgE,KAAK,GAAGD,KAAK,CAACC,KAAK;IACnB7D,IAAI,GAAG4D,KAAK,CAAC5D,IAAI;IACjB8D,aAAa,GAAGF,KAAK,CAACE,aAAa;IACnCC,UAAU,GAAGH,KAAK,CAACG,UAAU;IAC7BC,UAAU,GAAGJ,KAAK,CAACI,UAAU;IAC7BC,MAAM,GAAGL,KAAK,CAACK,MAAM;EACvB,IAAIC,WAAW,GAAGlE,IAAI,CAACxH,KAAK,CAAC0L,WAAW;EACxC,IAAIC,KAAK,GAAGhJ,aAAa,CAAC6E,IAAI,CAACxH,KAAK,CAACsH,QAAQ,EAAErE,IAAI,CAAC;EACpD,IAAI2I,YAAY,GAAGvJ,KAAK,CAAC+E,KAAK,CAACQ,OAAO,CAAC,GAAGJ,IAAI,CAACxH,KAAK,CAAC4H,OAAO,GAAGR,KAAK,CAACQ,OAAO;EAC5E,IAAIiE,YAAY,GAAGxJ,KAAK,CAACgF,KAAK,CAACO,OAAO,CAAC,GAAGJ,IAAI,CAACxH,KAAK,CAAC4H,OAAO,GAAGP,KAAK,CAACO,OAAO;EAC5E,IAAIkE,YAAY,GAAGT,KAAK,IAAIA,KAAK,CAACzD,OAAO;EACzC,IAAImE,aAAa,GAAGV,KAAK,GAAGA,KAAK,CAACW,KAAK,GAAGlJ,KAAK,CAACmJ,YAAY,CAACD,KAAK;EAClE,IAAIE,QAAQ,GAAGH,aAAa,IAAIA,aAAa,CAAC,CAAC,CAAC;EAChD,IAAII,SAAS,GAAG/E,KAAK,CAACgF,KAAK,CAACC,SAAS,GAAGjF,KAAK,CAACgF,KAAK,CAACC,SAAS,CAAC,CAAC,GAAG,CAAC;EACnE,IAAIC,SAAS,GAAGjF,KAAK,CAAC+E,KAAK,CAACC,SAAS,GAAGhF,KAAK,CAAC+E,KAAK,CAACC,SAAS,CAAC,CAAC,GAAG,CAAC;EACnE,IAAIjI,MAAM,GAAGkH,aAAa,CAAC3G,GAAG,CAAC,UAAUC,KAAK,EAAE8B,KAAK,EAAE;IACrD,IAAIwB,CAAC,GAAG7E,iBAAiB,CAACuB,KAAK,EAAEgH,YAAY,CAAC;IAC9C,IAAIzD,CAAC,GAAG9E,iBAAiB,CAACuB,KAAK,EAAEiH,YAAY,CAAC;IAC9C,IAAIU,CAAC,GAAG,CAAClK,KAAK,CAACyJ,YAAY,CAAC,IAAIzI,iBAAiB,CAACuB,KAAK,EAAEkH,YAAY,CAAC,IAAI,GAAG;IAC7E,IAAIU,cAAc,GAAG,CAAC;MACpBC,IAAI,EAAEpK,KAAK,CAAC+E,KAAK,CAACQ,OAAO,CAAC,GAAGJ,IAAI,CAACxH,KAAK,CAACyM,IAAI,GAAGrF,KAAK,CAACqF,IAAI,IAAIrF,KAAK,CAACQ,OAAO;MAC1E8E,IAAI,EAAEtF,KAAK,CAACsF,IAAI,IAAI,EAAE;MACtBhL,KAAK,EAAEwG,CAAC;MACRyE,OAAO,EAAE/H,KAAK;MACdgD,OAAO,EAAEgE,YAAY;MACrBnC,IAAI,EAAEiC;IACR,CAAC,EAAE;MACDe,IAAI,EAAEpK,KAAK,CAACgF,KAAK,CAACO,OAAO,CAAC,GAAGJ,IAAI,CAACxH,KAAK,CAACyM,IAAI,GAAGpF,KAAK,CAACoF,IAAI,IAAIpF,KAAK,CAACO,OAAO;MAC1E8E,IAAI,EAAErF,KAAK,CAACqF,IAAI,IAAI,EAAE;MACtBhL,KAAK,EAAEyG,CAAC;MACRwE,OAAO,EAAE/H,KAAK;MACdgD,OAAO,EAAEiE,YAAY;MACrBpC,IAAI,EAAEiC;IACR,CAAC,CAAC;IACF,IAAIa,CAAC,KAAK,GAAG,EAAE;MACbC,cAAc,CAACpN,IAAI,CAAC;QAClBqN,IAAI,EAAEpB,KAAK,CAACoB,IAAI,IAAIpB,KAAK,CAACzD,OAAO;QACjC8E,IAAI,EAAErB,KAAK,CAACqB,IAAI,IAAI,EAAE;QACtBhL,KAAK,EAAE6K,CAAC;QACRI,OAAO,EAAE/H,KAAK;QACdgD,OAAO,EAAEkE,YAAY;QACrBrC,IAAI,EAAEiC;MACR,CAAC,CAAC;IACJ;IACA,IAAIzG,EAAE,GAAG3B,uBAAuB,CAAC;MAC/BsJ,IAAI,EAAExF,KAAK;MACXyF,KAAK,EAAEtB,UAAU;MACjBuB,QAAQ,EAAEX,SAAS;MACnBvH,KAAK,EAAEA,KAAK;MACZ8B,KAAK,EAAEA,KAAK;MACZkB,OAAO,EAAEgE;IACX,CAAC,CAAC;IACF,IAAI1G,EAAE,GAAG5B,uBAAuB,CAAC;MAC/BsJ,IAAI,EAAEvF,KAAK;MACXwF,KAAK,EAAErB,UAAU;MACjBsB,QAAQ,EAAER,SAAS;MACnB1H,KAAK,EAAEA,KAAK;MACZ8B,KAAK,EAAEA,KAAK;MACZkB,OAAO,EAAEiE;IACX,CAAC,CAAC;IACF,IAAI1G,IAAI,GAAGoH,CAAC,KAAK,GAAG,GAAGlB,KAAK,CAACe,KAAK,CAACG,CAAC,CAAC,GAAGL,QAAQ;IAChD,IAAIa,MAAM,GAAGC,IAAI,CAACC,IAAI,CAACD,IAAI,CAACE,GAAG,CAAC/H,IAAI,EAAE,CAAC,CAAC,GAAG6H,IAAI,CAACG,EAAE,CAAC;IACnD,OAAO9N,aAAa,CAACA,aAAa,CAAC,CAAC,CAAC,EAAEuF,KAAK,CAAC,EAAE,CAAC,CAAC,EAAE;MACjDK,EAAE,EAAEA,EAAE;MACNC,EAAE,EAAEA,EAAE;MACNgD,CAAC,EAAEjD,EAAE,GAAG8H,MAAM;MACd5E,CAAC,EAAEjD,EAAE,GAAG6H,MAAM;MACd3F,KAAK,EAAEA,KAAK;MACZC,KAAK,EAAEA,KAAK;MACZgE,KAAK,EAAEA,KAAK;MACZtB,KAAK,EAAE,CAAC,GAAGgD,MAAM;MACjB/C,MAAM,EAAE,CAAC,GAAG+C,MAAM;MAClB5H,IAAI,EAAEA,IAAI;MACViD,IAAI,EAAE;QACJF,CAAC,EAAEA,CAAC;QACJC,CAAC,EAAEA,CAAC;QACJoE,CAAC,EAAEA;MACL,CAAC;MACDC,cAAc,EAAEA,cAAc;MAC9BY,eAAe,EAAE;QACflF,CAAC,EAAEjD,EAAE;QACLkD,CAAC,EAAEjD;MACL,CAAC;MACDyH,OAAO,EAAE/H;IACX,CAAC,EAAE+G,KAAK,IAAIA,KAAK,CAACjF,KAAK,CAAC,IAAIiF,KAAK,CAACjF,KAAK,CAAC,CAAC1G,KAAK,CAAC;EACjD,CAAC,CAAC;EACF,OAAOX,aAAa,CAAC;IACnB+E,MAAM,EAAEA;EACV,CAAC,EAAEqH,MAAM,CAAC;AACZ,CAAC,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}