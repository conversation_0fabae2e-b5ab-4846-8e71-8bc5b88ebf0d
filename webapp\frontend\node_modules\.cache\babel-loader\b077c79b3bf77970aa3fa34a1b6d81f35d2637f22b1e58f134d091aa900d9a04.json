{"ast": null, "code": "import { toDate } from \"./toDate.mjs\";\n\n/**\n * @name getMinutes\n * @category Minute Helpers\n * @summary Get the minutes of the given date.\n *\n * @description\n * Get the minutes of the given date.\n *\n * @typeParam DateType - The `Date` type, the function operates on. Gets inferred from passed arguments. Allows to use extensions like [`UTCDate`](https://github.com/date-fns/utc).\n *\n * @param date - The given date\n *\n * @returns The minutes\n *\n * @example\n * // Get the minutes of 29 February 2012 11:45:05:\n * const result = getMinutes(new Date(2012, 1, 29, 11, 45, 5))\n * //=> 45\n */\nexport function getMinutes(date) {\n  const _date = toDate(date);\n  const minutes = _date.getMinutes();\n  return minutes;\n}\n\n// Fallback for modularized imports:\nexport default getMinutes;", "map": {"version": 3, "names": ["toDate", "getMinutes", "date", "_date", "minutes"], "sources": ["C:/CMS/webapp/frontend/node_modules/date-fns/getMinutes.mjs"], "sourcesContent": ["import { toDate } from \"./toDate.mjs\";\n\n/**\n * @name getMinutes\n * @category Minute Helpers\n * @summary Get the minutes of the given date.\n *\n * @description\n * Get the minutes of the given date.\n *\n * @typeParam DateType - The `Date` type, the function operates on. Gets inferred from passed arguments. Allows to use extensions like [`UTCDate`](https://github.com/date-fns/utc).\n *\n * @param date - The given date\n *\n * @returns The minutes\n *\n * @example\n * // Get the minutes of 29 February 2012 11:45:05:\n * const result = getMinutes(new Date(2012, 1, 29, 11, 45, 5))\n * //=> 45\n */\nexport function getMinutes(date) {\n  const _date = toDate(date);\n  const minutes = _date.getMinutes();\n  return minutes;\n}\n\n// Fallback for modularized imports:\nexport default getMinutes;\n"], "mappings": "AAAA,SAASA,MAAM,QAAQ,cAAc;;AAErC;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO,SAASC,UAAUA,CAACC,IAAI,EAAE;EAC/B,MAAMC,KAAK,GAAGH,MAAM,CAACE,IAAI,CAAC;EAC1B,MAAME,OAAO,GAAGD,KAAK,CAACF,UAAU,CAAC,CAAC;EAClC,OAAOG,OAAO;AAChB;;AAEA;AACA,eAAeH,UAAU", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}