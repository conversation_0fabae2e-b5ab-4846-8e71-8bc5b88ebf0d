{"ast": null, "code": "import axios from 'axios';\nconst API_URL = 'http://localhost:8001/api';\n\n// Crea un'istanza di axios con configurazione personalizzata\nconst axiosInstance = axios.create({\n  baseURL: API_URL,\n  headers: {\n    'Content-Type': 'application/json'\n  }\n});\n\n// Configura axios per includere il token in tutte le richieste\naxiosInstance.interceptors.request.use(config => {\n  const token = localStorage.getItem('token');\n  if (token) {\n    config.headers.Authorization = `Bearer ${token}`;\n  }\n  return config;\n}, error => {\n  return Promise.reject(error);\n});\nconst reportService = {\n  // Ottiene il report di avanzamento\n  getProgressReport: async (cantiereId, formato = 'pdf') => {\n    try {\n      // Assicurati che cantiereId sia un numero\n      const cantiereIdNum = parseInt(cantiereId, 10);\n      if (isNaN(cantiereIdNum)) {\n        throw new Error(`ID cantiere non valido: ${cantiereId}`);\n      }\n      const response = await axiosInstance.get(`/reports/${cantiereIdNum}/progress?formato=${formato}`);\n      return response.data;\n    } catch (error) {\n      console.error('Get progress report error:', error);\n      throw error.response ? error.response.data : error;\n    }\n  },\n  // Ottiene la distinta materiali (Bill of Quantities)\n  getBillOfQuantities: async (cantiereId, formato = 'pdf') => {\n    try {\n      // Assicurati che cantiereId sia un numero\n      const cantiereIdNum = parseInt(cantiereId, 10);\n      if (isNaN(cantiereIdNum)) {\n        throw new Error(`ID cantiere non valido: ${cantiereId}`);\n      }\n      const response = await axiosInstance.get(`/reports/${cantiereIdNum}/boq?formato=${formato}`);\n      return response.data;\n    } catch (error) {\n      console.error('Get bill of quantities error:', error);\n      throw error.response ? error.response.data : error;\n    }\n  },\n  // Ottiene il report di utilizzo bobine\n  getBobineReport: async (cantiereId, formato = 'pdf') => {\n    try {\n      // Assicurati che cantiereId sia un numero\n      const cantiereIdNum = parseInt(cantiereId, 10);\n      if (isNaN(cantiereIdNum)) {\n        throw new Error(`ID cantiere non valido: ${cantiereId}`);\n      }\n      const response = await axiosInstance.get(`/reports/${cantiereIdNum}/bobine?formato=${formato}`);\n      return response.data;\n    } catch (error) {\n      console.error('Get bobine report error:', error);\n      throw error.response ? error.response.data : error;\n    }\n  },\n  // Ottiene il report di una bobina specifica\n  getBobinaReport: async (cantiereId, idBobina, formato = 'pdf') => {\n    try {\n      // Assicurati che cantiereId sia un numero\n      const cantiereIdNum = parseInt(cantiereId, 10);\n      if (isNaN(cantiereIdNum)) {\n        throw new Error(`ID cantiere non valido: ${cantiereId}`);\n      }\n      const response = await axiosInstance.get(`/reports/${cantiereIdNum}/bobina/${idBobina}?formato=${formato}`);\n      return response.data;\n    } catch (error) {\n      console.error('Get bobina report error:', error);\n      throw error.response ? error.response.data : error;\n    }\n  },\n  // Ottiene il report di posa per periodo\n  getPosaPerPeriodoReport: async (cantiereId, dataInizio, dataFine, formato = 'pdf') => {\n    try {\n      // Assicurati che cantiereId sia un numero\n      const cantiereIdNum = parseInt(cantiereId, 10);\n      if (isNaN(cantiereIdNum)) {\n        throw new Error(`ID cantiere non valido: ${cantiereId}`);\n      }\n      const response = await axiosInstance.get(`/reports/${cantiereIdNum}/posa-periodo?data_inizio=${dataInizio}&data_fine=${dataFine}&formato=${formato}`);\n      return response.data;\n    } catch (error) {\n      console.error('Get posa per periodo report error:', error);\n      throw error.response ? error.response.data : error;\n    }\n  }\n};\nexport default reportService;", "map": {"version": 3, "names": ["axios", "API_URL", "axiosInstance", "create", "baseURL", "headers", "interceptors", "request", "use", "config", "token", "localStorage", "getItem", "Authorization", "error", "Promise", "reject", "reportService", "getProgressReport", "cantiereId", "formato", "cantiereIdNum", "parseInt", "isNaN", "Error", "response", "get", "data", "console", "getBillOfQuantities", "getBobineReport", "getBobinaReport", "idBobina", "getPosaPerPeriodoReport", "dataInizio", "dataFine"], "sources": ["C:/Users/<USER>/PycharmProjects/CMS/webapp/frontend/src/services/reportService.js"], "sourcesContent": ["import axios from 'axios';\r\n\r\nconst API_URL = 'http://localhost:8001/api';\r\n\r\n// Crea un'istanza di axios con configurazione personalizzata\r\nconst axiosInstance = axios.create({\r\n  baseURL: API_URL,\r\n  headers: {\r\n    'Content-Type': 'application/json'\r\n  }\r\n});\r\n\r\n// Configura axios per includere il token in tutte le richieste\r\naxiosInstance.interceptors.request.use(\r\n  (config) => {\r\n    const token = localStorage.getItem('token');\r\n    if (token) {\r\n      config.headers.Authorization = `Bearer ${token}`;\r\n    }\r\n    return config;\r\n  },\r\n  (error) => {\r\n    return Promise.reject(error);\r\n  }\r\n);\r\n\r\nconst reportService = {\r\n  // Ottiene il report di avanzamento\r\n  getProgressReport: async (cantiereId, formato = 'pdf') => {\r\n    try {\r\n      // Assicurati che cantiereId sia un numero\r\n      const cantiereIdNum = parseInt(cantiereId, 10);\r\n      if (isNaN(cantiereIdNum)) {\r\n        throw new Error(`ID cantiere non valido: ${cantiereId}`);\r\n      }\r\n\r\n      const response = await axiosInstance.get(`/reports/${cantiereIdNum}/progress?formato=${formato}`);\r\n      return response.data;\r\n    } catch (error) {\r\n      console.error('Get progress report error:', error);\r\n      throw error.response ? error.response.data : error;\r\n    }\r\n  },\r\n\r\n  // Ottiene la distinta materiali (Bill of Quantities)\r\n  getBillOfQuantities: async (cantiereId, formato = 'pdf') => {\r\n    try {\r\n      // Assicurati che cantiereId sia un numero\r\n      const cantiereIdNum = parseInt(cantiereId, 10);\r\n      if (isNaN(cantiereIdNum)) {\r\n        throw new Error(`ID cantiere non valido: ${cantiereId}`);\r\n      }\r\n\r\n      const response = await axiosInstance.get(`/reports/${cantiereIdNum}/boq?formato=${formato}`);\r\n      return response.data;\r\n    } catch (error) {\r\n      console.error('Get bill of quantities error:', error);\r\n      throw error.response ? error.response.data : error;\r\n    }\r\n  },\r\n\r\n  // Ottiene il report di utilizzo bobine\r\n  getBobineReport: async (cantiereId, formato = 'pdf') => {\r\n    try {\r\n      // Assicurati che cantiereId sia un numero\r\n      const cantiereIdNum = parseInt(cantiereId, 10);\r\n      if (isNaN(cantiereIdNum)) {\r\n        throw new Error(`ID cantiere non valido: ${cantiereId}`);\r\n      }\r\n\r\n      const response = await axiosInstance.get(`/reports/${cantiereIdNum}/bobine?formato=${formato}`);\r\n      return response.data;\r\n    } catch (error) {\r\n      console.error('Get bobine report error:', error);\r\n      throw error.response ? error.response.data : error;\r\n    }\r\n  },\r\n\r\n  // Ottiene il report di una bobina specifica\r\n  getBobinaReport: async (cantiereId, idBobina, formato = 'pdf') => {\r\n    try {\r\n      // Assicurati che cantiereId sia un numero\r\n      const cantiereIdNum = parseInt(cantiereId, 10);\r\n      if (isNaN(cantiereIdNum)) {\r\n        throw new Error(`ID cantiere non valido: ${cantiereId}`);\r\n      }\r\n\r\n      const response = await axiosInstance.get(`/reports/${cantiereIdNum}/bobina/${idBobina}?formato=${formato}`);\r\n      return response.data;\r\n    } catch (error) {\r\n      console.error('Get bobina report error:', error);\r\n      throw error.response ? error.response.data : error;\r\n    }\r\n  },\r\n\r\n  // Ottiene il report di posa per periodo\r\n  getPosaPerPeriodoReport: async (cantiereId, dataInizio, dataFine, formato = 'pdf') => {\r\n    try {\r\n      // Assicurati che cantiereId sia un numero\r\n      const cantiereIdNum = parseInt(cantiereId, 10);\r\n      if (isNaN(cantiereIdNum)) {\r\n        throw new Error(`ID cantiere non valido: ${cantiereId}`);\r\n      }\r\n\r\n      const response = await axiosInstance.get(\r\n        `/reports/${cantiereIdNum}/posa-periodo?data_inizio=${dataInizio}&data_fine=${dataFine}&formato=${formato}`\r\n      );\r\n      return response.data;\r\n    } catch (error) {\r\n      console.error('Get posa per periodo report error:', error);\r\n      throw error.response ? error.response.data : error;\r\n    }\r\n  }\r\n};\r\n\r\nexport default reportService;\r\n"], "mappings": "AAAA,OAAOA,KAAK,MAAM,OAAO;AAEzB,MAAMC,OAAO,GAAG,2BAA2B;;AAE3C;AACA,MAAMC,aAAa,GAAGF,KAAK,CAACG,MAAM,CAAC;EACjCC,OAAO,EAAEH,OAAO;EAChBI,OAAO,EAAE;IACP,cAAc,EAAE;EAClB;AACF,CAAC,CAAC;;AAEF;AACAH,aAAa,CAACI,YAAY,CAACC,OAAO,CAACC,GAAG,CACnCC,MAAM,IAAK;EACV,MAAMC,KAAK,GAAGC,YAAY,CAACC,OAAO,CAAC,OAAO,CAAC;EAC3C,IAAIF,KAAK,EAAE;IACTD,MAAM,CAACJ,OAAO,CAACQ,aAAa,GAAG,UAAUH,KAAK,EAAE;EAClD;EACA,OAAOD,MAAM;AACf,CAAC,EACAK,KAAK,IAAK;EACT,OAAOC,OAAO,CAACC,MAAM,CAACF,KAAK,CAAC;AAC9B,CACF,CAAC;AAED,MAAMG,aAAa,GAAG;EACpB;EACAC,iBAAiB,EAAE,MAAAA,CAAOC,UAAU,EAAEC,OAAO,GAAG,KAAK,KAAK;IACxD,IAAI;MACF;MACA,MAAMC,aAAa,GAAGC,QAAQ,CAACH,UAAU,EAAE,EAAE,CAAC;MAC9C,IAAII,KAAK,CAACF,aAAa,CAAC,EAAE;QACxB,MAAM,IAAIG,KAAK,CAAC,2BAA2BL,UAAU,EAAE,CAAC;MAC1D;MAEA,MAAMM,QAAQ,GAAG,MAAMvB,aAAa,CAACwB,GAAG,CAAC,YAAYL,aAAa,qBAAqBD,OAAO,EAAE,CAAC;MACjG,OAAOK,QAAQ,CAACE,IAAI;IACtB,CAAC,CAAC,OAAOb,KAAK,EAAE;MACdc,OAAO,CAACd,KAAK,CAAC,4BAA4B,EAAEA,KAAK,CAAC;MAClD,MAAMA,KAAK,CAACW,QAAQ,GAAGX,KAAK,CAACW,QAAQ,CAACE,IAAI,GAAGb,KAAK;IACpD;EACF,CAAC;EAED;EACAe,mBAAmB,EAAE,MAAAA,CAAOV,UAAU,EAAEC,OAAO,GAAG,KAAK,KAAK;IAC1D,IAAI;MACF;MACA,MAAMC,aAAa,GAAGC,QAAQ,CAACH,UAAU,EAAE,EAAE,CAAC;MAC9C,IAAII,KAAK,CAACF,aAAa,CAAC,EAAE;QACxB,MAAM,IAAIG,KAAK,CAAC,2BAA2BL,UAAU,EAAE,CAAC;MAC1D;MAEA,MAAMM,QAAQ,GAAG,MAAMvB,aAAa,CAACwB,GAAG,CAAC,YAAYL,aAAa,gBAAgBD,OAAO,EAAE,CAAC;MAC5F,OAAOK,QAAQ,CAACE,IAAI;IACtB,CAAC,CAAC,OAAOb,KAAK,EAAE;MACdc,OAAO,CAACd,KAAK,CAAC,+BAA+B,EAAEA,KAAK,CAAC;MACrD,MAAMA,KAAK,CAACW,QAAQ,GAAGX,KAAK,CAACW,QAAQ,CAACE,IAAI,GAAGb,KAAK;IACpD;EACF,CAAC;EAED;EACAgB,eAAe,EAAE,MAAAA,CAAOX,UAAU,EAAEC,OAAO,GAAG,KAAK,KAAK;IACtD,IAAI;MACF;MACA,MAAMC,aAAa,GAAGC,QAAQ,CAACH,UAAU,EAAE,EAAE,CAAC;MAC9C,IAAII,KAAK,CAACF,aAAa,CAAC,EAAE;QACxB,MAAM,IAAIG,KAAK,CAAC,2BAA2BL,UAAU,EAAE,CAAC;MAC1D;MAEA,MAAMM,QAAQ,GAAG,MAAMvB,aAAa,CAACwB,GAAG,CAAC,YAAYL,aAAa,mBAAmBD,OAAO,EAAE,CAAC;MAC/F,OAAOK,QAAQ,CAACE,IAAI;IACtB,CAAC,CAAC,OAAOb,KAAK,EAAE;MACdc,OAAO,CAACd,KAAK,CAAC,0BAA0B,EAAEA,KAAK,CAAC;MAChD,MAAMA,KAAK,CAACW,QAAQ,GAAGX,KAAK,CAACW,QAAQ,CAACE,IAAI,GAAGb,KAAK;IACpD;EACF,CAAC;EAED;EACAiB,eAAe,EAAE,MAAAA,CAAOZ,UAAU,EAAEa,QAAQ,EAAEZ,OAAO,GAAG,KAAK,KAAK;IAChE,IAAI;MACF;MACA,MAAMC,aAAa,GAAGC,QAAQ,CAACH,UAAU,EAAE,EAAE,CAAC;MAC9C,IAAII,KAAK,CAACF,aAAa,CAAC,EAAE;QACxB,MAAM,IAAIG,KAAK,CAAC,2BAA2BL,UAAU,EAAE,CAAC;MAC1D;MAEA,MAAMM,QAAQ,GAAG,MAAMvB,aAAa,CAACwB,GAAG,CAAC,YAAYL,aAAa,WAAWW,QAAQ,YAAYZ,OAAO,EAAE,CAAC;MAC3G,OAAOK,QAAQ,CAACE,IAAI;IACtB,CAAC,CAAC,OAAOb,KAAK,EAAE;MACdc,OAAO,CAACd,KAAK,CAAC,0BAA0B,EAAEA,KAAK,CAAC;MAChD,MAAMA,KAAK,CAACW,QAAQ,GAAGX,KAAK,CAACW,QAAQ,CAACE,IAAI,GAAGb,KAAK;IACpD;EACF,CAAC;EAED;EACAmB,uBAAuB,EAAE,MAAAA,CAAOd,UAAU,EAAEe,UAAU,EAAEC,QAAQ,EAAEf,OAAO,GAAG,KAAK,KAAK;IACpF,IAAI;MACF;MACA,MAAMC,aAAa,GAAGC,QAAQ,CAACH,UAAU,EAAE,EAAE,CAAC;MAC9C,IAAII,KAAK,CAACF,aAAa,CAAC,EAAE;QACxB,MAAM,IAAIG,KAAK,CAAC,2BAA2BL,UAAU,EAAE,CAAC;MAC1D;MAEA,MAAMM,QAAQ,GAAG,MAAMvB,aAAa,CAACwB,GAAG,CACtC,YAAYL,aAAa,6BAA6Ba,UAAU,cAAcC,QAAQ,YAAYf,OAAO,EAC3G,CAAC;MACD,OAAOK,QAAQ,CAACE,IAAI;IACtB,CAAC,CAAC,OAAOb,KAAK,EAAE;MACdc,OAAO,CAACd,KAAK,CAAC,oCAAoC,EAAEA,KAAK,CAAC;MAC1D,MAAMA,KAAK,CAACW,QAAQ,GAAGX,KAAK,CAACW,QAAQ,CAACE,IAAI,GAAGb,KAAK;IACpD;EACF;AACF,CAAC;AAED,eAAeG,aAAa", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}