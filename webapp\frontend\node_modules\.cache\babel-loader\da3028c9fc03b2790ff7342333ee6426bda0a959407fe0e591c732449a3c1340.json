{"ast": null, "code": "import axios from 'axios';\nconst API_URL = 'http://localhost:8001/api';\n\n// Crea un'istanza di axios con configurazione personalizzata\nconst axiosInstance = axios.create({\n  baseURL: API_URL,\n  headers: {\n    'Content-Type': 'application/json'\n  },\n  timeout: 15000,\n  // Timeout aumentato a 15 secondi\n  withCredentials: false // Modificato per risolvere problemi CORS\n});\n\n// Configura axios per includere il token in tutte le richieste\naxiosInstance.interceptors.request.use(config => {\n  const token = localStorage.getItem('token');\n  if (token) {\n    config.headers.Authorization = `Bearer ${token}`;\n  }\n  return config;\n}, error => {\n  return Promise.reject(error);\n});\nconst caviService = {\n  // Ottiene la lista dei cavi di un cantiere\n  getCavi: async (cantiereId, tipoCavo = null, includeStats = false) => {\n    try {\n      console.log('getCavi chiamato con:', {\n        cantiereId,\n        tipoCavo,\n        includeStats\n      });\n      console.log('Tipo di cantiereId:', typeof cantiereId);\n\n      // Assicurati che cantiereId sia un numero\n      let cantiereIdNum = cantiereId;\n      if (typeof cantiereId === 'string') {\n        cantiereIdNum = parseInt(cantiereId, 10);\n        console.log('cantiereId convertito da stringa a numero:', cantiereIdNum);\n      }\n      if (isNaN(cantiereIdNum)) {\n        console.error('ID cantiere non è un numero valido:', cantiereId);\n        throw new Error(`ID cantiere non valido: ${cantiereId}`);\n      }\n      let url = `/cavi/${cantiereIdNum}`;\n      let params = [];\n      if (tipoCavo !== null) {\n        params.push(`tipo_cavo=${tipoCavo}`);\n      }\n      if (includeStats) {\n        params.push('include_stats=true');\n      }\n      if (params.length > 0) {\n        url += '?' + params.join('&');\n      }\n      console.log(`Chiamata API: GET ${url}`);\n      console.log('Token:', localStorage.getItem('token') ? 'Presente' : 'Mancante');\n      console.log('URL completo:', `${API_URL}${url}`);\n      try {\n        console.log(`Tentativo di chiamata API: GET ${url} con token: ${localStorage.getItem('token') ? 'presente' : 'mancante'}`);\n        console.log('Headers della richiesta:', {\n          'Content-Type': 'application/json',\n          'Authorization': `Bearer ${localStorage.getItem('token')}`\n        });\n\n        // Aggiungi un timeout più lungo per la richiesta\n        const response = await axiosInstance.get(url, {\n          timeout: 30000\n        });\n        console.log(`Risposta API: ${url}`, response.data);\n        console.log('Status della risposta:', response.status);\n        console.log('Headers della risposta:', response.headers);\n        if (Array.isArray(response.data)) {\n          console.log(`Numero di cavi ricevuti: ${response.data.length}`);\n          if (response.data.length > 0) {\n            console.log('Primo cavo ricevuto:', response.data[0]);\n          } else {\n            console.warn(`Nessun cavo trovato per il cantiere ${cantiereIdNum} con tipo ${tipoCavo}`);\n          }\n        } else {\n          console.warn(`Risposta non è un array: ${typeof response.data}`, response.data);\n        }\n        return response.data;\n      } catch (apiError) {\n        var _apiError$response, _apiError$response2, _apiError$response3, _apiError$response4;\n        console.error(`Errore nella chiamata API GET ${url}:`, apiError);\n        console.error('Dettagli errore API:', {\n          message: apiError.message,\n          status: (_apiError$response = apiError.response) === null || _apiError$response === void 0 ? void 0 : _apiError$response.status,\n          statusText: (_apiError$response2 = apiError.response) === null || _apiError$response2 === void 0 ? void 0 : _apiError$response2.statusText,\n          data: (_apiError$response3 = apiError.response) === null || _apiError$response3 === void 0 ? void 0 : _apiError$response3.data,\n          headers: (_apiError$response4 = apiError.response) === null || _apiError$response4 === void 0 ? void 0 : _apiError$response4.headers,\n          code: apiError.code,\n          isAxiosError: apiError.isAxiosError,\n          config: apiError.config ? {\n            url: apiError.config.url,\n            method: apiError.config.method,\n            timeout: apiError.config.timeout,\n            headers: apiError.config.headers\n          } : 'No config'\n        });\n\n        // Gestione specifica per errori di rete\n        if (apiError.code === 'ERR_NETWORK') {\n          console.error('Errore di rete. Verifica che il backend sia in esecuzione e accessibile.');\n          // Prova a fare una richiesta di base per verificare se il backend è raggiungibile\n          try {\n            console.log('Tentativo di test di connessione al backend...');\n            const testResponse = await fetch(API_URL);\n            console.log('Test di connessione al backend:', testResponse.status);\n          } catch (testError) {\n            console.error('Test di connessione al backend fallito:', testError);\n          }\n        }\n        throw apiError;\n      }\n    } catch (error) {\n      var _error$response, _error$response2, _error$response3, _error$response4, _error$response4$data, _error$response5, _error$response6;\n      console.error('Get cavi error:', error);\n      console.error('Error details:', {\n        message: error.message,\n        status: (_error$response = error.response) === null || _error$response === void 0 ? void 0 : _error$response.status,\n        statusText: (_error$response2 = error.response) === null || _error$response2 === void 0 ? void 0 : _error$response2.statusText,\n        data: (_error$response3 = error.response) === null || _error$response3 === void 0 ? void 0 : _error$response3.data,\n        url: `/cavi/${cantiereId}${tipoCavo !== null ? `?tipo_cavo=${tipoCavo}` : ''}`,\n        stack: error.stack\n      });\n\n      // Crea un errore più informativo\n      const enhancedError = new Error(((_error$response4 = error.response) === null || _error$response4 === void 0 ? void 0 : (_error$response4$data = _error$response4.data) === null || _error$response4$data === void 0 ? void 0 : _error$response4$data.detail) || error.message || 'Errore sconosciuto');\n      enhancedError.status = (_error$response5 = error.response) === null || _error$response5 === void 0 ? void 0 : _error$response5.status;\n      enhancedError.data = (_error$response6 = error.response) === null || _error$response6 === void 0 ? void 0 : _error$response6.data;\n      enhancedError.response = error.response;\n      enhancedError.originalError = error;\n      enhancedError.code = error.code;\n      enhancedError.isAxiosError = error.isAxiosError;\n      throw enhancedError;\n    }\n  },\n  // Crea un nuovo cavo\n  createCavo: async (cantiereId, cavoData) => {\n    try {\n      // Assicurati che cantiereId sia un numero\n      const cantiereIdNum = parseInt(cantiereId, 10);\n      if (isNaN(cantiereIdNum)) {\n        throw new Error(`ID cantiere non valido: ${cantiereId}`);\n      }\n      const response = await axiosInstance.post(`/cavi/${cantiereIdNum}`, cavoData);\n      return response.data;\n    } catch (error) {\n      console.error('Create cavo error:', error);\n      throw error.response ? error.response.data : error;\n    }\n  },\n  // Aggiorna un cavo esistente\n  updateCavo: async (cantiereId, cavoId, cavoData) => {\n    try {\n      // Assicurati che cantiereId sia un numero\n      const cantiereIdNum = parseInt(cantiereId, 10);\n      if (isNaN(cantiereIdNum)) {\n        throw new Error(`ID cantiere non valido: ${cantiereId}`);\n      }\n      const response = await axiosInstance.put(`/cavi/${cantiereIdNum}/${cavoId}`, cavoData);\n      return response.data;\n    } catch (error) {\n      console.error('Update cavo error:', error);\n      throw error.response ? error.response.data : error;\n    }\n  },\n  // Elimina un cavo\n  deleteCavo: async (cantiereId, cavoId) => {\n    try {\n      // Assicurati che cantiereId sia un numero\n      const cantiereIdNum = parseInt(cantiereId, 10);\n      if (isNaN(cantiereIdNum)) {\n        throw new Error(`ID cantiere non valido: ${cantiereId}`);\n      }\n      const response = await axiosInstance.delete(`/cavi/${cantiereIdNum}/${cavoId}`);\n      return response.data;\n    } catch (error) {\n      console.error('Delete cavo error:', error);\n      throw error.response ? error.response.data : error;\n    }\n  },\n  // Aggiorna i metri posati di un cavo\n  updateMetriPosati: async (cantiereId, cavoId, metriPosati) => {\n    try {\n      // Assicurati che cantiereId sia un numero\n      const cantiereIdNum = parseInt(cantiereId, 10);\n      if (isNaN(cantiereIdNum)) {\n        throw new Error(`ID cantiere non valido: ${cantiereId}`);\n      }\n      const response = await axiosInstance.post(`/cavi/${cantiereIdNum}/${cavoId}/metri-posati`, {\n        metri_posati: metriPosati\n      });\n      return response.data;\n    } catch (error) {\n      console.error('Update metri posati error:', error);\n      throw error.response ? error.response.data : error;\n    }\n  },\n  // Modifica la bobina di un cavo posato\n  updateBobina: async (cantiereId, cavoId, idBobina) => {\n    try {\n      // Assicurati che cantiereId sia un numero\n      const cantiereIdNum = parseInt(cantiereId, 10);\n      if (isNaN(cantiereIdNum)) {\n        throw new Error(`ID cantiere non valido: ${cantiereId}`);\n      }\n      const response = await axiosInstance.post(`/cavi/${cantiereIdNum}/${cavoId}/bobina`, {\n        id_bobina: idBobina\n      });\n      return response.data;\n    } catch (error) {\n      console.error('Update bobina error:', error);\n      throw error.response ? error.response.data : error;\n    }\n  },\n  // Ottiene la lista dei cavi installati di un cantiere\n  getCaviInstallati: async cantiereId => {\n    try {\n      // Assicurati che cantiereId sia un numero\n      const cantiereIdNum = parseInt(cantiereId, 10);\n      if (isNaN(cantiereIdNum)) {\n        throw new Error(`ID cantiere non valido: ${cantiereId}`);\n      }\n      const response = await axiosInstance.get(`/cavi/${cantiereIdNum}/installati`);\n      return response.data;\n    } catch (error) {\n      console.error('Get cavi installati error:', error);\n      throw error.response ? error.response.data : error;\n    }\n  },\n  // Collega un lato di un cavo\n  collegaCavo: async (cantiereId, cavoId, lato, responsabile) => {\n    try {\n      // Assicurati che cantiereId sia un numero\n      const cantiereIdNum = parseInt(cantiereId, 10);\n      if (isNaN(cantiereIdNum)) {\n        throw new Error(`ID cantiere non valido: ${cantiereId}`);\n      }\n      const response = await axiosInstance.post(`/cavi/${cantiereIdNum}/${cavoId}/collegamento`, {\n        lato: lato,\n        responsabile: responsabile || 'cantiere'\n      });\n      return response.data;\n    } catch (error) {\n      console.error('Collega cavo error:', error);\n      throw error.response ? error.response.data : error;\n    }\n  },\n  // Scollega un lato di un cavo\n  scollegaCavo: async (cantiereId, cavoId, lato) => {\n    try {\n      // Assicurati che cantiereId sia un numero\n      const cantiereIdNum = parseInt(cantiereId, 10);\n      if (isNaN(cantiereIdNum)) {\n        throw new Error(`ID cantiere non valido: ${cantiereId}`);\n      }\n      const response = await axiosInstance.delete(`/cavi/${cantiereIdNum}/${cavoId}/collegamento/${lato}`);\n      return response.data;\n    } catch (error) {\n      console.error('Scollega cavo error:', error);\n      throw error.response ? error.response.data : error;\n    }\n  }\n};\nexport default caviService;", "map": {"version": 3, "names": ["axios", "API_URL", "axiosInstance", "create", "baseURL", "headers", "timeout", "withCredentials", "interceptors", "request", "use", "config", "token", "localStorage", "getItem", "Authorization", "error", "Promise", "reject", "caviService", "get<PERSON><PERSON>", "cantiereId", "tipoCavo", "includeStats", "console", "log", "cantiereIdNum", "parseInt", "isNaN", "Error", "url", "params", "push", "length", "join", "response", "get", "data", "status", "Array", "isArray", "warn", "apiError", "_apiError$response", "_apiError$response2", "_apiError$response3", "_apiError$response4", "message", "statusText", "code", "isAxiosError", "method", "testResponse", "fetch", "testError", "_error$response", "_error$response2", "_error$response3", "_error$response4", "_error$response4$data", "_error$response5", "_error$response6", "stack", "enhancedError", "detail", "originalError", "createCavo", "cavoData", "post", "updateCavo", "cavoId", "put", "deleteCavo", "delete", "updateMetri<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "metri_posati", "updateBobina", "idBobina", "id_bobina", "getCaviInstallati", "collegaCavo", "lato", "responsabile", "scollegaCavo"], "sources": ["C:/Users/<USER>/PycharmProjects/CMS/webapp/frontend/src/services/caviService.js"], "sourcesContent": ["import axios from 'axios';\n\nconst API_URL = 'http://localhost:8001/api';\n\n// Crea un'istanza di axios con configurazione personalizzata\nconst axiosInstance = axios.create({\n  baseURL: API_URL,\n  headers: {\n    'Content-Type': 'application/json'\n  },\n  timeout: 15000, // Timeout aumentato a 15 secondi\n  withCredentials: false // Modificato per risolvere problemi CORS\n});\n\n// Configura axios per includere il token in tutte le richieste\naxiosInstance.interceptors.request.use(\n  (config) => {\n    const token = localStorage.getItem('token');\n    if (token) {\n      config.headers.Authorization = `Bearer ${token}`;\n    }\n    return config;\n  },\n  (error) => {\n    return Promise.reject(error);\n  }\n);\n\nconst caviService = {\n  // Ottiene la lista dei cavi di un cantiere\n  getCavi: async (cantiereId, tipoCavo = null, includeStats = false) => {\n    try {\n      console.log('getCavi chiamato con:', { cantiereId, tipoCavo, includeStats });\n      console.log('Tipo di cantiereId:', typeof cantiereId);\n\n      // Assicurati che cantiereId sia un numero\n      let cantiereIdNum = cantiereId;\n      if (typeof cantiereId === 'string') {\n        cantiereIdNum = parseInt(cantiereId, 10);\n        console.log('cantiereId convertito da stringa a numero:', cantiereIdNum);\n      }\n\n      if (isNaN(cantiereIdNum)) {\n        console.error('ID cantiere non è un numero valido:', cantiereId);\n        throw new Error(`ID cantiere non valido: ${cantiereId}`);\n      }\n\n      let url = `/cavi/${cantiereIdNum}`;\n      let params = [];\n\n      if (tipoCavo !== null) {\n        params.push(`tipo_cavo=${tipoCavo}`);\n      }\n\n      if (includeStats) {\n        params.push('include_stats=true');\n      }\n\n      if (params.length > 0) {\n        url += '?' + params.join('&');\n      }\n\n      console.log(`Chiamata API: GET ${url}`);\n      console.log('Token:', localStorage.getItem('token') ? 'Presente' : 'Mancante');\n      console.log('URL completo:', `${API_URL}${url}`);\n\n      try {\n        console.log(`Tentativo di chiamata API: GET ${url} con token: ${localStorage.getItem('token') ? 'presente' : 'mancante'}`);\n        console.log('Headers della richiesta:', {\n          'Content-Type': 'application/json',\n          'Authorization': `Bearer ${localStorage.getItem('token')}`\n        });\n\n        // Aggiungi un timeout più lungo per la richiesta\n        const response = await axiosInstance.get(url, { timeout: 30000 });\n\n        console.log(`Risposta API: ${url}`, response.data);\n        console.log('Status della risposta:', response.status);\n        console.log('Headers della risposta:', response.headers);\n\n        if (Array.isArray(response.data)) {\n          console.log(`Numero di cavi ricevuti: ${response.data.length}`);\n          if (response.data.length > 0) {\n            console.log('Primo cavo ricevuto:', response.data[0]);\n          } else {\n            console.warn(`Nessun cavo trovato per il cantiere ${cantiereIdNum} con tipo ${tipoCavo}`);\n          }\n        } else {\n          console.warn(`Risposta non è un array: ${typeof response.data}`, response.data);\n        }\n\n        return response.data;\n      } catch (apiError) {\n        console.error(`Errore nella chiamata API GET ${url}:`, apiError);\n        console.error('Dettagli errore API:', {\n          message: apiError.message,\n          status: apiError.response?.status,\n          statusText: apiError.response?.statusText,\n          data: apiError.response?.data,\n          headers: apiError.response?.headers,\n          code: apiError.code,\n          isAxiosError: apiError.isAxiosError,\n          config: apiError.config ? {\n            url: apiError.config.url,\n            method: apiError.config.method,\n            timeout: apiError.config.timeout,\n            headers: apiError.config.headers\n          } : 'No config'\n        });\n\n        // Gestione specifica per errori di rete\n        if (apiError.code === 'ERR_NETWORK') {\n          console.error('Errore di rete. Verifica che il backend sia in esecuzione e accessibile.');\n          // Prova a fare una richiesta di base per verificare se il backend è raggiungibile\n          try {\n            console.log('Tentativo di test di connessione al backend...');\n            const testResponse = await fetch(API_URL);\n            console.log('Test di connessione al backend:', testResponse.status);\n          } catch (testError) {\n            console.error('Test di connessione al backend fallito:', testError);\n          }\n        }\n\n        throw apiError;\n      }\n    } catch (error) {\n      console.error('Get cavi error:', error);\n      console.error('Error details:', {\n        message: error.message,\n        status: error.response?.status,\n        statusText: error.response?.statusText,\n        data: error.response?.data,\n        url: `/cavi/${cantiereId}${tipoCavo !== null ? `?tipo_cavo=${tipoCavo}` : ''}`,\n        stack: error.stack\n      });\n\n      // Crea un errore più informativo\n      const enhancedError = new Error(error.response?.data?.detail || error.message || 'Errore sconosciuto');\n      enhancedError.status = error.response?.status;\n      enhancedError.data = error.response?.data;\n      enhancedError.response = error.response;\n      enhancedError.originalError = error;\n      enhancedError.code = error.code;\n      enhancedError.isAxiosError = error.isAxiosError;\n\n      throw enhancedError;\n    }\n  },\n\n  // Crea un nuovo cavo\n  createCavo: async (cantiereId, cavoData) => {\n    try {\n      // Assicurati che cantiereId sia un numero\n      const cantiereIdNum = parseInt(cantiereId, 10);\n      if (isNaN(cantiereIdNum)) {\n        throw new Error(`ID cantiere non valido: ${cantiereId}`);\n      }\n\n      const response = await axiosInstance.post(`/cavi/${cantiereIdNum}`, cavoData);\n      return response.data;\n    } catch (error) {\n      console.error('Create cavo error:', error);\n      throw error.response ? error.response.data : error;\n    }\n  },\n\n  // Aggiorna un cavo esistente\n  updateCavo: async (cantiereId, cavoId, cavoData) => {\n    try {\n      // Assicurati che cantiereId sia un numero\n      const cantiereIdNum = parseInt(cantiereId, 10);\n      if (isNaN(cantiereIdNum)) {\n        throw new Error(`ID cantiere non valido: ${cantiereId}`);\n      }\n\n      const response = await axiosInstance.put(`/cavi/${cantiereIdNum}/${cavoId}`, cavoData);\n      return response.data;\n    } catch (error) {\n      console.error('Update cavo error:', error);\n      throw error.response ? error.response.data : error;\n    }\n  },\n\n  // Elimina un cavo\n  deleteCavo: async (cantiereId, cavoId) => {\n    try {\n      // Assicurati che cantiereId sia un numero\n      const cantiereIdNum = parseInt(cantiereId, 10);\n      if (isNaN(cantiereIdNum)) {\n        throw new Error(`ID cantiere non valido: ${cantiereId}`);\n      }\n\n      const response = await axiosInstance.delete(`/cavi/${cantiereIdNum}/${cavoId}`);\n      return response.data;\n    } catch (error) {\n      console.error('Delete cavo error:', error);\n      throw error.response ? error.response.data : error;\n    }\n  },\n\n  // Aggiorna i metri posati di un cavo\n  updateMetriPosati: async (cantiereId, cavoId, metriPosati) => {\n    try {\n      // Assicurati che cantiereId sia un numero\n      const cantiereIdNum = parseInt(cantiereId, 10);\n      if (isNaN(cantiereIdNum)) {\n        throw new Error(`ID cantiere non valido: ${cantiereId}`);\n      }\n\n      const response = await axiosInstance.post(`/cavi/${cantiereIdNum}/${cavoId}/metri-posati`, {\n        metri_posati: metriPosati\n      });\n      return response.data;\n    } catch (error) {\n      console.error('Update metri posati error:', error);\n      throw error.response ? error.response.data : error;\n    }\n  },\n\n  // Modifica la bobina di un cavo posato\n  updateBobina: async (cantiereId, cavoId, idBobina) => {\n    try {\n      // Assicurati che cantiereId sia un numero\n      const cantiereIdNum = parseInt(cantiereId, 10);\n      if (isNaN(cantiereIdNum)) {\n        throw new Error(`ID cantiere non valido: ${cantiereId}`);\n      }\n\n      const response = await axiosInstance.post(`/cavi/${cantiereIdNum}/${cavoId}/bobina`, {\n        id_bobina: idBobina\n      });\n      return response.data;\n    } catch (error) {\n      console.error('Update bobina error:', error);\n      throw error.response ? error.response.data : error;\n    }\n  },\n\n  // Ottiene la lista dei cavi installati di un cantiere\n  getCaviInstallati: async (cantiereId) => {\n    try {\n      // Assicurati che cantiereId sia un numero\n      const cantiereIdNum = parseInt(cantiereId, 10);\n      if (isNaN(cantiereIdNum)) {\n        throw new Error(`ID cantiere non valido: ${cantiereId}`);\n      }\n\n      const response = await axiosInstance.get(`/cavi/${cantiereIdNum}/installati`);\n      return response.data;\n    } catch (error) {\n      console.error('Get cavi installati error:', error);\n      throw error.response ? error.response.data : error;\n    }\n  },\n\n  // Collega un lato di un cavo\n  collegaCavo: async (cantiereId, cavoId, lato, responsabile) => {\n    try {\n      // Assicurati che cantiereId sia un numero\n      const cantiereIdNum = parseInt(cantiereId, 10);\n      if (isNaN(cantiereIdNum)) {\n        throw new Error(`ID cantiere non valido: ${cantiereId}`);\n      }\n\n      const response = await axiosInstance.post(`/cavi/${cantiereIdNum}/${cavoId}/collegamento`, {\n        lato: lato,\n        responsabile: responsabile || 'cantiere'\n      });\n      return response.data;\n    } catch (error) {\n      console.error('Collega cavo error:', error);\n      throw error.response ? error.response.data : error;\n    }\n  },\n\n  // Scollega un lato di un cavo\n  scollegaCavo: async (cantiereId, cavoId, lato) => {\n    try {\n      // Assicurati che cantiereId sia un numero\n      const cantiereIdNum = parseInt(cantiereId, 10);\n      if (isNaN(cantiereIdNum)) {\n        throw new Error(`ID cantiere non valido: ${cantiereId}`);\n      }\n\n      const response = await axiosInstance.delete(`/cavi/${cantiereIdNum}/${cavoId}/collegamento/${lato}`);\n      return response.data;\n    } catch (error) {\n      console.error('Scollega cavo error:', error);\n      throw error.response ? error.response.data : error;\n    }\n  }\n};\n\nexport default caviService;\n"], "mappings": "AAAA,OAAOA,KAAK,MAAM,OAAO;AAEzB,MAAMC,OAAO,GAAG,2BAA2B;;AAE3C;AACA,MAAMC,aAAa,GAAGF,KAAK,CAACG,MAAM,CAAC;EACjCC,OAAO,EAAEH,OAAO;EAChBI,OAAO,EAAE;IACP,cAAc,EAAE;EAClB,CAAC;EACDC,OAAO,EAAE,KAAK;EAAE;EAChBC,eAAe,EAAE,KAAK,CAAC;AACzB,CAAC,CAAC;;AAEF;AACAL,aAAa,CAACM,YAAY,CAACC,OAAO,CAACC,GAAG,CACnCC,MAAM,IAAK;EACV,MAAMC,KAAK,GAAGC,YAAY,CAACC,OAAO,CAAC,OAAO,CAAC;EAC3C,IAAIF,KAAK,EAAE;IACTD,MAAM,CAACN,OAAO,CAACU,aAAa,GAAG,UAAUH,KAAK,EAAE;EAClD;EACA,OAAOD,MAAM;AACf,CAAC,EACAK,KAAK,IAAK;EACT,OAAOC,OAAO,CAACC,MAAM,CAACF,KAAK,CAAC;AAC9B,CACF,CAAC;AAED,MAAMG,WAAW,GAAG;EAClB;EACAC,OAAO,EAAE,MAAAA,CAAOC,UAAU,EAAEC,QAAQ,GAAG,IAAI,EAAEC,YAAY,GAAG,KAAK,KAAK;IACpE,IAAI;MACFC,OAAO,CAACC,GAAG,CAAC,uBAAuB,EAAE;QAAEJ,UAAU;QAAEC,QAAQ;QAAEC;MAAa,CAAC,CAAC;MAC5EC,OAAO,CAACC,GAAG,CAAC,qBAAqB,EAAE,OAAOJ,UAAU,CAAC;;MAErD;MACA,IAAIK,aAAa,GAAGL,UAAU;MAC9B,IAAI,OAAOA,UAAU,KAAK,QAAQ,EAAE;QAClCK,aAAa,GAAGC,QAAQ,CAACN,UAAU,EAAE,EAAE,CAAC;QACxCG,OAAO,CAACC,GAAG,CAAC,4CAA4C,EAAEC,aAAa,CAAC;MAC1E;MAEA,IAAIE,KAAK,CAACF,aAAa,CAAC,EAAE;QACxBF,OAAO,CAACR,KAAK,CAAC,qCAAqC,EAAEK,UAAU,CAAC;QAChE,MAAM,IAAIQ,KAAK,CAAC,2BAA2BR,UAAU,EAAE,CAAC;MAC1D;MAEA,IAAIS,GAAG,GAAG,SAASJ,aAAa,EAAE;MAClC,IAAIK,MAAM,GAAG,EAAE;MAEf,IAAIT,QAAQ,KAAK,IAAI,EAAE;QACrBS,MAAM,CAACC,IAAI,CAAC,aAAaV,QAAQ,EAAE,CAAC;MACtC;MAEA,IAAIC,YAAY,EAAE;QAChBQ,MAAM,CAACC,IAAI,CAAC,oBAAoB,CAAC;MACnC;MAEA,IAAID,MAAM,CAACE,MAAM,GAAG,CAAC,EAAE;QACrBH,GAAG,IAAI,GAAG,GAAGC,MAAM,CAACG,IAAI,CAAC,GAAG,CAAC;MAC/B;MAEAV,OAAO,CAACC,GAAG,CAAC,qBAAqBK,GAAG,EAAE,CAAC;MACvCN,OAAO,CAACC,GAAG,CAAC,QAAQ,EAAEZ,YAAY,CAACC,OAAO,CAAC,OAAO,CAAC,GAAG,UAAU,GAAG,UAAU,CAAC;MAC9EU,OAAO,CAACC,GAAG,CAAC,eAAe,EAAE,GAAGxB,OAAO,GAAG6B,GAAG,EAAE,CAAC;MAEhD,IAAI;QACFN,OAAO,CAACC,GAAG,CAAC,kCAAkCK,GAAG,eAAejB,YAAY,CAACC,OAAO,CAAC,OAAO,CAAC,GAAG,UAAU,GAAG,UAAU,EAAE,CAAC;QAC1HU,OAAO,CAACC,GAAG,CAAC,0BAA0B,EAAE;UACtC,cAAc,EAAE,kBAAkB;UAClC,eAAe,EAAE,UAAUZ,YAAY,CAACC,OAAO,CAAC,OAAO,CAAC;QAC1D,CAAC,CAAC;;QAEF;QACA,MAAMqB,QAAQ,GAAG,MAAMjC,aAAa,CAACkC,GAAG,CAACN,GAAG,EAAE;UAAExB,OAAO,EAAE;QAAM,CAAC,CAAC;QAEjEkB,OAAO,CAACC,GAAG,CAAC,iBAAiBK,GAAG,EAAE,EAAEK,QAAQ,CAACE,IAAI,CAAC;QAClDb,OAAO,CAACC,GAAG,CAAC,wBAAwB,EAAEU,QAAQ,CAACG,MAAM,CAAC;QACtDd,OAAO,CAACC,GAAG,CAAC,yBAAyB,EAAEU,QAAQ,CAAC9B,OAAO,CAAC;QAExD,IAAIkC,KAAK,CAACC,OAAO,CAACL,QAAQ,CAACE,IAAI,CAAC,EAAE;UAChCb,OAAO,CAACC,GAAG,CAAC,4BAA4BU,QAAQ,CAACE,IAAI,CAACJ,MAAM,EAAE,CAAC;UAC/D,IAAIE,QAAQ,CAACE,IAAI,CAACJ,MAAM,GAAG,CAAC,EAAE;YAC5BT,OAAO,CAACC,GAAG,CAAC,sBAAsB,EAAEU,QAAQ,CAACE,IAAI,CAAC,CAAC,CAAC,CAAC;UACvD,CAAC,MAAM;YACLb,OAAO,CAACiB,IAAI,CAAC,uCAAuCf,aAAa,aAAaJ,QAAQ,EAAE,CAAC;UAC3F;QACF,CAAC,MAAM;UACLE,OAAO,CAACiB,IAAI,CAAC,4BAA4B,OAAON,QAAQ,CAACE,IAAI,EAAE,EAAEF,QAAQ,CAACE,IAAI,CAAC;QACjF;QAEA,OAAOF,QAAQ,CAACE,IAAI;MACtB,CAAC,CAAC,OAAOK,QAAQ,EAAE;QAAA,IAAAC,kBAAA,EAAAC,mBAAA,EAAAC,mBAAA,EAAAC,mBAAA;QACjBtB,OAAO,CAACR,KAAK,CAAC,iCAAiCc,GAAG,GAAG,EAAEY,QAAQ,CAAC;QAChElB,OAAO,CAACR,KAAK,CAAC,sBAAsB,EAAE;UACpC+B,OAAO,EAAEL,QAAQ,CAACK,OAAO;UACzBT,MAAM,GAAAK,kBAAA,GAAED,QAAQ,CAACP,QAAQ,cAAAQ,kBAAA,uBAAjBA,kBAAA,CAAmBL,MAAM;UACjCU,UAAU,GAAAJ,mBAAA,GAAEF,QAAQ,CAACP,QAAQ,cAAAS,mBAAA,uBAAjBA,mBAAA,CAAmBI,UAAU;UACzCX,IAAI,GAAAQ,mBAAA,GAAEH,QAAQ,CAACP,QAAQ,cAAAU,mBAAA,uBAAjBA,mBAAA,CAAmBR,IAAI;UAC7BhC,OAAO,GAAAyC,mBAAA,GAAEJ,QAAQ,CAACP,QAAQ,cAAAW,mBAAA,uBAAjBA,mBAAA,CAAmBzC,OAAO;UACnC4C,IAAI,EAAEP,QAAQ,CAACO,IAAI;UACnBC,YAAY,EAAER,QAAQ,CAACQ,YAAY;UACnCvC,MAAM,EAAE+B,QAAQ,CAAC/B,MAAM,GAAG;YACxBmB,GAAG,EAAEY,QAAQ,CAAC/B,MAAM,CAACmB,GAAG;YACxBqB,MAAM,EAAET,QAAQ,CAAC/B,MAAM,CAACwC,MAAM;YAC9B7C,OAAO,EAAEoC,QAAQ,CAAC/B,MAAM,CAACL,OAAO;YAChCD,OAAO,EAAEqC,QAAQ,CAAC/B,MAAM,CAACN;UAC3B,CAAC,GAAG;QACN,CAAC,CAAC;;QAEF;QACA,IAAIqC,QAAQ,CAACO,IAAI,KAAK,aAAa,EAAE;UACnCzB,OAAO,CAACR,KAAK,CAAC,0EAA0E,CAAC;UACzF;UACA,IAAI;YACFQ,OAAO,CAACC,GAAG,CAAC,gDAAgD,CAAC;YAC7D,MAAM2B,YAAY,GAAG,MAAMC,KAAK,CAACpD,OAAO,CAAC;YACzCuB,OAAO,CAACC,GAAG,CAAC,iCAAiC,EAAE2B,YAAY,CAACd,MAAM,CAAC;UACrE,CAAC,CAAC,OAAOgB,SAAS,EAAE;YAClB9B,OAAO,CAACR,KAAK,CAAC,yCAAyC,EAAEsC,SAAS,CAAC;UACrE;QACF;QAEA,MAAMZ,QAAQ;MAChB;IACF,CAAC,CAAC,OAAO1B,KAAK,EAAE;MAAA,IAAAuC,eAAA,EAAAC,gBAAA,EAAAC,gBAAA,EAAAC,gBAAA,EAAAC,qBAAA,EAAAC,gBAAA,EAAAC,gBAAA;MACdrC,OAAO,CAACR,KAAK,CAAC,iBAAiB,EAAEA,KAAK,CAAC;MACvCQ,OAAO,CAACR,KAAK,CAAC,gBAAgB,EAAE;QAC9B+B,OAAO,EAAE/B,KAAK,CAAC+B,OAAO;QACtBT,MAAM,GAAAiB,eAAA,GAAEvC,KAAK,CAACmB,QAAQ,cAAAoB,eAAA,uBAAdA,eAAA,CAAgBjB,MAAM;QAC9BU,UAAU,GAAAQ,gBAAA,GAAExC,KAAK,CAACmB,QAAQ,cAAAqB,gBAAA,uBAAdA,gBAAA,CAAgBR,UAAU;QACtCX,IAAI,GAAAoB,gBAAA,GAAEzC,KAAK,CAACmB,QAAQ,cAAAsB,gBAAA,uBAAdA,gBAAA,CAAgBpB,IAAI;QAC1BP,GAAG,EAAE,SAAST,UAAU,GAAGC,QAAQ,KAAK,IAAI,GAAG,cAAcA,QAAQ,EAAE,GAAG,EAAE,EAAE;QAC9EwC,KAAK,EAAE9C,KAAK,CAAC8C;MACf,CAAC,CAAC;;MAEF;MACA,MAAMC,aAAa,GAAG,IAAIlC,KAAK,CAAC,EAAA6B,gBAAA,GAAA1C,KAAK,CAACmB,QAAQ,cAAAuB,gBAAA,wBAAAC,qBAAA,GAAdD,gBAAA,CAAgBrB,IAAI,cAAAsB,qBAAA,uBAApBA,qBAAA,CAAsBK,MAAM,KAAIhD,KAAK,CAAC+B,OAAO,IAAI,oBAAoB,CAAC;MACtGgB,aAAa,CAACzB,MAAM,IAAAsB,gBAAA,GAAG5C,KAAK,CAACmB,QAAQ,cAAAyB,gBAAA,uBAAdA,gBAAA,CAAgBtB,MAAM;MAC7CyB,aAAa,CAAC1B,IAAI,IAAAwB,gBAAA,GAAG7C,KAAK,CAACmB,QAAQ,cAAA0B,gBAAA,uBAAdA,gBAAA,CAAgBxB,IAAI;MACzC0B,aAAa,CAAC5B,QAAQ,GAAGnB,KAAK,CAACmB,QAAQ;MACvC4B,aAAa,CAACE,aAAa,GAAGjD,KAAK;MACnC+C,aAAa,CAACd,IAAI,GAAGjC,KAAK,CAACiC,IAAI;MAC/Bc,aAAa,CAACb,YAAY,GAAGlC,KAAK,CAACkC,YAAY;MAE/C,MAAMa,aAAa;IACrB;EACF,CAAC;EAED;EACAG,UAAU,EAAE,MAAAA,CAAO7C,UAAU,EAAE8C,QAAQ,KAAK;IAC1C,IAAI;MACF;MACA,MAAMzC,aAAa,GAAGC,QAAQ,CAACN,UAAU,EAAE,EAAE,CAAC;MAC9C,IAAIO,KAAK,CAACF,aAAa,CAAC,EAAE;QACxB,MAAM,IAAIG,KAAK,CAAC,2BAA2BR,UAAU,EAAE,CAAC;MAC1D;MAEA,MAAMc,QAAQ,GAAG,MAAMjC,aAAa,CAACkE,IAAI,CAAC,SAAS1C,aAAa,EAAE,EAAEyC,QAAQ,CAAC;MAC7E,OAAOhC,QAAQ,CAACE,IAAI;IACtB,CAAC,CAAC,OAAOrB,KAAK,EAAE;MACdQ,OAAO,CAACR,KAAK,CAAC,oBAAoB,EAAEA,KAAK,CAAC;MAC1C,MAAMA,KAAK,CAACmB,QAAQ,GAAGnB,KAAK,CAACmB,QAAQ,CAACE,IAAI,GAAGrB,KAAK;IACpD;EACF,CAAC;EAED;EACAqD,UAAU,EAAE,MAAAA,CAAOhD,UAAU,EAAEiD,MAAM,EAAEH,QAAQ,KAAK;IAClD,IAAI;MACF;MACA,MAAMzC,aAAa,GAAGC,QAAQ,CAACN,UAAU,EAAE,EAAE,CAAC;MAC9C,IAAIO,KAAK,CAACF,aAAa,CAAC,EAAE;QACxB,MAAM,IAAIG,KAAK,CAAC,2BAA2BR,UAAU,EAAE,CAAC;MAC1D;MAEA,MAAMc,QAAQ,GAAG,MAAMjC,aAAa,CAACqE,GAAG,CAAC,SAAS7C,aAAa,IAAI4C,MAAM,EAAE,EAAEH,QAAQ,CAAC;MACtF,OAAOhC,QAAQ,CAACE,IAAI;IACtB,CAAC,CAAC,OAAOrB,KAAK,EAAE;MACdQ,OAAO,CAACR,KAAK,CAAC,oBAAoB,EAAEA,KAAK,CAAC;MAC1C,MAAMA,KAAK,CAACmB,QAAQ,GAAGnB,KAAK,CAACmB,QAAQ,CAACE,IAAI,GAAGrB,KAAK;IACpD;EACF,CAAC;EAED;EACAwD,UAAU,EAAE,MAAAA,CAAOnD,UAAU,EAAEiD,MAAM,KAAK;IACxC,IAAI;MACF;MACA,MAAM5C,aAAa,GAAGC,QAAQ,CAACN,UAAU,EAAE,EAAE,CAAC;MAC9C,IAAIO,KAAK,CAACF,aAAa,CAAC,EAAE;QACxB,MAAM,IAAIG,KAAK,CAAC,2BAA2BR,UAAU,EAAE,CAAC;MAC1D;MAEA,MAAMc,QAAQ,GAAG,MAAMjC,aAAa,CAACuE,MAAM,CAAC,SAAS/C,aAAa,IAAI4C,MAAM,EAAE,CAAC;MAC/E,OAAOnC,QAAQ,CAACE,IAAI;IACtB,CAAC,CAAC,OAAOrB,KAAK,EAAE;MACdQ,OAAO,CAACR,KAAK,CAAC,oBAAoB,EAAEA,KAAK,CAAC;MAC1C,MAAMA,KAAK,CAACmB,QAAQ,GAAGnB,KAAK,CAACmB,QAAQ,CAACE,IAAI,GAAGrB,KAAK;IACpD;EACF,CAAC;EAED;EACA0D,iBAAiB,EAAE,MAAAA,CAAOrD,UAAU,EAAEiD,MAAM,EAAEK,WAAW,KAAK;IAC5D,IAAI;MACF;MACA,MAAMjD,aAAa,GAAGC,QAAQ,CAACN,UAAU,EAAE,EAAE,CAAC;MAC9C,IAAIO,KAAK,CAACF,aAAa,CAAC,EAAE;QACxB,MAAM,IAAIG,KAAK,CAAC,2BAA2BR,UAAU,EAAE,CAAC;MAC1D;MAEA,MAAMc,QAAQ,GAAG,MAAMjC,aAAa,CAACkE,IAAI,CAAC,SAAS1C,aAAa,IAAI4C,MAAM,eAAe,EAAE;QACzFM,YAAY,EAAED;MAChB,CAAC,CAAC;MACF,OAAOxC,QAAQ,CAACE,IAAI;IACtB,CAAC,CAAC,OAAOrB,KAAK,EAAE;MACdQ,OAAO,CAACR,KAAK,CAAC,4BAA4B,EAAEA,KAAK,CAAC;MAClD,MAAMA,KAAK,CAACmB,QAAQ,GAAGnB,KAAK,CAACmB,QAAQ,CAACE,IAAI,GAAGrB,KAAK;IACpD;EACF,CAAC;EAED;EACA6D,YAAY,EAAE,MAAAA,CAAOxD,UAAU,EAAEiD,MAAM,EAAEQ,QAAQ,KAAK;IACpD,IAAI;MACF;MACA,MAAMpD,aAAa,GAAGC,QAAQ,CAACN,UAAU,EAAE,EAAE,CAAC;MAC9C,IAAIO,KAAK,CAACF,aAAa,CAAC,EAAE;QACxB,MAAM,IAAIG,KAAK,CAAC,2BAA2BR,UAAU,EAAE,CAAC;MAC1D;MAEA,MAAMc,QAAQ,GAAG,MAAMjC,aAAa,CAACkE,IAAI,CAAC,SAAS1C,aAAa,IAAI4C,MAAM,SAAS,EAAE;QACnFS,SAAS,EAAED;MACb,CAAC,CAAC;MACF,OAAO3C,QAAQ,CAACE,IAAI;IACtB,CAAC,CAAC,OAAOrB,KAAK,EAAE;MACdQ,OAAO,CAACR,KAAK,CAAC,sBAAsB,EAAEA,KAAK,CAAC;MAC5C,MAAMA,KAAK,CAACmB,QAAQ,GAAGnB,KAAK,CAACmB,QAAQ,CAACE,IAAI,GAAGrB,KAAK;IACpD;EACF,CAAC;EAED;EACAgE,iBAAiB,EAAE,MAAO3D,UAAU,IAAK;IACvC,IAAI;MACF;MACA,MAAMK,aAAa,GAAGC,QAAQ,CAACN,UAAU,EAAE,EAAE,CAAC;MAC9C,IAAIO,KAAK,CAACF,aAAa,CAAC,EAAE;QACxB,MAAM,IAAIG,KAAK,CAAC,2BAA2BR,UAAU,EAAE,CAAC;MAC1D;MAEA,MAAMc,QAAQ,GAAG,MAAMjC,aAAa,CAACkC,GAAG,CAAC,SAASV,aAAa,aAAa,CAAC;MAC7E,OAAOS,QAAQ,CAACE,IAAI;IACtB,CAAC,CAAC,OAAOrB,KAAK,EAAE;MACdQ,OAAO,CAACR,KAAK,CAAC,4BAA4B,EAAEA,KAAK,CAAC;MAClD,MAAMA,KAAK,CAACmB,QAAQ,GAAGnB,KAAK,CAACmB,QAAQ,CAACE,IAAI,GAAGrB,KAAK;IACpD;EACF,CAAC;EAED;EACAiE,WAAW,EAAE,MAAAA,CAAO5D,UAAU,EAAEiD,MAAM,EAAEY,IAAI,EAAEC,YAAY,KAAK;IAC7D,IAAI;MACF;MACA,MAAMzD,aAAa,GAAGC,QAAQ,CAACN,UAAU,EAAE,EAAE,CAAC;MAC9C,IAAIO,KAAK,CAACF,aAAa,CAAC,EAAE;QACxB,MAAM,IAAIG,KAAK,CAAC,2BAA2BR,UAAU,EAAE,CAAC;MAC1D;MAEA,MAAMc,QAAQ,GAAG,MAAMjC,aAAa,CAACkE,IAAI,CAAC,SAAS1C,aAAa,IAAI4C,MAAM,eAAe,EAAE;QACzFY,IAAI,EAAEA,IAAI;QACVC,YAAY,EAAEA,YAAY,IAAI;MAChC,CAAC,CAAC;MACF,OAAOhD,QAAQ,CAACE,IAAI;IACtB,CAAC,CAAC,OAAOrB,KAAK,EAAE;MACdQ,OAAO,CAACR,KAAK,CAAC,qBAAqB,EAAEA,KAAK,CAAC;MAC3C,MAAMA,KAAK,CAACmB,QAAQ,GAAGnB,KAAK,CAACmB,QAAQ,CAACE,IAAI,GAAGrB,KAAK;IACpD;EACF,CAAC;EAED;EACAoE,YAAY,EAAE,MAAAA,CAAO/D,UAAU,EAAEiD,MAAM,EAAEY,IAAI,KAAK;IAChD,IAAI;MACF;MACA,MAAMxD,aAAa,GAAGC,QAAQ,CAACN,UAAU,EAAE,EAAE,CAAC;MAC9C,IAAIO,KAAK,CAACF,aAAa,CAAC,EAAE;QACxB,MAAM,IAAIG,KAAK,CAAC,2BAA2BR,UAAU,EAAE,CAAC;MAC1D;MAEA,MAAMc,QAAQ,GAAG,MAAMjC,aAAa,CAACuE,MAAM,CAAC,SAAS/C,aAAa,IAAI4C,MAAM,iBAAiBY,IAAI,EAAE,CAAC;MACpG,OAAO/C,QAAQ,CAACE,IAAI;IACtB,CAAC,CAAC,OAAOrB,KAAK,EAAE;MACdQ,OAAO,CAACR,KAAK,CAAC,sBAAsB,EAAEA,KAAK,CAAC;MAC5C,MAAMA,KAAK,CAACmB,QAAQ,GAAGnB,KAAK,CAACmB,QAAQ,CAACE,IAAI,GAAGrB,KAAK;IACpD;EACF;AACF,CAAC;AAED,eAAeG,WAAW", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}