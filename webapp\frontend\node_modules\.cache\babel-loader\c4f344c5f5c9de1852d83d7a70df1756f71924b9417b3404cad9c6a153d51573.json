{"ast": null, "code": "import React,{useState,useEffect}from'react';import{Box,Typography,IconButton,Alert,Snackbar}from'@mui/material';import{ArrowBack as ArrowBackIcon,Refresh as RefreshIcon}from'@mui/icons-material';import{useNavigate}from'react-router-dom';import{useAuth}from'../../../context/AuthContext';import AdminHomeButton from'../../../components/common/AdminHomeButton';import ParcoCavi from'../../../components/cavi/ParcoCavi';import{jsx as _jsx,jsxs as _jsxs}from\"react/jsx-runtime\";const CreaBobinaPage=()=>{const navigate=useNavigate();const{selectedCantiere}=useAuth();const[successMessage,setSuccessMessage]=useState('');const[errorMessage,setErrorMessage]=useState('');const[openSuccess,setOpenSuccess]=useState(false);const[openError,setOpenError]=useState(false);// Recupera l'ID del cantiere dal localStorage se non è disponibile nel contesto\nconst cantiereId=selectedCantiere?selectedCantiere.id_cantiere:parseInt(localStorage.getItem('selectedCantiereId'),10);// Aggiungi un event listener per gestire il reindirizzamento dopo la creazione della bobina\nuseEffect(()=>{const handleRedirect=event=>{// Reindirizza alla pagina di visualizzazione bobine\nnavigate('/dashboard/cavi/parco/visualizza');};// Aggiungi l'event listener\nwindow.addEventListener('redirectToVisualizzaBobine',handleRedirect);// Rimuovi l'event listener quando il componente viene smontato\nreturn()=>{window.removeEventListener('redirectToVisualizzaBobine',handleRedirect);};},[navigate]);// Verifica se un cantiere è selezionato\nif(!cantiereId||isNaN(cantiereId)){return/*#__PURE__*/_jsx(Box,{sx:{p:3},children:/*#__PURE__*/_jsx(Alert,{severity:\"warning\",children:\"Nessun cantiere selezionato. Seleziona un cantiere per gestire il parco cavi.\"})});}// Gestisce il ritorno alla pagina dei cavi\nconst handleBackToCantieri=()=>{navigate('/dashboard/cavi');};// Gestisce i messaggi di successo\nconst handleSuccess=message=>{setSuccessMessage(message);setOpenSuccess(true);};// Gestisce i messaggi di errore\nconst handleError=message=>{setErrorMessage(message);setOpenError(true);};// Chiude i messaggi\nconst handleCloseSuccess=()=>{setOpenSuccess(false);};const handleCloseError=()=>{setOpenError(false);};return/*#__PURE__*/_jsxs(Box,{children:[/*#__PURE__*/_jsxs(Box,{sx:{mb:3,display:'flex',alignItems:'center',justifyContent:'space-between'},children:[/*#__PURE__*/_jsxs(Box,{sx:{display:'flex',alignItems:'center'},children:[/*#__PURE__*/_jsx(IconButton,{onClick:handleBackToCantieri,sx:{mr:1},children:/*#__PURE__*/_jsx(ArrowBackIcon,{})}),/*#__PURE__*/_jsx(Typography,{variant:\"h4\",children:\"Crea Nuova Bobina\"}),/*#__PURE__*/_jsx(IconButton,{onClick:()=>window.location.reload(),sx:{ml:2},color:\"primary\",title:\"Ricarica la pagina\",children:/*#__PURE__*/_jsx(RefreshIcon,{})})]}),/*#__PURE__*/_jsx(AdminHomeButton,{})]}),cantiereId&&/*#__PURE__*/_jsx(ParcoCavi,{cantiereId:cantiereId,onSuccess:handleSuccess,onError:handleError,initialOption:\"creaBobina\"}),/*#__PURE__*/_jsx(Snackbar,{open:openSuccess,autoHideDuration:6000,onClose:handleCloseSuccess,anchorOrigin:{vertical:'bottom',horizontal:'center'},children:/*#__PURE__*/_jsx(Alert,{onClose:handleCloseSuccess,severity:\"success\",sx:{width:'100%'},children:successMessage})}),/*#__PURE__*/_jsx(Snackbar,{open:openError,autoHideDuration:6000,onClose:handleCloseError,anchorOrigin:{vertical:'bottom',horizontal:'center'},children:/*#__PURE__*/_jsx(Alert,{onClose:handleCloseError,severity:\"error\",sx:{width:'100%'},children:errorMessage})})]});};export default CreaBobinaPage;", "map": {"version": 3, "names": ["React", "useState", "useEffect", "Box", "Typography", "IconButton", "<PERSON><PERSON>", "Snackbar", "ArrowBack", "ArrowBackIcon", "Refresh", "RefreshIcon", "useNavigate", "useAuth", "AdminHomeButton", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "jsx", "_jsx", "jsxs", "_jsxs", "CreaBobinaPage", "navigate", "selected<PERSON><PERSON><PERSON>", "successMessage", "setSuccessMessage", "errorMessage", "setErrorMessage", "openSuccess", "setOpenSuccess", "openError", "set<PERSON>pen<PERSON>rror", "cantiereId", "id_cantiere", "parseInt", "localStorage", "getItem", "handleRedirect", "event", "window", "addEventListener", "removeEventListener", "isNaN", "sx", "p", "children", "severity", "handleBackToCantieri", "handleSuccess", "message", "handleError", "handleCloseSuccess", "handleCloseError", "mb", "display", "alignItems", "justifyContent", "onClick", "mr", "variant", "location", "reload", "ml", "color", "title", "onSuccess", "onError", "initialOption", "open", "autoHideDuration", "onClose", "anchor<PERSON><PERSON><PERSON>", "vertical", "horizontal", "width"], "sources": ["C:/Users/<USER>/PycharmProjects/CMS/webapp/frontend/src/pages/cavi/parco/CreaBobinaPage.js"], "sourcesContent": ["import React, { useState, useEffect } from 'react';\nimport {\n  Box,\n  Typography,\n  IconButton,\n  Alert,\n  Snackbar\n} from '@mui/material';\nimport {\n  ArrowBack as ArrowBackIcon,\n  Refresh as RefreshIcon\n} from '@mui/icons-material';\nimport { useNavigate } from 'react-router-dom';\nimport { useAuth } from '../../../context/AuthContext';\nimport AdminHomeButton from '../../../components/common/AdminHomeButton';\nimport ParcoCavi from '../../../components/cavi/ParcoCavi';\n\nconst CreaBobinaPage = () => {\n  const navigate = useNavigate();\n  const { selectedCantiere } = useAuth();\n  const [successMessage, setSuccessMessage] = useState('');\n  const [errorMessage, setErrorMessage] = useState('');\n  const [openSuccess, setOpenSuccess] = useState(false);\n  const [openError, setOpenError] = useState(false);\n\n  // Recupera l'ID del cantiere dal localStorage se non è disponibile nel contesto\n  const cantiereId = selectedCantiere ? selectedCantiere.id_cantiere : parseInt(localStorage.getItem('selectedCantiereId'), 10);\n\n  // Aggiungi un event listener per gestire il reindirizzamento dopo la creazione della bobina\n  useEffect(() => {\n    const handleRedirect = (event) => {\n      // Reindirizza alla pagina di visualizzazione bobine\n      navigate('/dashboard/cavi/parco/visualizza');\n    };\n\n    // Aggiungi l'event listener\n    window.addEventListener('redirectToVisualizzaBobine', handleRedirect);\n\n    // Rimuovi l'event listener quando il componente viene smontato\n    return () => {\n      window.removeEventListener('redirectToVisualizzaBobine', handleRedirect);\n    };\n  }, [navigate]);\n\n  // Verifica se un cantiere è selezionato\n  if (!cantiereId || isNaN(cantiereId)) {\n    return (\n      <Box sx={{ p: 3 }}>\n        <Alert severity=\"warning\">\n          Nessun cantiere selezionato. Seleziona un cantiere per gestire il parco cavi.\n        </Alert>\n      </Box>\n    );\n  }\n\n  // Gestisce il ritorno alla pagina dei cavi\n  const handleBackToCantieri = () => {\n    navigate('/dashboard/cavi');\n  };\n\n  // Gestisce i messaggi di successo\n  const handleSuccess = (message) => {\n    setSuccessMessage(message);\n    setOpenSuccess(true);\n  };\n\n  // Gestisce i messaggi di errore\n  const handleError = (message) => {\n    setErrorMessage(message);\n    setOpenError(true);\n  };\n\n  // Chiude i messaggi\n  const handleCloseSuccess = () => {\n    setOpenSuccess(false);\n  };\n\n  const handleCloseError = () => {\n    setOpenError(false);\n  };\n\n  return (\n    <Box>\n      <Box sx={{ mb: 3, display: 'flex', alignItems: 'center', justifyContent: 'space-between' }}>\n        <Box sx={{ display: 'flex', alignItems: 'center' }}>\n          <IconButton onClick={handleBackToCantieri} sx={{ mr: 1 }}>\n            <ArrowBackIcon />\n          </IconButton>\n          <Typography variant=\"h4\">\n            Crea Nuova Bobina\n          </Typography>\n          <IconButton\n            onClick={() => window.location.reload()}\n            sx={{ ml: 2 }}\n            color=\"primary\"\n            title=\"Ricarica la pagina\"\n          >\n            <RefreshIcon />\n          </IconButton>\n        </Box>\n        <AdminHomeButton />\n      </Box>\n\n      {cantiereId && (\n        <ParcoCavi\n          cantiereId={cantiereId}\n          onSuccess={handleSuccess}\n          onError={handleError}\n          initialOption=\"creaBobina\"\n        />\n      )}\n\n      {/* Snackbar per i messaggi di successo */}\n      <Snackbar\n        open={openSuccess}\n        autoHideDuration={6000}\n        onClose={handleCloseSuccess}\n        anchorOrigin={{ vertical: 'bottom', horizontal: 'center' }}\n      >\n        <Alert onClose={handleCloseSuccess} severity=\"success\" sx={{ width: '100%' }}>\n          {successMessage}\n        </Alert>\n      </Snackbar>\n\n      {/* Snackbar per i messaggi di errore */}\n      <Snackbar\n        open={openError}\n        autoHideDuration={6000}\n        onClose={handleCloseError}\n        anchorOrigin={{ vertical: 'bottom', horizontal: 'center' }}\n      >\n        <Alert onClose={handleCloseError} severity=\"error\" sx={{ width: '100%' }}>\n          {errorMessage}\n        </Alert>\n      </Snackbar>\n    </Box>\n  );\n};\n\nexport default CreaBobinaPage;\n"], "mappings": "AAAA,MAAO,CAAAA,KAAK,EAAIC,QAAQ,CAAEC,SAAS,KAAQ,OAAO,CAClD,OACEC,GAAG,CACHC,UAAU,CACVC,UAAU,CACVC,KAAK,CACLC,QAAQ,KACH,eAAe,CACtB,OACEC,SAAS,GAAI,CAAAC,aAAa,CAC1BC,OAAO,GAAI,CAAAC,WAAW,KACjB,qBAAqB,CAC5B,OAASC,WAAW,KAAQ,kBAAkB,CAC9C,OAASC,OAAO,KAAQ,8BAA8B,CACtD,MAAO,CAAAC,eAAe,KAAM,4CAA4C,CACxE,MAAO,CAAAC,SAAS,KAAM,oCAAoC,CAAC,OAAAC,GAAA,IAAAC,IAAA,CAAAC,IAAA,IAAAC,KAAA,yBAE3D,KAAM,CAAAC,cAAc,CAAGA,CAAA,GAAM,CAC3B,KAAM,CAAAC,QAAQ,CAAGT,WAAW,CAAC,CAAC,CAC9B,KAAM,CAAEU,gBAAiB,CAAC,CAAGT,OAAO,CAAC,CAAC,CACtC,KAAM,CAACU,cAAc,CAAEC,iBAAiB,CAAC,CAAGvB,QAAQ,CAAC,EAAE,CAAC,CACxD,KAAM,CAACwB,YAAY,CAAEC,eAAe,CAAC,CAAGzB,QAAQ,CAAC,EAAE,CAAC,CACpD,KAAM,CAAC0B,WAAW,CAAEC,cAAc,CAAC,CAAG3B,QAAQ,CAAC,KAAK,CAAC,CACrD,KAAM,CAAC4B,SAAS,CAAEC,YAAY,CAAC,CAAG7B,QAAQ,CAAC,KAAK,CAAC,CAEjD;AACA,KAAM,CAAA8B,UAAU,CAAGT,gBAAgB,CAAGA,gBAAgB,CAACU,WAAW,CAAGC,QAAQ,CAACC,YAAY,CAACC,OAAO,CAAC,oBAAoB,CAAC,CAAE,EAAE,CAAC,CAE7H;AACAjC,SAAS,CAAC,IAAM,CACd,KAAM,CAAAkC,cAAc,CAAIC,KAAK,EAAK,CAChC;AACAhB,QAAQ,CAAC,kCAAkC,CAAC,CAC9C,CAAC,CAED;AACAiB,MAAM,CAACC,gBAAgB,CAAC,4BAA4B,CAAEH,cAAc,CAAC,CAErE;AACA,MAAO,IAAM,CACXE,MAAM,CAACE,mBAAmB,CAAC,4BAA4B,CAAEJ,cAAc,CAAC,CAC1E,CAAC,CACH,CAAC,CAAE,CAACf,QAAQ,CAAC,CAAC,CAEd;AACA,GAAI,CAACU,UAAU,EAAIU,KAAK,CAACV,UAAU,CAAC,CAAE,CACpC,mBACEd,IAAA,CAACd,GAAG,EAACuC,EAAE,CAAE,CAAEC,CAAC,CAAE,CAAE,CAAE,CAAAC,QAAA,cAChB3B,IAAA,CAACX,KAAK,EAACuC,QAAQ,CAAC,SAAS,CAAAD,QAAA,CAAC,+EAE1B,CAAO,CAAC,CACL,CAAC,CAEV,CAEA;AACA,KAAM,CAAAE,oBAAoB,CAAGA,CAAA,GAAM,CACjCzB,QAAQ,CAAC,iBAAiB,CAAC,CAC7B,CAAC,CAED;AACA,KAAM,CAAA0B,aAAa,CAAIC,OAAO,EAAK,CACjCxB,iBAAiB,CAACwB,OAAO,CAAC,CAC1BpB,cAAc,CAAC,IAAI,CAAC,CACtB,CAAC,CAED;AACA,KAAM,CAAAqB,WAAW,CAAID,OAAO,EAAK,CAC/BtB,eAAe,CAACsB,OAAO,CAAC,CACxBlB,YAAY,CAAC,IAAI,CAAC,CACpB,CAAC,CAED;AACA,KAAM,CAAAoB,kBAAkB,CAAGA,CAAA,GAAM,CAC/BtB,cAAc,CAAC,KAAK,CAAC,CACvB,CAAC,CAED,KAAM,CAAAuB,gBAAgB,CAAGA,CAAA,GAAM,CAC7BrB,YAAY,CAAC,KAAK,CAAC,CACrB,CAAC,CAED,mBACEX,KAAA,CAAChB,GAAG,EAAAyC,QAAA,eACFzB,KAAA,CAAChB,GAAG,EAACuC,EAAE,CAAE,CAAEU,EAAE,CAAE,CAAC,CAAEC,OAAO,CAAE,MAAM,CAAEC,UAAU,CAAE,QAAQ,CAAEC,cAAc,CAAE,eAAgB,CAAE,CAAAX,QAAA,eACzFzB,KAAA,CAAChB,GAAG,EAACuC,EAAE,CAAE,CAAEW,OAAO,CAAE,MAAM,CAAEC,UAAU,CAAE,QAAS,CAAE,CAAAV,QAAA,eACjD3B,IAAA,CAACZ,UAAU,EAACmD,OAAO,CAAEV,oBAAqB,CAACJ,EAAE,CAAE,CAAEe,EAAE,CAAE,CAAE,CAAE,CAAAb,QAAA,cACvD3B,IAAA,CAACR,aAAa,GAAE,CAAC,CACP,CAAC,cACbQ,IAAA,CAACb,UAAU,EAACsD,OAAO,CAAC,IAAI,CAAAd,QAAA,CAAC,mBAEzB,CAAY,CAAC,cACb3B,IAAA,CAACZ,UAAU,EACTmD,OAAO,CAAEA,CAAA,GAAMlB,MAAM,CAACqB,QAAQ,CAACC,MAAM,CAAC,CAAE,CACxClB,EAAE,CAAE,CAAEmB,EAAE,CAAE,CAAE,CAAE,CACdC,KAAK,CAAC,SAAS,CACfC,KAAK,CAAC,oBAAoB,CAAAnB,QAAA,cAE1B3B,IAAA,CAACN,WAAW,GAAE,CAAC,CACL,CAAC,EACV,CAAC,cACNM,IAAA,CAACH,eAAe,GAAE,CAAC,EAChB,CAAC,CAELiB,UAAU,eACTd,IAAA,CAACF,SAAS,EACRgB,UAAU,CAAEA,UAAW,CACvBiC,SAAS,CAAEjB,aAAc,CACzBkB,OAAO,CAAEhB,WAAY,CACrBiB,aAAa,CAAC,YAAY,CAC3B,CACF,cAGDjD,IAAA,CAACV,QAAQ,EACP4D,IAAI,CAAExC,WAAY,CAClByC,gBAAgB,CAAE,IAAK,CACvBC,OAAO,CAAEnB,kBAAmB,CAC5BoB,YAAY,CAAE,CAAEC,QAAQ,CAAE,QAAQ,CAAEC,UAAU,CAAE,QAAS,CAAE,CAAA5B,QAAA,cAE3D3B,IAAA,CAACX,KAAK,EAAC+D,OAAO,CAAEnB,kBAAmB,CAACL,QAAQ,CAAC,SAAS,CAACH,EAAE,CAAE,CAAE+B,KAAK,CAAE,MAAO,CAAE,CAAA7B,QAAA,CAC1ErB,cAAc,CACV,CAAC,CACA,CAAC,cAGXN,IAAA,CAACV,QAAQ,EACP4D,IAAI,CAAEtC,SAAU,CAChBuC,gBAAgB,CAAE,IAAK,CACvBC,OAAO,CAAElB,gBAAiB,CAC1BmB,YAAY,CAAE,CAAEC,QAAQ,CAAE,QAAQ,CAAEC,UAAU,CAAE,QAAS,CAAE,CAAA5B,QAAA,cAE3D3B,IAAA,CAACX,KAAK,EAAC+D,OAAO,CAAElB,gBAAiB,CAACN,QAAQ,CAAC,OAAO,CAACH,EAAE,CAAE,CAAE+B,KAAK,CAAE,MAAO,CAAE,CAAA7B,QAAA,CACtEnB,YAAY,CACR,CAAC,CACA,CAAC,EACR,CAAC,CAEV,CAAC,CAED,cAAe,CAAAL,cAAc", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}