{"ast": null, "code": "import React,{useState,useEffect}from'react';import{useParams}from'react-router-dom';import{Container,Typography,Box,Paper,Alert}from'@mui/material';import{jsx as _jsx,jsxs as _jsxs}from\"react/jsx-runtime\";function CertificazioniPageDebug(){const{cantiereId}=useParams();const[error,setError]=useState('');const[loading,setLoading]=useState(true);const[debugInfo,setDebugInfo]=useState([]);useEffect(()=>{const debugSteps=async()=>{const steps=[];try{steps.push('✅ Componente montato correttamente');steps.push(`✅ cantiereId ricevuto: ${cantiereId}`);// Test import servizi\ntry{const{apiService}=await import('../services/apiService');steps.push('✅ apiService importato correttamente');// Test metodi apiService\nif(typeof apiService.getCantiere==='function'){steps.push('✅ apiService.getCantiere esiste');}else{steps.push('❌ apiService.getCantiere non esiste');}if(typeof apiService.getCertificazioni==='function'){steps.push('✅ apiService.getCertificazioni esiste');}else{steps.push('❌ apiService.getCertificazioni non esiste');}if(typeof apiService.getStrumenti==='function'){steps.push('✅ apiService.getStrumenti esiste');}else{steps.push('❌ apiService.getStrumenti non esiste');}}catch(importError){steps.push(`❌ Errore import apiService: ${importError.message}`);}// Test import contesto auth\ntry{const{useAuth}=await import('../context/AuthContext');steps.push('✅ useAuth importato correttamente');}catch(authError){steps.push(`❌ Errore import useAuth: ${authError.message}`);}// Test import componenti\ntry{await import('../components/certificazioni/CertificazioniList');steps.push('✅ CertificazioniList importato correttamente');}catch(listError){steps.push(`❌ Errore import CertificazioniList: ${listError.message}`);}try{await import('../components/certificazioni/CertificazioneForm');steps.push('✅ CertificazioneForm importato correttamente');}catch(formError){steps.push(`❌ Errore import CertificazioneForm: ${formError.message}`);}try{await import('../components/certificazioni/StrumentiList');steps.push('✅ StrumentiList importato correttamente');}catch(strListError){steps.push(`❌ Errore import StrumentiList: ${strListError.message}`);}try{await import('../components/certificazioni/StrumentoForm');steps.push('✅ StrumentoForm importato correttamente');}catch(strFormError){steps.push(`❌ Errore import StrumentoForm: ${strFormError.message}`);}setDebugInfo(steps);setLoading(false);}catch(error){steps.push(`❌ Errore generale: ${error.message}`);setError(error.message);setDebugInfo(steps);setLoading(false);}};debugSteps();},[cantiereId]);return/*#__PURE__*/_jsxs(Container,{maxWidth:\"lg\",sx:{mt:4,mb:4},children:[/*#__PURE__*/_jsxs(Box,{sx:{mb:3},children:[/*#__PURE__*/_jsx(Typography,{variant:\"h4\",component:\"h1\",gutterBottom:true,children:\"Debug Certificazioni Cavi\"}),/*#__PURE__*/_jsxs(Typography,{variant:\"h6\",color:\"text.secondary\",children:[\"Cantiere ID: \",cantiereId]})]}),error&&/*#__PURE__*/_jsxs(Alert,{severity:\"error\",sx:{mb:2},children:[\"Errore: \",error]}),/*#__PURE__*/_jsxs(Paper,{sx:{p:3},children:[/*#__PURE__*/_jsx(Typography,{variant:\"h6\",gutterBottom:true,children:\"Risultati Debug\"}),loading?/*#__PURE__*/_jsx(Typography,{children:\"Esecuzione test...\"}):/*#__PURE__*/_jsx(Box,{children:debugInfo.map((step,index)=>/*#__PURE__*/_jsx(Typography,{variant:\"body2\",sx:{mb:1,fontFamily:'monospace'},children:step},index))})]})]});}export default CertificazioniPageDebug;", "map": {"version": 3, "names": ["React", "useState", "useEffect", "useParams", "Container", "Typography", "Box", "Paper", "<PERSON><PERSON>", "jsx", "_jsx", "jsxs", "_jsxs", "CertificazioniPageDebug", "cantiereId", "error", "setError", "loading", "setLoading", "debugInfo", "setDebugInfo", "debugSteps", "steps", "push", "apiService", "getCantiere", "getCertificazioni", "getStrumenti", "importError", "message", "useAuth", "authError", "listError", "formError", "strListError", "strFormError", "max<PERSON><PERSON><PERSON>", "sx", "mt", "mb", "children", "variant", "component", "gutterBottom", "color", "severity", "p", "map", "step", "index", "fontFamily"], "sources": ["C:/Users/<USER>/PycharmProjects/CMS/webapp/frontend/src/pages/CertificazioniPageDebug.jsx"], "sourcesContent": ["import React, { useState, useEffect } from 'react';\nimport { useParams } from 'react-router-dom';\nimport { Container, Typography, Box, Paper, Alert } from '@mui/material';\n\nfunction CertificazioniPageDebug() {\n  const { cantiereId } = useParams();\n  const [error, setError] = useState('');\n  const [loading, setLoading] = useState(true);\n  const [debugInfo, setDebugInfo] = useState([]);\n\n  useEffect(() => {\n    const debugSteps = async () => {\n      const steps = [];\n      \n      try {\n        steps.push('✅ Componente montato correttamente');\n        steps.push(`✅ cantiereId ricevuto: ${cantiereId}`);\n        \n        // Test import servizi\n        try {\n          const { apiService } = await import('../services/apiService');\n          steps.push('✅ apiService importato correttamente');\n          \n          // Test metodi apiService\n          if (typeof apiService.getCantiere === 'function') {\n            steps.push('✅ apiService.getCantiere esiste');\n          } else {\n            steps.push('❌ apiService.getCantiere non esiste');\n          }\n          \n          if (typeof apiService.getCertificazioni === 'function') {\n            steps.push('✅ apiService.getCertificazioni esiste');\n          } else {\n            steps.push('❌ apiService.getCertificazioni non esiste');\n          }\n          \n          if (typeof apiService.getStrumenti === 'function') {\n            steps.push('✅ apiService.getStrumenti esiste');\n          } else {\n            steps.push('❌ apiService.getStrumenti non esiste');\n          }\n          \n        } catch (importError) {\n          steps.push(`❌ Errore import apiService: ${importError.message}`);\n        }\n        \n        // Test import contesto auth\n        try {\n          const { useAuth } = await import('../context/AuthContext');\n          steps.push('✅ useAuth importato correttamente');\n        } catch (authError) {\n          steps.push(`❌ Errore import useAuth: ${authError.message}`);\n        }\n        \n        // Test import componenti\n        try {\n          await import('../components/certificazioni/CertificazioniList');\n          steps.push('✅ CertificazioniList importato correttamente');\n        } catch (listError) {\n          steps.push(`❌ Errore import CertificazioniList: ${listError.message}`);\n        }\n        \n        try {\n          await import('../components/certificazioni/CertificazioneForm');\n          steps.push('✅ CertificazioneForm importato correttamente');\n        } catch (formError) {\n          steps.push(`❌ Errore import CertificazioneForm: ${formError.message}`);\n        }\n        \n        try {\n          await import('../components/certificazioni/StrumentiList');\n          steps.push('✅ StrumentiList importato correttamente');\n        } catch (strListError) {\n          steps.push(`❌ Errore import StrumentiList: ${strListError.message}`);\n        }\n        \n        try {\n          await import('../components/certificazioni/StrumentoForm');\n          steps.push('✅ StrumentoForm importato correttamente');\n        } catch (strFormError) {\n          steps.push(`❌ Errore import StrumentoForm: ${strFormError.message}`);\n        }\n        \n        setDebugInfo(steps);\n        setLoading(false);\n        \n      } catch (error) {\n        steps.push(`❌ Errore generale: ${error.message}`);\n        setError(error.message);\n        setDebugInfo(steps);\n        setLoading(false);\n      }\n    };\n    \n    debugSteps();\n  }, [cantiereId]);\n\n  return (\n    <Container maxWidth=\"lg\" sx={{ mt: 4, mb: 4 }}>\n      <Box sx={{ mb: 3 }}>\n        <Typography variant=\"h4\" component=\"h1\" gutterBottom>\n          Debug Certificazioni Cavi\n        </Typography>\n        <Typography variant=\"h6\" color=\"text.secondary\">\n          Cantiere ID: {cantiereId}\n        </Typography>\n      </Box>\n\n      {error && (\n        <Alert severity=\"error\" sx={{ mb: 2 }}>\n          Errore: {error}\n        </Alert>\n      )}\n\n      <Paper sx={{ p: 3 }}>\n        <Typography variant=\"h6\" gutterBottom>\n          Risultati Debug\n        </Typography>\n        \n        {loading ? (\n          <Typography>Esecuzione test...</Typography>\n        ) : (\n          <Box>\n            {debugInfo.map((step, index) => (\n              <Typography key={index} variant=\"body2\" sx={{ mb: 1, fontFamily: 'monospace' }}>\n                {step}\n              </Typography>\n            ))}\n          </Box>\n        )}\n      </Paper>\n    </Container>\n  );\n}\n\nexport default CertificazioniPageDebug;\n"], "mappings": "AAAA,MAAO,CAAAA,KAAK,EAAIC,QAAQ,CAAEC,SAAS,KAAQ,OAAO,CAClD,OAASC,SAAS,KAAQ,kBAAkB,CAC5C,OAASC,SAAS,CAAEC,UAAU,CAAEC,GAAG,CAAEC,KAAK,CAAEC,KAAK,KAAQ,eAAe,CAAC,OAAAC,GAAA,IAAAC,IAAA,CAAAC,IAAA,IAAAC,KAAA,yBAEzE,QAAS,CAAAC,uBAAuBA,CAAA,CAAG,CACjC,KAAM,CAAEC,UAAW,CAAC,CAAGX,SAAS,CAAC,CAAC,CAClC,KAAM,CAACY,KAAK,CAAEC,QAAQ,CAAC,CAAGf,QAAQ,CAAC,EAAE,CAAC,CACtC,KAAM,CAACgB,OAAO,CAAEC,UAAU,CAAC,CAAGjB,QAAQ,CAAC,IAAI,CAAC,CAC5C,KAAM,CAACkB,SAAS,CAAEC,YAAY,CAAC,CAAGnB,QAAQ,CAAC,EAAE,CAAC,CAE9CC,SAAS,CAAC,IAAM,CACd,KAAM,CAAAmB,UAAU,CAAG,KAAAA,CAAA,GAAY,CAC7B,KAAM,CAAAC,KAAK,CAAG,EAAE,CAEhB,GAAI,CACFA,KAAK,CAACC,IAAI,CAAC,oCAAoC,CAAC,CAChDD,KAAK,CAACC,IAAI,CAAC,0BAA0BT,UAAU,EAAE,CAAC,CAElD;AACA,GAAI,CACF,KAAM,CAAEU,UAAW,CAAC,CAAG,KAAM,OAAM,CAAC,wBAAwB,CAAC,CAC7DF,KAAK,CAACC,IAAI,CAAC,sCAAsC,CAAC,CAElD;AACA,GAAI,MAAO,CAAAC,UAAU,CAACC,WAAW,GAAK,UAAU,CAAE,CAChDH,KAAK,CAACC,IAAI,CAAC,iCAAiC,CAAC,CAC/C,CAAC,IAAM,CACLD,KAAK,CAACC,IAAI,CAAC,qCAAqC,CAAC,CACnD,CAEA,GAAI,MAAO,CAAAC,UAAU,CAACE,iBAAiB,GAAK,UAAU,CAAE,CACtDJ,KAAK,CAACC,IAAI,CAAC,uCAAuC,CAAC,CACrD,CAAC,IAAM,CACLD,KAAK,CAACC,IAAI,CAAC,2CAA2C,CAAC,CACzD,CAEA,GAAI,MAAO,CAAAC,UAAU,CAACG,YAAY,GAAK,UAAU,CAAE,CACjDL,KAAK,CAACC,IAAI,CAAC,kCAAkC,CAAC,CAChD,CAAC,IAAM,CACLD,KAAK,CAACC,IAAI,CAAC,sCAAsC,CAAC,CACpD,CAEF,CAAE,MAAOK,WAAW,CAAE,CACpBN,KAAK,CAACC,IAAI,CAAC,+BAA+BK,WAAW,CAACC,OAAO,EAAE,CAAC,CAClE,CAEA;AACA,GAAI,CACF,KAAM,CAAEC,OAAQ,CAAC,CAAG,KAAM,OAAM,CAAC,wBAAwB,CAAC,CAC1DR,KAAK,CAACC,IAAI,CAAC,mCAAmC,CAAC,CACjD,CAAE,MAAOQ,SAAS,CAAE,CAClBT,KAAK,CAACC,IAAI,CAAC,4BAA4BQ,SAAS,CAACF,OAAO,EAAE,CAAC,CAC7D,CAEA;AACA,GAAI,CACF,KAAM,OAAM,CAAC,iDAAiD,CAAC,CAC/DP,KAAK,CAACC,IAAI,CAAC,8CAA8C,CAAC,CAC5D,CAAE,MAAOS,SAAS,CAAE,CAClBV,KAAK,CAACC,IAAI,CAAC,uCAAuCS,SAAS,CAACH,OAAO,EAAE,CAAC,CACxE,CAEA,GAAI,CACF,KAAM,OAAM,CAAC,iDAAiD,CAAC,CAC/DP,KAAK,CAACC,IAAI,CAAC,8CAA8C,CAAC,CAC5D,CAAE,MAAOU,SAAS,CAAE,CAClBX,KAAK,CAACC,IAAI,CAAC,uCAAuCU,SAAS,CAACJ,OAAO,EAAE,CAAC,CACxE,CAEA,GAAI,CACF,KAAM,OAAM,CAAC,4CAA4C,CAAC,CAC1DP,KAAK,CAACC,IAAI,CAAC,yCAAyC,CAAC,CACvD,CAAE,MAAOW,YAAY,CAAE,CACrBZ,KAAK,CAACC,IAAI,CAAC,kCAAkCW,YAAY,CAACL,OAAO,EAAE,CAAC,CACtE,CAEA,GAAI,CACF,KAAM,OAAM,CAAC,4CAA4C,CAAC,CAC1DP,KAAK,CAACC,IAAI,CAAC,yCAAyC,CAAC,CACvD,CAAE,MAAOY,YAAY,CAAE,CACrBb,KAAK,CAACC,IAAI,CAAC,kCAAkCY,YAAY,CAACN,OAAO,EAAE,CAAC,CACtE,CAEAT,YAAY,CAACE,KAAK,CAAC,CACnBJ,UAAU,CAAC,KAAK,CAAC,CAEnB,CAAE,MAAOH,KAAK,CAAE,CACdO,KAAK,CAACC,IAAI,CAAC,sBAAsBR,KAAK,CAACc,OAAO,EAAE,CAAC,CACjDb,QAAQ,CAACD,KAAK,CAACc,OAAO,CAAC,CACvBT,YAAY,CAACE,KAAK,CAAC,CACnBJ,UAAU,CAAC,KAAK,CAAC,CACnB,CACF,CAAC,CAEDG,UAAU,CAAC,CAAC,CACd,CAAC,CAAE,CAACP,UAAU,CAAC,CAAC,CAEhB,mBACEF,KAAA,CAACR,SAAS,EAACgC,QAAQ,CAAC,IAAI,CAACC,EAAE,CAAE,CAAEC,EAAE,CAAE,CAAC,CAAEC,EAAE,CAAE,CAAE,CAAE,CAAAC,QAAA,eAC5C5B,KAAA,CAACN,GAAG,EAAC+B,EAAE,CAAE,CAAEE,EAAE,CAAE,CAAE,CAAE,CAAAC,QAAA,eACjB9B,IAAA,CAACL,UAAU,EAACoC,OAAO,CAAC,IAAI,CAACC,SAAS,CAAC,IAAI,CAACC,YAAY,MAAAH,QAAA,CAAC,2BAErD,CAAY,CAAC,cACb5B,KAAA,CAACP,UAAU,EAACoC,OAAO,CAAC,IAAI,CAACG,KAAK,CAAC,gBAAgB,CAAAJ,QAAA,EAAC,eACjC,CAAC1B,UAAU,EACd,CAAC,EACV,CAAC,CAELC,KAAK,eACJH,KAAA,CAACJ,KAAK,EAACqC,QAAQ,CAAC,OAAO,CAACR,EAAE,CAAE,CAAEE,EAAE,CAAE,CAAE,CAAE,CAAAC,QAAA,EAAC,UAC7B,CAACzB,KAAK,EACT,CACR,cAEDH,KAAA,CAACL,KAAK,EAAC8B,EAAE,CAAE,CAAES,CAAC,CAAE,CAAE,CAAE,CAAAN,QAAA,eAClB9B,IAAA,CAACL,UAAU,EAACoC,OAAO,CAAC,IAAI,CAACE,YAAY,MAAAH,QAAA,CAAC,iBAEtC,CAAY,CAAC,CAEZvB,OAAO,cACNP,IAAA,CAACL,UAAU,EAAAmC,QAAA,CAAC,oBAAkB,CAAY,CAAC,cAE3C9B,IAAA,CAACJ,GAAG,EAAAkC,QAAA,CACDrB,SAAS,CAAC4B,GAAG,CAAC,CAACC,IAAI,CAAEC,KAAK,gBACzBvC,IAAA,CAACL,UAAU,EAAaoC,OAAO,CAAC,OAAO,CAACJ,EAAE,CAAE,CAAEE,EAAE,CAAE,CAAC,CAAEW,UAAU,CAAE,WAAY,CAAE,CAAAV,QAAA,CAC5EQ,IAAI,EADUC,KAEL,CACb,CAAC,CACC,CACN,EACI,CAAC,EACC,CAAC,CAEhB,CAEA,cAAe,CAAApC,uBAAuB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}