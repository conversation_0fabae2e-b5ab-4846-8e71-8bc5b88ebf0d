{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\PycharmProjects\\\\CMS\\\\webapp\\\\frontend\\\\src\\\\pages\\\\LoginPageNew.js\",\n  _s = $RefreshSig$();\nimport React, { useState } from 'react';\nimport { useNavigate } from 'react-router-dom';\nimport { Container, Box, Typography, TextField, Button, Paper, Card, CardContent, CardActions, Grid, Alert, CircularProgress, Dialog, DialogTitle, DialogContent, DialogContentText, DialogActions } from '@mui/material';\nimport { AdminPanelSettings as AdminIcon, Person as UserIcon, Construction as ConstructionIcon, Password as PasswordIcon } from '@mui/icons-material';\nimport { useAuth } from '../context/AuthContext';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst LoginPageNew = () => {\n  _s();\n  const [loginType, setLoginType] = useState(null);\n  const [loading, setLoading] = useState(false);\n  const [error, setError] = useState('');\n  const [showPasswordRecoveryDialog, setShowPasswordRecoveryDialog] = useState(false);\n  const [recoveryEmail, setRecoveryEmail] = useState('');\n\n  // Credenziali per i diversi tipi di login\n  const [adminCredentials, setAdminCredentials] = useState({\n    username: '',\n    password: ''\n  });\n  const [userCredentials, setUserCredentials] = useState({\n    username: '',\n    password: ''\n  });\n  const [cantiereCredentials, setCantiereCredentials] = useState({\n    codice_univoco: '',\n    password: ''\n  });\n  const {\n    login\n  } = useAuth();\n  const navigate = useNavigate();\n\n  // Gestione dell'input per il login amministratore\n  const handleAdminInputChange = e => {\n    const {\n      name,\n      value\n    } = e.target;\n    setAdminCredentials({\n      ...adminCredentials,\n      [name]: value\n    });\n  };\n\n  // Gestione dell'input per il login utente standard\n  const handleUserInputChange = e => {\n    const {\n      name,\n      value\n    } = e.target;\n    setUserCredentials({\n      ...userCredentials,\n      [name]: value\n    });\n  };\n\n  // Gestione dell'input per il login cantiere\n  const handleCantiereInputChange = e => {\n    const {\n      name,\n      value\n    } = e.target;\n    setCantiereCredentials({\n      ...cantiereCredentials,\n      [name]: value\n    });\n  };\n\n  // Gestione dell'input per il recupero password\n  const handleRecoveryEmailChange = e => {\n    setRecoveryEmail(e.target.value);\n  };\n\n  // Gestione del login amministratore\n  const handleAdminLogin = async e => {\n    e.preventDefault();\n    setLoading(true);\n    setError('');\n    console.log('Tentativo di login amministratore con:', adminCredentials);\n    try {\n      // Verifica che i campi non siano vuoti\n      if (!adminCredentials.username || !adminCredentials.password) {\n        console.log('Campi vuoti nel form di login admin');\n        setError('Username e password non possono essere vuoti');\n        setLoading(false);\n        return;\n      }\n      console.log('Chiamata alla funzione login per admin...');\n      try {\n        const userData = await login(adminCredentials, 'standard');\n        console.log('Login admin completato, dati utente:', userData);\n\n        // Verifica che l'utente sia un amministratore\n        if (userData.role !== 'owner') {\n          console.log('Utente non ha ruolo owner:', userData.role);\n          setError('Non hai i permessi di amministratore');\n          setLoading(false);\n          return;\n        }\n\n        // Reindirizza alla dashboard di amministrazione\n        console.log('Reindirizzamento a /dashboard/admin');\n        // Utilizza navigate invece di window.location per evitare refresh completo\n        navigate('/dashboard/admin');\n      } catch (loginError) {\n        console.error('Errore specifico durante login admin:', loginError);\n        setError(loginError.detail || 'Errore durante il login. Verifica le credenziali.');\n        setLoading(false);\n      }\n    } catch (err) {\n      console.error('Errore generale durante login admin:', err);\n      setError(err.detail || 'Errore imprevisto durante il login. Riprova più tardi.');\n      setLoading(false);\n    }\n    // Non mettiamo il finally qui perché il redirect interromperà l'esecuzione\n  };\n\n  // Gestione del login utente standard\n  const handleUserLogin = async e => {\n    e.preventDefault();\n    setLoading(true);\n    setError('');\n    console.log('Tentativo di login utente standard con:', userCredentials);\n    try {\n      // Verifica che i campi non siano vuoti\n      if (!userCredentials.username || !userCredentials.password) {\n        console.log('Campi vuoti nel form di login utente standard');\n        setError('Username e password non possono essere vuoti');\n        setLoading(false);\n        return;\n      }\n      console.log('Chiamata alla funzione login per utente standard...');\n      const userData = await login(userCredentials, 'standard');\n      console.log('Login utente standard completato, dati utente:', userData);\n\n      // Verifica che l'utente sia un utente standard\n      if (userData.role !== 'user') {\n        console.log('Utente non ha ruolo user:', userData.role);\n        setError('Non hai i permessi di utente standard');\n        setLoading(false);\n        return;\n      }\n\n      // Reindirizza alla dashboard utente\n      console.log('Reindirizzamento a /dashboard/cantieri');\n      // Utilizza navigate invece di window.location per evitare refresh completo\n      navigate('/dashboard/cantieri');\n    } catch (err) {\n      console.error('Errore durante login utente standard:', err);\n      setError(err.detail || 'Errore durante il login. Verifica le credenziali.');\n      setLoading(false);\n    }\n    // Non mettiamo il finally qui perché il redirect interromperà l'esecuzione\n  };\n\n  // Gestione del login cantiere\n  const handleCantiereLogin = async e => {\n    e.preventDefault();\n    setLoading(true);\n    setError('');\n    console.log('Tentativo di login cantiere con:', cantiereCredentials);\n    try {\n      // Verifica che i campi non siano vuoti\n      if (!cantiereCredentials.codice_univoco || !cantiereCredentials.password) {\n        console.log('Campi vuoti nel form di login cantiere');\n        setError('Codice univoco e password non possono essere vuoti');\n        setLoading(false);\n        return;\n      }\n      console.log('Chiamata alla funzione login per cantiere...');\n      const userData = await login(cantiereCredentials, 'cantiere');\n      console.log('Login cantiere completato, dati utente:', userData);\n\n      // Reindirizza alla dashboard cavi\n      console.log('Reindirizzamento a /dashboard/cavi');\n      // Utilizza navigate invece di window.location per evitare refresh completo\n      navigate('/dashboard/cavi');\n    } catch (err) {\n      console.error('Errore durante login cantiere:', err);\n      setError(err.detail || 'Errore durante il login. Verifica le credenziali.');\n      setLoading(false);\n    }\n    // Non mettiamo il finally qui perché il redirect interromperà l'esecuzione\n  };\n\n  // Gestione del recupero password\n  const handlePasswordRecovery = () => {\n    // Qui implementeremo la logica per il recupero della password\n    alert('Funzionalità di recupero password non ancora implementata');\n    setShowPasswordRecoveryDialog(false);\n  };\n\n  // Torna al menu principale\n  const handleBackToMainMenu = () => {\n    setLoginType(null);\n    setError('');\n  };\n\n  // Renderizza il form di login appropriato in base al tipo selezionato\n  const renderLoginForm = () => {\n    switch (loginType) {\n      case 'admin':\n        return /*#__PURE__*/_jsxDEV(Box, {\n          component: \"form\",\n          onSubmit: handleAdminLogin,\n          noValidate: true,\n          children: [/*#__PURE__*/_jsxDEV(Typography, {\n            variant: \"h5\",\n            gutterBottom: true,\n            children: \"Login Amministratore\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 225,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(TextField, {\n            margin: \"normal\",\n            required: true,\n            fullWidth: true,\n            id: \"admin-username\",\n            label: \"Username\",\n            name: \"username\",\n            autoComplete: \"username\",\n            autoFocus: true,\n            value: adminCredentials.username,\n            onChange: handleAdminInputChange\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 228,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(TextField, {\n            margin: \"normal\",\n            required: true,\n            fullWidth: true,\n            name: \"password\",\n            label: \"Password\",\n            type: \"password\",\n            id: \"admin-password\",\n            autoComplete: \"current-password\",\n            value: adminCredentials.password,\n            onChange: handleAdminInputChange\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 240,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Button, {\n            type: \"submit\",\n            fullWidth: true,\n            variant: \"contained\",\n            sx: {\n              mt: 3,\n              mb: 2\n            },\n            disabled: loading,\n            children: loading ? /*#__PURE__*/_jsxDEV(CircularProgress, {\n              size: 24\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 259,\n              columnNumber: 26\n            }, this) : 'Accedi'\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 252,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Button, {\n            fullWidth: true,\n            variant: \"outlined\",\n            onClick: handleBackToMainMenu,\n            sx: {\n              mb: 2\n            },\n            children: \"Torna al Menu Principale\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 261,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 224,\n          columnNumber: 11\n        }, this);\n      case 'user':\n        return /*#__PURE__*/_jsxDEV(Box, {\n          component: \"form\",\n          onSubmit: handleUserLogin,\n          noValidate: true,\n          children: [/*#__PURE__*/_jsxDEV(Typography, {\n            variant: \"h5\",\n            gutterBottom: true,\n            children: \"Login Utente Standard\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 274,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(TextField, {\n            margin: \"normal\",\n            required: true,\n            fullWidth: true,\n            id: \"user-username\",\n            label: \"Username\",\n            name: \"username\",\n            autoComplete: \"username\",\n            autoFocus: true,\n            value: userCredentials.username,\n            onChange: handleUserInputChange\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 277,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(TextField, {\n            margin: \"normal\",\n            required: true,\n            fullWidth: true,\n            name: \"password\",\n            label: \"Password\",\n            type: \"password\",\n            id: \"user-password\",\n            autoComplete: \"current-password\",\n            value: userCredentials.password,\n            onChange: handleUserInputChange\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 289,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Button, {\n            type: \"submit\",\n            fullWidth: true,\n            variant: \"contained\",\n            sx: {\n              mt: 3,\n              mb: 2\n            },\n            disabled: loading,\n            children: loading ? /*#__PURE__*/_jsxDEV(CircularProgress, {\n              size: 24\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 308,\n              columnNumber: 26\n            }, this) : 'Accedi'\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 301,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Button, {\n            fullWidth: true,\n            variant: \"outlined\",\n            onClick: handleBackToMainMenu,\n            sx: {\n              mb: 2\n            },\n            children: \"Torna al Menu Principale\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 310,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 273,\n          columnNumber: 11\n        }, this);\n      case 'cantiere':\n        return /*#__PURE__*/_jsxDEV(Box, {\n          component: \"form\",\n          onSubmit: handleCantiereLogin,\n          noValidate: true,\n          children: [/*#__PURE__*/_jsxDEV(Typography, {\n            variant: \"h5\",\n            gutterBottom: true,\n            children: \"Login Utente Cantiere\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 323,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(TextField, {\n            margin: \"normal\",\n            required: true,\n            fullWidth: true,\n            id: \"codice_univoco\",\n            label: \"Codice Univoco del Cantiere\",\n            name: \"codice_univoco\",\n            autoComplete: \"off\",\n            autoFocus: true,\n            value: cantiereCredentials.codice_univoco,\n            onChange: handleCantiereInputChange\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 326,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(TextField, {\n            margin: \"normal\",\n            required: true,\n            fullWidth: true,\n            name: \"password\",\n            label: \"Password\",\n            type: \"password\",\n            id: \"cantiere-password\",\n            autoComplete: \"current-password\",\n            value: cantiereCredentials.password,\n            onChange: handleCantiereInputChange\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 338,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Button, {\n            type: \"submit\",\n            fullWidth: true,\n            variant: \"contained\",\n            sx: {\n              mt: 3,\n              mb: 2\n            },\n            disabled: loading,\n            children: loading ? /*#__PURE__*/_jsxDEV(CircularProgress, {\n              size: 24\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 357,\n              columnNumber: 26\n            }, this) : 'Accedi'\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 350,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Button, {\n            fullWidth: true,\n            variant: \"outlined\",\n            onClick: handleBackToMainMenu,\n            sx: {\n              mb: 2\n            },\n            children: \"Torna al Menu Principale\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 359,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 322,\n          columnNumber: 11\n        }, this);\n      default:\n        return null;\n    }\n  };\n\n  // Renderizza il menu principale di login\n  const renderMainMenu = () => {\n    return /*#__PURE__*/_jsxDEV(Grid, {\n      container: true,\n      spacing: 3,\n      children: [/*#__PURE__*/_jsxDEV(Grid, {\n        item: true,\n        xs: 12,\n        sm: 6,\n        md: 6,\n        children: /*#__PURE__*/_jsxDEV(Card, {\n          children: [/*#__PURE__*/_jsxDEV(CardContent, {\n            sx: {\n              textAlign: 'center'\n            },\n            children: [/*#__PURE__*/_jsxDEV(AdminIcon, {\n              sx: {\n                fontSize: 60,\n                color: 'primary.main',\n                mb: 2\n              }\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 381,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Typography, {\n              variant: \"h5\",\n              component: \"div\",\n              gutterBottom: true,\n              children: \"Login Amministratore\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 382,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Typography, {\n              variant: \"body2\",\n              color: \"text.secondary\",\n              children: \"Accedi come amministratore per gestire utenti e sistema\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 385,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 380,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(CardActions, {\n            children: /*#__PURE__*/_jsxDEV(Button, {\n              fullWidth: true,\n              variant: \"contained\",\n              onClick: () => setLoginType('admin'),\n              children: \"Accedi\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 390,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 389,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 379,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 378,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Grid, {\n        item: true,\n        xs: 12,\n        sm: 6,\n        md: 6,\n        children: /*#__PURE__*/_jsxDEV(Card, {\n          children: [/*#__PURE__*/_jsxDEV(CardContent, {\n            sx: {\n              textAlign: 'center'\n            },\n            children: [/*#__PURE__*/_jsxDEV(UserIcon, {\n              sx: {\n                fontSize: 60,\n                color: 'secondary.main',\n                mb: 2\n              }\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 403,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Typography, {\n              variant: \"h5\",\n              component: \"div\",\n              gutterBottom: true,\n              children: \"Login Utente Standard\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 404,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Typography, {\n              variant: \"body2\",\n              color: \"text.secondary\",\n              children: \"Accedi come utente standard per gestire i tuoi cantieri\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 407,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 402,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(CardActions, {\n            children: /*#__PURE__*/_jsxDEV(Button, {\n              fullWidth: true,\n              variant: \"contained\",\n              color: \"secondary\",\n              onClick: () => setLoginType('user'),\n              children: \"Accedi\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 412,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 411,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 401,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 400,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Grid, {\n        item: true,\n        xs: 12,\n        sm: 6,\n        md: 6,\n        children: /*#__PURE__*/_jsxDEV(Card, {\n          children: [/*#__PURE__*/_jsxDEV(CardContent, {\n            sx: {\n              textAlign: 'center'\n            },\n            children: [/*#__PURE__*/_jsxDEV(ConstructionIcon, {\n              sx: {\n                fontSize: 60,\n                color: 'success.main',\n                mb: 2\n              }\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 426,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Typography, {\n              variant: \"h5\",\n              component: \"div\",\n              gutterBottom: true,\n              children: \"Login Utente Cantiere\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 427,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Typography, {\n              variant: \"body2\",\n              color: \"text.secondary\",\n              children: \"Accedi direttamente a un cantiere specifico\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 430,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 425,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(CardActions, {\n            children: /*#__PURE__*/_jsxDEV(Button, {\n              fullWidth: true,\n              variant: \"contained\",\n              color: \"success\",\n              onClick: () => setLoginType('cantiere'),\n              children: \"Accedi\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 435,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 434,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 424,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 423,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Grid, {\n        item: true,\n        xs: 12,\n        sm: 6,\n        md: 6,\n        children: /*#__PURE__*/_jsxDEV(Card, {\n          children: [/*#__PURE__*/_jsxDEV(CardContent, {\n            sx: {\n              textAlign: 'center'\n            },\n            children: [/*#__PURE__*/_jsxDEV(PasswordIcon, {\n              sx: {\n                fontSize: 60,\n                color: 'warning.main',\n                mb: 2\n              }\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 449,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Typography, {\n              variant: \"h5\",\n              component: \"div\",\n              gutterBottom: true,\n              children: \"Recupero Password Admin\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 450,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Typography, {\n              variant: \"body2\",\n              color: \"text.secondary\",\n              children: \"Recupera la password dell'amministratore\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 453,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 448,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(CardActions, {\n            children: /*#__PURE__*/_jsxDEV(Button, {\n              fullWidth: true,\n              variant: \"contained\",\n              color: \"warning\",\n              onClick: () => setShowPasswordRecoveryDialog(true),\n              children: \"Recupera\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 458,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 457,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 447,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 446,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 377,\n      columnNumber: 7\n    }, this);\n  };\n  return /*#__PURE__*/_jsxDEV(Container, {\n    component: \"main\",\n    maxWidth: \"md\",\n    children: [/*#__PURE__*/_jsxDEV(Box, {\n      sx: {\n        marginTop: 8,\n        display: 'flex',\n        flexDirection: 'column',\n        alignItems: 'center'\n      },\n      children: [/*#__PURE__*/_jsxDEV(Typography, {\n        component: \"h1\",\n        variant: \"h4\",\n        sx: {\n          mb: 4\n        },\n        children: \"Sistema di Gestione Cantieri\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 483,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Paper, {\n        elevation: 3,\n        sx: {\n          width: '100%',\n          p: 3\n        },\n        children: [/*#__PURE__*/_jsxDEV(Typography, {\n          variant: \"h5\",\n          gutterBottom: true,\n          sx: {\n            textAlign: 'center',\n            mb: 3\n          },\n          children: loginType ? 'Login' : 'Seleziona Tipo di Accesso'\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 488,\n          columnNumber: 11\n        }, this), error && /*#__PURE__*/_jsxDEV(Alert, {\n          severity: \"error\",\n          sx: {\n            mb: 3\n          },\n          children: error\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 493,\n          columnNumber: 13\n        }, this), loginType ? renderLoginForm() : renderMainMenu()]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 487,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 475,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Dialog, {\n      open: showPasswordRecoveryDialog,\n      onClose: () => setShowPasswordRecoveryDialog(false),\n      children: [/*#__PURE__*/_jsxDEV(DialogTitle, {\n        children: \"Recupero Password Amministratore\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 504,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(DialogContent, {\n        children: [/*#__PURE__*/_jsxDEV(DialogContentText, {\n          children: \"Inserisci l'indirizzo email associato all'account amministratore per ricevere le istruzioni per il recupero della password.\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 506,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(TextField, {\n          autoFocus: true,\n          margin: \"dense\",\n          id: \"email\",\n          label: \"Indirizzo Email\",\n          type: \"email\",\n          fullWidth: true,\n          variant: \"outlined\",\n          value: recoveryEmail,\n          onChange: handleRecoveryEmailChange\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 509,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 505,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(DialogActions, {\n        children: [/*#__PURE__*/_jsxDEV(Button, {\n          onClick: () => setShowPasswordRecoveryDialog(false),\n          children: \"Annulla\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 522,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Button, {\n          onClick: handlePasswordRecovery,\n          variant: \"contained\",\n          children: \"Invia\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 523,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 521,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 503,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 474,\n    columnNumber: 5\n  }, this);\n};\n_s(LoginPageNew, \"FN0PsZOhsEyM6WODOirrdcQ3vPc=\", false, function () {\n  return [useAuth, useNavigate];\n});\n_c = LoginPageNew;\nexport default LoginPageNew;\nvar _c;\n$RefreshReg$(_c, \"LoginPageNew\");", "map": {"version": 3, "names": ["React", "useState", "useNavigate", "Container", "Box", "Typography", "TextField", "<PERSON><PERSON>", "Paper", "Card", "<PERSON><PERSON><PERSON><PERSON>", "CardActions", "Grid", "<PERSON><PERSON>", "CircularProgress", "Dialog", "DialogTitle", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "DialogContentText", "DialogActions", "AdminPanelSettings", "AdminIcon", "Person", "UserIcon", "Construction", "ConstructionIcon", "Password", "PasswordIcon", "useAuth", "jsxDEV", "_jsxDEV", "LoginPageNew", "_s", "loginType", "setLoginType", "loading", "setLoading", "error", "setError", "showPasswordRecoveryDialog", "setShowPasswordRecoveryDialog", "recoveryEmail", "setRecoveryEmail", "adminCredentials", "setAdminCredentials", "username", "password", "userCredentials", "setUserCredentials", "cantiereCredentials", "setCantiereCredentials", "codice_univoco", "login", "navigate", "handleAdminInputChange", "e", "name", "value", "target", "handleUserInputChange", "handleCantiereInputChange", "handleRecoveryEmailChange", "handleAdminLogin", "preventDefault", "console", "log", "userData", "role", "loginError", "detail", "err", "handleUserLogin", "handleCantiereLogin", "handlePasswordRecovery", "alert", "handleBackToMainMenu", "renderLoginForm", "component", "onSubmit", "noValidate", "children", "variant", "gutterBottom", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "margin", "required", "fullWidth", "id", "label", "autoComplete", "autoFocus", "onChange", "type", "sx", "mt", "mb", "disabled", "size", "onClick", "renderMainMenu", "container", "spacing", "item", "xs", "sm", "md", "textAlign", "fontSize", "color", "max<PERSON><PERSON><PERSON>", "marginTop", "display", "flexDirection", "alignItems", "elevation", "width", "p", "severity", "open", "onClose", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/PycharmProjects/CMS/webapp/frontend/src/pages/LoginPageNew.js"], "sourcesContent": ["import React, { useState } from 'react';\nimport { useNavigate } from 'react-router-dom';\nimport {\n  Container,\n  Box,\n  Typography,\n  TextField,\n  Button,\n  Paper,\n  Card,\n  CardContent,\n  CardActions,\n  Grid,\n  Alert,\n  CircularProgress,\n  Dialog,\n  DialogTitle,\n  DialogContent,\n  DialogContentText,\n  DialogActions\n} from '@mui/material';\nimport {\n  AdminPanelSettings as AdminIcon,\n  Person as UserIcon,\n  Construction as ConstructionIcon,\n  Password as PasswordIcon\n} from '@mui/icons-material';\nimport { useAuth } from '../context/AuthContext';\n\nconst LoginPageNew = () => {\n  const [loginType, setLoginType] = useState(null);\n  const [loading, setLoading] = useState(false);\n  const [error, setError] = useState('');\n  const [showPasswordRecoveryDialog, setShowPasswordRecoveryDialog] = useState(false);\n  const [recoveryEmail, setRecoveryEmail] = useState('');\n\n  // Credenziali per i diversi tipi di login\n  const [adminCredentials, setAdminCredentials] = useState({\n    username: '',\n    password: ''\n  });\n\n  const [userCredentials, setUserCredentials] = useState({\n    username: '',\n    password: ''\n  });\n\n  const [cantiereCredentials, setCantiereCredentials] = useState({\n    codice_univoco: '',\n    password: ''\n  });\n\n  const { login } = useAuth();\n  const navigate = useNavigate();\n\n  // Gestione dell'input per il login amministratore\n  const handleAdminInputChange = (e) => {\n    const { name, value } = e.target;\n    setAdminCredentials({\n      ...adminCredentials,\n      [name]: value\n    });\n  };\n\n  // Gestione dell'input per il login utente standard\n  const handleUserInputChange = (e) => {\n    const { name, value } = e.target;\n    setUserCredentials({\n      ...userCredentials,\n      [name]: value\n    });\n  };\n\n  // Gestione dell'input per il login cantiere\n  const handleCantiereInputChange = (e) => {\n    const { name, value } = e.target;\n    setCantiereCredentials({\n      ...cantiereCredentials,\n      [name]: value\n    });\n  };\n\n  // Gestione dell'input per il recupero password\n  const handleRecoveryEmailChange = (e) => {\n    setRecoveryEmail(e.target.value);\n  };\n\n  // Gestione del login amministratore\n  const handleAdminLogin = async (e) => {\n    e.preventDefault();\n    setLoading(true);\n    setError('');\n    console.log('Tentativo di login amministratore con:', adminCredentials);\n\n    try {\n      // Verifica che i campi non siano vuoti\n      if (!adminCredentials.username || !adminCredentials.password) {\n        console.log('Campi vuoti nel form di login admin');\n        setError('Username e password non possono essere vuoti');\n        setLoading(false);\n        return;\n      }\n\n      console.log('Chiamata alla funzione login per admin...');\n      try {\n        const userData = await login(adminCredentials, 'standard');\n        console.log('Login admin completato, dati utente:', userData);\n\n        // Verifica che l'utente sia un amministratore\n        if (userData.role !== 'owner') {\n          console.log('Utente non ha ruolo owner:', userData.role);\n          setError('Non hai i permessi di amministratore');\n          setLoading(false);\n          return;\n        }\n\n        // Reindirizza alla dashboard di amministrazione\n        console.log('Reindirizzamento a /dashboard/admin');\n        // Utilizza navigate invece di window.location per evitare refresh completo\n        navigate('/dashboard/admin');\n      } catch (loginError) {\n        console.error('Errore specifico durante login admin:', loginError);\n        setError(loginError.detail || 'Errore durante il login. Verifica le credenziali.');\n        setLoading(false);\n      }\n    } catch (err) {\n      console.error('Errore generale durante login admin:', err);\n      setError(err.detail || 'Errore imprevisto durante il login. Riprova più tardi.');\n      setLoading(false);\n    }\n    // Non mettiamo il finally qui perché il redirect interromperà l'esecuzione\n  };\n\n  // Gestione del login utente standard\n  const handleUserLogin = async (e) => {\n    e.preventDefault();\n    setLoading(true);\n    setError('');\n    console.log('Tentativo di login utente standard con:', userCredentials);\n\n    try {\n      // Verifica che i campi non siano vuoti\n      if (!userCredentials.username || !userCredentials.password) {\n        console.log('Campi vuoti nel form di login utente standard');\n        setError('Username e password non possono essere vuoti');\n        setLoading(false);\n        return;\n      }\n\n      console.log('Chiamata alla funzione login per utente standard...');\n      const userData = await login(userCredentials, 'standard');\n      console.log('Login utente standard completato, dati utente:', userData);\n\n      // Verifica che l'utente sia un utente standard\n      if (userData.role !== 'user') {\n        console.log('Utente non ha ruolo user:', userData.role);\n        setError('Non hai i permessi di utente standard');\n        setLoading(false);\n        return;\n      }\n\n      // Reindirizza alla dashboard utente\n      console.log('Reindirizzamento a /dashboard/cantieri');\n      // Utilizza navigate invece di window.location per evitare refresh completo\n      navigate('/dashboard/cantieri');\n    } catch (err) {\n      console.error('Errore durante login utente standard:', err);\n      setError(err.detail || 'Errore durante il login. Verifica le credenziali.');\n      setLoading(false);\n    }\n    // Non mettiamo il finally qui perché il redirect interromperà l'esecuzione\n  };\n\n  // Gestione del login cantiere\n  const handleCantiereLogin = async (e) => {\n    e.preventDefault();\n    setLoading(true);\n    setError('');\n    console.log('Tentativo di login cantiere con:', cantiereCredentials);\n\n    try {\n      // Verifica che i campi non siano vuoti\n      if (!cantiereCredentials.codice_univoco || !cantiereCredentials.password) {\n        console.log('Campi vuoti nel form di login cantiere');\n        setError('Codice univoco e password non possono essere vuoti');\n        setLoading(false);\n        return;\n      }\n\n      console.log('Chiamata alla funzione login per cantiere...');\n      const userData = await login(cantiereCredentials, 'cantiere');\n      console.log('Login cantiere completato, dati utente:', userData);\n\n      // Reindirizza alla dashboard cavi\n      console.log('Reindirizzamento a /dashboard/cavi');\n      // Utilizza navigate invece di window.location per evitare refresh completo\n      navigate('/dashboard/cavi');\n    } catch (err) {\n      console.error('Errore durante login cantiere:', err);\n      setError(err.detail || 'Errore durante il login. Verifica le credenziali.');\n      setLoading(false);\n    }\n    // Non mettiamo il finally qui perché il redirect interromperà l'esecuzione\n  };\n\n  // Gestione del recupero password\n  const handlePasswordRecovery = () => {\n    // Qui implementeremo la logica per il recupero della password\n    alert('Funzionalità di recupero password non ancora implementata');\n    setShowPasswordRecoveryDialog(false);\n  };\n\n  // Torna al menu principale\n  const handleBackToMainMenu = () => {\n    setLoginType(null);\n    setError('');\n  };\n\n  // Renderizza il form di login appropriato in base al tipo selezionato\n  const renderLoginForm = () => {\n    switch (loginType) {\n      case 'admin':\n        return (\n          <Box component=\"form\" onSubmit={handleAdminLogin} noValidate>\n            <Typography variant=\"h5\" gutterBottom>\n              Login Amministratore\n            </Typography>\n            <TextField\n              margin=\"normal\"\n              required\n              fullWidth\n              id=\"admin-username\"\n              label=\"Username\"\n              name=\"username\"\n              autoComplete=\"username\"\n              autoFocus\n              value={adminCredentials.username}\n              onChange={handleAdminInputChange}\n            />\n            <TextField\n              margin=\"normal\"\n              required\n              fullWidth\n              name=\"password\"\n              label=\"Password\"\n              type=\"password\"\n              id=\"admin-password\"\n              autoComplete=\"current-password\"\n              value={adminCredentials.password}\n              onChange={handleAdminInputChange}\n            />\n            <Button\n              type=\"submit\"\n              fullWidth\n              variant=\"contained\"\n              sx={{ mt: 3, mb: 2 }}\n              disabled={loading}\n            >\n              {loading ? <CircularProgress size={24} /> : 'Accedi'}\n            </Button>\n            <Button\n              fullWidth\n              variant=\"outlined\"\n              onClick={handleBackToMainMenu}\n              sx={{ mb: 2 }}\n            >\n              Torna al Menu Principale\n            </Button>\n          </Box>\n        );\n      case 'user':\n        return (\n          <Box component=\"form\" onSubmit={handleUserLogin} noValidate>\n            <Typography variant=\"h5\" gutterBottom>\n              Login Utente Standard\n            </Typography>\n            <TextField\n              margin=\"normal\"\n              required\n              fullWidth\n              id=\"user-username\"\n              label=\"Username\"\n              name=\"username\"\n              autoComplete=\"username\"\n              autoFocus\n              value={userCredentials.username}\n              onChange={handleUserInputChange}\n            />\n            <TextField\n              margin=\"normal\"\n              required\n              fullWidth\n              name=\"password\"\n              label=\"Password\"\n              type=\"password\"\n              id=\"user-password\"\n              autoComplete=\"current-password\"\n              value={userCredentials.password}\n              onChange={handleUserInputChange}\n            />\n            <Button\n              type=\"submit\"\n              fullWidth\n              variant=\"contained\"\n              sx={{ mt: 3, mb: 2 }}\n              disabled={loading}\n            >\n              {loading ? <CircularProgress size={24} /> : 'Accedi'}\n            </Button>\n            <Button\n              fullWidth\n              variant=\"outlined\"\n              onClick={handleBackToMainMenu}\n              sx={{ mb: 2 }}\n            >\n              Torna al Menu Principale\n            </Button>\n          </Box>\n        );\n      case 'cantiere':\n        return (\n          <Box component=\"form\" onSubmit={handleCantiereLogin} noValidate>\n            <Typography variant=\"h5\" gutterBottom>\n              Login Utente Cantiere\n            </Typography>\n            <TextField\n              margin=\"normal\"\n              required\n              fullWidth\n              id=\"codice_univoco\"\n              label=\"Codice Univoco del Cantiere\"\n              name=\"codice_univoco\"\n              autoComplete=\"off\"\n              autoFocus\n              value={cantiereCredentials.codice_univoco}\n              onChange={handleCantiereInputChange}\n            />\n            <TextField\n              margin=\"normal\"\n              required\n              fullWidth\n              name=\"password\"\n              label=\"Password\"\n              type=\"password\"\n              id=\"cantiere-password\"\n              autoComplete=\"current-password\"\n              value={cantiereCredentials.password}\n              onChange={handleCantiereInputChange}\n            />\n            <Button\n              type=\"submit\"\n              fullWidth\n              variant=\"contained\"\n              sx={{ mt: 3, mb: 2 }}\n              disabled={loading}\n            >\n              {loading ? <CircularProgress size={24} /> : 'Accedi'}\n            </Button>\n            <Button\n              fullWidth\n              variant=\"outlined\"\n              onClick={handleBackToMainMenu}\n              sx={{ mb: 2 }}\n            >\n              Torna al Menu Principale\n            </Button>\n          </Box>\n        );\n      default:\n        return null;\n    }\n  };\n\n  // Renderizza il menu principale di login\n  const renderMainMenu = () => {\n    return (\n      <Grid container spacing={3}>\n        <Grid item xs={12} sm={6} md={6}>\n          <Card>\n            <CardContent sx={{ textAlign: 'center' }}>\n              <AdminIcon sx={{ fontSize: 60, color: 'primary.main', mb: 2 }} />\n              <Typography variant=\"h5\" component=\"div\" gutterBottom>\n                Login Amministratore\n              </Typography>\n              <Typography variant=\"body2\" color=\"text.secondary\">\n                Accedi come amministratore per gestire utenti e sistema\n              </Typography>\n            </CardContent>\n            <CardActions>\n              <Button\n                fullWidth\n                variant=\"contained\"\n                onClick={() => setLoginType('admin')}\n              >\n                Accedi\n              </Button>\n            </CardActions>\n          </Card>\n        </Grid>\n        <Grid item xs={12} sm={6} md={6}>\n          <Card>\n            <CardContent sx={{ textAlign: 'center' }}>\n              <UserIcon sx={{ fontSize: 60, color: 'secondary.main', mb: 2 }} />\n              <Typography variant=\"h5\" component=\"div\" gutterBottom>\n                Login Utente Standard\n              </Typography>\n              <Typography variant=\"body2\" color=\"text.secondary\">\n                Accedi come utente standard per gestire i tuoi cantieri\n              </Typography>\n            </CardContent>\n            <CardActions>\n              <Button\n                fullWidth\n                variant=\"contained\"\n                color=\"secondary\"\n                onClick={() => setLoginType('user')}\n              >\n                Accedi\n              </Button>\n            </CardActions>\n          </Card>\n        </Grid>\n        <Grid item xs={12} sm={6} md={6}>\n          <Card>\n            <CardContent sx={{ textAlign: 'center' }}>\n              <ConstructionIcon sx={{ fontSize: 60, color: 'success.main', mb: 2 }} />\n              <Typography variant=\"h5\" component=\"div\" gutterBottom>\n                Login Utente Cantiere\n              </Typography>\n              <Typography variant=\"body2\" color=\"text.secondary\">\n                Accedi direttamente a un cantiere specifico\n              </Typography>\n            </CardContent>\n            <CardActions>\n              <Button\n                fullWidth\n                variant=\"contained\"\n                color=\"success\"\n                onClick={() => setLoginType('cantiere')}\n              >\n                Accedi\n              </Button>\n            </CardActions>\n          </Card>\n        </Grid>\n        <Grid item xs={12} sm={6} md={6}>\n          <Card>\n            <CardContent sx={{ textAlign: 'center' }}>\n              <PasswordIcon sx={{ fontSize: 60, color: 'warning.main', mb: 2 }} />\n              <Typography variant=\"h5\" component=\"div\" gutterBottom>\n                Recupero Password Admin\n              </Typography>\n              <Typography variant=\"body2\" color=\"text.secondary\">\n                Recupera la password dell'amministratore\n              </Typography>\n            </CardContent>\n            <CardActions>\n              <Button\n                fullWidth\n                variant=\"contained\"\n                color=\"warning\"\n                onClick={() => setShowPasswordRecoveryDialog(true)}\n              >\n                Recupera\n              </Button>\n            </CardActions>\n          </Card>\n        </Grid>\n      </Grid>\n    );\n  };\n\n  return (\n    <Container component=\"main\" maxWidth=\"md\">\n      <Box\n        sx={{\n          marginTop: 8,\n          display: 'flex',\n          flexDirection: 'column',\n          alignItems: 'center',\n        }}\n      >\n        <Typography component=\"h1\" variant=\"h4\" sx={{ mb: 4 }}>\n          Sistema di Gestione Cantieri\n        </Typography>\n\n        <Paper elevation={3} sx={{ width: '100%', p: 3 }}>\n          <Typography variant=\"h5\" gutterBottom sx={{ textAlign: 'center', mb: 3 }}>\n            {loginType ? 'Login' : 'Seleziona Tipo di Accesso'}\n          </Typography>\n\n          {error && (\n            <Alert severity=\"error\" sx={{ mb: 3 }}>\n              {error}\n            </Alert>\n          )}\n\n          {loginType ? renderLoginForm() : renderMainMenu()}\n        </Paper>\n      </Box>\n\n      {/* Dialog per il recupero password */}\n      <Dialog open={showPasswordRecoveryDialog} onClose={() => setShowPasswordRecoveryDialog(false)}>\n        <DialogTitle>Recupero Password Amministratore</DialogTitle>\n        <DialogContent>\n          <DialogContentText>\n            Inserisci l'indirizzo email associato all'account amministratore per ricevere le istruzioni per il recupero della password.\n          </DialogContentText>\n          <TextField\n            autoFocus\n            margin=\"dense\"\n            id=\"email\"\n            label=\"Indirizzo Email\"\n            type=\"email\"\n            fullWidth\n            variant=\"outlined\"\n            value={recoveryEmail}\n            onChange={handleRecoveryEmailChange}\n          />\n        </DialogContent>\n        <DialogActions>\n          <Button onClick={() => setShowPasswordRecoveryDialog(false)}>Annulla</Button>\n          <Button onClick={handlePasswordRecovery} variant=\"contained\">Invia</Button>\n        </DialogActions>\n      </Dialog>\n    </Container>\n  );\n};\n\nexport default LoginPageNew;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,QAAQ,OAAO;AACvC,SAASC,WAAW,QAAQ,kBAAkB;AAC9C,SACEC,SAAS,EACTC,GAAG,EACHC,UAAU,EACVC,SAAS,EACTC,MAAM,EACNC,KAAK,EACLC,IAAI,EACJC,WAAW,EACXC,WAAW,EACXC,IAAI,EACJC,KAAK,EACLC,gBAAgB,EAChBC,MAAM,EACNC,WAAW,EACXC,aAAa,EACbC,iBAAiB,EACjBC,aAAa,QACR,eAAe;AACtB,SACEC,kBAAkB,IAAIC,SAAS,EAC/BC,MAAM,IAAIC,QAAQ,EAClBC,YAAY,IAAIC,gBAAgB,EAChCC,QAAQ,IAAIC,YAAY,QACnB,qBAAqB;AAC5B,SAASC,OAAO,QAAQ,wBAAwB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAEjD,MAAMC,YAAY,GAAGA,CAAA,KAAM;EAAAC,EAAA;EACzB,MAAM,CAACC,SAAS,EAAEC,YAAY,CAAC,GAAGjC,QAAQ,CAAC,IAAI,CAAC;EAChD,MAAM,CAACkC,OAAO,EAAEC,UAAU,CAAC,GAAGnC,QAAQ,CAAC,KAAK,CAAC;EAC7C,MAAM,CAACoC,KAAK,EAAEC,QAAQ,CAAC,GAAGrC,QAAQ,CAAC,EAAE,CAAC;EACtC,MAAM,CAACsC,0BAA0B,EAAEC,6BAA6B,CAAC,GAAGvC,QAAQ,CAAC,KAAK,CAAC;EACnF,MAAM,CAACwC,aAAa,EAAEC,gBAAgB,CAAC,GAAGzC,QAAQ,CAAC,EAAE,CAAC;;EAEtD;EACA,MAAM,CAAC0C,gBAAgB,EAAEC,mBAAmB,CAAC,GAAG3C,QAAQ,CAAC;IACvD4C,QAAQ,EAAE,EAAE;IACZC,QAAQ,EAAE;EACZ,CAAC,CAAC;EAEF,MAAM,CAACC,eAAe,EAAEC,kBAAkB,CAAC,GAAG/C,QAAQ,CAAC;IACrD4C,QAAQ,EAAE,EAAE;IACZC,QAAQ,EAAE;EACZ,CAAC,CAAC;EAEF,MAAM,CAACG,mBAAmB,EAAEC,sBAAsB,CAAC,GAAGjD,QAAQ,CAAC;IAC7DkD,cAAc,EAAE,EAAE;IAClBL,QAAQ,EAAE;EACZ,CAAC,CAAC;EAEF,MAAM;IAAEM;EAAM,CAAC,GAAGxB,OAAO,CAAC,CAAC;EAC3B,MAAMyB,QAAQ,GAAGnD,WAAW,CAAC,CAAC;;EAE9B;EACA,MAAMoD,sBAAsB,GAAIC,CAAC,IAAK;IACpC,MAAM;MAAEC,IAAI;MAAEC;IAAM,CAAC,GAAGF,CAAC,CAACG,MAAM;IAChCd,mBAAmB,CAAC;MAClB,GAAGD,gBAAgB;MACnB,CAACa,IAAI,GAAGC;IACV,CAAC,CAAC;EACJ,CAAC;;EAED;EACA,MAAME,qBAAqB,GAAIJ,CAAC,IAAK;IACnC,MAAM;MAAEC,IAAI;MAAEC;IAAM,CAAC,GAAGF,CAAC,CAACG,MAAM;IAChCV,kBAAkB,CAAC;MACjB,GAAGD,eAAe;MAClB,CAACS,IAAI,GAAGC;IACV,CAAC,CAAC;EACJ,CAAC;;EAED;EACA,MAAMG,yBAAyB,GAAIL,CAAC,IAAK;IACvC,MAAM;MAAEC,IAAI;MAAEC;IAAM,CAAC,GAAGF,CAAC,CAACG,MAAM;IAChCR,sBAAsB,CAAC;MACrB,GAAGD,mBAAmB;MACtB,CAACO,IAAI,GAAGC;IACV,CAAC,CAAC;EACJ,CAAC;;EAED;EACA,MAAMI,yBAAyB,GAAIN,CAAC,IAAK;IACvCb,gBAAgB,CAACa,CAAC,CAACG,MAAM,CAACD,KAAK,CAAC;EAClC,CAAC;;EAED;EACA,MAAMK,gBAAgB,GAAG,MAAOP,CAAC,IAAK;IACpCA,CAAC,CAACQ,cAAc,CAAC,CAAC;IAClB3B,UAAU,CAAC,IAAI,CAAC;IAChBE,QAAQ,CAAC,EAAE,CAAC;IACZ0B,OAAO,CAACC,GAAG,CAAC,wCAAwC,EAAEtB,gBAAgB,CAAC;IAEvE,IAAI;MACF;MACA,IAAI,CAACA,gBAAgB,CAACE,QAAQ,IAAI,CAACF,gBAAgB,CAACG,QAAQ,EAAE;QAC5DkB,OAAO,CAACC,GAAG,CAAC,qCAAqC,CAAC;QAClD3B,QAAQ,CAAC,8CAA8C,CAAC;QACxDF,UAAU,CAAC,KAAK,CAAC;QACjB;MACF;MAEA4B,OAAO,CAACC,GAAG,CAAC,2CAA2C,CAAC;MACxD,IAAI;QACF,MAAMC,QAAQ,GAAG,MAAMd,KAAK,CAACT,gBAAgB,EAAE,UAAU,CAAC;QAC1DqB,OAAO,CAACC,GAAG,CAAC,sCAAsC,EAAEC,QAAQ,CAAC;;QAE7D;QACA,IAAIA,QAAQ,CAACC,IAAI,KAAK,OAAO,EAAE;UAC7BH,OAAO,CAACC,GAAG,CAAC,4BAA4B,EAAEC,QAAQ,CAACC,IAAI,CAAC;UACxD7B,QAAQ,CAAC,sCAAsC,CAAC;UAChDF,UAAU,CAAC,KAAK,CAAC;UACjB;QACF;;QAEA;QACA4B,OAAO,CAACC,GAAG,CAAC,qCAAqC,CAAC;QAClD;QACAZ,QAAQ,CAAC,kBAAkB,CAAC;MAC9B,CAAC,CAAC,OAAOe,UAAU,EAAE;QACnBJ,OAAO,CAAC3B,KAAK,CAAC,uCAAuC,EAAE+B,UAAU,CAAC;QAClE9B,QAAQ,CAAC8B,UAAU,CAACC,MAAM,IAAI,mDAAmD,CAAC;QAClFjC,UAAU,CAAC,KAAK,CAAC;MACnB;IACF,CAAC,CAAC,OAAOkC,GAAG,EAAE;MACZN,OAAO,CAAC3B,KAAK,CAAC,sCAAsC,EAAEiC,GAAG,CAAC;MAC1DhC,QAAQ,CAACgC,GAAG,CAACD,MAAM,IAAI,wDAAwD,CAAC;MAChFjC,UAAU,CAAC,KAAK,CAAC;IACnB;IACA;EACF,CAAC;;EAED;EACA,MAAMmC,eAAe,GAAG,MAAOhB,CAAC,IAAK;IACnCA,CAAC,CAACQ,cAAc,CAAC,CAAC;IAClB3B,UAAU,CAAC,IAAI,CAAC;IAChBE,QAAQ,CAAC,EAAE,CAAC;IACZ0B,OAAO,CAACC,GAAG,CAAC,yCAAyC,EAAElB,eAAe,CAAC;IAEvE,IAAI;MACF;MACA,IAAI,CAACA,eAAe,CAACF,QAAQ,IAAI,CAACE,eAAe,CAACD,QAAQ,EAAE;QAC1DkB,OAAO,CAACC,GAAG,CAAC,+CAA+C,CAAC;QAC5D3B,QAAQ,CAAC,8CAA8C,CAAC;QACxDF,UAAU,CAAC,KAAK,CAAC;QACjB;MACF;MAEA4B,OAAO,CAACC,GAAG,CAAC,qDAAqD,CAAC;MAClE,MAAMC,QAAQ,GAAG,MAAMd,KAAK,CAACL,eAAe,EAAE,UAAU,CAAC;MACzDiB,OAAO,CAACC,GAAG,CAAC,gDAAgD,EAAEC,QAAQ,CAAC;;MAEvE;MACA,IAAIA,QAAQ,CAACC,IAAI,KAAK,MAAM,EAAE;QAC5BH,OAAO,CAACC,GAAG,CAAC,2BAA2B,EAAEC,QAAQ,CAACC,IAAI,CAAC;QACvD7B,QAAQ,CAAC,uCAAuC,CAAC;QACjDF,UAAU,CAAC,KAAK,CAAC;QACjB;MACF;;MAEA;MACA4B,OAAO,CAACC,GAAG,CAAC,wCAAwC,CAAC;MACrD;MACAZ,QAAQ,CAAC,qBAAqB,CAAC;IACjC,CAAC,CAAC,OAAOiB,GAAG,EAAE;MACZN,OAAO,CAAC3B,KAAK,CAAC,uCAAuC,EAAEiC,GAAG,CAAC;MAC3DhC,QAAQ,CAACgC,GAAG,CAACD,MAAM,IAAI,mDAAmD,CAAC;MAC3EjC,UAAU,CAAC,KAAK,CAAC;IACnB;IACA;EACF,CAAC;;EAED;EACA,MAAMoC,mBAAmB,GAAG,MAAOjB,CAAC,IAAK;IACvCA,CAAC,CAACQ,cAAc,CAAC,CAAC;IAClB3B,UAAU,CAAC,IAAI,CAAC;IAChBE,QAAQ,CAAC,EAAE,CAAC;IACZ0B,OAAO,CAACC,GAAG,CAAC,kCAAkC,EAAEhB,mBAAmB,CAAC;IAEpE,IAAI;MACF;MACA,IAAI,CAACA,mBAAmB,CAACE,cAAc,IAAI,CAACF,mBAAmB,CAACH,QAAQ,EAAE;QACxEkB,OAAO,CAACC,GAAG,CAAC,wCAAwC,CAAC;QACrD3B,QAAQ,CAAC,oDAAoD,CAAC;QAC9DF,UAAU,CAAC,KAAK,CAAC;QACjB;MACF;MAEA4B,OAAO,CAACC,GAAG,CAAC,8CAA8C,CAAC;MAC3D,MAAMC,QAAQ,GAAG,MAAMd,KAAK,CAACH,mBAAmB,EAAE,UAAU,CAAC;MAC7De,OAAO,CAACC,GAAG,CAAC,yCAAyC,EAAEC,QAAQ,CAAC;;MAEhE;MACAF,OAAO,CAACC,GAAG,CAAC,oCAAoC,CAAC;MACjD;MACAZ,QAAQ,CAAC,iBAAiB,CAAC;IAC7B,CAAC,CAAC,OAAOiB,GAAG,EAAE;MACZN,OAAO,CAAC3B,KAAK,CAAC,gCAAgC,EAAEiC,GAAG,CAAC;MACpDhC,QAAQ,CAACgC,GAAG,CAACD,MAAM,IAAI,mDAAmD,CAAC;MAC3EjC,UAAU,CAAC,KAAK,CAAC;IACnB;IACA;EACF,CAAC;;EAED;EACA,MAAMqC,sBAAsB,GAAGA,CAAA,KAAM;IACnC;IACAC,KAAK,CAAC,2DAA2D,CAAC;IAClElC,6BAA6B,CAAC,KAAK,CAAC;EACtC,CAAC;;EAED;EACA,MAAMmC,oBAAoB,GAAGA,CAAA,KAAM;IACjCzC,YAAY,CAAC,IAAI,CAAC;IAClBI,QAAQ,CAAC,EAAE,CAAC;EACd,CAAC;;EAED;EACA,MAAMsC,eAAe,GAAGA,CAAA,KAAM;IAC5B,QAAQ3C,SAAS;MACf,KAAK,OAAO;QACV,oBACEH,OAAA,CAAC1B,GAAG;UAACyE,SAAS,EAAC,MAAM;UAACC,QAAQ,EAAEhB,gBAAiB;UAACiB,UAAU;UAAAC,QAAA,gBAC1DlD,OAAA,CAACzB,UAAU;YAAC4E,OAAO,EAAC,IAAI;YAACC,YAAY;YAAAF,QAAA,EAAC;UAEtC;YAAAG,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAY,CAAC,eACbxD,OAAA,CAACxB,SAAS;YACRiF,MAAM,EAAC,QAAQ;YACfC,QAAQ;YACRC,SAAS;YACTC,EAAE,EAAC,gBAAgB;YACnBC,KAAK,EAAC,UAAU;YAChBnC,IAAI,EAAC,UAAU;YACfoC,YAAY,EAAC,UAAU;YACvBC,SAAS;YACTpC,KAAK,EAAEd,gBAAgB,CAACE,QAAS;YACjCiD,QAAQ,EAAExC;UAAuB;YAAA6B,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAClC,CAAC,eACFxD,OAAA,CAACxB,SAAS;YACRiF,MAAM,EAAC,QAAQ;YACfC,QAAQ;YACRC,SAAS;YACTjC,IAAI,EAAC,UAAU;YACfmC,KAAK,EAAC,UAAU;YAChBI,IAAI,EAAC,UAAU;YACfL,EAAE,EAAC,gBAAgB;YACnBE,YAAY,EAAC,kBAAkB;YAC/BnC,KAAK,EAAEd,gBAAgB,CAACG,QAAS;YACjCgD,QAAQ,EAAExC;UAAuB;YAAA6B,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAClC,CAAC,eACFxD,OAAA,CAACvB,MAAM;YACLwF,IAAI,EAAC,QAAQ;YACbN,SAAS;YACTR,OAAO,EAAC,WAAW;YACnBe,EAAE,EAAE;cAAEC,EAAE,EAAE,CAAC;cAAEC,EAAE,EAAE;YAAE,CAAE;YACrBC,QAAQ,EAAEhE,OAAQ;YAAA6C,QAAA,EAEjB7C,OAAO,gBAAGL,OAAA,CAAChB,gBAAgB;cAACsF,IAAI,EAAE;YAAG;cAAAjB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,GAAG;UAAQ;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC9C,CAAC,eACTxD,OAAA,CAACvB,MAAM;YACLkF,SAAS;YACTR,OAAO,EAAC,UAAU;YAClBoB,OAAO,EAAE1B,oBAAqB;YAC9BqB,EAAE,EAAE;cAAEE,EAAE,EAAE;YAAE,CAAE;YAAAlB,QAAA,EACf;UAED;YAAAG,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACN,CAAC;MAEV,KAAK,MAAM;QACT,oBACExD,OAAA,CAAC1B,GAAG;UAACyE,SAAS,EAAC,MAAM;UAACC,QAAQ,EAAEP,eAAgB;UAACQ,UAAU;UAAAC,QAAA,gBACzDlD,OAAA,CAACzB,UAAU;YAAC4E,OAAO,EAAC,IAAI;YAACC,YAAY;YAAAF,QAAA,EAAC;UAEtC;YAAAG,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAY,CAAC,eACbxD,OAAA,CAACxB,SAAS;YACRiF,MAAM,EAAC,QAAQ;YACfC,QAAQ;YACRC,SAAS;YACTC,EAAE,EAAC,eAAe;YAClBC,KAAK,EAAC,UAAU;YAChBnC,IAAI,EAAC,UAAU;YACfoC,YAAY,EAAC,UAAU;YACvBC,SAAS;YACTpC,KAAK,EAAEV,eAAe,CAACF,QAAS;YAChCiD,QAAQ,EAAEnC;UAAsB;YAAAwB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACjC,CAAC,eACFxD,OAAA,CAACxB,SAAS;YACRiF,MAAM,EAAC,QAAQ;YACfC,QAAQ;YACRC,SAAS;YACTjC,IAAI,EAAC,UAAU;YACfmC,KAAK,EAAC,UAAU;YAChBI,IAAI,EAAC,UAAU;YACfL,EAAE,EAAC,eAAe;YAClBE,YAAY,EAAC,kBAAkB;YAC/BnC,KAAK,EAAEV,eAAe,CAACD,QAAS;YAChCgD,QAAQ,EAAEnC;UAAsB;YAAAwB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACjC,CAAC,eACFxD,OAAA,CAACvB,MAAM;YACLwF,IAAI,EAAC,QAAQ;YACbN,SAAS;YACTR,OAAO,EAAC,WAAW;YACnBe,EAAE,EAAE;cAAEC,EAAE,EAAE,CAAC;cAAEC,EAAE,EAAE;YAAE,CAAE;YACrBC,QAAQ,EAAEhE,OAAQ;YAAA6C,QAAA,EAEjB7C,OAAO,gBAAGL,OAAA,CAAChB,gBAAgB;cAACsF,IAAI,EAAE;YAAG;cAAAjB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,GAAG;UAAQ;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC9C,CAAC,eACTxD,OAAA,CAACvB,MAAM;YACLkF,SAAS;YACTR,OAAO,EAAC,UAAU;YAClBoB,OAAO,EAAE1B,oBAAqB;YAC9BqB,EAAE,EAAE;cAAEE,EAAE,EAAE;YAAE,CAAE;YAAAlB,QAAA,EACf;UAED;YAAAG,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACN,CAAC;MAEV,KAAK,UAAU;QACb,oBACExD,OAAA,CAAC1B,GAAG;UAACyE,SAAS,EAAC,MAAM;UAACC,QAAQ,EAAEN,mBAAoB;UAACO,UAAU;UAAAC,QAAA,gBAC7DlD,OAAA,CAACzB,UAAU;YAAC4E,OAAO,EAAC,IAAI;YAACC,YAAY;YAAAF,QAAA,EAAC;UAEtC;YAAAG,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAY,CAAC,eACbxD,OAAA,CAACxB,SAAS;YACRiF,MAAM,EAAC,QAAQ;YACfC,QAAQ;YACRC,SAAS;YACTC,EAAE,EAAC,gBAAgB;YACnBC,KAAK,EAAC,6BAA6B;YACnCnC,IAAI,EAAC,gBAAgB;YACrBoC,YAAY,EAAC,KAAK;YAClBC,SAAS;YACTpC,KAAK,EAAER,mBAAmB,CAACE,cAAe;YAC1C2C,QAAQ,EAAElC;UAA0B;YAAAuB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACrC,CAAC,eACFxD,OAAA,CAACxB,SAAS;YACRiF,MAAM,EAAC,QAAQ;YACfC,QAAQ;YACRC,SAAS;YACTjC,IAAI,EAAC,UAAU;YACfmC,KAAK,EAAC,UAAU;YAChBI,IAAI,EAAC,UAAU;YACfL,EAAE,EAAC,mBAAmB;YACtBE,YAAY,EAAC,kBAAkB;YAC/BnC,KAAK,EAAER,mBAAmB,CAACH,QAAS;YACpCgD,QAAQ,EAAElC;UAA0B;YAAAuB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACrC,CAAC,eACFxD,OAAA,CAACvB,MAAM;YACLwF,IAAI,EAAC,QAAQ;YACbN,SAAS;YACTR,OAAO,EAAC,WAAW;YACnBe,EAAE,EAAE;cAAEC,EAAE,EAAE,CAAC;cAAEC,EAAE,EAAE;YAAE,CAAE;YACrBC,QAAQ,EAAEhE,OAAQ;YAAA6C,QAAA,EAEjB7C,OAAO,gBAAGL,OAAA,CAAChB,gBAAgB;cAACsF,IAAI,EAAE;YAAG;cAAAjB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,GAAG;UAAQ;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC9C,CAAC,eACTxD,OAAA,CAACvB,MAAM;YACLkF,SAAS;YACTR,OAAO,EAAC,UAAU;YAClBoB,OAAO,EAAE1B,oBAAqB;YAC9BqB,EAAE,EAAE;cAAEE,EAAE,EAAE;YAAE,CAAE;YAAAlB,QAAA,EACf;UAED;YAAAG,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACN,CAAC;MAEV;QACE,OAAO,IAAI;IACf;EACF,CAAC;;EAED;EACA,MAAMgB,cAAc,GAAGA,CAAA,KAAM;IAC3B,oBACExE,OAAA,CAAClB,IAAI;MAAC2F,SAAS;MAACC,OAAO,EAAE,CAAE;MAAAxB,QAAA,gBACzBlD,OAAA,CAAClB,IAAI;QAAC6F,IAAI;QAACC,EAAE,EAAE,EAAG;QAACC,EAAE,EAAE,CAAE;QAACC,EAAE,EAAE,CAAE;QAAA5B,QAAA,eAC9BlD,OAAA,CAACrB,IAAI;UAAAuE,QAAA,gBACHlD,OAAA,CAACpB,WAAW;YAACsF,EAAE,EAAE;cAAEa,SAAS,EAAE;YAAS,CAAE;YAAA7B,QAAA,gBACvClD,OAAA,CAACT,SAAS;cAAC2E,EAAE,EAAE;gBAAEc,QAAQ,EAAE,EAAE;gBAAEC,KAAK,EAAE,cAAc;gBAAEb,EAAE,EAAE;cAAE;YAAE;cAAAf,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,eACjExD,OAAA,CAACzB,UAAU;cAAC4E,OAAO,EAAC,IAAI;cAACJ,SAAS,EAAC,KAAK;cAACK,YAAY;cAAAF,QAAA,EAAC;YAEtD;cAAAG,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAY,CAAC,eACbxD,OAAA,CAACzB,UAAU;cAAC4E,OAAO,EAAC,OAAO;cAAC8B,KAAK,EAAC,gBAAgB;cAAA/B,QAAA,EAAC;YAEnD;cAAAG,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAY,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACF,CAAC,eACdxD,OAAA,CAACnB,WAAW;YAAAqE,QAAA,eACVlD,OAAA,CAACvB,MAAM;cACLkF,SAAS;cACTR,OAAO,EAAC,WAAW;cACnBoB,OAAO,EAAEA,CAAA,KAAMnE,YAAY,CAAC,OAAO,CAAE;cAAA8C,QAAA,EACtC;YAED;cAAAG,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACE,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACV;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,eACPxD,OAAA,CAAClB,IAAI;QAAC6F,IAAI;QAACC,EAAE,EAAE,EAAG;QAACC,EAAE,EAAE,CAAE;QAACC,EAAE,EAAE,CAAE;QAAA5B,QAAA,eAC9BlD,OAAA,CAACrB,IAAI;UAAAuE,QAAA,gBACHlD,OAAA,CAACpB,WAAW;YAACsF,EAAE,EAAE;cAAEa,SAAS,EAAE;YAAS,CAAE;YAAA7B,QAAA,gBACvClD,OAAA,CAACP,QAAQ;cAACyE,EAAE,EAAE;gBAAEc,QAAQ,EAAE,EAAE;gBAAEC,KAAK,EAAE,gBAAgB;gBAAEb,EAAE,EAAE;cAAE;YAAE;cAAAf,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,eAClExD,OAAA,CAACzB,UAAU;cAAC4E,OAAO,EAAC,IAAI;cAACJ,SAAS,EAAC,KAAK;cAACK,YAAY;cAAAF,QAAA,EAAC;YAEtD;cAAAG,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAY,CAAC,eACbxD,OAAA,CAACzB,UAAU;cAAC4E,OAAO,EAAC,OAAO;cAAC8B,KAAK,EAAC,gBAAgB;cAAA/B,QAAA,EAAC;YAEnD;cAAAG,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAY,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACF,CAAC,eACdxD,OAAA,CAACnB,WAAW;YAAAqE,QAAA,eACVlD,OAAA,CAACvB,MAAM;cACLkF,SAAS;cACTR,OAAO,EAAC,WAAW;cACnB8B,KAAK,EAAC,WAAW;cACjBV,OAAO,EAAEA,CAAA,KAAMnE,YAAY,CAAC,MAAM,CAAE;cAAA8C,QAAA,EACrC;YAED;cAAAG,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACE,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACV;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,eACPxD,OAAA,CAAClB,IAAI;QAAC6F,IAAI;QAACC,EAAE,EAAE,EAAG;QAACC,EAAE,EAAE,CAAE;QAACC,EAAE,EAAE,CAAE;QAAA5B,QAAA,eAC9BlD,OAAA,CAACrB,IAAI;UAAAuE,QAAA,gBACHlD,OAAA,CAACpB,WAAW;YAACsF,EAAE,EAAE;cAAEa,SAAS,EAAE;YAAS,CAAE;YAAA7B,QAAA,gBACvClD,OAAA,CAACL,gBAAgB;cAACuE,EAAE,EAAE;gBAAEc,QAAQ,EAAE,EAAE;gBAAEC,KAAK,EAAE,cAAc;gBAAEb,EAAE,EAAE;cAAE;YAAE;cAAAf,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,eACxExD,OAAA,CAACzB,UAAU;cAAC4E,OAAO,EAAC,IAAI;cAACJ,SAAS,EAAC,KAAK;cAACK,YAAY;cAAAF,QAAA,EAAC;YAEtD;cAAAG,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAY,CAAC,eACbxD,OAAA,CAACzB,UAAU;cAAC4E,OAAO,EAAC,OAAO;cAAC8B,KAAK,EAAC,gBAAgB;cAAA/B,QAAA,EAAC;YAEnD;cAAAG,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAY,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACF,CAAC,eACdxD,OAAA,CAACnB,WAAW;YAAAqE,QAAA,eACVlD,OAAA,CAACvB,MAAM;cACLkF,SAAS;cACTR,OAAO,EAAC,WAAW;cACnB8B,KAAK,EAAC,SAAS;cACfV,OAAO,EAAEA,CAAA,KAAMnE,YAAY,CAAC,UAAU,CAAE;cAAA8C,QAAA,EACzC;YAED;cAAAG,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACE,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACV;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,eACPxD,OAAA,CAAClB,IAAI;QAAC6F,IAAI;QAACC,EAAE,EAAE,EAAG;QAACC,EAAE,EAAE,CAAE;QAACC,EAAE,EAAE,CAAE;QAAA5B,QAAA,eAC9BlD,OAAA,CAACrB,IAAI;UAAAuE,QAAA,gBACHlD,OAAA,CAACpB,WAAW;YAACsF,EAAE,EAAE;cAAEa,SAAS,EAAE;YAAS,CAAE;YAAA7B,QAAA,gBACvClD,OAAA,CAACH,YAAY;cAACqE,EAAE,EAAE;gBAAEc,QAAQ,EAAE,EAAE;gBAAEC,KAAK,EAAE,cAAc;gBAAEb,EAAE,EAAE;cAAE;YAAE;cAAAf,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,eACpExD,OAAA,CAACzB,UAAU;cAAC4E,OAAO,EAAC,IAAI;cAACJ,SAAS,EAAC,KAAK;cAACK,YAAY;cAAAF,QAAA,EAAC;YAEtD;cAAAG,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAY,CAAC,eACbxD,OAAA,CAACzB,UAAU;cAAC4E,OAAO,EAAC,OAAO;cAAC8B,KAAK,EAAC,gBAAgB;cAAA/B,QAAA,EAAC;YAEnD;cAAAG,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAY,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACF,CAAC,eACdxD,OAAA,CAACnB,WAAW;YAAAqE,QAAA,eACVlD,OAAA,CAACvB,MAAM;cACLkF,SAAS;cACTR,OAAO,EAAC,WAAW;cACnB8B,KAAK,EAAC,SAAS;cACfV,OAAO,EAAEA,CAAA,KAAM7D,6BAA6B,CAAC,IAAI,CAAE;cAAAwC,QAAA,EACpD;YAED;cAAAG,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACE,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACV;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC;EAEX,CAAC;EAED,oBACExD,OAAA,CAAC3B,SAAS;IAAC0E,SAAS,EAAC,MAAM;IAACmC,QAAQ,EAAC,IAAI;IAAAhC,QAAA,gBACvClD,OAAA,CAAC1B,GAAG;MACF4F,EAAE,EAAE;QACFiB,SAAS,EAAE,CAAC;QACZC,OAAO,EAAE,MAAM;QACfC,aAAa,EAAE,QAAQ;QACvBC,UAAU,EAAE;MACd,CAAE;MAAApC,QAAA,gBAEFlD,OAAA,CAACzB,UAAU;QAACwE,SAAS,EAAC,IAAI;QAACI,OAAO,EAAC,IAAI;QAACe,EAAE,EAAE;UAAEE,EAAE,EAAE;QAAE,CAAE;QAAAlB,QAAA,EAAC;MAEvD;QAAAG,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAY,CAAC,eAEbxD,OAAA,CAACtB,KAAK;QAAC6G,SAAS,EAAE,CAAE;QAACrB,EAAE,EAAE;UAAEsB,KAAK,EAAE,MAAM;UAAEC,CAAC,EAAE;QAAE,CAAE;QAAAvC,QAAA,gBAC/ClD,OAAA,CAACzB,UAAU;UAAC4E,OAAO,EAAC,IAAI;UAACC,YAAY;UAACc,EAAE,EAAE;YAAEa,SAAS,EAAE,QAAQ;YAAEX,EAAE,EAAE;UAAE,CAAE;UAAAlB,QAAA,EACtE/C,SAAS,GAAG,OAAO,GAAG;QAA2B;UAAAkD,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACxC,CAAC,EAEZjD,KAAK,iBACJP,OAAA,CAACjB,KAAK;UAAC2G,QAAQ,EAAC,OAAO;UAACxB,EAAE,EAAE;YAAEE,EAAE,EAAE;UAAE,CAAE;UAAAlB,QAAA,EACnC3C;QAAK;UAAA8C,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACD,CACR,EAEArD,SAAS,GAAG2C,eAAe,CAAC,CAAC,GAAG0B,cAAc,CAAC,CAAC;MAAA;QAAAnB,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC5C,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACL,CAAC,eAGNxD,OAAA,CAACf,MAAM;MAAC0G,IAAI,EAAElF,0BAA2B;MAACmF,OAAO,EAAEA,CAAA,KAAMlF,6BAA6B,CAAC,KAAK,CAAE;MAAAwC,QAAA,gBAC5FlD,OAAA,CAACd,WAAW;QAAAgE,QAAA,EAAC;MAAgC;QAAAG,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAa,CAAC,eAC3DxD,OAAA,CAACb,aAAa;QAAA+D,QAAA,gBACZlD,OAAA,CAACZ,iBAAiB;UAAA8D,QAAA,EAAC;QAEnB;UAAAG,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAmB,CAAC,eACpBxD,OAAA,CAACxB,SAAS;UACRuF,SAAS;UACTN,MAAM,EAAC,OAAO;UACdG,EAAE,EAAC,OAAO;UACVC,KAAK,EAAC,iBAAiB;UACvBI,IAAI,EAAC,OAAO;UACZN,SAAS;UACTR,OAAO,EAAC,UAAU;UAClBxB,KAAK,EAAEhB,aAAc;UACrBqD,QAAQ,EAAEjC;QAA0B;UAAAsB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACrC,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACW,CAAC,eAChBxD,OAAA,CAACX,aAAa;QAAA6D,QAAA,gBACZlD,OAAA,CAACvB,MAAM;UAAC8F,OAAO,EAAEA,CAAA,KAAM7D,6BAA6B,CAAC,KAAK,CAAE;UAAAwC,QAAA,EAAC;QAAO;UAAAG,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC,eAC7ExD,OAAA,CAACvB,MAAM;UAAC8F,OAAO,EAAE5B,sBAAuB;UAACQ,OAAO,EAAC,WAAW;UAAAD,QAAA,EAAC;QAAK;UAAAG,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC9D,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACV,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACA,CAAC;AAEhB,CAAC;AAACtD,EAAA,CAlfID,YAAY;EAAA,QAuBEH,OAAO,EACR1B,WAAW;AAAA;AAAAyH,EAAA,GAxBxB5F,YAAY;AAoflB,eAAeA,YAAY;AAAC,IAAA4F,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}