{"ast": null, "code": "import React,{useState,useEffect}from'react';import{Box,Typography,Button,Paper,Grid,Card,CardContent,CardActions,Dialog,DialogTitle,DialogContent,DialogActions,TextField,FormControl,InputLabel,Select,MenuItem,List,ListItem,ListItemText,ListItemIcon,ListItemButton,Divider,Alert,CircularProgress,FormHelperText,IconButton,Table,TableBody,TableCell,TableContainer,TableHead,TableRow}from'@mui/material';import{Add as AddIcon,Edit as EditIcon,Delete as DeleteIcon,History as HistoryIcon,Save as SaveIcon,ViewList as ViewListIcon,Warning as WarningIcon}from'@mui/icons-material';import parcoCaviService from'../../services/parcoCaviService';import ConfigurazioneDialog from'./ConfigurazioneDialog';import BobineFilterableTable from'./BobineFilterableTable';import QuickAddCablesDialog from'./QuickAddCablesDialog';import{validateBobinaData,validateBobinaField,validateBobinaId,isEmpty}from'../../utils/bobinaValidationUtils';import{jsx as _jsx,jsxs as _jsxs}from\"react/jsx-runtime\";const ParcoCavi=_ref=>{let{cantiereId,onSuccess,onError,initialOption=null}=_ref;const[loading,setLoading]=useState(false);const[bobine,setBobine]=useState([]);const[selectedOption,setSelectedOption]=useState(initialOption);const[openDialog,setOpenDialog]=useState(false);const[dialogType,setDialogType]=useState('');const[selectedBobina,setSelectedBobina]=useState(null);const[showQuickAddDialog,setShowQuickAddDialog]=useState(false);const[formData,setFormData]=useState({numero_bobina:'',utility:'',tipologia:'',n_conduttori:'0',// Imposta sempre a '0' per il campo spare\nsezione:'',metri_totali:'',metri_residui:'',stato_bobina:'Disponibile',ubicazione_bobina:'',fornitore:'',n_DDT:'',data_DDT:'',configurazione:''});const[formErrors,setFormErrors]=useState({});const[formWarnings,setFormWarnings]=useState({});const[storicoUtilizzo,setStoricoUtilizzo]=useState([]);const[openConfigDialog,setOpenConfigDialog]=useState(false);const[isFirstInsertion,setIsFirstInsertion]=useState(false);// Carica le bobine disponibili\nconst loadBobine=async()=>{try{setLoading(true);const data=await parcoCaviService.getBobine(cantiereId);setBobine(data);}catch(error){let errorMessage='Errore nel caricamento delle bobine';if(error.detail){errorMessage+=': '+(typeof error.detail==='object'?JSON.stringify(error.detail):error.detail);}else if(error.message){errorMessage+=': '+error.message;}onError(errorMessage);console.error('Errore nel caricamento delle bobine:',error);}finally{setLoading(false);}};// Carica lo storico utilizzo bobine\nconst loadStoricoUtilizzo=async()=>{try{setLoading(true);const data=await parcoCaviService.getStoricoUtilizzo(cantiereId);setStoricoUtilizzo(data);}catch(error){let errorMessage='Errore nel caricamento dello storico utilizzo';if(error.detail){errorMessage+=': '+(typeof error.detail==='object'?JSON.stringify(error.detail):error.detail);}else if(error.message){errorMessage+=': '+error.message;}onError(errorMessage);console.error('Errore nel caricamento dello storico utilizzo:',error);}finally{setLoading(false);}};// Carica i dati all'avvio del componente e gestisce l'opzione iniziale\n// Utilizziamo una ref per tenere traccia se l'effetto è già stato eseguito\nconst initialLoadDone=React.useRef(false);useEffect(()=>{// Esegui solo una volta all'avvio del componente\nif(!initialLoadDone.current){console.log('Primo caricamento del componente, initialOption:',initialOption);initialLoadDone.current=true;if(initialOption==='creaBobina'){console.log('Avvio processo creazione bobina');// IMPORTANTE: Per il primo caricamento con creaBobina, forziamo l'apertura del dialog di configurazione\nconsole.log('FORZANDO APERTURA DIALOG CONFIGURAZIONE ALL\\'AVVIO');setIsFirstInsertion(true);setOpenConfigDialog(true);}else if(initialOption){console.log('Eseguendo handleOptionSelect con:',initialOption);handleOptionSelect(initialOption);}else{console.log('Caricando bobine');loadBobine();}}},[]);// Dipendenze vuote per eseguire solo al mount\n// Verifica se è il primo inserimento di una bobina per un cantiere\nconst checkIfFirstInsertion=async()=>{// Variabile per memorizzare la configurazione\nlet configurazione='s';// Valore predefinito\ntry{// Previene chiamate multiple\nif(loading){console.log('Operazione già in corso, uscita');return;}// Assicuriamoci che nessun dialog sia aperto\nsetOpenDialog(false);setOpenConfigDialog(false);// Chiudi anche il dialog di configurazione se aperto\nsetLoading(true);console.log('Verificando se è il primo inserimento per cantiere ID:',cantiereId);// Gestione caso in cui cantiereId non sia valido\nif(!cantiereId||isNaN(parseInt(cantiereId))){onError('ID cantiere non valido');console.error('ID cantiere non valido:',cantiereId);setLoading(false);return;}// Chiamata API reale\nlet isFirst=false;try{console.log('Verificando se è il primo inserimento per cantiere ID:',cantiereId);const response=await parcoCaviService.isFirstBobinaInsertion(cantiereId);isFirst=response.is_first_insertion;// Controlla se c'è una configurazione salvata in localStorage\nconst savedConfig=localStorage.getItem(`cantiere_${cantiereId}_config`);// Usa la configurazione salvata in localStorage se disponibile, altrimenti usa quella dal server\nconfigurazione=savedConfig||response.configurazione||'s';console.log('Configurazione da localStorage:',savedConfig);console.log('Configurazione dal server:',response.configurazione);console.log('Configurazione finale utilizzata:',configurazione);setIsFirstInsertion(isFirst);console.log('È il primo inserimento di una bobina?',isFirst);console.log('Configurazione esistente:',configurazione);}catch(error){console.error('Errore durante la verifica del primo inserimento:',error);// In caso di errore, assumiamo che non sia il primo inserimento\nsetIsFirstInsertion(false);onError('Errore durante la verifica del primo inserimento. Procedendo con inserimento standard.');}if(isFirst){// Se è il primo inserimento, mostra il dialog di configurazione\nconsole.log('Mostrando il dialog di configurazione');// Assicuriamoci che il dialog di creazione sia chiuso\nsetOpenDialog(false);// IMPORTANTE: Forziamo l'apertura del dialog di configurazione\nconsole.log('FORZANDO APERTURA DIALOG CONFIGURAZIONE');setOpenConfigDialog(true);}else{// Non è il primo inserimento, procedi con il form normale\nconsole.log('Non è il primo inserimento, mostrando il form normale');// Ottieni il prossimo numero di bobina solo se la configurazione è automatica\nlet nextBobinaNumber='1';if(configurazione==='s'){try{// Ottieni l'ultimo numero di bobina dal backend\nconst bobine=await parcoCaviService.getBobine(cantiereId);if(bobine&&bobine.length>0){// Filtra solo le bobine con numero_bobina numerico\nconst numericBobine=bobine.filter(b=>b.numero_bobina&&/^\\d+$/.test(b.numero_bobina));// Log per debug\nconsole.log('Bobine totali:',bobine.length);console.log('Bobine con numero numerico:',numericBobine.length);console.log('Bobine numeriche:',numericBobine.map(b=>b.numero_bobina));if(numericBobine.length>0){// Trova il numero massimo tra le bobine esistenti\nconst maxNumber=Math.max(...numericBobine.map(b=>parseInt(b.numero_bobina,10)));console.log('Numero massimo trovato:',maxNumber);nextBobinaNumber=String(maxNumber+1);}}console.log('Prossimo numero bobina:',nextBobinaNumber);}catch(error){console.error('Errore nel recupero del prossimo numero bobina:',error);// In caso di errore, usa 1 come default\nnextBobinaNumber='1';}}setDialogType('creaBobina');setFormData({// In modalità automatica, imposta il numero progressivo\n// In modalità manuale, lascia vuoto per far inserire all'utente\nnumero_bobina:configurazione==='s'?nextBobinaNumber:'',utility:'',tipologia:'',n_conduttori:'0',// Imposta sempre a '0' per il campo spare\nsezione:'',metri_totali:'',metri_residui:'',stato_bobina:'Disponibile',ubicazione_bobina:'',fornitore:'',n_DDT:'',data_DDT:'',configurazione:configurazione// Usa la configurazione esistente\n});setOpenDialog(true);}}catch(error){// Gestione dettagliata dell'errore\nlet errorMessage='Errore nel controllo dell\\'inserimento della prima bobina';if(error.response){var _error$response$data;// Errore di risposta dal server\nerrorMessage+=`: ${error.response.status} - ${((_error$response$data=error.response.data)===null||_error$response$data===void 0?void 0:_error$response$data.detail)||'Errore server'}`;console.error('Dettagli errore API:',error.response);}else if(error.request){// Errore di rete (nessuna risposta ricevuta)\nerrorMessage+=': Errore di connessione al server';}else{// Errore generico\nerrorMessage+=`: ${error.message||'Errore sconosciuto'}`;}onError(errorMessage);console.error('Errore completo:',error);// In caso di errore, mantieni la configurazione esistente o usa il default\n// Non forzare il reset a 's' per evitare di perdere la configurazione manuale\nif(!configurazione){configurazione='s';// Fallback al valore di default solo se non è già impostato\n}setDialogType('creaBobina');setFormData({numero_bobina:configurazione==='s'?'1':'',utility:'',tipologia:'',n_conduttori:'0',// Imposta sempre a '0' per il campo spare\nsezione:'',metri_totali:'',metri_residui:'',stato_bobina:'Disponibile',ubicazione_bobina:'',fornitore:'',n_DDT:'',data_DDT:'',configurazione:configurazione// Usa la configurazione esistente o il default\n});setOpenDialog(true);}finally{setLoading(false);}};// Gestisce la conferma della configurazione\nconst handleConfigConfirm=async configValue=>{console.log('Configurazione selezionata:',configValue);// Salva la configurazione selezionata in localStorage per persistenza\nlocalStorage.setItem(`cantiere_${cantiereId}_config`,configValue);setLoading(true);try{// Ottieni il prossimo numero di bobina se la configurazione è automatica\nlet nextBobinaNumber='1';if(configValue==='s'){try{// Ottieni l'ultimo numero di bobina dal backend\nconst bobine=await parcoCaviService.getBobine(cantiereId);if(bobine&&bobine.length>0){// Filtra solo le bobine con numero_bobina numerico\nconst numericBobine=bobine.filter(b=>b.numero_bobina&&/^\\d+$/.test(b.numero_bobina));// Log per debug\nconsole.log('Bobine totali:',bobine.length);console.log('Bobine con numero numerico:',numericBobine.length);console.log('Bobine numeriche:',numericBobine.map(b=>b.numero_bobina));if(numericBobine.length>0){// Trova il numero massimo tra le bobine esistenti\nconst maxNumber=Math.max(...numericBobine.map(b=>parseInt(b.numero_bobina,10)));console.log('Numero massimo trovato:',maxNumber);nextBobinaNumber=String(maxNumber+1);}}console.log('Prossimo numero bobina:',nextBobinaNumber);}catch(error){console.error('Errore nel recupero del prossimo numero bobina:',error);// In caso di errore, usa 1 come default\nnextBobinaNumber='1';}}// Imposta i valori di default per la bobina\nconst defaultFormData={// In modalità automatica, imposta il numero progressivo\n// In modalità manuale, lascia vuoto per far inserire all'utente\nnumero_bobina:configValue==='s'?nextBobinaNumber:'',utility:'',tipologia:'',n_conduttori:'0',// Imposta sempre a '0' per il campo spare\nsezione:'',metri_totali:'',metri_residui:'',stato_bobina:'Disponibile',ubicazione_bobina:'',fornitore:'',n_DDT:'',data_DDT:'',configurazione:configValue// Imposta la configurazione scelta\n};console.log('Impostando i dati del form con configurazione:',configValue,'numero_bobina:',defaultFormData.numero_bobina);// Importante: prima prepara il form, poi chiudi il dialog di configurazione\n// e solo dopo apri il dialog di creazione\nsetFormData(defaultFormData);setDialogType('creaBobina');// Chiudi il dialog di configurazione\nsetOpenConfigDialog(false);// Piccolo timeout per assicurarsi che il dialog di configurazione sia chiuso\n// prima di aprire il dialog di creazione\nsetTimeout(()=>{setOpenDialog(true);console.log('Dialog di creazione bobina aperto con numero_bobina:',defaultFormData.numero_bobina);},300);}catch(error){console.error('Errore durante la preparazione del form:',error);onError('Errore durante la preparazione del form: '+(error.message||'Errore sconosciuto'));}finally{setLoading(false);}};// Gestisce la selezione di un'opzione dal menu\nconst handleOptionSelect=option=>{setSelectedOption(option);if(option==='visualizzaBobine'){loadBobine();}else if(option==='creaBobina'){checkIfFirstInsertion();}else if(option==='modificaBobina'){loadBobine();setDialogType('selezionaBobina');setOpenDialog(true);}else if(option==='eliminaBobina'){loadBobine();setDialogType('eliminaBobina');setOpenDialog(true);}else if(option==='visualizzaStorico'){loadStoricoUtilizzo();setDialogType('visualizzaStorico');setOpenDialog(true);}};// Gestisce la chiusura del dialog\nconst handleCloseDialog=()=>{setOpenDialog(false);setSelectedBobina(null);// Recupera la configurazione salvata per questo cantiere\nconst savedConfig=localStorage.getItem(`cantiere_${cantiereId}_config`)||'s';setFormData({numero_bobina:'',utility:'',tipologia:'',n_conduttori:'0',// Imposta sempre a '0' per il campo spare\nsezione:'',metri_totali:'',metri_residui:'',stato_bobina:'Disponibile',ubicazione_bobina:'',fornitore:'',n_DDT:'',data_DDT:'',configurazione:savedConfig// Mantieni la configurazione salvata\n});setFormErrors({});setFormWarnings({});};// Gestisce la selezione di una bobina\nconst handleBobinaSelect=bobina=>{console.log('Bobina selezionata:',bobina);setSelectedBobina(bobina);if(dialogType==='selezionaBobina'){setDialogType('modificaBobina');// Assicurati che i campi numerici siano numeri e che i campi stringa siano stringhe\nsetFormData({numero_bobina:bobina.numero_bobina||'',utility:String(bobina.utility||''),tipologia:String(bobina.tipologia||''),n_conduttori:bobina.n_conduttori!==null&&bobina.n_conduttori!==undefined?String(bobina.n_conduttori):'',sezione:bobina.sezione!==null&&bobina.sezione!==undefined?String(bobina.sezione):'',metri_totali:bobina.metri_totali!==null&&bobina.metri_totali!==undefined?Number(bobina.metri_totali):'',metri_residui:bobina.metri_residui!==null&&bobina.metri_residui!==undefined?Number(bobina.metri_residui):'',stato_bobina:String(bobina.stato_bobina||'Disponibile'),ubicazione_bobina:String(bobina.ubicazione_bobina||''),fornitore:String(bobina.fornitore||''),n_DDT:String(bobina.n_DDT||''),data_DDT:bobina.data_DDT||'',configurazione:String(bobina.configurazione||'s')});console.log('Form data impostati per la modifica:',{numero_bobina:bobina.numero_bobina,utility:bobina.utility,tipologia:bobina.tipologia,n_conduttori:bobina.n_conduttori,sezione:bobina.sezione,metri_totali:bobina.metri_totali,metri_residui:bobina.metri_residui});}};// Gestisce il cambio dei valori nel form con validazione\nconst handleFormChange=e=>{const{name,value}=e.target;// Aggiorna il valore nel form\nsetFormData({...formData,[name]:value});// Valida il campo\nif(dialogType==='creaBobina'||dialogType==='modificaBobina'){// Validazione speciale per numero_bobina quando configurazione è 'n'\nif(name==='numero_bobina'&&formData.configurazione==='n'){const idResult=validateBobinaId(value);if(!idResult.valid){setFormErrors(prev=>({...prev,[name]:idResult.message}));}else{setFormErrors(prev=>{const newErrors={...prev};delete newErrors[name];return newErrors;});}return;}const result=validateBobinaField(name,value);// Aggiorna gli errori\nif(!result.valid){setFormErrors(prev=>({...prev,[name]:result.message}));}else{setFormErrors(prev=>{const newErrors={...prev};delete newErrors[name];return newErrors;});// Aggiorna gli avvisi\nif(result.warning){setFormWarnings(prev=>({...prev,[name]:result.message}));}else{setFormWarnings(prev=>{const newWarnings={...prev};delete newWarnings[name];return newWarnings;});}}}};// Gestisce il salvataggio del form\nconst handleSave=async()=>{try{// Validazione completa dei dati prima del salvataggio\nif(dialogType==='creaBobina'||dialogType==='modificaBobina'){const validation=validateBobinaData(formData);if(!validation.isValid){setFormErrors(validation.errors);setFormWarnings(validation.warnings);onError('Correggi gli errori nel form prima di salvare');return;}// Verifica aggiuntiva per il campo numero_bobina quando la configurazione è manuale\nif(formData.configurazione==='n'&&(!formData.numero_bobina||formData.numero_bobina.trim()==='')){setFormErrors({...formErrors,numero_bobina:'L\\'ID della bobina è obbligatorio'});onError('L\\'ID della bobina è obbligatorio');return;}}setLoading(true);console.log('Salvataggio dati bobina in corso...');if(dialogType==='creaBobina'){// Prepara i dati per la creazione della bobina\nconst bobinaData={...formData,// Assicurati che tutti i campi siano nel formato corretto\nnumero_bobina:String(formData.numero_bobina||''),utility:String(formData.utility||''),tipologia:String(formData.tipologia||''),n_conduttori:String(formData.n_conduttori||''),sezione:String(formData.sezione||''),metri_totali:parseFloat(formData.metri_totali)||0,ubicazione_bobina:String(formData.ubicazione_bobina||'TBD'),fornitore:String(formData.fornitore||'TBD'),n_DDT:String(formData.n_DDT||'TBD'),data_DDT:formData.data_DDT||null,// Assicurati che la configurazione sia impostata correttamente\nconfigurazione:String(formData.configurazione||'s')};console.log('Dati bobina da inviare:',bobinaData);try{// Log dei dati che stiamo per inviare\nconsole.log('Invio dati bobina al backend:',JSON.stringify(bobinaData,null,2));// Invia i dati al backend\nawait parcoCaviService.createBobina(cantiereId,bobinaData);onSuccess('Bobina creata con successo');// Chiudi il dialog\nhandleCloseDialog();// Imposta l'opzione selezionata a visualizzaBobine e carica le bobine\nsetSelectedOption('visualizzaBobine');loadBobine();// Notifica al componente padre che dovrebbe reindirizzare alla pagina di visualizzazione bobine\nif(typeof window!=='undefined'){// Usa un evento personalizzato per comunicare con il componente padre\nconst event=new CustomEvent('redirectToVisualizzaBobine',{detail:{cantiereId}});window.dispatchEvent(event);}}catch(error){console.error('Errore durante la creazione della bobina:',error);// Gestione dettagliata dell'errore\nlet errorMessage='Errore durante la creazione della bobina';if(error.detail){errorMessage=typeof error.detail==='object'?JSON.stringify(error.detail):error.detail;}else if(error.message){errorMessage=error.message;}// Log dettagliato dell'errore\nconsole.error('Dettagli errore:',errorMessage);console.error('Dati inviati:',JSON.stringify(bobinaData,null,2));onError(errorMessage);setLoading(false);return;}}else if(dialogType==='modificaBobina'){// Per la modifica, usa l'ID bobina completo o il numero bobina\nconst bobinaId=selectedBobina.id_bobina||selectedBobina.numero_bobina||formData.numero_bobina;console.log('Modifica bobina con ID:',bobinaId);console.log('Dati da inviare:',formData);try{const response=await parcoCaviService.updateBobina(cantiereId,bobinaId,formData);console.log('Risposta modifica bobina:',response);onSuccess('Bobina modificata con successo');}catch(error){console.error('Errore durante la modifica della bobina:',error);let errorMessage='Errore durante la modifica della bobina';if(error.detail){errorMessage=typeof error.detail==='object'?JSON.stringify(error.detail):error.detail;}else if(error.message){errorMessage=error.message;}onError(errorMessage);setLoading(false);return;}}else if(dialogType==='eliminaBobina'){// Per l'eliminazione, usa l'ID bobina completo\nconst bobinaId=selectedBobina.id_bobina||selectedBobina.numero_bobina;const response=await parcoCaviService.deleteBobina(cantiereId,bobinaId);// Verifica se è stata eliminata l'ultima bobina\nif(response.is_last_bobina){console.log(`Eliminata l'ultima bobina del cantiere ${cantiereId}. Il parco cavi è ora vuoto.`);onSuccess('Bobina eliminata con successo. Il parco cavi è ora vuoto.');// Non forziamo più l'apertura del dialog di creazione quando l'ultima bobina viene eliminata\n// L'utente può decidere se inserire una nuova bobina o lasciare il parco cavi vuoto\n}else{onSuccess('Bobina eliminata con successo');}}handleCloseDialog();loadBobine();// Ricarica le bobine dopo l'operazione\n}catch(error){let errorMessage='Errore durante l\\'operazione';if(error.detail){errorMessage+=': '+(typeof error.detail==='object'?JSON.stringify(error.detail):error.detail);}else if(error.message){errorMessage+=': '+error.message;}else{errorMessage+=': Errore sconosciuto';}onError(errorMessage);console.error('Errore durante l\\'operazione:',error);}finally{setLoading(false);}};// La visualizzazione delle bobine è ora gestita dal componente BobineFilterableTable\nconst renderBobineCards=()=>{return/*#__PURE__*/_jsx(BobineFilterableTable,{bobine:bobine,loading:loading,onFilteredDataChange:filteredData=>console.log('Bobine filtrate:',filteredData.length),onEdit:bobina=>{setSelectedBobina(bobina);setDialogType('modificaBobina');setOpenDialog(true);},onDelete:bobina=>{setSelectedBobina(bobina);setDialogType('eliminaBobina');setOpenDialog(true);},onViewHistory:bobina=>{setSelectedBobina(bobina);loadStoricoUtilizzo();setDialogType('visualizzaStorico');setOpenDialog(true);},onQuickAdd:bobina=>{setSelectedBobina(bobina);setShowQuickAddDialog(true);}});};// Renderizza il dialog in base al tipo\nconst renderDialog=()=>{if(dialogType==='creaBobina'||dialogType==='modificaBobina'){return/*#__PURE__*/_jsxs(Dialog,{open:openDialog,onClose:handleCloseDialog,maxWidth:\"md\",fullWidth:true,children:[/*#__PURE__*/_jsx(DialogTitle,{children:dialogType==='creaBobina'?'Crea Nuova Bobina':'Modifica Bobina'}),/*#__PURE__*/_jsxs(DialogContent,{children:[dialogType==='modificaBobina'&&/*#__PURE__*/_jsxs(Alert,{severity:\"info\",sx:{mb:2,mt:1},children:[/*#__PURE__*/_jsx(Typography,{variant:\"subtitle2\",fontWeight:\"bold\",children:\"Condizioni per la modifica:\"}),/*#__PURE__*/_jsxs(\"ul\",{children:[/*#__PURE__*/_jsx(\"li\",{children:\"La bobina deve essere nello stato \\\"Disponibile\\\"\"}),/*#__PURE__*/_jsx(\"li\",{children:\"La bobina non deve essere associata a nessun cavo\"}),/*#__PURE__*/_jsx(\"li\",{children:\"Se modifichi i metri totali, i metri residui verranno aggiornati automaticamente\"})]}),/*#__PURE__*/_jsx(Box,{sx:{mt:1},children:/*#__PURE__*/_jsxs(Typography,{variant:\"body2\",children:[/*#__PURE__*/_jsx(\"strong\",{children:\"Stato attuale:\"}),\" \",(selectedBobina===null||selectedBobina===void 0?void 0:selectedBobina.stato_bobina)||'N/A']})})]}),Object.keys(formWarnings).length>0&&/*#__PURE__*/_jsxs(Alert,{severity:\"warning\",sx:{mb:2,mt:1},children:[/*#__PURE__*/_jsx(Typography,{variant:\"subtitle2\",children:\"Attenzione:\"}),/*#__PURE__*/_jsx(\"ul\",{style:{margin:0,paddingLeft:'20px'},children:Object.values(formWarnings).map((warning,index)=>/*#__PURE__*/_jsx(\"li\",{children:warning},index))})]}),/*#__PURE__*/_jsxs(Grid,{container:true,spacing:2,sx:{mt:1},children:[/*#__PURE__*/_jsx(Grid,{item:true,xs:12,sm:6,children:/*#__PURE__*/_jsx(TextField,{name:\"numero_bobina\",label:\"ID Bobina\",fullWidth:true,variant:\"outlined\",value:formData.numero_bobina,onChange:handleFormChange,disabled:dialogType==='modificaBobina'||dialogType==='creaBobina'&&formData.configurazione==='s',required:true,error:!!formErrors.numero_bobina,InputProps:{sx:{bgcolor:formData.configurazione==='s'?'#f5f5f5':'transparent',fontWeight:'bold'}},helperText:formErrors.numero_bobina||(dialogType==='creaBobina'&&formData.configurazione==='s'?`ID completo: C${cantiereId}_B${formData.numero_bobina||''} (generato automaticamente)`:dialogType==='creaBobina'&&formData.configurazione==='n'?`Inserisci l'ID della bobina (es. A123). ID completo sarà: C${cantiereId}_B{tuo input}`:''),type:formData.configurazione==='s'?\"text\":\"text\"})}),/*#__PURE__*/_jsx(Grid,{item:true,xs:12,sm:6,children:/*#__PURE__*/_jsx(TextField,{name:\"utility\",label:\"Utility\",fullWidth:true,variant:\"outlined\",value:formData.utility,onChange:handleFormChange,required:true,error:!!formErrors.utility,helperText:formErrors.utility||''})}),/*#__PURE__*/_jsx(Grid,{item:true,xs:12,sm:6,children:/*#__PURE__*/_jsx(TextField,{name:\"tipologia\",label:\"Tipologia\",fullWidth:true,variant:\"outlined\",value:formData.tipologia,onChange:handleFormChange,required:true,error:!!formErrors.tipologia,helperText:formErrors.tipologia||''})}),/*#__PURE__*/_jsx(Grid,{item:true,xs:12,sm:6,children:/*#__PURE__*/_jsx(TextField,{name:\"sezione\",label:\"Formazione\",fullWidth:true,variant:\"outlined\",value:formData.sezione,onChange:handleFormChange,required:true,error:!!formErrors.sezione,helperText:formErrors.sezione||''})}),/*#__PURE__*/_jsx(Grid,{item:true,xs:12,sm:6,children:/*#__PURE__*/_jsx(TextField,{name:\"metri_totali\",label:\"Metri Totali\",type:\"number\",fullWidth:true,variant:\"outlined\",value:formData.metri_totali,onChange:handleFormChange,required:true,error:!!formErrors.metri_totali,helperText:formErrors.metri_totali||''})}),dialogType==='modificaBobina'&&/*#__PURE__*/_jsx(Grid,{item:true,xs:12,sm:6,children:/*#__PURE__*/_jsx(TextField,{name:\"metri_residui\",label:\"Metri Residui\",type:\"number\",fullWidth:true,variant:\"outlined\",value:formData.metri_residui,onChange:handleFormChange,required:true,disabled:true,helperText:\"I metri residui non possono essere modificati direttamente\"})}),/*#__PURE__*/_jsx(Grid,{item:true,xs:12,sm:6,children:/*#__PURE__*/_jsxs(FormControl,{fullWidth:true,children:[/*#__PURE__*/_jsx(InputLabel,{id:\"stato-bobina-label\",children:\"Stato Bobina\"}),/*#__PURE__*/_jsxs(Select,{labelId:\"stato-bobina-label\",name:\"stato_bobina\",value:formData.stato_bobina,label:\"Stato Bobina\",onChange:handleFormChange,disabled:dialogType==='creaBobina',children:[/*#__PURE__*/_jsx(MenuItem,{value:\"Disponibile\",children:\"Disponibile\"}),/*#__PURE__*/_jsx(MenuItem,{value:\"In uso\",children:\"In uso\"}),/*#__PURE__*/_jsx(MenuItem,{value:\"Terminata\",children:\"Terminata\"}),/*#__PURE__*/_jsx(MenuItem,{value:\"Danneggiata\",children:\"Danneggiata\"}),/*#__PURE__*/_jsx(MenuItem,{value:\"Over\",children:\"Over\"})]}),dialogType==='creaBobina'&&/*#__PURE__*/_jsx(FormHelperText,{children:\"Per una nuova bobina, lo stato \\xE8 sempre \\\"Disponibile\\\"\"})]})}),/*#__PURE__*/_jsx(Grid,{item:true,xs:12,sm:6,children:/*#__PURE__*/_jsx(TextField,{name:\"ubicazione_bobina\",label:\"Ubicazione Bobina\",fullWidth:true,variant:\"outlined\",value:formData.ubicazione_bobina,onChange:handleFormChange,error:!!formErrors.ubicazione_bobina,helperText:formErrors.ubicazione_bobina||''})}),/*#__PURE__*/_jsx(Grid,{item:true,xs:12,sm:6,children:/*#__PURE__*/_jsx(TextField,{name:\"fornitore\",label:\"Fornitore\",fullWidth:true,variant:\"outlined\",value:formData.fornitore,onChange:handleFormChange,error:!!formErrors.fornitore,helperText:formErrors.fornitore||''})}),/*#__PURE__*/_jsx(Grid,{item:true,xs:12,sm:6,children:/*#__PURE__*/_jsx(TextField,{name:\"n_DDT\",label:\"Numero DDT\",fullWidth:true,variant:\"outlined\",value:formData.n_DDT,onChange:handleFormChange,error:!!formErrors.n_DDT,helperText:formErrors.n_DDT||''})}),/*#__PURE__*/_jsx(Grid,{item:true,xs:12,sm:6,children:/*#__PURE__*/_jsx(TextField,{name:\"data_DDT\",label:\"Data DDT (YYYY-MM-DD)\",fullWidth:true,variant:\"outlined\",value:formData.data_DDT,onChange:handleFormChange,placeholder:\"YYYY-MM-DD\",error:!!formErrors.data_DDT,helperText:formErrors.data_DDT||''})}),/*#__PURE__*/_jsx(Grid,{item:true,xs:12,sm:6,children:/*#__PURE__*/_jsx(TextField,{name:\"configurazione\",label:\"Modalit\\xE0 Numerazione\",fullWidth:true,variant:\"outlined\",value:formData.configurazione==='s'?'Automatica':'Manuale',InputProps:{readOnly:true,sx:{bgcolor:'#f5f5f5'}},helperText:/*#__PURE__*/_jsx(Box,{sx:{fontWeight:'medium',color:formData.configurazione==='s'?'success.main':'info.main'},children:formData.configurazione==='s'?'Numerazione progressiva automatica (1, 2, 3, ...)':'Inserimento manuale dell\\'ID bobina (es. A123, SPEC01, ...)'})})})]})]}),/*#__PURE__*/_jsxs(DialogActions,{children:[/*#__PURE__*/_jsx(Button,{onClick:handleCloseDialog,children:\"Annulla\"}),/*#__PURE__*/_jsx(Button,{onClick:handleSave,disabled:loading||Object.keys(formErrors).length>0,startIcon:loading?/*#__PURE__*/_jsx(CircularProgress,{size:20}):/*#__PURE__*/_jsx(SaveIcon,{}),variant:\"contained\",color:\"primary\",children:\"Salva\"})]})]});}else if(dialogType==='selezionaBobina'){return/*#__PURE__*/_jsxs(Dialog,{open:openDialog,onClose:handleCloseDialog,maxWidth:\"md\",fullWidth:true,children:[/*#__PURE__*/_jsx(DialogTitle,{children:\"Seleziona Bobina da Modificare\"}),/*#__PURE__*/_jsx(DialogContent,{children:loading?/*#__PURE__*/_jsx(CircularProgress,{}):bobine.length===0?/*#__PURE__*/_jsx(Alert,{severity:\"info\",children:\"Nessuna bobina disponibile\"}):/*#__PURE__*/_jsx(List,{children:bobine.map(bobina=>/*#__PURE__*/_jsx(ListItem,{button:true,onClick:()=>handleBobinaSelect(bobina),children:/*#__PURE__*/_jsx(ListItemText,{primary:`Bobina: ${bobina.numero_bobina}`,secondary:`Tipologia: ${bobina.tipologia||'N/A'} - Utility: ${bobina.utility||'N/A'} - Residuo: ${bobina.metri_residui||'N/A'} m`})},bobina.numero_bobina))})}),/*#__PURE__*/_jsx(DialogActions,{children:/*#__PURE__*/_jsx(Button,{onClick:handleCloseDialog,children:\"Annulla\"})})]});}else if(dialogType==='eliminaBobina'){return/*#__PURE__*/_jsxs(Dialog,{open:openDialog,onClose:handleCloseDialog,maxWidth:\"sm\",fullWidth:true,children:[/*#__PURE__*/_jsx(DialogTitle,{children:\"Elimina Bobina\"}),/*#__PURE__*/_jsx(DialogContent,{children:!selectedBobina?loading?/*#__PURE__*/_jsx(CircularProgress,{}):bobine.length===0?/*#__PURE__*/_jsx(Alert,{severity:\"info\",children:\"Nessuna bobina disponibile\"}):/*#__PURE__*/_jsx(List,{children:bobine.map(bobina=>/*#__PURE__*/_jsx(ListItem,{button:true,onClick:()=>setSelectedBobina(bobina),children:/*#__PURE__*/_jsx(ListItemText,{primary:`Bobina: ${bobina.numero_bobina}`,secondary:`Tipologia: ${bobina.tipologia||'N/A'} - Utility: ${bobina.utility||'N/A'} - Residuo: ${bobina.metri_residui||'N/A'} m`})},bobina.numero_bobina))}):/*#__PURE__*/_jsxs(Box,{children:[/*#__PURE__*/_jsxs(Alert,{severity:\"warning\",sx:{mb:2},children:[\"Sei sicuro di voler eliminare la bobina \",selectedBobina.numero_bobina,\"?\"]}),/*#__PURE__*/_jsx(Typography,{variant:\"body1\",sx:{mb:2},children:\"Questa operazione non pu\\xF2 essere annullata.\"}),/*#__PURE__*/_jsxs(Alert,{severity:\"info\",sx:{mb:2},children:[/*#__PURE__*/_jsx(Typography,{variant:\"subtitle2\",fontWeight:\"bold\",children:\"Condizioni per l'eliminazione:\"}),/*#__PURE__*/_jsxs(\"ul\",{children:[/*#__PURE__*/_jsx(\"li\",{children:\"La bobina deve essere completamente integra (metri residui = metri totali)\"}),/*#__PURE__*/_jsx(\"li\",{children:\"La bobina deve essere nello stato \\\"Disponibile\\\"\"}),/*#__PURE__*/_jsx(\"li\",{children:\"La bobina non deve essere associata a nessun cavo\"})]})]}),/*#__PURE__*/_jsxs(Box,{sx:{display:'flex',flexDirection:'column',gap:1,mb:2},children:[/*#__PURE__*/_jsxs(Typography,{variant:\"body2\",children:[/*#__PURE__*/_jsx(\"strong\",{children:\"Stato attuale:\"}),\" \",selectedBobina.stato_bobina||'N/A']}),/*#__PURE__*/_jsxs(Typography,{variant:\"body2\",children:[/*#__PURE__*/_jsx(\"strong\",{children:\"Metri totali:\"}),\" \",selectedBobina.metri_totali||'N/A']}),/*#__PURE__*/_jsxs(Typography,{variant:\"body2\",children:[/*#__PURE__*/_jsx(\"strong\",{children:\"Metri residui:\"}),\" \",selectedBobina.metri_residui||'N/A']})]})]})}),/*#__PURE__*/_jsxs(DialogActions,{children:[/*#__PURE__*/_jsx(Button,{onClick:handleCloseDialog,children:\"Annulla\"}),selectedBobina&&/*#__PURE__*/_jsx(Button,{onClick:handleSave,disabled:loading,color:\"error\",startIcon:loading?/*#__PURE__*/_jsx(CircularProgress,{size:20}):/*#__PURE__*/_jsx(DeleteIcon,{}),children:\"Elimina\"})]})]});}else if(dialogType==='visualizzaStorico'){return/*#__PURE__*/_jsxs(Dialog,{open:openDialog,onClose:handleCloseDialog,maxWidth:\"lg\",fullWidth:true,children:[/*#__PURE__*/_jsx(DialogTitle,{children:\"Storico Utilizzo Bobine\"}),/*#__PURE__*/_jsx(DialogContent,{children:loading?/*#__PURE__*/_jsx(CircularProgress,{}):storicoUtilizzo.length===0?/*#__PURE__*/_jsx(Alert,{severity:\"info\",children:\"Nessun dato storico disponibile\"}):/*#__PURE__*/_jsx(TableContainer,{component:Paper,sx:{mt:2},children:/*#__PURE__*/_jsxs(Table,{size:\"small\",children:[/*#__PURE__*/_jsx(TableHead,{children:/*#__PURE__*/_jsxs(TableRow,{children:[/*#__PURE__*/_jsx(TableCell,{children:\"Bobina\"}),/*#__PURE__*/_jsx(TableCell,{children:\"Utility\"}),/*#__PURE__*/_jsx(TableCell,{children:\"Tipologia\"}),/*#__PURE__*/_jsx(TableCell,{children:\"Formazione\"}),/*#__PURE__*/_jsx(TableCell,{children:\"Metri Totali\"}),/*#__PURE__*/_jsx(TableCell,{children:\"Metri Residui\"}),/*#__PURE__*/_jsx(TableCell,{children:\"Cavi Associati\"})]})}),/*#__PURE__*/_jsx(TableBody,{children:storicoUtilizzo.map((record,index)=>/*#__PURE__*/_jsxs(TableRow,{children:[/*#__PURE__*/_jsx(TableCell,{children:record.numero_bobina}),/*#__PURE__*/_jsx(TableCell,{children:record.utility}),/*#__PURE__*/_jsx(TableCell,{children:record.tipologia}),/*#__PURE__*/_jsx(TableCell,{children:record.sezione}),/*#__PURE__*/_jsx(TableCell,{children:record.metri_totali}),/*#__PURE__*/_jsx(TableCell,{children:record.metri_residui}),/*#__PURE__*/_jsx(TableCell,{children:record.cavi.length})]},index))})]})})}),/*#__PURE__*/_jsx(DialogActions,{children:/*#__PURE__*/_jsx(Button,{onClick:handleCloseDialog,children:\"Chiudi\"})})]});}return null;};return/*#__PURE__*/_jsxs(Box,{children:[selectedOption==='visualizzaBobine'&&!openDialog?/*#__PURE__*/_jsxs(Paper,{sx:{p:3},children:[/*#__PURE__*/_jsx(Box,{sx:{display:'flex',justifyContent:'flex-end',alignItems:'center',mb:2},children:/*#__PURE__*/_jsxs(Box,{sx:{display:'flex',gap:2},children:[/*#__PURE__*/_jsx(Button,{variant:\"outlined\",color:\"inherit\",startIcon:/*#__PURE__*/_jsx(HistoryIcon,{}),onClick:()=>{loadStoricoUtilizzo();setDialogType('visualizzaStorico');setOpenDialog(true);},sx:{fontWeight:'medium',borderRadius:0},children:\"Storico Utilizzo\"}),/*#__PURE__*/_jsx(Button,{variant:\"contained\",color:\"inherit\",startIcon:/*#__PURE__*/_jsx(AddIcon,{}),onClick:()=>checkIfFirstInsertion(),sx:{fontWeight:'medium',borderRadius:0},children:\"Crea Nuova Bobina\"})]})}),loading?/*#__PURE__*/_jsx(Box,{sx:{display:'flex',justifyContent:'center',my:4},children:/*#__PURE__*/_jsx(CircularProgress,{})}):/*#__PURE__*/_jsx(BobineFilterableTable,{bobine:bobine,loading:loading,onFilteredDataChange:filteredData=>console.log('Bobine filtrate:',filteredData.length),onEdit:bobina=>{setSelectedBobina(bobina);setDialogType('modificaBobina');setOpenDialog(true);},onDelete:bobina=>{setSelectedBobina(bobina);setDialogType('eliminaBobina');setOpenDialog(true);},onViewHistory:bobina=>{setSelectedBobina(bobina);loadStoricoUtilizzo();setDialogType('visualizzaStorico');setOpenDialog(true);},onQuickAdd:bobina=>{setSelectedBobina(bobina);setShowQuickAddDialog(true);}})]}):!openDialog?/*#__PURE__*/_jsx(Paper,{sx:{p:3,minHeight:'300px',display:'flex',alignItems:'center',justifyContent:'center'},children:!selectedOption?/*#__PURE__*/_jsx(Typography,{variant:\"body1\",children:\"Seleziona un'opzione dal menu principale per iniziare.\"}):/*#__PURE__*/_jsxs(Box,{sx:{textAlign:'center'},children:[/*#__PURE__*/_jsxs(Typography,{variant:\"h6\",gutterBottom:true,children:[selectedOption==='creaBobina'&&'Crea Nuova Bobina',selectedOption==='modificaBobina'&&'Modifica Bobina',selectedOption==='eliminaBobina'&&'Elimina Bobina',selectedOption==='visualizzaStorico'&&'Visualizza Storico Utilizzo']}),/*#__PURE__*/_jsx(CircularProgress,{sx:{mt:2}})]})}):null,renderDialog(),/*#__PURE__*/_jsx(ConfigurazioneDialog,{open:openConfigDialog,onClose:()=>setOpenConfigDialog(false),onConfirm:handleConfigConfirm}),/*#__PURE__*/_jsx(QuickAddCablesDialog,{open:showQuickAddDialog,onClose:()=>{setShowQuickAddDialog(false);// Ricarica le bobine per riflettere i cambiamenti\nloadBobine();},bobina:selectedBobina,cantiereId:cantiereId,onSuccess:onSuccess,onError:onError})]});};export default ParcoCavi;", "map": {"version": 3, "names": ["React", "useState", "useEffect", "Box", "Typography", "<PERSON><PERSON>", "Paper", "Grid", "Card", "<PERSON><PERSON><PERSON><PERSON>", "CardActions", "Dialog", "DialogTitle", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "DialogActions", "TextField", "FormControl", "InputLabel", "Select", "MenuItem", "List", "ListItem", "ListItemText", "ListItemIcon", "ListItemButton", "Divider", "<PERSON><PERSON>", "CircularProgress", "FormHelperText", "IconButton", "Table", "TableBody", "TableCell", "TableContainer", "TableHead", "TableRow", "Add", "AddIcon", "Edit", "EditIcon", "Delete", "DeleteIcon", "History", "HistoryIcon", "Save", "SaveIcon", "ViewList", "ViewListIcon", "Warning", "WarningIcon", "parcoCaviService", "ConfigurazioneDialog", "BobineFilterableTable", "QuickAddCablesDialog", "validateBobinaData", "validateBob<PERSON>F<PERSON>", "validateBobinaId", "isEmpty", "jsx", "_jsx", "jsxs", "_jsxs", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "_ref", "cantiereId", "onSuccess", "onError", "initialOption", "loading", "setLoading", "bobine", "set<PERSON>ob<PERSON>", "selectedOption", "setSelectedOption", "openDialog", "setOpenDialog", "dialogType", "setDialogType", "<PERSON><PERSON><PERSON><PERSON>", "setSelectedBobina", "showQuickAddDialog", "setShowQuickAddDialog", "formData", "setFormData", "numero_bobina", "utility", "tipologia", "n_conduttori", "sezione", "metri_totali", "metri_residui", "stato_bobina", "ubicazione_bobina", "fornitore", "n_DDT", "data_DDT", "configurazione", "formErrors", "setFormErrors", "formWarnings", "setForm<PERSON><PERSON>nings", "storico<PERSON><PERSON><PERSON><PERSON>", "setStoricoUtilizzo", "openConfigDialog", "setOpenConfigDialog", "isFirstInsertion", "setIsFirstInsertion", "loadBobine", "data", "getBobine", "error", "errorMessage", "detail", "JSON", "stringify", "message", "console", "loadStoricoUtilizzo", "getStoricoUtilizzo", "initialLoadDone", "useRef", "current", "log", "handleOptionSelect", "checkIfFirstInsertion", "isNaN", "parseInt", "<PERSON><PERSON><PERSON><PERSON>", "response", "isFirstBobinaInsertion", "is_first_insertion", "savedConfig", "localStorage", "getItem", "nextBobinaNumber", "length", "numericBobine", "filter", "b", "test", "map", "maxNumber", "Math", "max", "String", "_error$response$data", "status", "request", "handleConfigConfirm", "config<PERSON><PERSON><PERSON>", "setItem", "defaultFormData", "setTimeout", "option", "handleCloseDialog", "handleBobinaSelect", "bobina", "undefined", "Number", "handleFormChange", "e", "name", "value", "target", "idResult", "valid", "prev", "newErrors", "result", "warning", "newWarnings", "handleSave", "validation", "<PERSON><PERSON><PERSON><PERSON>", "errors", "warnings", "trim", "bobina<PERSON><PERSON>", "parseFloat", "createBobina", "window", "event", "CustomEvent", "dispatchEvent", "bobina<PERSON>d", "id_bobina", "updateBobina", "deleteBobina", "is_last_bobina", "renderBobineCards", "onFilteredDataChange", "filteredData", "onEdit", "onDelete", "onViewHistory", "onQuickAdd", "renderDialog", "open", "onClose", "max<PERSON><PERSON><PERSON>", "fullWidth", "children", "severity", "sx", "mb", "mt", "variant", "fontWeight", "Object", "keys", "style", "margin", "paddingLeft", "values", "index", "container", "spacing", "item", "xs", "sm", "label", "onChange", "disabled", "required", "InputProps", "bgcolor", "helperText", "type", "id", "labelId", "placeholder", "readOnly", "color", "onClick", "startIcon", "size", "button", "primary", "secondary", "display", "flexDirection", "gap", "component", "record", "cavi", "p", "justifyContent", "alignItems", "borderRadius", "my", "minHeight", "textAlign", "gutterBottom", "onConfirm"], "sources": ["C:/Users/<USER>/PycharmProjects/CMS/webapp/frontend/src/components/cavi/ParcoCavi.js"], "sourcesContent": ["import React, { useState, useEffect } from 'react';\nimport {\n  Box,\n  Typography,\n  Button,\n  Paper,\n  Grid,\n  Card,\n  CardContent,\n  CardActions,\n  Dialog,\n  DialogTitle,\n  DialogContent,\n  DialogActions,\n  TextField,\n  FormControl,\n  InputLabel,\n  Select,\n  MenuItem,\n  List,\n  ListItem,\n  ListItemText,\n  ListItemIcon,\n  ListItemButton,\n  Divider,\n  Alert,\n  CircularProgress,\n  FormHelperText,\n  IconButton,\n  Table,\n  TableBody,\n  TableCell,\n  TableContainer,\n  TableHead,\n  TableRow\n} from '@mui/material';\nimport {\n  Add as AddIcon,\n  Edit as EditIcon,\n  Delete as DeleteIcon,\n  History as HistoryIcon,\n  Save as SaveIcon,\n  ViewList as ViewListIcon,\n  Warning as WarningIcon\n} from '@mui/icons-material';\nimport parcoCaviService from '../../services/parcoCaviService';\nimport ConfigurazioneDialog from './ConfigurazioneDialog';\nimport BobineFilterableTable from './BobineFilterableTable';\nimport QuickAddCablesDialog from './QuickAddCablesDialog';\nimport { validateBobinaData, validateBobinaField, validateBobinaId, isEmpty } from '../../utils/bobinaValidationUtils';\n\nconst ParcoCavi = ({ cantiereId, onSuccess, onError, initialOption = null }) => {\n  const [loading, setLoading] = useState(false);\n  const [bobine, setBobine] = useState([]);\n  const [selectedOption, setSelectedOption] = useState(initialOption);\n  const [openDialog, setOpenDialog] = useState(false);\n  const [dialogType, setDialogType] = useState('');\n  const [selectedBobina, setSelectedBobina] = useState(null);\n  const [showQuickAddDialog, setShowQuickAddDialog] = useState(false);\n  const [formData, setFormData] = useState({\n    numero_bobina: '',\n    utility: '',\n    tipologia: '',\n    n_conduttori: '0',  // Imposta sempre a '0' per il campo spare\n    sezione: '',\n    metri_totali: '',\n    metri_residui: '',\n    stato_bobina: 'Disponibile',\n    ubicazione_bobina: '',\n    fornitore: '',\n    n_DDT: '',\n    data_DDT: '',\n    configurazione: ''\n  });\n  const [formErrors, setFormErrors] = useState({});\n  const [formWarnings, setFormWarnings] = useState({});\n  const [storicoUtilizzo, setStoricoUtilizzo] = useState([]);\n  const [openConfigDialog, setOpenConfigDialog] = useState(false);\n  const [isFirstInsertion, setIsFirstInsertion] = useState(false);\n\n  // Carica le bobine disponibili\n  const loadBobine = async () => {\n    try {\n      setLoading(true);\n      const data = await parcoCaviService.getBobine(cantiereId);\n      setBobine(data);\n    } catch (error) {\n      let errorMessage = 'Errore nel caricamento delle bobine';\n      if (error.detail) {\n        errorMessage += ': ' + (typeof error.detail === 'object' ? JSON.stringify(error.detail) : error.detail);\n      } else if (error.message) {\n        errorMessage += ': ' + error.message;\n      }\n      onError(errorMessage);\n      console.error('Errore nel caricamento delle bobine:', error);\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  // Carica lo storico utilizzo bobine\n  const loadStoricoUtilizzo = async () => {\n    try {\n      setLoading(true);\n      const data = await parcoCaviService.getStoricoUtilizzo(cantiereId);\n      setStoricoUtilizzo(data);\n    } catch (error) {\n      let errorMessage = 'Errore nel caricamento dello storico utilizzo';\n      if (error.detail) {\n        errorMessage += ': ' + (typeof error.detail === 'object' ? JSON.stringify(error.detail) : error.detail);\n      } else if (error.message) {\n        errorMessage += ': ' + error.message;\n      }\n      onError(errorMessage);\n      console.error('Errore nel caricamento dello storico utilizzo:', error);\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  // Carica i dati all'avvio del componente e gestisce l'opzione iniziale\n  // Utilizziamo una ref per tenere traccia se l'effetto è già stato eseguito\n  const initialLoadDone = React.useRef(false);\n\n  useEffect(() => {\n    // Esegui solo una volta all'avvio del componente\n    if (!initialLoadDone.current) {\n      console.log('Primo caricamento del componente, initialOption:', initialOption);\n      initialLoadDone.current = true;\n\n      if (initialOption === 'creaBobina') {\n        console.log('Avvio processo creazione bobina');\n        // IMPORTANTE: Per il primo caricamento con creaBobina, forziamo l'apertura del dialog di configurazione\n        console.log('FORZANDO APERTURA DIALOG CONFIGURAZIONE ALL\\'AVVIO');\n        setIsFirstInsertion(true);\n        setOpenConfigDialog(true);\n      } else if (initialOption) {\n        console.log('Eseguendo handleOptionSelect con:', initialOption);\n        handleOptionSelect(initialOption);\n      } else {\n        console.log('Caricando bobine');\n        loadBobine();\n      }\n    }\n  }, []);  // Dipendenze vuote per eseguire solo al mount\n\n  // Verifica se è il primo inserimento di una bobina per un cantiere\n  const checkIfFirstInsertion = async () => {\n    // Variabile per memorizzare la configurazione\n    let configurazione = 's'; // Valore predefinito\n\n    try {\n      // Previene chiamate multiple\n      if (loading) {\n        console.log('Operazione già in corso, uscita');\n        return;\n      }\n\n      // Assicuriamoci che nessun dialog sia aperto\n      setOpenDialog(false);\n      setOpenConfigDialog(false); // Chiudi anche il dialog di configurazione se aperto\n\n      setLoading(true);\n      console.log('Verificando se è il primo inserimento per cantiere ID:', cantiereId);\n\n      // Gestione caso in cui cantiereId non sia valido\n      if (!cantiereId || isNaN(parseInt(cantiereId))) {\n        onError('ID cantiere non valido');\n        console.error('ID cantiere non valido:', cantiereId);\n        setLoading(false);\n        return;\n      }\n\n      // Chiamata API reale\n      let isFirst = false;\n\n      try {\n        console.log('Verificando se è il primo inserimento per cantiere ID:', cantiereId);\n        const response = await parcoCaviService.isFirstBobinaInsertion(cantiereId);\n        isFirst = response.is_first_insertion;\n\n        // Controlla se c'è una configurazione salvata in localStorage\n        const savedConfig = localStorage.getItem(`cantiere_${cantiereId}_config`);\n\n        // Usa la configurazione salvata in localStorage se disponibile, altrimenti usa quella dal server\n        configurazione = savedConfig || response.configurazione || 's';\n\n        console.log('Configurazione da localStorage:', savedConfig);\n        console.log('Configurazione dal server:', response.configurazione);\n        console.log('Configurazione finale utilizzata:', configurazione);\n\n        setIsFirstInsertion(isFirst);\n        console.log('È il primo inserimento di una bobina?', isFirst);\n        console.log('Configurazione esistente:', configurazione);\n      } catch (error) {\n        console.error('Errore durante la verifica del primo inserimento:', error);\n        // In caso di errore, assumiamo che non sia il primo inserimento\n        setIsFirstInsertion(false);\n        onError('Errore durante la verifica del primo inserimento. Procedendo con inserimento standard.');\n      }\n\n      if (isFirst) {\n        // Se è il primo inserimento, mostra il dialog di configurazione\n        console.log('Mostrando il dialog di configurazione');\n        // Assicuriamoci che il dialog di creazione sia chiuso\n        setOpenDialog(false);\n\n        // IMPORTANTE: Forziamo l'apertura del dialog di configurazione\n        console.log('FORZANDO APERTURA DIALOG CONFIGURAZIONE');\n        setOpenConfigDialog(true);\n      } else {\n        // Non è il primo inserimento, procedi con il form normale\n        console.log('Non è il primo inserimento, mostrando il form normale');\n\n        // Ottieni il prossimo numero di bobina solo se la configurazione è automatica\n        let nextBobinaNumber = '1';\n        if (configurazione === 's') {\n          try {\n            // Ottieni l'ultimo numero di bobina dal backend\n            const bobine = await parcoCaviService.getBobine(cantiereId);\n            if (bobine && bobine.length > 0) {\n              // Filtra solo le bobine con numero_bobina numerico\n              const numericBobine = bobine.filter(b => b.numero_bobina && /^\\d+$/.test(b.numero_bobina));\n\n              // Log per debug\n              console.log('Bobine totali:', bobine.length);\n              console.log('Bobine con numero numerico:', numericBobine.length);\n              console.log('Bobine numeriche:', numericBobine.map(b => b.numero_bobina));\n\n              if (numericBobine.length > 0) {\n                // Trova il numero massimo tra le bobine esistenti\n                const maxNumber = Math.max(...numericBobine.map(b => parseInt(b.numero_bobina, 10)));\n                console.log('Numero massimo trovato:', maxNumber);\n                nextBobinaNumber = String(maxNumber + 1);\n              }\n            }\n            console.log('Prossimo numero bobina:', nextBobinaNumber);\n          } catch (error) {\n            console.error('Errore nel recupero del prossimo numero bobina:', error);\n            // In caso di errore, usa 1 come default\n            nextBobinaNumber = '1';\n          }\n        }\n\n        setDialogType('creaBobina');\n        setFormData({\n          // In modalità automatica, imposta il numero progressivo\n          // In modalità manuale, lascia vuoto per far inserire all'utente\n          numero_bobina: configurazione === 's' ? nextBobinaNumber : '',\n          utility: '',\n          tipologia: '',\n          n_conduttori: '0',  // Imposta sempre a '0' per il campo spare\n          sezione: '',\n          metri_totali: '',\n          metri_residui: '',\n          stato_bobina: 'Disponibile',\n          ubicazione_bobina: '',\n          fornitore: '',\n          n_DDT: '',\n          data_DDT: '',\n          configurazione: configurazione  // Usa la configurazione esistente\n        });\n        setOpenDialog(true);\n      }\n    } catch (error) {\n      // Gestione dettagliata dell'errore\n      let errorMessage = 'Errore nel controllo dell\\'inserimento della prima bobina';\n\n      if (error.response) {\n        // Errore di risposta dal server\n        errorMessage += `: ${error.response.status} - ${error.response.data?.detail || 'Errore server'}`;\n        console.error('Dettagli errore API:', error.response);\n      } else if (error.request) {\n        // Errore di rete (nessuna risposta ricevuta)\n        errorMessage += ': Errore di connessione al server';\n      } else {\n        // Errore generico\n        errorMessage += `: ${error.message || 'Errore sconosciuto'}`;\n      }\n\n      onError(errorMessage);\n      console.error('Errore completo:', error);\n\n      // In caso di errore, mantieni la configurazione esistente o usa il default\n      // Non forzare il reset a 's' per evitare di perdere la configurazione manuale\n      if (!configurazione) {\n        configurazione = 's'; // Fallback al valore di default solo se non è già impostato\n      }\n\n      setDialogType('creaBobina');\n      setFormData({\n        numero_bobina: configurazione === 's' ? '1' : '',\n        utility: '',\n        tipologia: '',\n        n_conduttori: '0',  // Imposta sempre a '0' per il campo spare\n        sezione: '',\n        metri_totali: '',\n        metri_residui: '',\n        stato_bobina: 'Disponibile',\n        ubicazione_bobina: '',\n        fornitore: '',\n        n_DDT: '',\n        data_DDT: '',\n        configurazione: configurazione  // Usa la configurazione esistente o il default\n      });\n      setOpenDialog(true);\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  // Gestisce la conferma della configurazione\n  const handleConfigConfirm = async (configValue) => {\n    console.log('Configurazione selezionata:', configValue);\n    // Salva la configurazione selezionata in localStorage per persistenza\n    localStorage.setItem(`cantiere_${cantiereId}_config`, configValue);\n    setLoading(true);\n\n    try {\n      // Ottieni il prossimo numero di bobina se la configurazione è automatica\n      let nextBobinaNumber = '1';\n      if (configValue === 's') {\n        try {\n          // Ottieni l'ultimo numero di bobina dal backend\n          const bobine = await parcoCaviService.getBobine(cantiereId);\n          if (bobine && bobine.length > 0) {\n            // Filtra solo le bobine con numero_bobina numerico\n            const numericBobine = bobine.filter(b => b.numero_bobina && /^\\d+$/.test(b.numero_bobina));\n\n            // Log per debug\n            console.log('Bobine totali:', bobine.length);\n            console.log('Bobine con numero numerico:', numericBobine.length);\n            console.log('Bobine numeriche:', numericBobine.map(b => b.numero_bobina));\n\n            if (numericBobine.length > 0) {\n              // Trova il numero massimo tra le bobine esistenti\n              const maxNumber = Math.max(...numericBobine.map(b => parseInt(b.numero_bobina, 10)));\n              console.log('Numero massimo trovato:', maxNumber);\n              nextBobinaNumber = String(maxNumber + 1);\n            }\n          }\n          console.log('Prossimo numero bobina:', nextBobinaNumber);\n        } catch (error) {\n          console.error('Errore nel recupero del prossimo numero bobina:', error);\n          // In caso di errore, usa 1 come default\n          nextBobinaNumber = '1';\n        }\n      }\n\n      // Imposta i valori di default per la bobina\n      const defaultFormData = {\n        // In modalità automatica, imposta il numero progressivo\n        // In modalità manuale, lascia vuoto per far inserire all'utente\n        numero_bobina: configValue === 's' ? nextBobinaNumber : '',\n        utility: '',\n        tipologia: '',\n        n_conduttori: '0',  // Imposta sempre a '0' per il campo spare\n        sezione: '',\n        metri_totali: '',\n        metri_residui: '',\n        stato_bobina: 'Disponibile',\n        ubicazione_bobina: '',\n        fornitore: '',\n        n_DDT: '',\n        data_DDT: '',\n        configurazione: configValue // Imposta la configurazione scelta\n      };\n\n      console.log('Impostando i dati del form con configurazione:', configValue, 'numero_bobina:', defaultFormData.numero_bobina);\n\n      // Importante: prima prepara il form, poi chiudi il dialog di configurazione\n      // e solo dopo apri il dialog di creazione\n      setFormData(defaultFormData);\n      setDialogType('creaBobina');\n\n      // Chiudi il dialog di configurazione\n      setOpenConfigDialog(false);\n\n      // Piccolo timeout per assicurarsi che il dialog di configurazione sia chiuso\n      // prima di aprire il dialog di creazione\n      setTimeout(() => {\n        setOpenDialog(true);\n        console.log('Dialog di creazione bobina aperto con numero_bobina:', defaultFormData.numero_bobina);\n      }, 300);\n    } catch (error) {\n      console.error('Errore durante la preparazione del form:', error);\n      onError('Errore durante la preparazione del form: ' + (error.message || 'Errore sconosciuto'));\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  // Gestisce la selezione di un'opzione dal menu\n  const handleOptionSelect = (option) => {\n    setSelectedOption(option);\n\n    if (option === 'visualizzaBobine') {\n      loadBobine();\n    } else if (option === 'creaBobina') {\n      checkIfFirstInsertion();\n    } else if (option === 'modificaBobina') {\n      loadBobine();\n      setDialogType('selezionaBobina');\n      setOpenDialog(true);\n    } else if (option === 'eliminaBobina') {\n      loadBobine();\n      setDialogType('eliminaBobina');\n      setOpenDialog(true);\n    } else if (option === 'visualizzaStorico') {\n      loadStoricoUtilizzo();\n      setDialogType('visualizzaStorico');\n      setOpenDialog(true);\n    }\n  };\n\n  // Gestisce la chiusura del dialog\n  const handleCloseDialog = () => {\n    setOpenDialog(false);\n    setSelectedBobina(null);\n\n    // Recupera la configurazione salvata per questo cantiere\n    const savedConfig = localStorage.getItem(`cantiere_${cantiereId}_config`) || 's';\n\n    setFormData({\n      numero_bobina: '',\n      utility: '',\n      tipologia: '',\n      n_conduttori: '0',  // Imposta sempre a '0' per il campo spare\n      sezione: '',\n      metri_totali: '',\n      metri_residui: '',\n      stato_bobina: 'Disponibile',\n      ubicazione_bobina: '',\n      fornitore: '',\n      n_DDT: '',\n      data_DDT: '',\n      configurazione: savedConfig // Mantieni la configurazione salvata\n    });\n    setFormErrors({});\n    setFormWarnings({});\n  };\n\n  // Gestisce la selezione di una bobina\n  const handleBobinaSelect = (bobina) => {\n    console.log('Bobina selezionata:', bobina);\n    setSelectedBobina(bobina);\n    if (dialogType === 'selezionaBobina') {\n      setDialogType('modificaBobina');\n\n      // Assicurati che i campi numerici siano numeri e che i campi stringa siano stringhe\n      setFormData({\n        numero_bobina: bobina.numero_bobina || '',\n        utility: String(bobina.utility || ''),\n        tipologia: String(bobina.tipologia || ''),\n        n_conduttori: bobina.n_conduttori !== null && bobina.n_conduttori !== undefined ? String(bobina.n_conduttori) : '',\n        sezione: bobina.sezione !== null && bobina.sezione !== undefined ? String(bobina.sezione) : '',\n        metri_totali: bobina.metri_totali !== null && bobina.metri_totali !== undefined ? Number(bobina.metri_totali) : '',\n        metri_residui: bobina.metri_residui !== null && bobina.metri_residui !== undefined ? Number(bobina.metri_residui) : '',\n        stato_bobina: String(bobina.stato_bobina || 'Disponibile'),\n        ubicazione_bobina: String(bobina.ubicazione_bobina || ''),\n        fornitore: String(bobina.fornitore || ''),\n        n_DDT: String(bobina.n_DDT || ''),\n        data_DDT: bobina.data_DDT || '',\n        configurazione: String(bobina.configurazione || 's')\n      });\n\n      console.log('Form data impostati per la modifica:', {\n        numero_bobina: bobina.numero_bobina,\n        utility: bobina.utility,\n        tipologia: bobina.tipologia,\n        n_conduttori: bobina.n_conduttori,\n        sezione: bobina.sezione,\n        metri_totali: bobina.metri_totali,\n        metri_residui: bobina.metri_residui\n      });\n    }\n  };\n\n  // Gestisce il cambio dei valori nel form con validazione\n  const handleFormChange = (e) => {\n    const { name, value } = e.target;\n\n    // Aggiorna il valore nel form\n    setFormData({\n      ...formData,\n      [name]: value\n    });\n\n    // Valida il campo\n    if (dialogType === 'creaBobina' || dialogType === 'modificaBobina') {\n      // Validazione speciale per numero_bobina quando configurazione è 'n'\n      if (name === 'numero_bobina' && formData.configurazione === 'n') {\n        const idResult = validateBobinaId(value);\n        if (!idResult.valid) {\n          setFormErrors(prev => ({\n            ...prev,\n            [name]: idResult.message\n          }));\n        } else {\n          setFormErrors(prev => {\n            const newErrors = { ...prev };\n            delete newErrors[name];\n            return newErrors;\n          });\n        }\n        return;\n      }\n\n      const result = validateBobinaField(name, value);\n\n      // Aggiorna gli errori\n      if (!result.valid) {\n        setFormErrors(prev => ({\n          ...prev,\n          [name]: result.message\n        }));\n      } else {\n        setFormErrors(prev => {\n          const newErrors = { ...prev };\n          delete newErrors[name];\n          return newErrors;\n        });\n\n        // Aggiorna gli avvisi\n        if (result.warning) {\n          setFormWarnings(prev => ({\n            ...prev,\n            [name]: result.message\n          }));\n        } else {\n          setFormWarnings(prev => {\n            const newWarnings = { ...prev };\n            delete newWarnings[name];\n            return newWarnings;\n          });\n        }\n      }\n    }\n  };\n\n  // Gestisce il salvataggio del form\n  const handleSave = async () => {\n    try {\n      // Validazione completa dei dati prima del salvataggio\n      if (dialogType === 'creaBobina' || dialogType === 'modificaBobina') {\n        const validation = validateBobinaData(formData);\n\n        if (!validation.isValid) {\n          setFormErrors(validation.errors);\n          setFormWarnings(validation.warnings);\n          onError('Correggi gli errori nel form prima di salvare');\n          return;\n        }\n\n        // Verifica aggiuntiva per il campo numero_bobina quando la configurazione è manuale\n        if (formData.configurazione === 'n' && (!formData.numero_bobina || formData.numero_bobina.trim() === '')) {\n          setFormErrors({\n            ...formErrors,\n            numero_bobina: 'L\\'ID della bobina è obbligatorio'\n          });\n          onError('L\\'ID della bobina è obbligatorio');\n          return;\n        }\n      }\n\n      setLoading(true);\n      console.log('Salvataggio dati bobina in corso...');\n\n      if (dialogType === 'creaBobina') {\n        // Prepara i dati per la creazione della bobina\n        const bobinaData = {\n          ...formData,\n          // Assicurati che tutti i campi siano nel formato corretto\n          numero_bobina: String(formData.numero_bobina || ''),\n          utility: String(formData.utility || ''),\n          tipologia: String(formData.tipologia || ''),\n          n_conduttori: String(formData.n_conduttori || ''),\n          sezione: String(formData.sezione || ''),\n          metri_totali: parseFloat(formData.metri_totali) || 0,\n          ubicazione_bobina: String(formData.ubicazione_bobina || 'TBD'),\n          fornitore: String(formData.fornitore || 'TBD'),\n          n_DDT: String(formData.n_DDT || 'TBD'),\n          data_DDT: formData.data_DDT || null,\n          // Assicurati che la configurazione sia impostata correttamente\n          configurazione: String(formData.configurazione || 's')\n        };\n\n        console.log('Dati bobina da inviare:', bobinaData);\n\n        try {\n          // Log dei dati che stiamo per inviare\n          console.log('Invio dati bobina al backend:', JSON.stringify(bobinaData, null, 2));\n\n          // Invia i dati al backend\n          await parcoCaviService.createBobina(cantiereId, bobinaData);\n          onSuccess('Bobina creata con successo');\n\n          // Chiudi il dialog\n          handleCloseDialog();\n\n          // Imposta l'opzione selezionata a visualizzaBobine e carica le bobine\n          setSelectedOption('visualizzaBobine');\n          loadBobine();\n\n          // Notifica al componente padre che dovrebbe reindirizzare alla pagina di visualizzazione bobine\n          if (typeof window !== 'undefined') {\n            // Usa un evento personalizzato per comunicare con il componente padre\n            const event = new CustomEvent('redirectToVisualizzaBobine', { detail: { cantiereId } });\n            window.dispatchEvent(event);\n          }\n        } catch (error) {\n          console.error('Errore durante la creazione della bobina:', error);\n\n          // Gestione dettagliata dell'errore\n          let errorMessage = 'Errore durante la creazione della bobina';\n\n          if (error.detail) {\n            errorMessage = typeof error.detail === 'object' ? JSON.stringify(error.detail) : error.detail;\n          } else if (error.message) {\n            errorMessage = error.message;\n          }\n\n          // Log dettagliato dell'errore\n          console.error('Dettagli errore:', errorMessage);\n          console.error('Dati inviati:', JSON.stringify(bobinaData, null, 2));\n\n          onError(errorMessage);\n          setLoading(false);\n          return;\n        }\n      } else if (dialogType === 'modificaBobina') {\n        // Per la modifica, usa l'ID bobina completo o il numero bobina\n        const bobinaId = selectedBobina.id_bobina || selectedBobina.numero_bobina || formData.numero_bobina;\n\n        console.log('Modifica bobina con ID:', bobinaId);\n        console.log('Dati da inviare:', formData);\n\n        try {\n          const response = await parcoCaviService.updateBobina(cantiereId, bobinaId, formData);\n          console.log('Risposta modifica bobina:', response);\n          onSuccess('Bobina modificata con successo');\n        } catch (error) {\n          console.error('Errore durante la modifica della bobina:', error);\n          let errorMessage = 'Errore durante la modifica della bobina';\n          if (error.detail) {\n            errorMessage = typeof error.detail === 'object' ? JSON.stringify(error.detail) : error.detail;\n          } else if (error.message) {\n            errorMessage = error.message;\n          }\n          onError(errorMessage);\n          setLoading(false);\n          return;\n        }\n      } else if (dialogType === 'eliminaBobina') {\n        // Per l'eliminazione, usa l'ID bobina completo\n        const bobinaId = selectedBobina.id_bobina || selectedBobina.numero_bobina;\n        const response = await parcoCaviService.deleteBobina(cantiereId, bobinaId);\n\n        // Verifica se è stata eliminata l'ultima bobina\n        if (response.is_last_bobina) {\n          console.log(`Eliminata l'ultima bobina del cantiere ${cantiereId}. Il parco cavi è ora vuoto.`);\n          onSuccess('Bobina eliminata con successo. Il parco cavi è ora vuoto.');\n          // Non forziamo più l'apertura del dialog di creazione quando l'ultima bobina viene eliminata\n          // L'utente può decidere se inserire una nuova bobina o lasciare il parco cavi vuoto\n        } else {\n          onSuccess('Bobina eliminata con successo');\n        }\n      }\n\n      handleCloseDialog();\n      loadBobine(); // Ricarica le bobine dopo l'operazione\n    } catch (error) {\n      let errorMessage = 'Errore durante l\\'operazione';\n      if (error.detail) {\n        errorMessage += ': ' + (typeof error.detail === 'object' ? JSON.stringify(error.detail) : error.detail);\n      } else if (error.message) {\n        errorMessage += ': ' + error.message;\n      } else {\n        errorMessage += ': Errore sconosciuto';\n      }\n      onError(errorMessage);\n      console.error('Errore durante l\\'operazione:', error);\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  // La visualizzazione delle bobine è ora gestita dal componente BobineFilterableTable\n  const renderBobineCards = () => {\n    return (\n      <BobineFilterableTable\n        bobine={bobine}\n        loading={loading}\n        onFilteredDataChange={(filteredData) => console.log('Bobine filtrate:', filteredData.length)}\n        onEdit={(bobina) => {\n          setSelectedBobina(bobina);\n          setDialogType('modificaBobina');\n          setOpenDialog(true);\n        }}\n        onDelete={(bobina) => {\n          setSelectedBobina(bobina);\n          setDialogType('eliminaBobina');\n          setOpenDialog(true);\n        }}\n        onViewHistory={(bobina) => {\n          setSelectedBobina(bobina);\n          loadStoricoUtilizzo();\n          setDialogType('visualizzaStorico');\n          setOpenDialog(true);\n        }}\n        onQuickAdd={(bobina) => {\n          setSelectedBobina(bobina);\n          setShowQuickAddDialog(true);\n        }}\n      />\n    );\n  };\n\n  // Renderizza il dialog in base al tipo\n  const renderDialog = () => {\n    if (dialogType === 'creaBobina' || dialogType === 'modificaBobina') {\n      return (\n        <Dialog open={openDialog} onClose={handleCloseDialog} maxWidth=\"md\" fullWidth>\n          <DialogTitle>\n            {dialogType === 'creaBobina' ? 'Crea Nuova Bobina' : 'Modifica Bobina'}\n          </DialogTitle>\n          <DialogContent>\n            {dialogType === 'modificaBobina' && (\n              <Alert severity=\"info\" sx={{ mb: 2, mt: 1 }}>\n                <Typography variant=\"subtitle2\" fontWeight=\"bold\">\n                  Condizioni per la modifica:\n                </Typography>\n                <ul>\n                  <li>La bobina deve essere nello stato \"Disponibile\"</li>\n                  <li>La bobina non deve essere associata a nessun cavo</li>\n                  <li>Se modifichi i metri totali, i metri residui verranno aggiornati automaticamente</li>\n                </ul>\n                <Box sx={{ mt: 1 }}>\n                  <Typography variant=\"body2\">\n                    <strong>Stato attuale:</strong> {selectedBobina?.stato_bobina || 'N/A'}\n                  </Typography>\n                </Box>\n              </Alert>\n            )}\n\n            {Object.keys(formWarnings).length > 0 && (\n              <Alert severity=\"warning\" sx={{ mb: 2, mt: 1 }}>\n                <Typography variant=\"subtitle2\">Attenzione:</Typography>\n                <ul style={{ margin: 0, paddingLeft: '20px' }}>\n                  {Object.values(formWarnings).map((warning, index) => (\n                    <li key={index}>{warning}</li>\n                  ))}\n                </ul>\n              </Alert>\n            )}\n\n            <Grid container spacing={2} sx={{ mt: 1 }}>\n              <Grid item xs={12} sm={6}>\n                <TextField\n                  name=\"numero_bobina\"\n                  label=\"ID Bobina\"\n                  fullWidth\n                  variant=\"outlined\"\n                  value={formData.numero_bobina}\n                  onChange={handleFormChange}\n                  disabled={dialogType === 'modificaBobina' || (dialogType === 'creaBobina' && formData.configurazione === 's')}\n                  required\n                  error={!!formErrors.numero_bobina}\n                  InputProps={{\n                    sx: {\n                      bgcolor: formData.configurazione === 's' ? '#f5f5f5' : 'transparent',\n                      fontWeight: 'bold',\n                    }\n                  }}\n                  helperText={\n                    formErrors.numero_bobina ||\n                    (dialogType === 'creaBobina' && formData.configurazione === 's'\n                      ? `ID completo: C${cantiereId}_B${formData.numero_bobina || ''} (generato automaticamente)`\n                      : dialogType === 'creaBobina' && formData.configurazione === 'n'\n                        ? `Inserisci l'ID della bobina (es. A123). ID completo sarà: C${cantiereId}_B{tuo input}`\n                        : '')\n                  }\n                  type={formData.configurazione === 's' ? \"text\" : \"text\"}\n                />\n              </Grid>\n              <Grid item xs={12} sm={6}>\n                <TextField\n                  name=\"utility\"\n                  label=\"Utility\"\n                  fullWidth\n                  variant=\"outlined\"\n                  value={formData.utility}\n                  onChange={handleFormChange}\n                  required\n                  error={!!formErrors.utility}\n                  helperText={formErrors.utility || ''}\n                />\n              </Grid>\n              <Grid item xs={12} sm={6}>\n                <TextField\n                  name=\"tipologia\"\n                  label=\"Tipologia\"\n                  fullWidth\n                  variant=\"outlined\"\n                  value={formData.tipologia}\n                  onChange={handleFormChange}\n                  required\n                  error={!!formErrors.tipologia}\n                  helperText={formErrors.tipologia || ''}\n                />\n              </Grid>\n              {/* n_conduttori field is now a spare field (kept in DB but hidden in UI) */}\n              <Grid item xs={12} sm={6}>\n                <TextField\n                  name=\"sezione\"\n                  label=\"Formazione\"\n                  fullWidth\n                  variant=\"outlined\"\n                  value={formData.sezione}\n                  onChange={handleFormChange}\n                  required\n                  error={!!formErrors.sezione}\n                  helperText={formErrors.sezione || ''}\n                />\n              </Grid>\n              <Grid item xs={12} sm={6}>\n                <TextField\n                  name=\"metri_totali\"\n                  label=\"Metri Totali\"\n                  type=\"number\"\n                  fullWidth\n                  variant=\"outlined\"\n                  value={formData.metri_totali}\n                  onChange={handleFormChange}\n                  required\n                  error={!!formErrors.metri_totali}\n                  helperText={formErrors.metri_totali || ''}\n                />\n              </Grid>\n              {dialogType === 'modificaBobina' && (\n                <Grid item xs={12} sm={6}>\n                  <TextField\n                    name=\"metri_residui\"\n                    label=\"Metri Residui\"\n                    type=\"number\"\n                    fullWidth\n                    variant=\"outlined\"\n                    value={formData.metri_residui}\n                    onChange={handleFormChange}\n                    required\n                    disabled={true}\n                    helperText=\"I metri residui non possono essere modificati direttamente\"\n                  />\n                </Grid>\n              )}\n              <Grid item xs={12} sm={6}>\n                <FormControl fullWidth>\n                  <InputLabel id=\"stato-bobina-label\">Stato Bobina</InputLabel>\n                  <Select\n                    labelId=\"stato-bobina-label\"\n                    name=\"stato_bobina\"\n                    value={formData.stato_bobina}\n                    label=\"Stato Bobina\"\n                    onChange={handleFormChange}\n                    disabled={dialogType === 'creaBobina'}\n                  >\n                    <MenuItem value=\"Disponibile\">Disponibile</MenuItem>\n                    <MenuItem value=\"In uso\">In uso</MenuItem>\n                    <MenuItem value=\"Terminata\">Terminata</MenuItem>\n                    <MenuItem value=\"Danneggiata\">Danneggiata</MenuItem>\n                    <MenuItem value=\"Over\">Over</MenuItem>\n                  </Select>\n                  {dialogType === 'creaBobina' && (\n                    <FormHelperText>Per una nuova bobina, lo stato è sempre \"Disponibile\"</FormHelperText>\n                  )}\n                </FormControl>\n              </Grid>\n              <Grid item xs={12} sm={6}>\n                <TextField\n                  name=\"ubicazione_bobina\"\n                  label=\"Ubicazione Bobina\"\n                  fullWidth\n                  variant=\"outlined\"\n                  value={formData.ubicazione_bobina}\n                  onChange={handleFormChange}\n                  error={!!formErrors.ubicazione_bobina}\n                  helperText={formErrors.ubicazione_bobina || ''}\n                />\n              </Grid>\n              <Grid item xs={12} sm={6}>\n                <TextField\n                  name=\"fornitore\"\n                  label=\"Fornitore\"\n                  fullWidth\n                  variant=\"outlined\"\n                  value={formData.fornitore}\n                  onChange={handleFormChange}\n                  error={!!formErrors.fornitore}\n                  helperText={formErrors.fornitore || ''}\n                />\n              </Grid>\n              <Grid item xs={12} sm={6}>\n                <TextField\n                  name=\"n_DDT\"\n                  label=\"Numero DDT\"\n                  fullWidth\n                  variant=\"outlined\"\n                  value={formData.n_DDT}\n                  onChange={handleFormChange}\n                  error={!!formErrors.n_DDT}\n                  helperText={formErrors.n_DDT || ''}\n                />\n              </Grid>\n              <Grid item xs={12} sm={6}>\n                <TextField\n                  name=\"data_DDT\"\n                  label=\"Data DDT (YYYY-MM-DD)\"\n                  fullWidth\n                  variant=\"outlined\"\n                  value={formData.data_DDT}\n                  onChange={handleFormChange}\n                  placeholder=\"YYYY-MM-DD\"\n                  error={!!formErrors.data_DDT}\n                  helperText={formErrors.data_DDT || ''}\n                />\n              </Grid>\n              <Grid item xs={12} sm={6}>\n                <TextField\n                  name=\"configurazione\"\n                  label=\"Modalità Numerazione\"\n                  fullWidth\n                  variant=\"outlined\"\n                  value={formData.configurazione === 's' ? 'Automatica' : 'Manuale'}\n                  InputProps={{\n                    readOnly: true,\n                    sx: {\n                      bgcolor: '#f5f5f5',\n                    }\n                  }}\n                  helperText={\n                    <Box sx={{ fontWeight: 'medium', color: formData.configurazione === 's' ? 'success.main' : 'info.main' }}>\n                      {formData.configurazione === 's'\n                        ? 'Numerazione progressiva automatica (1, 2, 3, ...)'\n                        : 'Inserimento manuale dell\\'ID bobina (es. A123, SPEC01, ...)'}\n                    </Box>\n                  }\n                />\n              </Grid>\n            </Grid>\n          </DialogContent>\n          <DialogActions>\n            <Button onClick={handleCloseDialog}>Annulla</Button>\n            <Button\n              onClick={handleSave}\n              disabled={loading || Object.keys(formErrors).length > 0}\n              startIcon={loading ? <CircularProgress size={20} /> : <SaveIcon />}\n              variant=\"contained\"\n              color=\"primary\"\n            >\n              Salva\n            </Button>\n          </DialogActions>\n        </Dialog>\n      );\n    } else if (dialogType === 'selezionaBobina') {\n      return (\n        <Dialog open={openDialog} onClose={handleCloseDialog} maxWidth=\"md\" fullWidth>\n          <DialogTitle>Seleziona Bobina da Modificare</DialogTitle>\n          <DialogContent>\n            {loading ? (\n              <CircularProgress />\n            ) : bobine.length === 0 ? (\n              <Alert severity=\"info\">Nessuna bobina disponibile</Alert>\n            ) : (\n              <List>\n                {bobine.map((bobina) => (\n                  <ListItem\n                    button\n                    key={bobina.numero_bobina}\n                    onClick={() => handleBobinaSelect(bobina)}\n                  >\n                    <ListItemText\n                      primary={`Bobina: ${bobina.numero_bobina}`}\n                      secondary={`Tipologia: ${bobina.tipologia || 'N/A'} - Utility: ${bobina.utility || 'N/A'} - Residuo: ${bobina.metri_residui || 'N/A'} m`}\n                    />\n                  </ListItem>\n                ))}\n              </List>\n            )}\n          </DialogContent>\n          <DialogActions>\n            <Button onClick={handleCloseDialog}>Annulla</Button>\n          </DialogActions>\n        </Dialog>\n      );\n    } else if (dialogType === 'eliminaBobina') {\n      return (\n        <Dialog open={openDialog} onClose={handleCloseDialog} maxWidth=\"sm\" fullWidth>\n          <DialogTitle>Elimina Bobina</DialogTitle>\n          <DialogContent>\n            {!selectedBobina ? (\n              loading ? (\n                <CircularProgress />\n              ) : bobine.length === 0 ? (\n                <Alert severity=\"info\">Nessuna bobina disponibile</Alert>\n              ) : (\n                <List>\n                  {bobine.map((bobina) => (\n                    <ListItem\n                      button\n                      key={bobina.numero_bobina}\n                      onClick={() => setSelectedBobina(bobina)}\n                    >\n                      <ListItemText\n                        primary={`Bobina: ${bobina.numero_bobina}`}\n                        secondary={`Tipologia: ${bobina.tipologia || 'N/A'} - Utility: ${bobina.utility || 'N/A'} - Residuo: ${bobina.metri_residui || 'N/A'} m`}\n                      />\n                    </ListItem>\n                  ))}\n                </List>\n              )\n            ) : (\n              <Box>\n                <Alert severity=\"warning\" sx={{ mb: 2 }}>\n                  Sei sicuro di voler eliminare la bobina {selectedBobina.numero_bobina}?\n                </Alert>\n                <Typography variant=\"body1\" sx={{ mb: 2 }}>\n                  Questa operazione non può essere annullata.\n                </Typography>\n                <Alert severity=\"info\" sx={{ mb: 2 }}>\n                  <Typography variant=\"subtitle2\" fontWeight=\"bold\">\n                    Condizioni per l'eliminazione:\n                  </Typography>\n                  <ul>\n                    <li>La bobina deve essere completamente integra (metri residui = metri totali)</li>\n                    <li>La bobina deve essere nello stato \"Disponibile\"</li>\n                    <li>La bobina non deve essere associata a nessun cavo</li>\n                  </ul>\n                </Alert>\n                <Box sx={{ display: 'flex', flexDirection: 'column', gap: 1, mb: 2 }}>\n                  <Typography variant=\"body2\">\n                    <strong>Stato attuale:</strong> {selectedBobina.stato_bobina || 'N/A'}\n                  </Typography>\n                  <Typography variant=\"body2\">\n                    <strong>Metri totali:</strong> {selectedBobina.metri_totali || 'N/A'}\n                  </Typography>\n                  <Typography variant=\"body2\">\n                    <strong>Metri residui:</strong> {selectedBobina.metri_residui || 'N/A'}\n                  </Typography>\n                </Box>\n              </Box>\n            )}\n          </DialogContent>\n          <DialogActions>\n            <Button onClick={handleCloseDialog}>Annulla</Button>\n            {selectedBobina && (\n              <Button\n                onClick={handleSave}\n                disabled={loading}\n                color=\"error\"\n                startIcon={loading ? <CircularProgress size={20} /> : <DeleteIcon />}\n              >\n                Elimina\n              </Button>\n            )}\n          </DialogActions>\n        </Dialog>\n      );\n    } else if (dialogType === 'visualizzaStorico') {\n      return (\n        <Dialog open={openDialog} onClose={handleCloseDialog} maxWidth=\"lg\" fullWidth>\n          <DialogTitle>Storico Utilizzo Bobine</DialogTitle>\n          <DialogContent>\n            {loading ? (\n              <CircularProgress />\n            ) : storicoUtilizzo.length === 0 ? (\n              <Alert severity=\"info\">Nessun dato storico disponibile</Alert>\n            ) : (\n              <TableContainer component={Paper} sx={{ mt: 2 }}>\n                <Table size=\"small\">\n                  <TableHead>\n                    <TableRow>\n                      <TableCell>Bobina</TableCell>\n                      <TableCell>Utility</TableCell>\n                      <TableCell>Tipologia</TableCell>\n                      <TableCell>Formazione</TableCell>\n                      <TableCell>Metri Totali</TableCell>\n                      <TableCell>Metri Residui</TableCell>\n                      <TableCell>Cavi Associati</TableCell>\n                    </TableRow>\n                  </TableHead>\n                  <TableBody>\n                    {storicoUtilizzo.map((record, index) => (\n                      <TableRow key={index}>\n                        <TableCell>{record.numero_bobina}</TableCell>\n                        <TableCell>{record.utility}</TableCell>\n                        <TableCell>{record.tipologia}</TableCell>\n                        <TableCell>{record.sezione}</TableCell>\n                        <TableCell>{record.metri_totali}</TableCell>\n                        <TableCell>{record.metri_residui}</TableCell>\n                        <TableCell>{record.cavi.length}</TableCell>\n                      </TableRow>\n                    ))}\n                  </TableBody>\n                </Table>\n              </TableContainer>\n            )}\n          </DialogContent>\n          <DialogActions>\n            <Button onClick={handleCloseDialog}>Chiudi</Button>\n          </DialogActions>\n        </Dialog>\n      );\n    }\n\n    return null;\n  };\n\n  return (\n    <Box>\n      {selectedOption === 'visualizzaBobine' && !openDialog ? (\n        <Paper sx={{ p: 3 }}>\n          <Box sx={{ display: 'flex', justifyContent: 'flex-end', alignItems: 'center', mb: 2 }}>\n            <Box sx={{ display: 'flex', gap: 2 }}>\n              <Button\n                variant=\"outlined\"\n                color=\"inherit\"\n                startIcon={<HistoryIcon />}\n                onClick={() => {\n                  loadStoricoUtilizzo();\n                  setDialogType('visualizzaStorico');\n                  setOpenDialog(true);\n                }}\n                sx={{\n                  fontWeight: 'medium',\n                  borderRadius: 0\n                }}\n              >\n                Storico Utilizzo\n              </Button>\n              <Button\n                variant=\"contained\"\n                color=\"inherit\"\n                startIcon={<AddIcon />}\n                onClick={() => checkIfFirstInsertion()}\n                sx={{\n                  fontWeight: 'medium',\n                  borderRadius: 0\n                }}\n              >\n                Crea Nuova Bobina\n              </Button>\n            </Box>\n          </Box>\n          {loading ? (\n            <Box sx={{ display: 'flex', justifyContent: 'center', my: 4 }}>\n              <CircularProgress />\n            </Box>\n          ) : (\n            <BobineFilterableTable\n              bobine={bobine}\n              loading={loading}\n              onFilteredDataChange={(filteredData) => console.log('Bobine filtrate:', filteredData.length)}\n              onEdit={(bobina) => {\n                setSelectedBobina(bobina);\n                setDialogType('modificaBobina');\n                setOpenDialog(true);\n              }}\n              onDelete={(bobina) => {\n                setSelectedBobina(bobina);\n                setDialogType('eliminaBobina');\n                setOpenDialog(true);\n              }}\n              onViewHistory={(bobina) => {\n                setSelectedBobina(bobina);\n                loadStoricoUtilizzo();\n                setDialogType('visualizzaStorico');\n                setOpenDialog(true);\n              }}\n              onQuickAdd={(bobina) => {\n                setSelectedBobina(bobina);\n                setShowQuickAddDialog(true);\n              }}\n            />\n          )}\n        </Paper>\n      ) : !openDialog ? (\n        <Paper sx={{ p: 3, minHeight: '300px', display: 'flex', alignItems: 'center', justifyContent: 'center' }}>\n          {!selectedOption ? (\n            <Typography variant=\"body1\">\n              Seleziona un'opzione dal menu principale per iniziare.\n            </Typography>\n          ) : (\n            <Box sx={{ textAlign: 'center' }}>\n              <Typography variant=\"h6\" gutterBottom>\n                {selectedOption === 'creaBobina' && 'Crea Nuova Bobina'}\n                {selectedOption === 'modificaBobina' && 'Modifica Bobina'}\n                {selectedOption === 'eliminaBobina' && 'Elimina Bobina'}\n                {selectedOption === 'visualizzaStorico' && 'Visualizza Storico Utilizzo'}\n              </Typography>\n              <CircularProgress sx={{ mt: 2 }} />\n            </Box>\n          )}\n        </Paper>\n      ) : null}\n\n      {renderDialog()}\n\n      {/* Dialog di configurazione per il primo inserimento */}\n      <ConfigurazioneDialog\n        open={openConfigDialog}\n        onClose={() => setOpenConfigDialog(false)}\n        onConfirm={handleConfigConfirm}\n      />\n\n      {/* Dialog per aggiungere rapidamente cavi a una bobina */}\n      <QuickAddCablesDialog\n        open={showQuickAddDialog}\n        onClose={() => {\n          setShowQuickAddDialog(false);\n          // Ricarica le bobine per riflettere i cambiamenti\n          loadBobine();\n        }}\n        bobina={selectedBobina}\n        cantiereId={cantiereId}\n        onSuccess={onSuccess}\n        onError={onError}\n      />\n    </Box>\n  );\n};\n\nexport default ParcoCavi;\n"], "mappings": "AAAA,MAAO,CAAAA,KAAK,EAAIC,QAAQ,CAAEC,SAAS,KAAQ,OAAO,CAClD,OACEC,GAAG,CACHC,UAAU,CACVC,MAAM,CACNC,KAAK,CACLC,IAAI,CACJC,IAAI,CACJC,WAAW,CACXC,WAAW,CACXC,MAAM,CACNC,WAAW,CACXC,aAAa,CACbC,aAAa,CACbC,SAAS,CACTC,WAAW,CACXC,UAAU,CACVC,MAAM,CACNC,QAAQ,CACRC,IAAI,CACJC,QAAQ,CACRC,YAAY,CACZC,YAAY,CACZC,cAAc,CACdC,OAAO,CACPC,KAAK,CACLC,gBAAgB,CAChBC,cAAc,CACdC,UAAU,CACVC,KAAK,CACLC,SAAS,CACTC,SAAS,CACTC,cAAc,CACdC,SAAS,CACTC,QAAQ,KACH,eAAe,CACtB,OACEC,GAAG,GAAI,CAAAC,OAAO,CACdC,IAAI,GAAI,CAAAC,QAAQ,CAChBC,MAAM,GAAI,CAAAC,UAAU,CACpBC,OAAO,GAAI,CAAAC,WAAW,CACtBC,IAAI,GAAI,CAAAC,QAAQ,CAChBC,QAAQ,GAAI,CAAAC,YAAY,CACxBC,OAAO,GAAI,CAAAC,WAAW,KACjB,qBAAqB,CAC5B,MAAO,CAAAC,gBAAgB,KAAM,iCAAiC,CAC9D,MAAO,CAAAC,oBAAoB,KAAM,wBAAwB,CACzD,MAAO,CAAAC,qBAAqB,KAAM,yBAAyB,CAC3D,MAAO,CAAAC,oBAAoB,KAAM,wBAAwB,CACzD,OAASC,kBAAkB,CAAEC,mBAAmB,CAAEC,gBAAgB,CAAEC,OAAO,KAAQ,mCAAmC,CAAC,OAAAC,GAAA,IAAAC,IAAA,CAAAC,IAAA,IAAAC,KAAA,yBAEvH,KAAM,CAAAC,SAAS,CAAGC,IAAA,EAA8D,IAA7D,CAAEC,UAAU,CAAEC,SAAS,CAAEC,OAAO,CAAEC,aAAa,CAAG,IAAK,CAAC,CAAAJ,IAAA,CACzE,KAAM,CAACK,OAAO,CAAEC,UAAU,CAAC,CAAGpE,QAAQ,CAAC,KAAK,CAAC,CAC7C,KAAM,CAACqE,MAAM,CAAEC,SAAS,CAAC,CAAGtE,QAAQ,CAAC,EAAE,CAAC,CACxC,KAAM,CAACuE,cAAc,CAAEC,iBAAiB,CAAC,CAAGxE,QAAQ,CAACkE,aAAa,CAAC,CACnE,KAAM,CAACO,UAAU,CAAEC,aAAa,CAAC,CAAG1E,QAAQ,CAAC,KAAK,CAAC,CACnD,KAAM,CAAC2E,UAAU,CAAEC,aAAa,CAAC,CAAG5E,QAAQ,CAAC,EAAE,CAAC,CAChD,KAAM,CAAC6E,cAAc,CAAEC,iBAAiB,CAAC,CAAG9E,QAAQ,CAAC,IAAI,CAAC,CAC1D,KAAM,CAAC+E,kBAAkB,CAAEC,qBAAqB,CAAC,CAAGhF,QAAQ,CAAC,KAAK,CAAC,CACnE,KAAM,CAACiF,QAAQ,CAAEC,WAAW,CAAC,CAAGlF,QAAQ,CAAC,CACvCmF,aAAa,CAAE,EAAE,CACjBC,OAAO,CAAE,EAAE,CACXC,SAAS,CAAE,EAAE,CACbC,YAAY,CAAE,GAAG,CAAG;AACpBC,OAAO,CAAE,EAAE,CACXC,YAAY,CAAE,EAAE,CAChBC,aAAa,CAAE,EAAE,CACjBC,YAAY,CAAE,aAAa,CAC3BC,iBAAiB,CAAE,EAAE,CACrBC,SAAS,CAAE,EAAE,CACbC,KAAK,CAAE,EAAE,CACTC,QAAQ,CAAE,EAAE,CACZC,cAAc,CAAE,EAClB,CAAC,CAAC,CACF,KAAM,CAACC,UAAU,CAAEC,aAAa,CAAC,CAAGjG,QAAQ,CAAC,CAAC,CAAC,CAAC,CAChD,KAAM,CAACkG,YAAY,CAAEC,eAAe,CAAC,CAAGnG,QAAQ,CAAC,CAAC,CAAC,CAAC,CACpD,KAAM,CAACoG,eAAe,CAAEC,kBAAkB,CAAC,CAAGrG,QAAQ,CAAC,EAAE,CAAC,CAC1D,KAAM,CAACsG,gBAAgB,CAAEC,mBAAmB,CAAC,CAAGvG,QAAQ,CAAC,KAAK,CAAC,CAC/D,KAAM,CAACwG,gBAAgB,CAAEC,mBAAmB,CAAC,CAAGzG,QAAQ,CAAC,KAAK,CAAC,CAE/D;AACA,KAAM,CAAA0G,UAAU,CAAG,KAAAA,CAAA,GAAY,CAC7B,GAAI,CACFtC,UAAU,CAAC,IAAI,CAAC,CAChB,KAAM,CAAAuC,IAAI,CAAG,KAAM,CAAA1D,gBAAgB,CAAC2D,SAAS,CAAC7C,UAAU,CAAC,CACzDO,SAAS,CAACqC,IAAI,CAAC,CACjB,CAAE,MAAOE,KAAK,CAAE,CACd,GAAI,CAAAC,YAAY,CAAG,qCAAqC,CACxD,GAAID,KAAK,CAACE,MAAM,CAAE,CAChBD,YAAY,EAAI,IAAI,EAAI,MAAO,CAAAD,KAAK,CAACE,MAAM,GAAK,QAAQ,CAAGC,IAAI,CAACC,SAAS,CAACJ,KAAK,CAACE,MAAM,CAAC,CAAGF,KAAK,CAACE,MAAM,CAAC,CACzG,CAAC,IAAM,IAAIF,KAAK,CAACK,OAAO,CAAE,CACxBJ,YAAY,EAAI,IAAI,CAAGD,KAAK,CAACK,OAAO,CACtC,CACAjD,OAAO,CAAC6C,YAAY,CAAC,CACrBK,OAAO,CAACN,KAAK,CAAC,sCAAsC,CAAEA,KAAK,CAAC,CAC9D,CAAC,OAAS,CACRzC,UAAU,CAAC,KAAK,CAAC,CACnB,CACF,CAAC,CAED;AACA,KAAM,CAAAgD,mBAAmB,CAAG,KAAAA,CAAA,GAAY,CACtC,GAAI,CACFhD,UAAU,CAAC,IAAI,CAAC,CAChB,KAAM,CAAAuC,IAAI,CAAG,KAAM,CAAA1D,gBAAgB,CAACoE,kBAAkB,CAACtD,UAAU,CAAC,CAClEsC,kBAAkB,CAACM,IAAI,CAAC,CAC1B,CAAE,MAAOE,KAAK,CAAE,CACd,GAAI,CAAAC,YAAY,CAAG,+CAA+C,CAClE,GAAID,KAAK,CAACE,MAAM,CAAE,CAChBD,YAAY,EAAI,IAAI,EAAI,MAAO,CAAAD,KAAK,CAACE,MAAM,GAAK,QAAQ,CAAGC,IAAI,CAACC,SAAS,CAACJ,KAAK,CAACE,MAAM,CAAC,CAAGF,KAAK,CAACE,MAAM,CAAC,CACzG,CAAC,IAAM,IAAIF,KAAK,CAACK,OAAO,CAAE,CACxBJ,YAAY,EAAI,IAAI,CAAGD,KAAK,CAACK,OAAO,CACtC,CACAjD,OAAO,CAAC6C,YAAY,CAAC,CACrBK,OAAO,CAACN,KAAK,CAAC,gDAAgD,CAAEA,KAAK,CAAC,CACxE,CAAC,OAAS,CACRzC,UAAU,CAAC,KAAK,CAAC,CACnB,CACF,CAAC,CAED;AACA;AACA,KAAM,CAAAkD,eAAe,CAAGvH,KAAK,CAACwH,MAAM,CAAC,KAAK,CAAC,CAE3CtH,SAAS,CAAC,IAAM,CACd;AACA,GAAI,CAACqH,eAAe,CAACE,OAAO,CAAE,CAC5BL,OAAO,CAACM,GAAG,CAAC,kDAAkD,CAAEvD,aAAa,CAAC,CAC9EoD,eAAe,CAACE,OAAO,CAAG,IAAI,CAE9B,GAAItD,aAAa,GAAK,YAAY,CAAE,CAClCiD,OAAO,CAACM,GAAG,CAAC,iCAAiC,CAAC,CAC9C;AACAN,OAAO,CAACM,GAAG,CAAC,oDAAoD,CAAC,CACjEhB,mBAAmB,CAAC,IAAI,CAAC,CACzBF,mBAAmB,CAAC,IAAI,CAAC,CAC3B,CAAC,IAAM,IAAIrC,aAAa,CAAE,CACxBiD,OAAO,CAACM,GAAG,CAAC,mCAAmC,CAAEvD,aAAa,CAAC,CAC/DwD,kBAAkB,CAACxD,aAAa,CAAC,CACnC,CAAC,IAAM,CACLiD,OAAO,CAACM,GAAG,CAAC,kBAAkB,CAAC,CAC/Bf,UAAU,CAAC,CAAC,CACd,CACF,CACF,CAAC,CAAE,EAAE,CAAC,CAAG;AAET;AACA,KAAM,CAAAiB,qBAAqB,CAAG,KAAAA,CAAA,GAAY,CACxC;AACA,GAAI,CAAA5B,cAAc,CAAG,GAAG,CAAE;AAE1B,GAAI,CACF;AACA,GAAI5B,OAAO,CAAE,CACXgD,OAAO,CAACM,GAAG,CAAC,iCAAiC,CAAC,CAC9C,OACF,CAEA;AACA/C,aAAa,CAAC,KAAK,CAAC,CACpB6B,mBAAmB,CAAC,KAAK,CAAC,CAAE;AAE5BnC,UAAU,CAAC,IAAI,CAAC,CAChB+C,OAAO,CAACM,GAAG,CAAC,wDAAwD,CAAE1D,UAAU,CAAC,CAEjF;AACA,GAAI,CAACA,UAAU,EAAI6D,KAAK,CAACC,QAAQ,CAAC9D,UAAU,CAAC,CAAC,CAAE,CAC9CE,OAAO,CAAC,wBAAwB,CAAC,CACjCkD,OAAO,CAACN,KAAK,CAAC,yBAAyB,CAAE9C,UAAU,CAAC,CACpDK,UAAU,CAAC,KAAK,CAAC,CACjB,OACF,CAEA;AACA,GAAI,CAAA0D,OAAO,CAAG,KAAK,CAEnB,GAAI,CACFX,OAAO,CAACM,GAAG,CAAC,wDAAwD,CAAE1D,UAAU,CAAC,CACjF,KAAM,CAAAgE,QAAQ,CAAG,KAAM,CAAA9E,gBAAgB,CAAC+E,sBAAsB,CAACjE,UAAU,CAAC,CAC1E+D,OAAO,CAAGC,QAAQ,CAACE,kBAAkB,CAErC;AACA,KAAM,CAAAC,WAAW,CAAGC,YAAY,CAACC,OAAO,CAAC,YAAYrE,UAAU,SAAS,CAAC,CAEzE;AACAgC,cAAc,CAAGmC,WAAW,EAAIH,QAAQ,CAAChC,cAAc,EAAI,GAAG,CAE9DoB,OAAO,CAACM,GAAG,CAAC,iCAAiC,CAAES,WAAW,CAAC,CAC3Df,OAAO,CAACM,GAAG,CAAC,4BAA4B,CAAEM,QAAQ,CAAChC,cAAc,CAAC,CAClEoB,OAAO,CAACM,GAAG,CAAC,mCAAmC,CAAE1B,cAAc,CAAC,CAEhEU,mBAAmB,CAACqB,OAAO,CAAC,CAC5BX,OAAO,CAACM,GAAG,CAAC,uCAAuC,CAAEK,OAAO,CAAC,CAC7DX,OAAO,CAACM,GAAG,CAAC,2BAA2B,CAAE1B,cAAc,CAAC,CAC1D,CAAE,MAAOc,KAAK,CAAE,CACdM,OAAO,CAACN,KAAK,CAAC,mDAAmD,CAAEA,KAAK,CAAC,CACzE;AACAJ,mBAAmB,CAAC,KAAK,CAAC,CAC1BxC,OAAO,CAAC,wFAAwF,CAAC,CACnG,CAEA,GAAI6D,OAAO,CAAE,CACX;AACAX,OAAO,CAACM,GAAG,CAAC,uCAAuC,CAAC,CACpD;AACA/C,aAAa,CAAC,KAAK,CAAC,CAEpB;AACAyC,OAAO,CAACM,GAAG,CAAC,yCAAyC,CAAC,CACtDlB,mBAAmB,CAAC,IAAI,CAAC,CAC3B,CAAC,IAAM,CACL;AACAY,OAAO,CAACM,GAAG,CAAC,uDAAuD,CAAC,CAEpE;AACA,GAAI,CAAAY,gBAAgB,CAAG,GAAG,CAC1B,GAAItC,cAAc,GAAK,GAAG,CAAE,CAC1B,GAAI,CACF;AACA,KAAM,CAAA1B,MAAM,CAAG,KAAM,CAAApB,gBAAgB,CAAC2D,SAAS,CAAC7C,UAAU,CAAC,CAC3D,GAAIM,MAAM,EAAIA,MAAM,CAACiE,MAAM,CAAG,CAAC,CAAE,CAC/B;AACA,KAAM,CAAAC,aAAa,CAAGlE,MAAM,CAACmE,MAAM,CAACC,CAAC,EAAIA,CAAC,CAACtD,aAAa,EAAI,OAAO,CAACuD,IAAI,CAACD,CAAC,CAACtD,aAAa,CAAC,CAAC,CAE1F;AACAgC,OAAO,CAACM,GAAG,CAAC,gBAAgB,CAAEpD,MAAM,CAACiE,MAAM,CAAC,CAC5CnB,OAAO,CAACM,GAAG,CAAC,6BAA6B,CAAEc,aAAa,CAACD,MAAM,CAAC,CAChEnB,OAAO,CAACM,GAAG,CAAC,mBAAmB,CAAEc,aAAa,CAACI,GAAG,CAACF,CAAC,EAAIA,CAAC,CAACtD,aAAa,CAAC,CAAC,CAEzE,GAAIoD,aAAa,CAACD,MAAM,CAAG,CAAC,CAAE,CAC5B;AACA,KAAM,CAAAM,SAAS,CAAGC,IAAI,CAACC,GAAG,CAAC,GAAGP,aAAa,CAACI,GAAG,CAACF,CAAC,EAAIZ,QAAQ,CAACY,CAAC,CAACtD,aAAa,CAAE,EAAE,CAAC,CAAC,CAAC,CACpFgC,OAAO,CAACM,GAAG,CAAC,yBAAyB,CAAEmB,SAAS,CAAC,CACjDP,gBAAgB,CAAGU,MAAM,CAACH,SAAS,CAAG,CAAC,CAAC,CAC1C,CACF,CACAzB,OAAO,CAACM,GAAG,CAAC,yBAAyB,CAAEY,gBAAgB,CAAC,CAC1D,CAAE,MAAOxB,KAAK,CAAE,CACdM,OAAO,CAACN,KAAK,CAAC,iDAAiD,CAAEA,KAAK,CAAC,CACvE;AACAwB,gBAAgB,CAAG,GAAG,CACxB,CACF,CAEAzD,aAAa,CAAC,YAAY,CAAC,CAC3BM,WAAW,CAAC,CACV;AACA;AACAC,aAAa,CAAEY,cAAc,GAAK,GAAG,CAAGsC,gBAAgB,CAAG,EAAE,CAC7DjD,OAAO,CAAE,EAAE,CACXC,SAAS,CAAE,EAAE,CACbC,YAAY,CAAE,GAAG,CAAG;AACpBC,OAAO,CAAE,EAAE,CACXC,YAAY,CAAE,EAAE,CAChBC,aAAa,CAAE,EAAE,CACjBC,YAAY,CAAE,aAAa,CAC3BC,iBAAiB,CAAE,EAAE,CACrBC,SAAS,CAAE,EAAE,CACbC,KAAK,CAAE,EAAE,CACTC,QAAQ,CAAE,EAAE,CACZC,cAAc,CAAEA,cAAgB;AAClC,CAAC,CAAC,CACFrB,aAAa,CAAC,IAAI,CAAC,CACrB,CACF,CAAE,MAAOmC,KAAK,CAAE,CACd;AACA,GAAI,CAAAC,YAAY,CAAG,2DAA2D,CAE9E,GAAID,KAAK,CAACkB,QAAQ,CAAE,KAAAiB,oBAAA,CAClB;AACAlC,YAAY,EAAI,KAAKD,KAAK,CAACkB,QAAQ,CAACkB,MAAM,MAAM,EAAAD,oBAAA,CAAAnC,KAAK,CAACkB,QAAQ,CAACpB,IAAI,UAAAqC,oBAAA,iBAAnBA,oBAAA,CAAqBjC,MAAM,GAAI,eAAe,EAAE,CAChGI,OAAO,CAACN,KAAK,CAAC,sBAAsB,CAAEA,KAAK,CAACkB,QAAQ,CAAC,CACvD,CAAC,IAAM,IAAIlB,KAAK,CAACqC,OAAO,CAAE,CACxB;AACApC,YAAY,EAAI,mCAAmC,CACrD,CAAC,IAAM,CACL;AACAA,YAAY,EAAI,KAAKD,KAAK,CAACK,OAAO,EAAI,oBAAoB,EAAE,CAC9D,CAEAjD,OAAO,CAAC6C,YAAY,CAAC,CACrBK,OAAO,CAACN,KAAK,CAAC,kBAAkB,CAAEA,KAAK,CAAC,CAExC;AACA;AACA,GAAI,CAACd,cAAc,CAAE,CACnBA,cAAc,CAAG,GAAG,CAAE;AACxB,CAEAnB,aAAa,CAAC,YAAY,CAAC,CAC3BM,WAAW,CAAC,CACVC,aAAa,CAAEY,cAAc,GAAK,GAAG,CAAG,GAAG,CAAG,EAAE,CAChDX,OAAO,CAAE,EAAE,CACXC,SAAS,CAAE,EAAE,CACbC,YAAY,CAAE,GAAG,CAAG;AACpBC,OAAO,CAAE,EAAE,CACXC,YAAY,CAAE,EAAE,CAChBC,aAAa,CAAE,EAAE,CACjBC,YAAY,CAAE,aAAa,CAC3BC,iBAAiB,CAAE,EAAE,CACrBC,SAAS,CAAE,EAAE,CACbC,KAAK,CAAE,EAAE,CACTC,QAAQ,CAAE,EAAE,CACZC,cAAc,CAAEA,cAAgB;AAClC,CAAC,CAAC,CACFrB,aAAa,CAAC,IAAI,CAAC,CACrB,CAAC,OAAS,CACRN,UAAU,CAAC,KAAK,CAAC,CACnB,CACF,CAAC,CAED;AACA,KAAM,CAAA+E,mBAAmB,CAAG,KAAO,CAAAC,WAAW,EAAK,CACjDjC,OAAO,CAACM,GAAG,CAAC,6BAA6B,CAAE2B,WAAW,CAAC,CACvD;AACAjB,YAAY,CAACkB,OAAO,CAAC,YAAYtF,UAAU,SAAS,CAAEqF,WAAW,CAAC,CAClEhF,UAAU,CAAC,IAAI,CAAC,CAEhB,GAAI,CACF;AACA,GAAI,CAAAiE,gBAAgB,CAAG,GAAG,CAC1B,GAAIe,WAAW,GAAK,GAAG,CAAE,CACvB,GAAI,CACF;AACA,KAAM,CAAA/E,MAAM,CAAG,KAAM,CAAApB,gBAAgB,CAAC2D,SAAS,CAAC7C,UAAU,CAAC,CAC3D,GAAIM,MAAM,EAAIA,MAAM,CAACiE,MAAM,CAAG,CAAC,CAAE,CAC/B;AACA,KAAM,CAAAC,aAAa,CAAGlE,MAAM,CAACmE,MAAM,CAACC,CAAC,EAAIA,CAAC,CAACtD,aAAa,EAAI,OAAO,CAACuD,IAAI,CAACD,CAAC,CAACtD,aAAa,CAAC,CAAC,CAE1F;AACAgC,OAAO,CAACM,GAAG,CAAC,gBAAgB,CAAEpD,MAAM,CAACiE,MAAM,CAAC,CAC5CnB,OAAO,CAACM,GAAG,CAAC,6BAA6B,CAAEc,aAAa,CAACD,MAAM,CAAC,CAChEnB,OAAO,CAACM,GAAG,CAAC,mBAAmB,CAAEc,aAAa,CAACI,GAAG,CAACF,CAAC,EAAIA,CAAC,CAACtD,aAAa,CAAC,CAAC,CAEzE,GAAIoD,aAAa,CAACD,MAAM,CAAG,CAAC,CAAE,CAC5B;AACA,KAAM,CAAAM,SAAS,CAAGC,IAAI,CAACC,GAAG,CAAC,GAAGP,aAAa,CAACI,GAAG,CAACF,CAAC,EAAIZ,QAAQ,CAACY,CAAC,CAACtD,aAAa,CAAE,EAAE,CAAC,CAAC,CAAC,CACpFgC,OAAO,CAACM,GAAG,CAAC,yBAAyB,CAAEmB,SAAS,CAAC,CACjDP,gBAAgB,CAAGU,MAAM,CAACH,SAAS,CAAG,CAAC,CAAC,CAC1C,CACF,CACAzB,OAAO,CAACM,GAAG,CAAC,yBAAyB,CAAEY,gBAAgB,CAAC,CAC1D,CAAE,MAAOxB,KAAK,CAAE,CACdM,OAAO,CAACN,KAAK,CAAC,iDAAiD,CAAEA,KAAK,CAAC,CACvE;AACAwB,gBAAgB,CAAG,GAAG,CACxB,CACF,CAEA;AACA,KAAM,CAAAiB,eAAe,CAAG,CACtB;AACA;AACAnE,aAAa,CAAEiE,WAAW,GAAK,GAAG,CAAGf,gBAAgB,CAAG,EAAE,CAC1DjD,OAAO,CAAE,EAAE,CACXC,SAAS,CAAE,EAAE,CACbC,YAAY,CAAE,GAAG,CAAG;AACpBC,OAAO,CAAE,EAAE,CACXC,YAAY,CAAE,EAAE,CAChBC,aAAa,CAAE,EAAE,CACjBC,YAAY,CAAE,aAAa,CAC3BC,iBAAiB,CAAE,EAAE,CACrBC,SAAS,CAAE,EAAE,CACbC,KAAK,CAAE,EAAE,CACTC,QAAQ,CAAE,EAAE,CACZC,cAAc,CAAEqD,WAAY;AAC9B,CAAC,CAEDjC,OAAO,CAACM,GAAG,CAAC,gDAAgD,CAAE2B,WAAW,CAAE,gBAAgB,CAAEE,eAAe,CAACnE,aAAa,CAAC,CAE3H;AACA;AACAD,WAAW,CAACoE,eAAe,CAAC,CAC5B1E,aAAa,CAAC,YAAY,CAAC,CAE3B;AACA2B,mBAAmB,CAAC,KAAK,CAAC,CAE1B;AACA;AACAgD,UAAU,CAAC,IAAM,CACf7E,aAAa,CAAC,IAAI,CAAC,CACnByC,OAAO,CAACM,GAAG,CAAC,sDAAsD,CAAE6B,eAAe,CAACnE,aAAa,CAAC,CACpG,CAAC,CAAE,GAAG,CAAC,CACT,CAAE,MAAO0B,KAAK,CAAE,CACdM,OAAO,CAACN,KAAK,CAAC,0CAA0C,CAAEA,KAAK,CAAC,CAChE5C,OAAO,CAAC,2CAA2C,EAAI4C,KAAK,CAACK,OAAO,EAAI,oBAAoB,CAAC,CAAC,CAChG,CAAC,OAAS,CACR9C,UAAU,CAAC,KAAK,CAAC,CACnB,CACF,CAAC,CAED;AACA,KAAM,CAAAsD,kBAAkB,CAAI8B,MAAM,EAAK,CACrChF,iBAAiB,CAACgF,MAAM,CAAC,CAEzB,GAAIA,MAAM,GAAK,kBAAkB,CAAE,CACjC9C,UAAU,CAAC,CAAC,CACd,CAAC,IAAM,IAAI8C,MAAM,GAAK,YAAY,CAAE,CAClC7B,qBAAqB,CAAC,CAAC,CACzB,CAAC,IAAM,IAAI6B,MAAM,GAAK,gBAAgB,CAAE,CACtC9C,UAAU,CAAC,CAAC,CACZ9B,aAAa,CAAC,iBAAiB,CAAC,CAChCF,aAAa,CAAC,IAAI,CAAC,CACrB,CAAC,IAAM,IAAI8E,MAAM,GAAK,eAAe,CAAE,CACrC9C,UAAU,CAAC,CAAC,CACZ9B,aAAa,CAAC,eAAe,CAAC,CAC9BF,aAAa,CAAC,IAAI,CAAC,CACrB,CAAC,IAAM,IAAI8E,MAAM,GAAK,mBAAmB,CAAE,CACzCpC,mBAAmB,CAAC,CAAC,CACrBxC,aAAa,CAAC,mBAAmB,CAAC,CAClCF,aAAa,CAAC,IAAI,CAAC,CACrB,CACF,CAAC,CAED;AACA,KAAM,CAAA+E,iBAAiB,CAAGA,CAAA,GAAM,CAC9B/E,aAAa,CAAC,KAAK,CAAC,CACpBI,iBAAiB,CAAC,IAAI,CAAC,CAEvB;AACA,KAAM,CAAAoD,WAAW,CAAGC,YAAY,CAACC,OAAO,CAAC,YAAYrE,UAAU,SAAS,CAAC,EAAI,GAAG,CAEhFmB,WAAW,CAAC,CACVC,aAAa,CAAE,EAAE,CACjBC,OAAO,CAAE,EAAE,CACXC,SAAS,CAAE,EAAE,CACbC,YAAY,CAAE,GAAG,CAAG;AACpBC,OAAO,CAAE,EAAE,CACXC,YAAY,CAAE,EAAE,CAChBC,aAAa,CAAE,EAAE,CACjBC,YAAY,CAAE,aAAa,CAC3BC,iBAAiB,CAAE,EAAE,CACrBC,SAAS,CAAE,EAAE,CACbC,KAAK,CAAE,EAAE,CACTC,QAAQ,CAAE,EAAE,CACZC,cAAc,CAAEmC,WAAY;AAC9B,CAAC,CAAC,CACFjC,aAAa,CAAC,CAAC,CAAC,CAAC,CACjBE,eAAe,CAAC,CAAC,CAAC,CAAC,CACrB,CAAC,CAED;AACA,KAAM,CAAAuD,kBAAkB,CAAIC,MAAM,EAAK,CACrCxC,OAAO,CAACM,GAAG,CAAC,qBAAqB,CAAEkC,MAAM,CAAC,CAC1C7E,iBAAiB,CAAC6E,MAAM,CAAC,CACzB,GAAIhF,UAAU,GAAK,iBAAiB,CAAE,CACpCC,aAAa,CAAC,gBAAgB,CAAC,CAE/B;AACAM,WAAW,CAAC,CACVC,aAAa,CAAEwE,MAAM,CAACxE,aAAa,EAAI,EAAE,CACzCC,OAAO,CAAE2D,MAAM,CAACY,MAAM,CAACvE,OAAO,EAAI,EAAE,CAAC,CACrCC,SAAS,CAAE0D,MAAM,CAACY,MAAM,CAACtE,SAAS,EAAI,EAAE,CAAC,CACzCC,YAAY,CAAEqE,MAAM,CAACrE,YAAY,GAAK,IAAI,EAAIqE,MAAM,CAACrE,YAAY,GAAKsE,SAAS,CAAGb,MAAM,CAACY,MAAM,CAACrE,YAAY,CAAC,CAAG,EAAE,CAClHC,OAAO,CAAEoE,MAAM,CAACpE,OAAO,GAAK,IAAI,EAAIoE,MAAM,CAACpE,OAAO,GAAKqE,SAAS,CAAGb,MAAM,CAACY,MAAM,CAACpE,OAAO,CAAC,CAAG,EAAE,CAC9FC,YAAY,CAAEmE,MAAM,CAACnE,YAAY,GAAK,IAAI,EAAImE,MAAM,CAACnE,YAAY,GAAKoE,SAAS,CAAGC,MAAM,CAACF,MAAM,CAACnE,YAAY,CAAC,CAAG,EAAE,CAClHC,aAAa,CAAEkE,MAAM,CAAClE,aAAa,GAAK,IAAI,EAAIkE,MAAM,CAAClE,aAAa,GAAKmE,SAAS,CAAGC,MAAM,CAACF,MAAM,CAAClE,aAAa,CAAC,CAAG,EAAE,CACtHC,YAAY,CAAEqD,MAAM,CAACY,MAAM,CAACjE,YAAY,EAAI,aAAa,CAAC,CAC1DC,iBAAiB,CAAEoD,MAAM,CAACY,MAAM,CAAChE,iBAAiB,EAAI,EAAE,CAAC,CACzDC,SAAS,CAAEmD,MAAM,CAACY,MAAM,CAAC/D,SAAS,EAAI,EAAE,CAAC,CACzCC,KAAK,CAAEkD,MAAM,CAACY,MAAM,CAAC9D,KAAK,EAAI,EAAE,CAAC,CACjCC,QAAQ,CAAE6D,MAAM,CAAC7D,QAAQ,EAAI,EAAE,CAC/BC,cAAc,CAAEgD,MAAM,CAACY,MAAM,CAAC5D,cAAc,EAAI,GAAG,CACrD,CAAC,CAAC,CAEFoB,OAAO,CAACM,GAAG,CAAC,sCAAsC,CAAE,CAClDtC,aAAa,CAAEwE,MAAM,CAACxE,aAAa,CACnCC,OAAO,CAAEuE,MAAM,CAACvE,OAAO,CACvBC,SAAS,CAAEsE,MAAM,CAACtE,SAAS,CAC3BC,YAAY,CAAEqE,MAAM,CAACrE,YAAY,CACjCC,OAAO,CAAEoE,MAAM,CAACpE,OAAO,CACvBC,YAAY,CAAEmE,MAAM,CAACnE,YAAY,CACjCC,aAAa,CAAEkE,MAAM,CAAClE,aACxB,CAAC,CAAC,CACJ,CACF,CAAC,CAED;AACA,KAAM,CAAAqE,gBAAgB,CAAIC,CAAC,EAAK,CAC9B,KAAM,CAAEC,IAAI,CAAEC,KAAM,CAAC,CAAGF,CAAC,CAACG,MAAM,CAEhC;AACAhF,WAAW,CAAC,CACV,GAAGD,QAAQ,CACX,CAAC+E,IAAI,EAAGC,KACV,CAAC,CAAC,CAEF;AACA,GAAItF,UAAU,GAAK,YAAY,EAAIA,UAAU,GAAK,gBAAgB,CAAE,CAClE;AACA,GAAIqF,IAAI,GAAK,eAAe,EAAI/E,QAAQ,CAACc,cAAc,GAAK,GAAG,CAAE,CAC/D,KAAM,CAAAoE,QAAQ,CAAG5G,gBAAgB,CAAC0G,KAAK,CAAC,CACxC,GAAI,CAACE,QAAQ,CAACC,KAAK,CAAE,CACnBnE,aAAa,CAACoE,IAAI,GAAK,CACrB,GAAGA,IAAI,CACP,CAACL,IAAI,EAAGG,QAAQ,CAACjD,OACnB,CAAC,CAAC,CAAC,CACL,CAAC,IAAM,CACLjB,aAAa,CAACoE,IAAI,EAAI,CACpB,KAAM,CAAAC,SAAS,CAAG,CAAE,GAAGD,IAAK,CAAC,CAC7B,MAAO,CAAAC,SAAS,CAACN,IAAI,CAAC,CACtB,MAAO,CAAAM,SAAS,CAClB,CAAC,CAAC,CACJ,CACA,OACF,CAEA,KAAM,CAAAC,MAAM,CAAGjH,mBAAmB,CAAC0G,IAAI,CAAEC,KAAK,CAAC,CAE/C;AACA,GAAI,CAACM,MAAM,CAACH,KAAK,CAAE,CACjBnE,aAAa,CAACoE,IAAI,GAAK,CACrB,GAAGA,IAAI,CACP,CAACL,IAAI,EAAGO,MAAM,CAACrD,OACjB,CAAC,CAAC,CAAC,CACL,CAAC,IAAM,CACLjB,aAAa,CAACoE,IAAI,EAAI,CACpB,KAAM,CAAAC,SAAS,CAAG,CAAE,GAAGD,IAAK,CAAC,CAC7B,MAAO,CAAAC,SAAS,CAACN,IAAI,CAAC,CACtB,MAAO,CAAAM,SAAS,CAClB,CAAC,CAAC,CAEF;AACA,GAAIC,MAAM,CAACC,OAAO,CAAE,CAClBrE,eAAe,CAACkE,IAAI,GAAK,CACvB,GAAGA,IAAI,CACP,CAACL,IAAI,EAAGO,MAAM,CAACrD,OACjB,CAAC,CAAC,CAAC,CACL,CAAC,IAAM,CACLf,eAAe,CAACkE,IAAI,EAAI,CACtB,KAAM,CAAAI,WAAW,CAAG,CAAE,GAAGJ,IAAK,CAAC,CAC/B,MAAO,CAAAI,WAAW,CAACT,IAAI,CAAC,CACxB,MAAO,CAAAS,WAAW,CACpB,CAAC,CAAC,CACJ,CACF,CACF,CACF,CAAC,CAED;AACA,KAAM,CAAAC,UAAU,CAAG,KAAAA,CAAA,GAAY,CAC7B,GAAI,CACF;AACA,GAAI/F,UAAU,GAAK,YAAY,EAAIA,UAAU,GAAK,gBAAgB,CAAE,CAClE,KAAM,CAAAgG,UAAU,CAAGtH,kBAAkB,CAAC4B,QAAQ,CAAC,CAE/C,GAAI,CAAC0F,UAAU,CAACC,OAAO,CAAE,CACvB3E,aAAa,CAAC0E,UAAU,CAACE,MAAM,CAAC,CAChC1E,eAAe,CAACwE,UAAU,CAACG,QAAQ,CAAC,CACpC7G,OAAO,CAAC,+CAA+C,CAAC,CACxD,OACF,CAEA;AACA,GAAIgB,QAAQ,CAACc,cAAc,GAAK,GAAG,GAAK,CAACd,QAAQ,CAACE,aAAa,EAAIF,QAAQ,CAACE,aAAa,CAAC4F,IAAI,CAAC,CAAC,GAAK,EAAE,CAAC,CAAE,CACxG9E,aAAa,CAAC,CACZ,GAAGD,UAAU,CACbb,aAAa,CAAE,mCACjB,CAAC,CAAC,CACFlB,OAAO,CAAC,mCAAmC,CAAC,CAC5C,OACF,CACF,CAEAG,UAAU,CAAC,IAAI,CAAC,CAChB+C,OAAO,CAACM,GAAG,CAAC,qCAAqC,CAAC,CAElD,GAAI9C,UAAU,GAAK,YAAY,CAAE,CAC/B;AACA,KAAM,CAAAqG,UAAU,CAAG,CACjB,GAAG/F,QAAQ,CACX;AACAE,aAAa,CAAE4D,MAAM,CAAC9D,QAAQ,CAACE,aAAa,EAAI,EAAE,CAAC,CACnDC,OAAO,CAAE2D,MAAM,CAAC9D,QAAQ,CAACG,OAAO,EAAI,EAAE,CAAC,CACvCC,SAAS,CAAE0D,MAAM,CAAC9D,QAAQ,CAACI,SAAS,EAAI,EAAE,CAAC,CAC3CC,YAAY,CAAEyD,MAAM,CAAC9D,QAAQ,CAACK,YAAY,EAAI,EAAE,CAAC,CACjDC,OAAO,CAAEwD,MAAM,CAAC9D,QAAQ,CAACM,OAAO,EAAI,EAAE,CAAC,CACvCC,YAAY,CAAEyF,UAAU,CAAChG,QAAQ,CAACO,YAAY,CAAC,EAAI,CAAC,CACpDG,iBAAiB,CAAEoD,MAAM,CAAC9D,QAAQ,CAACU,iBAAiB,EAAI,KAAK,CAAC,CAC9DC,SAAS,CAAEmD,MAAM,CAAC9D,QAAQ,CAACW,SAAS,EAAI,KAAK,CAAC,CAC9CC,KAAK,CAAEkD,MAAM,CAAC9D,QAAQ,CAACY,KAAK,EAAI,KAAK,CAAC,CACtCC,QAAQ,CAAEb,QAAQ,CAACa,QAAQ,EAAI,IAAI,CACnC;AACAC,cAAc,CAAEgD,MAAM,CAAC9D,QAAQ,CAACc,cAAc,EAAI,GAAG,CACvD,CAAC,CAEDoB,OAAO,CAACM,GAAG,CAAC,yBAAyB,CAAEuD,UAAU,CAAC,CAElD,GAAI,CACF;AACA7D,OAAO,CAACM,GAAG,CAAC,+BAA+B,CAAET,IAAI,CAACC,SAAS,CAAC+D,UAAU,CAAE,IAAI,CAAE,CAAC,CAAC,CAAC,CAEjF;AACA,KAAM,CAAA/H,gBAAgB,CAACiI,YAAY,CAACnH,UAAU,CAAEiH,UAAU,CAAC,CAC3DhH,SAAS,CAAC,4BAA4B,CAAC,CAEvC;AACAyF,iBAAiB,CAAC,CAAC,CAEnB;AACAjF,iBAAiB,CAAC,kBAAkB,CAAC,CACrCkC,UAAU,CAAC,CAAC,CAEZ;AACA,GAAI,MAAO,CAAAyE,MAAM,GAAK,WAAW,CAAE,CACjC;AACA,KAAM,CAAAC,KAAK,CAAG,GAAI,CAAAC,WAAW,CAAC,4BAA4B,CAAE,CAAEtE,MAAM,CAAE,CAAEhD,UAAW,CAAE,CAAC,CAAC,CACvFoH,MAAM,CAACG,aAAa,CAACF,KAAK,CAAC,CAC7B,CACF,CAAE,MAAOvE,KAAK,CAAE,CACdM,OAAO,CAACN,KAAK,CAAC,2CAA2C,CAAEA,KAAK,CAAC,CAEjE;AACA,GAAI,CAAAC,YAAY,CAAG,0CAA0C,CAE7D,GAAID,KAAK,CAACE,MAAM,CAAE,CAChBD,YAAY,CAAG,MAAO,CAAAD,KAAK,CAACE,MAAM,GAAK,QAAQ,CAAGC,IAAI,CAACC,SAAS,CAACJ,KAAK,CAACE,MAAM,CAAC,CAAGF,KAAK,CAACE,MAAM,CAC/F,CAAC,IAAM,IAAIF,KAAK,CAACK,OAAO,CAAE,CACxBJ,YAAY,CAAGD,KAAK,CAACK,OAAO,CAC9B,CAEA;AACAC,OAAO,CAACN,KAAK,CAAC,kBAAkB,CAAEC,YAAY,CAAC,CAC/CK,OAAO,CAACN,KAAK,CAAC,eAAe,CAAEG,IAAI,CAACC,SAAS,CAAC+D,UAAU,CAAE,IAAI,CAAE,CAAC,CAAC,CAAC,CAEnE/G,OAAO,CAAC6C,YAAY,CAAC,CACrB1C,UAAU,CAAC,KAAK,CAAC,CACjB,OACF,CACF,CAAC,IAAM,IAAIO,UAAU,GAAK,gBAAgB,CAAE,CAC1C;AACA,KAAM,CAAA4G,QAAQ,CAAG1G,cAAc,CAAC2G,SAAS,EAAI3G,cAAc,CAACM,aAAa,EAAIF,QAAQ,CAACE,aAAa,CAEnGgC,OAAO,CAACM,GAAG,CAAC,yBAAyB,CAAE8D,QAAQ,CAAC,CAChDpE,OAAO,CAACM,GAAG,CAAC,kBAAkB,CAAExC,QAAQ,CAAC,CAEzC,GAAI,CACF,KAAM,CAAA8C,QAAQ,CAAG,KAAM,CAAA9E,gBAAgB,CAACwI,YAAY,CAAC1H,UAAU,CAAEwH,QAAQ,CAAEtG,QAAQ,CAAC,CACpFkC,OAAO,CAACM,GAAG,CAAC,2BAA2B,CAAEM,QAAQ,CAAC,CAClD/D,SAAS,CAAC,gCAAgC,CAAC,CAC7C,CAAE,MAAO6C,KAAK,CAAE,CACdM,OAAO,CAACN,KAAK,CAAC,0CAA0C,CAAEA,KAAK,CAAC,CAChE,GAAI,CAAAC,YAAY,CAAG,yCAAyC,CAC5D,GAAID,KAAK,CAACE,MAAM,CAAE,CAChBD,YAAY,CAAG,MAAO,CAAAD,KAAK,CAACE,MAAM,GAAK,QAAQ,CAAGC,IAAI,CAACC,SAAS,CAACJ,KAAK,CAACE,MAAM,CAAC,CAAGF,KAAK,CAACE,MAAM,CAC/F,CAAC,IAAM,IAAIF,KAAK,CAACK,OAAO,CAAE,CACxBJ,YAAY,CAAGD,KAAK,CAACK,OAAO,CAC9B,CACAjD,OAAO,CAAC6C,YAAY,CAAC,CACrB1C,UAAU,CAAC,KAAK,CAAC,CACjB,OACF,CACF,CAAC,IAAM,IAAIO,UAAU,GAAK,eAAe,CAAE,CACzC;AACA,KAAM,CAAA4G,QAAQ,CAAG1G,cAAc,CAAC2G,SAAS,EAAI3G,cAAc,CAACM,aAAa,CACzE,KAAM,CAAA4C,QAAQ,CAAG,KAAM,CAAA9E,gBAAgB,CAACyI,YAAY,CAAC3H,UAAU,CAAEwH,QAAQ,CAAC,CAE1E;AACA,GAAIxD,QAAQ,CAAC4D,cAAc,CAAE,CAC3BxE,OAAO,CAACM,GAAG,CAAC,0CAA0C1D,UAAU,8BAA8B,CAAC,CAC/FC,SAAS,CAAC,2DAA2D,CAAC,CACtE;AACA;AACF,CAAC,IAAM,CACLA,SAAS,CAAC,+BAA+B,CAAC,CAC5C,CACF,CAEAyF,iBAAiB,CAAC,CAAC,CACnB/C,UAAU,CAAC,CAAC,CAAE;AAChB,CAAE,MAAOG,KAAK,CAAE,CACd,GAAI,CAAAC,YAAY,CAAG,8BAA8B,CACjD,GAAID,KAAK,CAACE,MAAM,CAAE,CAChBD,YAAY,EAAI,IAAI,EAAI,MAAO,CAAAD,KAAK,CAACE,MAAM,GAAK,QAAQ,CAAGC,IAAI,CAACC,SAAS,CAACJ,KAAK,CAACE,MAAM,CAAC,CAAGF,KAAK,CAACE,MAAM,CAAC,CACzG,CAAC,IAAM,IAAIF,KAAK,CAACK,OAAO,CAAE,CACxBJ,YAAY,EAAI,IAAI,CAAGD,KAAK,CAACK,OAAO,CACtC,CAAC,IAAM,CACLJ,YAAY,EAAI,sBAAsB,CACxC,CACA7C,OAAO,CAAC6C,YAAY,CAAC,CACrBK,OAAO,CAACN,KAAK,CAAC,+BAA+B,CAAEA,KAAK,CAAC,CACvD,CAAC,OAAS,CACRzC,UAAU,CAAC,KAAK,CAAC,CACnB,CACF,CAAC,CAED;AACA,KAAM,CAAAwH,iBAAiB,CAAGA,CAAA,GAAM,CAC9B,mBACElI,IAAA,CAACP,qBAAqB,EACpBkB,MAAM,CAAEA,MAAO,CACfF,OAAO,CAAEA,OAAQ,CACjB0H,oBAAoB,CAAGC,YAAY,EAAK3E,OAAO,CAACM,GAAG,CAAC,kBAAkB,CAAEqE,YAAY,CAACxD,MAAM,CAAE,CAC7FyD,MAAM,CAAGpC,MAAM,EAAK,CAClB7E,iBAAiB,CAAC6E,MAAM,CAAC,CACzB/E,aAAa,CAAC,gBAAgB,CAAC,CAC/BF,aAAa,CAAC,IAAI,CAAC,CACrB,CAAE,CACFsH,QAAQ,CAAGrC,MAAM,EAAK,CACpB7E,iBAAiB,CAAC6E,MAAM,CAAC,CACzB/E,aAAa,CAAC,eAAe,CAAC,CAC9BF,aAAa,CAAC,IAAI,CAAC,CACrB,CAAE,CACFuH,aAAa,CAAGtC,MAAM,EAAK,CACzB7E,iBAAiB,CAAC6E,MAAM,CAAC,CACzBvC,mBAAmB,CAAC,CAAC,CACrBxC,aAAa,CAAC,mBAAmB,CAAC,CAClCF,aAAa,CAAC,IAAI,CAAC,CACrB,CAAE,CACFwH,UAAU,CAAGvC,MAAM,EAAK,CACtB7E,iBAAiB,CAAC6E,MAAM,CAAC,CACzB3E,qBAAqB,CAAC,IAAI,CAAC,CAC7B,CAAE,CACH,CAAC,CAEN,CAAC,CAED;AACA,KAAM,CAAAmH,YAAY,CAAGA,CAAA,GAAM,CACzB,GAAIxH,UAAU,GAAK,YAAY,EAAIA,UAAU,GAAK,gBAAgB,CAAE,CAClE,mBACEf,KAAA,CAAClD,MAAM,EAAC0L,IAAI,CAAE3H,UAAW,CAAC4H,OAAO,CAAE5C,iBAAkB,CAAC6C,QAAQ,CAAC,IAAI,CAACC,SAAS,MAAAC,QAAA,eAC3E9I,IAAA,CAAC/C,WAAW,EAAA6L,QAAA,CACT7H,UAAU,GAAK,YAAY,CAAG,mBAAmB,CAAG,iBAAiB,CAC3D,CAAC,cACdf,KAAA,CAAChD,aAAa,EAAA4L,QAAA,EACX7H,UAAU,GAAK,gBAAgB,eAC9Bf,KAAA,CAACnC,KAAK,EAACgL,QAAQ,CAAC,MAAM,CAACC,EAAE,CAAE,CAAEC,EAAE,CAAE,CAAC,CAAEC,EAAE,CAAE,CAAE,CAAE,CAAAJ,QAAA,eAC1C9I,IAAA,CAACvD,UAAU,EAAC0M,OAAO,CAAC,WAAW,CAACC,UAAU,CAAC,MAAM,CAAAN,QAAA,CAAC,6BAElD,CAAY,CAAC,cACb5I,KAAA,OAAA4I,QAAA,eACE9I,IAAA,OAAA8I,QAAA,CAAI,mDAA+C,CAAI,CAAC,cACxD9I,IAAA,OAAA8I,QAAA,CAAI,mDAAiD,CAAI,CAAC,cAC1D9I,IAAA,OAAA8I,QAAA,CAAI,kFAAgF,CAAI,CAAC,EACvF,CAAC,cACL9I,IAAA,CAACxD,GAAG,EAACwM,EAAE,CAAE,CAAEE,EAAE,CAAE,CAAE,CAAE,CAAAJ,QAAA,cACjB5I,KAAA,CAACzD,UAAU,EAAC0M,OAAO,CAAC,OAAO,CAAAL,QAAA,eACzB9I,IAAA,WAAA8I,QAAA,CAAQ,gBAAc,CAAQ,CAAC,IAAC,CAAC,CAAA3H,cAAc,SAAdA,cAAc,iBAAdA,cAAc,CAAEa,YAAY,GAAI,KAAK,EAC5D,CAAC,CACV,CAAC,EACD,CACR,CAEAqH,MAAM,CAACC,IAAI,CAAC9G,YAAY,CAAC,CAACoC,MAAM,CAAG,CAAC,eACnC1E,KAAA,CAACnC,KAAK,EAACgL,QAAQ,CAAC,SAAS,CAACC,EAAE,CAAE,CAAEC,EAAE,CAAE,CAAC,CAAEC,EAAE,CAAE,CAAE,CAAE,CAAAJ,QAAA,eAC7C9I,IAAA,CAACvD,UAAU,EAAC0M,OAAO,CAAC,WAAW,CAAAL,QAAA,CAAC,aAAW,CAAY,CAAC,cACxD9I,IAAA,OAAIuJ,KAAK,CAAE,CAAEC,MAAM,CAAE,CAAC,CAAEC,WAAW,CAAE,MAAO,CAAE,CAAAX,QAAA,CAC3CO,MAAM,CAACK,MAAM,CAAClH,YAAY,CAAC,CAACyC,GAAG,CAAC,CAAC6B,OAAO,CAAE6C,KAAK,gBAC9C3J,IAAA,OAAA8I,QAAA,CAAiBhC,OAAO,EAAf6C,KAAoB,CAC9B,CAAC,CACA,CAAC,EACA,CACR,cAEDzJ,KAAA,CAACtD,IAAI,EAACgN,SAAS,MAACC,OAAO,CAAE,CAAE,CAACb,EAAE,CAAE,CAAEE,EAAE,CAAE,CAAE,CAAE,CAAAJ,QAAA,eACxC9I,IAAA,CAACpD,IAAI,EAACkN,IAAI,MAACC,EAAE,CAAE,EAAG,CAACC,EAAE,CAAE,CAAE,CAAAlB,QAAA,cACvB9I,IAAA,CAAC5C,SAAS,EACRkJ,IAAI,CAAC,eAAe,CACpB2D,KAAK,CAAC,WAAW,CACjBpB,SAAS,MACTM,OAAO,CAAC,UAAU,CAClB5C,KAAK,CAAEhF,QAAQ,CAACE,aAAc,CAC9ByI,QAAQ,CAAE9D,gBAAiB,CAC3B+D,QAAQ,CAAElJ,UAAU,GAAK,gBAAgB,EAAKA,UAAU,GAAK,YAAY,EAAIM,QAAQ,CAACc,cAAc,GAAK,GAAK,CAC9G+H,QAAQ,MACRjH,KAAK,CAAE,CAAC,CAACb,UAAU,CAACb,aAAc,CAClC4I,UAAU,CAAE,CACVrB,EAAE,CAAE,CACFsB,OAAO,CAAE/I,QAAQ,CAACc,cAAc,GAAK,GAAG,CAAG,SAAS,CAAG,aAAa,CACpE+G,UAAU,CAAE,MACd,CACF,CAAE,CACFmB,UAAU,CACRjI,UAAU,CAACb,aAAa,GACvBR,UAAU,GAAK,YAAY,EAAIM,QAAQ,CAACc,cAAc,GAAK,GAAG,CAC3D,iBAAiBhC,UAAU,KAAKkB,QAAQ,CAACE,aAAa,EAAI,EAAE,6BAA6B,CACzFR,UAAU,GAAK,YAAY,EAAIM,QAAQ,CAACc,cAAc,GAAK,GAAG,CAC5D,8DAA8DhC,UAAU,eAAe,CACvF,EAAE,CACT,CACDmK,IAAI,CAAEjJ,QAAQ,CAACc,cAAc,GAAK,GAAG,CAAG,MAAM,CAAG,MAAO,CACzD,CAAC,CACE,CAAC,cACPrC,IAAA,CAACpD,IAAI,EAACkN,IAAI,MAACC,EAAE,CAAE,EAAG,CAACC,EAAE,CAAE,CAAE,CAAAlB,QAAA,cACvB9I,IAAA,CAAC5C,SAAS,EACRkJ,IAAI,CAAC,SAAS,CACd2D,KAAK,CAAC,SAAS,CACfpB,SAAS,MACTM,OAAO,CAAC,UAAU,CAClB5C,KAAK,CAAEhF,QAAQ,CAACG,OAAQ,CACxBwI,QAAQ,CAAE9D,gBAAiB,CAC3BgE,QAAQ,MACRjH,KAAK,CAAE,CAAC,CAACb,UAAU,CAACZ,OAAQ,CAC5B6I,UAAU,CAAEjI,UAAU,CAACZ,OAAO,EAAI,EAAG,CACtC,CAAC,CACE,CAAC,cACP1B,IAAA,CAACpD,IAAI,EAACkN,IAAI,MAACC,EAAE,CAAE,EAAG,CAACC,EAAE,CAAE,CAAE,CAAAlB,QAAA,cACvB9I,IAAA,CAAC5C,SAAS,EACRkJ,IAAI,CAAC,WAAW,CAChB2D,KAAK,CAAC,WAAW,CACjBpB,SAAS,MACTM,OAAO,CAAC,UAAU,CAClB5C,KAAK,CAAEhF,QAAQ,CAACI,SAAU,CAC1BuI,QAAQ,CAAE9D,gBAAiB,CAC3BgE,QAAQ,MACRjH,KAAK,CAAE,CAAC,CAACb,UAAU,CAACX,SAAU,CAC9B4I,UAAU,CAAEjI,UAAU,CAACX,SAAS,EAAI,EAAG,CACxC,CAAC,CACE,CAAC,cAEP3B,IAAA,CAACpD,IAAI,EAACkN,IAAI,MAACC,EAAE,CAAE,EAAG,CAACC,EAAE,CAAE,CAAE,CAAAlB,QAAA,cACvB9I,IAAA,CAAC5C,SAAS,EACRkJ,IAAI,CAAC,SAAS,CACd2D,KAAK,CAAC,YAAY,CAClBpB,SAAS,MACTM,OAAO,CAAC,UAAU,CAClB5C,KAAK,CAAEhF,QAAQ,CAACM,OAAQ,CACxBqI,QAAQ,CAAE9D,gBAAiB,CAC3BgE,QAAQ,MACRjH,KAAK,CAAE,CAAC,CAACb,UAAU,CAACT,OAAQ,CAC5B0I,UAAU,CAAEjI,UAAU,CAACT,OAAO,EAAI,EAAG,CACtC,CAAC,CACE,CAAC,cACP7B,IAAA,CAACpD,IAAI,EAACkN,IAAI,MAACC,EAAE,CAAE,EAAG,CAACC,EAAE,CAAE,CAAE,CAAAlB,QAAA,cACvB9I,IAAA,CAAC5C,SAAS,EACRkJ,IAAI,CAAC,cAAc,CACnB2D,KAAK,CAAC,cAAc,CACpBO,IAAI,CAAC,QAAQ,CACb3B,SAAS,MACTM,OAAO,CAAC,UAAU,CAClB5C,KAAK,CAAEhF,QAAQ,CAACO,YAAa,CAC7BoI,QAAQ,CAAE9D,gBAAiB,CAC3BgE,QAAQ,MACRjH,KAAK,CAAE,CAAC,CAACb,UAAU,CAACR,YAAa,CACjCyI,UAAU,CAAEjI,UAAU,CAACR,YAAY,EAAI,EAAG,CAC3C,CAAC,CACE,CAAC,CACNb,UAAU,GAAK,gBAAgB,eAC9BjB,IAAA,CAACpD,IAAI,EAACkN,IAAI,MAACC,EAAE,CAAE,EAAG,CAACC,EAAE,CAAE,CAAE,CAAAlB,QAAA,cACvB9I,IAAA,CAAC5C,SAAS,EACRkJ,IAAI,CAAC,eAAe,CACpB2D,KAAK,CAAC,eAAe,CACrBO,IAAI,CAAC,QAAQ,CACb3B,SAAS,MACTM,OAAO,CAAC,UAAU,CAClB5C,KAAK,CAAEhF,QAAQ,CAACQ,aAAc,CAC9BmI,QAAQ,CAAE9D,gBAAiB,CAC3BgE,QAAQ,MACRD,QAAQ,CAAE,IAAK,CACfI,UAAU,CAAC,4DAA4D,CACxE,CAAC,CACE,CACP,cACDvK,IAAA,CAACpD,IAAI,EAACkN,IAAI,MAACC,EAAE,CAAE,EAAG,CAACC,EAAE,CAAE,CAAE,CAAAlB,QAAA,cACvB5I,KAAA,CAAC7C,WAAW,EAACwL,SAAS,MAAAC,QAAA,eACpB9I,IAAA,CAAC1C,UAAU,EAACmN,EAAE,CAAC,oBAAoB,CAAA3B,QAAA,CAAC,cAAY,CAAY,CAAC,cAC7D5I,KAAA,CAAC3C,MAAM,EACLmN,OAAO,CAAC,oBAAoB,CAC5BpE,IAAI,CAAC,cAAc,CACnBC,KAAK,CAAEhF,QAAQ,CAACS,YAAa,CAC7BiI,KAAK,CAAC,cAAc,CACpBC,QAAQ,CAAE9D,gBAAiB,CAC3B+D,QAAQ,CAAElJ,UAAU,GAAK,YAAa,CAAA6H,QAAA,eAEtC9I,IAAA,CAACxC,QAAQ,EAAC+I,KAAK,CAAC,aAAa,CAAAuC,QAAA,CAAC,aAAW,CAAU,CAAC,cACpD9I,IAAA,CAACxC,QAAQ,EAAC+I,KAAK,CAAC,QAAQ,CAAAuC,QAAA,CAAC,QAAM,CAAU,CAAC,cAC1C9I,IAAA,CAACxC,QAAQ,EAAC+I,KAAK,CAAC,WAAW,CAAAuC,QAAA,CAAC,WAAS,CAAU,CAAC,cAChD9I,IAAA,CAACxC,QAAQ,EAAC+I,KAAK,CAAC,aAAa,CAAAuC,QAAA,CAAC,aAAW,CAAU,CAAC,cACpD9I,IAAA,CAACxC,QAAQ,EAAC+I,KAAK,CAAC,MAAM,CAAAuC,QAAA,CAAC,MAAI,CAAU,CAAC,EAChC,CAAC,CACR7H,UAAU,GAAK,YAAY,eAC1BjB,IAAA,CAAC/B,cAAc,EAAA6K,QAAA,CAAC,4DAAqD,CAAgB,CACtF,EACU,CAAC,CACV,CAAC,cACP9I,IAAA,CAACpD,IAAI,EAACkN,IAAI,MAACC,EAAE,CAAE,EAAG,CAACC,EAAE,CAAE,CAAE,CAAAlB,QAAA,cACvB9I,IAAA,CAAC5C,SAAS,EACRkJ,IAAI,CAAC,mBAAmB,CACxB2D,KAAK,CAAC,mBAAmB,CACzBpB,SAAS,MACTM,OAAO,CAAC,UAAU,CAClB5C,KAAK,CAAEhF,QAAQ,CAACU,iBAAkB,CAClCiI,QAAQ,CAAE9D,gBAAiB,CAC3BjD,KAAK,CAAE,CAAC,CAACb,UAAU,CAACL,iBAAkB,CACtCsI,UAAU,CAAEjI,UAAU,CAACL,iBAAiB,EAAI,EAAG,CAChD,CAAC,CACE,CAAC,cACPjC,IAAA,CAACpD,IAAI,EAACkN,IAAI,MAACC,EAAE,CAAE,EAAG,CAACC,EAAE,CAAE,CAAE,CAAAlB,QAAA,cACvB9I,IAAA,CAAC5C,SAAS,EACRkJ,IAAI,CAAC,WAAW,CAChB2D,KAAK,CAAC,WAAW,CACjBpB,SAAS,MACTM,OAAO,CAAC,UAAU,CAClB5C,KAAK,CAAEhF,QAAQ,CAACW,SAAU,CAC1BgI,QAAQ,CAAE9D,gBAAiB,CAC3BjD,KAAK,CAAE,CAAC,CAACb,UAAU,CAACJ,SAAU,CAC9BqI,UAAU,CAAEjI,UAAU,CAACJ,SAAS,EAAI,EAAG,CACxC,CAAC,CACE,CAAC,cACPlC,IAAA,CAACpD,IAAI,EAACkN,IAAI,MAACC,EAAE,CAAE,EAAG,CAACC,EAAE,CAAE,CAAE,CAAAlB,QAAA,cACvB9I,IAAA,CAAC5C,SAAS,EACRkJ,IAAI,CAAC,OAAO,CACZ2D,KAAK,CAAC,YAAY,CAClBpB,SAAS,MACTM,OAAO,CAAC,UAAU,CAClB5C,KAAK,CAAEhF,QAAQ,CAACY,KAAM,CACtB+H,QAAQ,CAAE9D,gBAAiB,CAC3BjD,KAAK,CAAE,CAAC,CAACb,UAAU,CAACH,KAAM,CAC1BoI,UAAU,CAAEjI,UAAU,CAACH,KAAK,EAAI,EAAG,CACpC,CAAC,CACE,CAAC,cACPnC,IAAA,CAACpD,IAAI,EAACkN,IAAI,MAACC,EAAE,CAAE,EAAG,CAACC,EAAE,CAAE,CAAE,CAAAlB,QAAA,cACvB9I,IAAA,CAAC5C,SAAS,EACRkJ,IAAI,CAAC,UAAU,CACf2D,KAAK,CAAC,uBAAuB,CAC7BpB,SAAS,MACTM,OAAO,CAAC,UAAU,CAClB5C,KAAK,CAAEhF,QAAQ,CAACa,QAAS,CACzB8H,QAAQ,CAAE9D,gBAAiB,CAC3BuE,WAAW,CAAC,YAAY,CACxBxH,KAAK,CAAE,CAAC,CAACb,UAAU,CAACF,QAAS,CAC7BmI,UAAU,CAAEjI,UAAU,CAACF,QAAQ,EAAI,EAAG,CACvC,CAAC,CACE,CAAC,cACPpC,IAAA,CAACpD,IAAI,EAACkN,IAAI,MAACC,EAAE,CAAE,EAAG,CAACC,EAAE,CAAE,CAAE,CAAAlB,QAAA,cACvB9I,IAAA,CAAC5C,SAAS,EACRkJ,IAAI,CAAC,gBAAgB,CACrB2D,KAAK,CAAC,yBAAsB,CAC5BpB,SAAS,MACTM,OAAO,CAAC,UAAU,CAClB5C,KAAK,CAAEhF,QAAQ,CAACc,cAAc,GAAK,GAAG,CAAG,YAAY,CAAG,SAAU,CAClEgI,UAAU,CAAE,CACVO,QAAQ,CAAE,IAAI,CACd5B,EAAE,CAAE,CACFsB,OAAO,CAAE,SACX,CACF,CAAE,CACFC,UAAU,cACRvK,IAAA,CAACxD,GAAG,EAACwM,EAAE,CAAE,CAAEI,UAAU,CAAE,QAAQ,CAAEyB,KAAK,CAAEtJ,QAAQ,CAACc,cAAc,GAAK,GAAG,CAAG,cAAc,CAAG,WAAY,CAAE,CAAAyG,QAAA,CACtGvH,QAAQ,CAACc,cAAc,GAAK,GAAG,CAC5B,mDAAmD,CACnD,6DAA6D,CAC9D,CACN,CACF,CAAC,CACE,CAAC,EACH,CAAC,EACM,CAAC,cAChBnC,KAAA,CAAC/C,aAAa,EAAA2L,QAAA,eACZ9I,IAAA,CAACtD,MAAM,EAACoO,OAAO,CAAE/E,iBAAkB,CAAA+C,QAAA,CAAC,SAAO,CAAQ,CAAC,cACpD9I,IAAA,CAACtD,MAAM,EACLoO,OAAO,CAAE9D,UAAW,CACpBmD,QAAQ,CAAE1J,OAAO,EAAI4I,MAAM,CAACC,IAAI,CAAChH,UAAU,CAAC,CAACsC,MAAM,CAAG,CAAE,CACxDmG,SAAS,CAAEtK,OAAO,cAAGT,IAAA,CAAChC,gBAAgB,EAACgN,IAAI,CAAE,EAAG,CAAE,CAAC,cAAGhL,IAAA,CAACd,QAAQ,GAAE,CAAE,CACnEiK,OAAO,CAAC,WAAW,CACnB0B,KAAK,CAAC,SAAS,CAAA/B,QAAA,CAChB,OAED,CAAQ,CAAC,EACI,CAAC,EACV,CAAC,CAEb,CAAC,IAAM,IAAI7H,UAAU,GAAK,iBAAiB,CAAE,CAC3C,mBACEf,KAAA,CAAClD,MAAM,EAAC0L,IAAI,CAAE3H,UAAW,CAAC4H,OAAO,CAAE5C,iBAAkB,CAAC6C,QAAQ,CAAC,IAAI,CAACC,SAAS,MAAAC,QAAA,eAC3E9I,IAAA,CAAC/C,WAAW,EAAA6L,QAAA,CAAC,gCAA8B,CAAa,CAAC,cACzD9I,IAAA,CAAC9C,aAAa,EAAA4L,QAAA,CACXrI,OAAO,cACNT,IAAA,CAAChC,gBAAgB,GAAE,CAAC,CAClB2C,MAAM,CAACiE,MAAM,GAAK,CAAC,cACrB5E,IAAA,CAACjC,KAAK,EAACgL,QAAQ,CAAC,MAAM,CAAAD,QAAA,CAAC,4BAA0B,CAAO,CAAC,cAEzD9I,IAAA,CAACvC,IAAI,EAAAqL,QAAA,CACFnI,MAAM,CAACsE,GAAG,CAAEgB,MAAM,eACjBjG,IAAA,CAACtC,QAAQ,EACPuN,MAAM,MAENH,OAAO,CAAEA,CAAA,GAAM9E,kBAAkB,CAACC,MAAM,CAAE,CAAA6C,QAAA,cAE1C9I,IAAA,CAACrC,YAAY,EACXuN,OAAO,CAAE,WAAWjF,MAAM,CAACxE,aAAa,EAAG,CAC3C0J,SAAS,CAAE,cAAclF,MAAM,CAACtE,SAAS,EAAI,KAAK,eAAesE,MAAM,CAACvE,OAAO,EAAI,KAAK,eAAeuE,MAAM,CAAClE,aAAa,EAAI,KAAK,IAAK,CAC1I,CAAC,EANGkE,MAAM,CAACxE,aAOJ,CACX,CAAC,CACE,CACP,CACY,CAAC,cAChBzB,IAAA,CAAC7C,aAAa,EAAA2L,QAAA,cACZ9I,IAAA,CAACtD,MAAM,EAACoO,OAAO,CAAE/E,iBAAkB,CAAA+C,QAAA,CAAC,SAAO,CAAQ,CAAC,CACvC,CAAC,EACV,CAAC,CAEb,CAAC,IAAM,IAAI7H,UAAU,GAAK,eAAe,CAAE,CACzC,mBACEf,KAAA,CAAClD,MAAM,EAAC0L,IAAI,CAAE3H,UAAW,CAAC4H,OAAO,CAAE5C,iBAAkB,CAAC6C,QAAQ,CAAC,IAAI,CAACC,SAAS,MAAAC,QAAA,eAC3E9I,IAAA,CAAC/C,WAAW,EAAA6L,QAAA,CAAC,gBAAc,CAAa,CAAC,cACzC9I,IAAA,CAAC9C,aAAa,EAAA4L,QAAA,CACX,CAAC3H,cAAc,CACdV,OAAO,cACLT,IAAA,CAAChC,gBAAgB,GAAE,CAAC,CAClB2C,MAAM,CAACiE,MAAM,GAAK,CAAC,cACrB5E,IAAA,CAACjC,KAAK,EAACgL,QAAQ,CAAC,MAAM,CAAAD,QAAA,CAAC,4BAA0B,CAAO,CAAC,cAEzD9I,IAAA,CAACvC,IAAI,EAAAqL,QAAA,CACFnI,MAAM,CAACsE,GAAG,CAAEgB,MAAM,eACjBjG,IAAA,CAACtC,QAAQ,EACPuN,MAAM,MAENH,OAAO,CAAEA,CAAA,GAAM1J,iBAAiB,CAAC6E,MAAM,CAAE,CAAA6C,QAAA,cAEzC9I,IAAA,CAACrC,YAAY,EACXuN,OAAO,CAAE,WAAWjF,MAAM,CAACxE,aAAa,EAAG,CAC3C0J,SAAS,CAAE,cAAclF,MAAM,CAACtE,SAAS,EAAI,KAAK,eAAesE,MAAM,CAACvE,OAAO,EAAI,KAAK,eAAeuE,MAAM,CAAClE,aAAa,EAAI,KAAK,IAAK,CAC1I,CAAC,EANGkE,MAAM,CAACxE,aAOJ,CACX,CAAC,CACE,CACP,cAEDvB,KAAA,CAAC1D,GAAG,EAAAsM,QAAA,eACF5I,KAAA,CAACnC,KAAK,EAACgL,QAAQ,CAAC,SAAS,CAACC,EAAE,CAAE,CAAEC,EAAE,CAAE,CAAE,CAAE,CAAAH,QAAA,EAAC,0CACC,CAAC3H,cAAc,CAACM,aAAa,CAAC,GACxE,EAAO,CAAC,cACRzB,IAAA,CAACvD,UAAU,EAAC0M,OAAO,CAAC,OAAO,CAACH,EAAE,CAAE,CAAEC,EAAE,CAAE,CAAE,CAAE,CAAAH,QAAA,CAAC,gDAE3C,CAAY,CAAC,cACb5I,KAAA,CAACnC,KAAK,EAACgL,QAAQ,CAAC,MAAM,CAACC,EAAE,CAAE,CAAEC,EAAE,CAAE,CAAE,CAAE,CAAAH,QAAA,eACnC9I,IAAA,CAACvD,UAAU,EAAC0M,OAAO,CAAC,WAAW,CAACC,UAAU,CAAC,MAAM,CAAAN,QAAA,CAAC,gCAElD,CAAY,CAAC,cACb5I,KAAA,OAAA4I,QAAA,eACE9I,IAAA,OAAA8I,QAAA,CAAI,4EAA0E,CAAI,CAAC,cACnF9I,IAAA,OAAA8I,QAAA,CAAI,mDAA+C,CAAI,CAAC,cACxD9I,IAAA,OAAA8I,QAAA,CAAI,mDAAiD,CAAI,CAAC,EACxD,CAAC,EACA,CAAC,cACR5I,KAAA,CAAC1D,GAAG,EAACwM,EAAE,CAAE,CAAEoC,OAAO,CAAE,MAAM,CAAEC,aAAa,CAAE,QAAQ,CAAEC,GAAG,CAAE,CAAC,CAAErC,EAAE,CAAE,CAAE,CAAE,CAAAH,QAAA,eACnE5I,KAAA,CAACzD,UAAU,EAAC0M,OAAO,CAAC,OAAO,CAAAL,QAAA,eACzB9I,IAAA,WAAA8I,QAAA,CAAQ,gBAAc,CAAQ,CAAC,IAAC,CAAC3H,cAAc,CAACa,YAAY,EAAI,KAAK,EAC3D,CAAC,cACb9B,KAAA,CAACzD,UAAU,EAAC0M,OAAO,CAAC,OAAO,CAAAL,QAAA,eACzB9I,IAAA,WAAA8I,QAAA,CAAQ,eAAa,CAAQ,CAAC,IAAC,CAAC3H,cAAc,CAACW,YAAY,EAAI,KAAK,EAC1D,CAAC,cACb5B,KAAA,CAACzD,UAAU,EAAC0M,OAAO,CAAC,OAAO,CAAAL,QAAA,eACzB9I,IAAA,WAAA8I,QAAA,CAAQ,gBAAc,CAAQ,CAAC,IAAC,CAAC3H,cAAc,CAACY,aAAa,EAAI,KAAK,EAC5D,CAAC,EACV,CAAC,EACH,CACN,CACY,CAAC,cAChB7B,KAAA,CAAC/C,aAAa,EAAA2L,QAAA,eACZ9I,IAAA,CAACtD,MAAM,EAACoO,OAAO,CAAE/E,iBAAkB,CAAA+C,QAAA,CAAC,SAAO,CAAQ,CAAC,CACnD3H,cAAc,eACbnB,IAAA,CAACtD,MAAM,EACLoO,OAAO,CAAE9D,UAAW,CACpBmD,QAAQ,CAAE1J,OAAQ,CAClBoK,KAAK,CAAC,OAAO,CACbE,SAAS,CAAEtK,OAAO,cAAGT,IAAA,CAAChC,gBAAgB,EAACgN,IAAI,CAAE,EAAG,CAAE,CAAC,cAAGhL,IAAA,CAAClB,UAAU,GAAE,CAAE,CAAAgK,QAAA,CACtE,SAED,CAAQ,CACT,EACY,CAAC,EACV,CAAC,CAEb,CAAC,IAAM,IAAI7H,UAAU,GAAK,mBAAmB,CAAE,CAC7C,mBACEf,KAAA,CAAClD,MAAM,EAAC0L,IAAI,CAAE3H,UAAW,CAAC4H,OAAO,CAAE5C,iBAAkB,CAAC6C,QAAQ,CAAC,IAAI,CAACC,SAAS,MAAAC,QAAA,eAC3E9I,IAAA,CAAC/C,WAAW,EAAA6L,QAAA,CAAC,yBAAuB,CAAa,CAAC,cAClD9I,IAAA,CAAC9C,aAAa,EAAA4L,QAAA,CACXrI,OAAO,cACNT,IAAA,CAAChC,gBAAgB,GAAE,CAAC,CAClB0E,eAAe,CAACkC,MAAM,GAAK,CAAC,cAC9B5E,IAAA,CAACjC,KAAK,EAACgL,QAAQ,CAAC,MAAM,CAAAD,QAAA,CAAC,iCAA+B,CAAO,CAAC,cAE9D9I,IAAA,CAAC1B,cAAc,EAACiN,SAAS,CAAE5O,KAAM,CAACqM,EAAE,CAAE,CAAEE,EAAE,CAAE,CAAE,CAAE,CAAAJ,QAAA,cAC9C5I,KAAA,CAAC/B,KAAK,EAAC6M,IAAI,CAAC,OAAO,CAAAlC,QAAA,eACjB9I,IAAA,CAACzB,SAAS,EAAAuK,QAAA,cACR5I,KAAA,CAAC1B,QAAQ,EAAAsK,QAAA,eACP9I,IAAA,CAAC3B,SAAS,EAAAyK,QAAA,CAAC,QAAM,CAAW,CAAC,cAC7B9I,IAAA,CAAC3B,SAAS,EAAAyK,QAAA,CAAC,SAAO,CAAW,CAAC,cAC9B9I,IAAA,CAAC3B,SAAS,EAAAyK,QAAA,CAAC,WAAS,CAAW,CAAC,cAChC9I,IAAA,CAAC3B,SAAS,EAAAyK,QAAA,CAAC,YAAU,CAAW,CAAC,cACjC9I,IAAA,CAAC3B,SAAS,EAAAyK,QAAA,CAAC,cAAY,CAAW,CAAC,cACnC9I,IAAA,CAAC3B,SAAS,EAAAyK,QAAA,CAAC,eAAa,CAAW,CAAC,cACpC9I,IAAA,CAAC3B,SAAS,EAAAyK,QAAA,CAAC,gBAAc,CAAW,CAAC,EAC7B,CAAC,CACF,CAAC,cACZ9I,IAAA,CAAC5B,SAAS,EAAA0K,QAAA,CACPpG,eAAe,CAACuC,GAAG,CAAC,CAACuG,MAAM,CAAE7B,KAAK,gBACjCzJ,KAAA,CAAC1B,QAAQ,EAAAsK,QAAA,eACP9I,IAAA,CAAC3B,SAAS,EAAAyK,QAAA,CAAE0C,MAAM,CAAC/J,aAAa,CAAY,CAAC,cAC7CzB,IAAA,CAAC3B,SAAS,EAAAyK,QAAA,CAAE0C,MAAM,CAAC9J,OAAO,CAAY,CAAC,cACvC1B,IAAA,CAAC3B,SAAS,EAAAyK,QAAA,CAAE0C,MAAM,CAAC7J,SAAS,CAAY,CAAC,cACzC3B,IAAA,CAAC3B,SAAS,EAAAyK,QAAA,CAAE0C,MAAM,CAAC3J,OAAO,CAAY,CAAC,cACvC7B,IAAA,CAAC3B,SAAS,EAAAyK,QAAA,CAAE0C,MAAM,CAAC1J,YAAY,CAAY,CAAC,cAC5C9B,IAAA,CAAC3B,SAAS,EAAAyK,QAAA,CAAE0C,MAAM,CAACzJ,aAAa,CAAY,CAAC,cAC7C/B,IAAA,CAAC3B,SAAS,EAAAyK,QAAA,CAAE0C,MAAM,CAACC,IAAI,CAAC7G,MAAM,CAAY,CAAC,GAP9B+E,KAQL,CACX,CAAC,CACO,CAAC,EACP,CAAC,CACM,CACjB,CACY,CAAC,cAChB3J,IAAA,CAAC7C,aAAa,EAAA2L,QAAA,cACZ9I,IAAA,CAACtD,MAAM,EAACoO,OAAO,CAAE/E,iBAAkB,CAAA+C,QAAA,CAAC,QAAM,CAAQ,CAAC,CACtC,CAAC,EACV,CAAC,CAEb,CAEA,MAAO,KAAI,CACb,CAAC,CAED,mBACE5I,KAAA,CAAC1D,GAAG,EAAAsM,QAAA,EACDjI,cAAc,GAAK,kBAAkB,EAAI,CAACE,UAAU,cACnDb,KAAA,CAACvD,KAAK,EAACqM,EAAE,CAAE,CAAE0C,CAAC,CAAE,CAAE,CAAE,CAAA5C,QAAA,eAClB9I,IAAA,CAACxD,GAAG,EAACwM,EAAE,CAAE,CAAEoC,OAAO,CAAE,MAAM,CAAEO,cAAc,CAAE,UAAU,CAAEC,UAAU,CAAE,QAAQ,CAAE3C,EAAE,CAAE,CAAE,CAAE,CAAAH,QAAA,cACpF5I,KAAA,CAAC1D,GAAG,EAACwM,EAAE,CAAE,CAAEoC,OAAO,CAAE,MAAM,CAAEE,GAAG,CAAE,CAAE,CAAE,CAAAxC,QAAA,eACnC9I,IAAA,CAACtD,MAAM,EACLyM,OAAO,CAAC,UAAU,CAClB0B,KAAK,CAAC,SAAS,CACfE,SAAS,cAAE/K,IAAA,CAAChB,WAAW,GAAE,CAAE,CAC3B8L,OAAO,CAAEA,CAAA,GAAM,CACbpH,mBAAmB,CAAC,CAAC,CACrBxC,aAAa,CAAC,mBAAmB,CAAC,CAClCF,aAAa,CAAC,IAAI,CAAC,CACrB,CAAE,CACFgI,EAAE,CAAE,CACFI,UAAU,CAAE,QAAQ,CACpByC,YAAY,CAAE,CAChB,CAAE,CAAA/C,QAAA,CACH,kBAED,CAAQ,CAAC,cACT9I,IAAA,CAACtD,MAAM,EACLyM,OAAO,CAAC,WAAW,CACnB0B,KAAK,CAAC,SAAS,CACfE,SAAS,cAAE/K,IAAA,CAACtB,OAAO,GAAE,CAAE,CACvBoM,OAAO,CAAEA,CAAA,GAAM7G,qBAAqB,CAAC,CAAE,CACvC+E,EAAE,CAAE,CACFI,UAAU,CAAE,QAAQ,CACpByC,YAAY,CAAE,CAChB,CAAE,CAAA/C,QAAA,CACH,mBAED,CAAQ,CAAC,EACN,CAAC,CACH,CAAC,CACLrI,OAAO,cACNT,IAAA,CAACxD,GAAG,EAACwM,EAAE,CAAE,CAAEoC,OAAO,CAAE,MAAM,CAAEO,cAAc,CAAE,QAAQ,CAAEG,EAAE,CAAE,CAAE,CAAE,CAAAhD,QAAA,cAC5D9I,IAAA,CAAChC,gBAAgB,GAAE,CAAC,CACjB,CAAC,cAENgC,IAAA,CAACP,qBAAqB,EACpBkB,MAAM,CAAEA,MAAO,CACfF,OAAO,CAAEA,OAAQ,CACjB0H,oBAAoB,CAAGC,YAAY,EAAK3E,OAAO,CAACM,GAAG,CAAC,kBAAkB,CAAEqE,YAAY,CAACxD,MAAM,CAAE,CAC7FyD,MAAM,CAAGpC,MAAM,EAAK,CAClB7E,iBAAiB,CAAC6E,MAAM,CAAC,CACzB/E,aAAa,CAAC,gBAAgB,CAAC,CAC/BF,aAAa,CAAC,IAAI,CAAC,CACrB,CAAE,CACFsH,QAAQ,CAAGrC,MAAM,EAAK,CACpB7E,iBAAiB,CAAC6E,MAAM,CAAC,CACzB/E,aAAa,CAAC,eAAe,CAAC,CAC9BF,aAAa,CAAC,IAAI,CAAC,CACrB,CAAE,CACFuH,aAAa,CAAGtC,MAAM,EAAK,CACzB7E,iBAAiB,CAAC6E,MAAM,CAAC,CACzBvC,mBAAmB,CAAC,CAAC,CACrBxC,aAAa,CAAC,mBAAmB,CAAC,CAClCF,aAAa,CAAC,IAAI,CAAC,CACrB,CAAE,CACFwH,UAAU,CAAGvC,MAAM,EAAK,CACtB7E,iBAAiB,CAAC6E,MAAM,CAAC,CACzB3E,qBAAqB,CAAC,IAAI,CAAC,CAC7B,CAAE,CACH,CACF,EACI,CAAC,CACN,CAACP,UAAU,cACbf,IAAA,CAACrD,KAAK,EAACqM,EAAE,CAAE,CAAE0C,CAAC,CAAE,CAAC,CAAEK,SAAS,CAAE,OAAO,CAAEX,OAAO,CAAE,MAAM,CAAEQ,UAAU,CAAE,QAAQ,CAAED,cAAc,CAAE,QAAS,CAAE,CAAA7C,QAAA,CACtG,CAACjI,cAAc,cACdb,IAAA,CAACvD,UAAU,EAAC0M,OAAO,CAAC,OAAO,CAAAL,QAAA,CAAC,wDAE5B,CAAY,CAAC,cAEb5I,KAAA,CAAC1D,GAAG,EAACwM,EAAE,CAAE,CAAEgD,SAAS,CAAE,QAAS,CAAE,CAAAlD,QAAA,eAC/B5I,KAAA,CAACzD,UAAU,EAAC0M,OAAO,CAAC,IAAI,CAAC8C,YAAY,MAAAnD,QAAA,EAClCjI,cAAc,GAAK,YAAY,EAAI,mBAAmB,CACtDA,cAAc,GAAK,gBAAgB,EAAI,iBAAiB,CACxDA,cAAc,GAAK,eAAe,EAAI,gBAAgB,CACtDA,cAAc,GAAK,mBAAmB,EAAI,6BAA6B,EAC9D,CAAC,cACbb,IAAA,CAAChC,gBAAgB,EAACgL,EAAE,CAAE,CAAEE,EAAE,CAAE,CAAE,CAAE,CAAE,CAAC,EAChC,CACN,CACI,CAAC,CACN,IAAI,CAEPT,YAAY,CAAC,CAAC,cAGfzI,IAAA,CAACR,oBAAoB,EACnBkJ,IAAI,CAAE9F,gBAAiB,CACvB+F,OAAO,CAAEA,CAAA,GAAM9F,mBAAmB,CAAC,KAAK,CAAE,CAC1CqJ,SAAS,CAAEzG,mBAAoB,CAChC,CAAC,cAGFzF,IAAA,CAACN,oBAAoB,EACnBgJ,IAAI,CAAErH,kBAAmB,CACzBsH,OAAO,CAAEA,CAAA,GAAM,CACbrH,qBAAqB,CAAC,KAAK,CAAC,CAC5B;AACA0B,UAAU,CAAC,CAAC,CACd,CAAE,CACFiD,MAAM,CAAE9E,cAAe,CACvBd,UAAU,CAAEA,UAAW,CACvBC,SAAS,CAAEA,SAAU,CACrBC,OAAO,CAAEA,OAAQ,CAClB,CAAC,EACC,CAAC,CAEV,CAAC,CAED,cAAe,CAAAJ,SAAS", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}