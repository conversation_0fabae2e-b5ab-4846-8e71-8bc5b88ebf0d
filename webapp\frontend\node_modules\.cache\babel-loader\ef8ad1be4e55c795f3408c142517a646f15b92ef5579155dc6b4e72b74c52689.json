{"ast": null, "code": "const formatDistanceLocale = {\n  lessThanXSeconds: {\n    one: \"mindre än en sekund\",\n    other: \"mindre än {{count}} sekunder\"\n  },\n  xSeconds: {\n    one: \"en sekund\",\n    other: \"{{count}} sekunder\"\n  },\n  halfAMinute: \"en halv minut\",\n  lessThanXMinutes: {\n    one: \"mindre än en minut\",\n    other: \"mindre än {{count}} minuter\"\n  },\n  xMinutes: {\n    one: \"en minut\",\n    other: \"{{count}} minuter\"\n  },\n  aboutXHours: {\n    one: \"ungefär en timme\",\n    other: \"ungefär {{count}} timmar\"\n  },\n  xHours: {\n    one: \"en timme\",\n    other: \"{{count}} timmar\"\n  },\n  xDays: {\n    one: \"en dag\",\n    other: \"{{count}} dagar\"\n  },\n  aboutXWeeks: {\n    one: \"ungefär en vecka\",\n    other: \"ungefär {{count}} veckor\"\n  },\n  xWeeks: {\n    one: \"en vecka\",\n    other: \"{{count}} veckor\"\n  },\n  aboutXMonths: {\n    one: \"ungefär en månad\",\n    other: \"ungefär {{count}} månader\"\n  },\n  xMonths: {\n    one: \"en månad\",\n    other: \"{{count}} månader\"\n  },\n  aboutXYears: {\n    one: \"ungefär ett år\",\n    other: \"ungefär {{count}} år\"\n  },\n  xYears: {\n    one: \"ett år\",\n    other: \"{{count}} år\"\n  },\n  overXYears: {\n    one: \"över ett år\",\n    other: \"över {{count}} år\"\n  },\n  almostXYears: {\n    one: \"nästan ett år\",\n    other: \"nästan {{count}} år\"\n  }\n};\nconst wordMapping = [\"noll\", \"en\", \"två\", \"tre\", \"fyra\", \"fem\", \"sex\", \"sju\", \"åtta\", \"nio\", \"tio\", \"elva\", \"tolv\"];\nexport const formatDistance = (token, count, options) => {\n  let result;\n  const tokenValue = formatDistanceLocale[token];\n  if (typeof tokenValue === \"string\") {\n    result = tokenValue;\n  } else if (count === 1) {\n    result = tokenValue.one;\n  } else {\n    result = tokenValue.other.replace(\"{{count}}\", count < 13 ? wordMapping[count] : String(count));\n  }\n  if (options?.addSuffix) {\n    if (options.comparison && options.comparison > 0) {\n      return \"om \" + result;\n    } else {\n      return result + \" sedan\";\n    }\n  }\n  return result;\n};", "map": {"version": 3, "names": ["formatDistanceLocale", "lessThanXSeconds", "one", "other", "xSeconds", "halfAMinute", "lessThanXMinutes", "xMinutes", "aboutXHours", "xHours", "xDays", "aboutXWeeks", "xWeeks", "aboutXMonths", "xMonths", "aboutXYears", "xYears", "overXYears", "almostXYears", "wordMapping", "formatDistance", "token", "count", "options", "result", "tokenValue", "replace", "String", "addSuffix", "comparison"], "sources": ["C:/CMS/webapp/frontend/node_modules/date-fns/locale/sv/_lib/formatDistance.mjs"], "sourcesContent": ["const formatDistanceLocale = {\n  lessThanXSeconds: {\n    one: \"mindre än en sekund\",\n    other: \"mindre än {{count}} sekunder\",\n  },\n\n  xSeconds: {\n    one: \"en sekund\",\n    other: \"{{count}} sekunder\",\n  },\n\n  halfAMinute: \"en halv minut\",\n\n  lessThanXMinutes: {\n    one: \"mindre än en minut\",\n    other: \"mindre än {{count}} minuter\",\n  },\n\n  xMinutes: {\n    one: \"en minut\",\n    other: \"{{count}} minuter\",\n  },\n\n  aboutXHours: {\n    one: \"ungefär en timme\",\n    other: \"ungefär {{count}} timmar\",\n  },\n\n  xHours: {\n    one: \"en timme\",\n    other: \"{{count}} timmar\",\n  },\n\n  xDays: {\n    one: \"en dag\",\n    other: \"{{count}} dagar\",\n  },\n\n  aboutXWeeks: {\n    one: \"ungefär en vecka\",\n    other: \"ungefär {{count}} veckor\",\n  },\n\n  xWeeks: {\n    one: \"en vecka\",\n    other: \"{{count}} veckor\",\n  },\n\n  aboutXMonths: {\n    one: \"ungefär en månad\",\n    other: \"ungefär {{count}} månader\",\n  },\n\n  xMonths: {\n    one: \"en månad\",\n    other: \"{{count}} månader\",\n  },\n\n  aboutXYears: {\n    one: \"ungefär ett år\",\n    other: \"ungefär {{count}} år\",\n  },\n\n  xYears: {\n    one: \"ett år\",\n    other: \"{{count}} år\",\n  },\n\n  overXYears: {\n    one: \"över ett år\",\n    other: \"över {{count}} år\",\n  },\n\n  almostXYears: {\n    one: \"nästan ett år\",\n    other: \"nästan {{count}} år\",\n  },\n};\n\nconst wordMapping = [\n  \"noll\",\n  \"en\",\n  \"två\",\n  \"tre\",\n  \"fyra\",\n  \"fem\",\n  \"sex\",\n  \"sju\",\n  \"åtta\",\n  \"nio\",\n  \"tio\",\n  \"elva\",\n  \"tolv\",\n];\n\nexport const formatDistance = (token, count, options) => {\n  let result;\n\n  const tokenValue = formatDistanceLocale[token];\n  if (typeof tokenValue === \"string\") {\n    result = tokenValue;\n  } else if (count === 1) {\n    result = tokenValue.one;\n  } else {\n    result = tokenValue.other.replace(\n      \"{{count}}\",\n      count < 13 ? wordMapping[count] : String(count),\n    );\n  }\n\n  if (options?.addSuffix) {\n    if (options.comparison && options.comparison > 0) {\n      return \"om \" + result;\n    } else {\n      return result + \" sedan\";\n    }\n  }\n\n  return result;\n};\n"], "mappings": "AAAA,MAAMA,oBAAoB,GAAG;EAC3BC,gBAAgB,EAAE;IAChBC,GAAG,EAAE,qBAAqB;IAC1BC,KAAK,EAAE;EACT,CAAC;EAEDC,QAAQ,EAAE;IACRF,GAAG,EAAE,WAAW;IAChBC,KAAK,EAAE;EACT,CAAC;EAEDE,WAAW,EAAE,eAAe;EAE5BC,gBAAgB,EAAE;IAChBJ,GAAG,EAAE,oBAAoB;IACzBC,KAAK,EAAE;EACT,CAAC;EAEDI,QAAQ,EAAE;IACRL,GAAG,EAAE,UAAU;IACfC,KAAK,EAAE;EACT,CAAC;EAEDK,WAAW,EAAE;IACXN,GAAG,EAAE,kBAAkB;IACvBC,KAAK,EAAE;EACT,CAAC;EAEDM,MAAM,EAAE;IACNP,GAAG,EAAE,UAAU;IACfC,KAAK,EAAE;EACT,CAAC;EAEDO,KAAK,EAAE;IACLR,GAAG,EAAE,QAAQ;IACbC,KAAK,EAAE;EACT,CAAC;EAEDQ,WAAW,EAAE;IACXT,GAAG,EAAE,kBAAkB;IACvBC,KAAK,EAAE;EACT,CAAC;EAEDS,MAAM,EAAE;IACNV,GAAG,EAAE,UAAU;IACfC,KAAK,EAAE;EACT,CAAC;EAEDU,YAAY,EAAE;IACZX,GAAG,EAAE,kBAAkB;IACvBC,KAAK,EAAE;EACT,CAAC;EAEDW,OAAO,EAAE;IACPZ,GAAG,EAAE,UAAU;IACfC,KAAK,EAAE;EACT,CAAC;EAEDY,WAAW,EAAE;IACXb,GAAG,EAAE,gBAAgB;IACrBC,KAAK,EAAE;EACT,CAAC;EAEDa,MAAM,EAAE;IACNd,GAAG,EAAE,QAAQ;IACbC,KAAK,EAAE;EACT,CAAC;EAEDc,UAAU,EAAE;IACVf,GAAG,EAAE,aAAa;IAClBC,KAAK,EAAE;EACT,CAAC;EAEDe,YAAY,EAAE;IACZhB,GAAG,EAAE,eAAe;IACpBC,KAAK,EAAE;EACT;AACF,CAAC;AAED,MAAMgB,WAAW,GAAG,CAClB,MAAM,EACN,IAAI,EACJ,KAAK,EACL,KAAK,EACL,MAAM,EACN,KAAK,EACL,KAAK,EACL,KAAK,EACL,MAAM,EACN,KAAK,EACL,KAAK,EACL,MAAM,EACN,MAAM,CACP;AAED,OAAO,MAAMC,cAAc,GAAGA,CAACC,KAAK,EAAEC,KAAK,EAAEC,OAAO,KAAK;EACvD,IAAIC,MAAM;EAEV,MAAMC,UAAU,GAAGzB,oBAAoB,CAACqB,KAAK,CAAC;EAC9C,IAAI,OAAOI,UAAU,KAAK,QAAQ,EAAE;IAClCD,MAAM,GAAGC,UAAU;EACrB,CAAC,MAAM,IAAIH,KAAK,KAAK,CAAC,EAAE;IACtBE,MAAM,GAAGC,UAAU,CAACvB,GAAG;EACzB,CAAC,MAAM;IACLsB,MAAM,GAAGC,UAAU,CAACtB,KAAK,CAACuB,OAAO,CAC/B,WAAW,EACXJ,KAAK,GAAG,EAAE,GAAGH,WAAW,CAACG,KAAK,CAAC,GAAGK,MAAM,CAACL,KAAK,CAChD,CAAC;EACH;EAEA,IAAIC,OAAO,EAAEK,SAAS,EAAE;IACtB,IAAIL,OAAO,CAACM,UAAU,IAAIN,OAAO,CAACM,UAAU,GAAG,CAAC,EAAE;MAChD,OAAO,KAAK,GAAGL,MAAM;IACvB,CAAC,MAAM;MACL,OAAOA,MAAM,GAAG,QAAQ;IAC1B;EACF;EAEA,OAAOA,MAAM;AACf,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}