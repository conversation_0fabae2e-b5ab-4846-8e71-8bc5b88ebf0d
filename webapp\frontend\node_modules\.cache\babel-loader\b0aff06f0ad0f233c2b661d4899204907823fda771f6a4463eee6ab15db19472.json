{"ast": null, "code": "import { formatDistance } from \"./ja-<PERSON>ra/_lib/formatDistance.js\";\nimport { formatLong } from \"./ja-Hira/_lib/formatLong.js\";\nimport { formatRelative } from \"./ja-Hira/_lib/formatRelative.js\";\nimport { localize } from \"./ja-Hira/_lib/localize.js\";\nimport { match } from \"./ja-Hira/_lib/match.js\";\n\n/**\n * @category Locales\n * @summary Japanese (Hiragana) locale.\n * @language Japanese (Hiragana)\n * @iso-639-2 jpn\n * <AUTHOR> [@Eritutteo](https://github.com/Eritutteo)\n */\nexport const jaHira = {\n  code: \"ja-<PERSON>ra\",\n  formatDistance: formatDistance,\n  formatLong: formatLong,\n  formatRelative: formatRelative,\n  localize: localize,\n  match: match,\n  options: {\n    weekStartsOn: 0 /* Sunday */,\n    firstWeekContainsDate: 1\n  }\n};\n\n// Fallback for modularized imports:\nexport default jaHira;", "map": {"version": 3, "names": ["formatDistance", "formatLong", "formatRelative", "localize", "match", "j<PERSON><PERSON><PERSON>", "code", "options", "weekStartsOn", "firstWeekContainsDate"], "sources": ["C:/Users/<USER>/PycharmProjects/CMS/webapp/frontend/node_modules/date-fns/locale/ja-Hira.js"], "sourcesContent": ["import { formatDistance } from \"./ja-<PERSON>ra/_lib/formatDistance.js\";\nimport { formatLong } from \"./ja-Hira/_lib/formatLong.js\";\nimport { formatRelative } from \"./ja-Hira/_lib/formatRelative.js\";\nimport { localize } from \"./ja-Hira/_lib/localize.js\";\nimport { match } from \"./ja-Hira/_lib/match.js\";\n\n/**\n * @category Locales\n * @summary Japanese (Hiragana) locale.\n * @language Japanese (Hiragana)\n * @iso-639-2 jpn\n * <AUTHOR> [@Eritutteo](https://github.com/Eritutteo)\n */\nexport const jaHira = {\n  code: \"ja-<PERSON>ra\",\n  formatDistance: formatDistance,\n  formatLong: formatLong,\n  formatRelative: formatRelative,\n  localize: localize,\n  match: match,\n  options: {\n    weekStartsOn: 0 /* Sunday */,\n    firstWeekContainsDate: 1,\n  },\n};\n\n// Fallback for modularized imports:\nexport default jaHira;\n"], "mappings": "AAAA,SAASA,cAAc,QAAQ,kCAAkC;AACjE,SAASC,UAAU,QAAQ,8BAA8B;AACzD,SAASC,cAAc,QAAQ,kCAAkC;AACjE,SAASC,QAAQ,QAAQ,4BAA4B;AACrD,SAASC,KAAK,QAAQ,yBAAyB;;AAE/C;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO,MAAMC,MAAM,GAAG;EACpBC,IAAI,EAAE,SAAS;EACfN,cAAc,EAAEA,cAAc;EAC9BC,UAAU,EAAEA,UAAU;EACtBC,cAAc,EAAEA,cAAc;EAC9BC,QAAQ,EAAEA,QAAQ;EAClBC,KAAK,EAAEA,KAAK;EACZG,OAAO,EAAE;IACPC,YAAY,EAAE,CAAC,CAAC;IAChBC,qBAAqB,EAAE;EACzB;AACF,CAAC;;AAED;AACA,eAAeJ,MAAM", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}