'use client'

import { useAuth } from '@/contexts/AuthContext'
import { useCantiere } from '@/hooks/useCantiere'
import CertificazioniManager from '@/components/certificazioni/CertificazioniManager'
import { Alert, AlertDescription } from '@/components/ui/alert'
import { AlertCircle } from 'lucide-react'

export default function CertificazioniPage() {
  const { user } = useAuth()
  const { cantiereId, isLoading, error } = useCantiere()

  if (isLoading) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-slate-50 to-slate-100 p-6">
        <div className="max-w-[90%] mx-auto">
          <div className="text-center py-8">
            <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto"></div>
            <p className="mt-2 text-gray-600">Caricamento cantiere...</p>
          </div>
        </div>
      </div>
    )
  }

  if (error || !cantiereId) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-slate-50 to-slate-100 p-6">
        <div className="max-w-[90%] mx-auto">
          <Alert className="border-red-200 bg-red-50">
            <AlertCircle className="h-4 w-4 text-red-600" />
            <AlertDescription className="text-red-800">
              {error || 'Cantiere non selezionato. Seleziona un cantiere per accedere alle certificazioni.'}
            </AlertDescription>
          </Alert>
        </div>
      </div>
    )
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-slate-50 to-slate-100 p-6">
      <div className="max-w-[95%] mx-auto">
        <CertificazioniManager cantiereId={cantiereId} />
      </div>
    </div>
  )
}
