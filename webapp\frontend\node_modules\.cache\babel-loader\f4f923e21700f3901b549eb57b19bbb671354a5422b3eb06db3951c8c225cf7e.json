{"ast": null, "code": "/**\n * Utility per la validazione dei campi delle bobine\n * Implementa le stesse regole di validazione della CLI\n */\n\n// Costanti\nexport const TBD = \"TBD\";\n\n/**\n * Verifica se un valore è vuoto\n * @param {string|number|null} value - Valore da verificare\n * @returns {boolean} - True se il valore è vuoto, false altrimenti\n */\nexport const isEmpty = value => {\n  return value === null || value === undefined || typeof value === 'string' && value.trim() === '' || typeof value === 'number' && isNaN(value);\n};\n\n/**\n * Converte un valore in float\n * @param {string|number|null} value - Valore da convertire\n * @returns {number} - Valore convertito in float\n */\nexport const convertToFloat = value => {\n  if (isEmpty(value)) return 0;\n  if (typeof value === 'number') return value;\n  if (typeof value === 'string') {\n    const normalized = value.trim().replace(',', '.');\n    try {\n      return parseFloat(normalized);\n    } catch (e) {\n      console.warn(`Errore conversione float: '${value}' non è un numero valido.`);\n      return 0;\n    }\n  }\n  console.warn(`Tipo di valore non supportato: ${typeof value}`);\n  return 0;\n};\n\n/**\n * Valida un campo numerico\n * @param {string|number} value - Valore da validare\n * @param {string} fieldName - Nome del campo\n * @returns {Object} - Risultato della validazione\n */\nexport const validateNumber = (value, fieldName) => {\n  try {\n    // Gestione campi vuoti\n    if (isEmpty(value)) {\n      if (fieldName === \"Numero conduttori\") {\n        // Per numero conduttori, accetta vuoto e imposta a 0\n        return {\n          valid: true,\n          message: \"\",\n          value: \"0\"\n        };\n      }\n      if (fieldName === \"Formazione\") {\n        // Per formazione, accetta vuoto e imposta a 0\n        return {\n          valid: true,\n          message: \"\",\n          value: \"0\"\n        };\n      }\n      return {\n        valid: false,\n        message: `${fieldName} non può essere vuoto`,\n        value: null\n      };\n    }\n\n    // Normalizzazione input se è stringa\n    let normalizedValue = value;\n    if (typeof value === 'string') {\n      normalizedValue = value.trim().replace(',', '.');\n      if (normalizedValue === '.') {\n        return {\n          valid: false,\n          message: `${fieldName} non valido`,\n          value: null\n        };\n      }\n    }\n\n    // Per Numero conduttori e Formazione, accetta anche valori non numerici\n    if (fieldName === \"Numero conduttori\" || fieldName === \"Formazione\") {\n      // Accetta qualsiasi valore come stringa\n      const stringValue = normalizedValue.toString();\n\n      // Se è un numero, verifica i limiti\n      if (!isNaN(parseFloat(normalizedValue))) {\n        const numero = parseFloat(normalizedValue);\n\n        // Validazione numero negativo\n        if (numero < 0) {\n          return {\n            valid: false,\n            message: `${fieldName} non può essere negativo`,\n            value: null\n          };\n        }\n\n        // Validazione limiti specifici\n        if (fieldName === \"Numero conduttori\" && numero > 24) {\n          return {\n            valid: true,\n            message: `ATTENZIONE: Il numero di conduttori (${numero}) supera il limite standard di 24`,\n            value: stringValue,\n            warning: true\n          };\n        }\n        if (fieldName === \"Formazione\" && numero > 1000) {\n          return {\n            valid: true,\n            message: `ATTENZIONE: La formazione (${numero}) supera il limite standard di 1000`,\n            value: stringValue,\n            warning: true\n          };\n        }\n      }\n      return {\n        valid: true,\n        message: \"\",\n        value: stringValue\n      };\n    } else {\n      // Per altri campi numerici, mantieni la validazione originale\n      const numero = parseFloat(normalizedValue);\n\n      // Validazione numero negativo\n      if (numero < 0) {\n        return {\n          valid: false,\n          message: `${fieldName} non può essere negativo`,\n          value: null\n        };\n      }\n      return {\n        valid: true,\n        message: \"\",\n        value: numero.toString()\n      };\n    }\n  } catch (e) {\n    return {\n      valid: false,\n      message: `Il valore inserito per ${fieldName} non è un numero valido`,\n      value: null\n    };\n  }\n};\n\n/**\n * Valida i metri totali\n * @param {string|number} value - Valore da validare\n * @returns {Object} - Risultato della validazione\n */\nexport const validateMetriTotali = value => {\n  try {\n    if (isEmpty(value)) {\n      return {\n        valid: false,\n        message: \"I metri totali sono obbligatori\",\n        value: null\n      };\n    }\n    const val = convertToFloat(value);\n    if (val <= 0) {\n      return {\n        valid: false,\n        message: \"I metri totali devono essere maggiori di zero\",\n        value: null\n      };\n    }\n    if (val > 100000) {\n      // 100km come limite ragionevole\n      return {\n        valid: false,\n        message: \"I metri totali non possono superare 100.000\",\n        value: null\n      };\n    }\n    return {\n      valid: true,\n      message: \"Valore valido\",\n      value: val\n    };\n  } catch (e) {\n    return {\n      valid: false,\n      message: \"Il valore deve essere un numero valido\",\n      value: null\n    };\n  }\n};\n\n/**\n * Valida un campo di testo base\n * @param {string} value - Valore da validare\n * @returns {Object} - Risultato della validazione\n */\nexport const validateBaseField = value => {\n  if (isEmpty(value)) {\n    return {\n      valid: true,\n      message: \"Campo vuoto\",\n      value: TBD\n    };\n  }\n  return {\n    valid: true,\n    message: \"Campo valido\",\n    value: value.trim()\n  };\n};\n\n/**\n * Valida un campo di testo obbligatorio\n * @param {string} value - Valore da validare\n * @returns {Object} - Risultato della validazione\n */\nexport const validateRequiredTextField = value => {\n  if (isEmpty(value)) {\n    return {\n      valid: false,\n      message: \"Il campo non può essere vuoto\",\n      value: null\n    };\n  }\n  return {\n    valid: true,\n    message: \"Campo valido\",\n    value: value.trim()\n  };\n};\n\n/**\n * Valida un campo in base al suo tipo\n * @param {string} fieldName - Nome del campo\n * @param {string|number} value - Valore da validare\n * @returns {Object} - Risultato della validazione\n */\nexport const validateBobinaField = (fieldName, value) => {\n  // Campi che richiedono validazione speciale\n  const specialValidations = {\n    'metri_totali': () => validateMetriTotali(value),\n    'n_conduttori': () => validateNumber(value, \"Numero conduttori\"),\n    'sezione': () => validateNumber(value, \"Formazione\")\n  };\n\n  // Campi obbligatori\n  const requiredFields = ['utility', 'tipologia', 'sezione', 'metri_totali'];\n\n  // Campi che possono avere \"TBD\" come valore predefinito quando vuoti\n  const tbdFields = ['ubicazione_bobina', 'fornitore', 'n_DDT'];\n\n  // Se il campo richiede validazione speciale, usala\n  if (fieldName in specialValidations) {\n    return specialValidations[fieldName]();\n  }\n\n  // Se il campo è obbligatorio\n  if (requiredFields.includes(fieldName)) {\n    return validateRequiredTextField(value);\n  }\n\n  // Se il campo può avere TBD come valore predefinito\n  if (tbdFields.includes(fieldName)) {\n    return validateBaseField(value);\n  }\n\n  // Per tutti gli altri campi, usa la validazione base\n  return validateBaseField(value);\n};\n\n/**\n * Valida l'ID della bobina\n * @param {string} value - ID della bobina\n * @returns {Object} - Risultato della validazione\n */\nexport const validateBobinaId = value => {\n  // Gestione dei valori nulli o vuoti\n  if (isEmpty(value)) {\n    return {\n      valid: false,\n      message: \"L'ID della bobina è obbligatorio\",\n      value: null\n    };\n  }\n\n  // Converti sempre in stringa e rimuovi spazi iniziali e finali\n  const stringValue = String(value).trim();\n\n  // Verifica che l'ID non sia vuoto dopo il trim\n  if (stringValue === '') {\n    return {\n      valid: false,\n      message: \"L'ID della bobina è obbligatorio\",\n      value: null\n    };\n  }\n\n  // Verifica che l'ID non contenga caratteri speciali non consentiti\n  const invalidChars = /[\\s\\\\/:*?\"<>|]/;\n  if (invalidChars.test(stringValue)) {\n    return {\n      valid: false,\n      message: \"L'ID della bobina non può contenere spazi o caratteri speciali come \\\\ / : * ? \\\" < > |\",\n      value: null\n    };\n  }\n\n  // Verifica che l'ID non sia troppo lungo\n  if (stringValue.length > 50) {\n    return {\n      valid: false,\n      message: \"L'ID della bobina non può superare i 50 caratteri\",\n      value: null\n    };\n  }\n  return {\n    valid: true,\n    message: \"\",\n    value: stringValue\n  };\n};\n\n/**\n * Valida tutti i campi di una bobina\n * @param {Object} bobinaData - Dati della bobina\n * @returns {Object} - Risultato della validazione\n */\nexport const validateBobinaData = bobinaData => {\n  const errors = {};\n  const warnings = {};\n  const validatedData = {\n    ...bobinaData\n  };\n\n  // Assicurati che configurazione sia impostata\n  validatedData.configurazione = validatedData.configurazione || 's';\n\n  // Campi da validare\n  const fieldsToValidate = ['utility', 'tipologia', 'sezione', 'metri_totali', 'ubicazione_bobina', 'fornitore', 'n_DDT'];\n\n  // Validazione speciale per numero_bobina (parte dell'ID_BOBINA)\n  if (validatedData.configurazione === 'n') {\n    // In modalità manuale, verifica che il campo numero_bobina sia presente e non vuoto\n    if (!validatedData.numero_bobina || String(validatedData.numero_bobina).trim() === '') {\n      errors.numero_bobina = \"L'ID della bobina è obbligatorio\";\n    } else {\n      const idResult = validateBobinaId(validatedData.numero_bobina);\n      if (!idResult.valid) {\n        errors.numero_bobina = idResult.message;\n      } else {\n        validatedData.numero_bobina = idResult.value;\n      }\n    }\n  } else if (validatedData.configurazione === 's') {\n    // In modalità automatica, il numero_bobina dovrebbe essere già impostato\n    if (!validatedData.numero_bobina || String(validatedData.numero_bobina).trim() === '') {\n      // Se per qualche motivo non è impostato, imposta un valore di default\n      validatedData.numero_bobina = '1';\n    } else {\n      // Assicurati che sia una stringa\n      validatedData.numero_bobina = String(validatedData.numero_bobina).trim();\n    }\n  }\n\n  // Validazione degli altri campi\n  for (const field of fieldsToValidate) {\n    // Assicurati che il campo esista nell'oggetto\n    if (validatedData[field] === undefined) {\n      if (field === 'metri_totali') {\n        errors[field] = \"I metri totali sono obbligatori\";\n        continue;\n      } else if (['utility', 'tipologia'].includes(field)) {\n        errors[field] = `Il campo ${field} è obbligatorio`;\n        continue;\n      } else if (field === 'n_conduttori') {\n        // Imposta un valore di default per n_conduttori\n        validatedData[field] = '0';\n        continue;\n      } else if (field === 'sezione') {\n        // Imposta un valore di default per sezione\n        validatedData[field] = '0';\n        continue;\n      } else {\n        // Per i campi non obbligatori, imposta un valore di default\n        validatedData[field] = field === 'data_DDT' ? null : 'TBD';\n        continue;\n      }\n    }\n    const result = validateBobinaField(field, validatedData[field]);\n    if (!result.valid) {\n      errors[field] = result.message;\n    } else {\n      validatedData[field] = result.value;\n      if (result.warning) {\n        warnings[field] = result.message;\n      }\n    }\n  }\n\n  // Assicurati che i campi numerici siano numeri\n  if (!errors.metri_totali) {\n    validatedData.metri_totali = parseFloat(validatedData.metri_totali) || 0;\n    if (validatedData.metri_totali <= 0) {\n      errors.metri_totali = \"I metri totali devono essere maggiori di zero\";\n    }\n  }\n\n  // Assicurati che n_conduttori sia una stringa\n  validatedData.n_conduttori = validatedData.n_conduttori !== undefined && validatedData.n_conduttori !== null ? validatedData.n_conduttori.toString() : \"0\";\n  // Se è vuoto, imposta a \"0\"\n  if (validatedData.n_conduttori === \"\") validatedData.n_conduttori = \"0\";\n\n  // Assicurati che sezione sia una stringa\n  validatedData.sezione = validatedData.sezione !== undefined && validatedData.sezione !== null ? validatedData.sezione.toString() : \"0\";\n  // Se è vuoto, imposta a \"0\"\n  if (validatedData.sezione === \"\") validatedData.sezione = \"0\";\n\n  // Log dei dati validati\n  console.log('Dati validati:', validatedData);\n  console.log('Errori di validazione:', errors);\n  console.log('Avvisi di validazione:', warnings);\n  return {\n    isValid: Object.keys(errors).length === 0,\n    errors,\n    warnings,\n    validatedData\n  };\n};", "map": {"version": 3, "names": ["TBD", "isEmpty", "value", "undefined", "trim", "isNaN", "convertToFloat", "normalized", "replace", "parseFloat", "e", "console", "warn", "validateNumber", "fieldName", "valid", "message", "normalizedValue", "stringValue", "toString", "numero", "warning", "validate<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "val", "validateBaseField", "validateRequiredTextField", "validateBob<PERSON>F<PERSON>", "specialValidations", "metri_totali", "n_conduttori", "sezione", "requiredFields", "tbdFields", "includes", "validateBobinaId", "String", "invalid<PERSON>hars", "test", "length", "validateBobinaData", "bobina<PERSON><PERSON>", "errors", "warnings", "validatedData", "configurazione", "fieldsToValidate", "numero_bobina", "idResult", "field", "result", "log", "<PERSON><PERSON><PERSON><PERSON>", "Object", "keys"], "sources": ["C:/Users/<USER>/PycharmProjects/CMS/webapp/frontend/src/utils/bobinaValidationUtils.js"], "sourcesContent": ["/**\n * Utility per la validazione dei campi delle bobine\n * Implementa le stesse regole di validazione della CLI\n */\n\n// Costanti\nexport const TBD = \"TBD\";\n\n/**\n * Verifica se un valore è vuoto\n * @param {string|number|null} value - Valore da verificare\n * @returns {boolean} - True se il valore è vuoto, false altrimenti\n */\nexport const isEmpty = (value) => {\n  return value === null || value === undefined ||\n         (typeof value === 'string' && value.trim() === '') ||\n         (typeof value === 'number' && isNaN(value));\n};\n\n/**\n * Converte un valore in float\n * @param {string|number|null} value - Valore da convertire\n * @returns {number} - Valore convertito in float\n */\nexport const convertToFloat = (value) => {\n  if (isEmpty(value)) return 0;\n\n  if (typeof value === 'number') return value;\n\n  if (typeof value === 'string') {\n    const normalized = value.trim().replace(',', '.');\n    try {\n      return parseFloat(normalized);\n    } catch (e) {\n      console.warn(`Errore conversione float: '${value}' non è un numero valido.`);\n      return 0;\n    }\n  }\n\n  console.warn(`Tipo di valore non supportato: ${typeof value}`);\n  return 0;\n};\n\n/**\n * Valida un campo numerico\n * @param {string|number} value - Valore da validare\n * @param {string} fieldName - Nome del campo\n * @returns {Object} - Risultato della validazione\n */\nexport const validateNumber = (value, fieldName) => {\n  try {\n    // Gestione campi vuoti\n    if (isEmpty(value)) {\n      if (fieldName === \"Numero conduttori\") {\n        // Per numero conduttori, accetta vuoto e imposta a 0\n        return { valid: true, message: \"\", value: \"0\" };\n      }\n      if (fieldName === \"Formazione\") {\n        // Per formazione, accetta vuoto e imposta a 0\n        return { valid: true, message: \"\", value: \"0\" };\n      }\n      return { valid: false, message: `${fieldName} non può essere vuoto`, value: null };\n    }\n\n    // Normalizzazione input se è stringa\n    let normalizedValue = value;\n    if (typeof value === 'string') {\n      normalizedValue = value.trim().replace(',', '.');\n      if (normalizedValue === '.') {\n        return { valid: false, message: `${fieldName} non valido`, value: null };\n      }\n    }\n\n    // Per Numero conduttori e Formazione, accetta anche valori non numerici\n    if (fieldName === \"Numero conduttori\" || fieldName === \"Formazione\") {\n      // Accetta qualsiasi valore come stringa\n      const stringValue = normalizedValue.toString();\n\n      // Se è un numero, verifica i limiti\n      if (!isNaN(parseFloat(normalizedValue))) {\n        const numero = parseFloat(normalizedValue);\n\n        // Validazione numero negativo\n        if (numero < 0) {\n          return { valid: false, message: `${fieldName} non può essere negativo`, value: null };\n        }\n\n        // Validazione limiti specifici\n        if (fieldName === \"Numero conduttori\" && numero > 24) {\n          return {\n            valid: true,\n            message: `ATTENZIONE: Il numero di conduttori (${numero}) supera il limite standard di 24`,\n            value: stringValue,\n            warning: true\n          };\n        }\n\n        if (fieldName === \"Formazione\" && numero > 1000) {\n          return {\n            valid: true,\n            message: `ATTENZIONE: La formazione (${numero}) supera il limite standard di 1000`,\n            value: stringValue,\n            warning: true\n          };\n        }\n      }\n\n      return { valid: true, message: \"\", value: stringValue };\n    } else {\n      // Per altri campi numerici, mantieni la validazione originale\n      const numero = parseFloat(normalizedValue);\n\n      // Validazione numero negativo\n      if (numero < 0) {\n        return { valid: false, message: `${fieldName} non può essere negativo`, value: null };\n      }\n\n      return { valid: true, message: \"\", value: numero.toString() };\n    }\n  } catch (e) {\n    return { valid: false, message: `Il valore inserito per ${fieldName} non è un numero valido`, value: null };\n  }\n};\n\n/**\n * Valida i metri totali\n * @param {string|number} value - Valore da validare\n * @returns {Object} - Risultato della validazione\n */\nexport const validateMetriTotali = (value) => {\n  try {\n    if (isEmpty(value)) {\n      return { valid: false, message: \"I metri totali sono obbligatori\", value: null };\n    }\n\n    const val = convertToFloat(value);\n\n    if (val <= 0) {\n      return { valid: false, message: \"I metri totali devono essere maggiori di zero\", value: null };\n    }\n\n    if (val > 100000) {  // 100km come limite ragionevole\n      return { valid: false, message: \"I metri totali non possono superare 100.000\", value: null };\n    }\n\n    return { valid: true, message: \"Valore valido\", value: val };\n  } catch (e) {\n    return { valid: false, message: \"Il valore deve essere un numero valido\", value: null };\n  }\n};\n\n/**\n * Valida un campo di testo base\n * @param {string} value - Valore da validare\n * @returns {Object} - Risultato della validazione\n */\nexport const validateBaseField = (value) => {\n  if (isEmpty(value)) {\n    return { valid: true, message: \"Campo vuoto\", value: TBD };\n  }\n  return { valid: true, message: \"Campo valido\", value: value.trim() };\n};\n\n/**\n * Valida un campo di testo obbligatorio\n * @param {string} value - Valore da validare\n * @returns {Object} - Risultato della validazione\n */\nexport const validateRequiredTextField = (value) => {\n  if (isEmpty(value)) {\n    return { valid: false, message: \"Il campo non può essere vuoto\", value: null };\n  }\n  return { valid: true, message: \"Campo valido\", value: value.trim() };\n};\n\n/**\n * Valida un campo in base al suo tipo\n * @param {string} fieldName - Nome del campo\n * @param {string|number} value - Valore da validare\n * @returns {Object} - Risultato della validazione\n */\nexport const validateBobinaField = (fieldName, value) => {\n  // Campi che richiedono validazione speciale\n  const specialValidations = {\n    'metri_totali': () => validateMetriTotali(value),\n    'n_conduttori': () => validateNumber(value, \"Numero conduttori\"),\n    'sezione': () => validateNumber(value, \"Formazione\"),\n  };\n\n  // Campi obbligatori\n  const requiredFields = [\n    'utility', 'tipologia', 'sezione', 'metri_totali'\n  ];\n\n  // Campi che possono avere \"TBD\" come valore predefinito quando vuoti\n  const tbdFields = [\n    'ubicazione_bobina', 'fornitore', 'n_DDT'\n  ];\n\n  // Se il campo richiede validazione speciale, usala\n  if (fieldName in specialValidations) {\n    return specialValidations[fieldName]();\n  }\n\n  // Se il campo è obbligatorio\n  if (requiredFields.includes(fieldName)) {\n    return validateRequiredTextField(value);\n  }\n\n  // Se il campo può avere TBD come valore predefinito\n  if (tbdFields.includes(fieldName)) {\n    return validateBaseField(value);\n  }\n\n  // Per tutti gli altri campi, usa la validazione base\n  return validateBaseField(value);\n};\n\n/**\n * Valida l'ID della bobina\n * @param {string} value - ID della bobina\n * @returns {Object} - Risultato della validazione\n */\nexport const validateBobinaId = (value) => {\n  // Gestione dei valori nulli o vuoti\n  if (isEmpty(value)) {\n    return { valid: false, message: \"L'ID della bobina è obbligatorio\", value: null };\n  }\n\n  // Converti sempre in stringa e rimuovi spazi iniziali e finali\n  const stringValue = String(value).trim();\n\n  // Verifica che l'ID non sia vuoto dopo il trim\n  if (stringValue === '') {\n    return { valid: false, message: \"L'ID della bobina è obbligatorio\", value: null };\n  }\n\n  // Verifica che l'ID non contenga caratteri speciali non consentiti\n  const invalidChars = /[\\s\\\\/:*?\"<>|]/;\n  if (invalidChars.test(stringValue)) {\n    return {\n      valid: false,\n      message: \"L'ID della bobina non può contenere spazi o caratteri speciali come \\\\ / : * ? \\\" < > |\",\n      value: null\n    };\n  }\n\n  // Verifica che l'ID non sia troppo lungo\n  if (stringValue.length > 50) {\n    return {\n      valid: false,\n      message: \"L'ID della bobina non può superare i 50 caratteri\",\n      value: null\n    };\n  }\n\n  return { valid: true, message: \"\", value: stringValue };\n};\n\n/**\n * Valida tutti i campi di una bobina\n * @param {Object} bobinaData - Dati della bobina\n * @returns {Object} - Risultato della validazione\n */\nexport const validateBobinaData = (bobinaData) => {\n  const errors = {};\n  const warnings = {};\n  const validatedData = { ...bobinaData };\n\n  // Assicurati che configurazione sia impostata\n  validatedData.configurazione = validatedData.configurazione || 's';\n\n  // Campi da validare\n  const fieldsToValidate = [\n    'utility', 'tipologia',\n    'sezione', 'metri_totali', 'ubicazione_bobina', 'fornitore', 'n_DDT'\n  ];\n\n  // Validazione speciale per numero_bobina (parte dell'ID_BOBINA)\n  if (validatedData.configurazione === 'n') {\n    // In modalità manuale, verifica che il campo numero_bobina sia presente e non vuoto\n    if (!validatedData.numero_bobina || String(validatedData.numero_bobina).trim() === '') {\n      errors.numero_bobina = \"L'ID della bobina è obbligatorio\";\n    } else {\n      const idResult = validateBobinaId(validatedData.numero_bobina);\n      if (!idResult.valid) {\n        errors.numero_bobina = idResult.message;\n      } else {\n        validatedData.numero_bobina = idResult.value;\n      }\n    }\n  } else if (validatedData.configurazione === 's') {\n    // In modalità automatica, il numero_bobina dovrebbe essere già impostato\n    if (!validatedData.numero_bobina || String(validatedData.numero_bobina).trim() === '') {\n      // Se per qualche motivo non è impostato, imposta un valore di default\n      validatedData.numero_bobina = '1';\n    } else {\n      // Assicurati che sia una stringa\n      validatedData.numero_bobina = String(validatedData.numero_bobina).trim();\n    }\n  }\n\n  // Validazione degli altri campi\n  for (const field of fieldsToValidate) {\n    // Assicurati che il campo esista nell'oggetto\n    if (validatedData[field] === undefined) {\n      if (field === 'metri_totali') {\n        errors[field] = \"I metri totali sono obbligatori\";\n        continue;\n      } else if (['utility', 'tipologia'].includes(field)) {\n        errors[field] = `Il campo ${field} è obbligatorio`;\n        continue;\n      } else if (field === 'n_conduttori') {\n        // Imposta un valore di default per n_conduttori\n        validatedData[field] = '0';\n        continue;\n      } else if (field === 'sezione') {\n        // Imposta un valore di default per sezione\n        validatedData[field] = '0';\n        continue;\n      } else {\n        // Per i campi non obbligatori, imposta un valore di default\n        validatedData[field] = field === 'data_DDT' ? null : 'TBD';\n        continue;\n      }\n    }\n\n    const result = validateBobinaField(field, validatedData[field]);\n\n    if (!result.valid) {\n      errors[field] = result.message;\n    } else {\n      validatedData[field] = result.value;\n      if (result.warning) {\n        warnings[field] = result.message;\n      }\n    }\n  }\n\n  // Assicurati che i campi numerici siano numeri\n  if (!errors.metri_totali) {\n    validatedData.metri_totali = parseFloat(validatedData.metri_totali) || 0;\n    if (validatedData.metri_totali <= 0) {\n      errors.metri_totali = \"I metri totali devono essere maggiori di zero\";\n    }\n  }\n\n  // Assicurati che n_conduttori sia una stringa\n  validatedData.n_conduttori = validatedData.n_conduttori !== undefined && validatedData.n_conduttori !== null ? validatedData.n_conduttori.toString() : \"0\";\n  // Se è vuoto, imposta a \"0\"\n  if (validatedData.n_conduttori === \"\") validatedData.n_conduttori = \"0\";\n\n  // Assicurati che sezione sia una stringa\n  validatedData.sezione = validatedData.sezione !== undefined && validatedData.sezione !== null ? validatedData.sezione.toString() : \"0\";\n  // Se è vuoto, imposta a \"0\"\n  if (validatedData.sezione === \"\") validatedData.sezione = \"0\";\n\n  // Log dei dati validati\n  console.log('Dati validati:', validatedData);\n  console.log('Errori di validazione:', errors);\n  console.log('Avvisi di validazione:', warnings);\n\n  return {\n    isValid: Object.keys(errors).length === 0,\n    errors,\n    warnings,\n    validatedData\n  };\n};\n"], "mappings": "AAAA;AACA;AACA;AACA;;AAEA;AACA,OAAO,MAAMA,GAAG,GAAG,KAAK;;AAExB;AACA;AACA;AACA;AACA;AACA,OAAO,MAAMC,OAAO,GAAIC,KAAK,IAAK;EAChC,OAAOA,KAAK,KAAK,IAAI,IAAIA,KAAK,KAAKC,SAAS,IACpC,OAAOD,KAAK,KAAK,QAAQ,IAAIA,KAAK,CAACE,IAAI,CAAC,CAAC,KAAK,EAAG,IACjD,OAAOF,KAAK,KAAK,QAAQ,IAAIG,KAAK,CAACH,KAAK,CAAE;AACpD,CAAC;;AAED;AACA;AACA;AACA;AACA;AACA,OAAO,MAAMI,cAAc,GAAIJ,KAAK,IAAK;EACvC,IAAID,OAAO,CAACC,KAAK,CAAC,EAAE,OAAO,CAAC;EAE5B,IAAI,OAAOA,KAAK,KAAK,QAAQ,EAAE,OAAOA,KAAK;EAE3C,IAAI,OAAOA,KAAK,KAAK,QAAQ,EAAE;IAC7B,MAAMK,UAAU,GAAGL,KAAK,CAACE,IAAI,CAAC,CAAC,CAACI,OAAO,CAAC,GAAG,EAAE,GAAG,CAAC;IACjD,IAAI;MACF,OAAOC,UAAU,CAACF,UAAU,CAAC;IAC/B,CAAC,CAAC,OAAOG,CAAC,EAAE;MACVC,OAAO,CAACC,IAAI,CAAC,8BAA8BV,KAAK,2BAA2B,CAAC;MAC5E,OAAO,CAAC;IACV;EACF;EAEAS,OAAO,CAACC,IAAI,CAAC,kCAAkC,OAAOV,KAAK,EAAE,CAAC;EAC9D,OAAO,CAAC;AACV,CAAC;;AAED;AACA;AACA;AACA;AACA;AACA;AACA,OAAO,MAAMW,cAAc,GAAGA,CAACX,KAAK,EAAEY,SAAS,KAAK;EAClD,IAAI;IACF;IACA,IAAIb,OAAO,CAACC,KAAK,CAAC,EAAE;MAClB,IAAIY,SAAS,KAAK,mBAAmB,EAAE;QACrC;QACA,OAAO;UAAEC,KAAK,EAAE,IAAI;UAAEC,OAAO,EAAE,EAAE;UAAEd,KAAK,EAAE;QAAI,CAAC;MACjD;MACA,IAAIY,SAAS,KAAK,YAAY,EAAE;QAC9B;QACA,OAAO;UAAEC,KAAK,EAAE,IAAI;UAAEC,OAAO,EAAE,EAAE;UAAEd,KAAK,EAAE;QAAI,CAAC;MACjD;MACA,OAAO;QAAEa,KAAK,EAAE,KAAK;QAAEC,OAAO,EAAE,GAAGF,SAAS,uBAAuB;QAAEZ,KAAK,EAAE;MAAK,CAAC;IACpF;;IAEA;IACA,IAAIe,eAAe,GAAGf,KAAK;IAC3B,IAAI,OAAOA,KAAK,KAAK,QAAQ,EAAE;MAC7Be,eAAe,GAAGf,KAAK,CAACE,IAAI,CAAC,CAAC,CAACI,OAAO,CAAC,GAAG,EAAE,GAAG,CAAC;MAChD,IAAIS,eAAe,KAAK,GAAG,EAAE;QAC3B,OAAO;UAAEF,KAAK,EAAE,KAAK;UAAEC,OAAO,EAAE,GAAGF,SAAS,aAAa;UAAEZ,KAAK,EAAE;QAAK,CAAC;MAC1E;IACF;;IAEA;IACA,IAAIY,SAAS,KAAK,mBAAmB,IAAIA,SAAS,KAAK,YAAY,EAAE;MACnE;MACA,MAAMI,WAAW,GAAGD,eAAe,CAACE,QAAQ,CAAC,CAAC;;MAE9C;MACA,IAAI,CAACd,KAAK,CAACI,UAAU,CAACQ,eAAe,CAAC,CAAC,EAAE;QACvC,MAAMG,MAAM,GAAGX,UAAU,CAACQ,eAAe,CAAC;;QAE1C;QACA,IAAIG,MAAM,GAAG,CAAC,EAAE;UACd,OAAO;YAAEL,KAAK,EAAE,KAAK;YAAEC,OAAO,EAAE,GAAGF,SAAS,0BAA0B;YAAEZ,KAAK,EAAE;UAAK,CAAC;QACvF;;QAEA;QACA,IAAIY,SAAS,KAAK,mBAAmB,IAAIM,MAAM,GAAG,EAAE,EAAE;UACpD,OAAO;YACLL,KAAK,EAAE,IAAI;YACXC,OAAO,EAAE,wCAAwCI,MAAM,mCAAmC;YAC1FlB,KAAK,EAAEgB,WAAW;YAClBG,OAAO,EAAE;UACX,CAAC;QACH;QAEA,IAAIP,SAAS,KAAK,YAAY,IAAIM,MAAM,GAAG,IAAI,EAAE;UAC/C,OAAO;YACLL,KAAK,EAAE,IAAI;YACXC,OAAO,EAAE,8BAA8BI,MAAM,qCAAqC;YAClFlB,KAAK,EAAEgB,WAAW;YAClBG,OAAO,EAAE;UACX,CAAC;QACH;MACF;MAEA,OAAO;QAAEN,KAAK,EAAE,IAAI;QAAEC,OAAO,EAAE,EAAE;QAAEd,KAAK,EAAEgB;MAAY,CAAC;IACzD,CAAC,MAAM;MACL;MACA,MAAME,MAAM,GAAGX,UAAU,CAACQ,eAAe,CAAC;;MAE1C;MACA,IAAIG,MAAM,GAAG,CAAC,EAAE;QACd,OAAO;UAAEL,KAAK,EAAE,KAAK;UAAEC,OAAO,EAAE,GAAGF,SAAS,0BAA0B;UAAEZ,KAAK,EAAE;QAAK,CAAC;MACvF;MAEA,OAAO;QAAEa,KAAK,EAAE,IAAI;QAAEC,OAAO,EAAE,EAAE;QAAEd,KAAK,EAAEkB,MAAM,CAACD,QAAQ,CAAC;MAAE,CAAC;IAC/D;EACF,CAAC,CAAC,OAAOT,CAAC,EAAE;IACV,OAAO;MAAEK,KAAK,EAAE,KAAK;MAAEC,OAAO,EAAE,0BAA0BF,SAAS,yBAAyB;MAAEZ,KAAK,EAAE;IAAK,CAAC;EAC7G;AACF,CAAC;;AAED;AACA;AACA;AACA;AACA;AACA,OAAO,MAAMoB,mBAAmB,GAAIpB,KAAK,IAAK;EAC5C,IAAI;IACF,IAAID,OAAO,CAACC,KAAK,CAAC,EAAE;MAClB,OAAO;QAAEa,KAAK,EAAE,KAAK;QAAEC,OAAO,EAAE,iCAAiC;QAAEd,KAAK,EAAE;MAAK,CAAC;IAClF;IAEA,MAAMqB,GAAG,GAAGjB,cAAc,CAACJ,KAAK,CAAC;IAEjC,IAAIqB,GAAG,IAAI,CAAC,EAAE;MACZ,OAAO;QAAER,KAAK,EAAE,KAAK;QAAEC,OAAO,EAAE,+CAA+C;QAAEd,KAAK,EAAE;MAAK,CAAC;IAChG;IAEA,IAAIqB,GAAG,GAAG,MAAM,EAAE;MAAG;MACnB,OAAO;QAAER,KAAK,EAAE,KAAK;QAAEC,OAAO,EAAE,6CAA6C;QAAEd,KAAK,EAAE;MAAK,CAAC;IAC9F;IAEA,OAAO;MAAEa,KAAK,EAAE,IAAI;MAAEC,OAAO,EAAE,eAAe;MAAEd,KAAK,EAAEqB;IAAI,CAAC;EAC9D,CAAC,CAAC,OAAOb,CAAC,EAAE;IACV,OAAO;MAAEK,KAAK,EAAE,KAAK;MAAEC,OAAO,EAAE,wCAAwC;MAAEd,KAAK,EAAE;IAAK,CAAC;EACzF;AACF,CAAC;;AAED;AACA;AACA;AACA;AACA;AACA,OAAO,MAAMsB,iBAAiB,GAAItB,KAAK,IAAK;EAC1C,IAAID,OAAO,CAACC,KAAK,CAAC,EAAE;IAClB,OAAO;MAAEa,KAAK,EAAE,IAAI;MAAEC,OAAO,EAAE,aAAa;MAAEd,KAAK,EAAEF;IAAI,CAAC;EAC5D;EACA,OAAO;IAAEe,KAAK,EAAE,IAAI;IAAEC,OAAO,EAAE,cAAc;IAAEd,KAAK,EAAEA,KAAK,CAACE,IAAI,CAAC;EAAE,CAAC;AACtE,CAAC;;AAED;AACA;AACA;AACA;AACA;AACA,OAAO,MAAMqB,yBAAyB,GAAIvB,KAAK,IAAK;EAClD,IAAID,OAAO,CAACC,KAAK,CAAC,EAAE;IAClB,OAAO;MAAEa,KAAK,EAAE,KAAK;MAAEC,OAAO,EAAE,+BAA+B;MAAEd,KAAK,EAAE;IAAK,CAAC;EAChF;EACA,OAAO;IAAEa,KAAK,EAAE,IAAI;IAAEC,OAAO,EAAE,cAAc;IAAEd,KAAK,EAAEA,KAAK,CAACE,IAAI,CAAC;EAAE,CAAC;AACtE,CAAC;;AAED;AACA;AACA;AACA;AACA;AACA;AACA,OAAO,MAAMsB,mBAAmB,GAAGA,CAACZ,SAAS,EAAEZ,KAAK,KAAK;EACvD;EACA,MAAMyB,kBAAkB,GAAG;IACzB,cAAc,EAAEC,CAAA,KAAMN,mBAAmB,CAACpB,KAAK,CAAC;IAChD,cAAc,EAAE2B,CAAA,KAAMhB,cAAc,CAACX,KAAK,EAAE,mBAAmB,CAAC;IAChE,SAAS,EAAE4B,CAAA,KAAMjB,cAAc,CAACX,KAAK,EAAE,YAAY;EACrD,CAAC;;EAED;EACA,MAAM6B,cAAc,GAAG,CACrB,SAAS,EAAE,WAAW,EAAE,SAAS,EAAE,cAAc,CAClD;;EAED;EACA,MAAMC,SAAS,GAAG,CAChB,mBAAmB,EAAE,WAAW,EAAE,OAAO,CAC1C;;EAED;EACA,IAAIlB,SAAS,IAAIa,kBAAkB,EAAE;IACnC,OAAOA,kBAAkB,CAACb,SAAS,CAAC,CAAC,CAAC;EACxC;;EAEA;EACA,IAAIiB,cAAc,CAACE,QAAQ,CAACnB,SAAS,CAAC,EAAE;IACtC,OAAOW,yBAAyB,CAACvB,KAAK,CAAC;EACzC;;EAEA;EACA,IAAI8B,SAAS,CAACC,QAAQ,CAACnB,SAAS,CAAC,EAAE;IACjC,OAAOU,iBAAiB,CAACtB,KAAK,CAAC;EACjC;;EAEA;EACA,OAAOsB,iBAAiB,CAACtB,KAAK,CAAC;AACjC,CAAC;;AAED;AACA;AACA;AACA;AACA;AACA,OAAO,MAAMgC,gBAAgB,GAAIhC,KAAK,IAAK;EACzC;EACA,IAAID,OAAO,CAACC,KAAK,CAAC,EAAE;IAClB,OAAO;MAAEa,KAAK,EAAE,KAAK;MAAEC,OAAO,EAAE,kCAAkC;MAAEd,KAAK,EAAE;IAAK,CAAC;EACnF;;EAEA;EACA,MAAMgB,WAAW,GAAGiB,MAAM,CAACjC,KAAK,CAAC,CAACE,IAAI,CAAC,CAAC;;EAExC;EACA,IAAIc,WAAW,KAAK,EAAE,EAAE;IACtB,OAAO;MAAEH,KAAK,EAAE,KAAK;MAAEC,OAAO,EAAE,kCAAkC;MAAEd,KAAK,EAAE;IAAK,CAAC;EACnF;;EAEA;EACA,MAAMkC,YAAY,GAAG,gBAAgB;EACrC,IAAIA,YAAY,CAACC,IAAI,CAACnB,WAAW,CAAC,EAAE;IAClC,OAAO;MACLH,KAAK,EAAE,KAAK;MACZC,OAAO,EAAE,yFAAyF;MAClGd,KAAK,EAAE;IACT,CAAC;EACH;;EAEA;EACA,IAAIgB,WAAW,CAACoB,MAAM,GAAG,EAAE,EAAE;IAC3B,OAAO;MACLvB,KAAK,EAAE,KAAK;MACZC,OAAO,EAAE,mDAAmD;MAC5Dd,KAAK,EAAE;IACT,CAAC;EACH;EAEA,OAAO;IAAEa,KAAK,EAAE,IAAI;IAAEC,OAAO,EAAE,EAAE;IAAEd,KAAK,EAAEgB;EAAY,CAAC;AACzD,CAAC;;AAED;AACA;AACA;AACA;AACA;AACA,OAAO,MAAMqB,kBAAkB,GAAIC,UAAU,IAAK;EAChD,MAAMC,MAAM,GAAG,CAAC,CAAC;EACjB,MAAMC,QAAQ,GAAG,CAAC,CAAC;EACnB,MAAMC,aAAa,GAAG;IAAE,GAAGH;EAAW,CAAC;;EAEvC;EACAG,aAAa,CAACC,cAAc,GAAGD,aAAa,CAACC,cAAc,IAAI,GAAG;;EAElE;EACA,MAAMC,gBAAgB,GAAG,CACvB,SAAS,EAAE,WAAW,EACtB,SAAS,EAAE,cAAc,EAAE,mBAAmB,EAAE,WAAW,EAAE,OAAO,CACrE;;EAED;EACA,IAAIF,aAAa,CAACC,cAAc,KAAK,GAAG,EAAE;IACxC;IACA,IAAI,CAACD,aAAa,CAACG,aAAa,IAAIX,MAAM,CAACQ,aAAa,CAACG,aAAa,CAAC,CAAC1C,IAAI,CAAC,CAAC,KAAK,EAAE,EAAE;MACrFqC,MAAM,CAACK,aAAa,GAAG,kCAAkC;IAC3D,CAAC,MAAM;MACL,MAAMC,QAAQ,GAAGb,gBAAgB,CAACS,aAAa,CAACG,aAAa,CAAC;MAC9D,IAAI,CAACC,QAAQ,CAAChC,KAAK,EAAE;QACnB0B,MAAM,CAACK,aAAa,GAAGC,QAAQ,CAAC/B,OAAO;MACzC,CAAC,MAAM;QACL2B,aAAa,CAACG,aAAa,GAAGC,QAAQ,CAAC7C,KAAK;MAC9C;IACF;EACF,CAAC,MAAM,IAAIyC,aAAa,CAACC,cAAc,KAAK,GAAG,EAAE;IAC/C;IACA,IAAI,CAACD,aAAa,CAACG,aAAa,IAAIX,MAAM,CAACQ,aAAa,CAACG,aAAa,CAAC,CAAC1C,IAAI,CAAC,CAAC,KAAK,EAAE,EAAE;MACrF;MACAuC,aAAa,CAACG,aAAa,GAAG,GAAG;IACnC,CAAC,MAAM;MACL;MACAH,aAAa,CAACG,aAAa,GAAGX,MAAM,CAACQ,aAAa,CAACG,aAAa,CAAC,CAAC1C,IAAI,CAAC,CAAC;IAC1E;EACF;;EAEA;EACA,KAAK,MAAM4C,KAAK,IAAIH,gBAAgB,EAAE;IACpC;IACA,IAAIF,aAAa,CAACK,KAAK,CAAC,KAAK7C,SAAS,EAAE;MACtC,IAAI6C,KAAK,KAAK,cAAc,EAAE;QAC5BP,MAAM,CAACO,KAAK,CAAC,GAAG,iCAAiC;QACjD;MACF,CAAC,MAAM,IAAI,CAAC,SAAS,EAAE,WAAW,CAAC,CAACf,QAAQ,CAACe,KAAK,CAAC,EAAE;QACnDP,MAAM,CAACO,KAAK,CAAC,GAAG,YAAYA,KAAK,iBAAiB;QAClD;MACF,CAAC,MAAM,IAAIA,KAAK,KAAK,cAAc,EAAE;QACnC;QACAL,aAAa,CAACK,KAAK,CAAC,GAAG,GAAG;QAC1B;MACF,CAAC,MAAM,IAAIA,KAAK,KAAK,SAAS,EAAE;QAC9B;QACAL,aAAa,CAACK,KAAK,CAAC,GAAG,GAAG;QAC1B;MACF,CAAC,MAAM;QACL;QACAL,aAAa,CAACK,KAAK,CAAC,GAAGA,KAAK,KAAK,UAAU,GAAG,IAAI,GAAG,KAAK;QAC1D;MACF;IACF;IAEA,MAAMC,MAAM,GAAGvB,mBAAmB,CAACsB,KAAK,EAAEL,aAAa,CAACK,KAAK,CAAC,CAAC;IAE/D,IAAI,CAACC,MAAM,CAAClC,KAAK,EAAE;MACjB0B,MAAM,CAACO,KAAK,CAAC,GAAGC,MAAM,CAACjC,OAAO;IAChC,CAAC,MAAM;MACL2B,aAAa,CAACK,KAAK,CAAC,GAAGC,MAAM,CAAC/C,KAAK;MACnC,IAAI+C,MAAM,CAAC5B,OAAO,EAAE;QAClBqB,QAAQ,CAACM,KAAK,CAAC,GAAGC,MAAM,CAACjC,OAAO;MAClC;IACF;EACF;;EAEA;EACA,IAAI,CAACyB,MAAM,CAACb,YAAY,EAAE;IACxBe,aAAa,CAACf,YAAY,GAAGnB,UAAU,CAACkC,aAAa,CAACf,YAAY,CAAC,IAAI,CAAC;IACxE,IAAIe,aAAa,CAACf,YAAY,IAAI,CAAC,EAAE;MACnCa,MAAM,CAACb,YAAY,GAAG,+CAA+C;IACvE;EACF;;EAEA;EACAe,aAAa,CAACd,YAAY,GAAGc,aAAa,CAACd,YAAY,KAAK1B,SAAS,IAAIwC,aAAa,CAACd,YAAY,KAAK,IAAI,GAAGc,aAAa,CAACd,YAAY,CAACV,QAAQ,CAAC,CAAC,GAAG,GAAG;EAC1J;EACA,IAAIwB,aAAa,CAACd,YAAY,KAAK,EAAE,EAAEc,aAAa,CAACd,YAAY,GAAG,GAAG;;EAEvE;EACAc,aAAa,CAACb,OAAO,GAAGa,aAAa,CAACb,OAAO,KAAK3B,SAAS,IAAIwC,aAAa,CAACb,OAAO,KAAK,IAAI,GAAGa,aAAa,CAACb,OAAO,CAACX,QAAQ,CAAC,CAAC,GAAG,GAAG;EACtI;EACA,IAAIwB,aAAa,CAACb,OAAO,KAAK,EAAE,EAAEa,aAAa,CAACb,OAAO,GAAG,GAAG;;EAE7D;EACAnB,OAAO,CAACuC,GAAG,CAAC,gBAAgB,EAAEP,aAAa,CAAC;EAC5ChC,OAAO,CAACuC,GAAG,CAAC,wBAAwB,EAAET,MAAM,CAAC;EAC7C9B,OAAO,CAACuC,GAAG,CAAC,wBAAwB,EAAER,QAAQ,CAAC;EAE/C,OAAO;IACLS,OAAO,EAAEC,MAAM,CAACC,IAAI,CAACZ,MAAM,CAAC,CAACH,MAAM,KAAK,CAAC;IACzCG,MAAM;IACNC,QAAQ;IACRC;EACF,CAAC;AACH,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}