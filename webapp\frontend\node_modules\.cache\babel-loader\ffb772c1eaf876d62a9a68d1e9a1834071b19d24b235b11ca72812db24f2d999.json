{"ast": null, "code": "import { eachWeekendOfInterval } from \"./eachWeekendOfInterval.mjs\";\nimport { endOfYear } from \"./endOfYear.mjs\";\nimport { startOfYear } from \"./startOfYear.mjs\";\n\n/**\n * @name eachWeekendOfYear\n * @category Year Helpers\n * @summary List all the Saturdays and Sundays in the year.\n *\n * @description\n * Get all the Saturdays and Sundays in the year.\n *\n * @typeParam DateType - The `Date` type, the function operates on. Gets inferred from passed arguments. Allows to use extensions like [`UTCDate`](https://github.com/date-fns/utc).\n *\n * @param date - The given year\n *\n * @returns An array containing all the Saturdays and Sundays\n *\n * @example\n * // Lists all Saturdays and Sundays in the year\n * const result = eachWeekendOfYear(new Date(2020, 1, 1))\n * //=> [\n * //   Sat Jan 03 2020 00:00:00,\n * //   Sun Jan 04 2020 00:00:00,\n * //   ...\n * //   Sun Dec 27 2020 00:00:00\n * // ]\n * ]\n */\nexport function eachWeekendOfYear(date) {\n  const start = startOfYear(date);\n  const end = endOfYear(date);\n  return eachWeekendOfInterval({\n    start,\n    end\n  });\n}\n\n// Fallback for modularized imports:\nexport default eachWeekendOfYear;", "map": {"version": 3, "names": ["eachWeekendOfInterval", "endOfYear", "startOfYear", "eachWeekendOfYear", "date", "start", "end"], "sources": ["C:/CMS/webapp/frontend/node_modules/date-fns/eachWeekendOfYear.mjs"], "sourcesContent": ["import { eachWeekendOfInterval } from \"./eachWeekendOfInterval.mjs\";\nimport { endOfYear } from \"./endOfYear.mjs\";\nimport { startOfYear } from \"./startOfYear.mjs\";\n\n/**\n * @name eachWeekendOfYear\n * @category Year Helpers\n * @summary List all the Saturdays and Sundays in the year.\n *\n * @description\n * Get all the Saturdays and Sundays in the year.\n *\n * @typeParam DateType - The `Date` type, the function operates on. Gets inferred from passed arguments. Allows to use extensions like [`UTCDate`](https://github.com/date-fns/utc).\n *\n * @param date - The given year\n *\n * @returns An array containing all the Saturdays and Sundays\n *\n * @example\n * // Lists all Saturdays and Sundays in the year\n * const result = eachWeekendOfYear(new Date(2020, 1, 1))\n * //=> [\n * //   Sat Jan 03 2020 00:00:00,\n * //   Sun Jan 04 2020 00:00:00,\n * //   ...\n * //   Sun Dec 27 2020 00:00:00\n * // ]\n * ]\n */\nexport function eachWeekendOfYear(date) {\n  const start = startOfYear(date);\n  const end = endOfYear(date);\n  return eachWeekendOfInterval({ start, end });\n}\n\n// Fallback for modularized imports:\nexport default eachWeekendOfYear;\n"], "mappings": "AAAA,SAASA,qBAAqB,QAAQ,6BAA6B;AACnE,SAASC,SAAS,QAAQ,iBAAiB;AAC3C,SAASC,WAAW,QAAQ,mBAAmB;;AAE/C;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO,SAASC,iBAAiBA,CAACC,IAAI,EAAE;EACtC,MAAMC,KAAK,GAAGH,WAAW,CAACE,IAAI,CAAC;EAC/B,MAAME,GAAG,GAAGL,SAAS,CAACG,IAAI,CAAC;EAC3B,OAAOJ,qBAAqB,CAAC;IAAEK,KAAK;IAAEC;EAAI,CAAC,CAAC;AAC9C;;AAEA;AACA,eAAeH,iBAAiB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}