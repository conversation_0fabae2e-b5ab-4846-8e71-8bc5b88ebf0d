{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\PycharmProjects\\\\CMS\\\\webapp\\\\frontend\\\\src\\\\components\\\\cavi\\\\CavoForm.js\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect, useMemo } from 'react';\nimport { Box, TextField, Button, Grid, MenuItem, Alert, CircularProgress, Typography, Card, CardContent, Collapse, IconButton, Divider } from '@mui/material';\nimport { Save as SaveIcon, Warning as WarningIcon, ExpandMore as ExpandMoreIcon, ExpandLess as ExpandLessIcon } from '@mui/icons-material';\nimport { useNavigate } from 'react-router-dom';\nimport { validateCavoData, validateField } from '../../utils/validationUtils';\nimport { redirectToVisualizzaCavi } from '../../utils/navigationUtils';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst SECTION_CONFIG = [{\n  title: 'Informazioni Generali',\n  collapsible: false,\n  fields: [{\n    name: 'id_cavo',\n    label: 'ID Cavo',\n    required: true,\n    inputProps: {\n      style: {\n        textTransform: 'uppercase'\n      }\n    }\n  }, {\n    name: 'utility',\n    label: 'Utility',\n    required: true\n  }, {\n    name: 'sistema',\n    label: 'Sistema'\n  }]\n}, {\n  title: 'Caratteristiche Tecniche',\n  collapsible: false,\n  fields: [{\n    name: 'colore_cavo',\n    label: 'Colore Cavo'\n  }, {\n    name: 'tipologia',\n    label: 'Tipologia'\n  }, {\n    name: 'n_conduttori',\n    label: 'Numero Conduttori'\n  }, {\n    name: 'sezione',\n    label: 'Sezione'\n  }, {\n    name: 'sh',\n    label: 'Schermato (S/N)',\n    type: 'select',\n    options: ['S', 'N'],\n    defaultValue: 'N'\n  }]\n}, {\n  title: 'Partenza',\n  collapsible: true,\n  fields: [{\n    name: 'ubicazione_partenza',\n    label: 'Ubicazione'\n  }, {\n    name: 'utenza_partenza',\n    label: 'Utenza'\n  }, {\n    name: 'descrizione_utenza_partenza',\n    label: 'Descrizione'\n  }]\n}, {\n  title: 'Arrivo',\n  collapsible: true,\n  fields: [{\n    name: 'ubicazione_arrivo',\n    label: 'Ubicazione'\n  }, {\n    name: 'utenza_arrivo',\n    label: 'Utenza'\n  }, {\n    name: 'descrizione_utenza_arrivo',\n    label: 'Descrizione'\n  }]\n}, {\n  title: 'Metratura',\n  collapsible: false,\n  fields: [{\n    name: 'metri_teorici',\n    label: 'Metri Teorici',\n    required: true\n  }, {\n    name: 'metratura_reale',\n    label: 'Metratura Reale'\n  }]\n}];\nconst defaultData = {\n  id_cavo: '',\n  utility: '',\n  sistema: '',\n  colore_cavo: '',\n  tipologia: '',\n  n_conduttori: '',\n  sezione: '',\n  sh: 'N',\n  ubicazione_partenza: '',\n  utenza_partenza: '',\n  descrizione_utenza_partenza: '',\n  ubicazione_arrivo: '',\n  utenza_arrivo: '',\n  descrizione_utenza_arrivo: '',\n  metri_teorici: '',\n  metratura_reale: '0'\n};\nconst CavoForm = ({\n  mode = 'add',\n  initialData = {},\n  onSubmit,\n  onSuccess,\n  onError,\n  onCancel\n}) => {\n  _s();\n  const navigate = useNavigate();\n  const [formData, setFormData] = useState({\n    ...defaultData,\n    ...initialData\n  });\n  const [formErrors, setFormErrors] = useState({});\n  const [warnings, setWarnings] = useState({});\n  const [loading, setLoading] = useState(false);\n  const [expanded, setExpanded] = useState({});\n  useEffect(() => {\n    const state = {};\n    SECTION_CONFIG.forEach(s => {\n      if (s.collapsible) state[s.title] = false;\n    });\n    setExpanded(state);\n  }, []);\n  const toggleExpand = title => {\n    setExpanded(prev => ({\n      ...prev,\n      [title]: !prev[title]\n    }));\n  };\n  const handleChange = e => {\n    const {\n      name,\n      value\n    } = e.target;\n    setFormData(prev => ({\n      ...prev,\n      [name]: value\n    }));\n    const extra = name === 'metratura_reale' ? {\n      metriTeorici: parseFloat(formData.metri_teorici || 0)\n    } : {};\n    const result = validateField(name, value, extra);\n    setFormErrors(prev => ({\n      ...prev,\n      [name]: result.valid ? null : result.message\n    }));\n    setWarnings(prev => ({\n      ...prev,\n      [name]: result.warning ? result.message : null\n    }));\n  };\n  const handleSubmit = async e => {\n    e.preventDefault();\n    setLoading(true);\n    const validation = validateCavoData(formData);\n    if (!validation.isValid) {\n      setFormErrors(validation.errors);\n      setWarnings(validation.warnings);\n      setLoading(false);\n      onError('Ci sono errori nel form.');\n      return;\n    }\n    try {\n      const finalData = {\n        ...validation.validatedData,\n        id_cavo: validation.validatedData.id_cavo.toUpperCase()\n      };\n      await onSubmit(finalData);\n      setLoading(false);\n      onSuccess(`Cavo ${mode === 'add' ? 'aggiunto' : 'modificato'} con successo.`);\n      redirectToVisualizzaCavi(navigate);\n    } catch (err) {\n      setLoading(false);\n      onError(err.message);\n    }\n  };\n  return /*#__PURE__*/_jsxDEV(Box, {\n    component: \"form\",\n    onSubmit: handleSubmit,\n    noValidate: true,\n    className: \"space-y-4\",\n    children: [Object.values(warnings).some(w => w) && /*#__PURE__*/_jsxDEV(Alert, {\n      severity: \"warning\",\n      icon: /*#__PURE__*/_jsxDEV(WarningIcon, {}, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 152,\n        columnNumber: 41\n      }, this),\n      children: \"Alcuni campi potrebbero necessitare revisione.\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 152,\n      columnNumber: 9\n    }, this), SECTION_CONFIG.map(section => /*#__PURE__*/_jsxDEV(Card, {\n      className: \"shadow-md\",\n      children: [/*#__PURE__*/_jsxDEV(Box, {\n        className: \"flex justify-between items-center p-4 cursor-pointer\",\n        onClick: () => section.collapsible && toggleExpand(section.title),\n        children: [/*#__PURE__*/_jsxDEV(Typography, {\n          variant: \"h6\",\n          className: \"text-blue-600 font-semibold\",\n          children: section.title\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 160,\n          columnNumber: 13\n        }, this), section.collapsible && (expanded[section.title] ? /*#__PURE__*/_jsxDEV(ExpandLessIcon, {}, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 163,\n          columnNumber: 64\n        }, this) : /*#__PURE__*/_jsxDEV(ExpandMoreIcon, {}, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 163,\n          columnNumber: 85\n        }, this))]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 159,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(Collapse, {\n        in: !section.collapsible || expanded[section.title],\n        children: [/*#__PURE__*/_jsxDEV(Divider, {}, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 166,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(CardContent, {\n          children: /*#__PURE__*/_jsxDEV(Grid, {\n            container: true,\n            spacing: 2,\n            children: section.fields.map(field => {\n              var _field$options;\n              return /*#__PURE__*/_jsxDEV(Grid, {\n                item: true,\n                xs: 12,\n                sm: 6,\n                children: /*#__PURE__*/_jsxDEV(TextField, {\n                  select: field.type === 'select',\n                  fullWidth: true,\n                  label: field.label,\n                  name: field.name,\n                  value: formData[field.name],\n                  onChange: handleChange,\n                  error: !!formErrors[field.name],\n                  helperText: formErrors[field.name] || warnings[field.name],\n                  required: field.required,\n                  variant: \"outlined\",\n                  InputLabelProps: {\n                    shrink: true\n                  },\n                  ...(field.inputProps || {}),\n                  children: (_field$options = field.options) === null || _field$options === void 0 ? void 0 : _field$options.map(opt => /*#__PURE__*/_jsxDEV(MenuItem, {\n                    value: opt,\n                    children: opt\n                  }, opt, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 186,\n                    columnNumber: 25\n                  }, this))\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 171,\n                  columnNumber: 21\n                }, this)\n              }, field.name, false, {\n                fileName: _jsxFileName,\n                lineNumber: 170,\n                columnNumber: 19\n              }, this);\n            })\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 168,\n            columnNumber: 15\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 167,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 165,\n        columnNumber: 11\n      }, this)]\n    }, section.title, true, {\n      fileName: _jsxFileName,\n      lineNumber: 158,\n      columnNumber: 9\n    }, this)), /*#__PURE__*/_jsxDEV(Box, {\n      className: \"flex justify-center gap-4 mt-6\",\n      children: [/*#__PURE__*/_jsxDEV(Button, {\n        variant: \"outlined\",\n        color: \"secondary\",\n        onClick: onCancel,\n        disabled: loading,\n        children: \"Annulla\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 198,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Button, {\n        type: \"submit\",\n        variant: \"contained\",\n        color: \"primary\",\n        startIcon: /*#__PURE__*/_jsxDEV(SaveIcon, {}, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 201,\n          columnNumber: 78\n        }, this),\n        disabled: loading,\n        children: loading ? /*#__PURE__*/_jsxDEV(CircularProgress, {\n          size: 20\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 202,\n          columnNumber: 22\n        }, this) : mode === 'add' ? 'Salva Cavo' : 'Salva Modifiche'\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 201,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 197,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 150,\n    columnNumber: 5\n  }, this);\n};\n_s(CavoForm, \"xW0H4VjJtlFb8kd2hZxEnOWqQy4=\", false, function () {\n  return [useNavigate];\n});\n_c = CavoForm;\nexport default CavoForm;\nvar _c;\n$RefreshReg$(_c, \"CavoForm\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "useMemo", "Box", "TextField", "<PERSON><PERSON>", "Grid", "MenuItem", "<PERSON><PERSON>", "CircularProgress", "Typography", "Card", "<PERSON><PERSON><PERSON><PERSON>", "Collapse", "IconButton", "Divider", "Save", "SaveIcon", "Warning", "WarningIcon", "ExpandMore", "ExpandMoreIcon", "ExpandLess", "ExpandLessIcon", "useNavigate", "validateCavoData", "validateField", "redirectToVisualizzaCavi", "jsxDEV", "_jsxDEV", "SECTION_CONFIG", "title", "collapsible", "fields", "name", "label", "required", "inputProps", "style", "textTransform", "type", "options", "defaultValue", "defaultData", "id_cavo", "utility", "sistema", "colore_cavo", "tipologia", "n_conduttori", "sezione", "sh", "ubicazione_partenza", "utenza_partenza", "descrizione_utenza_partenza", "ubicazione_arrivo", "utenza_arrivo", "descrizione_utenza_arrivo", "metri_te<PERSON>ci", "metratura_reale", "CavoForm", "mode", "initialData", "onSubmit", "onSuccess", "onError", "onCancel", "_s", "navigate", "formData", "setFormData", "formErrors", "setFormErrors", "warnings", "setWarnings", "loading", "setLoading", "expanded", "setExpanded", "state", "for<PERSON>ach", "s", "toggleExpand", "prev", "handleChange", "e", "value", "target", "extra", "metriTeorici", "parseFloat", "result", "valid", "message", "warning", "handleSubmit", "preventDefault", "validation", "<PERSON><PERSON><PERSON><PERSON>", "errors", "finalData", "validatedData", "toUpperCase", "err", "component", "noValidate", "className", "children", "Object", "values", "some", "w", "severity", "icon", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "map", "section", "onClick", "variant", "in", "container", "spacing", "field", "_field$options", "item", "xs", "sm", "select", "fullWidth", "onChange", "error", "helperText", "InputLabelProps", "shrink", "opt", "color", "disabled", "startIcon", "size", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/PycharmProjects/CMS/webapp/frontend/src/components/cavi/CavoForm.js"], "sourcesContent": ["import React, { useState, useEffect, useMemo } from 'react';\nimport {\n  Box,\n  TextField,\n  Button,\n  Grid,\n  MenuItem,\n  Alert,\n  CircularProgress,\n  Typography,\n  Card,\n  CardContent,\n  Collapse,\n  IconButton,\n  Divider\n} from '@mui/material';\nimport {\n  Save as SaveIcon,\n  Warning as WarningIcon,\n  ExpandMore as ExpandMoreIcon,\n  ExpandLess as ExpandLessIcon\n} from '@mui/icons-material';\nimport { useNavigate } from 'react-router-dom';\nimport { validateCavoData, validateField } from '../../utils/validationUtils';\nimport { redirectToVisualizzaCavi } from '../../utils/navigationUtils';\n\nconst SECTION_CONFIG = [\n  {\n    title: 'Informazioni Generali',\n    collapsible: false,\n    fields: [\n      { name: 'id_cavo', label: 'ID Cavo', required: true, inputProps: { style: { textTransform: 'uppercase' } } },\n      { name: 'utility', label: 'Utility', required: true },\n      { name: 'sistema', label: 'Sistema' }\n    ]\n  },\n  {\n    title: '<PERSON>tteristiche Tecniche',\n    collapsible: false,\n    fields: [\n      { name: 'colore_cavo', label: 'Colore Cavo' },\n      { name: 'tipologia', label: 'Tipologia' },\n      { name: 'n_conduttori', label: 'Numero Conduttori' },\n      { name: 'sezione', label: 'Sezione' },\n      { name: 'sh', label: 'Schermato (S/N)', type: 'select', options: ['S', 'N'], defaultValue: 'N' }\n    ]\n  },\n  {\n    title: 'Partenza',\n    collapsible: true,\n    fields: [\n      { name: 'ubicazione_partenza', label: 'Ubicazione' },\n      { name: 'utenza_partenza', label: 'Utenza' },\n      { name: 'descrizione_utenza_partenza', label: 'Descrizione' }\n    ]\n  },\n  {\n    title: 'Arrivo',\n    collapsible: true,\n    fields: [\n      { name: 'ubicazione_arrivo', label: 'Ubicazione' },\n      { name: 'utenza_arrivo', label: 'Utenza' },\n      { name: 'descrizione_utenza_arrivo', label: 'Descrizione' }\n    ]\n  },\n  {\n    title: 'Metratura',\n    collapsible: false,\n    fields: [\n      { name: 'metri_teorici', label: 'Metri Teorici', required: true },\n      { name: 'metratura_reale', label: 'Metratura Reale' }\n    ]\n  }\n];\n\nconst defaultData = {\n  id_cavo: '',\n  utility: '',\n  sistema: '',\n  colore_cavo: '',\n  tipologia: '',\n  n_conduttori: '',\n  sezione: '',\n  sh: 'N',\n  ubicazione_partenza: '',\n  utenza_partenza: '',\n  descrizione_utenza_partenza: '',\n  ubicazione_arrivo: '',\n  utenza_arrivo: '',\n  descrizione_utenza_arrivo: '',\n  metri_teorici: '',\n  metratura_reale: '0'\n};\n\nconst CavoForm = ({ mode = 'add', initialData = {}, onSubmit, onSuccess, onError, onCancel }) => {\n  const navigate = useNavigate();\n  const [formData, setFormData] = useState({ ...defaultData, ...initialData });\n  const [formErrors, setFormErrors] = useState({});\n  const [warnings, setWarnings] = useState({});\n  const [loading, setLoading] = useState(false);\n  const [expanded, setExpanded] = useState({});\n\n  useEffect(() => {\n    const state = {};\n    SECTION_CONFIG.forEach(s => { if (s.collapsible) state[s.title] = false; });\n    setExpanded(state);\n  }, []);\n\n  const toggleExpand = (title) => {\n    setExpanded(prev => ({ ...prev, [title]: !prev[title] }));\n  };\n\n  const handleChange = (e) => {\n    const { name, value } = e.target;\n    setFormData(prev => ({ ...prev, [name]: value }));\n\n    const extra = name === 'metratura_reale' ? { metriTeorici: parseFloat(formData.metri_teorici || 0) } : {};\n    const result = validateField(name, value, extra);\n\n    setFormErrors(prev => ({ ...prev, [name]: result.valid ? null : result.message }));\n    setWarnings(prev => ({ ...prev, [name]: result.warning ? result.message : null }));\n  };\n\n  const handleSubmit = async (e) => {\n    e.preventDefault();\n    setLoading(true);\n    const validation = validateCavoData(formData);\n\n    if (!validation.isValid) {\n      setFormErrors(validation.errors);\n      setWarnings(validation.warnings);\n      setLoading(false);\n      onError('Ci sono errori nel form.');\n      return;\n    }\n\n    try {\n      const finalData = { ...validation.validatedData, id_cavo: validation.validatedData.id_cavo.toUpperCase() };\n      await onSubmit(finalData);\n      setLoading(false);\n      onSuccess(`Cavo ${mode === 'add' ? 'aggiunto' : 'modificato'} con successo.`);\n      redirectToVisualizzaCavi(navigate);\n    } catch (err) {\n      setLoading(false);\n      onError(err.message);\n    }\n  };\n\n  return (\n    <Box component=\"form\" onSubmit={handleSubmit} noValidate className=\"space-y-4\">\n      {Object.values(warnings).some(w => w) && (\n        <Alert severity=\"warning\" icon={<WarningIcon />}>\n          Alcuni campi potrebbero necessitare revisione.\n        </Alert>\n      )}\n\n      {SECTION_CONFIG.map(section => (\n        <Card key={section.title} className=\"shadow-md\">\n          <Box className=\"flex justify-between items-center p-4 cursor-pointer\" onClick={() => section.collapsible && toggleExpand(section.title)}>\n            <Typography variant=\"h6\" className=\"text-blue-600 font-semibold\">\n              {section.title}\n            </Typography>\n            {section.collapsible && (expanded[section.title] ? <ExpandLessIcon /> : <ExpandMoreIcon />)}\n          </Box>\n          <Collapse in={!section.collapsible || expanded[section.title]}>\n            <Divider />\n            <CardContent>\n              <Grid container spacing={2}>\n                {section.fields.map(field => (\n                  <Grid item xs={12} sm={6} key={field.name}>\n                    <TextField\n                      select={field.type === 'select'}\n                      fullWidth\n                      label={field.label}\n                      name={field.name}\n                      value={formData[field.name]}\n                      onChange={handleChange}\n                      error={!!formErrors[field.name]}\n                      helperText={formErrors[field.name] || warnings[field.name]}\n                      required={field.required}\n                      variant=\"outlined\"\n                      InputLabelProps={{ shrink: true }}\n                      {...(field.inputProps || {})}\n                    >\n                      {field.options?.map(opt => (\n                        <MenuItem key={opt} value={opt}>{opt}</MenuItem>\n                      ))}\n                    </TextField>\n                  </Grid>\n                ))}\n              </Grid>\n            </CardContent>\n          </Collapse>\n        </Card>\n      ))}\n\n      <Box className=\"flex justify-center gap-4 mt-6\">\n        <Button variant=\"outlined\" color=\"secondary\" onClick={onCancel} disabled={loading}>\n          Annulla\n        </Button>\n        <Button type=\"submit\" variant=\"contained\" color=\"primary\" startIcon={<SaveIcon />} disabled={loading}>\n          {loading ? <CircularProgress size={20} /> : mode === 'add' ? 'Salva Cavo' : 'Salva Modifiche'}\n        </Button>\n      </Box>\n    </Box>\n  );\n};\n\nexport default CavoForm;"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,EAAEC,OAAO,QAAQ,OAAO;AAC3D,SACEC,GAAG,EACHC,SAAS,EACTC,MAAM,EACNC,IAAI,EACJC,QAAQ,EACRC,KAAK,EACLC,gBAAgB,EAChBC,UAAU,EACVC,IAAI,EACJC,WAAW,EACXC,QAAQ,EACRC,UAAU,EACVC,OAAO,QACF,eAAe;AACtB,SACEC,IAAI,IAAIC,QAAQ,EAChBC,OAAO,IAAIC,WAAW,EACtBC,UAAU,IAAIC,cAAc,EAC5BC,UAAU,IAAIC,cAAc,QACvB,qBAAqB;AAC5B,SAASC,WAAW,QAAQ,kBAAkB;AAC9C,SAASC,gBAAgB,EAAEC,aAAa,QAAQ,6BAA6B;AAC7E,SAASC,wBAAwB,QAAQ,6BAA6B;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAEvE,MAAMC,cAAc,GAAG,CACrB;EACEC,KAAK,EAAE,uBAAuB;EAC9BC,WAAW,EAAE,KAAK;EAClBC,MAAM,EAAE,CACN;IAAEC,IAAI,EAAE,SAAS;IAAEC,KAAK,EAAE,SAAS;IAAEC,QAAQ,EAAE,IAAI;IAAEC,UAAU,EAAE;MAAEC,KAAK,EAAE;QAAEC,aAAa,EAAE;MAAY;IAAE;EAAE,CAAC,EAC5G;IAAEL,IAAI,EAAE,SAAS;IAAEC,KAAK,EAAE,SAAS;IAAEC,QAAQ,EAAE;EAAK,CAAC,EACrD;IAAEF,IAAI,EAAE,SAAS;IAAEC,KAAK,EAAE;EAAU,CAAC;AAEzC,CAAC,EACD;EACEJ,KAAK,EAAE,0BAA0B;EACjCC,WAAW,EAAE,KAAK;EAClBC,MAAM,EAAE,CACN;IAAEC,IAAI,EAAE,aAAa;IAAEC,KAAK,EAAE;EAAc,CAAC,EAC7C;IAAED,IAAI,EAAE,WAAW;IAAEC,KAAK,EAAE;EAAY,CAAC,EACzC;IAAED,IAAI,EAAE,cAAc;IAAEC,KAAK,EAAE;EAAoB,CAAC,EACpD;IAAED,IAAI,EAAE,SAAS;IAAEC,KAAK,EAAE;EAAU,CAAC,EACrC;IAAED,IAAI,EAAE,IAAI;IAAEC,KAAK,EAAE,iBAAiB;IAAEK,IAAI,EAAE,QAAQ;IAAEC,OAAO,EAAE,CAAC,GAAG,EAAE,GAAG,CAAC;IAAEC,YAAY,EAAE;EAAI,CAAC;AAEpG,CAAC,EACD;EACEX,KAAK,EAAE,UAAU;EACjBC,WAAW,EAAE,IAAI;EACjBC,MAAM,EAAE,CACN;IAAEC,IAAI,EAAE,qBAAqB;IAAEC,KAAK,EAAE;EAAa,CAAC,EACpD;IAAED,IAAI,EAAE,iBAAiB;IAAEC,KAAK,EAAE;EAAS,CAAC,EAC5C;IAAED,IAAI,EAAE,6BAA6B;IAAEC,KAAK,EAAE;EAAc,CAAC;AAEjE,CAAC,EACD;EACEJ,KAAK,EAAE,QAAQ;EACfC,WAAW,EAAE,IAAI;EACjBC,MAAM,EAAE,CACN;IAAEC,IAAI,EAAE,mBAAmB;IAAEC,KAAK,EAAE;EAAa,CAAC,EAClD;IAAED,IAAI,EAAE,eAAe;IAAEC,KAAK,EAAE;EAAS,CAAC,EAC1C;IAAED,IAAI,EAAE,2BAA2B;IAAEC,KAAK,EAAE;EAAc,CAAC;AAE/D,CAAC,EACD;EACEJ,KAAK,EAAE,WAAW;EAClBC,WAAW,EAAE,KAAK;EAClBC,MAAM,EAAE,CACN;IAAEC,IAAI,EAAE,eAAe;IAAEC,KAAK,EAAE,eAAe;IAAEC,QAAQ,EAAE;EAAK,CAAC,EACjE;IAAEF,IAAI,EAAE,iBAAiB;IAAEC,KAAK,EAAE;EAAkB,CAAC;AAEzD,CAAC,CACF;AAED,MAAMQ,WAAW,GAAG;EAClBC,OAAO,EAAE,EAAE;EACXC,OAAO,EAAE,EAAE;EACXC,OAAO,EAAE,EAAE;EACXC,WAAW,EAAE,EAAE;EACfC,SAAS,EAAE,EAAE;EACbC,YAAY,EAAE,EAAE;EAChBC,OAAO,EAAE,EAAE;EACXC,EAAE,EAAE,GAAG;EACPC,mBAAmB,EAAE,EAAE;EACvBC,eAAe,EAAE,EAAE;EACnBC,2BAA2B,EAAE,EAAE;EAC/BC,iBAAiB,EAAE,EAAE;EACrBC,aAAa,EAAE,EAAE;EACjBC,yBAAyB,EAAE,EAAE;EAC7BC,aAAa,EAAE,EAAE;EACjBC,eAAe,EAAE;AACnB,CAAC;AAED,MAAMC,QAAQ,GAAGA,CAAC;EAAEC,IAAI,GAAG,KAAK;EAAEC,WAAW,GAAG,CAAC,CAAC;EAAEC,QAAQ;EAAEC,SAAS;EAAEC,OAAO;EAAEC;AAAS,CAAC,KAAK;EAAAC,EAAA;EAC/F,MAAMC,QAAQ,GAAG5C,WAAW,CAAC,CAAC;EAC9B,MAAM,CAAC6C,QAAQ,EAAEC,WAAW,CAAC,GAAGtE,QAAQ,CAAC;IAAE,GAAG2C,WAAW;IAAE,GAAGmB;EAAY,CAAC,CAAC;EAC5E,MAAM,CAACS,UAAU,EAAEC,aAAa,CAAC,GAAGxE,QAAQ,CAAC,CAAC,CAAC,CAAC;EAChD,MAAM,CAACyE,QAAQ,EAAEC,WAAW,CAAC,GAAG1E,QAAQ,CAAC,CAAC,CAAC,CAAC;EAC5C,MAAM,CAAC2E,OAAO,EAAEC,UAAU,CAAC,GAAG5E,QAAQ,CAAC,KAAK,CAAC;EAC7C,MAAM,CAAC6E,QAAQ,EAAEC,WAAW,CAAC,GAAG9E,QAAQ,CAAC,CAAC,CAAC,CAAC;EAE5CC,SAAS,CAAC,MAAM;IACd,MAAM8E,KAAK,GAAG,CAAC,CAAC;IAChBjD,cAAc,CAACkD,OAAO,CAACC,CAAC,IAAI;MAAE,IAAIA,CAAC,CAACjD,WAAW,EAAE+C,KAAK,CAACE,CAAC,CAAClD,KAAK,CAAC,GAAG,KAAK;IAAE,CAAC,CAAC;IAC3E+C,WAAW,CAACC,KAAK,CAAC;EACpB,CAAC,EAAE,EAAE,CAAC;EAEN,MAAMG,YAAY,GAAInD,KAAK,IAAK;IAC9B+C,WAAW,CAACK,IAAI,KAAK;MAAE,GAAGA,IAAI;MAAE,CAACpD,KAAK,GAAG,CAACoD,IAAI,CAACpD,KAAK;IAAE,CAAC,CAAC,CAAC;EAC3D,CAAC;EAED,MAAMqD,YAAY,GAAIC,CAAC,IAAK;IAC1B,MAAM;MAAEnD,IAAI;MAAEoD;IAAM,CAAC,GAAGD,CAAC,CAACE,MAAM;IAChCjB,WAAW,CAACa,IAAI,KAAK;MAAE,GAAGA,IAAI;MAAE,CAACjD,IAAI,GAAGoD;IAAM,CAAC,CAAC,CAAC;IAEjD,MAAME,KAAK,GAAGtD,IAAI,KAAK,iBAAiB,GAAG;MAAEuD,YAAY,EAAEC,UAAU,CAACrB,QAAQ,CAACX,aAAa,IAAI,CAAC;IAAE,CAAC,GAAG,CAAC,CAAC;IACzG,MAAMiC,MAAM,GAAGjE,aAAa,CAACQ,IAAI,EAAEoD,KAAK,EAAEE,KAAK,CAAC;IAEhDhB,aAAa,CAACW,IAAI,KAAK;MAAE,GAAGA,IAAI;MAAE,CAACjD,IAAI,GAAGyD,MAAM,CAACC,KAAK,GAAG,IAAI,GAAGD,MAAM,CAACE;IAAQ,CAAC,CAAC,CAAC;IAClFnB,WAAW,CAACS,IAAI,KAAK;MAAE,GAAGA,IAAI;MAAE,CAACjD,IAAI,GAAGyD,MAAM,CAACG,OAAO,GAAGH,MAAM,CAACE,OAAO,GAAG;IAAK,CAAC,CAAC,CAAC;EACpF,CAAC;EAED,MAAME,YAAY,GAAG,MAAOV,CAAC,IAAK;IAChCA,CAAC,CAACW,cAAc,CAAC,CAAC;IAClBpB,UAAU,CAAC,IAAI,CAAC;IAChB,MAAMqB,UAAU,GAAGxE,gBAAgB,CAAC4C,QAAQ,CAAC;IAE7C,IAAI,CAAC4B,UAAU,CAACC,OAAO,EAAE;MACvB1B,aAAa,CAACyB,UAAU,CAACE,MAAM,CAAC;MAChCzB,WAAW,CAACuB,UAAU,CAACxB,QAAQ,CAAC;MAChCG,UAAU,CAAC,KAAK,CAAC;MACjBX,OAAO,CAAC,0BAA0B,CAAC;MACnC;IACF;IAEA,IAAI;MACF,MAAMmC,SAAS,GAAG;QAAE,GAAGH,UAAU,CAACI,aAAa;QAAEzD,OAAO,EAAEqD,UAAU,CAACI,aAAa,CAACzD,OAAO,CAAC0D,WAAW,CAAC;MAAE,CAAC;MAC1G,MAAMvC,QAAQ,CAACqC,SAAS,CAAC;MACzBxB,UAAU,CAAC,KAAK,CAAC;MACjBZ,SAAS,CAAC,QAAQH,IAAI,KAAK,KAAK,GAAG,UAAU,GAAG,YAAY,gBAAgB,CAAC;MAC7ElC,wBAAwB,CAACyC,QAAQ,CAAC;IACpC,CAAC,CAAC,OAAOmC,GAAG,EAAE;MACZ3B,UAAU,CAAC,KAAK,CAAC;MACjBX,OAAO,CAACsC,GAAG,CAACV,OAAO,CAAC;IACtB;EACF,CAAC;EAED,oBACEhE,OAAA,CAAC1B,GAAG;IAACqG,SAAS,EAAC,MAAM;IAACzC,QAAQ,EAAEgC,YAAa;IAACU,UAAU;IAACC,SAAS,EAAC,WAAW;IAAAC,QAAA,GAC3EC,MAAM,CAACC,MAAM,CAACpC,QAAQ,CAAC,CAACqC,IAAI,CAACC,CAAC,IAAIA,CAAC,CAAC,iBACnClF,OAAA,CAACrB,KAAK;MAACwG,QAAQ,EAAC,SAAS;MAACC,IAAI,eAAEpF,OAAA,CAACV,WAAW;QAAA+F,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAE;MAAAV,QAAA,EAAC;IAEjD;MAAAO,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAO,CACR,EAEAvF,cAAc,CAACwF,GAAG,CAACC,OAAO,iBACzB1F,OAAA,CAAClB,IAAI;MAAqB+F,SAAS,EAAC,WAAW;MAAAC,QAAA,gBAC7C9E,OAAA,CAAC1B,GAAG;QAACuG,SAAS,EAAC,sDAAsD;QAACc,OAAO,EAAEA,CAAA,KAAMD,OAAO,CAACvF,WAAW,IAAIkD,YAAY,CAACqC,OAAO,CAACxF,KAAK,CAAE;QAAA4E,QAAA,gBACtI9E,OAAA,CAACnB,UAAU;UAAC+G,OAAO,EAAC,IAAI;UAACf,SAAS,EAAC,6BAA6B;UAAAC,QAAA,EAC7DY,OAAO,CAACxF;QAAK;UAAAmF,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACJ,CAAC,EACZE,OAAO,CAACvF,WAAW,KAAK6C,QAAQ,CAAC0C,OAAO,CAACxF,KAAK,CAAC,gBAAGF,OAAA,CAACN,cAAc;UAAA2F,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,gBAAGxF,OAAA,CAACR,cAAc;UAAA6F,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACxF,CAAC,eACNxF,OAAA,CAAChB,QAAQ;QAAC6G,EAAE,EAAE,CAACH,OAAO,CAACvF,WAAW,IAAI6C,QAAQ,CAAC0C,OAAO,CAACxF,KAAK,CAAE;QAAA4E,QAAA,gBAC5D9E,OAAA,CAACd,OAAO;UAAAmG,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,eACXxF,OAAA,CAACjB,WAAW;UAAA+F,QAAA,eACV9E,OAAA,CAACvB,IAAI;YAACqH,SAAS;YAACC,OAAO,EAAE,CAAE;YAAAjB,QAAA,EACxBY,OAAO,CAACtF,MAAM,CAACqF,GAAG,CAACO,KAAK;cAAA,IAAAC,cAAA;cAAA,oBACvBjG,OAAA,CAACvB,IAAI;gBAACyH,IAAI;gBAACC,EAAE,EAAE,EAAG;gBAACC,EAAE,EAAE,CAAE;gBAAAtB,QAAA,eACvB9E,OAAA,CAACzB,SAAS;kBACR8H,MAAM,EAAEL,KAAK,CAACrF,IAAI,KAAK,QAAS;kBAChC2F,SAAS;kBACThG,KAAK,EAAE0F,KAAK,CAAC1F,KAAM;kBACnBD,IAAI,EAAE2F,KAAK,CAAC3F,IAAK;kBACjBoD,KAAK,EAAEjB,QAAQ,CAACwD,KAAK,CAAC3F,IAAI,CAAE;kBAC5BkG,QAAQ,EAAEhD,YAAa;kBACvBiD,KAAK,EAAE,CAAC,CAAC9D,UAAU,CAACsD,KAAK,CAAC3F,IAAI,CAAE;kBAChCoG,UAAU,EAAE/D,UAAU,CAACsD,KAAK,CAAC3F,IAAI,CAAC,IAAIuC,QAAQ,CAACoD,KAAK,CAAC3F,IAAI,CAAE;kBAC3DE,QAAQ,EAAEyF,KAAK,CAACzF,QAAS;kBACzBqF,OAAO,EAAC,UAAU;kBAClBc,eAAe,EAAE;oBAAEC,MAAM,EAAE;kBAAK,CAAE;kBAAA,IAC7BX,KAAK,CAACxF,UAAU,IAAI,CAAC,CAAC;kBAAAsE,QAAA,GAAAmB,cAAA,GAE1BD,KAAK,CAACpF,OAAO,cAAAqF,cAAA,uBAAbA,cAAA,CAAeR,GAAG,CAACmB,GAAG,iBACrB5G,OAAA,CAACtB,QAAQ;oBAAW+E,KAAK,EAAEmD,GAAI;oBAAA9B,QAAA,EAAE8B;kBAAG,GAArBA,GAAG;oBAAAvB,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAA6B,CAChD;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACO;cAAC,GAlBiBQ,KAAK,CAAC3F,IAAI;gBAAAgF,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAmBnC,CAAC;YAAA,CACR;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACE;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACI,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACN,CAAC;IAAA,GAnCFE,OAAO,CAACxF,KAAK;MAAAmF,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAoClB,CACP,CAAC,eAEFxF,OAAA,CAAC1B,GAAG;MAACuG,SAAS,EAAC,gCAAgC;MAAAC,QAAA,gBAC7C9E,OAAA,CAACxB,MAAM;QAACoH,OAAO,EAAC,UAAU;QAACiB,KAAK,EAAC,WAAW;QAAClB,OAAO,EAAEtD,QAAS;QAACyE,QAAQ,EAAEhE,OAAQ;QAAAgC,QAAA,EAAC;MAEnF;QAAAO,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAQ,CAAC,eACTxF,OAAA,CAACxB,MAAM;QAACmC,IAAI,EAAC,QAAQ;QAACiF,OAAO,EAAC,WAAW;QAACiB,KAAK,EAAC,SAAS;QAACE,SAAS,eAAE/G,OAAA,CAACZ,QAAQ;UAAAiG,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAE;QAACsB,QAAQ,EAAEhE,OAAQ;QAAAgC,QAAA,EAClGhC,OAAO,gBAAG9C,OAAA,CAACpB,gBAAgB;UAACoI,IAAI,EAAE;QAAG;UAAA3B,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,GAAGxD,IAAI,KAAK,KAAK,GAAG,YAAY,GAAG;MAAiB;QAAAqD,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACvF,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACN,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CAAC;AAEV,CAAC;AAAClD,EAAA,CAhHIP,QAAQ;EAAA,QACKpC,WAAW;AAAA;AAAAsH,EAAA,GADxBlF,QAAQ;AAkHd,eAAeA,QAAQ;AAAC,IAAAkF,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}