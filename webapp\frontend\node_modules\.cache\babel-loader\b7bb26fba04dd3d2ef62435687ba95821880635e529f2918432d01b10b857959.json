{"ast": null, "code": "import ascending from \"./ascending.js\";\nimport maxIndex from \"./maxIndex.js\";\nexport default function greatestIndex(values) {\n  let compare = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : ascending;\n  if (compare.length === 1) return maxIndex(values, compare);\n  let maxValue;\n  let max = -1;\n  let index = -1;\n  for (const value of values) {\n    ++index;\n    if (max < 0 ? compare(value, value) === 0 : compare(value, maxValue) > 0) {\n      maxValue = value;\n      max = index;\n    }\n  }\n  return max;\n}", "map": {"version": 3, "names": ["ascending", "maxIndex", "greatestIndex", "values", "compare", "arguments", "length", "undefined", "maxValue", "max", "index", "value"], "sources": ["C:/Users/<USER>/PycharmProjects/CMS/webapp/frontend/node_modules/d3-array/src/greatestIndex.js"], "sourcesContent": ["import ascending from \"./ascending.js\";\nimport maxIndex from \"./maxIndex.js\";\n\nexport default function greatestIndex(values, compare = ascending) {\n  if (compare.length === 1) return maxIndex(values, compare);\n  let maxValue;\n  let max = -1;\n  let index = -1;\n  for (const value of values) {\n    ++index;\n    if (max < 0\n        ? compare(value, value) === 0\n        : compare(value, maxValue) > 0) {\n      maxValue = value;\n      max = index;\n    }\n  }\n  return max;\n}\n"], "mappings": "AAAA,OAAOA,SAAS,MAAM,gBAAgB;AACtC,OAAOC,QAAQ,MAAM,eAAe;AAEpC,eAAe,SAASC,aAAaA,CAACC,MAAM,EAAuB;EAAA,IAArBC,OAAO,GAAAC,SAAA,CAAAC,MAAA,QAAAD,SAAA,QAAAE,SAAA,GAAAF,SAAA,MAAGL,SAAS;EAC/D,IAAII,OAAO,CAACE,MAAM,KAAK,CAAC,EAAE,OAAOL,QAAQ,CAACE,MAAM,EAAEC,OAAO,CAAC;EAC1D,IAAII,QAAQ;EACZ,IAAIC,GAAG,GAAG,CAAC,CAAC;EACZ,IAAIC,KAAK,GAAG,CAAC,CAAC;EACd,KAAK,MAAMC,KAAK,IAAIR,MAAM,EAAE;IAC1B,EAAEO,KAAK;IACP,IAAID,GAAG,GAAG,CAAC,GACLL,OAAO,CAACO,KAAK,EAAEA,KAAK,CAAC,KAAK,CAAC,GAC3BP,OAAO,CAACO,KAAK,EAAEH,QAAQ,CAAC,GAAG,CAAC,EAAE;MAClCA,QAAQ,GAAGG,KAAK;MAChBF,GAAG,GAAGC,KAAK;IACb;EACF;EACA,OAAOD,GAAG;AACZ", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}