{"ast": null, "code": "import { constructNow } from \"./constructNow.mjs\";\nimport { isSameISOWeek } from \"./isSameISOWeek.mjs\";\n\n/**\n * @name isThisISOWeek\n * @category ISO Week Helpers\n * @summary Is the given date in the same ISO week as the current date?\n * @pure false\n *\n * @description\n * Is the given date in the same ISO week as the current date?\n *\n * ISO week-numbering year: http://en.wikipedia.org/wiki/ISO_week_date\n *\n * @typeParam DateType - The `Date` type, the function operates on. Gets inferred from passed arguments. Allows to use extensions like [`UTCDate`](https://github.com/date-fns/utc).\n *\n * @param date - The date to check\n *\n * @returns The date is in this ISO week\n *\n * @example\n * // If today is 25 September 2014, is 22 September 2014 in this ISO week?\n * const result = isThisISOWeek(new Date(2014, 8, 22))\n * //=> true\n */\n\nexport function isThisISOWeek(date) {\n  return isSameISOWeek(date, constructNow(date));\n}\n\n// Fallback for modularized imports:\nexport default isThisISOWeek;", "map": {"version": 3, "names": ["constructNow", "isSameISOWeek", "isThisISOWeek", "date"], "sources": ["C:/CMS/webapp/frontend/node_modules/date-fns/isThisISOWeek.mjs"], "sourcesContent": ["import { constructNow } from \"./constructNow.mjs\";\nimport { isSameISOWeek } from \"./isSameISOWeek.mjs\";\n\n/**\n * @name isThisISOWeek\n * @category ISO Week Helpers\n * @summary Is the given date in the same ISO week as the current date?\n * @pure false\n *\n * @description\n * Is the given date in the same ISO week as the current date?\n *\n * ISO week-numbering year: http://en.wikipedia.org/wiki/ISO_week_date\n *\n * @typeParam DateType - The `Date` type, the function operates on. Gets inferred from passed arguments. Allows to use extensions like [`UTCDate`](https://github.com/date-fns/utc).\n *\n * @param date - The date to check\n *\n * @returns The date is in this ISO week\n *\n * @example\n * // If today is 25 September 2014, is 22 September 2014 in this ISO week?\n * const result = isThisISOWeek(new Date(2014, 8, 22))\n * //=> true\n */\n\nexport function isThisISOWeek(date) {\n  return isSameISOWeek(date, constructNow(date));\n}\n\n// Fallback for modularized imports:\nexport default isThisISOWeek;\n"], "mappings": "AAAA,SAASA,YAAY,QAAQ,oBAAoB;AACjD,SAASC,aAAa,QAAQ,qBAAqB;;AAEnD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA,OAAO,SAASC,aAAaA,CAACC,IAAI,EAAE;EAClC,OAAOF,aAAa,CAACE,IAAI,EAAEH,YAAY,CAACG,IAAI,CAAC,CAAC;AAChD;;AAEA;AACA,eAAeD,aAAa", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}