{"ast": null, "code": "import axios from'axios';import config from'../config';import axiosInstance from'./axiosConfig';const API_URL=config.API_URL;const excelService={// Importa cavi da Excel\nimportCavi:async(cantiereId,formData)=>{try{// Assicurati che cantiereId sia un numero\nconst cantiereIdNum=parseInt(cantiereId,10);if(isNaN(cantiereIdNum)){throw new Error(`ID cantiere non valido: ${cantiereId}`);}// Modifica la configurazione per l'upload di file\nconst config={headers:{'Content-Type':'multipart/form-data','Authorization':`Bearer ${localStorage.getItem('token')}`}};const response=await axios.post(`${API_URL}/excel/${cantiereIdNum}/import-cavi`,formData,config);return response.data;}catch(error){console.error('Import cavi error:',error);throw error.response?error.response.data:error;}},// Importa parco bobine da Excel\nimportParcoBobine:async(cantiereId,formData)=>{try{// Assicurati che cantiereId sia un numero\nconst cantiereIdNum=parseInt(cantiereId,10);if(isNaN(cantiereIdNum)){throw new Error(`ID cantiere non valido: ${cantiereId}`);}// Modifica la configurazione per l'upload di file\nconst config={headers:{'Content-Type':'multipart/form-data','Authorization':`Bearer ${localStorage.getItem('token')}`}};const response=await axios.post(`${API_URL}/excel/${cantiereIdNum}/import-parco-bobine`,formData,config);return response.data;}catch(error){console.error('Import parco bobine error:',error);throw error.response?error.response.data:error;}},// Crea template Excel per cavi\ncreateCaviTemplate:async()=>{try{// Usa axiosInstance per fare la richiesta con il token di autenticazione\nconst response=await axiosInstance.get('/excel/template-cavi',{responseType:'blob'// Importante per scaricare file binari\n});// Crea un URL per il blob e avvia il download\nconst url=window.URL.createObjectURL(new Blob([response.data]));const link=document.createElement('a');link.href=url;link.setAttribute('download','template_cavi.xlsx');document.body.appendChild(link);link.click();link.remove();return{success:true};}catch(error){console.error('Create cavi template error:',error);throw error.response?error.response.data:error;}},// Crea template Excel per parco bobine\ncreateParcoBobineTemplate:async()=>{try{// Usa axiosInstance per fare la richiesta con il token di autenticazione\nconst response=await axiosInstance.get('/excel/template-parco-bobine',{responseType:'blob'// Importante per scaricare file binari\n});// Crea un URL per il blob e avvia il download\nconst url=window.URL.createObjectURL(new Blob([response.data]));const link=document.createElement('a');link.href=url;link.setAttribute('download','template_parco_bobine.xlsx');document.body.appendChild(link);link.click();link.remove();return{success:true};}catch(error){console.error('Create parco bobine template error:',error);throw error.response?error.response.data:error;}},// Esporta cavi in Excel\nexportCavi:async cantiereId=>{try{// Assicurati che cantiereId sia un numero\nconst cantiereIdNum=parseInt(cantiereId,10);if(isNaN(cantiereIdNum)){throw new Error(`ID cantiere non valido: ${cantiereId}`);}// Usa axiosInstance per fare la richiesta con il token di autenticazione\nconst response=await axiosInstance.get(`/excel/${cantiereIdNum}/export-cavi`,{responseType:'blob'// Importante per scaricare file binari\n});// Crea un URL per il blob e avvia il download\nconst url=window.URL.createObjectURL(new Blob([response.data]));const link=document.createElement('a');link.href=url;link.setAttribute('download',`export_cavi_${cantiereIdNum}.xlsx`);document.body.appendChild(link);link.click();link.remove();return{success:true};}catch(error){console.error('Export cavi error:',error);throw error.response?error.response.data:error;}},// Esporta parco bobine in Excel\nexportParcoBobine:async cantiereId=>{try{// Assicurati che cantiereId sia un numero\nconst cantiereIdNum=parseInt(cantiereId,10);if(isNaN(cantiereIdNum)){throw new Error(`ID cantiere non valido: ${cantiereId}`);}// Usa axiosInstance per fare la richiesta con il token di autenticazione\nconst response=await axiosInstance.get(`/excel/${cantiereIdNum}/export-parco-bobine`,{responseType:'blob'// Importante per scaricare file binari\n});// Crea un URL per il blob e avvia il download\nconst url=window.URL.createObjectURL(new Blob([response.data]));const link=document.createElement('a');link.href=url;link.setAttribute('download',`export_parco_bobine_${cantiereIdNum}.xlsx`);document.body.appendChild(link);link.click();link.remove();return{success:true};}catch(error){console.error('Export parco bobine error:',error);throw error.response?error.response.data:error;}}};export default excelService;", "map": {"version": 3, "names": ["axios", "config", "axiosInstance", "API_URL", "excelService", "importCavi", "cantiereId", "formData", "cantiereIdNum", "parseInt", "isNaN", "Error", "headers", "localStorage", "getItem", "response", "post", "data", "error", "console", "importParcoBobine", "createCaviTemplate", "get", "responseType", "url", "window", "URL", "createObjectURL", "Blob", "link", "document", "createElement", "href", "setAttribute", "body", "append<PERSON><PERSON><PERSON>", "click", "remove", "success", "createParcoBobineTemplate", "exportCavi", "exportParcoBobine"], "sources": ["C:/Users/<USER>/PycharmProjects/CMS/webapp/frontend/src/services/excelService.js"], "sourcesContent": ["import axios from 'axios';\r\nimport config from '../config';\r\nimport axiosInstance from './axiosConfig';\r\n\r\nconst API_URL = config.API_URL;\r\n\r\nconst excelService = {\r\n  // Importa cavi da Excel\r\n  importCavi: async (cantiereId, formData) => {\r\n    try {\r\n      // Assicurati che cantiereId sia un numero\r\n      const cantiereIdNum = parseInt(cantiereId, 10);\r\n      if (isNaN(cantiereIdNum)) {\r\n        throw new Error(`ID cantiere non valido: ${cantiereId}`);\r\n      }\r\n\r\n      // Modifica la configurazione per l'upload di file\r\n      const config = {\r\n        headers: {\r\n          'Content-Type': 'multipart/form-data',\r\n          'Authorization': `Bearer ${localStorage.getItem('token')}`\r\n        }\r\n      };\r\n\r\n      const response = await axios.post(`${API_URL}/excel/${cantiereIdNum}/import-cavi`, formData, config);\r\n      return response.data;\r\n    } catch (error) {\r\n      console.error('Import cavi error:', error);\r\n      throw error.response ? error.response.data : error;\r\n    }\r\n  },\r\n\r\n  // Importa parco bobine da Excel\r\n  importParcoBobine: async (cantiereId, formData) => {\r\n    try {\r\n      // Assicurati che cantiereId sia un numero\r\n      const cantiereIdNum = parseInt(cantiereId, 10);\r\n      if (isNaN(cantiereIdNum)) {\r\n        throw new Error(`ID cantiere non valido: ${cantiereId}`);\r\n      }\r\n\r\n      // Modifica la configurazione per l'upload di file\r\n      const config = {\r\n        headers: {\r\n          'Content-Type': 'multipart/form-data',\r\n          'Authorization': `Bearer ${localStorage.getItem('token')}`\r\n        }\r\n      };\r\n\r\n      const response = await axios.post(`${API_URL}/excel/${cantiereIdNum}/import-parco-bobine`, formData, config);\r\n      return response.data;\r\n    } catch (error) {\r\n      console.error('Import parco bobine error:', error);\r\n      throw error.response ? error.response.data : error;\r\n    }\r\n  },\r\n\r\n  // Crea template Excel per cavi\r\n  createCaviTemplate: async () => {\r\n    try {\r\n      // Usa axiosInstance per fare la richiesta con il token di autenticazione\r\n      const response = await axiosInstance.get('/excel/template-cavi', {\r\n        responseType: 'blob' // Importante per scaricare file binari\r\n      });\r\n\r\n      // Crea un URL per il blob e avvia il download\r\n      const url = window.URL.createObjectURL(new Blob([response.data]));\r\n      const link = document.createElement('a');\r\n      link.href = url;\r\n      link.setAttribute('download', 'template_cavi.xlsx');\r\n      document.body.appendChild(link);\r\n      link.click();\r\n      link.remove();\r\n\r\n      return { success: true };\r\n    } catch (error) {\r\n      console.error('Create cavi template error:', error);\r\n      throw error.response ? error.response.data : error;\r\n    }\r\n  },\r\n\r\n  // Crea template Excel per parco bobine\r\n  createParcoBobineTemplate: async () => {\r\n    try {\r\n      // Usa axiosInstance per fare la richiesta con il token di autenticazione\r\n      const response = await axiosInstance.get('/excel/template-parco-bobine', {\r\n        responseType: 'blob' // Importante per scaricare file binari\r\n      });\r\n\r\n      // Crea un URL per il blob e avvia il download\r\n      const url = window.URL.createObjectURL(new Blob([response.data]));\r\n      const link = document.createElement('a');\r\n      link.href = url;\r\n      link.setAttribute('download', 'template_parco_bobine.xlsx');\r\n      document.body.appendChild(link);\r\n      link.click();\r\n      link.remove();\r\n\r\n      return { success: true };\r\n    } catch (error) {\r\n      console.error('Create parco bobine template error:', error);\r\n      throw error.response ? error.response.data : error;\r\n    }\r\n  },\r\n\r\n\r\n  // Esporta cavi in Excel\r\n  exportCavi: async (cantiereId) => {\r\n    try {\r\n      // Assicurati che cantiereId sia un numero\r\n      const cantiereIdNum = parseInt(cantiereId, 10);\r\n      if (isNaN(cantiereIdNum)) {\r\n        throw new Error(`ID cantiere non valido: ${cantiereId}`);\r\n      }\r\n\r\n      // Usa axiosInstance per fare la richiesta con il token di autenticazione\r\n      const response = await axiosInstance.get(`/excel/${cantiereIdNum}/export-cavi`, {\r\n        responseType: 'blob' // Importante per scaricare file binari\r\n      });\r\n\r\n      // Crea un URL per il blob e avvia il download\r\n      const url = window.URL.createObjectURL(new Blob([response.data]));\r\n      const link = document.createElement('a');\r\n      link.href = url;\r\n      link.setAttribute('download', `export_cavi_${cantiereIdNum}.xlsx`);\r\n      document.body.appendChild(link);\r\n      link.click();\r\n      link.remove();\r\n\r\n      return { success: true };\r\n    } catch (error) {\r\n      console.error('Export cavi error:', error);\r\n      throw error.response ? error.response.data : error;\r\n    }\r\n  },\r\n\r\n  // Esporta parco bobine in Excel\r\n  exportParcoBobine: async (cantiereId) => {\r\n    try {\r\n      // Assicurati che cantiereId sia un numero\r\n      const cantiereIdNum = parseInt(cantiereId, 10);\r\n      if (isNaN(cantiereIdNum)) {\r\n        throw new Error(`ID cantiere non valido: ${cantiereId}`);\r\n      }\r\n\r\n      // Usa axiosInstance per fare la richiesta con il token di autenticazione\r\n      const response = await axiosInstance.get(`/excel/${cantiereIdNum}/export-parco-bobine`, {\r\n        responseType: 'blob' // Importante per scaricare file binari\r\n      });\r\n\r\n      // Crea un URL per il blob e avvia il download\r\n      const url = window.URL.createObjectURL(new Blob([response.data]));\r\n      const link = document.createElement('a');\r\n      link.href = url;\r\n      link.setAttribute('download', `export_parco_bobine_${cantiereIdNum}.xlsx`);\r\n      document.body.appendChild(link);\r\n      link.click();\r\n      link.remove();\r\n\r\n      return { success: true };\r\n    } catch (error) {\r\n      console.error('Export parco bobine error:', error);\r\n      throw error.response ? error.response.data : error;\r\n    }\r\n  }\r\n};\r\n\r\nexport default excelService;\r\n"], "mappings": "AAAA,MAAO,CAAAA,KAAK,KAAM,OAAO,CACzB,MAAO,CAAAC,MAAM,KAAM,WAAW,CAC9B,MAAO,CAAAC,aAAa,KAAM,eAAe,CAEzC,KAAM,CAAAC,OAAO,CAAGF,MAAM,CAACE,OAAO,CAE9B,KAAM,CAAAC,YAAY,CAAG,CACnB;AACAC,UAAU,CAAE,KAAAA,CAAOC,UAAU,CAAEC,QAAQ,GAAK,CAC1C,GAAI,CACF;AACA,KAAM,CAAAC,aAAa,CAAGC,QAAQ,CAACH,UAAU,CAAE,EAAE,CAAC,CAC9C,GAAII,KAAK,CAACF,aAAa,CAAC,CAAE,CACxB,KAAM,IAAI,CAAAG,KAAK,CAAC,2BAA2BL,UAAU,EAAE,CAAC,CAC1D,CAEA;AACA,KAAM,CAAAL,MAAM,CAAG,CACbW,OAAO,CAAE,CACP,cAAc,CAAE,qBAAqB,CACrC,eAAe,CAAE,UAAUC,YAAY,CAACC,OAAO,CAAC,OAAO,CAAC,EAC1D,CACF,CAAC,CAED,KAAM,CAAAC,QAAQ,CAAG,KAAM,CAAAf,KAAK,CAACgB,IAAI,CAAC,GAAGb,OAAO,UAAUK,aAAa,cAAc,CAAED,QAAQ,CAAEN,MAAM,CAAC,CACpG,MAAO,CAAAc,QAAQ,CAACE,IAAI,CACtB,CAAE,MAAOC,KAAK,CAAE,CACdC,OAAO,CAACD,KAAK,CAAC,oBAAoB,CAAEA,KAAK,CAAC,CAC1C,KAAM,CAAAA,KAAK,CAACH,QAAQ,CAAGG,KAAK,CAACH,QAAQ,CAACE,IAAI,CAAGC,KAAK,CACpD,CACF,CAAC,CAED;AACAE,iBAAiB,CAAE,KAAAA,CAAOd,UAAU,CAAEC,QAAQ,GAAK,CACjD,GAAI,CACF;AACA,KAAM,CAAAC,aAAa,CAAGC,QAAQ,CAACH,UAAU,CAAE,EAAE,CAAC,CAC9C,GAAII,KAAK,CAACF,aAAa,CAAC,CAAE,CACxB,KAAM,IAAI,CAAAG,KAAK,CAAC,2BAA2BL,UAAU,EAAE,CAAC,CAC1D,CAEA;AACA,KAAM,CAAAL,MAAM,CAAG,CACbW,OAAO,CAAE,CACP,cAAc,CAAE,qBAAqB,CACrC,eAAe,CAAE,UAAUC,YAAY,CAACC,OAAO,CAAC,OAAO,CAAC,EAC1D,CACF,CAAC,CAED,KAAM,CAAAC,QAAQ,CAAG,KAAM,CAAAf,KAAK,CAACgB,IAAI,CAAC,GAAGb,OAAO,UAAUK,aAAa,sBAAsB,CAAED,QAAQ,CAAEN,MAAM,CAAC,CAC5G,MAAO,CAAAc,QAAQ,CAACE,IAAI,CACtB,CAAE,MAAOC,KAAK,CAAE,CACdC,OAAO,CAACD,KAAK,CAAC,4BAA4B,CAAEA,KAAK,CAAC,CAClD,KAAM,CAAAA,KAAK,CAACH,QAAQ,CAAGG,KAAK,CAACH,QAAQ,CAACE,IAAI,CAAGC,KAAK,CACpD,CACF,CAAC,CAED;AACAG,kBAAkB,CAAE,KAAAA,CAAA,GAAY,CAC9B,GAAI,CACF;AACA,KAAM,CAAAN,QAAQ,CAAG,KAAM,CAAAb,aAAa,CAACoB,GAAG,CAAC,sBAAsB,CAAE,CAC/DC,YAAY,CAAE,MAAO;AACvB,CAAC,CAAC,CAEF;AACA,KAAM,CAAAC,GAAG,CAAGC,MAAM,CAACC,GAAG,CAACC,eAAe,CAAC,GAAI,CAAAC,IAAI,CAAC,CAACb,QAAQ,CAACE,IAAI,CAAC,CAAC,CAAC,CACjE,KAAM,CAAAY,IAAI,CAAGC,QAAQ,CAACC,aAAa,CAAC,GAAG,CAAC,CACxCF,IAAI,CAACG,IAAI,CAAGR,GAAG,CACfK,IAAI,CAACI,YAAY,CAAC,UAAU,CAAE,oBAAoB,CAAC,CACnDH,QAAQ,CAACI,IAAI,CAACC,WAAW,CAACN,IAAI,CAAC,CAC/BA,IAAI,CAACO,KAAK,CAAC,CAAC,CACZP,IAAI,CAACQ,MAAM,CAAC,CAAC,CAEb,MAAO,CAAEC,OAAO,CAAE,IAAK,CAAC,CAC1B,CAAE,MAAOpB,KAAK,CAAE,CACdC,OAAO,CAACD,KAAK,CAAC,6BAA6B,CAAEA,KAAK,CAAC,CACnD,KAAM,CAAAA,KAAK,CAACH,QAAQ,CAAGG,KAAK,CAACH,QAAQ,CAACE,IAAI,CAAGC,KAAK,CACpD,CACF,CAAC,CAED;AACAqB,yBAAyB,CAAE,KAAAA,CAAA,GAAY,CACrC,GAAI,CACF;AACA,KAAM,CAAAxB,QAAQ,CAAG,KAAM,CAAAb,aAAa,CAACoB,GAAG,CAAC,8BAA8B,CAAE,CACvEC,YAAY,CAAE,MAAO;AACvB,CAAC,CAAC,CAEF;AACA,KAAM,CAAAC,GAAG,CAAGC,MAAM,CAACC,GAAG,CAACC,eAAe,CAAC,GAAI,CAAAC,IAAI,CAAC,CAACb,QAAQ,CAACE,IAAI,CAAC,CAAC,CAAC,CACjE,KAAM,CAAAY,IAAI,CAAGC,QAAQ,CAACC,aAAa,CAAC,GAAG,CAAC,CACxCF,IAAI,CAACG,IAAI,CAAGR,GAAG,CACfK,IAAI,CAACI,YAAY,CAAC,UAAU,CAAE,4BAA4B,CAAC,CAC3DH,QAAQ,CAACI,IAAI,CAACC,WAAW,CAACN,IAAI,CAAC,CAC/BA,IAAI,CAACO,KAAK,CAAC,CAAC,CACZP,IAAI,CAACQ,MAAM,CAAC,CAAC,CAEb,MAAO,CAAEC,OAAO,CAAE,IAAK,CAAC,CAC1B,CAAE,MAAOpB,KAAK,CAAE,CACdC,OAAO,CAACD,KAAK,CAAC,qCAAqC,CAAEA,KAAK,CAAC,CAC3D,KAAM,CAAAA,KAAK,CAACH,QAAQ,CAAGG,KAAK,CAACH,QAAQ,CAACE,IAAI,CAAGC,KAAK,CACpD,CACF,CAAC,CAGD;AACAsB,UAAU,CAAE,KAAO,CAAAlC,UAAU,EAAK,CAChC,GAAI,CACF;AACA,KAAM,CAAAE,aAAa,CAAGC,QAAQ,CAACH,UAAU,CAAE,EAAE,CAAC,CAC9C,GAAII,KAAK,CAACF,aAAa,CAAC,CAAE,CACxB,KAAM,IAAI,CAAAG,KAAK,CAAC,2BAA2BL,UAAU,EAAE,CAAC,CAC1D,CAEA;AACA,KAAM,CAAAS,QAAQ,CAAG,KAAM,CAAAb,aAAa,CAACoB,GAAG,CAAC,UAAUd,aAAa,cAAc,CAAE,CAC9Ee,YAAY,CAAE,MAAO;AACvB,CAAC,CAAC,CAEF;AACA,KAAM,CAAAC,GAAG,CAAGC,MAAM,CAACC,GAAG,CAACC,eAAe,CAAC,GAAI,CAAAC,IAAI,CAAC,CAACb,QAAQ,CAACE,IAAI,CAAC,CAAC,CAAC,CACjE,KAAM,CAAAY,IAAI,CAAGC,QAAQ,CAACC,aAAa,CAAC,GAAG,CAAC,CACxCF,IAAI,CAACG,IAAI,CAAGR,GAAG,CACfK,IAAI,CAACI,YAAY,CAAC,UAAU,CAAE,eAAezB,aAAa,OAAO,CAAC,CAClEsB,QAAQ,CAACI,IAAI,CAACC,WAAW,CAACN,IAAI,CAAC,CAC/BA,IAAI,CAACO,KAAK,CAAC,CAAC,CACZP,IAAI,CAACQ,MAAM,CAAC,CAAC,CAEb,MAAO,CAAEC,OAAO,CAAE,IAAK,CAAC,CAC1B,CAAE,MAAOpB,KAAK,CAAE,CACdC,OAAO,CAACD,KAAK,CAAC,oBAAoB,CAAEA,KAAK,CAAC,CAC1C,KAAM,CAAAA,KAAK,CAACH,QAAQ,CAAGG,KAAK,CAACH,QAAQ,CAACE,IAAI,CAAGC,KAAK,CACpD,CACF,CAAC,CAED;AACAuB,iBAAiB,CAAE,KAAO,CAAAnC,UAAU,EAAK,CACvC,GAAI,CACF;AACA,KAAM,CAAAE,aAAa,CAAGC,QAAQ,CAACH,UAAU,CAAE,EAAE,CAAC,CAC9C,GAAII,KAAK,CAACF,aAAa,CAAC,CAAE,CACxB,KAAM,IAAI,CAAAG,KAAK,CAAC,2BAA2BL,UAAU,EAAE,CAAC,CAC1D,CAEA;AACA,KAAM,CAAAS,QAAQ,CAAG,KAAM,CAAAb,aAAa,CAACoB,GAAG,CAAC,UAAUd,aAAa,sBAAsB,CAAE,CACtFe,YAAY,CAAE,MAAO;AACvB,CAAC,CAAC,CAEF;AACA,KAAM,CAAAC,GAAG,CAAGC,MAAM,CAACC,GAAG,CAACC,eAAe,CAAC,GAAI,CAAAC,IAAI,CAAC,CAACb,QAAQ,CAACE,IAAI,CAAC,CAAC,CAAC,CACjE,KAAM,CAAAY,IAAI,CAAGC,QAAQ,CAACC,aAAa,CAAC,GAAG,CAAC,CACxCF,IAAI,CAACG,IAAI,CAAGR,GAAG,CACfK,IAAI,CAACI,YAAY,CAAC,UAAU,CAAE,uBAAuBzB,aAAa,OAAO,CAAC,CAC1EsB,QAAQ,CAACI,IAAI,CAACC,WAAW,CAACN,IAAI,CAAC,CAC/BA,IAAI,CAACO,KAAK,CAAC,CAAC,CACZP,IAAI,CAACQ,MAAM,CAAC,CAAC,CAEb,MAAO,CAAEC,OAAO,CAAE,IAAK,CAAC,CAC1B,CAAE,MAAOpB,KAAK,CAAE,CACdC,OAAO,CAACD,KAAK,CAAC,4BAA4B,CAAEA,KAAK,CAAC,CAClD,KAAM,CAAAA,KAAK,CAACH,QAAQ,CAAGG,KAAK,CAACH,QAAQ,CAACE,IAAI,CAAGC,KAAK,CACpD,CACF,CACF,CAAC,CAED,cAAe,CAAAd,YAAY", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}