{"ast": null, "code": "'use client';\n\nimport createTheme from './createTheme';\nimport useThemeWithoutDefault from './useThemeWithoutDefault';\nexport const systemDefaultTheme = createTheme();\nfunction useTheme(defaultTheme = systemDefaultTheme) {\n  return useThemeWithoutDefault(defaultTheme);\n}\nexport default useTheme;", "map": {"version": 3, "names": ["createTheme", "useThemeWithoutDefault", "systemDefaultTheme", "useTheme", "defaultTheme"], "sources": ["C:/Users/<USER>/PycharmProjects/CMS/webapp/frontend/node_modules/@mui/system/esm/useTheme.js"], "sourcesContent": ["'use client';\n\nimport createTheme from './createTheme';\nimport useThemeWithoutDefault from './useThemeWithoutDefault';\nexport const systemDefaultTheme = createTheme();\nfunction useTheme(defaultTheme = systemDefaultTheme) {\n  return useThemeWithoutDefault(defaultTheme);\n}\nexport default useTheme;"], "mappings": "AAAA,YAAY;;AAEZ,OAAOA,WAAW,MAAM,eAAe;AACvC,OAAOC,sBAAsB,MAAM,0BAA0B;AAC7D,OAAO,MAAMC,kBAAkB,GAAGF,WAAW,CAAC,CAAC;AAC/C,SAASG,QAAQA,CAACC,YAAY,GAAGF,kBAAkB,EAAE;EACnD,OAAOD,sBAAsB,CAACG,YAAY,CAAC;AAC7C;AACA,eAAeD,QAAQ", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}