{"ast": null, "code": "import { startOfQuarter } from \"./startOfQuarter.mjs\";\n\n/**\n * @name isSameQuarter\n * @category Quarter Helpers\n * @summary Are the given dates in the same quarter (and year)?\n *\n * @description\n * Are the given dates in the same quarter (and year)?\n *\n * @typeParam DateType - The `Date` type, the function operates on. Gets inferred from passed arguments. Allows to use extensions like [`UTCDate`](https://github.com/date-fns/utc).\n *\n * @param dateLeft - The first date to check\n * @param dateRight - The second date to check\n\n * @returns The dates are in the same quarter (and year)\n *\n * @example\n * // Are 1 January 2014 and 8 March 2014 in the same quarter?\n * const result = isSameQuarter(new Date(2014, 0, 1), new Date(2014, 2, 8))\n * //=> true\n *\n * @example\n * // Are 1 January 2014 and 1 January 2015 in the same quarter?\n * const result = isSameQuarter(new Date(2014, 0, 1), new Date(2015, 0, 1))\n * //=> false\n */\nexport function isSameQuarter(dateLeft, dateRight) {\n  const dateLeftStartOfQuarter = startOfQuarter(dateLeft);\n  const dateRightStartOfQuarter = startOfQuarter(dateRight);\n  return +dateLeftStartOfQuarter === +dateRightStartOfQuarter;\n}\n\n// Fallback for modularized imports:\nexport default isSameQuarter;", "map": {"version": 3, "names": ["startOfQuarter", "isSameQuarter", "dateLeft", "dateRight", "dateLeftStartOfQuarter", "dateRightStartOfQuarter"], "sources": ["C:/CMS/webapp/frontend/node_modules/date-fns/isSameQuarter.mjs"], "sourcesContent": ["import { startOfQuarter } from \"./startOfQuarter.mjs\";\n\n/**\n * @name isSameQuarter\n * @category Quarter Helpers\n * @summary Are the given dates in the same quarter (and year)?\n *\n * @description\n * Are the given dates in the same quarter (and year)?\n *\n * @typeParam DateType - The `Date` type, the function operates on. Gets inferred from passed arguments. Allows to use extensions like [`UTCDate`](https://github.com/date-fns/utc).\n *\n * @param dateLeft - The first date to check\n * @param dateRight - The second date to check\n\n * @returns The dates are in the same quarter (and year)\n *\n * @example\n * // Are 1 January 2014 and 8 March 2014 in the same quarter?\n * const result = isSameQuarter(new Date(2014, 0, 1), new Date(2014, 2, 8))\n * //=> true\n *\n * @example\n * // Are 1 January 2014 and 1 January 2015 in the same quarter?\n * const result = isSameQuarter(new Date(2014, 0, 1), new Date(2015, 0, 1))\n * //=> false\n */\nexport function isSameQuarter(dateLeft, dateRight) {\n  const dateLeftStartOfQuarter = startOfQuarter(dateLeft);\n  const dateRightStartOfQuarter = startOfQuarter(dateRight);\n\n  return +dateLeftStartOfQuarter === +dateRightStartOfQuarter;\n}\n\n// Fallback for modularized imports:\nexport default isSameQuarter;\n"], "mappings": "AAAA,SAASA,cAAc,QAAQ,sBAAsB;;AAErD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO,SAASC,aAAaA,CAACC,QAAQ,EAAEC,SAAS,EAAE;EACjD,MAAMC,sBAAsB,GAAGJ,cAAc,CAACE,QAAQ,CAAC;EACvD,MAAMG,uBAAuB,GAAGL,cAAc,CAACG,SAAS,CAAC;EAEzD,OAAO,CAACC,sBAAsB,KAAK,CAACC,uBAAuB;AAC7D;;AAEA;AACA,eAAeJ,aAAa", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}