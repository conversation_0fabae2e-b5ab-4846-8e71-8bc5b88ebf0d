{"ast": null, "code": "import React from'react';import{Box,Typography,Paper,Button,IconButton,Alert}from'@mui/material';import{ArrowBack as ArrowBackIcon,Refresh as RefreshIcon,Home as HomeIcon}from'@mui/icons-material';import{useNavigate}from'react-router-dom';import{useAuth}from'../../context/AuthContext';import AdminHomeButton from'../../components/common/AdminHomeButton';import GestioneComande from'../../components/cavi/GestioneComande';import{jsx as _jsx,jsxs as _jsxs}from\"react/jsx-runtime\";const GestioneComandeePage=()=>{const{isImpersonating}=useAuth();const navigate=useNavigate();// Recupera l'ID del cantiere dal localStorage\nconst cantiereId=parseInt(localStorage.getItem('selectedCantiereId'),10);const cantiereName=localStorage.getItem('selectedCantiereName')||`Cantiere ${cantiereId}`;// Torna alla lista dei cantieri\nconst handleBackToCantieri=()=>{navigate('/dashboard/cantieri');};// Gestisce le notifiche\nconst handleSuccess=message=>{// Qui puoi implementare una notifica di successo se necessario\nconsole.log('Successo:',message);};const handleError=message=>{// Qui puoi implementare una notifica di errore se necessario\nconsole.error('Errore:',message);};if(!cantiereId||isNaN(cantiereId)){return/*#__PURE__*/_jsxs(Box,{children:[/*#__PURE__*/_jsx(Alert,{severity:\"error\",sx:{mb:2},children:\"Nessun cantiere selezionato o ID cantiere non valido. Torna alla pagina dei cantieri.\"}),/*#__PURE__*/_jsx(Button,{variant:\"contained\",startIcon:/*#__PURE__*/_jsx(ArrowBackIcon,{}),onClick:handleBackToCantieri,children:\"Torna ai Cantieri\"})]});}return/*#__PURE__*/_jsxs(Box,{children:[/*#__PURE__*/_jsxs(Box,{sx:{mb:3,display:'flex',alignItems:'center',justifyContent:'space-between'},children:[/*#__PURE__*/_jsxs(Box,{sx:{display:'flex',alignItems:'center'},children:[/*#__PURE__*/_jsx(IconButton,{onClick:handleBackToCantieri,sx:{mr:1},children:/*#__PURE__*/_jsx(ArrowBackIcon,{})}),/*#__PURE__*/_jsx(Typography,{variant:\"h4\",children:\"Gestione Comande\"}),/*#__PURE__*/_jsx(IconButton,{onClick:()=>window.location.reload(),sx:{ml:2},color:\"primary\",title:\"Ricarica la pagina\",children:/*#__PURE__*/_jsx(RefreshIcon,{})})]}),/*#__PURE__*/_jsx(AdminHomeButton,{})]}),/*#__PURE__*/_jsx(Paper,{sx:{mb:3,p:2},children:/*#__PURE__*/_jsxs(Box,{sx:{display:'flex',justifyContent:'space-between',alignItems:'center'},children:[/*#__PURE__*/_jsxs(Typography,{variant:\"h6\",children:[\"Cantiere: \",cantiereName,\" (ID: \",cantiereId,\")\"]}),/*#__PURE__*/_jsx(Button,{variant:\"contained\",color:\"primary\",startIcon:/*#__PURE__*/_jsx(ArrowBackIcon,{}),onClick:handleBackToCantieri,children:\"Torna ai Cantieri\"})]})}),/*#__PURE__*/_jsx(GestioneComande,{cantiereId:cantiereId,onSuccess:handleSuccess,onError:handleError})]});};export default GestioneComandeePage;", "map": {"version": 3, "names": ["React", "Box", "Typography", "Paper", "<PERSON><PERSON>", "IconButton", "<PERSON><PERSON>", "ArrowBack", "ArrowBackIcon", "Refresh", "RefreshIcon", "Home", "HomeIcon", "useNavigate", "useAuth", "AdminHomeButton", "GestioneComande", "jsx", "_jsx", "jsxs", "_jsxs", "GestioneComandeePage", "isImpersonating", "navigate", "cantiereId", "parseInt", "localStorage", "getItem", "cantiereName", "handleBackToCantieri", "handleSuccess", "message", "console", "log", "handleError", "error", "isNaN", "children", "severity", "sx", "mb", "variant", "startIcon", "onClick", "display", "alignItems", "justifyContent", "mr", "window", "location", "reload", "ml", "color", "title", "p", "onSuccess", "onError"], "sources": ["C:/Users/<USER>/PycharmProjects/CMS/webapp/frontend/src/pages/cavi/GestioneComandeePage.js"], "sourcesContent": ["import React from 'react';\nimport {\n  Box,\n  Typography,\n  Paper,\n  Button,\n  IconButton,\n  Alert\n} from '@mui/material';\nimport {\n  ArrowBack as ArrowBackIcon,\n  Refresh as RefreshIcon,\n  Home as HomeIcon\n} from '@mui/icons-material';\nimport { useNavigate } from 'react-router-dom';\nimport { useAuth } from '../../context/AuthContext';\nimport AdminHomeButton from '../../components/common/AdminHomeButton';\nimport GestioneComande from '../../components/cavi/GestioneComande';\n\nconst GestioneComandeePage = () => {\n  const { isImpersonating } = useAuth();\n  const navigate = useNavigate();\n\n  // Recupera l'ID del cantiere dal localStorage\n  const cantiereId = parseInt(localStorage.getItem('selectedCantiereId'), 10);\n  const cantiereName = localStorage.getItem('selectedCantiereName') || `Cantiere ${cantiereId}`;\n\n  // Torna alla lista dei cantieri\n  const handleBackToCantieri = () => {\n    navigate('/dashboard/cantieri');\n  };\n\n  \n\n  // Gestisce le notifiche\n  const handleSuccess = (message) => {\n    // Qui puoi implementare una notifica di successo se necessario\n    console.log('Successo:', message);\n  };\n\n  const handleError = (message) => {\n    // Qui puoi implementare una notifica di errore se necessario\n    console.error('Errore:', message);\n  };\n\n  if (!cantiereId || isNaN(cantiereId)) {\n    return (\n      <Box>\n        <Alert severity=\"error\" sx={{ mb: 2 }}>\n          Nessun cantiere selezionato o ID cantiere non valido. Torna alla pagina dei cantieri.\n        </Alert>\n        <Button\n          variant=\"contained\"\n          startIcon={<ArrowBackIcon />}\n          onClick={handleBackToCantieri}\n        >\n          Torna ai Cantieri\n        </Button>\n      </Box>\n    );\n  }\n\n  return (\n    <Box>\n      <Box sx={{ mb: 3, display: 'flex', alignItems: 'center', justifyContent: 'space-between' }}>\n        <Box sx={{ display: 'flex', alignItems: 'center' }}>\n          <IconButton onClick={handleBackToCantieri} sx={{ mr: 1 }}>\n            <ArrowBackIcon />\n          </IconButton>\n          <Typography variant=\"h4\">\n            Gestione Comande\n          </Typography>\n          <IconButton\n            onClick={() => window.location.reload()}\n            sx={{ ml: 2 }}\n            color=\"primary\"\n            title=\"Ricarica la pagina\"\n          >\n            <RefreshIcon />\n          </IconButton>\n        </Box>\n        <AdminHomeButton />\n      </Box>\n\n      <Paper sx={{ mb: 3, p: 2 }}>\n        <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>\n          <Typography variant=\"h6\">\n            Cantiere: {cantiereName} (ID: {cantiereId})\n          </Typography>\n          <Button\n            variant=\"contained\"\n            color=\"primary\"\n            startIcon={<ArrowBackIcon />}\n            onClick={handleBackToCantieri}\n          >\n            Torna ai Cantieri\n          </Button>\n        </Box>\n      </Paper>\n\n      <GestioneComande\n        cantiereId={cantiereId}\n        onSuccess={handleSuccess}\n        onError={handleError}\n      />\n    </Box>\n  );\n};\n\nexport default GestioneComandeePage;\n"], "mappings": "AAAA,MAAO,CAAAA,KAAK,KAAM,OAAO,CACzB,OACEC,GAAG,CACHC,UAAU,CACVC,KAAK,CACLC,MAAM,CACNC,UAAU,CACVC,KAAK,KACA,eAAe,CACtB,OACEC,SAAS,GAAI,CAAAC,aAAa,CAC1BC,OAAO,GAAI,CAAAC,WAAW,CACtBC,IAAI,GAAI,CAAAC,QAAQ,KACX,qBAAqB,CAC5B,OAASC,WAAW,KAAQ,kBAAkB,CAC9C,OAASC,OAAO,KAAQ,2BAA2B,CACnD,MAAO,CAAAC,eAAe,KAAM,yCAAyC,CACrE,MAAO,CAAAC,eAAe,KAAM,uCAAuC,CAAC,OAAAC,GAAA,IAAAC,IAAA,CAAAC,IAAA,IAAAC,KAAA,yBAEpE,KAAM,CAAAC,oBAAoB,CAAGA,CAAA,GAAM,CACjC,KAAM,CAAEC,eAAgB,CAAC,CAAGR,OAAO,CAAC,CAAC,CACrC,KAAM,CAAAS,QAAQ,CAAGV,WAAW,CAAC,CAAC,CAE9B;AACA,KAAM,CAAAW,UAAU,CAAGC,QAAQ,CAACC,YAAY,CAACC,OAAO,CAAC,oBAAoB,CAAC,CAAE,EAAE,CAAC,CAC3E,KAAM,CAAAC,YAAY,CAAGF,YAAY,CAACC,OAAO,CAAC,sBAAsB,CAAC,EAAI,YAAYH,UAAU,EAAE,CAE7F;AACA,KAAM,CAAAK,oBAAoB,CAAGA,CAAA,GAAM,CACjCN,QAAQ,CAAC,qBAAqB,CAAC,CACjC,CAAC,CAID;AACA,KAAM,CAAAO,aAAa,CAAIC,OAAO,EAAK,CACjC;AACAC,OAAO,CAACC,GAAG,CAAC,WAAW,CAAEF,OAAO,CAAC,CACnC,CAAC,CAED,KAAM,CAAAG,WAAW,CAAIH,OAAO,EAAK,CAC/B;AACAC,OAAO,CAACG,KAAK,CAAC,SAAS,CAAEJ,OAAO,CAAC,CACnC,CAAC,CAED,GAAI,CAACP,UAAU,EAAIY,KAAK,CAACZ,UAAU,CAAC,CAAE,CACpC,mBACEJ,KAAA,CAACnB,GAAG,EAAAoC,QAAA,eACFnB,IAAA,CAACZ,KAAK,EAACgC,QAAQ,CAAC,OAAO,CAACC,EAAE,CAAE,CAAEC,EAAE,CAAE,CAAE,CAAE,CAAAH,QAAA,CAAC,uFAEvC,CAAO,CAAC,cACRnB,IAAA,CAACd,MAAM,EACLqC,OAAO,CAAC,WAAW,CACnBC,SAAS,cAAExB,IAAA,CAACV,aAAa,GAAE,CAAE,CAC7BmC,OAAO,CAAEd,oBAAqB,CAAAQ,QAAA,CAC/B,mBAED,CAAQ,CAAC,EACN,CAAC,CAEV,CAEA,mBACEjB,KAAA,CAACnB,GAAG,EAAAoC,QAAA,eACFjB,KAAA,CAACnB,GAAG,EAACsC,EAAE,CAAE,CAAEC,EAAE,CAAE,CAAC,CAAEI,OAAO,CAAE,MAAM,CAAEC,UAAU,CAAE,QAAQ,CAAEC,cAAc,CAAE,eAAgB,CAAE,CAAAT,QAAA,eACzFjB,KAAA,CAACnB,GAAG,EAACsC,EAAE,CAAE,CAAEK,OAAO,CAAE,MAAM,CAAEC,UAAU,CAAE,QAAS,CAAE,CAAAR,QAAA,eACjDnB,IAAA,CAACb,UAAU,EAACsC,OAAO,CAAEd,oBAAqB,CAACU,EAAE,CAAE,CAAEQ,EAAE,CAAE,CAAE,CAAE,CAAAV,QAAA,cACvDnB,IAAA,CAACV,aAAa,GAAE,CAAC,CACP,CAAC,cACbU,IAAA,CAAChB,UAAU,EAACuC,OAAO,CAAC,IAAI,CAAAJ,QAAA,CAAC,kBAEzB,CAAY,CAAC,cACbnB,IAAA,CAACb,UAAU,EACTsC,OAAO,CAAEA,CAAA,GAAMK,MAAM,CAACC,QAAQ,CAACC,MAAM,CAAC,CAAE,CACxCX,EAAE,CAAE,CAAEY,EAAE,CAAE,CAAE,CAAE,CACdC,KAAK,CAAC,SAAS,CACfC,KAAK,CAAC,oBAAoB,CAAAhB,QAAA,cAE1BnB,IAAA,CAACR,WAAW,GAAE,CAAC,CACL,CAAC,EACV,CAAC,cACNQ,IAAA,CAACH,eAAe,GAAE,CAAC,EAChB,CAAC,cAENG,IAAA,CAACf,KAAK,EAACoC,EAAE,CAAE,CAAEC,EAAE,CAAE,CAAC,CAAEc,CAAC,CAAE,CAAE,CAAE,CAAAjB,QAAA,cACzBjB,KAAA,CAACnB,GAAG,EAACsC,EAAE,CAAE,CAAEK,OAAO,CAAE,MAAM,CAAEE,cAAc,CAAE,eAAe,CAAED,UAAU,CAAE,QAAS,CAAE,CAAAR,QAAA,eAClFjB,KAAA,CAAClB,UAAU,EAACuC,OAAO,CAAC,IAAI,CAAAJ,QAAA,EAAC,YACb,CAACT,YAAY,CAAC,QAAM,CAACJ,UAAU,CAAC,GAC5C,EAAY,CAAC,cACbN,IAAA,CAACd,MAAM,EACLqC,OAAO,CAAC,WAAW,CACnBW,KAAK,CAAC,SAAS,CACfV,SAAS,cAAExB,IAAA,CAACV,aAAa,GAAE,CAAE,CAC7BmC,OAAO,CAAEd,oBAAqB,CAAAQ,QAAA,CAC/B,mBAED,CAAQ,CAAC,EACN,CAAC,CACD,CAAC,cAERnB,IAAA,CAACF,eAAe,EACdQ,UAAU,CAAEA,UAAW,CACvB+B,SAAS,CAAEzB,aAAc,CACzB0B,OAAO,CAAEtB,WAAY,CACtB,CAAC,EACC,CAAC,CAEV,CAAC,CAED,cAAe,CAAAb,oBAAoB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}