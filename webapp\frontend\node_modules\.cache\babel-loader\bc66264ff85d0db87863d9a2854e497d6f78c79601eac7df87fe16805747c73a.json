{"ast": null, "code": "import { formatDistance } from \"./ar-SA/_lib/formatDistance.js\";\nimport { formatLong } from \"./ar-SA/_lib/formatLong.js\";\nimport { formatRelative } from \"./ar-SA/_lib/formatRelative.js\";\nimport { localize } from \"./ar-SA/_lib/localize.js\";\nimport { match } from \"./ar-SA/_lib/match.js\";\n\n/**\n * @category Locales\n * @summary Arabic locale (Sauid Arabic).\n * @language Arabic\n * @iso-639-2 ara\n * <AUTHOR> [@dalwadani](https://github.com/dalwadani)\n */\nexport const arSA = {\n  code: \"ar-SA\",\n  formatDistance: formatDistance,\n  formatLong: formatLong,\n  formatRelative: formatRelative,\n  localize: localize,\n  match: match,\n  options: {\n    weekStartsOn: 0 /* Sunday */,\n    firstWeekContainsDate: 1\n  }\n};\n\n// Fallback for modularized imports:\nexport default arSA;", "map": {"version": 3, "names": ["formatDistance", "formatLong", "formatRelative", "localize", "match", "arSA", "code", "options", "weekStartsOn", "firstWeekContainsDate"], "sources": ["C:/Users/<USER>/PycharmProjects/CMS/webapp/frontend/node_modules/date-fns/locale/ar-SA.js"], "sourcesContent": ["import { formatDistance } from \"./ar-SA/_lib/formatDistance.js\";\nimport { formatLong } from \"./ar-SA/_lib/formatLong.js\";\nimport { formatRelative } from \"./ar-SA/_lib/formatRelative.js\";\nimport { localize } from \"./ar-SA/_lib/localize.js\";\nimport { match } from \"./ar-SA/_lib/match.js\";\n\n/**\n * @category Locales\n * @summary Arabic locale (Sauid Arabic).\n * @language Arabic\n * @iso-639-2 ara\n * <AUTHOR> [@dalwadani](https://github.com/dalwadani)\n */\nexport const arSA = {\n  code: \"ar-SA\",\n  formatDistance: formatDistance,\n  formatLong: formatLong,\n  formatRelative: formatRelative,\n  localize: localize,\n  match: match,\n  options: {\n    weekStartsOn: 0 /* Sunday */,\n    firstWeekContainsDate: 1,\n  },\n};\n\n// Fallback for modularized imports:\nexport default arSA;\n"], "mappings": "AAAA,SAASA,cAAc,QAAQ,gCAAgC;AAC/D,SAASC,UAAU,QAAQ,4BAA4B;AACvD,SAASC,cAAc,QAAQ,gCAAgC;AAC/D,SAASC,QAAQ,QAAQ,0BAA0B;AACnD,SAASC,KAAK,QAAQ,uBAAuB;;AAE7C;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO,MAAMC,IAAI,GAAG;EAClBC,IAAI,EAAE,OAAO;EACbN,cAAc,EAAEA,cAAc;EAC9BC,UAAU,EAAEA,UAAU;EACtBC,cAAc,EAAEA,cAAc;EAC9BC,QAAQ,EAAEA,QAAQ;EAClBC,KAAK,EAAEA,KAAK;EACZG,OAAO,EAAE;IACPC,YAAY,EAAE,CAAC,CAAC;IAChBC,qBAAqB,EAAE;EACzB;AACF,CAAC;;AAED;AACA,eAAeJ,IAAI", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}