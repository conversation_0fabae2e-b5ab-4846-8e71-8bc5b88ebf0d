{"ast": null, "code": "var _jsxFileName = \"C:\\\\CMS\\\\webapp\\\\frontend\\\\src\\\\components\\\\cavi\\\\CaviFilterableTable.js\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect } from 'react';\nimport { Box, Typography, Chip, TableRow, TableCell, Button } from '@mui/material';\nimport { Clear as ClearIcon } from '@mui/icons-material';\nimport FilterableTable from '../common/FilterableTable';\nimport { formatDate } from '../../utils/dateUtils';\n\n/**\n * Componente per visualizzare la lista dei cavi con filtri in stile Excel\n *\n * @param {Object} props - Proprietà del componente\n * @param {Array} props.cavi - Lista dei cavi da visualizzare\n * @param {boolean} props.loading - Indica se i dati sono in caricamento\n * @param {Function} props.onFilteredDataChange - Funzione chiamata quando i dati filtrati cambiano\n * @param {string} props.revisioneCorrente - Revisione corrente da mostrare nelle statistiche\n */\nimport { jsxDEV as _jsxDEV, Fragment as _Fragment } from \"react/jsx-dev-runtime\";\nconst CaviFilterableTable = ({\n  cavi = [],\n  loading = false,\n  onFilteredDataChange = null,\n  revisioneCorrente = null\n}) => {\n  _s();\n  const [filteredCavi, setFilteredCavi] = useState(cavi);\n\n  // Aggiorna i dati filtrati quando cambiano i cavi\n  useEffect(() => {\n    setFilteredCavi(cavi);\n  }, [cavi]);\n\n  // Notifica il componente padre quando cambiano i dati filtrati\n  const handleFilteredDataChange = data => {\n    setFilteredCavi(data);\n    if (onFilteredDataChange) {\n      onFilteredDataChange(data);\n    }\n  };\n\n  // Gestisce il reset dei filtri\n  const handleResetFilters = () => {\n    setResetFilters(false);\n  };\n\n  // Definizione delle colonne\n  const columns = [{\n    field: 'id_cavo',\n    headerName: 'ID Cavo',\n    dataType: 'text',\n    headerStyle: {\n      fontWeight: 'bold'\n    }\n  },\n  // Colonna Revisione rimossa e spostata nella tabella delle statistiche\n  {\n    field: 'sistema',\n    headerName: 'Sistema',\n    dataType: 'text'\n  }, {\n    field: 'utility',\n    headerName: 'Utility',\n    dataType: 'text'\n  }, {\n    field: 'tipologia',\n    headerName: 'Tipologia',\n    dataType: 'text'\n  },\n  // n_conduttori field is now a spare field (kept in DB but hidden in UI)\n  {\n    field: 'sezione',\n    headerName: 'Formazione',\n    dataType: 'text',\n    align: 'right',\n    cellStyle: {\n      textAlign: 'right'\n    }\n  }, {\n    field: 'metri_teorici',\n    headerName: 'Metri Teorici',\n    dataType: 'number',\n    align: 'right',\n    cellStyle: {\n      textAlign: 'right'\n    },\n    renderCell: row => row.metri_teorici ? row.metri_teorici.toFixed(1) : '0'\n  }, {\n    field: 'metratura_reale',\n    headerName: 'Metri Reali',\n    dataType: 'number',\n    align: 'right',\n    cellStyle: {\n      textAlign: 'right'\n    },\n    renderCell: row => row.metratura_reale ? row.metratura_reale.toFixed(1) : '0'\n  }, {\n    field: 'stato_installazione',\n    headerName: 'Stato',\n    dataType: 'text',\n    renderCell: row => {\n      let color = 'default';\n      if (row.stato_installazione === 'INSTALLATO') color = 'success';else if (row.stato_installazione === 'IN_CORSO') color = 'warning';else if (row.stato_installazione === 'DA_INSTALLARE') color = 'error';\n      return /*#__PURE__*/_jsxDEV(Chip, {\n        label: row.stato_installazione || 'N/D',\n        size: \"small\",\n        color: color,\n        variant: \"outlined\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 96,\n        columnNumber: 11\n      }, this);\n    }\n  }, {\n    field: 'id_bobina',\n    headerName: 'Bobina',\n    dataType: 'text',\n    renderCell: row => {\n      // Gestione differenziata per null e BOBINA_VUOTA\n      if (row.id_bobina === null) {\n        // Per cavi non posati (id_bobina è null)\n        return '-';\n      } else if (row.id_bobina === 'BOBINA_VUOTA') {\n        // Per cavi posati senza bobina specifica\n        return 'BOBINA VUOTA';\n      } else if (!row.id_bobina) {\n        // Per altri casi in cui id_bobina è falsy (undefined, stringa vuota)\n        return '-';\n      }\n\n      // Estrai solo il numero della bobina (parte dopo '_B')\n      const match = row.id_bobina.match(/_B(.+)$/);\n      return match ? match[1] : row.id_bobina;\n    }\n  }, {\n    field: 'timestamp',\n    headerName: 'Data Modifica',\n    dataType: 'date',\n    renderCell: row => formatDate(row.timestamp)\n  }, {\n    field: 'collegamenti',\n    headerName: 'Collegamenti',\n    dataType: 'number',\n    align: 'center',\n    cellStyle: {\n      textAlign: 'center'\n    },\n    renderCell: row => {\n      let color = 'default';\n      if (row.collegamenti === 2) color = 'success';else if (row.collegamenti === 1) color = 'warning';else color = 'error';\n      return /*#__PURE__*/_jsxDEV(Chip, {\n        label: row.collegamenti,\n        size: \"small\",\n        color: color,\n        variant: \"outlined\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 146,\n        columnNumber: 11\n      }, this);\n    }\n  }];\n\n  // Renderizza una riga personalizzata\n  const renderRow = (row, index) => {\n    // Determina il colore di sfondo in base allo stato\n    let bgColor = 'inherit';\n    if (row.stato_installazione === 'INSTALLATO') bgColor = 'rgba(76, 175, 80, 0.1)';else if (row.stato_installazione === 'IN_CORSO') bgColor = 'rgba(255, 152, 0, 0.1)';\n    return /*#__PURE__*/_jsxDEV(TableRow, {\n      sx: {\n        backgroundColor: bgColor,\n        '&:hover': {\n          backgroundColor: 'rgba(0, 0, 0, 0.04)'\n        }\n      },\n      children: columns.map(column => /*#__PURE__*/_jsxDEV(TableCell, {\n        align: column.align || 'left',\n        sx: column.cellStyle,\n        children: column.renderCell ? column.renderCell(row) : row[column.field]\n      }, column.field, false, {\n        fileName: _jsxFileName,\n        lineNumber: 173,\n        columnNumber: 11\n      }, this))\n    }, index, false, {\n      fileName: _jsxFileName,\n      lineNumber: 165,\n      columnNumber: 7\n    }, this);\n  };\n\n  // Calcola le statistiche\n  const calculateStats = () => {\n    if (!filteredCavi.length) return null;\n    const totalCavi = filteredCavi.length;\n    const installati = filteredCavi.filter(c => c.stato_installazione === 'INSTALLATO').length;\n    const inCorso = filteredCavi.filter(c => c.stato_installazione === 'IN_CORSO').length;\n    const daInstallare = filteredCavi.filter(c => c.stato_installazione === 'DA_INSTALLARE').length;\n    const metriTeoriciTotali = filteredCavi.reduce((sum, c) => sum + (c.metri_teorici || 0), 0);\n    const metriRealiTotali = filteredCavi.reduce((sum, c) => sum + (c.metratura_reale || 0), 0);\n    const percentualeCompletamento = totalCavi ? Math.round(installati / totalCavi * 100) : 0;\n    return {\n      totalCavi,\n      installati,\n      inCorso,\n      daInstallare,\n      metriTeoriciTotali,\n      metriRealiTotali,\n      percentualeCompletamento\n    };\n  };\n  const stats = calculateStats();\n  return /*#__PURE__*/_jsxDEV(Box, {\n    children: [stats && /*#__PURE__*/_jsxDEV(Box, {\n      sx: {\n        mb: 3,\n        p: 2,\n        bgcolor: 'background.paper',\n        borderRadius: 1,\n        boxShadow: 1\n      },\n      children: [/*#__PURE__*/_jsxDEV(Box, {\n        sx: {\n          display: 'flex',\n          justifyContent: 'space-between',\n          alignItems: 'center',\n          mb: 1\n        },\n        children: /*#__PURE__*/_jsxDEV(Typography, {\n          variant: \"subtitle1\",\n          children: [\"Statistiche tabella cavi in\", revisioneCorrente ? /*#__PURE__*/_jsxDEV(_Fragment, {\n            children: [\" \", /*#__PURE__*/_jsxDEV(\"span\", {\n              style: {\n                fontWeight: 'bold'\n              },\n              children: [\" Rev. \\\"\", revisioneCorrente, \"\\\"\"]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 219,\n              columnNumber: 20\n            }, this)]\n          }, void 0, true) : '']\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 217,\n          columnNumber: 13\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 216,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(Box, {\n        sx: {\n          display: 'flex',\n          flexWrap: 'wrap',\n          gap: 3\n        },\n        children: [/*#__PURE__*/_jsxDEV(Box, {\n          children: [/*#__PURE__*/_jsxDEV(Typography, {\n            variant: \"body2\",\n            color: \"text.secondary\",\n            children: \"Completamento\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 225,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(Typography, {\n            variant: \"h6\",\n            children: [stats.percentualeCompletamento, \"%\"]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 226,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 224,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(Box, {\n          children: [/*#__PURE__*/_jsxDEV(Typography, {\n            variant: \"body2\",\n            color: \"text.secondary\",\n            children: \"Installati\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 229,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(Typography, {\n            variant: \"h6\",\n            color: \"success.main\",\n            children: stats.installati\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 230,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 228,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(Box, {\n          children: [/*#__PURE__*/_jsxDEV(Typography, {\n            variant: \"body2\",\n            color: \"text.secondary\",\n            children: \"In corso\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 233,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(Typography, {\n            variant: \"h6\",\n            color: \"warning.main\",\n            children: stats.inCorso\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 234,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 232,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(Box, {\n          children: [/*#__PURE__*/_jsxDEV(Typography, {\n            variant: \"body2\",\n            color: \"text.secondary\",\n            children: \"Da installare\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 237,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(Typography, {\n            variant: \"h6\",\n            color: \"error.main\",\n            children: stats.daInstallare\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 238,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 236,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(Box, {\n          children: [/*#__PURE__*/_jsxDEV(Typography, {\n            variant: \"body2\",\n            color: \"text.secondary\",\n            children: \"Metri teorici\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 241,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(Typography, {\n            variant: \"h6\",\n            children: [stats.metriTeoriciTotali.toFixed(1), \" m\"]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 242,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 240,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(Box, {\n          children: [/*#__PURE__*/_jsxDEV(Typography, {\n            variant: \"body2\",\n            color: \"text.secondary\",\n            children: \"Metri reali\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 245,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(Typography, {\n            variant: \"h6\",\n            children: [stats.metriRealiTotali.toFixed(1), \" m\"]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 246,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 244,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 223,\n        columnNumber: 11\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 215,\n      columnNumber: 9\n    }, this), /*#__PURE__*/_jsxDEV(FilterableTable, {\n      data: cavi,\n      columns: columns,\n      onFilteredDataChange: handleFilteredDataChange,\n      loading: loading,\n      emptyMessage: \"Nessun cavo disponibile\",\n      renderRow: renderRow,\n      onResetFilters: handleResetFilters\n    }, resetFilters ? 'reset' : 'normal', false, {\n      fileName: _jsxFileName,\n      lineNumber: 252,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 213,\n    columnNumber: 5\n  }, this);\n};\n_s(CaviFilterableTable, \"eRBUlmNTTIMtkQjXirbuK4uD11w=\");\n_c = CaviFilterableTable;\nexport default CaviFilterableTable;\nvar _c;\n$RefreshReg$(_c, \"CaviFilterableTable\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "Box", "Typography", "Chip", "TableRow", "TableCell", "<PERSON><PERSON>", "Clear", "ClearIcon", "FilterableTable", "formatDate", "jsxDEV", "_jsxDEV", "Fragment", "_Fragment", "CaviFilterableTable", "cavi", "loading", "onFilteredDataChange", "revisioneCorrente", "_s", "filteredCavi", "setFilteredCavi", "handleFilteredDataChange", "data", "handleResetFilters", "setResetFilters", "columns", "field", "headerName", "dataType", "headerStyle", "fontWeight", "align", "cellStyle", "textAlign", "renderCell", "row", "metri_te<PERSON>ci", "toFixed", "metratura_reale", "color", "stato_installazione", "label", "size", "variant", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "id_bobina", "match", "timestamp", "colle<PERSON>nti", "renderRow", "index", "bgColor", "sx", "backgroundColor", "children", "map", "column", "calculateStats", "length", "totalCavi", "installati", "filter", "c", "inCorso", "daInstallare", "metriTeoriciTotali", "reduce", "sum", "metriRealiTotali", "percentualeCompletamento", "Math", "round", "stats", "mb", "p", "bgcolor", "borderRadius", "boxShadow", "display", "justifyContent", "alignItems", "style", "flexWrap", "gap", "emptyMessage", "onResetFilters", "resetFilters", "_c", "$RefreshReg$"], "sources": ["C:/CMS/webapp/frontend/src/components/cavi/CaviFilterableTable.js"], "sourcesContent": ["import React, { useState, useEffect } from 'react';\nimport { Box, Typography, Chip, TableRow, TableCell, Button } from '@mui/material';\nimport { Clear as ClearIcon } from '@mui/icons-material';\nimport FilterableTable from '../common/FilterableTable';\nimport { formatDate } from '../../utils/dateUtils';\n\n/**\n * Componente per visualizzare la lista dei cavi con filtri in stile Excel\n *\n * @param {Object} props - Proprietà del componente\n * @param {Array} props.cavi - Lista dei cavi da visualizzare\n * @param {boolean} props.loading - Indica se i dati sono in caricamento\n * @param {Function} props.onFilteredDataChange - Funzione chiamata quando i dati filtrati cambiano\n * @param {string} props.revisioneCorrente - Revisione corrente da mostrare nelle statistiche\n */\nconst CaviFilterableTable = ({ cavi = [], loading = false, onFilteredDataChange = null, revisioneCorrente = null }) => {\n  const [filteredCavi, setFilteredCavi] = useState(cavi);\n\n  // Aggiorna i dati filtrati quando cambiano i cavi\n  useEffect(() => {\n    setFilteredCavi(cavi);\n  }, [cavi]);\n\n  // Notifica il componente padre quando cambiano i dati filtrati\n  const handleFilteredDataChange = (data) => {\n    setFilteredCavi(data);\n    if (onFilteredDataChange) {\n      onFilteredDataChange(data);\n    }\n  };\n\n  // Gestisce il reset dei filtri\n  const handleResetFilters = () => {\n    setResetFilters(false);\n  };\n\n  // Definizione delle colonne\n  const columns = [\n    {\n      field: 'id_cavo',\n      headerName: 'ID Cavo',\n      dataType: 'text',\n      headerStyle: { fontWeight: 'bold' }\n    },\n    // Colonna Revisione rimossa e spostata nella tabella delle statistiche\n    {\n      field: 'sistema',\n      headerName: 'Sistema',\n      dataType: 'text'\n    },\n    {\n      field: 'utility',\n      headerName: 'Utility',\n      dataType: 'text'\n    },\n    {\n      field: 'tipologia',\n      headerName: 'Tipologia',\n      dataType: 'text'\n    },\n    // n_conduttori field is now a spare field (kept in DB but hidden in UI)\n    {\n      field: 'sezione',\n      headerName: 'Formazione',\n      dataType: 'text',\n      align: 'right',\n      cellStyle: { textAlign: 'right' }\n    },\n    {\n      field: 'metri_teorici',\n      headerName: 'Metri Teorici',\n      dataType: 'number',\n      align: 'right',\n      cellStyle: { textAlign: 'right' },\n      renderCell: (row) => row.metri_teorici ? row.metri_teorici.toFixed(1) : '0'\n    },\n    {\n      field: 'metratura_reale',\n      headerName: 'Metri Reali',\n      dataType: 'number',\n      align: 'right',\n      cellStyle: { textAlign: 'right' },\n      renderCell: (row) => row.metratura_reale ? row.metratura_reale.toFixed(1) : '0'\n    },\n    {\n      field: 'stato_installazione',\n      headerName: 'Stato',\n      dataType: 'text',\n      renderCell: (row) => {\n        let color = 'default';\n        if (row.stato_installazione === 'INSTALLATO') color = 'success';\n        else if (row.stato_installazione === 'IN_CORSO') color = 'warning';\n        else if (row.stato_installazione === 'DA_INSTALLARE') color = 'error';\n\n        return (\n          <Chip\n            label={row.stato_installazione || 'N/D'}\n            size=\"small\"\n            color={color}\n            variant=\"outlined\"\n          />\n        );\n      }\n    },\n    {\n      field: 'id_bobina',\n      headerName: 'Bobina',\n      dataType: 'text',\n      renderCell: (row) => {\n        // Gestione differenziata per null e BOBINA_VUOTA\n        if (row.id_bobina === null) {\n          // Per cavi non posati (id_bobina è null)\n          return '-';\n        } else if (row.id_bobina === 'BOBINA_VUOTA') {\n          // Per cavi posati senza bobina specifica\n          return 'BOBINA VUOTA';\n        } else if (!row.id_bobina) {\n          // Per altri casi in cui id_bobina è falsy (undefined, stringa vuota)\n          return '-';\n        }\n\n        // Estrai solo il numero della bobina (parte dopo '_B')\n        const match = row.id_bobina.match(/_B(.+)$/);\n        return match ? match[1] : row.id_bobina;\n      }\n    },\n    {\n      field: 'timestamp',\n      headerName: 'Data Modifica',\n      dataType: 'date',\n      renderCell: (row) => formatDate(row.timestamp)\n    },\n    {\n      field: 'collegamenti',\n      headerName: 'Collegamenti',\n      dataType: 'number',\n      align: 'center',\n      cellStyle: { textAlign: 'center' },\n      renderCell: (row) => {\n        let color = 'default';\n        if (row.collegamenti === 2) color = 'success';\n        else if (row.collegamenti === 1) color = 'warning';\n        else color = 'error';\n\n        return (\n          <Chip\n            label={row.collegamenti}\n            size=\"small\"\n            color={color}\n            variant=\"outlined\"\n          />\n        );\n      }\n    }\n  ];\n\n  // Renderizza una riga personalizzata\n  const renderRow = (row, index) => {\n    // Determina il colore di sfondo in base allo stato\n    let bgColor = 'inherit';\n    if (row.stato_installazione === 'INSTALLATO') bgColor = 'rgba(76, 175, 80, 0.1)';\n    else if (row.stato_installazione === 'IN_CORSO') bgColor = 'rgba(255, 152, 0, 0.1)';\n\n    return (\n      <TableRow\n        key={index}\n        sx={{\n          backgroundColor: bgColor,\n          '&:hover': { backgroundColor: 'rgba(0, 0, 0, 0.04)' }\n        }}\n      >\n        {columns.map((column) => (\n          <TableCell\n            key={column.field}\n            align={column.align || 'left'}\n            sx={column.cellStyle}\n          >\n            {column.renderCell ? column.renderCell(row) : row[column.field]}\n          </TableCell>\n        ))}\n      </TableRow>\n    );\n  };\n\n  // Calcola le statistiche\n  const calculateStats = () => {\n    if (!filteredCavi.length) return null;\n\n    const totalCavi = filteredCavi.length;\n    const installati = filteredCavi.filter(c => c.stato_installazione === 'INSTALLATO').length;\n    const inCorso = filteredCavi.filter(c => c.stato_installazione === 'IN_CORSO').length;\n    const daInstallare = filteredCavi.filter(c => c.stato_installazione === 'DA_INSTALLARE').length;\n\n    const metriTeoriciTotali = filteredCavi.reduce((sum, c) => sum + (c.metri_teorici || 0), 0);\n    const metriRealiTotali = filteredCavi.reduce((sum, c) => sum + (c.metratura_reale || 0), 0);\n\n    const percentualeCompletamento = totalCavi ? Math.round((installati / totalCavi) * 100) : 0;\n\n    return {\n      totalCavi,\n      installati,\n      inCorso,\n      daInstallare,\n      metriTeoriciTotali,\n      metriRealiTotali,\n      percentualeCompletamento\n    };\n  };\n\n  const stats = calculateStats();\n\n  return (\n    <Box>\n      {stats && (\n        <Box sx={{ mb: 3, p: 2, bgcolor: 'background.paper', borderRadius: 1, boxShadow: 1 }}>\n          <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 1 }}>\n            <Typography variant=\"subtitle1\">\n              Statistiche tabella cavi in{revisioneCorrente ? (\n                <> <span style={{ fontWeight: 'bold' }}> Rev. \"{revisioneCorrente}\"</span></>\n              ) : ''}\n            </Typography>\n          </Box>\n          <Box sx={{ display: 'flex', flexWrap: 'wrap', gap: 3 }}>\n            <Box>\n              <Typography variant=\"body2\" color=\"text.secondary\">Completamento</Typography>\n              <Typography variant=\"h6\">{stats.percentualeCompletamento}%</Typography>\n            </Box>\n            <Box>\n              <Typography variant=\"body2\" color=\"text.secondary\">Installati</Typography>\n              <Typography variant=\"h6\" color=\"success.main\">{stats.installati}</Typography>\n            </Box>\n            <Box>\n              <Typography variant=\"body2\" color=\"text.secondary\">In corso</Typography>\n              <Typography variant=\"h6\" color=\"warning.main\">{stats.inCorso}</Typography>\n            </Box>\n            <Box>\n              <Typography variant=\"body2\" color=\"text.secondary\">Da installare</Typography>\n              <Typography variant=\"h6\" color=\"error.main\">{stats.daInstallare}</Typography>\n            </Box>\n            <Box>\n              <Typography variant=\"body2\" color=\"text.secondary\">Metri teorici</Typography>\n              <Typography variant=\"h6\">{stats.metriTeoriciTotali.toFixed(1)} m</Typography>\n            </Box>\n            <Box>\n              <Typography variant=\"body2\" color=\"text.secondary\">Metri reali</Typography>\n              <Typography variant=\"h6\">{stats.metriRealiTotali.toFixed(1)} m</Typography>\n            </Box>\n          </Box>\n        </Box>\n      )}\n\n      <FilterableTable\n        data={cavi}\n        columns={columns}\n        onFilteredDataChange={handleFilteredDataChange}\n        loading={loading}\n        emptyMessage=\"Nessun cavo disponibile\"\n        renderRow={renderRow}\n        onResetFilters={handleResetFilters}\n        key={resetFilters ? 'reset' : 'normal'} // Forza il re-render quando resetFilters cambia\n      />\n    </Box>\n  );\n};\n\nexport default CaviFilterableTable;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAClD,SAASC,GAAG,EAAEC,UAAU,EAAEC,IAAI,EAAEC,QAAQ,EAAEC,SAAS,EAAEC,MAAM,QAAQ,eAAe;AAClF,SAASC,KAAK,IAAIC,SAAS,QAAQ,qBAAqB;AACxD,OAAOC,eAAe,MAAM,2BAA2B;AACvD,SAASC,UAAU,QAAQ,uBAAuB;;AAElD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AARA,SAAAC,MAAA,IAAAC,OAAA,EAAAC,QAAA,IAAAC,SAAA;AASA,MAAMC,mBAAmB,GAAGA,CAAC;EAAEC,IAAI,GAAG,EAAE;EAAEC,OAAO,GAAG,KAAK;EAAEC,oBAAoB,GAAG,IAAI;EAAEC,iBAAiB,GAAG;AAAK,CAAC,KAAK;EAAAC,EAAA;EACrH,MAAM,CAACC,YAAY,EAAEC,eAAe,CAAC,GAAGvB,QAAQ,CAACiB,IAAI,CAAC;;EAEtD;EACAhB,SAAS,CAAC,MAAM;IACdsB,eAAe,CAACN,IAAI,CAAC;EACvB,CAAC,EAAE,CAACA,IAAI,CAAC,CAAC;;EAEV;EACA,MAAMO,wBAAwB,GAAIC,IAAI,IAAK;IACzCF,eAAe,CAACE,IAAI,CAAC;IACrB,IAAIN,oBAAoB,EAAE;MACxBA,oBAAoB,CAACM,IAAI,CAAC;IAC5B;EACF,CAAC;;EAED;EACA,MAAMC,kBAAkB,GAAGA,CAAA,KAAM;IAC/BC,eAAe,CAAC,KAAK,CAAC;EACxB,CAAC;;EAED;EACA,MAAMC,OAAO,GAAG,CACd;IACEC,KAAK,EAAE,SAAS;IAChBC,UAAU,EAAE,SAAS;IACrBC,QAAQ,EAAE,MAAM;IAChBC,WAAW,EAAE;MAAEC,UAAU,EAAE;IAAO;EACpC,CAAC;EACD;EACA;IACEJ,KAAK,EAAE,SAAS;IAChBC,UAAU,EAAE,SAAS;IACrBC,QAAQ,EAAE;EACZ,CAAC,EACD;IACEF,KAAK,EAAE,SAAS;IAChBC,UAAU,EAAE,SAAS;IACrBC,QAAQ,EAAE;EACZ,CAAC,EACD;IACEF,KAAK,EAAE,WAAW;IAClBC,UAAU,EAAE,WAAW;IACvBC,QAAQ,EAAE;EACZ,CAAC;EACD;EACA;IACEF,KAAK,EAAE,SAAS;IAChBC,UAAU,EAAE,YAAY;IACxBC,QAAQ,EAAE,MAAM;IAChBG,KAAK,EAAE,OAAO;IACdC,SAAS,EAAE;MAAEC,SAAS,EAAE;IAAQ;EAClC,CAAC,EACD;IACEP,KAAK,EAAE,eAAe;IACtBC,UAAU,EAAE,eAAe;IAC3BC,QAAQ,EAAE,QAAQ;IAClBG,KAAK,EAAE,OAAO;IACdC,SAAS,EAAE;MAAEC,SAAS,EAAE;IAAQ,CAAC;IACjCC,UAAU,EAAGC,GAAG,IAAKA,GAAG,CAACC,aAAa,GAAGD,GAAG,CAACC,aAAa,CAACC,OAAO,CAAC,CAAC,CAAC,GAAG;EAC1E,CAAC,EACD;IACEX,KAAK,EAAE,iBAAiB;IACxBC,UAAU,EAAE,aAAa;IACzBC,QAAQ,EAAE,QAAQ;IAClBG,KAAK,EAAE,OAAO;IACdC,SAAS,EAAE;MAAEC,SAAS,EAAE;IAAQ,CAAC;IACjCC,UAAU,EAAGC,GAAG,IAAKA,GAAG,CAACG,eAAe,GAAGH,GAAG,CAACG,eAAe,CAACD,OAAO,CAAC,CAAC,CAAC,GAAG;EAC9E,CAAC,EACD;IACEX,KAAK,EAAE,qBAAqB;IAC5BC,UAAU,EAAE,OAAO;IACnBC,QAAQ,EAAE,MAAM;IAChBM,UAAU,EAAGC,GAAG,IAAK;MACnB,IAAII,KAAK,GAAG,SAAS;MACrB,IAAIJ,GAAG,CAACK,mBAAmB,KAAK,YAAY,EAAED,KAAK,GAAG,SAAS,CAAC,KAC3D,IAAIJ,GAAG,CAACK,mBAAmB,KAAK,UAAU,EAAED,KAAK,GAAG,SAAS,CAAC,KAC9D,IAAIJ,GAAG,CAACK,mBAAmB,KAAK,eAAe,EAAED,KAAK,GAAG,OAAO;MAErE,oBACE7B,OAAA,CAACT,IAAI;QACHwC,KAAK,EAAEN,GAAG,CAACK,mBAAmB,IAAI,KAAM;QACxCE,IAAI,EAAC,OAAO;QACZH,KAAK,EAAEA,KAAM;QACbI,OAAO,EAAC;MAAU;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACnB,CAAC;IAEN;EACF,CAAC,EACD;IACErB,KAAK,EAAE,WAAW;IAClBC,UAAU,EAAE,QAAQ;IACpBC,QAAQ,EAAE,MAAM;IAChBM,UAAU,EAAGC,GAAG,IAAK;MACnB;MACA,IAAIA,GAAG,CAACa,SAAS,KAAK,IAAI,EAAE;QAC1B;QACA,OAAO,GAAG;MACZ,CAAC,MAAM,IAAIb,GAAG,CAACa,SAAS,KAAK,cAAc,EAAE;QAC3C;QACA,OAAO,cAAc;MACvB,CAAC,MAAM,IAAI,CAACb,GAAG,CAACa,SAAS,EAAE;QACzB;QACA,OAAO,GAAG;MACZ;;MAEA;MACA,MAAMC,KAAK,GAAGd,GAAG,CAACa,SAAS,CAACC,KAAK,CAAC,SAAS,CAAC;MAC5C,OAAOA,KAAK,GAAGA,KAAK,CAAC,CAAC,CAAC,GAAGd,GAAG,CAACa,SAAS;IACzC;EACF,CAAC,EACD;IACEtB,KAAK,EAAE,WAAW;IAClBC,UAAU,EAAE,eAAe;IAC3BC,QAAQ,EAAE,MAAM;IAChBM,UAAU,EAAGC,GAAG,IAAK3B,UAAU,CAAC2B,GAAG,CAACe,SAAS;EAC/C,CAAC,EACD;IACExB,KAAK,EAAE,cAAc;IACrBC,UAAU,EAAE,cAAc;IAC1BC,QAAQ,EAAE,QAAQ;IAClBG,KAAK,EAAE,QAAQ;IACfC,SAAS,EAAE;MAAEC,SAAS,EAAE;IAAS,CAAC;IAClCC,UAAU,EAAGC,GAAG,IAAK;MACnB,IAAII,KAAK,GAAG,SAAS;MACrB,IAAIJ,GAAG,CAACgB,YAAY,KAAK,CAAC,EAAEZ,KAAK,GAAG,SAAS,CAAC,KACzC,IAAIJ,GAAG,CAACgB,YAAY,KAAK,CAAC,EAAEZ,KAAK,GAAG,SAAS,CAAC,KAC9CA,KAAK,GAAG,OAAO;MAEpB,oBACE7B,OAAA,CAACT,IAAI;QACHwC,KAAK,EAAEN,GAAG,CAACgB,YAAa;QACxBT,IAAI,EAAC,OAAO;QACZH,KAAK,EAAEA,KAAM;QACbI,OAAO,EAAC;MAAU;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACnB,CAAC;IAEN;EACF,CAAC,CACF;;EAED;EACA,MAAMK,SAAS,GAAGA,CAACjB,GAAG,EAAEkB,KAAK,KAAK;IAChC;IACA,IAAIC,OAAO,GAAG,SAAS;IACvB,IAAInB,GAAG,CAACK,mBAAmB,KAAK,YAAY,EAAEc,OAAO,GAAG,wBAAwB,CAAC,KAC5E,IAAInB,GAAG,CAACK,mBAAmB,KAAK,UAAU,EAAEc,OAAO,GAAG,wBAAwB;IAEnF,oBACE5C,OAAA,CAACR,QAAQ;MAEPqD,EAAE,EAAE;QACFC,eAAe,EAAEF,OAAO;QACxB,SAAS,EAAE;UAAEE,eAAe,EAAE;QAAsB;MACtD,CAAE;MAAAC,QAAA,EAEDhC,OAAO,CAACiC,GAAG,CAAEC,MAAM,iBAClBjD,OAAA,CAACP,SAAS;QAER4B,KAAK,EAAE4B,MAAM,CAAC5B,KAAK,IAAI,MAAO;QAC9BwB,EAAE,EAAEI,MAAM,CAAC3B,SAAU;QAAAyB,QAAA,EAEpBE,MAAM,CAACzB,UAAU,GAAGyB,MAAM,CAACzB,UAAU,CAACC,GAAG,CAAC,GAAGA,GAAG,CAACwB,MAAM,CAACjC,KAAK;MAAC,GAJ1DiC,MAAM,CAACjC,KAAK;QAAAkB,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAKR,CACZ;IAAC,GAdGM,KAAK;MAAAT,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAeF,CAAC;EAEf,CAAC;;EAED;EACA,MAAMa,cAAc,GAAGA,CAAA,KAAM;IAC3B,IAAI,CAACzC,YAAY,CAAC0C,MAAM,EAAE,OAAO,IAAI;IAErC,MAAMC,SAAS,GAAG3C,YAAY,CAAC0C,MAAM;IACrC,MAAME,UAAU,GAAG5C,YAAY,CAAC6C,MAAM,CAACC,CAAC,IAAIA,CAAC,CAACzB,mBAAmB,KAAK,YAAY,CAAC,CAACqB,MAAM;IAC1F,MAAMK,OAAO,GAAG/C,YAAY,CAAC6C,MAAM,CAACC,CAAC,IAAIA,CAAC,CAACzB,mBAAmB,KAAK,UAAU,CAAC,CAACqB,MAAM;IACrF,MAAMM,YAAY,GAAGhD,YAAY,CAAC6C,MAAM,CAACC,CAAC,IAAIA,CAAC,CAACzB,mBAAmB,KAAK,eAAe,CAAC,CAACqB,MAAM;IAE/F,MAAMO,kBAAkB,GAAGjD,YAAY,CAACkD,MAAM,CAAC,CAACC,GAAG,EAAEL,CAAC,KAAKK,GAAG,IAAIL,CAAC,CAAC7B,aAAa,IAAI,CAAC,CAAC,EAAE,CAAC,CAAC;IAC3F,MAAMmC,gBAAgB,GAAGpD,YAAY,CAACkD,MAAM,CAAC,CAACC,GAAG,EAAEL,CAAC,KAAKK,GAAG,IAAIL,CAAC,CAAC3B,eAAe,IAAI,CAAC,CAAC,EAAE,CAAC,CAAC;IAE3F,MAAMkC,wBAAwB,GAAGV,SAAS,GAAGW,IAAI,CAACC,KAAK,CAAEX,UAAU,GAAGD,SAAS,GAAI,GAAG,CAAC,GAAG,CAAC;IAE3F,OAAO;MACLA,SAAS;MACTC,UAAU;MACVG,OAAO;MACPC,YAAY;MACZC,kBAAkB;MAClBG,gBAAgB;MAChBC;IACF,CAAC;EACH,CAAC;EAED,MAAMG,KAAK,GAAGf,cAAc,CAAC,CAAC;EAE9B,oBACElD,OAAA,CAACX,GAAG;IAAA0D,QAAA,GACDkB,KAAK,iBACJjE,OAAA,CAACX,GAAG;MAACwD,EAAE,EAAE;QAAEqB,EAAE,EAAE,CAAC;QAAEC,CAAC,EAAE,CAAC;QAAEC,OAAO,EAAE,kBAAkB;QAAEC,YAAY,EAAE,CAAC;QAAEC,SAAS,EAAE;MAAE,CAAE;MAAAvB,QAAA,gBACnF/C,OAAA,CAACX,GAAG;QAACwD,EAAE,EAAE;UAAE0B,OAAO,EAAE,MAAM;UAAEC,cAAc,EAAE,eAAe;UAAEC,UAAU,EAAE,QAAQ;UAAEP,EAAE,EAAE;QAAE,CAAE;QAAAnB,QAAA,eACzF/C,OAAA,CAACV,UAAU;UAAC2C,OAAO,EAAC,WAAW;UAAAc,QAAA,GAAC,6BACH,EAACxC,iBAAiB,gBAC3CP,OAAA,CAAAE,SAAA;YAAA6C,QAAA,GAAE,GAAC,eAAA/C,OAAA;cAAM0E,KAAK,EAAE;gBAAEtD,UAAU,EAAE;cAAO,CAAE;cAAA2B,QAAA,GAAC,UAAO,EAACxC,iBAAiB,EAAC,IAAC;YAAA;cAAA2B,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC;UAAA,eAAE,CAAC,GAC3E,EAAE;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACI;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACV,CAAC,eACNrC,OAAA,CAACX,GAAG;QAACwD,EAAE,EAAE;UAAE0B,OAAO,EAAE,MAAM;UAAEI,QAAQ,EAAE,MAAM;UAAEC,GAAG,EAAE;QAAE,CAAE;QAAA7B,QAAA,gBACrD/C,OAAA,CAACX,GAAG;UAAA0D,QAAA,gBACF/C,OAAA,CAACV,UAAU;YAAC2C,OAAO,EAAC,OAAO;YAACJ,KAAK,EAAC,gBAAgB;YAAAkB,QAAA,EAAC;UAAa;YAAAb,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAY,CAAC,eAC7ErC,OAAA,CAACV,UAAU;YAAC2C,OAAO,EAAC,IAAI;YAAAc,QAAA,GAAEkB,KAAK,CAACH,wBAAwB,EAAC,GAAC;UAAA;YAAA5B,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAY,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACpE,CAAC,eACNrC,OAAA,CAACX,GAAG;UAAA0D,QAAA,gBACF/C,OAAA,CAACV,UAAU;YAAC2C,OAAO,EAAC,OAAO;YAACJ,KAAK,EAAC,gBAAgB;YAAAkB,QAAA,EAAC;UAAU;YAAAb,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAY,CAAC,eAC1ErC,OAAA,CAACV,UAAU;YAAC2C,OAAO,EAAC,IAAI;YAACJ,KAAK,EAAC,cAAc;YAAAkB,QAAA,EAAEkB,KAAK,CAACZ;UAAU;YAAAnB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAa,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC1E,CAAC,eACNrC,OAAA,CAACX,GAAG;UAAA0D,QAAA,gBACF/C,OAAA,CAACV,UAAU;YAAC2C,OAAO,EAAC,OAAO;YAACJ,KAAK,EAAC,gBAAgB;YAAAkB,QAAA,EAAC;UAAQ;YAAAb,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAY,CAAC,eACxErC,OAAA,CAACV,UAAU;YAAC2C,OAAO,EAAC,IAAI;YAACJ,KAAK,EAAC,cAAc;YAAAkB,QAAA,EAAEkB,KAAK,CAACT;UAAO;YAAAtB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAa,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACvE,CAAC,eACNrC,OAAA,CAACX,GAAG;UAAA0D,QAAA,gBACF/C,OAAA,CAACV,UAAU;YAAC2C,OAAO,EAAC,OAAO;YAACJ,KAAK,EAAC,gBAAgB;YAAAkB,QAAA,EAAC;UAAa;YAAAb,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAY,CAAC,eAC7ErC,OAAA,CAACV,UAAU;YAAC2C,OAAO,EAAC,IAAI;YAACJ,KAAK,EAAC,YAAY;YAAAkB,QAAA,EAAEkB,KAAK,CAACR;UAAY;YAAAvB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAa,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC1E,CAAC,eACNrC,OAAA,CAACX,GAAG;UAAA0D,QAAA,gBACF/C,OAAA,CAACV,UAAU;YAAC2C,OAAO,EAAC,OAAO;YAACJ,KAAK,EAAC,gBAAgB;YAAAkB,QAAA,EAAC;UAAa;YAAAb,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAY,CAAC,eAC7ErC,OAAA,CAACV,UAAU;YAAC2C,OAAO,EAAC,IAAI;YAAAc,QAAA,GAAEkB,KAAK,CAACP,kBAAkB,CAAC/B,OAAO,CAAC,CAAC,CAAC,EAAC,IAAE;UAAA;YAAAO,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAY,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC1E,CAAC,eACNrC,OAAA,CAACX,GAAG;UAAA0D,QAAA,gBACF/C,OAAA,CAACV,UAAU;YAAC2C,OAAO,EAAC,OAAO;YAACJ,KAAK,EAAC,gBAAgB;YAAAkB,QAAA,EAAC;UAAW;YAAAb,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAY,CAAC,eAC3ErC,OAAA,CAACV,UAAU;YAAC2C,OAAO,EAAC,IAAI;YAAAc,QAAA,GAAEkB,KAAK,CAACJ,gBAAgB,CAAClC,OAAO,CAAC,CAAC,CAAC,EAAC,IAAE;UAAA;YAAAO,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAY,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACxE,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CACN,eAEDrC,OAAA,CAACH,eAAe;MACde,IAAI,EAAER,IAAK;MACXW,OAAO,EAAEA,OAAQ;MACjBT,oBAAoB,EAAEK,wBAAyB;MAC/CN,OAAO,EAAEA,OAAQ;MACjBwE,YAAY,EAAC,yBAAyB;MACtCnC,SAAS,EAAEA,SAAU;MACrBoC,cAAc,EAAEjE;IAAmB,GAC9BkE,YAAY,GAAG,OAAO,GAAG,QAAQ;MAAA7C,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACvC,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACC,CAAC;AAEV,CAAC;AAAC7B,EAAA,CAxPIL,mBAAmB;AAAA6E,EAAA,GAAnB7E,mBAAmB;AA0PzB,eAAeA,mBAAmB;AAAC,IAAA6E,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}