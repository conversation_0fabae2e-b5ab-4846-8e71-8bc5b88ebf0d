{"ast": null, "code": "import { monthsInYear } from \"./constants.js\";\n\n/**\n * @name monthsToYears\n * @category Conversion Helpers\n * @summary Convert number of months to years.\n *\n * @description\n * Convert a number of months to a full number of years.\n *\n * @param months - The number of months to be converted\n *\n * @returns The number of months converted in years\n *\n * @example\n * // Convert 36 months to years:\n * const result = monthsToYears(36)\n * //=> 3\n *\n * // It uses floor rounding:\n * const result = monthsToYears(40)\n * //=> 3\n */\nexport function monthsToYears(months) {\n  const years = months / monthsInYear;\n  return Math.trunc(years);\n}\n\n// Fallback for modularized imports:\nexport default monthsToYears;", "map": {"version": 3, "names": ["monthsInYear", "monthsT<PERSON><PERSON><PERSON>s", "months", "years", "Math", "trunc"], "sources": ["C:/Users/<USER>/PycharmProjects/CMS/webapp/frontend/node_modules/date-fns/monthsToYears.js"], "sourcesContent": ["import { monthsInYear } from \"./constants.js\";\n\n/**\n * @name monthsToYears\n * @category Conversion Helpers\n * @summary Convert number of months to years.\n *\n * @description\n * Convert a number of months to a full number of years.\n *\n * @param months - The number of months to be converted\n *\n * @returns The number of months converted in years\n *\n * @example\n * // Convert 36 months to years:\n * const result = monthsToYears(36)\n * //=> 3\n *\n * // It uses floor rounding:\n * const result = monthsToYears(40)\n * //=> 3\n */\nexport function monthsToYears(months) {\n  const years = months / monthsInYear;\n  return Math.trunc(years);\n}\n\n// Fallback for modularized imports:\nexport default monthsToYears;\n"], "mappings": "AAAA,SAASA,YAAY,QAAQ,gBAAgB;;AAE7C;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO,SAASC,aAAaA,CAACC,MAAM,EAAE;EACpC,MAAMC,KAAK,GAAGD,MAAM,GAAGF,YAAY;EACnC,OAAOI,IAAI,CAACC,KAAK,CAACF,KAAK,CAAC;AAC1B;;AAEA;AACA,eAAeF,aAAa", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}