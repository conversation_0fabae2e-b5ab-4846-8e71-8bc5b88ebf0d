{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\PycharmProjects\\\\CMS\\\\webapp\\\\frontend\\\\src\\\\components\\\\admin\\\\ImpersonateUser.js\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect } from 'react';\nimport { useNavigate } from 'react-router-dom';\nimport { Box, Typography, Button, FormControl, InputLabel, Select, MenuItem, Alert, CircularProgress } from '@mui/material';\nimport { Refresh as RefreshIcon, Login as LoginIcon } from '@mui/icons-material';\nimport userService from '../../services/userService';\nimport authService from '../../services/authService';\nimport { useAuth } from '../../context/AuthContext';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst ImpersonateUser = () => {\n  _s();\n  const navigate = useNavigate();\n  const [users, setUsers] = useState([]);\n  const [selectedUserId, setSelectedUserId] = useState('');\n  const [loading, setLoading] = useState(true);\n  const [error, setError] = useState('');\n  const {\n    impersonateUser\n  } = useAuth();\n\n  // Carica gli utenti\n  const loadUsers = async () => {\n    setLoading(true);\n    try {\n      const data = await userService.getUsers();\n      // Filtra solo gli utenti attivi e non admin\n      const activeUsers = data.filter(user => user.abilitato && user.ruolo !== 'owner');\n      setUsers(activeUsers);\n      setError('');\n    } catch (err) {\n      setError(err.detail || 'Errore durante il caricamento degli utenti');\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  // Carica gli utenti all'avvio del componente\n  useEffect(() => {\n    loadUsers();\n  }, []);\n\n  // Gestisce il cambio dell'utente selezionato\n  const handleUserChange = event => {\n    setSelectedUserId(event.target.value);\n  };\n\n  // Gestisce l'accesso come utente selezionato\n  const handleImpersonate = async () => {\n    if (!selectedUserId) {\n      setError('Seleziona un utente');\n      return;\n    }\n    setLoading(true);\n    try {\n      // Utilizza la funzione impersonateUser dal contesto di autenticazione\n      await impersonateUser(selectedUserId);\n      console.log('Impersonificazione riuscita');\n\n      // Reindirizza alla dashboard usando il router di React dopo un breve ritardo\n      // per assicurarsi che il contesto di autenticazione sia aggiornato\n      setTimeout(() => {\n        navigate('/dashboard');\n      }, 500);\n    } catch (err) {\n      setError(err.detail || 'Errore durante l\\'accesso come utente selezionato');\n    } finally {\n      setLoading(false);\n    }\n  };\n  return /*#__PURE__*/_jsxDEV(Box, {\n    children: [/*#__PURE__*/_jsxDEV(Box, {\n      sx: {\n        display: 'flex',\n        justifyContent: 'space-between',\n        alignItems: 'center',\n        mb: 2\n      },\n      children: [/*#__PURE__*/_jsxDEV(Typography, {\n        variant: \"h6\",\n        children: \"Accedi come Utente\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 85,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Button, {\n        variant: \"outlined\",\n        startIcon: /*#__PURE__*/_jsxDEV(RefreshIcon, {}, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 88,\n          columnNumber: 22\n        }, this),\n        onClick: loadUsers,\n        disabled: loading,\n        children: \"Aggiorna\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 86,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 84,\n      columnNumber: 7\n    }, this), error && /*#__PURE__*/_jsxDEV(Alert, {\n      severity: \"error\",\n      sx: {\n        mb: 2\n      },\n      children: error\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 97,\n      columnNumber: 9\n    }, this), loading ? /*#__PURE__*/_jsxDEV(CircularProgress, {}, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 103,\n      columnNumber: 9\n    }, this) : users.length === 0 ? /*#__PURE__*/_jsxDEV(Typography, {\n      children: \"Nessun utente disponibile\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 105,\n      columnNumber: 9\n    }, this) : /*#__PURE__*/_jsxDEV(Box, {\n      sx: {\n        mt: 2\n      },\n      children: [/*#__PURE__*/_jsxDEV(FormControl, {\n        fullWidth: true,\n        sx: {\n          mb: 2\n        },\n        children: [/*#__PURE__*/_jsxDEV(InputLabel, {\n          id: \"user-select-label\",\n          children: \"Seleziona Utente\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 109,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(Select, {\n          labelId: \"user-select-label\",\n          id: \"user-select\",\n          value: selectedUserId,\n          label: \"Seleziona Utente\",\n          onChange: handleUserChange,\n          children: users.map(user => /*#__PURE__*/_jsxDEV(MenuItem, {\n            value: user.id_utente,\n            children: [user.username, \" (\", user.ruolo, \")\"]\n          }, user.id_utente, true, {\n            fileName: _jsxFileName,\n            lineNumber: 118,\n            columnNumber: 17\n          }, this))\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 110,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 108,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(Button, {\n        variant: \"contained\",\n        color: \"primary\",\n        startIcon: /*#__PURE__*/_jsxDEV(LoginIcon, {}, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 128,\n          columnNumber: 24\n        }, this),\n        onClick: handleImpersonate,\n        disabled: !selectedUserId || loading,\n        fullWidth: true,\n        children: \"Accedi come Utente Selezionato\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 125,\n        columnNumber: 11\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 107,\n      columnNumber: 9\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 83,\n    columnNumber: 5\n  }, this);\n};\n_s(ImpersonateUser, \"l9QBGMy0JEgbyWrKQx2GBsvtRPo=\", false, function () {\n  return [useNavigate, useAuth];\n});\n_c = ImpersonateUser;\nexport default ImpersonateUser;\nvar _c;\n$RefreshReg$(_c, \"ImpersonateUser\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "useNavigate", "Box", "Typography", "<PERSON><PERSON>", "FormControl", "InputLabel", "Select", "MenuItem", "<PERSON><PERSON>", "CircularProgress", "Refresh", "RefreshIcon", "<PERSON><PERSON>", "LoginIcon", "userService", "authService", "useAuth", "jsxDEV", "_jsxDEV", "ImpersonateUser", "_s", "navigate", "users", "setUsers", "selectedUserId", "setSelectedUserId", "loading", "setLoading", "error", "setError", "impersonate<PERSON><PERSON>", "loadUsers", "data", "getUsers", "activeUsers", "filter", "user", "abilitato", "ruolo", "err", "detail", "handleUserChange", "event", "target", "value", "handleImpersonate", "console", "log", "setTimeout", "children", "sx", "display", "justifyContent", "alignItems", "mb", "variant", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "startIcon", "onClick", "disabled", "severity", "length", "mt", "fullWidth", "id", "labelId", "label", "onChange", "map", "id_utente", "username", "color", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/PycharmProjects/CMS/webapp/frontend/src/components/admin/ImpersonateUser.js"], "sourcesContent": ["import React, { useState, useEffect } from 'react';\nimport { useNavigate } from 'react-router-dom';\nimport {\n  Box,\n  Typography,\n  Button,\n  FormControl,\n  InputLabel,\n  Select,\n  MenuItem,\n  Alert,\n  CircularProgress\n} from '@mui/material';\nimport {\n  Refresh as RefreshIcon,\n  Login as LoginIcon\n} from '@mui/icons-material';\nimport userService from '../../services/userService';\nimport authService from '../../services/authService';\nimport { useAuth } from '../../context/AuthContext';\n\nconst ImpersonateUser = () => {\n  const navigate = useNavigate();\n  const [users, setUsers] = useState([]);\n  const [selectedUserId, setSelectedUserId] = useState('');\n  const [loading, setLoading] = useState(true);\n  const [error, setError] = useState('');\n  const { impersonateUser } = useAuth();\n\n  // Carica gli utenti\n  const loadUsers = async () => {\n    setLoading(true);\n    try {\n      const data = await userService.getUsers();\n      // Filtra solo gli utenti attivi e non admin\n      const activeUsers = data.filter(user => user.abilitato && user.ruolo !== 'owner');\n      setUsers(activeUsers);\n      setError('');\n    } catch (err) {\n      setError(err.detail || 'Errore durante il caricamento degli utenti');\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  // Carica gli utenti all'avvio del componente\n  useEffect(() => {\n    loadUsers();\n  }, []);\n\n  // Gestisce il cambio dell'utente selezionato\n  const handleUserChange = (event) => {\n    setSelectedUserId(event.target.value);\n  };\n\n  // Gestisce l'accesso come utente selezionato\n  const handleImpersonate = async () => {\n    if (!selectedUserId) {\n      setError('Seleziona un utente');\n      return;\n    }\n\n    setLoading(true);\n    try {\n      // Utilizza la funzione impersonateUser dal contesto di autenticazione\n      await impersonateUser(selectedUserId);\n\n      console.log('Impersonificazione riuscita');\n\n      // Reindirizza alla dashboard usando il router di React dopo un breve ritardo\n      // per assicurarsi che il contesto di autenticazione sia aggiornato\n      setTimeout(() => {\n        navigate('/dashboard');\n      }, 500);\n    } catch (err) {\n      setError(err.detail || 'Errore durante l\\'accesso come utente selezionato');\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  return (\n    <Box>\n      <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 2 }}>\n        <Typography variant=\"h6\">Accedi come Utente</Typography>\n        <Button\n          variant=\"outlined\"\n          startIcon={<RefreshIcon />}\n          onClick={loadUsers}\n          disabled={loading}\n        >\n          Aggiorna\n        </Button>\n      </Box>\n\n      {error && (\n        <Alert severity=\"error\" sx={{ mb: 2 }}>\n          {error}\n        </Alert>\n      )}\n\n      {loading ? (\n        <CircularProgress />\n      ) : users.length === 0 ? (\n        <Typography>Nessun utente disponibile</Typography>\n      ) : (\n        <Box sx={{ mt: 2 }}>\n          <FormControl fullWidth sx={{ mb: 2 }}>\n            <InputLabel id=\"user-select-label\">Seleziona Utente</InputLabel>\n            <Select\n              labelId=\"user-select-label\"\n              id=\"user-select\"\n              value={selectedUserId}\n              label=\"Seleziona Utente\"\n              onChange={handleUserChange}\n            >\n              {users.map((user) => (\n                <MenuItem key={user.id_utente} value={user.id_utente}>\n                  {user.username} ({user.ruolo})\n                </MenuItem>\n              ))}\n            </Select>\n          </FormControl>\n\n          <Button\n            variant=\"contained\"\n            color=\"primary\"\n            startIcon={<LoginIcon />}\n            onClick={handleImpersonate}\n            disabled={!selectedUserId || loading}\n            fullWidth\n          >\n            Accedi come Utente Selezionato\n          </Button>\n        </Box>\n      )}\n    </Box>\n  );\n};\n\nexport default ImpersonateUser;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAClD,SAASC,WAAW,QAAQ,kBAAkB;AAC9C,SACEC,GAAG,EACHC,UAAU,EACVC,MAAM,EACNC,WAAW,EACXC,UAAU,EACVC,MAAM,EACNC,QAAQ,EACRC,KAAK,EACLC,gBAAgB,QACX,eAAe;AACtB,SACEC,OAAO,IAAIC,WAAW,EACtBC,KAAK,IAAIC,SAAS,QACb,qBAAqB;AAC5B,OAAOC,WAAW,MAAM,4BAA4B;AACpD,OAAOC,WAAW,MAAM,4BAA4B;AACpD,SAASC,OAAO,QAAQ,2BAA2B;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAEpD,MAAMC,eAAe,GAAGA,CAAA,KAAM;EAAAC,EAAA;EAC5B,MAAMC,QAAQ,GAAGrB,WAAW,CAAC,CAAC;EAC9B,MAAM,CAACsB,KAAK,EAAEC,QAAQ,CAAC,GAAGzB,QAAQ,CAAC,EAAE,CAAC;EACtC,MAAM,CAAC0B,cAAc,EAAEC,iBAAiB,CAAC,GAAG3B,QAAQ,CAAC,EAAE,CAAC;EACxD,MAAM,CAAC4B,OAAO,EAAEC,UAAU,CAAC,GAAG7B,QAAQ,CAAC,IAAI,CAAC;EAC5C,MAAM,CAAC8B,KAAK,EAAEC,QAAQ,CAAC,GAAG/B,QAAQ,CAAC,EAAE,CAAC;EACtC,MAAM;IAAEgC;EAAgB,CAAC,GAAGd,OAAO,CAAC,CAAC;;EAErC;EACA,MAAMe,SAAS,GAAG,MAAAA,CAAA,KAAY;IAC5BJ,UAAU,CAAC,IAAI,CAAC;IAChB,IAAI;MACF,MAAMK,IAAI,GAAG,MAAMlB,WAAW,CAACmB,QAAQ,CAAC,CAAC;MACzC;MACA,MAAMC,WAAW,GAAGF,IAAI,CAACG,MAAM,CAACC,IAAI,IAAIA,IAAI,CAACC,SAAS,IAAID,IAAI,CAACE,KAAK,KAAK,OAAO,CAAC;MACjFf,QAAQ,CAACW,WAAW,CAAC;MACrBL,QAAQ,CAAC,EAAE,CAAC;IACd,CAAC,CAAC,OAAOU,GAAG,EAAE;MACZV,QAAQ,CAACU,GAAG,CAACC,MAAM,IAAI,4CAA4C,CAAC;IACtE,CAAC,SAAS;MACRb,UAAU,CAAC,KAAK,CAAC;IACnB;EACF,CAAC;;EAED;EACA5B,SAAS,CAAC,MAAM;IACdgC,SAAS,CAAC,CAAC;EACb,CAAC,EAAE,EAAE,CAAC;;EAEN;EACA,MAAMU,gBAAgB,GAAIC,KAAK,IAAK;IAClCjB,iBAAiB,CAACiB,KAAK,CAACC,MAAM,CAACC,KAAK,CAAC;EACvC,CAAC;;EAED;EACA,MAAMC,iBAAiB,GAAG,MAAAA,CAAA,KAAY;IACpC,IAAI,CAACrB,cAAc,EAAE;MACnBK,QAAQ,CAAC,qBAAqB,CAAC;MAC/B;IACF;IAEAF,UAAU,CAAC,IAAI,CAAC;IAChB,IAAI;MACF;MACA,MAAMG,eAAe,CAACN,cAAc,CAAC;MAErCsB,OAAO,CAACC,GAAG,CAAC,6BAA6B,CAAC;;MAE1C;MACA;MACAC,UAAU,CAAC,MAAM;QACf3B,QAAQ,CAAC,YAAY,CAAC;MACxB,CAAC,EAAE,GAAG,CAAC;IACT,CAAC,CAAC,OAAOkB,GAAG,EAAE;MACZV,QAAQ,CAACU,GAAG,CAACC,MAAM,IAAI,mDAAmD,CAAC;IAC7E,CAAC,SAAS;MACRb,UAAU,CAAC,KAAK,CAAC;IACnB;EACF,CAAC;EAED,oBACET,OAAA,CAACjB,GAAG;IAAAgD,QAAA,gBACF/B,OAAA,CAACjB,GAAG;MAACiD,EAAE,EAAE;QAAEC,OAAO,EAAE,MAAM;QAAEC,cAAc,EAAE,eAAe;QAAEC,UAAU,EAAE,QAAQ;QAAEC,EAAE,EAAE;MAAE,CAAE;MAAAL,QAAA,gBACzF/B,OAAA,CAAChB,UAAU;QAACqD,OAAO,EAAC,IAAI;QAAAN,QAAA,EAAC;MAAkB;QAAAO,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAY,CAAC,eACxDzC,OAAA,CAACf,MAAM;QACLoD,OAAO,EAAC,UAAU;QAClBK,SAAS,eAAE1C,OAAA,CAACP,WAAW;UAAA6C,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAE;QAC3BE,OAAO,EAAE9B,SAAU;QACnB+B,QAAQ,EAAEpC,OAAQ;QAAAuB,QAAA,EACnB;MAED;QAAAO,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAQ,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACN,CAAC,EAEL/B,KAAK,iBACJV,OAAA,CAACV,KAAK;MAACuD,QAAQ,EAAC,OAAO;MAACb,EAAE,EAAE;QAAEI,EAAE,EAAE;MAAE,CAAE;MAAAL,QAAA,EACnCrB;IAAK;MAAA4B,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACD,CACR,EAEAjC,OAAO,gBACNR,OAAA,CAACT,gBAAgB;MAAA+C,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC,GAClBrC,KAAK,CAAC0C,MAAM,KAAK,CAAC,gBACpB9C,OAAA,CAAChB,UAAU;MAAA+C,QAAA,EAAC;IAAyB;MAAAO,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAY,CAAC,gBAElDzC,OAAA,CAACjB,GAAG;MAACiD,EAAE,EAAE;QAAEe,EAAE,EAAE;MAAE,CAAE;MAAAhB,QAAA,gBACjB/B,OAAA,CAACd,WAAW;QAAC8D,SAAS;QAAChB,EAAE,EAAE;UAAEI,EAAE,EAAE;QAAE,CAAE;QAAAL,QAAA,gBACnC/B,OAAA,CAACb,UAAU;UAAC8D,EAAE,EAAC,mBAAmB;UAAAlB,QAAA,EAAC;QAAgB;UAAAO,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAY,CAAC,eAChEzC,OAAA,CAACZ,MAAM;UACL8D,OAAO,EAAC,mBAAmB;UAC3BD,EAAE,EAAC,aAAa;UAChBvB,KAAK,EAAEpB,cAAe;UACtB6C,KAAK,EAAC,kBAAkB;UACxBC,QAAQ,EAAE7B,gBAAiB;UAAAQ,QAAA,EAE1B3B,KAAK,CAACiD,GAAG,CAAEnC,IAAI,iBACdlB,OAAA,CAACX,QAAQ;YAAsBqC,KAAK,EAAER,IAAI,CAACoC,SAAU;YAAAvB,QAAA,GAClDb,IAAI,CAACqC,QAAQ,EAAC,IAAE,EAACrC,IAAI,CAACE,KAAK,EAAC,GAC/B;UAAA,GAFeF,IAAI,CAACoC,SAAS;YAAAhB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAEnB,CACX;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACI,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACE,CAAC,eAEdzC,OAAA,CAACf,MAAM;QACLoD,OAAO,EAAC,WAAW;QACnBmB,KAAK,EAAC,SAAS;QACfd,SAAS,eAAE1C,OAAA,CAACL,SAAS;UAAA2C,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAE;QACzBE,OAAO,EAAEhB,iBAAkB;QAC3BiB,QAAQ,EAAE,CAACtC,cAAc,IAAIE,OAAQ;QACrCwC,SAAS;QAAAjB,QAAA,EACV;MAED;QAAAO,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAQ,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACN,CACN;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACE,CAAC;AAEV,CAAC;AAACvC,EAAA,CArHID,eAAe;EAAA,QACFnB,WAAW,EAKAgB,OAAO;AAAA;AAAA2D,EAAA,GAN/BxD,eAAe;AAuHrB,eAAeA,eAAe;AAAC,IAAAwD,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}