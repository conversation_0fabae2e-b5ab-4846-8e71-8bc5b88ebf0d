{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\PycharmProjects\\\\CMS\\\\webapp\\\\frontend\\\\src\\\\components\\\\cavi\\\\ExcelPopup.js\",\n  _s = $RefreshSig$();\nimport React, { useState } from 'react';\nimport { Dialog, DialogTitle, DialogContent, DialogActions, Button, Typography, Box, TextField, CircularProgress, Alert, IconButton, Divider } from '@mui/material';\nimport { Close as CloseIcon, Upload as UploadIcon, Download as DownloadIcon, FileUpload as FileUploadIcon } from '@mui/icons-material';\nimport excelService from '../../services/excelService';\nimport { jsxDEV as _jsxDEV, Fragment as _Fragment } from \"react/jsx-dev-runtime\";\nconst ExcelPopup = ({\n  open,\n  onClose,\n  operationType,\n  cantiereId,\n  onSuccess,\n  onError\n}) => {\n  _s();\n  const [loading, setLoading] = useState(false);\n  const [fileInput, setFileInput] = useState(null);\n  const [revisione, setRevisione] = useState('');\n  const [filePath, setFilePath] = useState('');\n  const [downloadUrl, setDownloadUrl] = useState('');\n\n  // Reset state when dialog opens or operation changes\n  React.useEffect(() => {\n    if (open) {\n      setFileInput(null);\n      setFilePath('');\n      setDownloadUrl('');\n      setRevisione('');\n      setLoading(false);\n    }\n  }, [open, operationType]);\n\n  // Gestisce la chiusura del dialog\n  const handleClose = () => {\n    setLoading(false);\n    setFileInput(null);\n    setFilePath('');\n    setDownloadUrl('');\n    onClose();\n  };\n\n  // Gestisce il cambio del file selezionato\n  const handleFileChange = e => {\n    setFileInput(e.target.files[0]);\n  };\n\n  // Gestisce l'importazione dei cavi da Excel\n  const handleImportaCavi = async () => {\n    try {\n      if (!fileInput) {\n        onError('Seleziona un file Excel da importare');\n        return;\n      }\n      if (!revisione.trim()) {\n        onError('Inserisci il codice identificativo della revisione');\n        return;\n      }\n      setLoading(true);\n      const formData = new FormData();\n      formData.append('file', fileInput);\n      formData.append('revisione', revisione.trim());\n      await excelService.importCavi(cantiereId, formData);\n      onSuccess('Cavi importati con successo');\n      handleClose();\n\n      // Aggiorna la pagina dopo l'importazione\n      setTimeout(() => {\n        window.location.reload();\n      }, 1500);\n    } catch (error) {\n      onError('Errore nell\\'importazione dei cavi: ' + (error.message || 'Errore sconosciuto'));\n      console.error('Errore nell\\'importazione dei cavi:', error);\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  // Gestisce l'importazione del parco bobine da Excel\n  const handleImportaParcoBobine = async () => {\n    try {\n      if (!fileInput) {\n        onError('Seleziona un file Excel da importare');\n        return;\n      }\n      setLoading(true);\n      const formData = new FormData();\n      formData.append('file', fileInput);\n      await excelService.importParcoBobine(cantiereId, formData);\n      onSuccess('Parco bobine importato con successo');\n      handleClose();\n    } catch (error) {\n      onError('Errore nell\\'importazione del parco bobine: ' + (error.message || 'Errore sconosciuto'));\n      console.error('Errore nell\\'importazione del parco bobine:', error);\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  // Gestisce la creazione del template Excel per i cavi\n  const handleCreaTemplateCavi = async () => {\n    try {\n      setLoading(true);\n      const response = await excelService.createCaviTemplate();\n      if (response && response.file_url) {\n        setDownloadUrl(response.file_url);\n        onSuccess('Template Excel per cavi creato e download avviato! Controlla la cartella Download del tuo browser.');\n      } else {\n        onError('Errore nella creazione del template Excel per cavi');\n      }\n    } catch (error) {\n      onError('Errore nella creazione del template Excel per cavi: ' + (error.message || 'Errore sconosciuto'));\n      console.error('Errore nella creazione del template Excel per cavi:', error);\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  // Gestisce la creazione del template Excel per il parco bobine\n  const handleCreaTemplateParcoBobine = async () => {\n    try {\n      setLoading(true);\n      const response = await excelService.createParcoBobineTemplate();\n      if (response && response.file_url) {\n        setDownloadUrl(response.file_url);\n        onSuccess('Template Excel per parco bobine creato e download avviato! Controlla la cartella Download del tuo browser.');\n      } else {\n        onError('Errore nella creazione del template Excel per parco bobine');\n      }\n    } catch (error) {\n      onError('Errore nella creazione del template Excel per parco bobine: ' + (error.message || 'Errore sconosciuto'));\n      console.error('Errore nella creazione del template Excel per parco bobine:', error);\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  // Gestisce l'esportazione dei cavi in Excel\n  const handleEsportaCavi = async () => {\n    try {\n      setLoading(true);\n      const response = await excelService.exportCavi(cantiereId);\n      if (response && response.file_url) {\n        setDownloadUrl(response.file_url);\n        onSuccess('Cavi esportati con successo e download avviato! Controlla la cartella Download del tuo browser.');\n      } else {\n        onError('Errore nell\\'esportazione dei cavi');\n      }\n    } catch (error) {\n      onError('Errore nell\\'esportazione dei cavi: ' + (error.message || 'Errore sconosciuto'));\n      console.error('Errore nell\\'esportazione dei cavi:', error);\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  // Gestisce l'esportazione del parco bobine in Excel\n  const handleEsportaParcoBobine = async () => {\n    try {\n      setLoading(true);\n      const response = await excelService.exportParcoBobine(cantiereId);\n      if (response && response.file_url) {\n        setDownloadUrl(response.file_url);\n        onSuccess('Parco bobine esportato con successo e download avviato! Controlla la cartella Download del tuo browser.');\n      } else {\n        onError('Errore nell\\'esportazione del parco bobine');\n      }\n    } catch (error) {\n      onError('Errore nell\\'esportazione del parco bobine: ' + (error.message || 'Errore sconosciuto'));\n      console.error('Errore nell\\'esportazione del parco bobine:', error);\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  // Esegue l'operazione selezionata\n  const executeOperation = () => {\n    switch (operationType) {\n      case 'importaCavi':\n        handleImportaCavi();\n        break;\n      case 'importaParcoBobine':\n        handleImportaParcoBobine();\n        break;\n      case 'creaTemplateCavi':\n        handleCreaTemplateCavi();\n        break;\n      case 'creaTemplateParcoBobine':\n        handleCreaTemplateParcoBobine();\n        break;\n      case 'esportaCavi':\n        handleEsportaCavi();\n        break;\n      case 'esportaParcoBobine':\n        handleEsportaParcoBobine();\n        break;\n      default:\n        break;\n    }\n  };\n\n  // Determina il titolo del dialog in base all'operazione\n  const getDialogTitle = () => {\n    switch (operationType) {\n      case 'importaCavi':\n        return 'Importa Cavi da Excel';\n      case 'importaParcoBobine':\n        return 'Importa Parco Bobine da Excel';\n      case 'creaTemplateCavi':\n        return 'Crea Template Excel per Cavi';\n      case 'creaTemplateParcoBobine':\n        return 'Crea Template Excel per Parco Bobine';\n      case 'esportaCavi':\n        return 'Esporta Cavi in Excel';\n      case 'esportaParcoBobine':\n        return 'Esporta Parco Bobine in Excel';\n      default:\n        return 'Gestione Excel';\n    }\n  };\n\n  // Determina se mostrare il selettore di file\n  const showFileInput = ['importaCavi', 'importaParcoBobine'].includes(operationType);\n\n  // Determina se mostrare il link di download\n  const showDownloadLink = downloadUrl && ['creaTemplateCavi', 'creaTemplateParcoBobine', 'esportaCavi', 'esportaParcoBobine'].includes(operationType);\n\n  // Determina il testo del pulsante di azione\n  const getActionButtonText = () => {\n    if (showDownloadLink) return 'Chiudi';\n    switch (operationType) {\n      case 'importaCavi':\n      case 'importaParcoBobine':\n        return 'Importa';\n      case 'creaTemplateCavi':\n      case 'creaTemplateParcoBobine':\n        return 'Crea Template';\n      case 'esportaCavi':\n      case 'esportaParcoBobine':\n        return 'Esporta';\n      default:\n        return 'Conferma';\n    }\n  };\n  return /*#__PURE__*/_jsxDEV(Dialog, {\n    open: open,\n    onClose: handleClose,\n    maxWidth: \"sm\",\n    fullWidth: true,\n    PaperProps: {\n      sx: {\n        borderRadius: '8px',\n        boxShadow: '0 4px 20px rgba(0,0,0,0.1)'\n      }\n    },\n    children: [/*#__PURE__*/_jsxDEV(DialogTitle, {\n      sx: {\n        display: 'flex',\n        justifyContent: 'space-between',\n        alignItems: 'center',\n        borderBottom: '1px solid #e0e0e0',\n        pb: 1\n      },\n      children: [/*#__PURE__*/_jsxDEV(Typography, {\n        variant: \"h6\",\n        children: getDialogTitle()\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 284,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(IconButton, {\n        onClick: handleClose,\n        size: \"small\",\n        children: /*#__PURE__*/_jsxDEV(CloseIcon, {}, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 286,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 285,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 277,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(DialogContent, {\n      sx: {\n        pt: 3,\n        pb: 2\n      },\n      children: loading ? /*#__PURE__*/_jsxDEV(Box, {\n        sx: {\n          display: 'flex',\n          flexDirection: 'column',\n          alignItems: 'center',\n          py: 3\n        },\n        children: [/*#__PURE__*/_jsxDEV(CircularProgress, {\n          size: 40\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 293,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(Typography, {\n          variant: \"body1\",\n          sx: {\n            mt: 2\n          },\n          children: \"Elaborazione in corso...\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 294,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 292,\n        columnNumber: 11\n      }, this) : /*#__PURE__*/_jsxDEV(Box, {\n        children: [showFileInput && /*#__PURE__*/_jsxDEV(_Fragment, {\n          children: [/*#__PURE__*/_jsxDEV(Typography, {\n            variant: \"body2\",\n            paragraph: true,\n            children: \"Seleziona un file Excel da importare:\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 302,\n            columnNumber: 17\n          }, this), /*#__PURE__*/_jsxDEV(Box, {\n            sx: {\n              mb: 2\n            },\n            children: [/*#__PURE__*/_jsxDEV(Button, {\n              variant: \"outlined\",\n              component: \"label\",\n              startIcon: /*#__PURE__*/_jsxDEV(UploadIcon, {}, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 309,\n                columnNumber: 32\n              }, this),\n              sx: {\n                mb: 1\n              },\n              children: [\"Seleziona File\", /*#__PURE__*/_jsxDEV(\"input\", {\n                type: \"file\",\n                accept: \".xlsx,.xls\",\n                hidden: true,\n                onChange: handleFileChange\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 313,\n                columnNumber: 21\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 306,\n              columnNumber: 19\n            }, this), fileInput && /*#__PURE__*/_jsxDEV(Typography, {\n              variant: \"body2\",\n              sx: {\n                mt: 1\n              },\n              children: [\"File selezionato: \", fileInput.name]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 321,\n              columnNumber: 21\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 305,\n            columnNumber: 17\n          }, this), operationType === 'importaCavi' && /*#__PURE__*/_jsxDEV(Box, {\n            sx: {\n              mb: 2\n            },\n            children: [/*#__PURE__*/_jsxDEV(Typography, {\n              variant: \"body2\",\n              sx: {\n                mb: 1\n              },\n              children: \"Codice identificativo della revisione:\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 329,\n              columnNumber: 21\n            }, this), /*#__PURE__*/_jsxDEV(TextField, {\n              fullWidth: true,\n              size: \"small\",\n              value: revisione,\n              onChange: e => setRevisione(e.target.value),\n              placeholder: \"Inserisci il codice revisione (es. REV_001, V1.0, etc.)\",\n              variant: \"outlined\",\n              sx: {\n                mb: 1\n              }\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 332,\n              columnNumber: 21\n            }, this), /*#__PURE__*/_jsxDEV(Typography, {\n              variant: \"caption\",\n              color: \"text.secondary\",\n              children: \"Questo codice identificher\\xE0 univocamente questa importazione\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 341,\n              columnNumber: 21\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 328,\n            columnNumber: 19\n          }, this), /*#__PURE__*/_jsxDEV(Alert, {\n            severity: \"info\",\n            sx: {\n              mb: 2\n            },\n            children: /*#__PURE__*/_jsxDEV(Typography, {\n              variant: \"body2\",\n              children: [\"Assicurati che il file Excel sia nel formato corretto.\", operationType === 'importaCavi' && ' Puoi scaricare un template vuoto utilizzando l\\'opzione \"Template Cavi\".', operationType === 'importaParcoBobine' && ' Puoi scaricare un template vuoto utilizzando l\\'opzione \"Template Parco Bobine\".', /*#__PURE__*/_jsxDEV(\"br\", {}, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 352,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(\"strong\", {\n                children: \"Nota:\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 353,\n                columnNumber: 21\n              }, this), \" Il sistema riconosce automaticamente se la prima riga contiene un titolo. In tal caso, le intestazioni delle colonne sono attese nella seconda riga.\"]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 348,\n              columnNumber: 19\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 347,\n            columnNumber: 17\n          }, this)]\n        }, void 0, true), ['creaTemplateCavi', 'creaTemplateParcoBobine'].includes(operationType) && !showDownloadLink && /*#__PURE__*/_jsxDEV(Typography, {\n          variant: \"body2\",\n          paragraph: true,\n          children: [\"Clicca su \\\"\", getActionButtonText(), \"\\\" per generare un template Excel vuoto.\"]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 360,\n          columnNumber: 15\n        }, this), ['esportaCavi', 'esportaParcoBobine'].includes(operationType) && !showDownloadLink && /*#__PURE__*/_jsxDEV(Typography, {\n          variant: \"body2\",\n          paragraph: true,\n          children: [\"Clicca su \\\"\", getActionButtonText(), \"\\\" per esportare i dati in un file Excel.\"]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 366,\n          columnNumber: 15\n        }, this), showDownloadLink && /*#__PURE__*/_jsxDEV(Box, {\n          sx: {\n            mt: 2,\n            textAlign: 'center'\n          },\n          children: [/*#__PURE__*/_jsxDEV(Alert, {\n            severity: \"success\",\n            sx: {\n              mb: 2\n            },\n            children: /*#__PURE__*/_jsxDEV(Typography, {\n              variant: \"body2\",\n              children: \"File generato con successo!\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 374,\n              columnNumber: 19\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 373,\n            columnNumber: 17\n          }, this), /*#__PURE__*/_jsxDEV(Button, {\n            variant: \"contained\",\n            color: \"primary\",\n            startIcon: /*#__PURE__*/_jsxDEV(DownloadIcon, {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 381,\n              columnNumber: 30\n            }, this),\n            href: downloadUrl,\n            target: \"_blank\",\n            rel: \"noopener noreferrer\",\n            sx: {\n              mt: 1\n            },\n            children: \"Scarica File\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 378,\n            columnNumber: 17\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 372,\n          columnNumber: 15\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 299,\n        columnNumber: 11\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 290,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(DialogActions, {\n      sx: {\n        px: 3,\n        py: 2,\n        borderTop: '1px solid #e0e0e0'\n      },\n      children: [/*#__PURE__*/_jsxDEV(Button, {\n        onClick: handleClose,\n        color: \"inherit\",\n        variant: \"outlined\",\n        sx: {\n          mr: 1\n        },\n        children: \"Annulla\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 396,\n        columnNumber: 9\n      }, this), !showDownloadLink && /*#__PURE__*/_jsxDEV(Button, {\n        onClick: executeOperation,\n        color: \"primary\",\n        variant: \"contained\",\n        startIcon: showFileInput ? /*#__PURE__*/_jsxDEV(FileUploadIcon, {}, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 409,\n          columnNumber: 40\n        }, this) : /*#__PURE__*/_jsxDEV(DownloadIcon, {}, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 409,\n          columnNumber: 61\n        }, this),\n        disabled: loading || showFileInput && !fileInput || operationType === 'importaCavi' && !revisione.trim(),\n        children: getActionButtonText()\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 405,\n        columnNumber: 11\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 395,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 265,\n    columnNumber: 5\n  }, this);\n};\n_s(ExcelPopup, \"nhJtkRmjdPZmv9HFGuNlzrEb0Vs=\");\n_c = ExcelPopup;\nexport default ExcelPopup;\nvar _c;\n$RefreshReg$(_c, \"ExcelPopup\");", "map": {"version": 3, "names": ["React", "useState", "Dialog", "DialogTitle", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "DialogActions", "<PERSON><PERSON>", "Typography", "Box", "TextField", "CircularProgress", "<PERSON><PERSON>", "IconButton", "Divider", "Close", "CloseIcon", "Upload", "UploadIcon", "Download", "DownloadIcon", "FileUpload", "FileUploadIcon", "excelService", "jsxDEV", "_jsxDEV", "Fragment", "_Fragment", "ExcelPopup", "open", "onClose", "operationType", "cantiereId", "onSuccess", "onError", "_s", "loading", "setLoading", "fileInput", "setFileInput", "revisione", "setRevisione", "filePath", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "downloadUrl", "setDownloadUrl", "useEffect", "handleClose", "handleFileChange", "e", "target", "files", "handleImportaCavi", "trim", "formData", "FormData", "append", "importCavi", "setTimeout", "window", "location", "reload", "error", "message", "console", "handleImportaParcoBobine", "importParcoBobine", "handleCreaTemplateCavi", "response", "createCaviTemplate", "file_url", "handleCreaTemplateParcoBobine", "createParcoBobineTemplate", "handleEsportaCavi", "exportCavi", "handleEsportaParcoBobine", "exportParcoBobine", "executeOperation", "getDialogTitle", "showFileInput", "includes", "showDownloadLink", "getActionButtonText", "max<PERSON><PERSON><PERSON>", "fullWidth", "PaperProps", "sx", "borderRadius", "boxShadow", "children", "display", "justifyContent", "alignItems", "borderBottom", "pb", "variant", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "onClick", "size", "pt", "flexDirection", "py", "mt", "paragraph", "mb", "component", "startIcon", "type", "accept", "hidden", "onChange", "name", "value", "placeholder", "color", "severity", "textAlign", "href", "rel", "px", "borderTop", "mr", "disabled", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/PycharmProjects/CMS/webapp/frontend/src/components/cavi/ExcelPopup.js"], "sourcesContent": ["import React, { useState } from 'react';\nimport {\n  Dialog,\n  DialogTitle,\n  DialogContent,\n  DialogActions,\n  Button,\n  Typography,\n  Box,\n  TextField,\n  CircularProgress,\n  Alert,\n  IconButton,\n  Divider\n} from '@mui/material';\nimport {\n  Close as CloseIcon,\n  Upload as UploadIcon,\n  Download as DownloadIcon,\n  FileUpload as FileUploadIcon\n} from '@mui/icons-material';\nimport excelService from '../../services/excelService';\n\nconst ExcelPopup = ({ open, onClose, operationType, cantiereId, onSuccess, onError }) => {\n  const [loading, setLoading] = useState(false);\n  const [fileInput, setFileInput] = useState(null);\n  const [revisione, setRevisione] = useState('');\n  const [filePath, setFilePath] = useState('');\n  const [downloadUrl, setDownloadUrl] = useState('');\n\n  // Reset state when dialog opens or operation changes\n  React.useEffect(() => {\n    if (open) {\n      setFileInput(null);\n      setFilePath('');\n      setDownloadUrl('');\n      setRevisione('');\n      setLoading(false);\n    }\n  }, [open, operationType]);\n\n  // Gestisce la chiusura del dialog\n  const handleClose = () => {\n    setLoading(false);\n    setFileInput(null);\n    setFilePath('');\n    setDownloadUrl('');\n    onClose();\n  };\n\n  // Gestisce il cambio del file selezionato\n  const handleFileChange = (e) => {\n    setFileInput(e.target.files[0]);\n  };\n\n  // Gestisce l'importazione dei cavi da Excel\n  const handleImportaCavi = async () => {\n    try {\n      if (!fileInput) {\n        onError('Seleziona un file Excel da importare');\n        return;\n      }\n\n      if (!revisione.trim()) {\n        onError('Inserisci il codice identificativo della revisione');\n        return;\n      }\n\n      setLoading(true);\n      const formData = new FormData();\n      formData.append('file', fileInput);\n      formData.append('revisione', revisione.trim());\n\n      await excelService.importCavi(cantiereId, formData);\n      onSuccess('Cavi importati con successo');\n      handleClose();\n\n      // Aggiorna la pagina dopo l'importazione\n      setTimeout(() => {\n        window.location.reload();\n      }, 1500);\n    } catch (error) {\n      onError('Errore nell\\'importazione dei cavi: ' + (error.message || 'Errore sconosciuto'));\n      console.error('Errore nell\\'importazione dei cavi:', error);\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  // Gestisce l'importazione del parco bobine da Excel\n  const handleImportaParcoBobine = async () => {\n    try {\n      if (!fileInput) {\n        onError('Seleziona un file Excel da importare');\n        return;\n      }\n\n      setLoading(true);\n      const formData = new FormData();\n      formData.append('file', fileInput);\n\n      await excelService.importParcoBobine(cantiereId, formData);\n      onSuccess('Parco bobine importato con successo');\n      handleClose();\n    } catch (error) {\n      onError('Errore nell\\'importazione del parco bobine: ' + (error.message || 'Errore sconosciuto'));\n      console.error('Errore nell\\'importazione del parco bobine:', error);\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  // Gestisce la creazione del template Excel per i cavi\n  const handleCreaTemplateCavi = async () => {\n    try {\n      setLoading(true);\n      const response = await excelService.createCaviTemplate();\n\n      if (response && response.file_url) {\n        setDownloadUrl(response.file_url);\n        onSuccess('Template Excel per cavi creato e download avviato! Controlla la cartella Download del tuo browser.');\n      } else {\n        onError('Errore nella creazione del template Excel per cavi');\n      }\n    } catch (error) {\n      onError('Errore nella creazione del template Excel per cavi: ' + (error.message || 'Errore sconosciuto'));\n      console.error('Errore nella creazione del template Excel per cavi:', error);\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  // Gestisce la creazione del template Excel per il parco bobine\n  const handleCreaTemplateParcoBobine = async () => {\n    try {\n      setLoading(true);\n      const response = await excelService.createParcoBobineTemplate();\n\n      if (response && response.file_url) {\n        setDownloadUrl(response.file_url);\n        onSuccess('Template Excel per parco bobine creato e download avviato! Controlla la cartella Download del tuo browser.');\n      } else {\n        onError('Errore nella creazione del template Excel per parco bobine');\n      }\n    } catch (error) {\n      onError('Errore nella creazione del template Excel per parco bobine: ' + (error.message || 'Errore sconosciuto'));\n      console.error('Errore nella creazione del template Excel per parco bobine:', error);\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  // Gestisce l'esportazione dei cavi in Excel\n  const handleEsportaCavi = async () => {\n    try {\n      setLoading(true);\n      const response = await excelService.exportCavi(cantiereId);\n\n      if (response && response.file_url) {\n        setDownloadUrl(response.file_url);\n        onSuccess('Cavi esportati con successo e download avviato! Controlla la cartella Download del tuo browser.');\n      } else {\n        onError('Errore nell\\'esportazione dei cavi');\n      }\n    } catch (error) {\n      onError('Errore nell\\'esportazione dei cavi: ' + (error.message || 'Errore sconosciuto'));\n      console.error('Errore nell\\'esportazione dei cavi:', error);\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  // Gestisce l'esportazione del parco bobine in Excel\n  const handleEsportaParcoBobine = async () => {\n    try {\n      setLoading(true);\n      const response = await excelService.exportParcoBobine(cantiereId);\n\n      if (response && response.file_url) {\n        setDownloadUrl(response.file_url);\n        onSuccess('Parco bobine esportato con successo e download avviato! Controlla la cartella Download del tuo browser.');\n      } else {\n        onError('Errore nell\\'esportazione del parco bobine');\n      }\n    } catch (error) {\n      onError('Errore nell\\'esportazione del parco bobine: ' + (error.message || 'Errore sconosciuto'));\n      console.error('Errore nell\\'esportazione del parco bobine:', error);\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  // Esegue l'operazione selezionata\n  const executeOperation = () => {\n    switch (operationType) {\n      case 'importaCavi':\n        handleImportaCavi();\n        break;\n      case 'importaParcoBobine':\n        handleImportaParcoBobine();\n        break;\n      case 'creaTemplateCavi':\n        handleCreaTemplateCavi();\n        break;\n      case 'creaTemplateParcoBobine':\n        handleCreaTemplateParcoBobine();\n        break;\n      case 'esportaCavi':\n        handleEsportaCavi();\n        break;\n      case 'esportaParcoBobine':\n        handleEsportaParcoBobine();\n        break;\n      default:\n        break;\n    }\n  };\n\n  // Determina il titolo del dialog in base all'operazione\n  const getDialogTitle = () => {\n    switch (operationType) {\n      case 'importaCavi':\n        return 'Importa Cavi da Excel';\n      case 'importaParcoBobine':\n        return 'Importa Parco Bobine da Excel';\n      case 'creaTemplateCavi':\n        return 'Crea Template Excel per Cavi';\n      case 'creaTemplateParcoBobine':\n        return 'Crea Template Excel per Parco Bobine';\n      case 'esportaCavi':\n        return 'Esporta Cavi in Excel';\n      case 'esportaParcoBobine':\n        return 'Esporta Parco Bobine in Excel';\n      default:\n        return 'Gestione Excel';\n    }\n  };\n\n  // Determina se mostrare il selettore di file\n  const showFileInput = ['importaCavi', 'importaParcoBobine'].includes(operationType);\n\n  // Determina se mostrare il link di download\n  const showDownloadLink = downloadUrl && ['creaTemplateCavi', 'creaTemplateParcoBobine', 'esportaCavi', 'esportaParcoBobine'].includes(operationType);\n\n  // Determina il testo del pulsante di azione\n  const getActionButtonText = () => {\n    if (showDownloadLink) return 'Chiudi';\n\n    switch (operationType) {\n      case 'importaCavi':\n      case 'importaParcoBobine':\n        return 'Importa';\n      case 'creaTemplateCavi':\n      case 'creaTemplateParcoBobine':\n        return 'Crea Template';\n      case 'esportaCavi':\n      case 'esportaParcoBobine':\n        return 'Esporta';\n      default:\n        return 'Conferma';\n    }\n  };\n\n  return (\n    <Dialog\n      open={open}\n      onClose={handleClose}\n      maxWidth=\"sm\"\n      fullWidth\n      PaperProps={{\n        sx: {\n          borderRadius: '8px',\n          boxShadow: '0 4px 20px rgba(0,0,0,0.1)'\n        }\n      }}\n    >\n      <DialogTitle sx={{\n        display: 'flex',\n        justifyContent: 'space-between',\n        alignItems: 'center',\n        borderBottom: '1px solid #e0e0e0',\n        pb: 1\n      }}>\n        <Typography variant=\"h6\">{getDialogTitle()}</Typography>\n        <IconButton onClick={handleClose} size=\"small\">\n          <CloseIcon />\n        </IconButton>\n      </DialogTitle>\n\n      <DialogContent sx={{ pt: 3, pb: 2 }}>\n        {loading ? (\n          <Box sx={{ display: 'flex', flexDirection: 'column', alignItems: 'center', py: 3 }}>\n            <CircularProgress size={40} />\n            <Typography variant=\"body1\" sx={{ mt: 2 }}>\n              Elaborazione in corso...\n            </Typography>\n          </Box>\n        ) : (\n          <Box>\n            {showFileInput && (\n              <>\n                <Typography variant=\"body2\" paragraph>\n                  Seleziona un file Excel da importare:\n                </Typography>\n                <Box sx={{ mb: 2 }}>\n                  <Button\n                    variant=\"outlined\"\n                    component=\"label\"\n                    startIcon={<UploadIcon />}\n                    sx={{ mb: 1 }}\n                  >\n                    Seleziona File\n                    <input\n                      type=\"file\"\n                      accept=\".xlsx,.xls\"\n                      hidden\n                      onChange={handleFileChange}\n                    />\n                  </Button>\n                  {fileInput && (\n                    <Typography variant=\"body2\" sx={{ mt: 1 }}>\n                      File selezionato: {fileInput.name}\n                    </Typography>\n                  )}\n                </Box>\n\n                {operationType === 'importaCavi' && (\n                  <Box sx={{ mb: 2 }}>\n                    <Typography variant=\"body2\" sx={{ mb: 1 }}>\n                      Codice identificativo della revisione:\n                    </Typography>\n                    <TextField\n                      fullWidth\n                      size=\"small\"\n                      value={revisione}\n                      onChange={(e) => setRevisione(e.target.value)}\n                      placeholder=\"Inserisci il codice revisione (es. REV_001, V1.0, etc.)\"\n                      variant=\"outlined\"\n                      sx={{ mb: 1 }}\n                    />\n                    <Typography variant=\"caption\" color=\"text.secondary\">\n                      Questo codice identificherà univocamente questa importazione\n                    </Typography>\n                  </Box>\n                )}\n\n                <Alert severity=\"info\" sx={{ mb: 2 }}>\n                  <Typography variant=\"body2\">\n                    Assicurati che il file Excel sia nel formato corretto.\n                    {operationType === 'importaCavi' && ' Puoi scaricare un template vuoto utilizzando l\\'opzione \"Template Cavi\".'}\n                    {operationType === 'importaParcoBobine' && ' Puoi scaricare un template vuoto utilizzando l\\'opzione \"Template Parco Bobine\".'}\n                    <br />\n                    <strong>Nota:</strong> Il sistema riconosce automaticamente se la prima riga contiene un titolo. In tal caso, le intestazioni delle colonne sono attese nella seconda riga.\n                  </Typography>\n                </Alert>\n              </>\n            )}\n\n            {['creaTemplateCavi', 'creaTemplateParcoBobine'].includes(operationType) && !showDownloadLink && (\n              <Typography variant=\"body2\" paragraph>\n                Clicca su \"{getActionButtonText()}\" per generare un template Excel vuoto.\n              </Typography>\n            )}\n\n            {['esportaCavi', 'esportaParcoBobine'].includes(operationType) && !showDownloadLink && (\n              <Typography variant=\"body2\" paragraph>\n                Clicca su \"{getActionButtonText()}\" per esportare i dati in un file Excel.\n              </Typography>\n            )}\n\n            {showDownloadLink && (\n              <Box sx={{ mt: 2, textAlign: 'center' }}>\n                <Alert severity=\"success\" sx={{ mb: 2 }}>\n                  <Typography variant=\"body2\">\n                    File generato con successo!\n                  </Typography>\n                </Alert>\n                <Button\n                  variant=\"contained\"\n                  color=\"primary\"\n                  startIcon={<DownloadIcon />}\n                  href={downloadUrl}\n                  target=\"_blank\"\n                  rel=\"noopener noreferrer\"\n                  sx={{ mt: 1 }}\n                >\n                  Scarica File\n                </Button>\n              </Box>\n            )}\n          </Box>\n        )}\n      </DialogContent>\n\n      <DialogActions sx={{ px: 3, py: 2, borderTop: '1px solid #e0e0e0' }}>\n        <Button\n          onClick={handleClose}\n          color=\"inherit\"\n          variant=\"outlined\"\n          sx={{ mr: 1 }}\n        >\n          Annulla\n        </Button>\n        {!showDownloadLink && (\n          <Button\n            onClick={executeOperation}\n            color=\"primary\"\n            variant=\"contained\"\n            startIcon={showFileInput ? <FileUploadIcon /> : <DownloadIcon />}\n            disabled={loading || (showFileInput && !fileInput) || (operationType === 'importaCavi' && !revisione.trim())}\n          >\n            {getActionButtonText()}\n          </Button>\n        )}\n      </DialogActions>\n    </Dialog>\n  );\n};\n\nexport default ExcelPopup;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,QAAQ,OAAO;AACvC,SACEC,MAAM,EACNC,WAAW,EACXC,aAAa,EACbC,aAAa,EACbC,MAAM,EACNC,UAAU,EACVC,GAAG,EACHC,SAAS,EACTC,gBAAgB,EAChBC,KAAK,EACLC,UAAU,EACVC,OAAO,QACF,eAAe;AACtB,SACEC,KAAK,IAAIC,SAAS,EAClBC,MAAM,IAAIC,UAAU,EACpBC,QAAQ,IAAIC,YAAY,EACxBC,UAAU,IAAIC,cAAc,QACvB,qBAAqB;AAC5B,OAAOC,YAAY,MAAM,6BAA6B;AAAC,SAAAC,MAAA,IAAAC,OAAA,EAAAC,QAAA,IAAAC,SAAA;AAEvD,MAAMC,UAAU,GAAGA,CAAC;EAAEC,IAAI;EAAEC,OAAO;EAAEC,aAAa;EAAEC,UAAU;EAAEC,SAAS;EAAEC;AAAQ,CAAC,KAAK;EAAAC,EAAA;EACvF,MAAM,CAACC,OAAO,EAAEC,UAAU,CAAC,GAAGnC,QAAQ,CAAC,KAAK,CAAC;EAC7C,MAAM,CAACoC,SAAS,EAAEC,YAAY,CAAC,GAAGrC,QAAQ,CAAC,IAAI,CAAC;EAChD,MAAM,CAACsC,SAAS,EAAEC,YAAY,CAAC,GAAGvC,QAAQ,CAAC,EAAE,CAAC;EAC9C,MAAM,CAACwC,QAAQ,EAAEC,WAAW,CAAC,GAAGzC,QAAQ,CAAC,EAAE,CAAC;EAC5C,MAAM,CAAC0C,WAAW,EAAEC,cAAc,CAAC,GAAG3C,QAAQ,CAAC,EAAE,CAAC;;EAElD;EACAD,KAAK,CAAC6C,SAAS,CAAC,MAAM;IACpB,IAAIjB,IAAI,EAAE;MACRU,YAAY,CAAC,IAAI,CAAC;MAClBI,WAAW,CAAC,EAAE,CAAC;MACfE,cAAc,CAAC,EAAE,CAAC;MAClBJ,YAAY,CAAC,EAAE,CAAC;MAChBJ,UAAU,CAAC,KAAK,CAAC;IACnB;EACF,CAAC,EAAE,CAACR,IAAI,EAAEE,aAAa,CAAC,CAAC;;EAEzB;EACA,MAAMgB,WAAW,GAAGA,CAAA,KAAM;IACxBV,UAAU,CAAC,KAAK,CAAC;IACjBE,YAAY,CAAC,IAAI,CAAC;IAClBI,WAAW,CAAC,EAAE,CAAC;IACfE,cAAc,CAAC,EAAE,CAAC;IAClBf,OAAO,CAAC,CAAC;EACX,CAAC;;EAED;EACA,MAAMkB,gBAAgB,GAAIC,CAAC,IAAK;IAC9BV,YAAY,CAACU,CAAC,CAACC,MAAM,CAACC,KAAK,CAAC,CAAC,CAAC,CAAC;EACjC,CAAC;;EAED;EACA,MAAMC,iBAAiB,GAAG,MAAAA,CAAA,KAAY;IACpC,IAAI;MACF,IAAI,CAACd,SAAS,EAAE;QACdJ,OAAO,CAAC,sCAAsC,CAAC;QAC/C;MACF;MAEA,IAAI,CAACM,SAAS,CAACa,IAAI,CAAC,CAAC,EAAE;QACrBnB,OAAO,CAAC,oDAAoD,CAAC;QAC7D;MACF;MAEAG,UAAU,CAAC,IAAI,CAAC;MAChB,MAAMiB,QAAQ,GAAG,IAAIC,QAAQ,CAAC,CAAC;MAC/BD,QAAQ,CAACE,MAAM,CAAC,MAAM,EAAElB,SAAS,CAAC;MAClCgB,QAAQ,CAACE,MAAM,CAAC,WAAW,EAAEhB,SAAS,CAACa,IAAI,CAAC,CAAC,CAAC;MAE9C,MAAM9B,YAAY,CAACkC,UAAU,CAACzB,UAAU,EAAEsB,QAAQ,CAAC;MACnDrB,SAAS,CAAC,6BAA6B,CAAC;MACxCc,WAAW,CAAC,CAAC;;MAEb;MACAW,UAAU,CAAC,MAAM;QACfC,MAAM,CAACC,QAAQ,CAACC,MAAM,CAAC,CAAC;MAC1B,CAAC,EAAE,IAAI,CAAC;IACV,CAAC,CAAC,OAAOC,KAAK,EAAE;MACd5B,OAAO,CAAC,sCAAsC,IAAI4B,KAAK,CAACC,OAAO,IAAI,oBAAoB,CAAC,CAAC;MACzFC,OAAO,CAACF,KAAK,CAAC,qCAAqC,EAAEA,KAAK,CAAC;IAC7D,CAAC,SAAS;MACRzB,UAAU,CAAC,KAAK,CAAC;IACnB;EACF,CAAC;;EAED;EACA,MAAM4B,wBAAwB,GAAG,MAAAA,CAAA,KAAY;IAC3C,IAAI;MACF,IAAI,CAAC3B,SAAS,EAAE;QACdJ,OAAO,CAAC,sCAAsC,CAAC;QAC/C;MACF;MAEAG,UAAU,CAAC,IAAI,CAAC;MAChB,MAAMiB,QAAQ,GAAG,IAAIC,QAAQ,CAAC,CAAC;MAC/BD,QAAQ,CAACE,MAAM,CAAC,MAAM,EAAElB,SAAS,CAAC;MAElC,MAAMf,YAAY,CAAC2C,iBAAiB,CAAClC,UAAU,EAAEsB,QAAQ,CAAC;MAC1DrB,SAAS,CAAC,qCAAqC,CAAC;MAChDc,WAAW,CAAC,CAAC;IACf,CAAC,CAAC,OAAOe,KAAK,EAAE;MACd5B,OAAO,CAAC,8CAA8C,IAAI4B,KAAK,CAACC,OAAO,IAAI,oBAAoB,CAAC,CAAC;MACjGC,OAAO,CAACF,KAAK,CAAC,6CAA6C,EAAEA,KAAK,CAAC;IACrE,CAAC,SAAS;MACRzB,UAAU,CAAC,KAAK,CAAC;IACnB;EACF,CAAC;;EAED;EACA,MAAM8B,sBAAsB,GAAG,MAAAA,CAAA,KAAY;IACzC,IAAI;MACF9B,UAAU,CAAC,IAAI,CAAC;MAChB,MAAM+B,QAAQ,GAAG,MAAM7C,YAAY,CAAC8C,kBAAkB,CAAC,CAAC;MAExD,IAAID,QAAQ,IAAIA,QAAQ,CAACE,QAAQ,EAAE;QACjCzB,cAAc,CAACuB,QAAQ,CAACE,QAAQ,CAAC;QACjCrC,SAAS,CAAC,oGAAoG,CAAC;MACjH,CAAC,MAAM;QACLC,OAAO,CAAC,oDAAoD,CAAC;MAC/D;IACF,CAAC,CAAC,OAAO4B,KAAK,EAAE;MACd5B,OAAO,CAAC,sDAAsD,IAAI4B,KAAK,CAACC,OAAO,IAAI,oBAAoB,CAAC,CAAC;MACzGC,OAAO,CAACF,KAAK,CAAC,qDAAqD,EAAEA,KAAK,CAAC;IAC7E,CAAC,SAAS;MACRzB,UAAU,CAAC,KAAK,CAAC;IACnB;EACF,CAAC;;EAED;EACA,MAAMkC,6BAA6B,GAAG,MAAAA,CAAA,KAAY;IAChD,IAAI;MACFlC,UAAU,CAAC,IAAI,CAAC;MAChB,MAAM+B,QAAQ,GAAG,MAAM7C,YAAY,CAACiD,yBAAyB,CAAC,CAAC;MAE/D,IAAIJ,QAAQ,IAAIA,QAAQ,CAACE,QAAQ,EAAE;QACjCzB,cAAc,CAACuB,QAAQ,CAACE,QAAQ,CAAC;QACjCrC,SAAS,CAAC,4GAA4G,CAAC;MACzH,CAAC,MAAM;QACLC,OAAO,CAAC,4DAA4D,CAAC;MACvE;IACF,CAAC,CAAC,OAAO4B,KAAK,EAAE;MACd5B,OAAO,CAAC,8DAA8D,IAAI4B,KAAK,CAACC,OAAO,IAAI,oBAAoB,CAAC,CAAC;MACjHC,OAAO,CAACF,KAAK,CAAC,6DAA6D,EAAEA,KAAK,CAAC;IACrF,CAAC,SAAS;MACRzB,UAAU,CAAC,KAAK,CAAC;IACnB;EACF,CAAC;;EAED;EACA,MAAMoC,iBAAiB,GAAG,MAAAA,CAAA,KAAY;IACpC,IAAI;MACFpC,UAAU,CAAC,IAAI,CAAC;MAChB,MAAM+B,QAAQ,GAAG,MAAM7C,YAAY,CAACmD,UAAU,CAAC1C,UAAU,CAAC;MAE1D,IAAIoC,QAAQ,IAAIA,QAAQ,CAACE,QAAQ,EAAE;QACjCzB,cAAc,CAACuB,QAAQ,CAACE,QAAQ,CAAC;QACjCrC,SAAS,CAAC,iGAAiG,CAAC;MAC9G,CAAC,MAAM;QACLC,OAAO,CAAC,oCAAoC,CAAC;MAC/C;IACF,CAAC,CAAC,OAAO4B,KAAK,EAAE;MACd5B,OAAO,CAAC,sCAAsC,IAAI4B,KAAK,CAACC,OAAO,IAAI,oBAAoB,CAAC,CAAC;MACzFC,OAAO,CAACF,KAAK,CAAC,qCAAqC,EAAEA,KAAK,CAAC;IAC7D,CAAC,SAAS;MACRzB,UAAU,CAAC,KAAK,CAAC;IACnB;EACF,CAAC;;EAED;EACA,MAAMsC,wBAAwB,GAAG,MAAAA,CAAA,KAAY;IAC3C,IAAI;MACFtC,UAAU,CAAC,IAAI,CAAC;MAChB,MAAM+B,QAAQ,GAAG,MAAM7C,YAAY,CAACqD,iBAAiB,CAAC5C,UAAU,CAAC;MAEjE,IAAIoC,QAAQ,IAAIA,QAAQ,CAACE,QAAQ,EAAE;QACjCzB,cAAc,CAACuB,QAAQ,CAACE,QAAQ,CAAC;QACjCrC,SAAS,CAAC,yGAAyG,CAAC;MACtH,CAAC,MAAM;QACLC,OAAO,CAAC,4CAA4C,CAAC;MACvD;IACF,CAAC,CAAC,OAAO4B,KAAK,EAAE;MACd5B,OAAO,CAAC,8CAA8C,IAAI4B,KAAK,CAACC,OAAO,IAAI,oBAAoB,CAAC,CAAC;MACjGC,OAAO,CAACF,KAAK,CAAC,6CAA6C,EAAEA,KAAK,CAAC;IACrE,CAAC,SAAS;MACRzB,UAAU,CAAC,KAAK,CAAC;IACnB;EACF,CAAC;;EAED;EACA,MAAMwC,gBAAgB,GAAGA,CAAA,KAAM;IAC7B,QAAQ9C,aAAa;MACnB,KAAK,aAAa;QAChBqB,iBAAiB,CAAC,CAAC;QACnB;MACF,KAAK,oBAAoB;QACvBa,wBAAwB,CAAC,CAAC;QAC1B;MACF,KAAK,kBAAkB;QACrBE,sBAAsB,CAAC,CAAC;QACxB;MACF,KAAK,yBAAyB;QAC5BI,6BAA6B,CAAC,CAAC;QAC/B;MACF,KAAK,aAAa;QAChBE,iBAAiB,CAAC,CAAC;QACnB;MACF,KAAK,oBAAoB;QACvBE,wBAAwB,CAAC,CAAC;QAC1B;MACF;QACE;IACJ;EACF,CAAC;;EAED;EACA,MAAMG,cAAc,GAAGA,CAAA,KAAM;IAC3B,QAAQ/C,aAAa;MACnB,KAAK,aAAa;QAChB,OAAO,uBAAuB;MAChC,KAAK,oBAAoB;QACvB,OAAO,+BAA+B;MACxC,KAAK,kBAAkB;QACrB,OAAO,8BAA8B;MACvC,KAAK,yBAAyB;QAC5B,OAAO,sCAAsC;MAC/C,KAAK,aAAa;QAChB,OAAO,uBAAuB;MAChC,KAAK,oBAAoB;QACvB,OAAO,+BAA+B;MACxC;QACE,OAAO,gBAAgB;IAC3B;EACF,CAAC;;EAED;EACA,MAAMgD,aAAa,GAAG,CAAC,aAAa,EAAE,oBAAoB,CAAC,CAACC,QAAQ,CAACjD,aAAa,CAAC;;EAEnF;EACA,MAAMkD,gBAAgB,GAAGrC,WAAW,IAAI,CAAC,kBAAkB,EAAE,yBAAyB,EAAE,aAAa,EAAE,oBAAoB,CAAC,CAACoC,QAAQ,CAACjD,aAAa,CAAC;;EAEpJ;EACA,MAAMmD,mBAAmB,GAAGA,CAAA,KAAM;IAChC,IAAID,gBAAgB,EAAE,OAAO,QAAQ;IAErC,QAAQlD,aAAa;MACnB,KAAK,aAAa;MAClB,KAAK,oBAAoB;QACvB,OAAO,SAAS;MAClB,KAAK,kBAAkB;MACvB,KAAK,yBAAyB;QAC5B,OAAO,eAAe;MACxB,KAAK,aAAa;MAClB,KAAK,oBAAoB;QACvB,OAAO,SAAS;MAClB;QACE,OAAO,UAAU;IACrB;EACF,CAAC;EAED,oBACEN,OAAA,CAACtB,MAAM;IACL0B,IAAI,EAAEA,IAAK;IACXC,OAAO,EAAEiB,WAAY;IACrBoC,QAAQ,EAAC,IAAI;IACbC,SAAS;IACTC,UAAU,EAAE;MACVC,EAAE,EAAE;QACFC,YAAY,EAAE,KAAK;QACnBC,SAAS,EAAE;MACb;IACF,CAAE;IAAAC,QAAA,gBAEFhE,OAAA,CAACrB,WAAW;MAACkF,EAAE,EAAE;QACfI,OAAO,EAAE,MAAM;QACfC,cAAc,EAAE,eAAe;QAC/BC,UAAU,EAAE,QAAQ;QACpBC,YAAY,EAAE,mBAAmB;QACjCC,EAAE,EAAE;MACN,CAAE;MAAAL,QAAA,gBACAhE,OAAA,CAACjB,UAAU;QAACuF,OAAO,EAAC,IAAI;QAAAN,QAAA,EAAEX,cAAc,CAAC;MAAC;QAAAkB,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAa,CAAC,eACxD1E,OAAA,CAACZ,UAAU;QAACuF,OAAO,EAAErD,WAAY;QAACsD,IAAI,EAAC,OAAO;QAAAZ,QAAA,eAC5ChE,OAAA,CAACT,SAAS;UAAAgF,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACF,CAAC,eAEd1E,OAAA,CAACpB,aAAa;MAACiF,EAAE,EAAE;QAAEgB,EAAE,EAAE,CAAC;QAAER,EAAE,EAAE;MAAE,CAAE;MAAAL,QAAA,EACjCrD,OAAO,gBACNX,OAAA,CAAChB,GAAG;QAAC6E,EAAE,EAAE;UAAEI,OAAO,EAAE,MAAM;UAAEa,aAAa,EAAE,QAAQ;UAAEX,UAAU,EAAE,QAAQ;UAAEY,EAAE,EAAE;QAAE,CAAE;QAAAf,QAAA,gBACjFhE,OAAA,CAACd,gBAAgB;UAAC0F,IAAI,EAAE;QAAG;UAAAL,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,eAC9B1E,OAAA,CAACjB,UAAU;UAACuF,OAAO,EAAC,OAAO;UAACT,EAAE,EAAE;YAAEmB,EAAE,EAAE;UAAE,CAAE;UAAAhB,QAAA,EAAC;QAE3C;UAAAO,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAY,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACV,CAAC,gBAEN1E,OAAA,CAAChB,GAAG;QAAAgF,QAAA,GACDV,aAAa,iBACZtD,OAAA,CAAAE,SAAA;UAAA8D,QAAA,gBACEhE,OAAA,CAACjB,UAAU;YAACuF,OAAO,EAAC,OAAO;YAACW,SAAS;YAAAjB,QAAA,EAAC;UAEtC;YAAAO,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAY,CAAC,eACb1E,OAAA,CAAChB,GAAG;YAAC6E,EAAE,EAAE;cAAEqB,EAAE,EAAE;YAAE,CAAE;YAAAlB,QAAA,gBACjBhE,OAAA,CAAClB,MAAM;cACLwF,OAAO,EAAC,UAAU;cAClBa,SAAS,EAAC,OAAO;cACjBC,SAAS,eAAEpF,OAAA,CAACP,UAAU;gBAAA8E,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAE;cAC1Bb,EAAE,EAAE;gBAAEqB,EAAE,EAAE;cAAE,CAAE;cAAAlB,QAAA,GACf,gBAEC,eAAAhE,OAAA;gBACEqF,IAAI,EAAC,MAAM;gBACXC,MAAM,EAAC,YAAY;gBACnBC,MAAM;gBACNC,QAAQ,EAAEjE;cAAiB;gBAAAgD,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC5B,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACI,CAAC,EACR7D,SAAS,iBACRb,OAAA,CAACjB,UAAU;cAACuF,OAAO,EAAC,OAAO;cAACT,EAAE,EAAE;gBAAEmB,EAAE,EAAE;cAAE,CAAE;cAAAhB,QAAA,GAAC,oBACvB,EAACnD,SAAS,CAAC4E,IAAI;YAAA;cAAAlB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACvB,CACb;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACE,CAAC,EAELpE,aAAa,KAAK,aAAa,iBAC9BN,OAAA,CAAChB,GAAG;YAAC6E,EAAE,EAAE;cAAEqB,EAAE,EAAE;YAAE,CAAE;YAAAlB,QAAA,gBACjBhE,OAAA,CAACjB,UAAU;cAACuF,OAAO,EAAC,OAAO;cAACT,EAAE,EAAE;gBAAEqB,EAAE,EAAE;cAAE,CAAE;cAAAlB,QAAA,EAAC;YAE3C;cAAAO,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAY,CAAC,eACb1E,OAAA,CAACf,SAAS;cACR0E,SAAS;cACTiB,IAAI,EAAC,OAAO;cACZc,KAAK,EAAE3E,SAAU;cACjByE,QAAQ,EAAGhE,CAAC,IAAKR,YAAY,CAACQ,CAAC,CAACC,MAAM,CAACiE,KAAK,CAAE;cAC9CC,WAAW,EAAC,yDAAyD;cACrErB,OAAO,EAAC,UAAU;cAClBT,EAAE,EAAE;gBAAEqB,EAAE,EAAE;cAAE;YAAE;cAAAX,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACf,CAAC,eACF1E,OAAA,CAACjB,UAAU;cAACuF,OAAO,EAAC,SAAS;cAACsB,KAAK,EAAC,gBAAgB;cAAA5B,QAAA,EAAC;YAErD;cAAAO,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAY,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACV,CACN,eAED1E,OAAA,CAACb,KAAK;YAAC0G,QAAQ,EAAC,MAAM;YAAChC,EAAE,EAAE;cAAEqB,EAAE,EAAE;YAAE,CAAE;YAAAlB,QAAA,eACnChE,OAAA,CAACjB,UAAU;cAACuF,OAAO,EAAC,OAAO;cAAAN,QAAA,GAAC,wDAE1B,EAAC1D,aAAa,KAAK,aAAa,IAAI,2EAA2E,EAC9GA,aAAa,KAAK,oBAAoB,IAAI,mFAAmF,eAC9HN,OAAA;gBAAAuE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAK,CAAC,eACN1E,OAAA;gBAAAgE,QAAA,EAAQ;cAAK;gBAAAO,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC,yJACxB;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAY;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACR,CAAC;QAAA,eACR,CACH,EAEA,CAAC,kBAAkB,EAAE,yBAAyB,CAAC,CAACnB,QAAQ,CAACjD,aAAa,CAAC,IAAI,CAACkD,gBAAgB,iBAC3FxD,OAAA,CAACjB,UAAU;UAACuF,OAAO,EAAC,OAAO;UAACW,SAAS;UAAAjB,QAAA,GAAC,cACzB,EAACP,mBAAmB,CAAC,CAAC,EAAC,0CACpC;QAAA;UAAAc,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAY,CACb,EAEA,CAAC,aAAa,EAAE,oBAAoB,CAAC,CAACnB,QAAQ,CAACjD,aAAa,CAAC,IAAI,CAACkD,gBAAgB,iBACjFxD,OAAA,CAACjB,UAAU;UAACuF,OAAO,EAAC,OAAO;UAACW,SAAS;UAAAjB,QAAA,GAAC,cACzB,EAACP,mBAAmB,CAAC,CAAC,EAAC,2CACpC;QAAA;UAAAc,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAY,CACb,EAEAlB,gBAAgB,iBACfxD,OAAA,CAAChB,GAAG;UAAC6E,EAAE,EAAE;YAAEmB,EAAE,EAAE,CAAC;YAAEc,SAAS,EAAE;UAAS,CAAE;UAAA9B,QAAA,gBACtChE,OAAA,CAACb,KAAK;YAAC0G,QAAQ,EAAC,SAAS;YAAChC,EAAE,EAAE;cAAEqB,EAAE,EAAE;YAAE,CAAE;YAAAlB,QAAA,eACtChE,OAAA,CAACjB,UAAU;cAACuF,OAAO,EAAC,OAAO;cAAAN,QAAA,EAAC;YAE5B;cAAAO,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAY;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACR,CAAC,eACR1E,OAAA,CAAClB,MAAM;YACLwF,OAAO,EAAC,WAAW;YACnBsB,KAAK,EAAC,SAAS;YACfR,SAAS,eAAEpF,OAAA,CAACL,YAAY;cAAA4E,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAE;YAC5BqB,IAAI,EAAE5E,WAAY;YAClBM,MAAM,EAAC,QAAQ;YACfuE,GAAG,EAAC,qBAAqB;YACzBnC,EAAE,EAAE;cAAEmB,EAAE,EAAE;YAAE,CAAE;YAAAhB,QAAA,EACf;UAED;YAAAO,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACN,CACN;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACE;IACN;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACY,CAAC,eAEhB1E,OAAA,CAACnB,aAAa;MAACgF,EAAE,EAAE;QAAEoC,EAAE,EAAE,CAAC;QAAElB,EAAE,EAAE,CAAC;QAAEmB,SAAS,EAAE;MAAoB,CAAE;MAAAlC,QAAA,gBAClEhE,OAAA,CAAClB,MAAM;QACL6F,OAAO,EAAErD,WAAY;QACrBsE,KAAK,EAAC,SAAS;QACftB,OAAO,EAAC,UAAU;QAClBT,EAAE,EAAE;UAAEsC,EAAE,EAAE;QAAE,CAAE;QAAAnC,QAAA,EACf;MAED;QAAAO,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAQ,CAAC,EACR,CAAClB,gBAAgB,iBAChBxD,OAAA,CAAClB,MAAM;QACL6F,OAAO,EAAEvB,gBAAiB;QAC1BwC,KAAK,EAAC,SAAS;QACftB,OAAO,EAAC,WAAW;QACnBc,SAAS,EAAE9B,aAAa,gBAAGtD,OAAA,CAACH,cAAc;UAAA0E,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,gBAAG1E,OAAA,CAACL,YAAY;UAAA4E,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAE;QACjE0B,QAAQ,EAAEzF,OAAO,IAAK2C,aAAa,IAAI,CAACzC,SAAU,IAAKP,aAAa,KAAK,aAAa,IAAI,CAACS,SAAS,CAACa,IAAI,CAAC,CAAG;QAAAoC,QAAA,EAE5GP,mBAAmB,CAAC;MAAC;QAAAc,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAChB,CACT;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACY,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACV,CAAC;AAEb,CAAC;AAAChE,EAAA,CA1YIP,UAAU;AAAAkG,EAAA,GAAVlG,UAAU;AA4YhB,eAAeA,UAAU;AAAC,IAAAkG,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}