{"ast": null, "code": "var _jsxFileName = \"C:\\\\CMS\\\\webapp\\\\frontend\\\\src\\\\components\\\\comande\\\\CreaComandaMultipla.js\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect } from 'react';\nimport { Dialog, DialogTitle, DialogContent, DialogActions, Button, TextField, Typography, Box, Grid, MenuItem, Alert, CircularProgress, List, ListItem, ListItemText, Chip, Divider } from '@mui/material';\nimport { Assignment as AssignmentIcon, Build as BuildIcon, Link as LinkIcon, Verified as VerifiedIcon, Cable as CableIcon, Security as SecurityIcon } from '@mui/icons-material';\nimport comandeService from '../../services/comandeService';\n// import comandeValidationService from '../../services/comandeValidationService';\n// import ValidationResultsDialog from './ValidationResultsDialog';\n\n/**\n * Componente per la creazione rapida di comande multiple\n * Ottimizzato per il workflow: cavi già selezionati -> dettagli comanda -> creazione\n */\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst CreaComandaMultipla = ({\n  open,\n  onClose,\n  onSuccess,\n  onError,\n  tipoComanda,\n  caviSelezionati = [],\n  cantiereId\n}) => {\n  _s();\n  const [loading, setLoading] = useState(false);\n  const [error, setError] = useState(null);\n\n  // Stati per la validazione\n  const [validationResult, setValidationResult] = useState(null);\n  const [showValidationDialog, setShowValidationDialog] = useState(false);\n  const [validationLoading, setValidationLoading] = useState(false);\n\n  // Dati del form\n  const [formData, setFormData] = useState({\n    descrizione: '',\n    responsabile: '',\n    data_scadenza: '',\n    priorita: 'NORMALE',\n    note_capo_cantiere: ''\n  });\n\n  // Funzione per ottenere l'etichetta del tipo comanda\n  const getTipoComandaLabel = tipo => {\n    switch (tipo) {\n      case 'POSA':\n        return 'Posa';\n      case 'COLLEGAMENTO_PARTENZA':\n        return 'Collegamento Partenza';\n      case 'COLLEGAMENTO_ARRIVO':\n        return 'Collegamento Arrivo';\n      case 'CERTIFICAZIONE':\n        return 'Certificazione';\n      default:\n        return tipo;\n    }\n  };\n\n  // Funzione per ottenere l'icona del tipo comanda\n  const getTipoComandaIcon = tipo => {\n    switch (tipo) {\n      case 'POSA':\n        return /*#__PURE__*/_jsxDEV(BuildIcon, {}, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 77,\n          columnNumber: 27\n        }, this);\n      case 'COLLEGAMENTO_PARTENZA':\n        return /*#__PURE__*/_jsxDEV(LinkIcon, {}, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 78,\n          columnNumber: 44\n        }, this);\n      case 'COLLEGAMENTO_ARRIVO':\n        return /*#__PURE__*/_jsxDEV(LinkIcon, {}, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 79,\n          columnNumber: 42\n        }, this);\n      case 'CERTIFICAZIONE':\n        return /*#__PURE__*/_jsxDEV(VerifiedIcon, {}, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 80,\n          columnNumber: 37\n        }, this);\n      default:\n        return /*#__PURE__*/_jsxDEV(AssignmentIcon, {}, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 81,\n          columnNumber: 23\n        }, this);\n    }\n  };\n\n  // Funzione per ottenere la descrizione del tipo comanda\n  const getTipoComandaDescription = tipo => {\n    switch (tipo) {\n      case 'POSA':\n        return 'Comanda per la posa fisica dei cavi';\n      case 'COLLEGAMENTO_PARTENZA':\n        return 'Comanda per il collegamento lato partenza';\n      case 'COLLEGAMENTO_ARRIVO':\n        return 'Comanda per il collegamento lato arrivo';\n      case 'CERTIFICAZIONE':\n        return 'Comanda per la certificazione e test dei cavi';\n      default:\n        return 'Comanda generica';\n    }\n  };\n\n  // Validazione automatica quando cambiano i parametri - temporaneamente disabilitata\n  /*\n  useEffect(() => {\n    if (open && caviSelezionati.length > 0 && tipoComanda && formData.responsabile.trim()) {\n      performValidation();\n    }\n  }, [open, caviSelezionati, tipoComanda, formData.responsabile]);\n  */\n\n  // Funzione per eseguire la validazione - temporaneamente disabilitata\n  const performValidation = async () => {\n    // Temporaneamente disabilitata\n    return;\n    /*\n    if (!formData.responsabile.trim()) return;\n     setValidationLoading(true);\n    try {\n      const result = comandeValidationService.validateCaviForComanda(\n        caviSelezionati,\n        tipoComanda,\n        formData.responsabile\n      );\n      setValidationResult(result);\n    } catch (err) {\n      console.error('Errore durante la validazione:', err);\n      setError('Errore durante la validazione dei cavi');\n    } finally {\n      setValidationLoading(false);\n    }\n    */\n  };\n\n  // Reset del form quando si chiude il dialog\n  const handleClose = () => {\n    setFormData({\n      descrizione: '',\n      responsabile: '',\n      data_scadenza: '',\n      priorita: 'NORMALE',\n      note_capo_cantiere: ''\n    });\n    setError(null);\n    setValidationResult(null);\n    setShowValidationDialog(false);\n    onClose();\n  };\n\n  // Gestione del submit con validazione\n  const handleSubmit = async () => {\n    try {\n      // Validazione base\n      if (!formData.responsabile.trim()) {\n        setError('Il responsabile è obbligatorio');\n        return;\n      }\n      if (caviSelezionati.length === 0) {\n        setError('Nessun cavo selezionato');\n        return;\n      }\n\n      // Esegui validazione se non già presente\n      if (!validationResult) {\n        await performValidation();\n        return; // La validazione triggerà un re-render\n      }\n\n      // Se ci sono errori bloccanti, mostra il dialog di validazione\n      if (!validationResult.valid) {\n        setShowValidationDialog(true);\n        return;\n      }\n\n      // Se ci sono warning, mostra il dialog per conferma\n      if (validationResult.warnings.length > 0) {\n        setShowValidationDialog(true);\n        return;\n      }\n\n      // Procedi con la creazione\n      await createComanda();\n    } catch (err) {\n      console.error('Errore durante il submit:', err);\n      setError('Errore durante la validazione o creazione della comanda');\n    }\n  };\n\n  // Funzione separata per la creazione della comanda\n  const createComanda = async () => {\n    try {\n      setLoading(true);\n      setError(null);\n\n      // Usa solo i cavi validi se la validazione è stata eseguita\n      const caviDaUsare = validationResult ? validationResult.caviValidi : caviSelezionati;\n\n      // Prepara i dati della comanda\n      const comandaData = {\n        tipo_comanda: tipoComanda,\n        descrizione: formData.descrizione || `Comanda ${getTipoComandaLabel(tipoComanda)} per ${caviDaUsare.length} cavi`,\n        responsabile: formData.responsabile,\n        data_scadenza: formData.data_scadenza || null,\n        priorita: formData.priorita,\n        note_capo_cantiere: formData.note_capo_cantiere\n      };\n\n      // Lista degli ID dei cavi validi\n      const listaIdCavi = caviDaUsare.map(c => c.id_cavo);\n      console.log('Creazione comanda multipla:', {\n        cantiereId,\n        comandaData,\n        listaIdCavi,\n        caviOriginali: caviSelezionati.length,\n        caviValidi: caviDaUsare.length\n      });\n\n      // Crea la comanda con i cavi\n      const response = await comandeService.createComandaConCavi(cantiereId, comandaData, listaIdCavi);\n      console.log('Comanda creata con successo:', response);\n      if (onSuccess) {\n        onSuccess(response);\n      }\n      handleClose();\n    } catch (err) {\n      console.error('Errore nella creazione della comanda:', err);\n      const errorMessage = (err === null || err === void 0 ? void 0 : err.detail) || (err === null || err === void 0 ? void 0 : err.message) || 'Errore nella creazione della comanda';\n      setError(errorMessage);\n      if (onError) {\n        onError(errorMessage);\n      }\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  // Gestione del dialog di validazione\n  const handleValidationDialogClose = () => {\n    setShowValidationDialog(false);\n  };\n  const handleValidationDialogProceed = async () => {\n    setShowValidationDialog(false);\n    await createComanda();\n  };\n  return /*#__PURE__*/_jsxDEV(Dialog, {\n    open: open,\n    onClose: handleClose,\n    maxWidth: \"md\",\n    fullWidth: true,\n    children: [/*#__PURE__*/_jsxDEV(DialogTitle, {\n      children: /*#__PURE__*/_jsxDEV(Box, {\n        display: \"flex\",\n        alignItems: \"center\",\n        gap: 1,\n        children: [getTipoComandaIcon(tipoComanda), \"Crea Comanda \", getTipoComandaLabel(tipoComanda)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 254,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 253,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(DialogContent, {\n      children: /*#__PURE__*/_jsxDEV(Box, {\n        sx: {\n          pt: 2\n        },\n        children: [/*#__PURE__*/_jsxDEV(Alert, {\n          severity: \"info\",\n          sx: {\n            mb: 3\n          },\n          children: [/*#__PURE__*/_jsxDEV(Typography, {\n            variant: \"subtitle2\",\n            children: getTipoComandaDescription(tipoComanda)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 264,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Typography, {\n            variant: \"body2\",\n            sx: {\n              mt: 1\n            },\n            children: [\"Verranno assegnati \", /*#__PURE__*/_jsxDEV(\"strong\", {\n              children: [caviSelezionati.length, \" cavi\"]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 268,\n              columnNumber: 34\n            }, this), \" a questa comanda\"]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 267,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 263,\n          columnNumber: 11\n        }, this), validationLoading && /*#__PURE__*/_jsxDEV(Alert, {\n          severity: \"info\",\n          sx: {\n            mb: 2\n          },\n          children: /*#__PURE__*/_jsxDEV(Box, {\n            display: \"flex\",\n            alignItems: \"center\",\n            gap: 1,\n            children: [/*#__PURE__*/_jsxDEV(CircularProgress, {\n              size: 16\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 276,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(Typography, {\n              variant: \"body2\",\n              children: \"Validazione cavi in corso...\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 277,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 275,\n            columnNumber: 15\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 274,\n          columnNumber: 13\n        }, this), validationResult && !validationLoading && /*#__PURE__*/_jsxDEV(Alert, {\n          severity: !validationResult.valid ? 'error' : validationResult.warnings.length > 0 ? 'warning' : 'success',\n          sx: {\n            mb: 2\n          },\n          action: (validationResult.errors.length > 0 || validationResult.warnings.length > 0) && /*#__PURE__*/_jsxDEV(Button, {\n            color: \"inherit\",\n            size: \"small\",\n            startIcon: /*#__PURE__*/_jsxDEV(SecurityIcon, {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 296,\n              columnNumber: 32\n            }, this),\n            onClick: () => setShowValidationDialog(true),\n            children: \"Dettagli\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 293,\n            columnNumber: 19\n          }, this),\n          children: [/*#__PURE__*/_jsxDEV(Typography, {\n            variant: \"subtitle2\",\n            children: !validationResult.valid ? 'Errori di Validazione' : validationResult.warnings.length > 0 ? 'Avvisi di Validazione' : 'Validazione Completata'\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 304,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(Typography, {\n            variant: \"body2\",\n            children: [validationResult.caviValidi.length, \" cavi validi\", validationResult.caviProblematici.length > 0 && `, ${validationResult.caviProblematici.length} con problemi`, validationResult.errors.length > 0 && ` (${validationResult.errors.length} errori)`, validationResult.warnings.length > 0 && ` (${validationResult.warnings.length} avvisi)`]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 308,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 285,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(Grid, {\n          container: true,\n          spacing: 2,\n          children: [/*#__PURE__*/_jsxDEV(Grid, {\n            item: true,\n            xs: 12,\n            sm: 6,\n            children: /*#__PURE__*/_jsxDEV(TextField, {\n              fullWidth: true,\n              label: \"Responsabile\",\n              value: formData.responsabile,\n              onChange: e => setFormData({\n                ...formData,\n                responsabile: e.target.value\n              }),\n              required: true,\n              helperText: \"Chi eseguir\\xE0 il lavoro (obbligatorio)\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 323,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 322,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Grid, {\n            item: true,\n            xs: 12,\n            sm: 6,\n            children: /*#__PURE__*/_jsxDEV(TextField, {\n              fullWidth: true,\n              select: true,\n              label: \"Priorit\\xE0\",\n              value: formData.priorita,\n              onChange: e => setFormData({\n                ...formData,\n                priorita: e.target.value\n              }),\n              children: [/*#__PURE__*/_jsxDEV(MenuItem, {\n                value: \"BASSA\",\n                children: \"Bassa\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 340,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(MenuItem, {\n                value: \"NORMALE\",\n                children: \"Normale\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 341,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(MenuItem, {\n                value: \"ALTA\",\n                children: \"Alta\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 342,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(MenuItem, {\n                value: \"URGENTE\",\n                children: \"Urgente\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 343,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 333,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 332,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 321,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(TextField, {\n          fullWidth: true,\n          label: \"Descrizione\",\n          value: formData.descrizione,\n          onChange: e => setFormData({\n            ...formData,\n            descrizione: e.target.value\n          }),\n          margin: \"normal\",\n          multiline: true,\n          rows: 2,\n          placeholder: `Comanda ${getTipoComandaLabel(tipoComanda)} per ${caviSelezionati.length} cavi`\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 348,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(TextField, {\n          fullWidth: true,\n          label: \"Note Capo Cantiere\",\n          value: formData.note_capo_cantiere,\n          onChange: e => setFormData({\n            ...formData,\n            note_capo_cantiere: e.target.value\n          }),\n          margin: \"normal\",\n          multiline: true,\n          rows: 2,\n          helperText: \"Istruzioni specifiche per il responsabile\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 359,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(TextField, {\n          fullWidth: true,\n          label: \"Data Scadenza\",\n          type: \"date\",\n          value: formData.data_scadenza,\n          onChange: e => setFormData({\n            ...formData,\n            data_scadenza: e.target.value\n          }),\n          margin: \"normal\",\n          InputLabelProps: {\n            shrink: true\n          }\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 370,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Box, {\n          sx: {\n            mt: 3\n          },\n          children: [/*#__PURE__*/_jsxDEV(Typography, {\n            variant: \"h6\",\n            gutterBottom: true,\n            children: [/*#__PURE__*/_jsxDEV(CableIcon, {\n              sx: {\n                mr: 1,\n                verticalAlign: 'middle'\n              }\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 383,\n              columnNumber: 15\n            }, this), \"Cavi Selezionati (\", caviSelezionati.length, \")\"]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 382,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Box, {\n            sx: {\n              maxHeight: 200,\n              overflow: 'auto',\n              border: '1px solid #e0e0e0',\n              borderRadius: 1\n            },\n            children: /*#__PURE__*/_jsxDEV(List, {\n              dense: true,\n              children: caviSelezionati.map((cavo, index) => /*#__PURE__*/_jsxDEV(React.Fragment, {\n                children: [/*#__PURE__*/_jsxDEV(ListItem, {\n                  children: [/*#__PURE__*/_jsxDEV(ListItemText, {\n                    primary: cavo.id_cavo,\n                    secondary: /*#__PURE__*/_jsxDEV(Box, {\n                      children: [/*#__PURE__*/_jsxDEV(Typography, {\n                        variant: \"caption\",\n                        component: \"span\",\n                        children: [cavo.tipologia, \" \\u2022 \", cavo.sezione, \" \\u2022 \", cavo.metri_teorici, \"m\"]\n                      }, void 0, true, {\n                        fileName: _jsxFileName,\n                        lineNumber: 395,\n                        columnNumber: 29\n                      }, this), /*#__PURE__*/_jsxDEV(\"br\", {}, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 398,\n                        columnNumber: 29\n                      }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                        variant: \"caption\",\n                        color: \"text.secondary\",\n                        children: [cavo.ubicazione_partenza, \" \\u2192 \", cavo.ubicazione_arrivo]\n                      }, void 0, true, {\n                        fileName: _jsxFileName,\n                        lineNumber: 399,\n                        columnNumber: 29\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 394,\n                      columnNumber: 27\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 391,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(Chip, {\n                    size: \"small\",\n                    label: cavo.stato_installazione || 'N/A',\n                    variant: \"outlined\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 405,\n                    columnNumber: 23\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 390,\n                  columnNumber: 21\n                }, this), index < caviSelezionati.length - 1 && /*#__PURE__*/_jsxDEV(Divider, {}, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 411,\n                  columnNumber: 60\n                }, this)]\n              }, cavo.id_cavo, true, {\n                fileName: _jsxFileName,\n                lineNumber: 389,\n                columnNumber: 19\n              }, this))\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 387,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 386,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 381,\n          columnNumber: 11\n        }, this), error && /*#__PURE__*/_jsxDEV(Alert, {\n          severity: \"error\",\n          sx: {\n            mt: 2\n          },\n          children: error\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 420,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 261,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 260,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(DialogActions, {\n      children: [/*#__PURE__*/_jsxDEV(Button, {\n        onClick: handleClose,\n        disabled: loading,\n        children: \"Annulla\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 428,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Button, {\n        onClick: handleSubmit,\n        variant: \"contained\",\n        disabled: loading || validationLoading || !formData.responsabile.trim() || caviSelezionati.length === 0 || validationResult && !validationResult.valid,\n        startIcon: loading ? /*#__PURE__*/_jsxDEV(CircularProgress, {\n          size: 20\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 441,\n          columnNumber: 32\n        }, this) : getTipoComandaIcon(tipoComanda),\n        children: loading ? 'Creazione...' : validationLoading ? 'Validazione...' : `Crea Comanda ${getTipoComandaLabel(tipoComanda)}`\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 431,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 427,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 252,\n    columnNumber: 5\n  }, this);\n};\n_s(CreaComandaMultipla, \"DQLt7DLdupRuWuLn9rb9+eJwbR4=\");\n_c = CreaComandaMultipla;\nexport default CreaComandaMultipla;\nvar _c;\n$RefreshReg$(_c, \"CreaComandaMultipla\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "Dialog", "DialogTitle", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "DialogActions", "<PERSON><PERSON>", "TextField", "Typography", "Box", "Grid", "MenuItem", "<PERSON><PERSON>", "CircularProgress", "List", "ListItem", "ListItemText", "Chip", "Divider", "Assignment", "AssignmentIcon", "Build", "BuildIcon", "Link", "LinkIcon", "Verified", "VerifiedIcon", "Cable", "CableIcon", "Security", "SecurityIcon", "comandeService", "jsxDEV", "_jsxDEV", "CreaComandaMultipla", "open", "onClose", "onSuccess", "onError", "tipoComanda", "caviSelezionati", "cantiereId", "_s", "loading", "setLoading", "error", "setError", "validationResult", "setValidationResult", "showValidationDialog", "setShowValidationDialog", "validationLoading", "setValidationLoading", "formData", "setFormData", "descrizione", "responsabile", "data_scadenza", "priorita", "note_capo_cantiere", "getTipoComandaLabel", "tipo", "getTipoComandaIcon", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "getTipoComandaDescription", "performValidation", "handleClose", "handleSubmit", "trim", "length", "valid", "warnings", "createComanda", "err", "console", "caviDaUsare", "cavi<PERSON><PERSON><PERSON>", "comandaData", "tipo_comanda", "listaIdCavi", "map", "c", "id_cavo", "log", "cavi<PERSON><PERSON><PERSON><PERSON>", "response", "createComandaConCavi", "errorMessage", "detail", "message", "handleValidationDialogClose", "handleValidationDialogProceed", "max<PERSON><PERSON><PERSON>", "fullWidth", "children", "display", "alignItems", "gap", "sx", "pt", "severity", "mb", "variant", "mt", "size", "action", "errors", "color", "startIcon", "onClick", "caviProblematici", "container", "spacing", "item", "xs", "sm", "label", "value", "onChange", "e", "target", "required", "helperText", "select", "margin", "multiline", "rows", "placeholder", "type", "InputLabelProps", "shrink", "gutterBottom", "mr", "verticalAlign", "maxHeight", "overflow", "border", "borderRadius", "dense", "cavo", "index", "Fragment", "primary", "secondary", "component", "tipologia", "sezione", "metri_te<PERSON>ci", "ubicazione_partenza", "ubicazione_arrivo", "stato_installazione", "disabled", "_c", "$RefreshReg$"], "sources": ["C:/CMS/webapp/frontend/src/components/comande/CreaComandaMultipla.js"], "sourcesContent": ["import React, { useState, useEffect } from 'react';\nimport {\n  Dialog,\n  DialogTitle,\n  DialogContent,\n  DialogActions,\n  Button,\n  TextField,\n  Typography,\n  Box,\n  Grid,\n  MenuItem,\n  Alert,\n  CircularProgress,\n  List,\n  ListItem,\n  ListItemText,\n  Chip,\n  Divider\n} from '@mui/material';\nimport {\n  Assignment as AssignmentIcon,\n  Build as BuildIcon,\n  Link as LinkIcon,\n  Verified as VerifiedIcon,\n  Cable as CableIcon,\n  Security as SecurityIcon\n} from '@mui/icons-material';\nimport comandeService from '../../services/comandeService';\n// import comandeValidationService from '../../services/comandeValidationService';\n// import ValidationResultsDialog from './ValidationResultsDialog';\n\n/**\n * Componente per la creazione rapida di comande multiple\n * Ottimizzato per il workflow: cavi già selezionati -> dettagli comanda -> creazione\n */\nconst CreaComandaMultipla = ({ \n  open, \n  onClose, \n  onSuccess, \n  onError,\n  tipoComanda,\n  caviSelezionati = [],\n  cantiereId \n}) => {\n  const [loading, setLoading] = useState(false);\n  const [error, setError] = useState(null);\n\n  // Stati per la validazione\n  const [validationResult, setValidationResult] = useState(null);\n  const [showValidationDialog, setShowValidationDialog] = useState(false);\n  const [validationLoading, setValidationLoading] = useState(false);\n\n  // Dati del form\n  const [formData, setFormData] = useState({\n    descrizione: '',\n    responsabile: '',\n    data_scadenza: '',\n    priorita: 'NORMALE',\n    note_capo_cantiere: ''\n  });\n\n  // Funzione per ottenere l'etichetta del tipo comanda\n  const getTipoComandaLabel = (tipo) => {\n    switch (tipo) {\n      case 'POSA': return 'Posa';\n      case 'COLLEGAMENTO_PARTENZA': return 'Collegamento Partenza';\n      case 'COLLEGAMENTO_ARRIVO': return 'Collegamento Arrivo';\n      case 'CERTIFICAZIONE': return 'Certificazione';\n      default: return tipo;\n    }\n  };\n\n  // Funzione per ottenere l'icona del tipo comanda\n  const getTipoComandaIcon = (tipo) => {\n    switch (tipo) {\n      case 'POSA': return <BuildIcon />;\n      case 'COLLEGAMENTO_PARTENZA': return <LinkIcon />;\n      case 'COLLEGAMENTO_ARRIVO': return <LinkIcon />;\n      case 'CERTIFICAZIONE': return <VerifiedIcon />;\n      default: return <AssignmentIcon />;\n    }\n  };\n\n  // Funzione per ottenere la descrizione del tipo comanda\n  const getTipoComandaDescription = (tipo) => {\n    switch (tipo) {\n      case 'POSA': return 'Comanda per la posa fisica dei cavi';\n      case 'COLLEGAMENTO_PARTENZA': return 'Comanda per il collegamento lato partenza';\n      case 'COLLEGAMENTO_ARRIVO': return 'Comanda per il collegamento lato arrivo';\n      case 'CERTIFICAZIONE': return 'Comanda per la certificazione e test dei cavi';\n      default: return 'Comanda generica';\n    }\n  };\n\n  // Validazione automatica quando cambiano i parametri - temporaneamente disabilitata\n  /*\n  useEffect(() => {\n    if (open && caviSelezionati.length > 0 && tipoComanda && formData.responsabile.trim()) {\n      performValidation();\n    }\n  }, [open, caviSelezionati, tipoComanda, formData.responsabile]);\n  */\n\n  // Funzione per eseguire la validazione - temporaneamente disabilitata\n  const performValidation = async () => {\n    // Temporaneamente disabilitata\n    return;\n    /*\n    if (!formData.responsabile.trim()) return;\n\n    setValidationLoading(true);\n    try {\n      const result = comandeValidationService.validateCaviForComanda(\n        caviSelezionati,\n        tipoComanda,\n        formData.responsabile\n      );\n      setValidationResult(result);\n    } catch (err) {\n      console.error('Errore durante la validazione:', err);\n      setError('Errore durante la validazione dei cavi');\n    } finally {\n      setValidationLoading(false);\n    }\n    */\n  };\n\n  // Reset del form quando si chiude il dialog\n  const handleClose = () => {\n    setFormData({\n      descrizione: '',\n      responsabile: '',\n      data_scadenza: '',\n      priorita: 'NORMALE',\n      note_capo_cantiere: ''\n    });\n    setError(null);\n    setValidationResult(null);\n    setShowValidationDialog(false);\n    onClose();\n  };\n\n  // Gestione del submit con validazione\n  const handleSubmit = async () => {\n    try {\n      // Validazione base\n      if (!formData.responsabile.trim()) {\n        setError('Il responsabile è obbligatorio');\n        return;\n      }\n\n      if (caviSelezionati.length === 0) {\n        setError('Nessun cavo selezionato');\n        return;\n      }\n\n      // Esegui validazione se non già presente\n      if (!validationResult) {\n        await performValidation();\n        return; // La validazione triggerà un re-render\n      }\n\n      // Se ci sono errori bloccanti, mostra il dialog di validazione\n      if (!validationResult.valid) {\n        setShowValidationDialog(true);\n        return;\n      }\n\n      // Se ci sono warning, mostra il dialog per conferma\n      if (validationResult.warnings.length > 0) {\n        setShowValidationDialog(true);\n        return;\n      }\n\n      // Procedi con la creazione\n      await createComanda();\n    } catch (err) {\n      console.error('Errore durante il submit:', err);\n      setError('Errore durante la validazione o creazione della comanda');\n    }\n  };\n\n  // Funzione separata per la creazione della comanda\n  const createComanda = async () => {\n    try {\n      setLoading(true);\n      setError(null);\n\n      // Usa solo i cavi validi se la validazione è stata eseguita\n      const caviDaUsare = validationResult ? validationResult.caviValidi : caviSelezionati;\n\n      // Prepara i dati della comanda\n      const comandaData = {\n        tipo_comanda: tipoComanda,\n        descrizione: formData.descrizione || `Comanda ${getTipoComandaLabel(tipoComanda)} per ${caviDaUsare.length} cavi`,\n        responsabile: formData.responsabile,\n        data_scadenza: formData.data_scadenza || null,\n        priorita: formData.priorita,\n        note_capo_cantiere: formData.note_capo_cantiere\n      };\n\n      // Lista degli ID dei cavi validi\n      const listaIdCavi = caviDaUsare.map(c => c.id_cavo);\n\n      console.log('Creazione comanda multipla:', {\n        cantiereId,\n        comandaData,\n        listaIdCavi,\n        caviOriginali: caviSelezionati.length,\n        caviValidi: caviDaUsare.length\n      });\n\n      // Crea la comanda con i cavi\n      const response = await comandeService.createComandaConCavi(\n        cantiereId,\n        comandaData,\n        listaIdCavi\n      );\n\n      console.log('Comanda creata con successo:', response);\n\n      if (onSuccess) {\n        onSuccess(response);\n      }\n\n      handleClose();\n    } catch (err) {\n      console.error('Errore nella creazione della comanda:', err);\n      const errorMessage = err?.detail || err?.message || 'Errore nella creazione della comanda';\n      setError(errorMessage);\n\n      if (onError) {\n        onError(errorMessage);\n      }\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  // Gestione del dialog di validazione\n  const handleValidationDialogClose = () => {\n    setShowValidationDialog(false);\n  };\n\n  const handleValidationDialogProceed = async () => {\n    setShowValidationDialog(false);\n    await createComanda();\n  };\n\n  return (\n    <Dialog open={open} onClose={handleClose} maxWidth=\"md\" fullWidth>\n      <DialogTitle>\n        <Box display=\"flex\" alignItems=\"center\" gap={1}>\n          {getTipoComandaIcon(tipoComanda)}\n          Crea Comanda {getTipoComandaLabel(tipoComanda)}\n        </Box>\n      </DialogTitle>\n      \n      <DialogContent>\n        <Box sx={{ pt: 2 }}>\n          {/* Informazioni tipo comanda */}\n          <Alert severity=\"info\" sx={{ mb: 3 }}>\n            <Typography variant=\"subtitle2\">\n              {getTipoComandaDescription(tipoComanda)}\n            </Typography>\n            <Typography variant=\"body2\" sx={{ mt: 1 }}>\n              Verranno assegnati <strong>{caviSelezionati.length} cavi</strong> a questa comanda\n            </Typography>\n          </Alert>\n\n          {/* Indicatori di validazione */}\n          {validationLoading && (\n            <Alert severity=\"info\" sx={{ mb: 2 }}>\n              <Box display=\"flex\" alignItems=\"center\" gap={1}>\n                <CircularProgress size={16} />\n                <Typography variant=\"body2\">\n                  Validazione cavi in corso...\n                </Typography>\n              </Box>\n            </Alert>\n          )}\n\n          {validationResult && !validationLoading && (\n            <Alert\n              severity={\n                !validationResult.valid ? 'error' :\n                validationResult.warnings.length > 0 ? 'warning' : 'success'\n              }\n              sx={{ mb: 2 }}\n              action={\n                (validationResult.errors.length > 0 || validationResult.warnings.length > 0) && (\n                  <Button\n                    color=\"inherit\"\n                    size=\"small\"\n                    startIcon={<SecurityIcon />}\n                    onClick={() => setShowValidationDialog(true)}\n                  >\n                    Dettagli\n                  </Button>\n                )\n              }\n            >\n              <Typography variant=\"subtitle2\">\n                {!validationResult.valid ? 'Errori di Validazione' :\n                 validationResult.warnings.length > 0 ? 'Avvisi di Validazione' : 'Validazione Completata'}\n              </Typography>\n              <Typography variant=\"body2\">\n                {validationResult.caviValidi.length} cavi validi\n                {validationResult.caviProblematici.length > 0 &&\n                  `, ${validationResult.caviProblematici.length} con problemi`}\n                {validationResult.errors.length > 0 &&\n                  ` (${validationResult.errors.length} errori)`}\n                {validationResult.warnings.length > 0 &&\n                  ` (${validationResult.warnings.length} avvisi)`}\n              </Typography>\n            </Alert>\n          )}\n\n          {/* Form dati comanda */}\n          <Grid container spacing={2}>\n            <Grid item xs={12} sm={6}>\n              <TextField\n                fullWidth\n                label=\"Responsabile\"\n                value={formData.responsabile}\n                onChange={(e) => setFormData({ ...formData, responsabile: e.target.value })}\n                required\n                helperText=\"Chi eseguirà il lavoro (obbligatorio)\"\n              />\n            </Grid>\n            <Grid item xs={12} sm={6}>\n              <TextField\n                fullWidth\n                select\n                label=\"Priorità\"\n                value={formData.priorita}\n                onChange={(e) => setFormData({ ...formData, priorita: e.target.value })}\n              >\n                <MenuItem value=\"BASSA\">Bassa</MenuItem>\n                <MenuItem value=\"NORMALE\">Normale</MenuItem>\n                <MenuItem value=\"ALTA\">Alta</MenuItem>\n                <MenuItem value=\"URGENTE\">Urgente</MenuItem>\n              </TextField>\n            </Grid>\n          </Grid>\n\n          <TextField\n            fullWidth\n            label=\"Descrizione\"\n            value={formData.descrizione}\n            onChange={(e) => setFormData({ ...formData, descrizione: e.target.value })}\n            margin=\"normal\"\n            multiline\n            rows={2}\n            placeholder={`Comanda ${getTipoComandaLabel(tipoComanda)} per ${caviSelezionati.length} cavi`}\n          />\n\n          <TextField\n            fullWidth\n            label=\"Note Capo Cantiere\"\n            value={formData.note_capo_cantiere}\n            onChange={(e) => setFormData({ ...formData, note_capo_cantiere: e.target.value })}\n            margin=\"normal\"\n            multiline\n            rows={2}\n            helperText=\"Istruzioni specifiche per il responsabile\"\n          />\n\n          <TextField\n            fullWidth\n            label=\"Data Scadenza\"\n            type=\"date\"\n            value={formData.data_scadenza}\n            onChange={(e) => setFormData({ ...formData, data_scadenza: e.target.value })}\n            margin=\"normal\"\n            InputLabelProps={{ shrink: true }}\n          />\n\n          {/* Lista cavi selezionati */}\n          <Box sx={{ mt: 3 }}>\n            <Typography variant=\"h6\" gutterBottom>\n              <CableIcon sx={{ mr: 1, verticalAlign: 'middle' }} />\n              Cavi Selezionati ({caviSelezionati.length})\n            </Typography>\n            <Box sx={{ maxHeight: 200, overflow: 'auto', border: '1px solid #e0e0e0', borderRadius: 1 }}>\n              <List dense>\n                {caviSelezionati.map((cavo, index) => (\n                  <React.Fragment key={cavo.id_cavo}>\n                    <ListItem>\n                      <ListItemText\n                        primary={cavo.id_cavo}\n                        secondary={\n                          <Box>\n                            <Typography variant=\"caption\" component=\"span\">\n                              {cavo.tipologia} • {cavo.sezione} • {cavo.metri_teorici}m\n                            </Typography>\n                            <br />\n                            <Typography variant=\"caption\" color=\"text.secondary\">\n                              {cavo.ubicazione_partenza} → {cavo.ubicazione_arrivo}\n                            </Typography>\n                          </Box>\n                        }\n                      />\n                      <Chip \n                        size=\"small\" \n                        label={cavo.stato_installazione || 'N/A'} \n                        variant=\"outlined\"\n                      />\n                    </ListItem>\n                    {index < caviSelezionati.length - 1 && <Divider />}\n                  </React.Fragment>\n                ))}\n              </List>\n            </Box>\n          </Box>\n\n          {/* Errore */}\n          {error && (\n            <Alert severity=\"error\" sx={{ mt: 2 }}>\n              {error}\n            </Alert>\n          )}\n        </Box>\n      </DialogContent>\n\n      <DialogActions>\n        <Button onClick={handleClose} disabled={loading}>\n          Annulla\n        </Button>\n        <Button\n          onClick={handleSubmit}\n          variant=\"contained\"\n          disabled={\n            loading ||\n            validationLoading ||\n            !formData.responsabile.trim() ||\n            caviSelezionati.length === 0 ||\n            (validationResult && !validationResult.valid)\n          }\n          startIcon={loading ? <CircularProgress size={20} /> : getTipoComandaIcon(tipoComanda)}\n        >\n          {loading ? 'Creazione...' :\n           validationLoading ? 'Validazione...' :\n           `Crea Comanda ${getTipoComandaLabel(tipoComanda)}`}\n        </Button>\n      </DialogActions>\n\n      {/* Dialog di validazione - temporaneamente disabilitato */}\n      {/*\n      <ValidationResultsDialog\n        open={showValidationDialog}\n        onClose={handleValidationDialogClose}\n        onProceed={handleValidationDialogProceed}\n        validationResult={validationResult}\n        tipoComanda={tipoComanda}\n        responsabile={formData.responsabile}\n      />\n      */}\n    </Dialog>\n  );\n};\n\nexport default CreaComandaMultipla;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAClD,SACEC,MAAM,EACNC,WAAW,EACXC,aAAa,EACbC,aAAa,EACbC,MAAM,EACNC,SAAS,EACTC,UAAU,EACVC,GAAG,EACHC,IAAI,EACJC,QAAQ,EACRC,KAAK,EACLC,gBAAgB,EAChBC,IAAI,EACJC,QAAQ,EACRC,YAAY,EACZC,IAAI,EACJC,OAAO,QACF,eAAe;AACtB,SACEC,UAAU,IAAIC,cAAc,EAC5BC,KAAK,IAAIC,SAAS,EAClBC,IAAI,IAAIC,QAAQ,EAChBC,QAAQ,IAAIC,YAAY,EACxBC,KAAK,IAAIC,SAAS,EAClBC,QAAQ,IAAIC,YAAY,QACnB,qBAAqB;AAC5B,OAAOC,cAAc,MAAM,+BAA+B;AAC1D;AACA;;AAEA;AACA;AACA;AACA;AAHA,SAAAC,MAAA,IAAAC,OAAA;AAIA,MAAMC,mBAAmB,GAAGA,CAAC;EAC3BC,IAAI;EACJC,OAAO;EACPC,SAAS;EACTC,OAAO;EACPC,WAAW;EACXC,eAAe,GAAG,EAAE;EACpBC;AACF,CAAC,KAAK;EAAAC,EAAA;EACJ,MAAM,CAACC,OAAO,EAAEC,UAAU,CAAC,GAAG5C,QAAQ,CAAC,KAAK,CAAC;EAC7C,MAAM,CAAC6C,KAAK,EAAEC,QAAQ,CAAC,GAAG9C,QAAQ,CAAC,IAAI,CAAC;;EAExC;EACA,MAAM,CAAC+C,gBAAgB,EAAEC,mBAAmB,CAAC,GAAGhD,QAAQ,CAAC,IAAI,CAAC;EAC9D,MAAM,CAACiD,oBAAoB,EAAEC,uBAAuB,CAAC,GAAGlD,QAAQ,CAAC,KAAK,CAAC;EACvE,MAAM,CAACmD,iBAAiB,EAAEC,oBAAoB,CAAC,GAAGpD,QAAQ,CAAC,KAAK,CAAC;;EAEjE;EACA,MAAM,CAACqD,QAAQ,EAAEC,WAAW,CAAC,GAAGtD,QAAQ,CAAC;IACvCuD,WAAW,EAAE,EAAE;IACfC,YAAY,EAAE,EAAE;IAChBC,aAAa,EAAE,EAAE;IACjBC,QAAQ,EAAE,SAAS;IACnBC,kBAAkB,EAAE;EACtB,CAAC,CAAC;;EAEF;EACA,MAAMC,mBAAmB,GAAIC,IAAI,IAAK;IACpC,QAAQA,IAAI;MACV,KAAK,MAAM;QAAE,OAAO,MAAM;MAC1B,KAAK,uBAAuB;QAAE,OAAO,uBAAuB;MAC5D,KAAK,qBAAqB;QAAE,OAAO,qBAAqB;MACxD,KAAK,gBAAgB;QAAE,OAAO,gBAAgB;MAC9C;QAAS,OAAOA,IAAI;IACtB;EACF,CAAC;;EAED;EACA,MAAMC,kBAAkB,GAAID,IAAI,IAAK;IACnC,QAAQA,IAAI;MACV,KAAK,MAAM;QAAE,oBAAO5B,OAAA,CAACX,SAAS;UAAAyC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC;MACjC,KAAK,uBAAuB;QAAE,oBAAOjC,OAAA,CAACT,QAAQ;UAAAuC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC;MACjD,KAAK,qBAAqB;QAAE,oBAAOjC,OAAA,CAACT,QAAQ;UAAAuC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC;MAC/C,KAAK,gBAAgB;QAAE,oBAAOjC,OAAA,CAACP,YAAY;UAAAqC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC;MAC9C;QAAS,oBAAOjC,OAAA,CAACb,cAAc;UAAA2C,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC;IACpC;EACF,CAAC;;EAED;EACA,MAAMC,yBAAyB,GAAIN,IAAI,IAAK;IAC1C,QAAQA,IAAI;MACV,KAAK,MAAM;QAAE,OAAO,qCAAqC;MACzD,KAAK,uBAAuB;QAAE,OAAO,2CAA2C;MAChF,KAAK,qBAAqB;QAAE,OAAO,yCAAyC;MAC5E,KAAK,gBAAgB;QAAE,OAAO,+CAA+C;MAC7E;QAAS,OAAO,kBAAkB;IACpC;EACF,CAAC;;EAED;EACA;AACF;AACA;AACA;AACA;AACA;AACA;;EAEE;EACA,MAAMO,iBAAiB,GAAG,MAAAA,CAAA,KAAY;IACpC;IACA;IACA;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;EAEE,CAAC;;EAED;EACA,MAAMC,WAAW,GAAGA,CAAA,KAAM;IACxBf,WAAW,CAAC;MACVC,WAAW,EAAE,EAAE;MACfC,YAAY,EAAE,EAAE;MAChBC,aAAa,EAAE,EAAE;MACjBC,QAAQ,EAAE,SAAS;MACnBC,kBAAkB,EAAE;IACtB,CAAC,CAAC;IACFb,QAAQ,CAAC,IAAI,CAAC;IACdE,mBAAmB,CAAC,IAAI,CAAC;IACzBE,uBAAuB,CAAC,KAAK,CAAC;IAC9Bd,OAAO,CAAC,CAAC;EACX,CAAC;;EAED;EACA,MAAMkC,YAAY,GAAG,MAAAA,CAAA,KAAY;IAC/B,IAAI;MACF;MACA,IAAI,CAACjB,QAAQ,CAACG,YAAY,CAACe,IAAI,CAAC,CAAC,EAAE;QACjCzB,QAAQ,CAAC,gCAAgC,CAAC;QAC1C;MACF;MAEA,IAAIN,eAAe,CAACgC,MAAM,KAAK,CAAC,EAAE;QAChC1B,QAAQ,CAAC,yBAAyB,CAAC;QACnC;MACF;;MAEA;MACA,IAAI,CAACC,gBAAgB,EAAE;QACrB,MAAMqB,iBAAiB,CAAC,CAAC;QACzB,OAAO,CAAC;MACV;;MAEA;MACA,IAAI,CAACrB,gBAAgB,CAAC0B,KAAK,EAAE;QAC3BvB,uBAAuB,CAAC,IAAI,CAAC;QAC7B;MACF;;MAEA;MACA,IAAIH,gBAAgB,CAAC2B,QAAQ,CAACF,MAAM,GAAG,CAAC,EAAE;QACxCtB,uBAAuB,CAAC,IAAI,CAAC;QAC7B;MACF;;MAEA;MACA,MAAMyB,aAAa,CAAC,CAAC;IACvB,CAAC,CAAC,OAAOC,GAAG,EAAE;MACZC,OAAO,CAAChC,KAAK,CAAC,2BAA2B,EAAE+B,GAAG,CAAC;MAC/C9B,QAAQ,CAAC,yDAAyD,CAAC;IACrE;EACF,CAAC;;EAED;EACA,MAAM6B,aAAa,GAAG,MAAAA,CAAA,KAAY;IAChC,IAAI;MACF/B,UAAU,CAAC,IAAI,CAAC;MAChBE,QAAQ,CAAC,IAAI,CAAC;;MAEd;MACA,MAAMgC,WAAW,GAAG/B,gBAAgB,GAAGA,gBAAgB,CAACgC,UAAU,GAAGvC,eAAe;;MAEpF;MACA,MAAMwC,WAAW,GAAG;QAClBC,YAAY,EAAE1C,WAAW;QACzBgB,WAAW,EAAEF,QAAQ,CAACE,WAAW,IAAI,WAAWK,mBAAmB,CAACrB,WAAW,CAAC,QAAQuC,WAAW,CAACN,MAAM,OAAO;QACjHhB,YAAY,EAAEH,QAAQ,CAACG,YAAY;QACnCC,aAAa,EAAEJ,QAAQ,CAACI,aAAa,IAAI,IAAI;QAC7CC,QAAQ,EAAEL,QAAQ,CAACK,QAAQ;QAC3BC,kBAAkB,EAAEN,QAAQ,CAACM;MAC/B,CAAC;;MAED;MACA,MAAMuB,WAAW,GAAGJ,WAAW,CAACK,GAAG,CAACC,CAAC,IAAIA,CAAC,CAACC,OAAO,CAAC;MAEnDR,OAAO,CAACS,GAAG,CAAC,6BAA6B,EAAE;QACzC7C,UAAU;QACVuC,WAAW;QACXE,WAAW;QACXK,aAAa,EAAE/C,eAAe,CAACgC,MAAM;QACrCO,UAAU,EAAED,WAAW,CAACN;MAC1B,CAAC,CAAC;;MAEF;MACA,MAAMgB,QAAQ,GAAG,MAAMzD,cAAc,CAAC0D,oBAAoB,CACxDhD,UAAU,EACVuC,WAAW,EACXE,WACF,CAAC;MAEDL,OAAO,CAACS,GAAG,CAAC,8BAA8B,EAAEE,QAAQ,CAAC;MAErD,IAAInD,SAAS,EAAE;QACbA,SAAS,CAACmD,QAAQ,CAAC;MACrB;MAEAnB,WAAW,CAAC,CAAC;IACf,CAAC,CAAC,OAAOO,GAAG,EAAE;MACZC,OAAO,CAAChC,KAAK,CAAC,uCAAuC,EAAE+B,GAAG,CAAC;MAC3D,MAAMc,YAAY,GAAG,CAAAd,GAAG,aAAHA,GAAG,uBAAHA,GAAG,CAAEe,MAAM,MAAIf,GAAG,aAAHA,GAAG,uBAAHA,GAAG,CAAEgB,OAAO,KAAI,sCAAsC;MAC1F9C,QAAQ,CAAC4C,YAAY,CAAC;MAEtB,IAAIpD,OAAO,EAAE;QACXA,OAAO,CAACoD,YAAY,CAAC;MACvB;IACF,CAAC,SAAS;MACR9C,UAAU,CAAC,KAAK,CAAC;IACnB;EACF,CAAC;;EAED;EACA,MAAMiD,2BAA2B,GAAGA,CAAA,KAAM;IACxC3C,uBAAuB,CAAC,KAAK,CAAC;EAChC,CAAC;EAED,MAAM4C,6BAA6B,GAAG,MAAAA,CAAA,KAAY;IAChD5C,uBAAuB,CAAC,KAAK,CAAC;IAC9B,MAAMyB,aAAa,CAAC,CAAC;EACvB,CAAC;EAED,oBACE1C,OAAA,CAAC/B,MAAM;IAACiC,IAAI,EAAEA,IAAK;IAACC,OAAO,EAAEiC,WAAY;IAAC0B,QAAQ,EAAC,IAAI;IAACC,SAAS;IAAAC,QAAA,gBAC/DhE,OAAA,CAAC9B,WAAW;MAAA8F,QAAA,eACVhE,OAAA,CAACxB,GAAG;QAACyF,OAAO,EAAC,MAAM;QAACC,UAAU,EAAC,QAAQ;QAACC,GAAG,EAAE,CAAE;QAAAH,QAAA,GAC5CnC,kBAAkB,CAACvB,WAAW,CAAC,EAAC,eACpB,EAACqB,mBAAmB,CAACrB,WAAW,CAAC;MAAA;QAAAwB,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC3C;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACK,CAAC,eAEdjC,OAAA,CAAC7B,aAAa;MAAA6F,QAAA,eACZhE,OAAA,CAACxB,GAAG;QAAC4F,EAAE,EAAE;UAAEC,EAAE,EAAE;QAAE,CAAE;QAAAL,QAAA,gBAEjBhE,OAAA,CAACrB,KAAK;UAAC2F,QAAQ,EAAC,MAAM;UAACF,EAAE,EAAE;YAAEG,EAAE,EAAE;UAAE,CAAE;UAAAP,QAAA,gBACnChE,OAAA,CAACzB,UAAU;YAACiG,OAAO,EAAC,WAAW;YAAAR,QAAA,EAC5B9B,yBAAyB,CAAC5B,WAAW;UAAC;YAAAwB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC7B,CAAC,eACbjC,OAAA,CAACzB,UAAU;YAACiG,OAAO,EAAC,OAAO;YAACJ,EAAE,EAAE;cAAEK,EAAE,EAAE;YAAE,CAAE;YAAAT,QAAA,GAAC,qBACtB,eAAAhE,OAAA;cAAAgE,QAAA,GAASzD,eAAe,CAACgC,MAAM,EAAC,OAAK;YAAA;cAAAT,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC,qBACnE;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAY,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACR,CAAC,EAGPf,iBAAiB,iBAChBlB,OAAA,CAACrB,KAAK;UAAC2F,QAAQ,EAAC,MAAM;UAACF,EAAE,EAAE;YAAEG,EAAE,EAAE;UAAE,CAAE;UAAAP,QAAA,eACnChE,OAAA,CAACxB,GAAG;YAACyF,OAAO,EAAC,MAAM;YAACC,UAAU,EAAC,QAAQ;YAACC,GAAG,EAAE,CAAE;YAAAH,QAAA,gBAC7ChE,OAAA,CAACpB,gBAAgB;cAAC8F,IAAI,EAAE;YAAG;cAAA5C,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,eAC9BjC,OAAA,CAACzB,UAAU;cAACiG,OAAO,EAAC,OAAO;cAAAR,QAAA,EAAC;YAE5B;cAAAlC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAY,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACV;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACD,CACR,EAEAnB,gBAAgB,IAAI,CAACI,iBAAiB,iBACrClB,OAAA,CAACrB,KAAK;UACJ2F,QAAQ,EACN,CAACxD,gBAAgB,CAAC0B,KAAK,GAAG,OAAO,GACjC1B,gBAAgB,CAAC2B,QAAQ,CAACF,MAAM,GAAG,CAAC,GAAG,SAAS,GAAG,SACpD;UACD6B,EAAE,EAAE;YAAEG,EAAE,EAAE;UAAE,CAAE;UACdI,MAAM,EACJ,CAAC7D,gBAAgB,CAAC8D,MAAM,CAACrC,MAAM,GAAG,CAAC,IAAIzB,gBAAgB,CAAC2B,QAAQ,CAACF,MAAM,GAAG,CAAC,kBACzEvC,OAAA,CAAC3B,MAAM;YACLwG,KAAK,EAAC,SAAS;YACfH,IAAI,EAAC,OAAO;YACZI,SAAS,eAAE9E,OAAA,CAACH,YAAY;cAAAiC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAE;YAC5B8C,OAAO,EAAEA,CAAA,KAAM9D,uBAAuB,CAAC,IAAI,CAAE;YAAA+C,QAAA,EAC9C;UAED;YAAAlC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAEX;UAAA+B,QAAA,gBAEDhE,OAAA,CAACzB,UAAU;YAACiG,OAAO,EAAC,WAAW;YAAAR,QAAA,EAC5B,CAAClD,gBAAgB,CAAC0B,KAAK,GAAG,uBAAuB,GACjD1B,gBAAgB,CAAC2B,QAAQ,CAACF,MAAM,GAAG,CAAC,GAAG,uBAAuB,GAAG;UAAwB;YAAAT,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAChF,CAAC,eACbjC,OAAA,CAACzB,UAAU;YAACiG,OAAO,EAAC,OAAO;YAAAR,QAAA,GACxBlD,gBAAgB,CAACgC,UAAU,CAACP,MAAM,EAAC,cACpC,EAACzB,gBAAgB,CAACkE,gBAAgB,CAACzC,MAAM,GAAG,CAAC,IAC3C,KAAKzB,gBAAgB,CAACkE,gBAAgB,CAACzC,MAAM,eAAe,EAC7DzB,gBAAgB,CAAC8D,MAAM,CAACrC,MAAM,GAAG,CAAC,IACjC,KAAKzB,gBAAgB,CAAC8D,MAAM,CAACrC,MAAM,UAAU,EAC9CzB,gBAAgB,CAAC2B,QAAQ,CAACF,MAAM,GAAG,CAAC,IACnC,KAAKzB,gBAAgB,CAAC2B,QAAQ,CAACF,MAAM,UAAU;UAAA;YAAAT,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACvC,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACR,CACR,eAGDjC,OAAA,CAACvB,IAAI;UAACwG,SAAS;UAACC,OAAO,EAAE,CAAE;UAAAlB,QAAA,gBACzBhE,OAAA,CAACvB,IAAI;YAAC0G,IAAI;YAACC,EAAE,EAAE,EAAG;YAACC,EAAE,EAAE,CAAE;YAAArB,QAAA,eACvBhE,OAAA,CAAC1B,SAAS;cACRyF,SAAS;cACTuB,KAAK,EAAC,cAAc;cACpBC,KAAK,EAAEnE,QAAQ,CAACG,YAAa;cAC7BiE,QAAQ,EAAGC,CAAC,IAAKpE,WAAW,CAAC;gBAAE,GAAGD,QAAQ;gBAAEG,YAAY,EAAEkE,CAAC,CAACC,MAAM,CAACH;cAAM,CAAC,CAAE;cAC5EI,QAAQ;cACRC,UAAU,EAAC;YAAuC;cAAA9D,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACnD;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACE,CAAC,eACPjC,OAAA,CAACvB,IAAI;YAAC0G,IAAI;YAACC,EAAE,EAAE,EAAG;YAACC,EAAE,EAAE,CAAE;YAAArB,QAAA,eACvBhE,OAAA,CAAC1B,SAAS;cACRyF,SAAS;cACT8B,MAAM;cACNP,KAAK,EAAC,aAAU;cAChBC,KAAK,EAAEnE,QAAQ,CAACK,QAAS;cACzB+D,QAAQ,EAAGC,CAAC,IAAKpE,WAAW,CAAC;gBAAE,GAAGD,QAAQ;gBAAEK,QAAQ,EAAEgE,CAAC,CAACC,MAAM,CAACH;cAAM,CAAC,CAAE;cAAAvB,QAAA,gBAExEhE,OAAA,CAACtB,QAAQ;gBAAC6G,KAAK,EAAC,OAAO;gBAAAvB,QAAA,EAAC;cAAK;gBAAAlC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAU,CAAC,eACxCjC,OAAA,CAACtB,QAAQ;gBAAC6G,KAAK,EAAC,SAAS;gBAAAvB,QAAA,EAAC;cAAO;gBAAAlC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAU,CAAC,eAC5CjC,OAAA,CAACtB,QAAQ;gBAAC6G,KAAK,EAAC,MAAM;gBAAAvB,QAAA,EAAC;cAAI;gBAAAlC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAU,CAAC,eACtCjC,OAAA,CAACtB,QAAQ;gBAAC6G,KAAK,EAAC,SAAS;gBAAAvB,QAAA,EAAC;cAAO;gBAAAlC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAU,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACnC;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACR,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,eAEPjC,OAAA,CAAC1B,SAAS;UACRyF,SAAS;UACTuB,KAAK,EAAC,aAAa;UACnBC,KAAK,EAAEnE,QAAQ,CAACE,WAAY;UAC5BkE,QAAQ,EAAGC,CAAC,IAAKpE,WAAW,CAAC;YAAE,GAAGD,QAAQ;YAAEE,WAAW,EAAEmE,CAAC,CAACC,MAAM,CAACH;UAAM,CAAC,CAAE;UAC3EO,MAAM,EAAC,QAAQ;UACfC,SAAS;UACTC,IAAI,EAAE,CAAE;UACRC,WAAW,EAAE,WAAWtE,mBAAmB,CAACrB,WAAW,CAAC,QAAQC,eAAe,CAACgC,MAAM;QAAQ;UAAAT,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC/F,CAAC,eAEFjC,OAAA,CAAC1B,SAAS;UACRyF,SAAS;UACTuB,KAAK,EAAC,oBAAoB;UAC1BC,KAAK,EAAEnE,QAAQ,CAACM,kBAAmB;UACnC8D,QAAQ,EAAGC,CAAC,IAAKpE,WAAW,CAAC;YAAE,GAAGD,QAAQ;YAAEM,kBAAkB,EAAE+D,CAAC,CAACC,MAAM,CAACH;UAAM,CAAC,CAAE;UAClFO,MAAM,EAAC,QAAQ;UACfC,SAAS;UACTC,IAAI,EAAE,CAAE;UACRJ,UAAU,EAAC;QAA2C;UAAA9D,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACvD,CAAC,eAEFjC,OAAA,CAAC1B,SAAS;UACRyF,SAAS;UACTuB,KAAK,EAAC,eAAe;UACrBY,IAAI,EAAC,MAAM;UACXX,KAAK,EAAEnE,QAAQ,CAACI,aAAc;UAC9BgE,QAAQ,EAAGC,CAAC,IAAKpE,WAAW,CAAC;YAAE,GAAGD,QAAQ;YAAEI,aAAa,EAAEiE,CAAC,CAACC,MAAM,CAACH;UAAM,CAAC,CAAE;UAC7EO,MAAM,EAAC,QAAQ;UACfK,eAAe,EAAE;YAAEC,MAAM,EAAE;UAAK;QAAE;UAAAtE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACnC,CAAC,eAGFjC,OAAA,CAACxB,GAAG;UAAC4F,EAAE,EAAE;YAAEK,EAAE,EAAE;UAAE,CAAE;UAAAT,QAAA,gBACjBhE,OAAA,CAACzB,UAAU;YAACiG,OAAO,EAAC,IAAI;YAAC6B,YAAY;YAAArC,QAAA,gBACnChE,OAAA,CAACL,SAAS;cAACyE,EAAE,EAAE;gBAAEkC,EAAE,EAAE,CAAC;gBAAEC,aAAa,EAAE;cAAS;YAAE;cAAAzE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,sBACnC,EAAC1B,eAAe,CAACgC,MAAM,EAAC,GAC5C;UAAA;YAAAT,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAY,CAAC,eACbjC,OAAA,CAACxB,GAAG;YAAC4F,EAAE,EAAE;cAAEoC,SAAS,EAAE,GAAG;cAAEC,QAAQ,EAAE,MAAM;cAAEC,MAAM,EAAE,mBAAmB;cAAEC,YAAY,EAAE;YAAE,CAAE;YAAA3C,QAAA,eAC1FhE,OAAA,CAACnB,IAAI;cAAC+H,KAAK;cAAA5C,QAAA,EACRzD,eAAe,CAAC2C,GAAG,CAAC,CAAC2D,IAAI,EAAEC,KAAK,kBAC/B9G,OAAA,CAAClC,KAAK,CAACiJ,QAAQ;gBAAA/C,QAAA,gBACbhE,OAAA,CAAClB,QAAQ;kBAAAkF,QAAA,gBACPhE,OAAA,CAACjB,YAAY;oBACXiI,OAAO,EAAEH,IAAI,CAACzD,OAAQ;oBACtB6D,SAAS,eACPjH,OAAA,CAACxB,GAAG;sBAAAwF,QAAA,gBACFhE,OAAA,CAACzB,UAAU;wBAACiG,OAAO,EAAC,SAAS;wBAAC0C,SAAS,EAAC,MAAM;wBAAAlD,QAAA,GAC3C6C,IAAI,CAACM,SAAS,EAAC,UAAG,EAACN,IAAI,CAACO,OAAO,EAAC,UAAG,EAACP,IAAI,CAACQ,aAAa,EAAC,GAC1D;sBAAA;wBAAAvF,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAY,CAAC,eACbjC,OAAA;wBAAA8B,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAK,CAAC,eACNjC,OAAA,CAACzB,UAAU;wBAACiG,OAAO,EAAC,SAAS;wBAACK,KAAK,EAAC,gBAAgB;wBAAAb,QAAA,GACjD6C,IAAI,CAACS,mBAAmB,EAAC,UAAG,EAACT,IAAI,CAACU,iBAAiB;sBAAA;wBAAAzF,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAC1C,CAAC;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACV;kBACN;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACF,CAAC,eACFjC,OAAA,CAAChB,IAAI;oBACH0F,IAAI,EAAC,OAAO;oBACZY,KAAK,EAAEuB,IAAI,CAACW,mBAAmB,IAAI,KAAM;oBACzChD,OAAO,EAAC;kBAAU;oBAAA1C,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACnB,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACM,CAAC,EACV6E,KAAK,GAAGvG,eAAe,CAACgC,MAAM,GAAG,CAAC,iBAAIvC,OAAA,CAACf,OAAO;kBAAA6C,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAC;cAAA,GAtB/B4E,IAAI,CAACzD,OAAO;gBAAAtB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAuBjB,CACjB;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACE;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACJ,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,EAGLrB,KAAK,iBACJZ,OAAA,CAACrB,KAAK;UAAC2F,QAAQ,EAAC,OAAO;UAACF,EAAE,EAAE;YAAEK,EAAE,EAAE;UAAE,CAAE;UAAAT,QAAA,EACnCpD;QAAK;UAAAkB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACD,CACR;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACE;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACO,CAAC,eAEhBjC,OAAA,CAAC5B,aAAa;MAAA4F,QAAA,gBACZhE,OAAA,CAAC3B,MAAM;QAAC0G,OAAO,EAAE3C,WAAY;QAACqF,QAAQ,EAAE/G,OAAQ;QAAAsD,QAAA,EAAC;MAEjD;QAAAlC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAQ,CAAC,eACTjC,OAAA,CAAC3B,MAAM;QACL0G,OAAO,EAAE1C,YAAa;QACtBmC,OAAO,EAAC,WAAW;QACnBiD,QAAQ,EACN/G,OAAO,IACPQ,iBAAiB,IACjB,CAACE,QAAQ,CAACG,YAAY,CAACe,IAAI,CAAC,CAAC,IAC7B/B,eAAe,CAACgC,MAAM,KAAK,CAAC,IAC3BzB,gBAAgB,IAAI,CAACA,gBAAgB,CAAC0B,KACxC;QACDsC,SAAS,EAAEpE,OAAO,gBAAGV,OAAA,CAACpB,gBAAgB;UAAC8F,IAAI,EAAE;QAAG;UAAA5C,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,GAAGJ,kBAAkB,CAACvB,WAAW,CAAE;QAAA0D,QAAA,EAErFtD,OAAO,GAAG,cAAc,GACxBQ,iBAAiB,GAAG,gBAAgB,GACpC,gBAAgBS,mBAAmB,CAACrB,WAAW,CAAC;MAAE;QAAAwB,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC7C,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACI,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OAaV,CAAC;AAEb,CAAC;AAACxB,EAAA,CAzaIR,mBAAmB;AAAAyH,EAAA,GAAnBzH,mBAAmB;AA2azB,eAAeA,mBAAmB;AAAC,IAAAyH,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}