{"ast": null, "code": "const accusativeWeekdays = [\"ned<PERSON><PERSON>\", \"ponděl<PERSON>\", \"úter<PERSON>\", \"středu\", \"čtvrtek\", \"pátek\", \"sobotu\"];\nconst formatRelativeLocale = {\n  lastWeek: \"'posledn<PERSON>' eeee 've' p\",\n  yesterday: \"'včera v' p\",\n  today: \"'dnes v' p\",\n  tomorrow: \"'zítra v' p\",\n  nextWeek: date => {\n    const day = date.getDay();\n    return \"'v \" + accusativeWeekdays[day] + \" o' p\";\n  },\n  other: \"P\"\n};\nexport const formatRelative = (token, date) => {\n  const format = formatRelativeLocale[token];\n  if (typeof format === \"function\") {\n    return format(date);\n  }\n  return format;\n};", "map": {"version": 3, "names": ["accusativeWeekdays", "formatRelativeLocale", "lastWeek", "yesterday", "today", "tomorrow", "nextWeek", "date", "day", "getDay", "other", "formatRelative", "token", "format"], "sources": ["C:/CMS/webapp/frontend/node_modules/date-fns/locale/cs/_lib/formatRelative.js"], "sourcesContent": ["const accusativeWeekdays = [\n  \"ned<PERSON><PERSON>\",\n  \"ponděl<PERSON>\",\n  \"úter<PERSON>\",\n  \"středu\",\n  \"čtvrtek\",\n  \"pátek\",\n  \"sobotu\",\n];\n\nconst formatRelativeLocale = {\n  lastWeek: \"'posledn<PERSON>' eeee 've' p\",\n  yesterday: \"'včera v' p\",\n  today: \"'dnes v' p\",\n  tomorrow: \"'zítra v' p\",\n  nextWeek: (date) => {\n    const day = date.getDay();\n    return \"'v \" + accusativeWeekdays[day] + \" o' p\";\n  },\n  other: \"P\",\n};\n\nexport const formatRelative = (token, date) => {\n  const format = formatRelativeLocale[token];\n\n  if (typeof format === \"function\") {\n    return format(date);\n  }\n\n  return format;\n};\n"], "mappings": "AAAA,MAAMA,kBAAkB,GAAG,CACzB,QAAQ,EACR,SAAS,EACT,OAAO,EACP,QAAQ,EACR,SAAS,EACT,OAAO,EACP,QAAQ,CACT;AAED,MAAMC,oBAAoB,GAAG;EAC3BC,QAAQ,EAAE,wBAAwB;EAClCC,SAAS,EAAE,aAAa;EACxBC,KAAK,EAAE,YAAY;EACnBC,QAAQ,EAAE,aAAa;EACvBC,QAAQ,EAAGC,IAAI,IAAK;IAClB,MAAMC,GAAG,GAAGD,IAAI,CAACE,MAAM,CAAC,CAAC;IACzB,OAAO,KAAK,GAAGT,kBAAkB,CAACQ,GAAG,CAAC,GAAG,OAAO;EAClD,CAAC;EACDE,KAAK,EAAE;AACT,CAAC;AAED,OAAO,MAAMC,cAAc,GAAGA,CAACC,KAAK,EAAEL,IAAI,KAAK;EAC7C,MAAMM,MAAM,GAAGZ,oBAAoB,CAACW,KAAK,CAAC;EAE1C,IAAI,OAAOC,MAAM,KAAK,UAAU,EAAE;IAChC,OAAOA,MAAM,CAACN,IAAI,CAAC;EACrB;EAEA,OAAOM,MAAM;AACf,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}