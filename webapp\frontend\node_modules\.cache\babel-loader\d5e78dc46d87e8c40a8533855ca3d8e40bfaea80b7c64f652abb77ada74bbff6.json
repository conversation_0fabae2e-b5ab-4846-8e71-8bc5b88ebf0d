{"ast": null, "code": "import { isSameWeek } from \"../../../isSameWeek.js\";\n\n// https://www.unicode.org/cldr/charts/32/summary/sk.html?hide#1308\nconst accusativeWeekdays = [\"nedeľu\", \"pondelok\", \"utorok\", \"stredu\", \"štvrtok\", \"piatok\", \"sobotu\"];\nfunction lastWeek(day) {\n  const weekday = accusativeWeekdays[day];\n  switch (day) {\n    case 0: /* Sun */\n    case 3: /* Wed */\n    case 6 /* Sat */:\n      return \"'minulú \" + weekday + \" o' p\";\n    default:\n      return \"'minulý' eeee 'o' p\";\n  }\n}\nfunction thisWeek(day) {\n  const weekday = accusativeWeekdays[day];\n  if (day === 4 /* Thu */) {\n    return \"'vo' eeee 'o' p\";\n  } else {\n    return \"'v \" + weekday + \" o' p\";\n  }\n}\nfunction nextWeek(day) {\n  const weekday = accusativeWeekdays[day];\n  switch (day) {\n    case 0: /* Sun */\n    case 4: /* Wed */\n    case 6 /* Sat */:\n      return \"'bud<PERSON><PERSON> \" + weekday + \" o' p\";\n    default:\n      return \"'budúci' eeee 'o' p\";\n  }\n}\nconst formatRelativeLocale = {\n  lastWeek: (date, baseDate, options) => {\n    const day = date.getDay();\n    if (isSameWeek(date, baseDate, options)) {\n      return thisWeek(day);\n    } else {\n      return lastWeek(day);\n    }\n  },\n  yesterday: \"'včera o' p\",\n  today: \"'dnes o' p\",\n  tomorrow: \"'zajtra o' p\",\n  nextWeek: (date, baseDate, options) => {\n    const day = date.getDay();\n    if (isSameWeek(date, baseDate, options)) {\n      return thisWeek(day);\n    } else {\n      return nextWeek(day);\n    }\n  },\n  other: \"P\"\n};\nexport const formatRelative = (token, date, baseDate, options) => {\n  const format = formatRelativeLocale[token];\n  if (typeof format === \"function\") {\n    return format(date, baseDate, options);\n  }\n  return format;\n};", "map": {"version": 3, "names": ["isSameWeek", "accusativeWeekdays", "lastWeek", "day", "weekday", "thisWeek", "nextWeek", "formatRelativeLocale", "date", "baseDate", "options", "getDay", "yesterday", "today", "tomorrow", "other", "formatRelative", "token", "format"], "sources": ["C:/Users/<USER>/PycharmProjects/CMS/webapp/frontend/node_modules/date-fns/locale/sk/_lib/formatRelative.js"], "sourcesContent": ["import { isSameWeek } from \"../../../isSameWeek.js\";\n\n// https://www.unicode.org/cldr/charts/32/summary/sk.html?hide#1308\nconst accusativeWeekdays = [\n  \"nedeľu\",\n  \"pondelok\",\n  \"utorok\",\n  \"stredu\",\n  \"štvrtok\",\n  \"piatok\",\n  \"sobotu\",\n];\n\nfunction lastWeek(day) {\n  const weekday = accusativeWeekdays[day];\n\n  switch (day) {\n    case 0: /* Sun */\n    case 3: /* Wed */\n    case 6 /* Sat */:\n      return \"'minulú \" + weekday + \" o' p\";\n    default:\n      return \"'minulý' eeee 'o' p\";\n  }\n}\n\nfunction thisWeek(day) {\n  const weekday = accusativeWeekdays[day];\n\n  if (day === 4 /* Thu */) {\n    return \"'vo' eeee 'o' p\";\n  } else {\n    return \"'v \" + weekday + \" o' p\";\n  }\n}\n\nfunction nextWeek(day) {\n  const weekday = accusativeWeekdays[day];\n\n  switch (day) {\n    case 0: /* Sun */\n    case 4: /* Wed */\n    case 6 /* Sat */:\n      return \"'bud<PERSON><PERSON> \" + weekday + \" o' p\";\n    default:\n      return \"'budúci' eeee 'o' p\";\n  }\n}\n\nconst formatRelativeLocale = {\n  lastWeek: (date, baseDate, options) => {\n    const day = date.getDay();\n    if (isSameWeek(date, baseDate, options)) {\n      return thisWeek(day);\n    } else {\n      return lastWeek(day);\n    }\n  },\n  yesterday: \"'včera o' p\",\n  today: \"'dnes o' p\",\n  tomorrow: \"'zajtra o' p\",\n  nextWeek: (date, baseDate, options) => {\n    const day = date.getDay();\n    if (isSameWeek(date, baseDate, options)) {\n      return thisWeek(day);\n    } else {\n      return nextWeek(day);\n    }\n  },\n  other: \"P\",\n};\n\nexport const formatRelative = (token, date, baseDate, options) => {\n  const format = formatRelativeLocale[token];\n\n  if (typeof format === \"function\") {\n    return format(date, baseDate, options);\n  }\n\n  return format;\n};\n"], "mappings": "AAAA,SAASA,UAAU,QAAQ,wBAAwB;;AAEnD;AACA,MAAMC,kBAAkB,GAAG,CACzB,QAAQ,EACR,UAAU,EACV,QAAQ,EACR,QAAQ,EACR,SAAS,EACT,QAAQ,EACR,QAAQ,CACT;AAED,SAASC,QAAQA,CAACC,GAAG,EAAE;EACrB,MAAMC,OAAO,GAAGH,kBAAkB,CAACE,GAAG,CAAC;EAEvC,QAAQA,GAAG;IACT,KAAK,CAAC,CAAC,CAAC;IACR,KAAK,CAAC,CAAC,CAAC;IACR,KAAK,CAAC,CAAC;MACL,OAAO,UAAU,GAAGC,OAAO,GAAG,OAAO;IACvC;MACE,OAAO,qBAAqB;EAChC;AACF;AAEA,SAASC,QAAQA,CAACF,GAAG,EAAE;EACrB,MAAMC,OAAO,GAAGH,kBAAkB,CAACE,GAAG,CAAC;EAEvC,IAAIA,GAAG,KAAK,CAAC,CAAC,WAAW;IACvB,OAAO,iBAAiB;EAC1B,CAAC,MAAM;IACL,OAAO,KAAK,GAAGC,OAAO,GAAG,OAAO;EAClC;AACF;AAEA,SAASE,QAAQA,CAACH,GAAG,EAAE;EACrB,MAAMC,OAAO,GAAGH,kBAAkB,CAACE,GAAG,CAAC;EAEvC,QAAQA,GAAG;IACT,KAAK,CAAC,CAAC,CAAC;IACR,KAAK,CAAC,CAAC,CAAC;IACR,KAAK,CAAC,CAAC;MACL,OAAO,UAAU,GAAGC,OAAO,GAAG,OAAO;IACvC;MACE,OAAO,qBAAqB;EAChC;AACF;AAEA,MAAMG,oBAAoB,GAAG;EAC3BL,QAAQ,EAAEA,CAACM,IAAI,EAAEC,QAAQ,EAAEC,OAAO,KAAK;IACrC,MAAMP,GAAG,GAAGK,IAAI,CAACG,MAAM,CAAC,CAAC;IACzB,IAAIX,UAAU,CAACQ,IAAI,EAAEC,QAAQ,EAAEC,OAAO,CAAC,EAAE;MACvC,OAAOL,QAAQ,CAACF,GAAG,CAAC;IACtB,CAAC,MAAM;MACL,OAAOD,QAAQ,CAACC,GAAG,CAAC;IACtB;EACF,CAAC;EACDS,SAAS,EAAE,aAAa;EACxBC,KAAK,EAAE,YAAY;EACnBC,QAAQ,EAAE,cAAc;EACxBR,QAAQ,EAAEA,CAACE,IAAI,EAAEC,QAAQ,EAAEC,OAAO,KAAK;IACrC,MAAMP,GAAG,GAAGK,IAAI,CAACG,MAAM,CAAC,CAAC;IACzB,IAAIX,UAAU,CAACQ,IAAI,EAAEC,QAAQ,EAAEC,OAAO,CAAC,EAAE;MACvC,OAAOL,QAAQ,CAACF,GAAG,CAAC;IACtB,CAAC,MAAM;MACL,OAAOG,QAAQ,CAACH,GAAG,CAAC;IACtB;EACF,CAAC;EACDY,KAAK,EAAE;AACT,CAAC;AAED,OAAO,MAAMC,cAAc,GAAGA,CAACC,KAAK,EAAET,IAAI,EAAEC,QAAQ,EAAEC,OAAO,KAAK;EAChE,MAAMQ,MAAM,GAAGX,oBAAoB,CAACU,KAAK,CAAC;EAE1C,IAAI,OAAOC,MAAM,KAAK,UAAU,EAAE;IAChC,OAAOA,MAAM,CAACV,IAAI,EAAEC,QAAQ,EAAEC,OAAO,CAAC;EACxC;EAEA,OAAOQ,MAAM;AACf,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}