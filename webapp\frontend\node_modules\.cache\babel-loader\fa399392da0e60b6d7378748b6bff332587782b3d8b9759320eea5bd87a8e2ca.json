{"ast": null, "code": "const formatDistanceLocale = {\n  lessThanXSeconds: {\n    standalone: {\n      one: \"manner wéi eng Sekonn\",\n      other: \"manner wéi {{count}} Sekonnen\"\n    },\n    withPreposition: {\n      one: \"manner wéi enger Sekonn\",\n      other: \"manner wéi {{count}} Sekonnen\"\n    }\n  },\n  xSeconds: {\n    standalone: {\n      one: \"eng Sekonn\",\n      other: \"{{count}} Sekonnen\"\n    },\n    withPreposition: {\n      one: \"enger Sekonn\",\n      other: \"{{count}} Sekonnen\"\n    }\n  },\n  halfAMinute: {\n    standalone: \"eng hallef Minutt\",\n    withPreposition: \"enger hallwer Minutt\"\n  },\n  lessThanXMinutes: {\n    standalone: {\n      one: \"manner wéi eng Minutt\",\n      other: \"manner wéi {{count}} Minutten\"\n    },\n    withPreposition: {\n      one: \"manner wéi enger Minutt\",\n      other: \"manner wéi {{count}} Minutten\"\n    }\n  },\n  xMinutes: {\n    standalone: {\n      one: \"eng Minutt\",\n      other: \"{{count}} Minutten\"\n    },\n    withPreposition: {\n      one: \"enger Minutt\",\n      other: \"{{count}} Minutten\"\n    }\n  },\n  aboutXHours: {\n    standalone: {\n      one: \"ongeféier eng Stonn\",\n      other: \"ongeféier {{count}} Stonnen\"\n    },\n    withPreposition: {\n      one: \"ongeféier enger Stonn\",\n      other: \"ongeféier {{count}} Stonnen\"\n    }\n  },\n  xHours: {\n    standalone: {\n      one: \"eng Stonn\",\n      other: \"{{count}} Stonnen\"\n    },\n    withPreposition: {\n      one: \"enger Stonn\",\n      other: \"{{count}} Stonnen\"\n    }\n  },\n  xDays: {\n    standalone: {\n      one: \"een Dag\",\n      other: \"{{count}} Deeg\"\n    },\n    withPreposition: {\n      one: \"engem Dag\",\n      other: \"{{count}} Deeg\"\n    }\n  },\n  aboutXWeeks: {\n    standalone: {\n      one: \"ongeféier eng Woch\",\n      other: \"ongeféier {{count}} Wochen\"\n    },\n    withPreposition: {\n      one: \"ongeféier enger Woche\",\n      other: \"ongeféier {{count}} Wochen\"\n    }\n  },\n  xWeeks: {\n    standalone: {\n      one: \"eng Woch\",\n      other: \"{{count}} Wochen\"\n    },\n    withPreposition: {\n      one: \"enger Woch\",\n      other: \"{{count}} Wochen\"\n    }\n  },\n  aboutXMonths: {\n    standalone: {\n      one: \"ongeféier ee Mount\",\n      other: \"ongeféier {{count}} Méint\"\n    },\n    withPreposition: {\n      one: \"ongeféier engem Mount\",\n      other: \"ongeféier {{count}} Méint\"\n    }\n  },\n  xMonths: {\n    standalone: {\n      one: \"ee Mount\",\n      other: \"{{count}} Méint\"\n    },\n    withPreposition: {\n      one: \"engem Mount\",\n      other: \"{{count}} Méint\"\n    }\n  },\n  aboutXYears: {\n    standalone: {\n      one: \"ongeféier ee Joer\",\n      other: \"ongeféier {{count}} Joer\"\n    },\n    withPreposition: {\n      one: \"ongeféier engem Joer\",\n      other: \"ongeféier {{count}} Joer\"\n    }\n  },\n  xYears: {\n    standalone: {\n      one: \"ee Joer\",\n      other: \"{{count}} Joer\"\n    },\n    withPreposition: {\n      one: \"engem Joer\",\n      other: \"{{count}} Joer\"\n    }\n  },\n  overXYears: {\n    standalone: {\n      one: \"méi wéi ee Joer\",\n      other: \"méi wéi {{count}} Joer\"\n    },\n    withPreposition: {\n      one: \"méi wéi engem Joer\",\n      other: \"méi wéi {{count}} Joer\"\n    }\n  },\n  almostXYears: {\n    standalone: {\n      one: \"bal ee Joer\",\n      other: \"bal {{count}} Joer\"\n    },\n    withPreposition: {\n      one: \"bal engem Joer\",\n      other: \"bal {{count}} Joer\"\n    }\n  }\n};\nconst EXCEPTION_CONSONANTS = [\"d\", \"h\", \"n\", \"t\", \"z\"];\nconst VOWELS = [\"a,\", \"e\", \"i\", \"o\", \"u\"];\nconst DIGITS_SPOKEN_N_NEEDED = [0, 1, 2, 3, 8, 9];\nconst FIRST_TWO_DIGITS_SPOKEN_NO_N_NEEDED = [40, 50, 60, 70];\n\n// Eifeler Regel\nfunction isFinalNNeeded(nextWords) {\n  const firstLetter = nextWords.charAt(0).toLowerCase();\n  if (VOWELS.indexOf(firstLetter) != -1 || EXCEPTION_CONSONANTS.indexOf(firstLetter) != -1) {\n    return true;\n  }\n\n  // Numbers would need to converted into words for checking.\n  // Therefore, I have listed the digits that require a preceeding n with a few exceptions.\n  const firstWord = nextWords.split(\" \")[0];\n  const number = parseInt(firstWord);\n  if (!isNaN(number) && DIGITS_SPOKEN_N_NEEDED.indexOf(number % 10) != -1 && FIRST_TWO_DIGITS_SPOKEN_NO_N_NEEDED.indexOf(parseInt(firstWord.substring(0, 2))) == -1) {\n    return true;\n  }\n\n  // Omit other checks as they are not expected here.\n  return false;\n}\nexport const formatDistance = (token, count, options) => {\n  let result;\n  const tokenValue = formatDistanceLocale[token];\n  const usageGroup = options?.addSuffix ? tokenValue.withPreposition : tokenValue.standalone;\n  if (typeof usageGroup === \"string\") {\n    result = usageGroup;\n  } else if (count === 1) {\n    result = usageGroup.one;\n  } else {\n    result = usageGroup.other.replace(\"{{count}}\", String(count));\n  }\n  if (options?.addSuffix) {\n    if (options.comparison && options.comparison > 0) {\n      return \"a\" + (isFinalNNeeded(result) ? \"n\" : \"\") + \" \" + result;\n    } else {\n      return \"viru\" + (isFinalNNeeded(result) ? \"n\" : \"\") + \" \" + result;\n    }\n  }\n  return result;\n};", "map": {"version": 3, "names": ["formatDistanceLocale", "lessThanXSeconds", "standalone", "one", "other", "withPreposition", "xSeconds", "halfAMinute", "lessThanXMinutes", "xMinutes", "aboutXHours", "xHours", "xDays", "aboutXWeeks", "xWeeks", "aboutXMonths", "xMonths", "aboutXYears", "xYears", "overXYears", "almostXYears", "EXCEPTION_CONSONANTS", "VOWELS", "DIGITS_SPOKEN_N_NEEDED", "FIRST_TWO_DIGITS_SPOKEN_NO_N_NEEDED", "isFinalNNeeded", "nextWords", "firstLetter", "char<PERSON>t", "toLowerCase", "indexOf", "firstWord", "split", "number", "parseInt", "isNaN", "substring", "formatDistance", "token", "count", "options", "result", "tokenValue", "usageGroup", "addSuffix", "replace", "String", "comparison"], "sources": ["C:/CMS/webapp/frontend/node_modules/date-fns/locale/lb/_lib/formatDistance.js"], "sourcesContent": ["const formatDistanceLocale = {\n  lessThanXSeconds: {\n    standalone: {\n      one: \"manner wéi eng Sekonn\",\n      other: \"manner wéi {{count}} Sekonnen\",\n    },\n    withPreposition: {\n      one: \"manner wéi enger Sekonn\",\n      other: \"manner wéi {{count}} Sekonnen\",\n    },\n  },\n\n  xSeconds: {\n    standalone: {\n      one: \"eng Sekonn\",\n      other: \"{{count}} Sekonnen\",\n    },\n    withPreposition: {\n      one: \"enger Sekonn\",\n      other: \"{{count}} Sekonnen\",\n    },\n  },\n\n  halfAMinute: {\n    standalone: \"eng hallef Minutt\",\n    withPreposition: \"enger hallwer Minutt\",\n  },\n\n  lessThanXMinutes: {\n    standalone: {\n      one: \"manner wéi eng Minutt\",\n      other: \"manner wéi {{count}} Minutten\",\n    },\n    withPreposition: {\n      one: \"manner wéi enger Minutt\",\n      other: \"manner wéi {{count}} Minutten\",\n    },\n  },\n\n  xMinutes: {\n    standalone: {\n      one: \"eng Minutt\",\n      other: \"{{count}} Minutten\",\n    },\n    withPreposition: {\n      one: \"enger Minutt\",\n      other: \"{{count}} Minutten\",\n    },\n  },\n\n  aboutXHours: {\n    standalone: {\n      one: \"ongeféier eng Stonn\",\n      other: \"ongeféier {{count}} Stonnen\",\n    },\n    withPreposition: {\n      one: \"ongeféier enger Stonn\",\n      other: \"ongeféier {{count}} Stonnen\",\n    },\n  },\n\n  xHours: {\n    standalone: {\n      one: \"eng Stonn\",\n      other: \"{{count}} Stonnen\",\n    },\n    withPreposition: {\n      one: \"enger Stonn\",\n      other: \"{{count}} Stonnen\",\n    },\n  },\n\n  xDays: {\n    standalone: {\n      one: \"een Dag\",\n      other: \"{{count}} Deeg\",\n    },\n    withPreposition: {\n      one: \"engem Dag\",\n      other: \"{{count}} Deeg\",\n    },\n  },\n\n  aboutXWeeks: {\n    standalone: {\n      one: \"ongeféier eng Woch\",\n      other: \"ongeféier {{count}} Wochen\",\n    },\n    withPreposition: {\n      one: \"ongeféier enger Woche\",\n      other: \"ongeféier {{count}} Wochen\",\n    },\n  },\n\n  xWeeks: {\n    standalone: {\n      one: \"eng Woch\",\n      other: \"{{count}} Wochen\",\n    },\n    withPreposition: {\n      one: \"enger Woch\",\n      other: \"{{count}} Wochen\",\n    },\n  },\n\n  aboutXMonths: {\n    standalone: {\n      one: \"ongeféier ee Mount\",\n      other: \"ongeféier {{count}} Méint\",\n    },\n    withPreposition: {\n      one: \"ongeféier engem Mount\",\n      other: \"ongeféier {{count}} Méint\",\n    },\n  },\n\n  xMonths: {\n    standalone: {\n      one: \"ee Mount\",\n      other: \"{{count}} Méint\",\n    },\n    withPreposition: {\n      one: \"engem Mount\",\n      other: \"{{count}} Méint\",\n    },\n  },\n\n  aboutXYears: {\n    standalone: {\n      one: \"ongeféier ee Joer\",\n      other: \"ongeféier {{count}} Joer\",\n    },\n    withPreposition: {\n      one: \"ongeféier engem Joer\",\n      other: \"ongeféier {{count}} Joer\",\n    },\n  },\n\n  xYears: {\n    standalone: {\n      one: \"ee Joer\",\n      other: \"{{count}} Joer\",\n    },\n    withPreposition: {\n      one: \"engem Joer\",\n      other: \"{{count}} Joer\",\n    },\n  },\n\n  overXYears: {\n    standalone: {\n      one: \"méi wéi ee Joer\",\n      other: \"méi wéi {{count}} Joer\",\n    },\n    withPreposition: {\n      one: \"méi wéi engem Joer\",\n      other: \"méi wéi {{count}} Joer\",\n    },\n  },\n\n  almostXYears: {\n    standalone: {\n      one: \"bal ee Joer\",\n      other: \"bal {{count}} Joer\",\n    },\n    withPreposition: {\n      one: \"bal engem Joer\",\n      other: \"bal {{count}} Joer\",\n    },\n  },\n};\n\nconst EXCEPTION_CONSONANTS = [\"d\", \"h\", \"n\", \"t\", \"z\"];\nconst VOWELS = [\"a,\", \"e\", \"i\", \"o\", \"u\"];\nconst DIGITS_SPOKEN_N_NEEDED = [0, 1, 2, 3, 8, 9];\nconst FIRST_TWO_DIGITS_SPOKEN_NO_N_NEEDED = [40, 50, 60, 70];\n\n// Eifeler Regel\nfunction isFinalNNeeded(nextWords) {\n  const firstLetter = nextWords.charAt(0).toLowerCase();\n  if (\n    VOWELS.indexOf(firstLetter) != -1 ||\n    EXCEPTION_CONSONANTS.indexOf(firstLetter) != -1\n  ) {\n    return true;\n  }\n\n  // Numbers would need to converted into words for checking.\n  // Therefore, I have listed the digits that require a preceeding n with a few exceptions.\n  const firstWord = nextWords.split(\" \")[0];\n  const number = parseInt(firstWord);\n  if (\n    !isNaN(number) &&\n    DIGITS_SPOKEN_N_NEEDED.indexOf(number % 10) != -1 &&\n    FIRST_TWO_DIGITS_SPOKEN_NO_N_NEEDED.indexOf(\n      parseInt(firstWord.substring(0, 2)),\n    ) == -1\n  ) {\n    return true;\n  }\n\n  // Omit other checks as they are not expected here.\n  return false;\n}\n\nexport const formatDistance = (token, count, options) => {\n  let result;\n\n  const tokenValue = formatDistanceLocale[token];\n\n  const usageGroup = options?.addSuffix\n    ? tokenValue.withPreposition\n    : tokenValue.standalone;\n\n  if (typeof usageGroup === \"string\") {\n    result = usageGroup;\n  } else if (count === 1) {\n    result = usageGroup.one;\n  } else {\n    result = usageGroup.other.replace(\"{{count}}\", String(count));\n  }\n\n  if (options?.addSuffix) {\n    if (options.comparison && options.comparison > 0) {\n      return \"a\" + (isFinalNNeeded(result) ? \"n\" : \"\") + \" \" + result;\n    } else {\n      return \"viru\" + (isFinalNNeeded(result) ? \"n\" : \"\") + \" \" + result;\n    }\n  }\n\n  return result;\n};\n"], "mappings": "AAAA,MAAMA,oBAAoB,GAAG;EAC3BC,gBAAgB,EAAE;IAChBC,UAAU,EAAE;MACVC,GAAG,EAAE,uBAAuB;MAC5BC,KAAK,EAAE;IACT,CAAC;IACDC,eAAe,EAAE;MACfF,GAAG,EAAE,yBAAyB;MAC9BC,KAAK,EAAE;IACT;EACF,CAAC;EAEDE,QAAQ,EAAE;IACRJ,UAAU,EAAE;MACVC,GAAG,EAAE,YAAY;MACjBC,KAAK,EAAE;IACT,CAAC;IACDC,eAAe,EAAE;MACfF,GAAG,EAAE,cAAc;MACnBC,KAAK,EAAE;IACT;EACF,CAAC;EAEDG,WAAW,EAAE;IACXL,UAAU,EAAE,mBAAmB;IAC/BG,eAAe,EAAE;EACnB,CAAC;EAEDG,gBAAgB,EAAE;IAChBN,UAAU,EAAE;MACVC,GAAG,EAAE,uBAAuB;MAC5BC,KAAK,EAAE;IACT,CAAC;IACDC,eAAe,EAAE;MACfF,GAAG,EAAE,yBAAyB;MAC9BC,KAAK,EAAE;IACT;EACF,CAAC;EAEDK,QAAQ,EAAE;IACRP,UAAU,EAAE;MACVC,GAAG,EAAE,YAAY;MACjBC,KAAK,EAAE;IACT,CAAC;IACDC,eAAe,EAAE;MACfF,GAAG,EAAE,cAAc;MACnBC,KAAK,EAAE;IACT;EACF,CAAC;EAEDM,WAAW,EAAE;IACXR,UAAU,EAAE;MACVC,GAAG,EAAE,qBAAqB;MAC1BC,KAAK,EAAE;IACT,CAAC;IACDC,eAAe,EAAE;MACfF,GAAG,EAAE,uBAAuB;MAC5BC,KAAK,EAAE;IACT;EACF,CAAC;EAEDO,MAAM,EAAE;IACNT,UAAU,EAAE;MACVC,GAAG,EAAE,WAAW;MAChBC,KAAK,EAAE;IACT,CAAC;IACDC,eAAe,EAAE;MACfF,GAAG,EAAE,aAAa;MAClBC,KAAK,EAAE;IACT;EACF,CAAC;EAEDQ,KAAK,EAAE;IACLV,UAAU,EAAE;MACVC,GAAG,EAAE,SAAS;MACdC,KAAK,EAAE;IACT,CAAC;IACDC,eAAe,EAAE;MACfF,GAAG,EAAE,WAAW;MAChBC,KAAK,EAAE;IACT;EACF,CAAC;EAEDS,WAAW,EAAE;IACXX,UAAU,EAAE;MACVC,GAAG,EAAE,oBAAoB;MACzBC,KAAK,EAAE;IACT,CAAC;IACDC,eAAe,EAAE;MACfF,GAAG,EAAE,uBAAuB;MAC5BC,KAAK,EAAE;IACT;EACF,CAAC;EAEDU,MAAM,EAAE;IACNZ,UAAU,EAAE;MACVC,GAAG,EAAE,UAAU;MACfC,KAAK,EAAE;IACT,CAAC;IACDC,eAAe,EAAE;MACfF,GAAG,EAAE,YAAY;MACjBC,KAAK,EAAE;IACT;EACF,CAAC;EAEDW,YAAY,EAAE;IACZb,UAAU,EAAE;MACVC,GAAG,EAAE,oBAAoB;MACzBC,KAAK,EAAE;IACT,CAAC;IACDC,eAAe,EAAE;MACfF,GAAG,EAAE,uBAAuB;MAC5BC,KAAK,EAAE;IACT;EACF,CAAC;EAEDY,OAAO,EAAE;IACPd,UAAU,EAAE;MACVC,GAAG,EAAE,UAAU;MACfC,KAAK,EAAE;IACT,CAAC;IACDC,eAAe,EAAE;MACfF,GAAG,EAAE,aAAa;MAClBC,KAAK,EAAE;IACT;EACF,CAAC;EAEDa,WAAW,EAAE;IACXf,UAAU,EAAE;MACVC,GAAG,EAAE,mBAAmB;MACxBC,KAAK,EAAE;IACT,CAAC;IACDC,eAAe,EAAE;MACfF,GAAG,EAAE,sBAAsB;MAC3BC,KAAK,EAAE;IACT;EACF,CAAC;EAEDc,MAAM,EAAE;IACNhB,UAAU,EAAE;MACVC,GAAG,EAAE,SAAS;MACdC,KAAK,EAAE;IACT,CAAC;IACDC,eAAe,EAAE;MACfF,GAAG,EAAE,YAAY;MACjBC,KAAK,EAAE;IACT;EACF,CAAC;EAEDe,UAAU,EAAE;IACVjB,UAAU,EAAE;MACVC,GAAG,EAAE,iBAAiB;MACtBC,KAAK,EAAE;IACT,CAAC;IACDC,eAAe,EAAE;MACfF,GAAG,EAAE,oBAAoB;MACzBC,KAAK,EAAE;IACT;EACF,CAAC;EAEDgB,YAAY,EAAE;IACZlB,UAAU,EAAE;MACVC,GAAG,EAAE,aAAa;MAClBC,KAAK,EAAE;IACT,CAAC;IACDC,eAAe,EAAE;MACfF,GAAG,EAAE,gBAAgB;MACrBC,KAAK,EAAE;IACT;EACF;AACF,CAAC;AAED,MAAMiB,oBAAoB,GAAG,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,CAAC;AACtD,MAAMC,MAAM,GAAG,CAAC,IAAI,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,CAAC;AACzC,MAAMC,sBAAsB,GAAG,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC;AACjD,MAAMC,mCAAmC,GAAG,CAAC,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,CAAC;;AAE5D;AACA,SAASC,cAAcA,CAACC,SAAS,EAAE;EACjC,MAAMC,WAAW,GAAGD,SAAS,CAACE,MAAM,CAAC,CAAC,CAAC,CAACC,WAAW,CAAC,CAAC;EACrD,IACEP,MAAM,CAACQ,OAAO,CAACH,WAAW,CAAC,IAAI,CAAC,CAAC,IACjCN,oBAAoB,CAACS,OAAO,CAACH,WAAW,CAAC,IAAI,CAAC,CAAC,EAC/C;IACA,OAAO,IAAI;EACb;;EAEA;EACA;EACA,MAAMI,SAAS,GAAGL,SAAS,CAACM,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC;EACzC,MAAMC,MAAM,GAAGC,QAAQ,CAACH,SAAS,CAAC;EAClC,IACE,CAACI,KAAK,CAACF,MAAM,CAAC,IACdV,sBAAsB,CAACO,OAAO,CAACG,MAAM,GAAG,EAAE,CAAC,IAAI,CAAC,CAAC,IACjDT,mCAAmC,CAACM,OAAO,CACzCI,QAAQ,CAACH,SAAS,CAACK,SAAS,CAAC,CAAC,EAAE,CAAC,CAAC,CACpC,CAAC,IAAI,CAAC,CAAC,EACP;IACA,OAAO,IAAI;EACb;;EAEA;EACA,OAAO,KAAK;AACd;AAEA,OAAO,MAAMC,cAAc,GAAGA,CAACC,KAAK,EAAEC,KAAK,EAAEC,OAAO,KAAK;EACvD,IAAIC,MAAM;EAEV,MAAMC,UAAU,GAAG1C,oBAAoB,CAACsC,KAAK,CAAC;EAE9C,MAAMK,UAAU,GAAGH,OAAO,EAAEI,SAAS,GACjCF,UAAU,CAACrC,eAAe,GAC1BqC,UAAU,CAACxC,UAAU;EAEzB,IAAI,OAAOyC,UAAU,KAAK,QAAQ,EAAE;IAClCF,MAAM,GAAGE,UAAU;EACrB,CAAC,MAAM,IAAIJ,KAAK,KAAK,CAAC,EAAE;IACtBE,MAAM,GAAGE,UAAU,CAACxC,GAAG;EACzB,CAAC,MAAM;IACLsC,MAAM,GAAGE,UAAU,CAACvC,KAAK,CAACyC,OAAO,CAAC,WAAW,EAAEC,MAAM,CAACP,KAAK,CAAC,CAAC;EAC/D;EAEA,IAAIC,OAAO,EAAEI,SAAS,EAAE;IACtB,IAAIJ,OAAO,CAACO,UAAU,IAAIP,OAAO,CAACO,UAAU,GAAG,CAAC,EAAE;MAChD,OAAO,GAAG,IAAItB,cAAc,CAACgB,MAAM,CAAC,GAAG,GAAG,GAAG,EAAE,CAAC,GAAG,GAAG,GAAGA,MAAM;IACjE,CAAC,MAAM;MACL,OAAO,MAAM,IAAIhB,cAAc,CAACgB,MAAM,CAAC,GAAG,GAAG,GAAG,EAAE,CAAC,GAAG,GAAG,GAAGA,MAAM;IACpE;EACF;EAEA,OAAOA,MAAM;AACf,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}