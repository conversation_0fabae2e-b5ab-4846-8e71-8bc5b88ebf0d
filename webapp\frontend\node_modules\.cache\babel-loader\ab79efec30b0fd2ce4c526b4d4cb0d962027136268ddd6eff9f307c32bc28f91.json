{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\PycharmProjects\\\\CMS\\\\webapp\\\\frontend\\\\src\\\\components\\\\MainMenu.js\",\n  _s = $RefreshSig$();\nimport React from 'react';\nimport { useNavigate, useLocation } from 'react-router-dom';\nimport { List, ListItem, ListItemIcon, ListItemText, Divider } from '@mui/material';\nimport { Home as HomeIcon, AdminPanelSettings as AdminIcon, Construction as ConstructionIcon, Cable as CableIcon, Description as ReportIcon } from '@mui/icons-material';\nimport { useAuth } from '../context/AuthContext';\nimport { jsxDEV as _jsxDEV, Fragment as _Fragment } from \"react/jsx-dev-runtime\";\nconst MainMenu = () => {\n  _s();\n  const navigate = useNavigate();\n  const location = useLocation();\n  const {\n    user,\n    isImpersonating\n  } = useAuth();\n\n  // Verifica se un percorso è attivo\n  const isActive = path => {\n    return location.pathname === path;\n  };\n\n  // Naviga a un percorso\n  const navigateTo = path => {\n    console.log('Navigazione a:', path, 'isImpersonating:', isImpersonating, 'user:', user);\n    // Se l'utente è un amministratore che sta impersonando un utente e sta cliccando su Home,\n    // reindirizza al menu amministratore invece che alla home generica\n    if (path === '/dashboard' && isImpersonating) {\n      console.log('Utente impersonato, reindirizzamento al menu amministratore');\n      navigate('/dashboard/admin');\n    } else {\n      navigate(path);\n    }\n  };\n  return /*#__PURE__*/_jsxDEV(List, {\n    children: [/*#__PURE__*/_jsxDEV(ListItem, {\n      button: true,\n      selected: isImpersonating ? isActive('/dashboard/admin') : isActive('/dashboard'),\n      onClick: () => navigateTo('/dashboard'),\n      children: [/*#__PURE__*/_jsxDEV(ListItemIcon, {\n        children: /*#__PURE__*/_jsxDEV(HomeIcon, {}, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 51,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 50,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(ListItemText, {\n        primary: isImpersonating ? \"Torna al Menu Admin\" : \"Home\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 53,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 45,\n      columnNumber: 7\n    }, this), (user === null || user === void 0 ? void 0 : user.role) === 'owner' && /*#__PURE__*/_jsxDEV(_Fragment, {\n      children: [/*#__PURE__*/_jsxDEV(Divider, {}, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 59,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(ListItem, {\n        button: true,\n        selected: isActive('/dashboard/admin'),\n        onClick: () => navigateTo('/dashboard/admin'),\n        children: [/*#__PURE__*/_jsxDEV(ListItemIcon, {\n          children: /*#__PURE__*/_jsxDEV(AdminIcon, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 66,\n            columnNumber: 15\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 65,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(ListItemText, {\n          primary: \"Amministrazione\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 68,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 60,\n        columnNumber: 11\n      }, this)]\n    }, void 0, true), (user === null || user === void 0 ? void 0 : user.role) !== 'owner' && /*#__PURE__*/_jsxDEV(_Fragment, {\n      children: [/*#__PURE__*/_jsxDEV(Divider, {}, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 76,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(ListItem, {\n        button: true,\n        selected: isActive('/dashboard/cantieri'),\n        onClick: () => navigateTo('/dashboard/cantieri'),\n        children: [/*#__PURE__*/_jsxDEV(ListItemIcon, {\n          children: /*#__PURE__*/_jsxDEV(ConstructionIcon, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 83,\n            columnNumber: 15\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 82,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(ListItemText, {\n          primary: \"I Miei Cantieri\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 85,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 77,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(ListItem, {\n        button: true,\n        selected: isActive('/dashboard/cavi'),\n        onClick: () => navigateTo('/dashboard/cavi'),\n        children: [/*#__PURE__*/_jsxDEV(ListItemIcon, {\n          children: /*#__PURE__*/_jsxDEV(CableIcon, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 95,\n            columnNumber: 15\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 94,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(ListItemText, {\n          primary: \"Gestione Cavi\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 97,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 89,\n        columnNumber: 11\n      }, this)]\n    }, void 0, true)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 43,\n    columnNumber: 5\n  }, this);\n};\n_s(MainMenu, \"KIlfeDoKiL67c6wZKwaDGU5QasQ=\", false, function () {\n  return [useNavigate, useLocation, useAuth];\n});\n_c = MainMenu;\nexport default MainMenu;\nvar _c;\n$RefreshReg$(_c, \"MainMenu\");", "map": {"version": 3, "names": ["React", "useNavigate", "useLocation", "List", "ListItem", "ListItemIcon", "ListItemText", "Divider", "Home", "HomeIcon", "AdminPanelSettings", "AdminIcon", "Construction", "ConstructionIcon", "Cable", "CableIcon", "Description", "ReportIcon", "useAuth", "jsxDEV", "_jsxDEV", "Fragment", "_Fragment", "MainMenu", "_s", "navigate", "location", "user", "isImpersonating", "isActive", "path", "pathname", "navigateTo", "console", "log", "children", "button", "selected", "onClick", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "primary", "role", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/PycharmProjects/CMS/webapp/frontend/src/components/MainMenu.js"], "sourcesContent": ["import React from 'react';\nimport { useNavigate, useLocation } from 'react-router-dom';\nimport {\n  List,\n  ListItem,\n  ListItemIcon,\n  ListItemText,\n  Divider\n} from '@mui/material';\nimport {\n  Home as HomeIcon,\n  AdminPanelSettings as AdminIcon,\n  Construction as ConstructionIcon,\n  Cable as CableIcon,\n  Description as ReportIcon\n} from '@mui/icons-material';\nimport { useAuth } from '../context/AuthContext';\n\nconst MainMenu = () => {\n  const navigate = useNavigate();\n  const location = useLocation();\n  const { user, isImpersonating } = useAuth();\n\n  // Verifica se un percorso è attivo\n  const isActive = (path) => {\n    return location.pathname === path;\n  };\n\n  // Naviga a un percorso\n  const navigateTo = (path) => {\n    console.log('Navigazione a:', path, 'isImpersonating:', isImpersonating, 'user:', user);\n    // Se l'utente è un amministratore che sta impersonando un utente e sta cliccando su Home,\n    // reindirizza al menu amministratore invece che alla home generica\n    if (path === '/dashboard' && isImpersonating) {\n      console.log('Utente impersonato, reindirizzamento al menu amministratore');\n      navigate('/dashboard/admin');\n    } else {\n      navigate(path);\n    }\n  };\n\n  return (\n    <List>\n      {/* Home - Se l'utente è un amministratore che sta impersonando un utente, mostra \"Torna al Menu Admin\" */}\n      <ListItem\n        button\n        selected={isImpersonating ? isActive('/dashboard/admin') : isActive('/dashboard')}\n        onClick={() => navigateTo('/dashboard')}\n      >\n        <ListItemIcon>\n          <HomeIcon />\n        </ListItemIcon>\n        <ListItemText primary={isImpersonating ? \"Torna al Menu Admin\" : \"Home\"} />\n      </ListItem>\n\n      {/* Menu Amministratore (solo per admin) */}\n      {user?.role === 'owner' && (\n        <>\n          <Divider />\n          <ListItem\n            button\n            selected={isActive('/dashboard/admin')}\n            onClick={() => navigateTo('/dashboard/admin')}\n          >\n            <ListItemIcon>\n              <AdminIcon />\n            </ListItemIcon>\n            <ListItemText primary=\"Amministrazione\" />\n          </ListItem>\n        </>\n      )}\n\n      {/* Menu per utenti standard e cantieri (non per admin) */}\n      {user?.role !== 'owner' && (\n        <>\n          <Divider />\n          <ListItem\n            button\n            selected={isActive('/dashboard/cantieri')}\n            onClick={() => navigateTo('/dashboard/cantieri')}\n          >\n            <ListItemIcon>\n              <ConstructionIcon />\n            </ListItemIcon>\n            <ListItemText primary=\"I Miei Cantieri\" />\n          </ListItem>\n\n          {/* Menu Cavi */}\n          <ListItem\n            button\n            selected={isActive('/dashboard/cavi')}\n            onClick={() => navigateTo('/dashboard/cavi')}\n          >\n            <ListItemIcon>\n              <CableIcon />\n            </ListItemIcon>\n            <ListItemText primary=\"Gestione Cavi\" />\n          </ListItem>\n\n          {/* Rimosso il menu Report perché appartiene al menu gestione cavi */}\n        </>\n      )}\n    </List>\n  );\n};\n\nexport default MainMenu;\n"], "mappings": ";;AAAA,OAAOA,KAAK,MAAM,OAAO;AACzB,SAASC,WAAW,EAAEC,WAAW,QAAQ,kBAAkB;AAC3D,SACEC,IAAI,EACJC,QAAQ,EACRC,YAAY,EACZC,YAAY,EACZC,OAAO,QACF,eAAe;AACtB,SACEC,IAAI,IAAIC,QAAQ,EAChBC,kBAAkB,IAAIC,SAAS,EAC/BC,YAAY,IAAIC,gBAAgB,EAChCC,KAAK,IAAIC,SAAS,EAClBC,WAAW,IAAIC,UAAU,QACpB,qBAAqB;AAC5B,SAASC,OAAO,QAAQ,wBAAwB;AAAC,SAAAC,MAAA,IAAAC,OAAA,EAAAC,QAAA,IAAAC,SAAA;AAEjD,MAAMC,QAAQ,GAAGA,CAAA,KAAM;EAAAC,EAAA;EACrB,MAAMC,QAAQ,GAAGxB,WAAW,CAAC,CAAC;EAC9B,MAAMyB,QAAQ,GAAGxB,WAAW,CAAC,CAAC;EAC9B,MAAM;IAAEyB,IAAI;IAAEC;EAAgB,CAAC,GAAGV,OAAO,CAAC,CAAC;;EAE3C;EACA,MAAMW,QAAQ,GAAIC,IAAI,IAAK;IACzB,OAAOJ,QAAQ,CAACK,QAAQ,KAAKD,IAAI;EACnC,CAAC;;EAED;EACA,MAAME,UAAU,GAAIF,IAAI,IAAK;IAC3BG,OAAO,CAACC,GAAG,CAAC,gBAAgB,EAAEJ,IAAI,EAAE,kBAAkB,EAAEF,eAAe,EAAE,OAAO,EAAED,IAAI,CAAC;IACvF;IACA;IACA,IAAIG,IAAI,KAAK,YAAY,IAAIF,eAAe,EAAE;MAC5CK,OAAO,CAACC,GAAG,CAAC,6DAA6D,CAAC;MAC1ET,QAAQ,CAAC,kBAAkB,CAAC;IAC9B,CAAC,MAAM;MACLA,QAAQ,CAACK,IAAI,CAAC;IAChB;EACF,CAAC;EAED,oBACEV,OAAA,CAACjB,IAAI;IAAAgC,QAAA,gBAEHf,OAAA,CAAChB,QAAQ;MACPgC,MAAM;MACNC,QAAQ,EAAET,eAAe,GAAGC,QAAQ,CAAC,kBAAkB,CAAC,GAAGA,QAAQ,CAAC,YAAY,CAAE;MAClFS,OAAO,EAAEA,CAAA,KAAMN,UAAU,CAAC,YAAY,CAAE;MAAAG,QAAA,gBAExCf,OAAA,CAACf,YAAY;QAAA8B,QAAA,eACXf,OAAA,CAACX,QAAQ;UAAA8B,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACA,CAAC,eACftB,OAAA,CAACd,YAAY;QAACqC,OAAO,EAAEf,eAAe,GAAG,qBAAqB,GAAG;MAAO;QAAAW,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACnE,CAAC,EAGV,CAAAf,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAEiB,IAAI,MAAK,OAAO,iBACrBxB,OAAA,CAAAE,SAAA;MAAAa,QAAA,gBACEf,OAAA,CAACb,OAAO;QAAAgC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC,eACXtB,OAAA,CAAChB,QAAQ;QACPgC,MAAM;QACNC,QAAQ,EAAER,QAAQ,CAAC,kBAAkB,CAAE;QACvCS,OAAO,EAAEA,CAAA,KAAMN,UAAU,CAAC,kBAAkB,CAAE;QAAAG,QAAA,gBAE9Cf,OAAA,CAACf,YAAY;UAAA8B,QAAA,eACXf,OAAA,CAACT,SAAS;YAAA4B,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACD,CAAC,eACftB,OAAA,CAACd,YAAY;UAACqC,OAAO,EAAC;QAAiB;UAAAJ,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAClC,CAAC;IAAA,eACX,CACH,EAGA,CAAAf,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAEiB,IAAI,MAAK,OAAO,iBACrBxB,OAAA,CAAAE,SAAA;MAAAa,QAAA,gBACEf,OAAA,CAACb,OAAO;QAAAgC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC,eACXtB,OAAA,CAAChB,QAAQ;QACPgC,MAAM;QACNC,QAAQ,EAAER,QAAQ,CAAC,qBAAqB,CAAE;QAC1CS,OAAO,EAAEA,CAAA,KAAMN,UAAU,CAAC,qBAAqB,CAAE;QAAAG,QAAA,gBAEjDf,OAAA,CAACf,YAAY;UAAA8B,QAAA,eACXf,OAAA,CAACP,gBAAgB;YAAA0B,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACR,CAAC,eACftB,OAAA,CAACd,YAAY;UAACqC,OAAO,EAAC;QAAiB;UAAAJ,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAClC,CAAC,eAGXtB,OAAA,CAAChB,QAAQ;QACPgC,MAAM;QACNC,QAAQ,EAAER,QAAQ,CAAC,iBAAiB,CAAE;QACtCS,OAAO,EAAEA,CAAA,KAAMN,UAAU,CAAC,iBAAiB,CAAE;QAAAG,QAAA,gBAE7Cf,OAAA,CAACf,YAAY;UAAA8B,QAAA,eACXf,OAAA,CAACL,SAAS;YAAAwB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACD,CAAC,eACftB,OAAA,CAACd,YAAY;UAACqC,OAAO,EAAC;QAAe;UAAAJ,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAChC,CAAC;IAAA,eAGX,CACH;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACG,CAAC;AAEX,CAAC;AAAClB,EAAA,CAtFID,QAAQ;EAAA,QACKtB,WAAW,EACXC,WAAW,EACMgB,OAAO;AAAA;AAAA2B,EAAA,GAHrCtB,QAAQ;AAwFd,eAAeA,QAAQ;AAAC,IAAAsB,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}