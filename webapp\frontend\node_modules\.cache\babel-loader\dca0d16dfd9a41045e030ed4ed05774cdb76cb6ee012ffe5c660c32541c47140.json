{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\PycharmProjects\\\\CMS\\\\webapp\\\\frontend\\\\src\\\\components\\\\cavi\\\\InserisciMetriForm.js\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect } from 'react';\nimport { Box, Paper, Typography, TextField, Button, Stepper, Step, StepLabel, Grid, FormControl, InputLabel, Select, MenuItem, List, ListItem, ListItemText, Divider, Alert, CircularProgress, FormHelperText, IconButton, Chip } from '@mui/material';\nimport { Search as SearchIcon, Save as SaveIcon, ArrowBack as ArrowBackIcon, ArrowForward as ArrowForwardIcon, Cancel as CancelIcon, CheckCircle as CheckCircleIcon } from '@mui/icons-material';\nimport { useNavigate } from 'react-router-dom';\nimport caviService from '../../services/caviService';\nimport parcoCaviService from '../../services/parcoCaviService';\nimport { redirectToVisualizzaCavi } from '../../utils/navigationUtils';\n\n/**\n * Componente per l'inserimento dei metri posati di un cavo\n *\n * @param {Object} props - Proprietà del componente\n * @param {string} props.cantiereId - ID del cantiere\n * @param {Function} props.onSuccess - Funzione chiamata al successo dell'operazione\n * @param {Function} props.onError - Funzione chiamata in caso di errore\n */\nimport { jsxDEV as _jsxDEV, Fragment as _Fragment } from \"react/jsx-dev-runtime\";\nconst InserisciMetriForm = ({\n  cantiereId,\n  onSuccess,\n  onError\n}) => {\n  _s();\n  const navigate = useNavigate();\n\n  // Stati per la gestione del form multi-step\n  const [activeStep, setActiveStep] = useState(0);\n  const [loading, setLoading] = useState(false);\n  const [caviLoading, setCaviLoading] = useState(false);\n  const [bobineLoading, setBobineLoading] = useState(false);\n\n  // Stati per i dati\n  const [cavi, setCavi] = useState([]);\n  const [bobine, setBobine] = useState([]);\n  const [selectedCavo, setSelectedCavo] = useState(null);\n  const [cavoIdInput, setCavoIdInput] = useState('');\n\n  // Stati per il form\n  const [formData, setFormData] = useState({\n    id_cavo: '',\n    metri_posati: '',\n    id_bobina: ''\n  });\n\n  // Stati per la validazione\n  const [formErrors, setFormErrors] = useState({});\n  const [formWarnings, setFormWarnings] = useState({});\n\n  // Definizione dei passi del form\n  const steps = ['Seleziona Cavo', 'Inserisci Metri', 'Associa Bobina', 'Conferma'];\n\n  // Carica la lista dei cavi all'avvio\n  useEffect(() => {\n    loadCavi();\n  }, [cantiereId]);\n\n  // Carica la lista delle bobine quando necessario\n  useEffect(() => {\n    if (activeStep === 2) {\n      loadBobine();\n    }\n  }, [activeStep, cantiereId]);\n\n  // Funzione per caricare i cavi\n  const loadCavi = async () => {\n    try {\n      setCaviLoading(true);\n      // Carica tutti i cavi, inclusi quelli SPARE e installati\n      const caviData = await caviService.getCavi(cantiereId);\n\n      // Mostra tutti i cavi (da installare, in corso, installati e SPARE)\n      // Questo permette di vedere anche i cavi già installati per eventuale modifica della bobina\n      setCavi(caviData);\n    } catch (error) {\n      console.error('Errore nel caricamento dei cavi:', error);\n      onError('Errore nel caricamento dei cavi: ' + (error.message || 'Errore sconosciuto'));\n    } finally {\n      setCaviLoading(false);\n    }\n  };\n\n  // Funzione per caricare le bobine\n  const loadBobine = async () => {\n    try {\n      setBobineLoading(true);\n\n      // Carica solo le bobine disponibili o in uso\n      const bobineData = await parcoCaviService.getBobine(cantiereId);\n\n      // Filtra le bobine per stato (disponibile o in uso) e per compatibilità con il cavo selezionato\n      let bobineUtilizzabili = bobineData.filter(bobina => (bobina.stato_bobina === 'Disponibile' || bobina.stato_bobina === 'In Uso') && bobina.stato_bobina !== 'Over' && bobina.stato_bobina !== 'Terminata');\n\n      // Se c'è un cavo selezionato, filtra ulteriormente per caratteristiche del cavo\n      if (selectedCavo) {\n        // Filtra per tipologia, numero conduttori e sezione se disponibili\n        if (selectedCavo.tipologia && selectedCavo.n_conduttori && selectedCavo.sezione) {\n          const bobineCompatibili = bobineUtilizzabili.filter(bobina => bobina.tipologia === selectedCavo.tipologia && bobina.n_conduttori === selectedCavo.n_conduttori && bobina.sezione === selectedCavo.sezione);\n\n          // Se ci sono bobine compatibili, usa quelle, altrimenti mostra tutte le bobine utilizzabili\n          if (bobineCompatibili.length > 0) {\n            bobineUtilizzabili = bobineCompatibili;\n          } else {\n            console.log('Nessuna bobina compatibile trovata, mostro tutte le bobine disponibili');\n          }\n        }\n\n        // Ordina le bobine per metri residui (decrescente)\n        bobineUtilizzabili.sort((a, b) => b.metri_residui - a.metri_residui);\n      }\n      setBobine(bobineUtilizzabili);\n    } catch (error) {\n      console.error('Errore nel caricamento delle bobine:', error);\n      onError('Errore nel caricamento delle bobine: ' + (error.message || 'Errore sconosciuto'));\n    } finally {\n      setBobineLoading(false);\n    }\n  };\n\n  // Gestisce la ricerca di un cavo per ID\n  const handleSearchCavoById = async () => {\n    if (!cavoIdInput.trim()) {\n      onError('Inserisci un ID cavo valido');\n      return;\n    }\n    try {\n      setCaviLoading(true);\n      const cavoData = await caviService.getCavoById(cantiereId, cavoIdInput.trim());\n\n      // Verifica se il cavo è già installato\n      if (cavoData.stato_installazione === 'Installato' || cavoData.metratura_reale && cavoData.metratura_reale > 0) {\n        // Mostra un messaggio di conferma con opzioni\n        const message = `Il cavo ${cavoData.id_cavo} risulta già posato (${cavoData.metratura_reale || 0}m).\n\nVuoi:\n1. Modificare la bobina associata (usa la funzione \"Modifica bobina cavo posato\")\n2. Selezionare un altro cavo\n3. Annullare l'operazione`;\n        const userChoice = window.confirm(message);\n        if (userChoice) {\n          // L'utente ha scelto di modificare la bobina o selezionare un altro cavo\n          // Reindirizza alla pagina di modifica bobina\n          navigate(`/dashboard/cavi/modifica-bobina/${cantiereId}/${cavoData.id_cavo}`);\n        }\n        setCaviLoading(false);\n        return;\n      }\n\n      // Verifica se il cavo è SPARE\n      if (cavoData.modificato_manualmente === 3) {\n        // Gestione del cavo SPARE - verrà gestito in handleCavoSelect\n      }\n\n      // Seleziona il cavo e passa al passo successivo\n      handleCavoSelect(cavoData);\n    } catch (error) {\n      console.error('Errore nella ricerca del cavo:', error);\n      onError('Cavo non trovato o errore nella ricerca: ' + (error.message || 'Errore sconosciuto'));\n    } finally {\n      setCaviLoading(false);\n    }\n  };\n\n  // Gestisce la selezione di un cavo\n  const handleCavoSelect = cavo => {\n    // Verifica se il cavo è già installato\n    if (cavo.stato_installazione === 'Installato' || cavo.metratura_reale && cavo.metratura_reale > 0) {\n      // Mostra un messaggio di conferma con opzioni\n      const message = `Il cavo ${cavo.id_cavo} risulta già posato (${cavo.metratura_reale || 0}m).\n\nVuoi:\n1. Modificare la bobina associata (usa la funzione \"Modifica bobina cavo posato\")\n2. Selezionare un altro cavo\n3. Annullare l'operazione`;\n      const userChoice = window.confirm(message);\n      if (userChoice) {\n        // L'utente ha scelto di modificare la bobina o selezionare un altro cavo\n        // Reindirizza alla pagina di modifica bobina\n        navigate(`/dashboard/cavi/modifica-bobina/${cantiereId}/${cavo.id_cavo}`);\n      }\n      return;\n    }\n    // Verifica se il cavo è SPARE (modificato_manualmente = 3)\n    else if (cavo.modificato_manualmente === 3) {\n      // Chiedi conferma all'utente per riattivare il cavo SPARE\n      if (window.confirm(`Il cavo ${cavo.id_cavo} è marcato come SPARE. Vuoi riattivarlo?`)) {\n        // Riattiva il cavo SPARE (imposta modificato_manualmente = 0)\n        reactivateSpare(cavo.id_cavo).then(() => {\n          // Aggiorna il cavo selezionato con modificato_manualmente = 0\n          const updatedCavo = {\n            ...cavo,\n            modificato_manualmente: 0\n          };\n          setSelectedCavo(updatedCavo);\n          setFormData({\n            ...formData,\n            id_cavo: updatedCavo.id_cavo,\n            metri_posati: ''\n          });\n          // Passa al passo successivo\n          setActiveStep(1);\n        }).catch(error => {\n          console.error('Errore durante la riattivazione del cavo SPARE:', error);\n          onError('Errore durante la riattivazione del cavo SPARE: ' + (error.message || 'Errore sconosciuto'));\n        });\n      } else {\n        // L'utente ha annullato la riattivazione\n        return;\n      }\n    } else {\n      // Cavo normale (non SPARE e non installato)\n      setSelectedCavo(cavo);\n      setFormData({\n        ...formData,\n        id_cavo: cavo.id_cavo,\n        metri_posati: ''\n      });\n      // Passa al passo successivo\n      setActiveStep(1);\n    }\n  };\n\n  // Funzione per riattivare un cavo SPARE\n  const reactivateSpare = async cavoId => {\n    try {\n      // Chiamata API per riattivare il cavo SPARE\n      await caviService.reactivateSpare(cantiereId, cavoId);\n      onSuccess(`Cavo ${cavoId} riattivato con successo`);\n      return true;\n    } catch (error) {\n      console.error('Errore durante la riattivazione del cavo SPARE:', error);\n      onError('Errore durante la riattivazione del cavo SPARE: ' + (error.message || 'Errore sconosciuto'));\n      throw error;\n    }\n  };\n\n  // Gestisce il cambio dei valori nel form\n  const handleFormChange = e => {\n    const {\n      name,\n      value\n    } = e.target;\n    setFormData({\n      ...formData,\n      [name]: value\n    });\n\n    // Validazione in tempo reale\n    validateField(name, value);\n  };\n\n  // Validazione di un campo\n  const validateField = (name, value) => {\n    let error = null;\n    let warning = null;\n    if (name === 'metri_posati') {\n      if (!value || value.trim() === '') {\n        error = 'Inserire un valore per i metri posati';\n      } else if (isNaN(parseFloat(value)) || parseFloat(value) <= 0) {\n        error = 'Inserire un valore numerico positivo';\n      } else if (selectedCavo && selectedCavo.metri_teorici && parseFloat(value) > parseFloat(selectedCavo.metri_teorici)) {\n        warning = 'I metri posati superano i metri teorici del cavo';\n      }\n    }\n\n    // Aggiorna gli errori\n    setFormErrors(prev => ({\n      ...prev,\n      [name]: error\n    }));\n\n    // Aggiorna gli avvisi\n    setFormWarnings(prev => ({\n      ...prev,\n      [name]: warning\n    }));\n    return !error;\n  };\n\n  // Validazione completa del form\n  const validateForm = () => {\n    let isValid = true;\n    const errors = {};\n\n    // Validazione metri posati\n    if (!formData.metri_posati || formData.metri_posati.trim() === '') {\n      errors.metri_posati = 'Inserire un valore per i metri posati';\n      isValid = false;\n    } else if (isNaN(parseFloat(formData.metri_posati)) || parseFloat(formData.metri_posati) <= 0) {\n      errors.metri_posati = 'Inserire un valore numerico positivo';\n      isValid = false;\n    }\n    setFormErrors(errors);\n    return isValid;\n  };\n\n  // Gestisce il passaggio al passo successivo\n  const handleNext = () => {\n    if (activeStep === 1) {\n      // Validazione prima di passare al passo successivo\n      if (!validateForm()) {\n        return;\n      }\n    }\n    setActiveStep(prevActiveStep => prevActiveStep + 1);\n  };\n\n  // Gestisce il ritorno al passo precedente\n  const handleBack = () => {\n    setActiveStep(prevActiveStep => prevActiveStep - 1);\n  };\n\n  // Gestisce il reset del form\n  const handleReset = () => {\n    setActiveStep(0);\n    setSelectedCavo(null);\n    setCavoIdInput('');\n    setFormData({\n      id_cavo: '',\n      metri_posati: '',\n      id_bobina: ''\n    });\n    setFormErrors({});\n    setFormWarnings({});\n  };\n\n  // Determina lo stato di installazione in base ai metri posati\n  const determineInstallationStatus = (metriPosati, metriTeorici) => {\n    if (!metriPosati || parseFloat(metriPosati) <= 0) {\n      return 'Da installare';\n    }\n    if (parseFloat(metriPosati) >= parseFloat(metriTeorici)) {\n      return 'Installato';\n    }\n    return 'In corso';\n  };\n\n  // Gestisce l'invio del form\n  const handleSubmit = async () => {\n    try {\n      setLoading(true);\n\n      // Validazione finale\n      if (!validateForm()) {\n        setLoading(false);\n        return;\n      }\n\n      // Prepara i dati da inviare\n      const metriPosati = parseFloat(formData.metri_posati);\n      const idBobina = formData.id_bobina || null;\n\n      // Determina lo stato di installazione\n      const statoInstallazione = determineInstallationStatus(metriPosati, selectedCavo.metri_teorici);\n\n      // Log per debug\n      console.log('Invio dati:', {\n        cantiereId,\n        cavoId: formData.id_cavo,\n        metriPosati,\n        idBobina,\n        statoInstallazione\n      });\n\n      // Chiamata API\n      await caviService.updateMetriPosati(cantiereId, formData.id_cavo, metriPosati, idBobina);\n\n      // Gestione successo\n      onSuccess(`Metri posati aggiornati con successo. Stato cavo: ${statoInstallazione}`);\n\n      // Reset del form\n      handleReset();\n\n      // Ricarica i cavi\n      loadCavi();\n    } catch (error) {\n      console.error('Errore durante l\\'aggiornamento dei metri posati:', error);\n      onError('Errore durante l\\'aggiornamento dei metri posati: ' + (error.message || 'Errore sconosciuto'));\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  // Renderizza il passo 1: Selezione del cavo\n  const renderStep1 = () => {\n    return /*#__PURE__*/_jsxDEV(Box, {\n      children: [/*#__PURE__*/_jsxDEV(Typography, {\n        variant: \"h6\",\n        gutterBottom: true,\n        children: \"Seleziona un cavo\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 431,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Paper, {\n        sx: {\n          p: 2,\n          mb: 3\n        },\n        children: [/*#__PURE__*/_jsxDEV(Typography, {\n          variant: \"subtitle1\",\n          gutterBottom: true,\n          children: \"Cerca cavo per ID\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 437,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Grid, {\n          container: true,\n          spacing: 2,\n          alignItems: \"center\",\n          children: [/*#__PURE__*/_jsxDEV(Grid, {\n            item: true,\n            xs: 9,\n            children: /*#__PURE__*/_jsxDEV(TextField, {\n              fullWidth: true,\n              label: \"ID Cavo\",\n              variant: \"outlined\",\n              value: cavoIdInput,\n              onChange: e => setCavoIdInput(e.target.value),\n              placeholder: \"Inserisci l'ID del cavo\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 442,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 441,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Grid, {\n            item: true,\n            xs: 3,\n            children: /*#__PURE__*/_jsxDEV(Button, {\n              fullWidth: true,\n              variant: \"contained\",\n              color: \"primary\",\n              onClick: handleSearchCavoById,\n              disabled: caviLoading || !cavoIdInput.trim(),\n              startIcon: caviLoading ? /*#__PURE__*/_jsxDEV(CircularProgress, {\n                size: 20\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 458,\n                columnNumber: 42\n              }, this) : /*#__PURE__*/_jsxDEV(SearchIcon, {}, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 458,\n                columnNumber: 75\n              }, this),\n              children: \"Cerca\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 452,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 451,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 440,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 436,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Paper, {\n        sx: {\n          p: 2\n        },\n        children: [/*#__PURE__*/_jsxDEV(Typography, {\n          variant: \"subtitle1\",\n          gutterBottom: true,\n          children: \"Seleziona dalla lista\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 468,\n          columnNumber: 11\n        }, this), caviLoading ? /*#__PURE__*/_jsxDEV(Box, {\n          sx: {\n            display: 'flex',\n            justifyContent: 'center',\n            my: 4\n          },\n          children: /*#__PURE__*/_jsxDEV(CircularProgress, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 474,\n            columnNumber: 15\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 473,\n          columnNumber: 13\n        }, this) : cavi.length === 0 ? /*#__PURE__*/_jsxDEV(Alert, {\n          severity: \"info\",\n          children: \"Non ci sono cavi disponibili da installare.\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 477,\n          columnNumber: 13\n        }, this) : /*#__PURE__*/_jsxDEV(List, {\n          sx: {\n            maxHeight: '400px',\n            overflow: 'auto'\n          },\n          children: cavi.map(cavo => /*#__PURE__*/_jsxDEV(React.Fragment, {\n            children: [/*#__PURE__*/_jsxDEV(ListItem, {\n              button: true,\n              onClick: () => handleCavoSelect(cavo),\n              children: /*#__PURE__*/_jsxDEV(ListItemText, {\n                primary: /*#__PURE__*/_jsxDEV(Box, {\n                  sx: {\n                    display: 'flex',\n                    alignItems: 'center'\n                  },\n                  children: [/*#__PURE__*/_jsxDEV(Typography, {\n                    variant: \"subtitle1\",\n                    children: cavo.id_cavo\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 488,\n                    columnNumber: 27\n                  }, this), cavo.modificato_manualmente === 3 ? /*#__PURE__*/_jsxDEV(Chip, {\n                    size: \"small\",\n                    label: \"SPARE\",\n                    color: \"error\",\n                    sx: {\n                      ml: 1\n                    }\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 490,\n                    columnNumber: 29\n                  }, this) : cavo.stato_installazione === 'Installato' || cavo.metratura_reale && cavo.metratura_reale > 0 ? /*#__PURE__*/_jsxDEV(Chip, {\n                    size: \"small\",\n                    label: \"Installato\",\n                    color: \"success\",\n                    sx: {\n                      ml: 1\n                    }\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 497,\n                    columnNumber: 29\n                  }, this) : /*#__PURE__*/_jsxDEV(Chip, {\n                    size: \"small\",\n                    label: cavo.stato_installazione,\n                    color: cavo.stato_installazione === 'In corso' ? 'warning' : 'default',\n                    sx: {\n                      ml: 1\n                    }\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 504,\n                    columnNumber: 29\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 487,\n                  columnNumber: 25\n                }, this),\n                secondary: /*#__PURE__*/_jsxDEV(_Fragment, {\n                  children: [/*#__PURE__*/_jsxDEV(Typography, {\n                    variant: \"body2\",\n                    component: \"span\",\n                    children: [cavo.tipologia || 'N/A', \" - \", cavo.n_conduttori || 'N/A', \" x \", cavo.sezione || 'N/A']\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 515,\n                    columnNumber: 27\n                  }, this), /*#__PURE__*/_jsxDEV(\"br\", {}, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 518,\n                    columnNumber: 27\n                  }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                    variant: \"body2\",\n                    component: \"span\",\n                    children: [\"Da: \", cavo.ubicazione_partenza || 'N/A', \" - A: \", cavo.ubicazione_arrivo || 'N/A']\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 519,\n                    columnNumber: 27\n                  }, this), /*#__PURE__*/_jsxDEV(\"br\", {}, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 522,\n                    columnNumber: 27\n                  }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                    variant: \"body2\",\n                    component: \"span\",\n                    children: [\"Metri teorici: \", cavo.metri_teorici || 'N/A', \" - Metri posati: \", cavo.metratura_reale || '0']\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 523,\n                    columnNumber: 27\n                  }, this)]\n                }, void 0, true)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 485,\n                columnNumber: 21\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 484,\n              columnNumber: 19\n            }, this), /*#__PURE__*/_jsxDEV(Divider, {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 530,\n              columnNumber: 19\n            }, this)]\n          }, cavo.id_cavo, true, {\n            fileName: _jsxFileName,\n            lineNumber: 483,\n            columnNumber: 17\n          }, this))\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 481,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 467,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 430,\n      columnNumber: 7\n    }, this);\n  };\n\n  // Renderizza il passo 2: Inserimento metri\n  const renderStep2 = () => {\n    if (!selectedCavo) return null;\n    return /*#__PURE__*/_jsxDEV(Box, {\n      children: [/*#__PURE__*/_jsxDEV(Typography, {\n        variant: \"h6\",\n        gutterBottom: true,\n        children: \"Inserisci metri posati\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 546,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Paper, {\n        sx: {\n          p: 3,\n          mb: 3\n        },\n        children: [/*#__PURE__*/_jsxDEV(Typography, {\n          variant: \"subtitle1\",\n          gutterBottom: true,\n          children: \"Dettagli del cavo selezionato\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 551,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Grid, {\n          container: true,\n          spacing: 2,\n          children: [/*#__PURE__*/_jsxDEV(Grid, {\n            item: true,\n            xs: 12,\n            md: 6,\n            children: [/*#__PURE__*/_jsxDEV(Typography, {\n              variant: \"body1\",\n              children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n                children: \"ID Cavo:\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 558,\n                columnNumber: 17\n              }, this), \" \", selectedCavo.id_cavo]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 557,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Typography, {\n              variant: \"body1\",\n              children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n                children: \"Tipologia:\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 561,\n                columnNumber: 17\n              }, this), \" \", selectedCavo.tipologia || 'N/A']\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 560,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Typography, {\n              variant: \"body1\",\n              children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n                children: \"Conduttori:\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 564,\n                columnNumber: 17\n              }, this), \" \", selectedCavo.n_conduttori || 'N/A', \" x \", selectedCavo.sezione || 'N/A']\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 563,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 556,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Grid, {\n            item: true,\n            xs: 12,\n            md: 6,\n            children: [/*#__PURE__*/_jsxDEV(Typography, {\n              variant: \"body1\",\n              children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n                children: \"Ubicazione Partenza:\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 569,\n                columnNumber: 17\n              }, this), \" \", selectedCavo.ubicazione_partenza || 'N/A']\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 568,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Typography, {\n              variant: \"body1\",\n              children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n                children: \"Ubicazione Arrivo:\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 572,\n                columnNumber: 17\n              }, this), \" \", selectedCavo.ubicazione_arrivo || 'N/A']\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 571,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Typography, {\n              variant: \"body1\",\n              children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n                children: \"Metri Teorici:\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 575,\n                columnNumber: 17\n              }, this), \" \", selectedCavo.metri_teorici || 'N/A']\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 574,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 567,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 555,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 550,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Paper, {\n        sx: {\n          p: 3\n        },\n        children: [/*#__PURE__*/_jsxDEV(Typography, {\n          variant: \"subtitle1\",\n          gutterBottom: true,\n          children: \"Inserisci i metri posati\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 582,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(TextField, {\n          fullWidth: true,\n          label: \"Metri posati\",\n          variant: \"outlined\",\n          name: \"metri_posati\",\n          type: \"number\",\n          value: formData.metri_posati,\n          onChange: handleFormChange,\n          error: !!formErrors.metri_posati,\n          helperText: formErrors.metri_posati || formWarnings.metri_posati,\n          FormHelperTextProps: {\n            sx: {\n              color: formWarnings.metri_posati && !formErrors.metri_posati ? 'warning.main' : 'error.main'\n            }\n          },\n          sx: {\n            mb: 2\n          }\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 586,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Alert, {\n          severity: \"info\",\n          sx: {\n            mt: 2\n          },\n          children: \"Inserisci i metri di cavo effettivamente posati. Questo valore aggiorna lo stato di installazione del cavo.\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 602,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 581,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 545,\n      columnNumber: 7\n    }, this);\n  };\n\n  // Renderizza il passo 3: Associazione bobina\n  const renderStep3 = () => {\n    // Funzione per estrarre il numero della bobina dall'ID completo\n    const getBobinaNumber = idBobina => {\n      // L'ID completo è nel formato C{id_cantiere}_B{numero_bobina}\n      if (idBobina && idBobina.includes('_B')) {\n        return idBobina.split('_B')[1];\n      }\n      return idBobina;\n    };\n\n    // Funzione per costruire l'ID completo della bobina\n    const buildFullBobinaId = numeroBobina => {\n      return `C${cantiereId}_B${numeroBobina}`;\n    };\n\n    // Verifica se una bobina ha metri residui sufficienti\n    const hasSufficientMeters = bobina => {\n      if (!bobina || !formData.metri_posati) return true;\n      return parseFloat(bobina.metri_residui) >= parseFloat(formData.metri_posati);\n    };\n\n    // Gestisce l'input diretto del numero della bobina\n    const handleBobinaNumberInput = e => {\n      const numeroBobina = e.target.value.trim();\n\n      // Gestione opzioni speciali\n      if (numeroBobina.toLowerCase() === 'v') {\n        // Opzione 'v' per bobina vuota\n        setFormData({\n          ...formData,\n          id_bobina: 'BOBINA_VUOTA'\n        });\n        setFormErrors({\n          ...formErrors,\n          id_bobina_input: null\n        });\n        return;\n      }\n      if (numeroBobina.toLowerCase() === 'q') {\n        // Opzione 'q' per annullare\n        setFormData({\n          ...formData,\n          id_bobina: ''\n        });\n        setFormErrors({\n          ...formErrors,\n          id_bobina_input: null\n        });\n        return;\n      }\n      if (numeroBobina) {\n        // Costruisci l'ID completo\n        const idBobinaCompleto = buildFullBobinaId(numeroBobina);\n\n        // Verifica se la bobina esiste\n        const bobinaEsistente = bobine.find(b => b.id_bobina === idBobinaCompleto);\n        if (bobinaEsistente) {\n          // Verifica se la bobina è in stato OVER o TERMINATA\n          if (bobinaEsistente.stato_bobina === 'Over' || bobinaEsistente.stato_bobina === 'Terminata') {\n            setFormErrors({\n              ...formErrors,\n              id_bobina_input: `La bobina ${numeroBobina} è in stato ${bobinaEsistente.stato_bobina} e non può essere utilizzata`\n            });\n            return;\n          }\n\n          // Verifica se la bobina ha metri residui sufficienti\n          if (hasSufficientMeters(bobinaEsistente)) {\n            // Se la bobina esiste e ha metri sufficienti, imposta l'ID completo\n            setFormData({\n              ...formData,\n              id_bobina: idBobinaCompleto\n            });\n            setFormErrors({\n              ...formErrors,\n              id_bobina_input: null\n            });\n          } else {\n            // Se la bobina non ha metri sufficienti, mostra un avviso\n            setFormErrors({\n              ...formErrors,\n              id_bobina_input: `La bobina ${numeroBobina} non ha metri residui sufficienti (${bobinaEsistente.metri_residui} m disponibili)`\n            });\n          }\n        } else {\n          // Se la bobina non esiste, mostra un errore\n          setFormErrors({\n            ...formErrors,\n            id_bobina_input: `Bobina ${numeroBobina} non trovata`\n          });\n        }\n      } else {\n        // Se l'input è vuoto, resetta l'ID bobina\n        setFormData({\n          ...formData,\n          id_bobina: ''\n        });\n        setFormErrors({\n          ...formErrors,\n          id_bobina_input: null\n        });\n      }\n    };\n    return /*#__PURE__*/_jsxDEV(Box, {\n      children: [/*#__PURE__*/_jsxDEV(Typography, {\n        variant: \"h6\",\n        gutterBottom: true,\n        children: \"Associa bobina (opzionale)\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 720,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Paper, {\n        sx: {\n          p: 3\n        },\n        children: [/*#__PURE__*/_jsxDEV(Typography, {\n          variant: \"body1\",\n          paragraph: true,\n          children: \"Puoi associare una bobina al cavo selezionato. Questo \\xE8 opzionale, ma consigliato per tenere traccia dell'utilizzo delle bobine.\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 725,\n          columnNumber: 11\n        }, this), bobineLoading ? /*#__PURE__*/_jsxDEV(Box, {\n          sx: {\n            display: 'flex',\n            justifyContent: 'center',\n            my: 4\n          },\n          children: /*#__PURE__*/_jsxDEV(CircularProgress, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 731,\n            columnNumber: 15\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 730,\n          columnNumber: 13\n        }, this) : /*#__PURE__*/_jsxDEV(Box, {\n          children: [/*#__PURE__*/_jsxDEV(Grid, {\n            container: true,\n            spacing: 2,\n            sx: {\n              mb: 3\n            },\n            children: [/*#__PURE__*/_jsxDEV(Grid, {\n              item: true,\n              xs: 12,\n              md: 6,\n              children: /*#__PURE__*/_jsxDEV(TextField, {\n                fullWidth: true,\n                label: \"Inserisci numero bobina\",\n                variant: \"outlined\",\n                placeholder: \"Inserisci solo il numero (Y) o 'v' per bobina vuota\",\n                helperText: formErrors.id_bobina_input || \"Inserisci solo il numero della bobina (parte Y del codice Cx_By), 'v' per bobina vuota o 'q' per annullare\",\n                error: !!formErrors.id_bobina_input,\n                onBlur: handleBobinaNumberInput\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 738,\n                columnNumber: 19\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 737,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(Grid, {\n              item: true,\n              xs: 12,\n              md: 6,\n              children: /*#__PURE__*/_jsxDEV(Typography, {\n                variant: \"body2\",\n                sx: {\n                  mt: 1\n                },\n                children: [\"ID Bobina completo: \", formData.id_bobina === 'BOBINA_VUOTA' ? 'BOBINA VUOTA' : formData.id_bobina || '-']\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 749,\n                columnNumber: 19\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 748,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 736,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(Typography, {\n            variant: \"subtitle1\",\n            gutterBottom: true,\n            sx: {\n              mt: 3,\n              fontWeight: 'bold'\n            },\n            children: \"Bobine disponibili compatibili con il cavo selezionato\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 756,\n            columnNumber: 15\n          }, this), bobine.length > 0 ? /*#__PURE__*/_jsxDEV(Box, {\n            sx: {\n              overflowX: 'auto'\n            },\n            children: /*#__PURE__*/_jsxDEV(\"table\", {\n              style: {\n                width: '100%',\n                borderCollapse: 'collapse',\n                marginBottom: '20px'\n              },\n              children: [/*#__PURE__*/_jsxDEV(\"thead\", {\n                children: /*#__PURE__*/_jsxDEV(\"tr\", {\n                  style: {\n                    borderBottom: '2px solid #ddd',\n                    backgroundColor: '#f5f5f5'\n                  },\n                  children: [/*#__PURE__*/_jsxDEV(\"th\", {\n                    style: {\n                      padding: '8px',\n                      textAlign: 'left'\n                    },\n                    children: \"ID\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 765,\n                    columnNumber: 25\n                  }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n                    style: {\n                      padding: '8px',\n                      textAlign: 'left'\n                    },\n                    children: \"Tipologia\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 766,\n                    columnNumber: 25\n                  }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n                    style: {\n                      padding: '8px',\n                      textAlign: 'left'\n                    },\n                    children: \"Cond.\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 767,\n                    columnNumber: 25\n                  }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n                    style: {\n                      padding: '8px',\n                      textAlign: 'left'\n                    },\n                    children: \"Sezione\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 768,\n                    columnNumber: 25\n                  }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n                    style: {\n                      padding: '8px',\n                      textAlign: 'right'\n                    },\n                    children: \"Residui\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 769,\n                    columnNumber: 25\n                  }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n                    style: {\n                      padding: '8px',\n                      textAlign: 'left'\n                    },\n                    children: \"Stato\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 770,\n                    columnNumber: 25\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 764,\n                  columnNumber: 23\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 763,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(\"tbody\", {\n                children: bobine.map(bobina => {\n                  const isCompatible = selectedCavo && bobina.tipologia === selectedCavo.tipologia && bobina.n_conduttori === selectedCavo.n_conduttori && bobina.sezione === selectedCavo.sezione;\n                  const hasSufficient = bobina.metri_residui >= parseFloat(formData.metri_posati || 0);\n                  return /*#__PURE__*/_jsxDEV(\"tr\", {\n                    style: {\n                      borderBottom: '1px solid #ddd',\n                      backgroundColor: isCompatible ? hasSufficient ? '#e8f5e9' : '#fff8e1' : 'transparent',\n                      cursor: 'pointer'\n                    },\n                    onClick: () => {\n                      if (hasSufficient) {\n                        setFormData({\n                          ...formData,\n                          id_bobina: bobina.id_bobina\n                        });\n                      }\n                    },\n                    children: [/*#__PURE__*/_jsxDEV(\"td\", {\n                      style: {\n                        padding: '8px'\n                      },\n                      children: getBobinaNumber(bobina.id_bobina)\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 799,\n                      columnNumber: 29\n                    }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                      style: {\n                        padding: '8px'\n                      },\n                      children: bobina.tipologia || 'N/A'\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 800,\n                      columnNumber: 29\n                    }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                      style: {\n                        padding: '8px'\n                      },\n                      children: bobina.n_conduttori || 'N/A'\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 801,\n                      columnNumber: 29\n                    }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                      style: {\n                        padding: '8px'\n                      },\n                      children: bobina.sezione || 'N/A'\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 802,\n                      columnNumber: 29\n                    }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                      style: {\n                        padding: '8px',\n                        textAlign: 'right'\n                      },\n                      children: [bobina.metri_residui || 0, \" m\"]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 803,\n                      columnNumber: 29\n                    }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                      style: {\n                        padding: '8px'\n                      },\n                      children: /*#__PURE__*/_jsxDEV(Chip, {\n                        size: \"small\",\n                        label: bobina.stato_bobina,\n                        color: bobina.stato_bobina === 'Disponibile' ? 'success' : bobina.stato_bobina === 'In uso' ? 'primary' : bobina.stato_bobina === 'Over' ? 'error' : bobina.stato_bobina === 'Terminata' ? 'warning' : 'default'\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 805,\n                        columnNumber: 31\n                      }, this)\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 804,\n                      columnNumber: 29\n                    }, this)]\n                  }, bobina.id_bobina, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 783,\n                    columnNumber: 27\n                  }, this);\n                })\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 773,\n                columnNumber: 21\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 762,\n              columnNumber: 19\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 761,\n            columnNumber: 17\n          }, this) : /*#__PURE__*/_jsxDEV(Alert, {\n            severity: \"info\",\n            sx: {\n              mt: 2,\n              mb: 3\n            },\n            children: \"Non ci sono bobine disponibili compatibili con il cavo selezionato.\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 823,\n            columnNumber: 17\n          }, this), /*#__PURE__*/_jsxDEV(Box, {\n            sx: {\n              mt: 3,\n              mb: 3\n            },\n            children: [/*#__PURE__*/_jsxDEV(Typography, {\n              variant: \"subtitle2\",\n              gutterBottom: true,\n              children: \"Opzioni speciali:\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 830,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(Grid, {\n              container: true,\n              spacing: 2,\n              children: [/*#__PURE__*/_jsxDEV(Grid, {\n                item: true,\n                children: /*#__PURE__*/_jsxDEV(Button, {\n                  variant: \"outlined\",\n                  onClick: () => {\n                    setFormData({\n                      ...formData,\n                      id_bobina: 'BOBINA_VUOTA'\n                    });\n                  },\n                  children: \"Bobina Vuota (v)\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 835,\n                  columnNumber: 21\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 834,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(Grid, {\n                item: true,\n                children: /*#__PURE__*/_jsxDEV(Button, {\n                  variant: \"outlined\",\n                  color: \"secondary\",\n                  onClick: () => {\n                    setFormData({\n                      ...formData,\n                      id_bobina: ''\n                    });\n                  },\n                  children: \"Nessuna Bobina (q)\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 848,\n                  columnNumber: 21\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 847,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 833,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 829,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(FormControl, {\n            fullWidth: true,\n            children: [/*#__PURE__*/_jsxDEV(InputLabel, {\n              id: \"bobina-select-label\",\n              children: \"Seleziona Bobina dalla lista\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 866,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(Select, {\n              labelId: \"bobina-select-label\",\n              id: \"bobina-select\",\n              name: \"id_bobina\",\n              value: formData.id_bobina,\n              label: \"Seleziona Bobina dalla lista\",\n              onChange: handleFormChange,\n              children: [/*#__PURE__*/_jsxDEV(MenuItem, {\n                value: \"\",\n                children: /*#__PURE__*/_jsxDEV(\"em\", {\n                  children: \"Nessuna bobina\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 876,\n                  columnNumber: 21\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 875,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(MenuItem, {\n                value: \"BOBINA_VUOTA\",\n                children: /*#__PURE__*/_jsxDEV(\"em\", {\n                  children: \"BOBINA VUOTA\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 879,\n                  columnNumber: 21\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 878,\n                columnNumber: 19\n              }, this), bobine.map(bobina => /*#__PURE__*/_jsxDEV(MenuItem, {\n                value: bobina.id_bobina,\n                disabled: bobina.metri_residui < parseFloat(formData.metri_posati),\n                children: [getBobinaNumber(bobina.id_bobina), \" - \", bobina.tipologia || 'N/A', \" - Residui: \", bobina.metri_residui || 0, \" m\"]\n              }, bobina.id_bobina, true, {\n                fileName: _jsxFileName,\n                lineNumber: 882,\n                columnNumber: 21\n              }, this))]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 867,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(FormHelperText, {\n              children: \"Seleziona una bobina con metri residui sufficienti o lascia vuoto per non associare alcuna bobina\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 891,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 865,\n            columnNumber: 15\n          }, this), formData.id_bobina && /*#__PURE__*/_jsxDEV(Box, {\n            sx: {\n              mt: 3,\n              p: 2,\n              bgcolor: 'background.paper',\n              borderRadius: 1,\n              border: '1px solid #e0e0e0'\n            },\n            children: [/*#__PURE__*/_jsxDEV(Typography, {\n              variant: \"subtitle2\",\n              gutterBottom: true,\n              children: \"Dettagli bobina selezionata\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 899,\n              columnNumber: 19\n            }, this), (() => {\n              const bobina = bobine.find(b => b.id_bobina === formData.id_bobina);\n              if (bobina) {\n                return /*#__PURE__*/_jsxDEV(Grid, {\n                  container: true,\n                  spacing: 2,\n                  children: [/*#__PURE__*/_jsxDEV(Grid, {\n                    item: true,\n                    xs: 12,\n                    md: 6,\n                    children: [/*#__PURE__*/_jsxDEV(Typography, {\n                      variant: \"body2\",\n                      children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n                        children: \"Numero:\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 909,\n                        columnNumber: 31\n                      }, this), \" \", getBobinaNumber(bobina.id_bobina)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 908,\n                      columnNumber: 29\n                    }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                      variant: \"body2\",\n                      children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n                        children: \"Tipologia:\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 912,\n                        columnNumber: 31\n                      }, this), \" \", bobina.tipologia || 'N/A']\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 911,\n                      columnNumber: 29\n                    }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                      variant: \"body2\",\n                      children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n                        children: \"Conduttori:\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 915,\n                        columnNumber: 31\n                      }, this), \" \", bobina.n_conduttori || 'N/A', \" x \", bobina.sezione || 'N/A']\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 914,\n                      columnNumber: 29\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 907,\n                    columnNumber: 27\n                  }, this), /*#__PURE__*/_jsxDEV(Grid, {\n                    item: true,\n                    xs: 12,\n                    md: 6,\n                    children: [/*#__PURE__*/_jsxDEV(Typography, {\n                      variant: \"body2\",\n                      children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n                        children: \"Metri totali:\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 920,\n                        columnNumber: 31\n                      }, this), \" \", bobina.metri_totali || 0, \" m\"]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 919,\n                      columnNumber: 29\n                    }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                      variant: \"body2\",\n                      children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n                        children: \"Metri residui:\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 923,\n                        columnNumber: 31\n                      }, this), \" \", bobina.metri_residui || 0, \" m\"]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 922,\n                      columnNumber: 29\n                    }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                      variant: \"body2\",\n                      children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n                        children: \"Stato:\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 926,\n                        columnNumber: 31\n                      }, this), \" \", bobina.stato_bobina || 'N/A']\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 925,\n                      columnNumber: 29\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 918,\n                    columnNumber: 27\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 906,\n                  columnNumber: 25\n                }, this);\n              }\n              return /*#__PURE__*/_jsxDEV(Typography, {\n                variant: \"body2\",\n                color: \"error\",\n                children: \"Bobina non trovata nel database\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 933,\n                columnNumber: 23\n              }, this);\n            })()]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 898,\n            columnNumber: 17\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 734,\n          columnNumber: 13\n        }, this), bobine.length === 0 && !bobineLoading && /*#__PURE__*/_jsxDEV(Alert, {\n          severity: \"warning\",\n          sx: {\n            mt: 2\n          },\n          children: \"Non ci sono bobine disponibili. Puoi procedere senza associare una bobina o aggiungere prima una nuova bobina.\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 944,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 724,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 719,\n      columnNumber: 7\n    }, this);\n  };\n\n  // Renderizza il passo 4: Conferma\n  const renderStep4 = () => {\n    // Funzione per estrarre il numero della bobina dall'ID completo\n    const getBobinaNumber = idBobina => {\n      // L'ID completo è nel formato C{id_cantiere}_B{numero_bobina}\n      if (idBobina && idBobina.includes('_B')) {\n        return idBobina.split('_B')[1];\n      }\n      return idBobina;\n    };\n\n    // Ottieni il numero della bobina se presente\n    const numeroBobina = formData.id_bobina ? getBobinaNumber(formData.id_bobina) : 'Nessuna';\n    return /*#__PURE__*/_jsxDEV(Box, {\n      children: [/*#__PURE__*/_jsxDEV(Typography, {\n        variant: \"h6\",\n        gutterBottom: true,\n        children: \"Conferma inserimento\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 969,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Paper, {\n        sx: {\n          p: 3\n        },\n        children: [/*#__PURE__*/_jsxDEV(Typography, {\n          variant: \"subtitle1\",\n          gutterBottom: true,\n          children: \"Riepilogo dati\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 974,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Grid, {\n          container: true,\n          spacing: 2,\n          children: [/*#__PURE__*/_jsxDEV(Grid, {\n            item: true,\n            xs: 12,\n            md: 6,\n            children: [/*#__PURE__*/_jsxDEV(Typography, {\n              variant: \"body1\",\n              children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n                children: \"ID Cavo:\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 981,\n                columnNumber: 17\n              }, this), \" \", selectedCavo.id_cavo]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 980,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Typography, {\n              variant: \"body1\",\n              children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n                children: \"Tipologia:\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 984,\n                columnNumber: 17\n              }, this), \" \", selectedCavo.tipologia || 'N/A']\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 983,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Typography, {\n              variant: \"body1\",\n              children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n                children: \"Conduttori:\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 987,\n                columnNumber: 17\n              }, this), \" \", selectedCavo.n_conduttori || 'N/A', \" x \", selectedCavo.sezione || 'N/A']\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 986,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 979,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Grid, {\n            item: true,\n            xs: 12,\n            md: 6,\n            children: [/*#__PURE__*/_jsxDEV(Typography, {\n              variant: \"body1\",\n              children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n                children: \"Metri Posati:\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 992,\n                columnNumber: 17\n              }, this), \" \", formData.metri_posati]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 991,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Typography, {\n              variant: \"body1\",\n              children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n                children: \"Bobina Associata:\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 995,\n                columnNumber: 17\n              }, this), \" \", numeroBobina]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 994,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Typography, {\n              variant: \"body1\",\n              children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n                children: \"Stato Installazione:\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 998,\n                columnNumber: 17\n              }, this), \" \", parseFloat(formData.metri_posati) >= parseFloat(selectedCavo.metri_teorici) ? 'Installato' : 'In corso']\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 997,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 990,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 978,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Alert, {\n          severity: \"info\",\n          sx: {\n            mt: 3\n          },\n          children: [\"Conferma per aggiornare i metri posati e lo stato di installazione del cavo.\", formData.id_bobina && ' I metri posati verranno sottratti dai metri residui della bobina selezionata.']\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 1003,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 973,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 968,\n      columnNumber: 7\n    }, this);\n  };\n\n  // Renderizza il contenuto in base al passo attivo\n  const getStepContent = step => {\n    switch (step) {\n      case 0:\n        return renderStep1();\n      case 1:\n        return renderStep2();\n      case 2:\n        return renderStep3();\n      case 3:\n        return renderStep4();\n      default:\n        return 'Passo sconosciuto';\n    }\n  };\n  return /*#__PURE__*/_jsxDEV(Box, {\n    children: [/*#__PURE__*/_jsxDEV(Stepper, {\n      activeStep: activeStep,\n      sx: {\n        mb: 4\n      },\n      children: steps.map(label => /*#__PURE__*/_jsxDEV(Step, {\n        children: /*#__PURE__*/_jsxDEV(StepLabel, {\n          children: label\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 1033,\n          columnNumber: 13\n        }, this)\n      }, label, false, {\n        fileName: _jsxFileName,\n        lineNumber: 1032,\n        columnNumber: 11\n      }, this))\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 1030,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Box, {\n      sx: {\n        mt: 2,\n        mb: 4\n      },\n      children: getStepContent(activeStep)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 1038,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Box, {\n      sx: {\n        display: 'flex',\n        justifyContent: 'space-between',\n        mt: 4\n      },\n      children: [/*#__PURE__*/_jsxDEV(Button, {\n        variant: \"outlined\",\n        color: \"secondary\",\n        onClick: activeStep === 0 ? () => navigate('/dashboard/cavi/posa') : handleBack,\n        startIcon: /*#__PURE__*/_jsxDEV(ArrowBackIcon, {}, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 1047,\n          columnNumber: 22\n        }, this),\n        disabled: loading,\n        children: activeStep === 0 ? 'Annulla' : 'Indietro'\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 1043,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Button, {\n        variant: \"contained\",\n        color: \"primary\",\n        onClick: activeStep === steps.length - 1 ? handleSubmit : handleNext,\n        endIcon: activeStep === steps.length - 1 ? /*#__PURE__*/_jsxDEV(SaveIcon, {}, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 1057,\n          columnNumber: 54\n        }, this) : /*#__PURE__*/_jsxDEV(ArrowForwardIcon, {}, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 1057,\n          columnNumber: 69\n        }, this),\n        disabled: loading || activeStep === 0 && !selectedCavo,\n        children: loading ? /*#__PURE__*/_jsxDEV(CircularProgress, {\n          size: 24\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 1061,\n          columnNumber: 13\n        }, this) : activeStep === steps.length - 1 ? 'Salva' : 'Avanti'\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 1053,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 1042,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 1029,\n    columnNumber: 5\n  }, this);\n};\n_s(InserisciMetriForm, \"sTdBCOBiexa/+X2SJsmiccxpsm4=\", false, function () {\n  return [useNavigate];\n});\n_c = InserisciMetriForm;\nexport default InserisciMetriForm;\nvar _c;\n$RefreshReg$(_c, \"InserisciMetriForm\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "Box", "Paper", "Typography", "TextField", "<PERSON><PERSON>", "Stepper", "Step", "<PERSON><PERSON><PERSON><PERSON>", "Grid", "FormControl", "InputLabel", "Select", "MenuItem", "List", "ListItem", "ListItemText", "Divider", "<PERSON><PERSON>", "CircularProgress", "FormHelperText", "IconButton", "Chip", "Search", "SearchIcon", "Save", "SaveIcon", "ArrowBack", "ArrowBackIcon", "ArrowForward", "ArrowForwardIcon", "Cancel", "CancelIcon", "CheckCircle", "CheckCircleIcon", "useNavigate", "caviService", "parcoCaviService", "redirectToVisualizzaCavi", "jsxDEV", "_jsxDEV", "Fragment", "_Fragment", "InserisciMetriForm", "cantiereId", "onSuccess", "onError", "_s", "navigate", "activeStep", "setActiveStep", "loading", "setLoading", "caviLoading", "setCaviLoading", "bob<PERSON><PERSON><PERSON><PERSON>", "setBobineLoading", "cavi", "<PERSON><PERSON><PERSON>", "bobine", "set<PERSON>ob<PERSON>", "selected<PERSON><PERSON><PERSON>", "setSelectedCavo", "cavoIdInput", "setCavoIdInput", "formData", "setFormData", "id_cavo", "metri_posati", "id_bobina", "formErrors", "setFormErrors", "formWarnings", "setForm<PERSON><PERSON>nings", "steps", "loadCavi", "loadBobine", "caviData", "get<PERSON><PERSON>", "error", "console", "message", "bobine<PERSON><PERSON>", "getBobine", "b<PERSON><PERSON><PERSON><PERSON><PERSON>", "filter", "bobina", "stato_bobina", "tipologia", "n_conduttori", "sezione", "bobineCom<PERSON><PERSON><PERSON><PERSON>", "length", "log", "sort", "a", "b", "metri_residui", "handleSearchCavoById", "trim", "cavoData", "getCavoById", "stato_installazione", "metratura_reale", "userChoice", "window", "confirm", "modificato_manualmente", "handleCavoSelect", "cavo", "reactivateSpare", "then", "updatedCavo", "catch", "cavoId", "handleFormChange", "e", "name", "value", "target", "validateField", "warning", "isNaN", "parseFloat", "metri_te<PERSON>ci", "prev", "validateForm", "<PERSON><PERSON><PERSON><PERSON>", "errors", "handleNext", "prevActiveStep", "handleBack", "handleReset", "determineInstallationStatus", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "metriTeorici", "handleSubmit", "idBobina", "statoInstallazione", "updateMetri<PERSON><PERSON><PERSON>", "renderStep1", "children", "variant", "gutterBottom", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "sx", "p", "mb", "container", "spacing", "alignItems", "item", "xs", "fullWidth", "label", "onChange", "placeholder", "color", "onClick", "disabled", "startIcon", "size", "display", "justifyContent", "my", "severity", "maxHeight", "overflow", "map", "button", "primary", "ml", "secondary", "component", "ubicazione_partenza", "ubicazione_arrivo", "renderStep2", "md", "type", "helperText", "FormHelperTextProps", "mt", "renderStep3", "getBobinaNumber", "includes", "split", "buildFullBobinaId", "numeroBobina", "hasSufficientMeters", "handleBobinaNumberInput", "toLowerCase", "id_bobina_input", "idBobinaCompleto", "<PERSON><PERSON><PERSON>", "find", "paragraph", "onBlur", "fontWeight", "overflowX", "style", "width", "borderCollapse", "marginBottom", "borderBottom", "backgroundColor", "padding", "textAlign", "isCompatible", "hasSufficient", "cursor", "id", "labelId", "bgcolor", "borderRadius", "border", "metri_totali", "renderStep4", "getStepContent", "step", "endIcon", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/PycharmProjects/CMS/webapp/frontend/src/components/cavi/InserisciMetriForm.js"], "sourcesContent": ["import React, { useState, useEffect } from 'react';\nimport {\n  Box,\n  Paper,\n  Typography,\n  TextField,\n  Button,\n  Stepper,\n  Step,\n  StepLabel,\n  Grid,\n  FormControl,\n  InputLabel,\n  Select,\n  MenuItem,\n  List,\n  ListItem,\n  ListItemText,\n  Divider,\n  Alert,\n  CircularProgress,\n  FormHelperText,\n  IconButton,\n  Chip\n} from '@mui/material';\nimport {\n  Search as SearchIcon,\n  Save as SaveIcon,\n  ArrowBack as ArrowBackIcon,\n  ArrowForward as ArrowForwardIcon,\n  Cancel as CancelIcon,\n  CheckCircle as CheckCircleIcon\n} from '@mui/icons-material';\nimport { useNavigate } from 'react-router-dom';\nimport caviService from '../../services/caviService';\nimport parcoCaviService from '../../services/parcoCaviService';\nimport { redirectToVisualizzaCavi } from '../../utils/navigationUtils';\n\n/**\n * Componente per l'inserimento dei metri posati di un cavo\n *\n * @param {Object} props - Proprietà del componente\n * @param {string} props.cantiereId - ID del cantiere\n * @param {Function} props.onSuccess - Funzione chiamata al successo dell'operazione\n * @param {Function} props.onError - Funzione chiamata in caso di errore\n */\nconst InserisciMetriForm = ({ cantiereId, onSuccess, onError }) => {\n  const navigate = useNavigate();\n\n  // Stati per la gestione del form multi-step\n  const [activeStep, setActiveStep] = useState(0);\n  const [loading, setLoading] = useState(false);\n  const [caviLoading, setCaviLoading] = useState(false);\n  const [bobineLoading, setBobineLoading] = useState(false);\n\n  // Stati per i dati\n  const [cavi, setCavi] = useState([]);\n  const [bobine, setBobine] = useState([]);\n  const [selectedCavo, setSelectedCavo] = useState(null);\n  const [cavoIdInput, setCavoIdInput] = useState('');\n\n  // Stati per il form\n  const [formData, setFormData] = useState({\n    id_cavo: '',\n    metri_posati: '',\n    id_bobina: ''\n  });\n\n  // Stati per la validazione\n  const [formErrors, setFormErrors] = useState({});\n  const [formWarnings, setFormWarnings] = useState({});\n\n  // Definizione dei passi del form\n  const steps = ['Seleziona Cavo', 'Inserisci Metri', 'Associa Bobina', 'Conferma'];\n\n  // Carica la lista dei cavi all'avvio\n  useEffect(() => {\n    loadCavi();\n  }, [cantiereId]);\n\n  // Carica la lista delle bobine quando necessario\n  useEffect(() => {\n    if (activeStep === 2) {\n      loadBobine();\n    }\n  }, [activeStep, cantiereId]);\n\n  // Funzione per caricare i cavi\n  const loadCavi = async () => {\n    try {\n      setCaviLoading(true);\n      // Carica tutti i cavi, inclusi quelli SPARE e installati\n      const caviData = await caviService.getCavi(cantiereId);\n\n      // Mostra tutti i cavi (da installare, in corso, installati e SPARE)\n      // Questo permette di vedere anche i cavi già installati per eventuale modifica della bobina\n      setCavi(caviData);\n    } catch (error) {\n      console.error('Errore nel caricamento dei cavi:', error);\n      onError('Errore nel caricamento dei cavi: ' + (error.message || 'Errore sconosciuto'));\n    } finally {\n      setCaviLoading(false);\n    }\n  };\n\n  // Funzione per caricare le bobine\n  const loadBobine = async () => {\n    try {\n      setBobineLoading(true);\n\n      // Carica solo le bobine disponibili o in uso\n      const bobineData = await parcoCaviService.getBobine(cantiereId);\n\n      // Filtra le bobine per stato (disponibile o in uso) e per compatibilità con il cavo selezionato\n      let bobineUtilizzabili = bobineData.filter(bobina =>\n        (bobina.stato_bobina === 'Disponibile' || bobina.stato_bobina === 'In Uso') &&\n        bobina.stato_bobina !== 'Over' && bobina.stato_bobina !== 'Terminata'\n      );\n\n      // Se c'è un cavo selezionato, filtra ulteriormente per caratteristiche del cavo\n      if (selectedCavo) {\n        // Filtra per tipologia, numero conduttori e sezione se disponibili\n        if (selectedCavo.tipologia && selectedCavo.n_conduttori && selectedCavo.sezione) {\n          const bobineCompatibili = bobineUtilizzabili.filter(bobina =>\n            bobina.tipologia === selectedCavo.tipologia &&\n            bobina.n_conduttori === selectedCavo.n_conduttori &&\n            bobina.sezione === selectedCavo.sezione\n          );\n\n          // Se ci sono bobine compatibili, usa quelle, altrimenti mostra tutte le bobine utilizzabili\n          if (bobineCompatibili.length > 0) {\n            bobineUtilizzabili = bobineCompatibili;\n          } else {\n            console.log('Nessuna bobina compatibile trovata, mostro tutte le bobine disponibili');\n          }\n        }\n\n        // Ordina le bobine per metri residui (decrescente)\n        bobineUtilizzabili.sort((a, b) => b.metri_residui - a.metri_residui);\n      }\n\n      setBobine(bobineUtilizzabili);\n    } catch (error) {\n      console.error('Errore nel caricamento delle bobine:', error);\n      onError('Errore nel caricamento delle bobine: ' + (error.message || 'Errore sconosciuto'));\n    } finally {\n      setBobineLoading(false);\n    }\n  };\n\n  // Gestisce la ricerca di un cavo per ID\n  const handleSearchCavoById = async () => {\n    if (!cavoIdInput.trim()) {\n      onError('Inserisci un ID cavo valido');\n      return;\n    }\n\n    try {\n      setCaviLoading(true);\n      const cavoData = await caviService.getCavoById(cantiereId, cavoIdInput.trim());\n\n      // Verifica se il cavo è già installato\n      if (cavoData.stato_installazione === 'Installato' || (cavoData.metratura_reale && cavoData.metratura_reale > 0)) {\n        // Mostra un messaggio di conferma con opzioni\n        const message = `Il cavo ${cavoData.id_cavo} risulta già posato (${cavoData.metratura_reale || 0}m).\n\nVuoi:\n1. Modificare la bobina associata (usa la funzione \"Modifica bobina cavo posato\")\n2. Selezionare un altro cavo\n3. Annullare l'operazione`;\n\n        const userChoice = window.confirm(message);\n\n        if (userChoice) {\n          // L'utente ha scelto di modificare la bobina o selezionare un altro cavo\n          // Reindirizza alla pagina di modifica bobina\n          navigate(`/dashboard/cavi/modifica-bobina/${cantiereId}/${cavoData.id_cavo}`);\n        }\n\n        setCaviLoading(false);\n        return;\n      }\n\n      // Verifica se il cavo è SPARE\n      if (cavoData.modificato_manualmente === 3) {\n        // Gestione del cavo SPARE - verrà gestito in handleCavoSelect\n      }\n\n      // Seleziona il cavo e passa al passo successivo\n      handleCavoSelect(cavoData);\n    } catch (error) {\n      console.error('Errore nella ricerca del cavo:', error);\n      onError('Cavo non trovato o errore nella ricerca: ' + (error.message || 'Errore sconosciuto'));\n    } finally {\n      setCaviLoading(false);\n    }\n  };\n\n  // Gestisce la selezione di un cavo\n  const handleCavoSelect = (cavo) => {\n    // Verifica se il cavo è già installato\n    if (cavo.stato_installazione === 'Installato' || (cavo.metratura_reale && cavo.metratura_reale > 0)) {\n      // Mostra un messaggio di conferma con opzioni\n      const message = `Il cavo ${cavo.id_cavo} risulta già posato (${cavo.metratura_reale || 0}m).\n\nVuoi:\n1. Modificare la bobina associata (usa la funzione \"Modifica bobina cavo posato\")\n2. Selezionare un altro cavo\n3. Annullare l'operazione`;\n\n      const userChoice = window.confirm(message);\n\n      if (userChoice) {\n        // L'utente ha scelto di modificare la bobina o selezionare un altro cavo\n        // Reindirizza alla pagina di modifica bobina\n        navigate(`/dashboard/cavi/modifica-bobina/${cantiereId}/${cavo.id_cavo}`);\n      }\n\n      return;\n    }\n    // Verifica se il cavo è SPARE (modificato_manualmente = 3)\n    else if (cavo.modificato_manualmente === 3) {\n      // Chiedi conferma all'utente per riattivare il cavo SPARE\n      if (window.confirm(`Il cavo ${cavo.id_cavo} è marcato come SPARE. Vuoi riattivarlo?`)) {\n        // Riattiva il cavo SPARE (imposta modificato_manualmente = 0)\n        reactivateSpare(cavo.id_cavo).then(() => {\n          // Aggiorna il cavo selezionato con modificato_manualmente = 0\n          const updatedCavo = { ...cavo, modificato_manualmente: 0 };\n          setSelectedCavo(updatedCavo);\n          setFormData({\n            ...formData,\n            id_cavo: updatedCavo.id_cavo,\n            metri_posati: ''\n          });\n          // Passa al passo successivo\n          setActiveStep(1);\n        }).catch(error => {\n          console.error('Errore durante la riattivazione del cavo SPARE:', error);\n          onError('Errore durante la riattivazione del cavo SPARE: ' + (error.message || 'Errore sconosciuto'));\n        });\n      } else {\n        // L'utente ha annullato la riattivazione\n        return;\n      }\n    } else {\n      // Cavo normale (non SPARE e non installato)\n      setSelectedCavo(cavo);\n      setFormData({\n        ...formData,\n        id_cavo: cavo.id_cavo,\n        metri_posati: ''\n      });\n      // Passa al passo successivo\n      setActiveStep(1);\n    }\n  };\n\n  // Funzione per riattivare un cavo SPARE\n  const reactivateSpare = async (cavoId) => {\n    try {\n      // Chiamata API per riattivare il cavo SPARE\n      await caviService.reactivateSpare(cantiereId, cavoId);\n      onSuccess(`Cavo ${cavoId} riattivato con successo`);\n      return true;\n    } catch (error) {\n      console.error('Errore durante la riattivazione del cavo SPARE:', error);\n      onError('Errore durante la riattivazione del cavo SPARE: ' + (error.message || 'Errore sconosciuto'));\n      throw error;\n    }\n  };\n\n  // Gestisce il cambio dei valori nel form\n  const handleFormChange = (e) => {\n    const { name, value } = e.target;\n    setFormData({\n      ...formData,\n      [name]: value\n    });\n\n    // Validazione in tempo reale\n    validateField(name, value);\n  };\n\n  // Validazione di un campo\n  const validateField = (name, value) => {\n    let error = null;\n    let warning = null;\n\n    if (name === 'metri_posati') {\n      if (!value || value.trim() === '') {\n        error = 'Inserire un valore per i metri posati';\n      } else if (isNaN(parseFloat(value)) || parseFloat(value) <= 0) {\n        error = 'Inserire un valore numerico positivo';\n      } else if (selectedCavo && selectedCavo.metri_teorici && parseFloat(value) > parseFloat(selectedCavo.metri_teorici)) {\n        warning = 'I metri posati superano i metri teorici del cavo';\n      }\n    }\n\n    // Aggiorna gli errori\n    setFormErrors(prev => ({\n      ...prev,\n      [name]: error\n    }));\n\n    // Aggiorna gli avvisi\n    setFormWarnings(prev => ({\n      ...prev,\n      [name]: warning\n    }));\n\n    return !error;\n  };\n\n  // Validazione completa del form\n  const validateForm = () => {\n    let isValid = true;\n    const errors = {};\n\n    // Validazione metri posati\n    if (!formData.metri_posati || formData.metri_posati.trim() === '') {\n      errors.metri_posati = 'Inserire un valore per i metri posati';\n      isValid = false;\n    } else if (isNaN(parseFloat(formData.metri_posati)) || parseFloat(formData.metri_posati) <= 0) {\n      errors.metri_posati = 'Inserire un valore numerico positivo';\n      isValid = false;\n    }\n\n    setFormErrors(errors);\n    return isValid;\n  };\n\n  // Gestisce il passaggio al passo successivo\n  const handleNext = () => {\n    if (activeStep === 1) {\n      // Validazione prima di passare al passo successivo\n      if (!validateForm()) {\n        return;\n      }\n    }\n\n    setActiveStep((prevActiveStep) => prevActiveStep + 1);\n  };\n\n  // Gestisce il ritorno al passo precedente\n  const handleBack = () => {\n    setActiveStep((prevActiveStep) => prevActiveStep - 1);\n  };\n\n  // Gestisce il reset del form\n  const handleReset = () => {\n    setActiveStep(0);\n    setSelectedCavo(null);\n    setCavoIdInput('');\n    setFormData({\n      id_cavo: '',\n      metri_posati: '',\n      id_bobina: ''\n    });\n    setFormErrors({});\n    setFormWarnings({});\n  };\n\n  // Determina lo stato di installazione in base ai metri posati\n  const determineInstallationStatus = (metriPosati, metriTeorici) => {\n    if (!metriPosati || parseFloat(metriPosati) <= 0) {\n      return 'Da installare';\n    }\n\n    if (parseFloat(metriPosati) >= parseFloat(metriTeorici)) {\n      return 'Installato';\n    }\n\n    return 'In corso';\n  };\n\n  // Gestisce l'invio del form\n  const handleSubmit = async () => {\n    try {\n      setLoading(true);\n\n      // Validazione finale\n      if (!validateForm()) {\n        setLoading(false);\n        return;\n      }\n\n      // Prepara i dati da inviare\n      const metriPosati = parseFloat(formData.metri_posati);\n      const idBobina = formData.id_bobina || null;\n\n      // Determina lo stato di installazione\n      const statoInstallazione = determineInstallationStatus(metriPosati, selectedCavo.metri_teorici);\n\n      // Log per debug\n      console.log('Invio dati:', {\n        cantiereId,\n        cavoId: formData.id_cavo,\n        metriPosati,\n        idBobina,\n        statoInstallazione\n      });\n\n      // Chiamata API\n      await caviService.updateMetriPosati(\n        cantiereId,\n        formData.id_cavo,\n        metriPosati,\n        idBobina\n      );\n\n      // Gestione successo\n      onSuccess(`Metri posati aggiornati con successo. Stato cavo: ${statoInstallazione}`);\n\n      // Reset del form\n      handleReset();\n\n      // Ricarica i cavi\n      loadCavi();\n    } catch (error) {\n      console.error('Errore durante l\\'aggiornamento dei metri posati:', error);\n      onError('Errore durante l\\'aggiornamento dei metri posati: ' + (error.message || 'Errore sconosciuto'));\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  // Renderizza il passo 1: Selezione del cavo\n  const renderStep1 = () => {\n    return (\n      <Box>\n        <Typography variant=\"h6\" gutterBottom>\n          Seleziona un cavo\n        </Typography>\n\n        {/* Ricerca per ID */}\n        <Paper sx={{ p: 2, mb: 3 }}>\n          <Typography variant=\"subtitle1\" gutterBottom>\n            Cerca cavo per ID\n          </Typography>\n          <Grid container spacing={2} alignItems=\"center\">\n            <Grid item xs={9}>\n              <TextField\n                fullWidth\n                label=\"ID Cavo\"\n                variant=\"outlined\"\n                value={cavoIdInput}\n                onChange={(e) => setCavoIdInput(e.target.value)}\n                placeholder=\"Inserisci l'ID del cavo\"\n              />\n            </Grid>\n            <Grid item xs={3}>\n              <Button\n                fullWidth\n                variant=\"contained\"\n                color=\"primary\"\n                onClick={handleSearchCavoById}\n                disabled={caviLoading || !cavoIdInput.trim()}\n                startIcon={caviLoading ? <CircularProgress size={20} /> : <SearchIcon />}\n              >\n                Cerca\n              </Button>\n            </Grid>\n          </Grid>\n        </Paper>\n\n        {/* Lista cavi */}\n        <Paper sx={{ p: 2 }}>\n          <Typography variant=\"subtitle1\" gutterBottom>\n            Seleziona dalla lista\n          </Typography>\n\n          {caviLoading ? (\n            <Box sx={{ display: 'flex', justifyContent: 'center', my: 4 }}>\n              <CircularProgress />\n            </Box>\n          ) : cavi.length === 0 ? (\n            <Alert severity=\"info\">\n              Non ci sono cavi disponibili da installare.\n            </Alert>\n          ) : (\n            <List sx={{ maxHeight: '400px', overflow: 'auto' }}>\n              {cavi.map((cavo) => (\n                <React.Fragment key={cavo.id_cavo}>\n                  <ListItem button onClick={() => handleCavoSelect(cavo)}>\n                    <ListItemText\n                      primary={\n                        <Box sx={{ display: 'flex', alignItems: 'center' }}>\n                          <Typography variant=\"subtitle1\">{cavo.id_cavo}</Typography>\n                          {cavo.modificato_manualmente === 3 ? (\n                            <Chip\n                              size=\"small\"\n                              label=\"SPARE\"\n                              color=\"error\"\n                              sx={{ ml: 1 }}\n                            />\n                          ) : cavo.stato_installazione === 'Installato' || (cavo.metratura_reale && cavo.metratura_reale > 0) ? (\n                            <Chip\n                              size=\"small\"\n                              label=\"Installato\"\n                              color=\"success\"\n                              sx={{ ml: 1 }}\n                            />\n                          ) : (\n                            <Chip\n                              size=\"small\"\n                              label={cavo.stato_installazione}\n                              color={cavo.stato_installazione === 'In corso' ? 'warning' : 'default'}\n                              sx={{ ml: 1 }}\n                            />\n                          )}\n                        </Box>\n                      }\n                      secondary={\n                        <>\n                          <Typography variant=\"body2\" component=\"span\">\n                            {cavo.tipologia || 'N/A'} - {cavo.n_conduttori || 'N/A'} x {cavo.sezione || 'N/A'}\n                          </Typography>\n                          <br />\n                          <Typography variant=\"body2\" component=\"span\">\n                            Da: {cavo.ubicazione_partenza || 'N/A'} - A: {cavo.ubicazione_arrivo || 'N/A'}\n                          </Typography>\n                          <br />\n                          <Typography variant=\"body2\" component=\"span\">\n                            Metri teorici: {cavo.metri_teorici || 'N/A'} - Metri posati: {cavo.metratura_reale || '0'}\n                          </Typography>\n                        </>\n                      }\n                    />\n                  </ListItem>\n                  <Divider />\n                </React.Fragment>\n              ))}\n            </List>\n          )}\n        </Paper>\n      </Box>\n    );\n  };\n\n  // Renderizza il passo 2: Inserimento metri\n  const renderStep2 = () => {\n    if (!selectedCavo) return null;\n\n    return (\n      <Box>\n        <Typography variant=\"h6\" gutterBottom>\n          Inserisci metri posati\n        </Typography>\n\n        <Paper sx={{ p: 3, mb: 3 }}>\n          <Typography variant=\"subtitle1\" gutterBottom>\n            Dettagli del cavo selezionato\n          </Typography>\n\n          <Grid container spacing={2}>\n            <Grid item xs={12} md={6}>\n              <Typography variant=\"body1\">\n                <strong>ID Cavo:</strong> {selectedCavo.id_cavo}\n              </Typography>\n              <Typography variant=\"body1\">\n                <strong>Tipologia:</strong> {selectedCavo.tipologia || 'N/A'}\n              </Typography>\n              <Typography variant=\"body1\">\n                <strong>Conduttori:</strong> {selectedCavo.n_conduttori || 'N/A'} x {selectedCavo.sezione || 'N/A'}\n              </Typography>\n            </Grid>\n            <Grid item xs={12} md={6}>\n              <Typography variant=\"body1\">\n                <strong>Ubicazione Partenza:</strong> {selectedCavo.ubicazione_partenza || 'N/A'}\n              </Typography>\n              <Typography variant=\"body1\">\n                <strong>Ubicazione Arrivo:</strong> {selectedCavo.ubicazione_arrivo || 'N/A'}\n              </Typography>\n              <Typography variant=\"body1\">\n                <strong>Metri Teorici:</strong> {selectedCavo.metri_teorici || 'N/A'}\n              </Typography>\n            </Grid>\n          </Grid>\n        </Paper>\n\n        <Paper sx={{ p: 3 }}>\n          <Typography variant=\"subtitle1\" gutterBottom>\n            Inserisci i metri posati\n          </Typography>\n\n          <TextField\n            fullWidth\n            label=\"Metri posati\"\n            variant=\"outlined\"\n            name=\"metri_posati\"\n            type=\"number\"\n            value={formData.metri_posati}\n            onChange={handleFormChange}\n            error={!!formErrors.metri_posati}\n            helperText={formErrors.metri_posati || formWarnings.metri_posati}\n            FormHelperTextProps={{\n              sx: { color: formWarnings.metri_posati && !formErrors.metri_posati ? 'warning.main' : 'error.main' }\n            }}\n            sx={{ mb: 2 }}\n          />\n\n          <Alert severity=\"info\" sx={{ mt: 2 }}>\n            Inserisci i metri di cavo effettivamente posati. Questo valore aggiorna lo stato di installazione del cavo.\n          </Alert>\n        </Paper>\n      </Box>\n    );\n  };\n\n  // Renderizza il passo 3: Associazione bobina\n  const renderStep3 = () => {\n    // Funzione per estrarre il numero della bobina dall'ID completo\n    const getBobinaNumber = (idBobina) => {\n      // L'ID completo è nel formato C{id_cantiere}_B{numero_bobina}\n      if (idBobina && idBobina.includes('_B')) {\n        return idBobina.split('_B')[1];\n      }\n      return idBobina;\n    };\n\n    // Funzione per costruire l'ID completo della bobina\n    const buildFullBobinaId = (numeroBobina) => {\n      return `C${cantiereId}_B${numeroBobina}`;\n    };\n\n    // Verifica se una bobina ha metri residui sufficienti\n    const hasSufficientMeters = (bobina) => {\n      if (!bobina || !formData.metri_posati) return true;\n      return parseFloat(bobina.metri_residui) >= parseFloat(formData.metri_posati);\n    };\n\n    // Gestisce l'input diretto del numero della bobina\n    const handleBobinaNumberInput = (e) => {\n      const numeroBobina = e.target.value.trim();\n\n      // Gestione opzioni speciali\n      if (numeroBobina.toLowerCase() === 'v') {\n        // Opzione 'v' per bobina vuota\n        setFormData({\n          ...formData,\n          id_bobina: 'BOBINA_VUOTA'\n        });\n        setFormErrors({\n          ...formErrors,\n          id_bobina_input: null\n        });\n        return;\n      }\n\n      if (numeroBobina.toLowerCase() === 'q') {\n        // Opzione 'q' per annullare\n        setFormData({\n          ...formData,\n          id_bobina: ''\n        });\n        setFormErrors({\n          ...formErrors,\n          id_bobina_input: null\n        });\n        return;\n      }\n\n      if (numeroBobina) {\n        // Costruisci l'ID completo\n        const idBobinaCompleto = buildFullBobinaId(numeroBobina);\n\n        // Verifica se la bobina esiste\n        const bobinaEsistente = bobine.find(b => b.id_bobina === idBobinaCompleto);\n\n        if (bobinaEsistente) {\n          // Verifica se la bobina è in stato OVER o TERMINATA\n          if (bobinaEsistente.stato_bobina === 'Over' || bobinaEsistente.stato_bobina === 'Terminata') {\n            setFormErrors({\n              ...formErrors,\n              id_bobina_input: `La bobina ${numeroBobina} è in stato ${bobinaEsistente.stato_bobina} e non può essere utilizzata`\n            });\n            return;\n          }\n\n          // Verifica se la bobina ha metri residui sufficienti\n          if (hasSufficientMeters(bobinaEsistente)) {\n            // Se la bobina esiste e ha metri sufficienti, imposta l'ID completo\n            setFormData({\n              ...formData,\n              id_bobina: idBobinaCompleto\n            });\n            setFormErrors({\n              ...formErrors,\n              id_bobina_input: null\n            });\n          } else {\n            // Se la bobina non ha metri sufficienti, mostra un avviso\n            setFormErrors({\n              ...formErrors,\n              id_bobina_input: `La bobina ${numeroBobina} non ha metri residui sufficienti (${bobinaEsistente.metri_residui} m disponibili)`\n            });\n          }\n        } else {\n          // Se la bobina non esiste, mostra un errore\n          setFormErrors({\n            ...formErrors,\n            id_bobina_input: `Bobina ${numeroBobina} non trovata`\n          });\n        }\n      } else {\n        // Se l'input è vuoto, resetta l'ID bobina\n        setFormData({\n          ...formData,\n          id_bobina: ''\n        });\n        setFormErrors({\n          ...formErrors,\n          id_bobina_input: null\n        });\n      }\n    };\n\n    return (\n      <Box>\n        <Typography variant=\"h6\" gutterBottom>\n          Associa bobina (opzionale)\n        </Typography>\n\n        <Paper sx={{ p: 3 }}>\n          <Typography variant=\"body1\" paragraph>\n            Puoi associare una bobina al cavo selezionato. Questo è opzionale, ma consigliato per tenere traccia dell'utilizzo delle bobine.\n          </Typography>\n\n          {bobineLoading ? (\n            <Box sx={{ display: 'flex', justifyContent: 'center', my: 4 }}>\n              <CircularProgress />\n            </Box>\n          ) : (\n            <Box>\n              {/* Input diretto del numero della bobina */}\n              <Grid container spacing={2} sx={{ mb: 3 }}>\n                <Grid item xs={12} md={6}>\n                  <TextField\n                    fullWidth\n                    label=\"Inserisci numero bobina\"\n                    variant=\"outlined\"\n                    placeholder=\"Inserisci solo il numero (Y) o 'v' per bobina vuota\"\n                    helperText={formErrors.id_bobina_input || \"Inserisci solo il numero della bobina (parte Y del codice Cx_By), 'v' per bobina vuota o 'q' per annullare\"}\n                    error={!!formErrors.id_bobina_input}\n                    onBlur={handleBobinaNumberInput}\n                  />\n                </Grid>\n                <Grid item xs={12} md={6}>\n                  <Typography variant=\"body2\" sx={{ mt: 1 }}>\n                    ID Bobina completo: {formData.id_bobina === 'BOBINA_VUOTA' ? 'BOBINA VUOTA' : formData.id_bobina || '-'}\n                  </Typography>\n                </Grid>\n              </Grid>\n\n              {/* Tabella delle bobine disponibili */}\n              <Typography variant=\"subtitle1\" gutterBottom sx={{ mt: 3, fontWeight: 'bold' }}>\n                Bobine disponibili compatibili con il cavo selezionato\n              </Typography>\n\n              {bobine.length > 0 ? (\n                <Box sx={{ overflowX: 'auto' }}>\n                  <table style={{ width: '100%', borderCollapse: 'collapse', marginBottom: '20px' }}>\n                    <thead>\n                      <tr style={{ borderBottom: '2px solid #ddd', backgroundColor: '#f5f5f5' }}>\n                        <th style={{ padding: '8px', textAlign: 'left' }}>ID</th>\n                        <th style={{ padding: '8px', textAlign: 'left' }}>Tipologia</th>\n                        <th style={{ padding: '8px', textAlign: 'left' }}>Cond.</th>\n                        <th style={{ padding: '8px', textAlign: 'left' }}>Sezione</th>\n                        <th style={{ padding: '8px', textAlign: 'right' }}>Residui</th>\n                        <th style={{ padding: '8px', textAlign: 'left' }}>Stato</th>\n                      </tr>\n                    </thead>\n                    <tbody>\n                      {bobine.map((bobina) => {\n                        const isCompatible = selectedCavo &&\n                          bobina.tipologia === selectedCavo.tipologia &&\n                          bobina.n_conduttori === selectedCavo.n_conduttori &&\n                          bobina.sezione === selectedCavo.sezione;\n\n                        const hasSufficient = bobina.metri_residui >= parseFloat(formData.metri_posati || 0);\n\n                        return (\n                          <tr\n                            key={bobina.id_bobina}\n                            style={{\n                              borderBottom: '1px solid #ddd',\n                              backgroundColor: isCompatible ? (hasSufficient ? '#e8f5e9' : '#fff8e1') : 'transparent',\n                              cursor: 'pointer'\n                            }}\n                            onClick={() => {\n                              if (hasSufficient) {\n                                setFormData({\n                                  ...formData,\n                                  id_bobina: bobina.id_bobina\n                                });\n                              }\n                            }}\n                          >\n                            <td style={{ padding: '8px' }}>{getBobinaNumber(bobina.id_bobina)}</td>\n                            <td style={{ padding: '8px' }}>{bobina.tipologia || 'N/A'}</td>\n                            <td style={{ padding: '8px' }}>{bobina.n_conduttori || 'N/A'}</td>\n                            <td style={{ padding: '8px' }}>{bobina.sezione || 'N/A'}</td>\n                            <td style={{ padding: '8px', textAlign: 'right' }}>{bobina.metri_residui || 0} m</td>\n                            <td style={{ padding: '8px' }}>\n                              <Chip\n                                size=\"small\"\n                                label={bobina.stato_bobina}\n                                color={\n                                  bobina.stato_bobina === 'Disponibile' ? 'success' :\n                                  bobina.stato_bobina === 'In uso' ? 'primary' :\n                                  bobina.stato_bobina === 'Over' ? 'error' :\n                                  bobina.stato_bobina === 'Terminata' ? 'warning' : 'default'\n                                }\n                              />\n                            </td>\n                          </tr>\n                        );\n                      })}\n                    </tbody>\n                  </table>\n                </Box>\n              ) : (\n                <Alert severity=\"info\" sx={{ mt: 2, mb: 3 }}>\n                  Non ci sono bobine disponibili compatibili con il cavo selezionato.\n                </Alert>\n              )}\n\n              {/* Opzioni speciali */}\n              <Box sx={{ mt: 3, mb: 3 }}>\n                <Typography variant=\"subtitle2\" gutterBottom>\n                  Opzioni speciali:\n                </Typography>\n                <Grid container spacing={2}>\n                  <Grid item>\n                    <Button\n                      variant=\"outlined\"\n                      onClick={() => {\n                        setFormData({\n                          ...formData,\n                          id_bobina: 'BOBINA_VUOTA'\n                        });\n                      }}\n                    >\n                      Bobina Vuota (v)\n                    </Button>\n                  </Grid>\n                  <Grid item>\n                    <Button\n                      variant=\"outlined\"\n                      color=\"secondary\"\n                      onClick={() => {\n                        setFormData({\n                          ...formData,\n                          id_bobina: ''\n                        });\n                      }}\n                    >\n                      Nessuna Bobina (q)\n                    </Button>\n                  </Grid>\n                </Grid>\n              </Box>\n\n              {/* Selezione dalla lista (manteniamo anche questa per compatibilità) */}\n              <FormControl fullWidth>\n                <InputLabel id=\"bobina-select-label\">Seleziona Bobina dalla lista</InputLabel>\n                <Select\n                  labelId=\"bobina-select-label\"\n                  id=\"bobina-select\"\n                  name=\"id_bobina\"\n                  value={formData.id_bobina}\n                  label=\"Seleziona Bobina dalla lista\"\n                  onChange={handleFormChange}\n                >\n                  <MenuItem value=\"\">\n                    <em>Nessuna bobina</em>\n                  </MenuItem>\n                  <MenuItem value=\"BOBINA_VUOTA\">\n                    <em>BOBINA VUOTA</em>\n                  </MenuItem>\n                  {bobine.map((bobina) => (\n                    <MenuItem\n                      key={bobina.id_bobina}\n                      value={bobina.id_bobina}\n                      disabled={bobina.metri_residui < parseFloat(formData.metri_posati)}\n                    >\n                      {getBobinaNumber(bobina.id_bobina)} - {bobina.tipologia || 'N/A'} - Residui: {bobina.metri_residui || 0} m\n                    </MenuItem>\n                  ))}\n                </Select>\n                <FormHelperText>\n                  Seleziona una bobina con metri residui sufficienti o lascia vuoto per non associare alcuna bobina\n                </FormHelperText>\n              </FormControl>\n\n              {/* Mostra dettagli della bobina selezionata */}\n              {formData.id_bobina && (\n                <Box sx={{ mt: 3, p: 2, bgcolor: 'background.paper', borderRadius: 1, border: '1px solid #e0e0e0' }}>\n                  <Typography variant=\"subtitle2\" gutterBottom>\n                    Dettagli bobina selezionata\n                  </Typography>\n                  {(() => {\n                    const bobina = bobine.find(b => b.id_bobina === formData.id_bobina);\n                    if (bobina) {\n                      return (\n                        <Grid container spacing={2}>\n                          <Grid item xs={12} md={6}>\n                            <Typography variant=\"body2\">\n                              <strong>Numero:</strong> {getBobinaNumber(bobina.id_bobina)}\n                            </Typography>\n                            <Typography variant=\"body2\">\n                              <strong>Tipologia:</strong> {bobina.tipologia || 'N/A'}\n                            </Typography>\n                            <Typography variant=\"body2\">\n                              <strong>Conduttori:</strong> {bobina.n_conduttori || 'N/A'} x {bobina.sezione || 'N/A'}\n                            </Typography>\n                          </Grid>\n                          <Grid item xs={12} md={6}>\n                            <Typography variant=\"body2\">\n                              <strong>Metri totali:</strong> {bobina.metri_totali || 0} m\n                            </Typography>\n                            <Typography variant=\"body2\">\n                              <strong>Metri residui:</strong> {bobina.metri_residui || 0} m\n                            </Typography>\n                            <Typography variant=\"body2\">\n                              <strong>Stato:</strong> {bobina.stato_bobina || 'N/A'}\n                            </Typography>\n                          </Grid>\n                        </Grid>\n                      );\n                    }\n                    return (\n                      <Typography variant=\"body2\" color=\"error\">\n                        Bobina non trovata nel database\n                      </Typography>\n                    );\n                  })()}\n                </Box>\n              )}\n            </Box>\n          )}\n\n          {bobine.length === 0 && !bobineLoading && (\n            <Alert severity=\"warning\" sx={{ mt: 2 }}>\n              Non ci sono bobine disponibili. Puoi procedere senza associare una bobina o aggiungere prima una nuova bobina.\n            </Alert>\n          )}\n        </Paper>\n      </Box>\n    );\n  };\n\n  // Renderizza il passo 4: Conferma\n  const renderStep4 = () => {\n    // Funzione per estrarre il numero della bobina dall'ID completo\n    const getBobinaNumber = (idBobina) => {\n      // L'ID completo è nel formato C{id_cantiere}_B{numero_bobina}\n      if (idBobina && idBobina.includes('_B')) {\n        return idBobina.split('_B')[1];\n      }\n      return idBobina;\n    };\n\n    // Ottieni il numero della bobina se presente\n    const numeroBobina = formData.id_bobina ? getBobinaNumber(formData.id_bobina) : 'Nessuna';\n\n    return (\n      <Box>\n        <Typography variant=\"h6\" gutterBottom>\n          Conferma inserimento\n        </Typography>\n\n        <Paper sx={{ p: 3 }}>\n          <Typography variant=\"subtitle1\" gutterBottom>\n            Riepilogo dati\n          </Typography>\n\n          <Grid container spacing={2}>\n            <Grid item xs={12} md={6}>\n              <Typography variant=\"body1\">\n                <strong>ID Cavo:</strong> {selectedCavo.id_cavo}\n              </Typography>\n              <Typography variant=\"body1\">\n                <strong>Tipologia:</strong> {selectedCavo.tipologia || 'N/A'}\n              </Typography>\n              <Typography variant=\"body1\">\n                <strong>Conduttori:</strong> {selectedCavo.n_conduttori || 'N/A'} x {selectedCavo.sezione || 'N/A'}\n              </Typography>\n            </Grid>\n            <Grid item xs={12} md={6}>\n              <Typography variant=\"body1\">\n                <strong>Metri Posati:</strong> {formData.metri_posati}\n              </Typography>\n              <Typography variant=\"body1\">\n                <strong>Bobina Associata:</strong> {numeroBobina}\n              </Typography>\n              <Typography variant=\"body1\">\n                <strong>Stato Installazione:</strong> {parseFloat(formData.metri_posati) >= parseFloat(selectedCavo.metri_teorici) ? 'Installato' : 'In corso'}\n              </Typography>\n            </Grid>\n          </Grid>\n\n          <Alert severity=\"info\" sx={{ mt: 3 }}>\n            Conferma per aggiornare i metri posati e lo stato di installazione del cavo.\n            {formData.id_bobina && ' I metri posati verranno sottratti dai metri residui della bobina selezionata.'}\n          </Alert>\n        </Paper>\n      </Box>\n    );\n  };\n\n  // Renderizza il contenuto in base al passo attivo\n  const getStepContent = (step) => {\n    switch (step) {\n      case 0:\n        return renderStep1();\n      case 1:\n        return renderStep2();\n      case 2:\n        return renderStep3();\n      case 3:\n        return renderStep4();\n      default:\n        return 'Passo sconosciuto';\n    }\n  };\n\n  return (\n    <Box>\n      <Stepper activeStep={activeStep} sx={{ mb: 4 }}>\n        {steps.map((label) => (\n          <Step key={label}>\n            <StepLabel>{label}</StepLabel>\n          </Step>\n        ))}\n      </Stepper>\n\n      <Box sx={{ mt: 2, mb: 4 }}>\n        {getStepContent(activeStep)}\n      </Box>\n\n      <Box sx={{ display: 'flex', justifyContent: 'space-between', mt: 4 }}>\n        <Button\n          variant=\"outlined\"\n          color=\"secondary\"\n          onClick={activeStep === 0 ? () => navigate('/dashboard/cavi/posa') : handleBack}\n          startIcon={<ArrowBackIcon />}\n          disabled={loading}\n        >\n          {activeStep === 0 ? 'Annulla' : 'Indietro'}\n        </Button>\n\n        <Button\n          variant=\"contained\"\n          color=\"primary\"\n          onClick={activeStep === steps.length - 1 ? handleSubmit : handleNext}\n          endIcon={activeStep === steps.length - 1 ? <SaveIcon /> : <ArrowForwardIcon />}\n          disabled={loading || (activeStep === 0 && !selectedCavo)}\n        >\n          {loading ? (\n            <CircularProgress size={24} />\n          ) : activeStep === steps.length - 1 ? (\n            'Salva'\n          ) : (\n            'Avanti'\n          )}\n        </Button>\n      </Box>\n    </Box>\n  );\n};\n\nexport default InserisciMetriForm;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAClD,SACEC,GAAG,EACHC,KAAK,EACLC,UAAU,EACVC,SAAS,EACTC,MAAM,EACNC,OAAO,EACPC,IAAI,EACJC,SAAS,EACTC,IAAI,EACJC,WAAW,EACXC,UAAU,EACVC,MAAM,EACNC,QAAQ,EACRC,IAAI,EACJC,QAAQ,EACRC,YAAY,EACZC,OAAO,EACPC,KAAK,EACLC,gBAAgB,EAChBC,cAAc,EACdC,UAAU,EACVC,IAAI,QACC,eAAe;AACtB,SACEC,MAAM,IAAIC,UAAU,EACpBC,IAAI,IAAIC,QAAQ,EAChBC,SAAS,IAAIC,aAAa,EAC1BC,YAAY,IAAIC,gBAAgB,EAChCC,MAAM,IAAIC,UAAU,EACpBC,WAAW,IAAIC,eAAe,QACzB,qBAAqB;AAC5B,SAASC,WAAW,QAAQ,kBAAkB;AAC9C,OAAOC,WAAW,MAAM,4BAA4B;AACpD,OAAOC,gBAAgB,MAAM,iCAAiC;AAC9D,SAASC,wBAAwB,QAAQ,6BAA6B;;AAEtE;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAPA,SAAAC,MAAA,IAAAC,OAAA,EAAAC,QAAA,IAAAC,SAAA;AAQA,MAAMC,kBAAkB,GAAGA,CAAC;EAAEC,UAAU;EAAEC,SAAS;EAAEC;AAAQ,CAAC,KAAK;EAAAC,EAAA;EACjE,MAAMC,QAAQ,GAAGb,WAAW,CAAC,CAAC;;EAE9B;EACA,MAAM,CAACc,UAAU,EAAEC,aAAa,CAAC,GAAGnD,QAAQ,CAAC,CAAC,CAAC;EAC/C,MAAM,CAACoD,OAAO,EAAEC,UAAU,CAAC,GAAGrD,QAAQ,CAAC,KAAK,CAAC;EAC7C,MAAM,CAACsD,WAAW,EAAEC,cAAc,CAAC,GAAGvD,QAAQ,CAAC,KAAK,CAAC;EACrD,MAAM,CAACwD,aAAa,EAAEC,gBAAgB,CAAC,GAAGzD,QAAQ,CAAC,KAAK,CAAC;;EAEzD;EACA,MAAM,CAAC0D,IAAI,EAAEC,OAAO,CAAC,GAAG3D,QAAQ,CAAC,EAAE,CAAC;EACpC,MAAM,CAAC4D,MAAM,EAAEC,SAAS,CAAC,GAAG7D,QAAQ,CAAC,EAAE,CAAC;EACxC,MAAM,CAAC8D,YAAY,EAAEC,eAAe,CAAC,GAAG/D,QAAQ,CAAC,IAAI,CAAC;EACtD,MAAM,CAACgE,WAAW,EAAEC,cAAc,CAAC,GAAGjE,QAAQ,CAAC,EAAE,CAAC;;EAElD;EACA,MAAM,CAACkE,QAAQ,EAAEC,WAAW,CAAC,GAAGnE,QAAQ,CAAC;IACvCoE,OAAO,EAAE,EAAE;IACXC,YAAY,EAAE,EAAE;IAChBC,SAAS,EAAE;EACb,CAAC,CAAC;;EAEF;EACA,MAAM,CAACC,UAAU,EAAEC,aAAa,CAAC,GAAGxE,QAAQ,CAAC,CAAC,CAAC,CAAC;EAChD,MAAM,CAACyE,YAAY,EAAEC,eAAe,CAAC,GAAG1E,QAAQ,CAAC,CAAC,CAAC,CAAC;;EAEpD;EACA,MAAM2E,KAAK,GAAG,CAAC,gBAAgB,EAAE,iBAAiB,EAAE,gBAAgB,EAAE,UAAU,CAAC;;EAEjF;EACA1E,SAAS,CAAC,MAAM;IACd2E,QAAQ,CAAC,CAAC;EACZ,CAAC,EAAE,CAAC/B,UAAU,CAAC,CAAC;;EAEhB;EACA5C,SAAS,CAAC,MAAM;IACd,IAAIiD,UAAU,KAAK,CAAC,EAAE;MACpB2B,UAAU,CAAC,CAAC;IACd;EACF,CAAC,EAAE,CAAC3B,UAAU,EAAEL,UAAU,CAAC,CAAC;;EAE5B;EACA,MAAM+B,QAAQ,GAAG,MAAAA,CAAA,KAAY;IAC3B,IAAI;MACFrB,cAAc,CAAC,IAAI,CAAC;MACpB;MACA,MAAMuB,QAAQ,GAAG,MAAMzC,WAAW,CAAC0C,OAAO,CAAClC,UAAU,CAAC;;MAEtD;MACA;MACAc,OAAO,CAACmB,QAAQ,CAAC;IACnB,CAAC,CAAC,OAAOE,KAAK,EAAE;MACdC,OAAO,CAACD,KAAK,CAAC,kCAAkC,EAAEA,KAAK,CAAC;MACxDjC,OAAO,CAAC,mCAAmC,IAAIiC,KAAK,CAACE,OAAO,IAAI,oBAAoB,CAAC,CAAC;IACxF,CAAC,SAAS;MACR3B,cAAc,CAAC,KAAK,CAAC;IACvB;EACF,CAAC;;EAED;EACA,MAAMsB,UAAU,GAAG,MAAAA,CAAA,KAAY;IAC7B,IAAI;MACFpB,gBAAgB,CAAC,IAAI,CAAC;;MAEtB;MACA,MAAM0B,UAAU,GAAG,MAAM7C,gBAAgB,CAAC8C,SAAS,CAACvC,UAAU,CAAC;;MAE/D;MACA,IAAIwC,kBAAkB,GAAGF,UAAU,CAACG,MAAM,CAACC,MAAM,IAC/C,CAACA,MAAM,CAACC,YAAY,KAAK,aAAa,IAAID,MAAM,CAACC,YAAY,KAAK,QAAQ,KAC1ED,MAAM,CAACC,YAAY,KAAK,MAAM,IAAID,MAAM,CAACC,YAAY,KAAK,WAC5D,CAAC;;MAED;MACA,IAAI1B,YAAY,EAAE;QAChB;QACA,IAAIA,YAAY,CAAC2B,SAAS,IAAI3B,YAAY,CAAC4B,YAAY,IAAI5B,YAAY,CAAC6B,OAAO,EAAE;UAC/E,MAAMC,iBAAiB,GAAGP,kBAAkB,CAACC,MAAM,CAACC,MAAM,IACxDA,MAAM,CAACE,SAAS,KAAK3B,YAAY,CAAC2B,SAAS,IAC3CF,MAAM,CAACG,YAAY,KAAK5B,YAAY,CAAC4B,YAAY,IACjDH,MAAM,CAACI,OAAO,KAAK7B,YAAY,CAAC6B,OAClC,CAAC;;UAED;UACA,IAAIC,iBAAiB,CAACC,MAAM,GAAG,CAAC,EAAE;YAChCR,kBAAkB,GAAGO,iBAAiB;UACxC,CAAC,MAAM;YACLX,OAAO,CAACa,GAAG,CAAC,wEAAwE,CAAC;UACvF;QACF;;QAEA;QACAT,kBAAkB,CAACU,IAAI,CAAC,CAACC,CAAC,EAAEC,CAAC,KAAKA,CAAC,CAACC,aAAa,GAAGF,CAAC,CAACE,aAAa,CAAC;MACtE;MAEArC,SAAS,CAACwB,kBAAkB,CAAC;IAC/B,CAAC,CAAC,OAAOL,KAAK,EAAE;MACdC,OAAO,CAACD,KAAK,CAAC,sCAAsC,EAAEA,KAAK,CAAC;MAC5DjC,OAAO,CAAC,uCAAuC,IAAIiC,KAAK,CAACE,OAAO,IAAI,oBAAoB,CAAC,CAAC;IAC5F,CAAC,SAAS;MACRzB,gBAAgB,CAAC,KAAK,CAAC;IACzB;EACF,CAAC;;EAED;EACA,MAAM0C,oBAAoB,GAAG,MAAAA,CAAA,KAAY;IACvC,IAAI,CAACnC,WAAW,CAACoC,IAAI,CAAC,CAAC,EAAE;MACvBrD,OAAO,CAAC,6BAA6B,CAAC;MACtC;IACF;IAEA,IAAI;MACFQ,cAAc,CAAC,IAAI,CAAC;MACpB,MAAM8C,QAAQ,GAAG,MAAMhE,WAAW,CAACiE,WAAW,CAACzD,UAAU,EAAEmB,WAAW,CAACoC,IAAI,CAAC,CAAC,CAAC;;MAE9E;MACA,IAAIC,QAAQ,CAACE,mBAAmB,KAAK,YAAY,IAAKF,QAAQ,CAACG,eAAe,IAAIH,QAAQ,CAACG,eAAe,GAAG,CAAE,EAAE;QAC/G;QACA,MAAMtB,OAAO,GAAG,WAAWmB,QAAQ,CAACjC,OAAO,wBAAwBiC,QAAQ,CAACG,eAAe,IAAI,CAAC;AACxG;AACA;AACA;AACA;AACA,0BAA0B;QAElB,MAAMC,UAAU,GAAGC,MAAM,CAACC,OAAO,CAACzB,OAAO,CAAC;QAE1C,IAAIuB,UAAU,EAAE;UACd;UACA;UACAxD,QAAQ,CAAC,mCAAmCJ,UAAU,IAAIwD,QAAQ,CAACjC,OAAO,EAAE,CAAC;QAC/E;QAEAb,cAAc,CAAC,KAAK,CAAC;QACrB;MACF;;MAEA;MACA,IAAI8C,QAAQ,CAACO,sBAAsB,KAAK,CAAC,EAAE;QACzC;MAAA;;MAGF;MACAC,gBAAgB,CAACR,QAAQ,CAAC;IAC5B,CAAC,CAAC,OAAOrB,KAAK,EAAE;MACdC,OAAO,CAACD,KAAK,CAAC,gCAAgC,EAAEA,KAAK,CAAC;MACtDjC,OAAO,CAAC,2CAA2C,IAAIiC,KAAK,CAACE,OAAO,IAAI,oBAAoB,CAAC,CAAC;IAChG,CAAC,SAAS;MACR3B,cAAc,CAAC,KAAK,CAAC;IACvB;EACF,CAAC;;EAED;EACA,MAAMsD,gBAAgB,GAAIC,IAAI,IAAK;IACjC;IACA,IAAIA,IAAI,CAACP,mBAAmB,KAAK,YAAY,IAAKO,IAAI,CAACN,eAAe,IAAIM,IAAI,CAACN,eAAe,GAAG,CAAE,EAAE;MACnG;MACA,MAAMtB,OAAO,GAAG,WAAW4B,IAAI,CAAC1C,OAAO,wBAAwB0C,IAAI,CAACN,eAAe,IAAI,CAAC;AAC9F;AACA;AACA;AACA;AACA,0BAA0B;MAEpB,MAAMC,UAAU,GAAGC,MAAM,CAACC,OAAO,CAACzB,OAAO,CAAC;MAE1C,IAAIuB,UAAU,EAAE;QACd;QACA;QACAxD,QAAQ,CAAC,mCAAmCJ,UAAU,IAAIiE,IAAI,CAAC1C,OAAO,EAAE,CAAC;MAC3E;MAEA;IACF;IACA;IAAA,KACK,IAAI0C,IAAI,CAACF,sBAAsB,KAAK,CAAC,EAAE;MAC1C;MACA,IAAIF,MAAM,CAACC,OAAO,CAAC,WAAWG,IAAI,CAAC1C,OAAO,0CAA0C,CAAC,EAAE;QACrF;QACA2C,eAAe,CAACD,IAAI,CAAC1C,OAAO,CAAC,CAAC4C,IAAI,CAAC,MAAM;UACvC;UACA,MAAMC,WAAW,GAAG;YAAE,GAAGH,IAAI;YAAEF,sBAAsB,EAAE;UAAE,CAAC;UAC1D7C,eAAe,CAACkD,WAAW,CAAC;UAC5B9C,WAAW,CAAC;YACV,GAAGD,QAAQ;YACXE,OAAO,EAAE6C,WAAW,CAAC7C,OAAO;YAC5BC,YAAY,EAAE;UAChB,CAAC,CAAC;UACF;UACAlB,aAAa,CAAC,CAAC,CAAC;QAClB,CAAC,CAAC,CAAC+D,KAAK,CAAClC,KAAK,IAAI;UAChBC,OAAO,CAACD,KAAK,CAAC,iDAAiD,EAAEA,KAAK,CAAC;UACvEjC,OAAO,CAAC,kDAAkD,IAAIiC,KAAK,CAACE,OAAO,IAAI,oBAAoB,CAAC,CAAC;QACvG,CAAC,CAAC;MACJ,CAAC,MAAM;QACL;QACA;MACF;IACF,CAAC,MAAM;MACL;MACAnB,eAAe,CAAC+C,IAAI,CAAC;MACrB3C,WAAW,CAAC;QACV,GAAGD,QAAQ;QACXE,OAAO,EAAE0C,IAAI,CAAC1C,OAAO;QACrBC,YAAY,EAAE;MAChB,CAAC,CAAC;MACF;MACAlB,aAAa,CAAC,CAAC,CAAC;IAClB;EACF,CAAC;;EAED;EACA,MAAM4D,eAAe,GAAG,MAAOI,MAAM,IAAK;IACxC,IAAI;MACF;MACA,MAAM9E,WAAW,CAAC0E,eAAe,CAAClE,UAAU,EAAEsE,MAAM,CAAC;MACrDrE,SAAS,CAAC,QAAQqE,MAAM,0BAA0B,CAAC;MACnD,OAAO,IAAI;IACb,CAAC,CAAC,OAAOnC,KAAK,EAAE;MACdC,OAAO,CAACD,KAAK,CAAC,iDAAiD,EAAEA,KAAK,CAAC;MACvEjC,OAAO,CAAC,kDAAkD,IAAIiC,KAAK,CAACE,OAAO,IAAI,oBAAoB,CAAC,CAAC;MACrG,MAAMF,KAAK;IACb;EACF,CAAC;;EAED;EACA,MAAMoC,gBAAgB,GAAIC,CAAC,IAAK;IAC9B,MAAM;MAAEC,IAAI;MAAEC;IAAM,CAAC,GAAGF,CAAC,CAACG,MAAM;IAChCrD,WAAW,CAAC;MACV,GAAGD,QAAQ;MACX,CAACoD,IAAI,GAAGC;IACV,CAAC,CAAC;;IAEF;IACAE,aAAa,CAACH,IAAI,EAAEC,KAAK,CAAC;EAC5B,CAAC;;EAED;EACA,MAAME,aAAa,GAAGA,CAACH,IAAI,EAAEC,KAAK,KAAK;IACrC,IAAIvC,KAAK,GAAG,IAAI;IAChB,IAAI0C,OAAO,GAAG,IAAI;IAElB,IAAIJ,IAAI,KAAK,cAAc,EAAE;MAC3B,IAAI,CAACC,KAAK,IAAIA,KAAK,CAACnB,IAAI,CAAC,CAAC,KAAK,EAAE,EAAE;QACjCpB,KAAK,GAAG,uCAAuC;MACjD,CAAC,MAAM,IAAI2C,KAAK,CAACC,UAAU,CAACL,KAAK,CAAC,CAAC,IAAIK,UAAU,CAACL,KAAK,CAAC,IAAI,CAAC,EAAE;QAC7DvC,KAAK,GAAG,sCAAsC;MAChD,CAAC,MAAM,IAAIlB,YAAY,IAAIA,YAAY,CAAC+D,aAAa,IAAID,UAAU,CAACL,KAAK,CAAC,GAAGK,UAAU,CAAC9D,YAAY,CAAC+D,aAAa,CAAC,EAAE;QACnHH,OAAO,GAAG,kDAAkD;MAC9D;IACF;;IAEA;IACAlD,aAAa,CAACsD,IAAI,KAAK;MACrB,GAAGA,IAAI;MACP,CAACR,IAAI,GAAGtC;IACV,CAAC,CAAC,CAAC;;IAEH;IACAN,eAAe,CAACoD,IAAI,KAAK;MACvB,GAAGA,IAAI;MACP,CAACR,IAAI,GAAGI;IACV,CAAC,CAAC,CAAC;IAEH,OAAO,CAAC1C,KAAK;EACf,CAAC;;EAED;EACA,MAAM+C,YAAY,GAAGA,CAAA,KAAM;IACzB,IAAIC,OAAO,GAAG,IAAI;IAClB,MAAMC,MAAM,GAAG,CAAC,CAAC;;IAEjB;IACA,IAAI,CAAC/D,QAAQ,CAACG,YAAY,IAAIH,QAAQ,CAACG,YAAY,CAAC+B,IAAI,CAAC,CAAC,KAAK,EAAE,EAAE;MACjE6B,MAAM,CAAC5D,YAAY,GAAG,uCAAuC;MAC7D2D,OAAO,GAAG,KAAK;IACjB,CAAC,MAAM,IAAIL,KAAK,CAACC,UAAU,CAAC1D,QAAQ,CAACG,YAAY,CAAC,CAAC,IAAIuD,UAAU,CAAC1D,QAAQ,CAACG,YAAY,CAAC,IAAI,CAAC,EAAE;MAC7F4D,MAAM,CAAC5D,YAAY,GAAG,sCAAsC;MAC5D2D,OAAO,GAAG,KAAK;IACjB;IAEAxD,aAAa,CAACyD,MAAM,CAAC;IACrB,OAAOD,OAAO;EAChB,CAAC;;EAED;EACA,MAAME,UAAU,GAAGA,CAAA,KAAM;IACvB,IAAIhF,UAAU,KAAK,CAAC,EAAE;MACpB;MACA,IAAI,CAAC6E,YAAY,CAAC,CAAC,EAAE;QACnB;MACF;IACF;IAEA5E,aAAa,CAAEgF,cAAc,IAAKA,cAAc,GAAG,CAAC,CAAC;EACvD,CAAC;;EAED;EACA,MAAMC,UAAU,GAAGA,CAAA,KAAM;IACvBjF,aAAa,CAAEgF,cAAc,IAAKA,cAAc,GAAG,CAAC,CAAC;EACvD,CAAC;;EAED;EACA,MAAME,WAAW,GAAGA,CAAA,KAAM;IACxBlF,aAAa,CAAC,CAAC,CAAC;IAChBY,eAAe,CAAC,IAAI,CAAC;IACrBE,cAAc,CAAC,EAAE,CAAC;IAClBE,WAAW,CAAC;MACVC,OAAO,EAAE,EAAE;MACXC,YAAY,EAAE,EAAE;MAChBC,SAAS,EAAE;IACb,CAAC,CAAC;IACFE,aAAa,CAAC,CAAC,CAAC,CAAC;IACjBE,eAAe,CAAC,CAAC,CAAC,CAAC;EACrB,CAAC;;EAED;EACA,MAAM4D,2BAA2B,GAAGA,CAACC,WAAW,EAAEC,YAAY,KAAK;IACjE,IAAI,CAACD,WAAW,IAAIX,UAAU,CAACW,WAAW,CAAC,IAAI,CAAC,EAAE;MAChD,OAAO,eAAe;IACxB;IAEA,IAAIX,UAAU,CAACW,WAAW,CAAC,IAAIX,UAAU,CAACY,YAAY,CAAC,EAAE;MACvD,OAAO,YAAY;IACrB;IAEA,OAAO,UAAU;EACnB,CAAC;;EAED;EACA,MAAMC,YAAY,GAAG,MAAAA,CAAA,KAAY;IAC/B,IAAI;MACFpF,UAAU,CAAC,IAAI,CAAC;;MAEhB;MACA,IAAI,CAAC0E,YAAY,CAAC,CAAC,EAAE;QACnB1E,UAAU,CAAC,KAAK,CAAC;QACjB;MACF;;MAEA;MACA,MAAMkF,WAAW,GAAGX,UAAU,CAAC1D,QAAQ,CAACG,YAAY,CAAC;MACrD,MAAMqE,QAAQ,GAAGxE,QAAQ,CAACI,SAAS,IAAI,IAAI;;MAE3C;MACA,MAAMqE,kBAAkB,GAAGL,2BAA2B,CAACC,WAAW,EAAEzE,YAAY,CAAC+D,aAAa,CAAC;;MAE/F;MACA5C,OAAO,CAACa,GAAG,CAAC,aAAa,EAAE;QACzBjD,UAAU;QACVsE,MAAM,EAAEjD,QAAQ,CAACE,OAAO;QACxBmE,WAAW;QACXG,QAAQ;QACRC;MACF,CAAC,CAAC;;MAEF;MACA,MAAMtG,WAAW,CAACuG,iBAAiB,CACjC/F,UAAU,EACVqB,QAAQ,CAACE,OAAO,EAChBmE,WAAW,EACXG,QACF,CAAC;;MAED;MACA5F,SAAS,CAAC,qDAAqD6F,kBAAkB,EAAE,CAAC;;MAEpF;MACAN,WAAW,CAAC,CAAC;;MAEb;MACAzD,QAAQ,CAAC,CAAC;IACZ,CAAC,CAAC,OAAOI,KAAK,EAAE;MACdC,OAAO,CAACD,KAAK,CAAC,mDAAmD,EAAEA,KAAK,CAAC;MACzEjC,OAAO,CAAC,oDAAoD,IAAIiC,KAAK,CAACE,OAAO,IAAI,oBAAoB,CAAC,CAAC;IACzG,CAAC,SAAS;MACR7B,UAAU,CAAC,KAAK,CAAC;IACnB;EACF,CAAC;;EAED;EACA,MAAMwF,WAAW,GAAGA,CAAA,KAAM;IACxB,oBACEpG,OAAA,CAACvC,GAAG;MAAA4I,QAAA,gBACFrG,OAAA,CAACrC,UAAU;QAAC2I,OAAO,EAAC,IAAI;QAACC,YAAY;QAAAF,QAAA,EAAC;MAEtC;QAAAG,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAY,CAAC,eAGb3G,OAAA,CAACtC,KAAK;QAACkJ,EAAE,EAAE;UAAEC,CAAC,EAAE,CAAC;UAAEC,EAAE,EAAE;QAAE,CAAE;QAAAT,QAAA,gBACzBrG,OAAA,CAACrC,UAAU;UAAC2I,OAAO,EAAC,WAAW;UAACC,YAAY;UAAAF,QAAA,EAAC;QAE7C;UAAAG,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAY,CAAC,eACb3G,OAAA,CAAC/B,IAAI;UAAC8I,SAAS;UAACC,OAAO,EAAE,CAAE;UAACC,UAAU,EAAC,QAAQ;UAAAZ,QAAA,gBAC7CrG,OAAA,CAAC/B,IAAI;YAACiJ,IAAI;YAACC,EAAE,EAAE,CAAE;YAAAd,QAAA,eACfrG,OAAA,CAACpC,SAAS;cACRwJ,SAAS;cACTC,KAAK,EAAC,SAAS;cACff,OAAO,EAAC,UAAU;cAClBxB,KAAK,EAAEvD,WAAY;cACnB+F,QAAQ,EAAG1C,CAAC,IAAKpD,cAAc,CAACoD,CAAC,CAACG,MAAM,CAACD,KAAK,CAAE;cAChDyC,WAAW,EAAC;YAAyB;cAAAf,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACtC;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACE,CAAC,eACP3G,OAAA,CAAC/B,IAAI;YAACiJ,IAAI;YAACC,EAAE,EAAE,CAAE;YAAAd,QAAA,eACfrG,OAAA,CAACnC,MAAM;cACLuJ,SAAS;cACTd,OAAO,EAAC,WAAW;cACnBkB,KAAK,EAAC,SAAS;cACfC,OAAO,EAAE/D,oBAAqB;cAC9BgE,QAAQ,EAAE7G,WAAW,IAAI,CAACU,WAAW,CAACoC,IAAI,CAAC,CAAE;cAC7CgE,SAAS,EAAE9G,WAAW,gBAAGb,OAAA,CAACrB,gBAAgB;gBAACiJ,IAAI,EAAE;cAAG;gBAAApB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,gBAAG3G,OAAA,CAAChB,UAAU;gBAAAwH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAE;cAAAN,QAAA,EAC1E;YAED;cAAAG,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACL,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACF,CAAC,eAGR3G,OAAA,CAACtC,KAAK;QAACkJ,EAAE,EAAE;UAAEC,CAAC,EAAE;QAAE,CAAE;QAAAR,QAAA,gBAClBrG,OAAA,CAACrC,UAAU;UAAC2I,OAAO,EAAC,WAAW;UAACC,YAAY;UAAAF,QAAA,EAAC;QAE7C;UAAAG,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAY,CAAC,EAEZ9F,WAAW,gBACVb,OAAA,CAACvC,GAAG;UAACmJ,EAAE,EAAE;YAAEiB,OAAO,EAAE,MAAM;YAAEC,cAAc,EAAE,QAAQ;YAAEC,EAAE,EAAE;UAAE,CAAE;UAAA1B,QAAA,eAC5DrG,OAAA,CAACrB,gBAAgB;YAAA6H,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACjB,CAAC,GACJ1F,IAAI,CAACmC,MAAM,KAAK,CAAC,gBACnBpD,OAAA,CAACtB,KAAK;UAACsJ,QAAQ,EAAC,MAAM;UAAA3B,QAAA,EAAC;QAEvB;UAAAG,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAO,CAAC,gBAER3G,OAAA,CAAC1B,IAAI;UAACsI,EAAE,EAAE;YAAEqB,SAAS,EAAE,OAAO;YAAEC,QAAQ,EAAE;UAAO,CAAE;UAAA7B,QAAA,EAChDpF,IAAI,CAACkH,GAAG,CAAE9D,IAAI,iBACbrE,OAAA,CAAC1C,KAAK,CAAC2C,QAAQ;YAAAoG,QAAA,gBACbrG,OAAA,CAACzB,QAAQ;cAAC6J,MAAM;cAACX,OAAO,EAAEA,CAAA,KAAMrD,gBAAgB,CAACC,IAAI,CAAE;cAAAgC,QAAA,eACrDrG,OAAA,CAACxB,YAAY;gBACX6J,OAAO,eACLrI,OAAA,CAACvC,GAAG;kBAACmJ,EAAE,EAAE;oBAAEiB,OAAO,EAAE,MAAM;oBAAEZ,UAAU,EAAE;kBAAS,CAAE;kBAAAZ,QAAA,gBACjDrG,OAAA,CAACrC,UAAU;oBAAC2I,OAAO,EAAC,WAAW;oBAAAD,QAAA,EAAEhC,IAAI,CAAC1C;kBAAO;oBAAA6E,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAa,CAAC,EAC1DtC,IAAI,CAACF,sBAAsB,KAAK,CAAC,gBAChCnE,OAAA,CAAClB,IAAI;oBACH8I,IAAI,EAAC,OAAO;oBACZP,KAAK,EAAC,OAAO;oBACbG,KAAK,EAAC,OAAO;oBACbZ,EAAE,EAAE;sBAAE0B,EAAE,EAAE;oBAAE;kBAAE;oBAAA9B,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACf,CAAC,GACAtC,IAAI,CAACP,mBAAmB,KAAK,YAAY,IAAKO,IAAI,CAACN,eAAe,IAAIM,IAAI,CAACN,eAAe,GAAG,CAAE,gBACjG/D,OAAA,CAAClB,IAAI;oBACH8I,IAAI,EAAC,OAAO;oBACZP,KAAK,EAAC,YAAY;oBAClBG,KAAK,EAAC,SAAS;oBACfZ,EAAE,EAAE;sBAAE0B,EAAE,EAAE;oBAAE;kBAAE;oBAAA9B,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACf,CAAC,gBAEF3G,OAAA,CAAClB,IAAI;oBACH8I,IAAI,EAAC,OAAO;oBACZP,KAAK,EAAEhD,IAAI,CAACP,mBAAoB;oBAChC0D,KAAK,EAAEnD,IAAI,CAACP,mBAAmB,KAAK,UAAU,GAAG,SAAS,GAAG,SAAU;oBACvE8C,EAAE,EAAE;sBAAE0B,EAAE,EAAE;oBAAE;kBAAE;oBAAA9B,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACf,CACF;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACE,CACN;gBACD4B,SAAS,eACPvI,OAAA,CAAAE,SAAA;kBAAAmG,QAAA,gBACErG,OAAA,CAACrC,UAAU;oBAAC2I,OAAO,EAAC,OAAO;oBAACkC,SAAS,EAAC,MAAM;oBAAAnC,QAAA,GACzChC,IAAI,CAACrB,SAAS,IAAI,KAAK,EAAC,KAAG,EAACqB,IAAI,CAACpB,YAAY,IAAI,KAAK,EAAC,KAAG,EAACoB,IAAI,CAACnB,OAAO,IAAI,KAAK;kBAAA;oBAAAsD,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACvE,CAAC,eACb3G,OAAA;oBAAAwG,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAK,CAAC,eACN3G,OAAA,CAACrC,UAAU;oBAAC2I,OAAO,EAAC,OAAO;oBAACkC,SAAS,EAAC,MAAM;oBAAAnC,QAAA,GAAC,MACvC,EAAChC,IAAI,CAACoE,mBAAmB,IAAI,KAAK,EAAC,QAAM,EAACpE,IAAI,CAACqE,iBAAiB,IAAI,KAAK;kBAAA;oBAAAlC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACnE,CAAC,eACb3G,OAAA;oBAAAwG,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAK,CAAC,eACN3G,OAAA,CAACrC,UAAU;oBAAC2I,OAAO,EAAC,OAAO;oBAACkC,SAAS,EAAC,MAAM;oBAAAnC,QAAA,GAAC,iBAC5B,EAAChC,IAAI,CAACe,aAAa,IAAI,KAAK,EAAC,mBAAiB,EAACf,IAAI,CAACN,eAAe,IAAI,GAAG;kBAAA;oBAAAyC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAC/E,CAAC;gBAAA,eACb;cACH;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACF;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACM,CAAC,eACX3G,OAAA,CAACvB,OAAO;cAAA+H,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC;UAAA,GA/CQtC,IAAI,CAAC1C,OAAO;YAAA6E,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAgDjB,CACjB;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACE,CACP;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACI,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACL,CAAC;EAEV,CAAC;;EAED;EACA,MAAMgC,WAAW,GAAGA,CAAA,KAAM;IACxB,IAAI,CAACtH,YAAY,EAAE,OAAO,IAAI;IAE9B,oBACErB,OAAA,CAACvC,GAAG;MAAA4I,QAAA,gBACFrG,OAAA,CAACrC,UAAU;QAAC2I,OAAO,EAAC,IAAI;QAACC,YAAY;QAAAF,QAAA,EAAC;MAEtC;QAAAG,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAY,CAAC,eAEb3G,OAAA,CAACtC,KAAK;QAACkJ,EAAE,EAAE;UAAEC,CAAC,EAAE,CAAC;UAAEC,EAAE,EAAE;QAAE,CAAE;QAAAT,QAAA,gBACzBrG,OAAA,CAACrC,UAAU;UAAC2I,OAAO,EAAC,WAAW;UAACC,YAAY;UAAAF,QAAA,EAAC;QAE7C;UAAAG,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAY,CAAC,eAEb3G,OAAA,CAAC/B,IAAI;UAAC8I,SAAS;UAACC,OAAO,EAAE,CAAE;UAAAX,QAAA,gBACzBrG,OAAA,CAAC/B,IAAI;YAACiJ,IAAI;YAACC,EAAE,EAAE,EAAG;YAACyB,EAAE,EAAE,CAAE;YAAAvC,QAAA,gBACvBrG,OAAA,CAACrC,UAAU;cAAC2I,OAAO,EAAC,OAAO;cAAAD,QAAA,gBACzBrG,OAAA;gBAAAqG,QAAA,EAAQ;cAAQ;gBAAAG,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC,KAAC,EAACtF,YAAY,CAACM,OAAO;YAAA;cAAA6E,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACrC,CAAC,eACb3G,OAAA,CAACrC,UAAU;cAAC2I,OAAO,EAAC,OAAO;cAAAD,QAAA,gBACzBrG,OAAA;gBAAAqG,QAAA,EAAQ;cAAU;gBAAAG,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC,KAAC,EAACtF,YAAY,CAAC2B,SAAS,IAAI,KAAK;YAAA;cAAAwD,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAClD,CAAC,eACb3G,OAAA,CAACrC,UAAU;cAAC2I,OAAO,EAAC,OAAO;cAAAD,QAAA,gBACzBrG,OAAA;gBAAAqG,QAAA,EAAQ;cAAW;gBAAAG,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC,KAAC,EAACtF,YAAY,CAAC4B,YAAY,IAAI,KAAK,EAAC,KAAG,EAAC5B,YAAY,CAAC6B,OAAO,IAAI,KAAK;YAAA;cAAAsD,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACxF,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACT,CAAC,eACP3G,OAAA,CAAC/B,IAAI;YAACiJ,IAAI;YAACC,EAAE,EAAE,EAAG;YAACyB,EAAE,EAAE,CAAE;YAAAvC,QAAA,gBACvBrG,OAAA,CAACrC,UAAU;cAAC2I,OAAO,EAAC,OAAO;cAAAD,QAAA,gBACzBrG,OAAA;gBAAAqG,QAAA,EAAQ;cAAoB;gBAAAG,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC,KAAC,EAACtF,YAAY,CAACoH,mBAAmB,IAAI,KAAK;YAAA;cAAAjC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACtE,CAAC,eACb3G,OAAA,CAACrC,UAAU;cAAC2I,OAAO,EAAC,OAAO;cAAAD,QAAA,gBACzBrG,OAAA;gBAAAqG,QAAA,EAAQ;cAAkB;gBAAAG,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC,KAAC,EAACtF,YAAY,CAACqH,iBAAiB,IAAI,KAAK;YAAA;cAAAlC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAClE,CAAC,eACb3G,OAAA,CAACrC,UAAU;cAAC2I,OAAO,EAAC,OAAO;cAAAD,QAAA,gBACzBrG,OAAA;gBAAAqG,QAAA,EAAQ;cAAc;gBAAAG,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC,KAAC,EAACtF,YAAY,CAAC+D,aAAa,IAAI,KAAK;YAAA;cAAAoB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC1D,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACT,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACF,CAAC,eAER3G,OAAA,CAACtC,KAAK;QAACkJ,EAAE,EAAE;UAAEC,CAAC,EAAE;QAAE,CAAE;QAAAR,QAAA,gBAClBrG,OAAA,CAACrC,UAAU;UAAC2I,OAAO,EAAC,WAAW;UAACC,YAAY;UAAAF,QAAA,EAAC;QAE7C;UAAAG,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAY,CAAC,eAEb3G,OAAA,CAACpC,SAAS;UACRwJ,SAAS;UACTC,KAAK,EAAC,cAAc;UACpBf,OAAO,EAAC,UAAU;UAClBzB,IAAI,EAAC,cAAc;UACnBgE,IAAI,EAAC,QAAQ;UACb/D,KAAK,EAAErD,QAAQ,CAACG,YAAa;UAC7B0F,QAAQ,EAAE3C,gBAAiB;UAC3BpC,KAAK,EAAE,CAAC,CAACT,UAAU,CAACF,YAAa;UACjCkH,UAAU,EAAEhH,UAAU,CAACF,YAAY,IAAII,YAAY,CAACJ,YAAa;UACjEmH,mBAAmB,EAAE;YACnBnC,EAAE,EAAE;cAAEY,KAAK,EAAExF,YAAY,CAACJ,YAAY,IAAI,CAACE,UAAU,CAACF,YAAY,GAAG,cAAc,GAAG;YAAa;UACrG,CAAE;UACFgF,EAAE,EAAE;YAAEE,EAAE,EAAE;UAAE;QAAE;UAAAN,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACf,CAAC,eAEF3G,OAAA,CAACtB,KAAK;UAACsJ,QAAQ,EAAC,MAAM;UAACpB,EAAE,EAAE;YAAEoC,EAAE,EAAE;UAAE,CAAE;UAAA3C,QAAA,EAAC;QAEtC;UAAAG,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAO,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACL,CAAC;EAEV,CAAC;;EAED;EACA,MAAMsC,WAAW,GAAGA,CAAA,KAAM;IACxB;IACA,MAAMC,eAAe,GAAIjD,QAAQ,IAAK;MACpC;MACA,IAAIA,QAAQ,IAAIA,QAAQ,CAACkD,QAAQ,CAAC,IAAI,CAAC,EAAE;QACvC,OAAOlD,QAAQ,CAACmD,KAAK,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;MAChC;MACA,OAAOnD,QAAQ;IACjB,CAAC;;IAED;IACA,MAAMoD,iBAAiB,GAAIC,YAAY,IAAK;MAC1C,OAAO,IAAIlJ,UAAU,KAAKkJ,YAAY,EAAE;IAC1C,CAAC;;IAED;IACA,MAAMC,mBAAmB,GAAIzG,MAAM,IAAK;MACtC,IAAI,CAACA,MAAM,IAAI,CAACrB,QAAQ,CAACG,YAAY,EAAE,OAAO,IAAI;MAClD,OAAOuD,UAAU,CAACrC,MAAM,CAACW,aAAa,CAAC,IAAI0B,UAAU,CAAC1D,QAAQ,CAACG,YAAY,CAAC;IAC9E,CAAC;;IAED;IACA,MAAM4H,uBAAuB,GAAI5E,CAAC,IAAK;MACrC,MAAM0E,YAAY,GAAG1E,CAAC,CAACG,MAAM,CAACD,KAAK,CAACnB,IAAI,CAAC,CAAC;;MAE1C;MACA,IAAI2F,YAAY,CAACG,WAAW,CAAC,CAAC,KAAK,GAAG,EAAE;QACtC;QACA/H,WAAW,CAAC;UACV,GAAGD,QAAQ;UACXI,SAAS,EAAE;QACb,CAAC,CAAC;QACFE,aAAa,CAAC;UACZ,GAAGD,UAAU;UACb4H,eAAe,EAAE;QACnB,CAAC,CAAC;QACF;MACF;MAEA,IAAIJ,YAAY,CAACG,WAAW,CAAC,CAAC,KAAK,GAAG,EAAE;QACtC;QACA/H,WAAW,CAAC;UACV,GAAGD,QAAQ;UACXI,SAAS,EAAE;QACb,CAAC,CAAC;QACFE,aAAa,CAAC;UACZ,GAAGD,UAAU;UACb4H,eAAe,EAAE;QACnB,CAAC,CAAC;QACF;MACF;MAEA,IAAIJ,YAAY,EAAE;QAChB;QACA,MAAMK,gBAAgB,GAAGN,iBAAiB,CAACC,YAAY,CAAC;;QAExD;QACA,MAAMM,eAAe,GAAGzI,MAAM,CAAC0I,IAAI,CAACrG,CAAC,IAAIA,CAAC,CAAC3B,SAAS,KAAK8H,gBAAgB,CAAC;QAE1E,IAAIC,eAAe,EAAE;UACnB;UACA,IAAIA,eAAe,CAAC7G,YAAY,KAAK,MAAM,IAAI6G,eAAe,CAAC7G,YAAY,KAAK,WAAW,EAAE;YAC3FhB,aAAa,CAAC;cACZ,GAAGD,UAAU;cACb4H,eAAe,EAAE,aAAaJ,YAAY,eAAeM,eAAe,CAAC7G,YAAY;YACvF,CAAC,CAAC;YACF;UACF;;UAEA;UACA,IAAIwG,mBAAmB,CAACK,eAAe,CAAC,EAAE;YACxC;YACAlI,WAAW,CAAC;cACV,GAAGD,QAAQ;cACXI,SAAS,EAAE8H;YACb,CAAC,CAAC;YACF5H,aAAa,CAAC;cACZ,GAAGD,UAAU;cACb4H,eAAe,EAAE;YACnB,CAAC,CAAC;UACJ,CAAC,MAAM;YACL;YACA3H,aAAa,CAAC;cACZ,GAAGD,UAAU;cACb4H,eAAe,EAAE,aAAaJ,YAAY,sCAAsCM,eAAe,CAACnG,aAAa;YAC/G,CAAC,CAAC;UACJ;QACF,CAAC,MAAM;UACL;UACA1B,aAAa,CAAC;YACZ,GAAGD,UAAU;YACb4H,eAAe,EAAE,UAAUJ,YAAY;UACzC,CAAC,CAAC;QACJ;MACF,CAAC,MAAM;QACL;QACA5H,WAAW,CAAC;UACV,GAAGD,QAAQ;UACXI,SAAS,EAAE;QACb,CAAC,CAAC;QACFE,aAAa,CAAC;UACZ,GAAGD,UAAU;UACb4H,eAAe,EAAE;QACnB,CAAC,CAAC;MACJ;IACF,CAAC;IAED,oBACE1J,OAAA,CAACvC,GAAG;MAAA4I,QAAA,gBACFrG,OAAA,CAACrC,UAAU;QAAC2I,OAAO,EAAC,IAAI;QAACC,YAAY;QAAAF,QAAA,EAAC;MAEtC;QAAAG,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAY,CAAC,eAEb3G,OAAA,CAACtC,KAAK;QAACkJ,EAAE,EAAE;UAAEC,CAAC,EAAE;QAAE,CAAE;QAAAR,QAAA,gBAClBrG,OAAA,CAACrC,UAAU;UAAC2I,OAAO,EAAC,OAAO;UAACwD,SAAS;UAAAzD,QAAA,EAAC;QAEtC;UAAAG,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAY,CAAC,EAEZ5F,aAAa,gBACZf,OAAA,CAACvC,GAAG;UAACmJ,EAAE,EAAE;YAAEiB,OAAO,EAAE,MAAM;YAAEC,cAAc,EAAE,QAAQ;YAAEC,EAAE,EAAE;UAAE,CAAE;UAAA1B,QAAA,eAC5DrG,OAAA,CAACrB,gBAAgB;YAAA6H,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACjB,CAAC,gBAEN3G,OAAA,CAACvC,GAAG;UAAA4I,QAAA,gBAEFrG,OAAA,CAAC/B,IAAI;YAAC8I,SAAS;YAACC,OAAO,EAAE,CAAE;YAACJ,EAAE,EAAE;cAAEE,EAAE,EAAE;YAAE,CAAE;YAAAT,QAAA,gBACxCrG,OAAA,CAAC/B,IAAI;cAACiJ,IAAI;cAACC,EAAE,EAAE,EAAG;cAACyB,EAAE,EAAE,CAAE;cAAAvC,QAAA,eACvBrG,OAAA,CAACpC,SAAS;gBACRwJ,SAAS;gBACTC,KAAK,EAAC,yBAAyB;gBAC/Bf,OAAO,EAAC,UAAU;gBAClBiB,WAAW,EAAC,qDAAqD;gBACjEuB,UAAU,EAAEhH,UAAU,CAAC4H,eAAe,IAAI,4GAA6G;gBACvJnH,KAAK,EAAE,CAAC,CAACT,UAAU,CAAC4H,eAAgB;gBACpCK,MAAM,EAAEP;cAAwB;gBAAAhD,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACjC;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACE,CAAC,eACP3G,OAAA,CAAC/B,IAAI;cAACiJ,IAAI;cAACC,EAAE,EAAE,EAAG;cAACyB,EAAE,EAAE,CAAE;cAAAvC,QAAA,eACvBrG,OAAA,CAACrC,UAAU;gBAAC2I,OAAO,EAAC,OAAO;gBAACM,EAAE,EAAE;kBAAEoC,EAAE,EAAE;gBAAE,CAAE;gBAAA3C,QAAA,GAAC,sBACrB,EAAC5E,QAAQ,CAACI,SAAS,KAAK,cAAc,GAAG,cAAc,GAAGJ,QAAQ,CAACI,SAAS,IAAI,GAAG;cAAA;gBAAA2E,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC7F;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACT,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC,eAGP3G,OAAA,CAACrC,UAAU;YAAC2I,OAAO,EAAC,WAAW;YAACC,YAAY;YAACK,EAAE,EAAE;cAAEoC,EAAE,EAAE,CAAC;cAAEgB,UAAU,EAAE;YAAO,CAAE;YAAA3D,QAAA,EAAC;UAEhF;YAAAG,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAY,CAAC,EAEZxF,MAAM,CAACiC,MAAM,GAAG,CAAC,gBAChBpD,OAAA,CAACvC,GAAG;YAACmJ,EAAE,EAAE;cAAEqD,SAAS,EAAE;YAAO,CAAE;YAAA5D,QAAA,eAC7BrG,OAAA;cAAOkK,KAAK,EAAE;gBAAEC,KAAK,EAAE,MAAM;gBAAEC,cAAc,EAAE,UAAU;gBAAEC,YAAY,EAAE;cAAO,CAAE;cAAAhE,QAAA,gBAChFrG,OAAA;gBAAAqG,QAAA,eACErG,OAAA;kBAAIkK,KAAK,EAAE;oBAAEI,YAAY,EAAE,gBAAgB;oBAAEC,eAAe,EAAE;kBAAU,CAAE;kBAAAlE,QAAA,gBACxErG,OAAA;oBAAIkK,KAAK,EAAE;sBAAEM,OAAO,EAAE,KAAK;sBAAEC,SAAS,EAAE;oBAAO,CAAE;oBAAApE,QAAA,EAAC;kBAAE;oBAAAG,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAI,CAAC,eACzD3G,OAAA;oBAAIkK,KAAK,EAAE;sBAAEM,OAAO,EAAE,KAAK;sBAAEC,SAAS,EAAE;oBAAO,CAAE;oBAAApE,QAAA,EAAC;kBAAS;oBAAAG,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAI,CAAC,eAChE3G,OAAA;oBAAIkK,KAAK,EAAE;sBAAEM,OAAO,EAAE,KAAK;sBAAEC,SAAS,EAAE;oBAAO,CAAE;oBAAApE,QAAA,EAAC;kBAAK;oBAAAG,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAI,CAAC,eAC5D3G,OAAA;oBAAIkK,KAAK,EAAE;sBAAEM,OAAO,EAAE,KAAK;sBAAEC,SAAS,EAAE;oBAAO,CAAE;oBAAApE,QAAA,EAAC;kBAAO;oBAAAG,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAI,CAAC,eAC9D3G,OAAA;oBAAIkK,KAAK,EAAE;sBAAEM,OAAO,EAAE,KAAK;sBAAEC,SAAS,EAAE;oBAAQ,CAAE;oBAAApE,QAAA,EAAC;kBAAO;oBAAAG,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAI,CAAC,eAC/D3G,OAAA;oBAAIkK,KAAK,EAAE;sBAAEM,OAAO,EAAE,KAAK;sBAAEC,SAAS,EAAE;oBAAO,CAAE;oBAAApE,QAAA,EAAC;kBAAK;oBAAAG,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAI,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAC1D;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACA,CAAC,eACR3G,OAAA;gBAAAqG,QAAA,EACGlF,MAAM,CAACgH,GAAG,CAAErF,MAAM,IAAK;kBACtB,MAAM4H,YAAY,GAAGrJ,YAAY,IAC/ByB,MAAM,CAACE,SAAS,KAAK3B,YAAY,CAAC2B,SAAS,IAC3CF,MAAM,CAACG,YAAY,KAAK5B,YAAY,CAAC4B,YAAY,IACjDH,MAAM,CAACI,OAAO,KAAK7B,YAAY,CAAC6B,OAAO;kBAEzC,MAAMyH,aAAa,GAAG7H,MAAM,CAACW,aAAa,IAAI0B,UAAU,CAAC1D,QAAQ,CAACG,YAAY,IAAI,CAAC,CAAC;kBAEpF,oBACE5B,OAAA;oBAEEkK,KAAK,EAAE;sBACLI,YAAY,EAAE,gBAAgB;sBAC9BC,eAAe,EAAEG,YAAY,GAAIC,aAAa,GAAG,SAAS,GAAG,SAAS,GAAI,aAAa;sBACvFC,MAAM,EAAE;oBACV,CAAE;oBACFnD,OAAO,EAAEA,CAAA,KAAM;sBACb,IAAIkD,aAAa,EAAE;wBACjBjJ,WAAW,CAAC;0BACV,GAAGD,QAAQ;0BACXI,SAAS,EAAEiB,MAAM,CAACjB;wBACpB,CAAC,CAAC;sBACJ;oBACF,CAAE;oBAAAwE,QAAA,gBAEFrG,OAAA;sBAAIkK,KAAK,EAAE;wBAAEM,OAAO,EAAE;sBAAM,CAAE;sBAAAnE,QAAA,EAAE6C,eAAe,CAACpG,MAAM,CAACjB,SAAS;oBAAC;sBAAA2E,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAK,CAAC,eACvE3G,OAAA;sBAAIkK,KAAK,EAAE;wBAAEM,OAAO,EAAE;sBAAM,CAAE;sBAAAnE,QAAA,EAAEvD,MAAM,CAACE,SAAS,IAAI;oBAAK;sBAAAwD,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAK,CAAC,eAC/D3G,OAAA;sBAAIkK,KAAK,EAAE;wBAAEM,OAAO,EAAE;sBAAM,CAAE;sBAAAnE,QAAA,EAAEvD,MAAM,CAACG,YAAY,IAAI;oBAAK;sBAAAuD,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAK,CAAC,eAClE3G,OAAA;sBAAIkK,KAAK,EAAE;wBAAEM,OAAO,EAAE;sBAAM,CAAE;sBAAAnE,QAAA,EAAEvD,MAAM,CAACI,OAAO,IAAI;oBAAK;sBAAAsD,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAK,CAAC,eAC7D3G,OAAA;sBAAIkK,KAAK,EAAE;wBAAEM,OAAO,EAAE,KAAK;wBAAEC,SAAS,EAAE;sBAAQ,CAAE;sBAAApE,QAAA,GAAEvD,MAAM,CAACW,aAAa,IAAI,CAAC,EAAC,IAAE;oBAAA;sBAAA+C,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAI,CAAC,eACrF3G,OAAA;sBAAIkK,KAAK,EAAE;wBAAEM,OAAO,EAAE;sBAAM,CAAE;sBAAAnE,QAAA,eAC5BrG,OAAA,CAAClB,IAAI;wBACH8I,IAAI,EAAC,OAAO;wBACZP,KAAK,EAAEvE,MAAM,CAACC,YAAa;wBAC3ByE,KAAK,EACH1E,MAAM,CAACC,YAAY,KAAK,aAAa,GAAG,SAAS,GACjDD,MAAM,CAACC,YAAY,KAAK,QAAQ,GAAG,SAAS,GAC5CD,MAAM,CAACC,YAAY,KAAK,MAAM,GAAG,OAAO,GACxCD,MAAM,CAACC,YAAY,KAAK,WAAW,GAAG,SAAS,GAAG;sBACnD;wBAAAyD,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACF;oBAAC;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACA,CAAC;kBAAA,GA/BA7D,MAAM,CAACjB,SAAS;oBAAA2E,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAgCnB,CAAC;gBAET,CAAC;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACG,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACL,CAAC,gBAEN3G,OAAA,CAACtB,KAAK;YAACsJ,QAAQ,EAAC,MAAM;YAACpB,EAAE,EAAE;cAAEoC,EAAE,EAAE,CAAC;cAAElC,EAAE,EAAE;YAAE,CAAE;YAAAT,QAAA,EAAC;UAE7C;YAAAG,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAO,CACR,eAGD3G,OAAA,CAACvC,GAAG;YAACmJ,EAAE,EAAE;cAAEoC,EAAE,EAAE,CAAC;cAAElC,EAAE,EAAE;YAAE,CAAE;YAAAT,QAAA,gBACxBrG,OAAA,CAACrC,UAAU;cAAC2I,OAAO,EAAC,WAAW;cAACC,YAAY;cAAAF,QAAA,EAAC;YAE7C;cAAAG,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAY,CAAC,eACb3G,OAAA,CAAC/B,IAAI;cAAC8I,SAAS;cAACC,OAAO,EAAE,CAAE;cAAAX,QAAA,gBACzBrG,OAAA,CAAC/B,IAAI;gBAACiJ,IAAI;gBAAAb,QAAA,eACRrG,OAAA,CAACnC,MAAM;kBACLyI,OAAO,EAAC,UAAU;kBAClBmB,OAAO,EAAEA,CAAA,KAAM;oBACb/F,WAAW,CAAC;sBACV,GAAGD,QAAQ;sBACXI,SAAS,EAAE;oBACb,CAAC,CAAC;kBACJ,CAAE;kBAAAwE,QAAA,EACH;gBAED;kBAAAG,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACL,CAAC,eACP3G,OAAA,CAAC/B,IAAI;gBAACiJ,IAAI;gBAAAb,QAAA,eACRrG,OAAA,CAACnC,MAAM;kBACLyI,OAAO,EAAC,UAAU;kBAClBkB,KAAK,EAAC,WAAW;kBACjBC,OAAO,EAAEA,CAAA,KAAM;oBACb/F,WAAW,CAAC;sBACV,GAAGD,QAAQ;sBACXI,SAAS,EAAE;oBACb,CAAC,CAAC;kBACJ,CAAE;kBAAAwE,QAAA,EACH;gBAED;kBAAAG,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACL,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACJ,CAAC,eAGN3G,OAAA,CAAC9B,WAAW;YAACkJ,SAAS;YAAAf,QAAA,gBACpBrG,OAAA,CAAC7B,UAAU;cAAC0M,EAAE,EAAC,qBAAqB;cAAAxE,QAAA,EAAC;YAA4B;cAAAG,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAY,CAAC,eAC9E3G,OAAA,CAAC5B,MAAM;cACL0M,OAAO,EAAC,qBAAqB;cAC7BD,EAAE,EAAC,eAAe;cAClBhG,IAAI,EAAC,WAAW;cAChBC,KAAK,EAAErD,QAAQ,CAACI,SAAU;cAC1BwF,KAAK,EAAC,8BAA8B;cACpCC,QAAQ,EAAE3C,gBAAiB;cAAA0B,QAAA,gBAE3BrG,OAAA,CAAC3B,QAAQ;gBAACyG,KAAK,EAAC,EAAE;gBAAAuB,QAAA,eAChBrG,OAAA;kBAAAqG,QAAA,EAAI;gBAAc;kBAAAG,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAI;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACf,CAAC,eACX3G,OAAA,CAAC3B,QAAQ;gBAACyG,KAAK,EAAC,cAAc;gBAAAuB,QAAA,eAC5BrG,OAAA;kBAAAqG,QAAA,EAAI;gBAAY;kBAAAG,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAI;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACb,CAAC,EACVxF,MAAM,CAACgH,GAAG,CAAErF,MAAM,iBACjB9C,OAAA,CAAC3B,QAAQ;gBAEPyG,KAAK,EAAEhC,MAAM,CAACjB,SAAU;gBACxB6F,QAAQ,EAAE5E,MAAM,CAACW,aAAa,GAAG0B,UAAU,CAAC1D,QAAQ,CAACG,YAAY,CAAE;gBAAAyE,QAAA,GAElE6C,eAAe,CAACpG,MAAM,CAACjB,SAAS,CAAC,EAAC,KAAG,EAACiB,MAAM,CAACE,SAAS,IAAI,KAAK,EAAC,cAAY,EAACF,MAAM,CAACW,aAAa,IAAI,CAAC,EAAC,IAC1G;cAAA,GALOX,MAAM,CAACjB,SAAS;gBAAA2E,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAKb,CACX,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACI,CAAC,eACT3G,OAAA,CAACpB,cAAc;cAAAyH,QAAA,EAAC;YAEhB;cAAAG,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAgB,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACN,CAAC,EAGblF,QAAQ,CAACI,SAAS,iBACjB7B,OAAA,CAACvC,GAAG;YAACmJ,EAAE,EAAE;cAAEoC,EAAE,EAAE,CAAC;cAAEnC,CAAC,EAAE,CAAC;cAAEkE,OAAO,EAAE,kBAAkB;cAAEC,YAAY,EAAE,CAAC;cAAEC,MAAM,EAAE;YAAoB,CAAE;YAAA5E,QAAA,gBAClGrG,OAAA,CAACrC,UAAU;cAAC2I,OAAO,EAAC,WAAW;cAACC,YAAY;cAAAF,QAAA,EAAC;YAE7C;cAAAG,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAY,CAAC,EACZ,CAAC,MAAM;cACN,MAAM7D,MAAM,GAAG3B,MAAM,CAAC0I,IAAI,CAACrG,CAAC,IAAIA,CAAC,CAAC3B,SAAS,KAAKJ,QAAQ,CAACI,SAAS,CAAC;cACnE,IAAIiB,MAAM,EAAE;gBACV,oBACE9C,OAAA,CAAC/B,IAAI;kBAAC8I,SAAS;kBAACC,OAAO,EAAE,CAAE;kBAAAX,QAAA,gBACzBrG,OAAA,CAAC/B,IAAI;oBAACiJ,IAAI;oBAACC,EAAE,EAAE,EAAG;oBAACyB,EAAE,EAAE,CAAE;oBAAAvC,QAAA,gBACvBrG,OAAA,CAACrC,UAAU;sBAAC2I,OAAO,EAAC,OAAO;sBAAAD,QAAA,gBACzBrG,OAAA;wBAAAqG,QAAA,EAAQ;sBAAO;wBAAAG,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAQ,CAAC,KAAC,EAACuC,eAAe,CAACpG,MAAM,CAACjB,SAAS,CAAC;oBAAA;sBAAA2E,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACjD,CAAC,eACb3G,OAAA,CAACrC,UAAU;sBAAC2I,OAAO,EAAC,OAAO;sBAAAD,QAAA,gBACzBrG,OAAA;wBAAAqG,QAAA,EAAQ;sBAAU;wBAAAG,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAQ,CAAC,KAAC,EAAC7D,MAAM,CAACE,SAAS,IAAI,KAAK;oBAAA;sBAAAwD,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAC5C,CAAC,eACb3G,OAAA,CAACrC,UAAU;sBAAC2I,OAAO,EAAC,OAAO;sBAAAD,QAAA,gBACzBrG,OAAA;wBAAAqG,QAAA,EAAQ;sBAAW;wBAAAG,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAQ,CAAC,KAAC,EAAC7D,MAAM,CAACG,YAAY,IAAI,KAAK,EAAC,KAAG,EAACH,MAAM,CAACI,OAAO,IAAI,KAAK;oBAAA;sBAAAsD,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAC5E,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACT,CAAC,eACP3G,OAAA,CAAC/B,IAAI;oBAACiJ,IAAI;oBAACC,EAAE,EAAE,EAAG;oBAACyB,EAAE,EAAE,CAAE;oBAAAvC,QAAA,gBACvBrG,OAAA,CAACrC,UAAU;sBAAC2I,OAAO,EAAC,OAAO;sBAAAD,QAAA,gBACzBrG,OAAA;wBAAAqG,QAAA,EAAQ;sBAAa;wBAAAG,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAQ,CAAC,KAAC,EAAC7D,MAAM,CAACoI,YAAY,IAAI,CAAC,EAAC,IAC3D;oBAAA;sBAAA1E,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAY,CAAC,eACb3G,OAAA,CAACrC,UAAU;sBAAC2I,OAAO,EAAC,OAAO;sBAAAD,QAAA,gBACzBrG,OAAA;wBAAAqG,QAAA,EAAQ;sBAAc;wBAAAG,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAQ,CAAC,KAAC,EAAC7D,MAAM,CAACW,aAAa,IAAI,CAAC,EAAC,IAC7D;oBAAA;sBAAA+C,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAY,CAAC,eACb3G,OAAA,CAACrC,UAAU;sBAAC2I,OAAO,EAAC,OAAO;sBAAAD,QAAA,gBACzBrG,OAAA;wBAAAqG,QAAA,EAAQ;sBAAM;wBAAAG,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAQ,CAAC,KAAC,EAAC7D,MAAM,CAACC,YAAY,IAAI,KAAK;oBAAA;sBAAAyD,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAC3C,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACT,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH,CAAC;cAEX;cACA,oBACE3G,OAAA,CAACrC,UAAU;gBAAC2I,OAAO,EAAC,OAAO;gBAACkB,KAAK,EAAC,OAAO;gBAAAnB,QAAA,EAAC;cAE1C;gBAAAG,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAY,CAAC;YAEjB,CAAC,EAAE,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACD,CACN;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACE,CACN,EAEAxF,MAAM,CAACiC,MAAM,KAAK,CAAC,IAAI,CAACrC,aAAa,iBACpCf,OAAA,CAACtB,KAAK;UAACsJ,QAAQ,EAAC,SAAS;UAACpB,EAAE,EAAE;YAAEoC,EAAE,EAAE;UAAE,CAAE;UAAA3C,QAAA,EAAC;QAEzC;UAAAG,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAO,CACR;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACI,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACL,CAAC;EAEV,CAAC;;EAED;EACA,MAAMwE,WAAW,GAAGA,CAAA,KAAM;IACxB;IACA,MAAMjC,eAAe,GAAIjD,QAAQ,IAAK;MACpC;MACA,IAAIA,QAAQ,IAAIA,QAAQ,CAACkD,QAAQ,CAAC,IAAI,CAAC,EAAE;QACvC,OAAOlD,QAAQ,CAACmD,KAAK,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;MAChC;MACA,OAAOnD,QAAQ;IACjB,CAAC;;IAED;IACA,MAAMqD,YAAY,GAAG7H,QAAQ,CAACI,SAAS,GAAGqH,eAAe,CAACzH,QAAQ,CAACI,SAAS,CAAC,GAAG,SAAS;IAEzF,oBACE7B,OAAA,CAACvC,GAAG;MAAA4I,QAAA,gBACFrG,OAAA,CAACrC,UAAU;QAAC2I,OAAO,EAAC,IAAI;QAACC,YAAY;QAAAF,QAAA,EAAC;MAEtC;QAAAG,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAY,CAAC,eAEb3G,OAAA,CAACtC,KAAK;QAACkJ,EAAE,EAAE;UAAEC,CAAC,EAAE;QAAE,CAAE;QAAAR,QAAA,gBAClBrG,OAAA,CAACrC,UAAU;UAAC2I,OAAO,EAAC,WAAW;UAACC,YAAY;UAAAF,QAAA,EAAC;QAE7C;UAAAG,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAY,CAAC,eAEb3G,OAAA,CAAC/B,IAAI;UAAC8I,SAAS;UAACC,OAAO,EAAE,CAAE;UAAAX,QAAA,gBACzBrG,OAAA,CAAC/B,IAAI;YAACiJ,IAAI;YAACC,EAAE,EAAE,EAAG;YAACyB,EAAE,EAAE,CAAE;YAAAvC,QAAA,gBACvBrG,OAAA,CAACrC,UAAU;cAAC2I,OAAO,EAAC,OAAO;cAAAD,QAAA,gBACzBrG,OAAA;gBAAAqG,QAAA,EAAQ;cAAQ;gBAAAG,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC,KAAC,EAACtF,YAAY,CAACM,OAAO;YAAA;cAAA6E,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACrC,CAAC,eACb3G,OAAA,CAACrC,UAAU;cAAC2I,OAAO,EAAC,OAAO;cAAAD,QAAA,gBACzBrG,OAAA;gBAAAqG,QAAA,EAAQ;cAAU;gBAAAG,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC,KAAC,EAACtF,YAAY,CAAC2B,SAAS,IAAI,KAAK;YAAA;cAAAwD,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAClD,CAAC,eACb3G,OAAA,CAACrC,UAAU;cAAC2I,OAAO,EAAC,OAAO;cAAAD,QAAA,gBACzBrG,OAAA;gBAAAqG,QAAA,EAAQ;cAAW;gBAAAG,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC,KAAC,EAACtF,YAAY,CAAC4B,YAAY,IAAI,KAAK,EAAC,KAAG,EAAC5B,YAAY,CAAC6B,OAAO,IAAI,KAAK;YAAA;cAAAsD,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACxF,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACT,CAAC,eACP3G,OAAA,CAAC/B,IAAI;YAACiJ,IAAI;YAACC,EAAE,EAAE,EAAG;YAACyB,EAAE,EAAE,CAAE;YAAAvC,QAAA,gBACvBrG,OAAA,CAACrC,UAAU;cAAC2I,OAAO,EAAC,OAAO;cAAAD,QAAA,gBACzBrG,OAAA;gBAAAqG,QAAA,EAAQ;cAAa;gBAAAG,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC,KAAC,EAAClF,QAAQ,CAACG,YAAY;YAAA;cAAA4E,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC3C,CAAC,eACb3G,OAAA,CAACrC,UAAU;cAAC2I,OAAO,EAAC,OAAO;cAAAD,QAAA,gBACzBrG,OAAA;gBAAAqG,QAAA,EAAQ;cAAiB;gBAAAG,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC,KAAC,EAAC2C,YAAY;YAAA;cAAA9C,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACtC,CAAC,eACb3G,OAAA,CAACrC,UAAU;cAAC2I,OAAO,EAAC,OAAO;cAAAD,QAAA,gBACzBrG,OAAA;gBAAAqG,QAAA,EAAQ;cAAoB;gBAAAG,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC,KAAC,EAACxB,UAAU,CAAC1D,QAAQ,CAACG,YAAY,CAAC,IAAIuD,UAAU,CAAC9D,YAAY,CAAC+D,aAAa,CAAC,GAAG,YAAY,GAAG,UAAU;YAAA;cAAAoB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACpI,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACT,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,eAEP3G,OAAA,CAACtB,KAAK;UAACsJ,QAAQ,EAAC,MAAM;UAACpB,EAAE,EAAE;YAAEoC,EAAE,EAAE;UAAE,CAAE;UAAA3C,QAAA,GAAC,8EAEpC,EAAC5E,QAAQ,CAACI,SAAS,IAAI,gFAAgF;QAAA;UAAA2E,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAClG,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACL,CAAC;EAEV,CAAC;;EAED;EACA,MAAMyE,cAAc,GAAIC,IAAI,IAAK;IAC/B,QAAQA,IAAI;MACV,KAAK,CAAC;QACJ,OAAOjF,WAAW,CAAC,CAAC;MACtB,KAAK,CAAC;QACJ,OAAOuC,WAAW,CAAC,CAAC;MACtB,KAAK,CAAC;QACJ,OAAOM,WAAW,CAAC,CAAC;MACtB,KAAK,CAAC;QACJ,OAAOkC,WAAW,CAAC,CAAC;MACtB;QACE,OAAO,mBAAmB;IAC9B;EACF,CAAC;EAED,oBACEnL,OAAA,CAACvC,GAAG;IAAA4I,QAAA,gBACFrG,OAAA,CAAClC,OAAO;MAAC2C,UAAU,EAAEA,UAAW;MAACmG,EAAE,EAAE;QAAEE,EAAE,EAAE;MAAE,CAAE;MAAAT,QAAA,EAC5CnE,KAAK,CAACiG,GAAG,CAAEd,KAAK,iBACfrH,OAAA,CAACjC,IAAI;QAAAsI,QAAA,eACHrG,OAAA,CAAChC,SAAS;UAAAqI,QAAA,EAAEgB;QAAK;UAAAb,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAY;MAAC,GADrBU,KAAK;QAAAb,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAEV,CACP;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACK,CAAC,eAEV3G,OAAA,CAACvC,GAAG;MAACmJ,EAAE,EAAE;QAAEoC,EAAE,EAAE,CAAC;QAAElC,EAAE,EAAE;MAAE,CAAE;MAAAT,QAAA,EACvB+E,cAAc,CAAC3K,UAAU;IAAC;MAAA+F,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACxB,CAAC,eAEN3G,OAAA,CAACvC,GAAG;MAACmJ,EAAE,EAAE;QAAEiB,OAAO,EAAE,MAAM;QAAEC,cAAc,EAAE,eAAe;QAAEkB,EAAE,EAAE;MAAE,CAAE;MAAA3C,QAAA,gBACnErG,OAAA,CAACnC,MAAM;QACLyI,OAAO,EAAC,UAAU;QAClBkB,KAAK,EAAC,WAAW;QACjBC,OAAO,EAAEhH,UAAU,KAAK,CAAC,GAAG,MAAMD,QAAQ,CAAC,sBAAsB,CAAC,GAAGmF,UAAW;QAChFgC,SAAS,eAAE3H,OAAA,CAACZ,aAAa;UAAAoH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAE;QAC7Be,QAAQ,EAAE/G,OAAQ;QAAA0F,QAAA,EAEjB5F,UAAU,KAAK,CAAC,GAAG,SAAS,GAAG;MAAU;QAAA+F,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACpC,CAAC,eAET3G,OAAA,CAACnC,MAAM;QACLyI,OAAO,EAAC,WAAW;QACnBkB,KAAK,EAAC,SAAS;QACfC,OAAO,EAAEhH,UAAU,KAAKyB,KAAK,CAACkB,MAAM,GAAG,CAAC,GAAG4C,YAAY,GAAGP,UAAW;QACrE6F,OAAO,EAAE7K,UAAU,KAAKyB,KAAK,CAACkB,MAAM,GAAG,CAAC,gBAAGpD,OAAA,CAACd,QAAQ;UAAAsH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,gBAAG3G,OAAA,CAACV,gBAAgB;UAAAkH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAE;QAC/Ee,QAAQ,EAAE/G,OAAO,IAAKF,UAAU,KAAK,CAAC,IAAI,CAACY,YAAc;QAAAgF,QAAA,EAExD1F,OAAO,gBACNX,OAAA,CAACrB,gBAAgB;UAACiJ,IAAI,EAAE;QAAG;UAAApB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,GAC5BlG,UAAU,KAAKyB,KAAK,CAACkB,MAAM,GAAG,CAAC,GACjC,OAAO,GAEP;MACD;QAAAoD,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACK,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACN,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CAAC;AAEV,CAAC;AAACpG,EAAA,CAhgCIJ,kBAAkB;EAAA,QACLR,WAAW;AAAA;AAAA4L,EAAA,GADxBpL,kBAAkB;AAkgCxB,eAAeA,kBAAkB;AAAC,IAAAoL,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}