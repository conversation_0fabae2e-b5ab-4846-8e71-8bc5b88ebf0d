import { NextRequest, NextResponse } from 'next/server'

interface WeatherData {
  temperature: number
  humidity: number
  pressure?: number
  description?: string
  city?: string
  country?: string
  timestamp?: string
  isDemo?: boolean
  source?: string
}

export async function GET(
  request: NextRequest,
  { params }: { params: { cantiereId: string } }
) {
  try {
    const cantiereId = parseInt(params.cantiereId)
    
    if (isNaN(cantiereId)) {
      return NextResponse.json(
        { error: 'ID cantiere non valido' },
        { status: 400 }
      )
    }

    // Per ora restituiamo dati meteo mock per testare l'interfaccia
    // TODO: Implementare chiamata al backend Python per dati meteo reali
    const mockWeatherData: WeatherData = {
      temperature: 18.5,
      humidity: 68,
      pressure: 1013.2,
      description: "Partly cloudy",
      city: "Milano",
      country: "IT",
      timestamp: new Date().toISOString(),
      isDemo: true,
      source: "cantiere_database"
    }

    return NextResponse.json({
      success: true,
      data: mockWeatherData,
      message: "Dati meteorologici recuperati con successo"
    })

  } catch (error) {
    console.error('Errore nel recupero dati meteo:', error)
    return NextResponse.json(
      { error: 'Errore interno del server' },
      { status: 500 }
    )
  }
}
