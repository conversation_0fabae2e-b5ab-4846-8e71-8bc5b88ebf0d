{"ast": null, "code": "import { minutesInHour } from \"./constants.js\";\n\n/**\n * @name hoursToMinutes\n * @category Conversion Helpers\n * @summary Convert hours to minutes.\n *\n * @description\n * Convert a number of hours to a full number of minutes.\n *\n * @param hours - number of hours to be converted\n *\n * @returns The number of hours converted in minutes\n *\n * @example\n * // Convert 2 hours to minutes:\n * const result = hoursToMinutes(2)\n * //=> 120\n */\nexport function hoursToMinutes(hours) {\n  return Math.trunc(hours * minutesInHour);\n}\n\n// Fallback for modularized imports:\nexport default hoursToMinutes;", "map": {"version": 3, "names": ["minutesInHour", "hoursToMinutes", "hours", "Math", "trunc"], "sources": ["C:/Users/<USER>/PycharmProjects/CMS/webapp/frontend/node_modules/date-fns/hoursToMinutes.js"], "sourcesContent": ["import { minutesInHour } from \"./constants.js\";\n\n/**\n * @name hoursToMinutes\n * @category Conversion Helpers\n * @summary Convert hours to minutes.\n *\n * @description\n * Convert a number of hours to a full number of minutes.\n *\n * @param hours - number of hours to be converted\n *\n * @returns The number of hours converted in minutes\n *\n * @example\n * // Convert 2 hours to minutes:\n * const result = hoursToMinutes(2)\n * //=> 120\n */\nexport function hoursToMinutes(hours) {\n  return Math.trunc(hours * minutesInHour);\n}\n\n// Fallback for modularized imports:\nexport default hoursToMinutes;\n"], "mappings": "AAAA,SAASA,aAAa,QAAQ,gBAAgB;;AAE9C;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO,SAASC,cAAcA,CAACC,KAAK,EAAE;EACpC,OAAOC,IAAI,CAACC,KAAK,CAACF,KAAK,GAAGF,aAAa,CAAC;AAC1C;;AAEA;AACA,eAAeC,cAAc", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}