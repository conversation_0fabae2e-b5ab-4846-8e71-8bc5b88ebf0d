{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\PycharmProjects\\\\CMS\\\\webapp\\\\frontend\\\\src\\\\pages\\\\UserPage.js\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect } from 'react';\nimport { Box, Typography, Paper, Button, Grid, Card, CardContent, CardActions, Dialog, DialogActions, DialogContent, DialogContentText, DialogTitle, TextField, Snackbar, Alert } from '@mui/material';\nimport { Construction as ConstructionIcon, Delete as DeleteIcon, Add as AddIcon } from '@mui/icons-material';\nimport { useAuth } from '../context/AuthContext';\nimport { useNavigate } from 'react-router-dom';\nimport cantieriService from '../services/cantieriService';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst UserPage = () => {\n  _s();\n  const {\n    user,\n    isImpersonating,\n    impersonatedUser\n  } = useAuth();\n  const navigate = useNavigate();\n  const [cantieri, setCantieri] = useState([]);\n  const [loading, setLoading] = useState(true);\n  const [error, setError] = useState(null);\n  const [openCreateDialog, setOpenCreateDialog] = useState(false);\n  const [openDeleteDialog, setOpenDeleteDialog] = useState(false);\n  const [selectedCantiere, setSelectedCantiere] = useState(null);\n  const [newCantiereData, setNewCantiereData] = useState({\n    nome: '',\n    descrizione: '',\n    password_cantiere: '',\n    conferma_password: ''\n  });\n  const [notification, setNotification] = useState({\n    open: false,\n    message: '',\n    severity: 'success'\n  });\n\n  // Carica i cantieri dell'utente corrente o dell'utente impersonato\n  useEffect(() => {\n    const fetchCantieri = async () => {\n      try {\n        setLoading(true);\n        let data;\n\n        // Se l'amministratore sta impersonando un utente, carica i cantieri di quell'utente\n        if ((user === null || user === void 0 ? void 0 : user.role) === 'owner' && isImpersonating && impersonatedUser) {\n          // Carica i cantieri dell'utente impersonato\n          data = await cantieriService.getUserCantieri(impersonatedUser.id);\n        } else {\n          // Altrimenti carica i cantieri dell'utente corrente\n          data = await cantieriService.getMyCantieri();\n        }\n        setCantieri(data);\n      } catch (err) {\n        console.error('Errore nel caricamento dei cantieri:', err);\n        setError('Impossibile caricare i cantieri. Riprova più tardi.');\n      } finally {\n        setLoading(false);\n      }\n    };\n    fetchCantieri();\n  }, [user, isImpersonating, impersonatedUser]);\n\n  // Gestisce l'apertura del dialog per creare un nuovo cantiere\n  const handleOpenCreateDialog = () => {\n    setNewCantiereData({\n      nome: '',\n      descrizione: '',\n      password_cantiere: '',\n      conferma_password: ''\n    });\n    setOpenCreateDialog(true);\n  };\n\n  // Gestisce la chiusura del dialog per creare un nuovo cantiere\n  const handleCloseCreateDialog = () => {\n    setOpenCreateDialog(false);\n  };\n\n  // Gestisce l'apertura del dialog per eliminare un cantiere\n  const handleOpenDeleteDialog = cantiere => {\n    setSelectedCantiere(cantiere);\n    setOpenDeleteDialog(true);\n  };\n\n  // Gestisce la chiusura del dialog per eliminare un cantiere\n  const handleCloseDeleteDialog = () => {\n    setOpenDeleteDialog(false);\n    setSelectedCantiere(null);\n  };\n\n  // Gestisce la modifica dei campi del form per creare un nuovo cantiere\n  const handleInputChange = e => {\n    const {\n      name,\n      value\n    } = e.target;\n    setNewCantiereData({\n      ...newCantiereData,\n      [name]: value\n    });\n  };\n\n  // Gestisce la creazione di un nuovo cantiere\n  const handleCreateCantiere = async () => {\n    // Verifica che i campi obbligatori siano compilati\n    if (!newCantiereData.nome || !newCantiereData.password_cantiere) {\n      setNotification({\n        open: true,\n        message: 'Il nome e la password sono obbligatori',\n        severity: 'error'\n      });\n      return;\n    }\n\n    // Verifica che le password coincidano\n    if (newCantiereData.password_cantiere !== newCantiereData.conferma_password) {\n      setNotification({\n        open: true,\n        message: 'Le password non coincidono',\n        severity: 'error'\n      });\n      return;\n    }\n    try {\n      const createdCantiere = await cantieriService.createCantiere({\n        nome: newCantiereData.nome,\n        descrizione: newCantiereData.descrizione,\n        password_cantiere: newCantiereData.password_cantiere\n      });\n\n      // Aggiorna la lista dei cantieri\n      setCantieri([...cantieri, createdCantiere]);\n\n      // Chiudi il dialog\n      handleCloseCreateDialog();\n\n      // Mostra una notifica di successo\n      setNotification({\n        open: true,\n        message: `Cantiere ${createdCantiere.nome} creato con successo! Codice univoco: ${createdCantiere.codice_univoco}`,\n        severity: 'success'\n      });\n    } catch (err) {\n      console.error('Errore nella creazione del cantiere:', err);\n      setNotification({\n        open: true,\n        message: 'Errore nella creazione del cantiere',\n        severity: 'error'\n      });\n    }\n  };\n\n  // Gestisce l'eliminazione di un cantiere\n  const handleDeleteCantiere = async () => {\n    if (!selectedCantiere) return;\n    try {\n      await cantieriService.deleteCantiere(selectedCantiere.id_cantiere);\n\n      // Aggiorna la lista dei cantieri\n      setCantieri(cantieri.filter(c => c.id_cantiere !== selectedCantiere.id_cantiere));\n\n      // Chiudi il dialog\n      handleCloseDeleteDialog();\n\n      // Mostra una notifica di successo\n      setNotification({\n        open: true,\n        message: `Cantiere ${selectedCantiere.nome} eliminato con successo!`,\n        severity: 'success'\n      });\n    } catch (err) {\n      console.error('Errore nell\\'eliminazione del cantiere:', err);\n      setNotification({\n        open: true,\n        message: 'Errore nell\\'eliminazione del cantiere',\n        severity: 'error'\n      });\n    }\n  };\n\n  // Gestisce la selezione di un cantiere per aprire direttamente la pagina di gestione cavi\n  const handleSelectCantiere = cantiere => {\n    console.log('Selezionato cantiere:', cantiere);\n\n    // Salva l'ID e il nome del cantiere nel localStorage per l'uso nelle pagine di gestione cavi\n    localStorage.setItem('selectedCantiereId', cantiere.id_cantiere.toString());\n    localStorage.setItem('selectedCantiereName', cantiere.nome);\n\n    // Naviga direttamente alla pagina di visualizzazione cavi\n    navigate('/dashboard/cavi/visualizza');\n  };\n\n  // Gestisce la chiusura della notifica\n  const handleCloseNotification = () => {\n    setNotification({\n      ...notification,\n      open: false\n    });\n  };\n  return /*#__PURE__*/_jsxDEV(Box, {\n    children: [/*#__PURE__*/_jsxDEV(Typography, {\n      variant: \"h4\",\n      gutterBottom: true,\n      children: isImpersonating && impersonatedUser ? `Cantieri di ${impersonatedUser.username}` : \"I Miei Cantieri\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 217,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Box, {\n      sx: {\n        mb: 3,\n        display: 'flex',\n        justifyContent: 'space-between',\n        alignItems: 'center'\n      },\n      children: [/*#__PURE__*/_jsxDEV(Typography, {\n        variant: \"body1\",\n        children: isImpersonating && impersonatedUser ? `Visualizza e gestisci i cantieri di ${impersonatedUser.username}` : \"Visualizza e gestisci i tuoi cantieri\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 222,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Button, {\n        variant: \"contained\",\n        color: \"primary\",\n        startIcon: /*#__PURE__*/_jsxDEV(AddIcon, {}, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 230,\n          columnNumber: 22\n        }, this),\n        onClick: handleOpenCreateDialog,\n        children: \"Nuovo Cantiere\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 227,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 221,\n      columnNumber: 7\n    }, this), loading ? /*#__PURE__*/_jsxDEV(Typography, {\n      children: \"Caricamento cantieri...\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 238,\n      columnNumber: 9\n    }, this) : error ? /*#__PURE__*/_jsxDEV(Alert, {\n      severity: \"error\",\n      children: error\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 240,\n      columnNumber: 9\n    }, this) : cantieri.length === 0 ? /*#__PURE__*/_jsxDEV(Paper, {\n      sx: {\n        p: 3,\n        textAlign: 'center'\n      },\n      children: [/*#__PURE__*/_jsxDEV(Typography, {\n        variant: \"h6\",\n        children: \"Nessun cantiere trovato\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 243,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(Typography, {\n        variant: \"body2\",\n        color: \"text.secondary\",\n        children: \"Crea un nuovo cantiere per iniziare\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 244,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(Button, {\n        variant: \"contained\",\n        color: \"primary\",\n        startIcon: /*#__PURE__*/_jsxDEV(AddIcon, {}, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 250,\n          columnNumber: 24\n        }, this),\n        onClick: handleOpenCreateDialog,\n        sx: {\n          mt: 2\n        },\n        children: \"Nuovo Cantiere\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 247,\n        columnNumber: 11\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 242,\n      columnNumber: 9\n    }, this) : /*#__PURE__*/_jsxDEV(Grid, {\n      container: true,\n      spacing: 3,\n      children: cantieri.map(cantiere => /*#__PURE__*/_jsxDEV(Grid, {\n        item: true,\n        xs: 12,\n        sm: 6,\n        md: 4,\n        children: /*#__PURE__*/_jsxDEV(Card, {\n          children: [/*#__PURE__*/_jsxDEV(CardContent, {\n            children: [/*#__PURE__*/_jsxDEV(Box, {\n              className: \"cantiere-header\",\n              children: [/*#__PURE__*/_jsxDEV(ConstructionIcon, {}, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 264,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                variant: \"h6\",\n                component: \"div\",\n                children: cantiere.nome\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 265,\n                columnNumber: 21\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 263,\n              columnNumber: 19\n            }, this), /*#__PURE__*/_jsxDEV(Typography, {\n              variant: \"body2\",\n              color: \"text.secondary\",\n              sx: {\n                mb: 1\n              },\n              children: [\"ID: \", cantiere.id_cantiere]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 269,\n              columnNumber: 19\n            }, this), /*#__PURE__*/_jsxDEV(Typography, {\n              variant: \"body2\",\n              color: \"text.secondary\",\n              sx: {\n                mb: 1\n              },\n              children: [\"Codice Univoco: \", cantiere.codice_univoco]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 272,\n              columnNumber: 19\n            }, this), cantiere.descrizione && /*#__PURE__*/_jsxDEV(Typography, {\n              variant: \"body2\",\n              color: \"text.secondary\",\n              children: cantiere.descrizione\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 276,\n              columnNumber: 21\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 262,\n            columnNumber: 17\n          }, this), /*#__PURE__*/_jsxDEV(CardActions, {\n            children: [/*#__PURE__*/_jsxDEV(Button, {\n              variant: \"contained\",\n              className: \"primary-button\",\n              onClick: () => handleSelectCantiere(cantiere),\n              children: \"Gestione Cavi\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 282,\n              columnNumber: 19\n            }, this), /*#__PURE__*/_jsxDEV(Button, {\n              variant: \"contained\",\n              className: \"error-button\",\n              startIcon: /*#__PURE__*/_jsxDEV(DeleteIcon, {}, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 292,\n                columnNumber: 32\n              }, this),\n              onClick: () => handleOpenDeleteDialog(cantiere),\n              children: \"Elimina\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 289,\n              columnNumber: 19\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 281,\n            columnNumber: 17\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 261,\n          columnNumber: 15\n        }, this)\n      }, cantiere.id_cantiere, false, {\n        fileName: _jsxFileName,\n        lineNumber: 260,\n        columnNumber: 13\n      }, this))\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 258,\n      columnNumber: 9\n    }, this), /*#__PURE__*/_jsxDEV(Dialog, {\n      open: openCreateDialog,\n      onClose: handleCloseCreateDialog,\n      children: [/*#__PURE__*/_jsxDEV(DialogTitle, {\n        children: \"Crea Nuovo Cantiere\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 306,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(DialogContent, {\n        children: [/*#__PURE__*/_jsxDEV(DialogContentText, {\n          children: \"Inserisci i dati per creare un nuovo cantiere.\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 308,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(TextField, {\n          autoFocus: true,\n          margin: \"dense\",\n          name: \"nome\",\n          label: \"Nome Cantiere\",\n          type: \"text\",\n          fullWidth: true,\n          variant: \"outlined\",\n          value: newCantiereData.nome,\n          onChange: handleInputChange,\n          required: true\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 311,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(TextField, {\n          margin: \"dense\",\n          name: \"descrizione\",\n          label: \"Descrizione\",\n          type: \"text\",\n          fullWidth: true,\n          variant: \"outlined\",\n          value: newCantiereData.descrizione,\n          onChange: handleInputChange\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 323,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(TextField, {\n          margin: \"dense\",\n          name: \"password_cantiere\",\n          label: \"Password\",\n          type: \"password\",\n          fullWidth: true,\n          variant: \"outlined\",\n          value: newCantiereData.password_cantiere,\n          onChange: handleInputChange,\n          required: true\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 333,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(TextField, {\n          margin: \"dense\",\n          name: \"conferma_password\",\n          label: \"Conferma Password\",\n          type: \"password\",\n          fullWidth: true,\n          variant: \"outlined\",\n          value: newCantiereData.conferma_password,\n          onChange: handleInputChange,\n          required: true\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 344,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 307,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(DialogActions, {\n        children: [/*#__PURE__*/_jsxDEV(Button, {\n          variant: \"contained\",\n          onClick: handleCloseCreateDialog,\n          children: \"Annulla\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 357,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Button, {\n          onClick: handleCreateCantiere,\n          variant: \"contained\",\n          className: \"primary-button\",\n          children: \"Crea\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 358,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 356,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 305,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Dialog, {\n      open: openDeleteDialog,\n      onClose: handleCloseDeleteDialog,\n      children: [/*#__PURE__*/_jsxDEV(DialogTitle, {\n        children: \"Elimina Cantiere\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 366,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(DialogContent, {\n        children: /*#__PURE__*/_jsxDEV(DialogContentText, {\n          children: [\"ATTENZIONE: Sei sicuro di voler eliminare il cantiere \\\"\", selectedCantiere === null || selectedCantiere === void 0 ? void 0 : selectedCantiere.nome, \"\\\" e tutti i suoi dati? Questa operazione non pu\\xF2 essere annullata.\"]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 368,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 367,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(DialogActions, {\n        children: [/*#__PURE__*/_jsxDEV(Button, {\n          variant: \"contained\",\n          onClick: handleCloseDeleteDialog,\n          children: \"Annulla\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 374,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Button, {\n          onClick: handleDeleteCantiere,\n          variant: \"contained\",\n          className: \"error-button\",\n          children: \"Elimina\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 375,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 373,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 365,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Snackbar, {\n      open: notification.open,\n      autoHideDuration: 6000,\n      onClose: handleCloseNotification,\n      anchorOrigin: {\n        vertical: 'bottom',\n        horizontal: 'center'\n      },\n      children: /*#__PURE__*/_jsxDEV(Alert, {\n        onClose: handleCloseNotification,\n        severity: notification.severity,\n        sx: {\n          width: '100%'\n        },\n        children: notification.message\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 388,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 382,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 216,\n    columnNumber: 5\n  }, this);\n};\n_s(UserPage, \"JOd2o/flmlMz/xmKD7Cz7PkBqRE=\", false, function () {\n  return [useAuth, useNavigate];\n});\n_c = UserPage;\nexport default UserPage;\nvar _c;\n$RefreshReg$(_c, \"UserPage\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "Box", "Typography", "Paper", "<PERSON><PERSON>", "Grid", "Card", "<PERSON><PERSON><PERSON><PERSON>", "CardActions", "Dialog", "DialogActions", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "DialogContentText", "DialogTitle", "TextField", "Snackbar", "<PERSON><PERSON>", "Construction", "ConstructionIcon", "Delete", "DeleteIcon", "Add", "AddIcon", "useAuth", "useNavigate", "cantieriService", "jsxDEV", "_jsxDEV", "UserPage", "_s", "user", "isImpersonating", "impersonated<PERSON><PERSON>", "navigate", "cantieri", "set<PERSON><PERSON><PERSON>", "loading", "setLoading", "error", "setError", "openCreateDialog", "setOpenCreateDialog", "openDeleteDialog", "setOpenDeleteDialog", "selected<PERSON><PERSON><PERSON>", "setSelectedCantiere", "newCantiereData", "setNewCantiereData", "nome", "descrizione", "password_cantiere", "conferma_password", "notification", "setNotification", "open", "message", "severity", "fetchCantieri", "data", "role", "getUserCantieri", "id", "getMyCantieri", "err", "console", "handleOpenCreateDialog", "handleCloseCreateDialog", "handleOpenDeleteDialog", "cantiere", "handleCloseDeleteDialog", "handleInputChange", "e", "name", "value", "target", "handleCreateCantiere", "createdCantiere", "createCantiere", "codice_univoco", "handleDeleteCantiere", "deleteCantiere", "id_cantiere", "filter", "c", "handleSelectCantiere", "log", "localStorage", "setItem", "toString", "handleCloseNotification", "children", "variant", "gutterBottom", "username", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "sx", "mb", "display", "justifyContent", "alignItems", "color", "startIcon", "onClick", "length", "p", "textAlign", "mt", "container", "spacing", "map", "item", "xs", "sm", "md", "className", "component", "onClose", "autoFocus", "margin", "label", "type", "fullWidth", "onChange", "required", "autoHideDuration", "anchor<PERSON><PERSON><PERSON>", "vertical", "horizontal", "width", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/PycharmProjects/CMS/webapp/frontend/src/pages/UserPage.js"], "sourcesContent": ["import React, { useState, useEffect } from 'react';\nimport {\n  Box,\n  Typography,\n  Paper,\n  Button,\n  Grid,\n  Card,\n  CardContent,\n  CardActions,\n  Dialog,\n  DialogActions,\n  DialogContent,\n  DialogContentText,\n  DialogTitle,\n  TextField,\n  Snackbar,\n  Alert\n} from '@mui/material';\nimport {\n  Construction as ConstructionIcon,\n  Delete as DeleteIcon,\n  Add as AddIcon\n} from '@mui/icons-material';\nimport { useAuth } from '../context/AuthContext';\nimport { useNavigate } from 'react-router-dom';\nimport cantieriService from '../services/cantieriService';\n\nconst UserPage = () => {\n  const { user, isImpersonating, impersonatedUser } = useAuth();\n  const navigate = useNavigate();\n  const [cantieri, setCantieri] = useState([]);\n  const [loading, setLoading] = useState(true);\n  const [error, setError] = useState(null);\n  const [openCreateDialog, setOpenCreateDialog] = useState(false);\n  const [openDeleteDialog, setOpenDeleteDialog] = useState(false);\n  const [selectedCantiere, setSelectedCantiere] = useState(null);\n  const [newCantiereData, setNewCantiereData] = useState({\n    nome: '',\n    descrizione: '',\n    password_cantiere: '',\n    conferma_password: ''\n  });\n  const [notification, setNotification] = useState({\n    open: false,\n    message: '',\n    severity: 'success'\n  });\n\n  // Carica i cantieri dell'utente corrente o dell'utente impersonato\n  useEffect(() => {\n    const fetchCantieri = async () => {\n      try {\n        setLoading(true);\n        let data;\n\n        // Se l'amministratore sta impersonando un utente, carica i cantieri di quell'utente\n        if (user?.role === 'owner' && isImpersonating && impersonatedUser) {\n          // Carica i cantieri dell'utente impersonato\n          data = await cantieriService.getUserCantieri(impersonatedUser.id);\n        } else {\n          // Altrimenti carica i cantieri dell'utente corrente\n          data = await cantieriService.getMyCantieri();\n        }\n\n        setCantieri(data);\n      } catch (err) {\n        console.error('Errore nel caricamento dei cantieri:', err);\n        setError('Impossibile caricare i cantieri. Riprova più tardi.');\n      } finally {\n        setLoading(false);\n      }\n    };\n\n    fetchCantieri();\n  }, [user, isImpersonating, impersonatedUser]);\n\n  // Gestisce l'apertura del dialog per creare un nuovo cantiere\n  const handleOpenCreateDialog = () => {\n    setNewCantiereData({\n      nome: '',\n      descrizione: '',\n      password_cantiere: '',\n      conferma_password: ''\n    });\n    setOpenCreateDialog(true);\n  };\n\n  // Gestisce la chiusura del dialog per creare un nuovo cantiere\n  const handleCloseCreateDialog = () => {\n    setOpenCreateDialog(false);\n  };\n\n  // Gestisce l'apertura del dialog per eliminare un cantiere\n  const handleOpenDeleteDialog = (cantiere) => {\n    setSelectedCantiere(cantiere);\n    setOpenDeleteDialog(true);\n  };\n\n  // Gestisce la chiusura del dialog per eliminare un cantiere\n  const handleCloseDeleteDialog = () => {\n    setOpenDeleteDialog(false);\n    setSelectedCantiere(null);\n  };\n\n  // Gestisce la modifica dei campi del form per creare un nuovo cantiere\n  const handleInputChange = (e) => {\n    const { name, value } = e.target;\n    setNewCantiereData({\n      ...newCantiereData,\n      [name]: value\n    });\n  };\n\n  // Gestisce la creazione di un nuovo cantiere\n  const handleCreateCantiere = async () => {\n    // Verifica che i campi obbligatori siano compilati\n    if (!newCantiereData.nome || !newCantiereData.password_cantiere) {\n      setNotification({\n        open: true,\n        message: 'Il nome e la password sono obbligatori',\n        severity: 'error'\n      });\n      return;\n    }\n\n    // Verifica che le password coincidano\n    if (newCantiereData.password_cantiere !== newCantiereData.conferma_password) {\n      setNotification({\n        open: true,\n        message: 'Le password non coincidono',\n        severity: 'error'\n      });\n      return;\n    }\n\n    try {\n      const createdCantiere = await cantieriService.createCantiere({\n        nome: newCantiereData.nome,\n        descrizione: newCantiereData.descrizione,\n        password_cantiere: newCantiereData.password_cantiere\n      });\n\n      // Aggiorna la lista dei cantieri\n      setCantieri([...cantieri, createdCantiere]);\n\n      // Chiudi il dialog\n      handleCloseCreateDialog();\n\n      // Mostra una notifica di successo\n      setNotification({\n        open: true,\n        message: `Cantiere ${createdCantiere.nome} creato con successo! Codice univoco: ${createdCantiere.codice_univoco}`,\n        severity: 'success'\n      });\n    } catch (err) {\n      console.error('Errore nella creazione del cantiere:', err);\n      setNotification({\n        open: true,\n        message: 'Errore nella creazione del cantiere',\n        severity: 'error'\n      });\n    }\n  };\n\n  // Gestisce l'eliminazione di un cantiere\n  const handleDeleteCantiere = async () => {\n    if (!selectedCantiere) return;\n\n    try {\n      await cantieriService.deleteCantiere(selectedCantiere.id_cantiere);\n\n      // Aggiorna la lista dei cantieri\n      setCantieri(cantieri.filter(c => c.id_cantiere !== selectedCantiere.id_cantiere));\n\n      // Chiudi il dialog\n      handleCloseDeleteDialog();\n\n      // Mostra una notifica di successo\n      setNotification({\n        open: true,\n        message: `Cantiere ${selectedCantiere.nome} eliminato con successo!`,\n        severity: 'success'\n      });\n    } catch (err) {\n      console.error('Errore nell\\'eliminazione del cantiere:', err);\n      setNotification({\n        open: true,\n        message: 'Errore nell\\'eliminazione del cantiere',\n        severity: 'error'\n      });\n    }\n  };\n\n  // Gestisce la selezione di un cantiere per aprire direttamente la pagina di gestione cavi\n  const handleSelectCantiere = (cantiere) => {\n    console.log('Selezionato cantiere:', cantiere);\n\n    // Salva l'ID e il nome del cantiere nel localStorage per l'uso nelle pagine di gestione cavi\n    localStorage.setItem('selectedCantiereId', cantiere.id_cantiere.toString());\n    localStorage.setItem('selectedCantiereName', cantiere.nome);\n\n    // Naviga direttamente alla pagina di visualizzazione cavi\n    navigate('/dashboard/cavi/visualizza');\n  };\n\n  // Gestisce la chiusura della notifica\n  const handleCloseNotification = () => {\n    setNotification({\n      ...notification,\n      open: false\n    });\n  };\n\n  return (\n    <Box>\n      <Typography variant=\"h4\" gutterBottom>\n        {isImpersonating && impersonatedUser ? `Cantieri di ${impersonatedUser.username}` : \"I Miei Cantieri\"}\n      </Typography>\n\n      <Box sx={{ mb: 3, display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>\n        <Typography variant=\"body1\">\n          {isImpersonating && impersonatedUser\n            ? `Visualizza e gestisci i cantieri di ${impersonatedUser.username}`\n            : \"Visualizza e gestisci i tuoi cantieri\"}\n        </Typography>\n        <Button\n          variant=\"contained\"\n          color=\"primary\"\n          startIcon={<AddIcon />}\n          onClick={handleOpenCreateDialog}\n        >\n          Nuovo Cantiere\n        </Button>\n      </Box>\n\n      {loading ? (\n        <Typography>Caricamento cantieri...</Typography>\n      ) : error ? (\n        <Alert severity=\"error\">{error}</Alert>\n      ) : cantieri.length === 0 ? (\n        <Paper sx={{ p: 3, textAlign: 'center' }}>\n          <Typography variant=\"h6\">Nessun cantiere trovato</Typography>\n          <Typography variant=\"body2\" color=\"text.secondary\">\n            Crea un nuovo cantiere per iniziare\n          </Typography>\n          <Button\n            variant=\"contained\"\n            color=\"primary\"\n            startIcon={<AddIcon />}\n            onClick={handleOpenCreateDialog}\n            sx={{ mt: 2 }}\n          >\n            Nuovo Cantiere\n          </Button>\n        </Paper>\n      ) : (\n        <Grid container spacing={3}>\n          {cantieri.map((cantiere) => (\n            <Grid item xs={12} sm={6} md={4} key={cantiere.id_cantiere}>\n              <Card>\n                <CardContent>\n                  <Box className=\"cantiere-header\">\n                    <ConstructionIcon />\n                    <Typography variant=\"h6\" component=\"div\">\n                      {cantiere.nome}\n                    </Typography>\n                  </Box>\n                  <Typography variant=\"body2\" color=\"text.secondary\" sx={{ mb: 1 }}>\n                    ID: {cantiere.id_cantiere}\n                  </Typography>\n                  <Typography variant=\"body2\" color=\"text.secondary\" sx={{ mb: 1 }}>\n                    Codice Univoco: {cantiere.codice_univoco}\n                  </Typography>\n                  {cantiere.descrizione && (\n                    <Typography variant=\"body2\" color=\"text.secondary\">\n                      {cantiere.descrizione}\n                    </Typography>\n                  )}\n                </CardContent>\n                <CardActions>\n                  <Button\n                    variant=\"contained\"\n                    className=\"primary-button\"\n                    onClick={() => handleSelectCantiere(cantiere)}\n                  >\n                    Gestione Cavi\n                  </Button>\n                  <Button\n                    variant=\"contained\"\n                    className=\"error-button\"\n                    startIcon={<DeleteIcon />}\n                    onClick={() => handleOpenDeleteDialog(cantiere)}\n                  >\n                    Elimina\n                  </Button>\n                </CardActions>\n              </Card>\n            </Grid>\n          ))}\n        </Grid>\n      )}\n\n      {/* Dialog per creare un nuovo cantiere */}\n      <Dialog open={openCreateDialog} onClose={handleCloseCreateDialog}>\n        <DialogTitle>Crea Nuovo Cantiere</DialogTitle>\n        <DialogContent>\n          <DialogContentText>\n            Inserisci i dati per creare un nuovo cantiere.\n          </DialogContentText>\n          <TextField\n            autoFocus\n            margin=\"dense\"\n            name=\"nome\"\n            label=\"Nome Cantiere\"\n            type=\"text\"\n            fullWidth\n            variant=\"outlined\"\n            value={newCantiereData.nome}\n            onChange={handleInputChange}\n            required\n          />\n          <TextField\n            margin=\"dense\"\n            name=\"descrizione\"\n            label=\"Descrizione\"\n            type=\"text\"\n            fullWidth\n            variant=\"outlined\"\n            value={newCantiereData.descrizione}\n            onChange={handleInputChange}\n          />\n          <TextField\n            margin=\"dense\"\n            name=\"password_cantiere\"\n            label=\"Password\"\n            type=\"password\"\n            fullWidth\n            variant=\"outlined\"\n            value={newCantiereData.password_cantiere}\n            onChange={handleInputChange}\n            required\n          />\n          <TextField\n            margin=\"dense\"\n            name=\"conferma_password\"\n            label=\"Conferma Password\"\n            type=\"password\"\n            fullWidth\n            variant=\"outlined\"\n            value={newCantiereData.conferma_password}\n            onChange={handleInputChange}\n            required\n          />\n        </DialogContent>\n        <DialogActions>\n          <Button variant=\"contained\" onClick={handleCloseCreateDialog}>Annulla</Button>\n          <Button onClick={handleCreateCantiere} variant=\"contained\" className=\"primary-button\">\n            Crea\n          </Button>\n        </DialogActions>\n      </Dialog>\n\n      {/* Dialog per eliminare un cantiere */}\n      <Dialog open={openDeleteDialog} onClose={handleCloseDeleteDialog}>\n        <DialogTitle>Elimina Cantiere</DialogTitle>\n        <DialogContent>\n          <DialogContentText>\n            ATTENZIONE: Sei sicuro di voler eliminare il cantiere \"{selectedCantiere?.nome}\" e tutti i suoi dati?\n            Questa operazione non può essere annullata.\n          </DialogContentText>\n        </DialogContent>\n        <DialogActions>\n          <Button variant=\"contained\" onClick={handleCloseDeleteDialog}>Annulla</Button>\n          <Button onClick={handleDeleteCantiere} variant=\"contained\" className=\"error-button\">\n            Elimina\n          </Button>\n        </DialogActions>\n      </Dialog>\n\n      {/* Notifica */}\n      <Snackbar\n        open={notification.open}\n        autoHideDuration={6000}\n        onClose={handleCloseNotification}\n        anchorOrigin={{ vertical: 'bottom', horizontal: 'center' }}\n      >\n        <Alert onClose={handleCloseNotification} severity={notification.severity} sx={{ width: '100%' }}>\n          {notification.message}\n        </Alert>\n      </Snackbar>\n    </Box>\n  );\n};\n\nexport default UserPage;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAClD,SACEC,GAAG,EACHC,UAAU,EACVC,KAAK,EACLC,MAAM,EACNC,IAAI,EACJC,IAAI,EACJC,WAAW,EACXC,WAAW,EACXC,MAAM,EACNC,aAAa,EACbC,aAAa,EACbC,iBAAiB,EACjBC,WAAW,EACXC,SAAS,EACTC,QAAQ,EACRC,KAAK,QACA,eAAe;AACtB,SACEC,YAAY,IAAIC,gBAAgB,EAChCC,MAAM,IAAIC,UAAU,EACpBC,GAAG,IAAIC,OAAO,QACT,qBAAqB;AAC5B,SAASC,OAAO,QAAQ,wBAAwB;AAChD,SAASC,WAAW,QAAQ,kBAAkB;AAC9C,OAAOC,eAAe,MAAM,6BAA6B;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAE1D,MAAMC,QAAQ,GAAGA,CAAA,KAAM;EAAAC,EAAA;EACrB,MAAM;IAAEC,IAAI;IAAEC,eAAe;IAAEC;EAAiB,CAAC,GAAGT,OAAO,CAAC,CAAC;EAC7D,MAAMU,QAAQ,GAAGT,WAAW,CAAC,CAAC;EAC9B,MAAM,CAACU,QAAQ,EAAEC,WAAW,CAAC,GAAGpC,QAAQ,CAAC,EAAE,CAAC;EAC5C,MAAM,CAACqC,OAAO,EAAEC,UAAU,CAAC,GAAGtC,QAAQ,CAAC,IAAI,CAAC;EAC5C,MAAM,CAACuC,KAAK,EAAEC,QAAQ,CAAC,GAAGxC,QAAQ,CAAC,IAAI,CAAC;EACxC,MAAM,CAACyC,gBAAgB,EAAEC,mBAAmB,CAAC,GAAG1C,QAAQ,CAAC,KAAK,CAAC;EAC/D,MAAM,CAAC2C,gBAAgB,EAAEC,mBAAmB,CAAC,GAAG5C,QAAQ,CAAC,KAAK,CAAC;EAC/D,MAAM,CAAC6C,gBAAgB,EAAEC,mBAAmB,CAAC,GAAG9C,QAAQ,CAAC,IAAI,CAAC;EAC9D,MAAM,CAAC+C,eAAe,EAAEC,kBAAkB,CAAC,GAAGhD,QAAQ,CAAC;IACrDiD,IAAI,EAAE,EAAE;IACRC,WAAW,EAAE,EAAE;IACfC,iBAAiB,EAAE,EAAE;IACrBC,iBAAiB,EAAE;EACrB,CAAC,CAAC;EACF,MAAM,CAACC,YAAY,EAAEC,eAAe,CAAC,GAAGtD,QAAQ,CAAC;IAC/CuD,IAAI,EAAE,KAAK;IACXC,OAAO,EAAE,EAAE;IACXC,QAAQ,EAAE;EACZ,CAAC,CAAC;;EAEF;EACAxD,SAAS,CAAC,MAAM;IACd,MAAMyD,aAAa,GAAG,MAAAA,CAAA,KAAY;MAChC,IAAI;QACFpB,UAAU,CAAC,IAAI,CAAC;QAChB,IAAIqB,IAAI;;QAER;QACA,IAAI,CAAA5B,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAE6B,IAAI,MAAK,OAAO,IAAI5B,eAAe,IAAIC,gBAAgB,EAAE;UACjE;UACA0B,IAAI,GAAG,MAAMjC,eAAe,CAACmC,eAAe,CAAC5B,gBAAgB,CAAC6B,EAAE,CAAC;QACnE,CAAC,MAAM;UACL;UACAH,IAAI,GAAG,MAAMjC,eAAe,CAACqC,aAAa,CAAC,CAAC;QAC9C;QAEA3B,WAAW,CAACuB,IAAI,CAAC;MACnB,CAAC,CAAC,OAAOK,GAAG,EAAE;QACZC,OAAO,CAAC1B,KAAK,CAAC,sCAAsC,EAAEyB,GAAG,CAAC;QAC1DxB,QAAQ,CAAC,qDAAqD,CAAC;MACjE,CAAC,SAAS;QACRF,UAAU,CAAC,KAAK,CAAC;MACnB;IACF,CAAC;IAEDoB,aAAa,CAAC,CAAC;EACjB,CAAC,EAAE,CAAC3B,IAAI,EAAEC,eAAe,EAAEC,gBAAgB,CAAC,CAAC;;EAE7C;EACA,MAAMiC,sBAAsB,GAAGA,CAAA,KAAM;IACnClB,kBAAkB,CAAC;MACjBC,IAAI,EAAE,EAAE;MACRC,WAAW,EAAE,EAAE;MACfC,iBAAiB,EAAE,EAAE;MACrBC,iBAAiB,EAAE;IACrB,CAAC,CAAC;IACFV,mBAAmB,CAAC,IAAI,CAAC;EAC3B,CAAC;;EAED;EACA,MAAMyB,uBAAuB,GAAGA,CAAA,KAAM;IACpCzB,mBAAmB,CAAC,KAAK,CAAC;EAC5B,CAAC;;EAED;EACA,MAAM0B,sBAAsB,GAAIC,QAAQ,IAAK;IAC3CvB,mBAAmB,CAACuB,QAAQ,CAAC;IAC7BzB,mBAAmB,CAAC,IAAI,CAAC;EAC3B,CAAC;;EAED;EACA,MAAM0B,uBAAuB,GAAGA,CAAA,KAAM;IACpC1B,mBAAmB,CAAC,KAAK,CAAC;IAC1BE,mBAAmB,CAAC,IAAI,CAAC;EAC3B,CAAC;;EAED;EACA,MAAMyB,iBAAiB,GAAIC,CAAC,IAAK;IAC/B,MAAM;MAAEC,IAAI;MAAEC;IAAM,CAAC,GAAGF,CAAC,CAACG,MAAM;IAChC3B,kBAAkB,CAAC;MACjB,GAAGD,eAAe;MAClB,CAAC0B,IAAI,GAAGC;IACV,CAAC,CAAC;EACJ,CAAC;;EAED;EACA,MAAME,oBAAoB,GAAG,MAAAA,CAAA,KAAY;IACvC;IACA,IAAI,CAAC7B,eAAe,CAACE,IAAI,IAAI,CAACF,eAAe,CAACI,iBAAiB,EAAE;MAC/DG,eAAe,CAAC;QACdC,IAAI,EAAE,IAAI;QACVC,OAAO,EAAE,wCAAwC;QACjDC,QAAQ,EAAE;MACZ,CAAC,CAAC;MACF;IACF;;IAEA;IACA,IAAIV,eAAe,CAACI,iBAAiB,KAAKJ,eAAe,CAACK,iBAAiB,EAAE;MAC3EE,eAAe,CAAC;QACdC,IAAI,EAAE,IAAI;QACVC,OAAO,EAAE,4BAA4B;QACrCC,QAAQ,EAAE;MACZ,CAAC,CAAC;MACF;IACF;IAEA,IAAI;MACF,MAAMoB,eAAe,GAAG,MAAMnD,eAAe,CAACoD,cAAc,CAAC;QAC3D7B,IAAI,EAAEF,eAAe,CAACE,IAAI;QAC1BC,WAAW,EAAEH,eAAe,CAACG,WAAW;QACxCC,iBAAiB,EAAEJ,eAAe,CAACI;MACrC,CAAC,CAAC;;MAEF;MACAf,WAAW,CAAC,CAAC,GAAGD,QAAQ,EAAE0C,eAAe,CAAC,CAAC;;MAE3C;MACAV,uBAAuB,CAAC,CAAC;;MAEzB;MACAb,eAAe,CAAC;QACdC,IAAI,EAAE,IAAI;QACVC,OAAO,EAAE,YAAYqB,eAAe,CAAC5B,IAAI,yCAAyC4B,eAAe,CAACE,cAAc,EAAE;QAClHtB,QAAQ,EAAE;MACZ,CAAC,CAAC;IACJ,CAAC,CAAC,OAAOO,GAAG,EAAE;MACZC,OAAO,CAAC1B,KAAK,CAAC,sCAAsC,EAAEyB,GAAG,CAAC;MAC1DV,eAAe,CAAC;QACdC,IAAI,EAAE,IAAI;QACVC,OAAO,EAAE,qCAAqC;QAC9CC,QAAQ,EAAE;MACZ,CAAC,CAAC;IACJ;EACF,CAAC;;EAED;EACA,MAAMuB,oBAAoB,GAAG,MAAAA,CAAA,KAAY;IACvC,IAAI,CAACnC,gBAAgB,EAAE;IAEvB,IAAI;MACF,MAAMnB,eAAe,CAACuD,cAAc,CAACpC,gBAAgB,CAACqC,WAAW,CAAC;;MAElE;MACA9C,WAAW,CAACD,QAAQ,CAACgD,MAAM,CAACC,CAAC,IAAIA,CAAC,CAACF,WAAW,KAAKrC,gBAAgB,CAACqC,WAAW,CAAC,CAAC;;MAEjF;MACAZ,uBAAuB,CAAC,CAAC;;MAEzB;MACAhB,eAAe,CAAC;QACdC,IAAI,EAAE,IAAI;QACVC,OAAO,EAAE,YAAYX,gBAAgB,CAACI,IAAI,0BAA0B;QACpEQ,QAAQ,EAAE;MACZ,CAAC,CAAC;IACJ,CAAC,CAAC,OAAOO,GAAG,EAAE;MACZC,OAAO,CAAC1B,KAAK,CAAC,yCAAyC,EAAEyB,GAAG,CAAC;MAC7DV,eAAe,CAAC;QACdC,IAAI,EAAE,IAAI;QACVC,OAAO,EAAE,wCAAwC;QACjDC,QAAQ,EAAE;MACZ,CAAC,CAAC;IACJ;EACF,CAAC;;EAED;EACA,MAAM4B,oBAAoB,GAAIhB,QAAQ,IAAK;IACzCJ,OAAO,CAACqB,GAAG,CAAC,uBAAuB,EAAEjB,QAAQ,CAAC;;IAE9C;IACAkB,YAAY,CAACC,OAAO,CAAC,oBAAoB,EAAEnB,QAAQ,CAACa,WAAW,CAACO,QAAQ,CAAC,CAAC,CAAC;IAC3EF,YAAY,CAACC,OAAO,CAAC,sBAAsB,EAAEnB,QAAQ,CAACpB,IAAI,CAAC;;IAE3D;IACAf,QAAQ,CAAC,4BAA4B,CAAC;EACxC,CAAC;;EAED;EACA,MAAMwD,uBAAuB,GAAGA,CAAA,KAAM;IACpCpC,eAAe,CAAC;MACd,GAAGD,YAAY;MACfE,IAAI,EAAE;IACR,CAAC,CAAC;EACJ,CAAC;EAED,oBACE3B,OAAA,CAAC1B,GAAG;IAAAyF,QAAA,gBACF/D,OAAA,CAACzB,UAAU;MAACyF,OAAO,EAAC,IAAI;MAACC,YAAY;MAAAF,QAAA,EAClC3D,eAAe,IAAIC,gBAAgB,GAAG,eAAeA,gBAAgB,CAAC6D,QAAQ,EAAE,GAAG;IAAiB;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAC3F,CAAC,eAEbtE,OAAA,CAAC1B,GAAG;MAACiG,EAAE,EAAE;QAAEC,EAAE,EAAE,CAAC;QAAEC,OAAO,EAAE,MAAM;QAAEC,cAAc,EAAE,eAAe;QAAEC,UAAU,EAAE;MAAS,CAAE;MAAAZ,QAAA,gBACzF/D,OAAA,CAACzB,UAAU;QAACyF,OAAO,EAAC,OAAO;QAAAD,QAAA,EACxB3D,eAAe,IAAIC,gBAAgB,GAChC,uCAAuCA,gBAAgB,CAAC6D,QAAQ,EAAE,GAClE;MAAuC;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACjC,CAAC,eACbtE,OAAA,CAACvB,MAAM;QACLuF,OAAO,EAAC,WAAW;QACnBY,KAAK,EAAC,SAAS;QACfC,SAAS,eAAE7E,OAAA,CAACL,OAAO;UAAAwE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAE;QACvBQ,OAAO,EAAExC,sBAAuB;QAAAyB,QAAA,EACjC;MAED;QAAAI,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAQ,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACN,CAAC,EAEL7D,OAAO,gBACNT,OAAA,CAACzB,UAAU;MAAAwF,QAAA,EAAC;IAAuB;MAAAI,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAY,CAAC,GAC9C3D,KAAK,gBACPX,OAAA,CAACX,KAAK;MAACwC,QAAQ,EAAC,OAAO;MAAAkC,QAAA,EAAEpD;IAAK;MAAAwD,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAQ,CAAC,GACrC/D,QAAQ,CAACwE,MAAM,KAAK,CAAC,gBACvB/E,OAAA,CAACxB,KAAK;MAAC+F,EAAE,EAAE;QAAES,CAAC,EAAE,CAAC;QAAEC,SAAS,EAAE;MAAS,CAAE;MAAAlB,QAAA,gBACvC/D,OAAA,CAACzB,UAAU;QAACyF,OAAO,EAAC,IAAI;QAAAD,QAAA,EAAC;MAAuB;QAAAI,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAY,CAAC,eAC7DtE,OAAA,CAACzB,UAAU;QAACyF,OAAO,EAAC,OAAO;QAACY,KAAK,EAAC,gBAAgB;QAAAb,QAAA,EAAC;MAEnD;QAAAI,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAY,CAAC,eACbtE,OAAA,CAACvB,MAAM;QACLuF,OAAO,EAAC,WAAW;QACnBY,KAAK,EAAC,SAAS;QACfC,SAAS,eAAE7E,OAAA,CAACL,OAAO;UAAAwE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAE;QACvBQ,OAAO,EAAExC,sBAAuB;QAChCiC,EAAE,EAAE;UAAEW,EAAE,EAAE;QAAE,CAAE;QAAAnB,QAAA,EACf;MAED;QAAAI,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAQ,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACJ,CAAC,gBAERtE,OAAA,CAACtB,IAAI;MAACyG,SAAS;MAACC,OAAO,EAAE,CAAE;MAAArB,QAAA,EACxBxD,QAAQ,CAAC8E,GAAG,CAAE5C,QAAQ,iBACrBzC,OAAA,CAACtB,IAAI;QAAC4G,IAAI;QAACC,EAAE,EAAE,EAAG;QAACC,EAAE,EAAE,CAAE;QAACC,EAAE,EAAE,CAAE;QAAA1B,QAAA,eAC9B/D,OAAA,CAACrB,IAAI;UAAAoF,QAAA,gBACH/D,OAAA,CAACpB,WAAW;YAAAmF,QAAA,gBACV/D,OAAA,CAAC1B,GAAG;cAACoH,SAAS,EAAC,iBAAiB;cAAA3B,QAAA,gBAC9B/D,OAAA,CAACT,gBAAgB;gBAAA4E,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,eACpBtE,OAAA,CAACzB,UAAU;gBAACyF,OAAO,EAAC,IAAI;gBAAC2B,SAAS,EAAC,KAAK;gBAAA5B,QAAA,EACrCtB,QAAQ,CAACpB;cAAI;gBAAA8C,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACJ,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACV,CAAC,eACNtE,OAAA,CAACzB,UAAU;cAACyF,OAAO,EAAC,OAAO;cAACY,KAAK,EAAC,gBAAgB;cAACL,EAAE,EAAE;gBAAEC,EAAE,EAAE;cAAE,CAAE;cAAAT,QAAA,GAAC,MAC5D,EAACtB,QAAQ,CAACa,WAAW;YAAA;cAAAa,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACf,CAAC,eACbtE,OAAA,CAACzB,UAAU;cAACyF,OAAO,EAAC,OAAO;cAACY,KAAK,EAAC,gBAAgB;cAACL,EAAE,EAAE;gBAAEC,EAAE,EAAE;cAAE,CAAE;cAAAT,QAAA,GAAC,kBAChD,EAACtB,QAAQ,CAACU,cAAc;YAAA;cAAAgB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC9B,CAAC,EACZ7B,QAAQ,CAACnB,WAAW,iBACnBtB,OAAA,CAACzB,UAAU;cAACyF,OAAO,EAAC,OAAO;cAACY,KAAK,EAAC,gBAAgB;cAAAb,QAAA,EAC/CtB,QAAQ,CAACnB;YAAW;cAAA6C,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACX,CACb;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACU,CAAC,eACdtE,OAAA,CAACnB,WAAW;YAAAkF,QAAA,gBACV/D,OAAA,CAACvB,MAAM;cACLuF,OAAO,EAAC,WAAW;cACnB0B,SAAS,EAAC,gBAAgB;cAC1BZ,OAAO,EAAEA,CAAA,KAAMrB,oBAAoB,CAAChB,QAAQ,CAAE;cAAAsB,QAAA,EAC/C;YAED;cAAAI,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC,eACTtE,OAAA,CAACvB,MAAM;cACLuF,OAAO,EAAC,WAAW;cACnB0B,SAAS,EAAC,cAAc;cACxBb,SAAS,eAAE7E,OAAA,CAACP,UAAU;gBAAA0E,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAE;cAC1BQ,OAAO,EAAEA,CAAA,KAAMtC,sBAAsB,CAACC,QAAQ,CAAE;cAAAsB,QAAA,EACjD;YAED;cAAAI,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACE,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACV;MAAC,GAtC6B7B,QAAQ,CAACa,WAAW;QAAAa,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAuCpD,CACP;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACE,CACP,eAGDtE,OAAA,CAAClB,MAAM;MAAC6C,IAAI,EAAEd,gBAAiB;MAAC+E,OAAO,EAAErD,uBAAwB;MAAAwB,QAAA,gBAC/D/D,OAAA,CAACd,WAAW;QAAA6E,QAAA,EAAC;MAAmB;QAAAI,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAa,CAAC,eAC9CtE,OAAA,CAAChB,aAAa;QAAA+E,QAAA,gBACZ/D,OAAA,CAACf,iBAAiB;UAAA8E,QAAA,EAAC;QAEnB;UAAAI,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAmB,CAAC,eACpBtE,OAAA,CAACb,SAAS;UACR0G,SAAS;UACTC,MAAM,EAAC,OAAO;UACdjD,IAAI,EAAC,MAAM;UACXkD,KAAK,EAAC,eAAe;UACrBC,IAAI,EAAC,MAAM;UACXC,SAAS;UACTjC,OAAO,EAAC,UAAU;UAClBlB,KAAK,EAAE3B,eAAe,CAACE,IAAK;UAC5B6E,QAAQ,EAAEvD,iBAAkB;UAC5BwD,QAAQ;QAAA;UAAAhC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACT,CAAC,eACFtE,OAAA,CAACb,SAAS;UACR2G,MAAM,EAAC,OAAO;UACdjD,IAAI,EAAC,aAAa;UAClBkD,KAAK,EAAC,aAAa;UACnBC,IAAI,EAAC,MAAM;UACXC,SAAS;UACTjC,OAAO,EAAC,UAAU;UAClBlB,KAAK,EAAE3B,eAAe,CAACG,WAAY;UACnC4E,QAAQ,EAAEvD;QAAkB;UAAAwB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC7B,CAAC,eACFtE,OAAA,CAACb,SAAS;UACR2G,MAAM,EAAC,OAAO;UACdjD,IAAI,EAAC,mBAAmB;UACxBkD,KAAK,EAAC,UAAU;UAChBC,IAAI,EAAC,UAAU;UACfC,SAAS;UACTjC,OAAO,EAAC,UAAU;UAClBlB,KAAK,EAAE3B,eAAe,CAACI,iBAAkB;UACzC2E,QAAQ,EAAEvD,iBAAkB;UAC5BwD,QAAQ;QAAA;UAAAhC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACT,CAAC,eACFtE,OAAA,CAACb,SAAS;UACR2G,MAAM,EAAC,OAAO;UACdjD,IAAI,EAAC,mBAAmB;UACxBkD,KAAK,EAAC,mBAAmB;UACzBC,IAAI,EAAC,UAAU;UACfC,SAAS;UACTjC,OAAO,EAAC,UAAU;UAClBlB,KAAK,EAAE3B,eAAe,CAACK,iBAAkB;UACzC0E,QAAQ,EAAEvD,iBAAkB;UAC5BwD,QAAQ;QAAA;UAAAhC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACT,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACW,CAAC,eAChBtE,OAAA,CAACjB,aAAa;QAAAgF,QAAA,gBACZ/D,OAAA,CAACvB,MAAM;UAACuF,OAAO,EAAC,WAAW;UAACc,OAAO,EAAEvC,uBAAwB;UAAAwB,QAAA,EAAC;QAAO;UAAAI,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC,eAC9EtE,OAAA,CAACvB,MAAM;UAACqG,OAAO,EAAE9B,oBAAqB;UAACgB,OAAO,EAAC,WAAW;UAAC0B,SAAS,EAAC,gBAAgB;UAAA3B,QAAA,EAAC;QAEtF;UAAAI,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACI,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACV,CAAC,eAGTtE,OAAA,CAAClB,MAAM;MAAC6C,IAAI,EAAEZ,gBAAiB;MAAC6E,OAAO,EAAElD,uBAAwB;MAAAqB,QAAA,gBAC/D/D,OAAA,CAACd,WAAW;QAAA6E,QAAA,EAAC;MAAgB;QAAAI,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAa,CAAC,eAC3CtE,OAAA,CAAChB,aAAa;QAAA+E,QAAA,eACZ/D,OAAA,CAACf,iBAAiB;UAAA8E,QAAA,GAAC,0DACsC,EAAC9C,gBAAgB,aAAhBA,gBAAgB,uBAAhBA,gBAAgB,CAAEI,IAAI,EAAC,wEAEjF;QAAA;UAAA8C,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAmB;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACP,CAAC,eAChBtE,OAAA,CAACjB,aAAa;QAAAgF,QAAA,gBACZ/D,OAAA,CAACvB,MAAM;UAACuF,OAAO,EAAC,WAAW;UAACc,OAAO,EAAEpC,uBAAwB;UAAAqB,QAAA,EAAC;QAAO;UAAAI,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC,eAC9EtE,OAAA,CAACvB,MAAM;UAACqG,OAAO,EAAE1B,oBAAqB;UAACY,OAAO,EAAC,WAAW;UAAC0B,SAAS,EAAC,cAAc;UAAA3B,QAAA,EAAC;QAEpF;UAAAI,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACI,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACV,CAAC,eAGTtE,OAAA,CAACZ,QAAQ;MACPuC,IAAI,EAAEF,YAAY,CAACE,IAAK;MACxByE,gBAAgB,EAAE,IAAK;MACvBR,OAAO,EAAE9B,uBAAwB;MACjCuC,YAAY,EAAE;QAAEC,QAAQ,EAAE,QAAQ;QAAEC,UAAU,EAAE;MAAS,CAAE;MAAAxC,QAAA,eAE3D/D,OAAA,CAACX,KAAK;QAACuG,OAAO,EAAE9B,uBAAwB;QAACjC,QAAQ,EAAEJ,YAAY,CAACI,QAAS;QAAC0C,EAAE,EAAE;UAAEiC,KAAK,EAAE;QAAO,CAAE;QAAAzC,QAAA,EAC7FtC,YAAY,CAACG;MAAO;QAAAuC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAChB;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACA,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACR,CAAC;AAEV,CAAC;AAACpE,EAAA,CA7WID,QAAQ;EAAA,QACwCL,OAAO,EAC1CC,WAAW;AAAA;AAAA4G,EAAA,GAFxBxG,QAAQ;AA+Wd,eAAeA,QAAQ;AAAC,IAAAwG,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}