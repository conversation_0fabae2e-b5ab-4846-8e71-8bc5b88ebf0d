{"ast": null, "code": "import { getQuarter } from \"./getQuarter.mjs\";\nimport { toDate } from \"./toDate.mjs\";\n\n/**\n * @name differenceInCalendarQuarters\n * @category Quarter Helpers\n * @summary Get the number of calendar quarters between the given dates.\n *\n * @description\n * Get the number of calendar quarters between the given dates.\n *\n * @typeParam DateType - The `Date` type, the function operates on. Gets inferred from passed arguments. Allows to use extensions like [`UTCDate`](https://github.com/date-fns/utc).\n *\n * @param dateLeft - The later date\n * @param dateRight - The earlier date\n\n * @returns The number of calendar quarters\n *\n * @example\n * // How many calendar quarters are between 31 December 2013 and 2 July 2014?\n * const result = differenceInCalendarQuarters(\n *   new Date(2014, 6, 2),\n *   new Date(2013, 11, 31)\n * )\n * //=> 3\n */\nexport function differenceInCalendarQuarters(dateLeft, dateRight) {\n  const _dateLeft = toDate(dateLeft);\n  const _dateRight = toDate(dateRight);\n  const yearDiff = _dateLeft.getFullYear() - _dateRight.getFullYear();\n  const quarterDiff = getQuarter(_dateLeft) - getQuarter(_dateRight);\n  return yearDiff * 4 + quarterDiff;\n}\n\n// Fallback for modularized imports:\nexport default differenceInCalendarQuarters;", "map": {"version": 3, "names": ["getQuarter", "toDate", "differenceInCalendarQuarters", "dateLeft", "dateRight", "_dateLeft", "_dateRight", "yearDiff", "getFullYear", "quarterDiff"], "sources": ["C:/CMS/webapp/frontend/node_modules/date-fns/differenceInCalendarQuarters.mjs"], "sourcesContent": ["import { getQuarter } from \"./getQuarter.mjs\";\nimport { toDate } from \"./toDate.mjs\";\n\n/**\n * @name differenceInCalendarQuarters\n * @category Quarter Helpers\n * @summary Get the number of calendar quarters between the given dates.\n *\n * @description\n * Get the number of calendar quarters between the given dates.\n *\n * @typeParam DateType - The `Date` type, the function operates on. Gets inferred from passed arguments. Allows to use extensions like [`UTCDate`](https://github.com/date-fns/utc).\n *\n * @param dateLeft - The later date\n * @param dateRight - The earlier date\n\n * @returns The number of calendar quarters\n *\n * @example\n * // How many calendar quarters are between 31 December 2013 and 2 July 2014?\n * const result = differenceInCalendarQuarters(\n *   new Date(2014, 6, 2),\n *   new Date(2013, 11, 31)\n * )\n * //=> 3\n */\nexport function differenceInCalendarQuarters(dateLeft, dateRight) {\n  const _dateLeft = toDate(dateLeft);\n  const _dateRight = toDate(dateRight);\n\n  const yearDiff = _dateLeft.getFullYear() - _dateRight.getFullYear();\n  const quarterDiff = getQuarter(_dateLeft) - getQuarter(_dateRight);\n\n  return yearDiff * 4 + quarterDiff;\n}\n\n// Fallback for modularized imports:\nexport default differenceInCalendarQuarters;\n"], "mappings": "AAAA,SAASA,UAAU,QAAQ,kBAAkB;AAC7C,SAASC,MAAM,QAAQ,cAAc;;AAErC;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO,SAASC,4BAA4BA,CAACC,QAAQ,EAAEC,SAAS,EAAE;EAChE,MAAMC,SAAS,GAAGJ,MAAM,CAACE,QAAQ,CAAC;EAClC,MAAMG,UAAU,GAAGL,MAAM,CAACG,SAAS,CAAC;EAEpC,MAAMG,QAAQ,GAAGF,SAAS,CAACG,WAAW,CAAC,CAAC,GAAGF,UAAU,CAACE,WAAW,CAAC,CAAC;EACnE,MAAMC,WAAW,GAAGT,UAAU,CAACK,SAAS,CAAC,GAAGL,UAAU,CAACM,UAAU,CAAC;EAElE,OAAOC,QAAQ,GAAG,CAAC,GAAGE,WAAW;AACnC;;AAEA;AACA,eAAeP,4BAA4B", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}