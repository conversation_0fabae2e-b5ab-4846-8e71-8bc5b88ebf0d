{"ast": null, "code": "import buildLocalizeFn from \"../../../_lib/buildLocalizeFn/index.js\"; // Ref: https://www.unicode.org/cldr/charts/32/summary/ta.html\nvar eraValues = {\n  narrow: ['கி.மு.', 'கி.பி.'],\n  abbreviated: ['கி.மு.', 'கி.பி.'],\n  // CLDR #1624, #1626\n  wide: ['கிறிஸ்துவுக்கு முன்', 'அன்னோ டோமினி'] // CLDR #1620, #1622\n};\nvar quarterValues = {\n  // CLDR #1644 - #1647\n  narrow: ['1', '2', '3', '4'],\n  // CLDR #1636 - #1639\n  abbreviated: ['காலா.1', 'காலா.2', 'காலா.3', 'காலா.4'],\n  // CLDR #1628 - #1631\n  wide: ['ஒன்றாம் காலாண்டு', 'இரண்டாம் காலாண்டு', 'மூன்றாம் காலாண்டு', 'நான்காம் காலாண்டு']\n};\nvar monthValues = {\n  // CLDR #700 - #711\n  narrow: ['ஜ', 'பி', 'மா', 'ஏ', 'மே', 'ஜூ', 'ஜூ', 'ஆ', 'செ', 'அ', 'ந', 'டி'],\n  // CLDR #1676 - #1687\n  abbreviated: ['ஜன.', 'பிப்.', 'மார்.', 'ஏப்.', 'மே', 'ஜூன்', 'ஜூலை', 'ஆக.', 'செப்.', 'அக்.', 'நவ.', 'டிச.'],\n  // CLDR #1652 - #1663\n  wide: ['ஜனவரி',\n  // January\n  'பிப்ரவரி',\n  // February\n  'மார்ச்',\n  // March\n  'ஏப்ரல்',\n  // April\n  'மே',\n  // May\n  'ஜூன்',\n  // June\n  'ஜூலை',\n  // July\n  'ஆகஸ்ட்',\n  // August\n  'செப்டம்பர்',\n  // September\n  'அக்டோபர்',\n  // October\n  'நவம்பர்',\n  // November\n  'டிசம்பர்' // December\n  ]\n};\nvar dayValues = {\n  // CLDR #1766 - #1772\n  narrow: ['ஞா', 'தி', 'செ', 'பு', 'வி', 'வெ', 'ச'],\n  // CLDR #1752 - #1758\n  short: ['ஞா', 'தி', 'செ', 'பு', 'வி', 'வெ', 'ச'],\n  // CLDR #1738 - #1744\n  abbreviated: ['ஞாயி.', 'திங்.', 'செவ்.', 'புத.', 'வியா.', 'வெள்.', 'சனி'],\n  // CLDR #1724 - #1730\n  wide: ['ஞாயிறு',\n  // Sunday\n  'திங்கள்',\n  // Monday\n  'செவ்வாய்',\n  // Tuesday\n  'புதன்',\n  // Wednesday\n  'வியாழன்',\n  // Thursday\n  'வெள்ளி',\n  // Friday\n  'சனி' // Saturday\n  ]\n};\n\n// CLDR #1780 - #1845\nvar dayPeriodValues = {\n  narrow: {\n    am: 'மு.ப',\n    pm: 'பி.ப',\n    midnight: 'நள்.',\n    noon: 'நண்.',\n    morning: 'கா.',\n    afternoon: 'மதி.',\n    evening: 'மா.',\n    night: 'இர.'\n  },\n  abbreviated: {\n    am: 'முற்பகல்',\n    pm: 'பிற்பகல்',\n    midnight: 'நள்ளிரவு',\n    noon: 'நண்பகல்',\n    morning: 'காலை',\n    afternoon: 'மதியம்',\n    evening: 'மாலை',\n    night: 'இரவு'\n  },\n  wide: {\n    am: 'முற்பகல்',\n    pm: 'பிற்பகல்',\n    midnight: 'நள்ளிரவு',\n    noon: 'நண்பகல்',\n    morning: 'காலை',\n    afternoon: 'மதியம்',\n    evening: 'மாலை',\n    night: 'இரவு'\n  }\n};\n\n// CLDR #1780 - #1845\nvar formattingDayPeriodValues = {\n  narrow: {\n    am: 'மு.ப',\n    pm: 'பி.ப',\n    midnight: 'நள்.',\n    noon: 'நண்.',\n    morning: 'கா.',\n    afternoon: 'மதி.',\n    evening: 'மா.',\n    night: 'இர.'\n  },\n  abbreviated: {\n    am: 'முற்பகல்',\n    pm: 'பிற்பகல்',\n    midnight: 'நள்ளிரவு',\n    noon: 'நண்பகல்',\n    morning: 'காலை',\n    afternoon: 'மதியம்',\n    evening: 'மாலை',\n    night: 'இரவு'\n  },\n  wide: {\n    am: 'முற்பகல்',\n    pm: 'பிற்பகல்',\n    midnight: 'நள்ளிரவு',\n    noon: 'நண்பகல்',\n    morning: 'காலை',\n    afternoon: 'மதியம்',\n    evening: 'மாலை',\n    night: 'இரவு'\n  }\n};\nvar ordinalNumber = function ordinalNumber(dirtyNumber, _options) {\n  return String(dirtyNumber);\n};\nvar localize = {\n  ordinalNumber: ordinalNumber,\n  era: buildLocalizeFn({\n    values: eraValues,\n    defaultWidth: 'wide'\n  }),\n  quarter: buildLocalizeFn({\n    values: quarterValues,\n    defaultWidth: 'wide',\n    argumentCallback: function argumentCallback(quarter) {\n      return quarter - 1;\n    }\n  }),\n  month: buildLocalizeFn({\n    values: monthValues,\n    defaultWidth: 'wide'\n  }),\n  day: buildLocalizeFn({\n    values: dayValues,\n    defaultWidth: 'wide'\n  }),\n  dayPeriod: buildLocalizeFn({\n    values: dayPeriodValues,\n    defaultWidth: 'wide',\n    formattingValues: formattingDayPeriodValues,\n    defaultFormattingWidth: 'wide'\n  })\n};\nexport default localize;", "map": {"version": 3, "names": ["buildLocalizeFn", "<PERSON><PERSON><PERSON><PERSON>", "narrow", "abbreviated", "wide", "quarterValues", "month<PERSON><PERSON><PERSON>", "dayV<PERSON><PERSON>", "short", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "am", "pm", "midnight", "noon", "morning", "afternoon", "evening", "night", "formattingDayPeriodValues", "ordinalNumber", "dirtyNumber", "_options", "String", "localize", "era", "values", "defaultWidth", "quarter", "argument<PERSON>allback", "month", "day", "<PERSON><PERSON><PERSON><PERSON>", "formattingValues", "defaultFormattingWidth"], "sources": ["C:/CMS/webapp/frontend/node_modules/date-fns/esm/locale/ta/_lib/localize/index.js"], "sourcesContent": ["import buildLocalizeFn from \"../../../_lib/buildLocalizeFn/index.js\"; // Ref: https://www.unicode.org/cldr/charts/32/summary/ta.html\nvar eraValues = {\n  narrow: ['கி.மு.', 'கி.பி.'],\n  abbreviated: ['கி.மு.', 'கி.பி.'],\n  // CLDR #1624, #1626\n  wide: ['கிறிஸ்துவுக்கு முன்', 'அன்னோ டோமினி'] // CLDR #1620, #1622\n};\n\nvar quarterValues = {\n  // CLDR #1644 - #1647\n  narrow: ['1', '2', '3', '4'],\n  // CLDR #1636 - #1639\n  abbreviated: ['காலா.1', 'காலா.2', 'காலா.3', 'காலா.4'],\n  // CLDR #1628 - #1631\n  wide: ['ஒன்றாம் காலாண்டு', 'இரண்டாம் காலாண்டு', 'மூன்றாம் காலாண்டு', 'நான்காம் காலாண்டு']\n};\nvar monthValues = {\n  // CLDR #700 - #711\n  narrow: ['ஜ', 'பி', 'மா', 'ஏ', 'மே', 'ஜூ', 'ஜூ', 'ஆ', 'செ', 'அ', 'ந', 'டி'],\n  // CLDR #1676 - #1687\n  abbreviated: ['ஜன.', 'பிப்.', 'மார்.', 'ஏப்.', 'மே', 'ஜூன்', 'ஜூலை', 'ஆக.', 'செப்.', 'அக்.', 'நவ.', 'டிச.'],\n  // CLDR #1652 - #1663\n  wide: ['ஜனவரி',\n  // January\n  'பிப்ரவரி',\n  // February\n  'மார்ச்',\n  // March\n  'ஏப்ரல்',\n  // April\n  'மே',\n  // May\n  'ஜூன்',\n  // June\n  'ஜூலை',\n  // July\n  'ஆகஸ்ட்',\n  // August\n  'செப்டம்பர்',\n  // September\n  'அக்டோபர்',\n  // October\n  'நவம்பர்',\n  // November\n  'டிசம்பர்' // December\n  ]\n};\n\nvar dayValues = {\n  // CLDR #1766 - #1772\n  narrow: ['ஞா', 'தி', 'செ', 'பு', 'வி', 'வெ', 'ச'],\n  // CLDR #1752 - #1758\n  short: ['ஞா', 'தி', 'செ', 'பு', 'வி', 'வெ', 'ச'],\n  // CLDR #1738 - #1744\n  abbreviated: ['ஞாயி.', 'திங்.', 'செவ்.', 'புத.', 'வியா.', 'வெள்.', 'சனி'],\n  // CLDR #1724 - #1730\n  wide: ['ஞாயிறு',\n  // Sunday\n  'திங்கள்',\n  // Monday\n  'செவ்வாய்',\n  // Tuesday\n  'புதன்',\n  // Wednesday\n  'வியாழன்',\n  // Thursday\n  'வெள்ளி',\n  // Friday\n  'சனி' // Saturday\n  ]\n};\n\n// CLDR #1780 - #1845\nvar dayPeriodValues = {\n  narrow: {\n    am: 'மு.ப',\n    pm: 'பி.ப',\n    midnight: 'நள்.',\n    noon: 'நண்.',\n    morning: 'கா.',\n    afternoon: 'மதி.',\n    evening: 'மா.',\n    night: 'இர.'\n  },\n  abbreviated: {\n    am: 'முற்பகல்',\n    pm: 'பிற்பகல்',\n    midnight: 'நள்ளிரவு',\n    noon: 'நண்பகல்',\n    morning: 'காலை',\n    afternoon: 'மதியம்',\n    evening: 'மாலை',\n    night: 'இரவு'\n  },\n  wide: {\n    am: 'முற்பகல்',\n    pm: 'பிற்பகல்',\n    midnight: 'நள்ளிரவு',\n    noon: 'நண்பகல்',\n    morning: 'காலை',\n    afternoon: 'மதியம்',\n    evening: 'மாலை',\n    night: 'இரவு'\n  }\n};\n\n// CLDR #1780 - #1845\nvar formattingDayPeriodValues = {\n  narrow: {\n    am: 'மு.ப',\n    pm: 'பி.ப',\n    midnight: 'நள்.',\n    noon: 'நண்.',\n    morning: 'கா.',\n    afternoon: 'மதி.',\n    evening: 'மா.',\n    night: 'இர.'\n  },\n  abbreviated: {\n    am: 'முற்பகல்',\n    pm: 'பிற்பகல்',\n    midnight: 'நள்ளிரவு',\n    noon: 'நண்பகல்',\n    morning: 'காலை',\n    afternoon: 'மதியம்',\n    evening: 'மாலை',\n    night: 'இரவு'\n  },\n  wide: {\n    am: 'முற்பகல்',\n    pm: 'பிற்பகல்',\n    midnight: 'நள்ளிரவு',\n    noon: 'நண்பகல்',\n    morning: 'காலை',\n    afternoon: 'மதியம்',\n    evening: 'மாலை',\n    night: 'இரவு'\n  }\n};\nvar ordinalNumber = function ordinalNumber(dirtyNumber, _options) {\n  return String(dirtyNumber);\n};\nvar localize = {\n  ordinalNumber: ordinalNumber,\n  era: buildLocalizeFn({\n    values: eraValues,\n    defaultWidth: 'wide'\n  }),\n  quarter: buildLocalizeFn({\n    values: quarterValues,\n    defaultWidth: 'wide',\n    argumentCallback: function argumentCallback(quarter) {\n      return quarter - 1;\n    }\n  }),\n  month: buildLocalizeFn({\n    values: monthValues,\n    defaultWidth: 'wide'\n  }),\n  day: buildLocalizeFn({\n    values: dayValues,\n    defaultWidth: 'wide'\n  }),\n  dayPeriod: buildLocalizeFn({\n    values: dayPeriodValues,\n    defaultWidth: 'wide',\n    formattingValues: formattingDayPeriodValues,\n    defaultFormattingWidth: 'wide'\n  })\n};\nexport default localize;"], "mappings": "AAAA,OAAOA,eAAe,MAAM,wCAAwC,CAAC,CAAC;AACtE,IAAIC,SAAS,GAAG;EACdC,MAAM,EAAE,CAAC,QAAQ,EAAE,QAAQ,CAAC;EAC5BC,WAAW,EAAE,CAAC,QAAQ,EAAE,QAAQ,CAAC;EACjC;EACAC,IAAI,EAAE,CAAC,qBAAqB,EAAE,cAAc,CAAC,CAAC;AAChD,CAAC;AAED,IAAIC,aAAa,GAAG;EAClB;EACAH,MAAM,EAAE,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,CAAC;EAC5B;EACAC,WAAW,EAAE,CAAC,QAAQ,EAAE,QAAQ,EAAE,QAAQ,EAAE,QAAQ,CAAC;EACrD;EACAC,IAAI,EAAE,CAAC,kBAAkB,EAAE,mBAAmB,EAAE,mBAAmB,EAAE,mBAAmB;AAC1F,CAAC;AACD,IAAIE,WAAW,GAAG;EAChB;EACAJ,MAAM,EAAE,CAAC,GAAG,EAAE,IAAI,EAAE,IAAI,EAAE,GAAG,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,GAAG,EAAE,IAAI,EAAE,GAAG,EAAE,GAAG,EAAE,IAAI,CAAC;EAC3E;EACAC,WAAW,EAAE,CAAC,KAAK,EAAE,OAAO,EAAE,OAAO,EAAE,MAAM,EAAE,IAAI,EAAE,MAAM,EAAE,MAAM,EAAE,KAAK,EAAE,OAAO,EAAE,MAAM,EAAE,KAAK,EAAE,MAAM,CAAC;EAC3G;EACAC,IAAI,EAAE,CAAC,OAAO;EACd;EACA,UAAU;EACV;EACA,QAAQ;EACR;EACA,QAAQ;EACR;EACA,IAAI;EACJ;EACA,MAAM;EACN;EACA,MAAM;EACN;EACA,QAAQ;EACR;EACA,YAAY;EACZ;EACA,UAAU;EACV;EACA,SAAS;EACT;EACA,UAAU,CAAC;EAAA;AAEb,CAAC;AAED,IAAIG,SAAS,GAAG;EACd;EACAL,MAAM,EAAE,CAAC,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,GAAG,CAAC;EACjD;EACAM,KAAK,EAAE,CAAC,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,GAAG,CAAC;EAChD;EACAL,WAAW,EAAE,CAAC,OAAO,EAAE,OAAO,EAAE,OAAO,EAAE,MAAM,EAAE,OAAO,EAAE,OAAO,EAAE,KAAK,CAAC;EACzE;EACAC,IAAI,EAAE,CAAC,QAAQ;EACf;EACA,SAAS;EACT;EACA,UAAU;EACV;EACA,OAAO;EACP;EACA,SAAS;EACT;EACA,QAAQ;EACR;EACA,KAAK,CAAC;EAAA;AAER,CAAC;;AAED;AACA,IAAIK,eAAe,GAAG;EACpBP,MAAM,EAAE;IACNQ,EAAE,EAAE,MAAM;IACVC,EAAE,EAAE,MAAM;IACVC,QAAQ,EAAE,MAAM;IAChBC,IAAI,EAAE,MAAM;IACZC,OAAO,EAAE,KAAK;IACdC,SAAS,EAAE,MAAM;IACjBC,OAAO,EAAE,KAAK;IACdC,KAAK,EAAE;EACT,CAAC;EACDd,WAAW,EAAE;IACXO,EAAE,EAAE,UAAU;IACdC,EAAE,EAAE,UAAU;IACdC,QAAQ,EAAE,UAAU;IACpBC,IAAI,EAAE,SAAS;IACfC,OAAO,EAAE,MAAM;IACfC,SAAS,EAAE,QAAQ;IACnBC,OAAO,EAAE,MAAM;IACfC,KAAK,EAAE;EACT,CAAC;EACDb,IAAI,EAAE;IACJM,EAAE,EAAE,UAAU;IACdC,EAAE,EAAE,UAAU;IACdC,QAAQ,EAAE,UAAU;IACpBC,IAAI,EAAE,SAAS;IACfC,OAAO,EAAE,MAAM;IACfC,SAAS,EAAE,QAAQ;IACnBC,OAAO,EAAE,MAAM;IACfC,KAAK,EAAE;EACT;AACF,CAAC;;AAED;AACA,IAAIC,yBAAyB,GAAG;EAC9BhB,MAAM,EAAE;IACNQ,EAAE,EAAE,MAAM;IACVC,EAAE,EAAE,MAAM;IACVC,QAAQ,EAAE,MAAM;IAChBC,IAAI,EAAE,MAAM;IACZC,OAAO,EAAE,KAAK;IACdC,SAAS,EAAE,MAAM;IACjBC,OAAO,EAAE,KAAK;IACdC,KAAK,EAAE;EACT,CAAC;EACDd,WAAW,EAAE;IACXO,EAAE,EAAE,UAAU;IACdC,EAAE,EAAE,UAAU;IACdC,QAAQ,EAAE,UAAU;IACpBC,IAAI,EAAE,SAAS;IACfC,OAAO,EAAE,MAAM;IACfC,SAAS,EAAE,QAAQ;IACnBC,OAAO,EAAE,MAAM;IACfC,KAAK,EAAE;EACT,CAAC;EACDb,IAAI,EAAE;IACJM,EAAE,EAAE,UAAU;IACdC,EAAE,EAAE,UAAU;IACdC,QAAQ,EAAE,UAAU;IACpBC,IAAI,EAAE,SAAS;IACfC,OAAO,EAAE,MAAM;IACfC,SAAS,EAAE,QAAQ;IACnBC,OAAO,EAAE,MAAM;IACfC,KAAK,EAAE;EACT;AACF,CAAC;AACD,IAAIE,aAAa,GAAG,SAASA,aAAaA,CAACC,WAAW,EAAEC,QAAQ,EAAE;EAChE,OAAOC,MAAM,CAACF,WAAW,CAAC;AAC5B,CAAC;AACD,IAAIG,QAAQ,GAAG;EACbJ,aAAa,EAAEA,aAAa;EAC5BK,GAAG,EAAExB,eAAe,CAAC;IACnByB,MAAM,EAAExB,SAAS;IACjByB,YAAY,EAAE;EAChB,CAAC,CAAC;EACFC,OAAO,EAAE3B,eAAe,CAAC;IACvByB,MAAM,EAAEpB,aAAa;IACrBqB,YAAY,EAAE,MAAM;IACpBE,gBAAgB,EAAE,SAASA,gBAAgBA,CAACD,OAAO,EAAE;MACnD,OAAOA,OAAO,GAAG,CAAC;IACpB;EACF,CAAC,CAAC;EACFE,KAAK,EAAE7B,eAAe,CAAC;IACrByB,MAAM,EAAEnB,WAAW;IACnBoB,YAAY,EAAE;EAChB,CAAC,CAAC;EACFI,GAAG,EAAE9B,eAAe,CAAC;IACnByB,MAAM,EAAElB,SAAS;IACjBmB,YAAY,EAAE;EAChB,CAAC,CAAC;EACFK,SAAS,EAAE/B,eAAe,CAAC;IACzByB,MAAM,EAAEhB,eAAe;IACvBiB,YAAY,EAAE,MAAM;IACpBM,gBAAgB,EAAEd,yBAAyB;IAC3Ce,sBAAsB,EAAE;EAC1B,CAAC;AACH,CAAC;AACD,eAAeV,QAAQ", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}