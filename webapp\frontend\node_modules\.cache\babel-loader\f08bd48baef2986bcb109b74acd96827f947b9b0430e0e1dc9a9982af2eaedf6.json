{"ast": null, "code": "import axios from'axios';import config from'../config';// Crea un'istanza di axios con configurazione migliorata\nconst axiosInstance=axios.create({baseURL:config.API_URL,headers:{'Content-Type':'application/json'},timeout:60000,// 60 secondi\nwithCredentials:false// Disabilita l'invio di credenziali per evitare problemi CORS\n});// Configura axios per includere il token in tutte le richieste\naxiosInstance.interceptors.request.use(config=>{const token=localStorage.getItem('token');if(token){config.headers.Authorization=`Bearer ${token}`;}return config;},error=>{console.error('Errore nella configurazione della richiesta:',error);return Promise.reject(error);});// Gestione globale degli errori\naxiosInstance.interceptors.response.use(response=>{return response;},error=>{// Log dettagliato dell'errore (solo in console, non mostrato all'utente)\nconsole.error('Errore nella risposta:',error);// Gestione specifica per errori di rete\nif(error.message&&(error.message.includes('Network Error')||error.message.includes('Failed to fetch'))){console.error('Errore di rete:',error);error.isNetworkError=true;error.customMessage='Impossibile connettersi al server. Verifica la connessione di rete e riprova.';// Sostituisci il messaggio di errore originale per evitare alert da localhost\nerror.message=error.customMessage;}// Gestione errori di timeout\nif(error.code==='ECONNABORTED'){console.error('Timeout della richiesta:',error);error.isTimeoutError=true;error.customMessage='La richiesta ha impiegato troppo tempo. Riprova più tardi.';// Sostituisci il messaggio di errore originale per evitare alert da localhost\nerror.message=error.customMessage;}// Gestione errori CORS\nif(error.message&&error.message.includes('CORS')){error.customMessage='Errore di comunicazione con il server. Riprova più tardi.';error.message=error.customMessage;}return Promise.reject(error);});export default axiosInstance;", "map": {"version": 3, "names": ["axios", "config", "axiosInstance", "create", "baseURL", "API_URL", "headers", "timeout", "withCredentials", "interceptors", "request", "use", "token", "localStorage", "getItem", "Authorization", "error", "console", "Promise", "reject", "response", "message", "includes", "isNetworkError", "customMessage", "code", "isTimeoutError"], "sources": ["C:/Users/<USER>/PycharmProjects/CMS/webapp/frontend/src/services/axiosConfig.js"], "sourcesContent": ["import axios from 'axios';\nimport config from '../config';\n\n// Crea un'istanza di axios con configurazione migliorata\nconst axiosInstance = axios.create({\n  baseURL: config.API_URL,\n  headers: {\n    'Content-Type': 'application/json',\n  },\n  timeout: 60000, // 60 secondi\n  withCredentials: false, // Disabilita l'invio di credenziali per evitare problemi CORS\n});\n\n// Configura axios per includere il token in tutte le richieste\naxiosInstance.interceptors.request.use(\n  (config) => {\n    const token = localStorage.getItem('token');\n    if (token) {\n      config.headers.Authorization = `Bearer ${token}`;\n    }\n    return config;\n  },\n  (error) => {\n    console.error('Errore nella configurazione della richiesta:', error);\n    return Promise.reject(error);\n  }\n);\n\n// Gestione globale degli errori\naxiosInstance.interceptors.response.use(\n  (response) => {\n    return response;\n  },\n  (error) => {\n    // Log dettagliato dell'errore (solo in console, non mostrato all'utente)\n    console.error('Errore nella risposta:', error);\n\n    // Gestione specifica per errori di rete\n    if (error.message && (error.message.includes('Network Error') || error.message.includes('Failed to fetch'))) {\n      console.error('Errore di rete:', error);\n      error.isNetworkError = true;\n      error.customMessage = 'Impossibile connettersi al server. Verifica la connessione di rete e riprova.';\n      // Sostituisci il messaggio di errore originale per evitare alert da localhost\n      error.message = error.customMessage;\n    }\n\n    // Gestione errori di timeout\n    if (error.code === 'ECONNABORTED') {\n      console.error('Timeout della richiesta:', error);\n      error.isTimeoutError = true;\n      error.customMessage = 'La richiesta ha impiegato troppo tempo. Riprova più tardi.';\n      // Sostituisci il messaggio di errore originale per evitare alert da localhost\n      error.message = error.customMessage;\n    }\n\n    // Gestione errori CORS\n    if (error.message && error.message.includes('CORS')) {\n      error.customMessage = 'Errore di comunicazione con il server. Riprova più tardi.';\n      error.message = error.customMessage;\n    }\n\n    return Promise.reject(error);\n  }\n);\n\nexport default axiosInstance;\n"], "mappings": "AAAA,MAAO,CAAAA,KAAK,KAAM,OAAO,CACzB,MAAO,CAAAC,MAAM,KAAM,WAAW,CAE9B;AACA,KAAM,CAAAC,aAAa,CAAGF,KAAK,CAACG,MAAM,CAAC,CACjCC,OAAO,CAAEH,MAAM,CAACI,OAAO,CACvBC,OAAO,CAAE,CACP,cAAc,CAAE,kBAClB,CAAC,CACDC,OAAO,CAAE,KAAK,CAAE;AAChBC,eAAe,CAAE,KAAO;AAC1B,CAAC,CAAC,CAEF;AACAN,aAAa,CAACO,YAAY,CAACC,OAAO,CAACC,GAAG,CACnCV,MAAM,EAAK,CACV,KAAM,CAAAW,KAAK,CAAGC,YAAY,CAACC,OAAO,CAAC,OAAO,CAAC,CAC3C,GAAIF,KAAK,CAAE,CACTX,MAAM,CAACK,OAAO,CAACS,aAAa,CAAG,UAAUH,KAAK,EAAE,CAClD,CACA,MAAO,CAAAX,MAAM,CACf,CAAC,CACAe,KAAK,EAAK,CACTC,OAAO,CAACD,KAAK,CAAC,8CAA8C,CAAEA,KAAK,CAAC,CACpE,MAAO,CAAAE,OAAO,CAACC,MAAM,CAACH,KAAK,CAAC,CAC9B,CACF,CAAC,CAED;AACAd,aAAa,CAACO,YAAY,CAACW,QAAQ,CAACT,GAAG,CACpCS,QAAQ,EAAK,CACZ,MAAO,CAAAA,QAAQ,CACjB,CAAC,CACAJ,KAAK,EAAK,CACT;AACAC,OAAO,CAACD,KAAK,CAAC,wBAAwB,CAAEA,KAAK,CAAC,CAE9C;AACA,GAAIA,KAAK,CAACK,OAAO,GAAKL,KAAK,CAACK,OAAO,CAACC,QAAQ,CAAC,eAAe,CAAC,EAAIN,KAAK,CAACK,OAAO,CAACC,QAAQ,CAAC,iBAAiB,CAAC,CAAC,CAAE,CAC3GL,OAAO,CAACD,KAAK,CAAC,iBAAiB,CAAEA,KAAK,CAAC,CACvCA,KAAK,CAACO,cAAc,CAAG,IAAI,CAC3BP,KAAK,CAACQ,aAAa,CAAG,+EAA+E,CACrG;AACAR,KAAK,CAACK,OAAO,CAAGL,KAAK,CAACQ,aAAa,CACrC,CAEA;AACA,GAAIR,KAAK,CAACS,IAAI,GAAK,cAAc,CAAE,CACjCR,OAAO,CAACD,KAAK,CAAC,0BAA0B,CAAEA,KAAK,CAAC,CAChDA,KAAK,CAACU,cAAc,CAAG,IAAI,CAC3BV,KAAK,CAACQ,aAAa,CAAG,4DAA4D,CAClF;AACAR,KAAK,CAACK,OAAO,CAAGL,KAAK,CAACQ,aAAa,CACrC,CAEA;AACA,GAAIR,KAAK,CAACK,OAAO,EAAIL,KAAK,CAACK,OAAO,CAACC,QAAQ,CAAC,MAAM,CAAC,CAAE,CACnDN,KAAK,CAACQ,aAAa,CAAG,2DAA2D,CACjFR,KAAK,CAACK,OAAO,CAAGL,KAAK,CAACQ,aAAa,CACrC,CAEA,MAAO,CAAAN,OAAO,CAACC,MAAM,CAACH,KAAK,CAAC,CAC9B,CACF,CAAC,CAED,cAAe,CAAAd,aAAa", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}