{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\PycharmProjects\\\\CMS\\\\webapp\\\\frontend\\\\src\\\\components\\\\cavi\\\\ParcoCavi.js\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect } from 'react';\nimport { Box, Typography, Button, Paper, Grid, Card, CardContent, CardActions, Dialog, DialogTitle, DialogContent, DialogActions, TextField, FormControl, InputLabel, Select, MenuItem, List, ListItem, ListItemText, ListItemIcon, ListItemButton, Divider, Alert, CircularProgress, Table, TableBody, TableCell, TableContainer, TableHead, TableRow } from '@mui/material';\nimport { Add as AddIcon, Edit as EditIcon, Delete as DeleteIcon, History as HistoryIcon, Save as SaveIcon, ViewList as ViewListIcon } from '@mui/icons-material';\nimport parcoCaviService from '../../services/parcoCaviService';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst ParcoCavi = ({\n  cantiereId,\n  onSuccess,\n  onError\n}) => {\n  _s();\n  const [loading, setLoading] = useState(false);\n  const [bobine, setBobine] = useState([]);\n  const [selectedOption, setSelectedOption] = useState(null);\n  const [openDialog, setOpenDialog] = useState(false);\n  const [dialogType, setDialogType] = useState('');\n  const [selectedBobina, setSelectedBobina] = useState(null);\n  const [formData, setFormData] = useState({\n    numero_bobina: '',\n    utility: '',\n    tipologia: '',\n    n_conduttori: '',\n    sezione: '',\n    metri_totali: '',\n    metri_residui: '',\n    stato_bobina: 'DISPONIBILE',\n    ubicazione_bobina: '',\n    fornitore: '',\n    n_DDT: '',\n    data_DDT: '',\n    configurazione: ''\n  });\n  const [storicoUtilizzo, setStoricoUtilizzo] = useState([]);\n\n  // Carica le bobine disponibili\n  const loadBobine = async () => {\n    try {\n      setLoading(true);\n      const data = await parcoCaviService.getBobine(cantiereId);\n      setBobine(data);\n    } catch (error) {\n      onError('Errore nel caricamento delle bobine');\n      console.error('Errore nel caricamento delle bobine:', error);\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  // Carica lo storico utilizzo bobine\n  const loadStoricoUtilizzo = async () => {\n    try {\n      setLoading(true);\n      const data = await parcoCaviService.getStoricoUtilizzo(cantiereId);\n      setStoricoUtilizzo(data);\n    } catch (error) {\n      onError('Errore nel caricamento dello storico utilizzo');\n      console.error('Errore nel caricamento dello storico utilizzo:', error);\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  // Carica i dati all'avvio del componente\n  useEffect(() => {\n    loadBobine();\n  }, [cantiereId]);\n\n  // Gestisce la selezione di un'opzione dal menu\n  const handleOptionSelect = option => {\n    setSelectedOption(option);\n    if (option === 'visualizzaBobine') {\n      loadBobine();\n    } else if (option === 'creaBobina') {\n      setDialogType('creaBobina');\n      setFormData({\n        numero_bobina: '',\n        utility: '',\n        tipologia: '',\n        n_conduttori: '',\n        sezione: '',\n        metri_totali: '',\n        metri_residui: '',\n        stato_bobina: 'DISPONIBILE',\n        ubicazione_bobina: '',\n        fornitore: '',\n        n_DDT: '',\n        data_DDT: '',\n        configurazione: ''\n      });\n      setOpenDialog(true);\n    } else if (option === 'modificaBobina') {\n      loadBobine();\n      setDialogType('selezionaBobina');\n      setOpenDialog(true);\n    } else if (option === 'eliminaBobina') {\n      loadBobine();\n      setDialogType('eliminaBobina');\n      setOpenDialog(true);\n    } else if (option === 'visualizzaStorico') {\n      loadStoricoUtilizzo();\n      setDialogType('visualizzaStorico');\n      setOpenDialog(true);\n    }\n  };\n\n  // Gestisce la chiusura del dialog\n  const handleCloseDialog = () => {\n    setOpenDialog(false);\n    setSelectedBobina(null);\n    setFormData({\n      numero_bobina: '',\n      utility: '',\n      tipologia: '',\n      n_conduttori: '',\n      sezione: '',\n      metri_totali: '',\n      metri_residui: '',\n      stato_bobina: 'DISPONIBILE',\n      ubicazione_bobina: '',\n      fornitore: '',\n      n_DDT: '',\n      data_DDT: '',\n      configurazione: ''\n    });\n  };\n\n  // Gestisce la selezione di una bobina\n  const handleBobinaSelect = bobina => {\n    setSelectedBobina(bobina);\n    if (dialogType === 'selezionaBobina') {\n      setDialogType('modificaBobina');\n      setFormData({\n        numero_bobina: bobina.numero_bobina,\n        utility: bobina.utility || '',\n        tipologia: bobina.tipologia || '',\n        n_conduttori: bobina.n_conduttori || '',\n        sezione: bobina.sezione || '',\n        metri_totali: bobina.metri_totali || '',\n        metri_residui: bobina.metri_residui || '',\n        stato_bobina: bobina.stato_bobina || 'DISPONIBILE',\n        ubicazione_bobina: bobina.ubicazione_bobina || '',\n        fornitore: bobina.fornitore || '',\n        n_DDT: bobina.n_DDT || '',\n        data_DDT: bobina.data_DDT || '',\n        configurazione: bobina.configurazione || ''\n      });\n    }\n  };\n\n  // Gestisce il cambio dei valori nel form\n  const handleFormChange = e => {\n    const {\n      name,\n      value\n    } = e.target;\n    setFormData({\n      ...formData,\n      [name]: value\n    });\n  };\n\n  // Gestisce il salvataggio del form\n  const handleSave = async () => {\n    try {\n      setLoading(true);\n      if (dialogType === 'creaBobina') {\n        await parcoCaviService.createBobina(cantiereId, formData);\n        onSuccess('Bobina creata con successo');\n      } else if (dialogType === 'modificaBobina') {\n        await parcoCaviService.updateBobina(cantiereId, formData.numero_bobina, formData);\n        onSuccess('Bobina modificata con successo');\n      } else if (dialogType === 'eliminaBobina') {\n        await parcoCaviService.deleteBobina(cantiereId, selectedBobina.numero_bobina);\n        onSuccess('Bobina eliminata con successo');\n      }\n      handleCloseDialog();\n      loadBobine(); // Ricarica le bobine dopo l'operazione\n    } catch (error) {\n      onError('Errore durante l\\'operazione: ' + (error.message || 'Errore sconosciuto'));\n      console.error('Errore durante l\\'operazione:', error);\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  // Renderizza le bobine in formato card\n  const renderBobineCards = () => {\n    if (bobine.length === 0) {\n      return /*#__PURE__*/_jsxDEV(Alert, {\n        severity: \"info\",\n        children: \"Nessuna bobina disponibile\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 224,\n        columnNumber: 9\n      }, this);\n    }\n    return /*#__PURE__*/_jsxDEV(Grid, {\n      container: true,\n      spacing: 2,\n      children: bobine.map(bobina => /*#__PURE__*/_jsxDEV(Grid, {\n        item: true,\n        xs: 12,\n        sm: 6,\n        md: 4,\n        children: /*#__PURE__*/_jsxDEV(Card, {\n          children: [/*#__PURE__*/_jsxDEV(CardContent, {\n            children: [/*#__PURE__*/_jsxDEV(Typography, {\n              variant: \"h6\",\n              component: \"div\",\n              children: [\"Bobina: \", bobina.numero_bobina]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 234,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(Typography, {\n              variant: \"body2\",\n              color: \"text.secondary\",\n              children: [\"Utility: \", bobina.utility || 'N/A']\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 237,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(Typography, {\n              variant: \"body2\",\n              color: \"text.secondary\",\n              children: [\"Tipologia: \", bobina.tipologia || 'N/A']\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 240,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(Typography, {\n              variant: \"body2\",\n              color: \"text.secondary\",\n              children: [\"N\\xB0 Conduttori: \", bobina.n_conduttori || 'N/A']\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 243,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(Typography, {\n              variant: \"body2\",\n              color: \"text.secondary\",\n              children: [\"Sezione: \", bobina.sezione || 'N/A']\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 246,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(Typography, {\n              variant: \"body2\",\n              color: \"text.secondary\",\n              children: [\"Metri totali: \", bobina.metri_totali || 'N/A', \" m\"]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 249,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(Typography, {\n              variant: \"body2\",\n              color: \"text.secondary\",\n              children: [\"Metri residui: \", bobina.metri_residui || 'N/A', \" m\"]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 252,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(Typography, {\n              variant: \"body2\",\n              color: \"text.secondary\",\n              children: [\"Stato: \", bobina.stato_bobina || 'DISPONIBILE']\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 255,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(Typography, {\n              variant: \"body2\",\n              color: \"text.secondary\",\n              children: [\"Ubicazione: \", bobina.ubicazione_bobina || 'N/A']\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 258,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(Typography, {\n              variant: \"body2\",\n              color: \"text.secondary\",\n              children: [\"Fornitore: \", bobina.fornitore || 'N/A']\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 261,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 233,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(CardActions, {\n            children: [/*#__PURE__*/_jsxDEV(Button, {\n              size: \"small\",\n              startIcon: /*#__PURE__*/_jsxDEV(EditIcon, {}, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 268,\n                columnNumber: 30\n              }, this),\n              onClick: () => {\n                setDialogType('selezionaBobina');\n                handleBobinaSelect(bobina);\n              },\n              children: \"Modifica\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 266,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(Button, {\n              size: \"small\",\n              color: \"error\",\n              startIcon: /*#__PURE__*/_jsxDEV(DeleteIcon, {}, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 279,\n                columnNumber: 30\n              }, this),\n              onClick: () => {\n                setDialogType('eliminaBobina');\n                setSelectedBobina(bobina);\n                setOpenDialog(true);\n              },\n              children: \"Elimina\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 276,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 265,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 232,\n          columnNumber: 13\n        }, this)\n      }, bobina.numero_bobina, false, {\n        fileName: _jsxFileName,\n        lineNumber: 231,\n        columnNumber: 11\n      }, this))\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 229,\n      columnNumber: 7\n    }, this);\n  };\n\n  // Renderizza il dialog in base al tipo\n  const renderDialog = () => {\n    if (dialogType === 'creaBobina' || dialogType === 'modificaBobina') {\n      return /*#__PURE__*/_jsxDEV(Dialog, {\n        open: openDialog,\n        onClose: handleCloseDialog,\n        maxWidth: \"md\",\n        fullWidth: true,\n        children: [/*#__PURE__*/_jsxDEV(DialogTitle, {\n          children: dialogType === 'creaBobina' ? 'Crea Nuova Bobina' : 'Modifica Bobina'\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 301,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(DialogContent, {\n          children: /*#__PURE__*/_jsxDEV(Grid, {\n            container: true,\n            spacing: 2,\n            sx: {\n              mt: 1\n            },\n            children: [/*#__PURE__*/_jsxDEV(Grid, {\n              item: true,\n              xs: 12,\n              sm: 6,\n              children: /*#__PURE__*/_jsxDEV(TextField, {\n                name: \"numero_bobina\",\n                label: \"Numero Bobina\",\n                fullWidth: true,\n                variant: \"outlined\",\n                value: formData.numero_bobina,\n                onChange: handleFormChange,\n                disabled: dialogType === 'modificaBobina',\n                required: true\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 307,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 306,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Grid, {\n              item: true,\n              xs: 12,\n              sm: 6,\n              children: /*#__PURE__*/_jsxDEV(TextField, {\n                name: \"tipo_cavo\",\n                label: \"Tipo Cavo\",\n                fullWidth: true,\n                variant: \"outlined\",\n                value: formData.tipo_cavo,\n                onChange: handleFormChange\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 319,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 318,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Grid, {\n              item: true,\n              xs: 12,\n              sm: 6,\n              children: /*#__PURE__*/_jsxDEV(TextField, {\n                name: \"costruttore\",\n                label: \"Costruttore\",\n                fullWidth: true,\n                variant: \"outlined\",\n                value: formData.costruttore,\n                onChange: handleFormChange\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 329,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 328,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Grid, {\n              item: true,\n              xs: 12,\n              sm: 6,\n              children: /*#__PURE__*/_jsxDEV(TextField, {\n                name: \"lotto\",\n                label: \"Lotto\",\n                fullWidth: true,\n                variant: \"outlined\",\n                value: formData.lotto,\n                onChange: handleFormChange\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 339,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 338,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Grid, {\n              item: true,\n              xs: 12,\n              sm: 6,\n              children: /*#__PURE__*/_jsxDEV(TextField, {\n                name: \"anno_costruzione\",\n                label: \"Anno Costruzione\",\n                fullWidth: true,\n                variant: \"outlined\",\n                value: formData.anno_costruzione,\n                onChange: handleFormChange\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 349,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 348,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Grid, {\n              item: true,\n              xs: 12,\n              sm: 6,\n              children: /*#__PURE__*/_jsxDEV(TextField, {\n                name: \"lunghezza_totale\",\n                label: \"Lunghezza Totale (m)\",\n                type: \"number\",\n                fullWidth: true,\n                variant: \"outlined\",\n                value: formData.lunghezza_totale,\n                onChange: handleFormChange\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 359,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 358,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Grid, {\n              item: true,\n              xs: 12,\n              sm: 6,\n              children: /*#__PURE__*/_jsxDEV(TextField, {\n                name: \"lunghezza_residua\",\n                label: \"Lunghezza Residua (m)\",\n                type: \"number\",\n                fullWidth: true,\n                variant: \"outlined\",\n                value: formData.lunghezza_residua,\n                onChange: handleFormChange\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 370,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 369,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Grid, {\n              item: true,\n              xs: 12,\n              children: /*#__PURE__*/_jsxDEV(TextField, {\n                name: \"note\",\n                label: \"Note\",\n                fullWidth: true,\n                multiline: true,\n                rows: 3,\n                variant: \"outlined\",\n                value: formData.note,\n                onChange: handleFormChange\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 381,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 380,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 305,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 304,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(DialogActions, {\n          children: [/*#__PURE__*/_jsxDEV(Button, {\n            onClick: handleCloseDialog,\n            children: \"Annulla\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 395,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Button, {\n            onClick: handleSave,\n            disabled: loading || dialogType === 'creaBobina' && !formData.numero_bobina,\n            startIcon: loading ? /*#__PURE__*/_jsxDEV(CircularProgress, {\n              size: 20\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 399,\n              columnNumber: 36\n            }, this) : /*#__PURE__*/_jsxDEV(SaveIcon, {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 399,\n              columnNumber: 69\n            }, this),\n            children: \"Salva\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 396,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 394,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 300,\n        columnNumber: 9\n      }, this);\n    } else if (dialogType === 'selezionaBobina') {\n      return /*#__PURE__*/_jsxDEV(Dialog, {\n        open: openDialog,\n        onClose: handleCloseDialog,\n        maxWidth: \"md\",\n        fullWidth: true,\n        children: [/*#__PURE__*/_jsxDEV(DialogTitle, {\n          children: \"Seleziona Bobina da Modificare\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 409,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(DialogContent, {\n          children: loading ? /*#__PURE__*/_jsxDEV(CircularProgress, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 412,\n            columnNumber: 15\n          }, this) : bobine.length === 0 ? /*#__PURE__*/_jsxDEV(Alert, {\n            severity: \"info\",\n            children: \"Nessuna bobina disponibile\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 414,\n            columnNumber: 15\n          }, this) : /*#__PURE__*/_jsxDEV(List, {\n            children: bobine.map(bobina => /*#__PURE__*/_jsxDEV(ListItem, {\n              button: true,\n              onClick: () => handleBobinaSelect(bobina),\n              children: /*#__PURE__*/_jsxDEV(ListItemText, {\n                primary: `Bobina: ${bobina.numero_bobina}`,\n                secondary: `Tipologia: ${bobina.tipologia || 'N/A'} - Utility: ${bobina.utility || 'N/A'} - Residuo: ${bobina.metri_residui || 'N/A'} m`\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 423,\n                columnNumber: 21\n              }, this)\n            }, bobina.numero_bobina, false, {\n              fileName: _jsxFileName,\n              lineNumber: 418,\n              columnNumber: 19\n            }, this))\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 416,\n            columnNumber: 15\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 410,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(DialogActions, {\n          children: /*#__PURE__*/_jsxDEV(Button, {\n            onClick: handleCloseDialog,\n            children: \"Annulla\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 433,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 432,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 408,\n        columnNumber: 9\n      }, this);\n    } else if (dialogType === 'eliminaBobina') {\n      return /*#__PURE__*/_jsxDEV(Dialog, {\n        open: openDialog,\n        onClose: handleCloseDialog,\n        maxWidth: \"sm\",\n        fullWidth: true,\n        children: [/*#__PURE__*/_jsxDEV(DialogTitle, {\n          children: \"Elimina Bobina\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 440,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(DialogContent, {\n          children: !selectedBobina ? loading ? /*#__PURE__*/_jsxDEV(CircularProgress, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 444,\n            columnNumber: 17\n          }, this) : bobine.length === 0 ? /*#__PURE__*/_jsxDEV(Alert, {\n            severity: \"info\",\n            children: \"Nessuna bobina disponibile\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 446,\n            columnNumber: 17\n          }, this) : /*#__PURE__*/_jsxDEV(List, {\n            children: bobine.map(bobina => /*#__PURE__*/_jsxDEV(ListItem, {\n              button: true,\n              onClick: () => setSelectedBobina(bobina),\n              children: /*#__PURE__*/_jsxDEV(ListItemText, {\n                primary: `Bobina: ${bobina.numero_bobina}`,\n                secondary: `Tipologia: ${bobina.tipologia || 'N/A'} - Utility: ${bobina.utility || 'N/A'} - Residuo: ${bobina.metri_residui || 'N/A'} m`\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 455,\n                columnNumber: 23\n              }, this)\n            }, bobina.numero_bobina, false, {\n              fileName: _jsxFileName,\n              lineNumber: 450,\n              columnNumber: 21\n            }, this))\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 448,\n            columnNumber: 17\n          }, this) : /*#__PURE__*/_jsxDEV(Box, {\n            children: [/*#__PURE__*/_jsxDEV(Alert, {\n              severity: \"warning\",\n              sx: {\n                mb: 2\n              },\n              children: [\"Sei sicuro di voler eliminare la bobina \", selectedBobina.numero_bobina, \"?\"]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 465,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(Typography, {\n              variant: \"body1\",\n              children: \"Questa operazione non pu\\xF2 essere annullata.\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 468,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 464,\n            columnNumber: 15\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 441,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(DialogActions, {\n          children: [/*#__PURE__*/_jsxDEV(Button, {\n            onClick: handleCloseDialog,\n            children: \"Annulla\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 475,\n            columnNumber: 13\n          }, this), selectedBobina && /*#__PURE__*/_jsxDEV(Button, {\n            onClick: handleSave,\n            disabled: loading,\n            color: \"error\",\n            startIcon: loading ? /*#__PURE__*/_jsxDEV(CircularProgress, {\n              size: 20\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 481,\n              columnNumber: 38\n            }, this) : /*#__PURE__*/_jsxDEV(DeleteIcon, {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 481,\n              columnNumber: 71\n            }, this),\n            children: \"Elimina\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 477,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 474,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 439,\n        columnNumber: 9\n      }, this);\n    } else if (dialogType === 'visualizzaStorico') {\n      return /*#__PURE__*/_jsxDEV(Dialog, {\n        open: openDialog,\n        onClose: handleCloseDialog,\n        maxWidth: \"lg\",\n        fullWidth: true,\n        children: [/*#__PURE__*/_jsxDEV(DialogTitle, {\n          children: \"Storico Utilizzo Bobine\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 492,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(DialogContent, {\n          children: loading ? /*#__PURE__*/_jsxDEV(CircularProgress, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 495,\n            columnNumber: 15\n          }, this) : storicoUtilizzo.length === 0 ? /*#__PURE__*/_jsxDEV(Alert, {\n            severity: \"info\",\n            children: \"Nessun dato storico disponibile\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 497,\n            columnNumber: 15\n          }, this) : /*#__PURE__*/_jsxDEV(TableContainer, {\n            component: Paper,\n            sx: {\n              mt: 2\n            },\n            children: /*#__PURE__*/_jsxDEV(Table, {\n              size: \"small\",\n              children: [/*#__PURE__*/_jsxDEV(TableHead, {\n                children: /*#__PURE__*/_jsxDEV(TableRow, {\n                  children: [/*#__PURE__*/_jsxDEV(TableCell, {\n                    children: \"Bobina\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 503,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n                    children: \"Cavo\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 504,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n                    children: \"Data Utilizzo\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 505,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n                    children: \"Metri Utilizzati\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 506,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n                    children: \"Operatore\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 507,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n                    children: \"Note\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 508,\n                    columnNumber: 23\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 502,\n                  columnNumber: 21\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 501,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(TableBody, {\n                children: storicoUtilizzo.map((record, index) => /*#__PURE__*/_jsxDEV(TableRow, {\n                  children: [/*#__PURE__*/_jsxDEV(TableCell, {\n                    children: record.numero_bobina\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 514,\n                    columnNumber: 25\n                  }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n                    children: record.id_cavo\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 515,\n                    columnNumber: 25\n                  }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n                    children: new Date(record.data_utilizzo).toLocaleDateString()\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 516,\n                    columnNumber: 25\n                  }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n                    children: record.metri_utilizzati\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 517,\n                    columnNumber: 25\n                  }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n                    children: record.operatore\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 518,\n                    columnNumber: 25\n                  }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n                    children: record.note || '-'\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 519,\n                    columnNumber: 25\n                  }, this)]\n                }, index, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 513,\n                  columnNumber: 23\n                }, this))\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 511,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 500,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 499,\n            columnNumber: 15\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 493,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(DialogActions, {\n          children: /*#__PURE__*/_jsxDEV(Button, {\n            onClick: handleCloseDialog,\n            children: \"Chiudi\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 528,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 527,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 491,\n        columnNumber: 9\n      }, this);\n    }\n    return null;\n  };\n  return /*#__PURE__*/_jsxDEV(Box, {\n    children: [selectedOption === 'visualizzaBobine' && !openDialog ? /*#__PURE__*/_jsxDEV(Paper, {\n      sx: {\n        p: 3\n      },\n      children: [/*#__PURE__*/_jsxDEV(Typography, {\n        variant: \"h6\",\n        gutterBottom: true,\n        children: \"Bobine Disponibili\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 541,\n        columnNumber: 11\n      }, this), loading ? /*#__PURE__*/_jsxDEV(Box, {\n        sx: {\n          display: 'flex',\n          justifyContent: 'center',\n          my: 4\n        },\n        children: /*#__PURE__*/_jsxDEV(CircularProgress, {}, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 546,\n          columnNumber: 15\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 545,\n        columnNumber: 13\n      }, this) : renderBobineCards()]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 540,\n      columnNumber: 9\n    }, this) : !openDialog ? /*#__PURE__*/_jsxDEV(Paper, {\n      sx: {\n        p: 3,\n        minHeight: '300px',\n        display: 'flex',\n        alignItems: 'center',\n        justifyContent: 'center'\n      },\n      children: !selectedOption ? /*#__PURE__*/_jsxDEV(Typography, {\n        variant: \"body1\",\n        children: \"Seleziona un'opzione dal menu principale per iniziare.\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 555,\n        columnNumber: 13\n      }, this) : /*#__PURE__*/_jsxDEV(Box, {\n        sx: {\n          textAlign: 'center'\n        },\n        children: [/*#__PURE__*/_jsxDEV(Typography, {\n          variant: \"h6\",\n          gutterBottom: true,\n          children: [selectedOption === 'creaBobina' && 'Crea Nuova Bobina', selectedOption === 'modificaBobina' && 'Modifica Bobina', selectedOption === 'eliminaBobina' && 'Elimina Bobina', selectedOption === 'visualizzaStorico' && 'Visualizza Storico Utilizzo']\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 560,\n          columnNumber: 15\n        }, this), /*#__PURE__*/_jsxDEV(CircularProgress, {\n          sx: {\n            mt: 2\n          }\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 566,\n          columnNumber: 15\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 559,\n        columnNumber: 13\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 553,\n      columnNumber: 9\n    }, this) : null, renderDialog()]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 538,\n    columnNumber: 5\n  }, this);\n};\n_s(ParcoCavi, \"U+BJYWml7La8JY8XMNQMy0mMXrE=\");\n_c = ParcoCavi;\nexport default ParcoCavi;\nvar _c;\n$RefreshReg$(_c, \"ParcoCavi\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "Box", "Typography", "<PERSON><PERSON>", "Paper", "Grid", "Card", "<PERSON><PERSON><PERSON><PERSON>", "CardActions", "Dialog", "DialogTitle", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "DialogActions", "TextField", "FormControl", "InputLabel", "Select", "MenuItem", "List", "ListItem", "ListItemText", "ListItemIcon", "ListItemButton", "Divider", "<PERSON><PERSON>", "CircularProgress", "Table", "TableBody", "TableCell", "TableContainer", "TableHead", "TableRow", "Add", "AddIcon", "Edit", "EditIcon", "Delete", "DeleteIcon", "History", "HistoryIcon", "Save", "SaveIcon", "ViewList", "ViewListIcon", "parcoCaviService", "jsxDEV", "_jsxDEV", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "cantiereId", "onSuccess", "onError", "_s", "loading", "setLoading", "bobine", "set<PERSON>ob<PERSON>", "selectedOption", "setSelectedOption", "openDialog", "setOpenDialog", "dialogType", "setDialogType", "<PERSON><PERSON><PERSON><PERSON>", "setSelectedBobina", "formData", "setFormData", "numero_bobina", "utility", "tipologia", "n_conduttori", "sezione", "metri_totali", "metri_residui", "stato_bobina", "ubicazione_bobina", "fornitore", "n_DDT", "data_DDT", "configurazione", "storico<PERSON><PERSON><PERSON><PERSON>", "setStoricoUtilizzo", "loadBobine", "data", "getBobine", "error", "console", "loadStoricoUtilizzo", "getStoricoUtilizzo", "handleOptionSelect", "option", "handleCloseDialog", "handleBobinaSelect", "bobina", "handleFormChange", "e", "name", "value", "target", "handleSave", "createBobina", "updateBobina", "deleteBobina", "message", "renderBobineCards", "length", "severity", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "container", "spacing", "map", "item", "xs", "sm", "md", "variant", "component", "color", "size", "startIcon", "onClick", "renderDialog", "open", "onClose", "max<PERSON><PERSON><PERSON>", "fullWidth", "sx", "mt", "label", "onChange", "disabled", "required", "tipo_cavo", "costru<PERSON><PERSON>", "lotto", "anno_costruzione", "type", "lunghezza_totale", "lunghezza_residua", "multiline", "rows", "note", "button", "primary", "secondary", "mb", "record", "index", "id_cavo", "Date", "data_utilizzo", "toLocaleDateString", "<PERSON><PERSON>_util<PERSON><PERSON><PERSON>", "operatore", "p", "gutterBottom", "display", "justifyContent", "my", "minHeight", "alignItems", "textAlign", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/PycharmProjects/CMS/webapp/frontend/src/components/cavi/ParcoCavi.js"], "sourcesContent": ["import React, { useState, useEffect } from 'react';\nimport {\n  Box,\n  Typography,\n  Button,\n  Paper,\n  Grid,\n  Card,\n  CardContent,\n  CardActions,\n  Dialog,\n  DialogTitle,\n  DialogContent,\n  DialogActions,\n  TextField,\n  FormControl,\n  InputLabel,\n  Select,\n  MenuItem,\n  List,\n  ListItem,\n  ListItemText,\n  ListItemIcon,\n  ListItemButton,\n  Divider,\n  Alert,\n  CircularProgress,\n  Table,\n  TableBody,\n  TableCell,\n  TableContainer,\n  TableHead,\n  TableRow\n} from '@mui/material';\nimport {\n  Add as AddIcon,\n  Edit as EditIcon,\n  Delete as DeleteIcon,\n  History as HistoryIcon,\n  Save as SaveIcon,\n  ViewList as ViewListIcon\n} from '@mui/icons-material';\nimport parcoCaviService from '../../services/parcoCaviService';\n\nconst ParcoCavi = ({ cantiereId, onSuccess, onError }) => {\n  const [loading, setLoading] = useState(false);\n  const [bobine, setBobine] = useState([]);\n  const [selectedOption, setSelectedOption] = useState(null);\n  const [openDialog, setOpenDialog] = useState(false);\n  const [dialogType, setDialogType] = useState('');\n  const [selectedBobina, setSelectedBobina] = useState(null);\n  const [formData, setFormData] = useState({\n    numero_bobina: '',\n    utility: '',\n    tipologia: '',\n    n_conduttori: '',\n    sezione: '',\n    metri_totali: '',\n    metri_residui: '',\n    stato_bobina: 'DISPONIBILE',\n    ubicazione_bobina: '',\n    fornitore: '',\n    n_DDT: '',\n    data_DDT: '',\n    configurazione: ''\n  });\n  const [storicoUtilizzo, setStoricoUtilizzo] = useState([]);\n\n  // Carica le bobine disponibili\n  const loadBobine = async () => {\n    try {\n      setLoading(true);\n      const data = await parcoCaviService.getBobine(cantiereId);\n      setBobine(data);\n    } catch (error) {\n      onError('Errore nel caricamento delle bobine');\n      console.error('Errore nel caricamento delle bobine:', error);\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  // Carica lo storico utilizzo bobine\n  const loadStoricoUtilizzo = async () => {\n    try {\n      setLoading(true);\n      const data = await parcoCaviService.getStoricoUtilizzo(cantiereId);\n      setStoricoUtilizzo(data);\n    } catch (error) {\n      onError('Errore nel caricamento dello storico utilizzo');\n      console.error('Errore nel caricamento dello storico utilizzo:', error);\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  // Carica i dati all'avvio del componente\n  useEffect(() => {\n    loadBobine();\n  }, [cantiereId]);\n\n  // Gestisce la selezione di un'opzione dal menu\n  const handleOptionSelect = (option) => {\n    setSelectedOption(option);\n\n    if (option === 'visualizzaBobine') {\n      loadBobine();\n    } else if (option === 'creaBobina') {\n      setDialogType('creaBobina');\n      setFormData({\n        numero_bobina: '',\n        utility: '',\n        tipologia: '',\n        n_conduttori: '',\n        sezione: '',\n        metri_totali: '',\n        metri_residui: '',\n        stato_bobina: 'DISPONIBILE',\n        ubicazione_bobina: '',\n        fornitore: '',\n        n_DDT: '',\n        data_DDT: '',\n        configurazione: ''\n      });\n      setOpenDialog(true);\n    } else if (option === 'modificaBobina') {\n      loadBobine();\n      setDialogType('selezionaBobina');\n      setOpenDialog(true);\n    } else if (option === 'eliminaBobina') {\n      loadBobine();\n      setDialogType('eliminaBobina');\n      setOpenDialog(true);\n    } else if (option === 'visualizzaStorico') {\n      loadStoricoUtilizzo();\n      setDialogType('visualizzaStorico');\n      setOpenDialog(true);\n    }\n  };\n\n  // Gestisce la chiusura del dialog\n  const handleCloseDialog = () => {\n    setOpenDialog(false);\n    setSelectedBobina(null);\n    setFormData({\n      numero_bobina: '',\n      utility: '',\n      tipologia: '',\n      n_conduttori: '',\n      sezione: '',\n      metri_totali: '',\n      metri_residui: '',\n      stato_bobina: 'DISPONIBILE',\n      ubicazione_bobina: '',\n      fornitore: '',\n      n_DDT: '',\n      data_DDT: '',\n      configurazione: ''\n    });\n  };\n\n  // Gestisce la selezione di una bobina\n  const handleBobinaSelect = (bobina) => {\n    setSelectedBobina(bobina);\n    if (dialogType === 'selezionaBobina') {\n      setDialogType('modificaBobina');\n      setFormData({\n        numero_bobina: bobina.numero_bobina,\n        utility: bobina.utility || '',\n        tipologia: bobina.tipologia || '',\n        n_conduttori: bobina.n_conduttori || '',\n        sezione: bobina.sezione || '',\n        metri_totali: bobina.metri_totali || '',\n        metri_residui: bobina.metri_residui || '',\n        stato_bobina: bobina.stato_bobina || 'DISPONIBILE',\n        ubicazione_bobina: bobina.ubicazione_bobina || '',\n        fornitore: bobina.fornitore || '',\n        n_DDT: bobina.n_DDT || '',\n        data_DDT: bobina.data_DDT || '',\n        configurazione: bobina.configurazione || ''\n      });\n    }\n  };\n\n  // Gestisce il cambio dei valori nel form\n  const handleFormChange = (e) => {\n    const { name, value } = e.target;\n    setFormData({\n      ...formData,\n      [name]: value\n    });\n  };\n\n  // Gestisce il salvataggio del form\n  const handleSave = async () => {\n    try {\n      setLoading(true);\n\n      if (dialogType === 'creaBobina') {\n        await parcoCaviService.createBobina(cantiereId, formData);\n        onSuccess('Bobina creata con successo');\n      } else if (dialogType === 'modificaBobina') {\n        await parcoCaviService.updateBobina(cantiereId, formData.numero_bobina, formData);\n        onSuccess('Bobina modificata con successo');\n      } else if (dialogType === 'eliminaBobina') {\n        await parcoCaviService.deleteBobina(cantiereId, selectedBobina.numero_bobina);\n        onSuccess('Bobina eliminata con successo');\n      }\n\n      handleCloseDialog();\n      loadBobine(); // Ricarica le bobine dopo l'operazione\n    } catch (error) {\n      onError('Errore durante l\\'operazione: ' + (error.message || 'Errore sconosciuto'));\n      console.error('Errore durante l\\'operazione:', error);\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  // Renderizza le bobine in formato card\n  const renderBobineCards = () => {\n    if (bobine.length === 0) {\n      return (\n        <Alert severity=\"info\">Nessuna bobina disponibile</Alert>\n      );\n    }\n\n    return (\n      <Grid container spacing={2}>\n        {bobine.map((bobina) => (\n          <Grid item xs={12} sm={6} md={4} key={bobina.numero_bobina}>\n            <Card>\n              <CardContent>\n                <Typography variant=\"h6\" component=\"div\">\n                  Bobina: {bobina.numero_bobina}\n                </Typography>\n                <Typography variant=\"body2\" color=\"text.secondary\">\n                  Utility: {bobina.utility || 'N/A'}\n                </Typography>\n                <Typography variant=\"body2\" color=\"text.secondary\">\n                  Tipologia: {bobina.tipologia || 'N/A'}\n                </Typography>\n                <Typography variant=\"body2\" color=\"text.secondary\">\n                  N° Conduttori: {bobina.n_conduttori || 'N/A'}\n                </Typography>\n                <Typography variant=\"body2\" color=\"text.secondary\">\n                  Sezione: {bobina.sezione || 'N/A'}\n                </Typography>\n                <Typography variant=\"body2\" color=\"text.secondary\">\n                  Metri totali: {bobina.metri_totali || 'N/A'} m\n                </Typography>\n                <Typography variant=\"body2\" color=\"text.secondary\">\n                  Metri residui: {bobina.metri_residui || 'N/A'} m\n                </Typography>\n                <Typography variant=\"body2\" color=\"text.secondary\">\n                  Stato: {bobina.stato_bobina || 'DISPONIBILE'}\n                </Typography>\n                <Typography variant=\"body2\" color=\"text.secondary\">\n                  Ubicazione: {bobina.ubicazione_bobina || 'N/A'}\n                </Typography>\n                <Typography variant=\"body2\" color=\"text.secondary\">\n                  Fornitore: {bobina.fornitore || 'N/A'}\n                </Typography>\n              </CardContent>\n              <CardActions>\n                <Button\n                  size=\"small\"\n                  startIcon={<EditIcon />}\n                  onClick={() => {\n                    setDialogType('selezionaBobina');\n                    handleBobinaSelect(bobina);\n                  }}\n                >\n                  Modifica\n                </Button>\n                <Button\n                  size=\"small\"\n                  color=\"error\"\n                  startIcon={<DeleteIcon />}\n                  onClick={() => {\n                    setDialogType('eliminaBobina');\n                    setSelectedBobina(bobina);\n                    setOpenDialog(true);\n                  }}\n                >\n                  Elimina\n                </Button>\n              </CardActions>\n            </Card>\n          </Grid>\n        ))}\n      </Grid>\n    );\n  };\n\n  // Renderizza il dialog in base al tipo\n  const renderDialog = () => {\n    if (dialogType === 'creaBobina' || dialogType === 'modificaBobina') {\n      return (\n        <Dialog open={openDialog} onClose={handleCloseDialog} maxWidth=\"md\" fullWidth>\n          <DialogTitle>\n            {dialogType === 'creaBobina' ? 'Crea Nuova Bobina' : 'Modifica Bobina'}\n          </DialogTitle>\n          <DialogContent>\n            <Grid container spacing={2} sx={{ mt: 1 }}>\n              <Grid item xs={12} sm={6}>\n                <TextField\n                  name=\"numero_bobina\"\n                  label=\"Numero Bobina\"\n                  fullWidth\n                  variant=\"outlined\"\n                  value={formData.numero_bobina}\n                  onChange={handleFormChange}\n                  disabled={dialogType === 'modificaBobina'}\n                  required\n                />\n              </Grid>\n              <Grid item xs={12} sm={6}>\n                <TextField\n                  name=\"tipo_cavo\"\n                  label=\"Tipo Cavo\"\n                  fullWidth\n                  variant=\"outlined\"\n                  value={formData.tipo_cavo}\n                  onChange={handleFormChange}\n                />\n              </Grid>\n              <Grid item xs={12} sm={6}>\n                <TextField\n                  name=\"costruttore\"\n                  label=\"Costruttore\"\n                  fullWidth\n                  variant=\"outlined\"\n                  value={formData.costruttore}\n                  onChange={handleFormChange}\n                />\n              </Grid>\n              <Grid item xs={12} sm={6}>\n                <TextField\n                  name=\"lotto\"\n                  label=\"Lotto\"\n                  fullWidth\n                  variant=\"outlined\"\n                  value={formData.lotto}\n                  onChange={handleFormChange}\n                />\n              </Grid>\n              <Grid item xs={12} sm={6}>\n                <TextField\n                  name=\"anno_costruzione\"\n                  label=\"Anno Costruzione\"\n                  fullWidth\n                  variant=\"outlined\"\n                  value={formData.anno_costruzione}\n                  onChange={handleFormChange}\n                />\n              </Grid>\n              <Grid item xs={12} sm={6}>\n                <TextField\n                  name=\"lunghezza_totale\"\n                  label=\"Lunghezza Totale (m)\"\n                  type=\"number\"\n                  fullWidth\n                  variant=\"outlined\"\n                  value={formData.lunghezza_totale}\n                  onChange={handleFormChange}\n                />\n              </Grid>\n              <Grid item xs={12} sm={6}>\n                <TextField\n                  name=\"lunghezza_residua\"\n                  label=\"Lunghezza Residua (m)\"\n                  type=\"number\"\n                  fullWidth\n                  variant=\"outlined\"\n                  value={formData.lunghezza_residua}\n                  onChange={handleFormChange}\n                />\n              </Grid>\n              <Grid item xs={12}>\n                <TextField\n                  name=\"note\"\n                  label=\"Note\"\n                  fullWidth\n                  multiline\n                  rows={3}\n                  variant=\"outlined\"\n                  value={formData.note}\n                  onChange={handleFormChange}\n                />\n              </Grid>\n            </Grid>\n          </DialogContent>\n          <DialogActions>\n            <Button onClick={handleCloseDialog}>Annulla</Button>\n            <Button\n              onClick={handleSave}\n              disabled={loading || (dialogType === 'creaBobina' && !formData.numero_bobina)}\n              startIcon={loading ? <CircularProgress size={20} /> : <SaveIcon />}\n            >\n              Salva\n            </Button>\n          </DialogActions>\n        </Dialog>\n      );\n    } else if (dialogType === 'selezionaBobina') {\n      return (\n        <Dialog open={openDialog} onClose={handleCloseDialog} maxWidth=\"md\" fullWidth>\n          <DialogTitle>Seleziona Bobina da Modificare</DialogTitle>\n          <DialogContent>\n            {loading ? (\n              <CircularProgress />\n            ) : bobine.length === 0 ? (\n              <Alert severity=\"info\">Nessuna bobina disponibile</Alert>\n            ) : (\n              <List>\n                {bobine.map((bobina) => (\n                  <ListItem\n                    button\n                    key={bobina.numero_bobina}\n                    onClick={() => handleBobinaSelect(bobina)}\n                  >\n                    <ListItemText\n                      primary={`Bobina: ${bobina.numero_bobina}`}\n                      secondary={`Tipologia: ${bobina.tipologia || 'N/A'} - Utility: ${bobina.utility || 'N/A'} - Residuo: ${bobina.metri_residui || 'N/A'} m`}\n                    />\n                  </ListItem>\n                ))}\n              </List>\n            )}\n          </DialogContent>\n          <DialogActions>\n            <Button onClick={handleCloseDialog}>Annulla</Button>\n          </DialogActions>\n        </Dialog>\n      );\n    } else if (dialogType === 'eliminaBobina') {\n      return (\n        <Dialog open={openDialog} onClose={handleCloseDialog} maxWidth=\"sm\" fullWidth>\n          <DialogTitle>Elimina Bobina</DialogTitle>\n          <DialogContent>\n            {!selectedBobina ? (\n              loading ? (\n                <CircularProgress />\n              ) : bobine.length === 0 ? (\n                <Alert severity=\"info\">Nessuna bobina disponibile</Alert>\n              ) : (\n                <List>\n                  {bobine.map((bobina) => (\n                    <ListItem\n                      button\n                      key={bobina.numero_bobina}\n                      onClick={() => setSelectedBobina(bobina)}\n                    >\n                      <ListItemText\n                        primary={`Bobina: ${bobina.numero_bobina}`}\n                        secondary={`Tipologia: ${bobina.tipologia || 'N/A'} - Utility: ${bobina.utility || 'N/A'} - Residuo: ${bobina.metri_residui || 'N/A'} m`}\n                      />\n                    </ListItem>\n                  ))}\n                </List>\n              )\n            ) : (\n              <Box>\n                <Alert severity=\"warning\" sx={{ mb: 2 }}>\n                  Sei sicuro di voler eliminare la bobina {selectedBobina.numero_bobina}?\n                </Alert>\n                <Typography variant=\"body1\">\n                  Questa operazione non può essere annullata.\n                </Typography>\n              </Box>\n            )}\n          </DialogContent>\n          <DialogActions>\n            <Button onClick={handleCloseDialog}>Annulla</Button>\n            {selectedBobina && (\n              <Button\n                onClick={handleSave}\n                disabled={loading}\n                color=\"error\"\n                startIcon={loading ? <CircularProgress size={20} /> : <DeleteIcon />}\n              >\n                Elimina\n              </Button>\n            )}\n          </DialogActions>\n        </Dialog>\n      );\n    } else if (dialogType === 'visualizzaStorico') {\n      return (\n        <Dialog open={openDialog} onClose={handleCloseDialog} maxWidth=\"lg\" fullWidth>\n          <DialogTitle>Storico Utilizzo Bobine</DialogTitle>\n          <DialogContent>\n            {loading ? (\n              <CircularProgress />\n            ) : storicoUtilizzo.length === 0 ? (\n              <Alert severity=\"info\">Nessun dato storico disponibile</Alert>\n            ) : (\n              <TableContainer component={Paper} sx={{ mt: 2 }}>\n                <Table size=\"small\">\n                  <TableHead>\n                    <TableRow>\n                      <TableCell>Bobina</TableCell>\n                      <TableCell>Cavo</TableCell>\n                      <TableCell>Data Utilizzo</TableCell>\n                      <TableCell>Metri Utilizzati</TableCell>\n                      <TableCell>Operatore</TableCell>\n                      <TableCell>Note</TableCell>\n                    </TableRow>\n                  </TableHead>\n                  <TableBody>\n                    {storicoUtilizzo.map((record, index) => (\n                      <TableRow key={index}>\n                        <TableCell>{record.numero_bobina}</TableCell>\n                        <TableCell>{record.id_cavo}</TableCell>\n                        <TableCell>{new Date(record.data_utilizzo).toLocaleDateString()}</TableCell>\n                        <TableCell>{record.metri_utilizzati}</TableCell>\n                        <TableCell>{record.operatore}</TableCell>\n                        <TableCell>{record.note || '-'}</TableCell>\n                      </TableRow>\n                    ))}\n                  </TableBody>\n                </Table>\n              </TableContainer>\n            )}\n          </DialogContent>\n          <DialogActions>\n            <Button onClick={handleCloseDialog}>Chiudi</Button>\n          </DialogActions>\n        </Dialog>\n      );\n    }\n\n    return null;\n  };\n\n  return (\n    <Box>\n      {selectedOption === 'visualizzaBobine' && !openDialog ? (\n        <Paper sx={{ p: 3 }}>\n          <Typography variant=\"h6\" gutterBottom>\n            Bobine Disponibili\n          </Typography>\n          {loading ? (\n            <Box sx={{ display: 'flex', justifyContent: 'center', my: 4 }}>\n              <CircularProgress />\n            </Box>\n          ) : (\n            renderBobineCards()\n          )}\n        </Paper>\n      ) : !openDialog ? (\n        <Paper sx={{ p: 3, minHeight: '300px', display: 'flex', alignItems: 'center', justifyContent: 'center' }}>\n          {!selectedOption ? (\n            <Typography variant=\"body1\">\n              Seleziona un'opzione dal menu principale per iniziare.\n            </Typography>\n          ) : (\n            <Box sx={{ textAlign: 'center' }}>\n              <Typography variant=\"h6\" gutterBottom>\n                {selectedOption === 'creaBobina' && 'Crea Nuova Bobina'}\n                {selectedOption === 'modificaBobina' && 'Modifica Bobina'}\n                {selectedOption === 'eliminaBobina' && 'Elimina Bobina'}\n                {selectedOption === 'visualizzaStorico' && 'Visualizza Storico Utilizzo'}\n              </Typography>\n              <CircularProgress sx={{ mt: 2 }} />\n            </Box>\n          )}\n        </Paper>\n      ) : null}\n\n      {renderDialog()}\n    </Box>\n  );\n};\n\nexport default ParcoCavi;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAClD,SACEC,GAAG,EACHC,UAAU,EACVC,MAAM,EACNC,KAAK,EACLC,IAAI,EACJC,IAAI,EACJC,WAAW,EACXC,WAAW,EACXC,MAAM,EACNC,WAAW,EACXC,aAAa,EACbC,aAAa,EACbC,SAAS,EACTC,WAAW,EACXC,UAAU,EACVC,MAAM,EACNC,QAAQ,EACRC,IAAI,EACJC,QAAQ,EACRC,YAAY,EACZC,YAAY,EACZC,cAAc,EACdC,OAAO,EACPC,KAAK,EACLC,gBAAgB,EAChBC,KAAK,EACLC,SAAS,EACTC,SAAS,EACTC,cAAc,EACdC,SAAS,EACTC,QAAQ,QACH,eAAe;AACtB,SACEC,GAAG,IAAIC,OAAO,EACdC,IAAI,IAAIC,QAAQ,EAChBC,MAAM,IAAIC,UAAU,EACpBC,OAAO,IAAIC,WAAW,EACtBC,IAAI,IAAIC,QAAQ,EAChBC,QAAQ,IAAIC,YAAY,QACnB,qBAAqB;AAC5B,OAAOC,gBAAgB,MAAM,iCAAiC;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAE/D,MAAMC,SAAS,GAAGA,CAAC;EAAEC,UAAU;EAAEC,SAAS;EAAEC;AAAQ,CAAC,KAAK;EAAAC,EAAA;EACxD,MAAM,CAACC,OAAO,EAAEC,UAAU,CAAC,GAAGtD,QAAQ,CAAC,KAAK,CAAC;EAC7C,MAAM,CAACuD,MAAM,EAAEC,SAAS,CAAC,GAAGxD,QAAQ,CAAC,EAAE,CAAC;EACxC,MAAM,CAACyD,cAAc,EAAEC,iBAAiB,CAAC,GAAG1D,QAAQ,CAAC,IAAI,CAAC;EAC1D,MAAM,CAAC2D,UAAU,EAAEC,aAAa,CAAC,GAAG5D,QAAQ,CAAC,KAAK,CAAC;EACnD,MAAM,CAAC6D,UAAU,EAAEC,aAAa,CAAC,GAAG9D,QAAQ,CAAC,EAAE,CAAC;EAChD,MAAM,CAAC+D,cAAc,EAAEC,iBAAiB,CAAC,GAAGhE,QAAQ,CAAC,IAAI,CAAC;EAC1D,MAAM,CAACiE,QAAQ,EAAEC,WAAW,CAAC,GAAGlE,QAAQ,CAAC;IACvCmE,aAAa,EAAE,EAAE;IACjBC,OAAO,EAAE,EAAE;IACXC,SAAS,EAAE,EAAE;IACbC,YAAY,EAAE,EAAE;IAChBC,OAAO,EAAE,EAAE;IACXC,YAAY,EAAE,EAAE;IAChBC,aAAa,EAAE,EAAE;IACjBC,YAAY,EAAE,aAAa;IAC3BC,iBAAiB,EAAE,EAAE;IACrBC,SAAS,EAAE,EAAE;IACbC,KAAK,EAAE,EAAE;IACTC,QAAQ,EAAE,EAAE;IACZC,cAAc,EAAE;EAClB,CAAC,CAAC;EACF,MAAM,CAACC,eAAe,EAAEC,kBAAkB,CAAC,GAAGjF,QAAQ,CAAC,EAAE,CAAC;;EAE1D;EACA,MAAMkF,UAAU,GAAG,MAAAA,CAAA,KAAY;IAC7B,IAAI;MACF5B,UAAU,CAAC,IAAI,CAAC;MAChB,MAAM6B,IAAI,GAAG,MAAMtC,gBAAgB,CAACuC,SAAS,CAACnC,UAAU,CAAC;MACzDO,SAAS,CAAC2B,IAAI,CAAC;IACjB,CAAC,CAAC,OAAOE,KAAK,EAAE;MACdlC,OAAO,CAAC,qCAAqC,CAAC;MAC9CmC,OAAO,CAACD,KAAK,CAAC,sCAAsC,EAAEA,KAAK,CAAC;IAC9D,CAAC,SAAS;MACR/B,UAAU,CAAC,KAAK,CAAC;IACnB;EACF,CAAC;;EAED;EACA,MAAMiC,mBAAmB,GAAG,MAAAA,CAAA,KAAY;IACtC,IAAI;MACFjC,UAAU,CAAC,IAAI,CAAC;MAChB,MAAM6B,IAAI,GAAG,MAAMtC,gBAAgB,CAAC2C,kBAAkB,CAACvC,UAAU,CAAC;MAClEgC,kBAAkB,CAACE,IAAI,CAAC;IAC1B,CAAC,CAAC,OAAOE,KAAK,EAAE;MACdlC,OAAO,CAAC,+CAA+C,CAAC;MACxDmC,OAAO,CAACD,KAAK,CAAC,gDAAgD,EAAEA,KAAK,CAAC;IACxE,CAAC,SAAS;MACR/B,UAAU,CAAC,KAAK,CAAC;IACnB;EACF,CAAC;;EAED;EACArD,SAAS,CAAC,MAAM;IACdiF,UAAU,CAAC,CAAC;EACd,CAAC,EAAE,CAACjC,UAAU,CAAC,CAAC;;EAEhB;EACA,MAAMwC,kBAAkB,GAAIC,MAAM,IAAK;IACrChC,iBAAiB,CAACgC,MAAM,CAAC;IAEzB,IAAIA,MAAM,KAAK,kBAAkB,EAAE;MACjCR,UAAU,CAAC,CAAC;IACd,CAAC,MAAM,IAAIQ,MAAM,KAAK,YAAY,EAAE;MAClC5B,aAAa,CAAC,YAAY,CAAC;MAC3BI,WAAW,CAAC;QACVC,aAAa,EAAE,EAAE;QACjBC,OAAO,EAAE,EAAE;QACXC,SAAS,EAAE,EAAE;QACbC,YAAY,EAAE,EAAE;QAChBC,OAAO,EAAE,EAAE;QACXC,YAAY,EAAE,EAAE;QAChBC,aAAa,EAAE,EAAE;QACjBC,YAAY,EAAE,aAAa;QAC3BC,iBAAiB,EAAE,EAAE;QACrBC,SAAS,EAAE,EAAE;QACbC,KAAK,EAAE,EAAE;QACTC,QAAQ,EAAE,EAAE;QACZC,cAAc,EAAE;MAClB,CAAC,CAAC;MACFnB,aAAa,CAAC,IAAI,CAAC;IACrB,CAAC,MAAM,IAAI8B,MAAM,KAAK,gBAAgB,EAAE;MACtCR,UAAU,CAAC,CAAC;MACZpB,aAAa,CAAC,iBAAiB,CAAC;MAChCF,aAAa,CAAC,IAAI,CAAC;IACrB,CAAC,MAAM,IAAI8B,MAAM,KAAK,eAAe,EAAE;MACrCR,UAAU,CAAC,CAAC;MACZpB,aAAa,CAAC,eAAe,CAAC;MAC9BF,aAAa,CAAC,IAAI,CAAC;IACrB,CAAC,MAAM,IAAI8B,MAAM,KAAK,mBAAmB,EAAE;MACzCH,mBAAmB,CAAC,CAAC;MACrBzB,aAAa,CAAC,mBAAmB,CAAC;MAClCF,aAAa,CAAC,IAAI,CAAC;IACrB;EACF,CAAC;;EAED;EACA,MAAM+B,iBAAiB,GAAGA,CAAA,KAAM;IAC9B/B,aAAa,CAAC,KAAK,CAAC;IACpBI,iBAAiB,CAAC,IAAI,CAAC;IACvBE,WAAW,CAAC;MACVC,aAAa,EAAE,EAAE;MACjBC,OAAO,EAAE,EAAE;MACXC,SAAS,EAAE,EAAE;MACbC,YAAY,EAAE,EAAE;MAChBC,OAAO,EAAE,EAAE;MACXC,YAAY,EAAE,EAAE;MAChBC,aAAa,EAAE,EAAE;MACjBC,YAAY,EAAE,aAAa;MAC3BC,iBAAiB,EAAE,EAAE;MACrBC,SAAS,EAAE,EAAE;MACbC,KAAK,EAAE,EAAE;MACTC,QAAQ,EAAE,EAAE;MACZC,cAAc,EAAE;IAClB,CAAC,CAAC;EACJ,CAAC;;EAED;EACA,MAAMa,kBAAkB,GAAIC,MAAM,IAAK;IACrC7B,iBAAiB,CAAC6B,MAAM,CAAC;IACzB,IAAIhC,UAAU,KAAK,iBAAiB,EAAE;MACpCC,aAAa,CAAC,gBAAgB,CAAC;MAC/BI,WAAW,CAAC;QACVC,aAAa,EAAE0B,MAAM,CAAC1B,aAAa;QACnCC,OAAO,EAAEyB,MAAM,CAACzB,OAAO,IAAI,EAAE;QAC7BC,SAAS,EAAEwB,MAAM,CAACxB,SAAS,IAAI,EAAE;QACjCC,YAAY,EAAEuB,MAAM,CAACvB,YAAY,IAAI,EAAE;QACvCC,OAAO,EAAEsB,MAAM,CAACtB,OAAO,IAAI,EAAE;QAC7BC,YAAY,EAAEqB,MAAM,CAACrB,YAAY,IAAI,EAAE;QACvCC,aAAa,EAAEoB,MAAM,CAACpB,aAAa,IAAI,EAAE;QACzCC,YAAY,EAAEmB,MAAM,CAACnB,YAAY,IAAI,aAAa;QAClDC,iBAAiB,EAAEkB,MAAM,CAAClB,iBAAiB,IAAI,EAAE;QACjDC,SAAS,EAAEiB,MAAM,CAACjB,SAAS,IAAI,EAAE;QACjCC,KAAK,EAAEgB,MAAM,CAAChB,KAAK,IAAI,EAAE;QACzBC,QAAQ,EAAEe,MAAM,CAACf,QAAQ,IAAI,EAAE;QAC/BC,cAAc,EAAEc,MAAM,CAACd,cAAc,IAAI;MAC3C,CAAC,CAAC;IACJ;EACF,CAAC;;EAED;EACA,MAAMe,gBAAgB,GAAIC,CAAC,IAAK;IAC9B,MAAM;MAAEC,IAAI;MAAEC;IAAM,CAAC,GAAGF,CAAC,CAACG,MAAM;IAChChC,WAAW,CAAC;MACV,GAAGD,QAAQ;MACX,CAAC+B,IAAI,GAAGC;IACV,CAAC,CAAC;EACJ,CAAC;;EAED;EACA,MAAME,UAAU,GAAG,MAAAA,CAAA,KAAY;IAC7B,IAAI;MACF7C,UAAU,CAAC,IAAI,CAAC;MAEhB,IAAIO,UAAU,KAAK,YAAY,EAAE;QAC/B,MAAMhB,gBAAgB,CAACuD,YAAY,CAACnD,UAAU,EAAEgB,QAAQ,CAAC;QACzDf,SAAS,CAAC,4BAA4B,CAAC;MACzC,CAAC,MAAM,IAAIW,UAAU,KAAK,gBAAgB,EAAE;QAC1C,MAAMhB,gBAAgB,CAACwD,YAAY,CAACpD,UAAU,EAAEgB,QAAQ,CAACE,aAAa,EAAEF,QAAQ,CAAC;QACjFf,SAAS,CAAC,gCAAgC,CAAC;MAC7C,CAAC,MAAM,IAAIW,UAAU,KAAK,eAAe,EAAE;QACzC,MAAMhB,gBAAgB,CAACyD,YAAY,CAACrD,UAAU,EAAEc,cAAc,CAACI,aAAa,CAAC;QAC7EjB,SAAS,CAAC,+BAA+B,CAAC;MAC5C;MAEAyC,iBAAiB,CAAC,CAAC;MACnBT,UAAU,CAAC,CAAC,CAAC,CAAC;IAChB,CAAC,CAAC,OAAOG,KAAK,EAAE;MACdlC,OAAO,CAAC,gCAAgC,IAAIkC,KAAK,CAACkB,OAAO,IAAI,oBAAoB,CAAC,CAAC;MACnFjB,OAAO,CAACD,KAAK,CAAC,+BAA+B,EAAEA,KAAK,CAAC;IACvD,CAAC,SAAS;MACR/B,UAAU,CAAC,KAAK,CAAC;IACnB;EACF,CAAC;;EAED;EACA,MAAMkD,iBAAiB,GAAGA,CAAA,KAAM;IAC9B,IAAIjD,MAAM,CAACkD,MAAM,KAAK,CAAC,EAAE;MACvB,oBACE1D,OAAA,CAACtB,KAAK;QAACiF,QAAQ,EAAC,MAAM;QAAAC,QAAA,EAAC;MAA0B;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAO,CAAC;IAE7D;IAEA,oBACEhE,OAAA,CAACzC,IAAI;MAAC0G,SAAS;MAACC,OAAO,EAAE,CAAE;MAAAN,QAAA,EACxBpD,MAAM,CAAC2D,GAAG,CAAErB,MAAM,iBACjB9C,OAAA,CAACzC,IAAI;QAAC6G,IAAI;QAACC,EAAE,EAAE,EAAG;QAACC,EAAE,EAAE,CAAE;QAACC,EAAE,EAAE,CAAE;QAAAX,QAAA,eAC9B5D,OAAA,CAACxC,IAAI;UAAAoG,QAAA,gBACH5D,OAAA,CAACvC,WAAW;YAAAmG,QAAA,gBACV5D,OAAA,CAAC5C,UAAU;cAACoH,OAAO,EAAC,IAAI;cAACC,SAAS,EAAC,KAAK;cAAAb,QAAA,GAAC,UAC/B,EAACd,MAAM,CAAC1B,aAAa;YAAA;cAAAyC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACnB,CAAC,eACbhE,OAAA,CAAC5C,UAAU;cAACoH,OAAO,EAAC,OAAO;cAACE,KAAK,EAAC,gBAAgB;cAAAd,QAAA,GAAC,WACxC,EAACd,MAAM,CAACzB,OAAO,IAAI,KAAK;YAAA;cAAAwC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACvB,CAAC,eACbhE,OAAA,CAAC5C,UAAU;cAACoH,OAAO,EAAC,OAAO;cAACE,KAAK,EAAC,gBAAgB;cAAAd,QAAA,GAAC,aACtC,EAACd,MAAM,CAACxB,SAAS,IAAI,KAAK;YAAA;cAAAuC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC3B,CAAC,eACbhE,OAAA,CAAC5C,UAAU;cAACoH,OAAO,EAAC,OAAO;cAACE,KAAK,EAAC,gBAAgB;cAAAd,QAAA,GAAC,oBAClC,EAACd,MAAM,CAACvB,YAAY,IAAI,KAAK;YAAA;cAAAsC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAClC,CAAC,eACbhE,OAAA,CAAC5C,UAAU;cAACoH,OAAO,EAAC,OAAO;cAACE,KAAK,EAAC,gBAAgB;cAAAd,QAAA,GAAC,WACxC,EAACd,MAAM,CAACtB,OAAO,IAAI,KAAK;YAAA;cAAAqC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACvB,CAAC,eACbhE,OAAA,CAAC5C,UAAU;cAACoH,OAAO,EAAC,OAAO;cAACE,KAAK,EAAC,gBAAgB;cAAAd,QAAA,GAAC,gBACnC,EAACd,MAAM,CAACrB,YAAY,IAAI,KAAK,EAAC,IAC9C;YAAA;cAAAoC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAY,CAAC,eACbhE,OAAA,CAAC5C,UAAU;cAACoH,OAAO,EAAC,OAAO;cAACE,KAAK,EAAC,gBAAgB;cAAAd,QAAA,GAAC,iBAClC,EAACd,MAAM,CAACpB,aAAa,IAAI,KAAK,EAAC,IAChD;YAAA;cAAAmC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAY,CAAC,eACbhE,OAAA,CAAC5C,UAAU;cAACoH,OAAO,EAAC,OAAO;cAACE,KAAK,EAAC,gBAAgB;cAAAd,QAAA,GAAC,SAC1C,EAACd,MAAM,CAACnB,YAAY,IAAI,aAAa;YAAA;cAAAkC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAClC,CAAC,eACbhE,OAAA,CAAC5C,UAAU;cAACoH,OAAO,EAAC,OAAO;cAACE,KAAK,EAAC,gBAAgB;cAAAd,QAAA,GAAC,cACrC,EAACd,MAAM,CAAClB,iBAAiB,IAAI,KAAK;YAAA;cAAAiC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACpC,CAAC,eACbhE,OAAA,CAAC5C,UAAU;cAACoH,OAAO,EAAC,OAAO;cAACE,KAAK,EAAC,gBAAgB;cAAAd,QAAA,GAAC,aACtC,EAACd,MAAM,CAACjB,SAAS,IAAI,KAAK;YAAA;cAAAgC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC3B,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACF,CAAC,eACdhE,OAAA,CAACtC,WAAW;YAAAkG,QAAA,gBACV5D,OAAA,CAAC3C,MAAM;cACLsH,IAAI,EAAC,OAAO;cACZC,SAAS,eAAE5E,OAAA,CAACX,QAAQ;gBAAAwE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAE;cACxBa,OAAO,EAAEA,CAAA,KAAM;gBACb9D,aAAa,CAAC,iBAAiB,CAAC;gBAChC8B,kBAAkB,CAACC,MAAM,CAAC;cAC5B,CAAE;cAAAc,QAAA,EACH;YAED;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC,eACThE,OAAA,CAAC3C,MAAM;cACLsH,IAAI,EAAC,OAAO;cACZD,KAAK,EAAC,OAAO;cACbE,SAAS,eAAE5E,OAAA,CAACT,UAAU;gBAAAsE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAE;cAC1Ba,OAAO,EAAEA,CAAA,KAAM;gBACb9D,aAAa,CAAC,eAAe,CAAC;gBAC9BE,iBAAiB,CAAC6B,MAAM,CAAC;gBACzBjC,aAAa,CAAC,IAAI,CAAC;cACrB,CAAE;cAAA+C,QAAA,EACH;YAED;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACE,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACV;MAAC,GA1D6BlB,MAAM,CAAC1B,aAAa;QAAAyC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OA2DpD,CACP;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACE,CAAC;EAEX,CAAC;;EAED;EACA,MAAMc,YAAY,GAAGA,CAAA,KAAM;IACzB,IAAIhE,UAAU,KAAK,YAAY,IAAIA,UAAU,KAAK,gBAAgB,EAAE;MAClE,oBACEd,OAAA,CAACrC,MAAM;QAACoH,IAAI,EAAEnE,UAAW;QAACoE,OAAO,EAAEpC,iBAAkB;QAACqC,QAAQ,EAAC,IAAI;QAACC,SAAS;QAAAtB,QAAA,gBAC3E5D,OAAA,CAACpC,WAAW;UAAAgG,QAAA,EACT9C,UAAU,KAAK,YAAY,GAAG,mBAAmB,GAAG;QAAiB;UAAA+C,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC3D,CAAC,eACdhE,OAAA,CAACnC,aAAa;UAAA+F,QAAA,eACZ5D,OAAA,CAACzC,IAAI;YAAC0G,SAAS;YAACC,OAAO,EAAE,CAAE;YAACiB,EAAE,EAAE;cAAEC,EAAE,EAAE;YAAE,CAAE;YAAAxB,QAAA,gBACxC5D,OAAA,CAACzC,IAAI;cAAC6G,IAAI;cAACC,EAAE,EAAE,EAAG;cAACC,EAAE,EAAE,CAAE;cAAAV,QAAA,eACvB5D,OAAA,CAACjC,SAAS;gBACRkF,IAAI,EAAC,eAAe;gBACpBoC,KAAK,EAAC,eAAe;gBACrBH,SAAS;gBACTV,OAAO,EAAC,UAAU;gBAClBtB,KAAK,EAAEhC,QAAQ,CAACE,aAAc;gBAC9BkE,QAAQ,EAAEvC,gBAAiB;gBAC3BwC,QAAQ,EAAEzE,UAAU,KAAK,gBAAiB;gBAC1C0E,QAAQ;cAAA;gBAAA3B,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACT;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACE,CAAC,eACPhE,OAAA,CAACzC,IAAI;cAAC6G,IAAI;cAACC,EAAE,EAAE,EAAG;cAACC,EAAE,EAAE,CAAE;cAAAV,QAAA,eACvB5D,OAAA,CAACjC,SAAS;gBACRkF,IAAI,EAAC,WAAW;gBAChBoC,KAAK,EAAC,WAAW;gBACjBH,SAAS;gBACTV,OAAO,EAAC,UAAU;gBAClBtB,KAAK,EAAEhC,QAAQ,CAACuE,SAAU;gBAC1BH,QAAQ,EAAEvC;cAAiB;gBAAAc,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC5B;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACE,CAAC,eACPhE,OAAA,CAACzC,IAAI;cAAC6G,IAAI;cAACC,EAAE,EAAE,EAAG;cAACC,EAAE,EAAE,CAAE;cAAAV,QAAA,eACvB5D,OAAA,CAACjC,SAAS;gBACRkF,IAAI,EAAC,aAAa;gBAClBoC,KAAK,EAAC,aAAa;gBACnBH,SAAS;gBACTV,OAAO,EAAC,UAAU;gBAClBtB,KAAK,EAAEhC,QAAQ,CAACwE,WAAY;gBAC5BJ,QAAQ,EAAEvC;cAAiB;gBAAAc,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC5B;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACE,CAAC,eACPhE,OAAA,CAACzC,IAAI;cAAC6G,IAAI;cAACC,EAAE,EAAE,EAAG;cAACC,EAAE,EAAE,CAAE;cAAAV,QAAA,eACvB5D,OAAA,CAACjC,SAAS;gBACRkF,IAAI,EAAC,OAAO;gBACZoC,KAAK,EAAC,OAAO;gBACbH,SAAS;gBACTV,OAAO,EAAC,UAAU;gBAClBtB,KAAK,EAAEhC,QAAQ,CAACyE,KAAM;gBACtBL,QAAQ,EAAEvC;cAAiB;gBAAAc,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC5B;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACE,CAAC,eACPhE,OAAA,CAACzC,IAAI;cAAC6G,IAAI;cAACC,EAAE,EAAE,EAAG;cAACC,EAAE,EAAE,CAAE;cAAAV,QAAA,eACvB5D,OAAA,CAACjC,SAAS;gBACRkF,IAAI,EAAC,kBAAkB;gBACvBoC,KAAK,EAAC,kBAAkB;gBACxBH,SAAS;gBACTV,OAAO,EAAC,UAAU;gBAClBtB,KAAK,EAAEhC,QAAQ,CAAC0E,gBAAiB;gBACjCN,QAAQ,EAAEvC;cAAiB;gBAAAc,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC5B;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACE,CAAC,eACPhE,OAAA,CAACzC,IAAI;cAAC6G,IAAI;cAACC,EAAE,EAAE,EAAG;cAACC,EAAE,EAAE,CAAE;cAAAV,QAAA,eACvB5D,OAAA,CAACjC,SAAS;gBACRkF,IAAI,EAAC,kBAAkB;gBACvBoC,KAAK,EAAC,sBAAsB;gBAC5BQ,IAAI,EAAC,QAAQ;gBACbX,SAAS;gBACTV,OAAO,EAAC,UAAU;gBAClBtB,KAAK,EAAEhC,QAAQ,CAAC4E,gBAAiB;gBACjCR,QAAQ,EAAEvC;cAAiB;gBAAAc,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC5B;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACE,CAAC,eACPhE,OAAA,CAACzC,IAAI;cAAC6G,IAAI;cAACC,EAAE,EAAE,EAAG;cAACC,EAAE,EAAE,CAAE;cAAAV,QAAA,eACvB5D,OAAA,CAACjC,SAAS;gBACRkF,IAAI,EAAC,mBAAmB;gBACxBoC,KAAK,EAAC,uBAAuB;gBAC7BQ,IAAI,EAAC,QAAQ;gBACbX,SAAS;gBACTV,OAAO,EAAC,UAAU;gBAClBtB,KAAK,EAAEhC,QAAQ,CAAC6E,iBAAkB;gBAClCT,QAAQ,EAAEvC;cAAiB;gBAAAc,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC5B;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACE,CAAC,eACPhE,OAAA,CAACzC,IAAI;cAAC6G,IAAI;cAACC,EAAE,EAAE,EAAG;cAAAT,QAAA,eAChB5D,OAAA,CAACjC,SAAS;gBACRkF,IAAI,EAAC,MAAM;gBACXoC,KAAK,EAAC,MAAM;gBACZH,SAAS;gBACTc,SAAS;gBACTC,IAAI,EAAE,CAAE;gBACRzB,OAAO,EAAC,UAAU;gBAClBtB,KAAK,EAAEhC,QAAQ,CAACgF,IAAK;gBACrBZ,QAAQ,EAAEvC;cAAiB;gBAAAc,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC5B;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACE,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACM,CAAC,eAChBhE,OAAA,CAAClC,aAAa;UAAA8F,QAAA,gBACZ5D,OAAA,CAAC3C,MAAM;YAACwH,OAAO,EAAEjC,iBAAkB;YAAAgB,QAAA,EAAC;UAAO;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC,eACpDhE,OAAA,CAAC3C,MAAM;YACLwH,OAAO,EAAEzB,UAAW;YACpBmC,QAAQ,EAAEjF,OAAO,IAAKQ,UAAU,KAAK,YAAY,IAAI,CAACI,QAAQ,CAACE,aAAe;YAC9EwD,SAAS,EAAEtE,OAAO,gBAAGN,OAAA,CAACrB,gBAAgB;cAACgG,IAAI,EAAE;YAAG;cAAAd,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,gBAAGhE,OAAA,CAACL,QAAQ;cAAAkE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAE;YAAAJ,QAAA,EACpE;UAED;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACI,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACV,CAAC;IAEb,CAAC,MAAM,IAAIlD,UAAU,KAAK,iBAAiB,EAAE;MAC3C,oBACEd,OAAA,CAACrC,MAAM;QAACoH,IAAI,EAAEnE,UAAW;QAACoE,OAAO,EAAEpC,iBAAkB;QAACqC,QAAQ,EAAC,IAAI;QAACC,SAAS;QAAAtB,QAAA,gBAC3E5D,OAAA,CAACpC,WAAW;UAAAgG,QAAA,EAAC;QAA8B;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAa,CAAC,eACzDhE,OAAA,CAACnC,aAAa;UAAA+F,QAAA,EACXtD,OAAO,gBACNN,OAAA,CAACrB,gBAAgB;YAAAkF,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,GAClBxD,MAAM,CAACkD,MAAM,KAAK,CAAC,gBACrB1D,OAAA,CAACtB,KAAK;YAACiF,QAAQ,EAAC,MAAM;YAAAC,QAAA,EAAC;UAA0B;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAO,CAAC,gBAEzDhE,OAAA,CAAC5B,IAAI;YAAAwF,QAAA,EACFpD,MAAM,CAAC2D,GAAG,CAAErB,MAAM,iBACjB9C,OAAA,CAAC3B,QAAQ;cACP8H,MAAM;cAENtB,OAAO,EAAEA,CAAA,KAAMhC,kBAAkB,CAACC,MAAM,CAAE;cAAAc,QAAA,eAE1C5D,OAAA,CAAC1B,YAAY;gBACX8H,OAAO,EAAE,WAAWtD,MAAM,CAAC1B,aAAa,EAAG;gBAC3CiF,SAAS,EAAE,cAAcvD,MAAM,CAACxB,SAAS,IAAI,KAAK,eAAewB,MAAM,CAACzB,OAAO,IAAI,KAAK,eAAeyB,MAAM,CAACpB,aAAa,IAAI,KAAK;cAAK;gBAAAmC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC1I;YAAC,GANGlB,MAAM,CAAC1B,aAAa;cAAAyC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAOjB,CACX;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACE;QACP;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACY,CAAC,eAChBhE,OAAA,CAAClC,aAAa;UAAA8F,QAAA,eACZ5D,OAAA,CAAC3C,MAAM;YAACwH,OAAO,EAAEjC,iBAAkB;YAAAgB,QAAA,EAAC;UAAO;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACvC,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACV,CAAC;IAEb,CAAC,MAAM,IAAIlD,UAAU,KAAK,eAAe,EAAE;MACzC,oBACEd,OAAA,CAACrC,MAAM;QAACoH,IAAI,EAAEnE,UAAW;QAACoE,OAAO,EAAEpC,iBAAkB;QAACqC,QAAQ,EAAC,IAAI;QAACC,SAAS;QAAAtB,QAAA,gBAC3E5D,OAAA,CAACpC,WAAW;UAAAgG,QAAA,EAAC;QAAc;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAa,CAAC,eACzChE,OAAA,CAACnC,aAAa;UAAA+F,QAAA,EACX,CAAC5C,cAAc,GACdV,OAAO,gBACLN,OAAA,CAACrB,gBAAgB;YAAAkF,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,GAClBxD,MAAM,CAACkD,MAAM,KAAK,CAAC,gBACrB1D,OAAA,CAACtB,KAAK;YAACiF,QAAQ,EAAC,MAAM;YAAAC,QAAA,EAAC;UAA0B;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAO,CAAC,gBAEzDhE,OAAA,CAAC5B,IAAI;YAAAwF,QAAA,EACFpD,MAAM,CAAC2D,GAAG,CAAErB,MAAM,iBACjB9C,OAAA,CAAC3B,QAAQ;cACP8H,MAAM;cAENtB,OAAO,EAAEA,CAAA,KAAM5D,iBAAiB,CAAC6B,MAAM,CAAE;cAAAc,QAAA,eAEzC5D,OAAA,CAAC1B,YAAY;gBACX8H,OAAO,EAAE,WAAWtD,MAAM,CAAC1B,aAAa,EAAG;gBAC3CiF,SAAS,EAAE,cAAcvD,MAAM,CAACxB,SAAS,IAAI,KAAK,eAAewB,MAAM,CAACzB,OAAO,IAAI,KAAK,eAAeyB,MAAM,CAACpB,aAAa,IAAI,KAAK;cAAK;gBAAAmC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC1I;YAAC,GANGlB,MAAM,CAAC1B,aAAa;cAAAyC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAOjB,CACX;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACE,CACP,gBAEDhE,OAAA,CAAC7C,GAAG;YAAAyG,QAAA,gBACF5D,OAAA,CAACtB,KAAK;cAACiF,QAAQ,EAAC,SAAS;cAACwB,EAAE,EAAE;gBAAEmB,EAAE,EAAE;cAAE,CAAE;cAAA1C,QAAA,GAAC,0CACC,EAAC5C,cAAc,CAACI,aAAa,EAAC,GACxE;YAAA;cAAAyC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CAAC,eACRhE,OAAA,CAAC5C,UAAU;cAACoH,OAAO,EAAC,OAAO;cAAAZ,QAAA,EAAC;YAE5B;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAY,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACV;QACN;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACY,CAAC,eAChBhE,OAAA,CAAClC,aAAa;UAAA8F,QAAA,gBACZ5D,OAAA,CAAC3C,MAAM;YAACwH,OAAO,EAAEjC,iBAAkB;YAAAgB,QAAA,EAAC;UAAO;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC,EACnDhD,cAAc,iBACbhB,OAAA,CAAC3C,MAAM;YACLwH,OAAO,EAAEzB,UAAW;YACpBmC,QAAQ,EAAEjF,OAAQ;YAClBoE,KAAK,EAAC,OAAO;YACbE,SAAS,EAAEtE,OAAO,gBAAGN,OAAA,CAACrB,gBAAgB;cAACgG,IAAI,EAAE;YAAG;cAAAd,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,gBAAGhE,OAAA,CAACT,UAAU;cAAAsE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAE;YAAAJ,QAAA,EACtE;UAED;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CACT;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACY,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACV,CAAC;IAEb,CAAC,MAAM,IAAIlD,UAAU,KAAK,mBAAmB,EAAE;MAC7C,oBACEd,OAAA,CAACrC,MAAM;QAACoH,IAAI,EAAEnE,UAAW;QAACoE,OAAO,EAAEpC,iBAAkB;QAACqC,QAAQ,EAAC,IAAI;QAACC,SAAS;QAAAtB,QAAA,gBAC3E5D,OAAA,CAACpC,WAAW;UAAAgG,QAAA,EAAC;QAAuB;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAa,CAAC,eAClDhE,OAAA,CAACnC,aAAa;UAAA+F,QAAA,EACXtD,OAAO,gBACNN,OAAA,CAACrB,gBAAgB;YAAAkF,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,GAClB/B,eAAe,CAACyB,MAAM,KAAK,CAAC,gBAC9B1D,OAAA,CAACtB,KAAK;YAACiF,QAAQ,EAAC,MAAM;YAAAC,QAAA,EAAC;UAA+B;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAO,CAAC,gBAE9DhE,OAAA,CAACjB,cAAc;YAAC0F,SAAS,EAAEnH,KAAM;YAAC6H,EAAE,EAAE;cAAEC,EAAE,EAAE;YAAE,CAAE;YAAAxB,QAAA,eAC9C5D,OAAA,CAACpB,KAAK;cAAC+F,IAAI,EAAC,OAAO;cAAAf,QAAA,gBACjB5D,OAAA,CAAChB,SAAS;gBAAA4E,QAAA,eACR5D,OAAA,CAACf,QAAQ;kBAAA2E,QAAA,gBACP5D,OAAA,CAAClB,SAAS;oBAAA8E,QAAA,EAAC;kBAAM;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAW,CAAC,eAC7BhE,OAAA,CAAClB,SAAS;oBAAA8E,QAAA,EAAC;kBAAI;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAW,CAAC,eAC3BhE,OAAA,CAAClB,SAAS;oBAAA8E,QAAA,EAAC;kBAAa;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAW,CAAC,eACpChE,OAAA,CAAClB,SAAS;oBAAA8E,QAAA,EAAC;kBAAgB;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAW,CAAC,eACvChE,OAAA,CAAClB,SAAS;oBAAA8E,QAAA,EAAC;kBAAS;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAW,CAAC,eAChChE,OAAA,CAAClB,SAAS;oBAAA8E,QAAA,EAAC;kBAAI;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAW,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACnB;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACF,CAAC,eACZhE,OAAA,CAACnB,SAAS;gBAAA+E,QAAA,EACP3B,eAAe,CAACkC,GAAG,CAAC,CAACoC,MAAM,EAAEC,KAAK,kBACjCxG,OAAA,CAACf,QAAQ;kBAAA2E,QAAA,gBACP5D,OAAA,CAAClB,SAAS;oBAAA8E,QAAA,EAAE2C,MAAM,CAACnF;kBAAa;oBAAAyC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAY,CAAC,eAC7ChE,OAAA,CAAClB,SAAS;oBAAA8E,QAAA,EAAE2C,MAAM,CAACE;kBAAO;oBAAA5C,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAY,CAAC,eACvChE,OAAA,CAAClB,SAAS;oBAAA8E,QAAA,EAAE,IAAI8C,IAAI,CAACH,MAAM,CAACI,aAAa,CAAC,CAACC,kBAAkB,CAAC;kBAAC;oBAAA/C,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAY,CAAC,eAC5EhE,OAAA,CAAClB,SAAS;oBAAA8E,QAAA,EAAE2C,MAAM,CAACM;kBAAgB;oBAAAhD,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAY,CAAC,eAChDhE,OAAA,CAAClB,SAAS;oBAAA8E,QAAA,EAAE2C,MAAM,CAACO;kBAAS;oBAAAjD,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAY,CAAC,eACzChE,OAAA,CAAClB,SAAS;oBAAA8E,QAAA,EAAE2C,MAAM,CAACL,IAAI,IAAI;kBAAG;oBAAArC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAY,CAAC;gBAAA,GAN9BwC,KAAK;kBAAA3C,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAOV,CACX;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACO,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACP;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACM;QACjB;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACY,CAAC,eAChBhE,OAAA,CAAClC,aAAa;UAAA8F,QAAA,eACZ5D,OAAA,CAAC3C,MAAM;YAACwH,OAAO,EAAEjC,iBAAkB;YAAAgB,QAAA,EAAC;UAAM;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACtC,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACV,CAAC;IAEb;IAEA,OAAO,IAAI;EACb,CAAC;EAED,oBACEhE,OAAA,CAAC7C,GAAG;IAAAyG,QAAA,GACDlD,cAAc,KAAK,kBAAkB,IAAI,CAACE,UAAU,gBACnDZ,OAAA,CAAC1C,KAAK;MAAC6H,EAAE,EAAE;QAAE4B,CAAC,EAAE;MAAE,CAAE;MAAAnD,QAAA,gBAClB5D,OAAA,CAAC5C,UAAU;QAACoH,OAAO,EAAC,IAAI;QAACwC,YAAY;QAAApD,QAAA,EAAC;MAEtC;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAY,CAAC,EACZ1D,OAAO,gBACNN,OAAA,CAAC7C,GAAG;QAACgI,EAAE,EAAE;UAAE8B,OAAO,EAAE,MAAM;UAAEC,cAAc,EAAE,QAAQ;UAAEC,EAAE,EAAE;QAAE,CAAE;QAAAvD,QAAA,eAC5D5D,OAAA,CAACrB,gBAAgB;UAAAkF,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACjB,CAAC,GAENP,iBAAiB,CAAC,CACnB;IAAA;MAAAI,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACI,CAAC,GACN,CAACpD,UAAU,gBACbZ,OAAA,CAAC1C,KAAK;MAAC6H,EAAE,EAAE;QAAE4B,CAAC,EAAE,CAAC;QAAEK,SAAS,EAAE,OAAO;QAAEH,OAAO,EAAE,MAAM;QAAEI,UAAU,EAAE,QAAQ;QAAEH,cAAc,EAAE;MAAS,CAAE;MAAAtD,QAAA,EACtG,CAAClD,cAAc,gBACdV,OAAA,CAAC5C,UAAU;QAACoH,OAAO,EAAC,OAAO;QAAAZ,QAAA,EAAC;MAE5B;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAY,CAAC,gBAEbhE,OAAA,CAAC7C,GAAG;QAACgI,EAAE,EAAE;UAAEmC,SAAS,EAAE;QAAS,CAAE;QAAA1D,QAAA,gBAC/B5D,OAAA,CAAC5C,UAAU;UAACoH,OAAO,EAAC,IAAI;UAACwC,YAAY;UAAApD,QAAA,GAClClD,cAAc,KAAK,YAAY,IAAI,mBAAmB,EACtDA,cAAc,KAAK,gBAAgB,IAAI,iBAAiB,EACxDA,cAAc,KAAK,eAAe,IAAI,gBAAgB,EACtDA,cAAc,KAAK,mBAAmB,IAAI,6BAA6B;QAAA;UAAAmD,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC9D,CAAC,eACbhE,OAAA,CAACrB,gBAAgB;UAACwG,EAAE,EAAE;YAAEC,EAAE,EAAE;UAAE;QAAE;UAAAvB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAChC;IACN;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACI,CAAC,GACN,IAAI,EAEPc,YAAY,CAAC,CAAC;EAAA;IAAAjB,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACZ,CAAC;AAEV,CAAC;AAAC3D,EAAA,CAlhBIJ,SAAS;AAAAsH,EAAA,GAATtH,SAAS;AAohBf,eAAeA,SAAS;AAAC,IAAAsH,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}