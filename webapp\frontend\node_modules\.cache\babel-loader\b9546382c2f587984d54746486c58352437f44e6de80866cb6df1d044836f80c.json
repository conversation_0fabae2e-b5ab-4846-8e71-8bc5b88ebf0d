{"ast": null, "code": "export { default } from './Accordion';\nexport { default as accordionClasses } from './accordionClasses';\nexport * from './accordionClasses';", "map": {"version": 3, "names": ["default", "accordionClasses"], "sources": ["C:/Users/<USER>/PycharmProjects/CMS/webapp/frontend/node_modules/@mui/material/Accordion/index.js"], "sourcesContent": ["export { default } from './Accordion';\nexport { default as accordionClasses } from './accordionClasses';\nexport * from './accordionClasses';"], "mappings": "AAAA,SAASA,OAAO,QAAQ,aAAa;AACrC,SAASA,OAAO,IAAIC,gBAAgB,QAAQ,oBAAoB;AAChE,cAAc,oBAAoB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}