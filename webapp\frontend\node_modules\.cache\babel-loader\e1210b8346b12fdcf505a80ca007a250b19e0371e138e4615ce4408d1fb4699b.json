{"ast": null, "code": "import ascending from \"./ascending.js\";\nimport minIndex from \"./minIndex.js\";\nexport default function leastIndex(values) {\n  let compare = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : ascending;\n  if (compare.length === 1) return minIndex(values, compare);\n  let minValue;\n  let min = -1;\n  let index = -1;\n  for (const value of values) {\n    ++index;\n    if (min < 0 ? compare(value, value) === 0 : compare(value, minValue) < 0) {\n      minValue = value;\n      min = index;\n    }\n  }\n  return min;\n}", "map": {"version": 3, "names": ["ascending", "minIndex", "leastIndex", "values", "compare", "arguments", "length", "undefined", "minValue", "min", "index", "value"], "sources": ["C:/Users/<USER>/PycharmProjects/CMS/webapp/frontend/node_modules/d3-array/src/leastIndex.js"], "sourcesContent": ["import ascending from \"./ascending.js\";\nimport minIndex from \"./minIndex.js\";\n\nexport default function leastIndex(values, compare = ascending) {\n  if (compare.length === 1) return minIndex(values, compare);\n  let minValue;\n  let min = -1;\n  let index = -1;\n  for (const value of values) {\n    ++index;\n    if (min < 0\n        ? compare(value, value) === 0\n        : compare(value, minValue) < 0) {\n      minValue = value;\n      min = index;\n    }\n  }\n  return min;\n}\n"], "mappings": "AAAA,OAAOA,SAAS,MAAM,gBAAgB;AACtC,OAAOC,QAAQ,MAAM,eAAe;AAEpC,eAAe,SAASC,UAAUA,CAACC,MAAM,EAAuB;EAAA,IAArBC,OAAO,GAAAC,SAAA,CAAAC,MAAA,QAAAD,SAAA,QAAAE,SAAA,GAAAF,SAAA,MAAGL,SAAS;EAC5D,IAAII,OAAO,CAACE,MAAM,KAAK,CAAC,EAAE,OAAOL,QAAQ,CAACE,MAAM,EAAEC,OAAO,CAAC;EAC1D,IAAII,QAAQ;EACZ,IAAIC,GAAG,GAAG,CAAC,CAAC;EACZ,IAAIC,KAAK,GAAG,CAAC,CAAC;EACd,KAAK,MAAMC,KAAK,IAAIR,MAAM,EAAE;IAC1B,EAAEO,KAAK;IACP,IAAID,GAAG,GAAG,CAAC,GACLL,OAAO,CAACO,KAAK,EAAEA,KAAK,CAAC,KAAK,CAAC,GAC3BP,OAAO,CAACO,KAAK,EAAEH,QAAQ,CAAC,GAAG,CAAC,EAAE;MAClCA,QAAQ,GAAGG,KAAK;MAChBF,GAAG,GAAGC,KAAK;IACb;EACF;EACA,OAAOD,GAAG;AACZ", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}