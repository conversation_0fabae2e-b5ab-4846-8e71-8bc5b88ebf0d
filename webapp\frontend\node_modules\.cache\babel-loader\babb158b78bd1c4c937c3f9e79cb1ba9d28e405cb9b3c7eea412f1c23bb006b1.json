{"ast": null, "code": "var _jsxFileName = \"C:\\\\CMS\\\\webapp\\\\frontend\\\\src\\\\components\\\\common\\\\FilterableTable.js\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect } from 'react';\nimport { Table, TableBody, TableContainer, TableHead, TableRow, Paper, Box, Typography, Chip, Button, CircularProgress, TablePagination } from '@mui/material';\nimport { Clear as ClearIcon } from '@mui/icons-material';\nimport FilterableTableHeader from './FilterableTableHeader';\n\n/**\n * Componente di tabella filtrabile in stile Excel\n *\n * @param {Object} props - Proprietà del componente\n * @param {Array} props.data - Dati da visualizzare\n * @param {Array} props.columns - Configurazione delle colonne\n * @param {Function} props.onFilteredDataChange - Funzione chiamata quando i dati filtrati cambiano\n * @param {boolean} props.loading - Indica se i dati sono in caricamento\n * @param {string} props.emptyMessage - Messaggio da visualizzare quando non ci sono dati\n * @param {Function} props.renderRow - Funzione per renderizzare una riga personalizzata\n * @param {boolean} props.pagination - Abilita la paginazione\n * @param {Function} props.onResetFilters - Funzione chiamata quando vengono resettati i filtri\n */\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst FilterableTable = ({\n  data = [],\n  columns = [],\n  onFilteredDataChange = null,\n  loading = false,\n  emptyMessage = \"Nessun dato disponibile\",\n  renderRow = null,\n  pagination = true,\n  onResetFilters = null\n}) => {\n  _s();\n  const [filteredData, setFilteredData] = useState(data);\n  const [activeFilters, setActiveFilters] = useState({});\n  const [sortConfig, setSortConfig] = useState({\n    key: null,\n    direction: null\n  });\n  const [page, setPage] = useState(0);\n  const [rowsPerPage, setRowsPerPage] = useState(10);\n\n  // Aggiorna i dati filtrati quando cambiano i dati di input\n  useEffect(() => {\n    setFilteredData(data);\n  }, [data]);\n\n  // Notifica il componente padre quando cambiano i dati filtrati\n  useEffect(() => {\n    if (onFilteredDataChange) {\n      onFilteredDataChange(filteredData);\n    }\n  }, [filteredData, onFilteredDataChange]);\n\n  // Gestisce il cambio di filtro per una colonna\n  const handleFilterChange = (columnName, filteredColumnData, filterConfig) => {\n    // Aggiorna i filtri attivi\n    setActiveFilters(prev => ({\n      ...prev,\n      [columnName]: filterConfig\n    }));\n\n    // Applica tutti i filtri attivi\n    let result = [...data];\n\n    // Per ogni filtro attivo, filtra i dati\n    Object.keys(activeFilters).forEach(key => {\n      if (key !== columnName) {\n        const filter = activeFilters[key];\n        result = applyFilter(result, filter);\n      }\n    });\n\n    // Applica il nuovo filtro\n    result = filteredColumnData;\n\n    // Applica l'ordinamento corrente\n    if (sortConfig.key) {\n      result = applySorting(result, sortConfig.key, sortConfig.direction);\n    }\n    setFilteredData(result);\n    setPage(0); // Torna alla prima pagina quando cambia il filtro\n  };\n\n  // Applica un filtro ai dati\n  const applyFilter = (dataToFilter, filterConfig) => {\n    if (!filterConfig) return dataToFilter;\n    const {\n      columnName,\n      filterType,\n      selectedValues,\n      searchTerm,\n      rangeValues\n    } = filterConfig;\n    let result = [...dataToFilter];\n\n    // Applica il filtro in base al tipo\n    if (filterType === 'equals' && selectedValues && selectedValues.length > 0) {\n      result = result.filter(item => selectedValues.includes(item[columnName]));\n    } else if (filterType === 'contains' && searchTerm) {\n      result = result.filter(item => item[columnName] && String(item[columnName]).toLowerCase().includes(searchTerm.toLowerCase()));\n    } else if (filterType === 'greaterThan' && rangeValues && rangeValues.min !== '') {\n      result = result.filter(item => parseFloat(item[columnName]) > parseFloat(rangeValues.min));\n    } else if (filterType === 'lessThan' && rangeValues && rangeValues.max !== '') {\n      result = result.filter(item => parseFloat(item[columnName]) < parseFloat(rangeValues.max));\n    } else if (filterType === 'between' && rangeValues && rangeValues.min !== '' && rangeValues.max !== '') {\n      result = result.filter(item => parseFloat(item[columnName]) >= parseFloat(rangeValues.min) && parseFloat(item[columnName]) <= parseFloat(rangeValues.max));\n    }\n    return result;\n  };\n\n  // Gestisce il cambio di ordinamento\n  const handleSortChange = (key, direction) => {\n    setSortConfig({\n      key,\n      direction\n    });\n    const sortedData = applySorting([...filteredData], key, direction);\n    setFilteredData(sortedData);\n  };\n\n  // Applica l'ordinamento ai dati\n  const applySorting = (dataToSort, key, direction) => {\n    return dataToSort.sort((a, b) => {\n      // Gestisci i valori null o undefined\n      if (!a[key] && a[key] !== 0) return direction === 'asc' ? 1 : -1;\n      if (!b[key] && b[key] !== 0) return direction === 'asc' ? -1 : 1;\n\n      // Determina il tipo di dato\n      const column = columns.find(col => col.field === key);\n      const dataType = (column === null || column === void 0 ? void 0 : column.dataType) || 'text';\n      let valueA = a[key];\n      let valueB = b[key];\n\n      // Converti i valori in base al tipo di dato\n      if (dataType === 'number') {\n        valueA = parseFloat(valueA) || 0;\n        valueB = parseFloat(valueB) || 0;\n      } else {\n        valueA = String(valueA || '').toLowerCase();\n        valueB = String(valueB || '').toLowerCase();\n      }\n\n      // Esegui l'ordinamento\n      if (direction === 'asc') {\n        return valueA > valueB ? 1 : -1;\n      } else {\n        return valueA < valueB ? 1 : -1;\n      }\n    });\n  };\n\n  // Resetta tutti i filtri\n  const resetAllFilters = () => {\n    setActiveFilters({});\n    setSortConfig({\n      key: null,\n      direction: null\n    });\n    setFilteredData(data);\n    setPage(0);\n\n    // Chiama la callback onResetFilters se fornita\n    if (onResetFilters) {\n      onResetFilters();\n    }\n  };\n\n  // Rimuove un filtro specifico\n  const removeFilter = columnName => {\n    const newFilters = {\n      ...activeFilters\n    };\n    delete newFilters[columnName];\n    setActiveFilters(newFilters);\n\n    // Riapplica i filtri rimanenti\n    let result = [...data];\n    Object.values(newFilters).forEach(filter => {\n      result = applyFilter(result, filter);\n    });\n\n    // Applica l'ordinamento corrente\n    if (sortConfig.key) {\n      result = applySorting(result, sortConfig.key, sortConfig.direction);\n    }\n    setFilteredData(result);\n  };\n\n  // Gestisce il cambio di pagina\n  const handleChangePage = (event, newPage) => {\n    setPage(newPage);\n  };\n\n  // Gestisce il cambio di righe per pagina\n  const handleChangeRowsPerPage = event => {\n    setRowsPerPage(parseInt(event.target.value, 10));\n    setPage(0);\n  };\n\n  // Calcola i dati da visualizzare in base alla paginazione\n  const displayData = pagination ? filteredData.slice(page * rowsPerPage, page * rowsPerPage + rowsPerPage) : filteredData;\n\n  // Verifica se ci sono filtri attivi\n  const hasActiveFilters = Object.keys(activeFilters).length > 0;\n  return /*#__PURE__*/_jsxDEV(Box, {\n    children: [hasActiveFilters && /*#__PURE__*/_jsxDEV(Box, {\n      sx: {\n        mb: 2,\n        display: 'flex',\n        flexWrap: 'wrap',\n        gap: 1,\n        alignItems: 'center'\n      },\n      children: [/*#__PURE__*/_jsxDEV(Typography, {\n        variant: \"body2\",\n        sx: {\n          mr: 1\n        },\n        children: \"Filtri attivi:\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 221,\n        columnNumber: 11\n      }, this), Object.entries(activeFilters).map(([columnName, filter]) => {\n        const column = columns.find(col => col.field === columnName);\n        if (!column || !filter) return null;\n        let label = '';\n        if (filter.filterType === 'equals') {\n          var _filter$selectedValue;\n          const selectedCount = ((_filter$selectedValue = filter.selectedValues) === null || _filter$selectedValue === void 0 ? void 0 : _filter$selectedValue.length) || 0;\n          const totalCount = data.filter(item => item[columnName] !== undefined && item[columnName] !== null).length;\n          label = `${column.headerName}: ${selectedCount} di ${totalCount}`;\n        } else if (filter.filterType === 'contains') {\n          label = `${column.headerName} contiene: ${filter.searchTerm}`;\n        } else if (filter.filterType === 'greaterThan') {\n          label = `${column.headerName} > ${filter.rangeValues.min}`;\n        } else if (filter.filterType === 'lessThan') {\n          label = `${column.headerName} < ${filter.rangeValues.max}`;\n        } else if (filter.filterType === 'between') {\n          label = `${column.headerName}: ${filter.rangeValues.min} - ${filter.rangeValues.max}`;\n        }\n        return /*#__PURE__*/_jsxDEV(Chip, {\n          label: label,\n          size: \"small\",\n          onDelete: () => removeFilter(columnName)\n        }, columnName, false, {\n          fileName: _jsxFileName,\n          lineNumber: 243,\n          columnNumber: 15\n        }, this);\n      }), /*#__PURE__*/_jsxDEV(Button, {\n        variant: \"text\",\n        size: \"small\",\n        startIcon: /*#__PURE__*/_jsxDEV(ClearIcon, {}, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 255,\n          columnNumber: 24\n        }, this),\n        onClick: resetAllFilters,\n        children: \"Resetta tutti\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 252,\n        columnNumber: 11\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 220,\n      columnNumber: 9\n    }, this), /*#__PURE__*/_jsxDEV(TableContainer, {\n      component: Paper,\n      sx: {\n        mb: 2\n      },\n      children: /*#__PURE__*/_jsxDEV(Table, {\n        size: \"small\",\n        children: [/*#__PURE__*/_jsxDEV(TableHead, {\n          children: /*#__PURE__*/_jsxDEV(TableRow, {\n            children: columns.map(column => /*#__PURE__*/_jsxDEV(FilterableTableHeader, {\n              columnName: column.field,\n              label: column.headerName,\n              data: data,\n              onFilterChange: (filteredData, filterConfig) => handleFilterChange(column.field, filteredData, filterConfig),\n              dataType: column.dataType || 'text',\n              sortDirection: sortConfig.key === column.field ? sortConfig.direction : null,\n              onSortChange: handleSortChange,\n              disableFilter: column.disableFilter,\n              disableSort: column.disableSort,\n              align: column.align || 'left',\n              sx: column.headerStyle,\n              renderHeader: column.renderHeader\n            }, column.field, false, {\n              fileName: _jsxFileName,\n              lineNumber: 269,\n              columnNumber: 17\n            }, this))\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 267,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 266,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(TableBody, {\n          children: loading ? /*#__PURE__*/_jsxDEV(TableRow, {\n            children: /*#__PURE__*/_jsxDEV(\"td\", {\n              colSpan: columns.length,\n              style: {\n                textAlign: 'center',\n                padding: '20px'\n              },\n              children: /*#__PURE__*/_jsxDEV(CircularProgress, {\n                size: 30\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 293,\n                columnNumber: 19\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 292,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 291,\n            columnNumber: 15\n          }, this) : displayData.length > 0 ? displayData.map((row, index) => renderRow ? renderRow(row, index) : /*#__PURE__*/_jsxDEV(TableRow, {\n            children: columns.map(column => /*#__PURE__*/_jsxDEV(\"td\", {\n              style: {\n                padding: '8px 16px',\n                textAlign: column.align || 'left',\n                ...(column.cellStyle || {})\n              },\n              children: column.renderCell ? column.renderCell(row) : row[column.field]\n            }, column.field, false, {\n              fileName: _jsxFileName,\n              lineNumber: 303,\n              columnNumber: 23\n            }, this))\n          }, index, false, {\n            fileName: _jsxFileName,\n            lineNumber: 301,\n            columnNumber: 19\n          }, this)) : /*#__PURE__*/_jsxDEV(TableRow, {\n            children: /*#__PURE__*/_jsxDEV(\"td\", {\n              colSpan: columns.length,\n              style: {\n                textAlign: 'center',\n                padding: '20px'\n              },\n              children: emptyMessage\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 319,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 318,\n            columnNumber: 15\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 289,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 265,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 264,\n      columnNumber: 7\n    }, this), pagination && filteredData.length > 0 && /*#__PURE__*/_jsxDEV(TablePagination, {\n      rowsPerPageOptions: [5, 10, 25, 50, 100],\n      component: \"div\",\n      count: filteredData.length,\n      rowsPerPage: rowsPerPage,\n      page: page,\n      onPageChange: handleChangePage,\n      onRowsPerPageChange: handleChangeRowsPerPage,\n      labelRowsPerPage: \"Righe per pagina:\",\n      labelDisplayedRows: ({\n        from,\n        to,\n        count\n      }) => `${from}-${to} di ${count}`\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 332,\n      columnNumber: 9\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 217,\n    columnNumber: 5\n  }, this);\n};\n_s(FilterableTable, \"x0NTjsRs0JcRFSi5H+t+eTmr9ao=\");\n_c = FilterableTable;\nexport default FilterableTable;\nvar _c;\n$RefreshReg$(_c, \"FilterableTable\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "Table", "TableBody", "TableContainer", "TableHead", "TableRow", "Paper", "Box", "Typography", "Chip", "<PERSON><PERSON>", "CircularProgress", "TablePagination", "Clear", "ClearIcon", "FilterableTableHeader", "jsxDEV", "_jsxDEV", "FilterableTable", "data", "columns", "onFilteredDataChange", "loading", "emptyMessage", "renderRow", "pagination", "onResetFilters", "_s", "filteredData", "setFilteredData", "activeFilters", "setActiveFilters", "sortConfig", "setSortConfig", "key", "direction", "page", "setPage", "rowsPerPage", "setRowsPerPage", "handleFilterChange", "columnName", "filteredColumnData", "filterConfig", "prev", "result", "Object", "keys", "for<PERSON>ach", "filter", "applyFilter", "applySorting", "dataToFilter", "filterType", "<PERSON><PERSON><PERSON><PERSON>", "searchTerm", "rangeValues", "length", "item", "includes", "String", "toLowerCase", "min", "parseFloat", "max", "handleSortChange", "sortedData", "dataToSort", "sort", "a", "b", "column", "find", "col", "field", "dataType", "valueA", "valueB", "resetAllFilters", "removeFilter", "newFilters", "values", "handleChangePage", "event", "newPage", "handleChangeRowsPerPage", "parseInt", "target", "value", "displayData", "slice", "hasActiveFilters", "children", "sx", "mb", "display", "flexWrap", "gap", "alignItems", "variant", "mr", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "entries", "map", "label", "_filter$selectedValue", "selectedCount", "totalCount", "undefined", "headerName", "size", "onDelete", "startIcon", "onClick", "component", "onFilterChange", "sortDirection", "onSortChange", "disableFilter", "disableSort", "align", "headerStyle", "renderHeader", "colSpan", "style", "textAlign", "padding", "row", "index", "cellStyle", "renderCell", "rowsPerPageOptions", "count", "onPageChange", "onRowsPerPageChange", "labelRowsPerPage", "labelDisplayedRows", "from", "to", "_c", "$RefreshReg$"], "sources": ["C:/CMS/webapp/frontend/src/components/common/FilterableTable.js"], "sourcesContent": ["import React, { useState, useEffect } from 'react';\nimport {\n  Table,\n  TableBody,\n  TableContainer,\n  TableHead,\n  TableRow,\n  Paper,\n  Box,\n  Typography,\n  Chip,\n  Button,\n  CircularProgress,\n  TablePagination\n} from '@mui/material';\nimport { Clear as ClearIcon } from '@mui/icons-material';\nimport FilterableTableHeader from './FilterableTableHeader';\n\n/**\n * Componente di tabella filtrabile in stile Excel\n *\n * @param {Object} props - Proprietà del componente\n * @param {Array} props.data - Dati da visualizzare\n * @param {Array} props.columns - Configurazione delle colonne\n * @param {Function} props.onFilteredDataChange - Funzione chiamata quando i dati filtrati cambiano\n * @param {boolean} props.loading - Indica se i dati sono in caricamento\n * @param {string} props.emptyMessage - Messaggio da visualizzare quando non ci sono dati\n * @param {Function} props.renderRow - Funzione per renderizzare una riga personalizzata\n * @param {boolean} props.pagination - Abilita la paginazione\n * @param {Function} props.onResetFilters - Funzione chiamata quando vengono resettati i filtri\n */\nconst FilterableTable = ({\n  data = [],\n  columns = [],\n  onFilteredDataChange = null,\n  loading = false,\n  emptyMessage = \"Nessun dato disponibile\",\n  renderRow = null,\n  pagination = true,\n  onResetFilters = null\n}) => {\n  const [filteredData, setFilteredData] = useState(data);\n  const [activeFilters, setActiveFilters] = useState({});\n  const [sortConfig, setSortConfig] = useState({ key: null, direction: null });\n  const [page, setPage] = useState(0);\n  const [rowsPerPage, setRowsPerPage] = useState(10);\n\n  // Aggiorna i dati filtrati quando cambiano i dati di input\n  useEffect(() => {\n    setFilteredData(data);\n  }, [data]);\n\n  // Notifica il componente padre quando cambiano i dati filtrati\n  useEffect(() => {\n    if (onFilteredDataChange) {\n      onFilteredDataChange(filteredData);\n    }\n  }, [filteredData, onFilteredDataChange]);\n\n  // Gestisce il cambio di filtro per una colonna\n  const handleFilterChange = (columnName, filteredColumnData, filterConfig) => {\n    // Aggiorna i filtri attivi\n    setActiveFilters(prev => ({\n      ...prev,\n      [columnName]: filterConfig\n    }));\n\n    // Applica tutti i filtri attivi\n    let result = [...data];\n\n    // Per ogni filtro attivo, filtra i dati\n    Object.keys(activeFilters).forEach(key => {\n      if (key !== columnName) {\n        const filter = activeFilters[key];\n        result = applyFilter(result, filter);\n      }\n    });\n\n    // Applica il nuovo filtro\n    result = filteredColumnData;\n\n    // Applica l'ordinamento corrente\n    if (sortConfig.key) {\n      result = applySorting(result, sortConfig.key, sortConfig.direction);\n    }\n\n    setFilteredData(result);\n    setPage(0); // Torna alla prima pagina quando cambia il filtro\n  };\n\n  // Applica un filtro ai dati\n  const applyFilter = (dataToFilter, filterConfig) => {\n    if (!filterConfig) return dataToFilter;\n\n    const { columnName, filterType, selectedValues, searchTerm, rangeValues } = filterConfig;\n\n    let result = [...dataToFilter];\n\n    // Applica il filtro in base al tipo\n    if (filterType === 'equals' && selectedValues && selectedValues.length > 0) {\n      result = result.filter(item => selectedValues.includes(item[columnName]));\n    } else if (filterType === 'contains' && searchTerm) {\n      result = result.filter(item =>\n        item[columnName] && String(item[columnName]).toLowerCase().includes(searchTerm.toLowerCase())\n      );\n    } else if (filterType === 'greaterThan' && rangeValues && rangeValues.min !== '') {\n      result = result.filter(item =>\n        parseFloat(item[columnName]) > parseFloat(rangeValues.min)\n      );\n    } else if (filterType === 'lessThan' && rangeValues && rangeValues.max !== '') {\n      result = result.filter(item =>\n        parseFloat(item[columnName]) < parseFloat(rangeValues.max)\n      );\n    } else if (filterType === 'between' && rangeValues && rangeValues.min !== '' && rangeValues.max !== '') {\n      result = result.filter(item =>\n        parseFloat(item[columnName]) >= parseFloat(rangeValues.min) &&\n        parseFloat(item[columnName]) <= parseFloat(rangeValues.max)\n      );\n    }\n\n    return result;\n  };\n\n  // Gestisce il cambio di ordinamento\n  const handleSortChange = (key, direction) => {\n    setSortConfig({ key, direction });\n\n    const sortedData = applySorting([...filteredData], key, direction);\n    setFilteredData(sortedData);\n  };\n\n  // Applica l'ordinamento ai dati\n  const applySorting = (dataToSort, key, direction) => {\n    return dataToSort.sort((a, b) => {\n      // Gestisci i valori null o undefined\n      if (!a[key] && a[key] !== 0) return direction === 'asc' ? 1 : -1;\n      if (!b[key] && b[key] !== 0) return direction === 'asc' ? -1 : 1;\n\n      // Determina il tipo di dato\n      const column = columns.find(col => col.field === key);\n      const dataType = column?.dataType || 'text';\n\n      let valueA = a[key];\n      let valueB = b[key];\n\n      // Converti i valori in base al tipo di dato\n      if (dataType === 'number') {\n        valueA = parseFloat(valueA) || 0;\n        valueB = parseFloat(valueB) || 0;\n      } else {\n        valueA = String(valueA || '').toLowerCase();\n        valueB = String(valueB || '').toLowerCase();\n      }\n\n      // Esegui l'ordinamento\n      if (direction === 'asc') {\n        return valueA > valueB ? 1 : -1;\n      } else {\n        return valueA < valueB ? 1 : -1;\n      }\n    });\n  };\n\n  // Resetta tutti i filtri\n  const resetAllFilters = () => {\n    setActiveFilters({});\n    setSortConfig({ key: null, direction: null });\n    setFilteredData(data);\n    setPage(0);\n\n    // Chiama la callback onResetFilters se fornita\n    if (onResetFilters) {\n      onResetFilters();\n    }\n  };\n\n  // Rimuove un filtro specifico\n  const removeFilter = (columnName) => {\n    const newFilters = { ...activeFilters };\n    delete newFilters[columnName];\n    setActiveFilters(newFilters);\n\n    // Riapplica i filtri rimanenti\n    let result = [...data];\n    Object.values(newFilters).forEach(filter => {\n      result = applyFilter(result, filter);\n    });\n\n    // Applica l'ordinamento corrente\n    if (sortConfig.key) {\n      result = applySorting(result, sortConfig.key, sortConfig.direction);\n    }\n\n    setFilteredData(result);\n  };\n\n  // Gestisce il cambio di pagina\n  const handleChangePage = (event, newPage) => {\n    setPage(newPage);\n  };\n\n  // Gestisce il cambio di righe per pagina\n  const handleChangeRowsPerPage = (event) => {\n    setRowsPerPage(parseInt(event.target.value, 10));\n    setPage(0);\n  };\n\n  // Calcola i dati da visualizzare in base alla paginazione\n  const displayData = pagination\n    ? filteredData.slice(page * rowsPerPage, page * rowsPerPage + rowsPerPage)\n    : filteredData;\n\n  // Verifica se ci sono filtri attivi\n  const hasActiveFilters = Object.keys(activeFilters).length > 0;\n\n  return (\n    <Box>\n      {/* Mostra i filtri attivi */}\n      {hasActiveFilters && (\n        <Box sx={{ mb: 2, display: 'flex', flexWrap: 'wrap', gap: 1, alignItems: 'center' }}>\n          <Typography variant=\"body2\" sx={{ mr: 1 }}>Filtri attivi:</Typography>\n\n          {Object.entries(activeFilters).map(([columnName, filter]) => {\n            const column = columns.find(col => col.field === columnName);\n            if (!column || !filter) return null;\n\n            let label = '';\n            if (filter.filterType === 'equals') {\n              const selectedCount = filter.selectedValues?.length || 0;\n              const totalCount = data.filter(item => item[columnName] !== undefined && item[columnName] !== null).length;\n              label = `${column.headerName}: ${selectedCount} di ${totalCount}`;\n            } else if (filter.filterType === 'contains') {\n              label = `${column.headerName} contiene: ${filter.searchTerm}`;\n            } else if (filter.filterType === 'greaterThan') {\n              label = `${column.headerName} > ${filter.rangeValues.min}`;\n            } else if (filter.filterType === 'lessThan') {\n              label = `${column.headerName} < ${filter.rangeValues.max}`;\n            } else if (filter.filterType === 'between') {\n              label = `${column.headerName}: ${filter.rangeValues.min} - ${filter.rangeValues.max}`;\n            }\n\n            return (\n              <Chip\n                key={columnName}\n                label={label}\n                size=\"small\"\n                onDelete={() => removeFilter(columnName)}\n              />\n            );\n          })}\n\n          <Button\n            variant=\"text\"\n            size=\"small\"\n            startIcon={<ClearIcon />}\n            onClick={resetAllFilters}\n          >\n            Resetta tutti\n          </Button>\n        </Box>\n      )}\n\n      {/* Tabella */}\n      <TableContainer component={Paper} sx={{ mb: 2 }}>\n        <Table size=\"small\">\n          <TableHead>\n            <TableRow>\n              {columns.map((column) => (\n                <FilterableTableHeader\n                  key={column.field}\n                  columnName={column.field}\n                  label={column.headerName}\n                  data={data}\n                  onFilterChange={(filteredData, filterConfig) =>\n                    handleFilterChange(column.field, filteredData, filterConfig)\n                  }\n                  dataType={column.dataType || 'text'}\n                  sortDirection={sortConfig.key === column.field ? sortConfig.direction : null}\n                  onSortChange={handleSortChange}\n                  disableFilter={column.disableFilter}\n                  disableSort={column.disableSort}\n                  align={column.align || 'left'}\n                  sx={column.headerStyle}\n                  renderHeader={column.renderHeader}\n                />\n              ))}\n            </TableRow>\n          </TableHead>\n          <TableBody>\n            {loading ? (\n              <TableRow>\n                <td colSpan={columns.length} style={{ textAlign: 'center', padding: '20px' }}>\n                  <CircularProgress size={30} />\n                </td>\n              </TableRow>\n            ) : displayData.length > 0 ? (\n              displayData.map((row, index) => (\n                renderRow ? (\n                  renderRow(row, index)\n                ) : (\n                  <TableRow key={index}>\n                    {columns.map((column) => (\n                      <td\n                        key={column.field}\n                        style={{\n                          padding: '8px 16px',\n                          textAlign: column.align || 'left',\n                          ...(column.cellStyle || {})\n                        }}\n                      >\n                        {column.renderCell ? column.renderCell(row) : row[column.field]}\n                      </td>\n                    ))}\n                  </TableRow>\n                )\n              ))\n            ) : (\n              <TableRow>\n                <td colSpan={columns.length} style={{ textAlign: 'center', padding: '20px' }}>\n                  {emptyMessage}\n                </td>\n              </TableRow>\n            )}\n          </TableBody>\n        </Table>\n      </TableContainer>\n\n      {/* Pulsante per resettare tutti i filtri spostato nel riquadro delle statistiche */}\n\n      {/* Paginazione */}\n      {pagination && filteredData.length > 0 && (\n        <TablePagination\n          rowsPerPageOptions={[5, 10, 25, 50, 100]}\n          component=\"div\"\n          count={filteredData.length}\n          rowsPerPage={rowsPerPage}\n          page={page}\n          onPageChange={handleChangePage}\n          onRowsPerPageChange={handleChangeRowsPerPage}\n          labelRowsPerPage=\"Righe per pagina:\"\n          labelDisplayedRows={({ from, to, count }) => `${from}-${to} di ${count}`}\n        />\n      )}\n    </Box>\n  );\n};\n\nexport default FilterableTable;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAClD,SACEC,KAAK,EACLC,SAAS,EACTC,cAAc,EACdC,SAAS,EACTC,QAAQ,EACRC,KAAK,EACLC,GAAG,EACHC,UAAU,EACVC,IAAI,EACJC,MAAM,EACNC,gBAAgB,EAChBC,eAAe,QACV,eAAe;AACtB,SAASC,KAAK,IAAIC,SAAS,QAAQ,qBAAqB;AACxD,OAAOC,qBAAqB,MAAM,yBAAyB;;AAE3D;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAZA,SAAAC,MAAA,IAAAC,OAAA;AAaA,MAAMC,eAAe,GAAGA,CAAC;EACvBC,IAAI,GAAG,EAAE;EACTC,OAAO,GAAG,EAAE;EACZC,oBAAoB,GAAG,IAAI;EAC3BC,OAAO,GAAG,KAAK;EACfC,YAAY,GAAG,yBAAyB;EACxCC,SAAS,GAAG,IAAI;EAChBC,UAAU,GAAG,IAAI;EACjBC,cAAc,GAAG;AACnB,CAAC,KAAK;EAAAC,EAAA;EACJ,MAAM,CAACC,YAAY,EAAEC,eAAe,CAAC,GAAG9B,QAAQ,CAACoB,IAAI,CAAC;EACtD,MAAM,CAACW,aAAa,EAAEC,gBAAgB,CAAC,GAAGhC,QAAQ,CAAC,CAAC,CAAC,CAAC;EACtD,MAAM,CAACiC,UAAU,EAAEC,aAAa,CAAC,GAAGlC,QAAQ,CAAC;IAAEmC,GAAG,EAAE,IAAI;IAAEC,SAAS,EAAE;EAAK,CAAC,CAAC;EAC5E,MAAM,CAACC,IAAI,EAAEC,OAAO,CAAC,GAAGtC,QAAQ,CAAC,CAAC,CAAC;EACnC,MAAM,CAACuC,WAAW,EAAEC,cAAc,CAAC,GAAGxC,QAAQ,CAAC,EAAE,CAAC;;EAElD;EACAC,SAAS,CAAC,MAAM;IACd6B,eAAe,CAACV,IAAI,CAAC;EACvB,CAAC,EAAE,CAACA,IAAI,CAAC,CAAC;;EAEV;EACAnB,SAAS,CAAC,MAAM;IACd,IAAIqB,oBAAoB,EAAE;MACxBA,oBAAoB,CAACO,YAAY,CAAC;IACpC;EACF,CAAC,EAAE,CAACA,YAAY,EAAEP,oBAAoB,CAAC,CAAC;;EAExC;EACA,MAAMmB,kBAAkB,GAAGA,CAACC,UAAU,EAAEC,kBAAkB,EAAEC,YAAY,KAAK;IAC3E;IACAZ,gBAAgB,CAACa,IAAI,KAAK;MACxB,GAAGA,IAAI;MACP,CAACH,UAAU,GAAGE;IAChB,CAAC,CAAC,CAAC;;IAEH;IACA,IAAIE,MAAM,GAAG,CAAC,GAAG1B,IAAI,CAAC;;IAEtB;IACA2B,MAAM,CAACC,IAAI,CAACjB,aAAa,CAAC,CAACkB,OAAO,CAACd,GAAG,IAAI;MACxC,IAAIA,GAAG,KAAKO,UAAU,EAAE;QACtB,MAAMQ,MAAM,GAAGnB,aAAa,CAACI,GAAG,CAAC;QACjCW,MAAM,GAAGK,WAAW,CAACL,MAAM,EAAEI,MAAM,CAAC;MACtC;IACF,CAAC,CAAC;;IAEF;IACAJ,MAAM,GAAGH,kBAAkB;;IAE3B;IACA,IAAIV,UAAU,CAACE,GAAG,EAAE;MAClBW,MAAM,GAAGM,YAAY,CAACN,MAAM,EAAEb,UAAU,CAACE,GAAG,EAAEF,UAAU,CAACG,SAAS,CAAC;IACrE;IAEAN,eAAe,CAACgB,MAAM,CAAC;IACvBR,OAAO,CAAC,CAAC,CAAC,CAAC,CAAC;EACd,CAAC;;EAED;EACA,MAAMa,WAAW,GAAGA,CAACE,YAAY,EAAET,YAAY,KAAK;IAClD,IAAI,CAACA,YAAY,EAAE,OAAOS,YAAY;IAEtC,MAAM;MAAEX,UAAU;MAAEY,UAAU;MAAEC,cAAc;MAAEC,UAAU;MAAEC;IAAY,CAAC,GAAGb,YAAY;IAExF,IAAIE,MAAM,GAAG,CAAC,GAAGO,YAAY,CAAC;;IAE9B;IACA,IAAIC,UAAU,KAAK,QAAQ,IAAIC,cAAc,IAAIA,cAAc,CAACG,MAAM,GAAG,CAAC,EAAE;MAC1EZ,MAAM,GAAGA,MAAM,CAACI,MAAM,CAACS,IAAI,IAAIJ,cAAc,CAACK,QAAQ,CAACD,IAAI,CAACjB,UAAU,CAAC,CAAC,CAAC;IAC3E,CAAC,MAAM,IAAIY,UAAU,KAAK,UAAU,IAAIE,UAAU,EAAE;MAClDV,MAAM,GAAGA,MAAM,CAACI,MAAM,CAACS,IAAI,IACzBA,IAAI,CAACjB,UAAU,CAAC,IAAImB,MAAM,CAACF,IAAI,CAACjB,UAAU,CAAC,CAAC,CAACoB,WAAW,CAAC,CAAC,CAACF,QAAQ,CAACJ,UAAU,CAACM,WAAW,CAAC,CAAC,CAC9F,CAAC;IACH,CAAC,MAAM,IAAIR,UAAU,KAAK,aAAa,IAAIG,WAAW,IAAIA,WAAW,CAACM,GAAG,KAAK,EAAE,EAAE;MAChFjB,MAAM,GAAGA,MAAM,CAACI,MAAM,CAACS,IAAI,IACzBK,UAAU,CAACL,IAAI,CAACjB,UAAU,CAAC,CAAC,GAAGsB,UAAU,CAACP,WAAW,CAACM,GAAG,CAC3D,CAAC;IACH,CAAC,MAAM,IAAIT,UAAU,KAAK,UAAU,IAAIG,WAAW,IAAIA,WAAW,CAACQ,GAAG,KAAK,EAAE,EAAE;MAC7EnB,MAAM,GAAGA,MAAM,CAACI,MAAM,CAACS,IAAI,IACzBK,UAAU,CAACL,IAAI,CAACjB,UAAU,CAAC,CAAC,GAAGsB,UAAU,CAACP,WAAW,CAACQ,GAAG,CAC3D,CAAC;IACH,CAAC,MAAM,IAAIX,UAAU,KAAK,SAAS,IAAIG,WAAW,IAAIA,WAAW,CAACM,GAAG,KAAK,EAAE,IAAIN,WAAW,CAACQ,GAAG,KAAK,EAAE,EAAE;MACtGnB,MAAM,GAAGA,MAAM,CAACI,MAAM,CAACS,IAAI,IACzBK,UAAU,CAACL,IAAI,CAACjB,UAAU,CAAC,CAAC,IAAIsB,UAAU,CAACP,WAAW,CAACM,GAAG,CAAC,IAC3DC,UAAU,CAACL,IAAI,CAACjB,UAAU,CAAC,CAAC,IAAIsB,UAAU,CAACP,WAAW,CAACQ,GAAG,CAC5D,CAAC;IACH;IAEA,OAAOnB,MAAM;EACf,CAAC;;EAED;EACA,MAAMoB,gBAAgB,GAAGA,CAAC/B,GAAG,EAAEC,SAAS,KAAK;IAC3CF,aAAa,CAAC;MAAEC,GAAG;MAAEC;IAAU,CAAC,CAAC;IAEjC,MAAM+B,UAAU,GAAGf,YAAY,CAAC,CAAC,GAAGvB,YAAY,CAAC,EAAEM,GAAG,EAAEC,SAAS,CAAC;IAClEN,eAAe,CAACqC,UAAU,CAAC;EAC7B,CAAC;;EAED;EACA,MAAMf,YAAY,GAAGA,CAACgB,UAAU,EAAEjC,GAAG,EAAEC,SAAS,KAAK;IACnD,OAAOgC,UAAU,CAACC,IAAI,CAAC,CAACC,CAAC,EAAEC,CAAC,KAAK;MAC/B;MACA,IAAI,CAACD,CAAC,CAACnC,GAAG,CAAC,IAAImC,CAAC,CAACnC,GAAG,CAAC,KAAK,CAAC,EAAE,OAAOC,SAAS,KAAK,KAAK,GAAG,CAAC,GAAG,CAAC,CAAC;MAChE,IAAI,CAACmC,CAAC,CAACpC,GAAG,CAAC,IAAIoC,CAAC,CAACpC,GAAG,CAAC,KAAK,CAAC,EAAE,OAAOC,SAAS,KAAK,KAAK,GAAG,CAAC,CAAC,GAAG,CAAC;;MAEhE;MACA,MAAMoC,MAAM,GAAGnD,OAAO,CAACoD,IAAI,CAACC,GAAG,IAAIA,GAAG,CAACC,KAAK,KAAKxC,GAAG,CAAC;MACrD,MAAMyC,QAAQ,GAAG,CAAAJ,MAAM,aAANA,MAAM,uBAANA,MAAM,CAAEI,QAAQ,KAAI,MAAM;MAE3C,IAAIC,MAAM,GAAGP,CAAC,CAACnC,GAAG,CAAC;MACnB,IAAI2C,MAAM,GAAGP,CAAC,CAACpC,GAAG,CAAC;;MAEnB;MACA,IAAIyC,QAAQ,KAAK,QAAQ,EAAE;QACzBC,MAAM,GAAGb,UAAU,CAACa,MAAM,CAAC,IAAI,CAAC;QAChCC,MAAM,GAAGd,UAAU,CAACc,MAAM,CAAC,IAAI,CAAC;MAClC,CAAC,MAAM;QACLD,MAAM,GAAGhB,MAAM,CAACgB,MAAM,IAAI,EAAE,CAAC,CAACf,WAAW,CAAC,CAAC;QAC3CgB,MAAM,GAAGjB,MAAM,CAACiB,MAAM,IAAI,EAAE,CAAC,CAAChB,WAAW,CAAC,CAAC;MAC7C;;MAEA;MACA,IAAI1B,SAAS,KAAK,KAAK,EAAE;QACvB,OAAOyC,MAAM,GAAGC,MAAM,GAAG,CAAC,GAAG,CAAC,CAAC;MACjC,CAAC,MAAM;QACL,OAAOD,MAAM,GAAGC,MAAM,GAAG,CAAC,GAAG,CAAC,CAAC;MACjC;IACF,CAAC,CAAC;EACJ,CAAC;;EAED;EACA,MAAMC,eAAe,GAAGA,CAAA,KAAM;IAC5B/C,gBAAgB,CAAC,CAAC,CAAC,CAAC;IACpBE,aAAa,CAAC;MAAEC,GAAG,EAAE,IAAI;MAAEC,SAAS,EAAE;IAAK,CAAC,CAAC;IAC7CN,eAAe,CAACV,IAAI,CAAC;IACrBkB,OAAO,CAAC,CAAC,CAAC;;IAEV;IACA,IAAIX,cAAc,EAAE;MAClBA,cAAc,CAAC,CAAC;IAClB;EACF,CAAC;;EAED;EACA,MAAMqD,YAAY,GAAItC,UAAU,IAAK;IACnC,MAAMuC,UAAU,GAAG;MAAE,GAAGlD;IAAc,CAAC;IACvC,OAAOkD,UAAU,CAACvC,UAAU,CAAC;IAC7BV,gBAAgB,CAACiD,UAAU,CAAC;;IAE5B;IACA,IAAInC,MAAM,GAAG,CAAC,GAAG1B,IAAI,CAAC;IACtB2B,MAAM,CAACmC,MAAM,CAACD,UAAU,CAAC,CAAChC,OAAO,CAACC,MAAM,IAAI;MAC1CJ,MAAM,GAAGK,WAAW,CAACL,MAAM,EAAEI,MAAM,CAAC;IACtC,CAAC,CAAC;;IAEF;IACA,IAAIjB,UAAU,CAACE,GAAG,EAAE;MAClBW,MAAM,GAAGM,YAAY,CAACN,MAAM,EAAEb,UAAU,CAACE,GAAG,EAAEF,UAAU,CAACG,SAAS,CAAC;IACrE;IAEAN,eAAe,CAACgB,MAAM,CAAC;EACzB,CAAC;;EAED;EACA,MAAMqC,gBAAgB,GAAGA,CAACC,KAAK,EAAEC,OAAO,KAAK;IAC3C/C,OAAO,CAAC+C,OAAO,CAAC;EAClB,CAAC;;EAED;EACA,MAAMC,uBAAuB,GAAIF,KAAK,IAAK;IACzC5C,cAAc,CAAC+C,QAAQ,CAACH,KAAK,CAACI,MAAM,CAACC,KAAK,EAAE,EAAE,CAAC,CAAC;IAChDnD,OAAO,CAAC,CAAC,CAAC;EACZ,CAAC;;EAED;EACA,MAAMoD,WAAW,GAAGhE,UAAU,GAC1BG,YAAY,CAAC8D,KAAK,CAACtD,IAAI,GAAGE,WAAW,EAAEF,IAAI,GAAGE,WAAW,GAAGA,WAAW,CAAC,GACxEV,YAAY;;EAEhB;EACA,MAAM+D,gBAAgB,GAAG7C,MAAM,CAACC,IAAI,CAACjB,aAAa,CAAC,CAAC2B,MAAM,GAAG,CAAC;EAE9D,oBACExC,OAAA,CAACV,GAAG;IAAAqF,QAAA,GAEDD,gBAAgB,iBACf1E,OAAA,CAACV,GAAG;MAACsF,EAAE,EAAE;QAAEC,EAAE,EAAE,CAAC;QAAEC,OAAO,EAAE,MAAM;QAAEC,QAAQ,EAAE,MAAM;QAAEC,GAAG,EAAE,CAAC;QAAEC,UAAU,EAAE;MAAS,CAAE;MAAAN,QAAA,gBAClF3E,OAAA,CAACT,UAAU;QAAC2F,OAAO,EAAC,OAAO;QAACN,EAAE,EAAE;UAAEO,EAAE,EAAE;QAAE,CAAE;QAAAR,QAAA,EAAC;MAAc;QAAAS,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAY,CAAC,EAErE1D,MAAM,CAAC2D,OAAO,CAAC3E,aAAa,CAAC,CAAC4E,GAAG,CAAC,CAAC,CAACjE,UAAU,EAAEQ,MAAM,CAAC,KAAK;QAC3D,MAAMsB,MAAM,GAAGnD,OAAO,CAACoD,IAAI,CAACC,GAAG,IAAIA,GAAG,CAACC,KAAK,KAAKjC,UAAU,CAAC;QAC5D,IAAI,CAAC8B,MAAM,IAAI,CAACtB,MAAM,EAAE,OAAO,IAAI;QAEnC,IAAI0D,KAAK,GAAG,EAAE;QACd,IAAI1D,MAAM,CAACI,UAAU,KAAK,QAAQ,EAAE;UAAA,IAAAuD,qBAAA;UAClC,MAAMC,aAAa,GAAG,EAAAD,qBAAA,GAAA3D,MAAM,CAACK,cAAc,cAAAsD,qBAAA,uBAArBA,qBAAA,CAAuBnD,MAAM,KAAI,CAAC;UACxD,MAAMqD,UAAU,GAAG3F,IAAI,CAAC8B,MAAM,CAACS,IAAI,IAAIA,IAAI,CAACjB,UAAU,CAAC,KAAKsE,SAAS,IAAIrD,IAAI,CAACjB,UAAU,CAAC,KAAK,IAAI,CAAC,CAACgB,MAAM;UAC1GkD,KAAK,GAAG,GAAGpC,MAAM,CAACyC,UAAU,KAAKH,aAAa,OAAOC,UAAU,EAAE;QACnE,CAAC,MAAM,IAAI7D,MAAM,CAACI,UAAU,KAAK,UAAU,EAAE;UAC3CsD,KAAK,GAAG,GAAGpC,MAAM,CAACyC,UAAU,cAAc/D,MAAM,CAACM,UAAU,EAAE;QAC/D,CAAC,MAAM,IAAIN,MAAM,CAACI,UAAU,KAAK,aAAa,EAAE;UAC9CsD,KAAK,GAAG,GAAGpC,MAAM,CAACyC,UAAU,MAAM/D,MAAM,CAACO,WAAW,CAACM,GAAG,EAAE;QAC5D,CAAC,MAAM,IAAIb,MAAM,CAACI,UAAU,KAAK,UAAU,EAAE;UAC3CsD,KAAK,GAAG,GAAGpC,MAAM,CAACyC,UAAU,MAAM/D,MAAM,CAACO,WAAW,CAACQ,GAAG,EAAE;QAC5D,CAAC,MAAM,IAAIf,MAAM,CAACI,UAAU,KAAK,SAAS,EAAE;UAC1CsD,KAAK,GAAG,GAAGpC,MAAM,CAACyC,UAAU,KAAK/D,MAAM,CAACO,WAAW,CAACM,GAAG,MAAMb,MAAM,CAACO,WAAW,CAACQ,GAAG,EAAE;QACvF;QAEA,oBACE/C,OAAA,CAACR,IAAI;UAEHkG,KAAK,EAAEA,KAAM;UACbM,IAAI,EAAC,OAAO;UACZC,QAAQ,EAAEA,CAAA,KAAMnC,YAAY,CAACtC,UAAU;QAAE,GAHpCA,UAAU;UAAA4D,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAIhB,CAAC;MAEN,CAAC,CAAC,eAEFvF,OAAA,CAACP,MAAM;QACLyF,OAAO,EAAC,MAAM;QACdc,IAAI,EAAC,OAAO;QACZE,SAAS,eAAElG,OAAA,CAACH,SAAS;UAAAuF,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAE;QACzBY,OAAO,EAAEtC,eAAgB;QAAAc,QAAA,EAC1B;MAED;QAAAS,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAQ,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACN,CACN,eAGDvF,OAAA,CAACd,cAAc;MAACkH,SAAS,EAAE/G,KAAM;MAACuF,EAAE,EAAE;QAAEC,EAAE,EAAE;MAAE,CAAE;MAAAF,QAAA,eAC9C3E,OAAA,CAAChB,KAAK;QAACgH,IAAI,EAAC,OAAO;QAAArB,QAAA,gBACjB3E,OAAA,CAACb,SAAS;UAAAwF,QAAA,eACR3E,OAAA,CAACZ,QAAQ;YAAAuF,QAAA,EACNxE,OAAO,CAACsF,GAAG,CAAEnC,MAAM,iBAClBtD,OAAA,CAACF,qBAAqB;cAEpB0B,UAAU,EAAE8B,MAAM,CAACG,KAAM;cACzBiC,KAAK,EAAEpC,MAAM,CAACyC,UAAW;cACzB7F,IAAI,EAAEA,IAAK;cACXmG,cAAc,EAAEA,CAAC1F,YAAY,EAAEe,YAAY,KACzCH,kBAAkB,CAAC+B,MAAM,CAACG,KAAK,EAAE9C,YAAY,EAAEe,YAAY,CAC5D;cACDgC,QAAQ,EAAEJ,MAAM,CAACI,QAAQ,IAAI,MAAO;cACpC4C,aAAa,EAAEvF,UAAU,CAACE,GAAG,KAAKqC,MAAM,CAACG,KAAK,GAAG1C,UAAU,CAACG,SAAS,GAAG,IAAK;cAC7EqF,YAAY,EAAEvD,gBAAiB;cAC/BwD,aAAa,EAAElD,MAAM,CAACkD,aAAc;cACpCC,WAAW,EAAEnD,MAAM,CAACmD,WAAY;cAChCC,KAAK,EAAEpD,MAAM,CAACoD,KAAK,IAAI,MAAO;cAC9B9B,EAAE,EAAEtB,MAAM,CAACqD,WAAY;cACvBC,YAAY,EAAEtD,MAAM,CAACsD;YAAa,GAd7BtD,MAAM,CAACG,KAAK;cAAA2B,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAelB,CACF;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACM;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACF,CAAC,eACZvF,OAAA,CAACf,SAAS;UAAA0F,QAAA,EACPtE,OAAO,gBACNL,OAAA,CAACZ,QAAQ;YAAAuF,QAAA,eACP3E,OAAA;cAAI6G,OAAO,EAAE1G,OAAO,CAACqC,MAAO;cAACsE,KAAK,EAAE;gBAAEC,SAAS,EAAE,QAAQ;gBAAEC,OAAO,EAAE;cAAO,CAAE;cAAArC,QAAA,eAC3E3E,OAAA,CAACN,gBAAgB;gBAACsG,IAAI,EAAE;cAAG;gBAAAZ,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC5B;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACG,CAAC,GACTf,WAAW,CAAChC,MAAM,GAAG,CAAC,GACxBgC,WAAW,CAACiB,GAAG,CAAC,CAACwB,GAAG,EAAEC,KAAK,KACzB3G,SAAS,GACPA,SAAS,CAAC0G,GAAG,EAAEC,KAAK,CAAC,gBAErBlH,OAAA,CAACZ,QAAQ;YAAAuF,QAAA,EACNxE,OAAO,CAACsF,GAAG,CAAEnC,MAAM,iBAClBtD,OAAA;cAEE8G,KAAK,EAAE;gBACLE,OAAO,EAAE,UAAU;gBACnBD,SAAS,EAAEzD,MAAM,CAACoD,KAAK,IAAI,MAAM;gBACjC,IAAIpD,MAAM,CAAC6D,SAAS,IAAI,CAAC,CAAC;cAC5B,CAAE;cAAAxC,QAAA,EAEDrB,MAAM,CAAC8D,UAAU,GAAG9D,MAAM,CAAC8D,UAAU,CAACH,GAAG,CAAC,GAAGA,GAAG,CAAC3D,MAAM,CAACG,KAAK;YAAC,GAP1DH,MAAM,CAACG,KAAK;cAAA2B,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAQf,CACL;UAAC,GAZW2B,KAAK;YAAA9B,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAaV,CAEb,CAAC,gBAEFvF,OAAA,CAACZ,QAAQ;YAAAuF,QAAA,eACP3E,OAAA;cAAI6G,OAAO,EAAE1G,OAAO,CAACqC,MAAO;cAACsE,KAAK,EAAE;gBAAEC,SAAS,EAAE,QAAQ;gBAAEC,OAAO,EAAE;cAAO,CAAE;cAAArC,QAAA,EAC1ErE;YAAY;cAAA8E,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACX;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACG;QACX;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACQ,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACP;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACM,CAAC,EAKhB/E,UAAU,IAAIG,YAAY,CAAC6B,MAAM,GAAG,CAAC,iBACpCxC,OAAA,CAACL,eAAe;MACd0H,kBAAkB,EAAE,CAAC,CAAC,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,GAAG,CAAE;MACzCjB,SAAS,EAAC,KAAK;MACfkB,KAAK,EAAE3G,YAAY,CAAC6B,MAAO;MAC3BnB,WAAW,EAAEA,WAAY;MACzBF,IAAI,EAAEA,IAAK;MACXoG,YAAY,EAAEtD,gBAAiB;MAC/BuD,mBAAmB,EAAEpD,uBAAwB;MAC7CqD,gBAAgB,EAAC,mBAAmB;MACpCC,kBAAkB,EAAEA,CAAC;QAAEC,IAAI;QAAEC,EAAE;QAAEN;MAAM,CAAC,KAAK,GAAGK,IAAI,IAAIC,EAAE,OAAON,KAAK;IAAG;MAAAlC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAC1E,CACF;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACE,CAAC;AAEV,CAAC;AAAC7E,EAAA,CA1TIT,eAAe;AAAA4H,EAAA,GAAf5H,eAAe;AA4TrB,eAAeA,eAAe;AAAC,IAAA4H,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}