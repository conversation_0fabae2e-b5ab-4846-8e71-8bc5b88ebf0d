{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\PycharmProjects\\\\CMS\\\\webapp\\\\frontend\\\\src\\\\pages\\\\cavi\\\\ReportCaviPageNew.js\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect } from 'react';\nimport { Box, Typography, Paper, Grid, Card, CardContent, CardActions, Button, Chip, Alert, CircularProgress, Divider, IconButton, Tooltip, Dialog, DialogTitle, DialogContent, DialogActions, FormControl, InputLabel, Select, MenuItem, TextField, Accordion, AccordionSummary, AccordionDetails, Switch, FormControlLabel } from '@mui/material';\nimport { Assessment as AssessmentIcon, BarChart as BarChartIcon, PieChart as PieChartIcon, Timeline as TimelineIcon, List as ListIcon, Download as DownloadIcon, Visibility as VisibilityIcon, Refresh as RefreshIcon, ArrowBack as ArrowBackIcon, DateRange as DateRangeIcon, Cable as CableIcon, Inventory as InventoryIcon, ExpandMore as ExpandMoreIcon, ShowChart as ShowChartIcon } from '@mui/icons-material';\nimport { useNavigate, useParams } from 'react-router-dom';\nimport { useAuth } from '../../context/AuthContext';\nimport AdminHomeButton from '../../components/common/AdminHomeButton';\nimport reportService from '../../services/reportService';\nimport FilterableTable from '../../components/common/FilterableTable';\n\n// Import dei componenti grafici\nimport ProgressChart from '../../components/charts/ProgressChart';\nimport BobineChart from '../../components/charts/BobineChart';\nimport BoqChart from '../../components/charts/BoqChart';\nimport TimelineChart from '../../components/charts/TimelineChart';\nimport CaviStatoChart from '../../components/charts/CaviStatoChart';\nimport { jsxDEV as _jsxDEV, Fragment as _Fragment } from \"react/jsx-dev-runtime\";\nconst ReportCaviPageNew = () => {\n  _s();\n  const navigate = useNavigate();\n  const {\n    cantiereId\n  } = useParams();\n  const {\n    user\n  } = useAuth();\n  const [loading, setLoading] = useState(false);\n  const [error, setError] = useState(null);\n  const [reportData, setReportData] = useState(null);\n  const [selectedReport, setSelectedReport] = useState(null);\n  const [openDialog, setOpenDialog] = useState(false);\n  const [dialogType, setDialogType] = useState('');\n  const [selectedReportType, setSelectedReportType] = useState('progress');\n  const [formData, setFormData] = useState({\n    formato: 'video',\n    data_inizio: '',\n    data_fine: '',\n    id_bobina: ''\n  });\n\n  // New state to store all report data\n  const [reportsData, setReportsData] = useState({\n    progress: null,\n    boq: null,\n    bobine: null,\n    caviStato: null,\n    bobinaSpecifica: null,\n    posaPeriodo: null\n  });\n\n  // State per controllo visualizzazione grafici\n  const [showCharts, setShowCharts] = useState(true);\n\n  // Load all basic reports on component mount\n  useEffect(() => {\n    const loadAllReports = async () => {\n      setLoading(true);\n      try {\n        // Create individual promises that handle their own errors\n        const progressPromise = reportService.getProgressReport(cantiereId, 'video').catch(err => {\n          console.error('Error loading progress report:', err);\n          return {\n            content: null\n          };\n        });\n        const boqPromise = reportService.getBillOfQuantities(cantiereId, 'video').catch(err => {\n          console.error('Error loading BOQ report:', err);\n          return {\n            content: null\n          };\n        });\n        const bobinePromise = reportService.getBobineReport(cantiereId, 'video').catch(err => {\n          console.error('Error loading bobine report:', err);\n          return {\n            content: null\n          };\n        });\n        const caviStatoPromise = reportService.getCaviStatoReport(cantiereId, 'video').catch(err => {\n          console.error('Error loading cavi stato report:', err);\n          return {\n            content: null\n          };\n        });\n\n        // Wait for all promises to resolve (they won't reject due to the catch handlers)\n        const [progressData, boqData, bobineData, caviStatoData] = await Promise.all([progressPromise, boqPromise, bobinePromise, caviStatoPromise]);\n\n        // Set the data for each report, even if some are null\n        setReportsData({\n          progress: progressData.content,\n          boq: boqData.content,\n          bobine: bobineData.content,\n          caviStato: caviStatoData.content,\n          bobinaSpecifica: null,\n          posaPeriodo: null\n        });\n\n        // Only set error to null if we successfully loaded at least one report\n        if (progressData.content || boqData.content || bobineData.content || caviStatoData.content) {\n          setError(null);\n        } else {\n          setError('Errore nel caricamento dei report. Riprova più tardi.');\n        }\n      } catch (err) {\n        // This catch block should rarely be hit due to the individual error handling above\n        console.error('Unexpected error loading reports:', err);\n        setError('Errore nel caricamento dei report. Riprova più tardi.');\n      } finally {\n        setLoading(false);\n      }\n    };\n    if (cantiereId) {\n      loadAllReports();\n    }\n  }, [cantiereId]);\n\n  // Configurazione dei report disponibili\n  const reportTypes = [{\n    id: 'progress',\n    title: 'Report Avanzamento',\n    description: 'Panoramica completa dell\\'avanzamento dei lavori con metriche di performance e previsioni',\n    icon: /*#__PURE__*/_jsxDEV(AssessmentIcon, {}, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 167,\n      columnNumber: 13\n    }, this),\n    color: 'primary',\n    features: ['Metri posati vs teorici', 'Percentuale completamento', 'Previsioni timeline', 'Performance giornaliera']\n  }, {\n    id: 'boq',\n    title: 'Bill of Quantities',\n    description: 'Distinta materiali dettagliata con analisi dei consumi e disponibilità',\n    icon: /*#__PURE__*/_jsxDEV(ListIcon, {}, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 175,\n      columnNumber: 13\n    }, this),\n    color: 'secondary',\n    features: ['Materiali per tipologia', 'Consumi vs disponibilità', 'Previsioni acquisti', 'Analisi costi']\n  }, {\n    id: 'bobine',\n    title: 'Report Utilizzo Bobine',\n    description: 'Analisi completa dell\\'utilizzo delle bobine con efficienza e sprechi',\n    icon: /*#__PURE__*/_jsxDEV(InventoryIcon, {}, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 183,\n      columnNumber: 13\n    }, this),\n    color: 'success',\n    features: ['Utilizzo per bobina', 'Efficienza materiali', 'Bobine disponibili', 'Analisi sprechi']\n  }, {\n    id: 'bobina-specifica',\n    title: 'Report Bobina Specifica',\n    description: 'Dettaglio approfondito di una singola bobina con tutti i cavi associati',\n    icon: /*#__PURE__*/_jsxDEV(CableIcon, {}, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 191,\n      columnNumber: 13\n    }, this),\n    color: 'info',\n    features: ['Dettaglio bobina', 'Cavi associati', 'Utilizzo specifico', 'Storico operazioni']\n  }, {\n    id: 'posa-periodo',\n    title: 'Report Posa per Periodo',\n    description: 'Analisi temporale della posa con trend e pattern di lavoro',\n    icon: /*#__PURE__*/_jsxDEV(TimelineIcon, {}, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 199,\n      columnNumber: 13\n    }, this),\n    color: 'warning',\n    features: ['Trend temporali', 'Performance periodiche', 'Analisi stagionali', 'Produttività team']\n  }, {\n    id: 'cavi-stato',\n    title: 'Report Cavi per Stato',\n    description: 'Classificazione dei cavi per stato di installazione con statistiche dettagliate',\n    icon: /*#__PURE__*/_jsxDEV(BarChartIcon, {}, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 207,\n      columnNumber: 13\n    }, this),\n    color: 'error',\n    features: ['Cavi per stato', 'Statistiche installazione', 'Problematiche', 'Azioni richieste']\n  }];\n\n  // Nuova funzione per generare report con formato specificato\n  const generateReportWithFormat = async (reportType, format) => {\n    try {\n      setLoading(true);\n      setError(null);\n      let response;\n      switch (reportType) {\n        case 'progress':\n          response = await reportService.getProgressReport(cantiereId, format);\n          break;\n        case 'boq':\n          response = await reportService.getBillOfQuantities(cantiereId, format);\n          break;\n        case 'bobine':\n          response = await reportService.getBobineReport(cantiereId, format);\n          break;\n        case 'cavi-stato':\n          response = await reportService.getCaviStatoReport(cantiereId, format);\n          break;\n        case 'bobina-specifica':\n          if (!formData.id_bobina) {\n            setError('Inserisci l\\'ID della bobina');\n            return;\n          }\n          response = await reportService.getBobinaReport(cantiereId, formData.id_bobina, format);\n          break;\n        case 'posa-periodo':\n          if (!formData.data_inizio || !formData.data_fine) {\n            setError('Seleziona le date di inizio e fine periodo');\n            return;\n          }\n          response = await reportService.getPosaPerPeriodoReport(cantiereId, formData.data_inizio, formData.data_fine, format);\n          break;\n        default:\n          throw new Error('Tipo di report non riconosciuto');\n      }\n      if (format === 'video') {\n        // For special reports, update the specific report data\n        if (reportType === 'bobina-specifica' || reportType === 'posa-periodo') {\n          setReportsData(prev => ({\n            ...prev,\n            [reportType === 'bobina-specifica' ? 'bobinaSpecifica' : 'posaPeriodo']: response.content\n          }));\n        }\n        setReportData(response.content);\n      } else {\n        // Per PDF/Excel, apri il link di download\n        if (response.file_url) {\n          window.open(response.file_url, '_blank');\n        }\n      }\n    } catch (err) {\n      console.error('Errore nella generazione del report:', err);\n      setError(err.detail || err.message || 'Errore durante la generazione del report');\n    } finally {\n      setLoading(false);\n    }\n  };\n  const handleReportSelect = reportType => {\n    setSelectedReport(reportType);\n    setDialogType(reportType.id);\n\n    // Per report che necessitano di parametri aggiuntivi, mostra il dialog\n    if (reportType.id === 'posa-periodo' || reportType.id === 'bobina-specifica') {\n      // Imposta valori di default per alcuni report\n      if (reportType.id === 'posa-periodo') {\n        const today = new Date();\n        const lastMonth = new Date();\n        lastMonth.setMonth(today.getMonth() - 1);\n        setFormData({\n          ...formData,\n          data_inizio: lastMonth.toISOString().split('T')[0],\n          data_fine: today.toISOString().split('T')[0]\n        });\n      }\n      setOpenDialog(true);\n    } else {\n      // Per report senza parametri aggiuntivi, genera direttamente con formato 'video'\n      generateReportWithFormat(reportType.id, 'video');\n    }\n  };\n  const handleGenerateReport = async () => {\n    await generateReportWithFormat(dialogType, formData.formato);\n    setOpenDialog(false);\n  };\n  const handleCloseDialog = () => {\n    setOpenDialog(false);\n    setError(null);\n    setFormData({\n      formato: 'video',\n      data_inizio: '',\n      data_fine: '',\n      id_bobina: ''\n    });\n  };\n  const renderReportContent = () => {\n    if (!reportData) return null;\n    return /*#__PURE__*/_jsxDEV(Paper, {\n      sx: {\n        p: 3,\n        mt: 3\n      },\n      children: [/*#__PURE__*/_jsxDEV(Box, {\n        sx: {\n          display: 'flex',\n          justifyContent: 'space-between',\n          alignItems: 'center',\n          mb: 2\n        },\n        children: [/*#__PURE__*/_jsxDEV(Typography, {\n          variant: \"h6\",\n          children: [selectedReport === null || selectedReport === void 0 ? void 0 : selectedReport.title, \" - \", reportData.nome_cantiere]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 328,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Box, {\n          sx: {\n            display: 'flex',\n            gap: 1\n          },\n          children: [/*#__PURE__*/_jsxDEV(Button, {\n            startIcon: /*#__PURE__*/_jsxDEV(DownloadIcon, {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 334,\n              columnNumber: 26\n            }, this),\n            onClick: () => generateReportWithFormat(dialogType, 'pdf'),\n            variant: \"outlined\",\n            size: \"small\",\n            color: \"primary\",\n            children: \"PDF\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 333,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Button, {\n            startIcon: /*#__PURE__*/_jsxDEV(DownloadIcon, {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 343,\n              columnNumber: 26\n            }, this),\n            onClick: () => generateReportWithFormat(dialogType, 'excel'),\n            variant: \"outlined\",\n            size: \"small\",\n            color: \"success\",\n            children: \"Excel\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 342,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Button, {\n            startIcon: /*#__PURE__*/_jsxDEV(RefreshIcon, {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 352,\n              columnNumber: 26\n            }, this),\n            onClick: () => setReportData(null),\n            variant: \"outlined\",\n            size: \"small\",\n            children: \"Nuovo Report\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 351,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 331,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 327,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Divider, {\n        sx: {\n          mb: 3\n        }\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 362,\n        columnNumber: 9\n      }, this), dialogType === 'progress' && renderProgressReport(reportData), dialogType === 'boq' && renderBoqReport(reportData), dialogType === 'bobine' && renderBobineReport(reportData), dialogType === 'bobina-specifica' && renderBobinaSpecificaReport(reportData), dialogType === 'posa-periodo' && renderPosaPeriodoReport(reportData), dialogType === 'cavi-stato' && renderCaviStatoReport(reportData)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 326,\n      columnNumber: 7\n    }, this);\n  };\n  const renderProgressReport = data => /*#__PURE__*/_jsxDEV(Box, {\n    children: [/*#__PURE__*/_jsxDEV(Box, {\n      sx: {\n        display: 'flex',\n        justifyContent: 'flex-end',\n        alignItems: 'center',\n        mb: 2\n      },\n      children: /*#__PURE__*/_jsxDEV(FormControlLabel, {\n        control: /*#__PURE__*/_jsxDEV(Switch, {\n          checked: showCharts,\n          onChange: e => setShowCharts(e.target.checked),\n          color: \"primary\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 381,\n          columnNumber: 13\n        }, this),\n        label: /*#__PURE__*/_jsxDEV(Box, {\n          sx: {\n            display: 'flex',\n            alignItems: 'center'\n          },\n          children: [/*#__PURE__*/_jsxDEV(ShowChartIcon, {\n            sx: {\n              mr: 1\n            }\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 389,\n            columnNumber: 15\n          }, this), \"Grafici\"]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 388,\n          columnNumber: 13\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 379,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 378,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Paper, {\n      sx: {\n        p: 2,\n        mb: 3,\n        border: '1px solid #e0e0e0'\n      },\n      children: /*#__PURE__*/_jsxDEV(Grid, {\n        container: true,\n        spacing: 2,\n        children: [/*#__PURE__*/_jsxDEV(Grid, {\n          item: true,\n          xs: 12,\n          md: 3,\n          children: /*#__PURE__*/_jsxDEV(Box, {\n            sx: {\n              textAlign: 'center',\n              p: 1\n            },\n            children: [/*#__PURE__*/_jsxDEV(Typography, {\n              variant: \"h5\",\n              sx: {\n                fontWeight: 600,\n                color: '#2c3e50',\n                mb: 0.5\n              },\n              children: [data.metri_totali, \"m\"]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 401,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Typography, {\n              variant: \"caption\",\n              sx: {\n                color: '#666',\n                textTransform: 'uppercase',\n                letterSpacing: 0.5\n              },\n              children: \"Metri Totali\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 404,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 400,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 399,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Grid, {\n          item: true,\n          xs: 12,\n          md: 3,\n          children: /*#__PURE__*/_jsxDEV(Box, {\n            sx: {\n              textAlign: 'center',\n              p: 1\n            },\n            children: [/*#__PURE__*/_jsxDEV(Typography, {\n              variant: \"h5\",\n              sx: {\n                fontWeight: 600,\n                color: '#3498db',\n                mb: 0.5\n              },\n              children: [data.metri_posati, \"m\"]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 411,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Typography, {\n              variant: \"caption\",\n              sx: {\n                color: '#666',\n                textTransform: 'uppercase',\n                letterSpacing: 0.5\n              },\n              children: [\"Metri Posati (\", data.percentuale_avanzamento, \"%)\"]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 414,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 410,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 409,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Grid, {\n          item: true,\n          xs: 12,\n          md: 3,\n          children: /*#__PURE__*/_jsxDEV(Box, {\n            sx: {\n              textAlign: 'center',\n              p: 1\n            },\n            children: [/*#__PURE__*/_jsxDEV(Typography, {\n              variant: \"h5\",\n              sx: {\n                fontWeight: 600,\n                color: '#5d6d7e',\n                mb: 0.5\n              },\n              children: [data.metri_da_posare, \"m\"]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 421,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Typography, {\n              variant: \"caption\",\n              sx: {\n                color: '#666',\n                textTransform: 'uppercase',\n                letterSpacing: 0.5\n              },\n              children: [\"Metri Rimanenti (\", 100 - data.percentuale_avanzamento, \"%)\"]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 424,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 420,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 419,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Grid, {\n          item: true,\n          xs: 12,\n          md: 3,\n          children: /*#__PURE__*/_jsxDEV(Box, {\n            sx: {\n              textAlign: 'center',\n              p: 1\n            },\n            children: [/*#__PURE__*/_jsxDEV(Typography, {\n              variant: \"h5\",\n              sx: {\n                fontWeight: 600,\n                color: '#85929e',\n                mb: 0.5\n              },\n              children: [data.media_giornaliera, \"m\"]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 431,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Typography, {\n              variant: \"caption\",\n              sx: {\n                color: '#666',\n                textTransform: 'uppercase',\n                letterSpacing: 0.5\n              },\n              children: [\"Media/Giorno\", data.giorni_stimati && ` (${data.giorni_stimati} giorni rimasti)`]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 434,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 430,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 429,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 398,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 397,\n      columnNumber: 7\n    }, this), showCharts && /*#__PURE__*/_jsxDEV(Box, {\n      sx: {\n        mb: 4\n      },\n      children: /*#__PURE__*/_jsxDEV(ProgressChart, {\n        data: data\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 446,\n        columnNumber: 11\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 445,\n      columnNumber: 9\n    }, this), /*#__PURE__*/_jsxDEV(Paper, {\n      sx: {\n        p: 2,\n        mb: 3,\n        border: '1px solid #e0e0e0'\n      },\n      children: [/*#__PURE__*/_jsxDEV(Typography, {\n        variant: \"subtitle1\",\n        sx: {\n          fontWeight: 500,\n          mb: 2,\n          color: '#2c3e50'\n        },\n        children: \"Dettagli Cavi e Performance\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 452,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Box, {\n        sx: {\n          border: '1px solid #e0e0e0',\n          borderRadius: 1,\n          overflow: 'hidden'\n        },\n        children: /*#__PURE__*/_jsxDEV(\"table\", {\n          style: {\n            width: '100%',\n            borderCollapse: 'collapse'\n          },\n          children: [/*#__PURE__*/_jsxDEV(\"thead\", {\n            children: /*#__PURE__*/_jsxDEV(\"tr\", {\n              style: {\n                backgroundColor: '#f8f9fa'\n              },\n              children: [/*#__PURE__*/_jsxDEV(\"th\", {\n                style: {\n                  padding: '8px 12px',\n                  textAlign: 'left',\n                  fontSize: '12px',\n                  fontWeight: 600,\n                  color: '#2c3e50',\n                  borderBottom: '1px solid #e0e0e0',\n                  width: '25%'\n                },\n                children: \"Totale Cavi\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 463,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n                style: {\n                  padding: '8px 12px',\n                  textAlign: 'left',\n                  fontSize: '12px',\n                  fontWeight: 600,\n                  color: '#2c3e50',\n                  borderBottom: '1px solid #e0e0e0',\n                  width: '25%'\n                },\n                children: \"Cavi Posati\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 472,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n                style: {\n                  padding: '8px 12px',\n                  textAlign: 'left',\n                  fontSize: '12px',\n                  fontWeight: 600,\n                  color: '#2c3e50',\n                  borderBottom: '1px solid #e0e0e0',\n                  width: '25%'\n                },\n                children: \"Media Giornaliera\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 481,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n                style: {\n                  padding: '8px 12px',\n                  textAlign: 'left',\n                  fontSize: '12px',\n                  fontWeight: 600,\n                  color: '#2c3e50',\n                  borderBottom: '1px solid #e0e0e0',\n                  width: '25%'\n                },\n                children: \"Completamento Previsto\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 490,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 462,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 461,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"tbody\", {\n            children: /*#__PURE__*/_jsxDEV(\"tr\", {\n              children: [/*#__PURE__*/_jsxDEV(\"td\", {\n                style: {\n                  padding: '12px',\n                  fontSize: '13px',\n                  textAlign: 'center'\n                },\n                children: [/*#__PURE__*/_jsxDEV(Typography, {\n                  variant: \"h6\",\n                  sx: {\n                    fontWeight: 600,\n                    color: '#2c3e50',\n                    mb: 0.5\n                  },\n                  children: data.totale_cavi\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 508,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                  variant: \"caption\",\n                  sx: {\n                    color: '#666'\n                  },\n                  children: \"cavi totali\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 511,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 503,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                style: {\n                  padding: '12px',\n                  fontSize: '13px',\n                  textAlign: 'center'\n                },\n                children: [/*#__PURE__*/_jsxDEV(Typography, {\n                  variant: \"h6\",\n                  sx: {\n                    fontWeight: 600,\n                    color: '#3498db',\n                    mb: 0.5\n                  },\n                  children: data.cavi_posati\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 520,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                  variant: \"caption\",\n                  sx: {\n                    color: '#666'\n                  },\n                  children: [data.percentuale_cavi, \"% completato\"]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 523,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 515,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                style: {\n                  padding: '12px',\n                  fontSize: '13px',\n                  textAlign: 'center'\n                },\n                children: [/*#__PURE__*/_jsxDEV(Typography, {\n                  variant: \"h6\",\n                  sx: {\n                    fontWeight: 600,\n                    color: '#3498db',\n                    mb: 0.5\n                  },\n                  children: [data.media_giornaliera, \"m\"]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 532,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                  variant: \"caption\",\n                  sx: {\n                    color: '#666'\n                  },\n                  children: \"al giorno\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 535,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 527,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                style: {\n                  padding: '12px',\n                  fontSize: '13px',\n                  textAlign: 'center'\n                },\n                children: data.giorni_stimati ? /*#__PURE__*/_jsxDEV(_Fragment, {\n                  children: [/*#__PURE__*/_jsxDEV(Typography, {\n                    variant: \"body2\",\n                    sx: {\n                      fontWeight: 600,\n                      color: '#f39c12',\n                      mb: 0.5\n                    },\n                    children: data.data_completamento\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 546,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                    variant: \"caption\",\n                    sx: {\n                      color: '#666'\n                    },\n                    children: [\"(\", data.giorni_stimati, \" giorni)\"]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 549,\n                    columnNumber: 23\n                  }, this)]\n                }, void 0, true) : /*#__PURE__*/_jsxDEV(Typography, {\n                  variant: \"caption\",\n                  sx: {\n                    color: '#999'\n                  },\n                  children: \"Non disponibile\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 554,\n                  columnNumber: 21\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 539,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 502,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 501,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 460,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 455,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 451,\n      columnNumber: 7\n    }, this), data.posa_recente && data.posa_recente.length > 0 && /*#__PURE__*/_jsxDEV(Paper, {\n      sx: {\n        p: 2,\n        border: '1px solid #e0e0e0'\n      },\n      children: [/*#__PURE__*/_jsxDEV(Typography, {\n        variant: \"subtitle1\",\n        sx: {\n          fontWeight: 500,\n          mb: 2,\n          color: '#2c3e50'\n        },\n        children: \"Attivit\\xE0 Recente\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 568,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(FilterableTable, {\n        data: data.posa_recente.map(posa => ({\n          data: posa.data,\n          metri: `${posa.metri}m`\n        })),\n        columns: [{\n          field: 'data',\n          headerName: 'Data',\n          width: 200\n        }, {\n          field: 'metri',\n          headerName: 'Metri Posati',\n          width: 150,\n          align: 'right'\n        }],\n        pagination: data.posa_recente.length > 6,\n        pageSize: 6\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 571,\n        columnNumber: 11\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 567,\n      columnNumber: 9\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 376,\n    columnNumber: 5\n  }, this);\n  const renderBoqReport = data => /*#__PURE__*/_jsxDEV(Box, {\n    children: [/*#__PURE__*/_jsxDEV(Box, {\n      sx: {\n        display: 'flex',\n        justifyContent: 'space-between',\n        alignItems: 'center',\n        mb: 3\n      },\n      children: [/*#__PURE__*/_jsxDEV(Typography, {\n        variant: \"h5\",\n        sx: {\n          fontWeight: 600,\n          color: 'secondary.main'\n        },\n        children: \"Bill of Quantities - Distinta Materiali\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 592,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(FormControlLabel, {\n        control: /*#__PURE__*/_jsxDEV(Switch, {\n          checked: showCharts,\n          onChange: e => setShowCharts(e.target.checked),\n          color: \"primary\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 597,\n          columnNumber: 13\n        }, this),\n        label: /*#__PURE__*/_jsxDEV(Box, {\n          sx: {\n            display: 'flex',\n            alignItems: 'center'\n          },\n          children: [/*#__PURE__*/_jsxDEV(ShowChartIcon, {\n            sx: {\n              mr: 1\n            }\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 605,\n            columnNumber: 15\n          }, this), \"Grafici\"]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 604,\n          columnNumber: 13\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 595,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 591,\n      columnNumber: 7\n    }, this), showCharts && /*#__PURE__*/_jsxDEV(Box, {\n      sx: {\n        mb: 4\n      },\n      children: /*#__PURE__*/_jsxDEV(BoqChart, {\n        data: data\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 615,\n        columnNumber: 11\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 614,\n      columnNumber: 9\n    }, this), /*#__PURE__*/_jsxDEV(Paper, {\n      sx: {\n        p: 3,\n        mb: 3\n      },\n      children: [/*#__PURE__*/_jsxDEV(Typography, {\n        variant: \"h6\",\n        sx: {\n          mb: 2,\n          fontWeight: 600\n        },\n        children: \"Cavi per Tipologia\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 621,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(FilterableTable, {\n        data: data.cavi_per_tipo || [],\n        columns: [{\n          field: 'tipologia',\n          headerName: 'Tipologia',\n          width: 150\n        }, {\n          field: 'sezione',\n          headerName: 'Sezione',\n          width: 100\n        }, {\n          field: 'num_cavi',\n          headerName: 'Cavi',\n          width: 80,\n          align: 'right',\n          dataType: 'number'\n        }, {\n          field: 'metri_teorici',\n          headerName: 'Metri Teorici',\n          width: 120,\n          align: 'right',\n          dataType: 'number',\n          renderCell: row => `${row.metri_teorici}m`\n        }, {\n          field: 'metri_reali',\n          headerName: 'Metri Reali',\n          width: 120,\n          align: 'right',\n          dataType: 'number',\n          renderCell: row => `${row.metri_reali}m`\n        }, {\n          field: 'metri_da_posare',\n          headerName: 'Da Posare',\n          width: 120,\n          align: 'right',\n          dataType: 'number',\n          renderCell: row => `${row.metri_da_posare}m`\n        }],\n        pageSize: 10\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 624,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 620,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Paper, {\n      sx: {\n        p: 3\n      },\n      children: [/*#__PURE__*/_jsxDEV(Typography, {\n        variant: \"h6\",\n        sx: {\n          mb: 2,\n          fontWeight: 600\n        },\n        children: \"Bobine Disponibili\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 643,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(FilterableTable, {\n        data: data.bobine_per_tipo || [],\n        columns: [{\n          field: 'tipologia',\n          headerName: 'Tipologia',\n          width: 150\n        }, {\n          field: 'sezione',\n          headerName: 'Sezione',\n          width: 100\n        }, {\n          field: 'num_bobine',\n          headerName: 'Bobine',\n          width: 100,\n          align: 'right',\n          dataType: 'number'\n        }, {\n          field: 'metri_disponibili',\n          headerName: 'Metri Disponibili',\n          width: 150,\n          align: 'right',\n          dataType: 'number',\n          renderCell: row => `${row.metri_disponibili}m`\n        }],\n        pageSize: 10\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 646,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 642,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 589,\n    columnNumber: 5\n  }, this);\n  const renderBobineReport = data => /*#__PURE__*/_jsxDEV(Box, {\n    children: [/*#__PURE__*/_jsxDEV(Box, {\n      sx: {\n        display: 'flex',\n        justifyContent: 'space-between',\n        alignItems: 'center',\n        mb: 3\n      },\n      children: [/*#__PURE__*/_jsxDEV(Typography, {\n        variant: \"h5\",\n        sx: {\n          fontWeight: 600,\n          color: 'success.main'\n        },\n        children: [\"Report Utilizzo Bobine (\", data.totale_bobine, \" totali)\"]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 665,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(FormControlLabel, {\n        control: /*#__PURE__*/_jsxDEV(Switch, {\n          checked: showCharts,\n          onChange: e => setShowCharts(e.target.checked),\n          color: \"primary\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 670,\n          columnNumber: 13\n        }, this),\n        label: /*#__PURE__*/_jsxDEV(Box, {\n          sx: {\n            display: 'flex',\n            alignItems: 'center'\n          },\n          children: [/*#__PURE__*/_jsxDEV(ShowChartIcon, {\n            sx: {\n              mr: 1\n            }\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 678,\n            columnNumber: 15\n          }, this), \"Grafici\"]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 677,\n          columnNumber: 13\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 668,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 664,\n      columnNumber: 7\n    }, this), showCharts && /*#__PURE__*/_jsxDEV(Box, {\n      sx: {\n        mb: 4\n      },\n      children: /*#__PURE__*/_jsxDEV(BobineChart, {\n        data: data\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 688,\n        columnNumber: 11\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 687,\n      columnNumber: 9\n    }, this), /*#__PURE__*/_jsxDEV(Paper, {\n      sx: {\n        p: 3\n      },\n      children: [/*#__PURE__*/_jsxDEV(Typography, {\n        variant: \"h6\",\n        sx: {\n          mb: 2,\n          fontWeight: 600\n        },\n        children: \"Dettaglio Bobine del Cantiere\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 694,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(FilterableTable, {\n        data: data.bobine || [],\n        columns: [{\n          field: 'id_bobina',\n          headerName: 'ID Bobina',\n          width: 120\n        }, {\n          field: 'tipologia',\n          headerName: 'Tipologia',\n          width: 150\n        }, {\n          field: 'sezione',\n          headerName: 'Sezione',\n          width: 100\n        }, {\n          field: 'stato',\n          headerName: 'Stato',\n          width: 120,\n          renderCell: row => /*#__PURE__*/_jsxDEV(Chip, {\n            label: row.stato,\n            color: row.stato === 'DISPONIBILE' ? 'success' : 'warning',\n            size: \"small\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 705,\n            columnNumber: 17\n          }, this)\n        }, {\n          field: 'metri_totali',\n          headerName: 'Metri Totali',\n          width: 120,\n          align: 'right',\n          dataType: 'number',\n          renderCell: row => `${row.metri_totali}m`\n        }, {\n          field: 'metri_residui',\n          headerName: 'Metri Residui',\n          width: 120,\n          align: 'right',\n          dataType: 'number',\n          renderCell: row => `${row.metri_residui}m`\n        }, {\n          field: 'metri_utilizzati',\n          headerName: 'Metri Utilizzati',\n          width: 140,\n          align: 'right',\n          dataType: 'number',\n          renderCell: row => `${row.metri_utilizzati}m`\n        }, {\n          field: 'percentuale_utilizzo',\n          headerName: 'Utilizzo',\n          width: 100,\n          align: 'right',\n          dataType: 'number',\n          renderCell: row => `${row.percentuale_utilizzo}%`\n        }],\n        pageSize: 10\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 697,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 693,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 662,\n    columnNumber: 5\n  }, this);\n  const renderBobinaSpecificaReport = data => {\n    var _data$bobina;\n    return /*#__PURE__*/_jsxDEV(Box, {\n      children: [/*#__PURE__*/_jsxDEV(Typography, {\n        variant: \"h5\",\n        sx: {\n          fontWeight: 600,\n          color: 'info.main',\n          mb: 3\n        },\n        children: [\"Report Bobina Specifica - \", (_data$bobina = data.bobina) === null || _data$bobina === void 0 ? void 0 : _data$bobina.id_bobina]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 730,\n        columnNumber: 7\n      }, this), /*#__PURE__*/_jsxDEV(Grid, {\n        container: true,\n        spacing: 3,\n        children: [/*#__PURE__*/_jsxDEV(Grid, {\n          item: true,\n          xs: 12,\n          md: 6,\n          children: /*#__PURE__*/_jsxDEV(Paper, {\n            sx: {\n              p: 3\n            },\n            children: [/*#__PURE__*/_jsxDEV(Typography, {\n              variant: \"h6\",\n              sx: {\n                mb: 2,\n                fontWeight: 600\n              },\n              children: \"Dettagli Bobina\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 738,\n              columnNumber: 13\n            }, this), data.bobina && /*#__PURE__*/_jsxDEV(Box, {\n              children: [/*#__PURE__*/_jsxDEV(Box, {\n                sx: {\n                  display: 'flex',\n                  justifyContent: 'space-between',\n                  mb: 1\n                },\n                children: [/*#__PURE__*/_jsxDEV(Typography, {\n                  variant: \"body1\",\n                  children: \"ID Bobina:\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 744,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                  variant: \"body1\",\n                  sx: {\n                    fontWeight: 600\n                  },\n                  children: data.bobina.id_bobina\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 745,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 743,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(Box, {\n                sx: {\n                  display: 'flex',\n                  justifyContent: 'space-between',\n                  mb: 1\n                },\n                children: [/*#__PURE__*/_jsxDEV(Typography, {\n                  variant: \"body1\",\n                  children: \"Tipologia:\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 748,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                  variant: \"body1\",\n                  sx: {\n                    fontWeight: 600\n                  },\n                  children: data.bobina.tipologia\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 749,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 747,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(Box, {\n                sx: {\n                  display: 'flex',\n                  justifyContent: 'space-between',\n                  mb: 1\n                },\n                children: [/*#__PURE__*/_jsxDEV(Typography, {\n                  variant: \"body1\",\n                  children: \"Sezione:\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 752,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                  variant: \"body1\",\n                  sx: {\n                    fontWeight: 600\n                  },\n                  children: data.bobina.sezione\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 753,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 751,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(Box, {\n                sx: {\n                  display: 'flex',\n                  justifyContent: 'space-between',\n                  mb: 1\n                },\n                children: [/*#__PURE__*/_jsxDEV(Typography, {\n                  variant: \"body1\",\n                  children: \"Stato:\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 756,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(Chip, {\n                  label: data.bobina.stato,\n                  color: data.bobina.stato === 'DISPONIBILE' ? 'success' : 'warning',\n                  size: \"small\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 757,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 755,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 742,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 737,\n            columnNumber: 11\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 736,\n          columnNumber: 9\n        }, this), /*#__PURE__*/_jsxDEV(Grid, {\n          item: true,\n          xs: 12,\n          md: 6,\n          children: /*#__PURE__*/_jsxDEV(Paper, {\n            sx: {\n              p: 3\n            },\n            children: [/*#__PURE__*/_jsxDEV(Typography, {\n              variant: \"h6\",\n              sx: {\n                mb: 2,\n                fontWeight: 600\n              },\n              children: \"Metriche Utilizzo\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 771,\n              columnNumber: 13\n            }, this), data.bobina && /*#__PURE__*/_jsxDEV(Box, {\n              children: [/*#__PURE__*/_jsxDEV(Box, {\n                sx: {\n                  display: 'flex',\n                  justifyContent: 'space-between',\n                  mb: 1\n                },\n                children: [/*#__PURE__*/_jsxDEV(Typography, {\n                  variant: \"body1\",\n                  children: \"Metri Totali:\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 777,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                  variant: \"body1\",\n                  sx: {\n                    fontWeight: 600\n                  },\n                  children: [data.bobina.metri_totali, \"m\"]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 778,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 776,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(Box, {\n                sx: {\n                  display: 'flex',\n                  justifyContent: 'space-between',\n                  mb: 1\n                },\n                children: [/*#__PURE__*/_jsxDEV(Typography, {\n                  variant: \"body1\",\n                  children: \"Metri Utilizzati:\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 781,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                  variant: \"body1\",\n                  sx: {\n                    fontWeight: 600,\n                    color: 'success.main'\n                  },\n                  children: [data.bobina.metri_utilizzati, \"m\"]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 782,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 780,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(Box, {\n                sx: {\n                  display: 'flex',\n                  justifyContent: 'space-between',\n                  mb: 1\n                },\n                children: [/*#__PURE__*/_jsxDEV(Typography, {\n                  variant: \"body1\",\n                  children: \"Metri Residui:\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 787,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                  variant: \"body1\",\n                  sx: {\n                    fontWeight: 600,\n                    color: 'warning.main'\n                  },\n                  children: [data.bobina.metri_residui, \"m\"]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 788,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 786,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(Box, {\n                sx: {\n                  display: 'flex',\n                  justifyContent: 'space-between'\n                },\n                children: [/*#__PURE__*/_jsxDEV(Typography, {\n                  variant: \"body1\",\n                  children: \"Percentuale Utilizzo:\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 793,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                  variant: \"body1\",\n                  sx: {\n                    fontWeight: 600\n                  },\n                  children: [data.bobina.percentuale_utilizzo, \"%\"]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 794,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 792,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 775,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 770,\n            columnNumber: 11\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 769,\n          columnNumber: 9\n        }, this), /*#__PURE__*/_jsxDEV(Grid, {\n          item: true,\n          xs: 12,\n          children: /*#__PURE__*/_jsxDEV(Paper, {\n            sx: {\n              p: 3\n            },\n            children: [/*#__PURE__*/_jsxDEV(Typography, {\n              variant: \"h6\",\n              sx: {\n                mb: 2,\n                fontWeight: 600\n              },\n              children: [\"Cavi Associati (\", data.totale_cavi, \")\"]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 804,\n              columnNumber: 13\n            }, this), /*#__PURE__*/_jsxDEV(FilterableTable, {\n              data: data.cavi_associati || [],\n              columns: [{\n                field: 'id_cavo',\n                headerName: 'ID Cavo',\n                width: 120\n              }, {\n                field: 'sistema',\n                headerName: 'Sistema',\n                width: 120\n              }, {\n                field: 'utility',\n                headerName: 'Utility',\n                width: 120\n              }, {\n                field: 'tipologia',\n                headerName: 'Tipologia',\n                width: 150\n              }, {\n                field: 'metri_teorici',\n                headerName: 'Metri Teorici',\n                width: 120,\n                align: 'right',\n                dataType: 'number',\n                renderCell: row => `${row.metri_teorici}m`\n              }, {\n                field: 'metri_reali',\n                headerName: 'Metri Reali',\n                width: 120,\n                align: 'right',\n                dataType: 'number',\n                renderCell: row => `${row.metri_reali}m`\n              }, {\n                field: 'stato',\n                headerName: 'Stato',\n                width: 120,\n                renderCell: row => /*#__PURE__*/_jsxDEV(Chip, {\n                  label: row.stato,\n                  color: row.stato === 'POSATO' ? 'success' : 'warning',\n                  size: \"small\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 820,\n                  columnNumber: 21\n                }, this)\n              }],\n              pageSize: 10\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 807,\n              columnNumber: 13\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 803,\n            columnNumber: 11\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 802,\n          columnNumber: 9\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 734,\n        columnNumber: 7\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 728,\n      columnNumber: 5\n    }, this);\n  };\n  const renderPosaPeriodoReport = data => /*#__PURE__*/_jsxDEV(Box, {\n    children: [/*#__PURE__*/_jsxDEV(Box, {\n      sx: {\n        display: 'flex',\n        justifyContent: 'space-between',\n        alignItems: 'center',\n        mb: 3\n      },\n      children: [/*#__PURE__*/_jsxDEV(Typography, {\n        variant: \"h5\",\n        sx: {\n          fontWeight: 600,\n          color: 'warning.main'\n        },\n        children: \"Report Posa per Periodo\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 840,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(FormControlLabel, {\n        control: /*#__PURE__*/_jsxDEV(Switch, {\n          checked: showCharts,\n          onChange: e => setShowCharts(e.target.checked),\n          color: \"primary\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 845,\n          columnNumber: 13\n        }, this),\n        label: /*#__PURE__*/_jsxDEV(Box, {\n          sx: {\n            display: 'flex',\n            alignItems: 'center'\n          },\n          children: [/*#__PURE__*/_jsxDEV(ShowChartIcon, {\n            sx: {\n              mr: 1\n            }\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 853,\n            columnNumber: 15\n          }, this), \"Grafici\"]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 852,\n          columnNumber: 13\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 843,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 839,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Grid, {\n      container: true,\n      spacing: 3,\n      sx: {\n        mb: 4\n      },\n      children: [/*#__PURE__*/_jsxDEV(Grid, {\n        item: true,\n        xs: 12,\n        md: 3,\n        children: /*#__PURE__*/_jsxDEV(Paper, {\n          sx: {\n            p: 3,\n            textAlign: 'center',\n            bgcolor: 'warning.main',\n            color: 'white'\n          },\n          children: [/*#__PURE__*/_jsxDEV(Typography, {\n            variant: \"h4\",\n            sx: {\n              fontWeight: 'bold',\n              mb: 1\n            },\n            children: [data.totale_metri_periodo, \"m\"]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 864,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Typography, {\n            variant: \"body1\",\n            children: \"Metri Totali\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 867,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Typography, {\n            variant: \"caption\",\n            children: [data.data_inizio, \" - \", data.data_fine]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 868,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 863,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 862,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Grid, {\n        item: true,\n        xs: 12,\n        md: 3,\n        children: /*#__PURE__*/_jsxDEV(Paper, {\n          sx: {\n            p: 3,\n            textAlign: 'center',\n            bgcolor: 'info.main',\n            color: 'white'\n          },\n          children: [/*#__PURE__*/_jsxDEV(Typography, {\n            variant: \"h4\",\n            sx: {\n              fontWeight: 'bold',\n              mb: 1\n            },\n            children: data.giorni_attivi\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 873,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Typography, {\n            variant: \"body1\",\n            children: \"Giorni Attivi\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 876,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 872,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 871,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Grid, {\n        item: true,\n        xs: 12,\n        md: 3,\n        children: /*#__PURE__*/_jsxDEV(Paper, {\n          sx: {\n            p: 3,\n            textAlign: 'center',\n            bgcolor: 'success.main',\n            color: 'white'\n          },\n          children: [/*#__PURE__*/_jsxDEV(Typography, {\n            variant: \"h4\",\n            sx: {\n              fontWeight: 'bold',\n              mb: 1\n            },\n            children: [data.media_giornaliera, \"m\"]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 881,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Typography, {\n            variant: \"body1\",\n            children: \"Media/Giorno\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 884,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 880,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 879,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Grid, {\n        item: true,\n        xs: 12,\n        md: 3,\n        children: /*#__PURE__*/_jsxDEV(Paper, {\n          sx: {\n            p: 3,\n            textAlign: 'center',\n            bgcolor: 'primary.main',\n            color: 'white'\n          },\n          children: [/*#__PURE__*/_jsxDEV(Typography, {\n            variant: \"h4\",\n            sx: {\n              fontWeight: 'bold',\n              mb: 1\n            },\n            children: [Math.round(data.totale_metri_periodo / data.giorni_attivi * 7), \"m\"]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 889,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Typography, {\n            variant: \"body1\",\n            children: \"Media/Settimana\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 892,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 888,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 887,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 861,\n      columnNumber: 7\n    }, this), showCharts && /*#__PURE__*/_jsxDEV(Box, {\n      sx: {\n        mb: 4\n      },\n      children: /*#__PURE__*/_jsxDEV(TimelineChart, {\n        data: data\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 900,\n        columnNumber: 11\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 899,\n      columnNumber: 9\n    }, this), /*#__PURE__*/_jsxDEV(Paper, {\n      sx: {\n        p: 3\n      },\n      children: [/*#__PURE__*/_jsxDEV(Typography, {\n        variant: \"h6\",\n        sx: {\n          mb: 2,\n          fontWeight: 600\n        },\n        children: \"Dettaglio Posa Giornaliera\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 906,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(FilterableTable, {\n        data: data.posa_giornaliera || [],\n        columns: [{\n          field: 'data',\n          headerName: 'Data',\n          width: 200\n        }, {\n          field: 'metri',\n          headerName: 'Metri Posati',\n          width: 150,\n          align: 'right',\n          dataType: 'number',\n          renderCell: row => `${row.metri}m`\n        }],\n        pageSize: 10\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 909,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 905,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 837,\n    columnNumber: 5\n  }, this);\n  const renderCaviStatoReport = data => /*#__PURE__*/_jsxDEV(Box, {\n    children: [/*#__PURE__*/_jsxDEV(Box, {\n      sx: {\n        display: 'flex',\n        justifyContent: 'space-between',\n        alignItems: 'center',\n        mb: 3\n      },\n      children: [/*#__PURE__*/_jsxDEV(Typography, {\n        variant: \"h5\",\n        sx: {\n          fontWeight: 600,\n          color: 'error.main'\n        },\n        children: \"Report Cavi per Stato\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 926,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(FormControlLabel, {\n        control: /*#__PURE__*/_jsxDEV(Switch, {\n          checked: showCharts,\n          onChange: e => setShowCharts(e.target.checked),\n          color: \"primary\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 931,\n          columnNumber: 13\n        }, this),\n        label: /*#__PURE__*/_jsxDEV(Box, {\n          sx: {\n            display: 'flex',\n            alignItems: 'center'\n          },\n          children: [/*#__PURE__*/_jsxDEV(ShowChartIcon, {\n            sx: {\n              mr: 1\n            }\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 939,\n            columnNumber: 15\n          }, this), \"Grafici\"]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 938,\n          columnNumber: 13\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 929,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 925,\n      columnNumber: 7\n    }, this), showCharts && /*#__PURE__*/_jsxDEV(Box, {\n      sx: {\n        mb: 4\n      },\n      children: /*#__PURE__*/_jsxDEV(CaviStatoChart, {\n        data: data\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 949,\n        columnNumber: 11\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 948,\n      columnNumber: 9\n    }, this), /*#__PURE__*/_jsxDEV(Paper, {\n      sx: {\n        p: 3\n      },\n      children: [/*#__PURE__*/_jsxDEV(Typography, {\n        variant: \"h6\",\n        sx: {\n          mb: 2,\n          fontWeight: 600\n        },\n        children: \"Distribuzione Cavi per Stato di Installazione\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 955,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(FilterableTable, {\n        data: data.cavi_per_stato || [],\n        columns: [{\n          field: 'stato',\n          headerName: 'Stato',\n          width: 150,\n          renderCell: row => /*#__PURE__*/_jsxDEV(Chip, {\n            label: row.stato,\n            color: row.stato === 'Installato' ? 'success' : 'warning',\n            size: \"small\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 963,\n            columnNumber: 17\n          }, this)\n        }, {\n          field: 'num_cavi',\n          headerName: 'Numero Cavi',\n          width: 120,\n          align: 'right',\n          dataType: 'number'\n        }, {\n          field: 'metri_teorici',\n          headerName: 'Metri Teorici',\n          width: 150,\n          align: 'right',\n          dataType: 'number',\n          renderCell: row => `${row.metri_teorici}m`\n        }, {\n          field: 'metri_reali',\n          headerName: 'Metri Reali',\n          width: 150,\n          align: 'right',\n          dataType: 'number',\n          renderCell: row => `${row.metri_reali}m`\n        }],\n        pagination: false\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 958,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 954,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 923,\n    columnNumber: 5\n  }, this);\n  const renderDialog = () => /*#__PURE__*/_jsxDEV(Dialog, {\n    open: openDialog,\n    onClose: handleCloseDialog,\n    maxWidth: \"sm\",\n    fullWidth: true,\n    children: [/*#__PURE__*/_jsxDEV(DialogTitle, {\n      children: selectedReport === null || selectedReport === void 0 ? void 0 : selectedReport.title\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 984,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(DialogContent, {\n      children: [error && /*#__PURE__*/_jsxDEV(Alert, {\n        severity: \"error\",\n        sx: {\n          mb: 2\n        },\n        children: error\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 989,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(Grid, {\n        container: true,\n        spacing: 2,\n        sx: {\n          mt: 1\n        },\n        children: [/*#__PURE__*/_jsxDEV(Grid, {\n          item: true,\n          xs: 12,\n          children: /*#__PURE__*/_jsxDEV(FormControl, {\n            fullWidth: true,\n            children: [/*#__PURE__*/_jsxDEV(InputLabel, {\n              children: \"Formato\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 997,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Select, {\n              value: formData.formato,\n              label: \"Formato\",\n              onChange: e => setFormData({\n                ...formData,\n                formato: e.target.value\n              }),\n              children: [/*#__PURE__*/_jsxDEV(MenuItem, {\n                value: \"video\",\n                children: \"Visualizza a schermo\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1003,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(MenuItem, {\n                value: \"pdf\",\n                children: \"Download PDF\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1004,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(MenuItem, {\n                value: \"excel\",\n                children: \"Download Excel\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1005,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 998,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 996,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 995,\n          columnNumber: 11\n        }, this), dialogType === 'bobina-specifica' && /*#__PURE__*/_jsxDEV(Grid, {\n          item: true,\n          xs: 12,\n          children: /*#__PURE__*/_jsxDEV(TextField, {\n            fullWidth: true,\n            label: \"ID Bobina\",\n            value: formData.id_bobina,\n            onChange: e => setFormData({\n              ...formData,\n              id_bobina: e.target.value\n            }),\n            placeholder: \"Es: 1, 2, A, B...\",\n            helperText: \"Inserisci solo la parte finale dell'ID (es: 1 per C1_B1)\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 1012,\n            columnNumber: 15\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 1011,\n          columnNumber: 13\n        }, this), dialogType === 'posa-periodo' && /*#__PURE__*/_jsxDEV(_Fragment, {\n          children: [/*#__PURE__*/_jsxDEV(Grid, {\n            item: true,\n            xs: 6,\n            children: /*#__PURE__*/_jsxDEV(TextField, {\n              fullWidth: true,\n              type: \"date\",\n              label: \"Data Inizio\",\n              value: formData.data_inizio,\n              onChange: e => setFormData({\n                ...formData,\n                data_inizio: e.target.value\n              }),\n              InputLabelProps: {\n                shrink: true\n              }\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1026,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 1025,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(Grid, {\n            item: true,\n            xs: 6,\n            children: /*#__PURE__*/_jsxDEV(TextField, {\n              fullWidth: true,\n              type: \"date\",\n              label: \"Data Fine\",\n              value: formData.data_fine,\n              onChange: e => setFormData({\n                ...formData,\n                data_fine: e.target.value\n              }),\n              InputLabelProps: {\n                shrink: true\n              }\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1036,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 1035,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 994,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 987,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(DialogActions, {\n      children: [/*#__PURE__*/_jsxDEV(Button, {\n        onClick: handleCloseDialog,\n        children: \"Annulla\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 1050,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Button, {\n        onClick: handleGenerateReport,\n        variant: \"contained\",\n        disabled: loading,\n        startIcon: loading ? /*#__PURE__*/_jsxDEV(CircularProgress, {\n          size: 20\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 1055,\n          columnNumber: 32\n        }, this) : /*#__PURE__*/_jsxDEV(VisibilityIcon, {}, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 1055,\n          columnNumber: 65\n        }, this),\n        children: loading ? 'Generazione...' : 'Genera Report'\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 1051,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 1049,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 983,\n    columnNumber: 5\n  }, this);\n  return /*#__PURE__*/_jsxDEV(Box, {\n    sx: {\n      p: 3\n    },\n    children: [/*#__PURE__*/_jsxDEV(Box, {\n      sx: {\n        display: 'flex',\n        justifyContent: 'space-between',\n        alignItems: 'center',\n        mb: 3\n      },\n      children: [/*#__PURE__*/_jsxDEV(Box, {\n        sx: {\n          display: 'flex',\n          alignItems: 'center',\n          gap: 2\n        },\n        children: [/*#__PURE__*/_jsxDEV(IconButton, {\n          onClick: () => navigate(-1),\n          color: \"primary\",\n          children: /*#__PURE__*/_jsxDEV(ArrowBackIcon, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 1069,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 1068,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Typography, {\n          variant: \"h4\",\n          component: \"h1\",\n          children: \"Report\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 1071,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 1067,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(AdminHomeButton, {}, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 1075,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 1066,\n      columnNumber: 7\n    }, this), loading && /*#__PURE__*/_jsxDEV(Box, {\n      sx: {\n        display: 'flex',\n        justifyContent: 'center',\n        my: 4\n      },\n      children: /*#__PURE__*/_jsxDEV(CircularProgress, {}, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 1081,\n        columnNumber: 11\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 1080,\n      columnNumber: 9\n    }, this), /*#__PURE__*/_jsxDEV(Box, {\n      sx: {\n        mt: 3\n      },\n      children: [/*#__PURE__*/_jsxDEV(Paper, {\n        sx: {\n          mb: 3,\n          border: '1px solid #e0e0e0'\n        },\n        children: /*#__PURE__*/_jsxDEV(Box, {\n          sx: {\n            borderBottom: 1,\n            borderColor: '#e0e0e0',\n            bgcolor: '#f8f9fa'\n          },\n          children: /*#__PURE__*/_jsxDEV(Box, {\n            sx: {\n              display: 'flex',\n              flexWrap: 'wrap',\n              gap: 1,\n              p: 2\n            },\n            children: [/*#__PURE__*/_jsxDEV(Button, {\n              variant: selectedReportType === 'progress' ? 'contained' : 'outlined',\n              startIcon: /*#__PURE__*/_jsxDEV(AssessmentIcon, {}, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1093,\n                columnNumber: 28\n              }, this),\n              onClick: () => setSelectedReportType('progress'),\n              sx: {\n                bgcolor: selectedReportType === 'progress' ? '#2c3e50' : 'transparent',\n                color: selectedReportType === 'progress' ? 'white' : '#2c3e50',\n                borderColor: '#2c3e50',\n                '&:hover': {\n                  bgcolor: selectedReportType === 'progress' ? '#34495e' : '#f8f9fa',\n                  borderColor: '#2c3e50'\n                }\n              },\n              children: \"Avanzamento\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1091,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Button, {\n              variant: selectedReportType === 'boq' ? 'contained' : 'outlined',\n              startIcon: /*#__PURE__*/_jsxDEV(ListIcon, {}, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1109,\n                columnNumber: 28\n              }, this),\n              onClick: () => setSelectedReportType('boq'),\n              sx: {\n                bgcolor: selectedReportType === 'boq' ? '#2c3e50' : 'transparent',\n                color: selectedReportType === 'boq' ? 'white' : '#2c3e50',\n                borderColor: '#2c3e50',\n                '&:hover': {\n                  bgcolor: selectedReportType === 'boq' ? '#34495e' : '#f8f9fa',\n                  borderColor: '#2c3e50'\n                }\n              },\n              children: \"Bill of Quantities\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1107,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Button, {\n              variant: selectedReportType === 'bobine' ? 'contained' : 'outlined',\n              startIcon: /*#__PURE__*/_jsxDEV(InventoryIcon, {}, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1125,\n                columnNumber: 28\n              }, this),\n              onClick: () => setSelectedReportType('bobine'),\n              sx: {\n                bgcolor: selectedReportType === 'bobine' ? '#2c3e50' : 'transparent',\n                color: selectedReportType === 'bobine' ? 'white' : '#2c3e50',\n                borderColor: '#2c3e50',\n                '&:hover': {\n                  bgcolor: selectedReportType === 'bobine' ? '#34495e' : '#f8f9fa',\n                  borderColor: '#2c3e50'\n                }\n              },\n              children: \"Bobine\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1123,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Button, {\n              variant: selectedReportType === 'cavi-stato' ? 'contained' : 'outlined',\n              startIcon: /*#__PURE__*/_jsxDEV(BarChartIcon, {}, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1141,\n                columnNumber: 28\n              }, this),\n              onClick: () => setSelectedReportType('cavi-stato'),\n              sx: {\n                bgcolor: selectedReportType === 'cavi-stato' ? '#2c3e50' : 'transparent',\n                color: selectedReportType === 'cavi-stato' ? 'white' : '#2c3e50',\n                borderColor: '#2c3e50',\n                '&:hover': {\n                  bgcolor: selectedReportType === 'cavi-stato' ? '#34495e' : '#f8f9fa',\n                  borderColor: '#2c3e50'\n                }\n              },\n              children: \"Cavi per Stato\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1139,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Button, {\n              variant: selectedReportType === 'bobina-specifica' ? 'contained' : 'outlined',\n              startIcon: /*#__PURE__*/_jsxDEV(CableIcon, {}, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1157,\n                columnNumber: 28\n              }, this),\n              onClick: () => setSelectedReportType('bobina-specifica'),\n              sx: {\n                bgcolor: selectedReportType === 'bobina-specifica' ? '#2c3e50' : 'transparent',\n                color: selectedReportType === 'bobina-specifica' ? 'white' : '#2c3e50',\n                borderColor: '#2c3e50',\n                '&:hover': {\n                  bgcolor: selectedReportType === 'bobina-specifica' ? '#34495e' : '#f8f9fa',\n                  borderColor: '#2c3e50'\n                }\n              },\n              children: \"Bobina Specifica\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1155,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Button, {\n              variant: selectedReportType === 'posa-periodo' ? 'contained' : 'outlined',\n              startIcon: /*#__PURE__*/_jsxDEV(TimelineIcon, {}, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1173,\n                columnNumber: 28\n              }, this),\n              onClick: () => setSelectedReportType('posa-periodo'),\n              sx: {\n                bgcolor: selectedReportType === 'posa-periodo' ? '#2c3e50' : 'transparent',\n                color: selectedReportType === 'posa-periodo' ? 'white' : '#2c3e50',\n                borderColor: '#2c3e50',\n                '&:hover': {\n                  bgcolor: selectedReportType === 'posa-periodo' ? '#34495e' : '#f8f9fa',\n                  borderColor: '#2c3e50'\n                }\n              },\n              children: \"Posa per Periodo\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1171,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 1090,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 1089,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 1088,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Box, {\n        sx: {\n          minHeight: '400px'\n        },\n        children: [selectedReportType === 'progress' && /*#__PURE__*/_jsxDEV(Paper, {\n          sx: {\n            p: 3\n          },\n          children: reportsData.progress ? /*#__PURE__*/_jsxDEV(Box, {\n            children: [/*#__PURE__*/_jsxDEV(Box, {\n              sx: {\n                display: 'flex',\n                justifyContent: 'flex-end',\n                mb: 2\n              },\n              children: [/*#__PURE__*/_jsxDEV(Button, {\n                startIcon: /*#__PURE__*/_jsxDEV(DownloadIcon, {}, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1200,\n                  columnNumber: 34\n                }, this),\n                onClick: () => generateReportWithFormat('progress', 'pdf'),\n                variant: \"outlined\",\n                size: \"small\",\n                color: \"primary\",\n                sx: {\n                  mr: 1\n                },\n                children: \"PDF\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1199,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(Button, {\n                startIcon: /*#__PURE__*/_jsxDEV(DownloadIcon, {}, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1210,\n                  columnNumber: 34\n                }, this),\n                onClick: () => generateReportWithFormat('progress', 'excel'),\n                variant: \"outlined\",\n                size: \"small\",\n                color: \"success\",\n                children: \"Excel\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1209,\n                columnNumber: 21\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 1198,\n              columnNumber: 19\n            }, this), renderProgressReport(reportsData.progress)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 1197,\n            columnNumber: 17\n          }, this) : loading ? /*#__PURE__*/_jsxDEV(Box, {\n            sx: {\n              display: 'flex',\n              alignItems: 'center',\n              justifyContent: 'center',\n              my: 4\n            },\n            children: [/*#__PURE__*/_jsxDEV(CircularProgress, {\n              size: 24\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1223,\n              columnNumber: 19\n            }, this), /*#__PURE__*/_jsxDEV(Typography, {\n              sx: {\n                ml: 2\n              },\n              children: \"Caricamento in corso...\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1224,\n              columnNumber: 19\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 1222,\n            columnNumber: 17\n          }, this) : /*#__PURE__*/_jsxDEV(Box, {\n            sx: {\n              textAlign: 'center',\n              my: 4\n            },\n            children: [/*#__PURE__*/_jsxDEV(Alert, {\n              severity: \"error\",\n              sx: {\n                mb: 2\n              },\n              children: \"Impossibile caricare il report. Riprova pi\\xF9 tardi.\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1228,\n              columnNumber: 19\n            }, this), /*#__PURE__*/_jsxDEV(Button, {\n              variant: \"outlined\",\n              startIcon: /*#__PURE__*/_jsxDEV(RefreshIcon, {}, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1233,\n                columnNumber: 32\n              }, this),\n              onClick: () => {\n                setLoading(true);\n                reportService.getProgressReport(cantiereId, 'video').then(data => {\n                  setReportsData(prev => ({\n                    ...prev,\n                    progress: data.content\n                  }));\n                }).catch(err => {\n                  console.error('Error retrying progress report:', err);\n                }).finally(() => {\n                  setLoading(false);\n                });\n              },\n              children: \"Riprova\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1231,\n              columnNumber: 19\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 1227,\n            columnNumber: 17\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 1195,\n          columnNumber: 13\n        }, this), selectedReportType === 'boq' && /*#__PURE__*/_jsxDEV(Paper, {\n          sx: {\n            p: 3\n          },\n          children: reportsData.boq ? /*#__PURE__*/_jsxDEV(Box, {\n            children: [/*#__PURE__*/_jsxDEV(Box, {\n              sx: {\n                display: 'flex',\n                justifyContent: 'flex-end',\n                mb: 2\n              },\n              children: [/*#__PURE__*/_jsxDEV(Button, {\n                startIcon: /*#__PURE__*/_jsxDEV(DownloadIcon, {}, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1265,\n                  columnNumber: 34\n                }, this),\n                onClick: () => generateReportWithFormat('boq', 'pdf'),\n                variant: \"outlined\",\n                size: \"small\",\n                color: \"primary\",\n                sx: {\n                  mr: 1\n                },\n                children: \"PDF\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1264,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(Button, {\n                startIcon: /*#__PURE__*/_jsxDEV(DownloadIcon, {}, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1275,\n                  columnNumber: 34\n                }, this),\n                onClick: () => generateReportWithFormat('boq', 'excel'),\n                variant: \"outlined\",\n                size: \"small\",\n                color: \"success\",\n                children: \"Excel\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1274,\n                columnNumber: 21\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 1263,\n              columnNumber: 19\n            }, this), renderBoqReport(reportsData.boq)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 1262,\n            columnNumber: 17\n          }, this) : loading ? /*#__PURE__*/_jsxDEV(Box, {\n            sx: {\n              display: 'flex',\n              alignItems: 'center',\n              justifyContent: 'center',\n              my: 4\n            },\n            children: [/*#__PURE__*/_jsxDEV(CircularProgress, {\n              size: 24\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1288,\n              columnNumber: 19\n            }, this), /*#__PURE__*/_jsxDEV(Typography, {\n              sx: {\n                ml: 2\n              },\n              children: \"Caricamento in corso...\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1289,\n              columnNumber: 19\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 1287,\n            columnNumber: 17\n          }, this) : /*#__PURE__*/_jsxDEV(Box, {\n            sx: {\n              textAlign: 'center',\n              my: 4\n            },\n            children: [/*#__PURE__*/_jsxDEV(Alert, {\n              severity: \"error\",\n              sx: {\n                mb: 2\n              },\n              children: \"Impossibile caricare il report. Riprova pi\\xF9 tardi.\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1293,\n              columnNumber: 19\n            }, this), /*#__PURE__*/_jsxDEV(Button, {\n              variant: \"outlined\",\n              startIcon: /*#__PURE__*/_jsxDEV(RefreshIcon, {}, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1298,\n                columnNumber: 32\n              }, this),\n              onClick: () => {\n                setLoading(true);\n                reportService.getBillOfQuantities(cantiereId, 'video').then(data => {\n                  setReportsData(prev => ({\n                    ...prev,\n                    boq: data.content\n                  }));\n                }).catch(err => {\n                  console.error('Error retrying BOQ report:', err);\n                }).finally(() => {\n                  setLoading(false);\n                });\n              },\n              children: \"Riprova\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1296,\n              columnNumber: 19\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 1292,\n            columnNumber: 17\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 1260,\n          columnNumber: 13\n        }, this), selectedReportType === 'bobine' && /*#__PURE__*/_jsxDEV(Paper, {\n          sx: {\n            p: 3\n          },\n          children: reportsData.bobine ? /*#__PURE__*/_jsxDEV(Box, {\n            children: [/*#__PURE__*/_jsxDEV(Box, {\n              sx: {\n                display: 'flex',\n                justifyContent: 'flex-end',\n                mb: 2\n              },\n              children: [/*#__PURE__*/_jsxDEV(Button, {\n                startIcon: /*#__PURE__*/_jsxDEV(DownloadIcon, {}, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1330,\n                  columnNumber: 34\n                }, this),\n                onClick: () => generateReportWithFormat('bobine', 'pdf'),\n                variant: \"outlined\",\n                size: \"small\",\n                color: \"primary\",\n                sx: {\n                  mr: 1\n                },\n                children: \"PDF\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1329,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(Button, {\n                startIcon: /*#__PURE__*/_jsxDEV(DownloadIcon, {}, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1340,\n                  columnNumber: 34\n                }, this),\n                onClick: () => generateReportWithFormat('bobine', 'excel'),\n                variant: \"outlined\",\n                size: \"small\",\n                color: \"success\",\n                children: \"Excel\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1339,\n                columnNumber: 21\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 1328,\n              columnNumber: 19\n            }, this), renderBobineReport(reportsData.bobine)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 1327,\n            columnNumber: 17\n          }, this) : loading ? /*#__PURE__*/_jsxDEV(Box, {\n            sx: {\n              display: 'flex',\n              alignItems: 'center',\n              justifyContent: 'center',\n              my: 4\n            },\n            children: [/*#__PURE__*/_jsxDEV(CircularProgress, {\n              size: 24\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1353,\n              columnNumber: 19\n            }, this), /*#__PURE__*/_jsxDEV(Typography, {\n              sx: {\n                ml: 2\n              },\n              children: \"Caricamento in corso...\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1354,\n              columnNumber: 19\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 1352,\n            columnNumber: 17\n          }, this) : /*#__PURE__*/_jsxDEV(Box, {\n            sx: {\n              textAlign: 'center',\n              my: 4\n            },\n            children: [/*#__PURE__*/_jsxDEV(Alert, {\n              severity: \"error\",\n              sx: {\n                mb: 2\n              },\n              children: \"Impossibile caricare il report. Riprova pi\\xF9 tardi.\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1358,\n              columnNumber: 19\n            }, this), /*#__PURE__*/_jsxDEV(Button, {\n              variant: \"outlined\",\n              startIcon: /*#__PURE__*/_jsxDEV(RefreshIcon, {}, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1363,\n                columnNumber: 32\n              }, this),\n              onClick: () => {\n                setLoading(true);\n                reportService.getBobineReport(cantiereId, 'video').then(data => {\n                  setReportsData(prev => ({\n                    ...prev,\n                    bobine: data.content\n                  }));\n                }).catch(err => {\n                  console.error('Error retrying bobine report:', err);\n                }).finally(() => {\n                  setLoading(false);\n                });\n              },\n              children: \"Riprova\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1361,\n              columnNumber: 19\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 1357,\n            columnNumber: 17\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 1325,\n          columnNumber: 13\n        }, this), selectedReportType === 'cavi-stato' && /*#__PURE__*/_jsxDEV(Paper, {\n          sx: {\n            p: 3\n          },\n          children: reportsData.caviStato ? /*#__PURE__*/_jsxDEV(Box, {\n            children: [/*#__PURE__*/_jsxDEV(Box, {\n              sx: {\n                display: 'flex',\n                justifyContent: 'flex-end',\n                mb: 2\n              },\n              children: [/*#__PURE__*/_jsxDEV(Button, {\n                startIcon: /*#__PURE__*/_jsxDEV(DownloadIcon, {}, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1395,\n                  columnNumber: 34\n                }, this),\n                onClick: () => generateReportWithFormat('cavi-stato', 'pdf'),\n                variant: \"outlined\",\n                size: \"small\",\n                color: \"primary\",\n                sx: {\n                  mr: 1\n                },\n                children: \"PDF\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1394,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(Button, {\n                startIcon: /*#__PURE__*/_jsxDEV(DownloadIcon, {}, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1405,\n                  columnNumber: 34\n                }, this),\n                onClick: () => generateReportWithFormat('cavi-stato', 'excel'),\n                variant: \"outlined\",\n                size: \"small\",\n                color: \"success\",\n                children: \"Excel\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1404,\n                columnNumber: 21\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 1393,\n              columnNumber: 19\n            }, this), renderCaviStatoReport(reportsData.caviStato)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 1392,\n            columnNumber: 17\n          }, this) : loading ? /*#__PURE__*/_jsxDEV(Box, {\n            sx: {\n              display: 'flex',\n              alignItems: 'center',\n              justifyContent: 'center',\n              my: 4\n            },\n            children: [/*#__PURE__*/_jsxDEV(CircularProgress, {\n              size: 24\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1418,\n              columnNumber: 19\n            }, this), /*#__PURE__*/_jsxDEV(Typography, {\n              sx: {\n                ml: 2\n              },\n              children: \"Caricamento in corso...\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1419,\n              columnNumber: 19\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 1417,\n            columnNumber: 17\n          }, this) : /*#__PURE__*/_jsxDEV(Box, {\n            sx: {\n              textAlign: 'center',\n              my: 4\n            },\n            children: [/*#__PURE__*/_jsxDEV(Alert, {\n              severity: \"error\",\n              sx: {\n                mb: 2\n              },\n              children: \"Impossibile caricare il report. Riprova pi\\xF9 tardi.\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1423,\n              columnNumber: 19\n            }, this), /*#__PURE__*/_jsxDEV(Button, {\n              variant: \"outlined\",\n              startIcon: /*#__PURE__*/_jsxDEV(RefreshIcon, {}, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1428,\n                columnNumber: 32\n              }, this),\n              onClick: () => {\n                setLoading(true);\n                reportService.getCaviStatoReport(cantiereId, 'video').then(data => {\n                  setReportsData(prev => ({\n                    ...prev,\n                    caviStato: data.content\n                  }));\n                }).catch(err => {\n                  console.error('Error retrying cavi stato report:', err);\n                }).finally(() => {\n                  setLoading(false);\n                });\n              },\n              children: \"Riprova\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1426,\n              columnNumber: 19\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 1422,\n            columnNumber: 17\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 1390,\n          columnNumber: 13\n        }, this), selectedReportType === 'bobina-specifica' && /*#__PURE__*/_jsxDEV(Paper, {\n          sx: {\n            p: 3\n          },\n          children: reportsData.bobinaSpecifica ? /*#__PURE__*/_jsxDEV(Box, {\n            children: [/*#__PURE__*/_jsxDEV(Box, {\n              sx: {\n                display: 'flex',\n                justifyContent: 'flex-end',\n                mb: 2\n              },\n              children: [/*#__PURE__*/_jsxDEV(Button, {\n                startIcon: /*#__PURE__*/_jsxDEV(DownloadIcon, {}, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1460,\n                  columnNumber: 34\n                }, this),\n                onClick: () => generateReportWithFormat('bobina-specifica', 'pdf'),\n                variant: \"outlined\",\n                size: \"small\",\n                color: \"primary\",\n                sx: {\n                  mr: 1\n                },\n                children: \"PDF\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1459,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(Button, {\n                startIcon: /*#__PURE__*/_jsxDEV(DownloadIcon, {}, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1470,\n                  columnNumber: 34\n                }, this),\n                onClick: () => generateReportWithFormat('bobina-specifica', 'excel'),\n                variant: \"outlined\",\n                size: \"small\",\n                color: \"success\",\n                children: \"Excel\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1469,\n                columnNumber: 21\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 1458,\n              columnNumber: 19\n            }, this), renderBobinaSpecificaReport(reportsData.bobinaSpecifica)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 1457,\n            columnNumber: 17\n          }, this) : /*#__PURE__*/_jsxDEV(Box, {\n            sx: {\n              textAlign: 'center',\n              my: 4\n            },\n            children: [/*#__PURE__*/_jsxDEV(Typography, {\n              variant: \"h6\",\n              sx: {\n                mb: 2\n              },\n              children: \"Report Bobina Specifica\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1483,\n              columnNumber: 19\n            }, this), /*#__PURE__*/_jsxDEV(Typography, {\n              variant: \"body2\",\n              color: \"text.secondary\",\n              sx: {\n                mb: 3\n              },\n              children: \"Seleziona una bobina per generare il report dettagliato con tutti i cavi associati.\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1486,\n              columnNumber: 19\n            }, this), /*#__PURE__*/_jsxDEV(Button, {\n              variant: \"contained\",\n              color: \"info\",\n              startIcon: /*#__PURE__*/_jsxDEV(CableIcon, {}, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1492,\n                columnNumber: 32\n              }, this),\n              onClick: () => {\n                setDialogType('bobina-specifica');\n                setOpenDialog(true);\n              },\n              children: \"Seleziona Bobina\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1489,\n              columnNumber: 19\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 1482,\n            columnNumber: 17\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 1455,\n          columnNumber: 13\n        }, this), selectedReportType === 'posa-periodo' && /*#__PURE__*/_jsxDEV(Paper, {\n          sx: {\n            p: 3\n          },\n          children: reportsData.posaPeriodo ? /*#__PURE__*/_jsxDEV(Box, {\n            children: [/*#__PURE__*/_jsxDEV(Box, {\n              sx: {\n                display: 'flex',\n                justifyContent: 'flex-end',\n                mb: 2\n              },\n              children: [/*#__PURE__*/_jsxDEV(Button, {\n                startIcon: /*#__PURE__*/_jsxDEV(DownloadIcon, {}, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1512,\n                  columnNumber: 34\n                }, this),\n                onClick: () => generateReportWithFormat('posa-periodo', 'pdf'),\n                variant: \"outlined\",\n                size: \"small\",\n                color: \"primary\",\n                sx: {\n                  mr: 1\n                },\n                children: \"PDF\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1511,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(Button, {\n                startIcon: /*#__PURE__*/_jsxDEV(DownloadIcon, {}, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1522,\n                  columnNumber: 34\n                }, this),\n                onClick: () => generateReportWithFormat('posa-periodo', 'excel'),\n                variant: \"outlined\",\n                size: \"small\",\n                color: \"success\",\n                children: \"Excel\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1521,\n                columnNumber: 21\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 1510,\n              columnNumber: 19\n            }, this), renderPosaPeriodoReport(reportsData.posaPeriodo)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 1509,\n            columnNumber: 17\n          }, this) : /*#__PURE__*/_jsxDEV(Box, {\n            sx: {\n              textAlign: 'center',\n              my: 4\n            },\n            children: [/*#__PURE__*/_jsxDEV(Typography, {\n              variant: \"h6\",\n              sx: {\n                mb: 2\n              },\n              children: \"Report Posa per Periodo\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1535,\n              columnNumber: 19\n            }, this), /*#__PURE__*/_jsxDEV(Typography, {\n              variant: \"body2\",\n              color: \"text.secondary\",\n              sx: {\n                mb: 3\n              },\n              children: \"Seleziona un periodo per analizzare i trend e pattern di posa dei cavi.\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1538,\n              columnNumber: 19\n            }, this), /*#__PURE__*/_jsxDEV(Button, {\n              variant: \"contained\",\n              color: \"warning\",\n              startIcon: /*#__PURE__*/_jsxDEV(TimelineIcon, {}, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1544,\n                columnNumber: 32\n              }, this),\n              onClick: () => {\n                setDialogType('posa-periodo');\n                // Set default date range (last month to today)\n                const today = new Date();\n                const lastMonth = new Date();\n                lastMonth.setMonth(today.getMonth() - 1);\n                setFormData({\n                  ...formData,\n                  data_inizio: lastMonth.toISOString().split('T')[0],\n                  data_fine: today.toISOString().split('T')[0]\n                });\n                setOpenDialog(true);\n              },\n              children: \"Seleziona Periodo\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1541,\n              columnNumber: 19\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 1534,\n            columnNumber: 17\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 1507,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 1192,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 1086,\n      columnNumber: 7\n    }, this), renderDialog()]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 1064,\n    columnNumber: 5\n  }, this);\n};\n_s(ReportCaviPageNew, \"A7Tak7ZNqlgfHityJ1Jz8L67TXo=\", false, function () {\n  return [useNavigate, useParams, useAuth];\n});\n_c = ReportCaviPageNew;\nexport default ReportCaviPageNew;\nvar _c;\n$RefreshReg$(_c, \"ReportCaviPageNew\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "Box", "Typography", "Paper", "Grid", "Card", "<PERSON><PERSON><PERSON><PERSON>", "CardActions", "<PERSON><PERSON>", "Chip", "<PERSON><PERSON>", "CircularProgress", "Divider", "IconButton", "<PERSON><PERSON><PERSON>", "Dialog", "DialogTitle", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "DialogActions", "FormControl", "InputLabel", "Select", "MenuItem", "TextField", "Accordion", "AccordionSummary", "AccordionDetails", "Switch", "FormControlLabel", "Assessment", "AssessmentIcon", "<PERSON><PERSON><PERSON>", "BarChartIcon", "<PERSON><PERSON><PERSON>", "PieChartIcon", "Timeline", "TimelineIcon", "List", "ListIcon", "Download", "DownloadIcon", "Visibility", "VisibilityIcon", "Refresh", "RefreshIcon", "ArrowBack", "ArrowBackIcon", "DateRange", "DateRangeIcon", "Cable", "CableIcon", "Inventory", "InventoryIcon", "ExpandMore", "ExpandMoreIcon", "ShowChart", "ShowChartIcon", "useNavigate", "useParams", "useAuth", "AdminHomeButton", "reportService", "FilterableTable", "ProgressChart", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "TimelineChart", "Cavi<PERSON>tato<PERSON>hart", "jsxDEV", "_jsxDEV", "Fragment", "_Fragment", "ReportCaviPageNew", "_s", "navigate", "cantiereId", "user", "loading", "setLoading", "error", "setError", "reportData", "setReportData", "selectedReport", "setSelectedReport", "openDialog", "setOpenDialog", "dialogType", "setDialogType", "selectedReportType", "setSelectedReportType", "formData", "setFormData", "formato", "data_inizio", "data_fine", "id_bobina", "reportsData", "setReportsData", "progress", "boq", "bobine", "caviStato", "bobinaSpecifica", "posaPeriodo", "show<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "loadAllReports", "progressPromise", "getProgressReport", "catch", "err", "console", "content", "boq<PERSON><PERSON><PERSON>", "getBillOfQuantities", "bob<PERSON><PERSON><PERSON><PERSON>", "getBobineReport", "caviStatoPromise", "getCaviStatoReport", "progressData", "boqData", "bobine<PERSON><PERSON>", "caviStatoData", "Promise", "all", "reportTypes", "id", "title", "description", "icon", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "color", "features", "generateReportWithFormat", "reportType", "format", "response", "getBobinaReport", "getPosaPerPeriodoReport", "Error", "prev", "file_url", "window", "open", "detail", "message", "handleReportSelect", "today", "Date", "lastM<PERSON>h", "setMonth", "getMonth", "toISOString", "split", "handleGenerateReport", "handleCloseDialog", "renderReportContent", "sx", "p", "mt", "children", "display", "justifyContent", "alignItems", "mb", "variant", "nome_cantiere", "gap", "startIcon", "onClick", "size", "renderProgressReport", "renderBoqReport", "renderBobineReport", "renderBobinaSpecificaReport", "renderPosaPeriodoReport", "renderCaviStatoReport", "data", "control", "checked", "onChange", "e", "target", "label", "mr", "border", "container", "spacing", "item", "xs", "md", "textAlign", "fontWeight", "metri_totali", "textTransform", "letterSpacing", "metri_posati", "percentuale_avanzamento", "metri_da_posare", "media_giornaliera", "giorni_stimati", "borderRadius", "overflow", "style", "width", "borderCollapse", "backgroundColor", "padding", "fontSize", "borderBottom", "totale_cavi", "cavi_posati", "percentuale_cavi", "data_completamento", "posa_recente", "length", "map", "posa", "metri", "columns", "field", "headerName", "align", "pagination", "pageSize", "cavi_per_tipo", "dataType", "renderCell", "row", "metri_te<PERSON>ci", "metri_reali", "bobine_per_tipo", "metri_disponibili", "totale_bobine", "stato", "metri_residui", "<PERSON><PERSON>_util<PERSON><PERSON><PERSON>", "percentuale_utilizzo", "_data$bobina", "bobina", "tipologia", "sezione", "cavi_associati", "bgcolor", "totale_metri_periodo", "giorni_attivi", "Math", "round", "posa_giornal<PERSON>", "cavi_per_stato", "renderDialog", "onClose", "max<PERSON><PERSON><PERSON>", "fullWidth", "severity", "value", "placeholder", "helperText", "type", "InputLabelProps", "shrink", "disabled", "component", "my", "borderColor", "flexWrap", "minHeight", "ml", "then", "finally", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/PycharmProjects/CMS/webapp/frontend/src/pages/cavi/ReportCaviPageNew.js"], "sourcesContent": ["import React, { useState, useEffect } from 'react';\nimport {\n  Box,\n  Typography,\n  Paper,\n  Grid,\n  Card,\n  CardContent,\n  CardActions,\n  Button,\n  Chip,\n  Alert,\n  CircularProgress,\n  Divider,\n  IconButton,\n  Tooltip,\n  Dialog,\n  DialogTitle,\n  DialogContent,\n  DialogActions,\n  FormControl,\n  InputLabel,\n  Select,\n  MenuItem,\n  TextField,\n  Accordion,\n  AccordionSummary,\n  AccordionDetails,\n  Switch,\n  FormControlLabel\n} from '@mui/material';\nimport {\n  Assessment as AssessmentIcon,\n  BarChart as BarChartIcon,\n  <PERSON><PERSON><PERSON> as PieChartIcon,\n  Timeline as TimelineIcon,\n  List as ListIcon,\n  Download as DownloadIcon,\n  Visibility as VisibilityIcon,\n  Refresh as RefreshIcon,\n  ArrowBack as ArrowBackIcon,\n  DateRange as DateRangeIcon,\n  Cable as CableIcon,\n  Inventory as InventoryIcon,\n  ExpandMore as ExpandMoreIcon,\n  ShowChart as ShowChartIcon\n} from '@mui/icons-material';\nimport { useNavigate, useParams } from 'react-router-dom';\nimport { useAuth } from '../../context/AuthContext';\nimport AdminHomeButton from '../../components/common/AdminHomeButton';\nimport reportService from '../../services/reportService';\nimport FilterableTable from '../../components/common/FilterableTable';\n\n// Import dei componenti grafici\nimport ProgressChart from '../../components/charts/ProgressChart';\nimport BobineChart from '../../components/charts/BobineChart';\nimport BoqChart from '../../components/charts/BoqChart';\nimport TimelineChart from '../../components/charts/TimelineChart';\nimport CaviStatoChart from '../../components/charts/CaviStatoChart';\n\nconst ReportCaviPageNew = () => {\n  const navigate = useNavigate();\n  const { cantiereId } = useParams();\n  const { user } = useAuth();\n\n  const [loading, setLoading] = useState(false);\n  const [error, setError] = useState(null);\n  const [reportData, setReportData] = useState(null);\n  const [selectedReport, setSelectedReport] = useState(null);\n  const [openDialog, setOpenDialog] = useState(false);\n  const [dialogType, setDialogType] = useState('');\n  const [selectedReportType, setSelectedReportType] = useState('progress');\n  const [formData, setFormData] = useState({\n    formato: 'video',\n    data_inizio: '',\n    data_fine: '',\n    id_bobina: ''\n  });\n\n  // New state to store all report data\n  const [reportsData, setReportsData] = useState({\n    progress: null,\n    boq: null,\n    bobine: null,\n    caviStato: null,\n    bobinaSpecifica: null,\n    posaPeriodo: null\n  });\n\n  // State per controllo visualizzazione grafici\n  const [showCharts, setShowCharts] = useState(true);\n\n  // Load all basic reports on component mount\n  useEffect(() => {\n    const loadAllReports = async () => {\n      setLoading(true);\n      try {\n        // Create individual promises that handle their own errors\n        const progressPromise = reportService.getProgressReport(cantiereId, 'video')\n          .catch(err => {\n            console.error('Error loading progress report:', err);\n            return { content: null };\n          });\n\n        const boqPromise = reportService.getBillOfQuantities(cantiereId, 'video')\n          .catch(err => {\n            console.error('Error loading BOQ report:', err);\n            return { content: null };\n          });\n\n        const bobinePromise = reportService.getBobineReport(cantiereId, 'video')\n          .catch(err => {\n            console.error('Error loading bobine report:', err);\n            return { content: null };\n          });\n\n        const caviStatoPromise = reportService.getCaviStatoReport(cantiereId, 'video')\n          .catch(err => {\n            console.error('Error loading cavi stato report:', err);\n            return { content: null };\n          });\n\n        // Wait for all promises to resolve (they won't reject due to the catch handlers)\n        const [progressData, boqData, bobineData, caviStatoData] = await Promise.all([\n          progressPromise,\n          boqPromise,\n          bobinePromise,\n          caviStatoPromise\n        ]);\n\n        // Set the data for each report, even if some are null\n        setReportsData({\n          progress: progressData.content,\n          boq: boqData.content,\n          bobine: bobineData.content,\n          caviStato: caviStatoData.content,\n          bobinaSpecifica: null,\n          posaPeriodo: null\n        });\n\n        // Only set error to null if we successfully loaded at least one report\n        if (progressData.content || boqData.content || bobineData.content || caviStatoData.content) {\n          setError(null);\n        } else {\n          setError('Errore nel caricamento dei report. Riprova più tardi.');\n        }\n      } catch (err) {\n        // This catch block should rarely be hit due to the individual error handling above\n        console.error('Unexpected error loading reports:', err);\n        setError('Errore nel caricamento dei report. Riprova più tardi.');\n      } finally {\n        setLoading(false);\n      }\n    };\n\n    if (cantiereId) {\n      loadAllReports();\n    }\n  }, [cantiereId]);\n\n  // Configurazione dei report disponibili\n  const reportTypes = [\n    {\n      id: 'progress',\n      title: 'Report Avanzamento',\n      description: 'Panoramica completa dell\\'avanzamento dei lavori con metriche di performance e previsioni',\n      icon: <AssessmentIcon />,\n      color: 'primary',\n      features: ['Metri posati vs teorici', 'Percentuale completamento', 'Previsioni timeline', 'Performance giornaliera']\n    },\n    {\n      id: 'boq',\n      title: 'Bill of Quantities',\n      description: 'Distinta materiali dettagliata con analisi dei consumi e disponibilità',\n      icon: <ListIcon />,\n      color: 'secondary',\n      features: ['Materiali per tipologia', 'Consumi vs disponibilità', 'Previsioni acquisti', 'Analisi costi']\n    },\n    {\n      id: 'bobine',\n      title: 'Report Utilizzo Bobine',\n      description: 'Analisi completa dell\\'utilizzo delle bobine con efficienza e sprechi',\n      icon: <InventoryIcon />,\n      color: 'success',\n      features: ['Utilizzo per bobina', 'Efficienza materiali', 'Bobine disponibili', 'Analisi sprechi']\n    },\n    {\n      id: 'bobina-specifica',\n      title: 'Report Bobina Specifica',\n      description: 'Dettaglio approfondito di una singola bobina con tutti i cavi associati',\n      icon: <CableIcon />,\n      color: 'info',\n      features: ['Dettaglio bobina', 'Cavi associati', 'Utilizzo specifico', 'Storico operazioni']\n    },\n    {\n      id: 'posa-periodo',\n      title: 'Report Posa per Periodo',\n      description: 'Analisi temporale della posa con trend e pattern di lavoro',\n      icon: <TimelineIcon />,\n      color: 'warning',\n      features: ['Trend temporali', 'Performance periodiche', 'Analisi stagionali', 'Produttività team']\n    },\n    {\n      id: 'cavi-stato',\n      title: 'Report Cavi per Stato',\n      description: 'Classificazione dei cavi per stato di installazione con statistiche dettagliate',\n      icon: <BarChartIcon />,\n      color: 'error',\n      features: ['Cavi per stato', 'Statistiche installazione', 'Problematiche', 'Azioni richieste']\n    }\n  ];\n\n  // Nuova funzione per generare report con formato specificato\n  const generateReportWithFormat = async (reportType, format) => {\n    try {\n      setLoading(true);\n      setError(null);\n\n      let response;\n\n      switch (reportType) {\n        case 'progress':\n          response = await reportService.getProgressReport(cantiereId, format);\n          break;\n        case 'boq':\n          response = await reportService.getBillOfQuantities(cantiereId, format);\n          break;\n        case 'bobine':\n          response = await reportService.getBobineReport(cantiereId, format);\n          break;\n        case 'cavi-stato':\n          response = await reportService.getCaviStatoReport(cantiereId, format);\n          break;\n        case 'bobina-specifica':\n          if (!formData.id_bobina) {\n            setError('Inserisci l\\'ID della bobina');\n            return;\n          }\n          response = await reportService.getBobinaReport(cantiereId, formData.id_bobina, format);\n          break;\n        case 'posa-periodo':\n          if (!formData.data_inizio || !formData.data_fine) {\n            setError('Seleziona le date di inizio e fine periodo');\n            return;\n          }\n          response = await reportService.getPosaPerPeriodoReport(\n            cantiereId,\n            formData.data_inizio,\n            formData.data_fine,\n            format\n          );\n          break;\n        default:\n          throw new Error('Tipo di report non riconosciuto');\n      }\n\n      if (format === 'video') {\n        // For special reports, update the specific report data\n        if (reportType === 'bobina-specifica' || reportType === 'posa-periodo') {\n          setReportsData(prev => ({\n            ...prev,\n            [reportType === 'bobina-specifica' ? 'bobinaSpecifica' : 'posaPeriodo']: response.content\n          }));\n        }\n        setReportData(response.content);\n      } else {\n        // Per PDF/Excel, apri il link di download\n        if (response.file_url) {\n          window.open(response.file_url, '_blank');\n        }\n      }\n    } catch (err) {\n      console.error('Errore nella generazione del report:', err);\n      setError(err.detail || err.message || 'Errore durante la generazione del report');\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  const handleReportSelect = (reportType) => {\n    setSelectedReport(reportType);\n    setDialogType(reportType.id);\n\n    // Per report che necessitano di parametri aggiuntivi, mostra il dialog\n    if (reportType.id === 'posa-periodo' || reportType.id === 'bobina-specifica') {\n      // Imposta valori di default per alcuni report\n      if (reportType.id === 'posa-periodo') {\n        const today = new Date();\n        const lastMonth = new Date();\n        lastMonth.setMonth(today.getMonth() - 1);\n\n        setFormData({\n          ...formData,\n          data_inizio: lastMonth.toISOString().split('T')[0],\n          data_fine: today.toISOString().split('T')[0]\n        });\n      }\n\n      setOpenDialog(true);\n    } else {\n      // Per report senza parametri aggiuntivi, genera direttamente con formato 'video'\n      generateReportWithFormat(reportType.id, 'video');\n    }\n  };\n\n  const handleGenerateReport = async () => {\n    await generateReportWithFormat(dialogType, formData.formato);\n    setOpenDialog(false);\n  };\n\n  const handleCloseDialog = () => {\n    setOpenDialog(false);\n    setError(null);\n    setFormData({\n      formato: 'video',\n      data_inizio: '',\n      data_fine: '',\n      id_bobina: ''\n    });\n  };\n\n  const renderReportContent = () => {\n    if (!reportData) return null;\n\n    return (\n      <Paper sx={{ p: 3, mt: 3 }}>\n        <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 2 }}>\n          <Typography variant=\"h6\">\n            {selectedReport?.title} - {reportData.nome_cantiere}\n          </Typography>\n          <Box sx={{ display: 'flex', gap: 1 }}>\n            {/* Export buttons */}\n            <Button\n              startIcon={<DownloadIcon />}\n              onClick={() => generateReportWithFormat(dialogType, 'pdf')}\n              variant=\"outlined\"\n              size=\"small\"\n              color=\"primary\"\n            >\n              PDF\n            </Button>\n            <Button\n              startIcon={<DownloadIcon />}\n              onClick={() => generateReportWithFormat(dialogType, 'excel')}\n              variant=\"outlined\"\n              size=\"small\"\n              color=\"success\"\n            >\n              Excel\n            </Button>\n            <Button\n              startIcon={<RefreshIcon />}\n              onClick={() => setReportData(null)}\n              variant=\"outlined\"\n              size=\"small\"\n            >\n              Nuovo Report\n            </Button>\n          </Box>\n        </Box>\n\n        <Divider sx={{ mb: 3 }} />\n\n        {/* Renderizza il contenuto specifico del report */}\n        {dialogType === 'progress' && renderProgressReport(reportData)}\n        {dialogType === 'boq' && renderBoqReport(reportData)}\n        {dialogType === 'bobine' && renderBobineReport(reportData)}\n        {dialogType === 'bobina-specifica' && renderBobinaSpecificaReport(reportData)}\n        {dialogType === 'posa-periodo' && renderPosaPeriodoReport(reportData)}\n        {dialogType === 'cavi-stato' && renderCaviStatoReport(reportData)}\n      </Paper>\n    );\n  };\n\n  const renderProgressReport = (data) => (\n    <Box>\n      {/* Header con controlli */}\n      <Box sx={{ display: 'flex', justifyContent: 'flex-end', alignItems: 'center', mb: 2 }}>\n        <FormControlLabel\n          control={\n            <Switch\n              checked={showCharts}\n              onChange={(e) => setShowCharts(e.target.checked)}\n              color=\"primary\"\n            />\n          }\n          label={\n            <Box sx={{ display: 'flex', alignItems: 'center' }}>\n              <ShowChartIcon sx={{ mr: 1 }} />\n              Grafici\n            </Box>\n          }\n        />\n      </Box>\n\n      {/* Metriche Principali - Layout Orizzontale Compatto */}\n      <Paper sx={{ p: 2, mb: 3, border: '1px solid #e0e0e0' }}>\n        <Grid container spacing={2}>\n          <Grid item xs={12} md={3}>\n            <Box sx={{ textAlign: 'center', p: 1 }}>\n              <Typography variant=\"h5\" sx={{ fontWeight: 600, color: '#2c3e50', mb: 0.5 }}>\n                {data.metri_totali}m\n              </Typography>\n              <Typography variant=\"caption\" sx={{ color: '#666', textTransform: 'uppercase', letterSpacing: 0.5 }}>\n                Metri Totali\n              </Typography>\n            </Box>\n          </Grid>\n          <Grid item xs={12} md={3}>\n            <Box sx={{ textAlign: 'center', p: 1 }}>\n              <Typography variant=\"h5\" sx={{ fontWeight: 600, color: '#3498db', mb: 0.5 }}>\n                {data.metri_posati}m\n              </Typography>\n              <Typography variant=\"caption\" sx={{ color: '#666', textTransform: 'uppercase', letterSpacing: 0.5 }}>\n                Metri Posati ({data.percentuale_avanzamento}%)\n              </Typography>\n            </Box>\n          </Grid>\n          <Grid item xs={12} md={3}>\n            <Box sx={{ textAlign: 'center', p: 1 }}>\n              <Typography variant=\"h5\" sx={{ fontWeight: 600, color: '#5d6d7e', mb: 0.5 }}>\n                {data.metri_da_posare}m\n              </Typography>\n              <Typography variant=\"caption\" sx={{ color: '#666', textTransform: 'uppercase', letterSpacing: 0.5 }}>\n                Metri Rimanenti ({100 - data.percentuale_avanzamento}%)\n              </Typography>\n            </Box>\n          </Grid>\n          <Grid item xs={12} md={3}>\n            <Box sx={{ textAlign: 'center', p: 1 }}>\n              <Typography variant=\"h5\" sx={{ fontWeight: 600, color: '#85929e', mb: 0.5 }}>\n                {data.media_giornaliera}m\n              </Typography>\n              <Typography variant=\"caption\" sx={{ color: '#666', textTransform: 'uppercase', letterSpacing: 0.5 }}>\n                Media/Giorno\n                {data.giorni_stimati && ` (${data.giorni_stimati} giorni rimasti)`}\n              </Typography>\n            </Box>\n          </Grid>\n        </Grid>\n      </Paper>\n\n      {/* Grafici */}\n      {showCharts && (\n        <Box sx={{ mb: 4 }}>\n          <ProgressChart data={data} />\n        </Box>\n      )}\n\n      {/* Dettagli Compatti in Tabella Orizzontale */}\n      <Paper sx={{ p: 2, mb: 3, border: '1px solid #e0e0e0' }}>\n        <Typography variant=\"subtitle1\" sx={{ fontWeight: 500, mb: 2, color: '#2c3e50' }}>\n          Dettagli Cavi e Performance\n        </Typography>\n        <Box sx={{\n          border: '1px solid #e0e0e0',\n          borderRadius: 1,\n          overflow: 'hidden'\n        }}>\n          <table style={{ width: '100%', borderCollapse: 'collapse' }}>\n            <thead>\n              <tr style={{ backgroundColor: '#f8f9fa' }}>\n                <th style={{\n                  padding: '8px 12px',\n                  textAlign: 'left',\n                  fontSize: '12px',\n                  fontWeight: 600,\n                  color: '#2c3e50',\n                  borderBottom: '1px solid #e0e0e0',\n                  width: '25%'\n                }}>Totale Cavi</th>\n                <th style={{\n                  padding: '8px 12px',\n                  textAlign: 'left',\n                  fontSize: '12px',\n                  fontWeight: 600,\n                  color: '#2c3e50',\n                  borderBottom: '1px solid #e0e0e0',\n                  width: '25%'\n                }}>Cavi Posati</th>\n                <th style={{\n                  padding: '8px 12px',\n                  textAlign: 'left',\n                  fontSize: '12px',\n                  fontWeight: 600,\n                  color: '#2c3e50',\n                  borderBottom: '1px solid #e0e0e0',\n                  width: '25%'\n                }}>Media Giornaliera</th>\n                <th style={{\n                  padding: '8px 12px',\n                  textAlign: 'left',\n                  fontSize: '12px',\n                  fontWeight: 600,\n                  color: '#2c3e50',\n                  borderBottom: '1px solid #e0e0e0',\n                  width: '25%'\n                }}>Completamento Previsto</th>\n              </tr>\n            </thead>\n            <tbody>\n              <tr>\n                <td style={{\n                  padding: '12px',\n                  fontSize: '13px',\n                  textAlign: 'center'\n                }}>\n                  <Typography variant=\"h6\" sx={{ fontWeight: 600, color: '#2c3e50', mb: 0.5 }}>\n                    {data.totale_cavi}\n                  </Typography>\n                  <Typography variant=\"caption\" sx={{ color: '#666' }}>\n                    cavi totali\n                  </Typography>\n                </td>\n                <td style={{\n                  padding: '12px',\n                  fontSize: '13px',\n                  textAlign: 'center'\n                }}>\n                  <Typography variant=\"h6\" sx={{ fontWeight: 600, color: '#3498db', mb: 0.5 }}>\n                    {data.cavi_posati}\n                  </Typography>\n                  <Typography variant=\"caption\" sx={{ color: '#666' }}>\n                    {data.percentuale_cavi}% completato\n                  </Typography>\n                </td>\n                <td style={{\n                  padding: '12px',\n                  fontSize: '13px',\n                  textAlign: 'center'\n                }}>\n                  <Typography variant=\"h6\" sx={{ fontWeight: 600, color: '#3498db', mb: 0.5 }}>\n                    {data.media_giornaliera}m\n                  </Typography>\n                  <Typography variant=\"caption\" sx={{ color: '#666' }}>\n                    al giorno\n                  </Typography>\n                </td>\n                <td style={{\n                  padding: '12px',\n                  fontSize: '13px',\n                  textAlign: 'center'\n                }}>\n                  {data.giorni_stimati ? (\n                    <>\n                      <Typography variant=\"body2\" sx={{ fontWeight: 600, color: '#f39c12', mb: 0.5 }}>\n                        {data.data_completamento}\n                      </Typography>\n                      <Typography variant=\"caption\" sx={{ color: '#666' }}>\n                        ({data.giorni_stimati} giorni)\n                      </Typography>\n                    </>\n                  ) : (\n                    <Typography variant=\"caption\" sx={{ color: '#999' }}>\n                      Non disponibile\n                    </Typography>\n                  )}\n                </td>\n              </tr>\n            </tbody>\n          </table>\n        </Box>\n      </Paper>\n\n      {/* Posa Recente */}\n      {data.posa_recente && data.posa_recente.length > 0 && (\n        <Paper sx={{ p: 2, border: '1px solid #e0e0e0' }}>\n          <Typography variant=\"subtitle1\" sx={{ fontWeight: 500, mb: 2, color: '#2c3e50' }}>\n            Attività Recente\n          </Typography>\n          <FilterableTable\n            data={data.posa_recente.map(posa => ({\n              data: posa.data,\n              metri: `${posa.metri}m`\n            }))}\n            columns={[\n              { field: 'data', headerName: 'Data', width: 200 },\n              { field: 'metri', headerName: 'Metri Posati', width: 150, align: 'right' }\n            ]}\n            pagination={data.posa_recente.length > 6}\n            pageSize={6}\n          />\n        </Paper>\n      )}\n    </Box>\n  );\n\n  const renderBoqReport = (data) => (\n    <Box>\n      {/* Header */}\n      <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 3 }}>\n        <Typography variant=\"h5\" sx={{ fontWeight: 600, color: 'secondary.main' }}>\n          Bill of Quantities - Distinta Materiali\n        </Typography>\n        <FormControlLabel\n          control={\n            <Switch\n              checked={showCharts}\n              onChange={(e) => setShowCharts(e.target.checked)}\n              color=\"primary\"\n            />\n          }\n          label={\n            <Box sx={{ display: 'flex', alignItems: 'center' }}>\n              <ShowChartIcon sx={{ mr: 1 }} />\n              Grafici\n            </Box>\n          }\n        />\n      </Box>\n\n      {/* Grafici */}\n      {showCharts && (\n        <Box sx={{ mb: 4 }}>\n          <BoqChart data={data} />\n        </Box>\n      )}\n\n      {/* Cavi per Tipologia */}\n      <Paper sx={{ p: 3, mb: 3 }}>\n        <Typography variant=\"h6\" sx={{ mb: 2, fontWeight: 600 }}>\n          Cavi per Tipologia\n        </Typography>\n        <FilterableTable\n          data={data.cavi_per_tipo || []}\n          columns={[\n            { field: 'tipologia', headerName: 'Tipologia', width: 150 },\n            { field: 'sezione', headerName: 'Sezione', width: 100 },\n            { field: 'num_cavi', headerName: 'Cavi', width: 80, align: 'right', dataType: 'number' },\n            { field: 'metri_teorici', headerName: 'Metri Teorici', width: 120, align: 'right', dataType: 'number',\n              renderCell: (row) => `${row.metri_teorici}m` },\n            { field: 'metri_reali', headerName: 'Metri Reali', width: 120, align: 'right', dataType: 'number',\n              renderCell: (row) => `${row.metri_reali}m` },\n            { field: 'metri_da_posare', headerName: 'Da Posare', width: 120, align: 'right', dataType: 'number',\n              renderCell: (row) => `${row.metri_da_posare}m` }\n          ]}\n          pageSize={10}\n        />\n      </Paper>\n\n      {/* Bobine Disponibili */}\n      <Paper sx={{ p: 3 }}>\n        <Typography variant=\"h6\" sx={{ mb: 2, fontWeight: 600 }}>\n          Bobine Disponibili\n        </Typography>\n        <FilterableTable\n          data={data.bobine_per_tipo || []}\n          columns={[\n            { field: 'tipologia', headerName: 'Tipologia', width: 150 },\n            { field: 'sezione', headerName: 'Sezione', width: 100 },\n            { field: 'num_bobine', headerName: 'Bobine', width: 100, align: 'right', dataType: 'number' },\n            { field: 'metri_disponibili', headerName: 'Metri Disponibili', width: 150, align: 'right', dataType: 'number',\n              renderCell: (row) => `${row.metri_disponibili}m` }\n          ]}\n          pageSize={10}\n        />\n      </Paper>\n    </Box>\n  );\n\n  const renderBobineReport = (data) => (\n    <Box>\n      {/* Header */}\n      <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 3 }}>\n        <Typography variant=\"h5\" sx={{ fontWeight: 600, color: 'success.main' }}>\n          Report Utilizzo Bobine ({data.totale_bobine} totali)\n        </Typography>\n        <FormControlLabel\n          control={\n            <Switch\n              checked={showCharts}\n              onChange={(e) => setShowCharts(e.target.checked)}\n              color=\"primary\"\n            />\n          }\n          label={\n            <Box sx={{ display: 'flex', alignItems: 'center' }}>\n              <ShowChartIcon sx={{ mr: 1 }} />\n              Grafici\n            </Box>\n          }\n        />\n      </Box>\n\n      {/* Grafici */}\n      {showCharts && (\n        <Box sx={{ mb: 4 }}>\n          <BobineChart data={data} />\n        </Box>\n      )}\n\n      {/* Bobine del Cantiere */}\n      <Paper sx={{ p: 3 }}>\n        <Typography variant=\"h6\" sx={{ mb: 2, fontWeight: 600 }}>\n          Dettaglio Bobine del Cantiere\n        </Typography>\n        <FilterableTable\n          data={data.bobine || []}\n          columns={[\n            { field: 'id_bobina', headerName: 'ID Bobina', width: 120 },\n            { field: 'tipologia', headerName: 'Tipologia', width: 150 },\n            { field: 'sezione', headerName: 'Sezione', width: 100 },\n            { field: 'stato', headerName: 'Stato', width: 120,\n              renderCell: (row) => (\n                <Chip\n                  label={row.stato}\n                  color={row.stato === 'DISPONIBILE' ? 'success' : 'warning'}\n                  size=\"small\"\n                />\n              )\n            },\n            { field: 'metri_totali', headerName: 'Metri Totali', width: 120, align: 'right', dataType: 'number',\n              renderCell: (row) => `${row.metri_totali}m` },\n            { field: 'metri_residui', headerName: 'Metri Residui', width: 120, align: 'right', dataType: 'number',\n              renderCell: (row) => `${row.metri_residui}m` },\n            { field: 'metri_utilizzati', headerName: 'Metri Utilizzati', width: 140, align: 'right', dataType: 'number',\n              renderCell: (row) => `${row.metri_utilizzati}m` },\n            { field: 'percentuale_utilizzo', headerName: 'Utilizzo', width: 100, align: 'right', dataType: 'number',\n              renderCell: (row) => `${row.percentuale_utilizzo}%` }\n          ]}\n          pageSize={10}\n        />\n      </Paper>\n    </Box>\n  );\n\n  const renderBobinaSpecificaReport = (data) => (\n    <Box>\n      {/* Header */}\n      <Typography variant=\"h5\" sx={{ fontWeight: 600, color: 'info.main', mb: 3 }}>\n        Report Bobina Specifica - {data.bobina?.id_bobina}\n      </Typography>\n\n      <Grid container spacing={3}>\n        {/* Dettagli Bobina */}\n        <Grid item xs={12} md={6}>\n          <Paper sx={{ p: 3 }}>\n            <Typography variant=\"h6\" sx={{ mb: 2, fontWeight: 600 }}>\n              Dettagli Bobina\n            </Typography>\n            {data.bobina && (\n              <Box>\n                <Box sx={{ display: 'flex', justifyContent: 'space-between', mb: 1 }}>\n                  <Typography variant=\"body1\">ID Bobina:</Typography>\n                  <Typography variant=\"body1\" sx={{ fontWeight: 600 }}>{data.bobina.id_bobina}</Typography>\n                </Box>\n                <Box sx={{ display: 'flex', justifyContent: 'space-between', mb: 1 }}>\n                  <Typography variant=\"body1\">Tipologia:</Typography>\n                  <Typography variant=\"body1\" sx={{ fontWeight: 600 }}>{data.bobina.tipologia}</Typography>\n                </Box>\n                <Box sx={{ display: 'flex', justifyContent: 'space-between', mb: 1 }}>\n                  <Typography variant=\"body1\">Sezione:</Typography>\n                  <Typography variant=\"body1\" sx={{ fontWeight: 600 }}>{data.bobina.sezione}</Typography>\n                </Box>\n                <Box sx={{ display: 'flex', justifyContent: 'space-between', mb: 1 }}>\n                  <Typography variant=\"body1\">Stato:</Typography>\n                  <Chip\n                    label={data.bobina.stato}\n                    color={data.bobina.stato === 'DISPONIBILE' ? 'success' : 'warning'}\n                    size=\"small\"\n                  />\n                </Box>\n              </Box>\n            )}\n          </Paper>\n        </Grid>\n\n        {/* Metriche Utilizzo */}\n        <Grid item xs={12} md={6}>\n          <Paper sx={{ p: 3 }}>\n            <Typography variant=\"h6\" sx={{ mb: 2, fontWeight: 600 }}>\n              Metriche Utilizzo\n            </Typography>\n            {data.bobina && (\n              <Box>\n                <Box sx={{ display: 'flex', justifyContent: 'space-between', mb: 1 }}>\n                  <Typography variant=\"body1\">Metri Totali:</Typography>\n                  <Typography variant=\"body1\" sx={{ fontWeight: 600 }}>{data.bobina.metri_totali}m</Typography>\n                </Box>\n                <Box sx={{ display: 'flex', justifyContent: 'space-between', mb: 1 }}>\n                  <Typography variant=\"body1\">Metri Utilizzati:</Typography>\n                  <Typography variant=\"body1\" sx={{ fontWeight: 600, color: 'success.main' }}>\n                    {data.bobina.metri_utilizzati}m\n                  </Typography>\n                </Box>\n                <Box sx={{ display: 'flex', justifyContent: 'space-between', mb: 1 }}>\n                  <Typography variant=\"body1\">Metri Residui:</Typography>\n                  <Typography variant=\"body1\" sx={{ fontWeight: 600, color: 'warning.main' }}>\n                    {data.bobina.metri_residui}m\n                  </Typography>\n                </Box>\n                <Box sx={{ display: 'flex', justifyContent: 'space-between' }}>\n                  <Typography variant=\"body1\">Percentuale Utilizzo:</Typography>\n                  <Typography variant=\"body1\" sx={{ fontWeight: 600 }}>{data.bobina.percentuale_utilizzo}%</Typography>\n                </Box>\n              </Box>\n            )}\n          </Paper>\n        </Grid>\n\n        {/* Cavi Associati */}\n        <Grid item xs={12}>\n          <Paper sx={{ p: 3 }}>\n            <Typography variant=\"h6\" sx={{ mb: 2, fontWeight: 600 }}>\n              Cavi Associati ({data.totale_cavi})\n            </Typography>\n            <FilterableTable\n              data={data.cavi_associati || []}\n              columns={[\n                { field: 'id_cavo', headerName: 'ID Cavo', width: 120 },\n                { field: 'sistema', headerName: 'Sistema', width: 120 },\n                { field: 'utility', headerName: 'Utility', width: 120 },\n                { field: 'tipologia', headerName: 'Tipologia', width: 150 },\n                { field: 'metri_teorici', headerName: 'Metri Teorici', width: 120, align: 'right', dataType: 'number',\n                  renderCell: (row) => `${row.metri_teorici}m` },\n                { field: 'metri_reali', headerName: 'Metri Reali', width: 120, align: 'right', dataType: 'number',\n                  renderCell: (row) => `${row.metri_reali}m` },\n                { field: 'stato', headerName: 'Stato', width: 120,\n                  renderCell: (row) => (\n                    <Chip\n                      label={row.stato}\n                      color={row.stato === 'POSATO' ? 'success' : 'warning'}\n                      size=\"small\"\n                    />\n                  )\n                }\n              ]}\n              pageSize={10}\n            />\n          </Paper>\n        </Grid>\n      </Grid>\n    </Box>\n  );\n\n  const renderPosaPeriodoReport = (data) => (\n    <Box>\n      {/* Header */}\n      <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 3 }}>\n        <Typography variant=\"h5\" sx={{ fontWeight: 600, color: 'warning.main' }}>\n          Report Posa per Periodo\n        </Typography>\n        <FormControlLabel\n          control={\n            <Switch\n              checked={showCharts}\n              onChange={(e) => setShowCharts(e.target.checked)}\n              color=\"primary\"\n            />\n          }\n          label={\n            <Box sx={{ display: 'flex', alignItems: 'center' }}>\n              <ShowChartIcon sx={{ mr: 1 }} />\n              Grafici\n            </Box>\n          }\n        />\n      </Box>\n\n      {/* Statistiche Periodo - Layout Orizzontale */}\n      <Grid container spacing={3} sx={{ mb: 4 }}>\n        <Grid item xs={12} md={3}>\n          <Paper sx={{ p: 3, textAlign: 'center', bgcolor: 'warning.main', color: 'white' }}>\n            <Typography variant=\"h4\" sx={{ fontWeight: 'bold', mb: 1 }}>\n              {data.totale_metri_periodo}m\n            </Typography>\n            <Typography variant=\"body1\">Metri Totali</Typography>\n            <Typography variant=\"caption\">{data.data_inizio} - {data.data_fine}</Typography>\n          </Paper>\n        </Grid>\n        <Grid item xs={12} md={3}>\n          <Paper sx={{ p: 3, textAlign: 'center', bgcolor: 'info.main', color: 'white' }}>\n            <Typography variant=\"h4\" sx={{ fontWeight: 'bold', mb: 1 }}>\n              {data.giorni_attivi}\n            </Typography>\n            <Typography variant=\"body1\">Giorni Attivi</Typography>\n          </Paper>\n        </Grid>\n        <Grid item xs={12} md={3}>\n          <Paper sx={{ p: 3, textAlign: 'center', bgcolor: 'success.main', color: 'white' }}>\n            <Typography variant=\"h4\" sx={{ fontWeight: 'bold', mb: 1 }}>\n              {data.media_giornaliera}m\n            </Typography>\n            <Typography variant=\"body1\">Media/Giorno</Typography>\n          </Paper>\n        </Grid>\n        <Grid item xs={12} md={3}>\n          <Paper sx={{ p: 3, textAlign: 'center', bgcolor: 'primary.main', color: 'white' }}>\n            <Typography variant=\"h4\" sx={{ fontWeight: 'bold', mb: 1 }}>\n              {Math.round(data.totale_metri_periodo / data.giorni_attivi * 7)}m\n            </Typography>\n            <Typography variant=\"body1\">Media/Settimana</Typography>\n          </Paper>\n        </Grid>\n      </Grid>\n\n      {/* Grafici */}\n      {showCharts && (\n        <Box sx={{ mb: 4 }}>\n          <TimelineChart data={data} />\n        </Box>\n      )}\n\n      {/* Posa Giornaliera */}\n      <Paper sx={{ p: 3 }}>\n        <Typography variant=\"h6\" sx={{ mb: 2, fontWeight: 600 }}>\n          Dettaglio Posa Giornaliera\n        </Typography>\n        <FilterableTable\n          data={data.posa_giornaliera || []}\n          columns={[\n            { field: 'data', headerName: 'Data', width: 200 },\n            { field: 'metri', headerName: 'Metri Posati', width: 150, align: 'right', dataType: 'number',\n              renderCell: (row) => `${row.metri}m` }\n          ]}\n          pageSize={10}\n        />\n      </Paper>\n    </Box>\n  );\n\n  const renderCaviStatoReport = (data) => (\n    <Box>\n      {/* Header */}\n      <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 3 }}>\n        <Typography variant=\"h5\" sx={{ fontWeight: 600, color: 'error.main' }}>\n          Report Cavi per Stato\n        </Typography>\n        <FormControlLabel\n          control={\n            <Switch\n              checked={showCharts}\n              onChange={(e) => setShowCharts(e.target.checked)}\n              color=\"primary\"\n            />\n          }\n          label={\n            <Box sx={{ display: 'flex', alignItems: 'center' }}>\n              <ShowChartIcon sx={{ mr: 1 }} />\n              Grafici\n            </Box>\n          }\n        />\n      </Box>\n\n      {/* Grafici */}\n      {showCharts && (\n        <Box sx={{ mb: 4 }}>\n          <CaviStatoChart data={data} />\n        </Box>\n      )}\n\n      {/* Cavi per Stato di Installazione */}\n      <Paper sx={{ p: 3 }}>\n        <Typography variant=\"h6\" sx={{ mb: 2, fontWeight: 600 }}>\n          Distribuzione Cavi per Stato di Installazione\n        </Typography>\n        <FilterableTable\n          data={data.cavi_per_stato || []}\n          columns={[\n            { field: 'stato', headerName: 'Stato', width: 150,\n              renderCell: (row) => (\n                <Chip\n                  label={row.stato}\n                  color={row.stato === 'Installato' ? 'success' : 'warning'}\n                  size=\"small\"\n                />\n              )\n            },\n            { field: 'num_cavi', headerName: 'Numero Cavi', width: 120, align: 'right', dataType: 'number' },\n            { field: 'metri_teorici', headerName: 'Metri Teorici', width: 150, align: 'right', dataType: 'number',\n              renderCell: (row) => `${row.metri_teorici}m` },\n            { field: 'metri_reali', headerName: 'Metri Reali', width: 150, align: 'right', dataType: 'number',\n              renderCell: (row) => `${row.metri_reali}m` }\n          ]}\n          pagination={false}\n        />\n      </Paper>\n    </Box>\n  );\n\n  const renderDialog = () => (\n    <Dialog open={openDialog} onClose={handleCloseDialog} maxWidth=\"sm\" fullWidth>\n      <DialogTitle>\n        {selectedReport?.title}\n      </DialogTitle>\n      <DialogContent>\n        {error && (\n          <Alert severity=\"error\" sx={{ mb: 2 }}>\n            {error}\n          </Alert>\n        )}\n\n        <Grid container spacing={2} sx={{ mt: 1 }}>\n          <Grid item xs={12}>\n            <FormControl fullWidth>\n              <InputLabel>Formato</InputLabel>\n              <Select\n                value={formData.formato}\n                label=\"Formato\"\n                onChange={(e) => setFormData({ ...formData, formato: e.target.value })}\n              >\n                <MenuItem value=\"video\">Visualizza a schermo</MenuItem>\n                <MenuItem value=\"pdf\">Download PDF</MenuItem>\n                <MenuItem value=\"excel\">Download Excel</MenuItem>\n              </Select>\n            </FormControl>\n          </Grid>\n\n          {dialogType === 'bobina-specifica' && (\n            <Grid item xs={12}>\n              <TextField\n                fullWidth\n                label=\"ID Bobina\"\n                value={formData.id_bobina}\n                onChange={(e) => setFormData({ ...formData, id_bobina: e.target.value })}\n                placeholder=\"Es: 1, 2, A, B...\"\n                helperText=\"Inserisci solo la parte finale dell'ID (es: 1 per C1_B1)\"\n              />\n            </Grid>\n          )}\n\n          {dialogType === 'posa-periodo' && (\n            <>\n              <Grid item xs={6}>\n                <TextField\n                  fullWidth\n                  type=\"date\"\n                  label=\"Data Inizio\"\n                  value={formData.data_inizio}\n                  onChange={(e) => setFormData({ ...formData, data_inizio: e.target.value })}\n                  InputLabelProps={{ shrink: true }}\n                />\n              </Grid>\n              <Grid item xs={6}>\n                <TextField\n                  fullWidth\n                  type=\"date\"\n                  label=\"Data Fine\"\n                  value={formData.data_fine}\n                  onChange={(e) => setFormData({ ...formData, data_fine: e.target.value })}\n                  InputLabelProps={{ shrink: true }}\n                />\n              </Grid>\n            </>\n          )}\n        </Grid>\n      </DialogContent>\n      <DialogActions>\n        <Button onClick={handleCloseDialog}>Annulla</Button>\n        <Button\n          onClick={handleGenerateReport}\n          variant=\"contained\"\n          disabled={loading}\n          startIcon={loading ? <CircularProgress size={20} /> : <VisibilityIcon />}\n        >\n          {loading ? 'Generazione...' : 'Genera Report'}\n        </Button>\n      </DialogActions>\n    </Dialog>\n  );\n\n  return (\n    <Box sx={{ p: 3 }}>\n      {/* Header */}\n      <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 3 }}>\n        <Box sx={{ display: 'flex', alignItems: 'center', gap: 2 }}>\n          <IconButton onClick={() => navigate(-1)} color=\"primary\">\n            <ArrowBackIcon />\n          </IconButton>\n          <Typography variant=\"h4\" component=\"h1\">\n            Report\n          </Typography>\n        </Box>\n        <AdminHomeButton />\n      </Box>\n\n      {/* Loading indicator */}\n      {loading && (\n        <Box sx={{ display: 'flex', justifyContent: 'center', my: 4 }}>\n          <CircularProgress />\n        </Box>\n      )}\n\n      {/* Reports Navigation */}\n      <Box sx={{ mt: 3 }}>\n        {/* Report Tabs */}\n        <Paper sx={{ mb: 3, border: '1px solid #e0e0e0' }}>\n          <Box sx={{ borderBottom: 1, borderColor: '#e0e0e0', bgcolor: '#f8f9fa' }}>\n            <Box sx={{ display: 'flex', flexWrap: 'wrap', gap: 1, p: 2 }}>\n              <Button\n                variant={selectedReportType === 'progress' ? 'contained' : 'outlined'}\n                startIcon={<AssessmentIcon />}\n                onClick={() => setSelectedReportType('progress')}\n                sx={{\n                  bgcolor: selectedReportType === 'progress' ? '#2c3e50' : 'transparent',\n                  color: selectedReportType === 'progress' ? 'white' : '#2c3e50',\n                  borderColor: '#2c3e50',\n                  '&:hover': {\n                    bgcolor: selectedReportType === 'progress' ? '#34495e' : '#f8f9fa',\n                    borderColor: '#2c3e50'\n                  }\n                }}\n              >\n                Avanzamento\n              </Button>\n              <Button\n                variant={selectedReportType === 'boq' ? 'contained' : 'outlined'}\n                startIcon={<ListIcon />}\n                onClick={() => setSelectedReportType('boq')}\n                sx={{\n                  bgcolor: selectedReportType === 'boq' ? '#2c3e50' : 'transparent',\n                  color: selectedReportType === 'boq' ? 'white' : '#2c3e50',\n                  borderColor: '#2c3e50',\n                  '&:hover': {\n                    bgcolor: selectedReportType === 'boq' ? '#34495e' : '#f8f9fa',\n                    borderColor: '#2c3e50'\n                  }\n                }}\n              >\n                Bill of Quantities\n              </Button>\n              <Button\n                variant={selectedReportType === 'bobine' ? 'contained' : 'outlined'}\n                startIcon={<InventoryIcon />}\n                onClick={() => setSelectedReportType('bobine')}\n                sx={{\n                  bgcolor: selectedReportType === 'bobine' ? '#2c3e50' : 'transparent',\n                  color: selectedReportType === 'bobine' ? 'white' : '#2c3e50',\n                  borderColor: '#2c3e50',\n                  '&:hover': {\n                    bgcolor: selectedReportType === 'bobine' ? '#34495e' : '#f8f9fa',\n                    borderColor: '#2c3e50'\n                  }\n                }}\n              >\n                Bobine\n              </Button>\n              <Button\n                variant={selectedReportType === 'cavi-stato' ? 'contained' : 'outlined'}\n                startIcon={<BarChartIcon />}\n                onClick={() => setSelectedReportType('cavi-stato')}\n                sx={{\n                  bgcolor: selectedReportType === 'cavi-stato' ? '#2c3e50' : 'transparent',\n                  color: selectedReportType === 'cavi-stato' ? 'white' : '#2c3e50',\n                  borderColor: '#2c3e50',\n                  '&:hover': {\n                    bgcolor: selectedReportType === 'cavi-stato' ? '#34495e' : '#f8f9fa',\n                    borderColor: '#2c3e50'\n                  }\n                }}\n              >\n                Cavi per Stato\n              </Button>\n              <Button\n                variant={selectedReportType === 'bobina-specifica' ? 'contained' : 'outlined'}\n                startIcon={<CableIcon />}\n                onClick={() => setSelectedReportType('bobina-specifica')}\n                sx={{\n                  bgcolor: selectedReportType === 'bobina-specifica' ? '#2c3e50' : 'transparent',\n                  color: selectedReportType === 'bobina-specifica' ? 'white' : '#2c3e50',\n                  borderColor: '#2c3e50',\n                  '&:hover': {\n                    bgcolor: selectedReportType === 'bobina-specifica' ? '#34495e' : '#f8f9fa',\n                    borderColor: '#2c3e50'\n                  }\n                }}\n              >\n                Bobina Specifica\n              </Button>\n              <Button\n                variant={selectedReportType === 'posa-periodo' ? 'contained' : 'outlined'}\n                startIcon={<TimelineIcon />}\n                onClick={() => setSelectedReportType('posa-periodo')}\n                sx={{\n                  bgcolor: selectedReportType === 'posa-periodo' ? '#2c3e50' : 'transparent',\n                  color: selectedReportType === 'posa-periodo' ? 'white' : '#2c3e50',\n                  borderColor: '#2c3e50',\n                  '&:hover': {\n                    bgcolor: selectedReportType === 'posa-periodo' ? '#34495e' : '#f8f9fa',\n                    borderColor: '#2c3e50'\n                  }\n                }}\n              >\n                Posa per Periodo\n              </Button>\n            </Box>\n          </Box>\n        </Paper>\n\n        {/* Report Content */}\n        <Box sx={{ minHeight: '400px' }}>\n          {/* Progress Report */}\n          {selectedReportType === 'progress' && (\n            <Paper sx={{ p: 3 }}>\n              {reportsData.progress ? (\n                <Box>\n                  <Box sx={{ display: 'flex', justifyContent: 'flex-end', mb: 2 }}>\n                    <Button\n                      startIcon={<DownloadIcon />}\n                      onClick={() => generateReportWithFormat('progress', 'pdf')}\n                      variant=\"outlined\"\n                      size=\"small\"\n                      color=\"primary\"\n                      sx={{ mr: 1 }}\n                    >\n                      PDF\n                    </Button>\n                    <Button\n                      startIcon={<DownloadIcon />}\n                      onClick={() => generateReportWithFormat('progress', 'excel')}\n                      variant=\"outlined\"\n                      size=\"small\"\n                      color=\"success\"\n                    >\n                      Excel\n                    </Button>\n                  </Box>\n                  {renderProgressReport(reportsData.progress)}\n                </Box>\n              ) : loading ? (\n                <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'center', my: 4 }}>\n                  <CircularProgress size={24} />\n                  <Typography sx={{ ml: 2 }}>Caricamento in corso...</Typography>\n                </Box>\n              ) : (\n                <Box sx={{ textAlign: 'center', my: 4 }}>\n                  <Alert severity=\"error\" sx={{ mb: 2 }}>\n                    Impossibile caricare il report. Riprova più tardi.\n                  </Alert>\n                  <Button\n                    variant=\"outlined\"\n                    startIcon={<RefreshIcon />}\n                    onClick={() => {\n                      setLoading(true);\n                      reportService.getProgressReport(cantiereId, 'video')\n                        .then(data => {\n                          setReportsData(prev => ({\n                            ...prev,\n                            progress: data.content\n                          }));\n                        })\n                        .catch(err => {\n                          console.error('Error retrying progress report:', err);\n                        })\n                        .finally(() => {\n                          setLoading(false);\n                        });\n                    }}\n                  >\n                    Riprova\n                  </Button>\n                </Box>\n              )}\n            </Paper>\n          )}\n\n          {/* Bill of Quantities */}\n          {selectedReportType === 'boq' && (\n            <Paper sx={{ p: 3 }}>\n              {reportsData.boq ? (\n                <Box>\n                  <Box sx={{ display: 'flex', justifyContent: 'flex-end', mb: 2 }}>\n                    <Button\n                      startIcon={<DownloadIcon />}\n                      onClick={() => generateReportWithFormat('boq', 'pdf')}\n                      variant=\"outlined\"\n                      size=\"small\"\n                      color=\"primary\"\n                      sx={{ mr: 1 }}\n                    >\n                      PDF\n                    </Button>\n                    <Button\n                      startIcon={<DownloadIcon />}\n                      onClick={() => generateReportWithFormat('boq', 'excel')}\n                      variant=\"outlined\"\n                      size=\"small\"\n                      color=\"success\"\n                    >\n                      Excel\n                    </Button>\n                  </Box>\n                  {renderBoqReport(reportsData.boq)}\n                </Box>\n              ) : loading ? (\n                <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'center', my: 4 }}>\n                  <CircularProgress size={24} />\n                  <Typography sx={{ ml: 2 }}>Caricamento in corso...</Typography>\n                </Box>\n              ) : (\n                <Box sx={{ textAlign: 'center', my: 4 }}>\n                  <Alert severity=\"error\" sx={{ mb: 2 }}>\n                    Impossibile caricare il report. Riprova più tardi.\n                  </Alert>\n                  <Button\n                    variant=\"outlined\"\n                    startIcon={<RefreshIcon />}\n                    onClick={() => {\n                      setLoading(true);\n                      reportService.getBillOfQuantities(cantiereId, 'video')\n                        .then(data => {\n                          setReportsData(prev => ({\n                            ...prev,\n                            boq: data.content\n                          }));\n                        })\n                        .catch(err => {\n                          console.error('Error retrying BOQ report:', err);\n                        })\n                        .finally(() => {\n                          setLoading(false);\n                        });\n                    }}\n                  >\n                    Riprova\n                  </Button>\n                </Box>\n              )}\n            </Paper>\n          )}\n\n          {/* Bobine Report */}\n          {selectedReportType === 'bobine' && (\n            <Paper sx={{ p: 3 }}>\n              {reportsData.bobine ? (\n                <Box>\n                  <Box sx={{ display: 'flex', justifyContent: 'flex-end', mb: 2 }}>\n                    <Button\n                      startIcon={<DownloadIcon />}\n                      onClick={() => generateReportWithFormat('bobine', 'pdf')}\n                      variant=\"outlined\"\n                      size=\"small\"\n                      color=\"primary\"\n                      sx={{ mr: 1 }}\n                    >\n                      PDF\n                    </Button>\n                    <Button\n                      startIcon={<DownloadIcon />}\n                      onClick={() => generateReportWithFormat('bobine', 'excel')}\n                      variant=\"outlined\"\n                      size=\"small\"\n                      color=\"success\"\n                    >\n                      Excel\n                    </Button>\n                  </Box>\n                  {renderBobineReport(reportsData.bobine)}\n                </Box>\n              ) : loading ? (\n                <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'center', my: 4 }}>\n                  <CircularProgress size={24} />\n                  <Typography sx={{ ml: 2 }}>Caricamento in corso...</Typography>\n                </Box>\n              ) : (\n                <Box sx={{ textAlign: 'center', my: 4 }}>\n                  <Alert severity=\"error\" sx={{ mb: 2 }}>\n                    Impossibile caricare il report. Riprova più tardi.\n                  </Alert>\n                  <Button\n                    variant=\"outlined\"\n                    startIcon={<RefreshIcon />}\n                    onClick={() => {\n                      setLoading(true);\n                      reportService.getBobineReport(cantiereId, 'video')\n                        .then(data => {\n                          setReportsData(prev => ({\n                            ...prev,\n                            bobine: data.content\n                          }));\n                        })\n                        .catch(err => {\n                          console.error('Error retrying bobine report:', err);\n                        })\n                        .finally(() => {\n                          setLoading(false);\n                        });\n                    }}\n                  >\n                    Riprova\n                  </Button>\n                </Box>\n              )}\n            </Paper>\n          )}\n\n          {/* Cavi Stato Report */}\n          {selectedReportType === 'cavi-stato' && (\n            <Paper sx={{ p: 3 }}>\n              {reportsData.caviStato ? (\n                <Box>\n                  <Box sx={{ display: 'flex', justifyContent: 'flex-end', mb: 2 }}>\n                    <Button\n                      startIcon={<DownloadIcon />}\n                      onClick={() => generateReportWithFormat('cavi-stato', 'pdf')}\n                      variant=\"outlined\"\n                      size=\"small\"\n                      color=\"primary\"\n                      sx={{ mr: 1 }}\n                    >\n                      PDF\n                    </Button>\n                    <Button\n                      startIcon={<DownloadIcon />}\n                      onClick={() => generateReportWithFormat('cavi-stato', 'excel')}\n                      variant=\"outlined\"\n                      size=\"small\"\n                      color=\"success\"\n                    >\n                      Excel\n                    </Button>\n                  </Box>\n                  {renderCaviStatoReport(reportsData.caviStato)}\n                </Box>\n              ) : loading ? (\n                <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'center', my: 4 }}>\n                  <CircularProgress size={24} />\n                  <Typography sx={{ ml: 2 }}>Caricamento in corso...</Typography>\n                </Box>\n              ) : (\n                <Box sx={{ textAlign: 'center', my: 4 }}>\n                  <Alert severity=\"error\" sx={{ mb: 2 }}>\n                    Impossibile caricare il report. Riprova più tardi.\n                  </Alert>\n                  <Button\n                    variant=\"outlined\"\n                    startIcon={<RefreshIcon />}\n                    onClick={() => {\n                      setLoading(true);\n                      reportService.getCaviStatoReport(cantiereId, 'video')\n                        .then(data => {\n                          setReportsData(prev => ({\n                            ...prev,\n                            caviStato: data.content\n                          }));\n                        })\n                        .catch(err => {\n                          console.error('Error retrying cavi stato report:', err);\n                        })\n                        .finally(() => {\n                          setLoading(false);\n                        });\n                    }}\n                  >\n                    Riprova\n                  </Button>\n                </Box>\n              )}\n            </Paper>\n          )}\n\n          {/* Bobina Specifica Report */}\n          {selectedReportType === 'bobina-specifica' && (\n            <Paper sx={{ p: 3 }}>\n              {reportsData.bobinaSpecifica ? (\n                <Box>\n                  <Box sx={{ display: 'flex', justifyContent: 'flex-end', mb: 2 }}>\n                    <Button\n                      startIcon={<DownloadIcon />}\n                      onClick={() => generateReportWithFormat('bobina-specifica', 'pdf')}\n                      variant=\"outlined\"\n                      size=\"small\"\n                      color=\"primary\"\n                      sx={{ mr: 1 }}\n                    >\n                      PDF\n                    </Button>\n                    <Button\n                      startIcon={<DownloadIcon />}\n                      onClick={() => generateReportWithFormat('bobina-specifica', 'excel')}\n                      variant=\"outlined\"\n                      size=\"small\"\n                      color=\"success\"\n                    >\n                      Excel\n                    </Button>\n                  </Box>\n                  {renderBobinaSpecificaReport(reportsData.bobinaSpecifica)}\n                </Box>\n              ) : (\n                <Box sx={{ textAlign: 'center', my: 4 }}>\n                  <Typography variant=\"h6\" sx={{ mb: 2 }}>\n                    Report Bobina Specifica\n                  </Typography>\n                  <Typography variant=\"body2\" color=\"text.secondary\" sx={{ mb: 3 }}>\n                    Seleziona una bobina per generare il report dettagliato con tutti i cavi associati.\n                  </Typography>\n                  <Button\n                    variant=\"contained\"\n                    color=\"info\"\n                    startIcon={<CableIcon />}\n                    onClick={() => {\n                      setDialogType('bobina-specifica');\n                      setOpenDialog(true);\n                    }}\n                  >\n                    Seleziona Bobina\n                  </Button>\n                </Box>\n              )}\n            </Paper>\n          )}\n\n          {/* Posa per Periodo Report */}\n          {selectedReportType === 'posa-periodo' && (\n            <Paper sx={{ p: 3 }}>\n              {reportsData.posaPeriodo ? (\n                <Box>\n                  <Box sx={{ display: 'flex', justifyContent: 'flex-end', mb: 2 }}>\n                    <Button\n                      startIcon={<DownloadIcon />}\n                      onClick={() => generateReportWithFormat('posa-periodo', 'pdf')}\n                      variant=\"outlined\"\n                      size=\"small\"\n                      color=\"primary\"\n                      sx={{ mr: 1 }}\n                    >\n                      PDF\n                    </Button>\n                    <Button\n                      startIcon={<DownloadIcon />}\n                      onClick={() => generateReportWithFormat('posa-periodo', 'excel')}\n                      variant=\"outlined\"\n                      size=\"small\"\n                      color=\"success\"\n                    >\n                      Excel\n                    </Button>\n                  </Box>\n                  {renderPosaPeriodoReport(reportsData.posaPeriodo)}\n                </Box>\n              ) : (\n                <Box sx={{ textAlign: 'center', my: 4 }}>\n                  <Typography variant=\"h6\" sx={{ mb: 2 }}>\n                    Report Posa per Periodo\n                  </Typography>\n                  <Typography variant=\"body2\" color=\"text.secondary\" sx={{ mb: 3 }}>\n                    Seleziona un periodo per analizzare i trend e pattern di posa dei cavi.\n                  </Typography>\n                  <Button\n                    variant=\"contained\"\n                    color=\"warning\"\n                    startIcon={<TimelineIcon />}\n                    onClick={() => {\n                      setDialogType('posa-periodo');\n                      // Set default date range (last month to today)\n                      const today = new Date();\n                      const lastMonth = new Date();\n                      lastMonth.setMonth(today.getMonth() - 1);\n\n                      setFormData({\n                        ...formData,\n                        data_inizio: lastMonth.toISOString().split('T')[0],\n                        data_fine: today.toISOString().split('T')[0]\n                      });\n                      setOpenDialog(true);\n                    }}\n                  >\n                    Seleziona Periodo\n                  </Button>\n                </Box>\n              )}\n            </Paper>\n          )}\n        </Box>\n      </Box>\n\n      {/* Dialog per configurazione report */}\n      {renderDialog()}\n    </Box>\n  );\n};\n\nexport default ReportCaviPageNew;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAClD,SACEC,GAAG,EACHC,UAAU,EACVC,KAAK,EACLC,IAAI,EACJC,IAAI,EACJC,WAAW,EACXC,WAAW,EACXC,MAAM,EACNC,IAAI,EACJC,KAAK,EACLC,gBAAgB,EAChBC,OAAO,EACPC,UAAU,EACVC,OAAO,EACPC,MAAM,EACNC,WAAW,EACXC,aAAa,EACbC,aAAa,EACbC,WAAW,EACXC,UAAU,EACVC,MAAM,EACNC,QAAQ,EACRC,SAAS,EACTC,SAAS,EACTC,gBAAgB,EAChBC,gBAAgB,EAChBC,MAAM,EACNC,gBAAgB,QACX,eAAe;AACtB,SACEC,UAAU,IAAIC,cAAc,EAC5BC,QAAQ,IAAIC,YAAY,EACxBC,QAAQ,IAAIC,YAAY,EACxBC,QAAQ,IAAIC,YAAY,EACxBC,IAAI,IAAIC,QAAQ,EAChBC,QAAQ,IAAIC,YAAY,EACxBC,UAAU,IAAIC,cAAc,EAC5BC,OAAO,IAAIC,WAAW,EACtBC,SAAS,IAAIC,aAAa,EAC1BC,SAAS,IAAIC,aAAa,EAC1BC,KAAK,IAAIC,SAAS,EAClBC,SAAS,IAAIC,aAAa,EAC1BC,UAAU,IAAIC,cAAc,EAC5BC,SAAS,IAAIC,aAAa,QACrB,qBAAqB;AAC5B,SAASC,WAAW,EAAEC,SAAS,QAAQ,kBAAkB;AACzD,SAASC,OAAO,QAAQ,2BAA2B;AACnD,OAAOC,eAAe,MAAM,yCAAyC;AACrE,OAAOC,aAAa,MAAM,8BAA8B;AACxD,OAAOC,eAAe,MAAM,yCAAyC;;AAErE;AACA,OAAOC,aAAa,MAAM,uCAAuC;AACjE,OAAOC,WAAW,MAAM,qCAAqC;AAC7D,OAAOC,QAAQ,MAAM,kCAAkC;AACvD,OAAOC,aAAa,MAAM,uCAAuC;AACjE,OAAOC,cAAc,MAAM,wCAAwC;AAAC,SAAAC,MAAA,IAAAC,OAAA,EAAAC,QAAA,IAAAC,SAAA;AAEpE,MAAMC,iBAAiB,GAAGA,CAAA,KAAM;EAAAC,EAAA;EAC9B,MAAMC,QAAQ,GAAGjB,WAAW,CAAC,CAAC;EAC9B,MAAM;IAAEkB;EAAW,CAAC,GAAGjB,SAAS,CAAC,CAAC;EAClC,MAAM;IAAEkB;EAAK,CAAC,GAAGjB,OAAO,CAAC,CAAC;EAE1B,MAAM,CAACkB,OAAO,EAAEC,UAAU,CAAC,GAAG/E,QAAQ,CAAC,KAAK,CAAC;EAC7C,MAAM,CAACgF,KAAK,EAAEC,QAAQ,CAAC,GAAGjF,QAAQ,CAAC,IAAI,CAAC;EACxC,MAAM,CAACkF,UAAU,EAAEC,aAAa,CAAC,GAAGnF,QAAQ,CAAC,IAAI,CAAC;EAClD,MAAM,CAACoF,cAAc,EAAEC,iBAAiB,CAAC,GAAGrF,QAAQ,CAAC,IAAI,CAAC;EAC1D,MAAM,CAACsF,UAAU,EAAEC,aAAa,CAAC,GAAGvF,QAAQ,CAAC,KAAK,CAAC;EACnD,MAAM,CAACwF,UAAU,EAAEC,aAAa,CAAC,GAAGzF,QAAQ,CAAC,EAAE,CAAC;EAChD,MAAM,CAAC0F,kBAAkB,EAAEC,qBAAqB,CAAC,GAAG3F,QAAQ,CAAC,UAAU,CAAC;EACxE,MAAM,CAAC4F,QAAQ,EAAEC,WAAW,CAAC,GAAG7F,QAAQ,CAAC;IACvC8F,OAAO,EAAE,OAAO;IAChBC,WAAW,EAAE,EAAE;IACfC,SAAS,EAAE,EAAE;IACbC,SAAS,EAAE;EACb,CAAC,CAAC;;EAEF;EACA,MAAM,CAACC,WAAW,EAAEC,cAAc,CAAC,GAAGnG,QAAQ,CAAC;IAC7CoG,QAAQ,EAAE,IAAI;IACdC,GAAG,EAAE,IAAI;IACTC,MAAM,EAAE,IAAI;IACZC,SAAS,EAAE,IAAI;IACfC,eAAe,EAAE,IAAI;IACrBC,WAAW,EAAE;EACf,CAAC,CAAC;;EAEF;EACA,MAAM,CAACC,UAAU,EAAEC,aAAa,CAAC,GAAG3G,QAAQ,CAAC,IAAI,CAAC;;EAElD;EACAC,SAAS,CAAC,MAAM;IACd,MAAM2G,cAAc,GAAG,MAAAA,CAAA,KAAY;MACjC7B,UAAU,CAAC,IAAI,CAAC;MAChB,IAAI;QACF;QACA,MAAM8B,eAAe,GAAG/C,aAAa,CAACgD,iBAAiB,CAAClC,UAAU,EAAE,OAAO,CAAC,CACzEmC,KAAK,CAACC,GAAG,IAAI;UACZC,OAAO,CAACjC,KAAK,CAAC,gCAAgC,EAAEgC,GAAG,CAAC;UACpD,OAAO;YAAEE,OAAO,EAAE;UAAK,CAAC;QAC1B,CAAC,CAAC;QAEJ,MAAMC,UAAU,GAAGrD,aAAa,CAACsD,mBAAmB,CAACxC,UAAU,EAAE,OAAO,CAAC,CACtEmC,KAAK,CAACC,GAAG,IAAI;UACZC,OAAO,CAACjC,KAAK,CAAC,2BAA2B,EAAEgC,GAAG,CAAC;UAC/C,OAAO;YAAEE,OAAO,EAAE;UAAK,CAAC;QAC1B,CAAC,CAAC;QAEJ,MAAMG,aAAa,GAAGvD,aAAa,CAACwD,eAAe,CAAC1C,UAAU,EAAE,OAAO,CAAC,CACrEmC,KAAK,CAACC,GAAG,IAAI;UACZC,OAAO,CAACjC,KAAK,CAAC,8BAA8B,EAAEgC,GAAG,CAAC;UAClD,OAAO;YAAEE,OAAO,EAAE;UAAK,CAAC;QAC1B,CAAC,CAAC;QAEJ,MAAMK,gBAAgB,GAAGzD,aAAa,CAAC0D,kBAAkB,CAAC5C,UAAU,EAAE,OAAO,CAAC,CAC3EmC,KAAK,CAACC,GAAG,IAAI;UACZC,OAAO,CAACjC,KAAK,CAAC,kCAAkC,EAAEgC,GAAG,CAAC;UACtD,OAAO;YAAEE,OAAO,EAAE;UAAK,CAAC;QAC1B,CAAC,CAAC;;QAEJ;QACA,MAAM,CAACO,YAAY,EAAEC,OAAO,EAAEC,UAAU,EAAEC,aAAa,CAAC,GAAG,MAAMC,OAAO,CAACC,GAAG,CAAC,CAC3EjB,eAAe,EACfM,UAAU,EACVE,aAAa,EACbE,gBAAgB,CACjB,CAAC;;QAEF;QACApB,cAAc,CAAC;UACbC,QAAQ,EAAEqB,YAAY,CAACP,OAAO;UAC9Bb,GAAG,EAAEqB,OAAO,CAACR,OAAO;UACpBZ,MAAM,EAAEqB,UAAU,CAACT,OAAO;UAC1BX,SAAS,EAAEqB,aAAa,CAACV,OAAO;UAChCV,eAAe,EAAE,IAAI;UACrBC,WAAW,EAAE;QACf,CAAC,CAAC;;QAEF;QACA,IAAIgB,YAAY,CAACP,OAAO,IAAIQ,OAAO,CAACR,OAAO,IAAIS,UAAU,CAACT,OAAO,IAAIU,aAAa,CAACV,OAAO,EAAE;UAC1FjC,QAAQ,CAAC,IAAI,CAAC;QAChB,CAAC,MAAM;UACLA,QAAQ,CAAC,uDAAuD,CAAC;QACnE;MACF,CAAC,CAAC,OAAO+B,GAAG,EAAE;QACZ;QACAC,OAAO,CAACjC,KAAK,CAAC,mCAAmC,EAAEgC,GAAG,CAAC;QACvD/B,QAAQ,CAAC,uDAAuD,CAAC;MACnE,CAAC,SAAS;QACRF,UAAU,CAAC,KAAK,CAAC;MACnB;IACF,CAAC;IAED,IAAIH,UAAU,EAAE;MACdgC,cAAc,CAAC,CAAC;IAClB;EACF,CAAC,EAAE,CAAChC,UAAU,CAAC,CAAC;;EAEhB;EACA,MAAMmD,WAAW,GAAG,CAClB;IACEC,EAAE,EAAE,UAAU;IACdC,KAAK,EAAE,oBAAoB;IAC3BC,WAAW,EAAE,2FAA2F;IACxGC,IAAI,eAAE7D,OAAA,CAACvC,cAAc;MAAAqG,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC;IACxBC,KAAK,EAAE,SAAS;IAChBC,QAAQ,EAAE,CAAC,yBAAyB,EAAE,2BAA2B,EAAE,qBAAqB,EAAE,yBAAyB;EACrH,CAAC,EACD;IACET,EAAE,EAAE,KAAK;IACTC,KAAK,EAAE,oBAAoB;IAC3BC,WAAW,EAAE,wEAAwE;IACrFC,IAAI,eAAE7D,OAAA,CAAC/B,QAAQ;MAAA6F,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC;IAClBC,KAAK,EAAE,WAAW;IAClBC,QAAQ,EAAE,CAAC,yBAAyB,EAAE,0BAA0B,EAAE,qBAAqB,EAAE,eAAe;EAC1G,CAAC,EACD;IACET,EAAE,EAAE,QAAQ;IACZC,KAAK,EAAE,wBAAwB;IAC/BC,WAAW,EAAE,uEAAuE;IACpFC,IAAI,eAAE7D,OAAA,CAACjB,aAAa;MAAA+E,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC;IACvBC,KAAK,EAAE,SAAS;IAChBC,QAAQ,EAAE,CAAC,qBAAqB,EAAE,sBAAsB,EAAE,oBAAoB,EAAE,iBAAiB;EACnG,CAAC,EACD;IACET,EAAE,EAAE,kBAAkB;IACtBC,KAAK,EAAE,yBAAyB;IAChCC,WAAW,EAAE,yEAAyE;IACtFC,IAAI,eAAE7D,OAAA,CAACnB,SAAS;MAAAiF,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC;IACnBC,KAAK,EAAE,MAAM;IACbC,QAAQ,EAAE,CAAC,kBAAkB,EAAE,gBAAgB,EAAE,oBAAoB,EAAE,oBAAoB;EAC7F,CAAC,EACD;IACET,EAAE,EAAE,cAAc;IAClBC,KAAK,EAAE,yBAAyB;IAChCC,WAAW,EAAE,4DAA4D;IACzEC,IAAI,eAAE7D,OAAA,CAACjC,YAAY;MAAA+F,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC;IACtBC,KAAK,EAAE,SAAS;IAChBC,QAAQ,EAAE,CAAC,iBAAiB,EAAE,wBAAwB,EAAE,oBAAoB,EAAE,mBAAmB;EACnG,CAAC,EACD;IACET,EAAE,EAAE,YAAY;IAChBC,KAAK,EAAE,uBAAuB;IAC9BC,WAAW,EAAE,iFAAiF;IAC9FC,IAAI,eAAE7D,OAAA,CAACrC,YAAY;MAAAmG,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC;IACtBC,KAAK,EAAE,OAAO;IACdC,QAAQ,EAAE,CAAC,gBAAgB,EAAE,2BAA2B,EAAE,eAAe,EAAE,kBAAkB;EAC/F,CAAC,CACF;;EAED;EACA,MAAMC,wBAAwB,GAAG,MAAAA,CAAOC,UAAU,EAAEC,MAAM,KAAK;IAC7D,IAAI;MACF7D,UAAU,CAAC,IAAI,CAAC;MAChBE,QAAQ,CAAC,IAAI,CAAC;MAEd,IAAI4D,QAAQ;MAEZ,QAAQF,UAAU;QAChB,KAAK,UAAU;UACbE,QAAQ,GAAG,MAAM/E,aAAa,CAACgD,iBAAiB,CAAClC,UAAU,EAAEgE,MAAM,CAAC;UACpE;QACF,KAAK,KAAK;UACRC,QAAQ,GAAG,MAAM/E,aAAa,CAACsD,mBAAmB,CAACxC,UAAU,EAAEgE,MAAM,CAAC;UACtE;QACF,KAAK,QAAQ;UACXC,QAAQ,GAAG,MAAM/E,aAAa,CAACwD,eAAe,CAAC1C,UAAU,EAAEgE,MAAM,CAAC;UAClE;QACF,KAAK,YAAY;UACfC,QAAQ,GAAG,MAAM/E,aAAa,CAAC0D,kBAAkB,CAAC5C,UAAU,EAAEgE,MAAM,CAAC;UACrE;QACF,KAAK,kBAAkB;UACrB,IAAI,CAAChD,QAAQ,CAACK,SAAS,EAAE;YACvBhB,QAAQ,CAAC,8BAA8B,CAAC;YACxC;UACF;UACA4D,QAAQ,GAAG,MAAM/E,aAAa,CAACgF,eAAe,CAAClE,UAAU,EAAEgB,QAAQ,CAACK,SAAS,EAAE2C,MAAM,CAAC;UACtF;QACF,KAAK,cAAc;UACjB,IAAI,CAAChD,QAAQ,CAACG,WAAW,IAAI,CAACH,QAAQ,CAACI,SAAS,EAAE;YAChDf,QAAQ,CAAC,4CAA4C,CAAC;YACtD;UACF;UACA4D,QAAQ,GAAG,MAAM/E,aAAa,CAACiF,uBAAuB,CACpDnE,UAAU,EACVgB,QAAQ,CAACG,WAAW,EACpBH,QAAQ,CAACI,SAAS,EAClB4C,MACF,CAAC;UACD;QACF;UACE,MAAM,IAAII,KAAK,CAAC,iCAAiC,CAAC;MACtD;MAEA,IAAIJ,MAAM,KAAK,OAAO,EAAE;QACtB;QACA,IAAID,UAAU,KAAK,kBAAkB,IAAIA,UAAU,KAAK,cAAc,EAAE;UACtExC,cAAc,CAAC8C,IAAI,KAAK;YACtB,GAAGA,IAAI;YACP,CAACN,UAAU,KAAK,kBAAkB,GAAG,iBAAiB,GAAG,aAAa,GAAGE,QAAQ,CAAC3B;UACpF,CAAC,CAAC,CAAC;QACL;QACA/B,aAAa,CAAC0D,QAAQ,CAAC3B,OAAO,CAAC;MACjC,CAAC,MAAM;QACL;QACA,IAAI2B,QAAQ,CAACK,QAAQ,EAAE;UACrBC,MAAM,CAACC,IAAI,CAACP,QAAQ,CAACK,QAAQ,EAAE,QAAQ,CAAC;QAC1C;MACF;IACF,CAAC,CAAC,OAAOlC,GAAG,EAAE;MACZC,OAAO,CAACjC,KAAK,CAAC,sCAAsC,EAAEgC,GAAG,CAAC;MAC1D/B,QAAQ,CAAC+B,GAAG,CAACqC,MAAM,IAAIrC,GAAG,CAACsC,OAAO,IAAI,0CAA0C,CAAC;IACnF,CAAC,SAAS;MACRvE,UAAU,CAAC,KAAK,CAAC;IACnB;EACF,CAAC;EAED,MAAMwE,kBAAkB,GAAIZ,UAAU,IAAK;IACzCtD,iBAAiB,CAACsD,UAAU,CAAC;IAC7BlD,aAAa,CAACkD,UAAU,CAACX,EAAE,CAAC;;IAE5B;IACA,IAAIW,UAAU,CAACX,EAAE,KAAK,cAAc,IAAIW,UAAU,CAACX,EAAE,KAAK,kBAAkB,EAAE;MAC5E;MACA,IAAIW,UAAU,CAACX,EAAE,KAAK,cAAc,EAAE;QACpC,MAAMwB,KAAK,GAAG,IAAIC,IAAI,CAAC,CAAC;QACxB,MAAMC,SAAS,GAAG,IAAID,IAAI,CAAC,CAAC;QAC5BC,SAAS,CAACC,QAAQ,CAACH,KAAK,CAACI,QAAQ,CAAC,CAAC,GAAG,CAAC,CAAC;QAExC/D,WAAW,CAAC;UACV,GAAGD,QAAQ;UACXG,WAAW,EAAE2D,SAAS,CAACG,WAAW,CAAC,CAAC,CAACC,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC;UAClD9D,SAAS,EAAEwD,KAAK,CAACK,WAAW,CAAC,CAAC,CAACC,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC;QAC7C,CAAC,CAAC;MACJ;MAEAvE,aAAa,CAAC,IAAI,CAAC;IACrB,CAAC,MAAM;MACL;MACAmD,wBAAwB,CAACC,UAAU,CAACX,EAAE,EAAE,OAAO,CAAC;IAClD;EACF,CAAC;EAED,MAAM+B,oBAAoB,GAAG,MAAAA,CAAA,KAAY;IACvC,MAAMrB,wBAAwB,CAAClD,UAAU,EAAEI,QAAQ,CAACE,OAAO,CAAC;IAC5DP,aAAa,CAAC,KAAK,CAAC;EACtB,CAAC;EAED,MAAMyE,iBAAiB,GAAGA,CAAA,KAAM;IAC9BzE,aAAa,CAAC,KAAK,CAAC;IACpBN,QAAQ,CAAC,IAAI,CAAC;IACdY,WAAW,CAAC;MACVC,OAAO,EAAE,OAAO;MAChBC,WAAW,EAAE,EAAE;MACfC,SAAS,EAAE,EAAE;MACbC,SAAS,EAAE;IACb,CAAC,CAAC;EACJ,CAAC;EAED,MAAMgE,mBAAmB,GAAGA,CAAA,KAAM;IAChC,IAAI,CAAC/E,UAAU,EAAE,OAAO,IAAI;IAE5B,oBACEZ,OAAA,CAAClE,KAAK;MAAC8J,EAAE,EAAE;QAAEC,CAAC,EAAE,CAAC;QAAEC,EAAE,EAAE;MAAE,CAAE;MAAAC,QAAA,gBACzB/F,OAAA,CAACpE,GAAG;QAACgK,EAAE,EAAE;UAAEI,OAAO,EAAE,MAAM;UAAEC,cAAc,EAAE,eAAe;UAAEC,UAAU,EAAE,QAAQ;UAAEC,EAAE,EAAE;QAAE,CAAE;QAAAJ,QAAA,gBACzF/F,OAAA,CAACnE,UAAU;UAACuK,OAAO,EAAC,IAAI;UAAAL,QAAA,GACrBjF,cAAc,aAAdA,cAAc,uBAAdA,cAAc,CAAE6C,KAAK,EAAC,KAAG,EAAC/C,UAAU,CAACyF,aAAa;QAAA;UAAAvC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACzC,CAAC,eACbjE,OAAA,CAACpE,GAAG;UAACgK,EAAE,EAAE;YAAEI,OAAO,EAAE,MAAM;YAAEM,GAAG,EAAE;UAAE,CAAE;UAAAP,QAAA,gBAEnC/F,OAAA,CAAC7D,MAAM;YACLoK,SAAS,eAAEvG,OAAA,CAAC7B,YAAY;cAAA2F,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAE;YAC5BuC,OAAO,EAAEA,CAAA,KAAMpC,wBAAwB,CAAClD,UAAU,EAAE,KAAK,CAAE;YAC3DkF,OAAO,EAAC,UAAU;YAClBK,IAAI,EAAC,OAAO;YACZvC,KAAK,EAAC,SAAS;YAAA6B,QAAA,EAChB;UAED;YAAAjC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC,eACTjE,OAAA,CAAC7D,MAAM;YACLoK,SAAS,eAAEvG,OAAA,CAAC7B,YAAY;cAAA2F,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAE;YAC5BuC,OAAO,EAAEA,CAAA,KAAMpC,wBAAwB,CAAClD,UAAU,EAAE,OAAO,CAAE;YAC7DkF,OAAO,EAAC,UAAU;YAClBK,IAAI,EAAC,OAAO;YACZvC,KAAK,EAAC,SAAS;YAAA6B,QAAA,EAChB;UAED;YAAAjC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC,eACTjE,OAAA,CAAC7D,MAAM;YACLoK,SAAS,eAAEvG,OAAA,CAACzB,WAAW;cAAAuF,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAE;YAC3BuC,OAAO,EAAEA,CAAA,KAAM3F,aAAa,CAAC,IAAI,CAAE;YACnCuF,OAAO,EAAC,UAAU;YAClBK,IAAI,EAAC,OAAO;YAAAV,QAAA,EACb;UAED;YAAAjC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACN,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,eAENjE,OAAA,CAACzD,OAAO;QAACqJ,EAAE,EAAE;UAAEO,EAAE,EAAE;QAAE;MAAE;QAAArC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC,EAGzB/C,UAAU,KAAK,UAAU,IAAIwF,oBAAoB,CAAC9F,UAAU,CAAC,EAC7DM,UAAU,KAAK,KAAK,IAAIyF,eAAe,CAAC/F,UAAU,CAAC,EACnDM,UAAU,KAAK,QAAQ,IAAI0F,kBAAkB,CAAChG,UAAU,CAAC,EACzDM,UAAU,KAAK,kBAAkB,IAAI2F,2BAA2B,CAACjG,UAAU,CAAC,EAC5EM,UAAU,KAAK,cAAc,IAAI4F,uBAAuB,CAAClG,UAAU,CAAC,EACpEM,UAAU,KAAK,YAAY,IAAI6F,qBAAqB,CAACnG,UAAU,CAAC;IAAA;MAAAkD,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAC5D,CAAC;EAEZ,CAAC;EAED,MAAMyC,oBAAoB,GAAIM,IAAI,iBAChChH,OAAA,CAACpE,GAAG;IAAAmK,QAAA,gBAEF/F,OAAA,CAACpE,GAAG;MAACgK,EAAE,EAAE;QAAEI,OAAO,EAAE,MAAM;QAAEC,cAAc,EAAE,UAAU;QAAEC,UAAU,EAAE,QAAQ;QAAEC,EAAE,EAAE;MAAE,CAAE;MAAAJ,QAAA,eACpF/F,OAAA,CAACzC,gBAAgB;QACf0J,OAAO,eACLjH,OAAA,CAAC1C,MAAM;UACL4J,OAAO,EAAE9E,UAAW;UACpB+E,QAAQ,EAAGC,CAAC,IAAK/E,aAAa,CAAC+E,CAAC,CAACC,MAAM,CAACH,OAAO,CAAE;UACjDhD,KAAK,EAAC;QAAS;UAAAJ,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAChB,CACF;QACDqD,KAAK,eACHtH,OAAA,CAACpE,GAAG;UAACgK,EAAE,EAAE;YAAEI,OAAO,EAAE,MAAM;YAAEE,UAAU,EAAE;UAAS,CAAE;UAAAH,QAAA,gBACjD/F,OAAA,CAACb,aAAa;YAACyG,EAAE,EAAE;cAAE2B,EAAE,EAAE;YAAE;UAAE;YAAAzD,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,WAElC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAK;MACN;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACF;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACC,CAAC,eAGNjE,OAAA,CAAClE,KAAK;MAAC8J,EAAE,EAAE;QAAEC,CAAC,EAAE,CAAC;QAAEM,EAAE,EAAE,CAAC;QAAEqB,MAAM,EAAE;MAAoB,CAAE;MAAAzB,QAAA,eACtD/F,OAAA,CAACjE,IAAI;QAAC0L,SAAS;QAACC,OAAO,EAAE,CAAE;QAAA3B,QAAA,gBACzB/F,OAAA,CAACjE,IAAI;UAAC4L,IAAI;UAACC,EAAE,EAAE,EAAG;UAACC,EAAE,EAAE,CAAE;UAAA9B,QAAA,eACvB/F,OAAA,CAACpE,GAAG;YAACgK,EAAE,EAAE;cAAEkC,SAAS,EAAE,QAAQ;cAAEjC,CAAC,EAAE;YAAE,CAAE;YAAAE,QAAA,gBACrC/F,OAAA,CAACnE,UAAU;cAACuK,OAAO,EAAC,IAAI;cAACR,EAAE,EAAE;gBAAEmC,UAAU,EAAE,GAAG;gBAAE7D,KAAK,EAAE,SAAS;gBAAEiC,EAAE,EAAE;cAAI,CAAE;cAAAJ,QAAA,GACzEiB,IAAI,CAACgB,YAAY,EAAC,GACrB;YAAA;cAAAlE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAY,CAAC,eACbjE,OAAA,CAACnE,UAAU;cAACuK,OAAO,EAAC,SAAS;cAACR,EAAE,EAAE;gBAAE1B,KAAK,EAAE,MAAM;gBAAE+D,aAAa,EAAE,WAAW;gBAAEC,aAAa,EAAE;cAAI,CAAE;cAAAnC,QAAA,EAAC;YAErG;cAAAjC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAY,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACV;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACF,CAAC,eACPjE,OAAA,CAACjE,IAAI;UAAC4L,IAAI;UAACC,EAAE,EAAE,EAAG;UAACC,EAAE,EAAE,CAAE;UAAA9B,QAAA,eACvB/F,OAAA,CAACpE,GAAG;YAACgK,EAAE,EAAE;cAAEkC,SAAS,EAAE,QAAQ;cAAEjC,CAAC,EAAE;YAAE,CAAE;YAAAE,QAAA,gBACrC/F,OAAA,CAACnE,UAAU;cAACuK,OAAO,EAAC,IAAI;cAACR,EAAE,EAAE;gBAAEmC,UAAU,EAAE,GAAG;gBAAE7D,KAAK,EAAE,SAAS;gBAAEiC,EAAE,EAAE;cAAI,CAAE;cAAAJ,QAAA,GACzEiB,IAAI,CAACmB,YAAY,EAAC,GACrB;YAAA;cAAArE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAY,CAAC,eACbjE,OAAA,CAACnE,UAAU;cAACuK,OAAO,EAAC,SAAS;cAACR,EAAE,EAAE;gBAAE1B,KAAK,EAAE,MAAM;gBAAE+D,aAAa,EAAE,WAAW;gBAAEC,aAAa,EAAE;cAAI,CAAE;cAAAnC,QAAA,GAAC,gBACrF,EAACiB,IAAI,CAACoB,uBAAuB,EAAC,IAC9C;YAAA;cAAAtE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAY,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACV;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACF,CAAC,eACPjE,OAAA,CAACjE,IAAI;UAAC4L,IAAI;UAACC,EAAE,EAAE,EAAG;UAACC,EAAE,EAAE,CAAE;UAAA9B,QAAA,eACvB/F,OAAA,CAACpE,GAAG;YAACgK,EAAE,EAAE;cAAEkC,SAAS,EAAE,QAAQ;cAAEjC,CAAC,EAAE;YAAE,CAAE;YAAAE,QAAA,gBACrC/F,OAAA,CAACnE,UAAU;cAACuK,OAAO,EAAC,IAAI;cAACR,EAAE,EAAE;gBAAEmC,UAAU,EAAE,GAAG;gBAAE7D,KAAK,EAAE,SAAS;gBAAEiC,EAAE,EAAE;cAAI,CAAE;cAAAJ,QAAA,GACzEiB,IAAI,CAACqB,eAAe,EAAC,GACxB;YAAA;cAAAvE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAY,CAAC,eACbjE,OAAA,CAACnE,UAAU;cAACuK,OAAO,EAAC,SAAS;cAACR,EAAE,EAAE;gBAAE1B,KAAK,EAAE,MAAM;gBAAE+D,aAAa,EAAE,WAAW;gBAAEC,aAAa,EAAE;cAAI,CAAE;cAAAnC,QAAA,GAAC,mBAClF,EAAC,GAAG,GAAGiB,IAAI,CAACoB,uBAAuB,EAAC,IACvD;YAAA;cAAAtE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAY,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACV;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACF,CAAC,eACPjE,OAAA,CAACjE,IAAI;UAAC4L,IAAI;UAACC,EAAE,EAAE,EAAG;UAACC,EAAE,EAAE,CAAE;UAAA9B,QAAA,eACvB/F,OAAA,CAACpE,GAAG;YAACgK,EAAE,EAAE;cAAEkC,SAAS,EAAE,QAAQ;cAAEjC,CAAC,EAAE;YAAE,CAAE;YAAAE,QAAA,gBACrC/F,OAAA,CAACnE,UAAU;cAACuK,OAAO,EAAC,IAAI;cAACR,EAAE,EAAE;gBAAEmC,UAAU,EAAE,GAAG;gBAAE7D,KAAK,EAAE,SAAS;gBAAEiC,EAAE,EAAE;cAAI,CAAE;cAAAJ,QAAA,GACzEiB,IAAI,CAACsB,iBAAiB,EAAC,GAC1B;YAAA;cAAAxE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAY,CAAC,eACbjE,OAAA,CAACnE,UAAU;cAACuK,OAAO,EAAC,SAAS;cAACR,EAAE,EAAE;gBAAE1B,KAAK,EAAE,MAAM;gBAAE+D,aAAa,EAAE,WAAW;gBAAEC,aAAa,EAAE;cAAI,CAAE;cAAAnC,QAAA,GAAC,cAEnG,EAACiB,IAAI,CAACuB,cAAc,IAAI,KAAKvB,IAAI,CAACuB,cAAc,kBAAkB;YAAA;cAAAzE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACxD,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACV;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACF,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACF,CAAC,EAGP7B,UAAU,iBACTpC,OAAA,CAACpE,GAAG;MAACgK,EAAE,EAAE;QAAEO,EAAE,EAAE;MAAE,CAAE;MAAAJ,QAAA,eACjB/F,OAAA,CAACN,aAAa;QAACsH,IAAI,EAAEA;MAAK;QAAAlD,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAC1B,CACN,eAGDjE,OAAA,CAAClE,KAAK;MAAC8J,EAAE,EAAE;QAAEC,CAAC,EAAE,CAAC;QAAEM,EAAE,EAAE,CAAC;QAAEqB,MAAM,EAAE;MAAoB,CAAE;MAAAzB,QAAA,gBACtD/F,OAAA,CAACnE,UAAU;QAACuK,OAAO,EAAC,WAAW;QAACR,EAAE,EAAE;UAAEmC,UAAU,EAAE,GAAG;UAAE5B,EAAE,EAAE,CAAC;UAAEjC,KAAK,EAAE;QAAU,CAAE;QAAA6B,QAAA,EAAC;MAElF;QAAAjC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAY,CAAC,eACbjE,OAAA,CAACpE,GAAG;QAACgK,EAAE,EAAE;UACP4B,MAAM,EAAE,mBAAmB;UAC3BgB,YAAY,EAAE,CAAC;UACfC,QAAQ,EAAE;QACZ,CAAE;QAAA1C,QAAA,eACA/F,OAAA;UAAO0I,KAAK,EAAE;YAAEC,KAAK,EAAE,MAAM;YAAEC,cAAc,EAAE;UAAW,CAAE;UAAA7C,QAAA,gBAC1D/F,OAAA;YAAA+F,QAAA,eACE/F,OAAA;cAAI0I,KAAK,EAAE;gBAAEG,eAAe,EAAE;cAAU,CAAE;cAAA9C,QAAA,gBACxC/F,OAAA;gBAAI0I,KAAK,EAAE;kBACTI,OAAO,EAAE,UAAU;kBACnBhB,SAAS,EAAE,MAAM;kBACjBiB,QAAQ,EAAE,MAAM;kBAChBhB,UAAU,EAAE,GAAG;kBACf7D,KAAK,EAAE,SAAS;kBAChB8E,YAAY,EAAE,mBAAmB;kBACjCL,KAAK,EAAE;gBACT,CAAE;gBAAA5C,QAAA,EAAC;cAAW;gBAAAjC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eACnBjE,OAAA;gBAAI0I,KAAK,EAAE;kBACTI,OAAO,EAAE,UAAU;kBACnBhB,SAAS,EAAE,MAAM;kBACjBiB,QAAQ,EAAE,MAAM;kBAChBhB,UAAU,EAAE,GAAG;kBACf7D,KAAK,EAAE,SAAS;kBAChB8E,YAAY,EAAE,mBAAmB;kBACjCL,KAAK,EAAE;gBACT,CAAE;gBAAA5C,QAAA,EAAC;cAAW;gBAAAjC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eACnBjE,OAAA;gBAAI0I,KAAK,EAAE;kBACTI,OAAO,EAAE,UAAU;kBACnBhB,SAAS,EAAE,MAAM;kBACjBiB,QAAQ,EAAE,MAAM;kBAChBhB,UAAU,EAAE,GAAG;kBACf7D,KAAK,EAAE,SAAS;kBAChB8E,YAAY,EAAE,mBAAmB;kBACjCL,KAAK,EAAE;gBACT,CAAE;gBAAA5C,QAAA,EAAC;cAAiB;gBAAAjC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eACzBjE,OAAA;gBAAI0I,KAAK,EAAE;kBACTI,OAAO,EAAE,UAAU;kBACnBhB,SAAS,EAAE,MAAM;kBACjBiB,QAAQ,EAAE,MAAM;kBAChBhB,UAAU,EAAE,GAAG;kBACf7D,KAAK,EAAE,SAAS;kBAChB8E,YAAY,EAAE,mBAAmB;kBACjCL,KAAK,EAAE;gBACT,CAAE;gBAAA5C,QAAA,EAAC;cAAsB;gBAAAjC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC5B;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACA,CAAC,eACRjE,OAAA;YAAA+F,QAAA,eACE/F,OAAA;cAAA+F,QAAA,gBACE/F,OAAA;gBAAI0I,KAAK,EAAE;kBACTI,OAAO,EAAE,MAAM;kBACfC,QAAQ,EAAE,MAAM;kBAChBjB,SAAS,EAAE;gBACb,CAAE;gBAAA/B,QAAA,gBACA/F,OAAA,CAACnE,UAAU;kBAACuK,OAAO,EAAC,IAAI;kBAACR,EAAE,EAAE;oBAAEmC,UAAU,EAAE,GAAG;oBAAE7D,KAAK,EAAE,SAAS;oBAAEiC,EAAE,EAAE;kBAAI,CAAE;kBAAAJ,QAAA,EACzEiB,IAAI,CAACiC;gBAAW;kBAAAnF,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACP,CAAC,eACbjE,OAAA,CAACnE,UAAU;kBAACuK,OAAO,EAAC,SAAS;kBAACR,EAAE,EAAE;oBAAE1B,KAAK,EAAE;kBAAO,CAAE;kBAAA6B,QAAA,EAAC;gBAErD;kBAAAjC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAY,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACX,CAAC,eACLjE,OAAA;gBAAI0I,KAAK,EAAE;kBACTI,OAAO,EAAE,MAAM;kBACfC,QAAQ,EAAE,MAAM;kBAChBjB,SAAS,EAAE;gBACb,CAAE;gBAAA/B,QAAA,gBACA/F,OAAA,CAACnE,UAAU;kBAACuK,OAAO,EAAC,IAAI;kBAACR,EAAE,EAAE;oBAAEmC,UAAU,EAAE,GAAG;oBAAE7D,KAAK,EAAE,SAAS;oBAAEiC,EAAE,EAAE;kBAAI,CAAE;kBAAAJ,QAAA,EACzEiB,IAAI,CAACkC;gBAAW;kBAAApF,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACP,CAAC,eACbjE,OAAA,CAACnE,UAAU;kBAACuK,OAAO,EAAC,SAAS;kBAACR,EAAE,EAAE;oBAAE1B,KAAK,EAAE;kBAAO,CAAE;kBAAA6B,QAAA,GACjDiB,IAAI,CAACmC,gBAAgB,EAAC,cACzB;gBAAA;kBAAArF,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAY,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACX,CAAC,eACLjE,OAAA;gBAAI0I,KAAK,EAAE;kBACTI,OAAO,EAAE,MAAM;kBACfC,QAAQ,EAAE,MAAM;kBAChBjB,SAAS,EAAE;gBACb,CAAE;gBAAA/B,QAAA,gBACA/F,OAAA,CAACnE,UAAU;kBAACuK,OAAO,EAAC,IAAI;kBAACR,EAAE,EAAE;oBAAEmC,UAAU,EAAE,GAAG;oBAAE7D,KAAK,EAAE,SAAS;oBAAEiC,EAAE,EAAE;kBAAI,CAAE;kBAAAJ,QAAA,GACzEiB,IAAI,CAACsB,iBAAiB,EAAC,GAC1B;gBAAA;kBAAAxE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAY,CAAC,eACbjE,OAAA,CAACnE,UAAU;kBAACuK,OAAO,EAAC,SAAS;kBAACR,EAAE,EAAE;oBAAE1B,KAAK,EAAE;kBAAO,CAAE;kBAAA6B,QAAA,EAAC;gBAErD;kBAAAjC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAY,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACX,CAAC,eACLjE,OAAA;gBAAI0I,KAAK,EAAE;kBACTI,OAAO,EAAE,MAAM;kBACfC,QAAQ,EAAE,MAAM;kBAChBjB,SAAS,EAAE;gBACb,CAAE;gBAAA/B,QAAA,EACCiB,IAAI,CAACuB,cAAc,gBAClBvI,OAAA,CAAAE,SAAA;kBAAA6F,QAAA,gBACE/F,OAAA,CAACnE,UAAU;oBAACuK,OAAO,EAAC,OAAO;oBAACR,EAAE,EAAE;sBAAEmC,UAAU,EAAE,GAAG;sBAAE7D,KAAK,EAAE,SAAS;sBAAEiC,EAAE,EAAE;oBAAI,CAAE;oBAAAJ,QAAA,EAC5EiB,IAAI,CAACoC;kBAAkB;oBAAAtF,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACd,CAAC,eACbjE,OAAA,CAACnE,UAAU;oBAACuK,OAAO,EAAC,SAAS;oBAACR,EAAE,EAAE;sBAAE1B,KAAK,EAAE;oBAAO,CAAE;oBAAA6B,QAAA,GAAC,GAClD,EAACiB,IAAI,CAACuB,cAAc,EAAC,UACxB;kBAAA;oBAAAzE,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAY,CAAC;gBAAA,eACb,CAAC,gBAEHjE,OAAA,CAACnE,UAAU;kBAACuK,OAAO,EAAC,SAAS;kBAACR,EAAE,EAAE;oBAAE1B,KAAK,EAAE;kBAAO,CAAE;kBAAA6B,QAAA,EAAC;gBAErD;kBAAAjC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAY;cACb;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACC,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACA,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACL,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACD,CAAC,EAGP+C,IAAI,CAACqC,YAAY,IAAIrC,IAAI,CAACqC,YAAY,CAACC,MAAM,GAAG,CAAC,iBAChDtJ,OAAA,CAAClE,KAAK;MAAC8J,EAAE,EAAE;QAAEC,CAAC,EAAE,CAAC;QAAE2B,MAAM,EAAE;MAAoB,CAAE;MAAAzB,QAAA,gBAC/C/F,OAAA,CAACnE,UAAU;QAACuK,OAAO,EAAC,WAAW;QAACR,EAAE,EAAE;UAAEmC,UAAU,EAAE,GAAG;UAAE5B,EAAE,EAAE,CAAC;UAAEjC,KAAK,EAAE;QAAU,CAAE;QAAA6B,QAAA,EAAC;MAElF;QAAAjC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAY,CAAC,eACbjE,OAAA,CAACP,eAAe;QACduH,IAAI,EAAEA,IAAI,CAACqC,YAAY,CAACE,GAAG,CAACC,IAAI,KAAK;UACnCxC,IAAI,EAAEwC,IAAI,CAACxC,IAAI;UACfyC,KAAK,EAAE,GAAGD,IAAI,CAACC,KAAK;QACtB,CAAC,CAAC,CAAE;QACJC,OAAO,EAAE,CACP;UAAEC,KAAK,EAAE,MAAM;UAAEC,UAAU,EAAE,MAAM;UAAEjB,KAAK,EAAE;QAAI,CAAC,EACjD;UAAEgB,KAAK,EAAE,OAAO;UAAEC,UAAU,EAAE,cAAc;UAAEjB,KAAK,EAAE,GAAG;UAAEkB,KAAK,EAAE;QAAQ,CAAC,CAC1E;QACFC,UAAU,EAAE9C,IAAI,CAACqC,YAAY,CAACC,MAAM,GAAG,CAAE;QACzCS,QAAQ,EAAE;MAAE;QAAAjG,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACb,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACG,CACR;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACE,CACN;EAED,MAAM0C,eAAe,GAAIK,IAAI,iBAC3BhH,OAAA,CAACpE,GAAG;IAAAmK,QAAA,gBAEF/F,OAAA,CAACpE,GAAG;MAACgK,EAAE,EAAE;QAAEI,OAAO,EAAE,MAAM;QAAEC,cAAc,EAAE,eAAe;QAAEC,UAAU,EAAE,QAAQ;QAAEC,EAAE,EAAE;MAAE,CAAE;MAAAJ,QAAA,gBACzF/F,OAAA,CAACnE,UAAU;QAACuK,OAAO,EAAC,IAAI;QAACR,EAAE,EAAE;UAAEmC,UAAU,EAAE,GAAG;UAAE7D,KAAK,EAAE;QAAiB,CAAE;QAAA6B,QAAA,EAAC;MAE3E;QAAAjC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAY,CAAC,eACbjE,OAAA,CAACzC,gBAAgB;QACf0J,OAAO,eACLjH,OAAA,CAAC1C,MAAM;UACL4J,OAAO,EAAE9E,UAAW;UACpB+E,QAAQ,EAAGC,CAAC,IAAK/E,aAAa,CAAC+E,CAAC,CAACC,MAAM,CAACH,OAAO,CAAE;UACjDhD,KAAK,EAAC;QAAS;UAAAJ,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAChB,CACF;QACDqD,KAAK,eACHtH,OAAA,CAACpE,GAAG;UAACgK,EAAE,EAAE;YAAEI,OAAO,EAAE,MAAM;YAAEE,UAAU,EAAE;UAAS,CAAE;UAAAH,QAAA,gBACjD/F,OAAA,CAACb,aAAa;YAACyG,EAAE,EAAE;cAAE2B,EAAE,EAAE;YAAE;UAAE;YAAAzD,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,WAElC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAK;MACN;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACF,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACC,CAAC,EAGL7B,UAAU,iBACTpC,OAAA,CAACpE,GAAG;MAACgK,EAAE,EAAE;QAAEO,EAAE,EAAE;MAAE,CAAE;MAAAJ,QAAA,eACjB/F,OAAA,CAACJ,QAAQ;QAACoH,IAAI,EAAEA;MAAK;QAAAlD,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACrB,CACN,eAGDjE,OAAA,CAAClE,KAAK;MAAC8J,EAAE,EAAE;QAAEC,CAAC,EAAE,CAAC;QAAEM,EAAE,EAAE;MAAE,CAAE;MAAAJ,QAAA,gBACzB/F,OAAA,CAACnE,UAAU;QAACuK,OAAO,EAAC,IAAI;QAACR,EAAE,EAAE;UAAEO,EAAE,EAAE,CAAC;UAAE4B,UAAU,EAAE;QAAI,CAAE;QAAAhC,QAAA,EAAC;MAEzD;QAAAjC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAY,CAAC,eACbjE,OAAA,CAACP,eAAe;QACduH,IAAI,EAAEA,IAAI,CAACgD,aAAa,IAAI,EAAG;QAC/BN,OAAO,EAAE,CACP;UAAEC,KAAK,EAAE,WAAW;UAAEC,UAAU,EAAE,WAAW;UAAEjB,KAAK,EAAE;QAAI,CAAC,EAC3D;UAAEgB,KAAK,EAAE,SAAS;UAAEC,UAAU,EAAE,SAAS;UAAEjB,KAAK,EAAE;QAAI,CAAC,EACvD;UAAEgB,KAAK,EAAE,UAAU;UAAEC,UAAU,EAAE,MAAM;UAAEjB,KAAK,EAAE,EAAE;UAAEkB,KAAK,EAAE,OAAO;UAAEI,QAAQ,EAAE;QAAS,CAAC,EACxF;UAAEN,KAAK,EAAE,eAAe;UAAEC,UAAU,EAAE,eAAe;UAAEjB,KAAK,EAAE,GAAG;UAAEkB,KAAK,EAAE,OAAO;UAAEI,QAAQ,EAAE,QAAQ;UACnGC,UAAU,EAAGC,GAAG,IAAK,GAAGA,GAAG,CAACC,aAAa;QAAI,CAAC,EAChD;UAAET,KAAK,EAAE,aAAa;UAAEC,UAAU,EAAE,aAAa;UAAEjB,KAAK,EAAE,GAAG;UAAEkB,KAAK,EAAE,OAAO;UAAEI,QAAQ,EAAE,QAAQ;UAC/FC,UAAU,EAAGC,GAAG,IAAK,GAAGA,GAAG,CAACE,WAAW;QAAI,CAAC,EAC9C;UAAEV,KAAK,EAAE,iBAAiB;UAAEC,UAAU,EAAE,WAAW;UAAEjB,KAAK,EAAE,GAAG;UAAEkB,KAAK,EAAE,OAAO;UAAEI,QAAQ,EAAE,QAAQ;UACjGC,UAAU,EAAGC,GAAG,IAAK,GAAGA,GAAG,CAAC9B,eAAe;QAAI,CAAC,CAClD;QACF0B,QAAQ,EAAE;MAAG;QAAAjG,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACd,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACG,CAAC,eAGRjE,OAAA,CAAClE,KAAK;MAAC8J,EAAE,EAAE;QAAEC,CAAC,EAAE;MAAE,CAAE;MAAAE,QAAA,gBAClB/F,OAAA,CAACnE,UAAU;QAACuK,OAAO,EAAC,IAAI;QAACR,EAAE,EAAE;UAAEO,EAAE,EAAE,CAAC;UAAE4B,UAAU,EAAE;QAAI,CAAE;QAAAhC,QAAA,EAAC;MAEzD;QAAAjC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAY,CAAC,eACbjE,OAAA,CAACP,eAAe;QACduH,IAAI,EAAEA,IAAI,CAACsD,eAAe,IAAI,EAAG;QACjCZ,OAAO,EAAE,CACP;UAAEC,KAAK,EAAE,WAAW;UAAEC,UAAU,EAAE,WAAW;UAAEjB,KAAK,EAAE;QAAI,CAAC,EAC3D;UAAEgB,KAAK,EAAE,SAAS;UAAEC,UAAU,EAAE,SAAS;UAAEjB,KAAK,EAAE;QAAI,CAAC,EACvD;UAAEgB,KAAK,EAAE,YAAY;UAAEC,UAAU,EAAE,QAAQ;UAAEjB,KAAK,EAAE,GAAG;UAAEkB,KAAK,EAAE,OAAO;UAAEI,QAAQ,EAAE;QAAS,CAAC,EAC7F;UAAEN,KAAK,EAAE,mBAAmB;UAAEC,UAAU,EAAE,mBAAmB;UAAEjB,KAAK,EAAE,GAAG;UAAEkB,KAAK,EAAE,OAAO;UAAEI,QAAQ,EAAE,QAAQ;UAC3GC,UAAU,EAAGC,GAAG,IAAK,GAAGA,GAAG,CAACI,iBAAiB;QAAI,CAAC,CACpD;QACFR,QAAQ,EAAE;MAAG;QAAAjG,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACd,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACG,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACL,CACN;EAED,MAAM2C,kBAAkB,GAAII,IAAI,iBAC9BhH,OAAA,CAACpE,GAAG;IAAAmK,QAAA,gBAEF/F,OAAA,CAACpE,GAAG;MAACgK,EAAE,EAAE;QAAEI,OAAO,EAAE,MAAM;QAAEC,cAAc,EAAE,eAAe;QAAEC,UAAU,EAAE,QAAQ;QAAEC,EAAE,EAAE;MAAE,CAAE;MAAAJ,QAAA,gBACzF/F,OAAA,CAACnE,UAAU;QAACuK,OAAO,EAAC,IAAI;QAACR,EAAE,EAAE;UAAEmC,UAAU,EAAE,GAAG;UAAE7D,KAAK,EAAE;QAAe,CAAE;QAAA6B,QAAA,GAAC,0BAC/C,EAACiB,IAAI,CAACwD,aAAa,EAAC,UAC9C;MAAA;QAAA1G,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAY,CAAC,eACbjE,OAAA,CAACzC,gBAAgB;QACf0J,OAAO,eACLjH,OAAA,CAAC1C,MAAM;UACL4J,OAAO,EAAE9E,UAAW;UACpB+E,QAAQ,EAAGC,CAAC,IAAK/E,aAAa,CAAC+E,CAAC,CAACC,MAAM,CAACH,OAAO,CAAE;UACjDhD,KAAK,EAAC;QAAS;UAAAJ,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAChB,CACF;QACDqD,KAAK,eACHtH,OAAA,CAACpE,GAAG;UAACgK,EAAE,EAAE;YAAEI,OAAO,EAAE,MAAM;YAAEE,UAAU,EAAE;UAAS,CAAE;UAAAH,QAAA,gBACjD/F,OAAA,CAACb,aAAa;YAACyG,EAAE,EAAE;cAAE2B,EAAE,EAAE;YAAE;UAAE;YAAAzD,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,WAElC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAK;MACN;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACF,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACC,CAAC,EAGL7B,UAAU,iBACTpC,OAAA,CAACpE,GAAG;MAACgK,EAAE,EAAE;QAAEO,EAAE,EAAE;MAAE,CAAE;MAAAJ,QAAA,eACjB/F,OAAA,CAACL,WAAW;QAACqH,IAAI,EAAEA;MAAK;QAAAlD,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACxB,CACN,eAGDjE,OAAA,CAAClE,KAAK;MAAC8J,EAAE,EAAE;QAAEC,CAAC,EAAE;MAAE,CAAE;MAAAE,QAAA,gBAClB/F,OAAA,CAACnE,UAAU;QAACuK,OAAO,EAAC,IAAI;QAACR,EAAE,EAAE;UAAEO,EAAE,EAAE,CAAC;UAAE4B,UAAU,EAAE;QAAI,CAAE;QAAAhC,QAAA,EAAC;MAEzD;QAAAjC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAY,CAAC,eACbjE,OAAA,CAACP,eAAe;QACduH,IAAI,EAAEA,IAAI,CAAChF,MAAM,IAAI,EAAG;QACxB0H,OAAO,EAAE,CACP;UAAEC,KAAK,EAAE,WAAW;UAAEC,UAAU,EAAE,WAAW;UAAEjB,KAAK,EAAE;QAAI,CAAC,EAC3D;UAAEgB,KAAK,EAAE,WAAW;UAAEC,UAAU,EAAE,WAAW;UAAEjB,KAAK,EAAE;QAAI,CAAC,EAC3D;UAAEgB,KAAK,EAAE,SAAS;UAAEC,UAAU,EAAE,SAAS;UAAEjB,KAAK,EAAE;QAAI,CAAC,EACvD;UAAEgB,KAAK,EAAE,OAAO;UAAEC,UAAU,EAAE,OAAO;UAAEjB,KAAK,EAAE,GAAG;UAC/CuB,UAAU,EAAGC,GAAG,iBACdnK,OAAA,CAAC5D,IAAI;YACHkL,KAAK,EAAE6C,GAAG,CAACM,KAAM;YACjBvG,KAAK,EAAEiG,GAAG,CAACM,KAAK,KAAK,aAAa,GAAG,SAAS,GAAG,SAAU;YAC3DhE,IAAI,EAAC;UAAO;YAAA3C,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACb;QAEL,CAAC,EACD;UAAE0F,KAAK,EAAE,cAAc;UAAEC,UAAU,EAAE,cAAc;UAAEjB,KAAK,EAAE,GAAG;UAAEkB,KAAK,EAAE,OAAO;UAAEI,QAAQ,EAAE,QAAQ;UACjGC,UAAU,EAAGC,GAAG,IAAK,GAAGA,GAAG,CAACnC,YAAY;QAAI,CAAC,EAC/C;UAAE2B,KAAK,EAAE,eAAe;UAAEC,UAAU,EAAE,eAAe;UAAEjB,KAAK,EAAE,GAAG;UAAEkB,KAAK,EAAE,OAAO;UAAEI,QAAQ,EAAE,QAAQ;UACnGC,UAAU,EAAGC,GAAG,IAAK,GAAGA,GAAG,CAACO,aAAa;QAAI,CAAC,EAChD;UAAEf,KAAK,EAAE,kBAAkB;UAAEC,UAAU,EAAE,kBAAkB;UAAEjB,KAAK,EAAE,GAAG;UAAEkB,KAAK,EAAE,OAAO;UAAEI,QAAQ,EAAE,QAAQ;UACzGC,UAAU,EAAGC,GAAG,IAAK,GAAGA,GAAG,CAACQ,gBAAgB;QAAI,CAAC,EACnD;UAAEhB,KAAK,EAAE,sBAAsB;UAAEC,UAAU,EAAE,UAAU;UAAEjB,KAAK,EAAE,GAAG;UAAEkB,KAAK,EAAE,OAAO;UAAEI,QAAQ,EAAE,QAAQ;UACrGC,UAAU,EAAGC,GAAG,IAAK,GAAGA,GAAG,CAACS,oBAAoB;QAAI,CAAC,CACvD;QACFb,QAAQ,EAAE;MAAG;QAAAjG,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACd,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACG,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACL,CACN;EAED,MAAM4C,2BAA2B,GAAIG,IAAI;IAAA,IAAA6D,YAAA;IAAA,oBACvC7K,OAAA,CAACpE,GAAG;MAAAmK,QAAA,gBAEF/F,OAAA,CAACnE,UAAU;QAACuK,OAAO,EAAC,IAAI;QAACR,EAAE,EAAE;UAAEmC,UAAU,EAAE,GAAG;UAAE7D,KAAK,EAAE,WAAW;UAAEiC,EAAE,EAAE;QAAE,CAAE;QAAAJ,QAAA,GAAC,4BACjD,GAAA8E,YAAA,GAAC7D,IAAI,CAAC8D,MAAM,cAAAD,YAAA,uBAAXA,YAAA,CAAalJ,SAAS;MAAA;QAAAmC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACvC,CAAC,eAEbjE,OAAA,CAACjE,IAAI;QAAC0L,SAAS;QAACC,OAAO,EAAE,CAAE;QAAA3B,QAAA,gBAEzB/F,OAAA,CAACjE,IAAI;UAAC4L,IAAI;UAACC,EAAE,EAAE,EAAG;UAACC,EAAE,EAAE,CAAE;UAAA9B,QAAA,eACvB/F,OAAA,CAAClE,KAAK;YAAC8J,EAAE,EAAE;cAAEC,CAAC,EAAE;YAAE,CAAE;YAAAE,QAAA,gBAClB/F,OAAA,CAACnE,UAAU;cAACuK,OAAO,EAAC,IAAI;cAACR,EAAE,EAAE;gBAAEO,EAAE,EAAE,CAAC;gBAAE4B,UAAU,EAAE;cAAI,CAAE;cAAAhC,QAAA,EAAC;YAEzD;cAAAjC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAY,CAAC,EACZ+C,IAAI,CAAC8D,MAAM,iBACV9K,OAAA,CAACpE,GAAG;cAAAmK,QAAA,gBACF/F,OAAA,CAACpE,GAAG;gBAACgK,EAAE,EAAE;kBAAEI,OAAO,EAAE,MAAM;kBAAEC,cAAc,EAAE,eAAe;kBAAEE,EAAE,EAAE;gBAAE,CAAE;gBAAAJ,QAAA,gBACnE/F,OAAA,CAACnE,UAAU;kBAACuK,OAAO,EAAC,OAAO;kBAAAL,QAAA,EAAC;gBAAU;kBAAAjC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAY,CAAC,eACnDjE,OAAA,CAACnE,UAAU;kBAACuK,OAAO,EAAC,OAAO;kBAACR,EAAE,EAAE;oBAAEmC,UAAU,EAAE;kBAAI,CAAE;kBAAAhC,QAAA,EAAEiB,IAAI,CAAC8D,MAAM,CAACnJ;gBAAS;kBAAAmC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAa,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACtF,CAAC,eACNjE,OAAA,CAACpE,GAAG;gBAACgK,EAAE,EAAE;kBAAEI,OAAO,EAAE,MAAM;kBAAEC,cAAc,EAAE,eAAe;kBAAEE,EAAE,EAAE;gBAAE,CAAE;gBAAAJ,QAAA,gBACnE/F,OAAA,CAACnE,UAAU;kBAACuK,OAAO,EAAC,OAAO;kBAAAL,QAAA,EAAC;gBAAU;kBAAAjC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAY,CAAC,eACnDjE,OAAA,CAACnE,UAAU;kBAACuK,OAAO,EAAC,OAAO;kBAACR,EAAE,EAAE;oBAAEmC,UAAU,EAAE;kBAAI,CAAE;kBAAAhC,QAAA,EAAEiB,IAAI,CAAC8D,MAAM,CAACC;gBAAS;kBAAAjH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAa,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACtF,CAAC,eACNjE,OAAA,CAACpE,GAAG;gBAACgK,EAAE,EAAE;kBAAEI,OAAO,EAAE,MAAM;kBAAEC,cAAc,EAAE,eAAe;kBAAEE,EAAE,EAAE;gBAAE,CAAE;gBAAAJ,QAAA,gBACnE/F,OAAA,CAACnE,UAAU;kBAACuK,OAAO,EAAC,OAAO;kBAAAL,QAAA,EAAC;gBAAQ;kBAAAjC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAY,CAAC,eACjDjE,OAAA,CAACnE,UAAU;kBAACuK,OAAO,EAAC,OAAO;kBAACR,EAAE,EAAE;oBAAEmC,UAAU,EAAE;kBAAI,CAAE;kBAAAhC,QAAA,EAAEiB,IAAI,CAAC8D,MAAM,CAACE;gBAAO;kBAAAlH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAa,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACpF,CAAC,eACNjE,OAAA,CAACpE,GAAG;gBAACgK,EAAE,EAAE;kBAAEI,OAAO,EAAE,MAAM;kBAAEC,cAAc,EAAE,eAAe;kBAAEE,EAAE,EAAE;gBAAE,CAAE;gBAAAJ,QAAA,gBACnE/F,OAAA,CAACnE,UAAU;kBAACuK,OAAO,EAAC,OAAO;kBAAAL,QAAA,EAAC;gBAAM;kBAAAjC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAY,CAAC,eAC/CjE,OAAA,CAAC5D,IAAI;kBACHkL,KAAK,EAAEN,IAAI,CAAC8D,MAAM,CAACL,KAAM;kBACzBvG,KAAK,EAAE8C,IAAI,CAAC8D,MAAM,CAACL,KAAK,KAAK,aAAa,GAAG,SAAS,GAAG,SAAU;kBACnEhE,IAAI,EAAC;gBAAO;kBAAA3C,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACb,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACC,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CACN;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACI;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACJ,CAAC,eAGPjE,OAAA,CAACjE,IAAI;UAAC4L,IAAI;UAACC,EAAE,EAAE,EAAG;UAACC,EAAE,EAAE,CAAE;UAAA9B,QAAA,eACvB/F,OAAA,CAAClE,KAAK;YAAC8J,EAAE,EAAE;cAAEC,CAAC,EAAE;YAAE,CAAE;YAAAE,QAAA,gBAClB/F,OAAA,CAACnE,UAAU;cAACuK,OAAO,EAAC,IAAI;cAACR,EAAE,EAAE;gBAAEO,EAAE,EAAE,CAAC;gBAAE4B,UAAU,EAAE;cAAI,CAAE;cAAAhC,QAAA,EAAC;YAEzD;cAAAjC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAY,CAAC,EACZ+C,IAAI,CAAC8D,MAAM,iBACV9K,OAAA,CAACpE,GAAG;cAAAmK,QAAA,gBACF/F,OAAA,CAACpE,GAAG;gBAACgK,EAAE,EAAE;kBAAEI,OAAO,EAAE,MAAM;kBAAEC,cAAc,EAAE,eAAe;kBAAEE,EAAE,EAAE;gBAAE,CAAE;gBAAAJ,QAAA,gBACnE/F,OAAA,CAACnE,UAAU;kBAACuK,OAAO,EAAC,OAAO;kBAAAL,QAAA,EAAC;gBAAa;kBAAAjC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAY,CAAC,eACtDjE,OAAA,CAACnE,UAAU;kBAACuK,OAAO,EAAC,OAAO;kBAACR,EAAE,EAAE;oBAAEmC,UAAU,EAAE;kBAAI,CAAE;kBAAAhC,QAAA,GAAEiB,IAAI,CAAC8D,MAAM,CAAC9C,YAAY,EAAC,GAAC;gBAAA;kBAAAlE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAY,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC1F,CAAC,eACNjE,OAAA,CAACpE,GAAG;gBAACgK,EAAE,EAAE;kBAAEI,OAAO,EAAE,MAAM;kBAAEC,cAAc,EAAE,eAAe;kBAAEE,EAAE,EAAE;gBAAE,CAAE;gBAAAJ,QAAA,gBACnE/F,OAAA,CAACnE,UAAU;kBAACuK,OAAO,EAAC,OAAO;kBAAAL,QAAA,EAAC;gBAAiB;kBAAAjC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAY,CAAC,eAC1DjE,OAAA,CAACnE,UAAU;kBAACuK,OAAO,EAAC,OAAO;kBAACR,EAAE,EAAE;oBAAEmC,UAAU,EAAE,GAAG;oBAAE7D,KAAK,EAAE;kBAAe,CAAE;kBAAA6B,QAAA,GACxEiB,IAAI,CAAC8D,MAAM,CAACH,gBAAgB,EAAC,GAChC;gBAAA;kBAAA7G,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAY,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACV,CAAC,eACNjE,OAAA,CAACpE,GAAG;gBAACgK,EAAE,EAAE;kBAAEI,OAAO,EAAE,MAAM;kBAAEC,cAAc,EAAE,eAAe;kBAAEE,EAAE,EAAE;gBAAE,CAAE;gBAAAJ,QAAA,gBACnE/F,OAAA,CAACnE,UAAU;kBAACuK,OAAO,EAAC,OAAO;kBAAAL,QAAA,EAAC;gBAAc;kBAAAjC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAY,CAAC,eACvDjE,OAAA,CAACnE,UAAU;kBAACuK,OAAO,EAAC,OAAO;kBAACR,EAAE,EAAE;oBAAEmC,UAAU,EAAE,GAAG;oBAAE7D,KAAK,EAAE;kBAAe,CAAE;kBAAA6B,QAAA,GACxEiB,IAAI,CAAC8D,MAAM,CAACJ,aAAa,EAAC,GAC7B;gBAAA;kBAAA5G,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAY,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACV,CAAC,eACNjE,OAAA,CAACpE,GAAG;gBAACgK,EAAE,EAAE;kBAAEI,OAAO,EAAE,MAAM;kBAAEC,cAAc,EAAE;gBAAgB,CAAE;gBAAAF,QAAA,gBAC5D/F,OAAA,CAACnE,UAAU;kBAACuK,OAAO,EAAC,OAAO;kBAAAL,QAAA,EAAC;gBAAqB;kBAAAjC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAY,CAAC,eAC9DjE,OAAA,CAACnE,UAAU;kBAACuK,OAAO,EAAC,OAAO;kBAACR,EAAE,EAAE;oBAAEmC,UAAU,EAAE;kBAAI,CAAE;kBAAAhC,QAAA,GAAEiB,IAAI,CAAC8D,MAAM,CAACF,oBAAoB,EAAC,GAAC;gBAAA;kBAAA9G,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAY,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAClG,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CACN;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACI;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACJ,CAAC,eAGPjE,OAAA,CAACjE,IAAI;UAAC4L,IAAI;UAACC,EAAE,EAAE,EAAG;UAAA7B,QAAA,eAChB/F,OAAA,CAAClE,KAAK;YAAC8J,EAAE,EAAE;cAAEC,CAAC,EAAE;YAAE,CAAE;YAAAE,QAAA,gBAClB/F,OAAA,CAACnE,UAAU;cAACuK,OAAO,EAAC,IAAI;cAACR,EAAE,EAAE;gBAAEO,EAAE,EAAE,CAAC;gBAAE4B,UAAU,EAAE;cAAI,CAAE;cAAAhC,QAAA,GAAC,kBACvC,EAACiB,IAAI,CAACiC,WAAW,EAAC,GACpC;YAAA;cAAAnF,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAY,CAAC,eACbjE,OAAA,CAACP,eAAe;cACduH,IAAI,EAAEA,IAAI,CAACiE,cAAc,IAAI,EAAG;cAChCvB,OAAO,EAAE,CACP;gBAAEC,KAAK,EAAE,SAAS;gBAAEC,UAAU,EAAE,SAAS;gBAAEjB,KAAK,EAAE;cAAI,CAAC,EACvD;gBAAEgB,KAAK,EAAE,SAAS;gBAAEC,UAAU,EAAE,SAAS;gBAAEjB,KAAK,EAAE;cAAI,CAAC,EACvD;gBAAEgB,KAAK,EAAE,SAAS;gBAAEC,UAAU,EAAE,SAAS;gBAAEjB,KAAK,EAAE;cAAI,CAAC,EACvD;gBAAEgB,KAAK,EAAE,WAAW;gBAAEC,UAAU,EAAE,WAAW;gBAAEjB,KAAK,EAAE;cAAI,CAAC,EAC3D;gBAAEgB,KAAK,EAAE,eAAe;gBAAEC,UAAU,EAAE,eAAe;gBAAEjB,KAAK,EAAE,GAAG;gBAAEkB,KAAK,EAAE,OAAO;gBAAEI,QAAQ,EAAE,QAAQ;gBACnGC,UAAU,EAAGC,GAAG,IAAK,GAAGA,GAAG,CAACC,aAAa;cAAI,CAAC,EAChD;gBAAET,KAAK,EAAE,aAAa;gBAAEC,UAAU,EAAE,aAAa;gBAAEjB,KAAK,EAAE,GAAG;gBAAEkB,KAAK,EAAE,OAAO;gBAAEI,QAAQ,EAAE,QAAQ;gBAC/FC,UAAU,EAAGC,GAAG,IAAK,GAAGA,GAAG,CAACE,WAAW;cAAI,CAAC,EAC9C;gBAAEV,KAAK,EAAE,OAAO;gBAAEC,UAAU,EAAE,OAAO;gBAAEjB,KAAK,EAAE,GAAG;gBAC/CuB,UAAU,EAAGC,GAAG,iBACdnK,OAAA,CAAC5D,IAAI;kBACHkL,KAAK,EAAE6C,GAAG,CAACM,KAAM;kBACjBvG,KAAK,EAAEiG,GAAG,CAACM,KAAK,KAAK,QAAQ,GAAG,SAAS,GAAG,SAAU;kBACtDhE,IAAI,EAAC;gBAAO;kBAAA3C,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACb;cAEL,CAAC,CACD;cACF8F,QAAQ,EAAE;YAAG;cAAAjG,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACd,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACG;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACJ,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACJ,CAAC;EAAA,CACP;EAED,MAAM6C,uBAAuB,GAAIE,IAAI,iBACnChH,OAAA,CAACpE,GAAG;IAAAmK,QAAA,gBAEF/F,OAAA,CAACpE,GAAG;MAACgK,EAAE,EAAE;QAAEI,OAAO,EAAE,MAAM;QAAEC,cAAc,EAAE,eAAe;QAAEC,UAAU,EAAE,QAAQ;QAAEC,EAAE,EAAE;MAAE,CAAE;MAAAJ,QAAA,gBACzF/F,OAAA,CAACnE,UAAU;QAACuK,OAAO,EAAC,IAAI;QAACR,EAAE,EAAE;UAAEmC,UAAU,EAAE,GAAG;UAAE7D,KAAK,EAAE;QAAe,CAAE;QAAA6B,QAAA,EAAC;MAEzE;QAAAjC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAY,CAAC,eACbjE,OAAA,CAACzC,gBAAgB;QACf0J,OAAO,eACLjH,OAAA,CAAC1C,MAAM;UACL4J,OAAO,EAAE9E,UAAW;UACpB+E,QAAQ,EAAGC,CAAC,IAAK/E,aAAa,CAAC+E,CAAC,CAACC,MAAM,CAACH,OAAO,CAAE;UACjDhD,KAAK,EAAC;QAAS;UAAAJ,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAChB,CACF;QACDqD,KAAK,eACHtH,OAAA,CAACpE,GAAG;UAACgK,EAAE,EAAE;YAAEI,OAAO,EAAE,MAAM;YAAEE,UAAU,EAAE;UAAS,CAAE;UAAAH,QAAA,gBACjD/F,OAAA,CAACb,aAAa;YAACyG,EAAE,EAAE;cAAE2B,EAAE,EAAE;YAAE;UAAE;YAAAzD,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,WAElC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAK;MACN;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACF,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACC,CAAC,eAGNjE,OAAA,CAACjE,IAAI;MAAC0L,SAAS;MAACC,OAAO,EAAE,CAAE;MAAC9B,EAAE,EAAE;QAAEO,EAAE,EAAE;MAAE,CAAE;MAAAJ,QAAA,gBACxC/F,OAAA,CAACjE,IAAI;QAAC4L,IAAI;QAACC,EAAE,EAAE,EAAG;QAACC,EAAE,EAAE,CAAE;QAAA9B,QAAA,eACvB/F,OAAA,CAAClE,KAAK;UAAC8J,EAAE,EAAE;YAAEC,CAAC,EAAE,CAAC;YAAEiC,SAAS,EAAE,QAAQ;YAAEoD,OAAO,EAAE,cAAc;YAAEhH,KAAK,EAAE;UAAQ,CAAE;UAAA6B,QAAA,gBAChF/F,OAAA,CAACnE,UAAU;YAACuK,OAAO,EAAC,IAAI;YAACR,EAAE,EAAE;cAAEmC,UAAU,EAAE,MAAM;cAAE5B,EAAE,EAAE;YAAE,CAAE;YAAAJ,QAAA,GACxDiB,IAAI,CAACmE,oBAAoB,EAAC,GAC7B;UAAA;YAAArH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAY,CAAC,eACbjE,OAAA,CAACnE,UAAU;YAACuK,OAAO,EAAC,OAAO;YAAAL,QAAA,EAAC;UAAY;YAAAjC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAY,CAAC,eACrDjE,OAAA,CAACnE,UAAU;YAACuK,OAAO,EAAC,SAAS;YAAAL,QAAA,GAAEiB,IAAI,CAACvF,WAAW,EAAC,KAAG,EAACuF,IAAI,CAACtF,SAAS;UAAA;YAAAoC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAa,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC3E;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACJ,CAAC,eACPjE,OAAA,CAACjE,IAAI;QAAC4L,IAAI;QAACC,EAAE,EAAE,EAAG;QAACC,EAAE,EAAE,CAAE;QAAA9B,QAAA,eACvB/F,OAAA,CAAClE,KAAK;UAAC8J,EAAE,EAAE;YAAEC,CAAC,EAAE,CAAC;YAAEiC,SAAS,EAAE,QAAQ;YAAEoD,OAAO,EAAE,WAAW;YAAEhH,KAAK,EAAE;UAAQ,CAAE;UAAA6B,QAAA,gBAC7E/F,OAAA,CAACnE,UAAU;YAACuK,OAAO,EAAC,IAAI;YAACR,EAAE,EAAE;cAAEmC,UAAU,EAAE,MAAM;cAAE5B,EAAE,EAAE;YAAE,CAAE;YAAAJ,QAAA,EACxDiB,IAAI,CAACoE;UAAa;YAAAtH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACT,CAAC,eACbjE,OAAA,CAACnE,UAAU;YAACuK,OAAO,EAAC,OAAO;YAAAL,QAAA,EAAC;UAAa;YAAAjC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAY,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACjD;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACJ,CAAC,eACPjE,OAAA,CAACjE,IAAI;QAAC4L,IAAI;QAACC,EAAE,EAAE,EAAG;QAACC,EAAE,EAAE,CAAE;QAAA9B,QAAA,eACvB/F,OAAA,CAAClE,KAAK;UAAC8J,EAAE,EAAE;YAAEC,CAAC,EAAE,CAAC;YAAEiC,SAAS,EAAE,QAAQ;YAAEoD,OAAO,EAAE,cAAc;YAAEhH,KAAK,EAAE;UAAQ,CAAE;UAAA6B,QAAA,gBAChF/F,OAAA,CAACnE,UAAU;YAACuK,OAAO,EAAC,IAAI;YAACR,EAAE,EAAE;cAAEmC,UAAU,EAAE,MAAM;cAAE5B,EAAE,EAAE;YAAE,CAAE;YAAAJ,QAAA,GACxDiB,IAAI,CAACsB,iBAAiB,EAAC,GAC1B;UAAA;YAAAxE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAY,CAAC,eACbjE,OAAA,CAACnE,UAAU;YAACuK,OAAO,EAAC,OAAO;YAAAL,QAAA,EAAC;UAAY;YAAAjC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAY,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAChD;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACJ,CAAC,eACPjE,OAAA,CAACjE,IAAI;QAAC4L,IAAI;QAACC,EAAE,EAAE,EAAG;QAACC,EAAE,EAAE,CAAE;QAAA9B,QAAA,eACvB/F,OAAA,CAAClE,KAAK;UAAC8J,EAAE,EAAE;YAAEC,CAAC,EAAE,CAAC;YAAEiC,SAAS,EAAE,QAAQ;YAAEoD,OAAO,EAAE,cAAc;YAAEhH,KAAK,EAAE;UAAQ,CAAE;UAAA6B,QAAA,gBAChF/F,OAAA,CAACnE,UAAU;YAACuK,OAAO,EAAC,IAAI;YAACR,EAAE,EAAE;cAAEmC,UAAU,EAAE,MAAM;cAAE5B,EAAE,EAAE;YAAE,CAAE;YAAAJ,QAAA,GACxDsF,IAAI,CAACC,KAAK,CAACtE,IAAI,CAACmE,oBAAoB,GAAGnE,IAAI,CAACoE,aAAa,GAAG,CAAC,CAAC,EAAC,GAClE;UAAA;YAAAtH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAY,CAAC,eACbjE,OAAA,CAACnE,UAAU;YAACuK,OAAO,EAAC,OAAO;YAAAL,QAAA,EAAC;UAAe;YAAAjC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAY,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACnD;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACJ,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,EAGN7B,UAAU,iBACTpC,OAAA,CAACpE,GAAG;MAACgK,EAAE,EAAE;QAAEO,EAAE,EAAE;MAAE,CAAE;MAAAJ,QAAA,eACjB/F,OAAA,CAACH,aAAa;QAACmH,IAAI,EAAEA;MAAK;QAAAlD,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAC1B,CACN,eAGDjE,OAAA,CAAClE,KAAK;MAAC8J,EAAE,EAAE;QAAEC,CAAC,EAAE;MAAE,CAAE;MAAAE,QAAA,gBAClB/F,OAAA,CAACnE,UAAU;QAACuK,OAAO,EAAC,IAAI;QAACR,EAAE,EAAE;UAAEO,EAAE,EAAE,CAAC;UAAE4B,UAAU,EAAE;QAAI,CAAE;QAAAhC,QAAA,EAAC;MAEzD;QAAAjC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAY,CAAC,eACbjE,OAAA,CAACP,eAAe;QACduH,IAAI,EAAEA,IAAI,CAACuE,gBAAgB,IAAI,EAAG;QAClC7B,OAAO,EAAE,CACP;UAAEC,KAAK,EAAE,MAAM;UAAEC,UAAU,EAAE,MAAM;UAAEjB,KAAK,EAAE;QAAI,CAAC,EACjD;UAAEgB,KAAK,EAAE,OAAO;UAAEC,UAAU,EAAE,cAAc;UAAEjB,KAAK,EAAE,GAAG;UAAEkB,KAAK,EAAE,OAAO;UAAEI,QAAQ,EAAE,QAAQ;UAC1FC,UAAU,EAAGC,GAAG,IAAK,GAAGA,GAAG,CAACV,KAAK;QAAI,CAAC,CACxC;QACFM,QAAQ,EAAE;MAAG;QAAAjG,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACd,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACG,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACL,CACN;EAED,MAAM8C,qBAAqB,GAAIC,IAAI,iBACjChH,OAAA,CAACpE,GAAG;IAAAmK,QAAA,gBAEF/F,OAAA,CAACpE,GAAG;MAACgK,EAAE,EAAE;QAAEI,OAAO,EAAE,MAAM;QAAEC,cAAc,EAAE,eAAe;QAAEC,UAAU,EAAE,QAAQ;QAAEC,EAAE,EAAE;MAAE,CAAE;MAAAJ,QAAA,gBACzF/F,OAAA,CAACnE,UAAU;QAACuK,OAAO,EAAC,IAAI;QAACR,EAAE,EAAE;UAAEmC,UAAU,EAAE,GAAG;UAAE7D,KAAK,EAAE;QAAa,CAAE;QAAA6B,QAAA,EAAC;MAEvE;QAAAjC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAY,CAAC,eACbjE,OAAA,CAACzC,gBAAgB;QACf0J,OAAO,eACLjH,OAAA,CAAC1C,MAAM;UACL4J,OAAO,EAAE9E,UAAW;UACpB+E,QAAQ,EAAGC,CAAC,IAAK/E,aAAa,CAAC+E,CAAC,CAACC,MAAM,CAACH,OAAO,CAAE;UACjDhD,KAAK,EAAC;QAAS;UAAAJ,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAChB,CACF;QACDqD,KAAK,eACHtH,OAAA,CAACpE,GAAG;UAACgK,EAAE,EAAE;YAAEI,OAAO,EAAE,MAAM;YAAEE,UAAU,EAAE;UAAS,CAAE;UAAAH,QAAA,gBACjD/F,OAAA,CAACb,aAAa;YAACyG,EAAE,EAAE;cAAE2B,EAAE,EAAE;YAAE;UAAE;YAAAzD,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,WAElC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAK;MACN;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACF,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACC,CAAC,EAGL7B,UAAU,iBACTpC,OAAA,CAACpE,GAAG;MAACgK,EAAE,EAAE;QAAEO,EAAE,EAAE;MAAE,CAAE;MAAAJ,QAAA,eACjB/F,OAAA,CAACF,cAAc;QAACkH,IAAI,EAAEA;MAAK;QAAAlD,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAC3B,CACN,eAGDjE,OAAA,CAAClE,KAAK;MAAC8J,EAAE,EAAE;QAAEC,CAAC,EAAE;MAAE,CAAE;MAAAE,QAAA,gBAClB/F,OAAA,CAACnE,UAAU;QAACuK,OAAO,EAAC,IAAI;QAACR,EAAE,EAAE;UAAEO,EAAE,EAAE,CAAC;UAAE4B,UAAU,EAAE;QAAI,CAAE;QAAAhC,QAAA,EAAC;MAEzD;QAAAjC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAY,CAAC,eACbjE,OAAA,CAACP,eAAe;QACduH,IAAI,EAAEA,IAAI,CAACwE,cAAc,IAAI,EAAG;QAChC9B,OAAO,EAAE,CACP;UAAEC,KAAK,EAAE,OAAO;UAAEC,UAAU,EAAE,OAAO;UAAEjB,KAAK,EAAE,GAAG;UAC/CuB,UAAU,EAAGC,GAAG,iBACdnK,OAAA,CAAC5D,IAAI;YACHkL,KAAK,EAAE6C,GAAG,CAACM,KAAM;YACjBvG,KAAK,EAAEiG,GAAG,CAACM,KAAK,KAAK,YAAY,GAAG,SAAS,GAAG,SAAU;YAC1DhE,IAAI,EAAC;UAAO;YAAA3C,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACb;QAEL,CAAC,EACD;UAAE0F,KAAK,EAAE,UAAU;UAAEC,UAAU,EAAE,aAAa;UAAEjB,KAAK,EAAE,GAAG;UAAEkB,KAAK,EAAE,OAAO;UAAEI,QAAQ,EAAE;QAAS,CAAC,EAChG;UAAEN,KAAK,EAAE,eAAe;UAAEC,UAAU,EAAE,eAAe;UAAEjB,KAAK,EAAE,GAAG;UAAEkB,KAAK,EAAE,OAAO;UAAEI,QAAQ,EAAE,QAAQ;UACnGC,UAAU,EAAGC,GAAG,IAAK,GAAGA,GAAG,CAACC,aAAa;QAAI,CAAC,EAChD;UAAET,KAAK,EAAE,aAAa;UAAEC,UAAU,EAAE,aAAa;UAAEjB,KAAK,EAAE,GAAG;UAAEkB,KAAK,EAAE,OAAO;UAAEI,QAAQ,EAAE,QAAQ;UAC/FC,UAAU,EAAGC,GAAG,IAAK,GAAGA,GAAG,CAACE,WAAW;QAAI,CAAC,CAC9C;QACFP,UAAU,EAAE;MAAM;QAAAhG,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACnB,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACG,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACL,CACN;EAED,MAAMwH,YAAY,GAAGA,CAAA,kBACnBzL,OAAA,CAACtD,MAAM;IAACoI,IAAI,EAAE9D,UAAW;IAAC0K,OAAO,EAAEhG,iBAAkB;IAACiG,QAAQ,EAAC,IAAI;IAACC,SAAS;IAAA7F,QAAA,gBAC3E/F,OAAA,CAACrD,WAAW;MAAAoJ,QAAA,EACTjF,cAAc,aAAdA,cAAc,uBAAdA,cAAc,CAAE6C;IAAK;MAAAG,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACX,CAAC,eACdjE,OAAA,CAACpD,aAAa;MAAAmJ,QAAA,GACXrF,KAAK,iBACJV,OAAA,CAAC3D,KAAK;QAACwP,QAAQ,EAAC,OAAO;QAACjG,EAAE,EAAE;UAAEO,EAAE,EAAE;QAAE,CAAE;QAAAJ,QAAA,EACnCrF;MAAK;QAAAoD,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACD,CACR,eAEDjE,OAAA,CAACjE,IAAI;QAAC0L,SAAS;QAACC,OAAO,EAAE,CAAE;QAAC9B,EAAE,EAAE;UAAEE,EAAE,EAAE;QAAE,CAAE;QAAAC,QAAA,gBACxC/F,OAAA,CAACjE,IAAI;UAAC4L,IAAI;UAACC,EAAE,EAAE,EAAG;UAAA7B,QAAA,eAChB/F,OAAA,CAAClD,WAAW;YAAC8O,SAAS;YAAA7F,QAAA,gBACpB/F,OAAA,CAACjD,UAAU;cAAAgJ,QAAA,EAAC;YAAO;cAAAjC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAY,CAAC,eAChCjE,OAAA,CAAChD,MAAM;cACL8O,KAAK,EAAExK,QAAQ,CAACE,OAAQ;cACxB8F,KAAK,EAAC,SAAS;cACfH,QAAQ,EAAGC,CAAC,IAAK7F,WAAW,CAAC;gBAAE,GAAGD,QAAQ;gBAAEE,OAAO,EAAE4F,CAAC,CAACC,MAAM,CAACyE;cAAM,CAAC,CAAE;cAAA/F,QAAA,gBAEvE/F,OAAA,CAAC/C,QAAQ;gBAAC6O,KAAK,EAAC,OAAO;gBAAA/F,QAAA,EAAC;cAAoB;gBAAAjC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAU,CAAC,eACvDjE,OAAA,CAAC/C,QAAQ;gBAAC6O,KAAK,EAAC,KAAK;gBAAA/F,QAAA,EAAC;cAAY;gBAAAjC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAU,CAAC,eAC7CjE,OAAA,CAAC/C,QAAQ;gBAAC6O,KAAK,EAAC,OAAO;gBAAA/F,QAAA,EAAC;cAAc;gBAAAjC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAU,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC3C,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACE;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACV,CAAC,EAEN/C,UAAU,KAAK,kBAAkB,iBAChClB,OAAA,CAACjE,IAAI;UAAC4L,IAAI;UAACC,EAAE,EAAE,EAAG;UAAA7B,QAAA,eAChB/F,OAAA,CAAC9C,SAAS;YACR0O,SAAS;YACTtE,KAAK,EAAC,WAAW;YACjBwE,KAAK,EAAExK,QAAQ,CAACK,SAAU;YAC1BwF,QAAQ,EAAGC,CAAC,IAAK7F,WAAW,CAAC;cAAE,GAAGD,QAAQ;cAAEK,SAAS,EAAEyF,CAAC,CAACC,MAAM,CAACyE;YAAM,CAAC,CAAE;YACzEC,WAAW,EAAC,mBAAmB;YAC/BC,UAAU,EAAC;UAA0D;YAAAlI,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACtE;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACE,CACP,EAEA/C,UAAU,KAAK,cAAc,iBAC5BlB,OAAA,CAAAE,SAAA;UAAA6F,QAAA,gBACE/F,OAAA,CAACjE,IAAI;YAAC4L,IAAI;YAACC,EAAE,EAAE,CAAE;YAAA7B,QAAA,eACf/F,OAAA,CAAC9C,SAAS;cACR0O,SAAS;cACTK,IAAI,EAAC,MAAM;cACX3E,KAAK,EAAC,aAAa;cACnBwE,KAAK,EAAExK,QAAQ,CAACG,WAAY;cAC5B0F,QAAQ,EAAGC,CAAC,IAAK7F,WAAW,CAAC;gBAAE,GAAGD,QAAQ;gBAAEG,WAAW,EAAE2F,CAAC,CAACC,MAAM,CAACyE;cAAM,CAAC,CAAE;cAC3EI,eAAe,EAAE;gBAAEC,MAAM,EAAE;cAAK;YAAE;cAAArI,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACnC;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACE,CAAC,eACPjE,OAAA,CAACjE,IAAI;YAAC4L,IAAI;YAACC,EAAE,EAAE,CAAE;YAAA7B,QAAA,eACf/F,OAAA,CAAC9C,SAAS;cACR0O,SAAS;cACTK,IAAI,EAAC,MAAM;cACX3E,KAAK,EAAC,WAAW;cACjBwE,KAAK,EAAExK,QAAQ,CAACI,SAAU;cAC1ByF,QAAQ,EAAGC,CAAC,IAAK7F,WAAW,CAAC;gBAAE,GAAGD,QAAQ;gBAAEI,SAAS,EAAE0F,CAAC,CAACC,MAAM,CAACyE;cAAM,CAAC,CAAE;cACzEI,eAAe,EAAE;gBAAEC,MAAM,EAAE;cAAK;YAAE;cAAArI,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACnC;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACE,CAAC;QAAA,eACP,CACH;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACG,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACM,CAAC,eAChBjE,OAAA,CAACnD,aAAa;MAAAkJ,QAAA,gBACZ/F,OAAA,CAAC7D,MAAM;QAACqK,OAAO,EAAEd,iBAAkB;QAAAK,QAAA,EAAC;MAAO;QAAAjC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAQ,CAAC,eACpDjE,OAAA,CAAC7D,MAAM;QACLqK,OAAO,EAAEf,oBAAqB;QAC9BW,OAAO,EAAC,WAAW;QACnBgG,QAAQ,EAAE5L,OAAQ;QAClB+F,SAAS,EAAE/F,OAAO,gBAAGR,OAAA,CAAC1D,gBAAgB;UAACmK,IAAI,EAAE;QAAG;UAAA3C,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,gBAAGjE,OAAA,CAAC3B,cAAc;UAAAyF,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAE;QAAA8B,QAAA,EAExEvF,OAAO,GAAG,gBAAgB,GAAG;MAAe;QAAAsD,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACvC,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACI,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACV,CACT;EAED,oBACEjE,OAAA,CAACpE,GAAG;IAACgK,EAAE,EAAE;MAAEC,CAAC,EAAE;IAAE,CAAE;IAAAE,QAAA,gBAEhB/F,OAAA,CAACpE,GAAG;MAACgK,EAAE,EAAE;QAAEI,OAAO,EAAE,MAAM;QAAEC,cAAc,EAAE,eAAe;QAAEC,UAAU,EAAE,QAAQ;QAAEC,EAAE,EAAE;MAAE,CAAE;MAAAJ,QAAA,gBACzF/F,OAAA,CAACpE,GAAG;QAACgK,EAAE,EAAE;UAAEI,OAAO,EAAE,MAAM;UAAEE,UAAU,EAAE,QAAQ;UAAEI,GAAG,EAAE;QAAE,CAAE;QAAAP,QAAA,gBACzD/F,OAAA,CAACxD,UAAU;UAACgK,OAAO,EAAEA,CAAA,KAAMnG,QAAQ,CAAC,CAAC,CAAC,CAAE;UAAC6D,KAAK,EAAC,SAAS;UAAA6B,QAAA,eACtD/F,OAAA,CAACvB,aAAa;YAAAqF,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACP,CAAC,eACbjE,OAAA,CAACnE,UAAU;UAACuK,OAAO,EAAC,IAAI;UAACiG,SAAS,EAAC,IAAI;UAAAtG,QAAA,EAAC;QAExC;UAAAjC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAY,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACV,CAAC,eACNjE,OAAA,CAACT,eAAe;QAAAuE,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAChB,CAAC,EAGLzD,OAAO,iBACNR,OAAA,CAACpE,GAAG;MAACgK,EAAE,EAAE;QAAEI,OAAO,EAAE,MAAM;QAAEC,cAAc,EAAE,QAAQ;QAAEqG,EAAE,EAAE;MAAE,CAAE;MAAAvG,QAAA,eAC5D/F,OAAA,CAAC1D,gBAAgB;QAAAwH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACjB,CACN,eAGDjE,OAAA,CAACpE,GAAG;MAACgK,EAAE,EAAE;QAAEE,EAAE,EAAE;MAAE,CAAE;MAAAC,QAAA,gBAEjB/F,OAAA,CAAClE,KAAK;QAAC8J,EAAE,EAAE;UAAEO,EAAE,EAAE,CAAC;UAAEqB,MAAM,EAAE;QAAoB,CAAE;QAAAzB,QAAA,eAChD/F,OAAA,CAACpE,GAAG;UAACgK,EAAE,EAAE;YAAEoD,YAAY,EAAE,CAAC;YAAEuD,WAAW,EAAE,SAAS;YAAErB,OAAO,EAAE;UAAU,CAAE;UAAAnF,QAAA,eACvE/F,OAAA,CAACpE,GAAG;YAACgK,EAAE,EAAE;cAAEI,OAAO,EAAE,MAAM;cAAEwG,QAAQ,EAAE,MAAM;cAAElG,GAAG,EAAE,CAAC;cAAET,CAAC,EAAE;YAAE,CAAE;YAAAE,QAAA,gBAC3D/F,OAAA,CAAC7D,MAAM;cACLiK,OAAO,EAAEhF,kBAAkB,KAAK,UAAU,GAAG,WAAW,GAAG,UAAW;cACtEmF,SAAS,eAAEvG,OAAA,CAACvC,cAAc;gBAAAqG,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAE;cAC9BuC,OAAO,EAAEA,CAAA,KAAMnF,qBAAqB,CAAC,UAAU,CAAE;cACjDuE,EAAE,EAAE;gBACFsF,OAAO,EAAE9J,kBAAkB,KAAK,UAAU,GAAG,SAAS,GAAG,aAAa;gBACtE8C,KAAK,EAAE9C,kBAAkB,KAAK,UAAU,GAAG,OAAO,GAAG,SAAS;gBAC9DmL,WAAW,EAAE,SAAS;gBACtB,SAAS,EAAE;kBACTrB,OAAO,EAAE9J,kBAAkB,KAAK,UAAU,GAAG,SAAS,GAAG,SAAS;kBAClEmL,WAAW,EAAE;gBACf;cACF,CAAE;cAAAxG,QAAA,EACH;YAED;cAAAjC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC,eACTjE,OAAA,CAAC7D,MAAM;cACLiK,OAAO,EAAEhF,kBAAkB,KAAK,KAAK,GAAG,WAAW,GAAG,UAAW;cACjEmF,SAAS,eAAEvG,OAAA,CAAC/B,QAAQ;gBAAA6F,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAE;cACxBuC,OAAO,EAAEA,CAAA,KAAMnF,qBAAqB,CAAC,KAAK,CAAE;cAC5CuE,EAAE,EAAE;gBACFsF,OAAO,EAAE9J,kBAAkB,KAAK,KAAK,GAAG,SAAS,GAAG,aAAa;gBACjE8C,KAAK,EAAE9C,kBAAkB,KAAK,KAAK,GAAG,OAAO,GAAG,SAAS;gBACzDmL,WAAW,EAAE,SAAS;gBACtB,SAAS,EAAE;kBACTrB,OAAO,EAAE9J,kBAAkB,KAAK,KAAK,GAAG,SAAS,GAAG,SAAS;kBAC7DmL,WAAW,EAAE;gBACf;cACF,CAAE;cAAAxG,QAAA,EACH;YAED;cAAAjC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC,eACTjE,OAAA,CAAC7D,MAAM;cACLiK,OAAO,EAAEhF,kBAAkB,KAAK,QAAQ,GAAG,WAAW,GAAG,UAAW;cACpEmF,SAAS,eAAEvG,OAAA,CAACjB,aAAa;gBAAA+E,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAE;cAC7BuC,OAAO,EAAEA,CAAA,KAAMnF,qBAAqB,CAAC,QAAQ,CAAE;cAC/CuE,EAAE,EAAE;gBACFsF,OAAO,EAAE9J,kBAAkB,KAAK,QAAQ,GAAG,SAAS,GAAG,aAAa;gBACpE8C,KAAK,EAAE9C,kBAAkB,KAAK,QAAQ,GAAG,OAAO,GAAG,SAAS;gBAC5DmL,WAAW,EAAE,SAAS;gBACtB,SAAS,EAAE;kBACTrB,OAAO,EAAE9J,kBAAkB,KAAK,QAAQ,GAAG,SAAS,GAAG,SAAS;kBAChEmL,WAAW,EAAE;gBACf;cACF,CAAE;cAAAxG,QAAA,EACH;YAED;cAAAjC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC,eACTjE,OAAA,CAAC7D,MAAM;cACLiK,OAAO,EAAEhF,kBAAkB,KAAK,YAAY,GAAG,WAAW,GAAG,UAAW;cACxEmF,SAAS,eAAEvG,OAAA,CAACrC,YAAY;gBAAAmG,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAE;cAC5BuC,OAAO,EAAEA,CAAA,KAAMnF,qBAAqB,CAAC,YAAY,CAAE;cACnDuE,EAAE,EAAE;gBACFsF,OAAO,EAAE9J,kBAAkB,KAAK,YAAY,GAAG,SAAS,GAAG,aAAa;gBACxE8C,KAAK,EAAE9C,kBAAkB,KAAK,YAAY,GAAG,OAAO,GAAG,SAAS;gBAChEmL,WAAW,EAAE,SAAS;gBACtB,SAAS,EAAE;kBACTrB,OAAO,EAAE9J,kBAAkB,KAAK,YAAY,GAAG,SAAS,GAAG,SAAS;kBACpEmL,WAAW,EAAE;gBACf;cACF,CAAE;cAAAxG,QAAA,EACH;YAED;cAAAjC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC,eACTjE,OAAA,CAAC7D,MAAM;cACLiK,OAAO,EAAEhF,kBAAkB,KAAK,kBAAkB,GAAG,WAAW,GAAG,UAAW;cAC9EmF,SAAS,eAAEvG,OAAA,CAACnB,SAAS;gBAAAiF,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAE;cACzBuC,OAAO,EAAEA,CAAA,KAAMnF,qBAAqB,CAAC,kBAAkB,CAAE;cACzDuE,EAAE,EAAE;gBACFsF,OAAO,EAAE9J,kBAAkB,KAAK,kBAAkB,GAAG,SAAS,GAAG,aAAa;gBAC9E8C,KAAK,EAAE9C,kBAAkB,KAAK,kBAAkB,GAAG,OAAO,GAAG,SAAS;gBACtEmL,WAAW,EAAE,SAAS;gBACtB,SAAS,EAAE;kBACTrB,OAAO,EAAE9J,kBAAkB,KAAK,kBAAkB,GAAG,SAAS,GAAG,SAAS;kBAC1EmL,WAAW,EAAE;gBACf;cACF,CAAE;cAAAxG,QAAA,EACH;YAED;cAAAjC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC,eACTjE,OAAA,CAAC7D,MAAM;cACLiK,OAAO,EAAEhF,kBAAkB,KAAK,cAAc,GAAG,WAAW,GAAG,UAAW;cAC1EmF,SAAS,eAAEvG,OAAA,CAACjC,YAAY;gBAAA+F,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAE;cAC5BuC,OAAO,EAAEA,CAAA,KAAMnF,qBAAqB,CAAC,cAAc,CAAE;cACrDuE,EAAE,EAAE;gBACFsF,OAAO,EAAE9J,kBAAkB,KAAK,cAAc,GAAG,SAAS,GAAG,aAAa;gBAC1E8C,KAAK,EAAE9C,kBAAkB,KAAK,cAAc,GAAG,OAAO,GAAG,SAAS;gBAClEmL,WAAW,EAAE,SAAS;gBACtB,SAAS,EAAE;kBACTrB,OAAO,EAAE9J,kBAAkB,KAAK,cAAc,GAAG,SAAS,GAAG,SAAS;kBACtEmL,WAAW,EAAE;gBACf;cACF,CAAE;cAAAxG,QAAA,EACH;YAED;cAAAjC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACN;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACD,CAAC,eAGRjE,OAAA,CAACpE,GAAG;QAACgK,EAAE,EAAE;UAAE6G,SAAS,EAAE;QAAQ,CAAE;QAAA1G,QAAA,GAE7B3E,kBAAkB,KAAK,UAAU,iBAChCpB,OAAA,CAAClE,KAAK;UAAC8J,EAAE,EAAE;YAAEC,CAAC,EAAE;UAAE,CAAE;UAAAE,QAAA,EACjBnE,WAAW,CAACE,QAAQ,gBACnB9B,OAAA,CAACpE,GAAG;YAAAmK,QAAA,gBACF/F,OAAA,CAACpE,GAAG;cAACgK,EAAE,EAAE;gBAAEI,OAAO,EAAE,MAAM;gBAAEC,cAAc,EAAE,UAAU;gBAAEE,EAAE,EAAE;cAAE,CAAE;cAAAJ,QAAA,gBAC9D/F,OAAA,CAAC7D,MAAM;gBACLoK,SAAS,eAAEvG,OAAA,CAAC7B,YAAY;kBAAA2F,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAE;gBAC5BuC,OAAO,EAAEA,CAAA,KAAMpC,wBAAwB,CAAC,UAAU,EAAE,KAAK,CAAE;gBAC3DgC,OAAO,EAAC,UAAU;gBAClBK,IAAI,EAAC,OAAO;gBACZvC,KAAK,EAAC,SAAS;gBACf0B,EAAE,EAAE;kBAAE2B,EAAE,EAAE;gBAAE,CAAE;gBAAAxB,QAAA,EACf;cAED;gBAAAjC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC,eACTjE,OAAA,CAAC7D,MAAM;gBACLoK,SAAS,eAAEvG,OAAA,CAAC7B,YAAY;kBAAA2F,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAE;gBAC5BuC,OAAO,EAAEA,CAAA,KAAMpC,wBAAwB,CAAC,UAAU,EAAE,OAAO,CAAE;gBAC7DgC,OAAO,EAAC,UAAU;gBAClBK,IAAI,EAAC,OAAO;gBACZvC,KAAK,EAAC,SAAS;gBAAA6B,QAAA,EAChB;cAED;gBAAAjC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACN,CAAC,EACLyC,oBAAoB,CAAC9E,WAAW,CAACE,QAAQ,CAAC;UAAA;YAAAgC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACxC,CAAC,GACJzD,OAAO,gBACTR,OAAA,CAACpE,GAAG;YAACgK,EAAE,EAAE;cAAEI,OAAO,EAAE,MAAM;cAAEE,UAAU,EAAE,QAAQ;cAAED,cAAc,EAAE,QAAQ;cAAEqG,EAAE,EAAE;YAAE,CAAE;YAAAvG,QAAA,gBAClF/F,OAAA,CAAC1D,gBAAgB;cAACmK,IAAI,EAAE;YAAG;cAAA3C,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,eAC9BjE,OAAA,CAACnE,UAAU;cAAC+J,EAAE,EAAE;gBAAE8G,EAAE,EAAE;cAAE,CAAE;cAAA3G,QAAA,EAAC;YAAuB;cAAAjC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAY,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC5D,CAAC,gBAENjE,OAAA,CAACpE,GAAG;YAACgK,EAAE,EAAE;cAAEkC,SAAS,EAAE,QAAQ;cAAEwE,EAAE,EAAE;YAAE,CAAE;YAAAvG,QAAA,gBACtC/F,OAAA,CAAC3D,KAAK;cAACwP,QAAQ,EAAC,OAAO;cAACjG,EAAE,EAAE;gBAAEO,EAAE,EAAE;cAAE,CAAE;cAAAJ,QAAA,EAAC;YAEvC;cAAAjC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CAAC,eACRjE,OAAA,CAAC7D,MAAM;cACLiK,OAAO,EAAC,UAAU;cAClBG,SAAS,eAAEvG,OAAA,CAACzB,WAAW;gBAAAuF,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAE;cAC3BuC,OAAO,EAAEA,CAAA,KAAM;gBACb/F,UAAU,CAAC,IAAI,CAAC;gBAChBjB,aAAa,CAACgD,iBAAiB,CAAClC,UAAU,EAAE,OAAO,CAAC,CACjDqM,IAAI,CAAC3F,IAAI,IAAI;kBACZnF,cAAc,CAAC8C,IAAI,KAAK;oBACtB,GAAGA,IAAI;oBACP7C,QAAQ,EAAEkF,IAAI,CAACpE;kBACjB,CAAC,CAAC,CAAC;gBACL,CAAC,CAAC,CACDH,KAAK,CAACC,GAAG,IAAI;kBACZC,OAAO,CAACjC,KAAK,CAAC,iCAAiC,EAAEgC,GAAG,CAAC;gBACvD,CAAC,CAAC,CACDkK,OAAO,CAAC,MAAM;kBACbnM,UAAU,CAAC,KAAK,CAAC;gBACnB,CAAC,CAAC;cACN,CAAE;cAAAsF,QAAA,EACH;YAED;cAAAjC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACN;QACN;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACI,CACR,EAGA7C,kBAAkB,KAAK,KAAK,iBAC3BpB,OAAA,CAAClE,KAAK;UAAC8J,EAAE,EAAE;YAAEC,CAAC,EAAE;UAAE,CAAE;UAAAE,QAAA,EACjBnE,WAAW,CAACG,GAAG,gBACd/B,OAAA,CAACpE,GAAG;YAAAmK,QAAA,gBACF/F,OAAA,CAACpE,GAAG;cAACgK,EAAE,EAAE;gBAAEI,OAAO,EAAE,MAAM;gBAAEC,cAAc,EAAE,UAAU;gBAAEE,EAAE,EAAE;cAAE,CAAE;cAAAJ,QAAA,gBAC9D/F,OAAA,CAAC7D,MAAM;gBACLoK,SAAS,eAAEvG,OAAA,CAAC7B,YAAY;kBAAA2F,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAE;gBAC5BuC,OAAO,EAAEA,CAAA,KAAMpC,wBAAwB,CAAC,KAAK,EAAE,KAAK,CAAE;gBACtDgC,OAAO,EAAC,UAAU;gBAClBK,IAAI,EAAC,OAAO;gBACZvC,KAAK,EAAC,SAAS;gBACf0B,EAAE,EAAE;kBAAE2B,EAAE,EAAE;gBAAE,CAAE;gBAAAxB,QAAA,EACf;cAED;gBAAAjC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC,eACTjE,OAAA,CAAC7D,MAAM;gBACLoK,SAAS,eAAEvG,OAAA,CAAC7B,YAAY;kBAAA2F,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAE;gBAC5BuC,OAAO,EAAEA,CAAA,KAAMpC,wBAAwB,CAAC,KAAK,EAAE,OAAO,CAAE;gBACxDgC,OAAO,EAAC,UAAU;gBAClBK,IAAI,EAAC,OAAO;gBACZvC,KAAK,EAAC,SAAS;gBAAA6B,QAAA,EAChB;cAED;gBAAAjC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACN,CAAC,EACL0C,eAAe,CAAC/E,WAAW,CAACG,GAAG,CAAC;UAAA;YAAA+B,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC9B,CAAC,GACJzD,OAAO,gBACTR,OAAA,CAACpE,GAAG;YAACgK,EAAE,EAAE;cAAEI,OAAO,EAAE,MAAM;cAAEE,UAAU,EAAE,QAAQ;cAAED,cAAc,EAAE,QAAQ;cAAEqG,EAAE,EAAE;YAAE,CAAE;YAAAvG,QAAA,gBAClF/F,OAAA,CAAC1D,gBAAgB;cAACmK,IAAI,EAAE;YAAG;cAAA3C,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,eAC9BjE,OAAA,CAACnE,UAAU;cAAC+J,EAAE,EAAE;gBAAE8G,EAAE,EAAE;cAAE,CAAE;cAAA3G,QAAA,EAAC;YAAuB;cAAAjC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAY,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC5D,CAAC,gBAENjE,OAAA,CAACpE,GAAG;YAACgK,EAAE,EAAE;cAAEkC,SAAS,EAAE,QAAQ;cAAEwE,EAAE,EAAE;YAAE,CAAE;YAAAvG,QAAA,gBACtC/F,OAAA,CAAC3D,KAAK;cAACwP,QAAQ,EAAC,OAAO;cAACjG,EAAE,EAAE;gBAAEO,EAAE,EAAE;cAAE,CAAE;cAAAJ,QAAA,EAAC;YAEvC;cAAAjC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CAAC,eACRjE,OAAA,CAAC7D,MAAM;cACLiK,OAAO,EAAC,UAAU;cAClBG,SAAS,eAAEvG,OAAA,CAACzB,WAAW;gBAAAuF,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAE;cAC3BuC,OAAO,EAAEA,CAAA,KAAM;gBACb/F,UAAU,CAAC,IAAI,CAAC;gBAChBjB,aAAa,CAACsD,mBAAmB,CAACxC,UAAU,EAAE,OAAO,CAAC,CACnDqM,IAAI,CAAC3F,IAAI,IAAI;kBACZnF,cAAc,CAAC8C,IAAI,KAAK;oBACtB,GAAGA,IAAI;oBACP5C,GAAG,EAAEiF,IAAI,CAACpE;kBACZ,CAAC,CAAC,CAAC;gBACL,CAAC,CAAC,CACDH,KAAK,CAACC,GAAG,IAAI;kBACZC,OAAO,CAACjC,KAAK,CAAC,4BAA4B,EAAEgC,GAAG,CAAC;gBAClD,CAAC,CAAC,CACDkK,OAAO,CAAC,MAAM;kBACbnM,UAAU,CAAC,KAAK,CAAC;gBACnB,CAAC,CAAC;cACN,CAAE;cAAAsF,QAAA,EACH;YAED;cAAAjC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACN;QACN;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACI,CACR,EAGA7C,kBAAkB,KAAK,QAAQ,iBAC9BpB,OAAA,CAAClE,KAAK;UAAC8J,EAAE,EAAE;YAAEC,CAAC,EAAE;UAAE,CAAE;UAAAE,QAAA,EACjBnE,WAAW,CAACI,MAAM,gBACjBhC,OAAA,CAACpE,GAAG;YAAAmK,QAAA,gBACF/F,OAAA,CAACpE,GAAG;cAACgK,EAAE,EAAE;gBAAEI,OAAO,EAAE,MAAM;gBAAEC,cAAc,EAAE,UAAU;gBAAEE,EAAE,EAAE;cAAE,CAAE;cAAAJ,QAAA,gBAC9D/F,OAAA,CAAC7D,MAAM;gBACLoK,SAAS,eAAEvG,OAAA,CAAC7B,YAAY;kBAAA2F,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAE;gBAC5BuC,OAAO,EAAEA,CAAA,KAAMpC,wBAAwB,CAAC,QAAQ,EAAE,KAAK,CAAE;gBACzDgC,OAAO,EAAC,UAAU;gBAClBK,IAAI,EAAC,OAAO;gBACZvC,KAAK,EAAC,SAAS;gBACf0B,EAAE,EAAE;kBAAE2B,EAAE,EAAE;gBAAE,CAAE;gBAAAxB,QAAA,EACf;cAED;gBAAAjC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC,eACTjE,OAAA,CAAC7D,MAAM;gBACLoK,SAAS,eAAEvG,OAAA,CAAC7B,YAAY;kBAAA2F,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAE;gBAC5BuC,OAAO,EAAEA,CAAA,KAAMpC,wBAAwB,CAAC,QAAQ,EAAE,OAAO,CAAE;gBAC3DgC,OAAO,EAAC,UAAU;gBAClBK,IAAI,EAAC,OAAO;gBACZvC,KAAK,EAAC,SAAS;gBAAA6B,QAAA,EAChB;cAED;gBAAAjC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACN,CAAC,EACL2C,kBAAkB,CAAChF,WAAW,CAACI,MAAM,CAAC;UAAA;YAAA8B,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACpC,CAAC,GACJzD,OAAO,gBACTR,OAAA,CAACpE,GAAG;YAACgK,EAAE,EAAE;cAAEI,OAAO,EAAE,MAAM;cAAEE,UAAU,EAAE,QAAQ;cAAED,cAAc,EAAE,QAAQ;cAAEqG,EAAE,EAAE;YAAE,CAAE;YAAAvG,QAAA,gBAClF/F,OAAA,CAAC1D,gBAAgB;cAACmK,IAAI,EAAE;YAAG;cAAA3C,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,eAC9BjE,OAAA,CAACnE,UAAU;cAAC+J,EAAE,EAAE;gBAAE8G,EAAE,EAAE;cAAE,CAAE;cAAA3G,QAAA,EAAC;YAAuB;cAAAjC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAY,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC5D,CAAC,gBAENjE,OAAA,CAACpE,GAAG;YAACgK,EAAE,EAAE;cAAEkC,SAAS,EAAE,QAAQ;cAAEwE,EAAE,EAAE;YAAE,CAAE;YAAAvG,QAAA,gBACtC/F,OAAA,CAAC3D,KAAK;cAACwP,QAAQ,EAAC,OAAO;cAACjG,EAAE,EAAE;gBAAEO,EAAE,EAAE;cAAE,CAAE;cAAAJ,QAAA,EAAC;YAEvC;cAAAjC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CAAC,eACRjE,OAAA,CAAC7D,MAAM;cACLiK,OAAO,EAAC,UAAU;cAClBG,SAAS,eAAEvG,OAAA,CAACzB,WAAW;gBAAAuF,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAE;cAC3BuC,OAAO,EAAEA,CAAA,KAAM;gBACb/F,UAAU,CAAC,IAAI,CAAC;gBAChBjB,aAAa,CAACwD,eAAe,CAAC1C,UAAU,EAAE,OAAO,CAAC,CAC/CqM,IAAI,CAAC3F,IAAI,IAAI;kBACZnF,cAAc,CAAC8C,IAAI,KAAK;oBACtB,GAAGA,IAAI;oBACP3C,MAAM,EAAEgF,IAAI,CAACpE;kBACf,CAAC,CAAC,CAAC;gBACL,CAAC,CAAC,CACDH,KAAK,CAACC,GAAG,IAAI;kBACZC,OAAO,CAACjC,KAAK,CAAC,+BAA+B,EAAEgC,GAAG,CAAC;gBACrD,CAAC,CAAC,CACDkK,OAAO,CAAC,MAAM;kBACbnM,UAAU,CAAC,KAAK,CAAC;gBACnB,CAAC,CAAC;cACN,CAAE;cAAAsF,QAAA,EACH;YAED;cAAAjC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACN;QACN;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACI,CACR,EAGA7C,kBAAkB,KAAK,YAAY,iBAClCpB,OAAA,CAAClE,KAAK;UAAC8J,EAAE,EAAE;YAAEC,CAAC,EAAE;UAAE,CAAE;UAAAE,QAAA,EACjBnE,WAAW,CAACK,SAAS,gBACpBjC,OAAA,CAACpE,GAAG;YAAAmK,QAAA,gBACF/F,OAAA,CAACpE,GAAG;cAACgK,EAAE,EAAE;gBAAEI,OAAO,EAAE,MAAM;gBAAEC,cAAc,EAAE,UAAU;gBAAEE,EAAE,EAAE;cAAE,CAAE;cAAAJ,QAAA,gBAC9D/F,OAAA,CAAC7D,MAAM;gBACLoK,SAAS,eAAEvG,OAAA,CAAC7B,YAAY;kBAAA2F,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAE;gBAC5BuC,OAAO,EAAEA,CAAA,KAAMpC,wBAAwB,CAAC,YAAY,EAAE,KAAK,CAAE;gBAC7DgC,OAAO,EAAC,UAAU;gBAClBK,IAAI,EAAC,OAAO;gBACZvC,KAAK,EAAC,SAAS;gBACf0B,EAAE,EAAE;kBAAE2B,EAAE,EAAE;gBAAE,CAAE;gBAAAxB,QAAA,EACf;cAED;gBAAAjC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC,eACTjE,OAAA,CAAC7D,MAAM;gBACLoK,SAAS,eAAEvG,OAAA,CAAC7B,YAAY;kBAAA2F,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAE;gBAC5BuC,OAAO,EAAEA,CAAA,KAAMpC,wBAAwB,CAAC,YAAY,EAAE,OAAO,CAAE;gBAC/DgC,OAAO,EAAC,UAAU;gBAClBK,IAAI,EAAC,OAAO;gBACZvC,KAAK,EAAC,SAAS;gBAAA6B,QAAA,EAChB;cAED;gBAAAjC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACN,CAAC,EACL8C,qBAAqB,CAACnF,WAAW,CAACK,SAAS,CAAC;UAAA;YAAA6B,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC1C,CAAC,GACJzD,OAAO,gBACTR,OAAA,CAACpE,GAAG;YAACgK,EAAE,EAAE;cAAEI,OAAO,EAAE,MAAM;cAAEE,UAAU,EAAE,QAAQ;cAAED,cAAc,EAAE,QAAQ;cAAEqG,EAAE,EAAE;YAAE,CAAE;YAAAvG,QAAA,gBAClF/F,OAAA,CAAC1D,gBAAgB;cAACmK,IAAI,EAAE;YAAG;cAAA3C,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,eAC9BjE,OAAA,CAACnE,UAAU;cAAC+J,EAAE,EAAE;gBAAE8G,EAAE,EAAE;cAAE,CAAE;cAAA3G,QAAA,EAAC;YAAuB;cAAAjC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAY,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC5D,CAAC,gBAENjE,OAAA,CAACpE,GAAG;YAACgK,EAAE,EAAE;cAAEkC,SAAS,EAAE,QAAQ;cAAEwE,EAAE,EAAE;YAAE,CAAE;YAAAvG,QAAA,gBACtC/F,OAAA,CAAC3D,KAAK;cAACwP,QAAQ,EAAC,OAAO;cAACjG,EAAE,EAAE;gBAAEO,EAAE,EAAE;cAAE,CAAE;cAAAJ,QAAA,EAAC;YAEvC;cAAAjC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CAAC,eACRjE,OAAA,CAAC7D,MAAM;cACLiK,OAAO,EAAC,UAAU;cAClBG,SAAS,eAAEvG,OAAA,CAACzB,WAAW;gBAAAuF,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAE;cAC3BuC,OAAO,EAAEA,CAAA,KAAM;gBACb/F,UAAU,CAAC,IAAI,CAAC;gBAChBjB,aAAa,CAAC0D,kBAAkB,CAAC5C,UAAU,EAAE,OAAO,CAAC,CAClDqM,IAAI,CAAC3F,IAAI,IAAI;kBACZnF,cAAc,CAAC8C,IAAI,KAAK;oBACtB,GAAGA,IAAI;oBACP1C,SAAS,EAAE+E,IAAI,CAACpE;kBAClB,CAAC,CAAC,CAAC;gBACL,CAAC,CAAC,CACDH,KAAK,CAACC,GAAG,IAAI;kBACZC,OAAO,CAACjC,KAAK,CAAC,mCAAmC,EAAEgC,GAAG,CAAC;gBACzD,CAAC,CAAC,CACDkK,OAAO,CAAC,MAAM;kBACbnM,UAAU,CAAC,KAAK,CAAC;gBACnB,CAAC,CAAC;cACN,CAAE;cAAAsF,QAAA,EACH;YAED;cAAAjC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACN;QACN;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACI,CACR,EAGA7C,kBAAkB,KAAK,kBAAkB,iBACxCpB,OAAA,CAAClE,KAAK;UAAC8J,EAAE,EAAE;YAAEC,CAAC,EAAE;UAAE,CAAE;UAAAE,QAAA,EACjBnE,WAAW,CAACM,eAAe,gBAC1BlC,OAAA,CAACpE,GAAG;YAAAmK,QAAA,gBACF/F,OAAA,CAACpE,GAAG;cAACgK,EAAE,EAAE;gBAAEI,OAAO,EAAE,MAAM;gBAAEC,cAAc,EAAE,UAAU;gBAAEE,EAAE,EAAE;cAAE,CAAE;cAAAJ,QAAA,gBAC9D/F,OAAA,CAAC7D,MAAM;gBACLoK,SAAS,eAAEvG,OAAA,CAAC7B,YAAY;kBAAA2F,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAE;gBAC5BuC,OAAO,EAAEA,CAAA,KAAMpC,wBAAwB,CAAC,kBAAkB,EAAE,KAAK,CAAE;gBACnEgC,OAAO,EAAC,UAAU;gBAClBK,IAAI,EAAC,OAAO;gBACZvC,KAAK,EAAC,SAAS;gBACf0B,EAAE,EAAE;kBAAE2B,EAAE,EAAE;gBAAE,CAAE;gBAAAxB,QAAA,EACf;cAED;gBAAAjC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC,eACTjE,OAAA,CAAC7D,MAAM;gBACLoK,SAAS,eAAEvG,OAAA,CAAC7B,YAAY;kBAAA2F,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAE;gBAC5BuC,OAAO,EAAEA,CAAA,KAAMpC,wBAAwB,CAAC,kBAAkB,EAAE,OAAO,CAAE;gBACrEgC,OAAO,EAAC,UAAU;gBAClBK,IAAI,EAAC,OAAO;gBACZvC,KAAK,EAAC,SAAS;gBAAA6B,QAAA,EAChB;cAED;gBAAAjC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACN,CAAC,EACL4C,2BAA2B,CAACjF,WAAW,CAACM,eAAe,CAAC;UAAA;YAAA4B,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACtD,CAAC,gBAENjE,OAAA,CAACpE,GAAG;YAACgK,EAAE,EAAE;cAAEkC,SAAS,EAAE,QAAQ;cAAEwE,EAAE,EAAE;YAAE,CAAE;YAAAvG,QAAA,gBACtC/F,OAAA,CAACnE,UAAU;cAACuK,OAAO,EAAC,IAAI;cAACR,EAAE,EAAE;gBAAEO,EAAE,EAAE;cAAE,CAAE;cAAAJ,QAAA,EAAC;YAExC;cAAAjC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAY,CAAC,eACbjE,OAAA,CAACnE,UAAU;cAACuK,OAAO,EAAC,OAAO;cAAClC,KAAK,EAAC,gBAAgB;cAAC0B,EAAE,EAAE;gBAAEO,EAAE,EAAE;cAAE,CAAE;cAAAJ,QAAA,EAAC;YAElE;cAAAjC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAY,CAAC,eACbjE,OAAA,CAAC7D,MAAM;cACLiK,OAAO,EAAC,WAAW;cACnBlC,KAAK,EAAC,MAAM;cACZqC,SAAS,eAAEvG,OAAA,CAACnB,SAAS;gBAAAiF,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAE;cACzBuC,OAAO,EAAEA,CAAA,KAAM;gBACbrF,aAAa,CAAC,kBAAkB,CAAC;gBACjCF,aAAa,CAAC,IAAI,CAAC;cACrB,CAAE;cAAA8E,QAAA,EACH;YAED;cAAAjC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACN;QACN;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACI,CACR,EAGA7C,kBAAkB,KAAK,cAAc,iBACpCpB,OAAA,CAAClE,KAAK;UAAC8J,EAAE,EAAE;YAAEC,CAAC,EAAE;UAAE,CAAE;UAAAE,QAAA,EACjBnE,WAAW,CAACO,WAAW,gBACtBnC,OAAA,CAACpE,GAAG;YAAAmK,QAAA,gBACF/F,OAAA,CAACpE,GAAG;cAACgK,EAAE,EAAE;gBAAEI,OAAO,EAAE,MAAM;gBAAEC,cAAc,EAAE,UAAU;gBAAEE,EAAE,EAAE;cAAE,CAAE;cAAAJ,QAAA,gBAC9D/F,OAAA,CAAC7D,MAAM;gBACLoK,SAAS,eAAEvG,OAAA,CAAC7B,YAAY;kBAAA2F,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAE;gBAC5BuC,OAAO,EAAEA,CAAA,KAAMpC,wBAAwB,CAAC,cAAc,EAAE,KAAK,CAAE;gBAC/DgC,OAAO,EAAC,UAAU;gBAClBK,IAAI,EAAC,OAAO;gBACZvC,KAAK,EAAC,SAAS;gBACf0B,EAAE,EAAE;kBAAE2B,EAAE,EAAE;gBAAE,CAAE;gBAAAxB,QAAA,EACf;cAED;gBAAAjC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC,eACTjE,OAAA,CAAC7D,MAAM;gBACLoK,SAAS,eAAEvG,OAAA,CAAC7B,YAAY;kBAAA2F,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAE;gBAC5BuC,OAAO,EAAEA,CAAA,KAAMpC,wBAAwB,CAAC,cAAc,EAAE,OAAO,CAAE;gBACjEgC,OAAO,EAAC,UAAU;gBAClBK,IAAI,EAAC,OAAO;gBACZvC,KAAK,EAAC,SAAS;gBAAA6B,QAAA,EAChB;cAED;gBAAAjC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACN,CAAC,EACL6C,uBAAuB,CAAClF,WAAW,CAACO,WAAW,CAAC;UAAA;YAAA2B,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC9C,CAAC,gBAENjE,OAAA,CAACpE,GAAG;YAACgK,EAAE,EAAE;cAAEkC,SAAS,EAAE,QAAQ;cAAEwE,EAAE,EAAE;YAAE,CAAE;YAAAvG,QAAA,gBACtC/F,OAAA,CAACnE,UAAU;cAACuK,OAAO,EAAC,IAAI;cAACR,EAAE,EAAE;gBAAEO,EAAE,EAAE;cAAE,CAAE;cAAAJ,QAAA,EAAC;YAExC;cAAAjC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAY,CAAC,eACbjE,OAAA,CAACnE,UAAU;cAACuK,OAAO,EAAC,OAAO;cAAClC,KAAK,EAAC,gBAAgB;cAAC0B,EAAE,EAAE;gBAAEO,EAAE,EAAE;cAAE,CAAE;cAAAJ,QAAA,EAAC;YAElE;cAAAjC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAY,CAAC,eACbjE,OAAA,CAAC7D,MAAM;cACLiK,OAAO,EAAC,WAAW;cACnBlC,KAAK,EAAC,SAAS;cACfqC,SAAS,eAAEvG,OAAA,CAACjC,YAAY;gBAAA+F,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAE;cAC5BuC,OAAO,EAAEA,CAAA,KAAM;gBACbrF,aAAa,CAAC,cAAc,CAAC;gBAC7B;gBACA,MAAM+D,KAAK,GAAG,IAAIC,IAAI,CAAC,CAAC;gBACxB,MAAMC,SAAS,GAAG,IAAID,IAAI,CAAC,CAAC;gBAC5BC,SAAS,CAACC,QAAQ,CAACH,KAAK,CAACI,QAAQ,CAAC,CAAC,GAAG,CAAC,CAAC;gBAExC/D,WAAW,CAAC;kBACV,GAAGD,QAAQ;kBACXG,WAAW,EAAE2D,SAAS,CAACG,WAAW,CAAC,CAAC,CAACC,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC;kBAClD9D,SAAS,EAAEwD,KAAK,CAACK,WAAW,CAAC,CAAC,CAACC,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC;gBAC7C,CAAC,CAAC;gBACFvE,aAAa,CAAC,IAAI,CAAC;cACrB,CAAE;cAAA8E,QAAA,EACH;YAED;cAAAjC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACN;QACN;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACI,CACR;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACE,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,EAGLwH,YAAY,CAAC,CAAC;EAAA;IAAA3H,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACZ,CAAC;AAEV,CAAC;AAAC7D,EAAA,CAx+CID,iBAAiB;EAAA,QACJf,WAAW,EACLC,SAAS,EACfC,OAAO;AAAA;AAAAuN,EAAA,GAHpB1M,iBAAiB;AA0+CvB,eAAeA,iBAAiB;AAAC,IAAA0M,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}