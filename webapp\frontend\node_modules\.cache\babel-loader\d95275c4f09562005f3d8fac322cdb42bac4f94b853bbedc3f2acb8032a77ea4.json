{"ast": null, "code": "'use client';\n\nexport { default } from './MenuItem';\nexport * from './menuItemClasses';\nexport { default as menuItemClasses } from './menuItemClasses';", "map": {"version": 3, "names": ["default", "menuItemClasses"], "sources": ["C:/Users/<USER>/PycharmProjects/CMS/webapp/frontend/node_modules/@mui/material/MenuItem/index.js"], "sourcesContent": ["'use client';\n\nexport { default } from './MenuItem';\nexport * from './menuItemClasses';\nexport { default as menuItemClasses } from './menuItemClasses';"], "mappings": "AAAA,YAAY;;AAEZ,SAASA,OAAO,QAAQ,YAAY;AACpC,cAAc,mBAAmB;AACjC,SAASA,OAAO,IAAIC,eAAe,QAAQ,mBAAmB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}