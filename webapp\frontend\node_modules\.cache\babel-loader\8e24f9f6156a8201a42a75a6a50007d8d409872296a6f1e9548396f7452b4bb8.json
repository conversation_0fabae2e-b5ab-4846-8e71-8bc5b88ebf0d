{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\PycharmProjects\\\\CMS\\\\webapp\\\\frontend\\\\src\\\\components\\\\cavi\\\\MetriPosatiSemplificatoForm.js\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect } from 'react';\nimport { Box, Typography, TextField, Button, Paper, Table, TableBody, TableCell, TableContainer, TableHead, TableRow, CircularProgress, Alert, Chip, Divider, Grid, FormControl, InputLabel, Select, MenuItem, Dialog, DialogTitle, DialogContent, DialogActions, DialogContentText } from '@mui/material';\nimport { useNavigate } from 'react-router-dom';\nimport caviService from '../../services/caviService';\nimport parcoCaviService from '../../services/parcoCaviService';\nimport { CABLE_STATES, REEL_STATES, determineCableState, determineReelState, canModifyCable, isCableSpare, isCableInstalled, getCableStateColor, getReelStateColor } from '../../utils/stateUtils';\nimport { redirectToVisualizzaCavi } from '../../utils/navigationUtils';\nimport IncompatibleReelDialog from './IncompatibleReelDialog';\n\n/**\n * Componente per l'inserimento dei metri posati di un cavo\n * Versione ultra-semplificata con workflow compresso in un'unica pagina\n *\n * @param {Object} props - Proprietà del componente\n * @param {string} props.cantiereId - ID del cantiere\n * @param {Function} props.onSuccess - Funzione chiamata al successo dell'operazione\n * @param {Function} props.onError - Funzione chiamata in caso di errore\n */\nimport { jsxDEV as _jsxDEV, Fragment as _Fragment } from \"react/jsx-dev-runtime\";\nconst MetriPosatiSemplificatoForm = ({\n  cantiereId,\n  onSuccess,\n  onError\n}) => {\n  _s();\n  const navigate = useNavigate();\n\n  // Stati per i dati\n  const [cavi, setCavi] = useState([]);\n  const [bobine, setBobine] = useState([]);\n  const [selectedCavo, setSelectedCavo] = useState(null);\n  const [formData, setFormData] = useState({\n    id_cavo: '',\n    metri_posati: '',\n    id_bobina: ''\n  });\n\n  // Stati per il caricamento\n  const [loading, setLoading] = useState(false);\n  const [caviLoading, setCaviLoading] = useState(false);\n  const [bobineLoading, setBobineLoading] = useState(false);\n  const [saving, setSaving] = useState(false);\n\n  // Stati per la validazione\n  const [formErrors, setFormErrors] = useState({});\n  const [formWarnings, setFormWarnings] = useState({});\n\n  // Stati per i dialoghi speciali\n  const [showIncompatibleReelDialog, setShowIncompatibleReelDialog] = useState(false);\n  const [incompatibleReelData, setIncompatibleReelData] = useState({\n    cavo: null,\n    bobina: null\n  });\n  const [showAlreadyLaidDialog, setShowAlreadyLaidDialog] = useState(false);\n  const [alreadyLaidCavo, setAlreadyLaidCavo] = useState(null);\n\n  // Carica la lista dei cavi e delle bobine all'avvio\n  useEffect(() => {\n    loadCavi();\n    loadBobine();\n  }, [cantiereId]);\n\n  // Carica la lista dei cavi\n  const loadCavi = async () => {\n    try {\n      setCaviLoading(true);\n      const caviData = await caviService.getCavi(cantiereId);\n\n      // Filtra i cavi che non sono SPARE\n      const caviAttivi = caviData.filter(cavo => !isCableSpare(cavo));\n      setCavi(caviAttivi);\n    } catch (error) {\n      console.error('Errore nel caricamento dei cavi:', error);\n      onError('Errore nel caricamento dei cavi: ' + (error.message || 'Errore sconosciuto'));\n    } finally {\n      setCaviLoading(false);\n    }\n  };\n\n  // Carica la lista delle bobine\n  const loadBobine = async () => {\n    try {\n      setBobineLoading(true);\n      console.log('Caricamento bobine per cantiere:', cantiereId);\n      const bobineData = await parcoCaviService.getBobine(cantiereId);\n      console.log('Bobine caricate:', bobineData);\n      setBobine(bobineData);\n    } catch (error) {\n      console.error('Errore nel caricamento delle bobine:', error);\n      onError('Errore nel caricamento delle bobine: ' + (error.message || 'Errore sconosciuto'));\n    } finally {\n      setBobineLoading(false);\n    }\n  };\n\n  // Gestisce la selezione di un cavo\n  const handleCavoSelect = cavo => {\n    // Verifica se il cavo è già posato\n    if (isCableInstalled(cavo)) {\n      setAlreadyLaidCavo(cavo);\n      setShowAlreadyLaidDialog(true);\n      return;\n    }\n    setSelectedCavo(cavo);\n    setFormData({\n      id_cavo: cavo.id_cavo,\n      metri_posati: '',\n      id_bobina: ''\n    });\n    setFormErrors({});\n    setFormWarnings({});\n  };\n\n  // Gestisce la modifica dei campi del form\n  const handleInputChange = e => {\n    const {\n      name,\n      value\n    } = e.target;\n    setFormData(prev => ({\n      ...prev,\n      [name]: value\n    }));\n\n    // Validazione in tempo reale\n    validateField(name, value);\n  };\n\n  // Validazione di un singolo campo\n  const validateField = (name, value) => {\n    const newErrors = {\n      ...formErrors\n    };\n    const newWarnings = {\n      ...formWarnings\n    };\n    if (name === 'metri_posati') {\n      // Validazione metri posati\n      if (value === '') {\n        newErrors.metri_posati = 'I metri posati sono obbligatori';\n      } else if (isNaN(value) || parseFloat(value) < 0) {\n        newErrors.metri_posati = 'I metri posati devono essere un numero positivo';\n      } else {\n        delete newErrors.metri_posati;\n\n        // Avvisi sui metri posati\n        const metriPosati = parseFloat(value);\n        if (selectedCavo && metriPosati > selectedCavo.metri_teorici) {\n          newWarnings.metri_posati = `I metri posati (${metriPosati}) superano i metri teorici (${selectedCavo.metri_teorici})`;\n        } else {\n          delete newWarnings.metri_posati;\n        }\n\n        // Avvisi sulla bobina selezionata\n        if (formData.id_bobina && formData.id_bobina !== 'BOBINA_VUOTA') {\n          const selectedBobina = bobine.find(b => b.id_bobina === formData.id_bobina);\n          if (selectedBobina && metriPosati > selectedBobina.metri_residui) {\n            newWarnings.id_bobina = `I metri posati (${metriPosati}) superano i metri residui della bobina (${selectedBobina.metri_residui})`;\n          } else {\n            delete newWarnings.id_bobina;\n          }\n        }\n      }\n    }\n    if (name === 'id_bobina') {\n      // Validazione bobina\n      if (value === '') {\n        newErrors.id_bobina = 'La bobina è obbligatoria';\n      } else {\n        delete newErrors.id_bobina;\n\n        // Avvisi sulla bobina selezionata\n        if (value !== 'BOBINA_VUOTA' && formData.metri_posati) {\n          const metriPosati = parseFloat(formData.metri_posati);\n          const selectedBobina = bobine.find(b => b.id_bobina === value);\n          if (selectedBobina && metriPosati > selectedBobina.metri_residui) {\n            newWarnings.id_bobina = `I metri posati (${metriPosati}) superano i metri residui della bobina (${selectedBobina.metri_residui})`;\n          } else {\n            delete newWarnings.id_bobina;\n          }\n        }\n      }\n    }\n    setFormErrors(newErrors);\n    setFormWarnings(newWarnings);\n  };\n\n  // Validazione completa del form\n  const validateForm = () => {\n    const newErrors = {};\n\n    // Validazione metri posati\n    if (!formData.metri_posati) {\n      newErrors.metri_posati = 'I metri posati sono obbligatori';\n    } else if (isNaN(formData.metri_posati) || parseFloat(formData.metri_posati) < 0) {\n      newErrors.metri_posati = 'I metri posati devono essere un numero positivo';\n    }\n\n    // Validazione bobina\n    if (!formData.id_bobina) {\n      newErrors.id_bobina = 'La bobina è obbligatoria';\n    }\n    setFormErrors(newErrors);\n    return Object.keys(newErrors).length === 0;\n  };\n\n  // Verifica la compatibilità tra cavo e bobina\n  const checkCompatibility = () => {\n    if (formData.id_bobina === 'BOBINA_VUOTA') {\n      return true; // BOBINA_VUOTA è sempre compatibile\n    }\n    const selectedBobina = bobine.find(b => b.id_bobina === formData.id_bobina);\n    if (!selectedBobina) {\n      return false;\n    }\n\n    // Verifica compatibilità tipologia\n    const tipologiaCompatibile = selectedCavo.tipologia === selectedBobina.tipologia;\n\n    // Verifica compatibilità sezione\n    const sezioneCompatibile = String(selectedCavo.sezione) === String(selectedBobina.sezione);\n    return tipologiaCompatibile && sezioneCompatibile;\n  };\n\n  // Gestisce il salvataggio dei dati\n  const handleSave = async () => {\n    // Se non ci sono bobine disponibili, imposta automaticamente BOBINA_VUOTA\n    if (bobine.length === 0 && !bobineLoading) {\n      if (!formData.metri_posati || isNaN(parseFloat(formData.metri_posati)) || parseFloat(formData.metri_posati) <= 0) {\n        setFormErrors({\n          ...formErrors,\n          metri_posati: 'I metri posati sono obbligatori e devono essere maggiori di zero'\n        });\n        return;\n      }\n\n      // Imposta BOBINA_VUOTA e procedi con il salvataggio\n      formData.id_bobina = 'BOBINA_VUOTA';\n    } else {\n      // Validazione completa\n      if (!validateForm()) {\n        return;\n      }\n\n      // Verifica compatibilità\n      if (!checkCompatibility()) {\n        // Mostra dialog per incompatibilità\n        const selectedBobina = bobine.find(b => b.id_bobina === formData.id_bobina);\n        setIncompatibleReelData({\n          cavo: selectedCavo,\n          bobina: selectedBobina\n        });\n        setShowIncompatibleReelDialog(true);\n        return;\n      }\n    }\n\n    // Procedi con il salvataggio\n    try {\n      setSaving(true);\n\n      // Converti metri posati in numero\n      const metriPosati = parseFloat(formData.metri_posati);\n\n      // Chiamata API\n      console.log('Invio richiesta updateMetriPosati con i seguenti parametri:');\n      console.log('- cantiereId:', cantiereId);\n      console.log('- id_cavo:', formData.id_cavo);\n      console.log('- metri_posati:', metriPosati);\n      console.log('- id_bobina:', formData.id_bobina);\n\n      // Imposta sempre forceOver a true per evitare blocchi quando la bobina va in OVER\n      await caviService.updateMetriPosati(cantiereId, formData.id_cavo, metriPosati, formData.id_bobina, true // Forza sempre a true per evitare blocchi\n      );\n\n      // Mostra messaggio di successo\n      onSuccess('Metri posati aggiornati con successo');\n\n      // Resetta il form\n      setSelectedCavo(null);\n      setFormData({\n        id_cavo: '',\n        metri_posati: '',\n        id_bobina: ''\n      });\n\n      // Ricarica i dati\n      loadCavi();\n      loadBobine();\n    } catch (error) {\n      console.error('Errore durante il salvataggio:', error);\n      onError('Errore durante il salvataggio: ' + (error.message || 'Errore sconosciuto'));\n    } finally {\n      setSaving(false);\n    }\n  };\n\n  // Gestisce l'aggiornamento del cavo per compatibilità\n  const handleUpdateCavoForCompatibility = async () => {\n    try {\n      setSaving(true);\n      setShowIncompatibleReelDialog(false);\n      const {\n        cavo,\n        bobina\n      } = incompatibleReelData;\n\n      // Aggiorna il cavo per renderlo compatibile con la bobina\n      await caviService.updateCavoForCompatibility(cantiereId, cavo.id_cavo, {\n        id_bobina: bobina.id_bobina,\n        tipologia: bobina.tipologia,\n        sezione: bobina.sezione\n      });\n\n      // Procedi con l'aggiornamento dei metri posati\n      await caviService.updateMetriPosati(cantiereId, formData.id_cavo, parseFloat(formData.metri_posati), formData.id_bobina, true // Forza sempre a true per evitare blocchi\n      );\n\n      // Mostra messaggio di successo\n      onSuccess('Cavo aggiornato e metri posati registrati con successo');\n\n      // Resetta il form\n      setSelectedCavo(null);\n      setFormData({\n        id_cavo: '',\n        metri_posati: '',\n        id_bobina: ''\n      });\n\n      // Ricarica i dati\n      loadCavi();\n      loadBobine();\n    } catch (error) {\n      console.error('Errore durante l\\'aggiornamento del cavo:', error);\n      onError('Errore durante l\\'aggiornamento del cavo: ' + (error.message || 'Errore sconosciuto'));\n    } finally {\n      setSaving(false);\n    }\n  };\n\n  // Gestisce la chiusura del dialogo per cavi già posati\n  const handleCloseAlreadyLaidDialog = () => {\n    setShowAlreadyLaidDialog(false);\n    setAlreadyLaidCavo(null);\n  };\n\n  // Gestisce la selezione di un altro cavo\n  const handleSelectAnotherCable = () => {\n    handleCloseAlreadyLaidDialog();\n  };\n\n  // Gestisce l'opzione di modificare la bobina di un cavo già posato\n  const handleModifyReel = () => {\n    if (alreadyLaidCavo) {\n      navigate(`/dashboard/cavi/posa/modifica-bobina?cavoId=${alreadyLaidCavo.id_cavo}`);\n    }\n    handleCloseAlreadyLaidDialog();\n  };\n\n  // Stato per le bobine compatibili\n  const [compatibleBobine, setCompatibleBobine] = useState([]);\n  const [compatibleBobineLoading, setCompatibleBobineLoading] = useState(false);\n\n  // Carica le bobine compatibili dal backend quando viene selezionato un cavo\n  useEffect(() => {\n    if (selectedCavo) {\n      loadCompatibleBobine(selectedCavo);\n    } else {\n      setCompatibleBobine([]);\n    }\n  }, [selectedCavo]);\n\n  // Carica le bobine compatibili dal backend\n  const loadCompatibleBobine = async cavo => {\n    try {\n      setCompatibleBobineLoading(true);\n      console.log('Caricamento bobine compatibili per cavo:', cavo);\n\n      // Chiamata diretta all'API del backend per ottenere le bobine compatibili\n      const compatibleBobineData = await parcoCaviService.getBobineCompatibili(cantiereId, cavo.tipologia, null,\n      // n_conduttori non è più utilizzato per la compatibilità\n      cavo.sezione);\n      console.log('Bobine compatibili caricate dal backend:', compatibleBobineData);\n      setCompatibleBobine(compatibleBobineData);\n    } catch (error) {\n      console.error('Errore nel caricamento delle bobine compatibili:', error);\n      onError('Errore nel caricamento delle bobine compatibili: ' + (error.message || 'Errore sconosciuto'));\n\n      // In caso di errore, fallback al filtraggio locale\n      console.log('Fallback al filtraggio locale delle bobine compatibili');\n      const localCompatibleBobine = getLocalCompatibleBobine(cavo);\n      setCompatibleBobine(localCompatibleBobine);\n    } finally {\n      setCompatibleBobineLoading(false);\n    }\n  };\n\n  // Filtra le bobine compatibili localmente (usato come fallback)\n  const getLocalCompatibleBobine = cavo => {\n    if (!cavo) return [];\n    console.log('Filtrando bobine compatibili localmente per cavo:', cavo);\n    console.log('Bobine disponibili:', bobine);\n\n    // Converti i valori in stringhe per un confronto più robusto\n    const cavoTipologia = String(cavo.tipologia || '').trim();\n    const cavoSezione = String(cavo.sezione || '').trim();\n    const localCompatibleBobine = bobine.filter(bobina => {\n      // Converti i valori della bobina in stringhe\n      const bobinaTipologia = String(bobina.tipologia || '').trim();\n      const bobinaSezione = String(bobina.sezione || '').trim();\n\n      // Determina lo stato della bobina\n      const bobinaState = determineReelState(bobina.metri_residui, bobina.metri_totali);\n\n      // Log per debug\n      console.log(`Verifica bobina ${bobina.id_bobina}:`, {\n        tipologia: `'${bobinaTipologia}' === '${cavoTipologia}'`,\n        sezione: `'${bobinaSezione}' === '${cavoSezione}'`,\n        stato: bobinaState,\n        isCompatibile: bobinaTipologia === cavoTipologia && bobinaSezione === cavoSezione && bobinaState !== REEL_STATES.TERMINATA\n      });\n      return bobinaTipologia === cavoTipologia && bobinaSezione === cavoSezione && bobinaState !== REEL_STATES.TERMINATA;\n    });\n    console.log('Bobine compatibili trovate localmente:', localCompatibleBobine.length);\n    return localCompatibleBobine;\n  };\n\n  // Funzione di utilità per ottenere le bobine compatibili (usata nel rendering)\n  const getCompatibleBobine = () => {\n    return compatibleBobine;\n  };\n\n  // Renderizza la tabella dei cavi\n  const renderCaviTable = () => {\n    if (caviLoading) {\n      return /*#__PURE__*/_jsxDEV(Box, {\n        sx: {\n          display: 'flex',\n          justifyContent: 'center',\n          my: 4\n        },\n        children: /*#__PURE__*/_jsxDEV(CircularProgress, {}, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 485,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 484,\n        columnNumber: 9\n      }, this);\n    }\n    if (cavi.length === 0) {\n      return /*#__PURE__*/_jsxDEV(Alert, {\n        severity: \"info\",\n        sx: {\n          my: 2\n        },\n        children: \"Nessun cavo disponibile per questo cantiere.\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 492,\n        columnNumber: 9\n      }, this);\n    }\n    return /*#__PURE__*/_jsxDEV(_Fragment, {\n      children: [/*#__PURE__*/_jsxDEV(Alert, {\n        severity: \"info\",\n        sx: {\n          mb: 2\n        },\n        children: \"Seleziona un cavo dalla tabella per inserire i metri posati. I cavi gi\\xE0 installati sono disabilitati.\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 500,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(TableContainer, {\n        component: Paper,\n        sx: {\n          mb: 3\n        },\n        children: /*#__PURE__*/_jsxDEV(Table, {\n          size: \"small\",\n          children: [/*#__PURE__*/_jsxDEV(TableHead, {\n            children: /*#__PURE__*/_jsxDEV(TableRow, {\n              sx: {\n                bgcolor: '#e3f2fd'\n              },\n              children: [/*#__PURE__*/_jsxDEV(TableCell, {\n                children: \"ID Cavo\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 508,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n                children: \"Tipologia\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 509,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n                children: \"Ubicazione\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 510,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n                children: \"Metri Teorici\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 511,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n                children: \"Stato\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 512,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n                children: \"Azioni\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 513,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 507,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 506,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(TableBody, {\n            children: cavi.map(cavo => {\n              const isInstalled = isCableInstalled(cavo);\n              return /*#__PURE__*/_jsxDEV(TableRow, {\n                sx: {\n                  bgcolor: isInstalled ? '#f5f5f5' : 'inherit',\n                  '&:hover': {\n                    bgcolor: isInstalled ? '#f5f5f5' : '#f1f8e9'\n                  }\n                },\n                children: [/*#__PURE__*/_jsxDEV(TableCell, {\n                  children: /*#__PURE__*/_jsxDEV(\"strong\", {\n                    children: cavo.id_cavo\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 527,\n                    columnNumber: 32\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 527,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n                  children: cavo.tipologia || 'N/A'\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 528,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n                  children: [\"Da: \", cavo.ubicazione_partenza || 'N/A', /*#__PURE__*/_jsxDEV(\"br\", {}, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 529,\n                    columnNumber: 71\n                  }, this), \"A: \", cavo.ubicazione_arrivo || 'N/A']\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 529,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n                  children: [cavo.metri_teorici || 'N/A', \" m\"]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 530,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n                  children: /*#__PURE__*/_jsxDEV(Chip, {\n                    label: cavo.stato_installazione || 'N/D',\n                    size: \"small\",\n                    color: getCableStateColor(cavo.stato_installazione),\n                    variant: \"outlined\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 532,\n                    columnNumber: 23\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 531,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n                  children: /*#__PURE__*/_jsxDEV(Button, {\n                    size: \"small\",\n                    variant: \"contained\",\n                    color: \"primary\",\n                    onClick: () => handleCavoSelect(cavo),\n                    disabled: isInstalled,\n                    children: isInstalled ? 'Già installato' : 'Seleziona'\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 540,\n                    columnNumber: 23\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 539,\n                  columnNumber: 21\n                }, this)]\n              }, cavo.id_cavo, true, {\n                fileName: _jsxFileName,\n                lineNumber: 520,\n                columnNumber: 19\n              }, this);\n            })\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 516,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 505,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 504,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true);\n  };\n\n  // Renderizza il form per inserimento metri e selezione bobina\n  const renderForm = () => {\n    if (!selectedCavo) return null;\n\n    // Verifica se ci sono bobine disponibili\n    if (bobine.length === 0 && !bobineLoading) {\n      return /*#__PURE__*/_jsxDEV(Paper, {\n        sx: {\n          p: 3\n        },\n        children: [/*#__PURE__*/_jsxDEV(Typography, {\n          variant: \"h6\",\n          gutterBottom: true,\n          children: \"Inserimento metri posati\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 568,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Alert, {\n          severity: \"warning\",\n          sx: {\n            mb: 3\n          },\n          children: \"Non ci sono bobine disponibili nel cantiere. Puoi comunque registrare i metri posati utilizzando l'opzione \\\"BOBINA VUOTA\\\".\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 572,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Box, {\n          sx: {\n            mb: 3,\n            p: 2,\n            bgcolor: '#f5f5f5',\n            borderRadius: 1\n          },\n          children: [/*#__PURE__*/_jsxDEV(Typography, {\n            variant: \"subtitle1\",\n            gutterBottom: true,\n            fontWeight: \"bold\",\n            color: \"primary\",\n            children: [\"Cavo selezionato: \", selectedCavo.id_cavo]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 577,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Grid, {\n            container: true,\n            spacing: 2,\n            children: [/*#__PURE__*/_jsxDEV(Grid, {\n              item: true,\n              xs: 12,\n              md: 4,\n              children: /*#__PURE__*/_jsxDEV(Typography, {\n                variant: \"body2\",\n                children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n                  children: \"Tipologia:\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 583,\n                  columnNumber: 19\n                }, this), \" \", selectedCavo.tipologia || 'N/A']\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 582,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 581,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Grid, {\n              item: true,\n              xs: 12,\n              md: 4,\n              children: /*#__PURE__*/_jsxDEV(Typography, {\n                variant: \"body2\",\n                children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n                  children: \"Formazione:\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 588,\n                  columnNumber: 19\n                }, this), \" \", selectedCavo.sezione || 'N/A']\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 587,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 586,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Grid, {\n              item: true,\n              xs: 12,\n              md: 4,\n              children: /*#__PURE__*/_jsxDEV(Typography, {\n                variant: \"body2\",\n                children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n                  children: \"Metri teorici:\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 593,\n                  columnNumber: 19\n                }, this), \" \", selectedCavo.metri_teorici || 'N/A', \" m\"]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 592,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 591,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 580,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 576,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Grid, {\n          container: true,\n          spacing: 3,\n          children: [/*#__PURE__*/_jsxDEV(Grid, {\n            item: true,\n            xs: 12,\n            md: 6,\n            children: /*#__PURE__*/_jsxDEV(TextField, {\n              fullWidth: true,\n              label: \"Metri posati\",\n              name: \"metri_posati\",\n              value: formData.metri_posati,\n              onChange: handleInputChange,\n              type: \"number\",\n              InputProps: {\n                inputProps: {\n                  min: 0,\n                  step: 0.1\n                }\n              }\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 601,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 600,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Grid, {\n            item: true,\n            xs: 12,\n            md: 6,\n            children: /*#__PURE__*/_jsxDEV(FormControl, {\n              fullWidth: true,\n              children: [/*#__PURE__*/_jsxDEV(InputLabel, {\n                children: \"Bobina\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 615,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(Select, {\n                name: \"id_bobina\",\n                value: \"BOBINA_VUOTA\",\n                disabled: true,\n                children: /*#__PURE__*/_jsxDEV(MenuItem, {\n                  value: \"BOBINA_VUOTA\",\n                  sx: {\n                    fontWeight: 'bold',\n                    color: '#2e7d32',\n                    bgcolor: '#f1f8e9'\n                  },\n                  children: \"BOBINA VUOTA (Cavo posato senza bobina)\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 621,\n                  columnNumber: 19\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 616,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                variant: \"caption\",\n                color: \"text.secondary\",\n                sx: {\n                  mt: 1\n                },\n                children: \"Non ci sono bobine disponibili. Verr\\xE0 utilizzata l'opzione BOBINA VUOTA.\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 625,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 614,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 613,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 599,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Box, {\n          sx: {\n            mt: 3,\n            display: 'flex',\n            justifyContent: 'space-between'\n          },\n          children: [/*#__PURE__*/_jsxDEV(Button, {\n            variant: \"outlined\",\n            color: \"secondary\",\n            onClick: () => {\n              setSelectedCavo(null);\n              setFormData({\n                id_cavo: '',\n                metri_posati: '',\n                id_bobina: ''\n              });\n            },\n            disabled: saving,\n            children: \"Annulla\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 633,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Button, {\n            variant: \"contained\",\n            color: \"primary\",\n            onClick: () => handleSave(),\n            disabled: saving || !formData.metri_posati,\n            children: saving ? /*#__PURE__*/_jsxDEV(CircularProgress, {\n              size: 24\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 654,\n              columnNumber: 25\n            }, this) : 'Salva'\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 648,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 632,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 567,\n        columnNumber: 9\n      }, this);\n    }\n    const compatibleBobine = getCompatibleBobine();\n    return /*#__PURE__*/_jsxDEV(Paper, {\n      sx: {\n        p: 3\n      },\n      children: [/*#__PURE__*/_jsxDEV(Typography, {\n        variant: \"h6\",\n        gutterBottom: true,\n        children: \"Inserimento metri posati\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 665,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Alert, {\n        severity: \"info\",\n        sx: {\n          mb: 3\n        },\n        children: [\"Inserisci i metri posati per il cavo selezionato e associa una bobina. Se il cavo \\xE8 stato posato senza una bobina specifica, seleziona \", /*#__PURE__*/_jsxDEV(\"strong\", {\n          children: \"BOBINA VUOTA\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 670,\n          columnNumber: 146\n        }, this), \".\"]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 669,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Box, {\n        sx: {\n          mb: 3,\n          p: 2,\n          bgcolor: '#f5f5f5',\n          borderRadius: 1\n        },\n        children: [/*#__PURE__*/_jsxDEV(Typography, {\n          variant: \"subtitle1\",\n          gutterBottom: true,\n          fontWeight: \"bold\",\n          color: \"primary\",\n          children: [\"Cavo selezionato: \", selectedCavo.id_cavo]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 674,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Grid, {\n          container: true,\n          spacing: 2,\n          children: [/*#__PURE__*/_jsxDEV(Grid, {\n            item: true,\n            xs: 12,\n            md: 4,\n            children: /*#__PURE__*/_jsxDEV(Typography, {\n              variant: \"body2\",\n              children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n                children: \"Tipologia:\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 680,\n                columnNumber: 17\n              }, this), \" \", selectedCavo.tipologia || 'N/A']\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 679,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 678,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Grid, {\n            item: true,\n            xs: 12,\n            md: 4,\n            children: /*#__PURE__*/_jsxDEV(Typography, {\n              variant: \"body2\",\n              children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n                children: \"Formazione:\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 685,\n                columnNumber: 17\n              }, this), \" \", selectedCavo.sezione || 'N/A']\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 684,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 683,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Grid, {\n            item: true,\n            xs: 12,\n            md: 4,\n            children: /*#__PURE__*/_jsxDEV(Typography, {\n              variant: \"body2\",\n              children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n                children: \"Metri teorici:\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 690,\n                columnNumber: 17\n              }, this), \" \", selectedCavo.metri_teorici || 'N/A', \" m\"]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 689,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 688,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Grid, {\n            item: true,\n            xs: 12,\n            md: 6,\n            children: /*#__PURE__*/_jsxDEV(Typography, {\n              variant: \"body2\",\n              children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n                children: \"Ubicazione partenza:\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 695,\n                columnNumber: 17\n              }, this), \" \", selectedCavo.ubicazione_partenza || 'N/A']\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 694,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 693,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Grid, {\n            item: true,\n            xs: 12,\n            md: 6,\n            children: /*#__PURE__*/_jsxDEV(Typography, {\n              variant: \"body2\",\n              children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n                children: \"Ubicazione arrivo:\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 700,\n                columnNumber: 17\n              }, this), \" \", selectedCavo.ubicazione_arrivo || 'N/A']\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 699,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 698,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Grid, {\n            item: true,\n            xs: 12,\n            children: /*#__PURE__*/_jsxDEV(Typography, {\n              variant: \"body2\",\n              children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n                children: \"Stato:\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 705,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(Chip, {\n                label: selectedCavo.stato_installazione || 'N/D',\n                size: \"small\",\n                color: getCableStateColor(selectedCavo.stato_installazione),\n                variant: \"outlined\",\n                sx: {\n                  ml: 1\n                }\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 706,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 704,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 703,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 677,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 673,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Divider, {\n        sx: {\n          mb: 3\n        }\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 718,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Grid, {\n        container: true,\n        spacing: 3,\n        children: [/*#__PURE__*/_jsxDEV(Grid, {\n          item: true,\n          xs: 12,\n          md: 6,\n          children: /*#__PURE__*/_jsxDEV(TextField, {\n            fullWidth: true,\n            label: \"Metri posati\",\n            name: \"metri_posati\",\n            value: formData.metri_posati,\n            onChange: handleInputChange,\n            type: \"number\",\n            error: !!formErrors.metri_posati,\n            helperText: formErrors.metri_posati || formWarnings.metri_posati && /*#__PURE__*/_jsxDEV(\"span\", {\n              style: {\n                color: 'orange'\n              },\n              children: formWarnings.metri_posati\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 731,\n              columnNumber: 17\n            }, this),\n            disabled: saving,\n            InputProps: {\n              inputProps: {\n                min: 0,\n                step: 0.1\n              }\n            }\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 722,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 721,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Grid, {\n          item: true,\n          xs: 12,\n          md: 6,\n          children: /*#__PURE__*/_jsxDEV(FormControl, {\n            fullWidth: true,\n            error: !!formErrors.id_bobina,\n            children: [/*#__PURE__*/_jsxDEV(InputLabel, {\n              children: \"Bobina\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 741,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Select, {\n              name: \"id_bobina\",\n              value: formData.id_bobina,\n              onChange: handleInputChange,\n              disabled: saving || bobineLoading || compatibleBobineLoading,\n              children: [/*#__PURE__*/_jsxDEV(MenuItem, {\n                value: \"BOBINA_VUOTA\",\n                sx: {\n                  fontWeight: 'bold',\n                  color: '#2e7d32',\n                  bgcolor: '#f1f8e9'\n                },\n                children: \"BOBINA VUOTA (Cavo posato senza bobina)\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 749,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(Divider, {}, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 754,\n                columnNumber: 17\n              }, this), compatibleBobineLoading ? /*#__PURE__*/_jsxDEV(MenuItem, {\n                disabled: true,\n                children: /*#__PURE__*/_jsxDEV(Box, {\n                  sx: {\n                    display: 'flex',\n                    alignItems: 'center'\n                  },\n                  children: [/*#__PURE__*/_jsxDEV(CircularProgress, {\n                    size: 20,\n                    sx: {\n                      mr: 1\n                    }\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 760,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                    variant: \"caption\",\n                    children: \"Caricamento bobine compatibili...\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 761,\n                    columnNumber: 23\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 759,\n                  columnNumber: 21\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 758,\n                columnNumber: 19\n              }, this) : compatibleBobine.length === 0 ? /*#__PURE__*/_jsxDEV(MenuItem, {\n                disabled: true,\n                children: /*#__PURE__*/_jsxDEV(Typography, {\n                  variant: \"caption\",\n                  color: \"text.secondary\",\n                  children: \"Nessuna bobina compatibile disponibile. Utilizzare BOBINA VUOTA.\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 768,\n                  columnNumber: 21\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 767,\n                columnNumber: 19\n              }, this) : /*#__PURE__*/_jsxDEV(MenuItem, {\n                disabled: true,\n                children: /*#__PURE__*/_jsxDEV(Typography, {\n                  variant: \"caption\",\n                  children: [\"Bobine compatibili (\", compatibleBobine.length, \")\"]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 774,\n                  columnNumber: 21\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 773,\n                columnNumber: 19\n              }, this), !compatibleBobineLoading && compatibleBobine.map(bobina => /*#__PURE__*/_jsxDEV(MenuItem, {\n                value: bobina.id_bobina,\n                children: [bobina.id_bobina, \" - \", bobina.tipologia, \" - \", bobina.metri_residui, \"m\"]\n              }, bobina.id_bobina, true, {\n                fileName: _jsxFileName,\n                lineNumber: 782,\n                columnNumber: 19\n              }, this)), (() => {\n                const nonCompatibleBobine = bobine.filter(bobina => {\n                  // Verifica che non sia già nelle bobine compatibili\n                  const isInCompatible = compatibleBobine.some(b => b.id_bobina === bobina.id_bobina);\n                  // Verifica che non sia terminata\n                  const bobinaState = determineReelState(bobina.metri_residui, bobina.metri_totali);\n                  return !isInCompatible && bobinaState !== REEL_STATES.TERMINATA;\n                });\n                if (nonCompatibleBobine.length === 0) {\n                  return null;\n                }\n                return /*#__PURE__*/_jsxDEV(React.Fragment, {\n                  children: [/*#__PURE__*/_jsxDEV(Divider, {}, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 804,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(MenuItem, {\n                    disabled: true,\n                    children: /*#__PURE__*/_jsxDEV(Typography, {\n                      variant: \"caption\",\n                      children: [\"Bobine non compatibili (\", nonCompatibleBobine.length, \")\"]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 806,\n                      columnNumber: 25\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 805,\n                    columnNumber: 23\n                  }, this), nonCompatibleBobine.map(bobina => /*#__PURE__*/_jsxDEV(MenuItem, {\n                    value: bobina.id_bobina,\n                    children: [bobina.id_bobina, \" - \", bobina.tipologia, \" - \", bobina.metri_residui, \"m\"]\n                  }, bobina.id_bobina, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 811,\n                    columnNumber: 25\n                  }, this))]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 803,\n                  columnNumber: 21\n                }, this);\n              })()]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 742,\n              columnNumber: 15\n            }, this), formErrors.id_bobina && /*#__PURE__*/_jsxDEV(Typography, {\n              variant: \"caption\",\n              color: \"error\",\n              children: formErrors.id_bobina\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 820,\n              columnNumber: 17\n            }, this), formWarnings.id_bobina && /*#__PURE__*/_jsxDEV(Typography, {\n              variant: \"caption\",\n              sx: {\n                color: 'orange'\n              },\n              children: formWarnings.id_bobina\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 825,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(Typography, {\n              variant: \"caption\",\n              color: \"text.secondary\",\n              sx: {\n                mt: 1\n              },\n              children: \"Seleziona una bobina o usa BOBINA VUOTA se il cavo \\xE8 stato posato senza una bobina specifica.\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 830,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 740,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 739,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 720,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Box, {\n        sx: {\n          mt: 3,\n          display: 'flex',\n          justifyContent: 'space-between'\n        },\n        children: [/*#__PURE__*/_jsxDEV(Button, {\n          variant: \"outlined\",\n          color: \"secondary\",\n          onClick: () => {\n            setSelectedCavo(null);\n            setFormData({\n              id_cavo: '',\n              metri_posati: '',\n              id_bobina: ''\n            });\n          },\n          disabled: saving,\n          children: \"Annulla\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 838,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Button, {\n          variant: \"contained\",\n          color: \"primary\",\n          onClick: handleSave,\n          disabled: saving || Object.keys(formErrors).length > 0,\n          children: saving ? /*#__PURE__*/_jsxDEV(CircularProgress, {\n            size: 24\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 859,\n            columnNumber: 23\n          }, this) : 'Salva'\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 853,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 837,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 664,\n      columnNumber: 7\n    }, this);\n  };\n  return /*#__PURE__*/_jsxDEV(Box, {\n    children: [!selectedCavo && renderCaviTable(), renderForm(), /*#__PURE__*/_jsxDEV(IncompatibleReelDialog, {\n      open: showIncompatibleReelDialog,\n      onClose: () => setShowIncompatibleReelDialog(false),\n      cavo: incompatibleReelData.cavo,\n      bobina: incompatibleReelData.bobina,\n      onUpdateCavo: handleUpdateCavoForCompatibility,\n      onSelectAnotherReel: () => {\n        setShowIncompatibleReelDialog(false);\n        setFormData(prev => ({\n          ...prev,\n          id_bobina: ''\n        }));\n      }\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 875,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Dialog, {\n      open: showAlreadyLaidDialog,\n      onClose: handleCloseAlreadyLaidDialog,\n      children: [/*#__PURE__*/_jsxDEV(DialogTitle, {\n        children: \"Cavo gi\\xE0 posato\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 889,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(DialogContent, {\n        children: [/*#__PURE__*/_jsxDEV(DialogContentText, {\n          children: [\"Il cavo \", alreadyLaidCavo === null || alreadyLaidCavo === void 0 ? void 0 : alreadyLaidCavo.id_cavo, \" \\xE8 gi\\xE0 stato posato.\"]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 891,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Box, {\n          sx: {\n            mt: 2\n          },\n          children: [/*#__PURE__*/_jsxDEV(Typography, {\n            variant: \"body2\",\n            gutterBottom: true,\n            children: \"Puoi:\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 895,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Typography, {\n            component: \"ul\",\n            variant: \"body2\",\n            children: [/*#__PURE__*/_jsxDEV(\"li\", {\n              children: \"Modificare la bobina associata\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 899,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"li\", {\n              children: \"Selezionare un altro cavo\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 900,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"li\", {\n              children: \"Annullare l'operazione\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 901,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 898,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 894,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 890,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(DialogActions, {\n        sx: {\n          p: 2,\n          justifyContent: 'space-between'\n        },\n        children: [/*#__PURE__*/_jsxDEV(Button, {\n          onClick: handleCloseAlreadyLaidDialog,\n          color: \"secondary\",\n          children: \"Annulla operazione\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 906,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Box, {\n          children: [/*#__PURE__*/_jsxDEV(Button, {\n            onClick: handleSelectAnotherCable,\n            color: \"primary\",\n            sx: {\n              mr: 1\n            },\n            children: \"Seleziona altro cavo\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 910,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Button, {\n            onClick: handleModifyReel,\n            variant: \"contained\",\n            color: \"primary\",\n            children: \"Modifica bobina\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 913,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 909,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 905,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 888,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 867,\n    columnNumber: 5\n  }, this);\n};\n_s(MetriPosatiSemplificatoForm, \"W2VBgEfWoWLiiu+w6e9IsRtmzP8=\", false, function () {\n  return [useNavigate];\n});\n_c = MetriPosatiSemplificatoForm;\nexport default MetriPosatiSemplificatoForm;\nvar _c;\n$RefreshReg$(_c, \"MetriPosatiSemplificatoForm\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "Box", "Typography", "TextField", "<PERSON><PERSON>", "Paper", "Table", "TableBody", "TableCell", "TableContainer", "TableHead", "TableRow", "CircularProgress", "<PERSON><PERSON>", "Chip", "Divider", "Grid", "FormControl", "InputLabel", "Select", "MenuItem", "Dialog", "DialogTitle", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "DialogActions", "DialogContentText", "useNavigate", "caviService", "parcoCaviService", "CABLE_STATES", "REEL_STATES", "determineCableState", "determineReelState", "canModifyCable", "isCableSpare", "isCableInstalled", "getCableStateColor", "getReelStateColor", "redirectToVisualizzaCavi", "IncompatibleReelDialog", "jsxDEV", "_jsxDEV", "Fragment", "_Fragment", "MetriPosatiSemplificatoForm", "cantiereId", "onSuccess", "onError", "_s", "navigate", "cavi", "<PERSON><PERSON><PERSON>", "bobine", "set<PERSON>ob<PERSON>", "selected<PERSON><PERSON><PERSON>", "setSelectedCavo", "formData", "setFormData", "id_cavo", "metri_posati", "id_bobina", "loading", "setLoading", "caviLoading", "setCaviLoading", "bob<PERSON><PERSON><PERSON><PERSON>", "setBobineLoading", "saving", "setSaving", "formErrors", "setFormErrors", "formWarnings", "setForm<PERSON><PERSON>nings", "showIncompatibleReelDialog", "setShowIncompatibleReelDialog", "incompatibleReelData", "setIncompatibleReelData", "cavo", "bobina", "showAlreadyLaidDialog", "setShowAlreadyLaidDialog", "alreadyLaidCavo", "setAlreadyLaidCavo", "loadCavi", "loadBobine", "caviData", "get<PERSON><PERSON>", "caviAttivi", "filter", "error", "console", "message", "log", "bobine<PERSON><PERSON>", "getBobine", "handleCavoSelect", "handleInputChange", "e", "name", "value", "target", "prev", "validateField", "newErrors", "newWarnings", "isNaN", "parseFloat", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "metri_te<PERSON>ci", "<PERSON><PERSON><PERSON><PERSON>", "find", "b", "metri_residui", "validateForm", "Object", "keys", "length", "checkCompatibility", "tipologiaCompatibile", "tipologia", "sezioneCompatibile", "String", "sezione", "handleSave", "updateMetri<PERSON><PERSON><PERSON>", "handleUpdateCavoForCompatibility", "updateCavoForCompatibility", "handleCloseAlreadyLaidDialog", "handleSelectAnotherCable", "handleModifyReel", "compatibleBobine", "setCompatibleBobine", "compatibleBobineLoading", "setCompatibleBobineLoading", "loadCompatibleBobine", "compatibleBobineData", "getBobineCompatibili", "localCompatibleBobine", "getLocalCompatibleBobine", "cavoTipologia", "trim", "cavoSezione", "bobinaTipologia", "bobinaSezione", "bobinaState", "metri_totali", "stato", "isCompatibile", "TERMINATA", "getCompatibleBobine", "renderCaviTable", "sx", "display", "justifyContent", "my", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "severity", "mb", "component", "size", "bgcolor", "map", "isInstalled", "ubicazione_partenza", "ubicazione_arrivo", "label", "stato_installazione", "color", "variant", "onClick", "disabled", "renderForm", "p", "gutterBottom", "borderRadius", "fontWeight", "container", "spacing", "item", "xs", "md", "fullWidth", "onChange", "type", "InputProps", "inputProps", "min", "step", "mt", "ml", "helperText", "style", "alignItems", "mr", "nonCompatibleBobine", "isInCompatible", "some", "open", "onClose", "onUpdateCavo", "onSelectAnotherReel", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/PycharmProjects/CMS/webapp/frontend/src/components/cavi/MetriPosatiSemplificatoForm.js"], "sourcesContent": ["import React, { useState, useEffect } from 'react';\nimport {\n  Box,\n  Typography,\n  TextField,\n  Button,\n  Paper,\n  Table,\n  TableBody,\n  TableCell,\n  TableContainer,\n  TableHead,\n  TableRow,\n  CircularProgress,\n  Alert,\n  Chip,\n  Divider,\n  Grid,\n  FormControl,\n  InputLabel,\n  Select,\n  MenuItem,\n  Dialog,\n  DialogTitle,\n  DialogContent,\n  DialogActions,\n  DialogContentText\n} from '@mui/material';\nimport { useNavigate } from 'react-router-dom';\nimport caviService from '../../services/caviService';\nimport parcoCaviService from '../../services/parcoCaviService';\nimport {\n  CABLE_STATES,\n  REEL_STATES,\n  determineCableState,\n  determineReelState,\n  canModifyCable,\n  isCableSpare,\n  isCableInstalled,\n  getCableStateColor,\n  getReelStateColor\n} from '../../utils/stateUtils';\nimport { redirectToVisualizzaCavi } from '../../utils/navigationUtils';\nimport IncompatibleReelDialog from './IncompatibleReelDialog';\n\n/**\n * Componente per l'inserimento dei metri posati di un cavo\n * Versione ultra-semplificata con workflow compresso in un'unica pagina\n *\n * @param {Object} props - Proprietà del componente\n * @param {string} props.cantiereId - ID del cantiere\n * @param {Function} props.onSuccess - Funzione chiamata al successo dell'operazione\n * @param {Function} props.onError - Funzione chiamata in caso di errore\n */\nconst MetriPosatiSemplificatoForm = ({ cantiereId, onSuccess, onError }) => {\n  const navigate = useNavigate();\n\n  // Stati per i dati\n  const [cavi, setCavi] = useState([]);\n  const [bobine, setBobine] = useState([]);\n  const [selectedCavo, setSelectedCavo] = useState(null);\n  const [formData, setFormData] = useState({\n    id_cavo: '',\n    metri_posati: '',\n    id_bobina: ''\n  });\n\n  // Stati per il caricamento\n  const [loading, setLoading] = useState(false);\n  const [caviLoading, setCaviLoading] = useState(false);\n  const [bobineLoading, setBobineLoading] = useState(false);\n  const [saving, setSaving] = useState(false);\n\n  // Stati per la validazione\n  const [formErrors, setFormErrors] = useState({});\n  const [formWarnings, setFormWarnings] = useState({});\n\n  // Stati per i dialoghi speciali\n  const [showIncompatibleReelDialog, setShowIncompatibleReelDialog] = useState(false);\n  const [incompatibleReelData, setIncompatibleReelData] = useState({ cavo: null, bobina: null });\n  const [showAlreadyLaidDialog, setShowAlreadyLaidDialog] = useState(false);\n  const [alreadyLaidCavo, setAlreadyLaidCavo] = useState(null);\n\n  // Carica la lista dei cavi e delle bobine all'avvio\n  useEffect(() => {\n    loadCavi();\n    loadBobine();\n  }, [cantiereId]);\n\n  // Carica la lista dei cavi\n  const loadCavi = async () => {\n    try {\n      setCaviLoading(true);\n      const caviData = await caviService.getCavi(cantiereId);\n\n      // Filtra i cavi che non sono SPARE\n      const caviAttivi = caviData.filter(cavo => !isCableSpare(cavo));\n\n      setCavi(caviAttivi);\n    } catch (error) {\n      console.error('Errore nel caricamento dei cavi:', error);\n      onError('Errore nel caricamento dei cavi: ' + (error.message || 'Errore sconosciuto'));\n    } finally {\n      setCaviLoading(false);\n    }\n  };\n\n  // Carica la lista delle bobine\n  const loadBobine = async () => {\n    try {\n      setBobineLoading(true);\n      console.log('Caricamento bobine per cantiere:', cantiereId);\n      const bobineData = await parcoCaviService.getBobine(cantiereId);\n      console.log('Bobine caricate:', bobineData);\n      setBobine(bobineData);\n    } catch (error) {\n      console.error('Errore nel caricamento delle bobine:', error);\n      onError('Errore nel caricamento delle bobine: ' + (error.message || 'Errore sconosciuto'));\n    } finally {\n      setBobineLoading(false);\n    }\n  };\n\n  // Gestisce la selezione di un cavo\n  const handleCavoSelect = (cavo) => {\n    // Verifica se il cavo è già posato\n    if (isCableInstalled(cavo)) {\n      setAlreadyLaidCavo(cavo);\n      setShowAlreadyLaidDialog(true);\n      return;\n    }\n\n    setSelectedCavo(cavo);\n    setFormData({\n      id_cavo: cavo.id_cavo,\n      metri_posati: '',\n      id_bobina: ''\n    });\n    setFormErrors({});\n    setFormWarnings({});\n  };\n\n  // Gestisce la modifica dei campi del form\n  const handleInputChange = (e) => {\n    const { name, value } = e.target;\n    setFormData(prev => ({ ...prev, [name]: value }));\n\n    // Validazione in tempo reale\n    validateField(name, value);\n  };\n\n  // Validazione di un singolo campo\n  const validateField = (name, value) => {\n    const newErrors = { ...formErrors };\n    const newWarnings = { ...formWarnings };\n\n    if (name === 'metri_posati') {\n      // Validazione metri posati\n      if (value === '') {\n        newErrors.metri_posati = 'I metri posati sono obbligatori';\n      } else if (isNaN(value) || parseFloat(value) < 0) {\n        newErrors.metri_posati = 'I metri posati devono essere un numero positivo';\n      } else {\n        delete newErrors.metri_posati;\n\n        // Avvisi sui metri posati\n        const metriPosati = parseFloat(value);\n        if (selectedCavo && metriPosati > selectedCavo.metri_teorici) {\n          newWarnings.metri_posati = `I metri posati (${metriPosati}) superano i metri teorici (${selectedCavo.metri_teorici})`;\n        } else {\n          delete newWarnings.metri_posati;\n        }\n\n        // Avvisi sulla bobina selezionata\n        if (formData.id_bobina && formData.id_bobina !== 'BOBINA_VUOTA') {\n          const selectedBobina = bobine.find(b => b.id_bobina === formData.id_bobina);\n          if (selectedBobina && metriPosati > selectedBobina.metri_residui) {\n            newWarnings.id_bobina = `I metri posati (${metriPosati}) superano i metri residui della bobina (${selectedBobina.metri_residui})`;\n          } else {\n            delete newWarnings.id_bobina;\n          }\n        }\n      }\n    }\n\n    if (name === 'id_bobina') {\n      // Validazione bobina\n      if (value === '') {\n        newErrors.id_bobina = 'La bobina è obbligatoria';\n      } else {\n        delete newErrors.id_bobina;\n\n        // Avvisi sulla bobina selezionata\n        if (value !== 'BOBINA_VUOTA' && formData.metri_posati) {\n          const metriPosati = parseFloat(formData.metri_posati);\n          const selectedBobina = bobine.find(b => b.id_bobina === value);\n          if (selectedBobina && metriPosati > selectedBobina.metri_residui) {\n            newWarnings.id_bobina = `I metri posati (${metriPosati}) superano i metri residui della bobina (${selectedBobina.metri_residui})`;\n          } else {\n            delete newWarnings.id_bobina;\n          }\n        }\n      }\n    }\n\n    setFormErrors(newErrors);\n    setFormWarnings(newWarnings);\n  };\n\n  // Validazione completa del form\n  const validateForm = () => {\n    const newErrors = {};\n\n    // Validazione metri posati\n    if (!formData.metri_posati) {\n      newErrors.metri_posati = 'I metri posati sono obbligatori';\n    } else if (isNaN(formData.metri_posati) || parseFloat(formData.metri_posati) < 0) {\n      newErrors.metri_posati = 'I metri posati devono essere un numero positivo';\n    }\n\n    // Validazione bobina\n    if (!formData.id_bobina) {\n      newErrors.id_bobina = 'La bobina è obbligatoria';\n    }\n\n    setFormErrors(newErrors);\n    return Object.keys(newErrors).length === 0;\n  };\n\n  // Verifica la compatibilità tra cavo e bobina\n  const checkCompatibility = () => {\n    if (formData.id_bobina === 'BOBINA_VUOTA') {\n      return true; // BOBINA_VUOTA è sempre compatibile\n    }\n\n    const selectedBobina = bobine.find(b => b.id_bobina === formData.id_bobina);\n    if (!selectedBobina) {\n      return false;\n    }\n\n    // Verifica compatibilità tipologia\n    const tipologiaCompatibile = selectedCavo.tipologia === selectedBobina.tipologia;\n\n    // Verifica compatibilità sezione\n    const sezioneCompatibile = String(selectedCavo.sezione) === String(selectedBobina.sezione);\n\n    return tipologiaCompatibile && sezioneCompatibile;\n  };\n\n  // Gestisce il salvataggio dei dati\n  const handleSave = async () => {\n    // Se non ci sono bobine disponibili, imposta automaticamente BOBINA_VUOTA\n    if (bobine.length === 0 && !bobineLoading) {\n      if (!formData.metri_posati || isNaN(parseFloat(formData.metri_posati)) || parseFloat(formData.metri_posati) <= 0) {\n        setFormErrors({\n          ...formErrors,\n          metri_posati: 'I metri posati sono obbligatori e devono essere maggiori di zero'\n        });\n        return;\n      }\n\n      // Imposta BOBINA_VUOTA e procedi con il salvataggio\n      formData.id_bobina = 'BOBINA_VUOTA';\n    } else {\n      // Validazione completa\n      if (!validateForm()) {\n        return;\n      }\n\n      // Verifica compatibilità\n      if (!checkCompatibility()) {\n        // Mostra dialog per incompatibilità\n        const selectedBobina = bobine.find(b => b.id_bobina === formData.id_bobina);\n        setIncompatibleReelData({\n          cavo: selectedCavo,\n          bobina: selectedBobina\n        });\n        setShowIncompatibleReelDialog(true);\n        return;\n      }\n    }\n\n    // Procedi con il salvataggio\n    try {\n      setSaving(true);\n\n      // Converti metri posati in numero\n      const metriPosati = parseFloat(formData.metri_posati);\n\n      // Chiamata API\n      console.log('Invio richiesta updateMetriPosati con i seguenti parametri:');\n      console.log('- cantiereId:', cantiereId);\n      console.log('- id_cavo:', formData.id_cavo);\n      console.log('- metri_posati:', metriPosati);\n      console.log('- id_bobina:', formData.id_bobina);\n\n      // Imposta sempre forceOver a true per evitare blocchi quando la bobina va in OVER\n      await caviService.updateMetriPosati(\n        cantiereId,\n        formData.id_cavo,\n        metriPosati,\n        formData.id_bobina,\n        true // Forza sempre a true per evitare blocchi\n      );\n\n      // Mostra messaggio di successo\n      onSuccess('Metri posati aggiornati con successo');\n\n      // Resetta il form\n      setSelectedCavo(null);\n      setFormData({\n        id_cavo: '',\n        metri_posati: '',\n        id_bobina: ''\n      });\n\n      // Ricarica i dati\n      loadCavi();\n      loadBobine();\n    } catch (error) {\n      console.error('Errore durante il salvataggio:', error);\n      onError('Errore durante il salvataggio: ' + (error.message || 'Errore sconosciuto'));\n    } finally {\n      setSaving(false);\n    }\n  };\n\n  // Gestisce l'aggiornamento del cavo per compatibilità\n  const handleUpdateCavoForCompatibility = async () => {\n    try {\n      setSaving(true);\n      setShowIncompatibleReelDialog(false);\n\n      const { cavo, bobina } = incompatibleReelData;\n\n      // Aggiorna il cavo per renderlo compatibile con la bobina\n      await caviService.updateCavoForCompatibility(\n        cantiereId,\n        cavo.id_cavo,\n        {\n          id_bobina: bobina.id_bobina,\n          tipologia: bobina.tipologia,\n          sezione: bobina.sezione\n        }\n      );\n\n      // Procedi con l'aggiornamento dei metri posati\n      await caviService.updateMetriPosati(\n        cantiereId,\n        formData.id_cavo,\n        parseFloat(formData.metri_posati),\n        formData.id_bobina,\n        true // Forza sempre a true per evitare blocchi\n      );\n\n      // Mostra messaggio di successo\n      onSuccess('Cavo aggiornato e metri posati registrati con successo');\n\n      // Resetta il form\n      setSelectedCavo(null);\n      setFormData({\n        id_cavo: '',\n        metri_posati: '',\n        id_bobina: ''\n      });\n\n      // Ricarica i dati\n      loadCavi();\n      loadBobine();\n    } catch (error) {\n      console.error('Errore durante l\\'aggiornamento del cavo:', error);\n      onError('Errore durante l\\'aggiornamento del cavo: ' + (error.message || 'Errore sconosciuto'));\n    } finally {\n      setSaving(false);\n    }\n  };\n\n  // Gestisce la chiusura del dialogo per cavi già posati\n  const handleCloseAlreadyLaidDialog = () => {\n    setShowAlreadyLaidDialog(false);\n    setAlreadyLaidCavo(null);\n  };\n\n  // Gestisce la selezione di un altro cavo\n  const handleSelectAnotherCable = () => {\n    handleCloseAlreadyLaidDialog();\n  };\n\n  // Gestisce l'opzione di modificare la bobina di un cavo già posato\n  const handleModifyReel = () => {\n    if (alreadyLaidCavo) {\n      navigate(`/dashboard/cavi/posa/modifica-bobina?cavoId=${alreadyLaidCavo.id_cavo}`);\n    }\n    handleCloseAlreadyLaidDialog();\n  };\n\n  // Stato per le bobine compatibili\n  const [compatibleBobine, setCompatibleBobine] = useState([]);\n  const [compatibleBobineLoading, setCompatibleBobineLoading] = useState(false);\n\n  // Carica le bobine compatibili dal backend quando viene selezionato un cavo\n  useEffect(() => {\n    if (selectedCavo) {\n      loadCompatibleBobine(selectedCavo);\n    } else {\n      setCompatibleBobine([]);\n    }\n  }, [selectedCavo]);\n\n  // Carica le bobine compatibili dal backend\n  const loadCompatibleBobine = async (cavo) => {\n    try {\n      setCompatibleBobineLoading(true);\n      console.log('Caricamento bobine compatibili per cavo:', cavo);\n\n      // Chiamata diretta all'API del backend per ottenere le bobine compatibili\n      const compatibleBobineData = await parcoCaviService.getBobineCompatibili(\n        cantiereId,\n        cavo.tipologia,\n        null, // n_conduttori non è più utilizzato per la compatibilità\n        cavo.sezione\n      );\n\n      console.log('Bobine compatibili caricate dal backend:', compatibleBobineData);\n      setCompatibleBobine(compatibleBobineData);\n    } catch (error) {\n      console.error('Errore nel caricamento delle bobine compatibili:', error);\n      onError('Errore nel caricamento delle bobine compatibili: ' + (error.message || 'Errore sconosciuto'));\n\n      // In caso di errore, fallback al filtraggio locale\n      console.log('Fallback al filtraggio locale delle bobine compatibili');\n      const localCompatibleBobine = getLocalCompatibleBobine(cavo);\n      setCompatibleBobine(localCompatibleBobine);\n    } finally {\n      setCompatibleBobineLoading(false);\n    }\n  };\n\n  // Filtra le bobine compatibili localmente (usato come fallback)\n  const getLocalCompatibleBobine = (cavo) => {\n    if (!cavo) return [];\n\n    console.log('Filtrando bobine compatibili localmente per cavo:', cavo);\n    console.log('Bobine disponibili:', bobine);\n\n    // Converti i valori in stringhe per un confronto più robusto\n    const cavoTipologia = String(cavo.tipologia || '').trim();\n    const cavoSezione = String(cavo.sezione || '').trim();\n\n    const localCompatibleBobine = bobine.filter(bobina => {\n      // Converti i valori della bobina in stringhe\n      const bobinaTipologia = String(bobina.tipologia || '').trim();\n      const bobinaSezione = String(bobina.sezione || '').trim();\n\n      // Determina lo stato della bobina\n      const bobinaState = determineReelState(bobina.metri_residui, bobina.metri_totali);\n\n      // Log per debug\n      console.log(`Verifica bobina ${bobina.id_bobina}:`, {\n        tipologia: `'${bobinaTipologia}' === '${cavoTipologia}'`,\n        sezione: `'${bobinaSezione}' === '${cavoSezione}'`,\n        stato: bobinaState,\n        isCompatibile: bobinaTipologia === cavoTipologia && bobinaSezione === cavoSezione && bobinaState !== REEL_STATES.TERMINATA\n      });\n\n      return bobinaTipologia === cavoTipologia &&\n             bobinaSezione === cavoSezione &&\n             bobinaState !== REEL_STATES.TERMINATA;\n    });\n\n    console.log('Bobine compatibili trovate localmente:', localCompatibleBobine.length);\n    return localCompatibleBobine;\n  };\n\n  // Funzione di utilità per ottenere le bobine compatibili (usata nel rendering)\n  const getCompatibleBobine = () => {\n    return compatibleBobine;\n  };\n\n  // Renderizza la tabella dei cavi\n  const renderCaviTable = () => {\n    if (caviLoading) {\n      return (\n        <Box sx={{ display: 'flex', justifyContent: 'center', my: 4 }}>\n          <CircularProgress />\n        </Box>\n      );\n    }\n\n    if (cavi.length === 0) {\n      return (\n        <Alert severity=\"info\" sx={{ my: 2 }}>\n          Nessun cavo disponibile per questo cantiere.\n        </Alert>\n      );\n    }\n\n    return (\n      <>\n        <Alert severity=\"info\" sx={{ mb: 2 }}>\n          Seleziona un cavo dalla tabella per inserire i metri posati. I cavi già installati sono disabilitati.\n        </Alert>\n\n        <TableContainer component={Paper} sx={{ mb: 3 }}>\n          <Table size=\"small\">\n            <TableHead>\n              <TableRow sx={{ bgcolor: '#e3f2fd' }}>\n                <TableCell>ID Cavo</TableCell>\n                <TableCell>Tipologia</TableCell>\n                <TableCell>Ubicazione</TableCell>\n                <TableCell>Metri Teorici</TableCell>\n                <TableCell>Stato</TableCell>\n                <TableCell>Azioni</TableCell>\n              </TableRow>\n            </TableHead>\n            <TableBody>\n              {cavi.map((cavo) => {\n                const isInstalled = isCableInstalled(cavo);\n                return (\n                  <TableRow\n                    key={cavo.id_cavo}\n                    sx={{\n                      bgcolor: isInstalled ? '#f5f5f5' : 'inherit',\n                      '&:hover': { bgcolor: isInstalled ? '#f5f5f5' : '#f1f8e9' }\n                    }}\n                  >\n                    <TableCell><strong>{cavo.id_cavo}</strong></TableCell>\n                    <TableCell>{cavo.tipologia || 'N/A'}</TableCell>\n                    <TableCell>Da: {cavo.ubicazione_partenza || 'N/A'}<br/>A: {cavo.ubicazione_arrivo || 'N/A'}</TableCell>\n                    <TableCell>{cavo.metri_teorici || 'N/A'} m</TableCell>\n                    <TableCell>\n                      <Chip\n                        label={cavo.stato_installazione || 'N/D'}\n                        size=\"small\"\n                        color={getCableStateColor(cavo.stato_installazione)}\n                        variant=\"outlined\"\n                      />\n                    </TableCell>\n                    <TableCell>\n                      <Button\n                        size=\"small\"\n                        variant=\"contained\"\n                        color=\"primary\"\n                        onClick={() => handleCavoSelect(cavo)}\n                        disabled={isInstalled}\n                      >\n                        {isInstalled ? 'Già installato' : 'Seleziona'}\n                      </Button>\n                    </TableCell>\n                  </TableRow>\n                );\n              })}\n            </TableBody>\n          </Table>\n        </TableContainer>\n      </>\n    );\n  };\n\n  // Renderizza il form per inserimento metri e selezione bobina\n  const renderForm = () => {\n    if (!selectedCavo) return null;\n\n    // Verifica se ci sono bobine disponibili\n    if (bobine.length === 0 && !bobineLoading) {\n      return (\n        <Paper sx={{ p: 3 }}>\n          <Typography variant=\"h6\" gutterBottom>\n            Inserimento metri posati\n          </Typography>\n\n          <Alert severity=\"warning\" sx={{ mb: 3 }}>\n            Non ci sono bobine disponibili nel cantiere. Puoi comunque registrare i metri posati utilizzando l'opzione \"BOBINA VUOTA\".\n          </Alert>\n\n          <Box sx={{ mb: 3, p: 2, bgcolor: '#f5f5f5', borderRadius: 1 }}>\n            <Typography variant=\"subtitle1\" gutterBottom fontWeight=\"bold\" color=\"primary\">\n              Cavo selezionato: {selectedCavo.id_cavo}\n            </Typography>\n            <Grid container spacing={2}>\n              <Grid item xs={12} md={4}>\n                <Typography variant=\"body2\">\n                  <strong>Tipologia:</strong> {selectedCavo.tipologia || 'N/A'}\n                </Typography>\n              </Grid>\n              <Grid item xs={12} md={4}>\n                <Typography variant=\"body2\">\n                  <strong>Formazione:</strong> {selectedCavo.sezione || 'N/A'}\n                </Typography>\n              </Grid>\n              <Grid item xs={12} md={4}>\n                <Typography variant=\"body2\">\n                  <strong>Metri teorici:</strong> {selectedCavo.metri_teorici || 'N/A'} m\n                </Typography>\n              </Grid>\n            </Grid>\n          </Box>\n\n          <Grid container spacing={3}>\n            <Grid item xs={12} md={6}>\n              <TextField\n                fullWidth\n                label=\"Metri posati\"\n                name=\"metri_posati\"\n                value={formData.metri_posati}\n                onChange={handleInputChange}\n                type=\"number\"\n                InputProps={{\n                  inputProps: { min: 0, step: 0.1 }\n                }}\n              />\n            </Grid>\n            <Grid item xs={12} md={6}>\n              <FormControl fullWidth>\n                <InputLabel>Bobina</InputLabel>\n                <Select\n                  name=\"id_bobina\"\n                  value=\"BOBINA_VUOTA\"\n                  disabled\n                >\n                  <MenuItem value=\"BOBINA_VUOTA\" sx={{ fontWeight: 'bold', color: '#2e7d32', bgcolor: '#f1f8e9' }}>\n                    BOBINA VUOTA (Cavo posato senza bobina)\n                  </MenuItem>\n                </Select>\n                <Typography variant=\"caption\" color=\"text.secondary\" sx={{ mt: 1 }}>\n                  Non ci sono bobine disponibili. Verrà utilizzata l'opzione BOBINA VUOTA.\n                </Typography>\n              </FormControl>\n            </Grid>\n          </Grid>\n\n          <Box sx={{ mt: 3, display: 'flex', justifyContent: 'space-between' }}>\n            <Button\n              variant=\"outlined\"\n              color=\"secondary\"\n              onClick={() => {\n                setSelectedCavo(null);\n                setFormData({\n                  id_cavo: '',\n                  metri_posati: '',\n                  id_bobina: ''\n                });\n              }}\n              disabled={saving}\n            >\n              Annulla\n            </Button>\n            <Button\n              variant=\"contained\"\n              color=\"primary\"\n              onClick={() => handleSave()}\n              disabled={saving || !formData.metri_posati}\n            >\n              {saving ? <CircularProgress size={24} /> : 'Salva'}\n            </Button>\n          </Box>\n        </Paper>\n      );\n    }\n\n    const compatibleBobine = getCompatibleBobine();\n\n    return (\n      <Paper sx={{ p: 3 }}>\n        <Typography variant=\"h6\" gutterBottom>\n          Inserimento metri posati\n        </Typography>\n\n        <Alert severity=\"info\" sx={{ mb: 3 }}>\n          Inserisci i metri posati per il cavo selezionato e associa una bobina. Se il cavo è stato posato senza una bobina specifica, seleziona <strong>BOBINA VUOTA</strong>.\n        </Alert>\n\n        <Box sx={{ mb: 3, p: 2, bgcolor: '#f5f5f5', borderRadius: 1 }}>\n          <Typography variant=\"subtitle1\" gutterBottom fontWeight=\"bold\" color=\"primary\">\n            Cavo selezionato: {selectedCavo.id_cavo}\n          </Typography>\n          <Grid container spacing={2}>\n            <Grid item xs={12} md={4}>\n              <Typography variant=\"body2\">\n                <strong>Tipologia:</strong> {selectedCavo.tipologia || 'N/A'}\n              </Typography>\n            </Grid>\n            <Grid item xs={12} md={4}>\n              <Typography variant=\"body2\">\n                <strong>Formazione:</strong> {selectedCavo.sezione || 'N/A'}\n              </Typography>\n            </Grid>\n            <Grid item xs={12} md={4}>\n              <Typography variant=\"body2\">\n                <strong>Metri teorici:</strong> {selectedCavo.metri_teorici || 'N/A'} m\n              </Typography>\n            </Grid>\n            <Grid item xs={12} md={6}>\n              <Typography variant=\"body2\">\n                <strong>Ubicazione partenza:</strong> {selectedCavo.ubicazione_partenza || 'N/A'}\n              </Typography>\n            </Grid>\n            <Grid item xs={12} md={6}>\n              <Typography variant=\"body2\">\n                <strong>Ubicazione arrivo:</strong> {selectedCavo.ubicazione_arrivo || 'N/A'}\n              </Typography>\n            </Grid>\n            <Grid item xs={12}>\n              <Typography variant=\"body2\">\n                <strong>Stato:</strong>\n                <Chip\n                  label={selectedCavo.stato_installazione || 'N/D'}\n                  size=\"small\"\n                  color={getCableStateColor(selectedCavo.stato_installazione)}\n                  variant=\"outlined\"\n                  sx={{ ml: 1 }}\n                />\n              </Typography>\n            </Grid>\n          </Grid>\n        </Box>\n\n        <Divider sx={{ mb: 3 }} />\n\n        <Grid container spacing={3}>\n          <Grid item xs={12} md={6}>\n            <TextField\n              fullWidth\n              label=\"Metri posati\"\n              name=\"metri_posati\"\n              value={formData.metri_posati}\n              onChange={handleInputChange}\n              type=\"number\"\n              error={!!formErrors.metri_posati}\n              helperText={formErrors.metri_posati || (formWarnings.metri_posati && (\n                <span style={{ color: 'orange' }}>{formWarnings.metri_posati}</span>\n              ))}\n              disabled={saving}\n              InputProps={{\n                inputProps: { min: 0, step: 0.1 }\n              }}\n            />\n          </Grid>\n          <Grid item xs={12} md={6}>\n            <FormControl fullWidth error={!!formErrors.id_bobina}>\n              <InputLabel>Bobina</InputLabel>\n              <Select\n                name=\"id_bobina\"\n                value={formData.id_bobina}\n                onChange={handleInputChange}\n                disabled={saving || bobineLoading || compatibleBobineLoading}\n              >\n                {/* Opzione BOBINA VUOTA sempre disponibile e in evidenza */}\n                <MenuItem value=\"BOBINA_VUOTA\" sx={{ fontWeight: 'bold', color: '#2e7d32', bgcolor: '#f1f8e9' }}>\n                  BOBINA VUOTA (Cavo posato senza bobina)\n                </MenuItem>\n\n                {/* Separatore */}\n                <Divider />\n\n                {/* Indicatore di caricamento */}\n                {compatibleBobineLoading ? (\n                  <MenuItem disabled>\n                    <Box sx={{ display: 'flex', alignItems: 'center' }}>\n                      <CircularProgress size={20} sx={{ mr: 1 }} />\n                      <Typography variant=\"caption\">\n                        Caricamento bobine compatibili...\n                      </Typography>\n                    </Box>\n                  </MenuItem>\n                ) : compatibleBobine.length === 0 ? (\n                  <MenuItem disabled>\n                    <Typography variant=\"caption\" color=\"text.secondary\">\n                      Nessuna bobina compatibile disponibile. Utilizzare BOBINA VUOTA.\n                    </Typography>\n                  </MenuItem>\n                ) : (\n                  <MenuItem disabled>\n                    <Typography variant=\"caption\">\n                      Bobine compatibili ({compatibleBobine.length})\n                    </Typography>\n                  </MenuItem>\n                )}\n\n                {/* Bobine compatibili */}\n                {!compatibleBobineLoading && compatibleBobine.map((bobina) => (\n                  <MenuItem key={bobina.id_bobina} value={bobina.id_bobina}>\n                    {bobina.id_bobina} - {bobina.tipologia} - {bobina.metri_residui}m\n                  </MenuItem>\n                ))}\n\n                {/* Bobine non compatibili */}\n                {/* Filtra le bobine non compatibili ma utilizzabili */}\n                {(() => {\n                  const nonCompatibleBobine = bobine.filter(bobina => {\n                    // Verifica che non sia già nelle bobine compatibili\n                    const isInCompatible = compatibleBobine.some(b => b.id_bobina === bobina.id_bobina);\n                    // Verifica che non sia terminata\n                    const bobinaState = determineReelState(bobina.metri_residui, bobina.metri_totali);\n                    return !isInCompatible && bobinaState !== REEL_STATES.TERMINATA;\n                  });\n\n                  if (nonCompatibleBobine.length === 0) {\n                    return null;\n                  }\n\n                  return (\n                    <React.Fragment>\n                      <Divider />\n                      <MenuItem disabled>\n                        <Typography variant=\"caption\">\n                          Bobine non compatibili ({nonCompatibleBobine.length})\n                        </Typography>\n                      </MenuItem>\n                      {nonCompatibleBobine.map((bobina) => (\n                        <MenuItem key={bobina.id_bobina} value={bobina.id_bobina}>\n                          {bobina.id_bobina} - {bobina.tipologia} - {bobina.metri_residui}m\n                        </MenuItem>\n                      ))}\n                    </React.Fragment>\n                  );\n                })()}\n              </Select>\n              {formErrors.id_bobina && (\n                <Typography variant=\"caption\" color=\"error\">\n                  {formErrors.id_bobina}\n                </Typography>\n              )}\n              {formWarnings.id_bobina && (\n                <Typography variant=\"caption\" sx={{ color: 'orange' }}>\n                  {formWarnings.id_bobina}\n                </Typography>\n              )}\n              {/* Messaggio informativo sotto il campo */}\n              <Typography variant=\"caption\" color=\"text.secondary\" sx={{ mt: 1 }}>\n                Seleziona una bobina o usa BOBINA VUOTA se il cavo è stato posato senza una bobina specifica.\n              </Typography>\n            </FormControl>\n          </Grid>\n        </Grid>\n\n        <Box sx={{ mt: 3, display: 'flex', justifyContent: 'space-between' }}>\n          <Button\n            variant=\"outlined\"\n            color=\"secondary\"\n            onClick={() => {\n              setSelectedCavo(null);\n              setFormData({\n                id_cavo: '',\n                metri_posati: '',\n                id_bobina: ''\n              });\n            }}\n            disabled={saving}\n          >\n            Annulla\n          </Button>\n          <Button\n            variant=\"contained\"\n            color=\"primary\"\n            onClick={handleSave}\n            disabled={saving || Object.keys(formErrors).length > 0}\n          >\n            {saving ? <CircularProgress size={24} /> : 'Salva'}\n          </Button>\n        </Box>\n      </Paper>\n    );\n  };\n\n  return (\n    <Box>\n      {/* Tabella cavi */}\n      {!selectedCavo && renderCaviTable()}\n\n      {/* Form per inserimento metri e selezione bobina */}\n      {renderForm()}\n\n      {/* Dialog per bobine incompatibili */}\n      <IncompatibleReelDialog\n        open={showIncompatibleReelDialog}\n        onClose={() => setShowIncompatibleReelDialog(false)}\n        cavo={incompatibleReelData.cavo}\n        bobina={incompatibleReelData.bobina}\n        onUpdateCavo={handleUpdateCavoForCompatibility}\n        onSelectAnotherReel={() => {\n          setShowIncompatibleReelDialog(false);\n          setFormData(prev => ({ ...prev, id_bobina: '' }));\n        }}\n      />\n\n      {/* Dialog per cavi già posati */}\n      <Dialog open={showAlreadyLaidDialog} onClose={handleCloseAlreadyLaidDialog}>\n        <DialogTitle>Cavo già posato</DialogTitle>\n        <DialogContent>\n          <DialogContentText>\n            Il cavo {alreadyLaidCavo?.id_cavo} è già stato posato.\n          </DialogContentText>\n          <Box sx={{ mt: 2 }}>\n            <Typography variant=\"body2\" gutterBottom>\n              Puoi:\n            </Typography>\n            <Typography component=\"ul\" variant=\"body2\">\n              <li>Modificare la bobina associata</li>\n              <li>Selezionare un altro cavo</li>\n              <li>Annullare l'operazione</li>\n            </Typography>\n          </Box>\n        </DialogContent>\n        <DialogActions sx={{ p: 2, justifyContent: 'space-between' }}>\n          <Button onClick={handleCloseAlreadyLaidDialog} color=\"secondary\">\n            Annulla operazione\n          </Button>\n          <Box>\n            <Button onClick={handleSelectAnotherCable} color=\"primary\" sx={{ mr: 1 }}>\n              Seleziona altro cavo\n            </Button>\n            <Button onClick={handleModifyReel} variant=\"contained\" color=\"primary\">\n              Modifica bobina\n            </Button>\n          </Box>\n        </DialogActions>\n      </Dialog>\n    </Box>\n  );\n};\n\nexport default MetriPosatiSemplificatoForm;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAClD,SACEC,GAAG,EACHC,UAAU,EACVC,SAAS,EACTC,MAAM,EACNC,KAAK,EACLC,KAAK,EACLC,SAAS,EACTC,SAAS,EACTC,cAAc,EACdC,SAAS,EACTC,QAAQ,EACRC,gBAAgB,EAChBC,KAAK,EACLC,IAAI,EACJC,OAAO,EACPC,IAAI,EACJC,WAAW,EACXC,UAAU,EACVC,MAAM,EACNC,QAAQ,EACRC,MAAM,EACNC,WAAW,EACXC,aAAa,EACbC,aAAa,EACbC,iBAAiB,QACZ,eAAe;AACtB,SAASC,WAAW,QAAQ,kBAAkB;AAC9C,OAAOC,WAAW,MAAM,4BAA4B;AACpD,OAAOC,gBAAgB,MAAM,iCAAiC;AAC9D,SACEC,YAAY,EACZC,WAAW,EACXC,mBAAmB,EACnBC,kBAAkB,EAClBC,cAAc,EACdC,YAAY,EACZC,gBAAgB,EAChBC,kBAAkB,EAClBC,iBAAiB,QACZ,wBAAwB;AAC/B,SAASC,wBAAwB,QAAQ,6BAA6B;AACtE,OAAOC,sBAAsB,MAAM,0BAA0B;;AAE7D;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AARA,SAAAC,MAAA,IAAAC,OAAA,EAAAC,QAAA,IAAAC,SAAA;AASA,MAAMC,2BAA2B,GAAGA,CAAC;EAAEC,UAAU;EAAEC,SAAS;EAAEC;AAAQ,CAAC,KAAK;EAAAC,EAAA;EAC1E,MAAMC,QAAQ,GAAGvB,WAAW,CAAC,CAAC;;EAE9B;EACA,MAAM,CAACwB,IAAI,EAAEC,OAAO,CAAC,GAAGpD,QAAQ,CAAC,EAAE,CAAC;EACpC,MAAM,CAACqD,MAAM,EAAEC,SAAS,CAAC,GAAGtD,QAAQ,CAAC,EAAE,CAAC;EACxC,MAAM,CAACuD,YAAY,EAAEC,eAAe,CAAC,GAAGxD,QAAQ,CAAC,IAAI,CAAC;EACtD,MAAM,CAACyD,QAAQ,EAAEC,WAAW,CAAC,GAAG1D,QAAQ,CAAC;IACvC2D,OAAO,EAAE,EAAE;IACXC,YAAY,EAAE,EAAE;IAChBC,SAAS,EAAE;EACb,CAAC,CAAC;;EAEF;EACA,MAAM,CAACC,OAAO,EAAEC,UAAU,CAAC,GAAG/D,QAAQ,CAAC,KAAK,CAAC;EAC7C,MAAM,CAACgE,WAAW,EAAEC,cAAc,CAAC,GAAGjE,QAAQ,CAAC,KAAK,CAAC;EACrD,MAAM,CAACkE,aAAa,EAAEC,gBAAgB,CAAC,GAAGnE,QAAQ,CAAC,KAAK,CAAC;EACzD,MAAM,CAACoE,MAAM,EAAEC,SAAS,CAAC,GAAGrE,QAAQ,CAAC,KAAK,CAAC;;EAE3C;EACA,MAAM,CAACsE,UAAU,EAAEC,aAAa,CAAC,GAAGvE,QAAQ,CAAC,CAAC,CAAC,CAAC;EAChD,MAAM,CAACwE,YAAY,EAAEC,eAAe,CAAC,GAAGzE,QAAQ,CAAC,CAAC,CAAC,CAAC;;EAEpD;EACA,MAAM,CAAC0E,0BAA0B,EAAEC,6BAA6B,CAAC,GAAG3E,QAAQ,CAAC,KAAK,CAAC;EACnF,MAAM,CAAC4E,oBAAoB,EAAEC,uBAAuB,CAAC,GAAG7E,QAAQ,CAAC;IAAE8E,IAAI,EAAE,IAAI;IAAEC,MAAM,EAAE;EAAK,CAAC,CAAC;EAC9F,MAAM,CAACC,qBAAqB,EAAEC,wBAAwB,CAAC,GAAGjF,QAAQ,CAAC,KAAK,CAAC;EACzE,MAAM,CAACkF,eAAe,EAAEC,kBAAkB,CAAC,GAAGnF,QAAQ,CAAC,IAAI,CAAC;;EAE5D;EACAC,SAAS,CAAC,MAAM;IACdmF,QAAQ,CAAC,CAAC;IACVC,UAAU,CAAC,CAAC;EACd,CAAC,EAAE,CAACvC,UAAU,CAAC,CAAC;;EAEhB;EACA,MAAMsC,QAAQ,GAAG,MAAAA,CAAA,KAAY;IAC3B,IAAI;MACFnB,cAAc,CAAC,IAAI,CAAC;MACpB,MAAMqB,QAAQ,GAAG,MAAM1D,WAAW,CAAC2D,OAAO,CAACzC,UAAU,CAAC;;MAEtD;MACA,MAAM0C,UAAU,GAAGF,QAAQ,CAACG,MAAM,CAACX,IAAI,IAAI,CAAC3C,YAAY,CAAC2C,IAAI,CAAC,CAAC;MAE/D1B,OAAO,CAACoC,UAAU,CAAC;IACrB,CAAC,CAAC,OAAOE,KAAK,EAAE;MACdC,OAAO,CAACD,KAAK,CAAC,kCAAkC,EAAEA,KAAK,CAAC;MACxD1C,OAAO,CAAC,mCAAmC,IAAI0C,KAAK,CAACE,OAAO,IAAI,oBAAoB,CAAC,CAAC;IACxF,CAAC,SAAS;MACR3B,cAAc,CAAC,KAAK,CAAC;IACvB;EACF,CAAC;;EAED;EACA,MAAMoB,UAAU,GAAG,MAAAA,CAAA,KAAY;IAC7B,IAAI;MACFlB,gBAAgB,CAAC,IAAI,CAAC;MACtBwB,OAAO,CAACE,GAAG,CAAC,kCAAkC,EAAE/C,UAAU,CAAC;MAC3D,MAAMgD,UAAU,GAAG,MAAMjE,gBAAgB,CAACkE,SAAS,CAACjD,UAAU,CAAC;MAC/D6C,OAAO,CAACE,GAAG,CAAC,kBAAkB,EAAEC,UAAU,CAAC;MAC3CxC,SAAS,CAACwC,UAAU,CAAC;IACvB,CAAC,CAAC,OAAOJ,KAAK,EAAE;MACdC,OAAO,CAACD,KAAK,CAAC,sCAAsC,EAAEA,KAAK,CAAC;MAC5D1C,OAAO,CAAC,uCAAuC,IAAI0C,KAAK,CAACE,OAAO,IAAI,oBAAoB,CAAC,CAAC;IAC5F,CAAC,SAAS;MACRzB,gBAAgB,CAAC,KAAK,CAAC;IACzB;EACF,CAAC;;EAED;EACA,MAAM6B,gBAAgB,GAAIlB,IAAI,IAAK;IACjC;IACA,IAAI1C,gBAAgB,CAAC0C,IAAI,CAAC,EAAE;MAC1BK,kBAAkB,CAACL,IAAI,CAAC;MACxBG,wBAAwB,CAAC,IAAI,CAAC;MAC9B;IACF;IAEAzB,eAAe,CAACsB,IAAI,CAAC;IACrBpB,WAAW,CAAC;MACVC,OAAO,EAAEmB,IAAI,CAACnB,OAAO;MACrBC,YAAY,EAAE,EAAE;MAChBC,SAAS,EAAE;IACb,CAAC,CAAC;IACFU,aAAa,CAAC,CAAC,CAAC,CAAC;IACjBE,eAAe,CAAC,CAAC,CAAC,CAAC;EACrB,CAAC;;EAED;EACA,MAAMwB,iBAAiB,GAAIC,CAAC,IAAK;IAC/B,MAAM;MAAEC,IAAI;MAAEC;IAAM,CAAC,GAAGF,CAAC,CAACG,MAAM;IAChC3C,WAAW,CAAC4C,IAAI,KAAK;MAAE,GAAGA,IAAI;MAAE,CAACH,IAAI,GAAGC;IAAM,CAAC,CAAC,CAAC;;IAEjD;IACAG,aAAa,CAACJ,IAAI,EAAEC,KAAK,CAAC;EAC5B,CAAC;;EAED;EACA,MAAMG,aAAa,GAAGA,CAACJ,IAAI,EAAEC,KAAK,KAAK;IACrC,MAAMI,SAAS,GAAG;MAAE,GAAGlC;IAAW,CAAC;IACnC,MAAMmC,WAAW,GAAG;MAAE,GAAGjC;IAAa,CAAC;IAEvC,IAAI2B,IAAI,KAAK,cAAc,EAAE;MAC3B;MACA,IAAIC,KAAK,KAAK,EAAE,EAAE;QAChBI,SAAS,CAAC5C,YAAY,GAAG,iCAAiC;MAC5D,CAAC,MAAM,IAAI8C,KAAK,CAACN,KAAK,CAAC,IAAIO,UAAU,CAACP,KAAK,CAAC,GAAG,CAAC,EAAE;QAChDI,SAAS,CAAC5C,YAAY,GAAG,iDAAiD;MAC5E,CAAC,MAAM;QACL,OAAO4C,SAAS,CAAC5C,YAAY;;QAE7B;QACA,MAAMgD,WAAW,GAAGD,UAAU,CAACP,KAAK,CAAC;QACrC,IAAI7C,YAAY,IAAIqD,WAAW,GAAGrD,YAAY,CAACsD,aAAa,EAAE;UAC5DJ,WAAW,CAAC7C,YAAY,GAAG,mBAAmBgD,WAAW,+BAA+BrD,YAAY,CAACsD,aAAa,GAAG;QACvH,CAAC,MAAM;UACL,OAAOJ,WAAW,CAAC7C,YAAY;QACjC;;QAEA;QACA,IAAIH,QAAQ,CAACI,SAAS,IAAIJ,QAAQ,CAACI,SAAS,KAAK,cAAc,EAAE;UAC/D,MAAMiD,cAAc,GAAGzD,MAAM,CAAC0D,IAAI,CAACC,CAAC,IAAIA,CAAC,CAACnD,SAAS,KAAKJ,QAAQ,CAACI,SAAS,CAAC;UAC3E,IAAIiD,cAAc,IAAIF,WAAW,GAAGE,cAAc,CAACG,aAAa,EAAE;YAChER,WAAW,CAAC5C,SAAS,GAAG,mBAAmB+C,WAAW,4CAA4CE,cAAc,CAACG,aAAa,GAAG;UACnI,CAAC,MAAM;YACL,OAAOR,WAAW,CAAC5C,SAAS;UAC9B;QACF;MACF;IACF;IAEA,IAAIsC,IAAI,KAAK,WAAW,EAAE;MACxB;MACA,IAAIC,KAAK,KAAK,EAAE,EAAE;QAChBI,SAAS,CAAC3C,SAAS,GAAG,0BAA0B;MAClD,CAAC,MAAM;QACL,OAAO2C,SAAS,CAAC3C,SAAS;;QAE1B;QACA,IAAIuC,KAAK,KAAK,cAAc,IAAI3C,QAAQ,CAACG,YAAY,EAAE;UACrD,MAAMgD,WAAW,GAAGD,UAAU,CAAClD,QAAQ,CAACG,YAAY,CAAC;UACrD,MAAMkD,cAAc,GAAGzD,MAAM,CAAC0D,IAAI,CAACC,CAAC,IAAIA,CAAC,CAACnD,SAAS,KAAKuC,KAAK,CAAC;UAC9D,IAAIU,cAAc,IAAIF,WAAW,GAAGE,cAAc,CAACG,aAAa,EAAE;YAChER,WAAW,CAAC5C,SAAS,GAAG,mBAAmB+C,WAAW,4CAA4CE,cAAc,CAACG,aAAa,GAAG;UACnI,CAAC,MAAM;YACL,OAAOR,WAAW,CAAC5C,SAAS;UAC9B;QACF;MACF;IACF;IAEAU,aAAa,CAACiC,SAAS,CAAC;IACxB/B,eAAe,CAACgC,WAAW,CAAC;EAC9B,CAAC;;EAED;EACA,MAAMS,YAAY,GAAGA,CAAA,KAAM;IACzB,MAAMV,SAAS,GAAG,CAAC,CAAC;;IAEpB;IACA,IAAI,CAAC/C,QAAQ,CAACG,YAAY,EAAE;MAC1B4C,SAAS,CAAC5C,YAAY,GAAG,iCAAiC;IAC5D,CAAC,MAAM,IAAI8C,KAAK,CAACjD,QAAQ,CAACG,YAAY,CAAC,IAAI+C,UAAU,CAAClD,QAAQ,CAACG,YAAY,CAAC,GAAG,CAAC,EAAE;MAChF4C,SAAS,CAAC5C,YAAY,GAAG,iDAAiD;IAC5E;;IAEA;IACA,IAAI,CAACH,QAAQ,CAACI,SAAS,EAAE;MACvB2C,SAAS,CAAC3C,SAAS,GAAG,0BAA0B;IAClD;IAEAU,aAAa,CAACiC,SAAS,CAAC;IACxB,OAAOW,MAAM,CAACC,IAAI,CAACZ,SAAS,CAAC,CAACa,MAAM,KAAK,CAAC;EAC5C,CAAC;;EAED;EACA,MAAMC,kBAAkB,GAAGA,CAAA,KAAM;IAC/B,IAAI7D,QAAQ,CAACI,SAAS,KAAK,cAAc,EAAE;MACzC,OAAO,IAAI,CAAC,CAAC;IACf;IAEA,MAAMiD,cAAc,GAAGzD,MAAM,CAAC0D,IAAI,CAACC,CAAC,IAAIA,CAAC,CAACnD,SAAS,KAAKJ,QAAQ,CAACI,SAAS,CAAC;IAC3E,IAAI,CAACiD,cAAc,EAAE;MACnB,OAAO,KAAK;IACd;;IAEA;IACA,MAAMS,oBAAoB,GAAGhE,YAAY,CAACiE,SAAS,KAAKV,cAAc,CAACU,SAAS;;IAEhF;IACA,MAAMC,kBAAkB,GAAGC,MAAM,CAACnE,YAAY,CAACoE,OAAO,CAAC,KAAKD,MAAM,CAACZ,cAAc,CAACa,OAAO,CAAC;IAE1F,OAAOJ,oBAAoB,IAAIE,kBAAkB;EACnD,CAAC;;EAED;EACA,MAAMG,UAAU,GAAG,MAAAA,CAAA,KAAY;IAC7B;IACA,IAAIvE,MAAM,CAACgE,MAAM,KAAK,CAAC,IAAI,CAACnD,aAAa,EAAE;MACzC,IAAI,CAACT,QAAQ,CAACG,YAAY,IAAI8C,KAAK,CAACC,UAAU,CAAClD,QAAQ,CAACG,YAAY,CAAC,CAAC,IAAI+C,UAAU,CAAClD,QAAQ,CAACG,YAAY,CAAC,IAAI,CAAC,EAAE;QAChHW,aAAa,CAAC;UACZ,GAAGD,UAAU;UACbV,YAAY,EAAE;QAChB,CAAC,CAAC;QACF;MACF;;MAEA;MACAH,QAAQ,CAACI,SAAS,GAAG,cAAc;IACrC,CAAC,MAAM;MACL;MACA,IAAI,CAACqD,YAAY,CAAC,CAAC,EAAE;QACnB;MACF;;MAEA;MACA,IAAI,CAACI,kBAAkB,CAAC,CAAC,EAAE;QACzB;QACA,MAAMR,cAAc,GAAGzD,MAAM,CAAC0D,IAAI,CAACC,CAAC,IAAIA,CAAC,CAACnD,SAAS,KAAKJ,QAAQ,CAACI,SAAS,CAAC;QAC3EgB,uBAAuB,CAAC;UACtBC,IAAI,EAAEvB,YAAY;UAClBwB,MAAM,EAAE+B;QACV,CAAC,CAAC;QACFnC,6BAA6B,CAAC,IAAI,CAAC;QACnC;MACF;IACF;;IAEA;IACA,IAAI;MACFN,SAAS,CAAC,IAAI,CAAC;;MAEf;MACA,MAAMuC,WAAW,GAAGD,UAAU,CAAClD,QAAQ,CAACG,YAAY,CAAC;;MAErD;MACA+B,OAAO,CAACE,GAAG,CAAC,6DAA6D,CAAC;MAC1EF,OAAO,CAACE,GAAG,CAAC,eAAe,EAAE/C,UAAU,CAAC;MACxC6C,OAAO,CAACE,GAAG,CAAC,YAAY,EAAEpC,QAAQ,CAACE,OAAO,CAAC;MAC3CgC,OAAO,CAACE,GAAG,CAAC,iBAAiB,EAAEe,WAAW,CAAC;MAC3CjB,OAAO,CAACE,GAAG,CAAC,cAAc,EAAEpC,QAAQ,CAACI,SAAS,CAAC;;MAE/C;MACA,MAAMjC,WAAW,CAACiG,iBAAiB,CACjC/E,UAAU,EACVW,QAAQ,CAACE,OAAO,EAChBiD,WAAW,EACXnD,QAAQ,CAACI,SAAS,EAClB,IAAI,CAAC;MACP,CAAC;;MAED;MACAd,SAAS,CAAC,sCAAsC,CAAC;;MAEjD;MACAS,eAAe,CAAC,IAAI,CAAC;MACrBE,WAAW,CAAC;QACVC,OAAO,EAAE,EAAE;QACXC,YAAY,EAAE,EAAE;QAChBC,SAAS,EAAE;MACb,CAAC,CAAC;;MAEF;MACAuB,QAAQ,CAAC,CAAC;MACVC,UAAU,CAAC,CAAC;IACd,CAAC,CAAC,OAAOK,KAAK,EAAE;MACdC,OAAO,CAACD,KAAK,CAAC,gCAAgC,EAAEA,KAAK,CAAC;MACtD1C,OAAO,CAAC,iCAAiC,IAAI0C,KAAK,CAACE,OAAO,IAAI,oBAAoB,CAAC,CAAC;IACtF,CAAC,SAAS;MACRvB,SAAS,CAAC,KAAK,CAAC;IAClB;EACF,CAAC;;EAED;EACA,MAAMyD,gCAAgC,GAAG,MAAAA,CAAA,KAAY;IACnD,IAAI;MACFzD,SAAS,CAAC,IAAI,CAAC;MACfM,6BAA6B,CAAC,KAAK,CAAC;MAEpC,MAAM;QAAEG,IAAI;QAAEC;MAAO,CAAC,GAAGH,oBAAoB;;MAE7C;MACA,MAAMhD,WAAW,CAACmG,0BAA0B,CAC1CjF,UAAU,EACVgC,IAAI,CAACnB,OAAO,EACZ;QACEE,SAAS,EAAEkB,MAAM,CAAClB,SAAS;QAC3B2D,SAAS,EAAEzC,MAAM,CAACyC,SAAS;QAC3BG,OAAO,EAAE5C,MAAM,CAAC4C;MAClB,CACF,CAAC;;MAED;MACA,MAAM/F,WAAW,CAACiG,iBAAiB,CACjC/E,UAAU,EACVW,QAAQ,CAACE,OAAO,EAChBgD,UAAU,CAAClD,QAAQ,CAACG,YAAY,CAAC,EACjCH,QAAQ,CAACI,SAAS,EAClB,IAAI,CAAC;MACP,CAAC;;MAED;MACAd,SAAS,CAAC,wDAAwD,CAAC;;MAEnE;MACAS,eAAe,CAAC,IAAI,CAAC;MACrBE,WAAW,CAAC;QACVC,OAAO,EAAE,EAAE;QACXC,YAAY,EAAE,EAAE;QAChBC,SAAS,EAAE;MACb,CAAC,CAAC;;MAEF;MACAuB,QAAQ,CAAC,CAAC;MACVC,UAAU,CAAC,CAAC;IACd,CAAC,CAAC,OAAOK,KAAK,EAAE;MACdC,OAAO,CAACD,KAAK,CAAC,2CAA2C,EAAEA,KAAK,CAAC;MACjE1C,OAAO,CAAC,4CAA4C,IAAI0C,KAAK,CAACE,OAAO,IAAI,oBAAoB,CAAC,CAAC;IACjG,CAAC,SAAS;MACRvB,SAAS,CAAC,KAAK,CAAC;IAClB;EACF,CAAC;;EAED;EACA,MAAM2D,4BAA4B,GAAGA,CAAA,KAAM;IACzC/C,wBAAwB,CAAC,KAAK,CAAC;IAC/BE,kBAAkB,CAAC,IAAI,CAAC;EAC1B,CAAC;;EAED;EACA,MAAM8C,wBAAwB,GAAGA,CAAA,KAAM;IACrCD,4BAA4B,CAAC,CAAC;EAChC,CAAC;;EAED;EACA,MAAME,gBAAgB,GAAGA,CAAA,KAAM;IAC7B,IAAIhD,eAAe,EAAE;MACnBhC,QAAQ,CAAC,+CAA+CgC,eAAe,CAACvB,OAAO,EAAE,CAAC;IACpF;IACAqE,4BAA4B,CAAC,CAAC;EAChC,CAAC;;EAED;EACA,MAAM,CAACG,gBAAgB,EAAEC,mBAAmB,CAAC,GAAGpI,QAAQ,CAAC,EAAE,CAAC;EAC5D,MAAM,CAACqI,uBAAuB,EAAEC,0BAA0B,CAAC,GAAGtI,QAAQ,CAAC,KAAK,CAAC;;EAE7E;EACAC,SAAS,CAAC,MAAM;IACd,IAAIsD,YAAY,EAAE;MAChBgF,oBAAoB,CAAChF,YAAY,CAAC;IACpC,CAAC,MAAM;MACL6E,mBAAmB,CAAC,EAAE,CAAC;IACzB;EACF,CAAC,EAAE,CAAC7E,YAAY,CAAC,CAAC;;EAElB;EACA,MAAMgF,oBAAoB,GAAG,MAAOzD,IAAI,IAAK;IAC3C,IAAI;MACFwD,0BAA0B,CAAC,IAAI,CAAC;MAChC3C,OAAO,CAACE,GAAG,CAAC,0CAA0C,EAAEf,IAAI,CAAC;;MAE7D;MACA,MAAM0D,oBAAoB,GAAG,MAAM3G,gBAAgB,CAAC4G,oBAAoB,CACtE3F,UAAU,EACVgC,IAAI,CAAC0C,SAAS,EACd,IAAI;MAAE;MACN1C,IAAI,CAAC6C,OACP,CAAC;MAEDhC,OAAO,CAACE,GAAG,CAAC,0CAA0C,EAAE2C,oBAAoB,CAAC;MAC7EJ,mBAAmB,CAACI,oBAAoB,CAAC;IAC3C,CAAC,CAAC,OAAO9C,KAAK,EAAE;MACdC,OAAO,CAACD,KAAK,CAAC,kDAAkD,EAAEA,KAAK,CAAC;MACxE1C,OAAO,CAAC,mDAAmD,IAAI0C,KAAK,CAACE,OAAO,IAAI,oBAAoB,CAAC,CAAC;;MAEtG;MACAD,OAAO,CAACE,GAAG,CAAC,wDAAwD,CAAC;MACrE,MAAM6C,qBAAqB,GAAGC,wBAAwB,CAAC7D,IAAI,CAAC;MAC5DsD,mBAAmB,CAACM,qBAAqB,CAAC;IAC5C,CAAC,SAAS;MACRJ,0BAA0B,CAAC,KAAK,CAAC;IACnC;EACF,CAAC;;EAED;EACA,MAAMK,wBAAwB,GAAI7D,IAAI,IAAK;IACzC,IAAI,CAACA,IAAI,EAAE,OAAO,EAAE;IAEpBa,OAAO,CAACE,GAAG,CAAC,mDAAmD,EAAEf,IAAI,CAAC;IACtEa,OAAO,CAACE,GAAG,CAAC,qBAAqB,EAAExC,MAAM,CAAC;;IAE1C;IACA,MAAMuF,aAAa,GAAGlB,MAAM,CAAC5C,IAAI,CAAC0C,SAAS,IAAI,EAAE,CAAC,CAACqB,IAAI,CAAC,CAAC;IACzD,MAAMC,WAAW,GAAGpB,MAAM,CAAC5C,IAAI,CAAC6C,OAAO,IAAI,EAAE,CAAC,CAACkB,IAAI,CAAC,CAAC;IAErD,MAAMH,qBAAqB,GAAGrF,MAAM,CAACoC,MAAM,CAACV,MAAM,IAAI;MACpD;MACA,MAAMgE,eAAe,GAAGrB,MAAM,CAAC3C,MAAM,CAACyC,SAAS,IAAI,EAAE,CAAC,CAACqB,IAAI,CAAC,CAAC;MAC7D,MAAMG,aAAa,GAAGtB,MAAM,CAAC3C,MAAM,CAAC4C,OAAO,IAAI,EAAE,CAAC,CAACkB,IAAI,CAAC,CAAC;;MAEzD;MACA,MAAMI,WAAW,GAAGhH,kBAAkB,CAAC8C,MAAM,CAACkC,aAAa,EAAElC,MAAM,CAACmE,YAAY,CAAC;;MAEjF;MACAvD,OAAO,CAACE,GAAG,CAAC,mBAAmBd,MAAM,CAAClB,SAAS,GAAG,EAAE;QAClD2D,SAAS,EAAE,IAAIuB,eAAe,UAAUH,aAAa,GAAG;QACxDjB,OAAO,EAAE,IAAIqB,aAAa,UAAUF,WAAW,GAAG;QAClDK,KAAK,EAAEF,WAAW;QAClBG,aAAa,EAAEL,eAAe,KAAKH,aAAa,IAAII,aAAa,KAAKF,WAAW,IAAIG,WAAW,KAAKlH,WAAW,CAACsH;MACnH,CAAC,CAAC;MAEF,OAAON,eAAe,KAAKH,aAAa,IACjCI,aAAa,KAAKF,WAAW,IAC7BG,WAAW,KAAKlH,WAAW,CAACsH,SAAS;IAC9C,CAAC,CAAC;IAEF1D,OAAO,CAACE,GAAG,CAAC,wCAAwC,EAAE6C,qBAAqB,CAACrB,MAAM,CAAC;IACnF,OAAOqB,qBAAqB;EAC9B,CAAC;;EAED;EACA,MAAMY,mBAAmB,GAAGA,CAAA,KAAM;IAChC,OAAOnB,gBAAgB;EACzB,CAAC;;EAED;EACA,MAAMoB,eAAe,GAAGA,CAAA,KAAM;IAC5B,IAAIvF,WAAW,EAAE;MACf,oBACEtB,OAAA,CAACxC,GAAG;QAACsJ,EAAE,EAAE;UAAEC,OAAO,EAAE,MAAM;UAAEC,cAAc,EAAE,QAAQ;UAAEC,EAAE,EAAE;QAAE,CAAE;QAAAC,QAAA,eAC5DlH,OAAA,CAAC7B,gBAAgB;UAAAgJ,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACjB,CAAC;IAEV;IAEA,IAAI7G,IAAI,CAACkE,MAAM,KAAK,CAAC,EAAE;MACrB,oBACE3E,OAAA,CAAC5B,KAAK;QAACmJ,QAAQ,EAAC,MAAM;QAACT,EAAE,EAAE;UAAEG,EAAE,EAAE;QAAE,CAAE;QAAAC,QAAA,EAAC;MAEtC;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAO,CAAC;IAEZ;IAEA,oBACEtH,OAAA,CAAAE,SAAA;MAAAgH,QAAA,gBACElH,OAAA,CAAC5B,KAAK;QAACmJ,QAAQ,EAAC,MAAM;QAACT,EAAE,EAAE;UAAEU,EAAE,EAAE;QAAE,CAAE;QAAAN,QAAA,EAAC;MAEtC;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAO,CAAC,eAERtH,OAAA,CAAChC,cAAc;QAACyJ,SAAS,EAAE7J,KAAM;QAACkJ,EAAE,EAAE;UAAEU,EAAE,EAAE;QAAE,CAAE;QAAAN,QAAA,eAC9ClH,OAAA,CAACnC,KAAK;UAAC6J,IAAI,EAAC,OAAO;UAAAR,QAAA,gBACjBlH,OAAA,CAAC/B,SAAS;YAAAiJ,QAAA,eACRlH,OAAA,CAAC9B,QAAQ;cAAC4I,EAAE,EAAE;gBAAEa,OAAO,EAAE;cAAU,CAAE;cAAAT,QAAA,gBACnClH,OAAA,CAACjC,SAAS;gBAAAmJ,QAAA,EAAC;cAAO;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAW,CAAC,eAC9BtH,OAAA,CAACjC,SAAS;gBAAAmJ,QAAA,EAAC;cAAS;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAW,CAAC,eAChCtH,OAAA,CAACjC,SAAS;gBAAAmJ,QAAA,EAAC;cAAU;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAW,CAAC,eACjCtH,OAAA,CAACjC,SAAS;gBAAAmJ,QAAA,EAAC;cAAa;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAW,CAAC,eACpCtH,OAAA,CAACjC,SAAS;gBAAAmJ,QAAA,EAAC;cAAK;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAW,CAAC,eAC5BtH,OAAA,CAACjC,SAAS;gBAAAmJ,QAAA,EAAC;cAAM;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAW,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACrB;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACF,CAAC,eACZtH,OAAA,CAAClC,SAAS;YAAAoJ,QAAA,EACPzG,IAAI,CAACmH,GAAG,CAAExF,IAAI,IAAK;cAClB,MAAMyF,WAAW,GAAGnI,gBAAgB,CAAC0C,IAAI,CAAC;cAC1C,oBACEpC,OAAA,CAAC9B,QAAQ;gBAEP4I,EAAE,EAAE;kBACFa,OAAO,EAAEE,WAAW,GAAG,SAAS,GAAG,SAAS;kBAC5C,SAAS,EAAE;oBAAEF,OAAO,EAAEE,WAAW,GAAG,SAAS,GAAG;kBAAU;gBAC5D,CAAE;gBAAAX,QAAA,gBAEFlH,OAAA,CAACjC,SAAS;kBAAAmJ,QAAA,eAAClH,OAAA;oBAAAkH,QAAA,EAAS9E,IAAI,CAACnB;kBAAO;oBAAAkG,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAS;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAW,CAAC,eACtDtH,OAAA,CAACjC,SAAS;kBAAAmJ,QAAA,EAAE9E,IAAI,CAAC0C,SAAS,IAAI;gBAAK;kBAAAqC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAY,CAAC,eAChDtH,OAAA,CAACjC,SAAS;kBAAAmJ,QAAA,GAAC,MAAI,EAAC9E,IAAI,CAAC0F,mBAAmB,IAAI,KAAK,eAAC9H,OAAA;oBAAAmH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAI,CAAC,OAAG,EAAClF,IAAI,CAAC2F,iBAAiB,IAAI,KAAK;gBAAA;kBAAAZ,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAY,CAAC,eACvGtH,OAAA,CAACjC,SAAS;kBAAAmJ,QAAA,GAAE9E,IAAI,CAAC+B,aAAa,IAAI,KAAK,EAAC,IAAE;gBAAA;kBAAAgD,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAW,CAAC,eACtDtH,OAAA,CAACjC,SAAS;kBAAAmJ,QAAA,eACRlH,OAAA,CAAC3B,IAAI;oBACH2J,KAAK,EAAE5F,IAAI,CAAC6F,mBAAmB,IAAI,KAAM;oBACzCP,IAAI,EAAC,OAAO;oBACZQ,KAAK,EAAEvI,kBAAkB,CAACyC,IAAI,CAAC6F,mBAAmB,CAAE;oBACpDE,OAAO,EAAC;kBAAU;oBAAAhB,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACnB;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACO,CAAC,eACZtH,OAAA,CAACjC,SAAS;kBAAAmJ,QAAA,eACRlH,OAAA,CAACrC,MAAM;oBACL+J,IAAI,EAAC,OAAO;oBACZS,OAAO,EAAC,WAAW;oBACnBD,KAAK,EAAC,SAAS;oBACfE,OAAO,EAAEA,CAAA,KAAM9E,gBAAgB,CAAClB,IAAI,CAAE;oBACtCiG,QAAQ,EAAER,WAAY;oBAAAX,QAAA,EAErBW,WAAW,GAAG,gBAAgB,GAAG;kBAAW;oBAAAV,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACvC;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACA,CAAC;cAAA,GA5BPlF,IAAI,CAACnB,OAAO;gBAAAkG,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OA6BT,CAAC;YAEf,CAAC;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACO,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACP;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACM,CAAC;IAAA,eACjB,CAAC;EAEP,CAAC;;EAED;EACA,MAAMgB,UAAU,GAAGA,CAAA,KAAM;IACvB,IAAI,CAACzH,YAAY,EAAE,OAAO,IAAI;;IAE9B;IACA,IAAIF,MAAM,CAACgE,MAAM,KAAK,CAAC,IAAI,CAACnD,aAAa,EAAE;MACzC,oBACExB,OAAA,CAACpC,KAAK;QAACkJ,EAAE,EAAE;UAAEyB,CAAC,EAAE;QAAE,CAAE;QAAArB,QAAA,gBAClBlH,OAAA,CAACvC,UAAU;UAAC0K,OAAO,EAAC,IAAI;UAACK,YAAY;UAAAtB,QAAA,EAAC;QAEtC;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAY,CAAC,eAEbtH,OAAA,CAAC5B,KAAK;UAACmJ,QAAQ,EAAC,SAAS;UAACT,EAAE,EAAE;YAAEU,EAAE,EAAE;UAAE,CAAE;UAAAN,QAAA,EAAC;QAEzC;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAO,CAAC,eAERtH,OAAA,CAACxC,GAAG;UAACsJ,EAAE,EAAE;YAAEU,EAAE,EAAE,CAAC;YAAEe,CAAC,EAAE,CAAC;YAAEZ,OAAO,EAAE,SAAS;YAAEc,YAAY,EAAE;UAAE,CAAE;UAAAvB,QAAA,gBAC5DlH,OAAA,CAACvC,UAAU;YAAC0K,OAAO,EAAC,WAAW;YAACK,YAAY;YAACE,UAAU,EAAC,MAAM;YAACR,KAAK,EAAC,SAAS;YAAAhB,QAAA,GAAC,oBAC3D,EAACrG,YAAY,CAACI,OAAO;UAAA;YAAAkG,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC7B,CAAC,eACbtH,OAAA,CAACzB,IAAI;YAACoK,SAAS;YAACC,OAAO,EAAE,CAAE;YAAA1B,QAAA,gBACzBlH,OAAA,CAACzB,IAAI;cAACsK,IAAI;cAACC,EAAE,EAAE,EAAG;cAACC,EAAE,EAAE,CAAE;cAAA7B,QAAA,eACvBlH,OAAA,CAACvC,UAAU;gBAAC0K,OAAO,EAAC,OAAO;gBAAAjB,QAAA,gBACzBlH,OAAA;kBAAAkH,QAAA,EAAQ;gBAAU;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,KAAC,EAACzG,YAAY,CAACiE,SAAS,IAAI,KAAK;cAAA;gBAAAqC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAClD;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACT,CAAC,eACPtH,OAAA,CAACzB,IAAI;cAACsK,IAAI;cAACC,EAAE,EAAE,EAAG;cAACC,EAAE,EAAE,CAAE;cAAA7B,QAAA,eACvBlH,OAAA,CAACvC,UAAU;gBAAC0K,OAAO,EAAC,OAAO;gBAAAjB,QAAA,gBACzBlH,OAAA;kBAAAkH,QAAA,EAAQ;gBAAW;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,KAAC,EAACzG,YAAY,CAACoE,OAAO,IAAI,KAAK;cAAA;gBAAAkC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACjD;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACT,CAAC,eACPtH,OAAA,CAACzB,IAAI;cAACsK,IAAI;cAACC,EAAE,EAAE,EAAG;cAACC,EAAE,EAAE,CAAE;cAAA7B,QAAA,eACvBlH,OAAA,CAACvC,UAAU;gBAAC0K,OAAO,EAAC,OAAO;gBAAAjB,QAAA,gBACzBlH,OAAA;kBAAAkH,QAAA,EAAQ;gBAAc;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,KAAC,EAACzG,YAAY,CAACsD,aAAa,IAAI,KAAK,EAAC,IACvE;cAAA;gBAAAgD,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAY;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACT,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACJ,CAAC,eAENtH,OAAA,CAACzB,IAAI;UAACoK,SAAS;UAACC,OAAO,EAAE,CAAE;UAAA1B,QAAA,gBACzBlH,OAAA,CAACzB,IAAI;YAACsK,IAAI;YAACC,EAAE,EAAE,EAAG;YAACC,EAAE,EAAE,CAAE;YAAA7B,QAAA,eACvBlH,OAAA,CAACtC,SAAS;cACRsL,SAAS;cACThB,KAAK,EAAC,cAAc;cACpBvE,IAAI,EAAC,cAAc;cACnBC,KAAK,EAAE3C,QAAQ,CAACG,YAAa;cAC7B+H,QAAQ,EAAE1F,iBAAkB;cAC5B2F,IAAI,EAAC,QAAQ;cACbC,UAAU,EAAE;gBACVC,UAAU,EAAE;kBAAEC,GAAG,EAAE,CAAC;kBAAEC,IAAI,EAAE;gBAAI;cAClC;YAAE;cAAAnC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACE,CAAC,eACPtH,OAAA,CAACzB,IAAI;YAACsK,IAAI;YAACC,EAAE,EAAE,EAAG;YAACC,EAAE,EAAE,CAAE;YAAA7B,QAAA,eACvBlH,OAAA,CAACxB,WAAW;cAACwK,SAAS;cAAA9B,QAAA,gBACpBlH,OAAA,CAACvB,UAAU;gBAAAyI,QAAA,EAAC;cAAM;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAY,CAAC,eAC/BtH,OAAA,CAACtB,MAAM;gBACL+E,IAAI,EAAC,WAAW;gBAChBC,KAAK,EAAC,cAAc;gBACpB2E,QAAQ;gBAAAnB,QAAA,eAERlH,OAAA,CAACrB,QAAQ;kBAAC+E,KAAK,EAAC,cAAc;kBAACoD,EAAE,EAAE;oBAAE4B,UAAU,EAAE,MAAM;oBAAER,KAAK,EAAE,SAAS;oBAAEP,OAAO,EAAE;kBAAU,CAAE;kBAAAT,QAAA,EAAC;gBAEjG;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAU;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACL,CAAC,eACTtH,OAAA,CAACvC,UAAU;gBAAC0K,OAAO,EAAC,SAAS;gBAACD,KAAK,EAAC,gBAAgB;gBAACpB,EAAE,EAAE;kBAAEyC,EAAE,EAAE;gBAAE,CAAE;gBAAArC,QAAA,EAAC;cAEpE;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAY,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACF;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACV,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,eAEPtH,OAAA,CAACxC,GAAG;UAACsJ,EAAE,EAAE;YAAEyC,EAAE,EAAE,CAAC;YAAExC,OAAO,EAAE,MAAM;YAAEC,cAAc,EAAE;UAAgB,CAAE;UAAAE,QAAA,gBACnElH,OAAA,CAACrC,MAAM;YACLwK,OAAO,EAAC,UAAU;YAClBD,KAAK,EAAC,WAAW;YACjBE,OAAO,EAAEA,CAAA,KAAM;cACbtH,eAAe,CAAC,IAAI,CAAC;cACrBE,WAAW,CAAC;gBACVC,OAAO,EAAE,EAAE;gBACXC,YAAY,EAAE,EAAE;gBAChBC,SAAS,EAAE;cACb,CAAC,CAAC;YACJ,CAAE;YACFkH,QAAQ,EAAE3G,MAAO;YAAAwF,QAAA,EAClB;UAED;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC,eACTtH,OAAA,CAACrC,MAAM;YACLwK,OAAO,EAAC,WAAW;YACnBD,KAAK,EAAC,SAAS;YACfE,OAAO,EAAEA,CAAA,KAAMlD,UAAU,CAAC,CAAE;YAC5BmD,QAAQ,EAAE3G,MAAM,IAAI,CAACX,QAAQ,CAACG,YAAa;YAAAgG,QAAA,EAE1CxF,MAAM,gBAAG1B,OAAA,CAAC7B,gBAAgB;cAACuJ,IAAI,EAAE;YAAG;cAAAP,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,GAAG;UAAO;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC5C,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACN,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACD,CAAC;IAEZ;IAEA,MAAM7B,gBAAgB,GAAGmB,mBAAmB,CAAC,CAAC;IAE9C,oBACE5G,OAAA,CAACpC,KAAK;MAACkJ,EAAE,EAAE;QAAEyB,CAAC,EAAE;MAAE,CAAE;MAAArB,QAAA,gBAClBlH,OAAA,CAACvC,UAAU;QAAC0K,OAAO,EAAC,IAAI;QAACK,YAAY;QAAAtB,QAAA,EAAC;MAEtC;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAY,CAAC,eAEbtH,OAAA,CAAC5B,KAAK;QAACmJ,QAAQ,EAAC,MAAM;QAACT,EAAE,EAAE;UAAEU,EAAE,EAAE;QAAE,CAAE;QAAAN,QAAA,GAAC,4IACmG,eAAAlH,OAAA;UAAAkH,QAAA,EAAQ;QAAY;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC,KACtK;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAO,CAAC,eAERtH,OAAA,CAACxC,GAAG;QAACsJ,EAAE,EAAE;UAAEU,EAAE,EAAE,CAAC;UAAEe,CAAC,EAAE,CAAC;UAAEZ,OAAO,EAAE,SAAS;UAAEc,YAAY,EAAE;QAAE,CAAE;QAAAvB,QAAA,gBAC5DlH,OAAA,CAACvC,UAAU;UAAC0K,OAAO,EAAC,WAAW;UAACK,YAAY;UAACE,UAAU,EAAC,MAAM;UAACR,KAAK,EAAC,SAAS;UAAAhB,QAAA,GAAC,oBAC3D,EAACrG,YAAY,CAACI,OAAO;QAAA;UAAAkG,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC7B,CAAC,eACbtH,OAAA,CAACzB,IAAI;UAACoK,SAAS;UAACC,OAAO,EAAE,CAAE;UAAA1B,QAAA,gBACzBlH,OAAA,CAACzB,IAAI;YAACsK,IAAI;YAACC,EAAE,EAAE,EAAG;YAACC,EAAE,EAAE,CAAE;YAAA7B,QAAA,eACvBlH,OAAA,CAACvC,UAAU;cAAC0K,OAAO,EAAC,OAAO;cAAAjB,QAAA,gBACzBlH,OAAA;gBAAAkH,QAAA,EAAQ;cAAU;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC,KAAC,EAACzG,YAAY,CAACiE,SAAS,IAAI,KAAK;YAAA;cAAAqC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAClD;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACT,CAAC,eACPtH,OAAA,CAACzB,IAAI;YAACsK,IAAI;YAACC,EAAE,EAAE,EAAG;YAACC,EAAE,EAAE,CAAE;YAAA7B,QAAA,eACvBlH,OAAA,CAACvC,UAAU;cAAC0K,OAAO,EAAC,OAAO;cAAAjB,QAAA,gBACzBlH,OAAA;gBAAAkH,QAAA,EAAQ;cAAW;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC,KAAC,EAACzG,YAAY,CAACoE,OAAO,IAAI,KAAK;YAAA;cAAAkC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACjD;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACT,CAAC,eACPtH,OAAA,CAACzB,IAAI;YAACsK,IAAI;YAACC,EAAE,EAAE,EAAG;YAACC,EAAE,EAAE,CAAE;YAAA7B,QAAA,eACvBlH,OAAA,CAACvC,UAAU;cAAC0K,OAAO,EAAC,OAAO;cAAAjB,QAAA,gBACzBlH,OAAA;gBAAAkH,QAAA,EAAQ;cAAc;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC,KAAC,EAACzG,YAAY,CAACsD,aAAa,IAAI,KAAK,EAAC,IACvE;YAAA;cAAAgD,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAY;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACT,CAAC,eACPtH,OAAA,CAACzB,IAAI;YAACsK,IAAI;YAACC,EAAE,EAAE,EAAG;YAACC,EAAE,EAAE,CAAE;YAAA7B,QAAA,eACvBlH,OAAA,CAACvC,UAAU;cAAC0K,OAAO,EAAC,OAAO;cAAAjB,QAAA,gBACzBlH,OAAA;gBAAAkH,QAAA,EAAQ;cAAoB;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC,KAAC,EAACzG,YAAY,CAACiH,mBAAmB,IAAI,KAAK;YAAA;cAAAX,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACtE;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACT,CAAC,eACPtH,OAAA,CAACzB,IAAI;YAACsK,IAAI;YAACC,EAAE,EAAE,EAAG;YAACC,EAAE,EAAE,CAAE;YAAA7B,QAAA,eACvBlH,OAAA,CAACvC,UAAU;cAAC0K,OAAO,EAAC,OAAO;cAAAjB,QAAA,gBACzBlH,OAAA;gBAAAkH,QAAA,EAAQ;cAAkB;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC,KAAC,EAACzG,YAAY,CAACkH,iBAAiB,IAAI,KAAK;YAAA;cAAAZ,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAClE;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACT,CAAC,eACPtH,OAAA,CAACzB,IAAI;YAACsK,IAAI;YAACC,EAAE,EAAE,EAAG;YAAA5B,QAAA,eAChBlH,OAAA,CAACvC,UAAU;cAAC0K,OAAO,EAAC,OAAO;cAAAjB,QAAA,gBACzBlH,OAAA;gBAAAkH,QAAA,EAAQ;cAAM;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC,eACvBtH,OAAA,CAAC3B,IAAI;gBACH2J,KAAK,EAAEnH,YAAY,CAACoH,mBAAmB,IAAI,KAAM;gBACjDP,IAAI,EAAC,OAAO;gBACZQ,KAAK,EAAEvI,kBAAkB,CAACkB,YAAY,CAACoH,mBAAmB,CAAE;gBAC5DE,OAAO,EAAC,UAAU;gBAClBrB,EAAE,EAAE;kBAAE0C,EAAE,EAAE;gBAAE;cAAE;gBAAArC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACf,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACQ;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACT,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACJ,CAAC,eAENtH,OAAA,CAAC1B,OAAO;QAACwI,EAAE,EAAE;UAAEU,EAAE,EAAE;QAAE;MAAE;QAAAL,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC,eAE1BtH,OAAA,CAACzB,IAAI;QAACoK,SAAS;QAACC,OAAO,EAAE,CAAE;QAAA1B,QAAA,gBACzBlH,OAAA,CAACzB,IAAI;UAACsK,IAAI;UAACC,EAAE,EAAE,EAAG;UAACC,EAAE,EAAE,CAAE;UAAA7B,QAAA,eACvBlH,OAAA,CAACtC,SAAS;YACRsL,SAAS;YACThB,KAAK,EAAC,cAAc;YACpBvE,IAAI,EAAC,cAAc;YACnBC,KAAK,EAAE3C,QAAQ,CAACG,YAAa;YAC7B+H,QAAQ,EAAE1F,iBAAkB;YAC5B2F,IAAI,EAAC,QAAQ;YACblG,KAAK,EAAE,CAAC,CAACpB,UAAU,CAACV,YAAa;YACjCuI,UAAU,EAAE7H,UAAU,CAACV,YAAY,IAAKY,YAAY,CAACZ,YAAY,iBAC/DlB,OAAA;cAAM0J,KAAK,EAAE;gBAAExB,KAAK,EAAE;cAAS,CAAE;cAAAhB,QAAA,EAAEpF,YAAY,CAACZ;YAAY;cAAAiG,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CAClE;YACHe,QAAQ,EAAE3G,MAAO;YACjByH,UAAU,EAAE;cACVC,UAAU,EAAE;gBAAEC,GAAG,EAAE,CAAC;gBAAEC,IAAI,EAAE;cAAI;YAClC;UAAE;YAAAnC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACE,CAAC,eACPtH,OAAA,CAACzB,IAAI;UAACsK,IAAI;UAACC,EAAE,EAAE,EAAG;UAACC,EAAE,EAAE,CAAE;UAAA7B,QAAA,eACvBlH,OAAA,CAACxB,WAAW;YAACwK,SAAS;YAAChG,KAAK,EAAE,CAAC,CAACpB,UAAU,CAACT,SAAU;YAAA+F,QAAA,gBACnDlH,OAAA,CAACvB,UAAU;cAAAyI,QAAA,EAAC;YAAM;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAY,CAAC,eAC/BtH,OAAA,CAACtB,MAAM;cACL+E,IAAI,EAAC,WAAW;cAChBC,KAAK,EAAE3C,QAAQ,CAACI,SAAU;cAC1B8H,QAAQ,EAAE1F,iBAAkB;cAC5B8E,QAAQ,EAAE3G,MAAM,IAAIF,aAAa,IAAImE,uBAAwB;cAAAuB,QAAA,gBAG7DlH,OAAA,CAACrB,QAAQ;gBAAC+E,KAAK,EAAC,cAAc;gBAACoD,EAAE,EAAE;kBAAE4B,UAAU,EAAE,MAAM;kBAAER,KAAK,EAAE,SAAS;kBAAEP,OAAO,EAAE;gBAAU,CAAE;gBAAAT,QAAA,EAAC;cAEjG;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAU,CAAC,eAGXtH,OAAA,CAAC1B,OAAO;gBAAA6I,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,EAGV3B,uBAAuB,gBACtB3F,OAAA,CAACrB,QAAQ;gBAAC0J,QAAQ;gBAAAnB,QAAA,eAChBlH,OAAA,CAACxC,GAAG;kBAACsJ,EAAE,EAAE;oBAAEC,OAAO,EAAE,MAAM;oBAAE4C,UAAU,EAAE;kBAAS,CAAE;kBAAAzC,QAAA,gBACjDlH,OAAA,CAAC7B,gBAAgB;oBAACuJ,IAAI,EAAE,EAAG;oBAACZ,EAAE,EAAE;sBAAE8C,EAAE,EAAE;oBAAE;kBAAE;oBAAAzC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE,CAAC,eAC7CtH,OAAA,CAACvC,UAAU;oBAAC0K,OAAO,EAAC,SAAS;oBAAAjB,QAAA,EAAC;kBAE9B;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAY,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACV;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACE,CAAC,GACT7B,gBAAgB,CAACd,MAAM,KAAK,CAAC,gBAC/B3E,OAAA,CAACrB,QAAQ;gBAAC0J,QAAQ;gBAAAnB,QAAA,eAChBlH,OAAA,CAACvC,UAAU;kBAAC0K,OAAO,EAAC,SAAS;kBAACD,KAAK,EAAC,gBAAgB;kBAAAhB,QAAA,EAAC;gBAErD;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAY;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACL,CAAC,gBAEXtH,OAAA,CAACrB,QAAQ;gBAAC0J,QAAQ;gBAAAnB,QAAA,eAChBlH,OAAA,CAACvC,UAAU;kBAAC0K,OAAO,EAAC,SAAS;kBAAAjB,QAAA,GAAC,sBACR,EAACzB,gBAAgB,CAACd,MAAM,EAAC,GAC/C;gBAAA;kBAAAwC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAY;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACL,CACX,EAGA,CAAC3B,uBAAuB,IAAIF,gBAAgB,CAACmC,GAAG,CAAEvF,MAAM,iBACvDrC,OAAA,CAACrB,QAAQ;gBAAwB+E,KAAK,EAAErB,MAAM,CAAClB,SAAU;gBAAA+F,QAAA,GACtD7E,MAAM,CAAClB,SAAS,EAAC,KAAG,EAACkB,MAAM,CAACyC,SAAS,EAAC,KAAG,EAACzC,MAAM,CAACkC,aAAa,EAAC,GAClE;cAAA,GAFelC,MAAM,CAAClB,SAAS;gBAAAgG,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAErB,CACX,CAAC,EAID,CAAC,MAAM;gBACN,MAAMuC,mBAAmB,GAAGlJ,MAAM,CAACoC,MAAM,CAACV,MAAM,IAAI;kBAClD;kBACA,MAAMyH,cAAc,GAAGrE,gBAAgB,CAACsE,IAAI,CAACzF,CAAC,IAAIA,CAAC,CAACnD,SAAS,KAAKkB,MAAM,CAAClB,SAAS,CAAC;kBACnF;kBACA,MAAMoF,WAAW,GAAGhH,kBAAkB,CAAC8C,MAAM,CAACkC,aAAa,EAAElC,MAAM,CAACmE,YAAY,CAAC;kBACjF,OAAO,CAACsD,cAAc,IAAIvD,WAAW,KAAKlH,WAAW,CAACsH,SAAS;gBACjE,CAAC,CAAC;gBAEF,IAAIkD,mBAAmB,CAAClF,MAAM,KAAK,CAAC,EAAE;kBACpC,OAAO,IAAI;gBACb;gBAEA,oBACE3E,OAAA,CAAC3C,KAAK,CAAC4C,QAAQ;kBAAAiH,QAAA,gBACblH,OAAA,CAAC1B,OAAO;oBAAA6I,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE,CAAC,eACXtH,OAAA,CAACrB,QAAQ;oBAAC0J,QAAQ;oBAAAnB,QAAA,eAChBlH,OAAA,CAACvC,UAAU;sBAAC0K,OAAO,EAAC,SAAS;sBAAAjB,QAAA,GAAC,0BACJ,EAAC2C,mBAAmB,CAAClF,MAAM,EAAC,GACtD;oBAAA;sBAAAwC,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAY;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACL,CAAC,EACVuC,mBAAmB,CAACjC,GAAG,CAAEvF,MAAM,iBAC9BrC,OAAA,CAACrB,QAAQ;oBAAwB+E,KAAK,EAAErB,MAAM,CAAClB,SAAU;oBAAA+F,QAAA,GACtD7E,MAAM,CAAClB,SAAS,EAAC,KAAG,EAACkB,MAAM,CAACyC,SAAS,EAAC,KAAG,EAACzC,MAAM,CAACkC,aAAa,EAAC,GAClE;kBAAA,GAFelC,MAAM,CAAClB,SAAS;oBAAAgG,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAErB,CACX,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACY,CAAC;cAErB,CAAC,EAAE,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACE,CAAC,EACR1F,UAAU,CAACT,SAAS,iBACnBnB,OAAA,CAACvC,UAAU;cAAC0K,OAAO,EAAC,SAAS;cAACD,KAAK,EAAC,OAAO;cAAAhB,QAAA,EACxCtF,UAAU,CAACT;YAAS;cAAAgG,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACX,CACb,EACAxF,YAAY,CAACX,SAAS,iBACrBnB,OAAA,CAACvC,UAAU;cAAC0K,OAAO,EAAC,SAAS;cAACrB,EAAE,EAAE;gBAAEoB,KAAK,EAAE;cAAS,CAAE;cAAAhB,QAAA,EACnDpF,YAAY,CAACX;YAAS;cAAAgG,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACb,CACb,eAEDtH,OAAA,CAACvC,UAAU;cAAC0K,OAAO,EAAC,SAAS;cAACD,KAAK,EAAC,gBAAgB;cAACpB,EAAE,EAAE;gBAAEyC,EAAE,EAAE;cAAE,CAAE;cAAArC,QAAA,EAAC;YAEpE;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAY,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACF;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACV,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,eAEPtH,OAAA,CAACxC,GAAG;QAACsJ,EAAE,EAAE;UAAEyC,EAAE,EAAE,CAAC;UAAExC,OAAO,EAAE,MAAM;UAAEC,cAAc,EAAE;QAAgB,CAAE;QAAAE,QAAA,gBACnElH,OAAA,CAACrC,MAAM;UACLwK,OAAO,EAAC,UAAU;UAClBD,KAAK,EAAC,WAAW;UACjBE,OAAO,EAAEA,CAAA,KAAM;YACbtH,eAAe,CAAC,IAAI,CAAC;YACrBE,WAAW,CAAC;cACVC,OAAO,EAAE,EAAE;cACXC,YAAY,EAAE,EAAE;cAChBC,SAAS,EAAE;YACb,CAAC,CAAC;UACJ,CAAE;UACFkH,QAAQ,EAAE3G,MAAO;UAAAwF,QAAA,EAClB;QAED;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC,eACTtH,OAAA,CAACrC,MAAM;UACLwK,OAAO,EAAC,WAAW;UACnBD,KAAK,EAAC,SAAS;UACfE,OAAO,EAAElD,UAAW;UACpBmD,QAAQ,EAAE3G,MAAM,IAAI+C,MAAM,CAACC,IAAI,CAAC9C,UAAU,CAAC,CAAC+C,MAAM,GAAG,CAAE;UAAAuC,QAAA,EAEtDxF,MAAM,gBAAG1B,OAAA,CAAC7B,gBAAgB;YAACuJ,IAAI,EAAE;UAAG;YAAAP,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,GAAG;QAAO;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC5C,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACN,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACD,CAAC;EAEZ,CAAC;EAED,oBACEtH,OAAA,CAACxC,GAAG;IAAA0J,QAAA,GAED,CAACrG,YAAY,IAAIgG,eAAe,CAAC,CAAC,EAGlCyB,UAAU,CAAC,CAAC,eAGbtI,OAAA,CAACF,sBAAsB;MACrBkK,IAAI,EAAEhI,0BAA2B;MACjCiI,OAAO,EAAEA,CAAA,KAAMhI,6BAA6B,CAAC,KAAK,CAAE;MACpDG,IAAI,EAAEF,oBAAoB,CAACE,IAAK;MAChCC,MAAM,EAAEH,oBAAoB,CAACG,MAAO;MACpC6H,YAAY,EAAE9E,gCAAiC;MAC/C+E,mBAAmB,EAAEA,CAAA,KAAM;QACzBlI,6BAA6B,CAAC,KAAK,CAAC;QACpCjB,WAAW,CAAC4C,IAAI,KAAK;UAAE,GAAGA,IAAI;UAAEzC,SAAS,EAAE;QAAG,CAAC,CAAC,CAAC;MACnD;IAAE;MAAAgG,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,eAGFtH,OAAA,CAACpB,MAAM;MAACoL,IAAI,EAAE1H,qBAAsB;MAAC2H,OAAO,EAAE3E,4BAA6B;MAAA4B,QAAA,gBACzElH,OAAA,CAACnB,WAAW;QAAAqI,QAAA,EAAC;MAAe;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAa,CAAC,eAC1CtH,OAAA,CAAClB,aAAa;QAAAoI,QAAA,gBACZlH,OAAA,CAAChB,iBAAiB;UAAAkI,QAAA,GAAC,UACT,EAAC1E,eAAe,aAAfA,eAAe,uBAAfA,eAAe,CAAEvB,OAAO,EAAC,4BACpC;QAAA;UAAAkG,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAmB,CAAC,eACpBtH,OAAA,CAACxC,GAAG;UAACsJ,EAAE,EAAE;YAAEyC,EAAE,EAAE;UAAE,CAAE;UAAArC,QAAA,gBACjBlH,OAAA,CAACvC,UAAU;YAAC0K,OAAO,EAAC,OAAO;YAACK,YAAY;YAAAtB,QAAA,EAAC;UAEzC;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAY,CAAC,eACbtH,OAAA,CAACvC,UAAU;YAACgK,SAAS,EAAC,IAAI;YAACU,OAAO,EAAC,OAAO;YAAAjB,QAAA,gBACxClH,OAAA;cAAAkH,QAAA,EAAI;YAA8B;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eACvCtH,OAAA;cAAAkH,QAAA,EAAI;YAAyB;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eAClCtH,OAAA;cAAAkH,QAAA,EAAI;YAAsB;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACrB,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACV,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACO,CAAC,eAChBtH,OAAA,CAACjB,aAAa;QAAC+H,EAAE,EAAE;UAAEyB,CAAC,EAAE,CAAC;UAAEvB,cAAc,EAAE;QAAgB,CAAE;QAAAE,QAAA,gBAC3DlH,OAAA,CAACrC,MAAM;UAACyK,OAAO,EAAE9C,4BAA6B;UAAC4C,KAAK,EAAC,WAAW;UAAAhB,QAAA,EAAC;QAEjE;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC,eACTtH,OAAA,CAACxC,GAAG;UAAA0J,QAAA,gBACFlH,OAAA,CAACrC,MAAM;YAACyK,OAAO,EAAE7C,wBAAyB;YAAC2C,KAAK,EAAC,SAAS;YAACpB,EAAE,EAAE;cAAE8C,EAAE,EAAE;YAAE,CAAE;YAAA1C,QAAA,EAAC;UAE1E;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC,eACTtH,OAAA,CAACrC,MAAM;YAACyK,OAAO,EAAE5C,gBAAiB;YAAC2C,OAAO,EAAC,WAAW;YAACD,KAAK,EAAC,SAAS;YAAAhB,QAAA,EAAC;UAEvE;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACN,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACO,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACV,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACN,CAAC;AAEV,CAAC;AAAC/G,EAAA,CAl2BIJ,2BAA2B;EAAA,QACdlB,WAAW;AAAA;AAAAmL,EAAA,GADxBjK,2BAA2B;AAo2BjC,eAAeA,2BAA2B;AAAC,IAAAiK,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}