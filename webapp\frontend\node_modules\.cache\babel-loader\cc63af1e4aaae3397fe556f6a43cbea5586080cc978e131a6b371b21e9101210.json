{"ast": null, "code": "import { getISOWeekYear } from \"./getISOWeekYear.mjs\";\nimport { setISOWeekYear } from \"./setISOWeekYear.mjs\";\n\n/**\n * @name addISOWeekYears\n * @category ISO Week-Numbering Year Helpers\n * @summary Add the specified number of ISO week-numbering years to the given date.\n *\n * @description\n * Add the specified number of ISO week-numbering years to the given date.\n *\n * ISO week-numbering year: http://en.wikipedia.org/wiki/ISO_week_date\n *\n * @typeParam DateType - The `Date` type, the function operates on. Gets inferred from passed arguments. Allows to use extensions like [`UTCDate`](https://github.com/date-fns/utc).\n *\n * @param date - The date to be changed\n * @param amount - The amount of ISO week-numbering years to be added.\n *\n * @returns The new date with the ISO week-numbering years added\n *\n * @example\n * // Add 5 ISO week-numbering years to 2 July 2010:\n * const result = addISOWeekYears(new Date(2010, 6, 2), 5)\n * //=> Fri Jn 26 2015 00:00:00\n */\nexport function addISOWeekYears(date, amount) {\n  return setISOWeekYear(date, getISOWeekYear(date) + amount);\n}\n\n// Fallback for modularized imports:\nexport default addISOWeekYears;", "map": {"version": 3, "names": ["getISOWeekYear", "setISOWeekYear", "addISOWeekYears", "date", "amount"], "sources": ["C:/CMS/webapp/frontend/node_modules/date-fns/addISOWeekYears.mjs"], "sourcesContent": ["import { getISOWeekYear } from \"./getISOWeekYear.mjs\";\nimport { setISOWeekYear } from \"./setISOWeekYear.mjs\";\n\n/**\n * @name addISOWeekYears\n * @category ISO Week-Numbering Year Helpers\n * @summary Add the specified number of ISO week-numbering years to the given date.\n *\n * @description\n * Add the specified number of ISO week-numbering years to the given date.\n *\n * ISO week-numbering year: http://en.wikipedia.org/wiki/ISO_week_date\n *\n * @typeParam DateType - The `Date` type, the function operates on. Gets inferred from passed arguments. Allows to use extensions like [`UTCDate`](https://github.com/date-fns/utc).\n *\n * @param date - The date to be changed\n * @param amount - The amount of ISO week-numbering years to be added.\n *\n * @returns The new date with the ISO week-numbering years added\n *\n * @example\n * // Add 5 ISO week-numbering years to 2 July 2010:\n * const result = addISOWeekYears(new Date(2010, 6, 2), 5)\n * //=> Fri Jn 26 2015 00:00:00\n */\nexport function addISOWeekYears(date, amount) {\n  return setISOWeekYear(date, getISOWeekYear(date) + amount);\n}\n\n// Fallback for modularized imports:\nexport default addISOWeekYears;\n"], "mappings": "AAAA,SAASA,cAAc,QAAQ,sBAAsB;AACrD,SAASC,cAAc,QAAQ,sBAAsB;;AAErD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO,SAASC,eAAeA,CAACC,IAAI,EAAEC,MAAM,EAAE;EAC5C,OAAOH,cAAc,CAACE,IAAI,EAAEH,cAAc,CAACG,IAAI,CAAC,GAAGC,MAAM,CAAC;AAC5D;;AAEA;AACA,eAAeF,eAAe", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}