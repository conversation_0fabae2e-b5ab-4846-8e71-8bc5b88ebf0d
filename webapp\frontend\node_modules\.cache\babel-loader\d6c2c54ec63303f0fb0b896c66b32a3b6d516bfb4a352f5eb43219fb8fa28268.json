{"ast": null, "code": "import { formatDistance } from \"./id/_lib/formatDistance.mjs\";\nimport { formatLong } from \"./id/_lib/formatLong.mjs\";\nimport { formatRelative } from \"./id/_lib/formatRelative.mjs\";\nimport { localize } from \"./id/_lib/localize.mjs\";\nimport { match } from \"./id/_lib/match.mjs\";\n\n/**\n * @category Locales\n * @summary Indonesian locale.\n * @language Indonesian\n * @iso-639-2 ind\n * <AUTHOR> [@rbudiharso](https://github.com/rbudiharso)\n * <AUTHOR> Nata [@bentinata](https://github.com/bentinata)\n * <AUTHOR> [@deerawan](https://github.com/deerawan)\n * <AUTHOR> Ajitiono [@imballinst](https://github.com/imballinst)\n */\nexport const id = {\n  code: \"id\",\n  formatDistance: formatDistance,\n  formatLong: formatLong,\n  formatRelative: formatRelative,\n  localize: localize,\n  match: match,\n  options: {\n    weekStartsOn: 1 /* Monday */,\n    firstWeekContainsDate: 1\n  }\n};\n\n// Fallback for modularized imports:\nexport default id;", "map": {"version": 3, "names": ["formatDistance", "formatLong", "formatRelative", "localize", "match", "id", "code", "options", "weekStartsOn", "firstWeekContainsDate"], "sources": ["C:/CMS/webapp/frontend/node_modules/date-fns/locale/id.mjs"], "sourcesContent": ["import { formatDistance } from \"./id/_lib/formatDistance.mjs\";\nimport { formatLong } from \"./id/_lib/formatLong.mjs\";\nimport { formatRelative } from \"./id/_lib/formatRelative.mjs\";\nimport { localize } from \"./id/_lib/localize.mjs\";\nimport { match } from \"./id/_lib/match.mjs\";\n\n/**\n * @category Locales\n * @summary Indonesian locale.\n * @language Indonesian\n * @iso-639-2 ind\n * <AUTHOR> [@rbudiharso](https://github.com/rbudiharso)\n * <AUTHOR> Nata [@bentinata](https://github.com/bentinata)\n * <AUTHOR> [@deerawan](https://github.com/deerawan)\n * <AUTHOR> Ajitiono [@imballinst](https://github.com/imballinst)\n */\nexport const id = {\n  code: \"id\",\n  formatDistance: formatDistance,\n  formatLong: formatLong,\n  formatRelative: formatRelative,\n  localize: localize,\n  match: match,\n  options: {\n    weekStartsOn: 1 /* Monday */,\n    firstWeekContainsDate: 1,\n  },\n};\n\n// Fallback for modularized imports:\nexport default id;\n"], "mappings": "AAAA,SAASA,cAAc,QAAQ,8BAA8B;AAC7D,SAASC,UAAU,QAAQ,0BAA0B;AACrD,SAASC,cAAc,QAAQ,8BAA8B;AAC7D,SAASC,QAAQ,QAAQ,wBAAwB;AACjD,SAASC,KAAK,QAAQ,qBAAqB;;AAE3C;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO,MAAMC,EAAE,GAAG;EAChBC,IAAI,EAAE,IAAI;EACVN,cAAc,EAAEA,cAAc;EAC9BC,UAAU,EAAEA,UAAU;EACtBC,cAAc,EAAEA,cAAc;EAC9BC,QAAQ,EAAEA,QAAQ;EAClBC,KAAK,EAAEA,KAAK;EACZG,OAAO,EAAE;IACPC,YAAY,EAAE,CAAC,CAAC;IAChBC,qBAAqB,EAAE;EACzB;AACF,CAAC;;AAED;AACA,eAAeJ,EAAE", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}