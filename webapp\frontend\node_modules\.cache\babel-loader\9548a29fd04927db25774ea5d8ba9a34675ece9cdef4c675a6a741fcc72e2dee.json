{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\PycharmProjects\\\\CMS\\\\webapp\\\\frontend\\\\src\\\\components\\\\common\\\\AdminHomeButton.js\",\n  _s = $RefreshSig$();\nimport React from 'react';\nimport { Button, Tooltip } from '@mui/material';\nimport { Home as HomeIcon } from '@mui/icons-material';\nimport { useNavigate } from 'react-router-dom';\nimport { useAuth } from '../../context/AuthContext';\n\n/**\n * Componente che mostra un pulsante per tornare al pannello admin\n * Visibile solo quando un amministratore sta impersonando un utente\n */\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst AdminHomeButton = () => {\n  _s();\n  const {\n    isImpersonating\n  } = useAuth();\n  const navigate = useNavigate();\n\n  // Se l'utente non è un amministratore che sta impersonando un utente, non mostrare il pulsante\n  if (!isImpersonating) {\n    return null;\n  }\n\n  // Naviga al pannello amministratore\n  const handleBackToAdmin = () => {\n    navigate('/dashboard/admin');\n  };\n  return /*#__PURE__*/_jsxDEV(Tooltip, {\n    title: \"Torna al pannello amministratore\",\n    children: /*#__PURE__*/_jsxDEV(Button, {\n      variant: \"outlined\",\n      color: \"primary\",\n      startIcon: /*#__PURE__*/_jsxDEV(HomeIcon, {}, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 30,\n        columnNumber: 20\n      }, this),\n      onClick: handleBackToAdmin,\n      size: \"small\",\n      children: \"Pannello Admin\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 27,\n      columnNumber: 7\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 26,\n    columnNumber: 5\n  }, this);\n};\n_s(AdminHomeButton, \"2LrHI1hw5pqPbj9zv+06AB4ZF78=\", false, function () {\n  return [useAuth, useNavigate];\n});\n_c = AdminHomeButton;\nexport default AdminHomeButton;\nvar _c;\n$RefreshReg$(_c, \"AdminHomeButton\");", "map": {"version": 3, "names": ["React", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "Home", "HomeIcon", "useNavigate", "useAuth", "jsxDEV", "_jsxDEV", "AdminHomeButton", "_s", "isImpersonating", "navigate", "handleBackToAdmin", "title", "children", "variant", "color", "startIcon", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "onClick", "size", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/PycharmProjects/CMS/webapp/frontend/src/components/common/AdminHomeButton.js"], "sourcesContent": ["import React from 'react';\nimport { Button, Tooltip } from '@mui/material';\nimport { Home as HomeIcon } from '@mui/icons-material';\nimport { useNavigate } from 'react-router-dom';\nimport { useAuth } from '../../context/AuthContext';\n\n/**\n * Componente che mostra un pulsante per tornare al pannello admin\n * Visibile solo quando un amministratore sta impersonando un utente\n */\nconst AdminHomeButton = () => {\n  const { isImpersonating } = useAuth();\n  const navigate = useNavigate();\n\n  // Se l'utente non è un amministratore che sta impersonando un utente, non mostrare il pulsante\n  if (!isImpersonating) {\n    return null;\n  }\n\n  // Naviga al pannello amministratore\n  const handleBackToAdmin = () => {\n    navigate('/dashboard/admin');\n  };\n\n  return (\n    <Tooltip title=\"Torna al pannello amministratore\">\n      <Button\n        variant=\"outlined\"\n        color=\"primary\"\n        startIcon={<HomeIcon />}\n        onClick={handleBackToAdmin}\n        size=\"small\"\n      >\n        Pannello Admin\n      </Button>\n    </Tooltip>\n  );\n};\n\nexport default AdminHomeButton;\n"], "mappings": ";;AAAA,OAAOA,KAAK,MAAM,OAAO;AACzB,SAASC,MAAM,EAAEC,OAAO,QAAQ,eAAe;AAC/C,SAASC,IAAI,IAAIC,QAAQ,QAAQ,qBAAqB;AACtD,SAASC,WAAW,QAAQ,kBAAkB;AAC9C,SAASC,OAAO,QAAQ,2BAA2B;;AAEnD;AACA;AACA;AACA;AAHA,SAAAC,MAAA,IAAAC,OAAA;AAIA,MAAMC,eAAe,GAAGA,CAAA,KAAM;EAAAC,EAAA;EAC5B,MAAM;IAAEC;EAAgB,CAAC,GAAGL,OAAO,CAAC,CAAC;EACrC,MAAMM,QAAQ,GAAGP,WAAW,CAAC,CAAC;;EAE9B;EACA,IAAI,CAACM,eAAe,EAAE;IACpB,OAAO,IAAI;EACb;;EAEA;EACA,MAAME,iBAAiB,GAAGA,CAAA,KAAM;IAC9BD,QAAQ,CAAC,kBAAkB,CAAC;EAC9B,CAAC;EAED,oBACEJ,OAAA,CAACN,OAAO;IAACY,KAAK,EAAC,kCAAkC;IAAAC,QAAA,eAC/CP,OAAA,CAACP,MAAM;MACLe,OAAO,EAAC,UAAU;MAClBC,KAAK,EAAC,SAAS;MACfC,SAAS,eAAEV,OAAA,CAACJ,QAAQ;QAAAe,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAE;MACxBC,OAAO,EAAEV,iBAAkB;MAC3BW,IAAI,EAAC,OAAO;MAAAT,QAAA,EACb;IAED;MAAAI,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAQ;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACF,CAAC;AAEd,CAAC;AAACZ,EAAA,CA3BID,eAAe;EAAA,QACSH,OAAO,EAClBD,WAAW;AAAA;AAAAoB,EAAA,GAFxBhB,eAAe;AA6BrB,eAAeA,eAAe;AAAC,IAAAgB,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}