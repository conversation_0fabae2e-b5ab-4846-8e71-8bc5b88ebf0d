{"ast": null, "code": "function _typeof(o) {\n  \"@babel/helpers - typeof\";\n\n  return _typeof = \"function\" == typeof Symbol && \"symbol\" == typeof Symbol.iterator ? function (o) {\n    return typeof o;\n  } : function (o) {\n    return o && \"function\" == typeof Symbol && o.constructor === Symbol && o !== Symbol.prototype ? \"symbol\" : typeof o;\n  }, _typeof(o);\n}\nfunction _toConsumableArray(arr) {\n  return _arrayWithoutHoles(arr) || _iterableToArray(arr) || _unsupportedIterableToArray(arr) || _nonIterableSpread();\n}\nfunction _nonIterableSpread() {\n  throw new TypeError(\"Invalid attempt to spread non-iterable instance.\\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.\");\n}\nfunction _unsupportedIterableToArray(o, minLen) {\n  if (!o) return;\n  if (typeof o === \"string\") return _arrayLikeToArray(o, minLen);\n  var n = Object.prototype.toString.call(o).slice(8, -1);\n  if (n === \"Object\" && o.constructor) n = o.constructor.name;\n  if (n === \"Map\" || n === \"Set\") return Array.from(o);\n  if (n === \"Arguments\" || /^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n)) return _arrayLikeToArray(o, minLen);\n}\nfunction _iterableToArray(iter) {\n  if (typeof Symbol !== \"undefined\" && iter[Symbol.iterator] != null || iter[\"@@iterator\"] != null) return Array.from(iter);\n}\nfunction _arrayWithoutHoles(arr) {\n  if (Array.isArray(arr)) return _arrayLikeToArray(arr);\n}\nfunction _arrayLikeToArray(arr, len) {\n  if (len == null || len > arr.length) len = arr.length;\n  for (var i = 0, arr2 = new Array(len); i < len; i++) arr2[i] = arr[i];\n  return arr2;\n}\nfunction ownKeys(e, r) {\n  var t = Object.keys(e);\n  if (Object.getOwnPropertySymbols) {\n    var o = Object.getOwnPropertySymbols(e);\n    r && (o = o.filter(function (r) {\n      return Object.getOwnPropertyDescriptor(e, r).enumerable;\n    })), t.push.apply(t, o);\n  }\n  return t;\n}\nfunction _objectSpread(e) {\n  for (var r = 1; r < arguments.length; r++) {\n    var t = null != arguments[r] ? arguments[r] : {};\n    r % 2 ? ownKeys(Object(t), !0).forEach(function (r) {\n      _defineProperty(e, r, t[r]);\n    }) : Object.getOwnPropertyDescriptors ? Object.defineProperties(e, Object.getOwnPropertyDescriptors(t)) : ownKeys(Object(t)).forEach(function (r) {\n      Object.defineProperty(e, r, Object.getOwnPropertyDescriptor(t, r));\n    });\n  }\n  return e;\n}\nfunction _defineProperty(obj, key, value) {\n  key = _toPropertyKey(key);\n  if (key in obj) {\n    Object.defineProperty(obj, key, {\n      value: value,\n      enumerable: true,\n      configurable: true,\n      writable: true\n    });\n  } else {\n    obj[key] = value;\n  }\n  return obj;\n}\nfunction _toPropertyKey(t) {\n  var i = _toPrimitive(t, \"string\");\n  return \"symbol\" == _typeof(i) ? i : i + \"\";\n}\nfunction _toPrimitive(t, r) {\n  if (\"object\" != _typeof(t) || !t) return t;\n  var e = t[Symbol.toPrimitive];\n  if (void 0 !== e) {\n    var i = e.call(t, r || \"default\");\n    if (\"object\" != _typeof(i)) return i;\n    throw new TypeError(\"@@toPrimitive must return a primitive value.\");\n  }\n  return (\"string\" === r ? String : Number)(t);\n}\nimport * as d3Scales from 'victory-vendor/d3-scale';\nimport { stack as shapeStack, stackOffsetExpand, stackOffsetNone, stackOffsetSilhouette, stackOffsetWiggle, stackOrderNone } from 'victory-vendor/d3-shape';\nimport max from 'lodash/max';\nimport min from 'lodash/min';\nimport isNil from 'lodash/isNil';\nimport isFunction from 'lodash/isFunction';\nimport isString from 'lodash/isString';\nimport get from 'lodash/get';\nimport flatMap from 'lodash/flatMap';\nimport isNan from 'lodash/isNaN';\nimport upperFirst from 'lodash/upperFirst';\nimport isEqual from 'lodash/isEqual';\nimport sortBy from 'lodash/sortBy';\nimport { getNiceTickValues, getTickValuesFixedDomain } from 'recharts-scale';\nimport { ErrorBar } from '../cartesian/ErrorBar';\nimport { findEntryInArray, getPercentValue, isNumber, isNumOrStr, mathSign, uniqueId } from './DataUtils';\nimport { filterProps, findAllByType, getDisplayName } from './ReactUtils';\n// TODO: Cause of circular dependency. Needs refactor.\n// import { RadiusAxisProps, AngleAxisProps } from '../polar/types';\n\nimport { getLegendProps } from './getLegendProps';\n\n// Exported for backwards compatibility\nexport { getLegendProps };\nexport function getValueByDataKey(obj, dataKey, defaultValue) {\n  if (isNil(obj) || isNil(dataKey)) {\n    return defaultValue;\n  }\n  if (isNumOrStr(dataKey)) {\n    return get(obj, dataKey, defaultValue);\n  }\n  if (isFunction(dataKey)) {\n    return dataKey(obj);\n  }\n  return defaultValue;\n}\n/**\n * Get domain of data by key.\n * @param  {Array}   data      The data displayed in the chart\n * @param  {String}  key       The unique key of a group of data\n * @param  {String}  type      The type of axis\n * @param  {Boolean} filterNil Whether or not filter nil values\n * @return {Array} Domain of data\n */\nexport function getDomainOfDataByKey(data, key, type, filterNil) {\n  var flattenData = flatMap(data, function (entry) {\n    return getValueByDataKey(entry, key);\n  });\n  if (type === 'number') {\n    // @ts-expect-error parseFloat type only accepts strings\n    var domain = flattenData.filter(function (entry) {\n      return isNumber(entry) || parseFloat(entry);\n    });\n    return domain.length ? [min(domain), max(domain)] : [Infinity, -Infinity];\n  }\n  var validateData = filterNil ? flattenData.filter(function (entry) {\n    return !isNil(entry);\n  }) : flattenData;\n\n  // Supports x-axis of Date type\n  return validateData.map(function (entry) {\n    return isNumOrStr(entry) || entry instanceof Date ? entry : '';\n  });\n}\nexport var calculateActiveTickIndex = function calculateActiveTickIndex(coordinate) {\n  var _ticks$length;\n  var ticks = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : [];\n  var unsortedTicks = arguments.length > 2 ? arguments[2] : undefined;\n  var axis = arguments.length > 3 ? arguments[3] : undefined;\n  var index = -1;\n  var len = (_ticks$length = ticks === null || ticks === void 0 ? void 0 : ticks.length) !== null && _ticks$length !== void 0 ? _ticks$length : 0;\n\n  // if there are 1 or less ticks ticks then the active tick is at index 0\n  if (len <= 1) {\n    return 0;\n  }\n  if (axis && axis.axisType === 'angleAxis' && Math.abs(Math.abs(axis.range[1] - axis.range[0]) - 360) <= 1e-6) {\n    var range = axis.range;\n    // ticks are distributed in a circle\n    for (var i = 0; i < len; i++) {\n      var before = i > 0 ? unsortedTicks[i - 1].coordinate : unsortedTicks[len - 1].coordinate;\n      var cur = unsortedTicks[i].coordinate;\n      var after = i >= len - 1 ? unsortedTicks[0].coordinate : unsortedTicks[i + 1].coordinate;\n      var sameDirectionCoord = void 0;\n      if (mathSign(cur - before) !== mathSign(after - cur)) {\n        var diffInterval = [];\n        if (mathSign(after - cur) === mathSign(range[1] - range[0])) {\n          sameDirectionCoord = after;\n          var curInRange = cur + range[1] - range[0];\n          diffInterval[0] = Math.min(curInRange, (curInRange + before) / 2);\n          diffInterval[1] = Math.max(curInRange, (curInRange + before) / 2);\n        } else {\n          sameDirectionCoord = before;\n          var afterInRange = after + range[1] - range[0];\n          diffInterval[0] = Math.min(cur, (afterInRange + cur) / 2);\n          diffInterval[1] = Math.max(cur, (afterInRange + cur) / 2);\n        }\n        var sameInterval = [Math.min(cur, (sameDirectionCoord + cur) / 2), Math.max(cur, (sameDirectionCoord + cur) / 2)];\n        if (coordinate > sameInterval[0] && coordinate <= sameInterval[1] || coordinate >= diffInterval[0] && coordinate <= diffInterval[1]) {\n          index = unsortedTicks[i].index;\n          break;\n        }\n      } else {\n        var minValue = Math.min(before, after);\n        var maxValue = Math.max(before, after);\n        if (coordinate > (minValue + cur) / 2 && coordinate <= (maxValue + cur) / 2) {\n          index = unsortedTicks[i].index;\n          break;\n        }\n      }\n    }\n  } else {\n    // ticks are distributed in a single direction\n    for (var _i = 0; _i < len; _i++) {\n      if (_i === 0 && coordinate <= (ticks[_i].coordinate + ticks[_i + 1].coordinate) / 2 || _i > 0 && _i < len - 1 && coordinate > (ticks[_i].coordinate + ticks[_i - 1].coordinate) / 2 && coordinate <= (ticks[_i].coordinate + ticks[_i + 1].coordinate) / 2 || _i === len - 1 && coordinate > (ticks[_i].coordinate + ticks[_i - 1].coordinate) / 2) {\n        index = ticks[_i].index;\n        break;\n      }\n    }\n  }\n  return index;\n};\n\n/**\n * Get the main color of each graphic item\n * @param  {ReactElement} item A graphic item\n * @return {String}            Color\n */\nexport var getMainColorOfGraphicItem = function getMainColorOfGraphicItem(item) {\n  var _item$type;\n  var _ref = item,\n    displayName = _ref.type.displayName; // TODO: check if displayName is valid.\n  var defaultedProps = (_item$type = item.type) !== null && _item$type !== void 0 && _item$type.defaultProps ? _objectSpread(_objectSpread({}, item.type.defaultProps), item.props) : item.props;\n  var stroke = defaultedProps.stroke,\n    fill = defaultedProps.fill;\n  var result;\n  switch (displayName) {\n    case 'Line':\n      result = stroke;\n      break;\n    case 'Area':\n    case 'Radar':\n      result = stroke && stroke !== 'none' ? stroke : fill;\n      break;\n    default:\n      result = fill;\n      break;\n  }\n  return result;\n};\n/**\n * Calculate the size of all groups for stacked bar graph\n * @param  {Object} stackGroups The items grouped by axisId and stackId\n * @return {Object} The size of all groups\n */\nexport var getBarSizeList = function getBarSizeList(_ref2) {\n  var globalSize = _ref2.barSize,\n    totalSize = _ref2.totalSize,\n    _ref2$stackGroups = _ref2.stackGroups,\n    stackGroups = _ref2$stackGroups === void 0 ? {} : _ref2$stackGroups;\n  if (!stackGroups) {\n    return {};\n  }\n  var result = {};\n  var numericAxisIds = Object.keys(stackGroups);\n  for (var i = 0, len = numericAxisIds.length; i < len; i++) {\n    var sgs = stackGroups[numericAxisIds[i]].stackGroups;\n    var stackIds = Object.keys(sgs);\n    for (var j = 0, sLen = stackIds.length; j < sLen; j++) {\n      var _sgs$stackIds$j = sgs[stackIds[j]],\n        items = _sgs$stackIds$j.items,\n        cateAxisId = _sgs$stackIds$j.cateAxisId;\n      var barItems = items.filter(function (item) {\n        return getDisplayName(item.type).indexOf('Bar') >= 0;\n      });\n      if (barItems && barItems.length) {\n        var barItemDefaultProps = barItems[0].type.defaultProps;\n        var barItemProps = barItemDefaultProps !== undefined ? _objectSpread(_objectSpread({}, barItemDefaultProps), barItems[0].props) : barItems[0].props;\n        var selfSize = barItemProps.barSize;\n        var cateId = barItemProps[cateAxisId];\n        if (!result[cateId]) {\n          result[cateId] = [];\n        }\n        var barSize = isNil(selfSize) ? globalSize : selfSize;\n        result[cateId].push({\n          item: barItems[0],\n          stackList: barItems.slice(1),\n          barSize: isNil(barSize) ? undefined : getPercentValue(barSize, totalSize, 0)\n        });\n      }\n    }\n  }\n  return result;\n};\n/**\n * Calculate the size of each bar and offset between start of band and the bar\n *\n * @param  {number} bandSize is the size of area where bars can render\n * @param  {number | string} barGap is the gap size, as a percentage of `bandSize`.\n *                                  Can be defined as number or percent string\n * @param  {number | string} barCategoryGap is the gap size, as a percentage of `bandSize`.\n *                                  Can be defined as number or percent string\n * @param  {Array<object>} sizeList Sizes of all groups\n * @param  {number} maxBarSize The maximum size of each bar\n * @return {Array<object>} The size and offset of each bar\n */\nexport var getBarPosition = function getBarPosition(_ref3) {\n  var barGap = _ref3.barGap,\n    barCategoryGap = _ref3.barCategoryGap,\n    bandSize = _ref3.bandSize,\n    _ref3$sizeList = _ref3.sizeList,\n    sizeList = _ref3$sizeList === void 0 ? [] : _ref3$sizeList,\n    maxBarSize = _ref3.maxBarSize;\n  var len = sizeList.length;\n  if (len < 1) return null;\n  var realBarGap = getPercentValue(barGap, bandSize, 0, true);\n  var result;\n  var initialValue = [];\n\n  // whether or not is barSize setted by user\n  if (sizeList[0].barSize === +sizeList[0].barSize) {\n    var useFull = false;\n    var fullBarSize = bandSize / len;\n    // @ts-expect-error the type check above does not check for type number explicitly\n    var sum = sizeList.reduce(function (res, entry) {\n      return res + entry.barSize || 0;\n    }, 0);\n    sum += (len - 1) * realBarGap;\n    if (sum >= bandSize) {\n      sum -= (len - 1) * realBarGap;\n      realBarGap = 0;\n    }\n    if (sum >= bandSize && fullBarSize > 0) {\n      useFull = true;\n      fullBarSize *= 0.9;\n      sum = len * fullBarSize;\n    }\n    var offset = (bandSize - sum) / 2 >> 0;\n    var prev = {\n      offset: offset - realBarGap,\n      size: 0\n    };\n    result = sizeList.reduce(function (res, entry) {\n      var newPosition = {\n        item: entry.item,\n        position: {\n          offset: prev.offset + prev.size + realBarGap,\n          // @ts-expect-error the type check above does not check for type number explicitly\n          size: useFull ? fullBarSize : entry.barSize\n        }\n      };\n      var newRes = [].concat(_toConsumableArray(res), [newPosition]);\n      prev = newRes[newRes.length - 1].position;\n      if (entry.stackList && entry.stackList.length) {\n        entry.stackList.forEach(function (item) {\n          newRes.push({\n            item: item,\n            position: prev\n          });\n        });\n      }\n      return newRes;\n    }, initialValue);\n  } else {\n    var _offset = getPercentValue(barCategoryGap, bandSize, 0, true);\n    if (bandSize - 2 * _offset - (len - 1) * realBarGap <= 0) {\n      realBarGap = 0;\n    }\n    var originalSize = (bandSize - 2 * _offset - (len - 1) * realBarGap) / len;\n    if (originalSize > 1) {\n      originalSize >>= 0;\n    }\n    var size = maxBarSize === +maxBarSize ? Math.min(originalSize, maxBarSize) : originalSize;\n    result = sizeList.reduce(function (res, entry, i) {\n      var newRes = [].concat(_toConsumableArray(res), [{\n        item: entry.item,\n        position: {\n          offset: _offset + (originalSize + realBarGap) * i + (originalSize - size) / 2,\n          size: size\n        }\n      }]);\n      if (entry.stackList && entry.stackList.length) {\n        entry.stackList.forEach(function (item) {\n          newRes.push({\n            item: item,\n            position: newRes[newRes.length - 1].position\n          });\n        });\n      }\n      return newRes;\n    }, initialValue);\n  }\n  return result;\n};\nexport var appendOffsetOfLegend = function appendOffsetOfLegend(offset, _unused, props, legendBox) {\n  var children = props.children,\n    width = props.width,\n    margin = props.margin;\n  var legendWidth = width - (margin.left || 0) - (margin.right || 0);\n  var legendProps = getLegendProps({\n    children: children,\n    legendWidth: legendWidth\n  });\n  if (legendProps) {\n    var _ref4 = legendBox || {},\n      boxWidth = _ref4.width,\n      boxHeight = _ref4.height;\n    var align = legendProps.align,\n      verticalAlign = legendProps.verticalAlign,\n      layout = legendProps.layout;\n    if ((layout === 'vertical' || layout === 'horizontal' && verticalAlign === 'middle') && align !== 'center' && isNumber(offset[align])) {\n      return _objectSpread(_objectSpread({}, offset), {}, _defineProperty({}, align, offset[align] + (boxWidth || 0)));\n    }\n    if ((layout === 'horizontal' || layout === 'vertical' && align === 'center') && verticalAlign !== 'middle' && isNumber(offset[verticalAlign])) {\n      return _objectSpread(_objectSpread({}, offset), {}, _defineProperty({}, verticalAlign, offset[verticalAlign] + (boxHeight || 0)));\n    }\n  }\n  return offset;\n};\nvar isErrorBarRelevantForAxis = function isErrorBarRelevantForAxis(layout, axisType, direction) {\n  if (isNil(axisType)) {\n    return true;\n  }\n  if (layout === 'horizontal') {\n    return axisType === 'yAxis';\n  }\n  if (layout === 'vertical') {\n    return axisType === 'xAxis';\n  }\n  if (direction === 'x') {\n    return axisType === 'xAxis';\n  }\n  if (direction === 'y') {\n    return axisType === 'yAxis';\n  }\n  return true;\n};\nexport var getDomainOfErrorBars = function getDomainOfErrorBars(data, item, dataKey, layout, axisType) {\n  var children = item.props.children;\n  var errorBars = findAllByType(children, ErrorBar).filter(function (errorBarChild) {\n    return isErrorBarRelevantForAxis(layout, axisType, errorBarChild.props.direction);\n  });\n  if (errorBars && errorBars.length) {\n    var keys = errorBars.map(function (errorBarChild) {\n      return errorBarChild.props.dataKey;\n    });\n    return data.reduce(function (result, entry) {\n      var entryValue = getValueByDataKey(entry, dataKey);\n      if (isNil(entryValue)) return result;\n      var mainValue = Array.isArray(entryValue) ? [min(entryValue), max(entryValue)] : [entryValue, entryValue];\n      var errorDomain = keys.reduce(function (prevErrorArr, k) {\n        var errorValue = getValueByDataKey(entry, k, 0);\n        var lowerValue = mainValue[0] - Math.abs(Array.isArray(errorValue) ? errorValue[0] : errorValue);\n        var upperValue = mainValue[1] + Math.abs(Array.isArray(errorValue) ? errorValue[1] : errorValue);\n        return [Math.min(lowerValue, prevErrorArr[0]), Math.max(upperValue, prevErrorArr[1])];\n      }, [Infinity, -Infinity]);\n      return [Math.min(errorDomain[0], result[0]), Math.max(errorDomain[1], result[1])];\n    }, [Infinity, -Infinity]);\n  }\n  return null;\n};\nexport var parseErrorBarsOfAxis = function parseErrorBarsOfAxis(data, items, dataKey, axisType, layout) {\n  var domains = items.map(function (item) {\n    return getDomainOfErrorBars(data, item, dataKey, layout, axisType);\n  }).filter(function (entry) {\n    return !isNil(entry);\n  });\n  if (domains && domains.length) {\n    return domains.reduce(function (result, entry) {\n      return [Math.min(result[0], entry[0]), Math.max(result[1], entry[1])];\n    }, [Infinity, -Infinity]);\n  }\n  return null;\n};\n\n/**\n * Get domain of data by the configuration of item element\n * @param  {Array}   data      The data displayed in the chart\n * @param  {Array}   items     The instances of item\n * @param  {String}  type      The type of axis, number - Number Axis, category - Category Axis\n * @param  {LayoutType} layout The type of layout\n * @param  {Boolean} filterNil Whether or not filter nil values\n * @return {Array}        Domain\n */\nexport var getDomainOfItemsWithSameAxis = function getDomainOfItemsWithSameAxis(data, items, type, layout, filterNil) {\n  var domains = items.map(function (item) {\n    var dataKey = item.props.dataKey;\n    if (type === 'number' && dataKey) {\n      return getDomainOfErrorBars(data, item, dataKey, layout) || getDomainOfDataByKey(data, dataKey, type, filterNil);\n    }\n    return getDomainOfDataByKey(data, dataKey, type, filterNil);\n  });\n  if (type === 'number') {\n    // Calculate the domain of number axis\n    return domains.reduce(\n    // @ts-expect-error if (type === number) means that the domain is numerical type\n    // - but this link is missing in the type definition\n    function (result, entry) {\n      return [Math.min(result[0], entry[0]), Math.max(result[1], entry[1])];\n    }, [Infinity, -Infinity]);\n  }\n  var tag = {};\n  // Get the union set of category axis\n  return domains.reduce(function (result, entry) {\n    for (var i = 0, len = entry.length; i < len; i++) {\n      // @ts-expect-error Date cannot index an object\n      if (!tag[entry[i]]) {\n        // @ts-expect-error Date cannot index an object\n        tag[entry[i]] = true;\n\n        // @ts-expect-error Date cannot index an object\n        result.push(entry[i]);\n      }\n    }\n    return result;\n  }, []);\n};\nexport var isCategoricalAxis = function isCategoricalAxis(layout, axisType) {\n  return layout === 'horizontal' && axisType === 'xAxis' || layout === 'vertical' && axisType === 'yAxis' || layout === 'centric' && axisType === 'angleAxis' || layout === 'radial' && axisType === 'radiusAxis';\n};\n\n/**\n * Calculate the Coordinates of grid\n * @param  {Array} ticks           The ticks in axis\n * @param {Number} minValue        The minimun value of axis\n * @param {Number} maxValue        The maximun value of axis\n * @param {boolean} syncWithTicks  Synchronize grid lines with ticks or not\n * @return {Array}                 Coordinates\n */\nexport var getCoordinatesOfGrid = function getCoordinatesOfGrid(ticks, minValue, maxValue, syncWithTicks) {\n  if (syncWithTicks) {\n    return ticks.map(function (entry) {\n      return entry.coordinate;\n    });\n  }\n  var hasMin, hasMax;\n  var values = ticks.map(function (entry) {\n    if (entry.coordinate === minValue) {\n      hasMin = true;\n    }\n    if (entry.coordinate === maxValue) {\n      hasMax = true;\n    }\n    return entry.coordinate;\n  });\n  if (!hasMin) {\n    values.push(minValue);\n  }\n  if (!hasMax) {\n    values.push(maxValue);\n  }\n  return values;\n};\n\n/**\n * Get the ticks of an axis\n * @param  {Object}  axis The configuration of an axis\n * @param {Boolean} isGrid Whether or not are the ticks in grid\n * @param {Boolean} isAll Return the ticks of all the points or not\n * @return {Array}  Ticks\n */\nexport var getTicksOfAxis = function getTicksOfAxis(axis, isGrid, isAll) {\n  if (!axis) return null;\n  var scale = axis.scale;\n  var duplicateDomain = axis.duplicateDomain,\n    type = axis.type,\n    range = axis.range;\n  var offsetForBand = axis.realScaleType === 'scaleBand' ? scale.bandwidth() / 2 : 2;\n  var offset = (isGrid || isAll) && type === 'category' && scale.bandwidth ? scale.bandwidth() / offsetForBand : 0;\n  offset = axis.axisType === 'angleAxis' && (range === null || range === void 0 ? void 0 : range.length) >= 2 ? mathSign(range[0] - range[1]) * 2 * offset : offset;\n\n  // The ticks set by user should only affect the ticks adjacent to axis line\n  if (isGrid && (axis.ticks || axis.niceTicks)) {\n    var result = (axis.ticks || axis.niceTicks).map(function (entry) {\n      var scaleContent = duplicateDomain ? duplicateDomain.indexOf(entry) : entry;\n      return {\n        // If the scaleContent is not a number, the coordinate will be NaN.\n        // That could be the case for example with a PointScale and a string as domain.\n        coordinate: scale(scaleContent) + offset,\n        value: entry,\n        offset: offset\n      };\n    });\n    return result.filter(function (row) {\n      return !isNan(row.coordinate);\n    });\n  }\n\n  // When axis is a categorial axis, but the type of axis is number or the scale of axis is not \"auto\"\n  if (axis.isCategorical && axis.categoricalDomain) {\n    return axis.categoricalDomain.map(function (entry, index) {\n      return {\n        coordinate: scale(entry) + offset,\n        value: entry,\n        index: index,\n        offset: offset\n      };\n    });\n  }\n  if (scale.ticks && !isAll) {\n    return scale.ticks(axis.tickCount).map(function (entry) {\n      return {\n        coordinate: scale(entry) + offset,\n        value: entry,\n        offset: offset\n      };\n    });\n  }\n\n  // When axis has duplicated text, serial numbers are used to generate scale\n  return scale.domain().map(function (entry, index) {\n    return {\n      coordinate: scale(entry) + offset,\n      value: duplicateDomain ? duplicateDomain[entry] : entry,\n      index: index,\n      offset: offset\n    };\n  });\n};\n\n/**\n * combine the handlers\n * @param  {Function} defaultHandler Internal private handler\n * @param  {Function} childHandler Handler function specified in child component\n * @return {Function}                The combined handler\n */\n\nvar handlerWeakMap = new WeakMap();\nexport var combineEventHandlers = function combineEventHandlers(defaultHandler, childHandler) {\n  if (typeof childHandler !== 'function') {\n    return defaultHandler;\n  }\n  if (!handlerWeakMap.has(defaultHandler)) {\n    handlerWeakMap.set(defaultHandler, new WeakMap());\n  }\n  var childWeakMap = handlerWeakMap.get(defaultHandler);\n  if (childWeakMap.has(childHandler)) {\n    return childWeakMap.get(childHandler);\n  }\n  var combineHandler = function combineHandler() {\n    defaultHandler.apply(void 0, arguments);\n    childHandler.apply(void 0, arguments);\n  };\n  childWeakMap.set(childHandler, combineHandler);\n  return combineHandler;\n};\n\n/**\n * Parse the scale function of axis\n * @param  {Object}   axis          The option of axis\n * @param  {String}   chartType     The displayName of chart\n * @param  {Boolean}  hasBar        if it has a bar\n * @return {object}               The scale function and resolved name\n */\nexport var parseScale = function parseScale(axis, chartType, hasBar) {\n  var scale = axis.scale,\n    type = axis.type,\n    layout = axis.layout,\n    axisType = axis.axisType;\n  if (scale === 'auto') {\n    if (layout === 'radial' && axisType === 'radiusAxis') {\n      return {\n        scale: d3Scales.scaleBand(),\n        realScaleType: 'band'\n      };\n    }\n    if (layout === 'radial' && axisType === 'angleAxis') {\n      return {\n        scale: d3Scales.scaleLinear(),\n        realScaleType: 'linear'\n      };\n    }\n    if (type === 'category' && chartType && (chartType.indexOf('LineChart') >= 0 || chartType.indexOf('AreaChart') >= 0 || chartType.indexOf('ComposedChart') >= 0 && !hasBar)) {\n      return {\n        scale: d3Scales.scalePoint(),\n        realScaleType: 'point'\n      };\n    }\n    if (type === 'category') {\n      return {\n        scale: d3Scales.scaleBand(),\n        realScaleType: 'band'\n      };\n    }\n    return {\n      scale: d3Scales.scaleLinear(),\n      realScaleType: 'linear'\n    };\n  }\n  if (isString(scale)) {\n    var name = \"scale\".concat(upperFirst(scale));\n    return {\n      scale: (d3Scales[name] || d3Scales.scalePoint)(),\n      realScaleType: d3Scales[name] ? name : 'point'\n    };\n  }\n  return isFunction(scale) ? {\n    scale: scale\n  } : {\n    scale: d3Scales.scalePoint(),\n    realScaleType: 'point'\n  };\n};\nvar EPS = 1e-4;\nexport var checkDomainOfScale = function checkDomainOfScale(scale) {\n  var domain = scale.domain();\n  if (!domain || domain.length <= 2) {\n    return;\n  }\n  var len = domain.length;\n  var range = scale.range();\n  var minValue = Math.min(range[0], range[1]) - EPS;\n  var maxValue = Math.max(range[0], range[1]) + EPS;\n  var first = scale(domain[0]);\n  var last = scale(domain[len - 1]);\n  if (first < minValue || first > maxValue || last < minValue || last > maxValue) {\n    scale.domain([domain[0], domain[len - 1]]);\n  }\n};\nexport var findPositionOfBar = function findPositionOfBar(barPosition, child) {\n  if (!barPosition) {\n    return null;\n  }\n  for (var i = 0, len = barPosition.length; i < len; i++) {\n    if (barPosition[i].item === child) {\n      return barPosition[i].position;\n    }\n  }\n  return null;\n};\n\n/**\n * Both value and domain are tuples of two numbers\n * - but the type stays as array of numbers until we have better support in rest of the app\n * @param {Array} value input that will be truncated\n * @param {Array} domain boundaries\n * @returns {Array} tuple of two numbers\n */\nexport var truncateByDomain = function truncateByDomain(value, domain) {\n  if (!domain || domain.length !== 2 || !isNumber(domain[0]) || !isNumber(domain[1])) {\n    return value;\n  }\n  var minValue = Math.min(domain[0], domain[1]);\n  var maxValue = Math.max(domain[0], domain[1]);\n  var result = [value[0], value[1]];\n  if (!isNumber(value[0]) || value[0] < minValue) {\n    result[0] = minValue;\n  }\n  if (!isNumber(value[1]) || value[1] > maxValue) {\n    result[1] = maxValue;\n  }\n  if (result[0] > maxValue) {\n    result[0] = maxValue;\n  }\n  if (result[1] < minValue) {\n    result[1] = minValue;\n  }\n  return result;\n};\n\n/**\n * Stacks all positive numbers above zero and all negative numbers below zero.\n *\n * If all values in the series are positive then this behaves the same as 'none' stacker.\n *\n * @param {Array} series from d3-shape Stack\n * @return {Array} series with applied offset\n */\nexport var offsetSign = function offsetSign(series) {\n  var n = series.length;\n  if (n <= 0) {\n    return;\n  }\n  for (var j = 0, m = series[0].length; j < m; ++j) {\n    var positive = 0;\n    var negative = 0;\n    for (var i = 0; i < n; ++i) {\n      var value = isNan(series[i][j][1]) ? series[i][j][0] : series[i][j][1];\n\n      /* eslint-disable prefer-destructuring, no-param-reassign */\n      if (value >= 0) {\n        series[i][j][0] = positive;\n        series[i][j][1] = positive + value;\n        positive = series[i][j][1];\n      } else {\n        series[i][j][0] = negative;\n        series[i][j][1] = negative + value;\n        negative = series[i][j][1];\n      }\n      /* eslint-enable prefer-destructuring, no-param-reassign */\n    }\n  }\n};\n\n/**\n * Replaces all negative values with zero when stacking data.\n *\n * If all values in the series are positive then this behaves the same as 'none' stacker.\n *\n * @param {Array} series from d3-shape Stack\n * @return {Array} series with applied offset\n */\nexport var offsetPositive = function offsetPositive(series) {\n  var n = series.length;\n  if (n <= 0) {\n    return;\n  }\n  for (var j = 0, m = series[0].length; j < m; ++j) {\n    var positive = 0;\n    for (var i = 0; i < n; ++i) {\n      var value = isNan(series[i][j][1]) ? series[i][j][0] : series[i][j][1];\n\n      /* eslint-disable prefer-destructuring, no-param-reassign */\n      if (value >= 0) {\n        series[i][j][0] = positive;\n        series[i][j][1] = positive + value;\n        positive = series[i][j][1];\n      } else {\n        series[i][j][0] = 0;\n        series[i][j][1] = 0;\n      }\n      /* eslint-enable prefer-destructuring, no-param-reassign */\n    }\n  }\n};\n\n/**\n * Function type to compute offset for stacked data.\n *\n * d3-shape has something fishy going on with its types.\n * In @definitelytyped/d3-shape, this function (the offset accessor) is typed as Series<> => void.\n * However! When I actually open the storybook I can see that the offset accessor actually receives Array<Series<>>.\n * The same I can see in the source code itself:\n * https://github.com/DefinitelyTyped/DefinitelyTyped/discussions/66042\n * That one unfortunately has no types but we can tell it passes three-dimensional array.\n *\n * Which leads me to believe that definitelytyped is wrong on this one.\n * There's open discussion on this topic without much attention:\n * https://github.com/DefinitelyTyped/DefinitelyTyped/discussions/66042\n */\n\nvar STACK_OFFSET_MAP = {\n  sign: offsetSign,\n  // @ts-expect-error definitelytyped types are incorrect\n  expand: stackOffsetExpand,\n  // @ts-expect-error definitelytyped types are incorrect\n  none: stackOffsetNone,\n  // @ts-expect-error definitelytyped types are incorrect\n  silhouette: stackOffsetSilhouette,\n  // @ts-expect-error definitelytyped types are incorrect\n  wiggle: stackOffsetWiggle,\n  positive: offsetPositive\n};\nexport var getStackedData = function getStackedData(data, stackItems, offsetType) {\n  var dataKeys = stackItems.map(function (item) {\n    return item.props.dataKey;\n  });\n  var offsetAccessor = STACK_OFFSET_MAP[offsetType];\n  var stack = shapeStack()\n  // @ts-expect-error stack.keys type wants an array of strings, but we provide array of DataKeys\n  .keys(dataKeys).value(function (d, key) {\n    return +getValueByDataKey(d, key, 0);\n  }).order(stackOrderNone)\n  // @ts-expect-error definitelytyped types are incorrect\n  .offset(offsetAccessor);\n  return stack(data);\n};\nexport var getStackGroupsByAxisId = function getStackGroupsByAxisId(data, _items, numericAxisId, cateAxisId, offsetType, reverseStackOrder) {\n  if (!data) {\n    return null;\n  }\n\n  // reversing items to affect render order (for layering)\n  var items = reverseStackOrder ? _items.reverse() : _items;\n  var parentStackGroupsInitialValue = {};\n  var stackGroups = items.reduce(function (result, item) {\n    var _item$type2;\n    var defaultedProps = (_item$type2 = item.type) !== null && _item$type2 !== void 0 && _item$type2.defaultProps ? _objectSpread(_objectSpread({}, item.type.defaultProps), item.props) : item.props;\n    var stackId = defaultedProps.stackId,\n      hide = defaultedProps.hide;\n    if (hide) {\n      return result;\n    }\n    var axisId = defaultedProps[numericAxisId];\n    var parentGroup = result[axisId] || {\n      hasStack: false,\n      stackGroups: {}\n    };\n    if (isNumOrStr(stackId)) {\n      var childGroup = parentGroup.stackGroups[stackId] || {\n        numericAxisId: numericAxisId,\n        cateAxisId: cateAxisId,\n        items: []\n      };\n      childGroup.items.push(item);\n      parentGroup.hasStack = true;\n      parentGroup.stackGroups[stackId] = childGroup;\n    } else {\n      parentGroup.stackGroups[uniqueId('_stackId_')] = {\n        numericAxisId: numericAxisId,\n        cateAxisId: cateAxisId,\n        items: [item]\n      };\n    }\n    return _objectSpread(_objectSpread({}, result), {}, _defineProperty({}, axisId, parentGroup));\n  }, parentStackGroupsInitialValue);\n  var axisStackGroupsInitialValue = {};\n  return Object.keys(stackGroups).reduce(function (result, axisId) {\n    var group = stackGroups[axisId];\n    if (group.hasStack) {\n      var stackGroupsInitialValue = {};\n      group.stackGroups = Object.keys(group.stackGroups).reduce(function (res, stackId) {\n        var g = group.stackGroups[stackId];\n        return _objectSpread(_objectSpread({}, res), {}, _defineProperty({}, stackId, {\n          numericAxisId: numericAxisId,\n          cateAxisId: cateAxisId,\n          items: g.items,\n          stackedData: getStackedData(data, g.items, offsetType)\n        }));\n      }, stackGroupsInitialValue);\n    }\n    return _objectSpread(_objectSpread({}, result), {}, _defineProperty({}, axisId, group));\n  }, axisStackGroupsInitialValue);\n};\n\n/**\n * Configure the scale function of axis\n * @param {Object} scale The scale function\n * @param {Object} opts  The configuration of axis\n * @return {Object}      null\n */\nexport var getTicksOfScale = function getTicksOfScale(scale, opts) {\n  var realScaleType = opts.realScaleType,\n    type = opts.type,\n    tickCount = opts.tickCount,\n    originalDomain = opts.originalDomain,\n    allowDecimals = opts.allowDecimals;\n  var scaleType = realScaleType || opts.scale;\n  if (scaleType !== 'auto' && scaleType !== 'linear') {\n    return null;\n  }\n  if (tickCount && type === 'number' && originalDomain && (originalDomain[0] === 'auto' || originalDomain[1] === 'auto')) {\n    // Calculate the ticks by the number of grid when the axis is a number axis\n    var domain = scale.domain();\n    if (!domain.length) {\n      return null;\n    }\n    var tickValues = getNiceTickValues(domain, tickCount, allowDecimals);\n    scale.domain([min(tickValues), max(tickValues)]);\n    return {\n      niceTicks: tickValues\n    };\n  }\n  if (tickCount && type === 'number') {\n    var _domain = scale.domain();\n    var _tickValues = getTickValuesFixedDomain(_domain, tickCount, allowDecimals);\n    return {\n      niceTicks: _tickValues\n    };\n  }\n  return null;\n};\nexport function getCateCoordinateOfLine(_ref5) {\n  var axis = _ref5.axis,\n    ticks = _ref5.ticks,\n    bandSize = _ref5.bandSize,\n    entry = _ref5.entry,\n    index = _ref5.index,\n    dataKey = _ref5.dataKey;\n  if (axis.type === 'category') {\n    // find coordinate of category axis by the value of category\n    // @ts-expect-error why does this use direct object access instead of getValueByDataKey?\n    if (!axis.allowDuplicatedCategory && axis.dataKey && !isNil(entry[axis.dataKey])) {\n      // @ts-expect-error why does this use direct object access instead of getValueByDataKey?\n      var matchedTick = findEntryInArray(ticks, 'value', entry[axis.dataKey]);\n      if (matchedTick) {\n        return matchedTick.coordinate + bandSize / 2;\n      }\n    }\n    return ticks[index] ? ticks[index].coordinate + bandSize / 2 : null;\n  }\n  var value = getValueByDataKey(entry, !isNil(dataKey) ? dataKey : axis.dataKey);\n  return !isNil(value) ? axis.scale(value) : null;\n}\nexport var getCateCoordinateOfBar = function getCateCoordinateOfBar(_ref6) {\n  var axis = _ref6.axis,\n    ticks = _ref6.ticks,\n    offset = _ref6.offset,\n    bandSize = _ref6.bandSize,\n    entry = _ref6.entry,\n    index = _ref6.index;\n  if (axis.type === 'category') {\n    return ticks[index] ? ticks[index].coordinate + offset : null;\n  }\n  var value = getValueByDataKey(entry, axis.dataKey, axis.domain[index]);\n  return !isNil(value) ? axis.scale(value) - bandSize / 2 + offset : null;\n};\nexport var getBaseValueOfBar = function getBaseValueOfBar(_ref7) {\n  var numericAxis = _ref7.numericAxis;\n  var domain = numericAxis.scale.domain();\n  if (numericAxis.type === 'number') {\n    var minValue = Math.min(domain[0], domain[1]);\n    var maxValue = Math.max(domain[0], domain[1]);\n    if (minValue <= 0 && maxValue >= 0) {\n      return 0;\n    }\n    if (maxValue < 0) {\n      return maxValue;\n    }\n    return minValue;\n  }\n  return domain[0];\n};\nexport var getStackedDataOfItem = function getStackedDataOfItem(item, stackGroups) {\n  var _item$type3;\n  var defaultedProps = (_item$type3 = item.type) !== null && _item$type3 !== void 0 && _item$type3.defaultProps ? _objectSpread(_objectSpread({}, item.type.defaultProps), item.props) : item.props;\n  var stackId = defaultedProps.stackId;\n  if (isNumOrStr(stackId)) {\n    var group = stackGroups[stackId];\n    if (group) {\n      var itemIndex = group.items.indexOf(item);\n      return itemIndex >= 0 ? group.stackedData[itemIndex] : null;\n    }\n  }\n  return null;\n};\nvar getDomainOfSingle = function getDomainOfSingle(data) {\n  return data.reduce(function (result, entry) {\n    return [min(entry.concat([result[0]]).filter(isNumber)), max(entry.concat([result[1]]).filter(isNumber))];\n  }, [Infinity, -Infinity]);\n};\nexport var getDomainOfStackGroups = function getDomainOfStackGroups(stackGroups, startIndex, endIndex) {\n  return Object.keys(stackGroups).reduce(function (result, stackId) {\n    var group = stackGroups[stackId];\n    var stackedData = group.stackedData;\n    var domain = stackedData.reduce(function (res, entry) {\n      var s = getDomainOfSingle(entry.slice(startIndex, endIndex + 1));\n      return [Math.min(res[0], s[0]), Math.max(res[1], s[1])];\n    }, [Infinity, -Infinity]);\n    return [Math.min(domain[0], result[0]), Math.max(domain[1], result[1])];\n  }, [Infinity, -Infinity]).map(function (result) {\n    return result === Infinity || result === -Infinity ? 0 : result;\n  });\n};\nexport var MIN_VALUE_REG = /^dataMin[\\s]*-[\\s]*([0-9]+([.]{1}[0-9]+){0,1})$/;\nexport var MAX_VALUE_REG = /^dataMax[\\s]*\\+[\\s]*([0-9]+([.]{1}[0-9]+){0,1})$/;\nexport var parseSpecifiedDomain = function parseSpecifiedDomain(specifiedDomain, dataDomain, allowDataOverflow) {\n  if (isFunction(specifiedDomain)) {\n    return specifiedDomain(dataDomain, allowDataOverflow);\n  }\n  if (!Array.isArray(specifiedDomain)) {\n    return dataDomain;\n  }\n  var domain = [];\n\n  /* eslint-disable prefer-destructuring */\n  if (isNumber(specifiedDomain[0])) {\n    domain[0] = allowDataOverflow ? specifiedDomain[0] : Math.min(specifiedDomain[0], dataDomain[0]);\n  } else if (MIN_VALUE_REG.test(specifiedDomain[0])) {\n    var value = +MIN_VALUE_REG.exec(specifiedDomain[0])[1];\n    domain[0] = dataDomain[0] - value;\n  } else if (isFunction(specifiedDomain[0])) {\n    domain[0] = specifiedDomain[0](dataDomain[0]);\n  } else {\n    domain[0] = dataDomain[0];\n  }\n  if (isNumber(specifiedDomain[1])) {\n    domain[1] = allowDataOverflow ? specifiedDomain[1] : Math.max(specifiedDomain[1], dataDomain[1]);\n  } else if (MAX_VALUE_REG.test(specifiedDomain[1])) {\n    var _value = +MAX_VALUE_REG.exec(specifiedDomain[1])[1];\n    domain[1] = dataDomain[1] + _value;\n  } else if (isFunction(specifiedDomain[1])) {\n    domain[1] = specifiedDomain[1](dataDomain[1]);\n  } else {\n    domain[1] = dataDomain[1];\n  }\n  /* eslint-enable prefer-destructuring */\n\n  return domain;\n};\n\n/**\n * Calculate the size between two category\n * @param  {Object} axis  The options of axis\n * @param  {Array}  ticks The ticks of axis\n * @param  {Boolean} isBar if items in axis are bars\n * @return {Number} Size\n */\nexport var getBandSizeOfAxis = function getBandSizeOfAxis(axis, ticks, isBar) {\n  // @ts-expect-error we need to rethink scale type\n  if (axis && axis.scale && axis.scale.bandwidth) {\n    // @ts-expect-error we need to rethink scale type\n    var bandWidth = axis.scale.bandwidth();\n    if (!isBar || bandWidth > 0) {\n      return bandWidth;\n    }\n  }\n  if (axis && ticks && ticks.length >= 2) {\n    var orderedTicks = sortBy(ticks, function (o) {\n      return o.coordinate;\n    });\n    var bandSize = Infinity;\n    for (var i = 1, len = orderedTicks.length; i < len; i++) {\n      var cur = orderedTicks[i];\n      var prev = orderedTicks[i - 1];\n      bandSize = Math.min((cur.coordinate || 0) - (prev.coordinate || 0), bandSize);\n    }\n    return bandSize === Infinity ? 0 : bandSize;\n  }\n  return isBar ? undefined : 0;\n};\n/**\n * parse the domain of a category axis when a domain is specified\n * @param   {Array}        specifiedDomain  The domain specified by users\n * @param   {Array}        calculatedDomain The domain calculated by dateKey\n * @param   {ReactElement} axisChild        The axis ReactElement\n * @returns {Array}        domains\n */\nexport var parseDomainOfCategoryAxis = function parseDomainOfCategoryAxis(specifiedDomain, calculatedDomain, axisChild) {\n  if (!specifiedDomain || !specifiedDomain.length) {\n    return calculatedDomain;\n  }\n  if (isEqual(specifiedDomain, get(axisChild, 'type.defaultProps.domain'))) {\n    return calculatedDomain;\n  }\n  return specifiedDomain;\n};\nexport var getTooltipItem = function getTooltipItem(graphicalItem, payload) {\n  var defaultedProps = graphicalItem.type.defaultProps ? _objectSpread(_objectSpread({}, graphicalItem.type.defaultProps), graphicalItem.props) : graphicalItem.props;\n  var dataKey = defaultedProps.dataKey,\n    name = defaultedProps.name,\n    unit = defaultedProps.unit,\n    formatter = defaultedProps.formatter,\n    tooltipType = defaultedProps.tooltipType,\n    chartType = defaultedProps.chartType,\n    hide = defaultedProps.hide;\n  return _objectSpread(_objectSpread({}, filterProps(graphicalItem, false)), {}, {\n    dataKey: dataKey,\n    unit: unit,\n    formatter: formatter,\n    name: name || dataKey,\n    color: getMainColorOfGraphicItem(graphicalItem),\n    value: getValueByDataKey(payload, dataKey),\n    type: tooltipType,\n    payload: payload,\n    chartType: chartType,\n    hide: hide\n  });\n};", "map": {"version": 3, "names": ["_typeof", "o", "Symbol", "iterator", "constructor", "prototype", "_toConsumableArray", "arr", "_arrayWithoutHoles", "_iterableToArray", "_unsupportedIterableToArray", "_nonIterableSpread", "TypeError", "minLen", "_arrayLikeToArray", "n", "Object", "toString", "call", "slice", "name", "Array", "from", "test", "iter", "isArray", "len", "length", "i", "arr2", "ownKeys", "e", "r", "t", "keys", "getOwnPropertySymbols", "filter", "getOwnPropertyDescriptor", "enumerable", "push", "apply", "_objectSpread", "arguments", "for<PERSON>ach", "_defineProperty", "getOwnPropertyDescriptors", "defineProperties", "defineProperty", "obj", "key", "value", "_to<PERSON><PERSON><PERSON><PERSON><PERSON>", "configurable", "writable", "_toPrimitive", "toPrimitive", "String", "Number", "d3Scales", "stack", "shapeStack", "stackOffsetExpand", "stackOffsetNone", "stackOffsetSilhouette", "stackOffsetWiggle", "stackOrderNone", "max", "min", "isNil", "isFunction", "isString", "get", "flatMap", "isNan", "upperFirst", "isEqual", "sortBy", "getNiceTickValues", "getTickValuesFixedDomain", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "findEntryInArray", "getPercentValue", "isNumber", "isNumOrStr", "mathSign", "uniqueId", "filterProps", "findAllByType", "getDisplayName", "getLegendProps", "getValueByDataKey", "dataKey", "defaultValue", "getDomainOfDataByKey", "data", "type", "filterNil", "flattenData", "entry", "domain", "parseFloat", "Infinity", "validateData", "map", "Date", "calculateActiveTickIndex", "coordinate", "_ticks$length", "ticks", "undefined", "unsortedTicks", "axis", "index", "axisType", "Math", "abs", "range", "before", "cur", "after", "sameDirectionCoord", "diffInterval", "curInRange", "afterInRange", "sameInterval", "minValue", "maxValue", "_i", "getMainColorOfGraphicItem", "item", "_item$type", "_ref", "displayName", "defaultedProps", "defaultProps", "props", "stroke", "fill", "result", "getBarSizeList", "_ref2", "globalSize", "barSize", "totalSize", "_ref2$stackGroups", "stackGroups", "numericAxisIds", "sgs", "stackIds", "j", "sLen", "_sgs$stackIds$j", "items", "cateAxisId", "barItems", "indexOf", "barItemDefaultProps", "barItemProps", "selfSize", "cateId", "stackList", "getBarPosition", "_ref3", "barGap", "barCategoryGap", "bandSize", "_ref3$sizeList", "sizeList", "maxBarSize", "realBarGap", "initialValue", "useFull", "fullBarSize", "sum", "reduce", "res", "offset", "prev", "size", "newPosition", "position", "newRes", "concat", "_offset", "originalSize", "appendOffsetOfLegend", "_unused", "legendBox", "children", "width", "margin", "legend<PERSON><PERSON><PERSON>", "left", "right", "legendProps", "_ref4", "boxWidth", "boxHeight", "height", "align", "verticalAlign", "layout", "isErrorBarRelevantForAxis", "direction", "getDomainOfErrorBars", "errorBars", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "entryValue", "mainValue", "errorDomain", "prevErrorArr", "k", "errorValue", "lowerValue", "upperValue", "parseErrorBarsOfAxis", "domains", "getDomainOfItemsWithSameAxis", "tag", "isCategoricalAxis", "getCoordinatesOfGrid", "syncWithTicks", "has<PERSON>in", "hasMax", "values", "getTicksOfAxis", "isGrid", "isAll", "scale", "duplicateDomain", "offsetForBand", "realScaleType", "bandwidth", "niceTicks", "scaleContent", "row", "isCategorical", "categoricalDomain", "tickCount", "handlerWeakMap", "WeakMap", "combineEventHandlers", "defaultHandler", "<PERSON><PERSON><PERSON><PERSON>", "has", "set", "childWeakMap", "combineHandler", "parseScale", "chartType", "<PERSON><PERSON><PERSON>", "scaleBand", "scaleLinear", "scalePoint", "EPS", "checkDomainOfScale", "first", "last", "findPositionOfBar", "barPosition", "child", "truncateByDomain", "offsetSign", "series", "m", "positive", "negative", "offsetPositive", "STACK_OFFSET_MAP", "sign", "expand", "none", "silhouette", "wiggle", "getStackedData", "stackItems", "offsetType", "dataKeys", "offsetAccessor", "d", "order", "getStackGroupsByAxisId", "_items", "numericAxisId", "reverseStackOrder", "reverse", "parentStackGroupsInitialValue", "_item$type2", "stackId", "hide", "axisId", "parentGroup", "hasStack", "childGroup", "axisStackGroupsInitialValue", "group", "stackGroupsInitialValue", "g", "stackedData", "getTicksOfScale", "opts", "originalDomain", "allowDecimals", "scaleType", "tickValues", "_domain", "_tickValues", "getCateCoordinateOfLine", "_ref5", "allowDuplicatedCategory", "matchedTick", "getCateCoordinateOfBar", "_ref6", "getBaseValueOfBar", "_ref7", "numericAxis", "getStackedDataOfItem", "_item$type3", "itemIndex", "getDomainOfSingle", "getDomainOfStackGroups", "startIndex", "endIndex", "s", "MIN_VALUE_REG", "MAX_VALUE_REG", "parseSpecifiedDomain", "specifiedDomain", "dataDomain", "allowDataOverflow", "exec", "_value", "getBandSizeOfAxis", "isBar", "bandWidth", "orderedTicks", "parseDomainOfCategoryAxis", "calculatedDomain", "axisChild", "getTooltipItem", "graphicalItem", "payload", "unit", "formatter", "tooltipType", "color"], "sources": ["C:/Users/<USER>/PycharmProjects/CMS/webapp/frontend/node_modules/recharts/es6/util/ChartUtils.js"], "sourcesContent": ["function _typeof(o) { \"@babel/helpers - typeof\"; return _typeof = \"function\" == typeof Symbol && \"symbol\" == typeof Symbol.iterator ? function (o) { return typeof o; } : function (o) { return o && \"function\" == typeof Symbol && o.constructor === Symbol && o !== Symbol.prototype ? \"symbol\" : typeof o; }, _typeof(o); }\nfunction _toConsumableArray(arr) { return _arrayWithoutHoles(arr) || _iterableToArray(arr) || _unsupportedIterableToArray(arr) || _nonIterableSpread(); }\nfunction _nonIterableSpread() { throw new TypeError(\"Invalid attempt to spread non-iterable instance.\\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.\"); }\nfunction _unsupportedIterableToArray(o, minLen) { if (!o) return; if (typeof o === \"string\") return _arrayLikeToArray(o, minLen); var n = Object.prototype.toString.call(o).slice(8, -1); if (n === \"Object\" && o.constructor) n = o.constructor.name; if (n === \"Map\" || n === \"Set\") return Array.from(o); if (n === \"Arguments\" || /^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n)) return _arrayLikeToArray(o, minLen); }\nfunction _iterableToArray(iter) { if (typeof Symbol !== \"undefined\" && iter[Symbol.iterator] != null || iter[\"@@iterator\"] != null) return Array.from(iter); }\nfunction _arrayWithoutHoles(arr) { if (Array.isArray(arr)) return _arrayLikeToArray(arr); }\nfunction _arrayLikeToArray(arr, len) { if (len == null || len > arr.length) len = arr.length; for (var i = 0, arr2 = new Array(len); i < len; i++) arr2[i] = arr[i]; return arr2; }\nfunction ownKeys(e, r) { var t = Object.keys(e); if (Object.getOwnPropertySymbols) { var o = Object.getOwnPropertySymbols(e); r && (o = o.filter(function (r) { return Object.getOwnPropertyDescriptor(e, r).enumerable; })), t.push.apply(t, o); } return t; }\nfunction _objectSpread(e) { for (var r = 1; r < arguments.length; r++) { var t = null != arguments[r] ? arguments[r] : {}; r % 2 ? ownKeys(Object(t), !0).forEach(function (r) { _defineProperty(e, r, t[r]); }) : Object.getOwnPropertyDescriptors ? Object.defineProperties(e, Object.getOwnPropertyDescriptors(t)) : ownKeys(Object(t)).forEach(function (r) { Object.defineProperty(e, r, Object.getOwnPropertyDescriptor(t, r)); }); } return e; }\nfunction _defineProperty(obj, key, value) { key = _toPropertyKey(key); if (key in obj) { Object.defineProperty(obj, key, { value: value, enumerable: true, configurable: true, writable: true }); } else { obj[key] = value; } return obj; }\nfunction _toPropertyKey(t) { var i = _toPrimitive(t, \"string\"); return \"symbol\" == _typeof(i) ? i : i + \"\"; }\nfunction _toPrimitive(t, r) { if (\"object\" != _typeof(t) || !t) return t; var e = t[Symbol.toPrimitive]; if (void 0 !== e) { var i = e.call(t, r || \"default\"); if (\"object\" != _typeof(i)) return i; throw new TypeError(\"@@toPrimitive must return a primitive value.\"); } return (\"string\" === r ? String : Number)(t); }\nimport * as d3Scales from 'victory-vendor/d3-scale';\nimport { stack as shapeStack, stackOffsetExpand, stackOffsetNone, stackOffsetSilhouette, stackOffsetWiggle, stackOrderNone } from 'victory-vendor/d3-shape';\nimport max from 'lodash/max';\nimport min from 'lodash/min';\nimport isNil from 'lodash/isNil';\nimport isFunction from 'lodash/isFunction';\nimport isString from 'lodash/isString';\nimport get from 'lodash/get';\nimport flatMap from 'lodash/flatMap';\nimport isNan from 'lodash/isNaN';\nimport upperFirst from 'lodash/upperFirst';\nimport isEqual from 'lodash/isEqual';\nimport sortBy from 'lodash/sortBy';\nimport { getNiceTickValues, getTickValuesFixedDomain } from 'recharts-scale';\nimport { ErrorBar } from '../cartesian/ErrorBar';\nimport { findEntryInArray, getPercentValue, isNumber, isNumOrStr, mathSign, uniqueId } from './DataUtils';\nimport { filterProps, findAllByType, getDisplayName } from './ReactUtils';\n// TODO: Cause of circular dependency. Needs refactor.\n// import { RadiusAxisProps, AngleAxisProps } from '../polar/types';\n\nimport { getLegendProps } from './getLegendProps';\n\n// Exported for backwards compatibility\nexport { getLegendProps };\nexport function getValueByDataKey(obj, dataKey, defaultValue) {\n  if (isNil(obj) || isNil(dataKey)) {\n    return defaultValue;\n  }\n  if (isNumOrStr(dataKey)) {\n    return get(obj, dataKey, defaultValue);\n  }\n  if (isFunction(dataKey)) {\n    return dataKey(obj);\n  }\n  return defaultValue;\n}\n/**\n * Get domain of data by key.\n * @param  {Array}   data      The data displayed in the chart\n * @param  {String}  key       The unique key of a group of data\n * @param  {String}  type      The type of axis\n * @param  {Boolean} filterNil Whether or not filter nil values\n * @return {Array} Domain of data\n */\nexport function getDomainOfDataByKey(data, key, type, filterNil) {\n  var flattenData = flatMap(data, function (entry) {\n    return getValueByDataKey(entry, key);\n  });\n  if (type === 'number') {\n    // @ts-expect-error parseFloat type only accepts strings\n    var domain = flattenData.filter(function (entry) {\n      return isNumber(entry) || parseFloat(entry);\n    });\n    return domain.length ? [min(domain), max(domain)] : [Infinity, -Infinity];\n  }\n  var validateData = filterNil ? flattenData.filter(function (entry) {\n    return !isNil(entry);\n  }) : flattenData;\n\n  // Supports x-axis of Date type\n  return validateData.map(function (entry) {\n    return isNumOrStr(entry) || entry instanceof Date ? entry : '';\n  });\n}\nexport var calculateActiveTickIndex = function calculateActiveTickIndex(coordinate) {\n  var _ticks$length;\n  var ticks = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : [];\n  var unsortedTicks = arguments.length > 2 ? arguments[2] : undefined;\n  var axis = arguments.length > 3 ? arguments[3] : undefined;\n  var index = -1;\n  var len = (_ticks$length = ticks === null || ticks === void 0 ? void 0 : ticks.length) !== null && _ticks$length !== void 0 ? _ticks$length : 0;\n\n  // if there are 1 or less ticks ticks then the active tick is at index 0\n  if (len <= 1) {\n    return 0;\n  }\n  if (axis && axis.axisType === 'angleAxis' && Math.abs(Math.abs(axis.range[1] - axis.range[0]) - 360) <= 1e-6) {\n    var range = axis.range;\n    // ticks are distributed in a circle\n    for (var i = 0; i < len; i++) {\n      var before = i > 0 ? unsortedTicks[i - 1].coordinate : unsortedTicks[len - 1].coordinate;\n      var cur = unsortedTicks[i].coordinate;\n      var after = i >= len - 1 ? unsortedTicks[0].coordinate : unsortedTicks[i + 1].coordinate;\n      var sameDirectionCoord = void 0;\n      if (mathSign(cur - before) !== mathSign(after - cur)) {\n        var diffInterval = [];\n        if (mathSign(after - cur) === mathSign(range[1] - range[0])) {\n          sameDirectionCoord = after;\n          var curInRange = cur + range[1] - range[0];\n          diffInterval[0] = Math.min(curInRange, (curInRange + before) / 2);\n          diffInterval[1] = Math.max(curInRange, (curInRange + before) / 2);\n        } else {\n          sameDirectionCoord = before;\n          var afterInRange = after + range[1] - range[0];\n          diffInterval[0] = Math.min(cur, (afterInRange + cur) / 2);\n          diffInterval[1] = Math.max(cur, (afterInRange + cur) / 2);\n        }\n        var sameInterval = [Math.min(cur, (sameDirectionCoord + cur) / 2), Math.max(cur, (sameDirectionCoord + cur) / 2)];\n        if (coordinate > sameInterval[0] && coordinate <= sameInterval[1] || coordinate >= diffInterval[0] && coordinate <= diffInterval[1]) {\n          index = unsortedTicks[i].index;\n          break;\n        }\n      } else {\n        var minValue = Math.min(before, after);\n        var maxValue = Math.max(before, after);\n        if (coordinate > (minValue + cur) / 2 && coordinate <= (maxValue + cur) / 2) {\n          index = unsortedTicks[i].index;\n          break;\n        }\n      }\n    }\n  } else {\n    // ticks are distributed in a single direction\n    for (var _i = 0; _i < len; _i++) {\n      if (_i === 0 && coordinate <= (ticks[_i].coordinate + ticks[_i + 1].coordinate) / 2 || _i > 0 && _i < len - 1 && coordinate > (ticks[_i].coordinate + ticks[_i - 1].coordinate) / 2 && coordinate <= (ticks[_i].coordinate + ticks[_i + 1].coordinate) / 2 || _i === len - 1 && coordinate > (ticks[_i].coordinate + ticks[_i - 1].coordinate) / 2) {\n        index = ticks[_i].index;\n        break;\n      }\n    }\n  }\n  return index;\n};\n\n/**\n * Get the main color of each graphic item\n * @param  {ReactElement} item A graphic item\n * @return {String}            Color\n */\nexport var getMainColorOfGraphicItem = function getMainColorOfGraphicItem(item) {\n  var _item$type;\n  var _ref = item,\n    displayName = _ref.type.displayName; // TODO: check if displayName is valid.\n  var defaultedProps = (_item$type = item.type) !== null && _item$type !== void 0 && _item$type.defaultProps ? _objectSpread(_objectSpread({}, item.type.defaultProps), item.props) : item.props;\n  var stroke = defaultedProps.stroke,\n    fill = defaultedProps.fill;\n  var result;\n  switch (displayName) {\n    case 'Line':\n      result = stroke;\n      break;\n    case 'Area':\n    case 'Radar':\n      result = stroke && stroke !== 'none' ? stroke : fill;\n      break;\n    default:\n      result = fill;\n      break;\n  }\n  return result;\n};\n/**\n * Calculate the size of all groups for stacked bar graph\n * @param  {Object} stackGroups The items grouped by axisId and stackId\n * @return {Object} The size of all groups\n */\nexport var getBarSizeList = function getBarSizeList(_ref2) {\n  var globalSize = _ref2.barSize,\n    totalSize = _ref2.totalSize,\n    _ref2$stackGroups = _ref2.stackGroups,\n    stackGroups = _ref2$stackGroups === void 0 ? {} : _ref2$stackGroups;\n  if (!stackGroups) {\n    return {};\n  }\n  var result = {};\n  var numericAxisIds = Object.keys(stackGroups);\n  for (var i = 0, len = numericAxisIds.length; i < len; i++) {\n    var sgs = stackGroups[numericAxisIds[i]].stackGroups;\n    var stackIds = Object.keys(sgs);\n    for (var j = 0, sLen = stackIds.length; j < sLen; j++) {\n      var _sgs$stackIds$j = sgs[stackIds[j]],\n        items = _sgs$stackIds$j.items,\n        cateAxisId = _sgs$stackIds$j.cateAxisId;\n      var barItems = items.filter(function (item) {\n        return getDisplayName(item.type).indexOf('Bar') >= 0;\n      });\n      if (barItems && barItems.length) {\n        var barItemDefaultProps = barItems[0].type.defaultProps;\n        var barItemProps = barItemDefaultProps !== undefined ? _objectSpread(_objectSpread({}, barItemDefaultProps), barItems[0].props) : barItems[0].props;\n        var selfSize = barItemProps.barSize;\n        var cateId = barItemProps[cateAxisId];\n        if (!result[cateId]) {\n          result[cateId] = [];\n        }\n        var barSize = isNil(selfSize) ? globalSize : selfSize;\n        result[cateId].push({\n          item: barItems[0],\n          stackList: barItems.slice(1),\n          barSize: isNil(barSize) ? undefined : getPercentValue(barSize, totalSize, 0)\n        });\n      }\n    }\n  }\n  return result;\n};\n/**\n * Calculate the size of each bar and offset between start of band and the bar\n *\n * @param  {number} bandSize is the size of area where bars can render\n * @param  {number | string} barGap is the gap size, as a percentage of `bandSize`.\n *                                  Can be defined as number or percent string\n * @param  {number | string} barCategoryGap is the gap size, as a percentage of `bandSize`.\n *                                  Can be defined as number or percent string\n * @param  {Array<object>} sizeList Sizes of all groups\n * @param  {number} maxBarSize The maximum size of each bar\n * @return {Array<object>} The size and offset of each bar\n */\nexport var getBarPosition = function getBarPosition(_ref3) {\n  var barGap = _ref3.barGap,\n    barCategoryGap = _ref3.barCategoryGap,\n    bandSize = _ref3.bandSize,\n    _ref3$sizeList = _ref3.sizeList,\n    sizeList = _ref3$sizeList === void 0 ? [] : _ref3$sizeList,\n    maxBarSize = _ref3.maxBarSize;\n  var len = sizeList.length;\n  if (len < 1) return null;\n  var realBarGap = getPercentValue(barGap, bandSize, 0, true);\n  var result;\n  var initialValue = [];\n\n  // whether or not is barSize setted by user\n  if (sizeList[0].barSize === +sizeList[0].barSize) {\n    var useFull = false;\n    var fullBarSize = bandSize / len;\n    // @ts-expect-error the type check above does not check for type number explicitly\n    var sum = sizeList.reduce(function (res, entry) {\n      return res + entry.barSize || 0;\n    }, 0);\n    sum += (len - 1) * realBarGap;\n    if (sum >= bandSize) {\n      sum -= (len - 1) * realBarGap;\n      realBarGap = 0;\n    }\n    if (sum >= bandSize && fullBarSize > 0) {\n      useFull = true;\n      fullBarSize *= 0.9;\n      sum = len * fullBarSize;\n    }\n    var offset = (bandSize - sum) / 2 >> 0;\n    var prev = {\n      offset: offset - realBarGap,\n      size: 0\n    };\n    result = sizeList.reduce(function (res, entry) {\n      var newPosition = {\n        item: entry.item,\n        position: {\n          offset: prev.offset + prev.size + realBarGap,\n          // @ts-expect-error the type check above does not check for type number explicitly\n          size: useFull ? fullBarSize : entry.barSize\n        }\n      };\n      var newRes = [].concat(_toConsumableArray(res), [newPosition]);\n      prev = newRes[newRes.length - 1].position;\n      if (entry.stackList && entry.stackList.length) {\n        entry.stackList.forEach(function (item) {\n          newRes.push({\n            item: item,\n            position: prev\n          });\n        });\n      }\n      return newRes;\n    }, initialValue);\n  } else {\n    var _offset = getPercentValue(barCategoryGap, bandSize, 0, true);\n    if (bandSize - 2 * _offset - (len - 1) * realBarGap <= 0) {\n      realBarGap = 0;\n    }\n    var originalSize = (bandSize - 2 * _offset - (len - 1) * realBarGap) / len;\n    if (originalSize > 1) {\n      originalSize >>= 0;\n    }\n    var size = maxBarSize === +maxBarSize ? Math.min(originalSize, maxBarSize) : originalSize;\n    result = sizeList.reduce(function (res, entry, i) {\n      var newRes = [].concat(_toConsumableArray(res), [{\n        item: entry.item,\n        position: {\n          offset: _offset + (originalSize + realBarGap) * i + (originalSize - size) / 2,\n          size: size\n        }\n      }]);\n      if (entry.stackList && entry.stackList.length) {\n        entry.stackList.forEach(function (item) {\n          newRes.push({\n            item: item,\n            position: newRes[newRes.length - 1].position\n          });\n        });\n      }\n      return newRes;\n    }, initialValue);\n  }\n  return result;\n};\nexport var appendOffsetOfLegend = function appendOffsetOfLegend(offset, _unused, props, legendBox) {\n  var children = props.children,\n    width = props.width,\n    margin = props.margin;\n  var legendWidth = width - (margin.left || 0) - (margin.right || 0);\n  var legendProps = getLegendProps({\n    children: children,\n    legendWidth: legendWidth\n  });\n  if (legendProps) {\n    var _ref4 = legendBox || {},\n      boxWidth = _ref4.width,\n      boxHeight = _ref4.height;\n    var align = legendProps.align,\n      verticalAlign = legendProps.verticalAlign,\n      layout = legendProps.layout;\n    if ((layout === 'vertical' || layout === 'horizontal' && verticalAlign === 'middle') && align !== 'center' && isNumber(offset[align])) {\n      return _objectSpread(_objectSpread({}, offset), {}, _defineProperty({}, align, offset[align] + (boxWidth || 0)));\n    }\n    if ((layout === 'horizontal' || layout === 'vertical' && align === 'center') && verticalAlign !== 'middle' && isNumber(offset[verticalAlign])) {\n      return _objectSpread(_objectSpread({}, offset), {}, _defineProperty({}, verticalAlign, offset[verticalAlign] + (boxHeight || 0)));\n    }\n  }\n  return offset;\n};\nvar isErrorBarRelevantForAxis = function isErrorBarRelevantForAxis(layout, axisType, direction) {\n  if (isNil(axisType)) {\n    return true;\n  }\n  if (layout === 'horizontal') {\n    return axisType === 'yAxis';\n  }\n  if (layout === 'vertical') {\n    return axisType === 'xAxis';\n  }\n  if (direction === 'x') {\n    return axisType === 'xAxis';\n  }\n  if (direction === 'y') {\n    return axisType === 'yAxis';\n  }\n  return true;\n};\nexport var getDomainOfErrorBars = function getDomainOfErrorBars(data, item, dataKey, layout, axisType) {\n  var children = item.props.children;\n  var errorBars = findAllByType(children, ErrorBar).filter(function (errorBarChild) {\n    return isErrorBarRelevantForAxis(layout, axisType, errorBarChild.props.direction);\n  });\n  if (errorBars && errorBars.length) {\n    var keys = errorBars.map(function (errorBarChild) {\n      return errorBarChild.props.dataKey;\n    });\n    return data.reduce(function (result, entry) {\n      var entryValue = getValueByDataKey(entry, dataKey);\n      if (isNil(entryValue)) return result;\n      var mainValue = Array.isArray(entryValue) ? [min(entryValue), max(entryValue)] : [entryValue, entryValue];\n      var errorDomain = keys.reduce(function (prevErrorArr, k) {\n        var errorValue = getValueByDataKey(entry, k, 0);\n        var lowerValue = mainValue[0] - Math.abs(Array.isArray(errorValue) ? errorValue[0] : errorValue);\n        var upperValue = mainValue[1] + Math.abs(Array.isArray(errorValue) ? errorValue[1] : errorValue);\n        return [Math.min(lowerValue, prevErrorArr[0]), Math.max(upperValue, prevErrorArr[1])];\n      }, [Infinity, -Infinity]);\n      return [Math.min(errorDomain[0], result[0]), Math.max(errorDomain[1], result[1])];\n    }, [Infinity, -Infinity]);\n  }\n  return null;\n};\nexport var parseErrorBarsOfAxis = function parseErrorBarsOfAxis(data, items, dataKey, axisType, layout) {\n  var domains = items.map(function (item) {\n    return getDomainOfErrorBars(data, item, dataKey, layout, axisType);\n  }).filter(function (entry) {\n    return !isNil(entry);\n  });\n  if (domains && domains.length) {\n    return domains.reduce(function (result, entry) {\n      return [Math.min(result[0], entry[0]), Math.max(result[1], entry[1])];\n    }, [Infinity, -Infinity]);\n  }\n  return null;\n};\n\n/**\n * Get domain of data by the configuration of item element\n * @param  {Array}   data      The data displayed in the chart\n * @param  {Array}   items     The instances of item\n * @param  {String}  type      The type of axis, number - Number Axis, category - Category Axis\n * @param  {LayoutType} layout The type of layout\n * @param  {Boolean} filterNil Whether or not filter nil values\n * @return {Array}        Domain\n */\nexport var getDomainOfItemsWithSameAxis = function getDomainOfItemsWithSameAxis(data, items, type, layout, filterNil) {\n  var domains = items.map(function (item) {\n    var dataKey = item.props.dataKey;\n    if (type === 'number' && dataKey) {\n      return getDomainOfErrorBars(data, item, dataKey, layout) || getDomainOfDataByKey(data, dataKey, type, filterNil);\n    }\n    return getDomainOfDataByKey(data, dataKey, type, filterNil);\n  });\n  if (type === 'number') {\n    // Calculate the domain of number axis\n    return domains.reduce(\n    // @ts-expect-error if (type === number) means that the domain is numerical type\n    // - but this link is missing in the type definition\n    function (result, entry) {\n      return [Math.min(result[0], entry[0]), Math.max(result[1], entry[1])];\n    }, [Infinity, -Infinity]);\n  }\n  var tag = {};\n  // Get the union set of category axis\n  return domains.reduce(function (result, entry) {\n    for (var i = 0, len = entry.length; i < len; i++) {\n      // @ts-expect-error Date cannot index an object\n      if (!tag[entry[i]]) {\n        // @ts-expect-error Date cannot index an object\n        tag[entry[i]] = true;\n\n        // @ts-expect-error Date cannot index an object\n        result.push(entry[i]);\n      }\n    }\n    return result;\n  }, []);\n};\nexport var isCategoricalAxis = function isCategoricalAxis(layout, axisType) {\n  return layout === 'horizontal' && axisType === 'xAxis' || layout === 'vertical' && axisType === 'yAxis' || layout === 'centric' && axisType === 'angleAxis' || layout === 'radial' && axisType === 'radiusAxis';\n};\n\n/**\n * Calculate the Coordinates of grid\n * @param  {Array} ticks           The ticks in axis\n * @param {Number} minValue        The minimun value of axis\n * @param {Number} maxValue        The maximun value of axis\n * @param {boolean} syncWithTicks  Synchronize grid lines with ticks or not\n * @return {Array}                 Coordinates\n */\nexport var getCoordinatesOfGrid = function getCoordinatesOfGrid(ticks, minValue, maxValue, syncWithTicks) {\n  if (syncWithTicks) {\n    return ticks.map(function (entry) {\n      return entry.coordinate;\n    });\n  }\n  var hasMin, hasMax;\n  var values = ticks.map(function (entry) {\n    if (entry.coordinate === minValue) {\n      hasMin = true;\n    }\n    if (entry.coordinate === maxValue) {\n      hasMax = true;\n    }\n    return entry.coordinate;\n  });\n  if (!hasMin) {\n    values.push(minValue);\n  }\n  if (!hasMax) {\n    values.push(maxValue);\n  }\n  return values;\n};\n\n/**\n * Get the ticks of an axis\n * @param  {Object}  axis The configuration of an axis\n * @param {Boolean} isGrid Whether or not are the ticks in grid\n * @param {Boolean} isAll Return the ticks of all the points or not\n * @return {Array}  Ticks\n */\nexport var getTicksOfAxis = function getTicksOfAxis(axis, isGrid, isAll) {\n  if (!axis) return null;\n  var scale = axis.scale;\n  var duplicateDomain = axis.duplicateDomain,\n    type = axis.type,\n    range = axis.range;\n  var offsetForBand = axis.realScaleType === 'scaleBand' ? scale.bandwidth() / 2 : 2;\n  var offset = (isGrid || isAll) && type === 'category' && scale.bandwidth ? scale.bandwidth() / offsetForBand : 0;\n  offset = axis.axisType === 'angleAxis' && (range === null || range === void 0 ? void 0 : range.length) >= 2 ? mathSign(range[0] - range[1]) * 2 * offset : offset;\n\n  // The ticks set by user should only affect the ticks adjacent to axis line\n  if (isGrid && (axis.ticks || axis.niceTicks)) {\n    var result = (axis.ticks || axis.niceTicks).map(function (entry) {\n      var scaleContent = duplicateDomain ? duplicateDomain.indexOf(entry) : entry;\n      return {\n        // If the scaleContent is not a number, the coordinate will be NaN.\n        // That could be the case for example with a PointScale and a string as domain.\n        coordinate: scale(scaleContent) + offset,\n        value: entry,\n        offset: offset\n      };\n    });\n    return result.filter(function (row) {\n      return !isNan(row.coordinate);\n    });\n  }\n\n  // When axis is a categorial axis, but the type of axis is number or the scale of axis is not \"auto\"\n  if (axis.isCategorical && axis.categoricalDomain) {\n    return axis.categoricalDomain.map(function (entry, index) {\n      return {\n        coordinate: scale(entry) + offset,\n        value: entry,\n        index: index,\n        offset: offset\n      };\n    });\n  }\n  if (scale.ticks && !isAll) {\n    return scale.ticks(axis.tickCount).map(function (entry) {\n      return {\n        coordinate: scale(entry) + offset,\n        value: entry,\n        offset: offset\n      };\n    });\n  }\n\n  // When axis has duplicated text, serial numbers are used to generate scale\n  return scale.domain().map(function (entry, index) {\n    return {\n      coordinate: scale(entry) + offset,\n      value: duplicateDomain ? duplicateDomain[entry] : entry,\n      index: index,\n      offset: offset\n    };\n  });\n};\n\n/**\n * combine the handlers\n * @param  {Function} defaultHandler Internal private handler\n * @param  {Function} childHandler Handler function specified in child component\n * @return {Function}                The combined handler\n */\n\nvar handlerWeakMap = new WeakMap();\nexport var combineEventHandlers = function combineEventHandlers(defaultHandler, childHandler) {\n  if (typeof childHandler !== 'function') {\n    return defaultHandler;\n  }\n  if (!handlerWeakMap.has(defaultHandler)) {\n    handlerWeakMap.set(defaultHandler, new WeakMap());\n  }\n  var childWeakMap = handlerWeakMap.get(defaultHandler);\n  if (childWeakMap.has(childHandler)) {\n    return childWeakMap.get(childHandler);\n  }\n  var combineHandler = function combineHandler() {\n    defaultHandler.apply(void 0, arguments);\n    childHandler.apply(void 0, arguments);\n  };\n  childWeakMap.set(childHandler, combineHandler);\n  return combineHandler;\n};\n\n/**\n * Parse the scale function of axis\n * @param  {Object}   axis          The option of axis\n * @param  {String}   chartType     The displayName of chart\n * @param  {Boolean}  hasBar        if it has a bar\n * @return {object}               The scale function and resolved name\n */\nexport var parseScale = function parseScale(axis, chartType, hasBar) {\n  var scale = axis.scale,\n    type = axis.type,\n    layout = axis.layout,\n    axisType = axis.axisType;\n  if (scale === 'auto') {\n    if (layout === 'radial' && axisType === 'radiusAxis') {\n      return {\n        scale: d3Scales.scaleBand(),\n        realScaleType: 'band'\n      };\n    }\n    if (layout === 'radial' && axisType === 'angleAxis') {\n      return {\n        scale: d3Scales.scaleLinear(),\n        realScaleType: 'linear'\n      };\n    }\n    if (type === 'category' && chartType && (chartType.indexOf('LineChart') >= 0 || chartType.indexOf('AreaChart') >= 0 || chartType.indexOf('ComposedChart') >= 0 && !hasBar)) {\n      return {\n        scale: d3Scales.scalePoint(),\n        realScaleType: 'point'\n      };\n    }\n    if (type === 'category') {\n      return {\n        scale: d3Scales.scaleBand(),\n        realScaleType: 'band'\n      };\n    }\n    return {\n      scale: d3Scales.scaleLinear(),\n      realScaleType: 'linear'\n    };\n  }\n  if (isString(scale)) {\n    var name = \"scale\".concat(upperFirst(scale));\n    return {\n      scale: (d3Scales[name] || d3Scales.scalePoint)(),\n      realScaleType: d3Scales[name] ? name : 'point'\n    };\n  }\n  return isFunction(scale) ? {\n    scale: scale\n  } : {\n    scale: d3Scales.scalePoint(),\n    realScaleType: 'point'\n  };\n};\nvar EPS = 1e-4;\nexport var checkDomainOfScale = function checkDomainOfScale(scale) {\n  var domain = scale.domain();\n  if (!domain || domain.length <= 2) {\n    return;\n  }\n  var len = domain.length;\n  var range = scale.range();\n  var minValue = Math.min(range[0], range[1]) - EPS;\n  var maxValue = Math.max(range[0], range[1]) + EPS;\n  var first = scale(domain[0]);\n  var last = scale(domain[len - 1]);\n  if (first < minValue || first > maxValue || last < minValue || last > maxValue) {\n    scale.domain([domain[0], domain[len - 1]]);\n  }\n};\nexport var findPositionOfBar = function findPositionOfBar(barPosition, child) {\n  if (!barPosition) {\n    return null;\n  }\n  for (var i = 0, len = barPosition.length; i < len; i++) {\n    if (barPosition[i].item === child) {\n      return barPosition[i].position;\n    }\n  }\n  return null;\n};\n\n/**\n * Both value and domain are tuples of two numbers\n * - but the type stays as array of numbers until we have better support in rest of the app\n * @param {Array} value input that will be truncated\n * @param {Array} domain boundaries\n * @returns {Array} tuple of two numbers\n */\nexport var truncateByDomain = function truncateByDomain(value, domain) {\n  if (!domain || domain.length !== 2 || !isNumber(domain[0]) || !isNumber(domain[1])) {\n    return value;\n  }\n  var minValue = Math.min(domain[0], domain[1]);\n  var maxValue = Math.max(domain[0], domain[1]);\n  var result = [value[0], value[1]];\n  if (!isNumber(value[0]) || value[0] < minValue) {\n    result[0] = minValue;\n  }\n  if (!isNumber(value[1]) || value[1] > maxValue) {\n    result[1] = maxValue;\n  }\n  if (result[0] > maxValue) {\n    result[0] = maxValue;\n  }\n  if (result[1] < minValue) {\n    result[1] = minValue;\n  }\n  return result;\n};\n\n/**\n * Stacks all positive numbers above zero and all negative numbers below zero.\n *\n * If all values in the series are positive then this behaves the same as 'none' stacker.\n *\n * @param {Array} series from d3-shape Stack\n * @return {Array} series with applied offset\n */\nexport var offsetSign = function offsetSign(series) {\n  var n = series.length;\n  if (n <= 0) {\n    return;\n  }\n  for (var j = 0, m = series[0].length; j < m; ++j) {\n    var positive = 0;\n    var negative = 0;\n    for (var i = 0; i < n; ++i) {\n      var value = isNan(series[i][j][1]) ? series[i][j][0] : series[i][j][1];\n\n      /* eslint-disable prefer-destructuring, no-param-reassign */\n      if (value >= 0) {\n        series[i][j][0] = positive;\n        series[i][j][1] = positive + value;\n        positive = series[i][j][1];\n      } else {\n        series[i][j][0] = negative;\n        series[i][j][1] = negative + value;\n        negative = series[i][j][1];\n      }\n      /* eslint-enable prefer-destructuring, no-param-reassign */\n    }\n  }\n};\n\n/**\n * Replaces all negative values with zero when stacking data.\n *\n * If all values in the series are positive then this behaves the same as 'none' stacker.\n *\n * @param {Array} series from d3-shape Stack\n * @return {Array} series with applied offset\n */\nexport var offsetPositive = function offsetPositive(series) {\n  var n = series.length;\n  if (n <= 0) {\n    return;\n  }\n  for (var j = 0, m = series[0].length; j < m; ++j) {\n    var positive = 0;\n    for (var i = 0; i < n; ++i) {\n      var value = isNan(series[i][j][1]) ? series[i][j][0] : series[i][j][1];\n\n      /* eslint-disable prefer-destructuring, no-param-reassign */\n      if (value >= 0) {\n        series[i][j][0] = positive;\n        series[i][j][1] = positive + value;\n        positive = series[i][j][1];\n      } else {\n        series[i][j][0] = 0;\n        series[i][j][1] = 0;\n      }\n      /* eslint-enable prefer-destructuring, no-param-reassign */\n    }\n  }\n};\n\n/**\n * Function type to compute offset for stacked data.\n *\n * d3-shape has something fishy going on with its types.\n * In @definitelytyped/d3-shape, this function (the offset accessor) is typed as Series<> => void.\n * However! When I actually open the storybook I can see that the offset accessor actually receives Array<Series<>>.\n * The same I can see in the source code itself:\n * https://github.com/DefinitelyTyped/DefinitelyTyped/discussions/66042\n * That one unfortunately has no types but we can tell it passes three-dimensional array.\n *\n * Which leads me to believe that definitelytyped is wrong on this one.\n * There's open discussion on this topic without much attention:\n * https://github.com/DefinitelyTyped/DefinitelyTyped/discussions/66042\n */\n\nvar STACK_OFFSET_MAP = {\n  sign: offsetSign,\n  // @ts-expect-error definitelytyped types are incorrect\n  expand: stackOffsetExpand,\n  // @ts-expect-error definitelytyped types are incorrect\n  none: stackOffsetNone,\n  // @ts-expect-error definitelytyped types are incorrect\n  silhouette: stackOffsetSilhouette,\n  // @ts-expect-error definitelytyped types are incorrect\n  wiggle: stackOffsetWiggle,\n  positive: offsetPositive\n};\nexport var getStackedData = function getStackedData(data, stackItems, offsetType) {\n  var dataKeys = stackItems.map(function (item) {\n    return item.props.dataKey;\n  });\n  var offsetAccessor = STACK_OFFSET_MAP[offsetType];\n  var stack = shapeStack()\n  // @ts-expect-error stack.keys type wants an array of strings, but we provide array of DataKeys\n  .keys(dataKeys).value(function (d, key) {\n    return +getValueByDataKey(d, key, 0);\n  }).order(stackOrderNone)\n  // @ts-expect-error definitelytyped types are incorrect\n  .offset(offsetAccessor);\n  return stack(data);\n};\nexport var getStackGroupsByAxisId = function getStackGroupsByAxisId(data, _items, numericAxisId, cateAxisId, offsetType, reverseStackOrder) {\n  if (!data) {\n    return null;\n  }\n\n  // reversing items to affect render order (for layering)\n  var items = reverseStackOrder ? _items.reverse() : _items;\n  var parentStackGroupsInitialValue = {};\n  var stackGroups = items.reduce(function (result, item) {\n    var _item$type2;\n    var defaultedProps = (_item$type2 = item.type) !== null && _item$type2 !== void 0 && _item$type2.defaultProps ? _objectSpread(_objectSpread({}, item.type.defaultProps), item.props) : item.props;\n    var stackId = defaultedProps.stackId,\n      hide = defaultedProps.hide;\n    if (hide) {\n      return result;\n    }\n    var axisId = defaultedProps[numericAxisId];\n    var parentGroup = result[axisId] || {\n      hasStack: false,\n      stackGroups: {}\n    };\n    if (isNumOrStr(stackId)) {\n      var childGroup = parentGroup.stackGroups[stackId] || {\n        numericAxisId: numericAxisId,\n        cateAxisId: cateAxisId,\n        items: []\n      };\n      childGroup.items.push(item);\n      parentGroup.hasStack = true;\n      parentGroup.stackGroups[stackId] = childGroup;\n    } else {\n      parentGroup.stackGroups[uniqueId('_stackId_')] = {\n        numericAxisId: numericAxisId,\n        cateAxisId: cateAxisId,\n        items: [item]\n      };\n    }\n    return _objectSpread(_objectSpread({}, result), {}, _defineProperty({}, axisId, parentGroup));\n  }, parentStackGroupsInitialValue);\n  var axisStackGroupsInitialValue = {};\n  return Object.keys(stackGroups).reduce(function (result, axisId) {\n    var group = stackGroups[axisId];\n    if (group.hasStack) {\n      var stackGroupsInitialValue = {};\n      group.stackGroups = Object.keys(group.stackGroups).reduce(function (res, stackId) {\n        var g = group.stackGroups[stackId];\n        return _objectSpread(_objectSpread({}, res), {}, _defineProperty({}, stackId, {\n          numericAxisId: numericAxisId,\n          cateAxisId: cateAxisId,\n          items: g.items,\n          stackedData: getStackedData(data, g.items, offsetType)\n        }));\n      }, stackGroupsInitialValue);\n    }\n    return _objectSpread(_objectSpread({}, result), {}, _defineProperty({}, axisId, group));\n  }, axisStackGroupsInitialValue);\n};\n\n/**\n * Configure the scale function of axis\n * @param {Object} scale The scale function\n * @param {Object} opts  The configuration of axis\n * @return {Object}      null\n */\nexport var getTicksOfScale = function getTicksOfScale(scale, opts) {\n  var realScaleType = opts.realScaleType,\n    type = opts.type,\n    tickCount = opts.tickCount,\n    originalDomain = opts.originalDomain,\n    allowDecimals = opts.allowDecimals;\n  var scaleType = realScaleType || opts.scale;\n  if (scaleType !== 'auto' && scaleType !== 'linear') {\n    return null;\n  }\n  if (tickCount && type === 'number' && originalDomain && (originalDomain[0] === 'auto' || originalDomain[1] === 'auto')) {\n    // Calculate the ticks by the number of grid when the axis is a number axis\n    var domain = scale.domain();\n    if (!domain.length) {\n      return null;\n    }\n    var tickValues = getNiceTickValues(domain, tickCount, allowDecimals);\n    scale.domain([min(tickValues), max(tickValues)]);\n    return {\n      niceTicks: tickValues\n    };\n  }\n  if (tickCount && type === 'number') {\n    var _domain = scale.domain();\n    var _tickValues = getTickValuesFixedDomain(_domain, tickCount, allowDecimals);\n    return {\n      niceTicks: _tickValues\n    };\n  }\n  return null;\n};\nexport function getCateCoordinateOfLine(_ref5) {\n  var axis = _ref5.axis,\n    ticks = _ref5.ticks,\n    bandSize = _ref5.bandSize,\n    entry = _ref5.entry,\n    index = _ref5.index,\n    dataKey = _ref5.dataKey;\n  if (axis.type === 'category') {\n    // find coordinate of category axis by the value of category\n    // @ts-expect-error why does this use direct object access instead of getValueByDataKey?\n    if (!axis.allowDuplicatedCategory && axis.dataKey && !isNil(entry[axis.dataKey])) {\n      // @ts-expect-error why does this use direct object access instead of getValueByDataKey?\n      var matchedTick = findEntryInArray(ticks, 'value', entry[axis.dataKey]);\n      if (matchedTick) {\n        return matchedTick.coordinate + bandSize / 2;\n      }\n    }\n    return ticks[index] ? ticks[index].coordinate + bandSize / 2 : null;\n  }\n  var value = getValueByDataKey(entry, !isNil(dataKey) ? dataKey : axis.dataKey);\n  return !isNil(value) ? axis.scale(value) : null;\n}\nexport var getCateCoordinateOfBar = function getCateCoordinateOfBar(_ref6) {\n  var axis = _ref6.axis,\n    ticks = _ref6.ticks,\n    offset = _ref6.offset,\n    bandSize = _ref6.bandSize,\n    entry = _ref6.entry,\n    index = _ref6.index;\n  if (axis.type === 'category') {\n    return ticks[index] ? ticks[index].coordinate + offset : null;\n  }\n  var value = getValueByDataKey(entry, axis.dataKey, axis.domain[index]);\n  return !isNil(value) ? axis.scale(value) - bandSize / 2 + offset : null;\n};\nexport var getBaseValueOfBar = function getBaseValueOfBar(_ref7) {\n  var numericAxis = _ref7.numericAxis;\n  var domain = numericAxis.scale.domain();\n  if (numericAxis.type === 'number') {\n    var minValue = Math.min(domain[0], domain[1]);\n    var maxValue = Math.max(domain[0], domain[1]);\n    if (minValue <= 0 && maxValue >= 0) {\n      return 0;\n    }\n    if (maxValue < 0) {\n      return maxValue;\n    }\n    return minValue;\n  }\n  return domain[0];\n};\nexport var getStackedDataOfItem = function getStackedDataOfItem(item, stackGroups) {\n  var _item$type3;\n  var defaultedProps = (_item$type3 = item.type) !== null && _item$type3 !== void 0 && _item$type3.defaultProps ? _objectSpread(_objectSpread({}, item.type.defaultProps), item.props) : item.props;\n  var stackId = defaultedProps.stackId;\n  if (isNumOrStr(stackId)) {\n    var group = stackGroups[stackId];\n    if (group) {\n      var itemIndex = group.items.indexOf(item);\n      return itemIndex >= 0 ? group.stackedData[itemIndex] : null;\n    }\n  }\n  return null;\n};\nvar getDomainOfSingle = function getDomainOfSingle(data) {\n  return data.reduce(function (result, entry) {\n    return [min(entry.concat([result[0]]).filter(isNumber)), max(entry.concat([result[1]]).filter(isNumber))];\n  }, [Infinity, -Infinity]);\n};\nexport var getDomainOfStackGroups = function getDomainOfStackGroups(stackGroups, startIndex, endIndex) {\n  return Object.keys(stackGroups).reduce(function (result, stackId) {\n    var group = stackGroups[stackId];\n    var stackedData = group.stackedData;\n    var domain = stackedData.reduce(function (res, entry) {\n      var s = getDomainOfSingle(entry.slice(startIndex, endIndex + 1));\n      return [Math.min(res[0], s[0]), Math.max(res[1], s[1])];\n    }, [Infinity, -Infinity]);\n    return [Math.min(domain[0], result[0]), Math.max(domain[1], result[1])];\n  }, [Infinity, -Infinity]).map(function (result) {\n    return result === Infinity || result === -Infinity ? 0 : result;\n  });\n};\nexport var MIN_VALUE_REG = /^dataMin[\\s]*-[\\s]*([0-9]+([.]{1}[0-9]+){0,1})$/;\nexport var MAX_VALUE_REG = /^dataMax[\\s]*\\+[\\s]*([0-9]+([.]{1}[0-9]+){0,1})$/;\nexport var parseSpecifiedDomain = function parseSpecifiedDomain(specifiedDomain, dataDomain, allowDataOverflow) {\n  if (isFunction(specifiedDomain)) {\n    return specifiedDomain(dataDomain, allowDataOverflow);\n  }\n  if (!Array.isArray(specifiedDomain)) {\n    return dataDomain;\n  }\n  var domain = [];\n\n  /* eslint-disable prefer-destructuring */\n  if (isNumber(specifiedDomain[0])) {\n    domain[0] = allowDataOverflow ? specifiedDomain[0] : Math.min(specifiedDomain[0], dataDomain[0]);\n  } else if (MIN_VALUE_REG.test(specifiedDomain[0])) {\n    var value = +MIN_VALUE_REG.exec(specifiedDomain[0])[1];\n    domain[0] = dataDomain[0] - value;\n  } else if (isFunction(specifiedDomain[0])) {\n    domain[0] = specifiedDomain[0](dataDomain[0]);\n  } else {\n    domain[0] = dataDomain[0];\n  }\n  if (isNumber(specifiedDomain[1])) {\n    domain[1] = allowDataOverflow ? specifiedDomain[1] : Math.max(specifiedDomain[1], dataDomain[1]);\n  } else if (MAX_VALUE_REG.test(specifiedDomain[1])) {\n    var _value = +MAX_VALUE_REG.exec(specifiedDomain[1])[1];\n    domain[1] = dataDomain[1] + _value;\n  } else if (isFunction(specifiedDomain[1])) {\n    domain[1] = specifiedDomain[1](dataDomain[1]);\n  } else {\n    domain[1] = dataDomain[1];\n  }\n  /* eslint-enable prefer-destructuring */\n\n  return domain;\n};\n\n/**\n * Calculate the size between two category\n * @param  {Object} axis  The options of axis\n * @param  {Array}  ticks The ticks of axis\n * @param  {Boolean} isBar if items in axis are bars\n * @return {Number} Size\n */\nexport var getBandSizeOfAxis = function getBandSizeOfAxis(axis, ticks, isBar) {\n  // @ts-expect-error we need to rethink scale type\n  if (axis && axis.scale && axis.scale.bandwidth) {\n    // @ts-expect-error we need to rethink scale type\n    var bandWidth = axis.scale.bandwidth();\n    if (!isBar || bandWidth > 0) {\n      return bandWidth;\n    }\n  }\n  if (axis && ticks && ticks.length >= 2) {\n    var orderedTicks = sortBy(ticks, function (o) {\n      return o.coordinate;\n    });\n    var bandSize = Infinity;\n    for (var i = 1, len = orderedTicks.length; i < len; i++) {\n      var cur = orderedTicks[i];\n      var prev = orderedTicks[i - 1];\n      bandSize = Math.min((cur.coordinate || 0) - (prev.coordinate || 0), bandSize);\n    }\n    return bandSize === Infinity ? 0 : bandSize;\n  }\n  return isBar ? undefined : 0;\n};\n/**\n * parse the domain of a category axis when a domain is specified\n * @param   {Array}        specifiedDomain  The domain specified by users\n * @param   {Array}        calculatedDomain The domain calculated by dateKey\n * @param   {ReactElement} axisChild        The axis ReactElement\n * @returns {Array}        domains\n */\nexport var parseDomainOfCategoryAxis = function parseDomainOfCategoryAxis(specifiedDomain, calculatedDomain, axisChild) {\n  if (!specifiedDomain || !specifiedDomain.length) {\n    return calculatedDomain;\n  }\n  if (isEqual(specifiedDomain, get(axisChild, 'type.defaultProps.domain'))) {\n    return calculatedDomain;\n  }\n  return specifiedDomain;\n};\nexport var getTooltipItem = function getTooltipItem(graphicalItem, payload) {\n  var defaultedProps = graphicalItem.type.defaultProps ? _objectSpread(_objectSpread({}, graphicalItem.type.defaultProps), graphicalItem.props) : graphicalItem.props;\n  var dataKey = defaultedProps.dataKey,\n    name = defaultedProps.name,\n    unit = defaultedProps.unit,\n    formatter = defaultedProps.formatter,\n    tooltipType = defaultedProps.tooltipType,\n    chartType = defaultedProps.chartType,\n    hide = defaultedProps.hide;\n  return _objectSpread(_objectSpread({}, filterProps(graphicalItem, false)), {}, {\n    dataKey: dataKey,\n    unit: unit,\n    formatter: formatter,\n    name: name || dataKey,\n    color: getMainColorOfGraphicItem(graphicalItem),\n    value: getValueByDataKey(payload, dataKey),\n    type: tooltipType,\n    payload: payload,\n    chartType: chartType,\n    hide: hide\n  });\n};"], "mappings": "AAAA,SAASA,OAAOA,CAACC,CAAC,EAAE;EAAE,yBAAyB;;EAAE,OAAOD,OAAO,GAAG,UAAU,IAAI,OAAOE,MAAM,IAAI,QAAQ,IAAI,OAAOA,MAAM,CAACC,QAAQ,GAAG,UAAUF,CAAC,EAAE;IAAE,OAAO,OAAOA,CAAC;EAAE,CAAC,GAAG,UAAUA,CAAC,EAAE;IAAE,OAAOA,CAAC,IAAI,UAAU,IAAI,OAAOC,MAAM,IAAID,CAAC,CAACG,WAAW,KAAKF,MAAM,IAAID,CAAC,KAAKC,MAAM,CAACG,SAAS,GAAG,QAAQ,GAAG,OAAOJ,CAAC;EAAE,CAAC,EAAED,OAAO,CAACC,CAAC,CAAC;AAAE;AAC7T,SAASK,kBAAkBA,CAACC,GAAG,EAAE;EAAE,OAAOC,kBAAkB,CAACD,GAAG,CAAC,IAAIE,gBAAgB,CAACF,GAAG,CAAC,IAAIG,2BAA2B,CAACH,GAAG,CAAC,IAAII,kBAAkB,CAAC,CAAC;AAAE;AACxJ,SAASA,kBAAkBA,CAAA,EAAG;EAAE,MAAM,IAAIC,SAAS,CAAC,sIAAsI,CAAC;AAAE;AAC7L,SAASF,2BAA2BA,CAACT,CAAC,EAAEY,MAAM,EAAE;EAAE,IAAI,CAACZ,CAAC,EAAE;EAAQ,IAAI,OAAOA,CAAC,KAAK,QAAQ,EAAE,OAAOa,iBAAiB,CAACb,CAAC,EAAEY,MAAM,CAAC;EAAE,IAAIE,CAAC,GAAGC,MAAM,CAACX,SAAS,CAACY,QAAQ,CAACC,IAAI,CAACjB,CAAC,CAAC,CAACkB,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;EAAE,IAAIJ,CAAC,KAAK,QAAQ,IAAId,CAAC,CAACG,WAAW,EAAEW,CAAC,GAAGd,CAAC,CAACG,WAAW,CAACgB,IAAI;EAAE,IAAIL,CAAC,KAAK,KAAK,IAAIA,CAAC,KAAK,KAAK,EAAE,OAAOM,KAAK,CAACC,IAAI,CAACrB,CAAC,CAAC;EAAE,IAAIc,CAAC,KAAK,WAAW,IAAI,0CAA0C,CAACQ,IAAI,CAACR,CAAC,CAAC,EAAE,OAAOD,iBAAiB,CAACb,CAAC,EAAEY,MAAM,CAAC;AAAE;AAC/Z,SAASJ,gBAAgBA,CAACe,IAAI,EAAE;EAAE,IAAI,OAAOtB,MAAM,KAAK,WAAW,IAAIsB,IAAI,CAACtB,MAAM,CAACC,QAAQ,CAAC,IAAI,IAAI,IAAIqB,IAAI,CAAC,YAAY,CAAC,IAAI,IAAI,EAAE,OAAOH,KAAK,CAACC,IAAI,CAACE,IAAI,CAAC;AAAE;AAC7J,SAAShB,kBAAkBA,CAACD,GAAG,EAAE;EAAE,IAAIc,KAAK,CAACI,OAAO,CAAClB,GAAG,CAAC,EAAE,OAAOO,iBAAiB,CAACP,GAAG,CAAC;AAAE;AAC1F,SAASO,iBAAiBA,CAACP,GAAG,EAAEmB,GAAG,EAAE;EAAE,IAAIA,GAAG,IAAI,IAAI,IAAIA,GAAG,GAAGnB,GAAG,CAACoB,MAAM,EAAED,GAAG,GAAGnB,GAAG,CAACoB,MAAM;EAAE,KAAK,IAAIC,CAAC,GAAG,CAAC,EAAEC,IAAI,GAAG,IAAIR,KAAK,CAACK,GAAG,CAAC,EAAEE,CAAC,GAAGF,GAAG,EAAEE,CAAC,EAAE,EAAEC,IAAI,CAACD,CAAC,CAAC,GAAGrB,GAAG,CAACqB,CAAC,CAAC;EAAE,OAAOC,IAAI;AAAE;AAClL,SAASC,OAAOA,CAACC,CAAC,EAAEC,CAAC,EAAE;EAAE,IAAIC,CAAC,GAAGjB,MAAM,CAACkB,IAAI,CAACH,CAAC,CAAC;EAAE,IAAIf,MAAM,CAACmB,qBAAqB,EAAE;IAAE,IAAIlC,CAAC,GAAGe,MAAM,CAACmB,qBAAqB,CAACJ,CAAC,CAAC;IAAEC,CAAC,KAAK/B,CAAC,GAAGA,CAAC,CAACmC,MAAM,CAAC,UAAUJ,CAAC,EAAE;MAAE,OAAOhB,MAAM,CAACqB,wBAAwB,CAACN,CAAC,EAAEC,CAAC,CAAC,CAACM,UAAU;IAAE,CAAC,CAAC,CAAC,EAAEL,CAAC,CAACM,IAAI,CAACC,KAAK,CAACP,CAAC,EAAEhC,CAAC,CAAC;EAAE;EAAE,OAAOgC,CAAC;AAAE;AAC9P,SAASQ,aAAaA,CAACV,CAAC,EAAE;EAAE,KAAK,IAAIC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGU,SAAS,CAACf,MAAM,EAAEK,CAAC,EAAE,EAAE;IAAE,IAAIC,CAAC,GAAG,IAAI,IAAIS,SAAS,CAACV,CAAC,CAAC,GAAGU,SAAS,CAACV,CAAC,CAAC,GAAG,CAAC,CAAC;IAAEA,CAAC,GAAG,CAAC,GAAGF,OAAO,CAACd,MAAM,CAACiB,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAACU,OAAO,CAAC,UAAUX,CAAC,EAAE;MAAEY,eAAe,CAACb,CAAC,EAAEC,CAAC,EAAEC,CAAC,CAACD,CAAC,CAAC,CAAC;IAAE,CAAC,CAAC,GAAGhB,MAAM,CAAC6B,yBAAyB,GAAG7B,MAAM,CAAC8B,gBAAgB,CAACf,CAAC,EAAEf,MAAM,CAAC6B,yBAAyB,CAACZ,CAAC,CAAC,CAAC,GAAGH,OAAO,CAACd,MAAM,CAACiB,CAAC,CAAC,CAAC,CAACU,OAAO,CAAC,UAAUX,CAAC,EAAE;MAAEhB,MAAM,CAAC+B,cAAc,CAAChB,CAAC,EAAEC,CAAC,EAAEhB,MAAM,CAACqB,wBAAwB,CAACJ,CAAC,EAAED,CAAC,CAAC,CAAC;IAAE,CAAC,CAAC;EAAE;EAAE,OAAOD,CAAC;AAAE;AACtb,SAASa,eAAeA,CAACI,GAAG,EAAEC,GAAG,EAAEC,KAAK,EAAE;EAAED,GAAG,GAAGE,cAAc,CAACF,GAAG,CAAC;EAAE,IAAIA,GAAG,IAAID,GAAG,EAAE;IAAEhC,MAAM,CAAC+B,cAAc,CAACC,GAAG,EAAEC,GAAG,EAAE;MAAEC,KAAK,EAAEA,KAAK;MAAEZ,UAAU,EAAE,IAAI;MAAEc,YAAY,EAAE,IAAI;MAAEC,QAAQ,EAAE;IAAK,CAAC,CAAC;EAAE,CAAC,MAAM;IAAEL,GAAG,CAACC,GAAG,CAAC,GAAGC,KAAK;EAAE;EAAE,OAAOF,GAAG;AAAE;AAC3O,SAASG,cAAcA,CAAClB,CAAC,EAAE;EAAE,IAAIL,CAAC,GAAG0B,YAAY,CAACrB,CAAC,EAAE,QAAQ,CAAC;EAAE,OAAO,QAAQ,IAAIjC,OAAO,CAAC4B,CAAC,CAAC,GAAGA,CAAC,GAAGA,CAAC,GAAG,EAAE;AAAE;AAC5G,SAAS0B,YAAYA,CAACrB,CAAC,EAAED,CAAC,EAAE;EAAE,IAAI,QAAQ,IAAIhC,OAAO,CAACiC,CAAC,CAAC,IAAI,CAACA,CAAC,EAAE,OAAOA,CAAC;EAAE,IAAIF,CAAC,GAAGE,CAAC,CAAC/B,MAAM,CAACqD,WAAW,CAAC;EAAE,IAAI,KAAK,CAAC,KAAKxB,CAAC,EAAE;IAAE,IAAIH,CAAC,GAAGG,CAAC,CAACb,IAAI,CAACe,CAAC,EAAED,CAAC,IAAI,SAAS,CAAC;IAAE,IAAI,QAAQ,IAAIhC,OAAO,CAAC4B,CAAC,CAAC,EAAE,OAAOA,CAAC;IAAE,MAAM,IAAIhB,SAAS,CAAC,8CAA8C,CAAC;EAAE;EAAE,OAAO,CAAC,QAAQ,KAAKoB,CAAC,GAAGwB,MAAM,GAAGC,MAAM,EAAExB,CAAC,CAAC;AAAE;AAC3T,OAAO,KAAKyB,QAAQ,MAAM,yBAAyB;AACnD,SAASC,KAAK,IAAIC,UAAU,EAAEC,iBAAiB,EAAEC,eAAe,EAAEC,qBAAqB,EAAEC,iBAAiB,EAAEC,cAAc,QAAQ,yBAAyB;AAC3J,OAAOC,GAAG,MAAM,YAAY;AAC5B,OAAOC,GAAG,MAAM,YAAY;AAC5B,OAAOC,KAAK,MAAM,cAAc;AAChC,OAAOC,UAAU,MAAM,mBAAmB;AAC1C,OAAOC,QAAQ,MAAM,iBAAiB;AACtC,OAAOC,GAAG,MAAM,YAAY;AAC5B,OAAOC,OAAO,MAAM,gBAAgB;AACpC,OAAOC,KAAK,MAAM,cAAc;AAChC,OAAOC,UAAU,MAAM,mBAAmB;AAC1C,OAAOC,OAAO,MAAM,gBAAgB;AACpC,OAAOC,MAAM,MAAM,eAAe;AAClC,SAASC,iBAAiB,EAAEC,wBAAwB,QAAQ,gBAAgB;AAC5E,SAASC,QAAQ,QAAQ,uBAAuB;AAChD,SAASC,gBAAgB,EAAEC,eAAe,EAAEC,QAAQ,EAAEC,UAAU,EAAEC,QAAQ,EAAEC,QAAQ,QAAQ,aAAa;AACzG,SAASC,WAAW,EAAEC,aAAa,EAAEC,cAAc,QAAQ,cAAc;AACzE;AACA;;AAEA,SAASC,cAAc,QAAQ,kBAAkB;;AAEjD;AACA,SAASA,cAAc;AACvB,OAAO,SAASC,iBAAiBA,CAAC1C,GAAG,EAAE2C,OAAO,EAAEC,YAAY,EAAE;EAC5D,IAAIxB,KAAK,CAACpB,GAAG,CAAC,IAAIoB,KAAK,CAACuB,OAAO,CAAC,EAAE;IAChC,OAAOC,YAAY;EACrB;EACA,IAAIT,UAAU,CAACQ,OAAO,CAAC,EAAE;IACvB,OAAOpB,GAAG,CAACvB,GAAG,EAAE2C,OAAO,EAAEC,YAAY,CAAC;EACxC;EACA,IAAIvB,UAAU,CAACsB,OAAO,CAAC,EAAE;IACvB,OAAOA,OAAO,CAAC3C,GAAG,CAAC;EACrB;EACA,OAAO4C,YAAY;AACrB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO,SAASC,oBAAoBA,CAACC,IAAI,EAAE7C,GAAG,EAAE8C,IAAI,EAAEC,SAAS,EAAE;EAC/D,IAAIC,WAAW,GAAGzB,OAAO,CAACsB,IAAI,EAAE,UAAUI,KAAK,EAAE;IAC/C,OAAOR,iBAAiB,CAACQ,KAAK,EAAEjD,GAAG,CAAC;EACtC,CAAC,CAAC;EACF,IAAI8C,IAAI,KAAK,QAAQ,EAAE;IACrB;IACA,IAAII,MAAM,GAAGF,WAAW,CAAC7D,MAAM,CAAC,UAAU8D,KAAK,EAAE;MAC/C,OAAOhB,QAAQ,CAACgB,KAAK,CAAC,IAAIE,UAAU,CAACF,KAAK,CAAC;IAC7C,CAAC,CAAC;IACF,OAAOC,MAAM,CAACxE,MAAM,GAAG,CAACwC,GAAG,CAACgC,MAAM,CAAC,EAAEjC,GAAG,CAACiC,MAAM,CAAC,CAAC,GAAG,CAACE,QAAQ,EAAE,CAACA,QAAQ,CAAC;EAC3E;EACA,IAAIC,YAAY,GAAGN,SAAS,GAAGC,WAAW,CAAC7D,MAAM,CAAC,UAAU8D,KAAK,EAAE;IACjE,OAAO,CAAC9B,KAAK,CAAC8B,KAAK,CAAC;EACtB,CAAC,CAAC,GAAGD,WAAW;;EAEhB;EACA,OAAOK,YAAY,CAACC,GAAG,CAAC,UAAUL,KAAK,EAAE;IACvC,OAAOf,UAAU,CAACe,KAAK,CAAC,IAAIA,KAAK,YAAYM,IAAI,GAAGN,KAAK,GAAG,EAAE;EAChE,CAAC,CAAC;AACJ;AACA,OAAO,IAAIO,wBAAwB,GAAG,SAASA,wBAAwBA,CAACC,UAAU,EAAE;EAClF,IAAIC,aAAa;EACjB,IAAIC,KAAK,GAAGlE,SAAS,CAACf,MAAM,GAAG,CAAC,IAAIe,SAAS,CAAC,CAAC,CAAC,KAAKmE,SAAS,GAAGnE,SAAS,CAAC,CAAC,CAAC,GAAG,EAAE;EAClF,IAAIoE,aAAa,GAAGpE,SAAS,CAACf,MAAM,GAAG,CAAC,GAAGe,SAAS,CAAC,CAAC,CAAC,GAAGmE,SAAS;EACnE,IAAIE,IAAI,GAAGrE,SAAS,CAACf,MAAM,GAAG,CAAC,GAAGe,SAAS,CAAC,CAAC,CAAC,GAAGmE,SAAS;EAC1D,IAAIG,KAAK,GAAG,CAAC,CAAC;EACd,IAAItF,GAAG,GAAG,CAACiF,aAAa,GAAGC,KAAK,KAAK,IAAI,IAAIA,KAAK,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,KAAK,CAACjF,MAAM,MAAM,IAAI,IAAIgF,aAAa,KAAK,KAAK,CAAC,GAAGA,aAAa,GAAG,CAAC;;EAE/I;EACA,IAAIjF,GAAG,IAAI,CAAC,EAAE;IACZ,OAAO,CAAC;EACV;EACA,IAAIqF,IAAI,IAAIA,IAAI,CAACE,QAAQ,KAAK,WAAW,IAAIC,IAAI,CAACC,GAAG,CAACD,IAAI,CAACC,GAAG,CAACJ,IAAI,CAACK,KAAK,CAAC,CAAC,CAAC,GAAGL,IAAI,CAACK,KAAK,CAAC,CAAC,CAAC,CAAC,GAAG,GAAG,CAAC,IAAI,IAAI,EAAE;IAC5G,IAAIA,KAAK,GAAGL,IAAI,CAACK,KAAK;IACtB;IACA,KAAK,IAAIxF,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGF,GAAG,EAAEE,CAAC,EAAE,EAAE;MAC5B,IAAIyF,MAAM,GAAGzF,CAAC,GAAG,CAAC,GAAGkF,aAAa,CAAClF,CAAC,GAAG,CAAC,CAAC,CAAC8E,UAAU,GAAGI,aAAa,CAACpF,GAAG,GAAG,CAAC,CAAC,CAACgF,UAAU;MACxF,IAAIY,GAAG,GAAGR,aAAa,CAAClF,CAAC,CAAC,CAAC8E,UAAU;MACrC,IAAIa,KAAK,GAAG3F,CAAC,IAAIF,GAAG,GAAG,CAAC,GAAGoF,aAAa,CAAC,CAAC,CAAC,CAACJ,UAAU,GAAGI,aAAa,CAAClF,CAAC,GAAG,CAAC,CAAC,CAAC8E,UAAU;MACxF,IAAIc,kBAAkB,GAAG,KAAK,CAAC;MAC/B,IAAIpC,QAAQ,CAACkC,GAAG,GAAGD,MAAM,CAAC,KAAKjC,QAAQ,CAACmC,KAAK,GAAGD,GAAG,CAAC,EAAE;QACpD,IAAIG,YAAY,GAAG,EAAE;QACrB,IAAIrC,QAAQ,CAACmC,KAAK,GAAGD,GAAG,CAAC,KAAKlC,QAAQ,CAACgC,KAAK,CAAC,CAAC,CAAC,GAAGA,KAAK,CAAC,CAAC,CAAC,CAAC,EAAE;UAC3DI,kBAAkB,GAAGD,KAAK;UAC1B,IAAIG,UAAU,GAAGJ,GAAG,GAAGF,KAAK,CAAC,CAAC,CAAC,GAAGA,KAAK,CAAC,CAAC,CAAC;UAC1CK,YAAY,CAAC,CAAC,CAAC,GAAGP,IAAI,CAAC/C,GAAG,CAACuD,UAAU,EAAE,CAACA,UAAU,GAAGL,MAAM,IAAI,CAAC,CAAC;UACjEI,YAAY,CAAC,CAAC,CAAC,GAAGP,IAAI,CAAChD,GAAG,CAACwD,UAAU,EAAE,CAACA,UAAU,GAAGL,MAAM,IAAI,CAAC,CAAC;QACnE,CAAC,MAAM;UACLG,kBAAkB,GAAGH,MAAM;UAC3B,IAAIM,YAAY,GAAGJ,KAAK,GAAGH,KAAK,CAAC,CAAC,CAAC,GAAGA,KAAK,CAAC,CAAC,CAAC;UAC9CK,YAAY,CAAC,CAAC,CAAC,GAAGP,IAAI,CAAC/C,GAAG,CAACmD,GAAG,EAAE,CAACK,YAAY,GAAGL,GAAG,IAAI,CAAC,CAAC;UACzDG,YAAY,CAAC,CAAC,CAAC,GAAGP,IAAI,CAAChD,GAAG,CAACoD,GAAG,EAAE,CAACK,YAAY,GAAGL,GAAG,IAAI,CAAC,CAAC;QAC3D;QACA,IAAIM,YAAY,GAAG,CAACV,IAAI,CAAC/C,GAAG,CAACmD,GAAG,EAAE,CAACE,kBAAkB,GAAGF,GAAG,IAAI,CAAC,CAAC,EAAEJ,IAAI,CAAChD,GAAG,CAACoD,GAAG,EAAE,CAACE,kBAAkB,GAAGF,GAAG,IAAI,CAAC,CAAC,CAAC;QACjH,IAAIZ,UAAU,GAAGkB,YAAY,CAAC,CAAC,CAAC,IAAIlB,UAAU,IAAIkB,YAAY,CAAC,CAAC,CAAC,IAAIlB,UAAU,IAAIe,YAAY,CAAC,CAAC,CAAC,IAAIf,UAAU,IAAIe,YAAY,CAAC,CAAC,CAAC,EAAE;UACnIT,KAAK,GAAGF,aAAa,CAAClF,CAAC,CAAC,CAACoF,KAAK;UAC9B;QACF;MACF,CAAC,MAAM;QACL,IAAIa,QAAQ,GAAGX,IAAI,CAAC/C,GAAG,CAACkD,MAAM,EAAEE,KAAK,CAAC;QACtC,IAAIO,QAAQ,GAAGZ,IAAI,CAAChD,GAAG,CAACmD,MAAM,EAAEE,KAAK,CAAC;QACtC,IAAIb,UAAU,GAAG,CAACmB,QAAQ,GAAGP,GAAG,IAAI,CAAC,IAAIZ,UAAU,IAAI,CAACoB,QAAQ,GAAGR,GAAG,IAAI,CAAC,EAAE;UAC3EN,KAAK,GAAGF,aAAa,CAAClF,CAAC,CAAC,CAACoF,KAAK;UAC9B;QACF;MACF;IACF;EACF,CAAC,MAAM;IACL;IACA,KAAK,IAAIe,EAAE,GAAG,CAAC,EAAEA,EAAE,GAAGrG,GAAG,EAAEqG,EAAE,EAAE,EAAE;MAC/B,IAAIA,EAAE,KAAK,CAAC,IAAIrB,UAAU,IAAI,CAACE,KAAK,CAACmB,EAAE,CAAC,CAACrB,UAAU,GAAGE,KAAK,CAACmB,EAAE,GAAG,CAAC,CAAC,CAACrB,UAAU,IAAI,CAAC,IAAIqB,EAAE,GAAG,CAAC,IAAIA,EAAE,GAAGrG,GAAG,GAAG,CAAC,IAAIgF,UAAU,GAAG,CAACE,KAAK,CAACmB,EAAE,CAAC,CAACrB,UAAU,GAAGE,KAAK,CAACmB,EAAE,GAAG,CAAC,CAAC,CAACrB,UAAU,IAAI,CAAC,IAAIA,UAAU,IAAI,CAACE,KAAK,CAACmB,EAAE,CAAC,CAACrB,UAAU,GAAGE,KAAK,CAACmB,EAAE,GAAG,CAAC,CAAC,CAACrB,UAAU,IAAI,CAAC,IAAIqB,EAAE,KAAKrG,GAAG,GAAG,CAAC,IAAIgF,UAAU,GAAG,CAACE,KAAK,CAACmB,EAAE,CAAC,CAACrB,UAAU,GAAGE,KAAK,CAACmB,EAAE,GAAG,CAAC,CAAC,CAACrB,UAAU,IAAI,CAAC,EAAE;QAClVM,KAAK,GAAGJ,KAAK,CAACmB,EAAE,CAAC,CAACf,KAAK;QACvB;MACF;IACF;EACF;EACA,OAAOA,KAAK;AACd,CAAC;;AAED;AACA;AACA;AACA;AACA;AACA,OAAO,IAAIgB,yBAAyB,GAAG,SAASA,yBAAyBA,CAACC,IAAI,EAAE;EAC9E,IAAIC,UAAU;EACd,IAAIC,IAAI,GAAGF,IAAI;IACbG,WAAW,GAAGD,IAAI,CAACpC,IAAI,CAACqC,WAAW,CAAC,CAAC;EACvC,IAAIC,cAAc,GAAG,CAACH,UAAU,GAAGD,IAAI,CAAClC,IAAI,MAAM,IAAI,IAAImC,UAAU,KAAK,KAAK,CAAC,IAAIA,UAAU,CAACI,YAAY,GAAG7F,aAAa,CAACA,aAAa,CAAC,CAAC,CAAC,EAAEwF,IAAI,CAAClC,IAAI,CAACuC,YAAY,CAAC,EAAEL,IAAI,CAACM,KAAK,CAAC,GAAGN,IAAI,CAACM,KAAK;EAC9L,IAAIC,MAAM,GAAGH,cAAc,CAACG,MAAM;IAChCC,IAAI,GAAGJ,cAAc,CAACI,IAAI;EAC5B,IAAIC,MAAM;EACV,QAAQN,WAAW;IACjB,KAAK,MAAM;MACTM,MAAM,GAAGF,MAAM;MACf;IACF,KAAK,MAAM;IACX,KAAK,OAAO;MACVE,MAAM,GAAGF,MAAM,IAAIA,MAAM,KAAK,MAAM,GAAGA,MAAM,GAAGC,IAAI;MACpD;IACF;MACEC,MAAM,GAAGD,IAAI;MACb;EACJ;EACA,OAAOC,MAAM;AACf,CAAC;AACD;AACA;AACA;AACA;AACA;AACA,OAAO,IAAIC,cAAc,GAAG,SAASA,cAAcA,CAACC,KAAK,EAAE;EACzD,IAAIC,UAAU,GAAGD,KAAK,CAACE,OAAO;IAC5BC,SAAS,GAAGH,KAAK,CAACG,SAAS;IAC3BC,iBAAiB,GAAGJ,KAAK,CAACK,WAAW;IACrCA,WAAW,GAAGD,iBAAiB,KAAK,KAAK,CAAC,GAAG,CAAC,CAAC,GAAGA,iBAAiB;EACrE,IAAI,CAACC,WAAW,EAAE;IAChB,OAAO,CAAC,CAAC;EACX;EACA,IAAIP,MAAM,GAAG,CAAC,CAAC;EACf,IAAIQ,cAAc,GAAGlI,MAAM,CAACkB,IAAI,CAAC+G,WAAW,CAAC;EAC7C,KAAK,IAAIrH,CAAC,GAAG,CAAC,EAAEF,GAAG,GAAGwH,cAAc,CAACvH,MAAM,EAAEC,CAAC,GAAGF,GAAG,EAAEE,CAAC,EAAE,EAAE;IACzD,IAAIuH,GAAG,GAAGF,WAAW,CAACC,cAAc,CAACtH,CAAC,CAAC,CAAC,CAACqH,WAAW;IACpD,IAAIG,QAAQ,GAAGpI,MAAM,CAACkB,IAAI,CAACiH,GAAG,CAAC;IAC/B,KAAK,IAAIE,CAAC,GAAG,CAAC,EAAEC,IAAI,GAAGF,QAAQ,CAACzH,MAAM,EAAE0H,CAAC,GAAGC,IAAI,EAAED,CAAC,EAAE,EAAE;MACrD,IAAIE,eAAe,GAAGJ,GAAG,CAACC,QAAQ,CAACC,CAAC,CAAC,CAAC;QACpCG,KAAK,GAAGD,eAAe,CAACC,KAAK;QAC7BC,UAAU,GAAGF,eAAe,CAACE,UAAU;MACzC,IAAIC,QAAQ,GAAGF,KAAK,CAACpH,MAAM,CAAC,UAAU6F,IAAI,EAAE;QAC1C,OAAOzC,cAAc,CAACyC,IAAI,CAAClC,IAAI,CAAC,CAAC4D,OAAO,CAAC,KAAK,CAAC,IAAI,CAAC;MACtD,CAAC,CAAC;MACF,IAAID,QAAQ,IAAIA,QAAQ,CAAC/H,MAAM,EAAE;QAC/B,IAAIiI,mBAAmB,GAAGF,QAAQ,CAAC,CAAC,CAAC,CAAC3D,IAAI,CAACuC,YAAY;QACvD,IAAIuB,YAAY,GAAGD,mBAAmB,KAAK/C,SAAS,GAAGpE,aAAa,CAACA,aAAa,CAAC,CAAC,CAAC,EAAEmH,mBAAmB,CAAC,EAAEF,QAAQ,CAAC,CAAC,CAAC,CAACnB,KAAK,CAAC,GAAGmB,QAAQ,CAAC,CAAC,CAAC,CAACnB,KAAK;QACnJ,IAAIuB,QAAQ,GAAGD,YAAY,CAACf,OAAO;QACnC,IAAIiB,MAAM,GAAGF,YAAY,CAACJ,UAAU,CAAC;QACrC,IAAI,CAACf,MAAM,CAACqB,MAAM,CAAC,EAAE;UACnBrB,MAAM,CAACqB,MAAM,CAAC,GAAG,EAAE;QACrB;QACA,IAAIjB,OAAO,GAAG1E,KAAK,CAAC0F,QAAQ,CAAC,GAAGjB,UAAU,GAAGiB,QAAQ;QACrDpB,MAAM,CAACqB,MAAM,CAAC,CAACxH,IAAI,CAAC;UAClB0F,IAAI,EAAEyB,QAAQ,CAAC,CAAC,CAAC;UACjBM,SAAS,EAAEN,QAAQ,CAACvI,KAAK,CAAC,CAAC,CAAC;UAC5B2H,OAAO,EAAE1E,KAAK,CAAC0E,OAAO,CAAC,GAAGjC,SAAS,GAAG5B,eAAe,CAAC6D,OAAO,EAAEC,SAAS,EAAE,CAAC;QAC7E,CAAC,CAAC;MACJ;IACF;EACF;EACA,OAAOL,MAAM;AACf,CAAC;AACD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO,IAAIuB,cAAc,GAAG,SAASA,cAAcA,CAACC,KAAK,EAAE;EACzD,IAAIC,MAAM,GAAGD,KAAK,CAACC,MAAM;IACvBC,cAAc,GAAGF,KAAK,CAACE,cAAc;IACrCC,QAAQ,GAAGH,KAAK,CAACG,QAAQ;IACzBC,cAAc,GAAGJ,KAAK,CAACK,QAAQ;IAC/BA,QAAQ,GAAGD,cAAc,KAAK,KAAK,CAAC,GAAG,EAAE,GAAGA,cAAc;IAC1DE,UAAU,GAAGN,KAAK,CAACM,UAAU;EAC/B,IAAI9I,GAAG,GAAG6I,QAAQ,CAAC5I,MAAM;EACzB,IAAID,GAAG,GAAG,CAAC,EAAE,OAAO,IAAI;EACxB,IAAI+I,UAAU,GAAGxF,eAAe,CAACkF,MAAM,EAAEE,QAAQ,EAAE,CAAC,EAAE,IAAI,CAAC;EAC3D,IAAI3B,MAAM;EACV,IAAIgC,YAAY,GAAG,EAAE;;EAErB;EACA,IAAIH,QAAQ,CAAC,CAAC,CAAC,CAACzB,OAAO,KAAK,CAACyB,QAAQ,CAAC,CAAC,CAAC,CAACzB,OAAO,EAAE;IAChD,IAAI6B,OAAO,GAAG,KAAK;IACnB,IAAIC,WAAW,GAAGP,QAAQ,GAAG3I,GAAG;IAChC;IACA,IAAImJ,GAAG,GAAGN,QAAQ,CAACO,MAAM,CAAC,UAAUC,GAAG,EAAE7E,KAAK,EAAE;MAC9C,OAAO6E,GAAG,GAAG7E,KAAK,CAAC4C,OAAO,IAAI,CAAC;IACjC,CAAC,EAAE,CAAC,CAAC;IACL+B,GAAG,IAAI,CAACnJ,GAAG,GAAG,CAAC,IAAI+I,UAAU;IAC7B,IAAII,GAAG,IAAIR,QAAQ,EAAE;MACnBQ,GAAG,IAAI,CAACnJ,GAAG,GAAG,CAAC,IAAI+I,UAAU;MAC7BA,UAAU,GAAG,CAAC;IAChB;IACA,IAAII,GAAG,IAAIR,QAAQ,IAAIO,WAAW,GAAG,CAAC,EAAE;MACtCD,OAAO,GAAG,IAAI;MACdC,WAAW,IAAI,GAAG;MAClBC,GAAG,GAAGnJ,GAAG,GAAGkJ,WAAW;IACzB;IACA,IAAII,MAAM,GAAG,CAACX,QAAQ,GAAGQ,GAAG,IAAI,CAAC,IAAI,CAAC;IACtC,IAAII,IAAI,GAAG;MACTD,MAAM,EAAEA,MAAM,GAAGP,UAAU;MAC3BS,IAAI,EAAE;IACR,CAAC;IACDxC,MAAM,GAAG6B,QAAQ,CAACO,MAAM,CAAC,UAAUC,GAAG,EAAE7E,KAAK,EAAE;MAC7C,IAAIiF,WAAW,GAAG;QAChBlD,IAAI,EAAE/B,KAAK,CAAC+B,IAAI;QAChBmD,QAAQ,EAAE;UACRJ,MAAM,EAAEC,IAAI,CAACD,MAAM,GAAGC,IAAI,CAACC,IAAI,GAAGT,UAAU;UAC5C;UACAS,IAAI,EAAEP,OAAO,GAAGC,WAAW,GAAG1E,KAAK,CAAC4C;QACtC;MACF,CAAC;MACD,IAAIuC,MAAM,GAAG,EAAE,CAACC,MAAM,CAAChL,kBAAkB,CAACyK,GAAG,CAAC,EAAE,CAACI,WAAW,CAAC,CAAC;MAC9DF,IAAI,GAAGI,MAAM,CAACA,MAAM,CAAC1J,MAAM,GAAG,CAAC,CAAC,CAACyJ,QAAQ;MACzC,IAAIlF,KAAK,CAAC8D,SAAS,IAAI9D,KAAK,CAAC8D,SAAS,CAACrI,MAAM,EAAE;QAC7CuE,KAAK,CAAC8D,SAAS,CAACrH,OAAO,CAAC,UAAUsF,IAAI,EAAE;UACtCoD,MAAM,CAAC9I,IAAI,CAAC;YACV0F,IAAI,EAAEA,IAAI;YACVmD,QAAQ,EAAEH;UACZ,CAAC,CAAC;QACJ,CAAC,CAAC;MACJ;MACA,OAAOI,MAAM;IACf,CAAC,EAAEX,YAAY,CAAC;EAClB,CAAC,MAAM;IACL,IAAIa,OAAO,GAAGtG,eAAe,CAACmF,cAAc,EAAEC,QAAQ,EAAE,CAAC,EAAE,IAAI,CAAC;IAChE,IAAIA,QAAQ,GAAG,CAAC,GAAGkB,OAAO,GAAG,CAAC7J,GAAG,GAAG,CAAC,IAAI+I,UAAU,IAAI,CAAC,EAAE;MACxDA,UAAU,GAAG,CAAC;IAChB;IACA,IAAIe,YAAY,GAAG,CAACnB,QAAQ,GAAG,CAAC,GAAGkB,OAAO,GAAG,CAAC7J,GAAG,GAAG,CAAC,IAAI+I,UAAU,IAAI/I,GAAG;IAC1E,IAAI8J,YAAY,GAAG,CAAC,EAAE;MACpBA,YAAY,KAAK,CAAC;IACpB;IACA,IAAIN,IAAI,GAAGV,UAAU,KAAK,CAACA,UAAU,GAAGtD,IAAI,CAAC/C,GAAG,CAACqH,YAAY,EAAEhB,UAAU,CAAC,GAAGgB,YAAY;IACzF9C,MAAM,GAAG6B,QAAQ,CAACO,MAAM,CAAC,UAAUC,GAAG,EAAE7E,KAAK,EAAEtE,CAAC,EAAE;MAChD,IAAIyJ,MAAM,GAAG,EAAE,CAACC,MAAM,CAAChL,kBAAkB,CAACyK,GAAG,CAAC,EAAE,CAAC;QAC/C9C,IAAI,EAAE/B,KAAK,CAAC+B,IAAI;QAChBmD,QAAQ,EAAE;UACRJ,MAAM,EAAEO,OAAO,GAAG,CAACC,YAAY,GAAGf,UAAU,IAAI7I,CAAC,GAAG,CAAC4J,YAAY,GAAGN,IAAI,IAAI,CAAC;UAC7EA,IAAI,EAAEA;QACR;MACF,CAAC,CAAC,CAAC;MACH,IAAIhF,KAAK,CAAC8D,SAAS,IAAI9D,KAAK,CAAC8D,SAAS,CAACrI,MAAM,EAAE;QAC7CuE,KAAK,CAAC8D,SAAS,CAACrH,OAAO,CAAC,UAAUsF,IAAI,EAAE;UACtCoD,MAAM,CAAC9I,IAAI,CAAC;YACV0F,IAAI,EAAEA,IAAI;YACVmD,QAAQ,EAAEC,MAAM,CAACA,MAAM,CAAC1J,MAAM,GAAG,CAAC,CAAC,CAACyJ;UACtC,CAAC,CAAC;QACJ,CAAC,CAAC;MACJ;MACA,OAAOC,MAAM;IACf,CAAC,EAAEX,YAAY,CAAC;EAClB;EACA,OAAOhC,MAAM;AACf,CAAC;AACD,OAAO,IAAI+C,oBAAoB,GAAG,SAASA,oBAAoBA,CAACT,MAAM,EAAEU,OAAO,EAAEnD,KAAK,EAAEoD,SAAS,EAAE;EACjG,IAAIC,QAAQ,GAAGrD,KAAK,CAACqD,QAAQ;IAC3BC,KAAK,GAAGtD,KAAK,CAACsD,KAAK;IACnBC,MAAM,GAAGvD,KAAK,CAACuD,MAAM;EACvB,IAAIC,WAAW,GAAGF,KAAK,IAAIC,MAAM,CAACE,IAAI,IAAI,CAAC,CAAC,IAAIF,MAAM,CAACG,KAAK,IAAI,CAAC,CAAC;EAClE,IAAIC,WAAW,GAAGzG,cAAc,CAAC;IAC/BmG,QAAQ,EAAEA,QAAQ;IAClBG,WAAW,EAAEA;EACf,CAAC,CAAC;EACF,IAAIG,WAAW,EAAE;IACf,IAAIC,KAAK,GAAGR,SAAS,IAAI,CAAC,CAAC;MACzBS,QAAQ,GAAGD,KAAK,CAACN,KAAK;MACtBQ,SAAS,GAAGF,KAAK,CAACG,MAAM;IAC1B,IAAIC,KAAK,GAAGL,WAAW,CAACK,KAAK;MAC3BC,aAAa,GAAGN,WAAW,CAACM,aAAa;MACzCC,MAAM,GAAGP,WAAW,CAACO,MAAM;IAC7B,IAAI,CAACA,MAAM,KAAK,UAAU,IAAIA,MAAM,KAAK,YAAY,IAAID,aAAa,KAAK,QAAQ,KAAKD,KAAK,KAAK,QAAQ,IAAIrH,QAAQ,CAAC8F,MAAM,CAACuB,KAAK,CAAC,CAAC,EAAE;MACrI,OAAO9J,aAAa,CAACA,aAAa,CAAC,CAAC,CAAC,EAAEuI,MAAM,CAAC,EAAE,CAAC,CAAC,EAAEpI,eAAe,CAAC,CAAC,CAAC,EAAE2J,KAAK,EAAEvB,MAAM,CAACuB,KAAK,CAAC,IAAIH,QAAQ,IAAI,CAAC,CAAC,CAAC,CAAC;IAClH;IACA,IAAI,CAACK,MAAM,KAAK,YAAY,IAAIA,MAAM,KAAK,UAAU,IAAIF,KAAK,KAAK,QAAQ,KAAKC,aAAa,KAAK,QAAQ,IAAItH,QAAQ,CAAC8F,MAAM,CAACwB,aAAa,CAAC,CAAC,EAAE;MAC7I,OAAO/J,aAAa,CAACA,aAAa,CAAC,CAAC,CAAC,EAAEuI,MAAM,CAAC,EAAE,CAAC,CAAC,EAAEpI,eAAe,CAAC,CAAC,CAAC,EAAE4J,aAAa,EAAExB,MAAM,CAACwB,aAAa,CAAC,IAAIH,SAAS,IAAI,CAAC,CAAC,CAAC,CAAC;IACnI;EACF;EACA,OAAOrB,MAAM;AACf,CAAC;AACD,IAAI0B,yBAAyB,GAAG,SAASA,yBAAyBA,CAACD,MAAM,EAAExF,QAAQ,EAAE0F,SAAS,EAAE;EAC9F,IAAIvI,KAAK,CAAC6C,QAAQ,CAAC,EAAE;IACnB,OAAO,IAAI;EACb;EACA,IAAIwF,MAAM,KAAK,YAAY,EAAE;IAC3B,OAAOxF,QAAQ,KAAK,OAAO;EAC7B;EACA,IAAIwF,MAAM,KAAK,UAAU,EAAE;IACzB,OAAOxF,QAAQ,KAAK,OAAO;EAC7B;EACA,IAAI0F,SAAS,KAAK,GAAG,EAAE;IACrB,OAAO1F,QAAQ,KAAK,OAAO;EAC7B;EACA,IAAI0F,SAAS,KAAK,GAAG,EAAE;IACrB,OAAO1F,QAAQ,KAAK,OAAO;EAC7B;EACA,OAAO,IAAI;AACb,CAAC;AACD,OAAO,IAAI2F,oBAAoB,GAAG,SAASA,oBAAoBA,CAAC9G,IAAI,EAAEmC,IAAI,EAAEtC,OAAO,EAAE8G,MAAM,EAAExF,QAAQ,EAAE;EACrG,IAAI2E,QAAQ,GAAG3D,IAAI,CAACM,KAAK,CAACqD,QAAQ;EAClC,IAAIiB,SAAS,GAAGtH,aAAa,CAACqG,QAAQ,EAAE7G,QAAQ,CAAC,CAAC3C,MAAM,CAAC,UAAU0K,aAAa,EAAE;IAChF,OAAOJ,yBAAyB,CAACD,MAAM,EAAExF,QAAQ,EAAE6F,aAAa,CAACvE,KAAK,CAACoE,SAAS,CAAC;EACnF,CAAC,CAAC;EACF,IAAIE,SAAS,IAAIA,SAAS,CAAClL,MAAM,EAAE;IACjC,IAAIO,IAAI,GAAG2K,SAAS,CAACtG,GAAG,CAAC,UAAUuG,aAAa,EAAE;MAChD,OAAOA,aAAa,CAACvE,KAAK,CAAC5C,OAAO;IACpC,CAAC,CAAC;IACF,OAAOG,IAAI,CAACgF,MAAM,CAAC,UAAUpC,MAAM,EAAExC,KAAK,EAAE;MAC1C,IAAI6G,UAAU,GAAGrH,iBAAiB,CAACQ,KAAK,EAAEP,OAAO,CAAC;MAClD,IAAIvB,KAAK,CAAC2I,UAAU,CAAC,EAAE,OAAOrE,MAAM;MACpC,IAAIsE,SAAS,GAAG3L,KAAK,CAACI,OAAO,CAACsL,UAAU,CAAC,GAAG,CAAC5I,GAAG,CAAC4I,UAAU,CAAC,EAAE7I,GAAG,CAAC6I,UAAU,CAAC,CAAC,GAAG,CAACA,UAAU,EAAEA,UAAU,CAAC;MACzG,IAAIE,WAAW,GAAG/K,IAAI,CAAC4I,MAAM,CAAC,UAAUoC,YAAY,EAAEC,CAAC,EAAE;QACvD,IAAIC,UAAU,GAAG1H,iBAAiB,CAACQ,KAAK,EAAEiH,CAAC,EAAE,CAAC,CAAC;QAC/C,IAAIE,UAAU,GAAGL,SAAS,CAAC,CAAC,CAAC,GAAG9F,IAAI,CAACC,GAAG,CAAC9F,KAAK,CAACI,OAAO,CAAC2L,UAAU,CAAC,GAAGA,UAAU,CAAC,CAAC,CAAC,GAAGA,UAAU,CAAC;QAChG,IAAIE,UAAU,GAAGN,SAAS,CAAC,CAAC,CAAC,GAAG9F,IAAI,CAACC,GAAG,CAAC9F,KAAK,CAACI,OAAO,CAAC2L,UAAU,CAAC,GAAGA,UAAU,CAAC,CAAC,CAAC,GAAGA,UAAU,CAAC;QAChG,OAAO,CAAClG,IAAI,CAAC/C,GAAG,CAACkJ,UAAU,EAAEH,YAAY,CAAC,CAAC,CAAC,CAAC,EAAEhG,IAAI,CAAChD,GAAG,CAACoJ,UAAU,EAAEJ,YAAY,CAAC,CAAC,CAAC,CAAC,CAAC;MACvF,CAAC,EAAE,CAAC7G,QAAQ,EAAE,CAACA,QAAQ,CAAC,CAAC;MACzB,OAAO,CAACa,IAAI,CAAC/C,GAAG,CAAC8I,WAAW,CAAC,CAAC,CAAC,EAAEvE,MAAM,CAAC,CAAC,CAAC,CAAC,EAAExB,IAAI,CAAChD,GAAG,CAAC+I,WAAW,CAAC,CAAC,CAAC,EAAEvE,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC;IACnF,CAAC,EAAE,CAACrC,QAAQ,EAAE,CAACA,QAAQ,CAAC,CAAC;EAC3B;EACA,OAAO,IAAI;AACb,CAAC;AACD,OAAO,IAAIkH,oBAAoB,GAAG,SAASA,oBAAoBA,CAACzH,IAAI,EAAE0D,KAAK,EAAE7D,OAAO,EAAEsB,QAAQ,EAAEwF,MAAM,EAAE;EACtG,IAAIe,OAAO,GAAGhE,KAAK,CAACjD,GAAG,CAAC,UAAU0B,IAAI,EAAE;IACtC,OAAO2E,oBAAoB,CAAC9G,IAAI,EAAEmC,IAAI,EAAEtC,OAAO,EAAE8G,MAAM,EAAExF,QAAQ,CAAC;EACpE,CAAC,CAAC,CAAC7E,MAAM,CAAC,UAAU8D,KAAK,EAAE;IACzB,OAAO,CAAC9B,KAAK,CAAC8B,KAAK,CAAC;EACtB,CAAC,CAAC;EACF,IAAIsH,OAAO,IAAIA,OAAO,CAAC7L,MAAM,EAAE;IAC7B,OAAO6L,OAAO,CAAC1C,MAAM,CAAC,UAAUpC,MAAM,EAAExC,KAAK,EAAE;MAC7C,OAAO,CAACgB,IAAI,CAAC/C,GAAG,CAACuE,MAAM,CAAC,CAAC,CAAC,EAAExC,KAAK,CAAC,CAAC,CAAC,CAAC,EAAEgB,IAAI,CAAChD,GAAG,CAACwE,MAAM,CAAC,CAAC,CAAC,EAAExC,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC;IACvE,CAAC,EAAE,CAACG,QAAQ,EAAE,CAACA,QAAQ,CAAC,CAAC;EAC3B;EACA,OAAO,IAAI;AACb,CAAC;;AAED;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO,IAAIoH,4BAA4B,GAAG,SAASA,4BAA4BA,CAAC3H,IAAI,EAAE0D,KAAK,EAAEzD,IAAI,EAAE0G,MAAM,EAAEzG,SAAS,EAAE;EACpH,IAAIwH,OAAO,GAAGhE,KAAK,CAACjD,GAAG,CAAC,UAAU0B,IAAI,EAAE;IACtC,IAAItC,OAAO,GAAGsC,IAAI,CAACM,KAAK,CAAC5C,OAAO;IAChC,IAAII,IAAI,KAAK,QAAQ,IAAIJ,OAAO,EAAE;MAChC,OAAOiH,oBAAoB,CAAC9G,IAAI,EAAEmC,IAAI,EAAEtC,OAAO,EAAE8G,MAAM,CAAC,IAAI5G,oBAAoB,CAACC,IAAI,EAAEH,OAAO,EAAEI,IAAI,EAAEC,SAAS,CAAC;IAClH;IACA,OAAOH,oBAAoB,CAACC,IAAI,EAAEH,OAAO,EAAEI,IAAI,EAAEC,SAAS,CAAC;EAC7D,CAAC,CAAC;EACF,IAAID,IAAI,KAAK,QAAQ,EAAE;IACrB;IACA,OAAOyH,OAAO,CAAC1C,MAAM;IACrB;IACA;IACA,UAAUpC,MAAM,EAAExC,KAAK,EAAE;MACvB,OAAO,CAACgB,IAAI,CAAC/C,GAAG,CAACuE,MAAM,CAAC,CAAC,CAAC,EAAExC,KAAK,CAAC,CAAC,CAAC,CAAC,EAAEgB,IAAI,CAAChD,GAAG,CAACwE,MAAM,CAAC,CAAC,CAAC,EAAExC,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC;IACvE,CAAC,EAAE,CAACG,QAAQ,EAAE,CAACA,QAAQ,CAAC,CAAC;EAC3B;EACA,IAAIqH,GAAG,GAAG,CAAC,CAAC;EACZ;EACA,OAAOF,OAAO,CAAC1C,MAAM,CAAC,UAAUpC,MAAM,EAAExC,KAAK,EAAE;IAC7C,KAAK,IAAItE,CAAC,GAAG,CAAC,EAAEF,GAAG,GAAGwE,KAAK,CAACvE,MAAM,EAAEC,CAAC,GAAGF,GAAG,EAAEE,CAAC,EAAE,EAAE;MAChD;MACA,IAAI,CAAC8L,GAAG,CAACxH,KAAK,CAACtE,CAAC,CAAC,CAAC,EAAE;QAClB;QACA8L,GAAG,CAACxH,KAAK,CAACtE,CAAC,CAAC,CAAC,GAAG,IAAI;;QAEpB;QACA8G,MAAM,CAACnG,IAAI,CAAC2D,KAAK,CAACtE,CAAC,CAAC,CAAC;MACvB;IACF;IACA,OAAO8G,MAAM;EACf,CAAC,EAAE,EAAE,CAAC;AACR,CAAC;AACD,OAAO,IAAIiF,iBAAiB,GAAG,SAASA,iBAAiBA,CAAClB,MAAM,EAAExF,QAAQ,EAAE;EAC1E,OAAOwF,MAAM,KAAK,YAAY,IAAIxF,QAAQ,KAAK,OAAO,IAAIwF,MAAM,KAAK,UAAU,IAAIxF,QAAQ,KAAK,OAAO,IAAIwF,MAAM,KAAK,SAAS,IAAIxF,QAAQ,KAAK,WAAW,IAAIwF,MAAM,KAAK,QAAQ,IAAIxF,QAAQ,KAAK,YAAY;AACjN,CAAC;;AAED;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO,IAAI2G,oBAAoB,GAAG,SAASA,oBAAoBA,CAAChH,KAAK,EAAEiB,QAAQ,EAAEC,QAAQ,EAAE+F,aAAa,EAAE;EACxG,IAAIA,aAAa,EAAE;IACjB,OAAOjH,KAAK,CAACL,GAAG,CAAC,UAAUL,KAAK,EAAE;MAChC,OAAOA,KAAK,CAACQ,UAAU;IACzB,CAAC,CAAC;EACJ;EACA,IAAIoH,MAAM,EAAEC,MAAM;EAClB,IAAIC,MAAM,GAAGpH,KAAK,CAACL,GAAG,CAAC,UAAUL,KAAK,EAAE;IACtC,IAAIA,KAAK,CAACQ,UAAU,KAAKmB,QAAQ,EAAE;MACjCiG,MAAM,GAAG,IAAI;IACf;IACA,IAAI5H,KAAK,CAACQ,UAAU,KAAKoB,QAAQ,EAAE;MACjCiG,MAAM,GAAG,IAAI;IACf;IACA,OAAO7H,KAAK,CAACQ,UAAU;EACzB,CAAC,CAAC;EACF,IAAI,CAACoH,MAAM,EAAE;IACXE,MAAM,CAACzL,IAAI,CAACsF,QAAQ,CAAC;EACvB;EACA,IAAI,CAACkG,MAAM,EAAE;IACXC,MAAM,CAACzL,IAAI,CAACuF,QAAQ,CAAC;EACvB;EACA,OAAOkG,MAAM;AACf,CAAC;;AAED;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO,IAAIC,cAAc,GAAG,SAASA,cAAcA,CAAClH,IAAI,EAAEmH,MAAM,EAAEC,KAAK,EAAE;EACvE,IAAI,CAACpH,IAAI,EAAE,OAAO,IAAI;EACtB,IAAIqH,KAAK,GAAGrH,IAAI,CAACqH,KAAK;EACtB,IAAIC,eAAe,GAAGtH,IAAI,CAACsH,eAAe;IACxCtI,IAAI,GAAGgB,IAAI,CAAChB,IAAI;IAChBqB,KAAK,GAAGL,IAAI,CAACK,KAAK;EACpB,IAAIkH,aAAa,GAAGvH,IAAI,CAACwH,aAAa,KAAK,WAAW,GAAGH,KAAK,CAACI,SAAS,CAAC,CAAC,GAAG,CAAC,GAAG,CAAC;EAClF,IAAIxD,MAAM,GAAG,CAACkD,MAAM,IAAIC,KAAK,KAAKpI,IAAI,KAAK,UAAU,IAAIqI,KAAK,CAACI,SAAS,GAAGJ,KAAK,CAACI,SAAS,CAAC,CAAC,GAAGF,aAAa,GAAG,CAAC;EAChHtD,MAAM,GAAGjE,IAAI,CAACE,QAAQ,KAAK,WAAW,IAAI,CAACG,KAAK,KAAK,IAAI,IAAIA,KAAK,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,KAAK,CAACzF,MAAM,KAAK,CAAC,GAAGyD,QAAQ,CAACgC,KAAK,CAAC,CAAC,CAAC,GAAGA,KAAK,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,GAAG4D,MAAM,GAAGA,MAAM;;EAEjK;EACA,IAAIkD,MAAM,KAAKnH,IAAI,CAACH,KAAK,IAAIG,IAAI,CAAC0H,SAAS,CAAC,EAAE;IAC5C,IAAI/F,MAAM,GAAG,CAAC3B,IAAI,CAACH,KAAK,IAAIG,IAAI,CAAC0H,SAAS,EAAElI,GAAG,CAAC,UAAUL,KAAK,EAAE;MAC/D,IAAIwI,YAAY,GAAGL,eAAe,GAAGA,eAAe,CAAC1E,OAAO,CAACzD,KAAK,CAAC,GAAGA,KAAK;MAC3E,OAAO;QACL;QACA;QACAQ,UAAU,EAAE0H,KAAK,CAACM,YAAY,CAAC,GAAG1D,MAAM;QACxC9H,KAAK,EAAEgD,KAAK;QACZ8E,MAAM,EAAEA;MACV,CAAC;IACH,CAAC,CAAC;IACF,OAAOtC,MAAM,CAACtG,MAAM,CAAC,UAAUuM,GAAG,EAAE;MAClC,OAAO,CAAClK,KAAK,CAACkK,GAAG,CAACjI,UAAU,CAAC;IAC/B,CAAC,CAAC;EACJ;;EAEA;EACA,IAAIK,IAAI,CAAC6H,aAAa,IAAI7H,IAAI,CAAC8H,iBAAiB,EAAE;IAChD,OAAO9H,IAAI,CAAC8H,iBAAiB,CAACtI,GAAG,CAAC,UAAUL,KAAK,EAAEc,KAAK,EAAE;MACxD,OAAO;QACLN,UAAU,EAAE0H,KAAK,CAAClI,KAAK,CAAC,GAAG8E,MAAM;QACjC9H,KAAK,EAAEgD,KAAK;QACZc,KAAK,EAAEA,KAAK;QACZgE,MAAM,EAAEA;MACV,CAAC;IACH,CAAC,CAAC;EACJ;EACA,IAAIoD,KAAK,CAACxH,KAAK,IAAI,CAACuH,KAAK,EAAE;IACzB,OAAOC,KAAK,CAACxH,KAAK,CAACG,IAAI,CAAC+H,SAAS,CAAC,CAACvI,GAAG,CAAC,UAAUL,KAAK,EAAE;MACtD,OAAO;QACLQ,UAAU,EAAE0H,KAAK,CAAClI,KAAK,CAAC,GAAG8E,MAAM;QACjC9H,KAAK,EAAEgD,KAAK;QACZ8E,MAAM,EAAEA;MACV,CAAC;IACH,CAAC,CAAC;EACJ;;EAEA;EACA,OAAOoD,KAAK,CAACjI,MAAM,CAAC,CAAC,CAACI,GAAG,CAAC,UAAUL,KAAK,EAAEc,KAAK,EAAE;IAChD,OAAO;MACLN,UAAU,EAAE0H,KAAK,CAAClI,KAAK,CAAC,GAAG8E,MAAM;MACjC9H,KAAK,EAAEmL,eAAe,GAAGA,eAAe,CAACnI,KAAK,CAAC,GAAGA,KAAK;MACvDc,KAAK,EAAEA,KAAK;MACZgE,MAAM,EAAEA;IACV,CAAC;EACH,CAAC,CAAC;AACJ,CAAC;;AAED;AACA;AACA;AACA;AACA;AACA;;AAEA,IAAI+D,cAAc,GAAG,IAAIC,OAAO,CAAC,CAAC;AAClC,OAAO,IAAIC,oBAAoB,GAAG,SAASA,oBAAoBA,CAACC,cAAc,EAAEC,YAAY,EAAE;EAC5F,IAAI,OAAOA,YAAY,KAAK,UAAU,EAAE;IACtC,OAAOD,cAAc;EACvB;EACA,IAAI,CAACH,cAAc,CAACK,GAAG,CAACF,cAAc,CAAC,EAAE;IACvCH,cAAc,CAACM,GAAG,CAACH,cAAc,EAAE,IAAIF,OAAO,CAAC,CAAC,CAAC;EACnD;EACA,IAAIM,YAAY,GAAGP,cAAc,CAACxK,GAAG,CAAC2K,cAAc,CAAC;EACrD,IAAII,YAAY,CAACF,GAAG,CAACD,YAAY,CAAC,EAAE;IAClC,OAAOG,YAAY,CAAC/K,GAAG,CAAC4K,YAAY,CAAC;EACvC;EACA,IAAII,cAAc,GAAG,SAASA,cAAcA,CAAA,EAAG;IAC7CL,cAAc,CAAC1M,KAAK,CAAC,KAAK,CAAC,EAAEE,SAAS,CAAC;IACvCyM,YAAY,CAAC3M,KAAK,CAAC,KAAK,CAAC,EAAEE,SAAS,CAAC;EACvC,CAAC;EACD4M,YAAY,CAACD,GAAG,CAACF,YAAY,EAAEI,cAAc,CAAC;EAC9C,OAAOA,cAAc;AACvB,CAAC;;AAED;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO,IAAIC,UAAU,GAAG,SAASA,UAAUA,CAACzI,IAAI,EAAE0I,SAAS,EAAEC,MAAM,EAAE;EACnE,IAAItB,KAAK,GAAGrH,IAAI,CAACqH,KAAK;IACpBrI,IAAI,GAAGgB,IAAI,CAAChB,IAAI;IAChB0G,MAAM,GAAG1F,IAAI,CAAC0F,MAAM;IACpBxF,QAAQ,GAAGF,IAAI,CAACE,QAAQ;EAC1B,IAAImH,KAAK,KAAK,MAAM,EAAE;IACpB,IAAI3B,MAAM,KAAK,QAAQ,IAAIxF,QAAQ,KAAK,YAAY,EAAE;MACpD,OAAO;QACLmH,KAAK,EAAE1K,QAAQ,CAACiM,SAAS,CAAC,CAAC;QAC3BpB,aAAa,EAAE;MACjB,CAAC;IACH;IACA,IAAI9B,MAAM,KAAK,QAAQ,IAAIxF,QAAQ,KAAK,WAAW,EAAE;MACnD,OAAO;QACLmH,KAAK,EAAE1K,QAAQ,CAACkM,WAAW,CAAC,CAAC;QAC7BrB,aAAa,EAAE;MACjB,CAAC;IACH;IACA,IAAIxI,IAAI,KAAK,UAAU,IAAI0J,SAAS,KAAKA,SAAS,CAAC9F,OAAO,CAAC,WAAW,CAAC,IAAI,CAAC,IAAI8F,SAAS,CAAC9F,OAAO,CAAC,WAAW,CAAC,IAAI,CAAC,IAAI8F,SAAS,CAAC9F,OAAO,CAAC,eAAe,CAAC,IAAI,CAAC,IAAI,CAAC+F,MAAM,CAAC,EAAE;MAC1K,OAAO;QACLtB,KAAK,EAAE1K,QAAQ,CAACmM,UAAU,CAAC,CAAC;QAC5BtB,aAAa,EAAE;MACjB,CAAC;IACH;IACA,IAAIxI,IAAI,KAAK,UAAU,EAAE;MACvB,OAAO;QACLqI,KAAK,EAAE1K,QAAQ,CAACiM,SAAS,CAAC,CAAC;QAC3BpB,aAAa,EAAE;MACjB,CAAC;IACH;IACA,OAAO;MACLH,KAAK,EAAE1K,QAAQ,CAACkM,WAAW,CAAC,CAAC;MAC7BrB,aAAa,EAAE;IACjB,CAAC;EACH;EACA,IAAIjK,QAAQ,CAAC8J,KAAK,CAAC,EAAE;IACnB,IAAIhN,IAAI,GAAG,OAAO,CAACkK,MAAM,CAAC5G,UAAU,CAAC0J,KAAK,CAAC,CAAC;IAC5C,OAAO;MACLA,KAAK,EAAE,CAAC1K,QAAQ,CAACtC,IAAI,CAAC,IAAIsC,QAAQ,CAACmM,UAAU,EAAE,CAAC;MAChDtB,aAAa,EAAE7K,QAAQ,CAACtC,IAAI,CAAC,GAAGA,IAAI,GAAG;IACzC,CAAC;EACH;EACA,OAAOiD,UAAU,CAAC+J,KAAK,CAAC,GAAG;IACzBA,KAAK,EAAEA;EACT,CAAC,GAAG;IACFA,KAAK,EAAE1K,QAAQ,CAACmM,UAAU,CAAC,CAAC;IAC5BtB,aAAa,EAAE;EACjB,CAAC;AACH,CAAC;AACD,IAAIuB,GAAG,GAAG,IAAI;AACd,OAAO,IAAIC,kBAAkB,GAAG,SAASA,kBAAkBA,CAAC3B,KAAK,EAAE;EACjE,IAAIjI,MAAM,GAAGiI,KAAK,CAACjI,MAAM,CAAC,CAAC;EAC3B,IAAI,CAACA,MAAM,IAAIA,MAAM,CAACxE,MAAM,IAAI,CAAC,EAAE;IACjC;EACF;EACA,IAAID,GAAG,GAAGyE,MAAM,CAACxE,MAAM;EACvB,IAAIyF,KAAK,GAAGgH,KAAK,CAAChH,KAAK,CAAC,CAAC;EACzB,IAAIS,QAAQ,GAAGX,IAAI,CAAC/C,GAAG,CAACiD,KAAK,CAAC,CAAC,CAAC,EAAEA,KAAK,CAAC,CAAC,CAAC,CAAC,GAAG0I,GAAG;EACjD,IAAIhI,QAAQ,GAAGZ,IAAI,CAAChD,GAAG,CAACkD,KAAK,CAAC,CAAC,CAAC,EAAEA,KAAK,CAAC,CAAC,CAAC,CAAC,GAAG0I,GAAG;EACjD,IAAIE,KAAK,GAAG5B,KAAK,CAACjI,MAAM,CAAC,CAAC,CAAC,CAAC;EAC5B,IAAI8J,IAAI,GAAG7B,KAAK,CAACjI,MAAM,CAACzE,GAAG,GAAG,CAAC,CAAC,CAAC;EACjC,IAAIsO,KAAK,GAAGnI,QAAQ,IAAImI,KAAK,GAAGlI,QAAQ,IAAImI,IAAI,GAAGpI,QAAQ,IAAIoI,IAAI,GAAGnI,QAAQ,EAAE;IAC9EsG,KAAK,CAACjI,MAAM,CAAC,CAACA,MAAM,CAAC,CAAC,CAAC,EAAEA,MAAM,CAACzE,GAAG,GAAG,CAAC,CAAC,CAAC,CAAC;EAC5C;AACF,CAAC;AACD,OAAO,IAAIwO,iBAAiB,GAAG,SAASA,iBAAiBA,CAACC,WAAW,EAAEC,KAAK,EAAE;EAC5E,IAAI,CAACD,WAAW,EAAE;IAChB,OAAO,IAAI;EACb;EACA,KAAK,IAAIvO,CAAC,GAAG,CAAC,EAAEF,GAAG,GAAGyO,WAAW,CAACxO,MAAM,EAAEC,CAAC,GAAGF,GAAG,EAAEE,CAAC,EAAE,EAAE;IACtD,IAAIuO,WAAW,CAACvO,CAAC,CAAC,CAACqG,IAAI,KAAKmI,KAAK,EAAE;MACjC,OAAOD,WAAW,CAACvO,CAAC,CAAC,CAACwJ,QAAQ;IAChC;EACF;EACA,OAAO,IAAI;AACb,CAAC;;AAED;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO,IAAIiF,gBAAgB,GAAG,SAASA,gBAAgBA,CAACnN,KAAK,EAAEiD,MAAM,EAAE;EACrE,IAAI,CAACA,MAAM,IAAIA,MAAM,CAACxE,MAAM,KAAK,CAAC,IAAI,CAACuD,QAAQ,CAACiB,MAAM,CAAC,CAAC,CAAC,CAAC,IAAI,CAACjB,QAAQ,CAACiB,MAAM,CAAC,CAAC,CAAC,CAAC,EAAE;IAClF,OAAOjD,KAAK;EACd;EACA,IAAI2E,QAAQ,GAAGX,IAAI,CAAC/C,GAAG,CAACgC,MAAM,CAAC,CAAC,CAAC,EAAEA,MAAM,CAAC,CAAC,CAAC,CAAC;EAC7C,IAAI2B,QAAQ,GAAGZ,IAAI,CAAChD,GAAG,CAACiC,MAAM,CAAC,CAAC,CAAC,EAAEA,MAAM,CAAC,CAAC,CAAC,CAAC;EAC7C,IAAIuC,MAAM,GAAG,CAACxF,KAAK,CAAC,CAAC,CAAC,EAAEA,KAAK,CAAC,CAAC,CAAC,CAAC;EACjC,IAAI,CAACgC,QAAQ,CAAChC,KAAK,CAAC,CAAC,CAAC,CAAC,IAAIA,KAAK,CAAC,CAAC,CAAC,GAAG2E,QAAQ,EAAE;IAC9Ca,MAAM,CAAC,CAAC,CAAC,GAAGb,QAAQ;EACtB;EACA,IAAI,CAAC3C,QAAQ,CAAChC,KAAK,CAAC,CAAC,CAAC,CAAC,IAAIA,KAAK,CAAC,CAAC,CAAC,GAAG4E,QAAQ,EAAE;IAC9CY,MAAM,CAAC,CAAC,CAAC,GAAGZ,QAAQ;EACtB;EACA,IAAIY,MAAM,CAAC,CAAC,CAAC,GAAGZ,QAAQ,EAAE;IACxBY,MAAM,CAAC,CAAC,CAAC,GAAGZ,QAAQ;EACtB;EACA,IAAIY,MAAM,CAAC,CAAC,CAAC,GAAGb,QAAQ,EAAE;IACxBa,MAAM,CAAC,CAAC,CAAC,GAAGb,QAAQ;EACtB;EACA,OAAOa,MAAM;AACf,CAAC;;AAED;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO,IAAI4H,UAAU,GAAG,SAASA,UAAUA,CAACC,MAAM,EAAE;EAClD,IAAIxP,CAAC,GAAGwP,MAAM,CAAC5O,MAAM;EACrB,IAAIZ,CAAC,IAAI,CAAC,EAAE;IACV;EACF;EACA,KAAK,IAAIsI,CAAC,GAAG,CAAC,EAAEmH,CAAC,GAAGD,MAAM,CAAC,CAAC,CAAC,CAAC5O,MAAM,EAAE0H,CAAC,GAAGmH,CAAC,EAAE,EAAEnH,CAAC,EAAE;IAChD,IAAIoH,QAAQ,GAAG,CAAC;IAChB,IAAIC,QAAQ,GAAG,CAAC;IAChB,KAAK,IAAI9O,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGb,CAAC,EAAE,EAAEa,CAAC,EAAE;MAC1B,IAAIsB,KAAK,GAAGuB,KAAK,CAAC8L,MAAM,CAAC3O,CAAC,CAAC,CAACyH,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,GAAGkH,MAAM,CAAC3O,CAAC,CAAC,CAACyH,CAAC,CAAC,CAAC,CAAC,CAAC,GAAGkH,MAAM,CAAC3O,CAAC,CAAC,CAACyH,CAAC,CAAC,CAAC,CAAC,CAAC;;MAEtE;MACA,IAAInG,KAAK,IAAI,CAAC,EAAE;QACdqN,MAAM,CAAC3O,CAAC,CAAC,CAACyH,CAAC,CAAC,CAAC,CAAC,CAAC,GAAGoH,QAAQ;QAC1BF,MAAM,CAAC3O,CAAC,CAAC,CAACyH,CAAC,CAAC,CAAC,CAAC,CAAC,GAAGoH,QAAQ,GAAGvN,KAAK;QAClCuN,QAAQ,GAAGF,MAAM,CAAC3O,CAAC,CAAC,CAACyH,CAAC,CAAC,CAAC,CAAC,CAAC;MAC5B,CAAC,MAAM;QACLkH,MAAM,CAAC3O,CAAC,CAAC,CAACyH,CAAC,CAAC,CAAC,CAAC,CAAC,GAAGqH,QAAQ;QAC1BH,MAAM,CAAC3O,CAAC,CAAC,CAACyH,CAAC,CAAC,CAAC,CAAC,CAAC,GAAGqH,QAAQ,GAAGxN,KAAK;QAClCwN,QAAQ,GAAGH,MAAM,CAAC3O,CAAC,CAAC,CAACyH,CAAC,CAAC,CAAC,CAAC,CAAC;MAC5B;MACA;IACF;EACF;AACF,CAAC;;AAED;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO,IAAIsH,cAAc,GAAG,SAASA,cAAcA,CAACJ,MAAM,EAAE;EAC1D,IAAIxP,CAAC,GAAGwP,MAAM,CAAC5O,MAAM;EACrB,IAAIZ,CAAC,IAAI,CAAC,EAAE;IACV;EACF;EACA,KAAK,IAAIsI,CAAC,GAAG,CAAC,EAAEmH,CAAC,GAAGD,MAAM,CAAC,CAAC,CAAC,CAAC5O,MAAM,EAAE0H,CAAC,GAAGmH,CAAC,EAAE,EAAEnH,CAAC,EAAE;IAChD,IAAIoH,QAAQ,GAAG,CAAC;IAChB,KAAK,IAAI7O,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGb,CAAC,EAAE,EAAEa,CAAC,EAAE;MAC1B,IAAIsB,KAAK,GAAGuB,KAAK,CAAC8L,MAAM,CAAC3O,CAAC,CAAC,CAACyH,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,GAAGkH,MAAM,CAAC3O,CAAC,CAAC,CAACyH,CAAC,CAAC,CAAC,CAAC,CAAC,GAAGkH,MAAM,CAAC3O,CAAC,CAAC,CAACyH,CAAC,CAAC,CAAC,CAAC,CAAC;;MAEtE;MACA,IAAInG,KAAK,IAAI,CAAC,EAAE;QACdqN,MAAM,CAAC3O,CAAC,CAAC,CAACyH,CAAC,CAAC,CAAC,CAAC,CAAC,GAAGoH,QAAQ;QAC1BF,MAAM,CAAC3O,CAAC,CAAC,CAACyH,CAAC,CAAC,CAAC,CAAC,CAAC,GAAGoH,QAAQ,GAAGvN,KAAK;QAClCuN,QAAQ,GAAGF,MAAM,CAAC3O,CAAC,CAAC,CAACyH,CAAC,CAAC,CAAC,CAAC,CAAC;MAC5B,CAAC,MAAM;QACLkH,MAAM,CAAC3O,CAAC,CAAC,CAACyH,CAAC,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC;QACnBkH,MAAM,CAAC3O,CAAC,CAAC,CAACyH,CAAC,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC;MACrB;MACA;IACF;EACF;AACF,CAAC;;AAED;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA,IAAIuH,gBAAgB,GAAG;EACrBC,IAAI,EAAEP,UAAU;EAChB;EACAQ,MAAM,EAAEjN,iBAAiB;EACzB;EACAkN,IAAI,EAAEjN,eAAe;EACrB;EACAkN,UAAU,EAAEjN,qBAAqB;EACjC;EACAkN,MAAM,EAAEjN,iBAAiB;EACzByM,QAAQ,EAAEE;AACZ,CAAC;AACD,OAAO,IAAIO,cAAc,GAAG,SAASA,cAAcA,CAACpL,IAAI,EAAEqL,UAAU,EAAEC,UAAU,EAAE;EAChF,IAAIC,QAAQ,GAAGF,UAAU,CAAC5K,GAAG,CAAC,UAAU0B,IAAI,EAAE;IAC5C,OAAOA,IAAI,CAACM,KAAK,CAAC5C,OAAO;EAC3B,CAAC,CAAC;EACF,IAAI2L,cAAc,GAAGV,gBAAgB,CAACQ,UAAU,CAAC;EACjD,IAAIzN,KAAK,GAAGC,UAAU,CAAC;EACvB;EAAA,CACC1B,IAAI,CAACmP,QAAQ,CAAC,CAACnO,KAAK,CAAC,UAAUqO,CAAC,EAAEtO,GAAG,EAAE;IACtC,OAAO,CAACyC,iBAAiB,CAAC6L,CAAC,EAAEtO,GAAG,EAAE,CAAC,CAAC;EACtC,CAAC,CAAC,CAACuO,KAAK,CAACvN,cAAc;EACvB;EAAA,CACC+G,MAAM,CAACsG,cAAc,CAAC;EACvB,OAAO3N,KAAK,CAACmC,IAAI,CAAC;AACpB,CAAC;AACD,OAAO,IAAI2L,sBAAsB,GAAG,SAASA,sBAAsBA,CAAC3L,IAAI,EAAE4L,MAAM,EAAEC,aAAa,EAAElI,UAAU,EAAE2H,UAAU,EAAEQ,iBAAiB,EAAE;EAC1I,IAAI,CAAC9L,IAAI,EAAE;IACT,OAAO,IAAI;EACb;;EAEA;EACA,IAAI0D,KAAK,GAAGoI,iBAAiB,GAAGF,MAAM,CAACG,OAAO,CAAC,CAAC,GAAGH,MAAM;EACzD,IAAII,6BAA6B,GAAG,CAAC,CAAC;EACtC,IAAI7I,WAAW,GAAGO,KAAK,CAACsB,MAAM,CAAC,UAAUpC,MAAM,EAAET,IAAI,EAAE;IACrD,IAAI8J,WAAW;IACf,IAAI1J,cAAc,GAAG,CAAC0J,WAAW,GAAG9J,IAAI,CAAClC,IAAI,MAAM,IAAI,IAAIgM,WAAW,KAAK,KAAK,CAAC,IAAIA,WAAW,CAACzJ,YAAY,GAAG7F,aAAa,CAACA,aAAa,CAAC,CAAC,CAAC,EAAEwF,IAAI,CAAClC,IAAI,CAACuC,YAAY,CAAC,EAAEL,IAAI,CAACM,KAAK,CAAC,GAAGN,IAAI,CAACM,KAAK;IACjM,IAAIyJ,OAAO,GAAG3J,cAAc,CAAC2J,OAAO;MAClCC,IAAI,GAAG5J,cAAc,CAAC4J,IAAI;IAC5B,IAAIA,IAAI,EAAE;MACR,OAAOvJ,MAAM;IACf;IACA,IAAIwJ,MAAM,GAAG7J,cAAc,CAACsJ,aAAa,CAAC;IAC1C,IAAIQ,WAAW,GAAGzJ,MAAM,CAACwJ,MAAM,CAAC,IAAI;MAClCE,QAAQ,EAAE,KAAK;MACfnJ,WAAW,EAAE,CAAC;IAChB,CAAC;IACD,IAAI9D,UAAU,CAAC6M,OAAO,CAAC,EAAE;MACvB,IAAIK,UAAU,GAAGF,WAAW,CAAClJ,WAAW,CAAC+I,OAAO,CAAC,IAAI;QACnDL,aAAa,EAAEA,aAAa;QAC5BlI,UAAU,EAAEA,UAAU;QACtBD,KAAK,EAAE;MACT,CAAC;MACD6I,UAAU,CAAC7I,KAAK,CAACjH,IAAI,CAAC0F,IAAI,CAAC;MAC3BkK,WAAW,CAACC,QAAQ,GAAG,IAAI;MAC3BD,WAAW,CAAClJ,WAAW,CAAC+I,OAAO,CAAC,GAAGK,UAAU;IAC/C,CAAC,MAAM;MACLF,WAAW,CAAClJ,WAAW,CAAC5D,QAAQ,CAAC,WAAW,CAAC,CAAC,GAAG;QAC/CsM,aAAa,EAAEA,aAAa;QAC5BlI,UAAU,EAAEA,UAAU;QACtBD,KAAK,EAAE,CAACvB,IAAI;MACd,CAAC;IACH;IACA,OAAOxF,aAAa,CAACA,aAAa,CAAC,CAAC,CAAC,EAAEiG,MAAM,CAAC,EAAE,CAAC,CAAC,EAAE9F,eAAe,CAAC,CAAC,CAAC,EAAEsP,MAAM,EAAEC,WAAW,CAAC,CAAC;EAC/F,CAAC,EAAEL,6BAA6B,CAAC;EACjC,IAAIQ,2BAA2B,GAAG,CAAC,CAAC;EACpC,OAAOtR,MAAM,CAACkB,IAAI,CAAC+G,WAAW,CAAC,CAAC6B,MAAM,CAAC,UAAUpC,MAAM,EAAEwJ,MAAM,EAAE;IAC/D,IAAIK,KAAK,GAAGtJ,WAAW,CAACiJ,MAAM,CAAC;IAC/B,IAAIK,KAAK,CAACH,QAAQ,EAAE;MAClB,IAAII,uBAAuB,GAAG,CAAC,CAAC;MAChCD,KAAK,CAACtJ,WAAW,GAAGjI,MAAM,CAACkB,IAAI,CAACqQ,KAAK,CAACtJ,WAAW,CAAC,CAAC6B,MAAM,CAAC,UAAUC,GAAG,EAAEiH,OAAO,EAAE;QAChF,IAAIS,CAAC,GAAGF,KAAK,CAACtJ,WAAW,CAAC+I,OAAO,CAAC;QAClC,OAAOvP,aAAa,CAACA,aAAa,CAAC,CAAC,CAAC,EAAEsI,GAAG,CAAC,EAAE,CAAC,CAAC,EAAEnI,eAAe,CAAC,CAAC,CAAC,EAAEoP,OAAO,EAAE;UAC5EL,aAAa,EAAEA,aAAa;UAC5BlI,UAAU,EAAEA,UAAU;UACtBD,KAAK,EAAEiJ,CAAC,CAACjJ,KAAK;UACdkJ,WAAW,EAAExB,cAAc,CAACpL,IAAI,EAAE2M,CAAC,CAACjJ,KAAK,EAAE4H,UAAU;QACvD,CAAC,CAAC,CAAC;MACL,CAAC,EAAEoB,uBAAuB,CAAC;IAC7B;IACA,OAAO/P,aAAa,CAACA,aAAa,CAAC,CAAC,CAAC,EAAEiG,MAAM,CAAC,EAAE,CAAC,CAAC,EAAE9F,eAAe,CAAC,CAAC,CAAC,EAAEsP,MAAM,EAAEK,KAAK,CAAC,CAAC;EACzF,CAAC,EAAED,2BAA2B,CAAC;AACjC,CAAC;;AAED;AACA;AACA;AACA;AACA;AACA;AACA,OAAO,IAAIK,eAAe,GAAG,SAASA,eAAeA,CAACvE,KAAK,EAAEwE,IAAI,EAAE;EACjE,IAAIrE,aAAa,GAAGqE,IAAI,CAACrE,aAAa;IACpCxI,IAAI,GAAG6M,IAAI,CAAC7M,IAAI;IAChB+I,SAAS,GAAG8D,IAAI,CAAC9D,SAAS;IAC1B+D,cAAc,GAAGD,IAAI,CAACC,cAAc;IACpCC,aAAa,GAAGF,IAAI,CAACE,aAAa;EACpC,IAAIC,SAAS,GAAGxE,aAAa,IAAIqE,IAAI,CAACxE,KAAK;EAC3C,IAAI2E,SAAS,KAAK,MAAM,IAAIA,SAAS,KAAK,QAAQ,EAAE;IAClD,OAAO,IAAI;EACb;EACA,IAAIjE,SAAS,IAAI/I,IAAI,KAAK,QAAQ,IAAI8M,cAAc,KAAKA,cAAc,CAAC,CAAC,CAAC,KAAK,MAAM,IAAIA,cAAc,CAAC,CAAC,CAAC,KAAK,MAAM,CAAC,EAAE;IACtH;IACA,IAAI1M,MAAM,GAAGiI,KAAK,CAACjI,MAAM,CAAC,CAAC;IAC3B,IAAI,CAACA,MAAM,CAACxE,MAAM,EAAE;MAClB,OAAO,IAAI;IACb;IACA,IAAIqR,UAAU,GAAGnO,iBAAiB,CAACsB,MAAM,EAAE2I,SAAS,EAAEgE,aAAa,CAAC;IACpE1E,KAAK,CAACjI,MAAM,CAAC,CAAChC,GAAG,CAAC6O,UAAU,CAAC,EAAE9O,GAAG,CAAC8O,UAAU,CAAC,CAAC,CAAC;IAChD,OAAO;MACLvE,SAAS,EAAEuE;IACb,CAAC;EACH;EACA,IAAIlE,SAAS,IAAI/I,IAAI,KAAK,QAAQ,EAAE;IAClC,IAAIkN,OAAO,GAAG7E,KAAK,CAACjI,MAAM,CAAC,CAAC;IAC5B,IAAI+M,WAAW,GAAGpO,wBAAwB,CAACmO,OAAO,EAAEnE,SAAS,EAAEgE,aAAa,CAAC;IAC7E,OAAO;MACLrE,SAAS,EAAEyE;IACb,CAAC;EACH;EACA,OAAO,IAAI;AACb,CAAC;AACD,OAAO,SAASC,uBAAuBA,CAACC,KAAK,EAAE;EAC7C,IAAIrM,IAAI,GAAGqM,KAAK,CAACrM,IAAI;IACnBH,KAAK,GAAGwM,KAAK,CAACxM,KAAK;IACnByD,QAAQ,GAAG+I,KAAK,CAAC/I,QAAQ;IACzBnE,KAAK,GAAGkN,KAAK,CAAClN,KAAK;IACnBc,KAAK,GAAGoM,KAAK,CAACpM,KAAK;IACnBrB,OAAO,GAAGyN,KAAK,CAACzN,OAAO;EACzB,IAAIoB,IAAI,CAAChB,IAAI,KAAK,UAAU,EAAE;IAC5B;IACA;IACA,IAAI,CAACgB,IAAI,CAACsM,uBAAuB,IAAItM,IAAI,CAACpB,OAAO,IAAI,CAACvB,KAAK,CAAC8B,KAAK,CAACa,IAAI,CAACpB,OAAO,CAAC,CAAC,EAAE;MAChF;MACA,IAAI2N,WAAW,GAAGtO,gBAAgB,CAAC4B,KAAK,EAAE,OAAO,EAAEV,KAAK,CAACa,IAAI,CAACpB,OAAO,CAAC,CAAC;MACvE,IAAI2N,WAAW,EAAE;QACf,OAAOA,WAAW,CAAC5M,UAAU,GAAG2D,QAAQ,GAAG,CAAC;MAC9C;IACF;IACA,OAAOzD,KAAK,CAACI,KAAK,CAAC,GAAGJ,KAAK,CAACI,KAAK,CAAC,CAACN,UAAU,GAAG2D,QAAQ,GAAG,CAAC,GAAG,IAAI;EACrE;EACA,IAAInH,KAAK,GAAGwC,iBAAiB,CAACQ,KAAK,EAAE,CAAC9B,KAAK,CAACuB,OAAO,CAAC,GAAGA,OAAO,GAAGoB,IAAI,CAACpB,OAAO,CAAC;EAC9E,OAAO,CAACvB,KAAK,CAAClB,KAAK,CAAC,GAAG6D,IAAI,CAACqH,KAAK,CAAClL,KAAK,CAAC,GAAG,IAAI;AACjD;AACA,OAAO,IAAIqQ,sBAAsB,GAAG,SAASA,sBAAsBA,CAACC,KAAK,EAAE;EACzE,IAAIzM,IAAI,GAAGyM,KAAK,CAACzM,IAAI;IACnBH,KAAK,GAAG4M,KAAK,CAAC5M,KAAK;IACnBoE,MAAM,GAAGwI,KAAK,CAACxI,MAAM;IACrBX,QAAQ,GAAGmJ,KAAK,CAACnJ,QAAQ;IACzBnE,KAAK,GAAGsN,KAAK,CAACtN,KAAK;IACnBc,KAAK,GAAGwM,KAAK,CAACxM,KAAK;EACrB,IAAID,IAAI,CAAChB,IAAI,KAAK,UAAU,EAAE;IAC5B,OAAOa,KAAK,CAACI,KAAK,CAAC,GAAGJ,KAAK,CAACI,KAAK,CAAC,CAACN,UAAU,GAAGsE,MAAM,GAAG,IAAI;EAC/D;EACA,IAAI9H,KAAK,GAAGwC,iBAAiB,CAACQ,KAAK,EAAEa,IAAI,CAACpB,OAAO,EAAEoB,IAAI,CAACZ,MAAM,CAACa,KAAK,CAAC,CAAC;EACtE,OAAO,CAAC5C,KAAK,CAAClB,KAAK,CAAC,GAAG6D,IAAI,CAACqH,KAAK,CAAClL,KAAK,CAAC,GAAGmH,QAAQ,GAAG,CAAC,GAAGW,MAAM,GAAG,IAAI;AACzE,CAAC;AACD,OAAO,IAAIyI,iBAAiB,GAAG,SAASA,iBAAiBA,CAACC,KAAK,EAAE;EAC/D,IAAIC,WAAW,GAAGD,KAAK,CAACC,WAAW;EACnC,IAAIxN,MAAM,GAAGwN,WAAW,CAACvF,KAAK,CAACjI,MAAM,CAAC,CAAC;EACvC,IAAIwN,WAAW,CAAC5N,IAAI,KAAK,QAAQ,EAAE;IACjC,IAAI8B,QAAQ,GAAGX,IAAI,CAAC/C,GAAG,CAACgC,MAAM,CAAC,CAAC,CAAC,EAAEA,MAAM,CAAC,CAAC,CAAC,CAAC;IAC7C,IAAI2B,QAAQ,GAAGZ,IAAI,CAAChD,GAAG,CAACiC,MAAM,CAAC,CAAC,CAAC,EAAEA,MAAM,CAAC,CAAC,CAAC,CAAC;IAC7C,IAAI0B,QAAQ,IAAI,CAAC,IAAIC,QAAQ,IAAI,CAAC,EAAE;MAClC,OAAO,CAAC;IACV;IACA,IAAIA,QAAQ,GAAG,CAAC,EAAE;MAChB,OAAOA,QAAQ;IACjB;IACA,OAAOD,QAAQ;EACjB;EACA,OAAO1B,MAAM,CAAC,CAAC,CAAC;AAClB,CAAC;AACD,OAAO,IAAIyN,oBAAoB,GAAG,SAASA,oBAAoBA,CAAC3L,IAAI,EAAEgB,WAAW,EAAE;EACjF,IAAI4K,WAAW;EACf,IAAIxL,cAAc,GAAG,CAACwL,WAAW,GAAG5L,IAAI,CAAClC,IAAI,MAAM,IAAI,IAAI8N,WAAW,KAAK,KAAK,CAAC,IAAIA,WAAW,CAACvL,YAAY,GAAG7F,aAAa,CAACA,aAAa,CAAC,CAAC,CAAC,EAAEwF,IAAI,CAAClC,IAAI,CAACuC,YAAY,CAAC,EAAEL,IAAI,CAACM,KAAK,CAAC,GAAGN,IAAI,CAACM,KAAK;EACjM,IAAIyJ,OAAO,GAAG3J,cAAc,CAAC2J,OAAO;EACpC,IAAI7M,UAAU,CAAC6M,OAAO,CAAC,EAAE;IACvB,IAAIO,KAAK,GAAGtJ,WAAW,CAAC+I,OAAO,CAAC;IAChC,IAAIO,KAAK,EAAE;MACT,IAAIuB,SAAS,GAAGvB,KAAK,CAAC/I,KAAK,CAACG,OAAO,CAAC1B,IAAI,CAAC;MACzC,OAAO6L,SAAS,IAAI,CAAC,GAAGvB,KAAK,CAACG,WAAW,CAACoB,SAAS,CAAC,GAAG,IAAI;IAC7D;EACF;EACA,OAAO,IAAI;AACb,CAAC;AACD,IAAIC,iBAAiB,GAAG,SAASA,iBAAiBA,CAACjO,IAAI,EAAE;EACvD,OAAOA,IAAI,CAACgF,MAAM,CAAC,UAAUpC,MAAM,EAAExC,KAAK,EAAE;IAC1C,OAAO,CAAC/B,GAAG,CAAC+B,KAAK,CAACoF,MAAM,CAAC,CAAC5C,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC,CAACtG,MAAM,CAAC8C,QAAQ,CAAC,CAAC,EAAEhB,GAAG,CAACgC,KAAK,CAACoF,MAAM,CAAC,CAAC5C,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC,CAACtG,MAAM,CAAC8C,QAAQ,CAAC,CAAC,CAAC;EAC3G,CAAC,EAAE,CAACmB,QAAQ,EAAE,CAACA,QAAQ,CAAC,CAAC;AAC3B,CAAC;AACD,OAAO,IAAI2N,sBAAsB,GAAG,SAASA,sBAAsBA,CAAC/K,WAAW,EAAEgL,UAAU,EAAEC,QAAQ,EAAE;EACrG,OAAOlT,MAAM,CAACkB,IAAI,CAAC+G,WAAW,CAAC,CAAC6B,MAAM,CAAC,UAAUpC,MAAM,EAAEsJ,OAAO,EAAE;IAChE,IAAIO,KAAK,GAAGtJ,WAAW,CAAC+I,OAAO,CAAC;IAChC,IAAIU,WAAW,GAAGH,KAAK,CAACG,WAAW;IACnC,IAAIvM,MAAM,GAAGuM,WAAW,CAAC5H,MAAM,CAAC,UAAUC,GAAG,EAAE7E,KAAK,EAAE;MACpD,IAAIiO,CAAC,GAAGJ,iBAAiB,CAAC7N,KAAK,CAAC/E,KAAK,CAAC8S,UAAU,EAAEC,QAAQ,GAAG,CAAC,CAAC,CAAC;MAChE,OAAO,CAAChN,IAAI,CAAC/C,GAAG,CAAC4G,GAAG,CAAC,CAAC,CAAC,EAAEoJ,CAAC,CAAC,CAAC,CAAC,CAAC,EAAEjN,IAAI,CAAChD,GAAG,CAAC6G,GAAG,CAAC,CAAC,CAAC,EAAEoJ,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IACzD,CAAC,EAAE,CAAC9N,QAAQ,EAAE,CAACA,QAAQ,CAAC,CAAC;IACzB,OAAO,CAACa,IAAI,CAAC/C,GAAG,CAACgC,MAAM,CAAC,CAAC,CAAC,EAAEuC,MAAM,CAAC,CAAC,CAAC,CAAC,EAAExB,IAAI,CAAChD,GAAG,CAACiC,MAAM,CAAC,CAAC,CAAC,EAAEuC,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC;EACzE,CAAC,EAAE,CAACrC,QAAQ,EAAE,CAACA,QAAQ,CAAC,CAAC,CAACE,GAAG,CAAC,UAAUmC,MAAM,EAAE;IAC9C,OAAOA,MAAM,KAAKrC,QAAQ,IAAIqC,MAAM,KAAK,CAACrC,QAAQ,GAAG,CAAC,GAAGqC,MAAM;EACjE,CAAC,CAAC;AACJ,CAAC;AACD,OAAO,IAAI0L,aAAa,GAAG,iDAAiD;AAC5E,OAAO,IAAIC,aAAa,GAAG,kDAAkD;AAC7E,OAAO,IAAIC,oBAAoB,GAAG,SAASA,oBAAoBA,CAACC,eAAe,EAAEC,UAAU,EAAEC,iBAAiB,EAAE;EAC9G,IAAIpQ,UAAU,CAACkQ,eAAe,CAAC,EAAE;IAC/B,OAAOA,eAAe,CAACC,UAAU,EAAEC,iBAAiB,CAAC;EACvD;EACA,IAAI,CAACpT,KAAK,CAACI,OAAO,CAAC8S,eAAe,CAAC,EAAE;IACnC,OAAOC,UAAU;EACnB;EACA,IAAIrO,MAAM,GAAG,EAAE;;EAEf;EACA,IAAIjB,QAAQ,CAACqP,eAAe,CAAC,CAAC,CAAC,CAAC,EAAE;IAChCpO,MAAM,CAAC,CAAC,CAAC,GAAGsO,iBAAiB,GAAGF,eAAe,CAAC,CAAC,CAAC,GAAGrN,IAAI,CAAC/C,GAAG,CAACoQ,eAAe,CAAC,CAAC,CAAC,EAAEC,UAAU,CAAC,CAAC,CAAC,CAAC;EAClG,CAAC,MAAM,IAAIJ,aAAa,CAAC7S,IAAI,CAACgT,eAAe,CAAC,CAAC,CAAC,CAAC,EAAE;IACjD,IAAIrR,KAAK,GAAG,CAACkR,aAAa,CAACM,IAAI,CAACH,eAAe,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IACtDpO,MAAM,CAAC,CAAC,CAAC,GAAGqO,UAAU,CAAC,CAAC,CAAC,GAAGtR,KAAK;EACnC,CAAC,MAAM,IAAImB,UAAU,CAACkQ,eAAe,CAAC,CAAC,CAAC,CAAC,EAAE;IACzCpO,MAAM,CAAC,CAAC,CAAC,GAAGoO,eAAe,CAAC,CAAC,CAAC,CAACC,UAAU,CAAC,CAAC,CAAC,CAAC;EAC/C,CAAC,MAAM;IACLrO,MAAM,CAAC,CAAC,CAAC,GAAGqO,UAAU,CAAC,CAAC,CAAC;EAC3B;EACA,IAAItP,QAAQ,CAACqP,eAAe,CAAC,CAAC,CAAC,CAAC,EAAE;IAChCpO,MAAM,CAAC,CAAC,CAAC,GAAGsO,iBAAiB,GAAGF,eAAe,CAAC,CAAC,CAAC,GAAGrN,IAAI,CAAChD,GAAG,CAACqQ,eAAe,CAAC,CAAC,CAAC,EAAEC,UAAU,CAAC,CAAC,CAAC,CAAC;EAClG,CAAC,MAAM,IAAIH,aAAa,CAAC9S,IAAI,CAACgT,eAAe,CAAC,CAAC,CAAC,CAAC,EAAE;IACjD,IAAII,MAAM,GAAG,CAACN,aAAa,CAACK,IAAI,CAACH,eAAe,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IACvDpO,MAAM,CAAC,CAAC,CAAC,GAAGqO,UAAU,CAAC,CAAC,CAAC,GAAGG,MAAM;EACpC,CAAC,MAAM,IAAItQ,UAAU,CAACkQ,eAAe,CAAC,CAAC,CAAC,CAAC,EAAE;IACzCpO,MAAM,CAAC,CAAC,CAAC,GAAGoO,eAAe,CAAC,CAAC,CAAC,CAACC,UAAU,CAAC,CAAC,CAAC,CAAC;EAC/C,CAAC,MAAM;IACLrO,MAAM,CAAC,CAAC,CAAC,GAAGqO,UAAU,CAAC,CAAC,CAAC;EAC3B;EACA;;EAEA,OAAOrO,MAAM;AACf,CAAC;;AAED;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO,IAAIyO,iBAAiB,GAAG,SAASA,iBAAiBA,CAAC7N,IAAI,EAAEH,KAAK,EAAEiO,KAAK,EAAE;EAC5E;EACA,IAAI9N,IAAI,IAAIA,IAAI,CAACqH,KAAK,IAAIrH,IAAI,CAACqH,KAAK,CAACI,SAAS,EAAE;IAC9C;IACA,IAAIsG,SAAS,GAAG/N,IAAI,CAACqH,KAAK,CAACI,SAAS,CAAC,CAAC;IACtC,IAAI,CAACqG,KAAK,IAAIC,SAAS,GAAG,CAAC,EAAE;MAC3B,OAAOA,SAAS;IAClB;EACF;EACA,IAAI/N,IAAI,IAAIH,KAAK,IAAIA,KAAK,CAACjF,MAAM,IAAI,CAAC,EAAE;IACtC,IAAIoT,YAAY,GAAGnQ,MAAM,CAACgC,KAAK,EAAE,UAAU3G,CAAC,EAAE;MAC5C,OAAOA,CAAC,CAACyG,UAAU;IACrB,CAAC,CAAC;IACF,IAAI2D,QAAQ,GAAGhE,QAAQ;IACvB,KAAK,IAAIzE,CAAC,GAAG,CAAC,EAAEF,GAAG,GAAGqT,YAAY,CAACpT,MAAM,EAAEC,CAAC,GAAGF,GAAG,EAAEE,CAAC,EAAE,EAAE;MACvD,IAAI0F,GAAG,GAAGyN,YAAY,CAACnT,CAAC,CAAC;MACzB,IAAIqJ,IAAI,GAAG8J,YAAY,CAACnT,CAAC,GAAG,CAAC,CAAC;MAC9ByI,QAAQ,GAAGnD,IAAI,CAAC/C,GAAG,CAAC,CAACmD,GAAG,CAACZ,UAAU,IAAI,CAAC,KAAKuE,IAAI,CAACvE,UAAU,IAAI,CAAC,CAAC,EAAE2D,QAAQ,CAAC;IAC/E;IACA,OAAOA,QAAQ,KAAKhE,QAAQ,GAAG,CAAC,GAAGgE,QAAQ;EAC7C;EACA,OAAOwK,KAAK,GAAGhO,SAAS,GAAG,CAAC;AAC9B,CAAC;AACD;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO,IAAImO,yBAAyB,GAAG,SAASA,yBAAyBA,CAACT,eAAe,EAAEU,gBAAgB,EAAEC,SAAS,EAAE;EACtH,IAAI,CAACX,eAAe,IAAI,CAACA,eAAe,CAAC5S,MAAM,EAAE;IAC/C,OAAOsT,gBAAgB;EACzB;EACA,IAAItQ,OAAO,CAAC4P,eAAe,EAAEhQ,GAAG,CAAC2Q,SAAS,EAAE,0BAA0B,CAAC,CAAC,EAAE;IACxE,OAAOD,gBAAgB;EACzB;EACA,OAAOV,eAAe;AACxB,CAAC;AACD,OAAO,IAAIY,cAAc,GAAG,SAASA,cAAcA,CAACC,aAAa,EAAEC,OAAO,EAAE;EAC1E,IAAIhN,cAAc,GAAG+M,aAAa,CAACrP,IAAI,CAACuC,YAAY,GAAG7F,aAAa,CAACA,aAAa,CAAC,CAAC,CAAC,EAAE2S,aAAa,CAACrP,IAAI,CAACuC,YAAY,CAAC,EAAE8M,aAAa,CAAC7M,KAAK,CAAC,GAAG6M,aAAa,CAAC7M,KAAK;EACnK,IAAI5C,OAAO,GAAG0C,cAAc,CAAC1C,OAAO;IAClCvE,IAAI,GAAGiH,cAAc,CAACjH,IAAI;IAC1BkU,IAAI,GAAGjN,cAAc,CAACiN,IAAI;IAC1BC,SAAS,GAAGlN,cAAc,CAACkN,SAAS;IACpCC,WAAW,GAAGnN,cAAc,CAACmN,WAAW;IACxC/F,SAAS,GAAGpH,cAAc,CAACoH,SAAS;IACpCwC,IAAI,GAAG5J,cAAc,CAAC4J,IAAI;EAC5B,OAAOxP,aAAa,CAACA,aAAa,CAAC,CAAC,CAAC,EAAE6C,WAAW,CAAC8P,aAAa,EAAE,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE;IAC7EzP,OAAO,EAAEA,OAAO;IAChB2P,IAAI,EAAEA,IAAI;IACVC,SAAS,EAAEA,SAAS;IACpBnU,IAAI,EAAEA,IAAI,IAAIuE,OAAO;IACrB8P,KAAK,EAAEzN,yBAAyB,CAACoN,aAAa,CAAC;IAC/ClS,KAAK,EAAEwC,iBAAiB,CAAC2P,OAAO,EAAE1P,OAAO,CAAC;IAC1CI,IAAI,EAAEyP,WAAW;IACjBH,OAAO,EAAEA,OAAO;IAChB5F,SAAS,EAAEA,SAAS;IACpBwC,IAAI,EAAEA;EACR,CAAC,CAAC;AACJ,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}