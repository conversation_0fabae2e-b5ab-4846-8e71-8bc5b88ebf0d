{"ast": null, "code": "import axios from 'axios';\nimport config from '../config';\n\n// Crea un'istanza di axios con configurazione migliorata\nconst axiosInstance = axios.create({\n  baseURL: config.API_URL,\n  headers: {\n    'Content-Type': 'application/json'\n  },\n  timeout: 60000,\n  // 60 secondi\n  withCredentials: false // Disabilita l'invio di credenziali per evitare problemi CORS\n});\n\n// Configura axios per includere il token in tutte le richieste\naxiosInstance.interceptors.request.use(config => {\n  const token = localStorage.getItem('token');\n  if (token) {\n    config.headers.Authorization = `Bearer ${token}`;\n  }\n  return config;\n}, error => {\n  console.error('Errore nella configurazione della richiesta:', error);\n  return Promise.reject(error);\n});\n\n// Gestione globale degli errori\naxiosInstance.interceptors.response.use(response => {\n  return response;\n}, error => {\n  // Log dettagliato dell'errore (solo in console, non mostrato all'utente)\n  console.error('Errore nella risposta:', error);\n\n  // Gestione specifica per errori di rete\n  if (error.message && (error.message.includes('Network Error') || error.message.includes('Failed to fetch'))) {\n    console.error('Errore di rete:', error);\n    error.isNetworkError = true;\n    error.customMessage = 'Impossibile connettersi al server. Verifica la connessione di rete e riprova.';\n    // Sostituisci il messaggio di errore originale per evitare alert da localhost\n    error.message = error.customMessage;\n  }\n\n  // Gestione errori di timeout\n  if (error.code === 'ECONNABORTED') {\n    console.error('Timeout della richiesta:', error);\n    error.isTimeoutError = true;\n    error.customMessage = 'La richiesta ha impiegato troppo tempo. Riprova più tardi.';\n    // Sostituisci il messaggio di errore originale per evitare alert da localhost\n    error.message = error.customMessage;\n  }\n\n  // Gestione errori CORS\n  if (error.message && error.message.includes('CORS')) {\n    error.customMessage = 'Errore di comunicazione con il server. Riprova più tardi.';\n    error.message = error.customMessage;\n  }\n  return Promise.reject(error);\n});\nexport default axiosInstance;", "map": {"version": 3, "names": ["axios", "config", "axiosInstance", "create", "baseURL", "API_URL", "headers", "timeout", "withCredentials", "interceptors", "request", "use", "token", "localStorage", "getItem", "Authorization", "error", "console", "Promise", "reject", "response", "message", "includes", "isNetworkError", "customMessage", "code", "isTimeoutError"], "sources": ["C:/Users/<USER>/PycharmProjects/CMS/webapp/frontend/src/services/axiosConfig.js"], "sourcesContent": ["import axios from 'axios';\nimport config from '../config';\n\n// Crea un'istanza di axios con configurazione migliorata\nconst axiosInstance = axios.create({\n  baseURL: config.API_URL,\n  headers: {\n    'Content-Type': 'application/json',\n  },\n  timeout: 60000, // 60 secondi\n  withCredentials: false, // Disabilita l'invio di credenziali per evitare problemi CORS\n});\n\n// Configura axios per includere il token in tutte le richieste\naxiosInstance.interceptors.request.use(\n  (config) => {\n    const token = localStorage.getItem('token');\n    if (token) {\n      config.headers.Authorization = `Bearer ${token}`;\n    }\n    return config;\n  },\n  (error) => {\n    console.error('Errore nella configurazione della richiesta:', error);\n    return Promise.reject(error);\n  }\n);\n\n// Gestione globale degli errori\naxiosInstance.interceptors.response.use(\n  (response) => {\n    return response;\n  },\n  (error) => {\n    // Log dettagliato dell'errore (solo in console, non mostrato all'utente)\n    console.error('Errore nella risposta:', error);\n\n    // Gestione specifica per errori di rete\n    if (error.message && (error.message.includes('Network Error') || error.message.includes('Failed to fetch'))) {\n      console.error('Errore di rete:', error);\n      error.isNetworkError = true;\n      error.customMessage = 'Impossibile connettersi al server. Verifica la connessione di rete e riprova.';\n      // Sostituisci il messaggio di errore originale per evitare alert da localhost\n      error.message = error.customMessage;\n    }\n\n    // Gestione errori di timeout\n    if (error.code === 'ECONNABORTED') {\n      console.error('Timeout della richiesta:', error);\n      error.isTimeoutError = true;\n      error.customMessage = 'La richiesta ha impiegato troppo tempo. Riprova più tardi.';\n      // Sostituisci il messaggio di errore originale per evitare alert da localhost\n      error.message = error.customMessage;\n    }\n\n    // Gestione errori CORS\n    if (error.message && error.message.includes('CORS')) {\n      error.customMessage = 'Errore di comunicazione con il server. Riprova più tardi.';\n      error.message = error.customMessage;\n    }\n\n    return Promise.reject(error);\n  }\n);\n\nexport default axiosInstance;\n"], "mappings": "AAAA,OAAOA,KAAK,MAAM,OAAO;AACzB,OAAOC,MAAM,MAAM,WAAW;;AAE9B;AACA,MAAMC,aAAa,GAAGF,KAAK,CAACG,MAAM,CAAC;EACjCC,OAAO,EAAEH,MAAM,CAACI,OAAO;EACvBC,OAAO,EAAE;IACP,cAAc,EAAE;EAClB,CAAC;EACDC,OAAO,EAAE,KAAK;EAAE;EAChBC,eAAe,EAAE,KAAK,CAAE;AAC1B,CAAC,CAAC;;AAEF;AACAN,aAAa,CAACO,YAAY,CAACC,OAAO,CAACC,GAAG,CACnCV,MAAM,IAAK;EACV,MAAMW,KAAK,GAAGC,YAAY,CAACC,OAAO,CAAC,OAAO,CAAC;EAC3C,IAAIF,KAAK,EAAE;IACTX,MAAM,CAACK,OAAO,CAACS,aAAa,GAAG,UAAUH,KAAK,EAAE;EAClD;EACA,OAAOX,MAAM;AACf,CAAC,EACAe,KAAK,IAAK;EACTC,OAAO,CAACD,KAAK,CAAC,8CAA8C,EAAEA,KAAK,CAAC;EACpE,OAAOE,OAAO,CAACC,MAAM,CAACH,KAAK,CAAC;AAC9B,CACF,CAAC;;AAED;AACAd,aAAa,CAACO,YAAY,CAACW,QAAQ,CAACT,GAAG,CACpCS,QAAQ,IAAK;EACZ,OAAOA,QAAQ;AACjB,CAAC,EACAJ,KAAK,IAAK;EACT;EACAC,OAAO,CAACD,KAAK,CAAC,wBAAwB,EAAEA,KAAK,CAAC;;EAE9C;EACA,IAAIA,KAAK,CAACK,OAAO,KAAKL,KAAK,CAACK,OAAO,CAACC,QAAQ,CAAC,eAAe,CAAC,IAAIN,KAAK,CAACK,OAAO,CAACC,QAAQ,CAAC,iBAAiB,CAAC,CAAC,EAAE;IAC3GL,OAAO,CAACD,KAAK,CAAC,iBAAiB,EAAEA,KAAK,CAAC;IACvCA,KAAK,CAACO,cAAc,GAAG,IAAI;IAC3BP,KAAK,CAACQ,aAAa,GAAG,+EAA+E;IACrG;IACAR,KAAK,CAACK,OAAO,GAAGL,KAAK,CAACQ,aAAa;EACrC;;EAEA;EACA,IAAIR,KAAK,CAACS,IAAI,KAAK,cAAc,EAAE;IACjCR,OAAO,CAACD,KAAK,CAAC,0BAA0B,EAAEA,KAAK,CAAC;IAChDA,KAAK,CAACU,cAAc,GAAG,IAAI;IAC3BV,KAAK,CAACQ,aAAa,GAAG,4DAA4D;IAClF;IACAR,KAAK,CAACK,OAAO,GAAGL,KAAK,CAACQ,aAAa;EACrC;;EAEA;EACA,IAAIR,KAAK,CAACK,OAAO,IAAIL,KAAK,CAACK,OAAO,CAACC,QAAQ,CAAC,MAAM,CAAC,EAAE;IACnDN,KAAK,CAACQ,aAAa,GAAG,2DAA2D;IACjFR,KAAK,CAACK,OAAO,GAAGL,KAAK,CAACQ,aAAa;EACrC;EAEA,OAAON,OAAO,CAACC,MAAM,CAACH,KAAK,CAAC;AAC9B,CACF,CAAC;AAED,eAAed,aAAa", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}