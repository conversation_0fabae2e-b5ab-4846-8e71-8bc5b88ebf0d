{"ast": null, "code": "import { formatDistance } from \"./pt/_lib/formatDistance.mjs\";\nimport { formatLong } from \"./pt/_lib/formatLong.mjs\";\nimport { formatRelative } from \"./pt/_lib/formatRelative.mjs\";\nimport { localize } from \"./pt/_lib/localize.mjs\";\nimport { match } from \"./pt/_lib/match.mjs\";\n\n/**\n * @category Locales\n * @summary Portuguese locale.\n * @language Portuguese\n * @iso-639-2 por\n * <AUTHOR> [@dfreire](https://github.com/dfreire)\n * <AUTHOR> [@adrm](https://github.com/adrm)\n */\nexport const pt = {\n  code: \"pt\",\n  formatDistance: formatDistance,\n  formatLong: formatLong,\n  formatRelative: formatRelative,\n  localize: localize,\n  match: match,\n  options: {\n    weekStartsOn: 1 /* Monday */,\n    firstWeekContainsDate: 4\n  }\n};\n\n// Fallback for modularized imports:\nexport default pt;", "map": {"version": 3, "names": ["formatDistance", "formatLong", "formatRelative", "localize", "match", "pt", "code", "options", "weekStartsOn", "firstWeekContainsDate"], "sources": ["C:/CMS/webapp/frontend/node_modules/date-fns/locale/pt.mjs"], "sourcesContent": ["import { formatDistance } from \"./pt/_lib/formatDistance.mjs\";\nimport { formatLong } from \"./pt/_lib/formatLong.mjs\";\nimport { formatRelative } from \"./pt/_lib/formatRelative.mjs\";\nimport { localize } from \"./pt/_lib/localize.mjs\";\nimport { match } from \"./pt/_lib/match.mjs\";\n\n/**\n * @category Locales\n * @summary Portuguese locale.\n * @language Portuguese\n * @iso-639-2 por\n * <AUTHOR> [@dfreire](https://github.com/dfreire)\n * <AUTHOR> [@adrm](https://github.com/adrm)\n */\nexport const pt = {\n  code: \"pt\",\n  formatDistance: formatDistance,\n  formatLong: formatLong,\n  formatRelative: formatRelative,\n  localize: localize,\n  match: match,\n  options: {\n    weekStartsOn: 1 /* Monday */,\n    firstWeekContainsDate: 4,\n  },\n};\n\n// Fallback for modularized imports:\nexport default pt;\n"], "mappings": "AAAA,SAASA,cAAc,QAAQ,8BAA8B;AAC7D,SAASC,UAAU,QAAQ,0BAA0B;AACrD,SAASC,cAAc,QAAQ,8BAA8B;AAC7D,SAASC,QAAQ,QAAQ,wBAAwB;AACjD,SAASC,KAAK,QAAQ,qBAAqB;;AAE3C;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO,MAAMC,EAAE,GAAG;EAChBC,IAAI,EAAE,IAAI;EACVN,cAAc,EAAEA,cAAc;EAC9BC,UAAU,EAAEA,UAAU;EACtBC,cAAc,EAAEA,cAAc;EAC9BC,QAAQ,EAAEA,QAAQ;EAClBC,KAAK,EAAEA,KAAK;EACZG,OAAO,EAAE;IACPC,YAAY,EAAE,CAAC,CAAC;IAChBC,qBAAqB,EAAE;EACzB;AACF,CAAC;;AAED;AACA,eAAeJ,EAAE", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}