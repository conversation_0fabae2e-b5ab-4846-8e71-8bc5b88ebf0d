{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\PycharmProjects\\\\CMS\\\\webapp\\\\frontend\\\\src\\\\components\\\\common\\\\SelectedCantiereDisplay.js\";\nimport React from 'react';\nimport { Box, Typography, Chip } from '@mui/material';\nimport { Construction as ConstructionIcon } from '@mui/icons-material';\n\n/**\n * Componente che mostra il cantiere selezionato\n */\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst SelectedCantiereDisplay = () => {\n  // Recupera l'ID e il nome del cantiere selezionato dal localStorage\n  const selectedCantiereId = localStorage.getItem('selectedCantiereId');\n  const selectedCantiereName = localStorage.getItem('selectedCantiereName');\n\n  // Se non c'è un cantiere selezionato, non mostrare nulla\n  if (!selectedCantiereId) {\n    return null;\n  }\n  return /*#__PURE__*/_jsxDEV(Box, {\n    sx: {\n      display: 'flex',\n      alignItems: 'center',\n      mx: 2\n    },\n    className: \"selected-cantiere-display\",\n    children: [/*#__PURE__*/_jsxDEV(Typography, {\n      variant: \"body2\",\n      sx: {\n        mr: 1,\n        color: '#AFB1B3'\n      },\n      children: \"Cantiere attivo:\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 20,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Box, {\n      sx: {\n        display: 'flex',\n        alignItems: 'center'\n      },\n      children: [/*#__PURE__*/_jsxDEV(ConstructionIcon, {\n        fontSize: \"small\",\n        sx: {\n          mr: 0.5,\n          color: '#AFB1B3'\n        }\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 24,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Typography, {\n        variant: \"body2\",\n        sx: {\n          fontWeight: 'bold',\n          color: '#AFB1B3'\n        },\n        children: selectedCantiereName || selectedCantiereId\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 25,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 23,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 19,\n    columnNumber: 5\n  }, this);\n};\n_c = SelectedCantiereDisplay;\nexport default SelectedCantiereDisplay;\nvar _c;\n$RefreshReg$(_c, \"SelectedCantiereDisplay\");", "map": {"version": 3, "names": ["React", "Box", "Typography", "Chip", "Construction", "ConstructionIcon", "jsxDEV", "_jsxDEV", "SelectedCantiereDisplay", "selectedCantiereId", "localStorage", "getItem", "selectedCantiereName", "sx", "display", "alignItems", "mx", "className", "children", "variant", "mr", "color", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "fontSize", "fontWeight", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/PycharmProjects/CMS/webapp/frontend/src/components/common/SelectedCantiereDisplay.js"], "sourcesContent": ["import React from 'react';\nimport { Box, Typography, Chip } from '@mui/material';\nimport { Construction as ConstructionIcon } from '@mui/icons-material';\n\n/**\n * Componente che mostra il cantiere selezionato\n */\nconst SelectedCantiereDisplay = () => {\n  // Recupera l'ID e il nome del cantiere selezionato dal localStorage\n  const selectedCantiereId = localStorage.getItem('selectedCantiereId');\n  const selectedCantiereName = localStorage.getItem('selectedCantiereName');\n\n  // Se non c'è un cantiere selezionato, non mostrare nulla\n  if (!selectedCantiereId) {\n    return null;\n  }\n\n  return (\n    <Box sx={{ display: 'flex', alignItems: 'center', mx: 2 }} className=\"selected-cantiere-display\">\n      <Typography variant=\"body2\" sx={{ mr: 1, color: '#AFB1B3' }}>\n        Cantiere attivo:\n      </Typography>\n      <Box sx={{ display: 'flex', alignItems: 'center' }}>\n        <ConstructionIcon fontSize=\"small\" sx={{ mr: 0.5, color: '#AFB1B3' }} />\n        <Typography variant=\"body2\" sx={{ fontWeight: 'bold', color: '#AFB1B3' }}>\n          {selectedCantiereName || selectedCantiereId}\n        </Typography>\n      </Box>\n    </Box>\n  );\n};\n\nexport default SelectedCantiereDisplay;\n"], "mappings": ";AAAA,OAAOA,KAAK,MAAM,OAAO;AACzB,SAASC,GAAG,EAAEC,UAAU,EAAEC,IAAI,QAAQ,eAAe;AACrD,SAASC,YAAY,IAAIC,gBAAgB,QAAQ,qBAAqB;;AAEtE;AACA;AACA;AAFA,SAAAC,MAAA,IAAAC,OAAA;AAGA,MAAMC,uBAAuB,GAAGA,CAAA,KAAM;EACpC;EACA,MAAMC,kBAAkB,GAAGC,YAAY,CAACC,OAAO,CAAC,oBAAoB,CAAC;EACrE,MAAMC,oBAAoB,GAAGF,YAAY,CAACC,OAAO,CAAC,sBAAsB,CAAC;;EAEzE;EACA,IAAI,CAACF,kBAAkB,EAAE;IACvB,OAAO,IAAI;EACb;EAEA,oBACEF,OAAA,CAACN,GAAG;IAACY,EAAE,EAAE;MAAEC,OAAO,EAAE,MAAM;MAAEC,UAAU,EAAE,QAAQ;MAAEC,EAAE,EAAE;IAAE,CAAE;IAACC,SAAS,EAAC,2BAA2B;IAAAC,QAAA,gBAC9FX,OAAA,CAACL,UAAU;MAACiB,OAAO,EAAC,OAAO;MAACN,EAAE,EAAE;QAAEO,EAAE,EAAE,CAAC;QAAEC,KAAK,EAAE;MAAU,CAAE;MAAAH,QAAA,EAAC;IAE7D;MAAAI,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAY,CAAC,eACblB,OAAA,CAACN,GAAG;MAACY,EAAE,EAAE;QAAEC,OAAO,EAAE,MAAM;QAAEC,UAAU,EAAE;MAAS,CAAE;MAAAG,QAAA,gBACjDX,OAAA,CAACF,gBAAgB;QAACqB,QAAQ,EAAC,OAAO;QAACb,EAAE,EAAE;UAAEO,EAAE,EAAE,GAAG;UAAEC,KAAK,EAAE;QAAU;MAAE;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC,eACxElB,OAAA,CAACL,UAAU;QAACiB,OAAO,EAAC,OAAO;QAACN,EAAE,EAAE;UAAEc,UAAU,EAAE,MAAM;UAAEN,KAAK,EAAE;QAAU,CAAE;QAAAH,QAAA,EACtEN,oBAAoB,IAAIH;MAAkB;QAAAa,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACjC,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACV,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CAAC;AAEV,CAAC;AAACG,EAAA,GAvBIpB,uBAAuB;AAyB7B,eAAeA,uBAAuB;AAAC,IAAAoB,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}