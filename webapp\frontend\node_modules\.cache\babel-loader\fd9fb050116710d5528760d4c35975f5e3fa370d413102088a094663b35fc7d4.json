{"ast": null, "code": "import axiosInstance from './axiosConfig';\nconst nonConformitaService = {\n  // Ottiene la lista delle non conformità di un cantiere\n  getNonConformita: async (cantiereId, statoNc = null, tipoNc = null, skip = 0, limit = 100) => {\n    try {\n      const cantiereIdNum = parseInt(cantiereId, 10);\n      if (isNaN(cantiereIdNum)) {\n        throw new Error(`ID cantiere non valido: ${cantiereId}`);\n      }\n      const params = {\n        skip,\n        limit\n      };\n      if (statoNc) {\n        params.stato_nc = statoNc;\n      }\n      if (tipoNc) {\n        params.tipo_nc = tipoNc;\n      }\n      const response = await axiosInstance.get(`/cantieri/${cantiereIdNum}/non-conformita`, {\n        params\n      });\n      return response.data;\n    } catch (error) {\n      console.error('Get non conformità error:', error);\n      throw error.response ? error.response.data : error;\n    }\n  },\n  // Crea una nuova non conformità\n  createNonConformita: async (cantiereId, ncData) => {\n    try {\n      const cantiereIdNum = parseInt(cantiereId, 10);\n      if (isNaN(cantiereIdNum)) {\n        throw new Error(`ID cantiere non valido: ${cantiereId}`);\n      }\n      const response = await axiosInstance.post(`/cantieri/${cantiereIdNum}/non-conformita`, ncData);\n      return response.data;\n    } catch (error) {\n      console.error('Create non conformità error:', error);\n      throw error.response ? error.response.data : error;\n    }\n  },\n  // Ottiene i dettagli di una non conformità\n  getNonConformitaById: async (cantiereId, ncId) => {\n    try {\n      const cantiereIdNum = parseInt(cantiereId, 10);\n      if (isNaN(cantiereIdNum)) {\n        throw new Error(`ID cantiere non valido: ${cantiereId}`);\n      }\n      const response = await axiosInstance.get(`/cantieri/${cantiereIdNum}/non-conformita/${ncId}`);\n      return response.data;\n    } catch (error) {\n      console.error('Get non conformità error:', error);\n      throw error.response ? error.response.data : error;\n    }\n  },\n  // Aggiorna una non conformità\n  updateNonConformita: async (cantiereId, ncId, ncData) => {\n    try {\n      const cantiereIdNum = parseInt(cantiereId, 10);\n      if (isNaN(cantiereIdNum)) {\n        throw new Error(`ID cantiere non valido: ${cantiereId}`);\n      }\n      const response = await axiosInstance.put(`/cantieri/${cantiereIdNum}/non-conformita/${ncId}`, ncData);\n      return response.data;\n    } catch (error) {\n      console.error('Update non conformità error:', error);\n      throw error.response ? error.response.data : error;\n    }\n  },\n  // Elimina una non conformità\n  deleteNonConformita: async (cantiereId, ncId) => {\n    try {\n      const cantiereIdNum = parseInt(cantiereId, 10);\n      if (isNaN(cantiereIdNum)) {\n        throw new Error(`ID cantiere non valido: ${cantiereId}`);\n      }\n      const response = await axiosInstance.delete(`/cantieri/${cantiereIdNum}/non-conformita/${ncId}`);\n      return response.data;\n    } catch (error) {\n      console.error('Delete non conformità error:', error);\n      throw error.response ? error.response.data : error;\n    }\n  },\n  // Ottiene i tipi di non conformità disponibili\n  getTipiNc: async () => {\n    try {\n      const response = await axiosInstance.get('/cantieri/tipi-nc');\n      return response.data;\n    } catch (error) {\n      console.error('Get tipi NC error:', error);\n      throw error.response ? error.response.data : error;\n    }\n  }\n};\nexport default nonConformitaService;", "map": {"version": 3, "names": ["axiosInstance", "nonConformitaService", "getNonConformita", "cantiereId", "statoNc", "tipoNc", "skip", "limit", "cantiereIdNum", "parseInt", "isNaN", "Error", "params", "stato_nc", "tipo_nc", "response", "get", "data", "error", "console", "createNonConformita", "ncData", "post", "getNonConformitaById", "ncId", "updateNonConformita", "put", "deleteNonConformita", "delete", "getTipiNc"], "sources": ["C:/CMS/webapp/frontend/src/services/nonConformitaService.js"], "sourcesContent": ["import axiosInstance from './axiosConfig';\n\nconst nonConformitaService = {\n  // Ottiene la lista delle non conformità di un cantiere\n  getNonConformita: async (cantiereId, statoNc = null, tipoNc = null, skip = 0, limit = 100) => {\n    try {\n      const cantiereIdNum = parseInt(cantiereId, 10);\n      if (isNaN(cantiereIdNum)) {\n        throw new Error(`ID cantiere non valido: ${cantiereId}`);\n      }\n\n      const params = { skip, limit };\n      if (statoNc) {\n        params.stato_nc = statoNc;\n      }\n      if (tipoNc) {\n        params.tipo_nc = tipoNc;\n      }\n\n      const response = await axiosInstance.get(`/cantieri/${cantiereIdNum}/non-conformita`, {\n        params\n      });\n      return response.data;\n    } catch (error) {\n      console.error('Get non conformità error:', error);\n      throw error.response ? error.response.data : error;\n    }\n  },\n\n  // Crea una nuova non conformità\n  createNonConformita: async (cantiereId, ncData) => {\n    try {\n      const cantiereIdNum = parseInt(cantiereId, 10);\n      if (isNaN(cantiereIdNum)) {\n        throw new Error(`ID cantiere non valido: ${cantiereId}`);\n      }\n\n      const response = await axiosInstance.post(`/cantieri/${cantiereIdNum}/non-conformita`, ncData);\n      return response.data;\n    } catch (error) {\n      console.error('Create non conformità error:', error);\n      throw error.response ? error.response.data : error;\n    }\n  },\n\n  // Ottiene i dettagli di una non conformità\n  getNonConformitaById: async (cantiereId, ncId) => {\n    try {\n      const cantiereIdNum = parseInt(cantiereId, 10);\n      if (isNaN(cantiereIdNum)) {\n        throw new Error(`ID cantiere non valido: ${cantiereId}`);\n      }\n\n      const response = await axiosInstance.get(`/cantieri/${cantiereIdNum}/non-conformita/${ncId}`);\n      return response.data;\n    } catch (error) {\n      console.error('Get non conformità error:', error);\n      throw error.response ? error.response.data : error;\n    }\n  },\n\n  // Aggiorna una non conformità\n  updateNonConformita: async (cantiereId, ncId, ncData) => {\n    try {\n      const cantiereIdNum = parseInt(cantiereId, 10);\n      if (isNaN(cantiereIdNum)) {\n        throw new Error(`ID cantiere non valido: ${cantiereId}`);\n      }\n\n      const response = await axiosInstance.put(`/cantieri/${cantiereIdNum}/non-conformita/${ncId}`, ncData);\n      return response.data;\n    } catch (error) {\n      console.error('Update non conformità error:', error);\n      throw error.response ? error.response.data : error;\n    }\n  },\n\n  // Elimina una non conformità\n  deleteNonConformita: async (cantiereId, ncId) => {\n    try {\n      const cantiereIdNum = parseInt(cantiereId, 10);\n      if (isNaN(cantiereIdNum)) {\n        throw new Error(`ID cantiere non valido: ${cantiereId}`);\n      }\n\n      const response = await axiosInstance.delete(`/cantieri/${cantiereIdNum}/non-conformita/${ncId}`);\n      return response.data;\n    } catch (error) {\n      console.error('Delete non conformità error:', error);\n      throw error.response ? error.response.data : error;\n    }\n  },\n\n  // Ottiene i tipi di non conformità disponibili\n  getTipiNc: async () => {\n    try {\n      const response = await axiosInstance.get('/cantieri/tipi-nc');\n      return response.data;\n    } catch (error) {\n      console.error('Get tipi NC error:', error);\n      throw error.response ? error.response.data : error;\n    }\n  }\n};\n\nexport default nonConformitaService;\n"], "mappings": "AAAA,OAAOA,aAAa,MAAM,eAAe;AAEzC,MAAMC,oBAAoB,GAAG;EAC3B;EACAC,gBAAgB,EAAE,MAAAA,CAAOC,UAAU,EAAEC,OAAO,GAAG,IAAI,EAAEC,MAAM,GAAG,IAAI,EAAEC,IAAI,GAAG,CAAC,EAAEC,KAAK,GAAG,GAAG,KAAK;IAC5F,IAAI;MACF,MAAMC,aAAa,GAAGC,QAAQ,CAACN,UAAU,EAAE,EAAE,CAAC;MAC9C,IAAIO,KAAK,CAACF,aAAa,CAAC,EAAE;QACxB,MAAM,IAAIG,KAAK,CAAC,2BAA2BR,UAAU,EAAE,CAAC;MAC1D;MAEA,MAAMS,MAAM,GAAG;QAAEN,IAAI;QAAEC;MAAM,CAAC;MAC9B,IAAIH,OAAO,EAAE;QACXQ,MAAM,CAACC,QAAQ,GAAGT,OAAO;MAC3B;MACA,IAAIC,MAAM,EAAE;QACVO,MAAM,CAACE,OAAO,GAAGT,MAAM;MACzB;MAEA,MAAMU,QAAQ,GAAG,MAAMf,aAAa,CAACgB,GAAG,CAAC,aAAaR,aAAa,iBAAiB,EAAE;QACpFI;MACF,CAAC,CAAC;MACF,OAAOG,QAAQ,CAACE,IAAI;IACtB,CAAC,CAAC,OAAOC,KAAK,EAAE;MACdC,OAAO,CAACD,KAAK,CAAC,2BAA2B,EAAEA,KAAK,CAAC;MACjD,MAAMA,KAAK,CAACH,QAAQ,GAAGG,KAAK,CAACH,QAAQ,CAACE,IAAI,GAAGC,KAAK;IACpD;EACF,CAAC;EAED;EACAE,mBAAmB,EAAE,MAAAA,CAAOjB,UAAU,EAAEkB,MAAM,KAAK;IACjD,IAAI;MACF,MAAMb,aAAa,GAAGC,QAAQ,CAACN,UAAU,EAAE,EAAE,CAAC;MAC9C,IAAIO,KAAK,CAACF,aAAa,CAAC,EAAE;QACxB,MAAM,IAAIG,KAAK,CAAC,2BAA2BR,UAAU,EAAE,CAAC;MAC1D;MAEA,MAAMY,QAAQ,GAAG,MAAMf,aAAa,CAACsB,IAAI,CAAC,aAAad,aAAa,iBAAiB,EAAEa,MAAM,CAAC;MAC9F,OAAON,QAAQ,CAACE,IAAI;IACtB,CAAC,CAAC,OAAOC,KAAK,EAAE;MACdC,OAAO,CAACD,KAAK,CAAC,8BAA8B,EAAEA,KAAK,CAAC;MACpD,MAAMA,KAAK,CAACH,QAAQ,GAAGG,KAAK,CAACH,QAAQ,CAACE,IAAI,GAAGC,KAAK;IACpD;EACF,CAAC;EAED;EACAK,oBAAoB,EAAE,MAAAA,CAAOpB,UAAU,EAAEqB,IAAI,KAAK;IAChD,IAAI;MACF,MAAMhB,aAAa,GAAGC,QAAQ,CAACN,UAAU,EAAE,EAAE,CAAC;MAC9C,IAAIO,KAAK,CAACF,aAAa,CAAC,EAAE;QACxB,MAAM,IAAIG,KAAK,CAAC,2BAA2BR,UAAU,EAAE,CAAC;MAC1D;MAEA,MAAMY,QAAQ,GAAG,MAAMf,aAAa,CAACgB,GAAG,CAAC,aAAaR,aAAa,mBAAmBgB,IAAI,EAAE,CAAC;MAC7F,OAAOT,QAAQ,CAACE,IAAI;IACtB,CAAC,CAAC,OAAOC,KAAK,EAAE;MACdC,OAAO,CAACD,KAAK,CAAC,2BAA2B,EAAEA,KAAK,CAAC;MACjD,MAAMA,KAAK,CAACH,QAAQ,GAAGG,KAAK,CAACH,QAAQ,CAACE,IAAI,GAAGC,KAAK;IACpD;EACF,CAAC;EAED;EACAO,mBAAmB,EAAE,MAAAA,CAAOtB,UAAU,EAAEqB,IAAI,EAAEH,MAAM,KAAK;IACvD,IAAI;MACF,MAAMb,aAAa,GAAGC,QAAQ,CAACN,UAAU,EAAE,EAAE,CAAC;MAC9C,IAAIO,KAAK,CAACF,aAAa,CAAC,EAAE;QACxB,MAAM,IAAIG,KAAK,CAAC,2BAA2BR,UAAU,EAAE,CAAC;MAC1D;MAEA,MAAMY,QAAQ,GAAG,MAAMf,aAAa,CAAC0B,GAAG,CAAC,aAAalB,aAAa,mBAAmBgB,IAAI,EAAE,EAAEH,MAAM,CAAC;MACrG,OAAON,QAAQ,CAACE,IAAI;IACtB,CAAC,CAAC,OAAOC,KAAK,EAAE;MACdC,OAAO,CAACD,KAAK,CAAC,8BAA8B,EAAEA,KAAK,CAAC;MACpD,MAAMA,KAAK,CAACH,QAAQ,GAAGG,KAAK,CAACH,QAAQ,CAACE,IAAI,GAAGC,KAAK;IACpD;EACF,CAAC;EAED;EACAS,mBAAmB,EAAE,MAAAA,CAAOxB,UAAU,EAAEqB,IAAI,KAAK;IAC/C,IAAI;MACF,MAAMhB,aAAa,GAAGC,QAAQ,CAACN,UAAU,EAAE,EAAE,CAAC;MAC9C,IAAIO,KAAK,CAACF,aAAa,CAAC,EAAE;QACxB,MAAM,IAAIG,KAAK,CAAC,2BAA2BR,UAAU,EAAE,CAAC;MAC1D;MAEA,MAAMY,QAAQ,GAAG,MAAMf,aAAa,CAAC4B,MAAM,CAAC,aAAapB,aAAa,mBAAmBgB,IAAI,EAAE,CAAC;MAChG,OAAOT,QAAQ,CAACE,IAAI;IACtB,CAAC,CAAC,OAAOC,KAAK,EAAE;MACdC,OAAO,CAACD,KAAK,CAAC,8BAA8B,EAAEA,KAAK,CAAC;MACpD,MAAMA,KAAK,CAACH,QAAQ,GAAGG,KAAK,CAACH,QAAQ,CAACE,IAAI,GAAGC,KAAK;IACpD;EACF,CAAC;EAED;EACAW,SAAS,EAAE,MAAAA,CAAA,KAAY;IACrB,IAAI;MACF,MAAMd,QAAQ,GAAG,MAAMf,aAAa,CAACgB,GAAG,CAAC,mBAAmB,CAAC;MAC7D,OAAOD,QAAQ,CAACE,IAAI;IACtB,CAAC,CAAC,OAAOC,KAAK,EAAE;MACdC,OAAO,CAACD,KAAK,CAAC,oBAAoB,EAAEA,KAAK,CAAC;MAC1C,MAAMA,KAAK,CAACH,QAAQ,GAAGG,KAAK,CAACH,QAAQ,CAACE,IAAI,GAAGC,KAAK;IACpD;EACF;AACF,CAAC;AAED,eAAejB,oBAAoB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}