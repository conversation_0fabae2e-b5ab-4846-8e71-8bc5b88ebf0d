{"ast": null, "code": "var _jsxFileName = \"C:\\\\CMS\\\\webapp\\\\frontend\\\\src\\\\components\\\\comande\\\\InserimentoMetriDialog.js\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect } from 'react';\nimport { Dialog, DialogTitle, DialogContent, DialogActions, Button, TextField, Grid, Typography, Box, Alert, Chip, Table, TableBody, TableCell, TableContainer, TableHead, TableRow, Paper, IconButton, Tooltip, Autocomplete, FormControl, InputLabel, Select, MenuItem, ListItemText, ListItemIcon } from '@mui/material';\nimport { Save as SaveIcon, Cancel as CancelIcon, Cable as CableIcon, CheckCircle as CheckCircleIcon, Warning as WarningIcon, Storage as BobinaIcon, Error as ErrorIcon, Info as InfoIcon } from '@mui/icons-material';\nimport comandeService from '../../services/comandeService';\nimport caviService from '../../services/caviService';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst InserimentoMetriDialog = ({\n  open,\n  onClose,\n  comanda,\n  onSuccess\n}) => {\n  _s();\n  const [loading, setLoading] = useState(false);\n  const [error, setError] = useState(null);\n  const [cavi, setCavi] = useState([]);\n  const [datiPosa, setDatiPosa] = useState({});\n  const [validationErrors, setValidationErrors] = useState({});\n  const [bobineDisponibili, setBobineDisponibili] = useState([]);\n  const [loadingBobine, setLoadingBobine] = useState(false);\n  useEffect(() => {\n    if (open && comanda) {\n      loadCaviComanda();\n      loadBobineDisponibili();\n    }\n  }, [open, comanda]);\n  const loadCaviComanda = async () => {\n    try {\n      setLoading(true);\n      setError(null);\n      const caviData = await comandeService.getCaviComanda(comanda.codice_comanda);\n      setCavi(caviData);\n\n      // Inizializza i dati di posa per ogni cavo\n      const initialDati = {};\n      caviData.forEach(cavo => {\n        initialDati[cavo.id_cavo] = {\n          metratura_reale: cavo.metratura_reale || cavo.metratura_teorica || 0,\n          data_posa: new Date().toISOString().split('T')[0],\n          responsabile_posa: comanda.responsabile || '',\n          note: '',\n          id_bobina: cavo.id_bobina || '',\n          // Bobina attualmente associata\n          force_over: false // Flag per forzare associazione anche se metri insufficienti\n        };\n      });\n      setDatiPosa(initialDati);\n    } catch (err) {\n      console.error('Errore nel caricamento cavi:', err);\n      setError('Errore nel caricamento dei cavi della comanda');\n    } finally {\n      setLoading(false);\n    }\n  };\n  const loadBobineDisponibili = async () => {\n    try {\n      setLoadingBobine(true);\n\n      // Carica tutte le bobine disponibili per il cantiere\n      const bobineData = await caviService.getBobineDisponibili(comanda.id_cantiere);\n      setBobineDisponibili(bobineData);\n    } catch (err) {\n      console.error('Errore nel caricamento bobine:', err);\n      // Non bloccare l'interfaccia se le bobine non si caricano\n    } finally {\n      setLoadingBobine(false);\n    }\n  };\n  const handleMetriChange = (idCavo, value) => {\n    const numericValue = parseFloat(value) || 0;\n    setDatiPosa(prev => ({\n      ...prev,\n      [idCavo]: {\n        ...prev[idCavo],\n        metratura_reale: numericValue\n      }\n    }));\n\n    // Validazione metri vs teorico\n    const cavo = cavi.find(c => c.id_cavo === idCavo);\n    const datiCavo = datiPosa[idCavo];\n    let errors = [];\n    if (cavo && numericValue > cavo.metratura_teorica * 1.1) {\n      errors.push('Metratura superiore del 10% rispetto al teorico');\n    }\n\n    // Validazione metri vs bobina selezionata\n    if (datiCavo !== null && datiCavo !== void 0 && datiCavo.id_bobina && datiCavo.id_bobina !== 'BOBINA_VUOTA') {\n      const bobina = bobineDisponibili.find(b => b.id_bobina === datiCavo.id_bobina);\n      if (bobina && numericValue > bobina.metri_residui && !datiCavo.force_over) {\n        errors.push(`Bobina ha solo ${bobina.metri_residui}m residui (richiesti ${numericValue}m)`);\n      }\n    }\n    if (errors.length > 0) {\n      setValidationErrors(prev => ({\n        ...prev,\n        [idCavo]: errors.join('; ')\n      }));\n    } else {\n      setValidationErrors(prev => {\n        const newErrors = {\n          ...prev\n        };\n        delete newErrors[idCavo];\n        return newErrors;\n      });\n    }\n  };\n  const handleNoteChange = (idCavo, value) => {\n    setDatiPosa(prev => ({\n      ...prev,\n      [idCavo]: {\n        ...prev[idCavo],\n        note: value\n      }\n    }));\n  };\n  const handleBobinaChange = (idCavo, bobinaId) => {\n    setDatiPosa(prev => ({\n      ...prev,\n      [idCavo]: {\n        ...prev[idCavo],\n        id_bobina: bobinaId,\n        force_over: false // Reset force_over quando cambia bobina\n      }\n    }));\n\n    // Rivalidazione dopo cambio bobina\n    const datiCavo = datiPosa[idCavo];\n    if (datiCavo !== null && datiCavo !== void 0 && datiCavo.metratura_reale) {\n      handleMetriChange(idCavo, datiCavo.metratura_reale);\n    }\n  };\n  const handleForceOverChange = (idCavo, forceOver) => {\n    setDatiPosa(prev => ({\n      ...prev,\n      [idCavo]: {\n        ...prev[idCavo],\n        force_over: forceOver\n      }\n    }));\n\n    // Rivalidazione dopo cambio force_over\n    const datiCavo = datiPosa[idCavo];\n    if (datiCavo !== null && datiCavo !== void 0 && datiCavo.metratura_reale) {\n      handleMetriChange(idCavo, datiCavo.metratura_reale);\n    }\n  };\n  const handleSave = async () => {\n    try {\n      setLoading(true);\n      setError(null);\n\n      // Validazione finale\n      const hasErrors = Object.keys(validationErrors).length > 0;\n      if (hasErrors) {\n        setError('Correggere gli errori di validazione prima di salvare');\n        return;\n      }\n\n      // Salva i dati di posa con associazioni bobine\n      await comandeService.aggiornaDatiPosaConBobine(comanda.codice_comanda, datiPosa);\n      onSuccess === null || onSuccess === void 0 ? void 0 : onSuccess('Dati di posa salvati con successo');\n      onClose();\n    } catch (err) {\n      console.error('Errore nel salvataggio:', err);\n      setError(err.message || 'Errore nel salvataggio dei dati');\n    } finally {\n      setLoading(false);\n    }\n  };\n  const getTipoComandaColor = tipo => {\n    switch (tipo) {\n      case 'POSA':\n        return 'primary';\n      case 'COLLEGAMENTO_PARTENZA':\n        return 'warning';\n      case 'COLLEGAMENTO_ARRIVO':\n        return 'info';\n      case 'CERTIFICAZIONE':\n        return 'success';\n      default:\n        return 'default';\n    }\n  };\n  const getTipoComandaLabel = tipo => {\n    switch (tipo) {\n      case 'POSA':\n        return 'Posa';\n      case 'COLLEGAMENTO_PARTENZA':\n        return 'Coll. Partenza';\n      case 'COLLEGAMENTO_ARRIVO':\n        return 'Coll. Arrivo';\n      case 'CERTIFICAZIONE':\n        return 'Certificazione';\n      default:\n        return tipo;\n    }\n  };\n\n  // Filtra bobine compatibili per un cavo specifico\n  const getBobineCompatibili = cavo => {\n    return bobineDisponibili.filter(bobina => bobina.tipologia === cavo.tipologia && bobina.sezione === cavo.sezione && bobina.metri_residui > 0);\n  };\n\n  // Verifica se una bobina è compatibile con un cavo\n  const isBobinaCompatibile = (bobina, cavo) => {\n    return bobina.tipologia === cavo.tipologia && bobina.sezione === cavo.sezione;\n  };\n\n  // Ottiene il colore per lo stato della bobina\n  const getBobinaStatusColor = (bobina, metriRichiesti) => {\n    if (!bobina || bobina.id_bobina === 'BOBINA_VUOTA') return 'default';\n    if (metriRichiesti > bobina.metri_residui) return 'error';\n    if (bobina.metri_residui < bobina.metri_totali * 0.1) return 'warning';\n    return 'success';\n  };\n  if (!comanda) return null;\n  return /*#__PURE__*/_jsxDEV(Dialog, {\n    open: open,\n    onClose: onClose,\n    maxWidth: \"lg\",\n    fullWidth: true,\n    PaperProps: {\n      sx: {\n        minHeight: '70vh'\n      }\n    },\n    children: [/*#__PURE__*/_jsxDEV(DialogTitle, {\n      children: /*#__PURE__*/_jsxDEV(Box, {\n        display: \"flex\",\n        alignItems: \"center\",\n        gap: 2,\n        children: [/*#__PURE__*/_jsxDEV(CableIcon, {\n          color: \"primary\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 278,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Box, {\n          children: [/*#__PURE__*/_jsxDEV(Typography, {\n            variant: \"h6\",\n            children: \"Inserimento Metri Posati\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 280,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Box, {\n            display: \"flex\",\n            alignItems: \"center\",\n            gap: 1,\n            mt: 1,\n            children: [/*#__PURE__*/_jsxDEV(Typography, {\n              variant: \"body2\",\n              color: \"textSecondary\",\n              children: [\"Comanda: \", comanda.codice_comanda]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 284,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Chip, {\n              label: getTipoComandaLabel(comanda.tipo_comanda),\n              color: getTipoComandaColor(comanda.tipo_comanda),\n              size: \"small\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 287,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Typography, {\n              variant: \"body2\",\n              color: \"textSecondary\",\n              children: [\"Responsabile: \", comanda.responsabile]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 292,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 283,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 279,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 277,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 276,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(DialogContent, {\n      children: [error && /*#__PURE__*/_jsxDEV(Alert, {\n        severity: \"error\",\n        sx: {\n          mb: 2\n        },\n        children: error\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 302,\n        columnNumber: 11\n      }, this), loading ? /*#__PURE__*/_jsxDEV(Box, {\n        display: \"flex\",\n        justifyContent: \"center\",\n        p: 3,\n        children: /*#__PURE__*/_jsxDEV(Typography, {\n          children: \"Caricamento cavi...\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 309,\n          columnNumber: 13\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 308,\n        columnNumber: 11\n      }, this) : /*#__PURE__*/_jsxDEV(TableContainer, {\n        component: Paper,\n        variant: \"outlined\",\n        children: /*#__PURE__*/_jsxDEV(Table, {\n          size: \"small\",\n          children: [/*#__PURE__*/_jsxDEV(TableHead, {\n            children: /*#__PURE__*/_jsxDEV(TableRow, {\n              children: [/*#__PURE__*/_jsxDEV(TableCell, {\n                children: \"ID Cavo\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 316,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n                children: \"Tipologia\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 317,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n                children: \"Formazione\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 318,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n                children: \"Metri Teorici\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 319,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n                children: \"Metri Reali\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 320,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n                children: \"Bobina\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 321,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n                children: \"Stato\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 322,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n                children: \"Note\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 323,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 315,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 314,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(TableBody, {\n            children: cavi.map(cavo => {\n              var _cavo$metratura_teori, _datiPosa$cavo$id_cav, _datiPosa$cavo$id_cav2, _datiPosa$cavo$id_cav5, _datiPosa$cavo$id_cav6, _datiPosa$cavo$id_cav9, _datiPosa$cavo$id_cav1;\n              return /*#__PURE__*/_jsxDEV(TableRow, {\n                children: [/*#__PURE__*/_jsxDEV(TableCell, {\n                  children: /*#__PURE__*/_jsxDEV(Typography, {\n                    variant: \"body2\",\n                    fontWeight: \"bold\",\n                    children: cavo.id_cavo\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 330,\n                    columnNumber: 23\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 329,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n                  children: cavo.tipologia || 'N/A'\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 334,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n                  children: cavo.formazione || 'N/A'\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 335,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n                  children: /*#__PURE__*/_jsxDEV(Typography, {\n                    variant: \"body2\",\n                    children: [((_cavo$metratura_teori = cavo.metratura_teorica) === null || _cavo$metratura_teori === void 0 ? void 0 : _cavo$metratura_teori.toFixed(1)) || '0.0', \" m\"]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 337,\n                    columnNumber: 23\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 336,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n                  children: /*#__PURE__*/_jsxDEV(TextField, {\n                    type: \"number\",\n                    size: \"small\",\n                    value: ((_datiPosa$cavo$id_cav = datiPosa[cavo.id_cavo]) === null || _datiPosa$cavo$id_cav === void 0 ? void 0 : _datiPosa$cavo$id_cav.metratura_reale) || 0,\n                    onChange: e => handleMetriChange(cavo.id_cavo, e.target.value),\n                    error: !!validationErrors[cavo.id_cavo],\n                    helperText: validationErrors[cavo.id_cavo],\n                    inputProps: {\n                      min: 0,\n                      step: 0.1,\n                      style: {\n                        textAlign: 'right'\n                      }\n                    },\n                    sx: {\n                      width: 100\n                    }\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 342,\n                    columnNumber: 23\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 341,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n                  children: /*#__PURE__*/_jsxDEV(Box, {\n                    sx: {\n                      minWidth: 200\n                    },\n                    children: [/*#__PURE__*/_jsxDEV(Autocomplete, {\n                      size: \"small\",\n                      value: ((_datiPosa$cavo$id_cav2 = datiPosa[cavo.id_cavo]) === null || _datiPosa$cavo$id_cav2 === void 0 ? void 0 : _datiPosa$cavo$id_cav2.id_bobina) === 'BOBINA_VUOTA' ? {\n                        id_bobina: 'BOBINA_VUOTA',\n                        tipologia: 'Vuota',\n                        metri_residui: '∞'\n                      } : bobineDisponibili.find(b => {\n                        var _datiPosa$cavo$id_cav3;\n                        return b.id_bobina === ((_datiPosa$cavo$id_cav3 = datiPosa[cavo.id_cavo]) === null || _datiPosa$cavo$id_cav3 === void 0 ? void 0 : _datiPosa$cavo$id_cav3.id_bobina);\n                      }) || null,\n                      onChange: (event, newValue) => {\n                        handleBobinaChange(cavo.id_cavo, (newValue === null || newValue === void 0 ? void 0 : newValue.id_bobina) || '');\n                      },\n                      options: [{\n                        id_bobina: 'BOBINA_VUOTA',\n                        tipologia: 'Vuota',\n                        metri_residui: '∞'\n                      }, ...getBobineCompatibili(cavo)],\n                      getOptionLabel: option => {\n                        if (option.id_bobina === 'BOBINA_VUOTA') return 'BOBINA_VUOTA';\n                        return `${option.id_bobina} (${option.metri_residui}m)`;\n                      },\n                      renderOption: (props, option) => {\n                        var _datiPosa$cavo$id_cav4;\n                        return /*#__PURE__*/_jsxDEV(Box, {\n                          component: \"li\",\n                          ...props,\n                          children: [/*#__PURE__*/_jsxDEV(ListItemIcon, {\n                            children: /*#__PURE__*/_jsxDEV(BobinaIcon, {\n                              fontSize: \"small\",\n                              color: option.id_bobina === 'BOBINA_VUOTA' ? 'default' : getBobinaStatusColor(option, ((_datiPosa$cavo$id_cav4 = datiPosa[cavo.id_cavo]) === null || _datiPosa$cavo$id_cav4 === void 0 ? void 0 : _datiPosa$cavo$id_cav4.metratura_reale) || 0)\n                            }, void 0, false, {\n                              fileName: _jsxFileName,\n                              lineNumber: 380,\n                              columnNumber: 33\n                            }, this)\n                          }, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 379,\n                            columnNumber: 31\n                          }, this), /*#__PURE__*/_jsxDEV(ListItemText, {\n                            primary: option.id_bobina === 'BOBINA_VUOTA' ? 'BOBINA_VUOTA' : option.id_bobina,\n                            secondary: option.id_bobina === 'BOBINA_VUOTA' ? 'Nessuna bobina associata' : `${option.tipologia} ${option.sezione} - ${option.metri_residui}m residui`\n                          }, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 389,\n                            columnNumber: 31\n                          }, this)]\n                        }, void 0, true, {\n                          fileName: _jsxFileName,\n                          lineNumber: 378,\n                          columnNumber: 29\n                        }, this);\n                      },\n                      loading: loadingBobine,\n                      disabled: loadingBobine,\n                      sx: {\n                        width: '100%'\n                      }\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 359,\n                      columnNumber: 25\n                    }, this), ((_datiPosa$cavo$id_cav5 = datiPosa[cavo.id_cavo]) === null || _datiPosa$cavo$id_cav5 === void 0 ? void 0 : _datiPosa$cavo$id_cav5.id_bobina) && ((_datiPosa$cavo$id_cav6 = datiPosa[cavo.id_cavo]) === null || _datiPosa$cavo$id_cav6 === void 0 ? void 0 : _datiPosa$cavo$id_cav6.id_bobina) !== 'BOBINA_VUOTA' && (_datiPosa$cavo$id_cav8 => {\n                      const bobina = bobineDisponibili.find(b => {\n                        var _datiPosa$cavo$id_cav7;\n                        return b.id_bobina === ((_datiPosa$cavo$id_cav7 = datiPosa[cavo.id_cavo]) === null || _datiPosa$cavo$id_cav7 === void 0 ? void 0 : _datiPosa$cavo$id_cav7.id_bobina);\n                      });\n                      const metriRichiesti = ((_datiPosa$cavo$id_cav8 = datiPosa[cavo.id_cavo]) === null || _datiPosa$cavo$id_cav8 === void 0 ? void 0 : _datiPosa$cavo$id_cav8.metratura_reale) || 0;\n                      return bobina && metriRichiesti > bobina.metri_residui;\n                    })() && /*#__PURE__*/_jsxDEV(Box, {\n                      sx: {\n                        mt: 1\n                      },\n                      children: /*#__PURE__*/_jsxDEV(Button, {\n                        size: \"small\",\n                        variant: (_datiPosa$cavo$id_cav9 = datiPosa[cavo.id_cavo]) !== null && _datiPosa$cavo$id_cav9 !== void 0 && _datiPosa$cavo$id_cav9.force_over ? \"contained\" : \"outlined\",\n                        color: \"warning\",\n                        onClick: () => {\n                          var _datiPosa$cavo$id_cav0;\n                          return handleForceOverChange(cavo.id_cavo, !((_datiPosa$cavo$id_cav0 = datiPosa[cavo.id_cavo]) !== null && _datiPosa$cavo$id_cav0 !== void 0 && _datiPosa$cavo$id_cav0.force_over));\n                        },\n                        startIcon: /*#__PURE__*/_jsxDEV(WarningIcon, {}, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 417,\n                          columnNumber: 42\n                        }, this),\n                        children: \"Forza Over\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 412,\n                        columnNumber: 29\n                      }, this)\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 411,\n                      columnNumber: 27\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 358,\n                    columnNumber: 23\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 357,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n                  children: cavo.stato_installazione === 'Installato' ? /*#__PURE__*/_jsxDEV(Chip, {\n                    icon: /*#__PURE__*/_jsxDEV(CheckCircleIcon, {}, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 428,\n                      columnNumber: 33\n                    }, this),\n                    label: \"Installato\",\n                    color: \"success\",\n                    size: \"small\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 427,\n                    columnNumber: 25\n                  }, this) : /*#__PURE__*/_jsxDEV(Chip, {\n                    icon: /*#__PURE__*/_jsxDEV(WarningIcon, {}, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 435,\n                      columnNumber: 33\n                    }, this),\n                    label: \"Da Installare\",\n                    color: \"warning\",\n                    size: \"small\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 434,\n                    columnNumber: 25\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 425,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n                  children: /*#__PURE__*/_jsxDEV(TextField, {\n                    size: \"small\",\n                    placeholder: \"Note opzionali...\",\n                    value: ((_datiPosa$cavo$id_cav1 = datiPosa[cavo.id_cavo]) === null || _datiPosa$cavo$id_cav1 === void 0 ? void 0 : _datiPosa$cavo$id_cav1.note) || '',\n                    onChange: e => handleNoteChange(cavo.id_cavo, e.target.value),\n                    sx: {\n                      width: 150\n                    }\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 443,\n                    columnNumber: 23\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 442,\n                  columnNumber: 21\n                }, this)]\n              }, cavo.id_cavo, true, {\n                fileName: _jsxFileName,\n                lineNumber: 328,\n                columnNumber: 19\n              }, this);\n            })\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 326,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 313,\n          columnNumber: 13\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 312,\n        columnNumber: 11\n      }, this), cavi.length === 0 && !loading && /*#__PURE__*/_jsxDEV(Alert, {\n        severity: \"info\",\n        sx: {\n          mt: 2\n        },\n        children: \"Nessun cavo assegnato a questa comanda.\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 459,\n        columnNumber: 11\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 300,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(DialogActions, {\n      sx: {\n        p: 2,\n        gap: 1\n      },\n      children: [/*#__PURE__*/_jsxDEV(Button, {\n        onClick: onClose,\n        startIcon: /*#__PURE__*/_jsxDEV(CancelIcon, {}, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 468,\n          columnNumber: 22\n        }, this),\n        disabled: loading,\n        children: \"Annulla\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 466,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Button, {\n        onClick: handleSave,\n        variant: \"contained\",\n        startIcon: /*#__PURE__*/_jsxDEV(SaveIcon, {}, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 476,\n          columnNumber: 22\n        }, this),\n        disabled: loading || cavi.length === 0,\n        children: \"Salva Dati Posa\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 473,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 465,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 267,\n    columnNumber: 5\n  }, this);\n};\n_s(InserimentoMetriDialog, \"qZFpwoKx7H/Z3PEQZ2tXwtWx1vY=\");\n_c = InserimentoMetriDialog;\nexport default InserimentoMetriDialog;\nvar _c;\n$RefreshReg$(_c, \"InserimentoMetriDialog\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "Dialog", "DialogTitle", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "DialogActions", "<PERSON><PERSON>", "TextField", "Grid", "Typography", "Box", "<PERSON><PERSON>", "Chip", "Table", "TableBody", "TableCell", "TableContainer", "TableHead", "TableRow", "Paper", "IconButton", "<PERSON><PERSON><PERSON>", "Autocomplete", "FormControl", "InputLabel", "Select", "MenuItem", "ListItemText", "ListItemIcon", "Save", "SaveIcon", "Cancel", "CancelIcon", "Cable", "CableIcon", "CheckCircle", "CheckCircleIcon", "Warning", "WarningIcon", "Storage", "BobinaIcon", "Error", "ErrorIcon", "Info", "InfoIcon", "comandeService", "caviService", "jsxDEV", "_jsxDEV", "InserimentoMetriDialog", "open", "onClose", "comanda", "onSuccess", "_s", "loading", "setLoading", "error", "setError", "cavi", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "setDatiPosa", "validationErrors", "setValidationErrors", "bobine<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "setBobineDisponibili", "loadingBobine", "setLoadingBob<PERSON>", "loadCaviComanda", "loadBobineDisponibili", "caviData", "getCaviComanda", "codice_comanda", "initialDati", "for<PERSON>ach", "cavo", "id_cavo", "metratura_reale", "metratura_teorica", "data_posa", "Date", "toISOString", "split", "responsabile_posa", "responsabile", "note", "id_bobina", "force_over", "err", "console", "bobine<PERSON><PERSON>", "getBobineDisponibili", "id_cantiere", "handleMetriChange", "idCavo", "value", "numericValue", "parseFloat", "prev", "find", "c", "datiCavo", "errors", "push", "bobina", "b", "metri_residui", "length", "join", "newErrors", "handleNoteChange", "handleBobinaChange", "bobina<PERSON>d", "handleForceOverChange", "forceOver", "handleSave", "hasErrors", "Object", "keys", "aggiornaDatiPosaConBobine", "message", "getTipoComandaColor", "tipo", "getTipoComandaLabel", "getBobineCompatibili", "filter", "tipologia", "sezione", "isBobinaCompatibile", "getBobinaStatusColor", "metriRichiesti", "metri_totali", "max<PERSON><PERSON><PERSON>", "fullWidth", "PaperProps", "sx", "minHeight", "children", "display", "alignItems", "gap", "color", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "variant", "mt", "label", "tipo_comanda", "size", "severity", "mb", "justifyContent", "p", "component", "map", "_cavo$metratura_teori", "_datiPosa$cavo$id_cav", "_datiPosa$cavo$id_cav2", "_datiPosa$cavo$id_cav5", "_datiPosa$cavo$id_cav6", "_datiPosa$cavo$id_cav9", "_datiPosa$cavo$id_cav1", "fontWeight", "formazione", "toFixed", "type", "onChange", "e", "target", "helperText", "inputProps", "min", "step", "style", "textAlign", "width", "min<PERSON><PERSON><PERSON>", "_datiPosa$cavo$id_cav3", "event", "newValue", "options", "getOptionLabel", "option", "renderOption", "props", "_datiPosa$cavo$id_cav4", "fontSize", "primary", "secondary", "disabled", "_datiPosa$cavo$id_cav8", "_datiPosa$cavo$id_cav7", "onClick", "_datiPosa$cavo$id_cav0", "startIcon", "stato_installazione", "icon", "placeholder", "_c", "$RefreshReg$"], "sources": ["C:/CMS/webapp/frontend/src/components/comande/InserimentoMetriDialog.js"], "sourcesContent": ["import React, { useState, useEffect } from 'react';\nimport {\n  Dialog,\n  DialogTitle,\n  DialogContent,\n  DialogActions,\n  Button,\n  TextField,\n  Grid,\n  Typography,\n  Box,\n  Alert,\n  Chip,\n  Table,\n  TableBody,\n  TableCell,\n  TableContainer,\n  TableHead,\n  TableRow,\n  Paper,\n  IconButton,\n  Tooltip,\n  Autocomplete,\n  FormControl,\n  InputLabel,\n  Select,\n  MenuItem,\n  ListItemText,\n  ListItemIcon\n} from '@mui/material';\nimport {\n  Save as SaveIcon,\n  Cancel as CancelIcon,\n  Cable as CableIcon,\n  CheckCircle as CheckCircleIcon,\n  Warning as WarningIcon,\n  Storage as BobinaIcon,\n  Error as ErrorIcon,\n  Info as InfoIcon\n} from '@mui/icons-material';\nimport comandeService from '../../services/comandeService';\nimport caviService from '../../services/caviService';\n\nconst InserimentoMetriDialog = ({\n  open,\n  onClose,\n  comanda,\n  onSuccess\n}) => {\n  const [loading, setLoading] = useState(false);\n  const [error, setError] = useState(null);\n  const [cavi, setCavi] = useState([]);\n  const [datiPosa, setDatiPosa] = useState({});\n  const [validationErrors, setValidationErrors] = useState({});\n  const [bobineDisponibili, setBobineDisponibili] = useState([]);\n  const [loadingBobine, setLoadingBobine] = useState(false);\n\n  useEffect(() => {\n    if (open && comanda) {\n      loadCaviComanda();\n      loadBobineDisponibili();\n    }\n  }, [open, comanda]);\n\n  const loadCaviComanda = async () => {\n    try {\n      setLoading(true);\n      setError(null);\n      \n      const caviData = await comandeService.getCaviComanda(comanda.codice_comanda);\n      setCavi(caviData);\n      \n      // Inizializza i dati di posa per ogni cavo\n      const initialDati = {};\n      caviData.forEach(cavo => {\n        initialDati[cavo.id_cavo] = {\n          metratura_reale: cavo.metratura_reale || cavo.metratura_teorica || 0,\n          data_posa: new Date().toISOString().split('T')[0],\n          responsabile_posa: comanda.responsabile || '',\n          note: '',\n          id_bobina: cavo.id_bobina || '', // Bobina attualmente associata\n          force_over: false // Flag per forzare associazione anche se metri insufficienti\n        };\n      });\n      setDatiPosa(initialDati);\n      \n    } catch (err) {\n      console.error('Errore nel caricamento cavi:', err);\n      setError('Errore nel caricamento dei cavi della comanda');\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  const loadBobineDisponibili = async () => {\n    try {\n      setLoadingBobine(true);\n\n      // Carica tutte le bobine disponibili per il cantiere\n      const bobineData = await caviService.getBobineDisponibili(comanda.id_cantiere);\n      setBobineDisponibili(bobineData);\n\n    } catch (err) {\n      console.error('Errore nel caricamento bobine:', err);\n      // Non bloccare l'interfaccia se le bobine non si caricano\n    } finally {\n      setLoadingBobine(false);\n    }\n  };\n\n  const handleMetriChange = (idCavo, value) => {\n    const numericValue = parseFloat(value) || 0;\n\n    setDatiPosa(prev => ({\n      ...prev,\n      [idCavo]: {\n        ...prev[idCavo],\n        metratura_reale: numericValue\n      }\n    }));\n\n    // Validazione metri vs teorico\n    const cavo = cavi.find(c => c.id_cavo === idCavo);\n    const datiCavo = datiPosa[idCavo];\n    let errors = [];\n\n    if (cavo && numericValue > cavo.metratura_teorica * 1.1) {\n      errors.push('Metratura superiore del 10% rispetto al teorico');\n    }\n\n    // Validazione metri vs bobina selezionata\n    if (datiCavo?.id_bobina && datiCavo.id_bobina !== 'BOBINA_VUOTA') {\n      const bobina = bobineDisponibili.find(b => b.id_bobina === datiCavo.id_bobina);\n      if (bobina && numericValue > bobina.metri_residui && !datiCavo.force_over) {\n        errors.push(`Bobina ha solo ${bobina.metri_residui}m residui (richiesti ${numericValue}m)`);\n      }\n    }\n\n    if (errors.length > 0) {\n      setValidationErrors(prev => ({\n        ...prev,\n        [idCavo]: errors.join('; ')\n      }));\n    } else {\n      setValidationErrors(prev => {\n        const newErrors = { ...prev };\n        delete newErrors[idCavo];\n        return newErrors;\n      });\n    }\n  };\n\n  const handleNoteChange = (idCavo, value) => {\n    setDatiPosa(prev => ({\n      ...prev,\n      [idCavo]: {\n        ...prev[idCavo],\n        note: value\n      }\n    }));\n  };\n\n  const handleBobinaChange = (idCavo, bobinaId) => {\n    setDatiPosa(prev => ({\n      ...prev,\n      [idCavo]: {\n        ...prev[idCavo],\n        id_bobina: bobinaId,\n        force_over: false // Reset force_over quando cambia bobina\n      }\n    }));\n\n    // Rivalidazione dopo cambio bobina\n    const datiCavo = datiPosa[idCavo];\n    if (datiCavo?.metratura_reale) {\n      handleMetriChange(idCavo, datiCavo.metratura_reale);\n    }\n  };\n\n  const handleForceOverChange = (idCavo, forceOver) => {\n    setDatiPosa(prev => ({\n      ...prev,\n      [idCavo]: {\n        ...prev[idCavo],\n        force_over: forceOver\n      }\n    }));\n\n    // Rivalidazione dopo cambio force_over\n    const datiCavo = datiPosa[idCavo];\n    if (datiCavo?.metratura_reale) {\n      handleMetriChange(idCavo, datiCavo.metratura_reale);\n    }\n  };\n\n  const handleSave = async () => {\n    try {\n      setLoading(true);\n      setError(null);\n\n      // Validazione finale\n      const hasErrors = Object.keys(validationErrors).length > 0;\n      if (hasErrors) {\n        setError('Correggere gli errori di validazione prima di salvare');\n        return;\n      }\n\n      // Salva i dati di posa con associazioni bobine\n      await comandeService.aggiornaDatiPosaConBobine(comanda.codice_comanda, datiPosa);\n      \n      onSuccess?.('Dati di posa salvati con successo');\n      onClose();\n      \n    } catch (err) {\n      console.error('Errore nel salvataggio:', err);\n      setError(err.message || 'Errore nel salvataggio dei dati');\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  const getTipoComandaColor = (tipo) => {\n    switch (tipo) {\n      case 'POSA': return 'primary';\n      case 'COLLEGAMENTO_PARTENZA': return 'warning';\n      case 'COLLEGAMENTO_ARRIVO': return 'info';\n      case 'CERTIFICAZIONE': return 'success';\n      default: return 'default';\n    }\n  };\n\n  const getTipoComandaLabel = (tipo) => {\n    switch (tipo) {\n      case 'POSA': return 'Posa';\n      case 'COLLEGAMENTO_PARTENZA': return 'Coll. Partenza';\n      case 'COLLEGAMENTO_ARRIVO': return 'Coll. Arrivo';\n      case 'CERTIFICAZIONE': return 'Certificazione';\n      default: return tipo;\n    }\n  };\n\n  // Filtra bobine compatibili per un cavo specifico\n  const getBobineCompatibili = (cavo) => {\n    return bobineDisponibili.filter(bobina =>\n      bobina.tipologia === cavo.tipologia &&\n      bobina.sezione === cavo.sezione &&\n      bobina.metri_residui > 0\n    );\n  };\n\n  // Verifica se una bobina è compatibile con un cavo\n  const isBobinaCompatibile = (bobina, cavo) => {\n    return bobina.tipologia === cavo.tipologia && bobina.sezione === cavo.sezione;\n  };\n\n  // Ottiene il colore per lo stato della bobina\n  const getBobinaStatusColor = (bobina, metriRichiesti) => {\n    if (!bobina || bobina.id_bobina === 'BOBINA_VUOTA') return 'default';\n    if (metriRichiesti > bobina.metri_residui) return 'error';\n    if (bobina.metri_residui < bobina.metri_totali * 0.1) return 'warning';\n    return 'success';\n  };\n\n  if (!comanda) return null;\n\n  return (\n    <Dialog \n      open={open} \n      onClose={onClose}\n      maxWidth=\"lg\"\n      fullWidth\n      PaperProps={{\n        sx: { minHeight: '70vh' }\n      }}\n    >\n      <DialogTitle>\n        <Box display=\"flex\" alignItems=\"center\" gap={2}>\n          <CableIcon color=\"primary\" />\n          <Box>\n            <Typography variant=\"h6\">\n              Inserimento Metri Posati\n            </Typography>\n            <Box display=\"flex\" alignItems=\"center\" gap={1} mt={1}>\n              <Typography variant=\"body2\" color=\"textSecondary\">\n                Comanda: {comanda.codice_comanda}\n              </Typography>\n              <Chip \n                label={getTipoComandaLabel(comanda.tipo_comanda)}\n                color={getTipoComandaColor(comanda.tipo_comanda)}\n                size=\"small\"\n              />\n              <Typography variant=\"body2\" color=\"textSecondary\">\n                Responsabile: {comanda.responsabile}\n              </Typography>\n            </Box>\n          </Box>\n        </Box>\n      </DialogTitle>\n\n      <DialogContent>\n        {error && (\n          <Alert severity=\"error\" sx={{ mb: 2 }}>\n            {error}\n          </Alert>\n        )}\n\n        {loading ? (\n          <Box display=\"flex\" justifyContent=\"center\" p={3}>\n            <Typography>Caricamento cavi...</Typography>\n          </Box>\n        ) : (\n          <TableContainer component={Paper} variant=\"outlined\">\n            <Table size=\"small\">\n              <TableHead>\n                <TableRow>\n                  <TableCell>ID Cavo</TableCell>\n                  <TableCell>Tipologia</TableCell>\n                  <TableCell>Formazione</TableCell>\n                  <TableCell>Metri Teorici</TableCell>\n                  <TableCell>Metri Reali</TableCell>\n                  <TableCell>Bobina</TableCell>\n                  <TableCell>Stato</TableCell>\n                  <TableCell>Note</TableCell>\n                </TableRow>\n              </TableHead>\n              <TableBody>\n                {cavi.map((cavo) => (\n                  <TableRow key={cavo.id_cavo}>\n                    <TableCell>\n                      <Typography variant=\"body2\" fontWeight=\"bold\">\n                        {cavo.id_cavo}\n                      </Typography>\n                    </TableCell>\n                    <TableCell>{cavo.tipologia || 'N/A'}</TableCell>\n                    <TableCell>{cavo.formazione || 'N/A'}</TableCell>\n                    <TableCell>\n                      <Typography variant=\"body2\">\n                        {cavo.metratura_teorica?.toFixed(1) || '0.0'} m\n                      </Typography>\n                    </TableCell>\n                    <TableCell>\n                      <TextField\n                        type=\"number\"\n                        size=\"small\"\n                        value={datiPosa[cavo.id_cavo]?.metratura_reale || 0}\n                        onChange={(e) => handleMetriChange(cavo.id_cavo, e.target.value)}\n                        error={!!validationErrors[cavo.id_cavo]}\n                        helperText={validationErrors[cavo.id_cavo]}\n                        inputProps={{\n                          min: 0,\n                          step: 0.1,\n                          style: { textAlign: 'right' }\n                        }}\n                        sx={{ width: 100 }}\n                      />\n                    </TableCell>\n                    <TableCell>\n                      <Box sx={{ minWidth: 200 }}>\n                        <Autocomplete\n                          size=\"small\"\n                          value={\n                            datiPosa[cavo.id_cavo]?.id_bobina === 'BOBINA_VUOTA'\n                              ? { id_bobina: 'BOBINA_VUOTA', tipologia: 'Vuota', metri_residui: '∞' }\n                              : bobineDisponibili.find(b => b.id_bobina === datiPosa[cavo.id_cavo]?.id_bobina) || null\n                          }\n                          onChange={(event, newValue) => {\n                            handleBobinaChange(cavo.id_cavo, newValue?.id_bobina || '');\n                          }}\n                          options={[\n                            { id_bobina: 'BOBINA_VUOTA', tipologia: 'Vuota', metri_residui: '∞' },\n                            ...getBobineCompatibili(cavo)\n                          ]}\n                          getOptionLabel={(option) => {\n                            if (option.id_bobina === 'BOBINA_VUOTA') return 'BOBINA_VUOTA';\n                            return `${option.id_bobina} (${option.metri_residui}m)`;\n                          }}\n                          renderOption={(props, option) => (\n                            <Box component=\"li\" {...props}>\n                              <ListItemIcon>\n                                <BobinaIcon\n                                  fontSize=\"small\"\n                                  color={\n                                    option.id_bobina === 'BOBINA_VUOTA'\n                                      ? 'default'\n                                      : getBobinaStatusColor(option, datiPosa[cavo.id_cavo]?.metratura_reale || 0)\n                                  }\n                                />\n                              </ListItemIcon>\n                              <ListItemText\n                                primary={option.id_bobina === 'BOBINA_VUOTA' ? 'BOBINA_VUOTA' : option.id_bobina}\n                                secondary={\n                                  option.id_bobina === 'BOBINA_VUOTA'\n                                    ? 'Nessuna bobina associata'\n                                    : `${option.tipologia} ${option.sezione} - ${option.metri_residui}m residui`\n                                }\n                              />\n                            </Box>\n                          )}\n                          loading={loadingBobine}\n                          disabled={loadingBobine}\n                          sx={{ width: '100%' }}\n                        />\n                        {/* Checkbox per force_over se necessario */}\n                        {datiPosa[cavo.id_cavo]?.id_bobina &&\n                         datiPosa[cavo.id_cavo]?.id_bobina !== 'BOBINA_VUOTA' &&\n                         (() => {\n                           const bobina = bobineDisponibili.find(b => b.id_bobina === datiPosa[cavo.id_cavo]?.id_bobina);\n                           const metriRichiesti = datiPosa[cavo.id_cavo]?.metratura_reale || 0;\n                           return bobina && metriRichiesti > bobina.metri_residui;\n                         })() && (\n                          <Box sx={{ mt: 1 }}>\n                            <Button\n                              size=\"small\"\n                              variant={datiPosa[cavo.id_cavo]?.force_over ? \"contained\" : \"outlined\"}\n                              color=\"warning\"\n                              onClick={() => handleForceOverChange(cavo.id_cavo, !datiPosa[cavo.id_cavo]?.force_over)}\n                              startIcon={<WarningIcon />}\n                            >\n                              Forza Over\n                            </Button>\n                          </Box>\n                        )}\n                      </Box>\n                    </TableCell>\n                    <TableCell>\n                      {cavo.stato_installazione === 'Installato' ? (\n                        <Chip \n                          icon={<CheckCircleIcon />}\n                          label=\"Installato\" \n                          color=\"success\" \n                          size=\"small\" \n                        />\n                      ) : (\n                        <Chip \n                          icon={<WarningIcon />}\n                          label=\"Da Installare\" \n                          color=\"warning\" \n                          size=\"small\" \n                        />\n                      )}\n                    </TableCell>\n                    <TableCell>\n                      <TextField\n                        size=\"small\"\n                        placeholder=\"Note opzionali...\"\n                        value={datiPosa[cavo.id_cavo]?.note || ''}\n                        onChange={(e) => handleNoteChange(cavo.id_cavo, e.target.value)}\n                        sx={{ width: 150 }}\n                      />\n                    </TableCell>\n                  </TableRow>\n                ))}\n              </TableBody>\n            </Table>\n          </TableContainer>\n        )}\n\n        {cavi.length === 0 && !loading && (\n          <Alert severity=\"info\" sx={{ mt: 2 }}>\n            Nessun cavo assegnato a questa comanda.\n          </Alert>\n        )}\n      </DialogContent>\n\n      <DialogActions sx={{ p: 2, gap: 1 }}>\n        <Button\n          onClick={onClose}\n          startIcon={<CancelIcon />}\n          disabled={loading}\n        >\n          Annulla\n        </Button>\n        <Button\n          onClick={handleSave}\n          variant=\"contained\"\n          startIcon={<SaveIcon />}\n          disabled={loading || cavi.length === 0}\n        >\n          Salva Dati Posa\n        </Button>\n      </DialogActions>\n    </Dialog>\n  );\n};\n\nexport default InserimentoMetriDialog;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAClD,SACEC,MAAM,EACNC,WAAW,EACXC,aAAa,EACbC,aAAa,EACbC,MAAM,EACNC,SAAS,EACTC,IAAI,EACJC,UAAU,EACVC,GAAG,EACHC,KAAK,EACLC,IAAI,EACJC,KAAK,EACLC,SAAS,EACTC,SAAS,EACTC,cAAc,EACdC,SAAS,EACTC,QAAQ,EACRC,KAAK,EACLC,UAAU,EACVC,OAAO,EACPC,YAAY,EACZC,WAAW,EACXC,UAAU,EACVC,MAAM,EACNC,QAAQ,EACRC,YAAY,EACZC,YAAY,QACP,eAAe;AACtB,SACEC,IAAI,IAAIC,QAAQ,EAChBC,MAAM,IAAIC,UAAU,EACpBC,KAAK,IAAIC,SAAS,EAClBC,WAAW,IAAIC,eAAe,EAC9BC,OAAO,IAAIC,WAAW,EACtBC,OAAO,IAAIC,UAAU,EACrBC,KAAK,IAAIC,SAAS,EAClBC,IAAI,IAAIC,QAAQ,QACX,qBAAqB;AAC5B,OAAOC,cAAc,MAAM,+BAA+B;AAC1D,OAAOC,WAAW,MAAM,4BAA4B;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAErD,MAAMC,sBAAsB,GAAGA,CAAC;EAC9BC,IAAI;EACJC,OAAO;EACPC,OAAO;EACPC;AACF,CAAC,KAAK;EAAAC,EAAA;EACJ,MAAM,CAACC,OAAO,EAAEC,UAAU,CAAC,GAAGxD,QAAQ,CAAC,KAAK,CAAC;EAC7C,MAAM,CAACyD,KAAK,EAAEC,QAAQ,CAAC,GAAG1D,QAAQ,CAAC,IAAI,CAAC;EACxC,MAAM,CAAC2D,IAAI,EAAEC,OAAO,CAAC,GAAG5D,QAAQ,CAAC,EAAE,CAAC;EACpC,MAAM,CAAC6D,QAAQ,EAAEC,WAAW,CAAC,GAAG9D,QAAQ,CAAC,CAAC,CAAC,CAAC;EAC5C,MAAM,CAAC+D,gBAAgB,EAAEC,mBAAmB,CAAC,GAAGhE,QAAQ,CAAC,CAAC,CAAC,CAAC;EAC5D,MAAM,CAACiE,iBAAiB,EAAEC,oBAAoB,CAAC,GAAGlE,QAAQ,CAAC,EAAE,CAAC;EAC9D,MAAM,CAACmE,aAAa,EAAEC,gBAAgB,CAAC,GAAGpE,QAAQ,CAAC,KAAK,CAAC;EAEzDC,SAAS,CAAC,MAAM;IACd,IAAIiD,IAAI,IAAIE,OAAO,EAAE;MACnBiB,eAAe,CAAC,CAAC;MACjBC,qBAAqB,CAAC,CAAC;IACzB;EACF,CAAC,EAAE,CAACpB,IAAI,EAAEE,OAAO,CAAC,CAAC;EAEnB,MAAMiB,eAAe,GAAG,MAAAA,CAAA,KAAY;IAClC,IAAI;MACFb,UAAU,CAAC,IAAI,CAAC;MAChBE,QAAQ,CAAC,IAAI,CAAC;MAEd,MAAMa,QAAQ,GAAG,MAAM1B,cAAc,CAAC2B,cAAc,CAACpB,OAAO,CAACqB,cAAc,CAAC;MAC5Eb,OAAO,CAACW,QAAQ,CAAC;;MAEjB;MACA,MAAMG,WAAW,GAAG,CAAC,CAAC;MACtBH,QAAQ,CAACI,OAAO,CAACC,IAAI,IAAI;QACvBF,WAAW,CAACE,IAAI,CAACC,OAAO,CAAC,GAAG;UAC1BC,eAAe,EAAEF,IAAI,CAACE,eAAe,IAAIF,IAAI,CAACG,iBAAiB,IAAI,CAAC;UACpEC,SAAS,EAAE,IAAIC,IAAI,CAAC,CAAC,CAACC,WAAW,CAAC,CAAC,CAACC,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC;UACjDC,iBAAiB,EAAEhC,OAAO,CAACiC,YAAY,IAAI,EAAE;UAC7CC,IAAI,EAAE,EAAE;UACRC,SAAS,EAAEX,IAAI,CAACW,SAAS,IAAI,EAAE;UAAE;UACjCC,UAAU,EAAE,KAAK,CAAC;QACpB,CAAC;MACH,CAAC,CAAC;MACF1B,WAAW,CAACY,WAAW,CAAC;IAE1B,CAAC,CAAC,OAAOe,GAAG,EAAE;MACZC,OAAO,CAACjC,KAAK,CAAC,8BAA8B,EAAEgC,GAAG,CAAC;MAClD/B,QAAQ,CAAC,+CAA+C,CAAC;IAC3D,CAAC,SAAS;MACRF,UAAU,CAAC,KAAK,CAAC;IACnB;EACF,CAAC;EAED,MAAMc,qBAAqB,GAAG,MAAAA,CAAA,KAAY;IACxC,IAAI;MACFF,gBAAgB,CAAC,IAAI,CAAC;;MAEtB;MACA,MAAMuB,UAAU,GAAG,MAAM7C,WAAW,CAAC8C,oBAAoB,CAACxC,OAAO,CAACyC,WAAW,CAAC;MAC9E3B,oBAAoB,CAACyB,UAAU,CAAC;IAElC,CAAC,CAAC,OAAOF,GAAG,EAAE;MACZC,OAAO,CAACjC,KAAK,CAAC,gCAAgC,EAAEgC,GAAG,CAAC;MACpD;IACF,CAAC,SAAS;MACRrB,gBAAgB,CAAC,KAAK,CAAC;IACzB;EACF,CAAC;EAED,MAAM0B,iBAAiB,GAAGA,CAACC,MAAM,EAAEC,KAAK,KAAK;IAC3C,MAAMC,YAAY,GAAGC,UAAU,CAACF,KAAK,CAAC,IAAI,CAAC;IAE3ClC,WAAW,CAACqC,IAAI,KAAK;MACnB,GAAGA,IAAI;MACP,CAACJ,MAAM,GAAG;QACR,GAAGI,IAAI,CAACJ,MAAM,CAAC;QACfjB,eAAe,EAAEmB;MACnB;IACF,CAAC,CAAC,CAAC;;IAEH;IACA,MAAMrB,IAAI,GAAGjB,IAAI,CAACyC,IAAI,CAACC,CAAC,IAAIA,CAAC,CAACxB,OAAO,KAAKkB,MAAM,CAAC;IACjD,MAAMO,QAAQ,GAAGzC,QAAQ,CAACkC,MAAM,CAAC;IACjC,IAAIQ,MAAM,GAAG,EAAE;IAEf,IAAI3B,IAAI,IAAIqB,YAAY,GAAGrB,IAAI,CAACG,iBAAiB,GAAG,GAAG,EAAE;MACvDwB,MAAM,CAACC,IAAI,CAAC,iDAAiD,CAAC;IAChE;;IAEA;IACA,IAAIF,QAAQ,aAARA,QAAQ,eAARA,QAAQ,CAAEf,SAAS,IAAIe,QAAQ,CAACf,SAAS,KAAK,cAAc,EAAE;MAChE,MAAMkB,MAAM,GAAGxC,iBAAiB,CAACmC,IAAI,CAACM,CAAC,IAAIA,CAAC,CAACnB,SAAS,KAAKe,QAAQ,CAACf,SAAS,CAAC;MAC9E,IAAIkB,MAAM,IAAIR,YAAY,GAAGQ,MAAM,CAACE,aAAa,IAAI,CAACL,QAAQ,CAACd,UAAU,EAAE;QACzEe,MAAM,CAACC,IAAI,CAAC,kBAAkBC,MAAM,CAACE,aAAa,wBAAwBV,YAAY,IAAI,CAAC;MAC7F;IACF;IAEA,IAAIM,MAAM,CAACK,MAAM,GAAG,CAAC,EAAE;MACrB5C,mBAAmB,CAACmC,IAAI,KAAK;QAC3B,GAAGA,IAAI;QACP,CAACJ,MAAM,GAAGQ,MAAM,CAACM,IAAI,CAAC,IAAI;MAC5B,CAAC,CAAC,CAAC;IACL,CAAC,MAAM;MACL7C,mBAAmB,CAACmC,IAAI,IAAI;QAC1B,MAAMW,SAAS,GAAG;UAAE,GAAGX;QAAK,CAAC;QAC7B,OAAOW,SAAS,CAACf,MAAM,CAAC;QACxB,OAAOe,SAAS;MAClB,CAAC,CAAC;IACJ;EACF,CAAC;EAED,MAAMC,gBAAgB,GAAGA,CAAChB,MAAM,EAAEC,KAAK,KAAK;IAC1ClC,WAAW,CAACqC,IAAI,KAAK;MACnB,GAAGA,IAAI;MACP,CAACJ,MAAM,GAAG;QACR,GAAGI,IAAI,CAACJ,MAAM,CAAC;QACfT,IAAI,EAAEU;MACR;IACF,CAAC,CAAC,CAAC;EACL,CAAC;EAED,MAAMgB,kBAAkB,GAAGA,CAACjB,MAAM,EAAEkB,QAAQ,KAAK;IAC/CnD,WAAW,CAACqC,IAAI,KAAK;MACnB,GAAGA,IAAI;MACP,CAACJ,MAAM,GAAG;QACR,GAAGI,IAAI,CAACJ,MAAM,CAAC;QACfR,SAAS,EAAE0B,QAAQ;QACnBzB,UAAU,EAAE,KAAK,CAAC;MACpB;IACF,CAAC,CAAC,CAAC;;IAEH;IACA,MAAMc,QAAQ,GAAGzC,QAAQ,CAACkC,MAAM,CAAC;IACjC,IAAIO,QAAQ,aAARA,QAAQ,eAARA,QAAQ,CAAExB,eAAe,EAAE;MAC7BgB,iBAAiB,CAACC,MAAM,EAAEO,QAAQ,CAACxB,eAAe,CAAC;IACrD;EACF,CAAC;EAED,MAAMoC,qBAAqB,GAAGA,CAACnB,MAAM,EAAEoB,SAAS,KAAK;IACnDrD,WAAW,CAACqC,IAAI,KAAK;MACnB,GAAGA,IAAI;MACP,CAACJ,MAAM,GAAG;QACR,GAAGI,IAAI,CAACJ,MAAM,CAAC;QACfP,UAAU,EAAE2B;MACd;IACF,CAAC,CAAC,CAAC;;IAEH;IACA,MAAMb,QAAQ,GAAGzC,QAAQ,CAACkC,MAAM,CAAC;IACjC,IAAIO,QAAQ,aAARA,QAAQ,eAARA,QAAQ,CAAExB,eAAe,EAAE;MAC7BgB,iBAAiB,CAACC,MAAM,EAAEO,QAAQ,CAACxB,eAAe,CAAC;IACrD;EACF,CAAC;EAED,MAAMsC,UAAU,GAAG,MAAAA,CAAA,KAAY;IAC7B,IAAI;MACF5D,UAAU,CAAC,IAAI,CAAC;MAChBE,QAAQ,CAAC,IAAI,CAAC;;MAEd;MACA,MAAM2D,SAAS,GAAGC,MAAM,CAACC,IAAI,CAACxD,gBAAgB,CAAC,CAAC6C,MAAM,GAAG,CAAC;MAC1D,IAAIS,SAAS,EAAE;QACb3D,QAAQ,CAAC,uDAAuD,CAAC;QACjE;MACF;;MAEA;MACA,MAAMb,cAAc,CAAC2E,yBAAyB,CAACpE,OAAO,CAACqB,cAAc,EAAEZ,QAAQ,CAAC;MAEhFR,SAAS,aAATA,SAAS,uBAATA,SAAS,CAAG,mCAAmC,CAAC;MAChDF,OAAO,CAAC,CAAC;IAEX,CAAC,CAAC,OAAOsC,GAAG,EAAE;MACZC,OAAO,CAACjC,KAAK,CAAC,yBAAyB,EAAEgC,GAAG,CAAC;MAC7C/B,QAAQ,CAAC+B,GAAG,CAACgC,OAAO,IAAI,iCAAiC,CAAC;IAC5D,CAAC,SAAS;MACRjE,UAAU,CAAC,KAAK,CAAC;IACnB;EACF,CAAC;EAED,MAAMkE,mBAAmB,GAAIC,IAAI,IAAK;IACpC,QAAQA,IAAI;MACV,KAAK,MAAM;QAAE,OAAO,SAAS;MAC7B,KAAK,uBAAuB;QAAE,OAAO,SAAS;MAC9C,KAAK,qBAAqB;QAAE,OAAO,MAAM;MACzC,KAAK,gBAAgB;QAAE,OAAO,SAAS;MACvC;QAAS,OAAO,SAAS;IAC3B;EACF,CAAC;EAED,MAAMC,mBAAmB,GAAID,IAAI,IAAK;IACpC,QAAQA,IAAI;MACV,KAAK,MAAM;QAAE,OAAO,MAAM;MAC1B,KAAK,uBAAuB;QAAE,OAAO,gBAAgB;MACrD,KAAK,qBAAqB;QAAE,OAAO,cAAc;MACjD,KAAK,gBAAgB;QAAE,OAAO,gBAAgB;MAC9C;QAAS,OAAOA,IAAI;IACtB;EACF,CAAC;;EAED;EACA,MAAME,oBAAoB,GAAIjD,IAAI,IAAK;IACrC,OAAOX,iBAAiB,CAAC6D,MAAM,CAACrB,MAAM,IACpCA,MAAM,CAACsB,SAAS,KAAKnD,IAAI,CAACmD,SAAS,IACnCtB,MAAM,CAACuB,OAAO,KAAKpD,IAAI,CAACoD,OAAO,IAC/BvB,MAAM,CAACE,aAAa,GAAG,CACzB,CAAC;EACH,CAAC;;EAED;EACA,MAAMsB,mBAAmB,GAAGA,CAACxB,MAAM,EAAE7B,IAAI,KAAK;IAC5C,OAAO6B,MAAM,CAACsB,SAAS,KAAKnD,IAAI,CAACmD,SAAS,IAAItB,MAAM,CAACuB,OAAO,KAAKpD,IAAI,CAACoD,OAAO;EAC/E,CAAC;;EAED;EACA,MAAME,oBAAoB,GAAGA,CAACzB,MAAM,EAAE0B,cAAc,KAAK;IACvD,IAAI,CAAC1B,MAAM,IAAIA,MAAM,CAAClB,SAAS,KAAK,cAAc,EAAE,OAAO,SAAS;IACpE,IAAI4C,cAAc,GAAG1B,MAAM,CAACE,aAAa,EAAE,OAAO,OAAO;IACzD,IAAIF,MAAM,CAACE,aAAa,GAAGF,MAAM,CAAC2B,YAAY,GAAG,GAAG,EAAE,OAAO,SAAS;IACtE,OAAO,SAAS;EAClB,CAAC;EAED,IAAI,CAAChF,OAAO,EAAE,OAAO,IAAI;EAEzB,oBACEJ,OAAA,CAAC9C,MAAM;IACLgD,IAAI,EAAEA,IAAK;IACXC,OAAO,EAAEA,OAAQ;IACjBkF,QAAQ,EAAC,IAAI;IACbC,SAAS;IACTC,UAAU,EAAE;MACVC,EAAE,EAAE;QAAEC,SAAS,EAAE;MAAO;IAC1B,CAAE;IAAAC,QAAA,gBAEF1F,OAAA,CAAC7C,WAAW;MAAAuI,QAAA,eACV1F,OAAA,CAACtC,GAAG;QAACiI,OAAO,EAAC,MAAM;QAACC,UAAU,EAAC,QAAQ;QAACC,GAAG,EAAE,CAAE;QAAAH,QAAA,gBAC7C1F,OAAA,CAACd,SAAS;UAAC4G,KAAK,EAAC;QAAS;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,eAC7BlG,OAAA,CAACtC,GAAG;UAAAgI,QAAA,gBACF1F,OAAA,CAACvC,UAAU;YAAC0I,OAAO,EAAC,IAAI;YAAAT,QAAA,EAAC;UAEzB;YAAAK,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAY,CAAC,eACblG,OAAA,CAACtC,GAAG;YAACiI,OAAO,EAAC,MAAM;YAACC,UAAU,EAAC,QAAQ;YAACC,GAAG,EAAE,CAAE;YAACO,EAAE,EAAE,CAAE;YAAAV,QAAA,gBACpD1F,OAAA,CAACvC,UAAU;cAAC0I,OAAO,EAAC,OAAO;cAACL,KAAK,EAAC,eAAe;cAAAJ,QAAA,GAAC,WACvC,EAACtF,OAAO,CAACqB,cAAc;YAAA;cAAAsE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACtB,CAAC,eACblG,OAAA,CAACpC,IAAI;cACHyI,KAAK,EAAEzB,mBAAmB,CAACxE,OAAO,CAACkG,YAAY,CAAE;cACjDR,KAAK,EAAEpB,mBAAmB,CAACtE,OAAO,CAACkG,YAAY,CAAE;cACjDC,IAAI,EAAC;YAAO;cAAAR,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACb,CAAC,eACFlG,OAAA,CAACvC,UAAU;cAAC0I,OAAO,EAAC,OAAO;cAACL,KAAK,EAAC,eAAe;cAAAJ,QAAA,GAAC,gBAClC,EAACtF,OAAO,CAACiC,YAAY;YAAA;cAAA0D,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACzB,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACV,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACK,CAAC,eAEdlG,OAAA,CAAC5C,aAAa;MAAAsI,QAAA,GACXjF,KAAK,iBACJT,OAAA,CAACrC,KAAK;QAAC6I,QAAQ,EAAC,OAAO;QAAChB,EAAE,EAAE;UAAEiB,EAAE,EAAE;QAAE,CAAE;QAAAf,QAAA,EACnCjF;MAAK;QAAAsF,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACD,CACR,EAEA3F,OAAO,gBACNP,OAAA,CAACtC,GAAG;QAACiI,OAAO,EAAC,MAAM;QAACe,cAAc,EAAC,QAAQ;QAACC,CAAC,EAAE,CAAE;QAAAjB,QAAA,eAC/C1F,OAAA,CAACvC,UAAU;UAAAiI,QAAA,EAAC;QAAmB;UAAAK,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAY;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACzC,CAAC,gBAENlG,OAAA,CAAChC,cAAc;QAAC4I,SAAS,EAAEzI,KAAM;QAACgI,OAAO,EAAC,UAAU;QAAAT,QAAA,eAClD1F,OAAA,CAACnC,KAAK;UAAC0I,IAAI,EAAC,OAAO;UAAAb,QAAA,gBACjB1F,OAAA,CAAC/B,SAAS;YAAAyH,QAAA,eACR1F,OAAA,CAAC9B,QAAQ;cAAAwH,QAAA,gBACP1F,OAAA,CAACjC,SAAS;gBAAA2H,QAAA,EAAC;cAAO;gBAAAK,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAW,CAAC,eAC9BlG,OAAA,CAACjC,SAAS;gBAAA2H,QAAA,EAAC;cAAS;gBAAAK,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAW,CAAC,eAChClG,OAAA,CAACjC,SAAS;gBAAA2H,QAAA,EAAC;cAAU;gBAAAK,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAW,CAAC,eACjClG,OAAA,CAACjC,SAAS;gBAAA2H,QAAA,EAAC;cAAa;gBAAAK,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAW,CAAC,eACpClG,OAAA,CAACjC,SAAS;gBAAA2H,QAAA,EAAC;cAAW;gBAAAK,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAW,CAAC,eAClClG,OAAA,CAACjC,SAAS;gBAAA2H,QAAA,EAAC;cAAM;gBAAAK,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAW,CAAC,eAC7BlG,OAAA,CAACjC,SAAS;gBAAA2H,QAAA,EAAC;cAAK;gBAAAK,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAW,CAAC,eAC5BlG,OAAA,CAACjC,SAAS;gBAAA2H,QAAA,EAAC;cAAI;gBAAAK,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAW,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACnB;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACF,CAAC,eACZlG,OAAA,CAAClC,SAAS;YAAA4H,QAAA,EACP/E,IAAI,CAACkG,GAAG,CAAEjF,IAAI;cAAA,IAAAkF,qBAAA,EAAAC,qBAAA,EAAAC,sBAAA,EAAAC,sBAAA,EAAAC,sBAAA,EAAAC,sBAAA,EAAAC,sBAAA;cAAA,oBACbpH,OAAA,CAAC9B,QAAQ;gBAAAwH,QAAA,gBACP1F,OAAA,CAACjC,SAAS;kBAAA2H,QAAA,eACR1F,OAAA,CAACvC,UAAU;oBAAC0I,OAAO,EAAC,OAAO;oBAACkB,UAAU,EAAC,MAAM;oBAAA3B,QAAA,EAC1C9D,IAAI,CAACC;kBAAO;oBAAAkE,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACH;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACJ,CAAC,eACZlG,OAAA,CAACjC,SAAS;kBAAA2H,QAAA,EAAE9D,IAAI,CAACmD,SAAS,IAAI;gBAAK;kBAAAgB,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAY,CAAC,eAChDlG,OAAA,CAACjC,SAAS;kBAAA2H,QAAA,EAAE9D,IAAI,CAAC0F,UAAU,IAAI;gBAAK;kBAAAvB,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAY,CAAC,eACjDlG,OAAA,CAACjC,SAAS;kBAAA2H,QAAA,eACR1F,OAAA,CAACvC,UAAU;oBAAC0I,OAAO,EAAC,OAAO;oBAAAT,QAAA,GACxB,EAAAoB,qBAAA,GAAAlF,IAAI,CAACG,iBAAiB,cAAA+E,qBAAA,uBAAtBA,qBAAA,CAAwBS,OAAO,CAAC,CAAC,CAAC,KAAI,KAAK,EAAC,IAC/C;kBAAA;oBAAAxB,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAY;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACJ,CAAC,eACZlG,OAAA,CAACjC,SAAS;kBAAA2H,QAAA,eACR1F,OAAA,CAACzC,SAAS;oBACRiK,IAAI,EAAC,QAAQ;oBACbjB,IAAI,EAAC,OAAO;oBACZvD,KAAK,EAAE,EAAA+D,qBAAA,GAAAlG,QAAQ,CAACe,IAAI,CAACC,OAAO,CAAC,cAAAkF,qBAAA,uBAAtBA,qBAAA,CAAwBjF,eAAe,KAAI,CAAE;oBACpD2F,QAAQ,EAAGC,CAAC,IAAK5E,iBAAiB,CAAClB,IAAI,CAACC,OAAO,EAAE6F,CAAC,CAACC,MAAM,CAAC3E,KAAK,CAAE;oBACjEvC,KAAK,EAAE,CAAC,CAACM,gBAAgB,CAACa,IAAI,CAACC,OAAO,CAAE;oBACxC+F,UAAU,EAAE7G,gBAAgB,CAACa,IAAI,CAACC,OAAO,CAAE;oBAC3CgG,UAAU,EAAE;sBACVC,GAAG,EAAE,CAAC;sBACNC,IAAI,EAAE,GAAG;sBACTC,KAAK,EAAE;wBAAEC,SAAS,EAAE;sBAAQ;oBAC9B,CAAE;oBACFzC,EAAE,EAAE;sBAAE0C,KAAK,EAAE;oBAAI;kBAAE;oBAAAnC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACpB;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACO,CAAC,eACZlG,OAAA,CAACjC,SAAS;kBAAA2H,QAAA,eACR1F,OAAA,CAACtC,GAAG;oBAAC8H,EAAE,EAAE;sBAAE2C,QAAQ,EAAE;oBAAI,CAAE;oBAAAzC,QAAA,gBACzB1F,OAAA,CAAC1B,YAAY;sBACXiI,IAAI,EAAC,OAAO;sBACZvD,KAAK,EACH,EAAAgE,sBAAA,GAAAnG,QAAQ,CAACe,IAAI,CAACC,OAAO,CAAC,cAAAmF,sBAAA,uBAAtBA,sBAAA,CAAwBzE,SAAS,MAAK,cAAc,GAChD;wBAAEA,SAAS,EAAE,cAAc;wBAAEwC,SAAS,EAAE,OAAO;wBAAEpB,aAAa,EAAE;sBAAI,CAAC,GACrE1C,iBAAiB,CAACmC,IAAI,CAACM,CAAC;wBAAA,IAAA0E,sBAAA;wBAAA,OAAI1E,CAAC,CAACnB,SAAS,OAAA6F,sBAAA,GAAKvH,QAAQ,CAACe,IAAI,CAACC,OAAO,CAAC,cAAAuG,sBAAA,uBAAtBA,sBAAA,CAAwB7F,SAAS;sBAAA,EAAC,IAAI,IACvF;sBACDkF,QAAQ,EAAEA,CAACY,KAAK,EAAEC,QAAQ,KAAK;wBAC7BtE,kBAAkB,CAACpC,IAAI,CAACC,OAAO,EAAE,CAAAyG,QAAQ,aAARA,QAAQ,uBAARA,QAAQ,CAAE/F,SAAS,KAAI,EAAE,CAAC;sBAC7D,CAAE;sBACFgG,OAAO,EAAE,CACP;wBAAEhG,SAAS,EAAE,cAAc;wBAAEwC,SAAS,EAAE,OAAO;wBAAEpB,aAAa,EAAE;sBAAI,CAAC,EACrE,GAAGkB,oBAAoB,CAACjD,IAAI,CAAC,CAC7B;sBACF4G,cAAc,EAAGC,MAAM,IAAK;wBAC1B,IAAIA,MAAM,CAAClG,SAAS,KAAK,cAAc,EAAE,OAAO,cAAc;wBAC9D,OAAO,GAAGkG,MAAM,CAAClG,SAAS,KAAKkG,MAAM,CAAC9E,aAAa,IAAI;sBACzD,CAAE;sBACF+E,YAAY,EAAEA,CAACC,KAAK,EAAEF,MAAM;wBAAA,IAAAG,sBAAA;wBAAA,oBAC1B5I,OAAA,CAACtC,GAAG;0BAACkJ,SAAS,EAAC,IAAI;0BAAA,GAAK+B,KAAK;0BAAAjD,QAAA,gBAC3B1F,OAAA,CAACpB,YAAY;4BAAA8G,QAAA,eACX1F,OAAA,CAACR,UAAU;8BACTqJ,QAAQ,EAAC,OAAO;8BAChB/C,KAAK,EACH2C,MAAM,CAAClG,SAAS,KAAK,cAAc,GAC/B,SAAS,GACT2C,oBAAoB,CAACuD,MAAM,EAAE,EAAAG,sBAAA,GAAA/H,QAAQ,CAACe,IAAI,CAACC,OAAO,CAAC,cAAA+G,sBAAA,uBAAtBA,sBAAA,CAAwB9G,eAAe,KAAI,CAAC;4BAC9E;8BAAAiE,QAAA,EAAAC,YAAA;8BAAAC,UAAA;8BAAAC,YAAA;4BAAA,OACF;0BAAC;4BAAAH,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OACU,CAAC,eACflG,OAAA,CAACrB,YAAY;4BACXmK,OAAO,EAAEL,MAAM,CAAClG,SAAS,KAAK,cAAc,GAAG,cAAc,GAAGkG,MAAM,CAAClG,SAAU;4BACjFwG,SAAS,EACPN,MAAM,CAAClG,SAAS,KAAK,cAAc,GAC/B,0BAA0B,GAC1B,GAAGkG,MAAM,CAAC1D,SAAS,IAAI0D,MAAM,CAACzD,OAAO,MAAMyD,MAAM,CAAC9E,aAAa;0BACpE;4BAAAoC,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OACF,CAAC;wBAAA;0BAAAH,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OACC,CAAC;sBAAA,CACN;sBACF3F,OAAO,EAAEY,aAAc;sBACvB6H,QAAQ,EAAE7H,aAAc;sBACxBqE,EAAE,EAAE;wBAAE0C,KAAK,EAAE;sBAAO;oBAAE;sBAAAnC,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACvB,CAAC,EAED,EAAAe,sBAAA,GAAApG,QAAQ,CAACe,IAAI,CAACC,OAAO,CAAC,cAAAoF,sBAAA,uBAAtBA,sBAAA,CAAwB1E,SAAS,KACjC,EAAA2E,sBAAA,GAAArG,QAAQ,CAACe,IAAI,CAACC,OAAO,CAAC,cAAAqF,sBAAA,uBAAtBA,sBAAA,CAAwB3E,SAAS,MAAK,cAAc,IACpD,CAAC0G,sBAAA,IAAM;sBACL,MAAMxF,MAAM,GAAGxC,iBAAiB,CAACmC,IAAI,CAACM,CAAC;wBAAA,IAAAwF,sBAAA;wBAAA,OAAIxF,CAAC,CAACnB,SAAS,OAAA2G,sBAAA,GAAKrI,QAAQ,CAACe,IAAI,CAACC,OAAO,CAAC,cAAAqH,sBAAA,uBAAtBA,sBAAA,CAAwB3G,SAAS;sBAAA,EAAC;sBAC7F,MAAM4C,cAAc,GAAG,EAAA8D,sBAAA,GAAApI,QAAQ,CAACe,IAAI,CAACC,OAAO,CAAC,cAAAoH,sBAAA,uBAAtBA,sBAAA,CAAwBnH,eAAe,KAAI,CAAC;sBACnE,OAAO2B,MAAM,IAAI0B,cAAc,GAAG1B,MAAM,CAACE,aAAa;oBACxD,CAAC,EAAE,CAAC,iBACH3D,OAAA,CAACtC,GAAG;sBAAC8H,EAAE,EAAE;wBAAEY,EAAE,EAAE;sBAAE,CAAE;sBAAAV,QAAA,eACjB1F,OAAA,CAAC1C,MAAM;wBACLiJ,IAAI,EAAC,OAAO;wBACZJ,OAAO,EAAE,CAAAgB,sBAAA,GAAAtG,QAAQ,CAACe,IAAI,CAACC,OAAO,CAAC,cAAAsF,sBAAA,eAAtBA,sBAAA,CAAwB3E,UAAU,GAAG,WAAW,GAAG,UAAW;wBACvEsD,KAAK,EAAC,SAAS;wBACfqD,OAAO,EAAEA,CAAA;0BAAA,IAAAC,sBAAA;0BAAA,OAAMlF,qBAAqB,CAACtC,IAAI,CAACC,OAAO,EAAE,GAAAuH,sBAAA,GAACvI,QAAQ,CAACe,IAAI,CAACC,OAAO,CAAC,cAAAuH,sBAAA,eAAtBA,sBAAA,CAAwB5G,UAAU,EAAC;wBAAA,CAAC;wBACxF6G,SAAS,eAAErJ,OAAA,CAACV,WAAW;0BAAAyG,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAE,CAAE;wBAAAR,QAAA,EAC5B;sBAED;wBAAAK,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAQ;oBAAC;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACN,CACN;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACE;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACG,CAAC,eACZlG,OAAA,CAACjC,SAAS;kBAAA2H,QAAA,EACP9D,IAAI,CAAC0H,mBAAmB,KAAK,YAAY,gBACxCtJ,OAAA,CAACpC,IAAI;oBACH2L,IAAI,eAAEvJ,OAAA,CAACZ,eAAe;sBAAA2G,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAE,CAAE;oBAC1BG,KAAK,EAAC,YAAY;oBAClBP,KAAK,EAAC,SAAS;oBACfS,IAAI,EAAC;kBAAO;oBAAAR,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACb,CAAC,gBAEFlG,OAAA,CAACpC,IAAI;oBACH2L,IAAI,eAAEvJ,OAAA,CAACV,WAAW;sBAAAyG,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAE,CAAE;oBACtBG,KAAK,EAAC,eAAe;oBACrBP,KAAK,EAAC,SAAS;oBACfS,IAAI,EAAC;kBAAO;oBAAAR,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACb;gBACF;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACQ,CAAC,eACZlG,OAAA,CAACjC,SAAS;kBAAA2H,QAAA,eACR1F,OAAA,CAACzC,SAAS;oBACRgJ,IAAI,EAAC,OAAO;oBACZiD,WAAW,EAAC,mBAAmB;oBAC/BxG,KAAK,EAAE,EAAAoE,sBAAA,GAAAvG,QAAQ,CAACe,IAAI,CAACC,OAAO,CAAC,cAAAuF,sBAAA,uBAAtBA,sBAAA,CAAwB9E,IAAI,KAAI,EAAG;oBAC1CmF,QAAQ,EAAGC,CAAC,IAAK3D,gBAAgB,CAACnC,IAAI,CAACC,OAAO,EAAE6F,CAAC,CAACC,MAAM,CAAC3E,KAAK,CAAE;oBAChEwC,EAAE,EAAE;sBAAE0C,KAAK,EAAE;oBAAI;kBAAE;oBAAAnC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACpB;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACO,CAAC;cAAA,GA1HCtE,IAAI,CAACC,OAAO;gBAAAkE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OA2HjB,CAAC;YAAA,CACZ;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACO,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACP;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACM,CACjB,EAEAvF,IAAI,CAACiD,MAAM,KAAK,CAAC,IAAI,CAACrD,OAAO,iBAC5BP,OAAA,CAACrC,KAAK;QAAC6I,QAAQ,EAAC,MAAM;QAAChB,EAAE,EAAE;UAAEY,EAAE,EAAE;QAAE,CAAE;QAAAV,QAAA,EAAC;MAEtC;QAAAK,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAO,CACR;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACY,CAAC,eAEhBlG,OAAA,CAAC3C,aAAa;MAACmI,EAAE,EAAE;QAAEmB,CAAC,EAAE,CAAC;QAAEd,GAAG,EAAE;MAAE,CAAE;MAAAH,QAAA,gBAClC1F,OAAA,CAAC1C,MAAM;QACL6L,OAAO,EAAEhJ,OAAQ;QACjBkJ,SAAS,eAAErJ,OAAA,CAAChB,UAAU;UAAA+G,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAE;QAC1B8C,QAAQ,EAAEzI,OAAQ;QAAAmF,QAAA,EACnB;MAED;QAAAK,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAQ,CAAC,eACTlG,OAAA,CAAC1C,MAAM;QACL6L,OAAO,EAAE/E,UAAW;QACpB+B,OAAO,EAAC,WAAW;QACnBkD,SAAS,eAAErJ,OAAA,CAAClB,QAAQ;UAAAiH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAE;QACxB8C,QAAQ,EAAEzI,OAAO,IAAII,IAAI,CAACiD,MAAM,KAAK,CAAE;QAAA8B,QAAA,EACxC;MAED;QAAAK,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAQ,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACI,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACV,CAAC;AAEb,CAAC;AAAC5F,EAAA,CAxbIL,sBAAsB;AAAAwJ,EAAA,GAAtBxJ,sBAAsB;AA0b5B,eAAeA,sBAAsB;AAAC,IAAAwJ,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}