{"ast": null, "code": "// Servizio API unificato che espone tutti i servizi\nimport authService from './authService';\nimport cantieriService from './cantieriService';\nimport caviService from './caviService';\nimport certificazioneService from './certificazioneService';\nimport parcoCaviService from './parcoCaviService';\nimport excelService from './excelService';\nimport reportService from './reportService';\nexport const apiService = {\n  // Auth\n  login: authService.login,\n  logout: authService.logout,\n  getCurrentUser: authService.getCurrentUser,\n  // Cantieri\n  getCantieri: cantieriService.getMyCantieri,\n  getCantiere: cantieriService.getCantiere,\n  createCantiere: cantieriService.createCantiere,\n  deleteCantiere: cantieriService.deleteCantiere,\n  // Cavi\n  getCavi: caviService.getCavi,\n  getCavo: caviService.getCavo,\n  createCavo: caviService.createCavo,\n  updateCavo: caviService.updateCavo,\n  deleteCavo: caviService.deleteCavo,\n  aggiornaCavo: caviService.aggiornaCavo,\n  // Certificazioni\n  getCertificazioni: certificazioneService.getCertificazioni,\n  getCertificazione: certificazioneService.getCertificazione,\n  createCertificazione: certificazioneService.createCertificazione,\n  updateCertificazione: certificazioneService.updateCertificazione,\n  deleteCertificazione: certificazioneService.deleteCertificazione,\n  // Strumenti\n  getStrumenti: certificazioneService.getStrumenti,\n  createStrumento: certificazioneService.createStrumento,\n  updateStrumento: certificazioneService.updateStrumento,\n  deleteStrumento: certificazioneService.deleteStrumento,\n  // Parco Cavi\n  getParcoCavi: parcoCaviService.getParcoCavi,\n  createBobina: parcoCaviService.createBobina,\n  updateBobina: parcoCaviService.updateBobina,\n  deleteBobina: parcoCaviService.deleteBobina,\n  // Excel\n  generateTemplate: excelService.generateTemplate,\n  importExcel: excelService.importExcel,\n  // Reports\n  getReports: reportService.getReports\n};\nexport default apiService;", "map": {"version": 3, "names": ["authService", "cantieriService", "caviService", "certificazioneService", "parcoCaviService", "excelService", "reportService", "apiService", "login", "logout", "getCurrentUser", "getCantieri", "getMyCantieri", "getCantiere", "createCantiere", "deleteCantiere", "get<PERSON><PERSON>", "getCavo", "createCavo", "updateCavo", "deleteCavo", "aggiornaCavo", "getCertificazioni", "getCertificazione", "createCertificazione", "updateCertificazione", "deleteCertificazione", "getStrumenti", "createStrumento", "updateStrumento", "deleteStrumento", "getParcoCavi", "createBobina", "updateBobina", "deleteBobina", "generateTemplate", "importExcel", "getReports"], "sources": ["C:/CMS/webapp/frontend/src/services/apiService.js"], "sourcesContent": ["// Servizio API unificato che espone tutti i servizi\nimport authService from './authService';\nimport cantieriService from './cantieriService';\nimport caviService from './caviService';\nimport certificazioneService from './certificazioneService';\nimport parcoCaviService from './parcoCaviService';\nimport excelService from './excelService';\nimport reportService from './reportService';\n\nexport const apiService = {\n  // Auth\n  login: authService.login,\n  logout: authService.logout,\n  getCurrentUser: authService.getCurrentUser,\n\n  // Cantieri\n  getCantieri: cantieriService.getMyCantieri,\n  getCantiere: cantieriService.getCantiere,\n  createCantiere: cantieriService.createCantiere,\n  deleteCantiere: cantieriService.deleteCantiere,\n\n  // Cavi\n  getCavi: caviService.getCavi,\n  getCavo: caviService.getCavo,\n  createCavo: caviService.createCavo,\n  updateCavo: caviService.updateCavo,\n  deleteCavo: caviService.deleteCavo,\n  aggiornaCavo: caviService.aggiornaCavo,\n\n  // Certificazioni\n  getCertificazioni: certificazioneService.getCertificazioni,\n  getCertificazione: certificazioneService.getCertificazione,\n  createCertificazione: certificazioneService.createCertificazione,\n  updateCertificazione: certificazioneService.updateCertificazione,\n  deleteCertificazione: certificazioneService.deleteCertificazione,\n\n  // Strumenti\n  getStrumenti: certificazioneService.getStrumenti,\n  createStrumento: certificazioneService.createStrumento,\n  updateStrumento: certificazioneService.updateStrumento,\n  deleteStrumento: certificazioneService.deleteStrumento,\n\n  // Parco Cavi\n  getParcoCavi: parcoCaviService.getParcoCavi,\n  createBobina: parcoCaviService.createBobina,\n  updateBobina: parcoCaviService.updateBobina,\n  deleteBobina: parcoCaviService.deleteBobina,\n\n  // Excel\n  generateTemplate: excelService.generateTemplate,\n  importExcel: excelService.importExcel,\n\n  // Reports\n  getReports: reportService.getReports\n};\n\nexport default apiService;\n"], "mappings": "AAAA;AACA,OAAOA,WAAW,MAAM,eAAe;AACvC,OAAOC,eAAe,MAAM,mBAAmB;AAC/C,OAAOC,WAAW,MAAM,eAAe;AACvC,OAAOC,qBAAqB,MAAM,yBAAyB;AAC3D,OAAOC,gBAAgB,MAAM,oBAAoB;AACjD,OAAOC,YAAY,MAAM,gBAAgB;AACzC,OAAOC,aAAa,MAAM,iBAAiB;AAE3C,OAAO,MAAMC,UAAU,GAAG;EACxB;EACAC,KAAK,EAAER,WAAW,CAACQ,KAAK;EACxBC,MAAM,EAAET,WAAW,CAACS,MAAM;EAC1BC,cAAc,EAAEV,WAAW,CAACU,cAAc;EAE1C;EACAC,WAAW,EAAEV,eAAe,CAACW,aAAa;EAC1CC,WAAW,EAAEZ,eAAe,CAACY,WAAW;EACxCC,cAAc,EAAEb,eAAe,CAACa,cAAc;EAC9CC,cAAc,EAAEd,eAAe,CAACc,cAAc;EAE9C;EACAC,OAAO,EAAEd,WAAW,CAACc,OAAO;EAC5BC,OAAO,EAAEf,WAAW,CAACe,OAAO;EAC5BC,UAAU,EAAEhB,WAAW,CAACgB,UAAU;EAClCC,UAAU,EAAEjB,WAAW,CAACiB,UAAU;EAClCC,UAAU,EAAElB,WAAW,CAACkB,UAAU;EAClCC,YAAY,EAAEnB,WAAW,CAACmB,YAAY;EAEtC;EACAC,iBAAiB,EAAEnB,qBAAqB,CAACmB,iBAAiB;EAC1DC,iBAAiB,EAAEpB,qBAAqB,CAACoB,iBAAiB;EAC1DC,oBAAoB,EAAErB,qBAAqB,CAACqB,oBAAoB;EAChEC,oBAAoB,EAAEtB,qBAAqB,CAACsB,oBAAoB;EAChEC,oBAAoB,EAAEvB,qBAAqB,CAACuB,oBAAoB;EAEhE;EACAC,YAAY,EAAExB,qBAAqB,CAACwB,YAAY;EAChDC,eAAe,EAAEzB,qBAAqB,CAACyB,eAAe;EACtDC,eAAe,EAAE1B,qBAAqB,CAAC0B,eAAe;EACtDC,eAAe,EAAE3B,qBAAqB,CAAC2B,eAAe;EAEtD;EACAC,YAAY,EAAE3B,gBAAgB,CAAC2B,YAAY;EAC3CC,YAAY,EAAE5B,gBAAgB,CAAC4B,YAAY;EAC3CC,YAAY,EAAE7B,gBAAgB,CAAC6B,YAAY;EAC3CC,YAAY,EAAE9B,gBAAgB,CAAC8B,YAAY;EAE3C;EACAC,gBAAgB,EAAE9B,YAAY,CAAC8B,gBAAgB;EAC/CC,WAAW,EAAE/B,YAAY,CAAC+B,WAAW;EAErC;EACAC,UAAU,EAAE/B,aAAa,CAAC+B;AAC5B,CAAC;AAED,eAAe9B,UAAU", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}