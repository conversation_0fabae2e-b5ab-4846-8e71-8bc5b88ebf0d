{"ast": null, "code": "/**\n * @name startOfYesterday\n * @category Day Helpers\n * @summary Return the start of yesterday.\n * @pure false\n *\n * @description\n * Return the start of yesterday.\n *\n * @returns The start of yesterday\n *\n * @example\n * // If today is 6 October 2014:\n * const result = startOfYesterday()\n * //=> Sun Oct 5 2014 00:00:00\n */\nexport function startOfYesterday() {\n  const now = new Date();\n  const year = now.getFullYear();\n  const month = now.getMonth();\n  const day = now.getDate();\n  const date = new Date(0);\n  date.setFullYear(year, month, day - 1);\n  date.setHours(0, 0, 0, 0);\n  return date;\n}\n\n// Fallback for modularized imports:\nexport default startOfYesterday;", "map": {"version": 3, "names": ["startOfYesterday", "now", "Date", "year", "getFullYear", "month", "getMonth", "day", "getDate", "date", "setFullYear", "setHours"], "sources": ["C:/CMS/webapp/frontend/node_modules/date-fns/startOfYesterday.mjs"], "sourcesContent": ["/**\n * @name startOfYesterday\n * @category Day Helpers\n * @summary Return the start of yesterday.\n * @pure false\n *\n * @description\n * Return the start of yesterday.\n *\n * @returns The start of yesterday\n *\n * @example\n * // If today is 6 October 2014:\n * const result = startOfYesterday()\n * //=> Sun Oct 5 2014 00:00:00\n */\nexport function startOfYesterday() {\n  const now = new Date();\n  const year = now.getFullYear();\n  const month = now.getMonth();\n  const day = now.getDate();\n\n  const date = new Date(0);\n  date.setFullYear(year, month, day - 1);\n  date.setHours(0, 0, 0, 0);\n  return date;\n}\n\n// Fallback for modularized imports:\nexport default startOfYesterday;\n"], "mappings": "AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO,SAASA,gBAAgBA,CAAA,EAAG;EACjC,MAAMC,GAAG,GAAG,IAAIC,IAAI,CAAC,CAAC;EACtB,MAAMC,IAAI,GAAGF,GAAG,CAACG,WAAW,CAAC,CAAC;EAC9B,MAAMC,KAAK,GAAGJ,GAAG,CAACK,QAAQ,CAAC,CAAC;EAC5B,MAAMC,GAAG,GAAGN,GAAG,CAACO,OAAO,CAAC,CAAC;EAEzB,MAAMC,IAAI,GAAG,IAAIP,IAAI,CAAC,CAAC,CAAC;EACxBO,IAAI,CAACC,WAAW,CAACP,IAAI,EAAEE,KAAK,EAAEE,GAAG,GAAG,CAAC,CAAC;EACtCE,IAAI,CAACE,QAAQ,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC;EACzB,OAAOF,IAAI;AACb;;AAEA;AACA,eAAeT,gBAAgB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}