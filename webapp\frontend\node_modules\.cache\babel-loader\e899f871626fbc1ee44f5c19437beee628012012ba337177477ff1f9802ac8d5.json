{"ast": null, "code": "import React,{useState}from'react';import{Box,Typography,IconButton,Alert,Snackbar}from'@mui/material';import{ArrowBack as ArrowBackIcon,Refresh as RefreshIcon}from'@mui/icons-material';import{useNavigate}from'react-router-dom';import{useAuth}from'../../../context/AuthContext';import AdminHomeButton from'../../../components/common/AdminHomeButton';import SelezionaCavoForm from'../../../components/cavi/SelezionaCavoForm';import{jsx as _jsx,jsxs as _jsxs}from\"react/jsx-runtime\";const ModificaCavoPage=()=>{const navigate=useNavigate();const{isImpersonating}=useAuth();const[alertMessage,setAlertMessage]=useState(null);const[alertSeverity,setAlertSeverity]=useState('success');const[openSnackbar,setOpenSnackbar]=useState(false);// Recupera l'ID del cantiere selezionato dal localStorage\nconst cantiereId=localStorage.getItem('selectedCantiereId');const cantiereName=localStorage.getItem('selectedCantiereName');// Gestisce il ritorno alla pagina dei cantieri\nconst handleBackToCantieri=()=>{navigate('/dashboard/cantieri');};// Gestisce il ritorno al menu admin (per admin che impersonano utenti)\nconst handleBackToAdmin=()=>{navigate('/dashboard/admin');};// Gestisce il successo di un'operazione\nconst handleSuccess=message=>{setAlertMessage(message);setAlertSeverity('success');setOpenSnackbar(true);};// Gestisce l'errore di un'operazione\nconst handleError=message=>{setAlertMessage(message);setAlertSeverity('error');setOpenSnackbar(true);};// Chiude lo snackbar\nconst handleCloseSnackbar=()=>{setOpenSnackbar(false);};// Se non c'è un cantiere selezionato, reindirizza alla pagina dei cantieri\nif(!cantiereId){navigate('/dashboard/cantieri');return null;}return/*#__PURE__*/_jsxs(Box,{children:[/*#__PURE__*/_jsxs(Box,{sx:{mb:3,display:'flex',alignItems:'center',justifyContent:'space-between'},children:[/*#__PURE__*/_jsxs(Box,{sx:{display:'flex',alignItems:'center'},children:[/*#__PURE__*/_jsx(IconButton,{onClick:handleBackToCantieri,sx:{mr:1},children:/*#__PURE__*/_jsx(ArrowBackIcon,{})}),/*#__PURE__*/_jsx(Typography,{variant:\"h6\",children:\"Modifica Cavo\"}),/*#__PURE__*/_jsx(IconButton,{onClick:()=>window.location.reload(),sx:{ml:2},color:\"primary\",title:\"Ricarica la pagina\",children:/*#__PURE__*/_jsx(RefreshIcon,{})})]}),/*#__PURE__*/_jsx(AdminHomeButton,{})]}),/*#__PURE__*/_jsx(SelezionaCavoForm,{cantiereId:cantiereId,onSuccess:handleSuccess,onError:handleError}),/*#__PURE__*/_jsx(Snackbar,{open:openSnackbar,autoHideDuration:6000,onClose:handleCloseSnackbar,anchorOrigin:{vertical:'bottom',horizontal:'center'},children:/*#__PURE__*/_jsx(Alert,{onClose:handleCloseSnackbar,severity:alertSeverity,sx:{width:'100%'},children:alertMessage})})]});};export default ModificaCavoPage;", "map": {"version": 3, "names": ["React", "useState", "Box", "Typography", "IconButton", "<PERSON><PERSON>", "Snackbar", "ArrowBack", "ArrowBackIcon", "Refresh", "RefreshIcon", "useNavigate", "useAuth", "AdminHomeButton", "SelezionaCavoForm", "jsx", "_jsx", "jsxs", "_jsxs", "ModificaCavoPage", "navigate", "isImpersonating", "alertMessage", "setAlertMessage", "alertSeverity", "setAlertSeverity", "openSnackbar", "setOpenSnackbar", "cantiereId", "localStorage", "getItem", "cantiereName", "handleBackToCantieri", "handleBackToAdmin", "handleSuccess", "message", "handleError", "handleCloseSnackbar", "children", "sx", "mb", "display", "alignItems", "justifyContent", "onClick", "mr", "variant", "window", "location", "reload", "ml", "color", "title", "onSuccess", "onError", "open", "autoHideDuration", "onClose", "anchor<PERSON><PERSON><PERSON>", "vertical", "horizontal", "severity", "width"], "sources": ["C:/Users/<USER>/PycharmProjects/CMS/webapp/frontend/src/pages/cavi/posa/ModificaCavoPage.js"], "sourcesContent": ["import React, { useState } from 'react';\nimport {\n  Box,\n  Typography,\n  IconButton,\n  Alert,\n  Snackbar\n} from '@mui/material';\nimport {\n  ArrowBack as ArrowBackIcon,\n  Refresh as RefreshIcon\n} from '@mui/icons-material';\nimport { useNavigate } from 'react-router-dom';\nimport { useAuth } from '../../../context/AuthContext';\nimport AdminHomeButton from '../../../components/common/AdminHomeButton';\nimport SelezionaCavoForm from '../../../components/cavi/SelezionaCavoForm';\n\nconst ModificaCavoPage = () => {\n  const navigate = useNavigate();\n  const { isImpersonating } = useAuth();\n  const [alertMessage, setAlertMessage] = useState(null);\n  const [alertSeverity, setAlertSeverity] = useState('success');\n  const [openSnackbar, setOpenSnackbar] = useState(false);\n\n  // Recupera l'ID del cantiere selezionato dal localStorage\n  const cantiereId = localStorage.getItem('selectedCantiereId');\n  const cantiereName = localStorage.getItem('selectedCantiereName');\n\n  // Gestisce il ritorno alla pagina dei cantieri\n  const handleBackToCantieri = () => {\n    navigate('/dashboard/cantieri');\n  };\n\n  // Gestisce il ritorno al menu admin (per admin che impersonano utenti)\n  const handleBackToAdmin = () => {\n    navigate('/dashboard/admin');\n  };\n\n  // Gestisce il successo di un'operazione\n  const handleSuccess = (message) => {\n    setAlertMessage(message);\n    setAlertSeverity('success');\n    setOpenSnackbar(true);\n  };\n\n  // Gestisce l'errore di un'operazione\n  const handleError = (message) => {\n    setAlertMessage(message);\n    setAlertSeverity('error');\n    setOpenSnackbar(true);\n  };\n\n  // Chiude lo snackbar\n  const handleCloseSnackbar = () => {\n    setOpenSnackbar(false);\n  };\n\n  // Se non c'è un cantiere selezionato, reindirizza alla pagina dei cantieri\n  if (!cantiereId) {\n    navigate('/dashboard/cantieri');\n    return null;\n  }\n\n  return (\n    <Box>\n      <Box sx={{ mb: 3, display: 'flex', alignItems: 'center', justifyContent: 'space-between' }}>\n        <Box sx={{ display: 'flex', alignItems: 'center' }}>\n          <IconButton onClick={handleBackToCantieri} sx={{ mr: 1 }}>\n            <ArrowBackIcon />\n          </IconButton>\n          <Typography variant=\"h6\">\n            Modifica Cavo\n          </Typography>\n          <IconButton\n            onClick={() => window.location.reload()}\n            sx={{ ml: 2 }}\n            color=\"primary\"\n            title=\"Ricarica la pagina\"\n          >\n            <RefreshIcon />\n          </IconButton>\n        </Box>\n        <AdminHomeButton />\n      </Box>\n\n      {/* Componente per la modifica del cavo */}\n      <SelezionaCavoForm\n        cantiereId={cantiereId}\n        onSuccess={handleSuccess}\n        onError={handleError}\n      />\n\n      <Snackbar\n        open={openSnackbar}\n        autoHideDuration={6000}\n        onClose={handleCloseSnackbar}\n        anchorOrigin={{ vertical: 'bottom', horizontal: 'center' }}\n      >\n        <Alert onClose={handleCloseSnackbar} severity={alertSeverity} sx={{ width: '100%' }}>\n          {alertMessage}\n        </Alert>\n      </Snackbar>\n    </Box>\n  );\n};\n\nexport default ModificaCavoPage;\n"], "mappings": "AAAA,MAAO,CAAAA,KAAK,EAAIC,QAAQ,KAAQ,OAAO,CACvC,OACEC,GAAG,CACHC,UAAU,CACVC,UAAU,CACVC,KAAK,CACLC,QAAQ,KACH,eAAe,CACtB,OACEC,SAAS,GAAI,CAAAC,aAAa,CAC1BC,OAAO,GAAI,CAAAC,WAAW,KACjB,qBAAqB,CAC5B,OAASC,WAAW,KAAQ,kBAAkB,CAC9C,OAASC,OAAO,KAAQ,8BAA8B,CACtD,MAAO,CAAAC,eAAe,KAAM,4CAA4C,CACxE,MAAO,CAAAC,iBAAiB,KAAM,4CAA4C,CAAC,OAAAC,GAAA,IAAAC,IAAA,CAAAC,IAAA,IAAAC,KAAA,yBAE3E,KAAM,CAAAC,gBAAgB,CAAGA,CAAA,GAAM,CAC7B,KAAM,CAAAC,QAAQ,CAAGT,WAAW,CAAC,CAAC,CAC9B,KAAM,CAAEU,eAAgB,CAAC,CAAGT,OAAO,CAAC,CAAC,CACrC,KAAM,CAACU,YAAY,CAAEC,eAAe,CAAC,CAAGtB,QAAQ,CAAC,IAAI,CAAC,CACtD,KAAM,CAACuB,aAAa,CAAEC,gBAAgB,CAAC,CAAGxB,QAAQ,CAAC,SAAS,CAAC,CAC7D,KAAM,CAACyB,YAAY,CAAEC,eAAe,CAAC,CAAG1B,QAAQ,CAAC,KAAK,CAAC,CAEvD;AACA,KAAM,CAAA2B,UAAU,CAAGC,YAAY,CAACC,OAAO,CAAC,oBAAoB,CAAC,CAC7D,KAAM,CAAAC,YAAY,CAAGF,YAAY,CAACC,OAAO,CAAC,sBAAsB,CAAC,CAEjE;AACA,KAAM,CAAAE,oBAAoB,CAAGA,CAAA,GAAM,CACjCZ,QAAQ,CAAC,qBAAqB,CAAC,CACjC,CAAC,CAED;AACA,KAAM,CAAAa,iBAAiB,CAAGA,CAAA,GAAM,CAC9Bb,QAAQ,CAAC,kBAAkB,CAAC,CAC9B,CAAC,CAED;AACA,KAAM,CAAAc,aAAa,CAAIC,OAAO,EAAK,CACjCZ,eAAe,CAACY,OAAO,CAAC,CACxBV,gBAAgB,CAAC,SAAS,CAAC,CAC3BE,eAAe,CAAC,IAAI,CAAC,CACvB,CAAC,CAED;AACA,KAAM,CAAAS,WAAW,CAAID,OAAO,EAAK,CAC/BZ,eAAe,CAACY,OAAO,CAAC,CACxBV,gBAAgB,CAAC,OAAO,CAAC,CACzBE,eAAe,CAAC,IAAI,CAAC,CACvB,CAAC,CAED;AACA,KAAM,CAAAU,mBAAmB,CAAGA,CAAA,GAAM,CAChCV,eAAe,CAAC,KAAK,CAAC,CACxB,CAAC,CAED;AACA,GAAI,CAACC,UAAU,CAAE,CACfR,QAAQ,CAAC,qBAAqB,CAAC,CAC/B,MAAO,KAAI,CACb,CAEA,mBACEF,KAAA,CAAChB,GAAG,EAAAoC,QAAA,eACFpB,KAAA,CAAChB,GAAG,EAACqC,EAAE,CAAE,CAAEC,EAAE,CAAE,CAAC,CAAEC,OAAO,CAAE,MAAM,CAAEC,UAAU,CAAE,QAAQ,CAAEC,cAAc,CAAE,eAAgB,CAAE,CAAAL,QAAA,eACzFpB,KAAA,CAAChB,GAAG,EAACqC,EAAE,CAAE,CAAEE,OAAO,CAAE,MAAM,CAAEC,UAAU,CAAE,QAAS,CAAE,CAAAJ,QAAA,eACjDtB,IAAA,CAACZ,UAAU,EAACwC,OAAO,CAAEZ,oBAAqB,CAACO,EAAE,CAAE,CAAEM,EAAE,CAAE,CAAE,CAAE,CAAAP,QAAA,cACvDtB,IAAA,CAACR,aAAa,GAAE,CAAC,CACP,CAAC,cACbQ,IAAA,CAACb,UAAU,EAAC2C,OAAO,CAAC,IAAI,CAAAR,QAAA,CAAC,eAEzB,CAAY,CAAC,cACbtB,IAAA,CAACZ,UAAU,EACTwC,OAAO,CAAEA,CAAA,GAAMG,MAAM,CAACC,QAAQ,CAACC,MAAM,CAAC,CAAE,CACxCV,EAAE,CAAE,CAAEW,EAAE,CAAE,CAAE,CAAE,CACdC,KAAK,CAAC,SAAS,CACfC,KAAK,CAAC,oBAAoB,CAAAd,QAAA,cAE1BtB,IAAA,CAACN,WAAW,GAAE,CAAC,CACL,CAAC,EACV,CAAC,cACNM,IAAA,CAACH,eAAe,GAAE,CAAC,EAChB,CAAC,cAGNG,IAAA,CAACF,iBAAiB,EAChBc,UAAU,CAAEA,UAAW,CACvByB,SAAS,CAAEnB,aAAc,CACzBoB,OAAO,CAAElB,WAAY,CACtB,CAAC,cAEFpB,IAAA,CAACV,QAAQ,EACPiD,IAAI,CAAE7B,YAAa,CACnB8B,gBAAgB,CAAE,IAAK,CACvBC,OAAO,CAAEpB,mBAAoB,CAC7BqB,YAAY,CAAE,CAAEC,QAAQ,CAAE,QAAQ,CAAEC,UAAU,CAAE,QAAS,CAAE,CAAAtB,QAAA,cAE3DtB,IAAA,CAACX,KAAK,EAACoD,OAAO,CAAEpB,mBAAoB,CAACwB,QAAQ,CAAErC,aAAc,CAACe,EAAE,CAAE,CAAEuB,KAAK,CAAE,MAAO,CAAE,CAAAxB,QAAA,CACjFhB,YAAY,CACR,CAAC,CACA,CAAC,EACR,CAAC,CAEV,CAAC,CAED,cAAe,CAAAH,gBAAgB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}