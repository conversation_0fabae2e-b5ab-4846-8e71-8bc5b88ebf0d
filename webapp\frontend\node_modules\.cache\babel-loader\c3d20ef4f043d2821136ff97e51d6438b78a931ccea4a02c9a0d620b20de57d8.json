{"ast": null, "code": "import { formatDistance } from \"./ka/_lib/formatDistance.mjs\";\nimport { formatLong } from \"./ka/_lib/formatLong.mjs\";\nimport { formatRelative } from \"./ka/_lib/formatRelative.mjs\";\nimport { localize } from \"./ka/_lib/localize.mjs\";\nimport { match } from \"./ka/_lib/match.mjs\";\n\n/**\n * @category Locales\n * @summary Georgian locale.\n * @language Georgian\n * @iso-639-2 geo\n * <AUTHOR> [@Landish](https://github.com/Landish)\n * <AUTHOR> [@shvelo](https://github.com/shvelo)\n */\nexport const ka = {\n  code: \"ka\",\n  formatDistance: formatDistance,\n  formatLong: formatLong,\n  formatRelative: formatRelative,\n  localize: localize,\n  match: match,\n  options: {\n    weekStartsOn: 1 /* Monday */,\n    firstWeekContainsDate: 1\n  }\n};\n\n// Fallback for modularized imports:\nexport default ka;", "map": {"version": 3, "names": ["formatDistance", "formatLong", "formatRelative", "localize", "match", "ka", "code", "options", "weekStartsOn", "firstWeekContainsDate"], "sources": ["C:/CMS/webapp/frontend/node_modules/date-fns/locale/ka.mjs"], "sourcesContent": ["import { formatDistance } from \"./ka/_lib/formatDistance.mjs\";\nimport { formatLong } from \"./ka/_lib/formatLong.mjs\";\nimport { formatRelative } from \"./ka/_lib/formatRelative.mjs\";\nimport { localize } from \"./ka/_lib/localize.mjs\";\nimport { match } from \"./ka/_lib/match.mjs\";\n\n/**\n * @category Locales\n * @summary Georgian locale.\n * @language Georgian\n * @iso-639-2 geo\n * <AUTHOR> [@Landish](https://github.com/Landish)\n * <AUTHOR> [@shvelo](https://github.com/shvelo)\n */\nexport const ka = {\n  code: \"ka\",\n  formatDistance: formatDistance,\n  formatLong: formatLong,\n  formatRelative: formatRelative,\n  localize: localize,\n  match: match,\n  options: {\n    weekStartsOn: 1 /* Monday */,\n    firstWeekContainsDate: 1,\n  },\n};\n\n// Fallback for modularized imports:\nexport default ka;\n"], "mappings": "AAAA,SAASA,cAAc,QAAQ,8BAA8B;AAC7D,SAASC,UAAU,QAAQ,0BAA0B;AACrD,SAASC,cAAc,QAAQ,8BAA8B;AAC7D,SAASC,QAAQ,QAAQ,wBAAwB;AACjD,SAASC,KAAK,QAAQ,qBAAqB;;AAE3C;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO,MAAMC,EAAE,GAAG;EAChBC,IAAI,EAAE,IAAI;EACVN,cAAc,EAAEA,cAAc;EAC9BC,UAAU,EAAEA,UAAU;EACtBC,cAAc,EAAEA,cAAc;EAC9BC,QAAQ,EAAEA,QAAQ;EAClBC,KAAK,EAAEA,KAAK;EACZG,OAAO,EAAE;IACPC,YAAY,EAAE,CAAC,CAAC;IAChBC,qBAAqB,EAAE;EACzB;AACF,CAAC;;AAED;AACA,eAAeJ,EAAE", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}