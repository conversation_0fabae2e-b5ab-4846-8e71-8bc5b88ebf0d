{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\PycharmProjects\\\\CMS\\\\webapp\\\\frontend\\\\src\\\\components\\\\MainMenu.js\",\n  _s = $RefreshSig$();\nimport React from 'react';\nimport { useNavigate, useLocation } from 'react-router-dom';\nimport { List, ListItem, ListItemIcon, ListItemText, Divider } from '@mui/material';\nimport { Home as HomeIcon, AdminPanelSettings as AdminIcon, Construction as ConstructionIcon, Cable as CableIcon, Description as ReportIcon } from '@mui/icons-material';\nimport { useAuth } from '../context/AuthContext';\nimport { jsxDEV as _jsxDEV, Fragment as _Fragment } from \"react/jsx-dev-runtime\";\nconst MainMenu = () => {\n  _s();\n  const navigate = useNavigate();\n  const location = useLocation();\n  const {\n    user\n  } = useAuth();\n\n  // Verifica se un percorso è attivo\n  const isActive = path => {\n    return location.pathname === path;\n  };\n\n  // Naviga a un percorso\n  const navigateTo = path => {\n    navigate(path);\n  };\n  return /*#__PURE__*/_jsxDEV(List, {\n    children: [/*#__PURE__*/_jsxDEV(ListItem, {\n      button: true,\n      selected: isActive('/dashboard'),\n      onClick: () => navigateTo('/dashboard'),\n      children: [/*#__PURE__*/_jsxDEV(ListItemIcon, {\n        children: /*#__PURE__*/_jsxDEV(HomeIcon, {}, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 43,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 42,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(ListItemText, {\n        primary: \"Home\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 45,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 37,\n      columnNumber: 7\n    }, this), (user === null || user === void 0 ? void 0 : user.role) === 'owner' && /*#__PURE__*/_jsxDEV(_Fragment, {\n      children: [/*#__PURE__*/_jsxDEV(Divider, {}, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 51,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(ListItem, {\n        button: true,\n        selected: isActive('/dashboard/admin'),\n        onClick: () => navigateTo('/dashboard/admin'),\n        children: [/*#__PURE__*/_jsxDEV(ListItemIcon, {\n          children: /*#__PURE__*/_jsxDEV(AdminIcon, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 58,\n            columnNumber: 15\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 57,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(ListItemText, {\n          primary: \"Amministrazione\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 60,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 52,\n        columnNumber: 11\n      }, this)]\n    }, void 0, true), (user === null || user === void 0 ? void 0 : user.role) !== 'owner' && /*#__PURE__*/_jsxDEV(_Fragment, {\n      children: [/*#__PURE__*/_jsxDEV(Divider, {}, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 68,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(ListItem, {\n        button: true,\n        selected: isActive('/dashboard/cantieri'),\n        onClick: () => navigateTo('/dashboard/cantieri'),\n        children: [/*#__PURE__*/_jsxDEV(ListItemIcon, {\n          children: /*#__PURE__*/_jsxDEV(ConstructionIcon, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 75,\n            columnNumber: 15\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 74,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(ListItemText, {\n          primary: \"I Miei Cantieri\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 77,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 69,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(ListItem, {\n        button: true,\n        selected: isActive('/dashboard/cavi'),\n        onClick: () => navigateTo('/dashboard/cavi'),\n        children: [/*#__PURE__*/_jsxDEV(ListItemIcon, {\n          children: /*#__PURE__*/_jsxDEV(CableIcon, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 87,\n            columnNumber: 15\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 86,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(ListItemText, {\n          primary: \"Gestione Cavi\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 89,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 81,\n        columnNumber: 11\n      }, this)]\n    }, void 0, true)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 35,\n    columnNumber: 5\n  }, this);\n};\n_s(MainMenu, \"bz7/UxFJEN3KzTh2CtLO1c1o5iM=\", false, function () {\n  return [useNavigate, useLocation, useAuth];\n});\n_c = MainMenu;\nexport default MainMenu;\nvar _c;\n$RefreshReg$(_c, \"MainMenu\");", "map": {"version": 3, "names": ["React", "useNavigate", "useLocation", "List", "ListItem", "ListItemIcon", "ListItemText", "Divider", "Home", "HomeIcon", "AdminPanelSettings", "AdminIcon", "Construction", "ConstructionIcon", "Cable", "CableIcon", "Description", "ReportIcon", "useAuth", "jsxDEV", "_jsxDEV", "Fragment", "_Fragment", "MainMenu", "_s", "navigate", "location", "user", "isActive", "path", "pathname", "navigateTo", "children", "button", "selected", "onClick", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "primary", "role", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/PycharmProjects/CMS/webapp/frontend/src/components/MainMenu.js"], "sourcesContent": ["import React from 'react';\nimport { useNavigate, useLocation } from 'react-router-dom';\nimport {\n  List,\n  ListItem,\n  ListItemIcon,\n  ListItemText,\n  Divider\n} from '@mui/material';\nimport {\n  Home as HomeIcon,\n  AdminPanelSettings as AdminIcon,\n  Construction as ConstructionIcon,\n  Cable as CableIcon,\n  Description as ReportIcon\n} from '@mui/icons-material';\nimport { useAuth } from '../context/AuthContext';\n\nconst MainMenu = () => {\n  const navigate = useNavigate();\n  const location = useLocation();\n  const { user } = useAuth();\n\n  // Verifica se un percorso è attivo\n  const isActive = (path) => {\n    return location.pathname === path;\n  };\n\n  // Naviga a un percorso\n  const navigateTo = (path) => {\n    navigate(path);\n  };\n\n  return (\n    <List>\n      {/* Home */}\n      <ListItem\n        button\n        selected={isActive('/dashboard')}\n        onClick={() => navigateTo('/dashboard')}\n      >\n        <ListItemIcon>\n          <HomeIcon />\n        </ListItemIcon>\n        <ListItemText primary=\"Home\" />\n      </ListItem>\n\n      {/* Menu Amministratore (solo per admin) */}\n      {user?.role === 'owner' && (\n        <>\n          <Divider />\n          <ListItem\n            button\n            selected={isActive('/dashboard/admin')}\n            onClick={() => navigateTo('/dashboard/admin')}\n          >\n            <ListItemIcon>\n              <AdminIcon />\n            </ListItemIcon>\n            <ListItemText primary=\"Amministrazione\" />\n          </ListItem>\n        </>\n      )}\n\n      {/* Menu per utenti standard e cantieri (non per admin) */}\n      {user?.role !== 'owner' && (\n        <>\n          <Divider />\n          <ListItem\n            button\n            selected={isActive('/dashboard/cantieri')}\n            onClick={() => navigateTo('/dashboard/cantieri')}\n          >\n            <ListItemIcon>\n              <ConstructionIcon />\n            </ListItemIcon>\n            <ListItemText primary=\"I Miei Cantieri\" />\n          </ListItem>\n\n          {/* Menu Cavi */}\n          <ListItem\n            button\n            selected={isActive('/dashboard/cavi')}\n            onClick={() => navigateTo('/dashboard/cavi')}\n          >\n            <ListItemIcon>\n              <CableIcon />\n            </ListItemIcon>\n            <ListItemText primary=\"Gestione Cavi\" />\n          </ListItem>\n\n          {/* Rimosso il menu Report perché appartiene al menu gestione cavi */}\n        </>\n      )}\n    </List>\n  );\n};\n\nexport default MainMenu;\n"], "mappings": ";;AAAA,OAAOA,KAAK,MAAM,OAAO;AACzB,SAASC,WAAW,EAAEC,WAAW,QAAQ,kBAAkB;AAC3D,SACEC,IAAI,EACJC,QAAQ,EACRC,YAAY,EACZC,YAAY,EACZC,OAAO,QACF,eAAe;AACtB,SACEC,IAAI,IAAIC,QAAQ,EAChBC,kBAAkB,IAAIC,SAAS,EAC/BC,YAAY,IAAIC,gBAAgB,EAChCC,KAAK,IAAIC,SAAS,EAClBC,WAAW,IAAIC,UAAU,QACpB,qBAAqB;AAC5B,SAASC,OAAO,QAAQ,wBAAwB;AAAC,SAAAC,MAAA,IAAAC,OAAA,EAAAC,QAAA,IAAAC,SAAA;AAEjD,MAAMC,QAAQ,GAAGA,CAAA,KAAM;EAAAC,EAAA;EACrB,MAAMC,QAAQ,GAAGxB,WAAW,CAAC,CAAC;EAC9B,MAAMyB,QAAQ,GAAGxB,WAAW,CAAC,CAAC;EAC9B,MAAM;IAAEyB;EAAK,CAAC,GAAGT,OAAO,CAAC,CAAC;;EAE1B;EACA,MAAMU,QAAQ,GAAIC,IAAI,IAAK;IACzB,OAAOH,QAAQ,CAACI,QAAQ,KAAKD,IAAI;EACnC,CAAC;;EAED;EACA,MAAME,UAAU,GAAIF,IAAI,IAAK;IAC3BJ,QAAQ,CAACI,IAAI,CAAC;EAChB,CAAC;EAED,oBACET,OAAA,CAACjB,IAAI;IAAA6B,QAAA,gBAEHZ,OAAA,CAAChB,QAAQ;MACP6B,MAAM;MACNC,QAAQ,EAAEN,QAAQ,CAAC,YAAY,CAAE;MACjCO,OAAO,EAAEA,CAAA,KAAMJ,UAAU,CAAC,YAAY,CAAE;MAAAC,QAAA,gBAExCZ,OAAA,CAACf,YAAY;QAAA2B,QAAA,eACXZ,OAAA,CAACX,QAAQ;UAAA2B,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACA,CAAC,eACfnB,OAAA,CAACd,YAAY;QAACkC,OAAO,EAAC;MAAM;QAAAJ,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACvB,CAAC,EAGV,CAAAZ,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAEc,IAAI,MAAK,OAAO,iBACrBrB,OAAA,CAAAE,SAAA;MAAAU,QAAA,gBACEZ,OAAA,CAACb,OAAO;QAAA6B,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC,eACXnB,OAAA,CAAChB,QAAQ;QACP6B,MAAM;QACNC,QAAQ,EAAEN,QAAQ,CAAC,kBAAkB,CAAE;QACvCO,OAAO,EAAEA,CAAA,KAAMJ,UAAU,CAAC,kBAAkB,CAAE;QAAAC,QAAA,gBAE9CZ,OAAA,CAACf,YAAY;UAAA2B,QAAA,eACXZ,OAAA,CAACT,SAAS;YAAAyB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACD,CAAC,eACfnB,OAAA,CAACd,YAAY;UAACkC,OAAO,EAAC;QAAiB;UAAAJ,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAClC,CAAC;IAAA,eACX,CACH,EAGA,CAAAZ,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAEc,IAAI,MAAK,OAAO,iBACrBrB,OAAA,CAAAE,SAAA;MAAAU,QAAA,gBACEZ,OAAA,CAACb,OAAO;QAAA6B,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC,eACXnB,OAAA,CAAChB,QAAQ;QACP6B,MAAM;QACNC,QAAQ,EAAEN,QAAQ,CAAC,qBAAqB,CAAE;QAC1CO,OAAO,EAAEA,CAAA,KAAMJ,UAAU,CAAC,qBAAqB,CAAE;QAAAC,QAAA,gBAEjDZ,OAAA,CAACf,YAAY;UAAA2B,QAAA,eACXZ,OAAA,CAACP,gBAAgB;YAAAuB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACR,CAAC,eACfnB,OAAA,CAACd,YAAY;UAACkC,OAAO,EAAC;QAAiB;UAAAJ,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAClC,CAAC,eAGXnB,OAAA,CAAChB,QAAQ;QACP6B,MAAM;QACNC,QAAQ,EAAEN,QAAQ,CAAC,iBAAiB,CAAE;QACtCO,OAAO,EAAEA,CAAA,KAAMJ,UAAU,CAAC,iBAAiB,CAAE;QAAAC,QAAA,gBAE7CZ,OAAA,CAACf,YAAY;UAAA2B,QAAA,eACXZ,OAAA,CAACL,SAAS;YAAAqB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACD,CAAC,eACfnB,OAAA,CAACd,YAAY;UAACkC,OAAO,EAAC;QAAe;UAAAJ,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAChC,CAAC;IAAA,eAGX,CACH;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACG,CAAC;AAEX,CAAC;AAACf,EAAA,CA9EID,QAAQ;EAAA,QACKtB,WAAW,EACXC,WAAW,EACXgB,OAAO;AAAA;AAAAwB,EAAA,GAHpBnB,QAAQ;AAgFd,eAAeA,QAAQ;AAAC,IAAAmB,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}