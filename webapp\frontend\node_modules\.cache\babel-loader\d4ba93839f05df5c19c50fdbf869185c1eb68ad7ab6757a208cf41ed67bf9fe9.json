{"ast": null, "code": "var _jsxFileName = \"C:\\\\CMS\\\\webapp\\\\frontend\\\\src\\\\components\\\\comande\\\\ComandeListRivoluzionato.js\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect } from 'react';\nimport { Box, Card, CardContent, Typography, Button, Table, TableBody, TableCell, TableContainer, TableHead, TableRow, Paper, Chip, IconButton, Dialog, DialogTitle, DialogContent, DialogActions, TextField, Alert, CircularProgress, Tooltip, Grid, List, ListItem, ListItemText, Tabs, Tab, Accordion, AccordionSummary, AccordionDetails } from '@mui/material';\nimport { Add as AddIcon, Edit as EditIcon, Delete as DeleteIcon, Visibility as ViewIcon, Assignment as AssignIcon, Refresh as RefreshIcon, Person as PersonIcon, Email as EmailIcon, Phone as PhoneIcon, ExpandMore as ExpandMoreIcon } from '@mui/icons-material';\nimport comandeService from '../../services/comandeService';\nimport responsabiliService from '../../services/responsabiliService';\nimport CreaComandaConCavi from './CreaComandaConCavi';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst ComandeListRivoluzionato = ({\n  cantiereId,\n  cantiereName\n}) => {\n  _s();\n  // Stati principali\n  const [activeTab, setActiveTab] = useState(0);\n  const [loading, setLoading] = useState(true);\n  const [error, setError] = useState(null);\n\n  // Stati comande\n  const [comande, setComande] = useState([]);\n  const [statistiche, setStatistiche] = useState(null);\n  const [openCreaConCavi, setOpenCreaConCavi] = useState(false);\n\n  // Stati responsabili\n  const [responsabili, setResponsabili] = useState([]);\n  const [loadingResponsabili, setLoadingResponsabili] = useState(false);\n  const [comandePerResponsabile, setComandePerResponsabile] = useState({});\n  const [openResponsabileDialog, setOpenResponsabileDialog] = useState(false);\n  const [dialogModeResponsabile, setDialogModeResponsabile] = useState('create');\n  const [selectedResponsabile, setSelectedResponsabile] = useState(null);\n  const [formDataResponsabile, setFormDataResponsabile] = useState({\n    nome_responsabile: '',\n    email: '',\n    telefono: ''\n  });\n\n  // Stato per il dialog di conferma eliminazione\n  const [deleteConfirmDialog, setDeleteConfirmDialog] = useState({\n    open: false,\n    responsabileId: null,\n    responsabileName: ''\n  });\n\n  // Carica dati al mount\n  useEffect(() => {\n    if (cantiereId) {\n      loadComande();\n      loadStatistiche();\n      loadResponsabili();\n    }\n  }, [cantiereId]);\n  const loadComande = async () => {\n    try {\n      setLoading(true);\n      const data = await comandeService.getComande(cantiereId);\n      setComande(data.comande || []);\n      setError(null);\n    } catch (err) {\n      console.error('Errore nel caricamento delle comande:', err);\n      setError('Errore nel caricamento delle comande');\n    } finally {\n      setLoading(false);\n    }\n  };\n  const loadStatistiche = async () => {\n    try {\n      const stats = await comandeService.getStatisticheComande(cantiereId);\n      setStatistiche(stats);\n    } catch (err) {\n      console.error('Errore nel caricamento delle statistiche:', err);\n    }\n  };\n  const loadResponsabili = async () => {\n    try {\n      setLoadingResponsabili(true);\n      setError(null);\n      const data = await responsabiliService.getResponsabiliCantiere(cantiereId);\n      setResponsabili(data || []);\n      await loadComandePerResponsabili(data || []);\n    } catch (err) {\n      var _err$response, _err$response$data;\n      console.error('Errore nel caricamento dei responsabili:', err);\n      const errorMessage = ((_err$response = err.response) === null || _err$response === void 0 ? void 0 : (_err$response$data = _err$response.data) === null || _err$response$data === void 0 ? void 0 : _err$response$data.detail) || err.message || 'Errore nel caricamento dei responsabili';\n      setError(`Errore nel caricamento dei responsabili: ${errorMessage}`);\n    } finally {\n      setLoadingResponsabili(false);\n    }\n  };\n  const loadComandePerResponsabili = async responsabiliList => {\n    try {\n      const comandeMap = {};\n      for (const responsabile of responsabiliList) {\n        try {\n          const response = await comandeService.getComandeByResponsabile(cantiereId, responsabile.nome_responsabile);\n          // Assicurati che sia sempre un array\n          let comande = [];\n          if (response && Array.isArray(response)) {\n            comande = response;\n          } else if (response && response.comande && Array.isArray(response.comande)) {\n            comande = response.comande;\n          } else if (response && response.data && Array.isArray(response.data)) {\n            comande = response.data;\n          }\n          comandeMap[responsabile.id_responsabile] = comande;\n        } catch (err) {\n          console.error(`Errore nel caricamento comande per ${responsabile.nome_responsabile}:`, err);\n          comandeMap[responsabile.id_responsabile] = [];\n        }\n      }\n      setComandePerResponsabile(comandeMap);\n    } catch (err) {\n      console.error('Errore nel caricamento delle comande:', err);\n    }\n  };\n\n  // Gestione responsabili\n  const handleOpenResponsabileDialog = (mode, responsabile = null) => {\n    setDialogModeResponsabile(mode);\n    setSelectedResponsabile(responsabile);\n    if (mode === 'edit' && responsabile) {\n      setFormDataResponsabile({\n        nome_responsabile: responsabile.nome_responsabile || '',\n        email: responsabile.email || '',\n        telefono: responsabile.telefono || ''\n      });\n    } else {\n      setFormDataResponsabile({\n        nome_responsabile: '',\n        email: '',\n        telefono: ''\n      });\n    }\n    setOpenResponsabileDialog(true);\n  };\n  const handleCloseResponsabileDialog = () => {\n    setOpenResponsabileDialog(false);\n    setSelectedResponsabile(null);\n    setError(null);\n  };\n  const handleSubmitResponsabile = async () => {\n    try {\n      setError(null);\n      if (!formDataResponsabile.nome_responsabile.trim()) {\n        setError('Il nome del responsabile è obbligatorio');\n        return;\n      }\n      if (!formDataResponsabile.email && !formDataResponsabile.telefono) {\n        setError('Almeno uno tra email e telefono deve essere specificato');\n        return;\n      }\n      if (dialogModeResponsabile === 'create') {\n        await responsabiliService.createResponsabile(cantiereId, formDataResponsabile);\n      } else if (dialogModeResponsabile === 'edit') {\n        await responsabiliService.updateResponsabile(selectedResponsabile.id_responsabile, formDataResponsabile);\n      }\n      handleCloseResponsabileDialog();\n      await loadResponsabili();\n    } catch (err) {\n      console.error('Errore nel salvataggio:', err);\n      setError(err.detail || 'Errore nel salvataggio del responsabile');\n    }\n  };\n  const handleDeleteResponsabile = async idResponsabile => {\n    if (!window.confirm('Sei sicuro di voler eliminare questo responsabile?')) {\n      return;\n    }\n    try {\n      await responsabiliService.deleteResponsabile(idResponsabile);\n      await loadResponsabili();\n    } catch (err) {\n      console.error('Errore nell\\'eliminazione:', err);\n      setError('Errore nell\\'eliminazione del responsabile');\n    }\n  };\n  const getTipoComandaLabel = tipo => {\n    const labels = {\n      'POSA': 'Posa',\n      'COLLEGAMENTO_PARTENZA': 'Coll. Partenza',\n      'COLLEGAMENTO_ARRIVO': 'Coll. Arrivo',\n      'CERTIFICAZIONE': 'Certificazione'\n    };\n    return labels[tipo] || tipo;\n  };\n  const getStatoColor = stato => {\n    const colors = {\n      'CREATA': 'default',\n      'IN_CORSO': 'primary',\n      'COMPLETATA': 'success',\n      'ANNULLATA': 'error'\n    };\n    return colors[stato] || 'default';\n  };\n  if (loading) {\n    return /*#__PURE__*/_jsxDEV(Box, {\n      display: \"flex\",\n      justifyContent: \"center\",\n      alignItems: \"center\",\n      minHeight: \"400px\",\n      children: /*#__PURE__*/_jsxDEV(CircularProgress, {}, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 251,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 250,\n      columnNumber: 7\n    }, this);\n  }\n  return /*#__PURE__*/_jsxDEV(Box, {\n    children: [error && /*#__PURE__*/_jsxDEV(Alert, {\n      severity: \"error\",\n      sx: {\n        mb: 2\n      },\n      children: error\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 260,\n      columnNumber: 9\n    }, this), /*#__PURE__*/_jsxDEV(Box, {\n      sx: {\n        borderBottom: 1,\n        borderColor: 'divider',\n        mb: 4\n      },\n      children: /*#__PURE__*/_jsxDEV(Tabs, {\n        value: activeTab,\n        onChange: (e, newValue) => setActiveTab(newValue),\n        sx: {\n          '& .MuiTab-root': {\n            textTransform: 'none',\n            fontWeight: 500,\n            fontSize: '1rem'\n          }\n        },\n        children: [/*#__PURE__*/_jsxDEV(Tab, {\n          label: \"Responsabili\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 278,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Tab, {\n          label: \"Tutte le Comande\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 279,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 267,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 266,\n      columnNumber: 7\n    }, this), activeTab === 0 && /*#__PURE__*/_jsxDEV(Box, {\n      children: [/*#__PURE__*/_jsxDEV(Box, {\n        display: \"flex\",\n        justifyContent: \"space-between\",\n        alignItems: \"center\",\n        mb: 3,\n        children: [/*#__PURE__*/_jsxDEV(Typography, {\n          variant: \"h5\",\n          sx: {\n            fontWeight: 500,\n            color: 'text.primary'\n          },\n          children: \"Responsabili del Cantiere\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 288,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(Button, {\n          variant: \"contained\",\n          startIcon: /*#__PURE__*/_jsxDEV(AddIcon, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 293,\n            columnNumber: 26\n          }, this),\n          onClick: () => handleOpenResponsabileDialog('create'),\n          sx: {\n            textTransform: 'none',\n            fontWeight: 500,\n            px: 3,\n            py: 1\n          },\n          children: \"Inserisci Responsabile\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 291,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 287,\n        columnNumber: 11\n      }, this), loadingResponsabili ? /*#__PURE__*/_jsxDEV(Box, {\n        display: \"flex\",\n        justifyContent: \"center\",\n        py: 4,\n        children: /*#__PURE__*/_jsxDEV(CircularProgress, {}, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 308,\n          columnNumber: 15\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 307,\n        columnNumber: 13\n      }, this) : /*#__PURE__*/_jsxDEV(Box, {\n        children: responsabili.length === 0 ? /*#__PURE__*/_jsxDEV(Paper, {\n          elevation: 0,\n          sx: {\n            p: 6,\n            textAlign: 'center',\n            backgroundColor: 'grey.50',\n            border: '1px dashed',\n            borderColor: 'grey.300'\n          },\n          children: [/*#__PURE__*/_jsxDEV(PersonIcon, {\n            sx: {\n              fontSize: 48,\n              color: 'grey.400',\n              mb: 2\n            }\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 323,\n            columnNumber: 19\n          }, this), /*#__PURE__*/_jsxDEV(Typography, {\n            variant: \"h6\",\n            color: \"text.secondary\",\n            gutterBottom: true,\n            children: \"Nessun responsabile configurato\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 324,\n            columnNumber: 19\n          }, this), /*#__PURE__*/_jsxDEV(Typography, {\n            variant: \"body2\",\n            color: \"text.secondary\",\n            sx: {\n              mb: 3\n            },\n            children: \"Aggiungi il primo responsabile per iniziare a gestire le comande\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 327,\n            columnNumber: 19\n          }, this), /*#__PURE__*/_jsxDEV(Button, {\n            variant: \"contained\",\n            startIcon: /*#__PURE__*/_jsxDEV(AddIcon, {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 332,\n              columnNumber: 32\n            }, this),\n            onClick: () => handleOpenResponsabileDialog('create'),\n            sx: {\n              textTransform: 'none'\n            },\n            children: \"Inserisci Primo Responsabile\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 330,\n            columnNumber: 19\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 313,\n          columnNumber: 17\n        }, this) : responsabili.map(responsabile => /*#__PURE__*/_jsxDEV(Accordion, {\n          sx: {\n            mb: 2,\n            '&:before': {\n              display: 'none'\n            },\n            boxShadow: '0 1px 3px rgba(0,0,0,0.1)',\n            border: '1px solid',\n            borderColor: 'grey.200'\n          },\n          children: [/*#__PURE__*/_jsxDEV(AccordionSummary, {\n            expandIcon: /*#__PURE__*/_jsxDEV(ExpandMoreIcon, {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 352,\n              columnNumber: 35\n            }, this),\n            sx: {\n              '&:hover': {\n                backgroundColor: 'grey.50'\n              }\n            },\n            children: /*#__PURE__*/_jsxDEV(Box, {\n              display: \"flex\",\n              alignItems: \"center\",\n              justifyContent: \"space-between\",\n              width: \"100%\",\n              children: [/*#__PURE__*/_jsxDEV(Box, {\n                display: \"flex\",\n                alignItems: \"center\",\n                gap: 2,\n                children: [/*#__PURE__*/_jsxDEV(PersonIcon, {\n                  color: \"primary\",\n                  sx: {\n                    fontSize: 28\n                  }\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 361,\n                  columnNumber: 27\n                }, this), /*#__PURE__*/_jsxDEV(Box, {\n                  children: [/*#__PURE__*/_jsxDEV(Typography, {\n                    variant: \"h6\",\n                    sx: {\n                      fontWeight: 500\n                    },\n                    children: responsabile.nome_responsabile\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 363,\n                    columnNumber: 29\n                  }, this), /*#__PURE__*/_jsxDEV(Box, {\n                    display: \"flex\",\n                    gap: 3,\n                    mt: 0.5,\n                    children: [responsabile.email && /*#__PURE__*/_jsxDEV(Box, {\n                      display: \"flex\",\n                      alignItems: \"center\",\n                      gap: 0.5,\n                      children: [/*#__PURE__*/_jsxDEV(EmailIcon, {\n                        fontSize: \"small\",\n                        color: \"action\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 369,\n                        columnNumber: 35\n                      }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                        variant: \"body2\",\n                        color: \"text.secondary\",\n                        children: responsabile.email\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 370,\n                        columnNumber: 35\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 368,\n                      columnNumber: 33\n                    }, this), responsabile.telefono && /*#__PURE__*/_jsxDEV(Box, {\n                      display: \"flex\",\n                      alignItems: \"center\",\n                      gap: 0.5,\n                      children: [/*#__PURE__*/_jsxDEV(PhoneIcon, {\n                        fontSize: \"small\",\n                        color: \"action\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 377,\n                        columnNumber: 35\n                      }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                        variant: \"body2\",\n                        color: \"text.secondary\",\n                        children: responsabile.telefono\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 378,\n                        columnNumber: 35\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 376,\n                      columnNumber: 33\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 366,\n                    columnNumber: 29\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 362,\n                  columnNumber: 27\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 360,\n                columnNumber: 25\n              }, this), /*#__PURE__*/_jsxDEV(Box, {\n                display: \"flex\",\n                alignItems: \"center\",\n                gap: 1,\n                onClick: e => e.stopPropagation(),\n                children: [/*#__PURE__*/_jsxDEV(Chip, {\n                  icon: /*#__PURE__*/_jsxDEV(AssignIcon, {}, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 389,\n                    columnNumber: 35\n                  }, this),\n                  label: `${Array.isArray(comandePerResponsabile[responsabile.id_responsabile]) ? comandePerResponsabile[responsabile.id_responsabile].length : 0} comande`,\n                  size: \"small\",\n                  color: \"primary\",\n                  variant: \"outlined\",\n                  sx: {\n                    fontWeight: 500\n                  }\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 388,\n                  columnNumber: 27\n                }, this), /*#__PURE__*/_jsxDEV(Tooltip, {\n                  title: \"Modifica responsabile\",\n                  children: /*#__PURE__*/_jsxDEV(IconButton, {\n                    size: \"small\",\n                    onClick: () => handleOpenResponsabileDialog('edit', responsabile),\n                    sx: {\n                      '&:hover': {\n                        backgroundColor: 'primary.light',\n                        color: 'white'\n                      }\n                    },\n                    children: /*#__PURE__*/_jsxDEV(EditIcon, {\n                      fontSize: \"small\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 407,\n                      columnNumber: 31\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 397,\n                    columnNumber: 29\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 396,\n                  columnNumber: 27\n                }, this), /*#__PURE__*/_jsxDEV(Tooltip, {\n                  title: \"Elimina responsabile\",\n                  children: /*#__PURE__*/_jsxDEV(IconButton, {\n                    size: \"small\",\n                    onClick: () => handleDeleteResponsabile(responsabile.id_responsabile),\n                    sx: {\n                      '&:hover': {\n                        backgroundColor: 'error.light',\n                        color: 'white'\n                      }\n                    },\n                    children: /*#__PURE__*/_jsxDEV(DeleteIcon, {\n                      fontSize: \"small\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 421,\n                      columnNumber: 31\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 411,\n                    columnNumber: 29\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 410,\n                  columnNumber: 27\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 387,\n                columnNumber: 25\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 359,\n              columnNumber: 23\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 351,\n            columnNumber: 21\n          }, this), /*#__PURE__*/_jsxDEV(AccordionDetails, {\n            sx: {\n              pt: 2\n            },\n            children: [/*#__PURE__*/_jsxDEV(Typography, {\n              variant: \"subtitle2\",\n              gutterBottom: true,\n              sx: {\n                fontWeight: 500,\n                mb: 2\n              },\n              children: \"Comande Assegnate\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 429,\n              columnNumber: 23\n            }, this), !Array.isArray(comandePerResponsabile[responsabile.id_responsabile]) || comandePerResponsabile[responsabile.id_responsabile].length === 0 ? /*#__PURE__*/_jsxDEV(Box, {\n              sx: {\n                p: 3,\n                textAlign: 'center',\n                backgroundColor: 'grey.50',\n                borderRadius: 1,\n                border: '1px dashed',\n                borderColor: 'grey.300'\n              },\n              children: /*#__PURE__*/_jsxDEV(Typography, {\n                variant: \"body2\",\n                color: \"text.secondary\",\n                children: \"Nessuna comanda assegnata a questo responsabile\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 444,\n                columnNumber: 27\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 434,\n              columnNumber: 25\n            }, this) : /*#__PURE__*/_jsxDEV(List, {\n              dense: true,\n              children: Array.isArray(comandePerResponsabile[responsabile.id_responsabile]) && comandePerResponsabile[responsabile.id_responsabile].map(comanda => /*#__PURE__*/_jsxDEV(ListItem, {\n                divider: true,\n                children: /*#__PURE__*/_jsxDEV(ListItemText, {\n                  primary: /*#__PURE__*/_jsxDEV(Box, {\n                    display: \"flex\",\n                    alignItems: \"center\",\n                    gap: 1,\n                    children: [/*#__PURE__*/_jsxDEV(Typography, {\n                      variant: \"body2\",\n                      fontWeight: \"bold\",\n                      children: comanda.codice_comanda\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 455,\n                      columnNumber: 37\n                    }, this), /*#__PURE__*/_jsxDEV(Chip, {\n                      label: getTipoComandaLabel(comanda.tipo_comanda),\n                      size: \"small\",\n                      variant: \"outlined\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 458,\n                      columnNumber: 37\n                    }, this), /*#__PURE__*/_jsxDEV(Chip, {\n                      label: comanda.stato || 'CREATA',\n                      size: \"small\",\n                      color: getStatoColor(comanda.stato)\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 463,\n                      columnNumber: 37\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 454,\n                    columnNumber: 35\n                  }, this),\n                  secondary: /*#__PURE__*/_jsxDEV(Typography, {\n                    variant: \"body2\",\n                    color: \"textSecondary\",\n                    children: [comanda.descrizione || 'Nessuna descrizione', comanda.data_creazione && ` • Creata: ${new Date(comanda.data_creazione).toLocaleDateString()}`]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 471,\n                    columnNumber: 35\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 452,\n                  columnNumber: 31\n                }, this)\n              }, comanda.codice_comanda, false, {\n                fileName: _jsxFileName,\n                lineNumber: 451,\n                columnNumber: 29\n              }, this))\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 449,\n              columnNumber: 25\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 428,\n            columnNumber: 21\n          }, this)]\n        }, responsabile.id_responsabile, true, {\n          fileName: _jsxFileName,\n          lineNumber: 341,\n          columnNumber: 19\n        }, this))\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 311,\n        columnNumber: 13\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 285,\n      columnNumber: 9\n    }, this), activeTab === 1 && /*#__PURE__*/_jsxDEV(Box, {\n      children: [statistiche && /*#__PURE__*/_jsxDEV(Grid, {\n        container: true,\n        spacing: 3,\n        sx: {\n          mb: 4\n        },\n        children: [/*#__PURE__*/_jsxDEV(Grid, {\n          item: true,\n          xs: 12,\n          sm: 6,\n          md: 3,\n          children: /*#__PURE__*/_jsxDEV(Card, {\n            sx: {\n              boxShadow: '0 1px 3px rgba(0,0,0,0.1)',\n              border: '1px solid',\n              borderColor: 'grey.200'\n            },\n            children: /*#__PURE__*/_jsxDEV(CardContent, {\n              sx: {\n                textAlign: 'center',\n                py: 3\n              },\n              children: [/*#__PURE__*/_jsxDEV(Typography, {\n                color: \"text.secondary\",\n                variant: \"body2\",\n                sx: {\n                  fontWeight: 500,\n                  mb: 1\n                },\n                children: \"Totale\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 499,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                variant: \"h4\",\n                sx: {\n                  fontWeight: 600,\n                  color: 'text.primary'\n                },\n                children: statistiche.totale_comande\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 502,\n                columnNumber: 21\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 498,\n              columnNumber: 19\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 497,\n            columnNumber: 17\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 496,\n          columnNumber: 15\n        }, this), /*#__PURE__*/_jsxDEV(Grid, {\n          item: true,\n          xs: 12,\n          sm: 6,\n          md: 3,\n          children: /*#__PURE__*/_jsxDEV(Card, {\n            sx: {\n              boxShadow: '0 1px 3px rgba(0,0,0,0.1)',\n              border: '1px solid',\n              borderColor: 'grey.200'\n            },\n            children: /*#__PURE__*/_jsxDEV(CardContent, {\n              sx: {\n                textAlign: 'center',\n                py: 3\n              },\n              children: [/*#__PURE__*/_jsxDEV(Typography, {\n                color: \"text.secondary\",\n                variant: \"body2\",\n                sx: {\n                  fontWeight: 500,\n                  mb: 1\n                },\n                children: \"Create\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 511,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                variant: \"h4\",\n                sx: {\n                  fontWeight: 600,\n                  color: 'primary.main'\n                },\n                children: statistiche.comande_create\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 514,\n                columnNumber: 21\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 510,\n              columnNumber: 19\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 509,\n            columnNumber: 17\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 508,\n          columnNumber: 15\n        }, this), /*#__PURE__*/_jsxDEV(Grid, {\n          item: true,\n          xs: 12,\n          sm: 6,\n          md: 3,\n          children: /*#__PURE__*/_jsxDEV(Card, {\n            sx: {\n              boxShadow: '0 1px 3px rgba(0,0,0,0.1)',\n              border: '1px solid',\n              borderColor: 'grey.200'\n            },\n            children: /*#__PURE__*/_jsxDEV(CardContent, {\n              sx: {\n                textAlign: 'center',\n                py: 3\n              },\n              children: [/*#__PURE__*/_jsxDEV(Typography, {\n                color: \"text.secondary\",\n                variant: \"body2\",\n                sx: {\n                  fontWeight: 500,\n                  mb: 1\n                },\n                children: \"In Corso\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 523,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                variant: \"h4\",\n                sx: {\n                  fontWeight: 600,\n                  color: 'warning.main'\n                },\n                children: statistiche.comande_in_corso\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 526,\n                columnNumber: 21\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 522,\n              columnNumber: 19\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 521,\n            columnNumber: 17\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 520,\n          columnNumber: 15\n        }, this), /*#__PURE__*/_jsxDEV(Grid, {\n          item: true,\n          xs: 12,\n          sm: 6,\n          md: 3,\n          children: /*#__PURE__*/_jsxDEV(Card, {\n            sx: {\n              boxShadow: '0 1px 3px rgba(0,0,0,0.1)',\n              border: '1px solid',\n              borderColor: 'grey.200'\n            },\n            children: /*#__PURE__*/_jsxDEV(CardContent, {\n              sx: {\n                textAlign: 'center',\n                py: 3\n              },\n              children: [/*#__PURE__*/_jsxDEV(Typography, {\n                color: \"text.secondary\",\n                variant: \"body2\",\n                sx: {\n                  fontWeight: 500,\n                  mb: 1\n                },\n                children: \"Completate\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 535,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                variant: \"h4\",\n                sx: {\n                  fontWeight: 600,\n                  color: 'success.main'\n                },\n                children: statistiche.comande_completate\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 538,\n                columnNumber: 21\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 534,\n              columnNumber: 19\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 533,\n            columnNumber: 17\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 532,\n          columnNumber: 15\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 495,\n        columnNumber: 13\n      }, this), /*#__PURE__*/_jsxDEV(Box, {\n        display: \"flex\",\n        justifyContent: \"space-between\",\n        alignItems: \"center\",\n        mb: 3,\n        children: [/*#__PURE__*/_jsxDEV(Typography, {\n          variant: \"h5\",\n          sx: {\n            fontWeight: 500,\n            color: 'text.primary'\n          },\n          children: \"Tutte le Comande\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 549,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(Box, {\n          display: \"flex\",\n          gap: 2,\n          children: [/*#__PURE__*/_jsxDEV(Button, {\n            variant: \"contained\",\n            startIcon: /*#__PURE__*/_jsxDEV(AddIcon, {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 555,\n              columnNumber: 28\n            }, this),\n            onClick: () => setOpenCreaConCavi(true),\n            sx: {\n              textTransform: 'none',\n              fontWeight: 500,\n              px: 3\n            },\n            children: \"Nuova Comanda\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 553,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(Button, {\n            variant: \"outlined\",\n            startIcon: /*#__PURE__*/_jsxDEV(RefreshIcon, {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 567,\n              columnNumber: 28\n            }, this),\n            onClick: () => {\n              loadComande();\n              loadStatistiche();\n            },\n            sx: {\n              textTransform: 'none',\n              fontWeight: 500\n            },\n            children: \"Aggiorna\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 565,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 552,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 548,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(TableContainer, {\n        component: Paper,\n        sx: {\n          boxShadow: '0 1px 3px rgba(0,0,0,0.1)'\n        },\n        children: /*#__PURE__*/_jsxDEV(Table, {\n          children: [/*#__PURE__*/_jsxDEV(TableHead, {\n            children: /*#__PURE__*/_jsxDEV(TableRow, {\n              sx: {\n                backgroundColor: 'grey.50'\n              },\n              children: [/*#__PURE__*/_jsxDEV(TableCell, {\n                sx: {\n                  fontWeight: 600\n                },\n                children: \"Codice\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 587,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n                sx: {\n                  fontWeight: 600\n                },\n                children: \"Tipo\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 588,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n                sx: {\n                  fontWeight: 600\n                },\n                children: \"Responsabile\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 589,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n                sx: {\n                  fontWeight: 600\n                },\n                children: \"Stato\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 590,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n                sx: {\n                  fontWeight: 600\n                },\n                children: \"Priorit\\xE0\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 591,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n                sx: {\n                  fontWeight: 600\n                },\n                children: \"Data Creazione\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 592,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n                sx: {\n                  fontWeight: 600\n                },\n                children: \"Azioni\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 593,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 586,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 585,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(TableBody, {\n            children: comande.length === 0 ? /*#__PURE__*/_jsxDEV(TableRow, {\n              children: /*#__PURE__*/_jsxDEV(TableCell, {\n                colSpan: 7,\n                align: \"center\",\n                sx: {\n                  py: 8\n                },\n                children: /*#__PURE__*/_jsxDEV(Box, {\n                  children: [/*#__PURE__*/_jsxDEV(AssignIcon, {\n                    sx: {\n                      fontSize: 48,\n                      color: 'grey.400',\n                      mb: 2\n                    }\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 601,\n                    columnNumber: 25\n                  }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                    variant: \"h6\",\n                    color: \"text.secondary\",\n                    gutterBottom: true,\n                    children: \"Nessuna comanda trovata\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 602,\n                    columnNumber: 25\n                  }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                    variant: \"body2\",\n                    color: \"text.secondary\",\n                    sx: {\n                      mb: 3\n                    },\n                    children: \"Crea la prima comanda per iniziare\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 605,\n                    columnNumber: 25\n                  }, this), /*#__PURE__*/_jsxDEV(Button, {\n                    variant: \"contained\",\n                    startIcon: /*#__PURE__*/_jsxDEV(AddIcon, {}, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 610,\n                      columnNumber: 38\n                    }, this),\n                    onClick: () => setOpenCreaConCavi(true),\n                    sx: {\n                      textTransform: 'none'\n                    },\n                    children: \"Crea Prima Comanda\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 608,\n                    columnNumber: 25\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 600,\n                  columnNumber: 23\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 599,\n                columnNumber: 21\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 598,\n              columnNumber: 19\n            }, this) : comande.map(comanda => /*#__PURE__*/_jsxDEV(TableRow, {\n              children: [/*#__PURE__*/_jsxDEV(TableCell, {\n                children: /*#__PURE__*/_jsxDEV(Typography, {\n                  variant: \"body2\",\n                  fontWeight: \"bold\",\n                  children: comanda.codice_comanda\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 623,\n                  columnNumber: 25\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 622,\n                columnNumber: 23\n              }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n                children: /*#__PURE__*/_jsxDEV(Chip, {\n                  label: getTipoComandaLabel(comanda.tipo_comanda),\n                  size: \"small\",\n                  variant: \"outlined\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 628,\n                  columnNumber: 25\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 627,\n                columnNumber: 23\n              }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n                children: /*#__PURE__*/_jsxDEV(Typography, {\n                  variant: \"body2\",\n                  children: comanda.responsabile || 'Non assegnato'\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 635,\n                  columnNumber: 25\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 634,\n                columnNumber: 23\n              }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n                children: /*#__PURE__*/_jsxDEV(Chip, {\n                  label: comanda.stato || 'CREATA',\n                  size: \"small\",\n                  color: getStatoColor(comanda.stato)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 640,\n                  columnNumber: 25\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 639,\n                columnNumber: 23\n              }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n                children: /*#__PURE__*/_jsxDEV(Chip, {\n                  label: comanda.priorita || 'NORMALE',\n                  size: \"small\",\n                  variant: \"outlined\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 647,\n                  columnNumber: 25\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 646,\n                columnNumber: 23\n              }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n                children: /*#__PURE__*/_jsxDEV(Typography, {\n                  variant: \"body2\",\n                  children: comanda.data_creazione ? new Date(comanda.data_creazione).toLocaleDateString() : '-'\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 654,\n                  columnNumber: 25\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 653,\n                columnNumber: 23\n              }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n                children: /*#__PURE__*/_jsxDEV(Box, {\n                  display: \"flex\",\n                  gap: 0.5,\n                  children: [/*#__PURE__*/_jsxDEV(Tooltip, {\n                    title: \"Visualizza\",\n                    children: /*#__PURE__*/_jsxDEV(IconButton, {\n                      size: \"small\",\n                      children: /*#__PURE__*/_jsxDEV(ViewIcon, {}, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 662,\n                        columnNumber: 31\n                      }, this)\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 661,\n                      columnNumber: 29\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 660,\n                    columnNumber: 27\n                  }, this), /*#__PURE__*/_jsxDEV(Tooltip, {\n                    title: \"Modifica\",\n                    children: /*#__PURE__*/_jsxDEV(IconButton, {\n                      size: \"small\",\n                      children: /*#__PURE__*/_jsxDEV(EditIcon, {}, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 667,\n                        columnNumber: 31\n                      }, this)\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 666,\n                      columnNumber: 29\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 665,\n                    columnNumber: 27\n                  }, this), /*#__PURE__*/_jsxDEV(Tooltip, {\n                    title: \"Elimina\",\n                    children: /*#__PURE__*/_jsxDEV(IconButton, {\n                      size: \"small\",\n                      color: \"error\",\n                      children: /*#__PURE__*/_jsxDEV(DeleteIcon, {}, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 672,\n                        columnNumber: 31\n                      }, this)\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 671,\n                      columnNumber: 29\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 670,\n                    columnNumber: 27\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 659,\n                  columnNumber: 25\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 658,\n                columnNumber: 23\n              }, this)]\n            }, comanda.codice_comanda, true, {\n              fileName: _jsxFileName,\n              lineNumber: 621,\n              columnNumber: 21\n            }, this))\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 596,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 584,\n          columnNumber: 13\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 583,\n        columnNumber: 11\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 492,\n      columnNumber: 9\n    }, this), /*#__PURE__*/_jsxDEV(Dialog, {\n      open: openResponsabileDialog,\n      onClose: handleCloseResponsabileDialog,\n      maxWidth: \"sm\",\n      fullWidth: true,\n      PaperProps: {\n        sx: {\n          borderRadius: 2\n        }\n      },\n      children: [/*#__PURE__*/_jsxDEV(DialogTitle, {\n        sx: {\n          pb: 1\n        },\n        children: /*#__PURE__*/_jsxDEV(Typography, {\n          variant: \"h6\",\n          sx: {\n            fontWeight: 600\n          },\n          children: dialogModeResponsabile === 'create' ? 'Inserisci Responsabile' : 'Modifica Responsabile'\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 697,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 696,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(DialogContent, {\n        children: /*#__PURE__*/_jsxDEV(Box, {\n          sx: {\n            pt: 2\n          },\n          children: [/*#__PURE__*/_jsxDEV(TextField, {\n            fullWidth: true,\n            label: \"Nome Responsabile\",\n            value: formDataResponsabile.nome_responsabile,\n            onChange: e => setFormDataResponsabile({\n              ...formDataResponsabile,\n              nome_responsabile: e.target.value\n            }),\n            margin: \"normal\",\n            required: true,\n            variant: \"outlined\",\n            sx: {\n              mb: 2\n            }\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 703,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(TextField, {\n            fullWidth: true,\n            label: \"Email\",\n            type: \"email\",\n            value: formDataResponsabile.email,\n            onChange: e => setFormDataResponsabile({\n              ...formDataResponsabile,\n              email: e.target.value\n            }),\n            margin: \"normal\",\n            variant: \"outlined\",\n            helperText: \"Email per notifiche (opzionale se inserisci telefono)\",\n            sx: {\n              mb: 2\n            }\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 714,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(TextField, {\n            fullWidth: true,\n            label: \"Telefono\",\n            value: formDataResponsabile.telefono,\n            onChange: e => setFormDataResponsabile({\n              ...formDataResponsabile,\n              telefono: e.target.value\n            }),\n            margin: \"normal\",\n            variant: \"outlined\",\n            helperText: \"Numero per SMS (opzionale se inserisci email)\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 726,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 702,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 701,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(DialogActions, {\n        sx: {\n          p: 3,\n          pt: 2\n        },\n        children: [/*#__PURE__*/_jsxDEV(Button, {\n          onClick: handleCloseResponsabileDialog,\n          sx: {\n            textTransform: 'none'\n          },\n          children: \"Annulla\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 738,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Button, {\n          onClick: handleSubmitResponsabile,\n          variant: \"contained\",\n          sx: {\n            textTransform: 'none',\n            fontWeight: 500,\n            px: 3\n          },\n          children: dialogModeResponsabile === 'create' ? 'Crea' : 'Salva'\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 744,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 737,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 687,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Dialog, {\n      open: deleteConfirmDialog.open,\n      onClose: handleCancelDelete,\n      maxWidth: \"sm\",\n      fullWidth: true,\n      PaperProps: {\n        sx: {\n          borderRadius: 2,\n          boxShadow: '0 8px 32px rgba(0,0,0,0.12)'\n        }\n      },\n      children: [/*#__PURE__*/_jsxDEV(DialogTitle, {\n        sx: {\n          pb: 1\n        },\n        children: /*#__PURE__*/_jsxDEV(Box, {\n          display: \"flex\",\n          alignItems: \"center\",\n          gap: 1,\n          children: [/*#__PURE__*/_jsxDEV(DeleteIcon, {\n            color: \"error\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 773,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Typography, {\n            variant: \"h6\",\n            component: \"span\",\n            children: \"Conferma Eliminazione\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 774,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 772,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 771,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(DialogContent, {\n        sx: {\n          pb: 2\n        },\n        children: [/*#__PURE__*/_jsxDEV(Typography, {\n          variant: \"body1\",\n          sx: {\n            mb: 1\n          },\n          children: \"Sei sicuro di voler eliminare il responsabile:\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 780,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Typography, {\n          variant: \"h6\",\n          color: \"primary\",\n          sx: {\n            fontWeight: 600\n          },\n          children: deleteConfirmDialog.responsabileName\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 783,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Typography, {\n          variant: \"body2\",\n          color: \"text.secondary\",\n          sx: {\n            mt: 2\n          },\n          children: \"Questa operazione non pu\\xF2 essere annullata.\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 786,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 779,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(DialogActions, {\n        sx: {\n          px: 3,\n          pb: 3\n        },\n        children: [/*#__PURE__*/_jsxDEV(Button, {\n          onClick: handleCancelDelete,\n          variant: \"outlined\",\n          sx: {\n            textTransform: 'none',\n            fontWeight: 500,\n            px: 3\n          },\n          children: \"Annulla\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 791,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Button, {\n          onClick: handleConfirmDelete,\n          variant: \"contained\",\n          color: \"error\",\n          sx: {\n            textTransform: 'none',\n            fontWeight: 500,\n            px: 3\n          },\n          children: \"Elimina\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 802,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 790,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 759,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(CreaComandaConCavi, {\n      cantiereId: cantiereId,\n      open: openCreaConCavi,\n      onClose: () => setOpenCreaConCavi(false),\n      onSuccess: () => {\n        loadComande();\n        loadStatistiche();\n        loadResponsabili();\n        setOpenCreaConCavi(false);\n      }\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 818,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 257,\n    columnNumber: 5\n  }, this);\n};\n_s(ComandeListRivoluzionato, \"UuXOoQvO8LZx4nxi7EYgVtGGbQI=\");\n_c = ComandeListRivoluzionato;\nexport default ComandeListRivoluzionato;\nvar _c;\n$RefreshReg$(_c, \"ComandeListRivoluzionato\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "Box", "Card", "<PERSON><PERSON><PERSON><PERSON>", "Typography", "<PERSON><PERSON>", "Table", "TableBody", "TableCell", "TableContainer", "TableHead", "TableRow", "Paper", "Chip", "IconButton", "Dialog", "DialogTitle", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "DialogActions", "TextField", "<PERSON><PERSON>", "CircularProgress", "<PERSON><PERSON><PERSON>", "Grid", "List", "ListItem", "ListItemText", "Tabs", "Tab", "Accordion", "AccordionSummary", "AccordionDetails", "Add", "AddIcon", "Edit", "EditIcon", "Delete", "DeleteIcon", "Visibility", "ViewIcon", "Assignment", "AssignIcon", "Refresh", "RefreshIcon", "Person", "PersonIcon", "Email", "EmailIcon", "Phone", "PhoneIcon", "ExpandMore", "ExpandMoreIcon", "comandeService", "responsabiliService", "CreaComandaConCavi", "jsxDEV", "_jsxDEV", "ComandeListRivoluzionato", "cantiereId", "cantiereName", "_s", "activeTab", "setActiveTab", "loading", "setLoading", "error", "setError", "comande", "setComande", "statistiche", "setStatistiche", "openCreaConCavi", "setOpenCreaConCavi", "responsabili", "setResponsabili", "loadingResponsabili", "setLoadingResponsabili", "comandePerResponsabile", "setComandePerResponsabile", "openResponsabileDialog", "setOpenResponsabileDialog", "dialogModeResponsabile", "setDialogModeResponsabile", "selectedResponsabile", "setSelectedResponsabile", "formDataResponsabile", "setFormDataResponsabile", "nome_responsabile", "email", "telefono", "deleteConfirmDialog", "setDeleteConfirmDialog", "open", "responsabileId", "responsabileName", "loadComande", "loadStatistiche", "loadResponsabili", "data", "getComande", "err", "console", "stats", "getStatisticheComande", "getResponsabiliCantiere", "loadComandePerResponsabili", "_err$response", "_err$response$data", "errorMessage", "response", "detail", "message", "responsabiliList", "comandeMap", "responsabile", "getComandeByResponsabile", "Array", "isArray", "id_responsabile", "handleOpenResponsabileDialog", "mode", "handleCloseResponsabileDialog", "handleSubmitResponsabile", "trim", "createResponsabile", "updateResponsabile", "handleDeleteResponsabile", "idResponsabile", "window", "confirm", "deleteResponsabile", "getTipoComandaLabel", "tipo", "labels", "getStatoColor", "stato", "colors", "display", "justifyContent", "alignItems", "minHeight", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "severity", "sx", "mb", "borderBottom", "borderColor", "value", "onChange", "e", "newValue", "textTransform", "fontWeight", "fontSize", "label", "variant", "color", "startIcon", "onClick", "px", "py", "length", "elevation", "p", "textAlign", "backgroundColor", "border", "gutterBottom", "map", "boxShadow", "expandIcon", "width", "gap", "mt", "stopPropagation", "icon", "size", "title", "pt", "borderRadius", "dense", "comanda", "divider", "primary", "codice_comanda", "tipo_comanda", "secondary", "descrizione", "data_creazione", "Date", "toLocaleDateString", "container", "spacing", "item", "xs", "sm", "md", "totale_comande", "comande_create", "comande_in_corso", "comande_completate", "component", "colSpan", "align", "priorita", "onClose", "max<PERSON><PERSON><PERSON>", "fullWidth", "PaperProps", "pb", "target", "margin", "required", "type", "helperText", "handleCancelDelete", "handleConfirmDelete", "onSuccess", "_c", "$RefreshReg$"], "sources": ["C:/CMS/webapp/frontend/src/components/comande/ComandeListRivoluzionato.js"], "sourcesContent": ["import React, { useState, useEffect } from 'react';\nimport {\n  Box,\n  Card,\n  CardContent,\n  Typography,\n  Button,\n  Table,\n  TableBody,\n  TableCell,\n  TableContainer,\n  TableHead,\n  TableRow,\n  Paper,\n  Chip,\n  IconButton,\n  Dialog,\n  DialogTitle,\n  DialogContent,\n  DialogActions,\n  TextField,\n  Alert,\n  CircularProgress,\n  Tooltip,\n  Grid,\n  List,\n  ListItem,\n  ListItemText,\n  Tabs,\n  Tab,\n  Accordion,\n  AccordionSummary,\n  AccordionDetails\n} from '@mui/material';\nimport {\n  Add as AddIcon,\n  Edit as EditIcon,\n  Delete as DeleteIcon,\n  Visibility as ViewIcon,\n  Assignment as AssignIcon,\n  Refresh as RefreshIcon,\n  Person as PersonIcon,\n  Email as EmailIcon,\n  Phone as PhoneIcon,\n  ExpandMore as ExpandMoreIcon\n} from '@mui/icons-material';\nimport comandeService from '../../services/comandeService';\nimport responsabiliService from '../../services/responsabiliService';\nimport CreaComandaConCavi from './CreaComandaConCavi';\n\nconst ComandeListRivoluzionato = ({ cantiereId, cantiereName }) => {\n  // Stati principali\n  const [activeTab, setActiveTab] = useState(0);\n  const [loading, setLoading] = useState(true);\n  const [error, setError] = useState(null);\n\n  // Stati comande\n  const [comande, setComande] = useState([]);\n  const [statistiche, setStatistiche] = useState(null);\n  const [openCreaConCavi, setOpenCreaConCavi] = useState(false);\n\n  // Stati responsabili\n  const [responsabili, setResponsabili] = useState([]);\n  const [loadingResponsabili, setLoadingResponsabili] = useState(false);\n  const [comandePerResponsabile, setComandePerResponsabile] = useState({});\n  const [openResponsabileDialog, setOpenResponsabileDialog] = useState(false);\n  const [dialogModeResponsabile, setDialogModeResponsabile] = useState('create');\n  const [selectedResponsabile, setSelectedResponsabile] = useState(null);\n  const [formDataResponsabile, setFormDataResponsabile] = useState({\n    nome_responsabile: '',\n    email: '',\n    telefono: ''\n  });\n\n  // Stato per il dialog di conferma eliminazione\n  const [deleteConfirmDialog, setDeleteConfirmDialog] = useState({\n    open: false,\n    responsabileId: null,\n    responsabileName: ''\n  });\n\n  // Carica dati al mount\n  useEffect(() => {\n    if (cantiereId) {\n      loadComande();\n      loadStatistiche();\n      loadResponsabili();\n    }\n  }, [cantiereId]);\n\n  const loadComande = async () => {\n    try {\n      setLoading(true);\n      const data = await comandeService.getComande(cantiereId);\n      setComande(data.comande || []);\n      setError(null);\n    } catch (err) {\n      console.error('Errore nel caricamento delle comande:', err);\n      setError('Errore nel caricamento delle comande');\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  const loadStatistiche = async () => {\n    try {\n      const stats = await comandeService.getStatisticheComande(cantiereId);\n      setStatistiche(stats);\n    } catch (err) {\n      console.error('Errore nel caricamento delle statistiche:', err);\n    }\n  };\n\n  const loadResponsabili = async () => {\n    try {\n      setLoadingResponsabili(true);\n      setError(null);\n\n      const data = await responsabiliService.getResponsabiliCantiere(cantiereId);\n      setResponsabili(data || []);\n      await loadComandePerResponsabili(data || []);\n    } catch (err) {\n      console.error('Errore nel caricamento dei responsabili:', err);\n      const errorMessage = err.response?.data?.detail || err.message || 'Errore nel caricamento dei responsabili';\n      setError(`Errore nel caricamento dei responsabili: ${errorMessage}`);\n    } finally {\n      setLoadingResponsabili(false);\n    }\n  };\n\n  const loadComandePerResponsabili = async (responsabiliList) => {\n    try {\n      const comandeMap = {};\n      for (const responsabile of responsabiliList) {\n        try {\n          const response = await comandeService.getComandeByResponsabile(cantiereId, responsabile.nome_responsabile);\n          // Assicurati che sia sempre un array\n          let comande = [];\n          if (response && Array.isArray(response)) {\n            comande = response;\n          } else if (response && response.comande && Array.isArray(response.comande)) {\n            comande = response.comande;\n          } else if (response && response.data && Array.isArray(response.data)) {\n            comande = response.data;\n          }\n          comandeMap[responsabile.id_responsabile] = comande;\n        } catch (err) {\n          console.error(`Errore nel caricamento comande per ${responsabile.nome_responsabile}:`, err);\n          comandeMap[responsabile.id_responsabile] = [];\n        }\n      }\n      setComandePerResponsabile(comandeMap);\n    } catch (err) {\n      console.error('Errore nel caricamento delle comande:', err);\n    }\n  };\n\n  // Gestione responsabili\n  const handleOpenResponsabileDialog = (mode, responsabile = null) => {\n    setDialogModeResponsabile(mode);\n    setSelectedResponsabile(responsabile);\n    \n    if (mode === 'edit' && responsabile) {\n      setFormDataResponsabile({\n        nome_responsabile: responsabile.nome_responsabile || '',\n        email: responsabile.email || '',\n        telefono: responsabile.telefono || ''\n      });\n    } else {\n      setFormDataResponsabile({\n        nome_responsabile: '',\n        email: '',\n        telefono: ''\n      });\n    }\n    \n    setOpenResponsabileDialog(true);\n  };\n\n  const handleCloseResponsabileDialog = () => {\n    setOpenResponsabileDialog(false);\n    setSelectedResponsabile(null);\n    setError(null);\n  };\n\n  const handleSubmitResponsabile = async () => {\n    try {\n      setError(null);\n      \n      if (!formDataResponsabile.nome_responsabile.trim()) {\n        setError('Il nome del responsabile è obbligatorio');\n        return;\n      }\n      \n      if (!formDataResponsabile.email && !formDataResponsabile.telefono) {\n        setError('Almeno uno tra email e telefono deve essere specificato');\n        return;\n      }\n\n      if (dialogModeResponsabile === 'create') {\n        await responsabiliService.createResponsabile(cantiereId, formDataResponsabile);\n      } else if (dialogModeResponsabile === 'edit') {\n        await responsabiliService.updateResponsabile(selectedResponsabile.id_responsabile, formDataResponsabile);\n      }\n\n      handleCloseResponsabileDialog();\n      await loadResponsabili();\n    } catch (err) {\n      console.error('Errore nel salvataggio:', err);\n      setError(err.detail || 'Errore nel salvataggio del responsabile');\n    }\n  };\n\n  const handleDeleteResponsabile = async (idResponsabile) => {\n    if (!window.confirm('Sei sicuro di voler eliminare questo responsabile?')) {\n      return;\n    }\n\n    try {\n      await responsabiliService.deleteResponsabile(idResponsabile);\n      await loadResponsabili();\n    } catch (err) {\n      console.error('Errore nell\\'eliminazione:', err);\n      setError('Errore nell\\'eliminazione del responsabile');\n    }\n  };\n\n  const getTipoComandaLabel = (tipo) => {\n    const labels = {\n      'POSA': 'Posa',\n      'COLLEGAMENTO_PARTENZA': 'Coll. Partenza',\n      'COLLEGAMENTO_ARRIVO': 'Coll. Arrivo',\n      'CERTIFICAZIONE': 'Certificazione'\n    };\n    return labels[tipo] || tipo;\n  };\n\n  const getStatoColor = (stato) => {\n    const colors = {\n      'CREATA': 'default',\n      'IN_CORSO': 'primary',\n      'COMPLETATA': 'success',\n      'ANNULLATA': 'error'\n    };\n    return colors[stato] || 'default';\n  };\n\n  if (loading) {\n    return (\n      <Box display=\"flex\" justifyContent=\"center\" alignItems=\"center\" minHeight=\"400px\">\n        <CircularProgress />\n      </Box>\n    );\n  }\n\n  return (\n    <Box>\n\n      {error && (\n        <Alert severity=\"error\" sx={{ mb: 2 }}>\n          {error}\n        </Alert>\n      )}\n\n      {/* Tabs per navigazione */}\n      <Box sx={{ borderBottom: 1, borderColor: 'divider', mb: 4 }}>\n        <Tabs\n          value={activeTab}\n          onChange={(e, newValue) => setActiveTab(newValue)}\n          sx={{\n            '& .MuiTab-root': {\n              textTransform: 'none',\n              fontWeight: 500,\n              fontSize: '1rem'\n            }\n          }}\n        >\n          <Tab label=\"Responsabili\" />\n          <Tab label=\"Tutte le Comande\" />\n        </Tabs>\n      </Box>\n\n      {/* Tab 0: Responsabili */}\n      {activeTab === 0 && (\n        <Box>\n          {/* Toolbar Responsabili */}\n          <Box display=\"flex\" justifyContent=\"space-between\" alignItems=\"center\" mb={3}>\n            <Typography variant=\"h5\" sx={{ fontWeight: 500, color: 'text.primary' }}>\n              Responsabili del Cantiere\n            </Typography>\n            <Button\n              variant=\"contained\"\n              startIcon={<AddIcon />}\n              onClick={() => handleOpenResponsabileDialog('create')}\n              sx={{\n                textTransform: 'none',\n                fontWeight: 500,\n                px: 3,\n                py: 1\n              }}\n            >\n              Inserisci Responsabile\n            </Button>\n          </Box>\n\n          {loadingResponsabili ? (\n            <Box display=\"flex\" justifyContent=\"center\" py={4}>\n              <CircularProgress />\n            </Box>\n          ) : (\n            <Box>\n              {responsabili.length === 0 ? (\n                <Paper\n                  elevation={0}\n                  sx={{\n                    p: 6,\n                    textAlign: 'center',\n                    backgroundColor: 'grey.50',\n                    border: '1px dashed',\n                    borderColor: 'grey.300'\n                  }}\n                >\n                  <PersonIcon sx={{ fontSize: 48, color: 'grey.400', mb: 2 }} />\n                  <Typography variant=\"h6\" color=\"text.secondary\" gutterBottom>\n                    Nessun responsabile configurato\n                  </Typography>\n                  <Typography variant=\"body2\" color=\"text.secondary\" sx={{ mb: 3 }}>\n                    Aggiungi il primo responsabile per iniziare a gestire le comande\n                  </Typography>\n                  <Button\n                    variant=\"contained\"\n                    startIcon={<AddIcon />}\n                    onClick={() => handleOpenResponsabileDialog('create')}\n                    sx={{ textTransform: 'none' }}\n                  >\n                    Inserisci Primo Responsabile\n                  </Button>\n                </Paper>\n              ) : (\n                responsabili.map((responsabile) => (\n                  <Accordion\n                    key={responsabile.id_responsabile}\n                    sx={{\n                      mb: 2,\n                      '&:before': { display: 'none' },\n                      boxShadow: '0 1px 3px rgba(0,0,0,0.1)',\n                      border: '1px solid',\n                      borderColor: 'grey.200'\n                    }}\n                  >\n                    <AccordionSummary\n                      expandIcon={<ExpandMoreIcon />}\n                      sx={{\n                        '&:hover': {\n                          backgroundColor: 'grey.50'\n                        }\n                      }}\n                    >\n                      <Box display=\"flex\" alignItems=\"center\" justifyContent=\"space-between\" width=\"100%\">\n                        <Box display=\"flex\" alignItems=\"center\" gap={2}>\n                          <PersonIcon color=\"primary\" sx={{ fontSize: 28 }} />\n                          <Box>\n                            <Typography variant=\"h6\" sx={{ fontWeight: 500 }}>\n                              {responsabile.nome_responsabile}\n                            </Typography>\n                            <Box display=\"flex\" gap={3} mt={0.5}>\n                              {responsabile.email && (\n                                <Box display=\"flex\" alignItems=\"center\" gap={0.5}>\n                                  <EmailIcon fontSize=\"small\" color=\"action\" />\n                                  <Typography variant=\"body2\" color=\"text.secondary\">\n                                    {responsabile.email}\n                                  </Typography>\n                                </Box>\n                              )}\n                              {responsabile.telefono && (\n                                <Box display=\"flex\" alignItems=\"center\" gap={0.5}>\n                                  <PhoneIcon fontSize=\"small\" color=\"action\" />\n                                  <Typography variant=\"body2\" color=\"text.secondary\">\n                                    {responsabile.telefono}\n                                  </Typography>\n                                </Box>\n                              )}\n                            </Box>\n                          </Box>\n                        </Box>\n\n                        <Box display=\"flex\" alignItems=\"center\" gap={1} onClick={(e) => e.stopPropagation()}>\n                          <Chip\n                            icon={<AssignIcon />}\n                            label={`${Array.isArray(comandePerResponsabile[responsabile.id_responsabile]) ? comandePerResponsabile[responsabile.id_responsabile].length : 0} comande`}\n                            size=\"small\"\n                            color=\"primary\"\n                            variant=\"outlined\"\n                            sx={{ fontWeight: 500 }}\n                          />\n                          <Tooltip title=\"Modifica responsabile\">\n                            <IconButton\n                              size=\"small\"\n                              onClick={() => handleOpenResponsabileDialog('edit', responsabile)}\n                              sx={{\n                                '&:hover': {\n                                  backgroundColor: 'primary.light',\n                                  color: 'white'\n                                }\n                              }}\n                            >\n                              <EditIcon fontSize=\"small\" />\n                            </IconButton>\n                          </Tooltip>\n                          <Tooltip title=\"Elimina responsabile\">\n                            <IconButton\n                              size=\"small\"\n                              onClick={() => handleDeleteResponsabile(responsabile.id_responsabile)}\n                              sx={{\n                                '&:hover': {\n                                  backgroundColor: 'error.light',\n                                  color: 'white'\n                                }\n                              }}\n                            >\n                              <DeleteIcon fontSize=\"small\" />\n                            </IconButton>\n                          </Tooltip>\n                        </Box>\n                      </Box>\n                    </AccordionSummary>\n\n                    <AccordionDetails sx={{ pt: 2 }}>\n                      <Typography variant=\"subtitle2\" gutterBottom sx={{ fontWeight: 500, mb: 2 }}>\n                        Comande Assegnate\n                      </Typography>\n\n                      {(!Array.isArray(comandePerResponsabile[responsabile.id_responsabile]) || comandePerResponsabile[responsabile.id_responsabile].length === 0) ? (\n                        <Box\n                          sx={{\n                            p: 3,\n                            textAlign: 'center',\n                            backgroundColor: 'grey.50',\n                            borderRadius: 1,\n                            border: '1px dashed',\n                            borderColor: 'grey.300'\n                          }}\n                        >\n                          <Typography variant=\"body2\" color=\"text.secondary\">\n                            Nessuna comanda assegnata a questo responsabile\n                          </Typography>\n                        </Box>\n                      ) : (\n                        <List dense>\n                          {Array.isArray(comandePerResponsabile[responsabile.id_responsabile]) && comandePerResponsabile[responsabile.id_responsabile].map((comanda) => (\n                            <ListItem key={comanda.codice_comanda} divider>\n                              <ListItemText\n                                primary={\n                                  <Box display=\"flex\" alignItems=\"center\" gap={1}>\n                                    <Typography variant=\"body2\" fontWeight=\"bold\">\n                                      {comanda.codice_comanda}\n                                    </Typography>\n                                    <Chip\n                                      label={getTipoComandaLabel(comanda.tipo_comanda)}\n                                      size=\"small\"\n                                      variant=\"outlined\"\n                                    />\n                                    <Chip\n                                      label={comanda.stato || 'CREATA'}\n                                      size=\"small\"\n                                      color={getStatoColor(comanda.stato)}\n                                    />\n                                  </Box>\n                                }\n                                secondary={\n                                  <Typography variant=\"body2\" color=\"textSecondary\">\n                                    {comanda.descrizione || 'Nessuna descrizione'}\n                                    {comanda.data_creazione && ` • Creata: ${new Date(comanda.data_creazione).toLocaleDateString()}`}\n                                  </Typography>\n                                }\n                              />\n                            </ListItem>\n                          ))}\n                        </List>\n                      )}\n                    </AccordionDetails>\n                  </Accordion>\n                ))\n              )}\n            </Box>\n          )}\n        </Box>\n      )}\n\n      {/* Tab 1: Tutte le Comande */}\n      {activeTab === 1 && (\n        <Box>\n          {/* Statistiche */}\n          {statistiche && (\n            <Grid container spacing={3} sx={{ mb: 4 }}>\n              <Grid item xs={12} sm={6} md={3}>\n                <Card sx={{ boxShadow: '0 1px 3px rgba(0,0,0,0.1)', border: '1px solid', borderColor: 'grey.200' }}>\n                  <CardContent sx={{ textAlign: 'center', py: 3 }}>\n                    <Typography color=\"text.secondary\" variant=\"body2\" sx={{ fontWeight: 500, mb: 1 }}>\n                      Totale\n                    </Typography>\n                    <Typography variant=\"h4\" sx={{ fontWeight: 600, color: 'text.primary' }}>\n                      {statistiche.totale_comande}\n                    </Typography>\n                  </CardContent>\n                </Card>\n              </Grid>\n              <Grid item xs={12} sm={6} md={3}>\n                <Card sx={{ boxShadow: '0 1px 3px rgba(0,0,0,0.1)', border: '1px solid', borderColor: 'grey.200' }}>\n                  <CardContent sx={{ textAlign: 'center', py: 3 }}>\n                    <Typography color=\"text.secondary\" variant=\"body2\" sx={{ fontWeight: 500, mb: 1 }}>\n                      Create\n                    </Typography>\n                    <Typography variant=\"h4\" sx={{ fontWeight: 600, color: 'primary.main' }}>\n                      {statistiche.comande_create}\n                    </Typography>\n                  </CardContent>\n                </Card>\n              </Grid>\n              <Grid item xs={12} sm={6} md={3}>\n                <Card sx={{ boxShadow: '0 1px 3px rgba(0,0,0,0.1)', border: '1px solid', borderColor: 'grey.200' }}>\n                  <CardContent sx={{ textAlign: 'center', py: 3 }}>\n                    <Typography color=\"text.secondary\" variant=\"body2\" sx={{ fontWeight: 500, mb: 1 }}>\n                      In Corso\n                    </Typography>\n                    <Typography variant=\"h4\" sx={{ fontWeight: 600, color: 'warning.main' }}>\n                      {statistiche.comande_in_corso}\n                    </Typography>\n                  </CardContent>\n                </Card>\n              </Grid>\n              <Grid item xs={12} sm={6} md={3}>\n                <Card sx={{ boxShadow: '0 1px 3px rgba(0,0,0,0.1)', border: '1px solid', borderColor: 'grey.200' }}>\n                  <CardContent sx={{ textAlign: 'center', py: 3 }}>\n                    <Typography color=\"text.secondary\" variant=\"body2\" sx={{ fontWeight: 500, mb: 1 }}>\n                      Completate\n                    </Typography>\n                    <Typography variant=\"h4\" sx={{ fontWeight: 600, color: 'success.main' }}>\n                      {statistiche.comande_completate}\n                    </Typography>\n                  </CardContent>\n                </Card>\n              </Grid>\n            </Grid>\n          )}\n\n          {/* Toolbar Comande */}\n          <Box display=\"flex\" justifyContent=\"space-between\" alignItems=\"center\" mb={3}>\n            <Typography variant=\"h5\" sx={{ fontWeight: 500, color: 'text.primary' }}>\n              Tutte le Comande\n            </Typography>\n            <Box display=\"flex\" gap={2}>\n              <Button\n                variant=\"contained\"\n                startIcon={<AddIcon />}\n                onClick={() => setOpenCreaConCavi(true)}\n                sx={{\n                  textTransform: 'none',\n                  fontWeight: 500,\n                  px: 3\n                }}\n              >\n                Nuova Comanda\n              </Button>\n              <Button\n                variant=\"outlined\"\n                startIcon={<RefreshIcon />}\n                onClick={() => {\n                  loadComande();\n                  loadStatistiche();\n                }}\n                sx={{\n                  textTransform: 'none',\n                  fontWeight: 500\n                }}\n              >\n                Aggiorna\n              </Button>\n            </Box>\n          </Box>\n\n          {/* Tabella Comande */}\n          <TableContainer component={Paper} sx={{ boxShadow: '0 1px 3px rgba(0,0,0,0.1)' }}>\n            <Table>\n              <TableHead>\n                <TableRow sx={{ backgroundColor: 'grey.50' }}>\n                  <TableCell sx={{ fontWeight: 600 }}>Codice</TableCell>\n                  <TableCell sx={{ fontWeight: 600 }}>Tipo</TableCell>\n                  <TableCell sx={{ fontWeight: 600 }}>Responsabile</TableCell>\n                  <TableCell sx={{ fontWeight: 600 }}>Stato</TableCell>\n                  <TableCell sx={{ fontWeight: 600 }}>Priorità</TableCell>\n                  <TableCell sx={{ fontWeight: 600 }}>Data Creazione</TableCell>\n                  <TableCell sx={{ fontWeight: 600 }}>Azioni</TableCell>\n                </TableRow>\n              </TableHead>\n              <TableBody>\n                {comande.length === 0 ? (\n                  <TableRow>\n                    <TableCell colSpan={7} align=\"center\" sx={{ py: 8 }}>\n                      <Box>\n                        <AssignIcon sx={{ fontSize: 48, color: 'grey.400', mb: 2 }} />\n                        <Typography variant=\"h6\" color=\"text.secondary\" gutterBottom>\n                          Nessuna comanda trovata\n                        </Typography>\n                        <Typography variant=\"body2\" color=\"text.secondary\" sx={{ mb: 3 }}>\n                          Crea la prima comanda per iniziare\n                        </Typography>\n                        <Button\n                          variant=\"contained\"\n                          startIcon={<AddIcon />}\n                          onClick={() => setOpenCreaConCavi(true)}\n                          sx={{ textTransform: 'none' }}\n                        >\n                          Crea Prima Comanda\n                        </Button>\n                      </Box>\n                    </TableCell>\n                  </TableRow>\n                ) : (\n                  comande.map((comanda) => (\n                    <TableRow key={comanda.codice_comanda}>\n                      <TableCell>\n                        <Typography variant=\"body2\" fontWeight=\"bold\">\n                          {comanda.codice_comanda}\n                        </Typography>\n                      </TableCell>\n                      <TableCell>\n                        <Chip\n                          label={getTipoComandaLabel(comanda.tipo_comanda)}\n                          size=\"small\"\n                          variant=\"outlined\"\n                        />\n                      </TableCell>\n                      <TableCell>\n                        <Typography variant=\"body2\">\n                          {comanda.responsabile || 'Non assegnato'}\n                        </Typography>\n                      </TableCell>\n                      <TableCell>\n                        <Chip\n                          label={comanda.stato || 'CREATA'}\n                          size=\"small\"\n                          color={getStatoColor(comanda.stato)}\n                        />\n                      </TableCell>\n                      <TableCell>\n                        <Chip\n                          label={comanda.priorita || 'NORMALE'}\n                          size=\"small\"\n                          variant=\"outlined\"\n                        />\n                      </TableCell>\n                      <TableCell>\n                        <Typography variant=\"body2\">\n                          {comanda.data_creazione ? new Date(comanda.data_creazione).toLocaleDateString() : '-'}\n                        </Typography>\n                      </TableCell>\n                      <TableCell>\n                        <Box display=\"flex\" gap={0.5}>\n                          <Tooltip title=\"Visualizza\">\n                            <IconButton size=\"small\">\n                              <ViewIcon />\n                            </IconButton>\n                          </Tooltip>\n                          <Tooltip title=\"Modifica\">\n                            <IconButton size=\"small\">\n                              <EditIcon />\n                            </IconButton>\n                          </Tooltip>\n                          <Tooltip title=\"Elimina\">\n                            <IconButton size=\"small\" color=\"error\">\n                              <DeleteIcon />\n                            </IconButton>\n                          </Tooltip>\n                        </Box>\n                      </TableCell>\n                    </TableRow>\n                  ))\n                )}\n              </TableBody>\n            </Table>\n          </TableContainer>\n        </Box>\n      )}\n\n      {/* Dialog per creazione/modifica responsabile */}\n      <Dialog\n        open={openResponsabileDialog}\n        onClose={handleCloseResponsabileDialog}\n        maxWidth=\"sm\"\n        fullWidth\n        PaperProps={{\n          sx: { borderRadius: 2 }\n        }}\n      >\n        <DialogTitle sx={{ pb: 1 }}>\n          <Typography variant=\"h6\" sx={{ fontWeight: 600 }}>\n            {dialogModeResponsabile === 'create' ? 'Inserisci Responsabile' : 'Modifica Responsabile'}\n          </Typography>\n        </DialogTitle>\n        <DialogContent>\n          <Box sx={{ pt: 2 }}>\n            <TextField\n              fullWidth\n              label=\"Nome Responsabile\"\n              value={formDataResponsabile.nome_responsabile}\n              onChange={(e) => setFormDataResponsabile({ ...formDataResponsabile, nome_responsabile: e.target.value })}\n              margin=\"normal\"\n              required\n              variant=\"outlined\"\n              sx={{ mb: 2 }}\n            />\n\n            <TextField\n              fullWidth\n              label=\"Email\"\n              type=\"email\"\n              value={formDataResponsabile.email}\n              onChange={(e) => setFormDataResponsabile({ ...formDataResponsabile, email: e.target.value })}\n              margin=\"normal\"\n              variant=\"outlined\"\n              helperText=\"Email per notifiche (opzionale se inserisci telefono)\"\n              sx={{ mb: 2 }}\n            />\n\n            <TextField\n              fullWidth\n              label=\"Telefono\"\n              value={formDataResponsabile.telefono}\n              onChange={(e) => setFormDataResponsabile({ ...formDataResponsabile, telefono: e.target.value })}\n              margin=\"normal\"\n              variant=\"outlined\"\n              helperText=\"Numero per SMS (opzionale se inserisci email)\"\n            />\n          </Box>\n        </DialogContent>\n        <DialogActions sx={{ p: 3, pt: 2 }}>\n          <Button\n            onClick={handleCloseResponsabileDialog}\n            sx={{ textTransform: 'none' }}\n          >\n            Annulla\n          </Button>\n          <Button\n            onClick={handleSubmitResponsabile}\n            variant=\"contained\"\n            sx={{\n              textTransform: 'none',\n              fontWeight: 500,\n              px: 3\n            }}\n          >\n            {dialogModeResponsabile === 'create' ? 'Crea' : 'Salva'}\n          </Button>\n        </DialogActions>\n      </Dialog>\n\n      {/* Dialog di conferma eliminazione responsabile */}\n      <Dialog\n        open={deleteConfirmDialog.open}\n        onClose={handleCancelDelete}\n        maxWidth=\"sm\"\n        fullWidth\n        PaperProps={{\n          sx: {\n            borderRadius: 2,\n            boxShadow: '0 8px 32px rgba(0,0,0,0.12)'\n          }\n        }}\n      >\n        <DialogTitle sx={{ pb: 1 }}>\n          <Box display=\"flex\" alignItems=\"center\" gap={1}>\n            <DeleteIcon color=\"error\" />\n            <Typography variant=\"h6\" component=\"span\">\n              Conferma Eliminazione\n            </Typography>\n          </Box>\n        </DialogTitle>\n        <DialogContent sx={{ pb: 2 }}>\n          <Typography variant=\"body1\" sx={{ mb: 1 }}>\n            Sei sicuro di voler eliminare il responsabile:\n          </Typography>\n          <Typography variant=\"h6\" color=\"primary\" sx={{ fontWeight: 600 }}>\n            {deleteConfirmDialog.responsabileName}\n          </Typography>\n          <Typography variant=\"body2\" color=\"text.secondary\" sx={{ mt: 2 }}>\n            Questa operazione non può essere annullata.\n          </Typography>\n        </DialogContent>\n        <DialogActions sx={{ px: 3, pb: 3 }}>\n          <Button\n            onClick={handleCancelDelete}\n            variant=\"outlined\"\n            sx={{\n              textTransform: 'none',\n              fontWeight: 500,\n              px: 3\n            }}\n          >\n            Annulla\n          </Button>\n          <Button\n            onClick={handleConfirmDelete}\n            variant=\"contained\"\n            color=\"error\"\n            sx={{\n              textTransform: 'none',\n              fontWeight: 500,\n              px: 3\n            }}\n          >\n            Elimina\n          </Button>\n        </DialogActions>\n      </Dialog>\n\n      {/* Dialog CreaComandaConCavi */}\n      <CreaComandaConCavi\n        cantiereId={cantiereId}\n        open={openCreaConCavi}\n        onClose={() => setOpenCreaConCavi(false)}\n        onSuccess={() => {\n          loadComande();\n          loadStatistiche();\n          loadResponsabili();\n          setOpenCreaConCavi(false);\n        }}\n      />\n    </Box>\n  );\n};\n\nexport default ComandeListRivoluzionato;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAClD,SACEC,GAAG,EACHC,IAAI,EACJC,WAAW,EACXC,UAAU,EACVC,MAAM,EACNC,KAAK,EACLC,SAAS,EACTC,SAAS,EACTC,cAAc,EACdC,SAAS,EACTC,QAAQ,EACRC,KAAK,EACLC,IAAI,EACJC,UAAU,EACVC,MAAM,EACNC,WAAW,EACXC,aAAa,EACbC,aAAa,EACbC,SAAS,EACTC,KAAK,EACLC,gBAAgB,EAChBC,OAAO,EACPC,IAAI,EACJC,IAAI,EACJC,QAAQ,EACRC,YAAY,EACZC,IAAI,EACJC,GAAG,EACHC,SAAS,EACTC,gBAAgB,EAChBC,gBAAgB,QACX,eAAe;AACtB,SACEC,GAAG,IAAIC,OAAO,EACdC,IAAI,IAAIC,QAAQ,EAChBC,MAAM,IAAIC,UAAU,EACpBC,UAAU,IAAIC,QAAQ,EACtBC,UAAU,IAAIC,UAAU,EACxBC,OAAO,IAAIC,WAAW,EACtBC,MAAM,IAAIC,UAAU,EACpBC,KAAK,IAAIC,SAAS,EAClBC,KAAK,IAAIC,SAAS,EAClBC,UAAU,IAAIC,cAAc,QACvB,qBAAqB;AAC5B,OAAOC,cAAc,MAAM,+BAA+B;AAC1D,OAAOC,mBAAmB,MAAM,oCAAoC;AACpE,OAAOC,kBAAkB,MAAM,sBAAsB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAEtD,MAAMC,wBAAwB,GAAGA,CAAC;EAAEC,UAAU;EAAEC;AAAa,CAAC,KAAK;EAAAC,EAAA;EACjE;EACA,MAAM,CAACC,SAAS,EAAEC,YAAY,CAAC,GAAG/D,QAAQ,CAAC,CAAC,CAAC;EAC7C,MAAM,CAACgE,OAAO,EAAEC,UAAU,CAAC,GAAGjE,QAAQ,CAAC,IAAI,CAAC;EAC5C,MAAM,CAACkE,KAAK,EAAEC,QAAQ,CAAC,GAAGnE,QAAQ,CAAC,IAAI,CAAC;;EAExC;EACA,MAAM,CAACoE,OAAO,EAAEC,UAAU,CAAC,GAAGrE,QAAQ,CAAC,EAAE,CAAC;EAC1C,MAAM,CAACsE,WAAW,EAAEC,cAAc,CAAC,GAAGvE,QAAQ,CAAC,IAAI,CAAC;EACpD,MAAM,CAACwE,eAAe,EAAEC,kBAAkB,CAAC,GAAGzE,QAAQ,CAAC,KAAK,CAAC;;EAE7D;EACA,MAAM,CAAC0E,YAAY,EAAEC,eAAe,CAAC,GAAG3E,QAAQ,CAAC,EAAE,CAAC;EACpD,MAAM,CAAC4E,mBAAmB,EAAEC,sBAAsB,CAAC,GAAG7E,QAAQ,CAAC,KAAK,CAAC;EACrE,MAAM,CAAC8E,sBAAsB,EAAEC,yBAAyB,CAAC,GAAG/E,QAAQ,CAAC,CAAC,CAAC,CAAC;EACxE,MAAM,CAACgF,sBAAsB,EAAEC,yBAAyB,CAAC,GAAGjF,QAAQ,CAAC,KAAK,CAAC;EAC3E,MAAM,CAACkF,sBAAsB,EAAEC,yBAAyB,CAAC,GAAGnF,QAAQ,CAAC,QAAQ,CAAC;EAC9E,MAAM,CAACoF,oBAAoB,EAAEC,uBAAuB,CAAC,GAAGrF,QAAQ,CAAC,IAAI,CAAC;EACtE,MAAM,CAACsF,oBAAoB,EAAEC,uBAAuB,CAAC,GAAGvF,QAAQ,CAAC;IAC/DwF,iBAAiB,EAAE,EAAE;IACrBC,KAAK,EAAE,EAAE;IACTC,QAAQ,EAAE;EACZ,CAAC,CAAC;;EAEF;EACA,MAAM,CAACC,mBAAmB,EAAEC,sBAAsB,CAAC,GAAG5F,QAAQ,CAAC;IAC7D6F,IAAI,EAAE,KAAK;IACXC,cAAc,EAAE,IAAI;IACpBC,gBAAgB,EAAE;EACpB,CAAC,CAAC;;EAEF;EACA9F,SAAS,CAAC,MAAM;IACd,IAAI0D,UAAU,EAAE;MACdqC,WAAW,CAAC,CAAC;MACbC,eAAe,CAAC,CAAC;MACjBC,gBAAgB,CAAC,CAAC;IACpB;EACF,CAAC,EAAE,CAACvC,UAAU,CAAC,CAAC;EAEhB,MAAMqC,WAAW,GAAG,MAAAA,CAAA,KAAY;IAC9B,IAAI;MACF/B,UAAU,CAAC,IAAI,CAAC;MAChB,MAAMkC,IAAI,GAAG,MAAM9C,cAAc,CAAC+C,UAAU,CAACzC,UAAU,CAAC;MACxDU,UAAU,CAAC8B,IAAI,CAAC/B,OAAO,IAAI,EAAE,CAAC;MAC9BD,QAAQ,CAAC,IAAI,CAAC;IAChB,CAAC,CAAC,OAAOkC,GAAG,EAAE;MACZC,OAAO,CAACpC,KAAK,CAAC,uCAAuC,EAAEmC,GAAG,CAAC;MAC3DlC,QAAQ,CAAC,sCAAsC,CAAC;IAClD,CAAC,SAAS;MACRF,UAAU,CAAC,KAAK,CAAC;IACnB;EACF,CAAC;EAED,MAAMgC,eAAe,GAAG,MAAAA,CAAA,KAAY;IAClC,IAAI;MACF,MAAMM,KAAK,GAAG,MAAMlD,cAAc,CAACmD,qBAAqB,CAAC7C,UAAU,CAAC;MACpEY,cAAc,CAACgC,KAAK,CAAC;IACvB,CAAC,CAAC,OAAOF,GAAG,EAAE;MACZC,OAAO,CAACpC,KAAK,CAAC,2CAA2C,EAAEmC,GAAG,CAAC;IACjE;EACF,CAAC;EAED,MAAMH,gBAAgB,GAAG,MAAAA,CAAA,KAAY;IACnC,IAAI;MACFrB,sBAAsB,CAAC,IAAI,CAAC;MAC5BV,QAAQ,CAAC,IAAI,CAAC;MAEd,MAAMgC,IAAI,GAAG,MAAM7C,mBAAmB,CAACmD,uBAAuB,CAAC9C,UAAU,CAAC;MAC1EgB,eAAe,CAACwB,IAAI,IAAI,EAAE,CAAC;MAC3B,MAAMO,0BAA0B,CAACP,IAAI,IAAI,EAAE,CAAC;IAC9C,CAAC,CAAC,OAAOE,GAAG,EAAE;MAAA,IAAAM,aAAA,EAAAC,kBAAA;MACZN,OAAO,CAACpC,KAAK,CAAC,0CAA0C,EAAEmC,GAAG,CAAC;MAC9D,MAAMQ,YAAY,GAAG,EAAAF,aAAA,GAAAN,GAAG,CAACS,QAAQ,cAAAH,aAAA,wBAAAC,kBAAA,GAAZD,aAAA,CAAcR,IAAI,cAAAS,kBAAA,uBAAlBA,kBAAA,CAAoBG,MAAM,KAAIV,GAAG,CAACW,OAAO,IAAI,yCAAyC;MAC3G7C,QAAQ,CAAC,4CAA4C0C,YAAY,EAAE,CAAC;IACtE,CAAC,SAAS;MACRhC,sBAAsB,CAAC,KAAK,CAAC;IAC/B;EACF,CAAC;EAED,MAAM6B,0BAA0B,GAAG,MAAOO,gBAAgB,IAAK;IAC7D,IAAI;MACF,MAAMC,UAAU,GAAG,CAAC,CAAC;MACrB,KAAK,MAAMC,YAAY,IAAIF,gBAAgB,EAAE;QAC3C,IAAI;UACF,MAAMH,QAAQ,GAAG,MAAMzD,cAAc,CAAC+D,wBAAwB,CAACzD,UAAU,EAAEwD,YAAY,CAAC3B,iBAAiB,CAAC;UAC1G;UACA,IAAIpB,OAAO,GAAG,EAAE;UAChB,IAAI0C,QAAQ,IAAIO,KAAK,CAACC,OAAO,CAACR,QAAQ,CAAC,EAAE;YACvC1C,OAAO,GAAG0C,QAAQ;UACpB,CAAC,MAAM,IAAIA,QAAQ,IAAIA,QAAQ,CAAC1C,OAAO,IAAIiD,KAAK,CAACC,OAAO,CAACR,QAAQ,CAAC1C,OAAO,CAAC,EAAE;YAC1EA,OAAO,GAAG0C,QAAQ,CAAC1C,OAAO;UAC5B,CAAC,MAAM,IAAI0C,QAAQ,IAAIA,QAAQ,CAACX,IAAI,IAAIkB,KAAK,CAACC,OAAO,CAACR,QAAQ,CAACX,IAAI,CAAC,EAAE;YACpE/B,OAAO,GAAG0C,QAAQ,CAACX,IAAI;UACzB;UACAe,UAAU,CAACC,YAAY,CAACI,eAAe,CAAC,GAAGnD,OAAO;QACpD,CAAC,CAAC,OAAOiC,GAAG,EAAE;UACZC,OAAO,CAACpC,KAAK,CAAC,sCAAsCiD,YAAY,CAAC3B,iBAAiB,GAAG,EAAEa,GAAG,CAAC;UAC3Fa,UAAU,CAACC,YAAY,CAACI,eAAe,CAAC,GAAG,EAAE;QAC/C;MACF;MACAxC,yBAAyB,CAACmC,UAAU,CAAC;IACvC,CAAC,CAAC,OAAOb,GAAG,EAAE;MACZC,OAAO,CAACpC,KAAK,CAAC,uCAAuC,EAAEmC,GAAG,CAAC;IAC7D;EACF,CAAC;;EAED;EACA,MAAMmB,4BAA4B,GAAGA,CAACC,IAAI,EAAEN,YAAY,GAAG,IAAI,KAAK;IAClEhC,yBAAyB,CAACsC,IAAI,CAAC;IAC/BpC,uBAAuB,CAAC8B,YAAY,CAAC;IAErC,IAAIM,IAAI,KAAK,MAAM,IAAIN,YAAY,EAAE;MACnC5B,uBAAuB,CAAC;QACtBC,iBAAiB,EAAE2B,YAAY,CAAC3B,iBAAiB,IAAI,EAAE;QACvDC,KAAK,EAAE0B,YAAY,CAAC1B,KAAK,IAAI,EAAE;QAC/BC,QAAQ,EAAEyB,YAAY,CAACzB,QAAQ,IAAI;MACrC,CAAC,CAAC;IACJ,CAAC,MAAM;MACLH,uBAAuB,CAAC;QACtBC,iBAAiB,EAAE,EAAE;QACrBC,KAAK,EAAE,EAAE;QACTC,QAAQ,EAAE;MACZ,CAAC,CAAC;IACJ;IAEAT,yBAAyB,CAAC,IAAI,CAAC;EACjC,CAAC;EAED,MAAMyC,6BAA6B,GAAGA,CAAA,KAAM;IAC1CzC,yBAAyB,CAAC,KAAK,CAAC;IAChCI,uBAAuB,CAAC,IAAI,CAAC;IAC7BlB,QAAQ,CAAC,IAAI,CAAC;EAChB,CAAC;EAED,MAAMwD,wBAAwB,GAAG,MAAAA,CAAA,KAAY;IAC3C,IAAI;MACFxD,QAAQ,CAAC,IAAI,CAAC;MAEd,IAAI,CAACmB,oBAAoB,CAACE,iBAAiB,CAACoC,IAAI,CAAC,CAAC,EAAE;QAClDzD,QAAQ,CAAC,yCAAyC,CAAC;QACnD;MACF;MAEA,IAAI,CAACmB,oBAAoB,CAACG,KAAK,IAAI,CAACH,oBAAoB,CAACI,QAAQ,EAAE;QACjEvB,QAAQ,CAAC,yDAAyD,CAAC;QACnE;MACF;MAEA,IAAIe,sBAAsB,KAAK,QAAQ,EAAE;QACvC,MAAM5B,mBAAmB,CAACuE,kBAAkB,CAAClE,UAAU,EAAE2B,oBAAoB,CAAC;MAChF,CAAC,MAAM,IAAIJ,sBAAsB,KAAK,MAAM,EAAE;QAC5C,MAAM5B,mBAAmB,CAACwE,kBAAkB,CAAC1C,oBAAoB,CAACmC,eAAe,EAAEjC,oBAAoB,CAAC;MAC1G;MAEAoC,6BAA6B,CAAC,CAAC;MAC/B,MAAMxB,gBAAgB,CAAC,CAAC;IAC1B,CAAC,CAAC,OAAOG,GAAG,EAAE;MACZC,OAAO,CAACpC,KAAK,CAAC,yBAAyB,EAAEmC,GAAG,CAAC;MAC7ClC,QAAQ,CAACkC,GAAG,CAACU,MAAM,IAAI,yCAAyC,CAAC;IACnE;EACF,CAAC;EAED,MAAMgB,wBAAwB,GAAG,MAAOC,cAAc,IAAK;IACzD,IAAI,CAACC,MAAM,CAACC,OAAO,CAAC,oDAAoD,CAAC,EAAE;MACzE;IACF;IAEA,IAAI;MACF,MAAM5E,mBAAmB,CAAC6E,kBAAkB,CAACH,cAAc,CAAC;MAC5D,MAAM9B,gBAAgB,CAAC,CAAC;IAC1B,CAAC,CAAC,OAAOG,GAAG,EAAE;MACZC,OAAO,CAACpC,KAAK,CAAC,4BAA4B,EAAEmC,GAAG,CAAC;MAChDlC,QAAQ,CAAC,4CAA4C,CAAC;IACxD;EACF,CAAC;EAED,MAAMiE,mBAAmB,GAAIC,IAAI,IAAK;IACpC,MAAMC,MAAM,GAAG;MACb,MAAM,EAAE,MAAM;MACd,uBAAuB,EAAE,gBAAgB;MACzC,qBAAqB,EAAE,cAAc;MACrC,gBAAgB,EAAE;IACpB,CAAC;IACD,OAAOA,MAAM,CAACD,IAAI,CAAC,IAAIA,IAAI;EAC7B,CAAC;EAED,MAAME,aAAa,GAAIC,KAAK,IAAK;IAC/B,MAAMC,MAAM,GAAG;MACb,QAAQ,EAAE,SAAS;MACnB,UAAU,EAAE,SAAS;MACrB,YAAY,EAAE,SAAS;MACvB,WAAW,EAAE;IACf,CAAC;IACD,OAAOA,MAAM,CAACD,KAAK,CAAC,IAAI,SAAS;EACnC,CAAC;EAED,IAAIxE,OAAO,EAAE;IACX,oBACEP,OAAA,CAACvD,GAAG;MAACwI,OAAO,EAAC,MAAM;MAACC,cAAc,EAAC,QAAQ;MAACC,UAAU,EAAC,QAAQ;MAACC,SAAS,EAAC,OAAO;MAAAC,QAAA,eAC/ErF,OAAA,CAACnC,gBAAgB;QAAAyH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACjB,CAAC;EAEV;EAEA,oBACEzF,OAAA,CAACvD,GAAG;IAAA4I,QAAA,GAED5E,KAAK,iBACJT,OAAA,CAACpC,KAAK;MAAC8H,QAAQ,EAAC,OAAO;MAACC,EAAE,EAAE;QAAEC,EAAE,EAAE;MAAE,CAAE;MAAAP,QAAA,EACnC5E;IAAK;MAAA6E,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACD,CACR,eAGDzF,OAAA,CAACvD,GAAG;MAACkJ,EAAE,EAAE;QAAEE,YAAY,EAAE,CAAC;QAAEC,WAAW,EAAE,SAAS;QAAEF,EAAE,EAAE;MAAE,CAAE;MAAAP,QAAA,eAC1DrF,OAAA,CAAC7B,IAAI;QACH4H,KAAK,EAAE1F,SAAU;QACjB2F,QAAQ,EAAEA,CAACC,CAAC,EAAEC,QAAQ,KAAK5F,YAAY,CAAC4F,QAAQ,CAAE;QAClDP,EAAE,EAAE;UACF,gBAAgB,EAAE;YAChBQ,aAAa,EAAE,MAAM;YACrBC,UAAU,EAAE,GAAG;YACfC,QAAQ,EAAE;UACZ;QACF,CAAE;QAAAhB,QAAA,gBAEFrF,OAAA,CAAC5B,GAAG;UAACkI,KAAK,EAAC;QAAc;UAAAhB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,eAC5BzF,OAAA,CAAC5B,GAAG;UAACkI,KAAK,EAAC;QAAkB;UAAAhB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC5B;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACJ,CAAC,EAGLpF,SAAS,KAAK,CAAC,iBACdL,OAAA,CAACvD,GAAG;MAAA4I,QAAA,gBAEFrF,OAAA,CAACvD,GAAG;QAACwI,OAAO,EAAC,MAAM;QAACC,cAAc,EAAC,eAAe;QAACC,UAAU,EAAC,QAAQ;QAACS,EAAE,EAAE,CAAE;QAAAP,QAAA,gBAC3ErF,OAAA,CAACpD,UAAU;UAAC2J,OAAO,EAAC,IAAI;UAACZ,EAAE,EAAE;YAAES,UAAU,EAAE,GAAG;YAAEI,KAAK,EAAE;UAAe,CAAE;UAAAnB,QAAA,EAAC;QAEzE;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAY,CAAC,eACbzF,OAAA,CAACnD,MAAM;UACL0J,OAAO,EAAC,WAAW;UACnBE,SAAS,eAAEzG,OAAA,CAACvB,OAAO;YAAA6G,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAE;UACvBiB,OAAO,EAAEA,CAAA,KAAM3C,4BAA4B,CAAC,QAAQ,CAAE;UACtD4B,EAAE,EAAE;YACFQ,aAAa,EAAE,MAAM;YACrBC,UAAU,EAAE,GAAG;YACfO,EAAE,EAAE,CAAC;YACLC,EAAE,EAAE;UACN,CAAE;UAAAvB,QAAA,EACH;QAED;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACN,CAAC,EAELtE,mBAAmB,gBAClBnB,OAAA,CAACvD,GAAG;QAACwI,OAAO,EAAC,MAAM;QAACC,cAAc,EAAC,QAAQ;QAAC0B,EAAE,EAAE,CAAE;QAAAvB,QAAA,eAChDrF,OAAA,CAACnC,gBAAgB;UAAAyH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACjB,CAAC,gBAENzF,OAAA,CAACvD,GAAG;QAAA4I,QAAA,EACDpE,YAAY,CAAC4F,MAAM,KAAK,CAAC,gBACxB7G,OAAA,CAAC5C,KAAK;UACJ0J,SAAS,EAAE,CAAE;UACbnB,EAAE,EAAE;YACFoB,CAAC,EAAE,CAAC;YACJC,SAAS,EAAE,QAAQ;YACnBC,eAAe,EAAE,SAAS;YAC1BC,MAAM,EAAE,YAAY;YACpBpB,WAAW,EAAE;UACf,CAAE;UAAAT,QAAA,gBAEFrF,OAAA,CAACX,UAAU;YAACsG,EAAE,EAAE;cAAEU,QAAQ,EAAE,EAAE;cAAEG,KAAK,EAAE,UAAU;cAAEZ,EAAE,EAAE;YAAE;UAAE;YAAAN,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,eAC9DzF,OAAA,CAACpD,UAAU;YAAC2J,OAAO,EAAC,IAAI;YAACC,KAAK,EAAC,gBAAgB;YAACW,YAAY;YAAA9B,QAAA,EAAC;UAE7D;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAY,CAAC,eACbzF,OAAA,CAACpD,UAAU;YAAC2J,OAAO,EAAC,OAAO;YAACC,KAAK,EAAC,gBAAgB;YAACb,EAAE,EAAE;cAAEC,EAAE,EAAE;YAAE,CAAE;YAAAP,QAAA,EAAC;UAElE;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAY,CAAC,eACbzF,OAAA,CAACnD,MAAM;YACL0J,OAAO,EAAC,WAAW;YACnBE,SAAS,eAAEzG,OAAA,CAACvB,OAAO;cAAA6G,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAE;YACvBiB,OAAO,EAAEA,CAAA,KAAM3C,4BAA4B,CAAC,QAAQ,CAAE;YACtD4B,EAAE,EAAE;cAAEQ,aAAa,EAAE;YAAO,CAAE;YAAAd,QAAA,EAC/B;UAED;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACJ,CAAC,GAERxE,YAAY,CAACmG,GAAG,CAAE1D,YAAY,iBAC5B1D,OAAA,CAAC3B,SAAS;UAERsH,EAAE,EAAE;YACFC,EAAE,EAAE,CAAC;YACL,UAAU,EAAE;cAAEX,OAAO,EAAE;YAAO,CAAC;YAC/BoC,SAAS,EAAE,2BAA2B;YACtCH,MAAM,EAAE,WAAW;YACnBpB,WAAW,EAAE;UACf,CAAE;UAAAT,QAAA,gBAEFrF,OAAA,CAAC1B,gBAAgB;YACfgJ,UAAU,eAAEtH,OAAA,CAACL,cAAc;cAAA2F,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAE;YAC/BE,EAAE,EAAE;cACF,SAAS,EAAE;gBACTsB,eAAe,EAAE;cACnB;YACF,CAAE;YAAA5B,QAAA,eAEFrF,OAAA,CAACvD,GAAG;cAACwI,OAAO,EAAC,MAAM;cAACE,UAAU,EAAC,QAAQ;cAACD,cAAc,EAAC,eAAe;cAACqC,KAAK,EAAC,MAAM;cAAAlC,QAAA,gBACjFrF,OAAA,CAACvD,GAAG;gBAACwI,OAAO,EAAC,MAAM;gBAACE,UAAU,EAAC,QAAQ;gBAACqC,GAAG,EAAE,CAAE;gBAAAnC,QAAA,gBAC7CrF,OAAA,CAACX,UAAU;kBAACmH,KAAK,EAAC,SAAS;kBAACb,EAAE,EAAE;oBAAEU,QAAQ,EAAE;kBAAG;gBAAE;kBAAAf,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAC,eACpDzF,OAAA,CAACvD,GAAG;kBAAA4I,QAAA,gBACFrF,OAAA,CAACpD,UAAU;oBAAC2J,OAAO,EAAC,IAAI;oBAACZ,EAAE,EAAE;sBAAES,UAAU,EAAE;oBAAI,CAAE;oBAAAf,QAAA,EAC9C3B,YAAY,CAAC3B;kBAAiB;oBAAAuD,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACrB,CAAC,eACbzF,OAAA,CAACvD,GAAG;oBAACwI,OAAO,EAAC,MAAM;oBAACuC,GAAG,EAAE,CAAE;oBAACC,EAAE,EAAE,GAAI;oBAAApC,QAAA,GACjC3B,YAAY,CAAC1B,KAAK,iBACjBhC,OAAA,CAACvD,GAAG;sBAACwI,OAAO,EAAC,MAAM;sBAACE,UAAU,EAAC,QAAQ;sBAACqC,GAAG,EAAE,GAAI;sBAAAnC,QAAA,gBAC/CrF,OAAA,CAACT,SAAS;wBAAC8G,QAAQ,EAAC,OAAO;wBAACG,KAAK,EAAC;sBAAQ;wBAAAlB,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAE,CAAC,eAC7CzF,OAAA,CAACpD,UAAU;wBAAC2J,OAAO,EAAC,OAAO;wBAACC,KAAK,EAAC,gBAAgB;wBAAAnB,QAAA,EAC/C3B,YAAY,CAAC1B;sBAAK;wBAAAsD,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACT,CAAC;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACV,CACN,EACA/B,YAAY,CAACzB,QAAQ,iBACpBjC,OAAA,CAACvD,GAAG;sBAACwI,OAAO,EAAC,MAAM;sBAACE,UAAU,EAAC,QAAQ;sBAACqC,GAAG,EAAE,GAAI;sBAAAnC,QAAA,gBAC/CrF,OAAA,CAACP,SAAS;wBAAC4G,QAAQ,EAAC,OAAO;wBAACG,KAAK,EAAC;sBAAQ;wBAAAlB,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAE,CAAC,eAC7CzF,OAAA,CAACpD,UAAU;wBAAC2J,OAAO,EAAC,OAAO;wBAACC,KAAK,EAAC,gBAAgB;wBAAAnB,QAAA,EAC/C3B,YAAY,CAACzB;sBAAQ;wBAAAqD,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACZ,CAAC;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACV,CACN;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACE,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CAAC,eAENzF,OAAA,CAACvD,GAAG;gBAACwI,OAAO,EAAC,MAAM;gBAACE,UAAU,EAAC,QAAQ;gBAACqC,GAAG,EAAE,CAAE;gBAACd,OAAO,EAAGT,CAAC,IAAKA,CAAC,CAACyB,eAAe,CAAC,CAAE;gBAAArC,QAAA,gBAClFrF,OAAA,CAAC3C,IAAI;kBACHsK,IAAI,eAAE3H,OAAA,CAACf,UAAU;oBAAAqG,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE,CAAE;kBACrBa,KAAK,EAAE,GAAG1C,KAAK,CAACC,OAAO,CAACxC,sBAAsB,CAACqC,YAAY,CAACI,eAAe,CAAC,CAAC,GAAGzC,sBAAsB,CAACqC,YAAY,CAACI,eAAe,CAAC,CAAC+C,MAAM,GAAG,CAAC,UAAW;kBAC1Je,IAAI,EAAC,OAAO;kBACZpB,KAAK,EAAC,SAAS;kBACfD,OAAO,EAAC,UAAU;kBAClBZ,EAAE,EAAE;oBAAES,UAAU,EAAE;kBAAI;gBAAE;kBAAAd,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACzB,CAAC,eACFzF,OAAA,CAAClC,OAAO;kBAAC+J,KAAK,EAAC,uBAAuB;kBAAAxC,QAAA,eACpCrF,OAAA,CAAC1C,UAAU;oBACTsK,IAAI,EAAC,OAAO;oBACZlB,OAAO,EAAEA,CAAA,KAAM3C,4BAA4B,CAAC,MAAM,EAAEL,YAAY,CAAE;oBAClEiC,EAAE,EAAE;sBACF,SAAS,EAAE;wBACTsB,eAAe,EAAE,eAAe;wBAChCT,KAAK,EAAE;sBACT;oBACF,CAAE;oBAAAnB,QAAA,eAEFrF,OAAA,CAACrB,QAAQ;sBAAC0H,QAAQ,EAAC;oBAAO;sBAAAf,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAE;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACnB;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACN,CAAC,eACVzF,OAAA,CAAClC,OAAO;kBAAC+J,KAAK,EAAC,sBAAsB;kBAAAxC,QAAA,eACnCrF,OAAA,CAAC1C,UAAU;oBACTsK,IAAI,EAAC,OAAO;oBACZlB,OAAO,EAAEA,CAAA,KAAMpC,wBAAwB,CAACZ,YAAY,CAACI,eAAe,CAAE;oBACtE6B,EAAE,EAAE;sBACF,SAAS,EAAE;wBACTsB,eAAe,EAAE,aAAa;wBAC9BT,KAAK,EAAE;sBACT;oBACF,CAAE;oBAAAnB,QAAA,eAEFrF,OAAA,CAACnB,UAAU;sBAACwH,QAAQ,EAAC;oBAAO;sBAAAf,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAE;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACrB;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACN,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACP,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACU,CAAC,eAEnBzF,OAAA,CAACzB,gBAAgB;YAACoH,EAAE,EAAE;cAAEmC,EAAE,EAAE;YAAE,CAAE;YAAAzC,QAAA,gBAC9BrF,OAAA,CAACpD,UAAU;cAAC2J,OAAO,EAAC,WAAW;cAACY,YAAY;cAACxB,EAAE,EAAE;gBAAES,UAAU,EAAE,GAAG;gBAAER,EAAE,EAAE;cAAE,CAAE;cAAAP,QAAA,EAAC;YAE7E;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAY,CAAC,EAEX,CAAC7B,KAAK,CAACC,OAAO,CAACxC,sBAAsB,CAACqC,YAAY,CAACI,eAAe,CAAC,CAAC,IAAIzC,sBAAsB,CAACqC,YAAY,CAACI,eAAe,CAAC,CAAC+C,MAAM,KAAK,CAAC,gBACzI7G,OAAA,CAACvD,GAAG;cACFkJ,EAAE,EAAE;gBACFoB,CAAC,EAAE,CAAC;gBACJC,SAAS,EAAE,QAAQ;gBACnBC,eAAe,EAAE,SAAS;gBAC1Bc,YAAY,EAAE,CAAC;gBACfb,MAAM,EAAE,YAAY;gBACpBpB,WAAW,EAAE;cACf,CAAE;cAAAT,QAAA,eAEFrF,OAAA,CAACpD,UAAU;gBAAC2J,OAAO,EAAC,OAAO;gBAACC,KAAK,EAAC,gBAAgB;gBAAAnB,QAAA,EAAC;cAEnD;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAY;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACV,CAAC,gBAENzF,OAAA,CAAChC,IAAI;cAACgK,KAAK;cAAA3C,QAAA,EACRzB,KAAK,CAACC,OAAO,CAACxC,sBAAsB,CAACqC,YAAY,CAACI,eAAe,CAAC,CAAC,IAAIzC,sBAAsB,CAACqC,YAAY,CAACI,eAAe,CAAC,CAACsD,GAAG,CAAEa,OAAO,iBACvIjI,OAAA,CAAC/B,QAAQ;gBAA8BiK,OAAO;gBAAA7C,QAAA,eAC5CrF,OAAA,CAAC9B,YAAY;kBACXiK,OAAO,eACLnI,OAAA,CAACvD,GAAG;oBAACwI,OAAO,EAAC,MAAM;oBAACE,UAAU,EAAC,QAAQ;oBAACqC,GAAG,EAAE,CAAE;oBAAAnC,QAAA,gBAC7CrF,OAAA,CAACpD,UAAU;sBAAC2J,OAAO,EAAC,OAAO;sBAACH,UAAU,EAAC,MAAM;sBAAAf,QAAA,EAC1C4C,OAAO,CAACG;oBAAc;sBAAA9C,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACb,CAAC,eACbzF,OAAA,CAAC3C,IAAI;sBACHiJ,KAAK,EAAE3B,mBAAmB,CAACsD,OAAO,CAACI,YAAY,CAAE;sBACjDT,IAAI,EAAC,OAAO;sBACZrB,OAAO,EAAC;oBAAU;sBAAAjB,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACnB,CAAC,eACFzF,OAAA,CAAC3C,IAAI;sBACHiJ,KAAK,EAAE2B,OAAO,CAAClD,KAAK,IAAI,QAAS;sBACjC6C,IAAI,EAAC,OAAO;sBACZpB,KAAK,EAAE1B,aAAa,CAACmD,OAAO,CAAClD,KAAK;oBAAE;sBAAAO,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACrC,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACC,CACN;kBACD6C,SAAS,eACPtI,OAAA,CAACpD,UAAU;oBAAC2J,OAAO,EAAC,OAAO;oBAACC,KAAK,EAAC,eAAe;oBAAAnB,QAAA,GAC9C4C,OAAO,CAACM,WAAW,IAAI,qBAAqB,EAC5CN,OAAO,CAACO,cAAc,IAAI,cAAc,IAAIC,IAAI,CAACR,OAAO,CAACO,cAAc,CAAC,CAACE,kBAAkB,CAAC,CAAC,EAAE;kBAAA;oBAAApD,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACtF;gBACb;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACF;cAAC,GAzBWwC,OAAO,CAACG,cAAc;gBAAA9C,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OA0B3B,CACX;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACE,CACP;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACe,CAAC;QAAA,GA3Id/B,YAAY,CAACI,eAAe;UAAAwB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OA4IxB,CACZ;MACF;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACE,CACN;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACE,CACN,EAGApF,SAAS,KAAK,CAAC,iBACdL,OAAA,CAACvD,GAAG;MAAA4I,QAAA,GAEDxE,WAAW,iBACVb,OAAA,CAACjC,IAAI;QAAC4K,SAAS;QAACC,OAAO,EAAE,CAAE;QAACjD,EAAE,EAAE;UAAEC,EAAE,EAAE;QAAE,CAAE;QAAAP,QAAA,gBACxCrF,OAAA,CAACjC,IAAI;UAAC8K,IAAI;UAACC,EAAE,EAAE,EAAG;UAACC,EAAE,EAAE,CAAE;UAACC,EAAE,EAAE,CAAE;UAAA3D,QAAA,eAC9BrF,OAAA,CAACtD,IAAI;YAACiJ,EAAE,EAAE;cAAE0B,SAAS,EAAE,2BAA2B;cAAEH,MAAM,EAAE,WAAW;cAAEpB,WAAW,EAAE;YAAW,CAAE;YAAAT,QAAA,eACjGrF,OAAA,CAACrD,WAAW;cAACgJ,EAAE,EAAE;gBAAEqB,SAAS,EAAE,QAAQ;gBAAEJ,EAAE,EAAE;cAAE,CAAE;cAAAvB,QAAA,gBAC9CrF,OAAA,CAACpD,UAAU;gBAAC4J,KAAK,EAAC,gBAAgB;gBAACD,OAAO,EAAC,OAAO;gBAACZ,EAAE,EAAE;kBAAES,UAAU,EAAE,GAAG;kBAAER,EAAE,EAAE;gBAAE,CAAE;gBAAAP,QAAA,EAAC;cAEnF;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAY,CAAC,eACbzF,OAAA,CAACpD,UAAU;gBAAC2J,OAAO,EAAC,IAAI;gBAACZ,EAAE,EAAE;kBAAES,UAAU,EAAE,GAAG;kBAAEI,KAAK,EAAE;gBAAe,CAAE;gBAAAnB,QAAA,EACrExE,WAAW,CAACoI;cAAc;gBAAA3D,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACjB,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACF;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACV;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,eACPzF,OAAA,CAACjC,IAAI;UAAC8K,IAAI;UAACC,EAAE,EAAE,EAAG;UAACC,EAAE,EAAE,CAAE;UAACC,EAAE,EAAE,CAAE;UAAA3D,QAAA,eAC9BrF,OAAA,CAACtD,IAAI;YAACiJ,EAAE,EAAE;cAAE0B,SAAS,EAAE,2BAA2B;cAAEH,MAAM,EAAE,WAAW;cAAEpB,WAAW,EAAE;YAAW,CAAE;YAAAT,QAAA,eACjGrF,OAAA,CAACrD,WAAW;cAACgJ,EAAE,EAAE;gBAAEqB,SAAS,EAAE,QAAQ;gBAAEJ,EAAE,EAAE;cAAE,CAAE;cAAAvB,QAAA,gBAC9CrF,OAAA,CAACpD,UAAU;gBAAC4J,KAAK,EAAC,gBAAgB;gBAACD,OAAO,EAAC,OAAO;gBAACZ,EAAE,EAAE;kBAAES,UAAU,EAAE,GAAG;kBAAER,EAAE,EAAE;gBAAE,CAAE;gBAAAP,QAAA,EAAC;cAEnF;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAY,CAAC,eACbzF,OAAA,CAACpD,UAAU;gBAAC2J,OAAO,EAAC,IAAI;gBAACZ,EAAE,EAAE;kBAAES,UAAU,EAAE,GAAG;kBAAEI,KAAK,EAAE;gBAAe,CAAE;gBAAAnB,QAAA,EACrExE,WAAW,CAACqI;cAAc;gBAAA5D,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACjB,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACF;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACV;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,eACPzF,OAAA,CAACjC,IAAI;UAAC8K,IAAI;UAACC,EAAE,EAAE,EAAG;UAACC,EAAE,EAAE,CAAE;UAACC,EAAE,EAAE,CAAE;UAAA3D,QAAA,eAC9BrF,OAAA,CAACtD,IAAI;YAACiJ,EAAE,EAAE;cAAE0B,SAAS,EAAE,2BAA2B;cAAEH,MAAM,EAAE,WAAW;cAAEpB,WAAW,EAAE;YAAW,CAAE;YAAAT,QAAA,eACjGrF,OAAA,CAACrD,WAAW;cAACgJ,EAAE,EAAE;gBAAEqB,SAAS,EAAE,QAAQ;gBAAEJ,EAAE,EAAE;cAAE,CAAE;cAAAvB,QAAA,gBAC9CrF,OAAA,CAACpD,UAAU;gBAAC4J,KAAK,EAAC,gBAAgB;gBAACD,OAAO,EAAC,OAAO;gBAACZ,EAAE,EAAE;kBAAES,UAAU,EAAE,GAAG;kBAAER,EAAE,EAAE;gBAAE,CAAE;gBAAAP,QAAA,EAAC;cAEnF;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAY,CAAC,eACbzF,OAAA,CAACpD,UAAU;gBAAC2J,OAAO,EAAC,IAAI;gBAACZ,EAAE,EAAE;kBAAES,UAAU,EAAE,GAAG;kBAAEI,KAAK,EAAE;gBAAe,CAAE;gBAAAnB,QAAA,EACrExE,WAAW,CAACsI;cAAgB;gBAAA7D,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACnB,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACF;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACV;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,eACPzF,OAAA,CAACjC,IAAI;UAAC8K,IAAI;UAACC,EAAE,EAAE,EAAG;UAACC,EAAE,EAAE,CAAE;UAACC,EAAE,EAAE,CAAE;UAAA3D,QAAA,eAC9BrF,OAAA,CAACtD,IAAI;YAACiJ,EAAE,EAAE;cAAE0B,SAAS,EAAE,2BAA2B;cAAEH,MAAM,EAAE,WAAW;cAAEpB,WAAW,EAAE;YAAW,CAAE;YAAAT,QAAA,eACjGrF,OAAA,CAACrD,WAAW;cAACgJ,EAAE,EAAE;gBAAEqB,SAAS,EAAE,QAAQ;gBAAEJ,EAAE,EAAE;cAAE,CAAE;cAAAvB,QAAA,gBAC9CrF,OAAA,CAACpD,UAAU;gBAAC4J,KAAK,EAAC,gBAAgB;gBAACD,OAAO,EAAC,OAAO;gBAACZ,EAAE,EAAE;kBAAES,UAAU,EAAE,GAAG;kBAAER,EAAE,EAAE;gBAAE,CAAE;gBAAAP,QAAA,EAAC;cAEnF;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAY,CAAC,eACbzF,OAAA,CAACpD,UAAU;gBAAC2J,OAAO,EAAC,IAAI;gBAACZ,EAAE,EAAE;kBAAES,UAAU,EAAE,GAAG;kBAAEI,KAAK,EAAE;gBAAe,CAAE;gBAAAnB,QAAA,EACrExE,WAAW,CAACuI;cAAkB;gBAAA9D,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACrB,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACF;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACV;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CACP,eAGDzF,OAAA,CAACvD,GAAG;QAACwI,OAAO,EAAC,MAAM;QAACC,cAAc,EAAC,eAAe;QAACC,UAAU,EAAC,QAAQ;QAACS,EAAE,EAAE,CAAE;QAAAP,QAAA,gBAC3ErF,OAAA,CAACpD,UAAU;UAAC2J,OAAO,EAAC,IAAI;UAACZ,EAAE,EAAE;YAAES,UAAU,EAAE,GAAG;YAAEI,KAAK,EAAE;UAAe,CAAE;UAAAnB,QAAA,EAAC;QAEzE;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAY,CAAC,eACbzF,OAAA,CAACvD,GAAG;UAACwI,OAAO,EAAC,MAAM;UAACuC,GAAG,EAAE,CAAE;UAAAnC,QAAA,gBACzBrF,OAAA,CAACnD,MAAM;YACL0J,OAAO,EAAC,WAAW;YACnBE,SAAS,eAAEzG,OAAA,CAACvB,OAAO;cAAA6G,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAE;YACvBiB,OAAO,EAAEA,CAAA,KAAM1F,kBAAkB,CAAC,IAAI,CAAE;YACxC2E,EAAE,EAAE;cACFQ,aAAa,EAAE,MAAM;cACrBC,UAAU,EAAE,GAAG;cACfO,EAAE,EAAE;YACN,CAAE;YAAAtB,QAAA,EACH;UAED;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC,eACTzF,OAAA,CAACnD,MAAM;YACL0J,OAAO,EAAC,UAAU;YAClBE,SAAS,eAAEzG,OAAA,CAACb,WAAW;cAAAmG,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAE;YAC3BiB,OAAO,EAAEA,CAAA,KAAM;cACbnE,WAAW,CAAC,CAAC;cACbC,eAAe,CAAC,CAAC;YACnB,CAAE;YACFmD,EAAE,EAAE;cACFQ,aAAa,EAAE,MAAM;cACrBC,UAAU,EAAE;YACd,CAAE;YAAAf,QAAA,EACH;UAED;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACN,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,eAGNzF,OAAA,CAAC/C,cAAc;QAACoM,SAAS,EAAEjM,KAAM;QAACuI,EAAE,EAAE;UAAE0B,SAAS,EAAE;QAA4B,CAAE;QAAAhC,QAAA,eAC/ErF,OAAA,CAAClD,KAAK;UAAAuI,QAAA,gBACJrF,OAAA,CAAC9C,SAAS;YAAAmI,QAAA,eACRrF,OAAA,CAAC7C,QAAQ;cAACwI,EAAE,EAAE;gBAAEsB,eAAe,EAAE;cAAU,CAAE;cAAA5B,QAAA,gBAC3CrF,OAAA,CAAChD,SAAS;gBAAC2I,EAAE,EAAE;kBAAES,UAAU,EAAE;gBAAI,CAAE;gBAAAf,QAAA,EAAC;cAAM;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAW,CAAC,eACtDzF,OAAA,CAAChD,SAAS;gBAAC2I,EAAE,EAAE;kBAAES,UAAU,EAAE;gBAAI,CAAE;gBAAAf,QAAA,EAAC;cAAI;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAW,CAAC,eACpDzF,OAAA,CAAChD,SAAS;gBAAC2I,EAAE,EAAE;kBAAES,UAAU,EAAE;gBAAI,CAAE;gBAAAf,QAAA,EAAC;cAAY;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAW,CAAC,eAC5DzF,OAAA,CAAChD,SAAS;gBAAC2I,EAAE,EAAE;kBAAES,UAAU,EAAE;gBAAI,CAAE;gBAAAf,QAAA,EAAC;cAAK;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAW,CAAC,eACrDzF,OAAA,CAAChD,SAAS;gBAAC2I,EAAE,EAAE;kBAAES,UAAU,EAAE;gBAAI,CAAE;gBAAAf,QAAA,EAAC;cAAQ;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAW,CAAC,eACxDzF,OAAA,CAAChD,SAAS;gBAAC2I,EAAE,EAAE;kBAAES,UAAU,EAAE;gBAAI,CAAE;gBAAAf,QAAA,EAAC;cAAc;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAW,CAAC,eAC9DzF,OAAA,CAAChD,SAAS;gBAAC2I,EAAE,EAAE;kBAAES,UAAU,EAAE;gBAAI,CAAE;gBAAAf,QAAA,EAAC;cAAM;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAW,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC9C;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACF,CAAC,eACZzF,OAAA,CAACjD,SAAS;YAAAsI,QAAA,EACP1E,OAAO,CAACkG,MAAM,KAAK,CAAC,gBACnB7G,OAAA,CAAC7C,QAAQ;cAAAkI,QAAA,eACPrF,OAAA,CAAChD,SAAS;gBAACsM,OAAO,EAAE,CAAE;gBAACC,KAAK,EAAC,QAAQ;gBAAC5D,EAAE,EAAE;kBAAEiB,EAAE,EAAE;gBAAE,CAAE;gBAAAvB,QAAA,eAClDrF,OAAA,CAACvD,GAAG;kBAAA4I,QAAA,gBACFrF,OAAA,CAACf,UAAU;oBAAC0G,EAAE,EAAE;sBAAEU,QAAQ,EAAE,EAAE;sBAAEG,KAAK,EAAE,UAAU;sBAAEZ,EAAE,EAAE;oBAAE;kBAAE;oBAAAN,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE,CAAC,eAC9DzF,OAAA,CAACpD,UAAU;oBAAC2J,OAAO,EAAC,IAAI;oBAACC,KAAK,EAAC,gBAAgB;oBAACW,YAAY;oBAAA9B,QAAA,EAAC;kBAE7D;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAY,CAAC,eACbzF,OAAA,CAACpD,UAAU;oBAAC2J,OAAO,EAAC,OAAO;oBAACC,KAAK,EAAC,gBAAgB;oBAACb,EAAE,EAAE;sBAAEC,EAAE,EAAE;oBAAE,CAAE;oBAAAP,QAAA,EAAC;kBAElE;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAY,CAAC,eACbzF,OAAA,CAACnD,MAAM;oBACL0J,OAAO,EAAC,WAAW;oBACnBE,SAAS,eAAEzG,OAAA,CAACvB,OAAO;sBAAA6G,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAE,CAAE;oBACvBiB,OAAO,EAAEA,CAAA,KAAM1F,kBAAkB,CAAC,IAAI,CAAE;oBACxC2E,EAAE,EAAE;sBAAEQ,aAAa,EAAE;oBAAO,CAAE;oBAAAd,QAAA,EAC/B;kBAED;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAQ,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACN;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACG;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACJ,CAAC,GAEX9E,OAAO,CAACyG,GAAG,CAAEa,OAAO,iBAClBjI,OAAA,CAAC7C,QAAQ;cAAAkI,QAAA,gBACPrF,OAAA,CAAChD,SAAS;gBAAAqI,QAAA,eACRrF,OAAA,CAACpD,UAAU;kBAAC2J,OAAO,EAAC,OAAO;kBAACH,UAAU,EAAC,MAAM;kBAAAf,QAAA,EAC1C4C,OAAO,CAACG;gBAAc;kBAAA9C,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACb;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACJ,CAAC,eACZzF,OAAA,CAAChD,SAAS;gBAAAqI,QAAA,eACRrF,OAAA,CAAC3C,IAAI;kBACHiJ,KAAK,EAAE3B,mBAAmB,CAACsD,OAAO,CAACI,YAAY,CAAE;kBACjDT,IAAI,EAAC,OAAO;kBACZrB,OAAO,EAAC;gBAAU;kBAAAjB,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACnB;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACO,CAAC,eACZzF,OAAA,CAAChD,SAAS;gBAAAqI,QAAA,eACRrF,OAAA,CAACpD,UAAU;kBAAC2J,OAAO,EAAC,OAAO;kBAAAlB,QAAA,EACxB4C,OAAO,CAACvE,YAAY,IAAI;gBAAe;kBAAA4B,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAC9B;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACJ,CAAC,eACZzF,OAAA,CAAChD,SAAS;gBAAAqI,QAAA,eACRrF,OAAA,CAAC3C,IAAI;kBACHiJ,KAAK,EAAE2B,OAAO,CAAClD,KAAK,IAAI,QAAS;kBACjC6C,IAAI,EAAC,OAAO;kBACZpB,KAAK,EAAE1B,aAAa,CAACmD,OAAO,CAAClD,KAAK;gBAAE;kBAAAO,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACrC;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACO,CAAC,eACZzF,OAAA,CAAChD,SAAS;gBAAAqI,QAAA,eACRrF,OAAA,CAAC3C,IAAI;kBACHiJ,KAAK,EAAE2B,OAAO,CAACuB,QAAQ,IAAI,SAAU;kBACrC5B,IAAI,EAAC,OAAO;kBACZrB,OAAO,EAAC;gBAAU;kBAAAjB,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACnB;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACO,CAAC,eACZzF,OAAA,CAAChD,SAAS;gBAAAqI,QAAA,eACRrF,OAAA,CAACpD,UAAU;kBAAC2J,OAAO,EAAC,OAAO;kBAAAlB,QAAA,EACxB4C,OAAO,CAACO,cAAc,GAAG,IAAIC,IAAI,CAACR,OAAO,CAACO,cAAc,CAAC,CAACE,kBAAkB,CAAC,CAAC,GAAG;gBAAG;kBAAApD,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAC3E;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACJ,CAAC,eACZzF,OAAA,CAAChD,SAAS;gBAAAqI,QAAA,eACRrF,OAAA,CAACvD,GAAG;kBAACwI,OAAO,EAAC,MAAM;kBAACuC,GAAG,EAAE,GAAI;kBAAAnC,QAAA,gBAC3BrF,OAAA,CAAClC,OAAO;oBAAC+J,KAAK,EAAC,YAAY;oBAAAxC,QAAA,eACzBrF,OAAA,CAAC1C,UAAU;sBAACsK,IAAI,EAAC,OAAO;sBAAAvC,QAAA,eACtBrF,OAAA,CAACjB,QAAQ;wBAAAuG,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAE;oBAAC;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACF;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACN,CAAC,eACVzF,OAAA,CAAClC,OAAO;oBAAC+J,KAAK,EAAC,UAAU;oBAAAxC,QAAA,eACvBrF,OAAA,CAAC1C,UAAU;sBAACsK,IAAI,EAAC,OAAO;sBAAAvC,QAAA,eACtBrF,OAAA,CAACrB,QAAQ;wBAAA2G,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAE;oBAAC;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACF;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACN,CAAC,eACVzF,OAAA,CAAClC,OAAO;oBAAC+J,KAAK,EAAC,SAAS;oBAAAxC,QAAA,eACtBrF,OAAA,CAAC1C,UAAU;sBAACsK,IAAI,EAAC,OAAO;sBAACpB,KAAK,EAAC,OAAO;sBAAAnB,QAAA,eACpCrF,OAAA,CAACnB,UAAU;wBAAAyG,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAE;oBAAC;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACJ;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACN,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACP;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACG,CAAC;YAAA,GAvDCwC,OAAO,CAACG,cAAc;cAAA9C,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAwD3B,CACX;UACF;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACQ,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACP;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACM,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACd,CACN,eAGDzF,OAAA,CAACzC,MAAM;MACL6E,IAAI,EAAEb,sBAAuB;MAC7BkI,OAAO,EAAExF,6BAA8B;MACvCyF,QAAQ,EAAC,IAAI;MACbC,SAAS;MACTC,UAAU,EAAE;QACVjE,EAAE,EAAE;UAAEoC,YAAY,EAAE;QAAE;MACxB,CAAE;MAAA1C,QAAA,gBAEFrF,OAAA,CAACxC,WAAW;QAACmI,EAAE,EAAE;UAAEkE,EAAE,EAAE;QAAE,CAAE;QAAAxE,QAAA,eACzBrF,OAAA,CAACpD,UAAU;UAAC2J,OAAO,EAAC,IAAI;UAACZ,EAAE,EAAE;YAAES,UAAU,EAAE;UAAI,CAAE;UAAAf,QAAA,EAC9C5D,sBAAsB,KAAK,QAAQ,GAAG,wBAAwB,GAAG;QAAuB;UAAA6D,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC/E;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACF,CAAC,eACdzF,OAAA,CAACvC,aAAa;QAAA4H,QAAA,eACZrF,OAAA,CAACvD,GAAG;UAACkJ,EAAE,EAAE;YAAEmC,EAAE,EAAE;UAAE,CAAE;UAAAzC,QAAA,gBACjBrF,OAAA,CAACrC,SAAS;YACRgM,SAAS;YACTrD,KAAK,EAAC,mBAAmB;YACzBP,KAAK,EAAElE,oBAAoB,CAACE,iBAAkB;YAC9CiE,QAAQ,EAAGC,CAAC,IAAKnE,uBAAuB,CAAC;cAAE,GAAGD,oBAAoB;cAAEE,iBAAiB,EAAEkE,CAAC,CAAC6D,MAAM,CAAC/D;YAAM,CAAC,CAAE;YACzGgE,MAAM,EAAC,QAAQ;YACfC,QAAQ;YACRzD,OAAO,EAAC,UAAU;YAClBZ,EAAE,EAAE;cAAEC,EAAE,EAAE;YAAE;UAAE;YAAAN,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACf,CAAC,eAEFzF,OAAA,CAACrC,SAAS;YACRgM,SAAS;YACTrD,KAAK,EAAC,OAAO;YACb2D,IAAI,EAAC,OAAO;YACZlE,KAAK,EAAElE,oBAAoB,CAACG,KAAM;YAClCgE,QAAQ,EAAGC,CAAC,IAAKnE,uBAAuB,CAAC;cAAE,GAAGD,oBAAoB;cAAEG,KAAK,EAAEiE,CAAC,CAAC6D,MAAM,CAAC/D;YAAM,CAAC,CAAE;YAC7FgE,MAAM,EAAC,QAAQ;YACfxD,OAAO,EAAC,UAAU;YAClB2D,UAAU,EAAC,uDAAuD;YAClEvE,EAAE,EAAE;cAAEC,EAAE,EAAE;YAAE;UAAE;YAAAN,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACf,CAAC,eAEFzF,OAAA,CAACrC,SAAS;YACRgM,SAAS;YACTrD,KAAK,EAAC,UAAU;YAChBP,KAAK,EAAElE,oBAAoB,CAACI,QAAS;YACrC+D,QAAQ,EAAGC,CAAC,IAAKnE,uBAAuB,CAAC;cAAE,GAAGD,oBAAoB;cAAEI,QAAQ,EAAEgE,CAAC,CAAC6D,MAAM,CAAC/D;YAAM,CAAC,CAAE;YAChGgE,MAAM,EAAC,QAAQ;YACfxD,OAAO,EAAC,UAAU;YAClB2D,UAAU,EAAC;UAA+C;YAAA5E,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC3D,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACC;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACO,CAAC,eAChBzF,OAAA,CAACtC,aAAa;QAACiI,EAAE,EAAE;UAAEoB,CAAC,EAAE,CAAC;UAAEe,EAAE,EAAE;QAAE,CAAE;QAAAzC,QAAA,gBACjCrF,OAAA,CAACnD,MAAM;UACL6J,OAAO,EAAEzC,6BAA8B;UACvC0B,EAAE,EAAE;YAAEQ,aAAa,EAAE;UAAO,CAAE;UAAAd,QAAA,EAC/B;QAED;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC,eACTzF,OAAA,CAACnD,MAAM;UACL6J,OAAO,EAAExC,wBAAyB;UAClCqC,OAAO,EAAC,WAAW;UACnBZ,EAAE,EAAE;YACFQ,aAAa,EAAE,MAAM;YACrBC,UAAU,EAAE,GAAG;YACfO,EAAE,EAAE;UACN,CAAE;UAAAtB,QAAA,EAED5D,sBAAsB,KAAK,QAAQ,GAAG,MAAM,GAAG;QAAO;UAAA6D,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACjD,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACI,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACV,CAAC,eAGTzF,OAAA,CAACzC,MAAM;MACL6E,IAAI,EAAEF,mBAAmB,CAACE,IAAK;MAC/BqH,OAAO,EAAEU,kBAAmB;MAC5BT,QAAQ,EAAC,IAAI;MACbC,SAAS;MACTC,UAAU,EAAE;QACVjE,EAAE,EAAE;UACFoC,YAAY,EAAE,CAAC;UACfV,SAAS,EAAE;QACb;MACF,CAAE;MAAAhC,QAAA,gBAEFrF,OAAA,CAACxC,WAAW;QAACmI,EAAE,EAAE;UAAEkE,EAAE,EAAE;QAAE,CAAE;QAAAxE,QAAA,eACzBrF,OAAA,CAACvD,GAAG;UAACwI,OAAO,EAAC,MAAM;UAACE,UAAU,EAAC,QAAQ;UAACqC,GAAG,EAAE,CAAE;UAAAnC,QAAA,gBAC7CrF,OAAA,CAACnB,UAAU;YAAC2H,KAAK,EAAC;UAAO;YAAAlB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,eAC5BzF,OAAA,CAACpD,UAAU;YAAC2J,OAAO,EAAC,IAAI;YAAC8C,SAAS,EAAC,MAAM;YAAAhE,QAAA,EAAC;UAE1C;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAY,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACV;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACK,CAAC,eACdzF,OAAA,CAACvC,aAAa;QAACkI,EAAE,EAAE;UAAEkE,EAAE,EAAE;QAAE,CAAE;QAAAxE,QAAA,gBAC3BrF,OAAA,CAACpD,UAAU;UAAC2J,OAAO,EAAC,OAAO;UAACZ,EAAE,EAAE;YAAEC,EAAE,EAAE;UAAE,CAAE;UAAAP,QAAA,EAAC;QAE3C;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAY,CAAC,eACbzF,OAAA,CAACpD,UAAU;UAAC2J,OAAO,EAAC,IAAI;UAACC,KAAK,EAAC,SAAS;UAACb,EAAE,EAAE;YAAES,UAAU,EAAE;UAAI,CAAE;UAAAf,QAAA,EAC9DnD,mBAAmB,CAACI;QAAgB;UAAAgD,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC3B,CAAC,eACbzF,OAAA,CAACpD,UAAU;UAAC2J,OAAO,EAAC,OAAO;UAACC,KAAK,EAAC,gBAAgB;UAACb,EAAE,EAAE;YAAE8B,EAAE,EAAE;UAAE,CAAE;UAAApC,QAAA,EAAC;QAElE;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAY,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACA,CAAC,eAChBzF,OAAA,CAACtC,aAAa;QAACiI,EAAE,EAAE;UAAEgB,EAAE,EAAE,CAAC;UAAEkD,EAAE,EAAE;QAAE,CAAE;QAAAxE,QAAA,gBAClCrF,OAAA,CAACnD,MAAM;UACL6J,OAAO,EAAEyD,kBAAmB;UAC5B5D,OAAO,EAAC,UAAU;UAClBZ,EAAE,EAAE;YACFQ,aAAa,EAAE,MAAM;YACrBC,UAAU,EAAE,GAAG;YACfO,EAAE,EAAE;UACN,CAAE;UAAAtB,QAAA,EACH;QAED;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC,eACTzF,OAAA,CAACnD,MAAM;UACL6J,OAAO,EAAE0D,mBAAoB;UAC7B7D,OAAO,EAAC,WAAW;UACnBC,KAAK,EAAC,OAAO;UACbb,EAAE,EAAE;YACFQ,aAAa,EAAE,MAAM;YACrBC,UAAU,EAAE,GAAG;YACfO,EAAE,EAAE;UACN,CAAE;UAAAtB,QAAA,EACH;QAED;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACI,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACV,CAAC,eAGTzF,OAAA,CAACF,kBAAkB;MACjBI,UAAU,EAAEA,UAAW;MACvBkC,IAAI,EAAErB,eAAgB;MACtB0I,OAAO,EAAEA,CAAA,KAAMzI,kBAAkB,CAAC,KAAK,CAAE;MACzCqJ,SAAS,EAAEA,CAAA,KAAM;QACf9H,WAAW,CAAC,CAAC;QACbC,eAAe,CAAC,CAAC;QACjBC,gBAAgB,CAAC,CAAC;QAClBzB,kBAAkB,CAAC,KAAK,CAAC;MAC3B;IAAE;MAAAsE,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACC,CAAC;AAEV,CAAC;AAACrF,EAAA,CA5wBIH,wBAAwB;AAAAqK,EAAA,GAAxBrK,wBAAwB;AA8wB9B,eAAeA,wBAAwB;AAAC,IAAAqK,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}