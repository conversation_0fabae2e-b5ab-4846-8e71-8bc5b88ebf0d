{"ast": null, "code": "const formatDistanceLocale = {\n  lessThanXSeconds: {\n    one: \"секунд хүрэхгүй\",\n    other: \"{{count}} секунд хүрэхгүй\"\n  },\n  xSeconds: {\n    one: \"1 секунд\",\n    other: \"{{count}} секунд\"\n  },\n  halfAMinute: \"хагас минут\",\n  lessThanXMinutes: {\n    one: \"минут хүрэхгүй\",\n    other: \"{{count}} минут хүрэхгүй\"\n  },\n  xMinutes: {\n    one: \"1 минут\",\n    other: \"{{count}} минут\"\n  },\n  aboutXHours: {\n    one: \"ойролцоогоор 1 цаг\",\n    other: \"ойролцоогоор {{count}} цаг\"\n  },\n  xHours: {\n    one: \"1 цаг\",\n    other: \"{{count}} цаг\"\n  },\n  xDays: {\n    one: \"1 өдөр\",\n    other: \"{{count}} өдөр\"\n  },\n  aboutXWeeks: {\n    one: \"ойролцоогоор 1 долоо хоног\",\n    other: \"ойролцоогоор {{count}} долоо хоног\"\n  },\n  xWeeks: {\n    one: \"1 долоо хоног\",\n    other: \"{{count}} долоо хоног\"\n  },\n  aboutXMonths: {\n    one: \"ойролцоогоор 1 сар\",\n    other: \"ойролцоогоор {{count}} сар\"\n  },\n  xMonths: {\n    one: \"1 сар\",\n    other: \"{{count}} сар\"\n  },\n  aboutXYears: {\n    one: \"ойролцоогоор 1 жил\",\n    other: \"ойролцоогоор {{count}} жил\"\n  },\n  xYears: {\n    one: \"1 жил\",\n    other: \"{{count}} жил\"\n  },\n  overXYears: {\n    one: \"1 жил гаран\",\n    other: \"{{count}} жил гаран\"\n  },\n  almostXYears: {\n    one: \"бараг 1 жил\",\n    other: \"бараг {{count}} жил\"\n  }\n};\nexport const formatDistance = (token, count, options) => {\n  let result;\n  const tokenValue = formatDistanceLocale[token];\n  if (typeof tokenValue === \"string\") {\n    result = tokenValue;\n  } else if (count === 1) {\n    result = tokenValue.one;\n  } else {\n    result = tokenValue.other.replace(\"{{count}}\", String(count));\n  }\n  if (options?.addSuffix) {\n    /**\n     * Append genitive case\n     */\n    const words = result.split(\" \");\n    const lastword = words.pop();\n    result = words.join(\" \");\n    switch (lastword) {\n      case \"секунд\":\n        result += \" секундийн\";\n        break;\n      case \"минут\":\n        result += \" минутын\";\n        break;\n      case \"цаг\":\n        result += \" цагийн\";\n        break;\n      case \"өдөр\":\n        result += \" өдрийн\";\n        break;\n      case \"сар\":\n        result += \" сарын\";\n        break;\n      case \"жил\":\n        result += \" жилийн\";\n        break;\n      case \"хоног\":\n        result += \" хоногийн\";\n        break;\n      case \"гаран\":\n        result += \" гараны\";\n        break;\n      case \"хүрэхгүй\":\n        result += \" хүрэхгүй хугацааны\";\n        break;\n      default:\n        result += lastword + \"-н\";\n    }\n    if (options.comparison && options.comparison > 0) {\n      return result + \" дараа\";\n    } else {\n      return result + \" өмнө\";\n    }\n  }\n  return result;\n};", "map": {"version": 3, "names": ["formatDistanceLocale", "lessThanXSeconds", "one", "other", "xSeconds", "halfAMinute", "lessThanXMinutes", "xMinutes", "aboutXHours", "xHours", "xDays", "aboutXWeeks", "xWeeks", "aboutXMonths", "xMonths", "aboutXYears", "xYears", "overXYears", "almostXYears", "formatDistance", "token", "count", "options", "result", "tokenValue", "replace", "String", "addSuffix", "words", "split", "lastword", "pop", "join", "comparison"], "sources": ["C:/CMS/webapp/frontend/node_modules/date-fns/locale/mn/_lib/formatDistance.mjs"], "sourcesContent": ["const formatDistanceLocale = {\n  lessThanXSeconds: {\n    one: \"секунд хүрэхгүй\",\n    other: \"{{count}} секунд хүрэхгүй\",\n  },\n\n  xSeconds: {\n    one: \"1 секунд\",\n    other: \"{{count}} секунд\",\n  },\n\n  halfAMinute: \"хагас минут\",\n\n  lessThanXMinutes: {\n    one: \"минут хүрэхгүй\",\n    other: \"{{count}} минут хүрэхгүй\",\n  },\n\n  xMinutes: {\n    one: \"1 минут\",\n    other: \"{{count}} минут\",\n  },\n\n  aboutXHours: {\n    one: \"ойролцоогоор 1 цаг\",\n    other: \"ойролцоогоор {{count}} цаг\",\n  },\n\n  xHours: {\n    one: \"1 цаг\",\n    other: \"{{count}} цаг\",\n  },\n\n  xDays: {\n    one: \"1 өдөр\",\n    other: \"{{count}} өдөр\",\n  },\n\n  aboutXWeeks: {\n    one: \"ойролцоогоор 1 долоо хоног\",\n    other: \"ойролцоогоор {{count}} долоо хоног\",\n  },\n\n  xWeeks: {\n    one: \"1 долоо хоног\",\n    other: \"{{count}} долоо хоног\",\n  },\n\n  aboutXMonths: {\n    one: \"ойролцоогоор 1 сар\",\n    other: \"ойролцоогоор {{count}} сар\",\n  },\n\n  xMonths: {\n    one: \"1 сар\",\n    other: \"{{count}} сар\",\n  },\n\n  aboutXYears: {\n    one: \"ойролцоогоор 1 жил\",\n    other: \"ойролцоогоор {{count}} жил\",\n  },\n\n  xYears: {\n    one: \"1 жил\",\n    other: \"{{count}} жил\",\n  },\n\n  overXYears: {\n    one: \"1 жил гаран\",\n    other: \"{{count}} жил гаран\",\n  },\n\n  almostXYears: {\n    one: \"бараг 1 жил\",\n    other: \"бараг {{count}} жил\",\n  },\n};\n\nexport const formatDistance = (token, count, options) => {\n  let result;\n\n  const tokenValue = formatDistanceLocale[token];\n  if (typeof tokenValue === \"string\") {\n    result = tokenValue;\n  } else if (count === 1) {\n    result = tokenValue.one;\n  } else {\n    result = tokenValue.other.replace(\"{{count}}\", String(count));\n  }\n\n  if (options?.addSuffix) {\n    /**\n     * Append genitive case\n     */\n    const words = result.split(\" \");\n    const lastword = words.pop();\n    result = words.join(\" \");\n    switch (lastword) {\n      case \"секунд\":\n        result += \" секундийн\";\n        break;\n      case \"минут\":\n        result += \" минутын\";\n        break;\n      case \"цаг\":\n        result += \" цагийн\";\n        break;\n      case \"өдөр\":\n        result += \" өдрийн\";\n        break;\n      case \"сар\":\n        result += \" сарын\";\n        break;\n      case \"жил\":\n        result += \" жилийн\";\n        break;\n      case \"хоног\":\n        result += \" хоногийн\";\n        break;\n      case \"гаран\":\n        result += \" гараны\";\n        break;\n      case \"хүрэхгүй\":\n        result += \" хүрэхгүй хугацааны\";\n        break;\n      default:\n        result += lastword + \"-н\";\n    }\n\n    if (options.comparison && options.comparison > 0) {\n      return result + \" дараа\";\n    } else {\n      return result + \" өмнө\";\n    }\n  }\n\n  return result;\n};\n"], "mappings": "AAAA,MAAMA,oBAAoB,GAAG;EAC3BC,gBAAgB,EAAE;IAChBC,GAAG,EAAE,iBAAiB;IACtBC,KAAK,EAAE;EACT,CAAC;EAEDC,QAAQ,EAAE;IACRF,GAAG,EAAE,UAAU;IACfC,KAAK,EAAE;EACT,CAAC;EAEDE,WAAW,EAAE,aAAa;EAE1BC,gBAAgB,EAAE;IAChBJ,GAAG,EAAE,gBAAgB;IACrBC,KAAK,EAAE;EACT,CAAC;EAEDI,QAAQ,EAAE;IACRL,GAAG,EAAE,SAAS;IACdC,KAAK,EAAE;EACT,CAAC;EAEDK,WAAW,EAAE;IACXN,GAAG,EAAE,oBAAoB;IACzBC,KAAK,EAAE;EACT,CAAC;EAEDM,MAAM,EAAE;IACNP,GAAG,EAAE,OAAO;IACZC,KAAK,EAAE;EACT,CAAC;EAEDO,KAAK,EAAE;IACLR,GAAG,EAAE,QAAQ;IACbC,KAAK,EAAE;EACT,CAAC;EAEDQ,WAAW,EAAE;IACXT,GAAG,EAAE,4BAA4B;IACjCC,KAAK,EAAE;EACT,CAAC;EAEDS,MAAM,EAAE;IACNV,GAAG,EAAE,eAAe;IACpBC,KAAK,EAAE;EACT,CAAC;EAEDU,YAAY,EAAE;IACZX,GAAG,EAAE,oBAAoB;IACzBC,KAAK,EAAE;EACT,CAAC;EAEDW,OAAO,EAAE;IACPZ,GAAG,EAAE,OAAO;IACZC,KAAK,EAAE;EACT,CAAC;EAEDY,WAAW,EAAE;IACXb,GAAG,EAAE,oBAAoB;IACzBC,KAAK,EAAE;EACT,CAAC;EAEDa,MAAM,EAAE;IACNd,GAAG,EAAE,OAAO;IACZC,KAAK,EAAE;EACT,CAAC;EAEDc,UAAU,EAAE;IACVf,GAAG,EAAE,aAAa;IAClBC,KAAK,EAAE;EACT,CAAC;EAEDe,YAAY,EAAE;IACZhB,GAAG,EAAE,aAAa;IAClBC,KAAK,EAAE;EACT;AACF,CAAC;AAED,OAAO,MAAMgB,cAAc,GAAGA,CAACC,KAAK,EAAEC,KAAK,EAAEC,OAAO,KAAK;EACvD,IAAIC,MAAM;EAEV,MAAMC,UAAU,GAAGxB,oBAAoB,CAACoB,KAAK,CAAC;EAC9C,IAAI,OAAOI,UAAU,KAAK,QAAQ,EAAE;IAClCD,MAAM,GAAGC,UAAU;EACrB,CAAC,MAAM,IAAIH,KAAK,KAAK,CAAC,EAAE;IACtBE,MAAM,GAAGC,UAAU,CAACtB,GAAG;EACzB,CAAC,MAAM;IACLqB,MAAM,GAAGC,UAAU,CAACrB,KAAK,CAACsB,OAAO,CAAC,WAAW,EAAEC,MAAM,CAACL,KAAK,CAAC,CAAC;EAC/D;EAEA,IAAIC,OAAO,EAAEK,SAAS,EAAE;IACtB;AACJ;AACA;IACI,MAAMC,KAAK,GAAGL,MAAM,CAACM,KAAK,CAAC,GAAG,CAAC;IAC/B,MAAMC,QAAQ,GAAGF,KAAK,CAACG,GAAG,CAAC,CAAC;IAC5BR,MAAM,GAAGK,KAAK,CAACI,IAAI,CAAC,GAAG,CAAC;IACxB,QAAQF,QAAQ;MACd,KAAK,QAAQ;QACXP,MAAM,IAAI,YAAY;QACtB;MACF,KAAK,OAAO;QACVA,MAAM,IAAI,UAAU;QACpB;MACF,KAAK,KAAK;QACRA,MAAM,IAAI,SAAS;QACnB;MACF,KAAK,MAAM;QACTA,MAAM,IAAI,SAAS;QACnB;MACF,KAAK,KAAK;QACRA,MAAM,IAAI,QAAQ;QAClB;MACF,KAAK,KAAK;QACRA,MAAM,IAAI,SAAS;QACnB;MACF,KAAK,OAAO;QACVA,MAAM,IAAI,WAAW;QACrB;MACF,KAAK,OAAO;QACVA,MAAM,IAAI,SAAS;QACnB;MACF,KAAK,UAAU;QACbA,MAAM,IAAI,qBAAqB;QAC/B;MACF;QACEA,MAAM,IAAIO,QAAQ,GAAG,IAAI;IAC7B;IAEA,IAAIR,OAAO,CAACW,UAAU,IAAIX,OAAO,CAACW,UAAU,GAAG,CAAC,EAAE;MAChD,OAAOV,MAAM,GAAG,QAAQ;IAC1B,CAAC,MAAM;MACL,OAAOA,MAAM,GAAG,OAAO;IACzB;EACF;EAEA,OAAOA,MAAM;AACf,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}