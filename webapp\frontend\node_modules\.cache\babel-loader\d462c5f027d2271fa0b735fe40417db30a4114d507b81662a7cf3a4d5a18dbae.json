{"ast": null, "code": "const formatDistanceLocale = {\n  lessThanXSeconds: {\n    one: \"פחות משנייה\",\n    two: \"פחות משתי שניות\",\n    other: \"פחות מ־{{count}} שניות\"\n  },\n  xSeconds: {\n    one: \"שנייה\",\n    two: \"שתי שניות\",\n    other: \"{{count}} שניות\"\n  },\n  halfAMinute: \"חצי דקה\",\n  lessThanXMinutes: {\n    one: \"פחות מדקה\",\n    two: \"פחות משתי דקות\",\n    other: \"פחות מ־{{count}} דקות\"\n  },\n  xMinutes: {\n    one: \"דקה\",\n    two: \"שתי דקות\",\n    other: \"{{count}} דקות\"\n  },\n  aboutXHours: {\n    one: \"כשעה\",\n    two: \"כשעתיים\",\n    other: \"כ־{{count}} שעות\"\n  },\n  xHours: {\n    one: \"שעה\",\n    two: \"שעתיים\",\n    other: \"{{count}} שעות\"\n  },\n  xDays: {\n    one: \"יום\",\n    two: \"יומיים\",\n    other: \"{{count}} ימים\"\n  },\n  aboutXWeeks: {\n    one: \"כשבוע\",\n    two: \"כשבועיים\",\n    other: \"כ־{{count}} שבועות\"\n  },\n  xWeeks: {\n    one: \"שבוע\",\n    two: \"שבועיים\",\n    other: \"{{count}} שבועות\"\n  },\n  aboutXMonths: {\n    one: \"כחודש\",\n    two: \"כחודשיים\",\n    other: \"כ־{{count}} חודשים\"\n  },\n  xMonths: {\n    one: \"חודש\",\n    two: \"חודשיים\",\n    other: \"{{count}} חודשים\"\n  },\n  aboutXYears: {\n    one: \"כשנה\",\n    two: \"כשנתיים\",\n    other: \"כ־{{count}} שנים\"\n  },\n  xYears: {\n    one: \"שנה\",\n    two: \"שנתיים\",\n    other: \"{{count}} שנים\"\n  },\n  overXYears: {\n    one: \"יותר משנה\",\n    two: \"יותר משנתיים\",\n    other: \"יותר מ־{{count}} שנים\"\n  },\n  almostXYears: {\n    one: \"כמעט שנה\",\n    two: \"כמעט שנתיים\",\n    other: \"כמעט {{count}} שנים\"\n  }\n};\nexport const formatDistance = (token, count, options) => {\n  // Return word instead of `in one day` or `one day ago`\n  if (token === \"xDays\" && options?.addSuffix && count <= 2) {\n    if (options.comparison && options.comparison > 0) {\n      return count === 1 ? \"מחר\" : \"מחרתיים\";\n    }\n    return count === 1 ? \"אתמול\" : \"שלשום\";\n  }\n  let result;\n  const tokenValue = formatDistanceLocale[token];\n  if (typeof tokenValue === \"string\") {\n    result = tokenValue;\n  } else if (count === 1) {\n    result = tokenValue.one;\n  } else if (count === 2) {\n    result = tokenValue.two;\n  } else {\n    result = tokenValue.other.replace(\"{{count}}\", String(count));\n  }\n  if (options?.addSuffix) {\n    if (options.comparison && options.comparison > 0) {\n      return \"בעוד \" + result;\n    } else {\n      return \"לפני \" + result;\n    }\n  }\n  return result;\n};", "map": {"version": 3, "names": ["formatDistanceLocale", "lessThanXSeconds", "one", "two", "other", "xSeconds", "halfAMinute", "lessThanXMinutes", "xMinutes", "aboutXHours", "xHours", "xDays", "aboutXWeeks", "xWeeks", "aboutXMonths", "xMonths", "aboutXYears", "xYears", "overXYears", "almostXYears", "formatDistance", "token", "count", "options", "addSuffix", "comparison", "result", "tokenValue", "replace", "String"], "sources": ["C:/CMS/webapp/frontend/node_modules/date-fns/locale/he/_lib/formatDistance.mjs"], "sourcesContent": ["const formatDistanceLocale = {\n  lessThanXSeconds: {\n    one: \"פחות משנייה\",\n    two: \"פחות משתי שניות\",\n    other: \"פחות מ־{{count}} שניות\",\n  },\n\n  xSeconds: {\n    one: \"שנייה\",\n    two: \"שתי שניות\",\n    other: \"{{count}} שניות\",\n  },\n\n  halfAMinute: \"חצי דקה\",\n\n  lessThanXMinutes: {\n    one: \"פחות מדקה\",\n    two: \"פחות משתי דקות\",\n    other: \"פחות מ־{{count}} דקות\",\n  },\n\n  xMinutes: {\n    one: \"דקה\",\n    two: \"שתי דקות\",\n    other: \"{{count}} דקות\",\n  },\n\n  aboutXHours: {\n    one: \"כשעה\",\n    two: \"כשעתיים\",\n    other: \"כ־{{count}} שעות\",\n  },\n\n  xHours: {\n    one: \"שעה\",\n    two: \"שעתיים\",\n    other: \"{{count}} שעות\",\n  },\n\n  xDays: {\n    one: \"יום\",\n    two: \"יומיים\",\n    other: \"{{count}} ימים\",\n  },\n\n  aboutXWeeks: {\n    one: \"כשבוע\",\n    two: \"כשבועיים\",\n    other: \"כ־{{count}} שבועות\",\n  },\n\n  xWeeks: {\n    one: \"שבוע\",\n    two: \"שבועיים\",\n    other: \"{{count}} שבועות\",\n  },\n\n  aboutXMonths: {\n    one: \"כחודש\",\n    two: \"כחודשיים\",\n    other: \"כ־{{count}} חודשים\",\n  },\n\n  xMonths: {\n    one: \"חודש\",\n    two: \"חודשיים\",\n    other: \"{{count}} חודשים\",\n  },\n\n  aboutXYears: {\n    one: \"כשנה\",\n    two: \"כשנתיים\",\n    other: \"כ־{{count}} שנים\",\n  },\n\n  xYears: {\n    one: \"שנה\",\n    two: \"שנתיים\",\n    other: \"{{count}} שנים\",\n  },\n\n  overXYears: {\n    one: \"יותר משנה\",\n    two: \"יותר משנתיים\",\n    other: \"יותר מ־{{count}} שנים\",\n  },\n\n  almostXYears: {\n    one: \"כמעט שנה\",\n    two: \"כמעט שנתיים\",\n    other: \"כמעט {{count}} שנים\",\n  },\n};\n\nexport const formatDistance = (token, count, options) => {\n  // Return word instead of `in one day` or `one day ago`\n  if (token === \"xDays\" && options?.addSuffix && count <= 2) {\n    if (options.comparison && options.comparison > 0) {\n      return count === 1 ? \"מחר\" : \"מחרתיים\";\n    }\n\n    return count === 1 ? \"אתמול\" : \"שלשום\";\n  }\n\n  let result;\n\n  const tokenValue = formatDistanceLocale[token];\n  if (typeof tokenValue === \"string\") {\n    result = tokenValue;\n  } else if (count === 1) {\n    result = tokenValue.one;\n  } else if (count === 2) {\n    result = tokenValue.two;\n  } else {\n    result = tokenValue.other.replace(\"{{count}}\", String(count));\n  }\n\n  if (options?.addSuffix) {\n    if (options.comparison && options.comparison > 0) {\n      return \"בעוד \" + result;\n    } else {\n      return \"לפני \" + result;\n    }\n  }\n\n  return result;\n};\n"], "mappings": "AAAA,MAAMA,oBAAoB,GAAG;EAC3BC,gBAAgB,EAAE;IAChBC,GAAG,EAAE,aAAa;IAClBC,GAAG,EAAE,iBAAiB;IACtBC,KAAK,EAAE;EACT,CAAC;EAEDC,QAAQ,EAAE;IACRH,GAAG,EAAE,OAAO;IACZC,GAAG,EAAE,WAAW;IAChBC,KAAK,EAAE;EACT,CAAC;EAEDE,WAAW,EAAE,SAAS;EAEtBC,gBAAgB,EAAE;IAChBL,GAAG,EAAE,WAAW;IAChBC,GAAG,EAAE,gBAAgB;IACrBC,KAAK,EAAE;EACT,CAAC;EAEDI,QAAQ,EAAE;IACRN,GAAG,EAAE,KAAK;IACVC,GAAG,EAAE,UAAU;IACfC,KAAK,EAAE;EACT,CAAC;EAEDK,WAAW,EAAE;IACXP,GAAG,EAAE,MAAM;IACXC,GAAG,EAAE,SAAS;IACdC,KAAK,EAAE;EACT,CAAC;EAEDM,MAAM,EAAE;IACNR,GAAG,EAAE,KAAK;IACVC,GAAG,EAAE,QAAQ;IACbC,KAAK,EAAE;EACT,CAAC;EAEDO,KAAK,EAAE;IACLT,GAAG,EAAE,KAAK;IACVC,GAAG,EAAE,QAAQ;IACbC,KAAK,EAAE;EACT,CAAC;EAEDQ,WAAW,EAAE;IACXV,GAAG,EAAE,OAAO;IACZC,GAAG,EAAE,UAAU;IACfC,KAAK,EAAE;EACT,CAAC;EAEDS,MAAM,EAAE;IACNX,GAAG,EAAE,MAAM;IACXC,GAAG,EAAE,SAAS;IACdC,KAAK,EAAE;EACT,CAAC;EAEDU,YAAY,EAAE;IACZZ,GAAG,EAAE,OAAO;IACZC,GAAG,EAAE,UAAU;IACfC,KAAK,EAAE;EACT,CAAC;EAEDW,OAAO,EAAE;IACPb,GAAG,EAAE,MAAM;IACXC,GAAG,EAAE,SAAS;IACdC,KAAK,EAAE;EACT,CAAC;EAEDY,WAAW,EAAE;IACXd,GAAG,EAAE,MAAM;IACXC,GAAG,EAAE,SAAS;IACdC,KAAK,EAAE;EACT,CAAC;EAEDa,MAAM,EAAE;IACNf,GAAG,EAAE,KAAK;IACVC,GAAG,EAAE,QAAQ;IACbC,KAAK,EAAE;EACT,CAAC;EAEDc,UAAU,EAAE;IACVhB,GAAG,EAAE,WAAW;IAChBC,GAAG,EAAE,cAAc;IACnBC,KAAK,EAAE;EACT,CAAC;EAEDe,YAAY,EAAE;IACZjB,GAAG,EAAE,UAAU;IACfC,GAAG,EAAE,aAAa;IAClBC,KAAK,EAAE;EACT;AACF,CAAC;AAED,OAAO,MAAMgB,cAAc,GAAGA,CAACC,KAAK,EAAEC,KAAK,EAAEC,OAAO,KAAK;EACvD;EACA,IAAIF,KAAK,KAAK,OAAO,IAAIE,OAAO,EAAEC,SAAS,IAAIF,KAAK,IAAI,CAAC,EAAE;IACzD,IAAIC,OAAO,CAACE,UAAU,IAAIF,OAAO,CAACE,UAAU,GAAG,CAAC,EAAE;MAChD,OAAOH,KAAK,KAAK,CAAC,GAAG,KAAK,GAAG,SAAS;IACxC;IAEA,OAAOA,KAAK,KAAK,CAAC,GAAG,OAAO,GAAG,OAAO;EACxC;EAEA,IAAII,MAAM;EAEV,MAAMC,UAAU,GAAG3B,oBAAoB,CAACqB,KAAK,CAAC;EAC9C,IAAI,OAAOM,UAAU,KAAK,QAAQ,EAAE;IAClCD,MAAM,GAAGC,UAAU;EACrB,CAAC,MAAM,IAAIL,KAAK,KAAK,CAAC,EAAE;IACtBI,MAAM,GAAGC,UAAU,CAACzB,GAAG;EACzB,CAAC,MAAM,IAAIoB,KAAK,KAAK,CAAC,EAAE;IACtBI,MAAM,GAAGC,UAAU,CAACxB,GAAG;EACzB,CAAC,MAAM;IACLuB,MAAM,GAAGC,UAAU,CAACvB,KAAK,CAACwB,OAAO,CAAC,WAAW,EAAEC,MAAM,CAACP,KAAK,CAAC,CAAC;EAC/D;EAEA,IAAIC,OAAO,EAAEC,SAAS,EAAE;IACtB,IAAID,OAAO,CAACE,UAAU,IAAIF,OAAO,CAACE,UAAU,GAAG,CAAC,EAAE;MAChD,OAAO,OAAO,GAAGC,MAAM;IACzB,CAAC,MAAM;MACL,OAAO,OAAO,GAAGA,MAAM;IACzB;EACF;EAEA,OAAOA,MAAM;AACf,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}