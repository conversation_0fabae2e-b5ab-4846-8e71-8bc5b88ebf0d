{"ast": null, "code": "let defaultOptions = {};\nexport function getDefaultOptions() {\n  return defaultOptions;\n}\nexport function setDefaultOptions(newOptions) {\n  defaultOptions = newOptions;\n}", "map": {"version": 3, "names": ["defaultOptions", "getDefaultOptions", "setDefaultOptions", "newOptions"], "sources": ["C:/CMS/webapp/frontend/node_modules/date-fns/_lib/defaultOptions.js"], "sourcesContent": ["let defaultOptions = {};\n\nexport function getDefaultOptions() {\n  return defaultOptions;\n}\n\nexport function setDefaultOptions(newOptions) {\n  defaultOptions = newOptions;\n}\n"], "mappings": "AAAA,IAAIA,cAAc,GAAG,CAAC,CAAC;AAEvB,OAAO,SAASC,iBAAiBA,CAAA,EAAG;EAClC,OAAOD,cAAc;AACvB;AAEA,OAAO,SAASE,iBAAiBA,CAACC,UAAU,EAAE;EAC5CH,cAAc,GAAGG,UAAU;AAC7B", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}