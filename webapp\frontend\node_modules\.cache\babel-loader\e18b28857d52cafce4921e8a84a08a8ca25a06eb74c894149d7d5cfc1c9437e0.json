{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\PycharmProjects\\\\CMS\\\\webapp\\\\frontend\\\\src\\\\pages\\\\cavi\\\\VisualizzaCaviPage.js\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect } from 'react';\nimport { Box, Typography, Paper, Button, Grid, Card, CardContent, Alert, IconButton, Table, TableBody, TableCell, TableContainer, TableHead, TableRow, Tabs, Tab, TextField, MenuItem, Select, FormControl, InputLabel, Chip, Divider, CircularProgress, LinearProgress, Dialog, DialogTitle, DialogContent, DialogActions } from '@mui/material';\nimport { ArrowBack as ArrowBackIcon, Refresh as RefreshIcon, Home as HomeIcon, ViewList as ViewListIcon, ViewModule as ViewModuleIcon, FilterList as FilterListIcon, Sort as SortIcon, Info as InfoIcon, Search as SearchIcon, Clear as ClearIcon } from '@mui/icons-material';\nimport { useNavigate } from 'react-router-dom';\nimport { useAuth } from '../../context/AuthContext';\nimport caviService from '../../services/caviService';\nimport { normalizeInstallationStatus } from '../../utils/validationUtils';\nimport './CaviPage.css';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst VisualizzaCaviPage = () => {\n  _s();\n  var _sortOptions$find;\n  const {\n    isImpersonating,\n    user\n  } = useAuth();\n  const navigate = useNavigate();\n  const [cantiereId, setCantiereId] = useState(null);\n  const [cantiereName, setCantiereName] = useState('');\n  const [caviAttivi, setCaviAttivi] = useState([]);\n  const [caviSpare, setCaviSpare] = useState([]);\n  const [loading, setLoading] = useState(true);\n  const [error, setError] = useState(null);\n  const [viewMode, setViewMode] = useState('table'); // 'table' o 'card'\n\n  // Stato per filtri e ordinamento\n  const [filters, setFilters] = useState({\n    stato_installazione: '',\n    tipologia: '',\n    sort_by: '',\n    sort_order: 'asc'\n  });\n\n  // Stato per il dialogo dei dettagli del cavo\n  const [selectedCavo, setSelectedCavo] = useState(null);\n  const [detailsDialogOpen, setDetailsDialogOpen] = useState(false);\n\n  // Stato per le statistiche\n  const [stats, setStats] = useState(null);\n  const [loadingStats, setLoadingStats] = useState(false);\n\n  // Opzioni per i filtri\n  const [statiInstallazione, setStatiInstallazione] = useState([]);\n  const [tipologieCavi, setTipologieCavi] = useState([]);\n\n  // Opzioni di ordinamento\n  const sortOptions = [{\n    value: 'id_cavo',\n    label: 'ID Cavo'\n  }, {\n    value: 'metratura_reale',\n    label: 'Metratura Reale'\n  }, {\n    value: 'metri_teorici',\n    label: 'Metri Teorici'\n  }, {\n    value: 'stato_installazione',\n    label: 'Stato Installazione'\n  }, {\n    value: 'tipologia',\n    label: 'Tipologia'\n  }, {\n    value: 'timestamp',\n    label: 'Data Aggiornamento'\n  }];\n\n  // Stato per il pannello dei filtri\n  const [filtersOpen, setFiltersOpen] = useState(false);\n\n  // Carica i dati del cantiere e dei cavi\n  useEffect(() => {\n    const fetchData = async () => {\n      try {\n        console.log('Inizializzazione VisualizzaCaviPage...');\n\n        // Verifica che l'utente sia autenticato\n        const token = localStorage.getItem('token');\n        console.log('Token presente:', !!token);\n        if (!token) {\n          setError('Sessione scaduta. Effettua nuovamente il login.');\n          setLoading(false);\n          return;\n        }\n\n        // Recupera l'ID del cantiere selezionato dal localStorage\n        let selectedCantiereId = localStorage.getItem('selectedCantiereId');\n        let selectedCantiereName = localStorage.getItem('selectedCantiereName');\n        console.log('Cantiere selezionato dal localStorage:', {\n          selectedCantiereId,\n          selectedCantiereName\n        });\n        console.log('Dati utente:', user);\n\n        // Stampa tutti i dati nel localStorage per debug\n        console.log('DEBUG - Tutti i dati nel localStorage:');\n        for (let i = 0; i < localStorage.length; i++) {\n          const key = localStorage.key(i);\n          console.log(`${key}: ${localStorage.getItem(key)}`);\n        }\n\n        // SOLUZIONE DIRETTA: Ottieni l'ID del cantiere direttamente dal token JWT\n        if ((user === null || user === void 0 ? void 0 : user.role) === 'cantieri_user') {\n          console.log('Utente cantiere rilevato, tentativo di recupero ID cantiere dai dati utente');\n\n          // Verifica se l'utente ha un ID cantiere nei dati utente\n          if (user.cantiere_id) {\n            console.log('Trovato ID cantiere nei dati utente:', user.cantiere_id);\n            selectedCantiereId = user.cantiere_id.toString();\n            selectedCantiereName = user.cantiere_name || `Cantiere ${user.cantiere_id}`;\n\n            // Salva l'ID e il nome del cantiere nel localStorage\n            localStorage.setItem('selectedCantiereId', selectedCantiereId);\n            localStorage.setItem('selectedCantiereName', selectedCantiereName);\n            console.log('Salvato ID cantiere nel localStorage:', selectedCantiereId);\n          } else {\n            // Tentativo di recupero dal token JWT\n            try {\n              console.log('Tentativo di decodifica del token JWT per recuperare l\\'ID cantiere');\n              const token = localStorage.getItem('token');\n              if (token) {\n                // Decodifica il token JWT (senza verifica della firma)\n                const base64Url = token.split('.')[1];\n                const base64 = base64Url.replace(/-/g, '+').replace(/_/g, '/');\n                const jsonPayload = decodeURIComponent(atob(base64).split('').map(c => {\n                  return '%' + ('00' + c.charCodeAt(0).toString(16)).slice(-2);\n                }).join(''));\n                const payload = JSON.parse(jsonPayload);\n                console.log('Payload del token JWT:', payload);\n                if (payload.cantiere_id) {\n                  console.log('Trovato ID cantiere nel token JWT:', payload.cantiere_id);\n                  selectedCantiereId = payload.cantiere_id.toString();\n                  // Usa un nome generico se non disponibile\n                  selectedCantiereName = `Cantiere ${payload.cantiere_id}`;\n\n                  // Salva l'ID e il nome del cantiere nel localStorage\n                  localStorage.setItem('selectedCantiereId', selectedCantiereId);\n                  localStorage.setItem('selectedCantiereName', selectedCantiereName);\n                  console.log('Salvato ID cantiere nel localStorage:', selectedCantiereId);\n                }\n              }\n            } catch (e) {\n              console.error('Errore durante la decodifica del token JWT:', e);\n            }\n          }\n        }\n\n        // SOLUZIONE TEMPORANEA: Se ancora non abbiamo un ID cantiere, usa un valore hardcoded per debug\n        if (!selectedCantiereId || selectedCantiereId === 'undefined' || selectedCantiereId === 'null') {\n          console.warn('ATTENZIONE: Nessun ID cantiere trovato, utilizzo valore hardcoded per debug');\n          // Usa il primo cantiere disponibile (questo è solo per debug)\n          selectedCantiereId = '1'; // Sostituisci con un ID cantiere valido nel tuo database\n          selectedCantiereName = 'Cantiere Debug';\n\n          // Salva l'ID e il nome del cantiere nel localStorage\n          localStorage.setItem('selectedCantiereId', selectedCantiereId);\n          localStorage.setItem('selectedCantiereName', selectedCantiereName);\n          console.log('Salvato ID cantiere hardcoded nel localStorage:', selectedCantiereId);\n        }\n\n        // Verifica finale\n        if (!selectedCantiereId) {\n          setError('Nessun cantiere selezionato. Torna alla pagina dei cantieri.');\n          setLoading(false);\n          return;\n        }\n\n        // Verifica che l'ID del cantiere sia un numero valido\n        const cantiereIdNum = parseInt(selectedCantiereId, 10);\n        console.log('ID cantiere convertito a numero:', cantiereIdNum);\n        if (isNaN(cantiereIdNum)) {\n          setError(`ID cantiere non valido: ${selectedCantiereId}. Torna alla pagina dei cantieri.`);\n          setLoading(false);\n          return;\n        }\n\n        // Usa il numero convertito, non la stringa\n        setCantiereId(cantiereIdNum);\n        setCantiereName(selectedCantiereName || `Cantiere ${cantiereIdNum}`);\n\n        // Carica le statistiche dei cavi\n        try {\n          setLoadingStats(true);\n          console.log('Caricamento statistiche cavi per cantiere:', cantiereIdNum);\n          const statsData = await caviService.getCaviStats(cantiereIdNum);\n          console.log('Statistiche cavi caricate:', statsData);\n          setStats(statsData);\n\n          // Estrai gli stati di installazione e le tipologie per i filtri\n          if (statsData && statsData.stati) {\n            const stati = statsData.stati.map(item => item.stato).filter(stato => stato !== 'Non specificato');\n            setStatiInstallazione(stati);\n          }\n          if (statsData && statsData.tipologie) {\n            const tipologie = statsData.tipologie.map(item => item.tipologia).filter(tipo => tipo !== 'Non specificata');\n            setTipologieCavi(tipologie);\n          }\n          setLoadingStats(false);\n        } catch (statsError) {\n          console.error('Errore nel caricamento delle statistiche:', statsError);\n          setLoadingStats(false);\n          // Non interrompere il flusso se le statistiche falliscono\n        }\n\n        // Carica i cavi attivi con gestione degli errori migliorata\n        console.log('Caricamento cavi attivi per cantiere:', cantiereIdNum);\n        try {\n          // Imposta un timeout per evitare che la richiesta rimanga bloccata\n          const timeoutPromise = new Promise((_, reject) => {\n            setTimeout(() => reject(new Error('Timeout durante il caricamento dei cavi attivi')), 30000); // Aumentato a 30 secondi\n          });\n\n          // Esegui la richiesta con un timeout di sicurezza e applica i filtri\n          console.log('Iniziando chiamata API per cavi attivi con filtri:', filters);\n          const caviPromise = caviService.getCavi(cantiereIdNum, 0, filters);\n          const attivi = await Promise.race([caviPromise, timeoutPromise]);\n          console.log('Cavi attivi caricati:', attivi);\n          console.log('Numero di cavi attivi trovati:', attivi ? attivi.length : 0);\n          if (attivi && attivi.length > 0) {\n            console.log('Primo cavo attivo:', attivi[0]);\n          } else {\n            console.warn('Nessun cavo attivo trovato per il cantiere', cantiereIdNum);\n          }\n          setCaviAttivi(attivi || []);\n        } catch (caviError) {\n          console.error('Errore nel caricamento dei cavi attivi:', caviError);\n          console.error('Dettagli errore cavi attivi:', {\n            message: caviError.message,\n            status: caviError.status,\n            data: caviError.data,\n            stack: caviError.stack,\n            code: caviError.code,\n            name: caviError.name,\n            response: caviError.response ? {\n              status: caviError.response.status,\n              statusText: caviError.response.statusText,\n              data: caviError.response.data\n            } : 'No response'\n          });\n\n          // Non interrompere il flusso, continua con i cavi spare\n          setCaviAttivi([]);\n          console.warn('Continuazione del flusso dopo errore nei cavi attivi');\n\n          // Aggiungi un messaggio di errore visibile all'utente\n          setError(`Errore nel caricamento dei cavi attivi: ${caviError.message}. Controlla la console per maggiori dettagli.`);\n        }\n\n        // Carica i cavi spare con gestione degli errori migliorata\n        console.log('Caricamento cavi spare per cantiere:', cantiereIdNum);\n        try {\n          // Imposta un timeout per evitare che la richiesta rimanga bloccata\n          const timeoutPromise = new Promise((_, reject) => {\n            setTimeout(() => reject(new Error('Timeout durante il caricamento dei cavi spare')), 30000); // Aumentato a 30 secondi\n          });\n\n          // Esegui la richiesta con un timeout di sicurezza\n          console.log('Iniziando chiamata API per cavi spare...');\n          // Non applichiamo i filtri ai cavi spare, solo agli attivi\n          const sparePromise = caviService.getCavi(cantiereIdNum, 3);\n          const spare = await Promise.race([sparePromise, timeoutPromise]);\n          console.log('Cavi spare caricati:', spare);\n          console.log('Numero di cavi spare trovati:', spare ? spare.length : 0);\n          if (spare && spare.length > 0) {\n            console.log('Primo cavo spare:', spare[0]);\n          } else {\n            console.warn('Nessun cavo spare trovato per il cantiere', cantiereIdNum);\n          }\n          setCaviSpare(spare || []);\n        } catch (spareError) {\n          console.error('Errore nel caricamento dei cavi spare:', spareError);\n          console.error('Dettagli errore cavi spare:', {\n            message: spareError.message,\n            status: spareError.status,\n            data: spareError.data,\n            stack: spareError.stack,\n            code: spareError.code,\n            name: spareError.name,\n            response: spareError.response ? {\n              status: spareError.response.status,\n              statusText: spareError.response.statusText,\n              data: spareError.response.data\n            } : 'No response'\n          });\n\n          // Non interrompere il flusso, imposta un array vuoto\n          setCaviSpare([]);\n\n          // Aggiungi un messaggio di errore visibile all'utente se non c'è già un errore per i cavi attivi\n          if (!error) {\n            setError(`Errore nel caricamento dei cavi spare: ${spareError.message}. Controlla la console per maggiori dettagli.`);\n          }\n        }\n\n        // Se siamo arrivati qui, almeno abbiamo caricato l'interfaccia di base\n        setLoading(false);\n      } catch (err) {\n        var _err$response, _err$response2, _err$response3, _err$response4, _err$response5, _err$response5$data;\n        console.error('Errore nel caricamento dei cavi:', err);\n        console.error('Dettagli errore generale:', {\n          message: err.message,\n          status: err.status || ((_err$response = err.response) === null || _err$response === void 0 ? void 0 : _err$response.status),\n          data: err.data || ((_err$response2 = err.response) === null || _err$response2 === void 0 ? void 0 : _err$response2.data),\n          stack: err.stack\n        });\n\n        // Estrai il messaggio di errore dettagliato\n        let errorMessage = 'Errore sconosciuto';\n        if (err.message && err.message.includes('ID cantiere non valido')) {\n          errorMessage = err.message;\n        } else if (err.status === 401 || err.status === 403 || ((_err$response3 = err.response) === null || _err$response3 === void 0 ? void 0 : _err$response3.status) === 401 || ((_err$response4 = err.response) === null || _err$response4 === void 0 ? void 0 : _err$response4.status) === 403) {\n          errorMessage = 'Sessione scaduta o non autorizzata. Effettua nuovamente il login.';\n        } else if ((_err$response5 = err.response) !== null && _err$response5 !== void 0 && (_err$response5$data = _err$response5.data) !== null && _err$response5$data !== void 0 && _err$response5$data.detail) {\n          // Estrai il messaggio di errore dettagliato dall'API\n          errorMessage = `Errore API: ${err.response.data.detail}`;\n        } else if (err.code === 'ERR_NETWORK') {\n          // Errore di rete\n          errorMessage = 'Network Error. Verifica che il backend sia in esecuzione e accessibile.';\n        } else if (err.message) {\n          errorMessage = err.message;\n        }\n        setError(`Impossibile caricare i cavi: ${errorMessage}. Riprova più tardi.`);\n\n        // Imposta array vuoti per evitare errori di rendering\n        setCaviAttivi([]);\n        setCaviSpare([]);\n      } finally {\n        setLoading(false);\n      }\n    };\n    fetchData();\n  }, [filters]); // Ricarica i dati quando cambiano i filtri\n\n  // Funzione per gestire il cambio dei filtri\n  const handleFilterChange = event => {\n    const {\n      name,\n      value\n    } = event.target;\n    setFilters(prev => ({\n      ...prev,\n      [name]: value\n    }));\n  };\n\n  // Funzione per resettare i filtri\n  const resetFilters = () => {\n    setFilters({\n      stato_installazione: '',\n      tipologia: '',\n      sort_by: '',\n      sort_order: 'asc'\n    });\n  };\n\n  // Funzione per aprire il dialogo dei dettagli del cavo\n  const handleOpenDetails = cavo => {\n    setSelectedCavo(cavo);\n    setDetailsDialogOpen(true);\n  };\n\n  // Funzione per chiudere il dialogo dei dettagli del cavo\n  const handleCloseDetails = () => {\n    setDetailsDialogOpen(false);\n    setSelectedCavo(null);\n  };\n\n  // Nessuna funzione di navigazione necessaria, tutto è gestito dal menu principale\n\n  // Funzione per visualizzare i cavi in formato tabellare\n  const renderCaviTable = cavi => {\n    if (!cavi || cavi.length === 0) {\n      return /*#__PURE__*/_jsxDEV(Alert, {\n        severity: \"info\",\n        children: [\"Nessun cavo trovato in questa categoria.\", /*#__PURE__*/_jsxDEV(Button, {\n          variant: \"text\",\n          color: \"primary\",\n          onClick: () => window.location.reload(),\n          sx: {\n            ml: 2\n          },\n          children: \"Riprova\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 406,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 404,\n        columnNumber: 9\n      }, this);\n    }\n\n    // Funzione per ottenere il colore in base allo stato di installazione\n    const getStatusColor = stato => {\n      // Normalizza lo stato per gestire le variazioni di maiuscole/minuscole\n      const statoUpper = (stato || '').toUpperCase();\n      switch (statoUpper) {\n        case 'POSATO':\n        case 'INSTALLATO':\n          // Valore dall'enum StatoInstallazione\n          return '#4caf50';\n        // Verde\n        case 'COLLEGATO PARTENZA':\n          return '#ff9800';\n        // Arancione\n        case 'COLLEGATO ARRIVO':\n          return '#ff9800';\n        // Arancione\n        case 'COLLEGATO':\n          return '#2196f3';\n        // Blu\n        case 'DA POSARE':\n        case 'DA INSTALLARE':\n          // Valore dall'enum StatoInstallazione\n          return '#f44336';\n        // Rosso\n        case 'IN POSA':\n        case 'IN CORSO': // Valore dall'enum StatoInstallazione\n        case 'IN INSTALLAZIONE':\n          return '#9c27b0';\n        // Viola\n        default:\n          console.log('Stato non riconosciuto:', stato);\n          return '#757575';\n        // Grigio per stati sconosciuti\n      }\n    };\n\n    // Funzione per ottenere lo stato di collegamento\n    const getConnectionStatus = collegamenti => {\n      if (!collegamenti) return 'NON COLLEGATO';\n      switch (collegamenti) {\n        case 1:\n          return 'COLLEGATO PARTENZA';\n        case 2:\n          return 'COLLEGATO ARRIVO';\n        case 3:\n          return 'COLLEGATO';\n        default:\n          return 'NON COLLEGATO';\n      }\n    };\n\n    // Utilizziamo la funzione normalizeInstallationStatus importata da validationUtils\n\n    if (viewMode === 'table') {\n      return /*#__PURE__*/_jsxDEV(TableContainer, {\n        component: Paper,\n        sx: {\n          mt: 2,\n          overflowX: 'auto'\n        },\n        children: /*#__PURE__*/_jsxDEV(Table, {\n          size: \"small\",\n          children: [/*#__PURE__*/_jsxDEV(TableHead, {\n            children: /*#__PURE__*/_jsxDEV(TableRow, {\n              children: [/*#__PURE__*/_jsxDEV(TableCell, {\n                children: \"ID Cavo\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 466,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n                children: \"Utility\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 467,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n                children: \"Tipologia\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 468,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n                children: \"N.Cond\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 469,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n                children: \"Sezione\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 470,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n                children: \"Ubicaz.Part.\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 471,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n                children: \"Ubicaz.Arr.\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 472,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n                children: \"Metri T.\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 473,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n                children: \"Metri R.\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 474,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n                children: \"Stato\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 475,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n                children: \"Collegamenti\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 476,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n                children: \"Bobina\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 477,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n                children: \"Azioni\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 478,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 465,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 464,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(TableBody, {\n            children: cavi.map(cavo => {\n              // Formatta i valori per la visualizzazione come nella CLI\n              const id_cavo = String(cavo.id_cavo).replace('$', '');\n              const utility = cavo.utility || '-';\n              const tipologia = cavo.tipologia || '-';\n\n              // Gestisci n_conduttori come stringa o numero\n              let n_conduttori = '-';\n              try {\n                const n_cond_val = parseInt(cavo.n_conduttori, 10) || 0;\n                n_conduttori = n_cond_val > 0 ? String(n_cond_val) : '-';\n              } catch (e) {\n                n_conduttori = cavo.n_conduttori || '-';\n              }\n\n              // Gestisci sezione come stringa\n              let sezione = '-';\n              const sezione_val = cavo.sezione;\n              if (typeof sezione_val === 'number' && sezione_val === 0) {\n                sezione = '-';\n              } else {\n                sezione = sezione_val ? String(sezione_val) : '-';\n              }\n              const ubicazione_partenza = cavo.ubicazione_partenza || '-';\n              const ubicazione_arrivo = cavo.ubicazione_arrivo || '-';\n              const metri_teorici = cavo.metri_teorici ? `${parseFloat(cavo.metri_teorici).toFixed(2)}` : '-';\n              const metri_reali = cavo.metratura_reale ? `${parseFloat(cavo.metratura_reale).toFixed(2)}` : '-';\n              const stato = cavo.stato_installazione || 'Da installare';\n              const collegamenti = getConnectionStatus(cavo.collegamenti);\n              const bobina = cavo.id_bobina || '-';\n\n              // Determina il colore in base allo stato\n              const statusColor = getStatusColor(stato);\n              return /*#__PURE__*/_jsxDEV(TableRow, {\n                sx: {\n                  '&:hover': {\n                    backgroundColor: '#f5f5f5'\n                  }\n                },\n                onClick: () => handleOpenDetails(cavo),\n                children: [/*#__PURE__*/_jsxDEV(TableCell, {\n                  children: id_cavo\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 523,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n                  children: utility\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 524,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n                  children: tipologia\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 525,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n                  children: n_conduttori\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 526,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n                  children: sezione\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 527,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n                  children: ubicazione_partenza\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 528,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n                  children: ubicazione_arrivo\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 529,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n                  children: metri_teorici\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 530,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n                  children: metri_reali\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 531,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n                  children: /*#__PURE__*/_jsxDEV(Chip, {\n                    label: normalizeInstallationStatus(stato),\n                    size: \"small\",\n                    sx: {\n                      backgroundColor: statusColor,\n                      color: '#fff',\n                      fontWeight: 'bold'\n                    }\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 533,\n                    columnNumber: 23\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 532,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n                  children: collegamenti\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 543,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n                  children: bobina\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 544,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n                  children: /*#__PURE__*/_jsxDEV(IconButton, {\n                    size: \"small\",\n                    onClick: e => {\n                      e.stopPropagation();\n                      handleOpenDetails(cavo);\n                    },\n                    title: \"Visualizza dettagli\",\n                    children: /*#__PURE__*/_jsxDEV(InfoIcon, {\n                      fontSize: \"small\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 554,\n                      columnNumber: 25\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 546,\n                    columnNumber: 23\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 545,\n                  columnNumber: 21\n                }, this)]\n              }, cavo.id_cavo, true, {\n                fileName: _jsxFileName,\n                lineNumber: 518,\n                columnNumber: 19\n              }, this);\n            })\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 481,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 463,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 462,\n        columnNumber: 9\n      }, this);\n    } else {\n      // Visualizzazione a schede (card)\n      return /*#__PURE__*/_jsxDEV(Grid, {\n        container: true,\n        spacing: 2,\n        children: cavi.map(cavo => {\n          const stato = cavo.stato_installazione || 'Da installare';\n          const statusColor = getStatusColor(stato);\n          const collegamenti = getConnectionStatus(cavo.collegamenti);\n          return /*#__PURE__*/_jsxDEV(Grid, {\n            item: true,\n            xs: 12,\n            sm: 6,\n            md: 4,\n            children: /*#__PURE__*/_jsxDEV(Card, {\n              sx: {\n                cursor: 'pointer',\n                '&:hover': {\n                  boxShadow: 6\n                },\n                borderLeft: `5px solid ${statusColor}`\n              },\n              onClick: () => handleOpenDetails(cavo),\n              children: /*#__PURE__*/_jsxDEV(CardContent, {\n                children: [/*#__PURE__*/_jsxDEV(Typography, {\n                  variant: \"h6\",\n                  component: \"div\",\n                  sx: {\n                    display: 'flex',\n                    justifyContent: 'space-between'\n                  },\n                  children: [cavo.id_cavo, /*#__PURE__*/_jsxDEV(Chip, {\n                    label: normalizeInstallationStatus(stato),\n                    size: \"small\",\n                    sx: {\n                      backgroundColor: statusColor,\n                      color: '#fff',\n                      fontWeight: 'bold'\n                    }\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 586,\n                    columnNumber: 23\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 584,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                  variant: \"body2\",\n                  color: \"text.secondary\",\n                  children: [\"Sistema: \", cavo.sistema || 'N/A']\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 596,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                  variant: \"body2\",\n                  color: \"text.secondary\",\n                  children: [\"Tipologia: \", cavo.tipologia || 'N/A']\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 599,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                  variant: \"body2\",\n                  color: \"text.secondary\",\n                  children: [\"Partenza: \", cavo.ubicazione_partenza || 'N/A', \" - \", cavo.utenza_partenza || 'N/A']\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 602,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                  variant: \"body2\",\n                  color: \"text.secondary\",\n                  children: [\"Arrivo: \", cavo.ubicazione_arrivo || 'N/A', \" - \", cavo.utenza_arrivo || 'N/A']\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 605,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                  variant: \"body2\",\n                  color: \"text.secondary\",\n                  children: [\"Metri teorici: \", cavo.metri_teorici || 'N/A']\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 608,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                  variant: \"body2\",\n                  color: \"text.secondary\",\n                  children: [\"Metratura reale: \", cavo.metratura_reale || '0']\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 611,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                  variant: \"body2\",\n                  color: \"text.secondary\",\n                  children: [\"Collegamenti: \", collegamenti]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 614,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                  variant: \"body2\",\n                  color: \"text.secondary\",\n                  children: [\"Bobina: \", cavo.id_bobina || 'N/A']\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 617,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 583,\n                columnNumber: 19\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 575,\n              columnNumber: 17\n            }, this)\n          }, cavo.id_cavo, false, {\n            fileName: _jsxFileName,\n            lineNumber: 574,\n            columnNumber: 15\n          }, this);\n        })\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 567,\n        columnNumber: 9\n      }, this);\n    }\n  };\n\n  // Gestisce il cambio di modalità di visualizzazione\n  const handleViewModeChange = mode => {\n    setViewMode(mode);\n  };\n\n  // Renderizza il dialogo dei dettagli del cavo\n  const renderDetailsDialog = () => {\n    if (!selectedCavo) return null;\n    return /*#__PURE__*/_jsxDEV(Dialog, {\n      open: detailsDialogOpen,\n      onClose: handleCloseDetails,\n      maxWidth: \"md\",\n      fullWidth: true,\n      children: [/*#__PURE__*/_jsxDEV(DialogTitle, {\n        children: [\"Dettagli Cavo: \", selectedCavo.id_cavo]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 641,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(DialogContent, {\n        dividers: true,\n        children: /*#__PURE__*/_jsxDEV(Grid, {\n          container: true,\n          spacing: 2,\n          children: [/*#__PURE__*/_jsxDEV(Grid, {\n            item: true,\n            xs: 12,\n            md: 6,\n            children: [/*#__PURE__*/_jsxDEV(Typography, {\n              variant: \"subtitle1\",\n              gutterBottom: true,\n              children: \"Informazioni Generali\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 647,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Box, {\n              sx: {\n                mb: 2\n              },\n              children: [/*#__PURE__*/_jsxDEV(Typography, {\n                variant: \"body2\",\n                children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n                  children: \"Sistema:\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 649,\n                  columnNumber: 45\n                }, this), \" \", selectedCavo.sistema || 'N/A']\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 649,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                variant: \"body2\",\n                children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n                  children: \"Utility:\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 650,\n                  columnNumber: 45\n                }, this), \" \", selectedCavo.utility || 'N/A']\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 650,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                variant: \"body2\",\n                children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n                  children: \"Tipologia:\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 651,\n                  columnNumber: 45\n                }, this), \" \", selectedCavo.tipologia || 'N/A']\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 651,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                variant: \"body2\",\n                children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n                  children: \"Colore:\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 652,\n                  columnNumber: 45\n                }, this), \" \", selectedCavo.colore_cavo || 'N/A']\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 652,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                variant: \"body2\",\n                children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n                  children: \"N. Conduttori:\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 653,\n                  columnNumber: 45\n                }, this), \" \", selectedCavo.n_conduttori || 'N/A']\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 653,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                variant: \"body2\",\n                children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n                  children: \"Sezione:\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 654,\n                  columnNumber: 45\n                }, this), \" \", selectedCavo.sezione || 'N/A']\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 654,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                variant: \"body2\",\n                children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n                  children: \"SH:\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 655,\n                  columnNumber: 45\n                }, this), \" \", selectedCavo.sh || 'N/A']\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 655,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 648,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Typography, {\n              variant: \"subtitle1\",\n              gutterBottom: true,\n              children: \"Partenza\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 658,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Box, {\n              sx: {\n                mb: 2\n              },\n              children: [/*#__PURE__*/_jsxDEV(Typography, {\n                variant: \"body2\",\n                children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n                  children: \"Ubicazione:\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 660,\n                  columnNumber: 45\n                }, this), \" \", selectedCavo.ubicazione_partenza || 'N/A']\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 660,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                variant: \"body2\",\n                children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n                  children: \"Utenza:\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 661,\n                  columnNumber: 45\n                }, this), \" \", selectedCavo.utenza_partenza || 'N/A']\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 661,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                variant: \"body2\",\n                children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n                  children: \"Descrizione:\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 662,\n                  columnNumber: 45\n                }, this), \" \", selectedCavo.descrizione_utenza_partenza || 'N/A']\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 662,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                variant: \"body2\",\n                children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n                  children: \"Responsabile:\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 663,\n                  columnNumber: 45\n                }, this), \" \", selectedCavo.responsabile_partenza || 'N/A']\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 663,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                variant: \"body2\",\n                children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n                  children: \"Comanda:\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 664,\n                  columnNumber: 45\n                }, this), \" \", selectedCavo.comanda_partenza || 'N/A']\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 664,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 659,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 646,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Grid, {\n            item: true,\n            xs: 12,\n            md: 6,\n            children: [/*#__PURE__*/_jsxDEV(Typography, {\n              variant: \"subtitle1\",\n              gutterBottom: true,\n              children: \"Arrivo\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 669,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Box, {\n              sx: {\n                mb: 2\n              },\n              children: [/*#__PURE__*/_jsxDEV(Typography, {\n                variant: \"body2\",\n                children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n                  children: \"Ubicazione:\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 671,\n                  columnNumber: 45\n                }, this), \" \", selectedCavo.ubicazione_arrivo || 'N/A']\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 671,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                variant: \"body2\",\n                children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n                  children: \"Utenza:\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 672,\n                  columnNumber: 45\n                }, this), \" \", selectedCavo.utenza_arrivo || 'N/A']\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 672,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                variant: \"body2\",\n                children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n                  children: \"Descrizione:\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 673,\n                  columnNumber: 45\n                }, this), \" \", selectedCavo.descrizione_utenza_arrivo || 'N/A']\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 673,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                variant: \"body2\",\n                children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n                  children: \"Responsabile:\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 674,\n                  columnNumber: 45\n                }, this), \" \", selectedCavo.responsabile_arrivo || 'N/A']\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 674,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                variant: \"body2\",\n                children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n                  children: \"Comanda:\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 675,\n                  columnNumber: 45\n                }, this), \" \", selectedCavo.comanda_arrivo || 'N/A']\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 675,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 670,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Typography, {\n              variant: \"subtitle1\",\n              gutterBottom: true,\n              children: \"Installazione\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 678,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Box, {\n              sx: {\n                mb: 2\n              },\n              children: [/*#__PURE__*/_jsxDEV(Typography, {\n                variant: \"body2\",\n                children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n                  children: \"Metri Teorici:\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 680,\n                  columnNumber: 45\n                }, this), \" \", selectedCavo.metri_teorici || 'N/A']\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 680,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                variant: \"body2\",\n                children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n                  children: \"Metratura Reale:\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 681,\n                  columnNumber: 45\n                }, this), \" \", selectedCavo.metratura_reale || '0']\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 681,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                variant: \"body2\",\n                children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n                  children: \"Stato:\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 682,\n                  columnNumber: 45\n                }, this), \" \", normalizeInstallationStatus(selectedCavo.stato_installazione)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 682,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                variant: \"body2\",\n                children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n                  children: \"Collegamenti:\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 683,\n                  columnNumber: 45\n                }, this), \" \", selectedCavo.collegamenti || '0']\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 683,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                variant: \"body2\",\n                children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n                  children: \"Bobina:\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 684,\n                  columnNumber: 45\n                }, this), \" \", selectedCavo.id_bobina || 'N/A']\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 684,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                variant: \"body2\",\n                children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n                  children: \"Responsabile Posa:\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 685,\n                  columnNumber: 45\n                }, this), \" \", selectedCavo.responsabile_posa || 'N/A']\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 685,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                variant: \"body2\",\n                children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n                  children: \"Comanda Posa:\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 686,\n                  columnNumber: 45\n                }, this), \" \", selectedCavo.comanda_posa || 'N/A']\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 686,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                variant: \"body2\",\n                children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n                  children: \"Ultimo Aggiornamento:\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 687,\n                  columnNumber: 45\n                }, this), \" \", new Date(selectedCavo.timestamp).toLocaleString()]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 687,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 679,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 668,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 645,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 644,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(DialogActions, {\n        children: /*#__PURE__*/_jsxDEV(Button, {\n          onClick: handleCloseDetails,\n          children: \"Chiudi\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 693,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 692,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 640,\n      columnNumber: 7\n    }, this);\n  };\n\n  // Renderizza il pannello dei filtri\n  const renderFilterPanel = () => {\n    return /*#__PURE__*/_jsxDEV(Paper, {\n      sx: {\n        mb: 3,\n        p: 2,\n        display: filtersOpen ? 'block' : 'none'\n      },\n      children: [/*#__PURE__*/_jsxDEV(Typography, {\n        variant: \"h6\",\n        gutterBottom: true,\n        children: \"Filtri e Ordinamento\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 703,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Grid, {\n        container: true,\n        spacing: 2,\n        children: [/*#__PURE__*/_jsxDEV(Grid, {\n          item: true,\n          xs: 12,\n          sm: 6,\n          md: 3,\n          children: /*#__PURE__*/_jsxDEV(FormControl, {\n            fullWidth: true,\n            size: \"small\",\n            children: [/*#__PURE__*/_jsxDEV(InputLabel, {\n              id: \"stato-installazione-label\",\n              children: \"Stato Installazione\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 707,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Select, {\n              labelId: \"stato-installazione-label\",\n              id: \"stato-installazione\",\n              name: \"stato_installazione\",\n              value: filters.stato_installazione,\n              label: \"Stato Installazione\",\n              onChange: handleFilterChange,\n              children: [/*#__PURE__*/_jsxDEV(MenuItem, {\n                value: \"\",\n                children: \"Tutti\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 716,\n                columnNumber: 17\n              }, this), statiInstallazione.map(stato => /*#__PURE__*/_jsxDEV(MenuItem, {\n                value: stato,\n                children: stato\n              }, stato, false, {\n                fileName: _jsxFileName,\n                lineNumber: 718,\n                columnNumber: 19\n              }, this))]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 708,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 706,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 705,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Grid, {\n          item: true,\n          xs: 12,\n          sm: 6,\n          md: 3,\n          children: /*#__PURE__*/_jsxDEV(FormControl, {\n            fullWidth: true,\n            size: \"small\",\n            children: [/*#__PURE__*/_jsxDEV(InputLabel, {\n              id: \"tipologia-label\",\n              children: \"Tipologia\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 726,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Select, {\n              labelId: \"tipologia-label\",\n              id: \"tipologia\",\n              name: \"tipologia\",\n              value: filters.tipologia,\n              label: \"Tipologia\",\n              onChange: handleFilterChange,\n              children: [/*#__PURE__*/_jsxDEV(MenuItem, {\n                value: \"\",\n                children: \"Tutte\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 735,\n                columnNumber: 17\n              }, this), tipologieCavi.map(tipo => /*#__PURE__*/_jsxDEV(MenuItem, {\n                value: tipo,\n                children: tipo\n              }, tipo, false, {\n                fileName: _jsxFileName,\n                lineNumber: 737,\n                columnNumber: 19\n              }, this))]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 727,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 725,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 724,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Grid, {\n          item: true,\n          xs: 12,\n          sm: 6,\n          md: 3,\n          children: /*#__PURE__*/_jsxDEV(FormControl, {\n            fullWidth: true,\n            size: \"small\",\n            children: [/*#__PURE__*/_jsxDEV(InputLabel, {\n              id: \"sort-by-label\",\n              children: \"Ordina per\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 745,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Select, {\n              labelId: \"sort-by-label\",\n              id: \"sort-by\",\n              name: \"sort_by\",\n              value: filters.sort_by,\n              label: \"Ordina per\",\n              onChange: handleFilterChange,\n              children: [/*#__PURE__*/_jsxDEV(MenuItem, {\n                value: \"\",\n                children: \"Predefinito\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 754,\n                columnNumber: 17\n              }, this), sortOptions.map(option => /*#__PURE__*/_jsxDEV(MenuItem, {\n                value: option.value,\n                children: option.label\n              }, option.value, false, {\n                fileName: _jsxFileName,\n                lineNumber: 756,\n                columnNumber: 19\n              }, this))]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 746,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 744,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 743,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Grid, {\n          item: true,\n          xs: 12,\n          sm: 6,\n          md: 3,\n          children: /*#__PURE__*/_jsxDEV(FormControl, {\n            fullWidth: true,\n            size: \"small\",\n            children: [/*#__PURE__*/_jsxDEV(InputLabel, {\n              id: \"sort-order-label\",\n              children: \"Ordine\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 764,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Select, {\n              labelId: \"sort-order-label\",\n              id: \"sort-order\",\n              name: \"sort_order\",\n              value: filters.sort_order,\n              label: \"Ordine\",\n              onChange: handleFilterChange,\n              disabled: !filters.sort_by,\n              children: [/*#__PURE__*/_jsxDEV(MenuItem, {\n                value: \"asc\",\n                children: \"Crescente\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 774,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(MenuItem, {\n                value: \"desc\",\n                children: \"Decrescente\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 775,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 765,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 763,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 762,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 704,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Box, {\n        sx: {\n          mt: 2,\n          display: 'flex',\n          justifyContent: 'flex-end'\n        },\n        children: /*#__PURE__*/_jsxDEV(Button, {\n          variant: \"outlined\",\n          startIcon: /*#__PURE__*/_jsxDEV(ClearIcon, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 784,\n            columnNumber: 24\n          }, this),\n          onClick: resetFilters,\n          sx: {\n            mr: 1\n          },\n          children: \"Resetta Filtri\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 782,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 781,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 702,\n      columnNumber: 7\n    }, this);\n  };\n\n  // Renderizza il pannello delle statistiche\n  const renderStatsPanel = () => {\n    if (!stats) return null;\n    return /*#__PURE__*/_jsxDEV(Paper, {\n      sx: {\n        mb: 3,\n        p: 2\n      },\n      children: [/*#__PURE__*/_jsxDEV(Typography, {\n        variant: \"h6\",\n        gutterBottom: true,\n        children: \"Statistiche\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 801,\n        columnNumber: 9\n      }, this), loadingStats ? /*#__PURE__*/_jsxDEV(LinearProgress, {}, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 803,\n        columnNumber: 11\n      }, this) : /*#__PURE__*/_jsxDEV(Grid, {\n        container: true,\n        spacing: 2,\n        children: [/*#__PURE__*/_jsxDEV(Grid, {\n          item: true,\n          xs: 12,\n          md: 4,\n          children: [/*#__PURE__*/_jsxDEV(Typography, {\n            variant: \"subtitle1\",\n            gutterBottom: true,\n            children: \"Totali\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 807,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(Typography, {\n            variant: \"body2\",\n            children: [\"Cavi Attivi: \", stats.totali.cavi_attivi]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 808,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(Typography, {\n            variant: \"body2\",\n            children: [\"Cavi Spare: \", stats.totali.cavi_spare]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 809,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(Typography, {\n            variant: \"body2\",\n            children: [\"Totale Cavi: \", stats.totali.cavi_totali]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 810,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 806,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(Grid, {\n          item: true,\n          xs: 12,\n          md: 4,\n          children: [/*#__PURE__*/_jsxDEV(Typography, {\n            variant: \"subtitle1\",\n            gutterBottom: true,\n            children: \"Metrature\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 814,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(Typography, {\n            variant: \"body2\",\n            children: [\"Metri Teorici: \", stats.metrature.metri_teorici_totali.toFixed(2)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 815,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(Typography, {\n            variant: \"body2\",\n            children: [\"Metri Posati: \", stats.metrature.metri_reali_totali.toFixed(2)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 816,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(Box, {\n            sx: {\n              display: 'flex',\n              alignItems: 'center',\n              mt: 1\n            },\n            children: [/*#__PURE__*/_jsxDEV(Box, {\n              sx: {\n                width: '100%',\n                mr: 1\n              },\n              children: /*#__PURE__*/_jsxDEV(LinearProgress, {\n                variant: \"determinate\",\n                value: stats.metrature.percentuale_completamento,\n                sx: {\n                  height: 10,\n                  borderRadius: 5\n                }\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 819,\n                columnNumber: 19\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 818,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(Box, {\n              sx: {\n                minWidth: 35\n              },\n              children: /*#__PURE__*/_jsxDEV(Typography, {\n                variant: \"body2\",\n                color: \"text.secondary\",\n                children: `${stats.metrature.percentuale_completamento.toFixed(1)}%`\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 826,\n                columnNumber: 19\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 825,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 817,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 813,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(Grid, {\n          item: true,\n          xs: 12,\n          md: 4,\n          children: [/*#__PURE__*/_jsxDEV(Typography, {\n            variant: \"subtitle1\",\n            gutterBottom: true,\n            children: \"Stati\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 832,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(Box, {\n            sx: {\n              display: 'flex',\n              flexWrap: 'wrap',\n              gap: 1\n            },\n            children: stats.stati.map((stato, index) => /*#__PURE__*/_jsxDEV(Chip, {\n              label: `${stato.stato}: ${stato.count}`,\n              size: \"small\",\n              onClick: () => {\n                setFilters(prev => ({\n                  ...prev,\n                  stato_installazione: stato.stato === 'Non specificato' ? '' : stato.stato\n                }));\n              }\n            }, index, false, {\n              fileName: _jsxFileName,\n              lineNumber: 835,\n              columnNumber: 19\n            }, this))\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 833,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 831,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 805,\n        columnNumber: 11\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 800,\n      columnNumber: 7\n    }, this);\n  };\n  return /*#__PURE__*/_jsxDEV(Box, {\n    className: \"cavi-page\",\n    children: [/*#__PURE__*/_jsxDEV(Paper, {\n      sx: {\n        mb: 3,\n        p: 2\n      },\n      children: /*#__PURE__*/_jsxDEV(Box, {\n        sx: {\n          display: 'flex',\n          justifyContent: 'space-between',\n          alignItems: 'center'\n        },\n        children: [/*#__PURE__*/_jsxDEV(Typography, {\n          variant: \"h5\",\n          children: [\"Visualizzazione Cavi - \", cantiereName]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 859,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Box, {\n          sx: {\n            display: 'flex',\n            alignItems: 'center',\n            gap: 1\n          },\n          children: [/*#__PURE__*/_jsxDEV(IconButton, {\n            onClick: () => setFiltersOpen(!filtersOpen),\n            title: filtersOpen ? \"Nascondi filtri\" : \"Mostra filtri\",\n            color: filtersOpen ? \"primary\" : \"default\",\n            children: /*#__PURE__*/_jsxDEV(FilterListIcon, {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 869,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 864,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(IconButton, {\n            onClick: () => window.location.reload(),\n            title: \"Ricarica la pagina\",\n            children: /*#__PURE__*/_jsxDEV(RefreshIcon, {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 876,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 872,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Box, {\n            sx: {\n              display: 'flex',\n              border: '1px solid #ddd',\n              borderRadius: 1\n            },\n            children: [/*#__PURE__*/_jsxDEV(IconButton, {\n              sx: {\n                color: viewMode === 'table' ? '#212529' : '#6c757d'\n              },\n              onClick: () => handleViewModeChange('table'),\n              title: \"Vista tabellare\",\n              children: /*#__PURE__*/_jsxDEV(ViewListIcon, {}, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 885,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 880,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(IconButton, {\n              sx: {\n                color: viewMode === 'card' ? '#212529' : '#6c757d'\n              },\n              onClick: () => handleViewModeChange('card'),\n              title: \"Vista a schede\",\n              children: /*#__PURE__*/_jsxDEV(ViewModuleIcon, {}, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 892,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 887,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 879,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 863,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 858,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 857,\n      columnNumber: 7\n    }, this), renderFilterPanel(), renderStatsPanel(), loading ? /*#__PURE__*/_jsxDEV(Box, {\n      sx: {\n        display: 'flex',\n        flexDirection: 'column',\n        alignItems: 'center',\n        mt: 4\n      },\n      children: [/*#__PURE__*/_jsxDEV(CircularProgress, {\n        size: 40\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 907,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(Typography, {\n        sx: {\n          mt: 2\n        },\n        children: \"Caricamento cavi...\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 908,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(Button, {\n        variant: \"outlined\",\n        color: \"primary\",\n        onClick: () => window.location.reload(),\n        sx: {\n          mt: 2\n        },\n        children: \"Ricarica la pagina\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 909,\n        columnNumber: 11\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 906,\n      columnNumber: 9\n    }, this) : error ? /*#__PURE__*/_jsxDEV(Box, {\n      children: [/*#__PURE__*/_jsxDEV(Alert, {\n        severity: \"error\",\n        sx: {\n          mb: 2\n        },\n        children: [error, error.includes('Network Error') && /*#__PURE__*/_jsxDEV(Typography, {\n          variant: \"body2\",\n          sx: {\n            mt: 1\n          },\n          children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n            children: \"Suggerimento:\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 924,\n            columnNumber: 17\n          }, this), \" Verifica che il server backend sia in esecuzione sulla porta 8001.\", /*#__PURE__*/_jsxDEV(\"br\", {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 925,\n            columnNumber: 17\n          }, this), \"Puoi avviare il backend eseguendo il file \", /*#__PURE__*/_jsxDEV(\"code\", {\n            children: \"run_system.py\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 926,\n            columnNumber: 59\n          }, this), \" nella cartella principale del progetto.\"]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 923,\n          columnNumber: 15\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 920,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(Box, {\n        sx: {\n          display: 'flex',\n          gap: 2\n        },\n        children: /*#__PURE__*/_jsxDEV(Button, {\n          variant: \"contained\",\n          className: \"primary-button\",\n          onClick: () => window.location.reload(),\n          children: \"Ricarica la pagina\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 931,\n          columnNumber: 13\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 930,\n        columnNumber: 11\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 919,\n      columnNumber: 9\n    }, this) : /*#__PURE__*/_jsxDEV(Box, {\n      children: [(filters.stato_installazione || filters.tipologia || filters.sort_by) && /*#__PURE__*/_jsxDEV(Box, {\n        sx: {\n          mb: 2,\n          display: 'flex',\n          flexWrap: 'wrap',\n          gap: 1,\n          alignItems: 'center'\n        },\n        children: [/*#__PURE__*/_jsxDEV(Typography, {\n          variant: \"body2\",\n          sx: {\n            mr: 1\n          },\n          children: \"Filtri attivi:\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 945,\n          columnNumber: 15\n        }, this), filters.stato_installazione && /*#__PURE__*/_jsxDEV(Chip, {\n          label: `Stato: ${filters.stato_installazione}`,\n          size: \"small\",\n          onDelete: () => setFilters(prev => ({\n            ...prev,\n            stato_installazione: ''\n          }))\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 947,\n          columnNumber: 17\n        }, this), filters.tipologia && /*#__PURE__*/_jsxDEV(Chip, {\n          label: `Tipologia: ${filters.tipologia}`,\n          size: \"small\",\n          onDelete: () => setFilters(prev => ({\n            ...prev,\n            tipologia: ''\n          }))\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 954,\n          columnNumber: 17\n        }, this), filters.sort_by && /*#__PURE__*/_jsxDEV(Chip, {\n          label: `Ordinamento: ${((_sortOptions$find = sortOptions.find(opt => opt.value === filters.sort_by)) === null || _sortOptions$find === void 0 ? void 0 : _sortOptions$find.label) || filters.sort_by} (${filters.sort_order === 'asc' ? 'Crescente' : 'Decrescente'})`,\n          size: \"small\",\n          onDelete: () => setFilters(prev => ({\n            ...prev,\n            sort_by: ''\n          }))\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 961,\n          columnNumber: 17\n        }, this), (filters.stato_installazione || filters.tipologia || filters.sort_by) && /*#__PURE__*/_jsxDEV(Button, {\n          variant: \"text\",\n          size: \"small\",\n          startIcon: /*#__PURE__*/_jsxDEV(ClearIcon, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 971,\n            columnNumber: 30\n          }, this),\n          onClick: resetFilters,\n          children: \"Resetta tutti\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 968,\n          columnNumber: 17\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 944,\n        columnNumber: 13\n      }, this), /*#__PURE__*/_jsxDEV(Box, {\n        sx: {\n          mb: 3\n        },\n        children: [/*#__PURE__*/_jsxDEV(Typography, {\n          variant: \"h5\",\n          gutterBottom: true,\n          children: \"Cavi Attivi\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 981,\n          columnNumber: 13\n        }, this), renderCaviTable(caviAttivi)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 980,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(Box, {\n        sx: {\n          mt: 4\n        },\n        children: [/*#__PURE__*/_jsxDEV(Typography, {\n          variant: \"h5\",\n          gutterBottom: true,\n          children: \"Cavi Spare\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 988,\n          columnNumber: 13\n        }, this), renderCaviTable(caviSpare)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 987,\n        columnNumber: 11\n      }, this), renderDetailsDialog()]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 941,\n      columnNumber: 9\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 856,\n    columnNumber: 5\n  }, this);\n};\n_s(VisualizzaCaviPage, \"GuNU/VyLp9TQILc8dlZAOiXVcFo=\", false, function () {\n  return [useAuth, useNavigate];\n});\n_c = VisualizzaCaviPage;\nexport default VisualizzaCaviPage;\nvar _c;\n$RefreshReg$(_c, \"VisualizzaCaviPage\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "Box", "Typography", "Paper", "<PERSON><PERSON>", "Grid", "Card", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "IconButton", "Table", "TableBody", "TableCell", "TableContainer", "TableHead", "TableRow", "Tabs", "Tab", "TextField", "MenuItem", "Select", "FormControl", "InputLabel", "Chip", "Divider", "CircularProgress", "LinearProgress", "Dialog", "DialogTitle", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "DialogActions", "ArrowBack", "ArrowBackIcon", "Refresh", "RefreshIcon", "Home", "HomeIcon", "ViewList", "ViewListIcon", "ViewModule", "ViewModuleIcon", "FilterList", "FilterListIcon", "Sort", "SortIcon", "Info", "InfoIcon", "Search", "SearchIcon", "Clear", "ClearIcon", "useNavigate", "useAuth", "caviService", "normalizeInstallationStatus", "jsxDEV", "_jsxDEV", "VisualizzaCaviPage", "_s", "_sortOptions$find", "isImpersonating", "user", "navigate", "cantiereId", "setCantiereId", "cantiereName", "setCantiereName", "caviAttivi", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "caviSpare", "setCaviSpare", "loading", "setLoading", "error", "setError", "viewMode", "setViewMode", "filters", "setFilters", "stato_installazione", "tipologia", "sort_by", "sort_order", "selected<PERSON><PERSON><PERSON>", "setSelectedCavo", "detailsDialogOpen", "setDetailsDialogOpen", "stats", "setStats", "loadingStats", "setLoadingStats", "statiInstallazione", "setStatiInstallazione", "tipologieCavi", "setTipologieCavi", "sortOptions", "value", "label", "filtersOpen", "setFiltersOpen", "fetchData", "console", "log", "token", "localStorage", "getItem", "selectedCantiereId", "selectedCantiereName", "i", "length", "key", "role", "cantiere_id", "toString", "cantiere_name", "setItem", "base64Url", "split", "base64", "replace", "jsonPayload", "decodeURIComponent", "atob", "map", "c", "charCodeAt", "slice", "join", "payload", "JSON", "parse", "e", "warn", "cantiereIdNum", "parseInt", "isNaN", "statsData", "getCaviStats", "stati", "item", "stato", "filter", "tipologie", "tipo", "statsError", "timeoutPromise", "Promise", "_", "reject", "setTimeout", "Error", "caviPromise", "get<PERSON><PERSON>", "attivi", "race", "caviError", "message", "status", "data", "stack", "code", "name", "response", "statusText", "sparePromise", "spare", "spareError", "err", "_err$response", "_err$response2", "_err$response3", "_err$response4", "_err$response5", "_err$response5$data", "errorMessage", "includes", "detail", "handleFilterChange", "event", "target", "prev", "resetFilters", "handleOpenDetails", "cavo", "handleCloseDetails", "renderCaviTable", "cavi", "severity", "children", "variant", "color", "onClick", "window", "location", "reload", "sx", "ml", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "getStatusColor", "statoUpper", "toUpperCase", "getConnectionStatus", "colle<PERSON>nti", "component", "mt", "overflowX", "size", "id_cavo", "String", "utility", "n_conduttori", "n_cond_val", "sezione", "sezione_val", "ubicazione_partenza", "ubicazione_arrivo", "metri_te<PERSON>ci", "parseFloat", "toFixed", "metri_reali", "metratura_reale", "bobina", "id_bobina", "statusColor", "backgroundColor", "fontWeight", "stopPropagation", "title", "fontSize", "container", "spacing", "xs", "sm", "md", "cursor", "boxShadow", "borderLeft", "display", "justifyContent", "sistema", "utenza_partenza", "utenza_arrivo", "handleViewModeChange", "mode", "renderDetailsDialog", "open", "onClose", "max<PERSON><PERSON><PERSON>", "fullWidth", "dividers", "gutterBottom", "mb", "colore_cavo", "sh", "descrizione_utenza_partenza", "responsabile_partenza", "comanda_partenza", "descrizione_utenza_arrivo", "responsabile_arrivo", "comanda_arrivo", "responsabile_posa", "comanda_posa", "Date", "timestamp", "toLocaleString", "renderFilterPanel", "p", "id", "labelId", "onChange", "option", "disabled", "startIcon", "mr", "renderStatsPanel", "totali", "cavi_attivi", "cavi_spare", "cavi_totali", "metrature", "metri_teorici_totali", "metri_reali_totali", "alignItems", "width", "percentuale_completamento", "height", "borderRadius", "min<PERSON><PERSON><PERSON>", "flexWrap", "gap", "index", "count", "className", "border", "flexDirection", "onDelete", "find", "opt", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/PycharmProjects/CMS/webapp/frontend/src/pages/cavi/VisualizzaCaviPage.js"], "sourcesContent": ["import React, { useState, useEffect } from 'react';\nimport {\n  Box,\n  Typography,\n  Paper,\n  Button,\n  Grid,\n  Card,\n  CardContent,\n  Alert,\n  IconButton,\n  Table,\n  TableBody,\n  TableCell,\n  TableContainer,\n  TableHead,\n  TableRow,\n  Tabs,\n  Tab,\n  TextField,\n  MenuItem,\n  Select,\n  FormControl,\n  InputLabel,\n  Chip,\n  Divider,\n  CircularProgress,\n  LinearProgress,\n  Dialog,\n  DialogTitle,\n  DialogContent,\n  DialogActions\n} from '@mui/material';\nimport {\n  ArrowBack as ArrowBackIcon,\n  Refresh as RefreshIcon,\n  Home as HomeIcon,\n  ViewList as ViewListIcon,\n  ViewModule as ViewModuleIcon,\n  FilterList as FilterListIcon,\n  Sort as SortIcon,\n  Info as InfoIcon,\n  Search as SearchIcon,\n  Clear as ClearIcon\n} from '@mui/icons-material';\nimport { useNavigate } from 'react-router-dom';\nimport { useAuth } from '../../context/AuthContext';\nimport caviService from '../../services/caviService';\nimport { normalizeInstallationStatus } from '../../utils/validationUtils';\nimport './CaviPage.css';\n\nconst VisualizzaCaviPage = () => {\n  const { isImpersonating, user } = useAuth();\n  const navigate = useNavigate();\n  const [cantiereId, setCantiereId] = useState(null);\n  const [cantiereName, setCantiereName] = useState('');\n  const [caviAttivi, setCaviAttivi] = useState([]);\n  const [caviSpare, setCaviSpare] = useState([]);\n  const [loading, setLoading] = useState(true);\n  const [error, setError] = useState(null);\n  const [viewMode, setViewMode] = useState('table'); // 'table' o 'card'\n\n  // Stato per filtri e ordinamento\n  const [filters, setFilters] = useState({\n    stato_installazione: '',\n    tipologia: '',\n    sort_by: '',\n    sort_order: 'asc'\n  });\n\n  // Stato per il dialogo dei dettagli del cavo\n  const [selectedCavo, setSelectedCavo] = useState(null);\n  const [detailsDialogOpen, setDetailsDialogOpen] = useState(false);\n\n  // Stato per le statistiche\n  const [stats, setStats] = useState(null);\n  const [loadingStats, setLoadingStats] = useState(false);\n\n  // Opzioni per i filtri\n  const [statiInstallazione, setStatiInstallazione] = useState([]);\n  const [tipologieCavi, setTipologieCavi] = useState([]);\n\n  // Opzioni di ordinamento\n  const sortOptions = [\n    { value: 'id_cavo', label: 'ID Cavo' },\n    { value: 'metratura_reale', label: 'Metratura Reale' },\n    { value: 'metri_teorici', label: 'Metri Teorici' },\n    { value: 'stato_installazione', label: 'Stato Installazione' },\n    { value: 'tipologia', label: 'Tipologia' },\n    { value: 'timestamp', label: 'Data Aggiornamento' }\n  ];\n\n  // Stato per il pannello dei filtri\n  const [filtersOpen, setFiltersOpen] = useState(false);\n\n  // Carica i dati del cantiere e dei cavi\n  useEffect(() => {\n    const fetchData = async () => {\n      try {\n        console.log('Inizializzazione VisualizzaCaviPage...');\n\n        // Verifica che l'utente sia autenticato\n        const token = localStorage.getItem('token');\n        console.log('Token presente:', !!token);\n        if (!token) {\n          setError('Sessione scaduta. Effettua nuovamente il login.');\n          setLoading(false);\n          return;\n        }\n\n        // Recupera l'ID del cantiere selezionato dal localStorage\n        let selectedCantiereId = localStorage.getItem('selectedCantiereId');\n        let selectedCantiereName = localStorage.getItem('selectedCantiereName');\n\n        console.log('Cantiere selezionato dal localStorage:', { selectedCantiereId, selectedCantiereName });\n        console.log('Dati utente:', user);\n\n        // Stampa tutti i dati nel localStorage per debug\n        console.log('DEBUG - Tutti i dati nel localStorage:');\n        for (let i = 0; i < localStorage.length; i++) {\n          const key = localStorage.key(i);\n          console.log(`${key}: ${localStorage.getItem(key)}`);\n        }\n\n        // SOLUZIONE DIRETTA: Ottieni l'ID del cantiere direttamente dal token JWT\n        if (user?.role === 'cantieri_user') {\n          console.log('Utente cantiere rilevato, tentativo di recupero ID cantiere dai dati utente');\n\n          // Verifica se l'utente ha un ID cantiere nei dati utente\n          if (user.cantiere_id) {\n            console.log('Trovato ID cantiere nei dati utente:', user.cantiere_id);\n            selectedCantiereId = user.cantiere_id.toString();\n            selectedCantiereName = user.cantiere_name || `Cantiere ${user.cantiere_id}`;\n\n            // Salva l'ID e il nome del cantiere nel localStorage\n            localStorage.setItem('selectedCantiereId', selectedCantiereId);\n            localStorage.setItem('selectedCantiereName', selectedCantiereName);\n            console.log('Salvato ID cantiere nel localStorage:', selectedCantiereId);\n          } else {\n            // Tentativo di recupero dal token JWT\n            try {\n              console.log('Tentativo di decodifica del token JWT per recuperare l\\'ID cantiere');\n              const token = localStorage.getItem('token');\n              if (token) {\n                // Decodifica il token JWT (senza verifica della firma)\n                const base64Url = token.split('.')[1];\n                const base64 = base64Url.replace(/-/g, '+').replace(/_/g, '/');\n                const jsonPayload = decodeURIComponent(atob(base64).split('').map(c => {\n                  return '%' + ('00' + c.charCodeAt(0).toString(16)).slice(-2);\n                }).join(''));\n\n                const payload = JSON.parse(jsonPayload);\n                console.log('Payload del token JWT:', payload);\n\n                if (payload.cantiere_id) {\n                  console.log('Trovato ID cantiere nel token JWT:', payload.cantiere_id);\n                  selectedCantiereId = payload.cantiere_id.toString();\n                  // Usa un nome generico se non disponibile\n                  selectedCantiereName = `Cantiere ${payload.cantiere_id}`;\n\n                  // Salva l'ID e il nome del cantiere nel localStorage\n                  localStorage.setItem('selectedCantiereId', selectedCantiereId);\n                  localStorage.setItem('selectedCantiereName', selectedCantiereName);\n                  console.log('Salvato ID cantiere nel localStorage:', selectedCantiereId);\n                }\n              }\n            } catch (e) {\n              console.error('Errore durante la decodifica del token JWT:', e);\n            }\n          }\n        }\n\n        // SOLUZIONE TEMPORANEA: Se ancora non abbiamo un ID cantiere, usa un valore hardcoded per debug\n        if (!selectedCantiereId || selectedCantiereId === 'undefined' || selectedCantiereId === 'null') {\n          console.warn('ATTENZIONE: Nessun ID cantiere trovato, utilizzo valore hardcoded per debug');\n          // Usa il primo cantiere disponibile (questo è solo per debug)\n          selectedCantiereId = '1'; // Sostituisci con un ID cantiere valido nel tuo database\n          selectedCantiereName = 'Cantiere Debug';\n\n          // Salva l'ID e il nome del cantiere nel localStorage\n          localStorage.setItem('selectedCantiereId', selectedCantiereId);\n          localStorage.setItem('selectedCantiereName', selectedCantiereName);\n          console.log('Salvato ID cantiere hardcoded nel localStorage:', selectedCantiereId);\n        }\n\n        // Verifica finale\n        if (!selectedCantiereId) {\n          setError('Nessun cantiere selezionato. Torna alla pagina dei cantieri.');\n          setLoading(false);\n          return;\n        }\n\n        // Verifica che l'ID del cantiere sia un numero valido\n        const cantiereIdNum = parseInt(selectedCantiereId, 10);\n        console.log('ID cantiere convertito a numero:', cantiereIdNum);\n        if (isNaN(cantiereIdNum)) {\n          setError(`ID cantiere non valido: ${selectedCantiereId}. Torna alla pagina dei cantieri.`);\n          setLoading(false);\n          return;\n        }\n\n        // Usa il numero convertito, non la stringa\n        setCantiereId(cantiereIdNum);\n        setCantiereName(selectedCantiereName || `Cantiere ${cantiereIdNum}`);\n\n        // Carica le statistiche dei cavi\n        try {\n          setLoadingStats(true);\n          console.log('Caricamento statistiche cavi per cantiere:', cantiereIdNum);\n          const statsData = await caviService.getCaviStats(cantiereIdNum);\n          console.log('Statistiche cavi caricate:', statsData);\n          setStats(statsData);\n\n          // Estrai gli stati di installazione e le tipologie per i filtri\n          if (statsData && statsData.stati) {\n            const stati = statsData.stati.map(item => item.stato).filter(stato => stato !== 'Non specificato');\n            setStatiInstallazione(stati);\n          }\n\n          if (statsData && statsData.tipologie) {\n            const tipologie = statsData.tipologie.map(item => item.tipologia).filter(tipo => tipo !== 'Non specificata');\n            setTipologieCavi(tipologie);\n          }\n\n          setLoadingStats(false);\n        } catch (statsError) {\n          console.error('Errore nel caricamento delle statistiche:', statsError);\n          setLoadingStats(false);\n          // Non interrompere il flusso se le statistiche falliscono\n        }\n\n        // Carica i cavi attivi con gestione degli errori migliorata\n        console.log('Caricamento cavi attivi per cantiere:', cantiereIdNum);\n        try {\n          // Imposta un timeout per evitare che la richiesta rimanga bloccata\n          const timeoutPromise = new Promise((_, reject) => {\n            setTimeout(() => reject(new Error('Timeout durante il caricamento dei cavi attivi')), 30000); // Aumentato a 30 secondi\n          });\n\n          // Esegui la richiesta con un timeout di sicurezza e applica i filtri\n          console.log('Iniziando chiamata API per cavi attivi con filtri:', filters);\n          const caviPromise = caviService.getCavi(cantiereIdNum, 0, filters);\n          const attivi = await Promise.race([caviPromise, timeoutPromise]);\n\n          console.log('Cavi attivi caricati:', attivi);\n          console.log('Numero di cavi attivi trovati:', attivi ? attivi.length : 0);\n          if (attivi && attivi.length > 0) {\n            console.log('Primo cavo attivo:', attivi[0]);\n          } else {\n            console.warn('Nessun cavo attivo trovato per il cantiere', cantiereIdNum);\n          }\n          setCaviAttivi(attivi || []);\n        } catch (caviError) {\n          console.error('Errore nel caricamento dei cavi attivi:', caviError);\n          console.error('Dettagli errore cavi attivi:', {\n            message: caviError.message,\n            status: caviError.status,\n            data: caviError.data,\n            stack: caviError.stack,\n            code: caviError.code,\n            name: caviError.name,\n            response: caviError.response ? {\n              status: caviError.response.status,\n              statusText: caviError.response.statusText,\n              data: caviError.response.data\n            } : 'No response'\n          });\n\n          // Non interrompere il flusso, continua con i cavi spare\n          setCaviAttivi([]);\n          console.warn('Continuazione del flusso dopo errore nei cavi attivi');\n\n          // Aggiungi un messaggio di errore visibile all'utente\n          setError(`Errore nel caricamento dei cavi attivi: ${caviError.message}. Controlla la console per maggiori dettagli.`);\n        }\n\n        // Carica i cavi spare con gestione degli errori migliorata\n        console.log('Caricamento cavi spare per cantiere:', cantiereIdNum);\n        try {\n          // Imposta un timeout per evitare che la richiesta rimanga bloccata\n          const timeoutPromise = new Promise((_, reject) => {\n            setTimeout(() => reject(new Error('Timeout durante il caricamento dei cavi spare')), 30000); // Aumentato a 30 secondi\n          });\n\n          // Esegui la richiesta con un timeout di sicurezza\n          console.log('Iniziando chiamata API per cavi spare...');\n          // Non applichiamo i filtri ai cavi spare, solo agli attivi\n          const sparePromise = caviService.getCavi(cantiereIdNum, 3);\n          const spare = await Promise.race([sparePromise, timeoutPromise]);\n\n          console.log('Cavi spare caricati:', spare);\n          console.log('Numero di cavi spare trovati:', spare ? spare.length : 0);\n          if (spare && spare.length > 0) {\n            console.log('Primo cavo spare:', spare[0]);\n          } else {\n            console.warn('Nessun cavo spare trovato per il cantiere', cantiereIdNum);\n          }\n          setCaviSpare(spare || []);\n        } catch (spareError) {\n          console.error('Errore nel caricamento dei cavi spare:', spareError);\n          console.error('Dettagli errore cavi spare:', {\n            message: spareError.message,\n            status: spareError.status,\n            data: spareError.data,\n            stack: spareError.stack,\n            code: spareError.code,\n            name: spareError.name,\n            response: spareError.response ? {\n              status: spareError.response.status,\n              statusText: spareError.response.statusText,\n              data: spareError.response.data\n            } : 'No response'\n          });\n\n          // Non interrompere il flusso, imposta un array vuoto\n          setCaviSpare([]);\n\n          // Aggiungi un messaggio di errore visibile all'utente se non c'è già un errore per i cavi attivi\n          if (!error) {\n            setError(`Errore nel caricamento dei cavi spare: ${spareError.message}. Controlla la console per maggiori dettagli.`);\n          }\n        }\n\n        // Se siamo arrivati qui, almeno abbiamo caricato l'interfaccia di base\n        setLoading(false);\n\n      } catch (err) {\n        console.error('Errore nel caricamento dei cavi:', err);\n        console.error('Dettagli errore generale:', {\n          message: err.message,\n          status: err.status || err.response?.status,\n          data: err.data || err.response?.data,\n          stack: err.stack\n        });\n\n        // Estrai il messaggio di errore dettagliato\n        let errorMessage = 'Errore sconosciuto';\n\n        if (err.message && err.message.includes('ID cantiere non valido')) {\n          errorMessage = err.message;\n        } else if (err.status === 401 || err.status === 403 ||\n                  err.response?.status === 401 || err.response?.status === 403) {\n          errorMessage = 'Sessione scaduta o non autorizzata. Effettua nuovamente il login.';\n        } else if (err.response?.data?.detail) {\n          // Estrai il messaggio di errore dettagliato dall'API\n          errorMessage = `Errore API: ${err.response.data.detail}`;\n        } else if (err.code === 'ERR_NETWORK') {\n          // Errore di rete\n          errorMessage = 'Network Error. Verifica che il backend sia in esecuzione e accessibile.';\n        } else if (err.message) {\n          errorMessage = err.message;\n        }\n\n        setError(`Impossibile caricare i cavi: ${errorMessage}. Riprova più tardi.`);\n\n        // Imposta array vuoti per evitare errori di rendering\n        setCaviAttivi([]);\n        setCaviSpare([]);\n      } finally {\n        setLoading(false);\n      }\n    };\n\n    fetchData();\n  }, [filters]); // Ricarica i dati quando cambiano i filtri\n\n  // Funzione per gestire il cambio dei filtri\n  const handleFilterChange = (event) => {\n    const { name, value } = event.target;\n    setFilters(prev => ({\n      ...prev,\n      [name]: value\n    }));\n  };\n\n  // Funzione per resettare i filtri\n  const resetFilters = () => {\n    setFilters({\n      stato_installazione: '',\n      tipologia: '',\n      sort_by: '',\n      sort_order: 'asc'\n    });\n  };\n\n  // Funzione per aprire il dialogo dei dettagli del cavo\n  const handleOpenDetails = (cavo) => {\n    setSelectedCavo(cavo);\n    setDetailsDialogOpen(true);\n  };\n\n  // Funzione per chiudere il dialogo dei dettagli del cavo\n  const handleCloseDetails = () => {\n    setDetailsDialogOpen(false);\n    setSelectedCavo(null);\n  };\n\n  // Nessuna funzione di navigazione necessaria, tutto è gestito dal menu principale\n\n  // Funzione per visualizzare i cavi in formato tabellare\n  const renderCaviTable = (cavi) => {\n    if (!cavi || cavi.length === 0) {\n      return (\n        <Alert severity=\"info\">\n          Nessun cavo trovato in questa categoria.\n          <Button\n            variant=\"text\"\n            color=\"primary\"\n            onClick={() => window.location.reload()}\n            sx={{ ml: 2 }}\n          >\n            Riprova\n          </Button>\n        </Alert>\n      );\n    }\n\n    // Funzione per ottenere il colore in base allo stato di installazione\n    const getStatusColor = (stato) => {\n      // Normalizza lo stato per gestire le variazioni di maiuscole/minuscole\n      const statoUpper = (stato || '').toUpperCase();\n\n      switch (statoUpper) {\n        case 'POSATO':\n        case 'INSTALLATO': // Valore dall'enum StatoInstallazione\n          return '#4caf50'; // Verde\n        case 'COLLEGATO PARTENZA':\n          return '#ff9800'; // Arancione\n        case 'COLLEGATO ARRIVO':\n          return '#ff9800'; // Arancione\n        case 'COLLEGATO':\n          return '#2196f3'; // Blu\n        case 'DA POSARE':\n        case 'DA INSTALLARE': // Valore dall'enum StatoInstallazione\n          return '#f44336'; // Rosso\n        case 'IN POSA':\n        case 'IN CORSO': // Valore dall'enum StatoInstallazione\n        case 'IN INSTALLAZIONE':\n          return '#9c27b0'; // Viola\n        default:\n          console.log('Stato non riconosciuto:', stato);\n          return '#757575'; // Grigio per stati sconosciuti\n      }\n    };\n\n    // Funzione per ottenere lo stato di collegamento\n    const getConnectionStatus = (collegamenti) => {\n      if (!collegamenti) return 'NON COLLEGATO';\n\n      switch (collegamenti) {\n        case 1: return 'COLLEGATO PARTENZA';\n        case 2: return 'COLLEGATO ARRIVO';\n        case 3: return 'COLLEGATO';\n        default: return 'NON COLLEGATO';\n      }\n    };\n\n    // Utilizziamo la funzione normalizeInstallationStatus importata da validationUtils\n\n    if (viewMode === 'table') {\n      return (\n        <TableContainer component={Paper} sx={{ mt: 2, overflowX: 'auto' }}>\n          <Table size=\"small\">\n            <TableHead>\n              <TableRow>\n                <TableCell>ID Cavo</TableCell>\n                <TableCell>Utility</TableCell>\n                <TableCell>Tipologia</TableCell>\n                <TableCell>N.Cond</TableCell>\n                <TableCell>Sezione</TableCell>\n                <TableCell>Ubicaz.Part.</TableCell>\n                <TableCell>Ubicaz.Arr.</TableCell>\n                <TableCell>Metri T.</TableCell>\n                <TableCell>Metri R.</TableCell>\n                <TableCell>Stato</TableCell>\n                <TableCell>Collegamenti</TableCell>\n                <TableCell>Bobina</TableCell>\n                <TableCell>Azioni</TableCell>\n              </TableRow>\n            </TableHead>\n            <TableBody>\n              {cavi.map((cavo) => {\n                // Formatta i valori per la visualizzazione come nella CLI\n                const id_cavo = String(cavo.id_cavo).replace('$', '');\n                const utility = cavo.utility || '-';\n                const tipologia = cavo.tipologia || '-';\n\n                // Gestisci n_conduttori come stringa o numero\n                let n_conduttori = '-';\n                try {\n                  const n_cond_val = parseInt(cavo.n_conduttori, 10) || 0;\n                  n_conduttori = n_cond_val > 0 ? String(n_cond_val) : '-';\n                } catch (e) {\n                  n_conduttori = cavo.n_conduttori || '-';\n                }\n\n                // Gestisci sezione come stringa\n                let sezione = '-';\n                const sezione_val = cavo.sezione;\n                if (typeof sezione_val === 'number' && sezione_val === 0) {\n                  sezione = '-';\n                } else {\n                  sezione = sezione_val ? String(sezione_val) : '-';\n                }\n\n                const ubicazione_partenza = cavo.ubicazione_partenza || '-';\n                const ubicazione_arrivo = cavo.ubicazione_arrivo || '-';\n                const metri_teorici = cavo.metri_teorici ? `${parseFloat(cavo.metri_teorici).toFixed(2)}` : '-';\n                const metri_reali = cavo.metratura_reale ? `${parseFloat(cavo.metratura_reale).toFixed(2)}` : '-';\n                const stato = cavo.stato_installazione || 'Da installare';\n                const collegamenti = getConnectionStatus(cavo.collegamenti);\n                const bobina = cavo.id_bobina || '-';\n\n                // Determina il colore in base allo stato\n                const statusColor = getStatusColor(stato);\n\n                return (\n                  <TableRow\n                    key={cavo.id_cavo}\n                    sx={{ '&:hover': { backgroundColor: '#f5f5f5' } }}\n                    onClick={() => handleOpenDetails(cavo)}\n                  >\n                    <TableCell>{id_cavo}</TableCell>\n                    <TableCell>{utility}</TableCell>\n                    <TableCell>{tipologia}</TableCell>\n                    <TableCell>{n_conduttori}</TableCell>\n                    <TableCell>{sezione}</TableCell>\n                    <TableCell>{ubicazione_partenza}</TableCell>\n                    <TableCell>{ubicazione_arrivo}</TableCell>\n                    <TableCell>{metri_teorici}</TableCell>\n                    <TableCell>{metri_reali}</TableCell>\n                    <TableCell>\n                      <Chip\n                        label={normalizeInstallationStatus(stato)}\n                        size=\"small\"\n                        sx={{\n                          backgroundColor: statusColor,\n                          color: '#fff',\n                          fontWeight: 'bold'\n                        }}\n                      />\n                    </TableCell>\n                    <TableCell>{collegamenti}</TableCell>\n                    <TableCell>{bobina}</TableCell>\n                    <TableCell>\n                      <IconButton\n                        size=\"small\"\n                        onClick={(e) => {\n                          e.stopPropagation();\n                          handleOpenDetails(cavo);\n                        }}\n                        title=\"Visualizza dettagli\"\n                      >\n                        <InfoIcon fontSize=\"small\" />\n                      </IconButton>\n                    </TableCell>\n                  </TableRow>\n                );\n              })}\n            </TableBody>\n          </Table>\n        </TableContainer>\n      );\n    } else {\n      // Visualizzazione a schede (card)\n      return (\n        <Grid container spacing={2}>\n          {cavi.map((cavo) => {\n            const stato = cavo.stato_installazione || 'Da installare';\n            const statusColor = getStatusColor(stato);\n            const collegamenti = getConnectionStatus(cavo.collegamenti);\n\n            return (\n              <Grid item xs={12} sm={6} md={4} key={cavo.id_cavo}>\n                <Card\n                  sx={{\n                    cursor: 'pointer',\n                    '&:hover': { boxShadow: 6 },\n                    borderLeft: `5px solid ${statusColor}`\n                  }}\n                  onClick={() => handleOpenDetails(cavo)}\n                >\n                  <CardContent>\n                    <Typography variant=\"h6\" component=\"div\" sx={{ display: 'flex', justifyContent: 'space-between' }}>\n                      {cavo.id_cavo}\n                      <Chip\n                        label={normalizeInstallationStatus(stato)}\n                        size=\"small\"\n                        sx={{\n                          backgroundColor: statusColor,\n                          color: '#fff',\n                          fontWeight: 'bold'\n                        }}\n                      />\n                    </Typography>\n                    <Typography variant=\"body2\" color=\"text.secondary\">\n                      Sistema: {cavo.sistema || 'N/A'}\n                    </Typography>\n                    <Typography variant=\"body2\" color=\"text.secondary\">\n                      Tipologia: {cavo.tipologia || 'N/A'}\n                    </Typography>\n                    <Typography variant=\"body2\" color=\"text.secondary\">\n                      Partenza: {cavo.ubicazione_partenza || 'N/A'} - {cavo.utenza_partenza || 'N/A'}\n                    </Typography>\n                    <Typography variant=\"body2\" color=\"text.secondary\">\n                      Arrivo: {cavo.ubicazione_arrivo || 'N/A'} - {cavo.utenza_arrivo || 'N/A'}\n                    </Typography>\n                    <Typography variant=\"body2\" color=\"text.secondary\">\n                      Metri teorici: {cavo.metri_teorici || 'N/A'}\n                    </Typography>\n                    <Typography variant=\"body2\" color=\"text.secondary\">\n                      Metratura reale: {cavo.metratura_reale || '0'}\n                    </Typography>\n                    <Typography variant=\"body2\" color=\"text.secondary\">\n                      Collegamenti: {collegamenti}\n                    </Typography>\n                    <Typography variant=\"body2\" color=\"text.secondary\">\n                      Bobina: {cavo.id_bobina || 'N/A'}\n                    </Typography>\n                  </CardContent>\n                </Card>\n              </Grid>\n            );\n          })}\n        </Grid>\n      );\n    }\n  };\n\n  // Gestisce il cambio di modalità di visualizzazione\n  const handleViewModeChange = (mode) => {\n    setViewMode(mode);\n  };\n\n  // Renderizza il dialogo dei dettagli del cavo\n  const renderDetailsDialog = () => {\n    if (!selectedCavo) return null;\n\n    return (\n      <Dialog open={detailsDialogOpen} onClose={handleCloseDetails} maxWidth=\"md\" fullWidth>\n        <DialogTitle>\n          Dettagli Cavo: {selectedCavo.id_cavo}\n        </DialogTitle>\n        <DialogContent dividers>\n          <Grid container spacing={2}>\n            <Grid item xs={12} md={6}>\n              <Typography variant=\"subtitle1\" gutterBottom>Informazioni Generali</Typography>\n              <Box sx={{ mb: 2 }}>\n                <Typography variant=\"body2\"><strong>Sistema:</strong> {selectedCavo.sistema || 'N/A'}</Typography>\n                <Typography variant=\"body2\"><strong>Utility:</strong> {selectedCavo.utility || 'N/A'}</Typography>\n                <Typography variant=\"body2\"><strong>Tipologia:</strong> {selectedCavo.tipologia || 'N/A'}</Typography>\n                <Typography variant=\"body2\"><strong>Colore:</strong> {selectedCavo.colore_cavo || 'N/A'}</Typography>\n                <Typography variant=\"body2\"><strong>N. Conduttori:</strong> {selectedCavo.n_conduttori || 'N/A'}</Typography>\n                <Typography variant=\"body2\"><strong>Sezione:</strong> {selectedCavo.sezione || 'N/A'}</Typography>\n                <Typography variant=\"body2\"><strong>SH:</strong> {selectedCavo.sh || 'N/A'}</Typography>\n              </Box>\n\n              <Typography variant=\"subtitle1\" gutterBottom>Partenza</Typography>\n              <Box sx={{ mb: 2 }}>\n                <Typography variant=\"body2\"><strong>Ubicazione:</strong> {selectedCavo.ubicazione_partenza || 'N/A'}</Typography>\n                <Typography variant=\"body2\"><strong>Utenza:</strong> {selectedCavo.utenza_partenza || 'N/A'}</Typography>\n                <Typography variant=\"body2\"><strong>Descrizione:</strong> {selectedCavo.descrizione_utenza_partenza || 'N/A'}</Typography>\n                <Typography variant=\"body2\"><strong>Responsabile:</strong> {selectedCavo.responsabile_partenza || 'N/A'}</Typography>\n                <Typography variant=\"body2\"><strong>Comanda:</strong> {selectedCavo.comanda_partenza || 'N/A'}</Typography>\n              </Box>\n            </Grid>\n\n            <Grid item xs={12} md={6}>\n              <Typography variant=\"subtitle1\" gutterBottom>Arrivo</Typography>\n              <Box sx={{ mb: 2 }}>\n                <Typography variant=\"body2\"><strong>Ubicazione:</strong> {selectedCavo.ubicazione_arrivo || 'N/A'}</Typography>\n                <Typography variant=\"body2\"><strong>Utenza:</strong> {selectedCavo.utenza_arrivo || 'N/A'}</Typography>\n                <Typography variant=\"body2\"><strong>Descrizione:</strong> {selectedCavo.descrizione_utenza_arrivo || 'N/A'}</Typography>\n                <Typography variant=\"body2\"><strong>Responsabile:</strong> {selectedCavo.responsabile_arrivo || 'N/A'}</Typography>\n                <Typography variant=\"body2\"><strong>Comanda:</strong> {selectedCavo.comanda_arrivo || 'N/A'}</Typography>\n              </Box>\n\n              <Typography variant=\"subtitle1\" gutterBottom>Installazione</Typography>\n              <Box sx={{ mb: 2 }}>\n                <Typography variant=\"body2\"><strong>Metri Teorici:</strong> {selectedCavo.metri_teorici || 'N/A'}</Typography>\n                <Typography variant=\"body2\"><strong>Metratura Reale:</strong> {selectedCavo.metratura_reale || '0'}</Typography>\n                <Typography variant=\"body2\"><strong>Stato:</strong> {normalizeInstallationStatus(selectedCavo.stato_installazione)}</Typography>\n                <Typography variant=\"body2\"><strong>Collegamenti:</strong> {selectedCavo.collegamenti || '0'}</Typography>\n                <Typography variant=\"body2\"><strong>Bobina:</strong> {selectedCavo.id_bobina || 'N/A'}</Typography>\n                <Typography variant=\"body2\"><strong>Responsabile Posa:</strong> {selectedCavo.responsabile_posa || 'N/A'}</Typography>\n                <Typography variant=\"body2\"><strong>Comanda Posa:</strong> {selectedCavo.comanda_posa || 'N/A'}</Typography>\n                <Typography variant=\"body2\"><strong>Ultimo Aggiornamento:</strong> {new Date(selectedCavo.timestamp).toLocaleString()}</Typography>\n              </Box>\n            </Grid>\n          </Grid>\n        </DialogContent>\n        <DialogActions>\n          <Button onClick={handleCloseDetails}>Chiudi</Button>\n        </DialogActions>\n      </Dialog>\n    );\n  };\n\n  // Renderizza il pannello dei filtri\n  const renderFilterPanel = () => {\n    return (\n      <Paper sx={{ mb: 3, p: 2, display: filtersOpen ? 'block' : 'none' }}>\n        <Typography variant=\"h6\" gutterBottom>Filtri e Ordinamento</Typography>\n        <Grid container spacing={2}>\n          <Grid item xs={12} sm={6} md={3}>\n            <FormControl fullWidth size=\"small\">\n              <InputLabel id=\"stato-installazione-label\">Stato Installazione</InputLabel>\n              <Select\n                labelId=\"stato-installazione-label\"\n                id=\"stato-installazione\"\n                name=\"stato_installazione\"\n                value={filters.stato_installazione}\n                label=\"Stato Installazione\"\n                onChange={handleFilterChange}\n              >\n                <MenuItem value=\"\">Tutti</MenuItem>\n                {statiInstallazione.map(stato => (\n                  <MenuItem key={stato} value={stato}>{stato}</MenuItem>\n                ))}\n              </Select>\n            </FormControl>\n          </Grid>\n\n          <Grid item xs={12} sm={6} md={3}>\n            <FormControl fullWidth size=\"small\">\n              <InputLabel id=\"tipologia-label\">Tipologia</InputLabel>\n              <Select\n                labelId=\"tipologia-label\"\n                id=\"tipologia\"\n                name=\"tipologia\"\n                value={filters.tipologia}\n                label=\"Tipologia\"\n                onChange={handleFilterChange}\n              >\n                <MenuItem value=\"\">Tutte</MenuItem>\n                {tipologieCavi.map(tipo => (\n                  <MenuItem key={tipo} value={tipo}>{tipo}</MenuItem>\n                ))}\n              </Select>\n            </FormControl>\n          </Grid>\n\n          <Grid item xs={12} sm={6} md={3}>\n            <FormControl fullWidth size=\"small\">\n              <InputLabel id=\"sort-by-label\">Ordina per</InputLabel>\n              <Select\n                labelId=\"sort-by-label\"\n                id=\"sort-by\"\n                name=\"sort_by\"\n                value={filters.sort_by}\n                label=\"Ordina per\"\n                onChange={handleFilterChange}\n              >\n                <MenuItem value=\"\">Predefinito</MenuItem>\n                {sortOptions.map(option => (\n                  <MenuItem key={option.value} value={option.value}>{option.label}</MenuItem>\n                ))}\n              </Select>\n            </FormControl>\n          </Grid>\n\n          <Grid item xs={12} sm={6} md={3}>\n            <FormControl fullWidth size=\"small\">\n              <InputLabel id=\"sort-order-label\">Ordine</InputLabel>\n              <Select\n                labelId=\"sort-order-label\"\n                id=\"sort-order\"\n                name=\"sort_order\"\n                value={filters.sort_order}\n                label=\"Ordine\"\n                onChange={handleFilterChange}\n                disabled={!filters.sort_by}\n              >\n                <MenuItem value=\"asc\">Crescente</MenuItem>\n                <MenuItem value=\"desc\">Decrescente</MenuItem>\n              </Select>\n            </FormControl>\n          </Grid>\n        </Grid>\n\n        <Box sx={{ mt: 2, display: 'flex', justifyContent: 'flex-end' }}>\n          <Button\n            variant=\"outlined\"\n            startIcon={<ClearIcon />}\n            onClick={resetFilters}\n            sx={{ mr: 1 }}\n          >\n            Resetta Filtri\n          </Button>\n        </Box>\n      </Paper>\n    );\n  };\n\n  // Renderizza il pannello delle statistiche\n  const renderStatsPanel = () => {\n    if (!stats) return null;\n\n    return (\n      <Paper sx={{ mb: 3, p: 2 }}>\n        <Typography variant=\"h6\" gutterBottom>Statistiche</Typography>\n        {loadingStats ? (\n          <LinearProgress />\n        ) : (\n          <Grid container spacing={2}>\n            <Grid item xs={12} md={4}>\n              <Typography variant=\"subtitle1\" gutterBottom>Totali</Typography>\n              <Typography variant=\"body2\">Cavi Attivi: {stats.totali.cavi_attivi}</Typography>\n              <Typography variant=\"body2\">Cavi Spare: {stats.totali.cavi_spare}</Typography>\n              <Typography variant=\"body2\">Totale Cavi: {stats.totali.cavi_totali}</Typography>\n            </Grid>\n\n            <Grid item xs={12} md={4}>\n              <Typography variant=\"subtitle1\" gutterBottom>Metrature</Typography>\n              <Typography variant=\"body2\">Metri Teorici: {stats.metrature.metri_teorici_totali.toFixed(2)}</Typography>\n              <Typography variant=\"body2\">Metri Posati: {stats.metrature.metri_reali_totali.toFixed(2)}</Typography>\n              <Box sx={{ display: 'flex', alignItems: 'center', mt: 1 }}>\n                <Box sx={{ width: '100%', mr: 1 }}>\n                  <LinearProgress\n                    variant=\"determinate\"\n                    value={stats.metrature.percentuale_completamento}\n                    sx={{ height: 10, borderRadius: 5 }}\n                  />\n                </Box>\n                <Box sx={{ minWidth: 35 }}>\n                  <Typography variant=\"body2\" color=\"text.secondary\">{`${stats.metrature.percentuale_completamento.toFixed(1)}%`}</Typography>\n                </Box>\n              </Box>\n            </Grid>\n\n            <Grid item xs={12} md={4}>\n              <Typography variant=\"subtitle1\" gutterBottom>Stati</Typography>\n              <Box sx={{ display: 'flex', flexWrap: 'wrap', gap: 1 }}>\n                {stats.stati.map((stato, index) => (\n                  <Chip\n                    key={index}\n                    label={`${stato.stato}: ${stato.count}`}\n                    size=\"small\"\n                    onClick={() => {\n                      setFilters(prev => ({\n                        ...prev,\n                        stato_installazione: stato.stato === 'Non specificato' ? '' : stato.stato\n                      }));\n                    }}\n                  />\n                ))}\n              </Box>\n            </Grid>\n          </Grid>\n        )}\n      </Paper>\n    );\n  };\n\n  return (\n    <Box className=\"cavi-page\">\n      <Paper sx={{ mb: 3, p: 2 }}>\n        <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>\n          <Typography variant=\"h5\">\n            Visualizzazione Cavi - {cantiereName}\n          </Typography>\n\n          <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>\n            <IconButton\n              onClick={() => setFiltersOpen(!filtersOpen)}\n              title={filtersOpen ? \"Nascondi filtri\" : \"Mostra filtri\"}\n              color={filtersOpen ? \"primary\" : \"default\"}\n            >\n              <FilterListIcon />\n            </IconButton>\n\n            <IconButton\n              onClick={() => window.location.reload()}\n              title=\"Ricarica la pagina\"\n            >\n              <RefreshIcon />\n            </IconButton>\n\n            <Box sx={{ display: 'flex', border: '1px solid #ddd', borderRadius: 1 }}>\n              <IconButton\n                sx={{ color: viewMode === 'table' ? '#212529' : '#6c757d' }}\n                onClick={() => handleViewModeChange('table')}\n                title=\"Vista tabellare\"\n              >\n                <ViewListIcon />\n              </IconButton>\n              <IconButton\n                sx={{ color: viewMode === 'card' ? '#212529' : '#6c757d' }}\n                onClick={() => handleViewModeChange('card')}\n                title=\"Vista a schede\"\n              >\n                <ViewModuleIcon />\n              </IconButton>\n            </Box>\n          </Box>\n        </Box>\n      </Paper>\n\n      {/* Pannello dei filtri */}\n      {renderFilterPanel()}\n\n      {/* Pannello delle statistiche */}\n      {renderStatsPanel()}\n\n      {loading ? (\n        <Box sx={{ display: 'flex', flexDirection: 'column', alignItems: 'center', mt: 4 }}>\n          <CircularProgress size={40} />\n          <Typography sx={{ mt: 2 }}>Caricamento cavi...</Typography>\n          <Button\n            variant=\"outlined\"\n            color=\"primary\"\n            onClick={() => window.location.reload()}\n            sx={{ mt: 2 }}\n          >\n            Ricarica la pagina\n          </Button>\n        </Box>\n      ) : error ? (\n        <Box>\n          <Alert severity=\"error\" sx={{ mb: 2 }}>\n            {error}\n            {error.includes('Network Error') && (\n              <Typography variant=\"body2\" sx={{ mt: 1 }}>\n                <strong>Suggerimento:</strong> Verifica che il server backend sia in esecuzione sulla porta 8001.\n                <br />\n                Puoi avviare il backend eseguendo il file <code>run_system.py</code> nella cartella principale del progetto.\n              </Typography>\n            )}\n          </Alert>\n          <Box sx={{ display: 'flex', gap: 2 }}>\n            <Button\n              variant=\"contained\"\n              className=\"primary-button\"\n              onClick={() => window.location.reload()}\n            >\n              Ricarica la pagina\n            </Button>\n          </Box>\n        </Box>\n      ) : (\n        <Box>\n          {/* Filtri attivi */}\n          {(filters.stato_installazione || filters.tipologia || filters.sort_by) && (\n            <Box sx={{ mb: 2, display: 'flex', flexWrap: 'wrap', gap: 1, alignItems: 'center' }}>\n              <Typography variant=\"body2\" sx={{ mr: 1 }}>Filtri attivi:</Typography>\n              {filters.stato_installazione && (\n                <Chip\n                  label={`Stato: ${filters.stato_installazione}`}\n                  size=\"small\"\n                  onDelete={() => setFilters(prev => ({ ...prev, stato_installazione: '' }))}\n                />\n              )}\n              {filters.tipologia && (\n                <Chip\n                  label={`Tipologia: ${filters.tipologia}`}\n                  size=\"small\"\n                  onDelete={() => setFilters(prev => ({ ...prev, tipologia: '' }))}\n                />\n              )}\n              {filters.sort_by && (\n                <Chip\n                  label={`Ordinamento: ${sortOptions.find(opt => opt.value === filters.sort_by)?.label || filters.sort_by} (${filters.sort_order === 'asc' ? 'Crescente' : 'Decrescente'})`}\n                  size=\"small\"\n                  onDelete={() => setFilters(prev => ({ ...prev, sort_by: '' }))}\n                />\n              )}\n              {(filters.stato_installazione || filters.tipologia || filters.sort_by) && (\n                <Button\n                  variant=\"text\"\n                  size=\"small\"\n                  startIcon={<ClearIcon />}\n                  onClick={resetFilters}\n                >\n                  Resetta tutti\n                </Button>\n              )}\n            </Box>\n          )}\n\n          <Box sx={{ mb: 3 }}>\n            <Typography variant=\"h5\" gutterBottom>\n              Cavi Attivi\n            </Typography>\n            {renderCaviTable(caviAttivi)}\n          </Box>\n\n          <Box sx={{ mt: 4 }}>\n            <Typography variant=\"h5\" gutterBottom>\n              Cavi Spare\n            </Typography>\n            {renderCaviTable(caviSpare)}\n          </Box>\n\n          {/* Dialogo dei dettagli del cavo */}\n          {renderDetailsDialog()}\n        </Box>\n      )}\n    </Box>\n  );\n};\n\nexport default VisualizzaCaviPage;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAClD,SACEC,GAAG,EACHC,UAAU,EACVC,KAAK,EACLC,MAAM,EACNC,IAAI,EACJC,IAAI,EACJC,WAAW,EACXC,KAAK,EACLC,UAAU,EACVC,KAAK,EACLC,SAAS,EACTC,SAAS,EACTC,cAAc,EACdC,SAAS,EACTC,QAAQ,EACRC,IAAI,EACJC,GAAG,EACHC,SAAS,EACTC,QAAQ,EACRC,MAAM,EACNC,WAAW,EACXC,UAAU,EACVC,IAAI,EACJC,OAAO,EACPC,gBAAgB,EAChBC,cAAc,EACdC,MAAM,EACNC,WAAW,EACXC,aAAa,EACbC,aAAa,QACR,eAAe;AACtB,SACEC,SAAS,IAAIC,aAAa,EAC1BC,OAAO,IAAIC,WAAW,EACtBC,IAAI,IAAIC,QAAQ,EAChBC,QAAQ,IAAIC,YAAY,EACxBC,UAAU,IAAIC,cAAc,EAC5BC,UAAU,IAAIC,cAAc,EAC5BC,IAAI,IAAIC,QAAQ,EAChBC,IAAI,IAAIC,QAAQ,EAChBC,MAAM,IAAIC,UAAU,EACpBC,KAAK,IAAIC,SAAS,QACb,qBAAqB;AAC5B,SAASC,WAAW,QAAQ,kBAAkB;AAC9C,SAASC,OAAO,QAAQ,2BAA2B;AACnD,OAAOC,WAAW,MAAM,4BAA4B;AACpD,SAASC,2BAA2B,QAAQ,6BAA6B;AACzE,OAAO,gBAAgB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAExB,MAAMC,kBAAkB,GAAGA,CAAA,KAAM;EAAAC,EAAA;EAAA,IAAAC,iBAAA;EAC/B,MAAM;IAAEC,eAAe;IAAEC;EAAK,CAAC,GAAGT,OAAO,CAAC,CAAC;EAC3C,MAAMU,QAAQ,GAAGX,WAAW,CAAC,CAAC;EAC9B,MAAM,CAACY,UAAU,EAAEC,aAAa,CAAC,GAAGjE,QAAQ,CAAC,IAAI,CAAC;EAClD,MAAM,CAACkE,YAAY,EAAEC,eAAe,CAAC,GAAGnE,QAAQ,CAAC,EAAE,CAAC;EACpD,MAAM,CAACoE,UAAU,EAAEC,aAAa,CAAC,GAAGrE,QAAQ,CAAC,EAAE,CAAC;EAChD,MAAM,CAACsE,SAAS,EAAEC,YAAY,CAAC,GAAGvE,QAAQ,CAAC,EAAE,CAAC;EAC9C,MAAM,CAACwE,OAAO,EAAEC,UAAU,CAAC,GAAGzE,QAAQ,CAAC,IAAI,CAAC;EAC5C,MAAM,CAAC0E,KAAK,EAAEC,QAAQ,CAAC,GAAG3E,QAAQ,CAAC,IAAI,CAAC;EACxC,MAAM,CAAC4E,QAAQ,EAAEC,WAAW,CAAC,GAAG7E,QAAQ,CAAC,OAAO,CAAC,CAAC,CAAC;;EAEnD;EACA,MAAM,CAAC8E,OAAO,EAAEC,UAAU,CAAC,GAAG/E,QAAQ,CAAC;IACrCgF,mBAAmB,EAAE,EAAE;IACvBC,SAAS,EAAE,EAAE;IACbC,OAAO,EAAE,EAAE;IACXC,UAAU,EAAE;EACd,CAAC,CAAC;;EAEF;EACA,MAAM,CAACC,YAAY,EAAEC,eAAe,CAAC,GAAGrF,QAAQ,CAAC,IAAI,CAAC;EACtD,MAAM,CAACsF,iBAAiB,EAAEC,oBAAoB,CAAC,GAAGvF,QAAQ,CAAC,KAAK,CAAC;;EAEjE;EACA,MAAM,CAACwF,KAAK,EAAEC,QAAQ,CAAC,GAAGzF,QAAQ,CAAC,IAAI,CAAC;EACxC,MAAM,CAAC0F,YAAY,EAAEC,eAAe,CAAC,GAAG3F,QAAQ,CAAC,KAAK,CAAC;;EAEvD;EACA,MAAM,CAAC4F,kBAAkB,EAAEC,qBAAqB,CAAC,GAAG7F,QAAQ,CAAC,EAAE,CAAC;EAChE,MAAM,CAAC8F,aAAa,EAAEC,gBAAgB,CAAC,GAAG/F,QAAQ,CAAC,EAAE,CAAC;;EAEtD;EACA,MAAMgG,WAAW,GAAG,CAClB;IAAEC,KAAK,EAAE,SAAS;IAAEC,KAAK,EAAE;EAAU,CAAC,EACtC;IAAED,KAAK,EAAE,iBAAiB;IAAEC,KAAK,EAAE;EAAkB,CAAC,EACtD;IAAED,KAAK,EAAE,eAAe;IAAEC,KAAK,EAAE;EAAgB,CAAC,EAClD;IAAED,KAAK,EAAE,qBAAqB;IAAEC,KAAK,EAAE;EAAsB,CAAC,EAC9D;IAAED,KAAK,EAAE,WAAW;IAAEC,KAAK,EAAE;EAAY,CAAC,EAC1C;IAAED,KAAK,EAAE,WAAW;IAAEC,KAAK,EAAE;EAAqB,CAAC,CACpD;;EAED;EACA,MAAM,CAACC,WAAW,EAAEC,cAAc,CAAC,GAAGpG,QAAQ,CAAC,KAAK,CAAC;;EAErD;EACAC,SAAS,CAAC,MAAM;IACd,MAAMoG,SAAS,GAAG,MAAAA,CAAA,KAAY;MAC5B,IAAI;QACFC,OAAO,CAACC,GAAG,CAAC,wCAAwC,CAAC;;QAErD;QACA,MAAMC,KAAK,GAAGC,YAAY,CAACC,OAAO,CAAC,OAAO,CAAC;QAC3CJ,OAAO,CAACC,GAAG,CAAC,iBAAiB,EAAE,CAAC,CAACC,KAAK,CAAC;QACvC,IAAI,CAACA,KAAK,EAAE;UACV7B,QAAQ,CAAC,iDAAiD,CAAC;UAC3DF,UAAU,CAAC,KAAK,CAAC;UACjB;QACF;;QAEA;QACA,IAAIkC,kBAAkB,GAAGF,YAAY,CAACC,OAAO,CAAC,oBAAoB,CAAC;QACnE,IAAIE,oBAAoB,GAAGH,YAAY,CAACC,OAAO,CAAC,sBAAsB,CAAC;QAEvEJ,OAAO,CAACC,GAAG,CAAC,wCAAwC,EAAE;UAAEI,kBAAkB;UAAEC;QAAqB,CAAC,CAAC;QACnGN,OAAO,CAACC,GAAG,CAAC,cAAc,EAAEzC,IAAI,CAAC;;QAEjC;QACAwC,OAAO,CAACC,GAAG,CAAC,wCAAwC,CAAC;QACrD,KAAK,IAAIM,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGJ,YAAY,CAACK,MAAM,EAAED,CAAC,EAAE,EAAE;UAC5C,MAAME,GAAG,GAAGN,YAAY,CAACM,GAAG,CAACF,CAAC,CAAC;UAC/BP,OAAO,CAACC,GAAG,CAAC,GAAGQ,GAAG,KAAKN,YAAY,CAACC,OAAO,CAACK,GAAG,CAAC,EAAE,CAAC;QACrD;;QAEA;QACA,IAAI,CAAAjD,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAEkD,IAAI,MAAK,eAAe,EAAE;UAClCV,OAAO,CAACC,GAAG,CAAC,6EAA6E,CAAC;;UAE1F;UACA,IAAIzC,IAAI,CAACmD,WAAW,EAAE;YACpBX,OAAO,CAACC,GAAG,CAAC,sCAAsC,EAAEzC,IAAI,CAACmD,WAAW,CAAC;YACrEN,kBAAkB,GAAG7C,IAAI,CAACmD,WAAW,CAACC,QAAQ,CAAC,CAAC;YAChDN,oBAAoB,GAAG9C,IAAI,CAACqD,aAAa,IAAI,YAAYrD,IAAI,CAACmD,WAAW,EAAE;;YAE3E;YACAR,YAAY,CAACW,OAAO,CAAC,oBAAoB,EAAET,kBAAkB,CAAC;YAC9DF,YAAY,CAACW,OAAO,CAAC,sBAAsB,EAAER,oBAAoB,CAAC;YAClEN,OAAO,CAACC,GAAG,CAAC,uCAAuC,EAAEI,kBAAkB,CAAC;UAC1E,CAAC,MAAM;YACL;YACA,IAAI;cACFL,OAAO,CAACC,GAAG,CAAC,qEAAqE,CAAC;cAClF,MAAMC,KAAK,GAAGC,YAAY,CAACC,OAAO,CAAC,OAAO,CAAC;cAC3C,IAAIF,KAAK,EAAE;gBACT;gBACA,MAAMa,SAAS,GAAGb,KAAK,CAACc,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC;gBACrC,MAAMC,MAAM,GAAGF,SAAS,CAACG,OAAO,CAAC,IAAI,EAAE,GAAG,CAAC,CAACA,OAAO,CAAC,IAAI,EAAE,GAAG,CAAC;gBAC9D,MAAMC,WAAW,GAAGC,kBAAkB,CAACC,IAAI,CAACJ,MAAM,CAAC,CAACD,KAAK,CAAC,EAAE,CAAC,CAACM,GAAG,CAACC,CAAC,IAAI;kBACrE,OAAO,GAAG,GAAG,CAAC,IAAI,GAAGA,CAAC,CAACC,UAAU,CAAC,CAAC,CAAC,CAACZ,QAAQ,CAAC,EAAE,CAAC,EAAEa,KAAK,CAAC,CAAC,CAAC,CAAC;gBAC9D,CAAC,CAAC,CAACC,IAAI,CAAC,EAAE,CAAC,CAAC;gBAEZ,MAAMC,OAAO,GAAGC,IAAI,CAACC,KAAK,CAACV,WAAW,CAAC;gBACvCnB,OAAO,CAACC,GAAG,CAAC,wBAAwB,EAAE0B,OAAO,CAAC;gBAE9C,IAAIA,OAAO,CAAChB,WAAW,EAAE;kBACvBX,OAAO,CAACC,GAAG,CAAC,oCAAoC,EAAE0B,OAAO,CAAChB,WAAW,CAAC;kBACtEN,kBAAkB,GAAGsB,OAAO,CAAChB,WAAW,CAACC,QAAQ,CAAC,CAAC;kBACnD;kBACAN,oBAAoB,GAAG,YAAYqB,OAAO,CAAChB,WAAW,EAAE;;kBAExD;kBACAR,YAAY,CAACW,OAAO,CAAC,oBAAoB,EAAET,kBAAkB,CAAC;kBAC9DF,YAAY,CAACW,OAAO,CAAC,sBAAsB,EAAER,oBAAoB,CAAC;kBAClEN,OAAO,CAACC,GAAG,CAAC,uCAAuC,EAAEI,kBAAkB,CAAC;gBAC1E;cACF;YACF,CAAC,CAAC,OAAOyB,CAAC,EAAE;cACV9B,OAAO,CAAC5B,KAAK,CAAC,6CAA6C,EAAE0D,CAAC,CAAC;YACjE;UACF;QACF;;QAEA;QACA,IAAI,CAACzB,kBAAkB,IAAIA,kBAAkB,KAAK,WAAW,IAAIA,kBAAkB,KAAK,MAAM,EAAE;UAC9FL,OAAO,CAAC+B,IAAI,CAAC,6EAA6E,CAAC;UAC3F;UACA1B,kBAAkB,GAAG,GAAG,CAAC,CAAC;UAC1BC,oBAAoB,GAAG,gBAAgB;;UAEvC;UACAH,YAAY,CAACW,OAAO,CAAC,oBAAoB,EAAET,kBAAkB,CAAC;UAC9DF,YAAY,CAACW,OAAO,CAAC,sBAAsB,EAAER,oBAAoB,CAAC;UAClEN,OAAO,CAACC,GAAG,CAAC,iDAAiD,EAAEI,kBAAkB,CAAC;QACpF;;QAEA;QACA,IAAI,CAACA,kBAAkB,EAAE;UACvBhC,QAAQ,CAAC,8DAA8D,CAAC;UACxEF,UAAU,CAAC,KAAK,CAAC;UACjB;QACF;;QAEA;QACA,MAAM6D,aAAa,GAAGC,QAAQ,CAAC5B,kBAAkB,EAAE,EAAE,CAAC;QACtDL,OAAO,CAACC,GAAG,CAAC,kCAAkC,EAAE+B,aAAa,CAAC;QAC9D,IAAIE,KAAK,CAACF,aAAa,CAAC,EAAE;UACxB3D,QAAQ,CAAC,2BAA2BgC,kBAAkB,mCAAmC,CAAC;UAC1FlC,UAAU,CAAC,KAAK,CAAC;UACjB;QACF;;QAEA;QACAR,aAAa,CAACqE,aAAa,CAAC;QAC5BnE,eAAe,CAACyC,oBAAoB,IAAI,YAAY0B,aAAa,EAAE,CAAC;;QAEpE;QACA,IAAI;UACF3C,eAAe,CAAC,IAAI,CAAC;UACrBW,OAAO,CAACC,GAAG,CAAC,4CAA4C,EAAE+B,aAAa,CAAC;UACxE,MAAMG,SAAS,GAAG,MAAMnF,WAAW,CAACoF,YAAY,CAACJ,aAAa,CAAC;UAC/DhC,OAAO,CAACC,GAAG,CAAC,4BAA4B,EAAEkC,SAAS,CAAC;UACpDhD,QAAQ,CAACgD,SAAS,CAAC;;UAEnB;UACA,IAAIA,SAAS,IAAIA,SAAS,CAACE,KAAK,EAAE;YAChC,MAAMA,KAAK,GAAGF,SAAS,CAACE,KAAK,CAACf,GAAG,CAACgB,IAAI,IAAIA,IAAI,CAACC,KAAK,CAAC,CAACC,MAAM,CAACD,KAAK,IAAIA,KAAK,KAAK,iBAAiB,CAAC;YAClGhD,qBAAqB,CAAC8C,KAAK,CAAC;UAC9B;UAEA,IAAIF,SAAS,IAAIA,SAAS,CAACM,SAAS,EAAE;YACpC,MAAMA,SAAS,GAAGN,SAAS,CAACM,SAAS,CAACnB,GAAG,CAACgB,IAAI,IAAIA,IAAI,CAAC3D,SAAS,CAAC,CAAC6D,MAAM,CAACE,IAAI,IAAIA,IAAI,KAAK,iBAAiB,CAAC;YAC5GjD,gBAAgB,CAACgD,SAAS,CAAC;UAC7B;UAEApD,eAAe,CAAC,KAAK,CAAC;QACxB,CAAC,CAAC,OAAOsD,UAAU,EAAE;UACnB3C,OAAO,CAAC5B,KAAK,CAAC,2CAA2C,EAAEuE,UAAU,CAAC;UACtEtD,eAAe,CAAC,KAAK,CAAC;UACtB;QACF;;QAEA;QACAW,OAAO,CAACC,GAAG,CAAC,uCAAuC,EAAE+B,aAAa,CAAC;QACnE,IAAI;UACF;UACA,MAAMY,cAAc,GAAG,IAAIC,OAAO,CAAC,CAACC,CAAC,EAAEC,MAAM,KAAK;YAChDC,UAAU,CAAC,MAAMD,MAAM,CAAC,IAAIE,KAAK,CAAC,gDAAgD,CAAC,CAAC,EAAE,KAAK,CAAC,CAAC,CAAC;UAChG,CAAC,CAAC;;UAEF;UACAjD,OAAO,CAACC,GAAG,CAAC,oDAAoD,EAAEzB,OAAO,CAAC;UAC1E,MAAM0E,WAAW,GAAGlG,WAAW,CAACmG,OAAO,CAACnB,aAAa,EAAE,CAAC,EAAExD,OAAO,CAAC;UAClE,MAAM4E,MAAM,GAAG,MAAMP,OAAO,CAACQ,IAAI,CAAC,CAACH,WAAW,EAAEN,cAAc,CAAC,CAAC;UAEhE5C,OAAO,CAACC,GAAG,CAAC,uBAAuB,EAAEmD,MAAM,CAAC;UAC5CpD,OAAO,CAACC,GAAG,CAAC,gCAAgC,EAAEmD,MAAM,GAAGA,MAAM,CAAC5C,MAAM,GAAG,CAAC,CAAC;UACzE,IAAI4C,MAAM,IAAIA,MAAM,CAAC5C,MAAM,GAAG,CAAC,EAAE;YAC/BR,OAAO,CAACC,GAAG,CAAC,oBAAoB,EAAEmD,MAAM,CAAC,CAAC,CAAC,CAAC;UAC9C,CAAC,MAAM;YACLpD,OAAO,CAAC+B,IAAI,CAAC,4CAA4C,EAAEC,aAAa,CAAC;UAC3E;UACAjE,aAAa,CAACqF,MAAM,IAAI,EAAE,CAAC;QAC7B,CAAC,CAAC,OAAOE,SAAS,EAAE;UAClBtD,OAAO,CAAC5B,KAAK,CAAC,yCAAyC,EAAEkF,SAAS,CAAC;UACnEtD,OAAO,CAAC5B,KAAK,CAAC,8BAA8B,EAAE;YAC5CmF,OAAO,EAAED,SAAS,CAACC,OAAO;YAC1BC,MAAM,EAAEF,SAAS,CAACE,MAAM;YACxBC,IAAI,EAAEH,SAAS,CAACG,IAAI;YACpBC,KAAK,EAAEJ,SAAS,CAACI,KAAK;YACtBC,IAAI,EAAEL,SAAS,CAACK,IAAI;YACpBC,IAAI,EAAEN,SAAS,CAACM,IAAI;YACpBC,QAAQ,EAAEP,SAAS,CAACO,QAAQ,GAAG;cAC7BL,MAAM,EAAEF,SAAS,CAACO,QAAQ,CAACL,MAAM;cACjCM,UAAU,EAAER,SAAS,CAACO,QAAQ,CAACC,UAAU;cACzCL,IAAI,EAAEH,SAAS,CAACO,QAAQ,CAACJ;YAC3B,CAAC,GAAG;UACN,CAAC,CAAC;;UAEF;UACA1F,aAAa,CAAC,EAAE,CAAC;UACjBiC,OAAO,CAAC+B,IAAI,CAAC,sDAAsD,CAAC;;UAEpE;UACA1D,QAAQ,CAAC,2CAA2CiF,SAAS,CAACC,OAAO,+CAA+C,CAAC;QACvH;;QAEA;QACAvD,OAAO,CAACC,GAAG,CAAC,sCAAsC,EAAE+B,aAAa,CAAC;QAClE,IAAI;UACF;UACA,MAAMY,cAAc,GAAG,IAAIC,OAAO,CAAC,CAACC,CAAC,EAAEC,MAAM,KAAK;YAChDC,UAAU,CAAC,MAAMD,MAAM,CAAC,IAAIE,KAAK,CAAC,+CAA+C,CAAC,CAAC,EAAE,KAAK,CAAC,CAAC,CAAC;UAC/F,CAAC,CAAC;;UAEF;UACAjD,OAAO,CAACC,GAAG,CAAC,0CAA0C,CAAC;UACvD;UACA,MAAM8D,YAAY,GAAG/G,WAAW,CAACmG,OAAO,CAACnB,aAAa,EAAE,CAAC,CAAC;UAC1D,MAAMgC,KAAK,GAAG,MAAMnB,OAAO,CAACQ,IAAI,CAAC,CAACU,YAAY,EAAEnB,cAAc,CAAC,CAAC;UAEhE5C,OAAO,CAACC,GAAG,CAAC,sBAAsB,EAAE+D,KAAK,CAAC;UAC1ChE,OAAO,CAACC,GAAG,CAAC,+BAA+B,EAAE+D,KAAK,GAAGA,KAAK,CAACxD,MAAM,GAAG,CAAC,CAAC;UACtE,IAAIwD,KAAK,IAAIA,KAAK,CAACxD,MAAM,GAAG,CAAC,EAAE;YAC7BR,OAAO,CAACC,GAAG,CAAC,mBAAmB,EAAE+D,KAAK,CAAC,CAAC,CAAC,CAAC;UAC5C,CAAC,MAAM;YACLhE,OAAO,CAAC+B,IAAI,CAAC,2CAA2C,EAAEC,aAAa,CAAC;UAC1E;UACA/D,YAAY,CAAC+F,KAAK,IAAI,EAAE,CAAC;QAC3B,CAAC,CAAC,OAAOC,UAAU,EAAE;UACnBjE,OAAO,CAAC5B,KAAK,CAAC,wCAAwC,EAAE6F,UAAU,CAAC;UACnEjE,OAAO,CAAC5B,KAAK,CAAC,6BAA6B,EAAE;YAC3CmF,OAAO,EAAEU,UAAU,CAACV,OAAO;YAC3BC,MAAM,EAAES,UAAU,CAACT,MAAM;YACzBC,IAAI,EAAEQ,UAAU,CAACR,IAAI;YACrBC,KAAK,EAAEO,UAAU,CAACP,KAAK;YACvBC,IAAI,EAAEM,UAAU,CAACN,IAAI;YACrBC,IAAI,EAAEK,UAAU,CAACL,IAAI;YACrBC,QAAQ,EAAEI,UAAU,CAACJ,QAAQ,GAAG;cAC9BL,MAAM,EAAES,UAAU,CAACJ,QAAQ,CAACL,MAAM;cAClCM,UAAU,EAAEG,UAAU,CAACJ,QAAQ,CAACC,UAAU;cAC1CL,IAAI,EAAEQ,UAAU,CAACJ,QAAQ,CAACJ;YAC5B,CAAC,GAAG;UACN,CAAC,CAAC;;UAEF;UACAxF,YAAY,CAAC,EAAE,CAAC;;UAEhB;UACA,IAAI,CAACG,KAAK,EAAE;YACVC,QAAQ,CAAC,0CAA0C4F,UAAU,CAACV,OAAO,+CAA+C,CAAC;UACvH;QACF;;QAEA;QACApF,UAAU,CAAC,KAAK,CAAC;MAEnB,CAAC,CAAC,OAAO+F,GAAG,EAAE;QAAA,IAAAC,aAAA,EAAAC,cAAA,EAAAC,cAAA,EAAAC,cAAA,EAAAC,cAAA,EAAAC,mBAAA;QACZxE,OAAO,CAAC5B,KAAK,CAAC,kCAAkC,EAAE8F,GAAG,CAAC;QACtDlE,OAAO,CAAC5B,KAAK,CAAC,2BAA2B,EAAE;UACzCmF,OAAO,EAAEW,GAAG,CAACX,OAAO;UACpBC,MAAM,EAAEU,GAAG,CAACV,MAAM,MAAAW,aAAA,GAAID,GAAG,CAACL,QAAQ,cAAAM,aAAA,uBAAZA,aAAA,CAAcX,MAAM;UAC1CC,IAAI,EAAES,GAAG,CAACT,IAAI,MAAAW,cAAA,GAAIF,GAAG,CAACL,QAAQ,cAAAO,cAAA,uBAAZA,cAAA,CAAcX,IAAI;UACpCC,KAAK,EAAEQ,GAAG,CAACR;QACb,CAAC,CAAC;;QAEF;QACA,IAAIe,YAAY,GAAG,oBAAoB;QAEvC,IAAIP,GAAG,CAACX,OAAO,IAAIW,GAAG,CAACX,OAAO,CAACmB,QAAQ,CAAC,wBAAwB,CAAC,EAAE;UACjED,YAAY,GAAGP,GAAG,CAACX,OAAO;QAC5B,CAAC,MAAM,IAAIW,GAAG,CAACV,MAAM,KAAK,GAAG,IAAIU,GAAG,CAACV,MAAM,KAAK,GAAG,IACzC,EAAAa,cAAA,GAAAH,GAAG,CAACL,QAAQ,cAAAQ,cAAA,uBAAZA,cAAA,CAAcb,MAAM,MAAK,GAAG,IAAI,EAAAc,cAAA,GAAAJ,GAAG,CAACL,QAAQ,cAAAS,cAAA,uBAAZA,cAAA,CAAcd,MAAM,MAAK,GAAG,EAAE;UACtEiB,YAAY,GAAG,mEAAmE;QACpF,CAAC,MAAM,KAAAF,cAAA,GAAIL,GAAG,CAACL,QAAQ,cAAAU,cAAA,gBAAAC,mBAAA,GAAZD,cAAA,CAAcd,IAAI,cAAAe,mBAAA,eAAlBA,mBAAA,CAAoBG,MAAM,EAAE;UACrC;UACAF,YAAY,GAAG,eAAeP,GAAG,CAACL,QAAQ,CAACJ,IAAI,CAACkB,MAAM,EAAE;QAC1D,CAAC,MAAM,IAAIT,GAAG,CAACP,IAAI,KAAK,aAAa,EAAE;UACrC;UACAc,YAAY,GAAG,yEAAyE;QAC1F,CAAC,MAAM,IAAIP,GAAG,CAACX,OAAO,EAAE;UACtBkB,YAAY,GAAGP,GAAG,CAACX,OAAO;QAC5B;QAEAlF,QAAQ,CAAC,gCAAgCoG,YAAY,sBAAsB,CAAC;;QAE5E;QACA1G,aAAa,CAAC,EAAE,CAAC;QACjBE,YAAY,CAAC,EAAE,CAAC;MAClB,CAAC,SAAS;QACRE,UAAU,CAAC,KAAK,CAAC;MACnB;IACF,CAAC;IAED4B,SAAS,CAAC,CAAC;EACb,CAAC,EAAE,CAACvB,OAAO,CAAC,CAAC,CAAC,CAAC;;EAEf;EACA,MAAMoG,kBAAkB,GAAIC,KAAK,IAAK;IACpC,MAAM;MAAEjB,IAAI;MAAEjE;IAAM,CAAC,GAAGkF,KAAK,CAACC,MAAM;IACpCrG,UAAU,CAACsG,IAAI,KAAK;MAClB,GAAGA,IAAI;MACP,CAACnB,IAAI,GAAGjE;IACV,CAAC,CAAC,CAAC;EACL,CAAC;;EAED;EACA,MAAMqF,YAAY,GAAGA,CAAA,KAAM;IACzBvG,UAAU,CAAC;MACTC,mBAAmB,EAAE,EAAE;MACvBC,SAAS,EAAE,EAAE;MACbC,OAAO,EAAE,EAAE;MACXC,UAAU,EAAE;IACd,CAAC,CAAC;EACJ,CAAC;;EAED;EACA,MAAMoG,iBAAiB,GAAIC,IAAI,IAAK;IAClCnG,eAAe,CAACmG,IAAI,CAAC;IACrBjG,oBAAoB,CAAC,IAAI,CAAC;EAC5B,CAAC;;EAED;EACA,MAAMkG,kBAAkB,GAAGA,CAAA,KAAM;IAC/BlG,oBAAoB,CAAC,KAAK,CAAC;IAC3BF,eAAe,CAAC,IAAI,CAAC;EACvB,CAAC;;EAED;;EAEA;EACA,MAAMqG,eAAe,GAAIC,IAAI,IAAK;IAChC,IAAI,CAACA,IAAI,IAAIA,IAAI,CAAC7E,MAAM,KAAK,CAAC,EAAE;MAC9B,oBACErD,OAAA,CAAChD,KAAK;QAACmL,QAAQ,EAAC,MAAM;QAAAC,QAAA,GAAC,0CAErB,eAAApI,OAAA,CAACpD,MAAM;UACLyL,OAAO,EAAC,MAAM;UACdC,KAAK,EAAC,SAAS;UACfC,OAAO,EAAEA,CAAA,KAAMC,MAAM,CAACC,QAAQ,CAACC,MAAM,CAAC,CAAE;UACxCC,EAAE,EAAE;YAAEC,EAAE,EAAE;UAAE,CAAE;UAAAR,QAAA,EACf;QAED;UAAAS,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACJ,CAAC;IAEZ;;IAEA;IACA,MAAMC,cAAc,GAAI7D,KAAK,IAAK;MAChC;MACA,MAAM8D,UAAU,GAAG,CAAC9D,KAAK,IAAI,EAAE,EAAE+D,WAAW,CAAC,CAAC;MAE9C,QAAQD,UAAU;QAChB,KAAK,QAAQ;QACb,KAAK,YAAY;UAAE;UACjB,OAAO,SAAS;QAAE;QACpB,KAAK,oBAAoB;UACvB,OAAO,SAAS;QAAE;QACpB,KAAK,kBAAkB;UACrB,OAAO,SAAS;QAAE;QACpB,KAAK,WAAW;UACd,OAAO,SAAS;QAAE;QACpB,KAAK,WAAW;QAChB,KAAK,eAAe;UAAE;UACpB,OAAO,SAAS;QAAE;QACpB,KAAK,SAAS;QACd,KAAK,UAAU,CAAC,CAAC;QACjB,KAAK,kBAAkB;UACrB,OAAO,SAAS;QAAE;QACpB;UACErG,OAAO,CAACC,GAAG,CAAC,yBAAyB,EAAEsC,KAAK,CAAC;UAC7C,OAAO,SAAS;QAAE;MACtB;IACF,CAAC;;IAED;IACA,MAAMgE,mBAAmB,GAAIC,YAAY,IAAK;MAC5C,IAAI,CAACA,YAAY,EAAE,OAAO,eAAe;MAEzC,QAAQA,YAAY;QAClB,KAAK,CAAC;UAAE,OAAO,oBAAoB;QACnC,KAAK,CAAC;UAAE,OAAO,kBAAkB;QACjC,KAAK,CAAC;UAAE,OAAO,WAAW;QAC1B;UAAS,OAAO,eAAe;MACjC;IACF,CAAC;;IAED;;IAEA,IAAIlI,QAAQ,KAAK,OAAO,EAAE;MACxB,oBACEnB,OAAA,CAAC3C,cAAc;QAACiM,SAAS,EAAE3M,KAAM;QAACgM,EAAE,EAAE;UAAEY,EAAE,EAAE,CAAC;UAAEC,SAAS,EAAE;QAAO,CAAE;QAAApB,QAAA,eACjEpI,OAAA,CAAC9C,KAAK;UAACuM,IAAI,EAAC,OAAO;UAAArB,QAAA,gBACjBpI,OAAA,CAAC1C,SAAS;YAAA8K,QAAA,eACRpI,OAAA,CAACzC,QAAQ;cAAA6K,QAAA,gBACPpI,OAAA,CAAC5C,SAAS;gBAAAgL,QAAA,EAAC;cAAO;gBAAAS,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAW,CAAC,eAC9BhJ,OAAA,CAAC5C,SAAS;gBAAAgL,QAAA,EAAC;cAAO;gBAAAS,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAW,CAAC,eAC9BhJ,OAAA,CAAC5C,SAAS;gBAAAgL,QAAA,EAAC;cAAS;gBAAAS,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAW,CAAC,eAChChJ,OAAA,CAAC5C,SAAS;gBAAAgL,QAAA,EAAC;cAAM;gBAAAS,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAW,CAAC,eAC7BhJ,OAAA,CAAC5C,SAAS;gBAAAgL,QAAA,EAAC;cAAO;gBAAAS,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAW,CAAC,eAC9BhJ,OAAA,CAAC5C,SAAS;gBAAAgL,QAAA,EAAC;cAAY;gBAAAS,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAW,CAAC,eACnChJ,OAAA,CAAC5C,SAAS;gBAAAgL,QAAA,EAAC;cAAW;gBAAAS,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAW,CAAC,eAClChJ,OAAA,CAAC5C,SAAS;gBAAAgL,QAAA,EAAC;cAAQ;gBAAAS,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAW,CAAC,eAC/BhJ,OAAA,CAAC5C,SAAS;gBAAAgL,QAAA,EAAC;cAAQ;gBAAAS,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAW,CAAC,eAC/BhJ,OAAA,CAAC5C,SAAS;gBAAAgL,QAAA,EAAC;cAAK;gBAAAS,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAW,CAAC,eAC5BhJ,OAAA,CAAC5C,SAAS;gBAAAgL,QAAA,EAAC;cAAY;gBAAAS,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAW,CAAC,eACnChJ,OAAA,CAAC5C,SAAS;gBAAAgL,QAAA,EAAC;cAAM;gBAAAS,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAW,CAAC,eAC7BhJ,OAAA,CAAC5C,SAAS;gBAAAgL,QAAA,EAAC;cAAM;gBAAAS,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAW,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACrB;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACF,CAAC,eACZhJ,OAAA,CAAC7C,SAAS;YAAAiL,QAAA,EACPF,IAAI,CAAC/D,GAAG,CAAE4D,IAAI,IAAK;cAClB;cACA,MAAM2B,OAAO,GAAGC,MAAM,CAAC5B,IAAI,CAAC2B,OAAO,CAAC,CAAC3F,OAAO,CAAC,GAAG,EAAE,EAAE,CAAC;cACrD,MAAM6F,OAAO,GAAG7B,IAAI,CAAC6B,OAAO,IAAI,GAAG;cACnC,MAAMpI,SAAS,GAAGuG,IAAI,CAACvG,SAAS,IAAI,GAAG;;cAEvC;cACA,IAAIqI,YAAY,GAAG,GAAG;cACtB,IAAI;gBACF,MAAMC,UAAU,GAAGhF,QAAQ,CAACiD,IAAI,CAAC8B,YAAY,EAAE,EAAE,CAAC,IAAI,CAAC;gBACvDA,YAAY,GAAGC,UAAU,GAAG,CAAC,GAAGH,MAAM,CAACG,UAAU,CAAC,GAAG,GAAG;cAC1D,CAAC,CAAC,OAAOnF,CAAC,EAAE;gBACVkF,YAAY,GAAG9B,IAAI,CAAC8B,YAAY,IAAI,GAAG;cACzC;;cAEA;cACA,IAAIE,OAAO,GAAG,GAAG;cACjB,MAAMC,WAAW,GAAGjC,IAAI,CAACgC,OAAO;cAChC,IAAI,OAAOC,WAAW,KAAK,QAAQ,IAAIA,WAAW,KAAK,CAAC,EAAE;gBACxDD,OAAO,GAAG,GAAG;cACf,CAAC,MAAM;gBACLA,OAAO,GAAGC,WAAW,GAAGL,MAAM,CAACK,WAAW,CAAC,GAAG,GAAG;cACnD;cAEA,MAAMC,mBAAmB,GAAGlC,IAAI,CAACkC,mBAAmB,IAAI,GAAG;cAC3D,MAAMC,iBAAiB,GAAGnC,IAAI,CAACmC,iBAAiB,IAAI,GAAG;cACvD,MAAMC,aAAa,GAAGpC,IAAI,CAACoC,aAAa,GAAG,GAAGC,UAAU,CAACrC,IAAI,CAACoC,aAAa,CAAC,CAACE,OAAO,CAAC,CAAC,CAAC,EAAE,GAAG,GAAG;cAC/F,MAAMC,WAAW,GAAGvC,IAAI,CAACwC,eAAe,GAAG,GAAGH,UAAU,CAACrC,IAAI,CAACwC,eAAe,CAAC,CAACF,OAAO,CAAC,CAAC,CAAC,EAAE,GAAG,GAAG;cACjG,MAAMjF,KAAK,GAAG2C,IAAI,CAACxG,mBAAmB,IAAI,eAAe;cACzD,MAAM8H,YAAY,GAAGD,mBAAmB,CAACrB,IAAI,CAACsB,YAAY,CAAC;cAC3D,MAAMmB,MAAM,GAAGzC,IAAI,CAAC0C,SAAS,IAAI,GAAG;;cAEpC;cACA,MAAMC,WAAW,GAAGzB,cAAc,CAAC7D,KAAK,CAAC;cAEzC,oBACEpF,OAAA,CAACzC,QAAQ;gBAEPoL,EAAE,EAAE;kBAAE,SAAS,EAAE;oBAAEgC,eAAe,EAAE;kBAAU;gBAAE,CAAE;gBAClDpC,OAAO,EAAEA,CAAA,KAAMT,iBAAiB,CAACC,IAAI,CAAE;gBAAAK,QAAA,gBAEvCpI,OAAA,CAAC5C,SAAS;kBAAAgL,QAAA,EAAEsB;gBAAO;kBAAAb,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAY,CAAC,eAChChJ,OAAA,CAAC5C,SAAS;kBAAAgL,QAAA,EAAEwB;gBAAO;kBAAAf,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAY,CAAC,eAChChJ,OAAA,CAAC5C,SAAS;kBAAAgL,QAAA,EAAE5G;gBAAS;kBAAAqH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAY,CAAC,eAClChJ,OAAA,CAAC5C,SAAS;kBAAAgL,QAAA,EAAEyB;gBAAY;kBAAAhB,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAY,CAAC,eACrChJ,OAAA,CAAC5C,SAAS;kBAAAgL,QAAA,EAAE2B;gBAAO;kBAAAlB,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAY,CAAC,eAChChJ,OAAA,CAAC5C,SAAS;kBAAAgL,QAAA,EAAE6B;gBAAmB;kBAAApB,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAY,CAAC,eAC5ChJ,OAAA,CAAC5C,SAAS;kBAAAgL,QAAA,EAAE8B;gBAAiB;kBAAArB,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAY,CAAC,eAC1ChJ,OAAA,CAAC5C,SAAS;kBAAAgL,QAAA,EAAE+B;gBAAa;kBAAAtB,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAY,CAAC,eACtChJ,OAAA,CAAC5C,SAAS;kBAAAgL,QAAA,EAAEkC;gBAAW;kBAAAzB,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAY,CAAC,eACpChJ,OAAA,CAAC5C,SAAS;kBAAAgL,QAAA,eACRpI,OAAA,CAACjC,IAAI;oBACH0E,KAAK,EAAE3C,2BAA2B,CAACsF,KAAK,CAAE;oBAC1CqE,IAAI,EAAC,OAAO;oBACZd,EAAE,EAAE;sBACFgC,eAAe,EAAED,WAAW;sBAC5BpC,KAAK,EAAE,MAAM;sBACbsC,UAAU,EAAE;oBACd;kBAAE;oBAAA/B,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACH;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACO,CAAC,eACZhJ,OAAA,CAAC5C,SAAS;kBAAAgL,QAAA,EAAEiB;gBAAY;kBAAAR,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAY,CAAC,eACrChJ,OAAA,CAAC5C,SAAS;kBAAAgL,QAAA,EAAEoC;gBAAM;kBAAA3B,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAY,CAAC,eAC/BhJ,OAAA,CAAC5C,SAAS;kBAAAgL,QAAA,eACRpI,OAAA,CAAC/C,UAAU;oBACTwM,IAAI,EAAC,OAAO;oBACZlB,OAAO,EAAG5D,CAAC,IAAK;sBACdA,CAAC,CAACkG,eAAe,CAAC,CAAC;sBACnB/C,iBAAiB,CAACC,IAAI,CAAC;oBACzB,CAAE;oBACF+C,KAAK,EAAC,qBAAqB;oBAAA1C,QAAA,eAE3BpI,OAAA,CAACV,QAAQ;sBAACyL,QAAQ,EAAC;oBAAO;sBAAAlC,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAE;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACnB;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACJ,CAAC;cAAA,GArCPjB,IAAI,CAAC2B,OAAO;gBAAAb,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAsCT,CAAC;YAEf,CAAC;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACO,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACP;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACM,CAAC;IAErB,CAAC,MAAM;MACL;MACA,oBACEhJ,OAAA,CAACnD,IAAI;QAACmO,SAAS;QAACC,OAAO,EAAE,CAAE;QAAA7C,QAAA,EACxBF,IAAI,CAAC/D,GAAG,CAAE4D,IAAI,IAAK;UAClB,MAAM3C,KAAK,GAAG2C,IAAI,CAACxG,mBAAmB,IAAI,eAAe;UACzD,MAAMmJ,WAAW,GAAGzB,cAAc,CAAC7D,KAAK,CAAC;UACzC,MAAMiE,YAAY,GAAGD,mBAAmB,CAACrB,IAAI,CAACsB,YAAY,CAAC;UAE3D,oBACErJ,OAAA,CAACnD,IAAI;YAACsI,IAAI;YAAC+F,EAAE,EAAE,EAAG;YAACC,EAAE,EAAE,CAAE;YAACC,EAAE,EAAE,CAAE;YAAAhD,QAAA,eAC9BpI,OAAA,CAAClD,IAAI;cACH6L,EAAE,EAAE;gBACF0C,MAAM,EAAE,SAAS;gBACjB,SAAS,EAAE;kBAAEC,SAAS,EAAE;gBAAE,CAAC;gBAC3BC,UAAU,EAAE,aAAab,WAAW;cACtC,CAAE;cACFnC,OAAO,EAAEA,CAAA,KAAMT,iBAAiB,CAACC,IAAI,CAAE;cAAAK,QAAA,eAEvCpI,OAAA,CAACjD,WAAW;gBAAAqL,QAAA,gBACVpI,OAAA,CAACtD,UAAU;kBAAC2L,OAAO,EAAC,IAAI;kBAACiB,SAAS,EAAC,KAAK;kBAACX,EAAE,EAAE;oBAAE6C,OAAO,EAAE,MAAM;oBAAEC,cAAc,EAAE;kBAAgB,CAAE;kBAAArD,QAAA,GAC/FL,IAAI,CAAC2B,OAAO,eACb1J,OAAA,CAACjC,IAAI;oBACH0E,KAAK,EAAE3C,2BAA2B,CAACsF,KAAK,CAAE;oBAC1CqE,IAAI,EAAC,OAAO;oBACZd,EAAE,EAAE;sBACFgC,eAAe,EAAED,WAAW;sBAC5BpC,KAAK,EAAE,MAAM;sBACbsC,UAAU,EAAE;oBACd;kBAAE;oBAAA/B,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACH,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACQ,CAAC,eACbhJ,OAAA,CAACtD,UAAU;kBAAC2L,OAAO,EAAC,OAAO;kBAACC,KAAK,EAAC,gBAAgB;kBAAAF,QAAA,GAAC,WACxC,EAACL,IAAI,CAAC2D,OAAO,IAAI,KAAK;gBAAA;kBAAA7C,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACrB,CAAC,eACbhJ,OAAA,CAACtD,UAAU;kBAAC2L,OAAO,EAAC,OAAO;kBAACC,KAAK,EAAC,gBAAgB;kBAAAF,QAAA,GAAC,aACtC,EAACL,IAAI,CAACvG,SAAS,IAAI,KAAK;gBAAA;kBAAAqH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACzB,CAAC,eACbhJ,OAAA,CAACtD,UAAU;kBAAC2L,OAAO,EAAC,OAAO;kBAACC,KAAK,EAAC,gBAAgB;kBAAAF,QAAA,GAAC,YACvC,EAACL,IAAI,CAACkC,mBAAmB,IAAI,KAAK,EAAC,KAAG,EAAClC,IAAI,CAAC4D,eAAe,IAAI,KAAK;gBAAA;kBAAA9C,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACpE,CAAC,eACbhJ,OAAA,CAACtD,UAAU;kBAAC2L,OAAO,EAAC,OAAO;kBAACC,KAAK,EAAC,gBAAgB;kBAAAF,QAAA,GAAC,UACzC,EAACL,IAAI,CAACmC,iBAAiB,IAAI,KAAK,EAAC,KAAG,EAACnC,IAAI,CAAC6D,aAAa,IAAI,KAAK;gBAAA;kBAAA/C,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAC9D,CAAC,eACbhJ,OAAA,CAACtD,UAAU;kBAAC2L,OAAO,EAAC,OAAO;kBAACC,KAAK,EAAC,gBAAgB;kBAAAF,QAAA,GAAC,iBAClC,EAACL,IAAI,CAACoC,aAAa,IAAI,KAAK;gBAAA;kBAAAtB,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACjC,CAAC,eACbhJ,OAAA,CAACtD,UAAU;kBAAC2L,OAAO,EAAC,OAAO;kBAACC,KAAK,EAAC,gBAAgB;kBAAAF,QAAA,GAAC,mBAChC,EAACL,IAAI,CAACwC,eAAe,IAAI,GAAG;gBAAA;kBAAA1B,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACnC,CAAC,eACbhJ,OAAA,CAACtD,UAAU;kBAAC2L,OAAO,EAAC,OAAO;kBAACC,KAAK,EAAC,gBAAgB;kBAAAF,QAAA,GAAC,gBACnC,EAACiB,YAAY;gBAAA;kBAAAR,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACjB,CAAC,eACbhJ,OAAA,CAACtD,UAAU;kBAAC2L,OAAO,EAAC,OAAO;kBAACC,KAAK,EAAC,gBAAgB;kBAAAF,QAAA,GAAC,UACzC,EAACL,IAAI,CAAC0C,SAAS,IAAI,KAAK;gBAAA;kBAAA5B,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACtB,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACF;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACV;UAAC,GA/C6BjB,IAAI,CAAC2B,OAAO;YAAAb,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAgD5C,CAAC;QAEX,CAAC;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACE,CAAC;IAEX;EACF,CAAC;;EAED;EACA,MAAM6C,oBAAoB,GAAIC,IAAI,IAAK;IACrC1K,WAAW,CAAC0K,IAAI,CAAC;EACnB,CAAC;;EAED;EACA,MAAMC,mBAAmB,GAAGA,CAAA,KAAM;IAChC,IAAI,CAACpK,YAAY,EAAE,OAAO,IAAI;IAE9B,oBACE3B,OAAA,CAAC7B,MAAM;MAAC6N,IAAI,EAAEnK,iBAAkB;MAACoK,OAAO,EAAEjE,kBAAmB;MAACkE,QAAQ,EAAC,IAAI;MAACC,SAAS;MAAA/D,QAAA,gBACnFpI,OAAA,CAAC5B,WAAW;QAAAgK,QAAA,GAAC,iBACI,EAACzG,YAAY,CAAC+H,OAAO;MAAA;QAAAb,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACzB,CAAC,eACdhJ,OAAA,CAAC3B,aAAa;QAAC+N,QAAQ;QAAAhE,QAAA,eACrBpI,OAAA,CAACnD,IAAI;UAACmO,SAAS;UAACC,OAAO,EAAE,CAAE;UAAA7C,QAAA,gBACzBpI,OAAA,CAACnD,IAAI;YAACsI,IAAI;YAAC+F,EAAE,EAAE,EAAG;YAACE,EAAE,EAAE,CAAE;YAAAhD,QAAA,gBACvBpI,OAAA,CAACtD,UAAU;cAAC2L,OAAO,EAAC,WAAW;cAACgE,YAAY;cAAAjE,QAAA,EAAC;YAAqB;cAAAS,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAY,CAAC,eAC/EhJ,OAAA,CAACvD,GAAG;cAACkM,EAAE,EAAE;gBAAE2D,EAAE,EAAE;cAAE,CAAE;cAAAlE,QAAA,gBACjBpI,OAAA,CAACtD,UAAU;gBAAC2L,OAAO,EAAC,OAAO;gBAAAD,QAAA,gBAACpI,OAAA;kBAAAoI,QAAA,EAAQ;gBAAQ;kBAAAS,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,KAAC,EAACrH,YAAY,CAAC+J,OAAO,IAAI,KAAK;cAAA;gBAAA7C,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAa,CAAC,eAClGhJ,OAAA,CAACtD,UAAU;gBAAC2L,OAAO,EAAC,OAAO;gBAAAD,QAAA,gBAACpI,OAAA;kBAAAoI,QAAA,EAAQ;gBAAQ;kBAAAS,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,KAAC,EAACrH,YAAY,CAACiI,OAAO,IAAI,KAAK;cAAA;gBAAAf,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAa,CAAC,eAClGhJ,OAAA,CAACtD,UAAU;gBAAC2L,OAAO,EAAC,OAAO;gBAAAD,QAAA,gBAACpI,OAAA;kBAAAoI,QAAA,EAAQ;gBAAU;kBAAAS,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,KAAC,EAACrH,YAAY,CAACH,SAAS,IAAI,KAAK;cAAA;gBAAAqH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAa,CAAC,eACtGhJ,OAAA,CAACtD,UAAU;gBAAC2L,OAAO,EAAC,OAAO;gBAAAD,QAAA,gBAACpI,OAAA;kBAAAoI,QAAA,EAAQ;gBAAO;kBAAAS,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,KAAC,EAACrH,YAAY,CAAC4K,WAAW,IAAI,KAAK;cAAA;gBAAA1D,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAa,CAAC,eACrGhJ,OAAA,CAACtD,UAAU;gBAAC2L,OAAO,EAAC,OAAO;gBAAAD,QAAA,gBAACpI,OAAA;kBAAAoI,QAAA,EAAQ;gBAAc;kBAAAS,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,KAAC,EAACrH,YAAY,CAACkI,YAAY,IAAI,KAAK;cAAA;gBAAAhB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAa,CAAC,eAC7GhJ,OAAA,CAACtD,UAAU;gBAAC2L,OAAO,EAAC,OAAO;gBAAAD,QAAA,gBAACpI,OAAA;kBAAAoI,QAAA,EAAQ;gBAAQ;kBAAAS,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,KAAC,EAACrH,YAAY,CAACoI,OAAO,IAAI,KAAK;cAAA;gBAAAlB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAa,CAAC,eAClGhJ,OAAA,CAACtD,UAAU;gBAAC2L,OAAO,EAAC,OAAO;gBAAAD,QAAA,gBAACpI,OAAA;kBAAAoI,QAAA,EAAQ;gBAAG;kBAAAS,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,KAAC,EAACrH,YAAY,CAAC6K,EAAE,IAAI,KAAK;cAAA;gBAAA3D,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAa,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACrF,CAAC,eAENhJ,OAAA,CAACtD,UAAU;cAAC2L,OAAO,EAAC,WAAW;cAACgE,YAAY;cAAAjE,QAAA,EAAC;YAAQ;cAAAS,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAY,CAAC,eAClEhJ,OAAA,CAACvD,GAAG;cAACkM,EAAE,EAAE;gBAAE2D,EAAE,EAAE;cAAE,CAAE;cAAAlE,QAAA,gBACjBpI,OAAA,CAACtD,UAAU;gBAAC2L,OAAO,EAAC,OAAO;gBAAAD,QAAA,gBAACpI,OAAA;kBAAAoI,QAAA,EAAQ;gBAAW;kBAAAS,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,KAAC,EAACrH,YAAY,CAACsI,mBAAmB,IAAI,KAAK;cAAA;gBAAApB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAa,CAAC,eACjHhJ,OAAA,CAACtD,UAAU;gBAAC2L,OAAO,EAAC,OAAO;gBAAAD,QAAA,gBAACpI,OAAA;kBAAAoI,QAAA,EAAQ;gBAAO;kBAAAS,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,KAAC,EAACrH,YAAY,CAACgK,eAAe,IAAI,KAAK;cAAA;gBAAA9C,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAa,CAAC,eACzGhJ,OAAA,CAACtD,UAAU;gBAAC2L,OAAO,EAAC,OAAO;gBAAAD,QAAA,gBAACpI,OAAA;kBAAAoI,QAAA,EAAQ;gBAAY;kBAAAS,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,KAAC,EAACrH,YAAY,CAAC8K,2BAA2B,IAAI,KAAK;cAAA;gBAAA5D,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAa,CAAC,eAC1HhJ,OAAA,CAACtD,UAAU;gBAAC2L,OAAO,EAAC,OAAO;gBAAAD,QAAA,gBAACpI,OAAA;kBAAAoI,QAAA,EAAQ;gBAAa;kBAAAS,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,KAAC,EAACrH,YAAY,CAAC+K,qBAAqB,IAAI,KAAK;cAAA;gBAAA7D,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAa,CAAC,eACrHhJ,OAAA,CAACtD,UAAU;gBAAC2L,OAAO,EAAC,OAAO;gBAAAD,QAAA,gBAACpI,OAAA;kBAAAoI,QAAA,EAAQ;gBAAQ;kBAAAS,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,KAAC,EAACrH,YAAY,CAACgL,gBAAgB,IAAI,KAAK;cAAA;gBAAA9D,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAa,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACxG,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACF,CAAC,eAEPhJ,OAAA,CAACnD,IAAI;YAACsI,IAAI;YAAC+F,EAAE,EAAE,EAAG;YAACE,EAAE,EAAE,CAAE;YAAAhD,QAAA,gBACvBpI,OAAA,CAACtD,UAAU;cAAC2L,OAAO,EAAC,WAAW;cAACgE,YAAY;cAAAjE,QAAA,EAAC;YAAM;cAAAS,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAY,CAAC,eAChEhJ,OAAA,CAACvD,GAAG;cAACkM,EAAE,EAAE;gBAAE2D,EAAE,EAAE;cAAE,CAAE;cAAAlE,QAAA,gBACjBpI,OAAA,CAACtD,UAAU;gBAAC2L,OAAO,EAAC,OAAO;gBAAAD,QAAA,gBAACpI,OAAA;kBAAAoI,QAAA,EAAQ;gBAAW;kBAAAS,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,KAAC,EAACrH,YAAY,CAACuI,iBAAiB,IAAI,KAAK;cAAA;gBAAArB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAa,CAAC,eAC/GhJ,OAAA,CAACtD,UAAU;gBAAC2L,OAAO,EAAC,OAAO;gBAAAD,QAAA,gBAACpI,OAAA;kBAAAoI,QAAA,EAAQ;gBAAO;kBAAAS,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,KAAC,EAACrH,YAAY,CAACiK,aAAa,IAAI,KAAK;cAAA;gBAAA/C,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAa,CAAC,eACvGhJ,OAAA,CAACtD,UAAU;gBAAC2L,OAAO,EAAC,OAAO;gBAAAD,QAAA,gBAACpI,OAAA;kBAAAoI,QAAA,EAAQ;gBAAY;kBAAAS,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,KAAC,EAACrH,YAAY,CAACiL,yBAAyB,IAAI,KAAK;cAAA;gBAAA/D,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAa,CAAC,eACxHhJ,OAAA,CAACtD,UAAU;gBAAC2L,OAAO,EAAC,OAAO;gBAAAD,QAAA,gBAACpI,OAAA;kBAAAoI,QAAA,EAAQ;gBAAa;kBAAAS,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,KAAC,EAACrH,YAAY,CAACkL,mBAAmB,IAAI,KAAK;cAAA;gBAAAhE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAa,CAAC,eACnHhJ,OAAA,CAACtD,UAAU;gBAAC2L,OAAO,EAAC,OAAO;gBAAAD,QAAA,gBAACpI,OAAA;kBAAAoI,QAAA,EAAQ;gBAAQ;kBAAAS,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,KAAC,EAACrH,YAAY,CAACmL,cAAc,IAAI,KAAK;cAAA;gBAAAjE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAa,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACtG,CAAC,eAENhJ,OAAA,CAACtD,UAAU;cAAC2L,OAAO,EAAC,WAAW;cAACgE,YAAY;cAAAjE,QAAA,EAAC;YAAa;cAAAS,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAY,CAAC,eACvEhJ,OAAA,CAACvD,GAAG;cAACkM,EAAE,EAAE;gBAAE2D,EAAE,EAAE;cAAE,CAAE;cAAAlE,QAAA,gBACjBpI,OAAA,CAACtD,UAAU;gBAAC2L,OAAO,EAAC,OAAO;gBAAAD,QAAA,gBAACpI,OAAA;kBAAAoI,QAAA,EAAQ;gBAAc;kBAAAS,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,KAAC,EAACrH,YAAY,CAACwI,aAAa,IAAI,KAAK;cAAA;gBAAAtB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAa,CAAC,eAC9GhJ,OAAA,CAACtD,UAAU;gBAAC2L,OAAO,EAAC,OAAO;gBAAAD,QAAA,gBAACpI,OAAA;kBAAAoI,QAAA,EAAQ;gBAAgB;kBAAAS,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,KAAC,EAACrH,YAAY,CAAC4I,eAAe,IAAI,GAAG;cAAA;gBAAA1B,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAa,CAAC,eAChHhJ,OAAA,CAACtD,UAAU;gBAAC2L,OAAO,EAAC,OAAO;gBAAAD,QAAA,gBAACpI,OAAA;kBAAAoI,QAAA,EAAQ;gBAAM;kBAAAS,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,KAAC,EAAClJ,2BAA2B,CAAC6B,YAAY,CAACJ,mBAAmB,CAAC;cAAA;gBAAAsH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAa,CAAC,eAChIhJ,OAAA,CAACtD,UAAU;gBAAC2L,OAAO,EAAC,OAAO;gBAAAD,QAAA,gBAACpI,OAAA;kBAAAoI,QAAA,EAAQ;gBAAa;kBAAAS,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,KAAC,EAACrH,YAAY,CAAC0H,YAAY,IAAI,GAAG;cAAA;gBAAAR,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAa,CAAC,eAC1GhJ,OAAA,CAACtD,UAAU;gBAAC2L,OAAO,EAAC,OAAO;gBAAAD,QAAA,gBAACpI,OAAA;kBAAAoI,QAAA,EAAQ;gBAAO;kBAAAS,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,KAAC,EAACrH,YAAY,CAAC8I,SAAS,IAAI,KAAK;cAAA;gBAAA5B,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAa,CAAC,eACnGhJ,OAAA,CAACtD,UAAU;gBAAC2L,OAAO,EAAC,OAAO;gBAAAD,QAAA,gBAACpI,OAAA;kBAAAoI,QAAA,EAAQ;gBAAkB;kBAAAS,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,KAAC,EAACrH,YAAY,CAACoL,iBAAiB,IAAI,KAAK;cAAA;gBAAAlE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAa,CAAC,eACtHhJ,OAAA,CAACtD,UAAU;gBAAC2L,OAAO,EAAC,OAAO;gBAAAD,QAAA,gBAACpI,OAAA;kBAAAoI,QAAA,EAAQ;gBAAa;kBAAAS,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,KAAC,EAACrH,YAAY,CAACqL,YAAY,IAAI,KAAK;cAAA;gBAAAnE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAa,CAAC,eAC5GhJ,OAAA,CAACtD,UAAU;gBAAC2L,OAAO,EAAC,OAAO;gBAAAD,QAAA,gBAACpI,OAAA;kBAAAoI,QAAA,EAAQ;gBAAqB;kBAAAS,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,KAAC,EAAC,IAAIiE,IAAI,CAACtL,YAAY,CAACuL,SAAS,CAAC,CAACC,cAAc,CAAC,CAAC;cAAA;gBAAAtE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAa,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAChI,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACF,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACM,CAAC,eAChBhJ,OAAA,CAAC1B,aAAa;QAAA8J,QAAA,eACZpI,OAAA,CAACpD,MAAM;UAAC2L,OAAO,EAAEP,kBAAmB;UAAAI,QAAA,EAAC;QAAM;UAAAS,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACvC,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACV,CAAC;EAEb,CAAC;;EAED;EACA,MAAMoE,iBAAiB,GAAGA,CAAA,KAAM;IAC9B,oBACEpN,OAAA,CAACrD,KAAK;MAACgM,EAAE,EAAE;QAAE2D,EAAE,EAAE,CAAC;QAAEe,CAAC,EAAE,CAAC;QAAE7B,OAAO,EAAE9I,WAAW,GAAG,OAAO,GAAG;MAAO,CAAE;MAAA0F,QAAA,gBAClEpI,OAAA,CAACtD,UAAU;QAAC2L,OAAO,EAAC,IAAI;QAACgE,YAAY;QAAAjE,QAAA,EAAC;MAAoB;QAAAS,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAY,CAAC,eACvEhJ,OAAA,CAACnD,IAAI;QAACmO,SAAS;QAACC,OAAO,EAAE,CAAE;QAAA7C,QAAA,gBACzBpI,OAAA,CAACnD,IAAI;UAACsI,IAAI;UAAC+F,EAAE,EAAE,EAAG;UAACC,EAAE,EAAE,CAAE;UAACC,EAAE,EAAE,CAAE;UAAAhD,QAAA,eAC9BpI,OAAA,CAACnC,WAAW;YAACsO,SAAS;YAAC1C,IAAI,EAAC,OAAO;YAAArB,QAAA,gBACjCpI,OAAA,CAAClC,UAAU;cAACwP,EAAE,EAAC,2BAA2B;cAAAlF,QAAA,EAAC;YAAmB;cAAAS,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAY,CAAC,eAC3EhJ,OAAA,CAACpC,MAAM;cACL2P,OAAO,EAAC,2BAA2B;cACnCD,EAAE,EAAC,qBAAqB;cACxB7G,IAAI,EAAC,qBAAqB;cAC1BjE,KAAK,EAAEnB,OAAO,CAACE,mBAAoB;cACnCkB,KAAK,EAAC,qBAAqB;cAC3B+K,QAAQ,EAAE/F,kBAAmB;cAAAW,QAAA,gBAE7BpI,OAAA,CAACrC,QAAQ;gBAAC6E,KAAK,EAAC,EAAE;gBAAA4F,QAAA,EAAC;cAAK;gBAAAS,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAU,CAAC,EAClC7G,kBAAkB,CAACgC,GAAG,CAACiB,KAAK,iBAC3BpF,OAAA,CAACrC,QAAQ;gBAAa6E,KAAK,EAAE4C,KAAM;gBAAAgD,QAAA,EAAEhD;cAAK,GAA3BA,KAAK;gBAAAyD,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAiC,CACtD,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACI,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACE;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACV,CAAC,eAEPhJ,OAAA,CAACnD,IAAI;UAACsI,IAAI;UAAC+F,EAAE,EAAE,EAAG;UAACC,EAAE,EAAE,CAAE;UAACC,EAAE,EAAE,CAAE;UAAAhD,QAAA,eAC9BpI,OAAA,CAACnC,WAAW;YAACsO,SAAS;YAAC1C,IAAI,EAAC,OAAO;YAAArB,QAAA,gBACjCpI,OAAA,CAAClC,UAAU;cAACwP,EAAE,EAAC,iBAAiB;cAAAlF,QAAA,EAAC;YAAS;cAAAS,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAY,CAAC,eACvDhJ,OAAA,CAACpC,MAAM;cACL2P,OAAO,EAAC,iBAAiB;cACzBD,EAAE,EAAC,WAAW;cACd7G,IAAI,EAAC,WAAW;cAChBjE,KAAK,EAAEnB,OAAO,CAACG,SAAU;cACzBiB,KAAK,EAAC,WAAW;cACjB+K,QAAQ,EAAE/F,kBAAmB;cAAAW,QAAA,gBAE7BpI,OAAA,CAACrC,QAAQ;gBAAC6E,KAAK,EAAC,EAAE;gBAAA4F,QAAA,EAAC;cAAK;gBAAAS,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAU,CAAC,EAClC3G,aAAa,CAAC8B,GAAG,CAACoB,IAAI,iBACrBvF,OAAA,CAACrC,QAAQ;gBAAY6E,KAAK,EAAE+C,IAAK;gBAAA6C,QAAA,EAAE7C;cAAI,GAAxBA,IAAI;gBAAAsD,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAA+B,CACnD,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACI,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACE;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACV,CAAC,eAEPhJ,OAAA,CAACnD,IAAI;UAACsI,IAAI;UAAC+F,EAAE,EAAE,EAAG;UAACC,EAAE,EAAE,CAAE;UAACC,EAAE,EAAE,CAAE;UAAAhD,QAAA,eAC9BpI,OAAA,CAACnC,WAAW;YAACsO,SAAS;YAAC1C,IAAI,EAAC,OAAO;YAAArB,QAAA,gBACjCpI,OAAA,CAAClC,UAAU;cAACwP,EAAE,EAAC,eAAe;cAAAlF,QAAA,EAAC;YAAU;cAAAS,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAY,CAAC,eACtDhJ,OAAA,CAACpC,MAAM;cACL2P,OAAO,EAAC,eAAe;cACvBD,EAAE,EAAC,SAAS;cACZ7G,IAAI,EAAC,SAAS;cACdjE,KAAK,EAAEnB,OAAO,CAACI,OAAQ;cACvBgB,KAAK,EAAC,YAAY;cAClB+K,QAAQ,EAAE/F,kBAAmB;cAAAW,QAAA,gBAE7BpI,OAAA,CAACrC,QAAQ;gBAAC6E,KAAK,EAAC,EAAE;gBAAA4F,QAAA,EAAC;cAAW;gBAAAS,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAU,CAAC,EACxCzG,WAAW,CAAC4B,GAAG,CAACsJ,MAAM,iBACrBzN,OAAA,CAACrC,QAAQ;gBAAoB6E,KAAK,EAAEiL,MAAM,CAACjL,KAAM;gBAAA4F,QAAA,EAAEqF,MAAM,CAAChL;cAAK,GAAhDgL,MAAM,CAACjL,KAAK;gBAAAqG,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAA+C,CAC3E,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACI,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACE;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACV,CAAC,eAEPhJ,OAAA,CAACnD,IAAI;UAACsI,IAAI;UAAC+F,EAAE,EAAE,EAAG;UAACC,EAAE,EAAE,CAAE;UAACC,EAAE,EAAE,CAAE;UAAAhD,QAAA,eAC9BpI,OAAA,CAACnC,WAAW;YAACsO,SAAS;YAAC1C,IAAI,EAAC,OAAO;YAAArB,QAAA,gBACjCpI,OAAA,CAAClC,UAAU;cAACwP,EAAE,EAAC,kBAAkB;cAAAlF,QAAA,EAAC;YAAM;cAAAS,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAY,CAAC,eACrDhJ,OAAA,CAACpC,MAAM;cACL2P,OAAO,EAAC,kBAAkB;cAC1BD,EAAE,EAAC,YAAY;cACf7G,IAAI,EAAC,YAAY;cACjBjE,KAAK,EAAEnB,OAAO,CAACK,UAAW;cAC1Be,KAAK,EAAC,QAAQ;cACd+K,QAAQ,EAAE/F,kBAAmB;cAC7BiG,QAAQ,EAAE,CAACrM,OAAO,CAACI,OAAQ;cAAA2G,QAAA,gBAE3BpI,OAAA,CAACrC,QAAQ;gBAAC6E,KAAK,EAAC,KAAK;gBAAA4F,QAAA,EAAC;cAAS;gBAAAS,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAU,CAAC,eAC1ChJ,OAAA,CAACrC,QAAQ;gBAAC6E,KAAK,EAAC,MAAM;gBAAA4F,QAAA,EAAC;cAAW;gBAAAS,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAU,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACvC,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACE;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACV,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,eAEPhJ,OAAA,CAACvD,GAAG;QAACkM,EAAE,EAAE;UAAEY,EAAE,EAAE,CAAC;UAAEiC,OAAO,EAAE,MAAM;UAAEC,cAAc,EAAE;QAAW,CAAE;QAAArD,QAAA,eAC9DpI,OAAA,CAACpD,MAAM;UACLyL,OAAO,EAAC,UAAU;UAClBsF,SAAS,eAAE3N,OAAA,CAACN,SAAS;YAAAmJ,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAE;UACzBT,OAAO,EAAEV,YAAa;UACtBc,EAAE,EAAE;YAAEiF,EAAE,EAAE;UAAE,CAAE;UAAAxF,QAAA,EACf;QAED;UAAAS,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACN,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACD,CAAC;EAEZ,CAAC;;EAED;EACA,MAAM6E,gBAAgB,GAAGA,CAAA,KAAM;IAC7B,IAAI,CAAC9L,KAAK,EAAE,OAAO,IAAI;IAEvB,oBACE/B,OAAA,CAACrD,KAAK;MAACgM,EAAE,EAAE;QAAE2D,EAAE,EAAE,CAAC;QAAEe,CAAC,EAAE;MAAE,CAAE;MAAAjF,QAAA,gBACzBpI,OAAA,CAACtD,UAAU;QAAC2L,OAAO,EAAC,IAAI;QAACgE,YAAY;QAAAjE,QAAA,EAAC;MAAW;QAAAS,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAY,CAAC,EAC7D/G,YAAY,gBACXjC,OAAA,CAAC9B,cAAc;QAAA2K,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC,gBAElBhJ,OAAA,CAACnD,IAAI;QAACmO,SAAS;QAACC,OAAO,EAAE,CAAE;QAAA7C,QAAA,gBACzBpI,OAAA,CAACnD,IAAI;UAACsI,IAAI;UAAC+F,EAAE,EAAE,EAAG;UAACE,EAAE,EAAE,CAAE;UAAAhD,QAAA,gBACvBpI,OAAA,CAACtD,UAAU;YAAC2L,OAAO,EAAC,WAAW;YAACgE,YAAY;YAAAjE,QAAA,EAAC;UAAM;YAAAS,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAY,CAAC,eAChEhJ,OAAA,CAACtD,UAAU;YAAC2L,OAAO,EAAC,OAAO;YAAAD,QAAA,GAAC,eAAa,EAACrG,KAAK,CAAC+L,MAAM,CAACC,WAAW;UAAA;YAAAlF,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAa,CAAC,eAChFhJ,OAAA,CAACtD,UAAU;YAAC2L,OAAO,EAAC,OAAO;YAAAD,QAAA,GAAC,cAAY,EAACrG,KAAK,CAAC+L,MAAM,CAACE,UAAU;UAAA;YAAAnF,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAa,CAAC,eAC9EhJ,OAAA,CAACtD,UAAU;YAAC2L,OAAO,EAAC,OAAO;YAAAD,QAAA,GAAC,eAAa,EAACrG,KAAK,CAAC+L,MAAM,CAACG,WAAW;UAAA;YAAApF,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAa,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC5E,CAAC,eAEPhJ,OAAA,CAACnD,IAAI;UAACsI,IAAI;UAAC+F,EAAE,EAAE,EAAG;UAACE,EAAE,EAAE,CAAE;UAAAhD,QAAA,gBACvBpI,OAAA,CAACtD,UAAU;YAAC2L,OAAO,EAAC,WAAW;YAACgE,YAAY;YAAAjE,QAAA,EAAC;UAAS;YAAAS,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAY,CAAC,eACnEhJ,OAAA,CAACtD,UAAU;YAAC2L,OAAO,EAAC,OAAO;YAAAD,QAAA,GAAC,iBAAe,EAACrG,KAAK,CAACmM,SAAS,CAACC,oBAAoB,CAAC9D,OAAO,CAAC,CAAC,CAAC;UAAA;YAAAxB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAa,CAAC,eACzGhJ,OAAA,CAACtD,UAAU;YAAC2L,OAAO,EAAC,OAAO;YAAAD,QAAA,GAAC,gBAAc,EAACrG,KAAK,CAACmM,SAAS,CAACE,kBAAkB,CAAC/D,OAAO,CAAC,CAAC,CAAC;UAAA;YAAAxB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAa,CAAC,eACtGhJ,OAAA,CAACvD,GAAG;YAACkM,EAAE,EAAE;cAAE6C,OAAO,EAAE,MAAM;cAAE6C,UAAU,EAAE,QAAQ;cAAE9E,EAAE,EAAE;YAAE,CAAE;YAAAnB,QAAA,gBACxDpI,OAAA,CAACvD,GAAG;cAACkM,EAAE,EAAE;gBAAE2F,KAAK,EAAE,MAAM;gBAAEV,EAAE,EAAE;cAAE,CAAE;cAAAxF,QAAA,eAChCpI,OAAA,CAAC9B,cAAc;gBACbmK,OAAO,EAAC,aAAa;gBACrB7F,KAAK,EAAET,KAAK,CAACmM,SAAS,CAACK,yBAA0B;gBACjD5F,EAAE,EAAE;kBAAE6F,MAAM,EAAE,EAAE;kBAAEC,YAAY,EAAE;gBAAE;cAAE;gBAAA5F,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACrC;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACC,CAAC,eACNhJ,OAAA,CAACvD,GAAG;cAACkM,EAAE,EAAE;gBAAE+F,QAAQ,EAAE;cAAG,CAAE;cAAAtG,QAAA,eACxBpI,OAAA,CAACtD,UAAU;gBAAC2L,OAAO,EAAC,OAAO;gBAACC,KAAK,EAAC,gBAAgB;gBAAAF,QAAA,EAAE,GAAGrG,KAAK,CAACmM,SAAS,CAACK,yBAAyB,CAAClE,OAAO,CAAC,CAAC,CAAC;cAAG;gBAAAxB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAa;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACzH,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACF,CAAC,eAEPhJ,OAAA,CAACnD,IAAI;UAACsI,IAAI;UAAC+F,EAAE,EAAE,EAAG;UAACE,EAAE,EAAE,CAAE;UAAAhD,QAAA,gBACvBpI,OAAA,CAACtD,UAAU;YAAC2L,OAAO,EAAC,WAAW;YAACgE,YAAY;YAAAjE,QAAA,EAAC;UAAK;YAAAS,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAY,CAAC,eAC/DhJ,OAAA,CAACvD,GAAG;YAACkM,EAAE,EAAE;cAAE6C,OAAO,EAAE,MAAM;cAAEmD,QAAQ,EAAE,MAAM;cAAEC,GAAG,EAAE;YAAE,CAAE;YAAAxG,QAAA,EACpDrG,KAAK,CAACmD,KAAK,CAACf,GAAG,CAAC,CAACiB,KAAK,EAAEyJ,KAAK,kBAC5B7O,OAAA,CAACjC,IAAI;cAEH0E,KAAK,EAAE,GAAG2C,KAAK,CAACA,KAAK,KAAKA,KAAK,CAAC0J,KAAK,EAAG;cACxCrF,IAAI,EAAC,OAAO;cACZlB,OAAO,EAAEA,CAAA,KAAM;gBACbjH,UAAU,CAACsG,IAAI,KAAK;kBAClB,GAAGA,IAAI;kBACPrG,mBAAmB,EAAE6D,KAAK,CAACA,KAAK,KAAK,iBAAiB,GAAG,EAAE,GAAGA,KAAK,CAACA;gBACtE,CAAC,CAAC,CAAC;cACL;YAAE,GARGyJ,KAAK;cAAAhG,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OASX,CACF;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACC,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACF,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CACP;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACI,CAAC;EAEZ,CAAC;EAED,oBACEhJ,OAAA,CAACvD,GAAG;IAACsS,SAAS,EAAC,WAAW;IAAA3G,QAAA,gBACxBpI,OAAA,CAACrD,KAAK;MAACgM,EAAE,EAAE;QAAE2D,EAAE,EAAE,CAAC;QAAEe,CAAC,EAAE;MAAE,CAAE;MAAAjF,QAAA,eACzBpI,OAAA,CAACvD,GAAG;QAACkM,EAAE,EAAE;UAAE6C,OAAO,EAAE,MAAM;UAAEC,cAAc,EAAE,eAAe;UAAE4C,UAAU,EAAE;QAAS,CAAE;QAAAjG,QAAA,gBAClFpI,OAAA,CAACtD,UAAU;UAAC2L,OAAO,EAAC,IAAI;UAAAD,QAAA,GAAC,yBACA,EAAC3H,YAAY;QAAA;UAAAoI,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC1B,CAAC,eAEbhJ,OAAA,CAACvD,GAAG;UAACkM,EAAE,EAAE;YAAE6C,OAAO,EAAE,MAAM;YAAE6C,UAAU,EAAE,QAAQ;YAAEO,GAAG,EAAE;UAAE,CAAE;UAAAxG,QAAA,gBACzDpI,OAAA,CAAC/C,UAAU;YACTsL,OAAO,EAAEA,CAAA,KAAM5F,cAAc,CAAC,CAACD,WAAW,CAAE;YAC5CoI,KAAK,EAAEpI,WAAW,GAAG,iBAAiB,GAAG,eAAgB;YACzD4F,KAAK,EAAE5F,WAAW,GAAG,SAAS,GAAG,SAAU;YAAA0F,QAAA,eAE3CpI,OAAA,CAACd,cAAc;cAAA2J,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACR,CAAC,eAEbhJ,OAAA,CAAC/C,UAAU;YACTsL,OAAO,EAAEA,CAAA,KAAMC,MAAM,CAACC,QAAQ,CAACC,MAAM,CAAC,CAAE;YACxCoC,KAAK,EAAC,oBAAoB;YAAA1C,QAAA,eAE1BpI,OAAA,CAACtB,WAAW;cAAAmK,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACL,CAAC,eAEbhJ,OAAA,CAACvD,GAAG;YAACkM,EAAE,EAAE;cAAE6C,OAAO,EAAE,MAAM;cAAEwD,MAAM,EAAE,gBAAgB;cAAEP,YAAY,EAAE;YAAE,CAAE;YAAArG,QAAA,gBACtEpI,OAAA,CAAC/C,UAAU;cACT0L,EAAE,EAAE;gBAAEL,KAAK,EAAEnH,QAAQ,KAAK,OAAO,GAAG,SAAS,GAAG;cAAU,CAAE;cAC5DoH,OAAO,EAAEA,CAAA,KAAMsD,oBAAoB,CAAC,OAAO,CAAE;cAC7Cf,KAAK,EAAC,iBAAiB;cAAA1C,QAAA,eAEvBpI,OAAA,CAAClB,YAAY;gBAAA+J,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACN,CAAC,eACbhJ,OAAA,CAAC/C,UAAU;cACT0L,EAAE,EAAE;gBAAEL,KAAK,EAAEnH,QAAQ,KAAK,MAAM,GAAG,SAAS,GAAG;cAAU,CAAE;cAC3DoH,OAAO,EAAEA,CAAA,KAAMsD,oBAAoB,CAAC,MAAM,CAAE;cAC5Cf,KAAK,EAAC,gBAAgB;cAAA1C,QAAA,eAEtBpI,OAAA,CAAChB,cAAc;gBAAA6J,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACR,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACV,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACD,CAAC,EAGPoE,iBAAiB,CAAC,CAAC,EAGnBS,gBAAgB,CAAC,CAAC,EAElB9M,OAAO,gBACNf,OAAA,CAACvD,GAAG;MAACkM,EAAE,EAAE;QAAE6C,OAAO,EAAE,MAAM;QAAEyD,aAAa,EAAE,QAAQ;QAAEZ,UAAU,EAAE,QAAQ;QAAE9E,EAAE,EAAE;MAAE,CAAE;MAAAnB,QAAA,gBACjFpI,OAAA,CAAC/B,gBAAgB;QAACwL,IAAI,EAAE;MAAG;QAAAZ,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC,eAC9BhJ,OAAA,CAACtD,UAAU;QAACiM,EAAE,EAAE;UAAEY,EAAE,EAAE;QAAE,CAAE;QAAAnB,QAAA,EAAC;MAAmB;QAAAS,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAY,CAAC,eAC3DhJ,OAAA,CAACpD,MAAM;QACLyL,OAAO,EAAC,UAAU;QAClBC,KAAK,EAAC,SAAS;QACfC,OAAO,EAAEA,CAAA,KAAMC,MAAM,CAACC,QAAQ,CAACC,MAAM,CAAC,CAAE;QACxCC,EAAE,EAAE;UAAEY,EAAE,EAAE;QAAE,CAAE;QAAAnB,QAAA,EACf;MAED;QAAAS,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAQ,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACN,CAAC,GACJ/H,KAAK,gBACPjB,OAAA,CAACvD,GAAG;MAAA2L,QAAA,gBACFpI,OAAA,CAAChD,KAAK;QAACmL,QAAQ,EAAC,OAAO;QAACQ,EAAE,EAAE;UAAE2D,EAAE,EAAE;QAAE,CAAE;QAAAlE,QAAA,GACnCnH,KAAK,EACLA,KAAK,CAACsG,QAAQ,CAAC,eAAe,CAAC,iBAC9BvH,OAAA,CAACtD,UAAU;UAAC2L,OAAO,EAAC,OAAO;UAACM,EAAE,EAAE;YAAEY,EAAE,EAAE;UAAE,CAAE;UAAAnB,QAAA,gBACxCpI,OAAA;YAAAoI,QAAA,EAAQ;UAAa;YAAAS,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC,uEAC9B,eAAAhJ,OAAA;YAAA6I,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAK,CAAC,8CACoC,eAAAhJ,OAAA;YAAAoI,QAAA,EAAM;UAAa;YAAAS,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC,4CACtE;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAY,CACb;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACI,CAAC,eACRhJ,OAAA,CAACvD,GAAG;QAACkM,EAAE,EAAE;UAAE6C,OAAO,EAAE,MAAM;UAAEoD,GAAG,EAAE;QAAE,CAAE;QAAAxG,QAAA,eACnCpI,OAAA,CAACpD,MAAM;UACLyL,OAAO,EAAC,WAAW;UACnB0G,SAAS,EAAC,gBAAgB;UAC1BxG,OAAO,EAAEA,CAAA,KAAMC,MAAM,CAACC,QAAQ,CAACC,MAAM,CAAC,CAAE;UAAAN,QAAA,EACzC;QAED;UAAAS,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACN,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,gBAENhJ,OAAA,CAACvD,GAAG;MAAA2L,QAAA,GAED,CAAC/G,OAAO,CAACE,mBAAmB,IAAIF,OAAO,CAACG,SAAS,IAAIH,OAAO,CAACI,OAAO,kBACnEzB,OAAA,CAACvD,GAAG;QAACkM,EAAE,EAAE;UAAE2D,EAAE,EAAE,CAAC;UAAEd,OAAO,EAAE,MAAM;UAAEmD,QAAQ,EAAE,MAAM;UAAEC,GAAG,EAAE,CAAC;UAAEP,UAAU,EAAE;QAAS,CAAE;QAAAjG,QAAA,gBAClFpI,OAAA,CAACtD,UAAU;UAAC2L,OAAO,EAAC,OAAO;UAACM,EAAE,EAAE;YAAEiF,EAAE,EAAE;UAAE,CAAE;UAAAxF,QAAA,EAAC;QAAc;UAAAS,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAY,CAAC,EACrE3H,OAAO,CAACE,mBAAmB,iBAC1BvB,OAAA,CAACjC,IAAI;UACH0E,KAAK,EAAE,UAAUpB,OAAO,CAACE,mBAAmB,EAAG;UAC/CkI,IAAI,EAAC,OAAO;UACZyF,QAAQ,EAAEA,CAAA,KAAM5N,UAAU,CAACsG,IAAI,KAAK;YAAE,GAAGA,IAAI;YAAErG,mBAAmB,EAAE;UAAG,CAAC,CAAC;QAAE;UAAAsH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC5E,CACF,EACA3H,OAAO,CAACG,SAAS,iBAChBxB,OAAA,CAACjC,IAAI;UACH0E,KAAK,EAAE,cAAcpB,OAAO,CAACG,SAAS,EAAG;UACzCiI,IAAI,EAAC,OAAO;UACZyF,QAAQ,EAAEA,CAAA,KAAM5N,UAAU,CAACsG,IAAI,KAAK;YAAE,GAAGA,IAAI;YAAEpG,SAAS,EAAE;UAAG,CAAC,CAAC;QAAE;UAAAqH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAClE,CACF,EACA3H,OAAO,CAACI,OAAO,iBACdzB,OAAA,CAACjC,IAAI;UACH0E,KAAK,EAAE,gBAAgB,EAAAtC,iBAAA,GAAAoC,WAAW,CAAC4M,IAAI,CAACC,GAAG,IAAIA,GAAG,CAAC5M,KAAK,KAAKnB,OAAO,CAACI,OAAO,CAAC,cAAAtB,iBAAA,uBAAtDA,iBAAA,CAAwDsC,KAAK,KAAIpB,OAAO,CAACI,OAAO,KAAKJ,OAAO,CAACK,UAAU,KAAK,KAAK,GAAG,WAAW,GAAG,aAAa,GAAI;UAC1K+H,IAAI,EAAC,OAAO;UACZyF,QAAQ,EAAEA,CAAA,KAAM5N,UAAU,CAACsG,IAAI,KAAK;YAAE,GAAGA,IAAI;YAAEnG,OAAO,EAAE;UAAG,CAAC,CAAC;QAAE;UAAAoH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAChE,CACF,EACA,CAAC3H,OAAO,CAACE,mBAAmB,IAAIF,OAAO,CAACG,SAAS,IAAIH,OAAO,CAACI,OAAO,kBACnEzB,OAAA,CAACpD,MAAM;UACLyL,OAAO,EAAC,MAAM;UACdoB,IAAI,EAAC,OAAO;UACZkE,SAAS,eAAE3N,OAAA,CAACN,SAAS;YAAAmJ,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAE;UACzBT,OAAO,EAAEV,YAAa;UAAAO,QAAA,EACvB;QAED;UAAAS,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CACT;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACE,CACN,eAEDhJ,OAAA,CAACvD,GAAG;QAACkM,EAAE,EAAE;UAAE2D,EAAE,EAAE;QAAE,CAAE;QAAAlE,QAAA,gBACjBpI,OAAA,CAACtD,UAAU;UAAC2L,OAAO,EAAC,IAAI;UAACgE,YAAY;UAAAjE,QAAA,EAAC;QAEtC;UAAAS,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAY,CAAC,EACZf,eAAe,CAACtH,UAAU,CAAC;MAAA;QAAAkI,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACzB,CAAC,eAENhJ,OAAA,CAACvD,GAAG;QAACkM,EAAE,EAAE;UAAEY,EAAE,EAAE;QAAE,CAAE;QAAAnB,QAAA,gBACjBpI,OAAA,CAACtD,UAAU;UAAC2L,OAAO,EAAC,IAAI;UAACgE,YAAY;UAAAjE,QAAA,EAAC;QAEtC;UAAAS,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAY,CAAC,EACZf,eAAe,CAACpH,SAAS,CAAC;MAAA;QAAAgI,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACxB,CAAC,EAGL+C,mBAAmB,CAAC,CAAC;IAAA;MAAAlD,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACnB,CACN;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACE,CAAC;AAEV,CAAC;AAAC9I,EAAA,CAp7BID,kBAAkB;EAAA,QACYL,OAAO,EACxBD,WAAW;AAAA;AAAA0P,EAAA,GAFxBpP,kBAAkB;AAs7BxB,eAAeA,kBAAkB;AAAC,IAAAoP,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}