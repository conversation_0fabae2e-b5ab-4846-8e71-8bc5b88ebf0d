{"ast": null, "code": "import { toDate } from \"./toDate.mjs\";\n\n/**\n * @name closestIndexTo\n * @category Common Helpers\n * @summary Return an index of the closest date from the array comparing to the given date.\n *\n * @description\n * Return an index of the closest date from the array comparing to the given date.\n *\n * @typeParam DateType - The `Date` type, the function operates on. Gets inferred from passed arguments. Allows to use extensions like [`UTCDate`](https://github.com/date-fns/utc).\n *\n * @param dateToCompare - The date to compare with\n * @param dates - The array to search\n *\n * @returns An index of the date closest to the given date or undefined if no valid value is given\n *\n * @example\n * // Which date is closer to 6 September 2015?\n * const dateToCompare = new Date(2015, 8, 6)\n * const datesArray = [\n *   new Date(2015, 0, 1),\n *   new Date(2016, 0, 1),\n *   new Date(2017, 0, 1)\n * ]\n * const result = closestIndexTo(dateToCompare, datesArray)\n * //=> 1\n */\nexport function closestIndexTo(dateToCompare, dates) {\n  const date = toDate(dateToCompare);\n  if (isNaN(Number(date))) return NaN;\n  const timeToCompare = date.getTime();\n  let result;\n  let minDistance;\n  dates.forEach(function (dirtyDate, index) {\n    const currentDate = toDate(dirtyDate);\n    if (isNaN(Number(currentDate))) {\n      result = NaN;\n      minDistance = NaN;\n      return;\n    }\n    const distance = Math.abs(timeToCompare - currentDate.getTime());\n    if (result == null || distance < minDistance) {\n      result = index;\n      minDistance = distance;\n    }\n  });\n  return result;\n}\n\n// Fallback for modularized imports:\nexport default closestIndexTo;", "map": {"version": 3, "names": ["toDate", "closestIndexTo", "dateToCompare", "dates", "date", "isNaN", "Number", "NaN", "timeToCompare", "getTime", "result", "minDistance", "for<PERSON>ach", "dirtyDate", "index", "currentDate", "distance", "Math", "abs"], "sources": ["C:/CMS/webapp/frontend/node_modules/date-fns/closestIndexTo.mjs"], "sourcesContent": ["import { toDate } from \"./toDate.mjs\";\n\n/**\n * @name closestIndexTo\n * @category Common Helpers\n * @summary Return an index of the closest date from the array comparing to the given date.\n *\n * @description\n * Return an index of the closest date from the array comparing to the given date.\n *\n * @typeParam DateType - The `Date` type, the function operates on. Gets inferred from passed arguments. Allows to use extensions like [`UTCDate`](https://github.com/date-fns/utc).\n *\n * @param dateToCompare - The date to compare with\n * @param dates - The array to search\n *\n * @returns An index of the date closest to the given date or undefined if no valid value is given\n *\n * @example\n * // Which date is closer to 6 September 2015?\n * const dateToCompare = new Date(2015, 8, 6)\n * const datesArray = [\n *   new Date(2015, 0, 1),\n *   new Date(2016, 0, 1),\n *   new Date(2017, 0, 1)\n * ]\n * const result = closestIndexTo(dateToCompare, datesArray)\n * //=> 1\n */\nexport function closestIndexTo(dateToCompare, dates) {\n  const date = toDate(dateToCompare);\n\n  if (isNaN(Number(date))) return NaN;\n\n  const timeToCompare = date.getTime();\n\n  let result;\n  let minDistance;\n  dates.forEach(function (dirtyDate, index) {\n    const currentDate = toDate(dirtyDate);\n\n    if (isNaN(Number(currentDate))) {\n      result = NaN;\n      minDistance = NaN;\n      return;\n    }\n\n    const distance = Math.abs(timeToCompare - currentDate.getTime());\n    if (result == null || distance < minDistance) {\n      result = index;\n      minDistance = distance;\n    }\n  });\n\n  return result;\n}\n\n// Fallback for modularized imports:\nexport default closestIndexTo;\n"], "mappings": "AAAA,SAASA,MAAM,QAAQ,cAAc;;AAErC;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO,SAASC,cAAcA,CAACC,aAAa,EAAEC,KAAK,EAAE;EACnD,MAAMC,IAAI,GAAGJ,MAAM,CAACE,aAAa,CAAC;EAElC,IAAIG,KAAK,CAACC,MAAM,CAACF,IAAI,CAAC,CAAC,EAAE,OAAOG,GAAG;EAEnC,MAAMC,aAAa,GAAGJ,IAAI,CAACK,OAAO,CAAC,CAAC;EAEpC,IAAIC,MAAM;EACV,IAAIC,WAAW;EACfR,KAAK,CAACS,OAAO,CAAC,UAAUC,SAAS,EAAEC,KAAK,EAAE;IACxC,MAAMC,WAAW,GAAGf,MAAM,CAACa,SAAS,CAAC;IAErC,IAAIR,KAAK,CAACC,MAAM,CAACS,WAAW,CAAC,CAAC,EAAE;MAC9BL,MAAM,GAAGH,GAAG;MACZI,WAAW,GAAGJ,GAAG;MACjB;IACF;IAEA,MAAMS,QAAQ,GAAGC,IAAI,CAACC,GAAG,CAACV,aAAa,GAAGO,WAAW,CAACN,OAAO,CAAC,CAAC,CAAC;IAChE,IAAIC,MAAM,IAAI,IAAI,IAAIM,QAAQ,GAAGL,WAAW,EAAE;MAC5CD,MAAM,GAAGI,KAAK;MACdH,WAAW,GAAGK,QAAQ;IACxB;EACF,CAAC,CAAC;EAEF,OAAON,MAAM;AACf;;AAEA;AACA,eAAeT,cAAc", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}