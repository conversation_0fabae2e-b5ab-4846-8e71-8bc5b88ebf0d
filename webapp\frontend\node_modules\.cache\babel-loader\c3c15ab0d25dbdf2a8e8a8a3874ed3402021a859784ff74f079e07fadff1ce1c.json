{"ast": null, "code": "import { addQuarters } from \"./addQuarters.mjs\";\nimport { startOfQuarter } from \"./startOfQuarter.mjs\";\nimport { toDate } from \"./toDate.mjs\";\n\n/**\n * The {@link eachQuarterOfInterval} function options.\n */\n\n/**\n * @name eachQuarterOfInterval\n * @category Interval Helpers\n * @summary Return the array of quarters within the specified time interval.\n *\n * @description\n * Return the array of quarters within the specified time interval.\n *\n * @typeParam DateType - The `Date` type, the function operates on. Gets inferred from passed arguments. Allows to use extensions like [`UTCDate`](https://github.com/date-fns/utc).\n *\n * @param interval - The interval\n *\n * @returns The array with starts of quarters from the quarter of the interval start to the quarter of the interval end\n *\n * @example\n * // Each quarter within interval 6 February 2014 - 10 August 2014:\n * const result = eachQuarterOfInterval({\n *   start: new Date(2014, 1, 6),\n *   end: new Date(2014, 7, 10)\n * })\n * //=> [\n * //   Wed Jan 01 2014 00:00:00,\n * //   Tue Apr 01 2014 00:00:00,\n * //   Tue Jul 01 2014 00:00:00,\n * // ]\n */\nexport function eachQuarterOfInterval(interval, options) {\n  const startDate = toDate(interval.start);\n  const endDate = toDate(interval.end);\n  let reversed = +startDate > +endDate;\n  const endTime = reversed ? +startOfQuarter(startDate) : +startOfQuarter(endDate);\n  let currentDate = reversed ? startOfQuarter(endDate) : startOfQuarter(startDate);\n  let step = options?.step ?? 1;\n  if (!step) return [];\n  if (step < 0) {\n    step = -step;\n    reversed = !reversed;\n  }\n  const dates = [];\n  while (+currentDate <= endTime) {\n    dates.push(toDate(currentDate));\n    currentDate = addQuarters(currentDate, step);\n  }\n  return reversed ? dates.reverse() : dates;\n}\n\n// Fallback for modularized imports:\nexport default eachQuarterOfInterval;", "map": {"version": 3, "names": ["addQuarters", "startOfQuarter", "toDate", "eachQuarterOfInterval", "interval", "options", "startDate", "start", "endDate", "end", "reversed", "endTime", "currentDate", "step", "dates", "push", "reverse"], "sources": ["C:/CMS/webapp/frontend/node_modules/date-fns/eachQuarterOfInterval.mjs"], "sourcesContent": ["import { addQuarters } from \"./addQuarters.mjs\";\nimport { startOfQuarter } from \"./startOfQuarter.mjs\";\nimport { toDate } from \"./toDate.mjs\";\n\n/**\n * The {@link eachQuarterOfInterval} function options.\n */\n\n/**\n * @name eachQuarterOfInterval\n * @category Interval Helpers\n * @summary Return the array of quarters within the specified time interval.\n *\n * @description\n * Return the array of quarters within the specified time interval.\n *\n * @typeParam DateType - The `Date` type, the function operates on. Gets inferred from passed arguments. Allows to use extensions like [`UTCDate`](https://github.com/date-fns/utc).\n *\n * @param interval - The interval\n *\n * @returns The array with starts of quarters from the quarter of the interval start to the quarter of the interval end\n *\n * @example\n * // Each quarter within interval 6 February 2014 - 10 August 2014:\n * const result = eachQuarterOfInterval({\n *   start: new Date(2014, 1, 6),\n *   end: new Date(2014, 7, 10)\n * })\n * //=> [\n * //   Wed Jan 01 2014 00:00:00,\n * //   Tue Apr 01 2014 00:00:00,\n * //   Tue Jul 01 2014 00:00:00,\n * // ]\n */\nexport function eachQuarterOfInterval(interval, options) {\n  const startDate = toDate(interval.start);\n  const endDate = toDate(interval.end);\n\n  let reversed = +startDate > +endDate;\n  const endTime = reversed\n    ? +startOfQuarter(startDate)\n    : +startOfQuarter(endDate);\n  let currentDate = reversed\n    ? startOfQuarter(endDate)\n    : startOfQuarter(startDate);\n\n  let step = options?.step ?? 1;\n  if (!step) return [];\n  if (step < 0) {\n    step = -step;\n    reversed = !reversed;\n  }\n\n  const dates = [];\n\n  while (+currentDate <= endTime) {\n    dates.push(toDate(currentDate));\n    currentDate = addQuarters(currentDate, step);\n  }\n\n  return reversed ? dates.reverse() : dates;\n}\n\n// Fallback for modularized imports:\nexport default eachQuarterOfInterval;\n"], "mappings": "AAAA,SAASA,WAAW,QAAQ,mBAAmB;AAC/C,SAASC,cAAc,QAAQ,sBAAsB;AACrD,SAASC,MAAM,QAAQ,cAAc;;AAErC;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO,SAASC,qBAAqBA,CAACC,QAAQ,EAAEC,OAAO,EAAE;EACvD,MAAMC,SAAS,GAAGJ,MAAM,CAACE,QAAQ,CAACG,KAAK,CAAC;EACxC,MAAMC,OAAO,GAAGN,MAAM,CAACE,QAAQ,CAACK,GAAG,CAAC;EAEpC,IAAIC,QAAQ,GAAG,CAACJ,SAAS,GAAG,CAACE,OAAO;EACpC,MAAMG,OAAO,GAAGD,QAAQ,GACpB,CAACT,cAAc,CAACK,SAAS,CAAC,GAC1B,CAACL,cAAc,CAACO,OAAO,CAAC;EAC5B,IAAII,WAAW,GAAGF,QAAQ,GACtBT,cAAc,CAACO,OAAO,CAAC,GACvBP,cAAc,CAACK,SAAS,CAAC;EAE7B,IAAIO,IAAI,GAAGR,OAAO,EAAEQ,IAAI,IAAI,CAAC;EAC7B,IAAI,CAACA,IAAI,EAAE,OAAO,EAAE;EACpB,IAAIA,IAAI,GAAG,CAAC,EAAE;IACZA,IAAI,GAAG,CAACA,IAAI;IACZH,QAAQ,GAAG,CAACA,QAAQ;EACtB;EAEA,MAAMI,KAAK,GAAG,EAAE;EAEhB,OAAO,CAACF,WAAW,IAAID,OAAO,EAAE;IAC9BG,KAAK,CAACC,IAAI,CAACb,MAAM,CAACU,WAAW,CAAC,CAAC;IAC/BA,WAAW,GAAGZ,WAAW,CAACY,WAAW,EAAEC,IAAI,CAAC;EAC9C;EAEA,OAAOH,QAAQ,GAAGI,KAAK,CAACE,OAAO,CAAC,CAAC,GAAGF,KAAK;AAC3C;;AAEA;AACA,eAAeX,qBAAqB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}