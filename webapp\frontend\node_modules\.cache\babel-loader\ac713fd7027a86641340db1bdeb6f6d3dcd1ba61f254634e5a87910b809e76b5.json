{"ast": null, "code": "import axios from 'axios';\nconst API_URL = 'http://localhost:8000/api';\n\n// Crea un'istanza di axios con configurazione personalizzata\nconst axiosInstance = axios.create({\n  baseURL: API_URL,\n  headers: {\n    'Content-Type': 'application/json'\n  }\n});\n\n// Configura axios per includere il token in tutte le richieste\naxiosInstance.interceptors.request.use(config => {\n  const token = localStorage.getItem('token');\n  if (token) {\n    config.headers.Authorization = `Bearer ${token}`;\n  }\n  return config;\n}, error => {\n  return Promise.reject(error);\n});\nconst caviService = {\n  // Ottiene la lista dei cavi di un cantiere\n  getCavi: async (cantiereId, tipoCavo = null) => {\n    try {\n      let url = `/cavi/${cantiereId}`;\n      if (tipoCavo !== null) {\n        url += `?tipo_cavo=${tipoCavo}`;\n      }\n      const response = await axiosInstance.get(url);\n      return response.data;\n    } catch (error) {\n      console.error('Get cavi error:', error);\n      throw error.response ? error.response.data : error;\n    }\n  },\n  // Crea un nuovo cavo\n  createCavo: async (cantiereId, cavoData) => {\n    try {\n      const response = await axiosInstance.post(`/cavi/${cantiereId}`, cavoData);\n      return response.data;\n    } catch (error) {\n      console.error('Create cavo error:', error);\n      throw error.response ? error.response.data : error;\n    }\n  },\n  // Aggiorna un cavo esistente\n  updateCavo: async (cantiereId, cavoId, cavoData) => {\n    try {\n      const response = await axiosInstance.put(`/cavi/${cantiereId}/${cavoId}`, cavoData);\n      return response.data;\n    } catch (error) {\n      console.error('Update cavo error:', error);\n      throw error.response ? error.response.data : error;\n    }\n  },\n  // Elimina un cavo\n  deleteCavo: async (cantiereId, cavoId) => {\n    try {\n      const response = await axiosInstance.delete(`/cavi/${cantiereId}/${cavoId}`);\n      return response.data;\n    } catch (error) {\n      console.error('Delete cavo error:', error);\n      throw error.response ? error.response.data : error;\n    }\n  },\n  // Aggiorna i metri posati di un cavo\n  updateMetriPosati: async (cantiereId, cavoId, metriPosati) => {\n    try {\n      const response = await axiosInstance.post(`/cavi/${cantiereId}/${cavoId}/metri-posati`, {\n        metri_posati: metriPosati\n      });\n      return response.data;\n    } catch (error) {\n      console.error('Update metri posati error:', error);\n      throw error.response ? error.response.data : error;\n    }\n  },\n  // Modifica la bobina di un cavo posato\n  updateBobina: async (cantiereId, cavoId, idBobina) => {\n    try {\n      const response = await axiosInstance.post(`/cavi/${cantiereId}/${cavoId}/bobina`, {\n        id_bobina: idBobina\n      });\n      return response.data;\n    } catch (error) {\n      console.error('Update bobina error:', error);\n      throw error.response ? error.response.data : error;\n    }\n  }\n};\nexport default caviService;", "map": {"version": 3, "names": ["axios", "API_URL", "axiosInstance", "create", "baseURL", "headers", "interceptors", "request", "use", "config", "token", "localStorage", "getItem", "Authorization", "error", "Promise", "reject", "caviService", "get<PERSON><PERSON>", "cantiereId", "tipoCavo", "url", "response", "get", "data", "console", "createCavo", "cavoData", "post", "updateCavo", "cavoId", "put", "deleteCavo", "delete", "updateMetri<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "metri_posati", "updateBobina", "idBobina", "id_bobina"], "sources": ["C:/Users/<USER>/PycharmProjects/CMS/webapp/frontend/src/services/caviService.js"], "sourcesContent": ["import axios from 'axios';\n\nconst API_URL = 'http://localhost:8000/api';\n\n// Crea un'istanza di axios con configurazione personalizzata\nconst axiosInstance = axios.create({\n  baseURL: API_URL,\n  headers: {\n    'Content-Type': 'application/json'\n  }\n});\n\n// Configura axios per includere il token in tutte le richieste\naxiosInstance.interceptors.request.use(\n  (config) => {\n    const token = localStorage.getItem('token');\n    if (token) {\n      config.headers.Authorization = `Bearer ${token}`;\n    }\n    return config;\n  },\n  (error) => {\n    return Promise.reject(error);\n  }\n);\n\nconst caviService = {\n  // Ottiene la lista dei cavi di un cantiere\n  getCavi: async (cantiereId, tipoCavo = null) => {\n    try {\n      let url = `/cavi/${cantiereId}`;\n      if (tipoCavo !== null) {\n        url += `?tipo_cavo=${tipoCavo}`;\n      }\n      const response = await axiosInstance.get(url);\n      return response.data;\n    } catch (error) {\n      console.error('Get cavi error:', error);\n      throw error.response ? error.response.data : error;\n    }\n  },\n\n  // Crea un nuovo cavo\n  createCavo: async (cantiereId, cavoData) => {\n    try {\n      const response = await axiosInstance.post(`/cavi/${cantiereId}`, cavoData);\n      return response.data;\n    } catch (error) {\n      console.error('Create cavo error:', error);\n      throw error.response ? error.response.data : error;\n    }\n  },\n\n  // Aggiorna un cavo esistente\n  updateCavo: async (cantiereId, cavoId, cavoData) => {\n    try {\n      const response = await axiosInstance.put(`/cavi/${cantiereId}/${cavoId}`, cavoData);\n      return response.data;\n    } catch (error) {\n      console.error('Update cavo error:', error);\n      throw error.response ? error.response.data : error;\n    }\n  },\n\n  // Elimina un cavo\n  deleteCavo: async (cantiereId, cavoId) => {\n    try {\n      const response = await axiosInstance.delete(`/cavi/${cantiereId}/${cavoId}`);\n      return response.data;\n    } catch (error) {\n      console.error('Delete cavo error:', error);\n      throw error.response ? error.response.data : error;\n    }\n  },\n\n  // Aggiorna i metri posati di un cavo\n  updateMetriPosati: async (cantiereId, cavoId, metriPosati) => {\n    try {\n      const response = await axiosInstance.post(`/cavi/${cantiereId}/${cavoId}/metri-posati`, {\n        metri_posati: metriPosati\n      });\n      return response.data;\n    } catch (error) {\n      console.error('Update metri posati error:', error);\n      throw error.response ? error.response.data : error;\n    }\n  },\n\n  // Modifica la bobina di un cavo posato\n  updateBobina: async (cantiereId, cavoId, idBobina) => {\n    try {\n      const response = await axiosInstance.post(`/cavi/${cantiereId}/${cavoId}/bobina`, {\n        id_bobina: idBobina\n      });\n      return response.data;\n    } catch (error) {\n      console.error('Update bobina error:', error);\n      throw error.response ? error.response.data : error;\n    }\n  }\n};\n\nexport default caviService;\n"], "mappings": "AAAA,OAAOA,KAAK,MAAM,OAAO;AAEzB,MAAMC,OAAO,GAAG,2BAA2B;;AAE3C;AACA,MAAMC,aAAa,GAAGF,KAAK,CAACG,MAAM,CAAC;EACjCC,OAAO,EAAEH,OAAO;EAChBI,OAAO,EAAE;IACP,cAAc,EAAE;EAClB;AACF,CAAC,CAAC;;AAEF;AACAH,aAAa,CAACI,YAAY,CAACC,OAAO,CAACC,GAAG,CACnCC,MAAM,IAAK;EACV,MAAMC,KAAK,GAAGC,YAAY,CAACC,OAAO,CAAC,OAAO,CAAC;EAC3C,IAAIF,KAAK,EAAE;IACTD,MAAM,CAACJ,OAAO,CAACQ,aAAa,GAAG,UAAUH,KAAK,EAAE;EAClD;EACA,OAAOD,MAAM;AACf,CAAC,EACAK,KAAK,IAAK;EACT,OAAOC,OAAO,CAACC,MAAM,CAACF,KAAK,CAAC;AAC9B,CACF,CAAC;AAED,MAAMG,WAAW,GAAG;EAClB;EACAC,OAAO,EAAE,MAAAA,CAAOC,UAAU,EAAEC,QAAQ,GAAG,IAAI,KAAK;IAC9C,IAAI;MACF,IAAIC,GAAG,GAAG,SAASF,UAAU,EAAE;MAC/B,IAAIC,QAAQ,KAAK,IAAI,EAAE;QACrBC,GAAG,IAAI,cAAcD,QAAQ,EAAE;MACjC;MACA,MAAME,QAAQ,GAAG,MAAMpB,aAAa,CAACqB,GAAG,CAACF,GAAG,CAAC;MAC7C,OAAOC,QAAQ,CAACE,IAAI;IACtB,CAAC,CAAC,OAAOV,KAAK,EAAE;MACdW,OAAO,CAACX,KAAK,CAAC,iBAAiB,EAAEA,KAAK,CAAC;MACvC,MAAMA,KAAK,CAACQ,QAAQ,GAAGR,KAAK,CAACQ,QAAQ,CAACE,IAAI,GAAGV,KAAK;IACpD;EACF,CAAC;EAED;EACAY,UAAU,EAAE,MAAAA,CAAOP,UAAU,EAAEQ,QAAQ,KAAK;IAC1C,IAAI;MACF,MAAML,QAAQ,GAAG,MAAMpB,aAAa,CAAC0B,IAAI,CAAC,SAAST,UAAU,EAAE,EAAEQ,QAAQ,CAAC;MAC1E,OAAOL,QAAQ,CAACE,IAAI;IACtB,CAAC,CAAC,OAAOV,KAAK,EAAE;MACdW,OAAO,CAACX,KAAK,CAAC,oBAAoB,EAAEA,KAAK,CAAC;MAC1C,MAAMA,KAAK,CAACQ,QAAQ,GAAGR,KAAK,CAACQ,QAAQ,CAACE,IAAI,GAAGV,KAAK;IACpD;EACF,CAAC;EAED;EACAe,UAAU,EAAE,MAAAA,CAAOV,UAAU,EAAEW,MAAM,EAAEH,QAAQ,KAAK;IAClD,IAAI;MACF,MAAML,QAAQ,GAAG,MAAMpB,aAAa,CAAC6B,GAAG,CAAC,SAASZ,UAAU,IAAIW,MAAM,EAAE,EAAEH,QAAQ,CAAC;MACnF,OAAOL,QAAQ,CAACE,IAAI;IACtB,CAAC,CAAC,OAAOV,KAAK,EAAE;MACdW,OAAO,CAACX,KAAK,CAAC,oBAAoB,EAAEA,KAAK,CAAC;MAC1C,MAAMA,KAAK,CAACQ,QAAQ,GAAGR,KAAK,CAACQ,QAAQ,CAACE,IAAI,GAAGV,KAAK;IACpD;EACF,CAAC;EAED;EACAkB,UAAU,EAAE,MAAAA,CAAOb,UAAU,EAAEW,MAAM,KAAK;IACxC,IAAI;MACF,MAAMR,QAAQ,GAAG,MAAMpB,aAAa,CAAC+B,MAAM,CAAC,SAASd,UAAU,IAAIW,MAAM,EAAE,CAAC;MAC5E,OAAOR,QAAQ,CAACE,IAAI;IACtB,CAAC,CAAC,OAAOV,KAAK,EAAE;MACdW,OAAO,CAACX,KAAK,CAAC,oBAAoB,EAAEA,KAAK,CAAC;MAC1C,MAAMA,KAAK,CAACQ,QAAQ,GAAGR,KAAK,CAACQ,QAAQ,CAACE,IAAI,GAAGV,KAAK;IACpD;EACF,CAAC;EAED;EACAoB,iBAAiB,EAAE,MAAAA,CAAOf,UAAU,EAAEW,MAAM,EAAEK,WAAW,KAAK;IAC5D,IAAI;MACF,MAAMb,QAAQ,GAAG,MAAMpB,aAAa,CAAC0B,IAAI,CAAC,SAAST,UAAU,IAAIW,MAAM,eAAe,EAAE;QACtFM,YAAY,EAAED;MAChB,CAAC,CAAC;MACF,OAAOb,QAAQ,CAACE,IAAI;IACtB,CAAC,CAAC,OAAOV,KAAK,EAAE;MACdW,OAAO,CAACX,KAAK,CAAC,4BAA4B,EAAEA,KAAK,CAAC;MAClD,MAAMA,KAAK,CAACQ,QAAQ,GAAGR,KAAK,CAACQ,QAAQ,CAACE,IAAI,GAAGV,KAAK;IACpD;EACF,CAAC;EAED;EACAuB,YAAY,EAAE,MAAAA,CAAOlB,UAAU,EAAEW,MAAM,EAAEQ,QAAQ,KAAK;IACpD,IAAI;MACF,MAAMhB,QAAQ,GAAG,MAAMpB,aAAa,CAAC0B,IAAI,CAAC,SAAST,UAAU,IAAIW,MAAM,SAAS,EAAE;QAChFS,SAAS,EAAED;MACb,CAAC,CAAC;MACF,OAAOhB,QAAQ,CAACE,IAAI;IACtB,CAAC,CAAC,OAAOV,KAAK,EAAE;MACdW,OAAO,CAACX,KAAK,CAAC,sBAAsB,EAAEA,KAAK,CAAC;MAC5C,MAAMA,KAAK,CAACQ,QAAQ,GAAGR,KAAK,CAACQ,QAAQ,CAACE,IAAI,GAAGV,KAAK;IACpD;EACF;AACF,CAAC;AAED,eAAeG,WAAW", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}