{"ast": null, "code": "import { formatDistance } from \"./gl/_lib/formatDistance.js\";\nimport { formatLong } from \"./gl/_lib/formatLong.js\";\nimport { formatRelative } from \"./gl/_lib/formatRelative.js\";\nimport { localize } from \"./gl/_lib/localize.js\";\nimport { match } from \"./gl/_lib/match.js\";\n\n/**\n * @category Locales\n * @summary Galician locale.\n * @language Galician\n * @iso-639-2 glg\n * <AUTHOR>\n * <AUTHOR> Pita [@fidelpita](https://github.com/fidelpita)\n */\nexport const gl = {\n  code: \"gl\",\n  formatDistance: formatDistance,\n  formatLong: formatLong,\n  formatRelative: formatRelative,\n  localize: localize,\n  match: match,\n  options: {\n    weekStartsOn: 1 /* Monday */,\n    firstWeekContainsDate: 1\n  }\n};\n\n// Fallback for modularized imports:\nexport default gl;", "map": {"version": 3, "names": ["formatDistance", "formatLong", "formatRelative", "localize", "match", "gl", "code", "options", "weekStartsOn", "firstWeekContainsDate"], "sources": ["C:/Users/<USER>/PycharmProjects/CMS/webapp/frontend/node_modules/date-fns/locale/gl.js"], "sourcesContent": ["import { formatDistance } from \"./gl/_lib/formatDistance.js\";\nimport { formatLong } from \"./gl/_lib/formatLong.js\";\nimport { formatRelative } from \"./gl/_lib/formatRelative.js\";\nimport { localize } from \"./gl/_lib/localize.js\";\nimport { match } from \"./gl/_lib/match.js\";\n\n/**\n * @category Locales\n * @summary Galician locale.\n * @language Galician\n * @iso-639-2 glg\n * <AUTHOR>\n * <AUTHOR> Pita [@fidelpita](https://github.com/fidelpita)\n */\nexport const gl = {\n  code: \"gl\",\n  formatDistance: formatDistance,\n  formatLong: formatLong,\n  formatRelative: formatRelative,\n  localize: localize,\n  match: match,\n  options: {\n    weekStartsOn: 1 /* Monday */,\n    firstWeekContainsDate: 1,\n  },\n};\n\n// Fallback for modularized imports:\nexport default gl;\n"], "mappings": "AAAA,SAASA,cAAc,QAAQ,6BAA6B;AAC5D,SAASC,UAAU,QAAQ,yBAAyB;AACpD,SAASC,cAAc,QAAQ,6BAA6B;AAC5D,SAASC,QAAQ,QAAQ,uBAAuB;AAChD,SAASC,KAAK,QAAQ,oBAAoB;;AAE1C;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO,MAAMC,EAAE,GAAG;EAChBC,IAAI,EAAE,IAAI;EACVN,cAAc,EAAEA,cAAc;EAC9BC,UAAU,EAAEA,UAAU;EACtBC,cAAc,EAAEA,cAAc;EAC9BC,QAAQ,EAAEA,QAAQ;EAClBC,KAAK,EAAEA,KAAK;EACZG,OAAO,EAAE;IACPC,YAAY,EAAE,CAAC,CAAC;IAChBC,qBAAqB,EAAE;EACzB;AACF,CAAC;;AAED;AACA,eAAeJ,EAAE", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}