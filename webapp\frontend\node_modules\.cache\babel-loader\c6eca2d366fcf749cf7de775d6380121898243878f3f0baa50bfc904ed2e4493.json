{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\PycharmProjects\\\\CMS\\\\webapp\\\\frontend\\\\src\\\\context\\\\AuthContext.js\",\n  _s = $RefreshSig$(),\n  _s2 = $RefreshSig$();\nimport React, { createContext, useState, useContext, useEffect } from 'react';\nimport { useNavigate } from 'react-router-dom';\nimport authService from '../services/authService';\n\n// Crea il contesto di autenticazione\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst AuthContext = /*#__PURE__*/createContext(null);\n\n// Hook personalizzato per utilizzare il contesto di autenticazione\nexport const useAuth = () => {\n  _s();\n  return useContext(AuthContext);\n};\n_s(useAuth, \"gDsCjeeItUuvgOWf1v4qoK9RF6k=\");\nexport const AuthProvider = ({\n  children\n}) => {\n  _s2();\n  const [user, setUser] = useState(null);\n  const [isAuthenticated, setIsAuthenticated] = useState(false);\n  const [loading, setLoading] = useState(true);\n  const navigate = useNavigate();\n\n  // Verifica se l'utente è già autenticato all'avvio dell'applicazione\n  useEffect(() => {\n    const checkAuth = async () => {\n      try {\n        console.log('Verificando autenticazione all\\'avvio...');\n        // Prima di tutto, imposta loading a true\n        setLoading(true);\n\n        // Pulisci eventuali token non validi o scaduti\n        const token = localStorage.getItem('token');\n        console.log('Token trovato nel localStorage:', token ? 'Sì' : 'No');\n        if (token) {\n          try {\n            // Verifica la validità del token\n            console.log('Tentativo di verifica token...');\n            const userData = await authService.checkToken();\n            console.log('Token valido, dati utente:', userData);\n            setUser(userData);\n            setIsAuthenticated(true);\n          } catch (tokenError) {\n            console.error('Errore durante la verifica del token:', tokenError);\n            // Se il token non è valido, rimuovilo\n            console.log('Rimozione token non valido dal localStorage');\n            localStorage.removeItem('token');\n            setUser(null);\n            setIsAuthenticated(false);\n          }\n        } else {\n          console.log('Nessun token trovato, utente non autenticato');\n          setUser(null);\n          setIsAuthenticated(false);\n        }\n      } catch (error) {\n        console.error('Errore generale durante la verifica dell\\'autenticazione:', error);\n        // In caso di errore generale, assicurati che l'utente non sia autenticato\n        localStorage.removeItem('token');\n        setUser(null);\n        setIsAuthenticated(false);\n      } finally {\n        // Assicurati che loading sia impostato a false alla fine\n        console.log('Completata verifica autenticazione, loading:', false);\n        setLoading(false);\n      }\n    };\n\n    // Esegui la verifica dell'autenticazione\n    checkAuth();\n  }, []);\n\n  // Funzione di login\n  const login = async (credentials, loginType) => {\n    try {\n      console.log(`Tentativo di login come ${loginType}`, credentials);\n      const response = await authService.login(credentials, loginType);\n      console.log('Risposta login ricevuta:', response);\n      const {\n        access_token,\n        user_id,\n        username,\n        role\n      } = response;\n\n      // Salva il token nel localStorage\n      console.log('Salvataggio token nel localStorage');\n      localStorage.setItem('token', access_token);\n\n      // Imposta i dati dell'utente\n      const userData = {\n        id: user_id,\n        username,\n        role\n      };\n      console.log('Impostazione dati utente:', userData);\n      setUser(userData);\n      setIsAuthenticated(true);\n      return userData;\n    } catch (error) {\n      console.error('Errore durante il login:', error);\n      throw error;\n    }\n  };\n\n  // Funzione di logout\n  const logout = () => {\n    localStorage.removeItem('token');\n    setUser(null);\n    setIsAuthenticated(false);\n    navigate('/login');\n  };\n\n  // Funzione per impostare il token\n  const setToken = token => {\n    localStorage.setItem('token', token);\n  };\n\n  // Funzione per impersonare un utente\n  const impersonateUser = async userId => {\n    try {\n      const response = await authService.impersonateUser(userId);\n\n      // Salva il token nel localStorage\n      localStorage.setItem('token', response.access_token);\n\n      // Imposta i dati dell'utente\n      const userData = {\n        id: response.user_id,\n        username: response.username,\n        role: response.role\n      };\n      setUser(userData);\n      setIsAuthenticated(true);\n      return userData;\n    } catch (error) {\n      console.error('Errore durante l\\'impersonificazione:', error);\n      throw error;\n    }\n  };\n\n  // Valore del contesto\n  const value = {\n    user,\n    setUser,\n    isAuthenticated,\n    loading,\n    login,\n    logout,\n    setToken,\n    impersonateUser\n  };\n  return /*#__PURE__*/_jsxDEV(AuthContext.Provider, {\n    value: value,\n    children: children\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 142,\n    columnNumber: 10\n  }, this);\n};\n_s2(AuthProvider, \"DZ43dVOC6ATcOj/spkf7xa39n3k=\", false, function () {\n  return [useNavigate];\n});\n_c = AuthProvider;\nvar _c;\n$RefreshReg$(_c, \"AuthProvider\");", "map": {"version": 3, "names": ["React", "createContext", "useState", "useContext", "useEffect", "useNavigate", "authService", "jsxDEV", "_jsxDEV", "AuthContext", "useAuth", "_s", "<PERSON>th<PERSON><PERSON><PERSON>", "children", "_s2", "user", "setUser", "isAuthenticated", "setIsAuthenticated", "loading", "setLoading", "navigate", "checkAuth", "console", "log", "token", "localStorage", "getItem", "userData", "checkToken", "tokenError", "error", "removeItem", "login", "credentials", "loginType", "response", "access_token", "user_id", "username", "role", "setItem", "id", "logout", "setToken", "impersonate<PERSON><PERSON>", "userId", "value", "Provider", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/PycharmProjects/CMS/webapp/frontend/src/context/AuthContext.js"], "sourcesContent": ["import React, { createContext, useState, useContext, useEffect } from 'react';\nimport { useNavigate } from 'react-router-dom';\nimport authService from '../services/authService';\n\n// Crea il contesto di autenticazione\nconst AuthContext = createContext(null);\n\n// Hook personalizzato per utilizzare il contesto di autenticazione\nexport const useAuth = () => useContext(AuthContext);\n\nexport const AuthProvider = ({ children }) => {\n  const [user, setUser] = useState(null);\n  const [isAuthenticated, setIsAuthenticated] = useState(false);\n  const [loading, setLoading] = useState(true);\n  const navigate = useNavigate();\n\n  // Verifica se l'utente è già autenticato all'avvio dell'applicazione\n  useEffect(() => {\n    const checkAuth = async () => {\n      try {\n        console.log('Verificando autenticazione all\\'avvio...');\n        // Prima di tutto, imposta loading a true\n        setLoading(true);\n\n        // Pulisci eventuali token non validi o scaduti\n        const token = localStorage.getItem('token');\n        console.log('Token trovato nel localStorage:', token ? 'Sì' : 'No');\n\n        if (token) {\n          try {\n            // Verifica la validità del token\n            console.log('Tentativo di verifica token...');\n            const userData = await authService.checkToken();\n            console.log('Token valido, dati utente:', userData);\n            setUser(userData);\n            setIsAuthenticated(true);\n          } catch (tokenError) {\n            console.error('Errore durante la verifica del token:', tokenError);\n            // Se il token non è valido, rimuovilo\n            console.log('Rimozione token non valido dal localStorage');\n            localStorage.removeItem('token');\n            setUser(null);\n            setIsAuthenticated(false);\n          }\n        } else {\n          console.log('Nessun token trovato, utente non autenticato');\n          setUser(null);\n          setIsAuthenticated(false);\n        }\n      } catch (error) {\n        console.error('Errore generale durante la verifica dell\\'autenticazione:', error);\n        // In caso di errore generale, assicurati che l'utente non sia autenticato\n        localStorage.removeItem('token');\n        setUser(null);\n        setIsAuthenticated(false);\n      } finally {\n        // Assicurati che loading sia impostato a false alla fine\n        console.log('Completata verifica autenticazione, loading:', false);\n        setLoading(false);\n      }\n    };\n\n    // Esegui la verifica dell'autenticazione\n    checkAuth();\n  }, []);\n\n  // Funzione di login\n  const login = async (credentials, loginType) => {\n    try {\n      console.log(`Tentativo di login come ${loginType}`, credentials);\n      const response = await authService.login(credentials, loginType);\n      console.log('Risposta login ricevuta:', response);\n\n      const { access_token, user_id, username, role } = response;\n\n      // Salva il token nel localStorage\n      console.log('Salvataggio token nel localStorage');\n      localStorage.setItem('token', access_token);\n\n      // Imposta i dati dell'utente\n      const userData = { id: user_id, username, role };\n      console.log('Impostazione dati utente:', userData);\n      setUser(userData);\n      setIsAuthenticated(true);\n\n      return userData;\n    } catch (error) {\n      console.error('Errore durante il login:', error);\n      throw error;\n    }\n  };\n\n  // Funzione di logout\n  const logout = () => {\n    localStorage.removeItem('token');\n    setUser(null);\n    setIsAuthenticated(false);\n    navigate('/login');\n  };\n\n  // Funzione per impostare il token\n  const setToken = (token) => {\n    localStorage.setItem('token', token);\n  };\n\n  // Funzione per impersonare un utente\n  const impersonateUser = async (userId) => {\n    try {\n      const response = await authService.impersonateUser(userId);\n\n      // Salva il token nel localStorage\n      localStorage.setItem('token', response.access_token);\n\n      // Imposta i dati dell'utente\n      const userData = {\n        id: response.user_id,\n        username: response.username,\n        role: response.role\n      };\n      setUser(userData);\n      setIsAuthenticated(true);\n\n      return userData;\n    } catch (error) {\n      console.error('Errore durante l\\'impersonificazione:', error);\n      throw error;\n    }\n  };\n\n  // Valore del contesto\n  const value = {\n    user,\n    setUser,\n    isAuthenticated,\n    loading,\n    login,\n    logout,\n    setToken,\n    impersonateUser\n  };\n\n  return <AuthContext.Provider value={value}>{children}</AuthContext.Provider>;\n};\n"], "mappings": ";;;AAAA,OAAOA,KAAK,IAAIC,aAAa,EAAEC,QAAQ,EAAEC,UAAU,EAAEC,SAAS,QAAQ,OAAO;AAC7E,SAASC,WAAW,QAAQ,kBAAkB;AAC9C,OAAOC,WAAW,MAAM,yBAAyB;;AAEjD;AAAA,SAAAC,MAAA,IAAAC,OAAA;AACA,MAAMC,WAAW,gBAAGR,aAAa,CAAC,IAAI,CAAC;;AAEvC;AACA,OAAO,MAAMS,OAAO,GAAGA,CAAA;EAAAC,EAAA;EAAA,OAAMR,UAAU,CAACM,WAAW,CAAC;AAAA;AAACE,EAAA,CAAxCD,OAAO;AAEpB,OAAO,MAAME,YAAY,GAAGA,CAAC;EAAEC;AAAS,CAAC,KAAK;EAAAC,GAAA;EAC5C,MAAM,CAACC,IAAI,EAAEC,OAAO,CAAC,GAAGd,QAAQ,CAAC,IAAI,CAAC;EACtC,MAAM,CAACe,eAAe,EAAEC,kBAAkB,CAAC,GAAGhB,QAAQ,CAAC,KAAK,CAAC;EAC7D,MAAM,CAACiB,OAAO,EAAEC,UAAU,CAAC,GAAGlB,QAAQ,CAAC,IAAI,CAAC;EAC5C,MAAMmB,QAAQ,GAAGhB,WAAW,CAAC,CAAC;;EAE9B;EACAD,SAAS,CAAC,MAAM;IACd,MAAMkB,SAAS,GAAG,MAAAA,CAAA,KAAY;MAC5B,IAAI;QACFC,OAAO,CAACC,GAAG,CAAC,0CAA0C,CAAC;QACvD;QACAJ,UAAU,CAAC,IAAI,CAAC;;QAEhB;QACA,MAAMK,KAAK,GAAGC,YAAY,CAACC,OAAO,CAAC,OAAO,CAAC;QAC3CJ,OAAO,CAACC,GAAG,CAAC,iCAAiC,EAAEC,KAAK,GAAG,IAAI,GAAG,IAAI,CAAC;QAEnE,IAAIA,KAAK,EAAE;UACT,IAAI;YACF;YACAF,OAAO,CAACC,GAAG,CAAC,gCAAgC,CAAC;YAC7C,MAAMI,QAAQ,GAAG,MAAMtB,WAAW,CAACuB,UAAU,CAAC,CAAC;YAC/CN,OAAO,CAACC,GAAG,CAAC,4BAA4B,EAAEI,QAAQ,CAAC;YACnDZ,OAAO,CAACY,QAAQ,CAAC;YACjBV,kBAAkB,CAAC,IAAI,CAAC;UAC1B,CAAC,CAAC,OAAOY,UAAU,EAAE;YACnBP,OAAO,CAACQ,KAAK,CAAC,uCAAuC,EAAED,UAAU,CAAC;YAClE;YACAP,OAAO,CAACC,GAAG,CAAC,6CAA6C,CAAC;YAC1DE,YAAY,CAACM,UAAU,CAAC,OAAO,CAAC;YAChChB,OAAO,CAAC,IAAI,CAAC;YACbE,kBAAkB,CAAC,KAAK,CAAC;UAC3B;QACF,CAAC,MAAM;UACLK,OAAO,CAACC,GAAG,CAAC,8CAA8C,CAAC;UAC3DR,OAAO,CAAC,IAAI,CAAC;UACbE,kBAAkB,CAAC,KAAK,CAAC;QAC3B;MACF,CAAC,CAAC,OAAOa,KAAK,EAAE;QACdR,OAAO,CAACQ,KAAK,CAAC,2DAA2D,EAAEA,KAAK,CAAC;QACjF;QACAL,YAAY,CAACM,UAAU,CAAC,OAAO,CAAC;QAChChB,OAAO,CAAC,IAAI,CAAC;QACbE,kBAAkB,CAAC,KAAK,CAAC;MAC3B,CAAC,SAAS;QACR;QACAK,OAAO,CAACC,GAAG,CAAC,8CAA8C,EAAE,KAAK,CAAC;QAClEJ,UAAU,CAAC,KAAK,CAAC;MACnB;IACF,CAAC;;IAED;IACAE,SAAS,CAAC,CAAC;EACb,CAAC,EAAE,EAAE,CAAC;;EAEN;EACA,MAAMW,KAAK,GAAG,MAAAA,CAAOC,WAAW,EAAEC,SAAS,KAAK;IAC9C,IAAI;MACFZ,OAAO,CAACC,GAAG,CAAC,2BAA2BW,SAAS,EAAE,EAAED,WAAW,CAAC;MAChE,MAAME,QAAQ,GAAG,MAAM9B,WAAW,CAAC2B,KAAK,CAACC,WAAW,EAAEC,SAAS,CAAC;MAChEZ,OAAO,CAACC,GAAG,CAAC,0BAA0B,EAAEY,QAAQ,CAAC;MAEjD,MAAM;QAAEC,YAAY;QAAEC,OAAO;QAAEC,QAAQ;QAAEC;MAAK,CAAC,GAAGJ,QAAQ;;MAE1D;MACAb,OAAO,CAACC,GAAG,CAAC,oCAAoC,CAAC;MACjDE,YAAY,CAACe,OAAO,CAAC,OAAO,EAAEJ,YAAY,CAAC;;MAE3C;MACA,MAAMT,QAAQ,GAAG;QAAEc,EAAE,EAAEJ,OAAO;QAAEC,QAAQ;QAAEC;MAAK,CAAC;MAChDjB,OAAO,CAACC,GAAG,CAAC,2BAA2B,EAAEI,QAAQ,CAAC;MAClDZ,OAAO,CAACY,QAAQ,CAAC;MACjBV,kBAAkB,CAAC,IAAI,CAAC;MAExB,OAAOU,QAAQ;IACjB,CAAC,CAAC,OAAOG,KAAK,EAAE;MACdR,OAAO,CAACQ,KAAK,CAAC,0BAA0B,EAAEA,KAAK,CAAC;MAChD,MAAMA,KAAK;IACb;EACF,CAAC;;EAED;EACA,MAAMY,MAAM,GAAGA,CAAA,KAAM;IACnBjB,YAAY,CAACM,UAAU,CAAC,OAAO,CAAC;IAChChB,OAAO,CAAC,IAAI,CAAC;IACbE,kBAAkB,CAAC,KAAK,CAAC;IACzBG,QAAQ,CAAC,QAAQ,CAAC;EACpB,CAAC;;EAED;EACA,MAAMuB,QAAQ,GAAInB,KAAK,IAAK;IAC1BC,YAAY,CAACe,OAAO,CAAC,OAAO,EAAEhB,KAAK,CAAC;EACtC,CAAC;;EAED;EACA,MAAMoB,eAAe,GAAG,MAAOC,MAAM,IAAK;IACxC,IAAI;MACF,MAAMV,QAAQ,GAAG,MAAM9B,WAAW,CAACuC,eAAe,CAACC,MAAM,CAAC;;MAE1D;MACApB,YAAY,CAACe,OAAO,CAAC,OAAO,EAAEL,QAAQ,CAACC,YAAY,CAAC;;MAEpD;MACA,MAAMT,QAAQ,GAAG;QACfc,EAAE,EAAEN,QAAQ,CAACE,OAAO;QACpBC,QAAQ,EAAEH,QAAQ,CAACG,QAAQ;QAC3BC,IAAI,EAAEJ,QAAQ,CAACI;MACjB,CAAC;MACDxB,OAAO,CAACY,QAAQ,CAAC;MACjBV,kBAAkB,CAAC,IAAI,CAAC;MAExB,OAAOU,QAAQ;IACjB,CAAC,CAAC,OAAOG,KAAK,EAAE;MACdR,OAAO,CAACQ,KAAK,CAAC,uCAAuC,EAAEA,KAAK,CAAC;MAC7D,MAAMA,KAAK;IACb;EACF,CAAC;;EAED;EACA,MAAMgB,KAAK,GAAG;IACZhC,IAAI;IACJC,OAAO;IACPC,eAAe;IACfE,OAAO;IACPc,KAAK;IACLU,MAAM;IACNC,QAAQ;IACRC;EACF,CAAC;EAED,oBAAOrC,OAAA,CAACC,WAAW,CAACuC,QAAQ;IAACD,KAAK,EAAEA,KAAM;IAAAlC,QAAA,EAAEA;EAAQ;IAAAoC,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OAAuB,CAAC;AAC9E,CAAC;AAACtC,GAAA,CApIWF,YAAY;EAAA,QAINP,WAAW;AAAA;AAAAgD,EAAA,GAJjBzC,YAAY;AAAA,IAAAyC,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}