{"ast": null, "code": "// note: no implementation for weeks\n\nconst formatDistanceLocale = {\n  lessThanXSeconds: {\n    one: {\n      default: \"1 ಸೆಕೆಂಡ್‌ಗಿಂತ ಕಡಿಮೆ\",\n      future: \"1 ಸೆಕೆಂಡ್‌ಗಿಂತ ಕಡಿಮೆ\",\n      past: \"1 ಸೆಕೆಂಡ್‌ಗಿಂತ ಕಡಿಮೆ\"\n    },\n    other: {\n      default: \"{{count}} ಸೆಕೆಂಡ್‌ಗಿಂತ ಕಡಿಮೆ\",\n      future: \"{{count}} ಸೆಕೆಂಡ್‌ಗಿಂತ ಕಡಿಮೆ\",\n      past: \"{{count}} ಸೆಕೆಂಡ್‌ಗಿಂತ ಕಡಿಮೆ\"\n    }\n  },\n  xSeconds: {\n    one: {\n      default: \"1 ಸೆಕೆಂಡ್\",\n      future: \"1 ಸೆಕೆಂಡ್‌ನಲ್ಲಿ\",\n      past: \"1 ಸೆಕೆಂಡ್ ಹಿಂದೆ\"\n    },\n    other: {\n      default: \"{{count}} ಸೆಕೆಂಡುಗಳು\",\n      future: \"{{count}} ಸೆಕೆಂಡ್‌ಗಳಲ್ಲಿ\",\n      past: \"{{count}} ಸೆಕೆಂಡ್ ಹಿಂದೆ\"\n    }\n  },\n  halfAMinute: {\n    other: {\n      default: \"ಅರ್ಧ ನಿಮಿಷ\",\n      future: \"ಅರ್ಧ ನಿಮಿಷದಲ್ಲಿ\",\n      past: \"ಅರ್ಧ ನಿಮಿಷದ ಹಿಂದೆ\"\n    }\n  },\n  lessThanXMinutes: {\n    one: {\n      default: \"1 ನಿಮಿಷಕ್ಕಿಂತ ಕಡಿಮೆ\",\n      future: \"1 ನಿಮಿಷಕ್ಕಿಂತ ಕಡಿಮೆ\",\n      past: \"1 ನಿಮಿಷಕ್ಕಿಂತ ಕಡಿಮೆ\"\n    },\n    other: {\n      default: \"{{count}} ನಿಮಿಷಕ್ಕಿಂತ ಕಡಿಮೆ\",\n      future: \"{{count}} ನಿಮಿಷಕ್ಕಿಂತ ಕಡಿಮೆ\",\n      past: \"{{count}} ನಿಮಿಷಕ್ಕಿಂತ ಕಡಿಮೆ\"\n    }\n  },\n  xMinutes: {\n    one: {\n      default: \"1 ನಿಮಿಷ\",\n      future: \"1 ನಿಮಿಷದಲ್ಲಿ\",\n      past: \"1 ನಿಮಿಷದ ಹಿಂದೆ\"\n    },\n    other: {\n      default: \"{{count}} ನಿಮಿಷಗಳು\",\n      future: \"{{count}} ನಿಮಿಷಗಳಲ್ಲಿ\",\n      past: \"{{count}} ನಿಮಿಷಗಳ ಹಿಂದೆ\"\n    }\n  },\n  aboutXHours: {\n    one: {\n      default: \"ಸುಮಾರು 1 ಗಂಟೆ\",\n      future: \"ಸುಮಾರು 1 ಗಂಟೆಯಲ್ಲಿ\",\n      past: \"ಸುಮಾರು 1 ಗಂಟೆ ಹಿಂದೆ\"\n    },\n    other: {\n      default: \"ಸುಮಾರು {{count}} ಗಂಟೆಗಳು\",\n      future: \"ಸುಮಾರು {{count}} ಗಂಟೆಗಳಲ್ಲಿ\",\n      past: \"ಸುಮಾರು {{count}} ಗಂಟೆಗಳ ಹಿಂದೆ\"\n    }\n  },\n  xHours: {\n    one: {\n      default: \"1 ಗಂಟೆ\",\n      future: \"1 ಗಂಟೆಯಲ್ಲಿ\",\n      past: \"1 ಗಂಟೆ ಹಿಂದೆ\"\n    },\n    other: {\n      default: \"{{count}} ಗಂಟೆಗಳು\",\n      future: \"{{count}} ಗಂಟೆಗಳಲ್ಲಿ\",\n      past: \"{{count}} ಗಂಟೆಗಳ ಹಿಂದೆ\"\n    }\n  },\n  xDays: {\n    one: {\n      default: \"1 ದಿನ\",\n      future: \"1 ದಿನದಲ್ಲಿ\",\n      past: \"1 ದಿನದ ಹಿಂದೆ\"\n    },\n    other: {\n      default: \"{{count}} ದಿನಗಳು\",\n      future: \"{{count}} ದಿನಗಳಲ್ಲಿ\",\n      past: \"{{count}} ದಿನಗಳ ಹಿಂದೆ\"\n    }\n  },\n  // TODO\n  // aboutXWeeks: {},\n\n  // TODO\n  // xWeeks: {},\n\n  aboutXMonths: {\n    one: {\n      default: \"ಸುಮಾರು 1 ತಿಂಗಳು\",\n      future: \"ಸುಮಾರು 1 ತಿಂಗಳಲ್ಲಿ\",\n      past: \"ಸುಮಾರು 1 ತಿಂಗಳ ಹಿಂದೆ\"\n    },\n    other: {\n      default: \"ಸುಮಾರು {{count}} ತಿಂಗಳು\",\n      future: \"ಸುಮಾರು {{count}} ತಿಂಗಳುಗಳಲ್ಲಿ\",\n      past: \"ಸುಮಾರು {{count}} ತಿಂಗಳುಗಳ ಹಿಂದೆ\"\n    }\n  },\n  xMonths: {\n    one: {\n      default: \"1 ತಿಂಗಳು\",\n      future: \"1 ತಿಂಗಳಲ್ಲಿ\",\n      past: \"1 ತಿಂಗಳ ಹಿಂದೆ\"\n    },\n    other: {\n      default: \"{{count}} ತಿಂಗಳು\",\n      future: \"{{count}} ತಿಂಗಳುಗಳಲ್ಲಿ\",\n      past: \"{{count}} ತಿಂಗಳುಗಳ ಹಿಂದೆ\"\n    }\n  },\n  aboutXYears: {\n    one: {\n      default: \"ಸುಮಾರು 1 ವರ್ಷ\",\n      future: \"ಸುಮಾರು 1 ವರ್ಷದಲ್ಲಿ\",\n      past: \"ಸುಮಾರು 1 ವರ್ಷದ ಹಿಂದೆ\"\n    },\n    other: {\n      default: \"ಸುಮಾರು {{count}} ವರ್ಷಗಳು\",\n      future: \"ಸುಮಾರು {{count}} ವರ್ಷಗಳಲ್ಲಿ\",\n      past: \"ಸುಮಾರು {{count}} ವರ್ಷಗಳ ಹಿಂದೆ\"\n    }\n  },\n  xYears: {\n    one: {\n      default: \"1 ವರ್ಷ\",\n      future: \"1 ವರ್ಷದಲ್ಲಿ\",\n      past: \"1 ವರ್ಷದ ಹಿಂದೆ\"\n    },\n    other: {\n      default: \"{{count}} ವರ್ಷಗಳು\",\n      future: \"{{count}} ವರ್ಷಗಳಲ್ಲಿ\",\n      past: \"{{count}} ವರ್ಷಗಳ ಹಿಂದೆ\"\n    }\n  },\n  overXYears: {\n    one: {\n      default: \"1 ವರ್ಷದ ಮೇಲೆ\",\n      future: \"1 ವರ್ಷದ ಮೇಲೆ\",\n      past: \"1 ವರ್ಷದ ಮೇಲೆ\"\n    },\n    other: {\n      default: \"{{count}} ವರ್ಷಗಳ ಮೇಲೆ\",\n      future: \"{{count}} ವರ್ಷಗಳ ಮೇಲೆ\",\n      past: \"{{count}} ವರ್ಷಗಳ ಮೇಲೆ\"\n    }\n  },\n  almostXYears: {\n    one: {\n      default: \"ಬಹುತೇಕ 1 ವರ್ಷದಲ್ಲಿ\",\n      future: \"ಬಹುತೇಕ 1 ವರ್ಷದಲ್ಲಿ\",\n      past: \"ಬಹುತೇಕ 1 ವರ್ಷದಲ್ಲಿ\"\n    },\n    other: {\n      default: \"ಬಹುತೇಕ {{count}} ವರ್ಷಗಳಲ್ಲಿ\",\n      future: \"ಬಹುತೇಕ {{count}} ವರ್ಷಗಳಲ್ಲಿ\",\n      past: \"ಬಹುತೇಕ {{count}} ವರ್ಷಗಳಲ್ಲಿ\"\n    }\n  }\n};\nfunction getResultByTense(parentToken, options) {\n  if (options?.addSuffix) {\n    if (options.comparison && options.comparison > 0) {\n      return parentToken.future;\n    } else {\n      return parentToken.past;\n    }\n  }\n  return parentToken.default;\n}\nexport const formatDistance = (token, count, options) => {\n  let result;\n  const tokenValue = formatDistanceLocale[token];\n  if (tokenValue.one && count === 1) {\n    result = getResultByTense(tokenValue.one, options);\n  } else {\n    result = getResultByTense(tokenValue.other, options);\n  }\n  return result.replace(\"{{count}}\", String(count));\n};", "map": {"version": 3, "names": ["formatDistanceLocale", "lessThanXSeconds", "one", "default", "future", "past", "other", "xSeconds", "halfAMinute", "lessThanXMinutes", "xMinutes", "aboutXHours", "xHours", "xDays", "aboutXMonths", "xMonths", "aboutXYears", "xYears", "overXYears", "almostXYears", "getResultByTense", "parentToken", "options", "addSuffix", "comparison", "formatDistance", "token", "count", "result", "tokenValue", "replace", "String"], "sources": ["C:/Users/<USER>/PycharmProjects/CMS/webapp/frontend/node_modules/date-fns/locale/kn/_lib/formatDistance.js"], "sourcesContent": ["// note: no implementation for weeks\n\nconst formatDistanceLocale = {\n  lessThanXSeconds: {\n    one: {\n      default: \"1 ಸೆಕೆಂಡ್‌ಗಿಂತ ಕಡಿಮೆ\",\n      future: \"1 ಸೆಕೆಂಡ್‌ಗಿಂತ ಕಡಿಮೆ\",\n      past: \"1 ಸೆಕೆಂಡ್‌ಗಿಂತ ಕಡಿಮೆ\",\n    },\n    other: {\n      default: \"{{count}} ಸೆಕೆಂಡ್‌ಗಿಂತ ಕಡಿಮೆ\",\n      future: \"{{count}} ಸೆಕೆಂಡ್‌ಗಿಂತ ಕಡಿಮೆ\",\n      past: \"{{count}} ಸೆಕೆಂಡ್‌ಗಿಂತ ಕಡಿಮೆ\",\n    },\n  },\n\n  xSeconds: {\n    one: {\n      default: \"1 ಸೆಕೆಂಡ್\",\n      future: \"1 ಸೆಕೆಂಡ್‌ನಲ್ಲಿ\",\n      past: \"1 ಸೆಕೆಂಡ್ ಹಿಂದೆ\",\n    },\n    other: {\n      default: \"{{count}} ಸೆಕೆಂಡುಗಳು\",\n      future: \"{{count}} ಸೆಕೆಂಡ್‌ಗಳಲ್ಲಿ\",\n      past: \"{{count}} ಸೆಕೆಂಡ್ ಹಿಂದೆ\",\n    },\n  },\n\n  halfAMinute: {\n    other: {\n      default: \"ಅರ್ಧ ನಿಮಿಷ\",\n      future: \"ಅರ್ಧ ನಿಮಿಷದಲ್ಲಿ\",\n      past: \"ಅರ್ಧ ನಿಮಿಷದ ಹಿಂದೆ\",\n    },\n  },\n\n  lessThanXMinutes: {\n    one: {\n      default: \"1 ನಿಮಿಷಕ್ಕಿಂತ ಕಡಿಮೆ\",\n      future: \"1 ನಿಮಿಷಕ್ಕಿಂತ ಕಡಿಮೆ\",\n      past: \"1 ನಿಮಿಷಕ್ಕಿಂತ ಕಡಿಮೆ\",\n    },\n    other: {\n      default: \"{{count}} ನಿಮಿಷಕ್ಕಿಂತ ಕಡಿಮೆ\",\n      future: \"{{count}} ನಿಮಿಷಕ್ಕಿಂತ ಕಡಿಮೆ\",\n      past: \"{{count}} ನಿಮಿಷಕ್ಕಿಂತ ಕಡಿಮೆ\",\n    },\n  },\n\n  xMinutes: {\n    one: {\n      default: \"1 ನಿಮಿಷ\",\n      future: \"1 ನಿಮಿಷದಲ್ಲಿ\",\n      past: \"1 ನಿಮಿಷದ ಹಿಂದೆ\",\n    },\n    other: {\n      default: \"{{count}} ನಿಮಿಷಗಳು\",\n      future: \"{{count}} ನಿಮಿಷಗಳಲ್ಲಿ\",\n      past: \"{{count}} ನಿಮಿಷಗಳ ಹಿಂದೆ\",\n    },\n  },\n\n  aboutXHours: {\n    one: {\n      default: \"ಸುಮಾರು 1 ಗಂಟೆ\",\n      future: \"ಸುಮಾರು 1 ಗಂಟೆಯಲ್ಲಿ\",\n      past: \"ಸುಮಾರು 1 ಗಂಟೆ ಹಿಂದೆ\",\n    },\n    other: {\n      default: \"ಸುಮಾರು {{count}} ಗಂಟೆಗಳು\",\n      future: \"ಸುಮಾರು {{count}} ಗಂಟೆಗಳಲ್ಲಿ\",\n      past: \"ಸುಮಾರು {{count}} ಗಂಟೆಗಳ ಹಿಂದೆ\",\n    },\n  },\n\n  xHours: {\n    one: {\n      default: \"1 ಗಂಟೆ\",\n      future: \"1 ಗಂಟೆಯಲ್ಲಿ\",\n      past: \"1 ಗಂಟೆ ಹಿಂದೆ\",\n    },\n    other: {\n      default: \"{{count}} ಗಂಟೆಗಳು\",\n      future: \"{{count}} ಗಂಟೆಗಳಲ್ಲಿ\",\n      past: \"{{count}} ಗಂಟೆಗಳ ಹಿಂದೆ\",\n    },\n  },\n\n  xDays: {\n    one: {\n      default: \"1 ದಿನ\",\n      future: \"1 ದಿನದಲ್ಲಿ\",\n      past: \"1 ದಿನದ ಹಿಂದೆ\",\n    },\n    other: {\n      default: \"{{count}} ದಿನಗಳು\",\n      future: \"{{count}} ದಿನಗಳಲ್ಲಿ\",\n      past: \"{{count}} ದಿನಗಳ ಹಿಂದೆ\",\n    },\n  },\n\n  // TODO\n  // aboutXWeeks: {},\n\n  // TODO\n  // xWeeks: {},\n\n  aboutXMonths: {\n    one: {\n      default: \"ಸುಮಾರು 1 ತಿಂಗಳು\",\n      future: \"ಸುಮಾರು 1 ತಿಂಗಳಲ್ಲಿ\",\n      past: \"ಸುಮಾರು 1 ತಿಂಗಳ ಹಿಂದೆ\",\n    },\n    other: {\n      default: \"ಸುಮಾರು {{count}} ತಿಂಗಳು\",\n      future: \"ಸುಮಾರು {{count}} ತಿಂಗಳುಗಳಲ್ಲಿ\",\n      past: \"ಸುಮಾರು {{count}} ತಿಂಗಳುಗಳ ಹಿಂದೆ\",\n    },\n  },\n\n  xMonths: {\n    one: {\n      default: \"1 ತಿಂಗಳು\",\n      future: \"1 ತಿಂಗಳಲ್ಲಿ\",\n      past: \"1 ತಿಂಗಳ ಹಿಂದೆ\",\n    },\n    other: {\n      default: \"{{count}} ತಿಂಗಳು\",\n      future: \"{{count}} ತಿಂಗಳುಗಳಲ್ಲಿ\",\n      past: \"{{count}} ತಿಂಗಳುಗಳ ಹಿಂದೆ\",\n    },\n  },\n\n  aboutXYears: {\n    one: {\n      default: \"ಸುಮಾರು 1 ವರ್ಷ\",\n      future: \"ಸುಮಾರು 1 ವರ್ಷದಲ್ಲಿ\",\n      past: \"ಸುಮಾರು 1 ವರ್ಷದ ಹಿಂದೆ\",\n    },\n    other: {\n      default: \"ಸುಮಾರು {{count}} ವರ್ಷಗಳು\",\n      future: \"ಸುಮಾರು {{count}} ವರ್ಷಗಳಲ್ಲಿ\",\n      past: \"ಸುಮಾರು {{count}} ವರ್ಷಗಳ ಹಿಂದೆ\",\n    },\n  },\n\n  xYears: {\n    one: {\n      default: \"1 ವರ್ಷ\",\n      future: \"1 ವರ್ಷದಲ್ಲಿ\",\n      past: \"1 ವರ್ಷದ ಹಿಂದೆ\",\n    },\n    other: {\n      default: \"{{count}} ವರ್ಷಗಳು\",\n      future: \"{{count}} ವರ್ಷಗಳಲ್ಲಿ\",\n      past: \"{{count}} ವರ್ಷಗಳ ಹಿಂದೆ\",\n    },\n  },\n\n  overXYears: {\n    one: {\n      default: \"1 ವರ್ಷದ ಮೇಲೆ\",\n      future: \"1 ವರ್ಷದ ಮೇಲೆ\",\n      past: \"1 ವರ್ಷದ ಮೇಲೆ\",\n    },\n    other: {\n      default: \"{{count}} ವರ್ಷಗಳ ಮೇಲೆ\",\n      future: \"{{count}} ವರ್ಷಗಳ ಮೇಲೆ\",\n      past: \"{{count}} ವರ್ಷಗಳ ಮೇಲೆ\",\n    },\n  },\n\n  almostXYears: {\n    one: {\n      default: \"ಬಹುತೇಕ 1 ವರ್ಷದಲ್ಲಿ\",\n      future: \"ಬಹುತೇಕ 1 ವರ್ಷದಲ್ಲಿ\",\n      past: \"ಬಹುತೇಕ 1 ವರ್ಷದಲ್ಲಿ\",\n    },\n    other: {\n      default: \"ಬಹುತೇಕ {{count}} ವರ್ಷಗಳಲ್ಲಿ\",\n      future: \"ಬಹುತೇಕ {{count}} ವರ್ಷಗಳಲ್ಲಿ\",\n      past: \"ಬಹುತೇಕ {{count}} ವರ್ಷಗಳಲ್ಲಿ\",\n    },\n  },\n};\n\nfunction getResultByTense(parentToken, options) {\n  if (options?.addSuffix) {\n    if (options.comparison && options.comparison > 0) {\n      return parentToken.future;\n    } else {\n      return parentToken.past;\n    }\n  }\n  return parentToken.default;\n}\n\nexport const formatDistance = (token, count, options) => {\n  let result;\n\n  const tokenValue = formatDistanceLocale[token];\n\n  if (tokenValue.one && count === 1) {\n    result = getResultByTense(tokenValue.one, options);\n  } else {\n    result = getResultByTense(tokenValue.other, options);\n  }\n\n  return result.replace(\"{{count}}\", String(count));\n};\n"], "mappings": "AAAA;;AAEA,MAAMA,oBAAoB,GAAG;EAC3BC,gBAAgB,EAAE;IAChBC,GAAG,EAAE;MACHC,OAAO,EAAE,sBAAsB;MAC/BC,MAAM,EAAE,sBAAsB;MAC9BC,IAAI,EAAE;IACR,CAAC;IACDC,KAAK,EAAE;MACLH,OAAO,EAAE,8BAA8B;MACvCC,MAAM,EAAE,8BAA8B;MACtCC,IAAI,EAAE;IACR;EACF,CAAC;EAEDE,QAAQ,EAAE;IACRL,GAAG,EAAE;MACHC,OAAO,EAAE,WAAW;MACpBC,MAAM,EAAE,iBAAiB;MACzBC,IAAI,EAAE;IACR,CAAC;IACDC,KAAK,EAAE;MACLH,OAAO,EAAE,sBAAsB;MAC/BC,MAAM,EAAE,0BAA0B;MAClCC,IAAI,EAAE;IACR;EACF,CAAC;EAEDG,WAAW,EAAE;IACXF,KAAK,EAAE;MACLH,OAAO,EAAE,YAAY;MACrBC,MAAM,EAAE,iBAAiB;MACzBC,IAAI,EAAE;IACR;EACF,CAAC;EAEDI,gBAAgB,EAAE;IAChBP,GAAG,EAAE;MACHC,OAAO,EAAE,qBAAqB;MAC9BC,MAAM,EAAE,qBAAqB;MAC7BC,IAAI,EAAE;IACR,CAAC;IACDC,KAAK,EAAE;MACLH,OAAO,EAAE,6BAA6B;MACtCC,MAAM,EAAE,6BAA6B;MACrCC,IAAI,EAAE;IACR;EACF,CAAC;EAEDK,QAAQ,EAAE;IACRR,GAAG,EAAE;MACHC,OAAO,EAAE,SAAS;MAClBC,MAAM,EAAE,cAAc;MACtBC,IAAI,EAAE;IACR,CAAC;IACDC,KAAK,EAAE;MACLH,OAAO,EAAE,oBAAoB;MAC7BC,MAAM,EAAE,uBAAuB;MAC/BC,IAAI,EAAE;IACR;EACF,CAAC;EAEDM,WAAW,EAAE;IACXT,GAAG,EAAE;MACHC,OAAO,EAAE,eAAe;MACxBC,MAAM,EAAE,oBAAoB;MAC5BC,IAAI,EAAE;IACR,CAAC;IACDC,KAAK,EAAE;MACLH,OAAO,EAAE,0BAA0B;MACnCC,MAAM,EAAE,6BAA6B;MACrCC,IAAI,EAAE;IACR;EACF,CAAC;EAEDO,MAAM,EAAE;IACNV,GAAG,EAAE;MACHC,OAAO,EAAE,QAAQ;MACjBC,MAAM,EAAE,aAAa;MACrBC,IAAI,EAAE;IACR,CAAC;IACDC,KAAK,EAAE;MACLH,OAAO,EAAE,mBAAmB;MAC5BC,MAAM,EAAE,sBAAsB;MAC9BC,IAAI,EAAE;IACR;EACF,CAAC;EAEDQ,KAAK,EAAE;IACLX,GAAG,EAAE;MACHC,OAAO,EAAE,OAAO;MAChBC,MAAM,EAAE,YAAY;MACpBC,IAAI,EAAE;IACR,CAAC;IACDC,KAAK,EAAE;MACLH,OAAO,EAAE,kBAAkB;MAC3BC,MAAM,EAAE,qBAAqB;MAC7BC,IAAI,EAAE;IACR;EACF,CAAC;EAED;EACA;;EAEA;EACA;;EAEAS,YAAY,EAAE;IACZZ,GAAG,EAAE;MACHC,OAAO,EAAE,iBAAiB;MAC1BC,MAAM,EAAE,oBAAoB;MAC5BC,IAAI,EAAE;IACR,CAAC;IACDC,KAAK,EAAE;MACLH,OAAO,EAAE,yBAAyB;MAClCC,MAAM,EAAE,+BAA+B;MACvCC,IAAI,EAAE;IACR;EACF,CAAC;EAEDU,OAAO,EAAE;IACPb,GAAG,EAAE;MACHC,OAAO,EAAE,UAAU;MACnBC,MAAM,EAAE,aAAa;MACrBC,IAAI,EAAE;IACR,CAAC;IACDC,KAAK,EAAE;MACLH,OAAO,EAAE,kBAAkB;MAC3BC,MAAM,EAAE,wBAAwB;MAChCC,IAAI,EAAE;IACR;EACF,CAAC;EAEDW,WAAW,EAAE;IACXd,GAAG,EAAE;MACHC,OAAO,EAAE,eAAe;MACxBC,MAAM,EAAE,oBAAoB;MAC5BC,IAAI,EAAE;IACR,CAAC;IACDC,KAAK,EAAE;MACLH,OAAO,EAAE,0BAA0B;MACnCC,MAAM,EAAE,6BAA6B;MACrCC,IAAI,EAAE;IACR;EACF,CAAC;EAEDY,MAAM,EAAE;IACNf,GAAG,EAAE;MACHC,OAAO,EAAE,QAAQ;MACjBC,MAAM,EAAE,aAAa;MACrBC,IAAI,EAAE;IACR,CAAC;IACDC,KAAK,EAAE;MACLH,OAAO,EAAE,mBAAmB;MAC5BC,MAAM,EAAE,sBAAsB;MAC9BC,IAAI,EAAE;IACR;EACF,CAAC;EAEDa,UAAU,EAAE;IACVhB,GAAG,EAAE;MACHC,OAAO,EAAE,cAAc;MACvBC,MAAM,EAAE,cAAc;MACtBC,IAAI,EAAE;IACR,CAAC;IACDC,KAAK,EAAE;MACLH,OAAO,EAAE,uBAAuB;MAChCC,MAAM,EAAE,uBAAuB;MAC/BC,IAAI,EAAE;IACR;EACF,CAAC;EAEDc,YAAY,EAAE;IACZjB,GAAG,EAAE;MACHC,OAAO,EAAE,oBAAoB;MAC7BC,MAAM,EAAE,oBAAoB;MAC5BC,IAAI,EAAE;IACR,CAAC;IACDC,KAAK,EAAE;MACLH,OAAO,EAAE,6BAA6B;MACtCC,MAAM,EAAE,6BAA6B;MACrCC,IAAI,EAAE;IACR;EACF;AACF,CAAC;AAED,SAASe,gBAAgBA,CAACC,WAAW,EAAEC,OAAO,EAAE;EAC9C,IAAIA,OAAO,EAAEC,SAAS,EAAE;IACtB,IAAID,OAAO,CAACE,UAAU,IAAIF,OAAO,CAACE,UAAU,GAAG,CAAC,EAAE;MAChD,OAAOH,WAAW,CAACjB,MAAM;IAC3B,CAAC,MAAM;MACL,OAAOiB,WAAW,CAAChB,IAAI;IACzB;EACF;EACA,OAAOgB,WAAW,CAAClB,OAAO;AAC5B;AAEA,OAAO,MAAMsB,cAAc,GAAGA,CAACC,KAAK,EAAEC,KAAK,EAAEL,OAAO,KAAK;EACvD,IAAIM,MAAM;EAEV,MAAMC,UAAU,GAAG7B,oBAAoB,CAAC0B,KAAK,CAAC;EAE9C,IAAIG,UAAU,CAAC3B,GAAG,IAAIyB,KAAK,KAAK,CAAC,EAAE;IACjCC,MAAM,GAAGR,gBAAgB,CAACS,UAAU,CAAC3B,GAAG,EAAEoB,OAAO,CAAC;EACpD,CAAC,MAAM;IACLM,MAAM,GAAGR,gBAAgB,CAACS,UAAU,CAACvB,KAAK,EAAEgB,OAAO,CAAC;EACtD;EAEA,OAAOM,MAAM,CAACE,OAAO,CAAC,WAAW,EAAEC,MAAM,CAACJ,KAAK,CAAC,CAAC;AACnD,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}