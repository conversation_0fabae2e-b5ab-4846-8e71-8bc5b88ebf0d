{"ast": null, "code": "var _jsxFileName = \"C:\\\\CMS\\\\webapp\\\\frontend\\\\src\\\\components\\\\cavi\\\\QuickAddCablesDialog.js\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect } from 'react';\nimport { Dialog, DialogTitle, DialogContent, DialogActions, Button, Typography, Box, TextField, Checkbox, FormControlLabel, Table, TableBody, TableCell, TableContainer, TableHead, TableRow, Paper, CircularProgress, Alert, IconButton, Chip, Tooltip, Tabs, Tab, List, ListItem, ListItemText, ListItemSecondaryAction, Divider, Card, CardContent, CardHeader, Badge } from '@mui/material';\nimport { Add as AddIcon, Delete as DeleteIcon, Save as SaveIcon, Info as InfoIcon, Warning as WarningIcon, Search as SearchIcon, Cable as CableIcon, CheckCircle as CheckCircleIcon, Error as ErrorIcon } from '@mui/icons-material';\nimport caviService from '../../services/caviService';\nimport { determineCableState, getCableStateColor, isCableInstalled } from '../../utils/stateUtils';\nimport IncompatibleReelDialog from './IncompatibleReelDialog';\n\n// Utility per verificare compatibilità tra cavo e bobina\nimport { jsxDEV as _jsxDEV, Fragment as _Fragment } from \"react/jsx-dev-runtime\";\nconst isCompatible = (cavo, bobina) => {\n  return cavo.tipologia === bobina.tipologia && String(cavo.sezione) === String(bobina.sezione);\n};\n\n// Utility per ottenere il numero della bobina dall'ID\nconst getBobinaNumber = idBobina => {\n  if (!idBobina) return '';\n  const parts = idBobina.split('-');\n  return parts.length > 1 ? parts[1] : idBobina;\n};\n\n/**\n * Componente per aggiungere rapidamente più cavi a una bobina\n * Implementa sistema di doppia lista per cavi compatibili/incompatibili\n * con gestione intelligente delle incompatibilità e dialog moderni\n */\nconst QuickAddCablesDialog = ({\n  open,\n  onClose,\n  bobina,\n  cantiereId,\n  onSuccess,\n  onError\n}) => {\n  _s();\n  var _bobina$metri_residui, _bobina$metri_residui2;\n  // Stati per la gestione dei dati\n  const [loading, setLoading] = useState(false);\n  const [caviLoading, setCaviLoading] = useState(false);\n  const [saving, setSaving] = useState(false);\n\n  // Stati per i dati separati\n  const [allCavi, setAllCavi] = useState([]);\n  const [caviCompatibili, setCaviCompatibili] = useState([]);\n  const [caviIncompatibili, setCaviIncompatibili] = useState([]);\n  const [selectedCavi, setSelectedCavi] = useState([]);\n  const [caviMetri, setCaviMetri] = useState({});\n\n  // Stati per la UI\n  const [activeTab, setActiveTab] = useState(0);\n  const [searchTerm, setSearchTerm] = useState('');\n  const [showIncompatibleDialog, setShowIncompatibleDialog] = useState(false);\n  const [incompatibleSelection, setIncompatibleSelection] = useState(null);\n  const [showOverDialog, setShowOverDialog] = useState(false);\n  const [overDialogData, setOverDialogData] = useState(null);\n\n  // Stati per la validazione\n  const [errors, setErrors] = useState({});\n  const [warnings, setWarnings] = useState({});\n\n  // Carica i cavi disponibili quando il dialog viene aperto\n  useEffect(() => {\n    if (open && bobina && cantiereId) {\n      loadCavi();\n      // Reset stati quando si apre il dialog\n      resetDialogState();\n    }\n  }, [open, bobina, cantiereId]);\n\n  // Reset dello stato del dialog\n  const resetDialogState = () => {\n    setSelectedCavi([]);\n    setCaviMetri({});\n    setErrors({});\n    setWarnings({});\n    setSearchTerm('');\n    setActiveTab(0);\n    setShowIncompatibleDialog(false);\n    setIncompatibleSelection(null);\n    setShowOverDialog(false);\n    setOverDialogData(null);\n  };\n\n  // Funzione per caricare i cavi con sistema di doppia lista\n  const loadCavi = async () => {\n    try {\n      setCaviLoading(true);\n      const caviData = await caviService.getCavi(cantiereId);\n\n      // Filtra i cavi disponibili (non installati e non SPARE)\n      const caviDisponibili = caviData.filter(cavo => !isCableInstalled(cavo) && cavo.modificato_manualmente !== 3);\n\n      // Separa cavi compatibili e incompatibili (CORREZIONE: rimosso n_conduttori)\n      const compatibili = caviDisponibili.filter(cavo => isCompatible(cavo, bobina));\n      const incompatibili = caviDisponibili.filter(cavo => !isCompatible(cavo, bobina));\n      setAllCavi(caviDisponibili);\n      setCaviCompatibili(compatibili);\n      setCaviIncompatibili(incompatibili);\n      console.log(`Cavi caricati: ${compatibili.length} compatibili, ${incompatibili.length} incompatibili`);\n    } catch (error) {\n      console.error('Errore nel caricamento dei cavi:', error);\n      onError('Errore nel caricamento dei cavi: ' + (error.message || 'Errore sconosciuto'));\n    } finally {\n      setCaviLoading(false);\n    }\n  };\n\n  // Gestisce la selezione di un cavo compatibile\n  const handleCompatibleCavoSelect = cavo => {\n    setSelectedCavi(prev => {\n      const isSelected = prev.some(c => c.id_cavo === cavo.id_cavo);\n      if (isSelected) {\n        // Conferma prima di rimuovere se ci sono metri inseriti\n        const hasMetri = caviMetri[cavo.id_cavo] && caviMetri[cavo.id_cavo].trim() !== '';\n        if (hasMetri) {\n          if (!window.confirm(`Rimuovere il cavo ${cavo.id_cavo} dalla selezione? I metri inseriti (${caviMetri[cavo.id_cavo]}m) andranno persi.`)) {\n            return prev;\n          }\n        }\n\n        // Rimuovi il cavo dalla selezione\n        const newSelected = prev.filter(c => c.id_cavo !== cavo.id_cavo);\n\n        // Rimuovi anche i metri associati\n        const newCaviMetri = {\n          ...caviMetri\n        };\n        delete newCaviMetri[cavo.id_cavo];\n        setCaviMetri(newCaviMetri);\n\n        // Rimuovi errori e warning\n        setErrors(prevErrors => {\n          const newErrors = {\n            ...prevErrors\n          };\n          delete newErrors[cavo.id_cavo];\n          return newErrors;\n        });\n        setWarnings(prevWarnings => {\n          const newWarnings = {\n            ...prevWarnings\n          };\n          delete newWarnings[cavo.id_cavo];\n          return newWarnings;\n        });\n        return newSelected;\n      } else {\n        // Aggiungi il cavo alla selezione\n        return [...prev, cavo];\n      }\n    });\n  };\n\n  // Gestisce la selezione di un cavo incompatibile\n  const handleIncompatibleCavoSelect = cavo => {\n    setIncompatibleSelection({\n      cavo,\n      bobina\n    });\n    setShowIncompatibleDialog(true);\n  };\n\n  // Gestisce l'uso di una bobina incompatibile\n  const handleUseIncompatibleReel = () => {\n    if (incompatibleSelection) {\n      const {\n        cavo\n      } = incompatibleSelection;\n\n      // Aggiungi il cavo alla selezione con flag di incompatibilità\n      setSelectedCavi(prev => [...prev, {\n        ...cavo,\n        _isIncompatible: true\n      }]);\n      setShowIncompatibleDialog(false);\n      setIncompatibleSelection(null);\n      onSuccess === null || onSuccess === void 0 ? void 0 : onSuccess(`Cavo incompatibile ${cavo.id_cavo} aggiunto alla selezione. Sarà utilizzato con force_over.`);\n    }\n  };\n\n  // Gestisce l'input dei metri posati per un cavo\n  const handleMetriChange = (cavoId, value) => {\n    // Aggiorna i metri per il cavo\n    setCaviMetri(prev => ({\n      ...prev,\n      [cavoId]: value\n    }));\n\n    // Valida il valore inserito in tempo reale\n    validateMetri(cavoId, value);\n  };\n\n  // Valida i metri inseriti per un cavo con feedback migliorato\n  const validateMetri = (cavoId, value) => {\n    const cavo = allCavi.find(c => c.id_cavo === cavoId);\n    if (!cavo) return;\n\n    // Resetta gli errori e gli avvisi per questo cavo\n    setErrors(prev => {\n      const newErrors = {\n        ...prev\n      };\n      delete newErrors[cavoId];\n      return newErrors;\n    });\n    setWarnings(prev => {\n      const newWarnings = {\n        ...prev\n      };\n      delete newWarnings[cavoId];\n      return newWarnings;\n    });\n\n    // Controllo input vuoto\n    if (!value || value.trim() === '') {\n      return true; // Non mostrare errore per input vuoto durante la digitazione\n    }\n\n    // Controllo formato numerico\n    if (isNaN(parseFloat(value)) || parseFloat(value) <= 0) {\n      setErrors(prev => ({\n        ...prev,\n        [cavoId]: 'Inserire un valore numerico positivo'\n      }));\n      return false;\n    }\n    const metriPosati = parseFloat(value);\n\n    // Controllo metri teorici cavo\n    if (cavo.metri_teorici && metriPosati > parseFloat(cavo.metri_teorici)) {\n      setWarnings(prev => ({\n        ...prev,\n        [cavoId]: `I metri posati (${metriPosati}m) superano i metri teorici del cavo (${cavo.metri_teorici}m)`\n      }));\n    }\n\n    // Controllo metri residui bobina con calcolo in tempo reale\n    const metriTotaliRichiesti = Object.entries(caviMetri).filter(([id, _]) => id !== cavoId) // Escludi il cavo corrente\n    .reduce((sum, [_, metri]) => sum + parseFloat(metri || 0), 0) + metriPosati;\n    if (metriTotaliRichiesti > bobina.metri_residui) {\n      setWarnings(prev => ({\n        ...prev,\n        [cavoId]: `Totale richiesto: ${metriTotaliRichiesti.toFixed(1)}m > Residui bobina: ${bobina.metri_residui.toFixed(1)}m (OVER)`\n      }));\n    }\n    return true;\n  };\n\n  // Valida tutti i metri inseriti\n  const validateAllMetri = () => {\n    let isValid = true;\n    const newErrors = {};\n    const newWarnings = {};\n\n    // Verifica che ci siano cavi selezionati\n    if (selectedCavi.length === 0) {\n      onError('Seleziona almeno un cavo');\n      return false;\n    }\n\n    // Verifica che tutti i cavi selezionati abbiano metri inseriti\n    for (const cavo of selectedCavi) {\n      const metri = caviMetri[cavo.id_cavo];\n\n      // Controllo input vuoto\n      if (!metri || metri.trim() === '') {\n        newErrors[cavo.id_cavo] = 'Inserire un valore per i metri posati';\n        isValid = false;\n        continue;\n      }\n\n      // Controllo formato numerico\n      if (isNaN(parseFloat(metri)) || parseFloat(metri) <= 0) {\n        newErrors[cavo.id_cavo] = 'Inserire un valore numerico positivo';\n        isValid = false;\n        continue;\n      }\n      const metriPosati = parseFloat(metri);\n\n      // Controllo metri teorici cavo\n      if (cavo.metri_teorici && metriPosati > parseFloat(cavo.metri_teorici)) {\n        newWarnings[cavo.id_cavo] = `I metri posati (${metriPosati}m) superano i metri teorici del cavo (${cavo.metri_teorici}m)`;\n        // Non mostrare popup di conferma, solo l'avviso nel form\n      }\n    }\n\n    // Verifica che i metri totali richiesti non superino i metri residui della bobina\n    const metriTotaliRichiesti = Object.values(caviMetri).reduce((sum, metri) => sum + parseFloat(metri || 0), 0);\n    if (metriTotaliRichiesti > bobina.metri_residui) {\n      // Questo è un avviso globale, non specifico per un cavo\n      if (!window.confirm(`ATTENZIONE: I metri totali richiesti (${metriTotaliRichiesti}m) superano i metri residui della bobina (${bobina.metri_residui}m).\\n\\nQuesto porterà la bobina in stato OVER.\\n\\nVuoi continuare?`)) {\n        isValid = false;\n      }\n    }\n    setErrors(newErrors);\n    setWarnings(newWarnings);\n    return isValid;\n  };\n\n  // Gestisce il salvataggio dei dati\n  const handleSave = async () => {\n    try {\n      // Validazione\n      if (!validateAllMetri()) {\n        return;\n      }\n      setSaving(true);\n\n      // Conferma finale\n      if (!window.confirm(`Confermi l'aggiornamento di ${selectedCavi.length} cavi con la bobina ${bobina.id_bobina}?`)) {\n        setSaving(false);\n        return;\n      }\n\n      // Aggiorna ogni cavo selezionato\n      const results = [];\n      let errors = [];\n      for (const cavo of selectedCavi) {\n        try {\n          const metriPosati = parseFloat(caviMetri[cavo.id_cavo]);\n\n          // Determina se è necessario forzare lo stato OVER della bobina\n          const metriGiàUtilizzati = results.reduce((sum, r) => sum + r.metriPosati, 0);\n          const forceOver = metriGiàUtilizzati + metriPosati > bobina.metri_residui;\n\n          // Aggiorna i metri posati del cavo\n          const result = await caviService.updateMetriPosati(cantiereId, cavo.id_cavo, metriPosati, bobina.id_bobina, forceOver);\n          results.push({\n            cavo: cavo.id_cavo,\n            metriPosati,\n            success: true\n          });\n        } catch (error) {\n          console.error(`Errore nell'aggiornamento del cavo ${cavo.id_cavo}:`, error);\n          errors.push({\n            cavo: cavo.id_cavo,\n            error: error.message || 'Errore sconosciuto'\n          });\n        }\n      }\n\n      // Gestione del risultato\n      if (errors.length === 0) {\n        // Tutti i cavi sono stati aggiornati con successo\n        onSuccess(`${results.length} cavi aggiornati con successo`);\n        onClose();\n      } else if (results.length > 0) {\n        // Alcuni cavi sono stati aggiornati, altri no\n        onSuccess(`${results.length} cavi aggiornati con successo, ${errors.length} falliti`);\n        onError(`Errori: ${errors.map(e => `${e.cavo}: ${e.error}`).join(', ')}`);\n        onClose();\n      } else {\n        // Nessun cavo è stato aggiornato\n        onError(`Nessun cavo aggiornato. Errori: ${errors.map(e => `${e.cavo}: ${e.error}`).join(', ')}`);\n      }\n    } catch (error) {\n      console.error('Errore durante il salvataggio:', error);\n      onError('Errore durante il salvataggio: ' + (error.message || 'Errore sconosciuto'));\n    } finally {\n      setSaving(false);\n    }\n  };\n\n  // Filtra i cavi in base al termine di ricerca\n  const filteredCavi = cavi.filter(cavo => cavo.id_cavo.toLowerCase().includes(searchTerm.toLowerCase()) || cavo.tipologia && cavo.tipologia.toLowerCase().includes(searchTerm.toLowerCase()) || cavo.ubicazione_partenza && cavo.ubicazione_partenza.toLowerCase().includes(searchTerm.toLowerCase()) || cavo.ubicazione_arrivo && cavo.ubicazione_arrivo.toLowerCase().includes(searchTerm.toLowerCase()));\n  return /*#__PURE__*/_jsxDEV(Dialog, {\n    open: open,\n    onClose: onClose,\n    maxWidth: \"lg\",\n    fullWidth: true,\n    children: [/*#__PURE__*/_jsxDEV(DialogTitle, {\n      children: [\"Aggiungi cavi alla bobina \", (bobina === null || bobina === void 0 ? void 0 : bobina.numero_bobina) || '']\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 415,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(DialogContent, {\n      children: !bobina ? /*#__PURE__*/_jsxDEV(Alert, {\n        severity: \"error\",\n        children: \"Nessuna bobina selezionata\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 420,\n        columnNumber: 11\n      }, this) : /*#__PURE__*/_jsxDEV(_Fragment, {\n        children: [/*#__PURE__*/_jsxDEV(Box, {\n          sx: {\n            mb: 3,\n            p: 2,\n            bgcolor: '#f5f5f5',\n            borderRadius: 1\n          },\n          children: [/*#__PURE__*/_jsxDEV(Typography, {\n            variant: \"subtitle1\",\n            gutterBottom: true,\n            children: \"Dettagli bobina\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 425,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(Box, {\n            sx: {\n              display: 'flex',\n              flexWrap: 'wrap',\n              gap: 3\n            },\n            children: [/*#__PURE__*/_jsxDEV(Box, {\n              children: [/*#__PURE__*/_jsxDEV(Typography, {\n                variant: \"body2\",\n                color: \"text.secondary\",\n                children: \"ID Bobina\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 430,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                variant: \"body1\",\n                children: bobina.id_bobina\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 431,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 429,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(Box, {\n              children: [/*#__PURE__*/_jsxDEV(Typography, {\n                variant: \"body2\",\n                color: \"text.secondary\",\n                children: \"Tipologia\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 434,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                variant: \"body1\",\n                children: bobina.tipologia || 'N/A'\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 435,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 433,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(Box, {\n              children: [/*#__PURE__*/_jsxDEV(Typography, {\n                variant: \"body2\",\n                color: \"text.secondary\",\n                children: \"Conduttori\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 438,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                variant: \"body1\",\n                children: [bobina.n_conduttori || 'N/A', \" x \", bobina.sezione || 'N/A']\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 439,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 437,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(Box, {\n              children: [/*#__PURE__*/_jsxDEV(Typography, {\n                variant: \"body2\",\n                color: \"text.secondary\",\n                children: \"Metri residui\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 442,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                variant: \"body1\",\n                children: [((_bobina$metri_residui = bobina.metri_residui) === null || _bobina$metri_residui === void 0 ? void 0 : _bobina$metri_residui.toFixed(1)) || '0', \" m\"]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 443,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 441,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(Box, {\n              children: [/*#__PURE__*/_jsxDEV(Typography, {\n                variant: \"body2\",\n                color: \"text.secondary\",\n                children: \"Stato\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 446,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(Chip, {\n                label: bobina.stato_bobina || 'N/D',\n                size: \"small\",\n                color: bobina.stato_bobina === 'Disponibile' ? 'success' : bobina.stato_bobina === 'In uso' ? 'primary' : bobina.stato_bobina === 'Over' ? 'error' : bobina.stato_bobina === 'Terminata' ? 'warning' : 'default'\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 447,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 445,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 428,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 424,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(TextField, {\n          fullWidth: true,\n          label: \"Cerca cavi\",\n          variant: \"outlined\",\n          value: searchTerm,\n          onChange: e => setSearchTerm(e.target.value),\n          placeholder: \"Cerca per ID, tipologia, ubicazione...\",\n          sx: {\n            mb: 2\n          }\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 462,\n          columnNumber: 13\n        }, this), caviLoading ? /*#__PURE__*/_jsxDEV(Box, {\n          sx: {\n            display: 'flex',\n            justifyContent: 'center',\n            my: 4\n          },\n          children: /*#__PURE__*/_jsxDEV(CircularProgress, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 475,\n            columnNumber: 17\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 474,\n          columnNumber: 15\n        }, this) : filteredCavi.length === 0 ? /*#__PURE__*/_jsxDEV(Alert, {\n          severity: \"info\",\n          children: \"Nessun cavo compatibile disponibile per questa bobina.\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 478,\n          columnNumber: 15\n        }, this) : /*#__PURE__*/_jsxDEV(TableContainer, {\n          component: Paper,\n          sx: {\n            mb: 3\n          },\n          children: /*#__PURE__*/_jsxDEV(Table, {\n            size: \"small\",\n            children: [/*#__PURE__*/_jsxDEV(TableHead, {\n              children: /*#__PURE__*/_jsxDEV(TableRow, {\n                sx: {\n                  bgcolor: '#f5f5f5'\n                },\n                children: [/*#__PURE__*/_jsxDEV(TableCell, {\n                  padding: \"checkbox\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 486,\n                  columnNumber: 23\n                }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n                  children: \"ID Cavo\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 487,\n                  columnNumber: 23\n                }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n                  children: \"Tipologia\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 488,\n                  columnNumber: 23\n                }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n                  children: \"Ubicazione\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 489,\n                  columnNumber: 23\n                }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n                  children: \"Metri Teorici\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 490,\n                  columnNumber: 23\n                }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n                  children: \"Metri Posati\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 491,\n                  columnNumber: 23\n                }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n                  children: \"Stato\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 492,\n                  columnNumber: 23\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 485,\n                columnNumber: 21\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 484,\n              columnNumber: 19\n            }, this), /*#__PURE__*/_jsxDEV(TableBody, {\n              children: filteredCavi.map(cavo => {\n                const isSelected = selectedCavi.some(c => c.id_cavo === cavo.id_cavo);\n                return /*#__PURE__*/_jsxDEV(TableRow, {\n                  hover: true,\n                  selected: isSelected,\n                  onClick: () => handleCavoSelect(cavo),\n                  sx: {\n                    cursor: 'pointer'\n                  },\n                  children: [/*#__PURE__*/_jsxDEV(TableCell, {\n                    padding: \"checkbox\",\n                    children: /*#__PURE__*/_jsxDEV(Checkbox, {\n                      checked: isSelected,\n                      onChange: e => {\n                        e.stopPropagation();\n                        handleCavoSelect(cavo);\n                      }\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 507,\n                      columnNumber: 29\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 506,\n                    columnNumber: 27\n                  }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n                    children: cavo.id_cavo\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 515,\n                    columnNumber: 27\n                  }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n                    children: cavo.tipologia || 'N/A'\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 516,\n                    columnNumber: 27\n                  }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n                    children: /*#__PURE__*/_jsxDEV(Tooltip, {\n                      title: `Da: ${cavo.ubicazione_partenza || 'N/A'} - A: ${cavo.ubicazione_arrivo || 'N/A'}`,\n                      children: /*#__PURE__*/_jsxDEV(Box, {\n                        sx: {\n                          maxWidth: 150,\n                          overflow: 'hidden',\n                          textOverflow: 'ellipsis',\n                          whiteSpace: 'nowrap'\n                        },\n                        children: [cavo.ubicazione_partenza || 'N/A', \" \\u2192 \", cavo.ubicazione_arrivo || 'N/A']\n                      }, void 0, true, {\n                        fileName: _jsxFileName,\n                        lineNumber: 519,\n                        columnNumber: 31\n                      }, this)\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 518,\n                      columnNumber: 29\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 517,\n                    columnNumber: 27\n                  }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n                    children: cavo.metri_teorici || 'N/A'\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 524,\n                    columnNumber: 27\n                  }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n                    children: isSelected ? /*#__PURE__*/_jsxDEV(TextField, {\n                      size: \"small\",\n                      type: \"number\",\n                      value: caviMetri[cavo.id_cavo] || '',\n                      onChange: e => {\n                        e.stopPropagation();\n                        handleMetriChange(cavo.id_cavo, e.target.value);\n                      },\n                      onClick: e => e.stopPropagation(),\n                      error: !!errors[cavo.id_cavo],\n                      helperText: errors[cavo.id_cavo] || warnings[cavo.id_cavo],\n                      FormHelperTextProps: {\n                        sx: {\n                          color: warnings[cavo.id_cavo] && !errors[cavo.id_cavo] ? 'warning.main' : 'error.main'\n                        }\n                      },\n                      InputProps: {\n                        endAdornment: warnings[cavo.id_cavo] && !errors[cavo.id_cavo] ? /*#__PURE__*/_jsxDEV(Tooltip, {\n                          title: warnings[cavo.id_cavo],\n                          children: /*#__PURE__*/_jsxDEV(WarningIcon, {\n                            color: \"warning\",\n                            fontSize: \"small\"\n                          }, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 544,\n                            columnNumber: 39\n                          }, this)\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 543,\n                          columnNumber: 37\n                        }, this) : null\n                      }\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 527,\n                      columnNumber: 31\n                    }, this) : 'N/A'\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 525,\n                    columnNumber: 27\n                  }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n                    children: /*#__PURE__*/_jsxDEV(Chip, {\n                      size: \"small\",\n                      label: cavo.stato_installazione || 'Da installare',\n                      color: getCableStateColor(cavo.stato_installazione)\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 554,\n                      columnNumber: 29\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 553,\n                    columnNumber: 27\n                  }, this)]\n                }, cavo.id_cavo, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 499,\n                  columnNumber: 25\n                }, this);\n              })\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 495,\n              columnNumber: 19\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 483,\n            columnNumber: 17\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 482,\n          columnNumber: 15\n        }, this), selectedCavi.length > 0 && /*#__PURE__*/_jsxDEV(Box, {\n          sx: {\n            mb: 2\n          },\n          children: [/*#__PURE__*/_jsxDEV(Typography, {\n            variant: \"subtitle1\",\n            gutterBottom: true,\n            children: [\"Riepilogo selezione (\", selectedCavi.length, \" cavi)\"]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 571,\n            columnNumber: 17\n          }, this), /*#__PURE__*/_jsxDEV(TableContainer, {\n            component: Paper,\n            children: /*#__PURE__*/_jsxDEV(Table, {\n              size: \"small\",\n              children: [/*#__PURE__*/_jsxDEV(TableHead, {\n                children: /*#__PURE__*/_jsxDEV(TableRow, {\n                  sx: {\n                    bgcolor: '#f5f5f5'\n                  },\n                  children: [/*#__PURE__*/_jsxDEV(TableCell, {\n                    children: \"ID Cavo\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 578,\n                    columnNumber: 25\n                  }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n                    children: \"Metri Posati\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 579,\n                    columnNumber: 25\n                  }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n                    children: \"Azioni\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 580,\n                    columnNumber: 25\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 577,\n                  columnNumber: 23\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 576,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(TableBody, {\n                children: [selectedCavi.map(cavo => /*#__PURE__*/_jsxDEV(TableRow, {\n                  children: [/*#__PURE__*/_jsxDEV(TableCell, {\n                    children: cavo.id_cavo\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 586,\n                    columnNumber: 27\n                  }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n                    children: /*#__PURE__*/_jsxDEV(TextField, {\n                      size: \"small\",\n                      type: \"number\",\n                      value: caviMetri[cavo.id_cavo] || '',\n                      onChange: e => handleMetriChange(cavo.id_cavo, e.target.value),\n                      error: !!errors[cavo.id_cavo],\n                      helperText: errors[cavo.id_cavo] || warnings[cavo.id_cavo],\n                      FormHelperTextProps: {\n                        sx: {\n                          color: warnings[cavo.id_cavo] && !errors[cavo.id_cavo] ? 'warning.main' : 'error.main'\n                        }\n                      }\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 588,\n                      columnNumber: 29\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 587,\n                    columnNumber: 27\n                  }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n                    children: /*#__PURE__*/_jsxDEV(IconButton, {\n                      size: \"small\",\n                      color: \"error\",\n                      onClick: () => handleCavoSelect(cavo),\n                      children: /*#__PURE__*/_jsxDEV(DeleteIcon, {\n                        fontSize: \"small\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 606,\n                        columnNumber: 31\n                      }, this)\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 601,\n                      columnNumber: 29\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 600,\n                    columnNumber: 27\n                  }, this)]\n                }, cavo.id_cavo, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 585,\n                  columnNumber: 25\n                }, this)), /*#__PURE__*/_jsxDEV(TableRow, {\n                  children: /*#__PURE__*/_jsxDEV(TableCell, {\n                    colSpan: 3,\n                    align: \"right\",\n                    children: [/*#__PURE__*/_jsxDEV(Typography, {\n                      variant: \"body2\",\n                      children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n                        children: \"Metri totali richiesti:\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 614,\n                        columnNumber: 29\n                      }, this), \" \", Object.values(caviMetri).reduce((sum, metri) => {\n                        const value = parseFloat(metri || 0);\n                        return isNaN(value) ? sum : sum + value;\n                      }, 0).toFixed(1), \" m\"]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 613,\n                      columnNumber: 27\n                    }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                      variant: \"body2\",\n                      children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n                        children: \"Metri residui bobina:\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 622,\n                        columnNumber: 29\n                      }, this), \" \", ((_bobina$metri_residui2 = bobina.metri_residui) === null || _bobina$metri_residui2 === void 0 ? void 0 : _bobina$metri_residui2.toFixed(1)) || '0', \" m\"]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 621,\n                      columnNumber: 27\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 612,\n                    columnNumber: 25\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 611,\n                  columnNumber: 23\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 583,\n                columnNumber: 21\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 575,\n              columnNumber: 19\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 574,\n            columnNumber: 17\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 570,\n          columnNumber: 15\n        }, this), Object.keys(warnings).length > 0 && /*#__PURE__*/_jsxDEV(Alert, {\n          severity: \"warning\",\n          sx: {\n            mb: 2\n          },\n          children: [/*#__PURE__*/_jsxDEV(Typography, {\n            variant: \"subtitle2\",\n            children: \"Attenzione:\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 635,\n            columnNumber: 17\n          }, this), /*#__PURE__*/_jsxDEV(\"ul\", {\n            children: Object.entries(warnings).map(([cavoId, warning]) => /*#__PURE__*/_jsxDEV(\"li\", {\n              children: [cavoId, \": \", warning]\n            }, cavoId, true, {\n              fileName: _jsxFileName,\n              lineNumber: 638,\n              columnNumber: 21\n            }, this))\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 636,\n            columnNumber: 17\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 634,\n          columnNumber: 15\n        }, this), /*#__PURE__*/_jsxDEV(Alert, {\n          severity: \"info\",\n          sx: {\n            mb: 2\n          },\n          children: \"Seleziona i cavi che vuoi associare a questa bobina e inserisci i metri posati per ciascuno. I metri posati verranno sottratti dai metri residui della bobina.\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 645,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 418,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(DialogActions, {\n      children: [/*#__PURE__*/_jsxDEV(Button, {\n        onClick: onClose,\n        disabled: saving,\n        children: \"Annulla\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 653,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Button, {\n        onClick: handleSave,\n        color: \"primary\",\n        variant: \"contained\",\n        disabled: saving || selectedCavi.length === 0 || Object.keys(errors).length > 0,\n        startIcon: saving ? /*#__PURE__*/_jsxDEV(CircularProgress, {\n          size: 20\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 661,\n          columnNumber: 31\n        }, this) : /*#__PURE__*/_jsxDEV(SaveIcon, {}, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 661,\n          columnNumber: 64\n        }, this),\n        children: \"Salva\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 656,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 652,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 414,\n    columnNumber: 5\n  }, this);\n};\n_s(QuickAddCablesDialog, \"AbIIuLGdIuO/w2uebXwSQvCbijs=\");\n_c = QuickAddCablesDialog;\nexport default QuickAddCablesDialog;\nvar _c;\n$RefreshReg$(_c, \"QuickAddCablesDialog\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "Dialog", "DialogTitle", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "DialogActions", "<PERSON><PERSON>", "Typography", "Box", "TextField", "Checkbox", "FormControlLabel", "Table", "TableBody", "TableCell", "TableContainer", "TableHead", "TableRow", "Paper", "CircularProgress", "<PERSON><PERSON>", "IconButton", "Chip", "<PERSON><PERSON><PERSON>", "Tabs", "Tab", "List", "ListItem", "ListItemText", "ListItemSecondaryAction", "Divider", "Card", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "Badge", "Add", "AddIcon", "Delete", "DeleteIcon", "Save", "SaveIcon", "Info", "InfoIcon", "Warning", "WarningIcon", "Search", "SearchIcon", "Cable", "CableIcon", "CheckCircle", "CheckCircleIcon", "Error", "ErrorIcon", "caviService", "determineCableState", "getCableStateColor", "isCableInstalled", "IncompatibleReelDialog", "jsxDEV", "_jsxDEV", "Fragment", "_Fragment", "isCompatible", "cavo", "bobina", "tipologia", "String", "sezione", "getBobinaNumber", "idBobina", "parts", "split", "length", "QuickAddCablesDialog", "open", "onClose", "cantiereId", "onSuccess", "onError", "_s", "_bobina$metri_residui", "_bobina$metri_residui2", "loading", "setLoading", "caviLoading", "setCaviLoading", "saving", "setSaving", "allCavi", "set<PERSON><PERSON><PERSON><PERSON>", "caviCompatibili", "setCaviCompatibili", "caviIncompatibili", "setCaviIncompatibili", "<PERSON><PERSON><PERSON>", "setSelectedCavi", "caviMetri", "setCaviMetri", "activeTab", "setActiveTab", "searchTerm", "setSearchTerm", "showIncompatibleDialog", "setShowIncompatibleDialog", "incompatibleSelection", "setIncompatibleSelection", "showOverDialog", "setShowOverDialog", "overDialogData", "setOverDialogData", "errors", "setErrors", "warnings", "setWarnings", "loadCavi", "resetDialogState", "caviData", "get<PERSON><PERSON>", "caviDisponibili", "filter", "modificato_manualmente", "compatibili", "incompatibili", "console", "log", "error", "message", "handleCompatibleCavoSelect", "prev", "isSelected", "some", "c", "id_cavo", "has<PERSON><PERSON>ri", "trim", "window", "confirm", "newSelected", "newCaviMetri", "prevErrors", "newErrors", "prevWarnings", "newWarnings", "handleIncompatibleCavoSelect", "handleUseIncompatibleReel", "_isIncompatible", "handleMetriChange", "cavoId", "value", "validate<PERSON>etri", "find", "isNaN", "parseFloat", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "metri_te<PERSON>ci", "metriTotaliRichiesti", "Object", "entries", "id", "_", "reduce", "sum", "metri", "metri_residui", "toFixed", "validateAllMetri", "<PERSON><PERSON><PERSON><PERSON>", "values", "handleSave", "id_bobina", "results", "metriGiàUtilizzati", "r", "forceOver", "result", "updateMetri<PERSON><PERSON><PERSON>", "push", "success", "map", "e", "join", "filteredCavi", "cavi", "toLowerCase", "includes", "ubicazione_partenza", "ubicazione_arrivo", "max<PERSON><PERSON><PERSON>", "fullWidth", "children", "numero_bobina", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "severity", "sx", "mb", "p", "bgcolor", "borderRadius", "variant", "gutterBottom", "display", "flexWrap", "gap", "color", "n_conduttori", "label", "stato_bobina", "size", "onChange", "target", "placeholder", "justifyContent", "my", "component", "padding", "hover", "selected", "onClick", "handleCavoSelect", "cursor", "checked", "stopPropagation", "title", "overflow", "textOverflow", "whiteSpace", "type", "helperText", "FormHelperTextProps", "InputProps", "endAdornment", "fontSize", "stato_installazione", "colSpan", "align", "keys", "warning", "disabled", "startIcon", "_c", "$RefreshReg$"], "sources": ["C:/CMS/webapp/frontend/src/components/cavi/QuickAddCablesDialog.js"], "sourcesContent": ["import React, { useState, useEffect } from 'react';\nimport {\n  Dialog,\n  DialogTitle,\n  DialogContent,\n  DialogActions,\n  Button,\n  Typography,\n  Box,\n  TextField,\n  Checkbox,\n  FormControlLabel,\n  Table,\n  TableBody,\n  TableCell,\n  TableContainer,\n  TableHead,\n  TableRow,\n  Paper,\n  CircularProgress,\n  Alert,\n  IconButton,\n  Chip,\n  Tooltip,\n  Tabs,\n  Tab,\n  List,\n  ListItem,\n  ListItemText,\n  ListItemSecondaryAction,\n  Divider,\n  Card,\n  CardContent,\n  CardHeader,\n  Badge\n} from '@mui/material';\nimport {\n  Add as AddIcon,\n  Delete as DeleteIcon,\n  Save as SaveIcon,\n  Info as InfoIcon,\n  Warning as WarningIcon,\n  Search as SearchIcon,\n  Cable as CableIcon,\n  CheckCircle as CheckCircleIcon,\n  Error as ErrorIcon\n} from '@mui/icons-material';\nimport caviService from '../../services/caviService';\nimport { determineCableState, getCableStateColor, isCableInstalled } from '../../utils/stateUtils';\nimport IncompatibleReelDialog from './IncompatibleReelDialog';\n\n// Utility per verificare compatibilità tra cavo e bobina\nconst isCompatible = (cavo, bobina) => {\n  return cavo.tipologia === bobina.tipologia &&\n         String(cavo.sezione) === String(bobina.sezione);\n};\n\n// Utility per ottenere il numero della bobina dall'ID\nconst getBobinaNumber = (idBobina) => {\n  if (!idBobina) return '';\n  const parts = idBobina.split('-');\n  return parts.length > 1 ? parts[1] : idBobina;\n};\n\n/**\n * Componente per aggiungere rapidamente più cavi a una bobina\n * Implementa sistema di doppia lista per cavi compatibili/incompatibili\n * con gestione intelligente delle incompatibilità e dialog moderni\n */\nconst QuickAddCablesDialog = ({ open, onClose, bobina, cantiereId, onSuccess, onError }) => {\n  // Stati per la gestione dei dati\n  const [loading, setLoading] = useState(false);\n  const [caviLoading, setCaviLoading] = useState(false);\n  const [saving, setSaving] = useState(false);\n\n  // Stati per i dati separati\n  const [allCavi, setAllCavi] = useState([]);\n  const [caviCompatibili, setCaviCompatibili] = useState([]);\n  const [caviIncompatibili, setCaviIncompatibili] = useState([]);\n  const [selectedCavi, setSelectedCavi] = useState([]);\n  const [caviMetri, setCaviMetri] = useState({});\n\n  // Stati per la UI\n  const [activeTab, setActiveTab] = useState(0);\n  const [searchTerm, setSearchTerm] = useState('');\n  const [showIncompatibleDialog, setShowIncompatibleDialog] = useState(false);\n  const [incompatibleSelection, setIncompatibleSelection] = useState(null);\n  const [showOverDialog, setShowOverDialog] = useState(false);\n  const [overDialogData, setOverDialogData] = useState(null);\n\n  // Stati per la validazione\n  const [errors, setErrors] = useState({});\n  const [warnings, setWarnings] = useState({});\n\n  // Carica i cavi disponibili quando il dialog viene aperto\n  useEffect(() => {\n    if (open && bobina && cantiereId) {\n      loadCavi();\n      // Reset stati quando si apre il dialog\n      resetDialogState();\n    }\n  }, [open, bobina, cantiereId]);\n\n  // Reset dello stato del dialog\n  const resetDialogState = () => {\n    setSelectedCavi([]);\n    setCaviMetri({});\n    setErrors({});\n    setWarnings({});\n    setSearchTerm('');\n    setActiveTab(0);\n    setShowIncompatibleDialog(false);\n    setIncompatibleSelection(null);\n    setShowOverDialog(false);\n    setOverDialogData(null);\n  };\n\n  // Funzione per caricare i cavi con sistema di doppia lista\n  const loadCavi = async () => {\n    try {\n      setCaviLoading(true);\n      const caviData = await caviService.getCavi(cantiereId);\n\n      // Filtra i cavi disponibili (non installati e non SPARE)\n      const caviDisponibili = caviData.filter(cavo =>\n        !isCableInstalled(cavo) &&\n        cavo.modificato_manualmente !== 3\n      );\n\n      // Separa cavi compatibili e incompatibili (CORREZIONE: rimosso n_conduttori)\n      const compatibili = caviDisponibili.filter(cavo => isCompatible(cavo, bobina));\n      const incompatibili = caviDisponibili.filter(cavo => !isCompatible(cavo, bobina));\n\n      setAllCavi(caviDisponibili);\n      setCaviCompatibili(compatibili);\n      setCaviIncompatibili(incompatibili);\n\n      console.log(`Cavi caricati: ${compatibili.length} compatibili, ${incompatibili.length} incompatibili`);\n    } catch (error) {\n      console.error('Errore nel caricamento dei cavi:', error);\n      onError('Errore nel caricamento dei cavi: ' + (error.message || 'Errore sconosciuto'));\n    } finally {\n      setCaviLoading(false);\n    }\n  };\n\n  // Gestisce la selezione di un cavo compatibile\n  const handleCompatibleCavoSelect = (cavo) => {\n    setSelectedCavi(prev => {\n      const isSelected = prev.some(c => c.id_cavo === cavo.id_cavo);\n\n      if (isSelected) {\n        // Conferma prima di rimuovere se ci sono metri inseriti\n        const hasMetri = caviMetri[cavo.id_cavo] && caviMetri[cavo.id_cavo].trim() !== '';\n        if (hasMetri) {\n          if (!window.confirm(`Rimuovere il cavo ${cavo.id_cavo} dalla selezione? I metri inseriti (${caviMetri[cavo.id_cavo]}m) andranno persi.`)) {\n            return prev;\n          }\n        }\n\n        // Rimuovi il cavo dalla selezione\n        const newSelected = prev.filter(c => c.id_cavo !== cavo.id_cavo);\n\n        // Rimuovi anche i metri associati\n        const newCaviMetri = { ...caviMetri };\n        delete newCaviMetri[cavo.id_cavo];\n        setCaviMetri(newCaviMetri);\n\n        // Rimuovi errori e warning\n        setErrors(prevErrors => {\n          const newErrors = { ...prevErrors };\n          delete newErrors[cavo.id_cavo];\n          return newErrors;\n        });\n        setWarnings(prevWarnings => {\n          const newWarnings = { ...prevWarnings };\n          delete newWarnings[cavo.id_cavo];\n          return newWarnings;\n        });\n\n        return newSelected;\n      } else {\n        // Aggiungi il cavo alla selezione\n        return [...prev, cavo];\n      }\n    });\n  };\n\n  // Gestisce la selezione di un cavo incompatibile\n  const handleIncompatibleCavoSelect = (cavo) => {\n    setIncompatibleSelection({ cavo, bobina });\n    setShowIncompatibleDialog(true);\n  };\n\n  // Gestisce l'uso di una bobina incompatibile\n  const handleUseIncompatibleReel = () => {\n    if (incompatibleSelection) {\n      const { cavo } = incompatibleSelection;\n\n      // Aggiungi il cavo alla selezione con flag di incompatibilità\n      setSelectedCavi(prev => [...prev, { ...cavo, _isIncompatible: true }]);\n\n      setShowIncompatibleDialog(false);\n      setIncompatibleSelection(null);\n\n      onSuccess?.(`Cavo incompatibile ${cavo.id_cavo} aggiunto alla selezione. Sarà utilizzato con force_over.`);\n    }\n  };\n\n  // Gestisce l'input dei metri posati per un cavo\n  const handleMetriChange = (cavoId, value) => {\n    // Aggiorna i metri per il cavo\n    setCaviMetri(prev => ({\n      ...prev,\n      [cavoId]: value\n    }));\n\n    // Valida il valore inserito in tempo reale\n    validateMetri(cavoId, value);\n  };\n\n  // Valida i metri inseriti per un cavo con feedback migliorato\n  const validateMetri = (cavoId, value) => {\n    const cavo = allCavi.find(c => c.id_cavo === cavoId);\n    if (!cavo) return;\n\n    // Resetta gli errori e gli avvisi per questo cavo\n    setErrors(prev => {\n      const newErrors = { ...prev };\n      delete newErrors[cavoId];\n      return newErrors;\n    });\n\n    setWarnings(prev => {\n      const newWarnings = { ...prev };\n      delete newWarnings[cavoId];\n      return newWarnings;\n    });\n\n    // Controllo input vuoto\n    if (!value || value.trim() === '') {\n      return true; // Non mostrare errore per input vuoto durante la digitazione\n    }\n\n    // Controllo formato numerico\n    if (isNaN(parseFloat(value)) || parseFloat(value) <= 0) {\n      setErrors(prev => ({\n        ...prev,\n        [cavoId]: 'Inserire un valore numerico positivo'\n      }));\n      return false;\n    }\n\n    const metriPosati = parseFloat(value);\n\n    // Controllo metri teorici cavo\n    if (cavo.metri_teorici && metriPosati > parseFloat(cavo.metri_teorici)) {\n      setWarnings(prev => ({\n        ...prev,\n        [cavoId]: `I metri posati (${metriPosati}m) superano i metri teorici del cavo (${cavo.metri_teorici}m)`\n      }));\n    }\n\n    // Controllo metri residui bobina con calcolo in tempo reale\n    const metriTotaliRichiesti = Object.entries(caviMetri)\n      .filter(([id, _]) => id !== cavoId) // Escludi il cavo corrente\n      .reduce((sum, [_, metri]) => sum + parseFloat(metri || 0), 0) + metriPosati;\n\n    if (metriTotaliRichiesti > bobina.metri_residui) {\n      setWarnings(prev => ({\n        ...prev,\n        [cavoId]: `Totale richiesto: ${metriTotaliRichiesti.toFixed(1)}m > Residui bobina: ${bobina.metri_residui.toFixed(1)}m (OVER)`\n      }));\n    }\n\n    return true;\n  };\n\n  // Valida tutti i metri inseriti\n  const validateAllMetri = () => {\n    let isValid = true;\n    const newErrors = {};\n    const newWarnings = {};\n\n    // Verifica che ci siano cavi selezionati\n    if (selectedCavi.length === 0) {\n      onError('Seleziona almeno un cavo');\n      return false;\n    }\n\n    // Verifica che tutti i cavi selezionati abbiano metri inseriti\n    for (const cavo of selectedCavi) {\n      const metri = caviMetri[cavo.id_cavo];\n\n      // Controllo input vuoto\n      if (!metri || metri.trim() === '') {\n        newErrors[cavo.id_cavo] = 'Inserire un valore per i metri posati';\n        isValid = false;\n        continue;\n      }\n\n      // Controllo formato numerico\n      if (isNaN(parseFloat(metri)) || parseFloat(metri) <= 0) {\n        newErrors[cavo.id_cavo] = 'Inserire un valore numerico positivo';\n        isValid = false;\n        continue;\n      }\n\n      const metriPosati = parseFloat(metri);\n\n      // Controllo metri teorici cavo\n      if (cavo.metri_teorici && metriPosati > parseFloat(cavo.metri_teorici)) {\n        newWarnings[cavo.id_cavo] = `I metri posati (${metriPosati}m) superano i metri teorici del cavo (${cavo.metri_teorici}m)`;\n        // Non mostrare popup di conferma, solo l'avviso nel form\n      }\n    }\n\n    // Verifica che i metri totali richiesti non superino i metri residui della bobina\n    const metriTotaliRichiesti = Object.values(caviMetri).reduce((sum, metri) => sum + parseFloat(metri || 0), 0);\n    if (metriTotaliRichiesti > bobina.metri_residui) {\n      // Questo è un avviso globale, non specifico per un cavo\n      if (!window.confirm(`ATTENZIONE: I metri totali richiesti (${metriTotaliRichiesti}m) superano i metri residui della bobina (${bobina.metri_residui}m).\\n\\nQuesto porterà la bobina in stato OVER.\\n\\nVuoi continuare?`)) {\n        isValid = false;\n      }\n    }\n\n    setErrors(newErrors);\n    setWarnings(newWarnings);\n    return isValid;\n  };\n\n  // Gestisce il salvataggio dei dati\n  const handleSave = async () => {\n    try {\n      // Validazione\n      if (!validateAllMetri()) {\n        return;\n      }\n\n      setSaving(true);\n\n      // Conferma finale\n      if (!window.confirm(`Confermi l'aggiornamento di ${selectedCavi.length} cavi con la bobina ${bobina.id_bobina}?`)) {\n        setSaving(false);\n        return;\n      }\n\n      // Aggiorna ogni cavo selezionato\n      const results = [];\n      let errors = [];\n\n      for (const cavo of selectedCavi) {\n        try {\n          const metriPosati = parseFloat(caviMetri[cavo.id_cavo]);\n\n          // Determina se è necessario forzare lo stato OVER della bobina\n          const metriGiàUtilizzati = results.reduce((sum, r) => sum + r.metriPosati, 0);\n          const forceOver = (metriGiàUtilizzati + metriPosati) > bobina.metri_residui;\n\n          // Aggiorna i metri posati del cavo\n          const result = await caviService.updateMetriPosati(\n            cantiereId,\n            cavo.id_cavo,\n            metriPosati,\n            bobina.id_bobina,\n            forceOver\n          );\n\n          results.push({\n            cavo: cavo.id_cavo,\n            metriPosati,\n            success: true\n          });\n        } catch (error) {\n          console.error(`Errore nell'aggiornamento del cavo ${cavo.id_cavo}:`, error);\n          errors.push({\n            cavo: cavo.id_cavo,\n            error: error.message || 'Errore sconosciuto'\n          });\n        }\n      }\n\n      // Gestione del risultato\n      if (errors.length === 0) {\n        // Tutti i cavi sono stati aggiornati con successo\n        onSuccess(`${results.length} cavi aggiornati con successo`);\n        onClose();\n      } else if (results.length > 0) {\n        // Alcuni cavi sono stati aggiornati, altri no\n        onSuccess(`${results.length} cavi aggiornati con successo, ${errors.length} falliti`);\n        onError(`Errori: ${errors.map(e => `${e.cavo}: ${e.error}`).join(', ')}`);\n        onClose();\n      } else {\n        // Nessun cavo è stato aggiornato\n        onError(`Nessun cavo aggiornato. Errori: ${errors.map(e => `${e.cavo}: ${e.error}`).join(', ')}`);\n      }\n    } catch (error) {\n      console.error('Errore durante il salvataggio:', error);\n      onError('Errore durante il salvataggio: ' + (error.message || 'Errore sconosciuto'));\n    } finally {\n      setSaving(false);\n    }\n  };\n\n  // Filtra i cavi in base al termine di ricerca\n  const filteredCavi = cavi.filter(cavo =>\n    cavo.id_cavo.toLowerCase().includes(searchTerm.toLowerCase()) ||\n    (cavo.tipologia && cavo.tipologia.toLowerCase().includes(searchTerm.toLowerCase())) ||\n    (cavo.ubicazione_partenza && cavo.ubicazione_partenza.toLowerCase().includes(searchTerm.toLowerCase())) ||\n    (cavo.ubicazione_arrivo && cavo.ubicazione_arrivo.toLowerCase().includes(searchTerm.toLowerCase()))\n  );\n\n  return (\n    <Dialog open={open} onClose={onClose} maxWidth=\"lg\" fullWidth>\n      <DialogTitle>\n        Aggiungi cavi alla bobina {bobina?.numero_bobina || ''}\n      </DialogTitle>\n      <DialogContent>\n        {!bobina ? (\n          <Alert severity=\"error\">Nessuna bobina selezionata</Alert>\n        ) : (\n          <>\n            {/* Informazioni sulla bobina */}\n            <Box sx={{ mb: 3, p: 2, bgcolor: '#f5f5f5', borderRadius: 1 }}>\n              <Typography variant=\"subtitle1\" gutterBottom>\n                Dettagli bobina\n              </Typography>\n              <Box sx={{ display: 'flex', flexWrap: 'wrap', gap: 3 }}>\n                <Box>\n                  <Typography variant=\"body2\" color=\"text.secondary\">ID Bobina</Typography>\n                  <Typography variant=\"body1\">{bobina.id_bobina}</Typography>\n                </Box>\n                <Box>\n                  <Typography variant=\"body2\" color=\"text.secondary\">Tipologia</Typography>\n                  <Typography variant=\"body1\">{bobina.tipologia || 'N/A'}</Typography>\n                </Box>\n                <Box>\n                  <Typography variant=\"body2\" color=\"text.secondary\">Conduttori</Typography>\n                  <Typography variant=\"body1\">{bobina.n_conduttori || 'N/A'} x {bobina.sezione || 'N/A'}</Typography>\n                </Box>\n                <Box>\n                  <Typography variant=\"body2\" color=\"text.secondary\">Metri residui</Typography>\n                  <Typography variant=\"body1\">{bobina.metri_residui?.toFixed(1) || '0'} m</Typography>\n                </Box>\n                <Box>\n                  <Typography variant=\"body2\" color=\"text.secondary\">Stato</Typography>\n                  <Chip\n                    label={bobina.stato_bobina || 'N/D'}\n                    size=\"small\"\n                    color={\n                      bobina.stato_bobina === 'Disponibile' ? 'success' :\n                      bobina.stato_bobina === 'In uso' ? 'primary' :\n                      bobina.stato_bobina === 'Over' ? 'error' :\n                      bobina.stato_bobina === 'Terminata' ? 'warning' : 'default'\n                    }\n                  />\n                </Box>\n              </Box>\n            </Box>\n\n            {/* Ricerca cavi */}\n            <TextField\n              fullWidth\n              label=\"Cerca cavi\"\n              variant=\"outlined\"\n              value={searchTerm}\n              onChange={(e) => setSearchTerm(e.target.value)}\n              placeholder=\"Cerca per ID, tipologia, ubicazione...\"\n              sx={{ mb: 2 }}\n            />\n\n            {/* Tabella cavi */}\n            {caviLoading ? (\n              <Box sx={{ display: 'flex', justifyContent: 'center', my: 4 }}>\n                <CircularProgress />\n              </Box>\n            ) : filteredCavi.length === 0 ? (\n              <Alert severity=\"info\">\n                Nessun cavo compatibile disponibile per questa bobina.\n              </Alert>\n            ) : (\n              <TableContainer component={Paper} sx={{ mb: 3 }}>\n                <Table size=\"small\">\n                  <TableHead>\n                    <TableRow sx={{ bgcolor: '#f5f5f5' }}>\n                      <TableCell padding=\"checkbox\"></TableCell>\n                      <TableCell>ID Cavo</TableCell>\n                      <TableCell>Tipologia</TableCell>\n                      <TableCell>Ubicazione</TableCell>\n                      <TableCell>Metri Teorici</TableCell>\n                      <TableCell>Metri Posati</TableCell>\n                      <TableCell>Stato</TableCell>\n                    </TableRow>\n                  </TableHead>\n                  <TableBody>\n                    {filteredCavi.map((cavo) => {\n                      const isSelected = selectedCavi.some(c => c.id_cavo === cavo.id_cavo);\n                      return (\n                        <TableRow\n                          key={cavo.id_cavo}\n                          hover\n                          selected={isSelected}\n                          onClick={() => handleCavoSelect(cavo)}\n                          sx={{ cursor: 'pointer' }}\n                        >\n                          <TableCell padding=\"checkbox\">\n                            <Checkbox\n                              checked={isSelected}\n                              onChange={(e) => {\n                                e.stopPropagation();\n                                handleCavoSelect(cavo);\n                              }}\n                            />\n                          </TableCell>\n                          <TableCell>{cavo.id_cavo}</TableCell>\n                          <TableCell>{cavo.tipologia || 'N/A'}</TableCell>\n                          <TableCell>\n                            <Tooltip title={`Da: ${cavo.ubicazione_partenza || 'N/A'} - A: ${cavo.ubicazione_arrivo || 'N/A'}`}>\n                              <Box sx={{ maxWidth: 150, overflow: 'hidden', textOverflow: 'ellipsis', whiteSpace: 'nowrap' }}>\n                                {cavo.ubicazione_partenza || 'N/A'} → {cavo.ubicazione_arrivo || 'N/A'}\n                              </Box>\n                            </Tooltip>\n                          </TableCell>\n                          <TableCell>{cavo.metri_teorici || 'N/A'}</TableCell>\n                          <TableCell>\n                            {isSelected ? (\n                              <TextField\n                                size=\"small\"\n                                type=\"number\"\n                                value={caviMetri[cavo.id_cavo] || ''}\n                                onChange={(e) => {\n                                  e.stopPropagation();\n                                  handleMetriChange(cavo.id_cavo, e.target.value);\n                                }}\n                                onClick={(e) => e.stopPropagation()}\n                                error={!!errors[cavo.id_cavo]}\n                                helperText={errors[cavo.id_cavo] || warnings[cavo.id_cavo]}\n                                FormHelperTextProps={{\n                                  sx: { color: warnings[cavo.id_cavo] && !errors[cavo.id_cavo] ? 'warning.main' : 'error.main' }\n                                }}\n                                InputProps={{\n                                  endAdornment: warnings[cavo.id_cavo] && !errors[cavo.id_cavo] ? (\n                                    <Tooltip title={warnings[cavo.id_cavo]}>\n                                      <WarningIcon color=\"warning\" fontSize=\"small\" />\n                                    </Tooltip>\n                                  ) : null\n                                }}\n                              />\n                            ) : (\n                              'N/A'\n                            )}\n                          </TableCell>\n                          <TableCell>\n                            <Chip\n                              size=\"small\"\n                              label={cavo.stato_installazione || 'Da installare'}\n                              color={getCableStateColor(cavo.stato_installazione)}\n                            />\n                          </TableCell>\n                        </TableRow>\n                      );\n                    })}\n                  </TableBody>\n                </Table>\n              </TableContainer>\n            )}\n\n            {/* Riepilogo selezione */}\n            {selectedCavi.length > 0 && (\n              <Box sx={{ mb: 2 }}>\n                <Typography variant=\"subtitle1\" gutterBottom>\n                  Riepilogo selezione ({selectedCavi.length} cavi)\n                </Typography>\n                <TableContainer component={Paper}>\n                  <Table size=\"small\">\n                    <TableHead>\n                      <TableRow sx={{ bgcolor: '#f5f5f5' }}>\n                        <TableCell>ID Cavo</TableCell>\n                        <TableCell>Metri Posati</TableCell>\n                        <TableCell>Azioni</TableCell>\n                      </TableRow>\n                    </TableHead>\n                    <TableBody>\n                      {selectedCavi.map((cavo) => (\n                        <TableRow key={cavo.id_cavo}>\n                          <TableCell>{cavo.id_cavo}</TableCell>\n                          <TableCell>\n                            <TextField\n                              size=\"small\"\n                              type=\"number\"\n                              value={caviMetri[cavo.id_cavo] || ''}\n                              onChange={(e) => handleMetriChange(cavo.id_cavo, e.target.value)}\n                              error={!!errors[cavo.id_cavo]}\n                              helperText={errors[cavo.id_cavo] || warnings[cavo.id_cavo]}\n                              FormHelperTextProps={{\n                                sx: { color: warnings[cavo.id_cavo] && !errors[cavo.id_cavo] ? 'warning.main' : 'error.main' }\n                              }}\n                            />\n                          </TableCell>\n                          <TableCell>\n                            <IconButton\n                              size=\"small\"\n                              color=\"error\"\n                              onClick={() => handleCavoSelect(cavo)}\n                            >\n                              <DeleteIcon fontSize=\"small\" />\n                            </IconButton>\n                          </TableCell>\n                        </TableRow>\n                      ))}\n                      <TableRow>\n                        <TableCell colSpan={3} align=\"right\">\n                          <Typography variant=\"body2\">\n                            <strong>Metri totali richiesti:</strong> {\n                              Object.values(caviMetri).reduce((sum, metri) => {\n                                const value = parseFloat(metri || 0);\n                                return isNaN(value) ? sum : sum + value;\n                              }, 0).toFixed(1)\n                            } m\n                          </Typography>\n                          <Typography variant=\"body2\">\n                            <strong>Metri residui bobina:</strong> {bobina.metri_residui?.toFixed(1) || '0'} m\n                          </Typography>\n                        </TableCell>\n                      </TableRow>\n                    </TableBody>\n                  </Table>\n                </TableContainer>\n              </Box>\n            )}\n\n            {/* Avvisi */}\n            {Object.keys(warnings).length > 0 && (\n              <Alert severity=\"warning\" sx={{ mb: 2 }}>\n                <Typography variant=\"subtitle2\">Attenzione:</Typography>\n                <ul>\n                  {Object.entries(warnings).map(([cavoId, warning]) => (\n                    <li key={cavoId}>{cavoId}: {warning}</li>\n                  ))}\n                </ul>\n              </Alert>\n            )}\n\n            {/* Istruzioni */}\n            <Alert severity=\"info\" sx={{ mb: 2 }}>\n              Seleziona i cavi che vuoi associare a questa bobina e inserisci i metri posati per ciascuno.\n              I metri posati verranno sottratti dai metri residui della bobina.\n            </Alert>\n          </>\n        )}\n      </DialogContent>\n      <DialogActions>\n        <Button onClick={onClose} disabled={saving}>\n          Annulla\n        </Button>\n        <Button\n          onClick={handleSave}\n          color=\"primary\"\n          variant=\"contained\"\n          disabled={saving || selectedCavi.length === 0 || Object.keys(errors).length > 0}\n          startIcon={saving ? <CircularProgress size={20} /> : <SaveIcon />}\n        >\n          Salva\n        </Button>\n      </DialogActions>\n    </Dialog>\n  );\n};\n\nexport default QuickAddCablesDialog;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAClD,SACEC,MAAM,EACNC,WAAW,EACXC,aAAa,EACbC,aAAa,EACbC,MAAM,EACNC,UAAU,EACVC,GAAG,EACHC,SAAS,EACTC,QAAQ,EACRC,gBAAgB,EAChBC,KAAK,EACLC,SAAS,EACTC,SAAS,EACTC,cAAc,EACdC,SAAS,EACTC,QAAQ,EACRC,KAAK,EACLC,gBAAgB,EAChBC,KAAK,EACLC,UAAU,EACVC,IAAI,EACJC,OAAO,EACPC,IAAI,EACJC,GAAG,EACHC,IAAI,EACJC,QAAQ,EACRC,YAAY,EACZC,uBAAuB,EACvBC,OAAO,EACPC,IAAI,EACJC,WAAW,EACXC,UAAU,EACVC,KAAK,QACA,eAAe;AACtB,SACEC,GAAG,IAAIC,OAAO,EACdC,MAAM,IAAIC,UAAU,EACpBC,IAAI,IAAIC,QAAQ,EAChBC,IAAI,IAAIC,QAAQ,EAChBC,OAAO,IAAIC,WAAW,EACtBC,MAAM,IAAIC,UAAU,EACpBC,KAAK,IAAIC,SAAS,EAClBC,WAAW,IAAIC,eAAe,EAC9BC,KAAK,IAAIC,SAAS,QACb,qBAAqB;AAC5B,OAAOC,WAAW,MAAM,4BAA4B;AACpD,SAASC,mBAAmB,EAAEC,kBAAkB,EAAEC,gBAAgB,QAAQ,wBAAwB;AAClG,OAAOC,sBAAsB,MAAM,0BAA0B;;AAE7D;AAAA,SAAAC,MAAA,IAAAC,OAAA,EAAAC,QAAA,IAAAC,SAAA;AACA,MAAMC,YAAY,GAAGA,CAACC,IAAI,EAAEC,MAAM,KAAK;EACrC,OAAOD,IAAI,CAACE,SAAS,KAAKD,MAAM,CAACC,SAAS,IACnCC,MAAM,CAACH,IAAI,CAACI,OAAO,CAAC,KAAKD,MAAM,CAACF,MAAM,CAACG,OAAO,CAAC;AACxD,CAAC;;AAED;AACA,MAAMC,eAAe,GAAIC,QAAQ,IAAK;EACpC,IAAI,CAACA,QAAQ,EAAE,OAAO,EAAE;EACxB,MAAMC,KAAK,GAAGD,QAAQ,CAACE,KAAK,CAAC,GAAG,CAAC;EACjC,OAAOD,KAAK,CAACE,MAAM,GAAG,CAAC,GAAGF,KAAK,CAAC,CAAC,CAAC,GAAGD,QAAQ;AAC/C,CAAC;;AAED;AACA;AACA;AACA;AACA;AACA,MAAMI,oBAAoB,GAAGA,CAAC;EAAEC,IAAI;EAAEC,OAAO;EAAEX,MAAM;EAAEY,UAAU;EAAEC,SAAS;EAAEC;AAAQ,CAAC,KAAK;EAAAC,EAAA;EAAA,IAAAC,qBAAA,EAAAC,sBAAA;EAC1F;EACA,MAAM,CAACC,OAAO,EAAEC,UAAU,CAAC,GAAGnF,QAAQ,CAAC,KAAK,CAAC;EAC7C,MAAM,CAACoF,WAAW,EAAEC,cAAc,CAAC,GAAGrF,QAAQ,CAAC,KAAK,CAAC;EACrD,MAAM,CAACsF,MAAM,EAAEC,SAAS,CAAC,GAAGvF,QAAQ,CAAC,KAAK,CAAC;;EAE3C;EACA,MAAM,CAACwF,OAAO,EAAEC,UAAU,CAAC,GAAGzF,QAAQ,CAAC,EAAE,CAAC;EAC1C,MAAM,CAAC0F,eAAe,EAAEC,kBAAkB,CAAC,GAAG3F,QAAQ,CAAC,EAAE,CAAC;EAC1D,MAAM,CAAC4F,iBAAiB,EAAEC,oBAAoB,CAAC,GAAG7F,QAAQ,CAAC,EAAE,CAAC;EAC9D,MAAM,CAAC8F,YAAY,EAAEC,eAAe,CAAC,GAAG/F,QAAQ,CAAC,EAAE,CAAC;EACpD,MAAM,CAACgG,SAAS,EAAEC,YAAY,CAAC,GAAGjG,QAAQ,CAAC,CAAC,CAAC,CAAC;;EAE9C;EACA,MAAM,CAACkG,SAAS,EAAEC,YAAY,CAAC,GAAGnG,QAAQ,CAAC,CAAC,CAAC;EAC7C,MAAM,CAACoG,UAAU,EAAEC,aAAa,CAAC,GAAGrG,QAAQ,CAAC,EAAE,CAAC;EAChD,MAAM,CAACsG,sBAAsB,EAAEC,yBAAyB,CAAC,GAAGvG,QAAQ,CAAC,KAAK,CAAC;EAC3E,MAAM,CAACwG,qBAAqB,EAAEC,wBAAwB,CAAC,GAAGzG,QAAQ,CAAC,IAAI,CAAC;EACxE,MAAM,CAAC0G,cAAc,EAAEC,iBAAiB,CAAC,GAAG3G,QAAQ,CAAC,KAAK,CAAC;EAC3D,MAAM,CAAC4G,cAAc,EAAEC,iBAAiB,CAAC,GAAG7G,QAAQ,CAAC,IAAI,CAAC;;EAE1D;EACA,MAAM,CAAC8G,MAAM,EAAEC,SAAS,CAAC,GAAG/G,QAAQ,CAAC,CAAC,CAAC,CAAC;EACxC,MAAM,CAACgH,QAAQ,EAAEC,WAAW,CAAC,GAAGjH,QAAQ,CAAC,CAAC,CAAC,CAAC;;EAE5C;EACAC,SAAS,CAAC,MAAM;IACd,IAAIyE,IAAI,IAAIV,MAAM,IAAIY,UAAU,EAAE;MAChCsC,QAAQ,CAAC,CAAC;MACV;MACAC,gBAAgB,CAAC,CAAC;IACpB;EACF,CAAC,EAAE,CAACzC,IAAI,EAAEV,MAAM,EAAEY,UAAU,CAAC,CAAC;;EAE9B;EACA,MAAMuC,gBAAgB,GAAGA,CAAA,KAAM;IAC7BpB,eAAe,CAAC,EAAE,CAAC;IACnBE,YAAY,CAAC,CAAC,CAAC,CAAC;IAChBc,SAAS,CAAC,CAAC,CAAC,CAAC;IACbE,WAAW,CAAC,CAAC,CAAC,CAAC;IACfZ,aAAa,CAAC,EAAE,CAAC;IACjBF,YAAY,CAAC,CAAC,CAAC;IACfI,yBAAyB,CAAC,KAAK,CAAC;IAChCE,wBAAwB,CAAC,IAAI,CAAC;IAC9BE,iBAAiB,CAAC,KAAK,CAAC;IACxBE,iBAAiB,CAAC,IAAI,CAAC;EACzB,CAAC;;EAED;EACA,MAAMK,QAAQ,GAAG,MAAAA,CAAA,KAAY;IAC3B,IAAI;MACF7B,cAAc,CAAC,IAAI,CAAC;MACpB,MAAM+B,QAAQ,GAAG,MAAM/D,WAAW,CAACgE,OAAO,CAACzC,UAAU,CAAC;;MAEtD;MACA,MAAM0C,eAAe,GAAGF,QAAQ,CAACG,MAAM,CAACxD,IAAI,IAC1C,CAACP,gBAAgB,CAACO,IAAI,CAAC,IACvBA,IAAI,CAACyD,sBAAsB,KAAK,CAClC,CAAC;;MAED;MACA,MAAMC,WAAW,GAAGH,eAAe,CAACC,MAAM,CAACxD,IAAI,IAAID,YAAY,CAACC,IAAI,EAAEC,MAAM,CAAC,CAAC;MAC9E,MAAM0D,aAAa,GAAGJ,eAAe,CAACC,MAAM,CAACxD,IAAI,IAAI,CAACD,YAAY,CAACC,IAAI,EAAEC,MAAM,CAAC,CAAC;MAEjFyB,UAAU,CAAC6B,eAAe,CAAC;MAC3B3B,kBAAkB,CAAC8B,WAAW,CAAC;MAC/B5B,oBAAoB,CAAC6B,aAAa,CAAC;MAEnCC,OAAO,CAACC,GAAG,CAAC,kBAAkBH,WAAW,CAACjD,MAAM,iBAAiBkD,aAAa,CAAClD,MAAM,gBAAgB,CAAC;IACxG,CAAC,CAAC,OAAOqD,KAAK,EAAE;MACdF,OAAO,CAACE,KAAK,CAAC,kCAAkC,EAAEA,KAAK,CAAC;MACxD/C,OAAO,CAAC,mCAAmC,IAAI+C,KAAK,CAACC,OAAO,IAAI,oBAAoB,CAAC,CAAC;IACxF,CAAC,SAAS;MACRzC,cAAc,CAAC,KAAK,CAAC;IACvB;EACF,CAAC;;EAED;EACA,MAAM0C,0BAA0B,GAAIhE,IAAI,IAAK;IAC3CgC,eAAe,CAACiC,IAAI,IAAI;MACtB,MAAMC,UAAU,GAAGD,IAAI,CAACE,IAAI,CAACC,CAAC,IAAIA,CAAC,CAACC,OAAO,KAAKrE,IAAI,CAACqE,OAAO,CAAC;MAE7D,IAAIH,UAAU,EAAE;QACd;QACA,MAAMI,QAAQ,GAAGrC,SAAS,CAACjC,IAAI,CAACqE,OAAO,CAAC,IAAIpC,SAAS,CAACjC,IAAI,CAACqE,OAAO,CAAC,CAACE,IAAI,CAAC,CAAC,KAAK,EAAE;QACjF,IAAID,QAAQ,EAAE;UACZ,IAAI,CAACE,MAAM,CAACC,OAAO,CAAC,qBAAqBzE,IAAI,CAACqE,OAAO,uCAAuCpC,SAAS,CAACjC,IAAI,CAACqE,OAAO,CAAC,oBAAoB,CAAC,EAAE;YACxI,OAAOJ,IAAI;UACb;QACF;;QAEA;QACA,MAAMS,WAAW,GAAGT,IAAI,CAACT,MAAM,CAACY,CAAC,IAAIA,CAAC,CAACC,OAAO,KAAKrE,IAAI,CAACqE,OAAO,CAAC;;QAEhE;QACA,MAAMM,YAAY,GAAG;UAAE,GAAG1C;QAAU,CAAC;QACrC,OAAO0C,YAAY,CAAC3E,IAAI,CAACqE,OAAO,CAAC;QACjCnC,YAAY,CAACyC,YAAY,CAAC;;QAE1B;QACA3B,SAAS,CAAC4B,UAAU,IAAI;UACtB,MAAMC,SAAS,GAAG;YAAE,GAAGD;UAAW,CAAC;UACnC,OAAOC,SAAS,CAAC7E,IAAI,CAACqE,OAAO,CAAC;UAC9B,OAAOQ,SAAS;QAClB,CAAC,CAAC;QACF3B,WAAW,CAAC4B,YAAY,IAAI;UAC1B,MAAMC,WAAW,GAAG;YAAE,GAAGD;UAAa,CAAC;UACvC,OAAOC,WAAW,CAAC/E,IAAI,CAACqE,OAAO,CAAC;UAChC,OAAOU,WAAW;QACpB,CAAC,CAAC;QAEF,OAAOL,WAAW;MACpB,CAAC,MAAM;QACL;QACA,OAAO,CAAC,GAAGT,IAAI,EAAEjE,IAAI,CAAC;MACxB;IACF,CAAC,CAAC;EACJ,CAAC;;EAED;EACA,MAAMgF,4BAA4B,GAAIhF,IAAI,IAAK;IAC7C0C,wBAAwB,CAAC;MAAE1C,IAAI;MAAEC;IAAO,CAAC,CAAC;IAC1CuC,yBAAyB,CAAC,IAAI,CAAC;EACjC,CAAC;;EAED;EACA,MAAMyC,yBAAyB,GAAGA,CAAA,KAAM;IACtC,IAAIxC,qBAAqB,EAAE;MACzB,MAAM;QAAEzC;MAAK,CAAC,GAAGyC,qBAAqB;;MAEtC;MACAT,eAAe,CAACiC,IAAI,IAAI,CAAC,GAAGA,IAAI,EAAE;QAAE,GAAGjE,IAAI;QAAEkF,eAAe,EAAE;MAAK,CAAC,CAAC,CAAC;MAEtE1C,yBAAyB,CAAC,KAAK,CAAC;MAChCE,wBAAwB,CAAC,IAAI,CAAC;MAE9B5B,SAAS,aAATA,SAAS,uBAATA,SAAS,CAAG,sBAAsBd,IAAI,CAACqE,OAAO,2DAA2D,CAAC;IAC5G;EACF,CAAC;;EAED;EACA,MAAMc,iBAAiB,GAAGA,CAACC,MAAM,EAAEC,KAAK,KAAK;IAC3C;IACAnD,YAAY,CAAC+B,IAAI,KAAK;MACpB,GAAGA,IAAI;MACP,CAACmB,MAAM,GAAGC;IACZ,CAAC,CAAC,CAAC;;IAEH;IACAC,aAAa,CAACF,MAAM,EAAEC,KAAK,CAAC;EAC9B,CAAC;;EAED;EACA,MAAMC,aAAa,GAAGA,CAACF,MAAM,EAAEC,KAAK,KAAK;IACvC,MAAMrF,IAAI,GAAGyB,OAAO,CAAC8D,IAAI,CAACnB,CAAC,IAAIA,CAAC,CAACC,OAAO,KAAKe,MAAM,CAAC;IACpD,IAAI,CAACpF,IAAI,EAAE;;IAEX;IACAgD,SAAS,CAACiB,IAAI,IAAI;MAChB,MAAMY,SAAS,GAAG;QAAE,GAAGZ;MAAK,CAAC;MAC7B,OAAOY,SAAS,CAACO,MAAM,CAAC;MACxB,OAAOP,SAAS;IAClB,CAAC,CAAC;IAEF3B,WAAW,CAACe,IAAI,IAAI;MAClB,MAAMc,WAAW,GAAG;QAAE,GAAGd;MAAK,CAAC;MAC/B,OAAOc,WAAW,CAACK,MAAM,CAAC;MAC1B,OAAOL,WAAW;IACpB,CAAC,CAAC;;IAEF;IACA,IAAI,CAACM,KAAK,IAAIA,KAAK,CAACd,IAAI,CAAC,CAAC,KAAK,EAAE,EAAE;MACjC,OAAO,IAAI,CAAC,CAAC;IACf;;IAEA;IACA,IAAIiB,KAAK,CAACC,UAAU,CAACJ,KAAK,CAAC,CAAC,IAAII,UAAU,CAACJ,KAAK,CAAC,IAAI,CAAC,EAAE;MACtDrC,SAAS,CAACiB,IAAI,KAAK;QACjB,GAAGA,IAAI;QACP,CAACmB,MAAM,GAAG;MACZ,CAAC,CAAC,CAAC;MACH,OAAO,KAAK;IACd;IAEA,MAAMM,WAAW,GAAGD,UAAU,CAACJ,KAAK,CAAC;;IAErC;IACA,IAAIrF,IAAI,CAAC2F,aAAa,IAAID,WAAW,GAAGD,UAAU,CAACzF,IAAI,CAAC2F,aAAa,CAAC,EAAE;MACtEzC,WAAW,CAACe,IAAI,KAAK;QACnB,GAAGA,IAAI;QACP,CAACmB,MAAM,GAAG,mBAAmBM,WAAW,yCAAyC1F,IAAI,CAAC2F,aAAa;MACrG,CAAC,CAAC,CAAC;IACL;;IAEA;IACA,MAAMC,oBAAoB,GAAGC,MAAM,CAACC,OAAO,CAAC7D,SAAS,CAAC,CACnDuB,MAAM,CAAC,CAAC,CAACuC,EAAE,EAAEC,CAAC,CAAC,KAAKD,EAAE,KAAKX,MAAM,CAAC,CAAC;IAAA,CACnCa,MAAM,CAAC,CAACC,GAAG,EAAE,CAACF,CAAC,EAAEG,KAAK,CAAC,KAAKD,GAAG,GAAGT,UAAU,CAACU,KAAK,IAAI,CAAC,CAAC,EAAE,CAAC,CAAC,GAAGT,WAAW;IAE7E,IAAIE,oBAAoB,GAAG3F,MAAM,CAACmG,aAAa,EAAE;MAC/ClD,WAAW,CAACe,IAAI,KAAK;QACnB,GAAGA,IAAI;QACP,CAACmB,MAAM,GAAG,qBAAqBQ,oBAAoB,CAACS,OAAO,CAAC,CAAC,CAAC,uBAAuBpG,MAAM,CAACmG,aAAa,CAACC,OAAO,CAAC,CAAC,CAAC;MACtH,CAAC,CAAC,CAAC;IACL;IAEA,OAAO,IAAI;EACb,CAAC;;EAED;EACA,MAAMC,gBAAgB,GAAGA,CAAA,KAAM;IAC7B,IAAIC,OAAO,GAAG,IAAI;IAClB,MAAM1B,SAAS,GAAG,CAAC,CAAC;IACpB,MAAME,WAAW,GAAG,CAAC,CAAC;;IAEtB;IACA,IAAIhD,YAAY,CAACtB,MAAM,KAAK,CAAC,EAAE;MAC7BM,OAAO,CAAC,0BAA0B,CAAC;MACnC,OAAO,KAAK;IACd;;IAEA;IACA,KAAK,MAAMf,IAAI,IAAI+B,YAAY,EAAE;MAC/B,MAAMoE,KAAK,GAAGlE,SAAS,CAACjC,IAAI,CAACqE,OAAO,CAAC;;MAErC;MACA,IAAI,CAAC8B,KAAK,IAAIA,KAAK,CAAC5B,IAAI,CAAC,CAAC,KAAK,EAAE,EAAE;QACjCM,SAAS,CAAC7E,IAAI,CAACqE,OAAO,CAAC,GAAG,uCAAuC;QACjEkC,OAAO,GAAG,KAAK;QACf;MACF;;MAEA;MACA,IAAIf,KAAK,CAACC,UAAU,CAACU,KAAK,CAAC,CAAC,IAAIV,UAAU,CAACU,KAAK,CAAC,IAAI,CAAC,EAAE;QACtDtB,SAAS,CAAC7E,IAAI,CAACqE,OAAO,CAAC,GAAG,sCAAsC;QAChEkC,OAAO,GAAG,KAAK;QACf;MACF;MAEA,MAAMb,WAAW,GAAGD,UAAU,CAACU,KAAK,CAAC;;MAErC;MACA,IAAInG,IAAI,CAAC2F,aAAa,IAAID,WAAW,GAAGD,UAAU,CAACzF,IAAI,CAAC2F,aAAa,CAAC,EAAE;QACtEZ,WAAW,CAAC/E,IAAI,CAACqE,OAAO,CAAC,GAAG,mBAAmBqB,WAAW,yCAAyC1F,IAAI,CAAC2F,aAAa,IAAI;QACzH;MACF;IACF;;IAEA;IACA,MAAMC,oBAAoB,GAAGC,MAAM,CAACW,MAAM,CAACvE,SAAS,CAAC,CAACgE,MAAM,CAAC,CAACC,GAAG,EAAEC,KAAK,KAAKD,GAAG,GAAGT,UAAU,CAACU,KAAK,IAAI,CAAC,CAAC,EAAE,CAAC,CAAC;IAC7G,IAAIP,oBAAoB,GAAG3F,MAAM,CAACmG,aAAa,EAAE;MAC/C;MACA,IAAI,CAAC5B,MAAM,CAACC,OAAO,CAAC,yCAAyCmB,oBAAoB,6CAA6C3F,MAAM,CAACmG,aAAa,oEAAoE,CAAC,EAAE;QACvNG,OAAO,GAAG,KAAK;MACjB;IACF;IAEAvD,SAAS,CAAC6B,SAAS,CAAC;IACpB3B,WAAW,CAAC6B,WAAW,CAAC;IACxB,OAAOwB,OAAO;EAChB,CAAC;;EAED;EACA,MAAME,UAAU,GAAG,MAAAA,CAAA,KAAY;IAC7B,IAAI;MACF;MACA,IAAI,CAACH,gBAAgB,CAAC,CAAC,EAAE;QACvB;MACF;MAEA9E,SAAS,CAAC,IAAI,CAAC;;MAEf;MACA,IAAI,CAACgD,MAAM,CAACC,OAAO,CAAC,+BAA+B1C,YAAY,CAACtB,MAAM,uBAAuBR,MAAM,CAACyG,SAAS,GAAG,CAAC,EAAE;QACjHlF,SAAS,CAAC,KAAK,CAAC;QAChB;MACF;;MAEA;MACA,MAAMmF,OAAO,GAAG,EAAE;MAClB,IAAI5D,MAAM,GAAG,EAAE;MAEf,KAAK,MAAM/C,IAAI,IAAI+B,YAAY,EAAE;QAC/B,IAAI;UACF,MAAM2D,WAAW,GAAGD,UAAU,CAACxD,SAAS,CAACjC,IAAI,CAACqE,OAAO,CAAC,CAAC;;UAEvD;UACA,MAAMuC,kBAAkB,GAAGD,OAAO,CAACV,MAAM,CAAC,CAACC,GAAG,EAAEW,CAAC,KAAKX,GAAG,GAAGW,CAAC,CAACnB,WAAW,EAAE,CAAC,CAAC;UAC7E,MAAMoB,SAAS,GAAIF,kBAAkB,GAAGlB,WAAW,GAAIzF,MAAM,CAACmG,aAAa;;UAE3E;UACA,MAAMW,MAAM,GAAG,MAAMzH,WAAW,CAAC0H,iBAAiB,CAChDnG,UAAU,EACVb,IAAI,CAACqE,OAAO,EACZqB,WAAW,EACXzF,MAAM,CAACyG,SAAS,EAChBI,SACF,CAAC;UAEDH,OAAO,CAACM,IAAI,CAAC;YACXjH,IAAI,EAAEA,IAAI,CAACqE,OAAO;YAClBqB,WAAW;YACXwB,OAAO,EAAE;UACX,CAAC,CAAC;QACJ,CAAC,CAAC,OAAOpD,KAAK,EAAE;UACdF,OAAO,CAACE,KAAK,CAAC,sCAAsC9D,IAAI,CAACqE,OAAO,GAAG,EAAEP,KAAK,CAAC;UAC3Ef,MAAM,CAACkE,IAAI,CAAC;YACVjH,IAAI,EAAEA,IAAI,CAACqE,OAAO;YAClBP,KAAK,EAAEA,KAAK,CAACC,OAAO,IAAI;UAC1B,CAAC,CAAC;QACJ;MACF;;MAEA;MACA,IAAIhB,MAAM,CAACtC,MAAM,KAAK,CAAC,EAAE;QACvB;QACAK,SAAS,CAAC,GAAG6F,OAAO,CAAClG,MAAM,+BAA+B,CAAC;QAC3DG,OAAO,CAAC,CAAC;MACX,CAAC,MAAM,IAAI+F,OAAO,CAAClG,MAAM,GAAG,CAAC,EAAE;QAC7B;QACAK,SAAS,CAAC,GAAG6F,OAAO,CAAClG,MAAM,kCAAkCsC,MAAM,CAACtC,MAAM,UAAU,CAAC;QACrFM,OAAO,CAAC,WAAWgC,MAAM,CAACoE,GAAG,CAACC,CAAC,IAAI,GAAGA,CAAC,CAACpH,IAAI,KAAKoH,CAAC,CAACtD,KAAK,EAAE,CAAC,CAACuD,IAAI,CAAC,IAAI,CAAC,EAAE,CAAC;QACzEzG,OAAO,CAAC,CAAC;MACX,CAAC,MAAM;QACL;QACAG,OAAO,CAAC,mCAAmCgC,MAAM,CAACoE,GAAG,CAACC,CAAC,IAAI,GAAGA,CAAC,CAACpH,IAAI,KAAKoH,CAAC,CAACtD,KAAK,EAAE,CAAC,CAACuD,IAAI,CAAC,IAAI,CAAC,EAAE,CAAC;MACnG;IACF,CAAC,CAAC,OAAOvD,KAAK,EAAE;MACdF,OAAO,CAACE,KAAK,CAAC,gCAAgC,EAAEA,KAAK,CAAC;MACtD/C,OAAO,CAAC,iCAAiC,IAAI+C,KAAK,CAACC,OAAO,IAAI,oBAAoB,CAAC,CAAC;IACtF,CAAC,SAAS;MACRvC,SAAS,CAAC,KAAK,CAAC;IAClB;EACF,CAAC;;EAED;EACA,MAAM8F,YAAY,GAAGC,IAAI,CAAC/D,MAAM,CAACxD,IAAI,IACnCA,IAAI,CAACqE,OAAO,CAACmD,WAAW,CAAC,CAAC,CAACC,QAAQ,CAACpF,UAAU,CAACmF,WAAW,CAAC,CAAC,CAAC,IAC5DxH,IAAI,CAACE,SAAS,IAAIF,IAAI,CAACE,SAAS,CAACsH,WAAW,CAAC,CAAC,CAACC,QAAQ,CAACpF,UAAU,CAACmF,WAAW,CAAC,CAAC,CAAE,IAClFxH,IAAI,CAAC0H,mBAAmB,IAAI1H,IAAI,CAAC0H,mBAAmB,CAACF,WAAW,CAAC,CAAC,CAACC,QAAQ,CAACpF,UAAU,CAACmF,WAAW,CAAC,CAAC,CAAE,IACtGxH,IAAI,CAAC2H,iBAAiB,IAAI3H,IAAI,CAAC2H,iBAAiB,CAACH,WAAW,CAAC,CAAC,CAACC,QAAQ,CAACpF,UAAU,CAACmF,WAAW,CAAC,CAAC,CACnG,CAAC;EAED,oBACE5H,OAAA,CAACzD,MAAM;IAACwE,IAAI,EAAEA,IAAK;IAACC,OAAO,EAAEA,OAAQ;IAACgH,QAAQ,EAAC,IAAI;IAACC,SAAS;IAAAC,QAAA,gBAC3DlI,OAAA,CAACxD,WAAW;MAAA0L,QAAA,GAAC,4BACe,EAAC,CAAA7H,MAAM,aAANA,MAAM,uBAANA,MAAM,CAAE8H,aAAa,KAAI,EAAE;IAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAC3C,CAAC,eACdvI,OAAA,CAACvD,aAAa;MAAAyL,QAAA,EACX,CAAC7H,MAAM,gBACNL,OAAA,CAACvC,KAAK;QAAC+K,QAAQ,EAAC,OAAO;QAAAN,QAAA,EAAC;MAA0B;QAAAE,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAO,CAAC,gBAE1DvI,OAAA,CAAAE,SAAA;QAAAgI,QAAA,gBAEElI,OAAA,CAACnD,GAAG;UAAC4L,EAAE,EAAE;YAAEC,EAAE,EAAE,CAAC;YAAEC,CAAC,EAAE,CAAC;YAAEC,OAAO,EAAE,SAAS;YAAEC,YAAY,EAAE;UAAE,CAAE;UAAAX,QAAA,gBAC5DlI,OAAA,CAACpD,UAAU;YAACkM,OAAO,EAAC,WAAW;YAACC,YAAY;YAAAb,QAAA,EAAC;UAE7C;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAY,CAAC,eACbvI,OAAA,CAACnD,GAAG;YAAC4L,EAAE,EAAE;cAAEO,OAAO,EAAE,MAAM;cAAEC,QAAQ,EAAE,MAAM;cAAEC,GAAG,EAAE;YAAE,CAAE;YAAAhB,QAAA,gBACrDlI,OAAA,CAACnD,GAAG;cAAAqL,QAAA,gBACFlI,OAAA,CAACpD,UAAU;gBAACkM,OAAO,EAAC,OAAO;gBAACK,KAAK,EAAC,gBAAgB;gBAAAjB,QAAA,EAAC;cAAS;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAY,CAAC,eACzEvI,OAAA,CAACpD,UAAU;gBAACkM,OAAO,EAAC,OAAO;gBAAAZ,QAAA,EAAE7H,MAAM,CAACyG;cAAS;gBAAAsB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAa,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACxD,CAAC,eACNvI,OAAA,CAACnD,GAAG;cAAAqL,QAAA,gBACFlI,OAAA,CAACpD,UAAU;gBAACkM,OAAO,EAAC,OAAO;gBAACK,KAAK,EAAC,gBAAgB;gBAAAjB,QAAA,EAAC;cAAS;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAY,CAAC,eACzEvI,OAAA,CAACpD,UAAU;gBAACkM,OAAO,EAAC,OAAO;gBAAAZ,QAAA,EAAE7H,MAAM,CAACC,SAAS,IAAI;cAAK;gBAAA8H,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAa,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACjE,CAAC,eACNvI,OAAA,CAACnD,GAAG;cAAAqL,QAAA,gBACFlI,OAAA,CAACpD,UAAU;gBAACkM,OAAO,EAAC,OAAO;gBAACK,KAAK,EAAC,gBAAgB;gBAAAjB,QAAA,EAAC;cAAU;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAY,CAAC,eAC1EvI,OAAA,CAACpD,UAAU;gBAACkM,OAAO,EAAC,OAAO;gBAAAZ,QAAA,GAAE7H,MAAM,CAAC+I,YAAY,IAAI,KAAK,EAAC,KAAG,EAAC/I,MAAM,CAACG,OAAO,IAAI,KAAK;cAAA;gBAAA4H,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAa,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAChG,CAAC,eACNvI,OAAA,CAACnD,GAAG;cAAAqL,QAAA,gBACFlI,OAAA,CAACpD,UAAU;gBAACkM,OAAO,EAAC,OAAO;gBAACK,KAAK,EAAC,gBAAgB;gBAAAjB,QAAA,EAAC;cAAa;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAY,CAAC,eAC7EvI,OAAA,CAACpD,UAAU;gBAACkM,OAAO,EAAC,OAAO;gBAAAZ,QAAA,GAAE,EAAA7G,qBAAA,GAAAhB,MAAM,CAACmG,aAAa,cAAAnF,qBAAA,uBAApBA,qBAAA,CAAsBoF,OAAO,CAAC,CAAC,CAAC,KAAI,GAAG,EAAC,IAAE;cAAA;gBAAA2B,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAY,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACjF,CAAC,eACNvI,OAAA,CAACnD,GAAG;cAAAqL,QAAA,gBACFlI,OAAA,CAACpD,UAAU;gBAACkM,OAAO,EAAC,OAAO;gBAACK,KAAK,EAAC,gBAAgB;gBAAAjB,QAAA,EAAC;cAAK;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAY,CAAC,eACrEvI,OAAA,CAACrC,IAAI;gBACH0L,KAAK,EAAEhJ,MAAM,CAACiJ,YAAY,IAAI,KAAM;gBACpCC,IAAI,EAAC,OAAO;gBACZJ,KAAK,EACH9I,MAAM,CAACiJ,YAAY,KAAK,aAAa,GAAG,SAAS,GACjDjJ,MAAM,CAACiJ,YAAY,KAAK,QAAQ,GAAG,SAAS,GAC5CjJ,MAAM,CAACiJ,YAAY,KAAK,MAAM,GAAG,OAAO,GACxCjJ,MAAM,CAACiJ,YAAY,KAAK,WAAW,GAAG,SAAS,GAAG;cACnD;gBAAAlB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACF,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACC,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,eAGNvI,OAAA,CAAClD,SAAS;UACRmL,SAAS;UACToB,KAAK,EAAC,YAAY;UAClBP,OAAO,EAAC,UAAU;UAClBrD,KAAK,EAAEhD,UAAW;UAClB+G,QAAQ,EAAGhC,CAAC,IAAK9E,aAAa,CAAC8E,CAAC,CAACiC,MAAM,CAAChE,KAAK,CAAE;UAC/CiE,WAAW,EAAC,wCAAwC;UACpDjB,EAAE,EAAE;YAAEC,EAAE,EAAE;UAAE;QAAE;UAAAN,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACf,CAAC,EAGD9G,WAAW,gBACVzB,OAAA,CAACnD,GAAG;UAAC4L,EAAE,EAAE;YAAEO,OAAO,EAAE,MAAM;YAAEW,cAAc,EAAE,QAAQ;YAAEC,EAAE,EAAE;UAAE,CAAE;UAAA1B,QAAA,eAC5DlI,OAAA,CAACxC,gBAAgB;YAAA4K,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACjB,CAAC,GACJb,YAAY,CAAC7G,MAAM,KAAK,CAAC,gBAC3Bb,OAAA,CAACvC,KAAK;UAAC+K,QAAQ,EAAC,MAAM;UAAAN,QAAA,EAAC;QAEvB;UAAAE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAO,CAAC,gBAERvI,OAAA,CAAC5C,cAAc;UAACyM,SAAS,EAAEtM,KAAM;UAACkL,EAAE,EAAE;YAAEC,EAAE,EAAE;UAAE,CAAE;UAAAR,QAAA,eAC9ClI,OAAA,CAAC/C,KAAK;YAACsM,IAAI,EAAC,OAAO;YAAArB,QAAA,gBACjBlI,OAAA,CAAC3C,SAAS;cAAA6K,QAAA,eACRlI,OAAA,CAAC1C,QAAQ;gBAACmL,EAAE,EAAE;kBAAEG,OAAO,EAAE;gBAAU,CAAE;gBAAAV,QAAA,gBACnClI,OAAA,CAAC7C,SAAS;kBAAC2M,OAAO,EAAC;gBAAU;kBAAA1B,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAY,CAAC,eAC1CvI,OAAA,CAAC7C,SAAS;kBAAA+K,QAAA,EAAC;gBAAO;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAW,CAAC,eAC9BvI,OAAA,CAAC7C,SAAS;kBAAA+K,QAAA,EAAC;gBAAS;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAW,CAAC,eAChCvI,OAAA,CAAC7C,SAAS;kBAAA+K,QAAA,EAAC;gBAAU;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAW,CAAC,eACjCvI,OAAA,CAAC7C,SAAS;kBAAA+K,QAAA,EAAC;gBAAa;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAW,CAAC,eACpCvI,OAAA,CAAC7C,SAAS;kBAAA+K,QAAA,EAAC;gBAAY;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAW,CAAC,eACnCvI,OAAA,CAAC7C,SAAS;kBAAA+K,QAAA,EAAC;gBAAK;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAW,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACpB;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACF,CAAC,eACZvI,OAAA,CAAC9C,SAAS;cAAAgL,QAAA,EACPR,YAAY,CAACH,GAAG,CAAEnH,IAAI,IAAK;gBAC1B,MAAMkE,UAAU,GAAGnC,YAAY,CAACoC,IAAI,CAACC,CAAC,IAAIA,CAAC,CAACC,OAAO,KAAKrE,IAAI,CAACqE,OAAO,CAAC;gBACrE,oBACEzE,OAAA,CAAC1C,QAAQ;kBAEPyM,KAAK;kBACLC,QAAQ,EAAE1F,UAAW;kBACrB2F,OAAO,EAAEA,CAAA,KAAMC,gBAAgB,CAAC9J,IAAI,CAAE;kBACtCqI,EAAE,EAAE;oBAAE0B,MAAM,EAAE;kBAAU,CAAE;kBAAAjC,QAAA,gBAE1BlI,OAAA,CAAC7C,SAAS;oBAAC2M,OAAO,EAAC,UAAU;oBAAA5B,QAAA,eAC3BlI,OAAA,CAACjD,QAAQ;sBACPqN,OAAO,EAAE9F,UAAW;sBACpBkF,QAAQ,EAAGhC,CAAC,IAAK;wBACfA,CAAC,CAAC6C,eAAe,CAAC,CAAC;wBACnBH,gBAAgB,CAAC9J,IAAI,CAAC;sBACxB;oBAAE;sBAAAgI,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACH;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACO,CAAC,eACZvI,OAAA,CAAC7C,SAAS;oBAAA+K,QAAA,EAAE9H,IAAI,CAACqE;kBAAO;oBAAA2D,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAY,CAAC,eACrCvI,OAAA,CAAC7C,SAAS;oBAAA+K,QAAA,EAAE9H,IAAI,CAACE,SAAS,IAAI;kBAAK;oBAAA8H,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAY,CAAC,eAChDvI,OAAA,CAAC7C,SAAS;oBAAA+K,QAAA,eACRlI,OAAA,CAACpC,OAAO;sBAAC0M,KAAK,EAAE,OAAOlK,IAAI,CAAC0H,mBAAmB,IAAI,KAAK,SAAS1H,IAAI,CAAC2H,iBAAiB,IAAI,KAAK,EAAG;sBAAAG,QAAA,eACjGlI,OAAA,CAACnD,GAAG;wBAAC4L,EAAE,EAAE;0BAAET,QAAQ,EAAE,GAAG;0BAAEuC,QAAQ,EAAE,QAAQ;0BAAEC,YAAY,EAAE,UAAU;0BAAEC,UAAU,EAAE;wBAAS,CAAE;wBAAAvC,QAAA,GAC5F9H,IAAI,CAAC0H,mBAAmB,IAAI,KAAK,EAAC,UAAG,EAAC1H,IAAI,CAAC2H,iBAAiB,IAAI,KAAK;sBAAA;wBAAAK,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACnE;oBAAC;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACC;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACD,CAAC,eACZvI,OAAA,CAAC7C,SAAS;oBAAA+K,QAAA,EAAE9H,IAAI,CAAC2F,aAAa,IAAI;kBAAK;oBAAAqC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAY,CAAC,eACpDvI,OAAA,CAAC7C,SAAS;oBAAA+K,QAAA,EACP5D,UAAU,gBACTtE,OAAA,CAAClD,SAAS;sBACRyM,IAAI,EAAC,OAAO;sBACZmB,IAAI,EAAC,QAAQ;sBACbjF,KAAK,EAAEpD,SAAS,CAACjC,IAAI,CAACqE,OAAO,CAAC,IAAI,EAAG;sBACrC+E,QAAQ,EAAGhC,CAAC,IAAK;wBACfA,CAAC,CAAC6C,eAAe,CAAC,CAAC;wBACnB9E,iBAAiB,CAACnF,IAAI,CAACqE,OAAO,EAAE+C,CAAC,CAACiC,MAAM,CAAChE,KAAK,CAAC;sBACjD,CAAE;sBACFwE,OAAO,EAAGzC,CAAC,IAAKA,CAAC,CAAC6C,eAAe,CAAC,CAAE;sBACpCnG,KAAK,EAAE,CAAC,CAACf,MAAM,CAAC/C,IAAI,CAACqE,OAAO,CAAE;sBAC9BkG,UAAU,EAAExH,MAAM,CAAC/C,IAAI,CAACqE,OAAO,CAAC,IAAIpB,QAAQ,CAACjD,IAAI,CAACqE,OAAO,CAAE;sBAC3DmG,mBAAmB,EAAE;wBACnBnC,EAAE,EAAE;0BAAEU,KAAK,EAAE9F,QAAQ,CAACjD,IAAI,CAACqE,OAAO,CAAC,IAAI,CAACtB,MAAM,CAAC/C,IAAI,CAACqE,OAAO,CAAC,GAAG,cAAc,GAAG;wBAAa;sBAC/F,CAAE;sBACFoG,UAAU,EAAE;wBACVC,YAAY,EAAEzH,QAAQ,CAACjD,IAAI,CAACqE,OAAO,CAAC,IAAI,CAACtB,MAAM,CAAC/C,IAAI,CAACqE,OAAO,CAAC,gBAC3DzE,OAAA,CAACpC,OAAO;0BAAC0M,KAAK,EAAEjH,QAAQ,CAACjD,IAAI,CAACqE,OAAO,CAAE;0BAAAyD,QAAA,eACrClI,OAAA,CAACf,WAAW;4BAACkK,KAAK,EAAC,SAAS;4BAAC4B,QAAQ,EAAC;0BAAO;4BAAA3C,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OAAE;wBAAC;0BAAAH,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OACzC,CAAC,GACR;sBACN;oBAAE;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACH,CAAC,GAEF;kBACD;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACQ,CAAC,eACZvI,OAAA,CAAC7C,SAAS;oBAAA+K,QAAA,eACRlI,OAAA,CAACrC,IAAI;sBACH4L,IAAI,EAAC,OAAO;sBACZF,KAAK,EAAEjJ,IAAI,CAAC4K,mBAAmB,IAAI,eAAgB;sBACnD7B,KAAK,EAAEvJ,kBAAkB,CAACQ,IAAI,CAAC4K,mBAAmB;oBAAE;sBAAA5C,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACrD;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACO,CAAC;gBAAA,GA3DPnI,IAAI,CAACqE,OAAO;kBAAA2D,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OA4DT,CAAC;cAEf,CAAC;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACO,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACP;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACM,CACjB,EAGApG,YAAY,CAACtB,MAAM,GAAG,CAAC,iBACtBb,OAAA,CAACnD,GAAG;UAAC4L,EAAE,EAAE;YAAEC,EAAE,EAAE;UAAE,CAAE;UAAAR,QAAA,gBACjBlI,OAAA,CAACpD,UAAU;YAACkM,OAAO,EAAC,WAAW;YAACC,YAAY;YAAAb,QAAA,GAAC,uBACtB,EAAC/F,YAAY,CAACtB,MAAM,EAAC,QAC5C;UAAA;YAAAuH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAY,CAAC,eACbvI,OAAA,CAAC5C,cAAc;YAACyM,SAAS,EAAEtM,KAAM;YAAA2K,QAAA,eAC/BlI,OAAA,CAAC/C,KAAK;cAACsM,IAAI,EAAC,OAAO;cAAArB,QAAA,gBACjBlI,OAAA,CAAC3C,SAAS;gBAAA6K,QAAA,eACRlI,OAAA,CAAC1C,QAAQ;kBAACmL,EAAE,EAAE;oBAAEG,OAAO,EAAE;kBAAU,CAAE;kBAAAV,QAAA,gBACnClI,OAAA,CAAC7C,SAAS;oBAAA+K,QAAA,EAAC;kBAAO;oBAAAE,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAW,CAAC,eAC9BvI,OAAA,CAAC7C,SAAS;oBAAA+K,QAAA,EAAC;kBAAY;oBAAAE,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAW,CAAC,eACnCvI,OAAA,CAAC7C,SAAS;oBAAA+K,QAAA,EAAC;kBAAM;oBAAAE,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAW,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACrB;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACF,CAAC,eACZvI,OAAA,CAAC9C,SAAS;gBAAAgL,QAAA,GACP/F,YAAY,CAACoF,GAAG,CAAEnH,IAAI,iBACrBJ,OAAA,CAAC1C,QAAQ;kBAAA4K,QAAA,gBACPlI,OAAA,CAAC7C,SAAS;oBAAA+K,QAAA,EAAE9H,IAAI,CAACqE;kBAAO;oBAAA2D,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAY,CAAC,eACrCvI,OAAA,CAAC7C,SAAS;oBAAA+K,QAAA,eACRlI,OAAA,CAAClD,SAAS;sBACRyM,IAAI,EAAC,OAAO;sBACZmB,IAAI,EAAC,QAAQ;sBACbjF,KAAK,EAAEpD,SAAS,CAACjC,IAAI,CAACqE,OAAO,CAAC,IAAI,EAAG;sBACrC+E,QAAQ,EAAGhC,CAAC,IAAKjC,iBAAiB,CAACnF,IAAI,CAACqE,OAAO,EAAE+C,CAAC,CAACiC,MAAM,CAAChE,KAAK,CAAE;sBACjEvB,KAAK,EAAE,CAAC,CAACf,MAAM,CAAC/C,IAAI,CAACqE,OAAO,CAAE;sBAC9BkG,UAAU,EAAExH,MAAM,CAAC/C,IAAI,CAACqE,OAAO,CAAC,IAAIpB,QAAQ,CAACjD,IAAI,CAACqE,OAAO,CAAE;sBAC3DmG,mBAAmB,EAAE;wBACnBnC,EAAE,EAAE;0BAAEU,KAAK,EAAE9F,QAAQ,CAACjD,IAAI,CAACqE,OAAO,CAAC,IAAI,CAACtB,MAAM,CAAC/C,IAAI,CAACqE,OAAO,CAAC,GAAG,cAAc,GAAG;wBAAa;sBAC/F;oBAAE;sBAAA2D,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACH;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACO,CAAC,eACZvI,OAAA,CAAC7C,SAAS;oBAAA+K,QAAA,eACRlI,OAAA,CAACtC,UAAU;sBACT6L,IAAI,EAAC,OAAO;sBACZJ,KAAK,EAAC,OAAO;sBACbc,OAAO,EAAEA,CAAA,KAAMC,gBAAgB,CAAC9J,IAAI,CAAE;sBAAA8H,QAAA,eAEtClI,OAAA,CAACrB,UAAU;wBAACoM,QAAQ,EAAC;sBAAO;wBAAA3C,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAE;oBAAC;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACrB;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACJ,CAAC;gBAAA,GAvBCnI,IAAI,CAACqE,OAAO;kBAAA2D,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAwBjB,CACX,CAAC,eACFvI,OAAA,CAAC1C,QAAQ;kBAAA4K,QAAA,eACPlI,OAAA,CAAC7C,SAAS;oBAAC8N,OAAO,EAAE,CAAE;oBAACC,KAAK,EAAC,OAAO;oBAAAhD,QAAA,gBAClClI,OAAA,CAACpD,UAAU;sBAACkM,OAAO,EAAC,OAAO;sBAAAZ,QAAA,gBACzBlI,OAAA;wBAAAkI,QAAA,EAAQ;sBAAuB;wBAAAE,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAQ,CAAC,KAAC,EACvCtC,MAAM,CAACW,MAAM,CAACvE,SAAS,CAAC,CAACgE,MAAM,CAAC,CAACC,GAAG,EAAEC,KAAK,KAAK;wBAC9C,MAAMd,KAAK,GAAGI,UAAU,CAACU,KAAK,IAAI,CAAC,CAAC;wBACpC,OAAOX,KAAK,CAACH,KAAK,CAAC,GAAGa,GAAG,GAAGA,GAAG,GAAGb,KAAK;sBACzC,CAAC,EAAE,CAAC,CAAC,CAACgB,OAAO,CAAC,CAAC,CAAC,EACjB,IACH;oBAAA;sBAAA2B,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAY,CAAC,eACbvI,OAAA,CAACpD,UAAU;sBAACkM,OAAO,EAAC,OAAO;sBAAAZ,QAAA,gBACzBlI,OAAA;wBAAAkI,QAAA,EAAQ;sBAAqB;wBAAAE,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAQ,CAAC,KAAC,EAAC,EAAAjH,sBAAA,GAAAjB,MAAM,CAACmG,aAAa,cAAAlF,sBAAA,uBAApBA,sBAAA,CAAsBmF,OAAO,CAAC,CAAC,CAAC,KAAI,GAAG,EAAC,IAClF;oBAAA;sBAAA2B,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAY,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACJ;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACJ,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACF,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACP;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACM,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACd,CACN,EAGAtC,MAAM,CAACkF,IAAI,CAAC9H,QAAQ,CAAC,CAACxC,MAAM,GAAG,CAAC,iBAC/Bb,OAAA,CAACvC,KAAK;UAAC+K,QAAQ,EAAC,SAAS;UAACC,EAAE,EAAE;YAAEC,EAAE,EAAE;UAAE,CAAE;UAAAR,QAAA,gBACtClI,OAAA,CAACpD,UAAU;YAACkM,OAAO,EAAC,WAAW;YAAAZ,QAAA,EAAC;UAAW;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAY,CAAC,eACxDvI,OAAA;YAAAkI,QAAA,EACGjC,MAAM,CAACC,OAAO,CAAC7C,QAAQ,CAAC,CAACkE,GAAG,CAAC,CAAC,CAAC/B,MAAM,EAAE4F,OAAO,CAAC,kBAC9CpL,OAAA;cAAAkI,QAAA,GAAkB1C,MAAM,EAAC,IAAE,EAAC4F,OAAO;YAAA,GAA1B5F,MAAM;cAAA4C,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAyB,CACzC;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACA,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACA,CACR,eAGDvI,OAAA,CAACvC,KAAK;UAAC+K,QAAQ,EAAC,MAAM;UAACC,EAAE,EAAE;YAAEC,EAAE,EAAE;UAAE,CAAE;UAAAR,QAAA,EAAC;QAGtC;UAAAE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAO,CAAC;MAAA,eACR;IACH;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACY,CAAC,eAChBvI,OAAA,CAACtD,aAAa;MAAAwL,QAAA,gBACZlI,OAAA,CAACrD,MAAM;QAACsN,OAAO,EAAEjJ,OAAQ;QAACqK,QAAQ,EAAE1J,MAAO;QAAAuG,QAAA,EAAC;MAE5C;QAAAE,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAQ,CAAC,eACTvI,OAAA,CAACrD,MAAM;QACLsN,OAAO,EAAEpD,UAAW;QACpBsC,KAAK,EAAC,SAAS;QACfL,OAAO,EAAC,WAAW;QACnBuC,QAAQ,EAAE1J,MAAM,IAAIQ,YAAY,CAACtB,MAAM,KAAK,CAAC,IAAIoF,MAAM,CAACkF,IAAI,CAAChI,MAAM,CAAC,CAACtC,MAAM,GAAG,CAAE;QAChFyK,SAAS,EAAE3J,MAAM,gBAAG3B,OAAA,CAACxC,gBAAgB;UAAC+L,IAAI,EAAE;QAAG;UAAAnB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,gBAAGvI,OAAA,CAACnB,QAAQ;UAAAuJ,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAE;QAAAL,QAAA,EACnE;MAED;QAAAE,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAQ,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACI,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACV,CAAC;AAEb,CAAC;AAACnH,EAAA,CAtlBIN,oBAAoB;AAAAyK,EAAA,GAApBzK,oBAAoB;AAwlB1B,eAAeA,oBAAoB;AAAC,IAAAyK,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}