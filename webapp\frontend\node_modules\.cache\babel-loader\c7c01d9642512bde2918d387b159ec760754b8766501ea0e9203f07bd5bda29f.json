{"ast": null, "code": "import { startOfDay } from \"./startOfDay.js\";\n\n/**\n * The {@link startOfToday} function options.\n */\n\n/**\n * @name startOfToday\n * @category Day Helpers\n * @summary Return the start of today.\n * @pure false\n *\n * @description\n * Return the start of today.\n *\n * @typeParam ContextDate - The `Date` type of the context function.\n *\n * @param options - An object with options\n *\n * @returns The start of today\n *\n * @example\n * // If today is 6 October 2014:\n * const result = startOfToday()\n * //=> Mon Oct 6 2014 00:00:00\n */\nexport function startOfToday(options) {\n  return startOfDay(Date.now(), options);\n}\n\n// Fallback for modularized imports:\nexport default startOfToday;", "map": {"version": 3, "names": ["startOfDay", "startOfToday", "options", "Date", "now"], "sources": ["C:/Users/<USER>/PycharmProjects/CMS/webapp/frontend/node_modules/date-fns/startOfToday.js"], "sourcesContent": ["import { startOfDay } from \"./startOfDay.js\";\n\n/**\n * The {@link startOfToday} function options.\n */\n\n/**\n * @name startOfToday\n * @category Day Helpers\n * @summary Return the start of today.\n * @pure false\n *\n * @description\n * Return the start of today.\n *\n * @typeParam ContextDate - The `Date` type of the context function.\n *\n * @param options - An object with options\n *\n * @returns The start of today\n *\n * @example\n * // If today is 6 October 2014:\n * const result = startOfToday()\n * //=> Mon Oct 6 2014 00:00:00\n */\nexport function startOfToday(options) {\n  return startOfDay(Date.now(), options);\n}\n\n// Fallback for modularized imports:\nexport default startOfToday;\n"], "mappings": "AAAA,SAASA,UAAU,QAAQ,iBAAiB;;AAE5C;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO,SAASC,YAAYA,CAACC,OAAO,EAAE;EACpC,OAAOF,UAAU,CAACG,IAAI,CAACC,GAAG,CAAC,CAAC,EAAEF,OAAO,CAAC;AACxC;;AAEA;AACA,eAAeD,YAAY", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}