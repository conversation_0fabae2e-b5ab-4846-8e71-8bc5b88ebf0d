'use client'

import { useState, useEffect } from 'react'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Badge } from '@/components/ui/badge'
import { Alert, AlertDescription } from '@/components/ui/alert'
import { 
  CheckCircle, 
  AlertCircle, 
  Loader2, 
  TestTube,
  FileText,
  Settings,
  Cloud,
  Link,
  Thermometer
} from 'lucide-react'
import { certificazioniApi, caviApi, cantieriApi, strumentiApi } from '@/lib/api'

export default function TestCertificazioniPage() {
  const [isLoading, setIsLoading] = useState(false)
  const [testResults, setTestResults] = useState<any[]>([])
  const [cantiereId] = useState(1) // Test con cantiere ID 1

  const runTests = async () => {
    setIsLoading(true)
    setTestResults([])
    
    const results: any[] = []

    // Test 1: Caricamento dati meteorologici
    try {
      const weatherResponse = await cantieriApi.getWeatherData(cantiereId)
      results.push({
        test: 'Dati Meteorologici',
        status: 'success',
        message: `Temperatura: ${weatherResponse.data.temperature}°C, Umidità: ${weatherResponse.data.humidity}%`,
        data: weatherResponse.data
      })
    } catch (error: any) {
      results.push({
        test: 'Dati Meteorologici',
        status: 'error',
        message: error.response?.data?.detail || error.message
      })
    }

    // Test 2: Caricamento cavi
    try {
      const caviResponse = await caviApi.getCavi(cantiereId)
      const caviNonCollegati = caviResponse.filter((c: any) => (c.collegamenti || 0) < 3)
      results.push({
        test: 'Caricamento Cavi',
        status: 'success',
        message: `${caviResponse.length} cavi totali, ${caviNonCollegati.length} non completamente collegati`,
        data: { totali: caviResponse.length, nonCollegati: caviNonCollegati.length }
      })
    } catch (error: any) {
      results.push({
        test: 'Caricamento Cavi',
        status: 'error',
        message: error.response?.data?.detail || error.message
      })
    }

    // Test 3: Caricamento strumenti
    try {
      const strumentiResponse = await strumentiApi.getStrumenti(cantiereId)
      const strumentiAttivi = strumentiResponse.filter((s: any) => s.stato === 'ATTIVO')
      results.push({
        test: 'Caricamento Strumenti',
        status: 'success',
        message: `${strumentiResponse.length} strumenti totali, ${strumentiAttivi.length} attivi`,
        data: { totali: strumentiResponse.length, attivi: strumentiAttivi.length }
      })
    } catch (error: any) {
      results.push({
        test: 'Caricamento Strumenti',
        status: 'error',
        message: error.response?.data?.detail || error.message
      })
    }

    // Test 4: Caricamento certificazioni esistenti
    try {
      const certificazioniResponse = await certificazioniApi.getCertificazioni(cantiereId)
      const conformi = certificazioniResponse.filter((c: any) => c.stato_certificato === 'CONFORME')
      results.push({
        test: 'Caricamento Certificazioni',
        status: 'success',
        message: `${certificazioniResponse.length} certificazioni totali, ${conformi.length} conformi`,
        data: { totali: certificazioniResponse.length, conformi: conformi.length }
      })
    } catch (error: any) {
      results.push({
        test: 'Caricamento Certificazioni',
        status: 'error',
        message: error.response?.data?.detail || error.message
      })
    }

    // Test 5: Test collegamento automatico (simulato)
    try {
      const caviResponse = await caviApi.getCavi(cantiereId)
      const cavoTest = caviResponse.find((c: any) => (c.collegamenti || 0) < 3)
      
      if (cavoTest) {
        // Simula il collegamento automatico
        results.push({
          test: 'Collegamento Automatico',
          status: 'info',
          message: `Cavo ${cavoTest.id_cavo} disponibile per test collegamento (collegamenti: ${cavoTest.collegamenti || 0}/3)`,
          data: { cavoId: cavoTest.id_cavo, collegamenti: cavoTest.collegamenti || 0 }
        })
      } else {
        results.push({
          test: 'Collegamento Automatico',
          status: 'warning',
          message: 'Nessun cavo disponibile per test collegamento (tutti già collegati)'
        })
      }
    } catch (error: any) {
      results.push({
        test: 'Collegamento Automatico',
        status: 'error',
        message: error.response?.data?.detail || error.message
      })
    }

    setTestResults(results)
    setIsLoading(false)
  }

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'success':
        return <CheckCircle className="h-5 w-5 text-green-600" />
      case 'error':
        return <AlertCircle className="h-5 w-5 text-red-600" />
      case 'warning':
        return <AlertCircle className="h-5 w-5 text-yellow-600" />
      case 'info':
        return <TestTube className="h-5 w-5 text-blue-600" />
      default:
        return <Loader2 className="h-5 w-5 animate-spin" />
    }
  }

  const getStatusBadge = (status: string) => {
    switch (status) {
      case 'success':
        return <Badge className="bg-green-100 text-green-800 border-green-200">Successo</Badge>
      case 'error':
        return <Badge className="bg-red-100 text-red-800 border-red-200">Errore</Badge>
      case 'warning':
        return <Badge className="bg-yellow-100 text-yellow-800 border-yellow-200">Attenzione</Badge>
      case 'info':
        return <Badge className="bg-blue-100 text-blue-800 border-blue-200">Info</Badge>
      default:
        return <Badge variant="outline">In corso</Badge>
    }
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-slate-50 to-slate-100 p-6">
      <div className="max-w-4xl mx-auto space-y-6">
        {/* Header */}
        <div className="flex items-center justify-between">
          <div>
            <h1 className="text-3xl font-bold text-slate-900 flex items-center gap-3">
              <TestTube className="h-8 w-8 text-blue-600" />
              Test Sistema Certificazioni
            </h1>
            <p className="text-slate-600 mt-1">Verifica funzionalità e automazioni CEI 64-8</p>
          </div>
          
          <Button onClick={runTests} disabled={isLoading}>
            {isLoading ? (
              <>
                <Loader2 className="h-4 w-4 mr-2 animate-spin" />
                Test in corso...
              </>
            ) : (
              <>
                <TestTube className="h-4 w-4 mr-2" />
                Avvia Test
              </>
            )}
          </Button>
        </div>

        {/* Funzionalità Testate */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <FileText className="h-5 w-5" />
              Funzionalità Implementate
            </CardTitle>
            <CardDescription>Sistema certificazioni CEI 64-8 con automazioni</CardDescription>
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div className="space-y-2">
                <div className="flex items-center gap-2">
                  <Cloud className="h-4 w-4 text-blue-500" />
                  <span className="font-medium">Dati Meteorologici Automatici</span>
                </div>
                <p className="text-sm text-slate-600 ml-6">
                  Temperatura e umidità rilevate automaticamente per il cantiere
                </p>
              </div>
              
              <div className="space-y-2">
                <div className="flex items-center gap-2">
                  <Link className="h-4 w-4 text-green-500" />
                  <span className="font-medium">Collegamento Automatico Cavi</span>
                </div>
                <p className="text-sm text-slate-600 ml-6">
                  Collegamento automatico durante certificazione CEI 64-8
                </p>
              </div>
              
              <div className="space-y-2">
                <div className="flex items-center gap-2">
                  <CheckCircle className="h-4 w-4 text-purple-500" />
                  <span className="font-medium">Validazione Automatica</span>
                </div>
                <p className="text-sm text-slate-600 ml-6">
                  Determinazione automatica conformità in base ai valori
                </p>
              </div>
              
              <div className="space-y-2">
                <div className="flex items-center gap-2">
                  <Settings className="h-4 w-4 text-orange-500" />
                  <span className="font-medium">Gestione Strumenti</span>
                </div>
                <p className="text-sm text-slate-600 ml-6">
                  Tracking calibrazione e notifiche scadenze
                </p>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Risultati Test */}
        {testResults.length > 0 && (
          <Card>
            <CardHeader>
              <CardTitle>Risultati Test</CardTitle>
              <CardDescription>Verifica delle funzionalità del sistema</CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                {testResults.map((result, index) => (
                  <div key={index} className="flex items-start gap-3 p-3 rounded-lg border">
                    {getStatusIcon(result.status)}
                    <div className="flex-1">
                      <div className="flex items-center justify-between">
                        <h4 className="font-medium">{result.test}</h4>
                        {getStatusBadge(result.status)}
                      </div>
                      <p className="text-sm text-slate-600 mt-1">{result.message}</p>
                      {result.data && (
                        <pre className="text-xs bg-slate-100 p-2 rounded mt-2 overflow-auto">
                          {JSON.stringify(result.data, null, 2)}
                        </pre>
                      )}
                    </div>
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>
        )}

        {/* Link al Sistema */}
        <Card>
          <CardHeader>
            <CardTitle>Accesso al Sistema</CardTitle>
            <CardDescription>Link per testare l'interfaccia completa</CardDescription>
          </CardHeader>
          <CardContent>
            <div className="flex gap-4">
              <Button asChild>
                <a href="/certificazioni" target="_blank">
                  <FileText className="h-4 w-4 mr-2" />
                  Sistema Certificazioni
                </a>
              </Button>
              <Button variant="outline" asChild>
                <a href="/cavi" target="_blank">
                  <Link className="h-4 w-4 mr-2" />
                  Gestione Cavi
                </a>
              </Button>
            </div>
          </CardContent>
        </Card>
      </div>
    </div>
  )
}
