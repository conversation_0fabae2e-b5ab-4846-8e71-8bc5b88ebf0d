{"ast": null, "code": "export default function shouldSkipGeneratingVar(keys) {\n  var _keys$;\n  return !!keys[0].match(/(cssVarPrefix|typography|mixins|breakpoints|direction|transitions)/) || !!keys[0].match(/sxConfig$/) ||\n  // ends with sxConfig\n  keys[0] === 'palette' && !!((_keys$ = keys[1]) != null && _keys$.match(/(mode|contrastThreshold|tonalOffset)/));\n}", "map": {"version": 3, "names": ["shouldSkipGeneratingVar", "keys", "_keys$", "match"], "sources": ["C:/Users/<USER>/PycharmProjects/CMS/webapp/frontend/node_modules/@mui/material/styles/shouldSkipGeneratingVar.js"], "sourcesContent": ["export default function shouldSkipGeneratingVar(keys) {\n  var _keys$;\n  return !!keys[0].match(/(cssVarPrefix|typography|mixins|breakpoints|direction|transitions)/) || !!keys[0].match(/sxConfig$/) ||\n  // ends with sxConfig\n  keys[0] === 'palette' && !!((_keys$ = keys[1]) != null && _keys$.match(/(mode|contrastThreshold|tonalOffset)/));\n}"], "mappings": "AAAA,eAAe,SAASA,uBAAuBA,CAACC,IAAI,EAAE;EACpD,IAAIC,MAAM;EACV,OAAO,CAAC,CAACD,IAAI,CAAC,CAAC,CAAC,CAACE,KAAK,CAAC,oEAAoE,CAAC,IAAI,CAAC,CAACF,IAAI,CAAC,CAAC,CAAC,CAACE,KAAK,CAAC,WAAW,CAAC;EAC5H;EACAF,IAAI,CAAC,CAAC,CAAC,KAAK,SAAS,IAAI,CAAC,EAAE,CAACC,MAAM,GAAGD,IAAI,CAAC,CAAC,CAAC,KAAK,IAAI,IAAIC,MAAM,CAACC,KAAK,CAAC,sCAAsC,CAAC,CAAC;AACjH", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}