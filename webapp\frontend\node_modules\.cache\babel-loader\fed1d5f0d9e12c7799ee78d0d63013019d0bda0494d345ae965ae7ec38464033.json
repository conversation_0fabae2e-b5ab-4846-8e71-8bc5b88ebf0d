{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\PycharmProjects\\\\CMS\\\\webapp\\\\frontend\\\\src\\\\components\\\\cavi\\\\MetriPosatiSemplificatoForm.js\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect } from 'react';\nimport { Box, Typography, TextField, Button, Paper, Table, TableBody, TableCell, TableContainer, TableHead, TableRow, CircularProgress, Alert, Chip, Divider, Grid, FormControl, InputLabel, Select, MenuItem, Dialog, DialogTitle, DialogContent, DialogActions, DialogContentText } from '@mui/material';\nimport { useNavigate } from 'react-router-dom';\nimport caviService from '../../services/caviService';\nimport parcoCaviService from '../../services/parcoCaviService';\nimport { CABLE_STATES, REEL_STATES, determineCableState, determineReelState, canModifyCable, isCableSpare, isCableInstalled, getCableStateColor, getReelStateColor } from '../../utils/stateUtils';\nimport { redirectToVisualizzaCavi } from '../../utils/navigationUtils';\nimport IncompatibleReelDialog from './IncompatibleReelDialog';\n\n/**\n * Componente per l'inserimento dei metri posati di un cavo\n * Versione ultra-semplificata con workflow compresso in un'unica pagina\n *\n * @param {Object} props - Proprietà del componente\n * @param {string} props.cantiereId - ID del cantiere\n * @param {Function} props.onSuccess - Funzione chiamata al successo dell'operazione\n * @param {Function} props.onError - Funzione chiamata in caso di errore\n */\nimport { jsxDEV as _jsxDEV, Fragment as _Fragment } from \"react/jsx-dev-runtime\";\nconst MetriPosatiSemplificatoForm = ({\n  cantiereId,\n  onSuccess,\n  onError\n}) => {\n  _s();\n  const navigate = useNavigate();\n\n  // Stati per i dati\n  const [cavi, setCavi] = useState([]);\n  const [bobine, setBobine] = useState([]);\n  const [selectedCavo, setSelectedCavo] = useState(null);\n  const [formData, setFormData] = useState({\n    id_cavo: '',\n    metri_posati: '',\n    id_bobina: ''\n  });\n\n  // Stati per il caricamento\n  const [loading, setLoading] = useState(false);\n  const [caviLoading, setCaviLoading] = useState(false);\n  const [bobineLoading, setBobineLoading] = useState(false);\n  const [saving, setSaving] = useState(false);\n\n  // Stati per la validazione\n  const [formErrors, setFormErrors] = useState({});\n  const [formWarnings, setFormWarnings] = useState({});\n\n  // Stati per i dialoghi speciali\n  const [showIncompatibleReelDialog, setShowIncompatibleReelDialog] = useState(false);\n  const [incompatibleReelData, setIncompatibleReelData] = useState({\n    cavo: null,\n    bobina: null\n  });\n  const [showAlreadyLaidDialog, setShowAlreadyLaidDialog] = useState(false);\n  const [alreadyLaidCavo, setAlreadyLaidCavo] = useState(null);\n\n  // Carica la lista dei cavi e delle bobine all'avvio\n  useEffect(() => {\n    loadCavi();\n    loadBobine();\n  }, [cantiereId]);\n\n  // Carica la lista dei cavi\n  const loadCavi = async () => {\n    try {\n      setCaviLoading(true);\n      const caviData = await caviService.getCavi(cantiereId);\n\n      // Filtra i cavi che non sono SPARE\n      const caviAttivi = caviData.filter(cavo => !isCableSpare(cavo));\n      setCavi(caviAttivi);\n    } catch (error) {\n      console.error('Errore nel caricamento dei cavi:', error);\n      onError('Errore nel caricamento dei cavi: ' + (error.message || 'Errore sconosciuto'));\n    } finally {\n      setCaviLoading(false);\n    }\n  };\n\n  // Carica la lista delle bobine\n  const loadBobine = async () => {\n    try {\n      setBobineLoading(true);\n      console.log('Caricamento bobine per cantiere:', cantiereId);\n      const bobineData = await parcoCaviService.getBobine(cantiereId);\n      console.log('Bobine caricate:', bobineData);\n      console.log('Dettaglio bobine:');\n      bobineData.forEach(bobina => {\n        console.log(`Bobina ${bobina.id_bobina}:`, {\n          tipologia: bobina.tipologia,\n          sezione: bobina.sezione,\n          metri_residui: bobina.metri_residui,\n          stato_bobina: bobina.stato_bobina\n        });\n      });\n      setBobine(bobineData);\n    } catch (error) {\n      console.error('Errore nel caricamento delle bobine:', error);\n      onError('Errore nel caricamento delle bobine: ' + (error.message || 'Errore sconosciuto'));\n    } finally {\n      setBobineLoading(false);\n    }\n  };\n\n  // Gestisce la selezione di un cavo\n  const handleCavoSelect = cavo => {\n    console.log('Cavo selezionato:', cavo);\n    console.log('Dettaglio cavo:', {\n      id_cavo: cavo.id_cavo,\n      tipologia: cavo.tipologia,\n      sezione: cavo.sezione,\n      metri_teorici: cavo.metri_teorici,\n      stato_installazione: cavo.stato_installazione\n    });\n\n    // Verifica se il cavo è già posato\n    if (isCableInstalled(cavo)) {\n      console.log('Cavo già posato, mostro dialog');\n      setAlreadyLaidCavo(cavo);\n      setShowAlreadyLaidDialog(true);\n      return;\n    }\n    setSelectedCavo(cavo);\n    setFormData({\n      id_cavo: cavo.id_cavo,\n      metri_posati: '',\n      id_bobina: ''\n    });\n    setFormErrors({});\n    setFormWarnings({});\n\n    // Log per debug - verifica se ci sono bobine compatibili\n    console.log('Verifica bobine compatibili per il cavo selezionato...');\n  };\n\n  // Gestisce la modifica dei campi del form\n  const handleInputChange = e => {\n    const {\n      name,\n      value\n    } = e.target;\n    setFormData(prev => ({\n      ...prev,\n      [name]: value\n    }));\n\n    // Validazione in tempo reale\n    validateField(name, value);\n  };\n\n  // Validazione di un singolo campo\n  const validateField = (name, value) => {\n    const newErrors = {\n      ...formErrors\n    };\n    const newWarnings = {\n      ...formWarnings\n    };\n    if (name === 'metri_posati') {\n      // Validazione metri posati\n      if (value === '') {\n        newErrors.metri_posati = 'I metri posati sono obbligatori';\n      } else if (isNaN(value) || parseFloat(value) < 0) {\n        newErrors.metri_posati = 'I metri posati devono essere un numero positivo';\n      } else {\n        delete newErrors.metri_posati;\n\n        // Avvisi sui metri posati\n        const metriPosati = parseFloat(value);\n        if (selectedCavo && metriPosati > selectedCavo.metri_teorici) {\n          newWarnings.metri_posati = `I metri posati (${metriPosati}) superano i metri teorici (${selectedCavo.metri_teorici})`;\n        } else {\n          delete newWarnings.metri_posati;\n        }\n\n        // Avvisi sulla bobina selezionata\n        if (formData.id_bobina && formData.id_bobina !== 'BOBINA_VUOTA') {\n          const selectedBobina = bobine.find(b => b.id_bobina === formData.id_bobina);\n          if (selectedBobina && metriPosati > selectedBobina.metri_residui) {\n            newWarnings.id_bobina = `I metri posati (${metriPosati}) superano i metri residui della bobina (${selectedBobina.metri_residui})`;\n          } else {\n            delete newWarnings.id_bobina;\n          }\n        }\n      }\n    }\n    if (name === 'id_bobina') {\n      // Validazione bobina\n      if (value === '') {\n        newErrors.id_bobina = 'La bobina è obbligatoria';\n      } else {\n        delete newErrors.id_bobina;\n\n        // Avvisi sulla bobina selezionata\n        if (value !== 'BOBINA_VUOTA' && formData.metri_posati) {\n          const metriPosati = parseFloat(formData.metri_posati);\n          const selectedBobina = bobine.find(b => b.id_bobina === value);\n          if (selectedBobina && metriPosati > selectedBobina.metri_residui) {\n            newWarnings.id_bobina = `I metri posati (${metriPosati}) superano i metri residui della bobina (${selectedBobina.metri_residui})`;\n          } else {\n            delete newWarnings.id_bobina;\n          }\n        }\n      }\n    }\n    setFormErrors(newErrors);\n    setFormWarnings(newWarnings);\n  };\n\n  // Validazione completa del form\n  const validateForm = () => {\n    const newErrors = {};\n\n    // Validazione metri posati\n    if (!formData.metri_posati) {\n      newErrors.metri_posati = 'I metri posati sono obbligatori';\n    } else if (isNaN(formData.metri_posati) || parseFloat(formData.metri_posati) < 0) {\n      newErrors.metri_posati = 'I metri posati devono essere un numero positivo';\n    }\n\n    // Validazione bobina\n    if (!formData.id_bobina) {\n      newErrors.id_bobina = 'La bobina è obbligatoria';\n    }\n    setFormErrors(newErrors);\n    return Object.keys(newErrors).length === 0;\n  };\n\n  // Verifica la compatibilità tra cavo e bobina\n  const checkCompatibility = () => {\n    if (formData.id_bobina === 'BOBINA_VUOTA') {\n      return true; // BOBINA_VUOTA è sempre compatibile\n    }\n    const selectedBobina = bobine.find(b => b.id_bobina === formData.id_bobina);\n    if (!selectedBobina) {\n      return false;\n    }\n\n    // Verifica compatibilità tipologia\n    const tipologiaCompatibile = selectedCavo.tipologia === selectedBobina.tipologia;\n\n    // Verifica compatibilità sezione\n    const sezioneCompatibile = String(selectedCavo.sezione) === String(selectedBobina.sezione);\n    return tipologiaCompatibile && sezioneCompatibile;\n  };\n\n  // Gestisce il salvataggio dei dati\n  const handleSave = async () => {\n    // Se non ci sono bobine disponibili, imposta automaticamente BOBINA_VUOTA\n    if (bobine.length === 0 && !bobineLoading) {\n      if (!formData.metri_posati || isNaN(parseFloat(formData.metri_posati)) || parseFloat(formData.metri_posati) <= 0) {\n        setFormErrors({\n          ...formErrors,\n          metri_posati: 'I metri posati sono obbligatori e devono essere maggiori di zero'\n        });\n        return;\n      }\n\n      // Imposta BOBINA_VUOTA e procedi con il salvataggio\n      formData.id_bobina = 'BOBINA_VUOTA';\n    } else {\n      // Validazione completa\n      if (!validateForm()) {\n        return;\n      }\n\n      // Verifica compatibilità\n      if (!checkCompatibility()) {\n        // Mostra dialog per incompatibilità\n        const selectedBobina = bobine.find(b => b.id_bobina === formData.id_bobina);\n        setIncompatibleReelData({\n          cavo: selectedCavo,\n          bobina: selectedBobina\n        });\n        setShowIncompatibleReelDialog(true);\n        return;\n      }\n    }\n\n    // Procedi con il salvataggio\n    try {\n      setSaving(true);\n\n      // Converti metri posati in numero\n      const metriPosati = parseFloat(formData.metri_posati);\n\n      // Chiamata API\n      console.log('Invio richiesta updateMetriPosati con i seguenti parametri:');\n      console.log('- cantiereId:', cantiereId);\n      console.log('- id_cavo:', formData.id_cavo);\n      console.log('- metri_posati:', metriPosati);\n      console.log('- id_bobina:', formData.id_bobina);\n\n      // Imposta sempre forceOver a true per evitare blocchi quando la bobina va in OVER\n      await caviService.updateMetriPosati(cantiereId, formData.id_cavo, metriPosati, formData.id_bobina, true // Forza sempre a true per evitare blocchi\n      );\n\n      // Mostra messaggio di successo\n      onSuccess('Metri posati aggiornati con successo');\n\n      // Resetta il form\n      setSelectedCavo(null);\n      setFormData({\n        id_cavo: '',\n        metri_posati: '',\n        id_bobina: ''\n      });\n\n      // Ricarica i dati\n      loadCavi();\n      loadBobine();\n    } catch (error) {\n      console.error('Errore durante il salvataggio:', error);\n      onError('Errore durante il salvataggio: ' + (error.message || 'Errore sconosciuto'));\n    } finally {\n      setSaving(false);\n    }\n  };\n\n  // Gestisce l'aggiornamento del cavo per compatibilità\n  const handleUpdateCavoForCompatibility = async () => {\n    try {\n      setSaving(true);\n      setShowIncompatibleReelDialog(false);\n      const {\n        cavo,\n        bobina\n      } = incompatibleReelData;\n\n      // Aggiorna il cavo per renderlo compatibile con la bobina\n      await caviService.updateCavoForCompatibility(cantiereId, cavo.id_cavo, {\n        id_bobina: bobina.id_bobina,\n        tipologia: bobina.tipologia,\n        sezione: bobina.sezione\n      });\n\n      // Procedi con l'aggiornamento dei metri posati\n      await caviService.updateMetriPosati(cantiereId, formData.id_cavo, parseFloat(formData.metri_posati), formData.id_bobina, true // Forza sempre a true per evitare blocchi\n      );\n\n      // Mostra messaggio di successo\n      onSuccess('Cavo aggiornato e metri posati registrati con successo');\n\n      // Resetta il form\n      setSelectedCavo(null);\n      setFormData({\n        id_cavo: '',\n        metri_posati: '',\n        id_bobina: ''\n      });\n\n      // Ricarica i dati\n      loadCavi();\n      loadBobine();\n    } catch (error) {\n      console.error('Errore durante l\\'aggiornamento del cavo:', error);\n      onError('Errore durante l\\'aggiornamento del cavo: ' + (error.message || 'Errore sconosciuto'));\n    } finally {\n      setSaving(false);\n    }\n  };\n\n  // Gestisce la chiusura del dialogo per cavi già posati\n  const handleCloseAlreadyLaidDialog = () => {\n    setShowAlreadyLaidDialog(false);\n    setAlreadyLaidCavo(null);\n  };\n\n  // Gestisce la selezione di un altro cavo\n  const handleSelectAnotherCable = () => {\n    handleCloseAlreadyLaidDialog();\n  };\n\n  // Gestisce l'opzione di modificare la bobina di un cavo già posato\n  const handleModifyReel = () => {\n    if (alreadyLaidCavo) {\n      navigate(`/dashboard/cavi/posa/modifica-bobina?cavoId=${alreadyLaidCavo.id_cavo}`);\n    }\n    handleCloseAlreadyLaidDialog();\n  };\n\n  // Stato per le bobine compatibili\n  const [compatibleBobine, setCompatibleBobine] = useState([]);\n\n  // Aggiorna le bobine compatibili quando viene selezionato un cavo o cambiano le bobine disponibili\n  useEffect(() => {\n    if (selectedCavo) {\n      // Filtra le bobine compatibili localmente\n      const filtered = filterCompatibleBobine(selectedCavo);\n      setCompatibleBobine(filtered);\n    } else {\n      setCompatibleBobine([]);\n    }\n  }, [selectedCavo, bobine]);\n\n  // Filtra le bobine compatibili localmente usando lo stesso approccio di QuickAddCablesDialog\n  const filterCompatibleBobine = cavo => {\n    if (!cavo) return [];\n    console.log('Filtrando bobine compatibili per cavo:', cavo);\n    console.log('Bobine disponibili:', bobine);\n    console.log('Numero di bobine disponibili:', bobine.length);\n\n    // Verifica se ci sono bobine disponibili\n    if (bobine.length === 0) {\n      console.log('ATTENZIONE: Nessuna bobina disponibile nel cantiere!');\n      return [];\n    }\n\n    // Filtra le bobine compatibili con il cavo\n    const filtered = bobine.filter(bobina => {\n      // Usa un approccio più flessibile per il confronto\n      // 1. Normalizza tipologia (trim e lowercase)\n      // 2. Normalizza sezione (trim e conversione in stringa)\n      // 3. Verifica che la bobina non sia terminata\n      const cavoTipologiaNorm = String(cavo.tipologia || '').trim().toLowerCase();\n      const cavoSezioneNorm = String(cavo.sezione || '').trim();\n      const bobinaTipologiaNorm = String(bobina.tipologia || '').trim().toLowerCase();\n      const bobinaSezioneNorm = String(bobina.sezione || '').trim();\n      const tipologiaMatch = bobinaTipologiaNorm === cavoTipologiaNorm;\n      const sezioneMatch = bobinaSezioneNorm === cavoSezioneNorm;\n      const reelState = determineReelState(bobina.metri_residui, bobina.metri_totali);\n      const stateOk = reelState !== REEL_STATES.TERMINATA;\n      const isCompatible = tipologiaMatch && sezioneMatch && stateOk;\n\n      // Log ancora più dettagliati\n      console.log(`Confronto dettagliato per bobina ${bobina.id_bobina}:`);\n      console.log(`- Tipologia bobina (originale): \"${bobina.tipologia}\", tipo: ${typeof bobina.tipologia}`);\n      console.log(`- Tipologia cavo (originale): \"${cavo.tipologia}\", tipo: ${typeof cavo.tipologia}`);\n      console.log(`- Tipologia bobina (normalizzata): \"${bobinaTipologiaNorm}\"`);\n      console.log(`- Tipologia cavo (normalizzata): \"${cavoTipologiaNorm}\"`);\n      console.log(`- Sezione bobina (originale): \"${bobina.sezione}\", tipo: ${typeof bobina.sezione}`);\n      console.log(`- Sezione cavo (originale): \"${cavo.sezione}\", tipo: ${typeof cavo.sezione}`);\n      console.log(`- Sezione bobina (normalizzata): \"${bobinaSezioneNorm}\"`);\n      console.log(`- Sezione cavo (normalizzata): \"${cavoSezioneNorm}\"`);\n\n      // Log dettagliato per debug\n      console.log(`Bobina ${bobina.id_bobina}:`, {\n        'Tipologia bobina': `\"${bobina.tipologia}\"`,\n        'Tipologia cavo': `\"${cavo.tipologia}\"`,\n        'Tipologie uguali?': tipologiaMatch,\n        'Sezione bobina': `\"${String(bobina.sezione)}\"`,\n        'Sezione cavo': `\"${String(cavo.sezione)}\"`,\n        'Sezioni uguali?': sezioneMatch,\n        'Stato bobina': reelState,\n        'Stato OK?': stateOk,\n        'Compatibile?': isCompatible\n      });\n      return isCompatible;\n    });\n    console.log('Bobine compatibili trovate:', filtered.length);\n    if (filtered.length > 0) {\n      console.log('Prima bobina compatibile:', filtered[0]);\n    } else {\n      console.log('ATTENZIONE: Nessuna bobina compatibile trovata!');\n    }\n    return filtered;\n  };\n\n  // Funzione di utilità per ottenere le bobine compatibili (usata nel rendering)\n  const getCompatibleBobine = () => {\n    return compatibleBobine;\n  };\n\n  // Renderizza la tabella dei cavi\n  const renderCaviTable = () => {\n    if (caviLoading) {\n      return /*#__PURE__*/_jsxDEV(Box, {\n        sx: {\n          display: 'flex',\n          justifyContent: 'center',\n          my: 4\n        },\n        children: /*#__PURE__*/_jsxDEV(CircularProgress, {}, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 513,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 512,\n        columnNumber: 9\n      }, this);\n    }\n    if (cavi.length === 0) {\n      return /*#__PURE__*/_jsxDEV(Alert, {\n        severity: \"info\",\n        sx: {\n          my: 2\n        },\n        children: \"Nessun cavo disponibile per questo cantiere.\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 520,\n        columnNumber: 9\n      }, this);\n    }\n    return /*#__PURE__*/_jsxDEV(_Fragment, {\n      children: [/*#__PURE__*/_jsxDEV(Alert, {\n        severity: \"info\",\n        sx: {\n          mb: 2\n        },\n        children: \"Seleziona un cavo dalla tabella per inserire i metri posati. I cavi gi\\xE0 installati sono disabilitati.\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 528,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(TableContainer, {\n        component: Paper,\n        sx: {\n          mb: 3\n        },\n        children: /*#__PURE__*/_jsxDEV(Table, {\n          size: \"small\",\n          children: [/*#__PURE__*/_jsxDEV(TableHead, {\n            children: /*#__PURE__*/_jsxDEV(TableRow, {\n              sx: {\n                bgcolor: '#e3f2fd'\n              },\n              children: [/*#__PURE__*/_jsxDEV(TableCell, {\n                children: \"ID Cavo\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 536,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n                children: \"Tipologia\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 537,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n                children: \"Ubicazione\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 538,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n                children: \"Metri Teorici\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 539,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n                children: \"Stato\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 540,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n                children: \"Azioni\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 541,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 535,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 534,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(TableBody, {\n            children: cavi.map(cavo => {\n              const isInstalled = isCableInstalled(cavo);\n              return /*#__PURE__*/_jsxDEV(TableRow, {\n                sx: {\n                  bgcolor: isInstalled ? '#f5f5f5' : 'inherit',\n                  '&:hover': {\n                    bgcolor: isInstalled ? '#f5f5f5' : '#f1f8e9'\n                  }\n                },\n                children: [/*#__PURE__*/_jsxDEV(TableCell, {\n                  children: /*#__PURE__*/_jsxDEV(\"strong\", {\n                    children: cavo.id_cavo\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 555,\n                    columnNumber: 32\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 555,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n                  children: cavo.tipologia || 'N/A'\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 556,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n                  children: [\"Da: \", cavo.ubicazione_partenza || 'N/A', /*#__PURE__*/_jsxDEV(\"br\", {}, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 557,\n                    columnNumber: 71\n                  }, this), \"A: \", cavo.ubicazione_arrivo || 'N/A']\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 557,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n                  children: [cavo.metri_teorici || 'N/A', \" m\"]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 558,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n                  children: /*#__PURE__*/_jsxDEV(Chip, {\n                    label: cavo.stato_installazione || 'N/D',\n                    size: \"small\",\n                    color: getCableStateColor(cavo.stato_installazione),\n                    variant: \"outlined\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 560,\n                    columnNumber: 23\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 559,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n                  children: /*#__PURE__*/_jsxDEV(Button, {\n                    size: \"small\",\n                    variant: \"contained\",\n                    color: \"primary\",\n                    onClick: () => handleCavoSelect(cavo),\n                    disabled: isInstalled,\n                    children: isInstalled ? 'Già installato' : 'Seleziona'\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 568,\n                    columnNumber: 23\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 567,\n                  columnNumber: 21\n                }, this)]\n              }, cavo.id_cavo, true, {\n                fileName: _jsxFileName,\n                lineNumber: 548,\n                columnNumber: 19\n              }, this);\n            })\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 544,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 533,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 532,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true);\n  };\n\n  // Renderizza il form per inserimento metri e selezione bobina\n  const renderForm = () => {\n    if (!selectedCavo) return null;\n\n    // Log per debug - verifica le bobine compatibili nel rendering\n    const compatibleBobineList = getCompatibleBobine();\n    console.log('Rendering form - Bobine compatibili:', compatibleBobineList);\n    console.log('Rendering form - Numero di bobine compatibili:', compatibleBobineList.length);\n\n    // Verifica se ci sono bobine disponibili\n    if (bobine.length === 0 && !bobineLoading) {\n      return /*#__PURE__*/_jsxDEV(Paper, {\n        sx: {\n          p: 3\n        },\n        children: [/*#__PURE__*/_jsxDEV(Typography, {\n          variant: \"h6\",\n          gutterBottom: true,\n          children: \"Inserimento metri posati\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 601,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Alert, {\n          severity: \"warning\",\n          sx: {\n            mb: 3\n          },\n          children: \"Non ci sono bobine disponibili nel cantiere. Puoi comunque registrare i metri posati utilizzando l'opzione \\\"BOBINA VUOTA\\\".\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 605,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Box, {\n          sx: {\n            mb: 3,\n            p: 2,\n            bgcolor: '#f5f5f5',\n            borderRadius: 1\n          },\n          children: [/*#__PURE__*/_jsxDEV(Typography, {\n            variant: \"subtitle1\",\n            gutterBottom: true,\n            fontWeight: \"bold\",\n            color: \"primary\",\n            children: [\"Cavo selezionato: \", selectedCavo.id_cavo]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 610,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Grid, {\n            container: true,\n            spacing: 2,\n            children: [/*#__PURE__*/_jsxDEV(Grid, {\n              item: true,\n              xs: 12,\n              md: 4,\n              children: /*#__PURE__*/_jsxDEV(Typography, {\n                variant: \"body2\",\n                children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n                  children: \"Tipologia:\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 616,\n                  columnNumber: 19\n                }, this), \" \", selectedCavo.tipologia || 'N/A']\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 615,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 614,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Grid, {\n              item: true,\n              xs: 12,\n              md: 4,\n              children: /*#__PURE__*/_jsxDEV(Typography, {\n                variant: \"body2\",\n                children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n                  children: \"Formazione:\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 621,\n                  columnNumber: 19\n                }, this), \" \", selectedCavo.sezione || 'N/A']\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 620,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 619,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Grid, {\n              item: true,\n              xs: 12,\n              md: 4,\n              children: /*#__PURE__*/_jsxDEV(Typography, {\n                variant: \"body2\",\n                children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n                  children: \"Metri teorici:\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 626,\n                  columnNumber: 19\n                }, this), \" \", selectedCavo.metri_teorici || 'N/A', \" m\"]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 625,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 624,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 613,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 609,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Grid, {\n          container: true,\n          spacing: 3,\n          children: [/*#__PURE__*/_jsxDEV(Grid, {\n            item: true,\n            xs: 12,\n            md: 6,\n            children: /*#__PURE__*/_jsxDEV(TextField, {\n              fullWidth: true,\n              label: \"Metri posati\",\n              name: \"metri_posati\",\n              value: formData.metri_posati,\n              onChange: handleInputChange,\n              type: \"number\",\n              InputProps: {\n                inputProps: {\n                  min: 0,\n                  step: 0.1\n                }\n              }\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 634,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 633,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Grid, {\n            item: true,\n            xs: 12,\n            md: 6,\n            children: /*#__PURE__*/_jsxDEV(FormControl, {\n              fullWidth: true,\n              children: [/*#__PURE__*/_jsxDEV(InputLabel, {\n                children: \"Bobina\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 648,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(Select, {\n                name: \"id_bobina\",\n                value: \"BOBINA_VUOTA\",\n                disabled: true,\n                children: /*#__PURE__*/_jsxDEV(MenuItem, {\n                  value: \"BOBINA_VUOTA\",\n                  sx: {\n                    fontWeight: 'bold',\n                    color: '#2e7d32',\n                    bgcolor: '#f1f8e9'\n                  },\n                  children: \"BOBINA VUOTA (Cavo posato senza bobina)\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 654,\n                  columnNumber: 19\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 649,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                variant: \"caption\",\n                color: \"text.secondary\",\n                sx: {\n                  mt: 1\n                },\n                children: \"Non ci sono bobine disponibili. Verr\\xE0 utilizzata l'opzione BOBINA VUOTA.\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 658,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 647,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 646,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 632,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Box, {\n          sx: {\n            mt: 3,\n            display: 'flex',\n            justifyContent: 'space-between'\n          },\n          children: [/*#__PURE__*/_jsxDEV(Button, {\n            variant: \"outlined\",\n            color: \"secondary\",\n            onClick: () => {\n              setSelectedCavo(null);\n              setFormData({\n                id_cavo: '',\n                metri_posati: '',\n                id_bobina: ''\n              });\n            },\n            disabled: saving,\n            children: \"Annulla\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 666,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Button, {\n            variant: \"contained\",\n            color: \"primary\",\n            onClick: () => handleSave(),\n            disabled: saving || !formData.metri_posati,\n            children: saving ? /*#__PURE__*/_jsxDEV(CircularProgress, {\n              size: 24\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 687,\n              columnNumber: 25\n            }, this) : 'Salva'\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 681,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 665,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 600,\n        columnNumber: 9\n      }, this);\n    }\n    const compatibleBobine = getCompatibleBobine();\n    return /*#__PURE__*/_jsxDEV(Paper, {\n      sx: {\n        p: 3\n      },\n      children: [/*#__PURE__*/_jsxDEV(Typography, {\n        variant: \"h6\",\n        gutterBottom: true,\n        children: \"Inserimento metri posati\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 698,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Alert, {\n        severity: \"info\",\n        sx: {\n          mb: 3\n        },\n        children: [\"Inserisci i metri posati per il cavo selezionato e associa una bobina. Se il cavo \\xE8 stato posato senza una bobina specifica, seleziona \", /*#__PURE__*/_jsxDEV(\"strong\", {\n          children: \"BOBINA VUOTA\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 703,\n          columnNumber: 146\n        }, this), \".\"]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 702,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Box, {\n        sx: {\n          mb: 3,\n          p: 2,\n          bgcolor: '#f5f5f5',\n          borderRadius: 1\n        },\n        children: [/*#__PURE__*/_jsxDEV(Typography, {\n          variant: \"subtitle1\",\n          gutterBottom: true,\n          fontWeight: \"bold\",\n          color: \"primary\",\n          children: [\"Cavo selezionato: \", selectedCavo.id_cavo]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 707,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Grid, {\n          container: true,\n          spacing: 2,\n          children: [/*#__PURE__*/_jsxDEV(Grid, {\n            item: true,\n            xs: 12,\n            md: 4,\n            children: /*#__PURE__*/_jsxDEV(Typography, {\n              variant: \"body2\",\n              children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n                children: \"Tipologia:\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 713,\n                columnNumber: 17\n              }, this), \" \", selectedCavo.tipologia || 'N/A']\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 712,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 711,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Grid, {\n            item: true,\n            xs: 12,\n            md: 4,\n            children: /*#__PURE__*/_jsxDEV(Typography, {\n              variant: \"body2\",\n              children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n                children: \"Formazione:\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 718,\n                columnNumber: 17\n              }, this), \" \", selectedCavo.sezione || 'N/A']\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 717,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 716,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Grid, {\n            item: true,\n            xs: 12,\n            md: 4,\n            children: /*#__PURE__*/_jsxDEV(Typography, {\n              variant: \"body2\",\n              children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n                children: \"Metri teorici:\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 723,\n                columnNumber: 17\n              }, this), \" \", selectedCavo.metri_teorici || 'N/A', \" m\"]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 722,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 721,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Grid, {\n            item: true,\n            xs: 12,\n            md: 6,\n            children: /*#__PURE__*/_jsxDEV(Typography, {\n              variant: \"body2\",\n              children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n                children: \"Ubicazione partenza:\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 728,\n                columnNumber: 17\n              }, this), \" \", selectedCavo.ubicazione_partenza || 'N/A']\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 727,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 726,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Grid, {\n            item: true,\n            xs: 12,\n            md: 6,\n            children: /*#__PURE__*/_jsxDEV(Typography, {\n              variant: \"body2\",\n              children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n                children: \"Ubicazione arrivo:\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 733,\n                columnNumber: 17\n              }, this), \" \", selectedCavo.ubicazione_arrivo || 'N/A']\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 732,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 731,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Grid, {\n            item: true,\n            xs: 12,\n            children: /*#__PURE__*/_jsxDEV(Typography, {\n              variant: \"body2\",\n              children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n                children: \"Stato:\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 738,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(Chip, {\n                label: selectedCavo.stato_installazione || 'N/D',\n                size: \"small\",\n                color: getCableStateColor(selectedCavo.stato_installazione),\n                variant: \"outlined\",\n                sx: {\n                  ml: 1\n                }\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 739,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 737,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 736,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 710,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 706,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Divider, {\n        sx: {\n          mb: 3\n        }\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 751,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Grid, {\n        container: true,\n        spacing: 3,\n        children: [/*#__PURE__*/_jsxDEV(Grid, {\n          item: true,\n          xs: 12,\n          md: 6,\n          children: /*#__PURE__*/_jsxDEV(TextField, {\n            fullWidth: true,\n            label: \"Metri posati\",\n            name: \"metri_posati\",\n            value: formData.metri_posati,\n            onChange: handleInputChange,\n            type: \"number\",\n            error: !!formErrors.metri_posati,\n            helperText: formErrors.metri_posati || formWarnings.metri_posati && /*#__PURE__*/_jsxDEV(\"span\", {\n              style: {\n                color: 'orange'\n              },\n              children: formWarnings.metri_posati\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 764,\n              columnNumber: 17\n            }, this),\n            disabled: saving,\n            InputProps: {\n              inputProps: {\n                min: 0,\n                step: 0.1\n              }\n            }\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 755,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 754,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Grid, {\n          item: true,\n          xs: 12,\n          md: 6,\n          children: /*#__PURE__*/_jsxDEV(FormControl, {\n            fullWidth: true,\n            error: !!formErrors.id_bobina,\n            children: [/*#__PURE__*/_jsxDEV(InputLabel, {\n              children: \"Bobina\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 774,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Select, {\n              name: \"id_bobina\",\n              value: formData.id_bobina,\n              onChange: handleInputChange,\n              disabled: saving || bobineLoading,\n              children: [/*#__PURE__*/_jsxDEV(MenuItem, {\n                value: \"BOBINA_VUOTA\",\n                sx: {\n                  fontWeight: 'bold',\n                  color: '#2e7d32',\n                  bgcolor: '#f1f8e9'\n                },\n                children: \"BOBINA VUOTA (Cavo posato senza bobina)\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 782,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(Divider, {}, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 787,\n                columnNumber: 17\n              }, this), bobineLoading ? /*#__PURE__*/_jsxDEV(MenuItem, {\n                disabled: true,\n                children: /*#__PURE__*/_jsxDEV(Box, {\n                  sx: {\n                    display: 'flex',\n                    alignItems: 'center'\n                  },\n                  children: [/*#__PURE__*/_jsxDEV(CircularProgress, {\n                    size: 20,\n                    sx: {\n                      mr: 1\n                    }\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 793,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                    variant: \"caption\",\n                    children: \"Caricamento bobine...\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 794,\n                    columnNumber: 23\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 792,\n                  columnNumber: 21\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 791,\n                columnNumber: 19\n              }, this) : compatibleBobine.length === 0 ? /*#__PURE__*/_jsxDEV(MenuItem, {\n                disabled: true,\n                children: /*#__PURE__*/_jsxDEV(Typography, {\n                  variant: \"caption\",\n                  color: \"text.secondary\",\n                  children: \"Nessuna bobina compatibile disponibile. Utilizzare BOBINA VUOTA.\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 801,\n                  columnNumber: 21\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 800,\n                columnNumber: 19\n              }, this) : /*#__PURE__*/_jsxDEV(MenuItem, {\n                disabled: true,\n                children: /*#__PURE__*/_jsxDEV(Typography, {\n                  variant: \"caption\",\n                  children: [\"Bobine compatibili (\", compatibleBobine.length, \")\"]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 807,\n                  columnNumber: 21\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 806,\n                columnNumber: 19\n              }, this), !bobineLoading && (() => {\n                console.log('Rendering Select - Bobine compatibili:', compatibleBobine);\n                console.log('Rendering Select - Numero di bobine compatibili:', compatibleBobine.length);\n                return compatibleBobine.map(bobina => {\n                  console.log('Rendering bobina compatibile:', bobina);\n                  return /*#__PURE__*/_jsxDEV(MenuItem, {\n                    value: bobina.id_bobina,\n                    children: [bobina.id_bobina, \" - \", bobina.tipologia, \" - \", bobina.metri_residui, \"m\"]\n                  }, bobina.id_bobina, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 821,\n                    columnNumber: 23\n                  }, this);\n                });\n              })(), /*#__PURE__*/_jsxDEV(Divider, {}, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 830,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(MenuItem, {\n                disabled: true,\n                children: /*#__PURE__*/_jsxDEV(Typography, {\n                  variant: \"caption\",\n                  sx: {\n                    fontWeight: 'bold',\n                    color: '#ff9800'\n                  },\n                  children: \"TUTTE LE BOBINE DISPONIBILI (Ignora compatibilit\\xE0)\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 834,\n                  columnNumber: 19\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 833,\n                columnNumber: 17\n              }, this), bobine.filter(bobina => determineReelState(bobina.metri_residui, bobina.metri_totali) !== REEL_STATES.TERMINATA).map(bobina => /*#__PURE__*/_jsxDEV(MenuItem, {\n                value: bobina.id_bobina,\n                children: [bobina.id_bobina, \" - \", bobina.tipologia, \" - \", bobina.sezione, \" - \", bobina.metri_residui, \"m\"]\n              }, bobina.id_bobina, true, {\n                fileName: _jsxFileName,\n                lineNumber: 841,\n                columnNumber: 19\n              }, this))]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 775,\n              columnNumber: 15\n            }, this), formErrors.id_bobina && /*#__PURE__*/_jsxDEV(Typography, {\n              variant: \"caption\",\n              color: \"error\",\n              children: formErrors.id_bobina\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 847,\n              columnNumber: 17\n            }, this), formWarnings.id_bobina && /*#__PURE__*/_jsxDEV(Typography, {\n              variant: \"caption\",\n              sx: {\n                color: 'orange'\n              },\n              children: formWarnings.id_bobina\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 852,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(Typography, {\n              variant: \"caption\",\n              color: \"text.secondary\",\n              sx: {\n                mt: 1\n              },\n              children: \"Seleziona una bobina o usa BOBINA VUOTA se il cavo \\xE8 stato posato senza una bobina specifica.\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 857,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 773,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 772,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 753,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Box, {\n        sx: {\n          mt: 3,\n          display: 'flex',\n          justifyContent: 'space-between'\n        },\n        children: [/*#__PURE__*/_jsxDEV(Button, {\n          variant: \"outlined\",\n          color: \"secondary\",\n          onClick: () => {\n            setSelectedCavo(null);\n            setFormData({\n              id_cavo: '',\n              metri_posati: '',\n              id_bobina: ''\n            });\n          },\n          disabled: saving,\n          children: \"Annulla\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 865,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Button, {\n          variant: \"contained\",\n          color: \"primary\",\n          onClick: handleSave,\n          disabled: saving || Object.keys(formErrors).length > 0,\n          children: saving ? /*#__PURE__*/_jsxDEV(CircularProgress, {\n            size: 24\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 886,\n            columnNumber: 23\n          }, this) : 'Salva'\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 880,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 864,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 697,\n      columnNumber: 7\n    }, this);\n  };\n  return /*#__PURE__*/_jsxDEV(Box, {\n    children: [!selectedCavo && renderCaviTable(), renderForm(), /*#__PURE__*/_jsxDEV(IncompatibleReelDialog, {\n      open: showIncompatibleReelDialog,\n      onClose: () => setShowIncompatibleReelDialog(false),\n      cavo: incompatibleReelData.cavo,\n      bobina: incompatibleReelData.bobina,\n      onUpdateCavo: handleUpdateCavoForCompatibility,\n      onSelectAnotherReel: () => {\n        setShowIncompatibleReelDialog(false);\n        setFormData(prev => ({\n          ...prev,\n          id_bobina: ''\n        }));\n      }\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 902,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Dialog, {\n      open: showAlreadyLaidDialog,\n      onClose: handleCloseAlreadyLaidDialog,\n      children: [/*#__PURE__*/_jsxDEV(DialogTitle, {\n        children: \"Cavo gi\\xE0 posato\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 916,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(DialogContent, {\n        children: [/*#__PURE__*/_jsxDEV(DialogContentText, {\n          children: [\"Il cavo \", alreadyLaidCavo === null || alreadyLaidCavo === void 0 ? void 0 : alreadyLaidCavo.id_cavo, \" \\xE8 gi\\xE0 stato posato.\"]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 918,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Box, {\n          sx: {\n            mt: 2\n          },\n          children: [/*#__PURE__*/_jsxDEV(Typography, {\n            variant: \"body2\",\n            gutterBottom: true,\n            children: \"Puoi:\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 922,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Typography, {\n            component: \"ul\",\n            variant: \"body2\",\n            children: [/*#__PURE__*/_jsxDEV(\"li\", {\n              children: \"Modificare la bobina associata\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 926,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"li\", {\n              children: \"Selezionare un altro cavo\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 927,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"li\", {\n              children: \"Annullare l'operazione\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 928,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 925,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 921,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 917,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(DialogActions, {\n        sx: {\n          p: 2,\n          justifyContent: 'space-between'\n        },\n        children: [/*#__PURE__*/_jsxDEV(Button, {\n          onClick: handleCloseAlreadyLaidDialog,\n          color: \"secondary\",\n          children: \"Annulla operazione\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 933,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Box, {\n          children: [/*#__PURE__*/_jsxDEV(Button, {\n            onClick: handleSelectAnotherCable,\n            color: \"primary\",\n            sx: {\n              mr: 1\n            },\n            children: \"Seleziona altro cavo\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 937,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Button, {\n            onClick: handleModifyReel,\n            variant: \"contained\",\n            color: \"primary\",\n            children: \"Modifica bobina\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 940,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 936,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 932,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 915,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 894,\n    columnNumber: 5\n  }, this);\n};\n_s(MetriPosatiSemplificatoForm, \"tIpFGuuJh3Bk4ST3cUZrN28YjYA=\", false, function () {\n  return [useNavigate];\n});\n_c = MetriPosatiSemplificatoForm;\nexport default MetriPosatiSemplificatoForm;\nvar _c;\n$RefreshReg$(_c, \"MetriPosatiSemplificatoForm\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "Box", "Typography", "TextField", "<PERSON><PERSON>", "Paper", "Table", "TableBody", "TableCell", "TableContainer", "TableHead", "TableRow", "CircularProgress", "<PERSON><PERSON>", "Chip", "Divider", "Grid", "FormControl", "InputLabel", "Select", "MenuItem", "Dialog", "DialogTitle", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "DialogActions", "DialogContentText", "useNavigate", "caviService", "parcoCaviService", "CABLE_STATES", "REEL_STATES", "determineCableState", "determineReelState", "canModifyCable", "isCableSpare", "isCableInstalled", "getCableStateColor", "getReelStateColor", "redirectToVisualizzaCavi", "IncompatibleReelDialog", "jsxDEV", "_jsxDEV", "Fragment", "_Fragment", "MetriPosatiSemplificatoForm", "cantiereId", "onSuccess", "onError", "_s", "navigate", "cavi", "<PERSON><PERSON><PERSON>", "bobine", "set<PERSON>ob<PERSON>", "selected<PERSON><PERSON><PERSON>", "setSelectedCavo", "formData", "setFormData", "id_cavo", "metri_posati", "id_bobina", "loading", "setLoading", "caviLoading", "setCaviLoading", "bob<PERSON><PERSON><PERSON><PERSON>", "setBobineLoading", "saving", "setSaving", "formErrors", "setFormErrors", "formWarnings", "setForm<PERSON><PERSON>nings", "showIncompatibleReelDialog", "setShowIncompatibleReelDialog", "incompatibleReelData", "setIncompatibleReelData", "cavo", "bobina", "showAlreadyLaidDialog", "setShowAlreadyLaidDialog", "alreadyLaidCavo", "setAlreadyLaidCavo", "loadCavi", "loadBobine", "caviData", "get<PERSON><PERSON>", "caviAttivi", "filter", "error", "console", "message", "log", "bobine<PERSON><PERSON>", "getBobine", "for<PERSON>ach", "tipologia", "sezione", "metri_residui", "stato_bobina", "handleCavoSelect", "metri_te<PERSON>ci", "stato_installazione", "handleInputChange", "e", "name", "value", "target", "prev", "validateField", "newErrors", "newWarnings", "isNaN", "parseFloat", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "find", "b", "validateForm", "Object", "keys", "length", "checkCompatibility", "tipologiaCompatibile", "sezioneCompatibile", "String", "handleSave", "updateMetri<PERSON><PERSON><PERSON>", "handleUpdateCavoForCompatibility", "updateCavoForCompatibility", "handleCloseAlreadyLaidDialog", "handleSelectAnotherCable", "handleModifyReel", "compatibleBobine", "setCompatibleBobine", "filtered", "filterCompatibleBobine", "cavoTipologiaNorm", "trim", "toLowerCase", "cavoSezioneNorm", "bobinaTipologiaNorm", "bobinaSezioneNorm", "tipologiaMatch", "sezioneMatch", "reelState", "metri_totali", "stateOk", "TERMINATA", "isCompatible", "getCompatibleBobine", "renderCaviTable", "sx", "display", "justifyContent", "my", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "severity", "mb", "component", "size", "bgcolor", "map", "isInstalled", "ubicazione_partenza", "ubicazione_arrivo", "label", "color", "variant", "onClick", "disabled", "renderForm", "compatibleBobineList", "p", "gutterBottom", "borderRadius", "fontWeight", "container", "spacing", "item", "xs", "md", "fullWidth", "onChange", "type", "InputProps", "inputProps", "min", "step", "mt", "ml", "helperText", "style", "alignItems", "mr", "open", "onClose", "onUpdateCavo", "onSelectAnotherReel", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/PycharmProjects/CMS/webapp/frontend/src/components/cavi/MetriPosatiSemplificatoForm.js"], "sourcesContent": ["import React, { useState, useEffect } from 'react';\nimport {\n  Box,\n  Typography,\n  TextField,\n  Button,\n  Paper,\n  Table,\n  TableBody,\n  TableCell,\n  TableContainer,\n  TableHead,\n  TableRow,\n  CircularProgress,\n  Alert,\n  Chip,\n  Divider,\n  Grid,\n  FormControl,\n  InputLabel,\n  Select,\n  MenuItem,\n  Dialog,\n  DialogTitle,\n  DialogContent,\n  DialogActions,\n  DialogContentText\n} from '@mui/material';\nimport { useNavigate } from 'react-router-dom';\nimport caviService from '../../services/caviService';\nimport parcoCaviService from '../../services/parcoCaviService';\nimport {\n  CABLE_STATES,\n  REEL_STATES,\n  determineCableState,\n  determineReelState,\n  canModifyCable,\n  isCableSpare,\n  isCableInstalled,\n  getCableStateColor,\n  getReelStateColor\n} from '../../utils/stateUtils';\nimport { redirectToVisualizzaCavi } from '../../utils/navigationUtils';\nimport IncompatibleReelDialog from './IncompatibleReelDialog';\n\n/**\n * Componente per l'inserimento dei metri posati di un cavo\n * Versione ultra-semplificata con workflow compresso in un'unica pagina\n *\n * @param {Object} props - Proprietà del componente\n * @param {string} props.cantiereId - ID del cantiere\n * @param {Function} props.onSuccess - Funzione chiamata al successo dell'operazione\n * @param {Function} props.onError - Funzione chiamata in caso di errore\n */\nconst MetriPosatiSemplificatoForm = ({ cantiereId, onSuccess, onError }) => {\n  const navigate = useNavigate();\n\n  // Stati per i dati\n  const [cavi, setCavi] = useState([]);\n  const [bobine, setBobine] = useState([]);\n  const [selectedCavo, setSelectedCavo] = useState(null);\n  const [formData, setFormData] = useState({\n    id_cavo: '',\n    metri_posati: '',\n    id_bobina: ''\n  });\n\n  // Stati per il caricamento\n  const [loading, setLoading] = useState(false);\n  const [caviLoading, setCaviLoading] = useState(false);\n  const [bobineLoading, setBobineLoading] = useState(false);\n  const [saving, setSaving] = useState(false);\n\n  // Stati per la validazione\n  const [formErrors, setFormErrors] = useState({});\n  const [formWarnings, setFormWarnings] = useState({});\n\n  // Stati per i dialoghi speciali\n  const [showIncompatibleReelDialog, setShowIncompatibleReelDialog] = useState(false);\n  const [incompatibleReelData, setIncompatibleReelData] = useState({ cavo: null, bobina: null });\n  const [showAlreadyLaidDialog, setShowAlreadyLaidDialog] = useState(false);\n  const [alreadyLaidCavo, setAlreadyLaidCavo] = useState(null);\n\n  // Carica la lista dei cavi e delle bobine all'avvio\n  useEffect(() => {\n    loadCavi();\n    loadBobine();\n  }, [cantiereId]);\n\n  // Carica la lista dei cavi\n  const loadCavi = async () => {\n    try {\n      setCaviLoading(true);\n      const caviData = await caviService.getCavi(cantiereId);\n\n      // Filtra i cavi che non sono SPARE\n      const caviAttivi = caviData.filter(cavo => !isCableSpare(cavo));\n\n      setCavi(caviAttivi);\n    } catch (error) {\n      console.error('Errore nel caricamento dei cavi:', error);\n      onError('Errore nel caricamento dei cavi: ' + (error.message || 'Errore sconosciuto'));\n    } finally {\n      setCaviLoading(false);\n    }\n  };\n\n  // Carica la lista delle bobine\n  const loadBobine = async () => {\n    try {\n      setBobineLoading(true);\n      console.log('Caricamento bobine per cantiere:', cantiereId);\n      const bobineData = await parcoCaviService.getBobine(cantiereId);\n      console.log('Bobine caricate:', bobineData);\n      console.log('Dettaglio bobine:');\n      bobineData.forEach(bobina => {\n        console.log(`Bobina ${bobina.id_bobina}:`, {\n          tipologia: bobina.tipologia,\n          sezione: bobina.sezione,\n          metri_residui: bobina.metri_residui,\n          stato_bobina: bobina.stato_bobina\n        });\n      });\n      setBobine(bobineData);\n    } catch (error) {\n      console.error('Errore nel caricamento delle bobine:', error);\n      onError('Errore nel caricamento delle bobine: ' + (error.message || 'Errore sconosciuto'));\n    } finally {\n      setBobineLoading(false);\n    }\n  };\n\n  // Gestisce la selezione di un cavo\n  const handleCavoSelect = (cavo) => {\n    console.log('Cavo selezionato:', cavo);\n    console.log('Dettaglio cavo:', {\n      id_cavo: cavo.id_cavo,\n      tipologia: cavo.tipologia,\n      sezione: cavo.sezione,\n      metri_teorici: cavo.metri_teorici,\n      stato_installazione: cavo.stato_installazione\n    });\n\n    // Verifica se il cavo è già posato\n    if (isCableInstalled(cavo)) {\n      console.log('Cavo già posato, mostro dialog');\n      setAlreadyLaidCavo(cavo);\n      setShowAlreadyLaidDialog(true);\n      return;\n    }\n\n    setSelectedCavo(cavo);\n    setFormData({\n      id_cavo: cavo.id_cavo,\n      metri_posati: '',\n      id_bobina: ''\n    });\n    setFormErrors({});\n    setFormWarnings({});\n\n    // Log per debug - verifica se ci sono bobine compatibili\n    console.log('Verifica bobine compatibili per il cavo selezionato...');\n  };\n\n  // Gestisce la modifica dei campi del form\n  const handleInputChange = (e) => {\n    const { name, value } = e.target;\n    setFormData(prev => ({ ...prev, [name]: value }));\n\n    // Validazione in tempo reale\n    validateField(name, value);\n  };\n\n  // Validazione di un singolo campo\n  const validateField = (name, value) => {\n    const newErrors = { ...formErrors };\n    const newWarnings = { ...formWarnings };\n\n    if (name === 'metri_posati') {\n      // Validazione metri posati\n      if (value === '') {\n        newErrors.metri_posati = 'I metri posati sono obbligatori';\n      } else if (isNaN(value) || parseFloat(value) < 0) {\n        newErrors.metri_posati = 'I metri posati devono essere un numero positivo';\n      } else {\n        delete newErrors.metri_posati;\n\n        // Avvisi sui metri posati\n        const metriPosati = parseFloat(value);\n        if (selectedCavo && metriPosati > selectedCavo.metri_teorici) {\n          newWarnings.metri_posati = `I metri posati (${metriPosati}) superano i metri teorici (${selectedCavo.metri_teorici})`;\n        } else {\n          delete newWarnings.metri_posati;\n        }\n\n        // Avvisi sulla bobina selezionata\n        if (formData.id_bobina && formData.id_bobina !== 'BOBINA_VUOTA') {\n          const selectedBobina = bobine.find(b => b.id_bobina === formData.id_bobina);\n          if (selectedBobina && metriPosati > selectedBobina.metri_residui) {\n            newWarnings.id_bobina = `I metri posati (${metriPosati}) superano i metri residui della bobina (${selectedBobina.metri_residui})`;\n          } else {\n            delete newWarnings.id_bobina;\n          }\n        }\n      }\n    }\n\n    if (name === 'id_bobina') {\n      // Validazione bobina\n      if (value === '') {\n        newErrors.id_bobina = 'La bobina è obbligatoria';\n      } else {\n        delete newErrors.id_bobina;\n\n        // Avvisi sulla bobina selezionata\n        if (value !== 'BOBINA_VUOTA' && formData.metri_posati) {\n          const metriPosati = parseFloat(formData.metri_posati);\n          const selectedBobina = bobine.find(b => b.id_bobina === value);\n          if (selectedBobina && metriPosati > selectedBobina.metri_residui) {\n            newWarnings.id_bobina = `I metri posati (${metriPosati}) superano i metri residui della bobina (${selectedBobina.metri_residui})`;\n          } else {\n            delete newWarnings.id_bobina;\n          }\n        }\n      }\n    }\n\n    setFormErrors(newErrors);\n    setFormWarnings(newWarnings);\n  };\n\n  // Validazione completa del form\n  const validateForm = () => {\n    const newErrors = {};\n\n    // Validazione metri posati\n    if (!formData.metri_posati) {\n      newErrors.metri_posati = 'I metri posati sono obbligatori';\n    } else if (isNaN(formData.metri_posati) || parseFloat(formData.metri_posati) < 0) {\n      newErrors.metri_posati = 'I metri posati devono essere un numero positivo';\n    }\n\n    // Validazione bobina\n    if (!formData.id_bobina) {\n      newErrors.id_bobina = 'La bobina è obbligatoria';\n    }\n\n    setFormErrors(newErrors);\n    return Object.keys(newErrors).length === 0;\n  };\n\n  // Verifica la compatibilità tra cavo e bobina\n  const checkCompatibility = () => {\n    if (formData.id_bobina === 'BOBINA_VUOTA') {\n      return true; // BOBINA_VUOTA è sempre compatibile\n    }\n\n    const selectedBobina = bobine.find(b => b.id_bobina === formData.id_bobina);\n    if (!selectedBobina) {\n      return false;\n    }\n\n    // Verifica compatibilità tipologia\n    const tipologiaCompatibile = selectedCavo.tipologia === selectedBobina.tipologia;\n\n    // Verifica compatibilità sezione\n    const sezioneCompatibile = String(selectedCavo.sezione) === String(selectedBobina.sezione);\n\n    return tipologiaCompatibile && sezioneCompatibile;\n  };\n\n  // Gestisce il salvataggio dei dati\n  const handleSave = async () => {\n    // Se non ci sono bobine disponibili, imposta automaticamente BOBINA_VUOTA\n    if (bobine.length === 0 && !bobineLoading) {\n      if (!formData.metri_posati || isNaN(parseFloat(formData.metri_posati)) || parseFloat(formData.metri_posati) <= 0) {\n        setFormErrors({\n          ...formErrors,\n          metri_posati: 'I metri posati sono obbligatori e devono essere maggiori di zero'\n        });\n        return;\n      }\n\n      // Imposta BOBINA_VUOTA e procedi con il salvataggio\n      formData.id_bobina = 'BOBINA_VUOTA';\n    } else {\n      // Validazione completa\n      if (!validateForm()) {\n        return;\n      }\n\n      // Verifica compatibilità\n      if (!checkCompatibility()) {\n        // Mostra dialog per incompatibilità\n        const selectedBobina = bobine.find(b => b.id_bobina === formData.id_bobina);\n        setIncompatibleReelData({\n          cavo: selectedCavo,\n          bobina: selectedBobina\n        });\n        setShowIncompatibleReelDialog(true);\n        return;\n      }\n    }\n\n    // Procedi con il salvataggio\n    try {\n      setSaving(true);\n\n      // Converti metri posati in numero\n      const metriPosati = parseFloat(formData.metri_posati);\n\n      // Chiamata API\n      console.log('Invio richiesta updateMetriPosati con i seguenti parametri:');\n      console.log('- cantiereId:', cantiereId);\n      console.log('- id_cavo:', formData.id_cavo);\n      console.log('- metri_posati:', metriPosati);\n      console.log('- id_bobina:', formData.id_bobina);\n\n      // Imposta sempre forceOver a true per evitare blocchi quando la bobina va in OVER\n      await caviService.updateMetriPosati(\n        cantiereId,\n        formData.id_cavo,\n        metriPosati,\n        formData.id_bobina,\n        true // Forza sempre a true per evitare blocchi\n      );\n\n      // Mostra messaggio di successo\n      onSuccess('Metri posati aggiornati con successo');\n\n      // Resetta il form\n      setSelectedCavo(null);\n      setFormData({\n        id_cavo: '',\n        metri_posati: '',\n        id_bobina: ''\n      });\n\n      // Ricarica i dati\n      loadCavi();\n      loadBobine();\n    } catch (error) {\n      console.error('Errore durante il salvataggio:', error);\n      onError('Errore durante il salvataggio: ' + (error.message || 'Errore sconosciuto'));\n    } finally {\n      setSaving(false);\n    }\n  };\n\n  // Gestisce l'aggiornamento del cavo per compatibilità\n  const handleUpdateCavoForCompatibility = async () => {\n    try {\n      setSaving(true);\n      setShowIncompatibleReelDialog(false);\n\n      const { cavo, bobina } = incompatibleReelData;\n\n      // Aggiorna il cavo per renderlo compatibile con la bobina\n      await caviService.updateCavoForCompatibility(\n        cantiereId,\n        cavo.id_cavo,\n        {\n          id_bobina: bobina.id_bobina,\n          tipologia: bobina.tipologia,\n          sezione: bobina.sezione\n        }\n      );\n\n      // Procedi con l'aggiornamento dei metri posati\n      await caviService.updateMetriPosati(\n        cantiereId,\n        formData.id_cavo,\n        parseFloat(formData.metri_posati),\n        formData.id_bobina,\n        true // Forza sempre a true per evitare blocchi\n      );\n\n      // Mostra messaggio di successo\n      onSuccess('Cavo aggiornato e metri posati registrati con successo');\n\n      // Resetta il form\n      setSelectedCavo(null);\n      setFormData({\n        id_cavo: '',\n        metri_posati: '',\n        id_bobina: ''\n      });\n\n      // Ricarica i dati\n      loadCavi();\n      loadBobine();\n    } catch (error) {\n      console.error('Errore durante l\\'aggiornamento del cavo:', error);\n      onError('Errore durante l\\'aggiornamento del cavo: ' + (error.message || 'Errore sconosciuto'));\n    } finally {\n      setSaving(false);\n    }\n  };\n\n  // Gestisce la chiusura del dialogo per cavi già posati\n  const handleCloseAlreadyLaidDialog = () => {\n    setShowAlreadyLaidDialog(false);\n    setAlreadyLaidCavo(null);\n  };\n\n  // Gestisce la selezione di un altro cavo\n  const handleSelectAnotherCable = () => {\n    handleCloseAlreadyLaidDialog();\n  };\n\n  // Gestisce l'opzione di modificare la bobina di un cavo già posato\n  const handleModifyReel = () => {\n    if (alreadyLaidCavo) {\n      navigate(`/dashboard/cavi/posa/modifica-bobina?cavoId=${alreadyLaidCavo.id_cavo}`);\n    }\n    handleCloseAlreadyLaidDialog();\n  };\n\n  // Stato per le bobine compatibili\n  const [compatibleBobine, setCompatibleBobine] = useState([]);\n\n  // Aggiorna le bobine compatibili quando viene selezionato un cavo o cambiano le bobine disponibili\n  useEffect(() => {\n    if (selectedCavo) {\n      // Filtra le bobine compatibili localmente\n      const filtered = filterCompatibleBobine(selectedCavo);\n      setCompatibleBobine(filtered);\n    } else {\n      setCompatibleBobine([]);\n    }\n  }, [selectedCavo, bobine]);\n\n  // Filtra le bobine compatibili localmente usando lo stesso approccio di QuickAddCablesDialog\n  const filterCompatibleBobine = (cavo) => {\n    if (!cavo) return [];\n\n    console.log('Filtrando bobine compatibili per cavo:', cavo);\n    console.log('Bobine disponibili:', bobine);\n    console.log('Numero di bobine disponibili:', bobine.length);\n\n    // Verifica se ci sono bobine disponibili\n    if (bobine.length === 0) {\n      console.log('ATTENZIONE: Nessuna bobina disponibile nel cantiere!');\n      return [];\n    }\n\n    // Filtra le bobine compatibili con il cavo\n    const filtered = bobine.filter(bobina => {\n      // Usa un approccio più flessibile per il confronto\n      // 1. Normalizza tipologia (trim e lowercase)\n      // 2. Normalizza sezione (trim e conversione in stringa)\n      // 3. Verifica che la bobina non sia terminata\n      const cavoTipologiaNorm = String(cavo.tipologia || '').trim().toLowerCase();\n      const cavoSezioneNorm = String(cavo.sezione || '').trim();\n\n      const bobinaTipologiaNorm = String(bobina.tipologia || '').trim().toLowerCase();\n      const bobinaSezioneNorm = String(bobina.sezione || '').trim();\n\n      const tipologiaMatch = bobinaTipologiaNorm === cavoTipologiaNorm;\n      const sezioneMatch = bobinaSezioneNorm === cavoSezioneNorm;\n      const reelState = determineReelState(bobina.metri_residui, bobina.metri_totali);\n      const stateOk = reelState !== REEL_STATES.TERMINATA;\n\n      const isCompatible = tipologiaMatch && sezioneMatch && stateOk;\n\n      // Log ancora più dettagliati\n      console.log(`Confronto dettagliato per bobina ${bobina.id_bobina}:`);\n      console.log(`- Tipologia bobina (originale): \"${bobina.tipologia}\", tipo: ${typeof bobina.tipologia}`);\n      console.log(`- Tipologia cavo (originale): \"${cavo.tipologia}\", tipo: ${typeof cavo.tipologia}`);\n      console.log(`- Tipologia bobina (normalizzata): \"${bobinaTipologiaNorm}\"`);\n      console.log(`- Tipologia cavo (normalizzata): \"${cavoTipologiaNorm}\"`);\n      console.log(`- Sezione bobina (originale): \"${bobina.sezione}\", tipo: ${typeof bobina.sezione}`);\n      console.log(`- Sezione cavo (originale): \"${cavo.sezione}\", tipo: ${typeof cavo.sezione}`);\n      console.log(`- Sezione bobina (normalizzata): \"${bobinaSezioneNorm}\"`);\n      console.log(`- Sezione cavo (normalizzata): \"${cavoSezioneNorm}\"`);\n\n      // Log dettagliato per debug\n      console.log(`Bobina ${bobina.id_bobina}:`, {\n        'Tipologia bobina': `\"${bobina.tipologia}\"`,\n        'Tipologia cavo': `\"${cavo.tipologia}\"`,\n        'Tipologie uguali?': tipologiaMatch,\n        'Sezione bobina': `\"${String(bobina.sezione)}\"`,\n        'Sezione cavo': `\"${String(cavo.sezione)}\"`,\n        'Sezioni uguali?': sezioneMatch,\n        'Stato bobina': reelState,\n        'Stato OK?': stateOk,\n        'Compatibile?': isCompatible\n      });\n\n      return isCompatible;\n    });\n\n    console.log('Bobine compatibili trovate:', filtered.length);\n    if (filtered.length > 0) {\n      console.log('Prima bobina compatibile:', filtered[0]);\n    } else {\n      console.log('ATTENZIONE: Nessuna bobina compatibile trovata!');\n    }\n\n    return filtered;\n  };\n\n  // Funzione di utilità per ottenere le bobine compatibili (usata nel rendering)\n  const getCompatibleBobine = () => {\n    return compatibleBobine;\n  };\n\n  // Renderizza la tabella dei cavi\n  const renderCaviTable = () => {\n    if (caviLoading) {\n      return (\n        <Box sx={{ display: 'flex', justifyContent: 'center', my: 4 }}>\n          <CircularProgress />\n        </Box>\n      );\n    }\n\n    if (cavi.length === 0) {\n      return (\n        <Alert severity=\"info\" sx={{ my: 2 }}>\n          Nessun cavo disponibile per questo cantiere.\n        </Alert>\n      );\n    }\n\n    return (\n      <>\n        <Alert severity=\"info\" sx={{ mb: 2 }}>\n          Seleziona un cavo dalla tabella per inserire i metri posati. I cavi già installati sono disabilitati.\n        </Alert>\n\n        <TableContainer component={Paper} sx={{ mb: 3 }}>\n          <Table size=\"small\">\n            <TableHead>\n              <TableRow sx={{ bgcolor: '#e3f2fd' }}>\n                <TableCell>ID Cavo</TableCell>\n                <TableCell>Tipologia</TableCell>\n                <TableCell>Ubicazione</TableCell>\n                <TableCell>Metri Teorici</TableCell>\n                <TableCell>Stato</TableCell>\n                <TableCell>Azioni</TableCell>\n              </TableRow>\n            </TableHead>\n            <TableBody>\n              {cavi.map((cavo) => {\n                const isInstalled = isCableInstalled(cavo);\n                return (\n                  <TableRow\n                    key={cavo.id_cavo}\n                    sx={{\n                      bgcolor: isInstalled ? '#f5f5f5' : 'inherit',\n                      '&:hover': { bgcolor: isInstalled ? '#f5f5f5' : '#f1f8e9' }\n                    }}\n                  >\n                    <TableCell><strong>{cavo.id_cavo}</strong></TableCell>\n                    <TableCell>{cavo.tipologia || 'N/A'}</TableCell>\n                    <TableCell>Da: {cavo.ubicazione_partenza || 'N/A'}<br/>A: {cavo.ubicazione_arrivo || 'N/A'}</TableCell>\n                    <TableCell>{cavo.metri_teorici || 'N/A'} m</TableCell>\n                    <TableCell>\n                      <Chip\n                        label={cavo.stato_installazione || 'N/D'}\n                        size=\"small\"\n                        color={getCableStateColor(cavo.stato_installazione)}\n                        variant=\"outlined\"\n                      />\n                    </TableCell>\n                    <TableCell>\n                      <Button\n                        size=\"small\"\n                        variant=\"contained\"\n                        color=\"primary\"\n                        onClick={() => handleCavoSelect(cavo)}\n                        disabled={isInstalled}\n                      >\n                        {isInstalled ? 'Già installato' : 'Seleziona'}\n                      </Button>\n                    </TableCell>\n                  </TableRow>\n                );\n              })}\n            </TableBody>\n          </Table>\n        </TableContainer>\n      </>\n    );\n  };\n\n  // Renderizza il form per inserimento metri e selezione bobina\n  const renderForm = () => {\n    if (!selectedCavo) return null;\n\n    // Log per debug - verifica le bobine compatibili nel rendering\n    const compatibleBobineList = getCompatibleBobine();\n    console.log('Rendering form - Bobine compatibili:', compatibleBobineList);\n    console.log('Rendering form - Numero di bobine compatibili:', compatibleBobineList.length);\n\n    // Verifica se ci sono bobine disponibili\n    if (bobine.length === 0 && !bobineLoading) {\n      return (\n        <Paper sx={{ p: 3 }}>\n          <Typography variant=\"h6\" gutterBottom>\n            Inserimento metri posati\n          </Typography>\n\n          <Alert severity=\"warning\" sx={{ mb: 3 }}>\n            Non ci sono bobine disponibili nel cantiere. Puoi comunque registrare i metri posati utilizzando l'opzione \"BOBINA VUOTA\".\n          </Alert>\n\n          <Box sx={{ mb: 3, p: 2, bgcolor: '#f5f5f5', borderRadius: 1 }}>\n            <Typography variant=\"subtitle1\" gutterBottom fontWeight=\"bold\" color=\"primary\">\n              Cavo selezionato: {selectedCavo.id_cavo}\n            </Typography>\n            <Grid container spacing={2}>\n              <Grid item xs={12} md={4}>\n                <Typography variant=\"body2\">\n                  <strong>Tipologia:</strong> {selectedCavo.tipologia || 'N/A'}\n                </Typography>\n              </Grid>\n              <Grid item xs={12} md={4}>\n                <Typography variant=\"body2\">\n                  <strong>Formazione:</strong> {selectedCavo.sezione || 'N/A'}\n                </Typography>\n              </Grid>\n              <Grid item xs={12} md={4}>\n                <Typography variant=\"body2\">\n                  <strong>Metri teorici:</strong> {selectedCavo.metri_teorici || 'N/A'} m\n                </Typography>\n              </Grid>\n            </Grid>\n          </Box>\n\n          <Grid container spacing={3}>\n            <Grid item xs={12} md={6}>\n              <TextField\n                fullWidth\n                label=\"Metri posati\"\n                name=\"metri_posati\"\n                value={formData.metri_posati}\n                onChange={handleInputChange}\n                type=\"number\"\n                InputProps={{\n                  inputProps: { min: 0, step: 0.1 }\n                }}\n              />\n            </Grid>\n            <Grid item xs={12} md={6}>\n              <FormControl fullWidth>\n                <InputLabel>Bobina</InputLabel>\n                <Select\n                  name=\"id_bobina\"\n                  value=\"BOBINA_VUOTA\"\n                  disabled\n                >\n                  <MenuItem value=\"BOBINA_VUOTA\" sx={{ fontWeight: 'bold', color: '#2e7d32', bgcolor: '#f1f8e9' }}>\n                    BOBINA VUOTA (Cavo posato senza bobina)\n                  </MenuItem>\n                </Select>\n                <Typography variant=\"caption\" color=\"text.secondary\" sx={{ mt: 1 }}>\n                  Non ci sono bobine disponibili. Verrà utilizzata l'opzione BOBINA VUOTA.\n                </Typography>\n              </FormControl>\n            </Grid>\n          </Grid>\n\n          <Box sx={{ mt: 3, display: 'flex', justifyContent: 'space-between' }}>\n            <Button\n              variant=\"outlined\"\n              color=\"secondary\"\n              onClick={() => {\n                setSelectedCavo(null);\n                setFormData({\n                  id_cavo: '',\n                  metri_posati: '',\n                  id_bobina: ''\n                });\n              }}\n              disabled={saving}\n            >\n              Annulla\n            </Button>\n            <Button\n              variant=\"contained\"\n              color=\"primary\"\n              onClick={() => handleSave()}\n              disabled={saving || !formData.metri_posati}\n            >\n              {saving ? <CircularProgress size={24} /> : 'Salva'}\n            </Button>\n          </Box>\n        </Paper>\n      );\n    }\n\n    const compatibleBobine = getCompatibleBobine();\n\n    return (\n      <Paper sx={{ p: 3 }}>\n        <Typography variant=\"h6\" gutterBottom>\n          Inserimento metri posati\n        </Typography>\n\n        <Alert severity=\"info\" sx={{ mb: 3 }}>\n          Inserisci i metri posati per il cavo selezionato e associa una bobina. Se il cavo è stato posato senza una bobina specifica, seleziona <strong>BOBINA VUOTA</strong>.\n        </Alert>\n\n        <Box sx={{ mb: 3, p: 2, bgcolor: '#f5f5f5', borderRadius: 1 }}>\n          <Typography variant=\"subtitle1\" gutterBottom fontWeight=\"bold\" color=\"primary\">\n            Cavo selezionato: {selectedCavo.id_cavo}\n          </Typography>\n          <Grid container spacing={2}>\n            <Grid item xs={12} md={4}>\n              <Typography variant=\"body2\">\n                <strong>Tipologia:</strong> {selectedCavo.tipologia || 'N/A'}\n              </Typography>\n            </Grid>\n            <Grid item xs={12} md={4}>\n              <Typography variant=\"body2\">\n                <strong>Formazione:</strong> {selectedCavo.sezione || 'N/A'}\n              </Typography>\n            </Grid>\n            <Grid item xs={12} md={4}>\n              <Typography variant=\"body2\">\n                <strong>Metri teorici:</strong> {selectedCavo.metri_teorici || 'N/A'} m\n              </Typography>\n            </Grid>\n            <Grid item xs={12} md={6}>\n              <Typography variant=\"body2\">\n                <strong>Ubicazione partenza:</strong> {selectedCavo.ubicazione_partenza || 'N/A'}\n              </Typography>\n            </Grid>\n            <Grid item xs={12} md={6}>\n              <Typography variant=\"body2\">\n                <strong>Ubicazione arrivo:</strong> {selectedCavo.ubicazione_arrivo || 'N/A'}\n              </Typography>\n            </Grid>\n            <Grid item xs={12}>\n              <Typography variant=\"body2\">\n                <strong>Stato:</strong>\n                <Chip\n                  label={selectedCavo.stato_installazione || 'N/D'}\n                  size=\"small\"\n                  color={getCableStateColor(selectedCavo.stato_installazione)}\n                  variant=\"outlined\"\n                  sx={{ ml: 1 }}\n                />\n              </Typography>\n            </Grid>\n          </Grid>\n        </Box>\n\n        <Divider sx={{ mb: 3 }} />\n\n        <Grid container spacing={3}>\n          <Grid item xs={12} md={6}>\n            <TextField\n              fullWidth\n              label=\"Metri posati\"\n              name=\"metri_posati\"\n              value={formData.metri_posati}\n              onChange={handleInputChange}\n              type=\"number\"\n              error={!!formErrors.metri_posati}\n              helperText={formErrors.metri_posati || (formWarnings.metri_posati && (\n                <span style={{ color: 'orange' }}>{formWarnings.metri_posati}</span>\n              ))}\n              disabled={saving}\n              InputProps={{\n                inputProps: { min: 0, step: 0.1 }\n              }}\n            />\n          </Grid>\n          <Grid item xs={12} md={6}>\n            <FormControl fullWidth error={!!formErrors.id_bobina}>\n              <InputLabel>Bobina</InputLabel>\n              <Select\n                name=\"id_bobina\"\n                value={formData.id_bobina}\n                onChange={handleInputChange}\n                disabled={saving || bobineLoading}\n              >\n                {/* Opzione BOBINA VUOTA sempre disponibile e in evidenza */}\n                <MenuItem value=\"BOBINA_VUOTA\" sx={{ fontWeight: 'bold', color: '#2e7d32', bgcolor: '#f1f8e9' }}>\n                  BOBINA VUOTA (Cavo posato senza bobina)\n                </MenuItem>\n\n                {/* Separatore */}\n                <Divider />\n\n                {/* Messaggio informativo */}\n                {bobineLoading ? (\n                  <MenuItem disabled>\n                    <Box sx={{ display: 'flex', alignItems: 'center' }}>\n                      <CircularProgress size={20} sx={{ mr: 1 }} />\n                      <Typography variant=\"caption\">\n                        Caricamento bobine...\n                      </Typography>\n                    </Box>\n                  </MenuItem>\n                ) : compatibleBobine.length === 0 ? (\n                  <MenuItem disabled>\n                    <Typography variant=\"caption\" color=\"text.secondary\">\n                      Nessuna bobina compatibile disponibile. Utilizzare BOBINA VUOTA.\n                    </Typography>\n                  </MenuItem>\n                ) : (\n                  <MenuItem disabled>\n                    <Typography variant=\"caption\">\n                      Bobine compatibili ({compatibleBobine.length})\n                    </Typography>\n                  </MenuItem>\n                )}\n\n                {/* Bobine compatibili */}\n                {!bobineLoading && (() => {\n                  console.log('Rendering Select - Bobine compatibili:', compatibleBobine);\n                  console.log('Rendering Select - Numero di bobine compatibili:', compatibleBobine.length);\n\n                  return compatibleBobine.map((bobina) => {\n                    console.log('Rendering bobina compatibile:', bobina);\n                    return (\n                      <MenuItem key={bobina.id_bobina} value={bobina.id_bobina}>\n                        {bobina.id_bobina} - {bobina.tipologia} - {bobina.metri_residui}m\n                      </MenuItem>\n                    );\n                  });\n                })()\n                }\n\n                {/* Separatore per tutte le bobine */}\n                <Divider />\n\n                {/* Titolo per tutte le bobine */}\n                <MenuItem disabled>\n                  <Typography variant=\"caption\" sx={{ fontWeight: 'bold', color: '#ff9800' }}>\n                    TUTTE LE BOBINE DISPONIBILI (Ignora compatibilità)\n                  </Typography>\n                </MenuItem>\n\n                {/* Mostra tutte le bobine disponibili */}\n                {bobine.filter(bobina => determineReelState(bobina.metri_residui, bobina.metri_totali) !== REEL_STATES.TERMINATA).map((bobina) => (\n                  <MenuItem key={bobina.id_bobina} value={bobina.id_bobina}>\n                    {bobina.id_bobina} - {bobina.tipologia} - {bobina.sezione} - {bobina.metri_residui}m\n                  </MenuItem>\n                ))}\n              </Select>\n              {formErrors.id_bobina && (\n                <Typography variant=\"caption\" color=\"error\">\n                  {formErrors.id_bobina}\n                </Typography>\n              )}\n              {formWarnings.id_bobina && (\n                <Typography variant=\"caption\" sx={{ color: 'orange' }}>\n                  {formWarnings.id_bobina}\n                </Typography>\n              )}\n              {/* Messaggio informativo sotto il campo */}\n              <Typography variant=\"caption\" color=\"text.secondary\" sx={{ mt: 1 }}>\n                Seleziona una bobina o usa BOBINA VUOTA se il cavo è stato posato senza una bobina specifica.\n              </Typography>\n            </FormControl>\n          </Grid>\n        </Grid>\n\n        <Box sx={{ mt: 3, display: 'flex', justifyContent: 'space-between' }}>\n          <Button\n            variant=\"outlined\"\n            color=\"secondary\"\n            onClick={() => {\n              setSelectedCavo(null);\n              setFormData({\n                id_cavo: '',\n                metri_posati: '',\n                id_bobina: ''\n              });\n            }}\n            disabled={saving}\n          >\n            Annulla\n          </Button>\n          <Button\n            variant=\"contained\"\n            color=\"primary\"\n            onClick={handleSave}\n            disabled={saving || Object.keys(formErrors).length > 0}\n          >\n            {saving ? <CircularProgress size={24} /> : 'Salva'}\n          </Button>\n        </Box>\n      </Paper>\n    );\n  };\n\n  return (\n    <Box>\n      {/* Tabella cavi */}\n      {!selectedCavo && renderCaviTable()}\n\n      {/* Form per inserimento metri e selezione bobina */}\n      {renderForm()}\n\n      {/* Dialog per bobine incompatibili */}\n      <IncompatibleReelDialog\n        open={showIncompatibleReelDialog}\n        onClose={() => setShowIncompatibleReelDialog(false)}\n        cavo={incompatibleReelData.cavo}\n        bobina={incompatibleReelData.bobina}\n        onUpdateCavo={handleUpdateCavoForCompatibility}\n        onSelectAnotherReel={() => {\n          setShowIncompatibleReelDialog(false);\n          setFormData(prev => ({ ...prev, id_bobina: '' }));\n        }}\n      />\n\n      {/* Dialog per cavi già posati */}\n      <Dialog open={showAlreadyLaidDialog} onClose={handleCloseAlreadyLaidDialog}>\n        <DialogTitle>Cavo già posato</DialogTitle>\n        <DialogContent>\n          <DialogContentText>\n            Il cavo {alreadyLaidCavo?.id_cavo} è già stato posato.\n          </DialogContentText>\n          <Box sx={{ mt: 2 }}>\n            <Typography variant=\"body2\" gutterBottom>\n              Puoi:\n            </Typography>\n            <Typography component=\"ul\" variant=\"body2\">\n              <li>Modificare la bobina associata</li>\n              <li>Selezionare un altro cavo</li>\n              <li>Annullare l'operazione</li>\n            </Typography>\n          </Box>\n        </DialogContent>\n        <DialogActions sx={{ p: 2, justifyContent: 'space-between' }}>\n          <Button onClick={handleCloseAlreadyLaidDialog} color=\"secondary\">\n            Annulla operazione\n          </Button>\n          <Box>\n            <Button onClick={handleSelectAnotherCable} color=\"primary\" sx={{ mr: 1 }}>\n              Seleziona altro cavo\n            </Button>\n            <Button onClick={handleModifyReel} variant=\"contained\" color=\"primary\">\n              Modifica bobina\n            </Button>\n          </Box>\n        </DialogActions>\n      </Dialog>\n    </Box>\n  );\n};\n\nexport default MetriPosatiSemplificatoForm;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAClD,SACEC,GAAG,EACHC,UAAU,EACVC,SAAS,EACTC,MAAM,EACNC,KAAK,EACLC,KAAK,EACLC,SAAS,EACTC,SAAS,EACTC,cAAc,EACdC,SAAS,EACTC,QAAQ,EACRC,gBAAgB,EAChBC,KAAK,EACLC,IAAI,EACJC,OAAO,EACPC,IAAI,EACJC,WAAW,EACXC,UAAU,EACVC,MAAM,EACNC,QAAQ,EACRC,MAAM,EACNC,WAAW,EACXC,aAAa,EACbC,aAAa,EACbC,iBAAiB,QACZ,eAAe;AACtB,SAASC,WAAW,QAAQ,kBAAkB;AAC9C,OAAOC,WAAW,MAAM,4BAA4B;AACpD,OAAOC,gBAAgB,MAAM,iCAAiC;AAC9D,SACEC,YAAY,EACZC,WAAW,EACXC,mBAAmB,EACnBC,kBAAkB,EAClBC,cAAc,EACdC,YAAY,EACZC,gBAAgB,EAChBC,kBAAkB,EAClBC,iBAAiB,QACZ,wBAAwB;AAC/B,SAASC,wBAAwB,QAAQ,6BAA6B;AACtE,OAAOC,sBAAsB,MAAM,0BAA0B;;AAE7D;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AARA,SAAAC,MAAA,IAAAC,OAAA,EAAAC,QAAA,IAAAC,SAAA;AASA,MAAMC,2BAA2B,GAAGA,CAAC;EAAEC,UAAU;EAAEC,SAAS;EAAEC;AAAQ,CAAC,KAAK;EAAAC,EAAA;EAC1E,MAAMC,QAAQ,GAAGvB,WAAW,CAAC,CAAC;;EAE9B;EACA,MAAM,CAACwB,IAAI,EAAEC,OAAO,CAAC,GAAGpD,QAAQ,CAAC,EAAE,CAAC;EACpC,MAAM,CAACqD,MAAM,EAAEC,SAAS,CAAC,GAAGtD,QAAQ,CAAC,EAAE,CAAC;EACxC,MAAM,CAACuD,YAAY,EAAEC,eAAe,CAAC,GAAGxD,QAAQ,CAAC,IAAI,CAAC;EACtD,MAAM,CAACyD,QAAQ,EAAEC,WAAW,CAAC,GAAG1D,QAAQ,CAAC;IACvC2D,OAAO,EAAE,EAAE;IACXC,YAAY,EAAE,EAAE;IAChBC,SAAS,EAAE;EACb,CAAC,CAAC;;EAEF;EACA,MAAM,CAACC,OAAO,EAAEC,UAAU,CAAC,GAAG/D,QAAQ,CAAC,KAAK,CAAC;EAC7C,MAAM,CAACgE,WAAW,EAAEC,cAAc,CAAC,GAAGjE,QAAQ,CAAC,KAAK,CAAC;EACrD,MAAM,CAACkE,aAAa,EAAEC,gBAAgB,CAAC,GAAGnE,QAAQ,CAAC,KAAK,CAAC;EACzD,MAAM,CAACoE,MAAM,EAAEC,SAAS,CAAC,GAAGrE,QAAQ,CAAC,KAAK,CAAC;;EAE3C;EACA,MAAM,CAACsE,UAAU,EAAEC,aAAa,CAAC,GAAGvE,QAAQ,CAAC,CAAC,CAAC,CAAC;EAChD,MAAM,CAACwE,YAAY,EAAEC,eAAe,CAAC,GAAGzE,QAAQ,CAAC,CAAC,CAAC,CAAC;;EAEpD;EACA,MAAM,CAAC0E,0BAA0B,EAAEC,6BAA6B,CAAC,GAAG3E,QAAQ,CAAC,KAAK,CAAC;EACnF,MAAM,CAAC4E,oBAAoB,EAAEC,uBAAuB,CAAC,GAAG7E,QAAQ,CAAC;IAAE8E,IAAI,EAAE,IAAI;IAAEC,MAAM,EAAE;EAAK,CAAC,CAAC;EAC9F,MAAM,CAACC,qBAAqB,EAAEC,wBAAwB,CAAC,GAAGjF,QAAQ,CAAC,KAAK,CAAC;EACzE,MAAM,CAACkF,eAAe,EAAEC,kBAAkB,CAAC,GAAGnF,QAAQ,CAAC,IAAI,CAAC;;EAE5D;EACAC,SAAS,CAAC,MAAM;IACdmF,QAAQ,CAAC,CAAC;IACVC,UAAU,CAAC,CAAC;EACd,CAAC,EAAE,CAACvC,UAAU,CAAC,CAAC;;EAEhB;EACA,MAAMsC,QAAQ,GAAG,MAAAA,CAAA,KAAY;IAC3B,IAAI;MACFnB,cAAc,CAAC,IAAI,CAAC;MACpB,MAAMqB,QAAQ,GAAG,MAAM1D,WAAW,CAAC2D,OAAO,CAACzC,UAAU,CAAC;;MAEtD;MACA,MAAM0C,UAAU,GAAGF,QAAQ,CAACG,MAAM,CAACX,IAAI,IAAI,CAAC3C,YAAY,CAAC2C,IAAI,CAAC,CAAC;MAE/D1B,OAAO,CAACoC,UAAU,CAAC;IACrB,CAAC,CAAC,OAAOE,KAAK,EAAE;MACdC,OAAO,CAACD,KAAK,CAAC,kCAAkC,EAAEA,KAAK,CAAC;MACxD1C,OAAO,CAAC,mCAAmC,IAAI0C,KAAK,CAACE,OAAO,IAAI,oBAAoB,CAAC,CAAC;IACxF,CAAC,SAAS;MACR3B,cAAc,CAAC,KAAK,CAAC;IACvB;EACF,CAAC;;EAED;EACA,MAAMoB,UAAU,GAAG,MAAAA,CAAA,KAAY;IAC7B,IAAI;MACFlB,gBAAgB,CAAC,IAAI,CAAC;MACtBwB,OAAO,CAACE,GAAG,CAAC,kCAAkC,EAAE/C,UAAU,CAAC;MAC3D,MAAMgD,UAAU,GAAG,MAAMjE,gBAAgB,CAACkE,SAAS,CAACjD,UAAU,CAAC;MAC/D6C,OAAO,CAACE,GAAG,CAAC,kBAAkB,EAAEC,UAAU,CAAC;MAC3CH,OAAO,CAACE,GAAG,CAAC,mBAAmB,CAAC;MAChCC,UAAU,CAACE,OAAO,CAACjB,MAAM,IAAI;QAC3BY,OAAO,CAACE,GAAG,CAAC,UAAUd,MAAM,CAAClB,SAAS,GAAG,EAAE;UACzCoC,SAAS,EAAElB,MAAM,CAACkB,SAAS;UAC3BC,OAAO,EAAEnB,MAAM,CAACmB,OAAO;UACvBC,aAAa,EAAEpB,MAAM,CAACoB,aAAa;UACnCC,YAAY,EAAErB,MAAM,CAACqB;QACvB,CAAC,CAAC;MACJ,CAAC,CAAC;MACF9C,SAAS,CAACwC,UAAU,CAAC;IACvB,CAAC,CAAC,OAAOJ,KAAK,EAAE;MACdC,OAAO,CAACD,KAAK,CAAC,sCAAsC,EAAEA,KAAK,CAAC;MAC5D1C,OAAO,CAAC,uCAAuC,IAAI0C,KAAK,CAACE,OAAO,IAAI,oBAAoB,CAAC,CAAC;IAC5F,CAAC,SAAS;MACRzB,gBAAgB,CAAC,KAAK,CAAC;IACzB;EACF,CAAC;;EAED;EACA,MAAMkC,gBAAgB,GAAIvB,IAAI,IAAK;IACjCa,OAAO,CAACE,GAAG,CAAC,mBAAmB,EAAEf,IAAI,CAAC;IACtCa,OAAO,CAACE,GAAG,CAAC,iBAAiB,EAAE;MAC7BlC,OAAO,EAAEmB,IAAI,CAACnB,OAAO;MACrBsC,SAAS,EAAEnB,IAAI,CAACmB,SAAS;MACzBC,OAAO,EAAEpB,IAAI,CAACoB,OAAO;MACrBI,aAAa,EAAExB,IAAI,CAACwB,aAAa;MACjCC,mBAAmB,EAAEzB,IAAI,CAACyB;IAC5B,CAAC,CAAC;;IAEF;IACA,IAAInE,gBAAgB,CAAC0C,IAAI,CAAC,EAAE;MAC1Ba,OAAO,CAACE,GAAG,CAAC,gCAAgC,CAAC;MAC7CV,kBAAkB,CAACL,IAAI,CAAC;MACxBG,wBAAwB,CAAC,IAAI,CAAC;MAC9B;IACF;IAEAzB,eAAe,CAACsB,IAAI,CAAC;IACrBpB,WAAW,CAAC;MACVC,OAAO,EAAEmB,IAAI,CAACnB,OAAO;MACrBC,YAAY,EAAE,EAAE;MAChBC,SAAS,EAAE;IACb,CAAC,CAAC;IACFU,aAAa,CAAC,CAAC,CAAC,CAAC;IACjBE,eAAe,CAAC,CAAC,CAAC,CAAC;;IAEnB;IACAkB,OAAO,CAACE,GAAG,CAAC,wDAAwD,CAAC;EACvE,CAAC;;EAED;EACA,MAAMW,iBAAiB,GAAIC,CAAC,IAAK;IAC/B,MAAM;MAAEC,IAAI;MAAEC;IAAM,CAAC,GAAGF,CAAC,CAACG,MAAM;IAChClD,WAAW,CAACmD,IAAI,KAAK;MAAE,GAAGA,IAAI;MAAE,CAACH,IAAI,GAAGC;IAAM,CAAC,CAAC,CAAC;;IAEjD;IACAG,aAAa,CAACJ,IAAI,EAAEC,KAAK,CAAC;EAC5B,CAAC;;EAED;EACA,MAAMG,aAAa,GAAGA,CAACJ,IAAI,EAAEC,KAAK,KAAK;IACrC,MAAMI,SAAS,GAAG;MAAE,GAAGzC;IAAW,CAAC;IACnC,MAAM0C,WAAW,GAAG;MAAE,GAAGxC;IAAa,CAAC;IAEvC,IAAIkC,IAAI,KAAK,cAAc,EAAE;MAC3B;MACA,IAAIC,KAAK,KAAK,EAAE,EAAE;QAChBI,SAAS,CAACnD,YAAY,GAAG,iCAAiC;MAC5D,CAAC,MAAM,IAAIqD,KAAK,CAACN,KAAK,CAAC,IAAIO,UAAU,CAACP,KAAK,CAAC,GAAG,CAAC,EAAE;QAChDI,SAAS,CAACnD,YAAY,GAAG,iDAAiD;MAC5E,CAAC,MAAM;QACL,OAAOmD,SAAS,CAACnD,YAAY;;QAE7B;QACA,MAAMuD,WAAW,GAAGD,UAAU,CAACP,KAAK,CAAC;QACrC,IAAIpD,YAAY,IAAI4D,WAAW,GAAG5D,YAAY,CAAC+C,aAAa,EAAE;UAC5DU,WAAW,CAACpD,YAAY,GAAG,mBAAmBuD,WAAW,+BAA+B5D,YAAY,CAAC+C,aAAa,GAAG;QACvH,CAAC,MAAM;UACL,OAAOU,WAAW,CAACpD,YAAY;QACjC;;QAEA;QACA,IAAIH,QAAQ,CAACI,SAAS,IAAIJ,QAAQ,CAACI,SAAS,KAAK,cAAc,EAAE;UAC/D,MAAMuD,cAAc,GAAG/D,MAAM,CAACgE,IAAI,CAACC,CAAC,IAAIA,CAAC,CAACzD,SAAS,KAAKJ,QAAQ,CAACI,SAAS,CAAC;UAC3E,IAAIuD,cAAc,IAAID,WAAW,GAAGC,cAAc,CAACjB,aAAa,EAAE;YAChEa,WAAW,CAACnD,SAAS,GAAG,mBAAmBsD,WAAW,4CAA4CC,cAAc,CAACjB,aAAa,GAAG;UACnI,CAAC,MAAM;YACL,OAAOa,WAAW,CAACnD,SAAS;UAC9B;QACF;MACF;IACF;IAEA,IAAI6C,IAAI,KAAK,WAAW,EAAE;MACxB;MACA,IAAIC,KAAK,KAAK,EAAE,EAAE;QAChBI,SAAS,CAAClD,SAAS,GAAG,0BAA0B;MAClD,CAAC,MAAM;QACL,OAAOkD,SAAS,CAAClD,SAAS;;QAE1B;QACA,IAAI8C,KAAK,KAAK,cAAc,IAAIlD,QAAQ,CAACG,YAAY,EAAE;UACrD,MAAMuD,WAAW,GAAGD,UAAU,CAACzD,QAAQ,CAACG,YAAY,CAAC;UACrD,MAAMwD,cAAc,GAAG/D,MAAM,CAACgE,IAAI,CAACC,CAAC,IAAIA,CAAC,CAACzD,SAAS,KAAK8C,KAAK,CAAC;UAC9D,IAAIS,cAAc,IAAID,WAAW,GAAGC,cAAc,CAACjB,aAAa,EAAE;YAChEa,WAAW,CAACnD,SAAS,GAAG,mBAAmBsD,WAAW,4CAA4CC,cAAc,CAACjB,aAAa,GAAG;UACnI,CAAC,MAAM;YACL,OAAOa,WAAW,CAACnD,SAAS;UAC9B;QACF;MACF;IACF;IAEAU,aAAa,CAACwC,SAAS,CAAC;IACxBtC,eAAe,CAACuC,WAAW,CAAC;EAC9B,CAAC;;EAED;EACA,MAAMO,YAAY,GAAGA,CAAA,KAAM;IACzB,MAAMR,SAAS,GAAG,CAAC,CAAC;;IAEpB;IACA,IAAI,CAACtD,QAAQ,CAACG,YAAY,EAAE;MAC1BmD,SAAS,CAACnD,YAAY,GAAG,iCAAiC;IAC5D,CAAC,MAAM,IAAIqD,KAAK,CAACxD,QAAQ,CAACG,YAAY,CAAC,IAAIsD,UAAU,CAACzD,QAAQ,CAACG,YAAY,CAAC,GAAG,CAAC,EAAE;MAChFmD,SAAS,CAACnD,YAAY,GAAG,iDAAiD;IAC5E;;IAEA;IACA,IAAI,CAACH,QAAQ,CAACI,SAAS,EAAE;MACvBkD,SAAS,CAAClD,SAAS,GAAG,0BAA0B;IAClD;IAEAU,aAAa,CAACwC,SAAS,CAAC;IACxB,OAAOS,MAAM,CAACC,IAAI,CAACV,SAAS,CAAC,CAACW,MAAM,KAAK,CAAC;EAC5C,CAAC;;EAED;EACA,MAAMC,kBAAkB,GAAGA,CAAA,KAAM;IAC/B,IAAIlE,QAAQ,CAACI,SAAS,KAAK,cAAc,EAAE;MACzC,OAAO,IAAI,CAAC,CAAC;IACf;IAEA,MAAMuD,cAAc,GAAG/D,MAAM,CAACgE,IAAI,CAACC,CAAC,IAAIA,CAAC,CAACzD,SAAS,KAAKJ,QAAQ,CAACI,SAAS,CAAC;IAC3E,IAAI,CAACuD,cAAc,EAAE;MACnB,OAAO,KAAK;IACd;;IAEA;IACA,MAAMQ,oBAAoB,GAAGrE,YAAY,CAAC0C,SAAS,KAAKmB,cAAc,CAACnB,SAAS;;IAEhF;IACA,MAAM4B,kBAAkB,GAAGC,MAAM,CAACvE,YAAY,CAAC2C,OAAO,CAAC,KAAK4B,MAAM,CAACV,cAAc,CAAClB,OAAO,CAAC;IAE1F,OAAO0B,oBAAoB,IAAIC,kBAAkB;EACnD,CAAC;;EAED;EACA,MAAME,UAAU,GAAG,MAAAA,CAAA,KAAY;IAC7B;IACA,IAAI1E,MAAM,CAACqE,MAAM,KAAK,CAAC,IAAI,CAACxD,aAAa,EAAE;MACzC,IAAI,CAACT,QAAQ,CAACG,YAAY,IAAIqD,KAAK,CAACC,UAAU,CAACzD,QAAQ,CAACG,YAAY,CAAC,CAAC,IAAIsD,UAAU,CAACzD,QAAQ,CAACG,YAAY,CAAC,IAAI,CAAC,EAAE;QAChHW,aAAa,CAAC;UACZ,GAAGD,UAAU;UACbV,YAAY,EAAE;QAChB,CAAC,CAAC;QACF;MACF;;MAEA;MACAH,QAAQ,CAACI,SAAS,GAAG,cAAc;IACrC,CAAC,MAAM;MACL;MACA,IAAI,CAAC0D,YAAY,CAAC,CAAC,EAAE;QACnB;MACF;;MAEA;MACA,IAAI,CAACI,kBAAkB,CAAC,CAAC,EAAE;QACzB;QACA,MAAMP,cAAc,GAAG/D,MAAM,CAACgE,IAAI,CAACC,CAAC,IAAIA,CAAC,CAACzD,SAAS,KAAKJ,QAAQ,CAACI,SAAS,CAAC;QAC3EgB,uBAAuB,CAAC;UACtBC,IAAI,EAAEvB,YAAY;UAClBwB,MAAM,EAAEqC;QACV,CAAC,CAAC;QACFzC,6BAA6B,CAAC,IAAI,CAAC;QACnC;MACF;IACF;;IAEA;IACA,IAAI;MACFN,SAAS,CAAC,IAAI,CAAC;;MAEf;MACA,MAAM8C,WAAW,GAAGD,UAAU,CAACzD,QAAQ,CAACG,YAAY,CAAC;;MAErD;MACA+B,OAAO,CAACE,GAAG,CAAC,6DAA6D,CAAC;MAC1EF,OAAO,CAACE,GAAG,CAAC,eAAe,EAAE/C,UAAU,CAAC;MACxC6C,OAAO,CAACE,GAAG,CAAC,YAAY,EAAEpC,QAAQ,CAACE,OAAO,CAAC;MAC3CgC,OAAO,CAACE,GAAG,CAAC,iBAAiB,EAAEsB,WAAW,CAAC;MAC3CxB,OAAO,CAACE,GAAG,CAAC,cAAc,EAAEpC,QAAQ,CAACI,SAAS,CAAC;;MAE/C;MACA,MAAMjC,WAAW,CAACoG,iBAAiB,CACjClF,UAAU,EACVW,QAAQ,CAACE,OAAO,EAChBwD,WAAW,EACX1D,QAAQ,CAACI,SAAS,EAClB,IAAI,CAAC;MACP,CAAC;;MAED;MACAd,SAAS,CAAC,sCAAsC,CAAC;;MAEjD;MACAS,eAAe,CAAC,IAAI,CAAC;MACrBE,WAAW,CAAC;QACVC,OAAO,EAAE,EAAE;QACXC,YAAY,EAAE,EAAE;QAChBC,SAAS,EAAE;MACb,CAAC,CAAC;;MAEF;MACAuB,QAAQ,CAAC,CAAC;MACVC,UAAU,CAAC,CAAC;IACd,CAAC,CAAC,OAAOK,KAAK,EAAE;MACdC,OAAO,CAACD,KAAK,CAAC,gCAAgC,EAAEA,KAAK,CAAC;MACtD1C,OAAO,CAAC,iCAAiC,IAAI0C,KAAK,CAACE,OAAO,IAAI,oBAAoB,CAAC,CAAC;IACtF,CAAC,SAAS;MACRvB,SAAS,CAAC,KAAK,CAAC;IAClB;EACF,CAAC;;EAED;EACA,MAAM4D,gCAAgC,GAAG,MAAAA,CAAA,KAAY;IACnD,IAAI;MACF5D,SAAS,CAAC,IAAI,CAAC;MACfM,6BAA6B,CAAC,KAAK,CAAC;MAEpC,MAAM;QAAEG,IAAI;QAAEC;MAAO,CAAC,GAAGH,oBAAoB;;MAE7C;MACA,MAAMhD,WAAW,CAACsG,0BAA0B,CAC1CpF,UAAU,EACVgC,IAAI,CAACnB,OAAO,EACZ;QACEE,SAAS,EAAEkB,MAAM,CAAClB,SAAS;QAC3BoC,SAAS,EAAElB,MAAM,CAACkB,SAAS;QAC3BC,OAAO,EAAEnB,MAAM,CAACmB;MAClB,CACF,CAAC;;MAED;MACA,MAAMtE,WAAW,CAACoG,iBAAiB,CACjClF,UAAU,EACVW,QAAQ,CAACE,OAAO,EAChBuD,UAAU,CAACzD,QAAQ,CAACG,YAAY,CAAC,EACjCH,QAAQ,CAACI,SAAS,EAClB,IAAI,CAAC;MACP,CAAC;;MAED;MACAd,SAAS,CAAC,wDAAwD,CAAC;;MAEnE;MACAS,eAAe,CAAC,IAAI,CAAC;MACrBE,WAAW,CAAC;QACVC,OAAO,EAAE,EAAE;QACXC,YAAY,EAAE,EAAE;QAChBC,SAAS,EAAE;MACb,CAAC,CAAC;;MAEF;MACAuB,QAAQ,CAAC,CAAC;MACVC,UAAU,CAAC,CAAC;IACd,CAAC,CAAC,OAAOK,KAAK,EAAE;MACdC,OAAO,CAACD,KAAK,CAAC,2CAA2C,EAAEA,KAAK,CAAC;MACjE1C,OAAO,CAAC,4CAA4C,IAAI0C,KAAK,CAACE,OAAO,IAAI,oBAAoB,CAAC,CAAC;IACjG,CAAC,SAAS;MACRvB,SAAS,CAAC,KAAK,CAAC;IAClB;EACF,CAAC;;EAED;EACA,MAAM8D,4BAA4B,GAAGA,CAAA,KAAM;IACzClD,wBAAwB,CAAC,KAAK,CAAC;IAC/BE,kBAAkB,CAAC,IAAI,CAAC;EAC1B,CAAC;;EAED;EACA,MAAMiD,wBAAwB,GAAGA,CAAA,KAAM;IACrCD,4BAA4B,CAAC,CAAC;EAChC,CAAC;;EAED;EACA,MAAME,gBAAgB,GAAGA,CAAA,KAAM;IAC7B,IAAInD,eAAe,EAAE;MACnBhC,QAAQ,CAAC,+CAA+CgC,eAAe,CAACvB,OAAO,EAAE,CAAC;IACpF;IACAwE,4BAA4B,CAAC,CAAC;EAChC,CAAC;;EAED;EACA,MAAM,CAACG,gBAAgB,EAAEC,mBAAmB,CAAC,GAAGvI,QAAQ,CAAC,EAAE,CAAC;;EAE5D;EACAC,SAAS,CAAC,MAAM;IACd,IAAIsD,YAAY,EAAE;MAChB;MACA,MAAMiF,QAAQ,GAAGC,sBAAsB,CAAClF,YAAY,CAAC;MACrDgF,mBAAmB,CAACC,QAAQ,CAAC;IAC/B,CAAC,MAAM;MACLD,mBAAmB,CAAC,EAAE,CAAC;IACzB;EACF,CAAC,EAAE,CAAChF,YAAY,EAAEF,MAAM,CAAC,CAAC;;EAE1B;EACA,MAAMoF,sBAAsB,GAAI3D,IAAI,IAAK;IACvC,IAAI,CAACA,IAAI,EAAE,OAAO,EAAE;IAEpBa,OAAO,CAACE,GAAG,CAAC,wCAAwC,EAAEf,IAAI,CAAC;IAC3Da,OAAO,CAACE,GAAG,CAAC,qBAAqB,EAAExC,MAAM,CAAC;IAC1CsC,OAAO,CAACE,GAAG,CAAC,+BAA+B,EAAExC,MAAM,CAACqE,MAAM,CAAC;;IAE3D;IACA,IAAIrE,MAAM,CAACqE,MAAM,KAAK,CAAC,EAAE;MACvB/B,OAAO,CAACE,GAAG,CAAC,sDAAsD,CAAC;MACnE,OAAO,EAAE;IACX;;IAEA;IACA,MAAM2C,QAAQ,GAAGnF,MAAM,CAACoC,MAAM,CAACV,MAAM,IAAI;MACvC;MACA;MACA;MACA;MACA,MAAM2D,iBAAiB,GAAGZ,MAAM,CAAChD,IAAI,CAACmB,SAAS,IAAI,EAAE,CAAC,CAAC0C,IAAI,CAAC,CAAC,CAACC,WAAW,CAAC,CAAC;MAC3E,MAAMC,eAAe,GAAGf,MAAM,CAAChD,IAAI,CAACoB,OAAO,IAAI,EAAE,CAAC,CAACyC,IAAI,CAAC,CAAC;MAEzD,MAAMG,mBAAmB,GAAGhB,MAAM,CAAC/C,MAAM,CAACkB,SAAS,IAAI,EAAE,CAAC,CAAC0C,IAAI,CAAC,CAAC,CAACC,WAAW,CAAC,CAAC;MAC/E,MAAMG,iBAAiB,GAAGjB,MAAM,CAAC/C,MAAM,CAACmB,OAAO,IAAI,EAAE,CAAC,CAACyC,IAAI,CAAC,CAAC;MAE7D,MAAMK,cAAc,GAAGF,mBAAmB,KAAKJ,iBAAiB;MAChE,MAAMO,YAAY,GAAGF,iBAAiB,KAAKF,eAAe;MAC1D,MAAMK,SAAS,GAAGjH,kBAAkB,CAAC8C,MAAM,CAACoB,aAAa,EAAEpB,MAAM,CAACoE,YAAY,CAAC;MAC/E,MAAMC,OAAO,GAAGF,SAAS,KAAKnH,WAAW,CAACsH,SAAS;MAEnD,MAAMC,YAAY,GAAGN,cAAc,IAAIC,YAAY,IAAIG,OAAO;;MAE9D;MACAzD,OAAO,CAACE,GAAG,CAAC,oCAAoCd,MAAM,CAAClB,SAAS,GAAG,CAAC;MACpE8B,OAAO,CAACE,GAAG,CAAC,oCAAoCd,MAAM,CAACkB,SAAS,YAAY,OAAOlB,MAAM,CAACkB,SAAS,EAAE,CAAC;MACtGN,OAAO,CAACE,GAAG,CAAC,kCAAkCf,IAAI,CAACmB,SAAS,YAAY,OAAOnB,IAAI,CAACmB,SAAS,EAAE,CAAC;MAChGN,OAAO,CAACE,GAAG,CAAC,uCAAuCiD,mBAAmB,GAAG,CAAC;MAC1EnD,OAAO,CAACE,GAAG,CAAC,qCAAqC6C,iBAAiB,GAAG,CAAC;MACtE/C,OAAO,CAACE,GAAG,CAAC,kCAAkCd,MAAM,CAACmB,OAAO,YAAY,OAAOnB,MAAM,CAACmB,OAAO,EAAE,CAAC;MAChGP,OAAO,CAACE,GAAG,CAAC,gCAAgCf,IAAI,CAACoB,OAAO,YAAY,OAAOpB,IAAI,CAACoB,OAAO,EAAE,CAAC;MAC1FP,OAAO,CAACE,GAAG,CAAC,qCAAqCkD,iBAAiB,GAAG,CAAC;MACtEpD,OAAO,CAACE,GAAG,CAAC,mCAAmCgD,eAAe,GAAG,CAAC;;MAElE;MACAlD,OAAO,CAACE,GAAG,CAAC,UAAUd,MAAM,CAAClB,SAAS,GAAG,EAAE;QACzC,kBAAkB,EAAE,IAAIkB,MAAM,CAACkB,SAAS,GAAG;QAC3C,gBAAgB,EAAE,IAAInB,IAAI,CAACmB,SAAS,GAAG;QACvC,mBAAmB,EAAE+C,cAAc;QACnC,gBAAgB,EAAE,IAAIlB,MAAM,CAAC/C,MAAM,CAACmB,OAAO,CAAC,GAAG;QAC/C,cAAc,EAAE,IAAI4B,MAAM,CAAChD,IAAI,CAACoB,OAAO,CAAC,GAAG;QAC3C,iBAAiB,EAAE+C,YAAY;QAC/B,cAAc,EAAEC,SAAS;QACzB,WAAW,EAAEE,OAAO;QACpB,cAAc,EAAEE;MAClB,CAAC,CAAC;MAEF,OAAOA,YAAY;IACrB,CAAC,CAAC;IAEF3D,OAAO,CAACE,GAAG,CAAC,6BAA6B,EAAE2C,QAAQ,CAACd,MAAM,CAAC;IAC3D,IAAIc,QAAQ,CAACd,MAAM,GAAG,CAAC,EAAE;MACvB/B,OAAO,CAACE,GAAG,CAAC,2BAA2B,EAAE2C,QAAQ,CAAC,CAAC,CAAC,CAAC;IACvD,CAAC,MAAM;MACL7C,OAAO,CAACE,GAAG,CAAC,iDAAiD,CAAC;IAChE;IAEA,OAAO2C,QAAQ;EACjB,CAAC;;EAED;EACA,MAAMe,mBAAmB,GAAGA,CAAA,KAAM;IAChC,OAAOjB,gBAAgB;EACzB,CAAC;;EAED;EACA,MAAMkB,eAAe,GAAGA,CAAA,KAAM;IAC5B,IAAIxF,WAAW,EAAE;MACf,oBACEtB,OAAA,CAACxC,GAAG;QAACuJ,EAAE,EAAE;UAAEC,OAAO,EAAE,MAAM;UAAEC,cAAc,EAAE,QAAQ;UAAEC,EAAE,EAAE;QAAE,CAAE;QAAAC,QAAA,eAC5DnH,OAAA,CAAC7B,gBAAgB;UAAAiJ,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACjB,CAAC;IAEV;IAEA,IAAI9G,IAAI,CAACuE,MAAM,KAAK,CAAC,EAAE;MACrB,oBACEhF,OAAA,CAAC5B,KAAK;QAACoJ,QAAQ,EAAC,MAAM;QAACT,EAAE,EAAE;UAAEG,EAAE,EAAE;QAAE,CAAE;QAAAC,QAAA,EAAC;MAEtC;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAO,CAAC;IAEZ;IAEA,oBACEvH,OAAA,CAAAE,SAAA;MAAAiH,QAAA,gBACEnH,OAAA,CAAC5B,KAAK;QAACoJ,QAAQ,EAAC,MAAM;QAACT,EAAE,EAAE;UAAEU,EAAE,EAAE;QAAE,CAAE;QAAAN,QAAA,EAAC;MAEtC;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAO,CAAC,eAERvH,OAAA,CAAChC,cAAc;QAAC0J,SAAS,EAAE9J,KAAM;QAACmJ,EAAE,EAAE;UAAEU,EAAE,EAAE;QAAE,CAAE;QAAAN,QAAA,eAC9CnH,OAAA,CAACnC,KAAK;UAAC8J,IAAI,EAAC,OAAO;UAAAR,QAAA,gBACjBnH,OAAA,CAAC/B,SAAS;YAAAkJ,QAAA,eACRnH,OAAA,CAAC9B,QAAQ;cAAC6I,EAAE,EAAE;gBAAEa,OAAO,EAAE;cAAU,CAAE;cAAAT,QAAA,gBACnCnH,OAAA,CAACjC,SAAS;gBAAAoJ,QAAA,EAAC;cAAO;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAW,CAAC,eAC9BvH,OAAA,CAACjC,SAAS;gBAAAoJ,QAAA,EAAC;cAAS;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAW,CAAC,eAChCvH,OAAA,CAACjC,SAAS;gBAAAoJ,QAAA,EAAC;cAAU;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAW,CAAC,eACjCvH,OAAA,CAACjC,SAAS;gBAAAoJ,QAAA,EAAC;cAAa;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAW,CAAC,eACpCvH,OAAA,CAACjC,SAAS;gBAAAoJ,QAAA,EAAC;cAAK;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAW,CAAC,eAC5BvH,OAAA,CAACjC,SAAS;gBAAAoJ,QAAA,EAAC;cAAM;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAW,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACrB;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACF,CAAC,eACZvH,OAAA,CAAClC,SAAS;YAAAqJ,QAAA,EACP1G,IAAI,CAACoH,GAAG,CAAEzF,IAAI,IAAK;cAClB,MAAM0F,WAAW,GAAGpI,gBAAgB,CAAC0C,IAAI,CAAC;cAC1C,oBACEpC,OAAA,CAAC9B,QAAQ;gBAEP6I,EAAE,EAAE;kBACFa,OAAO,EAAEE,WAAW,GAAG,SAAS,GAAG,SAAS;kBAC5C,SAAS,EAAE;oBAAEF,OAAO,EAAEE,WAAW,GAAG,SAAS,GAAG;kBAAU;gBAC5D,CAAE;gBAAAX,QAAA,gBAEFnH,OAAA,CAACjC,SAAS;kBAAAoJ,QAAA,eAACnH,OAAA;oBAAAmH,QAAA,EAAS/E,IAAI,CAACnB;kBAAO;oBAAAmG,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAS;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAW,CAAC,eACtDvH,OAAA,CAACjC,SAAS;kBAAAoJ,QAAA,EAAE/E,IAAI,CAACmB,SAAS,IAAI;gBAAK;kBAAA6D,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAY,CAAC,eAChDvH,OAAA,CAACjC,SAAS;kBAAAoJ,QAAA,GAAC,MAAI,EAAC/E,IAAI,CAAC2F,mBAAmB,IAAI,KAAK,eAAC/H,OAAA;oBAAAoH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAI,CAAC,OAAG,EAACnF,IAAI,CAAC4F,iBAAiB,IAAI,KAAK;gBAAA;kBAAAZ,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAY,CAAC,eACvGvH,OAAA,CAACjC,SAAS;kBAAAoJ,QAAA,GAAE/E,IAAI,CAACwB,aAAa,IAAI,KAAK,EAAC,IAAE;gBAAA;kBAAAwD,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAW,CAAC,eACtDvH,OAAA,CAACjC,SAAS;kBAAAoJ,QAAA,eACRnH,OAAA,CAAC3B,IAAI;oBACH4J,KAAK,EAAE7F,IAAI,CAACyB,mBAAmB,IAAI,KAAM;oBACzC8D,IAAI,EAAC,OAAO;oBACZO,KAAK,EAAEvI,kBAAkB,CAACyC,IAAI,CAACyB,mBAAmB,CAAE;oBACpDsE,OAAO,EAAC;kBAAU;oBAAAf,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACnB;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACO,CAAC,eACZvH,OAAA,CAACjC,SAAS;kBAAAoJ,QAAA,eACRnH,OAAA,CAACrC,MAAM;oBACLgK,IAAI,EAAC,OAAO;oBACZQ,OAAO,EAAC,WAAW;oBACnBD,KAAK,EAAC,SAAS;oBACfE,OAAO,EAAEA,CAAA,KAAMzE,gBAAgB,CAACvB,IAAI,CAAE;oBACtCiG,QAAQ,EAAEP,WAAY;oBAAAX,QAAA,EAErBW,WAAW,GAAG,gBAAgB,GAAG;kBAAW;oBAAAV,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACvC;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACA,CAAC;cAAA,GA5BPnF,IAAI,CAACnB,OAAO;gBAAAmG,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OA6BT,CAAC;YAEf,CAAC;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACO,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACP;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACM,CAAC;IAAA,eACjB,CAAC;EAEP,CAAC;;EAED;EACA,MAAMe,UAAU,GAAGA,CAAA,KAAM;IACvB,IAAI,CAACzH,YAAY,EAAE,OAAO,IAAI;;IAE9B;IACA,MAAM0H,oBAAoB,GAAG1B,mBAAmB,CAAC,CAAC;IAClD5D,OAAO,CAACE,GAAG,CAAC,sCAAsC,EAAEoF,oBAAoB,CAAC;IACzEtF,OAAO,CAACE,GAAG,CAAC,gDAAgD,EAAEoF,oBAAoB,CAACvD,MAAM,CAAC;;IAE1F;IACA,IAAIrE,MAAM,CAACqE,MAAM,KAAK,CAAC,IAAI,CAACxD,aAAa,EAAE;MACzC,oBACExB,OAAA,CAACpC,KAAK;QAACmJ,EAAE,EAAE;UAAEyB,CAAC,EAAE;QAAE,CAAE;QAAArB,QAAA,gBAClBnH,OAAA,CAACvC,UAAU;UAAC0K,OAAO,EAAC,IAAI;UAACM,YAAY;UAAAtB,QAAA,EAAC;QAEtC;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAY,CAAC,eAEbvH,OAAA,CAAC5B,KAAK;UAACoJ,QAAQ,EAAC,SAAS;UAACT,EAAE,EAAE;YAAEU,EAAE,EAAE;UAAE,CAAE;UAAAN,QAAA,EAAC;QAEzC;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAO,CAAC,eAERvH,OAAA,CAACxC,GAAG;UAACuJ,EAAE,EAAE;YAAEU,EAAE,EAAE,CAAC;YAAEe,CAAC,EAAE,CAAC;YAAEZ,OAAO,EAAE,SAAS;YAAEc,YAAY,EAAE;UAAE,CAAE;UAAAvB,QAAA,gBAC5DnH,OAAA,CAACvC,UAAU;YAAC0K,OAAO,EAAC,WAAW;YAACM,YAAY;YAACE,UAAU,EAAC,MAAM;YAACT,KAAK,EAAC,SAAS;YAAAf,QAAA,GAAC,oBAC3D,EAACtG,YAAY,CAACI,OAAO;UAAA;YAAAmG,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC7B,CAAC,eACbvH,OAAA,CAACzB,IAAI;YAACqK,SAAS;YAACC,OAAO,EAAE,CAAE;YAAA1B,QAAA,gBACzBnH,OAAA,CAACzB,IAAI;cAACuK,IAAI;cAACC,EAAE,EAAE,EAAG;cAACC,EAAE,EAAE,CAAE;cAAA7B,QAAA,eACvBnH,OAAA,CAACvC,UAAU;gBAAC0K,OAAO,EAAC,OAAO;gBAAAhB,QAAA,gBACzBnH,OAAA;kBAAAmH,QAAA,EAAQ;gBAAU;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,KAAC,EAAC1G,YAAY,CAAC0C,SAAS,IAAI,KAAK;cAAA;gBAAA6D,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAClD;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACT,CAAC,eACPvH,OAAA,CAACzB,IAAI;cAACuK,IAAI;cAACC,EAAE,EAAE,EAAG;cAACC,EAAE,EAAE,CAAE;cAAA7B,QAAA,eACvBnH,OAAA,CAACvC,UAAU;gBAAC0K,OAAO,EAAC,OAAO;gBAAAhB,QAAA,gBACzBnH,OAAA;kBAAAmH,QAAA,EAAQ;gBAAW;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,KAAC,EAAC1G,YAAY,CAAC2C,OAAO,IAAI,KAAK;cAAA;gBAAA4D,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACjD;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACT,CAAC,eACPvH,OAAA,CAACzB,IAAI;cAACuK,IAAI;cAACC,EAAE,EAAE,EAAG;cAACC,EAAE,EAAE,CAAE;cAAA7B,QAAA,eACvBnH,OAAA,CAACvC,UAAU;gBAAC0K,OAAO,EAAC,OAAO;gBAAAhB,QAAA,gBACzBnH,OAAA;kBAAAmH,QAAA,EAAQ;gBAAc;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,KAAC,EAAC1G,YAAY,CAAC+C,aAAa,IAAI,KAAK,EAAC,IACvE;cAAA;gBAAAwD,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAY;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACT,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACJ,CAAC,eAENvH,OAAA,CAACzB,IAAI;UAACqK,SAAS;UAACC,OAAO,EAAE,CAAE;UAAA1B,QAAA,gBACzBnH,OAAA,CAACzB,IAAI;YAACuK,IAAI;YAACC,EAAE,EAAE,EAAG;YAACC,EAAE,EAAE,CAAE;YAAA7B,QAAA,eACvBnH,OAAA,CAACtC,SAAS;cACRuL,SAAS;cACThB,KAAK,EAAC,cAAc;cACpBjE,IAAI,EAAC,cAAc;cACnBC,KAAK,EAAElD,QAAQ,CAACG,YAAa;cAC7BgI,QAAQ,EAAEpF,iBAAkB;cAC5BqF,IAAI,EAAC,QAAQ;cACbC,UAAU,EAAE;gBACVC,UAAU,EAAE;kBAAEC,GAAG,EAAE,CAAC;kBAAEC,IAAI,EAAE;gBAAI;cAClC;YAAE;cAAAnC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACE,CAAC,eACPvH,OAAA,CAACzB,IAAI;YAACuK,IAAI;YAACC,EAAE,EAAE,EAAG;YAACC,EAAE,EAAE,CAAE;YAAA7B,QAAA,eACvBnH,OAAA,CAACxB,WAAW;cAACyK,SAAS;cAAA9B,QAAA,gBACpBnH,OAAA,CAACvB,UAAU;gBAAA0I,QAAA,EAAC;cAAM;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAY,CAAC,eAC/BvH,OAAA,CAACtB,MAAM;gBACLsF,IAAI,EAAC,WAAW;gBAChBC,KAAK,EAAC,cAAc;gBACpBoE,QAAQ;gBAAAlB,QAAA,eAERnH,OAAA,CAACrB,QAAQ;kBAACsF,KAAK,EAAC,cAAc;kBAAC8C,EAAE,EAAE;oBAAE4B,UAAU,EAAE,MAAM;oBAAET,KAAK,EAAE,SAAS;oBAAEN,OAAO,EAAE;kBAAU,CAAE;kBAAAT,QAAA,EAAC;gBAEjG;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAU;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACL,CAAC,eACTvH,OAAA,CAACvC,UAAU;gBAAC0K,OAAO,EAAC,SAAS;gBAACD,KAAK,EAAC,gBAAgB;gBAACnB,EAAE,EAAE;kBAAEyC,EAAE,EAAE;gBAAE,CAAE;gBAAArC,QAAA,EAAC;cAEpE;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAY,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACF;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACV,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,eAEPvH,OAAA,CAACxC,GAAG;UAACuJ,EAAE,EAAE;YAAEyC,EAAE,EAAE,CAAC;YAAExC,OAAO,EAAE,MAAM;YAAEC,cAAc,EAAE;UAAgB,CAAE;UAAAE,QAAA,gBACnEnH,OAAA,CAACrC,MAAM;YACLwK,OAAO,EAAC,UAAU;YAClBD,KAAK,EAAC,WAAW;YACjBE,OAAO,EAAEA,CAAA,KAAM;cACbtH,eAAe,CAAC,IAAI,CAAC;cACrBE,WAAW,CAAC;gBACVC,OAAO,EAAE,EAAE;gBACXC,YAAY,EAAE,EAAE;gBAChBC,SAAS,EAAE;cACb,CAAC,CAAC;YACJ,CAAE;YACFkH,QAAQ,EAAE3G,MAAO;YAAAyF,QAAA,EAClB;UAED;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC,eACTvH,OAAA,CAACrC,MAAM;YACLwK,OAAO,EAAC,WAAW;YACnBD,KAAK,EAAC,SAAS;YACfE,OAAO,EAAEA,CAAA,KAAM/C,UAAU,CAAC,CAAE;YAC5BgD,QAAQ,EAAE3G,MAAM,IAAI,CAACX,QAAQ,CAACG,YAAa;YAAAiG,QAAA,EAE1CzF,MAAM,gBAAG1B,OAAA,CAAC7B,gBAAgB;cAACwJ,IAAI,EAAE;YAAG;cAAAP,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,GAAG;UAAO;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC5C,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACN,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACD,CAAC;IAEZ;IAEA,MAAM3B,gBAAgB,GAAGiB,mBAAmB,CAAC,CAAC;IAE9C,oBACE7G,OAAA,CAACpC,KAAK;MAACmJ,EAAE,EAAE;QAAEyB,CAAC,EAAE;MAAE,CAAE;MAAArB,QAAA,gBAClBnH,OAAA,CAACvC,UAAU;QAAC0K,OAAO,EAAC,IAAI;QAACM,YAAY;QAAAtB,QAAA,EAAC;MAEtC;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAY,CAAC,eAEbvH,OAAA,CAAC5B,KAAK;QAACoJ,QAAQ,EAAC,MAAM;QAACT,EAAE,EAAE;UAAEU,EAAE,EAAE;QAAE,CAAE;QAAAN,QAAA,GAAC,4IACmG,eAAAnH,OAAA;UAAAmH,QAAA,EAAQ;QAAY;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC,KACtK;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAO,CAAC,eAERvH,OAAA,CAACxC,GAAG;QAACuJ,EAAE,EAAE;UAAEU,EAAE,EAAE,CAAC;UAAEe,CAAC,EAAE,CAAC;UAAEZ,OAAO,EAAE,SAAS;UAAEc,YAAY,EAAE;QAAE,CAAE;QAAAvB,QAAA,gBAC5DnH,OAAA,CAACvC,UAAU;UAAC0K,OAAO,EAAC,WAAW;UAACM,YAAY;UAACE,UAAU,EAAC,MAAM;UAACT,KAAK,EAAC,SAAS;UAAAf,QAAA,GAAC,oBAC3D,EAACtG,YAAY,CAACI,OAAO;QAAA;UAAAmG,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC7B,CAAC,eACbvH,OAAA,CAACzB,IAAI;UAACqK,SAAS;UAACC,OAAO,EAAE,CAAE;UAAA1B,QAAA,gBACzBnH,OAAA,CAACzB,IAAI;YAACuK,IAAI;YAACC,EAAE,EAAE,EAAG;YAACC,EAAE,EAAE,CAAE;YAAA7B,QAAA,eACvBnH,OAAA,CAACvC,UAAU;cAAC0K,OAAO,EAAC,OAAO;cAAAhB,QAAA,gBACzBnH,OAAA;gBAAAmH,QAAA,EAAQ;cAAU;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC,KAAC,EAAC1G,YAAY,CAAC0C,SAAS,IAAI,KAAK;YAAA;cAAA6D,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAClD;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACT,CAAC,eACPvH,OAAA,CAACzB,IAAI;YAACuK,IAAI;YAACC,EAAE,EAAE,EAAG;YAACC,EAAE,EAAE,CAAE;YAAA7B,QAAA,eACvBnH,OAAA,CAACvC,UAAU;cAAC0K,OAAO,EAAC,OAAO;cAAAhB,QAAA,gBACzBnH,OAAA;gBAAAmH,QAAA,EAAQ;cAAW;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC,KAAC,EAAC1G,YAAY,CAAC2C,OAAO,IAAI,KAAK;YAAA;cAAA4D,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACjD;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACT,CAAC,eACPvH,OAAA,CAACzB,IAAI;YAACuK,IAAI;YAACC,EAAE,EAAE,EAAG;YAACC,EAAE,EAAE,CAAE;YAAA7B,QAAA,eACvBnH,OAAA,CAACvC,UAAU;cAAC0K,OAAO,EAAC,OAAO;cAAAhB,QAAA,gBACzBnH,OAAA;gBAAAmH,QAAA,EAAQ;cAAc;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC,KAAC,EAAC1G,YAAY,CAAC+C,aAAa,IAAI,KAAK,EAAC,IACvE;YAAA;cAAAwD,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAY;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACT,CAAC,eACPvH,OAAA,CAACzB,IAAI;YAACuK,IAAI;YAACC,EAAE,EAAE,EAAG;YAACC,EAAE,EAAE,CAAE;YAAA7B,QAAA,eACvBnH,OAAA,CAACvC,UAAU;cAAC0K,OAAO,EAAC,OAAO;cAAAhB,QAAA,gBACzBnH,OAAA;gBAAAmH,QAAA,EAAQ;cAAoB;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC,KAAC,EAAC1G,YAAY,CAACkH,mBAAmB,IAAI,KAAK;YAAA;cAAAX,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACtE;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACT,CAAC,eACPvH,OAAA,CAACzB,IAAI;YAACuK,IAAI;YAACC,EAAE,EAAE,EAAG;YAACC,EAAE,EAAE,CAAE;YAAA7B,QAAA,eACvBnH,OAAA,CAACvC,UAAU;cAAC0K,OAAO,EAAC,OAAO;cAAAhB,QAAA,gBACzBnH,OAAA;gBAAAmH,QAAA,EAAQ;cAAkB;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC,KAAC,EAAC1G,YAAY,CAACmH,iBAAiB,IAAI,KAAK;YAAA;cAAAZ,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAClE;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACT,CAAC,eACPvH,OAAA,CAACzB,IAAI;YAACuK,IAAI;YAACC,EAAE,EAAE,EAAG;YAAA5B,QAAA,eAChBnH,OAAA,CAACvC,UAAU;cAAC0K,OAAO,EAAC,OAAO;cAAAhB,QAAA,gBACzBnH,OAAA;gBAAAmH,QAAA,EAAQ;cAAM;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC,eACvBvH,OAAA,CAAC3B,IAAI;gBACH4J,KAAK,EAAEpH,YAAY,CAACgD,mBAAmB,IAAI,KAAM;gBACjD8D,IAAI,EAAC,OAAO;gBACZO,KAAK,EAAEvI,kBAAkB,CAACkB,YAAY,CAACgD,mBAAmB,CAAE;gBAC5DsE,OAAO,EAAC,UAAU;gBAClBpB,EAAE,EAAE;kBAAE0C,EAAE,EAAE;gBAAE;cAAE;gBAAArC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACf,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACQ;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACT,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACJ,CAAC,eAENvH,OAAA,CAAC1B,OAAO;QAACyI,EAAE,EAAE;UAAEU,EAAE,EAAE;QAAE;MAAE;QAAAL,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC,eAE1BvH,OAAA,CAACzB,IAAI;QAACqK,SAAS;QAACC,OAAO,EAAE,CAAE;QAAA1B,QAAA,gBACzBnH,OAAA,CAACzB,IAAI;UAACuK,IAAI;UAACC,EAAE,EAAE,EAAG;UAACC,EAAE,EAAE,CAAE;UAAA7B,QAAA,eACvBnH,OAAA,CAACtC,SAAS;YACRuL,SAAS;YACThB,KAAK,EAAC,cAAc;YACpBjE,IAAI,EAAC,cAAc;YACnBC,KAAK,EAAElD,QAAQ,CAACG,YAAa;YAC7BgI,QAAQ,EAAEpF,iBAAkB;YAC5BqF,IAAI,EAAC,QAAQ;YACbnG,KAAK,EAAE,CAAC,CAACpB,UAAU,CAACV,YAAa;YACjCwI,UAAU,EAAE9H,UAAU,CAACV,YAAY,IAAKY,YAAY,CAACZ,YAAY,iBAC/DlB,OAAA;cAAM2J,KAAK,EAAE;gBAAEzB,KAAK,EAAE;cAAS,CAAE;cAAAf,QAAA,EAAErF,YAAY,CAACZ;YAAY;cAAAkG,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CAClE;YACHc,QAAQ,EAAE3G,MAAO;YACjB0H,UAAU,EAAE;cACVC,UAAU,EAAE;gBAAEC,GAAG,EAAE,CAAC;gBAAEC,IAAI,EAAE;cAAI;YAClC;UAAE;YAAAnC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACE,CAAC,eACPvH,OAAA,CAACzB,IAAI;UAACuK,IAAI;UAACC,EAAE,EAAE,EAAG;UAACC,EAAE,EAAE,CAAE;UAAA7B,QAAA,eACvBnH,OAAA,CAACxB,WAAW;YAACyK,SAAS;YAACjG,KAAK,EAAE,CAAC,CAACpB,UAAU,CAACT,SAAU;YAAAgG,QAAA,gBACnDnH,OAAA,CAACvB,UAAU;cAAA0I,QAAA,EAAC;YAAM;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAY,CAAC,eAC/BvH,OAAA,CAACtB,MAAM;cACLsF,IAAI,EAAC,WAAW;cAChBC,KAAK,EAAElD,QAAQ,CAACI,SAAU;cAC1B+H,QAAQ,EAAEpF,iBAAkB;cAC5BuE,QAAQ,EAAE3G,MAAM,IAAIF,aAAc;cAAA2F,QAAA,gBAGlCnH,OAAA,CAACrB,QAAQ;gBAACsF,KAAK,EAAC,cAAc;gBAAC8C,EAAE,EAAE;kBAAE4B,UAAU,EAAE,MAAM;kBAAET,KAAK,EAAE,SAAS;kBAAEN,OAAO,EAAE;gBAAU,CAAE;gBAAAT,QAAA,EAAC;cAEjG;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAU,CAAC,eAGXvH,OAAA,CAAC1B,OAAO;gBAAA8I,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,EAGV/F,aAAa,gBACZxB,OAAA,CAACrB,QAAQ;gBAAC0J,QAAQ;gBAAAlB,QAAA,eAChBnH,OAAA,CAACxC,GAAG;kBAACuJ,EAAE,EAAE;oBAAEC,OAAO,EAAE,MAAM;oBAAE4C,UAAU,EAAE;kBAAS,CAAE;kBAAAzC,QAAA,gBACjDnH,OAAA,CAAC7B,gBAAgB;oBAACwJ,IAAI,EAAE,EAAG;oBAACZ,EAAE,EAAE;sBAAE8C,EAAE,EAAE;oBAAE;kBAAE;oBAAAzC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE,CAAC,eAC7CvH,OAAA,CAACvC,UAAU;oBAAC0K,OAAO,EAAC,SAAS;oBAAAhB,QAAA,EAAC;kBAE9B;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAY,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACV;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACE,CAAC,GACT3B,gBAAgB,CAACZ,MAAM,KAAK,CAAC,gBAC/BhF,OAAA,CAACrB,QAAQ;gBAAC0J,QAAQ;gBAAAlB,QAAA,eAChBnH,OAAA,CAACvC,UAAU;kBAAC0K,OAAO,EAAC,SAAS;kBAACD,KAAK,EAAC,gBAAgB;kBAAAf,QAAA,EAAC;gBAErD;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAY;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACL,CAAC,gBAEXvH,OAAA,CAACrB,QAAQ;gBAAC0J,QAAQ;gBAAAlB,QAAA,eAChBnH,OAAA,CAACvC,UAAU;kBAAC0K,OAAO,EAAC,SAAS;kBAAAhB,QAAA,GAAC,sBACR,EAACvB,gBAAgB,CAACZ,MAAM,EAAC,GAC/C;gBAAA;kBAAAoC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAY;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACL,CACX,EAGA,CAAC/F,aAAa,IAAI,CAAC,MAAM;gBACxByB,OAAO,CAACE,GAAG,CAAC,wCAAwC,EAAEyC,gBAAgB,CAAC;gBACvE3C,OAAO,CAACE,GAAG,CAAC,kDAAkD,EAAEyC,gBAAgB,CAACZ,MAAM,CAAC;gBAExF,OAAOY,gBAAgB,CAACiC,GAAG,CAAExF,MAAM,IAAK;kBACtCY,OAAO,CAACE,GAAG,CAAC,+BAA+B,EAAEd,MAAM,CAAC;kBACpD,oBACErC,OAAA,CAACrB,QAAQ;oBAAwBsF,KAAK,EAAE5B,MAAM,CAAClB,SAAU;oBAAAgG,QAAA,GACtD9E,MAAM,CAAClB,SAAS,EAAC,KAAG,EAACkB,MAAM,CAACkB,SAAS,EAAC,KAAG,EAAClB,MAAM,CAACoB,aAAa,EAAC,GAClE;kBAAA,GAFepB,MAAM,CAAClB,SAAS;oBAAAiG,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAErB,CAAC;gBAEf,CAAC,CAAC;cACJ,CAAC,EAAE,CAAC,eAIJvH,OAAA,CAAC1B,OAAO;gBAAA8I,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,eAGXvH,OAAA,CAACrB,QAAQ;gBAAC0J,QAAQ;gBAAAlB,QAAA,eAChBnH,OAAA,CAACvC,UAAU;kBAAC0K,OAAO,EAAC,SAAS;kBAACpB,EAAE,EAAE;oBAAE4B,UAAU,EAAE,MAAM;oBAAET,KAAK,EAAE;kBAAU,CAAE;kBAAAf,QAAA,EAAC;gBAE5E;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAY;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACL,CAAC,EAGV5G,MAAM,CAACoC,MAAM,CAACV,MAAM,IAAI9C,kBAAkB,CAAC8C,MAAM,CAACoB,aAAa,EAAEpB,MAAM,CAACoE,YAAY,CAAC,KAAKpH,WAAW,CAACsH,SAAS,CAAC,CAACkB,GAAG,CAAExF,MAAM,iBAC3HrC,OAAA,CAACrB,QAAQ;gBAAwBsF,KAAK,EAAE5B,MAAM,CAAClB,SAAU;gBAAAgG,QAAA,GACtD9E,MAAM,CAAClB,SAAS,EAAC,KAAG,EAACkB,MAAM,CAACkB,SAAS,EAAC,KAAG,EAAClB,MAAM,CAACmB,OAAO,EAAC,KAAG,EAACnB,MAAM,CAACoB,aAAa,EAAC,GACrF;cAAA,GAFepB,MAAM,CAAClB,SAAS;gBAAAiG,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAErB,CACX,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACI,CAAC,EACR3F,UAAU,CAACT,SAAS,iBACnBnB,OAAA,CAACvC,UAAU;cAAC0K,OAAO,EAAC,SAAS;cAACD,KAAK,EAAC,OAAO;cAAAf,QAAA,EACxCvF,UAAU,CAACT;YAAS;cAAAiG,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACX,CACb,EACAzF,YAAY,CAACX,SAAS,iBACrBnB,OAAA,CAACvC,UAAU;cAAC0K,OAAO,EAAC,SAAS;cAACpB,EAAE,EAAE;gBAAEmB,KAAK,EAAE;cAAS,CAAE;cAAAf,QAAA,EACnDrF,YAAY,CAACX;YAAS;cAAAiG,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACb,CACb,eAEDvH,OAAA,CAACvC,UAAU;cAAC0K,OAAO,EAAC,SAAS;cAACD,KAAK,EAAC,gBAAgB;cAACnB,EAAE,EAAE;gBAAEyC,EAAE,EAAE;cAAE,CAAE;cAAArC,QAAA,EAAC;YAEpE;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAY,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACF;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACV,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,eAEPvH,OAAA,CAACxC,GAAG;QAACuJ,EAAE,EAAE;UAAEyC,EAAE,EAAE,CAAC;UAAExC,OAAO,EAAE,MAAM;UAAEC,cAAc,EAAE;QAAgB,CAAE;QAAAE,QAAA,gBACnEnH,OAAA,CAACrC,MAAM;UACLwK,OAAO,EAAC,UAAU;UAClBD,KAAK,EAAC,WAAW;UACjBE,OAAO,EAAEA,CAAA,KAAM;YACbtH,eAAe,CAAC,IAAI,CAAC;YACrBE,WAAW,CAAC;cACVC,OAAO,EAAE,EAAE;cACXC,YAAY,EAAE,EAAE;cAChBC,SAAS,EAAE;YACb,CAAC,CAAC;UACJ,CAAE;UACFkH,QAAQ,EAAE3G,MAAO;UAAAyF,QAAA,EAClB;QAED;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC,eACTvH,OAAA,CAACrC,MAAM;UACLwK,OAAO,EAAC,WAAW;UACnBD,KAAK,EAAC,SAAS;UACfE,OAAO,EAAE/C,UAAW;UACpBgD,QAAQ,EAAE3G,MAAM,IAAIoD,MAAM,CAACC,IAAI,CAACnD,UAAU,CAAC,CAACoD,MAAM,GAAG,CAAE;UAAAmC,QAAA,EAEtDzF,MAAM,gBAAG1B,OAAA,CAAC7B,gBAAgB;YAACwJ,IAAI,EAAE;UAAG;YAAAP,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,GAAG;QAAO;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC5C,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACN,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACD,CAAC;EAEZ,CAAC;EAED,oBACEvH,OAAA,CAACxC,GAAG;IAAA2J,QAAA,GAED,CAACtG,YAAY,IAAIiG,eAAe,CAAC,CAAC,EAGlCwB,UAAU,CAAC,CAAC,eAGbtI,OAAA,CAACF,sBAAsB;MACrBgK,IAAI,EAAE9H,0BAA2B;MACjC+H,OAAO,EAAEA,CAAA,KAAM9H,6BAA6B,CAAC,KAAK,CAAE;MACpDG,IAAI,EAAEF,oBAAoB,CAACE,IAAK;MAChCC,MAAM,EAAEH,oBAAoB,CAACG,MAAO;MACpC2H,YAAY,EAAEzE,gCAAiC;MAC/C0E,mBAAmB,EAAEA,CAAA,KAAM;QACzBhI,6BAA6B,CAAC,KAAK,CAAC;QACpCjB,WAAW,CAACmD,IAAI,KAAK;UAAE,GAAGA,IAAI;UAAEhD,SAAS,EAAE;QAAG,CAAC,CAAC,CAAC;MACnD;IAAE;MAAAiG,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,eAGFvH,OAAA,CAACpB,MAAM;MAACkL,IAAI,EAAExH,qBAAsB;MAACyH,OAAO,EAAEtE,4BAA6B;MAAA0B,QAAA,gBACzEnH,OAAA,CAACnB,WAAW;QAAAsI,QAAA,EAAC;MAAe;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAa,CAAC,eAC1CvH,OAAA,CAAClB,aAAa;QAAAqI,QAAA,gBACZnH,OAAA,CAAChB,iBAAiB;UAAAmI,QAAA,GAAC,UACT,EAAC3E,eAAe,aAAfA,eAAe,uBAAfA,eAAe,CAAEvB,OAAO,EAAC,4BACpC;QAAA;UAAAmG,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAmB,CAAC,eACpBvH,OAAA,CAACxC,GAAG;UAACuJ,EAAE,EAAE;YAAEyC,EAAE,EAAE;UAAE,CAAE;UAAArC,QAAA,gBACjBnH,OAAA,CAACvC,UAAU;YAAC0K,OAAO,EAAC,OAAO;YAACM,YAAY;YAAAtB,QAAA,EAAC;UAEzC;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAY,CAAC,eACbvH,OAAA,CAACvC,UAAU;YAACiK,SAAS,EAAC,IAAI;YAACS,OAAO,EAAC,OAAO;YAAAhB,QAAA,gBACxCnH,OAAA;cAAAmH,QAAA,EAAI;YAA8B;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eACvCvH,OAAA;cAAAmH,QAAA,EAAI;YAAyB;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eAClCvH,OAAA;cAAAmH,QAAA,EAAI;YAAsB;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACrB,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACV,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACO,CAAC,eAChBvH,OAAA,CAACjB,aAAa;QAACgI,EAAE,EAAE;UAAEyB,CAAC,EAAE,CAAC;UAAEvB,cAAc,EAAE;QAAgB,CAAE;QAAAE,QAAA,gBAC3DnH,OAAA,CAACrC,MAAM;UAACyK,OAAO,EAAE3C,4BAA6B;UAACyC,KAAK,EAAC,WAAW;UAAAf,QAAA,EAAC;QAEjE;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC,eACTvH,OAAA,CAACxC,GAAG;UAAA2J,QAAA,gBACFnH,OAAA,CAACrC,MAAM;YAACyK,OAAO,EAAE1C,wBAAyB;YAACwC,KAAK,EAAC,SAAS;YAACnB,EAAE,EAAE;cAAE8C,EAAE,EAAE;YAAE,CAAE;YAAA1C,QAAA,EAAC;UAE1E;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC,eACTvH,OAAA,CAACrC,MAAM;YAACyK,OAAO,EAAEzC,gBAAiB;YAACwC,OAAO,EAAC,WAAW;YAACD,KAAK,EAAC,SAAS;YAAAf,QAAA,EAAC;UAEvE;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACN,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACO,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACV,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACN,CAAC;AAEV,CAAC;AAAChH,EAAA,CA73BIJ,2BAA2B;EAAA,QACdlB,WAAW;AAAA;AAAAiL,EAAA,GADxB/J,2BAA2B;AA+3BjC,eAAeA,2BAA2B;AAAC,IAAA+J,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}