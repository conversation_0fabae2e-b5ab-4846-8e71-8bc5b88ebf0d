{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\PycharmProjects\\\\CMS\\\\webapp\\\\frontend\\\\src\\\\pages\\\\cavi\\\\VisualizzaCaviPage.js\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect } from 'react';\nimport { Box, Typography, Paper, Button, Grid, Card, CardContent, Alert, IconButton, Table, TableBody, TableCell, TableContainer, TableHead, TableRow, Tabs, Tab } from '@mui/material';\nimport { ArrowBack as ArrowBackIcon, Refresh as RefreshIcon, Home as HomeIcon, ViewList as ViewListIcon, ViewModule as ViewModuleIcon } from '@mui/icons-material';\nimport { useNavigate } from 'react-router-dom';\nimport { useAuth } from '../../context/AuthContext';\nimport caviService from '../../services/caviService';\nimport './CaviPage.css';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst VisualizzaCaviPage = () => {\n  _s();\n  const {\n    isImpersonating,\n    user\n  } = useAuth();\n  const navigate = useNavigate();\n  const [cantiereId, setCantiereId] = useState(null);\n  const [cantiereName, setCantiereName] = useState('');\n  const [caviAttivi, setCaviAttivi] = useState([]);\n  const [caviSpare, setCaviSpare] = useState([]);\n  const [loading, setLoading] = useState(true);\n  const [error, setError] = useState(null);\n  const [viewMode, setViewMode] = useState('table'); // 'table' o 'card'\n  const [stats, setStats] = useState(null); // Statistiche sui cavi\n\n  // Carica i dati del cantiere e dei cavi\n  useEffect(() => {\n    const fetchData = async () => {\n      try {\n        console.log('Inizializzazione VisualizzaCaviPage...');\n\n        // Verifica che l'utente sia autenticato\n        const token = localStorage.getItem('token');\n        console.log('Token presente:', !!token);\n        if (!token) {\n          setError('Sessione scaduta. Effettua nuovamente il login.');\n          setLoading(false);\n          return;\n        }\n\n        // Recupera l'ID del cantiere selezionato dal localStorage\n        let selectedCantiereId = localStorage.getItem('selectedCantiereId');\n        let selectedCantiereName = localStorage.getItem('selectedCantiereName');\n        console.log('Cantiere selezionato dal localStorage:', {\n          selectedCantiereId,\n          selectedCantiereName\n        });\n        console.log('Dati utente:', user);\n\n        // Stampa tutti i dati nel localStorage per debug\n        console.log('DEBUG - Tutti i dati nel localStorage:');\n        for (let i = 0; i < localStorage.length; i++) {\n          const key = localStorage.key(i);\n          console.log(`${key}: ${localStorage.getItem(key)}`);\n        }\n\n        // SOLUZIONE DIRETTA: Ottieni l'ID del cantiere direttamente dal token JWT\n        if ((user === null || user === void 0 ? void 0 : user.role) === 'cantieri_user') {\n          console.log('Utente cantiere rilevato, tentativo di recupero ID cantiere dai dati utente');\n\n          // Verifica se l'utente ha un ID cantiere nei dati utente\n          if (user.cantiere_id) {\n            console.log('Trovato ID cantiere nei dati utente:', user.cantiere_id);\n            selectedCantiereId = user.cantiere_id.toString();\n            selectedCantiereName = user.cantiere_name || `Cantiere ${user.cantiere_id}`;\n\n            // Salva l'ID e il nome del cantiere nel localStorage\n            localStorage.setItem('selectedCantiereId', selectedCantiereId);\n            localStorage.setItem('selectedCantiereName', selectedCantiereName);\n            console.log('Salvato ID cantiere nel localStorage:', selectedCantiereId);\n          } else {\n            // Tentativo di recupero dal token JWT\n            try {\n              console.log('Tentativo di decodifica del token JWT per recuperare l\\'ID cantiere');\n              const token = localStorage.getItem('token');\n              if (token) {\n                // Decodifica il token JWT (senza verifica della firma)\n                const base64Url = token.split('.')[1];\n                const base64 = base64Url.replace(/-/g, '+').replace(/_/g, '/');\n                const jsonPayload = decodeURIComponent(atob(base64).split('').map(c => {\n                  return '%' + ('00' + c.charCodeAt(0).toString(16)).slice(-2);\n                }).join(''));\n                const payload = JSON.parse(jsonPayload);\n                console.log('Payload del token JWT:', payload);\n                if (payload.cantiere_id) {\n                  console.log('Trovato ID cantiere nel token JWT:', payload.cantiere_id);\n                  selectedCantiereId = payload.cantiere_id.toString();\n                  // Usa un nome generico se non disponibile\n                  selectedCantiereName = `Cantiere ${payload.cantiere_id}`;\n\n                  // Salva l'ID e il nome del cantiere nel localStorage\n                  localStorage.setItem('selectedCantiereId', selectedCantiereId);\n                  localStorage.setItem('selectedCantiereName', selectedCantiereName);\n                  console.log('Salvato ID cantiere nel localStorage:', selectedCantiereId);\n                }\n              }\n            } catch (e) {\n              console.error('Errore durante la decodifica del token JWT:', e);\n            }\n          }\n        }\n\n        // SOLUZIONE TEMPORANEA: Se ancora non abbiamo un ID cantiere, usa un valore hardcoded per debug\n        if (!selectedCantiereId || selectedCantiereId === 'undefined' || selectedCantiereId === 'null') {\n          console.warn('ATTENZIONE: Nessun ID cantiere trovato, utilizzo valore hardcoded per debug');\n          // Usa il primo cantiere disponibile (questo è solo per debug)\n          selectedCantiereId = '1'; // Sostituisci con un ID cantiere valido nel tuo database\n          selectedCantiereName = 'Cantiere Debug';\n\n          // Salva l'ID e il nome del cantiere nel localStorage\n          localStorage.setItem('selectedCantiereId', selectedCantiereId);\n          localStorage.setItem('selectedCantiereName', selectedCantiereName);\n          console.log('Salvato ID cantiere hardcoded nel localStorage:', selectedCantiereId);\n        }\n\n        // Verifica finale\n        if (!selectedCantiereId) {\n          setError('Nessun cantiere selezionato. Torna alla pagina dei cantieri.');\n          setLoading(false);\n          return;\n        }\n\n        // Verifica che l'ID del cantiere sia un numero valido\n        const cantiereIdNum = parseInt(selectedCantiereId, 10);\n        console.log('ID cantiere convertito a numero:', cantiereIdNum);\n        if (isNaN(cantiereIdNum)) {\n          setError(`ID cantiere non valido: ${selectedCantiereId}. Torna alla pagina dei cantieri.`);\n          setLoading(false);\n          return;\n        }\n\n        // Usa il numero convertito, non la stringa\n        setCantiereId(cantiereIdNum);\n        setCantiereName(selectedCantiereName || `Cantiere ${cantiereIdNum}`);\n\n        // Carica i cavi attivi con statistiche\n        console.log('Caricamento cavi attivi per cantiere:', cantiereIdNum);\n        try {\n          // Imposta un timeout per evitare che la richiesta rimanga bloccata\n          const timeoutPromise = new Promise((_, reject) => {\n            setTimeout(() => reject(new Error('Timeout durante il caricamento dei cavi attivi')), 30000); // Aumentato a 30 secondi\n          });\n\n          // Esegui la richiesta con un timeout di sicurezza e includi le statistiche\n          console.log('Iniziando chiamata API per cavi attivi con statistiche...');\n          const caviPromise = caviService.getCavi(cantiereIdNum, 0, true); // true per includere le statistiche\n          const response = await Promise.race([caviPromise, timeoutPromise]);\n\n          // Verifica se la risposta contiene cavi e statistiche\n          if (response && response.cavi) {\n            // Formato con statistiche\n            console.log('Risposta con statistiche ricevuta:', response);\n            setCaviAttivi(response.cavi || []);\n            setStats(response.stats || null);\n            console.log('Numero di cavi attivi trovati:', response.cavi ? response.cavi.length : 0);\n            if (response.cavi && response.cavi.length > 0) {\n              console.log('Primo cavo attivo:', response.cavi[0]);\n            }\n          } else {\n            // Formato senza statistiche (retrocompatibilità)\n            console.log('Risposta senza statistiche ricevuta:', response);\n            setCaviAttivi(response || []);\n            console.log('Numero di cavi attivi trovati:', response ? response.length : 0);\n            if (response && response.length > 0) {\n              console.log('Primo cavo attivo:', response[0]);\n            }\n          }\n          if (!response || Array.isArray(response) && response.length === 0 || response.cavi && response.cavi.length === 0) {\n            console.warn('Nessun cavo attivo trovato per il cantiere', cantiereIdNum);\n          }\n        } catch (caviError) {\n          console.error('Errore nel caricamento dei cavi attivi:', caviError);\n          console.error('Dettagli errore cavi attivi:', {\n            message: caviError.message,\n            status: caviError.status,\n            data: caviError.data,\n            stack: caviError.stack,\n            code: caviError.code,\n            name: caviError.name,\n            response: caviError.response ? {\n              status: caviError.response.status,\n              statusText: caviError.response.statusText,\n              data: caviError.response.data\n            } : 'No response'\n          });\n\n          // Non interrompere il flusso, continua con i cavi spare\n          setCaviAttivi([]);\n          console.warn('Continuazione del flusso dopo errore nei cavi attivi');\n\n          // Aggiungi un messaggio di errore visibile all'utente\n          setError(`Errore nel caricamento dei cavi attivi: ${caviError.message}. Controlla la console per maggiori dettagli.`);\n        }\n\n        // Carica i cavi spare con gestione degli errori migliorata\n        console.log('Caricamento cavi spare per cantiere:', cantiereIdNum);\n        try {\n          // Imposta un timeout per evitare che la richiesta rimanga bloccata\n          const timeoutPromise = new Promise((_, reject) => {\n            setTimeout(() => reject(new Error('Timeout durante il caricamento dei cavi spare')), 30000); // Aumentato a 30 secondi\n          });\n\n          // Esegui la richiesta con un timeout di sicurezza\n          console.log('Iniziando chiamata API per cavi spare...');\n          const sparePromise = caviService.getCavi(cantiereIdNum, 3);\n          const spare = await Promise.race([sparePromise, timeoutPromise]);\n          console.log('Cavi spare caricati:', spare);\n          console.log('Numero di cavi spare trovati:', spare ? spare.length : 0);\n          if (spare && spare.length > 0) {\n            console.log('Primo cavo spare:', spare[0]);\n          } else {\n            console.warn('Nessun cavo spare trovato per il cantiere', cantiereIdNum);\n          }\n          setCaviSpare(spare || []);\n        } catch (spareError) {\n          console.error('Errore nel caricamento dei cavi spare:', spareError);\n          console.error('Dettagli errore cavi spare:', {\n            message: spareError.message,\n            status: spareError.status,\n            data: spareError.data,\n            stack: spareError.stack,\n            code: spareError.code,\n            name: spareError.name,\n            response: spareError.response ? {\n              status: spareError.response.status,\n              statusText: spareError.response.statusText,\n              data: spareError.response.data\n            } : 'No response'\n          });\n\n          // Non interrompere il flusso, imposta un array vuoto\n          setCaviSpare([]);\n\n          // Aggiungi un messaggio di errore visibile all'utente se non c'è già un errore per i cavi attivi\n          if (!error) {\n            setError(`Errore nel caricamento dei cavi spare: ${spareError.message}. Controlla la console per maggiori dettagli.`);\n          }\n        }\n\n        // Se siamo arrivati qui, almeno abbiamo caricato l'interfaccia di base\n        setLoading(false);\n      } catch (err) {\n        var _err$response, _err$response2, _err$response3, _err$response4, _err$response5, _err$response5$data;\n        console.error('Errore nel caricamento dei cavi:', err);\n        console.error('Dettagli errore generale:', {\n          message: err.message,\n          status: err.status || ((_err$response = err.response) === null || _err$response === void 0 ? void 0 : _err$response.status),\n          data: err.data || ((_err$response2 = err.response) === null || _err$response2 === void 0 ? void 0 : _err$response2.data),\n          stack: err.stack\n        });\n\n        // Estrai il messaggio di errore dettagliato\n        let errorMessage = 'Errore sconosciuto';\n        if (err.message && err.message.includes('ID cantiere non valido')) {\n          errorMessage = err.message;\n        } else if (err.status === 401 || err.status === 403 || ((_err$response3 = err.response) === null || _err$response3 === void 0 ? void 0 : _err$response3.status) === 401 || ((_err$response4 = err.response) === null || _err$response4 === void 0 ? void 0 : _err$response4.status) === 403) {\n          errorMessage = 'Sessione scaduta o non autorizzata. Effettua nuovamente il login.';\n        } else if ((_err$response5 = err.response) !== null && _err$response5 !== void 0 && (_err$response5$data = _err$response5.data) !== null && _err$response5$data !== void 0 && _err$response5$data.detail) {\n          // Estrai il messaggio di errore dettagliato dall'API\n          errorMessage = `Errore API: ${err.response.data.detail}`;\n        } else if (err.code === 'ERR_NETWORK') {\n          // Errore di rete\n          errorMessage = 'Network Error. Verifica che il backend sia in esecuzione e accessibile.';\n        } else if (err.message) {\n          errorMessage = err.message;\n        }\n        setError(`Impossibile caricare i cavi: ${errorMessage}. Riprova più tardi.`);\n\n        // Imposta array vuoti per evitare errori di rendering\n        setCaviAttivi([]);\n        setCaviSpare([]);\n      } finally {\n        setLoading(false);\n      }\n    };\n    fetchData();\n  }, []);\n\n  // Nessuna funzione di navigazione necessaria, tutto è gestito dal menu principale\n\n  // Funzione per visualizzare i cavi in formato tabellare\n  const renderCaviTable = cavi => {\n    if (!cavi || cavi.length === 0) {\n      return /*#__PURE__*/_jsxDEV(Alert, {\n        severity: \"info\",\n        children: [\"Nessun cavo trovato in questa categoria.\", /*#__PURE__*/_jsxDEV(Button, {\n          variant: \"text\",\n          color: \"primary\",\n          onClick: () => window.location.reload(),\n          sx: {\n            ml: 2\n          },\n          children: \"Riprova\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 313,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 311,\n        columnNumber: 9\n      }, this);\n    }\n    if (viewMode === 'table') {\n      return /*#__PURE__*/_jsxDEV(TableContainer, {\n        component: Paper,\n        sx: {\n          mt: 2,\n          overflowX: 'auto'\n        },\n        children: /*#__PURE__*/_jsxDEV(Table, {\n          size: \"small\",\n          children: [/*#__PURE__*/_jsxDEV(TableHead, {\n            children: /*#__PURE__*/_jsxDEV(TableRow, {\n              children: [/*#__PURE__*/_jsxDEV(TableCell, {\n                children: \"ID Cavo\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 331,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n                children: \"Utility\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 332,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n                children: \"Tipologia\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 333,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n                children: \"N.Cond\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 334,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n                children: \"Sezione\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 335,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n                children: \"Ubicaz.Part.\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 336,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n                children: \"Ubicaz.Arr.\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 337,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n                children: \"Metri T.\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 338,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n                children: \"Metri R.\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 339,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n                children: \"Bobina\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 340,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n                children: \"Stato\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 341,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n                children: \"Collegamenti\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 342,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 330,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 329,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(TableBody, {\n            children: cavi.map(cavo => {\n              // Formatta i valori per la visualizzazione come nella CLI\n              const id_cavo = String(cavo.id_cavo).replace('$', '');\n              const utility = cavo.utility || '-';\n              const tipologia = cavo.tipologia || '-';\n\n              // Gestisci n_conduttori come stringa o numero\n              let n_conduttori = '-';\n              try {\n                const n_cond_val = parseInt(cavo.n_conduttori, 10) || 0;\n                n_conduttori = n_cond_val > 0 ? String(n_cond_val) : '-';\n              } catch (e) {\n                n_conduttori = cavo.n_conduttori || '-';\n              }\n\n              // Gestisci sezione come stringa\n              let sezione = '-';\n              const sezione_val = cavo.sezione;\n              if (typeof sezione_val === 'number' && sezione_val === 0) {\n                sezione = '-';\n              } else {\n                sezione = sezione_val ? String(sezione_val) : '-';\n              }\n              const ubicazione_partenza = cavo.ubicazione_partenza || '-';\n              const ubicazione_arrivo = cavo.ubicazione_arrivo || '-';\n              const metri_teorici = cavo.metri_teorici ? `${parseFloat(cavo.metri_teorici).toFixed(2)}` : '-';\n              const stato = cavo.stato_installazione || '-';\n\n              // Metratura reale\n              const metri_reali = cavo.metratura_reale ? `${parseFloat(cavo.metratura_reale).toFixed(2)}` : '-';\n\n              // Bobina\n              const bobina = cavo.id_bobina || '-';\n\n              // Collegamenti\n              let stato_collegamenti = 'Non collegato';\n              const collegamenti = cavo.collegamenti || 0;\n              if (collegamenti === 1) {\n                stato_collegamenti = 'Partenza';\n              } else if (collegamenti === 2) {\n                stato_collegamenti = 'Arrivo';\n              } else if (collegamenti === 3) {\n                stato_collegamenti = 'Completo';\n              }\n\n              // Colore per lo stato\n              let statoColor = 'inherit';\n              if (stato.toLowerCase().includes('posato') || stato.toLowerCase().includes('installato')) {\n                statoColor = 'green';\n              } else if (stato.toLowerCase().includes('in corso') || stato.toLowerCase().includes('in posa')) {\n                statoColor = 'orange';\n              } else if (stato.toLowerCase().includes('da posare') || stato.toLowerCase().includes('da installare')) {\n                statoColor = 'red';\n              }\n              return /*#__PURE__*/_jsxDEV(TableRow, {\n                children: [/*#__PURE__*/_jsxDEV(TableCell, {\n                  children: id_cavo\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 404,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n                  children: utility\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 405,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n                  children: tipologia\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 406,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n                  children: n_conduttori\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 407,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n                  children: sezione\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 408,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n                  children: ubicazione_partenza\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 409,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n                  children: ubicazione_arrivo\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 410,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n                  children: metri_teorici\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 411,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n                  children: metri_reali\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 412,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n                  children: bobina\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 413,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n                  sx: {\n                    color: statoColor\n                  },\n                  children: stato\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 414,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n                  children: stato_collegamenti\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 415,\n                  columnNumber: 21\n                }, this)]\n              }, cavo.id_cavo, true, {\n                fileName: _jsxFileName,\n                lineNumber: 403,\n                columnNumber: 19\n              }, this);\n            })\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 345,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 328,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 327,\n        columnNumber: 9\n      }, this);\n    } else {\n      // Visualizzazione a schede (card)\n      return /*#__PURE__*/_jsxDEV(Grid, {\n        container: true,\n        spacing: 2,\n        children: cavi.map(cavo => {\n          var _cavo$stato_installaz, _cavo$stato_installaz2, _cavo$stato_installaz3, _cavo$stato_installaz4, _cavo$stato_installaz5, _cavo$stato_installaz6;\n          return /*#__PURE__*/_jsxDEV(Grid, {\n            item: true,\n            xs: 12,\n            sm: 6,\n            md: 4,\n            children: /*#__PURE__*/_jsxDEV(Card, {\n              children: /*#__PURE__*/_jsxDEV(CardContent, {\n                children: [/*#__PURE__*/_jsxDEV(Typography, {\n                  variant: \"h6\",\n                  component: \"div\",\n                  children: cavo.id_cavo\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 431,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                  variant: \"body2\",\n                  color: \"text.secondary\",\n                  children: [\"Sistema: \", cavo.sistema || 'N/A']\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 434,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                  variant: \"body2\",\n                  color: \"text.secondary\",\n                  children: [\"Tipologia: \", cavo.tipologia || 'N/A']\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 437,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                  variant: \"body2\",\n                  color: \"text.secondary\",\n                  children: [\"Partenza: \", cavo.ubicazione_partenza || 'N/A', \" - \", cavo.utenza_partenza || 'N/A']\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 440,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                  variant: \"body2\",\n                  color: \"text.secondary\",\n                  children: [\"Arrivo: \", cavo.ubicazione_arrivo || 'N/A', \" - \", cavo.utenza_arrivo || 'N/A']\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 443,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                  variant: \"body2\",\n                  color: \"text.secondary\",\n                  children: [\"Metri teorici: \", cavo.metri_teorici || 'N/A']\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 446,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                  variant: \"body2\",\n                  color: \"text.secondary\",\n                  children: [\"Metratura reale: \", cavo.metratura_reale || '0']\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 449,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                  variant: \"body2\",\n                  color: \"text.secondary\",\n                  children: [\"Bobina: \", cavo.id_bobina || 'N/A']\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 452,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                  variant: \"body2\",\n                  color: \"text.secondary\",\n                  sx: {\n                    color: (_cavo$stato_installaz = cavo.stato_installazione) !== null && _cavo$stato_installaz !== void 0 && _cavo$stato_installaz.toLowerCase().includes('posato') || (_cavo$stato_installaz2 = cavo.stato_installazione) !== null && _cavo$stato_installaz2 !== void 0 && _cavo$stato_installaz2.toLowerCase().includes('installato') ? 'green' : (_cavo$stato_installaz3 = cavo.stato_installazione) !== null && _cavo$stato_installaz3 !== void 0 && _cavo$stato_installaz3.toLowerCase().includes('in corso') || (_cavo$stato_installaz4 = cavo.stato_installazione) !== null && _cavo$stato_installaz4 !== void 0 && _cavo$stato_installaz4.toLowerCase().includes('in posa') ? 'orange' : (_cavo$stato_installaz5 = cavo.stato_installazione) !== null && _cavo$stato_installaz5 !== void 0 && _cavo$stato_installaz5.toLowerCase().includes('da posare') || (_cavo$stato_installaz6 = cavo.stato_installazione) !== null && _cavo$stato_installaz6 !== void 0 && _cavo$stato_installaz6.toLowerCase().includes('da installare') ? 'red' : 'inherit'\n                  },\n                  children: [\"Stato: \", cavo.stato_installazione || 'N/A']\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 455,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                  variant: \"body2\",\n                  color: \"text.secondary\",\n                  children: [\"Collegamenti: \", cavo.collegamenti === 0 ? 'Non collegato' : cavo.collegamenti === 1 ? 'Partenza' : cavo.collegamenti === 2 ? 'Arrivo' : cavo.collegamenti === 3 ? 'Completo' : 'N/A']\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 465,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 430,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 429,\n              columnNumber: 15\n            }, this)\n          }, cavo.id_cavo, false, {\n            fileName: _jsxFileName,\n            lineNumber: 428,\n            columnNumber: 13\n          }, this);\n        })\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 426,\n        columnNumber: 9\n      }, this);\n    }\n  };\n\n  // Gestisce il cambio di modalità di visualizzazione\n  const handleViewModeChange = mode => {\n    setViewMode(mode);\n  };\n\n  // Componente per visualizzare le statistiche dei cavi\n  const renderStatistics = () => {\n    if (!stats) return null;\n    return /*#__PURE__*/_jsxDEV(Paper, {\n      sx: {\n        p: 2,\n        mb: 3,\n        bgcolor: '#f5f5f5'\n      },\n      children: [/*#__PURE__*/_jsxDEV(Typography, {\n        variant: \"h6\",\n        gutterBottom: true,\n        children: \"Statistiche Cavi\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 491,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Grid, {\n        container: true,\n        spacing: 2,\n        children: [/*#__PURE__*/_jsxDEV(Grid, {\n          item: true,\n          xs: 12,\n          md: 6,\n          children: /*#__PURE__*/_jsxDEV(Box, {\n            children: [/*#__PURE__*/_jsxDEV(Typography, {\n              variant: \"subtitle1\",\n              children: \"Informazioni Generali\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 497,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Typography, {\n              variant: \"body2\",\n              children: [\"Totale cavi: \", stats.totale_cavi]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 498,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Typography, {\n              variant: \"body2\",\n              children: [\"Metri teorici totali: \", stats.totale_metri_teorici.toFixed(2), \" m\"]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 499,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Typography, {\n              variant: \"body2\",\n              children: [\"Metri posati totali: \", stats.totale_metri_posati.toFixed(2), \" m (\", stats.percentuale_completamento.toFixed(1), \"% del totale)\"]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 500,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 496,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 495,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Grid, {\n          item: true,\n          xs: 12,\n          md: 6,\n          children: /*#__PURE__*/_jsxDEV(Box, {\n            children: [/*#__PURE__*/_jsxDEV(Typography, {\n              variant: \"subtitle1\",\n              children: \"Distribuzione per stato\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 505,\n              columnNumber: 15\n            }, this), Object.entries(stats.stati).map(([stato, conteggio]) => /*#__PURE__*/_jsxDEV(Typography, {\n              variant: \"body2\",\n              children: [stato, \": \", conteggio, \" cavi\"]\n            }, stato, true, {\n              fileName: _jsxFileName,\n              lineNumber: 507,\n              columnNumber: 17\n            }, this))]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 504,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 503,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Grid, {\n          item: true,\n          xs: 12,\n          children: /*#__PURE__*/_jsxDEV(Box, {\n            children: [/*#__PURE__*/_jsxDEV(Typography, {\n              variant: \"subtitle1\",\n              children: \"Collegamenti\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 513,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Typography, {\n              variant: \"body2\",\n              children: [\"Non collegati: \", stats.collegamenti.non_collegati]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 514,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Typography, {\n              variant: \"body2\",\n              children: [\"Collegati lato partenza: \", stats.collegamenti.collegati_partenza]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 515,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Typography, {\n              variant: \"body2\",\n              children: [\"Collegati lato arrivo: \", stats.collegamenti.collegati_arrivo]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 516,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Typography, {\n              variant: \"body2\",\n              children: [\"Completamente collegati: \", stats.collegamenti.completamente_collegati]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 517,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 512,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 511,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 494,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 490,\n      columnNumber: 7\n    }, this);\n  };\n  return /*#__PURE__*/_jsxDEV(Box, {\n    className: \"cavi-page\",\n    children: [/*#__PURE__*/_jsxDEV(Paper, {\n      sx: {\n        mb: 3,\n        p: 2\n      },\n      children: /*#__PURE__*/_jsxDEV(Box, {\n        sx: {\n          display: 'flex',\n          justifyContent: 'flex-end',\n          alignItems: 'center',\n          gap: 2\n        },\n        children: [/*#__PURE__*/_jsxDEV(IconButton, {\n          onClick: () => window.location.reload(),\n          title: \"Ricarica la pagina\",\n          children: /*#__PURE__*/_jsxDEV(RefreshIcon, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 533,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 529,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Box, {\n          sx: {\n            display: 'flex',\n            border: '1px solid #ddd',\n            borderRadius: 1\n          },\n          children: [/*#__PURE__*/_jsxDEV(IconButton, {\n            sx: {\n              color: viewMode === 'table' ? '#212529' : '#6c757d'\n            },\n            onClick: () => handleViewModeChange('table'),\n            title: \"Vista tabellare\",\n            children: /*#__PURE__*/_jsxDEV(ViewListIcon, {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 541,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 536,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(IconButton, {\n            sx: {\n              color: viewMode === 'card' ? '#212529' : '#6c757d'\n            },\n            onClick: () => handleViewModeChange('card'),\n            title: \"Vista a schede\",\n            children: /*#__PURE__*/_jsxDEV(ViewModuleIcon, {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 548,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 543,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 535,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 528,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 527,\n      columnNumber: 7\n    }, this), loading ? /*#__PURE__*/_jsxDEV(Box, {\n      sx: {\n        display: 'flex',\n        flexDirection: 'column',\n        alignItems: 'center',\n        mt: 4\n      },\n      children: [/*#__PURE__*/_jsxDEV(Typography, {\n        children: \"Caricamento cavi...\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 556,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(Button, {\n        variant: \"outlined\",\n        color: \"primary\",\n        onClick: () => window.location.reload(),\n        sx: {\n          mt: 2\n        },\n        children: \"Ricarica la pagina\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 557,\n        columnNumber: 11\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 555,\n      columnNumber: 9\n    }, this) : error ? /*#__PURE__*/_jsxDEV(Box, {\n      children: [/*#__PURE__*/_jsxDEV(Alert, {\n        severity: \"error\",\n        sx: {\n          mb: 2\n        },\n        children: [error, error.includes('Network Error') && /*#__PURE__*/_jsxDEV(Typography, {\n          variant: \"body2\",\n          sx: {\n            mt: 1\n          },\n          children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n            children: \"Suggerimento:\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 572,\n            columnNumber: 17\n          }, this), \" Verifica che il server backend sia in esecuzione sulla porta 8001.\", /*#__PURE__*/_jsxDEV(\"br\", {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 573,\n            columnNumber: 17\n          }, this), \"Puoi avviare il backend eseguendo il file \", /*#__PURE__*/_jsxDEV(\"code\", {\n            children: \"run_system.py\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 574,\n            columnNumber: 59\n          }, this), \" nella cartella principale del progetto.\"]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 571,\n          columnNumber: 15\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 568,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(Box, {\n        sx: {\n          display: 'flex',\n          gap: 2\n        },\n        children: /*#__PURE__*/_jsxDEV(Button, {\n          variant: \"contained\",\n          className: \"primary-button\",\n          onClick: () => window.location.reload(),\n          children: \"Ricarica la pagina\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 579,\n          columnNumber: 13\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 578,\n        columnNumber: 11\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 567,\n      columnNumber: 9\n    }, this) : /*#__PURE__*/_jsxDEV(Box, {\n      children: [stats && renderStatistics(), /*#__PURE__*/_jsxDEV(Box, {\n        sx: {\n          mb: 3\n        },\n        children: [/*#__PURE__*/_jsxDEV(Typography, {\n          variant: \"h5\",\n          gutterBottom: true,\n          children: \"Cavi Attivi\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 594,\n          columnNumber: 13\n        }, this), renderCaviTable(caviAttivi)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 593,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(Box, {\n        sx: {\n          mt: 4\n        },\n        children: [/*#__PURE__*/_jsxDEV(Typography, {\n          variant: \"h5\",\n          gutterBottom: true,\n          children: \"Cavi Spare\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 601,\n          columnNumber: 13\n        }, this), renderCaviTable(caviSpare)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 600,\n        columnNumber: 11\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 589,\n      columnNumber: 9\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 526,\n    columnNumber: 5\n  }, this);\n};\n_s(VisualizzaCaviPage, \"1Ys6DVTwK6TlJNpWWlJXZyzvizg=\", false, function () {\n  return [useAuth, useNavigate];\n});\n_c = VisualizzaCaviPage;\nexport default VisualizzaCaviPage;\nvar _c;\n$RefreshReg$(_c, \"VisualizzaCaviPage\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "Box", "Typography", "Paper", "<PERSON><PERSON>", "Grid", "Card", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "IconButton", "Table", "TableBody", "TableCell", "TableContainer", "TableHead", "TableRow", "Tabs", "Tab", "ArrowBack", "ArrowBackIcon", "Refresh", "RefreshIcon", "Home", "HomeIcon", "ViewList", "ViewListIcon", "ViewModule", "ViewModuleIcon", "useNavigate", "useAuth", "caviService", "jsxDEV", "_jsxDEV", "VisualizzaCaviPage", "_s", "isImpersonating", "user", "navigate", "cantiereId", "setCantiereId", "cantiereName", "setCantiereName", "caviAttivi", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "caviSpare", "setCaviSpare", "loading", "setLoading", "error", "setError", "viewMode", "setViewMode", "stats", "setStats", "fetchData", "console", "log", "token", "localStorage", "getItem", "selectedCantiereId", "selectedCantiereName", "i", "length", "key", "role", "cantiere_id", "toString", "cantiere_name", "setItem", "base64Url", "split", "base64", "replace", "jsonPayload", "decodeURIComponent", "atob", "map", "c", "charCodeAt", "slice", "join", "payload", "JSON", "parse", "e", "warn", "cantiereIdNum", "parseInt", "isNaN", "timeoutPromise", "Promise", "_", "reject", "setTimeout", "Error", "caviPromise", "get<PERSON><PERSON>", "response", "race", "cavi", "Array", "isArray", "caviError", "message", "status", "data", "stack", "code", "name", "statusText", "sparePromise", "spare", "spareError", "err", "_err$response", "_err$response2", "_err$response3", "_err$response4", "_err$response5", "_err$response5$data", "errorMessage", "includes", "detail", "renderCaviTable", "severity", "children", "variant", "color", "onClick", "window", "location", "reload", "sx", "ml", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "component", "mt", "overflowX", "size", "cavo", "id_cavo", "String", "utility", "tipologia", "n_conduttori", "n_cond_val", "sezione", "sezione_val", "ubicazione_partenza", "ubicazione_arrivo", "metri_te<PERSON>ci", "parseFloat", "toFixed", "stato", "stato_installazione", "metri_reali", "metratura_reale", "bobina", "id_bobina", "stato_collegamenti", "colle<PERSON>nti", "statoColor", "toLowerCase", "container", "spacing", "_cavo$stato_installaz", "_cavo$stato_installaz2", "_cavo$stato_installaz3", "_cavo$stato_installaz4", "_cavo$stato_installaz5", "_cavo$stato_installaz6", "item", "xs", "sm", "md", "sistema", "utenza_partenza", "utenza_arrivo", "handleViewModeChange", "mode", "renderStatistics", "p", "mb", "bgcolor", "gutterBottom", "totale_cavi", "totale_metri_teorici", "totale_metri_posati", "percentuale_completamento", "Object", "entries", "stati", "conteggio", "non_collegati", "collegati_partenza", "collegati_arrivo", "completamente_collegati", "className", "display", "justifyContent", "alignItems", "gap", "title", "border", "borderRadius", "flexDirection", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/PycharmProjects/CMS/webapp/frontend/src/pages/cavi/VisualizzaCaviPage.js"], "sourcesContent": ["import React, { useState, useEffect } from 'react';\nimport {\n  Box,\n  Typography,\n  Paper,\n  Button,\n  Grid,\n  Card,\n  CardContent,\n  Alert,\n  IconButton,\n  Table,\n  TableBody,\n  TableCell,\n  TableContainer,\n  TableHead,\n  TableRow,\n  Tabs,\n  Tab\n} from '@mui/material';\nimport {\n  ArrowBack as ArrowBackIcon,\n  Refresh as RefreshIcon,\n  Home as HomeIcon,\n  ViewList as ViewListIcon,\n  ViewModule as ViewModuleIcon\n} from '@mui/icons-material';\nimport { useNavigate } from 'react-router-dom';\nimport { useAuth } from '../../context/AuthContext';\nimport caviService from '../../services/caviService';\nimport './CaviPage.css';\n\nconst VisualizzaCaviPage = () => {\n  const { isImpersonating, user } = useAuth();\n  const navigate = useNavigate();\n  const [cantiereId, setCantiereId] = useState(null);\n  const [cantiereName, setCantiereName] = useState('');\n  const [caviAttivi, setCaviAttivi] = useState([]);\n  const [caviSpare, setCaviSpare] = useState([]);\n  const [loading, setLoading] = useState(true);\n  const [error, setError] = useState(null);\n  const [viewMode, setViewMode] = useState('table'); // 'table' o 'card'\n  const [stats, setStats] = useState(null); // Statistiche sui cavi\n\n  // Carica i dati del cantiere e dei cavi\n  useEffect(() => {\n    const fetchData = async () => {\n      try {\n        console.log('Inizializzazione VisualizzaCaviPage...');\n\n        // Verifica che l'utente sia autenticato\n        const token = localStorage.getItem('token');\n        console.log('Token presente:', !!token);\n        if (!token) {\n          setError('Sessione scaduta. Effettua nuovamente il login.');\n          setLoading(false);\n          return;\n        }\n\n        // Recupera l'ID del cantiere selezionato dal localStorage\n        let selectedCantiereId = localStorage.getItem('selectedCantiereId');\n        let selectedCantiereName = localStorage.getItem('selectedCantiereName');\n\n        console.log('Cantiere selezionato dal localStorage:', { selectedCantiereId, selectedCantiereName });\n        console.log('Dati utente:', user);\n\n        // Stampa tutti i dati nel localStorage per debug\n        console.log('DEBUG - Tutti i dati nel localStorage:');\n        for (let i = 0; i < localStorage.length; i++) {\n          const key = localStorage.key(i);\n          console.log(`${key}: ${localStorage.getItem(key)}`);\n        }\n\n        // SOLUZIONE DIRETTA: Ottieni l'ID del cantiere direttamente dal token JWT\n        if (user?.role === 'cantieri_user') {\n          console.log('Utente cantiere rilevato, tentativo di recupero ID cantiere dai dati utente');\n\n          // Verifica se l'utente ha un ID cantiere nei dati utente\n          if (user.cantiere_id) {\n            console.log('Trovato ID cantiere nei dati utente:', user.cantiere_id);\n            selectedCantiereId = user.cantiere_id.toString();\n            selectedCantiereName = user.cantiere_name || `Cantiere ${user.cantiere_id}`;\n\n            // Salva l'ID e il nome del cantiere nel localStorage\n            localStorage.setItem('selectedCantiereId', selectedCantiereId);\n            localStorage.setItem('selectedCantiereName', selectedCantiereName);\n            console.log('Salvato ID cantiere nel localStorage:', selectedCantiereId);\n          } else {\n            // Tentativo di recupero dal token JWT\n            try {\n              console.log('Tentativo di decodifica del token JWT per recuperare l\\'ID cantiere');\n              const token = localStorage.getItem('token');\n              if (token) {\n                // Decodifica il token JWT (senza verifica della firma)\n                const base64Url = token.split('.')[1];\n                const base64 = base64Url.replace(/-/g, '+').replace(/_/g, '/');\n                const jsonPayload = decodeURIComponent(atob(base64).split('').map(c => {\n                  return '%' + ('00' + c.charCodeAt(0).toString(16)).slice(-2);\n                }).join(''));\n\n                const payload = JSON.parse(jsonPayload);\n                console.log('Payload del token JWT:', payload);\n\n                if (payload.cantiere_id) {\n                  console.log('Trovato ID cantiere nel token JWT:', payload.cantiere_id);\n                  selectedCantiereId = payload.cantiere_id.toString();\n                  // Usa un nome generico se non disponibile\n                  selectedCantiereName = `Cantiere ${payload.cantiere_id}`;\n\n                  // Salva l'ID e il nome del cantiere nel localStorage\n                  localStorage.setItem('selectedCantiereId', selectedCantiereId);\n                  localStorage.setItem('selectedCantiereName', selectedCantiereName);\n                  console.log('Salvato ID cantiere nel localStorage:', selectedCantiereId);\n                }\n              }\n            } catch (e) {\n              console.error('Errore durante la decodifica del token JWT:', e);\n            }\n          }\n        }\n\n        // SOLUZIONE TEMPORANEA: Se ancora non abbiamo un ID cantiere, usa un valore hardcoded per debug\n        if (!selectedCantiereId || selectedCantiereId === 'undefined' || selectedCantiereId === 'null') {\n          console.warn('ATTENZIONE: Nessun ID cantiere trovato, utilizzo valore hardcoded per debug');\n          // Usa il primo cantiere disponibile (questo è solo per debug)\n          selectedCantiereId = '1'; // Sostituisci con un ID cantiere valido nel tuo database\n          selectedCantiereName = 'Cantiere Debug';\n\n          // Salva l'ID e il nome del cantiere nel localStorage\n          localStorage.setItem('selectedCantiereId', selectedCantiereId);\n          localStorage.setItem('selectedCantiereName', selectedCantiereName);\n          console.log('Salvato ID cantiere hardcoded nel localStorage:', selectedCantiereId);\n        }\n\n        // Verifica finale\n        if (!selectedCantiereId) {\n          setError('Nessun cantiere selezionato. Torna alla pagina dei cantieri.');\n          setLoading(false);\n          return;\n        }\n\n        // Verifica che l'ID del cantiere sia un numero valido\n        const cantiereIdNum = parseInt(selectedCantiereId, 10);\n        console.log('ID cantiere convertito a numero:', cantiereIdNum);\n        if (isNaN(cantiereIdNum)) {\n          setError(`ID cantiere non valido: ${selectedCantiereId}. Torna alla pagina dei cantieri.`);\n          setLoading(false);\n          return;\n        }\n\n        // Usa il numero convertito, non la stringa\n        setCantiereId(cantiereIdNum);\n        setCantiereName(selectedCantiereName || `Cantiere ${cantiereIdNum}`);\n\n        // Carica i cavi attivi con statistiche\n        console.log('Caricamento cavi attivi per cantiere:', cantiereIdNum);\n        try {\n          // Imposta un timeout per evitare che la richiesta rimanga bloccata\n          const timeoutPromise = new Promise((_, reject) => {\n            setTimeout(() => reject(new Error('Timeout durante il caricamento dei cavi attivi')), 30000); // Aumentato a 30 secondi\n          });\n\n          // Esegui la richiesta con un timeout di sicurezza e includi le statistiche\n          console.log('Iniziando chiamata API per cavi attivi con statistiche...');\n          const caviPromise = caviService.getCavi(cantiereIdNum, 0, true); // true per includere le statistiche\n          const response = await Promise.race([caviPromise, timeoutPromise]);\n\n          // Verifica se la risposta contiene cavi e statistiche\n          if (response && response.cavi) {\n            // Formato con statistiche\n            console.log('Risposta con statistiche ricevuta:', response);\n            setCaviAttivi(response.cavi || []);\n            setStats(response.stats || null);\n            console.log('Numero di cavi attivi trovati:', response.cavi ? response.cavi.length : 0);\n            if (response.cavi && response.cavi.length > 0) {\n              console.log('Primo cavo attivo:', response.cavi[0]);\n            }\n          } else {\n            // Formato senza statistiche (retrocompatibilità)\n            console.log('Risposta senza statistiche ricevuta:', response);\n            setCaviAttivi(response || []);\n            console.log('Numero di cavi attivi trovati:', response ? response.length : 0);\n            if (response && response.length > 0) {\n              console.log('Primo cavo attivo:', response[0]);\n            }\n          }\n\n          if (!response || (Array.isArray(response) && response.length === 0) ||\n              (response.cavi && response.cavi.length === 0)) {\n            console.warn('Nessun cavo attivo trovato per il cantiere', cantiereIdNum);\n          }\n        } catch (caviError) {\n          console.error('Errore nel caricamento dei cavi attivi:', caviError);\n          console.error('Dettagli errore cavi attivi:', {\n            message: caviError.message,\n            status: caviError.status,\n            data: caviError.data,\n            stack: caviError.stack,\n            code: caviError.code,\n            name: caviError.name,\n            response: caviError.response ? {\n              status: caviError.response.status,\n              statusText: caviError.response.statusText,\n              data: caviError.response.data\n            } : 'No response'\n          });\n\n          // Non interrompere il flusso, continua con i cavi spare\n          setCaviAttivi([]);\n          console.warn('Continuazione del flusso dopo errore nei cavi attivi');\n\n          // Aggiungi un messaggio di errore visibile all'utente\n          setError(`Errore nel caricamento dei cavi attivi: ${caviError.message}. Controlla la console per maggiori dettagli.`);\n        }\n\n        // Carica i cavi spare con gestione degli errori migliorata\n        console.log('Caricamento cavi spare per cantiere:', cantiereIdNum);\n        try {\n          // Imposta un timeout per evitare che la richiesta rimanga bloccata\n          const timeoutPromise = new Promise((_, reject) => {\n            setTimeout(() => reject(new Error('Timeout durante il caricamento dei cavi spare')), 30000); // Aumentato a 30 secondi\n          });\n\n          // Esegui la richiesta con un timeout di sicurezza\n          console.log('Iniziando chiamata API per cavi spare...');\n          const sparePromise = caviService.getCavi(cantiereIdNum, 3);\n          const spare = await Promise.race([sparePromise, timeoutPromise]);\n\n          console.log('Cavi spare caricati:', spare);\n          console.log('Numero di cavi spare trovati:', spare ? spare.length : 0);\n          if (spare && spare.length > 0) {\n            console.log('Primo cavo spare:', spare[0]);\n          } else {\n            console.warn('Nessun cavo spare trovato per il cantiere', cantiereIdNum);\n          }\n          setCaviSpare(spare || []);\n        } catch (spareError) {\n          console.error('Errore nel caricamento dei cavi spare:', spareError);\n          console.error('Dettagli errore cavi spare:', {\n            message: spareError.message,\n            status: spareError.status,\n            data: spareError.data,\n            stack: spareError.stack,\n            code: spareError.code,\n            name: spareError.name,\n            response: spareError.response ? {\n              status: spareError.response.status,\n              statusText: spareError.response.statusText,\n              data: spareError.response.data\n            } : 'No response'\n          });\n\n          // Non interrompere il flusso, imposta un array vuoto\n          setCaviSpare([]);\n\n          // Aggiungi un messaggio di errore visibile all'utente se non c'è già un errore per i cavi attivi\n          if (!error) {\n            setError(`Errore nel caricamento dei cavi spare: ${spareError.message}. Controlla la console per maggiori dettagli.`);\n          }\n        }\n\n        // Se siamo arrivati qui, almeno abbiamo caricato l'interfaccia di base\n        setLoading(false);\n\n      } catch (err) {\n        console.error('Errore nel caricamento dei cavi:', err);\n        console.error('Dettagli errore generale:', {\n          message: err.message,\n          status: err.status || err.response?.status,\n          data: err.data || err.response?.data,\n          stack: err.stack\n        });\n\n        // Estrai il messaggio di errore dettagliato\n        let errorMessage = 'Errore sconosciuto';\n\n        if (err.message && err.message.includes('ID cantiere non valido')) {\n          errorMessage = err.message;\n        } else if (err.status === 401 || err.status === 403 ||\n                  err.response?.status === 401 || err.response?.status === 403) {\n          errorMessage = 'Sessione scaduta o non autorizzata. Effettua nuovamente il login.';\n        } else if (err.response?.data?.detail) {\n          // Estrai il messaggio di errore dettagliato dall'API\n          errorMessage = `Errore API: ${err.response.data.detail}`;\n        } else if (err.code === 'ERR_NETWORK') {\n          // Errore di rete\n          errorMessage = 'Network Error. Verifica che il backend sia in esecuzione e accessibile.';\n        } else if (err.message) {\n          errorMessage = err.message;\n        }\n\n        setError(`Impossibile caricare i cavi: ${errorMessage}. Riprova più tardi.`);\n\n        // Imposta array vuoti per evitare errori di rendering\n        setCaviAttivi([]);\n        setCaviSpare([]);\n      } finally {\n        setLoading(false);\n      }\n    };\n\n    fetchData();\n  }, []);\n\n  // Nessuna funzione di navigazione necessaria, tutto è gestito dal menu principale\n\n  // Funzione per visualizzare i cavi in formato tabellare\n  const renderCaviTable = (cavi) => {\n    if (!cavi || cavi.length === 0) {\n      return (\n        <Alert severity=\"info\">\n          Nessun cavo trovato in questa categoria.\n          <Button\n            variant=\"text\"\n            color=\"primary\"\n            onClick={() => window.location.reload()}\n            sx={{ ml: 2 }}\n          >\n            Riprova\n          </Button>\n        </Alert>\n      );\n    }\n\n    if (viewMode === 'table') {\n      return (\n        <TableContainer component={Paper} sx={{ mt: 2, overflowX: 'auto' }}>\n          <Table size=\"small\">\n            <TableHead>\n              <TableRow>\n                <TableCell>ID Cavo</TableCell>\n                <TableCell>Utility</TableCell>\n                <TableCell>Tipologia</TableCell>\n                <TableCell>N.Cond</TableCell>\n                <TableCell>Sezione</TableCell>\n                <TableCell>Ubicaz.Part.</TableCell>\n                <TableCell>Ubicaz.Arr.</TableCell>\n                <TableCell>Metri T.</TableCell>\n                <TableCell>Metri R.</TableCell>\n                <TableCell>Bobina</TableCell>\n                <TableCell>Stato</TableCell>\n                <TableCell>Collegamenti</TableCell>\n              </TableRow>\n            </TableHead>\n            <TableBody>\n              {cavi.map((cavo) => {\n                // Formatta i valori per la visualizzazione come nella CLI\n                const id_cavo = String(cavo.id_cavo).replace('$', '');\n                const utility = cavo.utility || '-';\n                const tipologia = cavo.tipologia || '-';\n\n                // Gestisci n_conduttori come stringa o numero\n                let n_conduttori = '-';\n                try {\n                  const n_cond_val = parseInt(cavo.n_conduttori, 10) || 0;\n                  n_conduttori = n_cond_val > 0 ? String(n_cond_val) : '-';\n                } catch (e) {\n                  n_conduttori = cavo.n_conduttori || '-';\n                }\n\n                // Gestisci sezione come stringa\n                let sezione = '-';\n                const sezione_val = cavo.sezione;\n                if (typeof sezione_val === 'number' && sezione_val === 0) {\n                  sezione = '-';\n                } else {\n                  sezione = sezione_val ? String(sezione_val) : '-';\n                }\n\n                const ubicazione_partenza = cavo.ubicazione_partenza || '-';\n                const ubicazione_arrivo = cavo.ubicazione_arrivo || '-';\n                const metri_teorici = cavo.metri_teorici ? `${parseFloat(cavo.metri_teorici).toFixed(2)}` : '-';\n                const stato = cavo.stato_installazione || '-';\n\n                // Metratura reale\n                const metri_reali = cavo.metratura_reale ? `${parseFloat(cavo.metratura_reale).toFixed(2)}` : '-';\n\n                // Bobina\n                const bobina = cavo.id_bobina || '-';\n\n                // Collegamenti\n                let stato_collegamenti = 'Non collegato';\n                const collegamenti = cavo.collegamenti || 0;\n                if (collegamenti === 1) {\n                  stato_collegamenti = 'Partenza';\n                } else if (collegamenti === 2) {\n                  stato_collegamenti = 'Arrivo';\n                } else if (collegamenti === 3) {\n                  stato_collegamenti = 'Completo';\n                }\n\n                // Colore per lo stato\n                let statoColor = 'inherit';\n                if (stato.toLowerCase().includes('posato') || stato.toLowerCase().includes('installato')) {\n                  statoColor = 'green';\n                } else if (stato.toLowerCase().includes('in corso') || stato.toLowerCase().includes('in posa')) {\n                  statoColor = 'orange';\n                } else if (stato.toLowerCase().includes('da posare') || stato.toLowerCase().includes('da installare')) {\n                  statoColor = 'red';\n                }\n\n                return (\n                  <TableRow key={cavo.id_cavo}>\n                    <TableCell>{id_cavo}</TableCell>\n                    <TableCell>{utility}</TableCell>\n                    <TableCell>{tipologia}</TableCell>\n                    <TableCell>{n_conduttori}</TableCell>\n                    <TableCell>{sezione}</TableCell>\n                    <TableCell>{ubicazione_partenza}</TableCell>\n                    <TableCell>{ubicazione_arrivo}</TableCell>\n                    <TableCell>{metri_teorici}</TableCell>\n                    <TableCell>{metri_reali}</TableCell>\n                    <TableCell>{bobina}</TableCell>\n                    <TableCell sx={{ color: statoColor }}>{stato}</TableCell>\n                    <TableCell>{stato_collegamenti}</TableCell>\n                  </TableRow>\n                );\n              })}\n            </TableBody>\n          </Table>\n        </TableContainer>\n      );\n    } else {\n      // Visualizzazione a schede (card)\n      return (\n        <Grid container spacing={2}>\n          {cavi.map((cavo) => (\n            <Grid item xs={12} sm={6} md={4} key={cavo.id_cavo}>\n              <Card>\n                <CardContent>\n                  <Typography variant=\"h6\" component=\"div\">\n                    {cavo.id_cavo}\n                  </Typography>\n                  <Typography variant=\"body2\" color=\"text.secondary\">\n                    Sistema: {cavo.sistema || 'N/A'}\n                  </Typography>\n                  <Typography variant=\"body2\" color=\"text.secondary\">\n                    Tipologia: {cavo.tipologia || 'N/A'}\n                  </Typography>\n                  <Typography variant=\"body2\" color=\"text.secondary\">\n                    Partenza: {cavo.ubicazione_partenza || 'N/A'} - {cavo.utenza_partenza || 'N/A'}\n                  </Typography>\n                  <Typography variant=\"body2\" color=\"text.secondary\">\n                    Arrivo: {cavo.ubicazione_arrivo || 'N/A'} - {cavo.utenza_arrivo || 'N/A'}\n                  </Typography>\n                  <Typography variant=\"body2\" color=\"text.secondary\">\n                    Metri teorici: {cavo.metri_teorici || 'N/A'}\n                  </Typography>\n                  <Typography variant=\"body2\" color=\"text.secondary\">\n                    Metratura reale: {cavo.metratura_reale || '0'}\n                  </Typography>\n                  <Typography variant=\"body2\" color=\"text.secondary\">\n                    Bobina: {cavo.id_bobina || 'N/A'}\n                  </Typography>\n                  <Typography variant=\"body2\" color=\"text.secondary\" sx={{\n                    color: cavo.stato_installazione?.toLowerCase().includes('posato') ||\n                           cavo.stato_installazione?.toLowerCase().includes('installato') ? 'green' :\n                           cavo.stato_installazione?.toLowerCase().includes('in corso') ||\n                           cavo.stato_installazione?.toLowerCase().includes('in posa') ? 'orange' :\n                           cavo.stato_installazione?.toLowerCase().includes('da posare') ||\n                           cavo.stato_installazione?.toLowerCase().includes('da installare') ? 'red' : 'inherit'\n                  }}>\n                    Stato: {cavo.stato_installazione || 'N/A'}\n                  </Typography>\n                  <Typography variant=\"body2\" color=\"text.secondary\">\n                    Collegamenti: {cavo.collegamenti === 0 ? 'Non collegato' :\n                                  cavo.collegamenti === 1 ? 'Partenza' :\n                                  cavo.collegamenti === 2 ? 'Arrivo' :\n                                  cavo.collegamenti === 3 ? 'Completo' : 'N/A'}\n                  </Typography>\n                </CardContent>\n              </Card>\n            </Grid>\n          ))}\n        </Grid>\n      );\n    }\n  };\n\n  // Gestisce il cambio di modalità di visualizzazione\n  const handleViewModeChange = (mode) => {\n    setViewMode(mode);\n  };\n\n  // Componente per visualizzare le statistiche dei cavi\n  const renderStatistics = () => {\n    if (!stats) return null;\n\n    return (\n      <Paper sx={{ p: 2, mb: 3, bgcolor: '#f5f5f5' }}>\n        <Typography variant=\"h6\" gutterBottom>\n          Statistiche Cavi\n        </Typography>\n        <Grid container spacing={2}>\n          <Grid item xs={12} md={6}>\n            <Box>\n              <Typography variant=\"subtitle1\">Informazioni Generali</Typography>\n              <Typography variant=\"body2\">Totale cavi: {stats.totale_cavi}</Typography>\n              <Typography variant=\"body2\">Metri teorici totali: {stats.totale_metri_teorici.toFixed(2)} m</Typography>\n              <Typography variant=\"body2\">Metri posati totali: {stats.totale_metri_posati.toFixed(2)} m ({stats.percentuale_completamento.toFixed(1)}% del totale)</Typography>\n            </Box>\n          </Grid>\n          <Grid item xs={12} md={6}>\n            <Box>\n              <Typography variant=\"subtitle1\">Distribuzione per stato</Typography>\n              {Object.entries(stats.stati).map(([stato, conteggio]) => (\n                <Typography key={stato} variant=\"body2\">{stato}: {conteggio} cavi</Typography>\n              ))}\n            </Box>\n          </Grid>\n          <Grid item xs={12}>\n            <Box>\n              <Typography variant=\"subtitle1\">Collegamenti</Typography>\n              <Typography variant=\"body2\">Non collegati: {stats.collegamenti.non_collegati}</Typography>\n              <Typography variant=\"body2\">Collegati lato partenza: {stats.collegamenti.collegati_partenza}</Typography>\n              <Typography variant=\"body2\">Collegati lato arrivo: {stats.collegamenti.collegati_arrivo}</Typography>\n              <Typography variant=\"body2\">Completamente collegati: {stats.collegamenti.completamente_collegati}</Typography>\n            </Box>\n          </Grid>\n        </Grid>\n      </Paper>\n    );\n  };\n\n  return (\n    <Box className=\"cavi-page\">\n      <Paper sx={{ mb: 3, p: 2 }}>\n        <Box sx={{ display: 'flex', justifyContent: 'flex-end', alignItems: 'center', gap: 2 }}>\n          <IconButton\n            onClick={() => window.location.reload()}\n            title=\"Ricarica la pagina\"\n          >\n            <RefreshIcon />\n          </IconButton>\n          <Box sx={{ display: 'flex', border: '1px solid #ddd', borderRadius: 1 }}>\n            <IconButton\n              sx={{ color: viewMode === 'table' ? '#212529' : '#6c757d' }}\n              onClick={() => handleViewModeChange('table')}\n              title=\"Vista tabellare\"\n            >\n              <ViewListIcon />\n            </IconButton>\n            <IconButton\n              sx={{ color: viewMode === 'card' ? '#212529' : '#6c757d' }}\n              onClick={() => handleViewModeChange('card')}\n              title=\"Vista a schede\"\n            >\n              <ViewModuleIcon />\n            </IconButton>\n          </Box>\n        </Box>\n      </Paper>\n\n      {loading ? (\n        <Box sx={{ display: 'flex', flexDirection: 'column', alignItems: 'center', mt: 4 }}>\n          <Typography>Caricamento cavi...</Typography>\n          <Button\n            variant=\"outlined\"\n            color=\"primary\"\n            onClick={() => window.location.reload()}\n            sx={{ mt: 2 }}\n          >\n            Ricarica la pagina\n          </Button>\n        </Box>\n      ) : error ? (\n        <Box>\n          <Alert severity=\"error\" sx={{ mb: 2 }}>\n            {error}\n            {error.includes('Network Error') && (\n              <Typography variant=\"body2\" sx={{ mt: 1 }}>\n                <strong>Suggerimento:</strong> Verifica che il server backend sia in esecuzione sulla porta 8001.\n                <br />\n                Puoi avviare il backend eseguendo il file <code>run_system.py</code> nella cartella principale del progetto.\n              </Typography>\n            )}\n          </Alert>\n          <Box sx={{ display: 'flex', gap: 2 }}>\n            <Button\n              variant=\"contained\"\n              className=\"primary-button\"\n              onClick={() => window.location.reload()}\n            >\n              Ricarica la pagina\n            </Button>\n          </Box>\n        </Box>\n      ) : (\n        <Box>\n          {/* Mostra le statistiche se disponibili */}\n          {stats && renderStatistics()}\n\n          <Box sx={{ mb: 3 }}>\n            <Typography variant=\"h5\" gutterBottom>\n              Cavi Attivi\n            </Typography>\n            {renderCaviTable(caviAttivi)}\n          </Box>\n\n          <Box sx={{ mt: 4 }}>\n            <Typography variant=\"h5\" gutterBottom>\n              Cavi Spare\n            </Typography>\n            {renderCaviTable(caviSpare)}\n          </Box>\n        </Box>\n      )}\n    </Box>\n  );\n};\n\nexport default VisualizzaCaviPage;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAClD,SACEC,GAAG,EACHC,UAAU,EACVC,KAAK,EACLC,MAAM,EACNC,IAAI,EACJC,IAAI,EACJC,WAAW,EACXC,KAAK,EACLC,UAAU,EACVC,KAAK,EACLC,SAAS,EACTC,SAAS,EACTC,cAAc,EACdC,SAAS,EACTC,QAAQ,EACRC,IAAI,EACJC,GAAG,QACE,eAAe;AACtB,SACEC,SAAS,IAAIC,aAAa,EAC1BC,OAAO,IAAIC,WAAW,EACtBC,IAAI,IAAIC,QAAQ,EAChBC,QAAQ,IAAIC,YAAY,EACxBC,UAAU,IAAIC,cAAc,QACvB,qBAAqB;AAC5B,SAASC,WAAW,QAAQ,kBAAkB;AAC9C,SAASC,OAAO,QAAQ,2BAA2B;AACnD,OAAOC,WAAW,MAAM,4BAA4B;AACpD,OAAO,gBAAgB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAExB,MAAMC,kBAAkB,GAAGA,CAAA,KAAM;EAAAC,EAAA;EAC/B,MAAM;IAAEC,eAAe;IAAEC;EAAK,CAAC,GAAGP,OAAO,CAAC,CAAC;EAC3C,MAAMQ,QAAQ,GAAGT,WAAW,CAAC,CAAC;EAC9B,MAAM,CAACU,UAAU,EAAEC,aAAa,CAAC,GAAGxC,QAAQ,CAAC,IAAI,CAAC;EAClD,MAAM,CAACyC,YAAY,EAAEC,eAAe,CAAC,GAAG1C,QAAQ,CAAC,EAAE,CAAC;EACpD,MAAM,CAAC2C,UAAU,EAAEC,aAAa,CAAC,GAAG5C,QAAQ,CAAC,EAAE,CAAC;EAChD,MAAM,CAAC6C,SAAS,EAAEC,YAAY,CAAC,GAAG9C,QAAQ,CAAC,EAAE,CAAC;EAC9C,MAAM,CAAC+C,OAAO,EAAEC,UAAU,CAAC,GAAGhD,QAAQ,CAAC,IAAI,CAAC;EAC5C,MAAM,CAACiD,KAAK,EAAEC,QAAQ,CAAC,GAAGlD,QAAQ,CAAC,IAAI,CAAC;EACxC,MAAM,CAACmD,QAAQ,EAAEC,WAAW,CAAC,GAAGpD,QAAQ,CAAC,OAAO,CAAC,CAAC,CAAC;EACnD,MAAM,CAACqD,KAAK,EAAEC,QAAQ,CAAC,GAAGtD,QAAQ,CAAC,IAAI,CAAC,CAAC,CAAC;;EAE1C;EACAC,SAAS,CAAC,MAAM;IACd,MAAMsD,SAAS,GAAG,MAAAA,CAAA,KAAY;MAC5B,IAAI;QACFC,OAAO,CAACC,GAAG,CAAC,wCAAwC,CAAC;;QAErD;QACA,MAAMC,KAAK,GAAGC,YAAY,CAACC,OAAO,CAAC,OAAO,CAAC;QAC3CJ,OAAO,CAACC,GAAG,CAAC,iBAAiB,EAAE,CAAC,CAACC,KAAK,CAAC;QACvC,IAAI,CAACA,KAAK,EAAE;UACVR,QAAQ,CAAC,iDAAiD,CAAC;UAC3DF,UAAU,CAAC,KAAK,CAAC;UACjB;QACF;;QAEA;QACA,IAAIa,kBAAkB,GAAGF,YAAY,CAACC,OAAO,CAAC,oBAAoB,CAAC;QACnE,IAAIE,oBAAoB,GAAGH,YAAY,CAACC,OAAO,CAAC,sBAAsB,CAAC;QAEvEJ,OAAO,CAACC,GAAG,CAAC,wCAAwC,EAAE;UAAEI,kBAAkB;UAAEC;QAAqB,CAAC,CAAC;QACnGN,OAAO,CAACC,GAAG,CAAC,cAAc,EAAEpB,IAAI,CAAC;;QAEjC;QACAmB,OAAO,CAACC,GAAG,CAAC,wCAAwC,CAAC;QACrD,KAAK,IAAIM,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGJ,YAAY,CAACK,MAAM,EAAED,CAAC,EAAE,EAAE;UAC5C,MAAME,GAAG,GAAGN,YAAY,CAACM,GAAG,CAACF,CAAC,CAAC;UAC/BP,OAAO,CAACC,GAAG,CAAC,GAAGQ,GAAG,KAAKN,YAAY,CAACC,OAAO,CAACK,GAAG,CAAC,EAAE,CAAC;QACrD;;QAEA;QACA,IAAI,CAAA5B,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAE6B,IAAI,MAAK,eAAe,EAAE;UAClCV,OAAO,CAACC,GAAG,CAAC,6EAA6E,CAAC;;UAE1F;UACA,IAAIpB,IAAI,CAAC8B,WAAW,EAAE;YACpBX,OAAO,CAACC,GAAG,CAAC,sCAAsC,EAAEpB,IAAI,CAAC8B,WAAW,CAAC;YACrEN,kBAAkB,GAAGxB,IAAI,CAAC8B,WAAW,CAACC,QAAQ,CAAC,CAAC;YAChDN,oBAAoB,GAAGzB,IAAI,CAACgC,aAAa,IAAI,YAAYhC,IAAI,CAAC8B,WAAW,EAAE;;YAE3E;YACAR,YAAY,CAACW,OAAO,CAAC,oBAAoB,EAAET,kBAAkB,CAAC;YAC9DF,YAAY,CAACW,OAAO,CAAC,sBAAsB,EAAER,oBAAoB,CAAC;YAClEN,OAAO,CAACC,GAAG,CAAC,uCAAuC,EAAEI,kBAAkB,CAAC;UAC1E,CAAC,MAAM;YACL;YACA,IAAI;cACFL,OAAO,CAACC,GAAG,CAAC,qEAAqE,CAAC;cAClF,MAAMC,KAAK,GAAGC,YAAY,CAACC,OAAO,CAAC,OAAO,CAAC;cAC3C,IAAIF,KAAK,EAAE;gBACT;gBACA,MAAMa,SAAS,GAAGb,KAAK,CAACc,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC;gBACrC,MAAMC,MAAM,GAAGF,SAAS,CAACG,OAAO,CAAC,IAAI,EAAE,GAAG,CAAC,CAACA,OAAO,CAAC,IAAI,EAAE,GAAG,CAAC;gBAC9D,MAAMC,WAAW,GAAGC,kBAAkB,CAACC,IAAI,CAACJ,MAAM,CAAC,CAACD,KAAK,CAAC,EAAE,CAAC,CAACM,GAAG,CAACC,CAAC,IAAI;kBACrE,OAAO,GAAG,GAAG,CAAC,IAAI,GAAGA,CAAC,CAACC,UAAU,CAAC,CAAC,CAAC,CAACZ,QAAQ,CAAC,EAAE,CAAC,EAAEa,KAAK,CAAC,CAAC,CAAC,CAAC;gBAC9D,CAAC,CAAC,CAACC,IAAI,CAAC,EAAE,CAAC,CAAC;gBAEZ,MAAMC,OAAO,GAAGC,IAAI,CAACC,KAAK,CAACV,WAAW,CAAC;gBACvCnB,OAAO,CAACC,GAAG,CAAC,wBAAwB,EAAE0B,OAAO,CAAC;gBAE9C,IAAIA,OAAO,CAAChB,WAAW,EAAE;kBACvBX,OAAO,CAACC,GAAG,CAAC,oCAAoC,EAAE0B,OAAO,CAAChB,WAAW,CAAC;kBACtEN,kBAAkB,GAAGsB,OAAO,CAAChB,WAAW,CAACC,QAAQ,CAAC,CAAC;kBACnD;kBACAN,oBAAoB,GAAG,YAAYqB,OAAO,CAAChB,WAAW,EAAE;;kBAExD;kBACAR,YAAY,CAACW,OAAO,CAAC,oBAAoB,EAAET,kBAAkB,CAAC;kBAC9DF,YAAY,CAACW,OAAO,CAAC,sBAAsB,EAAER,oBAAoB,CAAC;kBAClEN,OAAO,CAACC,GAAG,CAAC,uCAAuC,EAAEI,kBAAkB,CAAC;gBAC1E;cACF;YACF,CAAC,CAAC,OAAOyB,CAAC,EAAE;cACV9B,OAAO,CAACP,KAAK,CAAC,6CAA6C,EAAEqC,CAAC,CAAC;YACjE;UACF;QACF;;QAEA;QACA,IAAI,CAACzB,kBAAkB,IAAIA,kBAAkB,KAAK,WAAW,IAAIA,kBAAkB,KAAK,MAAM,EAAE;UAC9FL,OAAO,CAAC+B,IAAI,CAAC,6EAA6E,CAAC;UAC3F;UACA1B,kBAAkB,GAAG,GAAG,CAAC,CAAC;UAC1BC,oBAAoB,GAAG,gBAAgB;;UAEvC;UACAH,YAAY,CAACW,OAAO,CAAC,oBAAoB,EAAET,kBAAkB,CAAC;UAC9DF,YAAY,CAACW,OAAO,CAAC,sBAAsB,EAAER,oBAAoB,CAAC;UAClEN,OAAO,CAACC,GAAG,CAAC,iDAAiD,EAAEI,kBAAkB,CAAC;QACpF;;QAEA;QACA,IAAI,CAACA,kBAAkB,EAAE;UACvBX,QAAQ,CAAC,8DAA8D,CAAC;UACxEF,UAAU,CAAC,KAAK,CAAC;UACjB;QACF;;QAEA;QACA,MAAMwC,aAAa,GAAGC,QAAQ,CAAC5B,kBAAkB,EAAE,EAAE,CAAC;QACtDL,OAAO,CAACC,GAAG,CAAC,kCAAkC,EAAE+B,aAAa,CAAC;QAC9D,IAAIE,KAAK,CAACF,aAAa,CAAC,EAAE;UACxBtC,QAAQ,CAAC,2BAA2BW,kBAAkB,mCAAmC,CAAC;UAC1Fb,UAAU,CAAC,KAAK,CAAC;UACjB;QACF;;QAEA;QACAR,aAAa,CAACgD,aAAa,CAAC;QAC5B9C,eAAe,CAACoB,oBAAoB,IAAI,YAAY0B,aAAa,EAAE,CAAC;;QAEpE;QACAhC,OAAO,CAACC,GAAG,CAAC,uCAAuC,EAAE+B,aAAa,CAAC;QACnE,IAAI;UACF;UACA,MAAMG,cAAc,GAAG,IAAIC,OAAO,CAAC,CAACC,CAAC,EAAEC,MAAM,KAAK;YAChDC,UAAU,CAAC,MAAMD,MAAM,CAAC,IAAIE,KAAK,CAAC,gDAAgD,CAAC,CAAC,EAAE,KAAK,CAAC,CAAC,CAAC;UAChG,CAAC,CAAC;;UAEF;UACAxC,OAAO,CAACC,GAAG,CAAC,2DAA2D,CAAC;UACxE,MAAMwC,WAAW,GAAGlE,WAAW,CAACmE,OAAO,CAACV,aAAa,EAAE,CAAC,EAAE,IAAI,CAAC,CAAC,CAAC;UACjE,MAAMW,QAAQ,GAAG,MAAMP,OAAO,CAACQ,IAAI,CAAC,CAACH,WAAW,EAAEN,cAAc,CAAC,CAAC;;UAElE;UACA,IAAIQ,QAAQ,IAAIA,QAAQ,CAACE,IAAI,EAAE;YAC7B;YACA7C,OAAO,CAACC,GAAG,CAAC,oCAAoC,EAAE0C,QAAQ,CAAC;YAC3DvD,aAAa,CAACuD,QAAQ,CAACE,IAAI,IAAI,EAAE,CAAC;YAClC/C,QAAQ,CAAC6C,QAAQ,CAAC9C,KAAK,IAAI,IAAI,CAAC;YAChCG,OAAO,CAACC,GAAG,CAAC,gCAAgC,EAAE0C,QAAQ,CAACE,IAAI,GAAGF,QAAQ,CAACE,IAAI,CAACrC,MAAM,GAAG,CAAC,CAAC;YACvF,IAAImC,QAAQ,CAACE,IAAI,IAAIF,QAAQ,CAACE,IAAI,CAACrC,MAAM,GAAG,CAAC,EAAE;cAC7CR,OAAO,CAACC,GAAG,CAAC,oBAAoB,EAAE0C,QAAQ,CAACE,IAAI,CAAC,CAAC,CAAC,CAAC;YACrD;UACF,CAAC,MAAM;YACL;YACA7C,OAAO,CAACC,GAAG,CAAC,sCAAsC,EAAE0C,QAAQ,CAAC;YAC7DvD,aAAa,CAACuD,QAAQ,IAAI,EAAE,CAAC;YAC7B3C,OAAO,CAACC,GAAG,CAAC,gCAAgC,EAAE0C,QAAQ,GAAGA,QAAQ,CAACnC,MAAM,GAAG,CAAC,CAAC;YAC7E,IAAImC,QAAQ,IAAIA,QAAQ,CAACnC,MAAM,GAAG,CAAC,EAAE;cACnCR,OAAO,CAACC,GAAG,CAAC,oBAAoB,EAAE0C,QAAQ,CAAC,CAAC,CAAC,CAAC;YAChD;UACF;UAEA,IAAI,CAACA,QAAQ,IAAKG,KAAK,CAACC,OAAO,CAACJ,QAAQ,CAAC,IAAIA,QAAQ,CAACnC,MAAM,KAAK,CAAE,IAC9DmC,QAAQ,CAACE,IAAI,IAAIF,QAAQ,CAACE,IAAI,CAACrC,MAAM,KAAK,CAAE,EAAE;YACjDR,OAAO,CAAC+B,IAAI,CAAC,4CAA4C,EAAEC,aAAa,CAAC;UAC3E;QACF,CAAC,CAAC,OAAOgB,SAAS,EAAE;UAClBhD,OAAO,CAACP,KAAK,CAAC,yCAAyC,EAAEuD,SAAS,CAAC;UACnEhD,OAAO,CAACP,KAAK,CAAC,8BAA8B,EAAE;YAC5CwD,OAAO,EAAED,SAAS,CAACC,OAAO;YAC1BC,MAAM,EAAEF,SAAS,CAACE,MAAM;YACxBC,IAAI,EAAEH,SAAS,CAACG,IAAI;YACpBC,KAAK,EAAEJ,SAAS,CAACI,KAAK;YACtBC,IAAI,EAAEL,SAAS,CAACK,IAAI;YACpBC,IAAI,EAAEN,SAAS,CAACM,IAAI;YACpBX,QAAQ,EAAEK,SAAS,CAACL,QAAQ,GAAG;cAC7BO,MAAM,EAAEF,SAAS,CAACL,QAAQ,CAACO,MAAM;cACjCK,UAAU,EAAEP,SAAS,CAACL,QAAQ,CAACY,UAAU;cACzCJ,IAAI,EAAEH,SAAS,CAACL,QAAQ,CAACQ;YAC3B,CAAC,GAAG;UACN,CAAC,CAAC;;UAEF;UACA/D,aAAa,CAAC,EAAE,CAAC;UACjBY,OAAO,CAAC+B,IAAI,CAAC,sDAAsD,CAAC;;UAEpE;UACArC,QAAQ,CAAC,2CAA2CsD,SAAS,CAACC,OAAO,+CAA+C,CAAC;QACvH;;QAEA;QACAjD,OAAO,CAACC,GAAG,CAAC,sCAAsC,EAAE+B,aAAa,CAAC;QAClE,IAAI;UACF;UACA,MAAMG,cAAc,GAAG,IAAIC,OAAO,CAAC,CAACC,CAAC,EAAEC,MAAM,KAAK;YAChDC,UAAU,CAAC,MAAMD,MAAM,CAAC,IAAIE,KAAK,CAAC,+CAA+C,CAAC,CAAC,EAAE,KAAK,CAAC,CAAC,CAAC;UAC/F,CAAC,CAAC;;UAEF;UACAxC,OAAO,CAACC,GAAG,CAAC,0CAA0C,CAAC;UACvD,MAAMuD,YAAY,GAAGjF,WAAW,CAACmE,OAAO,CAACV,aAAa,EAAE,CAAC,CAAC;UAC1D,MAAMyB,KAAK,GAAG,MAAMrB,OAAO,CAACQ,IAAI,CAAC,CAACY,YAAY,EAAErB,cAAc,CAAC,CAAC;UAEhEnC,OAAO,CAACC,GAAG,CAAC,sBAAsB,EAAEwD,KAAK,CAAC;UAC1CzD,OAAO,CAACC,GAAG,CAAC,+BAA+B,EAAEwD,KAAK,GAAGA,KAAK,CAACjD,MAAM,GAAG,CAAC,CAAC;UACtE,IAAIiD,KAAK,IAAIA,KAAK,CAACjD,MAAM,GAAG,CAAC,EAAE;YAC7BR,OAAO,CAACC,GAAG,CAAC,mBAAmB,EAAEwD,KAAK,CAAC,CAAC,CAAC,CAAC;UAC5C,CAAC,MAAM;YACLzD,OAAO,CAAC+B,IAAI,CAAC,2CAA2C,EAAEC,aAAa,CAAC;UAC1E;UACA1C,YAAY,CAACmE,KAAK,IAAI,EAAE,CAAC;QAC3B,CAAC,CAAC,OAAOC,UAAU,EAAE;UACnB1D,OAAO,CAACP,KAAK,CAAC,wCAAwC,EAAEiE,UAAU,CAAC;UACnE1D,OAAO,CAACP,KAAK,CAAC,6BAA6B,EAAE;YAC3CwD,OAAO,EAAES,UAAU,CAACT,OAAO;YAC3BC,MAAM,EAAEQ,UAAU,CAACR,MAAM;YACzBC,IAAI,EAAEO,UAAU,CAACP,IAAI;YACrBC,KAAK,EAAEM,UAAU,CAACN,KAAK;YACvBC,IAAI,EAAEK,UAAU,CAACL,IAAI;YACrBC,IAAI,EAAEI,UAAU,CAACJ,IAAI;YACrBX,QAAQ,EAAEe,UAAU,CAACf,QAAQ,GAAG;cAC9BO,MAAM,EAAEQ,UAAU,CAACf,QAAQ,CAACO,MAAM;cAClCK,UAAU,EAAEG,UAAU,CAACf,QAAQ,CAACY,UAAU;cAC1CJ,IAAI,EAAEO,UAAU,CAACf,QAAQ,CAACQ;YAC5B,CAAC,GAAG;UACN,CAAC,CAAC;;UAEF;UACA7D,YAAY,CAAC,EAAE,CAAC;;UAEhB;UACA,IAAI,CAACG,KAAK,EAAE;YACVC,QAAQ,CAAC,0CAA0CgE,UAAU,CAACT,OAAO,+CAA+C,CAAC;UACvH;QACF;;QAEA;QACAzD,UAAU,CAAC,KAAK,CAAC;MAEnB,CAAC,CAAC,OAAOmE,GAAG,EAAE;QAAA,IAAAC,aAAA,EAAAC,cAAA,EAAAC,cAAA,EAAAC,cAAA,EAAAC,cAAA,EAAAC,mBAAA;QACZjE,OAAO,CAACP,KAAK,CAAC,kCAAkC,EAAEkE,GAAG,CAAC;QACtD3D,OAAO,CAACP,KAAK,CAAC,2BAA2B,EAAE;UACzCwD,OAAO,EAAEU,GAAG,CAACV,OAAO;UACpBC,MAAM,EAAES,GAAG,CAACT,MAAM,MAAAU,aAAA,GAAID,GAAG,CAAChB,QAAQ,cAAAiB,aAAA,uBAAZA,aAAA,CAAcV,MAAM;UAC1CC,IAAI,EAAEQ,GAAG,CAACR,IAAI,MAAAU,cAAA,GAAIF,GAAG,CAAChB,QAAQ,cAAAkB,cAAA,uBAAZA,cAAA,CAAcV,IAAI;UACpCC,KAAK,EAAEO,GAAG,CAACP;QACb,CAAC,CAAC;;QAEF;QACA,IAAIc,YAAY,GAAG,oBAAoB;QAEvC,IAAIP,GAAG,CAACV,OAAO,IAAIU,GAAG,CAACV,OAAO,CAACkB,QAAQ,CAAC,wBAAwB,CAAC,EAAE;UACjED,YAAY,GAAGP,GAAG,CAACV,OAAO;QAC5B,CAAC,MAAM,IAAIU,GAAG,CAACT,MAAM,KAAK,GAAG,IAAIS,GAAG,CAACT,MAAM,KAAK,GAAG,IACzC,EAAAY,cAAA,GAAAH,GAAG,CAAChB,QAAQ,cAAAmB,cAAA,uBAAZA,cAAA,CAAcZ,MAAM,MAAK,GAAG,IAAI,EAAAa,cAAA,GAAAJ,GAAG,CAAChB,QAAQ,cAAAoB,cAAA,uBAAZA,cAAA,CAAcb,MAAM,MAAK,GAAG,EAAE;UACtEgB,YAAY,GAAG,mEAAmE;QACpF,CAAC,MAAM,KAAAF,cAAA,GAAIL,GAAG,CAAChB,QAAQ,cAAAqB,cAAA,gBAAAC,mBAAA,GAAZD,cAAA,CAAcb,IAAI,cAAAc,mBAAA,eAAlBA,mBAAA,CAAoBG,MAAM,EAAE;UACrC;UACAF,YAAY,GAAG,eAAeP,GAAG,CAAChB,QAAQ,CAACQ,IAAI,CAACiB,MAAM,EAAE;QAC1D,CAAC,MAAM,IAAIT,GAAG,CAACN,IAAI,KAAK,aAAa,EAAE;UACrC;UACAa,YAAY,GAAG,yEAAyE;QAC1F,CAAC,MAAM,IAAIP,GAAG,CAACV,OAAO,EAAE;UACtBiB,YAAY,GAAGP,GAAG,CAACV,OAAO;QAC5B;QAEAvD,QAAQ,CAAC,gCAAgCwE,YAAY,sBAAsB,CAAC;;QAE5E;QACA9E,aAAa,CAAC,EAAE,CAAC;QACjBE,YAAY,CAAC,EAAE,CAAC;MAClB,CAAC,SAAS;QACRE,UAAU,CAAC,KAAK,CAAC;MACnB;IACF,CAAC;IAEDO,SAAS,CAAC,CAAC;EACb,CAAC,EAAE,EAAE,CAAC;;EAEN;;EAEA;EACA,MAAMsE,eAAe,GAAIxB,IAAI,IAAK;IAChC,IAAI,CAACA,IAAI,IAAIA,IAAI,CAACrC,MAAM,KAAK,CAAC,EAAE;MAC9B,oBACE/B,OAAA,CAACxB,KAAK;QAACqH,QAAQ,EAAC,MAAM;QAAAC,QAAA,GAAC,0CAErB,eAAA9F,OAAA,CAAC5B,MAAM;UACL2H,OAAO,EAAC,MAAM;UACdC,KAAK,EAAC,SAAS;UACfC,OAAO,EAAEA,CAAA,KAAMC,MAAM,CAACC,QAAQ,CAACC,MAAM,CAAC,CAAE;UACxCC,EAAE,EAAE;YAAEC,EAAE,EAAE;UAAE,CAAE;UAAAR,QAAA,EACf;QAED;UAAAS,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACJ,CAAC;IAEZ;IAEA,IAAIxF,QAAQ,KAAK,OAAO,EAAE;MACxB,oBACElB,OAAA,CAACnB,cAAc;QAAC8H,SAAS,EAAExI,KAAM;QAACkI,EAAE,EAAE;UAAEO,EAAE,EAAE,CAAC;UAAEC,SAAS,EAAE;QAAO,CAAE;QAAAf,QAAA,eACjE9F,OAAA,CAACtB,KAAK;UAACoI,IAAI,EAAC,OAAO;UAAAhB,QAAA,gBACjB9F,OAAA,CAAClB,SAAS;YAAAgH,QAAA,eACR9F,OAAA,CAACjB,QAAQ;cAAA+G,QAAA,gBACP9F,OAAA,CAACpB,SAAS;gBAAAkH,QAAA,EAAC;cAAO;gBAAAS,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAW,CAAC,eAC9B1G,OAAA,CAACpB,SAAS;gBAAAkH,QAAA,EAAC;cAAO;gBAAAS,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAW,CAAC,eAC9B1G,OAAA,CAACpB,SAAS;gBAAAkH,QAAA,EAAC;cAAS;gBAAAS,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAW,CAAC,eAChC1G,OAAA,CAACpB,SAAS;gBAAAkH,QAAA,EAAC;cAAM;gBAAAS,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAW,CAAC,eAC7B1G,OAAA,CAACpB,SAAS;gBAAAkH,QAAA,EAAC;cAAO;gBAAAS,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAW,CAAC,eAC9B1G,OAAA,CAACpB,SAAS;gBAAAkH,QAAA,EAAC;cAAY;gBAAAS,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAW,CAAC,eACnC1G,OAAA,CAACpB,SAAS;gBAAAkH,QAAA,EAAC;cAAW;gBAAAS,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAW,CAAC,eAClC1G,OAAA,CAACpB,SAAS;gBAAAkH,QAAA,EAAC;cAAQ;gBAAAS,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAW,CAAC,eAC/B1G,OAAA,CAACpB,SAAS;gBAAAkH,QAAA,EAAC;cAAQ;gBAAAS,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAW,CAAC,eAC/B1G,OAAA,CAACpB,SAAS;gBAAAkH,QAAA,EAAC;cAAM;gBAAAS,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAW,CAAC,eAC7B1G,OAAA,CAACpB,SAAS;gBAAAkH,QAAA,EAAC;cAAK;gBAAAS,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAW,CAAC,eAC5B1G,OAAA,CAACpB,SAAS;gBAAAkH,QAAA,EAAC;cAAY;gBAAAS,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAW,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC3B;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACF,CAAC,eACZ1G,OAAA,CAACrB,SAAS;YAAAmH,QAAA,EACP1B,IAAI,CAACvB,GAAG,CAAEkE,IAAI,IAAK;cAClB;cACA,MAAMC,OAAO,GAAGC,MAAM,CAACF,IAAI,CAACC,OAAO,CAAC,CAACvE,OAAO,CAAC,GAAG,EAAE,EAAE,CAAC;cACrD,MAAMyE,OAAO,GAAGH,IAAI,CAACG,OAAO,IAAI,GAAG;cACnC,MAAMC,SAAS,GAAGJ,IAAI,CAACI,SAAS,IAAI,GAAG;;cAEvC;cACA,IAAIC,YAAY,GAAG,GAAG;cACtB,IAAI;gBACF,MAAMC,UAAU,GAAG7D,QAAQ,CAACuD,IAAI,CAACK,YAAY,EAAE,EAAE,CAAC,IAAI,CAAC;gBACvDA,YAAY,GAAGC,UAAU,GAAG,CAAC,GAAGJ,MAAM,CAACI,UAAU,CAAC,GAAG,GAAG;cAC1D,CAAC,CAAC,OAAOhE,CAAC,EAAE;gBACV+D,YAAY,GAAGL,IAAI,CAACK,YAAY,IAAI,GAAG;cACzC;;cAEA;cACA,IAAIE,OAAO,GAAG,GAAG;cACjB,MAAMC,WAAW,GAAGR,IAAI,CAACO,OAAO;cAChC,IAAI,OAAOC,WAAW,KAAK,QAAQ,IAAIA,WAAW,KAAK,CAAC,EAAE;gBACxDD,OAAO,GAAG,GAAG;cACf,CAAC,MAAM;gBACLA,OAAO,GAAGC,WAAW,GAAGN,MAAM,CAACM,WAAW,CAAC,GAAG,GAAG;cACnD;cAEA,MAAMC,mBAAmB,GAAGT,IAAI,CAACS,mBAAmB,IAAI,GAAG;cAC3D,MAAMC,iBAAiB,GAAGV,IAAI,CAACU,iBAAiB,IAAI,GAAG;cACvD,MAAMC,aAAa,GAAGX,IAAI,CAACW,aAAa,GAAG,GAAGC,UAAU,CAACZ,IAAI,CAACW,aAAa,CAAC,CAACE,OAAO,CAAC,CAAC,CAAC,EAAE,GAAG,GAAG;cAC/F,MAAMC,KAAK,GAAGd,IAAI,CAACe,mBAAmB,IAAI,GAAG;;cAE7C;cACA,MAAMC,WAAW,GAAGhB,IAAI,CAACiB,eAAe,GAAG,GAAGL,UAAU,CAACZ,IAAI,CAACiB,eAAe,CAAC,CAACJ,OAAO,CAAC,CAAC,CAAC,EAAE,GAAG,GAAG;;cAEjG;cACA,MAAMK,MAAM,GAAGlB,IAAI,CAACmB,SAAS,IAAI,GAAG;;cAEpC;cACA,IAAIC,kBAAkB,GAAG,eAAe;cACxC,MAAMC,YAAY,GAAGrB,IAAI,CAACqB,YAAY,IAAI,CAAC;cAC3C,IAAIA,YAAY,KAAK,CAAC,EAAE;gBACtBD,kBAAkB,GAAG,UAAU;cACjC,CAAC,MAAM,IAAIC,YAAY,KAAK,CAAC,EAAE;gBAC7BD,kBAAkB,GAAG,QAAQ;cAC/B,CAAC,MAAM,IAAIC,YAAY,KAAK,CAAC,EAAE;gBAC7BD,kBAAkB,GAAG,UAAU;cACjC;;cAEA;cACA,IAAIE,UAAU,GAAG,SAAS;cAC1B,IAAIR,KAAK,CAACS,WAAW,CAAC,CAAC,CAAC5C,QAAQ,CAAC,QAAQ,CAAC,IAAImC,KAAK,CAACS,WAAW,CAAC,CAAC,CAAC5C,QAAQ,CAAC,YAAY,CAAC,EAAE;gBACxF2C,UAAU,GAAG,OAAO;cACtB,CAAC,MAAM,IAAIR,KAAK,CAACS,WAAW,CAAC,CAAC,CAAC5C,QAAQ,CAAC,UAAU,CAAC,IAAImC,KAAK,CAACS,WAAW,CAAC,CAAC,CAAC5C,QAAQ,CAAC,SAAS,CAAC,EAAE;gBAC9F2C,UAAU,GAAG,QAAQ;cACvB,CAAC,MAAM,IAAIR,KAAK,CAACS,WAAW,CAAC,CAAC,CAAC5C,QAAQ,CAAC,WAAW,CAAC,IAAImC,KAAK,CAACS,WAAW,CAAC,CAAC,CAAC5C,QAAQ,CAAC,eAAe,CAAC,EAAE;gBACrG2C,UAAU,GAAG,KAAK;cACpB;cAEA,oBACErI,OAAA,CAACjB,QAAQ;gBAAA+G,QAAA,gBACP9F,OAAA,CAACpB,SAAS;kBAAAkH,QAAA,EAAEkB;gBAAO;kBAAAT,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAY,CAAC,eAChC1G,OAAA,CAACpB,SAAS;kBAAAkH,QAAA,EAAEoB;gBAAO;kBAAAX,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAY,CAAC,eAChC1G,OAAA,CAACpB,SAAS;kBAAAkH,QAAA,EAAEqB;gBAAS;kBAAAZ,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAY,CAAC,eAClC1G,OAAA,CAACpB,SAAS;kBAAAkH,QAAA,EAAEsB;gBAAY;kBAAAb,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAY,CAAC,eACrC1G,OAAA,CAACpB,SAAS;kBAAAkH,QAAA,EAAEwB;gBAAO;kBAAAf,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAY,CAAC,eAChC1G,OAAA,CAACpB,SAAS;kBAAAkH,QAAA,EAAE0B;gBAAmB;kBAAAjB,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAY,CAAC,eAC5C1G,OAAA,CAACpB,SAAS;kBAAAkH,QAAA,EAAE2B;gBAAiB;kBAAAlB,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAY,CAAC,eAC1C1G,OAAA,CAACpB,SAAS;kBAAAkH,QAAA,EAAE4B;gBAAa;kBAAAnB,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAY,CAAC,eACtC1G,OAAA,CAACpB,SAAS;kBAAAkH,QAAA,EAAEiC;gBAAW;kBAAAxB,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAY,CAAC,eACpC1G,OAAA,CAACpB,SAAS;kBAAAkH,QAAA,EAAEmC;gBAAM;kBAAA1B,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAY,CAAC,eAC/B1G,OAAA,CAACpB,SAAS;kBAACyH,EAAE,EAAE;oBAAEL,KAAK,EAAEqC;kBAAW,CAAE;kBAAAvC,QAAA,EAAE+B;gBAAK;kBAAAtB,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAY,CAAC,eACzD1G,OAAA,CAACpB,SAAS;kBAAAkH,QAAA,EAAEqC;gBAAkB;kBAAA5B,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAY,CAAC;cAAA,GAZ9BK,IAAI,CAACC,OAAO;gBAAAT,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAajB,CAAC;YAEf,CAAC;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACO,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACP;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACM,CAAC;IAErB,CAAC,MAAM;MACL;MACA,oBACE1G,OAAA,CAAC3B,IAAI;QAACkK,SAAS;QAACC,OAAO,EAAE,CAAE;QAAA1C,QAAA,EACxB1B,IAAI,CAACvB,GAAG,CAAEkE,IAAI;UAAA,IAAA0B,qBAAA,EAAAC,sBAAA,EAAAC,sBAAA,EAAAC,sBAAA,EAAAC,sBAAA,EAAAC,sBAAA;UAAA,oBACb9I,OAAA,CAAC3B,IAAI;YAAC0K,IAAI;YAACC,EAAE,EAAE,EAAG;YAACC,EAAE,EAAE,CAAE;YAACC,EAAE,EAAE,CAAE;YAAApD,QAAA,eAC9B9F,OAAA,CAAC1B,IAAI;cAAAwH,QAAA,eACH9F,OAAA,CAACzB,WAAW;gBAAAuH,QAAA,gBACV9F,OAAA,CAAC9B,UAAU;kBAAC6H,OAAO,EAAC,IAAI;kBAACY,SAAS,EAAC,KAAK;kBAAAb,QAAA,EACrCiB,IAAI,CAACC;gBAAO;kBAAAT,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH,CAAC,eACb1G,OAAA,CAAC9B,UAAU;kBAAC6H,OAAO,EAAC,OAAO;kBAACC,KAAK,EAAC,gBAAgB;kBAAAF,QAAA,GAAC,WACxC,EAACiB,IAAI,CAACoC,OAAO,IAAI,KAAK;gBAAA;kBAAA5C,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACrB,CAAC,eACb1G,OAAA,CAAC9B,UAAU;kBAAC6H,OAAO,EAAC,OAAO;kBAACC,KAAK,EAAC,gBAAgB;kBAAAF,QAAA,GAAC,aACtC,EAACiB,IAAI,CAACI,SAAS,IAAI,KAAK;gBAAA;kBAAAZ,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACzB,CAAC,eACb1G,OAAA,CAAC9B,UAAU;kBAAC6H,OAAO,EAAC,OAAO;kBAACC,KAAK,EAAC,gBAAgB;kBAAAF,QAAA,GAAC,YACvC,EAACiB,IAAI,CAACS,mBAAmB,IAAI,KAAK,EAAC,KAAG,EAACT,IAAI,CAACqC,eAAe,IAAI,KAAK;gBAAA;kBAAA7C,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACpE,CAAC,eACb1G,OAAA,CAAC9B,UAAU;kBAAC6H,OAAO,EAAC,OAAO;kBAACC,KAAK,EAAC,gBAAgB;kBAAAF,QAAA,GAAC,UACzC,EAACiB,IAAI,CAACU,iBAAiB,IAAI,KAAK,EAAC,KAAG,EAACV,IAAI,CAACsC,aAAa,IAAI,KAAK;gBAAA;kBAAA9C,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAC9D,CAAC,eACb1G,OAAA,CAAC9B,UAAU;kBAAC6H,OAAO,EAAC,OAAO;kBAACC,KAAK,EAAC,gBAAgB;kBAAAF,QAAA,GAAC,iBAClC,EAACiB,IAAI,CAACW,aAAa,IAAI,KAAK;gBAAA;kBAAAnB,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACjC,CAAC,eACb1G,OAAA,CAAC9B,UAAU;kBAAC6H,OAAO,EAAC,OAAO;kBAACC,KAAK,EAAC,gBAAgB;kBAAAF,QAAA,GAAC,mBAChC,EAACiB,IAAI,CAACiB,eAAe,IAAI,GAAG;gBAAA;kBAAAzB,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACnC,CAAC,eACb1G,OAAA,CAAC9B,UAAU;kBAAC6H,OAAO,EAAC,OAAO;kBAACC,KAAK,EAAC,gBAAgB;kBAAAF,QAAA,GAAC,UACzC,EAACiB,IAAI,CAACmB,SAAS,IAAI,KAAK;gBAAA;kBAAA3B,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACtB,CAAC,eACb1G,OAAA,CAAC9B,UAAU;kBAAC6H,OAAO,EAAC,OAAO;kBAACC,KAAK,EAAC,gBAAgB;kBAACK,EAAE,EAAE;oBACrDL,KAAK,EAAE,CAAAyC,qBAAA,GAAA1B,IAAI,CAACe,mBAAmB,cAAAW,qBAAA,eAAxBA,qBAAA,CAA0BH,WAAW,CAAC,CAAC,CAAC5C,QAAQ,CAAC,QAAQ,CAAC,KAAAgD,sBAAA,GAC1D3B,IAAI,CAACe,mBAAmB,cAAAY,sBAAA,eAAxBA,sBAAA,CAA0BJ,WAAW,CAAC,CAAC,CAAC5C,QAAQ,CAAC,YAAY,CAAC,GAAG,OAAO,GACxE,CAAAiD,sBAAA,GAAA5B,IAAI,CAACe,mBAAmB,cAAAa,sBAAA,eAAxBA,sBAAA,CAA0BL,WAAW,CAAC,CAAC,CAAC5C,QAAQ,CAAC,UAAU,CAAC,KAAAkD,sBAAA,GAC5D7B,IAAI,CAACe,mBAAmB,cAAAc,sBAAA,eAAxBA,sBAAA,CAA0BN,WAAW,CAAC,CAAC,CAAC5C,QAAQ,CAAC,SAAS,CAAC,GAAG,QAAQ,GACtE,CAAAmD,sBAAA,GAAA9B,IAAI,CAACe,mBAAmB,cAAAe,sBAAA,eAAxBA,sBAAA,CAA0BP,WAAW,CAAC,CAAC,CAAC5C,QAAQ,CAAC,WAAW,CAAC,KAAAoD,sBAAA,GAC7D/B,IAAI,CAACe,mBAAmB,cAAAgB,sBAAA,eAAxBA,sBAAA,CAA0BR,WAAW,CAAC,CAAC,CAAC5C,QAAQ,CAAC,eAAe,CAAC,GAAG,KAAK,GAAG;kBACrF,CAAE;kBAAAI,QAAA,GAAC,SACM,EAACiB,IAAI,CAACe,mBAAmB,IAAI,KAAK;gBAAA;kBAAAvB,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAC/B,CAAC,eACb1G,OAAA,CAAC9B,UAAU;kBAAC6H,OAAO,EAAC,OAAO;kBAACC,KAAK,EAAC,gBAAgB;kBAAAF,QAAA,GAAC,gBACnC,EAACiB,IAAI,CAACqB,YAAY,KAAK,CAAC,GAAG,eAAe,GAC1CrB,IAAI,CAACqB,YAAY,KAAK,CAAC,GAAG,UAAU,GACpCrB,IAAI,CAACqB,YAAY,KAAK,CAAC,GAAG,QAAQ,GAClCrB,IAAI,CAACqB,YAAY,KAAK,CAAC,GAAG,UAAU,GAAG,KAAK;gBAAA;kBAAA7B,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAChD,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACF;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACV;UAAC,GA5C6BK,IAAI,CAACC,OAAO;YAAAT,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OA6C5C,CAAC;QAAA,CACR;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACE,CAAC;IAEX;EACF,CAAC;;EAED;EACA,MAAM4C,oBAAoB,GAAIC,IAAI,IAAK;IACrCpI,WAAW,CAACoI,IAAI,CAAC;EACnB,CAAC;;EAED;EACA,MAAMC,gBAAgB,GAAGA,CAAA,KAAM;IAC7B,IAAI,CAACpI,KAAK,EAAE,OAAO,IAAI;IAEvB,oBACEpB,OAAA,CAAC7B,KAAK;MAACkI,EAAE,EAAE;QAAEoD,CAAC,EAAE,CAAC;QAAEC,EAAE,EAAE,CAAC;QAAEC,OAAO,EAAE;MAAU,CAAE;MAAA7D,QAAA,gBAC7C9F,OAAA,CAAC9B,UAAU;QAAC6H,OAAO,EAAC,IAAI;QAAC6D,YAAY;QAAA9D,QAAA,EAAC;MAEtC;QAAAS,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAY,CAAC,eACb1G,OAAA,CAAC3B,IAAI;QAACkK,SAAS;QAACC,OAAO,EAAE,CAAE;QAAA1C,QAAA,gBACzB9F,OAAA,CAAC3B,IAAI;UAAC0K,IAAI;UAACC,EAAE,EAAE,EAAG;UAACE,EAAE,EAAE,CAAE;UAAApD,QAAA,eACvB9F,OAAA,CAAC/B,GAAG;YAAA6H,QAAA,gBACF9F,OAAA,CAAC9B,UAAU;cAAC6H,OAAO,EAAC,WAAW;cAAAD,QAAA,EAAC;YAAqB;cAAAS,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAY,CAAC,eAClE1G,OAAA,CAAC9B,UAAU;cAAC6H,OAAO,EAAC,OAAO;cAAAD,QAAA,GAAC,eAAa,EAAC1E,KAAK,CAACyI,WAAW;YAAA;cAAAtD,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAa,CAAC,eACzE1G,OAAA,CAAC9B,UAAU;cAAC6H,OAAO,EAAC,OAAO;cAAAD,QAAA,GAAC,wBAAsB,EAAC1E,KAAK,CAAC0I,oBAAoB,CAAClC,OAAO,CAAC,CAAC,CAAC,EAAC,IAAE;YAAA;cAAArB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAY,CAAC,eACxG1G,OAAA,CAAC9B,UAAU;cAAC6H,OAAO,EAAC,OAAO;cAAAD,QAAA,GAAC,uBAAqB,EAAC1E,KAAK,CAAC2I,mBAAmB,CAACnC,OAAO,CAAC,CAAC,CAAC,EAAC,MAAI,EAACxG,KAAK,CAAC4I,yBAAyB,CAACpC,OAAO,CAAC,CAAC,CAAC,EAAC,eAAa;YAAA;cAAArB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAY,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC9J;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACF,CAAC,eACP1G,OAAA,CAAC3B,IAAI;UAAC0K,IAAI;UAACC,EAAE,EAAE,EAAG;UAACE,EAAE,EAAE,CAAE;UAAApD,QAAA,eACvB9F,OAAA,CAAC/B,GAAG;YAAA6H,QAAA,gBACF9F,OAAA,CAAC9B,UAAU;cAAC6H,OAAO,EAAC,WAAW;cAAAD,QAAA,EAAC;YAAuB;cAAAS,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAY,CAAC,EACnEuD,MAAM,CAACC,OAAO,CAAC9I,KAAK,CAAC+I,KAAK,CAAC,CAACtH,GAAG,CAAC,CAAC,CAACgF,KAAK,EAAEuC,SAAS,CAAC,kBAClDpK,OAAA,CAAC9B,UAAU;cAAa6H,OAAO,EAAC,OAAO;cAAAD,QAAA,GAAE+B,KAAK,EAAC,IAAE,EAACuC,SAAS,EAAC,OAAK;YAAA,GAAhDvC,KAAK;cAAAtB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAuD,CAC9E,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACC;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACF,CAAC,eACP1G,OAAA,CAAC3B,IAAI;UAAC0K,IAAI;UAACC,EAAE,EAAE,EAAG;UAAAlD,QAAA,eAChB9F,OAAA,CAAC/B,GAAG;YAAA6H,QAAA,gBACF9F,OAAA,CAAC9B,UAAU;cAAC6H,OAAO,EAAC,WAAW;cAAAD,QAAA,EAAC;YAAY;cAAAS,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAY,CAAC,eACzD1G,OAAA,CAAC9B,UAAU;cAAC6H,OAAO,EAAC,OAAO;cAAAD,QAAA,GAAC,iBAAe,EAAC1E,KAAK,CAACgH,YAAY,CAACiC,aAAa;YAAA;cAAA9D,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAa,CAAC,eAC1F1G,OAAA,CAAC9B,UAAU;cAAC6H,OAAO,EAAC,OAAO;cAAAD,QAAA,GAAC,2BAAyB,EAAC1E,KAAK,CAACgH,YAAY,CAACkC,kBAAkB;YAAA;cAAA/D,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAa,CAAC,eACzG1G,OAAA,CAAC9B,UAAU;cAAC6H,OAAO,EAAC,OAAO;cAAAD,QAAA,GAAC,yBAAuB,EAAC1E,KAAK,CAACgH,YAAY,CAACmC,gBAAgB;YAAA;cAAAhE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAa,CAAC,eACrG1G,OAAA,CAAC9B,UAAU;cAAC6H,OAAO,EAAC,OAAO;cAAAD,QAAA,GAAC,2BAAyB,EAAC1E,KAAK,CAACgH,YAAY,CAACoC,uBAAuB;YAAA;cAAAjE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAa,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC3G;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACF,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACF,CAAC;EAEZ,CAAC;EAED,oBACE1G,OAAA,CAAC/B,GAAG;IAACwM,SAAS,EAAC,WAAW;IAAA3E,QAAA,gBACxB9F,OAAA,CAAC7B,KAAK;MAACkI,EAAE,EAAE;QAAEqD,EAAE,EAAE,CAAC;QAAED,CAAC,EAAE;MAAE,CAAE;MAAA3D,QAAA,eACzB9F,OAAA,CAAC/B,GAAG;QAACoI,EAAE,EAAE;UAAEqE,OAAO,EAAE,MAAM;UAAEC,cAAc,EAAE,UAAU;UAAEC,UAAU,EAAE,QAAQ;UAAEC,GAAG,EAAE;QAAE,CAAE;QAAA/E,QAAA,gBACrF9F,OAAA,CAACvB,UAAU;UACTwH,OAAO,EAAEA,CAAA,KAAMC,MAAM,CAACC,QAAQ,CAACC,MAAM,CAAC,CAAE;UACxC0E,KAAK,EAAC,oBAAoB;UAAAhF,QAAA,eAE1B9F,OAAA,CAACX,WAAW;YAAAkH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACL,CAAC,eACb1G,OAAA,CAAC/B,GAAG;UAACoI,EAAE,EAAE;YAAEqE,OAAO,EAAE,MAAM;YAAEK,MAAM,EAAE,gBAAgB;YAAEC,YAAY,EAAE;UAAE,CAAE;UAAAlF,QAAA,gBACtE9F,OAAA,CAACvB,UAAU;YACT4H,EAAE,EAAE;cAAEL,KAAK,EAAE9E,QAAQ,KAAK,OAAO,GAAG,SAAS,GAAG;YAAU,CAAE;YAC5D+E,OAAO,EAAEA,CAAA,KAAMqD,oBAAoB,CAAC,OAAO,CAAE;YAC7CwB,KAAK,EAAC,iBAAiB;YAAAhF,QAAA,eAEvB9F,OAAA,CAACP,YAAY;cAAA8G,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACN,CAAC,eACb1G,OAAA,CAACvB,UAAU;YACT4H,EAAE,EAAE;cAAEL,KAAK,EAAE9E,QAAQ,KAAK,MAAM,GAAG,SAAS,GAAG;YAAU,CAAE;YAC3D+E,OAAO,EAAEA,CAAA,KAAMqD,oBAAoB,CAAC,MAAM,CAAE;YAC5CwB,KAAK,EAAC,gBAAgB;YAAAhF,QAAA,eAEtB9F,OAAA,CAACL,cAAc;cAAA4G,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACR,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACV,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACD,CAAC,EAEP5F,OAAO,gBACNd,OAAA,CAAC/B,GAAG;MAACoI,EAAE,EAAE;QAAEqE,OAAO,EAAE,MAAM;QAAEO,aAAa,EAAE,QAAQ;QAAEL,UAAU,EAAE,QAAQ;QAAEhE,EAAE,EAAE;MAAE,CAAE;MAAAd,QAAA,gBACjF9F,OAAA,CAAC9B,UAAU;QAAA4H,QAAA,EAAC;MAAmB;QAAAS,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAY,CAAC,eAC5C1G,OAAA,CAAC5B,MAAM;QACL2H,OAAO,EAAC,UAAU;QAClBC,KAAK,EAAC,SAAS;QACfC,OAAO,EAAEA,CAAA,KAAMC,MAAM,CAACC,QAAQ,CAACC,MAAM,CAAC,CAAE;QACxCC,EAAE,EAAE;UAAEO,EAAE,EAAE;QAAE,CAAE;QAAAd,QAAA,EACf;MAED;QAAAS,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAQ,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACN,CAAC,GACJ1F,KAAK,gBACPhB,OAAA,CAAC/B,GAAG;MAAA6H,QAAA,gBACF9F,OAAA,CAACxB,KAAK;QAACqH,QAAQ,EAAC,OAAO;QAACQ,EAAE,EAAE;UAAEqD,EAAE,EAAE;QAAE,CAAE;QAAA5D,QAAA,GACnC9E,KAAK,EACLA,KAAK,CAAC0E,QAAQ,CAAC,eAAe,CAAC,iBAC9B1F,OAAA,CAAC9B,UAAU;UAAC6H,OAAO,EAAC,OAAO;UAACM,EAAE,EAAE;YAAEO,EAAE,EAAE;UAAE,CAAE;UAAAd,QAAA,gBACxC9F,OAAA;YAAA8F,QAAA,EAAQ;UAAa;YAAAS,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC,uEAC9B,eAAA1G,OAAA;YAAAuG,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAK,CAAC,8CACoC,eAAA1G,OAAA;YAAA8F,QAAA,EAAM;UAAa;YAAAS,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC,4CACtE;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAY,CACb;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACI,CAAC,eACR1G,OAAA,CAAC/B,GAAG;QAACoI,EAAE,EAAE;UAAEqE,OAAO,EAAE,MAAM;UAAEG,GAAG,EAAE;QAAE,CAAE;QAAA/E,QAAA,eACnC9F,OAAA,CAAC5B,MAAM;UACL2H,OAAO,EAAC,WAAW;UACnB0E,SAAS,EAAC,gBAAgB;UAC1BxE,OAAO,EAAEA,CAAA,KAAMC,MAAM,CAACC,QAAQ,CAACC,MAAM,CAAC,CAAE;UAAAN,QAAA,EACzC;QAED;UAAAS,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACN,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,gBAEN1G,OAAA,CAAC/B,GAAG;MAAA6H,QAAA,GAED1E,KAAK,IAAIoI,gBAAgB,CAAC,CAAC,eAE5BxJ,OAAA,CAAC/B,GAAG;QAACoI,EAAE,EAAE;UAAEqD,EAAE,EAAE;QAAE,CAAE;QAAA5D,QAAA,gBACjB9F,OAAA,CAAC9B,UAAU;UAAC6H,OAAO,EAAC,IAAI;UAAC6D,YAAY;UAAA9D,QAAA,EAAC;QAEtC;UAAAS,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAY,CAAC,EACZd,eAAe,CAAClF,UAAU,CAAC;MAAA;QAAA6F,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACzB,CAAC,eAEN1G,OAAA,CAAC/B,GAAG;QAACoI,EAAE,EAAE;UAAEO,EAAE,EAAE;QAAE,CAAE;QAAAd,QAAA,gBACjB9F,OAAA,CAAC9B,UAAU;UAAC6H,OAAO,EAAC,IAAI;UAAC6D,YAAY;UAAA9D,QAAA,EAAC;QAEtC;UAAAS,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAY,CAAC,EACZd,eAAe,CAAChF,SAAS,CAAC;MAAA;QAAA2F,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACxB,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CACN;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACE,CAAC;AAEV,CAAC;AAACxG,EAAA,CAjkBID,kBAAkB;EAAA,QACYJ,OAAO,EACxBD,WAAW;AAAA;AAAAsL,EAAA,GAFxBjL,kBAAkB;AAmkBxB,eAAeA,kBAAkB;AAAC,IAAAiL,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}