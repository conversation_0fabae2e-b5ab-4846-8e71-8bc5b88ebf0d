{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\PycharmProjects\\\\CMS\\\\webapp\\\\frontend\\\\src\\\\pages\\\\cavi\\\\VisualizzaCaviPage.js\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect } from 'react';\nimport { Box, Typography, Paper, Button, Grid, Card, CardContent, Alert, IconButton, Chip, CircularProgress, LinearProgress, Dialog, DialogTitle, DialogContent, DialogActions, Snackbar } from '@mui/material';\nimport InfoIcon from '@mui/icons-material/Info';\nimport RefreshIcon from '@mui/icons-material/Refresh';\nimport { useNavigate } from 'react-router-dom';\nimport { useAuth } from '../../context/AuthContext';\nimport { useGlobalContext } from '../../context/GlobalContext';\nimport PosaCaviCollegamenti from '../../components/cavi/PosaCaviCollegamenti';\nimport caviService from '../../services/caviService';\nimport CavoForm from '../../components/cavi/CavoForm';\nimport { normalizeInstallationStatus } from '../../utils/validationUtils';\nimport CaviFilterableTable from '../../components/cavi/CaviFilterableTable';\nimport './CaviPage.css';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst VisualizzaCaviPage = () => {\n  _s();\n  var _caviAttivi$, _caviAttivi$2, _caviAttivi$3;\n  const {\n    isImpersonating,\n    user\n  } = useAuth();\n  const {\n    openEliminaCavoDialog,\n    setOpenEliminaCavoDialog,\n    openModificaCavoDialog,\n    setOpenModificaCavoDialog,\n    openAggiungiCavoDialog,\n    setOpenAggiungiCavoDialog\n  } = useGlobalContext();\n  const navigate = useNavigate();\n  const [cantiereId, setCantiereId] = useState(null);\n  const [cantiereName, setCantiereName] = useState('');\n  const [caviAttivi, setCaviAttivi] = useState([]);\n  const [caviSpare, setCaviSpare] = useState([]);\n  const [loading, setLoading] = useState(true);\n  const [error, setError] = useState(null);\n  // Stato per le notifiche\n  const [notification, setNotification] = useState({\n    open: false,\n    message: '',\n    severity: 'success'\n  });\n  // Rimosso stato viewMode\n\n  // Stato per il dialogo dei dettagli del cavo\n  const [selectedCavo, setSelectedCavo] = useState(null);\n  const [detailsDialogOpen, setDetailsDialogOpen] = useState(false);\n\n  // Stato per le statistiche\n  const [stats, setStats] = useState({\n    totali: {\n      cavi_attivi: 0,\n      cavi_spare: 0,\n      cavi_totali: 0\n    },\n    metrature: {\n      metri_teorici_totali: 0,\n      metri_reali_totali: 0,\n      percentuale_completamento: 0\n    },\n    stati: []\n  });\n  const [loadingStats, setLoadingStats] = useState(false);\n\n  // Rimosso stato per il debug\n\n  // Funzione per caricare gli stati di installazione disponibili\n  const loadStatiInstallazione = () => {\n    // Usa i valori dell'enum StatoInstallazione\n    setStatiInstallazione(['Installato', 'Da installare', 'In corso']);\n  };\n\n  // Stato per filtri e ordinamento\n  const [filters, setFilters] = useState({\n    stato_installazione: '',\n    tipologia: '',\n    sort_by: '',\n    sort_order: 'asc'\n  });\n\n  // Opzioni per i filtri\n  const [statiInstallazione, setStatiInstallazione] = useState([]);\n  const [tipologieCavi, setTipologieCavi] = useState([]);\n\n  // Rimossa funzione di debug\n\n  // Funzione per caricare i cavi\n  // Il parametro silentLoading permette di evitare di mostrare lo stato di caricamento\n  const fetchCavi = async (silentLoading = false) => {\n    try {\n      if (!silentLoading) {\n        setLoading(true);\n      }\n      console.log('Caricamento cavi per cantiere:', cantiereId);\n\n      // Verifica che cantiereId sia valido\n      if (!cantiereId) {\n        console.error('fetchCavi: cantiereId non valido:', cantiereId);\n        setError('ID cantiere non valido o mancante. Ricarica la pagina.');\n        setLoading(false);\n        return;\n      }\n\n      // Recupera il cantiereId dal localStorage come fallback\n      let cantiereIdToUse = cantiereId;\n      if (!cantiereIdToUse) {\n        cantiereIdToUse = localStorage.getItem('selectedCantiereId');\n        console.log('Usando cantiereId dal localStorage:', cantiereIdToUse);\n        if (!cantiereIdToUse) {\n          console.error('Impossibile trovare un ID cantiere valido');\n          setError('ID cantiere non trovato. Ricarica la pagina.');\n          setLoading(false);\n          return;\n        }\n      }\n\n      // Carica i cavi attivi\n      console.log('Caricamento cavi attivi (tipo_cavo=0)...');\n      let attivi = [];\n      try {\n        attivi = await caviService.getCavi(cantiereIdToUse, 0, filters);\n        console.log('Cavi attivi caricati:', attivi ? attivi.length : 0);\n      } catch (attiviError) {\n        console.error('Errore nel caricamento dei cavi attivi:', attiviError);\n        // Continua con un array vuoto\n        attivi = [];\n      }\n\n      // Verifica se ci sono cavi con modificato_manualmente = 3 tra i cavi attivi\n      if (attivi && attivi.length > 0) {\n        const caviSpareTraAttivi = attivi.filter(cavo => cavo.modificato_manualmente === 3);\n        if (caviSpareTraAttivi.length > 0) {\n          console.error('ERRORE: Trovati cavi con modificato_manualmente = 3 tra i cavi attivi:', caviSpareTraAttivi);\n        }\n      }\n      setCaviAttivi(attivi || []);\n\n      // Carica i cavi SPARE con la nuova funzione dedicata\n      let spare = [];\n      try {\n        console.log('Caricamento cavi SPARE con funzione dedicata...');\n        spare = await caviService.getCaviSpare(cantiereIdToUse);\n        console.log('Cavi SPARE caricati con funzione dedicata:', spare ? spare.length : 0);\n        if (spare && spare.length > 0) {\n          console.log('Primo cavo SPARE:', spare[0]);\n        }\n      } catch (spareError) {\n        console.error('Errore nel caricamento dei cavi SPARE con funzione dedicata:', spareError);\n        // Se fallisce, prova con il metodo standard\n        try {\n          console.log('Tentativo con metodo standard...');\n          spare = await caviService.getCavi(cantiereIdToUse, 3);\n          console.log('Cavi SPARE caricati con metodo standard:', spare ? spare.length : 0);\n        } catch (standardError) {\n          console.error('Errore anche con metodo standard:', standardError);\n          // Continua con un array vuoto\n          spare = [];\n        }\n      }\n      setCaviSpare(spare || []);\n\n      // Carica le statistiche\n      try {\n        console.log('Caricamento statistiche...');\n        const statsData = await caviService.getCaviStats(cantiereIdToUse);\n        console.log('Statistiche caricate:', statsData);\n\n        // Verifica che statsData abbia la struttura attesa\n        if (!statsData || typeof statsData !== 'object') {\n          console.error('Statistiche non valide:', statsData);\n          // Imposta un oggetto stats con struttura valida ma vuota\n          setStats({\n            totali: {\n              cavi_attivi: 0,\n              cavi_spare: 0,\n              cavi_totali: 0\n            },\n            metrature: {\n              metri_teorici_totali: 0,\n              metri_reali_totali: 0,\n              percentuale_completamento: 0\n            },\n            stati: []\n          });\n        } else {\n          // Assicurati che tutte le proprietà necessarie siano presenti\n          const validStats = {\n            totali: statsData.totali || {\n              cavi_attivi: 0,\n              cavi_spare: 0,\n              cavi_totali: 0\n            },\n            metrature: statsData.metrature || {\n              metri_teorici_totali: 0,\n              metri_reali_totali: 0,\n              percentuale_completamento: 0\n            },\n            stati: statsData.stati || []\n          };\n          setStats(validStats);\n        }\n      } catch (statsError) {\n        console.error('Errore nel caricamento delle statistiche:', statsError);\n        // Continua con statistiche vuote ma con struttura valida\n        setStats({\n          totali: {\n            cavi_attivi: 0,\n            cavi_spare: 0,\n            cavi_totali: 0\n          },\n          metrature: {\n            metri_teorici_totali: 0,\n            metri_reali_totali: 0,\n            percentuale_completamento: 0\n          },\n          stati: []\n        });\n      }\n\n      // Se siamo arrivati qui, rimuovi eventuali messaggi di errore precedenti\n      setError('');\n    } catch (error) {\n      console.error('Errore generale nel caricamento dei cavi:', error);\n      setError(`Errore nel caricamento dei cavi: ${error.message || 'Errore sconosciuto'}`);\n\n      // Prova a ricaricare la pagina dopo un ritardo se l'errore persiste\n      setTimeout(() => {\n        // Verifica se siamo ancora in errore\n        if (document.body.textContent.includes('Errore nel caricamento dei cavi')) {\n          console.log('Errore persistente, tentativo di ricaricamento della pagina...');\n          window.location.reload();\n        }\n      }, 5000); // 5 secondi di ritardo\n    } finally {\n      if (!silentLoading) {\n        setLoading(false);\n      }\n    }\n  };\n\n  // Carica i dati del cantiere e dei cavi\n  useEffect(() => {\n    // Carica gli stati di installazione all'avvio\n    loadStatiInstallazione();\n    const fetchData = async () => {\n      try {\n        console.log('Inizializzazione VisualizzaCaviPage...');\n\n        // Verifica che l'utente sia autenticato\n        const token = localStorage.getItem('token');\n        console.log('Token presente:', !!token);\n        if (!token) {\n          setError('Sessione scaduta. Effettua nuovamente il login.');\n          setLoading(false);\n          return;\n        }\n\n        // Recupera l'ID del cantiere selezionato dal localStorage\n        let selectedCantiereId = localStorage.getItem('selectedCantiereId');\n        let selectedCantiereName = localStorage.getItem('selectedCantiereName');\n        console.log('Cantiere selezionato dal localStorage:', {\n          selectedCantiereId,\n          selectedCantiereName\n        });\n        console.log('Dati utente:', user);\n\n        // Stampa tutti i dati nel localStorage per debug\n        console.log('DEBUG - Tutti i dati nel localStorage:');\n        for (let i = 0; i < localStorage.length; i++) {\n          const key = localStorage.key(i);\n          console.log(`${key}: ${localStorage.getItem(key)}`);\n        }\n\n        // SOLUZIONE DIRETTA: Ottieni l'ID del cantiere direttamente dal token JWT\n        if ((user === null || user === void 0 ? void 0 : user.role) === 'cantieri_user') {\n          console.log('Utente cantiere rilevato, tentativo di recupero ID cantiere dai dati utente');\n\n          // Verifica se l'utente ha un ID cantiere nei dati utente\n          if (user.cantiere_id) {\n            console.log('Trovato ID cantiere nei dati utente:', user.cantiere_id);\n            selectedCantiereId = user.cantiere_id.toString();\n            selectedCantiereName = user.cantiere_name || `Cantiere ${user.cantiere_id}`;\n\n            // Salva l'ID e il nome del cantiere nel localStorage\n            localStorage.setItem('selectedCantiereId', selectedCantiereId);\n            localStorage.setItem('selectedCantiereName', selectedCantiereName);\n            console.log('Salvato ID cantiere nel localStorage:', selectedCantiereId);\n          } else {\n            // Tentativo di recupero dal token JWT\n            try {\n              console.log('Tentativo di decodifica del token JWT per recuperare l\\'ID cantiere');\n              const token = localStorage.getItem('token');\n              if (token) {\n                // Decodifica il token JWT (senza verifica della firma)\n                const base64Url = token.split('.')[1];\n                const base64 = base64Url.replace(/-/g, '+').replace(/_/g, '/');\n                const jsonPayload = decodeURIComponent(atob(base64).split('').map(c => {\n                  return '%' + ('00' + c.charCodeAt(0).toString(16)).slice(-2);\n                }).join(''));\n                const payload = JSON.parse(jsonPayload);\n                console.log('Payload del token JWT:', payload);\n                if (payload.cantiere_id) {\n                  console.log('Trovato ID cantiere nel token JWT:', payload.cantiere_id);\n                  selectedCantiereId = payload.cantiere_id.toString();\n                  // Usa un nome generico se non disponibile\n                  selectedCantiereName = `Cantiere ${payload.cantiere_id}`;\n\n                  // Salva l'ID e il nome del cantiere nel localStorage\n                  localStorage.setItem('selectedCantiereId', selectedCantiereId);\n                  localStorage.setItem('selectedCantiereName', selectedCantiereName);\n                  console.log('Salvato ID cantiere nel localStorage:', selectedCantiereId);\n                }\n              }\n            } catch (e) {\n              console.error('Errore durante la decodifica del token JWT:', e);\n            }\n          }\n        }\n\n        // SOLUZIONE TEMPORANEA: Se ancora non abbiamo un ID cantiere, usa un valore hardcoded per debug\n        if (!selectedCantiereId || selectedCantiereId === 'undefined' || selectedCantiereId === 'null') {\n          console.warn('ATTENZIONE: Nessun ID cantiere trovato, utilizzo valore hardcoded per debug');\n          // Usa il primo cantiere disponibile (questo è solo per debug)\n          selectedCantiereId = '1'; // Sostituisci con un ID cantiere valido nel tuo database\n          selectedCantiereName = 'Cantiere Debug';\n\n          // Salva l'ID e il nome del cantiere nel localStorage\n          localStorage.setItem('selectedCantiereId', selectedCantiereId);\n          localStorage.setItem('selectedCantiereName', selectedCantiereName);\n          console.log('Salvato ID cantiere hardcoded nel localStorage:', selectedCantiereId);\n        }\n\n        // Verifica finale\n        if (!selectedCantiereId) {\n          setError('Nessun cantiere selezionato. Torna alla pagina dei cantieri.');\n          setLoading(false);\n          return;\n        }\n\n        // Verifica che l'ID del cantiere sia un numero valido\n        const cantiereIdNum = parseInt(selectedCantiereId, 10);\n        console.log('ID cantiere convertito a numero:', cantiereIdNum);\n        if (isNaN(cantiereIdNum)) {\n          setError(`ID cantiere non valido: ${selectedCantiereId}. Torna alla pagina dei cantieri.`);\n          setLoading(false);\n          return;\n        }\n\n        // Usa il numero convertito, non la stringa\n        setCantiereId(cantiereIdNum);\n        setCantiereName(selectedCantiereName || `Cantiere ${cantiereIdNum}`);\n\n        // Carica le statistiche dei cavi\n        try {\n          setLoadingStats(true);\n          console.log('Caricamento statistiche cavi per cantiere:', cantiereIdNum);\n          const statsData = await caviService.getCaviStats(cantiereIdNum);\n          console.log('Statistiche cavi caricate:', statsData);\n          setStats(statsData);\n\n          // Estrai gli stati di installazione e le tipologie per i filtri\n          if (statsData && statsData.stati) {\n            const stati = statsData.stati.map(item => item.stato).filter(stato => stato !== 'Non specificato');\n            setStatiInstallazione(stati);\n          }\n          if (statsData && statsData.tipologie) {\n            const tipologie = statsData.tipologie.map(item => item.tipologia).filter(tipo => tipo !== 'Non specificata');\n            setTipologieCavi(tipologie);\n          }\n          setLoadingStats(false);\n        } catch (statsError) {\n          console.error('Errore nel caricamento delle statistiche:', statsError);\n          setLoadingStats(false);\n          // Non interrompere il flusso se le statistiche falliscono\n        }\n\n        // Carica i cavi attivi con gestione degli errori migliorata\n        console.log('Caricamento cavi attivi per cantiere:', cantiereIdNum);\n        try {\n          // Imposta un timeout per evitare che la richiesta rimanga bloccata\n          const timeoutPromise = new Promise((_, reject) => {\n            setTimeout(() => reject(new Error('Timeout durante il caricamento dei cavi attivi')), 30000); // Aumentato a 30 secondi\n          });\n\n          // Esegui la richiesta con un timeout di sicurezza e applica i filtri\n          console.log('Iniziando chiamata API per cavi attivi con filtri:', filters);\n          const caviPromise = caviService.getCavi(cantiereIdNum, 0, filters);\n          const attivi = await Promise.race([caviPromise, timeoutPromise]);\n          console.log('Cavi attivi caricati:', attivi);\n          console.log('Numero di cavi attivi trovati:', attivi ? attivi.length : 0);\n          if (attivi && attivi.length > 0) {\n            console.log('Primo cavo attivo:', attivi[0]);\n          } else {\n            console.warn('Nessun cavo attivo trovato per il cantiere', cantiereIdNum);\n          }\n          setCaviAttivi(attivi || []);\n        } catch (caviError) {\n          console.error('Errore nel caricamento dei cavi attivi:', caviError);\n          console.error('Dettagli errore cavi attivi:', {\n            message: caviError.message,\n            status: caviError.status,\n            data: caviError.data,\n            stack: caviError.stack,\n            code: caviError.code,\n            name: caviError.name,\n            response: caviError.response ? {\n              status: caviError.response.status,\n              statusText: caviError.response.statusText,\n              data: caviError.response.data\n            } : 'No response'\n          });\n\n          // Non interrompere il flusso, continua con i cavi spare\n          setCaviAttivi([]);\n          console.warn('Continuazione del flusso dopo errore nei cavi attivi');\n\n          // Aggiungi un messaggio di errore visibile all'utente\n          setError(`Errore nel caricamento dei cavi attivi: ${caviError.message}. Controlla la console per maggiori dettagli.`);\n        }\n\n        // Carica i cavi spare con gestione degli errori migliorata\n        console.log('Caricamento cavi spare per cantiere:', cantiereIdNum);\n        try {\n          // Imposta un timeout per evitare che la richiesta rimanga bloccata\n          const timeoutPromise = new Promise((_, reject) => {\n            setTimeout(() => reject(new Error('Timeout durante il caricamento dei cavi spare')), 30000); // Aumentato a 30 secondi\n          });\n\n          // Esegui la richiesta con un timeout di sicurezza\n          console.log('Iniziando chiamata API per cavi spare...');\n          // Non applichiamo i filtri ai cavi spare, solo agli attivi\n          const sparePromise = caviService.getCavi(cantiereIdNum, 3);\n          const spare = await Promise.race([sparePromise, timeoutPromise]);\n          console.log('Cavi spare caricati:', spare);\n          console.log('Numero di cavi spare trovati:', spare ? spare.length : 0);\n          if (spare && spare.length > 0) {\n            console.log('Primo cavo spare:', spare[0]);\n          } else {\n            console.warn('Nessun cavo spare trovato per il cantiere', cantiereIdNum);\n          }\n          setCaviSpare(spare || []);\n        } catch (spareError) {\n          console.error('Errore nel caricamento dei cavi spare:', spareError);\n          console.error('Dettagli errore cavi spare:', {\n            message: spareError.message,\n            status: spareError.status,\n            data: spareError.data,\n            stack: spareError.stack,\n            code: spareError.code,\n            name: spareError.name,\n            response: spareError.response ? {\n              status: spareError.response.status,\n              statusText: spareError.response.statusText,\n              data: spareError.response.data\n            } : 'No response'\n          });\n\n          // Non interrompere il flusso, imposta un array vuoto\n          setCaviSpare([]);\n\n          // Aggiungi un messaggio di errore visibile all'utente se non c'è già un errore per i cavi attivi\n          if (!error) {\n            setError(`Errore nel caricamento dei cavi spare: ${spareError.message}. Controlla la console per maggiori dettagli.`);\n          }\n        }\n\n        // Se siamo arrivati qui, almeno abbiamo caricato l'interfaccia di base\n        setLoading(false);\n      } catch (err) {\n        var _err$response, _err$response2, _err$response3, _err$response4, _err$response5, _err$response5$data;\n        console.error('Errore nel caricamento dei cavi:', err);\n        console.error('Dettagli errore generale:', {\n          message: err.message,\n          status: err.status || ((_err$response = err.response) === null || _err$response === void 0 ? void 0 : _err$response.status),\n          data: err.data || ((_err$response2 = err.response) === null || _err$response2 === void 0 ? void 0 : _err$response2.data),\n          stack: err.stack\n        });\n\n        // Estrai il messaggio di errore dettagliato\n        let errorMessage = 'Errore sconosciuto';\n        if (err.message && err.message.includes('ID cantiere non valido')) {\n          errorMessage = err.message;\n        } else if (err.status === 401 || err.status === 403 || ((_err$response3 = err.response) === null || _err$response3 === void 0 ? void 0 : _err$response3.status) === 401 || ((_err$response4 = err.response) === null || _err$response4 === void 0 ? void 0 : _err$response4.status) === 403) {\n          errorMessage = 'Sessione scaduta o non autorizzata. Effettua nuovamente il login.';\n        } else if ((_err$response5 = err.response) !== null && _err$response5 !== void 0 && (_err$response5$data = _err$response5.data) !== null && _err$response5$data !== void 0 && _err$response5$data.detail) {\n          // Estrai il messaggio di errore dettagliato dall'API\n          errorMessage = `Errore API: ${err.response.data.detail}`;\n        } else if (err.code === 'ERR_NETWORK') {\n          // Errore di rete\n          errorMessage = 'Network Error. Verifica che il backend sia in esecuzione e accessibile.';\n        } else if (err.message) {\n          errorMessage = err.message;\n        }\n        setError(`Impossibile caricare i cavi: ${errorMessage}. Riprova più tardi.`);\n\n        // Imposta array vuoti per evitare errori di rendering\n        setCaviAttivi([]);\n        setCaviSpare([]);\n      } finally {\n        setLoading(false);\n      }\n    };\n    fetchData();\n  }, [filters]); // Ricarica i dati quando cambiano i filtri\n\n  // I filtri sono ora gestiti dal componente CaviFilterableTable\n\n  // Funzione per aprire il dialogo dei dettagli del cavo\n  const handleOpenDetails = cavo => {\n    setSelectedCavo(cavo);\n    setDetailsDialogOpen(true);\n  };\n\n  // Funzione per chiudere il dialogo dei dettagli del cavo\n  const handleCloseDetails = () => {\n    setDetailsDialogOpen(false);\n    setSelectedCavo(null);\n  };\n\n  // Funzione per chiudere la notifica\n  const handleCloseNotification = () => {\n    setNotification(prev => ({\n      ...prev,\n      open: false\n    }));\n  };\n\n  // Funzione per mostrare una notifica\n  const showNotification = (message, severity = 'success') => {\n    setNotification({\n      open: true,\n      message,\n      severity\n    });\n  };\n\n  // Nessuna funzione di navigazione necessaria, tutto è gestito dal menu principale\n\n  // La visualizzazione dei cavi è ora gestita dal componente CaviFilterableTable\n\n  // Rimossa funzione handleViewModeChange\n\n  // Renderizza il dialogo dei dettagli del cavo\n  const renderDetailsDialog = () => {\n    if (!selectedCavo) return null;\n    return /*#__PURE__*/_jsxDEV(Dialog, {\n      open: detailsDialogOpen,\n      onClose: handleCloseDetails,\n      maxWidth: \"md\",\n      fullWidth: true,\n      children: [/*#__PURE__*/_jsxDEV(DialogTitle, {\n        children: [\"Dettagli Cavo: \", selectedCavo.id_cavo]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 522,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(DialogContent, {\n        dividers: true,\n        children: /*#__PURE__*/_jsxDEV(Grid, {\n          container: true,\n          spacing: 2,\n          children: [/*#__PURE__*/_jsxDEV(Grid, {\n            item: true,\n            xs: 12,\n            md: 6,\n            children: [/*#__PURE__*/_jsxDEV(Typography, {\n              variant: \"subtitle1\",\n              gutterBottom: true,\n              children: \"Informazioni Generali\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 528,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Box, {\n              sx: {\n                mb: 2\n              },\n              children: [/*#__PURE__*/_jsxDEV(Typography, {\n                variant: \"body2\",\n                children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n                  children: \"Sistema:\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 530,\n                  columnNumber: 45\n                }, this), \" \", selectedCavo.sistema || 'N/A']\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 530,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                variant: \"body2\",\n                children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n                  children: \"Utility:\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 531,\n                  columnNumber: 45\n                }, this), \" \", selectedCavo.utility || 'N/A']\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 531,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                variant: \"body2\",\n                children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n                  children: \"Tipologia:\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 532,\n                  columnNumber: 45\n                }, this), \" \", selectedCavo.tipologia || 'N/A']\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 532,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                variant: \"body2\",\n                children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n                  children: \"Colore:\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 533,\n                  columnNumber: 45\n                }, this), \" \", selectedCavo.colore_cavo || 'N/A']\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 533,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                variant: \"body2\",\n                children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n                  children: \"N. Conduttori:\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 534,\n                  columnNumber: 45\n                }, this), \" \", selectedCavo.n_conduttori || 'N/A']\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 534,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                variant: \"body2\",\n                children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n                  children: \"Sezione:\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 535,\n                  columnNumber: 45\n                }, this), \" \", selectedCavo.sezione || 'N/A']\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 535,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                variant: \"body2\",\n                children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n                  children: \"SH:\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 536,\n                  columnNumber: 45\n                }, this), \" \", selectedCavo.sh || 'N/A']\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 536,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 529,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Typography, {\n              variant: \"subtitle1\",\n              gutterBottom: true,\n              children: \"Partenza\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 539,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Box, {\n              sx: {\n                mb: 2\n              },\n              children: [/*#__PURE__*/_jsxDEV(Typography, {\n                variant: \"body2\",\n                children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n                  children: \"Ubicazione:\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 541,\n                  columnNumber: 45\n                }, this), \" \", selectedCavo.ubicazione_partenza || 'N/A']\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 541,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                variant: \"body2\",\n                children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n                  children: \"Utenza:\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 542,\n                  columnNumber: 45\n                }, this), \" \", selectedCavo.utenza_partenza || 'N/A']\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 542,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                variant: \"body2\",\n                children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n                  children: \"Descrizione:\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 543,\n                  columnNumber: 45\n                }, this), \" \", selectedCavo.descrizione_utenza_partenza || 'N/A']\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 543,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                variant: \"body2\",\n                children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n                  children: \"Responsabile:\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 544,\n                  columnNumber: 45\n                }, this), \" \", selectedCavo.responsabile_partenza || 'N/A']\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 544,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                variant: \"body2\",\n                children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n                  children: \"Comanda:\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 545,\n                  columnNumber: 45\n                }, this), \" \", selectedCavo.comanda_partenza || 'N/A']\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 545,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 540,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 527,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Grid, {\n            item: true,\n            xs: 12,\n            md: 6,\n            children: [/*#__PURE__*/_jsxDEV(Typography, {\n              variant: \"subtitle1\",\n              gutterBottom: true,\n              children: \"Arrivo\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 550,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Box, {\n              sx: {\n                mb: 2\n              },\n              children: [/*#__PURE__*/_jsxDEV(Typography, {\n                variant: \"body2\",\n                children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n                  children: \"Ubicazione:\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 552,\n                  columnNumber: 45\n                }, this), \" \", selectedCavo.ubicazione_arrivo || 'N/A']\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 552,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                variant: \"body2\",\n                children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n                  children: \"Utenza:\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 553,\n                  columnNumber: 45\n                }, this), \" \", selectedCavo.utenza_arrivo || 'N/A']\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 553,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                variant: \"body2\",\n                children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n                  children: \"Descrizione:\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 554,\n                  columnNumber: 45\n                }, this), \" \", selectedCavo.descrizione_utenza_arrivo || 'N/A']\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 554,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                variant: \"body2\",\n                children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n                  children: \"Responsabile:\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 555,\n                  columnNumber: 45\n                }, this), \" \", selectedCavo.responsabile_arrivo || 'N/A']\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 555,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                variant: \"body2\",\n                children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n                  children: \"Comanda:\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 556,\n                  columnNumber: 45\n                }, this), \" \", selectedCavo.comanda_arrivo || 'N/A']\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 556,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 551,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Typography, {\n              variant: \"subtitle1\",\n              gutterBottom: true,\n              children: \"Installazione\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 559,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Box, {\n              sx: {\n                mb: 2\n              },\n              children: [/*#__PURE__*/_jsxDEV(Typography, {\n                variant: \"body2\",\n                children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n                  children: \"Metri Teorici:\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 561,\n                  columnNumber: 45\n                }, this), \" \", selectedCavo.metri_teorici || 'N/A']\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 561,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                variant: \"body2\",\n                children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n                  children: \"Metratura Reale:\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 562,\n                  columnNumber: 45\n                }, this), \" \", selectedCavo.metratura_reale || '0']\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 562,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                variant: \"body2\",\n                children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n                  children: \"Stato:\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 563,\n                  columnNumber: 45\n                }, this), \" \", normalizeInstallationStatus(selectedCavo.stato_installazione)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 563,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                variant: \"body2\",\n                children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n                  children: \"Collegamenti:\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 564,\n                  columnNumber: 45\n                }, this), \" \", selectedCavo.collegamenti || '0']\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 564,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                variant: \"body2\",\n                children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n                  children: \"Bobina:\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 565,\n                  columnNumber: 45\n                }, this), \" \", selectedCavo.id_bobina || 'N/A']\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 565,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                variant: \"body2\",\n                children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n                  children: \"Responsabile Posa:\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 566,\n                  columnNumber: 45\n                }, this), \" \", selectedCavo.responsabile_posa || 'N/A']\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 566,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                variant: \"body2\",\n                children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n                  children: \"Comanda Posa:\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 567,\n                  columnNumber: 45\n                }, this), \" \", selectedCavo.comanda_posa || 'N/A']\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 567,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                variant: \"body2\",\n                children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n                  children: \"Ultimo Aggiornamento:\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 568,\n                  columnNumber: 45\n                }, this), \" \", new Date(selectedCavo.timestamp).toLocaleString()]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 568,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 560,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 549,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 526,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 525,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(DialogActions, {\n        children: /*#__PURE__*/_jsxDEV(Button, {\n          onClick: handleCloseDetails,\n          children: \"Chiudi\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 574,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 573,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 521,\n      columnNumber: 7\n    }, this);\n  };\n\n  // Il pannello dei filtri è ora gestito dal componente CaviFilterableTable\n\n  // Renderizza il pannello delle statistiche\n  const renderStatsPanel = () => {\n    // Verifica che stats sia definito e abbia la struttura attesa\n    if (!stats || !stats.totali || !stats.metrature || !stats.stati) {\n      return /*#__PURE__*/_jsxDEV(Paper, {\n        sx: {\n          mb: 3,\n          p: 2\n        },\n        children: [/*#__PURE__*/_jsxDEV(Typography, {\n          variant: \"h6\",\n          gutterBottom: true,\n          children: \"Statistiche\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 588,\n          columnNumber: 11\n        }, this), loadingStats ? /*#__PURE__*/_jsxDEV(LinearProgress, {}, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 590,\n          columnNumber: 13\n        }, this) : /*#__PURE__*/_jsxDEV(Typography, {\n          variant: \"body2\",\n          children: \"Nessuna statistica disponibile\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 592,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 587,\n        columnNumber: 9\n      }, this);\n    }\n\n    // Valori predefiniti per evitare errori\n    const totali = stats.totali || {\n      cavi_attivi: 0,\n      cavi_spare: 0,\n      cavi_totali: 0\n    };\n    const metrature = stats.metrature || {\n      metri_teorici_totali: 0,\n      metri_reali_totali: 0,\n      percentuale_completamento: 0\n    };\n    const stati = stats.stati || [];\n    return /*#__PURE__*/_jsxDEV(Paper, {\n      sx: {\n        mb: 3,\n        p: 2\n      },\n      children: [/*#__PURE__*/_jsxDEV(Typography, {\n        variant: \"h6\",\n        gutterBottom: true,\n        children: \"Statistiche\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 605,\n        columnNumber: 9\n      }, this), loadingStats ? /*#__PURE__*/_jsxDEV(LinearProgress, {}, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 607,\n        columnNumber: 11\n      }, this) : /*#__PURE__*/_jsxDEV(Grid, {\n        container: true,\n        spacing: 2,\n        children: [/*#__PURE__*/_jsxDEV(Grid, {\n          item: true,\n          xs: 12,\n          md: 4,\n          children: [/*#__PURE__*/_jsxDEV(Typography, {\n            variant: \"subtitle1\",\n            gutterBottom: true,\n            children: \"Totali\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 611,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(Typography, {\n            variant: \"body2\",\n            children: [\"Cavi Attivi: \", totali.cavi_attivi || 0]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 612,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(Typography, {\n            variant: \"body2\",\n            children: [\"Cavi Spare: \", totali.cavi_spare || 0]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 613,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(Typography, {\n            variant: \"body2\",\n            children: [\"Totale Cavi: \", totali.cavi_totali || 0]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 614,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 610,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(Grid, {\n          item: true,\n          xs: 12,\n          md: 4,\n          children: [/*#__PURE__*/_jsxDEV(Typography, {\n            variant: \"subtitle1\",\n            gutterBottom: true,\n            children: \"Metrature\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 619,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(Typography, {\n            variant: \"body2\",\n            children: [\"Metri Teorici: \", (metrature.metri_teorici_totali || 0).toFixed(2)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 620,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(Typography, {\n            variant: \"body2\",\n            children: [\"Metri Posati: \", (metrature.metri_reali_totali || 0).toFixed(2)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 621,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(Box, {\n            sx: {\n              display: 'flex',\n              alignItems: 'center',\n              mt: 1\n            },\n            children: [/*#__PURE__*/_jsxDEV(Box, {\n              sx: {\n                width: '100%',\n                mr: 1\n              },\n              children: /*#__PURE__*/_jsxDEV(LinearProgress, {\n                variant: \"determinate\",\n                value: metrature.percentuale_completamento || 0,\n                sx: {\n                  height: 10,\n                  borderRadius: 5\n                }\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 624,\n                columnNumber: 19\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 623,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(Box, {\n              sx: {\n                minWidth: 35\n              },\n              children: /*#__PURE__*/_jsxDEV(Typography, {\n                variant: \"body2\",\n                color: \"text.secondary\",\n                children: `${(metrature.percentuale_completamento || 0).toFixed(1)}%`\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 631,\n                columnNumber: 19\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 630,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 622,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 618,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(Grid, {\n          item: true,\n          xs: 12,\n          md: 4,\n          children: [/*#__PURE__*/_jsxDEV(Typography, {\n            variant: \"subtitle1\",\n            gutterBottom: true,\n            children: \"Stati\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 637,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(Box, {\n            sx: {\n              display: 'flex',\n              flexWrap: 'wrap',\n              gap: 1\n            },\n            children: stati.length > 0 ? stati.map((stato, index) => /*#__PURE__*/_jsxDEV(Chip, {\n              label: `${stato.stato || 'N/A'}: ${stato.count || 0}`,\n              size: \"small\",\n              onClick: () => {\n                setFilters(prev => ({\n                  ...prev,\n                  stato_installazione: stato.stato === 'Non specificato' ? '' : stato.stato\n                }));\n              }\n            }, index, false, {\n              fileName: _jsxFileName,\n              lineNumber: 640,\n              columnNumber: 19\n            }, this)) : /*#__PURE__*/_jsxDEV(Typography, {\n              variant: \"body2\",\n              children: \"Nessuno stato disponibile\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 652,\n              columnNumber: 19\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 638,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 636,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 609,\n        columnNumber: 11\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 604,\n      columnNumber: 7\n    }, this);\n  };\n  return /*#__PURE__*/_jsxDEV(Box, {\n    className: \"cavi-page\",\n    children: loading ? /*#__PURE__*/_jsxDEV(Box, {\n      sx: {\n        display: 'flex',\n        flexDirection: 'column',\n        alignItems: 'center',\n        mt: 4\n      },\n      children: [/*#__PURE__*/_jsxDEV(CircularProgress, {\n        size: 40\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 666,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(Typography, {\n        sx: {\n          mt: 2\n        },\n        children: \"Caricamento cavi...\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 667,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(Button, {\n        variant: \"outlined\",\n        color: \"primary\",\n        onClick: () => window.location.reload(),\n        sx: {\n          mt: 2\n        },\n        children: \"Ricarica la pagina\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 668,\n        columnNumber: 11\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 665,\n      columnNumber: 9\n    }, this) : error ? /*#__PURE__*/_jsxDEV(Box, {\n      children: [/*#__PURE__*/_jsxDEV(Alert, {\n        severity: \"error\",\n        sx: {\n          mb: 2\n        },\n        children: [error, error.includes('Network Error') && /*#__PURE__*/_jsxDEV(Typography, {\n          variant: \"body2\",\n          sx: {\n            mt: 1\n          },\n          children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n            children: \"Suggerimento:\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 683,\n            columnNumber: 17\n          }, this), \" Verifica che il server backend sia in esecuzione sulla porta 8001.\", /*#__PURE__*/_jsxDEV(\"br\", {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 684,\n            columnNumber: 17\n          }, this), \"Puoi avviare il backend eseguendo il file \", /*#__PURE__*/_jsxDEV(\"code\", {\n            children: \"run_system.py\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 685,\n            columnNumber: 59\n          }, this), \" nella cartella principale del progetto.\"]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 682,\n          columnNumber: 15\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 679,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(Box, {\n        sx: {\n          display: 'flex',\n          gap: 2\n        },\n        children: /*#__PURE__*/_jsxDEV(Button, {\n          variant: \"contained\",\n          className: \"primary-button\",\n          onClick: () => window.location.reload(),\n          children: \"Ricarica la pagina\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 690,\n          columnNumber: 13\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 689,\n        columnNumber: 11\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 678,\n      columnNumber: 9\n    }, this) : /*#__PURE__*/_jsxDEV(Box, {\n      children: [/*#__PURE__*/_jsxDEV(Box, {\n        sx: {\n          mt: 4\n        },\n        children: /*#__PURE__*/_jsxDEV(Box, {\n          sx: {\n            display: 'flex',\n            justifyContent: 'space-between',\n            alignItems: 'center',\n            mb: 2\n          },\n          children: [/*#__PURE__*/_jsxDEV(Typography, {\n            variant: \"h5\",\n            children: [\"Cavi Attivi \", caviAttivi.length > 0 ? `(${caviAttivi.length})` : '']\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 706,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(Button, {\n            variant: \"outlined\",\n            size: \"small\",\n            onClick: () => window.location.reload(),\n            startIcon: loading ? /*#__PURE__*/_jsxDEV(CircularProgress, {\n              size: 20\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 713,\n              columnNumber: 38\n            }, this) : /*#__PURE__*/_jsxDEV(RefreshIcon, {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 713,\n              columnNumber: 71\n            }, this),\n            disabled: loading,\n            children: \"Aggiorna\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 709,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 705,\n          columnNumber: 13\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 704,\n        columnNumber: 11\n      }, this), caviAttivi.length > 0 ? /*#__PURE__*/_jsxDEV(Box, {\n        sx: {\n          mb: 2\n        },\n        children: [process.env.NODE_ENV === 'development' && /*#__PURE__*/_jsxDEV(Box, {\n          sx: {\n            mb: 2,\n            p: 1,\n            bgcolor: '#f0f0f0',\n            borderRadius: 1,\n            fontSize: '0.8rem',\n            fontFamily: 'monospace',\n            display: 'none'\n          },\n          children: Object.keys(caviAttivi[0]).map(key => /*#__PURE__*/_jsxDEV(\"div\", {\n            children: [key, \": \", JSON.stringify(caviAttivi[0][key])]\n          }, key, true, {\n            fileName: _jsxFileName,\n            lineNumber: 727,\n            columnNumber: 21\n          }, this))\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 725,\n          columnNumber: 17\n        }, this), /*#__PURE__*/_jsxDEV(CaviFilterableTable, {\n          cavi: caviAttivi,\n          loading: loading,\n          onFilteredDataChange: filteredData => console.log('Cavi attivi filtrati:', filteredData.length),\n          revisioneCorrente: ((_caviAttivi$ = caviAttivi[0]) === null || _caviAttivi$ === void 0 ? void 0 : _caviAttivi$.revisione_ufficiale) || ((_caviAttivi$2 = caviAttivi[0]) === null || _caviAttivi$2 === void 0 ? void 0 : _caviAttivi$2.revisione) || ((_caviAttivi$3 = caviAttivi[0]) === null || _caviAttivi$3 === void 0 ? void 0 : _caviAttivi$3.rev)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 731,\n          columnNumber: 15\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 722,\n        columnNumber: 13\n      }, this) : /*#__PURE__*/_jsxDEV(Alert, {\n        severity: \"info\",\n        sx: {\n          mb: 2\n        },\n        children: \"Nessun cavo attivo trovato. I cavi attivi appariranno qui.\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 739,\n        columnNumber: 13\n      }, this), /*#__PURE__*/_jsxDEV(Box, {\n        sx: {\n          mt: 4\n        },\n        children: [/*#__PURE__*/_jsxDEV(Box, {\n          sx: {\n            display: 'flex',\n            justifyContent: 'space-between',\n            alignItems: 'center',\n            mb: 2\n          },\n          children: [/*#__PURE__*/_jsxDEV(Typography, {\n            variant: \"h5\",\n            children: [\"Cavi Spare \", caviSpare.length > 0 ? `(${caviSpare.length})` : '']\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 747,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(Button, {\n            variant: \"outlined\",\n            size: \"small\",\n            onClick: () => window.location.reload(),\n            startIcon: loading ? /*#__PURE__*/_jsxDEV(CircularProgress, {\n              size: 20\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 754,\n              columnNumber: 38\n            }, this) : /*#__PURE__*/_jsxDEV(RefreshIcon, {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 754,\n              columnNumber: 71\n            }, this),\n            disabled: loading,\n            children: \"Aggiorna\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 750,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 746,\n          columnNumber: 13\n        }, this), caviSpare.length > 0 ? /*#__PURE__*/_jsxDEV(CaviFilterableTable, {\n          cavi: caviSpare,\n          loading: loading,\n          onFilteredDataChange: filteredData => console.log('Cavi spare filtrati:', filteredData.length)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 761,\n          columnNumber: 15\n        }, this) : /*#__PURE__*/_jsxDEV(Alert, {\n          severity: \"info\",\n          sx: {\n            mb: 2\n          },\n          children: \"Nessun cavo SPARE trovato. I cavi marcati come SPARE appariranno qui.\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 767,\n          columnNumber: 15\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 745,\n        columnNumber: 11\n      }, this), renderDetailsDialog(), /*#__PURE__*/_jsxDEV(Dialog, {\n        open: openEliminaCavoDialog,\n        onClose: () => setOpenEliminaCavoDialog(false),\n        fullWidth: true,\n        maxWidth: \"md\",\n        children: /*#__PURE__*/_jsxDEV(PosaCaviCollegamenti, {\n          cantiereId: cantiereId,\n          onSuccess: message => {\n            // Chiudi il dialogo\n            setOpenEliminaCavoDialog(false);\n\n            // Se c'è un messaggio, è un'operazione completata con successo\n            if (message) {\n              // Mostra un messaggio di successo\n              console.log('Operazione completata:', message);\n              // Mostra un messaggio di successo con Snackbar\n              showNotification(message, 'success');\n              // Ricarica i dati dopo un ritardo per dare tempo al database di aggiornarsi\n              setTimeout(() => {\n                console.log('Ricaricamento dati dopo operazione...');\n                try {\n                  // Ricarica i dati invece di ricaricare la pagina\n                  fetchCavi(true);\n                } catch (error) {\n                  console.error('Errore durante il ricaricamento dei dati:', error);\n                  // Se fallisce, prova a ricaricare la pagina\n                  window.location.reload();\n                }\n              }, 1000);\n            } else {\n              // È un'operazione annullata, non mostrare messaggi\n              console.log('Operazione annullata dall\\'utente');\n            }\n          },\n          onError: message => {\n            // Mostra un messaggio di errore\n            console.error('Errore durante l\\'eliminazione del cavo:', message);\n            // Mostra un alert all'utente\n            alert(`Errore: ${message}`);\n            // Chiudi il dialogo\n            setOpenEliminaCavoDialog(false);\n            // Ricarica comunque i dati per assicurarsi che la vista sia aggiornata\n            fetchCavi();\n          },\n          initialOption: \"eliminaCavo\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 785,\n          columnNumber: 13\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 779,\n        columnNumber: 11\n      }, this), openModificaCavoDialog && console.log('VisualizzaCaviPage - cantiereId prima di aprire il dialog:', cantiereId), /*#__PURE__*/_jsxDEV(Dialog, {\n        open: openModificaCavoDialog,\n        onClose: () => setOpenModificaCavoDialog(false),\n        fullWidth: true,\n        maxWidth: \"sm\",\n        children: /*#__PURE__*/_jsxDEV(PosaCaviCollegamenti, {\n          cantiereId: cantiereId,\n          onSuccess: message => {\n            // Chiudi il dialogo\n            setOpenModificaCavoDialog(false);\n\n            // Se c'è un messaggio, è un'operazione completata con successo\n            if (message) {\n              // Mostra un messaggio di successo\n              console.log('Operazione completata:', message);\n              // Mostra un messaggio di successo con Snackbar\n              showNotification(message, 'success');\n              // Ricarica i dati immediatamente\n              console.log('Ricaricamento dati dopo operazione...');\n              // Ricarica i dati con un ritardo per dare tempo al database di aggiornarsi\n              setTimeout(() => {\n                try {\n                  fetchCavi(true);\n                } catch (error) {\n                  console.error('Errore durante il ricaricamento dei dati:', error);\n                  // Se fallisce, prova a ricaricare la pagina\n                  window.location.reload();\n                }\n              }, 1000);\n            } else {\n              // È un'operazione annullata, non mostrare messaggi\n              console.log('Operazione annullata dall\\'utente');\n            }\n          },\n          onError: message => {\n            // Mostra un messaggio di errore\n            console.error('Errore durante la modifica del cavo:', message);\n            // Mostra un alert all'utente\n            alert(`Errore: ${message}`);\n            // Chiudi il dialogo\n            setOpenModificaCavoDialog(false);\n            // Ricarica comunque i dati per assicurarsi che la vista sia aggiornata\n            console.log('Ricaricamento dati dopo errore...');\n            fetchCavi(true);\n          },\n          initialOption: \"modificaCavo\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 838,\n          columnNumber: 13\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 832,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(Dialog, {\n        open: openAggiungiCavoDialog,\n        onClose: () => setOpenAggiungiCavoDialog(false),\n        fullWidth: true,\n        maxWidth: \"sm\",\n        children: [/*#__PURE__*/_jsxDEV(DialogTitle, {\n          sx: {\n            pb: 1\n          },\n          children: \"Aggiungi Nuovo Cavo\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 889,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(DialogContent, {\n          sx: {\n            pt: 0,\n            pb: 1\n          },\n          children: /*#__PURE__*/_jsxDEV(Box, {\n            sx: {\n              mt: 0\n            },\n            children: /*#__PURE__*/_jsxDEV(CavoForm, {\n              mode: \"add\",\n              cantiereId: cantiereId,\n              onSubmit: async validatedData => {\n                try {\n                  await caviService.addCavo(cantiereId, validatedData);\n                  return true;\n                } catch (error) {\n                  throw error;\n                }\n              },\n              onSuccess: message => {\n                // Chiudi il dialogo\n                setOpenAggiungiCavoDialog(false);\n                // Mostra un messaggio di successo con Snackbar\n                showNotification(message, 'success');\n                // Ricarica i dati dopo un ritardo per dare tempo al database di aggiornarsi\n                setTimeout(() => {\n                  console.log('Ricaricamento dati dopo operazione...');\n                  try {\n                    // Ricarica i dati in modalità silenziosa per evitare il \"blink\" della pagina\n                    fetchCavi(true);\n                  } catch (error) {\n                    console.error('Errore durante il ricaricamento dei dati:', error);\n                    // Se fallisce, prova a ricaricare la pagina immediatamente\n                    console.log('Tentativo di ricaricamento della pagina...');\n                    window.location.reload();\n                  }\n                }, 1000);\n              },\n              onError: message => {\n                // Mostra un messaggio di errore\n                console.error('Errore durante l\\'aggiunta del cavo:', message);\n                // Mostra un messaggio di errore con Snackbar\n                showNotification(`Errore: ${message}`, 'error');\n              },\n              isDialog: true,\n              onCancel: () => setOpenAggiungiCavoDialog(false)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 892,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 891,\n            columnNumber: 15\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 890,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 883,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(Snackbar, {\n        open: notification.open,\n        autoHideDuration: 4000,\n        onClose: handleCloseNotification,\n        anchorOrigin: {\n          vertical: 'bottom',\n          horizontal: 'center'\n        },\n        children: /*#__PURE__*/_jsxDEV(Alert, {\n          onClose: handleCloseNotification,\n          severity: notification.severity,\n          sx: {\n            width: '100%'\n          },\n          children: notification.message\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 943,\n          columnNumber: 13\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 937,\n        columnNumber: 11\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 700,\n      columnNumber: 9\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 663,\n    columnNumber: 5\n  }, this);\n};\n_s(VisualizzaCaviPage, \"dhkhYUt+KFu/CvGYCirW3sSoc2s=\", false, function () {\n  return [useAuth, useGlobalContext, useNavigate];\n});\n_c = VisualizzaCaviPage;\nexport default VisualizzaCaviPage;\nvar _c;\n$RefreshReg$(_c, \"VisualizzaCaviPage\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "Box", "Typography", "Paper", "<PERSON><PERSON>", "Grid", "Card", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "IconButton", "Chip", "CircularProgress", "LinearProgress", "Dialog", "DialogTitle", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "DialogActions", "Snackbar", "InfoIcon", "RefreshIcon", "useNavigate", "useAuth", "useGlobalContext", "PosaCaviCollegamenti", "caviService", "CavoForm", "normalizeInstallationStatus", "CaviFilterableTable", "jsxDEV", "_jsxDEV", "VisualizzaCaviPage", "_s", "_caviAttivi$", "_caviAttivi$2", "_caviAttivi$3", "isImpersonating", "user", "openEliminaCavoDialog", "setOpenEliminaCavoDialog", "openModificaCavoDialog", "setOpenModificaCavoDialog", "openAggiungiCavoDialog", "setOpenAggiungiCavoDialog", "navigate", "cantiereId", "setCantiereId", "cantiereName", "setCantiereName", "caviAttivi", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "caviSpare", "setCaviSpare", "loading", "setLoading", "error", "setError", "notification", "setNotification", "open", "message", "severity", "selected<PERSON><PERSON><PERSON>", "setSelectedCavo", "detailsDialogOpen", "setDetailsDialogOpen", "stats", "setStats", "totali", "cavi_attivi", "cavi_spare", "cavi_totali", "metrature", "metri_teorici_totali", "metri_reali_totali", "percentuale_completamento", "stati", "loadingStats", "setLoadingStats", "loadStatiInstallazione", "setStatiInstallazione", "filters", "setFilters", "stato_installazione", "tipologia", "sort_by", "sort_order", "statiInstallazione", "tipologieCavi", "setTipologieCavi", "<PERSON><PERSON><PERSON>", "silentLoading", "console", "log", "cantiereIdToUse", "localStorage", "getItem", "attivi", "get<PERSON><PERSON>", "length", "attiviError", "caviSpareTra<PERSON>ttivi", "filter", "cavo", "modificato_manualmente", "spare", "getCaviSpare", "spareError", "standardError", "statsData", "getCaviStats", "validStats", "statsError", "setTimeout", "document", "body", "textContent", "includes", "window", "location", "reload", "fetchData", "token", "selectedCantiereId", "selectedCantiereName", "i", "key", "role", "cantiere_id", "toString", "cantiere_name", "setItem", "base64Url", "split", "base64", "replace", "jsonPayload", "decodeURIComponent", "atob", "map", "c", "charCodeAt", "slice", "join", "payload", "JSON", "parse", "e", "warn", "cantiereIdNum", "parseInt", "isNaN", "item", "stato", "tipologie", "tipo", "timeoutPromise", "Promise", "_", "reject", "Error", "caviPromise", "race", "caviError", "status", "data", "stack", "code", "name", "response", "statusText", "sparePromise", "err", "_err$response", "_err$response2", "_err$response3", "_err$response4", "_err$response5", "_err$response5$data", "errorMessage", "detail", "handleOpenDetails", "handleCloseDetails", "handleCloseNotification", "prev", "showNotification", "renderDetailsDialog", "onClose", "max<PERSON><PERSON><PERSON>", "fullWidth", "children", "id_cavo", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "dividers", "container", "spacing", "xs", "md", "variant", "gutterBottom", "sx", "mb", "sistema", "utility", "colore_cavo", "n_conduttori", "sezione", "sh", "ubicazione_partenza", "utenza_partenza", "descrizione_utenza_partenza", "responsabile_partenza", "comanda_partenza", "ubicazione_arrivo", "utenza_arrivo", "descrizione_utenza_arrivo", "responsabile_arrivo", "comanda_arrivo", "metri_te<PERSON>ci", "metratura_reale", "colle<PERSON>nti", "id_bobina", "responsabile_posa", "comanda_posa", "Date", "timestamp", "toLocaleString", "onClick", "renderStatsPanel", "p", "toFixed", "display", "alignItems", "mt", "width", "mr", "value", "height", "borderRadius", "min<PERSON><PERSON><PERSON>", "color", "flexWrap", "gap", "index", "label", "count", "size", "className", "flexDirection", "justifyContent", "startIcon", "disabled", "process", "env", "NODE_ENV", "bgcolor", "fontSize", "fontFamily", "Object", "keys", "stringify", "cavi", "onFilteredDataChange", "filteredData", "revisioneCorrente", "revisione_ufficiale", "revisione", "rev", "onSuccess", "onError", "alert", "initialOption", "pb", "pt", "mode", "onSubmit", "validatedData", "addCavo", "isDialog", "onCancel", "autoHideDuration", "anchor<PERSON><PERSON><PERSON>", "vertical", "horizontal", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/PycharmProjects/CMS/webapp/frontend/src/pages/cavi/VisualizzaCaviPage.js"], "sourcesContent": ["import React, { useState, useEffect } from 'react';\nimport {\n  Box,\n  Typography,\n  Paper,\n  Button,\n  Grid,\n  Card,\n  CardContent,\n  Alert,\n  IconButton,\n  Chip,\n  CircularProgress,\n  LinearProgress,\n  Dialog,\n  DialogTitle,\n  DialogContent,\n  DialogActions,\n  Snackbar\n} from '@mui/material';\nimport InfoIcon from '@mui/icons-material/Info';\nimport RefreshIcon from '@mui/icons-material/Refresh';\nimport { useNavigate } from 'react-router-dom';\nimport { useAuth } from '../../context/AuthContext';\nimport { useGlobalContext } from '../../context/GlobalContext';\nimport PosaCaviCollegamenti from '../../components/cavi/PosaCaviCollegamenti';\nimport caviService from '../../services/caviService';\nimport CavoForm from '../../components/cavi/CavoForm';\nimport { normalizeInstallationStatus } from '../../utils/validationUtils';\nimport CaviFilterableTable from '../../components/cavi/CaviFilterableTable';\nimport './CaviPage.css';\n\nconst VisualizzaCaviPage = () => {\n  const { isImpersonating, user } = useAuth();\n  const { openEliminaCavoDialog, setOpenEliminaCavoDialog, openModificaCavoDialog, setOpenModificaCavoDialog, openAggiungiCavoDialog, setOpenAggiungiCavoDialog } = useGlobalContext();\n  const navigate = useNavigate();\n  const [cantiereId, setCantiereId] = useState(null);\n  const [cantiereName, setCantiereName] = useState('');\n  const [caviAttivi, setCaviAttivi] = useState([]);\n  const [caviSpare, setCaviSpare] = useState([]);\n  const [loading, setLoading] = useState(true);\n  const [error, setError] = useState(null);\n  // Stato per le notifiche\n  const [notification, setNotification] = useState({ open: false, message: '', severity: 'success' });\n  // Rimosso stato viewMode\n\n  // Stato per il dialogo dei dettagli del cavo\n  const [selectedCavo, setSelectedCavo] = useState(null);\n  const [detailsDialogOpen, setDetailsDialogOpen] = useState(false);\n\n  // Stato per le statistiche\n  const [stats, setStats] = useState({\n    totali: { cavi_attivi: 0, cavi_spare: 0, cavi_totali: 0 },\n    metrature: { metri_teorici_totali: 0, metri_reali_totali: 0, percentuale_completamento: 0 },\n    stati: []\n  });\n  const [loadingStats, setLoadingStats] = useState(false);\n\n  // Rimosso stato per il debug\n\n  // Funzione per caricare gli stati di installazione disponibili\n  const loadStatiInstallazione = () => {\n    // Usa i valori dell'enum StatoInstallazione\n    setStatiInstallazione(['Installato', 'Da installare', 'In corso']);\n  };\n\n  // Stato per filtri e ordinamento\n  const [filters, setFilters] = useState({\n    stato_installazione: '',\n    tipologia: '',\n    sort_by: '',\n    sort_order: 'asc'\n  });\n\n  // Opzioni per i filtri\n  const [statiInstallazione, setStatiInstallazione] = useState([]);\n  const [tipologieCavi, setTipologieCavi] = useState([]);\n\n  // Rimossa funzione di debug\n\n  // Funzione per caricare i cavi\n  // Il parametro silentLoading permette di evitare di mostrare lo stato di caricamento\n  const fetchCavi = async (silentLoading = false) => {\n    try {\n      if (!silentLoading) {\n        setLoading(true);\n      }\n      console.log('Caricamento cavi per cantiere:', cantiereId);\n\n      // Verifica che cantiereId sia valido\n      if (!cantiereId) {\n        console.error('fetchCavi: cantiereId non valido:', cantiereId);\n        setError('ID cantiere non valido o mancante. Ricarica la pagina.');\n        setLoading(false);\n        return;\n      }\n\n      // Recupera il cantiereId dal localStorage come fallback\n      let cantiereIdToUse = cantiereId;\n      if (!cantiereIdToUse) {\n        cantiereIdToUse = localStorage.getItem('selectedCantiereId');\n        console.log('Usando cantiereId dal localStorage:', cantiereIdToUse);\n        if (!cantiereIdToUse) {\n          console.error('Impossibile trovare un ID cantiere valido');\n          setError('ID cantiere non trovato. Ricarica la pagina.');\n          setLoading(false);\n          return;\n        }\n      }\n\n      // Carica i cavi attivi\n      console.log('Caricamento cavi attivi (tipo_cavo=0)...');\n      let attivi = [];\n      try {\n        attivi = await caviService.getCavi(cantiereIdToUse, 0, filters);\n        console.log('Cavi attivi caricati:', attivi ? attivi.length : 0);\n      } catch (attiviError) {\n        console.error('Errore nel caricamento dei cavi attivi:', attiviError);\n        // Continua con un array vuoto\n        attivi = [];\n      }\n\n      // Verifica se ci sono cavi con modificato_manualmente = 3 tra i cavi attivi\n      if (attivi && attivi.length > 0) {\n        const caviSpareTraAttivi = attivi.filter(cavo => cavo.modificato_manualmente === 3);\n        if (caviSpareTraAttivi.length > 0) {\n          console.error('ERRORE: Trovati cavi con modificato_manualmente = 3 tra i cavi attivi:', caviSpareTraAttivi);\n        }\n      }\n\n      setCaviAttivi(attivi || []);\n\n      // Carica i cavi SPARE con la nuova funzione dedicata\n      let spare = [];\n      try {\n        console.log('Caricamento cavi SPARE con funzione dedicata...');\n        spare = await caviService.getCaviSpare(cantiereIdToUse);\n        console.log('Cavi SPARE caricati con funzione dedicata:', spare ? spare.length : 0);\n        if (spare && spare.length > 0) {\n          console.log('Primo cavo SPARE:', spare[0]);\n        }\n      } catch (spareError) {\n        console.error('Errore nel caricamento dei cavi SPARE con funzione dedicata:', spareError);\n        // Se fallisce, prova con il metodo standard\n        try {\n          console.log('Tentativo con metodo standard...');\n          spare = await caviService.getCavi(cantiereIdToUse, 3);\n          console.log('Cavi SPARE caricati con metodo standard:', spare ? spare.length : 0);\n        } catch (standardError) {\n          console.error('Errore anche con metodo standard:', standardError);\n          // Continua con un array vuoto\n          spare = [];\n        }\n      }\n      setCaviSpare(spare || []);\n\n      // Carica le statistiche\n      try {\n        console.log('Caricamento statistiche...');\n        const statsData = await caviService.getCaviStats(cantiereIdToUse);\n        console.log('Statistiche caricate:', statsData);\n\n        // Verifica che statsData abbia la struttura attesa\n        if (!statsData || typeof statsData !== 'object') {\n          console.error('Statistiche non valide:', statsData);\n          // Imposta un oggetto stats con struttura valida ma vuota\n          setStats({\n            totali: { cavi_attivi: 0, cavi_spare: 0, cavi_totali: 0 },\n            metrature: { metri_teorici_totali: 0, metri_reali_totali: 0, percentuale_completamento: 0 },\n            stati: []\n          });\n        } else {\n          // Assicurati che tutte le proprietà necessarie siano presenti\n          const validStats = {\n            totali: statsData.totali || { cavi_attivi: 0, cavi_spare: 0, cavi_totali: 0 },\n            metrature: statsData.metrature || { metri_teorici_totali: 0, metri_reali_totali: 0, percentuale_completamento: 0 },\n            stati: statsData.stati || []\n          };\n          setStats(validStats);\n        }\n      } catch (statsError) {\n        console.error('Errore nel caricamento delle statistiche:', statsError);\n        // Continua con statistiche vuote ma con struttura valida\n        setStats({\n          totali: { cavi_attivi: 0, cavi_spare: 0, cavi_totali: 0 },\n          metrature: { metri_teorici_totali: 0, metri_reali_totali: 0, percentuale_completamento: 0 },\n          stati: []\n        });\n      }\n\n      // Se siamo arrivati qui, rimuovi eventuali messaggi di errore precedenti\n      setError('');\n    } catch (error) {\n      console.error('Errore generale nel caricamento dei cavi:', error);\n      setError(`Errore nel caricamento dei cavi: ${error.message || 'Errore sconosciuto'}`);\n\n      // Prova a ricaricare la pagina dopo un ritardo se l'errore persiste\n      setTimeout(() => {\n        // Verifica se siamo ancora in errore\n        if (document.body.textContent.includes('Errore nel caricamento dei cavi')) {\n          console.log('Errore persistente, tentativo di ricaricamento della pagina...');\n          window.location.reload();\n        }\n      }, 5000); // 5 secondi di ritardo\n    } finally {\n      if (!silentLoading) {\n        setLoading(false);\n      }\n    }\n  };\n\n  // Carica i dati del cantiere e dei cavi\n  useEffect(() => {\n    // Carica gli stati di installazione all'avvio\n    loadStatiInstallazione();\n\n    const fetchData = async () => {\n      try {\n        console.log('Inizializzazione VisualizzaCaviPage...');\n\n        // Verifica che l'utente sia autenticato\n        const token = localStorage.getItem('token');\n        console.log('Token presente:', !!token);\n        if (!token) {\n          setError('Sessione scaduta. Effettua nuovamente il login.');\n          setLoading(false);\n          return;\n        }\n\n        // Recupera l'ID del cantiere selezionato dal localStorage\n        let selectedCantiereId = localStorage.getItem('selectedCantiereId');\n        let selectedCantiereName = localStorage.getItem('selectedCantiereName');\n\n        console.log('Cantiere selezionato dal localStorage:', { selectedCantiereId, selectedCantiereName });\n        console.log('Dati utente:', user);\n\n        // Stampa tutti i dati nel localStorage per debug\n        console.log('DEBUG - Tutti i dati nel localStorage:');\n        for (let i = 0; i < localStorage.length; i++) {\n          const key = localStorage.key(i);\n          console.log(`${key}: ${localStorage.getItem(key)}`);\n        }\n\n        // SOLUZIONE DIRETTA: Ottieni l'ID del cantiere direttamente dal token JWT\n        if (user?.role === 'cantieri_user') {\n          console.log('Utente cantiere rilevato, tentativo di recupero ID cantiere dai dati utente');\n\n          // Verifica se l'utente ha un ID cantiere nei dati utente\n          if (user.cantiere_id) {\n            console.log('Trovato ID cantiere nei dati utente:', user.cantiere_id);\n            selectedCantiereId = user.cantiere_id.toString();\n            selectedCantiereName = user.cantiere_name || `Cantiere ${user.cantiere_id}`;\n\n            // Salva l'ID e il nome del cantiere nel localStorage\n            localStorage.setItem('selectedCantiereId', selectedCantiereId);\n            localStorage.setItem('selectedCantiereName', selectedCantiereName);\n            console.log('Salvato ID cantiere nel localStorage:', selectedCantiereId);\n          } else {\n            // Tentativo di recupero dal token JWT\n            try {\n              console.log('Tentativo di decodifica del token JWT per recuperare l\\'ID cantiere');\n              const token = localStorage.getItem('token');\n              if (token) {\n                // Decodifica il token JWT (senza verifica della firma)\n                const base64Url = token.split('.')[1];\n                const base64 = base64Url.replace(/-/g, '+').replace(/_/g, '/');\n                const jsonPayload = decodeURIComponent(atob(base64).split('').map(c => {\n                  return '%' + ('00' + c.charCodeAt(0).toString(16)).slice(-2);\n                }).join(''));\n\n                const payload = JSON.parse(jsonPayload);\n                console.log('Payload del token JWT:', payload);\n\n                if (payload.cantiere_id) {\n                  console.log('Trovato ID cantiere nel token JWT:', payload.cantiere_id);\n                  selectedCantiereId = payload.cantiere_id.toString();\n                  // Usa un nome generico se non disponibile\n                  selectedCantiereName = `Cantiere ${payload.cantiere_id}`;\n\n                  // Salva l'ID e il nome del cantiere nel localStorage\n                  localStorage.setItem('selectedCantiereId', selectedCantiereId);\n                  localStorage.setItem('selectedCantiereName', selectedCantiereName);\n                  console.log('Salvato ID cantiere nel localStorage:', selectedCantiereId);\n                }\n              }\n            } catch (e) {\n              console.error('Errore durante la decodifica del token JWT:', e);\n            }\n          }\n        }\n\n        // SOLUZIONE TEMPORANEA: Se ancora non abbiamo un ID cantiere, usa un valore hardcoded per debug\n        if (!selectedCantiereId || selectedCantiereId === 'undefined' || selectedCantiereId === 'null') {\n          console.warn('ATTENZIONE: Nessun ID cantiere trovato, utilizzo valore hardcoded per debug');\n          // Usa il primo cantiere disponibile (questo è solo per debug)\n          selectedCantiereId = '1'; // Sostituisci con un ID cantiere valido nel tuo database\n          selectedCantiereName = 'Cantiere Debug';\n\n          // Salva l'ID e il nome del cantiere nel localStorage\n          localStorage.setItem('selectedCantiereId', selectedCantiereId);\n          localStorage.setItem('selectedCantiereName', selectedCantiereName);\n          console.log('Salvato ID cantiere hardcoded nel localStorage:', selectedCantiereId);\n        }\n\n        // Verifica finale\n        if (!selectedCantiereId) {\n          setError('Nessun cantiere selezionato. Torna alla pagina dei cantieri.');\n          setLoading(false);\n          return;\n        }\n\n        // Verifica che l'ID del cantiere sia un numero valido\n        const cantiereIdNum = parseInt(selectedCantiereId, 10);\n        console.log('ID cantiere convertito a numero:', cantiereIdNum);\n        if (isNaN(cantiereIdNum)) {\n          setError(`ID cantiere non valido: ${selectedCantiereId}. Torna alla pagina dei cantieri.`);\n          setLoading(false);\n          return;\n        }\n\n        // Usa il numero convertito, non la stringa\n        setCantiereId(cantiereIdNum);\n        setCantiereName(selectedCantiereName || `Cantiere ${cantiereIdNum}`);\n\n        // Carica le statistiche dei cavi\n        try {\n          setLoadingStats(true);\n          console.log('Caricamento statistiche cavi per cantiere:', cantiereIdNum);\n          const statsData = await caviService.getCaviStats(cantiereIdNum);\n          console.log('Statistiche cavi caricate:', statsData);\n          setStats(statsData);\n\n          // Estrai gli stati di installazione e le tipologie per i filtri\n          if (statsData && statsData.stati) {\n            const stati = statsData.stati.map(item => item.stato).filter(stato => stato !== 'Non specificato');\n            setStatiInstallazione(stati);\n          }\n\n          if (statsData && statsData.tipologie) {\n            const tipologie = statsData.tipologie.map(item => item.tipologia).filter(tipo => tipo !== 'Non specificata');\n            setTipologieCavi(tipologie);\n          }\n\n          setLoadingStats(false);\n        } catch (statsError) {\n          console.error('Errore nel caricamento delle statistiche:', statsError);\n          setLoadingStats(false);\n          // Non interrompere il flusso se le statistiche falliscono\n        }\n\n        // Carica i cavi attivi con gestione degli errori migliorata\n        console.log('Caricamento cavi attivi per cantiere:', cantiereIdNum);\n        try {\n          // Imposta un timeout per evitare che la richiesta rimanga bloccata\n          const timeoutPromise = new Promise((_, reject) => {\n            setTimeout(() => reject(new Error('Timeout durante il caricamento dei cavi attivi')), 30000); // Aumentato a 30 secondi\n          });\n\n          // Esegui la richiesta con un timeout di sicurezza e applica i filtri\n          console.log('Iniziando chiamata API per cavi attivi con filtri:', filters);\n          const caviPromise = caviService.getCavi(cantiereIdNum, 0, filters);\n          const attivi = await Promise.race([caviPromise, timeoutPromise]);\n\n          console.log('Cavi attivi caricati:', attivi);\n          console.log('Numero di cavi attivi trovati:', attivi ? attivi.length : 0);\n          if (attivi && attivi.length > 0) {\n            console.log('Primo cavo attivo:', attivi[0]);\n          } else {\n            console.warn('Nessun cavo attivo trovato per il cantiere', cantiereIdNum);\n          }\n          setCaviAttivi(attivi || []);\n        } catch (caviError) {\n          console.error('Errore nel caricamento dei cavi attivi:', caviError);\n          console.error('Dettagli errore cavi attivi:', {\n            message: caviError.message,\n            status: caviError.status,\n            data: caviError.data,\n            stack: caviError.stack,\n            code: caviError.code,\n            name: caviError.name,\n            response: caviError.response ? {\n              status: caviError.response.status,\n              statusText: caviError.response.statusText,\n              data: caviError.response.data\n            } : 'No response'\n          });\n\n          // Non interrompere il flusso, continua con i cavi spare\n          setCaviAttivi([]);\n          console.warn('Continuazione del flusso dopo errore nei cavi attivi');\n\n          // Aggiungi un messaggio di errore visibile all'utente\n          setError(`Errore nel caricamento dei cavi attivi: ${caviError.message}. Controlla la console per maggiori dettagli.`);\n        }\n\n        // Carica i cavi spare con gestione degli errori migliorata\n        console.log('Caricamento cavi spare per cantiere:', cantiereIdNum);\n        try {\n          // Imposta un timeout per evitare che la richiesta rimanga bloccata\n          const timeoutPromise = new Promise((_, reject) => {\n            setTimeout(() => reject(new Error('Timeout durante il caricamento dei cavi spare')), 30000); // Aumentato a 30 secondi\n          });\n\n          // Esegui la richiesta con un timeout di sicurezza\n          console.log('Iniziando chiamata API per cavi spare...');\n          // Non applichiamo i filtri ai cavi spare, solo agli attivi\n          const sparePromise = caviService.getCavi(cantiereIdNum, 3);\n          const spare = await Promise.race([sparePromise, timeoutPromise]);\n\n          console.log('Cavi spare caricati:', spare);\n          console.log('Numero di cavi spare trovati:', spare ? spare.length : 0);\n          if (spare && spare.length > 0) {\n            console.log('Primo cavo spare:', spare[0]);\n          } else {\n            console.warn('Nessun cavo spare trovato per il cantiere', cantiereIdNum);\n          }\n          setCaviSpare(spare || []);\n        } catch (spareError) {\n          console.error('Errore nel caricamento dei cavi spare:', spareError);\n          console.error('Dettagli errore cavi spare:', {\n            message: spareError.message,\n            status: spareError.status,\n            data: spareError.data,\n            stack: spareError.stack,\n            code: spareError.code,\n            name: spareError.name,\n            response: spareError.response ? {\n              status: spareError.response.status,\n              statusText: spareError.response.statusText,\n              data: spareError.response.data\n            } : 'No response'\n          });\n\n          // Non interrompere il flusso, imposta un array vuoto\n          setCaviSpare([]);\n\n          // Aggiungi un messaggio di errore visibile all'utente se non c'è già un errore per i cavi attivi\n          if (!error) {\n            setError(`Errore nel caricamento dei cavi spare: ${spareError.message}. Controlla la console per maggiori dettagli.`);\n          }\n        }\n\n        // Se siamo arrivati qui, almeno abbiamo caricato l'interfaccia di base\n        setLoading(false);\n\n      } catch (err) {\n        console.error('Errore nel caricamento dei cavi:', err);\n        console.error('Dettagli errore generale:', {\n          message: err.message,\n          status: err.status || err.response?.status,\n          data: err.data || err.response?.data,\n          stack: err.stack\n        });\n\n        // Estrai il messaggio di errore dettagliato\n        let errorMessage = 'Errore sconosciuto';\n\n        if (err.message && err.message.includes('ID cantiere non valido')) {\n          errorMessage = err.message;\n        } else if (err.status === 401 || err.status === 403 ||\n                  err.response?.status === 401 || err.response?.status === 403) {\n          errorMessage = 'Sessione scaduta o non autorizzata. Effettua nuovamente il login.';\n        } else if (err.response?.data?.detail) {\n          // Estrai il messaggio di errore dettagliato dall'API\n          errorMessage = `Errore API: ${err.response.data.detail}`;\n        } else if (err.code === 'ERR_NETWORK') {\n          // Errore di rete\n          errorMessage = 'Network Error. Verifica che il backend sia in esecuzione e accessibile.';\n        } else if (err.message) {\n          errorMessage = err.message;\n        }\n\n        setError(`Impossibile caricare i cavi: ${errorMessage}. Riprova più tardi.`);\n\n        // Imposta array vuoti per evitare errori di rendering\n        setCaviAttivi([]);\n        setCaviSpare([]);\n      } finally {\n        setLoading(false);\n      }\n    };\n\n    fetchData();\n  }, [filters]); // Ricarica i dati quando cambiano i filtri\n\n  // I filtri sono ora gestiti dal componente CaviFilterableTable\n\n  // Funzione per aprire il dialogo dei dettagli del cavo\n  const handleOpenDetails = (cavo) => {\n    setSelectedCavo(cavo);\n    setDetailsDialogOpen(true);\n  };\n\n  // Funzione per chiudere il dialogo dei dettagli del cavo\n  const handleCloseDetails = () => {\n    setDetailsDialogOpen(false);\n    setSelectedCavo(null);\n  };\n\n  // Funzione per chiudere la notifica\n  const handleCloseNotification = () => {\n    setNotification(prev => ({ ...prev, open: false }));\n  };\n\n  // Funzione per mostrare una notifica\n  const showNotification = (message, severity = 'success') => {\n    setNotification({ open: true, message, severity });\n  };\n\n  // Nessuna funzione di navigazione necessaria, tutto è gestito dal menu principale\n\n  // La visualizzazione dei cavi è ora gestita dal componente CaviFilterableTable\n\n  // Rimossa funzione handleViewModeChange\n\n  // Renderizza il dialogo dei dettagli del cavo\n  const renderDetailsDialog = () => {\n    if (!selectedCavo) return null;\n\n    return (\n      <Dialog open={detailsDialogOpen} onClose={handleCloseDetails} maxWidth=\"md\" fullWidth>\n        <DialogTitle>\n          Dettagli Cavo: {selectedCavo.id_cavo}\n        </DialogTitle>\n        <DialogContent dividers>\n          <Grid container spacing={2}>\n            <Grid item xs={12} md={6}>\n              <Typography variant=\"subtitle1\" gutterBottom>Informazioni Generali</Typography>\n              <Box sx={{ mb: 2 }}>\n                <Typography variant=\"body2\"><strong>Sistema:</strong> {selectedCavo.sistema || 'N/A'}</Typography>\n                <Typography variant=\"body2\"><strong>Utility:</strong> {selectedCavo.utility || 'N/A'}</Typography>\n                <Typography variant=\"body2\"><strong>Tipologia:</strong> {selectedCavo.tipologia || 'N/A'}</Typography>\n                <Typography variant=\"body2\"><strong>Colore:</strong> {selectedCavo.colore_cavo || 'N/A'}</Typography>\n                <Typography variant=\"body2\"><strong>N. Conduttori:</strong> {selectedCavo.n_conduttori || 'N/A'}</Typography>\n                <Typography variant=\"body2\"><strong>Sezione:</strong> {selectedCavo.sezione || 'N/A'}</Typography>\n                <Typography variant=\"body2\"><strong>SH:</strong> {selectedCavo.sh || 'N/A'}</Typography>\n              </Box>\n\n              <Typography variant=\"subtitle1\" gutterBottom>Partenza</Typography>\n              <Box sx={{ mb: 2 }}>\n                <Typography variant=\"body2\"><strong>Ubicazione:</strong> {selectedCavo.ubicazione_partenza || 'N/A'}</Typography>\n                <Typography variant=\"body2\"><strong>Utenza:</strong> {selectedCavo.utenza_partenza || 'N/A'}</Typography>\n                <Typography variant=\"body2\"><strong>Descrizione:</strong> {selectedCavo.descrizione_utenza_partenza || 'N/A'}</Typography>\n                <Typography variant=\"body2\"><strong>Responsabile:</strong> {selectedCavo.responsabile_partenza || 'N/A'}</Typography>\n                <Typography variant=\"body2\"><strong>Comanda:</strong> {selectedCavo.comanda_partenza || 'N/A'}</Typography>\n              </Box>\n            </Grid>\n\n            <Grid item xs={12} md={6}>\n              <Typography variant=\"subtitle1\" gutterBottom>Arrivo</Typography>\n              <Box sx={{ mb: 2 }}>\n                <Typography variant=\"body2\"><strong>Ubicazione:</strong> {selectedCavo.ubicazione_arrivo || 'N/A'}</Typography>\n                <Typography variant=\"body2\"><strong>Utenza:</strong> {selectedCavo.utenza_arrivo || 'N/A'}</Typography>\n                <Typography variant=\"body2\"><strong>Descrizione:</strong> {selectedCavo.descrizione_utenza_arrivo || 'N/A'}</Typography>\n                <Typography variant=\"body2\"><strong>Responsabile:</strong> {selectedCavo.responsabile_arrivo || 'N/A'}</Typography>\n                <Typography variant=\"body2\"><strong>Comanda:</strong> {selectedCavo.comanda_arrivo || 'N/A'}</Typography>\n              </Box>\n\n              <Typography variant=\"subtitle1\" gutterBottom>Installazione</Typography>\n              <Box sx={{ mb: 2 }}>\n                <Typography variant=\"body2\"><strong>Metri Teorici:</strong> {selectedCavo.metri_teorici || 'N/A'}</Typography>\n                <Typography variant=\"body2\"><strong>Metratura Reale:</strong> {selectedCavo.metratura_reale || '0'}</Typography>\n                <Typography variant=\"body2\"><strong>Stato:</strong> {normalizeInstallationStatus(selectedCavo.stato_installazione)}</Typography>\n                <Typography variant=\"body2\"><strong>Collegamenti:</strong> {selectedCavo.collegamenti || '0'}</Typography>\n                <Typography variant=\"body2\"><strong>Bobina:</strong> {selectedCavo.id_bobina || 'N/A'}</Typography>\n                <Typography variant=\"body2\"><strong>Responsabile Posa:</strong> {selectedCavo.responsabile_posa || 'N/A'}</Typography>\n                <Typography variant=\"body2\"><strong>Comanda Posa:</strong> {selectedCavo.comanda_posa || 'N/A'}</Typography>\n                <Typography variant=\"body2\"><strong>Ultimo Aggiornamento:</strong> {new Date(selectedCavo.timestamp).toLocaleString()}</Typography>\n              </Box>\n            </Grid>\n          </Grid>\n        </DialogContent>\n        <DialogActions>\n          <Button onClick={handleCloseDetails}>Chiudi</Button>\n        </DialogActions>\n      </Dialog>\n    );\n  };\n\n  // Il pannello dei filtri è ora gestito dal componente CaviFilterableTable\n\n  // Renderizza il pannello delle statistiche\n  const renderStatsPanel = () => {\n    // Verifica che stats sia definito e abbia la struttura attesa\n    if (!stats || !stats.totali || !stats.metrature || !stats.stati) {\n      return (\n        <Paper sx={{ mb: 3, p: 2 }}>\n          <Typography variant=\"h6\" gutterBottom>Statistiche</Typography>\n          {loadingStats ? (\n            <LinearProgress />\n          ) : (\n            <Typography variant=\"body2\">Nessuna statistica disponibile</Typography>\n          )}\n        </Paper>\n      );\n    }\n\n    // Valori predefiniti per evitare errori\n    const totali = stats.totali || { cavi_attivi: 0, cavi_spare: 0, cavi_totali: 0 };\n    const metrature = stats.metrature || { metri_teorici_totali: 0, metri_reali_totali: 0, percentuale_completamento: 0 };\n    const stati = stats.stati || [];\n\n    return (\n      <Paper sx={{ mb: 3, p: 2 }}>\n        <Typography variant=\"h6\" gutterBottom>Statistiche</Typography>\n        {loadingStats ? (\n          <LinearProgress />\n        ) : (\n          <Grid container spacing={2}>\n            <Grid item xs={12} md={4}>\n              <Typography variant=\"subtitle1\" gutterBottom>Totali</Typography>\n              <Typography variant=\"body2\">Cavi Attivi: {totali.cavi_attivi || 0}</Typography>\n              <Typography variant=\"body2\">Cavi Spare: {totali.cavi_spare || 0}</Typography>\n              <Typography variant=\"body2\">Totale Cavi: {totali.cavi_totali || 0}</Typography>\n              {/* Rimossa visualizzazione della revisione da qui, spostata nel titolo delle statistiche della tabella */}\n            </Grid>\n\n            <Grid item xs={12} md={4}>\n              <Typography variant=\"subtitle1\" gutterBottom>Metrature</Typography>\n              <Typography variant=\"body2\">Metri Teorici: {(metrature.metri_teorici_totali || 0).toFixed(2)}</Typography>\n              <Typography variant=\"body2\">Metri Posati: {(metrature.metri_reali_totali || 0).toFixed(2)}</Typography>\n              <Box sx={{ display: 'flex', alignItems: 'center', mt: 1 }}>\n                <Box sx={{ width: '100%', mr: 1 }}>\n                  <LinearProgress\n                    variant=\"determinate\"\n                    value={metrature.percentuale_completamento || 0}\n                    sx={{ height: 10, borderRadius: 5 }}\n                  />\n                </Box>\n                <Box sx={{ minWidth: 35 }}>\n                  <Typography variant=\"body2\" color=\"text.secondary\">{`${(metrature.percentuale_completamento || 0).toFixed(1)}%`}</Typography>\n                </Box>\n              </Box>\n            </Grid>\n\n            <Grid item xs={12} md={4}>\n              <Typography variant=\"subtitle1\" gutterBottom>Stati</Typography>\n              <Box sx={{ display: 'flex', flexWrap: 'wrap', gap: 1 }}>\n                {stati.length > 0 ? stati.map((stato, index) => (\n                  <Chip\n                    key={index}\n                    label={`${stato.stato || 'N/A'}: ${stato.count || 0}`}\n                    size=\"small\"\n                    onClick={() => {\n                      setFilters(prev => ({\n                        ...prev,\n                        stato_installazione: stato.stato === 'Non specificato' ? '' : stato.stato\n                      }));\n                    }}\n                  />\n                )) : (\n                  <Typography variant=\"body2\">Nessuno stato disponibile</Typography>\n                )}\n              </Box>\n            </Grid>\n          </Grid>\n        )}\n      </Paper>\n    );\n  };\n\n  return (\n    <Box className=\"cavi-page\">\n      {loading ? (\n        <Box sx={{ display: 'flex', flexDirection: 'column', alignItems: 'center', mt: 4 }}>\n          <CircularProgress size={40} />\n          <Typography sx={{ mt: 2 }}>Caricamento cavi...</Typography>\n          <Button\n            variant=\"outlined\"\n            color=\"primary\"\n            onClick={() => window.location.reload()}\n            sx={{ mt: 2 }}\n          >\n            Ricarica la pagina\n          </Button>\n        </Box>\n      ) : error ? (\n        <Box>\n          <Alert severity=\"error\" sx={{ mb: 2 }}>\n            {error}\n            {error.includes('Network Error') && (\n              <Typography variant=\"body2\" sx={{ mt: 1 }}>\n                <strong>Suggerimento:</strong> Verifica che il server backend sia in esecuzione sulla porta 8001.\n                <br />\n                Puoi avviare il backend eseguendo il file <code>run_system.py</code> nella cartella principale del progetto.\n              </Typography>\n            )}\n          </Alert>\n          <Box sx={{ display: 'flex', gap: 2 }}>\n            <Button\n              variant=\"contained\"\n              className=\"primary-button\"\n              onClick={() => window.location.reload()}\n            >\n              Ricarica la pagina\n            </Button>\n          </Box>\n        </Box>\n      ) : (\n        <Box>\n          {/* Rimosso il pulsante di refresh, gli utenti possono usare il refresh del browser */}\n\n          {/* Sezione Cavi Attivi */}\n          <Box sx={{ mt: 4 }}>\n            <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 2 }}>\n              <Typography variant=\"h5\">\n                Cavi Attivi {caviAttivi.length > 0 ? `(${caviAttivi.length})` : ''}\n              </Typography>\n              <Button\n                variant=\"outlined\"\n                size=\"small\"\n                onClick={() => window.location.reload()}\n                startIcon={loading ? <CircularProgress size={20} /> : <RefreshIcon />}\n                disabled={loading}\n              >\n                Aggiorna\n              </Button>\n            </Box>\n          </Box>\n\n          {caviAttivi.length > 0 ? (\n            <Box sx={{ mb: 2 }}>\n              {/* Debug: Mostra le proprietà del primo cavo per verificare il nome del campo revisione */}\n              {process.env.NODE_ENV === 'development' && (\n                <Box sx={{ mb: 2, p: 1, bgcolor: '#f0f0f0', borderRadius: 1, fontSize: '0.8rem', fontFamily: 'monospace', display: 'none' }}>\n                  {Object.keys(caviAttivi[0]).map(key => (\n                    <div key={key}>{key}: {JSON.stringify(caviAttivi[0][key])}</div>\n                  ))}\n                </Box>\n              )}\n              <CaviFilterableTable\n                cavi={caviAttivi}\n                loading={loading}\n                onFilteredDataChange={(filteredData) => console.log('Cavi attivi filtrati:', filteredData.length)}\n                revisioneCorrente={caviAttivi[0]?.revisione_ufficiale || caviAttivi[0]?.revisione || caviAttivi[0]?.rev}\n              />\n            </Box>\n          ) : (\n            <Alert severity=\"info\" sx={{ mb: 2 }}>\n              Nessun cavo attivo trovato. I cavi attivi appariranno qui.\n            </Alert>\n          )}\n\n          {/* Sezione Cavi Spare */}\n          <Box sx={{ mt: 4 }}>\n            <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 2 }}>\n              <Typography variant=\"h5\">\n                Cavi Spare {caviSpare.length > 0 ? `(${caviSpare.length})` : ''}\n              </Typography>\n              <Button\n                variant=\"outlined\"\n                size=\"small\"\n                onClick={() => window.location.reload()}\n                startIcon={loading ? <CircularProgress size={20} /> : <RefreshIcon />}\n                disabled={loading}\n              >\n                Aggiorna\n              </Button>\n            </Box>\n            {caviSpare.length > 0 ? (\n              <CaviFilterableTable\n                cavi={caviSpare}\n                loading={loading}\n                onFilteredDataChange={(filteredData) => console.log('Cavi spare filtrati:', filteredData.length)}\n              />\n            ) : (\n              <Alert severity=\"info\" sx={{ mb: 2 }}>\n                Nessun cavo SPARE trovato. I cavi marcati come SPARE appariranno qui.\n              </Alert>\n            )}\n          </Box>\n\n          {/* Rimossa sezione Debug */}\n\n          {/* Dialogo dei dettagli del cavo */}\n          {renderDetailsDialog()}\n\n          {/* Dialogo per l'eliminazione dei cavi */}\n          <Dialog\n            open={openEliminaCavoDialog}\n            onClose={() => setOpenEliminaCavoDialog(false)}\n            fullWidth\n            maxWidth=\"md\"\n          >\n            <PosaCaviCollegamenti\n              cantiereId={cantiereId}\n              onSuccess={(message) => {\n                // Chiudi il dialogo\n                setOpenEliminaCavoDialog(false);\n\n                // Se c'è un messaggio, è un'operazione completata con successo\n                if (message) {\n                  // Mostra un messaggio di successo\n                  console.log('Operazione completata:', message);\n                  // Mostra un messaggio di successo con Snackbar\n                  showNotification(message, 'success');\n                  // Ricarica i dati dopo un ritardo per dare tempo al database di aggiornarsi\n                  setTimeout(() => {\n                    console.log('Ricaricamento dati dopo operazione...');\n                    try {\n                      // Ricarica i dati invece di ricaricare la pagina\n                      fetchCavi(true);\n                    } catch (error) {\n                      console.error('Errore durante il ricaricamento dei dati:', error);\n                      // Se fallisce, prova a ricaricare la pagina\n                      window.location.reload();\n                    }\n                  }, 1000);\n                } else {\n                  // È un'operazione annullata, non mostrare messaggi\n                  console.log('Operazione annullata dall\\'utente');\n                }\n              }}\n              onError={(message) => {\n                // Mostra un messaggio di errore\n                console.error('Errore durante l\\'eliminazione del cavo:', message);\n                // Mostra un alert all'utente\n                alert(`Errore: ${message}`);\n                // Chiudi il dialogo\n                setOpenEliminaCavoDialog(false);\n                // Ricarica comunque i dati per assicurarsi che la vista sia aggiornata\n                fetchCavi();\n              }}\n              initialOption=\"eliminaCavo\"\n            />\n          </Dialog>\n\n          {/* Dialogo per la modifica dei cavi */}\n          {/* Log del cantiereId prima di aprire il dialog */}\n          {openModificaCavoDialog && console.log('VisualizzaCaviPage - cantiereId prima di aprire il dialog:', cantiereId)}\n\n          <Dialog\n            open={openModificaCavoDialog}\n            onClose={() => setOpenModificaCavoDialog(false)}\n            fullWidth\n            maxWidth=\"sm\"\n          >\n            <PosaCaviCollegamenti\n              cantiereId={cantiereId}\n              onSuccess={(message) => {\n                // Chiudi il dialogo\n                setOpenModificaCavoDialog(false);\n\n                // Se c'è un messaggio, è un'operazione completata con successo\n                if (message) {\n                  // Mostra un messaggio di successo\n                  console.log('Operazione completata:', message);\n                  // Mostra un messaggio di successo con Snackbar\n                  showNotification(message, 'success');\n                  // Ricarica i dati immediatamente\n                  console.log('Ricaricamento dati dopo operazione...');\n                  // Ricarica i dati con un ritardo per dare tempo al database di aggiornarsi\n                  setTimeout(() => {\n                    try {\n                      fetchCavi(true);\n                    } catch (error) {\n                      console.error('Errore durante il ricaricamento dei dati:', error);\n                      // Se fallisce, prova a ricaricare la pagina\n                      window.location.reload();\n                    }\n                  }, 1000);\n                } else {\n                  // È un'operazione annullata, non mostrare messaggi\n                  console.log('Operazione annullata dall\\'utente');\n                }\n              }}\n              onError={(message) => {\n                // Mostra un messaggio di errore\n                console.error('Errore durante la modifica del cavo:', message);\n                // Mostra un alert all'utente\n                alert(`Errore: ${message}`);\n                // Chiudi il dialogo\n                setOpenModificaCavoDialog(false);\n                // Ricarica comunque i dati per assicurarsi che la vista sia aggiornata\n                console.log('Ricaricamento dati dopo errore...');\n                fetchCavi(true);\n              }}\n              initialOption=\"modificaCavo\"\n            />\n          </Dialog>\n\n          {/* Dialogo per l'aggiunta di un nuovo cavo */}\n          <Dialog\n            open={openAggiungiCavoDialog}\n            onClose={() => setOpenAggiungiCavoDialog(false)}\n            fullWidth\n            maxWidth=\"sm\"\n          >\n            <DialogTitle sx={{ pb: 1 }}>Aggiungi Nuovo Cavo</DialogTitle>\n            <DialogContent sx={{ pt: 0, pb: 1 }}>\n              <Box sx={{ mt: 0 }}>\n                <CavoForm\n                  mode=\"add\"\n                  cantiereId={cantiereId}\n                  onSubmit={async (validatedData) => {\n                    try {\n                      await caviService.addCavo(cantiereId, validatedData);\n                      return true;\n                    } catch (error) {\n                      throw error;\n                    }\n                  }}\n                  onSuccess={(message) => {\n                    // Chiudi il dialogo\n                    setOpenAggiungiCavoDialog(false);\n                    // Mostra un messaggio di successo con Snackbar\n                    showNotification(message, 'success');\n                    // Ricarica i dati dopo un ritardo per dare tempo al database di aggiornarsi\n                    setTimeout(() => {\n                      console.log('Ricaricamento dati dopo operazione...');\n                      try {\n                        // Ricarica i dati in modalità silenziosa per evitare il \"blink\" della pagina\n                        fetchCavi(true);\n                      } catch (error) {\n                        console.error('Errore durante il ricaricamento dei dati:', error);\n                        // Se fallisce, prova a ricaricare la pagina immediatamente\n                        console.log('Tentativo di ricaricamento della pagina...');\n                        window.location.reload();\n                      }\n                    }, 1000);\n                  }}\n                  onError={(message) => {\n                    // Mostra un messaggio di errore\n                    console.error('Errore durante l\\'aggiunta del cavo:', message);\n                    // Mostra un messaggio di errore con Snackbar\n                    showNotification(`Errore: ${message}`, 'error');\n                  }}\n                  isDialog={true}\n                  onCancel={() => setOpenAggiungiCavoDialog(false)}\n                />\n              </Box>\n            </DialogContent>\n            {/* No DialogActions needed here as AggiungiCavoForm has its own buttons */}\n          </Dialog>\n\n          {/* Snackbar per le notifiche */}\n          <Snackbar\n            open={notification.open}\n            autoHideDuration={4000}\n            onClose={handleCloseNotification}\n            anchorOrigin={{ vertical: 'bottom', horizontal: 'center' }}\n          >\n            <Alert onClose={handleCloseNotification} severity={notification.severity} sx={{ width: '100%' }}>\n              {notification.message}\n            </Alert>\n          </Snackbar>\n        </Box>\n      )}\n    </Box>\n  );\n};\n\nexport default VisualizzaCaviPage;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAClD,SACEC,GAAG,EACHC,UAAU,EACVC,KAAK,EACLC,MAAM,EACNC,IAAI,EACJC,IAAI,EACJC,WAAW,EACXC,KAAK,EACLC,UAAU,EACVC,IAAI,EACJC,gBAAgB,EAChBC,cAAc,EACdC,MAAM,EACNC,WAAW,EACXC,aAAa,EACbC,aAAa,EACbC,QAAQ,QACH,eAAe;AACtB,OAAOC,QAAQ,MAAM,0BAA0B;AAC/C,OAAOC,WAAW,MAAM,6BAA6B;AACrD,SAASC,WAAW,QAAQ,kBAAkB;AAC9C,SAASC,OAAO,QAAQ,2BAA2B;AACnD,SAASC,gBAAgB,QAAQ,6BAA6B;AAC9D,OAAOC,oBAAoB,MAAM,4CAA4C;AAC7E,OAAOC,WAAW,MAAM,4BAA4B;AACpD,OAAOC,QAAQ,MAAM,gCAAgC;AACrD,SAASC,2BAA2B,QAAQ,6BAA6B;AACzE,OAAOC,mBAAmB,MAAM,2CAA2C;AAC3E,OAAO,gBAAgB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAExB,MAAMC,kBAAkB,GAAGA,CAAA,KAAM;EAAAC,EAAA;EAAA,IAAAC,YAAA,EAAAC,aAAA,EAAAC,aAAA;EAC/B,MAAM;IAAEC,eAAe;IAAEC;EAAK,CAAC,GAAGf,OAAO,CAAC,CAAC;EAC3C,MAAM;IAAEgB,qBAAqB;IAAEC,wBAAwB;IAAEC,sBAAsB;IAAEC,yBAAyB;IAAEC,sBAAsB;IAAEC;EAA0B,CAAC,GAAGpB,gBAAgB,CAAC,CAAC;EACpL,MAAMqB,QAAQ,GAAGvB,WAAW,CAAC,CAAC;EAC9B,MAAM,CAACwB,UAAU,EAAEC,aAAa,CAAC,GAAG9C,QAAQ,CAAC,IAAI,CAAC;EAClD,MAAM,CAAC+C,YAAY,EAAEC,eAAe,CAAC,GAAGhD,QAAQ,CAAC,EAAE,CAAC;EACpD,MAAM,CAACiD,UAAU,EAAEC,aAAa,CAAC,GAAGlD,QAAQ,CAAC,EAAE,CAAC;EAChD,MAAM,CAACmD,SAAS,EAAEC,YAAY,CAAC,GAAGpD,QAAQ,CAAC,EAAE,CAAC;EAC9C,MAAM,CAACqD,OAAO,EAAEC,UAAU,CAAC,GAAGtD,QAAQ,CAAC,IAAI,CAAC;EAC5C,MAAM,CAACuD,KAAK,EAAEC,QAAQ,CAAC,GAAGxD,QAAQ,CAAC,IAAI,CAAC;EACxC;EACA,MAAM,CAACyD,YAAY,EAAEC,eAAe,CAAC,GAAG1D,QAAQ,CAAC;IAAE2D,IAAI,EAAE,KAAK;IAAEC,OAAO,EAAE,EAAE;IAAEC,QAAQ,EAAE;EAAU,CAAC,CAAC;EACnG;;EAEA;EACA,MAAM,CAACC,YAAY,EAAEC,eAAe,CAAC,GAAG/D,QAAQ,CAAC,IAAI,CAAC;EACtD,MAAM,CAACgE,iBAAiB,EAAEC,oBAAoB,CAAC,GAAGjE,QAAQ,CAAC,KAAK,CAAC;;EAEjE;EACA,MAAM,CAACkE,KAAK,EAAEC,QAAQ,CAAC,GAAGnE,QAAQ,CAAC;IACjCoE,MAAM,EAAE;MAAEC,WAAW,EAAE,CAAC;MAAEC,UAAU,EAAE,CAAC;MAAEC,WAAW,EAAE;IAAE,CAAC;IACzDC,SAAS,EAAE;MAAEC,oBAAoB,EAAE,CAAC;MAAEC,kBAAkB,EAAE,CAAC;MAAEC,yBAAyB,EAAE;IAAE,CAAC;IAC3FC,KAAK,EAAE;EACT,CAAC,CAAC;EACF,MAAM,CAACC,YAAY,EAAEC,eAAe,CAAC,GAAG9E,QAAQ,CAAC,KAAK,CAAC;;EAEvD;;EAEA;EACA,MAAM+E,sBAAsB,GAAGA,CAAA,KAAM;IACnC;IACAC,qBAAqB,CAAC,CAAC,YAAY,EAAE,eAAe,EAAE,UAAU,CAAC,CAAC;EACpE,CAAC;;EAED;EACA,MAAM,CAACC,OAAO,EAAEC,UAAU,CAAC,GAAGlF,QAAQ,CAAC;IACrCmF,mBAAmB,EAAE,EAAE;IACvBC,SAAS,EAAE,EAAE;IACbC,OAAO,EAAE,EAAE;IACXC,UAAU,EAAE;EACd,CAAC,CAAC;;EAEF;EACA,MAAM,CAACC,kBAAkB,EAAEP,qBAAqB,CAAC,GAAGhF,QAAQ,CAAC,EAAE,CAAC;EAChE,MAAM,CAACwF,aAAa,EAAEC,gBAAgB,CAAC,GAAGzF,QAAQ,CAAC,EAAE,CAAC;;EAEtD;;EAEA;EACA;EACA,MAAM0F,SAAS,GAAG,MAAAA,CAAOC,aAAa,GAAG,KAAK,KAAK;IACjD,IAAI;MACF,IAAI,CAACA,aAAa,EAAE;QAClBrC,UAAU,CAAC,IAAI,CAAC;MAClB;MACAsC,OAAO,CAACC,GAAG,CAAC,gCAAgC,EAAEhD,UAAU,CAAC;;MAEzD;MACA,IAAI,CAACA,UAAU,EAAE;QACf+C,OAAO,CAACrC,KAAK,CAAC,mCAAmC,EAAEV,UAAU,CAAC;QAC9DW,QAAQ,CAAC,wDAAwD,CAAC;QAClEF,UAAU,CAAC,KAAK,CAAC;QACjB;MACF;;MAEA;MACA,IAAIwC,eAAe,GAAGjD,UAAU;MAChC,IAAI,CAACiD,eAAe,EAAE;QACpBA,eAAe,GAAGC,YAAY,CAACC,OAAO,CAAC,oBAAoB,CAAC;QAC5DJ,OAAO,CAACC,GAAG,CAAC,qCAAqC,EAAEC,eAAe,CAAC;QACnE,IAAI,CAACA,eAAe,EAAE;UACpBF,OAAO,CAACrC,KAAK,CAAC,2CAA2C,CAAC;UAC1DC,QAAQ,CAAC,8CAA8C,CAAC;UACxDF,UAAU,CAAC,KAAK,CAAC;UACjB;QACF;MACF;;MAEA;MACAsC,OAAO,CAACC,GAAG,CAAC,0CAA0C,CAAC;MACvD,IAAII,MAAM,GAAG,EAAE;MACf,IAAI;QACFA,MAAM,GAAG,MAAMxE,WAAW,CAACyE,OAAO,CAACJ,eAAe,EAAE,CAAC,EAAEb,OAAO,CAAC;QAC/DW,OAAO,CAACC,GAAG,CAAC,uBAAuB,EAAEI,MAAM,GAAGA,MAAM,CAACE,MAAM,GAAG,CAAC,CAAC;MAClE,CAAC,CAAC,OAAOC,WAAW,EAAE;QACpBR,OAAO,CAACrC,KAAK,CAAC,yCAAyC,EAAE6C,WAAW,CAAC;QACrE;QACAH,MAAM,GAAG,EAAE;MACb;;MAEA;MACA,IAAIA,MAAM,IAAIA,MAAM,CAACE,MAAM,GAAG,CAAC,EAAE;QAC/B,MAAME,kBAAkB,GAAGJ,MAAM,CAACK,MAAM,CAACC,IAAI,IAAIA,IAAI,CAACC,sBAAsB,KAAK,CAAC,CAAC;QACnF,IAAIH,kBAAkB,CAACF,MAAM,GAAG,CAAC,EAAE;UACjCP,OAAO,CAACrC,KAAK,CAAC,wEAAwE,EAAE8C,kBAAkB,CAAC;QAC7G;MACF;MAEAnD,aAAa,CAAC+C,MAAM,IAAI,EAAE,CAAC;;MAE3B;MACA,IAAIQ,KAAK,GAAG,EAAE;MACd,IAAI;QACFb,OAAO,CAACC,GAAG,CAAC,iDAAiD,CAAC;QAC9DY,KAAK,GAAG,MAAMhF,WAAW,CAACiF,YAAY,CAACZ,eAAe,CAAC;QACvDF,OAAO,CAACC,GAAG,CAAC,4CAA4C,EAAEY,KAAK,GAAGA,KAAK,CAACN,MAAM,GAAG,CAAC,CAAC;QACnF,IAAIM,KAAK,IAAIA,KAAK,CAACN,MAAM,GAAG,CAAC,EAAE;UAC7BP,OAAO,CAACC,GAAG,CAAC,mBAAmB,EAAEY,KAAK,CAAC,CAAC,CAAC,CAAC;QAC5C;MACF,CAAC,CAAC,OAAOE,UAAU,EAAE;QACnBf,OAAO,CAACrC,KAAK,CAAC,8DAA8D,EAAEoD,UAAU,CAAC;QACzF;QACA,IAAI;UACFf,OAAO,CAACC,GAAG,CAAC,kCAAkC,CAAC;UAC/CY,KAAK,GAAG,MAAMhF,WAAW,CAACyE,OAAO,CAACJ,eAAe,EAAE,CAAC,CAAC;UACrDF,OAAO,CAACC,GAAG,CAAC,0CAA0C,EAAEY,KAAK,GAAGA,KAAK,CAACN,MAAM,GAAG,CAAC,CAAC;QACnF,CAAC,CAAC,OAAOS,aAAa,EAAE;UACtBhB,OAAO,CAACrC,KAAK,CAAC,mCAAmC,EAAEqD,aAAa,CAAC;UACjE;UACAH,KAAK,GAAG,EAAE;QACZ;MACF;MACArD,YAAY,CAACqD,KAAK,IAAI,EAAE,CAAC;;MAEzB;MACA,IAAI;QACFb,OAAO,CAACC,GAAG,CAAC,4BAA4B,CAAC;QACzC,MAAMgB,SAAS,GAAG,MAAMpF,WAAW,CAACqF,YAAY,CAAChB,eAAe,CAAC;QACjEF,OAAO,CAACC,GAAG,CAAC,uBAAuB,EAAEgB,SAAS,CAAC;;QAE/C;QACA,IAAI,CAACA,SAAS,IAAI,OAAOA,SAAS,KAAK,QAAQ,EAAE;UAC/CjB,OAAO,CAACrC,KAAK,CAAC,yBAAyB,EAAEsD,SAAS,CAAC;UACnD;UACA1C,QAAQ,CAAC;YACPC,MAAM,EAAE;cAAEC,WAAW,EAAE,CAAC;cAAEC,UAAU,EAAE,CAAC;cAAEC,WAAW,EAAE;YAAE,CAAC;YACzDC,SAAS,EAAE;cAAEC,oBAAoB,EAAE,CAAC;cAAEC,kBAAkB,EAAE,CAAC;cAAEC,yBAAyB,EAAE;YAAE,CAAC;YAC3FC,KAAK,EAAE;UACT,CAAC,CAAC;QACJ,CAAC,MAAM;UACL;UACA,MAAMmC,UAAU,GAAG;YACjB3C,MAAM,EAAEyC,SAAS,CAACzC,MAAM,IAAI;cAAEC,WAAW,EAAE,CAAC;cAAEC,UAAU,EAAE,CAAC;cAAEC,WAAW,EAAE;YAAE,CAAC;YAC7EC,SAAS,EAAEqC,SAAS,CAACrC,SAAS,IAAI;cAAEC,oBAAoB,EAAE,CAAC;cAAEC,kBAAkB,EAAE,CAAC;cAAEC,yBAAyB,EAAE;YAAE,CAAC;YAClHC,KAAK,EAAEiC,SAAS,CAACjC,KAAK,IAAI;UAC5B,CAAC;UACDT,QAAQ,CAAC4C,UAAU,CAAC;QACtB;MACF,CAAC,CAAC,OAAOC,UAAU,EAAE;QACnBpB,OAAO,CAACrC,KAAK,CAAC,2CAA2C,EAAEyD,UAAU,CAAC;QACtE;QACA7C,QAAQ,CAAC;UACPC,MAAM,EAAE;YAAEC,WAAW,EAAE,CAAC;YAAEC,UAAU,EAAE,CAAC;YAAEC,WAAW,EAAE;UAAE,CAAC;UACzDC,SAAS,EAAE;YAAEC,oBAAoB,EAAE,CAAC;YAAEC,kBAAkB,EAAE,CAAC;YAAEC,yBAAyB,EAAE;UAAE,CAAC;UAC3FC,KAAK,EAAE;QACT,CAAC,CAAC;MACJ;;MAEA;MACApB,QAAQ,CAAC,EAAE,CAAC;IACd,CAAC,CAAC,OAAOD,KAAK,EAAE;MACdqC,OAAO,CAACrC,KAAK,CAAC,2CAA2C,EAAEA,KAAK,CAAC;MACjEC,QAAQ,CAAC,oCAAoCD,KAAK,CAACK,OAAO,IAAI,oBAAoB,EAAE,CAAC;;MAErF;MACAqD,UAAU,CAAC,MAAM;QACf;QACA,IAAIC,QAAQ,CAACC,IAAI,CAACC,WAAW,CAACC,QAAQ,CAAC,iCAAiC,CAAC,EAAE;UACzEzB,OAAO,CAACC,GAAG,CAAC,gEAAgE,CAAC;UAC7EyB,MAAM,CAACC,QAAQ,CAACC,MAAM,CAAC,CAAC;QAC1B;MACF,CAAC,EAAE,IAAI,CAAC,CAAC,CAAC;IACZ,CAAC,SAAS;MACR,IAAI,CAAC7B,aAAa,EAAE;QAClBrC,UAAU,CAAC,KAAK,CAAC;MACnB;IACF;EACF,CAAC;;EAED;EACArD,SAAS,CAAC,MAAM;IACd;IACA8E,sBAAsB,CAAC,CAAC;IAExB,MAAM0C,SAAS,GAAG,MAAAA,CAAA,KAAY;MAC5B,IAAI;QACF7B,OAAO,CAACC,GAAG,CAAC,wCAAwC,CAAC;;QAErD;QACA,MAAM6B,KAAK,GAAG3B,YAAY,CAACC,OAAO,CAAC,OAAO,CAAC;QAC3CJ,OAAO,CAACC,GAAG,CAAC,iBAAiB,EAAE,CAAC,CAAC6B,KAAK,CAAC;QACvC,IAAI,CAACA,KAAK,EAAE;UACVlE,QAAQ,CAAC,iDAAiD,CAAC;UAC3DF,UAAU,CAAC,KAAK,CAAC;UACjB;QACF;;QAEA;QACA,IAAIqE,kBAAkB,GAAG5B,YAAY,CAACC,OAAO,CAAC,oBAAoB,CAAC;QACnE,IAAI4B,oBAAoB,GAAG7B,YAAY,CAACC,OAAO,CAAC,sBAAsB,CAAC;QAEvEJ,OAAO,CAACC,GAAG,CAAC,wCAAwC,EAAE;UAAE8B,kBAAkB;UAAEC;QAAqB,CAAC,CAAC;QACnGhC,OAAO,CAACC,GAAG,CAAC,cAAc,EAAExD,IAAI,CAAC;;QAEjC;QACAuD,OAAO,CAACC,GAAG,CAAC,wCAAwC,CAAC;QACrD,KAAK,IAAIgC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG9B,YAAY,CAACI,MAAM,EAAE0B,CAAC,EAAE,EAAE;UAC5C,MAAMC,GAAG,GAAG/B,YAAY,CAAC+B,GAAG,CAACD,CAAC,CAAC;UAC/BjC,OAAO,CAACC,GAAG,CAAC,GAAGiC,GAAG,KAAK/B,YAAY,CAACC,OAAO,CAAC8B,GAAG,CAAC,EAAE,CAAC;QACrD;;QAEA;QACA,IAAI,CAAAzF,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAE0F,IAAI,MAAK,eAAe,EAAE;UAClCnC,OAAO,CAACC,GAAG,CAAC,6EAA6E,CAAC;;UAE1F;UACA,IAAIxD,IAAI,CAAC2F,WAAW,EAAE;YACpBpC,OAAO,CAACC,GAAG,CAAC,sCAAsC,EAAExD,IAAI,CAAC2F,WAAW,CAAC;YACrEL,kBAAkB,GAAGtF,IAAI,CAAC2F,WAAW,CAACC,QAAQ,CAAC,CAAC;YAChDL,oBAAoB,GAAGvF,IAAI,CAAC6F,aAAa,IAAI,YAAY7F,IAAI,CAAC2F,WAAW,EAAE;;YAE3E;YACAjC,YAAY,CAACoC,OAAO,CAAC,oBAAoB,EAAER,kBAAkB,CAAC;YAC9D5B,YAAY,CAACoC,OAAO,CAAC,sBAAsB,EAAEP,oBAAoB,CAAC;YAClEhC,OAAO,CAACC,GAAG,CAAC,uCAAuC,EAAE8B,kBAAkB,CAAC;UAC1E,CAAC,MAAM;YACL;YACA,IAAI;cACF/B,OAAO,CAACC,GAAG,CAAC,qEAAqE,CAAC;cAClF,MAAM6B,KAAK,GAAG3B,YAAY,CAACC,OAAO,CAAC,OAAO,CAAC;cAC3C,IAAI0B,KAAK,EAAE;gBACT;gBACA,MAAMU,SAAS,GAAGV,KAAK,CAACW,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC;gBACrC,MAAMC,MAAM,GAAGF,SAAS,CAACG,OAAO,CAAC,IAAI,EAAE,GAAG,CAAC,CAACA,OAAO,CAAC,IAAI,EAAE,GAAG,CAAC;gBAC9D,MAAMC,WAAW,GAAGC,kBAAkB,CAACC,IAAI,CAACJ,MAAM,CAAC,CAACD,KAAK,CAAC,EAAE,CAAC,CAACM,GAAG,CAACC,CAAC,IAAI;kBACrE,OAAO,GAAG,GAAG,CAAC,IAAI,GAAGA,CAAC,CAACC,UAAU,CAAC,CAAC,CAAC,CAACZ,QAAQ,CAAC,EAAE,CAAC,EAAEa,KAAK,CAAC,CAAC,CAAC,CAAC;gBAC9D,CAAC,CAAC,CAACC,IAAI,CAAC,EAAE,CAAC,CAAC;gBAEZ,MAAMC,OAAO,GAAGC,IAAI,CAACC,KAAK,CAACV,WAAW,CAAC;gBACvC5C,OAAO,CAACC,GAAG,CAAC,wBAAwB,EAAEmD,OAAO,CAAC;gBAE9C,IAAIA,OAAO,CAAChB,WAAW,EAAE;kBACvBpC,OAAO,CAACC,GAAG,CAAC,oCAAoC,EAAEmD,OAAO,CAAChB,WAAW,CAAC;kBACtEL,kBAAkB,GAAGqB,OAAO,CAAChB,WAAW,CAACC,QAAQ,CAAC,CAAC;kBACnD;kBACAL,oBAAoB,GAAG,YAAYoB,OAAO,CAAChB,WAAW,EAAE;;kBAExD;kBACAjC,YAAY,CAACoC,OAAO,CAAC,oBAAoB,EAAER,kBAAkB,CAAC;kBAC9D5B,YAAY,CAACoC,OAAO,CAAC,sBAAsB,EAAEP,oBAAoB,CAAC;kBAClEhC,OAAO,CAACC,GAAG,CAAC,uCAAuC,EAAE8B,kBAAkB,CAAC;gBAC1E;cACF;YACF,CAAC,CAAC,OAAOwB,CAAC,EAAE;cACVvD,OAAO,CAACrC,KAAK,CAAC,6CAA6C,EAAE4F,CAAC,CAAC;YACjE;UACF;QACF;;QAEA;QACA,IAAI,CAACxB,kBAAkB,IAAIA,kBAAkB,KAAK,WAAW,IAAIA,kBAAkB,KAAK,MAAM,EAAE;UAC9F/B,OAAO,CAACwD,IAAI,CAAC,6EAA6E,CAAC;UAC3F;UACAzB,kBAAkB,GAAG,GAAG,CAAC,CAAC;UAC1BC,oBAAoB,GAAG,gBAAgB;;UAEvC;UACA7B,YAAY,CAACoC,OAAO,CAAC,oBAAoB,EAAER,kBAAkB,CAAC;UAC9D5B,YAAY,CAACoC,OAAO,CAAC,sBAAsB,EAAEP,oBAAoB,CAAC;UAClEhC,OAAO,CAACC,GAAG,CAAC,iDAAiD,EAAE8B,kBAAkB,CAAC;QACpF;;QAEA;QACA,IAAI,CAACA,kBAAkB,EAAE;UACvBnE,QAAQ,CAAC,8DAA8D,CAAC;UACxEF,UAAU,CAAC,KAAK,CAAC;UACjB;QACF;;QAEA;QACA,MAAM+F,aAAa,GAAGC,QAAQ,CAAC3B,kBAAkB,EAAE,EAAE,CAAC;QACtD/B,OAAO,CAACC,GAAG,CAAC,kCAAkC,EAAEwD,aAAa,CAAC;QAC9D,IAAIE,KAAK,CAACF,aAAa,CAAC,EAAE;UACxB7F,QAAQ,CAAC,2BAA2BmE,kBAAkB,mCAAmC,CAAC;UAC1FrE,UAAU,CAAC,KAAK,CAAC;UACjB;QACF;;QAEA;QACAR,aAAa,CAACuG,aAAa,CAAC;QAC5BrG,eAAe,CAAC4E,oBAAoB,IAAI,YAAYyB,aAAa,EAAE,CAAC;;QAEpE;QACA,IAAI;UACFvE,eAAe,CAAC,IAAI,CAAC;UACrBc,OAAO,CAACC,GAAG,CAAC,4CAA4C,EAAEwD,aAAa,CAAC;UACxE,MAAMxC,SAAS,GAAG,MAAMpF,WAAW,CAACqF,YAAY,CAACuC,aAAa,CAAC;UAC/DzD,OAAO,CAACC,GAAG,CAAC,4BAA4B,EAAEgB,SAAS,CAAC;UACpD1C,QAAQ,CAAC0C,SAAS,CAAC;;UAEnB;UACA,IAAIA,SAAS,IAAIA,SAAS,CAACjC,KAAK,EAAE;YAChC,MAAMA,KAAK,GAAGiC,SAAS,CAACjC,KAAK,CAAC+D,GAAG,CAACa,IAAI,IAAIA,IAAI,CAACC,KAAK,CAAC,CAACnD,MAAM,CAACmD,KAAK,IAAIA,KAAK,KAAK,iBAAiB,CAAC;YAClGzE,qBAAqB,CAACJ,KAAK,CAAC;UAC9B;UAEA,IAAIiC,SAAS,IAAIA,SAAS,CAAC6C,SAAS,EAAE;YACpC,MAAMA,SAAS,GAAG7C,SAAS,CAAC6C,SAAS,CAACf,GAAG,CAACa,IAAI,IAAIA,IAAI,CAACpE,SAAS,CAAC,CAACkB,MAAM,CAACqD,IAAI,IAAIA,IAAI,KAAK,iBAAiB,CAAC;YAC5GlE,gBAAgB,CAACiE,SAAS,CAAC;UAC7B;UAEA5E,eAAe,CAAC,KAAK,CAAC;QACxB,CAAC,CAAC,OAAOkC,UAAU,EAAE;UACnBpB,OAAO,CAACrC,KAAK,CAAC,2CAA2C,EAAEyD,UAAU,CAAC;UACtElC,eAAe,CAAC,KAAK,CAAC;UACtB;QACF;;QAEA;QACAc,OAAO,CAACC,GAAG,CAAC,uCAAuC,EAAEwD,aAAa,CAAC;QACnE,IAAI;UACF;UACA,MAAMO,cAAc,GAAG,IAAIC,OAAO,CAAC,CAACC,CAAC,EAAEC,MAAM,KAAK;YAChD9C,UAAU,CAAC,MAAM8C,MAAM,CAAC,IAAIC,KAAK,CAAC,gDAAgD,CAAC,CAAC,EAAE,KAAK,CAAC,CAAC,CAAC;UAChG,CAAC,CAAC;;UAEF;UACApE,OAAO,CAACC,GAAG,CAAC,oDAAoD,EAAEZ,OAAO,CAAC;UAC1E,MAAMgF,WAAW,GAAGxI,WAAW,CAACyE,OAAO,CAACmD,aAAa,EAAE,CAAC,EAAEpE,OAAO,CAAC;UAClE,MAAMgB,MAAM,GAAG,MAAM4D,OAAO,CAACK,IAAI,CAAC,CAACD,WAAW,EAAEL,cAAc,CAAC,CAAC;UAEhEhE,OAAO,CAACC,GAAG,CAAC,uBAAuB,EAAEI,MAAM,CAAC;UAC5CL,OAAO,CAACC,GAAG,CAAC,gCAAgC,EAAEI,MAAM,GAAGA,MAAM,CAACE,MAAM,GAAG,CAAC,CAAC;UACzE,IAAIF,MAAM,IAAIA,MAAM,CAACE,MAAM,GAAG,CAAC,EAAE;YAC/BP,OAAO,CAACC,GAAG,CAAC,oBAAoB,EAAEI,MAAM,CAAC,CAAC,CAAC,CAAC;UAC9C,CAAC,MAAM;YACLL,OAAO,CAACwD,IAAI,CAAC,4CAA4C,EAAEC,aAAa,CAAC;UAC3E;UACAnG,aAAa,CAAC+C,MAAM,IAAI,EAAE,CAAC;QAC7B,CAAC,CAAC,OAAOkE,SAAS,EAAE;UAClBvE,OAAO,CAACrC,KAAK,CAAC,yCAAyC,EAAE4G,SAAS,CAAC;UACnEvE,OAAO,CAACrC,KAAK,CAAC,8BAA8B,EAAE;YAC5CK,OAAO,EAAEuG,SAAS,CAACvG,OAAO;YAC1BwG,MAAM,EAAED,SAAS,CAACC,MAAM;YACxBC,IAAI,EAAEF,SAAS,CAACE,IAAI;YACpBC,KAAK,EAAEH,SAAS,CAACG,KAAK;YACtBC,IAAI,EAAEJ,SAAS,CAACI,IAAI;YACpBC,IAAI,EAAEL,SAAS,CAACK,IAAI;YACpBC,QAAQ,EAAEN,SAAS,CAACM,QAAQ,GAAG;cAC7BL,MAAM,EAAED,SAAS,CAACM,QAAQ,CAACL,MAAM;cACjCM,UAAU,EAAEP,SAAS,CAACM,QAAQ,CAACC,UAAU;cACzCL,IAAI,EAAEF,SAAS,CAACM,QAAQ,CAACJ;YAC3B,CAAC,GAAG;UACN,CAAC,CAAC;;UAEF;UACAnH,aAAa,CAAC,EAAE,CAAC;UACjB0C,OAAO,CAACwD,IAAI,CAAC,sDAAsD,CAAC;;UAEpE;UACA5F,QAAQ,CAAC,2CAA2C2G,SAAS,CAACvG,OAAO,+CAA+C,CAAC;QACvH;;QAEA;QACAgC,OAAO,CAACC,GAAG,CAAC,sCAAsC,EAAEwD,aAAa,CAAC;QAClE,IAAI;UACF;UACA,MAAMO,cAAc,GAAG,IAAIC,OAAO,CAAC,CAACC,CAAC,EAAEC,MAAM,KAAK;YAChD9C,UAAU,CAAC,MAAM8C,MAAM,CAAC,IAAIC,KAAK,CAAC,+CAA+C,CAAC,CAAC,EAAE,KAAK,CAAC,CAAC,CAAC;UAC/F,CAAC,CAAC;;UAEF;UACApE,OAAO,CAACC,GAAG,CAAC,0CAA0C,CAAC;UACvD;UACA,MAAM8E,YAAY,GAAGlJ,WAAW,CAACyE,OAAO,CAACmD,aAAa,EAAE,CAAC,CAAC;UAC1D,MAAM5C,KAAK,GAAG,MAAMoD,OAAO,CAACK,IAAI,CAAC,CAACS,YAAY,EAAEf,cAAc,CAAC,CAAC;UAEhEhE,OAAO,CAACC,GAAG,CAAC,sBAAsB,EAAEY,KAAK,CAAC;UAC1Cb,OAAO,CAACC,GAAG,CAAC,+BAA+B,EAAEY,KAAK,GAAGA,KAAK,CAACN,MAAM,GAAG,CAAC,CAAC;UACtE,IAAIM,KAAK,IAAIA,KAAK,CAACN,MAAM,GAAG,CAAC,EAAE;YAC7BP,OAAO,CAACC,GAAG,CAAC,mBAAmB,EAAEY,KAAK,CAAC,CAAC,CAAC,CAAC;UAC5C,CAAC,MAAM;YACLb,OAAO,CAACwD,IAAI,CAAC,2CAA2C,EAAEC,aAAa,CAAC;UAC1E;UACAjG,YAAY,CAACqD,KAAK,IAAI,EAAE,CAAC;QAC3B,CAAC,CAAC,OAAOE,UAAU,EAAE;UACnBf,OAAO,CAACrC,KAAK,CAAC,wCAAwC,EAAEoD,UAAU,CAAC;UACnEf,OAAO,CAACrC,KAAK,CAAC,6BAA6B,EAAE;YAC3CK,OAAO,EAAE+C,UAAU,CAAC/C,OAAO;YAC3BwG,MAAM,EAAEzD,UAAU,CAACyD,MAAM;YACzBC,IAAI,EAAE1D,UAAU,CAAC0D,IAAI;YACrBC,KAAK,EAAE3D,UAAU,CAAC2D,KAAK;YACvBC,IAAI,EAAE5D,UAAU,CAAC4D,IAAI;YACrBC,IAAI,EAAE7D,UAAU,CAAC6D,IAAI;YACrBC,QAAQ,EAAE9D,UAAU,CAAC8D,QAAQ,GAAG;cAC9BL,MAAM,EAAEzD,UAAU,CAAC8D,QAAQ,CAACL,MAAM;cAClCM,UAAU,EAAE/D,UAAU,CAAC8D,QAAQ,CAACC,UAAU;cAC1CL,IAAI,EAAE1D,UAAU,CAAC8D,QAAQ,CAACJ;YAC5B,CAAC,GAAG;UACN,CAAC,CAAC;;UAEF;UACAjH,YAAY,CAAC,EAAE,CAAC;;UAEhB;UACA,IAAI,CAACG,KAAK,EAAE;YACVC,QAAQ,CAAC,0CAA0CmD,UAAU,CAAC/C,OAAO,+CAA+C,CAAC;UACvH;QACF;;QAEA;QACAN,UAAU,CAAC,KAAK,CAAC;MAEnB,CAAC,CAAC,OAAOsH,GAAG,EAAE;QAAA,IAAAC,aAAA,EAAAC,cAAA,EAAAC,cAAA,EAAAC,cAAA,EAAAC,cAAA,EAAAC,mBAAA;QACZtF,OAAO,CAACrC,KAAK,CAAC,kCAAkC,EAAEqH,GAAG,CAAC;QACtDhF,OAAO,CAACrC,KAAK,CAAC,2BAA2B,EAAE;UACzCK,OAAO,EAAEgH,GAAG,CAAChH,OAAO;UACpBwG,MAAM,EAAEQ,GAAG,CAACR,MAAM,MAAAS,aAAA,GAAID,GAAG,CAACH,QAAQ,cAAAI,aAAA,uBAAZA,aAAA,CAAcT,MAAM;UAC1CC,IAAI,EAAEO,GAAG,CAACP,IAAI,MAAAS,cAAA,GAAIF,GAAG,CAACH,QAAQ,cAAAK,cAAA,uBAAZA,cAAA,CAAcT,IAAI;UACpCC,KAAK,EAAEM,GAAG,CAACN;QACb,CAAC,CAAC;;QAEF;QACA,IAAIa,YAAY,GAAG,oBAAoB;QAEvC,IAAIP,GAAG,CAAChH,OAAO,IAAIgH,GAAG,CAAChH,OAAO,CAACyD,QAAQ,CAAC,wBAAwB,CAAC,EAAE;UACjE8D,YAAY,GAAGP,GAAG,CAAChH,OAAO;QAC5B,CAAC,MAAM,IAAIgH,GAAG,CAACR,MAAM,KAAK,GAAG,IAAIQ,GAAG,CAACR,MAAM,KAAK,GAAG,IACzC,EAAAW,cAAA,GAAAH,GAAG,CAACH,QAAQ,cAAAM,cAAA,uBAAZA,cAAA,CAAcX,MAAM,MAAK,GAAG,IAAI,EAAAY,cAAA,GAAAJ,GAAG,CAACH,QAAQ,cAAAO,cAAA,uBAAZA,cAAA,CAAcZ,MAAM,MAAK,GAAG,EAAE;UACtEe,YAAY,GAAG,mEAAmE;QACpF,CAAC,MAAM,KAAAF,cAAA,GAAIL,GAAG,CAACH,QAAQ,cAAAQ,cAAA,gBAAAC,mBAAA,GAAZD,cAAA,CAAcZ,IAAI,cAAAa,mBAAA,eAAlBA,mBAAA,CAAoBE,MAAM,EAAE;UACrC;UACAD,YAAY,GAAG,eAAeP,GAAG,CAACH,QAAQ,CAACJ,IAAI,CAACe,MAAM,EAAE;QAC1D,CAAC,MAAM,IAAIR,GAAG,CAACL,IAAI,KAAK,aAAa,EAAE;UACrC;UACAY,YAAY,GAAG,yEAAyE;QAC1F,CAAC,MAAM,IAAIP,GAAG,CAAChH,OAAO,EAAE;UACtBuH,YAAY,GAAGP,GAAG,CAAChH,OAAO;QAC5B;QAEAJ,QAAQ,CAAC,gCAAgC2H,YAAY,sBAAsB,CAAC;;QAE5E;QACAjI,aAAa,CAAC,EAAE,CAAC;QACjBE,YAAY,CAAC,EAAE,CAAC;MAClB,CAAC,SAAS;QACRE,UAAU,CAAC,KAAK,CAAC;MACnB;IACF,CAAC;IAEDmE,SAAS,CAAC,CAAC;EACb,CAAC,EAAE,CAACxC,OAAO,CAAC,CAAC,CAAC,CAAC;;EAEf;;EAEA;EACA,MAAMoG,iBAAiB,GAAI9E,IAAI,IAAK;IAClCxC,eAAe,CAACwC,IAAI,CAAC;IACrBtC,oBAAoB,CAAC,IAAI,CAAC;EAC5B,CAAC;;EAED;EACA,MAAMqH,kBAAkB,GAAGA,CAAA,KAAM;IAC/BrH,oBAAoB,CAAC,KAAK,CAAC;IAC3BF,eAAe,CAAC,IAAI,CAAC;EACvB,CAAC;;EAED;EACA,MAAMwH,uBAAuB,GAAGA,CAAA,KAAM;IACpC7H,eAAe,CAAC8H,IAAI,KAAK;MAAE,GAAGA,IAAI;MAAE7H,IAAI,EAAE;IAAM,CAAC,CAAC,CAAC;EACrD,CAAC;;EAED;EACA,MAAM8H,gBAAgB,GAAGA,CAAC7H,OAAO,EAAEC,QAAQ,GAAG,SAAS,KAAK;IAC1DH,eAAe,CAAC;MAAEC,IAAI,EAAE,IAAI;MAAEC,OAAO;MAAEC;IAAS,CAAC,CAAC;EACpD,CAAC;;EAED;;EAEA;;EAEA;;EAEA;EACA,MAAM6H,mBAAmB,GAAGA,CAAA,KAAM;IAChC,IAAI,CAAC5H,YAAY,EAAE,OAAO,IAAI;IAE9B,oBACEhC,OAAA,CAAChB,MAAM;MAAC6C,IAAI,EAAEK,iBAAkB;MAAC2H,OAAO,EAAEL,kBAAmB;MAACM,QAAQ,EAAC,IAAI;MAACC,SAAS;MAAAC,QAAA,gBACnFhK,OAAA,CAACf,WAAW;QAAA+K,QAAA,GAAC,iBACI,EAAChI,YAAY,CAACiI,OAAO;MAAA;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACzB,CAAC,eACdrK,OAAA,CAACd,aAAa;QAACoL,QAAQ;QAAAN,QAAA,eACrBhK,OAAA,CAACxB,IAAI;UAAC+L,SAAS;UAACC,OAAO,EAAE,CAAE;UAAAR,QAAA,gBACzBhK,OAAA,CAACxB,IAAI;YAACkJ,IAAI;YAAC+C,EAAE,EAAE,EAAG;YAACC,EAAE,EAAE,CAAE;YAAAV,QAAA,gBACvBhK,OAAA,CAAC3B,UAAU;cAACsM,OAAO,EAAC,WAAW;cAACC,YAAY;cAAAZ,QAAA,EAAC;YAAqB;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAY,CAAC,eAC/ErK,OAAA,CAAC5B,GAAG;cAACyM,EAAE,EAAE;gBAAEC,EAAE,EAAE;cAAE,CAAE;cAAAd,QAAA,gBACjBhK,OAAA,CAAC3B,UAAU;gBAACsM,OAAO,EAAC,OAAO;gBAAAX,QAAA,gBAAChK,OAAA;kBAAAgK,QAAA,EAAQ;gBAAQ;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,KAAC,EAACrI,YAAY,CAAC+I,OAAO,IAAI,KAAK;cAAA;gBAAAb,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAa,CAAC,eAClGrK,OAAA,CAAC3B,UAAU;gBAACsM,OAAO,EAAC,OAAO;gBAAAX,QAAA,gBAAChK,OAAA;kBAAAgK,QAAA,EAAQ;gBAAQ;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,KAAC,EAACrI,YAAY,CAACgJ,OAAO,IAAI,KAAK;cAAA;gBAAAd,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAa,CAAC,eAClGrK,OAAA,CAAC3B,UAAU;gBAACsM,OAAO,EAAC,OAAO;gBAAAX,QAAA,gBAAChK,OAAA;kBAAAgK,QAAA,EAAQ;gBAAU;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,KAAC,EAACrI,YAAY,CAACsB,SAAS,IAAI,KAAK;cAAA;gBAAA4G,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAa,CAAC,eACtGrK,OAAA,CAAC3B,UAAU;gBAACsM,OAAO,EAAC,OAAO;gBAAAX,QAAA,gBAAChK,OAAA;kBAAAgK,QAAA,EAAQ;gBAAO;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,KAAC,EAACrI,YAAY,CAACiJ,WAAW,IAAI,KAAK;cAAA;gBAAAf,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAa,CAAC,eACrGrK,OAAA,CAAC3B,UAAU;gBAACsM,OAAO,EAAC,OAAO;gBAAAX,QAAA,gBAAChK,OAAA;kBAAAgK,QAAA,EAAQ;gBAAc;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,KAAC,EAACrI,YAAY,CAACkJ,YAAY,IAAI,KAAK;cAAA;gBAAAhB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAa,CAAC,eAC7GrK,OAAA,CAAC3B,UAAU;gBAACsM,OAAO,EAAC,OAAO;gBAAAX,QAAA,gBAAChK,OAAA;kBAAAgK,QAAA,EAAQ;gBAAQ;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,KAAC,EAACrI,YAAY,CAACmJ,OAAO,IAAI,KAAK;cAAA;gBAAAjB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAa,CAAC,eAClGrK,OAAA,CAAC3B,UAAU;gBAACsM,OAAO,EAAC,OAAO;gBAAAX,QAAA,gBAAChK,OAAA;kBAAAgK,QAAA,EAAQ;gBAAG;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,KAAC,EAACrI,YAAY,CAACoJ,EAAE,IAAI,KAAK;cAAA;gBAAAlB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAa,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACrF,CAAC,eAENrK,OAAA,CAAC3B,UAAU;cAACsM,OAAO,EAAC,WAAW;cAACC,YAAY;cAAAZ,QAAA,EAAC;YAAQ;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAY,CAAC,eAClErK,OAAA,CAAC5B,GAAG;cAACyM,EAAE,EAAE;gBAAEC,EAAE,EAAE;cAAE,CAAE;cAAAd,QAAA,gBACjBhK,OAAA,CAAC3B,UAAU;gBAACsM,OAAO,EAAC,OAAO;gBAAAX,QAAA,gBAAChK,OAAA;kBAAAgK,QAAA,EAAQ;gBAAW;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,KAAC,EAACrI,YAAY,CAACqJ,mBAAmB,IAAI,KAAK;cAAA;gBAAAnB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAa,CAAC,eACjHrK,OAAA,CAAC3B,UAAU;gBAACsM,OAAO,EAAC,OAAO;gBAAAX,QAAA,gBAAChK,OAAA;kBAAAgK,QAAA,EAAQ;gBAAO;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,KAAC,EAACrI,YAAY,CAACsJ,eAAe,IAAI,KAAK;cAAA;gBAAApB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAa,CAAC,eACzGrK,OAAA,CAAC3B,UAAU;gBAACsM,OAAO,EAAC,OAAO;gBAAAX,QAAA,gBAAChK,OAAA;kBAAAgK,QAAA,EAAQ;gBAAY;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,KAAC,EAACrI,YAAY,CAACuJ,2BAA2B,IAAI,KAAK;cAAA;gBAAArB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAa,CAAC,eAC1HrK,OAAA,CAAC3B,UAAU;gBAACsM,OAAO,EAAC,OAAO;gBAAAX,QAAA,gBAAChK,OAAA;kBAAAgK,QAAA,EAAQ;gBAAa;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,KAAC,EAACrI,YAAY,CAACwJ,qBAAqB,IAAI,KAAK;cAAA;gBAAAtB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAa,CAAC,eACrHrK,OAAA,CAAC3B,UAAU;gBAACsM,OAAO,EAAC,OAAO;gBAAAX,QAAA,gBAAChK,OAAA;kBAAAgK,QAAA,EAAQ;gBAAQ;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,KAAC,EAACrI,YAAY,CAACyJ,gBAAgB,IAAI,KAAK;cAAA;gBAAAvB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAa,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACxG,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACF,CAAC,eAEPrK,OAAA,CAACxB,IAAI;YAACkJ,IAAI;YAAC+C,EAAE,EAAE,EAAG;YAACC,EAAE,EAAE,CAAE;YAAAV,QAAA,gBACvBhK,OAAA,CAAC3B,UAAU;cAACsM,OAAO,EAAC,WAAW;cAACC,YAAY;cAAAZ,QAAA,EAAC;YAAM;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAY,CAAC,eAChErK,OAAA,CAAC5B,GAAG;cAACyM,EAAE,EAAE;gBAAEC,EAAE,EAAE;cAAE,CAAE;cAAAd,QAAA,gBACjBhK,OAAA,CAAC3B,UAAU;gBAACsM,OAAO,EAAC,OAAO;gBAAAX,QAAA,gBAAChK,OAAA;kBAAAgK,QAAA,EAAQ;gBAAW;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,KAAC,EAACrI,YAAY,CAAC0J,iBAAiB,IAAI,KAAK;cAAA;gBAAAxB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAa,CAAC,eAC/GrK,OAAA,CAAC3B,UAAU;gBAACsM,OAAO,EAAC,OAAO;gBAAAX,QAAA,gBAAChK,OAAA;kBAAAgK,QAAA,EAAQ;gBAAO;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,KAAC,EAACrI,YAAY,CAAC2J,aAAa,IAAI,KAAK;cAAA;gBAAAzB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAa,CAAC,eACvGrK,OAAA,CAAC3B,UAAU;gBAACsM,OAAO,EAAC,OAAO;gBAAAX,QAAA,gBAAChK,OAAA;kBAAAgK,QAAA,EAAQ;gBAAY;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,KAAC,EAACrI,YAAY,CAAC4J,yBAAyB,IAAI,KAAK;cAAA;gBAAA1B,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAa,CAAC,eACxHrK,OAAA,CAAC3B,UAAU;gBAACsM,OAAO,EAAC,OAAO;gBAAAX,QAAA,gBAAChK,OAAA;kBAAAgK,QAAA,EAAQ;gBAAa;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,KAAC,EAACrI,YAAY,CAAC6J,mBAAmB,IAAI,KAAK;cAAA;gBAAA3B,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAa,CAAC,eACnHrK,OAAA,CAAC3B,UAAU;gBAACsM,OAAO,EAAC,OAAO;gBAAAX,QAAA,gBAAChK,OAAA;kBAAAgK,QAAA,EAAQ;gBAAQ;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,KAAC,EAACrI,YAAY,CAAC8J,cAAc,IAAI,KAAK;cAAA;gBAAA5B,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAa,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACtG,CAAC,eAENrK,OAAA,CAAC3B,UAAU;cAACsM,OAAO,EAAC,WAAW;cAACC,YAAY;cAAAZ,QAAA,EAAC;YAAa;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAY,CAAC,eACvErK,OAAA,CAAC5B,GAAG;cAACyM,EAAE,EAAE;gBAAEC,EAAE,EAAE;cAAE,CAAE;cAAAd,QAAA,gBACjBhK,OAAA,CAAC3B,UAAU;gBAACsM,OAAO,EAAC,OAAO;gBAAAX,QAAA,gBAAChK,OAAA;kBAAAgK,QAAA,EAAQ;gBAAc;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,KAAC,EAACrI,YAAY,CAAC+J,aAAa,IAAI,KAAK;cAAA;gBAAA7B,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAa,CAAC,eAC9GrK,OAAA,CAAC3B,UAAU;gBAACsM,OAAO,EAAC,OAAO;gBAAAX,QAAA,gBAAChK,OAAA;kBAAAgK,QAAA,EAAQ;gBAAgB;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,KAAC,EAACrI,YAAY,CAACgK,eAAe,IAAI,GAAG;cAAA;gBAAA9B,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAa,CAAC,eAChHrK,OAAA,CAAC3B,UAAU;gBAACsM,OAAO,EAAC,OAAO;gBAAAX,QAAA,gBAAChK,OAAA;kBAAAgK,QAAA,EAAQ;gBAAM;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,KAAC,EAACxK,2BAA2B,CAACmC,YAAY,CAACqB,mBAAmB,CAAC;cAAA;gBAAA6G,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAa,CAAC,eAChIrK,OAAA,CAAC3B,UAAU;gBAACsM,OAAO,EAAC,OAAO;gBAAAX,QAAA,gBAAChK,OAAA;kBAAAgK,QAAA,EAAQ;gBAAa;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,KAAC,EAACrI,YAAY,CAACiK,YAAY,IAAI,GAAG;cAAA;gBAAA/B,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAa,CAAC,eAC1GrK,OAAA,CAAC3B,UAAU;gBAACsM,OAAO,EAAC,OAAO;gBAAAX,QAAA,gBAAChK,OAAA;kBAAAgK,QAAA,EAAQ;gBAAO;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,KAAC,EAACrI,YAAY,CAACkK,SAAS,IAAI,KAAK;cAAA;gBAAAhC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAa,CAAC,eACnGrK,OAAA,CAAC3B,UAAU;gBAACsM,OAAO,EAAC,OAAO;gBAAAX,QAAA,gBAAChK,OAAA;kBAAAgK,QAAA,EAAQ;gBAAkB;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,KAAC,EAACrI,YAAY,CAACmK,iBAAiB,IAAI,KAAK;cAAA;gBAAAjC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAa,CAAC,eACtHrK,OAAA,CAAC3B,UAAU;gBAACsM,OAAO,EAAC,OAAO;gBAAAX,QAAA,gBAAChK,OAAA;kBAAAgK,QAAA,EAAQ;gBAAa;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,KAAC,EAACrI,YAAY,CAACoK,YAAY,IAAI,KAAK;cAAA;gBAAAlC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAa,CAAC,eAC5GrK,OAAA,CAAC3B,UAAU;gBAACsM,OAAO,EAAC,OAAO;gBAAAX,QAAA,gBAAChK,OAAA;kBAAAgK,QAAA,EAAQ;gBAAqB;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,KAAC,EAAC,IAAIgC,IAAI,CAACrK,YAAY,CAACsK,SAAS,CAAC,CAACC,cAAc,CAAC,CAAC;cAAA;gBAAArC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAa,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAChI,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACF,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACM,CAAC,eAChBrK,OAAA,CAACb,aAAa;QAAA6K,QAAA,eACZhK,OAAA,CAACzB,MAAM;UAACiO,OAAO,EAAEhD,kBAAmB;UAAAQ,QAAA,EAAC;QAAM;UAAAE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACvC,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACV,CAAC;EAEb,CAAC;;EAED;;EAEA;EACA,MAAMoC,gBAAgB,GAAGA,CAAA,KAAM;IAC7B;IACA,IAAI,CAACrK,KAAK,IAAI,CAACA,KAAK,CAACE,MAAM,IAAI,CAACF,KAAK,CAACM,SAAS,IAAI,CAACN,KAAK,CAACU,KAAK,EAAE;MAC/D,oBACE9C,OAAA,CAAC1B,KAAK;QAACuM,EAAE,EAAE;UAAEC,EAAE,EAAE,CAAC;UAAE4B,CAAC,EAAE;QAAE,CAAE;QAAA1C,QAAA,gBACzBhK,OAAA,CAAC3B,UAAU;UAACsM,OAAO,EAAC,IAAI;UAACC,YAAY;UAAAZ,QAAA,EAAC;QAAW;UAAAE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAY,CAAC,EAC7DtH,YAAY,gBACX/C,OAAA,CAACjB,cAAc;UAAAmL,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,gBAElBrK,OAAA,CAAC3B,UAAU;UAACsM,OAAO,EAAC,OAAO;UAAAX,QAAA,EAAC;QAA8B;UAAAE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAY,CACvE;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACI,CAAC;IAEZ;;IAEA;IACA,MAAM/H,MAAM,GAAGF,KAAK,CAACE,MAAM,IAAI;MAAEC,WAAW,EAAE,CAAC;MAAEC,UAAU,EAAE,CAAC;MAAEC,WAAW,EAAE;IAAE,CAAC;IAChF,MAAMC,SAAS,GAAGN,KAAK,CAACM,SAAS,IAAI;MAAEC,oBAAoB,EAAE,CAAC;MAAEC,kBAAkB,EAAE,CAAC;MAAEC,yBAAyB,EAAE;IAAE,CAAC;IACrH,MAAMC,KAAK,GAAGV,KAAK,CAACU,KAAK,IAAI,EAAE;IAE/B,oBACE9C,OAAA,CAAC1B,KAAK;MAACuM,EAAE,EAAE;QAAEC,EAAE,EAAE,CAAC;QAAE4B,CAAC,EAAE;MAAE,CAAE;MAAA1C,QAAA,gBACzBhK,OAAA,CAAC3B,UAAU;QAACsM,OAAO,EAAC,IAAI;QAACC,YAAY;QAAAZ,QAAA,EAAC;MAAW;QAAAE,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAY,CAAC,EAC7DtH,YAAY,gBACX/C,OAAA,CAACjB,cAAc;QAAAmL,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC,gBAElBrK,OAAA,CAACxB,IAAI;QAAC+L,SAAS;QAACC,OAAO,EAAE,CAAE;QAAAR,QAAA,gBACzBhK,OAAA,CAACxB,IAAI;UAACkJ,IAAI;UAAC+C,EAAE,EAAE,EAAG;UAACC,EAAE,EAAE,CAAE;UAAAV,QAAA,gBACvBhK,OAAA,CAAC3B,UAAU;YAACsM,OAAO,EAAC,WAAW;YAACC,YAAY;YAAAZ,QAAA,EAAC;UAAM;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAY,CAAC,eAChErK,OAAA,CAAC3B,UAAU;YAACsM,OAAO,EAAC,OAAO;YAAAX,QAAA,GAAC,eAAa,EAAC1H,MAAM,CAACC,WAAW,IAAI,CAAC;UAAA;YAAA2H,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAa,CAAC,eAC/ErK,OAAA,CAAC3B,UAAU;YAACsM,OAAO,EAAC,OAAO;YAAAX,QAAA,GAAC,cAAY,EAAC1H,MAAM,CAACE,UAAU,IAAI,CAAC;UAAA;YAAA0H,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAa,CAAC,eAC7ErK,OAAA,CAAC3B,UAAU;YAACsM,OAAO,EAAC,OAAO;YAAAX,QAAA,GAAC,eAAa,EAAC1H,MAAM,CAACG,WAAW,IAAI,CAAC;UAAA;YAAAyH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAa,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAE3E,CAAC,eAEPrK,OAAA,CAACxB,IAAI;UAACkJ,IAAI;UAAC+C,EAAE,EAAE,EAAG;UAACC,EAAE,EAAE,CAAE;UAAAV,QAAA,gBACvBhK,OAAA,CAAC3B,UAAU;YAACsM,OAAO,EAAC,WAAW;YAACC,YAAY;YAAAZ,QAAA,EAAC;UAAS;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAY,CAAC,eACnErK,OAAA,CAAC3B,UAAU;YAACsM,OAAO,EAAC,OAAO;YAAAX,QAAA,GAAC,iBAAe,EAAC,CAACtH,SAAS,CAACC,oBAAoB,IAAI,CAAC,EAAEgK,OAAO,CAAC,CAAC,CAAC;UAAA;YAAAzC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAa,CAAC,eAC1GrK,OAAA,CAAC3B,UAAU;YAACsM,OAAO,EAAC,OAAO;YAAAX,QAAA,GAAC,gBAAc,EAAC,CAACtH,SAAS,CAACE,kBAAkB,IAAI,CAAC,EAAE+J,OAAO,CAAC,CAAC,CAAC;UAAA;YAAAzC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAa,CAAC,eACvGrK,OAAA,CAAC5B,GAAG;YAACyM,EAAE,EAAE;cAAE+B,OAAO,EAAE,MAAM;cAAEC,UAAU,EAAE,QAAQ;cAAEC,EAAE,EAAE;YAAE,CAAE;YAAA9C,QAAA,gBACxDhK,OAAA,CAAC5B,GAAG;cAACyM,EAAE,EAAE;gBAAEkC,KAAK,EAAE,MAAM;gBAAEC,EAAE,EAAE;cAAE,CAAE;cAAAhD,QAAA,eAChChK,OAAA,CAACjB,cAAc;gBACb4L,OAAO,EAAC,aAAa;gBACrBsC,KAAK,EAAEvK,SAAS,CAACG,yBAAyB,IAAI,CAAE;gBAChDgI,EAAE,EAAE;kBAAEqC,MAAM,EAAE,EAAE;kBAAEC,YAAY,EAAE;gBAAE;cAAE;gBAAAjD,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACrC;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACC,CAAC,eACNrK,OAAA,CAAC5B,GAAG;cAACyM,EAAE,EAAE;gBAAEuC,QAAQ,EAAE;cAAG,CAAE;cAAApD,QAAA,eACxBhK,OAAA,CAAC3B,UAAU;gBAACsM,OAAO,EAAC,OAAO;gBAAC0C,KAAK,EAAC,gBAAgB;gBAAArD,QAAA,EAAE,GAAG,CAACtH,SAAS,CAACG,yBAAyB,IAAI,CAAC,EAAE8J,OAAO,CAAC,CAAC,CAAC;cAAG;gBAAAzC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAa;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC1H,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACF,CAAC,eAEPrK,OAAA,CAACxB,IAAI;UAACkJ,IAAI;UAAC+C,EAAE,EAAE,EAAG;UAACC,EAAE,EAAE,CAAE;UAAAV,QAAA,gBACvBhK,OAAA,CAAC3B,UAAU;YAACsM,OAAO,EAAC,WAAW;YAACC,YAAY;YAAAZ,QAAA,EAAC;UAAK;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAY,CAAC,eAC/DrK,OAAA,CAAC5B,GAAG;YAACyM,EAAE,EAAE;cAAE+B,OAAO,EAAE,MAAM;cAAEU,QAAQ,EAAE,MAAM;cAAEC,GAAG,EAAE;YAAE,CAAE;YAAAvD,QAAA,EACpDlH,KAAK,CAACuB,MAAM,GAAG,CAAC,GAAGvB,KAAK,CAAC+D,GAAG,CAAC,CAACc,KAAK,EAAE6F,KAAK,kBACzCxN,OAAA,CAACnB,IAAI;cAEH4O,KAAK,EAAE,GAAG9F,KAAK,CAACA,KAAK,IAAI,KAAK,KAAKA,KAAK,CAAC+F,KAAK,IAAI,CAAC,EAAG;cACtDC,IAAI,EAAC,OAAO;cACZnB,OAAO,EAAEA,CAAA,KAAM;gBACbpJ,UAAU,CAACsG,IAAI,KAAK;kBAClB,GAAGA,IAAI;kBACPrG,mBAAmB,EAAEsE,KAAK,CAACA,KAAK,KAAK,iBAAiB,GAAG,EAAE,GAAGA,KAAK,CAACA;gBACtE,CAAC,CAAC,CAAC;cACL;YAAE,GARG6F,KAAK;cAAAtD,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OASX,CACF,CAAC,gBACArK,OAAA,CAAC3B,UAAU;cAACsM,OAAO,EAAC,OAAO;cAAAX,QAAA,EAAC;YAAyB;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAY;UAClE;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACE,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACF,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CACP;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACI,CAAC;EAEZ,CAAC;EAED,oBACErK,OAAA,CAAC5B,GAAG;IAACwP,SAAS,EAAC,WAAW;IAAA5D,QAAA,EACvBzI,OAAO,gBACNvB,OAAA,CAAC5B,GAAG;MAACyM,EAAE,EAAE;QAAE+B,OAAO,EAAE,MAAM;QAAEiB,aAAa,EAAE,QAAQ;QAAEhB,UAAU,EAAE,QAAQ;QAAEC,EAAE,EAAE;MAAE,CAAE;MAAA9C,QAAA,gBACjFhK,OAAA,CAAClB,gBAAgB;QAAC6O,IAAI,EAAE;MAAG;QAAAzD,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC,eAC9BrK,OAAA,CAAC3B,UAAU;QAACwM,EAAE,EAAE;UAAEiC,EAAE,EAAE;QAAE,CAAE;QAAA9C,QAAA,EAAC;MAAmB;QAAAE,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAY,CAAC,eAC3DrK,OAAA,CAACzB,MAAM;QACLoM,OAAO,EAAC,UAAU;QAClB0C,KAAK,EAAC,SAAS;QACfb,OAAO,EAAEA,CAAA,KAAMhH,MAAM,CAACC,QAAQ,CAACC,MAAM,CAAC,CAAE;QACxCmF,EAAE,EAAE;UAAEiC,EAAE,EAAE;QAAE,CAAE;QAAA9C,QAAA,EACf;MAED;QAAAE,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAQ,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACN,CAAC,GACJ5I,KAAK,gBACPzB,OAAA,CAAC5B,GAAG;MAAA4L,QAAA,gBACFhK,OAAA,CAACrB,KAAK;QAACoD,QAAQ,EAAC,OAAO;QAAC8I,EAAE,EAAE;UAAEC,EAAE,EAAE;QAAE,CAAE;QAAAd,QAAA,GACnCvI,KAAK,EACLA,KAAK,CAAC8D,QAAQ,CAAC,eAAe,CAAC,iBAC9BvF,OAAA,CAAC3B,UAAU;UAACsM,OAAO,EAAC,OAAO;UAACE,EAAE,EAAE;YAAEiC,EAAE,EAAE;UAAE,CAAE;UAAA9C,QAAA,gBACxChK,OAAA;YAAAgK,QAAA,EAAQ;UAAa;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC,uEAC9B,eAAArK,OAAA;YAAAkK,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAK,CAAC,8CACoC,eAAArK,OAAA;YAAAgK,QAAA,EAAM;UAAa;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC,4CACtE;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAY,CACb;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACI,CAAC,eACRrK,OAAA,CAAC5B,GAAG;QAACyM,EAAE,EAAE;UAAE+B,OAAO,EAAE,MAAM;UAAEW,GAAG,EAAE;QAAE,CAAE;QAAAvD,QAAA,eACnChK,OAAA,CAACzB,MAAM;UACLoM,OAAO,EAAC,WAAW;UACnBiD,SAAS,EAAC,gBAAgB;UAC1BpB,OAAO,EAAEA,CAAA,KAAMhH,MAAM,CAACC,QAAQ,CAACC,MAAM,CAAC,CAAE;UAAAsE,QAAA,EACzC;QAED;UAAAE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACN,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,gBAENrK,OAAA,CAAC5B,GAAG;MAAA4L,QAAA,gBAIFhK,OAAA,CAAC5B,GAAG;QAACyM,EAAE,EAAE;UAAEiC,EAAE,EAAE;QAAE,CAAE;QAAA9C,QAAA,eACjBhK,OAAA,CAAC5B,GAAG;UAACyM,EAAE,EAAE;YAAE+B,OAAO,EAAE,MAAM;YAAEkB,cAAc,EAAE,eAAe;YAAEjB,UAAU,EAAE,QAAQ;YAAE/B,EAAE,EAAE;UAAE,CAAE;UAAAd,QAAA,gBACzFhK,OAAA,CAAC3B,UAAU;YAACsM,OAAO,EAAC,IAAI;YAAAX,QAAA,GAAC,cACX,EAAC7I,UAAU,CAACkD,MAAM,GAAG,CAAC,GAAG,IAAIlD,UAAU,CAACkD,MAAM,GAAG,GAAG,EAAE;UAAA;YAAA6F,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACxD,CAAC,eACbrK,OAAA,CAACzB,MAAM;YACLoM,OAAO,EAAC,UAAU;YAClBgD,IAAI,EAAC,OAAO;YACZnB,OAAO,EAAEA,CAAA,KAAMhH,MAAM,CAACC,QAAQ,CAACC,MAAM,CAAC,CAAE;YACxCqI,SAAS,EAAExM,OAAO,gBAAGvB,OAAA,CAAClB,gBAAgB;cAAC6O,IAAI,EAAE;YAAG;cAAAzD,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,gBAAGrK,OAAA,CAACV,WAAW;cAAA4K,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAE;YACtE2D,QAAQ,EAAEzM,OAAQ;YAAAyI,QAAA,EACnB;UAED;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACN;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,EAELlJ,UAAU,CAACkD,MAAM,GAAG,CAAC,gBACpBrE,OAAA,CAAC5B,GAAG;QAACyM,EAAE,EAAE;UAAEC,EAAE,EAAE;QAAE,CAAE;QAAAd,QAAA,GAEhBiE,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,aAAa,iBACrCnO,OAAA,CAAC5B,GAAG;UAACyM,EAAE,EAAE;YAAEC,EAAE,EAAE,CAAC;YAAE4B,CAAC,EAAE,CAAC;YAAE0B,OAAO,EAAE,SAAS;YAAEjB,YAAY,EAAE,CAAC;YAAEkB,QAAQ,EAAE,QAAQ;YAAEC,UAAU,EAAE,WAAW;YAAE1B,OAAO,EAAE;UAAO,CAAE;UAAA5C,QAAA,EACzHuE,MAAM,CAACC,IAAI,CAACrN,UAAU,CAAC,CAAC,CAAC,CAAC,CAAC0F,GAAG,CAACb,GAAG,iBACjChG,OAAA;YAAAgK,QAAA,GAAgBhE,GAAG,EAAC,IAAE,EAACmB,IAAI,CAACsH,SAAS,CAACtN,UAAU,CAAC,CAAC,CAAC,CAAC6E,GAAG,CAAC,CAAC;UAAA,GAA/CA,GAAG;YAAAkE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAkD,CAChE;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACC,CACN,eACDrK,OAAA,CAACF,mBAAmB;UAClB4O,IAAI,EAAEvN,UAAW;UACjBI,OAAO,EAAEA,OAAQ;UACjBoN,oBAAoB,EAAGC,YAAY,IAAK9K,OAAO,CAACC,GAAG,CAAC,uBAAuB,EAAE6K,YAAY,CAACvK,MAAM,CAAE;UAClGwK,iBAAiB,EAAE,EAAA1O,YAAA,GAAAgB,UAAU,CAAC,CAAC,CAAC,cAAAhB,YAAA,uBAAbA,YAAA,CAAe2O,mBAAmB,OAAA1O,aAAA,GAAIe,UAAU,CAAC,CAAC,CAAC,cAAAf,aAAA,uBAAbA,aAAA,CAAe2O,SAAS,OAAA1O,aAAA,GAAIc,UAAU,CAAC,CAAC,CAAC,cAAAd,aAAA,uBAAbA,aAAA,CAAe2O,GAAG;QAAC;UAAA9E,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACzG,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACC,CAAC,gBAENrK,OAAA,CAACrB,KAAK;QAACoD,QAAQ,EAAC,MAAM;QAAC8I,EAAE,EAAE;UAAEC,EAAE,EAAE;QAAE,CAAE;QAAAd,QAAA,EAAC;MAEtC;QAAAE,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAO,CACR,eAGDrK,OAAA,CAAC5B,GAAG;QAACyM,EAAE,EAAE;UAAEiC,EAAE,EAAE;QAAE,CAAE;QAAA9C,QAAA,gBACjBhK,OAAA,CAAC5B,GAAG;UAACyM,EAAE,EAAE;YAAE+B,OAAO,EAAE,MAAM;YAAEkB,cAAc,EAAE,eAAe;YAAEjB,UAAU,EAAE,QAAQ;YAAE/B,EAAE,EAAE;UAAE,CAAE;UAAAd,QAAA,gBACzFhK,OAAA,CAAC3B,UAAU;YAACsM,OAAO,EAAC,IAAI;YAAAX,QAAA,GAAC,aACZ,EAAC3I,SAAS,CAACgD,MAAM,GAAG,CAAC,GAAG,IAAIhD,SAAS,CAACgD,MAAM,GAAG,GAAG,EAAE;UAAA;YAAA6F,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACrD,CAAC,eACbrK,OAAA,CAACzB,MAAM;YACLoM,OAAO,EAAC,UAAU;YAClBgD,IAAI,EAAC,OAAO;YACZnB,OAAO,EAAEA,CAAA,KAAMhH,MAAM,CAACC,QAAQ,CAACC,MAAM,CAAC,CAAE;YACxCqI,SAAS,EAAExM,OAAO,gBAAGvB,OAAA,CAAClB,gBAAgB;cAAC6O,IAAI,EAAE;YAAG;cAAAzD,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,gBAAGrK,OAAA,CAACV,WAAW;cAAA4K,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAE;YACtE2D,QAAQ,EAAEzM,OAAQ;YAAAyI,QAAA,EACnB;UAED;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACN,CAAC,EACLhJ,SAAS,CAACgD,MAAM,GAAG,CAAC,gBACnBrE,OAAA,CAACF,mBAAmB;UAClB4O,IAAI,EAAErN,SAAU;UAChBE,OAAO,EAAEA,OAAQ;UACjBoN,oBAAoB,EAAGC,YAAY,IAAK9K,OAAO,CAACC,GAAG,CAAC,sBAAsB,EAAE6K,YAAY,CAACvK,MAAM;QAAE;UAAA6F,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAClG,CAAC,gBAEFrK,OAAA,CAACrB,KAAK;UAACoD,QAAQ,EAAC,MAAM;UAAC8I,EAAE,EAAE;YAAEC,EAAE,EAAE;UAAE,CAAE;UAAAd,QAAA,EAAC;QAEtC;UAAAE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAO,CACR;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACE,CAAC,EAKLT,mBAAmB,CAAC,CAAC,eAGtB5J,OAAA,CAAChB,MAAM;QACL6C,IAAI,EAAErB,qBAAsB;QAC5BqJ,OAAO,EAAEA,CAAA,KAAMpJ,wBAAwB,CAAC,KAAK,CAAE;QAC/CsJ,SAAS;QACTD,QAAQ,EAAC,IAAI;QAAAE,QAAA,eAEbhK,OAAA,CAACN,oBAAoB;UACnBqB,UAAU,EAAEA,UAAW;UACvBkO,SAAS,EAAGnN,OAAO,IAAK;YACtB;YACArB,wBAAwB,CAAC,KAAK,CAAC;;YAE/B;YACA,IAAIqB,OAAO,EAAE;cACX;cACAgC,OAAO,CAACC,GAAG,CAAC,wBAAwB,EAAEjC,OAAO,CAAC;cAC9C;cACA6H,gBAAgB,CAAC7H,OAAO,EAAE,SAAS,CAAC;cACpC;cACAqD,UAAU,CAAC,MAAM;gBACfrB,OAAO,CAACC,GAAG,CAAC,uCAAuC,CAAC;gBACpD,IAAI;kBACF;kBACAH,SAAS,CAAC,IAAI,CAAC;gBACjB,CAAC,CAAC,OAAOnC,KAAK,EAAE;kBACdqC,OAAO,CAACrC,KAAK,CAAC,2CAA2C,EAAEA,KAAK,CAAC;kBACjE;kBACA+D,MAAM,CAACC,QAAQ,CAACC,MAAM,CAAC,CAAC;gBAC1B;cACF,CAAC,EAAE,IAAI,CAAC;YACV,CAAC,MAAM;cACL;cACA5B,OAAO,CAACC,GAAG,CAAC,mCAAmC,CAAC;YAClD;UACF,CAAE;UACFmL,OAAO,EAAGpN,OAAO,IAAK;YACpB;YACAgC,OAAO,CAACrC,KAAK,CAAC,0CAA0C,EAAEK,OAAO,CAAC;YAClE;YACAqN,KAAK,CAAC,WAAWrN,OAAO,EAAE,CAAC;YAC3B;YACArB,wBAAwB,CAAC,KAAK,CAAC;YAC/B;YACAmD,SAAS,CAAC,CAAC;UACb,CAAE;UACFwL,aAAa,EAAC;QAAa;UAAAlF,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC5B;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACI,CAAC,EAIR3J,sBAAsB,IAAIoD,OAAO,CAACC,GAAG,CAAC,4DAA4D,EAAEhD,UAAU,CAAC,eAEhHf,OAAA,CAAChB,MAAM;QACL6C,IAAI,EAAEnB,sBAAuB;QAC7BmJ,OAAO,EAAEA,CAAA,KAAMlJ,yBAAyB,CAAC,KAAK,CAAE;QAChDoJ,SAAS;QACTD,QAAQ,EAAC,IAAI;QAAAE,QAAA,eAEbhK,OAAA,CAACN,oBAAoB;UACnBqB,UAAU,EAAEA,UAAW;UACvBkO,SAAS,EAAGnN,OAAO,IAAK;YACtB;YACAnB,yBAAyB,CAAC,KAAK,CAAC;;YAEhC;YACA,IAAImB,OAAO,EAAE;cACX;cACAgC,OAAO,CAACC,GAAG,CAAC,wBAAwB,EAAEjC,OAAO,CAAC;cAC9C;cACA6H,gBAAgB,CAAC7H,OAAO,EAAE,SAAS,CAAC;cACpC;cACAgC,OAAO,CAACC,GAAG,CAAC,uCAAuC,CAAC;cACpD;cACAoB,UAAU,CAAC,MAAM;gBACf,IAAI;kBACFvB,SAAS,CAAC,IAAI,CAAC;gBACjB,CAAC,CAAC,OAAOnC,KAAK,EAAE;kBACdqC,OAAO,CAACrC,KAAK,CAAC,2CAA2C,EAAEA,KAAK,CAAC;kBACjE;kBACA+D,MAAM,CAACC,QAAQ,CAACC,MAAM,CAAC,CAAC;gBAC1B;cACF,CAAC,EAAE,IAAI,CAAC;YACV,CAAC,MAAM;cACL;cACA5B,OAAO,CAACC,GAAG,CAAC,mCAAmC,CAAC;YAClD;UACF,CAAE;UACFmL,OAAO,EAAGpN,OAAO,IAAK;YACpB;YACAgC,OAAO,CAACrC,KAAK,CAAC,sCAAsC,EAAEK,OAAO,CAAC;YAC9D;YACAqN,KAAK,CAAC,WAAWrN,OAAO,EAAE,CAAC;YAC3B;YACAnB,yBAAyB,CAAC,KAAK,CAAC;YAChC;YACAmD,OAAO,CAACC,GAAG,CAAC,mCAAmC,CAAC;YAChDH,SAAS,CAAC,IAAI,CAAC;UACjB,CAAE;UACFwL,aAAa,EAAC;QAAc;UAAAlF,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC7B;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACI,CAAC,eAGTrK,OAAA,CAAChB,MAAM;QACL6C,IAAI,EAAEjB,sBAAuB;QAC7BiJ,OAAO,EAAEA,CAAA,KAAMhJ,yBAAyB,CAAC,KAAK,CAAE;QAChDkJ,SAAS;QACTD,QAAQ,EAAC,IAAI;QAAAE,QAAA,gBAEbhK,OAAA,CAACf,WAAW;UAAC4L,EAAE,EAAE;YAAEwE,EAAE,EAAE;UAAE,CAAE;UAAArF,QAAA,EAAC;QAAmB;UAAAE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAa,CAAC,eAC7DrK,OAAA,CAACd,aAAa;UAAC2L,EAAE,EAAE;YAAEyE,EAAE,EAAE,CAAC;YAAED,EAAE,EAAE;UAAE,CAAE;UAAArF,QAAA,eAClChK,OAAA,CAAC5B,GAAG;YAACyM,EAAE,EAAE;cAAEiC,EAAE,EAAE;YAAE,CAAE;YAAA9C,QAAA,eACjBhK,OAAA,CAACJ,QAAQ;cACP2P,IAAI,EAAC,KAAK;cACVxO,UAAU,EAAEA,UAAW;cACvByO,QAAQ,EAAE,MAAOC,aAAa,IAAK;gBACjC,IAAI;kBACF,MAAM9P,WAAW,CAAC+P,OAAO,CAAC3O,UAAU,EAAE0O,aAAa,CAAC;kBACpD,OAAO,IAAI;gBACb,CAAC,CAAC,OAAOhO,KAAK,EAAE;kBACd,MAAMA,KAAK;gBACb;cACF,CAAE;cACFwN,SAAS,EAAGnN,OAAO,IAAK;gBACtB;gBACAjB,yBAAyB,CAAC,KAAK,CAAC;gBAChC;gBACA8I,gBAAgB,CAAC7H,OAAO,EAAE,SAAS,CAAC;gBACpC;gBACAqD,UAAU,CAAC,MAAM;kBACfrB,OAAO,CAACC,GAAG,CAAC,uCAAuC,CAAC;kBACpD,IAAI;oBACF;oBACAH,SAAS,CAAC,IAAI,CAAC;kBACjB,CAAC,CAAC,OAAOnC,KAAK,EAAE;oBACdqC,OAAO,CAACrC,KAAK,CAAC,2CAA2C,EAAEA,KAAK,CAAC;oBACjE;oBACAqC,OAAO,CAACC,GAAG,CAAC,4CAA4C,CAAC;oBACzDyB,MAAM,CAACC,QAAQ,CAACC,MAAM,CAAC,CAAC;kBAC1B;gBACF,CAAC,EAAE,IAAI,CAAC;cACV,CAAE;cACFwJ,OAAO,EAAGpN,OAAO,IAAK;gBACpB;gBACAgC,OAAO,CAACrC,KAAK,CAAC,sCAAsC,EAAEK,OAAO,CAAC;gBAC9D;gBACA6H,gBAAgB,CAAC,WAAW7H,OAAO,EAAE,EAAE,OAAO,CAAC;cACjD,CAAE;cACF6N,QAAQ,EAAE,IAAK;cACfC,QAAQ,EAAEA,CAAA,KAAM/O,yBAAyB,CAAC,KAAK;YAAE;cAAAqJ,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAClD;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACC;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACO,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAEV,CAAC,eAGTrK,OAAA,CAACZ,QAAQ;QACPyC,IAAI,EAAEF,YAAY,CAACE,IAAK;QACxBgO,gBAAgB,EAAE,IAAK;QACvBhG,OAAO,EAAEJ,uBAAwB;QACjCqG,YAAY,EAAE;UAAEC,QAAQ,EAAE,QAAQ;UAAEC,UAAU,EAAE;QAAS,CAAE;QAAAhG,QAAA,eAE3DhK,OAAA,CAACrB,KAAK;UAACkL,OAAO,EAAEJ,uBAAwB;UAAC1H,QAAQ,EAAEJ,YAAY,CAACI,QAAS;UAAC8I,EAAE,EAAE;YAAEkC,KAAK,EAAE;UAAO,CAAE;UAAA/C,QAAA,EAC7FrI,YAAY,CAACG;QAAO;UAAAoI,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAChB;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACA,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACR;EACN;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACE,CAAC;AAEV,CAAC;AAACnK,EAAA,CAt5BID,kBAAkB;EAAA,QACYT,OAAO,EACyHC,gBAAgB,EACjKF,WAAW;AAAA;AAAA0Q,EAAA,GAHxBhQ,kBAAkB;AAw5BxB,eAAeA,kBAAkB;AAAC,IAAAgQ,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}