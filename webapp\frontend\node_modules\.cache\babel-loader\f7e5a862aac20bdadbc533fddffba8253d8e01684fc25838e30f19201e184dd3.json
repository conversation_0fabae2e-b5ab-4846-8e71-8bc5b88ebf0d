{"ast": null, "code": "import _extends from \"@babel/runtime/helpers/esm/extends\";\nimport * as React from 'react';\nimport { useRtl } from '@mui/system/RtlProvider';\nimport { usePickerPrivateContext } from \"./usePickerPrivateContext.js\";\nexport function useFieldOwnerState(parameters) {\n  const {\n    ownerState: pickerOwnerState\n  } = usePickerPrivateContext();\n  const isRtl = useRtl();\n  return React.useMemo(() => _extends({}, pickerOwnerState, {\n    isFieldDisabled: parameters.disabled ?? false,\n    isFieldReadOnly: parameters.readOnly ?? false,\n    isFieldRequired: parameters.required ?? false,\n    fieldDirection: isRtl ? 'rtl' : 'ltr'\n  }), [pickerOwnerState, parameters.disabled, parameters.readOnly, parameters.required, isRtl]);\n}", "map": {"version": 3, "names": ["_extends", "React", "useRtl", "usePickerPrivateContext", "useFieldOwnerState", "parameters", "ownerState", "pickerOwnerState", "isRtl", "useMemo", "isFieldDisabled", "disabled", "isFieldReadOnly", "readOnly", "isFieldRequired", "required", "fieldDirection"], "sources": ["C:/CMS/webapp/frontend/node_modules/@mui/x-date-pickers/esm/internals/hooks/useFieldOwnerState.js"], "sourcesContent": ["import _extends from \"@babel/runtime/helpers/esm/extends\";\nimport * as React from 'react';\nimport { useRtl } from '@mui/system/RtlProvider';\nimport { usePickerPrivateContext } from \"./usePickerPrivateContext.js\";\nexport function useFieldOwnerState(parameters) {\n  const {\n    ownerState: pickerOwnerState\n  } = usePickerPrivateContext();\n  const isRtl = useRtl();\n  return React.useMemo(() => _extends({}, pickerOwnerState, {\n    isFieldDisabled: parameters.disabled ?? false,\n    isFieldReadOnly: parameters.readOnly ?? false,\n    isFieldRequired: parameters.required ?? false,\n    fieldDirection: isRtl ? 'rtl' : 'ltr'\n  }), [pickerOwnerState, parameters.disabled, parameters.readOnly, parameters.required, isRtl]);\n}"], "mappings": "AAAA,OAAOA,QAAQ,MAAM,oCAAoC;AACzD,OAAO,KAAKC,KAAK,MAAM,OAAO;AAC9B,SAASC,MAAM,QAAQ,yBAAyB;AAChD,SAASC,uBAAuB,QAAQ,8BAA8B;AACtE,OAAO,SAASC,kBAAkBA,CAACC,UAAU,EAAE;EAC7C,MAAM;IACJC,UAAU,EAAEC;EACd,CAAC,GAAGJ,uBAAuB,CAAC,CAAC;EAC7B,MAAMK,KAAK,GAAGN,MAAM,CAAC,CAAC;EACtB,OAAOD,KAAK,CAACQ,OAAO,CAAC,MAAMT,QAAQ,CAAC,CAAC,CAAC,EAAEO,gBAAgB,EAAE;IACxDG,eAAe,EAAEL,UAAU,CAACM,QAAQ,IAAI,KAAK;IAC7CC,eAAe,EAAEP,UAAU,CAACQ,QAAQ,IAAI,KAAK;IAC7CC,eAAe,EAAET,UAAU,CAACU,QAAQ,IAAI,KAAK;IAC7CC,cAAc,EAAER,KAAK,GAAG,KAAK,GAAG;EAClC,CAAC,CAAC,EAAE,CAACD,gBAAgB,EAAEF,UAAU,CAACM,QAAQ,EAAEN,UAAU,CAACQ,QAAQ,EAAER,UAAU,CAACU,QAAQ,EAAEP,KAAK,CAAC,CAAC;AAC/F", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}