{"ast": null, "code": "import { formatDistance } from \"./km/_lib/formatDistance.js\";\nimport { formatLong } from \"./km/_lib/formatLong.js\";\nimport { formatRelative } from \"./km/_lib/formatRelative.js\";\nimport { localize } from \"./km/_lib/localize.js\";\nimport { match } from \"./km/_lib/match.js\";\n\n/**\n * @category Locales\n * @summary Khmer locale (Cambodian).\n * @language Khmer\n * @iso-639-2 khm\n * <AUTHOR> [@seanghay](https://github.com/seanghay)\n */\nexport const km = {\n  code: \"km\",\n  formatDistance: formatDistance,\n  formatLong: formatLong,\n  formatRelative: formatRelative,\n  localize: localize,\n  match: match,\n  options: {\n    weekStartsOn: 0 /* Sunday */,\n    firstWeekContainsDate: 1\n  }\n};\n\n// Fallback for modularized imports:\nexport default km;", "map": {"version": 3, "names": ["formatDistance", "formatLong", "formatRelative", "localize", "match", "km", "code", "options", "weekStartsOn", "firstWeekContainsDate"], "sources": ["C:/Users/<USER>/PycharmProjects/CMS/webapp/frontend/node_modules/date-fns/locale/km.js"], "sourcesContent": ["import { formatDistance } from \"./km/_lib/formatDistance.js\";\nimport { formatLong } from \"./km/_lib/formatLong.js\";\nimport { formatRelative } from \"./km/_lib/formatRelative.js\";\nimport { localize } from \"./km/_lib/localize.js\";\nimport { match } from \"./km/_lib/match.js\";\n\n/**\n * @category Locales\n * @summary Khmer locale (Cambodian).\n * @language Khmer\n * @iso-639-2 khm\n * <AUTHOR> [@seanghay](https://github.com/seanghay)\n */\nexport const km = {\n  code: \"km\",\n  formatDistance: formatDistance,\n  formatLong: formatLong,\n  formatRelative: formatRelative,\n  localize: localize,\n  match: match,\n  options: {\n    weekStartsOn: 0 /* Sunday */,\n    firstWeekContainsDate: 1,\n  },\n};\n\n// Fallback for modularized imports:\nexport default km;\n"], "mappings": "AAAA,SAASA,cAAc,QAAQ,6BAA6B;AAC5D,SAASC,UAAU,QAAQ,yBAAyB;AACpD,SAASC,cAAc,QAAQ,6BAA6B;AAC5D,SAASC,QAAQ,QAAQ,uBAAuB;AAChD,SAASC,KAAK,QAAQ,oBAAoB;;AAE1C;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO,MAAMC,EAAE,GAAG;EAChBC,IAAI,EAAE,IAAI;EACVN,cAAc,EAAEA,cAAc;EAC9BC,UAAU,EAAEA,UAAU;EACtBC,cAAc,EAAEA,cAAc;EAC9BC,QAAQ,EAAEA,QAAQ;EAClBC,KAAK,EAAEA,KAAK;EACZG,OAAO,EAAE;IACPC,YAAY,EAAE,CAAC,CAAC;IAChBC,qBAAqB,EAAE;EACzB;AACF,CAAC;;AAED;AACA,eAAeJ,EAAE", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}