{"ast": null, "code": "var _jsxFileName = \"C:\\\\CMS\\\\webapp\\\\frontend\\\\src\\\\pages\\\\cavi\\\\VisualizzaCaviPage.js\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect } from 'react';\nimport { Box, Typography, Paper, Button, Grid, Card, CardContent, Alert, IconButton, Chip, CircularProgress, LinearProgress, Dialog, DialogTitle, DialogContent, DialogActions, Snackbar, FormControl, InputLabel, Select, MenuItem, Stack } from '@mui/material';\nimport InfoIcon from '@mui/icons-material/Info';\nimport { Cable as CableIcon, CheckCircle as CheckCircleIcon, Schedule as ScheduleIcon, Link as LinkIcon, LinkOff as LinkOffIcon, Timeline as TimelineIcon, CheckBox as CheckBoxIcon, CheckBoxOutlineBlank as CheckBoxOutlineBlankIcon, Visibility as VisibilityIcon, Edit as EditIcon, Delete as DeleteIcon, SelectAll as SelectAllIcon, ContentCopy as CopyIcon, Settings as SettingsIcon, Verified as VerifiedIcon } from '@mui/icons-material';\nimport { useNavigate } from 'react-router-dom';\nimport { useAuth } from '../../context/AuthContext';\nimport { useGlobalContext } from '../../context/GlobalContext';\nimport PosaCaviCollegamenti from '../../components/cavi/PosaCaviCollegamenti';\nimport caviService from '../../services/caviService';\nimport parcoCaviService from '../../services/parcoCaviService';\nimport CavoForm from '../../components/cavi/CavoForm';\nimport { normalizeInstallationStatus } from '../../utils/validationUtils';\nimport CaviFilterableTable from '../../components/cavi/CaviFilterableTable';\nimport InserisciMetriDialog from '../../components/cavi/InserisciMetriDialog';\nimport ModificaBobinaDialog from '../../components/cavi/ModificaBobinaDialog';\nimport './CaviPage.css';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst VisualizzaCaviPage = () => {\n  _s();\n  var _caviAttivi$, _caviAttivi$2, _caviAttivi$3;\n  const {\n    isImpersonating,\n    user\n  } = useAuth();\n  const {\n    openEliminaCavoDialog,\n    setOpenEliminaCavoDialog,\n    openModificaCavoDialog,\n    setOpenModificaCavoDialog,\n    openAggiungiCavoDialog,\n    setOpenAggiungiCavoDialog\n  } = useGlobalContext();\n  const navigate = useNavigate();\n  const [cantiereId, setCantiereId] = useState(null);\n  const [cantiereName, setCantiereName] = useState('');\n  const [caviAttivi, setCaviAttivi] = useState([]);\n  const [caviSpare, setCaviSpare] = useState([]);\n  const [loading, setLoading] = useState(true);\n  const [error, setError] = useState(null);\n  // Stato per le notifiche\n  const [notification, setNotification] = useState({\n    open: false,\n    message: '',\n    severity: 'success'\n  });\n  // Rimosso stato viewMode\n\n  // Stato per il dialogo dei dettagli del cavo\n  const [selectedCavo, setSelectedCavo] = useState(null);\n  const [detailsDialogOpen, setDetailsDialogOpen] = useState(false);\n\n  // Stati per la selezione dei cavi\n  const [selectionEnabled, setSelectionEnabled] = useState(false);\n  const [selectedCaviAttivi, setSelectedCaviAttivi] = useState([]);\n  const [selectedCaviSpare, setSelectedCaviSpare] = useState([]);\n\n  // Stati per i dialoghi di azione sui pulsanti stato\n  const [inserisciMetriDialog, setInserisciMetriDialog] = useState({\n    open: false,\n    cavo: null,\n    loading: false\n  });\n  const [modificaBobinaDialog, setModificaBobinaDialog] = useState({\n    open: false,\n    cavo: null,\n    loading: false\n  });\n  const [bobineDisponibili, setBobineDisponibili] = useState([]);\n\n  // Stati per statistiche avanzate\n  const [statistics, setStatistics] = useState({\n    totaleCavi: 0,\n    caviInstallati: 0,\n    caviDaInstallare: 0,\n    caviInCorso: 0,\n    caviCollegati: 0,\n    caviNonCollegati: 0,\n    caviCertificati: 0,\n    caviNonCertificati: 0,\n    percentualeInstallazione: 0,\n    percentualeCollegamento: 0,\n    percentualeCertificazione: 0,\n    metriTotali: 0,\n    metriInstallati: 0,\n    metriRimanenti: 0\n  });\n\n  // Stato per la gestione delle revisioni\n  const [revisioniDisponibili, setRevisioniDisponibili] = useState([]);\n  const [revisioneSelezionata, setRevisioneSelezionata] = useState('');\n  const [revisioneCorrente, setRevisioneCorrente] = useState('');\n\n  // Rimosso stato per il debug\n\n  // Funzione per calcolare le statistiche avanzate\n  const calculateStatistics = (caviAttiviData, caviSpareData) => {\n    const tuttiCavi = [...(caviAttiviData || []), ...(caviSpareData || [])];\n    if (tuttiCavi.length === 0) {\n      console.log('Nessun cavo disponibile per il calcolo delle statistiche');\n      return;\n    }\n    console.log('Calcolo statistiche con dati:', {\n      caviAttivi: (caviAttiviData === null || caviAttiviData === void 0 ? void 0 : caviAttiviData.length) || 0,\n      caviSpare: (caviSpareData === null || caviSpareData === void 0 ? void 0 : caviSpareData.length) || 0,\n      totale: tuttiCavi.length\n    });\n    const totaleCavi = tuttiCavi.length;\n\n    // Calcola stati di installazione\n    const caviInstallati = tuttiCavi.filter(cavo => cavo.stato_installazione === 'Installato' || cavo.stato_installazione === 'INSTALLATO' || cavo.stato_installazione === 'POSATO').length;\n    const caviDaInstallare = tuttiCavi.filter(cavo => cavo.stato_installazione === 'Da installare' || cavo.stato_installazione === 'DA_INSTALLARE').length;\n    const caviInCorso = tuttiCavi.filter(cavo => cavo.stato_installazione === 'In corso' || cavo.stato_installazione === 'IN_CORSO').length;\n\n    // Calcola stati di collegamento\n    const caviCollegati = tuttiCavi.filter(cavo => cavo.collegamenti === 3 && cavo.responsabile_partenza && cavo.responsabile_arrivo).length;\n    const caviNonCollegati = totaleCavi - caviCollegati;\n\n    // Calcola percentuali\n    const percentualeInstallazione = totaleCavi > 0 ? Math.round(caviInstallati / totaleCavi * 100) : 0;\n    const percentualeCollegamento = totaleCavi > 0 ? Math.round(caviCollegati / totaleCavi * 100) : 0;\n\n    // Calcola metri\n    const metriTotali = tuttiCavi.reduce((sum, cavo) => sum + (parseFloat(cavo.metri_teorici) || 0), 0);\n    const metriInstallati = tuttiCavi.filter(cavo => cavo.stato_installazione === 'Installato' || cavo.stato_installazione === 'INSTALLATO' || cavo.stato_installazione === 'POSATO').reduce((sum, cavo) => sum + (parseFloat(cavo.metratura_reale) || parseFloat(cavo.metri_teorici) || 0), 0);\n    const metriRimanenti = metriTotali - metriInstallati;\n\n    // Calcola certificazioni (per ora placeholder, sarà implementato)\n    const caviCertificati = 0; // TODO: Implementare caricamento certificazioni\n    const caviNonCertificati = totaleCavi - caviCertificati;\n    const percentualeCertificazione = totaleCavi > 0 ? Math.round(caviCertificati / totaleCavi * 100) : 0;\n    const newStatistics = {\n      totaleCavi,\n      caviInstallati,\n      caviDaInstallare,\n      caviInCorso,\n      caviCollegati,\n      caviNonCollegati,\n      caviCertificati,\n      caviNonCertificati,\n      percentualeInstallazione,\n      percentualeCollegamento,\n      percentualeCertificazione,\n      metriTotali: Math.round(metriTotali),\n      metriInstallati: Math.round(metriInstallati),\n      metriRimanenti: Math.round(metriRimanenti)\n    };\n    console.log('Nuove statistiche calcolate:', newStatistics);\n    setStatistics(newStatistics);\n  };\n\n  // Funzione per caricare gli stati di installazione disponibili\n  const loadStatiInstallazione = () => {\n    // Usa i valori dell'enum StatoInstallazione\n    setStatiInstallazione(['Installato', 'Da installare', 'In corso']);\n  };\n\n  // Funzione per caricare le revisioni disponibili\n  const loadRevisioni = async cantiereIdToUse => {\n    try {\n      console.log('Caricamento revisioni per cantiere:', cantiereIdToUse);\n\n      // Carica la revisione corrente\n      const revisioneCorrenteData = await caviService.getRevisioneCorrente(cantiereIdToUse);\n      console.log('Revisione corrente:', revisioneCorrenteData);\n      setRevisioneCorrente(revisioneCorrenteData.revisione_corrente);\n\n      // Carica tutte le revisioni disponibili\n      const revisioniData = await caviService.getRevisioniDisponibili(cantiereIdToUse);\n      console.log('Revisioni disponibili:', revisioniData);\n      setRevisioniDisponibili(revisioniData.revisioni || []);\n\n      // LOGICA REVISIONI: La revisione corrente è quella di default\n      // Non impostiamo una revisione selezionata, così il sistema usa automaticamente la corrente\n      console.log('Logica revisioni: usando revisione corrente di default');\n    } catch (error) {\n      console.error('Errore nel caricamento delle revisioni:', error);\n    }\n  };\n\n  // Funzione per gestire il cambio di revisione\n  const handleRevisioneChange = event => {\n    const nuovaRevisione = event.target.value;\n\n    // LOGICA REVISIONI:\n    // - Se vuoto o \"corrente\" -> usa revisione corrente (non specificare parametro)\n    // - Se specifica -> usa quella revisione per visualizzazione storica\n    if (nuovaRevisione === '' || nuovaRevisione === 'corrente') {\n      setRevisioneSelezionata('');\n      console.log('Passaggio a revisione corrente (default)');\n    } else {\n      setRevisioneSelezionata(nuovaRevisione);\n      console.log('Passaggio a revisione storica:', nuovaRevisione);\n    }\n  };\n\n  // Stato per filtri e ordinamento\n  const [filters, setFilters] = useState({\n    stato_installazione: '',\n    tipologia: '',\n    sort_by: '',\n    sort_order: 'asc'\n  });\n\n  // Opzioni per i filtri\n  const [statiInstallazione, setStatiInstallazione] = useState([]);\n  const [tipologieCavi, setTipologieCavi] = useState([]);\n\n  // Rimossa funzione di debug\n\n  // Funzione per caricare i cavi\n  // Il parametro silentLoading permette di evitare di mostrare lo stato di caricamento\n  const fetchCavi = async (silentLoading = false) => {\n    try {\n      if (!silentLoading) {\n        setLoading(true);\n      }\n      console.log('Caricamento cavi per cantiere:', cantiereId);\n\n      // Verifica che cantiereId sia valido\n      if (!cantiereId) {\n        console.error('fetchCavi: cantiereId non valido:', cantiereId);\n        setError('ID cantiere non valido o mancante. Ricarica la pagina.');\n        setLoading(false);\n        return;\n      }\n\n      // Recupera il cantiereId dal localStorage come fallback\n      let cantiereIdToUse = cantiereId;\n      if (!cantiereIdToUse) {\n        cantiereIdToUse = localStorage.getItem('selectedCantiereId');\n        console.log('Usando cantiereId dal localStorage:', cantiereIdToUse);\n        if (!cantiereIdToUse) {\n          console.error('Impossibile trovare un ID cantiere valido');\n          setError('ID cantiere non trovato. Ricarica la pagina.');\n          setLoading(false);\n          return;\n        }\n      }\n\n      // Carica i cavi attivi\n      console.log('Caricamento cavi attivi (tipo_cavo=0)...');\n      let attivi = [];\n      try {\n        attivi = await caviService.getCavi(cantiereIdToUse, 0, filters);\n        console.log('Cavi attivi caricati:', attivi ? attivi.length : 0);\n      } catch (attiviError) {\n        console.error('Errore nel caricamento dei cavi attivi:', attiviError);\n        // Continua con un array vuoto\n        attivi = [];\n      }\n\n      // Verifica se ci sono cavi con modificato_manualmente = 3 tra i cavi attivi\n      if (attivi && attivi.length > 0) {\n        const caviSpareTraAttivi = attivi.filter(cavo => cavo.modificato_manualmente === 3);\n        if (caviSpareTraAttivi.length > 0) {\n          console.error('ERRORE: Trovati cavi con modificato_manualmente = 3 tra i cavi attivi:', caviSpareTraAttivi);\n        }\n      }\n      setCaviAttivi(attivi || []);\n\n      // Carica i cavi SPARE con la nuova funzione dedicata\n      let spare = [];\n      try {\n        console.log('Caricamento cavi SPARE con funzione dedicata...');\n        spare = await caviService.getCaviSpare(cantiereIdToUse);\n        console.log('Cavi SPARE caricati con funzione dedicata:', spare ? spare.length : 0);\n        if (spare && spare.length > 0) {\n          console.log('Primo cavo SPARE:', spare[0]);\n        }\n      } catch (spareError) {\n        console.error('Errore nel caricamento dei cavi SPARE con funzione dedicata:', spareError);\n        // Se fallisce, prova con il metodo standard\n        try {\n          console.log('Tentativo con metodo standard...');\n          spare = await caviService.getCavi(cantiereIdToUse, 3);\n          console.log('Cavi SPARE caricati con metodo standard:', spare ? spare.length : 0);\n        } catch (standardError) {\n          console.error('Errore anche con metodo standard:', standardError);\n          // Continua con un array vuoto\n          spare = [];\n        }\n      }\n      setCaviSpare(spare || []);\n\n      // Se siamo arrivati qui, rimuovi eventuali messaggi di errore precedenti\n      setError('');\n    } catch (error) {\n      console.error('Errore generale nel caricamento dei cavi:', error);\n      setError(`Errore nel caricamento dei cavi: ${error.message || 'Errore sconosciuto'}`);\n\n      // Prova a ricaricare la pagina dopo un ritardo se l'errore persiste\n      setTimeout(() => {\n        // Verifica se siamo ancora in errore\n        if (document.body.textContent.includes('Errore nel caricamento dei cavi')) {\n          console.log('Errore persistente, tentativo di ricaricamento della pagina...');\n          window.location.reload();\n        }\n      }, 5000); // 5 secondi di ritardo\n    } finally {\n      if (!silentLoading) {\n        setLoading(false);\n      }\n    }\n  };\n\n  // Carica i dati del cantiere e dei cavi\n  useEffect(() => {\n    // Carica gli stati di installazione all'avvio\n    loadStatiInstallazione();\n    const fetchData = async () => {\n      try {\n        console.log('Inizializzazione VisualizzaCaviPage...');\n\n        // Verifica che l'utente sia autenticato\n        const token = localStorage.getItem('token');\n        console.log('Token presente:', !!token);\n        if (!token) {\n          setError('Sessione scaduta. Effettua nuovamente il login.');\n          setLoading(false);\n          return;\n        }\n\n        // Recupera l'ID del cantiere selezionato dal localStorage\n        let selectedCantiereId = localStorage.getItem('selectedCantiereId');\n        let selectedCantiereName = localStorage.getItem('selectedCantiereName');\n        console.log('Cantiere selezionato dal localStorage:', {\n          selectedCantiereId,\n          selectedCantiereName\n        });\n        console.log('Dati utente:', user);\n\n        // Stampa tutti i dati nel localStorage per debug\n        console.log('DEBUG - Tutti i dati nel localStorage:');\n        for (let i = 0; i < localStorage.length; i++) {\n          const key = localStorage.key(i);\n          console.log(`${key}: ${localStorage.getItem(key)}`);\n        }\n\n        // SOLUZIONE DIRETTA: Ottieni l'ID del cantiere direttamente dal token JWT\n        if ((user === null || user === void 0 ? void 0 : user.role) === 'cantieri_user') {\n          console.log('Utente cantiere rilevato, tentativo di recupero ID cantiere dai dati utente');\n\n          // Verifica se l'utente ha un ID cantiere nei dati utente\n          if (user.cantiere_id) {\n            console.log('Trovato ID cantiere nei dati utente:', user.cantiere_id);\n            selectedCantiereId = user.cantiere_id.toString();\n            selectedCantiereName = user.cantiere_name || `Cantiere ${user.cantiere_id}`;\n\n            // Salva l'ID e il nome del cantiere nel localStorage\n            localStorage.setItem('selectedCantiereId', selectedCantiereId);\n            localStorage.setItem('selectedCantiereName', selectedCantiereName);\n            console.log('Salvato ID cantiere nel localStorage:', selectedCantiereId);\n          } else {\n            // Tentativo di recupero dal token JWT\n            try {\n              console.log('Tentativo di decodifica del token JWT per recuperare l\\'ID cantiere');\n              const token = localStorage.getItem('token');\n              if (token) {\n                // Decodifica il token JWT (senza verifica della firma)\n                const base64Url = token.split('.')[1];\n                const base64 = base64Url.replace(/-/g, '+').replace(/_/g, '/');\n                const jsonPayload = decodeURIComponent(atob(base64).split('').map(c => {\n                  return '%' + ('00' + c.charCodeAt(0).toString(16)).slice(-2);\n                }).join(''));\n                const payload = JSON.parse(jsonPayload);\n                console.log('Payload del token JWT:', payload);\n                if (payload.cantiere_id) {\n                  console.log('Trovato ID cantiere nel token JWT:', payload.cantiere_id);\n                  selectedCantiereId = payload.cantiere_id.toString();\n                  // Usa un nome generico se non disponibile\n                  selectedCantiereName = `Cantiere ${payload.cantiere_id}`;\n\n                  // Salva l'ID e il nome del cantiere nel localStorage\n                  localStorage.setItem('selectedCantiereId', selectedCantiereId);\n                  localStorage.setItem('selectedCantiereName', selectedCantiereName);\n                  console.log('Salvato ID cantiere nel localStorage:', selectedCantiereId);\n                }\n              }\n            } catch (e) {\n              console.error('Errore durante la decodifica del token JWT:', e);\n            }\n          }\n        }\n\n        // SOLUZIONE TEMPORANEA: Se ancora non abbiamo un ID cantiere, usa un valore hardcoded per debug\n        if (!selectedCantiereId || selectedCantiereId === 'undefined' || selectedCantiereId === 'null') {\n          console.warn('ATTENZIONE: Nessun ID cantiere trovato, utilizzo valore hardcoded per debug');\n          // Usa il primo cantiere disponibile (questo è solo per debug)\n          selectedCantiereId = '1'; // Sostituisci con un ID cantiere valido nel tuo database\n          selectedCantiereName = 'Cantiere Debug';\n\n          // Salva l'ID e il nome del cantiere nel localStorage\n          localStorage.setItem('selectedCantiereId', selectedCantiereId);\n          localStorage.setItem('selectedCantiereName', selectedCantiereName);\n          console.log('Salvato ID cantiere hardcoded nel localStorage:', selectedCantiereId);\n        }\n\n        // Verifica finale\n        if (!selectedCantiereId) {\n          setError('Nessun cantiere selezionato. Torna alla pagina dei cantieri.');\n          setLoading(false);\n          return;\n        }\n\n        // Verifica che l'ID del cantiere sia un numero valido\n        const cantiereIdNum = parseInt(selectedCantiereId, 10);\n        console.log('ID cantiere convertito a numero:', cantiereIdNum);\n        if (isNaN(cantiereIdNum)) {\n          setError(`ID cantiere non valido: ${selectedCantiereId}. Torna alla pagina dei cantieri.`);\n          setLoading(false);\n          return;\n        }\n\n        // Usa il numero convertito, non la stringa\n        setCantiereId(cantiereIdNum);\n        setCantiereName(selectedCantiereName || `Cantiere ${cantiereIdNum}`);\n\n        // Carica le revisioni disponibili\n        await loadRevisioni(cantiereIdNum);\n\n        // Carica i cavi attivi con gestione degli errori migliorata\n        console.log('Caricamento cavi attivi per cantiere:', cantiereIdNum);\n        try {\n          // Imposta un timeout per evitare che la richiesta rimanga bloccata\n          const timeoutPromise = new Promise((_, reject) => {\n            setTimeout(() => reject(new Error('Timeout durante il caricamento dei cavi attivi')), 30000); // Aumentato a 30 secondi\n          });\n\n          // Esegui la richiesta con un timeout di sicurezza e applica i filtri\n          console.log('Iniziando chiamata API per cavi attivi con filtri:', filters);\n          const caviPromise = caviService.getCavi(cantiereIdNum, 0, filters);\n          const attivi = await Promise.race([caviPromise, timeoutPromise]);\n          console.log('Cavi attivi caricati:', attivi);\n          console.log('Numero di cavi attivi trovati:', attivi ? attivi.length : 0);\n          if (attivi && attivi.length > 0) {\n            console.log('Primo cavo attivo:', attivi[0]);\n          } else {\n            console.warn('Nessun cavo attivo trovato per il cantiere', cantiereIdNum);\n          }\n          setCaviAttivi(attivi || []);\n\n          // Calcola le statistiche dopo aver caricato i cavi attivi\n          calculateStatistics(attivi || [], caviSpare);\n        } catch (caviError) {\n          console.error('Errore nel caricamento dei cavi attivi:', caviError);\n          console.error('Dettagli errore cavi attivi:', {\n            message: caviError.message,\n            status: caviError.status,\n            data: caviError.data,\n            stack: caviError.stack,\n            code: caviError.code,\n            name: caviError.name,\n            response: caviError.response ? {\n              status: caviError.response.status,\n              statusText: caviError.response.statusText,\n              data: caviError.response.data\n            } : 'No response'\n          });\n\n          // Non interrompere il flusso, continua con i cavi spare\n          setCaviAttivi([]);\n          console.warn('Continuazione del flusso dopo errore nei cavi attivi');\n\n          // Aggiungi un messaggio di errore visibile all'utente\n          setError(`Errore nel caricamento dei cavi attivi: ${caviError.message}. Controlla la console per maggiori dettagli.`);\n        }\n\n        // Carica i cavi spare con gestione degli errori migliorata\n        console.log('Caricamento cavi spare per cantiere:', cantiereIdNum);\n        try {\n          // Imposta un timeout per evitare che la richiesta rimanga bloccata\n          const timeoutPromise = new Promise((_, reject) => {\n            setTimeout(() => reject(new Error('Timeout durante il caricamento dei cavi spare')), 30000); // Aumentato a 30 secondi\n          });\n\n          // Esegui la richiesta con un timeout di sicurezza\n          console.log('Iniziando chiamata API per cavi spare...');\n          // Non applichiamo i filtri ai cavi spare, solo agli attivi\n          const sparePromise = caviService.getCavi(cantiereIdNum, 3);\n          const spare = await Promise.race([sparePromise, timeoutPromise]);\n          console.log('Cavi spare caricati:', spare);\n          console.log('Numero di cavi spare trovati:', spare ? spare.length : 0);\n          if (spare && spare.length > 0) {\n            console.log('Primo cavo spare:', spare[0]);\n          } else {\n            console.warn('Nessun cavo spare trovato per il cantiere', cantiereIdNum);\n          }\n          setCaviSpare(spare || []);\n\n          // Calcola le statistiche dopo aver caricato i cavi spare\n          calculateStatistics(caviAttivi, spare || []);\n        } catch (spareError) {\n          console.error('Errore nel caricamento dei cavi spare:', spareError);\n          console.error('Dettagli errore cavi spare:', {\n            message: spareError.message,\n            status: spareError.status,\n            data: spareError.data,\n            stack: spareError.stack,\n            code: spareError.code,\n            name: spareError.name,\n            response: spareError.response ? {\n              status: spareError.response.status,\n              statusText: spareError.response.statusText,\n              data: spareError.response.data\n            } : 'No response'\n          });\n\n          // Non interrompere il flusso, imposta un array vuoto\n          setCaviSpare([]);\n\n          // Aggiungi un messaggio di errore visibile all'utente se non c'è già un errore per i cavi attivi\n          if (!error) {\n            setError(`Errore nel caricamento dei cavi spare: ${spareError.message}. Controlla la console per maggiori dettagli.`);\n          }\n        }\n\n        // Se siamo arrivati qui, almeno abbiamo caricato l'interfaccia di base\n        setLoading(false);\n      } catch (err) {\n        var _err$response, _err$response2, _err$response3, _err$response4, _err$response5, _err$response5$data;\n        console.error('Errore nel caricamento dei cavi:', err);\n        console.error('Dettagli errore generale:', {\n          message: err.message,\n          status: err.status || ((_err$response = err.response) === null || _err$response === void 0 ? void 0 : _err$response.status),\n          data: err.data || ((_err$response2 = err.response) === null || _err$response2 === void 0 ? void 0 : _err$response2.data),\n          stack: err.stack\n        });\n\n        // Estrai il messaggio di errore dettagliato\n        let errorMessage = 'Errore sconosciuto';\n        if (err.message && err.message.includes('ID cantiere non valido')) {\n          errorMessage = err.message;\n        } else if (err.status === 401 || err.status === 403 || ((_err$response3 = err.response) === null || _err$response3 === void 0 ? void 0 : _err$response3.status) === 401 || ((_err$response4 = err.response) === null || _err$response4 === void 0 ? void 0 : _err$response4.status) === 403) {\n          errorMessage = 'Sessione scaduta o non autorizzata. Effettua nuovamente il login.';\n        } else if ((_err$response5 = err.response) !== null && _err$response5 !== void 0 && (_err$response5$data = _err$response5.data) !== null && _err$response5$data !== void 0 && _err$response5$data.detail) {\n          // Estrai il messaggio di errore dettagliato dall'API\n          errorMessage = `Errore API: ${err.response.data.detail}`;\n        } else if (err.code === 'ERR_NETWORK') {\n          // Errore di rete\n          errorMessage = 'Network Error. Verifica che il backend sia in esecuzione e accessibile.';\n        } else if (err.message) {\n          errorMessage = err.message;\n        }\n        setError(`Impossibile caricare i cavi: ${errorMessage}. Riprova più tardi.`);\n\n        // Imposta array vuoti per evitare errori di rendering\n        setCaviAttivi([]);\n        setCaviSpare([]);\n      } finally {\n        setLoading(false);\n      }\n    };\n    fetchData();\n  }, [filters]); // Ricarica i dati quando cambiano i filtri\n\n  // I filtri sono ora gestiti dal componente CaviFilterableTable\n\n  // Funzione per aprire il dialogo dei dettagli del cavo\n  const handleOpenDetails = cavo => {\n    setSelectedCavo(cavo);\n    setDetailsDialogOpen(true);\n  };\n\n  // Funzione per chiudere il dialogo dei dettagli del cavo\n  const handleCloseDetails = () => {\n    setDetailsDialogOpen(false);\n    setSelectedCavo(null);\n  };\n\n  // Funzione per chiudere la notifica\n  const handleCloseNotification = () => {\n    setNotification(prev => ({\n      ...prev,\n      open: false\n    }));\n  };\n\n  // Funzione per mostrare una notifica\n  const showNotification = (message, severity = 'success') => {\n    setNotification({\n      open: true,\n      message,\n      severity\n    });\n  };\n\n  // Funzioni per gestire la selezione dei cavi\n  const handleSelectionToggle = () => {\n    setSelectionEnabled(!selectionEnabled);\n    // Pulisci le selezioni quando si disabilita la modalità selezione\n    if (selectionEnabled) {\n      setSelectedCaviAttivi([]);\n      setSelectedCaviSpare([]);\n    }\n  };\n  const handleCaviAttiviSelectionChange = selectedIds => {\n    setSelectedCaviAttivi(selectedIds);\n  };\n  const handleCaviSpareSelectionChange = selectedIds => {\n    setSelectedCaviSpare(selectedIds);\n  };\n\n  // Funzione per ottenere tutti i cavi selezionati\n  const getAllSelectedCavi = () => {\n    const selectedAttiviCavi = caviAttivi.filter(cavo => selectedCaviAttivi.includes(cavo.id_cavo));\n    const selectedSpareCavi = caviSpare.filter(cavo => selectedCaviSpare.includes(cavo.id_cavo));\n    return [...selectedAttiviCavi, ...selectedSpareCavi];\n  };\n\n  // Funzione per ottenere il conteggio totale dei cavi selezionati\n  const getTotalSelectedCount = () => {\n    return selectedCaviAttivi.length + selectedCaviSpare.length;\n  };\n\n  // Funzioni per gestire le azioni del menu contestuale\n  const handleContextMenuAction = (cavo, action) => {\n    console.log('Azione menu contestuale:', action, 'per cavo:', cavo);\n    switch (action) {\n      case 'view_details':\n        handleOpenDetails(cavo);\n        break;\n      case 'edit':\n        // Implementa logica di modifica\n        showNotification(`Modifica cavo ${cavo.id_cavo} - Funzione da implementare`, 'info');\n        break;\n      case 'delete':\n        // Implementa logica di eliminazione\n        if (window.confirm(`Sei sicuro di voler eliminare il cavo ${cavo.id_cavo}?`)) {\n          showNotification(`Eliminazione cavo ${cavo.id_cavo} - Funzione da implementare`, 'warning');\n        }\n        break;\n      case 'select':\n        if (caviAttivi.some(c => c.id_cavo === cavo.id_cavo)) {\n          // È un cavo attivo\n          const isSelected = selectedCaviAttivi.includes(cavo.id_cavo);\n          if (isSelected) {\n            setSelectedCaviAttivi(prev => prev.filter(id => id !== cavo.id_cavo));\n          } else {\n            setSelectedCaviAttivi(prev => [...prev, cavo.id_cavo]);\n          }\n        } else {\n          // È un cavo spare\n          const isSelected = selectedCaviSpare.includes(cavo.id_cavo);\n          if (isSelected) {\n            setSelectedCaviSpare(prev => prev.filter(id => id !== cavo.id_cavo));\n          } else {\n            setSelectedCaviSpare(prev => [...prev, cavo.id_cavo]);\n          }\n        }\n        // Abilita automaticamente la modalità selezione se non è già attiva\n        if (!selectionEnabled) {\n          setSelectionEnabled(true);\n        }\n        break;\n      case 'copy_id':\n        navigator.clipboard.writeText(cavo.id_cavo);\n        showNotification(`ID cavo ${cavo.id_cavo} copiato negli appunti`, 'success');\n        break;\n      case 'copy_details':\n        const details = `ID: ${cavo.id_cavo}\\nTipologia: ${cavo.tipologia}\\nSezione: ${cavo.sezione}\\nMetri: ${cavo.metri_teorici}`;\n        navigator.clipboard.writeText(details);\n        showNotification('Dettagli cavo copiati negli appunti', 'success');\n        break;\n      default:\n        console.warn('Azione non riconosciuta:', action);\n    }\n  };\n\n  // Definizione degli elementi del menu contestuale\n  const getContextMenuItems = cavo => {\n    const isSelected = caviAttivi.some(c => c.id_cavo === (cavo === null || cavo === void 0 ? void 0 : cavo.id_cavo)) ? selectedCaviAttivi.includes(cavo === null || cavo === void 0 ? void 0 : cavo.id_cavo) : selectedCaviSpare.includes(cavo === null || cavo === void 0 ? void 0 : cavo.id_cavo);\n    return [{\n      type: 'header',\n      label: `Cavo ${(cavo === null || cavo === void 0 ? void 0 : cavo.id_cavo) || ''}`\n    }, {\n      id: 'view_details',\n      label: 'Visualizza Dettagli',\n      icon: /*#__PURE__*/_jsxDEV(VisibilityIcon, {\n        fontSize: \"small\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 740,\n        columnNumber: 15\n      }, this),\n      action: 'view_details',\n      onClick: handleContextMenuAction\n    }, {\n      type: 'divider'\n    }, {\n      id: 'edit',\n      label: 'Modifica',\n      icon: /*#__PURE__*/_jsxDEV(EditIcon, {\n        fontSize: \"small\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 750,\n        columnNumber: 15\n      }, this),\n      action: 'edit',\n      onClick: handleContextMenuAction,\n      color: 'primary'\n    }, {\n      id: 'delete',\n      label: 'Elimina',\n      icon: /*#__PURE__*/_jsxDEV(DeleteIcon, {\n        fontSize: \"small\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 758,\n        columnNumber: 15\n      }, this),\n      action: 'delete',\n      onClick: handleContextMenuAction,\n      color: 'error'\n    }, {\n      type: 'divider'\n    }, {\n      id: 'select',\n      label: isSelected ? 'Deseleziona' : 'Seleziona',\n      icon: /*#__PURE__*/_jsxDEV(SelectAllIcon, {\n        fontSize: \"small\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 769,\n        columnNumber: 15\n      }, this),\n      action: 'select',\n      onClick: handleContextMenuAction,\n      color: isSelected ? 'warning' : 'success'\n    }, {\n      type: 'divider'\n    }, {\n      id: 'copy_id',\n      label: 'Copia ID',\n      icon: /*#__PURE__*/_jsxDEV(CopyIcon, {\n        fontSize: \"small\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 780,\n        columnNumber: 15\n      }, this),\n      action: 'copy_id',\n      onClick: handleContextMenuAction,\n      shortcut: 'Ctrl+C'\n    }, {\n      id: 'copy_details',\n      label: 'Copia Dettagli',\n      icon: /*#__PURE__*/_jsxDEV(CopyIcon, {\n        fontSize: \"small\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 788,\n        columnNumber: 15\n      }, this),\n      action: 'copy_details',\n      onClick: handleContextMenuAction,\n      description: 'Copia ID, tipologia, sezione e metri'\n    }];\n  };\n\n  // Funzioni per gestire le azioni sui pulsanti stato\n  const handleStatusAction = async (cavo, actionType, actionLabel) => {\n    console.log('🎯 CLICK PULSANTE STATO RILEVATO!');\n    console.log('Azione pulsante stato:', actionType, 'per cavo:', cavo);\n    console.log('Action label:', actionLabel);\n    if (actionType === 'insert_meters') {\n      // Apri il dialogo per inserire i metri posati\n      setInserisciMetriDialog({\n        open: true,\n        cavo: cavo,\n        loading: false\n      });\n    } else if (actionType === 'modify_reel') {\n      // Carica le bobine disponibili e apri il dialogo per modificare la bobina\n      try {\n        console.log('Caricamento bobine per cantiere:', cantiereId);\n        const bobine = await parcoCaviService.getBobine(cantiereId);\n        console.log('Bobine caricate:', bobine);\n        setBobineDisponibili(bobine || []);\n        setModificaBobinaDialog({\n          open: true,\n          cavo: cavo,\n          loading: false\n        });\n      } catch (error) {\n        console.error('Errore nel caricamento delle bobine:', error);\n        showNotification('Errore nel caricamento delle bobine disponibili', 'error');\n      }\n    }\n  };\n\n  // Funzione per salvare i metri posati\n  const handleSaveMetri = async (cavoId, metri) => {\n    setInserisciMetriDialog(prev => ({\n      ...prev,\n      loading: true\n    }));\n    try {\n      console.log('Salvando metri per cavo:', cavoId, 'metri:', metri, 'cantiere:', cantiereId);\n\n      // Chiamata API reale per aggiornare i metri posati\n      const cavoAggiornato = await caviService.updateMetriPosati(cantiereId, cavoId, metri, null,\n      // id_bobina - per ora null, può essere gestito separatamente\n      true // force_over\n      );\n      console.log('Cavo aggiornato:', cavoAggiornato);\n\n      // Aggiorna lo stato locale del cavo\n      const updateCavo = cavi => cavi.map(cavo => cavo.id_cavo === cavoId ? {\n        ...cavo,\n        metratura_reale: metri,\n        stato_installazione: cavoAggiornato.stato_installazione || (metri >= cavo.metri_teorici ? 'INSTALLATO' : 'IN_CORSO')\n      } : cavo);\n      setCaviAttivi(updateCavo);\n      setCaviSpare(updateCavo);\n\n      // Chiudi il dialogo\n      setInserisciMetriDialog({\n        open: false,\n        cavo: null,\n        loading: false\n      });\n      showNotification(`Metri posati aggiornati per il cavo ${cavoId}: ${metri}m`, 'success');\n\n      // Ricarica i dati per essere sicuri\n      setTimeout(() => fetchCavi(true), 500);\n    } catch (error) {\n      console.error('Errore nel salvataggio dei metri:', error);\n      const errorMessage = error.detail || error.message || 'Errore nel salvataggio dei metri posati';\n      showNotification(errorMessage, 'error');\n      setInserisciMetriDialog(prev => ({\n        ...prev,\n        loading: false\n      }));\n    }\n  };\n\n  // Funzione per modificare la bobina\n  const handleModifyBobina = async (cavoId, nuovaBobinaId, motivazione) => {\n    setModificaBobinaDialog(prev => ({\n      ...prev,\n      loading: true\n    }));\n    try {\n      console.log('Modificando bobina per cavo:', cavoId, 'nuova bobina:', nuovaBobinaId, 'motivazione:', motivazione, 'cantiere:', cantiereId);\n\n      // Chiamata API reale per aggiornare la bobina\n      const cavoAggiornato = await caviService.updateBobina(cantiereId, cavoId, nuovaBobinaId, true // force_over\n      );\n      console.log('Cavo aggiornato con nuova bobina:', cavoAggiornato);\n\n      // Aggiorna lo stato locale del cavo\n      const updateCavo = cavi => cavi.map(cavo => cavo.id_cavo === cavoId ? {\n        ...cavo,\n        id_bobina: nuovaBobinaId\n      } : cavo);\n      setCaviAttivi(updateCavo);\n      setCaviSpare(updateCavo);\n\n      // Chiudi il dialogo\n      setModificaBobinaDialog({\n        open: false,\n        cavo: null,\n        loading: false\n      });\n      showNotification(`Bobina modificata per il cavo ${cavoId}: ${nuovaBobinaId}`, 'success');\n\n      // Ricarica i dati per essere sicuri\n      setTimeout(() => fetchCavi(true), 500);\n    } catch (error) {\n      console.error('Errore nella modifica della bobina:', error);\n      const errorMessage = error.detail || error.message || 'Errore nella modifica della bobina';\n      showNotification(errorMessage, 'error');\n      setModificaBobinaDialog(prev => ({\n        ...prev,\n        loading: false\n      }));\n    }\n  };\n\n  // Funzioni per chiudere i dialoghi\n  const handleCloseInserisciMetri = () => {\n    if (!inserisciMetriDialog.loading) {\n      setInserisciMetriDialog({\n        open: false,\n        cavo: null,\n        loading: false\n      });\n    }\n  };\n  const handleCloseModificaBobina = () => {\n    if (!modificaBobinaDialog.loading) {\n      setModificaBobinaDialog({\n        open: false,\n        cavo: null,\n        loading: false\n      });\n    }\n  };\n\n  // Nessuna funzione di navigazione necessaria, tutto è gestito dal menu principale\n\n  // Dashboard minimal con statistiche essenziali per visualizzazione cavi\n  const renderDashboard = () => /*#__PURE__*/_jsxDEV(Paper, {\n    sx: {\n      p: 2,\n      mb: 3,\n      bgcolor: 'grey.50'\n    },\n    children: /*#__PURE__*/_jsxDEV(Stack, {\n      direction: \"row\",\n      spacing: 4,\n      alignItems: \"center\",\n      justifyContent: \"space-between\",\n      flexWrap: \"wrap\",\n      children: [/*#__PURE__*/_jsxDEV(Stack, {\n        direction: \"row\",\n        alignItems: \"center\",\n        spacing: 1,\n        children: [/*#__PURE__*/_jsxDEV(CableIcon, {\n          color: \"primary\",\n          fontSize: \"small\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 941,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Box, {\n          children: [/*#__PURE__*/_jsxDEV(Typography, {\n            variant: \"h6\",\n            fontWeight: \"bold\",\n            sx: {\n              lineHeight: 1\n            },\n            children: statistics.totaleCavi\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 943,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Typography, {\n            variant: \"caption\",\n            color: \"text.secondary\",\n            children: \"Totale\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 946,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 942,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 940,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Stack, {\n        direction: \"row\",\n        alignItems: \"center\",\n        spacing: 1,\n        children: [/*#__PURE__*/_jsxDEV(CheckCircleIcon, {\n          color: \"success\",\n          fontSize: \"small\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 953,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Box, {\n          children: [/*#__PURE__*/_jsxDEV(Typography, {\n            variant: \"h6\",\n            fontWeight: \"bold\",\n            sx: {\n              lineHeight: 1\n            },\n            children: statistics.caviInstallati\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 955,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Typography, {\n            variant: \"caption\",\n            color: \"text.secondary\",\n            children: \"Installati\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 958,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 954,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 952,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Stack, {\n        direction: \"row\",\n        alignItems: \"center\",\n        spacing: 1,\n        children: [/*#__PURE__*/_jsxDEV(LinkIcon, {\n          color: \"info\",\n          fontSize: \"small\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 965,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Box, {\n          children: [/*#__PURE__*/_jsxDEV(Typography, {\n            variant: \"h6\",\n            fontWeight: \"bold\",\n            sx: {\n              lineHeight: 1\n            },\n            children: statistics.caviCollegati\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 967,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Typography, {\n            variant: \"caption\",\n            color: \"text.secondary\",\n            children: \"Collegati\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 970,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 966,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 964,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Stack, {\n        direction: \"row\",\n        alignItems: \"center\",\n        spacing: 1,\n        children: [/*#__PURE__*/_jsxDEV(VerifiedIcon, {\n          color: \"warning\",\n          fontSize: \"small\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 977,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Box, {\n          children: [/*#__PURE__*/_jsxDEV(Typography, {\n            variant: \"h6\",\n            fontWeight: \"bold\",\n            sx: {\n              lineHeight: 1\n            },\n            children: statistics.caviCertificati\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 979,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Typography, {\n            variant: \"caption\",\n            color: \"text.secondary\",\n            children: \"Certificati\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 982,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 978,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 976,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Stack, {\n        direction: \"row\",\n        alignItems: \"center\",\n        spacing: 1,\n        children: [/*#__PURE__*/_jsxDEV(Box, {\n          sx: {\n            width: 32,\n            height: 32,\n            borderRadius: '50%',\n            bgcolor: statistics.percentualeInstallazione >= 80 ? 'success.main' : statistics.percentualeInstallazione >= 50 ? 'warning.main' : 'error.main',\n            display: 'flex',\n            alignItems: 'center',\n            justifyContent: 'center'\n          },\n          children: /*#__PURE__*/_jsxDEV(Typography, {\n            variant: \"caption\",\n            fontWeight: \"bold\",\n            color: \"white\",\n            children: [statistics.percentualeInstallazione, \"%\"]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 999,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 989,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Box, {\n          children: [/*#__PURE__*/_jsxDEV(Typography, {\n            variant: \"body2\",\n            fontWeight: \"medium\",\n            sx: {\n              lineHeight: 1\n            },\n            children: \"Installazione\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 1004,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Typography, {\n            variant: \"caption\",\n            color: \"text.secondary\",\n            children: [statistics.metriInstallati, \"m installati\"]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 1007,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 1003,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 988,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 938,\n      columnNumber: 7\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 937,\n    columnNumber: 5\n  }, this);\n\n  // La visualizzazione dei cavi è ora gestita dal componente CaviFilterableTable\n\n  // Rimossa funzione handleViewModeChange\n\n  // Renderizza il dialogo dei dettagli del cavo\n  const renderDetailsDialog = () => {\n    if (!selectedCavo) return null;\n    return /*#__PURE__*/_jsxDEV(Dialog, {\n      open: detailsDialogOpen,\n      onClose: handleCloseDetails,\n      maxWidth: \"md\",\n      fullWidth: true,\n      children: [/*#__PURE__*/_jsxDEV(DialogTitle, {\n        children: [\"Dettagli Cavo: \", selectedCavo.id_cavo]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 1026,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(DialogContent, {\n        dividers: true,\n        children: /*#__PURE__*/_jsxDEV(Grid, {\n          container: true,\n          spacing: 2,\n          children: [/*#__PURE__*/_jsxDEV(Grid, {\n            item: true,\n            xs: 12,\n            md: 6,\n            children: [/*#__PURE__*/_jsxDEV(Typography, {\n              variant: \"subtitle1\",\n              gutterBottom: true,\n              children: \"Informazioni Generali\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1032,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Box, {\n              sx: {\n                mb: 2\n              },\n              children: [/*#__PURE__*/_jsxDEV(Typography, {\n                variant: \"body2\",\n                children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n                  children: \"Sistema:\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1034,\n                  columnNumber: 45\n                }, this), \" \", selectedCavo.sistema || 'N/A']\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 1034,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                variant: \"body2\",\n                children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n                  children: \"Utility:\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1035,\n                  columnNumber: 45\n                }, this), \" \", selectedCavo.utility || 'N/A']\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 1035,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                variant: \"body2\",\n                children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n                  children: \"Tipologia:\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1036,\n                  columnNumber: 45\n                }, this), \" \", selectedCavo.tipologia || 'N/A']\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 1036,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                variant: \"body2\",\n                children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n                  children: \"Colore:\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1037,\n                  columnNumber: 45\n                }, this), \" \", selectedCavo.colore_cavo || 'N/A']\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 1037,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                variant: \"body2\",\n                children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n                  children: \"Formazione:\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1039,\n                  columnNumber: 45\n                }, this), \" \", selectedCavo.sezione || 'N/A']\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 1039,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 1033,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Typography, {\n              variant: \"subtitle1\",\n              gutterBottom: true,\n              children: \"Partenza\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1043,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Box, {\n              sx: {\n                mb: 2\n              },\n              children: [/*#__PURE__*/_jsxDEV(Typography, {\n                variant: \"body2\",\n                children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n                  children: \"Ubicazione:\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1045,\n                  columnNumber: 45\n                }, this), \" \", selectedCavo.ubicazione_partenza || 'N/A']\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 1045,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                variant: \"body2\",\n                children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n                  children: \"Utenza:\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1046,\n                  columnNumber: 45\n                }, this), \" \", selectedCavo.utenza_partenza || 'N/A']\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 1046,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                variant: \"body2\",\n                children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n                  children: \"Descrizione:\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1047,\n                  columnNumber: 45\n                }, this), \" \", selectedCavo.descrizione_utenza_partenza || 'N/A']\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 1047,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                variant: \"body2\",\n                children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n                  children: \"Responsabile:\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1048,\n                  columnNumber: 45\n                }, this), \" \", selectedCavo.responsabile_partenza || 'N/A']\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 1048,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                variant: \"body2\",\n                children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n                  children: \"Comanda:\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1049,\n                  columnNumber: 45\n                }, this), \" \", selectedCavo.comanda_partenza || 'N/A']\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 1049,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 1044,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 1031,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Grid, {\n            item: true,\n            xs: 12,\n            md: 6,\n            children: [/*#__PURE__*/_jsxDEV(Typography, {\n              variant: \"subtitle1\",\n              gutterBottom: true,\n              children: \"Arrivo\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1054,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Box, {\n              sx: {\n                mb: 2\n              },\n              children: [/*#__PURE__*/_jsxDEV(Typography, {\n                variant: \"body2\",\n                children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n                  children: \"Ubicazione:\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1056,\n                  columnNumber: 45\n                }, this), \" \", selectedCavo.ubicazione_arrivo || 'N/A']\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 1056,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                variant: \"body2\",\n                children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n                  children: \"Utenza:\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1057,\n                  columnNumber: 45\n                }, this), \" \", selectedCavo.utenza_arrivo || 'N/A']\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 1057,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                variant: \"body2\",\n                children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n                  children: \"Descrizione:\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1058,\n                  columnNumber: 45\n                }, this), \" \", selectedCavo.descrizione_utenza_arrivo || 'N/A']\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 1058,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                variant: \"body2\",\n                children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n                  children: \"Responsabile:\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1059,\n                  columnNumber: 45\n                }, this), \" \", selectedCavo.responsabile_arrivo || 'N/A']\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 1059,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                variant: \"body2\",\n                children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n                  children: \"Comanda:\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1060,\n                  columnNumber: 45\n                }, this), \" \", selectedCavo.comanda_arrivo || 'N/A']\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 1060,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 1055,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Typography, {\n              variant: \"subtitle1\",\n              gutterBottom: true,\n              children: \"Installazione\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1063,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Box, {\n              sx: {\n                mb: 2\n              },\n              children: [/*#__PURE__*/_jsxDEV(Typography, {\n                variant: \"body2\",\n                children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n                  children: \"Metri Teorici:\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1065,\n                  columnNumber: 45\n                }, this), \" \", selectedCavo.metri_teorici || 'N/A']\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 1065,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                variant: \"body2\",\n                children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n                  children: \"Metratura Reale:\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1066,\n                  columnNumber: 45\n                }, this), \" \", selectedCavo.metratura_reale || '0']\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 1066,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                variant: \"body2\",\n                children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n                  children: \"Stato:\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1067,\n                  columnNumber: 45\n                }, this), \" \", normalizeInstallationStatus(selectedCavo.stato_installazione)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 1067,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                variant: \"body2\",\n                children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n                  children: \"Collegamenti:\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1068,\n                  columnNumber: 45\n                }, this), \" \", selectedCavo.collegamenti || '0']\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 1068,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                variant: \"body2\",\n                children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n                  children: \"Bobina:\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1069,\n                  columnNumber: 45\n                }, this), \" \", selectedCavo.id_bobina || 'N/A']\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 1069,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                variant: \"body2\",\n                children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n                  children: \"Responsabile Posa:\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1070,\n                  columnNumber: 45\n                }, this), \" \", selectedCavo.responsabile_posa || 'N/A']\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 1070,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                variant: \"body2\",\n                children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n                  children: \"Comanda Posa:\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1071,\n                  columnNumber: 45\n                }, this), \" \", selectedCavo.comanda_posa || 'N/A']\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 1071,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                variant: \"body2\",\n                children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n                  children: \"Ultimo Aggiornamento:\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1072,\n                  columnNumber: 45\n                }, this), \" \", new Date(selectedCavo.timestamp).toLocaleString()]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 1072,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 1064,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 1053,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 1030,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 1029,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(DialogActions, {\n        children: /*#__PURE__*/_jsxDEV(Button, {\n          onClick: handleCloseDetails,\n          children: \"Chiudi\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 1078,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 1077,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 1025,\n      columnNumber: 7\n    }, this);\n  };\n\n  // Il pannello dei filtri è ora gestito dal componente CaviFilterableTable\n\n  return /*#__PURE__*/_jsxDEV(Box, {\n    className: \"cavi-page\",\n    children: loading ? /*#__PURE__*/_jsxDEV(Box, {\n      sx: {\n        display: 'flex',\n        flexDirection: 'column',\n        alignItems: 'center',\n        mt: 4\n      },\n      children: [/*#__PURE__*/_jsxDEV(CircularProgress, {\n        size: 40\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 1092,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(Typography, {\n        sx: {\n          mt: 2\n        },\n        children: \"Caricamento cavi...\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 1093,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(Button, {\n        variant: \"outlined\",\n        color: \"primary\",\n        onClick: () => window.location.reload(),\n        sx: {\n          mt: 2\n        },\n        children: \"Ricarica la pagina\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 1094,\n        columnNumber: 11\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 1091,\n      columnNumber: 9\n    }, this) : error ? /*#__PURE__*/_jsxDEV(Box, {\n      children: [/*#__PURE__*/_jsxDEV(Alert, {\n        severity: \"error\",\n        sx: {\n          mb: 2\n        },\n        children: [error, error.includes('Network Error') && /*#__PURE__*/_jsxDEV(Typography, {\n          variant: \"body2\",\n          sx: {\n            mt: 1\n          },\n          children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n            children: \"Suggerimento:\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 1109,\n            columnNumber: 17\n          }, this), \" Verifica che il server backend sia in esecuzione sulla porta 8001.\", /*#__PURE__*/_jsxDEV(\"br\", {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 1110,\n            columnNumber: 17\n          }, this), \"Puoi avviare il backend eseguendo il file \", /*#__PURE__*/_jsxDEV(\"code\", {\n            children: \"run_system.py\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 1111,\n            columnNumber: 59\n          }, this), \" nella cartella principale del progetto.\"]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 1108,\n          columnNumber: 15\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 1105,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(Box, {\n        sx: {\n          display: 'flex',\n          gap: 2\n        },\n        children: /*#__PURE__*/_jsxDEV(Button, {\n          variant: \"contained\",\n          className: \"primary-button\",\n          onClick: () => window.location.reload(),\n          children: \"Ricarica la pagina\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 1116,\n          columnNumber: 13\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 1115,\n        columnNumber: 11\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 1104,\n      columnNumber: 9\n    }, this) : /*#__PURE__*/_jsxDEV(Box, {\n      children: [revisioniDisponibili.length > 0 && /*#__PURE__*/_jsxDEV(Paper, {\n        sx: {\n          p: 2,\n          mb: 3\n        },\n        children: /*#__PURE__*/_jsxDEV(Box, {\n          sx: {\n            display: 'flex',\n            alignItems: 'center',\n            gap: 2\n          },\n          children: [/*#__PURE__*/_jsxDEV(Typography, {\n            variant: \"h6\",\n            children: \"Visualizzazione:\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 1131,\n            columnNumber: 17\n          }, this), /*#__PURE__*/_jsxDEV(FormControl, {\n            size: \"small\",\n            sx: {\n              minWidth: 250\n            },\n            children: [/*#__PURE__*/_jsxDEV(InputLabel, {\n              children: \"Revisione da Visualizzare\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1133,\n              columnNumber: 19\n            }, this), /*#__PURE__*/_jsxDEV(Select, {\n              value: revisioneSelezionata || 'corrente',\n              onChange: handleRevisioneChange,\n              label: \"Revisione da Visualizzare\",\n              children: [/*#__PURE__*/_jsxDEV(MenuItem, {\n                value: \"corrente\",\n                children: [\"\\uD83D\\uDCCB Revisione Corrente \", revisioneCorrente && `(${revisioneCorrente})`]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 1139,\n                columnNumber: 21\n              }, this), revisioniDisponibili.map(rev => /*#__PURE__*/_jsxDEV(MenuItem, {\n                value: rev.revisione,\n                children: [\"\\uD83D\\uDCDA \", rev.revisione, \" (\", rev.cavi_count, \" cavi)\", rev.revisione === revisioneCorrente && ' - Attuale']\n              }, rev.revisione, true, {\n                fileName: _jsxFileName,\n                lineNumber: 1143,\n                columnNumber: 23\n              }, this))]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 1134,\n              columnNumber: 19\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 1132,\n            columnNumber: 17\n          }, this), /*#__PURE__*/_jsxDEV(Chip, {\n            label: revisioneSelezionata ? `Storico: ${revisioneSelezionata}` : `Corrente: ${revisioneCorrente || 'N/A'}`,\n            color: revisioneSelezionata ? \"secondary\" : \"primary\",\n            variant: \"outlined\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 1150,\n            columnNumber: 17\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 1130,\n          columnNumber: 15\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 1129,\n        columnNumber: 13\n      }, this), renderDashboard(), /*#__PURE__*/_jsxDEV(Box, {\n        sx: {\n          mt: 4\n        },\n        children: [selectionEnabled && getTotalSelectedCount() > 0 && /*#__PURE__*/_jsxDEV(Box, {\n          sx: {\n            mb: 2\n          },\n          children: /*#__PURE__*/_jsxDEV(Chip, {\n            label: `${getTotalSelectedCount()} cavi selezionati`,\n            color: \"primary\",\n            variant: \"outlined\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 1171,\n            columnNumber: 17\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 1170,\n          columnNumber: 15\n        }, this), process.env.NODE_ENV === 'development' && caviAttivi.length > 0 && /*#__PURE__*/_jsxDEV(Box, {\n          sx: {\n            mb: 2,\n            p: 1,\n            bgcolor: '#f0f0f0',\n            borderRadius: 1,\n            fontSize: '0.8rem',\n            fontFamily: 'monospace',\n            display: 'none'\n          },\n          children: Object.keys(caviAttivi[0]).map(key => /*#__PURE__*/_jsxDEV(\"div\", {\n            children: [key, \": \", JSON.stringify(caviAttivi[0][key])]\n          }, key, true, {\n            fileName: _jsxFileName,\n            lineNumber: 1183,\n            columnNumber: 19\n          }, this))\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 1181,\n          columnNumber: 15\n        }, this), /*#__PURE__*/_jsxDEV(CaviFilterableTable, {\n          cavi: caviAttivi,\n          loading: loading,\n          onFilteredDataChange: filteredData => console.log('Cavi attivi filtrati:', filteredData.length),\n          revisioneCorrente: ((_caviAttivi$ = caviAttivi[0]) === null || _caviAttivi$ === void 0 ? void 0 : _caviAttivi$.revisione_ufficiale) || ((_caviAttivi$2 = caviAttivi[0]) === null || _caviAttivi$2 === void 0 ? void 0 : _caviAttivi$2.revisione) || ((_caviAttivi$3 = caviAttivi[0]) === null || _caviAttivi$3 === void 0 ? void 0 : _caviAttivi$3.rev),\n          selectionEnabled: selectionEnabled,\n          selectedCavi: selectedCaviAttivi,\n          onSelectionChange: handleCaviAttiviSelectionChange,\n          onSelectionToggle: handleSelectionToggle,\n          contextMenuItems: getContextMenuItems,\n          onContextMenuAction: handleContextMenuAction,\n          onStatusAction: handleStatusAction\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 1188,\n          columnNumber: 13\n        }, this), caviAttivi.length === 0 && !loading && /*#__PURE__*/_jsxDEV(Alert, {\n          severity: \"info\",\n          sx: {\n            mt: 2\n          },\n          children: \"Nessun cavo attivo trovato. I cavi attivi appariranno qui.\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 1202,\n          columnNumber: 15\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 1167,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(Box, {\n        sx: {\n          mt: 4\n        },\n        children: [/*#__PURE__*/_jsxDEV(Box, {\n          sx: {\n            display: 'flex',\n            justifyContent: 'space-between',\n            alignItems: 'center',\n            mb: 2\n          },\n          children: /*#__PURE__*/_jsxDEV(Typography, {\n            variant: \"h5\",\n            children: [\"Cavi Spare \", caviSpare.length > 0 ? `(${caviSpare.length})` : '']\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 1211,\n            columnNumber: 15\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 1210,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(CaviFilterableTable, {\n          cavi: caviSpare,\n          loading: loading,\n          onFilteredDataChange: filteredData => console.log('Cavi spare filtrati:', filteredData.length),\n          selectionEnabled: selectionEnabled,\n          selectedCavi: selectedCaviSpare,\n          onSelectionChange: handleCaviSpareSelectionChange,\n          onSelectionToggle: handleSelectionToggle,\n          contextMenuItems: getContextMenuItems,\n          onContextMenuAction: handleContextMenuAction,\n          onStatusAction: handleStatusAction\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 1215,\n          columnNumber: 13\n        }, this), caviSpare.length === 0 && !loading && /*#__PURE__*/_jsxDEV(Alert, {\n          severity: \"info\",\n          sx: {\n            mt: 2\n          },\n          children: \"Nessun cavo SPARE trovato. I cavi marcati come SPARE appariranno qui.\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 1228,\n          columnNumber: 15\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 1209,\n        columnNumber: 11\n      }, this), renderDetailsDialog(), /*#__PURE__*/_jsxDEV(Dialog, {\n        open: openEliminaCavoDialog,\n        onClose: () => setOpenEliminaCavoDialog(false),\n        fullWidth: true,\n        maxWidth: \"md\",\n        children: /*#__PURE__*/_jsxDEV(PosaCaviCollegamenti, {\n          cantiereId: cantiereId,\n          onSuccess: message => {\n            // Chiudi il dialogo\n            setOpenEliminaCavoDialog(false);\n\n            // Se c'è un messaggio, è un'operazione completata con successo\n            if (message) {\n              // Mostra un messaggio di successo\n              console.log('Operazione completata:', message);\n              // Mostra un messaggio di successo con Snackbar\n              showNotification(message, 'success');\n              // Ricarica i dati dopo un ritardo per dare tempo al database di aggiornarsi\n              setTimeout(() => {\n                console.log('Ricaricamento dati dopo operazione...');\n                try {\n                  // Ricarica i dati invece di ricaricare la pagina\n                  fetchCavi(true);\n                } catch (error) {\n                  console.error('Errore durante il ricaricamento dei dati:', error);\n                  // Se fallisce, prova a ricaricare la pagina\n                  window.location.reload();\n                }\n              }, 1000);\n            } else {\n              // È un'operazione annullata, non mostrare messaggi\n              console.log('Operazione annullata dall\\'utente');\n            }\n          },\n          onError: message => {\n            // Mostra un messaggio di errore\n            console.error('Errore durante l\\'eliminazione del cavo:', message);\n            // Mostra un alert all'utente\n            alert(`Errore: ${message}`);\n            // Chiudi il dialogo\n            setOpenEliminaCavoDialog(false);\n            // Ricarica comunque i dati per assicurarsi che la vista sia aggiornata\n            fetchCavi();\n          },\n          initialOption: \"eliminaCavo\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 1246,\n          columnNumber: 13\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 1240,\n        columnNumber: 11\n      }, this), openModificaCavoDialog && console.log('VisualizzaCaviPage - cantiereId prima di aprire il dialog:', cantiereId), /*#__PURE__*/_jsxDEV(Dialog, {\n        open: openModificaCavoDialog,\n        onClose: () => setOpenModificaCavoDialog(false),\n        fullWidth: true,\n        maxWidth: \"sm\",\n        children: /*#__PURE__*/_jsxDEV(PosaCaviCollegamenti, {\n          cantiereId: cantiereId,\n          onSuccess: message => {\n            // Chiudi il dialogo\n            setOpenModificaCavoDialog(false);\n\n            // Se c'è un messaggio, è un'operazione completata con successo\n            if (message) {\n              // Mostra un messaggio di successo\n              console.log('Operazione completata:', message);\n              // Mostra un messaggio di successo con Snackbar\n              showNotification(message, 'success');\n              // Ricarica i dati immediatamente\n              console.log('Ricaricamento dati dopo operazione...');\n              // Ricarica i dati con un ritardo per dare tempo al database di aggiornarsi\n              setTimeout(() => {\n                try {\n                  fetchCavi(true);\n                } catch (error) {\n                  console.error('Errore durante il ricaricamento dei dati:', error);\n                  // Se fallisce, prova a ricaricare la pagina\n                  window.location.reload();\n                }\n              }, 1000);\n            } else {\n              // È un'operazione annullata, non mostrare messaggi\n              console.log('Operazione annullata dall\\'utente');\n            }\n          },\n          onError: message => {\n            // Mostra un messaggio di errore\n            console.error('Errore durante la modifica del cavo:', message);\n            // Mostra un alert all'utente\n            alert(`Errore: ${message}`);\n            // Chiudi il dialogo\n            setOpenModificaCavoDialog(false);\n            // Ricarica comunque i dati per assicurarsi che la vista sia aggiornata\n            console.log('Ricaricamento dati dopo errore...');\n            fetchCavi(true);\n          },\n          initialOption: \"modificaCavo\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 1299,\n          columnNumber: 13\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 1293,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(Dialog, {\n        open: openAggiungiCavoDialog,\n        onClose: () => setOpenAggiungiCavoDialog(false),\n        maxWidth: \"sm\",\n        fullWidth: true,\n        children: /*#__PURE__*/_jsxDEV(PosaCaviCollegamenti, {\n          cantiereId: cantiereId,\n          onSuccess: message => {\n            // Chiudi il dialogo\n            setOpenAggiungiCavoDialog(false);\n\n            // Se c'è un messaggio, è un'operazione completata con successo\n            if (message) {\n              // Mostra un messaggio di successo\n              console.log('Operazione completata:', message);\n              // Mostra un messaggio di successo con Snackbar\n              showNotification(message, 'success');\n              // Ricarica i dati dopo un ritardo per dare tempo al database di aggiornarsi\n              setTimeout(() => {\n                console.log('Ricaricamento dati dopo operazione...');\n                try {\n                  // Ricarica i dati in modalità silenziosa per evitare il \"blink\" della pagina\n                  fetchCavi(true);\n                } catch (error) {\n                  console.error('Errore durante il ricaricamento dei dati:', error);\n                  // Se fallisce, prova a ricaricare la pagina immediatamente\n                  console.log('Tentativo di ricaricamento della pagina...');\n                  window.location.reload();\n                }\n              }, 1000);\n            } else {\n              // È un'operazione annullata, non mostrare messaggi\n              console.log('Operazione annullata dall\\'utente');\n            }\n          },\n          onError: message => {\n            // Mostra un messaggio di errore\n            console.error('Errore durante l\\'aggiunta del cavo:', message);\n            // Mostra un messaggio di errore con Snackbar\n            showNotification(`Errore: ${message}`, 'error');\n            // Chiudi il dialogo\n            setOpenAggiungiCavoDialog(false);\n            // Ricarica comunque i dati per assicurarsi che la vista sia aggiornata\n            fetchCavi(true);\n          },\n          initialOption: \"aggiungiCavo\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 1345,\n          columnNumber: 13\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 1344,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(InserisciMetriDialog, {\n        open: inserisciMetriDialog.open,\n        onClose: handleCloseInserisciMetri,\n        cavo: inserisciMetriDialog.cavo,\n        onSave: handleSaveMetri,\n        loading: inserisciMetriDialog.loading\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 1390,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(ModificaBobinaDialog, {\n        open: modificaBobinaDialog.open,\n        onClose: handleCloseModificaBobina,\n        cavo: modificaBobinaDialog.cavo,\n        bobineDisponibili: bobineDisponibili,\n        onSave: handleModifyBobina,\n        loading: modificaBobinaDialog.loading\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 1398,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(Snackbar, {\n        open: notification.open,\n        autoHideDuration: 4000,\n        onClose: handleCloseNotification,\n        anchorOrigin: {\n          vertical: 'bottom',\n          horizontal: 'center'\n        },\n        children: /*#__PURE__*/_jsxDEV(Alert, {\n          onClose: handleCloseNotification,\n          severity: notification.severity,\n          sx: {\n            width: '100%'\n          },\n          children: notification.message\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 1414,\n          columnNumber: 13\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 1408,\n        columnNumber: 11\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 1126,\n      columnNumber: 9\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 1089,\n    columnNumber: 5\n  }, this);\n};\n_s(VisualizzaCaviPage, \"d3UQyyhIGDDAD3IPTUWpA9fMl7o=\", false, function () {\n  return [useAuth, useGlobalContext, useNavigate];\n});\n_c = VisualizzaCaviPage;\nexport default VisualizzaCaviPage;\nvar _c;\n$RefreshReg$(_c, \"VisualizzaCaviPage\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "Box", "Typography", "Paper", "<PERSON><PERSON>", "Grid", "Card", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "IconButton", "Chip", "CircularProgress", "LinearProgress", "Dialog", "DialogTitle", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "DialogActions", "Snackbar", "FormControl", "InputLabel", "Select", "MenuItem", "<PERSON><PERSON>", "InfoIcon", "Cable", "CableIcon", "CheckCircle", "CheckCircleIcon", "Schedule", "ScheduleIcon", "Link", "LinkIcon", "<PERSON><PERSON><PERSON>", "LinkOffIcon", "Timeline", "TimelineIcon", "CheckBox", "CheckBoxIcon", "CheckBoxOutlineBlank", "CheckBoxOutlineBlankIcon", "Visibility", "VisibilityIcon", "Edit", "EditIcon", "Delete", "DeleteIcon", "SelectAll", "SelectAllIcon", "ContentCopy", "CopyIcon", "Settings", "SettingsIcon", "Verified", "VerifiedIcon", "useNavigate", "useAuth", "useGlobalContext", "PosaCaviCollegamenti", "caviService", "parcoCaviService", "CavoForm", "normalizeInstallationStatus", "CaviFilterableTable", "InserisciMetriDialog", "ModificaBobinaDialog", "jsxDEV", "_jsxDEV", "VisualizzaCaviPage", "_s", "_caviAttivi$", "_caviAttivi$2", "_caviAttivi$3", "isImpersonating", "user", "openEliminaCavoDialog", "setOpenEliminaCavoDialog", "openModificaCavoDialog", "setOpenModificaCavoDialog", "openAggiungiCavoDialog", "setOpenAggiungiCavoDialog", "navigate", "cantiereId", "setCantiereId", "cantiereName", "setCantiereName", "caviAttivi", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "caviSpare", "setCaviSpare", "loading", "setLoading", "error", "setError", "notification", "setNotification", "open", "message", "severity", "selected<PERSON><PERSON><PERSON>", "setSelectedCavo", "detailsDialogOpen", "setDetailsDialogOpen", "selectionEnabled", "setSelectionEnabled", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "setSele<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "selectedCaviSpare", "setSelectedCaviSpare", "inserisciMetriDialog", "setInserisciMetriDialog", "cavo", "modificaBobinaDialog", "setModificaBobinaDialog", "bobine<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "setBobineDisponibili", "statistics", "setStatistics", "totaleCavi", "caviInstallati", "caviDaInstallare", "caviInCorso", "caviCollegati", "caviNonCollegati", "caviCertificati", "caviNonCertificati", "percentualeInstallazione", "percentualeCollegamento", "percentualeCertificazione", "metriTotali", "metriInstallati", "metriR<PERSON><PERSON><PERSON>", "revisioniDisponibili", "setRevisioniDisponibili", "revisioneSelezionata", "setRevisioneSelezionata", "revisioneCorrente", "setRevisioneCorrente", "calculateStatistics", "caviAttiviData", "caviSpareData", "tuttiCavi", "length", "console", "log", "totale", "filter", "stato_installazione", "colle<PERSON>nti", "responsabile_partenza", "responsabile_arrivo", "Math", "round", "reduce", "sum", "parseFloat", "metri_te<PERSON>ci", "metratura_reale", "newStatistics", "loadStatiInstallazione", "setStatiInstallazione", "loadRevisioni", "cantiereIdToUse", "revisioneCorrenteData", "getRevisioneCorrente", "revisione_corrente", "revisioniData", "getRevisioniDisponibili", "revisioni", "handleRevisioneChange", "event", "nuovaRevisione", "target", "value", "filters", "setFilters", "tipologia", "sort_by", "sort_order", "statiInstallazione", "tipologieCavi", "setTipologieCavi", "<PERSON><PERSON><PERSON>", "silentLoading", "localStorage", "getItem", "attivi", "get<PERSON><PERSON>", "attiviError", "caviSpareTra<PERSON>ttivi", "modificato_manualmente", "spare", "getCaviSpare", "spareError", "standardError", "setTimeout", "document", "body", "textContent", "includes", "window", "location", "reload", "fetchData", "token", "selectedCantiereId", "selectedCantiereName", "i", "key", "role", "cantiere_id", "toString", "cantiere_name", "setItem", "base64Url", "split", "base64", "replace", "jsonPayload", "decodeURIComponent", "atob", "map", "c", "charCodeAt", "slice", "join", "payload", "JSON", "parse", "e", "warn", "cantiereIdNum", "parseInt", "isNaN", "timeoutPromise", "Promise", "_", "reject", "Error", "caviPromise", "race", "caviError", "status", "data", "stack", "code", "name", "response", "statusText", "sparePromise", "err", "_err$response", "_err$response2", "_err$response3", "_err$response4", "_err$response5", "_err$response5$data", "errorMessage", "detail", "handleOpenDetails", "handleCloseDetails", "handleCloseNotification", "prev", "showNotification", "handleSelectionToggle", "handleCaviAttiviSelectionChange", "selectedIds", "handleCaviSpareSelectionChange", "getAllSelectedCavi", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "id_cavo", "selected<PERSON><PERSON><PERSON><PERSON><PERSON>", "getTotalSelectedCount", "handleContextMenuAction", "action", "confirm", "some", "isSelected", "id", "navigator", "clipboard", "writeText", "details", "sezione", "getContextMenuItems", "type", "label", "icon", "fontSize", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "onClick", "color", "shortcut", "description", "handleStatusAction", "actionType", "actionLabel", "bobine", "getBobine", "handleSaveMetri", "cavoId", "metri", "cavoAggiornato", "updateMetri<PERSON><PERSON><PERSON>", "updateCavo", "cavi", "handleModifyBobina", "nuovaBobinaId", "motivazione", "updateBobina", "id_bobina", "handleCloseInserisciMetri", "handleCloseModificaBobina", "renderDashboard", "sx", "p", "mb", "bgcolor", "children", "direction", "spacing", "alignItems", "justifyContent", "flexWrap", "variant", "fontWeight", "lineHeight", "width", "height", "borderRadius", "display", "renderDetailsDialog", "onClose", "max<PERSON><PERSON><PERSON>", "fullWidth", "dividers", "container", "item", "xs", "md", "gutterBottom", "sistema", "utility", "colore_cavo", "ubicazione_partenza", "utenza_partenza", "descrizione_utenza_partenza", "comanda_partenza", "ubicazione_arrivo", "utenza_arrivo", "descrizione_utenza_arrivo", "comanda_arrivo", "responsabile_posa", "comanda_posa", "Date", "timestamp", "toLocaleString", "className", "flexDirection", "mt", "size", "gap", "min<PERSON><PERSON><PERSON>", "onChange", "rev", "revisione", "cavi_count", "process", "env", "NODE_ENV", "fontFamily", "Object", "keys", "stringify", "onFilteredDataChange", "filteredData", "revisione_ufficiale", "<PERSON><PERSON><PERSON>", "onSelectionChange", "onSelectionToggle", "contextMenuItems", "onContextMenuAction", "onStatusAction", "onSuccess", "onError", "alert", "initialOption", "onSave", "autoHideDuration", "anchor<PERSON><PERSON><PERSON>", "vertical", "horizontal", "_c", "$RefreshReg$"], "sources": ["C:/CMS/webapp/frontend/src/pages/cavi/VisualizzaCaviPage.js"], "sourcesContent": ["import React, { useState, useEffect } from 'react';\nimport {\n  Box,\n  Typography,\n  Paper,\n  Button,\n  Grid,\n  Card,\n  CardContent,\n  Alert,\n  IconButton,\n  Chip,\n  CircularProgress,\n  LinearProgress,\n  Dialog,\n  DialogTitle,\n  DialogContent,\n  DialogActions,\n  Snackbar,\n  FormControl,\n  InputLabel,\n  Select,\n  MenuItem,\n  Stack\n} from '@mui/material';\nimport InfoIcon from '@mui/icons-material/Info';\nimport {\n  Cable as CableIcon,\n  CheckCircle as CheckCircleIcon,\n  Schedule as ScheduleIcon,\n  Link as LinkIcon,\n  LinkOff as LinkOffIcon,\n  Timeline as TimelineIcon,\n  CheckBox as CheckBoxIcon,\n  CheckBoxOutlineBlank as CheckBoxOutlineBlankIcon,\n  Visibility as VisibilityIcon,\n  Edit as EditIcon,\n  Delete as DeleteIcon,\n  SelectAll as SelectAllIcon,\n  ContentCopy as CopyIcon,\n  Settings as SettingsIcon,\n  Verified as VerifiedIcon\n} from '@mui/icons-material';\nimport { useNavigate } from 'react-router-dom';\nimport { useAuth } from '../../context/AuthContext';\nimport { useGlobalContext } from '../../context/GlobalContext';\nimport PosaCaviCollegamenti from '../../components/cavi/PosaCaviCollegamenti';\nimport caviService from '../../services/caviService';\nimport parcoCaviService from '../../services/parcoCaviService';\nimport CavoForm from '../../components/cavi/CavoForm';\nimport { normalizeInstallationStatus } from '../../utils/validationUtils';\nimport CaviFilterableTable from '../../components/cavi/CaviFilterableTable';\nimport InserisciMetriDialog from '../../components/cavi/InserisciMetriDialog';\nimport ModificaBobinaDialog from '../../components/cavi/ModificaBobinaDialog';\nimport './CaviPage.css';\n\nconst VisualizzaCaviPage = () => {\n  const { isImpersonating, user } = useAuth();\n  const { openEliminaCavoDialog, setOpenEliminaCavoDialog, openModificaCavoDialog, setOpenModificaCavoDialog, openAggiungiCavoDialog, setOpenAggiungiCavoDialog } = useGlobalContext();\n  const navigate = useNavigate();\n  const [cantiereId, setCantiereId] = useState(null);\n  const [cantiereName, setCantiereName] = useState('');\n  const [caviAttivi, setCaviAttivi] = useState([]);\n  const [caviSpare, setCaviSpare] = useState([]);\n  const [loading, setLoading] = useState(true);\n  const [error, setError] = useState(null);\n  // Stato per le notifiche\n  const [notification, setNotification] = useState({ open: false, message: '', severity: 'success' });\n  // Rimosso stato viewMode\n\n  // Stato per il dialogo dei dettagli del cavo\n  const [selectedCavo, setSelectedCavo] = useState(null);\n  const [detailsDialogOpen, setDetailsDialogOpen] = useState(false);\n\n  // Stati per la selezione dei cavi\n  const [selectionEnabled, setSelectionEnabled] = useState(false);\n  const [selectedCaviAttivi, setSelectedCaviAttivi] = useState([]);\n  const [selectedCaviSpare, setSelectedCaviSpare] = useState([]);\n\n  // Stati per i dialoghi di azione sui pulsanti stato\n  const [inserisciMetriDialog, setInserisciMetriDialog] = useState({\n    open: false,\n    cavo: null,\n    loading: false\n  });\n  const [modificaBobinaDialog, setModificaBobinaDialog] = useState({\n    open: false,\n    cavo: null,\n    loading: false\n  });\n  const [bobineDisponibili, setBobineDisponibili] = useState([]);\n\n  // Stati per statistiche avanzate\n  const [statistics, setStatistics] = useState({\n    totaleCavi: 0,\n    caviInstallati: 0,\n    caviDaInstallare: 0,\n    caviInCorso: 0,\n    caviCollegati: 0,\n    caviNonCollegati: 0,\n    caviCertificati: 0,\n    caviNonCertificati: 0,\n    percentualeInstallazione: 0,\n    percentualeCollegamento: 0,\n    percentualeCertificazione: 0,\n    metriTotali: 0,\n    metriInstallati: 0,\n    metriRimanenti: 0\n  });\n\n\n\n  // Stato per la gestione delle revisioni\n  const [revisioniDisponibili, setRevisioniDisponibili] = useState([]);\n  const [revisioneSelezionata, setRevisioneSelezionata] = useState('');\n  const [revisioneCorrente, setRevisioneCorrente] = useState('');\n\n  // Rimosso stato per il debug\n\n  // Funzione per calcolare le statistiche avanzate\n  const calculateStatistics = (caviAttiviData, caviSpareData) => {\n    const tuttiCavi = [...(caviAttiviData || []), ...(caviSpareData || [])];\n\n    if (tuttiCavi.length === 0) {\n      console.log('Nessun cavo disponibile per il calcolo delle statistiche');\n      return;\n    }\n\n    console.log('Calcolo statistiche con dati:', {\n      caviAttivi: caviAttiviData?.length || 0,\n      caviSpare: caviSpareData?.length || 0,\n      totale: tuttiCavi.length\n    });\n\n    const totaleCavi = tuttiCavi.length;\n\n    // Calcola stati di installazione\n    const caviInstallati = tuttiCavi.filter(cavo =>\n      cavo.stato_installazione === 'Installato' ||\n      cavo.stato_installazione === 'INSTALLATO' ||\n      cavo.stato_installazione === 'POSATO'\n    ).length;\n\n    const caviDaInstallare = tuttiCavi.filter(cavo =>\n      cavo.stato_installazione === 'Da installare' ||\n      cavo.stato_installazione === 'DA_INSTALLARE'\n    ).length;\n\n    const caviInCorso = tuttiCavi.filter(cavo =>\n      cavo.stato_installazione === 'In corso' ||\n      cavo.stato_installazione === 'IN_CORSO'\n    ).length;\n\n    // Calcola stati di collegamento\n    const caviCollegati = tuttiCavi.filter(cavo =>\n      cavo.collegamenti === 3 &&\n      cavo.responsabile_partenza &&\n      cavo.responsabile_arrivo\n    ).length;\n\n    const caviNonCollegati = totaleCavi - caviCollegati;\n\n    // Calcola percentuali\n    const percentualeInstallazione = totaleCavi > 0 ? Math.round((caviInstallati / totaleCavi) * 100) : 0;\n    const percentualeCollegamento = totaleCavi > 0 ? Math.round((caviCollegati / totaleCavi) * 100) : 0;\n\n    // Calcola metri\n    const metriTotali = tuttiCavi.reduce((sum, cavo) => sum + (parseFloat(cavo.metri_teorici) || 0), 0);\n    const metriInstallati = tuttiCavi\n      .filter(cavo => cavo.stato_installazione === 'Installato' || cavo.stato_installazione === 'INSTALLATO' || cavo.stato_installazione === 'POSATO')\n      .reduce((sum, cavo) => sum + (parseFloat(cavo.metratura_reale) || parseFloat(cavo.metri_teorici) || 0), 0);\n    const metriRimanenti = metriTotali - metriInstallati;\n\n    // Calcola certificazioni (per ora placeholder, sarà implementato)\n    const caviCertificati = 0; // TODO: Implementare caricamento certificazioni\n    const caviNonCertificati = totaleCavi - caviCertificati;\n    const percentualeCertificazione = totaleCavi > 0 ? Math.round((caviCertificati / totaleCavi) * 100) : 0;\n\n    const newStatistics = {\n      totaleCavi,\n      caviInstallati,\n      caviDaInstallare,\n      caviInCorso,\n      caviCollegati,\n      caviNonCollegati,\n      caviCertificati,\n      caviNonCertificati,\n      percentualeInstallazione,\n      percentualeCollegamento,\n      percentualeCertificazione,\n      metriTotali: Math.round(metriTotali),\n      metriInstallati: Math.round(metriInstallati),\n      metriRimanenti: Math.round(metriRimanenti)\n    };\n\n    console.log('Nuove statistiche calcolate:', newStatistics);\n    setStatistics(newStatistics);\n  };\n\n  // Funzione per caricare gli stati di installazione disponibili\n  const loadStatiInstallazione = () => {\n    // Usa i valori dell'enum StatoInstallazione\n    setStatiInstallazione(['Installato', 'Da installare', 'In corso']);\n  };\n\n  // Funzione per caricare le revisioni disponibili\n  const loadRevisioni = async (cantiereIdToUse) => {\n    try {\n      console.log('Caricamento revisioni per cantiere:', cantiereIdToUse);\n\n      // Carica la revisione corrente\n      const revisioneCorrenteData = await caviService.getRevisioneCorrente(cantiereIdToUse);\n      console.log('Revisione corrente:', revisioneCorrenteData);\n      setRevisioneCorrente(revisioneCorrenteData.revisione_corrente);\n\n      // Carica tutte le revisioni disponibili\n      const revisioniData = await caviService.getRevisioniDisponibili(cantiereIdToUse);\n      console.log('Revisioni disponibili:', revisioniData);\n      setRevisioniDisponibili(revisioniData.revisioni || []);\n\n      // LOGICA REVISIONI: La revisione corrente è quella di default\n      // Non impostiamo una revisione selezionata, così il sistema usa automaticamente la corrente\n      console.log('Logica revisioni: usando revisione corrente di default');\n    } catch (error) {\n      console.error('Errore nel caricamento delle revisioni:', error);\n    }\n  };\n\n  // Funzione per gestire il cambio di revisione\n  const handleRevisioneChange = (event) => {\n    const nuovaRevisione = event.target.value;\n\n    // LOGICA REVISIONI:\n    // - Se vuoto o \"corrente\" -> usa revisione corrente (non specificare parametro)\n    // - Se specifica -> usa quella revisione per visualizzazione storica\n    if (nuovaRevisione === '' || nuovaRevisione === 'corrente') {\n      setRevisioneSelezionata('');\n      console.log('Passaggio a revisione corrente (default)');\n    } else {\n      setRevisioneSelezionata(nuovaRevisione);\n      console.log('Passaggio a revisione storica:', nuovaRevisione);\n    }\n  };\n\n  // Stato per filtri e ordinamento\n  const [filters, setFilters] = useState({\n    stato_installazione: '',\n    tipologia: '',\n    sort_by: '',\n    sort_order: 'asc'\n  });\n\n  // Opzioni per i filtri\n  const [statiInstallazione, setStatiInstallazione] = useState([]);\n  const [tipologieCavi, setTipologieCavi] = useState([]);\n\n  // Rimossa funzione di debug\n\n  // Funzione per caricare i cavi\n  // Il parametro silentLoading permette di evitare di mostrare lo stato di caricamento\n  const fetchCavi = async (silentLoading = false) => {\n    try {\n      if (!silentLoading) {\n        setLoading(true);\n      }\n      console.log('Caricamento cavi per cantiere:', cantiereId);\n\n      // Verifica che cantiereId sia valido\n      if (!cantiereId) {\n        console.error('fetchCavi: cantiereId non valido:', cantiereId);\n        setError('ID cantiere non valido o mancante. Ricarica la pagina.');\n        setLoading(false);\n        return;\n      }\n\n      // Recupera il cantiereId dal localStorage come fallback\n      let cantiereIdToUse = cantiereId;\n      if (!cantiereIdToUse) {\n        cantiereIdToUse = localStorage.getItem('selectedCantiereId');\n        console.log('Usando cantiereId dal localStorage:', cantiereIdToUse);\n        if (!cantiereIdToUse) {\n          console.error('Impossibile trovare un ID cantiere valido');\n          setError('ID cantiere non trovato. Ricarica la pagina.');\n          setLoading(false);\n          return;\n        }\n      }\n\n      // Carica i cavi attivi\n      console.log('Caricamento cavi attivi (tipo_cavo=0)...');\n      let attivi = [];\n      try {\n        attivi = await caviService.getCavi(cantiereIdToUse, 0, filters);\n        console.log('Cavi attivi caricati:', attivi ? attivi.length : 0);\n      } catch (attiviError) {\n        console.error('Errore nel caricamento dei cavi attivi:', attiviError);\n        // Continua con un array vuoto\n        attivi = [];\n      }\n\n      // Verifica se ci sono cavi con modificato_manualmente = 3 tra i cavi attivi\n      if (attivi && attivi.length > 0) {\n        const caviSpareTraAttivi = attivi.filter(cavo => cavo.modificato_manualmente === 3);\n        if (caviSpareTraAttivi.length > 0) {\n          console.error('ERRORE: Trovati cavi con modificato_manualmente = 3 tra i cavi attivi:', caviSpareTraAttivi);\n        }\n      }\n\n      setCaviAttivi(attivi || []);\n\n      // Carica i cavi SPARE con la nuova funzione dedicata\n      let spare = [];\n      try {\n        console.log('Caricamento cavi SPARE con funzione dedicata...');\n        spare = await caviService.getCaviSpare(cantiereIdToUse);\n        console.log('Cavi SPARE caricati con funzione dedicata:', spare ? spare.length : 0);\n        if (spare && spare.length > 0) {\n          console.log('Primo cavo SPARE:', spare[0]);\n        }\n      } catch (spareError) {\n        console.error('Errore nel caricamento dei cavi SPARE con funzione dedicata:', spareError);\n        // Se fallisce, prova con il metodo standard\n        try {\n          console.log('Tentativo con metodo standard...');\n          spare = await caviService.getCavi(cantiereIdToUse, 3);\n          console.log('Cavi SPARE caricati con metodo standard:', spare ? spare.length : 0);\n        } catch (standardError) {\n          console.error('Errore anche con metodo standard:', standardError);\n          // Continua con un array vuoto\n          spare = [];\n        }\n      }\n      setCaviSpare(spare || []);\n\n\n\n      // Se siamo arrivati qui, rimuovi eventuali messaggi di errore precedenti\n      setError('');\n    } catch (error) {\n      console.error('Errore generale nel caricamento dei cavi:', error);\n      setError(`Errore nel caricamento dei cavi: ${error.message || 'Errore sconosciuto'}`);\n\n      // Prova a ricaricare la pagina dopo un ritardo se l'errore persiste\n      setTimeout(() => {\n        // Verifica se siamo ancora in errore\n        if (document.body.textContent.includes('Errore nel caricamento dei cavi')) {\n          console.log('Errore persistente, tentativo di ricaricamento della pagina...');\n          window.location.reload();\n        }\n      }, 5000); // 5 secondi di ritardo\n    } finally {\n      if (!silentLoading) {\n        setLoading(false);\n      }\n    }\n  };\n\n  // Carica i dati del cantiere e dei cavi\n  useEffect(() => {\n    // Carica gli stati di installazione all'avvio\n    loadStatiInstallazione();\n\n    const fetchData = async () => {\n      try {\n        console.log('Inizializzazione VisualizzaCaviPage...');\n\n        // Verifica che l'utente sia autenticato\n        const token = localStorage.getItem('token');\n        console.log('Token presente:', !!token);\n        if (!token) {\n          setError('Sessione scaduta. Effettua nuovamente il login.');\n          setLoading(false);\n          return;\n        }\n\n        // Recupera l'ID del cantiere selezionato dal localStorage\n        let selectedCantiereId = localStorage.getItem('selectedCantiereId');\n        let selectedCantiereName = localStorage.getItem('selectedCantiereName');\n\n        console.log('Cantiere selezionato dal localStorage:', { selectedCantiereId, selectedCantiereName });\n        console.log('Dati utente:', user);\n\n        // Stampa tutti i dati nel localStorage per debug\n        console.log('DEBUG - Tutti i dati nel localStorage:');\n        for (let i = 0; i < localStorage.length; i++) {\n          const key = localStorage.key(i);\n          console.log(`${key}: ${localStorage.getItem(key)}`);\n        }\n\n        // SOLUZIONE DIRETTA: Ottieni l'ID del cantiere direttamente dal token JWT\n        if (user?.role === 'cantieri_user') {\n          console.log('Utente cantiere rilevato, tentativo di recupero ID cantiere dai dati utente');\n\n          // Verifica se l'utente ha un ID cantiere nei dati utente\n          if (user.cantiere_id) {\n            console.log('Trovato ID cantiere nei dati utente:', user.cantiere_id);\n            selectedCantiereId = user.cantiere_id.toString();\n            selectedCantiereName = user.cantiere_name || `Cantiere ${user.cantiere_id}`;\n\n            // Salva l'ID e il nome del cantiere nel localStorage\n            localStorage.setItem('selectedCantiereId', selectedCantiereId);\n            localStorage.setItem('selectedCantiereName', selectedCantiereName);\n            console.log('Salvato ID cantiere nel localStorage:', selectedCantiereId);\n          } else {\n            // Tentativo di recupero dal token JWT\n            try {\n              console.log('Tentativo di decodifica del token JWT per recuperare l\\'ID cantiere');\n              const token = localStorage.getItem('token');\n              if (token) {\n                // Decodifica il token JWT (senza verifica della firma)\n                const base64Url = token.split('.')[1];\n                const base64 = base64Url.replace(/-/g, '+').replace(/_/g, '/');\n                const jsonPayload = decodeURIComponent(atob(base64).split('').map(c => {\n                  return '%' + ('00' + c.charCodeAt(0).toString(16)).slice(-2);\n                }).join(''));\n\n                const payload = JSON.parse(jsonPayload);\n                console.log('Payload del token JWT:', payload);\n\n                if (payload.cantiere_id) {\n                  console.log('Trovato ID cantiere nel token JWT:', payload.cantiere_id);\n                  selectedCantiereId = payload.cantiere_id.toString();\n                  // Usa un nome generico se non disponibile\n                  selectedCantiereName = `Cantiere ${payload.cantiere_id}`;\n\n                  // Salva l'ID e il nome del cantiere nel localStorage\n                  localStorage.setItem('selectedCantiereId', selectedCantiereId);\n                  localStorage.setItem('selectedCantiereName', selectedCantiereName);\n                  console.log('Salvato ID cantiere nel localStorage:', selectedCantiereId);\n                }\n              }\n            } catch (e) {\n              console.error('Errore durante la decodifica del token JWT:', e);\n            }\n          }\n        }\n\n        // SOLUZIONE TEMPORANEA: Se ancora non abbiamo un ID cantiere, usa un valore hardcoded per debug\n        if (!selectedCantiereId || selectedCantiereId === 'undefined' || selectedCantiereId === 'null') {\n          console.warn('ATTENZIONE: Nessun ID cantiere trovato, utilizzo valore hardcoded per debug');\n          // Usa il primo cantiere disponibile (questo è solo per debug)\n          selectedCantiereId = '1'; // Sostituisci con un ID cantiere valido nel tuo database\n          selectedCantiereName = 'Cantiere Debug';\n\n          // Salva l'ID e il nome del cantiere nel localStorage\n          localStorage.setItem('selectedCantiereId', selectedCantiereId);\n          localStorage.setItem('selectedCantiereName', selectedCantiereName);\n          console.log('Salvato ID cantiere hardcoded nel localStorage:', selectedCantiereId);\n        }\n\n        // Verifica finale\n        if (!selectedCantiereId) {\n          setError('Nessun cantiere selezionato. Torna alla pagina dei cantieri.');\n          setLoading(false);\n          return;\n        }\n\n        // Verifica che l'ID del cantiere sia un numero valido\n        const cantiereIdNum = parseInt(selectedCantiereId, 10);\n        console.log('ID cantiere convertito a numero:', cantiereIdNum);\n        if (isNaN(cantiereIdNum)) {\n          setError(`ID cantiere non valido: ${selectedCantiereId}. Torna alla pagina dei cantieri.`);\n          setLoading(false);\n          return;\n        }\n\n        // Usa il numero convertito, non la stringa\n        setCantiereId(cantiereIdNum);\n        setCantiereName(selectedCantiereName || `Cantiere ${cantiereIdNum}`);\n\n        // Carica le revisioni disponibili\n        await loadRevisioni(cantiereIdNum);\n\n\n\n        // Carica i cavi attivi con gestione degli errori migliorata\n        console.log('Caricamento cavi attivi per cantiere:', cantiereIdNum);\n        try {\n          // Imposta un timeout per evitare che la richiesta rimanga bloccata\n          const timeoutPromise = new Promise((_, reject) => {\n            setTimeout(() => reject(new Error('Timeout durante il caricamento dei cavi attivi')), 30000); // Aumentato a 30 secondi\n          });\n\n          // Esegui la richiesta con un timeout di sicurezza e applica i filtri\n          console.log('Iniziando chiamata API per cavi attivi con filtri:', filters);\n          const caviPromise = caviService.getCavi(cantiereIdNum, 0, filters);\n          const attivi = await Promise.race([caviPromise, timeoutPromise]);\n\n          console.log('Cavi attivi caricati:', attivi);\n          console.log('Numero di cavi attivi trovati:', attivi ? attivi.length : 0);\n          if (attivi && attivi.length > 0) {\n            console.log('Primo cavo attivo:', attivi[0]);\n          } else {\n            console.warn('Nessun cavo attivo trovato per il cantiere', cantiereIdNum);\n          }\n          setCaviAttivi(attivi || []);\n\n          // Calcola le statistiche dopo aver caricato i cavi attivi\n          calculateStatistics(attivi || [], caviSpare);\n        } catch (caviError) {\n          console.error('Errore nel caricamento dei cavi attivi:', caviError);\n          console.error('Dettagli errore cavi attivi:', {\n            message: caviError.message,\n            status: caviError.status,\n            data: caviError.data,\n            stack: caviError.stack,\n            code: caviError.code,\n            name: caviError.name,\n            response: caviError.response ? {\n              status: caviError.response.status,\n              statusText: caviError.response.statusText,\n              data: caviError.response.data\n            } : 'No response'\n          });\n\n          // Non interrompere il flusso, continua con i cavi spare\n          setCaviAttivi([]);\n          console.warn('Continuazione del flusso dopo errore nei cavi attivi');\n\n          // Aggiungi un messaggio di errore visibile all'utente\n          setError(`Errore nel caricamento dei cavi attivi: ${caviError.message}. Controlla la console per maggiori dettagli.`);\n        }\n\n        // Carica i cavi spare con gestione degli errori migliorata\n        console.log('Caricamento cavi spare per cantiere:', cantiereIdNum);\n        try {\n          // Imposta un timeout per evitare che la richiesta rimanga bloccata\n          const timeoutPromise = new Promise((_, reject) => {\n            setTimeout(() => reject(new Error('Timeout durante il caricamento dei cavi spare')), 30000); // Aumentato a 30 secondi\n          });\n\n          // Esegui la richiesta con un timeout di sicurezza\n          console.log('Iniziando chiamata API per cavi spare...');\n          // Non applichiamo i filtri ai cavi spare, solo agli attivi\n          const sparePromise = caviService.getCavi(cantiereIdNum, 3);\n          const spare = await Promise.race([sparePromise, timeoutPromise]);\n\n          console.log('Cavi spare caricati:', spare);\n          console.log('Numero di cavi spare trovati:', spare ? spare.length : 0);\n          if (spare && spare.length > 0) {\n            console.log('Primo cavo spare:', spare[0]);\n          } else {\n            console.warn('Nessun cavo spare trovato per il cantiere', cantiereIdNum);\n          }\n          setCaviSpare(spare || []);\n\n          // Calcola le statistiche dopo aver caricato i cavi spare\n          calculateStatistics(caviAttivi, spare || []);\n        } catch (spareError) {\n          console.error('Errore nel caricamento dei cavi spare:', spareError);\n          console.error('Dettagli errore cavi spare:', {\n            message: spareError.message,\n            status: spareError.status,\n            data: spareError.data,\n            stack: spareError.stack,\n            code: spareError.code,\n            name: spareError.name,\n            response: spareError.response ? {\n              status: spareError.response.status,\n              statusText: spareError.response.statusText,\n              data: spareError.response.data\n            } : 'No response'\n          });\n\n          // Non interrompere il flusso, imposta un array vuoto\n          setCaviSpare([]);\n\n          // Aggiungi un messaggio di errore visibile all'utente se non c'è già un errore per i cavi attivi\n          if (!error) {\n            setError(`Errore nel caricamento dei cavi spare: ${spareError.message}. Controlla la console per maggiori dettagli.`);\n          }\n        }\n\n        // Se siamo arrivati qui, almeno abbiamo caricato l'interfaccia di base\n        setLoading(false);\n\n      } catch (err) {\n        console.error('Errore nel caricamento dei cavi:', err);\n        console.error('Dettagli errore generale:', {\n          message: err.message,\n          status: err.status || err.response?.status,\n          data: err.data || err.response?.data,\n          stack: err.stack\n        });\n\n        // Estrai il messaggio di errore dettagliato\n        let errorMessage = 'Errore sconosciuto';\n\n        if (err.message && err.message.includes('ID cantiere non valido')) {\n          errorMessage = err.message;\n        } else if (err.status === 401 || err.status === 403 ||\n                  err.response?.status === 401 || err.response?.status === 403) {\n          errorMessage = 'Sessione scaduta o non autorizzata. Effettua nuovamente il login.';\n        } else if (err.response?.data?.detail) {\n          // Estrai il messaggio di errore dettagliato dall'API\n          errorMessage = `Errore API: ${err.response.data.detail}`;\n        } else if (err.code === 'ERR_NETWORK') {\n          // Errore di rete\n          errorMessage = 'Network Error. Verifica che il backend sia in esecuzione e accessibile.';\n        } else if (err.message) {\n          errorMessage = err.message;\n        }\n\n        setError(`Impossibile caricare i cavi: ${errorMessage}. Riprova più tardi.`);\n\n        // Imposta array vuoti per evitare errori di rendering\n        setCaviAttivi([]);\n        setCaviSpare([]);\n      } finally {\n        setLoading(false);\n      }\n    };\n\n    fetchData();\n  }, [filters]); // Ricarica i dati quando cambiano i filtri\n\n  // I filtri sono ora gestiti dal componente CaviFilterableTable\n\n  // Funzione per aprire il dialogo dei dettagli del cavo\n  const handleOpenDetails = (cavo) => {\n    setSelectedCavo(cavo);\n    setDetailsDialogOpen(true);\n  };\n\n  // Funzione per chiudere il dialogo dei dettagli del cavo\n  const handleCloseDetails = () => {\n    setDetailsDialogOpen(false);\n    setSelectedCavo(null);\n  };\n\n  // Funzione per chiudere la notifica\n  const handleCloseNotification = () => {\n    setNotification(prev => ({ ...prev, open: false }));\n  };\n\n  // Funzione per mostrare una notifica\n  const showNotification = (message, severity = 'success') => {\n    setNotification({ open: true, message, severity });\n  };\n\n  // Funzioni per gestire la selezione dei cavi\n  const handleSelectionToggle = () => {\n    setSelectionEnabled(!selectionEnabled);\n    // Pulisci le selezioni quando si disabilita la modalità selezione\n    if (selectionEnabled) {\n      setSelectedCaviAttivi([]);\n      setSelectedCaviSpare([]);\n    }\n  };\n\n  const handleCaviAttiviSelectionChange = (selectedIds) => {\n    setSelectedCaviAttivi(selectedIds);\n  };\n\n  const handleCaviSpareSelectionChange = (selectedIds) => {\n    setSelectedCaviSpare(selectedIds);\n  };\n\n  // Funzione per ottenere tutti i cavi selezionati\n  const getAllSelectedCavi = () => {\n    const selectedAttiviCavi = caviAttivi.filter(cavo => selectedCaviAttivi.includes(cavo.id_cavo));\n    const selectedSpareCavi = caviSpare.filter(cavo => selectedCaviSpare.includes(cavo.id_cavo));\n    return [...selectedAttiviCavi, ...selectedSpareCavi];\n  };\n\n  // Funzione per ottenere il conteggio totale dei cavi selezionati\n  const getTotalSelectedCount = () => {\n    return selectedCaviAttivi.length + selectedCaviSpare.length;\n  };\n\n  // Funzioni per gestire le azioni del menu contestuale\n  const handleContextMenuAction = (cavo, action) => {\n    console.log('Azione menu contestuale:', action, 'per cavo:', cavo);\n\n    switch (action) {\n      case 'view_details':\n        handleOpenDetails(cavo);\n        break;\n      case 'edit':\n        // Implementa logica di modifica\n        showNotification(`Modifica cavo ${cavo.id_cavo} - Funzione da implementare`, 'info');\n        break;\n      case 'delete':\n        // Implementa logica di eliminazione\n        if (window.confirm(`Sei sicuro di voler eliminare il cavo ${cavo.id_cavo}?`)) {\n          showNotification(`Eliminazione cavo ${cavo.id_cavo} - Funzione da implementare`, 'warning');\n        }\n        break;\n      case 'select':\n        if (caviAttivi.some(c => c.id_cavo === cavo.id_cavo)) {\n          // È un cavo attivo\n          const isSelected = selectedCaviAttivi.includes(cavo.id_cavo);\n          if (isSelected) {\n            setSelectedCaviAttivi(prev => prev.filter(id => id !== cavo.id_cavo));\n          } else {\n            setSelectedCaviAttivi(prev => [...prev, cavo.id_cavo]);\n          }\n        } else {\n          // È un cavo spare\n          const isSelected = selectedCaviSpare.includes(cavo.id_cavo);\n          if (isSelected) {\n            setSelectedCaviSpare(prev => prev.filter(id => id !== cavo.id_cavo));\n          } else {\n            setSelectedCaviSpare(prev => [...prev, cavo.id_cavo]);\n          }\n        }\n        // Abilita automaticamente la modalità selezione se non è già attiva\n        if (!selectionEnabled) {\n          setSelectionEnabled(true);\n        }\n        break;\n      case 'copy_id':\n        navigator.clipboard.writeText(cavo.id_cavo);\n        showNotification(`ID cavo ${cavo.id_cavo} copiato negli appunti`, 'success');\n        break;\n      case 'copy_details':\n        const details = `ID: ${cavo.id_cavo}\\nTipologia: ${cavo.tipologia}\\nSezione: ${cavo.sezione}\\nMetri: ${cavo.metri_teorici}`;\n        navigator.clipboard.writeText(details);\n        showNotification('Dettagli cavo copiati negli appunti', 'success');\n        break;\n      default:\n        console.warn('Azione non riconosciuta:', action);\n    }\n  };\n\n  // Definizione degli elementi del menu contestuale\n  const getContextMenuItems = (cavo) => {\n    const isSelected = caviAttivi.some(c => c.id_cavo === cavo?.id_cavo)\n      ? selectedCaviAttivi.includes(cavo?.id_cavo)\n      : selectedCaviSpare.includes(cavo?.id_cavo);\n\n    return [\n      {\n        type: 'header',\n        label: `Cavo ${cavo?.id_cavo || ''}`\n      },\n      {\n        id: 'view_details',\n        label: 'Visualizza Dettagli',\n        icon: <VisibilityIcon fontSize=\"small\" />,\n        action: 'view_details',\n        onClick: handleContextMenuAction\n      },\n      {\n        type: 'divider'\n      },\n      {\n        id: 'edit',\n        label: 'Modifica',\n        icon: <EditIcon fontSize=\"small\" />,\n        action: 'edit',\n        onClick: handleContextMenuAction,\n        color: 'primary'\n      },\n      {\n        id: 'delete',\n        label: 'Elimina',\n        icon: <DeleteIcon fontSize=\"small\" />,\n        action: 'delete',\n        onClick: handleContextMenuAction,\n        color: 'error'\n      },\n      {\n        type: 'divider'\n      },\n      {\n        id: 'select',\n        label: isSelected ? 'Deseleziona' : 'Seleziona',\n        icon: <SelectAllIcon fontSize=\"small\" />,\n        action: 'select',\n        onClick: handleContextMenuAction,\n        color: isSelected ? 'warning' : 'success'\n      },\n      {\n        type: 'divider'\n      },\n      {\n        id: 'copy_id',\n        label: 'Copia ID',\n        icon: <CopyIcon fontSize=\"small\" />,\n        action: 'copy_id',\n        onClick: handleContextMenuAction,\n        shortcut: 'Ctrl+C'\n      },\n      {\n        id: 'copy_details',\n        label: 'Copia Dettagli',\n        icon: <CopyIcon fontSize=\"small\" />,\n        action: 'copy_details',\n        onClick: handleContextMenuAction,\n        description: 'Copia ID, tipologia, sezione e metri'\n      }\n    ];\n  };\n\n  // Funzioni per gestire le azioni sui pulsanti stato\n  const handleStatusAction = async (cavo, actionType, actionLabel) => {\n    console.log('🎯 CLICK PULSANTE STATO RILEVATO!');\n    console.log('Azione pulsante stato:', actionType, 'per cavo:', cavo);\n    console.log('Action label:', actionLabel);\n\n    if (actionType === 'insert_meters') {\n      // Apri il dialogo per inserire i metri posati\n      setInserisciMetriDialog({\n        open: true,\n        cavo: cavo,\n        loading: false\n      });\n    } else if (actionType === 'modify_reel') {\n      // Carica le bobine disponibili e apri il dialogo per modificare la bobina\n      try {\n        console.log('Caricamento bobine per cantiere:', cantiereId);\n        const bobine = await parcoCaviService.getBobine(cantiereId);\n        console.log('Bobine caricate:', bobine);\n\n        setBobineDisponibili(bobine || []);\n        setModificaBobinaDialog({\n          open: true,\n          cavo: cavo,\n          loading: false\n        });\n      } catch (error) {\n        console.error('Errore nel caricamento delle bobine:', error);\n        showNotification('Errore nel caricamento delle bobine disponibili', 'error');\n      }\n    }\n  };\n\n  // Funzione per salvare i metri posati\n  const handleSaveMetri = async (cavoId, metri) => {\n    setInserisciMetriDialog(prev => ({ ...prev, loading: true }));\n\n    try {\n      console.log('Salvando metri per cavo:', cavoId, 'metri:', metri, 'cantiere:', cantiereId);\n\n      // Chiamata API reale per aggiornare i metri posati\n      const cavoAggiornato = await caviService.updateMetriPosati(\n        cantiereId,\n        cavoId,\n        metri,\n        null, // id_bobina - per ora null, può essere gestito separatamente\n        true  // force_over\n      );\n\n      console.log('Cavo aggiornato:', cavoAggiornato);\n\n      // Aggiorna lo stato locale del cavo\n      const updateCavo = (cavi) => cavi.map(cavo =>\n        cavo.id_cavo === cavoId\n          ? {\n              ...cavo,\n              metratura_reale: metri,\n              stato_installazione: cavoAggiornato.stato_installazione || (metri >= cavo.metri_teorici ? 'INSTALLATO' : 'IN_CORSO')\n            }\n          : cavo\n      );\n\n      setCaviAttivi(updateCavo);\n      setCaviSpare(updateCavo);\n\n      // Chiudi il dialogo\n      setInserisciMetriDialog({ open: false, cavo: null, loading: false });\n\n      showNotification(`Metri posati aggiornati per il cavo ${cavoId}: ${metri}m`, 'success');\n\n      // Ricarica i dati per essere sicuri\n      setTimeout(() => fetchCavi(true), 500);\n\n    } catch (error) {\n      console.error('Errore nel salvataggio dei metri:', error);\n      const errorMessage = error.detail || error.message || 'Errore nel salvataggio dei metri posati';\n      showNotification(errorMessage, 'error');\n      setInserisciMetriDialog(prev => ({ ...prev, loading: false }));\n    }\n  };\n\n  // Funzione per modificare la bobina\n  const handleModifyBobina = async (cavoId, nuovaBobinaId, motivazione) => {\n    setModificaBobinaDialog(prev => ({ ...prev, loading: true }));\n\n    try {\n      console.log('Modificando bobina per cavo:', cavoId, 'nuova bobina:', nuovaBobinaId, 'motivazione:', motivazione, 'cantiere:', cantiereId);\n\n      // Chiamata API reale per aggiornare la bobina\n      const cavoAggiornato = await caviService.updateBobina(\n        cantiereId,\n        cavoId,\n        nuovaBobinaId,\n        true // force_over\n      );\n\n      console.log('Cavo aggiornato con nuova bobina:', cavoAggiornato);\n\n      // Aggiorna lo stato locale del cavo\n      const updateCavo = (cavi) => cavi.map(cavo =>\n        cavo.id_cavo === cavoId\n          ? { ...cavo, id_bobina: nuovaBobinaId }\n          : cavo\n      );\n\n      setCaviAttivi(updateCavo);\n      setCaviSpare(updateCavo);\n\n      // Chiudi il dialogo\n      setModificaBobinaDialog({ open: false, cavo: null, loading: false });\n\n      showNotification(`Bobina modificata per il cavo ${cavoId}: ${nuovaBobinaId}`, 'success');\n\n      // Ricarica i dati per essere sicuri\n      setTimeout(() => fetchCavi(true), 500);\n\n    } catch (error) {\n      console.error('Errore nella modifica della bobina:', error);\n      const errorMessage = error.detail || error.message || 'Errore nella modifica della bobina';\n      showNotification(errorMessage, 'error');\n      setModificaBobinaDialog(prev => ({ ...prev, loading: false }));\n    }\n  };\n\n  // Funzioni per chiudere i dialoghi\n  const handleCloseInserisciMetri = () => {\n    if (!inserisciMetriDialog.loading) {\n      setInserisciMetriDialog({ open: false, cavo: null, loading: false });\n    }\n  };\n\n  const handleCloseModificaBobina = () => {\n    if (!modificaBobinaDialog.loading) {\n      setModificaBobinaDialog({ open: false, cavo: null, loading: false });\n    }\n  };\n\n  // Nessuna funzione di navigazione necessaria, tutto è gestito dal menu principale\n\n  // Dashboard minimal con statistiche essenziali per visualizzazione cavi\n  const renderDashboard = () => (\n    <Paper sx={{ p: 2, mb: 3, bgcolor: 'grey.50' }}>\n      <Stack direction=\"row\" spacing={4} alignItems=\"center\" justifyContent=\"space-between\" flexWrap=\"wrap\">\n        {/* Statistiche essenziali in formato compatto */}\n        <Stack direction=\"row\" alignItems=\"center\" spacing={1}>\n          <CableIcon color=\"primary\" fontSize=\"small\" />\n          <Box>\n            <Typography variant=\"h6\" fontWeight=\"bold\" sx={{ lineHeight: 1 }}>\n              {statistics.totaleCavi}\n            </Typography>\n            <Typography variant=\"caption\" color=\"text.secondary\">\n              Totale\n            </Typography>\n          </Box>\n        </Stack>\n\n        <Stack direction=\"row\" alignItems=\"center\" spacing={1}>\n          <CheckCircleIcon color=\"success\" fontSize=\"small\" />\n          <Box>\n            <Typography variant=\"h6\" fontWeight=\"bold\" sx={{ lineHeight: 1 }}>\n              {statistics.caviInstallati}\n            </Typography>\n            <Typography variant=\"caption\" color=\"text.secondary\">\n              Installati\n            </Typography>\n          </Box>\n        </Stack>\n\n        <Stack direction=\"row\" alignItems=\"center\" spacing={1}>\n          <LinkIcon color=\"info\" fontSize=\"small\" />\n          <Box>\n            <Typography variant=\"h6\" fontWeight=\"bold\" sx={{ lineHeight: 1 }}>\n              {statistics.caviCollegati}\n            </Typography>\n            <Typography variant=\"caption\" color=\"text.secondary\">\n              Collegati\n            </Typography>\n          </Box>\n        </Stack>\n\n        <Stack direction=\"row\" alignItems=\"center\" spacing={1}>\n          <VerifiedIcon color=\"warning\" fontSize=\"small\" />\n          <Box>\n            <Typography variant=\"h6\" fontWeight=\"bold\" sx={{ lineHeight: 1 }}>\n              {statistics.caviCertificati}\n            </Typography>\n            <Typography variant=\"caption\" color=\"text.secondary\">\n              Certificati\n            </Typography>\n          </Box>\n        </Stack>\n\n        <Stack direction=\"row\" alignItems=\"center\" spacing={1}>\n          <Box sx={{\n            width: 32,\n            height: 32,\n            borderRadius: '50%',\n            bgcolor: statistics.percentualeInstallazione >= 80 ? 'success.main' :\n                     statistics.percentualeInstallazione >= 50 ? 'warning.main' : 'error.main',\n            display: 'flex',\n            alignItems: 'center',\n            justifyContent: 'center'\n          }}>\n            <Typography variant=\"caption\" fontWeight=\"bold\" color=\"white\">\n              {statistics.percentualeInstallazione}%\n            </Typography>\n          </Box>\n          <Box>\n            <Typography variant=\"body2\" fontWeight=\"medium\" sx={{ lineHeight: 1 }}>\n              Installazione\n            </Typography>\n            <Typography variant=\"caption\" color=\"text.secondary\">\n              {statistics.metriInstallati}m installati\n            </Typography>\n          </Box>\n        </Stack>\n      </Stack>\n    </Paper>\n  );\n\n  // La visualizzazione dei cavi è ora gestita dal componente CaviFilterableTable\n\n  // Rimossa funzione handleViewModeChange\n\n  // Renderizza il dialogo dei dettagli del cavo\n  const renderDetailsDialog = () => {\n    if (!selectedCavo) return null;\n\n    return (\n      <Dialog open={detailsDialogOpen} onClose={handleCloseDetails} maxWidth=\"md\" fullWidth>\n        <DialogTitle>\n          Dettagli Cavo: {selectedCavo.id_cavo}\n        </DialogTitle>\n        <DialogContent dividers>\n          <Grid container spacing={2}>\n            <Grid item xs={12} md={6}>\n              <Typography variant=\"subtitle1\" gutterBottom>Informazioni Generali</Typography>\n              <Box sx={{ mb: 2 }}>\n                <Typography variant=\"body2\"><strong>Sistema:</strong> {selectedCavo.sistema || 'N/A'}</Typography>\n                <Typography variant=\"body2\"><strong>Utility:</strong> {selectedCavo.utility || 'N/A'}</Typography>\n                <Typography variant=\"body2\"><strong>Tipologia:</strong> {selectedCavo.tipologia || 'N/A'}</Typography>\n                <Typography variant=\"body2\"><strong>Colore:</strong> {selectedCavo.colore_cavo || 'N/A'}</Typography>\n                {/* n_conduttori field is now a spare field (kept in DB but hidden in UI) */}\n                <Typography variant=\"body2\"><strong>Formazione:</strong> {selectedCavo.sezione || 'N/A'}</Typography>\n                {/* sh field is now a spare field (kept in DB but hidden in UI) */}\n              </Box>\n\n              <Typography variant=\"subtitle1\" gutterBottom>Partenza</Typography>\n              <Box sx={{ mb: 2 }}>\n                <Typography variant=\"body2\"><strong>Ubicazione:</strong> {selectedCavo.ubicazione_partenza || 'N/A'}</Typography>\n                <Typography variant=\"body2\"><strong>Utenza:</strong> {selectedCavo.utenza_partenza || 'N/A'}</Typography>\n                <Typography variant=\"body2\"><strong>Descrizione:</strong> {selectedCavo.descrizione_utenza_partenza || 'N/A'}</Typography>\n                <Typography variant=\"body2\"><strong>Responsabile:</strong> {selectedCavo.responsabile_partenza || 'N/A'}</Typography>\n                <Typography variant=\"body2\"><strong>Comanda:</strong> {selectedCavo.comanda_partenza || 'N/A'}</Typography>\n              </Box>\n            </Grid>\n\n            <Grid item xs={12} md={6}>\n              <Typography variant=\"subtitle1\" gutterBottom>Arrivo</Typography>\n              <Box sx={{ mb: 2 }}>\n                <Typography variant=\"body2\"><strong>Ubicazione:</strong> {selectedCavo.ubicazione_arrivo || 'N/A'}</Typography>\n                <Typography variant=\"body2\"><strong>Utenza:</strong> {selectedCavo.utenza_arrivo || 'N/A'}</Typography>\n                <Typography variant=\"body2\"><strong>Descrizione:</strong> {selectedCavo.descrizione_utenza_arrivo || 'N/A'}</Typography>\n                <Typography variant=\"body2\"><strong>Responsabile:</strong> {selectedCavo.responsabile_arrivo || 'N/A'}</Typography>\n                <Typography variant=\"body2\"><strong>Comanda:</strong> {selectedCavo.comanda_arrivo || 'N/A'}</Typography>\n              </Box>\n\n              <Typography variant=\"subtitle1\" gutterBottom>Installazione</Typography>\n              <Box sx={{ mb: 2 }}>\n                <Typography variant=\"body2\"><strong>Metri Teorici:</strong> {selectedCavo.metri_teorici || 'N/A'}</Typography>\n                <Typography variant=\"body2\"><strong>Metratura Reale:</strong> {selectedCavo.metratura_reale || '0'}</Typography>\n                <Typography variant=\"body2\"><strong>Stato:</strong> {normalizeInstallationStatus(selectedCavo.stato_installazione)}</Typography>\n                <Typography variant=\"body2\"><strong>Collegamenti:</strong> {selectedCavo.collegamenti || '0'}</Typography>\n                <Typography variant=\"body2\"><strong>Bobina:</strong> {selectedCavo.id_bobina || 'N/A'}</Typography>\n                <Typography variant=\"body2\"><strong>Responsabile Posa:</strong> {selectedCavo.responsabile_posa || 'N/A'}</Typography>\n                <Typography variant=\"body2\"><strong>Comanda Posa:</strong> {selectedCavo.comanda_posa || 'N/A'}</Typography>\n                <Typography variant=\"body2\"><strong>Ultimo Aggiornamento:</strong> {new Date(selectedCavo.timestamp).toLocaleString()}</Typography>\n              </Box>\n            </Grid>\n          </Grid>\n        </DialogContent>\n        <DialogActions>\n          <Button onClick={handleCloseDetails}>Chiudi</Button>\n        </DialogActions>\n      </Dialog>\n    );\n  };\n\n  // Il pannello dei filtri è ora gestito dal componente CaviFilterableTable\n\n\n\n  return (\n    <Box className=\"cavi-page\">\n      {loading ? (\n        <Box sx={{ display: 'flex', flexDirection: 'column', alignItems: 'center', mt: 4 }}>\n          <CircularProgress size={40} />\n          <Typography sx={{ mt: 2 }}>Caricamento cavi...</Typography>\n          <Button\n            variant=\"outlined\"\n            color=\"primary\"\n            onClick={() => window.location.reload()}\n            sx={{ mt: 2 }}\n          >\n            Ricarica la pagina\n          </Button>\n        </Box>\n      ) : error ? (\n        <Box>\n          <Alert severity=\"error\" sx={{ mb: 2 }}>\n            {error}\n            {error.includes('Network Error') && (\n              <Typography variant=\"body2\" sx={{ mt: 1 }}>\n                <strong>Suggerimento:</strong> Verifica che il server backend sia in esecuzione sulla porta 8001.\n                <br />\n                Puoi avviare il backend eseguendo il file <code>run_system.py</code> nella cartella principale del progetto.\n              </Typography>\n            )}\n          </Alert>\n          <Box sx={{ display: 'flex', gap: 2 }}>\n            <Button\n              variant=\"contained\"\n              className=\"primary-button\"\n              onClick={() => window.location.reload()}\n            >\n              Ricarica la pagina\n            </Button>\n          </Box>\n        </Box>\n      ) : (\n        <Box>\n          {/* Selettore Revisione */}\n          {revisioniDisponibili.length > 0 && (\n            <Paper sx={{ p: 2, mb: 3 }}>\n              <Box sx={{ display: 'flex', alignItems: 'center', gap: 2 }}>\n                <Typography variant=\"h6\">Visualizzazione:</Typography>\n                <FormControl size=\"small\" sx={{ minWidth: 250 }}>\n                  <InputLabel>Revisione da Visualizzare</InputLabel>\n                  <Select\n                    value={revisioneSelezionata || 'corrente'}\n                    onChange={handleRevisioneChange}\n                    label=\"Revisione da Visualizzare\"\n                  >\n                    <MenuItem value=\"corrente\">\n                      📋 Revisione Corrente {revisioneCorrente && `(${revisioneCorrente})`}\n                    </MenuItem>\n                    {revisioniDisponibili.map((rev) => (\n                      <MenuItem key={rev.revisione} value={rev.revisione}>\n                        📚 {rev.revisione} ({rev.cavi_count} cavi)\n                        {rev.revisione === revisioneCorrente && ' - Attuale'}\n                      </MenuItem>\n                    ))}\n                  </Select>\n                </FormControl>\n                <Chip\n                  label={\n                    revisioneSelezionata\n                      ? `Storico: ${revisioneSelezionata}`\n                      : `Corrente: ${revisioneCorrente || 'N/A'}`\n                  }\n                  color={revisioneSelezionata ? \"secondary\" : \"primary\"}\n                  variant=\"outlined\"\n                />\n              </Box>\n            </Paper>\n          )}\n\n          {/* Dashboard con statistiche avanzate */}\n          {renderDashboard()}\n\n          {/* Sezione Cavi */}\n          <Box sx={{ mt: 4 }}>\n            {/* Contatore selezione - solo quando ci sono cavi selezionati */}\n            {selectionEnabled && getTotalSelectedCount() > 0 && (\n              <Box sx={{ mb: 2 }}>\n                <Chip\n                  label={`${getTotalSelectedCount()} cavi selezionati`}\n                  color=\"primary\"\n                  variant=\"outlined\"\n                />\n              </Box>\n            )}\n\n            {/* Debug: Mostra le proprietà del primo cavo per verificare il nome del campo revisione */}\n            {process.env.NODE_ENV === 'development' && caviAttivi.length > 0 && (\n              <Box sx={{ mb: 2, p: 1, bgcolor: '#f0f0f0', borderRadius: 1, fontSize: '0.8rem', fontFamily: 'monospace', display: 'none' }}>\n                {Object.keys(caviAttivi[0]).map(key => (\n                  <div key={key}>{key}: {JSON.stringify(caviAttivi[0][key])}</div>\n                ))}\n              </Box>\n            )}\n\n            <CaviFilterableTable\n              cavi={caviAttivi}\n              loading={loading}\n              onFilteredDataChange={(filteredData) => console.log('Cavi attivi filtrati:', filteredData.length)}\n              revisioneCorrente={caviAttivi[0]?.revisione_ufficiale || caviAttivi[0]?.revisione || caviAttivi[0]?.rev}\n              selectionEnabled={selectionEnabled}\n              selectedCavi={selectedCaviAttivi}\n              onSelectionChange={handleCaviAttiviSelectionChange}\n              onSelectionToggle={handleSelectionToggle}\n              contextMenuItems={getContextMenuItems}\n              onContextMenuAction={handleContextMenuAction}\n              onStatusAction={handleStatusAction}\n            />\n            {caviAttivi.length === 0 && !loading && (\n              <Alert severity=\"info\" sx={{ mt: 2 }}>\n                Nessun cavo attivo trovato. I cavi attivi appariranno qui.\n              </Alert>\n            )}\n          </Box>\n\n          {/* Sezione Cavi Spare */}\n          <Box sx={{ mt: 4 }}>\n            <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 2 }}>\n              <Typography variant=\"h5\">\n                Cavi Spare {caviSpare.length > 0 ? `(${caviSpare.length})` : ''}\n              </Typography>\n            </Box>\n            <CaviFilterableTable\n              cavi={caviSpare}\n              loading={loading}\n              onFilteredDataChange={(filteredData) => console.log('Cavi spare filtrati:', filteredData.length)}\n              selectionEnabled={selectionEnabled}\n              selectedCavi={selectedCaviSpare}\n              onSelectionChange={handleCaviSpareSelectionChange}\n              onSelectionToggle={handleSelectionToggle}\n              contextMenuItems={getContextMenuItems}\n              onContextMenuAction={handleContextMenuAction}\n              onStatusAction={handleStatusAction}\n            />\n            {caviSpare.length === 0 && !loading && (\n              <Alert severity=\"info\" sx={{ mt: 2 }}>\n                Nessun cavo SPARE trovato. I cavi marcati come SPARE appariranno qui.\n              </Alert>\n            )}\n          </Box>\n\n          {/* Rimossa sezione Debug */}\n\n          {/* Dialogo dei dettagli del cavo */}\n          {renderDetailsDialog()}\n\n          {/* Dialogo per l'eliminazione dei cavi */}\n          <Dialog\n            open={openEliminaCavoDialog}\n            onClose={() => setOpenEliminaCavoDialog(false)}\n            fullWidth\n            maxWidth=\"md\"\n          >\n            <PosaCaviCollegamenti\n              cantiereId={cantiereId}\n              onSuccess={(message) => {\n                // Chiudi il dialogo\n                setOpenEliminaCavoDialog(false);\n\n                // Se c'è un messaggio, è un'operazione completata con successo\n                if (message) {\n                  // Mostra un messaggio di successo\n                  console.log('Operazione completata:', message);\n                  // Mostra un messaggio di successo con Snackbar\n                  showNotification(message, 'success');\n                  // Ricarica i dati dopo un ritardo per dare tempo al database di aggiornarsi\n                  setTimeout(() => {\n                    console.log('Ricaricamento dati dopo operazione...');\n                    try {\n                      // Ricarica i dati invece di ricaricare la pagina\n                      fetchCavi(true);\n                    } catch (error) {\n                      console.error('Errore durante il ricaricamento dei dati:', error);\n                      // Se fallisce, prova a ricaricare la pagina\n                      window.location.reload();\n                    }\n                  }, 1000);\n                } else {\n                  // È un'operazione annullata, non mostrare messaggi\n                  console.log('Operazione annullata dall\\'utente');\n                }\n              }}\n              onError={(message) => {\n                // Mostra un messaggio di errore\n                console.error('Errore durante l\\'eliminazione del cavo:', message);\n                // Mostra un alert all'utente\n                alert(`Errore: ${message}`);\n                // Chiudi il dialogo\n                setOpenEliminaCavoDialog(false);\n                // Ricarica comunque i dati per assicurarsi che la vista sia aggiornata\n                fetchCavi();\n              }}\n              initialOption=\"eliminaCavo\"\n            />\n          </Dialog>\n\n          {/* Dialogo per la modifica dei cavi */}\n          {/* Log del cantiereId prima di aprire il dialog */}\n          {openModificaCavoDialog && console.log('VisualizzaCaviPage - cantiereId prima di aprire il dialog:', cantiereId)}\n\n          <Dialog\n            open={openModificaCavoDialog}\n            onClose={() => setOpenModificaCavoDialog(false)}\n            fullWidth\n            maxWidth=\"sm\"\n          >\n            <PosaCaviCollegamenti\n              cantiereId={cantiereId}\n              onSuccess={(message) => {\n                // Chiudi il dialogo\n                setOpenModificaCavoDialog(false);\n\n                // Se c'è un messaggio, è un'operazione completata con successo\n                if (message) {\n                  // Mostra un messaggio di successo\n                  console.log('Operazione completata:', message);\n                  // Mostra un messaggio di successo con Snackbar\n                  showNotification(message, 'success');\n                  // Ricarica i dati immediatamente\n                  console.log('Ricaricamento dati dopo operazione...');\n                  // Ricarica i dati con un ritardo per dare tempo al database di aggiornarsi\n                  setTimeout(() => {\n                    try {\n                      fetchCavi(true);\n                    } catch (error) {\n                      console.error('Errore durante il ricaricamento dei dati:', error);\n                      // Se fallisce, prova a ricaricare la pagina\n                      window.location.reload();\n                    }\n                  }, 1000);\n                } else {\n                  // È un'operazione annullata, non mostrare messaggi\n                  console.log('Operazione annullata dall\\'utente');\n                }\n              }}\n              onError={(message) => {\n                // Mostra un messaggio di errore\n                console.error('Errore durante la modifica del cavo:', message);\n                // Mostra un alert all'utente\n                alert(`Errore: ${message}`);\n                // Chiudi il dialogo\n                setOpenModificaCavoDialog(false);\n                // Ricarica comunque i dati per assicurarsi che la vista sia aggiornata\n                console.log('Ricaricamento dati dopo errore...');\n                fetchCavi(true);\n              }}\n              initialOption=\"modificaCavo\"\n            />\n          </Dialog>\n\n          {/* Dialogo per l'aggiunta di un nuovo cavo */}\n          <Dialog open={openAggiungiCavoDialog} onClose={() => setOpenAggiungiCavoDialog(false)} maxWidth=\"sm\" fullWidth>\n            <PosaCaviCollegamenti\n              cantiereId={cantiereId}\n              onSuccess={(message) => {\n                // Chiudi il dialogo\n                setOpenAggiungiCavoDialog(false);\n\n                // Se c'è un messaggio, è un'operazione completata con successo\n                if (message) {\n                  // Mostra un messaggio di successo\n                  console.log('Operazione completata:', message);\n                  // Mostra un messaggio di successo con Snackbar\n                  showNotification(message, 'success');\n                  // Ricarica i dati dopo un ritardo per dare tempo al database di aggiornarsi\n                  setTimeout(() => {\n                    console.log('Ricaricamento dati dopo operazione...');\n                    try {\n                      // Ricarica i dati in modalità silenziosa per evitare il \"blink\" della pagina\n                      fetchCavi(true);\n                    } catch (error) {\n                      console.error('Errore durante il ricaricamento dei dati:', error);\n                      // Se fallisce, prova a ricaricare la pagina immediatamente\n                      console.log('Tentativo di ricaricamento della pagina...');\n                      window.location.reload();\n                    }\n                  }, 1000);\n                } else {\n                  // È un'operazione annullata, non mostrare messaggi\n                  console.log('Operazione annullata dall\\'utente');\n                }\n              }}\n              onError={(message) => {\n                // Mostra un messaggio di errore\n                console.error('Errore durante l\\'aggiunta del cavo:', message);\n                // Mostra un messaggio di errore con Snackbar\n                showNotification(`Errore: ${message}`, 'error');\n                // Chiudi il dialogo\n                setOpenAggiungiCavoDialog(false);\n                // Ricarica comunque i dati per assicurarsi che la vista sia aggiornata\n                fetchCavi(true);\n              }}\n              initialOption=\"aggiungiCavo\"\n            />\n          </Dialog>\n\n          {/* Dialoghi per azioni sui pulsanti stato */}\n          <InserisciMetriDialog\n            open={inserisciMetriDialog.open}\n            onClose={handleCloseInserisciMetri}\n            cavo={inserisciMetriDialog.cavo}\n            onSave={handleSaveMetri}\n            loading={inserisciMetriDialog.loading}\n          />\n\n          <ModificaBobinaDialog\n            open={modificaBobinaDialog.open}\n            onClose={handleCloseModificaBobina}\n            cavo={modificaBobinaDialog.cavo}\n            bobineDisponibili={bobineDisponibili}\n            onSave={handleModifyBobina}\n            loading={modificaBobinaDialog.loading}\n          />\n\n          {/* Snackbar per le notifiche */}\n          <Snackbar\n            open={notification.open}\n            autoHideDuration={4000}\n            onClose={handleCloseNotification}\n            anchorOrigin={{ vertical: 'bottom', horizontal: 'center' }}\n          >\n            <Alert onClose={handleCloseNotification} severity={notification.severity} sx={{ width: '100%' }}>\n              {notification.message}\n            </Alert>\n          </Snackbar>\n        </Box>\n      )}\n    </Box>\n  );\n};\n\nexport default VisualizzaCaviPage;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAClD,SACEC,GAAG,EACHC,UAAU,EACVC,KAAK,EACLC,MAAM,EACNC,IAAI,EACJC,IAAI,EACJC,WAAW,EACXC,KAAK,EACLC,UAAU,EACVC,IAAI,EACJC,gBAAgB,EAChBC,cAAc,EACdC,MAAM,EACNC,WAAW,EACXC,aAAa,EACbC,aAAa,EACbC,QAAQ,EACRC,WAAW,EACXC,UAAU,EACVC,MAAM,EACNC,QAAQ,EACRC,KAAK,QACA,eAAe;AACtB,OAAOC,QAAQ,MAAM,0BAA0B;AAC/C,SACEC,KAAK,IAAIC,SAAS,EAClBC,WAAW,IAAIC,eAAe,EAC9BC,QAAQ,IAAIC,YAAY,EACxBC,IAAI,IAAIC,QAAQ,EAChBC,OAAO,IAAIC,WAAW,EACtBC,QAAQ,IAAIC,YAAY,EACxBC,QAAQ,IAAIC,YAAY,EACxBC,oBAAoB,IAAIC,wBAAwB,EAChDC,UAAU,IAAIC,cAAc,EAC5BC,IAAI,IAAIC,QAAQ,EAChBC,MAAM,IAAIC,UAAU,EACpBC,SAAS,IAAIC,aAAa,EAC1BC,WAAW,IAAIC,QAAQ,EACvBC,QAAQ,IAAIC,YAAY,EACxBC,QAAQ,IAAIC,YAAY,QACnB,qBAAqB;AAC5B,SAASC,WAAW,QAAQ,kBAAkB;AAC9C,SAASC,OAAO,QAAQ,2BAA2B;AACnD,SAASC,gBAAgB,QAAQ,6BAA6B;AAC9D,OAAOC,oBAAoB,MAAM,4CAA4C;AAC7E,OAAOC,WAAW,MAAM,4BAA4B;AACpD,OAAOC,gBAAgB,MAAM,iCAAiC;AAC9D,OAAOC,QAAQ,MAAM,gCAAgC;AACrD,SAASC,2BAA2B,QAAQ,6BAA6B;AACzE,OAAOC,mBAAmB,MAAM,2CAA2C;AAC3E,OAAOC,oBAAoB,MAAM,4CAA4C;AAC7E,OAAOC,oBAAoB,MAAM,4CAA4C;AAC7E,OAAO,gBAAgB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAExB,MAAMC,kBAAkB,GAAGA,CAAA,KAAM;EAAAC,EAAA;EAAA,IAAAC,YAAA,EAAAC,aAAA,EAAAC,aAAA;EAC/B,MAAM;IAAEC,eAAe;IAAEC;EAAK,CAAC,GAAGlB,OAAO,CAAC,CAAC;EAC3C,MAAM;IAAEmB,qBAAqB;IAAEC,wBAAwB;IAAEC,sBAAsB;IAAEC,yBAAyB;IAAEC,sBAAsB;IAAEC;EAA0B,CAAC,GAAGvB,gBAAgB,CAAC,CAAC;EACpL,MAAMwB,QAAQ,GAAG1B,WAAW,CAAC,CAAC;EAC9B,MAAM,CAAC2B,UAAU,EAAEC,aAAa,CAAC,GAAGnF,QAAQ,CAAC,IAAI,CAAC;EAClD,MAAM,CAACoF,YAAY,EAAEC,eAAe,CAAC,GAAGrF,QAAQ,CAAC,EAAE,CAAC;EACpD,MAAM,CAACsF,UAAU,EAAEC,aAAa,CAAC,GAAGvF,QAAQ,CAAC,EAAE,CAAC;EAChD,MAAM,CAACwF,SAAS,EAAEC,YAAY,CAAC,GAAGzF,QAAQ,CAAC,EAAE,CAAC;EAC9C,MAAM,CAAC0F,OAAO,EAAEC,UAAU,CAAC,GAAG3F,QAAQ,CAAC,IAAI,CAAC;EAC5C,MAAM,CAAC4F,KAAK,EAAEC,QAAQ,CAAC,GAAG7F,QAAQ,CAAC,IAAI,CAAC;EACxC;EACA,MAAM,CAAC8F,YAAY,EAAEC,eAAe,CAAC,GAAG/F,QAAQ,CAAC;IAAEgG,IAAI,EAAE,KAAK;IAAEC,OAAO,EAAE,EAAE;IAAEC,QAAQ,EAAE;EAAU,CAAC,CAAC;EACnG;;EAEA;EACA,MAAM,CAACC,YAAY,EAAEC,eAAe,CAAC,GAAGpG,QAAQ,CAAC,IAAI,CAAC;EACtD,MAAM,CAACqG,iBAAiB,EAAEC,oBAAoB,CAAC,GAAGtG,QAAQ,CAAC,KAAK,CAAC;;EAEjE;EACA,MAAM,CAACuG,gBAAgB,EAAEC,mBAAmB,CAAC,GAAGxG,QAAQ,CAAC,KAAK,CAAC;EAC/D,MAAM,CAACyG,kBAAkB,EAAEC,qBAAqB,CAAC,GAAG1G,QAAQ,CAAC,EAAE,CAAC;EAChE,MAAM,CAAC2G,iBAAiB,EAAEC,oBAAoB,CAAC,GAAG5G,QAAQ,CAAC,EAAE,CAAC;;EAE9D;EACA,MAAM,CAAC6G,oBAAoB,EAAEC,uBAAuB,CAAC,GAAG9G,QAAQ,CAAC;IAC/DgG,IAAI,EAAE,KAAK;IACXe,IAAI,EAAE,IAAI;IACVrB,OAAO,EAAE;EACX,CAAC,CAAC;EACF,MAAM,CAACsB,oBAAoB,EAAEC,uBAAuB,CAAC,GAAGjH,QAAQ,CAAC;IAC/DgG,IAAI,EAAE,KAAK;IACXe,IAAI,EAAE,IAAI;IACVrB,OAAO,EAAE;EACX,CAAC,CAAC;EACF,MAAM,CAACwB,iBAAiB,EAAEC,oBAAoB,CAAC,GAAGnH,QAAQ,CAAC,EAAE,CAAC;;EAE9D;EACA,MAAM,CAACoH,UAAU,EAAEC,aAAa,CAAC,GAAGrH,QAAQ,CAAC;IAC3CsH,UAAU,EAAE,CAAC;IACbC,cAAc,EAAE,CAAC;IACjBC,gBAAgB,EAAE,CAAC;IACnBC,WAAW,EAAE,CAAC;IACdC,aAAa,EAAE,CAAC;IAChBC,gBAAgB,EAAE,CAAC;IACnBC,eAAe,EAAE,CAAC;IAClBC,kBAAkB,EAAE,CAAC;IACrBC,wBAAwB,EAAE,CAAC;IAC3BC,uBAAuB,EAAE,CAAC;IAC1BC,yBAAyB,EAAE,CAAC;IAC5BC,WAAW,EAAE,CAAC;IACdC,eAAe,EAAE,CAAC;IAClBC,cAAc,EAAE;EAClB,CAAC,CAAC;;EAIF;EACA,MAAM,CAACC,oBAAoB,EAAEC,uBAAuB,CAAC,GAAGrI,QAAQ,CAAC,EAAE,CAAC;EACpE,MAAM,CAACsI,oBAAoB,EAAEC,uBAAuB,CAAC,GAAGvI,QAAQ,CAAC,EAAE,CAAC;EACpE,MAAM,CAACwI,iBAAiB,EAAEC,oBAAoB,CAAC,GAAGzI,QAAQ,CAAC,EAAE,CAAC;;EAE9D;;EAEA;EACA,MAAM0I,mBAAmB,GAAGA,CAACC,cAAc,EAAEC,aAAa,KAAK;IAC7D,MAAMC,SAAS,GAAG,CAAC,IAAIF,cAAc,IAAI,EAAE,CAAC,EAAE,IAAIC,aAAa,IAAI,EAAE,CAAC,CAAC;IAEvE,IAAIC,SAAS,CAACC,MAAM,KAAK,CAAC,EAAE;MAC1BC,OAAO,CAACC,GAAG,CAAC,0DAA0D,CAAC;MACvE;IACF;IAEAD,OAAO,CAACC,GAAG,CAAC,+BAA+B,EAAE;MAC3C1D,UAAU,EAAE,CAAAqD,cAAc,aAAdA,cAAc,uBAAdA,cAAc,CAAEG,MAAM,KAAI,CAAC;MACvCtD,SAAS,EAAE,CAAAoD,aAAa,aAAbA,aAAa,uBAAbA,aAAa,CAAEE,MAAM,KAAI,CAAC;MACrCG,MAAM,EAAEJ,SAAS,CAACC;IACpB,CAAC,CAAC;IAEF,MAAMxB,UAAU,GAAGuB,SAAS,CAACC,MAAM;;IAEnC;IACA,MAAMvB,cAAc,GAAGsB,SAAS,CAACK,MAAM,CAACnC,IAAI,IAC1CA,IAAI,CAACoC,mBAAmB,KAAK,YAAY,IACzCpC,IAAI,CAACoC,mBAAmB,KAAK,YAAY,IACzCpC,IAAI,CAACoC,mBAAmB,KAAK,QAC/B,CAAC,CAACL,MAAM;IAER,MAAMtB,gBAAgB,GAAGqB,SAAS,CAACK,MAAM,CAACnC,IAAI,IAC5CA,IAAI,CAACoC,mBAAmB,KAAK,eAAe,IAC5CpC,IAAI,CAACoC,mBAAmB,KAAK,eAC/B,CAAC,CAACL,MAAM;IAER,MAAMrB,WAAW,GAAGoB,SAAS,CAACK,MAAM,CAACnC,IAAI,IACvCA,IAAI,CAACoC,mBAAmB,KAAK,UAAU,IACvCpC,IAAI,CAACoC,mBAAmB,KAAK,UAC/B,CAAC,CAACL,MAAM;;IAER;IACA,MAAMpB,aAAa,GAAGmB,SAAS,CAACK,MAAM,CAACnC,IAAI,IACzCA,IAAI,CAACqC,YAAY,KAAK,CAAC,IACvBrC,IAAI,CAACsC,qBAAqB,IAC1BtC,IAAI,CAACuC,mBACP,CAAC,CAACR,MAAM;IAER,MAAMnB,gBAAgB,GAAGL,UAAU,GAAGI,aAAa;;IAEnD;IACA,MAAMI,wBAAwB,GAAGR,UAAU,GAAG,CAAC,GAAGiC,IAAI,CAACC,KAAK,CAAEjC,cAAc,GAAGD,UAAU,GAAI,GAAG,CAAC,GAAG,CAAC;IACrG,MAAMS,uBAAuB,GAAGT,UAAU,GAAG,CAAC,GAAGiC,IAAI,CAACC,KAAK,CAAE9B,aAAa,GAAGJ,UAAU,GAAI,GAAG,CAAC,GAAG,CAAC;;IAEnG;IACA,MAAMW,WAAW,GAAGY,SAAS,CAACY,MAAM,CAAC,CAACC,GAAG,EAAE3C,IAAI,KAAK2C,GAAG,IAAIC,UAAU,CAAC5C,IAAI,CAAC6C,aAAa,CAAC,IAAI,CAAC,CAAC,EAAE,CAAC,CAAC;IACnG,MAAM1B,eAAe,GAAGW,SAAS,CAC9BK,MAAM,CAACnC,IAAI,IAAIA,IAAI,CAACoC,mBAAmB,KAAK,YAAY,IAAIpC,IAAI,CAACoC,mBAAmB,KAAK,YAAY,IAAIpC,IAAI,CAACoC,mBAAmB,KAAK,QAAQ,CAAC,CAC/IM,MAAM,CAAC,CAACC,GAAG,EAAE3C,IAAI,KAAK2C,GAAG,IAAIC,UAAU,CAAC5C,IAAI,CAAC8C,eAAe,CAAC,IAAIF,UAAU,CAAC5C,IAAI,CAAC6C,aAAa,CAAC,IAAI,CAAC,CAAC,EAAE,CAAC,CAAC;IAC5G,MAAMzB,cAAc,GAAGF,WAAW,GAAGC,eAAe;;IAEpD;IACA,MAAMN,eAAe,GAAG,CAAC,CAAC,CAAC;IAC3B,MAAMC,kBAAkB,GAAGP,UAAU,GAAGM,eAAe;IACvD,MAAMI,yBAAyB,GAAGV,UAAU,GAAG,CAAC,GAAGiC,IAAI,CAACC,KAAK,CAAE5B,eAAe,GAAGN,UAAU,GAAI,GAAG,CAAC,GAAG,CAAC;IAEvG,MAAMwC,aAAa,GAAG;MACpBxC,UAAU;MACVC,cAAc;MACdC,gBAAgB;MAChBC,WAAW;MACXC,aAAa;MACbC,gBAAgB;MAChBC,eAAe;MACfC,kBAAkB;MAClBC,wBAAwB;MACxBC,uBAAuB;MACvBC,yBAAyB;MACzBC,WAAW,EAAEsB,IAAI,CAACC,KAAK,CAACvB,WAAW,CAAC;MACpCC,eAAe,EAAEqB,IAAI,CAACC,KAAK,CAACtB,eAAe,CAAC;MAC5CC,cAAc,EAAEoB,IAAI,CAACC,KAAK,CAACrB,cAAc;IAC3C,CAAC;IAEDY,OAAO,CAACC,GAAG,CAAC,8BAA8B,EAAEc,aAAa,CAAC;IAC1DzC,aAAa,CAACyC,aAAa,CAAC;EAC9B,CAAC;;EAED;EACA,MAAMC,sBAAsB,GAAGA,CAAA,KAAM;IACnC;IACAC,qBAAqB,CAAC,CAAC,YAAY,EAAE,eAAe,EAAE,UAAU,CAAC,CAAC;EACpE,CAAC;;EAED;EACA,MAAMC,aAAa,GAAG,MAAOC,eAAe,IAAK;IAC/C,IAAI;MACFnB,OAAO,CAACC,GAAG,CAAC,qCAAqC,EAAEkB,eAAe,CAAC;;MAEnE;MACA,MAAMC,qBAAqB,GAAG,MAAMxG,WAAW,CAACyG,oBAAoB,CAACF,eAAe,CAAC;MACrFnB,OAAO,CAACC,GAAG,CAAC,qBAAqB,EAAEmB,qBAAqB,CAAC;MACzD1B,oBAAoB,CAAC0B,qBAAqB,CAACE,kBAAkB,CAAC;;MAE9D;MACA,MAAMC,aAAa,GAAG,MAAM3G,WAAW,CAAC4G,uBAAuB,CAACL,eAAe,CAAC;MAChFnB,OAAO,CAACC,GAAG,CAAC,wBAAwB,EAAEsB,aAAa,CAAC;MACpDjC,uBAAuB,CAACiC,aAAa,CAACE,SAAS,IAAI,EAAE,CAAC;;MAEtD;MACA;MACAzB,OAAO,CAACC,GAAG,CAAC,wDAAwD,CAAC;IACvE,CAAC,CAAC,OAAOpD,KAAK,EAAE;MACdmD,OAAO,CAACnD,KAAK,CAAC,yCAAyC,EAAEA,KAAK,CAAC;IACjE;EACF,CAAC;;EAED;EACA,MAAM6E,qBAAqB,GAAIC,KAAK,IAAK;IACvC,MAAMC,cAAc,GAAGD,KAAK,CAACE,MAAM,CAACC,KAAK;;IAEzC;IACA;IACA;IACA,IAAIF,cAAc,KAAK,EAAE,IAAIA,cAAc,KAAK,UAAU,EAAE;MAC1DpC,uBAAuB,CAAC,EAAE,CAAC;MAC3BQ,OAAO,CAACC,GAAG,CAAC,0CAA0C,CAAC;IACzD,CAAC,MAAM;MACLT,uBAAuB,CAACoC,cAAc,CAAC;MACvC5B,OAAO,CAACC,GAAG,CAAC,gCAAgC,EAAE2B,cAAc,CAAC;IAC/D;EACF,CAAC;;EAED;EACA,MAAM,CAACG,OAAO,EAAEC,UAAU,CAAC,GAAG/K,QAAQ,CAAC;IACrCmJ,mBAAmB,EAAE,EAAE;IACvB6B,SAAS,EAAE,EAAE;IACbC,OAAO,EAAE,EAAE;IACXC,UAAU,EAAE;EACd,CAAC,CAAC;;EAEF;EACA,MAAM,CAACC,kBAAkB,EAAEnB,qBAAqB,CAAC,GAAGhK,QAAQ,CAAC,EAAE,CAAC;EAChE,MAAM,CAACoL,aAAa,EAAEC,gBAAgB,CAAC,GAAGrL,QAAQ,CAAC,EAAE,CAAC;;EAEtD;;EAEA;EACA;EACA,MAAMsL,SAAS,GAAG,MAAAA,CAAOC,aAAa,GAAG,KAAK,KAAK;IACjD,IAAI;MACF,IAAI,CAACA,aAAa,EAAE;QAClB5F,UAAU,CAAC,IAAI,CAAC;MAClB;MACAoD,OAAO,CAACC,GAAG,CAAC,gCAAgC,EAAE9D,UAAU,CAAC;;MAEzD;MACA,IAAI,CAACA,UAAU,EAAE;QACf6D,OAAO,CAACnD,KAAK,CAAC,mCAAmC,EAAEV,UAAU,CAAC;QAC9DW,QAAQ,CAAC,wDAAwD,CAAC;QAClEF,UAAU,CAAC,KAAK,CAAC;QACjB;MACF;;MAEA;MACA,IAAIuE,eAAe,GAAGhF,UAAU;MAChC,IAAI,CAACgF,eAAe,EAAE;QACpBA,eAAe,GAAGsB,YAAY,CAACC,OAAO,CAAC,oBAAoB,CAAC;QAC5D1C,OAAO,CAACC,GAAG,CAAC,qCAAqC,EAAEkB,eAAe,CAAC;QACnE,IAAI,CAACA,eAAe,EAAE;UACpBnB,OAAO,CAACnD,KAAK,CAAC,2CAA2C,CAAC;UAC1DC,QAAQ,CAAC,8CAA8C,CAAC;UACxDF,UAAU,CAAC,KAAK,CAAC;UACjB;QACF;MACF;;MAEA;MACAoD,OAAO,CAACC,GAAG,CAAC,0CAA0C,CAAC;MACvD,IAAI0C,MAAM,GAAG,EAAE;MACf,IAAI;QACFA,MAAM,GAAG,MAAM/H,WAAW,CAACgI,OAAO,CAACzB,eAAe,EAAE,CAAC,EAAEY,OAAO,CAAC;QAC/D/B,OAAO,CAACC,GAAG,CAAC,uBAAuB,EAAE0C,MAAM,GAAGA,MAAM,CAAC5C,MAAM,GAAG,CAAC,CAAC;MAClE,CAAC,CAAC,OAAO8C,WAAW,EAAE;QACpB7C,OAAO,CAACnD,KAAK,CAAC,yCAAyC,EAAEgG,WAAW,CAAC;QACrE;QACAF,MAAM,GAAG,EAAE;MACb;;MAEA;MACA,IAAIA,MAAM,IAAIA,MAAM,CAAC5C,MAAM,GAAG,CAAC,EAAE;QAC/B,MAAM+C,kBAAkB,GAAGH,MAAM,CAACxC,MAAM,CAACnC,IAAI,IAAIA,IAAI,CAAC+E,sBAAsB,KAAK,CAAC,CAAC;QACnF,IAAID,kBAAkB,CAAC/C,MAAM,GAAG,CAAC,EAAE;UACjCC,OAAO,CAACnD,KAAK,CAAC,wEAAwE,EAAEiG,kBAAkB,CAAC;QAC7G;MACF;MAEAtG,aAAa,CAACmG,MAAM,IAAI,EAAE,CAAC;;MAE3B;MACA,IAAIK,KAAK,GAAG,EAAE;MACd,IAAI;QACFhD,OAAO,CAACC,GAAG,CAAC,iDAAiD,CAAC;QAC9D+C,KAAK,GAAG,MAAMpI,WAAW,CAACqI,YAAY,CAAC9B,eAAe,CAAC;QACvDnB,OAAO,CAACC,GAAG,CAAC,4CAA4C,EAAE+C,KAAK,GAAGA,KAAK,CAACjD,MAAM,GAAG,CAAC,CAAC;QACnF,IAAIiD,KAAK,IAAIA,KAAK,CAACjD,MAAM,GAAG,CAAC,EAAE;UAC7BC,OAAO,CAACC,GAAG,CAAC,mBAAmB,EAAE+C,KAAK,CAAC,CAAC,CAAC,CAAC;QAC5C;MACF,CAAC,CAAC,OAAOE,UAAU,EAAE;QACnBlD,OAAO,CAACnD,KAAK,CAAC,8DAA8D,EAAEqG,UAAU,CAAC;QACzF;QACA,IAAI;UACFlD,OAAO,CAACC,GAAG,CAAC,kCAAkC,CAAC;UAC/C+C,KAAK,GAAG,MAAMpI,WAAW,CAACgI,OAAO,CAACzB,eAAe,EAAE,CAAC,CAAC;UACrDnB,OAAO,CAACC,GAAG,CAAC,0CAA0C,EAAE+C,KAAK,GAAGA,KAAK,CAACjD,MAAM,GAAG,CAAC,CAAC;QACnF,CAAC,CAAC,OAAOoD,aAAa,EAAE;UACtBnD,OAAO,CAACnD,KAAK,CAAC,mCAAmC,EAAEsG,aAAa,CAAC;UACjE;UACAH,KAAK,GAAG,EAAE;QACZ;MACF;MACAtG,YAAY,CAACsG,KAAK,IAAI,EAAE,CAAC;;MAIzB;MACAlG,QAAQ,CAAC,EAAE,CAAC;IACd,CAAC,CAAC,OAAOD,KAAK,EAAE;MACdmD,OAAO,CAACnD,KAAK,CAAC,2CAA2C,EAAEA,KAAK,CAAC;MACjEC,QAAQ,CAAC,oCAAoCD,KAAK,CAACK,OAAO,IAAI,oBAAoB,EAAE,CAAC;;MAErF;MACAkG,UAAU,CAAC,MAAM;QACf;QACA,IAAIC,QAAQ,CAACC,IAAI,CAACC,WAAW,CAACC,QAAQ,CAAC,iCAAiC,CAAC,EAAE;UACzExD,OAAO,CAACC,GAAG,CAAC,gEAAgE,CAAC;UAC7EwD,MAAM,CAACC,QAAQ,CAACC,MAAM,CAAC,CAAC;QAC1B;MACF,CAAC,EAAE,IAAI,CAAC,CAAC,CAAC;IACZ,CAAC,SAAS;MACR,IAAI,CAACnB,aAAa,EAAE;QAClB5F,UAAU,CAAC,KAAK,CAAC;MACnB;IACF;EACF,CAAC;;EAED;EACA1F,SAAS,CAAC,MAAM;IACd;IACA8J,sBAAsB,CAAC,CAAC;IAExB,MAAM4C,SAAS,GAAG,MAAAA,CAAA,KAAY;MAC5B,IAAI;QACF5D,OAAO,CAACC,GAAG,CAAC,wCAAwC,CAAC;;QAErD;QACA,MAAM4D,KAAK,GAAGpB,YAAY,CAACC,OAAO,CAAC,OAAO,CAAC;QAC3C1C,OAAO,CAACC,GAAG,CAAC,iBAAiB,EAAE,CAAC,CAAC4D,KAAK,CAAC;QACvC,IAAI,CAACA,KAAK,EAAE;UACV/G,QAAQ,CAAC,iDAAiD,CAAC;UAC3DF,UAAU,CAAC,KAAK,CAAC;UACjB;QACF;;QAEA;QACA,IAAIkH,kBAAkB,GAAGrB,YAAY,CAACC,OAAO,CAAC,oBAAoB,CAAC;QACnE,IAAIqB,oBAAoB,GAAGtB,YAAY,CAACC,OAAO,CAAC,sBAAsB,CAAC;QAEvE1C,OAAO,CAACC,GAAG,CAAC,wCAAwC,EAAE;UAAE6D,kBAAkB;UAAEC;QAAqB,CAAC,CAAC;QACnG/D,OAAO,CAACC,GAAG,CAAC,cAAc,EAAEtE,IAAI,CAAC;;QAEjC;QACAqE,OAAO,CAACC,GAAG,CAAC,wCAAwC,CAAC;QACrD,KAAK,IAAI+D,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGvB,YAAY,CAAC1C,MAAM,EAAEiE,CAAC,EAAE,EAAE;UAC5C,MAAMC,GAAG,GAAGxB,YAAY,CAACwB,GAAG,CAACD,CAAC,CAAC;UAC/BhE,OAAO,CAACC,GAAG,CAAC,GAAGgE,GAAG,KAAKxB,YAAY,CAACC,OAAO,CAACuB,GAAG,CAAC,EAAE,CAAC;QACrD;;QAEA;QACA,IAAI,CAAAtI,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAEuI,IAAI,MAAK,eAAe,EAAE;UAClClE,OAAO,CAACC,GAAG,CAAC,6EAA6E,CAAC;;UAE1F;UACA,IAAItE,IAAI,CAACwI,WAAW,EAAE;YACpBnE,OAAO,CAACC,GAAG,CAAC,sCAAsC,EAAEtE,IAAI,CAACwI,WAAW,CAAC;YACrEL,kBAAkB,GAAGnI,IAAI,CAACwI,WAAW,CAACC,QAAQ,CAAC,CAAC;YAChDL,oBAAoB,GAAGpI,IAAI,CAAC0I,aAAa,IAAI,YAAY1I,IAAI,CAACwI,WAAW,EAAE;;YAE3E;YACA1B,YAAY,CAAC6B,OAAO,CAAC,oBAAoB,EAAER,kBAAkB,CAAC;YAC9DrB,YAAY,CAAC6B,OAAO,CAAC,sBAAsB,EAAEP,oBAAoB,CAAC;YAClE/D,OAAO,CAACC,GAAG,CAAC,uCAAuC,EAAE6D,kBAAkB,CAAC;UAC1E,CAAC,MAAM;YACL;YACA,IAAI;cACF9D,OAAO,CAACC,GAAG,CAAC,qEAAqE,CAAC;cAClF,MAAM4D,KAAK,GAAGpB,YAAY,CAACC,OAAO,CAAC,OAAO,CAAC;cAC3C,IAAImB,KAAK,EAAE;gBACT;gBACA,MAAMU,SAAS,GAAGV,KAAK,CAACW,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC;gBACrC,MAAMC,MAAM,GAAGF,SAAS,CAACG,OAAO,CAAC,IAAI,EAAE,GAAG,CAAC,CAACA,OAAO,CAAC,IAAI,EAAE,GAAG,CAAC;gBAC9D,MAAMC,WAAW,GAAGC,kBAAkB,CAACC,IAAI,CAACJ,MAAM,CAAC,CAACD,KAAK,CAAC,EAAE,CAAC,CAACM,GAAG,CAACC,CAAC,IAAI;kBACrE,OAAO,GAAG,GAAG,CAAC,IAAI,GAAGA,CAAC,CAACC,UAAU,CAAC,CAAC,CAAC,CAACZ,QAAQ,CAAC,EAAE,CAAC,EAAEa,KAAK,CAAC,CAAC,CAAC,CAAC;gBAC9D,CAAC,CAAC,CAACC,IAAI,CAAC,EAAE,CAAC,CAAC;gBAEZ,MAAMC,OAAO,GAAGC,IAAI,CAACC,KAAK,CAACV,WAAW,CAAC;gBACvC3E,OAAO,CAACC,GAAG,CAAC,wBAAwB,EAAEkF,OAAO,CAAC;gBAE9C,IAAIA,OAAO,CAAChB,WAAW,EAAE;kBACvBnE,OAAO,CAACC,GAAG,CAAC,oCAAoC,EAAEkF,OAAO,CAAChB,WAAW,CAAC;kBACtEL,kBAAkB,GAAGqB,OAAO,CAAChB,WAAW,CAACC,QAAQ,CAAC,CAAC;kBACnD;kBACAL,oBAAoB,GAAG,YAAYoB,OAAO,CAAChB,WAAW,EAAE;;kBAExD;kBACA1B,YAAY,CAAC6B,OAAO,CAAC,oBAAoB,EAAER,kBAAkB,CAAC;kBAC9DrB,YAAY,CAAC6B,OAAO,CAAC,sBAAsB,EAAEP,oBAAoB,CAAC;kBAClE/D,OAAO,CAACC,GAAG,CAAC,uCAAuC,EAAE6D,kBAAkB,CAAC;gBAC1E;cACF;YACF,CAAC,CAAC,OAAOwB,CAAC,EAAE;cACVtF,OAAO,CAACnD,KAAK,CAAC,6CAA6C,EAAEyI,CAAC,CAAC;YACjE;UACF;QACF;;QAEA;QACA,IAAI,CAACxB,kBAAkB,IAAIA,kBAAkB,KAAK,WAAW,IAAIA,kBAAkB,KAAK,MAAM,EAAE;UAC9F9D,OAAO,CAACuF,IAAI,CAAC,6EAA6E,CAAC;UAC3F;UACAzB,kBAAkB,GAAG,GAAG,CAAC,CAAC;UAC1BC,oBAAoB,GAAG,gBAAgB;;UAEvC;UACAtB,YAAY,CAAC6B,OAAO,CAAC,oBAAoB,EAAER,kBAAkB,CAAC;UAC9DrB,YAAY,CAAC6B,OAAO,CAAC,sBAAsB,EAAEP,oBAAoB,CAAC;UAClE/D,OAAO,CAACC,GAAG,CAAC,iDAAiD,EAAE6D,kBAAkB,CAAC;QACpF;;QAEA;QACA,IAAI,CAACA,kBAAkB,EAAE;UACvBhH,QAAQ,CAAC,8DAA8D,CAAC;UACxEF,UAAU,CAAC,KAAK,CAAC;UACjB;QACF;;QAEA;QACA,MAAM4I,aAAa,GAAGC,QAAQ,CAAC3B,kBAAkB,EAAE,EAAE,CAAC;QACtD9D,OAAO,CAACC,GAAG,CAAC,kCAAkC,EAAEuF,aAAa,CAAC;QAC9D,IAAIE,KAAK,CAACF,aAAa,CAAC,EAAE;UACxB1I,QAAQ,CAAC,2BAA2BgH,kBAAkB,mCAAmC,CAAC;UAC1FlH,UAAU,CAAC,KAAK,CAAC;UACjB;QACF;;QAEA;QACAR,aAAa,CAACoJ,aAAa,CAAC;QAC5BlJ,eAAe,CAACyH,oBAAoB,IAAI,YAAYyB,aAAa,EAAE,CAAC;;QAEpE;QACA,MAAMtE,aAAa,CAACsE,aAAa,CAAC;;QAIlC;QACAxF,OAAO,CAACC,GAAG,CAAC,uCAAuC,EAAEuF,aAAa,CAAC;QACnE,IAAI;UACF;UACA,MAAMG,cAAc,GAAG,IAAIC,OAAO,CAAC,CAACC,CAAC,EAAEC,MAAM,KAAK;YAChD1C,UAAU,CAAC,MAAM0C,MAAM,CAAC,IAAIC,KAAK,CAAC,gDAAgD,CAAC,CAAC,EAAE,KAAK,CAAC,CAAC,CAAC;UAChG,CAAC,CAAC;;UAEF;UACA/F,OAAO,CAACC,GAAG,CAAC,oDAAoD,EAAE8B,OAAO,CAAC;UAC1E,MAAMiE,WAAW,GAAGpL,WAAW,CAACgI,OAAO,CAAC4C,aAAa,EAAE,CAAC,EAAEzD,OAAO,CAAC;UAClE,MAAMY,MAAM,GAAG,MAAMiD,OAAO,CAACK,IAAI,CAAC,CAACD,WAAW,EAAEL,cAAc,CAAC,CAAC;UAEhE3F,OAAO,CAACC,GAAG,CAAC,uBAAuB,EAAE0C,MAAM,CAAC;UAC5C3C,OAAO,CAACC,GAAG,CAAC,gCAAgC,EAAE0C,MAAM,GAAGA,MAAM,CAAC5C,MAAM,GAAG,CAAC,CAAC;UACzE,IAAI4C,MAAM,IAAIA,MAAM,CAAC5C,MAAM,GAAG,CAAC,EAAE;YAC/BC,OAAO,CAACC,GAAG,CAAC,oBAAoB,EAAE0C,MAAM,CAAC,CAAC,CAAC,CAAC;UAC9C,CAAC,MAAM;YACL3C,OAAO,CAACuF,IAAI,CAAC,4CAA4C,EAAEC,aAAa,CAAC;UAC3E;UACAhJ,aAAa,CAACmG,MAAM,IAAI,EAAE,CAAC;;UAE3B;UACAhD,mBAAmB,CAACgD,MAAM,IAAI,EAAE,EAAElG,SAAS,CAAC;QAC9C,CAAC,CAAC,OAAOyJ,SAAS,EAAE;UAClBlG,OAAO,CAACnD,KAAK,CAAC,yCAAyC,EAAEqJ,SAAS,CAAC;UACnElG,OAAO,CAACnD,KAAK,CAAC,8BAA8B,EAAE;YAC5CK,OAAO,EAAEgJ,SAAS,CAAChJ,OAAO;YAC1BiJ,MAAM,EAAED,SAAS,CAACC,MAAM;YACxBC,IAAI,EAAEF,SAAS,CAACE,IAAI;YACpBC,KAAK,EAAEH,SAAS,CAACG,KAAK;YACtBC,IAAI,EAAEJ,SAAS,CAACI,IAAI;YACpBC,IAAI,EAAEL,SAAS,CAACK,IAAI;YACpBC,QAAQ,EAAEN,SAAS,CAACM,QAAQ,GAAG;cAC7BL,MAAM,EAAED,SAAS,CAACM,QAAQ,CAACL,MAAM;cACjCM,UAAU,EAAEP,SAAS,CAACM,QAAQ,CAACC,UAAU;cACzCL,IAAI,EAAEF,SAAS,CAACM,QAAQ,CAACJ;YAC3B,CAAC,GAAG;UACN,CAAC,CAAC;;UAEF;UACA5J,aAAa,CAAC,EAAE,CAAC;UACjBwD,OAAO,CAACuF,IAAI,CAAC,sDAAsD,CAAC;;UAEpE;UACAzI,QAAQ,CAAC,2CAA2CoJ,SAAS,CAAChJ,OAAO,+CAA+C,CAAC;QACvH;;QAEA;QACA8C,OAAO,CAACC,GAAG,CAAC,sCAAsC,EAAEuF,aAAa,CAAC;QAClE,IAAI;UACF;UACA,MAAMG,cAAc,GAAG,IAAIC,OAAO,CAAC,CAACC,CAAC,EAAEC,MAAM,KAAK;YAChD1C,UAAU,CAAC,MAAM0C,MAAM,CAAC,IAAIC,KAAK,CAAC,+CAA+C,CAAC,CAAC,EAAE,KAAK,CAAC,CAAC,CAAC;UAC/F,CAAC,CAAC;;UAEF;UACA/F,OAAO,CAACC,GAAG,CAAC,0CAA0C,CAAC;UACvD;UACA,MAAMyG,YAAY,GAAG9L,WAAW,CAACgI,OAAO,CAAC4C,aAAa,EAAE,CAAC,CAAC;UAC1D,MAAMxC,KAAK,GAAG,MAAM4C,OAAO,CAACK,IAAI,CAAC,CAACS,YAAY,EAAEf,cAAc,CAAC,CAAC;UAEhE3F,OAAO,CAACC,GAAG,CAAC,sBAAsB,EAAE+C,KAAK,CAAC;UAC1ChD,OAAO,CAACC,GAAG,CAAC,+BAA+B,EAAE+C,KAAK,GAAGA,KAAK,CAACjD,MAAM,GAAG,CAAC,CAAC;UACtE,IAAIiD,KAAK,IAAIA,KAAK,CAACjD,MAAM,GAAG,CAAC,EAAE;YAC7BC,OAAO,CAACC,GAAG,CAAC,mBAAmB,EAAE+C,KAAK,CAAC,CAAC,CAAC,CAAC;UAC5C,CAAC,MAAM;YACLhD,OAAO,CAACuF,IAAI,CAAC,2CAA2C,EAAEC,aAAa,CAAC;UAC1E;UACA9I,YAAY,CAACsG,KAAK,IAAI,EAAE,CAAC;;UAEzB;UACArD,mBAAmB,CAACpD,UAAU,EAAEyG,KAAK,IAAI,EAAE,CAAC;QAC9C,CAAC,CAAC,OAAOE,UAAU,EAAE;UACnBlD,OAAO,CAACnD,KAAK,CAAC,wCAAwC,EAAEqG,UAAU,CAAC;UACnElD,OAAO,CAACnD,KAAK,CAAC,6BAA6B,EAAE;YAC3CK,OAAO,EAAEgG,UAAU,CAAChG,OAAO;YAC3BiJ,MAAM,EAAEjD,UAAU,CAACiD,MAAM;YACzBC,IAAI,EAAElD,UAAU,CAACkD,IAAI;YACrBC,KAAK,EAAEnD,UAAU,CAACmD,KAAK;YACvBC,IAAI,EAAEpD,UAAU,CAACoD,IAAI;YACrBC,IAAI,EAAErD,UAAU,CAACqD,IAAI;YACrBC,QAAQ,EAAEtD,UAAU,CAACsD,QAAQ,GAAG;cAC9BL,MAAM,EAAEjD,UAAU,CAACsD,QAAQ,CAACL,MAAM;cAClCM,UAAU,EAAEvD,UAAU,CAACsD,QAAQ,CAACC,UAAU;cAC1CL,IAAI,EAAElD,UAAU,CAACsD,QAAQ,CAACJ;YAC5B,CAAC,GAAG;UACN,CAAC,CAAC;;UAEF;UACA1J,YAAY,CAAC,EAAE,CAAC;;UAEhB;UACA,IAAI,CAACG,KAAK,EAAE;YACVC,QAAQ,CAAC,0CAA0CoG,UAAU,CAAChG,OAAO,+CAA+C,CAAC;UACvH;QACF;;QAEA;QACAN,UAAU,CAAC,KAAK,CAAC;MAEnB,CAAC,CAAC,OAAO+J,GAAG,EAAE;QAAA,IAAAC,aAAA,EAAAC,cAAA,EAAAC,cAAA,EAAAC,cAAA,EAAAC,cAAA,EAAAC,mBAAA;QACZjH,OAAO,CAACnD,KAAK,CAAC,kCAAkC,EAAE8J,GAAG,CAAC;QACtD3G,OAAO,CAACnD,KAAK,CAAC,2BAA2B,EAAE;UACzCK,OAAO,EAAEyJ,GAAG,CAACzJ,OAAO;UACpBiJ,MAAM,EAAEQ,GAAG,CAACR,MAAM,MAAAS,aAAA,GAAID,GAAG,CAACH,QAAQ,cAAAI,aAAA,uBAAZA,aAAA,CAAcT,MAAM;UAC1CC,IAAI,EAAEO,GAAG,CAACP,IAAI,MAAAS,cAAA,GAAIF,GAAG,CAACH,QAAQ,cAAAK,cAAA,uBAAZA,cAAA,CAAcT,IAAI;UACpCC,KAAK,EAAEM,GAAG,CAACN;QACb,CAAC,CAAC;;QAEF;QACA,IAAIa,YAAY,GAAG,oBAAoB;QAEvC,IAAIP,GAAG,CAACzJ,OAAO,IAAIyJ,GAAG,CAACzJ,OAAO,CAACsG,QAAQ,CAAC,wBAAwB,CAAC,EAAE;UACjE0D,YAAY,GAAGP,GAAG,CAACzJ,OAAO;QAC5B,CAAC,MAAM,IAAIyJ,GAAG,CAACR,MAAM,KAAK,GAAG,IAAIQ,GAAG,CAACR,MAAM,KAAK,GAAG,IACzC,EAAAW,cAAA,GAAAH,GAAG,CAACH,QAAQ,cAAAM,cAAA,uBAAZA,cAAA,CAAcX,MAAM,MAAK,GAAG,IAAI,EAAAY,cAAA,GAAAJ,GAAG,CAACH,QAAQ,cAAAO,cAAA,uBAAZA,cAAA,CAAcZ,MAAM,MAAK,GAAG,EAAE;UACtEe,YAAY,GAAG,mEAAmE;QACpF,CAAC,MAAM,KAAAF,cAAA,GAAIL,GAAG,CAACH,QAAQ,cAAAQ,cAAA,gBAAAC,mBAAA,GAAZD,cAAA,CAAcZ,IAAI,cAAAa,mBAAA,eAAlBA,mBAAA,CAAoBE,MAAM,EAAE;UACrC;UACAD,YAAY,GAAG,eAAeP,GAAG,CAACH,QAAQ,CAACJ,IAAI,CAACe,MAAM,EAAE;QAC1D,CAAC,MAAM,IAAIR,GAAG,CAACL,IAAI,KAAK,aAAa,EAAE;UACrC;UACAY,YAAY,GAAG,yEAAyE;QAC1F,CAAC,MAAM,IAAIP,GAAG,CAACzJ,OAAO,EAAE;UACtBgK,YAAY,GAAGP,GAAG,CAACzJ,OAAO;QAC5B;QAEAJ,QAAQ,CAAC,gCAAgCoK,YAAY,sBAAsB,CAAC;;QAE5E;QACA1K,aAAa,CAAC,EAAE,CAAC;QACjBE,YAAY,CAAC,EAAE,CAAC;MAClB,CAAC,SAAS;QACRE,UAAU,CAAC,KAAK,CAAC;MACnB;IACF,CAAC;IAEDgH,SAAS,CAAC,CAAC;EACb,CAAC,EAAE,CAAC7B,OAAO,CAAC,CAAC,CAAC,CAAC;;EAEf;;EAEA;EACA,MAAMqF,iBAAiB,GAAIpJ,IAAI,IAAK;IAClCX,eAAe,CAACW,IAAI,CAAC;IACrBT,oBAAoB,CAAC,IAAI,CAAC;EAC5B,CAAC;;EAED;EACA,MAAM8J,kBAAkB,GAAGA,CAAA,KAAM;IAC/B9J,oBAAoB,CAAC,KAAK,CAAC;IAC3BF,eAAe,CAAC,IAAI,CAAC;EACvB,CAAC;;EAED;EACA,MAAMiK,uBAAuB,GAAGA,CAAA,KAAM;IACpCtK,eAAe,CAACuK,IAAI,KAAK;MAAE,GAAGA,IAAI;MAAEtK,IAAI,EAAE;IAAM,CAAC,CAAC,CAAC;EACrD,CAAC;;EAED;EACA,MAAMuK,gBAAgB,GAAGA,CAACtK,OAAO,EAAEC,QAAQ,GAAG,SAAS,KAAK;IAC1DH,eAAe,CAAC;MAAEC,IAAI,EAAE,IAAI;MAAEC,OAAO;MAAEC;IAAS,CAAC,CAAC;EACpD,CAAC;;EAED;EACA,MAAMsK,qBAAqB,GAAGA,CAAA,KAAM;IAClChK,mBAAmB,CAAC,CAACD,gBAAgB,CAAC;IACtC;IACA,IAAIA,gBAAgB,EAAE;MACpBG,qBAAqB,CAAC,EAAE,CAAC;MACzBE,oBAAoB,CAAC,EAAE,CAAC;IAC1B;EACF,CAAC;EAED,MAAM6J,+BAA+B,GAAIC,WAAW,IAAK;IACvDhK,qBAAqB,CAACgK,WAAW,CAAC;EACpC,CAAC;EAED,MAAMC,8BAA8B,GAAID,WAAW,IAAK;IACtD9J,oBAAoB,CAAC8J,WAAW,CAAC;EACnC,CAAC;;EAED;EACA,MAAME,kBAAkB,GAAGA,CAAA,KAAM;IAC/B,MAAMC,kBAAkB,GAAGvL,UAAU,CAAC4D,MAAM,CAACnC,IAAI,IAAIN,kBAAkB,CAAC8F,QAAQ,CAACxF,IAAI,CAAC+J,OAAO,CAAC,CAAC;IAC/F,MAAMC,iBAAiB,GAAGvL,SAAS,CAAC0D,MAAM,CAACnC,IAAI,IAAIJ,iBAAiB,CAAC4F,QAAQ,CAACxF,IAAI,CAAC+J,OAAO,CAAC,CAAC;IAC5F,OAAO,CAAC,GAAGD,kBAAkB,EAAE,GAAGE,iBAAiB,CAAC;EACtD,CAAC;;EAED;EACA,MAAMC,qBAAqB,GAAGA,CAAA,KAAM;IAClC,OAAOvK,kBAAkB,CAACqC,MAAM,GAAGnC,iBAAiB,CAACmC,MAAM;EAC7D,CAAC;;EAED;EACA,MAAMmI,uBAAuB,GAAGA,CAAClK,IAAI,EAAEmK,MAAM,KAAK;IAChDnI,OAAO,CAACC,GAAG,CAAC,0BAA0B,EAAEkI,MAAM,EAAE,WAAW,EAAEnK,IAAI,CAAC;IAElE,QAAQmK,MAAM;MACZ,KAAK,cAAc;QACjBf,iBAAiB,CAACpJ,IAAI,CAAC;QACvB;MACF,KAAK,MAAM;QACT;QACAwJ,gBAAgB,CAAC,iBAAiBxJ,IAAI,CAAC+J,OAAO,6BAA6B,EAAE,MAAM,CAAC;QACpF;MACF,KAAK,QAAQ;QACX;QACA,IAAItE,MAAM,CAAC2E,OAAO,CAAC,yCAAyCpK,IAAI,CAAC+J,OAAO,GAAG,CAAC,EAAE;UAC5EP,gBAAgB,CAAC,qBAAqBxJ,IAAI,CAAC+J,OAAO,6BAA6B,EAAE,SAAS,CAAC;QAC7F;QACA;MACF,KAAK,QAAQ;QACX,IAAIxL,UAAU,CAAC8L,IAAI,CAACtD,CAAC,IAAIA,CAAC,CAACgD,OAAO,KAAK/J,IAAI,CAAC+J,OAAO,CAAC,EAAE;UACpD;UACA,MAAMO,UAAU,GAAG5K,kBAAkB,CAAC8F,QAAQ,CAACxF,IAAI,CAAC+J,OAAO,CAAC;UAC5D,IAAIO,UAAU,EAAE;YACd3K,qBAAqB,CAAC4J,IAAI,IAAIA,IAAI,CAACpH,MAAM,CAACoI,EAAE,IAAIA,EAAE,KAAKvK,IAAI,CAAC+J,OAAO,CAAC,CAAC;UACvE,CAAC,MAAM;YACLpK,qBAAqB,CAAC4J,IAAI,IAAI,CAAC,GAAGA,IAAI,EAAEvJ,IAAI,CAAC+J,OAAO,CAAC,CAAC;UACxD;QACF,CAAC,MAAM;UACL;UACA,MAAMO,UAAU,GAAG1K,iBAAiB,CAAC4F,QAAQ,CAACxF,IAAI,CAAC+J,OAAO,CAAC;UAC3D,IAAIO,UAAU,EAAE;YACdzK,oBAAoB,CAAC0J,IAAI,IAAIA,IAAI,CAACpH,MAAM,CAACoI,EAAE,IAAIA,EAAE,KAAKvK,IAAI,CAAC+J,OAAO,CAAC,CAAC;UACtE,CAAC,MAAM;YACLlK,oBAAoB,CAAC0J,IAAI,IAAI,CAAC,GAAGA,IAAI,EAAEvJ,IAAI,CAAC+J,OAAO,CAAC,CAAC;UACvD;QACF;QACA;QACA,IAAI,CAACvK,gBAAgB,EAAE;UACrBC,mBAAmB,CAAC,IAAI,CAAC;QAC3B;QACA;MACF,KAAK,SAAS;QACZ+K,SAAS,CAACC,SAAS,CAACC,SAAS,CAAC1K,IAAI,CAAC+J,OAAO,CAAC;QAC3CP,gBAAgB,CAAC,WAAWxJ,IAAI,CAAC+J,OAAO,wBAAwB,EAAE,SAAS,CAAC;QAC5E;MACF,KAAK,cAAc;QACjB,MAAMY,OAAO,GAAG,OAAO3K,IAAI,CAAC+J,OAAO,gBAAgB/J,IAAI,CAACiE,SAAS,cAAcjE,IAAI,CAAC4K,OAAO,YAAY5K,IAAI,CAAC6C,aAAa,EAAE;QAC3H2H,SAAS,CAACC,SAAS,CAACC,SAAS,CAACC,OAAO,CAAC;QACtCnB,gBAAgB,CAAC,qCAAqC,EAAE,SAAS,CAAC;QAClE;MACF;QACExH,OAAO,CAACuF,IAAI,CAAC,0BAA0B,EAAE4C,MAAM,CAAC;IACpD;EACF,CAAC;;EAED;EACA,MAAMU,mBAAmB,GAAI7K,IAAI,IAAK;IACpC,MAAMsK,UAAU,GAAG/L,UAAU,CAAC8L,IAAI,CAACtD,CAAC,IAAIA,CAAC,CAACgD,OAAO,MAAK/J,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAE+J,OAAO,EAAC,GAChErK,kBAAkB,CAAC8F,QAAQ,CAACxF,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAE+J,OAAO,CAAC,GAC1CnK,iBAAiB,CAAC4F,QAAQ,CAACxF,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAE+J,OAAO,CAAC;IAE7C,OAAO,CACL;MACEe,IAAI,EAAE,QAAQ;MACdC,KAAK,EAAE,QAAQ,CAAA/K,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAE+J,OAAO,KAAI,EAAE;IACpC,CAAC,EACD;MACEQ,EAAE,EAAE,cAAc;MAClBQ,KAAK,EAAE,qBAAqB;MAC5BC,IAAI,eAAE5N,OAAA,CAACzB,cAAc;QAACsP,QAAQ,EAAC;MAAO;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC;MACzClB,MAAM,EAAE,cAAc;MACtBmB,OAAO,EAAEpB;IACX,CAAC,EACD;MACEY,IAAI,EAAE;IACR,CAAC,EACD;MACEP,EAAE,EAAE,MAAM;MACVQ,KAAK,EAAE,UAAU;MACjBC,IAAI,eAAE5N,OAAA,CAACvB,QAAQ;QAACoP,QAAQ,EAAC;MAAO;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC;MACnClB,MAAM,EAAE,MAAM;MACdmB,OAAO,EAAEpB,uBAAuB;MAChCqB,KAAK,EAAE;IACT,CAAC,EACD;MACEhB,EAAE,EAAE,QAAQ;MACZQ,KAAK,EAAE,SAAS;MAChBC,IAAI,eAAE5N,OAAA,CAACrB,UAAU;QAACkP,QAAQ,EAAC;MAAO;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC;MACrClB,MAAM,EAAE,QAAQ;MAChBmB,OAAO,EAAEpB,uBAAuB;MAChCqB,KAAK,EAAE;IACT,CAAC,EACD;MACET,IAAI,EAAE;IACR,CAAC,EACD;MACEP,EAAE,EAAE,QAAQ;MACZQ,KAAK,EAAET,UAAU,GAAG,aAAa,GAAG,WAAW;MAC/CU,IAAI,eAAE5N,OAAA,CAACnB,aAAa;QAACgP,QAAQ,EAAC;MAAO;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC;MACxClB,MAAM,EAAE,QAAQ;MAChBmB,OAAO,EAAEpB,uBAAuB;MAChCqB,KAAK,EAAEjB,UAAU,GAAG,SAAS,GAAG;IAClC,CAAC,EACD;MACEQ,IAAI,EAAE;IACR,CAAC,EACD;MACEP,EAAE,EAAE,SAAS;MACbQ,KAAK,EAAE,UAAU;MACjBC,IAAI,eAAE5N,OAAA,CAACjB,QAAQ;QAAC8O,QAAQ,EAAC;MAAO;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC;MACnClB,MAAM,EAAE,SAAS;MACjBmB,OAAO,EAAEpB,uBAAuB;MAChCsB,QAAQ,EAAE;IACZ,CAAC,EACD;MACEjB,EAAE,EAAE,cAAc;MAClBQ,KAAK,EAAE,gBAAgB;MACvBC,IAAI,eAAE5N,OAAA,CAACjB,QAAQ;QAAC8O,QAAQ,EAAC;MAAO;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC;MACnClB,MAAM,EAAE,cAAc;MACtBmB,OAAO,EAAEpB,uBAAuB;MAChCuB,WAAW,EAAE;IACf,CAAC,CACF;EACH,CAAC;;EAED;EACA,MAAMC,kBAAkB,GAAG,MAAAA,CAAO1L,IAAI,EAAE2L,UAAU,EAAEC,WAAW,KAAK;IAClE5J,OAAO,CAACC,GAAG,CAAC,mCAAmC,CAAC;IAChDD,OAAO,CAACC,GAAG,CAAC,wBAAwB,EAAE0J,UAAU,EAAE,WAAW,EAAE3L,IAAI,CAAC;IACpEgC,OAAO,CAACC,GAAG,CAAC,eAAe,EAAE2J,WAAW,CAAC;IAEzC,IAAID,UAAU,KAAK,eAAe,EAAE;MAClC;MACA5L,uBAAuB,CAAC;QACtBd,IAAI,EAAE,IAAI;QACVe,IAAI,EAAEA,IAAI;QACVrB,OAAO,EAAE;MACX,CAAC,CAAC;IACJ,CAAC,MAAM,IAAIgN,UAAU,KAAK,aAAa,EAAE;MACvC;MACA,IAAI;QACF3J,OAAO,CAACC,GAAG,CAAC,kCAAkC,EAAE9D,UAAU,CAAC;QAC3D,MAAM0N,MAAM,GAAG,MAAMhP,gBAAgB,CAACiP,SAAS,CAAC3N,UAAU,CAAC;QAC3D6D,OAAO,CAACC,GAAG,CAAC,kBAAkB,EAAE4J,MAAM,CAAC;QAEvCzL,oBAAoB,CAACyL,MAAM,IAAI,EAAE,CAAC;QAClC3L,uBAAuB,CAAC;UACtBjB,IAAI,EAAE,IAAI;UACVe,IAAI,EAAEA,IAAI;UACVrB,OAAO,EAAE;QACX,CAAC,CAAC;MACJ,CAAC,CAAC,OAAOE,KAAK,EAAE;QACdmD,OAAO,CAACnD,KAAK,CAAC,sCAAsC,EAAEA,KAAK,CAAC;QAC5D2K,gBAAgB,CAAC,iDAAiD,EAAE,OAAO,CAAC;MAC9E;IACF;EACF,CAAC;;EAED;EACA,MAAMuC,eAAe,GAAG,MAAAA,CAAOC,MAAM,EAAEC,KAAK,KAAK;IAC/ClM,uBAAuB,CAACwJ,IAAI,KAAK;MAAE,GAAGA,IAAI;MAAE5K,OAAO,EAAE;IAAK,CAAC,CAAC,CAAC;IAE7D,IAAI;MACFqD,OAAO,CAACC,GAAG,CAAC,0BAA0B,EAAE+J,MAAM,EAAE,QAAQ,EAAEC,KAAK,EAAE,WAAW,EAAE9N,UAAU,CAAC;;MAEzF;MACA,MAAM+N,cAAc,GAAG,MAAMtP,WAAW,CAACuP,iBAAiB,CACxDhO,UAAU,EACV6N,MAAM,EACNC,KAAK,EACL,IAAI;MAAE;MACN,IAAI,CAAE;MACR,CAAC;MAEDjK,OAAO,CAACC,GAAG,CAAC,kBAAkB,EAAEiK,cAAc,CAAC;;MAE/C;MACA,MAAME,UAAU,GAAIC,IAAI,IAAKA,IAAI,CAACvF,GAAG,CAAC9G,IAAI,IACxCA,IAAI,CAAC+J,OAAO,KAAKiC,MAAM,GACnB;QACE,GAAGhM,IAAI;QACP8C,eAAe,EAAEmJ,KAAK;QACtB7J,mBAAmB,EAAE8J,cAAc,CAAC9J,mBAAmB,KAAK6J,KAAK,IAAIjM,IAAI,CAAC6C,aAAa,GAAG,YAAY,GAAG,UAAU;MACrH,CAAC,GACD7C,IACN,CAAC;MAEDxB,aAAa,CAAC4N,UAAU,CAAC;MACzB1N,YAAY,CAAC0N,UAAU,CAAC;;MAExB;MACArM,uBAAuB,CAAC;QAAEd,IAAI,EAAE,KAAK;QAAEe,IAAI,EAAE,IAAI;QAAErB,OAAO,EAAE;MAAM,CAAC,CAAC;MAEpE6K,gBAAgB,CAAC,uCAAuCwC,MAAM,KAAKC,KAAK,GAAG,EAAE,SAAS,CAAC;;MAEvF;MACA7G,UAAU,CAAC,MAAMb,SAAS,CAAC,IAAI,CAAC,EAAE,GAAG,CAAC;IAExC,CAAC,CAAC,OAAO1F,KAAK,EAAE;MACdmD,OAAO,CAACnD,KAAK,CAAC,mCAAmC,EAAEA,KAAK,CAAC;MACzD,MAAMqK,YAAY,GAAGrK,KAAK,CAACsK,MAAM,IAAItK,KAAK,CAACK,OAAO,IAAI,yCAAyC;MAC/FsK,gBAAgB,CAACN,YAAY,EAAE,OAAO,CAAC;MACvCnJ,uBAAuB,CAACwJ,IAAI,KAAK;QAAE,GAAGA,IAAI;QAAE5K,OAAO,EAAE;MAAM,CAAC,CAAC,CAAC;IAChE;EACF,CAAC;;EAED;EACA,MAAM2N,kBAAkB,GAAG,MAAAA,CAAON,MAAM,EAAEO,aAAa,EAAEC,WAAW,KAAK;IACvEtM,uBAAuB,CAACqJ,IAAI,KAAK;MAAE,GAAGA,IAAI;MAAE5K,OAAO,EAAE;IAAK,CAAC,CAAC,CAAC;IAE7D,IAAI;MACFqD,OAAO,CAACC,GAAG,CAAC,8BAA8B,EAAE+J,MAAM,EAAE,eAAe,EAAEO,aAAa,EAAE,cAAc,EAAEC,WAAW,EAAE,WAAW,EAAErO,UAAU,CAAC;;MAEzI;MACA,MAAM+N,cAAc,GAAG,MAAMtP,WAAW,CAAC6P,YAAY,CACnDtO,UAAU,EACV6N,MAAM,EACNO,aAAa,EACb,IAAI,CAAC;MACP,CAAC;MAEDvK,OAAO,CAACC,GAAG,CAAC,mCAAmC,EAAEiK,cAAc,CAAC;;MAEhE;MACA,MAAME,UAAU,GAAIC,IAAI,IAAKA,IAAI,CAACvF,GAAG,CAAC9G,IAAI,IACxCA,IAAI,CAAC+J,OAAO,KAAKiC,MAAM,GACnB;QAAE,GAAGhM,IAAI;QAAE0M,SAAS,EAAEH;MAAc,CAAC,GACrCvM,IACN,CAAC;MAEDxB,aAAa,CAAC4N,UAAU,CAAC;MACzB1N,YAAY,CAAC0N,UAAU,CAAC;;MAExB;MACAlM,uBAAuB,CAAC;QAAEjB,IAAI,EAAE,KAAK;QAAEe,IAAI,EAAE,IAAI;QAAErB,OAAO,EAAE;MAAM,CAAC,CAAC;MAEpE6K,gBAAgB,CAAC,iCAAiCwC,MAAM,KAAKO,aAAa,EAAE,EAAE,SAAS,CAAC;;MAExF;MACAnH,UAAU,CAAC,MAAMb,SAAS,CAAC,IAAI,CAAC,EAAE,GAAG,CAAC;IAExC,CAAC,CAAC,OAAO1F,KAAK,EAAE;MACdmD,OAAO,CAACnD,KAAK,CAAC,qCAAqC,EAAEA,KAAK,CAAC;MAC3D,MAAMqK,YAAY,GAAGrK,KAAK,CAACsK,MAAM,IAAItK,KAAK,CAACK,OAAO,IAAI,oCAAoC;MAC1FsK,gBAAgB,CAACN,YAAY,EAAE,OAAO,CAAC;MACvChJ,uBAAuB,CAACqJ,IAAI,KAAK;QAAE,GAAGA,IAAI;QAAE5K,OAAO,EAAE;MAAM,CAAC,CAAC,CAAC;IAChE;EACF,CAAC;;EAED;EACA,MAAMgO,yBAAyB,GAAGA,CAAA,KAAM;IACtC,IAAI,CAAC7M,oBAAoB,CAACnB,OAAO,EAAE;MACjCoB,uBAAuB,CAAC;QAAEd,IAAI,EAAE,KAAK;QAAEe,IAAI,EAAE,IAAI;QAAErB,OAAO,EAAE;MAAM,CAAC,CAAC;IACtE;EACF,CAAC;EAED,MAAMiO,yBAAyB,GAAGA,CAAA,KAAM;IACtC,IAAI,CAAC3M,oBAAoB,CAACtB,OAAO,EAAE;MACjCuB,uBAAuB,CAAC;QAAEjB,IAAI,EAAE,KAAK;QAAEe,IAAI,EAAE,IAAI;QAAErB,OAAO,EAAE;MAAM,CAAC,CAAC;IACtE;EACF,CAAC;;EAED;;EAEA;EACA,MAAMkO,eAAe,GAAGA,CAAA,kBACtBzP,OAAA,CAAC/D,KAAK;IAACyT,EAAE,EAAE;MAAEC,CAAC,EAAE,CAAC;MAAEC,EAAE,EAAE,CAAC;MAAEC,OAAO,EAAE;IAAU,CAAE;IAAAC,QAAA,eAC7C9P,OAAA,CAAC5C,KAAK;MAAC2S,SAAS,EAAC,KAAK;MAACC,OAAO,EAAE,CAAE;MAACC,UAAU,EAAC,QAAQ;MAACC,cAAc,EAAC,eAAe;MAACC,QAAQ,EAAC,MAAM;MAAAL,QAAA,gBAEnG9P,OAAA,CAAC5C,KAAK;QAAC2S,SAAS,EAAC,KAAK;QAACE,UAAU,EAAC,QAAQ;QAACD,OAAO,EAAE,CAAE;QAAAF,QAAA,gBACpD9P,OAAA,CAACzC,SAAS;UAAC4Q,KAAK,EAAC,SAAS;UAACN,QAAQ,EAAC;QAAO;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,eAC9CjO,OAAA,CAACjE,GAAG;UAAA+T,QAAA,gBACF9P,OAAA,CAAChE,UAAU;YAACoU,OAAO,EAAC,IAAI;YAACC,UAAU,EAAC,MAAM;YAACX,EAAE,EAAE;cAAEY,UAAU,EAAE;YAAE,CAAE;YAAAR,QAAA,EAC9D7M,UAAU,CAACE;UAAU;YAAA2K,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACZ,CAAC,eACbjO,OAAA,CAAChE,UAAU;YAACoU,OAAO,EAAC,SAAS;YAACjC,KAAK,EAAC,gBAAgB;YAAA2B,QAAA,EAAC;UAErD;YAAAhC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAY,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACV,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACD,CAAC,eAERjO,OAAA,CAAC5C,KAAK;QAAC2S,SAAS,EAAC,KAAK;QAACE,UAAU,EAAC,QAAQ;QAACD,OAAO,EAAE,CAAE;QAAAF,QAAA,gBACpD9P,OAAA,CAACvC,eAAe;UAAC0Q,KAAK,EAAC,SAAS;UAACN,QAAQ,EAAC;QAAO;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,eACpDjO,OAAA,CAACjE,GAAG;UAAA+T,QAAA,gBACF9P,OAAA,CAAChE,UAAU;YAACoU,OAAO,EAAC,IAAI;YAACC,UAAU,EAAC,MAAM;YAACX,EAAE,EAAE;cAAEY,UAAU,EAAE;YAAE,CAAE;YAAAR,QAAA,EAC9D7M,UAAU,CAACG;UAAc;YAAA0K,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAChB,CAAC,eACbjO,OAAA,CAAChE,UAAU;YAACoU,OAAO,EAAC,SAAS;YAACjC,KAAK,EAAC,gBAAgB;YAAA2B,QAAA,EAAC;UAErD;YAAAhC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAY,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACV,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACD,CAAC,eAERjO,OAAA,CAAC5C,KAAK;QAAC2S,SAAS,EAAC,KAAK;QAACE,UAAU,EAAC,QAAQ;QAACD,OAAO,EAAE,CAAE;QAAAF,QAAA,gBACpD9P,OAAA,CAACnC,QAAQ;UAACsQ,KAAK,EAAC,MAAM;UAACN,QAAQ,EAAC;QAAO;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,eAC1CjO,OAAA,CAACjE,GAAG;UAAA+T,QAAA,gBACF9P,OAAA,CAAChE,UAAU;YAACoU,OAAO,EAAC,IAAI;YAACC,UAAU,EAAC,MAAM;YAACX,EAAE,EAAE;cAAEY,UAAU,EAAE;YAAE,CAAE;YAAAR,QAAA,EAC9D7M,UAAU,CAACM;UAAa;YAAAuK,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACf,CAAC,eACbjO,OAAA,CAAChE,UAAU;YAACoU,OAAO,EAAC,SAAS;YAACjC,KAAK,EAAC,gBAAgB;YAAA2B,QAAA,EAAC;UAErD;YAAAhC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAY,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACV,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACD,CAAC,eAERjO,OAAA,CAAC5C,KAAK;QAAC2S,SAAS,EAAC,KAAK;QAACE,UAAU,EAAC,QAAQ;QAACD,OAAO,EAAE,CAAE;QAAAF,QAAA,gBACpD9P,OAAA,CAACb,YAAY;UAACgP,KAAK,EAAC,SAAS;UAACN,QAAQ,EAAC;QAAO;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,eACjDjO,OAAA,CAACjE,GAAG;UAAA+T,QAAA,gBACF9P,OAAA,CAAChE,UAAU;YAACoU,OAAO,EAAC,IAAI;YAACC,UAAU,EAAC,MAAM;YAACX,EAAE,EAAE;cAAEY,UAAU,EAAE;YAAE,CAAE;YAAAR,QAAA,EAC9D7M,UAAU,CAACQ;UAAe;YAAAqK,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACjB,CAAC,eACbjO,OAAA,CAAChE,UAAU;YAACoU,OAAO,EAAC,SAAS;YAACjC,KAAK,EAAC,gBAAgB;YAAA2B,QAAA,EAAC;UAErD;YAAAhC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAY,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACV,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACD,CAAC,eAERjO,OAAA,CAAC5C,KAAK;QAAC2S,SAAS,EAAC,KAAK;QAACE,UAAU,EAAC,QAAQ;QAACD,OAAO,EAAE,CAAE;QAAAF,QAAA,gBACpD9P,OAAA,CAACjE,GAAG;UAAC2T,EAAE,EAAE;YACPa,KAAK,EAAE,EAAE;YACTC,MAAM,EAAE,EAAE;YACVC,YAAY,EAAE,KAAK;YACnBZ,OAAO,EAAE5M,UAAU,CAACU,wBAAwB,IAAI,EAAE,GAAG,cAAc,GAC1DV,UAAU,CAACU,wBAAwB,IAAI,EAAE,GAAG,cAAc,GAAG,YAAY;YAClF+M,OAAO,EAAE,MAAM;YACfT,UAAU,EAAE,QAAQ;YACpBC,cAAc,EAAE;UAClB,CAAE;UAAAJ,QAAA,eACA9P,OAAA,CAAChE,UAAU;YAACoU,OAAO,EAAC,SAAS;YAACC,UAAU,EAAC,MAAM;YAAClC,KAAK,EAAC,OAAO;YAAA2B,QAAA,GAC1D7M,UAAU,CAACU,wBAAwB,EAAC,GACvC;UAAA;YAAAmK,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAY;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACV,CAAC,eACNjO,OAAA,CAACjE,GAAG;UAAA+T,QAAA,gBACF9P,OAAA,CAAChE,UAAU;YAACoU,OAAO,EAAC,OAAO;YAACC,UAAU,EAAC,QAAQ;YAACX,EAAE,EAAE;cAAEY,UAAU,EAAE;YAAE,CAAE;YAAAR,QAAA,EAAC;UAEvE;YAAAhC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAY,CAAC,eACbjO,OAAA,CAAChE,UAAU;YAACoU,OAAO,EAAC,SAAS;YAACjC,KAAK,EAAC,gBAAgB;YAAA2B,QAAA,GACjD7M,UAAU,CAACc,eAAe,EAAC,cAC9B;UAAA;YAAA+J,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAY,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACV,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACD,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CACR;;EAED;;EAEA;;EAEA;EACA,MAAM0C,mBAAmB,GAAGA,CAAA,KAAM;IAChC,IAAI,CAAC3O,YAAY,EAAE,OAAO,IAAI;IAE9B,oBACEhC,OAAA,CAACrD,MAAM;MAACkF,IAAI,EAAEK,iBAAkB;MAAC0O,OAAO,EAAE3E,kBAAmB;MAAC4E,QAAQ,EAAC,IAAI;MAACC,SAAS;MAAAhB,QAAA,gBACnF9P,OAAA,CAACpD,WAAW;QAAAkT,QAAA,GAAC,iBACI,EAAC9N,YAAY,CAAC2K,OAAO;MAAA;QAAAmB,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACzB,CAAC,eACdjO,OAAA,CAACnD,aAAa;QAACkU,QAAQ;QAAAjB,QAAA,eACrB9P,OAAA,CAAC7D,IAAI;UAAC6U,SAAS;UAAChB,OAAO,EAAE,CAAE;UAAAF,QAAA,gBACzB9P,OAAA,CAAC7D,IAAI;YAAC8U,IAAI;YAACC,EAAE,EAAE,EAAG;YAACC,EAAE,EAAE,CAAE;YAAArB,QAAA,gBACvB9P,OAAA,CAAChE,UAAU;cAACoU,OAAO,EAAC,WAAW;cAACgB,YAAY;cAAAtB,QAAA,EAAC;YAAqB;cAAAhC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAY,CAAC,eAC/EjO,OAAA,CAACjE,GAAG;cAAC2T,EAAE,EAAE;gBAAEE,EAAE,EAAE;cAAE,CAAE;cAAAE,QAAA,gBACjB9P,OAAA,CAAChE,UAAU;gBAACoU,OAAO,EAAC,OAAO;gBAAAN,QAAA,gBAAC9P,OAAA;kBAAA8P,QAAA,EAAQ;gBAAQ;kBAAAhC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,KAAC,EAACjM,YAAY,CAACqP,OAAO,IAAI,KAAK;cAAA;gBAAAvD,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAa,CAAC,eAClGjO,OAAA,CAAChE,UAAU;gBAACoU,OAAO,EAAC,OAAO;gBAAAN,QAAA,gBAAC9P,OAAA;kBAAA8P,QAAA,EAAQ;gBAAQ;kBAAAhC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,KAAC,EAACjM,YAAY,CAACsP,OAAO,IAAI,KAAK;cAAA;gBAAAxD,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAa,CAAC,eAClGjO,OAAA,CAAChE,UAAU;gBAACoU,OAAO,EAAC,OAAO;gBAAAN,QAAA,gBAAC9P,OAAA;kBAAA8P,QAAA,EAAQ;gBAAU;kBAAAhC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,KAAC,EAACjM,YAAY,CAAC6E,SAAS,IAAI,KAAK;cAAA;gBAAAiH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAa,CAAC,eACtGjO,OAAA,CAAChE,UAAU;gBAACoU,OAAO,EAAC,OAAO;gBAAAN,QAAA,gBAAC9P,OAAA;kBAAA8P,QAAA,EAAQ;gBAAO;kBAAAhC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,KAAC,EAACjM,YAAY,CAACuP,WAAW,IAAI,KAAK;cAAA;gBAAAzD,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAa,CAAC,eAErGjO,OAAA,CAAChE,UAAU;gBAACoU,OAAO,EAAC,OAAO;gBAAAN,QAAA,gBAAC9P,OAAA;kBAAA8P,QAAA,EAAQ;gBAAW;kBAAAhC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,KAAC,EAACjM,YAAY,CAACwL,OAAO,IAAI,KAAK;cAAA;gBAAAM,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAa,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAElG,CAAC,eAENjO,OAAA,CAAChE,UAAU;cAACoU,OAAO,EAAC,WAAW;cAACgB,YAAY;cAAAtB,QAAA,EAAC;YAAQ;cAAAhC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAY,CAAC,eAClEjO,OAAA,CAACjE,GAAG;cAAC2T,EAAE,EAAE;gBAAEE,EAAE,EAAE;cAAE,CAAE;cAAAE,QAAA,gBACjB9P,OAAA,CAAChE,UAAU;gBAACoU,OAAO,EAAC,OAAO;gBAAAN,QAAA,gBAAC9P,OAAA;kBAAA8P,QAAA,EAAQ;gBAAW;kBAAAhC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,KAAC,EAACjM,YAAY,CAACwP,mBAAmB,IAAI,KAAK;cAAA;gBAAA1D,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAa,CAAC,eACjHjO,OAAA,CAAChE,UAAU;gBAACoU,OAAO,EAAC,OAAO;gBAAAN,QAAA,gBAAC9P,OAAA;kBAAA8P,QAAA,EAAQ;gBAAO;kBAAAhC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,KAAC,EAACjM,YAAY,CAACyP,eAAe,IAAI,KAAK;cAAA;gBAAA3D,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAa,CAAC,eACzGjO,OAAA,CAAChE,UAAU;gBAACoU,OAAO,EAAC,OAAO;gBAAAN,QAAA,gBAAC9P,OAAA;kBAAA8P,QAAA,EAAQ;gBAAY;kBAAAhC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,KAAC,EAACjM,YAAY,CAAC0P,2BAA2B,IAAI,KAAK;cAAA;gBAAA5D,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAa,CAAC,eAC1HjO,OAAA,CAAChE,UAAU;gBAACoU,OAAO,EAAC,OAAO;gBAAAN,QAAA,gBAAC9P,OAAA;kBAAA8P,QAAA,EAAQ;gBAAa;kBAAAhC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,KAAC,EAACjM,YAAY,CAACkD,qBAAqB,IAAI,KAAK;cAAA;gBAAA4I,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAa,CAAC,eACrHjO,OAAA,CAAChE,UAAU;gBAACoU,OAAO,EAAC,OAAO;gBAAAN,QAAA,gBAAC9P,OAAA;kBAAA8P,QAAA,EAAQ;gBAAQ;kBAAAhC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,KAAC,EAACjM,YAAY,CAAC2P,gBAAgB,IAAI,KAAK;cAAA;gBAAA7D,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAa,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACxG,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACF,CAAC,eAEPjO,OAAA,CAAC7D,IAAI;YAAC8U,IAAI;YAACC,EAAE,EAAE,EAAG;YAACC,EAAE,EAAE,CAAE;YAAArB,QAAA,gBACvB9P,OAAA,CAAChE,UAAU;cAACoU,OAAO,EAAC,WAAW;cAACgB,YAAY;cAAAtB,QAAA,EAAC;YAAM;cAAAhC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAY,CAAC,eAChEjO,OAAA,CAACjE,GAAG;cAAC2T,EAAE,EAAE;gBAAEE,EAAE,EAAE;cAAE,CAAE;cAAAE,QAAA,gBACjB9P,OAAA,CAAChE,UAAU;gBAACoU,OAAO,EAAC,OAAO;gBAAAN,QAAA,gBAAC9P,OAAA;kBAAA8P,QAAA,EAAQ;gBAAW;kBAAAhC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,KAAC,EAACjM,YAAY,CAAC4P,iBAAiB,IAAI,KAAK;cAAA;gBAAA9D,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAa,CAAC,eAC/GjO,OAAA,CAAChE,UAAU;gBAACoU,OAAO,EAAC,OAAO;gBAAAN,QAAA,gBAAC9P,OAAA;kBAAA8P,QAAA,EAAQ;gBAAO;kBAAAhC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,KAAC,EAACjM,YAAY,CAAC6P,aAAa,IAAI,KAAK;cAAA;gBAAA/D,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAa,CAAC,eACvGjO,OAAA,CAAChE,UAAU;gBAACoU,OAAO,EAAC,OAAO;gBAAAN,QAAA,gBAAC9P,OAAA;kBAAA8P,QAAA,EAAQ;gBAAY;kBAAAhC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,KAAC,EAACjM,YAAY,CAAC8P,yBAAyB,IAAI,KAAK;cAAA;gBAAAhE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAa,CAAC,eACxHjO,OAAA,CAAChE,UAAU;gBAACoU,OAAO,EAAC,OAAO;gBAAAN,QAAA,gBAAC9P,OAAA;kBAAA8P,QAAA,EAAQ;gBAAa;kBAAAhC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,KAAC,EAACjM,YAAY,CAACmD,mBAAmB,IAAI,KAAK;cAAA;gBAAA2I,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAa,CAAC,eACnHjO,OAAA,CAAChE,UAAU;gBAACoU,OAAO,EAAC,OAAO;gBAAAN,QAAA,gBAAC9P,OAAA;kBAAA8P,QAAA,EAAQ;gBAAQ;kBAAAhC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,KAAC,EAACjM,YAAY,CAAC+P,cAAc,IAAI,KAAK;cAAA;gBAAAjE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAa,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACtG,CAAC,eAENjO,OAAA,CAAChE,UAAU;cAACoU,OAAO,EAAC,WAAW;cAACgB,YAAY;cAAAtB,QAAA,EAAC;YAAa;cAAAhC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAY,CAAC,eACvEjO,OAAA,CAACjE,GAAG;cAAC2T,EAAE,EAAE;gBAAEE,EAAE,EAAE;cAAE,CAAE;cAAAE,QAAA,gBACjB9P,OAAA,CAAChE,UAAU;gBAACoU,OAAO,EAAC,OAAO;gBAAAN,QAAA,gBAAC9P,OAAA;kBAAA8P,QAAA,EAAQ;gBAAc;kBAAAhC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,KAAC,EAACjM,YAAY,CAACyD,aAAa,IAAI,KAAK;cAAA;gBAAAqI,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAa,CAAC,eAC9GjO,OAAA,CAAChE,UAAU;gBAACoU,OAAO,EAAC,OAAO;gBAAAN,QAAA,gBAAC9P,OAAA;kBAAA8P,QAAA,EAAQ;gBAAgB;kBAAAhC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,KAAC,EAACjM,YAAY,CAAC0D,eAAe,IAAI,GAAG;cAAA;gBAAAoI,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAa,CAAC,eAChHjO,OAAA,CAAChE,UAAU;gBAACoU,OAAO,EAAC,OAAO;gBAAAN,QAAA,gBAAC9P,OAAA;kBAAA8P,QAAA,EAAQ;gBAAM;kBAAAhC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,KAAC,EAACtO,2BAA2B,CAACqC,YAAY,CAACgD,mBAAmB,CAAC;cAAA;gBAAA8I,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAa,CAAC,eAChIjO,OAAA,CAAChE,UAAU;gBAACoU,OAAO,EAAC,OAAO;gBAAAN,QAAA,gBAAC9P,OAAA;kBAAA8P,QAAA,EAAQ;gBAAa;kBAAAhC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,KAAC,EAACjM,YAAY,CAACiD,YAAY,IAAI,GAAG;cAAA;gBAAA6I,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAa,CAAC,eAC1GjO,OAAA,CAAChE,UAAU;gBAACoU,OAAO,EAAC,OAAO;gBAAAN,QAAA,gBAAC9P,OAAA;kBAAA8P,QAAA,EAAQ;gBAAO;kBAAAhC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,KAAC,EAACjM,YAAY,CAACsN,SAAS,IAAI,KAAK;cAAA;gBAAAxB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAa,CAAC,eACnGjO,OAAA,CAAChE,UAAU;gBAACoU,OAAO,EAAC,OAAO;gBAAAN,QAAA,gBAAC9P,OAAA;kBAAA8P,QAAA,EAAQ;gBAAkB;kBAAAhC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,KAAC,EAACjM,YAAY,CAACgQ,iBAAiB,IAAI,KAAK;cAAA;gBAAAlE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAa,CAAC,eACtHjO,OAAA,CAAChE,UAAU;gBAACoU,OAAO,EAAC,OAAO;gBAAAN,QAAA,gBAAC9P,OAAA;kBAAA8P,QAAA,EAAQ;gBAAa;kBAAAhC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,KAAC,EAACjM,YAAY,CAACiQ,YAAY,IAAI,KAAK;cAAA;gBAAAnE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAa,CAAC,eAC5GjO,OAAA,CAAChE,UAAU;gBAACoU,OAAO,EAAC,OAAO;gBAAAN,QAAA,gBAAC9P,OAAA;kBAAA8P,QAAA,EAAQ;gBAAqB;kBAAAhC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,KAAC,EAAC,IAAIiE,IAAI,CAAClQ,YAAY,CAACmQ,SAAS,CAAC,CAACC,cAAc,CAAC,CAAC;cAAA;gBAAAtE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAa,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAChI,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACF,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACM,CAAC,eAChBjO,OAAA,CAAClD,aAAa;QAAAgT,QAAA,eACZ9P,OAAA,CAAC9D,MAAM;UAACgS,OAAO,EAAEjC,kBAAmB;UAAA6D,QAAA,EAAC;QAAM;UAAAhC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACvC,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACV,CAAC;EAEb,CAAC;;EAED;;EAIA,oBACEjO,OAAA,CAACjE,GAAG;IAACsW,SAAS,EAAC,WAAW;IAAAvC,QAAA,EACvBvO,OAAO,gBACNvB,OAAA,CAACjE,GAAG;MAAC2T,EAAE,EAAE;QAAEgB,OAAO,EAAE,MAAM;QAAE4B,aAAa,EAAE,QAAQ;QAAErC,UAAU,EAAE,QAAQ;QAAEsC,EAAE,EAAE;MAAE,CAAE;MAAAzC,QAAA,gBACjF9P,OAAA,CAACvD,gBAAgB;QAAC+V,IAAI,EAAE;MAAG;QAAA1E,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC,eAC9BjO,OAAA,CAAChE,UAAU;QAAC0T,EAAE,EAAE;UAAE6C,EAAE,EAAE;QAAE,CAAE;QAAAzC,QAAA,EAAC;MAAmB;QAAAhC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAY,CAAC,eAC3DjO,OAAA,CAAC9D,MAAM;QACLkU,OAAO,EAAC,UAAU;QAClBjC,KAAK,EAAC,SAAS;QACfD,OAAO,EAAEA,CAAA,KAAM7F,MAAM,CAACC,QAAQ,CAACC,MAAM,CAAC,CAAE;QACxCmH,EAAE,EAAE;UAAE6C,EAAE,EAAE;QAAE,CAAE;QAAAzC,QAAA,EACf;MAED;QAAAhC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAQ,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACN,CAAC,GACJxM,KAAK,gBACPzB,OAAA,CAACjE,GAAG;MAAA+T,QAAA,gBACF9P,OAAA,CAAC1D,KAAK;QAACyF,QAAQ,EAAC,OAAO;QAAC2N,EAAE,EAAE;UAAEE,EAAE,EAAE;QAAE,CAAE;QAAAE,QAAA,GACnCrO,KAAK,EACLA,KAAK,CAAC2G,QAAQ,CAAC,eAAe,CAAC,iBAC9BpI,OAAA,CAAChE,UAAU;UAACoU,OAAO,EAAC,OAAO;UAACV,EAAE,EAAE;YAAE6C,EAAE,EAAE;UAAE,CAAE;UAAAzC,QAAA,gBACxC9P,OAAA;YAAA8P,QAAA,EAAQ;UAAa;YAAAhC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC,uEAC9B,eAAAjO,OAAA;YAAA8N,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAK,CAAC,8CACoC,eAAAjO,OAAA;YAAA8P,QAAA,EAAM;UAAa;YAAAhC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC,4CACtE;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAY,CACb;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACI,CAAC,eACRjO,OAAA,CAACjE,GAAG;QAAC2T,EAAE,EAAE;UAAEgB,OAAO,EAAE,MAAM;UAAE+B,GAAG,EAAE;QAAE,CAAE;QAAA3C,QAAA,eACnC9P,OAAA,CAAC9D,MAAM;UACLkU,OAAO,EAAC,WAAW;UACnBiC,SAAS,EAAC,gBAAgB;UAC1BnE,OAAO,EAAEA,CAAA,KAAM7F,MAAM,CAACC,QAAQ,CAACC,MAAM,CAAC,CAAE;UAAAuH,QAAA,EACzC;QAED;UAAAhC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACN,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,gBAENjO,OAAA,CAACjE,GAAG;MAAA+T,QAAA,GAED7L,oBAAoB,CAACU,MAAM,GAAG,CAAC,iBAC9B3E,OAAA,CAAC/D,KAAK;QAACyT,EAAE,EAAE;UAAEC,CAAC,EAAE,CAAC;UAAEC,EAAE,EAAE;QAAE,CAAE;QAAAE,QAAA,eACzB9P,OAAA,CAACjE,GAAG;UAAC2T,EAAE,EAAE;YAAEgB,OAAO,EAAE,MAAM;YAAET,UAAU,EAAE,QAAQ;YAAEwC,GAAG,EAAE;UAAE,CAAE;UAAA3C,QAAA,gBACzD9P,OAAA,CAAChE,UAAU;YAACoU,OAAO,EAAC,IAAI;YAAAN,QAAA,EAAC;UAAgB;YAAAhC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAY,CAAC,eACtDjO,OAAA,CAAChD,WAAW;YAACwV,IAAI,EAAC,OAAO;YAAC9C,EAAE,EAAE;cAAEgD,QAAQ,EAAE;YAAI,CAAE;YAAA5C,QAAA,gBAC9C9P,OAAA,CAAC/C,UAAU;cAAA6S,QAAA,EAAC;YAAyB;cAAAhC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAY,CAAC,eAClDjO,OAAA,CAAC9C,MAAM;cACLwJ,KAAK,EAAEvC,oBAAoB,IAAI,UAAW;cAC1CwO,QAAQ,EAAErM,qBAAsB;cAChCqH,KAAK,EAAC,2BAA2B;cAAAmC,QAAA,gBAEjC9P,OAAA,CAAC7C,QAAQ;gBAACuJ,KAAK,EAAC,UAAU;gBAAAoJ,QAAA,GAAC,kCACH,EAACzL,iBAAiB,IAAI,IAAIA,iBAAiB,GAAG;cAAA;gBAAAyJ,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC5D,CAAC,EACVhK,oBAAoB,CAACyF,GAAG,CAAEkJ,GAAG,iBAC5B5S,OAAA,CAAC7C,QAAQ;gBAAqBuJ,KAAK,EAAEkM,GAAG,CAACC,SAAU;gBAAA/C,QAAA,GAAC,eAC/C,EAAC8C,GAAG,CAACC,SAAS,EAAC,IAAE,EAACD,GAAG,CAACE,UAAU,EAAC,QACpC,EAACF,GAAG,CAACC,SAAS,KAAKxO,iBAAiB,IAAI,YAAY;cAAA,GAFvCuO,GAAG,CAACC,SAAS;gBAAA/E,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAGlB,CACX,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACI,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACE,CAAC,eACdjO,OAAA,CAACxD,IAAI;YACHmR,KAAK,EACHxJ,oBAAoB,GAChB,YAAYA,oBAAoB,EAAE,GAClC,aAAaE,iBAAiB,IAAI,KAAK,EAC5C;YACD8J,KAAK,EAAEhK,oBAAoB,GAAG,WAAW,GAAG,SAAU;YACtDiM,OAAO,EAAC;UAAU;YAAAtC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACnB,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACC;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACD,CACR,EAGAwB,eAAe,CAAC,CAAC,eAGlBzP,OAAA,CAACjE,GAAG;QAAC2T,EAAE,EAAE;UAAE6C,EAAE,EAAE;QAAE,CAAE;QAAAzC,QAAA,GAEhB1N,gBAAgB,IAAIyK,qBAAqB,CAAC,CAAC,GAAG,CAAC,iBAC9C7M,OAAA,CAACjE,GAAG;UAAC2T,EAAE,EAAE;YAAEE,EAAE,EAAE;UAAE,CAAE;UAAAE,QAAA,eACjB9P,OAAA,CAACxD,IAAI;YACHmR,KAAK,EAAE,GAAGd,qBAAqB,CAAC,CAAC,mBAAoB;YACrDsB,KAAK,EAAC,SAAS;YACfiC,OAAO,EAAC;UAAU;YAAAtC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACnB;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACC,CACN,EAGA8E,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,aAAa,IAAI9R,UAAU,CAACwD,MAAM,GAAG,CAAC,iBAC9D3E,OAAA,CAACjE,GAAG;UAAC2T,EAAE,EAAE;YAAEE,EAAE,EAAE,CAAC;YAAED,CAAC,EAAE,CAAC;YAAEE,OAAO,EAAE,SAAS;YAAEY,YAAY,EAAE,CAAC;YAAE5C,QAAQ,EAAE,QAAQ;YAAEqF,UAAU,EAAE,WAAW;YAAExC,OAAO,EAAE;UAAO,CAAE;UAAAZ,QAAA,EACzHqD,MAAM,CAACC,IAAI,CAACjS,UAAU,CAAC,CAAC,CAAC,CAAC,CAACuI,GAAG,CAACb,GAAG,iBACjC7I,OAAA;YAAA8P,QAAA,GAAgBjH,GAAG,EAAC,IAAE,EAACmB,IAAI,CAACqJ,SAAS,CAAClS,UAAU,CAAC,CAAC,CAAC,CAAC0H,GAAG,CAAC,CAAC;UAAA,GAA/CA,GAAG;YAAAiF,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAkD,CAChE;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACC,CACN,eAEDjO,OAAA,CAACJ,mBAAmB;UAClBqP,IAAI,EAAE9N,UAAW;UACjBI,OAAO,EAAEA,OAAQ;UACjB+R,oBAAoB,EAAGC,YAAY,IAAK3O,OAAO,CAACC,GAAG,CAAC,uBAAuB,EAAE0O,YAAY,CAAC5O,MAAM,CAAE;UAClGN,iBAAiB,EAAE,EAAAlE,YAAA,GAAAgB,UAAU,CAAC,CAAC,CAAC,cAAAhB,YAAA,uBAAbA,YAAA,CAAeqT,mBAAmB,OAAApT,aAAA,GAAIe,UAAU,CAAC,CAAC,CAAC,cAAAf,aAAA,uBAAbA,aAAA,CAAeyS,SAAS,OAAAxS,aAAA,GAAIc,UAAU,CAAC,CAAC,CAAC,cAAAd,aAAA,uBAAbA,aAAA,CAAeuS,GAAG,CAAC;UACxGxQ,gBAAgB,EAAEA,gBAAiB;UACnCqR,YAAY,EAAEnR,kBAAmB;UACjCoR,iBAAiB,EAAEpH,+BAAgC;UACnDqH,iBAAiB,EAAEtH,qBAAsB;UACzCuH,gBAAgB,EAAEnG,mBAAoB;UACtCoG,mBAAmB,EAAE/G,uBAAwB;UAC7CgH,cAAc,EAAExF;QAAmB;UAAAR,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACpC,CAAC,EACD9M,UAAU,CAACwD,MAAM,KAAK,CAAC,IAAI,CAACpD,OAAO,iBAClCvB,OAAA,CAAC1D,KAAK;UAACyF,QAAQ,EAAC,MAAM;UAAC2N,EAAE,EAAE;YAAE6C,EAAE,EAAE;UAAE,CAAE;UAAAzC,QAAA,EAAC;QAEtC;UAAAhC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAO,CACR;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACE,CAAC,eAGNjO,OAAA,CAACjE,GAAG;QAAC2T,EAAE,EAAE;UAAE6C,EAAE,EAAE;QAAE,CAAE;QAAAzC,QAAA,gBACjB9P,OAAA,CAACjE,GAAG;UAAC2T,EAAE,EAAE;YAAEgB,OAAO,EAAE,MAAM;YAAER,cAAc,EAAE,eAAe;YAAED,UAAU,EAAE,QAAQ;YAAEL,EAAE,EAAE;UAAE,CAAE;UAAAE,QAAA,eACzF9P,OAAA,CAAChE,UAAU;YAACoU,OAAO,EAAC,IAAI;YAAAN,QAAA,GAAC,aACZ,EAACzO,SAAS,CAACsD,MAAM,GAAG,CAAC,GAAG,IAAItD,SAAS,CAACsD,MAAM,GAAG,GAAG,EAAE;UAAA;YAAAmJ,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACrD;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACV,CAAC,eACNjO,OAAA,CAACJ,mBAAmB;UAClBqP,IAAI,EAAE5N,SAAU;UAChBE,OAAO,EAAEA,OAAQ;UACjB+R,oBAAoB,EAAGC,YAAY,IAAK3O,OAAO,CAACC,GAAG,CAAC,sBAAsB,EAAE0O,YAAY,CAAC5O,MAAM,CAAE;UACjGvC,gBAAgB,EAAEA,gBAAiB;UACnCqR,YAAY,EAAEjR,iBAAkB;UAChCkR,iBAAiB,EAAElH,8BAA+B;UAClDmH,iBAAiB,EAAEtH,qBAAsB;UACzCuH,gBAAgB,EAAEnG,mBAAoB;UACtCoG,mBAAmB,EAAE/G,uBAAwB;UAC7CgH,cAAc,EAAExF;QAAmB;UAAAR,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACpC,CAAC,EACD5M,SAAS,CAACsD,MAAM,KAAK,CAAC,IAAI,CAACpD,OAAO,iBACjCvB,OAAA,CAAC1D,KAAK;UAACyF,QAAQ,EAAC,MAAM;UAAC2N,EAAE,EAAE;YAAE6C,EAAE,EAAE;UAAE,CAAE;UAAAzC,QAAA,EAAC;QAEtC;UAAAhC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAO,CACR;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACE,CAAC,EAKL0C,mBAAmB,CAAC,CAAC,eAGtB3Q,OAAA,CAACrD,MAAM;QACLkF,IAAI,EAAErB,qBAAsB;QAC5BoQ,OAAO,EAAEA,CAAA,KAAMnQ,wBAAwB,CAAC,KAAK,CAAE;QAC/CqQ,SAAS;QACTD,QAAQ,EAAC,IAAI;QAAAf,QAAA,eAEb9P,OAAA,CAACT,oBAAoB;UACnBwB,UAAU,EAAEA,UAAW;UACvBgT,SAAS,EAAGjS,OAAO,IAAK;YACtB;YACArB,wBAAwB,CAAC,KAAK,CAAC;;YAE/B;YACA,IAAIqB,OAAO,EAAE;cACX;cACA8C,OAAO,CAACC,GAAG,CAAC,wBAAwB,EAAE/C,OAAO,CAAC;cAC9C;cACAsK,gBAAgB,CAACtK,OAAO,EAAE,SAAS,CAAC;cACpC;cACAkG,UAAU,CAAC,MAAM;gBACfpD,OAAO,CAACC,GAAG,CAAC,uCAAuC,CAAC;gBACpD,IAAI;kBACF;kBACAsC,SAAS,CAAC,IAAI,CAAC;gBACjB,CAAC,CAAC,OAAO1F,KAAK,EAAE;kBACdmD,OAAO,CAACnD,KAAK,CAAC,2CAA2C,EAAEA,KAAK,CAAC;kBACjE;kBACA4G,MAAM,CAACC,QAAQ,CAACC,MAAM,CAAC,CAAC;gBAC1B;cACF,CAAC,EAAE,IAAI,CAAC;YACV,CAAC,MAAM;cACL;cACA3D,OAAO,CAACC,GAAG,CAAC,mCAAmC,CAAC;YAClD;UACF,CAAE;UACFmP,OAAO,EAAGlS,OAAO,IAAK;YACpB;YACA8C,OAAO,CAACnD,KAAK,CAAC,0CAA0C,EAAEK,OAAO,CAAC;YAClE;YACAmS,KAAK,CAAC,WAAWnS,OAAO,EAAE,CAAC;YAC3B;YACArB,wBAAwB,CAAC,KAAK,CAAC;YAC/B;YACA0G,SAAS,CAAC,CAAC;UACb,CAAE;UACF+M,aAAa,EAAC;QAAa;UAAApG,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC5B;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACI,CAAC,EAIRvN,sBAAsB,IAAIkE,OAAO,CAACC,GAAG,CAAC,4DAA4D,EAAE9D,UAAU,CAAC,eAEhHf,OAAA,CAACrD,MAAM;QACLkF,IAAI,EAAEnB,sBAAuB;QAC7BkQ,OAAO,EAAEA,CAAA,KAAMjQ,yBAAyB,CAAC,KAAK,CAAE;QAChDmQ,SAAS;QACTD,QAAQ,EAAC,IAAI;QAAAf,QAAA,eAEb9P,OAAA,CAACT,oBAAoB;UACnBwB,UAAU,EAAEA,UAAW;UACvBgT,SAAS,EAAGjS,OAAO,IAAK;YACtB;YACAnB,yBAAyB,CAAC,KAAK,CAAC;;YAEhC;YACA,IAAImB,OAAO,EAAE;cACX;cACA8C,OAAO,CAACC,GAAG,CAAC,wBAAwB,EAAE/C,OAAO,CAAC;cAC9C;cACAsK,gBAAgB,CAACtK,OAAO,EAAE,SAAS,CAAC;cACpC;cACA8C,OAAO,CAACC,GAAG,CAAC,uCAAuC,CAAC;cACpD;cACAmD,UAAU,CAAC,MAAM;gBACf,IAAI;kBACFb,SAAS,CAAC,IAAI,CAAC;gBACjB,CAAC,CAAC,OAAO1F,KAAK,EAAE;kBACdmD,OAAO,CAACnD,KAAK,CAAC,2CAA2C,EAAEA,KAAK,CAAC;kBACjE;kBACA4G,MAAM,CAACC,QAAQ,CAACC,MAAM,CAAC,CAAC;gBAC1B;cACF,CAAC,EAAE,IAAI,CAAC;YACV,CAAC,MAAM;cACL;cACA3D,OAAO,CAACC,GAAG,CAAC,mCAAmC,CAAC;YAClD;UACF,CAAE;UACFmP,OAAO,EAAGlS,OAAO,IAAK;YACpB;YACA8C,OAAO,CAACnD,KAAK,CAAC,sCAAsC,EAAEK,OAAO,CAAC;YAC9D;YACAmS,KAAK,CAAC,WAAWnS,OAAO,EAAE,CAAC;YAC3B;YACAnB,yBAAyB,CAAC,KAAK,CAAC;YAChC;YACAiE,OAAO,CAACC,GAAG,CAAC,mCAAmC,CAAC;YAChDsC,SAAS,CAAC,IAAI,CAAC;UACjB,CAAE;UACF+M,aAAa,EAAC;QAAc;UAAApG,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC7B;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACI,CAAC,eAGTjO,OAAA,CAACrD,MAAM;QAACkF,IAAI,EAAEjB,sBAAuB;QAACgQ,OAAO,EAAEA,CAAA,KAAM/P,yBAAyB,CAAC,KAAK,CAAE;QAACgQ,QAAQ,EAAC,IAAI;QAACC,SAAS;QAAAhB,QAAA,eAC5G9P,OAAA,CAACT,oBAAoB;UACnBwB,UAAU,EAAEA,UAAW;UACvBgT,SAAS,EAAGjS,OAAO,IAAK;YACtB;YACAjB,yBAAyB,CAAC,KAAK,CAAC;;YAEhC;YACA,IAAIiB,OAAO,EAAE;cACX;cACA8C,OAAO,CAACC,GAAG,CAAC,wBAAwB,EAAE/C,OAAO,CAAC;cAC9C;cACAsK,gBAAgB,CAACtK,OAAO,EAAE,SAAS,CAAC;cACpC;cACAkG,UAAU,CAAC,MAAM;gBACfpD,OAAO,CAACC,GAAG,CAAC,uCAAuC,CAAC;gBACpD,IAAI;kBACF;kBACAsC,SAAS,CAAC,IAAI,CAAC;gBACjB,CAAC,CAAC,OAAO1F,KAAK,EAAE;kBACdmD,OAAO,CAACnD,KAAK,CAAC,2CAA2C,EAAEA,KAAK,CAAC;kBACjE;kBACAmD,OAAO,CAACC,GAAG,CAAC,4CAA4C,CAAC;kBACzDwD,MAAM,CAACC,QAAQ,CAACC,MAAM,CAAC,CAAC;gBAC1B;cACF,CAAC,EAAE,IAAI,CAAC;YACV,CAAC,MAAM;cACL;cACA3D,OAAO,CAACC,GAAG,CAAC,mCAAmC,CAAC;YAClD;UACF,CAAE;UACFmP,OAAO,EAAGlS,OAAO,IAAK;YACpB;YACA8C,OAAO,CAACnD,KAAK,CAAC,sCAAsC,EAAEK,OAAO,CAAC;YAC9D;YACAsK,gBAAgB,CAAC,WAAWtK,OAAO,EAAE,EAAE,OAAO,CAAC;YAC/C;YACAjB,yBAAyB,CAAC,KAAK,CAAC;YAChC;YACAsG,SAAS,CAAC,IAAI,CAAC;UACjB,CAAE;UACF+M,aAAa,EAAC;QAAc;UAAApG,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC7B;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACI,CAAC,eAGTjO,OAAA,CAACH,oBAAoB;QACnBgC,IAAI,EAAEa,oBAAoB,CAACb,IAAK;QAChC+O,OAAO,EAAErB,yBAA0B;QACnC3M,IAAI,EAAEF,oBAAoB,CAACE,IAAK;QAChCuR,MAAM,EAAExF,eAAgB;QACxBpN,OAAO,EAAEmB,oBAAoB,CAACnB;MAAQ;QAAAuM,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACvC,CAAC,eAEFjO,OAAA,CAACF,oBAAoB;QACnB+B,IAAI,EAAEgB,oBAAoB,CAAChB,IAAK;QAChC+O,OAAO,EAAEpB,yBAA0B;QACnC5M,IAAI,EAAEC,oBAAoB,CAACD,IAAK;QAChCG,iBAAiB,EAAEA,iBAAkB;QACrCoR,MAAM,EAAEjF,kBAAmB;QAC3B3N,OAAO,EAAEsB,oBAAoB,CAACtB;MAAQ;QAAAuM,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACvC,CAAC,eAGFjO,OAAA,CAACjD,QAAQ;QACP8E,IAAI,EAAEF,YAAY,CAACE,IAAK;QACxBuS,gBAAgB,EAAE,IAAK;QACvBxD,OAAO,EAAE1E,uBAAwB;QACjCmI,YAAY,EAAE;UAAEC,QAAQ,EAAE,QAAQ;UAAEC,UAAU,EAAE;QAAS,CAAE;QAAAzE,QAAA,eAE3D9P,OAAA,CAAC1D,KAAK;UAACsU,OAAO,EAAE1E,uBAAwB;UAACnK,QAAQ,EAAEJ,YAAY,CAACI,QAAS;UAAC2N,EAAE,EAAE;YAAEa,KAAK,EAAE;UAAO,CAAE;UAAAT,QAAA,EAC7FnO,YAAY,CAACG;QAAO;UAAAgM,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAChB;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACA,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACR;EACN;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACE,CAAC;AAEV,CAAC;AAAC/N,EAAA,CAr1CID,kBAAkB;EAAA,QACYZ,OAAO,EACyHC,gBAAgB,EACjKF,WAAW;AAAA;AAAAoV,EAAA,GAHxBvU,kBAAkB;AAu1CxB,eAAeA,kBAAkB;AAAC,IAAAuU,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}