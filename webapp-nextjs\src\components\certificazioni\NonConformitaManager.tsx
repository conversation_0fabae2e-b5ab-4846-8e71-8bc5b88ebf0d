'use client'

import { useState } from 'react'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Badge } from '@/components/ui/badge'
import { Input } from '@/components/ui/input'
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '@/components/ui/table'
import { Alert, AlertDescription } from '@/components/ui/alert'
import { 
  AlertTriangle, 
  Search, 
  Plus, 
  Edit, 
  Trash2, 
  AlertCircle,
  CheckCircle,
  Clock,
  Loader2,
  Eye,
  FileText
} from 'lucide-react'
import { NonConformita } from '@/types/certificazioni'
import { nonConformitaApi } from '@/lib/api'
import NonConformitaForm from './NonConformitaForm'

interface NonConformitaManagerProps {
  cantiereId: number
  nonConformita: NonConformita[]
  onUpdate: () => void
}

export default function NonConformitaManager({ cantiereId, nonConformita, onUpdate }: NonConformitaManagerProps) {
  const [searchTerm, setSearchTerm] = useState('')
  const [selectedStatus, setSelectedStatus] = useState('all')
  const [isLoading, setIsLoading] = useState(false)
  const [error, setError] = useState('')
  const [showForm, setShowForm] = useState(false)
  const [editingNonConformita, setEditingNonConformita] = useState<NonConformita | null>(null)

  const handleCreateNonConformita = () => {
    setEditingNonConformita(null)
    setShowForm(true)
  }

  const handleEditNonConformita = (nc: NonConformita) => {
    setEditingNonConformita(nc)
    setShowForm(true)
  }

  const handleFormSuccess = () => {
    setShowForm(false)
    setEditingNonConformita(null)
    onUpdate()
  }

  const handleFormCancel = () => {
    setShowForm(false)
    setEditingNonConformita(null)
  }

  const handleDeleteNonConformita = async (id: number) => {
    if (!confirm('Sei sicuro di voler eliminare questa non conformità?')) return
    
    try {
      setIsLoading(true)
      await nonConformitaApi.deleteNonConformita(cantiereId, id)
      onUpdate()
    } catch (error: any) {
      setError(error.response?.data?.detail || 'Errore durante l\'eliminazione')
    } finally {
      setIsLoading(false)
    }
  }

  const getStatoBadge = (stato: string) => {
    switch (stato?.toLowerCase()) {
      case 'aperta':
        return <Badge className="bg-red-100 text-red-800 border-red-200">Aperta</Badge>
      case 'in_risoluzione':
        return <Badge className="bg-yellow-100 text-yellow-800 border-yellow-200">In Risoluzione</Badge>
      case 'risolta':
        return <Badge className="bg-green-100 text-green-800 border-green-200">Risolta</Badge>
      case 'chiusa':
        return <Badge className="bg-gray-100 text-gray-800 border-gray-200">Chiusa</Badge>
      default:
        return <Badge className="bg-gray-100 text-gray-800 border-gray-200">Da Verificare</Badge>
    }
  }

  const getSeveritaBadge = (severita: string) => {
    switch (severita?.toLowerCase()) {
      case 'critica':
        return <Badge className="bg-red-100 text-red-800 border-red-200">Critica</Badge>
      case 'alta':
        return <Badge className="bg-orange-100 text-orange-800 border-orange-200">Alta</Badge>
      case 'media':
        return <Badge className="bg-yellow-100 text-yellow-800 border-yellow-200">Media</Badge>
      case 'bassa':
        return <Badge className="bg-blue-100 text-blue-800 border-blue-200">Bassa</Badge>
      default:
        return <Badge variant="outline">Non Specificata</Badge>
    }
  }

  const getStatoIcon = (stato: string) => {
    switch (stato?.toLowerCase()) {
      case 'aperta':
        return <AlertCircle className="h-4 w-4 text-red-500" />
      case 'in_risoluzione':
        return <Clock className="h-4 w-4 text-yellow-500" />
      case 'risolta':
      case 'chiusa':
        return <CheckCircle className="h-4 w-4 text-green-500" />
      default:
        return <AlertTriangle className="h-4 w-4 text-gray-500" />
    }
  }

  const filteredNonConformita = nonConformita.filter(nc => {
    const searchLower = searchTerm.toLowerCase()
    const matchesSearch = (
      nc.id_cavo?.toLowerCase().includes(searchLower) ||
      nc.descrizione?.toLowerCase().includes(searchLower) ||
      nc.tipo_non_conformita?.toLowerCase().includes(searchLower) ||
      nc.responsabile_rilevazione?.toLowerCase().includes(searchLower)
    )
    
    let matchesStatus = true
    if (selectedStatus !== 'all') {
      matchesStatus = nc.stato?.toLowerCase() === selectedStatus
    }
    
    return matchesSearch && matchesStatus
  })

  const stats = {
    totali: nonConformita.length,
    aperte: nonConformita.filter(nc => nc.stato === 'APERTA').length,
    in_risoluzione: nonConformita.filter(nc => nc.stato === 'IN_RISOLUZIONE').length,
    risolte: nonConformita.filter(nc => nc.stato === 'RISOLTA' || nc.stato === 'CHIUSA').length,
    critiche: nonConformita.filter(nc => nc.severita === 'CRITICA').length
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h2 className="text-2xl font-bold text-slate-900 flex items-center gap-3">
            <AlertTriangle className="h-6 w-6 text-red-600" />
            Gestione Non Conformità
          </h2>
          <p className="text-slate-600 mt-1">Tracciamento e risoluzione delle non conformità</p>
        </div>
        
        <Button onClick={handleCreateNonConformita}>
          <Plus className="h-4 w-4 mr-2" />
          Nuova Non Conformità
        </Button>
      </div>

      {/* Statistics */}
      <div className="grid grid-cols-1 md:grid-cols-5 gap-4">
        <Card>
          <CardContent className="p-4">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm text-slate-600">Totali</p>
                <p className="text-2xl font-bold text-slate-900">{stats.totali}</p>
              </div>
              <FileText className="h-8 w-8 text-blue-500" />
            </div>
          </CardContent>
        </Card>
        
        <Card>
          <CardContent className="p-4">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm text-slate-600">Aperte</p>
                <p className="text-2xl font-bold text-red-600">{stats.aperte}</p>
              </div>
              <AlertCircle className="h-8 w-8 text-red-500" />
            </div>
          </CardContent>
        </Card>
        
        <Card>
          <CardContent className="p-4">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm text-slate-600">In Risoluzione</p>
                <p className="text-2xl font-bold text-yellow-600">{stats.in_risoluzione}</p>
              </div>
              <Clock className="h-8 w-8 text-yellow-500" />
            </div>
          </CardContent>
        </Card>
        
        <Card>
          <CardContent className="p-4">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm text-slate-600">Risolte</p>
                <p className="text-2xl font-bold text-green-600">{stats.risolte}</p>
              </div>
              <CheckCircle className="h-8 w-8 text-green-500" />
            </div>
          </CardContent>
        </Card>
        
        <Card>
          <CardContent className="p-4">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm text-slate-600">Critiche</p>
                <p className="text-2xl font-bold text-red-600">{stats.critiche}</p>
              </div>
              <AlertTriangle className="h-8 w-8 text-red-500" />
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Search and Filters */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Search className="h-5 w-5" />
            Ricerca e Filtri
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="flex gap-4">
            <div className="flex-1">
              <Input
                placeholder="Cerca per ID cavo, descrizione, tipo o responsabile..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
              />
            </div>
            <div className="flex gap-2">
              {[
                { value: 'all', label: 'Tutte' },
                { value: 'aperta', label: 'Aperte' },
                { value: 'in_risoluzione', label: 'In Risoluzione' },
                { value: 'risolta', label: 'Risolte' },
                { value: 'chiusa', label: 'Chiuse' }
              ].map((status) => (
                <Button
                  key={status.value}
                  variant={selectedStatus === status.value ? 'default' : 'outline'}
                  size="sm"
                  onClick={() => setSelectedStatus(status.value)}
                >
                  {status.label}
                </Button>
              ))}
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Non Conformità Table */}
      <Card>
        <CardHeader>
          <CardTitle>Elenco Non Conformità ({filteredNonConformita.length})</CardTitle>
          <CardDescription>
            Gestione delle non conformità rilevate durante le certificazioni
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="rounded-md border">
            <Table>
              <TableHeader>
                <TableRow>
                  <TableHead>ID Cavo</TableHead>
                  <TableHead>Tipo</TableHead>
                  <TableHead>Descrizione</TableHead>
                  <TableHead>Severità</TableHead>
                  <TableHead>Data Rilevazione</TableHead>
                  <TableHead>Responsabile</TableHead>
                  <TableHead>Stato</TableHead>
                  <TableHead>Azioni</TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                {isLoading ? (
                  <TableRow>
                    <TableCell colSpan={8} className="text-center py-8">
                      <div className="flex items-center justify-center gap-2">
                        <Loader2 className="h-4 w-4 animate-spin" />
                        Caricamento...
                      </div>
                    </TableCell>
                  </TableRow>
                ) : filteredNonConformita.length === 0 ? (
                  <TableRow>
                    <TableCell colSpan={8} className="text-center py-8 text-slate-500">
                      Nessuna non conformità trovata
                    </TableCell>
                  </TableRow>
                ) : (
                  filteredNonConformita.map((nc) => (
                    <TableRow key={nc.id_non_conformita}>
                      <TableCell className="font-medium">
                        <div className="flex items-center gap-2">
                          {getStatoIcon(nc.stato)}
                          {nc.id_cavo}
                        </div>
                      </TableCell>
                      <TableCell>
                        <Badge variant="outline">
                          {nc.tipo_non_conformita || 'Non Specificato'}
                        </Badge>
                      </TableCell>
                      <TableCell>
                        <div className="max-w-xs">
                          <div className="font-medium truncate" title={nc.descrizione}>
                            {nc.descrizione}
                          </div>
                          {nc.azione_correttiva && (
                            <div className="text-xs text-slate-500 truncate" title={nc.azione_correttiva}>
                              Azione: {nc.azione_correttiva}
                            </div>
                          )}
                        </div>
                      </TableCell>
                      <TableCell>{getSeveritaBadge(nc.severita)}</TableCell>
                      <TableCell>
                        {new Date(nc.data_rilevazione).toLocaleDateString('it-IT')}
                      </TableCell>
                      <TableCell>{nc.responsabile_rilevazione || '-'}</TableCell>
                      <TableCell>{getStatoBadge(nc.stato)}</TableCell>
                      <TableCell>
                        <div className="flex gap-1">
                          <Button 
                            variant="ghost" 
                            size="sm"
                            onClick={() => {}}
                            title="Visualizza Dettagli"
                          >
                            <Eye className="h-4 w-4" />
                          </Button>
                          <Button 
                            variant="ghost" 
                            size="sm"
                            onClick={() => handleEditNonConformita(nc)}
                            title="Modifica"
                          >
                            <Edit className="h-4 w-4" />
                          </Button>
                          <Button 
                            variant="ghost" 
                            size="sm"
                            onClick={() => handleDeleteNonConformita(nc.id_non_conformita)}
                            title="Elimina"
                          >
                            <Trash2 className="h-4 w-4" />
                          </Button>
                        </div>
                      </TableCell>
                    </TableRow>
                  ))
                )}
              </TableBody>
            </Table>
          </div>
        </CardContent>
      </Card>

      {error && (
        <Alert className="border-red-200 bg-red-50">
          <AlertCircle className="h-4 w-4 text-red-600" />
          <AlertDescription className="text-red-800">{error}</AlertDescription>
        </Alert>
      )}

      {/* Form Modal */}
      {showForm && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center p-4 z-50">
          <div className="bg-white rounded-lg max-w-4xl w-full max-h-[90vh] overflow-y-auto">
            <div className="p-6">
              <NonConformitaForm
                cantiereId={cantiereId}
                nonConformita={editingNonConformita}
                onSuccess={handleFormSuccess}
                onCancel={handleFormCancel}
              />
            </div>
          </div>
        </div>
      )}
    </div>
  )
}
