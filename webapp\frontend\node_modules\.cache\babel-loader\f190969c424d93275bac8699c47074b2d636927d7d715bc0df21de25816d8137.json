{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\PycharmProjects\\\\CMS\\\\webapp\\\\frontend\\\\src\\\\components\\\\cavi\\\\QuickAddCablesDialog.js\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect } from 'react';\nimport { Dialog, DialogTitle, DialogContent, DialogActions, Button, Typography, Box, TextField, Checkbox, FormControlLabel, Table, TableBody, TableCell, TableContainer, TableHead, TableRow, Paper, CircularProgress, Alert, IconButton, Chip, Tooltip } from '@mui/material';\nimport { Add as AddIcon, Delete as DeleteIcon, Save as SaveIcon, Info as InfoIcon, Warning as WarningIcon } from '@mui/icons-material';\nimport caviService from '../../services/caviService';\nimport { determineCableState, getCableStateColor, isCableInstalled } from '../../utils/stateUtils';\n\n/**\n * Componente per aggiungere rapidamente più cavi a una bobina\n * \n * @param {Object} props - Proprietà del componente\n * @param {boolean} props.open - Indica se il dialog è aperto\n * @param {Function} props.onClose - Funzione chiamata alla chiusura del dialog\n * @param {Object} props.bobina - Bobina selezionata\n * @param {string} props.cantiereId - ID del cantiere\n * @param {Function} props.onSuccess - Funzione chiamata al successo dell'operazione\n * @param {Function} props.onError - Funzione chiamata in caso di errore\n */\nimport { jsxDEV as _jsxDEV, Fragment as _Fragment } from \"react/jsx-dev-runtime\";\nconst QuickAddCablesDialog = ({\n  open,\n  onClose,\n  bobina,\n  cantiereId,\n  onSuccess,\n  onError\n}) => {\n  _s();\n  var _bobina$metri_residui, _bobina$metri_residui2;\n  // Stati per la gestione dei dati\n  const [loading, setLoading] = useState(false);\n  const [caviLoading, setCaviLoading] = useState(false);\n  const [cavi, setCavi] = useState([]);\n  const [selectedCavi, setSelectedCavi] = useState([]);\n  const [caviMetri, setCaviMetri] = useState({});\n  const [searchTerm, setSearchTerm] = useState('');\n  const [errors, setErrors] = useState({});\n  const [warnings, setWarnings] = useState({});\n  const [saving, setSaving] = useState(false);\n\n  // Carica i cavi disponibili quando il dialog viene aperto\n  useEffect(() => {\n    if (open && bobina) {\n      loadCavi();\n    }\n  }, [open, bobina, cantiereId]);\n\n  // Funzione per caricare i cavi\n  const loadCavi = async () => {\n    try {\n      setCaviLoading(true);\n      const caviData = await caviService.getCavi(cantiereId);\n\n      // Filtra i cavi compatibili con la bobina e non ancora installati\n      const caviCompatibili = caviData.filter(cavo =>\n      // Verifica compatibilità con la bobina\n      cavo.tipologia === bobina.tipologia && String(cavo.n_conduttori) === String(bobina.n_conduttori) && String(cavo.sezione) === String(bobina.sezione) &&\n      // Verifica che il cavo non sia già installato\n      !isCableInstalled(cavo) &&\n      // Verifica che il cavo non sia SPARE\n      cavo.modificato_manualmente !== 3);\n      setCavi(caviCompatibili);\n    } catch (error) {\n      console.error('Errore nel caricamento dei cavi:', error);\n      onError('Errore nel caricamento dei cavi: ' + (error.message || 'Errore sconosciuto'));\n    } finally {\n      setCaviLoading(false);\n    }\n  };\n\n  // Gestisce la selezione/deselezione di un cavo\n  const handleCavoSelect = cavo => {\n    setSelectedCavi(prev => {\n      const isSelected = prev.some(c => c.id_cavo === cavo.id_cavo);\n      if (isSelected) {\n        // Rimuovi il cavo dalla selezione\n        const newSelected = prev.filter(c => c.id_cavo !== cavo.id_cavo);\n\n        // Rimuovi anche i metri associati\n        const newCaviMetri = {\n          ...caviMetri\n        };\n        delete newCaviMetri[cavo.id_cavo];\n        setCaviMetri(newCaviMetri);\n        return newSelected;\n      } else {\n        // Aggiungi il cavo alla selezione\n        return [...prev, cavo];\n      }\n    });\n  };\n\n  // Gestisce l'input dei metri posati per un cavo\n  const handleMetriChange = (cavoId, value) => {\n    // Aggiorna i metri per il cavo\n    setCaviMetri(prev => ({\n      ...prev,\n      [cavoId]: value\n    }));\n\n    // Valida il valore inserito\n    validateMetri(cavoId, value);\n  };\n\n  // Valida i metri inseriti per un cavo\n  const validateMetri = (cavoId, value) => {\n    const cavo = cavi.find(c => c.id_cavo === cavoId);\n    if (!cavo) return;\n\n    // Resetta gli errori e gli avvisi per questo cavo\n    setErrors(prev => {\n      const newErrors = {\n        ...prev\n      };\n      delete newErrors[cavoId];\n      return newErrors;\n    });\n    setWarnings(prev => {\n      const newWarnings = {\n        ...prev\n      };\n      delete newWarnings[cavoId];\n      return newWarnings;\n    });\n\n    // Controllo input vuoto\n    if (!value || value.trim() === '') {\n      setErrors(prev => ({\n        ...prev,\n        [cavoId]: 'Inserire un valore per i metri posati'\n      }));\n      return false;\n    }\n\n    // Controllo formato numerico\n    if (isNaN(parseFloat(value)) || parseFloat(value) <= 0) {\n      setErrors(prev => ({\n        ...prev,\n        [cavoId]: 'Inserire un valore numerico positivo'\n      }));\n      return false;\n    }\n    const metriPosati = parseFloat(value);\n\n    // Controllo metri teorici cavo\n    if (cavo.metri_teorici && metriPosati > parseFloat(cavo.metri_teorici)) {\n      setWarnings(prev => ({\n        ...prev,\n        [cavoId]: `I metri posati (${metriPosati}m) superano i metri teorici del cavo (${cavo.metri_teorici}m)`\n      }));\n    }\n\n    // Controllo metri residui bobina\n    const metriTotaliRichiesti = Object.entries(caviMetri).filter(([id, _]) => id !== cavoId) // Escludi il cavo corrente\n    .reduce((sum, [_, metri]) => sum + parseFloat(metri || 0), 0) + metriPosati;\n    if (metriTotaliRichiesti > bobina.metri_residui) {\n      setWarnings(prev => ({\n        ...prev,\n        [cavoId]: `I metri totali richiesti (${metriTotaliRichiesti}m) superano i metri residui della bobina (${bobina.metri_residui}m)`\n      }));\n    }\n    return true;\n  };\n\n  // Valida tutti i metri inseriti\n  const validateAllMetri = () => {\n    let isValid = true;\n    const newErrors = {};\n    const newWarnings = {};\n\n    // Verifica che ci siano cavi selezionati\n    if (selectedCavi.length === 0) {\n      onError('Seleziona almeno un cavo');\n      return false;\n    }\n\n    // Verifica che tutti i cavi selezionati abbiano metri inseriti\n    for (const cavo of selectedCavi) {\n      const metri = caviMetri[cavo.id_cavo];\n\n      // Controllo input vuoto\n      if (!metri || metri.trim() === '') {\n        newErrors[cavo.id_cavo] = 'Inserire un valore per i metri posati';\n        isValid = false;\n        continue;\n      }\n\n      // Controllo formato numerico\n      if (isNaN(parseFloat(metri)) || parseFloat(metri) <= 0) {\n        newErrors[cavo.id_cavo] = 'Inserire un valore numerico positivo';\n        isValid = false;\n        continue;\n      }\n      const metriPosati = parseFloat(metri);\n\n      // Controllo metri teorici cavo\n      if (cavo.metri_teorici && metriPosati > parseFloat(cavo.metri_teorici)) {\n        newWarnings[cavo.id_cavo] = `I metri posati (${metriPosati}m) superano i metri teorici del cavo (${cavo.metri_teorici}m)`;\n      }\n    }\n\n    // Verifica che i metri totali richiesti non superino i metri residui della bobina\n    const metriTotaliRichiesti = Object.values(caviMetri).reduce((sum, metri) => sum + parseFloat(metri || 0), 0);\n    if (metriTotaliRichiesti > bobina.metri_residui) {\n      // Questo è un avviso globale, non specifico per un cavo\n      if (!window.confirm(`ATTENZIONE: I metri totali richiesti (${metriTotaliRichiesti}m) superano i metri residui della bobina (${bobina.metri_residui}m).\\n\\nQuesto porterà la bobina in stato OVER.\\n\\nVuoi continuare?`)) {\n        isValid = false;\n      }\n    }\n    setErrors(newErrors);\n    setWarnings(newWarnings);\n    return isValid;\n  };\n\n  // Gestisce il salvataggio dei dati\n  const handleSave = async () => {\n    try {\n      // Validazione\n      if (!validateAllMetri()) {\n        return;\n      }\n      setSaving(true);\n\n      // Conferma finale\n      if (!window.confirm(`Confermi l'aggiornamento di ${selectedCavi.length} cavi con la bobina ${bobina.id_bobina}?`)) {\n        setSaving(false);\n        return;\n      }\n\n      // Aggiorna ogni cavo selezionato\n      const results = [];\n      let errors = [];\n      for (const cavo of selectedCavi) {\n        try {\n          const metriPosati = parseFloat(caviMetri[cavo.id_cavo]);\n\n          // Determina se è necessario forzare lo stato OVER della bobina\n          const metriGiàUtilizzati = results.reduce((sum, r) => sum + r.metriPosati, 0);\n          const forceOver = metriGiàUtilizzati + metriPosati > bobina.metri_residui;\n\n          // Aggiorna i metri posati del cavo\n          const result = await caviService.updateMetriPosati(cantiereId, cavo.id_cavo, metriPosati, bobina.id_bobina, forceOver);\n          results.push({\n            cavo: cavo.id_cavo,\n            metriPosati,\n            success: true\n          });\n        } catch (error) {\n          console.error(`Errore nell'aggiornamento del cavo ${cavo.id_cavo}:`, error);\n          errors.push({\n            cavo: cavo.id_cavo,\n            error: error.message || 'Errore sconosciuto'\n          });\n        }\n      }\n\n      // Gestione del risultato\n      if (errors.length === 0) {\n        // Tutti i cavi sono stati aggiornati con successo\n        onSuccess(`${results.length} cavi aggiornati con successo`);\n        onClose();\n      } else if (results.length > 0) {\n        // Alcuni cavi sono stati aggiornati, altri no\n        onSuccess(`${results.length} cavi aggiornati con successo, ${errors.length} falliti`);\n        onError(`Errori: ${errors.map(e => `${e.cavo}: ${e.error}`).join(', ')}`);\n        onClose();\n      } else {\n        // Nessun cavo è stato aggiornato\n        onError(`Nessun cavo aggiornato. Errori: ${errors.map(e => `${e.cavo}: ${e.error}`).join(', ')}`);\n      }\n    } catch (error) {\n      console.error('Errore durante il salvataggio:', error);\n      onError('Errore durante il salvataggio: ' + (error.message || 'Errore sconosciuto'));\n    } finally {\n      setSaving(false);\n    }\n  };\n\n  // Filtra i cavi in base al termine di ricerca\n  const filteredCavi = cavi.filter(cavo => cavo.id_cavo.toLowerCase().includes(searchTerm.toLowerCase()) || cavo.tipologia && cavo.tipologia.toLowerCase().includes(searchTerm.toLowerCase()) || cavo.ubicazione_partenza && cavo.ubicazione_partenza.toLowerCase().includes(searchTerm.toLowerCase()) || cavo.ubicazione_arrivo && cavo.ubicazione_arrivo.toLowerCase().includes(searchTerm.toLowerCase()));\n  return /*#__PURE__*/_jsxDEV(Dialog, {\n    open: open,\n    onClose: onClose,\n    maxWidth: \"lg\",\n    fullWidth: true,\n    children: [/*#__PURE__*/_jsxDEV(DialogTitle, {\n      children: [\"Aggiungi cavi alla bobina \", (bobina === null || bobina === void 0 ? void 0 : bobina.numero_bobina) || '']\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 323,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(DialogContent, {\n      children: !bobina ? /*#__PURE__*/_jsxDEV(Alert, {\n        severity: \"error\",\n        children: \"Nessuna bobina selezionata\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 328,\n        columnNumber: 11\n      }, this) : /*#__PURE__*/_jsxDEV(_Fragment, {\n        children: [/*#__PURE__*/_jsxDEV(Box, {\n          sx: {\n            mb: 3,\n            p: 2,\n            bgcolor: '#f5f5f5',\n            borderRadius: 1\n          },\n          children: [/*#__PURE__*/_jsxDEV(Typography, {\n            variant: \"subtitle1\",\n            gutterBottom: true,\n            children: \"Dettagli bobina\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 333,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(Box, {\n            sx: {\n              display: 'flex',\n              flexWrap: 'wrap',\n              gap: 3\n            },\n            children: [/*#__PURE__*/_jsxDEV(Box, {\n              children: [/*#__PURE__*/_jsxDEV(Typography, {\n                variant: \"body2\",\n                color: \"text.secondary\",\n                children: \"ID Bobina\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 338,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                variant: \"body1\",\n                children: bobina.id_bobina\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 339,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 337,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(Box, {\n              children: [/*#__PURE__*/_jsxDEV(Typography, {\n                variant: \"body2\",\n                color: \"text.secondary\",\n                children: \"Tipologia\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 342,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                variant: \"body1\",\n                children: bobina.tipologia || 'N/A'\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 343,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 341,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(Box, {\n              children: [/*#__PURE__*/_jsxDEV(Typography, {\n                variant: \"body2\",\n                color: \"text.secondary\",\n                children: \"Conduttori\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 346,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                variant: \"body1\",\n                children: [bobina.n_conduttori || 'N/A', \" x \", bobina.sezione || 'N/A']\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 347,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 345,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(Box, {\n              children: [/*#__PURE__*/_jsxDEV(Typography, {\n                variant: \"body2\",\n                color: \"text.secondary\",\n                children: \"Metri residui\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 350,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                variant: \"body1\",\n                children: [((_bobina$metri_residui = bobina.metri_residui) === null || _bobina$metri_residui === void 0 ? void 0 : _bobina$metri_residui.toFixed(1)) || '0', \" m\"]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 351,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 349,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(Box, {\n              children: [/*#__PURE__*/_jsxDEV(Typography, {\n                variant: \"body2\",\n                color: \"text.secondary\",\n                children: \"Stato\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 354,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(Chip, {\n                label: bobina.stato_bobina || 'N/D',\n                size: \"small\",\n                color: bobina.stato_bobina === 'Disponibile' ? 'success' : bobina.stato_bobina === 'In uso' ? 'primary' : bobina.stato_bobina === 'Over' ? 'error' : bobina.stato_bobina === 'Terminata' ? 'warning' : 'default'\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 355,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 353,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 336,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 332,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(TextField, {\n          fullWidth: true,\n          label: \"Cerca cavi\",\n          variant: \"outlined\",\n          value: searchTerm,\n          onChange: e => setSearchTerm(e.target.value),\n          placeholder: \"Cerca per ID, tipologia, ubicazione...\",\n          sx: {\n            mb: 2\n          }\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 370,\n          columnNumber: 13\n        }, this), caviLoading ? /*#__PURE__*/_jsxDEV(Box, {\n          sx: {\n            display: 'flex',\n            justifyContent: 'center',\n            my: 4\n          },\n          children: /*#__PURE__*/_jsxDEV(CircularProgress, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 383,\n            columnNumber: 17\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 382,\n          columnNumber: 15\n        }, this) : filteredCavi.length === 0 ? /*#__PURE__*/_jsxDEV(Alert, {\n          severity: \"info\",\n          children: \"Nessun cavo compatibile disponibile per questa bobina.\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 386,\n          columnNumber: 15\n        }, this) : /*#__PURE__*/_jsxDEV(TableContainer, {\n          component: Paper,\n          sx: {\n            mb: 3\n          },\n          children: /*#__PURE__*/_jsxDEV(Table, {\n            size: \"small\",\n            children: [/*#__PURE__*/_jsxDEV(TableHead, {\n              children: /*#__PURE__*/_jsxDEV(TableRow, {\n                sx: {\n                  bgcolor: '#f5f5f5'\n                },\n                children: [/*#__PURE__*/_jsxDEV(TableCell, {\n                  padding: \"checkbox\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 394,\n                  columnNumber: 23\n                }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n                  children: \"ID Cavo\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 395,\n                  columnNumber: 23\n                }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n                  children: \"Tipologia\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 396,\n                  columnNumber: 23\n                }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n                  children: \"Ubicazione\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 397,\n                  columnNumber: 23\n                }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n                  children: \"Metri Teorici\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 398,\n                  columnNumber: 23\n                }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n                  children: \"Metri Posati\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 399,\n                  columnNumber: 23\n                }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n                  children: \"Stato\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 400,\n                  columnNumber: 23\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 393,\n                columnNumber: 21\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 392,\n              columnNumber: 19\n            }, this), /*#__PURE__*/_jsxDEV(TableBody, {\n              children: filteredCavi.map(cavo => {\n                const isSelected = selectedCavi.some(c => c.id_cavo === cavo.id_cavo);\n                return /*#__PURE__*/_jsxDEV(TableRow, {\n                  hover: true,\n                  selected: isSelected,\n                  onClick: () => handleCavoSelect(cavo),\n                  sx: {\n                    cursor: 'pointer'\n                  },\n                  children: [/*#__PURE__*/_jsxDEV(TableCell, {\n                    padding: \"checkbox\",\n                    children: /*#__PURE__*/_jsxDEV(Checkbox, {\n                      checked: isSelected,\n                      onChange: e => {\n                        e.stopPropagation();\n                        handleCavoSelect(cavo);\n                      }\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 415,\n                      columnNumber: 29\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 414,\n                    columnNumber: 27\n                  }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n                    children: cavo.id_cavo\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 423,\n                    columnNumber: 27\n                  }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n                    children: cavo.tipologia || 'N/A'\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 424,\n                    columnNumber: 27\n                  }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n                    children: /*#__PURE__*/_jsxDEV(Tooltip, {\n                      title: `Da: ${cavo.ubicazione_partenza || 'N/A'} - A: ${cavo.ubicazione_arrivo || 'N/A'}`,\n                      children: /*#__PURE__*/_jsxDEV(Box, {\n                        sx: {\n                          maxWidth: 150,\n                          overflow: 'hidden',\n                          textOverflow: 'ellipsis',\n                          whiteSpace: 'nowrap'\n                        },\n                        children: [cavo.ubicazione_partenza || 'N/A', \" \\u2192 \", cavo.ubicazione_arrivo || 'N/A']\n                      }, void 0, true, {\n                        fileName: _jsxFileName,\n                        lineNumber: 427,\n                        columnNumber: 31\n                      }, this)\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 426,\n                      columnNumber: 29\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 425,\n                    columnNumber: 27\n                  }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n                    children: cavo.metri_teorici || 'N/A'\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 432,\n                    columnNumber: 27\n                  }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n                    children: isSelected ? /*#__PURE__*/_jsxDEV(TextField, {\n                      size: \"small\",\n                      type: \"number\",\n                      value: caviMetri[cavo.id_cavo] || '',\n                      onChange: e => {\n                        e.stopPropagation();\n                        handleMetriChange(cavo.id_cavo, e.target.value);\n                      },\n                      onClick: e => e.stopPropagation(),\n                      error: !!errors[cavo.id_cavo],\n                      helperText: errors[cavo.id_cavo] || warnings[cavo.id_cavo],\n                      FormHelperTextProps: {\n                        sx: {\n                          color: warnings[cavo.id_cavo] && !errors[cavo.id_cavo] ? 'warning.main' : 'error.main'\n                        }\n                      },\n                      InputProps: {\n                        endAdornment: warnings[cavo.id_cavo] && !errors[cavo.id_cavo] ? /*#__PURE__*/_jsxDEV(Tooltip, {\n                          title: warnings[cavo.id_cavo],\n                          children: /*#__PURE__*/_jsxDEV(WarningIcon, {\n                            color: \"warning\",\n                            fontSize: \"small\"\n                          }, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 452,\n                            columnNumber: 39\n                          }, this)\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 451,\n                          columnNumber: 37\n                        }, this) : null\n                      }\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 435,\n                      columnNumber: 31\n                    }, this) : 'N/A'\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 433,\n                    columnNumber: 27\n                  }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n                    children: /*#__PURE__*/_jsxDEV(Chip, {\n                      size: \"small\",\n                      label: cavo.stato_installazione || 'Da installare',\n                      color: getCableStateColor(cavo.stato_installazione)\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 462,\n                      columnNumber: 29\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 461,\n                    columnNumber: 27\n                  }, this)]\n                }, cavo.id_cavo, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 407,\n                  columnNumber: 25\n                }, this);\n              })\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 403,\n              columnNumber: 19\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 391,\n            columnNumber: 17\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 390,\n          columnNumber: 15\n        }, this), selectedCavi.length > 0 && /*#__PURE__*/_jsxDEV(Box, {\n          sx: {\n            mb: 2\n          },\n          children: [/*#__PURE__*/_jsxDEV(Typography, {\n            variant: \"subtitle1\",\n            gutterBottom: true,\n            children: [\"Riepilogo selezione (\", selectedCavi.length, \" cavi)\"]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 479,\n            columnNumber: 17\n          }, this), /*#__PURE__*/_jsxDEV(TableContainer, {\n            component: Paper,\n            children: /*#__PURE__*/_jsxDEV(Table, {\n              size: \"small\",\n              children: [/*#__PURE__*/_jsxDEV(TableHead, {\n                children: /*#__PURE__*/_jsxDEV(TableRow, {\n                  sx: {\n                    bgcolor: '#f5f5f5'\n                  },\n                  children: [/*#__PURE__*/_jsxDEV(TableCell, {\n                    children: \"ID Cavo\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 486,\n                    columnNumber: 25\n                  }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n                    children: \"Metri Posati\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 487,\n                    columnNumber: 25\n                  }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n                    children: \"Azioni\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 488,\n                    columnNumber: 25\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 485,\n                  columnNumber: 23\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 484,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(TableBody, {\n                children: [selectedCavi.map(cavo => /*#__PURE__*/_jsxDEV(TableRow, {\n                  children: [/*#__PURE__*/_jsxDEV(TableCell, {\n                    children: cavo.id_cavo\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 494,\n                    columnNumber: 27\n                  }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n                    children: /*#__PURE__*/_jsxDEV(TextField, {\n                      size: \"small\",\n                      type: \"number\",\n                      value: caviMetri[cavo.id_cavo] || '',\n                      onChange: e => handleMetriChange(cavo.id_cavo, e.target.value),\n                      error: !!errors[cavo.id_cavo],\n                      helperText: errors[cavo.id_cavo] || warnings[cavo.id_cavo],\n                      FormHelperTextProps: {\n                        sx: {\n                          color: warnings[cavo.id_cavo] && !errors[cavo.id_cavo] ? 'warning.main' : 'error.main'\n                        }\n                      }\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 496,\n                      columnNumber: 29\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 495,\n                    columnNumber: 27\n                  }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n                    children: /*#__PURE__*/_jsxDEV(IconButton, {\n                      size: \"small\",\n                      color: \"error\",\n                      onClick: () => handleCavoSelect(cavo),\n                      children: /*#__PURE__*/_jsxDEV(DeleteIcon, {\n                        fontSize: \"small\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 514,\n                        columnNumber: 31\n                      }, this)\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 509,\n                      columnNumber: 29\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 508,\n                    columnNumber: 27\n                  }, this)]\n                }, cavo.id_cavo, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 493,\n                  columnNumber: 25\n                }, this)), /*#__PURE__*/_jsxDEV(TableRow, {\n                  children: /*#__PURE__*/_jsxDEV(TableCell, {\n                    colSpan: 3,\n                    align: \"right\",\n                    children: [/*#__PURE__*/_jsxDEV(Typography, {\n                      variant: \"body2\",\n                      children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n                        children: \"Metri totali richiesti:\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 522,\n                        columnNumber: 29\n                      }, this), \" \", Object.values(caviMetri).reduce((sum, metri) => {\n                        const value = parseFloat(metri || 0);\n                        return isNaN(value) ? sum : sum + value;\n                      }, 0).toFixed(1), \" m\"]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 521,\n                      columnNumber: 27\n                    }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                      variant: \"body2\",\n                      children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n                        children: \"Metri residui bobina:\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 530,\n                        columnNumber: 29\n                      }, this), \" \", ((_bobina$metri_residui2 = bobina.metri_residui) === null || _bobina$metri_residui2 === void 0 ? void 0 : _bobina$metri_residui2.toFixed(1)) || '0', \" m\"]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 529,\n                      columnNumber: 27\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 520,\n                    columnNumber: 25\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 519,\n                  columnNumber: 23\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 491,\n                columnNumber: 21\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 483,\n              columnNumber: 19\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 482,\n            columnNumber: 17\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 478,\n          columnNumber: 15\n        }, this), Object.keys(warnings).length > 0 && /*#__PURE__*/_jsxDEV(Alert, {\n          severity: \"warning\",\n          sx: {\n            mb: 2\n          },\n          children: [/*#__PURE__*/_jsxDEV(Typography, {\n            variant: \"subtitle2\",\n            children: \"Attenzione:\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 543,\n            columnNumber: 17\n          }, this), /*#__PURE__*/_jsxDEV(\"ul\", {\n            children: Object.entries(warnings).map(([cavoId, warning]) => /*#__PURE__*/_jsxDEV(\"li\", {\n              children: [cavoId, \": \", warning]\n            }, cavoId, true, {\n              fileName: _jsxFileName,\n              lineNumber: 546,\n              columnNumber: 21\n            }, this))\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 544,\n            columnNumber: 17\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 542,\n          columnNumber: 15\n        }, this), /*#__PURE__*/_jsxDEV(Alert, {\n          severity: \"info\",\n          sx: {\n            mb: 2\n          },\n          children: \"Seleziona i cavi che vuoi associare a questa bobina e inserisci i metri posati per ciascuno. I metri posati verranno sottratti dai metri residui della bobina.\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 553,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 326,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(DialogActions, {\n      children: [/*#__PURE__*/_jsxDEV(Button, {\n        onClick: onClose,\n        disabled: saving,\n        children: \"Annulla\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 561,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Button, {\n        onClick: handleSave,\n        color: \"primary\",\n        variant: \"contained\",\n        disabled: saving || selectedCavi.length === 0 || Object.keys(errors).length > 0,\n        startIcon: saving ? /*#__PURE__*/_jsxDEV(CircularProgress, {\n          size: 20\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 569,\n          columnNumber: 31\n        }, this) : /*#__PURE__*/_jsxDEV(SaveIcon, {}, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 569,\n          columnNumber: 64\n        }, this),\n        children: \"Salva\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 564,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 560,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 322,\n    columnNumber: 5\n  }, this);\n};\n_s(QuickAddCablesDialog, \"TqGYVXx6TUXcDuKFj077066qZXQ=\");\n_c = QuickAddCablesDialog;\nexport default QuickAddCablesDialog;\nvar _c;\n$RefreshReg$(_c, \"QuickAddCablesDialog\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "Dialog", "DialogTitle", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "DialogActions", "<PERSON><PERSON>", "Typography", "Box", "TextField", "Checkbox", "FormControlLabel", "Table", "TableBody", "TableCell", "TableContainer", "TableHead", "TableRow", "Paper", "CircularProgress", "<PERSON><PERSON>", "IconButton", "Chip", "<PERSON><PERSON><PERSON>", "Add", "AddIcon", "Delete", "DeleteIcon", "Save", "SaveIcon", "Info", "InfoIcon", "Warning", "WarningIcon", "caviService", "determineCableState", "getCableStateColor", "isCableInstalled", "jsxDEV", "_jsxDEV", "Fragment", "_Fragment", "QuickAddCablesDialog", "open", "onClose", "bobina", "cantiereId", "onSuccess", "onError", "_s", "_bobina$metri_residui", "_bobina$metri_residui2", "loading", "setLoading", "caviLoading", "setCaviLoading", "cavi", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "setSelectedCavi", "caviMetri", "setCaviMetri", "searchTerm", "setSearchTerm", "errors", "setErrors", "warnings", "setWarnings", "saving", "setSaving", "loadCavi", "caviData", "get<PERSON><PERSON>", "caviCompatibili", "filter", "cavo", "tipologia", "String", "n_conduttori", "sezione", "modificato_manualmente", "error", "console", "message", "handleCavoSelect", "prev", "isSelected", "some", "c", "id_cavo", "newSelected", "newCaviMetri", "handleMetriChange", "cavoId", "value", "validate<PERSON>etri", "find", "newErrors", "newWarnings", "trim", "isNaN", "parseFloat", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "metri_te<PERSON>ci", "metriTotaliRichiesti", "Object", "entries", "id", "_", "reduce", "sum", "metri", "metri_residui", "validateAllMetri", "<PERSON><PERSON><PERSON><PERSON>", "length", "values", "window", "confirm", "handleSave", "id_bobina", "results", "metriGiàUtilizzati", "r", "forceOver", "result", "updateMetri<PERSON><PERSON><PERSON>", "push", "success", "map", "e", "join", "filteredCavi", "toLowerCase", "includes", "ubicazione_partenza", "ubicazione_arrivo", "max<PERSON><PERSON><PERSON>", "fullWidth", "children", "numero_bobina", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "severity", "sx", "mb", "p", "bgcolor", "borderRadius", "variant", "gutterBottom", "display", "flexWrap", "gap", "color", "toFixed", "label", "stato_bobina", "size", "onChange", "target", "placeholder", "justifyContent", "my", "component", "padding", "hover", "selected", "onClick", "cursor", "checked", "stopPropagation", "title", "overflow", "textOverflow", "whiteSpace", "type", "helperText", "FormHelperTextProps", "InputProps", "endAdornment", "fontSize", "stato_installazione", "colSpan", "align", "keys", "warning", "disabled", "startIcon", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/PycharmProjects/CMS/webapp/frontend/src/components/cavi/QuickAddCablesDialog.js"], "sourcesContent": ["import React, { useState, useEffect } from 'react';\nimport {\n  Dialog,\n  DialogTitle,\n  DialogContent,\n  DialogActions,\n  Button,\n  Typography,\n  Box,\n  TextField,\n  Checkbox,\n  FormControlLabel,\n  Table,\n  TableBody,\n  TableCell,\n  TableContainer,\n  TableHead,\n  TableRow,\n  Paper,\n  CircularProgress,\n  Alert,\n  IconButton,\n  Chip,\n  Tooltip\n} from '@mui/material';\nimport {\n  Add as AddIcon,\n  Delete as DeleteIcon,\n  Save as SaveIcon,\n  Info as InfoIcon,\n  Warning as WarningIcon\n} from '@mui/icons-material';\nimport caviService from '../../services/caviService';\nimport { determineCableState, getCableStateColor, isCableInstalled } from '../../utils/stateUtils';\n\n/**\n * Componente per aggiungere rapidamente più cavi a una bobina\n * \n * @param {Object} props - Proprietà del componente\n * @param {boolean} props.open - Indica se il dialog è aperto\n * @param {Function} props.onClose - Funzione chiamata alla chiusura del dialog\n * @param {Object} props.bobina - <PERSON><PERSON> selezionata\n * @param {string} props.cantiereId - ID del cantiere\n * @param {Function} props.onSuccess - Funzione chiamata al successo dell'operazione\n * @param {Function} props.onError - Funzione chiamata in caso di errore\n */\nconst QuickAddCablesDialog = ({ open, onClose, bobina, cantiereId, onSuccess, onError }) => {\n  // Stati per la gestione dei dati\n  const [loading, setLoading] = useState(false);\n  const [caviLoading, setCaviLoading] = useState(false);\n  const [cavi, setCavi] = useState([]);\n  const [selectedCavi, setSelectedCavi] = useState([]);\n  const [caviMetri, setCaviMetri] = useState({});\n  const [searchTerm, setSearchTerm] = useState('');\n  const [errors, setErrors] = useState({});\n  const [warnings, setWarnings] = useState({});\n  const [saving, setSaving] = useState(false);\n\n  // Carica i cavi disponibili quando il dialog viene aperto\n  useEffect(() => {\n    if (open && bobina) {\n      loadCavi();\n    }\n  }, [open, bobina, cantiereId]);\n\n  // Funzione per caricare i cavi\n  const loadCavi = async () => {\n    try {\n      setCaviLoading(true);\n      const caviData = await caviService.getCavi(cantiereId);\n      \n      // Filtra i cavi compatibili con la bobina e non ancora installati\n      const caviCompatibili = caviData.filter(cavo => \n        // Verifica compatibilità con la bobina\n        cavo.tipologia === bobina.tipologia &&\n        String(cavo.n_conduttori) === String(bobina.n_conduttori) &&\n        String(cavo.sezione) === String(bobina.sezione) &&\n        // Verifica che il cavo non sia già installato\n        !isCableInstalled(cavo) &&\n        // Verifica che il cavo non sia SPARE\n        cavo.modificato_manualmente !== 3\n      );\n      \n      setCavi(caviCompatibili);\n    } catch (error) {\n      console.error('Errore nel caricamento dei cavi:', error);\n      onError('Errore nel caricamento dei cavi: ' + (error.message || 'Errore sconosciuto'));\n    } finally {\n      setCaviLoading(false);\n    }\n  };\n\n  // Gestisce la selezione/deselezione di un cavo\n  const handleCavoSelect = (cavo) => {\n    setSelectedCavi(prev => {\n      const isSelected = prev.some(c => c.id_cavo === cavo.id_cavo);\n      \n      if (isSelected) {\n        // Rimuovi il cavo dalla selezione\n        const newSelected = prev.filter(c => c.id_cavo !== cavo.id_cavo);\n        \n        // Rimuovi anche i metri associati\n        const newCaviMetri = { ...caviMetri };\n        delete newCaviMetri[cavo.id_cavo];\n        setCaviMetri(newCaviMetri);\n        \n        return newSelected;\n      } else {\n        // Aggiungi il cavo alla selezione\n        return [...prev, cavo];\n      }\n    });\n  };\n\n  // Gestisce l'input dei metri posati per un cavo\n  const handleMetriChange = (cavoId, value) => {\n    // Aggiorna i metri per il cavo\n    setCaviMetri(prev => ({\n      ...prev,\n      [cavoId]: value\n    }));\n    \n    // Valida il valore inserito\n    validateMetri(cavoId, value);\n  };\n\n  // Valida i metri inseriti per un cavo\n  const validateMetri = (cavoId, value) => {\n    const cavo = cavi.find(c => c.id_cavo === cavoId);\n    if (!cavo) return;\n    \n    // Resetta gli errori e gli avvisi per questo cavo\n    setErrors(prev => {\n      const newErrors = { ...prev };\n      delete newErrors[cavoId];\n      return newErrors;\n    });\n    \n    setWarnings(prev => {\n      const newWarnings = { ...prev };\n      delete newWarnings[cavoId];\n      return newWarnings;\n    });\n    \n    // Controllo input vuoto\n    if (!value || value.trim() === '') {\n      setErrors(prev => ({\n        ...prev,\n        [cavoId]: 'Inserire un valore per i metri posati'\n      }));\n      return false;\n    }\n    \n    // Controllo formato numerico\n    if (isNaN(parseFloat(value)) || parseFloat(value) <= 0) {\n      setErrors(prev => ({\n        ...prev,\n        [cavoId]: 'Inserire un valore numerico positivo'\n      }));\n      return false;\n    }\n    \n    const metriPosati = parseFloat(value);\n    \n    // Controllo metri teorici cavo\n    if (cavo.metri_teorici && metriPosati > parseFloat(cavo.metri_teorici)) {\n      setWarnings(prev => ({\n        ...prev,\n        [cavoId]: `I metri posati (${metriPosati}m) superano i metri teorici del cavo (${cavo.metri_teorici}m)`\n      }));\n    }\n    \n    // Controllo metri residui bobina\n    const metriTotaliRichiesti = Object.entries(caviMetri)\n      .filter(([id, _]) => id !== cavoId) // Escludi il cavo corrente\n      .reduce((sum, [_, metri]) => sum + parseFloat(metri || 0), 0) + metriPosati;\n    \n    if (metriTotaliRichiesti > bobina.metri_residui) {\n      setWarnings(prev => ({\n        ...prev,\n        [cavoId]: `I metri totali richiesti (${metriTotaliRichiesti}m) superano i metri residui della bobina (${bobina.metri_residui}m)`\n      }));\n    }\n    \n    return true;\n  };\n\n  // Valida tutti i metri inseriti\n  const validateAllMetri = () => {\n    let isValid = true;\n    const newErrors = {};\n    const newWarnings = {};\n    \n    // Verifica che ci siano cavi selezionati\n    if (selectedCavi.length === 0) {\n      onError('Seleziona almeno un cavo');\n      return false;\n    }\n    \n    // Verifica che tutti i cavi selezionati abbiano metri inseriti\n    for (const cavo of selectedCavi) {\n      const metri = caviMetri[cavo.id_cavo];\n      \n      // Controllo input vuoto\n      if (!metri || metri.trim() === '') {\n        newErrors[cavo.id_cavo] = 'Inserire un valore per i metri posati';\n        isValid = false;\n        continue;\n      }\n      \n      // Controllo formato numerico\n      if (isNaN(parseFloat(metri)) || parseFloat(metri) <= 0) {\n        newErrors[cavo.id_cavo] = 'Inserire un valore numerico positivo';\n        isValid = false;\n        continue;\n      }\n      \n      const metriPosati = parseFloat(metri);\n      \n      // Controllo metri teorici cavo\n      if (cavo.metri_teorici && metriPosati > parseFloat(cavo.metri_teorici)) {\n        newWarnings[cavo.id_cavo] = `I metri posati (${metriPosati}m) superano i metri teorici del cavo (${cavo.metri_teorici}m)`;\n      }\n    }\n    \n    // Verifica che i metri totali richiesti non superino i metri residui della bobina\n    const metriTotaliRichiesti = Object.values(caviMetri).reduce((sum, metri) => sum + parseFloat(metri || 0), 0);\n    if (metriTotaliRichiesti > bobina.metri_residui) {\n      // Questo è un avviso globale, non specifico per un cavo\n      if (!window.confirm(`ATTENZIONE: I metri totali richiesti (${metriTotaliRichiesti}m) superano i metri residui della bobina (${bobina.metri_residui}m).\\n\\nQuesto porterà la bobina in stato OVER.\\n\\nVuoi continuare?`)) {\n        isValid = false;\n      }\n    }\n    \n    setErrors(newErrors);\n    setWarnings(newWarnings);\n    return isValid;\n  };\n\n  // Gestisce il salvataggio dei dati\n  const handleSave = async () => {\n    try {\n      // Validazione\n      if (!validateAllMetri()) {\n        return;\n      }\n      \n      setSaving(true);\n      \n      // Conferma finale\n      if (!window.confirm(`Confermi l'aggiornamento di ${selectedCavi.length} cavi con la bobina ${bobina.id_bobina}?`)) {\n        setSaving(false);\n        return;\n      }\n      \n      // Aggiorna ogni cavo selezionato\n      const results = [];\n      let errors = [];\n      \n      for (const cavo of selectedCavi) {\n        try {\n          const metriPosati = parseFloat(caviMetri[cavo.id_cavo]);\n          \n          // Determina se è necessario forzare lo stato OVER della bobina\n          const metriGiàUtilizzati = results.reduce((sum, r) => sum + r.metriPosati, 0);\n          const forceOver = (metriGiàUtilizzati + metriPosati) > bobina.metri_residui;\n          \n          // Aggiorna i metri posati del cavo\n          const result = await caviService.updateMetriPosati(\n            cantiereId,\n            cavo.id_cavo,\n            metriPosati,\n            bobina.id_bobina,\n            forceOver\n          );\n          \n          results.push({\n            cavo: cavo.id_cavo,\n            metriPosati,\n            success: true\n          });\n        } catch (error) {\n          console.error(`Errore nell'aggiornamento del cavo ${cavo.id_cavo}:`, error);\n          errors.push({\n            cavo: cavo.id_cavo,\n            error: error.message || 'Errore sconosciuto'\n          });\n        }\n      }\n      \n      // Gestione del risultato\n      if (errors.length === 0) {\n        // Tutti i cavi sono stati aggiornati con successo\n        onSuccess(`${results.length} cavi aggiornati con successo`);\n        onClose();\n      } else if (results.length > 0) {\n        // Alcuni cavi sono stati aggiornati, altri no\n        onSuccess(`${results.length} cavi aggiornati con successo, ${errors.length} falliti`);\n        onError(`Errori: ${errors.map(e => `${e.cavo}: ${e.error}`).join(', ')}`);\n        onClose();\n      } else {\n        // Nessun cavo è stato aggiornato\n        onError(`Nessun cavo aggiornato. Errori: ${errors.map(e => `${e.cavo}: ${e.error}`).join(', ')}`);\n      }\n    } catch (error) {\n      console.error('Errore durante il salvataggio:', error);\n      onError('Errore durante il salvataggio: ' + (error.message || 'Errore sconosciuto'));\n    } finally {\n      setSaving(false);\n    }\n  };\n\n  // Filtra i cavi in base al termine di ricerca\n  const filteredCavi = cavi.filter(cavo => \n    cavo.id_cavo.toLowerCase().includes(searchTerm.toLowerCase()) ||\n    (cavo.tipologia && cavo.tipologia.toLowerCase().includes(searchTerm.toLowerCase())) ||\n    (cavo.ubicazione_partenza && cavo.ubicazione_partenza.toLowerCase().includes(searchTerm.toLowerCase())) ||\n    (cavo.ubicazione_arrivo && cavo.ubicazione_arrivo.toLowerCase().includes(searchTerm.toLowerCase()))\n  );\n\n  return (\n    <Dialog open={open} onClose={onClose} maxWidth=\"lg\" fullWidth>\n      <DialogTitle>\n        Aggiungi cavi alla bobina {bobina?.numero_bobina || ''}\n      </DialogTitle>\n      <DialogContent>\n        {!bobina ? (\n          <Alert severity=\"error\">Nessuna bobina selezionata</Alert>\n        ) : (\n          <>\n            {/* Informazioni sulla bobina */}\n            <Box sx={{ mb: 3, p: 2, bgcolor: '#f5f5f5', borderRadius: 1 }}>\n              <Typography variant=\"subtitle1\" gutterBottom>\n                Dettagli bobina\n              </Typography>\n              <Box sx={{ display: 'flex', flexWrap: 'wrap', gap: 3 }}>\n                <Box>\n                  <Typography variant=\"body2\" color=\"text.secondary\">ID Bobina</Typography>\n                  <Typography variant=\"body1\">{bobina.id_bobina}</Typography>\n                </Box>\n                <Box>\n                  <Typography variant=\"body2\" color=\"text.secondary\">Tipologia</Typography>\n                  <Typography variant=\"body1\">{bobina.tipologia || 'N/A'}</Typography>\n                </Box>\n                <Box>\n                  <Typography variant=\"body2\" color=\"text.secondary\">Conduttori</Typography>\n                  <Typography variant=\"body1\">{bobina.n_conduttori || 'N/A'} x {bobina.sezione || 'N/A'}</Typography>\n                </Box>\n                <Box>\n                  <Typography variant=\"body2\" color=\"text.secondary\">Metri residui</Typography>\n                  <Typography variant=\"body1\">{bobina.metri_residui?.toFixed(1) || '0'} m</Typography>\n                </Box>\n                <Box>\n                  <Typography variant=\"body2\" color=\"text.secondary\">Stato</Typography>\n                  <Chip \n                    label={bobina.stato_bobina || 'N/D'} \n                    size=\"small\" \n                    color={\n                      bobina.stato_bobina === 'Disponibile' ? 'success' :\n                      bobina.stato_bobina === 'In uso' ? 'primary' :\n                      bobina.stato_bobina === 'Over' ? 'error' :\n                      bobina.stato_bobina === 'Terminata' ? 'warning' : 'default'\n                    }\n                  />\n                </Box>\n              </Box>\n            </Box>\n\n            {/* Ricerca cavi */}\n            <TextField\n              fullWidth\n              label=\"Cerca cavi\"\n              variant=\"outlined\"\n              value={searchTerm}\n              onChange={(e) => setSearchTerm(e.target.value)}\n              placeholder=\"Cerca per ID, tipologia, ubicazione...\"\n              sx={{ mb: 2 }}\n            />\n\n            {/* Tabella cavi */}\n            {caviLoading ? (\n              <Box sx={{ display: 'flex', justifyContent: 'center', my: 4 }}>\n                <CircularProgress />\n              </Box>\n            ) : filteredCavi.length === 0 ? (\n              <Alert severity=\"info\">\n                Nessun cavo compatibile disponibile per questa bobina.\n              </Alert>\n            ) : (\n              <TableContainer component={Paper} sx={{ mb: 3 }}>\n                <Table size=\"small\">\n                  <TableHead>\n                    <TableRow sx={{ bgcolor: '#f5f5f5' }}>\n                      <TableCell padding=\"checkbox\"></TableCell>\n                      <TableCell>ID Cavo</TableCell>\n                      <TableCell>Tipologia</TableCell>\n                      <TableCell>Ubicazione</TableCell>\n                      <TableCell>Metri Teorici</TableCell>\n                      <TableCell>Metri Posati</TableCell>\n                      <TableCell>Stato</TableCell>\n                    </TableRow>\n                  </TableHead>\n                  <TableBody>\n                    {filteredCavi.map((cavo) => {\n                      const isSelected = selectedCavi.some(c => c.id_cavo === cavo.id_cavo);\n                      return (\n                        <TableRow \n                          key={cavo.id_cavo}\n                          hover\n                          selected={isSelected}\n                          onClick={() => handleCavoSelect(cavo)}\n                          sx={{ cursor: 'pointer' }}\n                        >\n                          <TableCell padding=\"checkbox\">\n                            <Checkbox\n                              checked={isSelected}\n                              onChange={(e) => {\n                                e.stopPropagation();\n                                handleCavoSelect(cavo);\n                              }}\n                            />\n                          </TableCell>\n                          <TableCell>{cavo.id_cavo}</TableCell>\n                          <TableCell>{cavo.tipologia || 'N/A'}</TableCell>\n                          <TableCell>\n                            <Tooltip title={`Da: ${cavo.ubicazione_partenza || 'N/A'} - A: ${cavo.ubicazione_arrivo || 'N/A'}`}>\n                              <Box sx={{ maxWidth: 150, overflow: 'hidden', textOverflow: 'ellipsis', whiteSpace: 'nowrap' }}>\n                                {cavo.ubicazione_partenza || 'N/A'} → {cavo.ubicazione_arrivo || 'N/A'}\n                              </Box>\n                            </Tooltip>\n                          </TableCell>\n                          <TableCell>{cavo.metri_teorici || 'N/A'}</TableCell>\n                          <TableCell>\n                            {isSelected ? (\n                              <TextField\n                                size=\"small\"\n                                type=\"number\"\n                                value={caviMetri[cavo.id_cavo] || ''}\n                                onChange={(e) => {\n                                  e.stopPropagation();\n                                  handleMetriChange(cavo.id_cavo, e.target.value);\n                                }}\n                                onClick={(e) => e.stopPropagation()}\n                                error={!!errors[cavo.id_cavo]}\n                                helperText={errors[cavo.id_cavo] || warnings[cavo.id_cavo]}\n                                FormHelperTextProps={{\n                                  sx: { color: warnings[cavo.id_cavo] && !errors[cavo.id_cavo] ? 'warning.main' : 'error.main' }\n                                }}\n                                InputProps={{\n                                  endAdornment: warnings[cavo.id_cavo] && !errors[cavo.id_cavo] ? (\n                                    <Tooltip title={warnings[cavo.id_cavo]}>\n                                      <WarningIcon color=\"warning\" fontSize=\"small\" />\n                                    </Tooltip>\n                                  ) : null\n                                }}\n                              />\n                            ) : (\n                              'N/A'\n                            )}\n                          </TableCell>\n                          <TableCell>\n                            <Chip\n                              size=\"small\"\n                              label={cavo.stato_installazione || 'Da installare'}\n                              color={getCableStateColor(cavo.stato_installazione)}\n                            />\n                          </TableCell>\n                        </TableRow>\n                      );\n                    })}\n                  </TableBody>\n                </Table>\n              </TableContainer>\n            )}\n\n            {/* Riepilogo selezione */}\n            {selectedCavi.length > 0 && (\n              <Box sx={{ mb: 2 }}>\n                <Typography variant=\"subtitle1\" gutterBottom>\n                  Riepilogo selezione ({selectedCavi.length} cavi)\n                </Typography>\n                <TableContainer component={Paper}>\n                  <Table size=\"small\">\n                    <TableHead>\n                      <TableRow sx={{ bgcolor: '#f5f5f5' }}>\n                        <TableCell>ID Cavo</TableCell>\n                        <TableCell>Metri Posati</TableCell>\n                        <TableCell>Azioni</TableCell>\n                      </TableRow>\n                    </TableHead>\n                    <TableBody>\n                      {selectedCavi.map((cavo) => (\n                        <TableRow key={cavo.id_cavo}>\n                          <TableCell>{cavo.id_cavo}</TableCell>\n                          <TableCell>\n                            <TextField\n                              size=\"small\"\n                              type=\"number\"\n                              value={caviMetri[cavo.id_cavo] || ''}\n                              onChange={(e) => handleMetriChange(cavo.id_cavo, e.target.value)}\n                              error={!!errors[cavo.id_cavo]}\n                              helperText={errors[cavo.id_cavo] || warnings[cavo.id_cavo]}\n                              FormHelperTextProps={{\n                                sx: { color: warnings[cavo.id_cavo] && !errors[cavo.id_cavo] ? 'warning.main' : 'error.main' }\n                              }}\n                            />\n                          </TableCell>\n                          <TableCell>\n                            <IconButton\n                              size=\"small\"\n                              color=\"error\"\n                              onClick={() => handleCavoSelect(cavo)}\n                            >\n                              <DeleteIcon fontSize=\"small\" />\n                            </IconButton>\n                          </TableCell>\n                        </TableRow>\n                      ))}\n                      <TableRow>\n                        <TableCell colSpan={3} align=\"right\">\n                          <Typography variant=\"body2\">\n                            <strong>Metri totali richiesti:</strong> {\n                              Object.values(caviMetri).reduce((sum, metri) => {\n                                const value = parseFloat(metri || 0);\n                                return isNaN(value) ? sum : sum + value;\n                              }, 0).toFixed(1)\n                            } m\n                          </Typography>\n                          <Typography variant=\"body2\">\n                            <strong>Metri residui bobina:</strong> {bobina.metri_residui?.toFixed(1) || '0'} m\n                          </Typography>\n                        </TableCell>\n                      </TableRow>\n                    </TableBody>\n                  </Table>\n                </TableContainer>\n              </Box>\n            )}\n\n            {/* Avvisi */}\n            {Object.keys(warnings).length > 0 && (\n              <Alert severity=\"warning\" sx={{ mb: 2 }}>\n                <Typography variant=\"subtitle2\">Attenzione:</Typography>\n                <ul>\n                  {Object.entries(warnings).map(([cavoId, warning]) => (\n                    <li key={cavoId}>{cavoId}: {warning}</li>\n                  ))}\n                </ul>\n              </Alert>\n            )}\n\n            {/* Istruzioni */}\n            <Alert severity=\"info\" sx={{ mb: 2 }}>\n              Seleziona i cavi che vuoi associare a questa bobina e inserisci i metri posati per ciascuno.\n              I metri posati verranno sottratti dai metri residui della bobina.\n            </Alert>\n          </>\n        )}\n      </DialogContent>\n      <DialogActions>\n        <Button onClick={onClose} disabled={saving}>\n          Annulla\n        </Button>\n        <Button\n          onClick={handleSave}\n          color=\"primary\"\n          variant=\"contained\"\n          disabled={saving || selectedCavi.length === 0 || Object.keys(errors).length > 0}\n          startIcon={saving ? <CircularProgress size={20} /> : <SaveIcon />}\n        >\n          Salva\n        </Button>\n      </DialogActions>\n    </Dialog>\n  );\n};\n\nexport default QuickAddCablesDialog;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAClD,SACEC,MAAM,EACNC,WAAW,EACXC,aAAa,EACbC,aAAa,EACbC,MAAM,EACNC,UAAU,EACVC,GAAG,EACHC,SAAS,EACTC,QAAQ,EACRC,gBAAgB,EAChBC,KAAK,EACLC,SAAS,EACTC,SAAS,EACTC,cAAc,EACdC,SAAS,EACTC,QAAQ,EACRC,KAAK,EACLC,gBAAgB,EAChBC,KAAK,EACLC,UAAU,EACVC,IAAI,EACJC,OAAO,QACF,eAAe;AACtB,SACEC,GAAG,IAAIC,OAAO,EACdC,MAAM,IAAIC,UAAU,EACpBC,IAAI,IAAIC,QAAQ,EAChBC,IAAI,IAAIC,QAAQ,EAChBC,OAAO,IAAIC,WAAW,QACjB,qBAAqB;AAC5B,OAAOC,WAAW,MAAM,4BAA4B;AACpD,SAASC,mBAAmB,EAAEC,kBAAkB,EAAEC,gBAAgB,QAAQ,wBAAwB;;AAElG;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAVA,SAAAC,MAAA,IAAAC,OAAA,EAAAC,QAAA,IAAAC,SAAA;AAWA,MAAMC,oBAAoB,GAAGA,CAAC;EAAEC,IAAI;EAAEC,OAAO;EAAEC,MAAM;EAAEC,UAAU;EAAEC,SAAS;EAAEC;AAAQ,CAAC,KAAK;EAAAC,EAAA;EAAA,IAAAC,qBAAA,EAAAC,sBAAA;EAC1F;EACA,MAAM,CAACC,OAAO,EAAEC,UAAU,CAAC,GAAGrD,QAAQ,CAAC,KAAK,CAAC;EAC7C,MAAM,CAACsD,WAAW,EAAEC,cAAc,CAAC,GAAGvD,QAAQ,CAAC,KAAK,CAAC;EACrD,MAAM,CAACwD,IAAI,EAAEC,OAAO,CAAC,GAAGzD,QAAQ,CAAC,EAAE,CAAC;EACpC,MAAM,CAAC0D,YAAY,EAAEC,eAAe,CAAC,GAAG3D,QAAQ,CAAC,EAAE,CAAC;EACpD,MAAM,CAAC4D,SAAS,EAAEC,YAAY,CAAC,GAAG7D,QAAQ,CAAC,CAAC,CAAC,CAAC;EAC9C,MAAM,CAAC8D,UAAU,EAAEC,aAAa,CAAC,GAAG/D,QAAQ,CAAC,EAAE,CAAC;EAChD,MAAM,CAACgE,MAAM,EAAEC,SAAS,CAAC,GAAGjE,QAAQ,CAAC,CAAC,CAAC,CAAC;EACxC,MAAM,CAACkE,QAAQ,EAAEC,WAAW,CAAC,GAAGnE,QAAQ,CAAC,CAAC,CAAC,CAAC;EAC5C,MAAM,CAACoE,MAAM,EAAEC,SAAS,CAAC,GAAGrE,QAAQ,CAAC,KAAK,CAAC;;EAE3C;EACAC,SAAS,CAAC,MAAM;IACd,IAAI0C,IAAI,IAAIE,MAAM,EAAE;MAClByB,QAAQ,CAAC,CAAC;IACZ;EACF,CAAC,EAAE,CAAC3B,IAAI,EAAEE,MAAM,EAAEC,UAAU,CAAC,CAAC;;EAE9B;EACA,MAAMwB,QAAQ,GAAG,MAAAA,CAAA,KAAY;IAC3B,IAAI;MACFf,cAAc,CAAC,IAAI,CAAC;MACpB,MAAMgB,QAAQ,GAAG,MAAMrC,WAAW,CAACsC,OAAO,CAAC1B,UAAU,CAAC;;MAEtD;MACA,MAAM2B,eAAe,GAAGF,QAAQ,CAACG,MAAM,CAACC,IAAI;MAC1C;MACAA,IAAI,CAACC,SAAS,KAAK/B,MAAM,CAAC+B,SAAS,IACnCC,MAAM,CAACF,IAAI,CAACG,YAAY,CAAC,KAAKD,MAAM,CAAChC,MAAM,CAACiC,YAAY,CAAC,IACzDD,MAAM,CAACF,IAAI,CAACI,OAAO,CAAC,KAAKF,MAAM,CAAChC,MAAM,CAACkC,OAAO,CAAC;MAC/C;MACA,CAAC1C,gBAAgB,CAACsC,IAAI,CAAC;MACvB;MACAA,IAAI,CAACK,sBAAsB,KAAK,CAClC,CAAC;MAEDvB,OAAO,CAACgB,eAAe,CAAC;IAC1B,CAAC,CAAC,OAAOQ,KAAK,EAAE;MACdC,OAAO,CAACD,KAAK,CAAC,kCAAkC,EAAEA,KAAK,CAAC;MACxDjC,OAAO,CAAC,mCAAmC,IAAIiC,KAAK,CAACE,OAAO,IAAI,oBAAoB,CAAC,CAAC;IACxF,CAAC,SAAS;MACR5B,cAAc,CAAC,KAAK,CAAC;IACvB;EACF,CAAC;;EAED;EACA,MAAM6B,gBAAgB,GAAIT,IAAI,IAAK;IACjChB,eAAe,CAAC0B,IAAI,IAAI;MACtB,MAAMC,UAAU,GAAGD,IAAI,CAACE,IAAI,CAACC,CAAC,IAAIA,CAAC,CAACC,OAAO,KAAKd,IAAI,CAACc,OAAO,CAAC;MAE7D,IAAIH,UAAU,EAAE;QACd;QACA,MAAMI,WAAW,GAAGL,IAAI,CAACX,MAAM,CAACc,CAAC,IAAIA,CAAC,CAACC,OAAO,KAAKd,IAAI,CAACc,OAAO,CAAC;;QAEhE;QACA,MAAME,YAAY,GAAG;UAAE,GAAG/B;QAAU,CAAC;QACrC,OAAO+B,YAAY,CAAChB,IAAI,CAACc,OAAO,CAAC;QACjC5B,YAAY,CAAC8B,YAAY,CAAC;QAE1B,OAAOD,WAAW;MACpB,CAAC,MAAM;QACL;QACA,OAAO,CAAC,GAAGL,IAAI,EAAEV,IAAI,CAAC;MACxB;IACF,CAAC,CAAC;EACJ,CAAC;;EAED;EACA,MAAMiB,iBAAiB,GAAGA,CAACC,MAAM,EAAEC,KAAK,KAAK;IAC3C;IACAjC,YAAY,CAACwB,IAAI,KAAK;MACpB,GAAGA,IAAI;MACP,CAACQ,MAAM,GAAGC;IACZ,CAAC,CAAC,CAAC;;IAEH;IACAC,aAAa,CAACF,MAAM,EAAEC,KAAK,CAAC;EAC9B,CAAC;;EAED;EACA,MAAMC,aAAa,GAAGA,CAACF,MAAM,EAAEC,KAAK,KAAK;IACvC,MAAMnB,IAAI,GAAGnB,IAAI,CAACwC,IAAI,CAACR,CAAC,IAAIA,CAAC,CAACC,OAAO,KAAKI,MAAM,CAAC;IACjD,IAAI,CAAClB,IAAI,EAAE;;IAEX;IACAV,SAAS,CAACoB,IAAI,IAAI;MAChB,MAAMY,SAAS,GAAG;QAAE,GAAGZ;MAAK,CAAC;MAC7B,OAAOY,SAAS,CAACJ,MAAM,CAAC;MACxB,OAAOI,SAAS;IAClB,CAAC,CAAC;IAEF9B,WAAW,CAACkB,IAAI,IAAI;MAClB,MAAMa,WAAW,GAAG;QAAE,GAAGb;MAAK,CAAC;MAC/B,OAAOa,WAAW,CAACL,MAAM,CAAC;MAC1B,OAAOK,WAAW;IACpB,CAAC,CAAC;;IAEF;IACA,IAAI,CAACJ,KAAK,IAAIA,KAAK,CAACK,IAAI,CAAC,CAAC,KAAK,EAAE,EAAE;MACjClC,SAAS,CAACoB,IAAI,KAAK;QACjB,GAAGA,IAAI;QACP,CAACQ,MAAM,GAAG;MACZ,CAAC,CAAC,CAAC;MACH,OAAO,KAAK;IACd;;IAEA;IACA,IAAIO,KAAK,CAACC,UAAU,CAACP,KAAK,CAAC,CAAC,IAAIO,UAAU,CAACP,KAAK,CAAC,IAAI,CAAC,EAAE;MACtD7B,SAAS,CAACoB,IAAI,KAAK;QACjB,GAAGA,IAAI;QACP,CAACQ,MAAM,GAAG;MACZ,CAAC,CAAC,CAAC;MACH,OAAO,KAAK;IACd;IAEA,MAAMS,WAAW,GAAGD,UAAU,CAACP,KAAK,CAAC;;IAErC;IACA,IAAInB,IAAI,CAAC4B,aAAa,IAAID,WAAW,GAAGD,UAAU,CAAC1B,IAAI,CAAC4B,aAAa,CAAC,EAAE;MACtEpC,WAAW,CAACkB,IAAI,KAAK;QACnB,GAAGA,IAAI;QACP,CAACQ,MAAM,GAAG,mBAAmBS,WAAW,yCAAyC3B,IAAI,CAAC4B,aAAa;MACrG,CAAC,CAAC,CAAC;IACL;;IAEA;IACA,MAAMC,oBAAoB,GAAGC,MAAM,CAACC,OAAO,CAAC9C,SAAS,CAAC,CACnDc,MAAM,CAAC,CAAC,CAACiC,EAAE,EAAEC,CAAC,CAAC,KAAKD,EAAE,KAAKd,MAAM,CAAC,CAAC;IAAA,CACnCgB,MAAM,CAAC,CAACC,GAAG,EAAE,CAACF,CAAC,EAAEG,KAAK,CAAC,KAAKD,GAAG,GAAGT,UAAU,CAACU,KAAK,IAAI,CAAC,CAAC,EAAE,CAAC,CAAC,GAAGT,WAAW;IAE7E,IAAIE,oBAAoB,GAAG3D,MAAM,CAACmE,aAAa,EAAE;MAC/C7C,WAAW,CAACkB,IAAI,KAAK;QACnB,GAAGA,IAAI;QACP,CAACQ,MAAM,GAAG,6BAA6BW,oBAAoB,6CAA6C3D,MAAM,CAACmE,aAAa;MAC9H,CAAC,CAAC,CAAC;IACL;IAEA,OAAO,IAAI;EACb,CAAC;;EAED;EACA,MAAMC,gBAAgB,GAAGA,CAAA,KAAM;IAC7B,IAAIC,OAAO,GAAG,IAAI;IAClB,MAAMjB,SAAS,GAAG,CAAC,CAAC;IACpB,MAAMC,WAAW,GAAG,CAAC,CAAC;;IAEtB;IACA,IAAIxC,YAAY,CAACyD,MAAM,KAAK,CAAC,EAAE;MAC7BnE,OAAO,CAAC,0BAA0B,CAAC;MACnC,OAAO,KAAK;IACd;;IAEA;IACA,KAAK,MAAM2B,IAAI,IAAIjB,YAAY,EAAE;MAC/B,MAAMqD,KAAK,GAAGnD,SAAS,CAACe,IAAI,CAACc,OAAO,CAAC;;MAErC;MACA,IAAI,CAACsB,KAAK,IAAIA,KAAK,CAACZ,IAAI,CAAC,CAAC,KAAK,EAAE,EAAE;QACjCF,SAAS,CAACtB,IAAI,CAACc,OAAO,CAAC,GAAG,uCAAuC;QACjEyB,OAAO,GAAG,KAAK;QACf;MACF;;MAEA;MACA,IAAId,KAAK,CAACC,UAAU,CAACU,KAAK,CAAC,CAAC,IAAIV,UAAU,CAACU,KAAK,CAAC,IAAI,CAAC,EAAE;QACtDd,SAAS,CAACtB,IAAI,CAACc,OAAO,CAAC,GAAG,sCAAsC;QAChEyB,OAAO,GAAG,KAAK;QACf;MACF;MAEA,MAAMZ,WAAW,GAAGD,UAAU,CAACU,KAAK,CAAC;;MAErC;MACA,IAAIpC,IAAI,CAAC4B,aAAa,IAAID,WAAW,GAAGD,UAAU,CAAC1B,IAAI,CAAC4B,aAAa,CAAC,EAAE;QACtEL,WAAW,CAACvB,IAAI,CAACc,OAAO,CAAC,GAAG,mBAAmBa,WAAW,yCAAyC3B,IAAI,CAAC4B,aAAa,IAAI;MAC3H;IACF;;IAEA;IACA,MAAMC,oBAAoB,GAAGC,MAAM,CAACW,MAAM,CAACxD,SAAS,CAAC,CAACiD,MAAM,CAAC,CAACC,GAAG,EAAEC,KAAK,KAAKD,GAAG,GAAGT,UAAU,CAACU,KAAK,IAAI,CAAC,CAAC,EAAE,CAAC,CAAC;IAC7G,IAAIP,oBAAoB,GAAG3D,MAAM,CAACmE,aAAa,EAAE;MAC/C;MACA,IAAI,CAACK,MAAM,CAACC,OAAO,CAAC,yCAAyCd,oBAAoB,6CAA6C3D,MAAM,CAACmE,aAAa,oEAAoE,CAAC,EAAE;QACvNE,OAAO,GAAG,KAAK;MACjB;IACF;IAEAjD,SAAS,CAACgC,SAAS,CAAC;IACpB9B,WAAW,CAAC+B,WAAW,CAAC;IACxB,OAAOgB,OAAO;EAChB,CAAC;;EAED;EACA,MAAMK,UAAU,GAAG,MAAAA,CAAA,KAAY;IAC7B,IAAI;MACF;MACA,IAAI,CAACN,gBAAgB,CAAC,CAAC,EAAE;QACvB;MACF;MAEA5C,SAAS,CAAC,IAAI,CAAC;;MAEf;MACA,IAAI,CAACgD,MAAM,CAACC,OAAO,CAAC,+BAA+B5D,YAAY,CAACyD,MAAM,uBAAuBtE,MAAM,CAAC2E,SAAS,GAAG,CAAC,EAAE;QACjHnD,SAAS,CAAC,KAAK,CAAC;QAChB;MACF;;MAEA;MACA,MAAMoD,OAAO,GAAG,EAAE;MAClB,IAAIzD,MAAM,GAAG,EAAE;MAEf,KAAK,MAAMW,IAAI,IAAIjB,YAAY,EAAE;QAC/B,IAAI;UACF,MAAM4C,WAAW,GAAGD,UAAU,CAACzC,SAAS,CAACe,IAAI,CAACc,OAAO,CAAC,CAAC;;UAEvD;UACA,MAAMiC,kBAAkB,GAAGD,OAAO,CAACZ,MAAM,CAAC,CAACC,GAAG,EAAEa,CAAC,KAAKb,GAAG,GAAGa,CAAC,CAACrB,WAAW,EAAE,CAAC,CAAC;UAC7E,MAAMsB,SAAS,GAAIF,kBAAkB,GAAGpB,WAAW,GAAIzD,MAAM,CAACmE,aAAa;;UAE3E;UACA,MAAMa,MAAM,GAAG,MAAM3F,WAAW,CAAC4F,iBAAiB,CAChDhF,UAAU,EACV6B,IAAI,CAACc,OAAO,EACZa,WAAW,EACXzD,MAAM,CAAC2E,SAAS,EAChBI,SACF,CAAC;UAEDH,OAAO,CAACM,IAAI,CAAC;YACXpD,IAAI,EAAEA,IAAI,CAACc,OAAO;YAClBa,WAAW;YACX0B,OAAO,EAAE;UACX,CAAC,CAAC;QACJ,CAAC,CAAC,OAAO/C,KAAK,EAAE;UACdC,OAAO,CAACD,KAAK,CAAC,sCAAsCN,IAAI,CAACc,OAAO,GAAG,EAAER,KAAK,CAAC;UAC3EjB,MAAM,CAAC+D,IAAI,CAAC;YACVpD,IAAI,EAAEA,IAAI,CAACc,OAAO;YAClBR,KAAK,EAAEA,KAAK,CAACE,OAAO,IAAI;UAC1B,CAAC,CAAC;QACJ;MACF;;MAEA;MACA,IAAInB,MAAM,CAACmD,MAAM,KAAK,CAAC,EAAE;QACvB;QACApE,SAAS,CAAC,GAAG0E,OAAO,CAACN,MAAM,+BAA+B,CAAC;QAC3DvE,OAAO,CAAC,CAAC;MACX,CAAC,MAAM,IAAI6E,OAAO,CAACN,MAAM,GAAG,CAAC,EAAE;QAC7B;QACApE,SAAS,CAAC,GAAG0E,OAAO,CAACN,MAAM,kCAAkCnD,MAAM,CAACmD,MAAM,UAAU,CAAC;QACrFnE,OAAO,CAAC,WAAWgB,MAAM,CAACiE,GAAG,CAACC,CAAC,IAAI,GAAGA,CAAC,CAACvD,IAAI,KAAKuD,CAAC,CAACjD,KAAK,EAAE,CAAC,CAACkD,IAAI,CAAC,IAAI,CAAC,EAAE,CAAC;QACzEvF,OAAO,CAAC,CAAC;MACX,CAAC,MAAM;QACL;QACAI,OAAO,CAAC,mCAAmCgB,MAAM,CAACiE,GAAG,CAACC,CAAC,IAAI,GAAGA,CAAC,CAACvD,IAAI,KAAKuD,CAAC,CAACjD,KAAK,EAAE,CAAC,CAACkD,IAAI,CAAC,IAAI,CAAC,EAAE,CAAC;MACnG;IACF,CAAC,CAAC,OAAOlD,KAAK,EAAE;MACdC,OAAO,CAACD,KAAK,CAAC,gCAAgC,EAAEA,KAAK,CAAC;MACtDjC,OAAO,CAAC,iCAAiC,IAAIiC,KAAK,CAACE,OAAO,IAAI,oBAAoB,CAAC,CAAC;IACtF,CAAC,SAAS;MACRd,SAAS,CAAC,KAAK,CAAC;IAClB;EACF,CAAC;;EAED;EACA,MAAM+D,YAAY,GAAG5E,IAAI,CAACkB,MAAM,CAACC,IAAI,IACnCA,IAAI,CAACc,OAAO,CAAC4C,WAAW,CAAC,CAAC,CAACC,QAAQ,CAACxE,UAAU,CAACuE,WAAW,CAAC,CAAC,CAAC,IAC5D1D,IAAI,CAACC,SAAS,IAAID,IAAI,CAACC,SAAS,CAACyD,WAAW,CAAC,CAAC,CAACC,QAAQ,CAACxE,UAAU,CAACuE,WAAW,CAAC,CAAC,CAAE,IAClF1D,IAAI,CAAC4D,mBAAmB,IAAI5D,IAAI,CAAC4D,mBAAmB,CAACF,WAAW,CAAC,CAAC,CAACC,QAAQ,CAACxE,UAAU,CAACuE,WAAW,CAAC,CAAC,CAAE,IACtG1D,IAAI,CAAC6D,iBAAiB,IAAI7D,IAAI,CAAC6D,iBAAiB,CAACH,WAAW,CAAC,CAAC,CAACC,QAAQ,CAACxE,UAAU,CAACuE,WAAW,CAAC,CAAC,CACnG,CAAC;EAED,oBACE9F,OAAA,CAACrC,MAAM;IAACyC,IAAI,EAAEA,IAAK;IAACC,OAAO,EAAEA,OAAQ;IAAC6F,QAAQ,EAAC,IAAI;IAACC,SAAS;IAAAC,QAAA,gBAC3DpG,OAAA,CAACpC,WAAW;MAAAwI,QAAA,GAAC,4BACe,EAAC,CAAA9F,MAAM,aAANA,MAAM,uBAANA,MAAM,CAAE+F,aAAa,KAAI,EAAE;IAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAC3C,CAAC,eACdzG,OAAA,CAACnC,aAAa;MAAAuI,QAAA,EACX,CAAC9F,MAAM,gBACNN,OAAA,CAACnB,KAAK;QAAC6H,QAAQ,EAAC,OAAO;QAAAN,QAAA,EAAC;MAA0B;QAAAE,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAO,CAAC,gBAE1DzG,OAAA,CAAAE,SAAA;QAAAkG,QAAA,gBAEEpG,OAAA,CAAC/B,GAAG;UAAC0I,EAAE,EAAE;YAAEC,EAAE,EAAE,CAAC;YAAEC,CAAC,EAAE,CAAC;YAAEC,OAAO,EAAE,SAAS;YAAEC,YAAY,EAAE;UAAE,CAAE;UAAAX,QAAA,gBAC5DpG,OAAA,CAAChC,UAAU;YAACgJ,OAAO,EAAC,WAAW;YAACC,YAAY;YAAAb,QAAA,EAAC;UAE7C;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAY,CAAC,eACbzG,OAAA,CAAC/B,GAAG;YAAC0I,EAAE,EAAE;cAAEO,OAAO,EAAE,MAAM;cAAEC,QAAQ,EAAE,MAAM;cAAEC,GAAG,EAAE;YAAE,CAAE;YAAAhB,QAAA,gBACrDpG,OAAA,CAAC/B,GAAG;cAAAmI,QAAA,gBACFpG,OAAA,CAAChC,UAAU;gBAACgJ,OAAO,EAAC,OAAO;gBAACK,KAAK,EAAC,gBAAgB;gBAAAjB,QAAA,EAAC;cAAS;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAY,CAAC,eACzEzG,OAAA,CAAChC,UAAU;gBAACgJ,OAAO,EAAC,OAAO;gBAAAZ,QAAA,EAAE9F,MAAM,CAAC2E;cAAS;gBAAAqB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAa,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACxD,CAAC,eACNzG,OAAA,CAAC/B,GAAG;cAAAmI,QAAA,gBACFpG,OAAA,CAAChC,UAAU;gBAACgJ,OAAO,EAAC,OAAO;gBAACK,KAAK,EAAC,gBAAgB;gBAAAjB,QAAA,EAAC;cAAS;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAY,CAAC,eACzEzG,OAAA,CAAChC,UAAU;gBAACgJ,OAAO,EAAC,OAAO;gBAAAZ,QAAA,EAAE9F,MAAM,CAAC+B,SAAS,IAAI;cAAK;gBAAAiE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAa,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACjE,CAAC,eACNzG,OAAA,CAAC/B,GAAG;cAAAmI,QAAA,gBACFpG,OAAA,CAAChC,UAAU;gBAACgJ,OAAO,EAAC,OAAO;gBAACK,KAAK,EAAC,gBAAgB;gBAAAjB,QAAA,EAAC;cAAU;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAY,CAAC,eAC1EzG,OAAA,CAAChC,UAAU;gBAACgJ,OAAO,EAAC,OAAO;gBAAAZ,QAAA,GAAE9F,MAAM,CAACiC,YAAY,IAAI,KAAK,EAAC,KAAG,EAACjC,MAAM,CAACkC,OAAO,IAAI,KAAK;cAAA;gBAAA8D,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAa,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAChG,CAAC,eACNzG,OAAA,CAAC/B,GAAG;cAAAmI,QAAA,gBACFpG,OAAA,CAAChC,UAAU;gBAACgJ,OAAO,EAAC,OAAO;gBAACK,KAAK,EAAC,gBAAgB;gBAAAjB,QAAA,EAAC;cAAa;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAY,CAAC,eAC7EzG,OAAA,CAAChC,UAAU;gBAACgJ,OAAO,EAAC,OAAO;gBAAAZ,QAAA,GAAE,EAAAzF,qBAAA,GAAAL,MAAM,CAACmE,aAAa,cAAA9D,qBAAA,uBAApBA,qBAAA,CAAsB2G,OAAO,CAAC,CAAC,CAAC,KAAI,GAAG,EAAC,IAAE;cAAA;gBAAAhB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAY,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACjF,CAAC,eACNzG,OAAA,CAAC/B,GAAG;cAAAmI,QAAA,gBACFpG,OAAA,CAAChC,UAAU;gBAACgJ,OAAO,EAAC,OAAO;gBAACK,KAAK,EAAC,gBAAgB;gBAAAjB,QAAA,EAAC;cAAK;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAY,CAAC,eACrEzG,OAAA,CAACjB,IAAI;gBACHwI,KAAK,EAAEjH,MAAM,CAACkH,YAAY,IAAI,KAAM;gBACpCC,IAAI,EAAC,OAAO;gBACZJ,KAAK,EACH/G,MAAM,CAACkH,YAAY,KAAK,aAAa,GAAG,SAAS,GACjDlH,MAAM,CAACkH,YAAY,KAAK,QAAQ,GAAG,SAAS,GAC5ClH,MAAM,CAACkH,YAAY,KAAK,MAAM,GAAG,OAAO,GACxClH,MAAM,CAACkH,YAAY,KAAK,WAAW,GAAG,SAAS,GAAG;cACnD;gBAAAlB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACF,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACC,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,eAGNzG,OAAA,CAAC9B,SAAS;UACRiI,SAAS;UACToB,KAAK,EAAC,YAAY;UAClBP,OAAO,EAAC,UAAU;UAClBzD,KAAK,EAAEhC,UAAW;UAClBmG,QAAQ,EAAG/B,CAAC,IAAKnE,aAAa,CAACmE,CAAC,CAACgC,MAAM,CAACpE,KAAK,CAAE;UAC/CqE,WAAW,EAAC,wCAAwC;UACpDjB,EAAE,EAAE;YAAEC,EAAE,EAAE;UAAE;QAAE;UAAAN,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACf,CAAC,EAGD1F,WAAW,gBACVf,OAAA,CAAC/B,GAAG;UAAC0I,EAAE,EAAE;YAAEO,OAAO,EAAE,MAAM;YAAEW,cAAc,EAAE,QAAQ;YAAEC,EAAE,EAAE;UAAE,CAAE;UAAA1B,QAAA,eAC5DpG,OAAA,CAACpB,gBAAgB;YAAA0H,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACjB,CAAC,GACJZ,YAAY,CAACjB,MAAM,KAAK,CAAC,gBAC3B5E,OAAA,CAACnB,KAAK;UAAC6H,QAAQ,EAAC,MAAM;UAAAN,QAAA,EAAC;QAEvB;UAAAE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAO,CAAC,gBAERzG,OAAA,CAACxB,cAAc;UAACuJ,SAAS,EAAEpJ,KAAM;UAACgI,EAAE,EAAE;YAAEC,EAAE,EAAE;UAAE,CAAE;UAAAR,QAAA,eAC9CpG,OAAA,CAAC3B,KAAK;YAACoJ,IAAI,EAAC,OAAO;YAAArB,QAAA,gBACjBpG,OAAA,CAACvB,SAAS;cAAA2H,QAAA,eACRpG,OAAA,CAACtB,QAAQ;gBAACiI,EAAE,EAAE;kBAAEG,OAAO,EAAE;gBAAU,CAAE;gBAAAV,QAAA,gBACnCpG,OAAA,CAACzB,SAAS;kBAACyJ,OAAO,EAAC;gBAAU;kBAAA1B,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAY,CAAC,eAC1CzG,OAAA,CAACzB,SAAS;kBAAA6H,QAAA,EAAC;gBAAO;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAW,CAAC,eAC9BzG,OAAA,CAACzB,SAAS;kBAAA6H,QAAA,EAAC;gBAAS;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAW,CAAC,eAChCzG,OAAA,CAACzB,SAAS;kBAAA6H,QAAA,EAAC;gBAAU;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAW,CAAC,eACjCzG,OAAA,CAACzB,SAAS;kBAAA6H,QAAA,EAAC;gBAAa;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAW,CAAC,eACpCzG,OAAA,CAACzB,SAAS;kBAAA6H,QAAA,EAAC;gBAAY;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAW,CAAC,eACnCzG,OAAA,CAACzB,SAAS;kBAAA6H,QAAA,EAAC;gBAAK;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAW,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACpB;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACF,CAAC,eACZzG,OAAA,CAAC1B,SAAS;cAAA8H,QAAA,EACPP,YAAY,CAACH,GAAG,CAAEtD,IAAI,IAAK;gBAC1B,MAAMW,UAAU,GAAG5B,YAAY,CAAC6B,IAAI,CAACC,CAAC,IAAIA,CAAC,CAACC,OAAO,KAAKd,IAAI,CAACc,OAAO,CAAC;gBACrE,oBACElD,OAAA,CAACtB,QAAQ;kBAEPuJ,KAAK;kBACLC,QAAQ,EAAEnF,UAAW;kBACrBoF,OAAO,EAAEA,CAAA,KAAMtF,gBAAgB,CAACT,IAAI,CAAE;kBACtCuE,EAAE,EAAE;oBAAEyB,MAAM,EAAE;kBAAU,CAAE;kBAAAhC,QAAA,gBAE1BpG,OAAA,CAACzB,SAAS;oBAACyJ,OAAO,EAAC,UAAU;oBAAA5B,QAAA,eAC3BpG,OAAA,CAAC7B,QAAQ;sBACPkK,OAAO,EAAEtF,UAAW;sBACpB2E,QAAQ,EAAG/B,CAAC,IAAK;wBACfA,CAAC,CAAC2C,eAAe,CAAC,CAAC;wBACnBzF,gBAAgB,CAACT,IAAI,CAAC;sBACxB;oBAAE;sBAAAkE,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACH;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACO,CAAC,eACZzG,OAAA,CAACzB,SAAS;oBAAA6H,QAAA,EAAEhE,IAAI,CAACc;kBAAO;oBAAAoD,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAY,CAAC,eACrCzG,OAAA,CAACzB,SAAS;oBAAA6H,QAAA,EAAEhE,IAAI,CAACC,SAAS,IAAI;kBAAK;oBAAAiE,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAY,CAAC,eAChDzG,OAAA,CAACzB,SAAS;oBAAA6H,QAAA,eACRpG,OAAA,CAAChB,OAAO;sBAACuJ,KAAK,EAAE,OAAOnG,IAAI,CAAC4D,mBAAmB,IAAI,KAAK,SAAS5D,IAAI,CAAC6D,iBAAiB,IAAI,KAAK,EAAG;sBAAAG,QAAA,eACjGpG,OAAA,CAAC/B,GAAG;wBAAC0I,EAAE,EAAE;0BAAET,QAAQ,EAAE,GAAG;0BAAEsC,QAAQ,EAAE,QAAQ;0BAAEC,YAAY,EAAE,UAAU;0BAAEC,UAAU,EAAE;wBAAS,CAAE;wBAAAtC,QAAA,GAC5FhE,IAAI,CAAC4D,mBAAmB,IAAI,KAAK,EAAC,UAAG,EAAC5D,IAAI,CAAC6D,iBAAiB,IAAI,KAAK;sBAAA;wBAAAK,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACnE;oBAAC;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACC;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACD,CAAC,eACZzG,OAAA,CAACzB,SAAS;oBAAA6H,QAAA,EAAEhE,IAAI,CAAC4B,aAAa,IAAI;kBAAK;oBAAAsC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAY,CAAC,eACpDzG,OAAA,CAACzB,SAAS;oBAAA6H,QAAA,EACPrD,UAAU,gBACT/C,OAAA,CAAC9B,SAAS;sBACRuJ,IAAI,EAAC,OAAO;sBACZkB,IAAI,EAAC,QAAQ;sBACbpF,KAAK,EAAElC,SAAS,CAACe,IAAI,CAACc,OAAO,CAAC,IAAI,EAAG;sBACrCwE,QAAQ,EAAG/B,CAAC,IAAK;wBACfA,CAAC,CAAC2C,eAAe,CAAC,CAAC;wBACnBjF,iBAAiB,CAACjB,IAAI,CAACc,OAAO,EAAEyC,CAAC,CAACgC,MAAM,CAACpE,KAAK,CAAC;sBACjD,CAAE;sBACF4E,OAAO,EAAGxC,CAAC,IAAKA,CAAC,CAAC2C,eAAe,CAAC,CAAE;sBACpC5F,KAAK,EAAE,CAAC,CAACjB,MAAM,CAACW,IAAI,CAACc,OAAO,CAAE;sBAC9B0F,UAAU,EAAEnH,MAAM,CAACW,IAAI,CAACc,OAAO,CAAC,IAAIvB,QAAQ,CAACS,IAAI,CAACc,OAAO,CAAE;sBAC3D2F,mBAAmB,EAAE;wBACnBlC,EAAE,EAAE;0BAAEU,KAAK,EAAE1F,QAAQ,CAACS,IAAI,CAACc,OAAO,CAAC,IAAI,CAACzB,MAAM,CAACW,IAAI,CAACc,OAAO,CAAC,GAAG,cAAc,GAAG;wBAAa;sBAC/F,CAAE;sBACF4F,UAAU,EAAE;wBACVC,YAAY,EAAEpH,QAAQ,CAACS,IAAI,CAACc,OAAO,CAAC,IAAI,CAACzB,MAAM,CAACW,IAAI,CAACc,OAAO,CAAC,gBAC3DlD,OAAA,CAAChB,OAAO;0BAACuJ,KAAK,EAAE5G,QAAQ,CAACS,IAAI,CAACc,OAAO,CAAE;0BAAAkD,QAAA,eACrCpG,OAAA,CAACN,WAAW;4BAAC2H,KAAK,EAAC,SAAS;4BAAC2B,QAAQ,EAAC;0BAAO;4BAAA1C,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OAAE;wBAAC;0BAAAH,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OACzC,CAAC,GACR;sBACN;oBAAE;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACH,CAAC,GAEF;kBACD;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACQ,CAAC,eACZzG,OAAA,CAACzB,SAAS;oBAAA6H,QAAA,eACRpG,OAAA,CAACjB,IAAI;sBACH0I,IAAI,EAAC,OAAO;sBACZF,KAAK,EAAEnF,IAAI,CAAC6G,mBAAmB,IAAI,eAAgB;sBACnD5B,KAAK,EAAExH,kBAAkB,CAACuC,IAAI,CAAC6G,mBAAmB;oBAAE;sBAAA3C,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACrD;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACO,CAAC;gBAAA,GA3DPrE,IAAI,CAACc,OAAO;kBAAAoD,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OA4DT,CAAC;cAEf,CAAC;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACO,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACP;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACM,CACjB,EAGAtF,YAAY,CAACyD,MAAM,GAAG,CAAC,iBACtB5E,OAAA,CAAC/B,GAAG;UAAC0I,EAAE,EAAE;YAAEC,EAAE,EAAE;UAAE,CAAE;UAAAR,QAAA,gBACjBpG,OAAA,CAAChC,UAAU;YAACgJ,OAAO,EAAC,WAAW;YAACC,YAAY;YAAAb,QAAA,GAAC,uBACtB,EAACjF,YAAY,CAACyD,MAAM,EAAC,QAC5C;UAAA;YAAA0B,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAY,CAAC,eACbzG,OAAA,CAACxB,cAAc;YAACuJ,SAAS,EAAEpJ,KAAM;YAAAyH,QAAA,eAC/BpG,OAAA,CAAC3B,KAAK;cAACoJ,IAAI,EAAC,OAAO;cAAArB,QAAA,gBACjBpG,OAAA,CAACvB,SAAS;gBAAA2H,QAAA,eACRpG,OAAA,CAACtB,QAAQ;kBAACiI,EAAE,EAAE;oBAAEG,OAAO,EAAE;kBAAU,CAAE;kBAAAV,QAAA,gBACnCpG,OAAA,CAACzB,SAAS;oBAAA6H,QAAA,EAAC;kBAAO;oBAAAE,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAW,CAAC,eAC9BzG,OAAA,CAACzB,SAAS;oBAAA6H,QAAA,EAAC;kBAAY;oBAAAE,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAW,CAAC,eACnCzG,OAAA,CAACzB,SAAS;oBAAA6H,QAAA,EAAC;kBAAM;oBAAAE,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAW,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACrB;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACF,CAAC,eACZzG,OAAA,CAAC1B,SAAS;gBAAA8H,QAAA,GACPjF,YAAY,CAACuE,GAAG,CAAEtD,IAAI,iBACrBpC,OAAA,CAACtB,QAAQ;kBAAA0H,QAAA,gBACPpG,OAAA,CAACzB,SAAS;oBAAA6H,QAAA,EAAEhE,IAAI,CAACc;kBAAO;oBAAAoD,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAY,CAAC,eACrCzG,OAAA,CAACzB,SAAS;oBAAA6H,QAAA,eACRpG,OAAA,CAAC9B,SAAS;sBACRuJ,IAAI,EAAC,OAAO;sBACZkB,IAAI,EAAC,QAAQ;sBACbpF,KAAK,EAAElC,SAAS,CAACe,IAAI,CAACc,OAAO,CAAC,IAAI,EAAG;sBACrCwE,QAAQ,EAAG/B,CAAC,IAAKtC,iBAAiB,CAACjB,IAAI,CAACc,OAAO,EAAEyC,CAAC,CAACgC,MAAM,CAACpE,KAAK,CAAE;sBACjEb,KAAK,EAAE,CAAC,CAACjB,MAAM,CAACW,IAAI,CAACc,OAAO,CAAE;sBAC9B0F,UAAU,EAAEnH,MAAM,CAACW,IAAI,CAACc,OAAO,CAAC,IAAIvB,QAAQ,CAACS,IAAI,CAACc,OAAO,CAAE;sBAC3D2F,mBAAmB,EAAE;wBACnBlC,EAAE,EAAE;0BAAEU,KAAK,EAAE1F,QAAQ,CAACS,IAAI,CAACc,OAAO,CAAC,IAAI,CAACzB,MAAM,CAACW,IAAI,CAACc,OAAO,CAAC,GAAG,cAAc,GAAG;wBAAa;sBAC/F;oBAAE;sBAAAoD,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACH;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACO,CAAC,eACZzG,OAAA,CAACzB,SAAS;oBAAA6H,QAAA,eACRpG,OAAA,CAAClB,UAAU;sBACT2I,IAAI,EAAC,OAAO;sBACZJ,KAAK,EAAC,OAAO;sBACbc,OAAO,EAAEA,CAAA,KAAMtF,gBAAgB,CAACT,IAAI,CAAE;sBAAAgE,QAAA,eAEtCpG,OAAA,CAACZ,UAAU;wBAAC4J,QAAQ,EAAC;sBAAO;wBAAA1C,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAE;oBAAC;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACrB;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACJ,CAAC;gBAAA,GAvBCrE,IAAI,CAACc,OAAO;kBAAAoD,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAwBjB,CACX,CAAC,eACFzG,OAAA,CAACtB,QAAQ;kBAAA0H,QAAA,eACPpG,OAAA,CAACzB,SAAS;oBAAC2K,OAAO,EAAE,CAAE;oBAACC,KAAK,EAAC,OAAO;oBAAA/C,QAAA,gBAClCpG,OAAA,CAAChC,UAAU;sBAACgJ,OAAO,EAAC,OAAO;sBAAAZ,QAAA,gBACzBpG,OAAA;wBAAAoG,QAAA,EAAQ;sBAAuB;wBAAAE,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAQ,CAAC,KAAC,EACvCvC,MAAM,CAACW,MAAM,CAACxD,SAAS,CAAC,CAACiD,MAAM,CAAC,CAACC,GAAG,EAAEC,KAAK,KAAK;wBAC9C,MAAMjB,KAAK,GAAGO,UAAU,CAACU,KAAK,IAAI,CAAC,CAAC;wBACpC,OAAOX,KAAK,CAACN,KAAK,CAAC,GAAGgB,GAAG,GAAGA,GAAG,GAAGhB,KAAK;sBACzC,CAAC,EAAE,CAAC,CAAC,CAAC+D,OAAO,CAAC,CAAC,CAAC,EACjB,IACH;oBAAA;sBAAAhB,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAY,CAAC,eACbzG,OAAA,CAAChC,UAAU;sBAACgJ,OAAO,EAAC,OAAO;sBAAAZ,QAAA,gBACzBpG,OAAA;wBAAAoG,QAAA,EAAQ;sBAAqB;wBAAAE,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAQ,CAAC,KAAC,EAAC,EAAA7F,sBAAA,GAAAN,MAAM,CAACmE,aAAa,cAAA7D,sBAAA,uBAApBA,sBAAA,CAAsB0G,OAAO,CAAC,CAAC,CAAC,KAAI,GAAG,EAAC,IAClF;oBAAA;sBAAAhB,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAY,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACJ;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACJ,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACF,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACP;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACM,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACd,CACN,EAGAvC,MAAM,CAACkF,IAAI,CAACzH,QAAQ,CAAC,CAACiD,MAAM,GAAG,CAAC,iBAC/B5E,OAAA,CAACnB,KAAK;UAAC6H,QAAQ,EAAC,SAAS;UAACC,EAAE,EAAE;YAAEC,EAAE,EAAE;UAAE,CAAE;UAAAR,QAAA,gBACtCpG,OAAA,CAAChC,UAAU;YAACgJ,OAAO,EAAC,WAAW;YAAAZ,QAAA,EAAC;UAAW;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAY,CAAC,eACxDzG,OAAA;YAAAoG,QAAA,EACGlC,MAAM,CAACC,OAAO,CAACxC,QAAQ,CAAC,CAAC+D,GAAG,CAAC,CAAC,CAACpC,MAAM,EAAE+F,OAAO,CAAC,kBAC9CrJ,OAAA;cAAAoG,QAAA,GAAkB9C,MAAM,EAAC,IAAE,EAAC+F,OAAO;YAAA,GAA1B/F,MAAM;cAAAgD,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAyB,CACzC;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACA,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACA,CACR,eAGDzG,OAAA,CAACnB,KAAK;UAAC6H,QAAQ,EAAC,MAAM;UAACC,EAAE,EAAE;YAAEC,EAAE,EAAE;UAAE,CAAE;UAAAR,QAAA,EAAC;QAGtC;UAAAE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAO,CAAC;MAAA,eACR;IACH;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACY,CAAC,eAChBzG,OAAA,CAAClC,aAAa;MAAAsI,QAAA,gBACZpG,OAAA,CAACjC,MAAM;QAACoK,OAAO,EAAE9H,OAAQ;QAACiJ,QAAQ,EAAEzH,MAAO;QAAAuE,QAAA,EAAC;MAE5C;QAAAE,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAQ,CAAC,eACTzG,OAAA,CAACjC,MAAM;QACLoK,OAAO,EAAEnD,UAAW;QACpBqC,KAAK,EAAC,SAAS;QACfL,OAAO,EAAC,WAAW;QACnBsC,QAAQ,EAAEzH,MAAM,IAAIV,YAAY,CAACyD,MAAM,KAAK,CAAC,IAAIV,MAAM,CAACkF,IAAI,CAAC3H,MAAM,CAAC,CAACmD,MAAM,GAAG,CAAE;QAChF2E,SAAS,EAAE1H,MAAM,gBAAG7B,OAAA,CAACpB,gBAAgB;UAAC6I,IAAI,EAAE;QAAG;UAAAnB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,gBAAGzG,OAAA,CAACV,QAAQ;UAAAgH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAE;QAAAL,QAAA,EACnE;MAED;QAAAE,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAQ,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACI,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACV,CAAC;AAEb,CAAC;AAAC/F,EAAA,CAjhBIP,oBAAoB;AAAAqJ,EAAA,GAApBrJ,oBAAoB;AAmhB1B,eAAeA,oBAAoB;AAAC,IAAAqJ,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}