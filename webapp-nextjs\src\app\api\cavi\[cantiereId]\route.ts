import { NextRequest, NextResponse } from 'next/server'

interface Cavo {
  id_cavo: string
  id_cantiere: number
  revisione_ufficiale: string
  sistema: string
  utility: string
  colore_cavo: string
  tipologia: string
  n_conduttori: number
  sezione: string
  sh: string
  ubicazione_partenza: string
  utenza_partenza: string
  descrizione_utenza_partenza: string
  ubicazione_arrivo: string
  utenza_arrivo: string
  descrizione_utenza_arrivo: string
  metri_teorici: number
  metratura_reale?: number
  responsabile_posa?: string
  id_bobina?: string
  stato_installazione: string
  modificato_manualmente: number
  timestamp: string
  collegamenti: number
  responsabile_partenza?: string
  responsabile_arrivo?: string
  comanda_posa?: string
  comanda_partenza?: string
  comanda_arrivo?: string
  comanda_certificazione?: string
  stato_certificazione?: string
  data_certificazione_cavo?: string
}

export async function GET(
  request: NextRequest,
  { params }: { params: { cantiereId: string } }
) {
  try {
    const cantiereId = parseInt(params.cantiereId)
    
    if (isNaN(cantiereId)) {
      return NextResponse.json(
        { error: 'ID cantiere non valido' },
        { status: 400 }
      )
    }

    // Per ora restituiamo dati mock per testare l'interfaccia
    // TODO: Implementare chiamata al backend Python
    const mockCavi: Cavo[] = [
      {
        id_cavo: "C001",
        id_cantiere: cantiereId,
        revisione_ufficiale: "Rev.1",
        sistema: "SYS01",
        utility: "POWER",
        colore_cavo: "NERO",
        tipologia: "FG16OR16",
        n_conduttori: 16,
        sezione: "2.5",
        sh: "SI",
        ubicazione_partenza: "QE01",
        utenza_partenza: "UT001",
        descrizione_utenza_partenza: "Quadro Elettrico Principale",
        ubicazione_arrivo: "QE02",
        utenza_arrivo: "UT002",
        descrizione_utenza_arrivo: "Quadro Secondario",
        metri_teorici: 150,
        metratura_reale: 148.5,
        responsabile_posa: "Mario Rossi",
        id_bobina: "BOB001",
        stato_installazione: "INSTALLATO",
        modificato_manualmente: 0,
        timestamp: "2024-01-15T10:00:00Z",
        collegamenti: 2,
        responsabile_partenza: "Luigi Verdi",
        responsabile_arrivo: "Anna Bianchi",
        comanda_posa: "CMD001",
        comanda_partenza: "CMD002",
        comanda_arrivo: "CMD003",
        stato_certificazione: "DA_CERTIFICARE"
      },
      {
        id_cavo: "C002",
        id_cantiere: cantiereId,
        revisione_ufficiale: "Rev.1",
        sistema: "SYS01",
        utility: "CONTROL",
        colore_cavo: "BLU",
        tipologia: "FG7OR",
        n_conduttori: 7,
        sezione: "1.5",
        sh: "SI",
        ubicazione_partenza: "QC01",
        utenza_partenza: "UT003",
        descrizione_utenza_partenza: "Quadro Controllo",
        ubicazione_arrivo: "PAN01",
        utenza_arrivo: "UT004",
        descrizione_utenza_arrivo: "Pannello Operatore",
        metri_teorici: 75,
        metratura_reale: 73.2,
        responsabile_posa: "Mario Rossi",
        id_bobina: "BOB002",
        stato_installazione: "INSTALLATO",
        modificato_manualmente: 0,
        timestamp: "2024-01-16T14:30:00Z",
        collegamenti: 1,
        responsabile_partenza: "Luigi Verdi",
        comanda_posa: "CMD004",
        comanda_partenza: "CMD005",
        stato_certificazione: "DA_CERTIFICARE"
      },
      {
        id_cavo: "C003",
        id_cantiere: cantiereId,
        revisione_ufficiale: "Rev.1",
        sistema: "SYS02",
        utility: "SIGNAL",
        colore_cavo: "VERDE",
        tipologia: "FG4OR",
        n_conduttori: 4,
        sezione: "0.75",
        sh: "NO",
        ubicazione_partenza: "SEN01",
        utenza_partenza: "UT005",
        descrizione_utenza_partenza: "Sensore Temperatura",
        ubicazione_arrivo: "PLC01",
        utenza_arrivo: "UT006",
        descrizione_utenza_arrivo: "PLC Controllo",
        metri_teorici: 25,
        stato_installazione: "DA_INSTALLARE",
        modificato_manualmente: 0,
        timestamp: "2024-01-17T09:15:00Z",
        collegamenti: 0,
        stato_certificazione: "NON_APPLICABILE"
      }
    ]

    // Applica filtri se presenti
    const { searchParams } = new URL(request.url)
    const statoFiltro = searchParams.get('stato_installazione')
    const tipologiaFiltro = searchParams.get('tipologia')
    
    let caviFiltrati = mockCavi
    
    if (statoFiltro) {
      caviFiltrati = caviFiltrati.filter(cavo => cavo.stato_installazione === statoFiltro)
    }
    
    if (tipologiaFiltro) {
      caviFiltrati = caviFiltrati.filter(cavo => cavo.tipologia.includes(tipologiaFiltro))
    }

    return NextResponse.json({
      success: true,
      data: caviFiltrati,
      total: caviFiltrati.length,
      cantiere_id: cantiereId
    })

  } catch (error) {
    console.error('Errore nel recupero cavi:', error)
    return NextResponse.json(
      { error: 'Errore interno del server' },
      { status: 500 }
    )
  }
}
