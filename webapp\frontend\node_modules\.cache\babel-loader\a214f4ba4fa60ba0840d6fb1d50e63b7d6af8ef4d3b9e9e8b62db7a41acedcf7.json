{"ast": null, "code": "import axios from 'axios';\nimport config from '../config';\nimport axiosInstance from './axiosConfig';\nconst API_URL = config.API_URL;\nconst parcoCaviService = {\n  // Ottiene la lista delle bobine di un cantiere\n  getBobine: async cantiereId => {\n    try {\n      // Assicurati che cantiereId sia un numero\n      const cantiereIdNum = parseInt(cantiereId, 10);\n      if (isNaN(cantiereIdNum)) {\n        throw new Error(`ID cantiere non valido: ${cantiereId}`);\n      }\n      const response = await axiosInstance.get(`/parco-cavi/${cantiereIdNum}`);\n      return response.data;\n    } catch (error) {\n      console.error('Get bobine error:', error);\n      throw error.response ? error.response.data : error;\n    }\n  },\n  // Crea una nuova bobina\n  createBobina: async (cantiereId, bobinaData) => {\n    // Variabile per memorizzare i dati inviati per il logging degli errori\n    let sentData;\n    try {\n      // Assicurati che cantiereId sia un numero\n      const cantiereIdNum = parseInt(cantiereId, 10);\n      if (isNaN(cantiereIdNum)) {\n        throw new Error(`ID cantiere non valido: ${cantiereId}`);\n      }\n\n      // Log dei dati originali\n      console.log('Dati originali della bobina:', JSON.stringify(bobinaData, null, 2));\n\n      // Validazione dei campi obbligatori\n      if (!bobinaData.utility || bobinaData.utility.trim() === '') {\n        throw new Error(\"L'utility è obbligatoria\");\n      }\n      if (!bobinaData.tipologia || bobinaData.tipologia.trim() === '') {\n        throw new Error(\"La tipologia è obbligatoria\");\n      }\n      if (!bobinaData.n_conduttori || bobinaData.n_conduttori.trim() === '') {\n        throw new Error(\"Il numero di conduttori è obbligatorio\");\n      }\n      if (!bobinaData.sezione || bobinaData.sezione.trim() === '') {\n        throw new Error(\"La sezione è obbligatoria\");\n      }\n\n      // Assicurati che i campi numerici siano numeri e che i campi stringa siano stringhe\n      const processedData = {\n        ...bobinaData,\n        numero_bobina: String(bobinaData.numero_bobina || ''),\n        utility: String(bobinaData.utility || ''),\n        tipologia: String(bobinaData.tipologia || ''),\n        n_conduttori: parseInt(bobinaData.n_conduttori) || 0,\n        sezione: parseFloat(bobinaData.sezione) || 0,\n        metri_totali: parseFloat(bobinaData.metri_totali) || 0,\n        ubicazione_bobina: String(bobinaData.ubicazione_bobina || 'TBD'),\n        fornitore: String(bobinaData.fornitore || 'TBD'),\n        n_DDT: String(bobinaData.n_DDT || 'TBD'),\n        data_DDT: bobinaData.data_DDT || null,\n        configurazione: String(bobinaData.configurazione || 's')\n        // Non inviare metri_residui, verranno impostati dal backend\n      };\n\n      // Validazione metri_totali\n      if (processedData.metri_totali <= 0) {\n        throw new Error(\"I metri totali devono essere maggiori di zero\");\n      }\n\n      // Log dei dati processati prima dell'invio\n      console.log('Dati processati prima dell\\'invio:', JSON.stringify(processedData, null, 2));\n\n      // Salva una copia dei dati per il log degli errori\n      const sentData = {\n        ...processedData\n      };\n\n      // Assicurati che numero_bobina sia impostato correttamente\n      if (processedData.configurazione === 'n' && (!processedData.numero_bobina || processedData.numero_bobina.trim() === '')) {\n        throw new Error('L\\'ID della bobina è obbligatorio');\n      }\n\n      // Log dei dati processati\n      console.log('Dati processati della bobina:', JSON.stringify(processedData, null, 2));\n\n      // Log della richiesta\n      console.log(`Invio richiesta POST a: /parco-cavi/${cantiereIdNum}`);\n      const response = await axiosInstance.post(`/parco-cavi/${cantiereIdNum}`, processedData);\n      console.log('Risposta dal server:', response.data);\n      return response.data;\n    } catch (error) {\n      console.error('Create bobina error:', error);\n      // Log dettagliato dei dati inviati che hanno causato l'errore\n      if (sentData) {\n        console.error('Dati inviati che hanno causato errore:', JSON.stringify(sentData, null, 2));\n      }\n\n      // Gestione specifica per errori di rete\n      if (error.isNetworkError || error.isTimeoutError) {\n        console.error('Errore di rete o timeout:', error.customMessage || error.message);\n        throw {\n          detail: error.customMessage || 'Impossibile connettersi al server. Verifica la connessione di rete e riprova.',\n          status: 0,\n          isNetworkError: true\n        };\n      }\n      if (error.response) {\n        console.error('Dettagli errore:', error.response.data);\n        console.error('Status errore:', error.response.status);\n        console.error('Headers errore:', error.response.headers);\n\n        // Formatta il messaggio di errore in modo più leggibile\n        const errorDetail = error.response.data.detail || 'Errore sconosciuto';\n        throw {\n          detail: errorDetail,\n          status: error.response.status\n        };\n      }\n      // Se è un errore di validazione locale, formatta il messaggio\n      if (error instanceof Error) {\n        throw {\n          detail: error.message,\n          status: 400\n        };\n      }\n      throw error;\n    }\n  },\n  // Aggiorna una bobina esistente\n  updateBobina: async (cantiereId, numeroBobina, bobinaData) => {\n    // Variabile per memorizzare i dati inviati per il logging degli errori\n    let sentData;\n    try {\n      // Assicurati che cantiereId sia un numero\n      const cantiereIdNum = parseInt(cantiereId, 10);\n      if (isNaN(cantiereIdNum)) {\n        throw new Error(`ID cantiere non valido: ${cantiereId}`);\n      }\n\n      // Estrai solo il numero della bobina dalla stringa completa (se necessario)\n      // Il formato dell'ID bobina è C{id_cantiere}_B{numero_bobina}\n      let bobinaPart = numeroBobina;\n      if (numeroBobina.includes('_B')) {\n        bobinaPart = numeroBobina.split('_B')[1];\n      }\n\n      // Validazione dei campi obbligatori\n      if (!bobinaData.utility || bobinaData.utility.trim() === '') {\n        throw new Error(\"L'utility è obbligatoria\");\n      }\n      if (!bobinaData.tipologia || bobinaData.tipologia.trim() === '') {\n        throw new Error(\"La tipologia è obbligatoria\");\n      }\n      if (!bobinaData.n_conduttori || String(bobinaData.n_conduttori).trim() === '') {\n        throw new Error(\"Il numero di conduttori è obbligatorio\");\n      }\n      if (!bobinaData.sezione || String(bobinaData.sezione).trim() === '') {\n        throw new Error(\"La sezione è obbligatoria\");\n      }\n\n      // Assicurati che i campi numerici siano numeri e che i campi stringa siano stringhe\n      const processedData = {\n        ...bobinaData,\n        utility: String(bobinaData.utility || ''),\n        tipologia: String(bobinaData.tipologia || ''),\n        n_conduttori: parseInt(bobinaData.n_conduttori) || 0,\n        sezione: parseFloat(bobinaData.sezione) || 0,\n        metri_totali: parseFloat(bobinaData.metri_totali) || 0,\n        ubicazione_bobina: String(bobinaData.ubicazione_bobina || 'TBD'),\n        fornitore: String(bobinaData.fornitore || 'TBD'),\n        n_DDT: String(bobinaData.n_DDT || 'TBD'),\n        data_DDT: bobinaData.data_DDT || null,\n        configurazione: String(bobinaData.configurazione || 's')\n      };\n\n      // Validazione metri_totali\n      if (processedData.metri_totali <= 0) {\n        throw new Error(\"I metri totali devono essere maggiori di zero\");\n      }\n\n      // Log dei dati processati\n      console.log('Dati processati per aggiornamento bobina:', JSON.stringify(processedData, null, 2));\n\n      // Salva una copia dei dati per il log degli errori\n      sentData = {\n        ...processedData\n      };\n\n      // Log della richiesta\n      console.log(`Invio richiesta PUT a: /parco-cavi/${cantiereIdNum}/${bobinaPart}`);\n      const response = await axiosInstance.put(`/parco-cavi/${cantiereIdNum}/${bobinaPart}`, processedData);\n      console.log('Risposta dal server:', response.data);\n      return response.data;\n    } catch (error) {\n      console.error('Update bobina error:', error);\n\n      // Log dettagliato dei dati inviati che hanno causato l'errore\n      if (sentData) {\n        console.error('Dati inviati che hanno causato errore:', JSON.stringify(sentData, null, 2));\n      }\n\n      // Gestione specifica per errori di rete\n      if (error.isNetworkError || error.isTimeoutError) {\n        console.error('Errore di rete o timeout:', error.customMessage || error.message);\n        throw {\n          detail: error.customMessage || 'Impossibile connettersi al server. Verifica la connessione di rete e riprova.',\n          status: 0,\n          isNetworkError: true\n        };\n      }\n      if (error.response) {\n        console.error('Dettagli errore:', error.response.data);\n        console.error('Status errore:', error.response.status);\n        console.error('Headers errore:', error.response.headers);\n\n        // Formatta il messaggio di errore in modo più leggibile\n        const errorDetail = error.response.data.detail || 'Errore sconosciuto';\n        throw {\n          detail: errorDetail,\n          status: error.response.status\n        };\n      }\n      // Se è un errore di validazione locale, formatta il messaggio\n      if (error instanceof Error) {\n        throw {\n          detail: error.message,\n          status: 400\n        };\n      }\n      throw error;\n    }\n  },\n  // Elimina una bobina\n  deleteBobina: async (cantiereId, numeroBobina) => {\n    try {\n      // Assicurati che cantiereId sia un numero\n      const cantiereIdNum = parseInt(cantiereId, 10);\n      if (isNaN(cantiereIdNum)) {\n        throw new Error(`ID cantiere non valido: ${cantiereId}`);\n      }\n\n      // Estrai solo il numero della bobina dalla stringa completa (se necessario)\n      // Il formato dell'ID bobina è C{id_cantiere}_B{numero_bobina}\n      let bobinaPart = numeroBobina;\n      if (numeroBobina.includes('_B')) {\n        bobinaPart = numeroBobina.split('_B')[1];\n      }\n      const response = await axiosInstance.delete(`/parco-cavi/${cantiereIdNum}/${bobinaPart}`);\n\n      // Log per debug\n      console.log('Risposta eliminazione bobina:', response.data);\n\n      // Verifica se è l'ultima bobina\n      if (response.data.is_last_bobina) {\n        console.log(`Eliminata l'ultima bobina del cantiere ${cantiereId}. Il parco cavi è ora vuoto.`);\n        // Rimuovi la configurazione salvata in localStorage\n        localStorage.removeItem(`cantiere_${cantiereId}_config`);\n      }\n      return response.data;\n    } catch (error) {\n      console.error('Delete bobina error:', error);\n\n      // Gestione specifica per errori di rete\n      if (error.isNetworkError || error.isTimeoutError) {\n        console.error('Errore di rete o timeout:', error.customMessage || error.message);\n        throw {\n          detail: error.customMessage || 'Impossibile connettersi al server. Verifica la connessione di rete e riprova.',\n          status: 0,\n          isNetworkError: true\n        };\n      }\n      throw error.response ? error.response.data : error;\n    }\n  },\n  // Verifica se è il primo inserimento di una bobina per un cantiere e recupera la configurazione esistente\n  isFirstBobinaInsertion: async cantiereId => {\n    try {\n      // Assicurati che cantiereId sia un numero\n      const cantiereIdNum = parseInt(cantiereId, 10);\n      if (isNaN(cantiereIdNum)) {\n        throw new Error(`ID cantiere non valido: ${cantiereId}`);\n      }\n\n      // Usa l'API per verificare se è il primo inserimento\n      const response = await axiosInstance.get(`/parco-cavi/${cantiereIdNum}/is-first-insertion`);\n      console.log('Risposta API is-first-insertion:', response.data);\n      return response.data;\n    } catch (error) {\n      console.error('Check first insertion error:', error);\n\n      // Gestione specifica per errori di rete\n      if (error.isNetworkError || error.isTimeoutError) {\n        console.error('Errore di rete o timeout:', error.customMessage || error.message);\n        // Per questo metodo, in caso di errore di rete, restituiamo un valore di default\n        // invece di propagare l'errore, per evitare che l'utente non possa procedere\n        return {\n          is_first_insertion: false,\n          configurazione: 's'\n        };\n      }\n      throw error.response ? error.response.data : error;\n    }\n  },\n  // Recupera la configurazione esistente per un cantiere\n  getBobinaConfig: async cantiereId => {\n    try {\n      // Usa l'API per verificare se è il primo inserimento e ottenere la configurazione\n      const response = await parcoCaviService.isFirstBobinaInsertion(cantiereId);\n      return {\n        configurazione: response.configurazione || 's'\n      };\n    } catch (error) {\n      console.error('Get bobina config error:', error);\n      // In caso di errore, restituisci la configurazione di default\n      return {\n        configurazione: 's'\n      };\n    }\n  },\n  // Ottiene lo storico utilizzo delle bobine\n  getStoricoUtilizzo: async cantiereId => {\n    try {\n      // Assicurati che cantiereId sia un numero\n      const cantiereIdNum = parseInt(cantiereId, 10);\n      if (isNaN(cantiereIdNum)) {\n        throw new Error(`ID cantiere non valido: ${cantiereId}`);\n      }\n      const response = await axiosInstance.get(`/parco-cavi/${cantiereIdNum}/storico`);\n      return response.data;\n    } catch (error) {\n      console.error('Get storico utilizzo error:', error);\n      throw error.response ? error.response.data : error;\n    }\n  }\n};\nexport default parcoCaviService;", "map": {"version": 3, "names": ["axios", "config", "axiosInstance", "API_URL", "parcoCaviService", "getBobine", "cantiereId", "cantiereIdNum", "parseInt", "isNaN", "Error", "response", "get", "data", "error", "console", "createBobina", "bobina<PERSON><PERSON>", "sentData", "log", "JSON", "stringify", "utility", "trim", "tipologia", "n_conduttori", "sezione", "processedData", "numero_bobina", "String", "parseFloat", "metri_totali", "ubicazione_bobina", "fornitore", "n_DDT", "data_DDT", "configurazione", "post", "isNetworkError", "isTimeoutError", "customMessage", "message", "detail", "status", "headers", "errorDetail", "updateBobina", "numeroBobina", "b<PERSON><PERSON><PERSON><PERSON>", "includes", "split", "put", "deleteBobina", "delete", "is_last_bobina", "localStorage", "removeItem", "isFirstBobinaInsertion", "is_first_insertion", "getBobinaConfig", "getStoricoUtilizzo"], "sources": ["C:/Users/<USER>/PycharmProjects/CMS/webapp/frontend/src/services/parcoCaviService.js"], "sourcesContent": ["import axios from 'axios';\r\nimport config from '../config';\r\nimport axiosInstance from './axiosConfig';\r\n\r\nconst API_URL = config.API_URL;\r\n\r\nconst parcoCaviService = {\r\n  // Ottiene la lista delle bobine di un cantiere\r\n  getBobine: async (cantiereId) => {\r\n    try {\r\n      // Assicurati che cantiereId sia un numero\r\n      const cantiereIdNum = parseInt(cantiereId, 10);\r\n      if (isNaN(cantiereIdNum)) {\r\n        throw new Error(`ID cantiere non valido: ${cantiereId}`);\r\n      }\r\n\r\n      const response = await axiosInstance.get(`/parco-cavi/${cantiereIdNum}`);\r\n      return response.data;\r\n    } catch (error) {\r\n      console.error('Get bobine error:', error);\r\n      throw error.response ? error.response.data : error;\r\n    }\r\n  },\r\n\r\n  // Crea una nuova bobina\r\n  createBobina: async (cantiereId, bobinaData) => {\r\n    // Variabile per memorizzare i dati inviati per il logging degli errori\r\n    let sentData;\r\n\r\n    try {\r\n      // Assicurati che cantiereId sia un numero\r\n      const cantiereIdNum = parseInt(cantiereId, 10);\r\n      if (isNaN(cantiereIdNum)) {\r\n        throw new Error(`ID cantiere non valido: ${cantiereId}`);\r\n      }\r\n\r\n      // Log dei dati originali\r\n      console.log('Dati originali della bobina:', JSON.stringify(bobinaData, null, 2));\r\n\r\n      // Validazione dei campi obbligatori\r\n      if (!bobinaData.utility || bobinaData.utility.trim() === '') {\r\n        throw new Error(\"L'utility è obbligatoria\");\r\n      }\r\n\r\n      if (!bobinaData.tipologia || bobinaData.tipologia.trim() === '') {\r\n        throw new Error(\"La tipologia è obbligatoria\");\r\n      }\r\n\r\n      if (!bobinaData.n_conduttori || bobinaData.n_conduttori.trim() === '') {\r\n        throw new Error(\"Il numero di conduttori è obbligatorio\");\r\n      }\r\n\r\n      if (!bobinaData.sezione || bobinaData.sezione.trim() === '') {\r\n        throw new Error(\"La sezione è obbligatoria\");\r\n      }\r\n\r\n      // Assicurati che i campi numerici siano numeri e che i campi stringa siano stringhe\r\n      const processedData = {\r\n        ...bobinaData,\r\n        numero_bobina: String(bobinaData.numero_bobina || ''),\r\n        utility: String(bobinaData.utility || ''),\r\n        tipologia: String(bobinaData.tipologia || ''),\r\n        n_conduttori: parseInt(bobinaData.n_conduttori) || 0,\r\n        sezione: parseFloat(bobinaData.sezione) || 0,\r\n        metri_totali: parseFloat(bobinaData.metri_totali) || 0,\r\n        ubicazione_bobina: String(bobinaData.ubicazione_bobina || 'TBD'),\r\n        fornitore: String(bobinaData.fornitore || 'TBD'),\r\n        n_DDT: String(bobinaData.n_DDT || 'TBD'),\r\n        data_DDT: bobinaData.data_DDT || null,\r\n        configurazione: String(bobinaData.configurazione || 's')\r\n        // Non inviare metri_residui, verranno impostati dal backend\r\n      };\r\n\r\n      // Validazione metri_totali\r\n      if (processedData.metri_totali <= 0) {\r\n        throw new Error(\"I metri totali devono essere maggiori di zero\");\r\n      }\r\n\r\n      // Log dei dati processati prima dell'invio\r\n      console.log('Dati processati prima dell\\'invio:', JSON.stringify(processedData, null, 2));\r\n\r\n      // Salva una copia dei dati per il log degli errori\r\n      const sentData = { ...processedData };\r\n\r\n      // Assicurati che numero_bobina sia impostato correttamente\r\n      if (processedData.configurazione === 'n' && (!processedData.numero_bobina || processedData.numero_bobina.trim() === '')) {\r\n        throw new Error('L\\'ID della bobina è obbligatorio');\r\n      }\r\n\r\n      // Log dei dati processati\r\n      console.log('Dati processati della bobina:', JSON.stringify(processedData, null, 2));\r\n\r\n      // Log della richiesta\r\n      console.log(`Invio richiesta POST a: /parco-cavi/${cantiereIdNum}`);\r\n\r\n      const response = await axiosInstance.post(`/parco-cavi/${cantiereIdNum}`, processedData);\r\n      console.log('Risposta dal server:', response.data);\r\n      return response.data;\r\n    } catch (error) {\r\n      console.error('Create bobina error:', error);\r\n      // Log dettagliato dei dati inviati che hanno causato l'errore\r\n      if (sentData) {\r\n        console.error('Dati inviati che hanno causato errore:', JSON.stringify(sentData, null, 2));\r\n      }\r\n\r\n      // Gestione specifica per errori di rete\r\n      if (error.isNetworkError || error.isTimeoutError) {\r\n        console.error('Errore di rete o timeout:', error.customMessage || error.message);\r\n        throw { detail: error.customMessage || 'Impossibile connettersi al server. Verifica la connessione di rete e riprova.', status: 0, isNetworkError: true };\r\n      }\r\n\r\n      if (error.response) {\r\n        console.error('Dettagli errore:', error.response.data);\r\n        console.error('Status errore:', error.response.status);\r\n        console.error('Headers errore:', error.response.headers);\r\n\r\n        // Formatta il messaggio di errore in modo più leggibile\r\n        const errorDetail = error.response.data.detail || 'Errore sconosciuto';\r\n        throw { detail: errorDetail, status: error.response.status };\r\n      }\r\n      // Se è un errore di validazione locale, formatta il messaggio\r\n      if (error instanceof Error) {\r\n        throw { detail: error.message, status: 400 };\r\n      }\r\n      throw error;\r\n    }\r\n  },\r\n\r\n  // Aggiorna una bobina esistente\r\n  updateBobina: async (cantiereId, numeroBobina, bobinaData) => {\r\n    // Variabile per memorizzare i dati inviati per il logging degli errori\r\n    let sentData;\r\n\r\n    try {\r\n      // Assicurati che cantiereId sia un numero\r\n      const cantiereIdNum = parseInt(cantiereId, 10);\r\n      if (isNaN(cantiereIdNum)) {\r\n        throw new Error(`ID cantiere non valido: ${cantiereId}`);\r\n      }\r\n\r\n      // Estrai solo il numero della bobina dalla stringa completa (se necessario)\r\n      // Il formato dell'ID bobina è C{id_cantiere}_B{numero_bobina}\r\n      let bobinaPart = numeroBobina;\r\n      if (numeroBobina.includes('_B')) {\r\n        bobinaPart = numeroBobina.split('_B')[1];\r\n      }\r\n\r\n      // Validazione dei campi obbligatori\r\n      if (!bobinaData.utility || bobinaData.utility.trim() === '') {\r\n        throw new Error(\"L'utility è obbligatoria\");\r\n      }\r\n\r\n      if (!bobinaData.tipologia || bobinaData.tipologia.trim() === '') {\r\n        throw new Error(\"La tipologia è obbligatoria\");\r\n      }\r\n\r\n      if (!bobinaData.n_conduttori || String(bobinaData.n_conduttori).trim() === '') {\r\n        throw new Error(\"Il numero di conduttori è obbligatorio\");\r\n      }\r\n\r\n      if (!bobinaData.sezione || String(bobinaData.sezione).trim() === '') {\r\n        throw new Error(\"La sezione è obbligatoria\");\r\n      }\r\n\r\n      // Assicurati che i campi numerici siano numeri e che i campi stringa siano stringhe\r\n      const processedData = {\r\n        ...bobinaData,\r\n        utility: String(bobinaData.utility || ''),\r\n        tipologia: String(bobinaData.tipologia || ''),\r\n        n_conduttori: parseInt(bobinaData.n_conduttori) || 0,\r\n        sezione: parseFloat(bobinaData.sezione) || 0,\r\n        metri_totali: parseFloat(bobinaData.metri_totali) || 0,\r\n        ubicazione_bobina: String(bobinaData.ubicazione_bobina || 'TBD'),\r\n        fornitore: String(bobinaData.fornitore || 'TBD'),\r\n        n_DDT: String(bobinaData.n_DDT || 'TBD'),\r\n        data_DDT: bobinaData.data_DDT || null,\r\n        configurazione: String(bobinaData.configurazione || 's')\r\n      };\r\n\r\n      // Validazione metri_totali\r\n      if (processedData.metri_totali <= 0) {\r\n        throw new Error(\"I metri totali devono essere maggiori di zero\");\r\n      }\r\n\r\n      // Log dei dati processati\r\n      console.log('Dati processati per aggiornamento bobina:', JSON.stringify(processedData, null, 2));\r\n\r\n      // Salva una copia dei dati per il log degli errori\r\n      sentData = { ...processedData };\r\n\r\n      // Log della richiesta\r\n      console.log(`Invio richiesta PUT a: /parco-cavi/${cantiereIdNum}/${bobinaPart}`);\r\n\r\n      const response = await axiosInstance.put(`/parco-cavi/${cantiereIdNum}/${bobinaPart}`, processedData);\r\n      console.log('Risposta dal server:', response.data);\r\n      return response.data;\r\n    } catch (error) {\r\n      console.error('Update bobina error:', error);\r\n\r\n      // Log dettagliato dei dati inviati che hanno causato l'errore\r\n      if (sentData) {\r\n        console.error('Dati inviati che hanno causato errore:', JSON.stringify(sentData, null, 2));\r\n      }\r\n\r\n      // Gestione specifica per errori di rete\r\n      if (error.isNetworkError || error.isTimeoutError) {\r\n        console.error('Errore di rete o timeout:', error.customMessage || error.message);\r\n        throw { detail: error.customMessage || 'Impossibile connettersi al server. Verifica la connessione di rete e riprova.', status: 0, isNetworkError: true };\r\n      }\r\n\r\n      if (error.response) {\r\n        console.error('Dettagli errore:', error.response.data);\r\n        console.error('Status errore:', error.response.status);\r\n        console.error('Headers errore:', error.response.headers);\r\n\r\n        // Formatta il messaggio di errore in modo più leggibile\r\n        const errorDetail = error.response.data.detail || 'Errore sconosciuto';\r\n        throw { detail: errorDetail, status: error.response.status };\r\n      }\r\n      // Se è un errore di validazione locale, formatta il messaggio\r\n      if (error instanceof Error) {\r\n        throw { detail: error.message, status: 400 };\r\n      }\r\n      throw error;\r\n    }\r\n  },\r\n\r\n  // Elimina una bobina\r\n  deleteBobina: async (cantiereId, numeroBobina) => {\r\n    try {\r\n      // Assicurati che cantiereId sia un numero\r\n      const cantiereIdNum = parseInt(cantiereId, 10);\r\n      if (isNaN(cantiereIdNum)) {\r\n        throw new Error(`ID cantiere non valido: ${cantiereId}`);\r\n      }\r\n\r\n      // Estrai solo il numero della bobina dalla stringa completa (se necessario)\r\n      // Il formato dell'ID bobina è C{id_cantiere}_B{numero_bobina}\r\n      let bobinaPart = numeroBobina;\r\n      if (numeroBobina.includes('_B')) {\r\n        bobinaPart = numeroBobina.split('_B')[1];\r\n      }\r\n\r\n      const response = await axiosInstance.delete(`/parco-cavi/${cantiereIdNum}/${bobinaPart}`);\r\n\r\n      // Log per debug\r\n      console.log('Risposta eliminazione bobina:', response.data);\r\n\r\n      // Verifica se è l'ultima bobina\r\n      if (response.data.is_last_bobina) {\r\n        console.log(`Eliminata l'ultima bobina del cantiere ${cantiereId}. Il parco cavi è ora vuoto.`);\r\n        // Rimuovi la configurazione salvata in localStorage\r\n        localStorage.removeItem(`cantiere_${cantiereId}_config`);\r\n      }\r\n\r\n      return response.data;\r\n    } catch (error) {\r\n      console.error('Delete bobina error:', error);\r\n\r\n      // Gestione specifica per errori di rete\r\n      if (error.isNetworkError || error.isTimeoutError) {\r\n        console.error('Errore di rete o timeout:', error.customMessage || error.message);\r\n        throw { detail: error.customMessage || 'Impossibile connettersi al server. Verifica la connessione di rete e riprova.', status: 0, isNetworkError: true };\r\n      }\r\n\r\n      throw error.response ? error.response.data : error;\r\n    }\r\n  },\r\n\r\n  // Verifica se è il primo inserimento di una bobina per un cantiere e recupera la configurazione esistente\r\n  isFirstBobinaInsertion: async (cantiereId) => {\r\n    try {\r\n      // Assicurati che cantiereId sia un numero\r\n      const cantiereIdNum = parseInt(cantiereId, 10);\r\n      if (isNaN(cantiereIdNum)) {\r\n        throw new Error(`ID cantiere non valido: ${cantiereId}`);\r\n      }\r\n\r\n      // Usa l'API per verificare se è il primo inserimento\r\n      const response = await axiosInstance.get(`/parco-cavi/${cantiereIdNum}/is-first-insertion`);\r\n      console.log('Risposta API is-first-insertion:', response.data);\r\n      return response.data;\r\n    } catch (error) {\r\n      console.error('Check first insertion error:', error);\r\n\r\n      // Gestione specifica per errori di rete\r\n      if (error.isNetworkError || error.isTimeoutError) {\r\n        console.error('Errore di rete o timeout:', error.customMessage || error.message);\r\n        // Per questo metodo, in caso di errore di rete, restituiamo un valore di default\r\n        // invece di propagare l'errore, per evitare che l'utente non possa procedere\r\n        return { is_first_insertion: false, configurazione: 's' };\r\n      }\r\n\r\n      throw error.response ? error.response.data : error;\r\n    }\r\n  },\r\n\r\n  // Recupera la configurazione esistente per un cantiere\r\n  getBobinaConfig: async (cantiereId) => {\r\n    try {\r\n      // Usa l'API per verificare se è il primo inserimento e ottenere la configurazione\r\n      const response = await parcoCaviService.isFirstBobinaInsertion(cantiereId);\r\n      return { configurazione: response.configurazione || 's' };\r\n    } catch (error) {\r\n      console.error('Get bobina config error:', error);\r\n      // In caso di errore, restituisci la configurazione di default\r\n      return { configurazione: 's' };\r\n    }\r\n  },\r\n\r\n  // Ottiene lo storico utilizzo delle bobine\r\n  getStoricoUtilizzo: async (cantiereId) => {\r\n    try {\r\n      // Assicurati che cantiereId sia un numero\r\n      const cantiereIdNum = parseInt(cantiereId, 10);\r\n      if (isNaN(cantiereIdNum)) {\r\n        throw new Error(`ID cantiere non valido: ${cantiereId}`);\r\n      }\r\n\r\n      const response = await axiosInstance.get(`/parco-cavi/${cantiereIdNum}/storico`);\r\n      return response.data;\r\n    } catch (error) {\r\n      console.error('Get storico utilizzo error:', error);\r\n      throw error.response ? error.response.data : error;\r\n    }\r\n  }\r\n};\r\n\r\nexport default parcoCaviService;\r\n"], "mappings": "AAAA,OAAOA,KAAK,MAAM,OAAO;AACzB,OAAOC,MAAM,MAAM,WAAW;AAC9B,OAAOC,aAAa,MAAM,eAAe;AAEzC,MAAMC,OAAO,GAAGF,MAAM,CAACE,OAAO;AAE9B,MAAMC,gBAAgB,GAAG;EACvB;EACAC,SAAS,EAAE,MAAOC,UAAU,IAAK;IAC/B,IAAI;MACF;MACA,MAAMC,aAAa,GAAGC,QAAQ,CAACF,UAAU,EAAE,EAAE,CAAC;MAC9C,IAAIG,KAAK,CAACF,aAAa,CAAC,EAAE;QACxB,MAAM,IAAIG,KAAK,CAAC,2BAA2BJ,UAAU,EAAE,CAAC;MAC1D;MAEA,MAAMK,QAAQ,GAAG,MAAMT,aAAa,CAACU,GAAG,CAAC,eAAeL,aAAa,EAAE,CAAC;MACxE,OAAOI,QAAQ,CAACE,IAAI;IACtB,CAAC,CAAC,OAAOC,KAAK,EAAE;MACdC,OAAO,CAACD,KAAK,CAAC,mBAAmB,EAAEA,KAAK,CAAC;MACzC,MAAMA,KAAK,CAACH,QAAQ,GAAGG,KAAK,CAACH,QAAQ,CAACE,IAAI,GAAGC,KAAK;IACpD;EACF,CAAC;EAED;EACAE,YAAY,EAAE,MAAAA,CAAOV,UAAU,EAAEW,UAAU,KAAK;IAC9C;IACA,IAAIC,QAAQ;IAEZ,IAAI;MACF;MACA,MAAMX,aAAa,GAAGC,QAAQ,CAACF,UAAU,EAAE,EAAE,CAAC;MAC9C,IAAIG,KAAK,CAACF,aAAa,CAAC,EAAE;QACxB,MAAM,IAAIG,KAAK,CAAC,2BAA2BJ,UAAU,EAAE,CAAC;MAC1D;;MAEA;MACAS,OAAO,CAACI,GAAG,CAAC,8BAA8B,EAAEC,IAAI,CAACC,SAAS,CAACJ,UAAU,EAAE,IAAI,EAAE,CAAC,CAAC,CAAC;;MAEhF;MACA,IAAI,CAACA,UAAU,CAACK,OAAO,IAAIL,UAAU,CAACK,OAAO,CAACC,IAAI,CAAC,CAAC,KAAK,EAAE,EAAE;QAC3D,MAAM,IAAIb,KAAK,CAAC,0BAA0B,CAAC;MAC7C;MAEA,IAAI,CAACO,UAAU,CAACO,SAAS,IAAIP,UAAU,CAACO,SAAS,CAACD,IAAI,CAAC,CAAC,KAAK,EAAE,EAAE;QAC/D,MAAM,IAAIb,KAAK,CAAC,6BAA6B,CAAC;MAChD;MAEA,IAAI,CAACO,UAAU,CAACQ,YAAY,IAAIR,UAAU,CAACQ,YAAY,CAACF,IAAI,CAAC,CAAC,KAAK,EAAE,EAAE;QACrE,MAAM,IAAIb,KAAK,CAAC,wCAAwC,CAAC;MAC3D;MAEA,IAAI,CAACO,UAAU,CAACS,OAAO,IAAIT,UAAU,CAACS,OAAO,CAACH,IAAI,CAAC,CAAC,KAAK,EAAE,EAAE;QAC3D,MAAM,IAAIb,KAAK,CAAC,2BAA2B,CAAC;MAC9C;;MAEA;MACA,MAAMiB,aAAa,GAAG;QACpB,GAAGV,UAAU;QACbW,aAAa,EAAEC,MAAM,CAACZ,UAAU,CAACW,aAAa,IAAI,EAAE,CAAC;QACrDN,OAAO,EAAEO,MAAM,CAACZ,UAAU,CAACK,OAAO,IAAI,EAAE,CAAC;QACzCE,SAAS,EAAEK,MAAM,CAACZ,UAAU,CAACO,SAAS,IAAI,EAAE,CAAC;QAC7CC,YAAY,EAAEjB,QAAQ,CAACS,UAAU,CAACQ,YAAY,CAAC,IAAI,CAAC;QACpDC,OAAO,EAAEI,UAAU,CAACb,UAAU,CAACS,OAAO,CAAC,IAAI,CAAC;QAC5CK,YAAY,EAAED,UAAU,CAACb,UAAU,CAACc,YAAY,CAAC,IAAI,CAAC;QACtDC,iBAAiB,EAAEH,MAAM,CAACZ,UAAU,CAACe,iBAAiB,IAAI,KAAK,CAAC;QAChEC,SAAS,EAAEJ,MAAM,CAACZ,UAAU,CAACgB,SAAS,IAAI,KAAK,CAAC;QAChDC,KAAK,EAAEL,MAAM,CAACZ,UAAU,CAACiB,KAAK,IAAI,KAAK,CAAC;QACxCC,QAAQ,EAAElB,UAAU,CAACkB,QAAQ,IAAI,IAAI;QACrCC,cAAc,EAAEP,MAAM,CAACZ,UAAU,CAACmB,cAAc,IAAI,GAAG;QACvD;MACF,CAAC;;MAED;MACA,IAAIT,aAAa,CAACI,YAAY,IAAI,CAAC,EAAE;QACnC,MAAM,IAAIrB,KAAK,CAAC,+CAA+C,CAAC;MAClE;;MAEA;MACAK,OAAO,CAACI,GAAG,CAAC,oCAAoC,EAAEC,IAAI,CAACC,SAAS,CAACM,aAAa,EAAE,IAAI,EAAE,CAAC,CAAC,CAAC;;MAEzF;MACA,MAAMT,QAAQ,GAAG;QAAE,GAAGS;MAAc,CAAC;;MAErC;MACA,IAAIA,aAAa,CAACS,cAAc,KAAK,GAAG,KAAK,CAACT,aAAa,CAACC,aAAa,IAAID,aAAa,CAACC,aAAa,CAACL,IAAI,CAAC,CAAC,KAAK,EAAE,CAAC,EAAE;QACvH,MAAM,IAAIb,KAAK,CAAC,mCAAmC,CAAC;MACtD;;MAEA;MACAK,OAAO,CAACI,GAAG,CAAC,+BAA+B,EAAEC,IAAI,CAACC,SAAS,CAACM,aAAa,EAAE,IAAI,EAAE,CAAC,CAAC,CAAC;;MAEpF;MACAZ,OAAO,CAACI,GAAG,CAAC,uCAAuCZ,aAAa,EAAE,CAAC;MAEnE,MAAMI,QAAQ,GAAG,MAAMT,aAAa,CAACmC,IAAI,CAAC,eAAe9B,aAAa,EAAE,EAAEoB,aAAa,CAAC;MACxFZ,OAAO,CAACI,GAAG,CAAC,sBAAsB,EAAER,QAAQ,CAACE,IAAI,CAAC;MAClD,OAAOF,QAAQ,CAACE,IAAI;IACtB,CAAC,CAAC,OAAOC,KAAK,EAAE;MACdC,OAAO,CAACD,KAAK,CAAC,sBAAsB,EAAEA,KAAK,CAAC;MAC5C;MACA,IAAII,QAAQ,EAAE;QACZH,OAAO,CAACD,KAAK,CAAC,wCAAwC,EAAEM,IAAI,CAACC,SAAS,CAACH,QAAQ,EAAE,IAAI,EAAE,CAAC,CAAC,CAAC;MAC5F;;MAEA;MACA,IAAIJ,KAAK,CAACwB,cAAc,IAAIxB,KAAK,CAACyB,cAAc,EAAE;QAChDxB,OAAO,CAACD,KAAK,CAAC,2BAA2B,EAAEA,KAAK,CAAC0B,aAAa,IAAI1B,KAAK,CAAC2B,OAAO,CAAC;QAChF,MAAM;UAAEC,MAAM,EAAE5B,KAAK,CAAC0B,aAAa,IAAI,+EAA+E;UAAEG,MAAM,EAAE,CAAC;UAAEL,cAAc,EAAE;QAAK,CAAC;MAC3J;MAEA,IAAIxB,KAAK,CAACH,QAAQ,EAAE;QAClBI,OAAO,CAACD,KAAK,CAAC,kBAAkB,EAAEA,KAAK,CAACH,QAAQ,CAACE,IAAI,CAAC;QACtDE,OAAO,CAACD,KAAK,CAAC,gBAAgB,EAAEA,KAAK,CAACH,QAAQ,CAACgC,MAAM,CAAC;QACtD5B,OAAO,CAACD,KAAK,CAAC,iBAAiB,EAAEA,KAAK,CAACH,QAAQ,CAACiC,OAAO,CAAC;;QAExD;QACA,MAAMC,WAAW,GAAG/B,KAAK,CAACH,QAAQ,CAACE,IAAI,CAAC6B,MAAM,IAAI,oBAAoB;QACtE,MAAM;UAAEA,MAAM,EAAEG,WAAW;UAAEF,MAAM,EAAE7B,KAAK,CAACH,QAAQ,CAACgC;QAAO,CAAC;MAC9D;MACA;MACA,IAAI7B,KAAK,YAAYJ,KAAK,EAAE;QAC1B,MAAM;UAAEgC,MAAM,EAAE5B,KAAK,CAAC2B,OAAO;UAAEE,MAAM,EAAE;QAAI,CAAC;MAC9C;MACA,MAAM7B,KAAK;IACb;EACF,CAAC;EAED;EACAgC,YAAY,EAAE,MAAAA,CAAOxC,UAAU,EAAEyC,YAAY,EAAE9B,UAAU,KAAK;IAC5D;IACA,IAAIC,QAAQ;IAEZ,IAAI;MACF;MACA,MAAMX,aAAa,GAAGC,QAAQ,CAACF,UAAU,EAAE,EAAE,CAAC;MAC9C,IAAIG,KAAK,CAACF,aAAa,CAAC,EAAE;QACxB,MAAM,IAAIG,KAAK,CAAC,2BAA2BJ,UAAU,EAAE,CAAC;MAC1D;;MAEA;MACA;MACA,IAAI0C,UAAU,GAAGD,YAAY;MAC7B,IAAIA,YAAY,CAACE,QAAQ,CAAC,IAAI,CAAC,EAAE;QAC/BD,UAAU,GAAGD,YAAY,CAACG,KAAK,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;MAC1C;;MAEA;MACA,IAAI,CAACjC,UAAU,CAACK,OAAO,IAAIL,UAAU,CAACK,OAAO,CAACC,IAAI,CAAC,CAAC,KAAK,EAAE,EAAE;QAC3D,MAAM,IAAIb,KAAK,CAAC,0BAA0B,CAAC;MAC7C;MAEA,IAAI,CAACO,UAAU,CAACO,SAAS,IAAIP,UAAU,CAACO,SAAS,CAACD,IAAI,CAAC,CAAC,KAAK,EAAE,EAAE;QAC/D,MAAM,IAAIb,KAAK,CAAC,6BAA6B,CAAC;MAChD;MAEA,IAAI,CAACO,UAAU,CAACQ,YAAY,IAAII,MAAM,CAACZ,UAAU,CAACQ,YAAY,CAAC,CAACF,IAAI,CAAC,CAAC,KAAK,EAAE,EAAE;QAC7E,MAAM,IAAIb,KAAK,CAAC,wCAAwC,CAAC;MAC3D;MAEA,IAAI,CAACO,UAAU,CAACS,OAAO,IAAIG,MAAM,CAACZ,UAAU,CAACS,OAAO,CAAC,CAACH,IAAI,CAAC,CAAC,KAAK,EAAE,EAAE;QACnE,MAAM,IAAIb,KAAK,CAAC,2BAA2B,CAAC;MAC9C;;MAEA;MACA,MAAMiB,aAAa,GAAG;QACpB,GAAGV,UAAU;QACbK,OAAO,EAAEO,MAAM,CAACZ,UAAU,CAACK,OAAO,IAAI,EAAE,CAAC;QACzCE,SAAS,EAAEK,MAAM,CAACZ,UAAU,CAACO,SAAS,IAAI,EAAE,CAAC;QAC7CC,YAAY,EAAEjB,QAAQ,CAACS,UAAU,CAACQ,YAAY,CAAC,IAAI,CAAC;QACpDC,OAAO,EAAEI,UAAU,CAACb,UAAU,CAACS,OAAO,CAAC,IAAI,CAAC;QAC5CK,YAAY,EAAED,UAAU,CAACb,UAAU,CAACc,YAAY,CAAC,IAAI,CAAC;QACtDC,iBAAiB,EAAEH,MAAM,CAACZ,UAAU,CAACe,iBAAiB,IAAI,KAAK,CAAC;QAChEC,SAAS,EAAEJ,MAAM,CAACZ,UAAU,CAACgB,SAAS,IAAI,KAAK,CAAC;QAChDC,KAAK,EAAEL,MAAM,CAACZ,UAAU,CAACiB,KAAK,IAAI,KAAK,CAAC;QACxCC,QAAQ,EAAElB,UAAU,CAACkB,QAAQ,IAAI,IAAI;QACrCC,cAAc,EAAEP,MAAM,CAACZ,UAAU,CAACmB,cAAc,IAAI,GAAG;MACzD,CAAC;;MAED;MACA,IAAIT,aAAa,CAACI,YAAY,IAAI,CAAC,EAAE;QACnC,MAAM,IAAIrB,KAAK,CAAC,+CAA+C,CAAC;MAClE;;MAEA;MACAK,OAAO,CAACI,GAAG,CAAC,2CAA2C,EAAEC,IAAI,CAACC,SAAS,CAACM,aAAa,EAAE,IAAI,EAAE,CAAC,CAAC,CAAC;;MAEhG;MACAT,QAAQ,GAAG;QAAE,GAAGS;MAAc,CAAC;;MAE/B;MACAZ,OAAO,CAACI,GAAG,CAAC,sCAAsCZ,aAAa,IAAIyC,UAAU,EAAE,CAAC;MAEhF,MAAMrC,QAAQ,GAAG,MAAMT,aAAa,CAACiD,GAAG,CAAC,eAAe5C,aAAa,IAAIyC,UAAU,EAAE,EAAErB,aAAa,CAAC;MACrGZ,OAAO,CAACI,GAAG,CAAC,sBAAsB,EAAER,QAAQ,CAACE,IAAI,CAAC;MAClD,OAAOF,QAAQ,CAACE,IAAI;IACtB,CAAC,CAAC,OAAOC,KAAK,EAAE;MACdC,OAAO,CAACD,KAAK,CAAC,sBAAsB,EAAEA,KAAK,CAAC;;MAE5C;MACA,IAAII,QAAQ,EAAE;QACZH,OAAO,CAACD,KAAK,CAAC,wCAAwC,EAAEM,IAAI,CAACC,SAAS,CAACH,QAAQ,EAAE,IAAI,EAAE,CAAC,CAAC,CAAC;MAC5F;;MAEA;MACA,IAAIJ,KAAK,CAACwB,cAAc,IAAIxB,KAAK,CAACyB,cAAc,EAAE;QAChDxB,OAAO,CAACD,KAAK,CAAC,2BAA2B,EAAEA,KAAK,CAAC0B,aAAa,IAAI1B,KAAK,CAAC2B,OAAO,CAAC;QAChF,MAAM;UAAEC,MAAM,EAAE5B,KAAK,CAAC0B,aAAa,IAAI,+EAA+E;UAAEG,MAAM,EAAE,CAAC;UAAEL,cAAc,EAAE;QAAK,CAAC;MAC3J;MAEA,IAAIxB,KAAK,CAACH,QAAQ,EAAE;QAClBI,OAAO,CAACD,KAAK,CAAC,kBAAkB,EAAEA,KAAK,CAACH,QAAQ,CAACE,IAAI,CAAC;QACtDE,OAAO,CAACD,KAAK,CAAC,gBAAgB,EAAEA,KAAK,CAACH,QAAQ,CAACgC,MAAM,CAAC;QACtD5B,OAAO,CAACD,KAAK,CAAC,iBAAiB,EAAEA,KAAK,CAACH,QAAQ,CAACiC,OAAO,CAAC;;QAExD;QACA,MAAMC,WAAW,GAAG/B,KAAK,CAACH,QAAQ,CAACE,IAAI,CAAC6B,MAAM,IAAI,oBAAoB;QACtE,MAAM;UAAEA,MAAM,EAAEG,WAAW;UAAEF,MAAM,EAAE7B,KAAK,CAACH,QAAQ,CAACgC;QAAO,CAAC;MAC9D;MACA;MACA,IAAI7B,KAAK,YAAYJ,KAAK,EAAE;QAC1B,MAAM;UAAEgC,MAAM,EAAE5B,KAAK,CAAC2B,OAAO;UAAEE,MAAM,EAAE;QAAI,CAAC;MAC9C;MACA,MAAM7B,KAAK;IACb;EACF,CAAC;EAED;EACAsC,YAAY,EAAE,MAAAA,CAAO9C,UAAU,EAAEyC,YAAY,KAAK;IAChD,IAAI;MACF;MACA,MAAMxC,aAAa,GAAGC,QAAQ,CAACF,UAAU,EAAE,EAAE,CAAC;MAC9C,IAAIG,KAAK,CAACF,aAAa,CAAC,EAAE;QACxB,MAAM,IAAIG,KAAK,CAAC,2BAA2BJ,UAAU,EAAE,CAAC;MAC1D;;MAEA;MACA;MACA,IAAI0C,UAAU,GAAGD,YAAY;MAC7B,IAAIA,YAAY,CAACE,QAAQ,CAAC,IAAI,CAAC,EAAE;QAC/BD,UAAU,GAAGD,YAAY,CAACG,KAAK,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;MAC1C;MAEA,MAAMvC,QAAQ,GAAG,MAAMT,aAAa,CAACmD,MAAM,CAAC,eAAe9C,aAAa,IAAIyC,UAAU,EAAE,CAAC;;MAEzF;MACAjC,OAAO,CAACI,GAAG,CAAC,+BAA+B,EAAER,QAAQ,CAACE,IAAI,CAAC;;MAE3D;MACA,IAAIF,QAAQ,CAACE,IAAI,CAACyC,cAAc,EAAE;QAChCvC,OAAO,CAACI,GAAG,CAAC,0CAA0Cb,UAAU,8BAA8B,CAAC;QAC/F;QACAiD,YAAY,CAACC,UAAU,CAAC,YAAYlD,UAAU,SAAS,CAAC;MAC1D;MAEA,OAAOK,QAAQ,CAACE,IAAI;IACtB,CAAC,CAAC,OAAOC,KAAK,EAAE;MACdC,OAAO,CAACD,KAAK,CAAC,sBAAsB,EAAEA,KAAK,CAAC;;MAE5C;MACA,IAAIA,KAAK,CAACwB,cAAc,IAAIxB,KAAK,CAACyB,cAAc,EAAE;QAChDxB,OAAO,CAACD,KAAK,CAAC,2BAA2B,EAAEA,KAAK,CAAC0B,aAAa,IAAI1B,KAAK,CAAC2B,OAAO,CAAC;QAChF,MAAM;UAAEC,MAAM,EAAE5B,KAAK,CAAC0B,aAAa,IAAI,+EAA+E;UAAEG,MAAM,EAAE,CAAC;UAAEL,cAAc,EAAE;QAAK,CAAC;MAC3J;MAEA,MAAMxB,KAAK,CAACH,QAAQ,GAAGG,KAAK,CAACH,QAAQ,CAACE,IAAI,GAAGC,KAAK;IACpD;EACF,CAAC;EAED;EACA2C,sBAAsB,EAAE,MAAOnD,UAAU,IAAK;IAC5C,IAAI;MACF;MACA,MAAMC,aAAa,GAAGC,QAAQ,CAACF,UAAU,EAAE,EAAE,CAAC;MAC9C,IAAIG,KAAK,CAACF,aAAa,CAAC,EAAE;QACxB,MAAM,IAAIG,KAAK,CAAC,2BAA2BJ,UAAU,EAAE,CAAC;MAC1D;;MAEA;MACA,MAAMK,QAAQ,GAAG,MAAMT,aAAa,CAACU,GAAG,CAAC,eAAeL,aAAa,qBAAqB,CAAC;MAC3FQ,OAAO,CAACI,GAAG,CAAC,kCAAkC,EAAER,QAAQ,CAACE,IAAI,CAAC;MAC9D,OAAOF,QAAQ,CAACE,IAAI;IACtB,CAAC,CAAC,OAAOC,KAAK,EAAE;MACdC,OAAO,CAACD,KAAK,CAAC,8BAA8B,EAAEA,KAAK,CAAC;;MAEpD;MACA,IAAIA,KAAK,CAACwB,cAAc,IAAIxB,KAAK,CAACyB,cAAc,EAAE;QAChDxB,OAAO,CAACD,KAAK,CAAC,2BAA2B,EAAEA,KAAK,CAAC0B,aAAa,IAAI1B,KAAK,CAAC2B,OAAO,CAAC;QAChF;QACA;QACA,OAAO;UAAEiB,kBAAkB,EAAE,KAAK;UAAEtB,cAAc,EAAE;QAAI,CAAC;MAC3D;MAEA,MAAMtB,KAAK,CAACH,QAAQ,GAAGG,KAAK,CAACH,QAAQ,CAACE,IAAI,GAAGC,KAAK;IACpD;EACF,CAAC;EAED;EACA6C,eAAe,EAAE,MAAOrD,UAAU,IAAK;IACrC,IAAI;MACF;MACA,MAAMK,QAAQ,GAAG,MAAMP,gBAAgB,CAACqD,sBAAsB,CAACnD,UAAU,CAAC;MAC1E,OAAO;QAAE8B,cAAc,EAAEzB,QAAQ,CAACyB,cAAc,IAAI;MAAI,CAAC;IAC3D,CAAC,CAAC,OAAOtB,KAAK,EAAE;MACdC,OAAO,CAACD,KAAK,CAAC,0BAA0B,EAAEA,KAAK,CAAC;MAChD;MACA,OAAO;QAAEsB,cAAc,EAAE;MAAI,CAAC;IAChC;EACF,CAAC;EAED;EACAwB,kBAAkB,EAAE,MAAOtD,UAAU,IAAK;IACxC,IAAI;MACF;MACA,MAAMC,aAAa,GAAGC,QAAQ,CAACF,UAAU,EAAE,EAAE,CAAC;MAC9C,IAAIG,KAAK,CAACF,aAAa,CAAC,EAAE;QACxB,MAAM,IAAIG,KAAK,CAAC,2BAA2BJ,UAAU,EAAE,CAAC;MAC1D;MAEA,MAAMK,QAAQ,GAAG,MAAMT,aAAa,CAACU,GAAG,CAAC,eAAeL,aAAa,UAAU,CAAC;MAChF,OAAOI,QAAQ,CAACE,IAAI;IACtB,CAAC,CAAC,OAAOC,KAAK,EAAE;MACdC,OAAO,CAACD,KAAK,CAAC,6BAA6B,EAAEA,KAAK,CAAC;MACnD,MAAMA,KAAK,CAACH,QAAQ,GAAGG,KAAK,CAACH,QAAQ,CAACE,IAAI,GAAGC,KAAK;IACpD;EACF;AACF,CAAC;AAED,eAAeV,gBAAgB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}