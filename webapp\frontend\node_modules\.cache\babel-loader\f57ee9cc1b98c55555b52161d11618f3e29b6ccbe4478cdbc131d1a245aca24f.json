{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\PycharmProjects\\\\CMS\\\\webapp\\\\frontend\\\\src\\\\components\\\\cavi\\\\ParcoCavi.js\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect } from 'react';\nimport { Box, Typography, Button, Paper, Grid, Card, CardContent, CardActions, Dialog, DialogTitle, DialogContent, DialogActions, TextField, FormControl, InputLabel, Select, MenuItem, List, ListItem, ListItemText, ListItemIcon, ListItemButton, Divider, Alert, CircularProgress, Table, TableBody, TableCell, TableContainer, TableHead, TableRow, FormHelperText } from '@mui/material';\nimport { Add as AddIcon, Edit as EditIcon, Delete as DeleteIcon, History as HistoryIcon, Save as SaveIcon, ViewList as ViewListIcon, Warning as WarningIcon } from '@mui/icons-material';\nimport parcoCaviService from '../../services/parcoCaviService';\nimport ConfigurazioneDialog from './ConfigurazioneDialog';\nimport { validateBobinaData, validateBobinaField, validateBobinaId, isEmpty } from '../../utils/bobinaValidationUtils';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst ParcoCavi = ({\n  cantiereId,\n  onSuccess,\n  onError,\n  initialOption = null\n}) => {\n  _s();\n  const [loading, setLoading] = useState(false);\n  const [bobine, setBobine] = useState([]);\n  const [selectedOption, setSelectedOption] = useState(initialOption);\n  const [openDialog, setOpenDialog] = useState(false);\n  const [dialogType, setDialogType] = useState('');\n  const [selectedBobina, setSelectedBobina] = useState(null);\n  const [formData, setFormData] = useState({\n    numero_bobina: '',\n    utility: '',\n    tipologia: '',\n    n_conduttori: '',\n    sezione: '',\n    metri_totali: '',\n    metri_residui: '',\n    stato_bobina: 'Disponibile',\n    ubicazione_bobina: '',\n    fornitore: '',\n    n_DDT: '',\n    data_DDT: '',\n    configurazione: ''\n  });\n  const [formErrors, setFormErrors] = useState({});\n  const [formWarnings, setFormWarnings] = useState({});\n  const [storicoUtilizzo, setStoricoUtilizzo] = useState([]);\n  const [openConfigDialog, setOpenConfigDialog] = useState(false);\n  const [isFirstInsertion, setIsFirstInsertion] = useState(false);\n\n  // Carica le bobine disponibili\n  const loadBobine = async () => {\n    try {\n      setLoading(true);\n      const data = await parcoCaviService.getBobine(cantiereId);\n      setBobine(data);\n    } catch (error) {\n      onError('Errore nel caricamento delle bobine');\n      console.error('Errore nel caricamento delle bobine:', error);\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  // Carica lo storico utilizzo bobine\n  const loadStoricoUtilizzo = async () => {\n    try {\n      setLoading(true);\n      const data = await parcoCaviService.getStoricoUtilizzo(cantiereId);\n      setStoricoUtilizzo(data);\n    } catch (error) {\n      onError('Errore nel caricamento dello storico utilizzo');\n      console.error('Errore nel caricamento dello storico utilizzo:', error);\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  // Carica i dati all'avvio del componente e gestisce l'opzione iniziale\n  // Utilizziamo una ref per tenere traccia se l'effetto è già stato eseguito\n  const initialLoadDone = React.useRef(false);\n  useEffect(() => {\n    // Esegui solo una volta all'avvio del componente\n    if (!initialLoadDone.current) {\n      console.log('Primo caricamento del componente, initialOption:', initialOption);\n      initialLoadDone.current = true;\n      if (initialOption === 'creaBobina') {\n        console.log('Avvio processo creazione bobina');\n        // IMPORTANTE: Per il primo caricamento con creaBobina, forziamo l'apertura del dialog di configurazione\n        console.log('FORZANDO APERTURA DIALOG CONFIGURAZIONE ALL\\'AVVIO');\n        setIsFirstInsertion(true);\n        setOpenConfigDialog(true);\n      } else if (initialOption) {\n        console.log('Eseguendo handleOptionSelect con:', initialOption);\n        handleOptionSelect(initialOption);\n      } else {\n        console.log('Caricando bobine');\n        loadBobine();\n      }\n    }\n  }, []); // Dipendenze vuote per eseguire solo al mount\n\n  // Verifica se è il primo inserimento di una bobina per un cantiere\n  const checkIfFirstInsertion = async () => {\n    // Variabile per memorizzare la configurazione\n    let configurazione = 's'; // Valore predefinito\n\n    try {\n      // Previene chiamate multiple\n      if (loading) {\n        console.log('Operazione già in corso, uscita');\n        return;\n      }\n\n      // Assicuriamoci che nessun dialog sia aperto\n      setOpenDialog(false);\n      setOpenConfigDialog(false); // Chiudi anche il dialog di configurazione se aperto\n\n      setLoading(true);\n      console.log('Verificando se è il primo inserimento per cantiere ID:', cantiereId);\n\n      // Gestione caso in cui cantiereId non sia valido\n      if (!cantiereId || isNaN(parseInt(cantiereId))) {\n        onError('ID cantiere non valido');\n        console.error('ID cantiere non valido:', cantiereId);\n        setLoading(false);\n        return;\n      }\n\n      // Chiamata API reale\n      let isFirst = false;\n      try {\n        console.log('Verificando se è il primo inserimento per cantiere ID:', cantiereId);\n        const response = await parcoCaviService.isFirstBobinaInsertion(cantiereId);\n        isFirst = response.is_first_insertion;\n        configurazione = response.configurazione || 's';\n        setIsFirstInsertion(isFirst);\n        console.log('È il primo inserimento di una bobina?', isFirst);\n        console.log('Configurazione esistente:', configurazione);\n      } catch (error) {\n        console.error('Errore durante la verifica del primo inserimento:', error);\n        // In caso di errore, assumiamo che non sia il primo inserimento\n        setIsFirstInsertion(false);\n        onError('Errore durante la verifica del primo inserimento. Procedendo con inserimento standard.');\n      }\n      if (isFirst) {\n        // Se è il primo inserimento, mostra il dialog di configurazione\n        console.log('Mostrando il dialog di configurazione');\n        // Assicuriamoci che il dialog di creazione sia chiuso\n        setOpenDialog(false);\n\n        // IMPORTANTE: Forziamo l'apertura del dialog di configurazione\n        console.log('FORZANDO APERTURA DIALOG CONFIGURAZIONE');\n        setOpenConfigDialog(true);\n      } else {\n        // Non è il primo inserimento, procedi con il form normale\n        console.log('Non è il primo inserimento, mostrando il form normale');\n\n        // Ottieni il prossimo numero di bobina\n        let nextBobinaNumber = '1';\n        try {\n          // Ottieni l'ultimo numero di bobina dal backend\n          const bobine = await parcoCaviService.getBobine(cantiereId);\n          if (bobine && bobine.length > 0) {\n            // Trova il numero massimo tra le bobine esistenti\n            const maxNumber = Math.max(...bobine.filter(b => b.numero_bobina && /^\\d+$/.test(b.numero_bobina)).map(b => parseInt(b.numero_bobina, 10)));\n            nextBobinaNumber = isNaN(maxNumber) ? '1' : String(maxNumber + 1);\n          }\n          console.log('Prossimo numero bobina:', nextBobinaNumber);\n        } catch (error) {\n          console.error('Errore nel recupero del prossimo numero bobina:', error);\n          // In caso di errore, usa 1 come default\n          nextBobinaNumber = '1';\n        }\n        setDialogType('creaBobina');\n        setFormData({\n          // In modalità automatica, imposta il numero progressivo\n          // In modalità manuale, lascia vuoto per far inserire all'utente\n          numero_bobina: configurazione === 's' ? nextBobinaNumber : '',\n          utility: '',\n          tipologia: '',\n          n_conduttori: '',\n          sezione: '',\n          metri_totali: '',\n          metri_residui: '',\n          stato_bobina: 'Disponibile',\n          ubicazione_bobina: '',\n          fornitore: '',\n          n_DDT: '',\n          data_DDT: '',\n          configurazione: configurazione // Usa la configurazione esistente\n        });\n        setOpenDialog(true);\n      }\n    } catch (error) {\n      // Gestione dettagliata dell'errore\n      let errorMessage = 'Errore nel controllo dell\\'inserimento della prima bobina';\n      if (error.response) {\n        var _error$response$data;\n        // Errore di risposta dal server\n        errorMessage += `: ${error.response.status} - ${((_error$response$data = error.response.data) === null || _error$response$data === void 0 ? void 0 : _error$response$data.detail) || 'Errore server'}`;\n        console.error('Dettagli errore API:', error.response);\n      } else if (error.request) {\n        // Errore di rete (nessuna risposta ricevuta)\n        errorMessage += ': Errore di connessione al server';\n      } else {\n        // Errore generico\n        errorMessage += `: ${error.message || 'Errore sconosciuto'}`;\n      }\n      onError(errorMessage);\n      console.error('Errore completo:', error);\n\n      // In caso di errore, usa la configurazione di default\n      configurazione = 's'; // Fallback al valore di default\n\n      setDialogType('creaBobina');\n      setFormData({\n        numero_bobina: configurazione === 's' ? '1' : '',\n        utility: '',\n        tipologia: '',\n        n_conduttori: '',\n        sezione: '',\n        metri_totali: '',\n        metri_residui: '',\n        stato_bobina: 'Disponibile',\n        ubicazione_bobina: '',\n        fornitore: '',\n        n_DDT: '',\n        data_DDT: '',\n        configurazione: configurazione // Usa la configurazione esistente o il default\n      });\n      setOpenDialog(true);\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  // Gestisce la conferma della configurazione\n  const handleConfigConfirm = async configValue => {\n    console.log('Configurazione selezionata:', configValue);\n    setLoading(true);\n    try {\n      // Ottieni il prossimo numero di bobina se la configurazione è automatica\n      let nextBobinaNumber = '1';\n      if (configValue === 's') {\n        try {\n          // Ottieni l'ultimo numero di bobina dal backend\n          const bobine = await parcoCaviService.getBobine(cantiereId);\n          if (bobine && bobine.length > 0) {\n            // Trova il numero massimo tra le bobine esistenti\n            const maxNumber = Math.max(...bobine.filter(b => b.numero_bobina && /^\\d+$/.test(b.numero_bobina)).map(b => parseInt(b.numero_bobina, 10)));\n            nextBobinaNumber = isNaN(maxNumber) ? '1' : String(maxNumber + 1);\n          }\n          console.log('Prossimo numero bobina:', nextBobinaNumber);\n        } catch (error) {\n          console.error('Errore nel recupero del prossimo numero bobina:', error);\n          // In caso di errore, usa 1 come default\n          nextBobinaNumber = '1';\n        }\n      }\n\n      // Imposta i valori di default per la bobina\n      const defaultFormData = {\n        // In modalità automatica, imposta il numero progressivo\n        // In modalità manuale, lascia vuoto per far inserire all'utente\n        numero_bobina: configValue === 's' ? nextBobinaNumber : '',\n        utility: '',\n        tipologia: '',\n        n_conduttori: '',\n        sezione: '',\n        metri_totali: '',\n        metri_residui: '',\n        stato_bobina: 'Disponibile',\n        ubicazione_bobina: '',\n        fornitore: '',\n        n_DDT: '',\n        data_DDT: '',\n        configurazione: configValue // Imposta la configurazione scelta\n      };\n      console.log('Impostando i dati del form con configurazione:', configValue, 'numero_bobina:', defaultFormData.numero_bobina);\n\n      // Importante: prima prepara il form, poi chiudi il dialog di configurazione\n      // e solo dopo apri il dialog di creazione\n      setFormData(defaultFormData);\n      setDialogType('creaBobina');\n\n      // Chiudi il dialog di configurazione\n      setOpenConfigDialog(false);\n\n      // Piccolo timeout per assicurarsi che il dialog di configurazione sia chiuso\n      // prima di aprire il dialog di creazione\n      setTimeout(() => {\n        setOpenDialog(true);\n        console.log('Dialog di creazione bobina aperto con numero_bobina:', defaultFormData.numero_bobina);\n      }, 300);\n    } catch (error) {\n      console.error('Errore durante la preparazione del form:', error);\n      onError('Errore durante la preparazione del form: ' + (error.message || 'Errore sconosciuto'));\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  // Gestisce la selezione di un'opzione dal menu\n  const handleOptionSelect = option => {\n    setSelectedOption(option);\n    if (option === 'visualizzaBobine') {\n      loadBobine();\n    } else if (option === 'creaBobina') {\n      checkIfFirstInsertion();\n    } else if (option === 'modificaBobina') {\n      loadBobine();\n      setDialogType('selezionaBobina');\n      setOpenDialog(true);\n    } else if (option === 'eliminaBobina') {\n      loadBobine();\n      setDialogType('eliminaBobina');\n      setOpenDialog(true);\n    } else if (option === 'visualizzaStorico') {\n      loadStoricoUtilizzo();\n      setDialogType('visualizzaStorico');\n      setOpenDialog(true);\n    }\n  };\n\n  // Gestisce la chiusura del dialog\n  const handleCloseDialog = () => {\n    setOpenDialog(false);\n    setSelectedBobina(null);\n    setFormData({\n      numero_bobina: '',\n      utility: '',\n      tipologia: '',\n      n_conduttori: '',\n      sezione: '',\n      metri_totali: '',\n      metri_residui: '',\n      stato_bobina: 'Disponibile',\n      ubicazione_bobina: '',\n      fornitore: '',\n      n_DDT: '',\n      data_DDT: '',\n      configurazione: ''\n    });\n    setFormErrors({});\n    setFormWarnings({});\n  };\n\n  // Gestisce la selezione di una bobina\n  const handleBobinaSelect = bobina => {\n    setSelectedBobina(bobina);\n    if (dialogType === 'selezionaBobina') {\n      setDialogType('modificaBobina');\n      setFormData({\n        numero_bobina: bobina.numero_bobina,\n        utility: bobina.utility || '',\n        tipologia: bobina.tipologia || '',\n        n_conduttori: bobina.n_conduttori || '',\n        sezione: bobina.sezione || '',\n        metri_totali: bobina.metri_totali || '',\n        metri_residui: bobina.metri_residui || '',\n        stato_bobina: bobina.stato_bobina || 'DISPONIBILE',\n        ubicazione_bobina: bobina.ubicazione_bobina || '',\n        fornitore: bobina.fornitore || '',\n        n_DDT: bobina.n_DDT || '',\n        data_DDT: bobina.data_DDT || '',\n        configurazione: bobina.configurazione || ''\n      });\n    }\n  };\n\n  // Gestisce il cambio dei valori nel form con validazione\n  const handleFormChange = e => {\n    const {\n      name,\n      value\n    } = e.target;\n\n    // Aggiorna il valore nel form\n    setFormData({\n      ...formData,\n      [name]: value\n    });\n\n    // Valida il campo\n    if (dialogType === 'creaBobina' || dialogType === 'modificaBobina') {\n      // Validazione speciale per numero_bobina quando configurazione è 'n'\n      if (name === 'numero_bobina' && formData.configurazione === 'n') {\n        const idResult = validateBobinaId(value);\n        if (!idResult.valid) {\n          setFormErrors(prev => ({\n            ...prev,\n            [name]: idResult.message\n          }));\n        } else {\n          setFormErrors(prev => {\n            const newErrors = {\n              ...prev\n            };\n            delete newErrors[name];\n            return newErrors;\n          });\n        }\n        return;\n      }\n      const result = validateBobinaField(name, value);\n\n      // Aggiorna gli errori\n      if (!result.valid) {\n        setFormErrors(prev => ({\n          ...prev,\n          [name]: result.message\n        }));\n      } else {\n        setFormErrors(prev => {\n          const newErrors = {\n            ...prev\n          };\n          delete newErrors[name];\n          return newErrors;\n        });\n\n        // Aggiorna gli avvisi\n        if (result.warning) {\n          setFormWarnings(prev => ({\n            ...prev,\n            [name]: result.message\n          }));\n        } else {\n          setFormWarnings(prev => {\n            const newWarnings = {\n              ...prev\n            };\n            delete newWarnings[name];\n            return newWarnings;\n          });\n        }\n      }\n    }\n  };\n\n  // Gestisce il salvataggio del form\n  const handleSave = async () => {\n    try {\n      // Validazione completa dei dati prima del salvataggio\n      if (dialogType === 'creaBobina' || dialogType === 'modificaBobina') {\n        const validation = validateBobinaData(formData);\n        if (!validation.isValid) {\n          setFormErrors(validation.errors);\n          setFormWarnings(validation.warnings);\n          onError('Correggi gli errori nel form prima di salvare');\n          return;\n        }\n\n        // Verifica aggiuntiva per il campo numero_bobina quando la configurazione è manuale\n        if (formData.configurazione === 'n' && (!formData.numero_bobina || formData.numero_bobina.trim() === '')) {\n          setFormErrors({\n            ...formErrors,\n            numero_bobina: 'L\\'ID della bobina è obbligatorio'\n          });\n          onError('L\\'ID della bobina è obbligatorio');\n          return;\n        }\n      }\n      setLoading(true);\n      console.log('Salvataggio dati bobina in corso...');\n      if (dialogType === 'creaBobina') {\n        // Prepara i dati per la creazione della bobina\n        const bobinaData = {\n          ...formData,\n          // Assicurati che tutti i campi siano nel formato corretto\n          numero_bobina: String(formData.numero_bobina || ''),\n          utility: String(formData.utility || ''),\n          tipologia: String(formData.tipologia || ''),\n          n_conduttori: String(formData.n_conduttori || ''),\n          sezione: String(formData.sezione || ''),\n          metri_totali: parseFloat(formData.metri_totali) || 0,\n          ubicazione_bobina: String(formData.ubicazione_bobina || 'TBD'),\n          fornitore: String(formData.fornitore || 'TBD'),\n          n_DDT: String(formData.n_DDT || 'TBD'),\n          data_DDT: formData.data_DDT || null,\n          // Assicurati che la configurazione sia impostata correttamente\n          configurazione: String(formData.configurazione || 's')\n        };\n        console.log('Dati bobina da inviare:', bobinaData);\n        try {\n          // Log dei dati che stiamo per inviare\n          console.log('Invio dati bobina al backend:', JSON.stringify(bobinaData, null, 2));\n\n          // Invia i dati al backend\n          await parcoCaviService.createBobina(cantiereId, bobinaData);\n          onSuccess('Bobina creata con successo');\n\n          // Chiudi il dialog\n          handleCloseDialog();\n\n          // Imposta l'opzione selezionata a visualizzaBobine e carica le bobine\n          setSelectedOption('visualizzaBobine');\n          loadBobine();\n\n          // Notifica al componente padre che dovrebbe reindirizzare alla pagina di visualizzazione bobine\n          if (typeof window !== 'undefined') {\n            // Usa un evento personalizzato per comunicare con il componente padre\n            const event = new CustomEvent('redirectToVisualizzaBobine', {\n              detail: {\n                cantiereId\n              }\n            });\n            window.dispatchEvent(event);\n          }\n        } catch (error) {\n          console.error('Errore durante la creazione della bobina:', error);\n\n          // Gestione dettagliata dell'errore\n          let errorMessage = 'Errore durante la creazione della bobina';\n          if (error.detail) {\n            errorMessage = error.detail;\n          } else if (error.message) {\n            errorMessage = error.message;\n          }\n\n          // Log dettagliato dell'errore\n          console.error('Dettagli errore:', errorMessage);\n          console.error('Dati inviati:', JSON.stringify(bobinaData, null, 2));\n          onError(errorMessage);\n          setLoading(false);\n          return;\n        }\n      } else if (dialogType === 'modificaBobina') {\n        // Per la modifica, usa l'ID bobina completo o il numero bobina\n        const bobinaId = selectedBobina.id_bobina || formData.numero_bobina;\n        await parcoCaviService.updateBobina(cantiereId, bobinaId, formData);\n        onSuccess('Bobina modificata con successo');\n      } else if (dialogType === 'eliminaBobina') {\n        // Per l'eliminazione, usa l'ID bobina completo\n        const bobinaId = selectedBobina.id_bobina || selectedBobina.numero_bobina;\n        await parcoCaviService.deleteBobina(cantiereId, bobinaId);\n        onSuccess('Bobina eliminata con successo');\n      }\n      handleCloseDialog();\n      loadBobine(); // Ricarica le bobine dopo l'operazione\n    } catch (error) {\n      onError('Errore durante l\\'operazione: ' + (error.detail || error.message || 'Errore sconosciuto'));\n      console.error('Errore durante l\\'operazione:', error);\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  // Renderizza le bobine in formato lista\n  const renderBobineCards = () => {\n    if (bobine.length === 0) {\n      return /*#__PURE__*/_jsxDEV(Alert, {\n        severity: \"info\",\n        children: \"Nessuna bobina disponibile\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 592,\n        columnNumber: 9\n      }, this);\n    }\n    return /*#__PURE__*/_jsxDEV(TableContainer, {\n      component: Paper,\n      sx: {\n        mt: 2\n      },\n      children: /*#__PURE__*/_jsxDEV(Table, {\n        size: \"small\",\n        children: [/*#__PURE__*/_jsxDEV(TableHead, {\n          children: /*#__PURE__*/_jsxDEV(TableRow, {\n            sx: {\n              bgcolor: '#f5f5f5'\n            },\n            children: [/*#__PURE__*/_jsxDEV(TableCell, {\n              children: /*#__PURE__*/_jsxDEV(\"strong\", {\n                children: \"ID Bobina\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 601,\n                columnNumber: 26\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 601,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n              children: /*#__PURE__*/_jsxDEV(\"strong\", {\n                children: \"Utility\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 602,\n                columnNumber: 26\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 602,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n              children: /*#__PURE__*/_jsxDEV(\"strong\", {\n                children: \"Tipologia\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 603,\n                columnNumber: 26\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 603,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n              children: /*#__PURE__*/_jsxDEV(\"strong\", {\n                children: \"N\\xB0 Cond.\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 604,\n                columnNumber: 26\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 604,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n              children: /*#__PURE__*/_jsxDEV(\"strong\", {\n                children: \"Sezione\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 605,\n                columnNumber: 26\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 605,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n              children: /*#__PURE__*/_jsxDEV(\"strong\", {\n                children: \"Metri Tot.\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 606,\n                columnNumber: 26\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 606,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n              children: /*#__PURE__*/_jsxDEV(\"strong\", {\n                children: \"Metri Res.\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 607,\n                columnNumber: 26\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 607,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n              children: /*#__PURE__*/_jsxDEV(\"strong\", {\n                children: \"Stato\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 608,\n                columnNumber: 26\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 608,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n              children: /*#__PURE__*/_jsxDEV(\"strong\", {\n                children: \"Ubicazione\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 609,\n                columnNumber: 26\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 609,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n              children: /*#__PURE__*/_jsxDEV(\"strong\", {\n                children: \"Azioni\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 610,\n                columnNumber: 26\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 610,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 600,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 599,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(TableBody, {\n          children: bobine.map(bobina => /*#__PURE__*/_jsxDEV(TableRow, {\n            hover: true,\n            children: [/*#__PURE__*/_jsxDEV(TableCell, {\n              children: bobina.numero_bobina\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 616,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n              children: bobina.utility || 'N/A'\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 617,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n              children: bobina.tipologia || 'N/A'\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 618,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n              children: bobina.n_conduttori || 'N/A'\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 619,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n              children: bobina.sezione || 'N/A'\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 620,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n              children: [bobina.metri_totali || 'N/A', \" m\"]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 621,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n              children: [bobina.metri_residui || 'N/A', \" m\"]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 622,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n              children: /*#__PURE__*/_jsxDEV(Box, {\n                component: \"span\",\n                sx: {\n                  px: 1,\n                  py: 0.5,\n                  borderRadius: 1,\n                  bgcolor: bobina.stato_bobina === 'Disponibile' ? 'success.light' : bobina.stato_bobina === 'In Uso' ? 'warning.light' : 'error.light',\n                  color: 'white',\n                  fontSize: '0.75rem',\n                  fontWeight: 'bold'\n                },\n                children: bobina.stato_bobina || 'DISPONIBILE'\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 624,\n                columnNumber: 19\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 623,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n              children: bobina.ubicazione_bobina || 'N/A'\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 640,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n              children: /*#__PURE__*/_jsxDEV(Box, {\n                sx: {\n                  display: 'flex',\n                  gap: 1\n                },\n                children: [/*#__PURE__*/_jsxDEV(IconButton, {\n                  size: \"small\",\n                  color: \"primary\",\n                  onClick: () => {\n                    setDialogType('selezionaBobina');\n                    handleBobinaSelect(bobina);\n                  },\n                  children: /*#__PURE__*/_jsxDEV(EditIcon, {\n                    fontSize: \"small\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 651,\n                    columnNumber: 23\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 643,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(IconButton, {\n                  size: \"small\",\n                  color: \"error\",\n                  onClick: () => {\n                    setDialogType('eliminaBobina');\n                    setSelectedBobina(bobina);\n                    setOpenDialog(true);\n                  },\n                  children: /*#__PURE__*/_jsxDEV(DeleteIcon, {\n                    fontSize: \"small\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 662,\n                    columnNumber: 23\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 653,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 642,\n                columnNumber: 19\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 641,\n              columnNumber: 17\n            }, this)]\n          }, bobina.numero_bobina, true, {\n            fileName: _jsxFileName,\n            lineNumber: 615,\n            columnNumber: 15\n          }, this))\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 613,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 598,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 597,\n      columnNumber: 7\n    }, this);\n  };\n\n  // Renderizza il dialog in base al tipo\n  const renderDialog = () => {\n    if (dialogType === 'creaBobina' || dialogType === 'modificaBobina') {\n      return /*#__PURE__*/_jsxDEV(Dialog, {\n        open: openDialog,\n        onClose: handleCloseDialog,\n        maxWidth: \"md\",\n        fullWidth: true,\n        children: [/*#__PURE__*/_jsxDEV(DialogTitle, {\n          children: dialogType === 'creaBobina' ? 'Crea Nuova Bobina' : 'Modifica Bobina'\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 679,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(DialogContent, {\n          children: [Object.keys(formWarnings).length > 0 && /*#__PURE__*/_jsxDEV(Alert, {\n            severity: \"warning\",\n            sx: {\n              mb: 2,\n              mt: 1\n            },\n            children: [/*#__PURE__*/_jsxDEV(Typography, {\n              variant: \"subtitle2\",\n              children: \"Attenzione:\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 685,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"ul\", {\n              style: {\n                margin: 0,\n                paddingLeft: '20px'\n              },\n              children: Object.values(formWarnings).map((warning, index) => /*#__PURE__*/_jsxDEV(\"li\", {\n                children: warning\n              }, index, false, {\n                fileName: _jsxFileName,\n                lineNumber: 688,\n                columnNumber: 21\n              }, this))\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 686,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 684,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(Grid, {\n            container: true,\n            spacing: 2,\n            sx: {\n              mt: 1\n            },\n            children: [/*#__PURE__*/_jsxDEV(Grid, {\n              item: true,\n              xs: 12,\n              sm: 6,\n              children: /*#__PURE__*/_jsxDEV(TextField, {\n                name: \"numero_bobina\",\n                label: \"ID Bobina\",\n                fullWidth: true,\n                variant: \"outlined\",\n                value: formData.numero_bobina,\n                onChange: handleFormChange,\n                disabled: dialogType === 'modificaBobina' || dialogType === 'creaBobina' && formData.configurazione === 's',\n                required: true,\n                error: !!formErrors.numero_bobina,\n                InputProps: {\n                  sx: {\n                    bgcolor: formData.configurazione === 's' ? '#f5f5f5' : 'transparent',\n                    fontWeight: 'bold'\n                  }\n                },\n                helperText: formErrors.numero_bobina || (dialogType === 'creaBobina' && formData.configurazione === 's' ? `ID completo: C${cantiereId}_B${formData.numero_bobina || ''} (generato automaticamente)` : dialogType === 'creaBobina' && formData.configurazione === 'n' ? `Inserisci l'ID della bobina (es. A123). ID completo sarà: C${cantiereId}_B{tuo input}` : ''),\n                type: formData.configurazione === 's' ? \"text\" : \"text\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 696,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 695,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Grid, {\n              item: true,\n              xs: 12,\n              sm: 6,\n              children: /*#__PURE__*/_jsxDEV(TextField, {\n                name: \"utility\",\n                label: \"Utility\",\n                fullWidth: true,\n                variant: \"outlined\",\n                value: formData.utility,\n                onChange: handleFormChange,\n                required: true,\n                error: !!formErrors.utility,\n                helperText: formErrors.utility || ''\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 724,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 723,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Grid, {\n              item: true,\n              xs: 12,\n              sm: 6,\n              children: /*#__PURE__*/_jsxDEV(TextField, {\n                name: \"tipologia\",\n                label: \"Tipologia\",\n                fullWidth: true,\n                variant: \"outlined\",\n                value: formData.tipologia,\n                onChange: handleFormChange,\n                required: true,\n                error: !!formErrors.tipologia,\n                helperText: formErrors.tipologia || ''\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 737,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 736,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Grid, {\n              item: true,\n              xs: 12,\n              sm: 6,\n              children: /*#__PURE__*/_jsxDEV(TextField, {\n                name: \"n_conduttori\",\n                label: \"N\\xB0 Conduttori\",\n                fullWidth: true,\n                variant: \"outlined\",\n                value: formData.n_conduttori,\n                onChange: handleFormChange,\n                required: true,\n                error: !!formErrors.n_conduttori,\n                helperText: formErrors.n_conduttori || ''\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 750,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 749,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Grid, {\n              item: true,\n              xs: 12,\n              sm: 6,\n              children: /*#__PURE__*/_jsxDEV(TextField, {\n                name: \"sezione\",\n                label: \"Sezione\",\n                fullWidth: true,\n                variant: \"outlined\",\n                value: formData.sezione,\n                onChange: handleFormChange,\n                required: true,\n                error: !!formErrors.sezione,\n                helperText: formErrors.sezione || ''\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 763,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 762,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Grid, {\n              item: true,\n              xs: 12,\n              sm: 6,\n              children: /*#__PURE__*/_jsxDEV(TextField, {\n                name: \"metri_totali\",\n                label: \"Metri Totali\",\n                type: \"number\",\n                fullWidth: true,\n                variant: \"outlined\",\n                value: formData.metri_totali,\n                onChange: handleFormChange,\n                required: true,\n                error: !!formErrors.metri_totali,\n                helperText: formErrors.metri_totali || ''\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 776,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 775,\n              columnNumber: 15\n            }, this), dialogType === 'modificaBobina' && /*#__PURE__*/_jsxDEV(Grid, {\n              item: true,\n              xs: 12,\n              sm: 6,\n              children: /*#__PURE__*/_jsxDEV(TextField, {\n                name: \"metri_residui\",\n                label: \"Metri Residui\",\n                type: \"number\",\n                fullWidth: true,\n                variant: \"outlined\",\n                value: formData.metri_residui,\n                onChange: handleFormChange,\n                required: true,\n                disabled: true,\n                helperText: \"I metri residui non possono essere modificati direttamente\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 791,\n                columnNumber: 19\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 790,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(Grid, {\n              item: true,\n              xs: 12,\n              sm: 6,\n              children: /*#__PURE__*/_jsxDEV(FormControl, {\n                fullWidth: true,\n                children: [/*#__PURE__*/_jsxDEV(InputLabel, {\n                  id: \"stato-bobina-label\",\n                  children: \"Stato Bobina\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 807,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(Select, {\n                  labelId: \"stato-bobina-label\",\n                  name: \"stato_bobina\",\n                  value: formData.stato_bobina,\n                  label: \"Stato Bobina\",\n                  onChange: handleFormChange,\n                  disabled: dialogType === 'creaBobina',\n                  children: [/*#__PURE__*/_jsxDEV(MenuItem, {\n                    value: \"Disponibile\",\n                    children: \"Disponibile\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 816,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(MenuItem, {\n                    value: \"In uso\",\n                    children: \"In uso\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 817,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(MenuItem, {\n                    value: \"Terminata\",\n                    children: \"Terminata\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 818,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(MenuItem, {\n                    value: \"Danneggiata\",\n                    children: \"Danneggiata\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 819,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(MenuItem, {\n                    value: \"Over\",\n                    children: \"Over\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 820,\n                    columnNumber: 21\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 808,\n                  columnNumber: 19\n                }, this), dialogType === 'creaBobina' && /*#__PURE__*/_jsxDEV(FormHelperText, {\n                  children: \"Per una nuova bobina, lo stato \\xE8 sempre \\\"Disponibile\\\"\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 823,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 806,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 805,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Grid, {\n              item: true,\n              xs: 12,\n              sm: 6,\n              children: /*#__PURE__*/_jsxDEV(TextField, {\n                name: \"ubicazione_bobina\",\n                label: \"Ubicazione Bobina\",\n                fullWidth: true,\n                variant: \"outlined\",\n                value: formData.ubicazione_bobina,\n                onChange: handleFormChange,\n                error: !!formErrors.ubicazione_bobina,\n                helperText: formErrors.ubicazione_bobina || ''\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 828,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 827,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Grid, {\n              item: true,\n              xs: 12,\n              sm: 6,\n              children: /*#__PURE__*/_jsxDEV(TextField, {\n                name: \"fornitore\",\n                label: \"Fornitore\",\n                fullWidth: true,\n                variant: \"outlined\",\n                value: formData.fornitore,\n                onChange: handleFormChange,\n                error: !!formErrors.fornitore,\n                helperText: formErrors.fornitore || ''\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 840,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 839,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Grid, {\n              item: true,\n              xs: 12,\n              sm: 6,\n              children: /*#__PURE__*/_jsxDEV(TextField, {\n                name: \"n_DDT\",\n                label: \"Numero DDT\",\n                fullWidth: true,\n                variant: \"outlined\",\n                value: formData.n_DDT,\n                onChange: handleFormChange,\n                error: !!formErrors.n_DDT,\n                helperText: formErrors.n_DDT || ''\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 852,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 851,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Grid, {\n              item: true,\n              xs: 12,\n              sm: 6,\n              children: /*#__PURE__*/_jsxDEV(TextField, {\n                name: \"data_DDT\",\n                label: \"Data DDT (YYYY-MM-DD)\",\n                fullWidth: true,\n                variant: \"outlined\",\n                value: formData.data_DDT,\n                onChange: handleFormChange,\n                placeholder: \"YYYY-MM-DD\",\n                error: !!formErrors.data_DDT,\n                helperText: formErrors.data_DDT || ''\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 864,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 863,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Grid, {\n              item: true,\n              xs: 12,\n              sm: 6,\n              children: /*#__PURE__*/_jsxDEV(TextField, {\n                name: \"configurazione\",\n                label: \"Modalit\\xE0 Numerazione\",\n                fullWidth: true,\n                variant: \"outlined\",\n                value: formData.configurazione === 's' ? 'Automatica' : 'Manuale',\n                InputProps: {\n                  readOnly: true,\n                  sx: {\n                    bgcolor: '#f5f5f5'\n                  }\n                },\n                helperText: /*#__PURE__*/_jsxDEV(Box, {\n                  sx: {\n                    fontWeight: 'medium',\n                    color: formData.configurazione === 's' ? 'success.main' : 'info.main'\n                  },\n                  children: formData.configurazione === 's' ? 'Numerazione progressiva automatica (1, 2, 3, ...)' : 'Inserimento manuale dell\\'ID bobina (es. A123, SPEC01, ...)'\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 890,\n                  columnNumber: 21\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 877,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 876,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 694,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 682,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(DialogActions, {\n          children: [/*#__PURE__*/_jsxDEV(Button, {\n            onClick: handleCloseDialog,\n            children: \"Annulla\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 901,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Button, {\n            onClick: handleSave,\n            disabled: loading || Object.keys(formErrors).length > 0,\n            startIcon: loading ? /*#__PURE__*/_jsxDEV(CircularProgress, {\n              size: 20\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 905,\n              columnNumber: 36\n            }, this) : /*#__PURE__*/_jsxDEV(SaveIcon, {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 905,\n              columnNumber: 69\n            }, this),\n            variant: \"contained\",\n            color: \"primary\",\n            children: \"Salva\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 902,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 900,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 678,\n        columnNumber: 9\n      }, this);\n    } else if (dialogType === 'selezionaBobina') {\n      return /*#__PURE__*/_jsxDEV(Dialog, {\n        open: openDialog,\n        onClose: handleCloseDialog,\n        maxWidth: \"md\",\n        fullWidth: true,\n        children: [/*#__PURE__*/_jsxDEV(DialogTitle, {\n          children: \"Seleziona Bobina da Modificare\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 917,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(DialogContent, {\n          children: loading ? /*#__PURE__*/_jsxDEV(CircularProgress, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 920,\n            columnNumber: 15\n          }, this) : bobine.length === 0 ? /*#__PURE__*/_jsxDEV(Alert, {\n            severity: \"info\",\n            children: \"Nessuna bobina disponibile\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 922,\n            columnNumber: 15\n          }, this) : /*#__PURE__*/_jsxDEV(List, {\n            children: bobine.map(bobina => /*#__PURE__*/_jsxDEV(ListItem, {\n              button: true,\n              onClick: () => handleBobinaSelect(bobina),\n              children: /*#__PURE__*/_jsxDEV(ListItemText, {\n                primary: `Bobina: ${bobina.numero_bobina}`,\n                secondary: `Tipologia: ${bobina.tipologia || 'N/A'} - Utility: ${bobina.utility || 'N/A'} - Residuo: ${bobina.metri_residui || 'N/A'} m`\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 931,\n                columnNumber: 21\n              }, this)\n            }, bobina.numero_bobina, false, {\n              fileName: _jsxFileName,\n              lineNumber: 926,\n              columnNumber: 19\n            }, this))\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 924,\n            columnNumber: 15\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 918,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(DialogActions, {\n          children: /*#__PURE__*/_jsxDEV(Button, {\n            onClick: handleCloseDialog,\n            children: \"Annulla\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 941,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 940,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 916,\n        columnNumber: 9\n      }, this);\n    } else if (dialogType === 'eliminaBobina') {\n      return /*#__PURE__*/_jsxDEV(Dialog, {\n        open: openDialog,\n        onClose: handleCloseDialog,\n        maxWidth: \"sm\",\n        fullWidth: true,\n        children: [/*#__PURE__*/_jsxDEV(DialogTitle, {\n          children: \"Elimina Bobina\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 948,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(DialogContent, {\n          children: !selectedBobina ? loading ? /*#__PURE__*/_jsxDEV(CircularProgress, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 952,\n            columnNumber: 17\n          }, this) : bobine.length === 0 ? /*#__PURE__*/_jsxDEV(Alert, {\n            severity: \"info\",\n            children: \"Nessuna bobina disponibile\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 954,\n            columnNumber: 17\n          }, this) : /*#__PURE__*/_jsxDEV(List, {\n            children: bobine.map(bobina => /*#__PURE__*/_jsxDEV(ListItem, {\n              button: true,\n              onClick: () => setSelectedBobina(bobina),\n              children: /*#__PURE__*/_jsxDEV(ListItemText, {\n                primary: `Bobina: ${bobina.numero_bobina}`,\n                secondary: `Tipologia: ${bobina.tipologia || 'N/A'} - Utility: ${bobina.utility || 'N/A'} - Residuo: ${bobina.metri_residui || 'N/A'} m`\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 963,\n                columnNumber: 23\n              }, this)\n            }, bobina.numero_bobina, false, {\n              fileName: _jsxFileName,\n              lineNumber: 958,\n              columnNumber: 21\n            }, this))\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 956,\n            columnNumber: 17\n          }, this) : /*#__PURE__*/_jsxDEV(Box, {\n            children: [/*#__PURE__*/_jsxDEV(Alert, {\n              severity: \"warning\",\n              sx: {\n                mb: 2\n              },\n              children: [\"Sei sicuro di voler eliminare la bobina \", selectedBobina.numero_bobina, \"?\"]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 973,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(Typography, {\n              variant: \"body1\",\n              children: \"Questa operazione non pu\\xF2 essere annullata.\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 976,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 972,\n            columnNumber: 15\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 949,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(DialogActions, {\n          children: [/*#__PURE__*/_jsxDEV(Button, {\n            onClick: handleCloseDialog,\n            children: \"Annulla\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 983,\n            columnNumber: 13\n          }, this), selectedBobina && /*#__PURE__*/_jsxDEV(Button, {\n            onClick: handleSave,\n            disabled: loading,\n            color: \"error\",\n            startIcon: loading ? /*#__PURE__*/_jsxDEV(CircularProgress, {\n              size: 20\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 989,\n              columnNumber: 38\n            }, this) : /*#__PURE__*/_jsxDEV(DeleteIcon, {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 989,\n              columnNumber: 71\n            }, this),\n            children: \"Elimina\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 985,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 982,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 947,\n        columnNumber: 9\n      }, this);\n    } else if (dialogType === 'visualizzaStorico') {\n      return /*#__PURE__*/_jsxDEV(Dialog, {\n        open: openDialog,\n        onClose: handleCloseDialog,\n        maxWidth: \"lg\",\n        fullWidth: true,\n        children: [/*#__PURE__*/_jsxDEV(DialogTitle, {\n          children: \"Storico Utilizzo Bobine\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 1000,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(DialogContent, {\n          children: loading ? /*#__PURE__*/_jsxDEV(CircularProgress, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 1003,\n            columnNumber: 15\n          }, this) : storicoUtilizzo.length === 0 ? /*#__PURE__*/_jsxDEV(Alert, {\n            severity: \"info\",\n            children: \"Nessun dato storico disponibile\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 1005,\n            columnNumber: 15\n          }, this) : /*#__PURE__*/_jsxDEV(TableContainer, {\n            component: Paper,\n            sx: {\n              mt: 2\n            },\n            children: /*#__PURE__*/_jsxDEV(Table, {\n              size: \"small\",\n              children: [/*#__PURE__*/_jsxDEV(TableHead, {\n                children: /*#__PURE__*/_jsxDEV(TableRow, {\n                  children: [/*#__PURE__*/_jsxDEV(TableCell, {\n                    children: \"Bobina\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1011,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n                    children: \"Utility\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1012,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n                    children: \"Tipologia\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1013,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n                    children: \"N\\xB0 Conduttori\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1014,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n                    children: \"Sezione\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1015,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n                    children: \"Metri Totali\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1016,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n                    children: \"Metri Residui\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1017,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n                    children: \"Cavi Associati\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1018,\n                    columnNumber: 23\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1010,\n                  columnNumber: 21\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1009,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(TableBody, {\n                children: storicoUtilizzo.map((record, index) => /*#__PURE__*/_jsxDEV(TableRow, {\n                  children: [/*#__PURE__*/_jsxDEV(TableCell, {\n                    children: record.numero_bobina\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1024,\n                    columnNumber: 25\n                  }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n                    children: record.utility\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1025,\n                    columnNumber: 25\n                  }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n                    children: record.tipologia\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1026,\n                    columnNumber: 25\n                  }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n                    children: record.n_conduttori\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1027,\n                    columnNumber: 25\n                  }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n                    children: record.sezione\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1028,\n                    columnNumber: 25\n                  }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n                    children: record.metri_totali\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1029,\n                    columnNumber: 25\n                  }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n                    children: record.metri_residui\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1030,\n                    columnNumber: 25\n                  }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n                    children: record.cavi.length\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1031,\n                    columnNumber: 25\n                  }, this)]\n                }, index, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1023,\n                  columnNumber: 23\n                }, this))\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1021,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 1008,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 1007,\n            columnNumber: 15\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 1001,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(DialogActions, {\n          children: /*#__PURE__*/_jsxDEV(Button, {\n            onClick: handleCloseDialog,\n            children: \"Chiudi\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 1040,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 1039,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 999,\n        columnNumber: 9\n      }, this);\n    }\n    return null;\n  };\n  return /*#__PURE__*/_jsxDEV(Box, {\n    children: [selectedOption === 'visualizzaBobine' && !openDialog ? /*#__PURE__*/_jsxDEV(Paper, {\n      sx: {\n        p: 3\n      },\n      children: [/*#__PURE__*/_jsxDEV(Box, {\n        sx: {\n          display: 'flex',\n          justifyContent: 'flex-end',\n          alignItems: 'center',\n          mb: 2\n        },\n        children: /*#__PURE__*/_jsxDEV(Box, {\n          sx: {\n            display: 'flex',\n            gap: 2\n          },\n          children: [/*#__PURE__*/_jsxDEV(Button, {\n            variant: \"outlined\",\n            color: \"inherit\",\n            startIcon: /*#__PURE__*/_jsxDEV(HistoryIcon, {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1058,\n              columnNumber: 28\n            }, this),\n            onClick: () => {\n              loadStoricoUtilizzo();\n              setDialogType('visualizzaStorico');\n              setOpenDialog(true);\n            },\n            sx: {\n              fontWeight: 'medium',\n              borderRadius: 0\n            },\n            children: \"Storico Utilizzo\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 1055,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(Button, {\n            variant: \"contained\",\n            color: \"inherit\",\n            startIcon: /*#__PURE__*/_jsxDEV(AddIcon, {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1074,\n              columnNumber: 28\n            }, this),\n            onClick: () => checkIfFirstInsertion(),\n            sx: {\n              fontWeight: 'medium',\n              borderRadius: 0\n            },\n            children: \"Crea Nuova Bobina\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 1071,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 1054,\n          columnNumber: 13\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 1053,\n        columnNumber: 11\n      }, this), loading ? /*#__PURE__*/_jsxDEV(Box, {\n        sx: {\n          display: 'flex',\n          justifyContent: 'center',\n          my: 4\n        },\n        children: /*#__PURE__*/_jsxDEV(CircularProgress, {}, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 1087,\n          columnNumber: 15\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 1086,\n        columnNumber: 13\n      }, this) : renderBobineCards()]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 1052,\n      columnNumber: 9\n    }, this) : !openDialog ? /*#__PURE__*/_jsxDEV(Paper, {\n      sx: {\n        p: 3,\n        minHeight: '300px',\n        display: 'flex',\n        alignItems: 'center',\n        justifyContent: 'center'\n      },\n      children: !selectedOption ? /*#__PURE__*/_jsxDEV(Typography, {\n        variant: \"body1\",\n        children: \"Seleziona un'opzione dal menu principale per iniziare.\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 1096,\n        columnNumber: 13\n      }, this) : /*#__PURE__*/_jsxDEV(Box, {\n        sx: {\n          textAlign: 'center'\n        },\n        children: [/*#__PURE__*/_jsxDEV(Typography, {\n          variant: \"h6\",\n          gutterBottom: true,\n          children: [selectedOption === 'creaBobina' && 'Crea Nuova Bobina', selectedOption === 'modificaBobina' && 'Modifica Bobina', selectedOption === 'eliminaBobina' && 'Elimina Bobina', selectedOption === 'visualizzaStorico' && 'Visualizza Storico Utilizzo']\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 1101,\n          columnNumber: 15\n        }, this), /*#__PURE__*/_jsxDEV(CircularProgress, {\n          sx: {\n            mt: 2\n          }\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 1107,\n          columnNumber: 15\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 1100,\n        columnNumber: 13\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 1094,\n      columnNumber: 9\n    }, this) : null, renderDialog(), /*#__PURE__*/_jsxDEV(ConfigurazioneDialog, {\n      open: openConfigDialog,\n      onClose: () => setOpenConfigDialog(false),\n      onConfirm: handleConfigConfirm\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 1116,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 1050,\n    columnNumber: 5\n  }, this);\n};\n_s(ParcoCavi, \"aPVvt0JmyP4/WtJb67LlF7JuBzs=\");\n_c = ParcoCavi;\nexport default ParcoCavi;\nvar _c;\n$RefreshReg$(_c, \"ParcoCavi\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "Box", "Typography", "<PERSON><PERSON>", "Paper", "Grid", "Card", "<PERSON><PERSON><PERSON><PERSON>", "CardActions", "Dialog", "DialogTitle", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "DialogActions", "TextField", "FormControl", "InputLabel", "Select", "MenuItem", "List", "ListItem", "ListItemText", "ListItemIcon", "ListItemButton", "Divider", "<PERSON><PERSON>", "CircularProgress", "Table", "TableBody", "TableCell", "TableContainer", "TableHead", "TableRow", "FormHelperText", "Add", "AddIcon", "Edit", "EditIcon", "Delete", "DeleteIcon", "History", "HistoryIcon", "Save", "SaveIcon", "ViewList", "ViewListIcon", "Warning", "WarningIcon", "parcoCaviService", "ConfigurazioneDialog", "validateBobinaData", "validateBob<PERSON>F<PERSON>", "validateBobinaId", "isEmpty", "jsxDEV", "_jsxDEV", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "cantiereId", "onSuccess", "onError", "initialOption", "_s", "loading", "setLoading", "bobine", "set<PERSON>ob<PERSON>", "selectedOption", "setSelectedOption", "openDialog", "setOpenDialog", "dialogType", "setDialogType", "<PERSON><PERSON><PERSON><PERSON>", "setSelectedBobina", "formData", "setFormData", "numero_bobina", "utility", "tipologia", "n_conduttori", "sezione", "metri_totali", "metri_residui", "stato_bobina", "ubicazione_bobina", "fornitore", "n_DDT", "data_DDT", "configurazione", "formErrors", "setFormErrors", "formWarnings", "setForm<PERSON><PERSON>nings", "storico<PERSON><PERSON><PERSON><PERSON>", "setStoricoUtilizzo", "openConfigDialog", "setOpenConfigDialog", "isFirstInsertion", "setIsFirstInsertion", "loadBobine", "data", "getBobine", "error", "console", "loadStoricoUtilizzo", "getStoricoUtilizzo", "initialLoadDone", "useRef", "current", "log", "handleOptionSelect", "checkIfFirstInsertion", "isNaN", "parseInt", "<PERSON><PERSON><PERSON><PERSON>", "response", "isFirstBobinaInsertion", "is_first_insertion", "nextBobinaNumber", "length", "maxNumber", "Math", "max", "filter", "b", "test", "map", "String", "errorMessage", "_error$response$data", "status", "detail", "request", "message", "handleConfigConfirm", "config<PERSON><PERSON><PERSON>", "defaultFormData", "setTimeout", "option", "handleCloseDialog", "handleBobinaSelect", "bobina", "handleFormChange", "e", "name", "value", "target", "idResult", "valid", "prev", "newErrors", "result", "warning", "newWarnings", "handleSave", "validation", "<PERSON><PERSON><PERSON><PERSON>", "errors", "warnings", "trim", "bobina<PERSON><PERSON>", "parseFloat", "JSON", "stringify", "createBobina", "window", "event", "CustomEvent", "dispatchEvent", "bobina<PERSON>d", "id_bobina", "updateBobina", "deleteBobina", "renderBobineCards", "severity", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "component", "sx", "mt", "size", "bgcolor", "hover", "px", "py", "borderRadius", "color", "fontSize", "fontWeight", "display", "gap", "IconButton", "onClick", "renderDialog", "open", "onClose", "max<PERSON><PERSON><PERSON>", "fullWidth", "Object", "keys", "mb", "variant", "style", "margin", "paddingLeft", "values", "index", "container", "spacing", "item", "xs", "sm", "label", "onChange", "disabled", "required", "InputProps", "helperText", "type", "id", "labelId", "placeholder", "readOnly", "startIcon", "button", "primary", "secondary", "record", "cavi", "p", "justifyContent", "alignItems", "my", "minHeight", "textAlign", "gutterBottom", "onConfirm", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/PycharmProjects/CMS/webapp/frontend/src/components/cavi/ParcoCavi.js"], "sourcesContent": ["import React, { useState, useEffect } from 'react';\nimport {\n  Box,\n  Typography,\n  Button,\n  Paper,\n  Grid,\n  Card,\n  CardContent,\n  CardActions,\n  Dialog,\n  DialogTitle,\n  DialogContent,\n  DialogActions,\n  TextField,\n  FormControl,\n  InputLabel,\n  Select,\n  MenuItem,\n  List,\n  ListItem,\n  ListItemText,\n  ListItemIcon,\n  ListItemButton,\n  Divider,\n  Alert,\n  CircularProgress,\n  Table,\n  TableBody,\n  TableCell,\n  TableContainer,\n  TableHead,\n  TableRow,\n  FormHelperText\n} from '@mui/material';\nimport {\n  Add as AddIcon,\n  Edit as EditIcon,\n  Delete as DeleteIcon,\n  History as HistoryIcon,\n  Save as SaveIcon,\n  ViewList as ViewListIcon,\n  Warning as WarningIcon\n} from '@mui/icons-material';\nimport parcoCaviService from '../../services/parcoCaviService';\nimport ConfigurazioneDialog from './ConfigurazioneDialog';\nimport { validateBobinaData, validateBobinaField, validateBobinaId, isEmpty } from '../../utils/bobinaValidationUtils';\n\nconst ParcoCavi = ({ cantiereId, onSuccess, onError, initialOption = null }) => {\n  const [loading, setLoading] = useState(false);\n  const [bobine, setBobine] = useState([]);\n  const [selectedOption, setSelectedOption] = useState(initialOption);\n  const [openDialog, setOpenDialog] = useState(false);\n  const [dialogType, setDialogType] = useState('');\n  const [selectedBobina, setSelectedBobina] = useState(null);\n  const [formData, setFormData] = useState({\n    numero_bobina: '',\n    utility: '',\n    tipologia: '',\n    n_conduttori: '',\n    sezione: '',\n    metri_totali: '',\n    metri_residui: '',\n    stato_bobina: 'Disponibile',\n    ubicazione_bobina: '',\n    fornitore: '',\n    n_DDT: '',\n    data_DDT: '',\n    configurazione: ''\n  });\n  const [formErrors, setFormErrors] = useState({});\n  const [formWarnings, setFormWarnings] = useState({});\n  const [storicoUtilizzo, setStoricoUtilizzo] = useState([]);\n  const [openConfigDialog, setOpenConfigDialog] = useState(false);\n  const [isFirstInsertion, setIsFirstInsertion] = useState(false);\n\n  // Carica le bobine disponibili\n  const loadBobine = async () => {\n    try {\n      setLoading(true);\n      const data = await parcoCaviService.getBobine(cantiereId);\n      setBobine(data);\n    } catch (error) {\n      onError('Errore nel caricamento delle bobine');\n      console.error('Errore nel caricamento delle bobine:', error);\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  // Carica lo storico utilizzo bobine\n  const loadStoricoUtilizzo = async () => {\n    try {\n      setLoading(true);\n      const data = await parcoCaviService.getStoricoUtilizzo(cantiereId);\n      setStoricoUtilizzo(data);\n    } catch (error) {\n      onError('Errore nel caricamento dello storico utilizzo');\n      console.error('Errore nel caricamento dello storico utilizzo:', error);\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  // Carica i dati all'avvio del componente e gestisce l'opzione iniziale\n  // Utilizziamo una ref per tenere traccia se l'effetto è già stato eseguito\n  const initialLoadDone = React.useRef(false);\n\n  useEffect(() => {\n    // Esegui solo una volta all'avvio del componente\n    if (!initialLoadDone.current) {\n      console.log('Primo caricamento del componente, initialOption:', initialOption);\n      initialLoadDone.current = true;\n\n      if (initialOption === 'creaBobina') {\n        console.log('Avvio processo creazione bobina');\n        // IMPORTANTE: Per il primo caricamento con creaBobina, forziamo l'apertura del dialog di configurazione\n        console.log('FORZANDO APERTURA DIALOG CONFIGURAZIONE ALL\\'AVVIO');\n        setIsFirstInsertion(true);\n        setOpenConfigDialog(true);\n      } else if (initialOption) {\n        console.log('Eseguendo handleOptionSelect con:', initialOption);\n        handleOptionSelect(initialOption);\n      } else {\n        console.log('Caricando bobine');\n        loadBobine();\n      }\n    }\n  }, []);  // Dipendenze vuote per eseguire solo al mount\n\n  // Verifica se è il primo inserimento di una bobina per un cantiere\n  const checkIfFirstInsertion = async () => {\n    // Variabile per memorizzare la configurazione\n    let configurazione = 's'; // Valore predefinito\n\n    try {\n      // Previene chiamate multiple\n      if (loading) {\n        console.log('Operazione già in corso, uscita');\n        return;\n      }\n\n      // Assicuriamoci che nessun dialog sia aperto\n      setOpenDialog(false);\n      setOpenConfigDialog(false); // Chiudi anche il dialog di configurazione se aperto\n\n      setLoading(true);\n      console.log('Verificando se è il primo inserimento per cantiere ID:', cantiereId);\n\n      // Gestione caso in cui cantiereId non sia valido\n      if (!cantiereId || isNaN(parseInt(cantiereId))) {\n        onError('ID cantiere non valido');\n        console.error('ID cantiere non valido:', cantiereId);\n        setLoading(false);\n        return;\n      }\n\n      // Chiamata API reale\n      let isFirst = false;\n\n      try {\n        console.log('Verificando se è il primo inserimento per cantiere ID:', cantiereId);\n        const response = await parcoCaviService.isFirstBobinaInsertion(cantiereId);\n        isFirst = response.is_first_insertion;\n        configurazione = response.configurazione || 's';\n\n        setIsFirstInsertion(isFirst);\n        console.log('È il primo inserimento di una bobina?', isFirst);\n        console.log('Configurazione esistente:', configurazione);\n      } catch (error) {\n        console.error('Errore durante la verifica del primo inserimento:', error);\n        // In caso di errore, assumiamo che non sia il primo inserimento\n        setIsFirstInsertion(false);\n        onError('Errore durante la verifica del primo inserimento. Procedendo con inserimento standard.');\n      }\n\n      if (isFirst) {\n        // Se è il primo inserimento, mostra il dialog di configurazione\n        console.log('Mostrando il dialog di configurazione');\n        // Assicuriamoci che il dialog di creazione sia chiuso\n        setOpenDialog(false);\n\n        // IMPORTANTE: Forziamo l'apertura del dialog di configurazione\n        console.log('FORZANDO APERTURA DIALOG CONFIGURAZIONE');\n        setOpenConfigDialog(true);\n      } else {\n        // Non è il primo inserimento, procedi con il form normale\n        console.log('Non è il primo inserimento, mostrando il form normale');\n\n        // Ottieni il prossimo numero di bobina\n        let nextBobinaNumber = '1';\n        try {\n          // Ottieni l'ultimo numero di bobina dal backend\n          const bobine = await parcoCaviService.getBobine(cantiereId);\n          if (bobine && bobine.length > 0) {\n            // Trova il numero massimo tra le bobine esistenti\n            const maxNumber = Math.max(...bobine\n              .filter(b => b.numero_bobina && /^\\d+$/.test(b.numero_bobina))\n              .map(b => parseInt(b.numero_bobina, 10)));\n\n            nextBobinaNumber = isNaN(maxNumber) ? '1' : String(maxNumber + 1);\n          }\n          console.log('Prossimo numero bobina:', nextBobinaNumber);\n        } catch (error) {\n          console.error('Errore nel recupero del prossimo numero bobina:', error);\n          // In caso di errore, usa 1 come default\n          nextBobinaNumber = '1';\n        }\n\n        setDialogType('creaBobina');\n        setFormData({\n          // In modalità automatica, imposta il numero progressivo\n          // In modalità manuale, lascia vuoto per far inserire all'utente\n          numero_bobina: configurazione === 's' ? nextBobinaNumber : '',\n          utility: '',\n          tipologia: '',\n          n_conduttori: '',\n          sezione: '',\n          metri_totali: '',\n          metri_residui: '',\n          stato_bobina: 'Disponibile',\n          ubicazione_bobina: '',\n          fornitore: '',\n          n_DDT: '',\n          data_DDT: '',\n          configurazione: configurazione  // Usa la configurazione esistente\n        });\n        setOpenDialog(true);\n      }\n    } catch (error) {\n      // Gestione dettagliata dell'errore\n      let errorMessage = 'Errore nel controllo dell\\'inserimento della prima bobina';\n\n      if (error.response) {\n        // Errore di risposta dal server\n        errorMessage += `: ${error.response.status} - ${error.response.data?.detail || 'Errore server'}`;\n        console.error('Dettagli errore API:', error.response);\n      } else if (error.request) {\n        // Errore di rete (nessuna risposta ricevuta)\n        errorMessage += ': Errore di connessione al server';\n      } else {\n        // Errore generico\n        errorMessage += `: ${error.message || 'Errore sconosciuto'}`;\n      }\n\n      onError(errorMessage);\n      console.error('Errore completo:', error);\n\n      // In caso di errore, usa la configurazione di default\n      configurazione = 's'; // Fallback al valore di default\n\n      setDialogType('creaBobina');\n      setFormData({\n        numero_bobina: configurazione === 's' ? '1' : '',\n        utility: '',\n        tipologia: '',\n        n_conduttori: '',\n        sezione: '',\n        metri_totali: '',\n        metri_residui: '',\n        stato_bobina: 'Disponibile',\n        ubicazione_bobina: '',\n        fornitore: '',\n        n_DDT: '',\n        data_DDT: '',\n        configurazione: configurazione  // Usa la configurazione esistente o il default\n      });\n      setOpenDialog(true);\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  // Gestisce la conferma della configurazione\n  const handleConfigConfirm = async (configValue) => {\n    console.log('Configurazione selezionata:', configValue);\n    setLoading(true);\n\n    try {\n      // Ottieni il prossimo numero di bobina se la configurazione è automatica\n      let nextBobinaNumber = '1';\n      if (configValue === 's') {\n        try {\n          // Ottieni l'ultimo numero di bobina dal backend\n          const bobine = await parcoCaviService.getBobine(cantiereId);\n          if (bobine && bobine.length > 0) {\n            // Trova il numero massimo tra le bobine esistenti\n            const maxNumber = Math.max(...bobine\n              .filter(b => b.numero_bobina && /^\\d+$/.test(b.numero_bobina))\n              .map(b => parseInt(b.numero_bobina, 10)));\n\n            nextBobinaNumber = isNaN(maxNumber) ? '1' : String(maxNumber + 1);\n          }\n          console.log('Prossimo numero bobina:', nextBobinaNumber);\n        } catch (error) {\n          console.error('Errore nel recupero del prossimo numero bobina:', error);\n          // In caso di errore, usa 1 come default\n          nextBobinaNumber = '1';\n        }\n      }\n\n      // Imposta i valori di default per la bobina\n      const defaultFormData = {\n        // In modalità automatica, imposta il numero progressivo\n        // In modalità manuale, lascia vuoto per far inserire all'utente\n        numero_bobina: configValue === 's' ? nextBobinaNumber : '',\n        utility: '',\n        tipologia: '',\n        n_conduttori: '',\n        sezione: '',\n        metri_totali: '',\n        metri_residui: '',\n        stato_bobina: 'Disponibile',\n        ubicazione_bobina: '',\n        fornitore: '',\n        n_DDT: '',\n        data_DDT: '',\n        configurazione: configValue // Imposta la configurazione scelta\n      };\n\n      console.log('Impostando i dati del form con configurazione:', configValue, 'numero_bobina:', defaultFormData.numero_bobina);\n\n      // Importante: prima prepara il form, poi chiudi il dialog di configurazione\n      // e solo dopo apri il dialog di creazione\n      setFormData(defaultFormData);\n      setDialogType('creaBobina');\n\n      // Chiudi il dialog di configurazione\n      setOpenConfigDialog(false);\n\n      // Piccolo timeout per assicurarsi che il dialog di configurazione sia chiuso\n      // prima di aprire il dialog di creazione\n      setTimeout(() => {\n        setOpenDialog(true);\n        console.log('Dialog di creazione bobina aperto con numero_bobina:', defaultFormData.numero_bobina);\n      }, 300);\n    } catch (error) {\n      console.error('Errore durante la preparazione del form:', error);\n      onError('Errore durante la preparazione del form: ' + (error.message || 'Errore sconosciuto'));\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  // Gestisce la selezione di un'opzione dal menu\n  const handleOptionSelect = (option) => {\n    setSelectedOption(option);\n\n    if (option === 'visualizzaBobine') {\n      loadBobine();\n    } else if (option === 'creaBobina') {\n      checkIfFirstInsertion();\n    } else if (option === 'modificaBobina') {\n      loadBobine();\n      setDialogType('selezionaBobina');\n      setOpenDialog(true);\n    } else if (option === 'eliminaBobina') {\n      loadBobine();\n      setDialogType('eliminaBobina');\n      setOpenDialog(true);\n    } else if (option === 'visualizzaStorico') {\n      loadStoricoUtilizzo();\n      setDialogType('visualizzaStorico');\n      setOpenDialog(true);\n    }\n  };\n\n  // Gestisce la chiusura del dialog\n  const handleCloseDialog = () => {\n    setOpenDialog(false);\n    setSelectedBobina(null);\n    setFormData({\n      numero_bobina: '',\n      utility: '',\n      tipologia: '',\n      n_conduttori: '',\n      sezione: '',\n      metri_totali: '',\n      metri_residui: '',\n      stato_bobina: 'Disponibile',\n      ubicazione_bobina: '',\n      fornitore: '',\n      n_DDT: '',\n      data_DDT: '',\n      configurazione: ''\n    });\n    setFormErrors({});\n    setFormWarnings({});\n  };\n\n  // Gestisce la selezione di una bobina\n  const handleBobinaSelect = (bobina) => {\n    setSelectedBobina(bobina);\n    if (dialogType === 'selezionaBobina') {\n      setDialogType('modificaBobina');\n      setFormData({\n        numero_bobina: bobina.numero_bobina,\n        utility: bobina.utility || '',\n        tipologia: bobina.tipologia || '',\n        n_conduttori: bobina.n_conduttori || '',\n        sezione: bobina.sezione || '',\n        metri_totali: bobina.metri_totali || '',\n        metri_residui: bobina.metri_residui || '',\n        stato_bobina: bobina.stato_bobina || 'DISPONIBILE',\n        ubicazione_bobina: bobina.ubicazione_bobina || '',\n        fornitore: bobina.fornitore || '',\n        n_DDT: bobina.n_DDT || '',\n        data_DDT: bobina.data_DDT || '',\n        configurazione: bobina.configurazione || ''\n      });\n    }\n  };\n\n  // Gestisce il cambio dei valori nel form con validazione\n  const handleFormChange = (e) => {\n    const { name, value } = e.target;\n\n    // Aggiorna il valore nel form\n    setFormData({\n      ...formData,\n      [name]: value\n    });\n\n    // Valida il campo\n    if (dialogType === 'creaBobina' || dialogType === 'modificaBobina') {\n      // Validazione speciale per numero_bobina quando configurazione è 'n'\n      if (name === 'numero_bobina' && formData.configurazione === 'n') {\n        const idResult = validateBobinaId(value);\n        if (!idResult.valid) {\n          setFormErrors(prev => ({\n            ...prev,\n            [name]: idResult.message\n          }));\n        } else {\n          setFormErrors(prev => {\n            const newErrors = { ...prev };\n            delete newErrors[name];\n            return newErrors;\n          });\n        }\n        return;\n      }\n\n      const result = validateBobinaField(name, value);\n\n      // Aggiorna gli errori\n      if (!result.valid) {\n        setFormErrors(prev => ({\n          ...prev,\n          [name]: result.message\n        }));\n      } else {\n        setFormErrors(prev => {\n          const newErrors = { ...prev };\n          delete newErrors[name];\n          return newErrors;\n        });\n\n        // Aggiorna gli avvisi\n        if (result.warning) {\n          setFormWarnings(prev => ({\n            ...prev,\n            [name]: result.message\n          }));\n        } else {\n          setFormWarnings(prev => {\n            const newWarnings = { ...prev };\n            delete newWarnings[name];\n            return newWarnings;\n          });\n        }\n      }\n    }\n  };\n\n  // Gestisce il salvataggio del form\n  const handleSave = async () => {\n    try {\n      // Validazione completa dei dati prima del salvataggio\n      if (dialogType === 'creaBobina' || dialogType === 'modificaBobina') {\n        const validation = validateBobinaData(formData);\n\n        if (!validation.isValid) {\n          setFormErrors(validation.errors);\n          setFormWarnings(validation.warnings);\n          onError('Correggi gli errori nel form prima di salvare');\n          return;\n        }\n\n        // Verifica aggiuntiva per il campo numero_bobina quando la configurazione è manuale\n        if (formData.configurazione === 'n' && (!formData.numero_bobina || formData.numero_bobina.trim() === '')) {\n          setFormErrors({\n            ...formErrors,\n            numero_bobina: 'L\\'ID della bobina è obbligatorio'\n          });\n          onError('L\\'ID della bobina è obbligatorio');\n          return;\n        }\n      }\n\n      setLoading(true);\n      console.log('Salvataggio dati bobina in corso...');\n\n      if (dialogType === 'creaBobina') {\n        // Prepara i dati per la creazione della bobina\n        const bobinaData = {\n          ...formData,\n          // Assicurati che tutti i campi siano nel formato corretto\n          numero_bobina: String(formData.numero_bobina || ''),\n          utility: String(formData.utility || ''),\n          tipologia: String(formData.tipologia || ''),\n          n_conduttori: String(formData.n_conduttori || ''),\n          sezione: String(formData.sezione || ''),\n          metri_totali: parseFloat(formData.metri_totali) || 0,\n          ubicazione_bobina: String(formData.ubicazione_bobina || 'TBD'),\n          fornitore: String(formData.fornitore || 'TBD'),\n          n_DDT: String(formData.n_DDT || 'TBD'),\n          data_DDT: formData.data_DDT || null,\n          // Assicurati che la configurazione sia impostata correttamente\n          configurazione: String(formData.configurazione || 's')\n        };\n\n        console.log('Dati bobina da inviare:', bobinaData);\n\n        try {\n          // Log dei dati che stiamo per inviare\n          console.log('Invio dati bobina al backend:', JSON.stringify(bobinaData, null, 2));\n\n          // Invia i dati al backend\n          await parcoCaviService.createBobina(cantiereId, bobinaData);\n          onSuccess('Bobina creata con successo');\n\n          // Chiudi il dialog\n          handleCloseDialog();\n\n          // Imposta l'opzione selezionata a visualizzaBobine e carica le bobine\n          setSelectedOption('visualizzaBobine');\n          loadBobine();\n\n          // Notifica al componente padre che dovrebbe reindirizzare alla pagina di visualizzazione bobine\n          if (typeof window !== 'undefined') {\n            // Usa un evento personalizzato per comunicare con il componente padre\n            const event = new CustomEvent('redirectToVisualizzaBobine', { detail: { cantiereId } });\n            window.dispatchEvent(event);\n          }\n        } catch (error) {\n          console.error('Errore durante la creazione della bobina:', error);\n\n          // Gestione dettagliata dell'errore\n          let errorMessage = 'Errore durante la creazione della bobina';\n\n          if (error.detail) {\n            errorMessage = error.detail;\n          } else if (error.message) {\n            errorMessage = error.message;\n          }\n\n          // Log dettagliato dell'errore\n          console.error('Dettagli errore:', errorMessage);\n          console.error('Dati inviati:', JSON.stringify(bobinaData, null, 2));\n\n          onError(errorMessage);\n          setLoading(false);\n          return;\n        }\n      } else if (dialogType === 'modificaBobina') {\n        // Per la modifica, usa l'ID bobina completo o il numero bobina\n        const bobinaId = selectedBobina.id_bobina || formData.numero_bobina;\n        await parcoCaviService.updateBobina(cantiereId, bobinaId, formData);\n        onSuccess('Bobina modificata con successo');\n      } else if (dialogType === 'eliminaBobina') {\n        // Per l'eliminazione, usa l'ID bobina completo\n        const bobinaId = selectedBobina.id_bobina || selectedBobina.numero_bobina;\n        await parcoCaviService.deleteBobina(cantiereId, bobinaId);\n        onSuccess('Bobina eliminata con successo');\n      }\n\n      handleCloseDialog();\n      loadBobine(); // Ricarica le bobine dopo l'operazione\n    } catch (error) {\n      onError('Errore durante l\\'operazione: ' + (error.detail || error.message || 'Errore sconosciuto'));\n      console.error('Errore durante l\\'operazione:', error);\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  // Renderizza le bobine in formato lista\n  const renderBobineCards = () => {\n    if (bobine.length === 0) {\n      return (\n        <Alert severity=\"info\">Nessuna bobina disponibile</Alert>\n      );\n    }\n\n    return (\n      <TableContainer component={Paper} sx={{ mt: 2 }}>\n        <Table size=\"small\">\n          <TableHead>\n            <TableRow sx={{ bgcolor: '#f5f5f5' }}>\n              <TableCell><strong>ID Bobina</strong></TableCell>\n              <TableCell><strong>Utility</strong></TableCell>\n              <TableCell><strong>Tipologia</strong></TableCell>\n              <TableCell><strong>N° Cond.</strong></TableCell>\n              <TableCell><strong>Sezione</strong></TableCell>\n              <TableCell><strong>Metri Tot.</strong></TableCell>\n              <TableCell><strong>Metri Res.</strong></TableCell>\n              <TableCell><strong>Stato</strong></TableCell>\n              <TableCell><strong>Ubicazione</strong></TableCell>\n              <TableCell><strong>Azioni</strong></TableCell>\n            </TableRow>\n          </TableHead>\n          <TableBody>\n            {bobine.map((bobina) => (\n              <TableRow key={bobina.numero_bobina} hover>\n                <TableCell>{bobina.numero_bobina}</TableCell>\n                <TableCell>{bobina.utility || 'N/A'}</TableCell>\n                <TableCell>{bobina.tipologia || 'N/A'}</TableCell>\n                <TableCell>{bobina.n_conduttori || 'N/A'}</TableCell>\n                <TableCell>{bobina.sezione || 'N/A'}</TableCell>\n                <TableCell>{bobina.metri_totali || 'N/A'} m</TableCell>\n                <TableCell>{bobina.metri_residui || 'N/A'} m</TableCell>\n                <TableCell>\n                  <Box\n                    component=\"span\"\n                    sx={{\n                      px: 1,\n                      py: 0.5,\n                      borderRadius: 1,\n                      bgcolor: bobina.stato_bobina === 'Disponibile' ? 'success.light' :\n                              bobina.stato_bobina === 'In Uso' ? 'warning.light' : 'error.light',\n                      color: 'white',\n                      fontSize: '0.75rem',\n                      fontWeight: 'bold'\n                    }}\n                  >\n                    {bobina.stato_bobina || 'DISPONIBILE'}\n                  </Box>\n                </TableCell>\n                <TableCell>{bobina.ubicazione_bobina || 'N/A'}</TableCell>\n                <TableCell>\n                  <Box sx={{ display: 'flex', gap: 1 }}>\n                    <IconButton\n                      size=\"small\"\n                      color=\"primary\"\n                      onClick={() => {\n                        setDialogType('selezionaBobina');\n                        handleBobinaSelect(bobina);\n                      }}\n                    >\n                      <EditIcon fontSize=\"small\" />\n                    </IconButton>\n                    <IconButton\n                      size=\"small\"\n                      color=\"error\"\n                      onClick={() => {\n                        setDialogType('eliminaBobina');\n                        setSelectedBobina(bobina);\n                        setOpenDialog(true);\n                      }}\n                    >\n                      <DeleteIcon fontSize=\"small\" />\n                    </IconButton>\n                  </Box>\n                </TableCell>\n              </TableRow>\n            ))}\n          </TableBody>\n        </Table>\n      </TableContainer>\n    );\n  };\n\n  // Renderizza il dialog in base al tipo\n  const renderDialog = () => {\n    if (dialogType === 'creaBobina' || dialogType === 'modificaBobina') {\n      return (\n        <Dialog open={openDialog} onClose={handleCloseDialog} maxWidth=\"md\" fullWidth>\n          <DialogTitle>\n            {dialogType === 'creaBobina' ? 'Crea Nuova Bobina' : 'Modifica Bobina'}\n          </DialogTitle>\n          <DialogContent>\n            {Object.keys(formWarnings).length > 0 && (\n              <Alert severity=\"warning\" sx={{ mb: 2, mt: 1 }}>\n                <Typography variant=\"subtitle2\">Attenzione:</Typography>\n                <ul style={{ margin: 0, paddingLeft: '20px' }}>\n                  {Object.values(formWarnings).map((warning, index) => (\n                    <li key={index}>{warning}</li>\n                  ))}\n                </ul>\n              </Alert>\n            )}\n\n            <Grid container spacing={2} sx={{ mt: 1 }}>\n              <Grid item xs={12} sm={6}>\n                <TextField\n                  name=\"numero_bobina\"\n                  label=\"ID Bobina\"\n                  fullWidth\n                  variant=\"outlined\"\n                  value={formData.numero_bobina}\n                  onChange={handleFormChange}\n                  disabled={dialogType === 'modificaBobina' || (dialogType === 'creaBobina' && formData.configurazione === 's')}\n                  required\n                  error={!!formErrors.numero_bobina}\n                  InputProps={{\n                    sx: {\n                      bgcolor: formData.configurazione === 's' ? '#f5f5f5' : 'transparent',\n                      fontWeight: 'bold',\n                    }\n                  }}\n                  helperText={\n                    formErrors.numero_bobina ||\n                    (dialogType === 'creaBobina' && formData.configurazione === 's'\n                      ? `ID completo: C${cantiereId}_B${formData.numero_bobina || ''} (generato automaticamente)`\n                      : dialogType === 'creaBobina' && formData.configurazione === 'n'\n                        ? `Inserisci l'ID della bobina (es. A123). ID completo sarà: C${cantiereId}_B{tuo input}`\n                        : '')\n                  }\n                  type={formData.configurazione === 's' ? \"text\" : \"text\"}\n                />\n              </Grid>\n              <Grid item xs={12} sm={6}>\n                <TextField\n                  name=\"utility\"\n                  label=\"Utility\"\n                  fullWidth\n                  variant=\"outlined\"\n                  value={formData.utility}\n                  onChange={handleFormChange}\n                  required\n                  error={!!formErrors.utility}\n                  helperText={formErrors.utility || ''}\n                />\n              </Grid>\n              <Grid item xs={12} sm={6}>\n                <TextField\n                  name=\"tipologia\"\n                  label=\"Tipologia\"\n                  fullWidth\n                  variant=\"outlined\"\n                  value={formData.tipologia}\n                  onChange={handleFormChange}\n                  required\n                  error={!!formErrors.tipologia}\n                  helperText={formErrors.tipologia || ''}\n                />\n              </Grid>\n              <Grid item xs={12} sm={6}>\n                <TextField\n                  name=\"n_conduttori\"\n                  label=\"N° Conduttori\"\n                  fullWidth\n                  variant=\"outlined\"\n                  value={formData.n_conduttori}\n                  onChange={handleFormChange}\n                  required\n                  error={!!formErrors.n_conduttori}\n                  helperText={formErrors.n_conduttori || ''}\n                />\n              </Grid>\n              <Grid item xs={12} sm={6}>\n                <TextField\n                  name=\"sezione\"\n                  label=\"Sezione\"\n                  fullWidth\n                  variant=\"outlined\"\n                  value={formData.sezione}\n                  onChange={handleFormChange}\n                  required\n                  error={!!formErrors.sezione}\n                  helperText={formErrors.sezione || ''}\n                />\n              </Grid>\n              <Grid item xs={12} sm={6}>\n                <TextField\n                  name=\"metri_totali\"\n                  label=\"Metri Totali\"\n                  type=\"number\"\n                  fullWidth\n                  variant=\"outlined\"\n                  value={formData.metri_totali}\n                  onChange={handleFormChange}\n                  required\n                  error={!!formErrors.metri_totali}\n                  helperText={formErrors.metri_totali || ''}\n                />\n              </Grid>\n              {dialogType === 'modificaBobina' && (\n                <Grid item xs={12} sm={6}>\n                  <TextField\n                    name=\"metri_residui\"\n                    label=\"Metri Residui\"\n                    type=\"number\"\n                    fullWidth\n                    variant=\"outlined\"\n                    value={formData.metri_residui}\n                    onChange={handleFormChange}\n                    required\n                    disabled={true}\n                    helperText=\"I metri residui non possono essere modificati direttamente\"\n                  />\n                </Grid>\n              )}\n              <Grid item xs={12} sm={6}>\n                <FormControl fullWidth>\n                  <InputLabel id=\"stato-bobina-label\">Stato Bobina</InputLabel>\n                  <Select\n                    labelId=\"stato-bobina-label\"\n                    name=\"stato_bobina\"\n                    value={formData.stato_bobina}\n                    label=\"Stato Bobina\"\n                    onChange={handleFormChange}\n                    disabled={dialogType === 'creaBobina'}\n                  >\n                    <MenuItem value=\"Disponibile\">Disponibile</MenuItem>\n                    <MenuItem value=\"In uso\">In uso</MenuItem>\n                    <MenuItem value=\"Terminata\">Terminata</MenuItem>\n                    <MenuItem value=\"Danneggiata\">Danneggiata</MenuItem>\n                    <MenuItem value=\"Over\">Over</MenuItem>\n                  </Select>\n                  {dialogType === 'creaBobina' && (\n                    <FormHelperText>Per una nuova bobina, lo stato è sempre \"Disponibile\"</FormHelperText>\n                  )}\n                </FormControl>\n              </Grid>\n              <Grid item xs={12} sm={6}>\n                <TextField\n                  name=\"ubicazione_bobina\"\n                  label=\"Ubicazione Bobina\"\n                  fullWidth\n                  variant=\"outlined\"\n                  value={formData.ubicazione_bobina}\n                  onChange={handleFormChange}\n                  error={!!formErrors.ubicazione_bobina}\n                  helperText={formErrors.ubicazione_bobina || ''}\n                />\n              </Grid>\n              <Grid item xs={12} sm={6}>\n                <TextField\n                  name=\"fornitore\"\n                  label=\"Fornitore\"\n                  fullWidth\n                  variant=\"outlined\"\n                  value={formData.fornitore}\n                  onChange={handleFormChange}\n                  error={!!formErrors.fornitore}\n                  helperText={formErrors.fornitore || ''}\n                />\n              </Grid>\n              <Grid item xs={12} sm={6}>\n                <TextField\n                  name=\"n_DDT\"\n                  label=\"Numero DDT\"\n                  fullWidth\n                  variant=\"outlined\"\n                  value={formData.n_DDT}\n                  onChange={handleFormChange}\n                  error={!!formErrors.n_DDT}\n                  helperText={formErrors.n_DDT || ''}\n                />\n              </Grid>\n              <Grid item xs={12} sm={6}>\n                <TextField\n                  name=\"data_DDT\"\n                  label=\"Data DDT (YYYY-MM-DD)\"\n                  fullWidth\n                  variant=\"outlined\"\n                  value={formData.data_DDT}\n                  onChange={handleFormChange}\n                  placeholder=\"YYYY-MM-DD\"\n                  error={!!formErrors.data_DDT}\n                  helperText={formErrors.data_DDT || ''}\n                />\n              </Grid>\n              <Grid item xs={12} sm={6}>\n                <TextField\n                  name=\"configurazione\"\n                  label=\"Modalità Numerazione\"\n                  fullWidth\n                  variant=\"outlined\"\n                  value={formData.configurazione === 's' ? 'Automatica' : 'Manuale'}\n                  InputProps={{\n                    readOnly: true,\n                    sx: {\n                      bgcolor: '#f5f5f5',\n                    }\n                  }}\n                  helperText={\n                    <Box sx={{ fontWeight: 'medium', color: formData.configurazione === 's' ? 'success.main' : 'info.main' }}>\n                      {formData.configurazione === 's'\n                        ? 'Numerazione progressiva automatica (1, 2, 3, ...)'\n                        : 'Inserimento manuale dell\\'ID bobina (es. A123, SPEC01, ...)'}\n                    </Box>\n                  }\n                />\n              </Grid>\n            </Grid>\n          </DialogContent>\n          <DialogActions>\n            <Button onClick={handleCloseDialog}>Annulla</Button>\n            <Button\n              onClick={handleSave}\n              disabled={loading || Object.keys(formErrors).length > 0}\n              startIcon={loading ? <CircularProgress size={20} /> : <SaveIcon />}\n              variant=\"contained\"\n              color=\"primary\"\n            >\n              Salva\n            </Button>\n          </DialogActions>\n        </Dialog>\n      );\n    } else if (dialogType === 'selezionaBobina') {\n      return (\n        <Dialog open={openDialog} onClose={handleCloseDialog} maxWidth=\"md\" fullWidth>\n          <DialogTitle>Seleziona Bobina da Modificare</DialogTitle>\n          <DialogContent>\n            {loading ? (\n              <CircularProgress />\n            ) : bobine.length === 0 ? (\n              <Alert severity=\"info\">Nessuna bobina disponibile</Alert>\n            ) : (\n              <List>\n                {bobine.map((bobina) => (\n                  <ListItem\n                    button\n                    key={bobina.numero_bobina}\n                    onClick={() => handleBobinaSelect(bobina)}\n                  >\n                    <ListItemText\n                      primary={`Bobina: ${bobina.numero_bobina}`}\n                      secondary={`Tipologia: ${bobina.tipologia || 'N/A'} - Utility: ${bobina.utility || 'N/A'} - Residuo: ${bobina.metri_residui || 'N/A'} m`}\n                    />\n                  </ListItem>\n                ))}\n              </List>\n            )}\n          </DialogContent>\n          <DialogActions>\n            <Button onClick={handleCloseDialog}>Annulla</Button>\n          </DialogActions>\n        </Dialog>\n      );\n    } else if (dialogType === 'eliminaBobina') {\n      return (\n        <Dialog open={openDialog} onClose={handleCloseDialog} maxWidth=\"sm\" fullWidth>\n          <DialogTitle>Elimina Bobina</DialogTitle>\n          <DialogContent>\n            {!selectedBobina ? (\n              loading ? (\n                <CircularProgress />\n              ) : bobine.length === 0 ? (\n                <Alert severity=\"info\">Nessuna bobina disponibile</Alert>\n              ) : (\n                <List>\n                  {bobine.map((bobina) => (\n                    <ListItem\n                      button\n                      key={bobina.numero_bobina}\n                      onClick={() => setSelectedBobina(bobina)}\n                    >\n                      <ListItemText\n                        primary={`Bobina: ${bobina.numero_bobina}`}\n                        secondary={`Tipologia: ${bobina.tipologia || 'N/A'} - Utility: ${bobina.utility || 'N/A'} - Residuo: ${bobina.metri_residui || 'N/A'} m`}\n                      />\n                    </ListItem>\n                  ))}\n                </List>\n              )\n            ) : (\n              <Box>\n                <Alert severity=\"warning\" sx={{ mb: 2 }}>\n                  Sei sicuro di voler eliminare la bobina {selectedBobina.numero_bobina}?\n                </Alert>\n                <Typography variant=\"body1\">\n                  Questa operazione non può essere annullata.\n                </Typography>\n              </Box>\n            )}\n          </DialogContent>\n          <DialogActions>\n            <Button onClick={handleCloseDialog}>Annulla</Button>\n            {selectedBobina && (\n              <Button\n                onClick={handleSave}\n                disabled={loading}\n                color=\"error\"\n                startIcon={loading ? <CircularProgress size={20} /> : <DeleteIcon />}\n              >\n                Elimina\n              </Button>\n            )}\n          </DialogActions>\n        </Dialog>\n      );\n    } else if (dialogType === 'visualizzaStorico') {\n      return (\n        <Dialog open={openDialog} onClose={handleCloseDialog} maxWidth=\"lg\" fullWidth>\n          <DialogTitle>Storico Utilizzo Bobine</DialogTitle>\n          <DialogContent>\n            {loading ? (\n              <CircularProgress />\n            ) : storicoUtilizzo.length === 0 ? (\n              <Alert severity=\"info\">Nessun dato storico disponibile</Alert>\n            ) : (\n              <TableContainer component={Paper} sx={{ mt: 2 }}>\n                <Table size=\"small\">\n                  <TableHead>\n                    <TableRow>\n                      <TableCell>Bobina</TableCell>\n                      <TableCell>Utility</TableCell>\n                      <TableCell>Tipologia</TableCell>\n                      <TableCell>N° Conduttori</TableCell>\n                      <TableCell>Sezione</TableCell>\n                      <TableCell>Metri Totali</TableCell>\n                      <TableCell>Metri Residui</TableCell>\n                      <TableCell>Cavi Associati</TableCell>\n                    </TableRow>\n                  </TableHead>\n                  <TableBody>\n                    {storicoUtilizzo.map((record, index) => (\n                      <TableRow key={index}>\n                        <TableCell>{record.numero_bobina}</TableCell>\n                        <TableCell>{record.utility}</TableCell>\n                        <TableCell>{record.tipologia}</TableCell>\n                        <TableCell>{record.n_conduttori}</TableCell>\n                        <TableCell>{record.sezione}</TableCell>\n                        <TableCell>{record.metri_totali}</TableCell>\n                        <TableCell>{record.metri_residui}</TableCell>\n                        <TableCell>{record.cavi.length}</TableCell>\n                      </TableRow>\n                    ))}\n                  </TableBody>\n                </Table>\n              </TableContainer>\n            )}\n          </DialogContent>\n          <DialogActions>\n            <Button onClick={handleCloseDialog}>Chiudi</Button>\n          </DialogActions>\n        </Dialog>\n      );\n    }\n\n    return null;\n  };\n\n  return (\n    <Box>\n      {selectedOption === 'visualizzaBobine' && !openDialog ? (\n        <Paper sx={{ p: 3 }}>\n          <Box sx={{ display: 'flex', justifyContent: 'flex-end', alignItems: 'center', mb: 2 }}>\n            <Box sx={{ display: 'flex', gap: 2 }}>\n              <Button\n                variant=\"outlined\"\n                color=\"inherit\"\n                startIcon={<HistoryIcon />}\n                onClick={() => {\n                  loadStoricoUtilizzo();\n                  setDialogType('visualizzaStorico');\n                  setOpenDialog(true);\n                }}\n                sx={{\n                  fontWeight: 'medium',\n                  borderRadius: 0\n                }}\n              >\n                Storico Utilizzo\n              </Button>\n              <Button\n                variant=\"contained\"\n                color=\"inherit\"\n                startIcon={<AddIcon />}\n                onClick={() => checkIfFirstInsertion()}\n                sx={{\n                  fontWeight: 'medium',\n                  borderRadius: 0\n                }}\n              >\n                Crea Nuova Bobina\n              </Button>\n            </Box>\n          </Box>\n          {loading ? (\n            <Box sx={{ display: 'flex', justifyContent: 'center', my: 4 }}>\n              <CircularProgress />\n            </Box>\n          ) : (\n            renderBobineCards()\n          )}\n        </Paper>\n      ) : !openDialog ? (\n        <Paper sx={{ p: 3, minHeight: '300px', display: 'flex', alignItems: 'center', justifyContent: 'center' }}>\n          {!selectedOption ? (\n            <Typography variant=\"body1\">\n              Seleziona un'opzione dal menu principale per iniziare.\n            </Typography>\n          ) : (\n            <Box sx={{ textAlign: 'center' }}>\n              <Typography variant=\"h6\" gutterBottom>\n                {selectedOption === 'creaBobina' && 'Crea Nuova Bobina'}\n                {selectedOption === 'modificaBobina' && 'Modifica Bobina'}\n                {selectedOption === 'eliminaBobina' && 'Elimina Bobina'}\n                {selectedOption === 'visualizzaStorico' && 'Visualizza Storico Utilizzo'}\n              </Typography>\n              <CircularProgress sx={{ mt: 2 }} />\n            </Box>\n          )}\n        </Paper>\n      ) : null}\n\n      {renderDialog()}\n\n      {/* Dialog di configurazione per il primo inserimento */}\n      <ConfigurazioneDialog\n        open={openConfigDialog}\n        onClose={() => setOpenConfigDialog(false)}\n        onConfirm={handleConfigConfirm}\n      />\n    </Box>\n  );\n};\n\nexport default ParcoCavi;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAClD,SACEC,GAAG,EACHC,UAAU,EACVC,MAAM,EACNC,KAAK,EACLC,IAAI,EACJC,IAAI,EACJC,WAAW,EACXC,WAAW,EACXC,MAAM,EACNC,WAAW,EACXC,aAAa,EACbC,aAAa,EACbC,SAAS,EACTC,WAAW,EACXC,UAAU,EACVC,MAAM,EACNC,QAAQ,EACRC,IAAI,EACJC,QAAQ,EACRC,YAAY,EACZC,YAAY,EACZC,cAAc,EACdC,OAAO,EACPC,KAAK,EACLC,gBAAgB,EAChBC,KAAK,EACLC,SAAS,EACTC,SAAS,EACTC,cAAc,EACdC,SAAS,EACTC,QAAQ,EACRC,cAAc,QACT,eAAe;AACtB,SACEC,GAAG,IAAIC,OAAO,EACdC,IAAI,IAAIC,QAAQ,EAChBC,MAAM,IAAIC,UAAU,EACpBC,OAAO,IAAIC,WAAW,EACtBC,IAAI,IAAIC,QAAQ,EAChBC,QAAQ,IAAIC,YAAY,EACxBC,OAAO,IAAIC,WAAW,QACjB,qBAAqB;AAC5B,OAAOC,gBAAgB,MAAM,iCAAiC;AAC9D,OAAOC,oBAAoB,MAAM,wBAAwB;AACzD,SAASC,kBAAkB,EAAEC,mBAAmB,EAAEC,gBAAgB,EAAEC,OAAO,QAAQ,mCAAmC;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAEvH,MAAMC,SAAS,GAAGA,CAAC;EAAEC,UAAU;EAAEC,SAAS;EAAEC,OAAO;EAAEC,aAAa,GAAG;AAAK,CAAC,KAAK;EAAAC,EAAA;EAC9E,MAAM,CAACC,OAAO,EAAEC,UAAU,CAAC,GAAG/D,QAAQ,CAAC,KAAK,CAAC;EAC7C,MAAM,CAACgE,MAAM,EAAEC,SAAS,CAAC,GAAGjE,QAAQ,CAAC,EAAE,CAAC;EACxC,MAAM,CAACkE,cAAc,EAAEC,iBAAiB,CAAC,GAAGnE,QAAQ,CAAC4D,aAAa,CAAC;EACnE,MAAM,CAACQ,UAAU,EAAEC,aAAa,CAAC,GAAGrE,QAAQ,CAAC,KAAK,CAAC;EACnD,MAAM,CAACsE,UAAU,EAAEC,aAAa,CAAC,GAAGvE,QAAQ,CAAC,EAAE,CAAC;EAChD,MAAM,CAACwE,cAAc,EAAEC,iBAAiB,CAAC,GAAGzE,QAAQ,CAAC,IAAI,CAAC;EAC1D,MAAM,CAAC0E,QAAQ,EAAEC,WAAW,CAAC,GAAG3E,QAAQ,CAAC;IACvC4E,aAAa,EAAE,EAAE;IACjBC,OAAO,EAAE,EAAE;IACXC,SAAS,EAAE,EAAE;IACbC,YAAY,EAAE,EAAE;IAChBC,OAAO,EAAE,EAAE;IACXC,YAAY,EAAE,EAAE;IAChBC,aAAa,EAAE,EAAE;IACjBC,YAAY,EAAE,aAAa;IAC3BC,iBAAiB,EAAE,EAAE;IACrBC,SAAS,EAAE,EAAE;IACbC,KAAK,EAAE,EAAE;IACTC,QAAQ,EAAE,EAAE;IACZC,cAAc,EAAE;EAClB,CAAC,CAAC;EACF,MAAM,CAACC,UAAU,EAAEC,aAAa,CAAC,GAAG1F,QAAQ,CAAC,CAAC,CAAC,CAAC;EAChD,MAAM,CAAC2F,YAAY,EAAEC,eAAe,CAAC,GAAG5F,QAAQ,CAAC,CAAC,CAAC,CAAC;EACpD,MAAM,CAAC6F,eAAe,EAAEC,kBAAkB,CAAC,GAAG9F,QAAQ,CAAC,EAAE,CAAC;EAC1D,MAAM,CAAC+F,gBAAgB,EAAEC,mBAAmB,CAAC,GAAGhG,QAAQ,CAAC,KAAK,CAAC;EAC/D,MAAM,CAACiG,gBAAgB,EAAEC,mBAAmB,CAAC,GAAGlG,QAAQ,CAAC,KAAK,CAAC;;EAE/D;EACA,MAAMmG,UAAU,GAAG,MAAAA,CAAA,KAAY;IAC7B,IAAI;MACFpC,UAAU,CAAC,IAAI,CAAC;MAChB,MAAMqC,IAAI,GAAG,MAAMpD,gBAAgB,CAACqD,SAAS,CAAC5C,UAAU,CAAC;MACzDQ,SAAS,CAACmC,IAAI,CAAC;IACjB,CAAC,CAAC,OAAOE,KAAK,EAAE;MACd3C,OAAO,CAAC,qCAAqC,CAAC;MAC9C4C,OAAO,CAACD,KAAK,CAAC,sCAAsC,EAAEA,KAAK,CAAC;IAC9D,CAAC,SAAS;MACRvC,UAAU,CAAC,KAAK,CAAC;IACnB;EACF,CAAC;;EAED;EACA,MAAMyC,mBAAmB,GAAG,MAAAA,CAAA,KAAY;IACtC,IAAI;MACFzC,UAAU,CAAC,IAAI,CAAC;MAChB,MAAMqC,IAAI,GAAG,MAAMpD,gBAAgB,CAACyD,kBAAkB,CAAChD,UAAU,CAAC;MAClEqC,kBAAkB,CAACM,IAAI,CAAC;IAC1B,CAAC,CAAC,OAAOE,KAAK,EAAE;MACd3C,OAAO,CAAC,+CAA+C,CAAC;MACxD4C,OAAO,CAACD,KAAK,CAAC,gDAAgD,EAAEA,KAAK,CAAC;IACxE,CAAC,SAAS;MACRvC,UAAU,CAAC,KAAK,CAAC;IACnB;EACF,CAAC;;EAED;EACA;EACA,MAAM2C,eAAe,GAAG3G,KAAK,CAAC4G,MAAM,CAAC,KAAK,CAAC;EAE3C1G,SAAS,CAAC,MAAM;IACd;IACA,IAAI,CAACyG,eAAe,CAACE,OAAO,EAAE;MAC5BL,OAAO,CAACM,GAAG,CAAC,kDAAkD,EAAEjD,aAAa,CAAC;MAC9E8C,eAAe,CAACE,OAAO,GAAG,IAAI;MAE9B,IAAIhD,aAAa,KAAK,YAAY,EAAE;QAClC2C,OAAO,CAACM,GAAG,CAAC,iCAAiC,CAAC;QAC9C;QACAN,OAAO,CAACM,GAAG,CAAC,oDAAoD,CAAC;QACjEX,mBAAmB,CAAC,IAAI,CAAC;QACzBF,mBAAmB,CAAC,IAAI,CAAC;MAC3B,CAAC,MAAM,IAAIpC,aAAa,EAAE;QACxB2C,OAAO,CAACM,GAAG,CAAC,mCAAmC,EAAEjD,aAAa,CAAC;QAC/DkD,kBAAkB,CAAClD,aAAa,CAAC;MACnC,CAAC,MAAM;QACL2C,OAAO,CAACM,GAAG,CAAC,kBAAkB,CAAC;QAC/BV,UAAU,CAAC,CAAC;MACd;IACF;EACF,CAAC,EAAE,EAAE,CAAC,CAAC,CAAE;;EAET;EACA,MAAMY,qBAAqB,GAAG,MAAAA,CAAA,KAAY;IACxC;IACA,IAAIvB,cAAc,GAAG,GAAG,CAAC,CAAC;;IAE1B,IAAI;MACF;MACA,IAAI1B,OAAO,EAAE;QACXyC,OAAO,CAACM,GAAG,CAAC,iCAAiC,CAAC;QAC9C;MACF;;MAEA;MACAxC,aAAa,CAAC,KAAK,CAAC;MACpB2B,mBAAmB,CAAC,KAAK,CAAC,CAAC,CAAC;;MAE5BjC,UAAU,CAAC,IAAI,CAAC;MAChBwC,OAAO,CAACM,GAAG,CAAC,wDAAwD,EAAEpD,UAAU,CAAC;;MAEjF;MACA,IAAI,CAACA,UAAU,IAAIuD,KAAK,CAACC,QAAQ,CAACxD,UAAU,CAAC,CAAC,EAAE;QAC9CE,OAAO,CAAC,wBAAwB,CAAC;QACjC4C,OAAO,CAACD,KAAK,CAAC,yBAAyB,EAAE7C,UAAU,CAAC;QACpDM,UAAU,CAAC,KAAK,CAAC;QACjB;MACF;;MAEA;MACA,IAAImD,OAAO,GAAG,KAAK;MAEnB,IAAI;QACFX,OAAO,CAACM,GAAG,CAAC,wDAAwD,EAAEpD,UAAU,CAAC;QACjF,MAAM0D,QAAQ,GAAG,MAAMnE,gBAAgB,CAACoE,sBAAsB,CAAC3D,UAAU,CAAC;QAC1EyD,OAAO,GAAGC,QAAQ,CAACE,kBAAkB;QACrC7B,cAAc,GAAG2B,QAAQ,CAAC3B,cAAc,IAAI,GAAG;QAE/CU,mBAAmB,CAACgB,OAAO,CAAC;QAC5BX,OAAO,CAACM,GAAG,CAAC,uCAAuC,EAAEK,OAAO,CAAC;QAC7DX,OAAO,CAACM,GAAG,CAAC,2BAA2B,EAAErB,cAAc,CAAC;MAC1D,CAAC,CAAC,OAAOc,KAAK,EAAE;QACdC,OAAO,CAACD,KAAK,CAAC,mDAAmD,EAAEA,KAAK,CAAC;QACzE;QACAJ,mBAAmB,CAAC,KAAK,CAAC;QAC1BvC,OAAO,CAAC,wFAAwF,CAAC;MACnG;MAEA,IAAIuD,OAAO,EAAE;QACX;QACAX,OAAO,CAACM,GAAG,CAAC,uCAAuC,CAAC;QACpD;QACAxC,aAAa,CAAC,KAAK,CAAC;;QAEpB;QACAkC,OAAO,CAACM,GAAG,CAAC,yCAAyC,CAAC;QACtDb,mBAAmB,CAAC,IAAI,CAAC;MAC3B,CAAC,MAAM;QACL;QACAO,OAAO,CAACM,GAAG,CAAC,uDAAuD,CAAC;;QAEpE;QACA,IAAIS,gBAAgB,GAAG,GAAG;QAC1B,IAAI;UACF;UACA,MAAMtD,MAAM,GAAG,MAAMhB,gBAAgB,CAACqD,SAAS,CAAC5C,UAAU,CAAC;UAC3D,IAAIO,MAAM,IAAIA,MAAM,CAACuD,MAAM,GAAG,CAAC,EAAE;YAC/B;YACA,MAAMC,SAAS,GAAGC,IAAI,CAACC,GAAG,CAAC,GAAG1D,MAAM,CACjC2D,MAAM,CAACC,CAAC,IAAIA,CAAC,CAAChD,aAAa,IAAI,OAAO,CAACiD,IAAI,CAACD,CAAC,CAAChD,aAAa,CAAC,CAAC,CAC7DkD,GAAG,CAACF,CAAC,IAAIX,QAAQ,CAACW,CAAC,CAAChD,aAAa,EAAE,EAAE,CAAC,CAAC,CAAC;YAE3C0C,gBAAgB,GAAGN,KAAK,CAACQ,SAAS,CAAC,GAAG,GAAG,GAAGO,MAAM,CAACP,SAAS,GAAG,CAAC,CAAC;UACnE;UACAjB,OAAO,CAACM,GAAG,CAAC,yBAAyB,EAAES,gBAAgB,CAAC;QAC1D,CAAC,CAAC,OAAOhB,KAAK,EAAE;UACdC,OAAO,CAACD,KAAK,CAAC,iDAAiD,EAAEA,KAAK,CAAC;UACvE;UACAgB,gBAAgB,GAAG,GAAG;QACxB;QAEA/C,aAAa,CAAC,YAAY,CAAC;QAC3BI,WAAW,CAAC;UACV;UACA;UACAC,aAAa,EAAEY,cAAc,KAAK,GAAG,GAAG8B,gBAAgB,GAAG,EAAE;UAC7DzC,OAAO,EAAE,EAAE;UACXC,SAAS,EAAE,EAAE;UACbC,YAAY,EAAE,EAAE;UAChBC,OAAO,EAAE,EAAE;UACXC,YAAY,EAAE,EAAE;UAChBC,aAAa,EAAE,EAAE;UACjBC,YAAY,EAAE,aAAa;UAC3BC,iBAAiB,EAAE,EAAE;UACrBC,SAAS,EAAE,EAAE;UACbC,KAAK,EAAE,EAAE;UACTC,QAAQ,EAAE,EAAE;UACZC,cAAc,EAAEA,cAAc,CAAE;QAClC,CAAC,CAAC;QACFnB,aAAa,CAAC,IAAI,CAAC;MACrB;IACF,CAAC,CAAC,OAAOiC,KAAK,EAAE;MACd;MACA,IAAI0B,YAAY,GAAG,2DAA2D;MAE9E,IAAI1B,KAAK,CAACa,QAAQ,EAAE;QAAA,IAAAc,oBAAA;QAClB;QACAD,YAAY,IAAI,KAAK1B,KAAK,CAACa,QAAQ,CAACe,MAAM,MAAM,EAAAD,oBAAA,GAAA3B,KAAK,CAACa,QAAQ,CAACf,IAAI,cAAA6B,oBAAA,uBAAnBA,oBAAA,CAAqBE,MAAM,KAAI,eAAe,EAAE;QAChG5B,OAAO,CAACD,KAAK,CAAC,sBAAsB,EAAEA,KAAK,CAACa,QAAQ,CAAC;MACvD,CAAC,MAAM,IAAIb,KAAK,CAAC8B,OAAO,EAAE;QACxB;QACAJ,YAAY,IAAI,mCAAmC;MACrD,CAAC,MAAM;QACL;QACAA,YAAY,IAAI,KAAK1B,KAAK,CAAC+B,OAAO,IAAI,oBAAoB,EAAE;MAC9D;MAEA1E,OAAO,CAACqE,YAAY,CAAC;MACrBzB,OAAO,CAACD,KAAK,CAAC,kBAAkB,EAAEA,KAAK,CAAC;;MAExC;MACAd,cAAc,GAAG,GAAG,CAAC,CAAC;;MAEtBjB,aAAa,CAAC,YAAY,CAAC;MAC3BI,WAAW,CAAC;QACVC,aAAa,EAAEY,cAAc,KAAK,GAAG,GAAG,GAAG,GAAG,EAAE;QAChDX,OAAO,EAAE,EAAE;QACXC,SAAS,EAAE,EAAE;QACbC,YAAY,EAAE,EAAE;QAChBC,OAAO,EAAE,EAAE;QACXC,YAAY,EAAE,EAAE;QAChBC,aAAa,EAAE,EAAE;QACjBC,YAAY,EAAE,aAAa;QAC3BC,iBAAiB,EAAE,EAAE;QACrBC,SAAS,EAAE,EAAE;QACbC,KAAK,EAAE,EAAE;QACTC,QAAQ,EAAE,EAAE;QACZC,cAAc,EAAEA,cAAc,CAAE;MAClC,CAAC,CAAC;MACFnB,aAAa,CAAC,IAAI,CAAC;IACrB,CAAC,SAAS;MACRN,UAAU,CAAC,KAAK,CAAC;IACnB;EACF,CAAC;;EAED;EACA,MAAMuE,mBAAmB,GAAG,MAAOC,WAAW,IAAK;IACjDhC,OAAO,CAACM,GAAG,CAAC,6BAA6B,EAAE0B,WAAW,CAAC;IACvDxE,UAAU,CAAC,IAAI,CAAC;IAEhB,IAAI;MACF;MACA,IAAIuD,gBAAgB,GAAG,GAAG;MAC1B,IAAIiB,WAAW,KAAK,GAAG,EAAE;QACvB,IAAI;UACF;UACA,MAAMvE,MAAM,GAAG,MAAMhB,gBAAgB,CAACqD,SAAS,CAAC5C,UAAU,CAAC;UAC3D,IAAIO,MAAM,IAAIA,MAAM,CAACuD,MAAM,GAAG,CAAC,EAAE;YAC/B;YACA,MAAMC,SAAS,GAAGC,IAAI,CAACC,GAAG,CAAC,GAAG1D,MAAM,CACjC2D,MAAM,CAACC,CAAC,IAAIA,CAAC,CAAChD,aAAa,IAAI,OAAO,CAACiD,IAAI,CAACD,CAAC,CAAChD,aAAa,CAAC,CAAC,CAC7DkD,GAAG,CAACF,CAAC,IAAIX,QAAQ,CAACW,CAAC,CAAChD,aAAa,EAAE,EAAE,CAAC,CAAC,CAAC;YAE3C0C,gBAAgB,GAAGN,KAAK,CAACQ,SAAS,CAAC,GAAG,GAAG,GAAGO,MAAM,CAACP,SAAS,GAAG,CAAC,CAAC;UACnE;UACAjB,OAAO,CAACM,GAAG,CAAC,yBAAyB,EAAES,gBAAgB,CAAC;QAC1D,CAAC,CAAC,OAAOhB,KAAK,EAAE;UACdC,OAAO,CAACD,KAAK,CAAC,iDAAiD,EAAEA,KAAK,CAAC;UACvE;UACAgB,gBAAgB,GAAG,GAAG;QACxB;MACF;;MAEA;MACA,MAAMkB,eAAe,GAAG;QACtB;QACA;QACA5D,aAAa,EAAE2D,WAAW,KAAK,GAAG,GAAGjB,gBAAgB,GAAG,EAAE;QAC1DzC,OAAO,EAAE,EAAE;QACXC,SAAS,EAAE,EAAE;QACbC,YAAY,EAAE,EAAE;QAChBC,OAAO,EAAE,EAAE;QACXC,YAAY,EAAE,EAAE;QAChBC,aAAa,EAAE,EAAE;QACjBC,YAAY,EAAE,aAAa;QAC3BC,iBAAiB,EAAE,EAAE;QACrBC,SAAS,EAAE,EAAE;QACbC,KAAK,EAAE,EAAE;QACTC,QAAQ,EAAE,EAAE;QACZC,cAAc,EAAE+C,WAAW,CAAC;MAC9B,CAAC;MAEDhC,OAAO,CAACM,GAAG,CAAC,gDAAgD,EAAE0B,WAAW,EAAE,gBAAgB,EAAEC,eAAe,CAAC5D,aAAa,CAAC;;MAE3H;MACA;MACAD,WAAW,CAAC6D,eAAe,CAAC;MAC5BjE,aAAa,CAAC,YAAY,CAAC;;MAE3B;MACAyB,mBAAmB,CAAC,KAAK,CAAC;;MAE1B;MACA;MACAyC,UAAU,CAAC,MAAM;QACfpE,aAAa,CAAC,IAAI,CAAC;QACnBkC,OAAO,CAACM,GAAG,CAAC,sDAAsD,EAAE2B,eAAe,CAAC5D,aAAa,CAAC;MACpG,CAAC,EAAE,GAAG,CAAC;IACT,CAAC,CAAC,OAAO0B,KAAK,EAAE;MACdC,OAAO,CAACD,KAAK,CAAC,0CAA0C,EAAEA,KAAK,CAAC;MAChE3C,OAAO,CAAC,2CAA2C,IAAI2C,KAAK,CAAC+B,OAAO,IAAI,oBAAoB,CAAC,CAAC;IAChG,CAAC,SAAS;MACRtE,UAAU,CAAC,KAAK,CAAC;IACnB;EACF,CAAC;;EAED;EACA,MAAM+C,kBAAkB,GAAI4B,MAAM,IAAK;IACrCvE,iBAAiB,CAACuE,MAAM,CAAC;IAEzB,IAAIA,MAAM,KAAK,kBAAkB,EAAE;MACjCvC,UAAU,CAAC,CAAC;IACd,CAAC,MAAM,IAAIuC,MAAM,KAAK,YAAY,EAAE;MAClC3B,qBAAqB,CAAC,CAAC;IACzB,CAAC,MAAM,IAAI2B,MAAM,KAAK,gBAAgB,EAAE;MACtCvC,UAAU,CAAC,CAAC;MACZ5B,aAAa,CAAC,iBAAiB,CAAC;MAChCF,aAAa,CAAC,IAAI,CAAC;IACrB,CAAC,MAAM,IAAIqE,MAAM,KAAK,eAAe,EAAE;MACrCvC,UAAU,CAAC,CAAC;MACZ5B,aAAa,CAAC,eAAe,CAAC;MAC9BF,aAAa,CAAC,IAAI,CAAC;IACrB,CAAC,MAAM,IAAIqE,MAAM,KAAK,mBAAmB,EAAE;MACzClC,mBAAmB,CAAC,CAAC;MACrBjC,aAAa,CAAC,mBAAmB,CAAC;MAClCF,aAAa,CAAC,IAAI,CAAC;IACrB;EACF,CAAC;;EAED;EACA,MAAMsE,iBAAiB,GAAGA,CAAA,KAAM;IAC9BtE,aAAa,CAAC,KAAK,CAAC;IACpBI,iBAAiB,CAAC,IAAI,CAAC;IACvBE,WAAW,CAAC;MACVC,aAAa,EAAE,EAAE;MACjBC,OAAO,EAAE,EAAE;MACXC,SAAS,EAAE,EAAE;MACbC,YAAY,EAAE,EAAE;MAChBC,OAAO,EAAE,EAAE;MACXC,YAAY,EAAE,EAAE;MAChBC,aAAa,EAAE,EAAE;MACjBC,YAAY,EAAE,aAAa;MAC3BC,iBAAiB,EAAE,EAAE;MACrBC,SAAS,EAAE,EAAE;MACbC,KAAK,EAAE,EAAE;MACTC,QAAQ,EAAE,EAAE;MACZC,cAAc,EAAE;IAClB,CAAC,CAAC;IACFE,aAAa,CAAC,CAAC,CAAC,CAAC;IACjBE,eAAe,CAAC,CAAC,CAAC,CAAC;EACrB,CAAC;;EAED;EACA,MAAMgD,kBAAkB,GAAIC,MAAM,IAAK;IACrCpE,iBAAiB,CAACoE,MAAM,CAAC;IACzB,IAAIvE,UAAU,KAAK,iBAAiB,EAAE;MACpCC,aAAa,CAAC,gBAAgB,CAAC;MAC/BI,WAAW,CAAC;QACVC,aAAa,EAAEiE,MAAM,CAACjE,aAAa;QACnCC,OAAO,EAAEgE,MAAM,CAAChE,OAAO,IAAI,EAAE;QAC7BC,SAAS,EAAE+D,MAAM,CAAC/D,SAAS,IAAI,EAAE;QACjCC,YAAY,EAAE8D,MAAM,CAAC9D,YAAY,IAAI,EAAE;QACvCC,OAAO,EAAE6D,MAAM,CAAC7D,OAAO,IAAI,EAAE;QAC7BC,YAAY,EAAE4D,MAAM,CAAC5D,YAAY,IAAI,EAAE;QACvCC,aAAa,EAAE2D,MAAM,CAAC3D,aAAa,IAAI,EAAE;QACzCC,YAAY,EAAE0D,MAAM,CAAC1D,YAAY,IAAI,aAAa;QAClDC,iBAAiB,EAAEyD,MAAM,CAACzD,iBAAiB,IAAI,EAAE;QACjDC,SAAS,EAAEwD,MAAM,CAACxD,SAAS,IAAI,EAAE;QACjCC,KAAK,EAAEuD,MAAM,CAACvD,KAAK,IAAI,EAAE;QACzBC,QAAQ,EAAEsD,MAAM,CAACtD,QAAQ,IAAI,EAAE;QAC/BC,cAAc,EAAEqD,MAAM,CAACrD,cAAc,IAAI;MAC3C,CAAC,CAAC;IACJ;EACF,CAAC;;EAED;EACA,MAAMsD,gBAAgB,GAAIC,CAAC,IAAK;IAC9B,MAAM;MAAEC,IAAI;MAAEC;IAAM,CAAC,GAAGF,CAAC,CAACG,MAAM;;IAEhC;IACAvE,WAAW,CAAC;MACV,GAAGD,QAAQ;MACX,CAACsE,IAAI,GAAGC;IACV,CAAC,CAAC;;IAEF;IACA,IAAI3E,UAAU,KAAK,YAAY,IAAIA,UAAU,KAAK,gBAAgB,EAAE;MAClE;MACA,IAAI0E,IAAI,KAAK,eAAe,IAAItE,QAAQ,CAACc,cAAc,KAAK,GAAG,EAAE;QAC/D,MAAM2D,QAAQ,GAAG/F,gBAAgB,CAAC6F,KAAK,CAAC;QACxC,IAAI,CAACE,QAAQ,CAACC,KAAK,EAAE;UACnB1D,aAAa,CAAC2D,IAAI,KAAK;YACrB,GAAGA,IAAI;YACP,CAACL,IAAI,GAAGG,QAAQ,CAACd;UACnB,CAAC,CAAC,CAAC;QACL,CAAC,MAAM;UACL3C,aAAa,CAAC2D,IAAI,IAAI;YACpB,MAAMC,SAAS,GAAG;cAAE,GAAGD;YAAK,CAAC;YAC7B,OAAOC,SAAS,CAACN,IAAI,CAAC;YACtB,OAAOM,SAAS;UAClB,CAAC,CAAC;QACJ;QACA;MACF;MAEA,MAAMC,MAAM,GAAGpG,mBAAmB,CAAC6F,IAAI,EAAEC,KAAK,CAAC;;MAE/C;MACA,IAAI,CAACM,MAAM,CAACH,KAAK,EAAE;QACjB1D,aAAa,CAAC2D,IAAI,KAAK;UACrB,GAAGA,IAAI;UACP,CAACL,IAAI,GAAGO,MAAM,CAAClB;QACjB,CAAC,CAAC,CAAC;MACL,CAAC,MAAM;QACL3C,aAAa,CAAC2D,IAAI,IAAI;UACpB,MAAMC,SAAS,GAAG;YAAE,GAAGD;UAAK,CAAC;UAC7B,OAAOC,SAAS,CAACN,IAAI,CAAC;UACtB,OAAOM,SAAS;QAClB,CAAC,CAAC;;QAEF;QACA,IAAIC,MAAM,CAACC,OAAO,EAAE;UAClB5D,eAAe,CAACyD,IAAI,KAAK;YACvB,GAAGA,IAAI;YACP,CAACL,IAAI,GAAGO,MAAM,CAAClB;UACjB,CAAC,CAAC,CAAC;QACL,CAAC,MAAM;UACLzC,eAAe,CAACyD,IAAI,IAAI;YACtB,MAAMI,WAAW,GAAG;cAAE,GAAGJ;YAAK,CAAC;YAC/B,OAAOI,WAAW,CAACT,IAAI,CAAC;YACxB,OAAOS,WAAW;UACpB,CAAC,CAAC;QACJ;MACF;IACF;EACF,CAAC;;EAED;EACA,MAAMC,UAAU,GAAG,MAAAA,CAAA,KAAY;IAC7B,IAAI;MACF;MACA,IAAIpF,UAAU,KAAK,YAAY,IAAIA,UAAU,KAAK,gBAAgB,EAAE;QAClE,MAAMqF,UAAU,GAAGzG,kBAAkB,CAACwB,QAAQ,CAAC;QAE/C,IAAI,CAACiF,UAAU,CAACC,OAAO,EAAE;UACvBlE,aAAa,CAACiE,UAAU,CAACE,MAAM,CAAC;UAChCjE,eAAe,CAAC+D,UAAU,CAACG,QAAQ,CAAC;UACpCnG,OAAO,CAAC,+CAA+C,CAAC;UACxD;QACF;;QAEA;QACA,IAAIe,QAAQ,CAACc,cAAc,KAAK,GAAG,KAAK,CAACd,QAAQ,CAACE,aAAa,IAAIF,QAAQ,CAACE,aAAa,CAACmF,IAAI,CAAC,CAAC,KAAK,EAAE,CAAC,EAAE;UACxGrE,aAAa,CAAC;YACZ,GAAGD,UAAU;YACbb,aAAa,EAAE;UACjB,CAAC,CAAC;UACFjB,OAAO,CAAC,mCAAmC,CAAC;UAC5C;QACF;MACF;MAEAI,UAAU,CAAC,IAAI,CAAC;MAChBwC,OAAO,CAACM,GAAG,CAAC,qCAAqC,CAAC;MAElD,IAAIvC,UAAU,KAAK,YAAY,EAAE;QAC/B;QACA,MAAM0F,UAAU,GAAG;UACjB,GAAGtF,QAAQ;UACX;UACAE,aAAa,EAAEmD,MAAM,CAACrD,QAAQ,CAACE,aAAa,IAAI,EAAE,CAAC;UACnDC,OAAO,EAAEkD,MAAM,CAACrD,QAAQ,CAACG,OAAO,IAAI,EAAE,CAAC;UACvCC,SAAS,EAAEiD,MAAM,CAACrD,QAAQ,CAACI,SAAS,IAAI,EAAE,CAAC;UAC3CC,YAAY,EAAEgD,MAAM,CAACrD,QAAQ,CAACK,YAAY,IAAI,EAAE,CAAC;UACjDC,OAAO,EAAE+C,MAAM,CAACrD,QAAQ,CAACM,OAAO,IAAI,EAAE,CAAC;UACvCC,YAAY,EAAEgF,UAAU,CAACvF,QAAQ,CAACO,YAAY,CAAC,IAAI,CAAC;UACpDG,iBAAiB,EAAE2C,MAAM,CAACrD,QAAQ,CAACU,iBAAiB,IAAI,KAAK,CAAC;UAC9DC,SAAS,EAAE0C,MAAM,CAACrD,QAAQ,CAACW,SAAS,IAAI,KAAK,CAAC;UAC9CC,KAAK,EAAEyC,MAAM,CAACrD,QAAQ,CAACY,KAAK,IAAI,KAAK,CAAC;UACtCC,QAAQ,EAAEb,QAAQ,CAACa,QAAQ,IAAI,IAAI;UACnC;UACAC,cAAc,EAAEuC,MAAM,CAACrD,QAAQ,CAACc,cAAc,IAAI,GAAG;QACvD,CAAC;QAEDe,OAAO,CAACM,GAAG,CAAC,yBAAyB,EAAEmD,UAAU,CAAC;QAElD,IAAI;UACF;UACAzD,OAAO,CAACM,GAAG,CAAC,+BAA+B,EAAEqD,IAAI,CAACC,SAAS,CAACH,UAAU,EAAE,IAAI,EAAE,CAAC,CAAC,CAAC;;UAEjF;UACA,MAAMhH,gBAAgB,CAACoH,YAAY,CAAC3G,UAAU,EAAEuG,UAAU,CAAC;UAC3DtG,SAAS,CAAC,4BAA4B,CAAC;;UAEvC;UACAiF,iBAAiB,CAAC,CAAC;;UAEnB;UACAxE,iBAAiB,CAAC,kBAAkB,CAAC;UACrCgC,UAAU,CAAC,CAAC;;UAEZ;UACA,IAAI,OAAOkE,MAAM,KAAK,WAAW,EAAE;YACjC;YACA,MAAMC,KAAK,GAAG,IAAIC,WAAW,CAAC,4BAA4B,EAAE;cAAEpC,MAAM,EAAE;gBAAE1E;cAAW;YAAE,CAAC,CAAC;YACvF4G,MAAM,CAACG,aAAa,CAACF,KAAK,CAAC;UAC7B;QACF,CAAC,CAAC,OAAOhE,KAAK,EAAE;UACdC,OAAO,CAACD,KAAK,CAAC,2CAA2C,EAAEA,KAAK,CAAC;;UAEjE;UACA,IAAI0B,YAAY,GAAG,0CAA0C;UAE7D,IAAI1B,KAAK,CAAC6B,MAAM,EAAE;YAChBH,YAAY,GAAG1B,KAAK,CAAC6B,MAAM;UAC7B,CAAC,MAAM,IAAI7B,KAAK,CAAC+B,OAAO,EAAE;YACxBL,YAAY,GAAG1B,KAAK,CAAC+B,OAAO;UAC9B;;UAEA;UACA9B,OAAO,CAACD,KAAK,CAAC,kBAAkB,EAAE0B,YAAY,CAAC;UAC/CzB,OAAO,CAACD,KAAK,CAAC,eAAe,EAAE4D,IAAI,CAACC,SAAS,CAACH,UAAU,EAAE,IAAI,EAAE,CAAC,CAAC,CAAC;UAEnErG,OAAO,CAACqE,YAAY,CAAC;UACrBjE,UAAU,CAAC,KAAK,CAAC;UACjB;QACF;MACF,CAAC,MAAM,IAAIO,UAAU,KAAK,gBAAgB,EAAE;QAC1C;QACA,MAAMmG,QAAQ,GAAGjG,cAAc,CAACkG,SAAS,IAAIhG,QAAQ,CAACE,aAAa;QACnE,MAAM5B,gBAAgB,CAAC2H,YAAY,CAAClH,UAAU,EAAEgH,QAAQ,EAAE/F,QAAQ,CAAC;QACnEhB,SAAS,CAAC,gCAAgC,CAAC;MAC7C,CAAC,MAAM,IAAIY,UAAU,KAAK,eAAe,EAAE;QACzC;QACA,MAAMmG,QAAQ,GAAGjG,cAAc,CAACkG,SAAS,IAAIlG,cAAc,CAACI,aAAa;QACzE,MAAM5B,gBAAgB,CAAC4H,YAAY,CAACnH,UAAU,EAAEgH,QAAQ,CAAC;QACzD/G,SAAS,CAAC,+BAA+B,CAAC;MAC5C;MAEAiF,iBAAiB,CAAC,CAAC;MACnBxC,UAAU,CAAC,CAAC,CAAC,CAAC;IAChB,CAAC,CAAC,OAAOG,KAAK,EAAE;MACd3C,OAAO,CAAC,gCAAgC,IAAI2C,KAAK,CAAC6B,MAAM,IAAI7B,KAAK,CAAC+B,OAAO,IAAI,oBAAoB,CAAC,CAAC;MACnG9B,OAAO,CAACD,KAAK,CAAC,+BAA+B,EAAEA,KAAK,CAAC;IACvD,CAAC,SAAS;MACRvC,UAAU,CAAC,KAAK,CAAC;IACnB;EACF,CAAC;;EAED;EACA,MAAM8G,iBAAiB,GAAGA,CAAA,KAAM;IAC9B,IAAI7G,MAAM,CAACuD,MAAM,KAAK,CAAC,EAAE;MACvB,oBACEhE,OAAA,CAAC9B,KAAK;QAACqJ,QAAQ,EAAC,MAAM;QAAAC,QAAA,EAAC;MAA0B;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAO,CAAC;IAE7D;IAEA,oBACE5H,OAAA,CAACzB,cAAc;MAACsJ,SAAS,EAAE/K,KAAM;MAACgL,EAAE,EAAE;QAAEC,EAAE,EAAE;MAAE,CAAE;MAAAP,QAAA,eAC9CxH,OAAA,CAAC5B,KAAK;QAAC4J,IAAI,EAAC,OAAO;QAAAR,QAAA,gBACjBxH,OAAA,CAACxB,SAAS;UAAAgJ,QAAA,eACRxH,OAAA,CAACvB,QAAQ;YAACqJ,EAAE,EAAE;cAAEG,OAAO,EAAE;YAAU,CAAE;YAAAT,QAAA,gBACnCxH,OAAA,CAAC1B,SAAS;cAAAkJ,QAAA,eAACxH,OAAA;gBAAAwH,QAAA,EAAQ;cAAS;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAW,CAAC,eACjD5H,OAAA,CAAC1B,SAAS;cAAAkJ,QAAA,eAACxH,OAAA;gBAAAwH,QAAA,EAAQ;cAAO;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAW,CAAC,eAC/C5H,OAAA,CAAC1B,SAAS;cAAAkJ,QAAA,eAACxH,OAAA;gBAAAwH,QAAA,EAAQ;cAAS;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAW,CAAC,eACjD5H,OAAA,CAAC1B,SAAS;cAAAkJ,QAAA,eAACxH,OAAA;gBAAAwH,QAAA,EAAQ;cAAQ;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAW,CAAC,eAChD5H,OAAA,CAAC1B,SAAS;cAAAkJ,QAAA,eAACxH,OAAA;gBAAAwH,QAAA,EAAQ;cAAO;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAW,CAAC,eAC/C5H,OAAA,CAAC1B,SAAS;cAAAkJ,QAAA,eAACxH,OAAA;gBAAAwH,QAAA,EAAQ;cAAU;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAW,CAAC,eAClD5H,OAAA,CAAC1B,SAAS;cAAAkJ,QAAA,eAACxH,OAAA;gBAAAwH,QAAA,EAAQ;cAAU;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAW,CAAC,eAClD5H,OAAA,CAAC1B,SAAS;cAAAkJ,QAAA,eAACxH,OAAA;gBAAAwH,QAAA,EAAQ;cAAK;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAW,CAAC,eAC7C5H,OAAA,CAAC1B,SAAS;cAAAkJ,QAAA,eAACxH,OAAA;gBAAAwH,QAAA,EAAQ;cAAU;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAW,CAAC,eAClD5H,OAAA,CAAC1B,SAAS;cAAAkJ,QAAA,eAACxH,OAAA;gBAAAwH,QAAA,EAAQ;cAAM;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAW,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACtC;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACF,CAAC,eACZ5H,OAAA,CAAC3B,SAAS;UAAAmJ,QAAA,EACP/G,MAAM,CAAC8D,GAAG,CAAEe,MAAM,iBACjBtF,OAAA,CAACvB,QAAQ;YAA4ByJ,KAAK;YAAAV,QAAA,gBACxCxH,OAAA,CAAC1B,SAAS;cAAAkJ,QAAA,EAAElC,MAAM,CAACjE;YAAa;cAAAoG,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAY,CAAC,eAC7C5H,OAAA,CAAC1B,SAAS;cAAAkJ,QAAA,EAAElC,MAAM,CAAChE,OAAO,IAAI;YAAK;cAAAmG,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAY,CAAC,eAChD5H,OAAA,CAAC1B,SAAS;cAAAkJ,QAAA,EAAElC,MAAM,CAAC/D,SAAS,IAAI;YAAK;cAAAkG,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAY,CAAC,eAClD5H,OAAA,CAAC1B,SAAS;cAAAkJ,QAAA,EAAElC,MAAM,CAAC9D,YAAY,IAAI;YAAK;cAAAiG,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAY,CAAC,eACrD5H,OAAA,CAAC1B,SAAS;cAAAkJ,QAAA,EAAElC,MAAM,CAAC7D,OAAO,IAAI;YAAK;cAAAgG,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAY,CAAC,eAChD5H,OAAA,CAAC1B,SAAS;cAAAkJ,QAAA,GAAElC,MAAM,CAAC5D,YAAY,IAAI,KAAK,EAAC,IAAE;YAAA;cAAA+F,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAW,CAAC,eACvD5H,OAAA,CAAC1B,SAAS;cAAAkJ,QAAA,GAAElC,MAAM,CAAC3D,aAAa,IAAI,KAAK,EAAC,IAAE;YAAA;cAAA8F,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAW,CAAC,eACxD5H,OAAA,CAAC1B,SAAS;cAAAkJ,QAAA,eACRxH,OAAA,CAACrD,GAAG;gBACFkL,SAAS,EAAC,MAAM;gBAChBC,EAAE,EAAE;kBACFK,EAAE,EAAE,CAAC;kBACLC,EAAE,EAAE,GAAG;kBACPC,YAAY,EAAE,CAAC;kBACfJ,OAAO,EAAE3C,MAAM,CAAC1D,YAAY,KAAK,aAAa,GAAG,eAAe,GACxD0D,MAAM,CAAC1D,YAAY,KAAK,QAAQ,GAAG,eAAe,GAAG,aAAa;kBAC1E0G,KAAK,EAAE,OAAO;kBACdC,QAAQ,EAAE,SAAS;kBACnBC,UAAU,EAAE;gBACd,CAAE;gBAAAhB,QAAA,EAEDlC,MAAM,CAAC1D,YAAY,IAAI;cAAa;gBAAA6F,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAClC;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACG,CAAC,eACZ5H,OAAA,CAAC1B,SAAS;cAAAkJ,QAAA,EAAElC,MAAM,CAACzD,iBAAiB,IAAI;YAAK;cAAA4F,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAY,CAAC,eAC1D5H,OAAA,CAAC1B,SAAS;cAAAkJ,QAAA,eACRxH,OAAA,CAACrD,GAAG;gBAACmL,EAAE,EAAE;kBAAEW,OAAO,EAAE,MAAM;kBAAEC,GAAG,EAAE;gBAAE,CAAE;gBAAAlB,QAAA,gBACnCxH,OAAA,CAAC2I,UAAU;kBACTX,IAAI,EAAC,OAAO;kBACZM,KAAK,EAAC,SAAS;kBACfM,OAAO,EAAEA,CAAA,KAAM;oBACb5H,aAAa,CAAC,iBAAiB,CAAC;oBAChCqE,kBAAkB,CAACC,MAAM,CAAC;kBAC5B,CAAE;kBAAAkC,QAAA,eAEFxH,OAAA,CAAClB,QAAQ;oBAACyJ,QAAQ,EAAC;kBAAO;oBAAAd,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACnB,CAAC,eACb5H,OAAA,CAAC2I,UAAU;kBACTX,IAAI,EAAC,OAAO;kBACZM,KAAK,EAAC,OAAO;kBACbM,OAAO,EAAEA,CAAA,KAAM;oBACb5H,aAAa,CAAC,eAAe,CAAC;oBAC9BE,iBAAiB,CAACoE,MAAM,CAAC;oBACzBxE,aAAa,CAAC,IAAI,CAAC;kBACrB,CAAE;kBAAA0G,QAAA,eAEFxH,OAAA,CAAChB,UAAU;oBAACuJ,QAAQ,EAAC;kBAAO;oBAAAd,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACrB,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACV;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACG,CAAC;UAAA,GAlDCtC,MAAM,CAACjE,aAAa;YAAAoG,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAmDzB,CACX;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACO,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACP;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACM,CAAC;EAErB,CAAC;;EAED;EACA,MAAMiB,YAAY,GAAGA,CAAA,KAAM;IACzB,IAAI9H,UAAU,KAAK,YAAY,IAAIA,UAAU,KAAK,gBAAgB,EAAE;MAClE,oBACEf,OAAA,CAAC7C,MAAM;QAAC2L,IAAI,EAAEjI,UAAW;QAACkI,OAAO,EAAE3D,iBAAkB;QAAC4D,QAAQ,EAAC,IAAI;QAACC,SAAS;QAAAzB,QAAA,gBAC3ExH,OAAA,CAAC5C,WAAW;UAAAoK,QAAA,EACTzG,UAAU,KAAK,YAAY,GAAG,mBAAmB,GAAG;QAAiB;UAAA0G,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC3D,CAAC,eACd5H,OAAA,CAAC3C,aAAa;UAAAmK,QAAA,GACX0B,MAAM,CAACC,IAAI,CAAC/G,YAAY,CAAC,CAAC4B,MAAM,GAAG,CAAC,iBACnChE,OAAA,CAAC9B,KAAK;YAACqJ,QAAQ,EAAC,SAAS;YAACO,EAAE,EAAE;cAAEsB,EAAE,EAAE,CAAC;cAAErB,EAAE,EAAE;YAAE,CAAE;YAAAP,QAAA,gBAC7CxH,OAAA,CAACpD,UAAU;cAACyM,OAAO,EAAC,WAAW;cAAA7B,QAAA,EAAC;YAAW;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAY,CAAC,eACxD5H,OAAA;cAAIsJ,KAAK,EAAE;gBAAEC,MAAM,EAAE,CAAC;gBAAEC,WAAW,EAAE;cAAO,CAAE;cAAAhC,QAAA,EAC3C0B,MAAM,CAACO,MAAM,CAACrH,YAAY,CAAC,CAACmC,GAAG,CAAC,CAAC0B,OAAO,EAAEyD,KAAK,kBAC9C1J,OAAA;gBAAAwH,QAAA,EAAiBvB;cAAO,GAAfyD,KAAK;gBAAAjC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAe,CAC9B;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACA,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACA,CACR,eAED5H,OAAA,CAACjD,IAAI;YAAC4M,SAAS;YAACC,OAAO,EAAE,CAAE;YAAC9B,EAAE,EAAE;cAAEC,EAAE,EAAE;YAAE,CAAE;YAAAP,QAAA,gBACxCxH,OAAA,CAACjD,IAAI;cAAC8M,IAAI;cAACC,EAAE,EAAE,EAAG;cAACC,EAAE,EAAE,CAAE;cAAAvC,QAAA,eACvBxH,OAAA,CAACzC,SAAS;gBACRkI,IAAI,EAAC,eAAe;gBACpBuE,KAAK,EAAC,WAAW;gBACjBf,SAAS;gBACTI,OAAO,EAAC,UAAU;gBAClB3D,KAAK,EAAEvE,QAAQ,CAACE,aAAc;gBAC9B4I,QAAQ,EAAE1E,gBAAiB;gBAC3B2E,QAAQ,EAAEnJ,UAAU,KAAK,gBAAgB,IAAKA,UAAU,KAAK,YAAY,IAAII,QAAQ,CAACc,cAAc,KAAK,GAAK;gBAC9GkI,QAAQ;gBACRpH,KAAK,EAAE,CAAC,CAACb,UAAU,CAACb,aAAc;gBAClC+I,UAAU,EAAE;kBACVtC,EAAE,EAAE;oBACFG,OAAO,EAAE9G,QAAQ,CAACc,cAAc,KAAK,GAAG,GAAG,SAAS,GAAG,aAAa;oBACpEuG,UAAU,EAAE;kBACd;gBACF,CAAE;gBACF6B,UAAU,EACRnI,UAAU,CAACb,aAAa,KACvBN,UAAU,KAAK,YAAY,IAAII,QAAQ,CAACc,cAAc,KAAK,GAAG,GAC3D,iBAAiB/B,UAAU,KAAKiB,QAAQ,CAACE,aAAa,IAAI,EAAE,6BAA6B,GACzFN,UAAU,KAAK,YAAY,IAAII,QAAQ,CAACc,cAAc,KAAK,GAAG,GAC5D,8DAA8D/B,UAAU,eAAe,GACvF,EAAE,CACT;gBACDoK,IAAI,EAAEnJ,QAAQ,CAACc,cAAc,KAAK,GAAG,GAAG,MAAM,GAAG;cAAO;gBAAAwF,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACzD;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACE,CAAC,eACP5H,OAAA,CAACjD,IAAI;cAAC8M,IAAI;cAACC,EAAE,EAAE,EAAG;cAACC,EAAE,EAAE,CAAE;cAAAvC,QAAA,eACvBxH,OAAA,CAACzC,SAAS;gBACRkI,IAAI,EAAC,SAAS;gBACduE,KAAK,EAAC,SAAS;gBACff,SAAS;gBACTI,OAAO,EAAC,UAAU;gBAClB3D,KAAK,EAAEvE,QAAQ,CAACG,OAAQ;gBACxB2I,QAAQ,EAAE1E,gBAAiB;gBAC3B4E,QAAQ;gBACRpH,KAAK,EAAE,CAAC,CAACb,UAAU,CAACZ,OAAQ;gBAC5B+I,UAAU,EAAEnI,UAAU,CAACZ,OAAO,IAAI;cAAG;gBAAAmG,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACtC;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACE,CAAC,eACP5H,OAAA,CAACjD,IAAI;cAAC8M,IAAI;cAACC,EAAE,EAAE,EAAG;cAACC,EAAE,EAAE,CAAE;cAAAvC,QAAA,eACvBxH,OAAA,CAACzC,SAAS;gBACRkI,IAAI,EAAC,WAAW;gBAChBuE,KAAK,EAAC,WAAW;gBACjBf,SAAS;gBACTI,OAAO,EAAC,UAAU;gBAClB3D,KAAK,EAAEvE,QAAQ,CAACI,SAAU;gBAC1B0I,QAAQ,EAAE1E,gBAAiB;gBAC3B4E,QAAQ;gBACRpH,KAAK,EAAE,CAAC,CAACb,UAAU,CAACX,SAAU;gBAC9B8I,UAAU,EAAEnI,UAAU,CAACX,SAAS,IAAI;cAAG;gBAAAkG,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACxC;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACE,CAAC,eACP5H,OAAA,CAACjD,IAAI;cAAC8M,IAAI;cAACC,EAAE,EAAE,EAAG;cAACC,EAAE,EAAE,CAAE;cAAAvC,QAAA,eACvBxH,OAAA,CAACzC,SAAS;gBACRkI,IAAI,EAAC,cAAc;gBACnBuE,KAAK,EAAC,kBAAe;gBACrBf,SAAS;gBACTI,OAAO,EAAC,UAAU;gBAClB3D,KAAK,EAAEvE,QAAQ,CAACK,YAAa;gBAC7ByI,QAAQ,EAAE1E,gBAAiB;gBAC3B4E,QAAQ;gBACRpH,KAAK,EAAE,CAAC,CAACb,UAAU,CAACV,YAAa;gBACjC6I,UAAU,EAAEnI,UAAU,CAACV,YAAY,IAAI;cAAG;gBAAAiG,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC3C;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACE,CAAC,eACP5H,OAAA,CAACjD,IAAI;cAAC8M,IAAI;cAACC,EAAE,EAAE,EAAG;cAACC,EAAE,EAAE,CAAE;cAAAvC,QAAA,eACvBxH,OAAA,CAACzC,SAAS;gBACRkI,IAAI,EAAC,SAAS;gBACduE,KAAK,EAAC,SAAS;gBACff,SAAS;gBACTI,OAAO,EAAC,UAAU;gBAClB3D,KAAK,EAAEvE,QAAQ,CAACM,OAAQ;gBACxBwI,QAAQ,EAAE1E,gBAAiB;gBAC3B4E,QAAQ;gBACRpH,KAAK,EAAE,CAAC,CAACb,UAAU,CAACT,OAAQ;gBAC5B4I,UAAU,EAAEnI,UAAU,CAACT,OAAO,IAAI;cAAG;gBAAAgG,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACtC;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACE,CAAC,eACP5H,OAAA,CAACjD,IAAI;cAAC8M,IAAI;cAACC,EAAE,EAAE,EAAG;cAACC,EAAE,EAAE,CAAE;cAAAvC,QAAA,eACvBxH,OAAA,CAACzC,SAAS;gBACRkI,IAAI,EAAC,cAAc;gBACnBuE,KAAK,EAAC,cAAc;gBACpBM,IAAI,EAAC,QAAQ;gBACbrB,SAAS;gBACTI,OAAO,EAAC,UAAU;gBAClB3D,KAAK,EAAEvE,QAAQ,CAACO,YAAa;gBAC7BuI,QAAQ,EAAE1E,gBAAiB;gBAC3B4E,QAAQ;gBACRpH,KAAK,EAAE,CAAC,CAACb,UAAU,CAACR,YAAa;gBACjC2I,UAAU,EAAEnI,UAAU,CAACR,YAAY,IAAI;cAAG;gBAAA+F,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC3C;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACE,CAAC,EACN7G,UAAU,KAAK,gBAAgB,iBAC9Bf,OAAA,CAACjD,IAAI;cAAC8M,IAAI;cAACC,EAAE,EAAE,EAAG;cAACC,EAAE,EAAE,CAAE;cAAAvC,QAAA,eACvBxH,OAAA,CAACzC,SAAS;gBACRkI,IAAI,EAAC,eAAe;gBACpBuE,KAAK,EAAC,eAAe;gBACrBM,IAAI,EAAC,QAAQ;gBACbrB,SAAS;gBACTI,OAAO,EAAC,UAAU;gBAClB3D,KAAK,EAAEvE,QAAQ,CAACQ,aAAc;gBAC9BsI,QAAQ,EAAE1E,gBAAiB;gBAC3B4E,QAAQ;gBACRD,QAAQ,EAAE,IAAK;gBACfG,UAAU,EAAC;cAA4D;gBAAA5C,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACxE;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACE,CACP,eACD5H,OAAA,CAACjD,IAAI;cAAC8M,IAAI;cAACC,EAAE,EAAE,EAAG;cAACC,EAAE,EAAE,CAAE;cAAAvC,QAAA,eACvBxH,OAAA,CAACxC,WAAW;gBAACyL,SAAS;gBAAAzB,QAAA,gBACpBxH,OAAA,CAACvC,UAAU;kBAAC8M,EAAE,EAAC,oBAAoB;kBAAA/C,QAAA,EAAC;gBAAY;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAY,CAAC,eAC7D5H,OAAA,CAACtC,MAAM;kBACL8M,OAAO,EAAC,oBAAoB;kBAC5B/E,IAAI,EAAC,cAAc;kBACnBC,KAAK,EAAEvE,QAAQ,CAACS,YAAa;kBAC7BoI,KAAK,EAAC,cAAc;kBACpBC,QAAQ,EAAE1E,gBAAiB;kBAC3B2E,QAAQ,EAAEnJ,UAAU,KAAK,YAAa;kBAAAyG,QAAA,gBAEtCxH,OAAA,CAACrC,QAAQ;oBAAC+H,KAAK,EAAC,aAAa;oBAAA8B,QAAA,EAAC;kBAAW;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAU,CAAC,eACpD5H,OAAA,CAACrC,QAAQ;oBAAC+H,KAAK,EAAC,QAAQ;oBAAA8B,QAAA,EAAC;kBAAM;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAU,CAAC,eAC1C5H,OAAA,CAACrC,QAAQ;oBAAC+H,KAAK,EAAC,WAAW;oBAAA8B,QAAA,EAAC;kBAAS;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAU,CAAC,eAChD5H,OAAA,CAACrC,QAAQ;oBAAC+H,KAAK,EAAC,aAAa;oBAAA8B,QAAA,EAAC;kBAAW;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAU,CAAC,eACpD5H,OAAA,CAACrC,QAAQ;oBAAC+H,KAAK,EAAC,MAAM;oBAAA8B,QAAA,EAAC;kBAAI;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAU,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAChC,CAAC,EACR7G,UAAU,KAAK,YAAY,iBAC1Bf,OAAA,CAACtB,cAAc;kBAAA8I,QAAA,EAAC;gBAAqD;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAgB,CACtF;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACU;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACV,CAAC,eACP5H,OAAA,CAACjD,IAAI;cAAC8M,IAAI;cAACC,EAAE,EAAE,EAAG;cAACC,EAAE,EAAE,CAAE;cAAAvC,QAAA,eACvBxH,OAAA,CAACzC,SAAS;gBACRkI,IAAI,EAAC,mBAAmB;gBACxBuE,KAAK,EAAC,mBAAmB;gBACzBf,SAAS;gBACTI,OAAO,EAAC,UAAU;gBAClB3D,KAAK,EAAEvE,QAAQ,CAACU,iBAAkB;gBAClCoI,QAAQ,EAAE1E,gBAAiB;gBAC3BxC,KAAK,EAAE,CAAC,CAACb,UAAU,CAACL,iBAAkB;gBACtCwI,UAAU,EAAEnI,UAAU,CAACL,iBAAiB,IAAI;cAAG;gBAAA4F,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAChD;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACE,CAAC,eACP5H,OAAA,CAACjD,IAAI;cAAC8M,IAAI;cAACC,EAAE,EAAE,EAAG;cAACC,EAAE,EAAE,CAAE;cAAAvC,QAAA,eACvBxH,OAAA,CAACzC,SAAS;gBACRkI,IAAI,EAAC,WAAW;gBAChBuE,KAAK,EAAC,WAAW;gBACjBf,SAAS;gBACTI,OAAO,EAAC,UAAU;gBAClB3D,KAAK,EAAEvE,QAAQ,CAACW,SAAU;gBAC1BmI,QAAQ,EAAE1E,gBAAiB;gBAC3BxC,KAAK,EAAE,CAAC,CAACb,UAAU,CAACJ,SAAU;gBAC9BuI,UAAU,EAAEnI,UAAU,CAACJ,SAAS,IAAI;cAAG;gBAAA2F,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACxC;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACE,CAAC,eACP5H,OAAA,CAACjD,IAAI;cAAC8M,IAAI;cAACC,EAAE,EAAE,EAAG;cAACC,EAAE,EAAE,CAAE;cAAAvC,QAAA,eACvBxH,OAAA,CAACzC,SAAS;gBACRkI,IAAI,EAAC,OAAO;gBACZuE,KAAK,EAAC,YAAY;gBAClBf,SAAS;gBACTI,OAAO,EAAC,UAAU;gBAClB3D,KAAK,EAAEvE,QAAQ,CAACY,KAAM;gBACtBkI,QAAQ,EAAE1E,gBAAiB;gBAC3BxC,KAAK,EAAE,CAAC,CAACb,UAAU,CAACH,KAAM;gBAC1BsI,UAAU,EAAEnI,UAAU,CAACH,KAAK,IAAI;cAAG;gBAAA0F,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACpC;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACE,CAAC,eACP5H,OAAA,CAACjD,IAAI;cAAC8M,IAAI;cAACC,EAAE,EAAE,EAAG;cAACC,EAAE,EAAE,CAAE;cAAAvC,QAAA,eACvBxH,OAAA,CAACzC,SAAS;gBACRkI,IAAI,EAAC,UAAU;gBACfuE,KAAK,EAAC,uBAAuB;gBAC7Bf,SAAS;gBACTI,OAAO,EAAC,UAAU;gBAClB3D,KAAK,EAAEvE,QAAQ,CAACa,QAAS;gBACzBiI,QAAQ,EAAE1E,gBAAiB;gBAC3BkF,WAAW,EAAC,YAAY;gBACxB1H,KAAK,EAAE,CAAC,CAACb,UAAU,CAACF,QAAS;gBAC7BqI,UAAU,EAAEnI,UAAU,CAACF,QAAQ,IAAI;cAAG;gBAAAyF,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACvC;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACE,CAAC,eACP5H,OAAA,CAACjD,IAAI;cAAC8M,IAAI;cAACC,EAAE,EAAE,EAAG;cAACC,EAAE,EAAE,CAAE;cAAAvC,QAAA,eACvBxH,OAAA,CAACzC,SAAS;gBACRkI,IAAI,EAAC,gBAAgB;gBACrBuE,KAAK,EAAC,yBAAsB;gBAC5Bf,SAAS;gBACTI,OAAO,EAAC,UAAU;gBAClB3D,KAAK,EAAEvE,QAAQ,CAACc,cAAc,KAAK,GAAG,GAAG,YAAY,GAAG,SAAU;gBAClEmI,UAAU,EAAE;kBACVM,QAAQ,EAAE,IAAI;kBACd5C,EAAE,EAAE;oBACFG,OAAO,EAAE;kBACX;gBACF,CAAE;gBACFoC,UAAU,eACRrK,OAAA,CAACrD,GAAG;kBAACmL,EAAE,EAAE;oBAAEU,UAAU,EAAE,QAAQ;oBAAEF,KAAK,EAAEnH,QAAQ,CAACc,cAAc,KAAK,GAAG,GAAG,cAAc,GAAG;kBAAY,CAAE;kBAAAuF,QAAA,EACtGrG,QAAQ,CAACc,cAAc,KAAK,GAAG,GAC5B,mDAAmD,GACnD;gBAA6D;kBAAAwF,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAC9D;cACN;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACF;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACE,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACM,CAAC,eAChB5H,OAAA,CAAC1C,aAAa;UAAAkK,QAAA,gBACZxH,OAAA,CAACnD,MAAM;YAAC+L,OAAO,EAAExD,iBAAkB;YAAAoC,QAAA,EAAC;UAAO;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC,eACpD5H,OAAA,CAACnD,MAAM;YACL+L,OAAO,EAAEzC,UAAW;YACpB+D,QAAQ,EAAE3J,OAAO,IAAI2I,MAAM,CAACC,IAAI,CAACjH,UAAU,CAAC,CAAC8B,MAAM,GAAG,CAAE;YACxD2G,SAAS,EAAEpK,OAAO,gBAAGP,OAAA,CAAC7B,gBAAgB;cAAC6J,IAAI,EAAE;YAAG;cAAAP,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,gBAAG5H,OAAA,CAACZ,QAAQ;cAAAqI,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAE;YACnEyB,OAAO,EAAC,WAAW;YACnBf,KAAK,EAAC,SAAS;YAAAd,QAAA,EAChB;UAED;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACI,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACV,CAAC;IAEb,CAAC,MAAM,IAAI7G,UAAU,KAAK,iBAAiB,EAAE;MAC3C,oBACEf,OAAA,CAAC7C,MAAM;QAAC2L,IAAI,EAAEjI,UAAW;QAACkI,OAAO,EAAE3D,iBAAkB;QAAC4D,QAAQ,EAAC,IAAI;QAACC,SAAS;QAAAzB,QAAA,gBAC3ExH,OAAA,CAAC5C,WAAW;UAAAoK,QAAA,EAAC;QAA8B;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAa,CAAC,eACzD5H,OAAA,CAAC3C,aAAa;UAAAmK,QAAA,EACXjH,OAAO,gBACNP,OAAA,CAAC7B,gBAAgB;YAAAsJ,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,GAClBnH,MAAM,CAACuD,MAAM,KAAK,CAAC,gBACrBhE,OAAA,CAAC9B,KAAK;YAACqJ,QAAQ,EAAC,MAAM;YAAAC,QAAA,EAAC;UAA0B;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAO,CAAC,gBAEzD5H,OAAA,CAACpC,IAAI;YAAA4J,QAAA,EACF/G,MAAM,CAAC8D,GAAG,CAAEe,MAAM,iBACjBtF,OAAA,CAACnC,QAAQ;cACP+M,MAAM;cAENhC,OAAO,EAAEA,CAAA,KAAMvD,kBAAkB,CAACC,MAAM,CAAE;cAAAkC,QAAA,eAE1CxH,OAAA,CAAClC,YAAY;gBACX+M,OAAO,EAAE,WAAWvF,MAAM,CAACjE,aAAa,EAAG;gBAC3CyJ,SAAS,EAAE,cAAcxF,MAAM,CAAC/D,SAAS,IAAI,KAAK,eAAe+D,MAAM,CAAChE,OAAO,IAAI,KAAK,eAAegE,MAAM,CAAC3D,aAAa,IAAI,KAAK;cAAK;gBAAA8F,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC1I;YAAC,GANGtC,MAAM,CAACjE,aAAa;cAAAoG,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAOjB,CACX;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACE;QACP;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACY,CAAC,eAChB5H,OAAA,CAAC1C,aAAa;UAAAkK,QAAA,eACZxH,OAAA,CAACnD,MAAM;YAAC+L,OAAO,EAAExD,iBAAkB;YAAAoC,QAAA,EAAC;UAAO;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACvC,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACV,CAAC;IAEb,CAAC,MAAM,IAAI7G,UAAU,KAAK,eAAe,EAAE;MACzC,oBACEf,OAAA,CAAC7C,MAAM;QAAC2L,IAAI,EAAEjI,UAAW;QAACkI,OAAO,EAAE3D,iBAAkB;QAAC4D,QAAQ,EAAC,IAAI;QAACC,SAAS;QAAAzB,QAAA,gBAC3ExH,OAAA,CAAC5C,WAAW;UAAAoK,QAAA,EAAC;QAAc;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAa,CAAC,eACzC5H,OAAA,CAAC3C,aAAa;UAAAmK,QAAA,EACX,CAACvG,cAAc,GACdV,OAAO,gBACLP,OAAA,CAAC7B,gBAAgB;YAAAsJ,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,GAClBnH,MAAM,CAACuD,MAAM,KAAK,CAAC,gBACrBhE,OAAA,CAAC9B,KAAK;YAACqJ,QAAQ,EAAC,MAAM;YAAAC,QAAA,EAAC;UAA0B;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAO,CAAC,gBAEzD5H,OAAA,CAACpC,IAAI;YAAA4J,QAAA,EACF/G,MAAM,CAAC8D,GAAG,CAAEe,MAAM,iBACjBtF,OAAA,CAACnC,QAAQ;cACP+M,MAAM;cAENhC,OAAO,EAAEA,CAAA,KAAM1H,iBAAiB,CAACoE,MAAM,CAAE;cAAAkC,QAAA,eAEzCxH,OAAA,CAAClC,YAAY;gBACX+M,OAAO,EAAE,WAAWvF,MAAM,CAACjE,aAAa,EAAG;gBAC3CyJ,SAAS,EAAE,cAAcxF,MAAM,CAAC/D,SAAS,IAAI,KAAK,eAAe+D,MAAM,CAAChE,OAAO,IAAI,KAAK,eAAegE,MAAM,CAAC3D,aAAa,IAAI,KAAK;cAAK;gBAAA8F,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC1I;YAAC,GANGtC,MAAM,CAACjE,aAAa;cAAAoG,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAOjB,CACX;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACE,CACP,gBAED5H,OAAA,CAACrD,GAAG;YAAA6K,QAAA,gBACFxH,OAAA,CAAC9B,KAAK;cAACqJ,QAAQ,EAAC,SAAS;cAACO,EAAE,EAAE;gBAAEsB,EAAE,EAAE;cAAE,CAAE;cAAA5B,QAAA,GAAC,0CACC,EAACvG,cAAc,CAACI,aAAa,EAAC,GACxE;YAAA;cAAAoG,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CAAC,eACR5H,OAAA,CAACpD,UAAU;cAACyM,OAAO,EAAC,OAAO;cAAA7B,QAAA,EAAC;YAE5B;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAY,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACV;QACN;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACY,CAAC,eAChB5H,OAAA,CAAC1C,aAAa;UAAAkK,QAAA,gBACZxH,OAAA,CAACnD,MAAM;YAAC+L,OAAO,EAAExD,iBAAkB;YAAAoC,QAAA,EAAC;UAAO;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC,EACnD3G,cAAc,iBACbjB,OAAA,CAACnD,MAAM;YACL+L,OAAO,EAAEzC,UAAW;YACpB+D,QAAQ,EAAE3J,OAAQ;YAClB+H,KAAK,EAAC,OAAO;YACbqC,SAAS,EAAEpK,OAAO,gBAAGP,OAAA,CAAC7B,gBAAgB;cAAC6J,IAAI,EAAE;YAAG;cAAAP,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,gBAAG5H,OAAA,CAAChB,UAAU;cAAAyI,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAE;YAAAJ,QAAA,EACtE;UAED;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CACT;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACY,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACV,CAAC;IAEb,CAAC,MAAM,IAAI7G,UAAU,KAAK,mBAAmB,EAAE;MAC7C,oBACEf,OAAA,CAAC7C,MAAM;QAAC2L,IAAI,EAAEjI,UAAW;QAACkI,OAAO,EAAE3D,iBAAkB;QAAC4D,QAAQ,EAAC,IAAI;QAACC,SAAS;QAAAzB,QAAA,gBAC3ExH,OAAA,CAAC5C,WAAW;UAAAoK,QAAA,EAAC;QAAuB;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAa,CAAC,eAClD5H,OAAA,CAAC3C,aAAa;UAAAmK,QAAA,EACXjH,OAAO,gBACNP,OAAA,CAAC7B,gBAAgB;YAAAsJ,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,GAClBtF,eAAe,CAAC0B,MAAM,KAAK,CAAC,gBAC9BhE,OAAA,CAAC9B,KAAK;YAACqJ,QAAQ,EAAC,MAAM;YAAAC,QAAA,EAAC;UAA+B;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAO,CAAC,gBAE9D5H,OAAA,CAACzB,cAAc;YAACsJ,SAAS,EAAE/K,KAAM;YAACgL,EAAE,EAAE;cAAEC,EAAE,EAAE;YAAE,CAAE;YAAAP,QAAA,eAC9CxH,OAAA,CAAC5B,KAAK;cAAC4J,IAAI,EAAC,OAAO;cAAAR,QAAA,gBACjBxH,OAAA,CAACxB,SAAS;gBAAAgJ,QAAA,eACRxH,OAAA,CAACvB,QAAQ;kBAAA+I,QAAA,gBACPxH,OAAA,CAAC1B,SAAS;oBAAAkJ,QAAA,EAAC;kBAAM;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAW,CAAC,eAC7B5H,OAAA,CAAC1B,SAAS;oBAAAkJ,QAAA,EAAC;kBAAO;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAW,CAAC,eAC9B5H,OAAA,CAAC1B,SAAS;oBAAAkJ,QAAA,EAAC;kBAAS;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAW,CAAC,eAChC5H,OAAA,CAAC1B,SAAS;oBAAAkJ,QAAA,EAAC;kBAAa;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAW,CAAC,eACpC5H,OAAA,CAAC1B,SAAS;oBAAAkJ,QAAA,EAAC;kBAAO;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAW,CAAC,eAC9B5H,OAAA,CAAC1B,SAAS;oBAAAkJ,QAAA,EAAC;kBAAY;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAW,CAAC,eACnC5H,OAAA,CAAC1B,SAAS;oBAAAkJ,QAAA,EAAC;kBAAa;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAW,CAAC,eACpC5H,OAAA,CAAC1B,SAAS;oBAAAkJ,QAAA,EAAC;kBAAc;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAW,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAC7B;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACF,CAAC,eACZ5H,OAAA,CAAC3B,SAAS;gBAAAmJ,QAAA,EACPlF,eAAe,CAACiC,GAAG,CAAC,CAACwG,MAAM,EAAErB,KAAK,kBACjC1J,OAAA,CAACvB,QAAQ;kBAAA+I,QAAA,gBACPxH,OAAA,CAAC1B,SAAS;oBAAAkJ,QAAA,EAAEuD,MAAM,CAAC1J;kBAAa;oBAAAoG,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAY,CAAC,eAC7C5H,OAAA,CAAC1B,SAAS;oBAAAkJ,QAAA,EAAEuD,MAAM,CAACzJ;kBAAO;oBAAAmG,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAY,CAAC,eACvC5H,OAAA,CAAC1B,SAAS;oBAAAkJ,QAAA,EAAEuD,MAAM,CAACxJ;kBAAS;oBAAAkG,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAY,CAAC,eACzC5H,OAAA,CAAC1B,SAAS;oBAAAkJ,QAAA,EAAEuD,MAAM,CAACvJ;kBAAY;oBAAAiG,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAY,CAAC,eAC5C5H,OAAA,CAAC1B,SAAS;oBAAAkJ,QAAA,EAAEuD,MAAM,CAACtJ;kBAAO;oBAAAgG,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAY,CAAC,eACvC5H,OAAA,CAAC1B,SAAS;oBAAAkJ,QAAA,EAAEuD,MAAM,CAACrJ;kBAAY;oBAAA+F,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAY,CAAC,eAC5C5H,OAAA,CAAC1B,SAAS;oBAAAkJ,QAAA,EAAEuD,MAAM,CAACpJ;kBAAa;oBAAA8F,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAY,CAAC,eAC7C5H,OAAA,CAAC1B,SAAS;oBAAAkJ,QAAA,EAAEuD,MAAM,CAACC,IAAI,CAAChH;kBAAM;oBAAAyD,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAY,CAAC;gBAAA,GAR9B8B,KAAK;kBAAAjC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OASV,CACX;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACO,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACP;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACM;QACjB;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACY,CAAC,eAChB5H,OAAA,CAAC1C,aAAa;UAAAkK,QAAA,eACZxH,OAAA,CAACnD,MAAM;YAAC+L,OAAO,EAAExD,iBAAkB;YAAAoC,QAAA,EAAC;UAAM;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACtC,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACV,CAAC;IAEb;IAEA,OAAO,IAAI;EACb,CAAC;EAED,oBACE5H,OAAA,CAACrD,GAAG;IAAA6K,QAAA,GACD7G,cAAc,KAAK,kBAAkB,IAAI,CAACE,UAAU,gBACnDb,OAAA,CAAClD,KAAK;MAACgL,EAAE,EAAE;QAAEmD,CAAC,EAAE;MAAE,CAAE;MAAAzD,QAAA,gBAClBxH,OAAA,CAACrD,GAAG;QAACmL,EAAE,EAAE;UAAEW,OAAO,EAAE,MAAM;UAAEyC,cAAc,EAAE,UAAU;UAAEC,UAAU,EAAE,QAAQ;UAAE/B,EAAE,EAAE;QAAE,CAAE;QAAA5B,QAAA,eACpFxH,OAAA,CAACrD,GAAG;UAACmL,EAAE,EAAE;YAAEW,OAAO,EAAE,MAAM;YAAEC,GAAG,EAAE;UAAE,CAAE;UAAAlB,QAAA,gBACnCxH,OAAA,CAACnD,MAAM;YACLwM,OAAO,EAAC,UAAU;YAClBf,KAAK,EAAC,SAAS;YACfqC,SAAS,eAAE3K,OAAA,CAACd,WAAW;cAAAuI,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAE;YAC3BgB,OAAO,EAAEA,CAAA,KAAM;cACb3F,mBAAmB,CAAC,CAAC;cACrBjC,aAAa,CAAC,mBAAmB,CAAC;cAClCF,aAAa,CAAC,IAAI,CAAC;YACrB,CAAE;YACFgH,EAAE,EAAE;cACFU,UAAU,EAAE,QAAQ;cACpBH,YAAY,EAAE;YAChB,CAAE;YAAAb,QAAA,EACH;UAED;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC,eACT5H,OAAA,CAACnD,MAAM;YACLwM,OAAO,EAAC,WAAW;YACnBf,KAAK,EAAC,SAAS;YACfqC,SAAS,eAAE3K,OAAA,CAACpB,OAAO;cAAA6I,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAE;YACvBgB,OAAO,EAAEA,CAAA,KAAMpF,qBAAqB,CAAC,CAAE;YACvCsE,EAAE,EAAE;cACFU,UAAU,EAAE,QAAQ;cACpBH,YAAY,EAAE;YAChB,CAAE;YAAAb,QAAA,EACH;UAED;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACN;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,EACLrH,OAAO,gBACNP,OAAA,CAACrD,GAAG;QAACmL,EAAE,EAAE;UAAEW,OAAO,EAAE,MAAM;UAAEyC,cAAc,EAAE,QAAQ;UAAEE,EAAE,EAAE;QAAE,CAAE;QAAA5D,QAAA,eAC5DxH,OAAA,CAAC7B,gBAAgB;UAAAsJ,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACjB,CAAC,GAENN,iBAAiB,CAAC,CACnB;IAAA;MAAAG,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACI,CAAC,GACN,CAAC/G,UAAU,gBACbb,OAAA,CAAClD,KAAK;MAACgL,EAAE,EAAE;QAAEmD,CAAC,EAAE,CAAC;QAAEI,SAAS,EAAE,OAAO;QAAE5C,OAAO,EAAE,MAAM;QAAE0C,UAAU,EAAE,QAAQ;QAAED,cAAc,EAAE;MAAS,CAAE;MAAA1D,QAAA,EACtG,CAAC7G,cAAc,gBACdX,OAAA,CAACpD,UAAU;QAACyM,OAAO,EAAC,OAAO;QAAA7B,QAAA,EAAC;MAE5B;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAY,CAAC,gBAEb5H,OAAA,CAACrD,GAAG;QAACmL,EAAE,EAAE;UAAEwD,SAAS,EAAE;QAAS,CAAE;QAAA9D,QAAA,gBAC/BxH,OAAA,CAACpD,UAAU;UAACyM,OAAO,EAAC,IAAI;UAACkC,YAAY;UAAA/D,QAAA,GAClC7G,cAAc,KAAK,YAAY,IAAI,mBAAmB,EACtDA,cAAc,KAAK,gBAAgB,IAAI,iBAAiB,EACxDA,cAAc,KAAK,eAAe,IAAI,gBAAgB,EACtDA,cAAc,KAAK,mBAAmB,IAAI,6BAA6B;QAAA;UAAA8G,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC9D,CAAC,eACb5H,OAAA,CAAC7B,gBAAgB;UAAC2J,EAAE,EAAE;YAAEC,EAAE,EAAE;UAAE;QAAE;UAAAN,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAChC;IACN;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACI,CAAC,GACN,IAAI,EAEPiB,YAAY,CAAC,CAAC,eAGf7I,OAAA,CAACN,oBAAoB;MACnBoJ,IAAI,EAAEtG,gBAAiB;MACvBuG,OAAO,EAAEA,CAAA,KAAMtG,mBAAmB,CAAC,KAAK,CAAE;MAC1C+I,SAAS,EAAEzG;IAAoB;MAAA0C,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAChC,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACC,CAAC;AAEV,CAAC;AAACtH,EAAA,CAljCIL,SAAS;AAAAwL,EAAA,GAATxL,SAAS;AAojCf,eAAeA,SAAS;AAAC,IAAAwL,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}