{"ast": null, "code": "import { buildLocalizeFn } from \"../../_lib/buildLocalizeFn.mjs\";\n\n// https://www.unicode.org/cldr/charts/32/summary/sk.html#1772\nconst eraValues = {\n  narrow: [\"pred Kr.\", \"po Kr.\"],\n  abbreviated: [\"pred Kr.\", \"po Kr.\"],\n  wide: [\"pred <PERSON><PERSON>\", \"po <PERSON><PERSON><PERSON>\"]\n};\n\n// https://www.unicode.org/cldr/charts/32/summary/sk.html#1780\nconst quarterValues = {\n  narrow: [\"1\", \"2\", \"3\", \"4\"],\n  abbreviated: [\"Q1\", \"Q2\", \"Q3\", \"Q4\"],\n  wide: [\"1. štvrťrok\", \"2. štvrťrok\", \"3. štvrťrok\", \"4. štvrťrok\"]\n};\n\n// https://www.unicode.org/cldr/charts/32/summary/sk.html#1804\nconst monthValues = {\n  narrow: [\"j\", \"f\", \"m\", \"a\", \"m\", \"j\", \"j\", \"a\", \"s\", \"o\", \"n\", \"d\"],\n  abbreviated: [\"jan\", \"feb\", \"mar\", \"apr\", \"máj\", \"jún\", \"júl\", \"aug\", \"sep\", \"okt\", \"nov\", \"dec\"],\n  wide: [\"január\", \"február\", \"marec\", \"apríl\", \"máj\", \"jún\", \"júl\", \"august\", \"september\", \"október\", \"november\", \"december\"]\n};\nconst formattingMonthValues = {\n  narrow: [\"j\", \"f\", \"m\", \"a\", \"m\", \"j\", \"j\", \"a\", \"s\", \"o\", \"n\", \"d\"],\n  abbreviated: [\"jan\", \"feb\", \"mar\", \"apr\", \"máj\", \"jún\", \"júl\", \"aug\", \"sep\", \"okt\", \"nov\", \"dec\"],\n  wide: [\"januára\", \"februára\", \"marca\", \"apríla\", \"mája\", \"júna\", \"júla\", \"augusta\", \"septembra\", \"októbra\", \"novembra\", \"decembra\"]\n};\n\n// https://www.unicode.org/cldr/charts/32/summary/sk.html#1876\nconst dayValues = {\n  narrow: [\"n\", \"p\", \"u\", \"s\", \"š\", \"p\", \"s\"],\n  short: [\"ne\", \"po\", \"ut\", \"st\", \"št\", \"pi\", \"so\"],\n  abbreviated: [\"ne\", \"po\", \"ut\", \"st\", \"št\", \"pi\", \"so\"],\n  wide: [\"nedeľa\", \"pondelok\", \"utorok\", \"streda\", \"štvrtok\", \"piatok\", \"sobota\"]\n};\n\n// https://www.unicode.org/cldr/charts/32/summary/sk.html#1932\nconst dayPeriodValues = {\n  narrow: {\n    am: \"AM\",\n    pm: \"PM\",\n    midnight: \"poln.\",\n    noon: \"pol.\",\n    morning: \"ráno\",\n    afternoon: \"pop.\",\n    evening: \"več.\",\n    night: \"noc\"\n  },\n  abbreviated: {\n    am: \"AM\",\n    pm: \"PM\",\n    midnight: \"poln.\",\n    noon: \"pol.\",\n    morning: \"ráno\",\n    afternoon: \"popol.\",\n    evening: \"večer\",\n    night: \"noc\"\n  },\n  wide: {\n    am: \"AM\",\n    pm: \"PM\",\n    midnight: \"polnoc\",\n    noon: \"poludnie\",\n    morning: \"ráno\",\n    afternoon: \"popoludnie\",\n    evening: \"večer\",\n    night: \"noc\"\n  }\n};\nconst formattingDayPeriodValues = {\n  narrow: {\n    am: \"AM\",\n    pm: \"PM\",\n    midnight: \"o poln.\",\n    noon: \"nap.\",\n    morning: \"ráno\",\n    afternoon: \"pop.\",\n    evening: \"več.\",\n    night: \"v n.\"\n  },\n  abbreviated: {\n    am: \"AM\",\n    pm: \"PM\",\n    midnight: \"o poln.\",\n    noon: \"napol.\",\n    morning: \"ráno\",\n    afternoon: \"popol.\",\n    evening: \"večer\",\n    night: \"v noci\"\n  },\n  wide: {\n    am: \"AM\",\n    pm: \"PM\",\n    midnight: \"o polnoci\",\n    noon: \"napoludnie\",\n    morning: \"ráno\",\n    afternoon: \"popoludní\",\n    evening: \"večer\",\n    night: \"v noci\"\n  }\n};\nconst ordinalNumber = (dirtyNumber, _options) => {\n  const number = Number(dirtyNumber);\n  return number + \".\";\n};\nexport const localize = {\n  ordinalNumber,\n  era: buildLocalizeFn({\n    values: eraValues,\n    defaultWidth: \"wide\"\n  }),\n  quarter: buildLocalizeFn({\n    values: quarterValues,\n    defaultWidth: \"wide\",\n    argumentCallback: quarter => quarter - 1\n  }),\n  month: buildLocalizeFn({\n    values: monthValues,\n    defaultWidth: \"wide\",\n    formattingValues: formattingMonthValues,\n    defaultFormattingWidth: \"wide\"\n  }),\n  day: buildLocalizeFn({\n    values: dayValues,\n    defaultWidth: \"wide\"\n  }),\n  dayPeriod: buildLocalizeFn({\n    values: dayPeriodValues,\n    defaultWidth: \"wide\",\n    formattingValues: formattingDayPeriodValues,\n    defaultFormattingWidth: \"wide\"\n  })\n};", "map": {"version": 3, "names": ["buildLocalizeFn", "<PERSON><PERSON><PERSON><PERSON>", "narrow", "abbreviated", "wide", "quarterValues", "month<PERSON><PERSON><PERSON>", "formattingMonthValues", "dayV<PERSON><PERSON>", "short", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "am", "pm", "midnight", "noon", "morning", "afternoon", "evening", "night", "formattingDayPeriodValues", "ordinalNumber", "dirtyNumber", "_options", "number", "Number", "localize", "era", "values", "defaultWidth", "quarter", "argument<PERSON>allback", "month", "formattingValues", "defaultFormattingWidth", "day", "<PERSON><PERSON><PERSON><PERSON>"], "sources": ["C:/CMS/webapp/frontend/node_modules/date-fns/locale/sk/_lib/localize.mjs"], "sourcesContent": ["import { buildLocalizeFn } from \"../../_lib/buildLocalizeFn.mjs\";\n\n// https://www.unicode.org/cldr/charts/32/summary/sk.html#1772\nconst eraValues = {\n  narrow: [\"pred Kr.\", \"po Kr.\"],\n  abbreviated: [\"pred Kr.\", \"po Kr.\"],\n  wide: [\"pred <PERSON><PERSON>\", \"po <PERSON><PERSON><PERSON>\"],\n};\n\n// https://www.unicode.org/cldr/charts/32/summary/sk.html#1780\nconst quarterValues = {\n  narrow: [\"1\", \"2\", \"3\", \"4\"],\n  abbreviated: [\"Q1\", \"Q2\", \"Q3\", \"Q4\"],\n  wide: [\"1. štvrťrok\", \"2. štvrťrok\", \"3. štvrťrok\", \"4. štvrťrok\"],\n};\n\n// https://www.unicode.org/cldr/charts/32/summary/sk.html#1804\nconst monthValues = {\n  narrow: [\"j\", \"f\", \"m\", \"a\", \"m\", \"j\", \"j\", \"a\", \"s\", \"o\", \"n\", \"d\"],\n  abbreviated: [\n    \"jan\",\n    \"feb\",\n    \"mar\",\n    \"apr\",\n    \"máj\",\n    \"jún\",\n    \"júl\",\n    \"aug\",\n    \"sep\",\n    \"okt\",\n    \"nov\",\n    \"dec\",\n  ],\n\n  wide: [\n    \"január\",\n    \"február\",\n    \"marec\",\n    \"apríl\",\n    \"máj\",\n    \"jún\",\n    \"júl\",\n    \"august\",\n    \"september\",\n    \"október\",\n    \"november\",\n    \"december\",\n  ],\n};\nconst formattingMonthValues = {\n  narrow: [\"j\", \"f\", \"m\", \"a\", \"m\", \"j\", \"j\", \"a\", \"s\", \"o\", \"n\", \"d\"],\n  abbreviated: [\n    \"jan\",\n    \"feb\",\n    \"mar\",\n    \"apr\",\n    \"máj\",\n    \"jún\",\n    \"júl\",\n    \"aug\",\n    \"sep\",\n    \"okt\",\n    \"nov\",\n    \"dec\",\n  ],\n\n  wide: [\n    \"januára\",\n    \"februára\",\n    \"marca\",\n    \"apríla\",\n    \"mája\",\n    \"júna\",\n    \"júla\",\n    \"augusta\",\n    \"septembra\",\n    \"októbra\",\n    \"novembra\",\n    \"decembra\",\n  ],\n};\n\n// https://www.unicode.org/cldr/charts/32/summary/sk.html#1876\nconst dayValues = {\n  narrow: [\"n\", \"p\", \"u\", \"s\", \"š\", \"p\", \"s\"],\n  short: [\"ne\", \"po\", \"ut\", \"st\", \"št\", \"pi\", \"so\"],\n  abbreviated: [\"ne\", \"po\", \"ut\", \"st\", \"št\", \"pi\", \"so\"],\n  wide: [\n    \"nedeľa\",\n    \"pondelok\",\n    \"utorok\",\n    \"streda\",\n    \"štvrtok\",\n    \"piatok\",\n    \"sobota\",\n  ],\n};\n\n// https://www.unicode.org/cldr/charts/32/summary/sk.html#1932\nconst dayPeriodValues = {\n  narrow: {\n    am: \"AM\",\n    pm: \"PM\",\n    midnight: \"poln.\",\n    noon: \"pol.\",\n    morning: \"ráno\",\n    afternoon: \"pop.\",\n    evening: \"več.\",\n    night: \"noc\",\n  },\n  abbreviated: {\n    am: \"AM\",\n    pm: \"PM\",\n    midnight: \"poln.\",\n    noon: \"pol.\",\n    morning: \"ráno\",\n    afternoon: \"popol.\",\n    evening: \"večer\",\n    night: \"noc\",\n  },\n  wide: {\n    am: \"AM\",\n    pm: \"PM\",\n    midnight: \"polnoc\",\n    noon: \"poludnie\",\n    morning: \"ráno\",\n    afternoon: \"popoludnie\",\n    evening: \"večer\",\n    night: \"noc\",\n  },\n};\n\nconst formattingDayPeriodValues = {\n  narrow: {\n    am: \"AM\",\n    pm: \"PM\",\n    midnight: \"o poln.\",\n    noon: \"nap.\",\n    morning: \"ráno\",\n    afternoon: \"pop.\",\n    evening: \"več.\",\n    night: \"v n.\",\n  },\n  abbreviated: {\n    am: \"AM\",\n    pm: \"PM\",\n    midnight: \"o poln.\",\n    noon: \"napol.\",\n    morning: \"ráno\",\n    afternoon: \"popol.\",\n    evening: \"večer\",\n    night: \"v noci\",\n  },\n  wide: {\n    am: \"AM\",\n    pm: \"PM\",\n    midnight: \"o polnoci\",\n    noon: \"napoludnie\",\n    morning: \"ráno\",\n    afternoon: \"popoludní\",\n    evening: \"večer\",\n    night: \"v noci\",\n  },\n};\n\nconst ordinalNumber = (dirtyNumber, _options) => {\n  const number = Number(dirtyNumber);\n  return number + \".\";\n};\n\nexport const localize = {\n  ordinalNumber,\n\n  era: buildLocalizeFn({\n    values: eraValues,\n    defaultWidth: \"wide\",\n  }),\n\n  quarter: buildLocalizeFn({\n    values: quarterValues,\n    defaultWidth: \"wide\",\n    argumentCallback: (quarter) => quarter - 1,\n  }),\n\n  month: buildLocalizeFn({\n    values: monthValues,\n    defaultWidth: \"wide\",\n    formattingValues: formattingMonthValues,\n    defaultFormattingWidth: \"wide\",\n  }),\n\n  day: buildLocalizeFn({\n    values: dayValues,\n    defaultWidth: \"wide\",\n  }),\n\n  dayPeriod: buildLocalizeFn({\n    values: dayPeriodValues,\n    defaultWidth: \"wide\",\n    formattingValues: formattingDayPeriodValues,\n    defaultFormattingWidth: \"wide\",\n  }),\n};\n"], "mappings": "AAAA,SAASA,eAAe,QAAQ,gCAAgC;;AAEhE;AACA,MAAMC,SAAS,GAAG;EAChBC,MAAM,EAAE,CAAC,UAAU,EAAE,QAAQ,CAAC;EAC9BC,WAAW,EAAE,CAAC,UAAU,EAAE,QAAQ,CAAC;EACnCC,IAAI,EAAE,CAAC,cAAc,EAAE,aAAa;AACtC,CAAC;;AAED;AACA,MAAMC,aAAa,GAAG;EACpBH,MAAM,EAAE,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,CAAC;EAC5BC,WAAW,EAAE,CAAC,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,CAAC;EACrCC,IAAI,EAAE,CAAC,aAAa,EAAE,aAAa,EAAE,aAAa,EAAE,aAAa;AACnE,CAAC;;AAED;AACA,MAAME,WAAW,GAAG;EAClBJ,MAAM,EAAE,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,CAAC;EACpEC,WAAW,EAAE,CACX,KAAK,EACL,KAAK,EACL,KAAK,EACL,KAAK,EACL,KAAK,EACL,KAAK,EACL,KAAK,EACL,KAAK,EACL,KAAK,EACL,KAAK,EACL,KAAK,EACL,KAAK,CACN;EAEDC,IAAI,EAAE,CACJ,QAAQ,EACR,SAAS,EACT,OAAO,EACP,OAAO,EACP,KAAK,EACL,KAAK,EACL,KAAK,EACL,QAAQ,EACR,WAAW,EACX,SAAS,EACT,UAAU,EACV,UAAU;AAEd,CAAC;AACD,MAAMG,qBAAqB,GAAG;EAC5BL,MAAM,EAAE,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,CAAC;EACpEC,WAAW,EAAE,CACX,KAAK,EACL,KAAK,EACL,KAAK,EACL,KAAK,EACL,KAAK,EACL,KAAK,EACL,KAAK,EACL,KAAK,EACL,KAAK,EACL,KAAK,EACL,KAAK,EACL,KAAK,CACN;EAEDC,IAAI,EAAE,CACJ,SAAS,EACT,UAAU,EACV,OAAO,EACP,QAAQ,EACR,MAAM,EACN,MAAM,EACN,MAAM,EACN,SAAS,EACT,WAAW,EACX,SAAS,EACT,UAAU,EACV,UAAU;AAEd,CAAC;;AAED;AACA,MAAMI,SAAS,GAAG;EAChBN,MAAM,EAAE,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,CAAC;EAC3CO,KAAK,EAAE,CAAC,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,CAAC;EACjDN,WAAW,EAAE,CAAC,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,CAAC;EACvDC,IAAI,EAAE,CACJ,QAAQ,EACR,UAAU,EACV,QAAQ,EACR,QAAQ,EACR,SAAS,EACT,QAAQ,EACR,QAAQ;AAEZ,CAAC;;AAED;AACA,MAAMM,eAAe,GAAG;EACtBR,MAAM,EAAE;IACNS,EAAE,EAAE,IAAI;IACRC,EAAE,EAAE,IAAI;IACRC,QAAQ,EAAE,OAAO;IACjBC,IAAI,EAAE,MAAM;IACZC,OAAO,EAAE,MAAM;IACfC,SAAS,EAAE,MAAM;IACjBC,OAAO,EAAE,MAAM;IACfC,KAAK,EAAE;EACT,CAAC;EACDf,WAAW,EAAE;IACXQ,EAAE,EAAE,IAAI;IACRC,EAAE,EAAE,IAAI;IACRC,QAAQ,EAAE,OAAO;IACjBC,IAAI,EAAE,MAAM;IACZC,OAAO,EAAE,MAAM;IACfC,SAAS,EAAE,QAAQ;IACnBC,OAAO,EAAE,OAAO;IAChBC,KAAK,EAAE;EACT,CAAC;EACDd,IAAI,EAAE;IACJO,EAAE,EAAE,IAAI;IACRC,EAAE,EAAE,IAAI;IACRC,QAAQ,EAAE,QAAQ;IAClBC,IAAI,EAAE,UAAU;IAChBC,OAAO,EAAE,MAAM;IACfC,SAAS,EAAE,YAAY;IACvBC,OAAO,EAAE,OAAO;IAChBC,KAAK,EAAE;EACT;AACF,CAAC;AAED,MAAMC,yBAAyB,GAAG;EAChCjB,MAAM,EAAE;IACNS,EAAE,EAAE,IAAI;IACRC,EAAE,EAAE,IAAI;IACRC,QAAQ,EAAE,SAAS;IACnBC,IAAI,EAAE,MAAM;IACZC,OAAO,EAAE,MAAM;IACfC,SAAS,EAAE,MAAM;IACjBC,OAAO,EAAE,MAAM;IACfC,KAAK,EAAE;EACT,CAAC;EACDf,WAAW,EAAE;IACXQ,EAAE,EAAE,IAAI;IACRC,EAAE,EAAE,IAAI;IACRC,QAAQ,EAAE,SAAS;IACnBC,IAAI,EAAE,QAAQ;IACdC,OAAO,EAAE,MAAM;IACfC,SAAS,EAAE,QAAQ;IACnBC,OAAO,EAAE,OAAO;IAChBC,KAAK,EAAE;EACT,CAAC;EACDd,IAAI,EAAE;IACJO,EAAE,EAAE,IAAI;IACRC,EAAE,EAAE,IAAI;IACRC,QAAQ,EAAE,WAAW;IACrBC,IAAI,EAAE,YAAY;IAClBC,OAAO,EAAE,MAAM;IACfC,SAAS,EAAE,WAAW;IACtBC,OAAO,EAAE,OAAO;IAChBC,KAAK,EAAE;EACT;AACF,CAAC;AAED,MAAME,aAAa,GAAGA,CAACC,WAAW,EAAEC,QAAQ,KAAK;EAC/C,MAAMC,MAAM,GAAGC,MAAM,CAACH,WAAW,CAAC;EAClC,OAAOE,MAAM,GAAG,GAAG;AACrB,CAAC;AAED,OAAO,MAAME,QAAQ,GAAG;EACtBL,aAAa;EAEbM,GAAG,EAAE1B,eAAe,CAAC;IACnB2B,MAAM,EAAE1B,SAAS;IACjB2B,YAAY,EAAE;EAChB,CAAC,CAAC;EAEFC,OAAO,EAAE7B,eAAe,CAAC;IACvB2B,MAAM,EAAEtB,aAAa;IACrBuB,YAAY,EAAE,MAAM;IACpBE,gBAAgB,EAAGD,OAAO,IAAKA,OAAO,GAAG;EAC3C,CAAC,CAAC;EAEFE,KAAK,EAAE/B,eAAe,CAAC;IACrB2B,MAAM,EAAErB,WAAW;IACnBsB,YAAY,EAAE,MAAM;IACpBI,gBAAgB,EAAEzB,qBAAqB;IACvC0B,sBAAsB,EAAE;EAC1B,CAAC,CAAC;EAEFC,GAAG,EAAElC,eAAe,CAAC;IACnB2B,MAAM,EAAEnB,SAAS;IACjBoB,YAAY,EAAE;EAChB,CAAC,CAAC;EAEFO,SAAS,EAAEnC,eAAe,CAAC;IACzB2B,MAAM,EAAEjB,eAAe;IACvBkB,YAAY,EAAE,MAAM;IACpBI,gBAAgB,EAAEb,yBAAyB;IAC3Cc,sBAAsB,EAAE;EAC1B,CAAC;AACH,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}