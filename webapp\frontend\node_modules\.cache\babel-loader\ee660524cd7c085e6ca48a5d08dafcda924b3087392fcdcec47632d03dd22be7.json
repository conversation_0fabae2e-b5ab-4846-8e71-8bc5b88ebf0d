{"ast": null, "code": "import { toDate } from \"./toDate.mjs\";\n\n/**\n * @name setMilliseconds\n * @category Millisecond Helpers\n * @summary Set the milliseconds to the given date.\n *\n * @description\n * Set the milliseconds to the given date.\n *\n * @typeParam DateType - The `Date` type, the function operates on. Gets inferred from passed arguments. Allows to use extensions like [`UTCDate`](https://github.com/date-fns/utc).\n *\n * @param date - The date to be changed\n * @param milliseconds - The milliseconds of the new date\n *\n * @returns The new date with the milliseconds set\n *\n * @example\n * // Set 300 milliseconds to 1 September 2014 11:30:40.500:\n * const result = setMilliseconds(new Date(2014, 8, 1, 11, 30, 40, 500), 300)\n * //=> Mon Sep 01 2014 11:30:40.300\n */\nexport function setMilliseconds(date, milliseconds) {\n  const _date = toDate(date);\n  _date.setMilliseconds(milliseconds);\n  return _date;\n}\n\n// Fallback for modularized imports:\nexport default setMilliseconds;", "map": {"version": 3, "names": ["toDate", "setMilliseconds", "date", "milliseconds", "_date"], "sources": ["C:/CMS/webapp/frontend/node_modules/date-fns/setMilliseconds.mjs"], "sourcesContent": ["import { toDate } from \"./toDate.mjs\";\n\n/**\n * @name setMilliseconds\n * @category Millisecond Helpers\n * @summary Set the milliseconds to the given date.\n *\n * @description\n * Set the milliseconds to the given date.\n *\n * @typeParam DateType - The `Date` type, the function operates on. Gets inferred from passed arguments. Allows to use extensions like [`UTCDate`](https://github.com/date-fns/utc).\n *\n * @param date - The date to be changed\n * @param milliseconds - The milliseconds of the new date\n *\n * @returns The new date with the milliseconds set\n *\n * @example\n * // Set 300 milliseconds to 1 September 2014 11:30:40.500:\n * const result = setMilliseconds(new Date(2014, 8, 1, 11, 30, 40, 500), 300)\n * //=> Mon Sep 01 2014 11:30:40.300\n */\nexport function setMilliseconds(date, milliseconds) {\n  const _date = toDate(date);\n  _date.setMilliseconds(milliseconds);\n  return _date;\n}\n\n// Fallback for modularized imports:\nexport default setMilliseconds;\n"], "mappings": "AAAA,SAASA,MAAM,QAAQ,cAAc;;AAErC;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO,SAASC,eAAeA,CAACC,IAAI,EAAEC,YAAY,EAAE;EAClD,MAAMC,KAAK,GAAGJ,MAAM,CAACE,IAAI,CAAC;EAC1BE,KAAK,CAACH,eAAe,CAACE,YAAY,CAAC;EACnC,OAAOC,KAAK;AACd;;AAEA;AACA,eAAeH,eAAe", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}