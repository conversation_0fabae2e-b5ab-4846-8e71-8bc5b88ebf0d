{"ast": null, "code": "var formatDistanceLocale = {\n  lessThanXSeconds: {\n    one: 'meno di un secondo',\n    other: 'meno di {{count}} secondi'\n  },\n  xSeconds: {\n    one: 'un secondo',\n    other: '{{count}} secondi'\n  },\n  halfAMinute: 'alcuni secondi',\n  lessThanXMinutes: {\n    one: 'meno di un minuto',\n    other: 'meno di {{count}} minuti'\n  },\n  xMinutes: {\n    one: 'un minuto',\n    other: '{{count}} minuti'\n  },\n  aboutXHours: {\n    one: \"circa un'ora\",\n    other: 'circa {{count}} ore'\n  },\n  xHours: {\n    one: \"un'ora\",\n    other: '{{count}} ore'\n  },\n  xDays: {\n    one: 'un giorno',\n    other: '{{count}} giorni'\n  },\n  aboutXWeeks: {\n    one: 'circa una settimana',\n    other: 'circa {{count}} settimane'\n  },\n  xWeeks: {\n    one: 'una settimana',\n    other: '{{count}} settimane'\n  },\n  aboutXMonths: {\n    one: 'circa un mese',\n    other: 'circa {{count}} mesi'\n  },\n  xMonths: {\n    one: 'un mese',\n    other: '{{count}} mesi'\n  },\n  aboutXYears: {\n    one: 'circa un anno',\n    other: 'circa {{count}} anni'\n  },\n  xYears: {\n    one: 'un anno',\n    other: '{{count}} anni'\n  },\n  overXYears: {\n    one: 'più di un anno',\n    other: 'più di {{count}} anni'\n  },\n  almostXYears: {\n    one: 'quasi un anno',\n    other: 'quasi {{count}} anni'\n  }\n};\nvar formatDistance = function formatDistance(token, count, options) {\n  var result;\n  var tokenValue = formatDistanceLocale[token];\n  if (typeof tokenValue === 'string') {\n    result = tokenValue;\n  } else if (count === 1) {\n    result = tokenValue.one;\n  } else {\n    result = tokenValue.other.replace('{{count}}', count.toString());\n  }\n  if (options !== null && options !== void 0 && options.addSuffix) {\n    if (options.comparison && options.comparison > 0) {\n      return 'tra ' + result;\n    } else {\n      return result + ' fa';\n    }\n  }\n  return result;\n};\nexport default formatDistance;", "map": {"version": 3, "names": ["formatDistanceLocale", "lessThanXSeconds", "one", "other", "xSeconds", "halfAMinute", "lessThanXMinutes", "xMinutes", "aboutXHours", "xHours", "xDays", "aboutXWeeks", "xWeeks", "aboutXMonths", "xMonths", "aboutXYears", "xYears", "overXYears", "almostXYears", "formatDistance", "token", "count", "options", "result", "tokenValue", "replace", "toString", "addSuffix", "comparison"], "sources": ["C:/CMS/webapp/frontend/node_modules/date-fns/esm/locale/it/_lib/formatDistance/index.js"], "sourcesContent": ["var formatDistanceLocale = {\n  lessThanXSeconds: {\n    one: 'meno di un secondo',\n    other: 'meno di {{count}} secondi'\n  },\n  xSeconds: {\n    one: 'un secondo',\n    other: '{{count}} secondi'\n  },\n  halfAMinute: 'alcuni secondi',\n  lessThanXMinutes: {\n    one: 'meno di un minuto',\n    other: 'meno di {{count}} minuti'\n  },\n  xMinutes: {\n    one: 'un minuto',\n    other: '{{count}} minuti'\n  },\n  aboutXHours: {\n    one: \"circa un'ora\",\n    other: 'circa {{count}} ore'\n  },\n  xHours: {\n    one: \"un'ora\",\n    other: '{{count}} ore'\n  },\n  xDays: {\n    one: 'un giorno',\n    other: '{{count}} giorni'\n  },\n  aboutXWeeks: {\n    one: 'circa una settimana',\n    other: 'circa {{count}} settimane'\n  },\n  xWeeks: {\n    one: 'una settimana',\n    other: '{{count}} settimane'\n  },\n  aboutXMonths: {\n    one: 'circa un mese',\n    other: 'circa {{count}} mesi'\n  },\n  xMonths: {\n    one: 'un mese',\n    other: '{{count}} mesi'\n  },\n  aboutXYears: {\n    one: 'circa un anno',\n    other: 'circa {{count}} anni'\n  },\n  xYears: {\n    one: 'un anno',\n    other: '{{count}} anni'\n  },\n  overXYears: {\n    one: 'più di un anno',\n    other: 'più di {{count}} anni'\n  },\n  almostXYears: {\n    one: 'quasi un anno',\n    other: 'quasi {{count}} anni'\n  }\n};\nvar formatDistance = function formatDistance(token, count, options) {\n  var result;\n  var tokenValue = formatDistanceLocale[token];\n  if (typeof tokenValue === 'string') {\n    result = tokenValue;\n  } else if (count === 1) {\n    result = tokenValue.one;\n  } else {\n    result = tokenValue.other.replace('{{count}}', count.toString());\n  }\n  if (options !== null && options !== void 0 && options.addSuffix) {\n    if (options.comparison && options.comparison > 0) {\n      return 'tra ' + result;\n    } else {\n      return result + ' fa';\n    }\n  }\n  return result;\n};\nexport default formatDistance;"], "mappings": "AAAA,IAAIA,oBAAoB,GAAG;EACzBC,gBAAgB,EAAE;IAChBC,GAAG,EAAE,oBAAoB;IACzBC,KAAK,EAAE;EACT,CAAC;EACDC,QAAQ,EAAE;IACRF,GAAG,EAAE,YAAY;IACjBC,KAAK,EAAE;EACT,CAAC;EACDE,WAAW,EAAE,gBAAgB;EAC7BC,gBAAgB,EAAE;IAChBJ,GAAG,EAAE,mBAAmB;IACxBC,KAAK,EAAE;EACT,CAAC;EACDI,QAAQ,EAAE;IACRL,GAAG,EAAE,WAAW;IAChBC,KAAK,EAAE;EACT,CAAC;EACDK,WAAW,EAAE;IACXN,GAAG,EAAE,cAAc;IACnBC,KAAK,EAAE;EACT,CAAC;EACDM,MAAM,EAAE;IACNP,GAAG,EAAE,QAAQ;IACbC,KAAK,EAAE;EACT,CAAC;EACDO,KAAK,EAAE;IACLR,GAAG,EAAE,WAAW;IAChBC,KAAK,EAAE;EACT,CAAC;EACDQ,WAAW,EAAE;IACXT,GAAG,EAAE,qBAAqB;IAC1BC,KAAK,EAAE;EACT,CAAC;EACDS,MAAM,EAAE;IACNV,GAAG,EAAE,eAAe;IACpBC,KAAK,EAAE;EACT,CAAC;EACDU,YAAY,EAAE;IACZX,GAAG,EAAE,eAAe;IACpBC,KAAK,EAAE;EACT,CAAC;EACDW,OAAO,EAAE;IACPZ,GAAG,EAAE,SAAS;IACdC,KAAK,EAAE;EACT,CAAC;EACDY,WAAW,EAAE;IACXb,GAAG,EAAE,eAAe;IACpBC,KAAK,EAAE;EACT,CAAC;EACDa,MAAM,EAAE;IACNd,GAAG,EAAE,SAAS;IACdC,KAAK,EAAE;EACT,CAAC;EACDc,UAAU,EAAE;IACVf,GAAG,EAAE,gBAAgB;IACrBC,KAAK,EAAE;EACT,CAAC;EACDe,YAAY,EAAE;IACZhB,GAAG,EAAE,eAAe;IACpBC,KAAK,EAAE;EACT;AACF,CAAC;AACD,IAAIgB,cAAc,GAAG,SAASA,cAAcA,CAACC,KAAK,EAAEC,KAAK,EAAEC,OAAO,EAAE;EAClE,IAAIC,MAAM;EACV,IAAIC,UAAU,GAAGxB,oBAAoB,CAACoB,KAAK,CAAC;EAC5C,IAAI,OAAOI,UAAU,KAAK,QAAQ,EAAE;IAClCD,MAAM,GAAGC,UAAU;EACrB,CAAC,MAAM,IAAIH,KAAK,KAAK,CAAC,EAAE;IACtBE,MAAM,GAAGC,UAAU,CAACtB,GAAG;EACzB,CAAC,MAAM;IACLqB,MAAM,GAAGC,UAAU,CAACrB,KAAK,CAACsB,OAAO,CAAC,WAAW,EAAEJ,KAAK,CAACK,QAAQ,CAAC,CAAC,CAAC;EAClE;EACA,IAAIJ,OAAO,KAAK,IAAI,IAAIA,OAAO,KAAK,KAAK,CAAC,IAAIA,OAAO,CAACK,SAAS,EAAE;IAC/D,IAAIL,OAAO,CAACM,UAAU,IAAIN,OAAO,CAACM,UAAU,GAAG,CAAC,EAAE;MAChD,OAAO,MAAM,GAAGL,MAAM;IACxB,CAAC,MAAM;MACL,OAAOA,MAAM,GAAG,KAAK;IACvB;EACF;EACA,OAAOA,MAAM;AACf,CAAC;AACD,eAAeJ,cAAc", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}