{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\PycharmProjects\\\\CMS\\\\webapp\\\\frontend\\\\src\\\\components\\\\cavi\\\\CavoForm.js\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect } from 'react';\nimport { Box, TextField, Button, Stack, MenuItem, Alert, CircularProgress, Typography, Divider, Paper, IconButton, Tooltip } from '@mui/material';\nimport { Save as SaveIcon, Warning as WarningIcon, Cancel as CancelIcon, KeyboardArrowDown as KeyboardArrowDownIcon, KeyboardArrowUp as KeyboardArrowUpIcon } from '@mui/icons-material';\nimport { useNavigate } from 'react-router-dom';\nimport { validateCavoData, validateField } from '../../utils/validationUtils';\nimport { redirectToVisualizzaCavi } from '../../utils/navigationUtils';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst SECTION_CONFIG = [{\n  title: 'Informazioni Generali',\n  collapsible: false,\n  fields: [{\n    name: 'id_cavo',\n    label: 'ID Cavo',\n    required: true,\n    inputProps: {\n      style: {\n        textTransform: 'uppercase'\n      }\n    }\n  }, {\n    name: 'utility',\n    label: 'Utility',\n    required: true\n  }, {\n    name: 'sistema',\n    label: 'Sistema'\n  }]\n}, {\n  title: 'Caratteristiche Tecniche',\n  collapsible: false,\n  fields: [{\n    name: 'colore_cavo',\n    label: 'Colore Cavo'\n  }, {\n    name: 'tipologia',\n    label: 'Tipologia'\n  }, {\n    name: 'n_conduttori',\n    label: 'Numero Conduttori'\n  }, {\n    name: 'sezione',\n    label: 'Sezione'\n  }, {\n    name: 'sh',\n    label: 'Schermato (S/N)',\n    type: 'select',\n    options: ['S', 'N'],\n    defaultValue: 'N'\n  }]\n}, {\n  title: 'Partenza',\n  collapsible: true,\n  fields: [{\n    name: 'ubicazione_partenza',\n    label: 'Ubicazione Partenza'\n  }, {\n    name: 'utenza_partenza',\n    label: 'Utenza Partenza'\n  }, {\n    name: 'descrizione_utenza_partenza',\n    label: 'Descrizione Utenza Partenza'\n  }]\n}, {\n  title: 'Arrivo',\n  collapsible: true,\n  fields: [{\n    name: 'ubicazione_arrivo',\n    label: 'Ubicazione Arrivo'\n  }, {\n    name: 'utenza_arrivo',\n    label: 'Utenza Arrivo'\n  }, {\n    name: 'descrizione_utenza_arrivo',\n    label: 'Descrizione Utenza Arrivo'\n  }]\n}, {\n  title: 'Metratura',\n  collapsible: false,\n  fields: [{\n    name: 'metri_teorici',\n    label: 'Metri Teorici',\n    required: true\n  }, {\n    name: 'metratura_reale',\n    label: 'Metratura Reale'\n  }]\n}];\nconst defaultData = {\n  id_cavo: '',\n  utility: '',\n  sistema: '',\n  colore_cavo: '',\n  tipologia: '',\n  n_conduttori: '',\n  sezione: '',\n  sh: 'N',\n  ubicazione_partenza: '',\n  utenza_partenza: '',\n  descrizione_utenza_partenza: '',\n  ubicazione_arrivo: '',\n  utenza_arrivo: '',\n  descrizione_utenza_arrivo: '',\n  metri_teorici: '',\n  metratura_reale: '0'\n};\nconst CavoForm = ({\n  mode = 'add',\n  initialData = {},\n  onSubmit,\n  onSuccess,\n  onError,\n  onCancel\n}) => {\n  _s();\n  const navigate = useNavigate();\n  const [formData, setFormData] = useState({\n    ...defaultData,\n    ...initialData\n  });\n  const [formErrors, setFormErrors] = useState({});\n  const [warnings, setWarnings] = useState({});\n  const [loading, setLoading] = useState(false);\n  const [expanded, setExpanded] = useState({});\n  const [showWarnings, setShowWarnings] = useState(false);\n\n  // Inizializza lo stato di espansione delle sezioni collassabili\n  useEffect(() => {\n    const state = {};\n    SECTION_CONFIG.forEach(s => {\n      if (s.collapsible) state[s.title] = true;\n    });\n    setExpanded(state);\n  }, []);\n\n  // Gestisce l'espansione/collasso delle sezioni\n  const toggleExpand = title => {\n    setExpanded(prev => ({\n      ...prev,\n      [title]: !prev[title]\n    }));\n  };\n\n  // Gestisce i cambiamenti nei campi del form\n  const handleChange = e => {\n    var _result$message;\n    const {\n      name,\n      value\n    } = e.target;\n    setFormData(prev => ({\n      ...prev,\n      [name]: value\n    }));\n\n    // Validazione in tempo reale\n    const extra = name === 'metratura_reale' ? {\n      metriTeorici: parseFloat(formData.metri_teorici || 0)\n    } : {};\n    const result = validateField(name, value, extra);\n\n    // Assicurati che gli errori siano sempre stringhe o null\n    const errorMessage = result.valid ? null : typeof result.message === 'object' ? ((_result$message = result.message) === null || _result$message === void 0 ? void 0 : _result$message.message) || JSON.stringify(result.message) : result.message;\n    setFormErrors(prev => ({\n      ...prev,\n      [name]: errorMessage\n    }));\n    setWarnings(prev => {\n      var _result$message2;\n      // Assicurati che i warning siano sempre stringhe o null\n      const warningMessage = result.warning ? typeof result.message === 'object' ? ((_result$message2 = result.message) === null || _result$message2 === void 0 ? void 0 : _result$message2.message) || JSON.stringify(result.message) : result.message : null;\n      const newWarnings = {\n        ...prev,\n        [name]: warningMessage\n      };\n      // Mostra il banner di avviso se ci sono warning\n      setShowWarnings(Object.values(newWarnings).some(w => w));\n      return newWarnings;\n    });\n  };\n\n  // Gestisce l'invio del form\n  const handleSubmit = async e => {\n    e.preventDefault();\n    setLoading(true);\n    const validation = validateCavoData(formData);\n    if (!validation.isValid) {\n      setFormErrors(validation.errors);\n      setWarnings(validation.warnings);\n      setShowWarnings(Object.values(validation.warnings).some(w => w));\n      setLoading(false);\n      onError('Ci sono errori nel form.');\n      return;\n    }\n    try {\n      const finalData = {\n        ...validation.validatedData,\n        id_cavo: validation.validatedData.id_cavo.toUpperCase()\n      };\n      await onSubmit(finalData);\n      setLoading(false);\n      onSuccess(`Cavo ${mode === 'add' ? 'aggiunto' : 'modificato'} con successo.`);\n      redirectToVisualizzaCavi(navigate);\n    } catch (err) {\n      setLoading(false);\n      onError(err.message);\n    }\n  };\n\n  // Gestisce l'annullamento del form\n  const handleCancel = () => {\n    if (onCancel) {\n      onCancel();\n    } else {\n      redirectToVisualizzaCavi(navigate);\n    }\n  };\n\n  // Renderizza un singolo campo del form\n  const renderField = field => {\n    var _formErrors$field$nam, _warnings$field$name, _field$options;\n    return /*#__PURE__*/_jsxDEV(TextField, {\n      select: field.type === 'select',\n      fullWidth: true,\n      size: \"small\",\n      margin: \"dense\",\n      label: field.label,\n      name: field.name,\n      value: formData[field.name],\n      onChange: handleChange,\n      error: !!formErrors[field.name],\n      helperText: typeof formErrors[field.name] === 'object' ? ((_formErrors$field$nam = formErrors[field.name]) === null || _formErrors$field$nam === void 0 ? void 0 : _formErrors$field$nam.message) || JSON.stringify(formErrors[field.name]) : formErrors[field.name] || (typeof warnings[field.name] === 'object' ? ((_warnings$field$name = warnings[field.name]) === null || _warnings$field$name === void 0 ? void 0 : _warnings$field$name.message) || JSON.stringify(warnings[field.name]) : warnings[field.name]),\n      required: field.required,\n      variant: \"outlined\",\n      InputLabelProps: {\n        shrink: true\n      },\n      ...(field.inputProps || {}),\n      children: (_field$options = field.options) === null || _field$options === void 0 ? void 0 : _field$options.map(opt => /*#__PURE__*/_jsxDEV(MenuItem, {\n        value: opt,\n        children: opt\n      }, opt, false, {\n        fileName: _jsxFileName,\n        lineNumber: 204,\n        columnNumber: 9\n      }, this))\n    }, field.name, false, {\n      fileName: _jsxFileName,\n      lineNumber: 186,\n      columnNumber: 5\n    }, this);\n  };\n  return /*#__PURE__*/_jsxDEV(Paper, {\n    elevation: 0,\n    sx: {\n      border: '1px solid #e0e0e0',\n      borderRadius: 2,\n      overflow: 'hidden'\n    },\n    children: /*#__PURE__*/_jsxDEV(Box, {\n      component: \"form\",\n      onSubmit: handleSubmit,\n      noValidate: true,\n      children: [showWarnings && /*#__PURE__*/_jsxDEV(Alert, {\n        severity: \"warning\",\n        icon: /*#__PURE__*/_jsxDEV(WarningIcon, {}, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 223,\n          columnNumber: 19\n        }, this),\n        sx: {\n          borderRadius: 0,\n          py: 0.5\n        },\n        children: \"Alcuni campi potrebbero necessitare revisione\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 221,\n        columnNumber: 11\n      }, this), SECTION_CONFIG.map((section, index) => {\n        const isExpanded = section.collapsible ? expanded[section.title] : true;\n        return /*#__PURE__*/_jsxDEV(Box, {\n          sx: {\n            borderBottom: index < SECTION_CONFIG.length - 1 ? '1px solid #e0e0e0' : 'none'\n          },\n          children: [/*#__PURE__*/_jsxDEV(Box, {\n            sx: {\n              display: 'flex',\n              justifyContent: 'space-between',\n              alignItems: 'center',\n              px: 2,\n              py: 1.5,\n              bgcolor: '#f5f5f5',\n              borderBottom: isExpanded ? '1px solid #e0e0e0' : 'none',\n              cursor: section.collapsible ? 'pointer' : 'default'\n            },\n            onClick: () => section.collapsible && toggleExpand(section.title),\n            children: [/*#__PURE__*/_jsxDEV(Typography, {\n              variant: \"subtitle1\",\n              sx: {\n                fontWeight: 600,\n                color: '#333',\n                fontSize: '1rem'\n              },\n              children: section.title\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 253,\n              columnNumber: 17\n            }, this), section.collapsible && /*#__PURE__*/_jsxDEV(IconButton, {\n              size: \"small\",\n              sx: {\n                ml: 1\n              },\n              onClick: e => {\n                e.stopPropagation();\n                toggleExpand(section.title);\n              },\n              children: isExpanded ? /*#__PURE__*/_jsxDEV(KeyboardArrowUpIcon, {}, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 273,\n                columnNumber: 35\n              }, this) : /*#__PURE__*/_jsxDEV(KeyboardArrowDownIcon, {}, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 273,\n                columnNumber: 61\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 265,\n              columnNumber: 19\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 240,\n            columnNumber: 15\n          }, this), isExpanded && /*#__PURE__*/_jsxDEV(Box, {\n            sx: {\n              p: 2\n            },\n            children: /*#__PURE__*/_jsxDEV(Stack, {\n              spacing: 1.5,\n              children: section.fields.map(field => renderField(field))\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 281,\n              columnNumber: 19\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 280,\n            columnNumber: 17\n          }, this)]\n        }, section.title, true, {\n          fileName: _jsxFileName,\n          lineNumber: 238,\n          columnNumber: 13\n        }, this);\n      }), /*#__PURE__*/_jsxDEV(Box, {\n        sx: {\n          display: 'flex',\n          justifyContent: 'flex-end',\n          gap: 1.5,\n          p: 2,\n          bgcolor: '#f9f9f9',\n          borderTop: '1px solid #e0e0e0'\n        },\n        children: [/*#__PURE__*/_jsxDEV(Tooltip, {\n          title: \"Annulla e torna indietro\",\n          children: /*#__PURE__*/_jsxDEV(Button, {\n            variant: \"outlined\",\n            color: \"inherit\",\n            onClick: handleCancel,\n            disabled: loading,\n            startIcon: /*#__PURE__*/_jsxDEV(CancelIcon, {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 307,\n              columnNumber: 26\n            }, this),\n            size: \"small\",\n            children: \"Annulla\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 302,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 301,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Tooltip, {\n          title: mode === 'add' ? 'Salva nuovo cavo' : 'Salva modifiche al cavo',\n          children: /*#__PURE__*/_jsxDEV(Button, {\n            type: \"submit\",\n            variant: \"contained\",\n            color: \"primary\",\n            startIcon: loading ? /*#__PURE__*/_jsxDEV(CircularProgress, {\n              size: 16,\n              color: \"inherit\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 319,\n              columnNumber: 36\n            }, this) : /*#__PURE__*/_jsxDEV(SaveIcon, {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 319,\n              columnNumber: 85\n            }, this),\n            disabled: loading,\n            size: \"small\",\n            children: loading ? 'Salvataggio...' : mode === 'add' ? 'Salva' : 'Salva'\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 315,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 314,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 291,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 218,\n      columnNumber: 7\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 210,\n    columnNumber: 5\n  }, this);\n};\n_s(CavoForm, \"ALEC1doFADxWA7KR9oJ+Ttc3ZMU=\", false, function () {\n  return [useNavigate];\n});\n_c = CavoForm;\nexport default CavoForm;\nvar _c;\n$RefreshReg$(_c, \"CavoForm\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "Box", "TextField", "<PERSON><PERSON>", "<PERSON><PERSON>", "MenuItem", "<PERSON><PERSON>", "CircularProgress", "Typography", "Divider", "Paper", "IconButton", "<PERSON><PERSON><PERSON>", "Save", "SaveIcon", "Warning", "WarningIcon", "Cancel", "CancelIcon", "KeyboardArrowDown", "KeyboardArrowDownIcon", "KeyboardArrowUp", "KeyboardArrowUpIcon", "useNavigate", "validateCavoData", "validateField", "redirectToVisualizzaCavi", "jsxDEV", "_jsxDEV", "SECTION_CONFIG", "title", "collapsible", "fields", "name", "label", "required", "inputProps", "style", "textTransform", "type", "options", "defaultValue", "defaultData", "id_cavo", "utility", "sistema", "colore_cavo", "tipologia", "n_conduttori", "sezione", "sh", "ubicazione_partenza", "utenza_partenza", "descrizione_utenza_partenza", "ubicazione_arrivo", "utenza_arrivo", "descrizione_utenza_arrivo", "metri_te<PERSON>ci", "metratura_reale", "CavoForm", "mode", "initialData", "onSubmit", "onSuccess", "onError", "onCancel", "_s", "navigate", "formData", "setFormData", "formErrors", "setFormErrors", "warnings", "setWarnings", "loading", "setLoading", "expanded", "setExpanded", "showWarnings", "setShowWarnings", "state", "for<PERSON>ach", "s", "toggleExpand", "prev", "handleChange", "e", "_result$message", "value", "target", "extra", "metriTeorici", "parseFloat", "result", "errorMessage", "valid", "message", "JSON", "stringify", "_result$message2", "warningMessage", "warning", "newWarnings", "Object", "values", "some", "w", "handleSubmit", "preventDefault", "validation", "<PERSON><PERSON><PERSON><PERSON>", "errors", "finalData", "validatedData", "toUpperCase", "err", "handleCancel", "renderField", "field", "_formErrors$field$nam", "_warnings$field$name", "_field$options", "select", "fullWidth", "size", "margin", "onChange", "error", "helperText", "variant", "InputLabelProps", "shrink", "children", "map", "opt", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "elevation", "sx", "border", "borderRadius", "overflow", "component", "noValidate", "severity", "icon", "py", "section", "index", "isExpanded", "borderBottom", "length", "display", "justifyContent", "alignItems", "px", "bgcolor", "cursor", "onClick", "fontWeight", "color", "fontSize", "ml", "stopPropagation", "p", "spacing", "gap", "borderTop", "disabled", "startIcon", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/PycharmProjects/CMS/webapp/frontend/src/components/cavi/CavoForm.js"], "sourcesContent": ["import React, { useState, useEffect } from 'react';\nimport {\n  Box,\n  TextField,\n  Button,\n  Stack,\n  MenuItem,\n  Alert,\n  CircularProgress,\n  Typography,\n  Divider,\n  Paper,\n  IconButton,\n  Tooltip\n} from '@mui/material';\nimport {\n  Save as SaveIcon,\n  Warning as WarningIcon,\n  Cancel as CancelIcon,\n  KeyboardArrowDown as KeyboardArrowDownIcon,\n  KeyboardArrowUp as KeyboardArrowUpIcon\n} from '@mui/icons-material';\nimport { useNavigate } from 'react-router-dom';\nimport { validateCavoData, validateField } from '../../utils/validationUtils';\nimport { redirectToVisualizzaCavi } from '../../utils/navigationUtils';\n\nconst SECTION_CONFIG = [\n  {\n    title: 'Informazioni Generali',\n    collapsible: false,\n    fields: [\n      { name: 'id_cavo', label: 'ID Cavo', required: true, inputProps: { style: { textTransform: 'uppercase' } } },\n      { name: 'utility', label: 'Utility', required: true },\n      { name: 'sistema', label: 'Sistema' }\n    ]\n  },\n  {\n    title: 'Cara<PERSON>istiche Tecniche',\n    collapsible: false,\n    fields: [\n      { name: 'colore_cavo', label: 'Colore Cavo' },\n      { name: 'tipologia', label: 'Tipologia' },\n      { name: 'n_conduttori', label: 'Numero Conduttori' },\n      { name: 'sezione', label: 'Sezione' },\n      { name: 'sh', label: 'Schermato (S/N)', type: 'select', options: ['S', 'N'], defaultValue: 'N' }\n    ]\n  },\n  {\n    title: 'Partenza',\n    collapsible: true,\n    fields: [\n      { name: 'ubicazione_partenza', label: 'Ubicazione Partenza' },\n      { name: 'utenza_partenza', label: 'Utenza Partenza' },\n      { name: 'descrizione_utenza_partenza', label: 'Descrizione Utenza Partenza' }\n    ]\n  },\n  {\n    title: 'Arrivo',\n    collapsible: true,\n    fields: [\n      { name: 'ubicazione_arrivo', label: 'Ubicazione Arrivo' },\n      { name: 'utenza_arrivo', label: 'Utenza Arrivo' },\n      { name: 'descrizione_utenza_arrivo', label: 'Descrizione Utenza Arrivo' }\n    ]\n  },\n  {\n    title: 'Metratura',\n    collapsible: false,\n    fields: [\n      { name: 'metri_teorici', label: 'Metri Teorici', required: true },\n      { name: 'metratura_reale', label: 'Metratura Reale' }\n    ]\n  }\n];\n\nconst defaultData = {\n  id_cavo: '',\n  utility: '',\n  sistema: '',\n  colore_cavo: '',\n  tipologia: '',\n  n_conduttori: '',\n  sezione: '',\n  sh: 'N',\n  ubicazione_partenza: '',\n  utenza_partenza: '',\n  descrizione_utenza_partenza: '',\n  ubicazione_arrivo: '',\n  utenza_arrivo: '',\n  descrizione_utenza_arrivo: '',\n  metri_teorici: '',\n  metratura_reale: '0'\n};\n\nconst CavoForm = ({ mode = 'add', initialData = {}, onSubmit, onSuccess, onError, onCancel }) => {\n  const navigate = useNavigate();\n  const [formData, setFormData] = useState({ ...defaultData, ...initialData });\n  const [formErrors, setFormErrors] = useState({});\n  const [warnings, setWarnings] = useState({});\n  const [loading, setLoading] = useState(false);\n  const [expanded, setExpanded] = useState({});\n  const [showWarnings, setShowWarnings] = useState(false);\n\n  // Inizializza lo stato di espansione delle sezioni collassabili\n  useEffect(() => {\n    const state = {};\n    SECTION_CONFIG.forEach(s => { if (s.collapsible) state[s.title] = true; });\n    setExpanded(state);\n  }, []);\n\n  // Gestisce l'espansione/collasso delle sezioni\n  const toggleExpand = (title) => {\n    setExpanded(prev => ({ ...prev, [title]: !prev[title] }));\n  };\n\n  // Gestisce i cambiamenti nei campi del form\n  const handleChange = (e) => {\n    const { name, value } = e.target;\n    setFormData(prev => ({ ...prev, [name]: value }));\n\n    // Validazione in tempo reale\n    const extra = name === 'metratura_reale' ? { metriTeorici: parseFloat(formData.metri_teorici || 0) } : {};\n    const result = validateField(name, value, extra);\n\n    // Assicurati che gli errori siano sempre stringhe o null\n    const errorMessage = result.valid ? null :\n      (typeof result.message === 'object' ?\n        (result.message?.message || JSON.stringify(result.message)) :\n        result.message);\n\n    setFormErrors(prev => ({ ...prev, [name]: errorMessage }));\n\n    setWarnings(prev => {\n      // Assicurati che i warning siano sempre stringhe o null\n      const warningMessage = result.warning ?\n        (typeof result.message === 'object' ?\n          (result.message?.message || JSON.stringify(result.message)) :\n          result.message) :\n        null;\n\n      const newWarnings = { ...prev, [name]: warningMessage };\n      // Mostra il banner di avviso se ci sono warning\n      setShowWarnings(Object.values(newWarnings).some(w => w));\n      return newWarnings;\n    });\n  };\n\n  // Gestisce l'invio del form\n  const handleSubmit = async (e) => {\n    e.preventDefault();\n    setLoading(true);\n    const validation = validateCavoData(formData);\n\n    if (!validation.isValid) {\n      setFormErrors(validation.errors);\n      setWarnings(validation.warnings);\n      setShowWarnings(Object.values(validation.warnings).some(w => w));\n      setLoading(false);\n      onError('Ci sono errori nel form.');\n      return;\n    }\n\n    try {\n      const finalData = { ...validation.validatedData, id_cavo: validation.validatedData.id_cavo.toUpperCase() };\n      await onSubmit(finalData);\n      setLoading(false);\n      onSuccess(`Cavo ${mode === 'add' ? 'aggiunto' : 'modificato'} con successo.`);\n      redirectToVisualizzaCavi(navigate);\n    } catch (err) {\n      setLoading(false);\n      onError(err.message);\n    }\n  };\n\n  // Gestisce l'annullamento del form\n  const handleCancel = () => {\n    if (onCancel) {\n      onCancel();\n    } else {\n      redirectToVisualizzaCavi(navigate);\n    }\n  };\n\n  // Renderizza un singolo campo del form\n  const renderField = (field) => (\n    <TextField\n      key={field.name}\n      select={field.type === 'select'}\n      fullWidth\n      size=\"small\"\n      margin=\"dense\"\n      label={field.label}\n      name={field.name}\n      value={formData[field.name]}\n      onChange={handleChange}\n      error={!!formErrors[field.name]}\n      helperText={typeof formErrors[field.name] === 'object' ? formErrors[field.name]?.message || JSON.stringify(formErrors[field.name]) : formErrors[field.name] || (typeof warnings[field.name] === 'object' ? warnings[field.name]?.message || JSON.stringify(warnings[field.name]) : warnings[field.name])}\n      required={field.required}\n      variant=\"outlined\"\n      InputLabelProps={{ shrink: true }}\n      {...(field.inputProps || {})}\n    >\n      {field.options?.map(opt => (\n        <MenuItem key={opt} value={opt}>{opt}</MenuItem>\n      ))}\n    </TextField>\n  );\n\n  return (\n    <Paper\n      elevation={0}\n      sx={{\n        border: '1px solid #e0e0e0',\n        borderRadius: 2,\n        overflow: 'hidden'\n      }}\n    >\n      <Box component=\"form\" onSubmit={handleSubmit} noValidate>\n        {/* Avviso di validazione */}\n        {showWarnings && (\n          <Alert\n            severity=\"warning\"\n            icon={<WarningIcon />}\n            sx={{\n              borderRadius: 0,\n              py: 0.5\n            }}\n          >\n            Alcuni campi potrebbero necessitare revisione\n          </Alert>\n        )}\n\n        {/* Sezioni del form */}\n        {SECTION_CONFIG.map((section, index) => {\n          const isExpanded = section.collapsible ? expanded[section.title] : true;\n\n          return (\n            <Box key={section.title} sx={{ borderBottom: index < SECTION_CONFIG.length - 1 ? '1px solid #e0e0e0' : 'none' }}>\n              {/* Intestazione della sezione */}\n              <Box\n                sx={{\n                  display: 'flex',\n                  justifyContent: 'space-between',\n                  alignItems: 'center',\n                  px: 2,\n                  py: 1.5,\n                  bgcolor: '#f5f5f5',\n                  borderBottom: isExpanded ? '1px solid #e0e0e0' : 'none',\n                  cursor: section.collapsible ? 'pointer' : 'default',\n                }}\n                onClick={() => section.collapsible && toggleExpand(section.title)}\n              >\n                <Typography\n                  variant=\"subtitle1\"\n                  sx={{\n                    fontWeight: 600,\n                    color: '#333',\n                    fontSize: '1rem'\n                  }}\n                >\n                  {section.title}\n                </Typography>\n\n                {section.collapsible && (\n                  <IconButton\n                    size=\"small\"\n                    sx={{ ml: 1 }}\n                    onClick={(e) => {\n                      e.stopPropagation();\n                      toggleExpand(section.title);\n                    }}\n                  >\n                    {isExpanded ? <KeyboardArrowUpIcon /> : <KeyboardArrowDownIcon />}\n                  </IconButton>\n                )}\n              </Box>\n\n              {/* Contenuto della sezione */}\n              {isExpanded && (\n                <Box sx={{ p: 2 }}>\n                  <Stack spacing={1.5}>\n                    {section.fields.map(field => renderField(field))}\n                  </Stack>\n                </Box>\n              )}\n            </Box>\n          );\n        })}\n\n        {/* Pulsanti di azione */}\n        <Box\n          sx={{\n            display: 'flex',\n            justifyContent: 'flex-end',\n            gap: 1.5,\n            p: 2,\n            bgcolor: '#f9f9f9',\n            borderTop: '1px solid #e0e0e0'\n          }}\n        >\n          <Tooltip title=\"Annulla e torna indietro\">\n            <Button\n              variant=\"outlined\"\n              color=\"inherit\"\n              onClick={handleCancel}\n              disabled={loading}\n              startIcon={<CancelIcon />}\n              size=\"small\"\n            >\n              Annulla\n            </Button>\n          </Tooltip>\n\n          <Tooltip title={mode === 'add' ? 'Salva nuovo cavo' : 'Salva modifiche al cavo'}>\n            <Button\n              type=\"submit\"\n              variant=\"contained\"\n              color=\"primary\"\n              startIcon={loading ? <CircularProgress size={16} color=\"inherit\" /> : <SaveIcon />}\n              disabled={loading}\n              size=\"small\"\n            >\n              {loading ? 'Salvataggio...' : mode === 'add' ? 'Salva' : 'Salva'}\n            </Button>\n          </Tooltip>\n        </Box>\n      </Box>\n    </Paper>\n  );\n};\n\nexport default CavoForm;"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAClD,SACEC,GAAG,EACHC,SAAS,EACTC,MAAM,EACNC,KAAK,EACLC,QAAQ,EACRC,KAAK,EACLC,gBAAgB,EAChBC,UAAU,EACVC,OAAO,EACPC,KAAK,EACLC,UAAU,EACVC,OAAO,QACF,eAAe;AACtB,SACEC,IAAI,IAAIC,QAAQ,EAChBC,OAAO,IAAIC,WAAW,EACtBC,MAAM,IAAIC,UAAU,EACpBC,iBAAiB,IAAIC,qBAAqB,EAC1CC,eAAe,IAAIC,mBAAmB,QACjC,qBAAqB;AAC5B,SAASC,WAAW,QAAQ,kBAAkB;AAC9C,SAASC,gBAAgB,EAAEC,aAAa,QAAQ,6BAA6B;AAC7E,SAASC,wBAAwB,QAAQ,6BAA6B;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAEvE,MAAMC,cAAc,GAAG,CACrB;EACEC,KAAK,EAAE,uBAAuB;EAC9BC,WAAW,EAAE,KAAK;EAClBC,MAAM,EAAE,CACN;IAAEC,IAAI,EAAE,SAAS;IAAEC,KAAK,EAAE,SAAS;IAAEC,QAAQ,EAAE,IAAI;IAAEC,UAAU,EAAE;MAAEC,KAAK,EAAE;QAAEC,aAAa,EAAE;MAAY;IAAE;EAAE,CAAC,EAC5G;IAAEL,IAAI,EAAE,SAAS;IAAEC,KAAK,EAAE,SAAS;IAAEC,QAAQ,EAAE;EAAK,CAAC,EACrD;IAAEF,IAAI,EAAE,SAAS;IAAEC,KAAK,EAAE;EAAU,CAAC;AAEzC,CAAC,EACD;EACEJ,KAAK,EAAE,0BAA0B;EACjCC,WAAW,EAAE,KAAK;EAClBC,MAAM,EAAE,CACN;IAAEC,IAAI,EAAE,aAAa;IAAEC,KAAK,EAAE;EAAc,CAAC,EAC7C;IAAED,IAAI,EAAE,WAAW;IAAEC,KAAK,EAAE;EAAY,CAAC,EACzC;IAAED,IAAI,EAAE,cAAc;IAAEC,KAAK,EAAE;EAAoB,CAAC,EACpD;IAAED,IAAI,EAAE,SAAS;IAAEC,KAAK,EAAE;EAAU,CAAC,EACrC;IAAED,IAAI,EAAE,IAAI;IAAEC,KAAK,EAAE,iBAAiB;IAAEK,IAAI,EAAE,QAAQ;IAAEC,OAAO,EAAE,CAAC,GAAG,EAAE,GAAG,CAAC;IAAEC,YAAY,EAAE;EAAI,CAAC;AAEpG,CAAC,EACD;EACEX,KAAK,EAAE,UAAU;EACjBC,WAAW,EAAE,IAAI;EACjBC,MAAM,EAAE,CACN;IAAEC,IAAI,EAAE,qBAAqB;IAAEC,KAAK,EAAE;EAAsB,CAAC,EAC7D;IAAED,IAAI,EAAE,iBAAiB;IAAEC,KAAK,EAAE;EAAkB,CAAC,EACrD;IAAED,IAAI,EAAE,6BAA6B;IAAEC,KAAK,EAAE;EAA8B,CAAC;AAEjF,CAAC,EACD;EACEJ,KAAK,EAAE,QAAQ;EACfC,WAAW,EAAE,IAAI;EACjBC,MAAM,EAAE,CACN;IAAEC,IAAI,EAAE,mBAAmB;IAAEC,KAAK,EAAE;EAAoB,CAAC,EACzD;IAAED,IAAI,EAAE,eAAe;IAAEC,KAAK,EAAE;EAAgB,CAAC,EACjD;IAAED,IAAI,EAAE,2BAA2B;IAAEC,KAAK,EAAE;EAA4B,CAAC;AAE7E,CAAC,EACD;EACEJ,KAAK,EAAE,WAAW;EAClBC,WAAW,EAAE,KAAK;EAClBC,MAAM,EAAE,CACN;IAAEC,IAAI,EAAE,eAAe;IAAEC,KAAK,EAAE,eAAe;IAAEC,QAAQ,EAAE;EAAK,CAAC,EACjE;IAAEF,IAAI,EAAE,iBAAiB;IAAEC,KAAK,EAAE;EAAkB,CAAC;AAEzD,CAAC,CACF;AAED,MAAMQ,WAAW,GAAG;EAClBC,OAAO,EAAE,EAAE;EACXC,OAAO,EAAE,EAAE;EACXC,OAAO,EAAE,EAAE;EACXC,WAAW,EAAE,EAAE;EACfC,SAAS,EAAE,EAAE;EACbC,YAAY,EAAE,EAAE;EAChBC,OAAO,EAAE,EAAE;EACXC,EAAE,EAAE,GAAG;EACPC,mBAAmB,EAAE,EAAE;EACvBC,eAAe,EAAE,EAAE;EACnBC,2BAA2B,EAAE,EAAE;EAC/BC,iBAAiB,EAAE,EAAE;EACrBC,aAAa,EAAE,EAAE;EACjBC,yBAAyB,EAAE,EAAE;EAC7BC,aAAa,EAAE,EAAE;EACjBC,eAAe,EAAE;AACnB,CAAC;AAED,MAAMC,QAAQ,GAAGA,CAAC;EAAEC,IAAI,GAAG,KAAK;EAAEC,WAAW,GAAG,CAAC,CAAC;EAAEC,QAAQ;EAAEC,SAAS;EAAEC,OAAO;EAAEC;AAAS,CAAC,KAAK;EAAAC,EAAA;EAC/F,MAAMC,QAAQ,GAAG5C,WAAW,CAAC,CAAC;EAC9B,MAAM,CAAC6C,QAAQ,EAAEC,WAAW,CAAC,GAAGtE,QAAQ,CAAC;IAAE,GAAG2C,WAAW;IAAE,GAAGmB;EAAY,CAAC,CAAC;EAC5E,MAAM,CAACS,UAAU,EAAEC,aAAa,CAAC,GAAGxE,QAAQ,CAAC,CAAC,CAAC,CAAC;EAChD,MAAM,CAACyE,QAAQ,EAAEC,WAAW,CAAC,GAAG1E,QAAQ,CAAC,CAAC,CAAC,CAAC;EAC5C,MAAM,CAAC2E,OAAO,EAAEC,UAAU,CAAC,GAAG5E,QAAQ,CAAC,KAAK,CAAC;EAC7C,MAAM,CAAC6E,QAAQ,EAAEC,WAAW,CAAC,GAAG9E,QAAQ,CAAC,CAAC,CAAC,CAAC;EAC5C,MAAM,CAAC+E,YAAY,EAAEC,eAAe,CAAC,GAAGhF,QAAQ,CAAC,KAAK,CAAC;;EAEvD;EACAC,SAAS,CAAC,MAAM;IACd,MAAMgF,KAAK,GAAG,CAAC,CAAC;IAChBnD,cAAc,CAACoD,OAAO,CAACC,CAAC,IAAI;MAAE,IAAIA,CAAC,CAACnD,WAAW,EAAEiD,KAAK,CAACE,CAAC,CAACpD,KAAK,CAAC,GAAG,IAAI;IAAE,CAAC,CAAC;IAC1E+C,WAAW,CAACG,KAAK,CAAC;EACpB,CAAC,EAAE,EAAE,CAAC;;EAEN;EACA,MAAMG,YAAY,GAAIrD,KAAK,IAAK;IAC9B+C,WAAW,CAACO,IAAI,KAAK;MAAE,GAAGA,IAAI;MAAE,CAACtD,KAAK,GAAG,CAACsD,IAAI,CAACtD,KAAK;IAAE,CAAC,CAAC,CAAC;EAC3D,CAAC;;EAED;EACA,MAAMuD,YAAY,GAAIC,CAAC,IAAK;IAAA,IAAAC,eAAA;IAC1B,MAAM;MAAEtD,IAAI;MAAEuD;IAAM,CAAC,GAAGF,CAAC,CAACG,MAAM;IAChCpB,WAAW,CAACe,IAAI,KAAK;MAAE,GAAGA,IAAI;MAAE,CAACnD,IAAI,GAAGuD;IAAM,CAAC,CAAC,CAAC;;IAEjD;IACA,MAAME,KAAK,GAAGzD,IAAI,KAAK,iBAAiB,GAAG;MAAE0D,YAAY,EAAEC,UAAU,CAACxB,QAAQ,CAACX,aAAa,IAAI,CAAC;IAAE,CAAC,GAAG,CAAC,CAAC;IACzG,MAAMoC,MAAM,GAAGpE,aAAa,CAACQ,IAAI,EAAEuD,KAAK,EAAEE,KAAK,CAAC;;IAEhD;IACA,MAAMI,YAAY,GAAGD,MAAM,CAACE,KAAK,GAAG,IAAI,GACrC,OAAOF,MAAM,CAACG,OAAO,KAAK,QAAQ,GAChC,EAAAT,eAAA,GAAAM,MAAM,CAACG,OAAO,cAAAT,eAAA,uBAAdA,eAAA,CAAgBS,OAAO,KAAIC,IAAI,CAACC,SAAS,CAACL,MAAM,CAACG,OAAO,CAAC,GAC1DH,MAAM,CAACG,OAAQ;IAEnBzB,aAAa,CAACa,IAAI,KAAK;MAAE,GAAGA,IAAI;MAAE,CAACnD,IAAI,GAAG6D;IAAa,CAAC,CAAC,CAAC;IAE1DrB,WAAW,CAACW,IAAI,IAAI;MAAA,IAAAe,gBAAA;MAClB;MACA,MAAMC,cAAc,GAAGP,MAAM,CAACQ,OAAO,GAClC,OAAOR,MAAM,CAACG,OAAO,KAAK,QAAQ,GAChC,EAAAG,gBAAA,GAAAN,MAAM,CAACG,OAAO,cAAAG,gBAAA,uBAAdA,gBAAA,CAAgBH,OAAO,KAAIC,IAAI,CAACC,SAAS,CAACL,MAAM,CAACG,OAAO,CAAC,GAC1DH,MAAM,CAACG,OAAO,GAChB,IAAI;MAEN,MAAMM,WAAW,GAAG;QAAE,GAAGlB,IAAI;QAAE,CAACnD,IAAI,GAAGmE;MAAe,CAAC;MACvD;MACArB,eAAe,CAACwB,MAAM,CAACC,MAAM,CAACF,WAAW,CAAC,CAACG,IAAI,CAACC,CAAC,IAAIA,CAAC,CAAC,CAAC;MACxD,OAAOJ,WAAW;IACpB,CAAC,CAAC;EACJ,CAAC;;EAED;EACA,MAAMK,YAAY,GAAG,MAAOrB,CAAC,IAAK;IAChCA,CAAC,CAACsB,cAAc,CAAC,CAAC;IAClBjC,UAAU,CAAC,IAAI,CAAC;IAChB,MAAMkC,UAAU,GAAGrF,gBAAgB,CAAC4C,QAAQ,CAAC;IAE7C,IAAI,CAACyC,UAAU,CAACC,OAAO,EAAE;MACvBvC,aAAa,CAACsC,UAAU,CAACE,MAAM,CAAC;MAChCtC,WAAW,CAACoC,UAAU,CAACrC,QAAQ,CAAC;MAChCO,eAAe,CAACwB,MAAM,CAACC,MAAM,CAACK,UAAU,CAACrC,QAAQ,CAAC,CAACiC,IAAI,CAACC,CAAC,IAAIA,CAAC,CAAC,CAAC;MAChE/B,UAAU,CAAC,KAAK,CAAC;MACjBX,OAAO,CAAC,0BAA0B,CAAC;MACnC;IACF;IAEA,IAAI;MACF,MAAMgD,SAAS,GAAG;QAAE,GAAGH,UAAU,CAACI,aAAa;QAAEtE,OAAO,EAAEkE,UAAU,CAACI,aAAa,CAACtE,OAAO,CAACuE,WAAW,CAAC;MAAE,CAAC;MAC1G,MAAMpD,QAAQ,CAACkD,SAAS,CAAC;MACzBrC,UAAU,CAAC,KAAK,CAAC;MACjBZ,SAAS,CAAC,QAAQH,IAAI,KAAK,KAAK,GAAG,UAAU,GAAG,YAAY,gBAAgB,CAAC;MAC7ElC,wBAAwB,CAACyC,QAAQ,CAAC;IACpC,CAAC,CAAC,OAAOgD,GAAG,EAAE;MACZxC,UAAU,CAAC,KAAK,CAAC;MACjBX,OAAO,CAACmD,GAAG,CAACnB,OAAO,CAAC;IACtB;EACF,CAAC;;EAED;EACA,MAAMoB,YAAY,GAAGA,CAAA,KAAM;IACzB,IAAInD,QAAQ,EAAE;MACZA,QAAQ,CAAC,CAAC;IACZ,CAAC,MAAM;MACLvC,wBAAwB,CAACyC,QAAQ,CAAC;IACpC;EACF,CAAC;;EAED;EACA,MAAMkD,WAAW,GAAIC,KAAK;IAAA,IAAAC,qBAAA,EAAAC,oBAAA,EAAAC,cAAA;IAAA,oBACxB7F,OAAA,CAAC1B,SAAS;MAERwH,MAAM,EAAEJ,KAAK,CAAC/E,IAAI,KAAK,QAAS;MAChCoF,SAAS;MACTC,IAAI,EAAC,OAAO;MACZC,MAAM,EAAC,OAAO;MACd3F,KAAK,EAAEoF,KAAK,CAACpF,KAAM;MACnBD,IAAI,EAAEqF,KAAK,CAACrF,IAAK;MACjBuD,KAAK,EAAEpB,QAAQ,CAACkD,KAAK,CAACrF,IAAI,CAAE;MAC5B6F,QAAQ,EAAEzC,YAAa;MACvB0C,KAAK,EAAE,CAAC,CAACzD,UAAU,CAACgD,KAAK,CAACrF,IAAI,CAAE;MAChC+F,UAAU,EAAE,OAAO1D,UAAU,CAACgD,KAAK,CAACrF,IAAI,CAAC,KAAK,QAAQ,GAAG,EAAAsF,qBAAA,GAAAjD,UAAU,CAACgD,KAAK,CAACrF,IAAI,CAAC,cAAAsF,qBAAA,uBAAtBA,qBAAA,CAAwBvB,OAAO,KAAIC,IAAI,CAACC,SAAS,CAAC5B,UAAU,CAACgD,KAAK,CAACrF,IAAI,CAAC,CAAC,GAAGqC,UAAU,CAACgD,KAAK,CAACrF,IAAI,CAAC,KAAK,OAAOuC,QAAQ,CAAC8C,KAAK,CAACrF,IAAI,CAAC,KAAK,QAAQ,GAAG,EAAAuF,oBAAA,GAAAhD,QAAQ,CAAC8C,KAAK,CAACrF,IAAI,CAAC,cAAAuF,oBAAA,uBAApBA,oBAAA,CAAsBxB,OAAO,KAAIC,IAAI,CAACC,SAAS,CAAC1B,QAAQ,CAAC8C,KAAK,CAACrF,IAAI,CAAC,CAAC,GAAGuC,QAAQ,CAAC8C,KAAK,CAACrF,IAAI,CAAC,CAAE;MACzSE,QAAQ,EAAEmF,KAAK,CAACnF,QAAS;MACzB8F,OAAO,EAAC,UAAU;MAClBC,eAAe,EAAE;QAAEC,MAAM,EAAE;MAAK,CAAE;MAAA,IAC7Bb,KAAK,CAAClF,UAAU,IAAI,CAAC,CAAC;MAAAgG,QAAA,GAAAX,cAAA,GAE1BH,KAAK,CAAC9E,OAAO,cAAAiF,cAAA,uBAAbA,cAAA,CAAeY,GAAG,CAACC,GAAG,iBACrB1G,OAAA,CAACvB,QAAQ;QAAWmF,KAAK,EAAE8C,GAAI;QAAAF,QAAA,EAAEE;MAAG,GAArBA,GAAG;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAA6B,CAChD;IAAC,GAlBGpB,KAAK,CAACrF,IAAI;MAAAsG,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAmBN,CAAC;EAAA,CACb;EAED,oBACE9G,OAAA,CAAClB,KAAK;IACJiI,SAAS,EAAE,CAAE;IACbC,EAAE,EAAE;MACFC,MAAM,EAAE,mBAAmB;MAC3BC,YAAY,EAAE,CAAC;MACfC,QAAQ,EAAE;IACZ,CAAE;IAAAX,QAAA,eAEFxG,OAAA,CAAC3B,GAAG;MAAC+I,SAAS,EAAC,MAAM;MAAClF,QAAQ,EAAE6C,YAAa;MAACsC,UAAU;MAAAb,QAAA,GAErDtD,YAAY,iBACXlD,OAAA,CAACtB,KAAK;QACJ4I,QAAQ,EAAC,SAAS;QAClBC,IAAI,eAAEvH,OAAA,CAACZ,WAAW;UAAAuH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAE;QACtBE,EAAE,EAAE;UACFE,YAAY,EAAE,CAAC;UACfM,EAAE,EAAE;QACN,CAAE;QAAAhB,QAAA,EACH;MAED;QAAAG,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAO,CACR,EAGA7G,cAAc,CAACwG,GAAG,CAAC,CAACgB,OAAO,EAAEC,KAAK,KAAK;QACtC,MAAMC,UAAU,GAAGF,OAAO,CAACtH,WAAW,GAAG6C,QAAQ,CAACyE,OAAO,CAACvH,KAAK,CAAC,GAAG,IAAI;QAEvE,oBACEF,OAAA,CAAC3B,GAAG;UAAqB2I,EAAE,EAAE;YAAEY,YAAY,EAAEF,KAAK,GAAGzH,cAAc,CAAC4H,MAAM,GAAG,CAAC,GAAG,mBAAmB,GAAG;UAAO,CAAE;UAAArB,QAAA,gBAE9GxG,OAAA,CAAC3B,GAAG;YACF2I,EAAE,EAAE;cACFc,OAAO,EAAE,MAAM;cACfC,cAAc,EAAE,eAAe;cAC/BC,UAAU,EAAE,QAAQ;cACpBC,EAAE,EAAE,CAAC;cACLT,EAAE,EAAE,GAAG;cACPU,OAAO,EAAE,SAAS;cAClBN,YAAY,EAAED,UAAU,GAAG,mBAAmB,GAAG,MAAM;cACvDQ,MAAM,EAAEV,OAAO,CAACtH,WAAW,GAAG,SAAS,GAAG;YAC5C,CAAE;YACFiI,OAAO,EAAEA,CAAA,KAAMX,OAAO,CAACtH,WAAW,IAAIoD,YAAY,CAACkE,OAAO,CAACvH,KAAK,CAAE;YAAAsG,QAAA,gBAElExG,OAAA,CAACpB,UAAU;cACTyH,OAAO,EAAC,WAAW;cACnBW,EAAE,EAAE;gBACFqB,UAAU,EAAE,GAAG;gBACfC,KAAK,EAAE,MAAM;gBACbC,QAAQ,EAAE;cACZ,CAAE;cAAA/B,QAAA,EAEDiB,OAAO,CAACvH;YAAK;cAAAyG,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACJ,CAAC,EAEZW,OAAO,CAACtH,WAAW,iBAClBH,OAAA,CAACjB,UAAU;cACTiH,IAAI,EAAC,OAAO;cACZgB,EAAE,EAAE;gBAAEwB,EAAE,EAAE;cAAE,CAAE;cACdJ,OAAO,EAAG1E,CAAC,IAAK;gBACdA,CAAC,CAAC+E,eAAe,CAAC,CAAC;gBACnBlF,YAAY,CAACkE,OAAO,CAACvH,KAAK,CAAC;cAC7B,CAAE;cAAAsG,QAAA,EAEDmB,UAAU,gBAAG3H,OAAA,CAACN,mBAAmB;gBAAAiH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,gBAAG9G,OAAA,CAACR,qBAAqB;gBAAAmH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACvD,CACb;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACE,CAAC,EAGLa,UAAU,iBACT3H,OAAA,CAAC3B,GAAG;YAAC2I,EAAE,EAAE;cAAE0B,CAAC,EAAE;YAAE,CAAE;YAAAlC,QAAA,eAChBxG,OAAA,CAACxB,KAAK;cAACmK,OAAO,EAAE,GAAI;cAAAnC,QAAA,EACjBiB,OAAO,CAACrH,MAAM,CAACqG,GAAG,CAACf,KAAK,IAAID,WAAW,CAACC,KAAK,CAAC;YAAC;cAAAiB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC3C;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACL,CACN;QAAA,GA/COW,OAAO,CAACvH,KAAK;UAAAyG,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAgDlB,CAAC;MAEV,CAAC,CAAC,eAGF9G,OAAA,CAAC3B,GAAG;QACF2I,EAAE,EAAE;UACFc,OAAO,EAAE,MAAM;UACfC,cAAc,EAAE,UAAU;UAC1Ba,GAAG,EAAE,GAAG;UACRF,CAAC,EAAE,CAAC;UACJR,OAAO,EAAE,SAAS;UAClBW,SAAS,EAAE;QACb,CAAE;QAAArC,QAAA,gBAEFxG,OAAA,CAAChB,OAAO;UAACkB,KAAK,EAAC,0BAA0B;UAAAsG,QAAA,eACvCxG,OAAA,CAACzB,MAAM;YACL8H,OAAO,EAAC,UAAU;YAClBiC,KAAK,EAAC,SAAS;YACfF,OAAO,EAAE5C,YAAa;YACtBsD,QAAQ,EAAEhG,OAAQ;YAClBiG,SAAS,eAAE/I,OAAA,CAACV,UAAU;cAAAqH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAE;YAC1Bd,IAAI,EAAC,OAAO;YAAAQ,QAAA,EACb;UAED;YAAAG,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACF,CAAC,eAEV9G,OAAA,CAAChB,OAAO;UAACkB,KAAK,EAAE8B,IAAI,KAAK,KAAK,GAAG,kBAAkB,GAAG,yBAA0B;UAAAwE,QAAA,eAC9ExG,OAAA,CAACzB,MAAM;YACLoC,IAAI,EAAC,QAAQ;YACb0F,OAAO,EAAC,WAAW;YACnBiC,KAAK,EAAC,SAAS;YACfS,SAAS,EAAEjG,OAAO,gBAAG9C,OAAA,CAACrB,gBAAgB;cAACqH,IAAI,EAAE,EAAG;cAACsC,KAAK,EAAC;YAAS;cAAA3B,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,gBAAG9G,OAAA,CAACd,QAAQ;cAAAyH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAE;YACnFgC,QAAQ,EAAEhG,OAAQ;YAClBkD,IAAI,EAAC,OAAO;YAAAQ,QAAA,EAEX1D,OAAO,GAAG,gBAAgB,GAAGd,IAAI,KAAK,KAAK,GAAG,OAAO,GAAG;UAAO;YAAA2E,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC1D;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACF,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACP,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACD,CAAC;AAEZ,CAAC;AAACxE,EAAA,CA3OIP,QAAQ;EAAA,QACKpC,WAAW;AAAA;AAAAqJ,EAAA,GADxBjH,QAAQ;AA6Od,eAAeA,QAAQ;AAAC,IAAAiH,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}