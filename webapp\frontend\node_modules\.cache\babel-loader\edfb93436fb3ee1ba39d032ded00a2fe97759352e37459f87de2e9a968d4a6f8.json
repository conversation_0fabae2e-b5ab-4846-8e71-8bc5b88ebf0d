{"ast": null, "code": "import axios from 'axios';\nimport config from '../config';\nimport axiosInstance from './axiosConfig';\nconst API_URL = config.API_URL;\n\n// Gestione degli errori di autenticazione\naxiosInstance.interceptors.response.use(response => response, error => {\n  console.error('Errore nella risposta API:', error);\n  if (error.response && error.response.status === 401) {\n    // Se la risposta è 401 Unauthorized, effettua il logout\n    console.log('Errore 401 rilevato, rimozione token');\n    localStorage.removeItem('token');\n\n    // Pulisci eventuali selezioni di cantiere precedenti\n    localStorage.removeItem('selectedCantiereId');\n    localStorage.removeItem('selectedCantiereName');\n    console.log('Rimossi dati cantiere precedenti dal localStorage');\n\n    // Non reindirizzare automaticamente, lascia che sia il componente React a gestire il reindirizzamento\n    // Questo evita loop di reindirizzamento\n  }\n  return Promise.reject(error);\n});\nconst authService = {\n  // Login standard (admin o utente standard)\n  login: async (credentials, loginType) => {\n    try {\n      console.log(`Tentativo di login ${loginType} con API_URL: ${API_URL}`);\n      if (loginType === 'standard') {\n        // Converti le credenziali nel formato richiesto da OAuth2\n        const formData = new FormData();\n        formData.append('username', credentials.username);\n        formData.append('password', credentials.password);\n        console.log(`Invio richiesta POST a ${API_URL}/auth/login`);\n        // Usa axios direttamente per il login perché richiede FormData\n        const response = await axios.post(`${API_URL}/auth/login`, formData, {\n          headers: {\n            'Content-Type': 'multipart/form-data'\n          }\n        });\n        console.log('Risposta ricevuta:', response);\n        return response.data;\n      } else if (loginType === 'cantiere') {\n        // Login cantiere\n        console.log(`Invio richiesta POST a ${API_URL}/auth/login/cantiere`);\n        const response = await axiosInstance.post('/auth/login/cantiere', {\n          codice_univoco: credentials.codice_univoco,\n          password: credentials.password\n        });\n        console.log('Risposta ricevuta:', response);\n\n        // Salva l'ID e il nome del cantiere nel localStorage\n        if (response.data.cantiere_id) {\n          console.log('Salvando ID cantiere nel localStorage:', response.data.cantiere_id);\n          localStorage.setItem('selectedCantiereId', response.data.cantiere_id.toString());\n          localStorage.setItem('selectedCantiereName', response.data.cantiere_name || `Cantiere ${response.data.cantiere_id}`);\n        } else {\n          console.warn('Risposta login cantiere non contiene cantiere_id:', response.data);\n        }\n        return response.data;\n      } else {\n        throw new Error('Tipo di login non valido');\n      }\n    } catch (error) {\n      console.error('Login error:', error);\n      if (error.response) {\n        console.error('Dettagli errore:', error.response.status, error.response.data);\n        // Miglioramento della gestione degli errori per mostrare messaggi più chiari\n        if (error.response.status === 401) {\n          throw {\n            detail: error.response.data.detail || 'Credenziali non valide. Verifica username e password.'\n          };\n        } else if (error.response.status === 403) {\n          throw {\n            detail: error.response.data.detail || 'Accesso negato. Non hai i permessi necessari.'\n          };\n        } else {\n          throw error.response.data;\n        }\n      } else if (error.request) {\n        console.error('Nessuna risposta ricevuta:', error.request);\n        throw {\n          detail: 'Errore di connessione al server. Verifica che il backend sia in esecuzione.'\n        };\n      } else {\n        console.error('Errore durante la configurazione della richiesta:', error.message);\n        throw {\n          detail: error.message\n        };\n      }\n    }\n  },\n  // Verifica la validità del token\n  checkToken: async () => {\n    try {\n      console.log('Verifica token in corso...');\n      const response = await axiosInstance.post('/auth/test-token');\n      console.log('Risposta verifica token:', response.data);\n\n      // Controlla se l'utente è impersonato da un admin\n      // Questo valore viene ora impostato dal backend nel token JWT\n      const isImpersonated = response.data.is_impersonated === true;\n\n      // Se l'utente è impersonato, salva lo stato nel localStorage\n      if (isImpersonated) {\n        console.log('Utente impersonato da admin, salvataggio stato nel localStorage');\n        localStorage.setItem('isImpersonating', 'true');\n      } else {\n        // Altrimenti, assicurati che non ci sia uno stato di impersonificazione salvato\n        localStorage.removeItem('isImpersonating');\n      }\n\n      // Costruisci l'oggetto utente con i dati dal token\n      const userData = {\n        id: response.data.user_id,\n        username: response.data.username,\n        role: response.data.role,\n        isImpersonated: isImpersonated\n      };\n\n      // Se l'utente è un utente cantiere, aggiungi i dati del cantiere\n      if (response.data.role === 'cantieri_user' && response.data.cantiere_id) {\n        userData.cantiere_id = response.data.cantiere_id;\n        userData.cantiere_name = response.data.cantiere_name || `Cantiere ${response.data.cantiere_id}`;\n\n        // Salva l'ID e il nome del cantiere nel localStorage\n        localStorage.setItem('selectedCantiereId', response.data.cantiere_id.toString());\n        localStorage.setItem('selectedCantiereName', userData.cantiere_name);\n        console.log('Salvati dati cantiere nel localStorage durante checkToken:', {\n          cantiere_id: response.data.cantiere_id,\n          cantiere_name: userData.cantiere_name\n        });\n      }\n\n      // Se l'utente è impersonato, aggiungi i dati dell'utente impersonato\n      if (isImpersonated && response.data.impersonated_id) {\n        // Salva i dati dell'utente impersonato nel localStorage\n        const impersonatedUserData = {\n          id: response.data.impersonated_id,\n          username: response.data.impersonated_username,\n          role: response.data.impersonated_role\n        };\n        localStorage.setItem('impersonatedUser', JSON.stringify(impersonatedUserData));\n      }\n      return userData;\n    } catch (error) {\n      console.error('Check token error:', error);\n      // Pulisci il localStorage per evitare loop\n      localStorage.removeItem('token');\n      localStorage.removeItem('isImpersonating');\n\n      // Pulisci eventuali selezioni di cantiere precedenti\n      localStorage.removeItem('selectedCantiereId');\n      localStorage.removeItem('selectedCantiereName');\n      console.log('Rimossi dati cantiere precedenti dal localStorage');\n      throw error.response ? error.response.data : error;\n    }\n  },\n  // Impersona un altro utente (solo per admin)\n  impersonateUser: async userId => {\n    try {\n      const response = await axiosInstance.post('/auth/impersonate', {\n        user_id: userId\n      });\n      return response.data;\n    } catch (error) {\n      console.error('Impersonate user error:', error);\n      throw error.response ? error.response.data : error;\n    }\n  }\n};\nexport default authService;", "map": {"version": 3, "names": ["axios", "config", "axiosInstance", "API_URL", "interceptors", "response", "use", "error", "console", "status", "log", "localStorage", "removeItem", "Promise", "reject", "authService", "login", "credentials", "loginType", "formData", "FormData", "append", "username", "password", "post", "headers", "data", "codice_univoco", "cantiere_id", "setItem", "toString", "cantiere_name", "warn", "Error", "detail", "request", "message", "checkToken", "isImpersonated", "is_impersonated", "userData", "id", "user_id", "role", "impersonated_id", "impersonated<PERSON><PERSON><PERSON><PERSON>", "impersonated_username", "impersonated_role", "JSON", "stringify", "impersonate<PERSON><PERSON>", "userId"], "sources": ["C:/CMS/webapp/frontend/src/services/authService.js"], "sourcesContent": ["import axios from 'axios';\r\nimport config from '../config';\r\nimport axiosInstance from './axiosConfig';\r\n\r\nconst API_URL = config.API_URL;\r\n\r\n// Gestione degli errori di autenticazione\r\naxiosInstance.interceptors.response.use(\r\n  (response) => response,\r\n  (error) => {\r\n    console.error('Errore nella risposta API:', error);\r\n    if (error.response && error.response.status === 401) {\r\n      // Se la risposta è 401 Unauthorized, effettua il logout\r\n      console.log('Errore 401 rilevato, rimozione token');\r\n      localStorage.removeItem('token');\r\n\r\n      // Pulisci eventuali selezioni di cantiere precedenti\r\n      localStorage.removeItem('selectedCantiereId');\r\n      localStorage.removeItem('selectedCantiereName');\r\n      console.log('Rimossi dati cantiere precedenti dal localStorage');\r\n\r\n      // Non reindirizzare automaticamente, lascia che sia il componente React a gestire il reindirizzamento\r\n      // Questo evita loop di reindirizzamento\r\n    }\r\n    return Promise.reject(error);\r\n  }\r\n);\r\n\r\nconst authService = {\r\n  // Login standard (admin o utente standard)\r\n  login: async (credentials, loginType) => {\r\n    try {\r\n      console.log(`Tentativo di login ${loginType} con API_URL: ${API_URL}`);\r\n\r\n      if (loginType === 'standard') {\r\n        // Converti le credenziali nel formato richiesto da OAuth2\r\n        const formData = new FormData();\r\n        formData.append('username', credentials.username);\r\n        formData.append('password', credentials.password);\r\n\r\n        console.log(`Invio richiesta POST a ${API_URL}/auth/login`);\r\n        // Usa axios direttamente per il login perché richiede FormData\r\n        const response = await axios.post(`${API_URL}/auth/login`, formData, {\r\n          headers: {\r\n            'Content-Type': 'multipart/form-data',\r\n          },\r\n        });\r\n        console.log('Risposta ricevuta:', response);\r\n        return response.data;\r\n      } else if (loginType === 'cantiere') {\r\n        // Login cantiere\r\n        console.log(`Invio richiesta POST a ${API_URL}/auth/login/cantiere`);\r\n        const response = await axiosInstance.post('/auth/login/cantiere', {\r\n          codice_univoco: credentials.codice_univoco,\r\n          password: credentials.password\r\n        });\r\n        console.log('Risposta ricevuta:', response);\r\n\r\n        // Salva l'ID e il nome del cantiere nel localStorage\r\n        if (response.data.cantiere_id) {\r\n          console.log('Salvando ID cantiere nel localStorage:', response.data.cantiere_id);\r\n          localStorage.setItem('selectedCantiereId', response.data.cantiere_id.toString());\r\n          localStorage.setItem('selectedCantiereName', response.data.cantiere_name || `Cantiere ${response.data.cantiere_id}`);\r\n        } else {\r\n          console.warn('Risposta login cantiere non contiene cantiere_id:', response.data);\r\n        }\r\n\r\n        return response.data;\r\n      } else {\r\n        throw new Error('Tipo di login non valido');\r\n      }\r\n    } catch (error) {\r\n      console.error('Login error:', error);\r\n      if (error.response) {\r\n        console.error('Dettagli errore:', error.response.status, error.response.data);\r\n        // Miglioramento della gestione degli errori per mostrare messaggi più chiari\r\n        if (error.response.status === 401) {\r\n          throw { detail: error.response.data.detail || 'Credenziali non valide. Verifica username e password.' };\r\n        } else if (error.response.status === 403) {\r\n          throw { detail: error.response.data.detail || 'Accesso negato. Non hai i permessi necessari.' };\r\n        } else {\r\n          throw error.response.data;\r\n        }\r\n      } else if (error.request) {\r\n        console.error('Nessuna risposta ricevuta:', error.request);\r\n        throw { detail: 'Errore di connessione al server. Verifica che il backend sia in esecuzione.' };\r\n      } else {\r\n        console.error('Errore durante la configurazione della richiesta:', error.message);\r\n        throw { detail: error.message };\r\n      }\r\n    }\r\n  },\r\n\r\n\r\n\r\n  // Verifica la validità del token\r\n  checkToken: async () => {\r\n    try {\r\n      console.log('Verifica token in corso...');\r\n      const response = await axiosInstance.post('/auth/test-token');\r\n      console.log('Risposta verifica token:', response.data);\r\n\r\n      // Controlla se l'utente è impersonato da un admin\r\n      // Questo valore viene ora impostato dal backend nel token JWT\r\n      const isImpersonated = response.data.is_impersonated === true;\r\n\r\n      // Se l'utente è impersonato, salva lo stato nel localStorage\r\n      if (isImpersonated) {\r\n        console.log('Utente impersonato da admin, salvataggio stato nel localStorage');\r\n        localStorage.setItem('isImpersonating', 'true');\r\n      } else {\r\n        // Altrimenti, assicurati che non ci sia uno stato di impersonificazione salvato\r\n        localStorage.removeItem('isImpersonating');\r\n      }\r\n\r\n      // Costruisci l'oggetto utente con i dati dal token\r\n      const userData = {\r\n        id: response.data.user_id,\r\n        username: response.data.username,\r\n        role: response.data.role,\r\n        isImpersonated: isImpersonated\r\n      };\r\n\r\n      // Se l'utente è un utente cantiere, aggiungi i dati del cantiere\r\n      if (response.data.role === 'cantieri_user' && response.data.cantiere_id) {\r\n        userData.cantiere_id = response.data.cantiere_id;\r\n        userData.cantiere_name = response.data.cantiere_name || `Cantiere ${response.data.cantiere_id}`;\r\n\r\n        // Salva l'ID e il nome del cantiere nel localStorage\r\n        localStorage.setItem('selectedCantiereId', response.data.cantiere_id.toString());\r\n        localStorage.setItem('selectedCantiereName', userData.cantiere_name);\r\n        console.log('Salvati dati cantiere nel localStorage durante checkToken:', {\r\n          cantiere_id: response.data.cantiere_id,\r\n          cantiere_name: userData.cantiere_name\r\n        });\r\n      }\r\n\r\n      // Se l'utente è impersonato, aggiungi i dati dell'utente impersonato\r\n      if (isImpersonated && response.data.impersonated_id) {\r\n        // Salva i dati dell'utente impersonato nel localStorage\r\n        const impersonatedUserData = {\r\n          id: response.data.impersonated_id,\r\n          username: response.data.impersonated_username,\r\n          role: response.data.impersonated_role\r\n        };\r\n\r\n        localStorage.setItem('impersonatedUser', JSON.stringify(impersonatedUserData));\r\n      }\r\n\r\n      return userData;\r\n    } catch (error) {\r\n      console.error('Check token error:', error);\r\n      // Pulisci il localStorage per evitare loop\r\n      localStorage.removeItem('token');\r\n      localStorage.removeItem('isImpersonating');\r\n\r\n      // Pulisci eventuali selezioni di cantiere precedenti\r\n      localStorage.removeItem('selectedCantiereId');\r\n      localStorage.removeItem('selectedCantiereName');\r\n      console.log('Rimossi dati cantiere precedenti dal localStorage');\r\n      throw error.response ? error.response.data : error;\r\n    }\r\n  },\r\n\r\n  // Impersona un altro utente (solo per admin)\r\n  impersonateUser: async (userId) => {\r\n    try {\r\n      const response = await axiosInstance.post('/auth/impersonate', {\r\n        user_id: userId\r\n      });\r\n      return response.data;\r\n    } catch (error) {\r\n      console.error('Impersonate user error:', error);\r\n      throw error.response ? error.response.data : error;\r\n    }\r\n  }\r\n};\r\n\r\nexport default authService;\r\n"], "mappings": "AAAA,OAAOA,KAAK,MAAM,OAAO;AACzB,OAAOC,MAAM,MAAM,WAAW;AAC9B,OAAOC,aAAa,MAAM,eAAe;AAEzC,MAAMC,OAAO,GAAGF,MAAM,CAACE,OAAO;;AAE9B;AACAD,aAAa,CAACE,YAAY,CAACC,QAAQ,CAACC,GAAG,CACpCD,QAAQ,IAAKA,QAAQ,EACrBE,KAAK,IAAK;EACTC,OAAO,CAACD,KAAK,CAAC,4BAA4B,EAAEA,KAAK,CAAC;EAClD,IAAIA,KAAK,CAACF,QAAQ,IAAIE,KAAK,CAACF,QAAQ,CAACI,MAAM,KAAK,GAAG,EAAE;IACnD;IACAD,OAAO,CAACE,GAAG,CAAC,sCAAsC,CAAC;IACnDC,YAAY,CAACC,UAAU,CAAC,OAAO,CAAC;;IAEhC;IACAD,YAAY,CAACC,UAAU,CAAC,oBAAoB,CAAC;IAC7CD,YAAY,CAACC,UAAU,CAAC,sBAAsB,CAAC;IAC/CJ,OAAO,CAACE,GAAG,CAAC,mDAAmD,CAAC;;IAEhE;IACA;EACF;EACA,OAAOG,OAAO,CAACC,MAAM,CAACP,KAAK,CAAC;AAC9B,CACF,CAAC;AAED,MAAMQ,WAAW,GAAG;EAClB;EACAC,KAAK,EAAE,MAAAA,CAAOC,WAAW,EAAEC,SAAS,KAAK;IACvC,IAAI;MACFV,OAAO,CAACE,GAAG,CAAC,sBAAsBQ,SAAS,iBAAiBf,OAAO,EAAE,CAAC;MAEtE,IAAIe,SAAS,KAAK,UAAU,EAAE;QAC5B;QACA,MAAMC,QAAQ,GAAG,IAAIC,QAAQ,CAAC,CAAC;QAC/BD,QAAQ,CAACE,MAAM,CAAC,UAAU,EAAEJ,WAAW,CAACK,QAAQ,CAAC;QACjDH,QAAQ,CAACE,MAAM,CAAC,UAAU,EAAEJ,WAAW,CAACM,QAAQ,CAAC;QAEjDf,OAAO,CAACE,GAAG,CAAC,0BAA0BP,OAAO,aAAa,CAAC;QAC3D;QACA,MAAME,QAAQ,GAAG,MAAML,KAAK,CAACwB,IAAI,CAAC,GAAGrB,OAAO,aAAa,EAAEgB,QAAQ,EAAE;UACnEM,OAAO,EAAE;YACP,cAAc,EAAE;UAClB;QACF,CAAC,CAAC;QACFjB,OAAO,CAACE,GAAG,CAAC,oBAAoB,EAAEL,QAAQ,CAAC;QAC3C,OAAOA,QAAQ,CAACqB,IAAI;MACtB,CAAC,MAAM,IAAIR,SAAS,KAAK,UAAU,EAAE;QACnC;QACAV,OAAO,CAACE,GAAG,CAAC,0BAA0BP,OAAO,sBAAsB,CAAC;QACpE,MAAME,QAAQ,GAAG,MAAMH,aAAa,CAACsB,IAAI,CAAC,sBAAsB,EAAE;UAChEG,cAAc,EAAEV,WAAW,CAACU,cAAc;UAC1CJ,QAAQ,EAAEN,WAAW,CAACM;QACxB,CAAC,CAAC;QACFf,OAAO,CAACE,GAAG,CAAC,oBAAoB,EAAEL,QAAQ,CAAC;;QAE3C;QACA,IAAIA,QAAQ,CAACqB,IAAI,CAACE,WAAW,EAAE;UAC7BpB,OAAO,CAACE,GAAG,CAAC,wCAAwC,EAAEL,QAAQ,CAACqB,IAAI,CAACE,WAAW,CAAC;UAChFjB,YAAY,CAACkB,OAAO,CAAC,oBAAoB,EAAExB,QAAQ,CAACqB,IAAI,CAACE,WAAW,CAACE,QAAQ,CAAC,CAAC,CAAC;UAChFnB,YAAY,CAACkB,OAAO,CAAC,sBAAsB,EAAExB,QAAQ,CAACqB,IAAI,CAACK,aAAa,IAAI,YAAY1B,QAAQ,CAACqB,IAAI,CAACE,WAAW,EAAE,CAAC;QACtH,CAAC,MAAM;UACLpB,OAAO,CAACwB,IAAI,CAAC,mDAAmD,EAAE3B,QAAQ,CAACqB,IAAI,CAAC;QAClF;QAEA,OAAOrB,QAAQ,CAACqB,IAAI;MACtB,CAAC,MAAM;QACL,MAAM,IAAIO,KAAK,CAAC,0BAA0B,CAAC;MAC7C;IACF,CAAC,CAAC,OAAO1B,KAAK,EAAE;MACdC,OAAO,CAACD,KAAK,CAAC,cAAc,EAAEA,KAAK,CAAC;MACpC,IAAIA,KAAK,CAACF,QAAQ,EAAE;QAClBG,OAAO,CAACD,KAAK,CAAC,kBAAkB,EAAEA,KAAK,CAACF,QAAQ,CAACI,MAAM,EAAEF,KAAK,CAACF,QAAQ,CAACqB,IAAI,CAAC;QAC7E;QACA,IAAInB,KAAK,CAACF,QAAQ,CAACI,MAAM,KAAK,GAAG,EAAE;UACjC,MAAM;YAAEyB,MAAM,EAAE3B,KAAK,CAACF,QAAQ,CAACqB,IAAI,CAACQ,MAAM,IAAI;UAAwD,CAAC;QACzG,CAAC,MAAM,IAAI3B,KAAK,CAACF,QAAQ,CAACI,MAAM,KAAK,GAAG,EAAE;UACxC,MAAM;YAAEyB,MAAM,EAAE3B,KAAK,CAACF,QAAQ,CAACqB,IAAI,CAACQ,MAAM,IAAI;UAAgD,CAAC;QACjG,CAAC,MAAM;UACL,MAAM3B,KAAK,CAACF,QAAQ,CAACqB,IAAI;QAC3B;MACF,CAAC,MAAM,IAAInB,KAAK,CAAC4B,OAAO,EAAE;QACxB3B,OAAO,CAACD,KAAK,CAAC,4BAA4B,EAAEA,KAAK,CAAC4B,OAAO,CAAC;QAC1D,MAAM;UAAED,MAAM,EAAE;QAA8E,CAAC;MACjG,CAAC,MAAM;QACL1B,OAAO,CAACD,KAAK,CAAC,mDAAmD,EAAEA,KAAK,CAAC6B,OAAO,CAAC;QACjF,MAAM;UAAEF,MAAM,EAAE3B,KAAK,CAAC6B;QAAQ,CAAC;MACjC;IACF;EACF,CAAC;EAID;EACAC,UAAU,EAAE,MAAAA,CAAA,KAAY;IACtB,IAAI;MACF7B,OAAO,CAACE,GAAG,CAAC,4BAA4B,CAAC;MACzC,MAAML,QAAQ,GAAG,MAAMH,aAAa,CAACsB,IAAI,CAAC,kBAAkB,CAAC;MAC7DhB,OAAO,CAACE,GAAG,CAAC,0BAA0B,EAAEL,QAAQ,CAACqB,IAAI,CAAC;;MAEtD;MACA;MACA,MAAMY,cAAc,GAAGjC,QAAQ,CAACqB,IAAI,CAACa,eAAe,KAAK,IAAI;;MAE7D;MACA,IAAID,cAAc,EAAE;QAClB9B,OAAO,CAACE,GAAG,CAAC,iEAAiE,CAAC;QAC9EC,YAAY,CAACkB,OAAO,CAAC,iBAAiB,EAAE,MAAM,CAAC;MACjD,CAAC,MAAM;QACL;QACAlB,YAAY,CAACC,UAAU,CAAC,iBAAiB,CAAC;MAC5C;;MAEA;MACA,MAAM4B,QAAQ,GAAG;QACfC,EAAE,EAAEpC,QAAQ,CAACqB,IAAI,CAACgB,OAAO;QACzBpB,QAAQ,EAAEjB,QAAQ,CAACqB,IAAI,CAACJ,QAAQ;QAChCqB,IAAI,EAAEtC,QAAQ,CAACqB,IAAI,CAACiB,IAAI;QACxBL,cAAc,EAAEA;MAClB,CAAC;;MAED;MACA,IAAIjC,QAAQ,CAACqB,IAAI,CAACiB,IAAI,KAAK,eAAe,IAAItC,QAAQ,CAACqB,IAAI,CAACE,WAAW,EAAE;QACvEY,QAAQ,CAACZ,WAAW,GAAGvB,QAAQ,CAACqB,IAAI,CAACE,WAAW;QAChDY,QAAQ,CAACT,aAAa,GAAG1B,QAAQ,CAACqB,IAAI,CAACK,aAAa,IAAI,YAAY1B,QAAQ,CAACqB,IAAI,CAACE,WAAW,EAAE;;QAE/F;QACAjB,YAAY,CAACkB,OAAO,CAAC,oBAAoB,EAAExB,QAAQ,CAACqB,IAAI,CAACE,WAAW,CAACE,QAAQ,CAAC,CAAC,CAAC;QAChFnB,YAAY,CAACkB,OAAO,CAAC,sBAAsB,EAAEW,QAAQ,CAACT,aAAa,CAAC;QACpEvB,OAAO,CAACE,GAAG,CAAC,4DAA4D,EAAE;UACxEkB,WAAW,EAAEvB,QAAQ,CAACqB,IAAI,CAACE,WAAW;UACtCG,aAAa,EAAES,QAAQ,CAACT;QAC1B,CAAC,CAAC;MACJ;;MAEA;MACA,IAAIO,cAAc,IAAIjC,QAAQ,CAACqB,IAAI,CAACkB,eAAe,EAAE;QACnD;QACA,MAAMC,oBAAoB,GAAG;UAC3BJ,EAAE,EAAEpC,QAAQ,CAACqB,IAAI,CAACkB,eAAe;UACjCtB,QAAQ,EAAEjB,QAAQ,CAACqB,IAAI,CAACoB,qBAAqB;UAC7CH,IAAI,EAAEtC,QAAQ,CAACqB,IAAI,CAACqB;QACtB,CAAC;QAEDpC,YAAY,CAACkB,OAAO,CAAC,kBAAkB,EAAEmB,IAAI,CAACC,SAAS,CAACJ,oBAAoB,CAAC,CAAC;MAChF;MAEA,OAAOL,QAAQ;IACjB,CAAC,CAAC,OAAOjC,KAAK,EAAE;MACdC,OAAO,CAACD,KAAK,CAAC,oBAAoB,EAAEA,KAAK,CAAC;MAC1C;MACAI,YAAY,CAACC,UAAU,CAAC,OAAO,CAAC;MAChCD,YAAY,CAACC,UAAU,CAAC,iBAAiB,CAAC;;MAE1C;MACAD,YAAY,CAACC,UAAU,CAAC,oBAAoB,CAAC;MAC7CD,YAAY,CAACC,UAAU,CAAC,sBAAsB,CAAC;MAC/CJ,OAAO,CAACE,GAAG,CAAC,mDAAmD,CAAC;MAChE,MAAMH,KAAK,CAACF,QAAQ,GAAGE,KAAK,CAACF,QAAQ,CAACqB,IAAI,GAAGnB,KAAK;IACpD;EACF,CAAC;EAED;EACA2C,eAAe,EAAE,MAAOC,MAAM,IAAK;IACjC,IAAI;MACF,MAAM9C,QAAQ,GAAG,MAAMH,aAAa,CAACsB,IAAI,CAAC,mBAAmB,EAAE;QAC7DkB,OAAO,EAAES;MACX,CAAC,CAAC;MACF,OAAO9C,QAAQ,CAACqB,IAAI;IACtB,CAAC,CAAC,OAAOnB,KAAK,EAAE;MACdC,OAAO,CAACD,KAAK,CAAC,yBAAyB,EAAEA,KAAK,CAAC;MAC/C,MAAMA,KAAK,CAACF,QAAQ,GAAGE,KAAK,CAACF,QAAQ,CAACqB,IAAI,GAAGnB,KAAK;IACpD;EACF;AACF,CAAC;AAED,eAAeQ,WAAW", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}