{"ast": null, "code": "'use client';\n\nexport { default } from './FormControlLabel';\nexport { default as formControlLabelClasses } from './formControlLabelClasses';\nexport * from './formControlLabelClasses';", "map": {"version": 3, "names": ["default", "formControlLabelClasses"], "sources": ["C:/Users/<USER>/PycharmProjects/CMS/webapp/frontend/node_modules/@mui/material/FormControlLabel/index.js"], "sourcesContent": ["'use client';\n\nexport { default } from './FormControlLabel';\nexport { default as formControlLabelClasses } from './formControlLabelClasses';\nexport * from './formControlLabelClasses';"], "mappings": "AAAA,YAAY;;AAEZ,SAASA,OAAO,QAAQ,oBAAoB;AAC5C,SAASA,OAAO,IAAIC,uBAAuB,QAAQ,2BAA2B;AAC9E,cAAc,2BAA2B", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}