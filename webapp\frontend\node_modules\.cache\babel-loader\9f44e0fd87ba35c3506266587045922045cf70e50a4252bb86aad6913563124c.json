{"ast": null, "code": "var _jsxFileName = \"C:\\\\CMS\\\\webapp\\\\frontend\\\\src\\\\components\\\\cavi\\\\QuickAddCablesDialog.js\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect } from 'react';\nimport { Dialog, DialogTitle, DialogContent, DialogActions, Button, Typography, Box, TextField, CircularProgress, Alert, Chip, IconButton, Tabs, Tab, List, ListItem, ListItemText, ListItemSecondaryAction, Divider, Card, CardContent, CardHeader, Badge } from '@mui/material';\nimport { Add as AddIcon, Delete as DeleteIcon, Save as SaveIcon, Warning as WarningIcon, Search as SearchIcon, Cable as CableIcon, CheckCircle as CheckCircleIcon } from '@mui/icons-material';\nimport caviService from '../../services/caviService';\nimport { isCableInstalled } from '../../utils/stateUtils';\nimport IncompatibleReelDialog from './IncompatibleReelDialog';\n\n// Utility per verificare compatibilità tra cavo e bobina\nimport { jsxDEV as _jsxDEV, Fragment as _Fragment } from \"react/jsx-dev-runtime\";\nconst isCompatible = (cavo, bobina) => {\n  return cavo.tipologia === bobina.tipologia && String(cavo.sezione) === String(bobina.sezione);\n};\n\n// Utility per ottenere il numero della bobina dall'ID\nconst getBobinaNumber = idBobina => {\n  if (!idBobina) return '';\n  const parts = idBobina.split('-');\n  return parts.length > 1 ? parts[1] : idBobina;\n};\n\n/**\n * Componente per aggiungere rapidamente più cavi a una bobina\n * Implementa sistema di doppia lista per cavi compatibili/incompatibili\n * con gestione intelligente delle incompatibilità e dialog moderni\n */\nconst QuickAddCablesDialog = ({\n  open,\n  onClose,\n  bobina,\n  cantiereId,\n  onSuccess,\n  onError\n}) => {\n  _s();\n  // Stati per la gestione dei dati\n  const [loading, setLoading] = useState(false);\n  const [caviLoading, setCaviLoading] = useState(false);\n  const [saving, setSaving] = useState(false);\n\n  // Stati per i dati separati\n  const [allCavi, setAllCavi] = useState([]);\n  const [caviCompatibili, setCaviCompatibili] = useState([]);\n  const [caviIncompatibili, setCaviIncompatibili] = useState([]);\n  const [selectedCavi, setSelectedCavi] = useState([]);\n  const [caviMetri, setCaviMetri] = useState({});\n\n  // Stati per la UI\n  const [activeTab, setActiveTab] = useState(0);\n  const [searchTerm, setSearchTerm] = useState('');\n  const [showIncompatibleDialog, setShowIncompatibleDialog] = useState(false);\n  const [incompatibleSelection, setIncompatibleSelection] = useState(null);\n  const [showOverDialog, setShowOverDialog] = useState(false);\n  const [overDialogData, setOverDialogData] = useState(null);\n\n  // Stati per la validazione\n  const [errors, setErrors] = useState({});\n  const [warnings, setWarnings] = useState({});\n\n  // Carica i cavi disponibili quando il dialog viene aperto\n  useEffect(() => {\n    if (open && bobina && cantiereId) {\n      loadCavi();\n      // Reset stati quando si apre il dialog\n      resetDialogState();\n    }\n  }, [open, bobina, cantiereId]);\n\n  // Early return se bobina è null per evitare errori\n  if (!bobina) {\n    return /*#__PURE__*/_jsxDEV(Dialog, {\n      open: open,\n      onClose: onClose,\n      maxWidth: \"sm\",\n      fullWidth: true,\n      children: [/*#__PURE__*/_jsxDEV(DialogTitle, {\n        children: \"Errore\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 96,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(DialogContent, {\n        children: /*#__PURE__*/_jsxDEV(Alert, {\n          severity: \"error\",\n          children: \"Nessuna bobina selezionata\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 98,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 97,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(DialogActions, {\n        children: /*#__PURE__*/_jsxDEV(Button, {\n          onClick: onClose,\n          children: \"Chiudi\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 101,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 100,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 95,\n      columnNumber: 7\n    }, this);\n  }\n\n  // Reset dello stato del dialog\n  const resetDialogState = () => {\n    setSelectedCavi([]);\n    setCaviMetri({});\n    setErrors({});\n    setWarnings({});\n    setSearchTerm('');\n    setActiveTab(0);\n    setShowIncompatibleDialog(false);\n    setIncompatibleSelection(null);\n    setShowOverDialog(false);\n    setOverDialogData(null);\n  };\n\n  // Funzione per caricare i cavi con sistema di doppia lista\n  const loadCavi = async () => {\n    try {\n      setCaviLoading(true);\n      const caviData = await caviService.getCavi(cantiereId);\n\n      // Filtra i cavi disponibili per la posa:\n      // 1. Non installati (metratura_reale = 0)\n      // 2. Non SPARE (modificato_manualmente !== 3)\n      // 3. Hanno metri teorici > 0\n      const caviDisponibili = caviData.filter(cavo => {\n        const metriReali = parseFloat(cavo.metratura_reale) || 0;\n        const metriTeorici = parseFloat(cavo.metri_teorici) || 0;\n        return !isCableInstalled(cavo) && cavo.modificato_manualmente !== 3 && metriReali === 0 && metriTeorici > 0;\n      });\n\n      // Separa cavi compatibili e incompatibili (CORREZIONE: rimosso n_conduttori)\n      const compatibili = caviDisponibili.filter(cavo => isCompatible(cavo, bobina));\n      const incompatibili = caviDisponibili.filter(cavo => !isCompatible(cavo, bobina));\n      setAllCavi(caviDisponibili);\n      setCaviCompatibili(compatibili);\n      setCaviIncompatibili(incompatibili);\n      console.log(`Cavi caricati: ${compatibili.length} compatibili, ${incompatibili.length} incompatibili`);\n    } catch (error) {\n      console.error('Errore nel caricamento dei cavi:', error);\n      onError('Errore nel caricamento dei cavi: ' + (error.message || 'Errore sconosciuto'));\n    } finally {\n      setCaviLoading(false);\n    }\n  };\n\n  // Gestisce la selezione di un cavo compatibile\n  const handleCompatibleCavoSelect = cavo => {\n    setSelectedCavi(prev => {\n      const isSelected = prev.some(c => c.id_cavo === cavo.id_cavo);\n      if (isSelected) {\n        // Conferma prima di rimuovere se ci sono metri inseriti\n        const hasMetri = caviMetri[cavo.id_cavo] && caviMetri[cavo.id_cavo].trim() !== '';\n        if (hasMetri) {\n          if (!window.confirm(`Rimuovere il cavo ${cavo.id_cavo} dalla selezione? I metri inseriti (${caviMetri[cavo.id_cavo]}m) andranno persi.`)) {\n            return prev;\n          }\n        }\n\n        // Rimuovi il cavo dalla selezione\n        const newSelected = prev.filter(c => c.id_cavo !== cavo.id_cavo);\n\n        // Rimuovi anche i metri associati\n        const newCaviMetri = {\n          ...caviMetri\n        };\n        delete newCaviMetri[cavo.id_cavo];\n        setCaviMetri(newCaviMetri);\n\n        // Rimuovi errori e warning\n        setErrors(prevErrors => {\n          const newErrors = {\n            ...prevErrors\n          };\n          delete newErrors[cavo.id_cavo];\n          return newErrors;\n        });\n        setWarnings(prevWarnings => {\n          const newWarnings = {\n            ...prevWarnings\n          };\n          delete newWarnings[cavo.id_cavo];\n          return newWarnings;\n        });\n        return newSelected;\n      } else {\n        // Aggiungi il cavo alla selezione\n        return [...prev, cavo];\n      }\n    });\n  };\n\n  // Gestisce la selezione di un cavo incompatibile\n  const handleIncompatibleCavoSelect = cavo => {\n    setIncompatibleSelection({\n      cavo,\n      bobina\n    });\n    setShowIncompatibleDialog(true);\n  };\n\n  // Gestisce l'uso di una bobina incompatibile\n  const handleUseIncompatibleReel = () => {\n    if (incompatibleSelection) {\n      const {\n        cavo\n      } = incompatibleSelection;\n\n      // Aggiungi il cavo alla selezione con flag di incompatibilità\n      setSelectedCavi(prev => [...prev, {\n        ...cavo,\n        _isIncompatible: true\n      }]);\n      setShowIncompatibleDialog(false);\n      setIncompatibleSelection(null);\n      onSuccess === null || onSuccess === void 0 ? void 0 : onSuccess(`Cavo incompatibile ${cavo.id_cavo} aggiunto alla selezione. Sarà utilizzato con force_over.`);\n    }\n  };\n\n  // Gestisce l'input dei metri posati per un cavo\n  const handleMetriChange = (cavoId, value) => {\n    // Aggiorna i metri per il cavo\n    setCaviMetri(prev => ({\n      ...prev,\n      [cavoId]: value\n    }));\n\n    // Valida il valore inserito in tempo reale\n    validateMetri(cavoId, value);\n  };\n\n  // Valida i metri inseriti per un cavo con feedback migliorato\n  const validateMetri = (cavoId, value) => {\n    const cavo = allCavi.find(c => c.id_cavo === cavoId);\n    if (!cavo) return;\n\n    // Resetta gli errori e gli avvisi per questo cavo\n    setErrors(prev => {\n      const newErrors = {\n        ...prev\n      };\n      delete newErrors[cavoId];\n      return newErrors;\n    });\n    setWarnings(prev => {\n      const newWarnings = {\n        ...prev\n      };\n      delete newWarnings[cavoId];\n      return newWarnings;\n    });\n\n    // Controllo input vuoto\n    if (!value || value.trim() === '') {\n      return true; // Non mostrare errore per input vuoto durante la digitazione\n    }\n\n    // Controllo formato numerico\n    if (isNaN(parseFloat(value)) || parseFloat(value) <= 0) {\n      setErrors(prev => ({\n        ...prev,\n        [cavoId]: 'Inserire un valore numerico positivo'\n      }));\n      return false;\n    }\n    const metriPosati = parseFloat(value);\n\n    // Controllo metri teorici cavo\n    if (cavo.metri_teorici && metriPosati > parseFloat(cavo.metri_teorici)) {\n      setWarnings(prev => ({\n        ...prev,\n        [cavoId]: `I metri posati (${metriPosati}m) superano i metri teorici del cavo (${cavo.metri_teorici}m)`\n      }));\n    }\n\n    // Controllo metri residui bobina con calcolo in tempo reale\n    const metriTotaliRichiesti = Object.entries(caviMetri).filter(([id, _]) => id !== cavoId) // Escludi il cavo corrente\n    .reduce((sum, [_, metri]) => sum + parseFloat(metri || 0), 0) + metriPosati;\n    const metriResiduiBobina = (bobina === null || bobina === void 0 ? void 0 : bobina.metri_residui) || 0;\n    if (metriTotaliRichiesti > metriResiduiBobina) {\n      setWarnings(prev => ({\n        ...prev,\n        [cavoId]: `Totale richiesto: ${metriTotaliRichiesti.toFixed(1)}m > Residui bobina: ${metriResiduiBobina.toFixed(1)}m (OVER)`\n      }));\n    }\n    return true;\n  };\n\n  // Valida tutti i metri inseriti con dialog moderno per OVER\n  const validateAllMetri = () => {\n    let isValid = true;\n    const newErrors = {};\n    const newWarnings = {};\n\n    // Verifica che ci siano cavi selezionati\n    if (selectedCavi.length === 0) {\n      onError('Seleziona almeno un cavo');\n      return false;\n    }\n\n    // Verifica che tutti i cavi selezionati abbiano metri inseriti\n    for (const cavo of selectedCavi) {\n      const metri = caviMetri[cavo.id_cavo];\n\n      // Controllo input vuoto\n      if (!metri || metri.trim() === '') {\n        newErrors[cavo.id_cavo] = 'Inserire un valore per i metri posati';\n        isValid = false;\n        continue;\n      }\n\n      // Controllo formato numerico\n      if (isNaN(parseFloat(metri)) || parseFloat(metri) <= 0) {\n        newErrors[cavo.id_cavo] = 'Inserire un valore numerico positivo';\n        isValid = false;\n        continue;\n      }\n      const metriPosati = parseFloat(metri);\n\n      // Controllo metri teorici cavo\n      if (cavo.metri_teorici && metriPosati > parseFloat(cavo.metri_teorici)) {\n        newWarnings[cavo.id_cavo] = `I metri posati (${metriPosati}m) superano i metri teorici del cavo (${cavo.metri_teorici}m)`;\n      }\n    }\n    setErrors(newErrors);\n    setWarnings(newWarnings);\n\n    // Se ci sono errori, non procedere\n    if (!isValid) {\n      return false;\n    }\n\n    // Verifica stato OVER della bobina\n    const metriTotaliRichiesti = Object.values(caviMetri).reduce((sum, metri) => sum + parseFloat(metri || 0), 0);\n    const metriResiduiBobina = (bobina === null || bobina === void 0 ? void 0 : bobina.metri_residui) || 0;\n    if (metriTotaliRichiesti > metriResiduiBobina) {\n      // Mostra dialog moderno invece di window.confirm\n      setOverDialogData({\n        metriTotaliRichiesti,\n        metriResiduiBobina,\n        selectedCavi: selectedCavi.length\n      });\n      setShowOverDialog(true);\n      return false; // Interrompi qui, il salvataggio continuerà dal dialog\n    }\n    return true;\n  };\n\n  // Gestisce il salvataggio dei dati con gestione migliorata\n  const handleSave = async (forceOver = false) => {\n    try {\n      // Validazione solo se non è un force over\n      if (!forceOver && !validateAllMetri()) {\n        return;\n      }\n      setSaving(true);\n\n      // Aggiorna ogni cavo selezionato\n      const results = [];\n      let errors = [];\n      for (const cavo of selectedCavi) {\n        try {\n          const metriPosati = parseFloat(caviMetri[cavo.id_cavo]);\n\n          // Determina se è necessario forzare lo stato OVER della bobina o incompatibilità\n          const metriGiàUtilizzati = results.reduce((sum, r) => sum + r.metriPosati, 0);\n          const metriResiduiBobina = (bobina === null || bobina === void 0 ? void 0 : bobina.metri_residui) || 0;\n          const needsForceOver = forceOver || metriGiàUtilizzati + metriPosati > metriResiduiBobina || cavo._isIncompatible;\n\n          // Aggiorna i metri posati del cavo\n          const result = await caviService.updateMetriPosati(cantiereId, cavo.id_cavo, metriPosati, bobina.id_bobina, needsForceOver);\n          results.push({\n            cavo: cavo.id_cavo,\n            metriPosati,\n            success: true,\n            wasIncompatible: cavo._isIncompatible\n          });\n        } catch (error) {\n          console.error(`Errore nell'aggiornamento del cavo ${cavo.id_cavo}:`, error);\n          errors.push({\n            cavo: cavo.id_cavo,\n            error: error.message || 'Errore sconosciuto'\n          });\n        }\n      }\n\n      // Gestione del risultato con messaggi migliorati\n      if (errors.length === 0) {\n        const incompatibleCount = results.filter(r => r.wasIncompatible).length;\n        let message = `${results.length} cavi aggiornati con successo`;\n        if (incompatibleCount > 0) {\n          message += ` (${incompatibleCount} incompatibili con force_over)`;\n        }\n        onSuccess(message);\n        onClose();\n      } else if (results.length > 0) {\n        onSuccess(`${results.length} cavi aggiornati con successo, ${errors.length} falliti`);\n        onError(`Errori: ${errors.map(e => `${e.cavo}: ${e.error}`).join(', ')}`);\n        onClose();\n      } else {\n        onError(`Nessun cavo aggiornato. Errori: ${errors.map(e => `${e.cavo}: ${e.error}`).join(', ')}`);\n      }\n    } catch (error) {\n      console.error('Errore durante il salvataggio:', error);\n      onError('Errore durante il salvataggio: ' + (error.message || 'Errore sconosciuto'));\n    } finally {\n      setSaving(false);\n    }\n  };\n\n  // Gestisce la conferma del dialog OVER\n  const handleOverDialogConfirm = () => {\n    setShowOverDialog(false);\n    setOverDialogData(null);\n    handleSave(true); // Procedi con force_over\n  };\n\n  // Filtra i cavi in base al termine di ricerca\n  const filterCavi = caviList => {\n    if (!searchTerm) return caviList;\n    return caviList.filter(cavo => cavo.id_cavo.toLowerCase().includes(searchTerm.toLowerCase()) || cavo.tipologia && cavo.tipologia.toLowerCase().includes(searchTerm.toLowerCase()) || cavo.ubicazione_partenza && cavo.ubicazione_partenza.toLowerCase().includes(searchTerm.toLowerCase()) || cavo.ubicazione_arrivo && cavo.ubicazione_arrivo.toLowerCase().includes(searchTerm.toLowerCase()));\n  };\n  const filteredCompatibili = filterCavi(caviCompatibili);\n  const filteredIncompatibili = filterCavi(caviIncompatibili);\n\n  // Calcola statistiche con controlli di sicurezza\n  const metriTotaliRichiesti = Object.values(caviMetri).reduce((sum, metri) => {\n    const value = parseFloat(metri || 0);\n    return isNaN(value) ? sum : sum + value;\n  }, 0);\n  const metriResiduiBobina = (bobina === null || bobina === void 0 ? void 0 : bobina.metri_residui) || 0;\n  const isOverState = metriTotaliRichiesti > metriResiduiBobina;\n\n  // Componente per renderizzare una lista di cavi\n  const renderCaviList = (caviList, isCompatible = true) => {\n    if (caviLoading) {\n      return /*#__PURE__*/_jsxDEV(Box, {\n        sx: {\n          display: 'flex',\n          justifyContent: 'center',\n          my: 4\n        },\n        children: /*#__PURE__*/_jsxDEV(CircularProgress, {}, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 461,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 460,\n        columnNumber: 9\n      }, this);\n    }\n    if (caviList.length === 0) {\n      return /*#__PURE__*/_jsxDEV(Alert, {\n        severity: \"info\",\n        sx: {\n          my: 2\n        },\n        children: isCompatible ? 'Nessun cavo compatibile disponibile.' : 'Nessun cavo incompatibile trovato.'\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 468,\n        columnNumber: 9\n      }, this);\n    }\n    return /*#__PURE__*/_jsxDEV(List, {\n      dense: true,\n      children: caviList.map(cavo => {\n        const isSelected = selectedCavi.some(c => c.id_cavo === cavo.id_cavo);\n        const hasMetri = caviMetri[cavo.id_cavo] && caviMetri[cavo.id_cavo].trim() !== '';\n        const hasError = errors[cavo.id_cavo];\n        const hasWarning = warnings[cavo.id_cavo];\n        return /*#__PURE__*/_jsxDEV(ListItem, {\n          sx: {\n            border: '1px solid #e0e0e0',\n            borderRadius: 1,\n            mb: 1,\n            bgcolor: isSelected ? 'rgba(33, 150, 243, 0.1)' : '#f5f7fa',\n            '&:hover': {\n              bgcolor: isSelected ? 'rgba(33, 150, 243, 0.2)' : 'rgba(33, 150, 243, 0.05)'\n            }\n          },\n          children: [/*#__PURE__*/_jsxDEV(ListItemText, {\n            primary: /*#__PURE__*/_jsxDEV(Box, {\n              sx: {\n                display: 'flex',\n                alignItems: 'center',\n                gap: 1\n              },\n              children: [/*#__PURE__*/_jsxDEV(CableIcon, {\n                fontSize: \"small\",\n                color: isCompatible ? 'success' : 'warning'\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 498,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                variant: \"subtitle2\",\n                children: cavo.id_cavo\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 499,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(Chip, {\n                size: \"small\",\n                label: cavo.tipologia || 'N/A',\n                color: isCompatible ? 'success' : 'warning',\n                variant: \"outlined\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 500,\n                columnNumber: 21\n              }, this), !isCompatible && /*#__PURE__*/_jsxDEV(Chip, {\n                size: \"small\",\n                label: \"INCOMPATIBILE\",\n                color: \"error\",\n                variant: \"filled\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 507,\n                columnNumber: 23\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 497,\n              columnNumber: 19\n            }, this),\n            secondary: /*#__PURE__*/_jsxDEV(Box, {\n              children: [/*#__PURE__*/_jsxDEV(Typography, {\n                variant: \"body2\",\n                color: \"text.secondary\",\n                children: [cavo.ubicazione_partenza || 'N/A', \" \\u2192 \", cavo.ubicazione_arrivo || 'N/A']\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 518,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                variant: \"body2\",\n                color: \"text.secondary\",\n                children: [\"Formazione: \", cavo.sezione || 'N/A', \" | Metri teorici: \", cavo.metri_teorici || 'N/A']\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 521,\n                columnNumber: 21\n              }, this), isSelected && /*#__PURE__*/_jsxDEV(Box, {\n                sx: {\n                  mt: 1\n                },\n                children: /*#__PURE__*/_jsxDEV(TextField, {\n                  size: \"small\",\n                  type: \"number\",\n                  label: \"Metri posati\",\n                  value: caviMetri[cavo.id_cavo] || '',\n                  onChange: e => handleMetriChange(cavo.id_cavo, e.target.value),\n                  error: !!hasError,\n                  helperText: hasError || hasWarning,\n                  FormHelperTextProps: {\n                    sx: {\n                      color: hasWarning && !hasError ? 'warning.main' : 'error.main'\n                    }\n                  },\n                  sx: {\n                    width: '200px'\n                  }\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 526,\n                  columnNumber: 25\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 525,\n                columnNumber: 23\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 517,\n              columnNumber: 19\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 495,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(ListItemSecondaryAction, {\n            children: /*#__PURE__*/_jsxDEV(Button, {\n              size: \"small\",\n              variant: isSelected ? 'contained' : 'outlined',\n              color: isCompatible ? 'primary' : 'warning',\n              onClick: () => isCompatible ? handleCompatibleCavoSelect(cavo) : handleIncompatibleCavoSelect(cavo),\n              startIcon: isSelected ? /*#__PURE__*/_jsxDEV(CheckCircleIcon, {}, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 550,\n                columnNumber: 43\n              }, this) : /*#__PURE__*/_jsxDEV(AddIcon, {}, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 550,\n                columnNumber: 65\n              }, this),\n              children: isSelected ? 'Selezionato' : 'Seleziona'\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 545,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 544,\n            columnNumber: 15\n          }, this)]\n        }, cavo.id_cavo, true, {\n          fileName: _jsxFileName,\n          lineNumber: 483,\n          columnNumber: 13\n        }, this);\n      })\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 475,\n      columnNumber: 7\n    }, this);\n  };\n  return /*#__PURE__*/_jsxDEV(Dialog, {\n    open: open,\n    onClose: onClose,\n    maxWidth: \"xl\",\n    fullWidth: true,\n    children: [/*#__PURE__*/_jsxDEV(DialogTitle, {\n      children: /*#__PURE__*/_jsxDEV(Box, {\n        sx: {\n          display: 'flex',\n          alignItems: 'center',\n          gap: 2\n        },\n        children: [/*#__PURE__*/_jsxDEV(CableIcon, {}, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 566,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Typography, {\n          variant: \"h6\",\n          children: [\"Aggiungi cavi alla bobina \", getBobinaNumber(bobina === null || bobina === void 0 ? void 0 : bobina.id_bobina)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 567,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 565,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 564,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(DialogContent, {\n      children: [/*#__PURE__*/_jsxDEV(Card, {\n        sx: {\n          mb: 3\n        },\n        children: [/*#__PURE__*/_jsxDEV(CardHeader, {\n          title: \"Dettagli bobina\",\n          titleTypographyProps: {\n            variant: 'subtitle1'\n          }\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 575,\n          columnNumber: 15\n        }, this), /*#__PURE__*/_jsxDEV(CardContent, {\n          sx: {\n            pt: 0\n          },\n          children: /*#__PURE__*/_jsxDEV(Box, {\n            sx: {\n              display: 'flex',\n              flexWrap: 'wrap',\n              gap: 3\n            },\n            children: [/*#__PURE__*/_jsxDEV(Box, {\n              children: [/*#__PURE__*/_jsxDEV(Typography, {\n                variant: \"body2\",\n                color: \"text.secondary\",\n                children: \"ID Bobina\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 582,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                variant: \"body1\",\n                children: bobina.id_bobina\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 583,\n                columnNumber: 21\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 581,\n              columnNumber: 19\n            }, this), /*#__PURE__*/_jsxDEV(Box, {\n              children: [/*#__PURE__*/_jsxDEV(Typography, {\n                variant: \"body2\",\n                color: \"text.secondary\",\n                children: \"Tipologia\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 586,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                variant: \"body1\",\n                children: bobina.tipologia || 'N/A'\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 587,\n                columnNumber: 21\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 585,\n              columnNumber: 19\n            }, this), /*#__PURE__*/_jsxDEV(Box, {\n              children: [/*#__PURE__*/_jsxDEV(Typography, {\n                variant: \"body2\",\n                color: \"text.secondary\",\n                children: \"Formazione\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 590,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                variant: \"body1\",\n                children: bobina.sezione || 'N/A'\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 591,\n                columnNumber: 21\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 589,\n              columnNumber: 19\n            }, this), /*#__PURE__*/_jsxDEV(Box, {\n              children: [/*#__PURE__*/_jsxDEV(Typography, {\n                variant: \"body2\",\n                color: \"text.secondary\",\n                children: \"Metri residui\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 594,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                variant: \"body1\",\n                color: isOverState ? 'error.main' : 'text.primary',\n                sx: {\n                  fontWeight: isOverState ? 'bold' : 'normal'\n                },\n                children: [metriResiduiBobina.toFixed(1), \" m\"]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 595,\n                columnNumber: 21\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 593,\n              columnNumber: 19\n            }, this), /*#__PURE__*/_jsxDEV(Box, {\n              children: [/*#__PURE__*/_jsxDEV(Typography, {\n                variant: \"body2\",\n                color: \"text.secondary\",\n                children: \"Stato\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 604,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(Chip, {\n                label: bobina.stato_bobina || 'N/D',\n                size: \"small\",\n                color: bobina.stato_bobina === 'Disponibile' ? 'success' : bobina.stato_bobina === 'In uso' ? 'primary' : bobina.stato_bobina === 'Over' ? 'error' : bobina.stato_bobina === 'Terminata' ? 'warning' : 'default'\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 605,\n                columnNumber: 21\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 603,\n              columnNumber: 19\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 580,\n            columnNumber: 17\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 579,\n          columnNumber: 15\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 574,\n        columnNumber: 13\n      }, this), /*#__PURE__*/_jsxDEV(TextField, {\n        fullWidth: true,\n        label: \"Ricerca intelligente cavi\",\n        variant: \"outlined\",\n        value: searchTerm,\n        onChange: e => setSearchTerm(e.target.value),\n        placeholder: \"Cerca per ID, tipologia, ubicazione...\",\n        InputProps: {\n          startAdornment: /*#__PURE__*/_jsxDEV(SearchIcon, {\n            sx: {\n              mr: 1,\n              color: 'text.secondary'\n            }\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 629,\n            columnNumber: 33\n          }, this)\n        },\n        sx: {\n          mb: 3\n        }\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 621,\n        columnNumber: 13\n      }, this), /*#__PURE__*/_jsxDEV(Box, {\n        sx: {\n          borderBottom: 1,\n          borderColor: 'divider',\n          mb: 2\n        },\n        children: /*#__PURE__*/_jsxDEV(Tabs, {\n          value: activeTab,\n          onChange: (e, newValue) => setActiveTab(newValue),\n          children: [/*#__PURE__*/_jsxDEV(Tab, {\n            label: /*#__PURE__*/_jsxDEV(Badge, {\n              badgeContent: filteredCompatibili.length,\n              color: \"success\",\n              children: /*#__PURE__*/_jsxDEV(Box, {\n                sx: {\n                  display: 'flex',\n                  alignItems: 'center',\n                  gap: 1\n                },\n                children: [/*#__PURE__*/_jsxDEV(CheckCircleIcon, {\n                  fontSize: \"small\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 641,\n                  columnNumber: 25\n                }, this), \"Cavi compatibili\"]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 640,\n                columnNumber: 23\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 639,\n              columnNumber: 21\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 637,\n            columnNumber: 17\n          }, this), /*#__PURE__*/_jsxDEV(Tab, {\n            label: /*#__PURE__*/_jsxDEV(Badge, {\n              badgeContent: filteredIncompatibili.length,\n              color: \"warning\",\n              children: /*#__PURE__*/_jsxDEV(Box, {\n                sx: {\n                  display: 'flex',\n                  alignItems: 'center',\n                  gap: 1\n                },\n                children: [/*#__PURE__*/_jsxDEV(WarningIcon, {\n                  fontSize: \"small\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 651,\n                  columnNumber: 25\n                }, this), \"Cavi incompatibili\"]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 650,\n                columnNumber: 23\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 649,\n              columnNumber: 21\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 647,\n            columnNumber: 17\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 636,\n          columnNumber: 15\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 635,\n        columnNumber: 13\n      }, this), /*#__PURE__*/_jsxDEV(Box, {\n        sx: {\n          minHeight: 300,\n          maxHeight: 400,\n          overflow: 'auto'\n        },\n        children: [activeTab === 0 && /*#__PURE__*/_jsxDEV(Box, {\n          children: [/*#__PURE__*/_jsxDEV(Typography, {\n            variant: \"body2\",\n            color: \"text.secondary\",\n            sx: {\n              mb: 2\n            },\n            children: [\"Cavi compatibili con tipologia \", /*#__PURE__*/_jsxDEV(\"strong\", {\n              children: bobina.tipologia\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 665,\n              columnNumber: 52\n            }, this), \" e formazione \", /*#__PURE__*/_jsxDEV(\"strong\", {\n              children: bobina.sezione\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 665,\n              columnNumber: 101\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 664,\n            columnNumber: 19\n          }, this), renderCaviList(filteredCompatibili, true)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 663,\n          columnNumber: 17\n        }, this), activeTab === 1 && /*#__PURE__*/_jsxDEV(Box, {\n          children: [/*#__PURE__*/_jsxDEV(Alert, {\n            severity: \"warning\",\n            sx: {\n              mb: 2\n            },\n            children: /*#__PURE__*/_jsxDEV(Typography, {\n              variant: \"body2\",\n              children: [\"I cavi incompatibili possono essere utilizzati con \", /*#__PURE__*/_jsxDEV(\"strong\", {\n                children: \"force_over\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 674,\n                columnNumber: 74\n              }, this), \", ma potrebbero non rispettare le specifiche tecniche della bobina.\"]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 673,\n              columnNumber: 21\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 672,\n            columnNumber: 19\n          }, this), renderCaviList(filteredIncompatibili, false)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 671,\n          columnNumber: 17\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 661,\n        columnNumber: 13\n      }, this), selectedCavi.length > 0 && /*#__PURE__*/_jsxDEV(Card, {\n        sx: {\n          mt: 3,\n          mb: 2\n        },\n        children: [/*#__PURE__*/_jsxDEV(CardHeader, {\n          title: `Riepilogo selezione (${selectedCavi.length} cavi)`,\n          titleTypographyProps: {\n            variant: 'subtitle1'\n          }\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 686,\n          columnNumber: 17\n        }, this), /*#__PURE__*/_jsxDEV(CardContent, {\n          sx: {\n            pt: 0\n          },\n          children: [/*#__PURE__*/_jsxDEV(Box, {\n            sx: {\n              mb: 2\n            },\n            children: [/*#__PURE__*/_jsxDEV(Box, {\n              sx: {\n                display: 'flex',\n                justifyContent: 'space-between',\n                mb: 1\n              },\n              children: [/*#__PURE__*/_jsxDEV(Typography, {\n                variant: \"body2\",\n                children: \"Metri totali richiesti:\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 693,\n                columnNumber: 23\n              }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                variant: \"body2\",\n                sx: {\n                  fontWeight: 'bold',\n                  color: isOverState ? 'error.main' : 'text.primary'\n                },\n                children: [metriTotaliRichiesti.toFixed(1), \" m\"]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 694,\n                columnNumber: 23\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 692,\n              columnNumber: 21\n            }, this), /*#__PURE__*/_jsxDEV(Box, {\n              sx: {\n                display: 'flex',\n                justifyContent: 'space-between',\n                mb: 1\n              },\n              children: [/*#__PURE__*/_jsxDEV(Typography, {\n                variant: \"body2\",\n                children: \"Metri residui bobina:\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 705,\n                columnNumber: 23\n              }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                variant: \"body2\",\n                sx: {\n                  fontWeight: 'bold'\n                },\n                children: [metriResiduiBobina.toFixed(1), \" m\"]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 706,\n                columnNumber: 23\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 704,\n              columnNumber: 21\n            }, this), /*#__PURE__*/_jsxDEV(Divider, {\n              sx: {\n                my: 1\n              }\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 710,\n              columnNumber: 21\n            }, this), /*#__PURE__*/_jsxDEV(Box, {\n              sx: {\n                display: 'flex',\n                justifyContent: 'space-between'\n              },\n              children: [/*#__PURE__*/_jsxDEV(Typography, {\n                variant: \"body2\",\n                children: \"Differenza:\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 712,\n                columnNumber: 23\n              }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                variant: \"body2\",\n                sx: {\n                  fontWeight: 'bold',\n                  color: isOverState ? 'error.main' : 'success.main'\n                },\n                children: [(metriResiduiBobina - metriTotaliRichiesti).toFixed(1), \" m\", isOverState && ' (OVER)']\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 713,\n                columnNumber: 23\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 711,\n              columnNumber: 21\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 691,\n            columnNumber: 19\n          }, this), isOverState && /*#__PURE__*/_jsxDEV(Alert, {\n            severity: \"warning\",\n            sx: {\n              mb: 2\n            },\n            children: /*#__PURE__*/_jsxDEV(Typography, {\n              variant: \"body2\",\n              children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n                children: \"ATTENZIONE:\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 729,\n                columnNumber: 25\n              }, this), \" I metri richiesti superano i metri residui della bobina. La bobina andr\\xE0 in stato OVER.\"]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 728,\n              columnNumber: 23\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 727,\n            columnNumber: 21\n          }, this), /*#__PURE__*/_jsxDEV(List, {\n            dense: true,\n            children: selectedCavi.map(cavo => /*#__PURE__*/_jsxDEV(ListItem, {\n              sx: {\n                border: '1px solid #e0e0e0',\n                borderRadius: 1,\n                mb: 1,\n                bgcolor: cavo._isIncompatible ? 'warning.light' : 'background.paper'\n              },\n              children: [/*#__PURE__*/_jsxDEV(ListItemText, {\n                primary: /*#__PURE__*/_jsxDEV(Box, {\n                  sx: {\n                    display: 'flex',\n                    alignItems: 'center',\n                    gap: 1\n                  },\n                  children: [/*#__PURE__*/_jsxDEV(Typography, {\n                    variant: \"subtitle2\",\n                    children: cavo.id_cavo\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 749,\n                    columnNumber: 31\n                  }, this), cavo._isIncompatible && /*#__PURE__*/_jsxDEV(Chip, {\n                    size: \"small\",\n                    label: \"INCOMPATIBILE\",\n                    color: \"warning\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 751,\n                    columnNumber: 33\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 748,\n                  columnNumber: 29\n                }, this),\n                secondary: `${caviMetri[cavo.id_cavo] || '0'} metri posati`\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 746,\n                columnNumber: 25\n              }, this), /*#__PURE__*/_jsxDEV(ListItemSecondaryAction, {\n                children: /*#__PURE__*/_jsxDEV(IconButton, {\n                  size: \"small\",\n                  color: \"error\",\n                  onClick: () => handleCompatibleCavoSelect(cavo),\n                  title: \"Rimuovi dalla selezione\",\n                  children: /*#__PURE__*/_jsxDEV(DeleteIcon, {\n                    fontSize: \"small\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 764,\n                    columnNumber: 29\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 758,\n                  columnNumber: 27\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 757,\n                columnNumber: 25\n              }, this)]\n            }, cavo.id_cavo, true, {\n              fileName: _jsxFileName,\n              lineNumber: 737,\n              columnNumber: 23\n            }, this))\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 735,\n            columnNumber: 19\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 690,\n          columnNumber: 17\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 685,\n        columnNumber: 15\n      }, this), Object.keys(warnings).length > 0 && /*#__PURE__*/_jsxDEV(Alert, {\n        severity: \"warning\",\n        sx: {\n          mt: 2\n        },\n        children: [/*#__PURE__*/_jsxDEV(Typography, {\n          variant: \"subtitle2\",\n          gutterBottom: true,\n          children: \"Avvisi:\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 777,\n          columnNumber: 17\n        }, this), /*#__PURE__*/_jsxDEV(List, {\n          dense: true,\n          children: Object.entries(warnings).map(([cavoId, warning]) => /*#__PURE__*/_jsxDEV(ListItem, {\n            sx: {\n              py: 0\n            },\n            children: /*#__PURE__*/_jsxDEV(ListItemText, {\n              primary: `${cavoId}: ${warning}`,\n              primaryTypographyProps: {\n                variant: 'body2'\n              }\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 781,\n              columnNumber: 23\n            }, this)\n          }, cavoId, false, {\n            fileName: _jsxFileName,\n            lineNumber: 780,\n            columnNumber: 21\n          }, this))\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 778,\n          columnNumber: 17\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 776,\n        columnNumber: 15\n      }, this), /*#__PURE__*/_jsxDEV(Alert, {\n        severity: \"info\",\n        sx: {\n          mt: 2\n        },\n        children: /*#__PURE__*/_jsxDEV(Typography, {\n          variant: \"body2\",\n          children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n            children: \"Istruzioni:\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 794,\n            columnNumber: 17\n          }, this), \" Seleziona i cavi dalle liste sopra e inserisci i metri posati. I cavi compatibili sono consigliati, quelli incompatibili richiedono conferma esplicita.\"]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 793,\n          columnNumber: 15\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 792,\n        columnNumber: 13\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 572,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(DialogActions, {\n      sx: {\n        px: 3,\n        py: 2\n      },\n      children: [/*#__PURE__*/_jsxDEV(Button, {\n        onClick: onClose,\n        disabled: saving,\n        children: \"Annulla\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 801,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Button, {\n        onClick: () => handleSave(false),\n        color: \"primary\",\n        variant: \"contained\",\n        disabled: saving || selectedCavi.length === 0 || Object.keys(errors).length > 0,\n        startIcon: saving ? /*#__PURE__*/_jsxDEV(CircularProgress, {\n          size: 20\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 809,\n          columnNumber: 31\n        }, this) : /*#__PURE__*/_jsxDEV(SaveIcon, {}, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 809,\n          columnNumber: 64\n        }, this),\n        children: saving ? 'Salvataggio...' : `Salva ${selectedCavi.length} cavi`\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 804,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 800,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(IncompatibleReelDialog, {\n      open: showIncompatibleDialog,\n      onClose: () => {\n        setShowIncompatibleDialog(false);\n        setIncompatibleSelection(null);\n      },\n      cavo: incompatibleSelection === null || incompatibleSelection === void 0 ? void 0 : incompatibleSelection.cavo,\n      bobina: incompatibleSelection === null || incompatibleSelection === void 0 ? void 0 : incompatibleSelection.bobina,\n      onConfirm: handleUseIncompatibleReel\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 816,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Dialog, {\n      open: showOverDialog,\n      onClose: () => setShowOverDialog(false),\n      maxWidth: \"sm\",\n      fullWidth: true,\n      children: [/*#__PURE__*/_jsxDEV(DialogTitle, {\n        children: /*#__PURE__*/_jsxDEV(Box, {\n          sx: {\n            display: 'flex',\n            alignItems: 'center',\n            gap: 1\n          },\n          children: [/*#__PURE__*/_jsxDEV(WarningIcon, {\n            color: \"warning\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 836,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Typography, {\n            variant: \"h6\",\n            children: \"Conferma stato OVER\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 837,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 835,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 834,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(DialogContent, {\n        children: overDialogData && /*#__PURE__*/_jsxDEV(_Fragment, {\n          children: [/*#__PURE__*/_jsxDEV(Alert, {\n            severity: \"warning\",\n            sx: {\n              mb: 2\n            },\n            children: /*#__PURE__*/_jsxDEV(Typography, {\n              variant: \"body1\",\n              gutterBottom: true,\n              children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n                children: \"ATTENZIONE:\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 845,\n                columnNumber: 19\n              }, this), \" L'operazione porter\\xE0 la bobina in stato OVER.\"]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 844,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 843,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(Box, {\n            sx: {\n              mb: 2\n            },\n            children: [/*#__PURE__*/_jsxDEV(Typography, {\n              variant: \"body2\",\n              gutterBottom: true,\n              children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n                children: \"Metri totali richiesti:\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 850,\n                columnNumber: 19\n              }, this), \" \", overDialogData.metriTotaliRichiesti.toFixed(1), \" m\"]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 849,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(Typography, {\n              variant: \"body2\",\n              gutterBottom: true,\n              children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n                children: \"Metri residui bobina:\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 853,\n                columnNumber: 19\n              }, this), \" \", overDialogData.metriResiduiBobina.toFixed(1), \" m\"]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 852,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(Typography, {\n              variant: \"body2\",\n              gutterBottom: true,\n              children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n                children: \"Eccedenza:\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 856,\n                columnNumber: 19\n              }, this), \" \", (overDialogData.metriTotaliRichiesti - overDialogData.metriResiduiBobina).toFixed(1), \" m\"]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 855,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(Typography, {\n              variant: \"body2\",\n              children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n                children: \"Cavi coinvolti:\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 859,\n                columnNumber: 19\n              }, this), \" \", overDialogData.selectedCavi]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 858,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 848,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(Typography, {\n            variant: \"body2\",\n            children: \"Vuoi continuare con l'operazione? La bobina andr\\xE0 in stato OVER.\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 862,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 840,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(DialogActions, {\n        children: [/*#__PURE__*/_jsxDEV(Button, {\n          onClick: () => setShowOverDialog(false),\n          children: \"Annulla\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 869,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Button, {\n          onClick: handleOverDialogConfirm,\n          color: \"warning\",\n          variant: \"contained\",\n          startIcon: /*#__PURE__*/_jsxDEV(WarningIcon, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 876,\n            columnNumber: 24\n          }, this),\n          children: \"Continua con OVER\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 872,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 868,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 828,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 563,\n    columnNumber: 5\n  }, this);\n};\n_s(QuickAddCablesDialog, \"AbIIuLGdIuO/w2uebXwSQvCbijs=\");\n_c = QuickAddCablesDialog;\nexport default QuickAddCablesDialog;\nvar _c;\n$RefreshReg$(_c, \"QuickAddCablesDialog\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "Dialog", "DialogTitle", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "DialogActions", "<PERSON><PERSON>", "Typography", "Box", "TextField", "CircularProgress", "<PERSON><PERSON>", "Chip", "IconButton", "Tabs", "Tab", "List", "ListItem", "ListItemText", "ListItemSecondaryAction", "Divider", "Card", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "Badge", "Add", "AddIcon", "Delete", "DeleteIcon", "Save", "SaveIcon", "Warning", "WarningIcon", "Search", "SearchIcon", "Cable", "CableIcon", "CheckCircle", "CheckCircleIcon", "caviService", "isCableInstalled", "IncompatibleReelDialog", "jsxDEV", "_jsxDEV", "Fragment", "_Fragment", "isCompatible", "cavo", "bobina", "tipologia", "String", "sezione", "getBobinaNumber", "idBobina", "parts", "split", "length", "QuickAddCablesDialog", "open", "onClose", "cantiereId", "onSuccess", "onError", "_s", "loading", "setLoading", "caviLoading", "setCaviLoading", "saving", "setSaving", "allCavi", "set<PERSON><PERSON><PERSON><PERSON>", "caviCompatibili", "setCaviCompatibili", "caviIncompatibili", "setCaviIncompatibili", "<PERSON><PERSON><PERSON>", "setSelectedCavi", "caviMetri", "setCaviMetri", "activeTab", "setActiveTab", "searchTerm", "setSearchTerm", "showIncompatibleDialog", "setShowIncompatibleDialog", "incompatibleSelection", "setIncompatibleSelection", "showOverDialog", "setShowOverDialog", "overDialogData", "setOverDialogData", "errors", "setErrors", "warnings", "setWarnings", "loadCavi", "resetDialogState", "max<PERSON><PERSON><PERSON>", "fullWidth", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "severity", "onClick", "caviData", "get<PERSON><PERSON>", "caviDisponibili", "filter", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "parseFloat", "metratura_reale", "metriTeorici", "metri_te<PERSON>ci", "modificato_manualmente", "compatibili", "incompatibili", "console", "log", "error", "message", "handleCompatibleCavoSelect", "prev", "isSelected", "some", "c", "id_cavo", "has<PERSON><PERSON>ri", "trim", "window", "confirm", "newSelected", "newCaviMetri", "prevErrors", "newErrors", "prevWarnings", "newWarnings", "handleIncompatibleCavoSelect", "handleUseIncompatibleReel", "_isIncompatible", "handleMetriChange", "cavoId", "value", "validate<PERSON>etri", "find", "isNaN", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "metriTotaliRichiesti", "Object", "entries", "id", "_", "reduce", "sum", "metri", "metriResiduiBobina", "metri_residui", "toFixed", "validateAllMetri", "<PERSON><PERSON><PERSON><PERSON>", "values", "handleSave", "forceOver", "results", "metriGiàUtilizzati", "r", "needsForceOver", "result", "updateMetri<PERSON><PERSON><PERSON>", "id_bobina", "push", "success", "wasIncompatible", "incompatibleCount", "map", "e", "join", "handleOverDialogConfirm", "filterCavi", "caviList", "toLowerCase", "includes", "ubicazione_partenza", "ubicazione_arrivo", "filteredCompatibili", "filteredIncompatibili", "isOverState", "renderCaviList", "sx", "display", "justifyContent", "my", "dense", "<PERSON><PERSON><PERSON><PERSON>", "hasWarning", "border", "borderRadius", "mb", "bgcolor", "primary", "alignItems", "gap", "fontSize", "color", "variant", "size", "label", "secondary", "mt", "type", "onChange", "target", "helperText", "FormHelperTextProps", "width", "startIcon", "title", "titleTypographyProps", "pt", "flexWrap", "fontWeight", "stato_bobina", "placeholder", "InputProps", "startAdornment", "mr", "borderBottom", "borderColor", "newValue", "badgeContent", "minHeight", "maxHeight", "overflow", "keys", "gutterBottom", "warning", "py", "primaryTypographyProps", "px", "disabled", "onConfirm", "_c", "$RefreshReg$"], "sources": ["C:/CMS/webapp/frontend/src/components/cavi/QuickAddCablesDialog.js"], "sourcesContent": ["import React, { useState, useEffect } from 'react';\nimport {\n  Dialog,\n  DialogTitle,\n  DialogContent,\n  DialogActions,\n  Button,\n  Typography,\n  Box,\n  TextField,\n  CircularProgress,\n  Alert,\n  Chip,\n  IconButton,\n  Tabs,\n  Tab,\n  List,\n  ListItem,\n  ListItemText,\n  ListItemSecondaryAction,\n  Divider,\n  Card,\n  CardContent,\n  CardHeader,\n  Badge\n} from '@mui/material';\nimport {\n  Add as AddIcon,\n  Delete as DeleteIcon,\n  Save as SaveIcon,\n  Warning as WarningIcon,\n  Search as SearchIcon,\n  Cable as CableIcon,\n  CheckCircle as CheckCircleIcon\n} from '@mui/icons-material';\nimport caviService from '../../services/caviService';\nimport { isCableInstalled } from '../../utils/stateUtils';\nimport IncompatibleReelDialog from './IncompatibleReelDialog';\n\n// Utility per verificare compatibilità tra cavo e bobina\nconst isCompatible = (cavo, bobina) => {\n  return cavo.tipologia === bobina.tipologia &&\n         String(cavo.sezione) === String(bobina.sezione);\n};\n\n// Utility per ottenere il numero della bobina dall'ID\nconst getBobinaNumber = (idBobina) => {\n  if (!idBobina) return '';\n  const parts = idBobina.split('-');\n  return parts.length > 1 ? parts[1] : idBobina;\n};\n\n/**\n * Componente per aggiungere rapidamente più cavi a una bobina\n * Implementa sistema di doppia lista per cavi compatibili/incompatibili\n * con gestione intelligente delle incompatibilità e dialog moderni\n */\nconst QuickAddCablesDialog = ({ open, onClose, bobina, cantiereId, onSuccess, onError }) => {\n  // Stati per la gestione dei dati\n  const [loading, setLoading] = useState(false);\n  const [caviLoading, setCaviLoading] = useState(false);\n  const [saving, setSaving] = useState(false);\n\n  // Stati per i dati separati\n  const [allCavi, setAllCavi] = useState([]);\n  const [caviCompatibili, setCaviCompatibili] = useState([]);\n  const [caviIncompatibili, setCaviIncompatibili] = useState([]);\n  const [selectedCavi, setSelectedCavi] = useState([]);\n  const [caviMetri, setCaviMetri] = useState({});\n\n  // Stati per la UI\n  const [activeTab, setActiveTab] = useState(0);\n  const [searchTerm, setSearchTerm] = useState('');\n  const [showIncompatibleDialog, setShowIncompatibleDialog] = useState(false);\n  const [incompatibleSelection, setIncompatibleSelection] = useState(null);\n  const [showOverDialog, setShowOverDialog] = useState(false);\n  const [overDialogData, setOverDialogData] = useState(null);\n\n  // Stati per la validazione\n  const [errors, setErrors] = useState({});\n  const [warnings, setWarnings] = useState({});\n\n  // Carica i cavi disponibili quando il dialog viene aperto\n  useEffect(() => {\n    if (open && bobina && cantiereId) {\n      loadCavi();\n      // Reset stati quando si apre il dialog\n      resetDialogState();\n    }\n  }, [open, bobina, cantiereId]);\n\n  // Early return se bobina è null per evitare errori\n  if (!bobina) {\n    return (\n      <Dialog open={open} onClose={onClose} maxWidth=\"sm\" fullWidth>\n        <DialogTitle>Errore</DialogTitle>\n        <DialogContent>\n          <Alert severity=\"error\">Nessuna bobina selezionata</Alert>\n        </DialogContent>\n        <DialogActions>\n          <Button onClick={onClose}>Chiudi</Button>\n        </DialogActions>\n      </Dialog>\n    );\n  }\n\n  // Reset dello stato del dialog\n  const resetDialogState = () => {\n    setSelectedCavi([]);\n    setCaviMetri({});\n    setErrors({});\n    setWarnings({});\n    setSearchTerm('');\n    setActiveTab(0);\n    setShowIncompatibleDialog(false);\n    setIncompatibleSelection(null);\n    setShowOverDialog(false);\n    setOverDialogData(null);\n  };\n\n  // Funzione per caricare i cavi con sistema di doppia lista\n  const loadCavi = async () => {\n    try {\n      setCaviLoading(true);\n      const caviData = await caviService.getCavi(cantiereId);\n\n      // Filtra i cavi disponibili per la posa:\n      // 1. Non installati (metratura_reale = 0)\n      // 2. Non SPARE (modificato_manualmente !== 3)\n      // 3. Hanno metri teorici > 0\n      const caviDisponibili = caviData.filter(cavo => {\n        const metriReali = parseFloat(cavo.metratura_reale) || 0;\n        const metriTeorici = parseFloat(cavo.metri_teorici) || 0;\n\n        return !isCableInstalled(cavo) &&\n               cavo.modificato_manualmente !== 3 &&\n               metriReali === 0 &&\n               metriTeorici > 0;\n      });\n\n      // Separa cavi compatibili e incompatibili (CORREZIONE: rimosso n_conduttori)\n      const compatibili = caviDisponibili.filter(cavo => isCompatible(cavo, bobina));\n      const incompatibili = caviDisponibili.filter(cavo => !isCompatible(cavo, bobina));\n\n      setAllCavi(caviDisponibili);\n      setCaviCompatibili(compatibili);\n      setCaviIncompatibili(incompatibili);\n\n      console.log(`Cavi caricati: ${compatibili.length} compatibili, ${incompatibili.length} incompatibili`);\n    } catch (error) {\n      console.error('Errore nel caricamento dei cavi:', error);\n      onError('Errore nel caricamento dei cavi: ' + (error.message || 'Errore sconosciuto'));\n    } finally {\n      setCaviLoading(false);\n    }\n  };\n\n  // Gestisce la selezione di un cavo compatibile\n  const handleCompatibleCavoSelect = (cavo) => {\n    setSelectedCavi(prev => {\n      const isSelected = prev.some(c => c.id_cavo === cavo.id_cavo);\n\n      if (isSelected) {\n        // Conferma prima di rimuovere se ci sono metri inseriti\n        const hasMetri = caviMetri[cavo.id_cavo] && caviMetri[cavo.id_cavo].trim() !== '';\n        if (hasMetri) {\n          if (!window.confirm(`Rimuovere il cavo ${cavo.id_cavo} dalla selezione? I metri inseriti (${caviMetri[cavo.id_cavo]}m) andranno persi.`)) {\n            return prev;\n          }\n        }\n\n        // Rimuovi il cavo dalla selezione\n        const newSelected = prev.filter(c => c.id_cavo !== cavo.id_cavo);\n\n        // Rimuovi anche i metri associati\n        const newCaviMetri = { ...caviMetri };\n        delete newCaviMetri[cavo.id_cavo];\n        setCaviMetri(newCaviMetri);\n\n        // Rimuovi errori e warning\n        setErrors(prevErrors => {\n          const newErrors = { ...prevErrors };\n          delete newErrors[cavo.id_cavo];\n          return newErrors;\n        });\n        setWarnings(prevWarnings => {\n          const newWarnings = { ...prevWarnings };\n          delete newWarnings[cavo.id_cavo];\n          return newWarnings;\n        });\n\n        return newSelected;\n      } else {\n        // Aggiungi il cavo alla selezione\n        return [...prev, cavo];\n      }\n    });\n  };\n\n  // Gestisce la selezione di un cavo incompatibile\n  const handleIncompatibleCavoSelect = (cavo) => {\n    setIncompatibleSelection({ cavo, bobina });\n    setShowIncompatibleDialog(true);\n  };\n\n  // Gestisce l'uso di una bobina incompatibile\n  const handleUseIncompatibleReel = () => {\n    if (incompatibleSelection) {\n      const { cavo } = incompatibleSelection;\n\n      // Aggiungi il cavo alla selezione con flag di incompatibilità\n      setSelectedCavi(prev => [...prev, { ...cavo, _isIncompatible: true }]);\n\n      setShowIncompatibleDialog(false);\n      setIncompatibleSelection(null);\n\n      onSuccess?.(`Cavo incompatibile ${cavo.id_cavo} aggiunto alla selezione. Sarà utilizzato con force_over.`);\n    }\n  };\n\n  // Gestisce l'input dei metri posati per un cavo\n  const handleMetriChange = (cavoId, value) => {\n    // Aggiorna i metri per il cavo\n    setCaviMetri(prev => ({\n      ...prev,\n      [cavoId]: value\n    }));\n\n    // Valida il valore inserito in tempo reale\n    validateMetri(cavoId, value);\n  };\n\n  // Valida i metri inseriti per un cavo con feedback migliorato\n  const validateMetri = (cavoId, value) => {\n    const cavo = allCavi.find(c => c.id_cavo === cavoId);\n    if (!cavo) return;\n\n    // Resetta gli errori e gli avvisi per questo cavo\n    setErrors(prev => {\n      const newErrors = { ...prev };\n      delete newErrors[cavoId];\n      return newErrors;\n    });\n\n    setWarnings(prev => {\n      const newWarnings = { ...prev };\n      delete newWarnings[cavoId];\n      return newWarnings;\n    });\n\n    // Controllo input vuoto\n    if (!value || value.trim() === '') {\n      return true; // Non mostrare errore per input vuoto durante la digitazione\n    }\n\n    // Controllo formato numerico\n    if (isNaN(parseFloat(value)) || parseFloat(value) <= 0) {\n      setErrors(prev => ({\n        ...prev,\n        [cavoId]: 'Inserire un valore numerico positivo'\n      }));\n      return false;\n    }\n\n    const metriPosati = parseFloat(value);\n\n    // Controllo metri teorici cavo\n    if (cavo.metri_teorici && metriPosati > parseFloat(cavo.metri_teorici)) {\n      setWarnings(prev => ({\n        ...prev,\n        [cavoId]: `I metri posati (${metriPosati}m) superano i metri teorici del cavo (${cavo.metri_teorici}m)`\n      }));\n    }\n\n    // Controllo metri residui bobina con calcolo in tempo reale\n    const metriTotaliRichiesti = Object.entries(caviMetri)\n      .filter(([id, _]) => id !== cavoId) // Escludi il cavo corrente\n      .reduce((sum, [_, metri]) => sum + parseFloat(metri || 0), 0) + metriPosati;\n\n    const metriResiduiBobina = bobina?.metri_residui || 0;\n    if (metriTotaliRichiesti > metriResiduiBobina) {\n      setWarnings(prev => ({\n        ...prev,\n        [cavoId]: `Totale richiesto: ${metriTotaliRichiesti.toFixed(1)}m > Residui bobina: ${metriResiduiBobina.toFixed(1)}m (OVER)`\n      }));\n    }\n\n    return true;\n  };\n\n  // Valida tutti i metri inseriti con dialog moderno per OVER\n  const validateAllMetri = () => {\n    let isValid = true;\n    const newErrors = {};\n    const newWarnings = {};\n\n    // Verifica che ci siano cavi selezionati\n    if (selectedCavi.length === 0) {\n      onError('Seleziona almeno un cavo');\n      return false;\n    }\n\n    // Verifica che tutti i cavi selezionati abbiano metri inseriti\n    for (const cavo of selectedCavi) {\n      const metri = caviMetri[cavo.id_cavo];\n\n      // Controllo input vuoto\n      if (!metri || metri.trim() === '') {\n        newErrors[cavo.id_cavo] = 'Inserire un valore per i metri posati';\n        isValid = false;\n        continue;\n      }\n\n      // Controllo formato numerico\n      if (isNaN(parseFloat(metri)) || parseFloat(metri) <= 0) {\n        newErrors[cavo.id_cavo] = 'Inserire un valore numerico positivo';\n        isValid = false;\n        continue;\n      }\n\n      const metriPosati = parseFloat(metri);\n\n      // Controllo metri teorici cavo\n      if (cavo.metri_teorici && metriPosati > parseFloat(cavo.metri_teorici)) {\n        newWarnings[cavo.id_cavo] = `I metri posati (${metriPosati}m) superano i metri teorici del cavo (${cavo.metri_teorici}m)`;\n      }\n    }\n\n    setErrors(newErrors);\n    setWarnings(newWarnings);\n\n    // Se ci sono errori, non procedere\n    if (!isValid) {\n      return false;\n    }\n\n    // Verifica stato OVER della bobina\n    const metriTotaliRichiesti = Object.values(caviMetri).reduce((sum, metri) => sum + parseFloat(metri || 0), 0);\n    const metriResiduiBobina = bobina?.metri_residui || 0;\n    if (metriTotaliRichiesti > metriResiduiBobina) {\n      // Mostra dialog moderno invece di window.confirm\n      setOverDialogData({\n        metriTotaliRichiesti,\n        metriResiduiBobina,\n        selectedCavi: selectedCavi.length\n      });\n      setShowOverDialog(true);\n      return false; // Interrompi qui, il salvataggio continuerà dal dialog\n    }\n\n    return true;\n  };\n\n  // Gestisce il salvataggio dei dati con gestione migliorata\n  const handleSave = async (forceOver = false) => {\n    try {\n      // Validazione solo se non è un force over\n      if (!forceOver && !validateAllMetri()) {\n        return;\n      }\n\n      setSaving(true);\n\n      // Aggiorna ogni cavo selezionato\n      const results = [];\n      let errors = [];\n\n      for (const cavo of selectedCavi) {\n        try {\n          const metriPosati = parseFloat(caviMetri[cavo.id_cavo]);\n\n          // Determina se è necessario forzare lo stato OVER della bobina o incompatibilità\n          const metriGiàUtilizzati = results.reduce((sum, r) => sum + r.metriPosati, 0);\n          const metriResiduiBobina = bobina?.metri_residui || 0;\n          const needsForceOver = forceOver || (metriGiàUtilizzati + metriPosati) > metriResiduiBobina || cavo._isIncompatible;\n\n          // Aggiorna i metri posati del cavo\n          const result = await caviService.updateMetriPosati(\n            cantiereId,\n            cavo.id_cavo,\n            metriPosati,\n            bobina.id_bobina,\n            needsForceOver\n          );\n\n          results.push({\n            cavo: cavo.id_cavo,\n            metriPosati,\n            success: true,\n            wasIncompatible: cavo._isIncompatible\n          });\n        } catch (error) {\n          console.error(`Errore nell'aggiornamento del cavo ${cavo.id_cavo}:`, error);\n          errors.push({\n            cavo: cavo.id_cavo,\n            error: error.message || 'Errore sconosciuto'\n          });\n        }\n      }\n\n      // Gestione del risultato con messaggi migliorati\n      if (errors.length === 0) {\n        const incompatibleCount = results.filter(r => r.wasIncompatible).length;\n        let message = `${results.length} cavi aggiornati con successo`;\n        if (incompatibleCount > 0) {\n          message += ` (${incompatibleCount} incompatibili con force_over)`;\n        }\n        onSuccess(message);\n        onClose();\n      } else if (results.length > 0) {\n        onSuccess(`${results.length} cavi aggiornati con successo, ${errors.length} falliti`);\n        onError(`Errori: ${errors.map(e => `${e.cavo}: ${e.error}`).join(', ')}`);\n        onClose();\n      } else {\n        onError(`Nessun cavo aggiornato. Errori: ${errors.map(e => `${e.cavo}: ${e.error}`).join(', ')}`);\n      }\n    } catch (error) {\n      console.error('Errore durante il salvataggio:', error);\n      onError('Errore durante il salvataggio: ' + (error.message || 'Errore sconosciuto'));\n    } finally {\n      setSaving(false);\n    }\n  };\n\n  // Gestisce la conferma del dialog OVER\n  const handleOverDialogConfirm = () => {\n    setShowOverDialog(false);\n    setOverDialogData(null);\n    handleSave(true); // Procedi con force_over\n  };\n\n  // Filtra i cavi in base al termine di ricerca\n  const filterCavi = (caviList) => {\n    if (!searchTerm) return caviList;\n\n    return caviList.filter(cavo =>\n      cavo.id_cavo.toLowerCase().includes(searchTerm.toLowerCase()) ||\n      (cavo.tipologia && cavo.tipologia.toLowerCase().includes(searchTerm.toLowerCase())) ||\n      (cavo.ubicazione_partenza && cavo.ubicazione_partenza.toLowerCase().includes(searchTerm.toLowerCase())) ||\n      (cavo.ubicazione_arrivo && cavo.ubicazione_arrivo.toLowerCase().includes(searchTerm.toLowerCase()))\n    );\n  };\n\n  const filteredCompatibili = filterCavi(caviCompatibili);\n  const filteredIncompatibili = filterCavi(caviIncompatibili);\n\n  // Calcola statistiche con controlli di sicurezza\n  const metriTotaliRichiesti = Object.values(caviMetri).reduce((sum, metri) => {\n    const value = parseFloat(metri || 0);\n    return isNaN(value) ? sum : sum + value;\n  }, 0);\n\n  const metriResiduiBobina = bobina?.metri_residui || 0;\n  const isOverState = metriTotaliRichiesti > metriResiduiBobina;\n\n  // Componente per renderizzare una lista di cavi\n  const renderCaviList = (caviList, isCompatible = true) => {\n    if (caviLoading) {\n      return (\n        <Box sx={{ display: 'flex', justifyContent: 'center', my: 4 }}>\n          <CircularProgress />\n        </Box>\n      );\n    }\n\n    if (caviList.length === 0) {\n      return (\n        <Alert severity=\"info\" sx={{ my: 2 }}>\n          {isCompatible ? 'Nessun cavo compatibile disponibile.' : 'Nessun cavo incompatibile trovato.'}\n        </Alert>\n      );\n    }\n\n    return (\n      <List dense>\n        {caviList.map((cavo) => {\n          const isSelected = selectedCavi.some(c => c.id_cavo === cavo.id_cavo);\n          const hasMetri = caviMetri[cavo.id_cavo] && caviMetri[cavo.id_cavo].trim() !== '';\n          const hasError = errors[cavo.id_cavo];\n          const hasWarning = warnings[cavo.id_cavo];\n\n          return (\n            <ListItem\n              key={cavo.id_cavo}\n              sx={{\n                border: '1px solid #e0e0e0',\n                borderRadius: 1,\n                mb: 1,\n                bgcolor: isSelected ? 'rgba(33, 150, 243, 0.1)' : '#f5f7fa',\n                '&:hover': {\n                  bgcolor: isSelected ? 'rgba(33, 150, 243, 0.2)' : 'rgba(33, 150, 243, 0.05)'\n                }\n              }}\n            >\n              <ListItemText\n                primary={\n                  <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>\n                    <CableIcon fontSize=\"small\" color={isCompatible ? 'success' : 'warning'} />\n                    <Typography variant=\"subtitle2\">{cavo.id_cavo}</Typography>\n                    <Chip\n                      size=\"small\"\n                      label={cavo.tipologia || 'N/A'}\n                      color={isCompatible ? 'success' : 'warning'}\n                      variant=\"outlined\"\n                    />\n                    {!isCompatible && (\n                      <Chip\n                        size=\"small\"\n                        label=\"INCOMPATIBILE\"\n                        color=\"error\"\n                        variant=\"filled\"\n                      />\n                    )}\n                  </Box>\n                }\n                secondary={\n                  <Box>\n                    <Typography variant=\"body2\" color=\"text.secondary\">\n                      {cavo.ubicazione_partenza || 'N/A'} → {cavo.ubicazione_arrivo || 'N/A'}\n                    </Typography>\n                    <Typography variant=\"body2\" color=\"text.secondary\">\n                      Formazione: {cavo.sezione || 'N/A'} | Metri teorici: {cavo.metri_teorici || 'N/A'}\n                    </Typography>\n                    {isSelected && (\n                      <Box sx={{ mt: 1 }}>\n                        <TextField\n                          size=\"small\"\n                          type=\"number\"\n                          label=\"Metri posati\"\n                          value={caviMetri[cavo.id_cavo] || ''}\n                          onChange={(e) => handleMetriChange(cavo.id_cavo, e.target.value)}\n                          error={!!hasError}\n                          helperText={hasError || hasWarning}\n                          FormHelperTextProps={{\n                            sx: { color: hasWarning && !hasError ? 'warning.main' : 'error.main' }\n                          }}\n                          sx={{ width: '200px' }}\n                        />\n                      </Box>\n                    )}\n                  </Box>\n                }\n              />\n              <ListItemSecondaryAction>\n                <Button\n                  size=\"small\"\n                  variant={isSelected ? 'contained' : 'outlined'}\n                  color={isCompatible ? 'primary' : 'warning'}\n                  onClick={() => isCompatible ? handleCompatibleCavoSelect(cavo) : handleIncompatibleCavoSelect(cavo)}\n                  startIcon={isSelected ? <CheckCircleIcon /> : <AddIcon />}\n                >\n                  {isSelected ? 'Selezionato' : 'Seleziona'}\n                </Button>\n              </ListItemSecondaryAction>\n            </ListItem>\n          );\n        })}\n      </List>\n    );\n  };\n\n  return (\n    <Dialog open={open} onClose={onClose} maxWidth=\"xl\" fullWidth>\n      <DialogTitle>\n        <Box sx={{ display: 'flex', alignItems: 'center', gap: 2 }}>\n          <CableIcon />\n          <Typography variant=\"h6\">\n            Aggiungi cavi alla bobina {getBobinaNumber(bobina?.id_bobina)}\n          </Typography>\n        </Box>\n      </DialogTitle>\n      <DialogContent>\n        {/* Informazioni sulla bobina */}\n            <Card sx={{ mb: 3 }}>\n              <CardHeader\n                title=\"Dettagli bobina\"\n                titleTypographyProps={{ variant: 'subtitle1' }}\n              />\n              <CardContent sx={{ pt: 0 }}>\n                <Box sx={{ display: 'flex', flexWrap: 'wrap', gap: 3 }}>\n                  <Box>\n                    <Typography variant=\"body2\" color=\"text.secondary\">ID Bobina</Typography>\n                    <Typography variant=\"body1\">{bobina.id_bobina}</Typography>\n                  </Box>\n                  <Box>\n                    <Typography variant=\"body2\" color=\"text.secondary\">Tipologia</Typography>\n                    <Typography variant=\"body1\">{bobina.tipologia || 'N/A'}</Typography>\n                  </Box>\n                  <Box>\n                    <Typography variant=\"body2\" color=\"text.secondary\">Formazione</Typography>\n                    <Typography variant=\"body1\">{bobina.sezione || 'N/A'}</Typography>\n                  </Box>\n                  <Box>\n                    <Typography variant=\"body2\" color=\"text.secondary\">Metri residui</Typography>\n                    <Typography\n                      variant=\"body1\"\n                      color={isOverState ? 'error.main' : 'text.primary'}\n                      sx={{ fontWeight: isOverState ? 'bold' : 'normal' }}\n                    >\n                      {metriResiduiBobina.toFixed(1)} m\n                    </Typography>\n                  </Box>\n                  <Box>\n                    <Typography variant=\"body2\" color=\"text.secondary\">Stato</Typography>\n                    <Chip\n                      label={bobina.stato_bobina || 'N/D'}\n                      size=\"small\"\n                      color={\n                        bobina.stato_bobina === 'Disponibile' ? 'success' :\n                        bobina.stato_bobina === 'In uso' ? 'primary' :\n                        bobina.stato_bobina === 'Over' ? 'error' :\n                        bobina.stato_bobina === 'Terminata' ? 'warning' : 'default'\n                      }\n                    />\n                  </Box>\n                </Box>\n              </CardContent>\n            </Card>\n\n            {/* Ricerca cavi */}\n            <TextField\n              fullWidth\n              label=\"Ricerca intelligente cavi\"\n              variant=\"outlined\"\n              value={searchTerm}\n              onChange={(e) => setSearchTerm(e.target.value)}\n              placeholder=\"Cerca per ID, tipologia, ubicazione...\"\n              InputProps={{\n                startAdornment: <SearchIcon sx={{ mr: 1, color: 'text.secondary' }} />\n              }}\n              sx={{ mb: 3 }}\n            />\n\n            {/* Sistema di tabs per cavi compatibili/incompatibili */}\n            <Box sx={{ borderBottom: 1, borderColor: 'divider', mb: 2 }}>\n              <Tabs value={activeTab} onChange={(e, newValue) => setActiveTab(newValue)}>\n                <Tab\n                  label={\n                    <Badge badgeContent={filteredCompatibili.length} color=\"success\">\n                      <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>\n                        <CheckCircleIcon fontSize=\"small\" />\n                        Cavi compatibili\n                      </Box>\n                    </Badge>\n                  }\n                />\n                <Tab\n                  label={\n                    <Badge badgeContent={filteredIncompatibili.length} color=\"warning\">\n                      <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>\n                        <WarningIcon fontSize=\"small\" />\n                        Cavi incompatibili\n                      </Box>\n                    </Badge>\n                  }\n                />\n              </Tabs>\n            </Box>\n\n            {/* Contenuto dei tabs */}\n            <Box sx={{ minHeight: 300, maxHeight: 400, overflow: 'auto' }}>\n              {activeTab === 0 && (\n                <Box>\n                  <Typography variant=\"body2\" color=\"text.secondary\" sx={{ mb: 2 }}>\n                    Cavi compatibili con tipologia <strong>{bobina.tipologia}</strong> e formazione <strong>{bobina.sezione}</strong>\n                  </Typography>\n                  {renderCaviList(filteredCompatibili, true)}\n                </Box>\n              )}\n              {activeTab === 1 && (\n                <Box>\n                  <Alert severity=\"warning\" sx={{ mb: 2 }}>\n                    <Typography variant=\"body2\">\n                      I cavi incompatibili possono essere utilizzati con <strong>force_over</strong>,\n                      ma potrebbero non rispettare le specifiche tecniche della bobina.\n                    </Typography>\n                  </Alert>\n                  {renderCaviList(filteredIncompatibili, false)}\n                </Box>\n              )}\n            </Box>\n\n            {/* Riepilogo selezione migliorato */}\n            {selectedCavi.length > 0 && (\n              <Card sx={{ mt: 3, mb: 2 }}>\n                <CardHeader\n                  title={`Riepilogo selezione (${selectedCavi.length} cavi)`}\n                  titleTypographyProps={{ variant: 'subtitle1' }}\n                />\n                <CardContent sx={{ pt: 0 }}>\n                  <Box sx={{ mb: 2 }}>\n                    <Box sx={{ display: 'flex', justifyContent: 'space-between', mb: 1 }}>\n                      <Typography variant=\"body2\">Metri totali richiesti:</Typography>\n                      <Typography\n                        variant=\"body2\"\n                        sx={{\n                          fontWeight: 'bold',\n                          color: isOverState ? 'error.main' : 'text.primary'\n                        }}\n                      >\n                        {metriTotaliRichiesti.toFixed(1)} m\n                      </Typography>\n                    </Box>\n                    <Box sx={{ display: 'flex', justifyContent: 'space-between', mb: 1 }}>\n                      <Typography variant=\"body2\">Metri residui bobina:</Typography>\n                      <Typography variant=\"body2\" sx={{ fontWeight: 'bold' }}>\n                        {metriResiduiBobina.toFixed(1)} m\n                      </Typography>\n                    </Box>\n                    <Divider sx={{ my: 1 }} />\n                    <Box sx={{ display: 'flex', justifyContent: 'space-between' }}>\n                      <Typography variant=\"body2\">Differenza:</Typography>\n                      <Typography\n                        variant=\"body2\"\n                        sx={{\n                          fontWeight: 'bold',\n                          color: isOverState ? 'error.main' : 'success.main'\n                        }}\n                      >\n                        {(metriResiduiBobina - metriTotaliRichiesti).toFixed(1)} m\n                        {isOverState && ' (OVER)'}\n                      </Typography>\n                    </Box>\n                  </Box>\n\n                  {isOverState && (\n                    <Alert severity=\"warning\" sx={{ mb: 2 }}>\n                      <Typography variant=\"body2\">\n                        <strong>ATTENZIONE:</strong> I metri richiesti superano i metri residui della bobina.\n                        La bobina andrà in stato OVER.\n                      </Typography>\n                    </Alert>\n                  )}\n\n                  <List dense>\n                    {selectedCavi.map((cavo) => (\n                      <ListItem\n                        key={cavo.id_cavo}\n                        sx={{\n                          border: '1px solid #e0e0e0',\n                          borderRadius: 1,\n                          mb: 1,\n                          bgcolor: cavo._isIncompatible ? 'warning.light' : 'background.paper'\n                        }}\n                      >\n                        <ListItemText\n                          primary={\n                            <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>\n                              <Typography variant=\"subtitle2\">{cavo.id_cavo}</Typography>\n                              {cavo._isIncompatible && (\n                                <Chip size=\"small\" label=\"INCOMPATIBILE\" color=\"warning\" />\n                              )}\n                            </Box>\n                          }\n                          secondary={`${caviMetri[cavo.id_cavo] || '0'} metri posati`}\n                        />\n                        <ListItemSecondaryAction>\n                          <IconButton\n                            size=\"small\"\n                            color=\"error\"\n                            onClick={() => handleCompatibleCavoSelect(cavo)}\n                            title=\"Rimuovi dalla selezione\"\n                          >\n                            <DeleteIcon fontSize=\"small\" />\n                          </IconButton>\n                        </ListItemSecondaryAction>\n                      </ListItem>\n                    ))}\n                  </List>\n                </CardContent>\n              </Card>\n            )}\n\n            {/* Avvisi globali */}\n            {Object.keys(warnings).length > 0 && (\n              <Alert severity=\"warning\" sx={{ mt: 2 }}>\n                <Typography variant=\"subtitle2\" gutterBottom>Avvisi:</Typography>\n                <List dense>\n                  {Object.entries(warnings).map(([cavoId, warning]) => (\n                    <ListItem key={cavoId} sx={{ py: 0 }}>\n                      <ListItemText\n                        primary={`${cavoId}: ${warning}`}\n                        primaryTypographyProps={{ variant: 'body2' }}\n                      />\n                    </ListItem>\n                  ))}\n                </List>\n              </Alert>\n            )}\n\n            {/* Istruzioni */}\n            <Alert severity=\"info\" sx={{ mt: 2 }}>\n              <Typography variant=\"body2\">\n                <strong>Istruzioni:</strong> Seleziona i cavi dalle liste sopra e inserisci i metri posati.\n                I cavi compatibili sono consigliati, quelli incompatibili richiedono conferma esplicita.\n              </Typography>\n            </Alert>\n      </DialogContent>\n\n      <DialogActions sx={{ px: 3, py: 2 }}>\n        <Button onClick={onClose} disabled={saving}>\n          Annulla\n        </Button>\n        <Button\n          onClick={() => handleSave(false)}\n          color=\"primary\"\n          variant=\"contained\"\n          disabled={saving || selectedCavi.length === 0 || Object.keys(errors).length > 0}\n          startIcon={saving ? <CircularProgress size={20} /> : <SaveIcon />}\n        >\n          {saving ? 'Salvataggio...' : `Salva ${selectedCavi.length} cavi`}\n        </Button>\n      </DialogActions>\n\n      {/* Dialog per incompatibilità */}\n      <IncompatibleReelDialog\n        open={showIncompatibleDialog}\n        onClose={() => {\n          setShowIncompatibleDialog(false);\n          setIncompatibleSelection(null);\n        }}\n        cavo={incompatibleSelection?.cavo}\n        bobina={incompatibleSelection?.bobina}\n        onConfirm={handleUseIncompatibleReel}\n      />\n\n      {/* Dialog moderno per conferma OVER */}\n      <Dialog\n        open={showOverDialog}\n        onClose={() => setShowOverDialog(false)}\n        maxWidth=\"sm\"\n        fullWidth\n      >\n        <DialogTitle>\n          <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>\n            <WarningIcon color=\"warning\" />\n            <Typography variant=\"h6\">Conferma stato OVER</Typography>\n          </Box>\n        </DialogTitle>\n        <DialogContent>\n          {overDialogData && (\n            <>\n              <Alert severity=\"warning\" sx={{ mb: 2 }}>\n                <Typography variant=\"body1\" gutterBottom>\n                  <strong>ATTENZIONE:</strong> L'operazione porterà la bobina in stato OVER.\n                </Typography>\n              </Alert>\n              <Box sx={{ mb: 2 }}>\n                <Typography variant=\"body2\" gutterBottom>\n                  <strong>Metri totali richiesti:</strong> {overDialogData.metriTotaliRichiesti.toFixed(1)} m\n                </Typography>\n                <Typography variant=\"body2\" gutterBottom>\n                  <strong>Metri residui bobina:</strong> {overDialogData.metriResiduiBobina.toFixed(1)} m\n                </Typography>\n                <Typography variant=\"body2\" gutterBottom>\n                  <strong>Eccedenza:</strong> {(overDialogData.metriTotaliRichiesti - overDialogData.metriResiduiBobina).toFixed(1)} m\n                </Typography>\n                <Typography variant=\"body2\">\n                  <strong>Cavi coinvolti:</strong> {overDialogData.selectedCavi}\n                </Typography>\n              </Box>\n              <Typography variant=\"body2\">\n                Vuoi continuare con l'operazione? La bobina andrà in stato OVER.\n              </Typography>\n            </>\n          )}\n        </DialogContent>\n        <DialogActions>\n          <Button onClick={() => setShowOverDialog(false)}>\n            Annulla\n          </Button>\n          <Button\n            onClick={handleOverDialogConfirm}\n            color=\"warning\"\n            variant=\"contained\"\n            startIcon={<WarningIcon />}\n          >\n            Continua con OVER\n          </Button>\n        </DialogActions>\n      </Dialog>\n    </Dialog>\n  );\n};\n\nexport default QuickAddCablesDialog;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAClD,SACEC,MAAM,EACNC,WAAW,EACXC,aAAa,EACbC,aAAa,EACbC,MAAM,EACNC,UAAU,EACVC,GAAG,EACHC,SAAS,EACTC,gBAAgB,EAChBC,KAAK,EACLC,IAAI,EACJC,UAAU,EACVC,IAAI,EACJC,GAAG,EACHC,IAAI,EACJC,QAAQ,EACRC,YAAY,EACZC,uBAAuB,EACvBC,OAAO,EACPC,IAAI,EACJC,WAAW,EACXC,UAAU,EACVC,KAAK,QACA,eAAe;AACtB,SACEC,GAAG,IAAIC,OAAO,EACdC,MAAM,IAAIC,UAAU,EACpBC,IAAI,IAAIC,QAAQ,EAChBC,OAAO,IAAIC,WAAW,EACtBC,MAAM,IAAIC,UAAU,EACpBC,KAAK,IAAIC,SAAS,EAClBC,WAAW,IAAIC,eAAe,QACzB,qBAAqB;AAC5B,OAAOC,WAAW,MAAM,4BAA4B;AACpD,SAASC,gBAAgB,QAAQ,wBAAwB;AACzD,OAAOC,sBAAsB,MAAM,0BAA0B;;AAE7D;AAAA,SAAAC,MAAA,IAAAC,OAAA,EAAAC,QAAA,IAAAC,SAAA;AACA,MAAMC,YAAY,GAAGA,CAACC,IAAI,EAAEC,MAAM,KAAK;EACrC,OAAOD,IAAI,CAACE,SAAS,KAAKD,MAAM,CAACC,SAAS,IACnCC,MAAM,CAACH,IAAI,CAACI,OAAO,CAAC,KAAKD,MAAM,CAACF,MAAM,CAACG,OAAO,CAAC;AACxD,CAAC;;AAED;AACA,MAAMC,eAAe,GAAIC,QAAQ,IAAK;EACpC,IAAI,CAACA,QAAQ,EAAE,OAAO,EAAE;EACxB,MAAMC,KAAK,GAAGD,QAAQ,CAACE,KAAK,CAAC,GAAG,CAAC;EACjC,OAAOD,KAAK,CAACE,MAAM,GAAG,CAAC,GAAGF,KAAK,CAAC,CAAC,CAAC,GAAGD,QAAQ;AAC/C,CAAC;;AAED;AACA;AACA;AACA;AACA;AACA,MAAMI,oBAAoB,GAAGA,CAAC;EAAEC,IAAI;EAAEC,OAAO;EAAEX,MAAM;EAAEY,UAAU;EAAEC,SAAS;EAAEC;AAAQ,CAAC,KAAK;EAAAC,EAAA;EAC1F;EACA,MAAM,CAACC,OAAO,EAAEC,UAAU,CAAC,GAAGjE,QAAQ,CAAC,KAAK,CAAC;EAC7C,MAAM,CAACkE,WAAW,EAAEC,cAAc,CAAC,GAAGnE,QAAQ,CAAC,KAAK,CAAC;EACrD,MAAM,CAACoE,MAAM,EAAEC,SAAS,CAAC,GAAGrE,QAAQ,CAAC,KAAK,CAAC;;EAE3C;EACA,MAAM,CAACsE,OAAO,EAAEC,UAAU,CAAC,GAAGvE,QAAQ,CAAC,EAAE,CAAC;EAC1C,MAAM,CAACwE,eAAe,EAAEC,kBAAkB,CAAC,GAAGzE,QAAQ,CAAC,EAAE,CAAC;EAC1D,MAAM,CAAC0E,iBAAiB,EAAEC,oBAAoB,CAAC,GAAG3E,QAAQ,CAAC,EAAE,CAAC;EAC9D,MAAM,CAAC4E,YAAY,EAAEC,eAAe,CAAC,GAAG7E,QAAQ,CAAC,EAAE,CAAC;EACpD,MAAM,CAAC8E,SAAS,EAAEC,YAAY,CAAC,GAAG/E,QAAQ,CAAC,CAAC,CAAC,CAAC;;EAE9C;EACA,MAAM,CAACgF,SAAS,EAAEC,YAAY,CAAC,GAAGjF,QAAQ,CAAC,CAAC,CAAC;EAC7C,MAAM,CAACkF,UAAU,EAAEC,aAAa,CAAC,GAAGnF,QAAQ,CAAC,EAAE,CAAC;EAChD,MAAM,CAACoF,sBAAsB,EAAEC,yBAAyB,CAAC,GAAGrF,QAAQ,CAAC,KAAK,CAAC;EAC3E,MAAM,CAACsF,qBAAqB,EAAEC,wBAAwB,CAAC,GAAGvF,QAAQ,CAAC,IAAI,CAAC;EACxE,MAAM,CAACwF,cAAc,EAAEC,iBAAiB,CAAC,GAAGzF,QAAQ,CAAC,KAAK,CAAC;EAC3D,MAAM,CAAC0F,cAAc,EAAEC,iBAAiB,CAAC,GAAG3F,QAAQ,CAAC,IAAI,CAAC;;EAE1D;EACA,MAAM,CAAC4F,MAAM,EAAEC,SAAS,CAAC,GAAG7F,QAAQ,CAAC,CAAC,CAAC,CAAC;EACxC,MAAM,CAAC8F,QAAQ,EAAEC,WAAW,CAAC,GAAG/F,QAAQ,CAAC,CAAC,CAAC,CAAC;;EAE5C;EACAC,SAAS,CAAC,MAAM;IACd,IAAIyD,IAAI,IAAIV,MAAM,IAAIY,UAAU,EAAE;MAChCoC,QAAQ,CAAC,CAAC;MACV;MACAC,gBAAgB,CAAC,CAAC;IACpB;EACF,CAAC,EAAE,CAACvC,IAAI,EAAEV,MAAM,EAAEY,UAAU,CAAC,CAAC;;EAE9B;EACA,IAAI,CAACZ,MAAM,EAAE;IACX,oBACEL,OAAA,CAACzC,MAAM;MAACwD,IAAI,EAAEA,IAAK;MAACC,OAAO,EAAEA,OAAQ;MAACuC,QAAQ,EAAC,IAAI;MAACC,SAAS;MAAAC,QAAA,gBAC3DzD,OAAA,CAACxC,WAAW;QAAAiG,QAAA,EAAC;MAAM;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAa,CAAC,eACjC7D,OAAA,CAACvC,aAAa;QAAAgG,QAAA,eACZzD,OAAA,CAAChC,KAAK;UAAC8F,QAAQ,EAAC,OAAO;UAAAL,QAAA,EAAC;QAA0B;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAO;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC7C,CAAC,eAChB7D,OAAA,CAACtC,aAAa;QAAA+F,QAAA,eACZzD,OAAA,CAACrC,MAAM;UAACoG,OAAO,EAAE/C,OAAQ;UAAAyC,QAAA,EAAC;QAAM;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC5B,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACV,CAAC;EAEb;;EAEA;EACA,MAAMP,gBAAgB,GAAGA,CAAA,KAAM;IAC7BpB,eAAe,CAAC,EAAE,CAAC;IACnBE,YAAY,CAAC,CAAC,CAAC,CAAC;IAChBc,SAAS,CAAC,CAAC,CAAC,CAAC;IACbE,WAAW,CAAC,CAAC,CAAC,CAAC;IACfZ,aAAa,CAAC,EAAE,CAAC;IACjBF,YAAY,CAAC,CAAC,CAAC;IACfI,yBAAyB,CAAC,KAAK,CAAC;IAChCE,wBAAwB,CAAC,IAAI,CAAC;IAC9BE,iBAAiB,CAAC,KAAK,CAAC;IACxBE,iBAAiB,CAAC,IAAI,CAAC;EACzB,CAAC;;EAED;EACA,MAAMK,QAAQ,GAAG,MAAAA,CAAA,KAAY;IAC3B,IAAI;MACF7B,cAAc,CAAC,IAAI,CAAC;MACpB,MAAMwC,QAAQ,GAAG,MAAMpE,WAAW,CAACqE,OAAO,CAAChD,UAAU,CAAC;;MAEtD;MACA;MACA;MACA;MACA,MAAMiD,eAAe,GAAGF,QAAQ,CAACG,MAAM,CAAC/D,IAAI,IAAI;QAC9C,MAAMgE,UAAU,GAAGC,UAAU,CAACjE,IAAI,CAACkE,eAAe,CAAC,IAAI,CAAC;QACxD,MAAMC,YAAY,GAAGF,UAAU,CAACjE,IAAI,CAACoE,aAAa,CAAC,IAAI,CAAC;QAExD,OAAO,CAAC3E,gBAAgB,CAACO,IAAI,CAAC,IACvBA,IAAI,CAACqE,sBAAsB,KAAK,CAAC,IACjCL,UAAU,KAAK,CAAC,IAChBG,YAAY,GAAG,CAAC;MACzB,CAAC,CAAC;;MAEF;MACA,MAAMG,WAAW,GAAGR,eAAe,CAACC,MAAM,CAAC/D,IAAI,IAAID,YAAY,CAACC,IAAI,EAAEC,MAAM,CAAC,CAAC;MAC9E,MAAMsE,aAAa,GAAGT,eAAe,CAACC,MAAM,CAAC/D,IAAI,IAAI,CAACD,YAAY,CAACC,IAAI,EAAEC,MAAM,CAAC,CAAC;MAEjFuB,UAAU,CAACsC,eAAe,CAAC;MAC3BpC,kBAAkB,CAAC4C,WAAW,CAAC;MAC/B1C,oBAAoB,CAAC2C,aAAa,CAAC;MAEnCC,OAAO,CAACC,GAAG,CAAC,kBAAkBH,WAAW,CAAC7D,MAAM,iBAAiB8D,aAAa,CAAC9D,MAAM,gBAAgB,CAAC;IACxG,CAAC,CAAC,OAAOiE,KAAK,EAAE;MACdF,OAAO,CAACE,KAAK,CAAC,kCAAkC,EAAEA,KAAK,CAAC;MACxD3D,OAAO,CAAC,mCAAmC,IAAI2D,KAAK,CAACC,OAAO,IAAI,oBAAoB,CAAC,CAAC;IACxF,CAAC,SAAS;MACRvD,cAAc,CAAC,KAAK,CAAC;IACvB;EACF,CAAC;;EAED;EACA,MAAMwD,0BAA0B,GAAI5E,IAAI,IAAK;IAC3C8B,eAAe,CAAC+C,IAAI,IAAI;MACtB,MAAMC,UAAU,GAAGD,IAAI,CAACE,IAAI,CAACC,CAAC,IAAIA,CAAC,CAACC,OAAO,KAAKjF,IAAI,CAACiF,OAAO,CAAC;MAE7D,IAAIH,UAAU,EAAE;QACd;QACA,MAAMI,QAAQ,GAAGnD,SAAS,CAAC/B,IAAI,CAACiF,OAAO,CAAC,IAAIlD,SAAS,CAAC/B,IAAI,CAACiF,OAAO,CAAC,CAACE,IAAI,CAAC,CAAC,KAAK,EAAE;QACjF,IAAID,QAAQ,EAAE;UACZ,IAAI,CAACE,MAAM,CAACC,OAAO,CAAC,qBAAqBrF,IAAI,CAACiF,OAAO,uCAAuClD,SAAS,CAAC/B,IAAI,CAACiF,OAAO,CAAC,oBAAoB,CAAC,EAAE;YACxI,OAAOJ,IAAI;UACb;QACF;;QAEA;QACA,MAAMS,WAAW,GAAGT,IAAI,CAACd,MAAM,CAACiB,CAAC,IAAIA,CAAC,CAACC,OAAO,KAAKjF,IAAI,CAACiF,OAAO,CAAC;;QAEhE;QACA,MAAMM,YAAY,GAAG;UAAE,GAAGxD;QAAU,CAAC;QACrC,OAAOwD,YAAY,CAACvF,IAAI,CAACiF,OAAO,CAAC;QACjCjD,YAAY,CAACuD,YAAY,CAAC;;QAE1B;QACAzC,SAAS,CAAC0C,UAAU,IAAI;UACtB,MAAMC,SAAS,GAAG;YAAE,GAAGD;UAAW,CAAC;UACnC,OAAOC,SAAS,CAACzF,IAAI,CAACiF,OAAO,CAAC;UAC9B,OAAOQ,SAAS;QAClB,CAAC,CAAC;QACFzC,WAAW,CAAC0C,YAAY,IAAI;UAC1B,MAAMC,WAAW,GAAG;YAAE,GAAGD;UAAa,CAAC;UACvC,OAAOC,WAAW,CAAC3F,IAAI,CAACiF,OAAO,CAAC;UAChC,OAAOU,WAAW;QACpB,CAAC,CAAC;QAEF,OAAOL,WAAW;MACpB,CAAC,MAAM;QACL;QACA,OAAO,CAAC,GAAGT,IAAI,EAAE7E,IAAI,CAAC;MACxB;IACF,CAAC,CAAC;EACJ,CAAC;;EAED;EACA,MAAM4F,4BAA4B,GAAI5F,IAAI,IAAK;IAC7CwC,wBAAwB,CAAC;MAAExC,IAAI;MAAEC;IAAO,CAAC,CAAC;IAC1CqC,yBAAyB,CAAC,IAAI,CAAC;EACjC,CAAC;;EAED;EACA,MAAMuD,yBAAyB,GAAGA,CAAA,KAAM;IACtC,IAAItD,qBAAqB,EAAE;MACzB,MAAM;QAAEvC;MAAK,CAAC,GAAGuC,qBAAqB;;MAEtC;MACAT,eAAe,CAAC+C,IAAI,IAAI,CAAC,GAAGA,IAAI,EAAE;QAAE,GAAG7E,IAAI;QAAE8F,eAAe,EAAE;MAAK,CAAC,CAAC,CAAC;MAEtExD,yBAAyB,CAAC,KAAK,CAAC;MAChCE,wBAAwB,CAAC,IAAI,CAAC;MAE9B1B,SAAS,aAATA,SAAS,uBAATA,SAAS,CAAG,sBAAsBd,IAAI,CAACiF,OAAO,2DAA2D,CAAC;IAC5G;EACF,CAAC;;EAED;EACA,MAAMc,iBAAiB,GAAGA,CAACC,MAAM,EAAEC,KAAK,KAAK;IAC3C;IACAjE,YAAY,CAAC6C,IAAI,KAAK;MACpB,GAAGA,IAAI;MACP,CAACmB,MAAM,GAAGC;IACZ,CAAC,CAAC,CAAC;;IAEH;IACAC,aAAa,CAACF,MAAM,EAAEC,KAAK,CAAC;EAC9B,CAAC;;EAED;EACA,MAAMC,aAAa,GAAGA,CAACF,MAAM,EAAEC,KAAK,KAAK;IACvC,MAAMjG,IAAI,GAAGuB,OAAO,CAAC4E,IAAI,CAACnB,CAAC,IAAIA,CAAC,CAACC,OAAO,KAAKe,MAAM,CAAC;IACpD,IAAI,CAAChG,IAAI,EAAE;;IAEX;IACA8C,SAAS,CAAC+B,IAAI,IAAI;MAChB,MAAMY,SAAS,GAAG;QAAE,GAAGZ;MAAK,CAAC;MAC7B,OAAOY,SAAS,CAACO,MAAM,CAAC;MACxB,OAAOP,SAAS;IAClB,CAAC,CAAC;IAEFzC,WAAW,CAAC6B,IAAI,IAAI;MAClB,MAAMc,WAAW,GAAG;QAAE,GAAGd;MAAK,CAAC;MAC/B,OAAOc,WAAW,CAACK,MAAM,CAAC;MAC1B,OAAOL,WAAW;IACpB,CAAC,CAAC;;IAEF;IACA,IAAI,CAACM,KAAK,IAAIA,KAAK,CAACd,IAAI,CAAC,CAAC,KAAK,EAAE,EAAE;MACjC,OAAO,IAAI,CAAC,CAAC;IACf;;IAEA;IACA,IAAIiB,KAAK,CAACnC,UAAU,CAACgC,KAAK,CAAC,CAAC,IAAIhC,UAAU,CAACgC,KAAK,CAAC,IAAI,CAAC,EAAE;MACtDnD,SAAS,CAAC+B,IAAI,KAAK;QACjB,GAAGA,IAAI;QACP,CAACmB,MAAM,GAAG;MACZ,CAAC,CAAC,CAAC;MACH,OAAO,KAAK;IACd;IAEA,MAAMK,WAAW,GAAGpC,UAAU,CAACgC,KAAK,CAAC;;IAErC;IACA,IAAIjG,IAAI,CAACoE,aAAa,IAAIiC,WAAW,GAAGpC,UAAU,CAACjE,IAAI,CAACoE,aAAa,CAAC,EAAE;MACtEpB,WAAW,CAAC6B,IAAI,KAAK;QACnB,GAAGA,IAAI;QACP,CAACmB,MAAM,GAAG,mBAAmBK,WAAW,yCAAyCrG,IAAI,CAACoE,aAAa;MACrG,CAAC,CAAC,CAAC;IACL;;IAEA;IACA,MAAMkC,oBAAoB,GAAGC,MAAM,CAACC,OAAO,CAACzE,SAAS,CAAC,CACnDgC,MAAM,CAAC,CAAC,CAAC0C,EAAE,EAAEC,CAAC,CAAC,KAAKD,EAAE,KAAKT,MAAM,CAAC,CAAC;IAAA,CACnCW,MAAM,CAAC,CAACC,GAAG,EAAE,CAACF,CAAC,EAAEG,KAAK,CAAC,KAAKD,GAAG,GAAG3C,UAAU,CAAC4C,KAAK,IAAI,CAAC,CAAC,EAAE,CAAC,CAAC,GAAGR,WAAW;IAE7E,MAAMS,kBAAkB,GAAG,CAAA7G,MAAM,aAANA,MAAM,uBAANA,MAAM,CAAE8G,aAAa,KAAI,CAAC;IACrD,IAAIT,oBAAoB,GAAGQ,kBAAkB,EAAE;MAC7C9D,WAAW,CAAC6B,IAAI,KAAK;QACnB,GAAGA,IAAI;QACP,CAACmB,MAAM,GAAG,qBAAqBM,oBAAoB,CAACU,OAAO,CAAC,CAAC,CAAC,uBAAuBF,kBAAkB,CAACE,OAAO,CAAC,CAAC,CAAC;MACpH,CAAC,CAAC,CAAC;IACL;IAEA,OAAO,IAAI;EACb,CAAC;;EAED;EACA,MAAMC,gBAAgB,GAAGA,CAAA,KAAM;IAC7B,IAAIC,OAAO,GAAG,IAAI;IAClB,MAAMzB,SAAS,GAAG,CAAC,CAAC;IACpB,MAAME,WAAW,GAAG,CAAC,CAAC;;IAEtB;IACA,IAAI9D,YAAY,CAACpB,MAAM,KAAK,CAAC,EAAE;MAC7BM,OAAO,CAAC,0BAA0B,CAAC;MACnC,OAAO,KAAK;IACd;;IAEA;IACA,KAAK,MAAMf,IAAI,IAAI6B,YAAY,EAAE;MAC/B,MAAMgF,KAAK,GAAG9E,SAAS,CAAC/B,IAAI,CAACiF,OAAO,CAAC;;MAErC;MACA,IAAI,CAAC4B,KAAK,IAAIA,KAAK,CAAC1B,IAAI,CAAC,CAAC,KAAK,EAAE,EAAE;QACjCM,SAAS,CAACzF,IAAI,CAACiF,OAAO,CAAC,GAAG,uCAAuC;QACjEiC,OAAO,GAAG,KAAK;QACf;MACF;;MAEA;MACA,IAAId,KAAK,CAACnC,UAAU,CAAC4C,KAAK,CAAC,CAAC,IAAI5C,UAAU,CAAC4C,KAAK,CAAC,IAAI,CAAC,EAAE;QACtDpB,SAAS,CAACzF,IAAI,CAACiF,OAAO,CAAC,GAAG,sCAAsC;QAChEiC,OAAO,GAAG,KAAK;QACf;MACF;MAEA,MAAMb,WAAW,GAAGpC,UAAU,CAAC4C,KAAK,CAAC;;MAErC;MACA,IAAI7G,IAAI,CAACoE,aAAa,IAAIiC,WAAW,GAAGpC,UAAU,CAACjE,IAAI,CAACoE,aAAa,CAAC,EAAE;QACtEuB,WAAW,CAAC3F,IAAI,CAACiF,OAAO,CAAC,GAAG,mBAAmBoB,WAAW,yCAAyCrG,IAAI,CAACoE,aAAa,IAAI;MAC3H;IACF;IAEAtB,SAAS,CAAC2C,SAAS,CAAC;IACpBzC,WAAW,CAAC2C,WAAW,CAAC;;IAExB;IACA,IAAI,CAACuB,OAAO,EAAE;MACZ,OAAO,KAAK;IACd;;IAEA;IACA,MAAMZ,oBAAoB,GAAGC,MAAM,CAACY,MAAM,CAACpF,SAAS,CAAC,CAAC4E,MAAM,CAAC,CAACC,GAAG,EAAEC,KAAK,KAAKD,GAAG,GAAG3C,UAAU,CAAC4C,KAAK,IAAI,CAAC,CAAC,EAAE,CAAC,CAAC;IAC7G,MAAMC,kBAAkB,GAAG,CAAA7G,MAAM,aAANA,MAAM,uBAANA,MAAM,CAAE8G,aAAa,KAAI,CAAC;IACrD,IAAIT,oBAAoB,GAAGQ,kBAAkB,EAAE;MAC7C;MACAlE,iBAAiB,CAAC;QAChB0D,oBAAoB;QACpBQ,kBAAkB;QAClBjF,YAAY,EAAEA,YAAY,CAACpB;MAC7B,CAAC,CAAC;MACFiC,iBAAiB,CAAC,IAAI,CAAC;MACvB,OAAO,KAAK,CAAC,CAAC;IAChB;IAEA,OAAO,IAAI;EACb,CAAC;;EAED;EACA,MAAM0E,UAAU,GAAG,MAAAA,CAAOC,SAAS,GAAG,KAAK,KAAK;IAC9C,IAAI;MACF;MACA,IAAI,CAACA,SAAS,IAAI,CAACJ,gBAAgB,CAAC,CAAC,EAAE;QACrC;MACF;MAEA3F,SAAS,CAAC,IAAI,CAAC;;MAEf;MACA,MAAMgG,OAAO,GAAG,EAAE;MAClB,IAAIzE,MAAM,GAAG,EAAE;MAEf,KAAK,MAAM7C,IAAI,IAAI6B,YAAY,EAAE;QAC/B,IAAI;UACF,MAAMwE,WAAW,GAAGpC,UAAU,CAAClC,SAAS,CAAC/B,IAAI,CAACiF,OAAO,CAAC,CAAC;;UAEvD;UACA,MAAMsC,kBAAkB,GAAGD,OAAO,CAACX,MAAM,CAAC,CAACC,GAAG,EAAEY,CAAC,KAAKZ,GAAG,GAAGY,CAAC,CAACnB,WAAW,EAAE,CAAC,CAAC;UAC7E,MAAMS,kBAAkB,GAAG,CAAA7G,MAAM,aAANA,MAAM,uBAANA,MAAM,CAAE8G,aAAa,KAAI,CAAC;UACrD,MAAMU,cAAc,GAAGJ,SAAS,IAAKE,kBAAkB,GAAGlB,WAAW,GAAIS,kBAAkB,IAAI9G,IAAI,CAAC8F,eAAe;;UAEnH;UACA,MAAM4B,MAAM,GAAG,MAAMlI,WAAW,CAACmI,iBAAiB,CAChD9G,UAAU,EACVb,IAAI,CAACiF,OAAO,EACZoB,WAAW,EACXpG,MAAM,CAAC2H,SAAS,EAChBH,cACF,CAAC;UAEDH,OAAO,CAACO,IAAI,CAAC;YACX7H,IAAI,EAAEA,IAAI,CAACiF,OAAO;YAClBoB,WAAW;YACXyB,OAAO,EAAE,IAAI;YACbC,eAAe,EAAE/H,IAAI,CAAC8F;UACxB,CAAC,CAAC;QACJ,CAAC,CAAC,OAAOpB,KAAK,EAAE;UACdF,OAAO,CAACE,KAAK,CAAC,sCAAsC1E,IAAI,CAACiF,OAAO,GAAG,EAAEP,KAAK,CAAC;UAC3E7B,MAAM,CAACgF,IAAI,CAAC;YACV7H,IAAI,EAAEA,IAAI,CAACiF,OAAO;YAClBP,KAAK,EAAEA,KAAK,CAACC,OAAO,IAAI;UAC1B,CAAC,CAAC;QACJ;MACF;;MAEA;MACA,IAAI9B,MAAM,CAACpC,MAAM,KAAK,CAAC,EAAE;QACvB,MAAMuH,iBAAiB,GAAGV,OAAO,CAACvD,MAAM,CAACyD,CAAC,IAAIA,CAAC,CAACO,eAAe,CAAC,CAACtH,MAAM;QACvE,IAAIkE,OAAO,GAAG,GAAG2C,OAAO,CAAC7G,MAAM,+BAA+B;QAC9D,IAAIuH,iBAAiB,GAAG,CAAC,EAAE;UACzBrD,OAAO,IAAI,KAAKqD,iBAAiB,gCAAgC;QACnE;QACAlH,SAAS,CAAC6D,OAAO,CAAC;QAClB/D,OAAO,CAAC,CAAC;MACX,CAAC,MAAM,IAAI0G,OAAO,CAAC7G,MAAM,GAAG,CAAC,EAAE;QAC7BK,SAAS,CAAC,GAAGwG,OAAO,CAAC7G,MAAM,kCAAkCoC,MAAM,CAACpC,MAAM,UAAU,CAAC;QACrFM,OAAO,CAAC,WAAW8B,MAAM,CAACoF,GAAG,CAACC,CAAC,IAAI,GAAGA,CAAC,CAAClI,IAAI,KAAKkI,CAAC,CAACxD,KAAK,EAAE,CAAC,CAACyD,IAAI,CAAC,IAAI,CAAC,EAAE,CAAC;QACzEvH,OAAO,CAAC,CAAC;MACX,CAAC,MAAM;QACLG,OAAO,CAAC,mCAAmC8B,MAAM,CAACoF,GAAG,CAACC,CAAC,IAAI,GAAGA,CAAC,CAAClI,IAAI,KAAKkI,CAAC,CAACxD,KAAK,EAAE,CAAC,CAACyD,IAAI,CAAC,IAAI,CAAC,EAAE,CAAC;MACnG;IACF,CAAC,CAAC,OAAOzD,KAAK,EAAE;MACdF,OAAO,CAACE,KAAK,CAAC,gCAAgC,EAAEA,KAAK,CAAC;MACtD3D,OAAO,CAAC,iCAAiC,IAAI2D,KAAK,CAACC,OAAO,IAAI,oBAAoB,CAAC,CAAC;IACtF,CAAC,SAAS;MACRrD,SAAS,CAAC,KAAK,CAAC;IAClB;EACF,CAAC;;EAED;EACA,MAAM8G,uBAAuB,GAAGA,CAAA,KAAM;IACpC1F,iBAAiB,CAAC,KAAK,CAAC;IACxBE,iBAAiB,CAAC,IAAI,CAAC;IACvBwE,UAAU,CAAC,IAAI,CAAC,CAAC,CAAC;EACpB,CAAC;;EAED;EACA,MAAMiB,UAAU,GAAIC,QAAQ,IAAK;IAC/B,IAAI,CAACnG,UAAU,EAAE,OAAOmG,QAAQ;IAEhC,OAAOA,QAAQ,CAACvE,MAAM,CAAC/D,IAAI,IACzBA,IAAI,CAACiF,OAAO,CAACsD,WAAW,CAAC,CAAC,CAACC,QAAQ,CAACrG,UAAU,CAACoG,WAAW,CAAC,CAAC,CAAC,IAC5DvI,IAAI,CAACE,SAAS,IAAIF,IAAI,CAACE,SAAS,CAACqI,WAAW,CAAC,CAAC,CAACC,QAAQ,CAACrG,UAAU,CAACoG,WAAW,CAAC,CAAC,CAAE,IAClFvI,IAAI,CAACyI,mBAAmB,IAAIzI,IAAI,CAACyI,mBAAmB,CAACF,WAAW,CAAC,CAAC,CAACC,QAAQ,CAACrG,UAAU,CAACoG,WAAW,CAAC,CAAC,CAAE,IACtGvI,IAAI,CAAC0I,iBAAiB,IAAI1I,IAAI,CAAC0I,iBAAiB,CAACH,WAAW,CAAC,CAAC,CAACC,QAAQ,CAACrG,UAAU,CAACoG,WAAW,CAAC,CAAC,CACnG,CAAC;EACH,CAAC;EAED,MAAMI,mBAAmB,GAAGN,UAAU,CAAC5G,eAAe,CAAC;EACvD,MAAMmH,qBAAqB,GAAGP,UAAU,CAAC1G,iBAAiB,CAAC;;EAE3D;EACA,MAAM2E,oBAAoB,GAAGC,MAAM,CAACY,MAAM,CAACpF,SAAS,CAAC,CAAC4E,MAAM,CAAC,CAACC,GAAG,EAAEC,KAAK,KAAK;IAC3E,MAAMZ,KAAK,GAAGhC,UAAU,CAAC4C,KAAK,IAAI,CAAC,CAAC;IACpC,OAAOT,KAAK,CAACH,KAAK,CAAC,GAAGW,GAAG,GAAGA,GAAG,GAAGX,KAAK;EACzC,CAAC,EAAE,CAAC,CAAC;EAEL,MAAMa,kBAAkB,GAAG,CAAA7G,MAAM,aAANA,MAAM,uBAANA,MAAM,CAAE8G,aAAa,KAAI,CAAC;EACrD,MAAM8B,WAAW,GAAGvC,oBAAoB,GAAGQ,kBAAkB;;EAE7D;EACA,MAAMgC,cAAc,GAAGA,CAACR,QAAQ,EAAEvI,YAAY,GAAG,IAAI,KAAK;IACxD,IAAIoB,WAAW,EAAE;MACf,oBACEvB,OAAA,CAACnC,GAAG;QAACsL,EAAE,EAAE;UAAEC,OAAO,EAAE,MAAM;UAAEC,cAAc,EAAE,QAAQ;UAAEC,EAAE,EAAE;QAAE,CAAE;QAAA7F,QAAA,eAC5DzD,OAAA,CAACjC,gBAAgB;UAAA2F,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACjB,CAAC;IAEV;IAEA,IAAI6E,QAAQ,CAAC7H,MAAM,KAAK,CAAC,EAAE;MACzB,oBACEb,OAAA,CAAChC,KAAK;QAAC8F,QAAQ,EAAC,MAAM;QAACqF,EAAE,EAAE;UAAEG,EAAE,EAAE;QAAE,CAAE;QAAA7F,QAAA,EAClCtD,YAAY,GAAG,sCAAsC,GAAG;MAAoC;QAAAuD,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACxF,CAAC;IAEZ;IAEA,oBACE7D,OAAA,CAAC3B,IAAI;MAACkL,KAAK;MAAA9F,QAAA,EACRiF,QAAQ,CAACL,GAAG,CAAEjI,IAAI,IAAK;QACtB,MAAM8E,UAAU,GAAGjD,YAAY,CAACkD,IAAI,CAACC,CAAC,IAAIA,CAAC,CAACC,OAAO,KAAKjF,IAAI,CAACiF,OAAO,CAAC;QACrE,MAAMC,QAAQ,GAAGnD,SAAS,CAAC/B,IAAI,CAACiF,OAAO,CAAC,IAAIlD,SAAS,CAAC/B,IAAI,CAACiF,OAAO,CAAC,CAACE,IAAI,CAAC,CAAC,KAAK,EAAE;QACjF,MAAMiE,QAAQ,GAAGvG,MAAM,CAAC7C,IAAI,CAACiF,OAAO,CAAC;QACrC,MAAMoE,UAAU,GAAGtG,QAAQ,CAAC/C,IAAI,CAACiF,OAAO,CAAC;QAEzC,oBACErF,OAAA,CAAC1B,QAAQ;UAEP6K,EAAE,EAAE;YACFO,MAAM,EAAE,mBAAmB;YAC3BC,YAAY,EAAE,CAAC;YACfC,EAAE,EAAE,CAAC;YACLC,OAAO,EAAE3E,UAAU,GAAG,yBAAyB,GAAG,SAAS;YAC3D,SAAS,EAAE;cACT2E,OAAO,EAAE3E,UAAU,GAAG,yBAAyB,GAAG;YACpD;UACF,CAAE;UAAAzB,QAAA,gBAEFzD,OAAA,CAACzB,YAAY;YACXuL,OAAO,eACL9J,OAAA,CAACnC,GAAG;cAACsL,EAAE,EAAE;gBAAEC,OAAO,EAAE,MAAM;gBAAEW,UAAU,EAAE,QAAQ;gBAAEC,GAAG,EAAE;cAAE,CAAE;cAAAvG,QAAA,gBACzDzD,OAAA,CAACP,SAAS;gBAACwK,QAAQ,EAAC,OAAO;gBAACC,KAAK,EAAE/J,YAAY,GAAG,SAAS,GAAG;cAAU;gBAAAuD,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,eAC3E7D,OAAA,CAACpC,UAAU;gBAACuM,OAAO,EAAC,WAAW;gBAAA1G,QAAA,EAAErD,IAAI,CAACiF;cAAO;gBAAA3B,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAa,CAAC,eAC3D7D,OAAA,CAAC/B,IAAI;gBACHmM,IAAI,EAAC,OAAO;gBACZC,KAAK,EAAEjK,IAAI,CAACE,SAAS,IAAI,KAAM;gBAC/B4J,KAAK,EAAE/J,YAAY,GAAG,SAAS,GAAG,SAAU;gBAC5CgK,OAAO,EAAC;cAAU;gBAAAzG,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACnB,CAAC,EACD,CAAC1D,YAAY,iBACZH,OAAA,CAAC/B,IAAI;gBACHmM,IAAI,EAAC,OAAO;gBACZC,KAAK,EAAC,eAAe;gBACrBH,KAAK,EAAC,OAAO;gBACbC,OAAO,EAAC;cAAQ;gBAAAzG,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACjB,CACF;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACE,CACN;YACDyG,SAAS,eACPtK,OAAA,CAACnC,GAAG;cAAA4F,QAAA,gBACFzD,OAAA,CAACpC,UAAU;gBAACuM,OAAO,EAAC,OAAO;gBAACD,KAAK,EAAC,gBAAgB;gBAAAzG,QAAA,GAC/CrD,IAAI,CAACyI,mBAAmB,IAAI,KAAK,EAAC,UAAG,EAACzI,IAAI,CAAC0I,iBAAiB,IAAI,KAAK;cAAA;gBAAApF,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC5D,CAAC,eACb7D,OAAA,CAACpC,UAAU;gBAACuM,OAAO,EAAC,OAAO;gBAACD,KAAK,EAAC,gBAAgB;gBAAAzG,QAAA,GAAC,cACrC,EAACrD,IAAI,CAACI,OAAO,IAAI,KAAK,EAAC,oBAAkB,EAACJ,IAAI,CAACoE,aAAa,IAAI,KAAK;cAAA;gBAAAd,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACvE,CAAC,EACZqB,UAAU,iBACTlF,OAAA,CAACnC,GAAG;gBAACsL,EAAE,EAAE;kBAAEoB,EAAE,EAAE;gBAAE,CAAE;gBAAA9G,QAAA,eACjBzD,OAAA,CAAClC,SAAS;kBACRsM,IAAI,EAAC,OAAO;kBACZI,IAAI,EAAC,QAAQ;kBACbH,KAAK,EAAC,cAAc;kBACpBhE,KAAK,EAAElE,SAAS,CAAC/B,IAAI,CAACiF,OAAO,CAAC,IAAI,EAAG;kBACrCoF,QAAQ,EAAGnC,CAAC,IAAKnC,iBAAiB,CAAC/F,IAAI,CAACiF,OAAO,EAAEiD,CAAC,CAACoC,MAAM,CAACrE,KAAK,CAAE;kBACjEvB,KAAK,EAAE,CAAC,CAAC0E,QAAS;kBAClBmB,UAAU,EAAEnB,QAAQ,IAAIC,UAAW;kBACnCmB,mBAAmB,EAAE;oBACnBzB,EAAE,EAAE;sBAAEe,KAAK,EAAET,UAAU,IAAI,CAACD,QAAQ,GAAG,cAAc,GAAG;oBAAa;kBACvE,CAAE;kBACFL,EAAE,EAAE;oBAAE0B,KAAK,EAAE;kBAAQ;gBAAE;kBAAAnH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACxB;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACC,CACN;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACE;UACN;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACF,CAAC,eACF7D,OAAA,CAACxB,uBAAuB;YAAAiF,QAAA,eACtBzD,OAAA,CAACrC,MAAM;cACLyM,IAAI,EAAC,OAAO;cACZD,OAAO,EAAEjF,UAAU,GAAG,WAAW,GAAG,UAAW;cAC/CgF,KAAK,EAAE/J,YAAY,GAAG,SAAS,GAAG,SAAU;cAC5C4D,OAAO,EAAEA,CAAA,KAAM5D,YAAY,GAAG6E,0BAA0B,CAAC5E,IAAI,CAAC,GAAG4F,4BAA4B,CAAC5F,IAAI,CAAE;cACpG0K,SAAS,EAAE5F,UAAU,gBAAGlF,OAAA,CAACL,eAAe;gBAAA+D,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,gBAAG7D,OAAA,CAACjB,OAAO;gBAAA2E,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAE;cAAAJ,QAAA,EAEzDyB,UAAU,GAAG,aAAa,GAAG;YAAW;cAAAxB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACnC;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACc,CAAC;QAAA,GAtErBzD,IAAI,CAACiF,OAAO;UAAA3B,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAuET,CAAC;MAEf,CAAC;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACE,CAAC;EAEX,CAAC;EAED,oBACE7D,OAAA,CAACzC,MAAM;IAACwD,IAAI,EAAEA,IAAK;IAACC,OAAO,EAAEA,OAAQ;IAACuC,QAAQ,EAAC,IAAI;IAACC,SAAS;IAAAC,QAAA,gBAC3DzD,OAAA,CAACxC,WAAW;MAAAiG,QAAA,eACVzD,OAAA,CAACnC,GAAG;QAACsL,EAAE,EAAE;UAAEC,OAAO,EAAE,MAAM;UAAEW,UAAU,EAAE,QAAQ;UAAEC,GAAG,EAAE;QAAE,CAAE;QAAAvG,QAAA,gBACzDzD,OAAA,CAACP,SAAS;UAAAiE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,eACb7D,OAAA,CAACpC,UAAU;UAACuM,OAAO,EAAC,IAAI;UAAA1G,QAAA,GAAC,4BACG,EAAChD,eAAe,CAACJ,MAAM,aAANA,MAAM,uBAANA,MAAM,CAAE2H,SAAS,CAAC;QAAA;UAAAtE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACnD,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACV;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACK,CAAC,eACd7D,OAAA,CAACvC,aAAa;MAAAgG,QAAA,gBAERzD,OAAA,CAACtB,IAAI;QAACyK,EAAE,EAAE;UAAES,EAAE,EAAE;QAAE,CAAE;QAAAnG,QAAA,gBAClBzD,OAAA,CAACpB,UAAU;UACTmM,KAAK,EAAC,iBAAiB;UACvBC,oBAAoB,EAAE;YAAEb,OAAO,EAAE;UAAY;QAAE;UAAAzG,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAChD,CAAC,eACF7D,OAAA,CAACrB,WAAW;UAACwK,EAAE,EAAE;YAAE8B,EAAE,EAAE;UAAE,CAAE;UAAAxH,QAAA,eACzBzD,OAAA,CAACnC,GAAG;YAACsL,EAAE,EAAE;cAAEC,OAAO,EAAE,MAAM;cAAE8B,QAAQ,EAAE,MAAM;cAAElB,GAAG,EAAE;YAAE,CAAE;YAAAvG,QAAA,gBACrDzD,OAAA,CAACnC,GAAG;cAAA4F,QAAA,gBACFzD,OAAA,CAACpC,UAAU;gBAACuM,OAAO,EAAC,OAAO;gBAACD,KAAK,EAAC,gBAAgB;gBAAAzG,QAAA,EAAC;cAAS;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAY,CAAC,eACzE7D,OAAA,CAACpC,UAAU;gBAACuM,OAAO,EAAC,OAAO;gBAAA1G,QAAA,EAAEpD,MAAM,CAAC2H;cAAS;gBAAAtE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAa,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACxD,CAAC,eACN7D,OAAA,CAACnC,GAAG;cAAA4F,QAAA,gBACFzD,OAAA,CAACpC,UAAU;gBAACuM,OAAO,EAAC,OAAO;gBAACD,KAAK,EAAC,gBAAgB;gBAAAzG,QAAA,EAAC;cAAS;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAY,CAAC,eACzE7D,OAAA,CAACpC,UAAU;gBAACuM,OAAO,EAAC,OAAO;gBAAA1G,QAAA,EAAEpD,MAAM,CAACC,SAAS,IAAI;cAAK;gBAAAoD,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAa,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACjE,CAAC,eACN7D,OAAA,CAACnC,GAAG;cAAA4F,QAAA,gBACFzD,OAAA,CAACpC,UAAU;gBAACuM,OAAO,EAAC,OAAO;gBAACD,KAAK,EAAC,gBAAgB;gBAAAzG,QAAA,EAAC;cAAU;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAY,CAAC,eAC1E7D,OAAA,CAACpC,UAAU;gBAACuM,OAAO,EAAC,OAAO;gBAAA1G,QAAA,EAAEpD,MAAM,CAACG,OAAO,IAAI;cAAK;gBAAAkD,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAa,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC/D,CAAC,eACN7D,OAAA,CAACnC,GAAG;cAAA4F,QAAA,gBACFzD,OAAA,CAACpC,UAAU;gBAACuM,OAAO,EAAC,OAAO;gBAACD,KAAK,EAAC,gBAAgB;gBAAAzG,QAAA,EAAC;cAAa;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAY,CAAC,eAC7E7D,OAAA,CAACpC,UAAU;gBACTuM,OAAO,EAAC,OAAO;gBACfD,KAAK,EAAEjB,WAAW,GAAG,YAAY,GAAG,cAAe;gBACnDE,EAAE,EAAE;kBAAEgC,UAAU,EAAElC,WAAW,GAAG,MAAM,GAAG;gBAAS,CAAE;gBAAAxF,QAAA,GAEnDyD,kBAAkB,CAACE,OAAO,CAAC,CAAC,CAAC,EAAC,IACjC;cAAA;gBAAA1D,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAY,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACV,CAAC,eACN7D,OAAA,CAACnC,GAAG;cAAA4F,QAAA,gBACFzD,OAAA,CAACpC,UAAU;gBAACuM,OAAO,EAAC,OAAO;gBAACD,KAAK,EAAC,gBAAgB;gBAAAzG,QAAA,EAAC;cAAK;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAY,CAAC,eACrE7D,OAAA,CAAC/B,IAAI;gBACHoM,KAAK,EAAEhK,MAAM,CAAC+K,YAAY,IAAI,KAAM;gBACpChB,IAAI,EAAC,OAAO;gBACZF,KAAK,EACH7J,MAAM,CAAC+K,YAAY,KAAK,aAAa,GAAG,SAAS,GACjD/K,MAAM,CAAC+K,YAAY,KAAK,QAAQ,GAAG,SAAS,GAC5C/K,MAAM,CAAC+K,YAAY,KAAK,MAAM,GAAG,OAAO,GACxC/K,MAAM,CAAC+K,YAAY,KAAK,WAAW,GAAG,SAAS,GAAG;cACnD;gBAAA1H,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACF,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACC,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACK,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACV,CAAC,eAGP7D,OAAA,CAAClC,SAAS;QACR0F,SAAS;QACT6G,KAAK,EAAC,2BAA2B;QACjCF,OAAO,EAAC,UAAU;QAClB9D,KAAK,EAAE9D,UAAW;QAClBkI,QAAQ,EAAGnC,CAAC,IAAK9F,aAAa,CAAC8F,CAAC,CAACoC,MAAM,CAACrE,KAAK,CAAE;QAC/CgF,WAAW,EAAC,wCAAwC;QACpDC,UAAU,EAAE;UACVC,cAAc,eAAEvL,OAAA,CAACT,UAAU;YAAC4J,EAAE,EAAE;cAAEqC,EAAE,EAAE,CAAC;cAAEtB,KAAK,EAAE;YAAiB;UAAE;YAAAxG,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QACvE,CAAE;QACFsF,EAAE,EAAE;UAAES,EAAE,EAAE;QAAE;MAAE;QAAAlG,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACf,CAAC,eAGF7D,OAAA,CAACnC,GAAG;QAACsL,EAAE,EAAE;UAAEsC,YAAY,EAAE,CAAC;UAAEC,WAAW,EAAE,SAAS;UAAE9B,EAAE,EAAE;QAAE,CAAE;QAAAnG,QAAA,eAC1DzD,OAAA,CAAC7B,IAAI;UAACkI,KAAK,EAAEhE,SAAU;UAACoI,QAAQ,EAAEA,CAACnC,CAAC,EAAEqD,QAAQ,KAAKrJ,YAAY,CAACqJ,QAAQ,CAAE;UAAAlI,QAAA,gBACxEzD,OAAA,CAAC5B,GAAG;YACFiM,KAAK,eACHrK,OAAA,CAACnB,KAAK;cAAC+M,YAAY,EAAE7C,mBAAmB,CAAClI,MAAO;cAACqJ,KAAK,EAAC,SAAS;cAAAzG,QAAA,eAC9DzD,OAAA,CAACnC,GAAG;gBAACsL,EAAE,EAAE;kBAAEC,OAAO,EAAE,MAAM;kBAAEW,UAAU,EAAE,QAAQ;kBAAEC,GAAG,EAAE;gBAAE,CAAE;gBAAAvG,QAAA,gBACzDzD,OAAA,CAACL,eAAe;kBAACsK,QAAQ,EAAC;gBAAO;kBAAAvG,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAC,oBAEtC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAK;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACD;UACR;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACF,CAAC,eACF7D,OAAA,CAAC5B,GAAG;YACFiM,KAAK,eACHrK,OAAA,CAACnB,KAAK;cAAC+M,YAAY,EAAE5C,qBAAqB,CAACnI,MAAO;cAACqJ,KAAK,EAAC,SAAS;cAAAzG,QAAA,eAChEzD,OAAA,CAACnC,GAAG;gBAACsL,EAAE,EAAE;kBAAEC,OAAO,EAAE,MAAM;kBAAEW,UAAU,EAAE,QAAQ;kBAAEC,GAAG,EAAE;gBAAE,CAAE;gBAAAvG,QAAA,gBACzDzD,OAAA,CAACX,WAAW;kBAAC4K,QAAQ,EAAC;gBAAO;kBAAAvG,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAC,sBAElC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAK;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACD;UACR;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACF,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACE;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACJ,CAAC,eAGN7D,OAAA,CAACnC,GAAG;QAACsL,EAAE,EAAE;UAAE0C,SAAS,EAAE,GAAG;UAAEC,SAAS,EAAE,GAAG;UAAEC,QAAQ,EAAE;QAAO,CAAE;QAAAtI,QAAA,GAC3DpB,SAAS,KAAK,CAAC,iBACdrC,OAAA,CAACnC,GAAG;UAAA4F,QAAA,gBACFzD,OAAA,CAACpC,UAAU;YAACuM,OAAO,EAAC,OAAO;YAACD,KAAK,EAAC,gBAAgB;YAACf,EAAE,EAAE;cAAES,EAAE,EAAE;YAAE,CAAE;YAAAnG,QAAA,GAAC,iCACjC,eAAAzD,OAAA;cAAAyD,QAAA,EAASpD,MAAM,CAACC;YAAS;cAAAoD,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAS,CAAC,kBAAc,eAAA7D,OAAA;cAAAyD,QAAA,EAASpD,MAAM,CAACG;YAAO;cAAAkD,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAS,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACvG,CAAC,EACZqF,cAAc,CAACH,mBAAmB,EAAE,IAAI,CAAC;QAAA;UAAArF,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACvC,CACN,EACAxB,SAAS,KAAK,CAAC,iBACdrC,OAAA,CAACnC,GAAG;UAAA4F,QAAA,gBACFzD,OAAA,CAAChC,KAAK;YAAC8F,QAAQ,EAAC,SAAS;YAACqF,EAAE,EAAE;cAAES,EAAE,EAAE;YAAE,CAAE;YAAAnG,QAAA,eACtCzD,OAAA,CAACpC,UAAU;cAACuM,OAAO,EAAC,OAAO;cAAA1G,QAAA,GAAC,qDACyB,eAAAzD,OAAA;gBAAAyD,QAAA,EAAQ;cAAU;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC,uEAEhF;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAY;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACR,CAAC,EACPqF,cAAc,CAACF,qBAAqB,EAAE,KAAK,CAAC;QAAA;UAAAtF,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC1C,CACN;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACE,CAAC,EAGL5B,YAAY,CAACpB,MAAM,GAAG,CAAC,iBACtBb,OAAA,CAACtB,IAAI;QAACyK,EAAE,EAAE;UAAEoB,EAAE,EAAE,CAAC;UAAEX,EAAE,EAAE;QAAE,CAAE;QAAAnG,QAAA,gBACzBzD,OAAA,CAACpB,UAAU;UACTmM,KAAK,EAAE,wBAAwB9I,YAAY,CAACpB,MAAM,QAAS;UAC3DmK,oBAAoB,EAAE;YAAEb,OAAO,EAAE;UAAY;QAAE;UAAAzG,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAChD,CAAC,eACF7D,OAAA,CAACrB,WAAW;UAACwK,EAAE,EAAE;YAAE8B,EAAE,EAAE;UAAE,CAAE;UAAAxH,QAAA,gBACzBzD,OAAA,CAACnC,GAAG;YAACsL,EAAE,EAAE;cAAES,EAAE,EAAE;YAAE,CAAE;YAAAnG,QAAA,gBACjBzD,OAAA,CAACnC,GAAG;cAACsL,EAAE,EAAE;gBAAEC,OAAO,EAAE,MAAM;gBAAEC,cAAc,EAAE,eAAe;gBAAEO,EAAE,EAAE;cAAE,CAAE;cAAAnG,QAAA,gBACnEzD,OAAA,CAACpC,UAAU;gBAACuM,OAAO,EAAC,OAAO;gBAAA1G,QAAA,EAAC;cAAuB;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAY,CAAC,eAChE7D,OAAA,CAACpC,UAAU;gBACTuM,OAAO,EAAC,OAAO;gBACfhB,EAAE,EAAE;kBACFgC,UAAU,EAAE,MAAM;kBAClBjB,KAAK,EAAEjB,WAAW,GAAG,YAAY,GAAG;gBACtC,CAAE;gBAAAxF,QAAA,GAEDiD,oBAAoB,CAACU,OAAO,CAAC,CAAC,CAAC,EAAC,IACnC;cAAA;gBAAA1D,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAY,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACV,CAAC,eACN7D,OAAA,CAACnC,GAAG;cAACsL,EAAE,EAAE;gBAAEC,OAAO,EAAE,MAAM;gBAAEC,cAAc,EAAE,eAAe;gBAAEO,EAAE,EAAE;cAAE,CAAE;cAAAnG,QAAA,gBACnEzD,OAAA,CAACpC,UAAU;gBAACuM,OAAO,EAAC,OAAO;gBAAA1G,QAAA,EAAC;cAAqB;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAY,CAAC,eAC9D7D,OAAA,CAACpC,UAAU;gBAACuM,OAAO,EAAC,OAAO;gBAAChB,EAAE,EAAE;kBAAEgC,UAAU,EAAE;gBAAO,CAAE;gBAAA1H,QAAA,GACpDyD,kBAAkB,CAACE,OAAO,CAAC,CAAC,CAAC,EAAC,IACjC;cAAA;gBAAA1D,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAY,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACV,CAAC,eACN7D,OAAA,CAACvB,OAAO;cAAC0K,EAAE,EAAE;gBAAEG,EAAE,EAAE;cAAE;YAAE;cAAA5F,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,eAC1B7D,OAAA,CAACnC,GAAG;cAACsL,EAAE,EAAE;gBAAEC,OAAO,EAAE,MAAM;gBAAEC,cAAc,EAAE;cAAgB,CAAE;cAAA5F,QAAA,gBAC5DzD,OAAA,CAACpC,UAAU;gBAACuM,OAAO,EAAC,OAAO;gBAAA1G,QAAA,EAAC;cAAW;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAY,CAAC,eACpD7D,OAAA,CAACpC,UAAU;gBACTuM,OAAO,EAAC,OAAO;gBACfhB,EAAE,EAAE;kBACFgC,UAAU,EAAE,MAAM;kBAClBjB,KAAK,EAAEjB,WAAW,GAAG,YAAY,GAAG;gBACtC,CAAE;gBAAAxF,QAAA,GAED,CAACyD,kBAAkB,GAAGR,oBAAoB,EAAEU,OAAO,CAAC,CAAC,CAAC,EAAC,IACxD,EAAC6B,WAAW,IAAI,SAAS;cAAA;gBAAAvF,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACf,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACV,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC,EAELoF,WAAW,iBACVjJ,OAAA,CAAChC,KAAK;YAAC8F,QAAQ,EAAC,SAAS;YAACqF,EAAE,EAAE;cAAES,EAAE,EAAE;YAAE,CAAE;YAAAnG,QAAA,eACtCzD,OAAA,CAACpC,UAAU;cAACuM,OAAO,EAAC,OAAO;cAAA1G,QAAA,gBACzBzD,OAAA;gBAAAyD,QAAA,EAAQ;cAAW;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC,+FAE9B;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAY;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACR,CACR,eAED7D,OAAA,CAAC3B,IAAI;YAACkL,KAAK;YAAA9F,QAAA,EACRxB,YAAY,CAACoG,GAAG,CAAEjI,IAAI,iBACrBJ,OAAA,CAAC1B,QAAQ;cAEP6K,EAAE,EAAE;gBACFO,MAAM,EAAE,mBAAmB;gBAC3BC,YAAY,EAAE,CAAC;gBACfC,EAAE,EAAE,CAAC;gBACLC,OAAO,EAAEzJ,IAAI,CAAC8F,eAAe,GAAG,eAAe,GAAG;cACpD,CAAE;cAAAzC,QAAA,gBAEFzD,OAAA,CAACzB,YAAY;gBACXuL,OAAO,eACL9J,OAAA,CAACnC,GAAG;kBAACsL,EAAE,EAAE;oBAAEC,OAAO,EAAE,MAAM;oBAAEW,UAAU,EAAE,QAAQ;oBAAEC,GAAG,EAAE;kBAAE,CAAE;kBAAAvG,QAAA,gBACzDzD,OAAA,CAACpC,UAAU;oBAACuM,OAAO,EAAC,WAAW;oBAAA1G,QAAA,EAAErD,IAAI,CAACiF;kBAAO;oBAAA3B,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAa,CAAC,EAC1DzD,IAAI,CAAC8F,eAAe,iBACnBlG,OAAA,CAAC/B,IAAI;oBAACmM,IAAI,EAAC,OAAO;oBAACC,KAAK,EAAC,eAAe;oBAACH,KAAK,EAAC;kBAAS;oBAAAxG,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE,CAC3D;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACE,CACN;gBACDyG,SAAS,EAAE,GAAGnI,SAAS,CAAC/B,IAAI,CAACiF,OAAO,CAAC,IAAI,GAAG;cAAgB;gBAAA3B,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC7D,CAAC,eACF7D,OAAA,CAACxB,uBAAuB;gBAAAiF,QAAA,eACtBzD,OAAA,CAAC9B,UAAU;kBACTkM,IAAI,EAAC,OAAO;kBACZF,KAAK,EAAC,OAAO;kBACbnG,OAAO,EAAEA,CAAA,KAAMiB,0BAA0B,CAAC5E,IAAI,CAAE;kBAChD2K,KAAK,EAAC,yBAAyB;kBAAAtH,QAAA,eAE/BzD,OAAA,CAACf,UAAU;oBAACgL,QAAQ,EAAC;kBAAO;oBAAAvG,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACrB;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACU,CAAC;YAAA,GA5BrBzD,IAAI,CAACiF,OAAO;cAAA3B,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OA6BT,CACX;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACE,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACI,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACV,CACP,EAGA8C,MAAM,CAACqF,IAAI,CAAC7I,QAAQ,CAAC,CAACtC,MAAM,GAAG,CAAC,iBAC/Bb,OAAA,CAAChC,KAAK;QAAC8F,QAAQ,EAAC,SAAS;QAACqF,EAAE,EAAE;UAAEoB,EAAE,EAAE;QAAE,CAAE;QAAA9G,QAAA,gBACtCzD,OAAA,CAACpC,UAAU;UAACuM,OAAO,EAAC,WAAW;UAAC8B,YAAY;UAAAxI,QAAA,EAAC;QAAO;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAY,CAAC,eACjE7D,OAAA,CAAC3B,IAAI;UAACkL,KAAK;UAAA9F,QAAA,EACRkD,MAAM,CAACC,OAAO,CAACzD,QAAQ,CAAC,CAACkF,GAAG,CAAC,CAAC,CAACjC,MAAM,EAAE8F,OAAO,CAAC,kBAC9ClM,OAAA,CAAC1B,QAAQ;YAAc6K,EAAE,EAAE;cAAEgD,EAAE,EAAE;YAAE,CAAE;YAAA1I,QAAA,eACnCzD,OAAA,CAACzB,YAAY;cACXuL,OAAO,EAAE,GAAG1D,MAAM,KAAK8F,OAAO,EAAG;cACjCE,sBAAsB,EAAE;gBAAEjC,OAAO,EAAE;cAAQ;YAAE;cAAAzG,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC9C;UAAC,GAJWuC,MAAM;YAAA1C,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAKX,CACX;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACE,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACF,CACR,eAGD7D,OAAA,CAAChC,KAAK;QAAC8F,QAAQ,EAAC,MAAM;QAACqF,EAAE,EAAE;UAAEoB,EAAE,EAAE;QAAE,CAAE;QAAA9G,QAAA,eACnCzD,OAAA,CAACpC,UAAU;UAACuM,OAAO,EAAC,OAAO;UAAA1G,QAAA,gBACzBzD,OAAA;YAAAyD,QAAA,EAAQ;UAAW;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC,4JAE9B;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAY;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACR,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACC,CAAC,eAEhB7D,OAAA,CAACtC,aAAa;MAACyL,EAAE,EAAE;QAAEkD,EAAE,EAAE,CAAC;QAAEF,EAAE,EAAE;MAAE,CAAE;MAAA1I,QAAA,gBAClCzD,OAAA,CAACrC,MAAM;QAACoG,OAAO,EAAE/C,OAAQ;QAACsL,QAAQ,EAAE7K,MAAO;QAAAgC,QAAA,EAAC;MAE5C;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAQ,CAAC,eACT7D,OAAA,CAACrC,MAAM;QACLoG,OAAO,EAAEA,CAAA,KAAMyD,UAAU,CAAC,KAAK,CAAE;QACjC0C,KAAK,EAAC,SAAS;QACfC,OAAO,EAAC,WAAW;QACnBmC,QAAQ,EAAE7K,MAAM,IAAIQ,YAAY,CAACpB,MAAM,KAAK,CAAC,IAAI8F,MAAM,CAACqF,IAAI,CAAC/I,MAAM,CAAC,CAACpC,MAAM,GAAG,CAAE;QAChFiK,SAAS,EAAErJ,MAAM,gBAAGzB,OAAA,CAACjC,gBAAgB;UAACqM,IAAI,EAAE;QAAG;UAAA1G,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,gBAAG7D,OAAA,CAACb,QAAQ;UAAAuE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAE;QAAAJ,QAAA,EAEjEhC,MAAM,GAAG,gBAAgB,GAAG,SAASQ,YAAY,CAACpB,MAAM;MAAO;QAAA6C,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC1D,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACI,CAAC,eAGhB7D,OAAA,CAACF,sBAAsB;MACrBiB,IAAI,EAAE0B,sBAAuB;MAC7BzB,OAAO,EAAEA,CAAA,KAAM;QACb0B,yBAAyB,CAAC,KAAK,CAAC;QAChCE,wBAAwB,CAAC,IAAI,CAAC;MAChC,CAAE;MACFxC,IAAI,EAAEuC,qBAAqB,aAArBA,qBAAqB,uBAArBA,qBAAqB,CAAEvC,IAAK;MAClCC,MAAM,EAAEsC,qBAAqB,aAArBA,qBAAqB,uBAArBA,qBAAqB,CAAEtC,MAAO;MACtCkM,SAAS,EAAEtG;IAA0B;MAAAvC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACtC,CAAC,eAGF7D,OAAA,CAACzC,MAAM;MACLwD,IAAI,EAAE8B,cAAe;MACrB7B,OAAO,EAAEA,CAAA,KAAM8B,iBAAiB,CAAC,KAAK,CAAE;MACxCS,QAAQ,EAAC,IAAI;MACbC,SAAS;MAAAC,QAAA,gBAETzD,OAAA,CAACxC,WAAW;QAAAiG,QAAA,eACVzD,OAAA,CAACnC,GAAG;UAACsL,EAAE,EAAE;YAAEC,OAAO,EAAE,MAAM;YAAEW,UAAU,EAAE,QAAQ;YAAEC,GAAG,EAAE;UAAE,CAAE;UAAAvG,QAAA,gBACzDzD,OAAA,CAACX,WAAW;YAAC6K,KAAK,EAAC;UAAS;YAAAxG,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,eAC/B7D,OAAA,CAACpC,UAAU;YAACuM,OAAO,EAAC,IAAI;YAAA1G,QAAA,EAAC;UAAmB;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAY,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACtD;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACK,CAAC,eACd7D,OAAA,CAACvC,aAAa;QAAAgG,QAAA,EACXV,cAAc,iBACb/C,OAAA,CAAAE,SAAA;UAAAuD,QAAA,gBACEzD,OAAA,CAAChC,KAAK;YAAC8F,QAAQ,EAAC,SAAS;YAACqF,EAAE,EAAE;cAAES,EAAE,EAAE;YAAE,CAAE;YAAAnG,QAAA,eACtCzD,OAAA,CAACpC,UAAU;cAACuM,OAAO,EAAC,OAAO;cAAC8B,YAAY;cAAAxI,QAAA,gBACtCzD,OAAA;gBAAAyD,QAAA,EAAQ;cAAW;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC,qDAC9B;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAY;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACR,CAAC,eACR7D,OAAA,CAACnC,GAAG;YAACsL,EAAE,EAAE;cAAES,EAAE,EAAE;YAAE,CAAE;YAAAnG,QAAA,gBACjBzD,OAAA,CAACpC,UAAU;cAACuM,OAAO,EAAC,OAAO;cAAC8B,YAAY;cAAAxI,QAAA,gBACtCzD,OAAA;gBAAAyD,QAAA,EAAQ;cAAuB;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC,KAAC,EAACd,cAAc,CAAC2D,oBAAoB,CAACU,OAAO,CAAC,CAAC,CAAC,EAAC,IAC3F;YAAA;cAAA1D,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAY,CAAC,eACb7D,OAAA,CAACpC,UAAU;cAACuM,OAAO,EAAC,OAAO;cAAC8B,YAAY;cAAAxI,QAAA,gBACtCzD,OAAA;gBAAAyD,QAAA,EAAQ;cAAqB;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC,KAAC,EAACd,cAAc,CAACmE,kBAAkB,CAACE,OAAO,CAAC,CAAC,CAAC,EAAC,IACvF;YAAA;cAAA1D,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAY,CAAC,eACb7D,OAAA,CAACpC,UAAU;cAACuM,OAAO,EAAC,OAAO;cAAC8B,YAAY;cAAAxI,QAAA,gBACtCzD,OAAA;gBAAAyD,QAAA,EAAQ;cAAU;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC,KAAC,EAAC,CAACd,cAAc,CAAC2D,oBAAoB,GAAG3D,cAAc,CAACmE,kBAAkB,EAAEE,OAAO,CAAC,CAAC,CAAC,EAAC,IACpH;YAAA;cAAA1D,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAY,CAAC,eACb7D,OAAA,CAACpC,UAAU;cAACuM,OAAO,EAAC,OAAO;cAAA1G,QAAA,gBACzBzD,OAAA;gBAAAyD,QAAA,EAAQ;cAAe;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC,KAAC,EAACd,cAAc,CAACd,YAAY;YAAA;cAAAyB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACnD,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACV,CAAC,eACN7D,OAAA,CAACpC,UAAU;YAACuM,OAAO,EAAC,OAAO;YAAA1G,QAAA,EAAC;UAE5B;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAY,CAAC;QAAA,eACb;MACH;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACY,CAAC,eAChB7D,OAAA,CAACtC,aAAa;QAAA+F,QAAA,gBACZzD,OAAA,CAACrC,MAAM;UAACoG,OAAO,EAAEA,CAAA,KAAMjB,iBAAiB,CAAC,KAAK,CAAE;UAAAW,QAAA,EAAC;QAEjD;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC,eACT7D,OAAA,CAACrC,MAAM;UACLoG,OAAO,EAAEyE,uBAAwB;UACjC0B,KAAK,EAAC,SAAS;UACfC,OAAO,EAAC,WAAW;UACnBW,SAAS,eAAE9K,OAAA,CAACX,WAAW;YAAAqE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAE;UAAAJ,QAAA,EAC5B;QAED;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACI,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACV,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CAAC;AAEb,CAAC;AAACzC,EAAA,CA1zBIN,oBAAoB;AAAA0L,EAAA,GAApB1L,oBAAoB;AA4zB1B,eAAeA,oBAAoB;AAAC,IAAA0L,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}