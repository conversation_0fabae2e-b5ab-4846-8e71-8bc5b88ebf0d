{"ast": null, "code": "var _jsxFileName = \"C:\\\\CMS\\\\webapp\\\\frontend\\\\src\\\\components\\\\cavi\\\\CertificazioneCaviImproved.js\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect, useCallback, forwardRef, useImperativeHandle } from 'react';\nimport { Box, Typography, Button, Paper, Grid, Card, CardContent, Dialog, DialogTitle, DialogContent, DialogActions, TextField, FormControl, InputLabel, Select, MenuItem, Autocomplete, CircularProgress, Alert, Table, TableBody, TableCell, TableContainer, TableHead, TableRow, IconButton, Tabs, Tab, Pagination, InputAdornment, Divider, Stack, Chip, Tooltip, Badge, LinearProgress, Collapse, List, ListItem, ListItemText, ListItemIcon, Snackbar, AppBar, Toolbar, Container, Fab } from '@mui/material';\nimport { Add as AddIcon, Search as SearchIcon, FilterList as FilterIcon, PictureAsPdf as PdfIcon, Download as DownloadIcon, Visibility as ViewIcon, Delete as DeleteIcon, Edit as EditIcon, Save as SaveIcon, Clear as ClearIcon, Build as BuildIcon, CheckCircle as CheckIcon, Warning as WarningIcon, GetApp as ExportIcon, Print as PrintIcon, Email as EmailIcon, CloudUpload as UploadIcon, Assessment as ReportIcon, Settings as SettingsIcon, Refresh as RefreshIcon, ExpandMore as ExpandMoreIcon, ExpandLess as ExpandLessIcon, Info as InfoIcon, Error as ErrorIcon, Schedule as ScheduleIcon, Person as PersonIcon, Cable as CableIcon, Science as ScienceIcon, Block as BlockIcon } from '@mui/icons-material';\nimport certificazioneService from '../../services/certificazioneService';\nimport caviService from '../../services/caviService';\nimport { jsxDEV as _jsxDEV, Fragment as _Fragment } from \"react/jsx-dev-runtime\";\nconst CertificazioneCaviImproved = /*#__PURE__*/_s(/*#__PURE__*/forwardRef(_c = _s(({\n  cantiereId,\n  onSuccess,\n  onError\n}, ref) => {\n  _s();\n  // Stati principali\n  const [loading, setLoading] = useState(false);\n  const [activeTab, setActiveTab] = useState(0);\n  const [certificazioni, setCertificazioni] = useState([]);\n  const [cavi, setCavi] = useState([]);\n  const [strumenti, setStrumenti] = useState([]);\n\n  // Stati per ricerca e filtri avanzati\n  const [searchTerm, setSearchTerm] = useState('');\n  const [filteredCavi, setFilteredCavi] = useState([]);\n  const [filteredCertificazioni, setFilteredCertificazioni] = useState([]);\n  const [advancedFiltersOpen, setAdvancedFiltersOpen] = useState(false);\n  const [filters, setFilters] = useState({\n    stato: '',\n    tipologia: '',\n    operatore: '',\n    dataInizio: '',\n    dataFine: '',\n    valoreIsolamento: '',\n    risultatoTest: '',\n    strumento: '',\n    certificazione: ''\n  });\n\n  // Stati per paginazione e ordinamento\n  const [currentPage, setCurrentPage] = useState(1);\n  const [itemsPerPage, setItemsPerPage] = useState(10);\n  const [sortBy, setSortBy] = useState('id_cavo');\n  const [sortOrder, setSortOrder] = useState('asc'); // Ordine crescente di default per ID cavo\n\n  // Stati per dialogs e modali\n  const [openDialog, setOpenDialog] = useState(false);\n  const [dialogType, setDialogType] = useState('');\n  const [selectedItem, setSelectedItem] = useState(null);\n  const [bulkSelection, setBulkSelection] = useState([]);\n  const [bulkMode, setBulkMode] = useState(false);\n\n  // Stati per notifiche e feedback\n  const [snackbar, setSnackbar] = useState({\n    open: false,\n    message: '',\n    severity: 'info'\n  });\n  const [progress, setProgress] = useState(0);\n  const [operationInProgress, setOperationInProgress] = useState(false);\n\n  // Stati per form certificazione avanzato\n  const [formData, setFormData] = useState({\n    id_cavo: '',\n    id_operatore: '',\n    id_strumento: '',\n    lunghezza_misurata: '',\n    valore_continuita: 'OK',\n    valore_isolamento: '',\n    valore_resistenza: 'OK',\n    note: '',\n    temperatura_ambiente: '',\n    umidita: '',\n    tensione_prova: '',\n    durata_prova: '',\n    risultato_finale: 'CONFORME'\n  });\n\n  // Stati per statistiche e dashboard\n  const [statistics, setStatistics] = useState({\n    totaleCavi: 0,\n    caviCertificati: 0,\n    caviNonCertificati: 0,\n    percentualeCompletamento: 0,\n    certificazioniOggi: 0,\n    certificazioniSettimana: 0\n  });\n\n  // Carica dati iniziali\n  useEffect(() => {\n    loadInitialData();\n  }, [cantiereId]);\n\n  // Filtra cavi in base alla ricerca\n  useEffect(() => {\n    filterCavi();\n  }, [cavi, searchTerm, filters, sortBy, sortOrder]);\n\n  // Filtra certificazioni\n  useEffect(() => {\n    filterCertificazioni();\n  }, [certificazioni, searchTerm, filters, sortBy, sortOrder]);\n\n  // Ricalcola statistiche quando cambiano i dati\n  useEffect(() => {\n    calculateStatistics();\n  }, [cavi, certificazioni]);\n\n  // Riapplica filtri quando cambia il tab attivo E applica filtri iniziali\n  useEffect(() => {\n    if (activeTab === 0) {\n      filterCavi();\n    } else if (activeTab === 1) {\n      filterCertificazioni();\n    }\n  }, [activeTab, cavi, certificazioni]); // Aggiunto cavi e certificazioni come dipendenze\n\n  const loadInitialData = async () => {\n    try {\n      setLoading(true);\n      setProgress(0);\n\n      // Carica dati in sequenza con progress\n      setProgress(25);\n      await loadCavi();\n      setProgress(50);\n      await loadCertificazioni();\n      setProgress(75);\n      await loadStrumenti();\n      setProgress(100);\n      calculateStatistics();\n    } catch (error) {\n      showSnackbar('Errore nel caricamento dei dati iniziali', 'error');\n      onError('Errore nel caricamento dei dati iniziali');\n    } finally {\n      setLoading(false);\n      setProgress(0);\n    }\n  };\n  const loadCertificazioni = async () => {\n    try {\n      const data = await certificazioneService.getCertificazioni(cantiereId);\n      setCertificazioni(data);\n      return data;\n    } catch (error) {\n      console.error('Errore nel caricamento delle certificazioni:', error);\n      throw error;\n    }\n  };\n  const loadCavi = async () => {\n    try {\n      const data = await caviService.getCavi(cantiereId);\n      // Ordina sempre i cavi per ID in ordine crescente (C001, C002, etc.)\n      const sortedData = data.sort((a, b) => {\n        // Estrai il numero dall'ID del cavo (es. C001 -> 1)\n        const getNumFromId = id => {\n          const match = id.match(/(\\d+)/);\n          return match ? parseInt(match[1], 10) : 0;\n        };\n        return getNumFromId(a.id_cavo) - getNumFromId(b.id_cavo);\n      });\n      setCavi(sortedData);\n      return sortedData;\n    } catch (error) {\n      console.error('Errore nel caricamento dei cavi:', error);\n      throw error;\n    }\n  };\n  const loadStrumenti = async () => {\n    try {\n      const data = await certificazioneService.getStrumenti(cantiereId);\n      setStrumenti(data);\n      return data;\n    } catch (error) {\n      console.error('Errore nel caricamento degli strumenti:', error);\n      throw error;\n    }\n  };\n\n  // Funzione per verificare se un cavo può essere certificato\n  const puoEssereCertificato = useCallback(cavo => {\n    // Verifica che il cavo sia installato/posato\n    const isInstallato = cavo.stato_installazione === 'Installato' || cavo.stato_installazione === 'INSTALLATO' || cavo.stato_installazione === 'POSATO';\n\n    // Per la certificazione basta che sia posato\n    // Il collegamento può essere gestito durante la certificazione\n    return isInstallato;\n  }, []);\n\n  // Funzione per verificare se un cavo è completamente collegato\n  const isCavoCollegato = useCallback(cavo => {\n    const isCollegato = cavo.collegamenti === 3;\n    const hasResponsabili = cavo.responsabile_partenza && cavo.responsabile_arrivo;\n    return isCollegato && hasResponsabili;\n  }, []);\n\n  // Calcola statistiche avanzate\n  const calculateStatistics = useCallback(() => {\n    if (!cavi || !certificazioni) return;\n    const totaleCavi = cavi.length;\n    const caviCertificati = certificazioni.length;\n    const caviNonCertificati = totaleCavi - caviCertificati;\n    const percentualeCompletamento = totaleCavi > 0 ? Math.round(caviCertificati / totaleCavi * 100) : 0;\n\n    // Calcola certificazioni di oggi\n    const oggi = new Date().toDateString();\n    const certificazioniOggi = certificazioni.filter(cert => new Date(cert.data_certificazione).toDateString() === oggi).length;\n\n    // Calcola certificazioni della settimana\n    const unaSettimanaFa = new Date();\n    unaSettimanaFa.setDate(unaSettimanaFa.getDate() - 7);\n    const certificazioniSettimana = certificazioni.filter(cert => new Date(cert.data_certificazione) >= unaSettimanaFa).length;\n\n    // Calcola cavi certificabili (solo posati/installati)\n    const caviCertificabili = cavi.filter(cavo => puoEssereCertificato(cavo)).length;\n    const caviNonCertificabili = totaleCavi - caviCertificabili;\n\n    // Calcola cavi collegati completamente\n    const caviCollegati = cavi.filter(cavo => isCavoCollegato(cavo)).length;\n    setStatistics({\n      totaleCavi,\n      caviCertificati,\n      caviNonCertificati,\n      caviCertificabili,\n      caviNonCertificabili,\n      caviCollegati,\n      percentualeCompletamento,\n      certificazioniOggi,\n      certificazioniSettimana\n    });\n  }, [cavi, certificazioni, puoEssereCertificato, isCavoCollegato]);\n\n  // Gestione snackbar\n  const showSnackbar = (message, severity = 'info') => {\n    setSnackbar({\n      open: true,\n      message,\n      severity\n    });\n  };\n  const closeSnackbar = () => {\n    setSnackbar({\n      ...snackbar,\n      open: false\n    });\n  };\n  const filterCavi = () => {\n    let filtered = cavi;\n\n    // Filtro per ricerca testuale avanzata\n    if (searchTerm) {\n      const searchLower = searchTerm.toLowerCase();\n      filtered = filtered.filter(cavo => {\n        var _cavo$tipologia, _cavo$ubicazione_part, _cavo$ubicazione_arri, _cavo$sezione, _cavo$utility;\n        return cavo.id_cavo.toLowerCase().includes(searchLower) || ((_cavo$tipologia = cavo.tipologia) === null || _cavo$tipologia === void 0 ? void 0 : _cavo$tipologia.toLowerCase().includes(searchLower)) || ((_cavo$ubicazione_part = cavo.ubicazione_partenza) === null || _cavo$ubicazione_part === void 0 ? void 0 : _cavo$ubicazione_part.toLowerCase().includes(searchLower)) || ((_cavo$ubicazione_arri = cavo.ubicazione_arrivo) === null || _cavo$ubicazione_arri === void 0 ? void 0 : _cavo$ubicazione_arri.toLowerCase().includes(searchLower)) || ((_cavo$sezione = cavo.sezione) === null || _cavo$sezione === void 0 ? void 0 : _cavo$sezione.toLowerCase().includes(searchLower)) || ((_cavo$utility = cavo.utility) === null || _cavo$utility === void 0 ? void 0 : _cavo$utility.toLowerCase().includes(searchLower));\n      });\n    }\n\n    // Filtri specifici avanzati\n    if (filters.stato) {\n      filtered = filtered.filter(cavo => cavo.stato_installazione === filters.stato);\n    }\n    if (filters.tipologia) {\n      filtered = filtered.filter(cavo => cavo.tipologia === filters.tipologia);\n    }\n\n    // Filtro per stato certificazione\n    if (filters.certificazione) {\n      if (filters.certificazione === 'CERTIFICATO') {\n        filtered = filtered.filter(cavo => certificazioni.some(cert => cert.id_cavo === cavo.id_cavo));\n      } else if (filters.certificazione === 'NON_CERTIFICATO') {\n        filtered = filtered.filter(cavo => !certificazioni.some(cert => cert.id_cavo === cavo.id_cavo));\n      }\n    }\n\n    // Ordinamento - speciale per ID cavo per mantenere ordine numerico\n    filtered.sort((a, b) => {\n      let aValue = a[sortBy];\n      let bValue = b[sortBy];\n\n      // Ordinamento speciale per ID cavo (C001, C002, etc.)\n      if (sortBy === 'id_cavo') {\n        const getNumFromId = id => {\n          const match = id.match(/(\\d+)/);\n          return match ? parseInt(match[1], 10) : 0;\n        };\n        const aNum = getNumFromId(aValue);\n        const bNum = getNumFromId(bValue);\n        if (sortOrder === 'asc') {\n          return aNum - bNum;\n        } else {\n          return bNum - aNum;\n        }\n      }\n\n      // Ordinamento normale per altri campi\n      if (typeof aValue === 'string') {\n        aValue = aValue.toLowerCase();\n        bValue = bValue.toLowerCase();\n      }\n      if (sortOrder === 'asc') {\n        return aValue > bValue ? 1 : -1;\n      } else {\n        return aValue < bValue ? 1 : -1;\n      }\n    });\n    setFilteredCavi(filtered);\n  };\n  const filterCertificazioni = () => {\n    let filtered = certificazioni;\n\n    // Ricerca testuale avanzata\n    if (searchTerm) {\n      const searchLower = searchTerm.toLowerCase();\n      filtered = filtered.filter(cert => {\n        var _cert$operatore, _cert$numero_certific, _cert$note;\n        return cert.id_cavo.toLowerCase().includes(searchLower) || ((_cert$operatore = cert.operatore) === null || _cert$operatore === void 0 ? void 0 : _cert$operatore.toLowerCase().includes(searchLower)) || ((_cert$numero_certific = cert.numero_certificato) === null || _cert$numero_certific === void 0 ? void 0 : _cert$numero_certific.toLowerCase().includes(searchLower)) || ((_cert$note = cert.note) === null || _cert$note === void 0 ? void 0 : _cert$note.toLowerCase().includes(searchLower));\n      });\n    }\n\n    // Filtri avanzati\n    if (filters.operatore) {\n      filtered = filtered.filter(cert => cert.operatore === filters.operatore);\n    }\n    if (filters.strumento) {\n      filtered = filtered.filter(cert => cert.strumento === filters.strumento);\n    }\n    if (filters.risultatoTest) {\n      filtered = filtered.filter(cert => cert.risultato_finale === filters.risultatoTest);\n    }\n    if (filters.dataInizio) {\n      filtered = filtered.filter(cert => new Date(cert.data_certificazione) >= new Date(filters.dataInizio));\n    }\n    if (filters.dataFine) {\n      filtered = filtered.filter(cert => new Date(cert.data_certificazione) <= new Date(filters.dataFine));\n    }\n    if (filters.valoreIsolamento) {\n      const valore = parseFloat(filters.valoreIsolamento);\n      filtered = filtered.filter(cert => parseFloat(cert.valore_isolamento) >= valore);\n    }\n\n    // Ordinamento\n    filtered.sort((a, b) => {\n      let aValue = a[sortBy];\n      let bValue = b[sortBy];\n      if (sortBy === 'data_certificazione') {\n        aValue = new Date(aValue);\n        bValue = new Date(bValue);\n      } else if (typeof aValue === 'string') {\n        aValue = aValue.toLowerCase();\n        bValue = bValue.toLowerCase();\n      }\n      if (sortOrder === 'asc') {\n        return aValue > bValue ? 1 : -1;\n      } else {\n        return aValue < bValue ? 1 : -1;\n      }\n    });\n    setFilteredCertificazioni(filtered);\n  };\n\n  // Gestione selezione multipla - SOLO per certificazioni\n  const toggleBulkMode = () => {\n    if (activeTab !== 1) {\n      showSnackbar('La selezione multipla è disponibile solo per le certificazioni', 'warning');\n      return;\n    }\n    setBulkMode(!bulkMode);\n    setBulkSelection([]);\n    showSnackbar(!bulkMode ? 'Modalità selezione attivata - Clicca sulle certificazioni per selezionarle' : 'Modalità selezione disattivata', 'info');\n  };\n  const toggleItemSelection = itemId => {\n    setBulkSelection(prev => {\n      const newSelection = prev.includes(itemId) ? prev.filter(id => id !== itemId) : [...prev, itemId];\n      showSnackbar(`${newSelection.length} certificazioni selezionate`, 'info');\n      return newSelection;\n    });\n  };\n  const selectAllItems = () => {\n    if (activeTab !== 1) return;\n    const allIds = filteredCertificazioni.map(cert => cert.id_certificazione);\n    setBulkSelection(allIds);\n    showSnackbar(`Tutte le ${allIds.length} certificazioni selezionate`, 'success');\n  };\n  const clearSelection = () => {\n    setBulkSelection([]);\n    showSnackbar('Selezione cancellata', 'info');\n  };\n\n  // Funzione per verificare se un cavo è certificato\n  const isCavoCertificato = idCavo => {\n    return certificazioni.some(cert => cert.id_cavo === idCavo);\n  };\n\n  // Funzione per ottenere il messaggio di errore per cavi non certificabili\n  const getMessaggioErroreCertificazione = cavo => {\n    const isInstallato = cavo.stato_installazione === 'Installato' || cavo.stato_installazione === 'INSTALLATO' || cavo.stato_installazione === 'POSATO';\n    if (!isInstallato) {\n      return 'Il cavo deve essere posato/installato prima di poter essere certificato';\n    }\n    return 'Cavo non certificabile per motivi sconosciuti';\n  };\n\n  // Gestione tabs\n  const handleTabChange = (event, newValue) => {\n    setActiveTab(newValue);\n    setCurrentPage(1);\n    setSearchTerm('');\n    setFilters({\n      stato: '',\n      tipologia: '',\n      operatore: ''\n    });\n  };\n\n  // Gestione dialogs\n  const openCreateDialog = (cavoPreselezionato = null) => {\n    setDialogType('create');\n    setSelectedItem(null);\n\n    // Se viene passato un cavo, precompila il form\n    if (cavoPreselezionato) {\n      setFormData({\n        id_cavo: cavoPreselezionato.id_cavo,\n        id_operatore: '',\n        id_strumento: '',\n        lunghezza_misurata: cavoPreselezionato.metratura_reale || cavoPreselezionato.metri_teorici || '',\n        valore_continuita: 'OK',\n        valore_isolamento: '',\n        valore_resistenza: 'OK',\n        note: '',\n        temperatura_ambiente: '',\n        umidita: '',\n        tensione_prova: '',\n        durata_prova: '',\n        risultato_finale: 'CONFORME'\n      });\n      showSnackbar(`Cavo ${cavoPreselezionato.id_cavo} selezionato automaticamente`, 'success');\n    } else {\n      // Reset form per nuova certificazione generica\n      setFormData({\n        id_cavo: '',\n        id_operatore: '',\n        id_strumento: '',\n        lunghezza_misurata: '',\n        valore_continuita: 'OK',\n        valore_isolamento: '',\n        valore_resistenza: 'OK',\n        note: '',\n        temperatura_ambiente: '',\n        umidita: '',\n        tensione_prova: '',\n        durata_prova: '',\n        risultato_finale: 'CONFORME'\n      });\n    }\n    setOpenDialog(true);\n  };\n  const closeDialog = () => {\n    setOpenDialog(false);\n    setSelectedItem(null);\n    setDialogType('');\n  };\n\n  // Gestione form\n  const handleFormChange = (field, value) => {\n    setFormData(prev => ({\n      ...prev,\n      [field]: value\n    }));\n  };\n  const handleCavoSelect = cavo => {\n    setFormData(prev => ({\n      ...prev,\n      id_cavo: cavo.id_cavo,\n      lunghezza_misurata: cavo.metratura_reale || cavo.metri_teorici || ''\n    }));\n  };\n\n  // Operazioni CRUD avanzate\n  const handleCreateCertificazione = async () => {\n    try {\n      if (!formData.id_cavo || !formData.id_operatore || !formData.id_strumento || !formData.valore_isolamento) {\n        showSnackbar('Compila tutti i campi obbligatori', 'warning');\n        return;\n      }\n\n      // Verifica che il cavo possa essere certificato\n      const cavo = cavi.find(c => c.id_cavo === formData.id_cavo);\n      if (!cavo) {\n        showSnackbar('Cavo non trovato', 'error');\n        return;\n      }\n      if (!puoEssereCertificato(cavo)) {\n        const messaggio = getMessaggioErroreCertificazione(cavo);\n        showSnackbar(`Impossibile certificare il cavo: ${messaggio}`, 'error');\n        return;\n      }\n\n      // Verifica che il cavo non sia già certificato\n      if (isCavoCertificato(formData.id_cavo)) {\n        showSnackbar('Il cavo è già stato certificato', 'warning');\n        return;\n      }\n\n      // Verifica se il cavo è collegato, altrimenti chiedi conferma\n      if (!isCavoCollegato(cavo)) {\n        const conferma = window.confirm(`ATTENZIONE: Il cavo ${cavo.id_cavo} non risulta completamente collegato.\\n\\n` + `Stato collegamenti: ${cavo.collegamenti === 0 ? 'Non collegato' : cavo.collegamenti === 1 ? 'Solo partenza collegata' : cavo.collegamenti === 2 ? 'Solo arrivo collegato' : 'Stato sconosciuto'}\\n\\n` + `Vuoi procedere comunque con la certificazione?\\n` + `(Ricorda di completare i collegamenti prima della messa in servizio)`);\n        if (!conferma) {\n          return;\n        }\n      }\n      setOperationInProgress(true);\n      await certificazioneService.createCertificazione(cantiereId, formData);\n      showSnackbar('Certificazione creata con successo', 'success');\n      closeDialog();\n      await loadCertificazioni();\n      calculateStatistics();\n    } catch (error) {\n      showSnackbar('Errore nella creazione della certificazione: ' + (error.message || 'Errore sconosciuto'), 'error');\n    } finally {\n      setOperationInProgress(false);\n    }\n  };\n  const handleGeneratePdf = async certificazione => {\n    try {\n      setOperationInProgress(true);\n      showSnackbar('Generazione PDF in corso...', 'info');\n      const response = await certificazioneService.generatePdf(cantiereId, certificazione.id_certificazione);\n      if (response.file_url) {\n        // Apri il PDF in una nuova finestra\n        const newWindow = window.open(response.file_url, '_blank');\n        if (newWindow) {\n          showSnackbar('PDF generato e aperto in una nuova finestra', 'success');\n        } else {\n          // Se il popup è bloccato, offri il download diretto\n          const link = document.createElement('a');\n          link.href = response.file_url;\n          link.download = `certificazione_${certificazione.numero_certificato}.pdf`;\n          document.body.appendChild(link);\n          link.click();\n          document.body.removeChild(link);\n          showSnackbar('PDF scaricato nella cartella Download', 'success');\n        }\n      } else if (response.pdf_content) {\n        // Se il PDF viene restituito come contenuto base64\n        const blob = new Blob([atob(response.pdf_content)], {\n          type: 'application/pdf'\n        });\n        const url = URL.createObjectURL(blob);\n        const link = document.createElement('a');\n        link.href = url;\n        link.download = `certificazione_${certificazione.numero_certificato}.pdf`;\n        document.body.appendChild(link);\n        link.click();\n        document.body.removeChild(link);\n        URL.revokeObjectURL(url);\n        showSnackbar('PDF scaricato con successo', 'success');\n      } else {\n        showSnackbar('Errore: Formato PDF non riconosciuto', 'error');\n      }\n    } catch (error) {\n      console.error('Errore generazione PDF:', error);\n      showSnackbar('Errore nella generazione del PDF: ' + (error.message || 'Errore sconosciuto'), 'error');\n    } finally {\n      setOperationInProgress(false);\n    }\n  };\n  const handleDeleteCertificazione = async certificazione => {\n    if (window.confirm(`Sei sicuro di voler eliminare la certificazione ${certificazione.numero_certificato}?`)) {\n      try {\n        setOperationInProgress(true);\n        await certificazioneService.deleteCertificazione(cantiereId, certificazione.id_certificazione);\n        showSnackbar('Certificazione eliminata con successo', 'success');\n        await loadCertificazioni();\n        calculateStatistics();\n      } catch (error) {\n        showSnackbar('Errore nell\\'eliminazione della certificazione: ' + (error.message || 'Errore sconosciuto'), 'error');\n      } finally {\n        setOperationInProgress(false);\n      }\n    }\n  };\n\n  // Operazioni bulk\n  const handleBulkDelete = async () => {\n    if (bulkSelection.length === 0) {\n      showSnackbar('Seleziona almeno un elemento', 'warning');\n      return;\n    }\n    if (window.confirm(`Sei sicuro di voler eliminare ${bulkSelection.length} certificazioni?`)) {\n      try {\n        setOperationInProgress(true);\n        for (const id of bulkSelection) {\n          await certificazioneService.deleteCertificazione(cantiereId, id);\n        }\n        showSnackbar(`${bulkSelection.length} certificazioni eliminate con successo`, 'success');\n        setBulkSelection([]);\n        await loadCertificazioni();\n        calculateStatistics();\n      } catch (error) {\n        showSnackbar('Errore nell\\'eliminazione delle certificazioni', 'error');\n      } finally {\n        setOperationInProgress(false);\n      }\n    }\n  };\n  const handleBulkExport = async () => {\n    if (bulkSelection.length === 0) {\n      showSnackbar('Seleziona almeno un elemento', 'warning');\n      return;\n    }\n    try {\n      setOperationInProgress(true);\n      // Implementa export bulk\n      const selectedCerts = certificazioni.filter(cert => bulkSelection.includes(cert.id_certificazione));\n\n      // Crea CSV\n      const csvContent = generateCSV(selectedCerts);\n      downloadCSV(csvContent, `certificazioni_${new Date().toISOString().split('T')[0]}.csv`);\n      showSnackbar(`${bulkSelection.length} certificazioni esportate`, 'success');\n    } catch (error) {\n      showSnackbar('Errore nell\\'esportazione', 'error');\n    } finally {\n      setOperationInProgress(false);\n    }\n  };\n\n  // Funzioni di export\n  const generateCSV = data => {\n    const headers = ['ID Cavo', 'Numero Certificato', 'Data', 'Operatore', 'Strumento', 'Lunghezza', 'Isolamento', 'Risultato'];\n    const rows = data.map(cert => [cert.id_cavo, cert.numero_certificato, new Date(cert.data_certificazione).toLocaleDateString(), cert.operatore, cert.strumento, cert.lunghezza_misurata, cert.valore_isolamento, cert.risultato_finale]);\n    return [headers, ...rows].map(row => row.join(',')).join('\\n');\n  };\n  const downloadCSV = (content, filename) => {\n    const blob = new Blob([content], {\n      type: 'text/csv;charset=utf-8;'\n    });\n    const link = document.createElement('a');\n    if (link.download !== undefined) {\n      const url = URL.createObjectURL(blob);\n      link.setAttribute('href', url);\n      link.setAttribute('download', filename);\n      link.style.visibility = 'hidden';\n      document.body.appendChild(link);\n      link.click();\n      document.body.removeChild(link);\n    }\n  };\n  const handleExportAll = () => {\n    const csvContent = generateCSV(filteredCertificazioni);\n    downloadCSV(csvContent, `tutte_certificazioni_${new Date().toISOString().split('T')[0]}.csv`);\n    showSnackbar('Esportazione completata', 'success');\n  };\n\n  // Espone metodi tramite ref\n  useImperativeHandle(ref, () => ({\n    handleOptionSelect: option => {\n      if (option === 'creaCertificazione') {\n        openCreateDialog();\n      } else if (option === 'visualizzaCertificazioni') {\n        setActiveTab(1);\n      }\n    }\n  }));\n\n  // Calcola elementi per paginazione\n  const getCurrentPageItems = items => {\n    const startIndex = (currentPage - 1) * itemsPerPage;\n    const endIndex = startIndex + itemsPerPage;\n    return items.slice(startIndex, endIndex);\n  };\n  const getTotalPages = items => Math.ceil(items.length / itemsPerPage);\n\n  // Ottieni opzioni uniche per filtri\n  const getUniqueValues = (array, field) => {\n    return [...new Set(array.map(item => item[field]).filter(Boolean))];\n  };\n\n  // Dashboard minimal con statistiche essenziali\n  const renderDashboard = () => /*#__PURE__*/_jsxDEV(Paper, {\n    sx: {\n      p: 2,\n      mb: 3,\n      bgcolor: 'grey.50'\n    },\n    children: /*#__PURE__*/_jsxDEV(Stack, {\n      direction: \"row\",\n      spacing: 4,\n      alignItems: \"center\",\n      justifyContent: \"space-between\",\n      flexWrap: \"wrap\",\n      children: [/*#__PURE__*/_jsxDEV(Stack, {\n        direction: \"row\",\n        alignItems: \"center\",\n        spacing: 1,\n        children: [/*#__PURE__*/_jsxDEV(CableIcon, {\n          color: \"primary\",\n          fontSize: \"small\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 833,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Box, {\n          children: [/*#__PURE__*/_jsxDEV(Typography, {\n            variant: \"h6\",\n            fontWeight: \"bold\",\n            sx: {\n              lineHeight: 1\n            },\n            children: statistics.totaleCavi\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 835,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Typography, {\n            variant: \"caption\",\n            color: \"text.secondary\",\n            children: \"Totale\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 838,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 834,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 832,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Stack, {\n        direction: \"row\",\n        alignItems: \"center\",\n        spacing: 1,\n        children: [/*#__PURE__*/_jsxDEV(CheckIcon, {\n          color: \"success\",\n          fontSize: \"small\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 845,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Box, {\n          children: [/*#__PURE__*/_jsxDEV(Typography, {\n            variant: \"h6\",\n            fontWeight: \"bold\",\n            sx: {\n              lineHeight: 1\n            },\n            children: statistics.caviCertificati\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 847,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Typography, {\n            variant: \"caption\",\n            color: \"text.secondary\",\n            children: \"Certificati\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 850,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 846,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 844,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Stack, {\n        direction: \"row\",\n        alignItems: \"center\",\n        spacing: 1,\n        children: [/*#__PURE__*/_jsxDEV(BuildIcon, {\n          color: \"info\",\n          fontSize: \"small\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 857,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Box, {\n          children: [/*#__PURE__*/_jsxDEV(Typography, {\n            variant: \"h6\",\n            fontWeight: \"bold\",\n            sx: {\n              lineHeight: 1\n            },\n            children: statistics.caviCertificabili\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 859,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Typography, {\n            variant: \"caption\",\n            color: \"text.secondary\",\n            children: \"Pronti\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 862,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 858,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 856,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Stack, {\n        direction: \"row\",\n        alignItems: \"center\",\n        spacing: 1,\n        children: [/*#__PURE__*/_jsxDEV(Box, {\n          sx: {\n            width: 32,\n            height: 32,\n            borderRadius: '50%',\n            bgcolor: statistics.percentualeCompletamento >= 80 ? 'success.main' : statistics.percentualeCompletamento >= 50 ? 'warning.main' : 'error.main',\n            display: 'flex',\n            alignItems: 'center',\n            justifyContent: 'center'\n          },\n          children: /*#__PURE__*/_jsxDEV(Typography, {\n            variant: \"caption\",\n            fontWeight: \"bold\",\n            color: \"white\",\n            children: [statistics.percentualeCompletamento, \"%\"]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 879,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 869,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Box, {\n          children: [/*#__PURE__*/_jsxDEV(Typography, {\n            variant: \"body2\",\n            fontWeight: \"medium\",\n            sx: {\n              lineHeight: 1\n            },\n            children: \"Completamento\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 884,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Typography, {\n            variant: \"caption\",\n            color: \"text.secondary\",\n            children: [statistics.certificazioniOggi, \" oggi\"]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 887,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 883,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 868,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 830,\n      columnNumber: 7\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 829,\n    columnNumber: 5\n  }, this);\n\n  // Componente barra di ricerca avanzata\n  const renderSearchAndFilters = () => /*#__PURE__*/_jsxDEV(Paper, {\n    sx: {\n      p: 2,\n      mb: 3\n    },\n    children: [/*#__PURE__*/_jsxDEV(Grid, {\n      container: true,\n      spacing: 2,\n      alignItems: \"center\",\n      children: [/*#__PURE__*/_jsxDEV(Grid, {\n        item: true,\n        xs: 12,\n        md: 4,\n        children: /*#__PURE__*/_jsxDEV(TextField, {\n          fullWidth: true,\n          placeholder: \"Cerca cavi, certificazioni, operatori...\",\n          value: searchTerm,\n          onChange: e => setSearchTerm(e.target.value),\n          InputProps: {\n            startAdornment: /*#__PURE__*/_jsxDEV(InputAdornment, {\n              position: \"start\",\n              children: /*#__PURE__*/_jsxDEV(SearchIcon, {}, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 911,\n                columnNumber: 19\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 910,\n              columnNumber: 17\n            }, this),\n            endAdornment: searchTerm && /*#__PURE__*/_jsxDEV(InputAdornment, {\n              position: \"end\",\n              children: /*#__PURE__*/_jsxDEV(IconButton, {\n                onClick: () => setSearchTerm(''),\n                size: \"small\",\n                children: /*#__PURE__*/_jsxDEV(ClearIcon, {}, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 917,\n                  columnNumber: 21\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 916,\n                columnNumber: 19\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 915,\n              columnNumber: 17\n            }, this)\n          }\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 903,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 902,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Grid, {\n        item: true,\n        xs: 12,\n        md: 2,\n        children: /*#__PURE__*/_jsxDEV(Button, {\n          fullWidth: true,\n          variant: \"outlined\",\n          startIcon: /*#__PURE__*/_jsxDEV(FilterIcon, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 929,\n            columnNumber: 24\n          }, this),\n          onClick: () => setAdvancedFiltersOpen(!advancedFiltersOpen),\n          color: Object.values(filters).some(f => f) ? 'primary' : 'inherit',\n          children: [\"Filtri \", Object.values(filters).filter(f => f).length > 0 && `(${Object.values(filters).filter(f => f).length})`]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 926,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 925,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Grid, {\n        item: true,\n        xs: 12,\n        md: 2,\n        children: /*#__PURE__*/_jsxDEV(Button, {\n          fullWidth: true,\n          variant: \"outlined\",\n          startIcon: bulkMode ? /*#__PURE__*/_jsxDEV(ClearIcon, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 941,\n            columnNumber: 35\n          }, this) : /*#__PURE__*/_jsxDEV(CheckIcon, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 941,\n            columnNumber: 51\n          }, this),\n          onClick: toggleBulkMode,\n          color: bulkMode ? 'secondary' : 'inherit',\n          disabled: activeTab === 0,\n          children: bulkMode ? 'Esci Selezione' : 'Selezione Multipla'\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 938,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 937,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Grid, {\n        item: true,\n        xs: 12,\n        md: 2,\n        children: /*#__PURE__*/_jsxDEV(Button, {\n          fullWidth: true,\n          variant: \"outlined\",\n          startIcon: /*#__PURE__*/_jsxDEV(ExportIcon, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 954,\n            columnNumber: 24\n          }, this),\n          onClick: handleExportAll,\n          disabled: activeTab === 0 || filteredCertificazioni.length === 0,\n          children: activeTab === 0 ? 'Export (solo certificazioni)' : 'Esporta Certificazioni'\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 951,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 950,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Grid, {\n        item: true,\n        xs: 12,\n        md: 2,\n        children: /*#__PURE__*/_jsxDEV(Button, {\n          fullWidth: true,\n          variant: \"contained\",\n          startIcon: /*#__PURE__*/_jsxDEV(AddIcon, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 966,\n            columnNumber: 24\n          }, this),\n          onClick: openCreateDialog,\n          children: \"Nuova Certificazione\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 963,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 962,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 901,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Collapse, {\n      in: advancedFiltersOpen,\n      children: [/*#__PURE__*/_jsxDEV(Divider, {\n        sx: {\n          my: 2\n        }\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 976,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Typography, {\n        variant: \"body2\",\n        color: \"text.secondary\",\n        sx: {\n          mb: 2\n        },\n        children: activeTab === 0 ? 'Filtri per Cavi' : 'Filtri per Certificazioni'\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 977,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Grid, {\n        container: true,\n        spacing: 2,\n        children: [activeTab === 0 && /*#__PURE__*/_jsxDEV(_Fragment, {\n          children: [/*#__PURE__*/_jsxDEV(Grid, {\n            item: true,\n            xs: 12,\n            md: 3,\n            children: /*#__PURE__*/_jsxDEV(FormControl, {\n              fullWidth: true,\n              size: \"small\",\n              children: [/*#__PURE__*/_jsxDEV(InputLabel, {\n                children: \"Stato Installazione\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 987,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(Select, {\n                value: filters.stato,\n                onChange: e => setFilters({\n                  ...filters,\n                  stato: e.target.value\n                }),\n                children: [/*#__PURE__*/_jsxDEV(MenuItem, {\n                  value: \"\",\n                  children: \"Tutti\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 992,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(MenuItem, {\n                  value: \"INSTALLATO\",\n                  children: \"Installato\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 993,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(MenuItem, {\n                  value: \"NON_INSTALLATO\",\n                  children: \"Non Installato\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 994,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(MenuItem, {\n                  value: \"IN_CORSO\",\n                  children: \"In Corso\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 995,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 988,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 986,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 985,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(Grid, {\n            item: true,\n            xs: 12,\n            md: 3,\n            children: /*#__PURE__*/_jsxDEV(FormControl, {\n              fullWidth: true,\n              size: \"small\",\n              children: [/*#__PURE__*/_jsxDEV(InputLabel, {\n                children: \"Tipologia\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1002,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(Select, {\n                value: filters.tipologia,\n                onChange: e => setFilters({\n                  ...filters,\n                  tipologia: e.target.value\n                }),\n                children: [/*#__PURE__*/_jsxDEV(MenuItem, {\n                  value: \"\",\n                  children: \"Tutte\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1007,\n                  columnNumber: 21\n                }, this), [...new Set(cavi.map(c => c.tipologia))].filter(Boolean).map(tip => /*#__PURE__*/_jsxDEV(MenuItem, {\n                  value: tip,\n                  children: tip\n                }, tip, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1009,\n                  columnNumber: 23\n                }, this))]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 1003,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 1001,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 1000,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(Grid, {\n            item: true,\n            xs: 12,\n            md: 3,\n            children: /*#__PURE__*/_jsxDEV(FormControl, {\n              fullWidth: true,\n              size: \"small\",\n              children: [/*#__PURE__*/_jsxDEV(InputLabel, {\n                children: \"Stato Certificazione\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1017,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(Select, {\n                value: filters.certificazione,\n                onChange: e => setFilters({\n                  ...filters,\n                  certificazione: e.target.value\n                }),\n                children: [/*#__PURE__*/_jsxDEV(MenuItem, {\n                  value: \"\",\n                  children: \"Tutti\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1022,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(MenuItem, {\n                  value: \"CERTIFICATO\",\n                  children: \"Certificato\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1023,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(MenuItem, {\n                  value: \"NON_CERTIFICATO\",\n                  children: \"Non Certificato\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1024,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 1018,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 1016,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 1015,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true), activeTab === 1 && /*#__PURE__*/_jsxDEV(_Fragment, {\n          children: [/*#__PURE__*/_jsxDEV(Grid, {\n            item: true,\n            xs: 12,\n            md: 3,\n            children: /*#__PURE__*/_jsxDEV(FormControl, {\n              fullWidth: true,\n              size: \"small\",\n              children: [/*#__PURE__*/_jsxDEV(InputLabel, {\n                children: \"Operatore\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1036,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(Select, {\n                value: filters.operatore,\n                onChange: e => setFilters({\n                  ...filters,\n                  operatore: e.target.value\n                }),\n                children: [/*#__PURE__*/_jsxDEV(MenuItem, {\n                  value: \"\",\n                  children: \"Tutti\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1041,\n                  columnNumber: 21\n                }, this), [...new Set(certificazioni.map(c => c.operatore))].filter(Boolean).map(op => /*#__PURE__*/_jsxDEV(MenuItem, {\n                  value: op,\n                  children: op\n                }, op, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1043,\n                  columnNumber: 23\n                }, this))]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 1037,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 1035,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 1034,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(Grid, {\n            item: true,\n            xs: 12,\n            md: 3,\n            children: /*#__PURE__*/_jsxDEV(FormControl, {\n              fullWidth: true,\n              size: \"small\",\n              children: [/*#__PURE__*/_jsxDEV(InputLabel, {\n                children: \"Risultato Test\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1051,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(Select, {\n                value: filters.risultatoTest,\n                onChange: e => setFilters({\n                  ...filters,\n                  risultatoTest: e.target.value\n                }),\n                children: [/*#__PURE__*/_jsxDEV(MenuItem, {\n                  value: \"\",\n                  children: \"Tutti\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1056,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(MenuItem, {\n                  value: \"CONFORME\",\n                  children: \"Conforme\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1057,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(MenuItem, {\n                  value: \"NON_CONFORME\",\n                  children: \"Non Conforme\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1058,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(MenuItem, {\n                  value: \"DA_VERIFICARE\",\n                  children: \"Da Verificare\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1059,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 1052,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 1050,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 1049,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(Grid, {\n            item: true,\n            xs: 12,\n            md: 3,\n            children: /*#__PURE__*/_jsxDEV(TextField, {\n              fullWidth: true,\n              size: \"small\",\n              label: \"Isolamento Min (M\\u03A9)\",\n              type: \"number\",\n              value: filters.valoreIsolamento,\n              onChange: e => setFilters({\n                ...filters,\n                valoreIsolamento: e.target.value\n              }),\n              placeholder: \"es. 500\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1065,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 1064,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(Grid, {\n            item: true,\n            xs: 12,\n            md: 3,\n            children: /*#__PURE__*/_jsxDEV(TextField, {\n              fullWidth: true,\n              size: \"small\",\n              label: \"Data Inizio\",\n              type: \"date\",\n              value: filters.dataInizio,\n              onChange: e => setFilters({\n                ...filters,\n                dataInizio: e.target.value\n              }),\n              InputLabelProps: {\n                shrink: true\n              }\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1077,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 1076,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(Grid, {\n            item: true,\n            xs: 12,\n            md: 3,\n            children: /*#__PURE__*/_jsxDEV(TextField, {\n              fullWidth: true,\n              size: \"small\",\n              label: \"Data Fine\",\n              type: \"date\",\n              value: filters.dataFine,\n              onChange: e => setFilters({\n                ...filters,\n                dataFine: e.target.value\n              }),\n              InputLabelProps: {\n                shrink: true\n              }\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1089,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 1088,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true), /*#__PURE__*/_jsxDEV(Grid, {\n          item: true,\n          xs: 12,\n          children: /*#__PURE__*/_jsxDEV(Stack, {\n            direction: \"row\",\n            spacing: 1,\n            justifyContent: \"flex-end\",\n            children: /*#__PURE__*/_jsxDEV(Button, {\n              variant: \"outlined\",\n              size: \"small\",\n              onClick: () => setFilters({\n                stato: '',\n                tipologia: '',\n                operatore: '',\n                dataInizio: '',\n                dataFine: '',\n                valoreIsolamento: '',\n                risultatoTest: '',\n                strumento: '',\n                certificazione: ''\n              }),\n              children: \"Pulisci Tutti i Filtri\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1104,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 1103,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 1102,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 981,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 975,\n      columnNumber: 7\n    }, this), bulkMode && bulkSelection.length > 0 && /*#__PURE__*/_jsxDEV(_Fragment, {\n      children: [/*#__PURE__*/_jsxDEV(Divider, {\n        sx: {\n          my: 2\n        }\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 1123,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(Stack, {\n        direction: \"row\",\n        spacing: 2,\n        alignItems: \"center\",\n        children: [/*#__PURE__*/_jsxDEV(Typography, {\n          variant: \"body2\",\n          children: [bulkSelection.length, \" elementi selezionati\"]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 1125,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(Button, {\n          size: \"small\",\n          variant: \"outlined\",\n          onClick: selectAllItems,\n          children: \"Seleziona Tutto\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 1128,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(Button, {\n          size: \"small\",\n          variant: \"outlined\",\n          onClick: clearSelection,\n          children: \"Deseleziona\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 1135,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(Button, {\n          size: \"small\",\n          variant: \"outlined\",\n          startIcon: /*#__PURE__*/_jsxDEV(ExportIcon, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 1145,\n            columnNumber: 26\n          }, this),\n          onClick: handleBulkExport,\n          children: \"Esporta Selezionati\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 1142,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(Button, {\n          size: \"small\",\n          variant: \"outlined\",\n          color: \"error\",\n          startIcon: /*#__PURE__*/_jsxDEV(DeleteIcon, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 1154,\n            columnNumber: 26\n          }, this),\n          onClick: handleBulkDelete,\n          children: \"Elimina Selezionati\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 1150,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 1124,\n        columnNumber: 11\n      }, this)]\n    }, void 0, true)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 900,\n    columnNumber: 5\n  }, this);\n\n  // Renderizza la tabella dei cavi\n  const renderCaviTable = () => {\n    const currentItems = getCurrentPageItems(filteredCavi);\n    if (filteredCavi.length === 0) {\n      return /*#__PURE__*/_jsxDEV(Alert, {\n        severity: \"info\",\n        children: searchTerm || filters.stato || filters.tipologia ? 'Nessun cavo trovato con i filtri applicati' : 'Nessun cavo disponibile'\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 1171,\n        columnNumber: 9\n      }, this);\n    }\n    return /*#__PURE__*/_jsxDEV(_Fragment, {\n      children: [/*#__PURE__*/_jsxDEV(TableContainer, {\n        component: Paper,\n        children: /*#__PURE__*/_jsxDEV(Table, {\n          size: \"small\",\n          children: [/*#__PURE__*/_jsxDEV(TableHead, {\n            children: /*#__PURE__*/_jsxDEV(TableRow, {\n              children: [/*#__PURE__*/_jsxDEV(TableCell, {\n                children: /*#__PURE__*/_jsxDEV(Stack, {\n                  direction: \"row\",\n                  alignItems: \"center\",\n                  spacing: 1,\n                  children: [/*#__PURE__*/_jsxDEV(Typography, {\n                    variant: \"body2\",\n                    fontWeight: \"bold\",\n                    children: \"ID Cavo\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1187,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(IconButton, {\n                    size: \"small\",\n                    onClick: () => {\n                      setSortBy('id_cavo');\n                      setSortOrder(sortOrder === 'asc' ? 'desc' : 'asc');\n                    },\n                    children: sortBy === 'id_cavo' ? sortOrder === 'asc' ? /*#__PURE__*/_jsxDEV(ExpandLessIcon, {}, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1192,\n                      columnNumber: 70\n                    }, this) : /*#__PURE__*/_jsxDEV(ExpandMoreIcon, {}, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1192,\n                      columnNumber: 91\n                    }, this) : /*#__PURE__*/_jsxDEV(ExpandMoreIcon, {}, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1192,\n                      columnNumber: 113\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1188,\n                    columnNumber: 21\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1186,\n                  columnNumber: 19\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1185,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n                children: \"Tipologia\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1196,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n                children: \"Sezione\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1197,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n                children: \"Partenza\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1198,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n                children: \"Arrivo\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1199,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n                children: \"Metri\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1200,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n                children: \"Stato\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1201,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n                children: \"Collegamenti\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1202,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n                children: \"Certificato\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1203,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n                children: \"Azioni\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1204,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 1184,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 1183,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(TableBody, {\n            children: currentItems.map(cavo => {\n              const isCertificato = isCavoCertificato(cavo.id_cavo);\n              const puoCertificare = puoEssereCertificato(cavo);\n              const messaggioErrore = !puoCertificare ? getMessaggioErroreCertificazione(cavo) : '';\n              return /*#__PURE__*/_jsxDEV(TableRow, {\n                children: [/*#__PURE__*/_jsxDEV(TableCell, {\n                  children: /*#__PURE__*/_jsxDEV(Typography, {\n                    variant: \"body2\",\n                    fontWeight: \"medium\",\n                    children: cavo.id_cavo\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1216,\n                    columnNumber: 23\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1215,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n                  children: cavo.tipologia\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1220,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n                  children: cavo.sezione\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1221,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n                  children: cavo.ubicazione_partenza\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1222,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n                  children: cavo.ubicazione_arrivo\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1223,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n                  children: [cavo.metratura_reale || cavo.metri_teorici, \" m\"]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1224,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n                  children: /*#__PURE__*/_jsxDEV(Chip, {\n                    size: \"small\",\n                    label: cavo.stato_installazione,\n                    color: cavo.stato_installazione === 'INSTALLATO' ? 'success' : 'default'\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1226,\n                    columnNumber: 23\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1225,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n                  children: (() => {\n                    const collegamenti = cavo.collegamenti || 0;\n                    const statoCollegamento = collegamenti === 0 ? 'Non collegato' : collegamenti === 1 ? 'Solo partenza' : collegamenti === 2 ? 'Solo arrivo' : collegamenti === 3 ? 'Completo' : 'Sconosciuto';\n                    const colore = collegamenti === 3 ? 'success' : collegamenti === 0 ? 'error' : 'warning';\n                    return /*#__PURE__*/_jsxDEV(Tooltip, {\n                      title: `Partenza: ${cavo.responsabile_partenza || 'Non collegato'} | Arrivo: ${cavo.responsabile_arrivo || 'Non collegato'}`,\n                      children: /*#__PURE__*/_jsxDEV(Chip, {\n                        size: \"small\",\n                        label: statoCollegamento,\n                        color: colore,\n                        icon: collegamenti === 3 ? /*#__PURE__*/_jsxDEV(CheckIcon, {}, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 1249,\n                          columnNumber: 58\n                        }, this) : /*#__PURE__*/_jsxDEV(WarningIcon, {}, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 1249,\n                          columnNumber: 74\n                        }, this)\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 1245,\n                        columnNumber: 29\n                      }, this)\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1244,\n                      columnNumber: 27\n                    }, this);\n                  })()\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1232,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n                  children: isCertificato ? /*#__PURE__*/_jsxDEV(Chip, {\n                    size: \"small\",\n                    icon: /*#__PURE__*/_jsxDEV(CheckIcon, {}, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1259,\n                      columnNumber: 33\n                    }, this),\n                    label: \"Certificato\",\n                    color: \"success\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1257,\n                    columnNumber: 25\n                  }, this) : /*#__PURE__*/_jsxDEV(Chip, {\n                    size: \"small\",\n                    icon: /*#__PURE__*/_jsxDEV(WarningIcon, {}, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1266,\n                      columnNumber: 33\n                    }, this),\n                    label: \"Non certificato\",\n                    color: \"warning\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1264,\n                    columnNumber: 25\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1255,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n                  children: isCertificato ? /*#__PURE__*/_jsxDEV(Tooltip, {\n                    title: \"Cavo gi\\xE0 certificato\",\n                    children: /*#__PURE__*/_jsxDEV(Chip, {\n                      icon: /*#__PURE__*/_jsxDEV(CheckIcon, {}, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 1276,\n                        columnNumber: 35\n                      }, this),\n                      label: \"Certificato\",\n                      color: \"success\",\n                      size: \"small\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1275,\n                      columnNumber: 27\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1274,\n                    columnNumber: 25\n                  }, this) : puoCertificare ? /*#__PURE__*/_jsxDEV(Tooltip, {\n                    title: \"Crea certificazione per questo cavo\",\n                    children: /*#__PURE__*/_jsxDEV(IconButton, {\n                      size: \"small\",\n                      onClick: () => openCreateDialog(cavo),\n                      color: \"primary\",\n                      children: /*#__PURE__*/_jsxDEV(AddIcon, {}, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 1289,\n                        columnNumber: 29\n                      }, this)\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1284,\n                      columnNumber: 27\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1283,\n                    columnNumber: 25\n                  }, this) : /*#__PURE__*/_jsxDEV(Tooltip, {\n                    title: messaggioErrore,\n                    children: /*#__PURE__*/_jsxDEV(\"span\", {\n                      children: /*#__PURE__*/_jsxDEV(IconButton, {\n                        size: \"small\",\n                        disabled: true,\n                        onClick: () => showSnackbar(messaggioErrore, 'warning'),\n                        children: /*#__PURE__*/_jsxDEV(BlockIcon, {}, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 1300,\n                          columnNumber: 31\n                        }, this)\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 1295,\n                        columnNumber: 29\n                      }, this)\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1294,\n                      columnNumber: 27\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1293,\n                    columnNumber: 25\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1272,\n                  columnNumber: 21\n                }, this)]\n              }, cavo.id_cavo, true, {\n                fileName: _jsxFileName,\n                lineNumber: 1214,\n                columnNumber: 19\n              }, this);\n            })\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 1207,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 1182,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 1181,\n        columnNumber: 9\n      }, this), getTotalPages(filteredCavi) > 1 && /*#__PURE__*/_jsxDEV(Box, {\n        sx: {\n          display: 'flex',\n          justifyContent: 'center',\n          mt: 2\n        },\n        children: /*#__PURE__*/_jsxDEV(Pagination, {\n          count: getTotalPages(filteredCavi),\n          page: currentPage,\n          onChange: (event, value) => setCurrentPage(value),\n          color: \"primary\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 1315,\n          columnNumber: 13\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 1314,\n        columnNumber: 11\n      }, this)]\n    }, void 0, true);\n  };\n\n  // Renderizza la tabella delle certificazioni\n  const renderCertificazioniTable = () => {\n    const currentItems = getCurrentPageItems(filteredCertificazioni);\n    if (filteredCertificazioni.length === 0) {\n      return /*#__PURE__*/_jsxDEV(Alert, {\n        severity: \"info\",\n        children: searchTerm || filters.operatore ? 'Nessuna certificazione trovata con i filtri applicati' : 'Nessuna certificazione disponibile'\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 1333,\n        columnNumber: 9\n      }, this);\n    }\n    return /*#__PURE__*/_jsxDEV(_Fragment, {\n      children: [/*#__PURE__*/_jsxDEV(TableContainer, {\n        component: Paper,\n        children: /*#__PURE__*/_jsxDEV(Table, {\n          size: \"small\",\n          children: [/*#__PURE__*/_jsxDEV(TableHead, {\n            children: /*#__PURE__*/_jsxDEV(TableRow, {\n              children: [bulkMode && /*#__PURE__*/_jsxDEV(TableCell, {\n                padding: \"checkbox\",\n                children: /*#__PURE__*/_jsxDEV(IconButton, {\n                  size: \"small\",\n                  onClick: bulkSelection.length === filteredCertificazioni.length ? clearSelection : selectAllItems,\n                  children: bulkSelection.length === filteredCertificazioni.length ? /*#__PURE__*/_jsxDEV(ClearIcon, {}, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1353,\n                    columnNumber: 81\n                  }, this) : /*#__PURE__*/_jsxDEV(CheckIcon, {}, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1353,\n                    columnNumber: 97\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1349,\n                  columnNumber: 21\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1348,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n                children: /*#__PURE__*/_jsxDEV(Stack, {\n                  direction: \"row\",\n                  alignItems: \"center\",\n                  spacing: 1,\n                  children: [/*#__PURE__*/_jsxDEV(Typography, {\n                    variant: \"body2\",\n                    fontWeight: \"bold\",\n                    children: \"N\\xB0 Certificato\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1359,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(IconButton, {\n                    size: \"small\",\n                    onClick: () => {\n                      setSortBy('numero_certificato');\n                      setSortOrder(sortOrder === 'asc' ? 'desc' : 'asc');\n                    },\n                    children: sortBy === 'numero_certificato' ? sortOrder === 'asc' ? /*#__PURE__*/_jsxDEV(ExpandLessIcon, {}, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1364,\n                      columnNumber: 81\n                    }, this) : /*#__PURE__*/_jsxDEV(ExpandMoreIcon, {}, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1364,\n                      columnNumber: 102\n                    }, this) : /*#__PURE__*/_jsxDEV(ExpandMoreIcon, {}, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1364,\n                      columnNumber: 124\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1360,\n                    columnNumber: 21\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1358,\n                  columnNumber: 19\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1357,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n                children: \"ID Cavo\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1368,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n                children: /*#__PURE__*/_jsxDEV(Stack, {\n                  direction: \"row\",\n                  alignItems: \"center\",\n                  spacing: 1,\n                  children: [/*#__PURE__*/_jsxDEV(Typography, {\n                    variant: \"body2\",\n                    fontWeight: \"bold\",\n                    children: \"Data\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1371,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(IconButton, {\n                    size: \"small\",\n                    onClick: () => {\n                      setSortBy('data_certificazione');\n                      setSortOrder(sortOrder === 'asc' ? 'desc' : 'asc');\n                    },\n                    children: sortBy === 'data_certificazione' ? sortOrder === 'asc' ? /*#__PURE__*/_jsxDEV(ExpandLessIcon, {}, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1376,\n                      columnNumber: 82\n                    }, this) : /*#__PURE__*/_jsxDEV(ExpandMoreIcon, {}, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1376,\n                      columnNumber: 103\n                    }, this) : /*#__PURE__*/_jsxDEV(ExpandMoreIcon, {}, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1376,\n                      columnNumber: 125\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1372,\n                    columnNumber: 21\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1370,\n                  columnNumber: 19\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1369,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n                children: \"Operatore\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1380,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n                children: \"Strumento\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1381,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n                children: \"Lunghezza\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1382,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n                children: \"Isolamento\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1383,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n                children: \"Risultato\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1384,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n                children: \"Azioni\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1385,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 1346,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 1345,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(TableBody, {\n            children: currentItems.map(cert => /*#__PURE__*/_jsxDEV(TableRow, {\n              selected: bulkSelection.includes(cert.id_certificazione),\n              hover: true,\n              children: [bulkMode && /*#__PURE__*/_jsxDEV(TableCell, {\n                padding: \"checkbox\",\n                children: /*#__PURE__*/_jsxDEV(IconButton, {\n                  size: \"small\",\n                  onClick: () => toggleItemSelection(cert.id_certificazione),\n                  color: bulkSelection.includes(cert.id_certificazione) ? 'primary' : 'default',\n                  children: bulkSelection.includes(cert.id_certificazione) ? /*#__PURE__*/_jsxDEV(CheckIcon, {}, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1402,\n                    columnNumber: 75\n                  }, this) : /*#__PURE__*/_jsxDEV(AddIcon, {}, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1402,\n                    columnNumber: 91\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1397,\n                  columnNumber: 23\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1396,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n                children: /*#__PURE__*/_jsxDEV(Typography, {\n                  variant: \"body2\",\n                  fontWeight: \"medium\",\n                  children: cert.numero_certificato\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1407,\n                  columnNumber: 21\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1406,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n                children: /*#__PURE__*/_jsxDEV(Chip, {\n                  size: \"small\",\n                  label: cert.id_cavo,\n                  variant: \"outlined\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1412,\n                  columnNumber: 21\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1411,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n                children: new Date(cert.data_certificazione).toLocaleDateString()\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1414,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n                children: /*#__PURE__*/_jsxDEV(Stack, {\n                  direction: \"row\",\n                  alignItems: \"center\",\n                  spacing: 1,\n                  children: [/*#__PURE__*/_jsxDEV(PersonIcon, {\n                    fontSize: \"small\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1417,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                    variant: \"body2\",\n                    children: cert.operatore || cert.id_operatore\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1418,\n                    columnNumber: 23\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1416,\n                  columnNumber: 21\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1415,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n                children: /*#__PURE__*/_jsxDEV(Typography, {\n                  variant: \"body2\",\n                  children: cert.id_strumento ? (() => {\n                    const strumento = strumenti.find(s => s.id_strumento === cert.id_strumento);\n                    return strumento ? `${strumento.nome} - ${strumento.marca}` : 'Strumento non trovato';\n                  })() : cert.strumento_utilizzato || 'N/A'\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1422,\n                  columnNumber: 21\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1421,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n                children: /*#__PURE__*/_jsxDEV(Typography, {\n                  variant: \"body2\",\n                  children: [cert.lunghezza_misurata, \" m\"]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1433,\n                  columnNumber: 21\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1432,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n                children: /*#__PURE__*/_jsxDEV(Chip, {\n                  size: \"small\",\n                  label: `${cert.valore_isolamento} MΩ`,\n                  color: parseFloat(cert.valore_isolamento) >= 500 ? 'success' : 'warning',\n                  icon: parseFloat(cert.valore_isolamento) >= 500 ? /*#__PURE__*/_jsxDEV(CheckIcon, {}, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1440,\n                    columnNumber: 73\n                  }, this) : /*#__PURE__*/_jsxDEV(WarningIcon, {}, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1440,\n                    columnNumber: 89\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1436,\n                  columnNumber: 21\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1435,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n                children: /*#__PURE__*/_jsxDEV(Chip, {\n                  size: \"small\",\n                  label: cert.risultato_finale || 'CONFORME',\n                  color: cert.risultato_finale === 'CONFORME' ? 'success' : cert.risultato_finale === 'NON_CONFORME' ? 'error' : 'warning'\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1444,\n                  columnNumber: 21\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1443,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n                children: /*#__PURE__*/_jsxDEV(Stack, {\n                  direction: \"row\",\n                  spacing: 0.5,\n                  children: [/*#__PURE__*/_jsxDEV(Tooltip, {\n                    title: \"Visualizza dettagli\",\n                    children: /*#__PURE__*/_jsxDEV(IconButton, {\n                      size: \"small\",\n                      onClick: () => {\n                        setSelectedItem(cert);\n                        setDialogType('view');\n                        setOpenDialog(true);\n                      },\n                      children: /*#__PURE__*/_jsxDEV(ViewIcon, {}, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 1461,\n                        columnNumber: 27\n                      }, this)\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1453,\n                      columnNumber: 25\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1452,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(Tooltip, {\n                    title: \"Genera PDF\",\n                    children: /*#__PURE__*/_jsxDEV(IconButton, {\n                      size: \"small\",\n                      onClick: () => handleGeneratePdf(cert),\n                      disabled: operationInProgress,\n                      children: /*#__PURE__*/_jsxDEV(PdfIcon, {}, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 1470,\n                        columnNumber: 27\n                      }, this)\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1465,\n                      columnNumber: 25\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1464,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(Tooltip, {\n                    title: \"Elimina\",\n                    children: /*#__PURE__*/_jsxDEV(IconButton, {\n                      size: \"small\",\n                      color: \"error\",\n                      onClick: () => handleDeleteCertificazione(cert),\n                      disabled: operationInProgress,\n                      children: /*#__PURE__*/_jsxDEV(DeleteIcon, {}, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 1480,\n                        columnNumber: 27\n                      }, this)\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1474,\n                      columnNumber: 25\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1473,\n                    columnNumber: 23\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1451,\n                  columnNumber: 21\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1450,\n                columnNumber: 19\n              }, this)]\n            }, cert.id_certificazione, true, {\n              fileName: _jsxFileName,\n              lineNumber: 1390,\n              columnNumber: 17\n            }, this))\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 1388,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 1344,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 1343,\n        columnNumber: 9\n      }, this), getTotalPages(filteredCertificazioni) > 1 && /*#__PURE__*/_jsxDEV(Box, {\n        sx: {\n          display: 'flex',\n          justifyContent: 'center',\n          mt: 2\n        },\n        children: /*#__PURE__*/_jsxDEV(Pagination, {\n          count: getTotalPages(filteredCertificazioni),\n          page: currentPage,\n          onChange: (event, value) => setCurrentPage(value),\n          color: \"primary\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 1493,\n          columnNumber: 13\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 1492,\n        columnNumber: 11\n      }, this)]\n    }, void 0, true);\n  };\n\n  // Renderizza il dialog per creare/modificare certificazione\n  const renderCertificazioneDialog = () => {\n    if (dialogType !== 'create' && dialogType !== 'edit') return null;\n    return /*#__PURE__*/_jsxDEV(Dialog, {\n      open: openDialog,\n      onClose: closeDialog,\n      maxWidth: \"md\",\n      fullWidth: true,\n      children: [/*#__PURE__*/_jsxDEV(DialogTitle, {\n        children: dialogType === 'create' ? 'Nuova Certificazione' : 'Modifica Certificazione'\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 1511,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(DialogContent, {\n        children: /*#__PURE__*/_jsxDEV(Grid, {\n          container: true,\n          spacing: 2,\n          sx: {\n            mt: 1\n          },\n          children: [/*#__PURE__*/_jsxDEV(Grid, {\n            item: true,\n            xs: 12,\n            md: 6,\n            children: /*#__PURE__*/_jsxDEV(Autocomplete, {\n              options: cavi.filter(cavo => {\n                // Mostra solo cavi che possono essere certificati o quello già selezionato\n                const isSelected = cavo.id_cavo === formData.id_cavo;\n                const isNotCertified = !certificazioni.some(cert => cert.id_cavo === cavo.id_cavo);\n                const canBeCertified = puoEssereCertificato(cavo);\n                return isSelected || isNotCertified && canBeCertified;\n              }),\n              getOptionLabel: option => `${option.id_cavo} - ${option.tipologia}`,\n              value: cavi.find(c => c.id_cavo === formData.id_cavo) || null,\n              onChange: (event, newValue) => {\n                if (newValue) {\n                  handleCavoSelect(newValue);\n                } else {\n                  setFormData(prev => ({\n                    ...prev,\n                    id_cavo: '',\n                    lunghezza_misurata: ''\n                  }));\n                }\n              },\n              renderInput: params => /*#__PURE__*/_jsxDEV(TextField, {\n                ...params,\n                label: \"Cavo *\",\n                placeholder: \"Seleziona un cavo posato\",\n                required: true,\n                helperText: \"Solo cavi posati/installati (il collegamento pu\\xF2 essere gestito al momento)\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1536,\n                columnNumber: 19\n              }, this),\n              renderOption: (props, option) => {\n                const collegamenti = option.collegamenti || 0;\n                const isCollegato = collegamenti === 3;\n                return /*#__PURE__*/_jsxDEV(Box, {\n                  component: \"li\",\n                  ...props,\n                  children: /*#__PURE__*/_jsxDEV(Box, {\n                    sx: {\n                      width: '100%'\n                    },\n                    children: /*#__PURE__*/_jsxDEV(Stack, {\n                      direction: \"row\",\n                      justifyContent: \"space-between\",\n                      alignItems: \"center\",\n                      children: [/*#__PURE__*/_jsxDEV(Box, {\n                        children: [/*#__PURE__*/_jsxDEV(Typography, {\n                          variant: \"body2\",\n                          fontWeight: \"medium\",\n                          children: option.id_cavo\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 1553,\n                          columnNumber: 29\n                        }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                          variant: \"caption\",\n                          color: \"text.secondary\",\n                          children: [option.tipologia, \" - \", option.ubicazione_partenza, \" \\u2192 \", option.ubicazione_arrivo]\n                        }, void 0, true, {\n                          fileName: _jsxFileName,\n                          lineNumber: 1556,\n                          columnNumber: 29\n                        }, this)]\n                      }, void 0, true, {\n                        fileName: _jsxFileName,\n                        lineNumber: 1552,\n                        columnNumber: 27\n                      }, this), /*#__PURE__*/_jsxDEV(Stack, {\n                        direction: \"row\",\n                        spacing: 1,\n                        children: [/*#__PURE__*/_jsxDEV(Chip, {\n                          size: \"small\",\n                          label: option.stato_installazione,\n                          color: option.stato_installazione === 'INSTALLATO' ? 'success' : 'default'\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 1561,\n                          columnNumber: 29\n                        }, this), /*#__PURE__*/_jsxDEV(Chip, {\n                          size: \"small\",\n                          label: isCollegato ? 'Collegato' : 'Da collegare',\n                          color: isCollegato ? 'success' : 'warning',\n                          icon: isCollegato ? /*#__PURE__*/_jsxDEV(CheckIcon, {}, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 1570,\n                            columnNumber: 51\n                          }, this) : /*#__PURE__*/_jsxDEV(WarningIcon, {}, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 1570,\n                            columnNumber: 67\n                          }, this)\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 1566,\n                          columnNumber: 29\n                        }, this)]\n                      }, void 0, true, {\n                        fileName: _jsxFileName,\n                        lineNumber: 1560,\n                        columnNumber: 27\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1551,\n                      columnNumber: 25\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1550,\n                    columnNumber: 23\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1549,\n                  columnNumber: 21\n                }, this);\n              }\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1517,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 1516,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Grid, {\n            item: true,\n            xs: 12,\n            md: 6,\n            children: /*#__PURE__*/_jsxDEV(TextField, {\n              fullWidth: true,\n              label: \"Operatore *\",\n              value: formData.id_operatore,\n              onChange: e => handleFormChange('id_operatore', e.target.value),\n              required: true\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1582,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 1581,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Grid, {\n            item: true,\n            xs: 12,\n            md: 6,\n            children: /*#__PURE__*/_jsxDEV(FormControl, {\n              fullWidth: true,\n              required: true,\n              children: [/*#__PURE__*/_jsxDEV(InputLabel, {\n                children: \"Strumento *\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1593,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(Select, {\n                value: formData.id_strumento,\n                onChange: e => handleFormChange('id_strumento', e.target.value),\n                label: \"Strumento *\",\n                children: strumenti.map(strumento => /*#__PURE__*/_jsxDEV(MenuItem, {\n                  value: strumento.id_strumento,\n                  children: [strumento.nome, \" - \", strumento.marca, \" \", strumento.modello, \" (S/N: \", strumento.numero_serie, \")\"]\n                }, strumento.id_strumento, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1600,\n                  columnNumber: 21\n                }, this))\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1594,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 1592,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 1591,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Grid, {\n            item: true,\n            xs: 12,\n            md: 6,\n            children: /*#__PURE__*/_jsxDEV(TextField, {\n              fullWidth: true,\n              label: \"Lunghezza Misurata (m) *\",\n              type: \"number\",\n              value: formData.lunghezza_misurata,\n              onChange: e => handleFormChange('lunghezza_misurata', e.target.value),\n              required: true\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1609,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 1608,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Grid, {\n            item: true,\n            xs: 12,\n            md: 4,\n            children: /*#__PURE__*/_jsxDEV(FormControl, {\n              fullWidth: true,\n              children: [/*#__PURE__*/_jsxDEV(InputLabel, {\n                children: \"Continuit\\xE0\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1621,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(Select, {\n                value: formData.valore_continuita,\n                onChange: e => handleFormChange('valore_continuita', e.target.value),\n                label: \"Continuit\\xE0\",\n                children: [/*#__PURE__*/_jsxDEV(MenuItem, {\n                  value: \"OK\",\n                  children: \"OK\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1627,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(MenuItem, {\n                  value: \"NOK\",\n                  children: \"NOK\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1628,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 1622,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 1620,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 1619,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Grid, {\n            item: true,\n            xs: 12,\n            md: 4,\n            children: /*#__PURE__*/_jsxDEV(TextField, {\n              fullWidth: true,\n              label: \"Isolamento (M\\u03A9) *\",\n              type: \"number\",\n              value: formData.valore_isolamento,\n              onChange: e => handleFormChange('valore_isolamento', e.target.value),\n              required: true,\n              helperText: \"Valore minimo consigliato: 500 M\\u03A9\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1634,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 1633,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Grid, {\n            item: true,\n            xs: 12,\n            md: 4,\n            children: /*#__PURE__*/_jsxDEV(FormControl, {\n              fullWidth: true,\n              children: [/*#__PURE__*/_jsxDEV(InputLabel, {\n                children: \"Resistenza\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1647,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(Select, {\n                value: formData.valore_resistenza,\n                onChange: e => handleFormChange('valore_resistenza', e.target.value),\n                label: \"Resistenza\",\n                children: [/*#__PURE__*/_jsxDEV(MenuItem, {\n                  value: \"OK\",\n                  children: \"OK\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1653,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(MenuItem, {\n                  value: \"NOK\",\n                  children: \"NOK\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1654,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 1648,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 1646,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 1645,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Grid, {\n            item: true,\n            xs: 12,\n            children: /*#__PURE__*/_jsxDEV(Divider, {\n              sx: {\n                my: 2\n              },\n              children: /*#__PURE__*/_jsxDEV(Typography, {\n                variant: \"body2\",\n                color: \"text.secondary\",\n                children: \"Stato Collegamenti Cavo\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1662,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1661,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 1660,\n            columnNumber: 13\n          }, this), formData.id_cavo && (() => {\n            const cavo = cavi.find(c => c.id_cavo === formData.id_cavo);\n            if (!cavo) return null;\n            const collegamenti = cavo.collegamenti || 0;\n            const isCollegato = collegamenti === 3;\n            return /*#__PURE__*/_jsxDEV(Grid, {\n              item: true,\n              xs: 12,\n              children: /*#__PURE__*/_jsxDEV(Paper, {\n                sx: {\n                  p: 2,\n                  bgcolor: isCollegato ? 'success.light' : 'warning.light'\n                },\n                children: /*#__PURE__*/_jsxDEV(Stack, {\n                  direction: \"row\",\n                  alignItems: \"center\",\n                  spacing: 2,\n                  children: [isCollegato ? /*#__PURE__*/_jsxDEV(CheckIcon, {\n                    color: \"success\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1679,\n                    columnNumber: 38\n                  }, this) : /*#__PURE__*/_jsxDEV(WarningIcon, {\n                    color: \"warning\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1679,\n                    columnNumber: 70\n                  }, this), /*#__PURE__*/_jsxDEV(Box, {\n                    children: [/*#__PURE__*/_jsxDEV(Typography, {\n                      variant: \"body2\",\n                      fontWeight: \"bold\",\n                      children: isCollegato ? 'Cavo Completamente Collegato' : 'Cavo Non Completamente Collegato'\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1681,\n                      columnNumber: 25\n                    }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                      variant: \"caption\",\n                      children: [\"Stato: \", collegamenti === 0 ? 'Non collegato' : collegamenti === 1 ? 'Solo partenza collegata' : collegamenti === 2 ? 'Solo arrivo collegato' : collegamenti === 3 ? 'Completamente collegato' : 'Stato sconosciuto']\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1684,\n                      columnNumber: 25\n                    }, this), !isCollegato && /*#__PURE__*/_jsxDEV(Typography, {\n                      variant: \"caption\",\n                      display: \"block\",\n                      sx: {\n                        mt: 1\n                      },\n                      children: \"\\u26A0\\uFE0F Il cavo pu\\xF2 essere certificato ma ricorda di completare i collegamenti prima della messa in servizio\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1692,\n                      columnNumber: 27\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1680,\n                    columnNumber: 23\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1678,\n                  columnNumber: 21\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1677,\n                columnNumber: 19\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1676,\n              columnNumber: 17\n            }, this);\n          })(), /*#__PURE__*/_jsxDEV(Grid, {\n            item: true,\n            xs: 12,\n            children: /*#__PURE__*/_jsxDEV(Divider, {\n              sx: {\n                my: 2\n              },\n              children: /*#__PURE__*/_jsxDEV(Typography, {\n                variant: \"body2\",\n                color: \"text.secondary\",\n                children: \"Parametri Ambientali e Test Avanzati\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1706,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1705,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 1704,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Grid, {\n            item: true,\n            xs: 12,\n            md: 3,\n            children: /*#__PURE__*/_jsxDEV(TextField, {\n              fullWidth: true,\n              label: \"Temperatura Ambiente (\\xB0C)\",\n              type: \"number\",\n              value: formData.temperatura_ambiente,\n              onChange: e => handleFormChange('temperatura_ambiente', e.target.value),\n              helperText: \"Temperatura durante il test\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1713,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 1712,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Grid, {\n            item: true,\n            xs: 12,\n            md: 3,\n            children: /*#__PURE__*/_jsxDEV(TextField, {\n              fullWidth: true,\n              label: \"Umidit\\xE0 (%)\",\n              type: \"number\",\n              value: formData.umidita,\n              onChange: e => handleFormChange('umidita', e.target.value),\n              helperText: \"Umidit\\xE0 relativa\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1724,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 1723,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Grid, {\n            item: true,\n            xs: 12,\n            md: 3,\n            children: /*#__PURE__*/_jsxDEV(TextField, {\n              fullWidth: true,\n              label: \"Tensione di Prova (V)\",\n              type: \"number\",\n              value: formData.tensione_prova,\n              onChange: e => handleFormChange('tensione_prova', e.target.value),\n              helperText: \"Tensione applicata per il test\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1735,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 1734,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Grid, {\n            item: true,\n            xs: 12,\n            md: 3,\n            children: /*#__PURE__*/_jsxDEV(TextField, {\n              fullWidth: true,\n              label: \"Durata Prova (min)\",\n              type: \"number\",\n              value: formData.durata_prova,\n              onChange: e => handleFormChange('durata_prova', e.target.value),\n              helperText: \"Durata del test in minuti\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1746,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 1745,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Grid, {\n            item: true,\n            xs: 12,\n            md: 6,\n            children: /*#__PURE__*/_jsxDEV(FormControl, {\n              fullWidth: true,\n              children: [/*#__PURE__*/_jsxDEV(InputLabel, {\n                children: \"Risultato Finale\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1758,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(Select, {\n                value: formData.risultato_finale,\n                onChange: e => handleFormChange('risultato_finale', e.target.value),\n                label: \"Risultato Finale\",\n                children: [/*#__PURE__*/_jsxDEV(MenuItem, {\n                  value: \"CONFORME\",\n                  children: /*#__PURE__*/_jsxDEV(Stack, {\n                    direction: \"row\",\n                    alignItems: \"center\",\n                    spacing: 1,\n                    children: [/*#__PURE__*/_jsxDEV(CheckIcon, {\n                      color: \"success\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1766,\n                      columnNumber: 23\n                    }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                      children: \"Conforme\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1767,\n                      columnNumber: 23\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1765,\n                    columnNumber: 21\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1764,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(MenuItem, {\n                  value: \"NON_CONFORME\",\n                  children: /*#__PURE__*/_jsxDEV(Stack, {\n                    direction: \"row\",\n                    alignItems: \"center\",\n                    spacing: 1,\n                    children: [/*#__PURE__*/_jsxDEV(ErrorIcon, {\n                      color: \"error\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1772,\n                      columnNumber: 23\n                    }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                      children: \"Non Conforme\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1773,\n                      columnNumber: 23\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1771,\n                    columnNumber: 21\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1770,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(MenuItem, {\n                  value: \"DA_VERIFICARE\",\n                  children: /*#__PURE__*/_jsxDEV(Stack, {\n                    direction: \"row\",\n                    alignItems: \"center\",\n                    spacing: 1,\n                    children: [/*#__PURE__*/_jsxDEV(WarningIcon, {\n                      color: \"warning\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1778,\n                      columnNumber: 23\n                    }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                      children: \"Da Verificare\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1779,\n                      columnNumber: 23\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1777,\n                    columnNumber: 21\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1776,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 1759,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 1757,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 1756,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Grid, {\n            item: true,\n            xs: 12,\n            children: /*#__PURE__*/_jsxDEV(TextField, {\n              fullWidth: true,\n              label: \"Note\",\n              multiline: true,\n              rows: 3,\n              value: formData.note,\n              onChange: e => handleFormChange('note', e.target.value),\n              placeholder: \"Inserisci eventuali note, osservazioni o anomalie riscontrate durante il test...\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1787,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 1786,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 1515,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 1514,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(DialogActions, {\n        children: [/*#__PURE__*/_jsxDEV(Button, {\n          onClick: closeDialog,\n          children: \"Annulla\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 1800,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Button, {\n          onClick: handleCreateCertificazione,\n          variant: \"contained\",\n          disabled: loading || !formData.id_cavo || !formData.id_operatore || !formData.id_strumento || !formData.valore_isolamento,\n          startIcon: loading ? /*#__PURE__*/_jsxDEV(CircularProgress, {\n            size: 20\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 1805,\n            columnNumber: 34\n          }, this) : /*#__PURE__*/_jsxDEV(SaveIcon, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 1805,\n            columnNumber: 67\n          }, this),\n          children: dialogType === 'create' ? 'Crea Certificazione' : 'Salva Modifiche'\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 1801,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 1799,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 1510,\n      columnNumber: 7\n    }, this);\n  };\n\n  // Renderizza il dialog di visualizzazione dettagli\n  const renderViewDialog = () => {\n    if (dialogType !== 'view' || !selectedItem) return null;\n    return /*#__PURE__*/_jsxDEV(Dialog, {\n      open: openDialog,\n      onClose: closeDialog,\n      maxWidth: \"md\",\n      fullWidth: true,\n      children: [/*#__PURE__*/_jsxDEV(DialogTitle, {\n        children: [\"Dettagli Certificazione - \", selectedItem.numero_certificato]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 1820,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(DialogContent, {\n        children: /*#__PURE__*/_jsxDEV(Grid, {\n          container: true,\n          spacing: 2,\n          sx: {\n            mt: 1\n          },\n          children: [/*#__PURE__*/_jsxDEV(Grid, {\n            item: true,\n            xs: 12,\n            md: 6,\n            children: /*#__PURE__*/_jsxDEV(Card, {\n              variant: \"outlined\",\n              children: /*#__PURE__*/_jsxDEV(CardContent, {\n                children: [/*#__PURE__*/_jsxDEV(Typography, {\n                  variant: \"h6\",\n                  gutterBottom: true,\n                  children: \"Informazioni Cavo\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1828,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                  variant: \"body2\",\n                  color: \"text.secondary\",\n                  children: [\"ID Cavo: \", /*#__PURE__*/_jsxDEV(\"strong\", {\n                    children: selectedItem.id_cavo\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1832,\n                    columnNumber: 30\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1831,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                  variant: \"body2\",\n                  color: \"text.secondary\",\n                  children: [\"Lunghezza Misurata: \", /*#__PURE__*/_jsxDEV(\"strong\", {\n                    children: [selectedItem.lunghezza_misurata, \" m\"]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1835,\n                    columnNumber: 41\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1834,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 1827,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1826,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 1825,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Grid, {\n            item: true,\n            xs: 12,\n            md: 6,\n            children: /*#__PURE__*/_jsxDEV(Card, {\n              variant: \"outlined\",\n              children: /*#__PURE__*/_jsxDEV(CardContent, {\n                children: [/*#__PURE__*/_jsxDEV(Typography, {\n                  variant: \"h6\",\n                  gutterBottom: true,\n                  children: \"Informazioni Certificazione\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1844,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                  variant: \"body2\",\n                  color: \"text.secondary\",\n                  children: [\"Numero: \", /*#__PURE__*/_jsxDEV(\"strong\", {\n                    children: selectedItem.numero_certificato\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1848,\n                    columnNumber: 29\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1847,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                  variant: \"body2\",\n                  color: \"text.secondary\",\n                  children: [\"Data: \", /*#__PURE__*/_jsxDEV(\"strong\", {\n                    children: new Date(selectedItem.data_certificazione).toLocaleDateString()\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1851,\n                    columnNumber: 27\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1850,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                  variant: \"body2\",\n                  color: \"text.secondary\",\n                  children: [\"Operatore: \", /*#__PURE__*/_jsxDEV(\"strong\", {\n                    children: selectedItem.operatore || selectedItem.id_operatore\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1854,\n                    columnNumber: 32\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1853,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 1843,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1842,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 1841,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Grid, {\n            item: true,\n            xs: 12,\n            children: /*#__PURE__*/_jsxDEV(Card, {\n              variant: \"outlined\",\n              children: /*#__PURE__*/_jsxDEV(CardContent, {\n                children: [/*#__PURE__*/_jsxDEV(Typography, {\n                  variant: \"h6\",\n                  gutterBottom: true,\n                  children: \"Risultati Test\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1863,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(Grid, {\n                  container: true,\n                  spacing: 2,\n                  children: [/*#__PURE__*/_jsxDEV(Grid, {\n                    item: true,\n                    xs: 4,\n                    children: [/*#__PURE__*/_jsxDEV(Typography, {\n                      variant: \"body2\",\n                      color: \"text.secondary\",\n                      children: \"Continuit\\xE0\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1868,\n                      columnNumber: 23\n                    }, this), /*#__PURE__*/_jsxDEV(Chip, {\n                      size: \"small\",\n                      label: selectedItem.valore_continuita,\n                      color: selectedItem.valore_continuita === 'OK' ? 'success' : 'error'\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1871,\n                      columnNumber: 23\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1867,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(Grid, {\n                    item: true,\n                    xs: 4,\n                    children: [/*#__PURE__*/_jsxDEV(Typography, {\n                      variant: \"body2\",\n                      color: \"text.secondary\",\n                      children: \"Isolamento\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1878,\n                      columnNumber: 23\n                    }, this), /*#__PURE__*/_jsxDEV(Chip, {\n                      size: \"small\",\n                      label: `${selectedItem.valore_isolamento} MΩ`,\n                      color: parseFloat(selectedItem.valore_isolamento) >= 500 ? 'success' : 'warning'\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1881,\n                      columnNumber: 23\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1877,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(Grid, {\n                    item: true,\n                    xs: 4,\n                    children: [/*#__PURE__*/_jsxDEV(Typography, {\n                      variant: \"body2\",\n                      color: \"text.secondary\",\n                      children: \"Resistenza\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1888,\n                      columnNumber: 23\n                    }, this), /*#__PURE__*/_jsxDEV(Chip, {\n                      size: \"small\",\n                      label: selectedItem.valore_resistenza,\n                      color: selectedItem.valore_resistenza === 'OK' ? 'success' : 'error'\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1891,\n                      columnNumber: 23\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1887,\n                    columnNumber: 21\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1866,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 1862,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1861,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 1860,\n            columnNumber: 13\n          }, this), selectedItem.note && /*#__PURE__*/_jsxDEV(Grid, {\n            item: true,\n            xs: 12,\n            children: /*#__PURE__*/_jsxDEV(Card, {\n              variant: \"outlined\",\n              children: /*#__PURE__*/_jsxDEV(CardContent, {\n                children: [/*#__PURE__*/_jsxDEV(Typography, {\n                  variant: \"h6\",\n                  gutterBottom: true,\n                  children: \"Note\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1906,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                  variant: \"body2\",\n                  children: selectedItem.note\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1909,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 1905,\n                columnNumber: 19\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1904,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 1903,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 1824,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 1823,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(DialogActions, {\n        children: [/*#__PURE__*/_jsxDEV(Button, {\n          onClick: closeDialog,\n          children: \"Chiudi\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 1919,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Button, {\n          onClick: () => handleGeneratePdf(selectedItem),\n          variant: \"contained\",\n          startIcon: /*#__PURE__*/_jsxDEV(PdfIcon, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 1923,\n            columnNumber: 24\n          }, this),\n          disabled: loading,\n          children: \"Genera PDF\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 1920,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 1918,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 1819,\n      columnNumber: 7\n    }, this);\n  };\n\n  // Renderizza le statistiche\n  const renderStats = () => {\n    const totalCavi = cavi.length;\n    const caviInstallati = cavi.filter(c => c.stato_installazione === 'INSTALLATO').length;\n    const caviCertificati = certificazioni.length;\n    const percentualeCertificazione = totalCavi > 0 ? Math.round(caviCertificati / caviInstallati * 100) : 0;\n    return /*#__PURE__*/_jsxDEV(Grid, {\n      container: true,\n      spacing: 2,\n      sx: {\n        mb: 3\n      },\n      children: [/*#__PURE__*/_jsxDEV(Grid, {\n        item: true,\n        xs: 12,\n        sm: 6,\n        md: 3,\n        children: /*#__PURE__*/_jsxDEV(Card, {\n          children: /*#__PURE__*/_jsxDEV(CardContent, {\n            children: [/*#__PURE__*/_jsxDEV(Typography, {\n              color: \"text.secondary\",\n              gutterBottom: true,\n              children: \"Cavi Totali\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1945,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Typography, {\n              variant: \"h4\",\n              children: totalCavi\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1948,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 1944,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 1943,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 1942,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Grid, {\n        item: true,\n        xs: 12,\n        sm: 6,\n        md: 3,\n        children: /*#__PURE__*/_jsxDEV(Card, {\n          children: /*#__PURE__*/_jsxDEV(CardContent, {\n            children: [/*#__PURE__*/_jsxDEV(Typography, {\n              color: \"text.secondary\",\n              gutterBottom: true,\n              children: \"Cavi Installati\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1957,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Typography, {\n              variant: \"h4\",\n              children: caviInstallati\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1960,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 1956,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 1955,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 1954,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Grid, {\n        item: true,\n        xs: 12,\n        sm: 6,\n        md: 3,\n        children: /*#__PURE__*/_jsxDEV(Card, {\n          children: /*#__PURE__*/_jsxDEV(CardContent, {\n            children: [/*#__PURE__*/_jsxDEV(Typography, {\n              color: \"text.secondary\",\n              gutterBottom: true,\n              children: \"Certificazioni\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1969,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Typography, {\n              variant: \"h4\",\n              children: caviCertificati\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1972,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 1968,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 1967,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 1966,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Grid, {\n        item: true,\n        xs: 12,\n        sm: 6,\n        md: 3,\n        children: /*#__PURE__*/_jsxDEV(Card, {\n          children: /*#__PURE__*/_jsxDEV(CardContent, {\n            children: [/*#__PURE__*/_jsxDEV(Typography, {\n              color: \"text.secondary\",\n              gutterBottom: true,\n              children: \"% Certificazione\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1981,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Typography, {\n              variant: \"h4\",\n              color: percentualeCertificazione >= 80 ? 'success.main' : 'warning.main',\n              children: [percentualeCertificazione, \"%\"]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 1984,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 1980,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 1979,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 1978,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 1941,\n      columnNumber: 7\n    }, this);\n  };\n  return /*#__PURE__*/_jsxDEV(Container, {\n    maxWidth: \"xl\",\n    sx: {\n      py: 3\n    },\n    children: [renderDashboard(), (loading || operationInProgress) && /*#__PURE__*/_jsxDEV(Box, {\n      sx: {\n        mb: 2\n      },\n      children: [/*#__PURE__*/_jsxDEV(LinearProgress, {}, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 2002,\n        columnNumber: 11\n      }, this), progress > 0 && /*#__PURE__*/_jsxDEV(Typography, {\n        variant: \"caption\",\n        color: \"text.secondary\",\n        sx: {\n          mt: 1\n        },\n        children: [\"Caricamento... \", progress, \"%\"]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 2004,\n        columnNumber: 13\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 2001,\n      columnNumber: 9\n    }, this), /*#__PURE__*/_jsxDEV(Paper, {\n      sx: {\n        mb: 3\n      },\n      children: /*#__PURE__*/_jsxDEV(Tabs, {\n        value: activeTab,\n        onChange: handleTabChange,\n        indicatorColor: \"primary\",\n        textColor: \"primary\",\n        variant: \"fullWidth\",\n        children: [/*#__PURE__*/_jsxDEV(Tab, {\n          label: /*#__PURE__*/_jsxDEV(Box, {\n            children: [/*#__PURE__*/_jsxDEV(Typography, {\n              variant: \"body2\",\n              fontWeight: \"bold\",\n              children: \"Cavi da Certificare\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 2023,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(Typography, {\n              variant: \"caption\",\n              color: \"text.secondary\",\n              children: [filteredCavi.length, \" cavi totali\"]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 2026,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 2022,\n            columnNumber: 15\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 2020,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Tab, {\n          label: /*#__PURE__*/_jsxDEV(Box, {\n            children: [/*#__PURE__*/_jsxDEV(Typography, {\n              variant: \"body2\",\n              fontWeight: \"bold\",\n              children: \"Certificazioni Completate\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 2035,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(Typography, {\n              variant: \"caption\",\n              color: \"text.secondary\",\n              children: [filteredCertificazioni.length, \" certificazioni\"]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 2038,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 2034,\n            columnNumber: 15\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 2032,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 2013,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 2012,\n      columnNumber: 7\n    }, this), renderSearchAndFilters(), !loading && activeTab === 0 && renderCaviTable(), !loading && activeTab === 1 && renderCertificazioniTable(), renderCertificazioneDialog(), renderViewDialog(), /*#__PURE__*/_jsxDEV(Snackbar, {\n      open: snackbar.open,\n      autoHideDuration: 6000,\n      onClose: closeSnackbar,\n      anchorOrigin: {\n        vertical: 'bottom',\n        horizontal: 'right'\n      },\n      children: /*#__PURE__*/_jsxDEV(Alert, {\n        onClose: closeSnackbar,\n        severity: snackbar.severity,\n        sx: {\n          width: '100%'\n        },\n        children: snackbar.message\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 2065,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 2059,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 1995,\n    columnNumber: 5\n  }, this);\n}, \"/CDNJF2qHcRy8Ql452Cpyxb6jcM=\")), \"/CDNJF2qHcRy8Ql452Cpyxb6jcM=\");\n_c2 = CertificazioneCaviImproved;\nexport default CertificazioneCaviImproved;\nvar _c, _c2;\n$RefreshReg$(_c, \"CertificazioneCaviImproved$forwardRef\");\n$RefreshReg$(_c2, \"CertificazioneCaviImproved\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "useCallback", "forwardRef", "useImperativeHandle", "Box", "Typography", "<PERSON><PERSON>", "Paper", "Grid", "Card", "<PERSON><PERSON><PERSON><PERSON>", "Dialog", "DialogTitle", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "DialogActions", "TextField", "FormControl", "InputLabel", "Select", "MenuItem", "Autocomplete", "CircularProgress", "<PERSON><PERSON>", "Table", "TableBody", "TableCell", "TableContainer", "TableHead", "TableRow", "IconButton", "Tabs", "Tab", "Pagination", "InputAdornment", "Divider", "<PERSON><PERSON>", "Chip", "<PERSON><PERSON><PERSON>", "Badge", "LinearProgress", "Collapse", "List", "ListItem", "ListItemText", "ListItemIcon", "Snackbar", "AppBar", "<PERSON><PERSON><PERSON>", "Container", "Fab", "Add", "AddIcon", "Search", "SearchIcon", "FilterList", "FilterIcon", "PictureAsPdf", "PdfIcon", "Download", "DownloadIcon", "Visibility", "ViewIcon", "Delete", "DeleteIcon", "Edit", "EditIcon", "Save", "SaveIcon", "Clear", "ClearIcon", "Build", "BuildIcon", "CheckCircle", "CheckIcon", "Warning", "WarningIcon", "GetApp", "ExportIcon", "Print", "PrintIcon", "Email", "EmailIcon", "CloudUpload", "UploadIcon", "Assessment", "ReportIcon", "Settings", "SettingsIcon", "Refresh", "RefreshIcon", "ExpandMore", "ExpandMoreIcon", "ExpandLess", "ExpandLessIcon", "Info", "InfoIcon", "Error", "ErrorIcon", "Schedule", "ScheduleIcon", "Person", "PersonIcon", "Cable", "CableIcon", "Science", "ScienceIcon", "Block", "BlockIcon", "certificazioneService", "caviService", "jsxDEV", "_jsxDEV", "Fragment", "_Fragment", "CertificazioneCaviImproved", "_s", "_c", "cantiereId", "onSuccess", "onError", "ref", "loading", "setLoading", "activeTab", "setActiveTab", "certificazioni", "setCertificazioni", "cavi", "<PERSON><PERSON><PERSON>", "strumenti", "setStrumenti", "searchTerm", "setSearchTerm", "filteredCavi", "setFilteredCavi", "filteredCertificazioni", "setFilteredCertificazioni", "advancedFiltersOpen", "setAdvancedFiltersOpen", "filters", "setFilters", "stato", "tipologia", "operatore", "dataInizio", "dataFine", "valoreIsolamento", "risultatoTest", "strumento", "certificazione", "currentPage", "setCurrentPage", "itemsPerPage", "setItemsPerPage", "sortBy", "setSortBy", "sortOrder", "setSortOrder", "openDialog", "setOpenDialog", "dialogType", "setDialogType", "selectedItem", "setSelectedItem", "bulkSelection", "setBulkSelection", "bulkMode", "setBulkMode", "snackbar", "setSnackbar", "open", "message", "severity", "progress", "setProgress", "operationInProgress", "setOperationInProgress", "formData", "setFormData", "id_cavo", "id_operatore", "id_strumento", "<PERSON><PERSON><PERSON>_misurata", "valore_continuita", "valore_isolamento", "valore_resistenza", "note", "temperatura_ambiente", "<PERSON><PERSON><PERSON>", "tensione_prova", "durata_prova", "risultato_finale", "statistics", "setStatistics", "totaleCavi", "caviCertificati", "caviNonCertificati", "percentualeCompletamento", "certificazioniOggi", "certificazioniSettimana", "loadInitialData", "filterCavi", "filterCertificazioni", "calculateStatistics", "loadCavi", "loadCertificazioni", "loadStrumenti", "error", "showSnackbar", "data", "getCertificazioni", "console", "get<PERSON><PERSON>", "sortedData", "sort", "a", "b", "getNumFromId", "id", "match", "parseInt", "getStrumenti", "puoEssereCertificato", "cavo", "isInstallato", "stato_installazione", "isCavoCollegato", "isCollegato", "colle<PERSON>nti", "hasResponsabili", "responsabile_partenza", "responsabile_arrivo", "length", "Math", "round", "oggi", "Date", "toDateString", "filter", "cert", "data_certificazione", "unaSettimanaFa", "setDate", "getDate", "caviCertificabili", "caviNonCertificabili", "caviCollegati", "closeSnackbar", "filtered", "searchLower", "toLowerCase", "_cavo$tipologia", "_cavo$ubicazione_part", "_cavo$ubicazione_arri", "_cavo$sezione", "_cavo$utility", "includes", "ubicazione_partenza", "ubicazione_arrivo", "sezione", "utility", "some", "aValue", "bValue", "aNum", "bNum", "_cert$operatore", "_cert$numero_certific", "_cert$note", "numero_certificato", "valore", "parseFloat", "toggleBulkMode", "toggleItemSelection", "itemId", "prev", "newSelection", "selectAllItems", "allIds", "map", "id_certificazione", "clearSelection", "isCavoCertificato", "idCavo", "getMessaggioErroreCertificazione", "handleTabChange", "event", "newValue", "openCreateDialog", "cavoPreselezionato", "metratura_reale", "metri_te<PERSON>ci", "closeDialog", "handleFormChange", "field", "value", "handleCavoSelect", "handleCreateCertificazione", "find", "c", "messaggio", "conferma", "window", "confirm", "createCertificazione", "handleGeneratePdf", "response", "generatePdf", "file_url", "newWindow", "link", "document", "createElement", "href", "download", "body", "append<PERSON><PERSON><PERSON>", "click", "<PERSON><PERSON><PERSON><PERSON>", "pdf_content", "blob", "Blob", "atob", "type", "url", "URL", "createObjectURL", "revokeObjectURL", "handleDeleteCertificazione", "deleteCertificazione", "handleBulkDelete", "handleBulkExport", "<PERSON><PERSON><PERSON><PERSON>", "csv<PERSON><PERSON>nt", "generateCSV", "downloadCSV", "toISOString", "split", "headers", "rows", "toLocaleDateString", "row", "join", "content", "filename", "undefined", "setAttribute", "style", "visibility", "handleExportAll", "handleOptionSelect", "option", "getCurrentPageItems", "items", "startIndex", "endIndex", "slice", "getTotalPages", "ceil", "getUniqueValues", "array", "Set", "item", "Boolean", "renderDashboard", "sx", "p", "mb", "bgcolor", "children", "direction", "spacing", "alignItems", "justifyContent", "flexWrap", "color", "fontSize", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "variant", "fontWeight", "lineHeight", "width", "height", "borderRadius", "display", "renderSearchAndFilters", "container", "xs", "md", "fullWidth", "placeholder", "onChange", "e", "target", "InputProps", "startAdornment", "position", "endAdornment", "onClick", "size", "startIcon", "Object", "values", "f", "disabled", "in", "my", "tip", "op", "label", "InputLabelProps", "shrink", "renderCaviTable", "currentItems", "component", "isCertificato", "puoCertificare", "messaggioErrore", "statoCollegamento", "colore", "title", "icon", "mt", "count", "page", "renderCertificazioniTable", "padding", "selected", "hover", "s", "nome", "marca", "strumento_utilizzato", "renderCertificazioneDialog", "onClose", "max<PERSON><PERSON><PERSON>", "options", "isSelected", "isNotCertified", "canBeCertified", "getOptionLabel", "renderInput", "params", "required", "helperText", "renderOption", "props", "modello", "numero_serie", "multiline", "renderViewDialog", "gutterBottom", "renderStats", "totalCavi", "caviInstallati", "percentualeCertificazione", "sm", "py", "indicatorColor", "textColor", "autoHideDuration", "anchor<PERSON><PERSON><PERSON>", "vertical", "horizontal", "_c2", "$RefreshReg$"], "sources": ["C:/CMS/webapp/frontend/src/components/cavi/CertificazioneCaviImproved.js"], "sourcesContent": ["import React, { useState, useEffect, useCallback, forwardRef, useImperativeHandle } from 'react';\nimport {\n  Box,\n  Typography,\n  Button,\n  Paper,\n  Grid,\n  Card,\n  CardContent,\n  Dialog,\n  DialogTitle,\n  DialogContent,\n  DialogActions,\n  TextField,\n  FormControl,\n  InputLabel,\n  Select,\n  MenuItem,\n  Autocomplete,\n  CircularProgress,\n  Alert,\n  Table,\n  TableBody,\n  TableCell,\n  TableContainer,\n  TableHead,\n  TableRow,\n  IconButton,\n  Tabs,\n  Tab,\n  Pagination,\n  InputAdornment,\n  Divider,\n  Stack,\n  Chip,\n  Tooltip,\n  Badge,\n  LinearProgress,\n  Collapse,\n  List,\n  ListItem,\n  ListItemText,\n  ListItemIcon,\n  Snackbar,\n  AppBar,\n  Toolbar,\n  Container,\n  Fab\n} from '@mui/material';\nimport {\n  Add as AddIcon,\n  Search as SearchIcon,\n  FilterList as FilterIcon,\n  PictureAsPdf as PdfIcon,\n  Download as DownloadIcon,\n  Visibility as ViewIcon,\n  Delete as DeleteIcon,\n  Edit as EditIcon,\n  Save as SaveIcon,\n  Clear as ClearIcon,\n  Build as BuildIcon,\n  CheckCircle as CheckIcon,\n  Warning as WarningIcon,\n  GetApp as ExportIcon,\n  Print as PrintIcon,\n  Email as EmailIcon,\n  CloudUpload as UploadIcon,\n  Assessment as ReportIcon,\n  Settings as SettingsIcon,\n  Refresh as RefreshIcon,\n  ExpandMore as ExpandMoreIcon,\n  ExpandLess as ExpandLessIcon,\n  Info as InfoIcon,\n  Error as ErrorIcon,\n  Schedule as ScheduleIcon,\n  Person as PersonIcon,\n  Cable as CableIcon,\n  Science as ScienceIcon,\n  Block as BlockIcon\n} from '@mui/icons-material';\n\nimport certificazioneService from '../../services/certificazioneService';\nimport caviService from '../../services/caviService';\n\nconst CertificazioneCaviImproved = forwardRef(({ cantiereId, onSuccess, onError }, ref) => {\n  // Stati principali\n  const [loading, setLoading] = useState(false);\n  const [activeTab, setActiveTab] = useState(0);\n  const [certificazioni, setCertificazioni] = useState([]);\n  const [cavi, setCavi] = useState([]);\n  const [strumenti, setStrumenti] = useState([]);\n\n  // Stati per ricerca e filtri avanzati\n  const [searchTerm, setSearchTerm] = useState('');\n  const [filteredCavi, setFilteredCavi] = useState([]);\n  const [filteredCertificazioni, setFilteredCertificazioni] = useState([]);\n  const [advancedFiltersOpen, setAdvancedFiltersOpen] = useState(false);\n  const [filters, setFilters] = useState({\n    stato: '',\n    tipologia: '',\n    operatore: '',\n    dataInizio: '',\n    dataFine: '',\n    valoreIsolamento: '',\n    risultatoTest: '',\n    strumento: '',\n    certificazione: ''\n  });\n\n  // Stati per paginazione e ordinamento\n  const [currentPage, setCurrentPage] = useState(1);\n  const [itemsPerPage, setItemsPerPage] = useState(10);\n  const [sortBy, setSortBy] = useState('id_cavo');\n  const [sortOrder, setSortOrder] = useState('asc'); // Ordine crescente di default per ID cavo\n\n  // Stati per dialogs e modali\n  const [openDialog, setOpenDialog] = useState(false);\n  const [dialogType, setDialogType] = useState('');\n  const [selectedItem, setSelectedItem] = useState(null);\n  const [bulkSelection, setBulkSelection] = useState([]);\n  const [bulkMode, setBulkMode] = useState(false);\n\n  // Stati per notifiche e feedback\n  const [snackbar, setSnackbar] = useState({ open: false, message: '', severity: 'info' });\n  const [progress, setProgress] = useState(0);\n  const [operationInProgress, setOperationInProgress] = useState(false);\n\n  // Stati per form certificazione avanzato\n  const [formData, setFormData] = useState({\n    id_cavo: '',\n    id_operatore: '',\n    id_strumento: '',\n    lunghezza_misurata: '',\n    valore_continuita: 'OK',\n    valore_isolamento: '',\n    valore_resistenza: 'OK',\n    note: '',\n    temperatura_ambiente: '',\n    umidita: '',\n    tensione_prova: '',\n    durata_prova: '',\n    risultato_finale: 'CONFORME'\n  });\n\n  // Stati per statistiche e dashboard\n  const [statistics, setStatistics] = useState({\n    totaleCavi: 0,\n    caviCertificati: 0,\n    caviNonCertificati: 0,\n    percentualeCompletamento: 0,\n    certificazioniOggi: 0,\n    certificazioniSettimana: 0\n  });\n\n  // Carica dati iniziali\n  useEffect(() => {\n    loadInitialData();\n  }, [cantiereId]);\n\n  // Filtra cavi in base alla ricerca\n  useEffect(() => {\n    filterCavi();\n  }, [cavi, searchTerm, filters, sortBy, sortOrder]);\n\n  // Filtra certificazioni\n  useEffect(() => {\n    filterCertificazioni();\n  }, [certificazioni, searchTerm, filters, sortBy, sortOrder]);\n\n  // Ricalcola statistiche quando cambiano i dati\n  useEffect(() => {\n    calculateStatistics();\n  }, [cavi, certificazioni]);\n\n  // Riapplica filtri quando cambia il tab attivo E applica filtri iniziali\n  useEffect(() => {\n    if (activeTab === 0) {\n      filterCavi();\n    } else if (activeTab === 1) {\n      filterCertificazioni();\n    }\n  }, [activeTab, cavi, certificazioni]); // Aggiunto cavi e certificazioni come dipendenze\n\n  const loadInitialData = async () => {\n    try {\n      setLoading(true);\n      setProgress(0);\n\n      // Carica dati in sequenza con progress\n      setProgress(25);\n      await loadCavi();\n\n      setProgress(50);\n      await loadCertificazioni();\n\n      setProgress(75);\n      await loadStrumenti();\n\n      setProgress(100);\n      calculateStatistics();\n\n    } catch (error) {\n      showSnackbar('Errore nel caricamento dei dati iniziali', 'error');\n      onError('Errore nel caricamento dei dati iniziali');\n    } finally {\n      setLoading(false);\n      setProgress(0);\n    }\n  };\n\n  const loadCertificazioni = async () => {\n    try {\n      const data = await certificazioneService.getCertificazioni(cantiereId);\n      setCertificazioni(data);\n      return data;\n    } catch (error) {\n      console.error('Errore nel caricamento delle certificazioni:', error);\n      throw error;\n    }\n  };\n\n  const loadCavi = async () => {\n    try {\n      const data = await caviService.getCavi(cantiereId);\n      // Ordina sempre i cavi per ID in ordine crescente (C001, C002, etc.)\n      const sortedData = data.sort((a, b) => {\n        // Estrai il numero dall'ID del cavo (es. C001 -> 1)\n        const getNumFromId = (id) => {\n          const match = id.match(/(\\d+)/);\n          return match ? parseInt(match[1], 10) : 0;\n        };\n        return getNumFromId(a.id_cavo) - getNumFromId(b.id_cavo);\n      });\n      setCavi(sortedData);\n      return sortedData;\n    } catch (error) {\n      console.error('Errore nel caricamento dei cavi:', error);\n      throw error;\n    }\n  };\n\n  const loadStrumenti = async () => {\n    try {\n      const data = await certificazioneService.getStrumenti(cantiereId);\n      setStrumenti(data);\n      return data;\n    } catch (error) {\n      console.error('Errore nel caricamento degli strumenti:', error);\n      throw error;\n    }\n  };\n\n  // Funzione per verificare se un cavo può essere certificato\n  const puoEssereCertificato = useCallback((cavo) => {\n    // Verifica che il cavo sia installato/posato\n    const isInstallato = cavo.stato_installazione === 'Installato' ||\n                        cavo.stato_installazione === 'INSTALLATO' ||\n                        cavo.stato_installazione === 'POSATO';\n\n    // Per la certificazione basta che sia posato\n    // Il collegamento può essere gestito durante la certificazione\n    return isInstallato;\n  }, []);\n\n  // Funzione per verificare se un cavo è completamente collegato\n  const isCavoCollegato = useCallback((cavo) => {\n    const isCollegato = cavo.collegamenti === 3;\n    const hasResponsabili = cavo.responsabile_partenza && cavo.responsabile_arrivo;\n    return isCollegato && hasResponsabili;\n  }, []);\n\n  // Calcola statistiche avanzate\n  const calculateStatistics = useCallback(() => {\n    if (!cavi || !certificazioni) return;\n\n    const totaleCavi = cavi.length;\n    const caviCertificati = certificazioni.length;\n    const caviNonCertificati = totaleCavi - caviCertificati;\n    const percentualeCompletamento = totaleCavi > 0 ? Math.round((caviCertificati / totaleCavi) * 100) : 0;\n\n    // Calcola certificazioni di oggi\n    const oggi = new Date().toDateString();\n    const certificazioniOggi = certificazioni.filter(cert =>\n      new Date(cert.data_certificazione).toDateString() === oggi\n    ).length;\n\n    // Calcola certificazioni della settimana\n    const unaSettimanaFa = new Date();\n    unaSettimanaFa.setDate(unaSettimanaFa.getDate() - 7);\n    const certificazioniSettimana = certificazioni.filter(cert =>\n      new Date(cert.data_certificazione) >= unaSettimanaFa\n    ).length;\n\n    // Calcola cavi certificabili (solo posati/installati)\n    const caviCertificabili = cavi.filter(cavo => puoEssereCertificato(cavo)).length;\n    const caviNonCertificabili = totaleCavi - caviCertificabili;\n\n    // Calcola cavi collegati completamente\n    const caviCollegati = cavi.filter(cavo => isCavoCollegato(cavo)).length;\n\n    setStatistics({\n      totaleCavi,\n      caviCertificati,\n      caviNonCertificati,\n      caviCertificabili,\n      caviNonCertificabili,\n      caviCollegati,\n      percentualeCompletamento,\n      certificazioniOggi,\n      certificazioniSettimana\n    });\n  }, [cavi, certificazioni, puoEssereCertificato, isCavoCollegato]);\n\n  // Gestione snackbar\n  const showSnackbar = (message, severity = 'info') => {\n    setSnackbar({ open: true, message, severity });\n  };\n\n  const closeSnackbar = () => {\n    setSnackbar({ ...snackbar, open: false });\n  };\n\n  const filterCavi = () => {\n    let filtered = cavi;\n\n    // Filtro per ricerca testuale avanzata\n    if (searchTerm) {\n      const searchLower = searchTerm.toLowerCase();\n      filtered = filtered.filter(cavo =>\n        cavo.id_cavo.toLowerCase().includes(searchLower) ||\n        cavo.tipologia?.toLowerCase().includes(searchLower) ||\n        cavo.ubicazione_partenza?.toLowerCase().includes(searchLower) ||\n        cavo.ubicazione_arrivo?.toLowerCase().includes(searchLower) ||\n        cavo.sezione?.toLowerCase().includes(searchLower) ||\n        cavo.utility?.toLowerCase().includes(searchLower)\n      );\n    }\n\n    // Filtri specifici avanzati\n    if (filters.stato) {\n      filtered = filtered.filter(cavo => cavo.stato_installazione === filters.stato);\n    }\n    if (filters.tipologia) {\n      filtered = filtered.filter(cavo => cavo.tipologia === filters.tipologia);\n    }\n\n    // Filtro per stato certificazione\n    if (filters.certificazione) {\n      if (filters.certificazione === 'CERTIFICATO') {\n        filtered = filtered.filter(cavo =>\n          certificazioni.some(cert => cert.id_cavo === cavo.id_cavo)\n        );\n      } else if (filters.certificazione === 'NON_CERTIFICATO') {\n        filtered = filtered.filter(cavo =>\n          !certificazioni.some(cert => cert.id_cavo === cavo.id_cavo)\n        );\n      }\n    }\n\n    // Ordinamento - speciale per ID cavo per mantenere ordine numerico\n    filtered.sort((a, b) => {\n      let aValue = a[sortBy];\n      let bValue = b[sortBy];\n\n      // Ordinamento speciale per ID cavo (C001, C002, etc.)\n      if (sortBy === 'id_cavo') {\n        const getNumFromId = (id) => {\n          const match = id.match(/(\\d+)/);\n          return match ? parseInt(match[1], 10) : 0;\n        };\n        const aNum = getNumFromId(aValue);\n        const bNum = getNumFromId(bValue);\n\n        if (sortOrder === 'asc') {\n          return aNum - bNum;\n        } else {\n          return bNum - aNum;\n        }\n      }\n\n      // Ordinamento normale per altri campi\n      if (typeof aValue === 'string') {\n        aValue = aValue.toLowerCase();\n        bValue = bValue.toLowerCase();\n      }\n\n      if (sortOrder === 'asc') {\n        return aValue > bValue ? 1 : -1;\n      } else {\n        return aValue < bValue ? 1 : -1;\n      }\n    });\n\n    setFilteredCavi(filtered);\n  };\n\n  const filterCertificazioni = () => {\n    let filtered = certificazioni;\n\n    // Ricerca testuale avanzata\n    if (searchTerm) {\n      const searchLower = searchTerm.toLowerCase();\n      filtered = filtered.filter(cert =>\n        cert.id_cavo.toLowerCase().includes(searchLower) ||\n        cert.operatore?.toLowerCase().includes(searchLower) ||\n        cert.numero_certificato?.toLowerCase().includes(searchLower) ||\n        cert.note?.toLowerCase().includes(searchLower)\n      );\n    }\n\n    // Filtri avanzati\n    if (filters.operatore) {\n      filtered = filtered.filter(cert => cert.operatore === filters.operatore);\n    }\n    if (filters.strumento) {\n      filtered = filtered.filter(cert => cert.strumento === filters.strumento);\n    }\n    if (filters.risultatoTest) {\n      filtered = filtered.filter(cert => cert.risultato_finale === filters.risultatoTest);\n    }\n    if (filters.dataInizio) {\n      filtered = filtered.filter(cert =>\n        new Date(cert.data_certificazione) >= new Date(filters.dataInizio)\n      );\n    }\n    if (filters.dataFine) {\n      filtered = filtered.filter(cert =>\n        new Date(cert.data_certificazione) <= new Date(filters.dataFine)\n      );\n    }\n    if (filters.valoreIsolamento) {\n      const valore = parseFloat(filters.valoreIsolamento);\n      filtered = filtered.filter(cert =>\n        parseFloat(cert.valore_isolamento) >= valore\n      );\n    }\n\n    // Ordinamento\n    filtered.sort((a, b) => {\n      let aValue = a[sortBy];\n      let bValue = b[sortBy];\n\n      if (sortBy === 'data_certificazione') {\n        aValue = new Date(aValue);\n        bValue = new Date(bValue);\n      } else if (typeof aValue === 'string') {\n        aValue = aValue.toLowerCase();\n        bValue = bValue.toLowerCase();\n      }\n\n      if (sortOrder === 'asc') {\n        return aValue > bValue ? 1 : -1;\n      } else {\n        return aValue < bValue ? 1 : -1;\n      }\n    });\n\n    setFilteredCertificazioni(filtered);\n  };\n\n  // Gestione selezione multipla - SOLO per certificazioni\n  const toggleBulkMode = () => {\n    if (activeTab !== 1) {\n      showSnackbar('La selezione multipla è disponibile solo per le certificazioni', 'warning');\n      return;\n    }\n    setBulkMode(!bulkMode);\n    setBulkSelection([]);\n    showSnackbar(\n      !bulkMode\n        ? 'Modalità selezione attivata - Clicca sulle certificazioni per selezionarle'\n        : 'Modalità selezione disattivata',\n      'info'\n    );\n  };\n\n  const toggleItemSelection = (itemId) => {\n    setBulkSelection(prev => {\n      const newSelection = prev.includes(itemId)\n        ? prev.filter(id => id !== itemId)\n        : [...prev, itemId];\n\n      showSnackbar(\n        `${newSelection.length} certificazioni selezionate`,\n        'info'\n      );\n      return newSelection;\n    });\n  };\n\n  const selectAllItems = () => {\n    if (activeTab !== 1) return;\n\n    const allIds = filteredCertificazioni.map(cert => cert.id_certificazione);\n    setBulkSelection(allIds);\n    showSnackbar(`Tutte le ${allIds.length} certificazioni selezionate`, 'success');\n  };\n\n  const clearSelection = () => {\n    setBulkSelection([]);\n    showSnackbar('Selezione cancellata', 'info');\n  };\n\n  // Funzione per verificare se un cavo è certificato\n  const isCavoCertificato = (idCavo) => {\n    return certificazioni.some(cert => cert.id_cavo === idCavo);\n  };\n\n  // Funzione per ottenere il messaggio di errore per cavi non certificabili\n  const getMessaggioErroreCertificazione = (cavo) => {\n    const isInstallato = cavo.stato_installazione === 'Installato' ||\n                        cavo.stato_installazione === 'INSTALLATO' ||\n                        cavo.stato_installazione === 'POSATO';\n\n    if (!isInstallato) {\n      return 'Il cavo deve essere posato/installato prima di poter essere certificato';\n    }\n\n    return 'Cavo non certificabile per motivi sconosciuti';\n  };\n\n  // Gestione tabs\n  const handleTabChange = (event, newValue) => {\n    setActiveTab(newValue);\n    setCurrentPage(1);\n    setSearchTerm('');\n    setFilters({ stato: '', tipologia: '', operatore: '' });\n  };\n\n  // Gestione dialogs\n  const openCreateDialog = (cavoPreselezionato = null) => {\n    setDialogType('create');\n    setSelectedItem(null);\n\n    // Se viene passato un cavo, precompila il form\n    if (cavoPreselezionato) {\n      setFormData({\n        id_cavo: cavoPreselezionato.id_cavo,\n        id_operatore: '',\n        id_strumento: '',\n        lunghezza_misurata: cavoPreselezionato.metratura_reale || cavoPreselezionato.metri_teorici || '',\n        valore_continuita: 'OK',\n        valore_isolamento: '',\n        valore_resistenza: 'OK',\n        note: '',\n        temperatura_ambiente: '',\n        umidita: '',\n        tensione_prova: '',\n        durata_prova: '',\n        risultato_finale: 'CONFORME'\n      });\n      showSnackbar(`Cavo ${cavoPreselezionato.id_cavo} selezionato automaticamente`, 'success');\n    } else {\n      // Reset form per nuova certificazione generica\n      setFormData({\n        id_cavo: '',\n        id_operatore: '',\n        id_strumento: '',\n        lunghezza_misurata: '',\n        valore_continuita: 'OK',\n        valore_isolamento: '',\n        valore_resistenza: 'OK',\n        note: '',\n        temperatura_ambiente: '',\n        umidita: '',\n        tensione_prova: '',\n        durata_prova: '',\n        risultato_finale: 'CONFORME'\n      });\n    }\n\n    setOpenDialog(true);\n  };\n\n  const closeDialog = () => {\n    setOpenDialog(false);\n    setSelectedItem(null);\n    setDialogType('');\n  };\n\n  // Gestione form\n  const handleFormChange = (field, value) => {\n    setFormData(prev => ({\n      ...prev,\n      [field]: value\n    }));\n  };\n\n  const handleCavoSelect = (cavo) => {\n    setFormData(prev => ({\n      ...prev,\n      id_cavo: cavo.id_cavo,\n      lunghezza_misurata: cavo.metratura_reale || cavo.metri_teorici || ''\n    }));\n  };\n\n  // Operazioni CRUD avanzate\n  const handleCreateCertificazione = async () => {\n    try {\n      if (!formData.id_cavo || !formData.id_operatore || !formData.id_strumento || !formData.valore_isolamento) {\n        showSnackbar('Compila tutti i campi obbligatori', 'warning');\n        return;\n      }\n\n      // Verifica che il cavo possa essere certificato\n      const cavo = cavi.find(c => c.id_cavo === formData.id_cavo);\n      if (!cavo) {\n        showSnackbar('Cavo non trovato', 'error');\n        return;\n      }\n\n      if (!puoEssereCertificato(cavo)) {\n        const messaggio = getMessaggioErroreCertificazione(cavo);\n        showSnackbar(`Impossibile certificare il cavo: ${messaggio}`, 'error');\n        return;\n      }\n\n      // Verifica che il cavo non sia già certificato\n      if (isCavoCertificato(formData.id_cavo)) {\n        showSnackbar('Il cavo è già stato certificato', 'warning');\n        return;\n      }\n\n      // Verifica se il cavo è collegato, altrimenti chiedi conferma\n      if (!isCavoCollegato(cavo)) {\n        const conferma = window.confirm(\n          `ATTENZIONE: Il cavo ${cavo.id_cavo} non risulta completamente collegato.\\n\\n` +\n          `Stato collegamenti: ${cavo.collegamenti === 0 ? 'Non collegato' :\n                                cavo.collegamenti === 1 ? 'Solo partenza collegata' :\n                                cavo.collegamenti === 2 ? 'Solo arrivo collegato' :\n                                'Stato sconosciuto'}\\n\\n` +\n          `Vuoi procedere comunque con la certificazione?\\n` +\n          `(Ricorda di completare i collegamenti prima della messa in servizio)`\n        );\n\n        if (!conferma) {\n          return;\n        }\n      }\n\n      setOperationInProgress(true);\n      await certificazioneService.createCertificazione(cantiereId, formData);\n      showSnackbar('Certificazione creata con successo', 'success');\n      closeDialog();\n      await loadCertificazioni();\n      calculateStatistics();\n    } catch (error) {\n      showSnackbar('Errore nella creazione della certificazione: ' + (error.message || 'Errore sconosciuto'), 'error');\n    } finally {\n      setOperationInProgress(false);\n    }\n  };\n\n  const handleGeneratePdf = async (certificazione) => {\n    try {\n      setOperationInProgress(true);\n      showSnackbar('Generazione PDF in corso...', 'info');\n\n      const response = await certificazioneService.generatePdf(cantiereId, certificazione.id_certificazione);\n\n      if (response.file_url) {\n        // Apri il PDF in una nuova finestra\n        const newWindow = window.open(response.file_url, '_blank');\n        if (newWindow) {\n          showSnackbar('PDF generato e aperto in una nuova finestra', 'success');\n        } else {\n          // Se il popup è bloccato, offri il download diretto\n          const link = document.createElement('a');\n          link.href = response.file_url;\n          link.download = `certificazione_${certificazione.numero_certificato}.pdf`;\n          document.body.appendChild(link);\n          link.click();\n          document.body.removeChild(link);\n          showSnackbar('PDF scaricato nella cartella Download', 'success');\n        }\n      } else if (response.pdf_content) {\n        // Se il PDF viene restituito come contenuto base64\n        const blob = new Blob([atob(response.pdf_content)], { type: 'application/pdf' });\n        const url = URL.createObjectURL(blob);\n        const link = document.createElement('a');\n        link.href = url;\n        link.download = `certificazione_${certificazione.numero_certificato}.pdf`;\n        document.body.appendChild(link);\n        link.click();\n        document.body.removeChild(link);\n        URL.revokeObjectURL(url);\n        showSnackbar('PDF scaricato con successo', 'success');\n      } else {\n        showSnackbar('Errore: Formato PDF non riconosciuto', 'error');\n      }\n    } catch (error) {\n      console.error('Errore generazione PDF:', error);\n      showSnackbar('Errore nella generazione del PDF: ' + (error.message || 'Errore sconosciuto'), 'error');\n    } finally {\n      setOperationInProgress(false);\n    }\n  };\n\n  const handleDeleteCertificazione = async (certificazione) => {\n    if (window.confirm(`Sei sicuro di voler eliminare la certificazione ${certificazione.numero_certificato}?`)) {\n      try {\n        setOperationInProgress(true);\n        await certificazioneService.deleteCertificazione(cantiereId, certificazione.id_certificazione);\n        showSnackbar('Certificazione eliminata con successo', 'success');\n        await loadCertificazioni();\n        calculateStatistics();\n      } catch (error) {\n        showSnackbar('Errore nell\\'eliminazione della certificazione: ' + (error.message || 'Errore sconosciuto'), 'error');\n      } finally {\n        setOperationInProgress(false);\n      }\n    }\n  };\n\n  // Operazioni bulk\n  const handleBulkDelete = async () => {\n    if (bulkSelection.length === 0) {\n      showSnackbar('Seleziona almeno un elemento', 'warning');\n      return;\n    }\n\n    if (window.confirm(`Sei sicuro di voler eliminare ${bulkSelection.length} certificazioni?`)) {\n      try {\n        setOperationInProgress(true);\n        for (const id of bulkSelection) {\n          await certificazioneService.deleteCertificazione(cantiereId, id);\n        }\n        showSnackbar(`${bulkSelection.length} certificazioni eliminate con successo`, 'success');\n        setBulkSelection([]);\n        await loadCertificazioni();\n        calculateStatistics();\n      } catch (error) {\n        showSnackbar('Errore nell\\'eliminazione delle certificazioni', 'error');\n      } finally {\n        setOperationInProgress(false);\n      }\n    }\n  };\n\n  const handleBulkExport = async () => {\n    if (bulkSelection.length === 0) {\n      showSnackbar('Seleziona almeno un elemento', 'warning');\n      return;\n    }\n\n    try {\n      setOperationInProgress(true);\n      // Implementa export bulk\n      const selectedCerts = certificazioni.filter(cert =>\n        bulkSelection.includes(cert.id_certificazione)\n      );\n\n      // Crea CSV\n      const csvContent = generateCSV(selectedCerts);\n      downloadCSV(csvContent, `certificazioni_${new Date().toISOString().split('T')[0]}.csv`);\n\n      showSnackbar(`${bulkSelection.length} certificazioni esportate`, 'success');\n    } catch (error) {\n      showSnackbar('Errore nell\\'esportazione', 'error');\n    } finally {\n      setOperationInProgress(false);\n    }\n  };\n\n  // Funzioni di export\n  const generateCSV = (data) => {\n    const headers = ['ID Cavo', 'Numero Certificato', 'Data', 'Operatore', 'Strumento', 'Lunghezza', 'Isolamento', 'Risultato'];\n    const rows = data.map(cert => [\n      cert.id_cavo,\n      cert.numero_certificato,\n      new Date(cert.data_certificazione).toLocaleDateString(),\n      cert.operatore,\n      cert.strumento,\n      cert.lunghezza_misurata,\n      cert.valore_isolamento,\n      cert.risultato_finale\n    ]);\n\n    return [headers, ...rows].map(row => row.join(',')).join('\\n');\n  };\n\n  const downloadCSV = (content, filename) => {\n    const blob = new Blob([content], { type: 'text/csv;charset=utf-8;' });\n    const link = document.createElement('a');\n    if (link.download !== undefined) {\n      const url = URL.createObjectURL(blob);\n      link.setAttribute('href', url);\n      link.setAttribute('download', filename);\n      link.style.visibility = 'hidden';\n      document.body.appendChild(link);\n      link.click();\n      document.body.removeChild(link);\n    }\n  };\n\n  const handleExportAll = () => {\n    const csvContent = generateCSV(filteredCertificazioni);\n    downloadCSV(csvContent, `tutte_certificazioni_${new Date().toISOString().split('T')[0]}.csv`);\n    showSnackbar('Esportazione completata', 'success');\n  };\n\n  // Espone metodi tramite ref\n  useImperativeHandle(ref, () => ({\n    handleOptionSelect: (option) => {\n      if (option === 'creaCertificazione') {\n        openCreateDialog();\n      } else if (option === 'visualizzaCertificazioni') {\n        setActiveTab(1);\n      }\n    }\n  }));\n\n  // Calcola elementi per paginazione\n  const getCurrentPageItems = (items) => {\n    const startIndex = (currentPage - 1) * itemsPerPage;\n    const endIndex = startIndex + itemsPerPage;\n    return items.slice(startIndex, endIndex);\n  };\n\n  const getTotalPages = (items) => Math.ceil(items.length / itemsPerPage);\n\n  // Ottieni opzioni uniche per filtri\n  const getUniqueValues = (array, field) => {\n    return [...new Set(array.map(item => item[field]).filter(Boolean))];\n  };\n\n  // Dashboard minimal con statistiche essenziali\n  const renderDashboard = () => (\n    <Paper sx={{ p: 2, mb: 3, bgcolor: 'grey.50' }}>\n      <Stack direction=\"row\" spacing={4} alignItems=\"center\" justifyContent=\"space-between\" flexWrap=\"wrap\">\n        {/* Statistiche essenziali in formato compatto */}\n        <Stack direction=\"row\" alignItems=\"center\" spacing={1}>\n          <CableIcon color=\"primary\" fontSize=\"small\" />\n          <Box>\n            <Typography variant=\"h6\" fontWeight=\"bold\" sx={{ lineHeight: 1 }}>\n              {statistics.totaleCavi}\n            </Typography>\n            <Typography variant=\"caption\" color=\"text.secondary\">\n              Totale\n            </Typography>\n          </Box>\n        </Stack>\n\n        <Stack direction=\"row\" alignItems=\"center\" spacing={1}>\n          <CheckIcon color=\"success\" fontSize=\"small\" />\n          <Box>\n            <Typography variant=\"h6\" fontWeight=\"bold\" sx={{ lineHeight: 1 }}>\n              {statistics.caviCertificati}\n            </Typography>\n            <Typography variant=\"caption\" color=\"text.secondary\">\n              Certificati\n            </Typography>\n          </Box>\n        </Stack>\n\n        <Stack direction=\"row\" alignItems=\"center\" spacing={1}>\n          <BuildIcon color=\"info\" fontSize=\"small\" />\n          <Box>\n            <Typography variant=\"h6\" fontWeight=\"bold\" sx={{ lineHeight: 1 }}>\n              {statistics.caviCertificabili}\n            </Typography>\n            <Typography variant=\"caption\" color=\"text.secondary\">\n              Pronti\n            </Typography>\n          </Box>\n        </Stack>\n\n        <Stack direction=\"row\" alignItems=\"center\" spacing={1}>\n          <Box sx={{\n            width: 32,\n            height: 32,\n            borderRadius: '50%',\n            bgcolor: statistics.percentualeCompletamento >= 80 ? 'success.main' :\n                     statistics.percentualeCompletamento >= 50 ? 'warning.main' : 'error.main',\n            display: 'flex',\n            alignItems: 'center',\n            justifyContent: 'center'\n          }}>\n            <Typography variant=\"caption\" fontWeight=\"bold\" color=\"white\">\n              {statistics.percentualeCompletamento}%\n            </Typography>\n          </Box>\n          <Box>\n            <Typography variant=\"body2\" fontWeight=\"medium\" sx={{ lineHeight: 1 }}>\n              Completamento\n            </Typography>\n            <Typography variant=\"caption\" color=\"text.secondary\">\n              {statistics.certificazioniOggi} oggi\n            </Typography>\n          </Box>\n        </Stack>\n\n\n      </Stack>\n    </Paper>\n  );\n\n  // Componente barra di ricerca avanzata\n  const renderSearchAndFilters = () => (\n    <Paper sx={{ p: 2, mb: 3 }}>\n      <Grid container spacing={2} alignItems=\"center\">\n        <Grid item xs={12} md={4}>\n          <TextField\n            fullWidth\n            placeholder=\"Cerca cavi, certificazioni, operatori...\"\n            value={searchTerm}\n            onChange={(e) => setSearchTerm(e.target.value)}\n            InputProps={{\n              startAdornment: (\n                <InputAdornment position=\"start\">\n                  <SearchIcon />\n                </InputAdornment>\n              ),\n              endAdornment: searchTerm && (\n                <InputAdornment position=\"end\">\n                  <IconButton onClick={() => setSearchTerm('')} size=\"small\">\n                    <ClearIcon />\n                  </IconButton>\n                </InputAdornment>\n              )\n            }}\n          />\n        </Grid>\n\n        <Grid item xs={12} md={2}>\n          <Button\n            fullWidth\n            variant=\"outlined\"\n            startIcon={<FilterIcon />}\n            onClick={() => setAdvancedFiltersOpen(!advancedFiltersOpen)}\n            color={Object.values(filters).some(f => f) ? 'primary' : 'inherit'}\n          >\n            Filtri {Object.values(filters).filter(f => f).length > 0 && `(${Object.values(filters).filter(f => f).length})`}\n          </Button>\n        </Grid>\n\n        <Grid item xs={12} md={2}>\n          <Button\n            fullWidth\n            variant=\"outlined\"\n            startIcon={bulkMode ? <ClearIcon /> : <CheckIcon />}\n            onClick={toggleBulkMode}\n            color={bulkMode ? 'secondary' : 'inherit'}\n            disabled={activeTab === 0}\n          >\n            {bulkMode ? 'Esci Selezione' : 'Selezione Multipla'}\n          </Button>\n        </Grid>\n\n        <Grid item xs={12} md={2}>\n          <Button\n            fullWidth\n            variant=\"outlined\"\n            startIcon={<ExportIcon />}\n            onClick={handleExportAll}\n            disabled={activeTab === 0 || filteredCertificazioni.length === 0}\n          >\n            {activeTab === 0 ? 'Export (solo certificazioni)' : 'Esporta Certificazioni'}\n          </Button>\n        </Grid>\n\n        <Grid item xs={12} md={2}>\n          <Button\n            fullWidth\n            variant=\"contained\"\n            startIcon={<AddIcon />}\n            onClick={openCreateDialog}\n          >\n            Nuova Certificazione\n          </Button>\n        </Grid>\n      </Grid>\n\n      {/* Filtri avanzati - Diversi per ogni tab */}\n      <Collapse in={advancedFiltersOpen}>\n        <Divider sx={{ my: 2 }} />\n        <Typography variant=\"body2\" color=\"text.secondary\" sx={{ mb: 2 }}>\n          {activeTab === 0 ? 'Filtri per Cavi' : 'Filtri per Certificazioni'}\n        </Typography>\n\n        <Grid container spacing={2}>\n          {/* Filtri per tab Cavi */}\n          {activeTab === 0 && (\n            <>\n              <Grid item xs={12} md={3}>\n                <FormControl fullWidth size=\"small\">\n                  <InputLabel>Stato Installazione</InputLabel>\n                  <Select\n                    value={filters.stato}\n                    onChange={(e) => setFilters({...filters, stato: e.target.value})}\n                  >\n                    <MenuItem value=\"\">Tutti</MenuItem>\n                    <MenuItem value=\"INSTALLATO\">Installato</MenuItem>\n                    <MenuItem value=\"NON_INSTALLATO\">Non Installato</MenuItem>\n                    <MenuItem value=\"IN_CORSO\">In Corso</MenuItem>\n                  </Select>\n                </FormControl>\n              </Grid>\n\n              <Grid item xs={12} md={3}>\n                <FormControl fullWidth size=\"small\">\n                  <InputLabel>Tipologia</InputLabel>\n                  <Select\n                    value={filters.tipologia}\n                    onChange={(e) => setFilters({...filters, tipologia: e.target.value})}\n                  >\n                    <MenuItem value=\"\">Tutte</MenuItem>\n                    {[...new Set(cavi.map(c => c.tipologia))].filter(Boolean).map(tip => (\n                      <MenuItem key={tip} value={tip}>{tip}</MenuItem>\n                    ))}\n                  </Select>\n                </FormControl>\n              </Grid>\n\n              <Grid item xs={12} md={3}>\n                <FormControl fullWidth size=\"small\">\n                  <InputLabel>Stato Certificazione</InputLabel>\n                  <Select\n                    value={filters.certificazione}\n                    onChange={(e) => setFilters({...filters, certificazione: e.target.value})}\n                  >\n                    <MenuItem value=\"\">Tutti</MenuItem>\n                    <MenuItem value=\"CERTIFICATO\">Certificato</MenuItem>\n                    <MenuItem value=\"NON_CERTIFICATO\">Non Certificato</MenuItem>\n                  </Select>\n                </FormControl>\n              </Grid>\n            </>\n          )}\n\n          {/* Filtri per tab Certificazioni */}\n          {activeTab === 1 && (\n            <>\n              <Grid item xs={12} md={3}>\n                <FormControl fullWidth size=\"small\">\n                  <InputLabel>Operatore</InputLabel>\n                  <Select\n                    value={filters.operatore}\n                    onChange={(e) => setFilters({...filters, operatore: e.target.value})}\n                  >\n                    <MenuItem value=\"\">Tutti</MenuItem>\n                    {[...new Set(certificazioni.map(c => c.operatore))].filter(Boolean).map(op => (\n                      <MenuItem key={op} value={op}>{op}</MenuItem>\n                    ))}\n                  </Select>\n                </FormControl>\n              </Grid>\n\n              <Grid item xs={12} md={3}>\n                <FormControl fullWidth size=\"small\">\n                  <InputLabel>Risultato Test</InputLabel>\n                  <Select\n                    value={filters.risultatoTest}\n                    onChange={(e) => setFilters({...filters, risultatoTest: e.target.value})}\n                  >\n                    <MenuItem value=\"\">Tutti</MenuItem>\n                    <MenuItem value=\"CONFORME\">Conforme</MenuItem>\n                    <MenuItem value=\"NON_CONFORME\">Non Conforme</MenuItem>\n                    <MenuItem value=\"DA_VERIFICARE\">Da Verificare</MenuItem>\n                  </Select>\n                </FormControl>\n              </Grid>\n\n              <Grid item xs={12} md={3}>\n                <TextField\n                  fullWidth\n                  size=\"small\"\n                  label=\"Isolamento Min (MΩ)\"\n                  type=\"number\"\n                  value={filters.valoreIsolamento}\n                  onChange={(e) => setFilters({...filters, valoreIsolamento: e.target.value})}\n                  placeholder=\"es. 500\"\n                />\n              </Grid>\n\n              <Grid item xs={12} md={3}>\n                <TextField\n                  fullWidth\n                  size=\"small\"\n                  label=\"Data Inizio\"\n                  type=\"date\"\n                  value={filters.dataInizio}\n                  onChange={(e) => setFilters({...filters, dataInizio: e.target.value})}\n                  InputLabelProps={{ shrink: true }}\n                />\n              </Grid>\n\n              <Grid item xs={12} md={3}>\n                <TextField\n                  fullWidth\n                  size=\"small\"\n                  label=\"Data Fine\"\n                  type=\"date\"\n                  value={filters.dataFine}\n                  onChange={(e) => setFilters({...filters, dataFine: e.target.value})}\n                  InputLabelProps={{ shrink: true }}\n                />\n              </Grid>\n            </>\n          )}\n\n          <Grid item xs={12}>\n            <Stack direction=\"row\" spacing={1} justifyContent=\"flex-end\">\n              <Button\n                variant=\"outlined\"\n                size=\"small\"\n                onClick={() => setFilters({\n                  stato: '', tipologia: '', operatore: '', dataInizio: '',\n                  dataFine: '', valoreIsolamento: '', risultatoTest: '', strumento: '',\n                  certificazione: ''\n                })}\n              >\n                Pulisci Tutti i Filtri\n              </Button>\n            </Stack>\n          </Grid>\n        </Grid>\n      </Collapse>\n\n      {/* Barra azioni bulk */}\n      {bulkMode && bulkSelection.length > 0 && (\n        <>\n          <Divider sx={{ my: 2 }} />\n          <Stack direction=\"row\" spacing={2} alignItems=\"center\">\n            <Typography variant=\"body2\">\n              {bulkSelection.length} elementi selezionati\n            </Typography>\n            <Button\n              size=\"small\"\n              variant=\"outlined\"\n              onClick={selectAllItems}\n            >\n              Seleziona Tutto\n            </Button>\n            <Button\n              size=\"small\"\n              variant=\"outlined\"\n              onClick={clearSelection}\n            >\n              Deseleziona\n            </Button>\n            <Button\n              size=\"small\"\n              variant=\"outlined\"\n              startIcon={<ExportIcon />}\n              onClick={handleBulkExport}\n            >\n              Esporta Selezionati\n            </Button>\n            <Button\n              size=\"small\"\n              variant=\"outlined\"\n              color=\"error\"\n              startIcon={<DeleteIcon />}\n              onClick={handleBulkDelete}\n            >\n              Elimina Selezionati\n            </Button>\n          </Stack>\n        </>\n      )}\n    </Paper>\n  );\n\n  // Renderizza la tabella dei cavi\n  const renderCaviTable = () => {\n    const currentItems = getCurrentPageItems(filteredCavi);\n\n    if (filteredCavi.length === 0) {\n      return (\n        <Alert severity=\"info\">\n          {searchTerm || filters.stato || filters.tipologia\n            ? 'Nessun cavo trovato con i filtri applicati'\n            : 'Nessun cavo disponibile'}\n        </Alert>\n      );\n    }\n\n    return (\n      <>\n        <TableContainer component={Paper}>\n          <Table size=\"small\">\n            <TableHead>\n              <TableRow>\n                <TableCell>\n                  <Stack direction=\"row\" alignItems=\"center\" spacing={1}>\n                    <Typography variant=\"body2\" fontWeight=\"bold\">ID Cavo</Typography>\n                    <IconButton size=\"small\" onClick={() => {\n                      setSortBy('id_cavo');\n                      setSortOrder(sortOrder === 'asc' ? 'desc' : 'asc');\n                    }}>\n                      {sortBy === 'id_cavo' ? (sortOrder === 'asc' ? <ExpandLessIcon /> : <ExpandMoreIcon />) : <ExpandMoreIcon />}\n                    </IconButton>\n                  </Stack>\n                </TableCell>\n                <TableCell>Tipologia</TableCell>\n                <TableCell>Sezione</TableCell>\n                <TableCell>Partenza</TableCell>\n                <TableCell>Arrivo</TableCell>\n                <TableCell>Metri</TableCell>\n                <TableCell>Stato</TableCell>\n                <TableCell>Collegamenti</TableCell>\n                <TableCell>Certificato</TableCell>\n                <TableCell>Azioni</TableCell>\n              </TableRow>\n            </TableHead>\n            <TableBody>\n              {currentItems.map((cavo) => {\n                const isCertificato = isCavoCertificato(cavo.id_cavo);\n                const puoCertificare = puoEssereCertificato(cavo);\n                const messaggioErrore = !puoCertificare ? getMessaggioErroreCertificazione(cavo) : '';\n\n                return (\n                  <TableRow key={cavo.id_cavo}>\n                    <TableCell>\n                      <Typography variant=\"body2\" fontWeight=\"medium\">\n                        {cavo.id_cavo}\n                      </Typography>\n                    </TableCell>\n                    <TableCell>{cavo.tipologia}</TableCell>\n                    <TableCell>{cavo.sezione}</TableCell>\n                    <TableCell>{cavo.ubicazione_partenza}</TableCell>\n                    <TableCell>{cavo.ubicazione_arrivo}</TableCell>\n                    <TableCell>{cavo.metratura_reale || cavo.metri_teorici} m</TableCell>\n                    <TableCell>\n                      <Chip\n                        size=\"small\"\n                        label={cavo.stato_installazione}\n                        color={cavo.stato_installazione === 'INSTALLATO' ? 'success' : 'default'}\n                      />\n                    </TableCell>\n                    <TableCell>\n                      {(() => {\n                        const collegamenti = cavo.collegamenti || 0;\n                        const statoCollegamento = collegamenti === 0 ? 'Non collegato' :\n                                                 collegamenti === 1 ? 'Solo partenza' :\n                                                 collegamenti === 2 ? 'Solo arrivo' :\n                                                 collegamenti === 3 ? 'Completo' :\n                                                 'Sconosciuto';\n                        const colore = collegamenti === 3 ? 'success' :\n                                      collegamenti === 0 ? 'error' : 'warning';\n\n                        return (\n                          <Tooltip title={`Partenza: ${cavo.responsabile_partenza || 'Non collegato'} | Arrivo: ${cavo.responsabile_arrivo || 'Non collegato'}`}>\n                            <Chip\n                              size=\"small\"\n                              label={statoCollegamento}\n                              color={colore}\n                              icon={collegamenti === 3 ? <CheckIcon /> : <WarningIcon />}\n                            />\n                          </Tooltip>\n                        );\n                      })()}\n                    </TableCell>\n                    <TableCell>\n                      {isCertificato ? (\n                        <Chip\n                          size=\"small\"\n                          icon={<CheckIcon />}\n                          label=\"Certificato\"\n                          color=\"success\"\n                        />\n                      ) : (\n                        <Chip\n                          size=\"small\"\n                          icon={<WarningIcon />}\n                          label=\"Non certificato\"\n                          color=\"warning\"\n                        />\n                      )}\n                    </TableCell>\n                    <TableCell>\n                      {isCertificato ? (\n                        <Tooltip title=\"Cavo già certificato\">\n                          <Chip\n                            icon={<CheckIcon />}\n                            label=\"Certificato\"\n                            color=\"success\"\n                            size=\"small\"\n                          />\n                        </Tooltip>\n                      ) : puoCertificare ? (\n                        <Tooltip title=\"Crea certificazione per questo cavo\">\n                          <IconButton\n                            size=\"small\"\n                            onClick={() => openCreateDialog(cavo)}\n                            color=\"primary\"\n                          >\n                            <AddIcon />\n                          </IconButton>\n                        </Tooltip>\n                      ) : (\n                        <Tooltip title={messaggioErrore}>\n                          <span>\n                            <IconButton\n                              size=\"small\"\n                              disabled\n                              onClick={() => showSnackbar(messaggioErrore, 'warning')}\n                            >\n                              <BlockIcon />\n                            </IconButton>\n                          </span>\n                        </Tooltip>\n                      )}\n                    </TableCell>\n                  </TableRow>\n                );\n              })}\n            </TableBody>\n          </Table>\n        </TableContainer>\n\n        {getTotalPages(filteredCavi) > 1 && (\n          <Box sx={{ display: 'flex', justifyContent: 'center', mt: 2 }}>\n            <Pagination\n              count={getTotalPages(filteredCavi)}\n              page={currentPage}\n              onChange={(event, value) => setCurrentPage(value)}\n              color=\"primary\"\n            />\n          </Box>\n        )}\n      </>\n    );\n  };\n\n  // Renderizza la tabella delle certificazioni\n  const renderCertificazioniTable = () => {\n    const currentItems = getCurrentPageItems(filteredCertificazioni);\n\n    if (filteredCertificazioni.length === 0) {\n      return (\n        <Alert severity=\"info\">\n          {searchTerm || filters.operatore\n            ? 'Nessuna certificazione trovata con i filtri applicati'\n            : 'Nessuna certificazione disponibile'}\n        </Alert>\n      );\n    }\n\n    return (\n      <>\n        <TableContainer component={Paper}>\n          <Table size=\"small\">\n            <TableHead>\n              <TableRow>\n                {bulkMode && (\n                  <TableCell padding=\"checkbox\">\n                    <IconButton\n                      size=\"small\"\n                      onClick={bulkSelection.length === filteredCertificazioni.length ? clearSelection : selectAllItems}\n                    >\n                      {bulkSelection.length === filteredCertificazioni.length ? <ClearIcon /> : <CheckIcon />}\n                    </IconButton>\n                  </TableCell>\n                )}\n                <TableCell>\n                  <Stack direction=\"row\" alignItems=\"center\" spacing={1}>\n                    <Typography variant=\"body2\" fontWeight=\"bold\">N° Certificato</Typography>\n                    <IconButton size=\"small\" onClick={() => {\n                      setSortBy('numero_certificato');\n                      setSortOrder(sortOrder === 'asc' ? 'desc' : 'asc');\n                    }}>\n                      {sortBy === 'numero_certificato' ? (sortOrder === 'asc' ? <ExpandLessIcon /> : <ExpandMoreIcon />) : <ExpandMoreIcon />}\n                    </IconButton>\n                  </Stack>\n                </TableCell>\n                <TableCell>ID Cavo</TableCell>\n                <TableCell>\n                  <Stack direction=\"row\" alignItems=\"center\" spacing={1}>\n                    <Typography variant=\"body2\" fontWeight=\"bold\">Data</Typography>\n                    <IconButton size=\"small\" onClick={() => {\n                      setSortBy('data_certificazione');\n                      setSortOrder(sortOrder === 'asc' ? 'desc' : 'asc');\n                    }}>\n                      {sortBy === 'data_certificazione' ? (sortOrder === 'asc' ? <ExpandLessIcon /> : <ExpandMoreIcon />) : <ExpandMoreIcon />}\n                    </IconButton>\n                  </Stack>\n                </TableCell>\n                <TableCell>Operatore</TableCell>\n                <TableCell>Strumento</TableCell>\n                <TableCell>Lunghezza</TableCell>\n                <TableCell>Isolamento</TableCell>\n                <TableCell>Risultato</TableCell>\n                <TableCell>Azioni</TableCell>\n              </TableRow>\n            </TableHead>\n            <TableBody>\n              {currentItems.map((cert) => (\n                <TableRow\n                  key={cert.id_certificazione}\n                  selected={bulkSelection.includes(cert.id_certificazione)}\n                  hover\n                >\n                  {bulkMode && (\n                    <TableCell padding=\"checkbox\">\n                      <IconButton\n                        size=\"small\"\n                        onClick={() => toggleItemSelection(cert.id_certificazione)}\n                        color={bulkSelection.includes(cert.id_certificazione) ? 'primary' : 'default'}\n                      >\n                        {bulkSelection.includes(cert.id_certificazione) ? <CheckIcon /> : <AddIcon />}\n                      </IconButton>\n                    </TableCell>\n                  )}\n                  <TableCell>\n                    <Typography variant=\"body2\" fontWeight=\"medium\">\n                      {cert.numero_certificato}\n                    </Typography>\n                  </TableCell>\n                  <TableCell>\n                    <Chip size=\"small\" label={cert.id_cavo} variant=\"outlined\" />\n                  </TableCell>\n                  <TableCell>{new Date(cert.data_certificazione).toLocaleDateString()}</TableCell>\n                  <TableCell>\n                    <Stack direction=\"row\" alignItems=\"center\" spacing={1}>\n                      <PersonIcon fontSize=\"small\" />\n                      <Typography variant=\"body2\">{cert.operatore || cert.id_operatore}</Typography>\n                    </Stack>\n                  </TableCell>\n                  <TableCell>\n                    <Typography variant=\"body2\">\n                      {cert.id_strumento ?\n                        (() => {\n                          const strumento = strumenti.find(s => s.id_strumento === cert.id_strumento);\n                          return strumento ? `${strumento.nome} - ${strumento.marca}` : 'Strumento non trovato';\n                        })()\n                        : (cert.strumento_utilizzato || 'N/A')\n                      }\n                    </Typography>\n                  </TableCell>\n                  <TableCell>\n                    <Typography variant=\"body2\">{cert.lunghezza_misurata} m</Typography>\n                  </TableCell>\n                  <TableCell>\n                    <Chip\n                      size=\"small\"\n                      label={`${cert.valore_isolamento} MΩ`}\n                      color={parseFloat(cert.valore_isolamento) >= 500 ? 'success' : 'warning'}\n                      icon={parseFloat(cert.valore_isolamento) >= 500 ? <CheckIcon /> : <WarningIcon />}\n                    />\n                  </TableCell>\n                  <TableCell>\n                    <Chip\n                      size=\"small\"\n                      label={cert.risultato_finale || 'CONFORME'}\n                      color={cert.risultato_finale === 'CONFORME' ? 'success' : cert.risultato_finale === 'NON_CONFORME' ? 'error' : 'warning'}\n                    />\n                  </TableCell>\n                  <TableCell>\n                    <Stack direction=\"row\" spacing={0.5}>\n                      <Tooltip title=\"Visualizza dettagli\">\n                        <IconButton\n                          size=\"small\"\n                          onClick={() => {\n                            setSelectedItem(cert);\n                            setDialogType('view');\n                            setOpenDialog(true);\n                          }}\n                        >\n                          <ViewIcon />\n                        </IconButton>\n                      </Tooltip>\n                      <Tooltip title=\"Genera PDF\">\n                        <IconButton\n                          size=\"small\"\n                          onClick={() => handleGeneratePdf(cert)}\n                          disabled={operationInProgress}\n                        >\n                          <PdfIcon />\n                        </IconButton>\n                      </Tooltip>\n                      <Tooltip title=\"Elimina\">\n                        <IconButton\n                          size=\"small\"\n                          color=\"error\"\n                          onClick={() => handleDeleteCertificazione(cert)}\n                          disabled={operationInProgress}\n                        >\n                          <DeleteIcon />\n                        </IconButton>\n                      </Tooltip>\n                    </Stack>\n                  </TableCell>\n                </TableRow>\n              ))}\n            </TableBody>\n          </Table>\n        </TableContainer>\n\n        {getTotalPages(filteredCertificazioni) > 1 && (\n          <Box sx={{ display: 'flex', justifyContent: 'center', mt: 2 }}>\n            <Pagination\n              count={getTotalPages(filteredCertificazioni)}\n              page={currentPage}\n              onChange={(event, value) => setCurrentPage(value)}\n              color=\"primary\"\n            />\n          </Box>\n        )}\n      </>\n    );\n  };\n\n  // Renderizza il dialog per creare/modificare certificazione\n  const renderCertificazioneDialog = () => {\n    if (dialogType !== 'create' && dialogType !== 'edit') return null;\n\n    return (\n      <Dialog open={openDialog} onClose={closeDialog} maxWidth=\"md\" fullWidth>\n        <DialogTitle>\n          {dialogType === 'create' ? 'Nuova Certificazione' : 'Modifica Certificazione'}\n        </DialogTitle>\n        <DialogContent>\n          <Grid container spacing={2} sx={{ mt: 1 }}>\n            <Grid item xs={12} md={6}>\n              <Autocomplete\n                options={cavi.filter(cavo => {\n                  // Mostra solo cavi che possono essere certificati o quello già selezionato\n                  const isSelected = cavo.id_cavo === formData.id_cavo;\n                  const isNotCertified = !certificazioni.some(cert => cert.id_cavo === cavo.id_cavo);\n                  const canBeCertified = puoEssereCertificato(cavo);\n\n                  return isSelected || (isNotCertified && canBeCertified);\n                })}\n                getOptionLabel={(option) => `${option.id_cavo} - ${option.tipologia}`}\n                value={cavi.find(c => c.id_cavo === formData.id_cavo) || null}\n                onChange={(event, newValue) => {\n                  if (newValue) {\n                    handleCavoSelect(newValue);\n                  } else {\n                    setFormData(prev => ({ ...prev, id_cavo: '', lunghezza_misurata: '' }));\n                  }\n                }}\n                renderInput={(params) => (\n                  <TextField\n                    {...params}\n                    label=\"Cavo *\"\n                    placeholder=\"Seleziona un cavo posato\"\n                    required\n                    helperText=\"Solo cavi posati/installati (il collegamento può essere gestito al momento)\"\n                  />\n                )}\n                renderOption={(props, option) => {\n                  const collegamenti = option.collegamenti || 0;\n                  const isCollegato = collegamenti === 3;\n\n                  return (\n                    <Box component=\"li\" {...props}>\n                      <Box sx={{ width: '100%' }}>\n                        <Stack direction=\"row\" justifyContent=\"space-between\" alignItems=\"center\">\n                          <Box>\n                            <Typography variant=\"body2\" fontWeight=\"medium\">\n                              {option.id_cavo}\n                            </Typography>\n                            <Typography variant=\"caption\" color=\"text.secondary\">\n                              {option.tipologia} - {option.ubicazione_partenza} → {option.ubicazione_arrivo}\n                            </Typography>\n                          </Box>\n                          <Stack direction=\"row\" spacing={1}>\n                            <Chip\n                              size=\"small\"\n                              label={option.stato_installazione}\n                              color={option.stato_installazione === 'INSTALLATO' ? 'success' : 'default'}\n                            />\n                            <Chip\n                              size=\"small\"\n                              label={isCollegato ? 'Collegato' : 'Da collegare'}\n                              color={isCollegato ? 'success' : 'warning'}\n                              icon={isCollegato ? <CheckIcon /> : <WarningIcon />}\n                            />\n                          </Stack>\n                        </Stack>\n                      </Box>\n                    </Box>\n                  );\n                }}\n              />\n            </Grid>\n\n            <Grid item xs={12} md={6}>\n              <TextField\n                fullWidth\n                label=\"Operatore *\"\n                value={formData.id_operatore}\n                onChange={(e) => handleFormChange('id_operatore', e.target.value)}\n                required\n              />\n            </Grid>\n\n            <Grid item xs={12} md={6}>\n              <FormControl fullWidth required>\n                <InputLabel>Strumento *</InputLabel>\n                <Select\n                  value={formData.id_strumento}\n                  onChange={(e) => handleFormChange('id_strumento', e.target.value)}\n                  label=\"Strumento *\"\n                >\n                  {strumenti.map((strumento) => (\n                    <MenuItem key={strumento.id_strumento} value={strumento.id_strumento}>\n                      {strumento.nome} - {strumento.marca} {strumento.modello} (S/N: {strumento.numero_serie})\n                    </MenuItem>\n                  ))}\n                </Select>\n              </FormControl>\n            </Grid>\n\n            <Grid item xs={12} md={6}>\n              <TextField\n                fullWidth\n                label=\"Lunghezza Misurata (m) *\"\n                type=\"number\"\n                value={formData.lunghezza_misurata}\n                onChange={(e) => handleFormChange('lunghezza_misurata', e.target.value)}\n                required\n              />\n            </Grid>\n\n            <Grid item xs={12} md={4}>\n              <FormControl fullWidth>\n                <InputLabel>Continuità</InputLabel>\n                <Select\n                  value={formData.valore_continuita}\n                  onChange={(e) => handleFormChange('valore_continuita', e.target.value)}\n                  label=\"Continuità\"\n                >\n                  <MenuItem value=\"OK\">OK</MenuItem>\n                  <MenuItem value=\"NOK\">NOK</MenuItem>\n                </Select>\n              </FormControl>\n            </Grid>\n\n            <Grid item xs={12} md={4}>\n              <TextField\n                fullWidth\n                label=\"Isolamento (MΩ) *\"\n                type=\"number\"\n                value={formData.valore_isolamento}\n                onChange={(e) => handleFormChange('valore_isolamento', e.target.value)}\n                required\n                helperText=\"Valore minimo consigliato: 500 MΩ\"\n              />\n            </Grid>\n\n            <Grid item xs={12} md={4}>\n              <FormControl fullWidth>\n                <InputLabel>Resistenza</InputLabel>\n                <Select\n                  value={formData.valore_resistenza}\n                  onChange={(e) => handleFormChange('valore_resistenza', e.target.value)}\n                  label=\"Resistenza\"\n                >\n                  <MenuItem value=\"OK\">OK</MenuItem>\n                  <MenuItem value=\"NOK\">NOK</MenuItem>\n                </Select>\n              </FormControl>\n            </Grid>\n\n            {/* Sezione Collegamenti */}\n            <Grid item xs={12}>\n              <Divider sx={{ my: 2 }}>\n                <Typography variant=\"body2\" color=\"text.secondary\">\n                  Stato Collegamenti Cavo\n                </Typography>\n              </Divider>\n            </Grid>\n\n            {formData.id_cavo && (() => {\n              const cavo = cavi.find(c => c.id_cavo === formData.id_cavo);\n              if (!cavo) return null;\n\n              const collegamenti = cavo.collegamenti || 0;\n              const isCollegato = collegamenti === 3;\n\n              return (\n                <Grid item xs={12}>\n                  <Paper sx={{ p: 2, bgcolor: isCollegato ? 'success.light' : 'warning.light' }}>\n                    <Stack direction=\"row\" alignItems=\"center\" spacing={2}>\n                      {isCollegato ? <CheckIcon color=\"success\" /> : <WarningIcon color=\"warning\" />}\n                      <Box>\n                        <Typography variant=\"body2\" fontWeight=\"bold\">\n                          {isCollegato ? 'Cavo Completamente Collegato' : 'Cavo Non Completamente Collegato'}\n                        </Typography>\n                        <Typography variant=\"caption\">\n                          Stato: {collegamenti === 0 ? 'Non collegato' :\n                                  collegamenti === 1 ? 'Solo partenza collegata' :\n                                  collegamenti === 2 ? 'Solo arrivo collegato' :\n                                  collegamenti === 3 ? 'Completamente collegato' :\n                                  'Stato sconosciuto'}\n                        </Typography>\n                        {!isCollegato && (\n                          <Typography variant=\"caption\" display=\"block\" sx={{ mt: 1 }}>\n                            ⚠️ Il cavo può essere certificato ma ricorda di completare i collegamenti prima della messa in servizio\n                          </Typography>\n                        )}\n                      </Box>\n                    </Stack>\n                  </Paper>\n                </Grid>\n              );\n            })()}\n\n            {/* Campi avanzati */}\n            <Grid item xs={12}>\n              <Divider sx={{ my: 2 }}>\n                <Typography variant=\"body2\" color=\"text.secondary\">\n                  Parametri Ambientali e Test Avanzati\n                </Typography>\n              </Divider>\n            </Grid>\n\n            <Grid item xs={12} md={3}>\n              <TextField\n                fullWidth\n                label=\"Temperatura Ambiente (°C)\"\n                type=\"number\"\n                value={formData.temperatura_ambiente}\n                onChange={(e) => handleFormChange('temperatura_ambiente', e.target.value)}\n                helperText=\"Temperatura durante il test\"\n              />\n            </Grid>\n\n            <Grid item xs={12} md={3}>\n              <TextField\n                fullWidth\n                label=\"Umidità (%)\"\n                type=\"number\"\n                value={formData.umidita}\n                onChange={(e) => handleFormChange('umidita', e.target.value)}\n                helperText=\"Umidità relativa\"\n              />\n            </Grid>\n\n            <Grid item xs={12} md={3}>\n              <TextField\n                fullWidth\n                label=\"Tensione di Prova (V)\"\n                type=\"number\"\n                value={formData.tensione_prova}\n                onChange={(e) => handleFormChange('tensione_prova', e.target.value)}\n                helperText=\"Tensione applicata per il test\"\n              />\n            </Grid>\n\n            <Grid item xs={12} md={3}>\n              <TextField\n                fullWidth\n                label=\"Durata Prova (min)\"\n                type=\"number\"\n                value={formData.durata_prova}\n                onChange={(e) => handleFormChange('durata_prova', e.target.value)}\n                helperText=\"Durata del test in minuti\"\n              />\n            </Grid>\n\n            <Grid item xs={12} md={6}>\n              <FormControl fullWidth>\n                <InputLabel>Risultato Finale</InputLabel>\n                <Select\n                  value={formData.risultato_finale}\n                  onChange={(e) => handleFormChange('risultato_finale', e.target.value)}\n                  label=\"Risultato Finale\"\n                >\n                  <MenuItem value=\"CONFORME\">\n                    <Stack direction=\"row\" alignItems=\"center\" spacing={1}>\n                      <CheckIcon color=\"success\" />\n                      <Typography>Conforme</Typography>\n                    </Stack>\n                  </MenuItem>\n                  <MenuItem value=\"NON_CONFORME\">\n                    <Stack direction=\"row\" alignItems=\"center\" spacing={1}>\n                      <ErrorIcon color=\"error\" />\n                      <Typography>Non Conforme</Typography>\n                    </Stack>\n                  </MenuItem>\n                  <MenuItem value=\"DA_VERIFICARE\">\n                    <Stack direction=\"row\" alignItems=\"center\" spacing={1}>\n                      <WarningIcon color=\"warning\" />\n                      <Typography>Da Verificare</Typography>\n                    </Stack>\n                  </MenuItem>\n                </Select>\n              </FormControl>\n            </Grid>\n\n            <Grid item xs={12}>\n              <TextField\n                fullWidth\n                label=\"Note\"\n                multiline\n                rows={3}\n                value={formData.note}\n                onChange={(e) => handleFormChange('note', e.target.value)}\n                placeholder=\"Inserisci eventuali note, osservazioni o anomalie riscontrate durante il test...\"\n              />\n            </Grid>\n          </Grid>\n        </DialogContent>\n        <DialogActions>\n          <Button onClick={closeDialog}>Annulla</Button>\n          <Button\n            onClick={handleCreateCertificazione}\n            variant=\"contained\"\n            disabled={loading || !formData.id_cavo || !formData.id_operatore || !formData.id_strumento || !formData.valore_isolamento}\n            startIcon={loading ? <CircularProgress size={20} /> : <SaveIcon />}\n          >\n            {dialogType === 'create' ? 'Crea Certificazione' : 'Salva Modifiche'}\n          </Button>\n        </DialogActions>\n      </Dialog>\n    );\n  };\n\n  // Renderizza il dialog di visualizzazione dettagli\n  const renderViewDialog = () => {\n    if (dialogType !== 'view' || !selectedItem) return null;\n\n    return (\n      <Dialog open={openDialog} onClose={closeDialog} maxWidth=\"md\" fullWidth>\n        <DialogTitle>\n          Dettagli Certificazione - {selectedItem.numero_certificato}\n        </DialogTitle>\n        <DialogContent>\n          <Grid container spacing={2} sx={{ mt: 1 }}>\n            <Grid item xs={12} md={6}>\n              <Card variant=\"outlined\">\n                <CardContent>\n                  <Typography variant=\"h6\" gutterBottom>\n                    Informazioni Cavo\n                  </Typography>\n                  <Typography variant=\"body2\" color=\"text.secondary\">\n                    ID Cavo: <strong>{selectedItem.id_cavo}</strong>\n                  </Typography>\n                  <Typography variant=\"body2\" color=\"text.secondary\">\n                    Lunghezza Misurata: <strong>{selectedItem.lunghezza_misurata} m</strong>\n                  </Typography>\n                </CardContent>\n              </Card>\n            </Grid>\n\n            <Grid item xs={12} md={6}>\n              <Card variant=\"outlined\">\n                <CardContent>\n                  <Typography variant=\"h6\" gutterBottom>\n                    Informazioni Certificazione\n                  </Typography>\n                  <Typography variant=\"body2\" color=\"text.secondary\">\n                    Numero: <strong>{selectedItem.numero_certificato}</strong>\n                  </Typography>\n                  <Typography variant=\"body2\" color=\"text.secondary\">\n                    Data: <strong>{new Date(selectedItem.data_certificazione).toLocaleDateString()}</strong>\n                  </Typography>\n                  <Typography variant=\"body2\" color=\"text.secondary\">\n                    Operatore: <strong>{selectedItem.operatore || selectedItem.id_operatore}</strong>\n                  </Typography>\n                </CardContent>\n              </Card>\n            </Grid>\n\n            <Grid item xs={12}>\n              <Card variant=\"outlined\">\n                <CardContent>\n                  <Typography variant=\"h6\" gutterBottom>\n                    Risultati Test\n                  </Typography>\n                  <Grid container spacing={2}>\n                    <Grid item xs={4}>\n                      <Typography variant=\"body2\" color=\"text.secondary\">\n                        Continuità\n                      </Typography>\n                      <Chip\n                        size=\"small\"\n                        label={selectedItem.valore_continuita}\n                        color={selectedItem.valore_continuita === 'OK' ? 'success' : 'error'}\n                      />\n                    </Grid>\n                    <Grid item xs={4}>\n                      <Typography variant=\"body2\" color=\"text.secondary\">\n                        Isolamento\n                      </Typography>\n                      <Chip\n                        size=\"small\"\n                        label={`${selectedItem.valore_isolamento} MΩ`}\n                        color={parseFloat(selectedItem.valore_isolamento) >= 500 ? 'success' : 'warning'}\n                      />\n                    </Grid>\n                    <Grid item xs={4}>\n                      <Typography variant=\"body2\" color=\"text.secondary\">\n                        Resistenza\n                      </Typography>\n                      <Chip\n                        size=\"small\"\n                        label={selectedItem.valore_resistenza}\n                        color={selectedItem.valore_resistenza === 'OK' ? 'success' : 'error'}\n                      />\n                    </Grid>\n                  </Grid>\n                </CardContent>\n              </Card>\n            </Grid>\n\n            {selectedItem.note && (\n              <Grid item xs={12}>\n                <Card variant=\"outlined\">\n                  <CardContent>\n                    <Typography variant=\"h6\" gutterBottom>\n                      Note\n                    </Typography>\n                    <Typography variant=\"body2\">\n                      {selectedItem.note}\n                    </Typography>\n                  </CardContent>\n                </Card>\n              </Grid>\n            )}\n          </Grid>\n        </DialogContent>\n        <DialogActions>\n          <Button onClick={closeDialog}>Chiudi</Button>\n          <Button\n            onClick={() => handleGeneratePdf(selectedItem)}\n            variant=\"contained\"\n            startIcon={<PdfIcon />}\n            disabled={loading}\n          >\n            Genera PDF\n          </Button>\n        </DialogActions>\n      </Dialog>\n    );\n  };\n\n  // Renderizza le statistiche\n  const renderStats = () => {\n    const totalCavi = cavi.length;\n    const caviInstallati = cavi.filter(c => c.stato_installazione === 'INSTALLATO').length;\n    const caviCertificati = certificazioni.length;\n    const percentualeCertificazione = totalCavi > 0 ? Math.round((caviCertificati / caviInstallati) * 100) : 0;\n\n    return (\n      <Grid container spacing={2} sx={{ mb: 3 }}>\n        <Grid item xs={12} sm={6} md={3}>\n          <Card>\n            <CardContent>\n              <Typography color=\"text.secondary\" gutterBottom>\n                Cavi Totali\n              </Typography>\n              <Typography variant=\"h4\">\n                {totalCavi}\n              </Typography>\n            </CardContent>\n          </Card>\n        </Grid>\n        <Grid item xs={12} sm={6} md={3}>\n          <Card>\n            <CardContent>\n              <Typography color=\"text.secondary\" gutterBottom>\n                Cavi Installati\n              </Typography>\n              <Typography variant=\"h4\">\n                {caviInstallati}\n              </Typography>\n            </CardContent>\n          </Card>\n        </Grid>\n        <Grid item xs={12} sm={6} md={3}>\n          <Card>\n            <CardContent>\n              <Typography color=\"text.secondary\" gutterBottom>\n                Certificazioni\n              </Typography>\n              <Typography variant=\"h4\">\n                {caviCertificati}\n              </Typography>\n            </CardContent>\n          </Card>\n        </Grid>\n        <Grid item xs={12} sm={6} md={3}>\n          <Card>\n            <CardContent>\n              <Typography color=\"text.secondary\" gutterBottom>\n                % Certificazione\n              </Typography>\n              <Typography variant=\"h4\" color={percentualeCertificazione >= 80 ? 'success.main' : 'warning.main'}>\n                {percentualeCertificazione}%\n              </Typography>\n            </CardContent>\n          </Card>\n        </Grid>\n      </Grid>\n    );\n  };\n\n  return (\n    <Container maxWidth=\"xl\" sx={{ py: 3 }}>\n      {/* Dashboard con statistiche */}\n      {renderDashboard()}\n\n      {/* Progress bar per operazioni in corso */}\n      {(loading || operationInProgress) && (\n        <Box sx={{ mb: 2 }}>\n          <LinearProgress />\n          {progress > 0 && (\n            <Typography variant=\"caption\" color=\"text.secondary\" sx={{ mt: 1 }}>\n              Caricamento... {progress}%\n            </Typography>\n          )}\n        </Box>\n      )}\n\n      {/* Tabs per navigazione */}\n      <Paper sx={{ mb: 3 }}>\n        <Tabs\n          value={activeTab}\n          onChange={handleTabChange}\n          indicatorColor=\"primary\"\n          textColor=\"primary\"\n          variant=\"fullWidth\"\n        >\n          <Tab\n            label={\n              <Box>\n                <Typography variant=\"body2\" fontWeight=\"bold\">\n                  Cavi da Certificare\n                </Typography>\n                <Typography variant=\"caption\" color=\"text.secondary\">\n                  {filteredCavi.length} cavi totali\n                </Typography>\n              </Box>\n            }\n          />\n          <Tab\n            label={\n              <Box>\n                <Typography variant=\"body2\" fontWeight=\"bold\">\n                  Certificazioni Completate\n                </Typography>\n                <Typography variant=\"caption\" color=\"text.secondary\">\n                  {filteredCertificazioni.length} certificazioni\n                </Typography>\n              </Box>\n            }\n          />\n        </Tabs>\n      </Paper>\n\n      {/* Barra di ricerca e filtri avanzati */}\n      {renderSearchAndFilters()}\n\n      {/* Contenuto delle tabs */}\n      {!loading && activeTab === 0 && renderCaviTable()}\n      {!loading && activeTab === 1 && renderCertificazioniTable()}\n\n      {/* Dialogs */}\n      {renderCertificazioneDialog()}\n      {renderViewDialog()}\n\n      {/* Snackbar per notifiche */}\n      <Snackbar\n        open={snackbar.open}\n        autoHideDuration={6000}\n        onClose={closeSnackbar}\n        anchorOrigin={{ vertical: 'bottom', horizontal: 'right' }}\n      >\n        <Alert onClose={closeSnackbar} severity={snackbar.severity} sx={{ width: '100%' }}>\n          {snackbar.message}\n        </Alert>\n      </Snackbar>\n\n\n    </Container>\n  );\n});\n\nexport default CertificazioneCaviImproved;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,EAAEC,WAAW,EAAEC,UAAU,EAAEC,mBAAmB,QAAQ,OAAO;AAChG,SACEC,GAAG,EACHC,UAAU,EACVC,MAAM,EACNC,KAAK,EACLC,IAAI,EACJC,IAAI,EACJC,WAAW,EACXC,MAAM,EACNC,WAAW,EACXC,aAAa,EACbC,aAAa,EACbC,SAAS,EACTC,WAAW,EACXC,UAAU,EACVC,MAAM,EACNC,QAAQ,EACRC,YAAY,EACZC,gBAAgB,EAChBC,KAAK,EACLC,KAAK,EACLC,SAAS,EACTC,SAAS,EACTC,cAAc,EACdC,SAAS,EACTC,QAAQ,EACRC,UAAU,EACVC,IAAI,EACJC,GAAG,EACHC,UAAU,EACVC,cAAc,EACdC,OAAO,EACPC,KAAK,EACLC,IAAI,EACJC,OAAO,EACPC,KAAK,EACLC,cAAc,EACdC,QAAQ,EACRC,IAAI,EACJC,QAAQ,EACRC,YAAY,EACZC,YAAY,EACZC,QAAQ,EACRC,MAAM,EACNC,OAAO,EACPC,SAAS,EACTC,GAAG,QACE,eAAe;AACtB,SACEC,GAAG,IAAIC,OAAO,EACdC,MAAM,IAAIC,UAAU,EACpBC,UAAU,IAAIC,UAAU,EACxBC,YAAY,IAAIC,OAAO,EACvBC,QAAQ,IAAIC,YAAY,EACxBC,UAAU,IAAIC,QAAQ,EACtBC,MAAM,IAAIC,UAAU,EACpBC,IAAI,IAAIC,QAAQ,EAChBC,IAAI,IAAIC,QAAQ,EAChBC,KAAK,IAAIC,SAAS,EAClBC,KAAK,IAAIC,SAAS,EAClBC,WAAW,IAAIC,SAAS,EACxBC,OAAO,IAAIC,WAAW,EACtBC,MAAM,IAAIC,UAAU,EACpBC,KAAK,IAAIC,SAAS,EAClBC,KAAK,IAAIC,SAAS,EAClBC,WAAW,IAAIC,UAAU,EACzBC,UAAU,IAAIC,UAAU,EACxBC,QAAQ,IAAIC,YAAY,EACxBC,OAAO,IAAIC,WAAW,EACtBC,UAAU,IAAIC,cAAc,EAC5BC,UAAU,IAAIC,cAAc,EAC5BC,IAAI,IAAIC,QAAQ,EAChBC,KAAK,IAAIC,SAAS,EAClBC,QAAQ,IAAIC,YAAY,EACxBC,MAAM,IAAIC,UAAU,EACpBC,KAAK,IAAIC,SAAS,EAClBC,OAAO,IAAIC,WAAW,EACtBC,KAAK,IAAIC,SAAS,QACb,qBAAqB;AAE5B,OAAOC,qBAAqB,MAAM,sCAAsC;AACxE,OAAOC,WAAW,MAAM,4BAA4B;AAAC,SAAAC,MAAA,IAAAC,OAAA,EAAAC,QAAA,IAAAC,SAAA;AAErD,MAAMC,0BAA0B,gBAAAC,EAAA,cAAGjH,UAAU,CAAAkH,EAAA,GAAAD,EAAA,CAAC,CAAC;EAAEE,UAAU;EAAEC,SAAS;EAAEC;AAAQ,CAAC,EAAEC,GAAG,KAAK;EAAAL,EAAA;EACzF;EACA,MAAM,CAACM,OAAO,EAAEC,UAAU,CAAC,GAAG3H,QAAQ,CAAC,KAAK,CAAC;EAC7C,MAAM,CAAC4H,SAAS,EAAEC,YAAY,CAAC,GAAG7H,QAAQ,CAAC,CAAC,CAAC;EAC7C,MAAM,CAAC8H,cAAc,EAAEC,iBAAiB,CAAC,GAAG/H,QAAQ,CAAC,EAAE,CAAC;EACxD,MAAM,CAACgI,IAAI,EAAEC,OAAO,CAAC,GAAGjI,QAAQ,CAAC,EAAE,CAAC;EACpC,MAAM,CAACkI,SAAS,EAAEC,YAAY,CAAC,GAAGnI,QAAQ,CAAC,EAAE,CAAC;;EAE9C;EACA,MAAM,CAACoI,UAAU,EAAEC,aAAa,CAAC,GAAGrI,QAAQ,CAAC,EAAE,CAAC;EAChD,MAAM,CAACsI,YAAY,EAAEC,eAAe,CAAC,GAAGvI,QAAQ,CAAC,EAAE,CAAC;EACpD,MAAM,CAACwI,sBAAsB,EAAEC,yBAAyB,CAAC,GAAGzI,QAAQ,CAAC,EAAE,CAAC;EACxE,MAAM,CAAC0I,mBAAmB,EAAEC,sBAAsB,CAAC,GAAG3I,QAAQ,CAAC,KAAK,CAAC;EACrE,MAAM,CAAC4I,OAAO,EAAEC,UAAU,CAAC,GAAG7I,QAAQ,CAAC;IACrC8I,KAAK,EAAE,EAAE;IACTC,SAAS,EAAE,EAAE;IACbC,SAAS,EAAE,EAAE;IACbC,UAAU,EAAE,EAAE;IACdC,QAAQ,EAAE,EAAE;IACZC,gBAAgB,EAAE,EAAE;IACpBC,aAAa,EAAE,EAAE;IACjBC,SAAS,EAAE,EAAE;IACbC,cAAc,EAAE;EAClB,CAAC,CAAC;;EAEF;EACA,MAAM,CAACC,WAAW,EAAEC,cAAc,CAAC,GAAGxJ,QAAQ,CAAC,CAAC,CAAC;EACjD,MAAM,CAACyJ,YAAY,EAAEC,eAAe,CAAC,GAAG1J,QAAQ,CAAC,EAAE,CAAC;EACpD,MAAM,CAAC2J,MAAM,EAAEC,SAAS,CAAC,GAAG5J,QAAQ,CAAC,SAAS,CAAC;EAC/C,MAAM,CAAC6J,SAAS,EAAEC,YAAY,CAAC,GAAG9J,QAAQ,CAAC,KAAK,CAAC,CAAC,CAAC;;EAEnD;EACA,MAAM,CAAC+J,UAAU,EAAEC,aAAa,CAAC,GAAGhK,QAAQ,CAAC,KAAK,CAAC;EACnD,MAAM,CAACiK,UAAU,EAAEC,aAAa,CAAC,GAAGlK,QAAQ,CAAC,EAAE,CAAC;EAChD,MAAM,CAACmK,YAAY,EAAEC,eAAe,CAAC,GAAGpK,QAAQ,CAAC,IAAI,CAAC;EACtD,MAAM,CAACqK,aAAa,EAAEC,gBAAgB,CAAC,GAAGtK,QAAQ,CAAC,EAAE,CAAC;EACtD,MAAM,CAACuK,QAAQ,EAAEC,WAAW,CAAC,GAAGxK,QAAQ,CAAC,KAAK,CAAC;;EAE/C;EACA,MAAM,CAACyK,QAAQ,EAAEC,WAAW,CAAC,GAAG1K,QAAQ,CAAC;IAAE2K,IAAI,EAAE,KAAK;IAAEC,OAAO,EAAE,EAAE;IAAEC,QAAQ,EAAE;EAAO,CAAC,CAAC;EACxF,MAAM,CAACC,QAAQ,EAAEC,WAAW,CAAC,GAAG/K,QAAQ,CAAC,CAAC,CAAC;EAC3C,MAAM,CAACgL,mBAAmB,EAAEC,sBAAsB,CAAC,GAAGjL,QAAQ,CAAC,KAAK,CAAC;;EAErE;EACA,MAAM,CAACkL,QAAQ,EAAEC,WAAW,CAAC,GAAGnL,QAAQ,CAAC;IACvCoL,OAAO,EAAE,EAAE;IACXC,YAAY,EAAE,EAAE;IAChBC,YAAY,EAAE,EAAE;IAChBC,kBAAkB,EAAE,EAAE;IACtBC,iBAAiB,EAAE,IAAI;IACvBC,iBAAiB,EAAE,EAAE;IACrBC,iBAAiB,EAAE,IAAI;IACvBC,IAAI,EAAE,EAAE;IACRC,oBAAoB,EAAE,EAAE;IACxBC,OAAO,EAAE,EAAE;IACXC,cAAc,EAAE,EAAE;IAClBC,YAAY,EAAE,EAAE;IAChBC,gBAAgB,EAAE;EACpB,CAAC,CAAC;;EAEF;EACA,MAAM,CAACC,UAAU,EAAEC,aAAa,CAAC,GAAGlM,QAAQ,CAAC;IAC3CmM,UAAU,EAAE,CAAC;IACbC,eAAe,EAAE,CAAC;IAClBC,kBAAkB,EAAE,CAAC;IACrBC,wBAAwB,EAAE,CAAC;IAC3BC,kBAAkB,EAAE,CAAC;IACrBC,uBAAuB,EAAE;EAC3B,CAAC,CAAC;;EAEF;EACAvM,SAAS,CAAC,MAAM;IACdwM,eAAe,CAAC,CAAC;EACnB,CAAC,EAAE,CAACnF,UAAU,CAAC,CAAC;;EAEhB;EACArH,SAAS,CAAC,MAAM;IACdyM,UAAU,CAAC,CAAC;EACd,CAAC,EAAE,CAAC1E,IAAI,EAAEI,UAAU,EAAEQ,OAAO,EAAEe,MAAM,EAAEE,SAAS,CAAC,CAAC;;EAElD;EACA5J,SAAS,CAAC,MAAM;IACd0M,oBAAoB,CAAC,CAAC;EACxB,CAAC,EAAE,CAAC7E,cAAc,EAAEM,UAAU,EAAEQ,OAAO,EAAEe,MAAM,EAAEE,SAAS,CAAC,CAAC;;EAE5D;EACA5J,SAAS,CAAC,MAAM;IACd2M,mBAAmB,CAAC,CAAC;EACvB,CAAC,EAAE,CAAC5E,IAAI,EAAEF,cAAc,CAAC,CAAC;;EAE1B;EACA7H,SAAS,CAAC,MAAM;IACd,IAAI2H,SAAS,KAAK,CAAC,EAAE;MACnB8E,UAAU,CAAC,CAAC;IACd,CAAC,MAAM,IAAI9E,SAAS,KAAK,CAAC,EAAE;MAC1B+E,oBAAoB,CAAC,CAAC;IACxB;EACF,CAAC,EAAE,CAAC/E,SAAS,EAAEI,IAAI,EAAEF,cAAc,CAAC,CAAC,CAAC,CAAC;;EAEvC,MAAM2E,eAAe,GAAG,MAAAA,CAAA,KAAY;IAClC,IAAI;MACF9E,UAAU,CAAC,IAAI,CAAC;MAChBoD,WAAW,CAAC,CAAC,CAAC;;MAEd;MACAA,WAAW,CAAC,EAAE,CAAC;MACf,MAAM8B,QAAQ,CAAC,CAAC;MAEhB9B,WAAW,CAAC,EAAE,CAAC;MACf,MAAM+B,kBAAkB,CAAC,CAAC;MAE1B/B,WAAW,CAAC,EAAE,CAAC;MACf,MAAMgC,aAAa,CAAC,CAAC;MAErBhC,WAAW,CAAC,GAAG,CAAC;MAChB6B,mBAAmB,CAAC,CAAC;IAEvB,CAAC,CAAC,OAAOI,KAAK,EAAE;MACdC,YAAY,CAAC,0CAA0C,EAAE,OAAO,CAAC;MACjEzF,OAAO,CAAC,0CAA0C,CAAC;IACrD,CAAC,SAAS;MACRG,UAAU,CAAC,KAAK,CAAC;MACjBoD,WAAW,CAAC,CAAC,CAAC;IAChB;EACF,CAAC;EAED,MAAM+B,kBAAkB,GAAG,MAAAA,CAAA,KAAY;IACrC,IAAI;MACF,MAAMI,IAAI,GAAG,MAAMrG,qBAAqB,CAACsG,iBAAiB,CAAC7F,UAAU,CAAC;MACtES,iBAAiB,CAACmF,IAAI,CAAC;MACvB,OAAOA,IAAI;IACb,CAAC,CAAC,OAAOF,KAAK,EAAE;MACdI,OAAO,CAACJ,KAAK,CAAC,8CAA8C,EAAEA,KAAK,CAAC;MACpE,MAAMA,KAAK;IACb;EACF,CAAC;EAED,MAAMH,QAAQ,GAAG,MAAAA,CAAA,KAAY;IAC3B,IAAI;MACF,MAAMK,IAAI,GAAG,MAAMpG,WAAW,CAACuG,OAAO,CAAC/F,UAAU,CAAC;MAClD;MACA,MAAMgG,UAAU,GAAGJ,IAAI,CAACK,IAAI,CAAC,CAACC,CAAC,EAAEC,CAAC,KAAK;QACrC;QACA,MAAMC,YAAY,GAAIC,EAAE,IAAK;UAC3B,MAAMC,KAAK,GAAGD,EAAE,CAACC,KAAK,CAAC,OAAO,CAAC;UAC/B,OAAOA,KAAK,GAAGC,QAAQ,CAACD,KAAK,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,GAAG,CAAC;QAC3C,CAAC;QACD,OAAOF,YAAY,CAACF,CAAC,CAACpC,OAAO,CAAC,GAAGsC,YAAY,CAACD,CAAC,CAACrC,OAAO,CAAC;MAC1D,CAAC,CAAC;MACFnD,OAAO,CAACqF,UAAU,CAAC;MACnB,OAAOA,UAAU;IACnB,CAAC,CAAC,OAAON,KAAK,EAAE;MACdI,OAAO,CAACJ,KAAK,CAAC,kCAAkC,EAAEA,KAAK,CAAC;MACxD,MAAMA,KAAK;IACb;EACF,CAAC;EAED,MAAMD,aAAa,GAAG,MAAAA,CAAA,KAAY;IAChC,IAAI;MACF,MAAMG,IAAI,GAAG,MAAMrG,qBAAqB,CAACiH,YAAY,CAACxG,UAAU,CAAC;MACjEa,YAAY,CAAC+E,IAAI,CAAC;MAClB,OAAOA,IAAI;IACb,CAAC,CAAC,OAAOF,KAAK,EAAE;MACdI,OAAO,CAACJ,KAAK,CAAC,yCAAyC,EAAEA,KAAK,CAAC;MAC/D,MAAMA,KAAK;IACb;EACF,CAAC;;EAED;EACA,MAAMe,oBAAoB,GAAG7N,WAAW,CAAE8N,IAAI,IAAK;IACjD;IACA,MAAMC,YAAY,GAAGD,IAAI,CAACE,mBAAmB,KAAK,YAAY,IAC1CF,IAAI,CAACE,mBAAmB,KAAK,YAAY,IACzCF,IAAI,CAACE,mBAAmB,KAAK,QAAQ;;IAEzD;IACA;IACA,OAAOD,YAAY;EACrB,CAAC,EAAE,EAAE,CAAC;;EAEN;EACA,MAAME,eAAe,GAAGjO,WAAW,CAAE8N,IAAI,IAAK;IAC5C,MAAMI,WAAW,GAAGJ,IAAI,CAACK,YAAY,KAAK,CAAC;IAC3C,MAAMC,eAAe,GAAGN,IAAI,CAACO,qBAAqB,IAAIP,IAAI,CAACQ,mBAAmB;IAC9E,OAAOJ,WAAW,IAAIE,eAAe;EACvC,CAAC,EAAE,EAAE,CAAC;;EAEN;EACA,MAAM1B,mBAAmB,GAAG1M,WAAW,CAAC,MAAM;IAC5C,IAAI,CAAC8H,IAAI,IAAI,CAACF,cAAc,EAAE;IAE9B,MAAMqE,UAAU,GAAGnE,IAAI,CAACyG,MAAM;IAC9B,MAAMrC,eAAe,GAAGtE,cAAc,CAAC2G,MAAM;IAC7C,MAAMpC,kBAAkB,GAAGF,UAAU,GAAGC,eAAe;IACvD,MAAME,wBAAwB,GAAGH,UAAU,GAAG,CAAC,GAAGuC,IAAI,CAACC,KAAK,CAAEvC,eAAe,GAAGD,UAAU,GAAI,GAAG,CAAC,GAAG,CAAC;;IAEtG;IACA,MAAMyC,IAAI,GAAG,IAAIC,IAAI,CAAC,CAAC,CAACC,YAAY,CAAC,CAAC;IACtC,MAAMvC,kBAAkB,GAAGzE,cAAc,CAACiH,MAAM,CAACC,IAAI,IACnD,IAAIH,IAAI,CAACG,IAAI,CAACC,mBAAmB,CAAC,CAACH,YAAY,CAAC,CAAC,KAAKF,IACxD,CAAC,CAACH,MAAM;;IAER;IACA,MAAMS,cAAc,GAAG,IAAIL,IAAI,CAAC,CAAC;IACjCK,cAAc,CAACC,OAAO,CAACD,cAAc,CAACE,OAAO,CAAC,CAAC,GAAG,CAAC,CAAC;IACpD,MAAM5C,uBAAuB,GAAG1E,cAAc,CAACiH,MAAM,CAACC,IAAI,IACxD,IAAIH,IAAI,CAACG,IAAI,CAACC,mBAAmB,CAAC,IAAIC,cACxC,CAAC,CAACT,MAAM;;IAER;IACA,MAAMY,iBAAiB,GAAGrH,IAAI,CAAC+G,MAAM,CAACf,IAAI,IAAID,oBAAoB,CAACC,IAAI,CAAC,CAAC,CAACS,MAAM;IAChF,MAAMa,oBAAoB,GAAGnD,UAAU,GAAGkD,iBAAiB;;IAE3D;IACA,MAAME,aAAa,GAAGvH,IAAI,CAAC+G,MAAM,CAACf,IAAI,IAAIG,eAAe,CAACH,IAAI,CAAC,CAAC,CAACS,MAAM;IAEvEvC,aAAa,CAAC;MACZC,UAAU;MACVC,eAAe;MACfC,kBAAkB;MAClBgD,iBAAiB;MACjBC,oBAAoB;MACpBC,aAAa;MACbjD,wBAAwB;MACxBC,kBAAkB;MAClBC;IACF,CAAC,CAAC;EACJ,CAAC,EAAE,CAACxE,IAAI,EAAEF,cAAc,EAAEiG,oBAAoB,EAAEI,eAAe,CAAC,CAAC;;EAEjE;EACA,MAAMlB,YAAY,GAAGA,CAACrC,OAAO,EAAEC,QAAQ,GAAG,MAAM,KAAK;IACnDH,WAAW,CAAC;MAAEC,IAAI,EAAE,IAAI;MAAEC,OAAO;MAAEC;IAAS,CAAC,CAAC;EAChD,CAAC;EAED,MAAM2E,aAAa,GAAGA,CAAA,KAAM;IAC1B9E,WAAW,CAAC;MAAE,GAAGD,QAAQ;MAAEE,IAAI,EAAE;IAAM,CAAC,CAAC;EAC3C,CAAC;EAED,MAAM+B,UAAU,GAAGA,CAAA,KAAM;IACvB,IAAI+C,QAAQ,GAAGzH,IAAI;;IAEnB;IACA,IAAII,UAAU,EAAE;MACd,MAAMsH,WAAW,GAAGtH,UAAU,CAACuH,WAAW,CAAC,CAAC;MAC5CF,QAAQ,GAAGA,QAAQ,CAACV,MAAM,CAACf,IAAI;QAAA,IAAA4B,eAAA,EAAAC,qBAAA,EAAAC,qBAAA,EAAAC,aAAA,EAAAC,aAAA;QAAA,OAC7BhC,IAAI,CAAC5C,OAAO,CAACuE,WAAW,CAAC,CAAC,CAACM,QAAQ,CAACP,WAAW,CAAC,MAAAE,eAAA,GAChD5B,IAAI,CAACjF,SAAS,cAAA6G,eAAA,uBAAdA,eAAA,CAAgBD,WAAW,CAAC,CAAC,CAACM,QAAQ,CAACP,WAAW,CAAC,OAAAG,qBAAA,GACnD7B,IAAI,CAACkC,mBAAmB,cAAAL,qBAAA,uBAAxBA,qBAAA,CAA0BF,WAAW,CAAC,CAAC,CAACM,QAAQ,CAACP,WAAW,CAAC,OAAAI,qBAAA,GAC7D9B,IAAI,CAACmC,iBAAiB,cAAAL,qBAAA,uBAAtBA,qBAAA,CAAwBH,WAAW,CAAC,CAAC,CAACM,QAAQ,CAACP,WAAW,CAAC,OAAAK,aAAA,GAC3D/B,IAAI,CAACoC,OAAO,cAAAL,aAAA,uBAAZA,aAAA,CAAcJ,WAAW,CAAC,CAAC,CAACM,QAAQ,CAACP,WAAW,CAAC,OAAAM,aAAA,GACjDhC,IAAI,CAACqC,OAAO,cAAAL,aAAA,uBAAZA,aAAA,CAAcL,WAAW,CAAC,CAAC,CAACM,QAAQ,CAACP,WAAW,CAAC;MAAA,CACnD,CAAC;IACH;;IAEA;IACA,IAAI9G,OAAO,CAACE,KAAK,EAAE;MACjB2G,QAAQ,GAAGA,QAAQ,CAACV,MAAM,CAACf,IAAI,IAAIA,IAAI,CAACE,mBAAmB,KAAKtF,OAAO,CAACE,KAAK,CAAC;IAChF;IACA,IAAIF,OAAO,CAACG,SAAS,EAAE;MACrB0G,QAAQ,GAAGA,QAAQ,CAACV,MAAM,CAACf,IAAI,IAAIA,IAAI,CAACjF,SAAS,KAAKH,OAAO,CAACG,SAAS,CAAC;IAC1E;;IAEA;IACA,IAAIH,OAAO,CAACU,cAAc,EAAE;MAC1B,IAAIV,OAAO,CAACU,cAAc,KAAK,aAAa,EAAE;QAC5CmG,QAAQ,GAAGA,QAAQ,CAACV,MAAM,CAACf,IAAI,IAC7BlG,cAAc,CAACwI,IAAI,CAACtB,IAAI,IAAIA,IAAI,CAAC5D,OAAO,KAAK4C,IAAI,CAAC5C,OAAO,CAC3D,CAAC;MACH,CAAC,MAAM,IAAIxC,OAAO,CAACU,cAAc,KAAK,iBAAiB,EAAE;QACvDmG,QAAQ,GAAGA,QAAQ,CAACV,MAAM,CAACf,IAAI,IAC7B,CAAClG,cAAc,CAACwI,IAAI,CAACtB,IAAI,IAAIA,IAAI,CAAC5D,OAAO,KAAK4C,IAAI,CAAC5C,OAAO,CAC5D,CAAC;MACH;IACF;;IAEA;IACAqE,QAAQ,CAAClC,IAAI,CAAC,CAACC,CAAC,EAAEC,CAAC,KAAK;MACtB,IAAI8C,MAAM,GAAG/C,CAAC,CAAC7D,MAAM,CAAC;MACtB,IAAI6G,MAAM,GAAG/C,CAAC,CAAC9D,MAAM,CAAC;;MAEtB;MACA,IAAIA,MAAM,KAAK,SAAS,EAAE;QACxB,MAAM+D,YAAY,GAAIC,EAAE,IAAK;UAC3B,MAAMC,KAAK,GAAGD,EAAE,CAACC,KAAK,CAAC,OAAO,CAAC;UAC/B,OAAOA,KAAK,GAAGC,QAAQ,CAACD,KAAK,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,GAAG,CAAC;QAC3C,CAAC;QACD,MAAM6C,IAAI,GAAG/C,YAAY,CAAC6C,MAAM,CAAC;QACjC,MAAMG,IAAI,GAAGhD,YAAY,CAAC8C,MAAM,CAAC;QAEjC,IAAI3G,SAAS,KAAK,KAAK,EAAE;UACvB,OAAO4G,IAAI,GAAGC,IAAI;QACpB,CAAC,MAAM;UACL,OAAOA,IAAI,GAAGD,IAAI;QACpB;MACF;;MAEA;MACA,IAAI,OAAOF,MAAM,KAAK,QAAQ,EAAE;QAC9BA,MAAM,GAAGA,MAAM,CAACZ,WAAW,CAAC,CAAC;QAC7Ba,MAAM,GAAGA,MAAM,CAACb,WAAW,CAAC,CAAC;MAC/B;MAEA,IAAI9F,SAAS,KAAK,KAAK,EAAE;QACvB,OAAO0G,MAAM,GAAGC,MAAM,GAAG,CAAC,GAAG,CAAC,CAAC;MACjC,CAAC,MAAM;QACL,OAAOD,MAAM,GAAGC,MAAM,GAAG,CAAC,GAAG,CAAC,CAAC;MACjC;IACF,CAAC,CAAC;IAEFjI,eAAe,CAACkH,QAAQ,CAAC;EAC3B,CAAC;EAED,MAAM9C,oBAAoB,GAAGA,CAAA,KAAM;IACjC,IAAI8C,QAAQ,GAAG3H,cAAc;;IAE7B;IACA,IAAIM,UAAU,EAAE;MACd,MAAMsH,WAAW,GAAGtH,UAAU,CAACuH,WAAW,CAAC,CAAC;MAC5CF,QAAQ,GAAGA,QAAQ,CAACV,MAAM,CAACC,IAAI;QAAA,IAAA2B,eAAA,EAAAC,qBAAA,EAAAC,UAAA;QAAA,OAC7B7B,IAAI,CAAC5D,OAAO,CAACuE,WAAW,CAAC,CAAC,CAACM,QAAQ,CAACP,WAAW,CAAC,MAAAiB,eAAA,GAChD3B,IAAI,CAAChG,SAAS,cAAA2H,eAAA,uBAAdA,eAAA,CAAgBhB,WAAW,CAAC,CAAC,CAACM,QAAQ,CAACP,WAAW,CAAC,OAAAkB,qBAAA,GACnD5B,IAAI,CAAC8B,kBAAkB,cAAAF,qBAAA,uBAAvBA,qBAAA,CAAyBjB,WAAW,CAAC,CAAC,CAACM,QAAQ,CAACP,WAAW,CAAC,OAAAmB,UAAA,GAC5D7B,IAAI,CAACrD,IAAI,cAAAkF,UAAA,uBAATA,UAAA,CAAWlB,WAAW,CAAC,CAAC,CAACM,QAAQ,CAACP,WAAW,CAAC;MAAA,CAChD,CAAC;IACH;;IAEA;IACA,IAAI9G,OAAO,CAACI,SAAS,EAAE;MACrByG,QAAQ,GAAGA,QAAQ,CAACV,MAAM,CAACC,IAAI,IAAIA,IAAI,CAAChG,SAAS,KAAKJ,OAAO,CAACI,SAAS,CAAC;IAC1E;IACA,IAAIJ,OAAO,CAACS,SAAS,EAAE;MACrBoG,QAAQ,GAAGA,QAAQ,CAACV,MAAM,CAACC,IAAI,IAAIA,IAAI,CAAC3F,SAAS,KAAKT,OAAO,CAACS,SAAS,CAAC;IAC1E;IACA,IAAIT,OAAO,CAACQ,aAAa,EAAE;MACzBqG,QAAQ,GAAGA,QAAQ,CAACV,MAAM,CAACC,IAAI,IAAIA,IAAI,CAAChD,gBAAgB,KAAKpD,OAAO,CAACQ,aAAa,CAAC;IACrF;IACA,IAAIR,OAAO,CAACK,UAAU,EAAE;MACtBwG,QAAQ,GAAGA,QAAQ,CAACV,MAAM,CAACC,IAAI,IAC7B,IAAIH,IAAI,CAACG,IAAI,CAACC,mBAAmB,CAAC,IAAI,IAAIJ,IAAI,CAACjG,OAAO,CAACK,UAAU,CACnE,CAAC;IACH;IACA,IAAIL,OAAO,CAACM,QAAQ,EAAE;MACpBuG,QAAQ,GAAGA,QAAQ,CAACV,MAAM,CAACC,IAAI,IAC7B,IAAIH,IAAI,CAACG,IAAI,CAACC,mBAAmB,CAAC,IAAI,IAAIJ,IAAI,CAACjG,OAAO,CAACM,QAAQ,CACjE,CAAC;IACH;IACA,IAAIN,OAAO,CAACO,gBAAgB,EAAE;MAC5B,MAAM4H,MAAM,GAAGC,UAAU,CAACpI,OAAO,CAACO,gBAAgB,CAAC;MACnDsG,QAAQ,GAAGA,QAAQ,CAACV,MAAM,CAACC,IAAI,IAC7BgC,UAAU,CAAChC,IAAI,CAACvD,iBAAiB,CAAC,IAAIsF,MACxC,CAAC;IACH;;IAEA;IACAtB,QAAQ,CAAClC,IAAI,CAAC,CAACC,CAAC,EAAEC,CAAC,KAAK;MACtB,IAAI8C,MAAM,GAAG/C,CAAC,CAAC7D,MAAM,CAAC;MACtB,IAAI6G,MAAM,GAAG/C,CAAC,CAAC9D,MAAM,CAAC;MAEtB,IAAIA,MAAM,KAAK,qBAAqB,EAAE;QACpC4G,MAAM,GAAG,IAAI1B,IAAI,CAAC0B,MAAM,CAAC;QACzBC,MAAM,GAAG,IAAI3B,IAAI,CAAC2B,MAAM,CAAC;MAC3B,CAAC,MAAM,IAAI,OAAOD,MAAM,KAAK,QAAQ,EAAE;QACrCA,MAAM,GAAGA,MAAM,CAACZ,WAAW,CAAC,CAAC;QAC7Ba,MAAM,GAAGA,MAAM,CAACb,WAAW,CAAC,CAAC;MAC/B;MAEA,IAAI9F,SAAS,KAAK,KAAK,EAAE;QACvB,OAAO0G,MAAM,GAAGC,MAAM,GAAG,CAAC,GAAG,CAAC,CAAC;MACjC,CAAC,MAAM;QACL,OAAOD,MAAM,GAAGC,MAAM,GAAG,CAAC,GAAG,CAAC,CAAC;MACjC;IACF,CAAC,CAAC;IAEF/H,yBAAyB,CAACgH,QAAQ,CAAC;EACrC,CAAC;;EAED;EACA,MAAMwB,cAAc,GAAGA,CAAA,KAAM;IAC3B,IAAIrJ,SAAS,KAAK,CAAC,EAAE;MACnBqF,YAAY,CAAC,gEAAgE,EAAE,SAAS,CAAC;MACzF;IACF;IACAzC,WAAW,CAAC,CAACD,QAAQ,CAAC;IACtBD,gBAAgB,CAAC,EAAE,CAAC;IACpB2C,YAAY,CACV,CAAC1C,QAAQ,GACL,4EAA4E,GAC5E,gCAAgC,EACpC,MACF,CAAC;EACH,CAAC;EAED,MAAM2G,mBAAmB,GAAIC,MAAM,IAAK;IACtC7G,gBAAgB,CAAC8G,IAAI,IAAI;MACvB,MAAMC,YAAY,GAAGD,IAAI,CAACnB,QAAQ,CAACkB,MAAM,CAAC,GACtCC,IAAI,CAACrC,MAAM,CAACpB,EAAE,IAAIA,EAAE,KAAKwD,MAAM,CAAC,GAChC,CAAC,GAAGC,IAAI,EAAED,MAAM,CAAC;MAErBlE,YAAY,CACV,GAAGoE,YAAY,CAAC5C,MAAM,6BAA6B,EACnD,MACF,CAAC;MACD,OAAO4C,YAAY;IACrB,CAAC,CAAC;EACJ,CAAC;EAED,MAAMC,cAAc,GAAGA,CAAA,KAAM;IAC3B,IAAI1J,SAAS,KAAK,CAAC,EAAE;IAErB,MAAM2J,MAAM,GAAG/I,sBAAsB,CAACgJ,GAAG,CAACxC,IAAI,IAAIA,IAAI,CAACyC,iBAAiB,CAAC;IACzEnH,gBAAgB,CAACiH,MAAM,CAAC;IACxBtE,YAAY,CAAC,YAAYsE,MAAM,CAAC9C,MAAM,6BAA6B,EAAE,SAAS,CAAC;EACjF,CAAC;EAED,MAAMiD,cAAc,GAAGA,CAAA,KAAM;IAC3BpH,gBAAgB,CAAC,EAAE,CAAC;IACpB2C,YAAY,CAAC,sBAAsB,EAAE,MAAM,CAAC;EAC9C,CAAC;;EAED;EACA,MAAM0E,iBAAiB,GAAIC,MAAM,IAAK;IACpC,OAAO9J,cAAc,CAACwI,IAAI,CAACtB,IAAI,IAAIA,IAAI,CAAC5D,OAAO,KAAKwG,MAAM,CAAC;EAC7D,CAAC;;EAED;EACA,MAAMC,gCAAgC,GAAI7D,IAAI,IAAK;IACjD,MAAMC,YAAY,GAAGD,IAAI,CAACE,mBAAmB,KAAK,YAAY,IAC1CF,IAAI,CAACE,mBAAmB,KAAK,YAAY,IACzCF,IAAI,CAACE,mBAAmB,KAAK,QAAQ;IAEzD,IAAI,CAACD,YAAY,EAAE;MACjB,OAAO,yEAAyE;IAClF;IAEA,OAAO,+CAA+C;EACxD,CAAC;;EAED;EACA,MAAM6D,eAAe,GAAGA,CAACC,KAAK,EAAEC,QAAQ,KAAK;IAC3CnK,YAAY,CAACmK,QAAQ,CAAC;IACtBxI,cAAc,CAAC,CAAC,CAAC;IACjBnB,aAAa,CAAC,EAAE,CAAC;IACjBQ,UAAU,CAAC;MAAEC,KAAK,EAAE,EAAE;MAAEC,SAAS,EAAE,EAAE;MAAEC,SAAS,EAAE;IAAG,CAAC,CAAC;EACzD,CAAC;;EAED;EACA,MAAMiJ,gBAAgB,GAAGA,CAACC,kBAAkB,GAAG,IAAI,KAAK;IACtDhI,aAAa,CAAC,QAAQ,CAAC;IACvBE,eAAe,CAAC,IAAI,CAAC;;IAErB;IACA,IAAI8H,kBAAkB,EAAE;MACtB/G,WAAW,CAAC;QACVC,OAAO,EAAE8G,kBAAkB,CAAC9G,OAAO;QACnCC,YAAY,EAAE,EAAE;QAChBC,YAAY,EAAE,EAAE;QAChBC,kBAAkB,EAAE2G,kBAAkB,CAACC,eAAe,IAAID,kBAAkB,CAACE,aAAa,IAAI,EAAE;QAChG5G,iBAAiB,EAAE,IAAI;QACvBC,iBAAiB,EAAE,EAAE;QACrBC,iBAAiB,EAAE,IAAI;QACvBC,IAAI,EAAE,EAAE;QACRC,oBAAoB,EAAE,EAAE;QACxBC,OAAO,EAAE,EAAE;QACXC,cAAc,EAAE,EAAE;QAClBC,YAAY,EAAE,EAAE;QAChBC,gBAAgB,EAAE;MACpB,CAAC,CAAC;MACFiB,YAAY,CAAC,QAAQiF,kBAAkB,CAAC9G,OAAO,8BAA8B,EAAE,SAAS,CAAC;IAC3F,CAAC,MAAM;MACL;MACAD,WAAW,CAAC;QACVC,OAAO,EAAE,EAAE;QACXC,YAAY,EAAE,EAAE;QAChBC,YAAY,EAAE,EAAE;QAChBC,kBAAkB,EAAE,EAAE;QACtBC,iBAAiB,EAAE,IAAI;QACvBC,iBAAiB,EAAE,EAAE;QACrBC,iBAAiB,EAAE,IAAI;QACvBC,IAAI,EAAE,EAAE;QACRC,oBAAoB,EAAE,EAAE;QACxBC,OAAO,EAAE,EAAE;QACXC,cAAc,EAAE,EAAE;QAClBC,YAAY,EAAE,EAAE;QAChBC,gBAAgB,EAAE;MACpB,CAAC,CAAC;IACJ;IAEAhC,aAAa,CAAC,IAAI,CAAC;EACrB,CAAC;EAED,MAAMqI,WAAW,GAAGA,CAAA,KAAM;IACxBrI,aAAa,CAAC,KAAK,CAAC;IACpBI,eAAe,CAAC,IAAI,CAAC;IACrBF,aAAa,CAAC,EAAE,CAAC;EACnB,CAAC;;EAED;EACA,MAAMoI,gBAAgB,GAAGA,CAACC,KAAK,EAAEC,KAAK,KAAK;IACzCrH,WAAW,CAACiG,IAAI,KAAK;MACnB,GAAGA,IAAI;MACP,CAACmB,KAAK,GAAGC;IACX,CAAC,CAAC,CAAC;EACL,CAAC;EAED,MAAMC,gBAAgB,GAAIzE,IAAI,IAAK;IACjC7C,WAAW,CAACiG,IAAI,KAAK;MACnB,GAAGA,IAAI;MACPhG,OAAO,EAAE4C,IAAI,CAAC5C,OAAO;MACrBG,kBAAkB,EAAEyC,IAAI,CAACmE,eAAe,IAAInE,IAAI,CAACoE,aAAa,IAAI;IACpE,CAAC,CAAC,CAAC;EACL,CAAC;;EAED;EACA,MAAMM,0BAA0B,GAAG,MAAAA,CAAA,KAAY;IAC7C,IAAI;MACF,IAAI,CAACxH,QAAQ,CAACE,OAAO,IAAI,CAACF,QAAQ,CAACG,YAAY,IAAI,CAACH,QAAQ,CAACI,YAAY,IAAI,CAACJ,QAAQ,CAACO,iBAAiB,EAAE;QACxGwB,YAAY,CAAC,mCAAmC,EAAE,SAAS,CAAC;QAC5D;MACF;;MAEA;MACA,MAAMe,IAAI,GAAGhG,IAAI,CAAC2K,IAAI,CAACC,CAAC,IAAIA,CAAC,CAACxH,OAAO,KAAKF,QAAQ,CAACE,OAAO,CAAC;MAC3D,IAAI,CAAC4C,IAAI,EAAE;QACTf,YAAY,CAAC,kBAAkB,EAAE,OAAO,CAAC;QACzC;MACF;MAEA,IAAI,CAACc,oBAAoB,CAACC,IAAI,CAAC,EAAE;QAC/B,MAAM6E,SAAS,GAAGhB,gCAAgC,CAAC7D,IAAI,CAAC;QACxDf,YAAY,CAAC,oCAAoC4F,SAAS,EAAE,EAAE,OAAO,CAAC;QACtE;MACF;;MAEA;MACA,IAAIlB,iBAAiB,CAACzG,QAAQ,CAACE,OAAO,CAAC,EAAE;QACvC6B,YAAY,CAAC,iCAAiC,EAAE,SAAS,CAAC;QAC1D;MACF;;MAEA;MACA,IAAI,CAACkB,eAAe,CAACH,IAAI,CAAC,EAAE;QAC1B,MAAM8E,QAAQ,GAAGC,MAAM,CAACC,OAAO,CAC7B,uBAAuBhF,IAAI,CAAC5C,OAAO,2CAA2C,GAC9E,uBAAuB4C,IAAI,CAACK,YAAY,KAAK,CAAC,GAAG,eAAe,GAC1CL,IAAI,CAACK,YAAY,KAAK,CAAC,GAAG,yBAAyB,GACnDL,IAAI,CAACK,YAAY,KAAK,CAAC,GAAG,uBAAuB,GACjD,mBAAmB,MAAM,GAC/C,kDAAkD,GAClD,sEACF,CAAC;QAED,IAAI,CAACyE,QAAQ,EAAE;UACb;QACF;MACF;MAEA7H,sBAAsB,CAAC,IAAI,CAAC;MAC5B,MAAMpE,qBAAqB,CAACoM,oBAAoB,CAAC3L,UAAU,EAAE4D,QAAQ,CAAC;MACtE+B,YAAY,CAAC,oCAAoC,EAAE,SAAS,CAAC;MAC7DoF,WAAW,CAAC,CAAC;MACb,MAAMvF,kBAAkB,CAAC,CAAC;MAC1BF,mBAAmB,CAAC,CAAC;IACvB,CAAC,CAAC,OAAOI,KAAK,EAAE;MACdC,YAAY,CAAC,+CAA+C,IAAID,KAAK,CAACpC,OAAO,IAAI,oBAAoB,CAAC,EAAE,OAAO,CAAC;IAClH,CAAC,SAAS;MACRK,sBAAsB,CAAC,KAAK,CAAC;IAC/B;EACF,CAAC;EAED,MAAMiI,iBAAiB,GAAG,MAAO5J,cAAc,IAAK;IAClD,IAAI;MACF2B,sBAAsB,CAAC,IAAI,CAAC;MAC5BgC,YAAY,CAAC,6BAA6B,EAAE,MAAM,CAAC;MAEnD,MAAMkG,QAAQ,GAAG,MAAMtM,qBAAqB,CAACuM,WAAW,CAAC9L,UAAU,EAAEgC,cAAc,CAACmI,iBAAiB,CAAC;MAEtG,IAAI0B,QAAQ,CAACE,QAAQ,EAAE;QACrB;QACA,MAAMC,SAAS,GAAGP,MAAM,CAACpI,IAAI,CAACwI,QAAQ,CAACE,QAAQ,EAAE,QAAQ,CAAC;QAC1D,IAAIC,SAAS,EAAE;UACbrG,YAAY,CAAC,6CAA6C,EAAE,SAAS,CAAC;QACxE,CAAC,MAAM;UACL;UACA,MAAMsG,IAAI,GAAGC,QAAQ,CAACC,aAAa,CAAC,GAAG,CAAC;UACxCF,IAAI,CAACG,IAAI,GAAGP,QAAQ,CAACE,QAAQ;UAC7BE,IAAI,CAACI,QAAQ,GAAG,kBAAkBrK,cAAc,CAACwH,kBAAkB,MAAM;UACzE0C,QAAQ,CAACI,IAAI,CAACC,WAAW,CAACN,IAAI,CAAC;UAC/BA,IAAI,CAACO,KAAK,CAAC,CAAC;UACZN,QAAQ,CAACI,IAAI,CAACG,WAAW,CAACR,IAAI,CAAC;UAC/BtG,YAAY,CAAC,uCAAuC,EAAE,SAAS,CAAC;QAClE;MACF,CAAC,MAAM,IAAIkG,QAAQ,CAACa,WAAW,EAAE;QAC/B;QACA,MAAMC,IAAI,GAAG,IAAIC,IAAI,CAAC,CAACC,IAAI,CAAChB,QAAQ,CAACa,WAAW,CAAC,CAAC,EAAE;UAAEI,IAAI,EAAE;QAAkB,CAAC,CAAC;QAChF,MAAMC,GAAG,GAAGC,GAAG,CAACC,eAAe,CAACN,IAAI,CAAC;QACrC,MAAMV,IAAI,GAAGC,QAAQ,CAACC,aAAa,CAAC,GAAG,CAAC;QACxCF,IAAI,CAACG,IAAI,GAAGW,GAAG;QACfd,IAAI,CAACI,QAAQ,GAAG,kBAAkBrK,cAAc,CAACwH,kBAAkB,MAAM;QACzE0C,QAAQ,CAACI,IAAI,CAACC,WAAW,CAACN,IAAI,CAAC;QAC/BA,IAAI,CAACO,KAAK,CAAC,CAAC;QACZN,QAAQ,CAACI,IAAI,CAACG,WAAW,CAACR,IAAI,CAAC;QAC/Be,GAAG,CAACE,eAAe,CAACH,GAAG,CAAC;QACxBpH,YAAY,CAAC,4BAA4B,EAAE,SAAS,CAAC;MACvD,CAAC,MAAM;QACLA,YAAY,CAAC,sCAAsC,EAAE,OAAO,CAAC;MAC/D;IACF,CAAC,CAAC,OAAOD,KAAK,EAAE;MACdI,OAAO,CAACJ,KAAK,CAAC,yBAAyB,EAAEA,KAAK,CAAC;MAC/CC,YAAY,CAAC,oCAAoC,IAAID,KAAK,CAACpC,OAAO,IAAI,oBAAoB,CAAC,EAAE,OAAO,CAAC;IACvG,CAAC,SAAS;MACRK,sBAAsB,CAAC,KAAK,CAAC;IAC/B;EACF,CAAC;EAED,MAAMwJ,0BAA0B,GAAG,MAAOnL,cAAc,IAAK;IAC3D,IAAIyJ,MAAM,CAACC,OAAO,CAAC,mDAAmD1J,cAAc,CAACwH,kBAAkB,GAAG,CAAC,EAAE;MAC3G,IAAI;QACF7F,sBAAsB,CAAC,IAAI,CAAC;QAC5B,MAAMpE,qBAAqB,CAAC6N,oBAAoB,CAACpN,UAAU,EAAEgC,cAAc,CAACmI,iBAAiB,CAAC;QAC9FxE,YAAY,CAAC,uCAAuC,EAAE,SAAS,CAAC;QAChE,MAAMH,kBAAkB,CAAC,CAAC;QAC1BF,mBAAmB,CAAC,CAAC;MACvB,CAAC,CAAC,OAAOI,KAAK,EAAE;QACdC,YAAY,CAAC,kDAAkD,IAAID,KAAK,CAACpC,OAAO,IAAI,oBAAoB,CAAC,EAAE,OAAO,CAAC;MACrH,CAAC,SAAS;QACRK,sBAAsB,CAAC,KAAK,CAAC;MAC/B;IACF;EACF,CAAC;;EAED;EACA,MAAM0J,gBAAgB,GAAG,MAAAA,CAAA,KAAY;IACnC,IAAItK,aAAa,CAACoE,MAAM,KAAK,CAAC,EAAE;MAC9BxB,YAAY,CAAC,8BAA8B,EAAE,SAAS,CAAC;MACvD;IACF;IAEA,IAAI8F,MAAM,CAACC,OAAO,CAAC,iCAAiC3I,aAAa,CAACoE,MAAM,kBAAkB,CAAC,EAAE;MAC3F,IAAI;QACFxD,sBAAsB,CAAC,IAAI,CAAC;QAC5B,KAAK,MAAM0C,EAAE,IAAItD,aAAa,EAAE;UAC9B,MAAMxD,qBAAqB,CAAC6N,oBAAoB,CAACpN,UAAU,EAAEqG,EAAE,CAAC;QAClE;QACAV,YAAY,CAAC,GAAG5C,aAAa,CAACoE,MAAM,wCAAwC,EAAE,SAAS,CAAC;QACxFnE,gBAAgB,CAAC,EAAE,CAAC;QACpB,MAAMwC,kBAAkB,CAAC,CAAC;QAC1BF,mBAAmB,CAAC,CAAC;MACvB,CAAC,CAAC,OAAOI,KAAK,EAAE;QACdC,YAAY,CAAC,gDAAgD,EAAE,OAAO,CAAC;MACzE,CAAC,SAAS;QACRhC,sBAAsB,CAAC,KAAK,CAAC;MAC/B;IACF;EACF,CAAC;EAED,MAAM2J,gBAAgB,GAAG,MAAAA,CAAA,KAAY;IACnC,IAAIvK,aAAa,CAACoE,MAAM,KAAK,CAAC,EAAE;MAC9BxB,YAAY,CAAC,8BAA8B,EAAE,SAAS,CAAC;MACvD;IACF;IAEA,IAAI;MACFhC,sBAAsB,CAAC,IAAI,CAAC;MAC5B;MACA,MAAM4J,aAAa,GAAG/M,cAAc,CAACiH,MAAM,CAACC,IAAI,IAC9C3E,aAAa,CAAC4F,QAAQ,CAACjB,IAAI,CAACyC,iBAAiB,CAC/C,CAAC;;MAED;MACA,MAAMqD,UAAU,GAAGC,WAAW,CAACF,aAAa,CAAC;MAC7CG,WAAW,CAACF,UAAU,EAAE,kBAAkB,IAAIjG,IAAI,CAAC,CAAC,CAACoG,WAAW,CAAC,CAAC,CAACC,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,MAAM,CAAC;MAEvFjI,YAAY,CAAC,GAAG5C,aAAa,CAACoE,MAAM,2BAA2B,EAAE,SAAS,CAAC;IAC7E,CAAC,CAAC,OAAOzB,KAAK,EAAE;MACdC,YAAY,CAAC,2BAA2B,EAAE,OAAO,CAAC;IACpD,CAAC,SAAS;MACRhC,sBAAsB,CAAC,KAAK,CAAC;IAC/B;EACF,CAAC;;EAED;EACA,MAAM8J,WAAW,GAAI7H,IAAI,IAAK;IAC5B,MAAMiI,OAAO,GAAG,CAAC,SAAS,EAAE,oBAAoB,EAAE,MAAM,EAAE,WAAW,EAAE,WAAW,EAAE,WAAW,EAAE,YAAY,EAAE,WAAW,CAAC;IAC3H,MAAMC,IAAI,GAAGlI,IAAI,CAACsE,GAAG,CAACxC,IAAI,IAAI,CAC5BA,IAAI,CAAC5D,OAAO,EACZ4D,IAAI,CAAC8B,kBAAkB,EACvB,IAAIjC,IAAI,CAACG,IAAI,CAACC,mBAAmB,CAAC,CAACoG,kBAAkB,CAAC,CAAC,EACvDrG,IAAI,CAAChG,SAAS,EACdgG,IAAI,CAAC3F,SAAS,EACd2F,IAAI,CAACzD,kBAAkB,EACvByD,IAAI,CAACvD,iBAAiB,EACtBuD,IAAI,CAAChD,gBAAgB,CACtB,CAAC;IAEF,OAAO,CAACmJ,OAAO,EAAE,GAAGC,IAAI,CAAC,CAAC5D,GAAG,CAAC8D,GAAG,IAAIA,GAAG,CAACC,IAAI,CAAC,GAAG,CAAC,CAAC,CAACA,IAAI,CAAC,IAAI,CAAC;EAChE,CAAC;EAED,MAAMP,WAAW,GAAGA,CAACQ,OAAO,EAAEC,QAAQ,KAAK;IACzC,MAAMxB,IAAI,GAAG,IAAIC,IAAI,CAAC,CAACsB,OAAO,CAAC,EAAE;MAAEpB,IAAI,EAAE;IAA0B,CAAC,CAAC;IACrE,MAAMb,IAAI,GAAGC,QAAQ,CAACC,aAAa,CAAC,GAAG,CAAC;IACxC,IAAIF,IAAI,CAACI,QAAQ,KAAK+B,SAAS,EAAE;MAC/B,MAAMrB,GAAG,GAAGC,GAAG,CAACC,eAAe,CAACN,IAAI,CAAC;MACrCV,IAAI,CAACoC,YAAY,CAAC,MAAM,EAAEtB,GAAG,CAAC;MAC9Bd,IAAI,CAACoC,YAAY,CAAC,UAAU,EAAEF,QAAQ,CAAC;MACvClC,IAAI,CAACqC,KAAK,CAACC,UAAU,GAAG,QAAQ;MAChCrC,QAAQ,CAACI,IAAI,CAACC,WAAW,CAACN,IAAI,CAAC;MAC/BA,IAAI,CAACO,KAAK,CAAC,CAAC;MACZN,QAAQ,CAACI,IAAI,CAACG,WAAW,CAACR,IAAI,CAAC;IACjC;EACF,CAAC;EAED,MAAMuC,eAAe,GAAGA,CAAA,KAAM;IAC5B,MAAMhB,UAAU,GAAGC,WAAW,CAACvM,sBAAsB,CAAC;IACtDwM,WAAW,CAACF,UAAU,EAAE,wBAAwB,IAAIjG,IAAI,CAAC,CAAC,CAACoG,WAAW,CAAC,CAAC,CAACC,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,MAAM,CAAC;IAC7FjI,YAAY,CAAC,yBAAyB,EAAE,SAAS,CAAC;EACpD,CAAC;;EAED;EACA7M,mBAAmB,CAACqH,GAAG,EAAE,OAAO;IAC9BsO,kBAAkB,EAAGC,MAAM,IAAK;MAC9B,IAAIA,MAAM,KAAK,oBAAoB,EAAE;QACnC/D,gBAAgB,CAAC,CAAC;MACpB,CAAC,MAAM,IAAI+D,MAAM,KAAK,0BAA0B,EAAE;QAChDnO,YAAY,CAAC,CAAC,CAAC;MACjB;IACF;EACF,CAAC,CAAC,CAAC;;EAEH;EACA,MAAMoO,mBAAmB,GAAIC,KAAK,IAAK;IACrC,MAAMC,UAAU,GAAG,CAAC5M,WAAW,GAAG,CAAC,IAAIE,YAAY;IACnD,MAAM2M,QAAQ,GAAGD,UAAU,GAAG1M,YAAY;IAC1C,OAAOyM,KAAK,CAACG,KAAK,CAACF,UAAU,EAAEC,QAAQ,CAAC;EAC1C,CAAC;EAED,MAAME,aAAa,GAAIJ,KAAK,IAAKxH,IAAI,CAAC6H,IAAI,CAACL,KAAK,CAACzH,MAAM,GAAGhF,YAAY,CAAC;;EAEvE;EACA,MAAM+M,eAAe,GAAGA,CAACC,KAAK,EAAElE,KAAK,KAAK;IACxC,OAAO,CAAC,GAAG,IAAImE,GAAG,CAACD,KAAK,CAACjF,GAAG,CAACmF,IAAI,IAAIA,IAAI,CAACpE,KAAK,CAAC,CAAC,CAACxD,MAAM,CAAC6H,OAAO,CAAC,CAAC,CAAC;EACrE,CAAC;;EAED;EACA,MAAMC,eAAe,GAAGA,CAAA,kBACtB7P,OAAA,CAACxG,KAAK;IAACsW,EAAE,EAAE;MAAEC,CAAC,EAAE,CAAC;MAAEC,EAAE,EAAE,CAAC;MAAEC,OAAO,EAAE;IAAU,CAAE;IAAAC,QAAA,eAC7ClQ,OAAA,CAAC5E,KAAK;MAAC+U,SAAS,EAAC,KAAK;MAACC,OAAO,EAAE,CAAE;MAACC,UAAU,EAAC,QAAQ;MAACC,cAAc,EAAC,eAAe;MAACC,QAAQ,EAAC,MAAM;MAAAL,QAAA,gBAEnGlQ,OAAA,CAAC5E,KAAK;QAAC+U,SAAS,EAAC,KAAK;QAACE,UAAU,EAAC,QAAQ;QAACD,OAAO,EAAE,CAAE;QAAAF,QAAA,gBACpDlQ,OAAA,CAACR,SAAS;UAACgR,KAAK,EAAC,SAAS;UAACC,QAAQ,EAAC;QAAO;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,eAC9C7Q,OAAA,CAAC3G,GAAG;UAAA6W,QAAA,gBACFlQ,OAAA,CAAC1G,UAAU;YAACwX,OAAO,EAAC,IAAI;YAACC,UAAU,EAAC,MAAM;YAACjB,EAAE,EAAE;cAAEkB,UAAU,EAAE;YAAE,CAAE;YAAAd,QAAA,EAC9DjL,UAAU,CAACE;UAAU;YAAAuL,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACZ,CAAC,eACb7Q,OAAA,CAAC1G,UAAU;YAACwX,OAAO,EAAC,SAAS;YAACN,KAAK,EAAC,gBAAgB;YAAAN,QAAA,EAAC;UAErD;YAAAQ,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAY,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACV,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACD,CAAC,eAER7Q,OAAA,CAAC5E,KAAK;QAAC+U,SAAS,EAAC,KAAK;QAACE,UAAU,EAAC,QAAQ;QAACD,OAAO,EAAE,CAAE;QAAAF,QAAA,gBACpDlQ,OAAA,CAACtC,SAAS;UAAC8S,KAAK,EAAC,SAAS;UAACC,QAAQ,EAAC;QAAO;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,eAC9C7Q,OAAA,CAAC3G,GAAG;UAAA6W,QAAA,gBACFlQ,OAAA,CAAC1G,UAAU;YAACwX,OAAO,EAAC,IAAI;YAACC,UAAU,EAAC,MAAM;YAACjB,EAAE,EAAE;cAAEkB,UAAU,EAAE;YAAE,CAAE;YAAAd,QAAA,EAC9DjL,UAAU,CAACG;UAAe;YAAAsL,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACjB,CAAC,eACb7Q,OAAA,CAAC1G,UAAU;YAACwX,OAAO,EAAC,SAAS;YAACN,KAAK,EAAC,gBAAgB;YAAAN,QAAA,EAAC;UAErD;YAAAQ,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAY,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACV,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACD,CAAC,eAER7Q,OAAA,CAAC5E,KAAK;QAAC+U,SAAS,EAAC,KAAK;QAACE,UAAU,EAAC,QAAQ;QAACD,OAAO,EAAE,CAAE;QAAAF,QAAA,gBACpDlQ,OAAA,CAACxC,SAAS;UAACgT,KAAK,EAAC,MAAM;UAACC,QAAQ,EAAC;QAAO;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,eAC3C7Q,OAAA,CAAC3G,GAAG;UAAA6W,QAAA,gBACFlQ,OAAA,CAAC1G,UAAU;YAACwX,OAAO,EAAC,IAAI;YAACC,UAAU,EAAC,MAAM;YAACjB,EAAE,EAAE;cAAEkB,UAAU,EAAE;YAAE,CAAE;YAAAd,QAAA,EAC9DjL,UAAU,CAACoD;UAAiB;YAAAqI,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACnB,CAAC,eACb7Q,OAAA,CAAC1G,UAAU;YAACwX,OAAO,EAAC,SAAS;YAACN,KAAK,EAAC,gBAAgB;YAAAN,QAAA,EAAC;UAErD;YAAAQ,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAY,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACV,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACD,CAAC,eAER7Q,OAAA,CAAC5E,KAAK;QAAC+U,SAAS,EAAC,KAAK;QAACE,UAAU,EAAC,QAAQ;QAACD,OAAO,EAAE,CAAE;QAAAF,QAAA,gBACpDlQ,OAAA,CAAC3G,GAAG;UAACyW,EAAE,EAAE;YACPmB,KAAK,EAAE,EAAE;YACTC,MAAM,EAAE,EAAE;YACVC,YAAY,EAAE,KAAK;YACnBlB,OAAO,EAAEhL,UAAU,CAACK,wBAAwB,IAAI,EAAE,GAAG,cAAc,GAC1DL,UAAU,CAACK,wBAAwB,IAAI,EAAE,GAAG,cAAc,GAAG,YAAY;YAClF8L,OAAO,EAAE,MAAM;YACff,UAAU,EAAE,QAAQ;YACpBC,cAAc,EAAE;UAClB,CAAE;UAAAJ,QAAA,eACAlQ,OAAA,CAAC1G,UAAU;YAACwX,OAAO,EAAC,SAAS;YAACC,UAAU,EAAC,MAAM;YAACP,KAAK,EAAC,OAAO;YAAAN,QAAA,GAC1DjL,UAAU,CAACK,wBAAwB,EAAC,GACvC;UAAA;YAAAoL,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAY;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACV,CAAC,eACN7Q,OAAA,CAAC3G,GAAG;UAAA6W,QAAA,gBACFlQ,OAAA,CAAC1G,UAAU;YAACwX,OAAO,EAAC,OAAO;YAACC,UAAU,EAAC,QAAQ;YAACjB,EAAE,EAAE;cAAEkB,UAAU,EAAE;YAAE,CAAE;YAAAd,QAAA,EAAC;UAEvE;YAAAQ,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAY,CAAC,eACb7Q,OAAA,CAAC1G,UAAU;YAACwX,OAAO,EAAC,SAAS;YAACN,KAAK,EAAC,gBAAgB;YAAAN,QAAA,GACjDjL,UAAU,CAACM,kBAAkB,EAAC,OACjC;UAAA;YAAAmL,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAY,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACV,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACD,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAGH;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CACR;;EAED;EACA,MAAMQ,sBAAsB,GAAGA,CAAA,kBAC7BrR,OAAA,CAACxG,KAAK;IAACsW,EAAE,EAAE;MAAEC,CAAC,EAAE,CAAC;MAAEC,EAAE,EAAE;IAAE,CAAE;IAAAE,QAAA,gBACzBlQ,OAAA,CAACvG,IAAI;MAAC6X,SAAS;MAAClB,OAAO,EAAE,CAAE;MAACC,UAAU,EAAC,QAAQ;MAAAH,QAAA,gBAC7ClQ,OAAA,CAACvG,IAAI;QAACkW,IAAI;QAAC4B,EAAE,EAAE,EAAG;QAACC,EAAE,EAAE,CAAE;QAAAtB,QAAA,eACvBlQ,OAAA,CAAChG,SAAS;UACRyX,SAAS;UACTC,WAAW,EAAC,0CAA0C;UACtDlG,KAAK,EAAEpK,UAAW;UAClBuQ,QAAQ,EAAGC,CAAC,IAAKvQ,aAAa,CAACuQ,CAAC,CAACC,MAAM,CAACrG,KAAK,CAAE;UAC/CsG,UAAU,EAAE;YACVC,cAAc,eACZ/R,OAAA,CAAC9E,cAAc;cAAC8W,QAAQ,EAAC,OAAO;cAAA9B,QAAA,eAC9BlQ,OAAA,CAAC1D,UAAU;gBAAAoU,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACA,CACjB;YACDoB,YAAY,EAAE7Q,UAAU,iBACtBpB,OAAA,CAAC9E,cAAc;cAAC8W,QAAQ,EAAC,KAAK;cAAA9B,QAAA,eAC5BlQ,OAAA,CAAClF,UAAU;gBAACoX,OAAO,EAAEA,CAAA,KAAM7Q,aAAa,CAAC,EAAE,CAAE;gBAAC8Q,IAAI,EAAC,OAAO;gBAAAjC,QAAA,eACxDlQ,OAAA,CAAC1C,SAAS;kBAAAoT,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACC;UAEpB;QAAE;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACE,CAAC,eAEP7Q,OAAA,CAACvG,IAAI;QAACkW,IAAI;QAAC4B,EAAE,EAAE,EAAG;QAACC,EAAE,EAAE,CAAE;QAAAtB,QAAA,eACvBlQ,OAAA,CAACzG,MAAM;UACLkY,SAAS;UACTX,OAAO,EAAC,UAAU;UAClBsB,SAAS,eAAEpS,OAAA,CAACxD,UAAU;YAAAkU,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAE;UAC1BqB,OAAO,EAAEA,CAAA,KAAMvQ,sBAAsB,CAAC,CAACD,mBAAmB,CAAE;UAC5D8O,KAAK,EAAE6B,MAAM,CAACC,MAAM,CAAC1Q,OAAO,CAAC,CAAC0H,IAAI,CAACiJ,CAAC,IAAIA,CAAC,CAAC,GAAG,SAAS,GAAG,SAAU;UAAArC,QAAA,GACpE,SACQ,EAACmC,MAAM,CAACC,MAAM,CAAC1Q,OAAO,CAAC,CAACmG,MAAM,CAACwK,CAAC,IAAIA,CAAC,CAAC,CAAC9K,MAAM,GAAG,CAAC,IAAI,IAAI4K,MAAM,CAACC,MAAM,CAAC1Q,OAAO,CAAC,CAACmG,MAAM,CAACwK,CAAC,IAAIA,CAAC,CAAC,CAAC9K,MAAM,GAAG;QAAA;UAAAiJ,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACzG;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACL,CAAC,eAEP7Q,OAAA,CAACvG,IAAI;QAACkW,IAAI;QAAC4B,EAAE,EAAE,EAAG;QAACC,EAAE,EAAE,CAAE;QAAAtB,QAAA,eACvBlQ,OAAA,CAACzG,MAAM;UACLkY,SAAS;UACTX,OAAO,EAAC,UAAU;UAClBsB,SAAS,EAAE7O,QAAQ,gBAAGvD,OAAA,CAAC1C,SAAS;YAAAoT,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,gBAAG7Q,OAAA,CAACtC,SAAS;YAAAgT,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAE;UACpDqB,OAAO,EAAEjI,cAAe;UACxBuG,KAAK,EAAEjN,QAAQ,GAAG,WAAW,GAAG,SAAU;UAC1CiP,QAAQ,EAAE5R,SAAS,KAAK,CAAE;UAAAsP,QAAA,EAEzB3M,QAAQ,GAAG,gBAAgB,GAAG;QAAoB;UAAAmN,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC7C;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACL,CAAC,eAEP7Q,OAAA,CAACvG,IAAI;QAACkW,IAAI;QAAC4B,EAAE,EAAE,EAAG;QAACC,EAAE,EAAE,CAAE;QAAAtB,QAAA,eACvBlQ,OAAA,CAACzG,MAAM;UACLkY,SAAS;UACTX,OAAO,EAAC,UAAU;UAClBsB,SAAS,eAAEpS,OAAA,CAAClC,UAAU;YAAA4S,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAE;UAC1BqB,OAAO,EAAEpD,eAAgB;UACzB0D,QAAQ,EAAE5R,SAAS,KAAK,CAAC,IAAIY,sBAAsB,CAACiG,MAAM,KAAK,CAAE;UAAAyI,QAAA,EAEhEtP,SAAS,KAAK,CAAC,GAAG,8BAA8B,GAAG;QAAwB;UAAA8P,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACtE;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACL,CAAC,eAEP7Q,OAAA,CAACvG,IAAI;QAACkW,IAAI;QAAC4B,EAAE,EAAE,EAAG;QAACC,EAAE,EAAE,CAAE;QAAAtB,QAAA,eACvBlQ,OAAA,CAACzG,MAAM;UACLkY,SAAS;UACTX,OAAO,EAAC,WAAW;UACnBsB,SAAS,eAAEpS,OAAA,CAAC5D,OAAO;YAAAsU,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAE;UACvBqB,OAAO,EAAEjH,gBAAiB;UAAAiF,QAAA,EAC3B;QAED;UAAAQ,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACL,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,eAGP7Q,OAAA,CAACvE,QAAQ;MAACgX,EAAE,EAAE/Q,mBAAoB;MAAAwO,QAAA,gBAChClQ,OAAA,CAAC7E,OAAO;QAAC2U,EAAE,EAAE;UAAE4C,EAAE,EAAE;QAAE;MAAE;QAAAhC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC,eAC1B7Q,OAAA,CAAC1G,UAAU;QAACwX,OAAO,EAAC,OAAO;QAACN,KAAK,EAAC,gBAAgB;QAACV,EAAE,EAAE;UAAEE,EAAE,EAAE;QAAE,CAAE;QAAAE,QAAA,EAC9DtP,SAAS,KAAK,CAAC,GAAG,iBAAiB,GAAG;MAA2B;QAAA8P,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACxD,CAAC,eAEb7Q,OAAA,CAACvG,IAAI;QAAC6X,SAAS;QAAClB,OAAO,EAAE,CAAE;QAAAF,QAAA,GAExBtP,SAAS,KAAK,CAAC,iBACdZ,OAAA,CAAAE,SAAA;UAAAgQ,QAAA,gBACElQ,OAAA,CAACvG,IAAI;YAACkW,IAAI;YAAC4B,EAAE,EAAE,EAAG;YAACC,EAAE,EAAE,CAAE;YAAAtB,QAAA,eACvBlQ,OAAA,CAAC/F,WAAW;cAACwX,SAAS;cAACU,IAAI,EAAC,OAAO;cAAAjC,QAAA,gBACjClQ,OAAA,CAAC9F,UAAU;gBAAAgW,QAAA,EAAC;cAAmB;gBAAAQ,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAY,CAAC,eAC5C7Q,OAAA,CAAC7F,MAAM;gBACLqR,KAAK,EAAE5J,OAAO,CAACE,KAAM;gBACrB6P,QAAQ,EAAGC,CAAC,IAAK/P,UAAU,CAAC;kBAAC,GAAGD,OAAO;kBAAEE,KAAK,EAAE8P,CAAC,CAACC,MAAM,CAACrG;gBAAK,CAAC,CAAE;gBAAA0E,QAAA,gBAEjElQ,OAAA,CAAC5F,QAAQ;kBAACoR,KAAK,EAAC,EAAE;kBAAA0E,QAAA,EAAC;gBAAK;kBAAAQ,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAU,CAAC,eACnC7Q,OAAA,CAAC5F,QAAQ;kBAACoR,KAAK,EAAC,YAAY;kBAAA0E,QAAA,EAAC;gBAAU;kBAAAQ,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAU,CAAC,eAClD7Q,OAAA,CAAC5F,QAAQ;kBAACoR,KAAK,EAAC,gBAAgB;kBAAA0E,QAAA,EAAC;gBAAc;kBAAAQ,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAU,CAAC,eAC1D7Q,OAAA,CAAC5F,QAAQ;kBAACoR,KAAK,EAAC,UAAU;kBAAA0E,QAAA,EAAC;gBAAQ;kBAAAQ,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAU,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACxC,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACE;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACV,CAAC,eAEP7Q,OAAA,CAACvG,IAAI;YAACkW,IAAI;YAAC4B,EAAE,EAAE,EAAG;YAACC,EAAE,EAAE,CAAE;YAAAtB,QAAA,eACvBlQ,OAAA,CAAC/F,WAAW;cAACwX,SAAS;cAACU,IAAI,EAAC,OAAO;cAAAjC,QAAA,gBACjClQ,OAAA,CAAC9F,UAAU;gBAAAgW,QAAA,EAAC;cAAS;gBAAAQ,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAY,CAAC,eAClC7Q,OAAA,CAAC7F,MAAM;gBACLqR,KAAK,EAAE5J,OAAO,CAACG,SAAU;gBACzB4P,QAAQ,EAAGC,CAAC,IAAK/P,UAAU,CAAC;kBAAC,GAAGD,OAAO;kBAAEG,SAAS,EAAE6P,CAAC,CAACC,MAAM,CAACrG;gBAAK,CAAC,CAAE;gBAAA0E,QAAA,gBAErElQ,OAAA,CAAC5F,QAAQ;kBAACoR,KAAK,EAAC,EAAE;kBAAA0E,QAAA,EAAC;gBAAK;kBAAAQ,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAU,CAAC,EAClC,CAAC,GAAG,IAAInB,GAAG,CAAC1O,IAAI,CAACwJ,GAAG,CAACoB,CAAC,IAAIA,CAAC,CAAC7J,SAAS,CAAC,CAAC,CAAC,CAACgG,MAAM,CAAC6H,OAAO,CAAC,CAACpF,GAAG,CAACmI,GAAG,iBAC/D3S,OAAA,CAAC5F,QAAQ;kBAAWoR,KAAK,EAAEmH,GAAI;kBAAAzC,QAAA,EAAEyC;gBAAG,GAArBA,GAAG;kBAAAjC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAA6B,CAChD,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACI,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACE;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACV,CAAC,eAEP7Q,OAAA,CAACvG,IAAI;YAACkW,IAAI;YAAC4B,EAAE,EAAE,EAAG;YAACC,EAAE,EAAE,CAAE;YAAAtB,QAAA,eACvBlQ,OAAA,CAAC/F,WAAW;cAACwX,SAAS;cAACU,IAAI,EAAC,OAAO;cAAAjC,QAAA,gBACjClQ,OAAA,CAAC9F,UAAU;gBAAAgW,QAAA,EAAC;cAAoB;gBAAAQ,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAY,CAAC,eAC7C7Q,OAAA,CAAC7F,MAAM;gBACLqR,KAAK,EAAE5J,OAAO,CAACU,cAAe;gBAC9BqP,QAAQ,EAAGC,CAAC,IAAK/P,UAAU,CAAC;kBAAC,GAAGD,OAAO;kBAAEU,cAAc,EAAEsP,CAAC,CAACC,MAAM,CAACrG;gBAAK,CAAC,CAAE;gBAAA0E,QAAA,gBAE1ElQ,OAAA,CAAC5F,QAAQ;kBAACoR,KAAK,EAAC,EAAE;kBAAA0E,QAAA,EAAC;gBAAK;kBAAAQ,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAU,CAAC,eACnC7Q,OAAA,CAAC5F,QAAQ;kBAACoR,KAAK,EAAC,aAAa;kBAAA0E,QAAA,EAAC;gBAAW;kBAAAQ,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAU,CAAC,eACpD7Q,OAAA,CAAC5F,QAAQ;kBAACoR,KAAK,EAAC,iBAAiB;kBAAA0E,QAAA,EAAC;gBAAe;kBAAAQ,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAU,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACtD,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACE;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACV,CAAC;QAAA,eACP,CACH,EAGAjQ,SAAS,KAAK,CAAC,iBACdZ,OAAA,CAAAE,SAAA;UAAAgQ,QAAA,gBACElQ,OAAA,CAACvG,IAAI;YAACkW,IAAI;YAAC4B,EAAE,EAAE,EAAG;YAACC,EAAE,EAAE,CAAE;YAAAtB,QAAA,eACvBlQ,OAAA,CAAC/F,WAAW;cAACwX,SAAS;cAACU,IAAI,EAAC,OAAO;cAAAjC,QAAA,gBACjClQ,OAAA,CAAC9F,UAAU;gBAAAgW,QAAA,EAAC;cAAS;gBAAAQ,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAY,CAAC,eAClC7Q,OAAA,CAAC7F,MAAM;gBACLqR,KAAK,EAAE5J,OAAO,CAACI,SAAU;gBACzB2P,QAAQ,EAAGC,CAAC,IAAK/P,UAAU,CAAC;kBAAC,GAAGD,OAAO;kBAAEI,SAAS,EAAE4P,CAAC,CAACC,MAAM,CAACrG;gBAAK,CAAC,CAAE;gBAAA0E,QAAA,gBAErElQ,OAAA,CAAC5F,QAAQ;kBAACoR,KAAK,EAAC,EAAE;kBAAA0E,QAAA,EAAC;gBAAK;kBAAAQ,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAU,CAAC,EAClC,CAAC,GAAG,IAAInB,GAAG,CAAC5O,cAAc,CAAC0J,GAAG,CAACoB,CAAC,IAAIA,CAAC,CAAC5J,SAAS,CAAC,CAAC,CAAC,CAAC+F,MAAM,CAAC6H,OAAO,CAAC,CAACpF,GAAG,CAACoI,EAAE,iBACxE5S,OAAA,CAAC5F,QAAQ;kBAAUoR,KAAK,EAAEoH,EAAG;kBAAA1C,QAAA,EAAE0C;gBAAE,GAAlBA,EAAE;kBAAAlC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAA2B,CAC7C,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACI,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACE;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACV,CAAC,eAEP7Q,OAAA,CAACvG,IAAI;YAACkW,IAAI;YAAC4B,EAAE,EAAE,EAAG;YAACC,EAAE,EAAE,CAAE;YAAAtB,QAAA,eACvBlQ,OAAA,CAAC/F,WAAW;cAACwX,SAAS;cAACU,IAAI,EAAC,OAAO;cAAAjC,QAAA,gBACjClQ,OAAA,CAAC9F,UAAU;gBAAAgW,QAAA,EAAC;cAAc;gBAAAQ,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAY,CAAC,eACvC7Q,OAAA,CAAC7F,MAAM;gBACLqR,KAAK,EAAE5J,OAAO,CAACQ,aAAc;gBAC7BuP,QAAQ,EAAGC,CAAC,IAAK/P,UAAU,CAAC;kBAAC,GAAGD,OAAO;kBAAEQ,aAAa,EAAEwP,CAAC,CAACC,MAAM,CAACrG;gBAAK,CAAC,CAAE;gBAAA0E,QAAA,gBAEzElQ,OAAA,CAAC5F,QAAQ;kBAACoR,KAAK,EAAC,EAAE;kBAAA0E,QAAA,EAAC;gBAAK;kBAAAQ,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAU,CAAC,eACnC7Q,OAAA,CAAC5F,QAAQ;kBAACoR,KAAK,EAAC,UAAU;kBAAA0E,QAAA,EAAC;gBAAQ;kBAAAQ,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAU,CAAC,eAC9C7Q,OAAA,CAAC5F,QAAQ;kBAACoR,KAAK,EAAC,cAAc;kBAAA0E,QAAA,EAAC;gBAAY;kBAAAQ,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAU,CAAC,eACtD7Q,OAAA,CAAC5F,QAAQ;kBAACoR,KAAK,EAAC,eAAe;kBAAA0E,QAAA,EAAC;gBAAa;kBAAAQ,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAU,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAClD,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACE;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACV,CAAC,eAEP7Q,OAAA,CAACvG,IAAI;YAACkW,IAAI;YAAC4B,EAAE,EAAE,EAAG;YAACC,EAAE,EAAE,CAAE;YAAAtB,QAAA,eACvBlQ,OAAA,CAAChG,SAAS;cACRyX,SAAS;cACTU,IAAI,EAAC,OAAO;cACZU,KAAK,EAAC,0BAAqB;cAC3BzF,IAAI,EAAC,QAAQ;cACb5B,KAAK,EAAE5J,OAAO,CAACO,gBAAiB;cAChCwP,QAAQ,EAAGC,CAAC,IAAK/P,UAAU,CAAC;gBAAC,GAAGD,OAAO;gBAAEO,gBAAgB,EAAEyP,CAAC,CAACC,MAAM,CAACrG;cAAK,CAAC,CAAE;cAC5EkG,WAAW,EAAC;YAAS;cAAAhB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACtB;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACE,CAAC,eAEP7Q,OAAA,CAACvG,IAAI;YAACkW,IAAI;YAAC4B,EAAE,EAAE,EAAG;YAACC,EAAE,EAAE,CAAE;YAAAtB,QAAA,eACvBlQ,OAAA,CAAChG,SAAS;cACRyX,SAAS;cACTU,IAAI,EAAC,OAAO;cACZU,KAAK,EAAC,aAAa;cACnBzF,IAAI,EAAC,MAAM;cACX5B,KAAK,EAAE5J,OAAO,CAACK,UAAW;cAC1B0P,QAAQ,EAAGC,CAAC,IAAK/P,UAAU,CAAC;gBAAC,GAAGD,OAAO;gBAAEK,UAAU,EAAE2P,CAAC,CAACC,MAAM,CAACrG;cAAK,CAAC,CAAE;cACtEsH,eAAe,EAAE;gBAAEC,MAAM,EAAE;cAAK;YAAE;cAAArC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACnC;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACE,CAAC,eAEP7Q,OAAA,CAACvG,IAAI;YAACkW,IAAI;YAAC4B,EAAE,EAAE,EAAG;YAACC,EAAE,EAAE,CAAE;YAAAtB,QAAA,eACvBlQ,OAAA,CAAChG,SAAS;cACRyX,SAAS;cACTU,IAAI,EAAC,OAAO;cACZU,KAAK,EAAC,WAAW;cACjBzF,IAAI,EAAC,MAAM;cACX5B,KAAK,EAAE5J,OAAO,CAACM,QAAS;cACxByP,QAAQ,EAAGC,CAAC,IAAK/P,UAAU,CAAC;gBAAC,GAAGD,OAAO;gBAAEM,QAAQ,EAAE0P,CAAC,CAACC,MAAM,CAACrG;cAAK,CAAC,CAAE;cACpEsH,eAAe,EAAE;gBAAEC,MAAM,EAAE;cAAK;YAAE;cAAArC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACnC;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACE,CAAC;QAAA,eACP,CACH,eAED7Q,OAAA,CAACvG,IAAI;UAACkW,IAAI;UAAC4B,EAAE,EAAE,EAAG;UAAArB,QAAA,eAChBlQ,OAAA,CAAC5E,KAAK;YAAC+U,SAAS,EAAC,KAAK;YAACC,OAAO,EAAE,CAAE;YAACE,cAAc,EAAC,UAAU;YAAAJ,QAAA,eAC1DlQ,OAAA,CAACzG,MAAM;cACLuX,OAAO,EAAC,UAAU;cAClBqB,IAAI,EAAC,OAAO;cACZD,OAAO,EAAEA,CAAA,KAAMrQ,UAAU,CAAC;gBACxBC,KAAK,EAAE,EAAE;gBAAEC,SAAS,EAAE,EAAE;gBAAEC,SAAS,EAAE,EAAE;gBAAEC,UAAU,EAAE,EAAE;gBACvDC,QAAQ,EAAE,EAAE;gBAAEC,gBAAgB,EAAE,EAAE;gBAAEC,aAAa,EAAE,EAAE;gBAAEC,SAAS,EAAE,EAAE;gBACpEC,cAAc,EAAE;cAClB,CAAC,CAAE;cAAA4N,QAAA,EACJ;YAED;cAAAQ,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACJ;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACJ,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACC,CAAC,EAGVtN,QAAQ,IAAIF,aAAa,CAACoE,MAAM,GAAG,CAAC,iBACnCzH,OAAA,CAAAE,SAAA;MAAAgQ,QAAA,gBACElQ,OAAA,CAAC7E,OAAO;QAAC2U,EAAE,EAAE;UAAE4C,EAAE,EAAE;QAAE;MAAE;QAAAhC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC,eAC1B7Q,OAAA,CAAC5E,KAAK;QAAC+U,SAAS,EAAC,KAAK;QAACC,OAAO,EAAE,CAAE;QAACC,UAAU,EAAC,QAAQ;QAAAH,QAAA,gBACpDlQ,OAAA,CAAC1G,UAAU;UAACwX,OAAO,EAAC,OAAO;UAAAZ,QAAA,GACxB7M,aAAa,CAACoE,MAAM,EAAC,uBACxB;QAAA;UAAAiJ,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAY,CAAC,eACb7Q,OAAA,CAACzG,MAAM;UACL4Y,IAAI,EAAC,OAAO;UACZrB,OAAO,EAAC,UAAU;UAClBoB,OAAO,EAAE5H,cAAe;UAAA4F,QAAA,EACzB;QAED;UAAAQ,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC,eACT7Q,OAAA,CAACzG,MAAM;UACL4Y,IAAI,EAAC,OAAO;UACZrB,OAAO,EAAC,UAAU;UAClBoB,OAAO,EAAExH,cAAe;UAAAwF,QAAA,EACzB;QAED;UAAAQ,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC,eACT7Q,OAAA,CAACzG,MAAM;UACL4Y,IAAI,EAAC,OAAO;UACZrB,OAAO,EAAC,UAAU;UAClBsB,SAAS,eAAEpS,OAAA,CAAClC,UAAU;YAAA4S,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAE;UAC1BqB,OAAO,EAAEtE,gBAAiB;UAAAsC,QAAA,EAC3B;QAED;UAAAQ,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC,eACT7Q,OAAA,CAACzG,MAAM;UACL4Y,IAAI,EAAC,OAAO;UACZrB,OAAO,EAAC,UAAU;UAClBN,KAAK,EAAC,OAAO;UACb4B,SAAS,eAAEpS,OAAA,CAAChD,UAAU;YAAA0T,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAE;UAC1BqB,OAAO,EAAEvE,gBAAiB;UAAAuC,QAAA,EAC3B;QAED;UAAAQ,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACJ,CAAC;IAAA,eACR,CACH;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACI,CACR;;EAED;EACA,MAAMmC,eAAe,GAAGA,CAAA,KAAM;IAC5B,MAAMC,YAAY,GAAGhE,mBAAmB,CAAC3N,YAAY,CAAC;IAEtD,IAAIA,YAAY,CAACmG,MAAM,KAAK,CAAC,EAAE;MAC7B,oBACEzH,OAAA,CAACzF,KAAK;QAACsJ,QAAQ,EAAC,MAAM;QAAAqM,QAAA,EACnB9O,UAAU,IAAIQ,OAAO,CAACE,KAAK,IAAIF,OAAO,CAACG,SAAS,GAC7C,4CAA4C,GAC5C;MAAyB;QAAA2O,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACxB,CAAC;IAEZ;IAEA,oBACE7Q,OAAA,CAAAE,SAAA;MAAAgQ,QAAA,gBACElQ,OAAA,CAACrF,cAAc;QAACuY,SAAS,EAAE1Z,KAAM;QAAA0W,QAAA,eAC/BlQ,OAAA,CAACxF,KAAK;UAAC2X,IAAI,EAAC,OAAO;UAAAjC,QAAA,gBACjBlQ,OAAA,CAACpF,SAAS;YAAAsV,QAAA,eACRlQ,OAAA,CAACnF,QAAQ;cAAAqV,QAAA,gBACPlQ,OAAA,CAACtF,SAAS;gBAAAwV,QAAA,eACRlQ,OAAA,CAAC5E,KAAK;kBAAC+U,SAAS,EAAC,KAAK;kBAACE,UAAU,EAAC,QAAQ;kBAACD,OAAO,EAAE,CAAE;kBAAAF,QAAA,gBACpDlQ,OAAA,CAAC1G,UAAU;oBAACwX,OAAO,EAAC,OAAO;oBAACC,UAAU,EAAC,MAAM;oBAAAb,QAAA,EAAC;kBAAO;oBAAAQ,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAY,CAAC,eAClE7Q,OAAA,CAAClF,UAAU;oBAACqX,IAAI,EAAC,OAAO;oBAACD,OAAO,EAAEA,CAAA,KAAM;sBACtCtP,SAAS,CAAC,SAAS,CAAC;sBACpBE,YAAY,CAACD,SAAS,KAAK,KAAK,GAAG,MAAM,GAAG,KAAK,CAAC;oBACpD,CAAE;oBAAAqN,QAAA,EACCvN,MAAM,KAAK,SAAS,GAAIE,SAAS,KAAK,KAAK,gBAAG7C,OAAA,CAAClB,cAAc;sBAAA4R,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAE,CAAC,gBAAG7Q,OAAA,CAACpB,cAAc;sBAAA8R,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAE,CAAC,gBAAI7Q,OAAA,CAACpB,cAAc;sBAAA8R,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAE;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAClG,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACR;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACC,CAAC,eACZ7Q,OAAA,CAACtF,SAAS;gBAAAwV,QAAA,EAAC;cAAS;gBAAAQ,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAW,CAAC,eAChC7Q,OAAA,CAACtF,SAAS;gBAAAwV,QAAA,EAAC;cAAO;gBAAAQ,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAW,CAAC,eAC9B7Q,OAAA,CAACtF,SAAS;gBAAAwV,QAAA,EAAC;cAAQ;gBAAAQ,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAW,CAAC,eAC/B7Q,OAAA,CAACtF,SAAS;gBAAAwV,QAAA,EAAC;cAAM;gBAAAQ,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAW,CAAC,eAC7B7Q,OAAA,CAACtF,SAAS;gBAAAwV,QAAA,EAAC;cAAK;gBAAAQ,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAW,CAAC,eAC5B7Q,OAAA,CAACtF,SAAS;gBAAAwV,QAAA,EAAC;cAAK;gBAAAQ,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAW,CAAC,eAC5B7Q,OAAA,CAACtF,SAAS;gBAAAwV,QAAA,EAAC;cAAY;gBAAAQ,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAW,CAAC,eACnC7Q,OAAA,CAACtF,SAAS;gBAAAwV,QAAA,EAAC;cAAW;gBAAAQ,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAW,CAAC,eAClC7Q,OAAA,CAACtF,SAAS;gBAAAwV,QAAA,EAAC;cAAM;gBAAAQ,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAW,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACrB;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACF,CAAC,eACZ7Q,OAAA,CAACvF,SAAS;YAAAyV,QAAA,EACP+C,YAAY,CAACzI,GAAG,CAAExD,IAAI,IAAK;cAC1B,MAAMmM,aAAa,GAAGxI,iBAAiB,CAAC3D,IAAI,CAAC5C,OAAO,CAAC;cACrD,MAAMgP,cAAc,GAAGrM,oBAAoB,CAACC,IAAI,CAAC;cACjD,MAAMqM,eAAe,GAAG,CAACD,cAAc,GAAGvI,gCAAgC,CAAC7D,IAAI,CAAC,GAAG,EAAE;cAErF,oBACEhH,OAAA,CAACnF,QAAQ;gBAAAqV,QAAA,gBACPlQ,OAAA,CAACtF,SAAS;kBAAAwV,QAAA,eACRlQ,OAAA,CAAC1G,UAAU;oBAACwX,OAAO,EAAC,OAAO;oBAACC,UAAU,EAAC,QAAQ;oBAAAb,QAAA,EAC5ClJ,IAAI,CAAC5C;kBAAO;oBAAAsM,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACH;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACJ,CAAC,eACZ7Q,OAAA,CAACtF,SAAS;kBAAAwV,QAAA,EAAElJ,IAAI,CAACjF;gBAAS;kBAAA2O,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAY,CAAC,eACvC7Q,OAAA,CAACtF,SAAS;kBAAAwV,QAAA,EAAElJ,IAAI,CAACoC;gBAAO;kBAAAsH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAY,CAAC,eACrC7Q,OAAA,CAACtF,SAAS;kBAAAwV,QAAA,EAAElJ,IAAI,CAACkC;gBAAmB;kBAAAwH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAY,CAAC,eACjD7Q,OAAA,CAACtF,SAAS;kBAAAwV,QAAA,EAAElJ,IAAI,CAACmC;gBAAiB;kBAAAuH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAY,CAAC,eAC/C7Q,OAAA,CAACtF,SAAS;kBAAAwV,QAAA,GAAElJ,IAAI,CAACmE,eAAe,IAAInE,IAAI,CAACoE,aAAa,EAAC,IAAE;gBAAA;kBAAAsF,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAW,CAAC,eACrE7Q,OAAA,CAACtF,SAAS;kBAAAwV,QAAA,eACRlQ,OAAA,CAAC3E,IAAI;oBACH8W,IAAI,EAAC,OAAO;oBACZU,KAAK,EAAE7L,IAAI,CAACE,mBAAoB;oBAChCsJ,KAAK,EAAExJ,IAAI,CAACE,mBAAmB,KAAK,YAAY,GAAG,SAAS,GAAG;kBAAU;oBAAAwJ,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAC1E;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACO,CAAC,eACZ7Q,OAAA,CAACtF,SAAS;kBAAAwV,QAAA,EACP,CAAC,MAAM;oBACN,MAAM7I,YAAY,GAAGL,IAAI,CAACK,YAAY,IAAI,CAAC;oBAC3C,MAAMiM,iBAAiB,GAAGjM,YAAY,KAAK,CAAC,GAAG,eAAe,GACrCA,YAAY,KAAK,CAAC,GAAG,eAAe,GACpCA,YAAY,KAAK,CAAC,GAAG,aAAa,GAClCA,YAAY,KAAK,CAAC,GAAG,UAAU,GAC/B,aAAa;oBACtC,MAAMkM,MAAM,GAAGlM,YAAY,KAAK,CAAC,GAAG,SAAS,GAC/BA,YAAY,KAAK,CAAC,GAAG,OAAO,GAAG,SAAS;oBAEtD,oBACErH,OAAA,CAAC1E,OAAO;sBAACkY,KAAK,EAAE,aAAaxM,IAAI,CAACO,qBAAqB,IAAI,eAAe,cAAcP,IAAI,CAACQ,mBAAmB,IAAI,eAAe,EAAG;sBAAA0I,QAAA,eACpIlQ,OAAA,CAAC3E,IAAI;wBACH8W,IAAI,EAAC,OAAO;wBACZU,KAAK,EAAES,iBAAkB;wBACzB9C,KAAK,EAAE+C,MAAO;wBACdE,IAAI,EAAEpM,YAAY,KAAK,CAAC,gBAAGrH,OAAA,CAACtC,SAAS;0BAAAgT,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAE,CAAC,gBAAG7Q,OAAA,CAACpC,WAAW;0BAAA8S,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAE;sBAAE;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAC5D;oBAAC;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACK,CAAC;kBAEd,CAAC,EAAE;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACK,CAAC,eACZ7Q,OAAA,CAACtF,SAAS;kBAAAwV,QAAA,EACPiD,aAAa,gBACZnT,OAAA,CAAC3E,IAAI;oBACH8W,IAAI,EAAC,OAAO;oBACZsB,IAAI,eAAEzT,OAAA,CAACtC,SAAS;sBAAAgT,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAE,CAAE;oBACpBgC,KAAK,EAAC,aAAa;oBACnBrC,KAAK,EAAC;kBAAS;oBAAAE,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAChB,CAAC,gBAEF7Q,OAAA,CAAC3E,IAAI;oBACH8W,IAAI,EAAC,OAAO;oBACZsB,IAAI,eAAEzT,OAAA,CAACpC,WAAW;sBAAA8S,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAE,CAAE;oBACtBgC,KAAK,EAAC,iBAAiB;oBACvBrC,KAAK,EAAC;kBAAS;oBAAAE,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAChB;gBACF;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACQ,CAAC,eACZ7Q,OAAA,CAACtF,SAAS;kBAAAwV,QAAA,EACPiD,aAAa,gBACZnT,OAAA,CAAC1E,OAAO;oBAACkY,KAAK,EAAC,yBAAsB;oBAAAtD,QAAA,eACnClQ,OAAA,CAAC3E,IAAI;sBACHoY,IAAI,eAAEzT,OAAA,CAACtC,SAAS;wBAAAgT,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAE,CAAE;sBACpBgC,KAAK,EAAC,aAAa;sBACnBrC,KAAK,EAAC,SAAS;sBACf2B,IAAI,EAAC;oBAAO;sBAAAzB,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACb;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACK,CAAC,GACRuC,cAAc,gBAChBpT,OAAA,CAAC1E,OAAO;oBAACkY,KAAK,EAAC,qCAAqC;oBAAAtD,QAAA,eAClDlQ,OAAA,CAAClF,UAAU;sBACTqX,IAAI,EAAC,OAAO;sBACZD,OAAO,EAAEA,CAAA,KAAMjH,gBAAgB,CAACjE,IAAI,CAAE;sBACtCwJ,KAAK,EAAC,SAAS;sBAAAN,QAAA,eAEflQ,OAAA,CAAC5D,OAAO;wBAAAsU,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAE;oBAAC;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACD;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACN,CAAC,gBAEV7Q,OAAA,CAAC1E,OAAO;oBAACkY,KAAK,EAAEH,eAAgB;oBAAAnD,QAAA,eAC9BlQ,OAAA;sBAAAkQ,QAAA,eACElQ,OAAA,CAAClF,UAAU;wBACTqX,IAAI,EAAC,OAAO;wBACZK,QAAQ;wBACRN,OAAO,EAAEA,CAAA,KAAMjM,YAAY,CAACoN,eAAe,EAAE,SAAS,CAAE;wBAAAnD,QAAA,eAExDlQ,OAAA,CAACJ,SAAS;0BAAA8Q,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAE;sBAAC;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACH;oBAAC;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACT;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACA;gBACV;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACQ,CAAC;cAAA,GA3FC7J,IAAI,CAAC5C,OAAO;gBAAAsM,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OA4FjB,CAAC;YAEf,CAAC;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACO,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACP;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACM,CAAC,EAEhBvB,aAAa,CAAChO,YAAY,CAAC,GAAG,CAAC,iBAC9BtB,OAAA,CAAC3G,GAAG;QAACyW,EAAE,EAAE;UAAEsB,OAAO,EAAE,MAAM;UAAEd,cAAc,EAAE,QAAQ;UAAEoD,EAAE,EAAE;QAAE,CAAE;QAAAxD,QAAA,eAC5DlQ,OAAA,CAAC/E,UAAU;UACT0Y,KAAK,EAAErE,aAAa,CAAChO,YAAY,CAAE;UACnCsS,IAAI,EAAErR,WAAY;UAClBoP,QAAQ,EAAEA,CAAC5G,KAAK,EAAES,KAAK,KAAKhJ,cAAc,CAACgJ,KAAK,CAAE;UAClDgF,KAAK,EAAC;QAAS;UAAAE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAChB;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACC,CACN;IAAA,eACD,CAAC;EAEP,CAAC;;EAED;EACA,MAAMgD,yBAAyB,GAAGA,CAAA,KAAM;IACtC,MAAMZ,YAAY,GAAGhE,mBAAmB,CAACzN,sBAAsB,CAAC;IAEhE,IAAIA,sBAAsB,CAACiG,MAAM,KAAK,CAAC,EAAE;MACvC,oBACEzH,OAAA,CAACzF,KAAK;QAACsJ,QAAQ,EAAC,MAAM;QAAAqM,QAAA,EACnB9O,UAAU,IAAIQ,OAAO,CAACI,SAAS,GAC5B,uDAAuD,GACvD;MAAoC;QAAA0O,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACnC,CAAC;IAEZ;IAEA,oBACE7Q,OAAA,CAAAE,SAAA;MAAAgQ,QAAA,gBACElQ,OAAA,CAACrF,cAAc;QAACuY,SAAS,EAAE1Z,KAAM;QAAA0W,QAAA,eAC/BlQ,OAAA,CAACxF,KAAK;UAAC2X,IAAI,EAAC,OAAO;UAAAjC,QAAA,gBACjBlQ,OAAA,CAACpF,SAAS;YAAAsV,QAAA,eACRlQ,OAAA,CAACnF,QAAQ;cAAAqV,QAAA,GACN3M,QAAQ,iBACPvD,OAAA,CAACtF,SAAS;gBAACoZ,OAAO,EAAC,UAAU;gBAAA5D,QAAA,eAC3BlQ,OAAA,CAAClF,UAAU;kBACTqX,IAAI,EAAC,OAAO;kBACZD,OAAO,EAAE7O,aAAa,CAACoE,MAAM,KAAKjG,sBAAsB,CAACiG,MAAM,GAAGiD,cAAc,GAAGJ,cAAe;kBAAA4F,QAAA,EAEjG7M,aAAa,CAACoE,MAAM,KAAKjG,sBAAsB,CAACiG,MAAM,gBAAGzH,OAAA,CAAC1C,SAAS;oBAAAoT,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE,CAAC,gBAAG7Q,OAAA,CAACtC,SAAS;oBAAAgT,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAC7E;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACJ,CACZ,eACD7Q,OAAA,CAACtF,SAAS;gBAAAwV,QAAA,eACRlQ,OAAA,CAAC5E,KAAK;kBAAC+U,SAAS,EAAC,KAAK;kBAACE,UAAU,EAAC,QAAQ;kBAACD,OAAO,EAAE,CAAE;kBAAAF,QAAA,gBACpDlQ,OAAA,CAAC1G,UAAU;oBAACwX,OAAO,EAAC,OAAO;oBAACC,UAAU,EAAC,MAAM;oBAAAb,QAAA,EAAC;kBAAc;oBAAAQ,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAY,CAAC,eACzE7Q,OAAA,CAAClF,UAAU;oBAACqX,IAAI,EAAC,OAAO;oBAACD,OAAO,EAAEA,CAAA,KAAM;sBACtCtP,SAAS,CAAC,oBAAoB,CAAC;sBAC/BE,YAAY,CAACD,SAAS,KAAK,KAAK,GAAG,MAAM,GAAG,KAAK,CAAC;oBACpD,CAAE;oBAAAqN,QAAA,EACCvN,MAAM,KAAK,oBAAoB,GAAIE,SAAS,KAAK,KAAK,gBAAG7C,OAAA,CAAClB,cAAc;sBAAA4R,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAE,CAAC,gBAAG7Q,OAAA,CAACpB,cAAc;sBAAA8R,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAE,CAAC,gBAAI7Q,OAAA,CAACpB,cAAc;sBAAA8R,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAE;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAC7G,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACR;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACC,CAAC,eACZ7Q,OAAA,CAACtF,SAAS;gBAAAwV,QAAA,EAAC;cAAO;gBAAAQ,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAW,CAAC,eAC9B7Q,OAAA,CAACtF,SAAS;gBAAAwV,QAAA,eACRlQ,OAAA,CAAC5E,KAAK;kBAAC+U,SAAS,EAAC,KAAK;kBAACE,UAAU,EAAC,QAAQ;kBAACD,OAAO,EAAE,CAAE;kBAAAF,QAAA,gBACpDlQ,OAAA,CAAC1G,UAAU;oBAACwX,OAAO,EAAC,OAAO;oBAACC,UAAU,EAAC,MAAM;oBAAAb,QAAA,EAAC;kBAAI;oBAAAQ,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAY,CAAC,eAC/D7Q,OAAA,CAAClF,UAAU;oBAACqX,IAAI,EAAC,OAAO;oBAACD,OAAO,EAAEA,CAAA,KAAM;sBACtCtP,SAAS,CAAC,qBAAqB,CAAC;sBAChCE,YAAY,CAACD,SAAS,KAAK,KAAK,GAAG,MAAM,GAAG,KAAK,CAAC;oBACpD,CAAE;oBAAAqN,QAAA,EACCvN,MAAM,KAAK,qBAAqB,GAAIE,SAAS,KAAK,KAAK,gBAAG7C,OAAA,CAAClB,cAAc;sBAAA4R,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAE,CAAC,gBAAG7Q,OAAA,CAACpB,cAAc;sBAAA8R,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAE,CAAC,gBAAI7Q,OAAA,CAACpB,cAAc;sBAAA8R,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAE;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAC9G,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACR;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACC,CAAC,eACZ7Q,OAAA,CAACtF,SAAS;gBAAAwV,QAAA,EAAC;cAAS;gBAAAQ,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAW,CAAC,eAChC7Q,OAAA,CAACtF,SAAS;gBAAAwV,QAAA,EAAC;cAAS;gBAAAQ,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAW,CAAC,eAChC7Q,OAAA,CAACtF,SAAS;gBAAAwV,QAAA,EAAC;cAAS;gBAAAQ,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAW,CAAC,eAChC7Q,OAAA,CAACtF,SAAS;gBAAAwV,QAAA,EAAC;cAAU;gBAAAQ,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAW,CAAC,eACjC7Q,OAAA,CAACtF,SAAS;gBAAAwV,QAAA,EAAC;cAAS;gBAAAQ,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAW,CAAC,eAChC7Q,OAAA,CAACtF,SAAS;gBAAAwV,QAAA,EAAC;cAAM;gBAAAQ,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAW,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACrB;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACF,CAAC,eACZ7Q,OAAA,CAACvF,SAAS;YAAAyV,QAAA,EACP+C,YAAY,CAACzI,GAAG,CAAExC,IAAI,iBACrBhI,OAAA,CAACnF,QAAQ;cAEPkZ,QAAQ,EAAE1Q,aAAa,CAAC4F,QAAQ,CAACjB,IAAI,CAACyC,iBAAiB,CAAE;cACzDuJ,KAAK;cAAA9D,QAAA,GAEJ3M,QAAQ,iBACPvD,OAAA,CAACtF,SAAS;gBAACoZ,OAAO,EAAC,UAAU;gBAAA5D,QAAA,eAC3BlQ,OAAA,CAAClF,UAAU;kBACTqX,IAAI,EAAC,OAAO;kBACZD,OAAO,EAAEA,CAAA,KAAMhI,mBAAmB,CAAClC,IAAI,CAACyC,iBAAiB,CAAE;kBAC3D+F,KAAK,EAAEnN,aAAa,CAAC4F,QAAQ,CAACjB,IAAI,CAACyC,iBAAiB,CAAC,GAAG,SAAS,GAAG,SAAU;kBAAAyF,QAAA,EAE7E7M,aAAa,CAAC4F,QAAQ,CAACjB,IAAI,CAACyC,iBAAiB,CAAC,gBAAGzK,OAAA,CAACtC,SAAS;oBAAAgT,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE,CAAC,gBAAG7Q,OAAA,CAAC5D,OAAO;oBAAAsU,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACnE;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACJ,CACZ,eACD7Q,OAAA,CAACtF,SAAS;gBAAAwV,QAAA,eACRlQ,OAAA,CAAC1G,UAAU;kBAACwX,OAAO,EAAC,OAAO;kBAACC,UAAU,EAAC,QAAQ;kBAAAb,QAAA,EAC5ClI,IAAI,CAAC8B;gBAAkB;kBAAA4G,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACd;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACJ,CAAC,eACZ7Q,OAAA,CAACtF,SAAS;gBAAAwV,QAAA,eACRlQ,OAAA,CAAC3E,IAAI;kBAAC8W,IAAI,EAAC,OAAO;kBAACU,KAAK,EAAE7K,IAAI,CAAC5D,OAAQ;kBAAC0M,OAAO,EAAC;gBAAU;kBAAAJ,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACpD,CAAC,eACZ7Q,OAAA,CAACtF,SAAS;gBAAAwV,QAAA,EAAE,IAAIrI,IAAI,CAACG,IAAI,CAACC,mBAAmB,CAAC,CAACoG,kBAAkB,CAAC;cAAC;gBAAAqC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAY,CAAC,eAChF7Q,OAAA,CAACtF,SAAS;gBAAAwV,QAAA,eACRlQ,OAAA,CAAC5E,KAAK;kBAAC+U,SAAS,EAAC,KAAK;kBAACE,UAAU,EAAC,QAAQ;kBAACD,OAAO,EAAE,CAAE;kBAAAF,QAAA,gBACpDlQ,OAAA,CAACV,UAAU;oBAACmR,QAAQ,EAAC;kBAAO;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE,CAAC,eAC/B7Q,OAAA,CAAC1G,UAAU;oBAACwX,OAAO,EAAC,OAAO;oBAAAZ,QAAA,EAAElI,IAAI,CAAChG,SAAS,IAAIgG,IAAI,CAAC3D;kBAAY;oBAAAqM,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAa,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACzE;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACC,CAAC,eACZ7Q,OAAA,CAACtF,SAAS;gBAAAwV,QAAA,eACRlQ,OAAA,CAAC1G,UAAU;kBAACwX,OAAO,EAAC,OAAO;kBAAAZ,QAAA,EACxBlI,IAAI,CAAC1D,YAAY,GAChB,CAAC,MAAM;oBACL,MAAMjC,SAAS,GAAGnB,SAAS,CAACyK,IAAI,CAACsI,CAAC,IAAIA,CAAC,CAAC3P,YAAY,KAAK0D,IAAI,CAAC1D,YAAY,CAAC;oBAC3E,OAAOjC,SAAS,GAAG,GAAGA,SAAS,CAAC6R,IAAI,MAAM7R,SAAS,CAAC8R,KAAK,EAAE,GAAG,uBAAuB;kBACvF,CAAC,EAAE,CAAC,GACDnM,IAAI,CAACoM,oBAAoB,IAAI;gBAAM;kBAAA1D,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAE9B;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACJ,CAAC,eACZ7Q,OAAA,CAACtF,SAAS;gBAAAwV,QAAA,eACRlQ,OAAA,CAAC1G,UAAU;kBAACwX,OAAO,EAAC,OAAO;kBAAAZ,QAAA,GAAElI,IAAI,CAACzD,kBAAkB,EAAC,IAAE;gBAAA;kBAAAmM,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAY;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC3D,CAAC,eACZ7Q,OAAA,CAACtF,SAAS;gBAAAwV,QAAA,eACRlQ,OAAA,CAAC3E,IAAI;kBACH8W,IAAI,EAAC,OAAO;kBACZU,KAAK,EAAE,GAAG7K,IAAI,CAACvD,iBAAiB,KAAM;kBACtC+L,KAAK,EAAExG,UAAU,CAAChC,IAAI,CAACvD,iBAAiB,CAAC,IAAI,GAAG,GAAG,SAAS,GAAG,SAAU;kBACzEgP,IAAI,EAAEzJ,UAAU,CAAChC,IAAI,CAACvD,iBAAiB,CAAC,IAAI,GAAG,gBAAGzE,OAAA,CAACtC,SAAS;oBAAAgT,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE,CAAC,gBAAG7Q,OAAA,CAACpC,WAAW;oBAAA8S,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE;gBAAE;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACnF;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACO,CAAC,eACZ7Q,OAAA,CAACtF,SAAS;gBAAAwV,QAAA,eACRlQ,OAAA,CAAC3E,IAAI;kBACH8W,IAAI,EAAC,OAAO;kBACZU,KAAK,EAAE7K,IAAI,CAAChD,gBAAgB,IAAI,UAAW;kBAC3CwL,KAAK,EAAExI,IAAI,CAAChD,gBAAgB,KAAK,UAAU,GAAG,SAAS,GAAGgD,IAAI,CAAChD,gBAAgB,KAAK,cAAc,GAAG,OAAO,GAAG;gBAAU;kBAAA0L,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAC1H;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACO,CAAC,eACZ7Q,OAAA,CAACtF,SAAS;gBAAAwV,QAAA,eACRlQ,OAAA,CAAC5E,KAAK;kBAAC+U,SAAS,EAAC,KAAK;kBAACC,OAAO,EAAE,GAAI;kBAAAF,QAAA,gBAClClQ,OAAA,CAAC1E,OAAO;oBAACkY,KAAK,EAAC,qBAAqB;oBAAAtD,QAAA,eAClClQ,OAAA,CAAClF,UAAU;sBACTqX,IAAI,EAAC,OAAO;sBACZD,OAAO,EAAEA,CAAA,KAAM;wBACb9O,eAAe,CAAC4E,IAAI,CAAC;wBACrB9E,aAAa,CAAC,MAAM,CAAC;wBACrBF,aAAa,CAAC,IAAI,CAAC;sBACrB,CAAE;sBAAAkN,QAAA,eAEFlQ,OAAA,CAAClD,QAAQ;wBAAA4T,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAE;oBAAC;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACF;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACN,CAAC,eACV7Q,OAAA,CAAC1E,OAAO;oBAACkY,KAAK,EAAC,YAAY;oBAAAtD,QAAA,eACzBlQ,OAAA,CAAClF,UAAU;sBACTqX,IAAI,EAAC,OAAO;sBACZD,OAAO,EAAEA,CAAA,KAAMhG,iBAAiB,CAAClE,IAAI,CAAE;sBACvCwK,QAAQ,EAAExO,mBAAoB;sBAAAkM,QAAA,eAE9BlQ,OAAA,CAACtD,OAAO;wBAAAgU,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAE;oBAAC;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACD;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACN,CAAC,eACV7Q,OAAA,CAAC1E,OAAO;oBAACkY,KAAK,EAAC,SAAS;oBAAAtD,QAAA,eACtBlQ,OAAA,CAAClF,UAAU;sBACTqX,IAAI,EAAC,OAAO;sBACZ3B,KAAK,EAAC,OAAO;sBACb0B,OAAO,EAAEA,CAAA,KAAMzE,0BAA0B,CAACzF,IAAI,CAAE;sBAChDwK,QAAQ,EAAExO,mBAAoB;sBAAAkM,QAAA,eAE9BlQ,OAAA,CAAChD,UAAU;wBAAA0T,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAE;oBAAC;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACJ;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACN,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACL;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACC,CAAC;YAAA,GA7FP7I,IAAI,CAACyC,iBAAiB;cAAAiG,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OA8FnB,CACX;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACO,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACP;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACM,CAAC,EAEhBvB,aAAa,CAAC9N,sBAAsB,CAAC,GAAG,CAAC,iBACxCxB,OAAA,CAAC3G,GAAG;QAACyW,EAAE,EAAE;UAAEsB,OAAO,EAAE,MAAM;UAAEd,cAAc,EAAE,QAAQ;UAAEoD,EAAE,EAAE;QAAE,CAAE;QAAAxD,QAAA,eAC5DlQ,OAAA,CAAC/E,UAAU;UACT0Y,KAAK,EAAErE,aAAa,CAAC9N,sBAAsB,CAAE;UAC7CoS,IAAI,EAAErR,WAAY;UAClBoP,QAAQ,EAAEA,CAAC5G,KAAK,EAAES,KAAK,KAAKhJ,cAAc,CAACgJ,KAAK,CAAE;UAClDgF,KAAK,EAAC;QAAS;UAAAE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAChB;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACC,CACN;IAAA,eACD,CAAC;EAEP,CAAC;;EAED;EACA,MAAMwD,0BAA0B,GAAGA,CAAA,KAAM;IACvC,IAAIpR,UAAU,KAAK,QAAQ,IAAIA,UAAU,KAAK,MAAM,EAAE,OAAO,IAAI;IAEjE,oBACEjD,OAAA,CAACpG,MAAM;MAAC+J,IAAI,EAAEZ,UAAW;MAACuR,OAAO,EAAEjJ,WAAY;MAACkJ,QAAQ,EAAC,IAAI;MAAC9C,SAAS;MAAAvB,QAAA,gBACrElQ,OAAA,CAACnG,WAAW;QAAAqW,QAAA,EACTjN,UAAU,KAAK,QAAQ,GAAG,sBAAsB,GAAG;MAAyB;QAAAyN,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAClE,CAAC,eACd7Q,OAAA,CAAClG,aAAa;QAAAoW,QAAA,eACZlQ,OAAA,CAACvG,IAAI;UAAC6X,SAAS;UAAClB,OAAO,EAAE,CAAE;UAACN,EAAE,EAAE;YAAE4D,EAAE,EAAE;UAAE,CAAE;UAAAxD,QAAA,gBACxClQ,OAAA,CAACvG,IAAI;YAACkW,IAAI;YAAC4B,EAAE,EAAE,EAAG;YAACC,EAAE,EAAE,CAAE;YAAAtB,QAAA,eACvBlQ,OAAA,CAAC3F,YAAY;cACXma,OAAO,EAAExT,IAAI,CAAC+G,MAAM,CAACf,IAAI,IAAI;gBAC3B;gBACA,MAAMyN,UAAU,GAAGzN,IAAI,CAAC5C,OAAO,KAAKF,QAAQ,CAACE,OAAO;gBACpD,MAAMsQ,cAAc,GAAG,CAAC5T,cAAc,CAACwI,IAAI,CAACtB,IAAI,IAAIA,IAAI,CAAC5D,OAAO,KAAK4C,IAAI,CAAC5C,OAAO,CAAC;gBAClF,MAAMuQ,cAAc,GAAG5N,oBAAoB,CAACC,IAAI,CAAC;gBAEjD,OAAOyN,UAAU,IAAKC,cAAc,IAAIC,cAAe;cACzD,CAAC,CAAE;cACHC,cAAc,EAAG5F,MAAM,IAAK,GAAGA,MAAM,CAAC5K,OAAO,MAAM4K,MAAM,CAACjN,SAAS,EAAG;cACtEyJ,KAAK,EAAExK,IAAI,CAAC2K,IAAI,CAACC,CAAC,IAAIA,CAAC,CAACxH,OAAO,KAAKF,QAAQ,CAACE,OAAO,CAAC,IAAI,IAAK;cAC9DuN,QAAQ,EAAEA,CAAC5G,KAAK,EAAEC,QAAQ,KAAK;gBAC7B,IAAIA,QAAQ,EAAE;kBACZS,gBAAgB,CAACT,QAAQ,CAAC;gBAC5B,CAAC,MAAM;kBACL7G,WAAW,CAACiG,IAAI,KAAK;oBAAE,GAAGA,IAAI;oBAAEhG,OAAO,EAAE,EAAE;oBAAEG,kBAAkB,EAAE;kBAAG,CAAC,CAAC,CAAC;gBACzE;cACF,CAAE;cACFsQ,WAAW,EAAGC,MAAM,iBAClB9U,OAAA,CAAChG,SAAS;gBAAA,GACJ8a,MAAM;gBACVjC,KAAK,EAAC,QAAQ;gBACdnB,WAAW,EAAC,0BAA0B;gBACtCqD,QAAQ;gBACRC,UAAU,EAAC;cAA6E;gBAAAtE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACzF,CACD;cACFoE,YAAY,EAAEA,CAACC,KAAK,EAAElG,MAAM,KAAK;gBAC/B,MAAM3H,YAAY,GAAG2H,MAAM,CAAC3H,YAAY,IAAI,CAAC;gBAC7C,MAAMD,WAAW,GAAGC,YAAY,KAAK,CAAC;gBAEtC,oBACErH,OAAA,CAAC3G,GAAG;kBAAC6Z,SAAS,EAAC,IAAI;kBAAA,GAAKgC,KAAK;kBAAAhF,QAAA,eAC3BlQ,OAAA,CAAC3G,GAAG;oBAACyW,EAAE,EAAE;sBAAEmB,KAAK,EAAE;oBAAO,CAAE;oBAAAf,QAAA,eACzBlQ,OAAA,CAAC5E,KAAK;sBAAC+U,SAAS,EAAC,KAAK;sBAACG,cAAc,EAAC,eAAe;sBAACD,UAAU,EAAC,QAAQ;sBAAAH,QAAA,gBACvElQ,OAAA,CAAC3G,GAAG;wBAAA6W,QAAA,gBACFlQ,OAAA,CAAC1G,UAAU;0BAACwX,OAAO,EAAC,OAAO;0BAACC,UAAU,EAAC,QAAQ;0BAAAb,QAAA,EAC5ClB,MAAM,CAAC5K;wBAAO;0BAAAsM,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OACL,CAAC,eACb7Q,OAAA,CAAC1G,UAAU;0BAACwX,OAAO,EAAC,SAAS;0BAACN,KAAK,EAAC,gBAAgB;0BAAAN,QAAA,GACjDlB,MAAM,CAACjN,SAAS,EAAC,KAAG,EAACiN,MAAM,CAAC9F,mBAAmB,EAAC,UAAG,EAAC8F,MAAM,CAAC7F,iBAAiB;wBAAA;0BAAAuH,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OACnE,CAAC;sBAAA;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACV,CAAC,eACN7Q,OAAA,CAAC5E,KAAK;wBAAC+U,SAAS,EAAC,KAAK;wBAACC,OAAO,EAAE,CAAE;wBAAAF,QAAA,gBAChClQ,OAAA,CAAC3E,IAAI;0BACH8W,IAAI,EAAC,OAAO;0BACZU,KAAK,EAAE7D,MAAM,CAAC9H,mBAAoB;0BAClCsJ,KAAK,EAAExB,MAAM,CAAC9H,mBAAmB,KAAK,YAAY,GAAG,SAAS,GAAG;wBAAU;0BAAAwJ,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAC5E,CAAC,eACF7Q,OAAA,CAAC3E,IAAI;0BACH8W,IAAI,EAAC,OAAO;0BACZU,KAAK,EAAEzL,WAAW,GAAG,WAAW,GAAG,cAAe;0BAClDoJ,KAAK,EAAEpJ,WAAW,GAAG,SAAS,GAAG,SAAU;0BAC3CqM,IAAI,EAAErM,WAAW,gBAAGpH,OAAA,CAACtC,SAAS;4BAAAgT,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OAAE,CAAC,gBAAG7Q,OAAA,CAACpC,WAAW;4BAAA8S,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OAAE;wBAAE;0BAAAH,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OACrD,CAAC;sBAAA;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACG,CAAC;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACH;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACL;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH,CAAC;cAEV;YAAE;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACE,CAAC,eAEP7Q,OAAA,CAACvG,IAAI;YAACkW,IAAI;YAAC4B,EAAE,EAAE,EAAG;YAACC,EAAE,EAAE,CAAE;YAAAtB,QAAA,eACvBlQ,OAAA,CAAChG,SAAS;cACRyX,SAAS;cACToB,KAAK,EAAC,aAAa;cACnBrH,KAAK,EAAEtH,QAAQ,CAACG,YAAa;cAC7BsN,QAAQ,EAAGC,CAAC,IAAKtG,gBAAgB,CAAC,cAAc,EAAEsG,CAAC,CAACC,MAAM,CAACrG,KAAK,CAAE;cAClEuJ,QAAQ;YAAA;cAAArE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACT;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACE,CAAC,eAEP7Q,OAAA,CAACvG,IAAI;YAACkW,IAAI;YAAC4B,EAAE,EAAE,EAAG;YAACC,EAAE,EAAE,CAAE;YAAAtB,QAAA,eACvBlQ,OAAA,CAAC/F,WAAW;cAACwX,SAAS;cAACsD,QAAQ;cAAA7E,QAAA,gBAC7BlQ,OAAA,CAAC9F,UAAU;gBAAAgW,QAAA,EAAC;cAAW;gBAAAQ,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAY,CAAC,eACpC7Q,OAAA,CAAC7F,MAAM;gBACLqR,KAAK,EAAEtH,QAAQ,CAACI,YAAa;gBAC7BqN,QAAQ,EAAGC,CAAC,IAAKtG,gBAAgB,CAAC,cAAc,EAAEsG,CAAC,CAACC,MAAM,CAACrG,KAAK,CAAE;gBAClEqH,KAAK,EAAC,aAAa;gBAAA3C,QAAA,EAElBhP,SAAS,CAACsJ,GAAG,CAAEnI,SAAS,iBACvBrC,OAAA,CAAC5F,QAAQ;kBAA8BoR,KAAK,EAAEnJ,SAAS,CAACiC,YAAa;kBAAA4L,QAAA,GAClE7N,SAAS,CAAC6R,IAAI,EAAC,KAAG,EAAC7R,SAAS,CAAC8R,KAAK,EAAC,GAAC,EAAC9R,SAAS,CAAC8S,OAAO,EAAC,SAAO,EAAC9S,SAAS,CAAC+S,YAAY,EAAC,GACzF;gBAAA,GAFe/S,SAAS,CAACiC,YAAY;kBAAAoM,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAE3B,CACX;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACI,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACE;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACV,CAAC,eAEP7Q,OAAA,CAACvG,IAAI;YAACkW,IAAI;YAAC4B,EAAE,EAAE,EAAG;YAACC,EAAE,EAAE,CAAE;YAAAtB,QAAA,eACvBlQ,OAAA,CAAChG,SAAS;cACRyX,SAAS;cACToB,KAAK,EAAC,0BAA0B;cAChCzF,IAAI,EAAC,QAAQ;cACb5B,KAAK,EAAEtH,QAAQ,CAACK,kBAAmB;cACnCoN,QAAQ,EAAGC,CAAC,IAAKtG,gBAAgB,CAAC,oBAAoB,EAAEsG,CAAC,CAACC,MAAM,CAACrG,KAAK,CAAE;cACxEuJ,QAAQ;YAAA;cAAArE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACT;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACE,CAAC,eAEP7Q,OAAA,CAACvG,IAAI;YAACkW,IAAI;YAAC4B,EAAE,EAAE,EAAG;YAACC,EAAE,EAAE,CAAE;YAAAtB,QAAA,eACvBlQ,OAAA,CAAC/F,WAAW;cAACwX,SAAS;cAAAvB,QAAA,gBACpBlQ,OAAA,CAAC9F,UAAU;gBAAAgW,QAAA,EAAC;cAAU;gBAAAQ,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAY,CAAC,eACnC7Q,OAAA,CAAC7F,MAAM;gBACLqR,KAAK,EAAEtH,QAAQ,CAACM,iBAAkB;gBAClCmN,QAAQ,EAAGC,CAAC,IAAKtG,gBAAgB,CAAC,mBAAmB,EAAEsG,CAAC,CAACC,MAAM,CAACrG,KAAK,CAAE;gBACvEqH,KAAK,EAAC,eAAY;gBAAA3C,QAAA,gBAElBlQ,OAAA,CAAC5F,QAAQ;kBAACoR,KAAK,EAAC,IAAI;kBAAA0E,QAAA,EAAC;gBAAE;kBAAAQ,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAU,CAAC,eAClC7Q,OAAA,CAAC5F,QAAQ;kBAACoR,KAAK,EAAC,KAAK;kBAAA0E,QAAA,EAAC;gBAAG;kBAAAQ,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAU,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC9B,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACE;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACV,CAAC,eAEP7Q,OAAA,CAACvG,IAAI;YAACkW,IAAI;YAAC4B,EAAE,EAAE,EAAG;YAACC,EAAE,EAAE,CAAE;YAAAtB,QAAA,eACvBlQ,OAAA,CAAChG,SAAS;cACRyX,SAAS;cACToB,KAAK,EAAC,wBAAmB;cACzBzF,IAAI,EAAC,QAAQ;cACb5B,KAAK,EAAEtH,QAAQ,CAACO,iBAAkB;cAClCkN,QAAQ,EAAGC,CAAC,IAAKtG,gBAAgB,CAAC,mBAAmB,EAAEsG,CAAC,CAACC,MAAM,CAACrG,KAAK,CAAE;cACvEuJ,QAAQ;cACRC,UAAU,EAAC;YAAmC;cAAAtE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC/C;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACE,CAAC,eAEP7Q,OAAA,CAACvG,IAAI;YAACkW,IAAI;YAAC4B,EAAE,EAAE,EAAG;YAACC,EAAE,EAAE,CAAE;YAAAtB,QAAA,eACvBlQ,OAAA,CAAC/F,WAAW;cAACwX,SAAS;cAAAvB,QAAA,gBACpBlQ,OAAA,CAAC9F,UAAU;gBAAAgW,QAAA,EAAC;cAAU;gBAAAQ,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAY,CAAC,eACnC7Q,OAAA,CAAC7F,MAAM;gBACLqR,KAAK,EAAEtH,QAAQ,CAACQ,iBAAkB;gBAClCiN,QAAQ,EAAGC,CAAC,IAAKtG,gBAAgB,CAAC,mBAAmB,EAAEsG,CAAC,CAACC,MAAM,CAACrG,KAAK,CAAE;gBACvEqH,KAAK,EAAC,YAAY;gBAAA3C,QAAA,gBAElBlQ,OAAA,CAAC5F,QAAQ;kBAACoR,KAAK,EAAC,IAAI;kBAAA0E,QAAA,EAAC;gBAAE;kBAAAQ,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAU,CAAC,eAClC7Q,OAAA,CAAC5F,QAAQ;kBAACoR,KAAK,EAAC,KAAK;kBAAA0E,QAAA,EAAC;gBAAG;kBAAAQ,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAU,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC9B,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACE;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACV,CAAC,eAGP7Q,OAAA,CAACvG,IAAI;YAACkW,IAAI;YAAC4B,EAAE,EAAE,EAAG;YAAArB,QAAA,eAChBlQ,OAAA,CAAC7E,OAAO;cAAC2U,EAAE,EAAE;gBAAE4C,EAAE,EAAE;cAAE,CAAE;cAAAxC,QAAA,eACrBlQ,OAAA,CAAC1G,UAAU;gBAACwX,OAAO,EAAC,OAAO;gBAACN,KAAK,EAAC,gBAAgB;gBAAAN,QAAA,EAAC;cAEnD;gBAAAQ,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAY;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACN;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACN,CAAC,EAEN3M,QAAQ,CAACE,OAAO,IAAI,CAAC,MAAM;YAC1B,MAAM4C,IAAI,GAAGhG,IAAI,CAAC2K,IAAI,CAACC,CAAC,IAAIA,CAAC,CAACxH,OAAO,KAAKF,QAAQ,CAACE,OAAO,CAAC;YAC3D,IAAI,CAAC4C,IAAI,EAAE,OAAO,IAAI;YAEtB,MAAMK,YAAY,GAAGL,IAAI,CAACK,YAAY,IAAI,CAAC;YAC3C,MAAMD,WAAW,GAAGC,YAAY,KAAK,CAAC;YAEtC,oBACErH,OAAA,CAACvG,IAAI;cAACkW,IAAI;cAAC4B,EAAE,EAAE,EAAG;cAAArB,QAAA,eAChBlQ,OAAA,CAACxG,KAAK;gBAACsW,EAAE,EAAE;kBAAEC,CAAC,EAAE,CAAC;kBAAEE,OAAO,EAAE7I,WAAW,GAAG,eAAe,GAAG;gBAAgB,CAAE;gBAAA8I,QAAA,eAC5ElQ,OAAA,CAAC5E,KAAK;kBAAC+U,SAAS,EAAC,KAAK;kBAACE,UAAU,EAAC,QAAQ;kBAACD,OAAO,EAAE,CAAE;kBAAAF,QAAA,GACnD9I,WAAW,gBAAGpH,OAAA,CAACtC,SAAS;oBAAC8S,KAAK,EAAC;kBAAS;oBAAAE,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE,CAAC,gBAAG7Q,OAAA,CAACpC,WAAW;oBAAC4S,KAAK,EAAC;kBAAS;oBAAAE,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE,CAAC,eAC9E7Q,OAAA,CAAC3G,GAAG;oBAAA6W,QAAA,gBACFlQ,OAAA,CAAC1G,UAAU;sBAACwX,OAAO,EAAC,OAAO;sBAACC,UAAU,EAAC,MAAM;sBAAAb,QAAA,EAC1C9I,WAAW,GAAG,8BAA8B,GAAG;oBAAkC;sBAAAsJ,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACxE,CAAC,eACb7Q,OAAA,CAAC1G,UAAU;sBAACwX,OAAO,EAAC,SAAS;sBAAAZ,QAAA,GAAC,SACrB,EAAC7I,YAAY,KAAK,CAAC,GAAG,eAAe,GACpCA,YAAY,KAAK,CAAC,GAAG,yBAAyB,GAC9CA,YAAY,KAAK,CAAC,GAAG,uBAAuB,GAC5CA,YAAY,KAAK,CAAC,GAAG,yBAAyB,GAC9C,mBAAmB;oBAAA;sBAAAqJ,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACjB,CAAC,EACZ,CAACzJ,WAAW,iBACXpH,OAAA,CAAC1G,UAAU;sBAACwX,OAAO,EAAC,SAAS;sBAACM,OAAO,EAAC,OAAO;sBAACtB,EAAE,EAAE;wBAAE4D,EAAE,EAAE;sBAAE,CAAE;sBAAAxD,QAAA,EAAC;oBAE7D;sBAAAQ,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAY,CACb;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACE,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACD;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACJ,CAAC;UAEX,CAAC,EAAE,CAAC,eAGJ7Q,OAAA,CAACvG,IAAI;YAACkW,IAAI;YAAC4B,EAAE,EAAE,EAAG;YAAArB,QAAA,eAChBlQ,OAAA,CAAC7E,OAAO;cAAC2U,EAAE,EAAE;gBAAE4C,EAAE,EAAE;cAAE,CAAE;cAAAxC,QAAA,eACrBlQ,OAAA,CAAC1G,UAAU;gBAACwX,OAAO,EAAC,OAAO;gBAACN,KAAK,EAAC,gBAAgB;gBAAAN,QAAA,EAAC;cAEnD;gBAAAQ,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAY;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACN;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACN,CAAC,eAEP7Q,OAAA,CAACvG,IAAI;YAACkW,IAAI;YAAC4B,EAAE,EAAE,EAAG;YAACC,EAAE,EAAE,CAAE;YAAAtB,QAAA,eACvBlQ,OAAA,CAAChG,SAAS;cACRyX,SAAS;cACToB,KAAK,EAAC,8BAA2B;cACjCzF,IAAI,EAAC,QAAQ;cACb5B,KAAK,EAAEtH,QAAQ,CAACU,oBAAqB;cACrC+M,QAAQ,EAAGC,CAAC,IAAKtG,gBAAgB,CAAC,sBAAsB,EAAEsG,CAAC,CAACC,MAAM,CAACrG,KAAK,CAAE;cAC1EwJ,UAAU,EAAC;YAA6B;cAAAtE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACzC;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACE,CAAC,eAEP7Q,OAAA,CAACvG,IAAI;YAACkW,IAAI;YAAC4B,EAAE,EAAE,EAAG;YAACC,EAAE,EAAE,CAAE;YAAAtB,QAAA,eACvBlQ,OAAA,CAAChG,SAAS;cACRyX,SAAS;cACToB,KAAK,EAAC,gBAAa;cACnBzF,IAAI,EAAC,QAAQ;cACb5B,KAAK,EAAEtH,QAAQ,CAACW,OAAQ;cACxB8M,QAAQ,EAAGC,CAAC,IAAKtG,gBAAgB,CAAC,SAAS,EAAEsG,CAAC,CAACC,MAAM,CAACrG,KAAK,CAAE;cAC7DwJ,UAAU,EAAC;YAAkB;cAAAtE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC9B;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACE,CAAC,eAEP7Q,OAAA,CAACvG,IAAI;YAACkW,IAAI;YAAC4B,EAAE,EAAE,EAAG;YAACC,EAAE,EAAE,CAAE;YAAAtB,QAAA,eACvBlQ,OAAA,CAAChG,SAAS;cACRyX,SAAS;cACToB,KAAK,EAAC,uBAAuB;cAC7BzF,IAAI,EAAC,QAAQ;cACb5B,KAAK,EAAEtH,QAAQ,CAACY,cAAe;cAC/B6M,QAAQ,EAAGC,CAAC,IAAKtG,gBAAgB,CAAC,gBAAgB,EAAEsG,CAAC,CAACC,MAAM,CAACrG,KAAK,CAAE;cACpEwJ,UAAU,EAAC;YAAgC;cAAAtE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC5C;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACE,CAAC,eAEP7Q,OAAA,CAACvG,IAAI;YAACkW,IAAI;YAAC4B,EAAE,EAAE,EAAG;YAACC,EAAE,EAAE,CAAE;YAAAtB,QAAA,eACvBlQ,OAAA,CAAChG,SAAS;cACRyX,SAAS;cACToB,KAAK,EAAC,oBAAoB;cAC1BzF,IAAI,EAAC,QAAQ;cACb5B,KAAK,EAAEtH,QAAQ,CAACa,YAAa;cAC7B4M,QAAQ,EAAGC,CAAC,IAAKtG,gBAAgB,CAAC,cAAc,EAAEsG,CAAC,CAACC,MAAM,CAACrG,KAAK,CAAE;cAClEwJ,UAAU,EAAC;YAA2B;cAAAtE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACvC;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACE,CAAC,eAEP7Q,OAAA,CAACvG,IAAI;YAACkW,IAAI;YAAC4B,EAAE,EAAE,EAAG;YAACC,EAAE,EAAE,CAAE;YAAAtB,QAAA,eACvBlQ,OAAA,CAAC/F,WAAW;cAACwX,SAAS;cAAAvB,QAAA,gBACpBlQ,OAAA,CAAC9F,UAAU;gBAAAgW,QAAA,EAAC;cAAgB;gBAAAQ,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAY,CAAC,eACzC7Q,OAAA,CAAC7F,MAAM;gBACLqR,KAAK,EAAEtH,QAAQ,CAACc,gBAAiB;gBACjC2M,QAAQ,EAAGC,CAAC,IAAKtG,gBAAgB,CAAC,kBAAkB,EAAEsG,CAAC,CAACC,MAAM,CAACrG,KAAK,CAAE;gBACtEqH,KAAK,EAAC,kBAAkB;gBAAA3C,QAAA,gBAExBlQ,OAAA,CAAC5F,QAAQ;kBAACoR,KAAK,EAAC,UAAU;kBAAA0E,QAAA,eACxBlQ,OAAA,CAAC5E,KAAK;oBAAC+U,SAAS,EAAC,KAAK;oBAACE,UAAU,EAAC,QAAQ;oBAACD,OAAO,EAAE,CAAE;oBAAAF,QAAA,gBACpDlQ,OAAA,CAACtC,SAAS;sBAAC8S,KAAK,EAAC;oBAAS;sBAAAE,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAE,CAAC,eAC7B7Q,OAAA,CAAC1G,UAAU;sBAAA4W,QAAA,EAAC;oBAAQ;sBAAAQ,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAY,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAC5B;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACA,CAAC,eACX7Q,OAAA,CAAC5F,QAAQ;kBAACoR,KAAK,EAAC,cAAc;kBAAA0E,QAAA,eAC5BlQ,OAAA,CAAC5E,KAAK;oBAAC+U,SAAS,EAAC,KAAK;oBAACE,UAAU,EAAC,QAAQ;oBAACD,OAAO,EAAE,CAAE;oBAAAF,QAAA,gBACpDlQ,OAAA,CAACd,SAAS;sBAACsR,KAAK,EAAC;oBAAO;sBAAAE,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAE,CAAC,eAC3B7Q,OAAA,CAAC1G,UAAU;sBAAA4W,QAAA,EAAC;oBAAY;sBAAAQ,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAY,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAChC;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACA,CAAC,eACX7Q,OAAA,CAAC5F,QAAQ;kBAACoR,KAAK,EAAC,eAAe;kBAAA0E,QAAA,eAC7BlQ,OAAA,CAAC5E,KAAK;oBAAC+U,SAAS,EAAC,KAAK;oBAACE,UAAU,EAAC,QAAQ;oBAACD,OAAO,EAAE,CAAE;oBAAAF,QAAA,gBACpDlQ,OAAA,CAACpC,WAAW;sBAAC4S,KAAK,EAAC;oBAAS;sBAAAE,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAE,CAAC,eAC/B7Q,OAAA,CAAC1G,UAAU;sBAAA4W,QAAA,EAAC;oBAAa;sBAAAQ,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAY,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACjC;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACA,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACL,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACE;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACV,CAAC,eAEP7Q,OAAA,CAACvG,IAAI;YAACkW,IAAI;YAAC4B,EAAE,EAAE,EAAG;YAAArB,QAAA,eAChBlQ,OAAA,CAAChG,SAAS;cACRyX,SAAS;cACToB,KAAK,EAAC,MAAM;cACZwC,SAAS;cACTjH,IAAI,EAAE,CAAE;cACR5C,KAAK,EAAEtH,QAAQ,CAACS,IAAK;cACrBgN,QAAQ,EAAGC,CAAC,IAAKtG,gBAAgB,CAAC,MAAM,EAAEsG,CAAC,CAACC,MAAM,CAACrG,KAAK,CAAE;cAC1DkG,WAAW,EAAC;YAAkF;cAAAhB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC/F;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACE,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACM,CAAC,eAChB7Q,OAAA,CAACjG,aAAa;QAAAmW,QAAA,gBACZlQ,OAAA,CAACzG,MAAM;UAAC2Y,OAAO,EAAE7G,WAAY;UAAA6E,QAAA,EAAC;QAAO;UAAAQ,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC,eAC9C7Q,OAAA,CAACzG,MAAM;UACL2Y,OAAO,EAAExG,0BAA2B;UACpCoF,OAAO,EAAC,WAAW;UACnB0B,QAAQ,EAAE9R,OAAO,IAAI,CAACwD,QAAQ,CAACE,OAAO,IAAI,CAACF,QAAQ,CAACG,YAAY,IAAI,CAACH,QAAQ,CAACI,YAAY,IAAI,CAACJ,QAAQ,CAACO,iBAAkB;UAC1H2N,SAAS,EAAE1R,OAAO,gBAAGV,OAAA,CAAC1F,gBAAgB;YAAC6X,IAAI,EAAE;UAAG;YAAAzB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,gBAAG7Q,OAAA,CAAC5C,QAAQ;YAAAsT,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAE;UAAAX,QAAA,EAElEjN,UAAU,KAAK,QAAQ,GAAG,qBAAqB,GAAG;QAAiB;UAAAyN,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC9D,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACI,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACV,CAAC;EAEb,CAAC;;EAED;EACA,MAAMyE,gBAAgB,GAAGA,CAAA,KAAM;IAC7B,IAAIrS,UAAU,KAAK,MAAM,IAAI,CAACE,YAAY,EAAE,OAAO,IAAI;IAEvD,oBACEnD,OAAA,CAACpG,MAAM;MAAC+J,IAAI,EAAEZ,UAAW;MAACuR,OAAO,EAAEjJ,WAAY;MAACkJ,QAAQ,EAAC,IAAI;MAAC9C,SAAS;MAAAvB,QAAA,gBACrElQ,OAAA,CAACnG,WAAW;QAAAqW,QAAA,GAAC,4BACe,EAAC/M,YAAY,CAAC2G,kBAAkB;MAAA;QAAA4G,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC/C,CAAC,eACd7Q,OAAA,CAAClG,aAAa;QAAAoW,QAAA,eACZlQ,OAAA,CAACvG,IAAI;UAAC6X,SAAS;UAAClB,OAAO,EAAE,CAAE;UAACN,EAAE,EAAE;YAAE4D,EAAE,EAAE;UAAE,CAAE;UAAAxD,QAAA,gBACxClQ,OAAA,CAACvG,IAAI;YAACkW,IAAI;YAAC4B,EAAE,EAAE,EAAG;YAACC,EAAE,EAAE,CAAE;YAAAtB,QAAA,eACvBlQ,OAAA,CAACtG,IAAI;cAACoX,OAAO,EAAC,UAAU;cAAAZ,QAAA,eACtBlQ,OAAA,CAACrG,WAAW;gBAAAuW,QAAA,gBACVlQ,OAAA,CAAC1G,UAAU;kBAACwX,OAAO,EAAC,IAAI;kBAACyE,YAAY;kBAAArF,QAAA,EAAC;gBAEtC;kBAAAQ,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAY,CAAC,eACb7Q,OAAA,CAAC1G,UAAU;kBAACwX,OAAO,EAAC,OAAO;kBAACN,KAAK,EAAC,gBAAgB;kBAAAN,QAAA,GAAC,WACxC,eAAAlQ,OAAA;oBAAAkQ,QAAA,EAAS/M,YAAY,CAACiB;kBAAO;oBAAAsM,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAS,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACtC,CAAC,eACb7Q,OAAA,CAAC1G,UAAU;kBAACwX,OAAO,EAAC,OAAO;kBAACN,KAAK,EAAC,gBAAgB;kBAAAN,QAAA,GAAC,sBAC7B,eAAAlQ,OAAA;oBAAAkQ,QAAA,GAAS/M,YAAY,CAACoB,kBAAkB,EAAC,IAAE;kBAAA;oBAAAmM,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAQ,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAC9D,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACF;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACV;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC,eAEP7Q,OAAA,CAACvG,IAAI;YAACkW,IAAI;YAAC4B,EAAE,EAAE,EAAG;YAACC,EAAE,EAAE,CAAE;YAAAtB,QAAA,eACvBlQ,OAAA,CAACtG,IAAI;cAACoX,OAAO,EAAC,UAAU;cAAAZ,QAAA,eACtBlQ,OAAA,CAACrG,WAAW;gBAAAuW,QAAA,gBACVlQ,OAAA,CAAC1G,UAAU;kBAACwX,OAAO,EAAC,IAAI;kBAACyE,YAAY;kBAAArF,QAAA,EAAC;gBAEtC;kBAAAQ,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAY,CAAC,eACb7Q,OAAA,CAAC1G,UAAU;kBAACwX,OAAO,EAAC,OAAO;kBAACN,KAAK,EAAC,gBAAgB;kBAAAN,QAAA,GAAC,UACzC,eAAAlQ,OAAA;oBAAAkQ,QAAA,EAAS/M,YAAY,CAAC2G;kBAAkB;oBAAA4G,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAS,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAChD,CAAC,eACb7Q,OAAA,CAAC1G,UAAU;kBAACwX,OAAO,EAAC,OAAO;kBAACN,KAAK,EAAC,gBAAgB;kBAAAN,QAAA,GAAC,QAC3C,eAAAlQ,OAAA;oBAAAkQ,QAAA,EAAS,IAAIrI,IAAI,CAAC1E,YAAY,CAAC8E,mBAAmB,CAAC,CAACoG,kBAAkB,CAAC;kBAAC;oBAAAqC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAS,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAC9E,CAAC,eACb7Q,OAAA,CAAC1G,UAAU;kBAACwX,OAAO,EAAC,OAAO;kBAACN,KAAK,EAAC,gBAAgB;kBAAAN,QAAA,GAAC,aACtC,eAAAlQ,OAAA;oBAAAkQ,QAAA,EAAS/M,YAAY,CAACnB,SAAS,IAAImB,YAAY,CAACkB;kBAAY;oBAAAqM,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAS,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACvE,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACF;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACV;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC,eAEP7Q,OAAA,CAACvG,IAAI;YAACkW,IAAI;YAAC4B,EAAE,EAAE,EAAG;YAAArB,QAAA,eAChBlQ,OAAA,CAACtG,IAAI;cAACoX,OAAO,EAAC,UAAU;cAAAZ,QAAA,eACtBlQ,OAAA,CAACrG,WAAW;gBAAAuW,QAAA,gBACVlQ,OAAA,CAAC1G,UAAU;kBAACwX,OAAO,EAAC,IAAI;kBAACyE,YAAY;kBAAArF,QAAA,EAAC;gBAEtC;kBAAAQ,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAY,CAAC,eACb7Q,OAAA,CAACvG,IAAI;kBAAC6X,SAAS;kBAAClB,OAAO,EAAE,CAAE;kBAAAF,QAAA,gBACzBlQ,OAAA,CAACvG,IAAI;oBAACkW,IAAI;oBAAC4B,EAAE,EAAE,CAAE;oBAAArB,QAAA,gBACflQ,OAAA,CAAC1G,UAAU;sBAACwX,OAAO,EAAC,OAAO;sBAACN,KAAK,EAAC,gBAAgB;sBAAAN,QAAA,EAAC;oBAEnD;sBAAAQ,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAY,CAAC,eACb7Q,OAAA,CAAC3E,IAAI;sBACH8W,IAAI,EAAC,OAAO;sBACZU,KAAK,EAAE1P,YAAY,CAACqB,iBAAkB;sBACtCgM,KAAK,EAAErN,YAAY,CAACqB,iBAAiB,KAAK,IAAI,GAAG,SAAS,GAAG;oBAAQ;sBAAAkM,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACtE,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACE,CAAC,eACP7Q,OAAA,CAACvG,IAAI;oBAACkW,IAAI;oBAAC4B,EAAE,EAAE,CAAE;oBAAArB,QAAA,gBACflQ,OAAA,CAAC1G,UAAU;sBAACwX,OAAO,EAAC,OAAO;sBAACN,KAAK,EAAC,gBAAgB;sBAAAN,QAAA,EAAC;oBAEnD;sBAAAQ,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAY,CAAC,eACb7Q,OAAA,CAAC3E,IAAI;sBACH8W,IAAI,EAAC,OAAO;sBACZU,KAAK,EAAE,GAAG1P,YAAY,CAACsB,iBAAiB,KAAM;sBAC9C+L,KAAK,EAAExG,UAAU,CAAC7G,YAAY,CAACsB,iBAAiB,CAAC,IAAI,GAAG,GAAG,SAAS,GAAG;oBAAU;sBAAAiM,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAClF,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACE,CAAC,eACP7Q,OAAA,CAACvG,IAAI;oBAACkW,IAAI;oBAAC4B,EAAE,EAAE,CAAE;oBAAArB,QAAA,gBACflQ,OAAA,CAAC1G,UAAU;sBAACwX,OAAO,EAAC,OAAO;sBAACN,KAAK,EAAC,gBAAgB;sBAAAN,QAAA,EAAC;oBAEnD;sBAAAQ,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAY,CAAC,eACb7Q,OAAA,CAAC3E,IAAI;sBACH8W,IAAI,EAAC,OAAO;sBACZU,KAAK,EAAE1P,YAAY,CAACuB,iBAAkB;sBACtC8L,KAAK,EAAErN,YAAY,CAACuB,iBAAiB,KAAK,IAAI,GAAG,SAAS,GAAG;oBAAQ;sBAAAgM,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACtE,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACE,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACI;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACV;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC,EAEN1N,YAAY,CAACwB,IAAI,iBAChB3E,OAAA,CAACvG,IAAI;YAACkW,IAAI;YAAC4B,EAAE,EAAE,EAAG;YAAArB,QAAA,eAChBlQ,OAAA,CAACtG,IAAI;cAACoX,OAAO,EAAC,UAAU;cAAAZ,QAAA,eACtBlQ,OAAA,CAACrG,WAAW;gBAAAuW,QAAA,gBACVlQ,OAAA,CAAC1G,UAAU;kBAACwX,OAAO,EAAC,IAAI;kBAACyE,YAAY;kBAAArF,QAAA,EAAC;gBAEtC;kBAAAQ,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAY,CAAC,eACb7Q,OAAA,CAAC1G,UAAU;kBAACwX,OAAO,EAAC,OAAO;kBAAAZ,QAAA,EACxB/M,YAAY,CAACwB;gBAAI;kBAAA+L,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACR,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACF;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACV;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CACP;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACG;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACM,CAAC,eAChB7Q,OAAA,CAACjG,aAAa;QAAAmW,QAAA,gBACZlQ,OAAA,CAACzG,MAAM;UAAC2Y,OAAO,EAAE7G,WAAY;UAAA6E,QAAA,EAAC;QAAM;UAAAQ,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC,eAC7C7Q,OAAA,CAACzG,MAAM;UACL2Y,OAAO,EAAEA,CAAA,KAAMhG,iBAAiB,CAAC/I,YAAY,CAAE;UAC/C2N,OAAO,EAAC,WAAW;UACnBsB,SAAS,eAAEpS,OAAA,CAACtD,OAAO;YAAAgU,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAE;UACvB2B,QAAQ,EAAE9R,OAAQ;UAAAwP,QAAA,EACnB;QAED;UAAAQ,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACI,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACV,CAAC;EAEb,CAAC;;EAED;EACA,MAAM2E,WAAW,GAAGA,CAAA,KAAM;IACxB,MAAMC,SAAS,GAAGzU,IAAI,CAACyG,MAAM;IAC7B,MAAMiO,cAAc,GAAG1U,IAAI,CAAC+G,MAAM,CAAC6D,CAAC,IAAIA,CAAC,CAAC1E,mBAAmB,KAAK,YAAY,CAAC,CAACO,MAAM;IACtF,MAAMrC,eAAe,GAAGtE,cAAc,CAAC2G,MAAM;IAC7C,MAAMkO,yBAAyB,GAAGF,SAAS,GAAG,CAAC,GAAG/N,IAAI,CAACC,KAAK,CAAEvC,eAAe,GAAGsQ,cAAc,GAAI,GAAG,CAAC,GAAG,CAAC;IAE1G,oBACE1V,OAAA,CAACvG,IAAI;MAAC6X,SAAS;MAAClB,OAAO,EAAE,CAAE;MAACN,EAAE,EAAE;QAAEE,EAAE,EAAE;MAAE,CAAE;MAAAE,QAAA,gBACxClQ,OAAA,CAACvG,IAAI;QAACkW,IAAI;QAAC4B,EAAE,EAAE,EAAG;QAACqE,EAAE,EAAE,CAAE;QAACpE,EAAE,EAAE,CAAE;QAAAtB,QAAA,eAC9BlQ,OAAA,CAACtG,IAAI;UAAAwW,QAAA,eACHlQ,OAAA,CAACrG,WAAW;YAAAuW,QAAA,gBACVlQ,OAAA,CAAC1G,UAAU;cAACkX,KAAK,EAAC,gBAAgB;cAAC+E,YAAY;cAAArF,QAAA,EAAC;YAEhD;cAAAQ,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAY,CAAC,eACb7Q,OAAA,CAAC1G,UAAU;cAACwX,OAAO,EAAC,IAAI;cAAAZ,QAAA,EACrBuF;YAAS;cAAA/E,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACA,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACF;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACV;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,eACP7Q,OAAA,CAACvG,IAAI;QAACkW,IAAI;QAAC4B,EAAE,EAAE,EAAG;QAACqE,EAAE,EAAE,CAAE;QAACpE,EAAE,EAAE,CAAE;QAAAtB,QAAA,eAC9BlQ,OAAA,CAACtG,IAAI;UAAAwW,QAAA,eACHlQ,OAAA,CAACrG,WAAW;YAAAuW,QAAA,gBACVlQ,OAAA,CAAC1G,UAAU;cAACkX,KAAK,EAAC,gBAAgB;cAAC+E,YAAY;cAAArF,QAAA,EAAC;YAEhD;cAAAQ,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAY,CAAC,eACb7Q,OAAA,CAAC1G,UAAU;cAACwX,OAAO,EAAC,IAAI;cAAAZ,QAAA,EACrBwF;YAAc;cAAAhF,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACL,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACF;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACV;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,eACP7Q,OAAA,CAACvG,IAAI;QAACkW,IAAI;QAAC4B,EAAE,EAAE,EAAG;QAACqE,EAAE,EAAE,CAAE;QAACpE,EAAE,EAAE,CAAE;QAAAtB,QAAA,eAC9BlQ,OAAA,CAACtG,IAAI;UAAAwW,QAAA,eACHlQ,OAAA,CAACrG,WAAW;YAAAuW,QAAA,gBACVlQ,OAAA,CAAC1G,UAAU;cAACkX,KAAK,EAAC,gBAAgB;cAAC+E,YAAY;cAAArF,QAAA,EAAC;YAEhD;cAAAQ,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAY,CAAC,eACb7Q,OAAA,CAAC1G,UAAU;cAACwX,OAAO,EAAC,IAAI;cAAAZ,QAAA,EACrB9K;YAAe;cAAAsL,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACN,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACF;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACV;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,eACP7Q,OAAA,CAACvG,IAAI;QAACkW,IAAI;QAAC4B,EAAE,EAAE,EAAG;QAACqE,EAAE,EAAE,CAAE;QAACpE,EAAE,EAAE,CAAE;QAAAtB,QAAA,eAC9BlQ,OAAA,CAACtG,IAAI;UAAAwW,QAAA,eACHlQ,OAAA,CAACrG,WAAW;YAAAuW,QAAA,gBACVlQ,OAAA,CAAC1G,UAAU;cAACkX,KAAK,EAAC,gBAAgB;cAAC+E,YAAY;cAAArF,QAAA,EAAC;YAEhD;cAAAQ,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAY,CAAC,eACb7Q,OAAA,CAAC1G,UAAU;cAACwX,OAAO,EAAC,IAAI;cAACN,KAAK,EAAEmF,yBAAyB,IAAI,EAAE,GAAG,cAAc,GAAG,cAAe;cAAAzF,QAAA,GAC/FyF,yBAAyB,EAAC,GAC7B;YAAA;cAAAjF,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAY,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACF;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACV;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC;EAEX,CAAC;EAED,oBACE7Q,OAAA,CAAC/D,SAAS;IAACsY,QAAQ,EAAC,IAAI;IAACzE,EAAE,EAAE;MAAE+F,EAAE,EAAE;IAAE,CAAE;IAAA3F,QAAA,GAEpCL,eAAe,CAAC,CAAC,EAGjB,CAACnP,OAAO,IAAIsD,mBAAmB,kBAC9BhE,OAAA,CAAC3G,GAAG;MAACyW,EAAE,EAAE;QAAEE,EAAE,EAAE;MAAE,CAAE;MAAAE,QAAA,gBACjBlQ,OAAA,CAACxE,cAAc;QAAAkV,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC,EACjB/M,QAAQ,GAAG,CAAC,iBACX9D,OAAA,CAAC1G,UAAU;QAACwX,OAAO,EAAC,SAAS;QAACN,KAAK,EAAC,gBAAgB;QAACV,EAAE,EAAE;UAAE4D,EAAE,EAAE;QAAE,CAAE;QAAAxD,QAAA,GAAC,iBACnD,EAACpM,QAAQ,EAAC,GAC3B;MAAA;QAAA4M,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAY,CACb;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACE,CACN,eAGD7Q,OAAA,CAACxG,KAAK;MAACsW,EAAE,EAAE;QAAEE,EAAE,EAAE;MAAE,CAAE;MAAAE,QAAA,eACnBlQ,OAAA,CAACjF,IAAI;QACHyQ,KAAK,EAAE5K,SAAU;QACjB+Q,QAAQ,EAAE7G,eAAgB;QAC1BgL,cAAc,EAAC,SAAS;QACxBC,SAAS,EAAC,SAAS;QACnBjF,OAAO,EAAC,WAAW;QAAAZ,QAAA,gBAEnBlQ,OAAA,CAAChF,GAAG;UACF6X,KAAK,eACH7S,OAAA,CAAC3G,GAAG;YAAA6W,QAAA,gBACFlQ,OAAA,CAAC1G,UAAU;cAACwX,OAAO,EAAC,OAAO;cAACC,UAAU,EAAC,MAAM;cAAAb,QAAA,EAAC;YAE9C;cAAAQ,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAY,CAAC,eACb7Q,OAAA,CAAC1G,UAAU;cAACwX,OAAO,EAAC,SAAS;cAACN,KAAK,EAAC,gBAAgB;cAAAN,QAAA,GACjD5O,YAAY,CAACmG,MAAM,EAAC,cACvB;YAAA;cAAAiJ,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAY,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACV;QACN;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACF,CAAC,eACF7Q,OAAA,CAAChF,GAAG;UACF6X,KAAK,eACH7S,OAAA,CAAC3G,GAAG;YAAA6W,QAAA,gBACFlQ,OAAA,CAAC1G,UAAU;cAACwX,OAAO,EAAC,OAAO;cAACC,UAAU,EAAC,MAAM;cAAAb,QAAA,EAAC;YAE9C;cAAAQ,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAY,CAAC,eACb7Q,OAAA,CAAC1G,UAAU;cAACwX,OAAO,EAAC,SAAS;cAACN,KAAK,EAAC,gBAAgB;cAAAN,QAAA,GACjD1O,sBAAsB,CAACiG,MAAM,EAAC,iBACjC;YAAA;cAAAiJ,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAY,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACV;QACN;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACF,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACE;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACF,CAAC,EAGPQ,sBAAsB,CAAC,CAAC,EAGxB,CAAC3Q,OAAO,IAAIE,SAAS,KAAK,CAAC,IAAIoS,eAAe,CAAC,CAAC,EAChD,CAACtS,OAAO,IAAIE,SAAS,KAAK,CAAC,IAAIiT,yBAAyB,CAAC,CAAC,EAG1DQ,0BAA0B,CAAC,CAAC,EAC5BiB,gBAAgB,CAAC,CAAC,eAGnBtV,OAAA,CAAClE,QAAQ;MACP6H,IAAI,EAAEF,QAAQ,CAACE,IAAK;MACpBqS,gBAAgB,EAAE,IAAK;MACvB1B,OAAO,EAAE9L,aAAc;MACvByN,YAAY,EAAE;QAAEC,QAAQ,EAAE,QAAQ;QAAEC,UAAU,EAAE;MAAQ,CAAE;MAAAjG,QAAA,eAE1DlQ,OAAA,CAACzF,KAAK;QAAC+Z,OAAO,EAAE9L,aAAc;QAAC3E,QAAQ,EAAEJ,QAAQ,CAACI,QAAS;QAACiM,EAAE,EAAE;UAAEmB,KAAK,EAAE;QAAO,CAAE;QAAAf,QAAA,EAC/EzM,QAAQ,CAACG;MAAO;QAAA8M,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACZ;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACA,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OAGF,CAAC;AAEhB,CAAC,kCAAC;AAACuF,GAAA,GAp8DGjW,0BAA0B;AAs8DhC,eAAeA,0BAA0B;AAAC,IAAAE,EAAA,EAAA+V,GAAA;AAAAC,YAAA,CAAAhW,EAAA;AAAAgW,YAAA,CAAAD,GAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}