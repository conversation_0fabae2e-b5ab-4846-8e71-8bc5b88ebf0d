{"ast": null, "code": "import { toDate } from \"./toDate.mjs\";\n\n/**\n * @name getSeconds\n * @category Second Helpers\n * @summary Get the seconds of the given date.\n *\n * @description\n * Get the seconds of the given date.\n *\n * @typeParam DateType - The `Date` type, the function operates on. Gets inferred from passed arguments. Allows to use extensions like [`UTCDate`](https://github.com/date-fns/utc).\n *\n * @param date - The given date\n *\n * @returns The seconds\n *\n * @example\n * // Get the seconds of 29 February 2012 11:45:05.123:\n * const result = getSeconds(new Date(2012, 1, 29, 11, 45, 5, 123))\n * //=> 5\n */\nexport function getSeconds(date) {\n  const _date = toDate(date);\n  const seconds = _date.getSeconds();\n  return seconds;\n}\n\n// Fallback for modularized imports:\nexport default getSeconds;", "map": {"version": 3, "names": ["toDate", "getSeconds", "date", "_date", "seconds"], "sources": ["C:/CMS/webapp/frontend/node_modules/date-fns/getSeconds.mjs"], "sourcesContent": ["import { toDate } from \"./toDate.mjs\";\n\n/**\n * @name getSeconds\n * @category Second Helpers\n * @summary Get the seconds of the given date.\n *\n * @description\n * Get the seconds of the given date.\n *\n * @typeParam DateType - The `Date` type, the function operates on. Gets inferred from passed arguments. Allows to use extensions like [`UTCDate`](https://github.com/date-fns/utc).\n *\n * @param date - The given date\n *\n * @returns The seconds\n *\n * @example\n * // Get the seconds of 29 February 2012 11:45:05.123:\n * const result = getSeconds(new Date(2012, 1, 29, 11, 45, 5, 123))\n * //=> 5\n */\nexport function getSeconds(date) {\n  const _date = toDate(date);\n  const seconds = _date.getSeconds();\n  return seconds;\n}\n\n// Fallback for modularized imports:\nexport default getSeconds;\n"], "mappings": "AAAA,SAASA,MAAM,QAAQ,cAAc;;AAErC;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO,SAASC,UAAUA,CAACC,IAAI,EAAE;EAC/B,MAAMC,KAAK,GAAGH,MAAM,CAACE,IAAI,CAAC;EAC1B,MAAME,OAAO,GAAGD,KAAK,CAACF,UAAU,CAAC,CAAC;EAClC,OAAOG,OAAO;AAChB;;AAEA;AACA,eAAeH,UAAU", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}