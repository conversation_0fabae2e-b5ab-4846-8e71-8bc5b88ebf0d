{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\PycharmProjects\\\\CMS\\\\webapp\\\\frontend\\\\src\\\\pages\\\\UserPage.js\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect } from 'react';\nimport { Box, Typography, Paper, Button, Grid, Card, CardContent, CardActions, Dialog, DialogActions, DialogContent, DialogContentText, DialogTitle, TextField, Snackbar, Alert } from '@mui/material';\nimport { Construction as ConstructionIcon, Delete as DeleteIcon, Add as AddIcon, ContentCopy as ContentCopyIcon } from '@mui/icons-material';\nimport { useAuth } from '../context/AuthContext';\nimport { useNavigate } from 'react-router-dom';\nimport cantieriService from '../services/cantieriService';\nimport './UserPage.css';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst UserPage = () => {\n  _s();\n  const {\n    user,\n    isImpersonating,\n    impersonatedUser\n  } = useAuth();\n  const navigate = useNavigate();\n  const [cantieri, setCantieri] = useState([]);\n  const [loading, setLoading] = useState(true);\n  const [error, setError] = useState(null);\n  const [openCreateDialog, setOpenCreateDialog] = useState(false);\n  const [openDeleteDialog, setOpenDeleteDialog] = useState(false);\n  const [selectedCantiere, setSelectedCantiere] = useState(null);\n  const [newCantiereData, setNewCantiereData] = useState({\n    nome: '',\n    descrizione: '',\n    password_cantiere: '',\n    conferma_password: ''\n  });\n  const [notification, setNotification] = useState({\n    open: false,\n    message: '',\n    severity: 'success'\n  });\n\n  // Carica i cantieri dell'utente corrente o dell'utente impersonato\n  useEffect(() => {\n    const fetchCantieri = async () => {\n      try {\n        setLoading(true);\n        let data;\n\n        // Se l'amministratore sta impersonando un utente, carica i cantieri di quell'utente\n        if ((user === null || user === void 0 ? void 0 : user.role) === 'owner' && isImpersonating && impersonatedUser) {\n          // Carica i cantieri dell'utente impersonato\n          data = await cantieriService.getUserCantieri(impersonatedUser.id);\n        } else {\n          // Altrimenti carica i cantieri dell'utente corrente\n          data = await cantieriService.getMyCantieri();\n        }\n        setCantieri(data);\n      } catch (err) {\n        console.error('Errore nel caricamento dei cantieri:', err);\n        setError('Impossibile caricare i cantieri. Riprova più tardi.');\n      } finally {\n        setLoading(false);\n      }\n    };\n    fetchCantieri();\n  }, [user, isImpersonating, impersonatedUser]);\n\n  // Gestisce l'apertura del dialog per creare un nuovo cantiere\n  const handleOpenCreateDialog = () => {\n    setNewCantiereData({\n      nome: '',\n      descrizione: '',\n      password_cantiere: '',\n      conferma_password: ''\n    });\n    setOpenCreateDialog(true);\n  };\n\n  // Gestisce la chiusura del dialog per creare un nuovo cantiere\n  const handleCloseCreateDialog = () => {\n    setOpenCreateDialog(false);\n  };\n\n  // Gestisce l'apertura del dialog per eliminare un cantiere\n  const handleOpenDeleteDialog = cantiere => {\n    setSelectedCantiere(cantiere);\n    setOpenDeleteDialog(true);\n  };\n\n  // Gestisce la chiusura del dialog per eliminare un cantiere\n  const handleCloseDeleteDialog = () => {\n    setOpenDeleteDialog(false);\n    setSelectedCantiere(null);\n  };\n\n  // Gestisce la modifica dei campi del form per creare un nuovo cantiere\n  const handleInputChange = e => {\n    const {\n      name,\n      value\n    } = e.target;\n    setNewCantiereData({\n      ...newCantiereData,\n      [name]: value\n    });\n  };\n\n  // Gestisce la creazione di un nuovo cantiere\n  const handleCreateCantiere = async () => {\n    // Verifica che i campi obbligatori siano compilati\n    if (!newCantiereData.nome || !newCantiereData.password_cantiere) {\n      setNotification({\n        open: true,\n        message: 'Il nome e la password sono obbligatori',\n        severity: 'error'\n      });\n      return;\n    }\n\n    // Verifica che le password coincidano\n    if (newCantiereData.password_cantiere !== newCantiereData.conferma_password) {\n      setNotification({\n        open: true,\n        message: 'Le password non coincidono',\n        severity: 'error'\n      });\n      return;\n    }\n    try {\n      const createdCantiere = await cantieriService.createCantiere({\n        nome: newCantiereData.nome,\n        descrizione: newCantiereData.descrizione,\n        password_cantiere: newCantiereData.password_cantiere\n      });\n\n      // Aggiorna la lista dei cantieri\n      setCantieri([...cantieri, createdCantiere]);\n\n      // Chiudi il dialog\n      handleCloseCreateDialog();\n\n      // Mostra una notifica di successo con la password\n      setNotification({\n        open: true,\n        message: `Cantiere ${createdCantiere.nome} creato con successo!\\nCodice univoco: ${createdCantiere.codice_univoco}\\nPassword: ${newCantiereData.password_cantiere}\\n\\nSalva queste informazioni in un luogo sicuro!`,\n        severity: 'success'\n      });\n    } catch (err) {\n      console.error('Errore nella creazione del cantiere:', err);\n      setNotification({\n        open: true,\n        message: 'Errore nella creazione del cantiere',\n        severity: 'error'\n      });\n    }\n  };\n\n  // Gestisce l'eliminazione di un cantiere\n  const handleDeleteCantiere = async () => {\n    if (!selectedCantiere) return;\n    try {\n      await cantieriService.deleteCantiere(selectedCantiere.id_cantiere);\n\n      // Aggiorna la lista dei cantieri\n      setCantieri(cantieri.filter(c => c.id_cantiere !== selectedCantiere.id_cantiere));\n\n      // Chiudi il dialog\n      handleCloseDeleteDialog();\n\n      // Mostra una notifica di successo\n      setNotification({\n        open: true,\n        message: `Cantiere ${selectedCantiere.nome} eliminato con successo!`,\n        severity: 'success'\n      });\n    } catch (err) {\n      console.error('Errore nell\\'eliminazione del cantiere:', err);\n      setNotification({\n        open: true,\n        message: 'Errore nell\\'eliminazione del cantiere',\n        severity: 'error'\n      });\n    }\n  };\n\n  // Gestisce la selezione di un cantiere per aprire direttamente la pagina di gestione cavi\n  const handleSelectCantiere = cantiere => {\n    console.log('Selezionato cantiere:', cantiere);\n\n    // Salva l'ID e il nome del cantiere nel localStorage per l'uso nelle pagine di gestione cavi\n    localStorage.setItem('selectedCantiereId', cantiere.id_cantiere.toString());\n    localStorage.setItem('selectedCantiereName', cantiere.nome);\n\n    // Naviga direttamente alla pagina di visualizzazione cavi\n    navigate('/dashboard/cavi/visualizza');\n  };\n\n  // Gestisce la chiusura della notifica\n  const handleCloseNotification = () => {\n    setNotification({\n      ...notification,\n      open: false\n    });\n  };\n  return /*#__PURE__*/_jsxDEV(Box, {\n    className: \"cantieri-page\",\n    children: [/*#__PURE__*/_jsxDEV(Typography, {\n      variant: \"h4\",\n      gutterBottom: true,\n      children: isImpersonating && impersonatedUser ? `Cantieri di ${impersonatedUser.username}` : \"I Miei Cantieri\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 219,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Box, {\n      sx: {\n        mb: 3,\n        display: 'flex',\n        justifyContent: 'space-between',\n        alignItems: 'center'\n      },\n      children: [/*#__PURE__*/_jsxDEV(Typography, {\n        variant: \"body1\",\n        children: isImpersonating && impersonatedUser ? `Visualizza e gestisci i cantieri di ${impersonatedUser.username}` : \"Visualizza e gestisci i tuoi cantieri\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 224,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Button, {\n        variant: \"contained\",\n        className: \"primary-button\",\n        startIcon: /*#__PURE__*/_jsxDEV(AddIcon, {}, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 232,\n          columnNumber: 22\n        }, this),\n        onClick: handleOpenCreateDialog,\n        children: \"Nuovo Cantiere\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 229,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 223,\n      columnNumber: 7\n    }, this), loading ? /*#__PURE__*/_jsxDEV(Typography, {\n      children: \"Caricamento cantieri...\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 240,\n      columnNumber: 9\n    }, this) : error ? /*#__PURE__*/_jsxDEV(Alert, {\n      severity: \"error\",\n      children: error\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 242,\n      columnNumber: 9\n    }, this) : cantieri.length === 0 ? /*#__PURE__*/_jsxDEV(Paper, {\n      sx: {\n        p: 3,\n        textAlign: 'center'\n      },\n      children: [/*#__PURE__*/_jsxDEV(Typography, {\n        variant: \"h6\",\n        children: \"Nessun cantiere trovato\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 245,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(Typography, {\n        variant: \"body2\",\n        color: \"text.secondary\",\n        children: \"Crea un nuovo cantiere per iniziare\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 246,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(Button, {\n        variant: \"contained\",\n        className: \"primary-button\",\n        startIcon: /*#__PURE__*/_jsxDEV(AddIcon, {}, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 252,\n          columnNumber: 24\n        }, this),\n        onClick: handleOpenCreateDialog,\n        sx: {\n          mt: 2\n        },\n        children: \"Nuovo Cantiere\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 249,\n        columnNumber: 11\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 244,\n      columnNumber: 9\n    }, this) : /*#__PURE__*/_jsxDEV(Grid, {\n      container: true,\n      spacing: 3,\n      children: cantieri.map(cantiere => /*#__PURE__*/_jsxDEV(Grid, {\n        item: true,\n        xs: 12,\n        sm: 6,\n        md: 4,\n        children: /*#__PURE__*/_jsxDEV(Card, {\n          children: [/*#__PURE__*/_jsxDEV(CardContent, {\n            children: [/*#__PURE__*/_jsxDEV(Box, {\n              className: \"cantiere-header\",\n              children: [/*#__PURE__*/_jsxDEV(ConstructionIcon, {}, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 266,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                variant: \"h6\",\n                component: \"div\",\n                children: cantiere.nome\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 267,\n                columnNumber: 21\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 265,\n              columnNumber: 19\n            }, this), /*#__PURE__*/_jsxDEV(Typography, {\n              variant: \"body2\",\n              color: \"text.secondary\",\n              sx: {\n                mb: 1\n              },\n              children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n                children: \"Descrizione:\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 272,\n                columnNumber: 21\n              }, this), \" \", cantiere.descrizione || 'N/A']\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 271,\n              columnNumber: 19\n            }, this), /*#__PURE__*/_jsxDEV(Typography, {\n              variant: \"body2\",\n              color: \"text.secondary\",\n              sx: {\n                mb: 1\n              },\n              children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n                children: \"Password:\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 275,\n                columnNumber: 21\n              }, this), \" ********\"]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 274,\n              columnNumber: 19\n            }, this), /*#__PURE__*/_jsxDEV(Box, {\n              sx: {\n                display: 'flex',\n                alignItems: 'center',\n                mb: 1\n              },\n              children: [/*#__PURE__*/_jsxDEV(Typography, {\n                variant: \"body2\",\n                color: \"text.secondary\",\n                sx: {\n                  mr: 1\n                },\n                children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n                  children: \"Codice Univoco:\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 279,\n                  columnNumber: 23\n                }, this), \" \", cantiere.codice_univoco || 'N/A']\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 278,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(IconButton, {\n                size: \"small\",\n                onClick: () => {\n                  navigator.clipboard.writeText(cantiere.codice_univoco);\n                  setNotification({\n                    open: true,\n                    message: 'Codice univoco copiato negli appunti',\n                    severity: 'success'\n                  });\n                },\n                title: \"Copia codice univoco\",\n                children: /*#__PURE__*/_jsxDEV(ContentCopyIcon, {\n                  fontSize: \"small\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 293,\n                  columnNumber: 23\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 281,\n                columnNumber: 21\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 277,\n              columnNumber: 19\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 264,\n            columnNumber: 17\n          }, this), /*#__PURE__*/_jsxDEV(CardActions, {\n            children: [/*#__PURE__*/_jsxDEV(Button, {\n              variant: \"contained\",\n              className: \"primary-button\",\n              onClick: () => handleSelectCantiere(cantiere),\n              children: \"Gestione Cavi\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 298,\n              columnNumber: 19\n            }, this), /*#__PURE__*/_jsxDEV(Button, {\n              variant: \"contained\",\n              className: \"error-button\",\n              startIcon: /*#__PURE__*/_jsxDEV(DeleteIcon, {}, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 308,\n                columnNumber: 32\n              }, this),\n              onClick: () => handleOpenDeleteDialog(cantiere),\n              children: \"Elimina\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 305,\n              columnNumber: 19\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 297,\n            columnNumber: 17\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 263,\n          columnNumber: 15\n        }, this)\n      }, cantiere.id_cantiere, false, {\n        fileName: _jsxFileName,\n        lineNumber: 262,\n        columnNumber: 13\n      }, this))\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 260,\n      columnNumber: 9\n    }, this), /*#__PURE__*/_jsxDEV(Dialog, {\n      open: openCreateDialog,\n      onClose: handleCloseCreateDialog,\n      children: [/*#__PURE__*/_jsxDEV(DialogTitle, {\n        children: \"Crea Nuovo Cantiere\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 322,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(DialogContent, {\n        children: [/*#__PURE__*/_jsxDEV(DialogContentText, {\n          children: \"Inserisci i dati per creare un nuovo cantiere.\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 324,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(TextField, {\n          autoFocus: true,\n          margin: \"dense\",\n          name: \"nome\",\n          label: \"Nome Cantiere\",\n          type: \"text\",\n          fullWidth: true,\n          variant: \"outlined\",\n          value: newCantiereData.nome,\n          onChange: handleInputChange,\n          required: true\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 327,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(TextField, {\n          margin: \"dense\",\n          name: \"descrizione\",\n          label: \"Descrizione\",\n          type: \"text\",\n          fullWidth: true,\n          variant: \"outlined\",\n          value: newCantiereData.descrizione,\n          onChange: handleInputChange\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 339,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(TextField, {\n          margin: \"dense\",\n          name: \"password_cantiere\",\n          label: \"Password\",\n          type: \"password\",\n          fullWidth: true,\n          variant: \"outlined\",\n          value: newCantiereData.password_cantiere,\n          onChange: handleInputChange,\n          required: true\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 349,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(TextField, {\n          margin: \"dense\",\n          name: \"conferma_password\",\n          label: \"Conferma Password\",\n          type: \"password\",\n          fullWidth: true,\n          variant: \"outlined\",\n          value: newCantiereData.conferma_password,\n          onChange: handleInputChange,\n          required: true\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 360,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 323,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(DialogActions, {\n        children: [/*#__PURE__*/_jsxDEV(Button, {\n          variant: \"contained\",\n          onClick: handleCloseCreateDialog,\n          children: \"Annulla\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 374,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Button, {\n          onClick: handleCreateCantiere,\n          variant: \"contained\",\n          className: \"primary-button\",\n          children: \"Crea\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 375,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 373,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 321,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Dialog, {\n      open: openDeleteDialog,\n      onClose: handleCloseDeleteDialog,\n      children: [/*#__PURE__*/_jsxDEV(DialogTitle, {\n        children: \"Elimina Cantiere\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 383,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(DialogContent, {\n        children: /*#__PURE__*/_jsxDEV(DialogContentText, {\n          children: [\"ATTENZIONE: Sei sicuro di voler eliminare il cantiere \\\"\", selectedCantiere === null || selectedCantiere === void 0 ? void 0 : selectedCantiere.nome, \"\\\" e tutti i suoi dati? Questa operazione non pu\\xF2 essere annullata.\"]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 385,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 384,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(DialogActions, {\n        children: [/*#__PURE__*/_jsxDEV(Button, {\n          variant: \"contained\",\n          onClick: handleCloseDeleteDialog,\n          children: \"Annulla\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 391,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Button, {\n          onClick: handleDeleteCantiere,\n          variant: \"contained\",\n          className: \"error-button\",\n          children: \"Elimina\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 392,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 390,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 382,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Snackbar, {\n      open: notification.open,\n      autoHideDuration: 6000,\n      onClose: handleCloseNotification,\n      anchorOrigin: {\n        vertical: 'bottom',\n        horizontal: 'center'\n      },\n      children: /*#__PURE__*/_jsxDEV(Alert, {\n        onClose: handleCloseNotification,\n        severity: notification.severity,\n        sx: {\n          width: '100%'\n        },\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          style: {\n            whiteSpace: 'pre-line'\n          },\n          children: notification.message\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 406,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 405,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 399,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 218,\n    columnNumber: 5\n  }, this);\n};\n_s(UserPage, \"JOd2o/flmlMz/xmKD7Cz7PkBqRE=\", false, function () {\n  return [useAuth, useNavigate];\n});\n_c = UserPage;\nexport default UserPage;\nvar _c;\n$RefreshReg$(_c, \"UserPage\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "Box", "Typography", "Paper", "<PERSON><PERSON>", "Grid", "Card", "<PERSON><PERSON><PERSON><PERSON>", "CardActions", "Dialog", "DialogActions", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "DialogContentText", "DialogTitle", "TextField", "Snackbar", "<PERSON><PERSON>", "Construction", "ConstructionIcon", "Delete", "DeleteIcon", "Add", "AddIcon", "ContentCopy", "ContentCopyIcon", "useAuth", "useNavigate", "cantieriService", "jsxDEV", "_jsxDEV", "UserPage", "_s", "user", "isImpersonating", "impersonated<PERSON><PERSON>", "navigate", "cantieri", "set<PERSON><PERSON><PERSON>", "loading", "setLoading", "error", "setError", "openCreateDialog", "setOpenCreateDialog", "openDeleteDialog", "setOpenDeleteDialog", "selected<PERSON><PERSON><PERSON>", "setSelectedCantiere", "newCantiereData", "setNewCantiereData", "nome", "descrizione", "password_cantiere", "conferma_password", "notification", "setNotification", "open", "message", "severity", "fetchCantieri", "data", "role", "getUserCantieri", "id", "getMyCantieri", "err", "console", "handleOpenCreateDialog", "handleCloseCreateDialog", "handleOpenDeleteDialog", "cantiere", "handleCloseDeleteDialog", "handleInputChange", "e", "name", "value", "target", "handleCreateCantiere", "createdCantiere", "createCantiere", "codice_univoco", "handleDeleteCantiere", "deleteCantiere", "id_cantiere", "filter", "c", "handleSelectCantiere", "log", "localStorage", "setItem", "toString", "handleCloseNotification", "className", "children", "variant", "gutterBottom", "username", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "sx", "mb", "display", "justifyContent", "alignItems", "startIcon", "onClick", "length", "p", "textAlign", "color", "mt", "container", "spacing", "map", "item", "xs", "sm", "md", "component", "mr", "IconButton", "size", "navigator", "clipboard", "writeText", "title", "fontSize", "onClose", "autoFocus", "margin", "label", "type", "fullWidth", "onChange", "required", "autoHideDuration", "anchor<PERSON><PERSON><PERSON>", "vertical", "horizontal", "width", "style", "whiteSpace", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/PycharmProjects/CMS/webapp/frontend/src/pages/UserPage.js"], "sourcesContent": ["import React, { useState, useEffect } from 'react';\nimport {\n  Box,\n  Typography,\n  Paper,\n  Button,\n  Grid,\n  Card,\n  CardContent,\n  CardActions,\n  Dialog,\n  DialogActions,\n  DialogContent,\n  DialogContentText,\n  DialogTitle,\n  TextField,\n  Snackbar,\n  Alert\n} from '@mui/material';\nimport {\n  Construction as ConstructionIcon,\n  Delete as DeleteIcon,\n  Add as AddIcon,\n  ContentCopy as ContentCopyIcon\n} from '@mui/icons-material';\nimport { useAuth } from '../context/AuthContext';\nimport { useNavigate } from 'react-router-dom';\nimport cantieriService from '../services/cantieriService';\nimport './UserPage.css';\n\nconst UserPage = () => {\n  const { user, isImpersonating, impersonatedUser } = useAuth();\n  const navigate = useNavigate();\n  const [cantieri, setCantieri] = useState([]);\n  const [loading, setLoading] = useState(true);\n  const [error, setError] = useState(null);\n  const [openCreateDialog, setOpenCreateDialog] = useState(false);\n  const [openDeleteDialog, setOpenDeleteDialog] = useState(false);\n  const [selectedCantiere, setSelectedCantiere] = useState(null);\n  const [newCantiereData, setNewCantiereData] = useState({\n    nome: '',\n    descrizione: '',\n    password_cantiere: '',\n    conferma_password: ''\n  });\n  const [notification, setNotification] = useState({\n    open: false,\n    message: '',\n    severity: 'success'\n  });\n\n  // Carica i cantieri dell'utente corrente o dell'utente impersonato\n  useEffect(() => {\n    const fetchCantieri = async () => {\n      try {\n        setLoading(true);\n        let data;\n\n        // Se l'amministratore sta impersonando un utente, carica i cantieri di quell'utente\n        if (user?.role === 'owner' && isImpersonating && impersonatedUser) {\n          // Carica i cantieri dell'utente impersonato\n          data = await cantieriService.getUserCantieri(impersonatedUser.id);\n        } else {\n          // Altrimenti carica i cantieri dell'utente corrente\n          data = await cantieriService.getMyCantieri();\n        }\n\n        setCantieri(data);\n      } catch (err) {\n        console.error('Errore nel caricamento dei cantieri:', err);\n        setError('Impossibile caricare i cantieri. Riprova più tardi.');\n      } finally {\n        setLoading(false);\n      }\n    };\n\n    fetchCantieri();\n  }, [user, isImpersonating, impersonatedUser]);\n\n  // Gestisce l'apertura del dialog per creare un nuovo cantiere\n  const handleOpenCreateDialog = () => {\n    setNewCantiereData({\n      nome: '',\n      descrizione: '',\n      password_cantiere: '',\n      conferma_password: ''\n    });\n    setOpenCreateDialog(true);\n  };\n\n  // Gestisce la chiusura del dialog per creare un nuovo cantiere\n  const handleCloseCreateDialog = () => {\n    setOpenCreateDialog(false);\n  };\n\n  // Gestisce l'apertura del dialog per eliminare un cantiere\n  const handleOpenDeleteDialog = (cantiere) => {\n    setSelectedCantiere(cantiere);\n    setOpenDeleteDialog(true);\n  };\n\n  // Gestisce la chiusura del dialog per eliminare un cantiere\n  const handleCloseDeleteDialog = () => {\n    setOpenDeleteDialog(false);\n    setSelectedCantiere(null);\n  };\n\n  // Gestisce la modifica dei campi del form per creare un nuovo cantiere\n  const handleInputChange = (e) => {\n    const { name, value } = e.target;\n    setNewCantiereData({\n      ...newCantiereData,\n      [name]: value\n    });\n  };\n\n  // Gestisce la creazione di un nuovo cantiere\n  const handleCreateCantiere = async () => {\n    // Verifica che i campi obbligatori siano compilati\n    if (!newCantiereData.nome || !newCantiereData.password_cantiere) {\n      setNotification({\n        open: true,\n        message: 'Il nome e la password sono obbligatori',\n        severity: 'error'\n      });\n      return;\n    }\n\n    // Verifica che le password coincidano\n    if (newCantiereData.password_cantiere !== newCantiereData.conferma_password) {\n      setNotification({\n        open: true,\n        message: 'Le password non coincidono',\n        severity: 'error'\n      });\n      return;\n    }\n\n    try {\n      const createdCantiere = await cantieriService.createCantiere({\n        nome: newCantiereData.nome,\n        descrizione: newCantiereData.descrizione,\n        password_cantiere: newCantiereData.password_cantiere\n      });\n\n      // Aggiorna la lista dei cantieri\n      setCantieri([...cantieri, createdCantiere]);\n\n      // Chiudi il dialog\n      handleCloseCreateDialog();\n\n      // Mostra una notifica di successo con la password\n      setNotification({\n        open: true,\n        message: `Cantiere ${createdCantiere.nome} creato con successo!\\nCodice univoco: ${createdCantiere.codice_univoco}\\nPassword: ${newCantiereData.password_cantiere}\\n\\nSalva queste informazioni in un luogo sicuro!`,\n        severity: 'success'\n      });\n    } catch (err) {\n      console.error('Errore nella creazione del cantiere:', err);\n      setNotification({\n        open: true,\n        message: 'Errore nella creazione del cantiere',\n        severity: 'error'\n      });\n    }\n  };\n\n  // Gestisce l'eliminazione di un cantiere\n  const handleDeleteCantiere = async () => {\n    if (!selectedCantiere) return;\n\n    try {\n      await cantieriService.deleteCantiere(selectedCantiere.id_cantiere);\n\n      // Aggiorna la lista dei cantieri\n      setCantieri(cantieri.filter(c => c.id_cantiere !== selectedCantiere.id_cantiere));\n\n      // Chiudi il dialog\n      handleCloseDeleteDialog();\n\n      // Mostra una notifica di successo\n      setNotification({\n        open: true,\n        message: `Cantiere ${selectedCantiere.nome} eliminato con successo!`,\n        severity: 'success'\n      });\n    } catch (err) {\n      console.error('Errore nell\\'eliminazione del cantiere:', err);\n      setNotification({\n        open: true,\n        message: 'Errore nell\\'eliminazione del cantiere',\n        severity: 'error'\n      });\n    }\n  };\n\n  // Gestisce la selezione di un cantiere per aprire direttamente la pagina di gestione cavi\n  const handleSelectCantiere = (cantiere) => {\n    console.log('Selezionato cantiere:', cantiere);\n\n    // Salva l'ID e il nome del cantiere nel localStorage per l'uso nelle pagine di gestione cavi\n    localStorage.setItem('selectedCantiereId', cantiere.id_cantiere.toString());\n    localStorage.setItem('selectedCantiereName', cantiere.nome);\n\n    // Naviga direttamente alla pagina di visualizzazione cavi\n    navigate('/dashboard/cavi/visualizza');\n  };\n\n  // Gestisce la chiusura della notifica\n  const handleCloseNotification = () => {\n    setNotification({\n      ...notification,\n      open: false\n    });\n  };\n\n  return (\n    <Box className=\"cantieri-page\">\n      <Typography variant=\"h4\" gutterBottom>\n        {isImpersonating && impersonatedUser ? `Cantieri di ${impersonatedUser.username}` : \"I Miei Cantieri\"}\n      </Typography>\n\n      <Box sx={{ mb: 3, display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>\n        <Typography variant=\"body1\">\n          {isImpersonating && impersonatedUser\n            ? `Visualizza e gestisci i cantieri di ${impersonatedUser.username}`\n            : \"Visualizza e gestisci i tuoi cantieri\"}\n        </Typography>\n        <Button\n          variant=\"contained\"\n          className=\"primary-button\"\n          startIcon={<AddIcon />}\n          onClick={handleOpenCreateDialog}\n        >\n          Nuovo Cantiere\n        </Button>\n      </Box>\n\n      {loading ? (\n        <Typography>Caricamento cantieri...</Typography>\n      ) : error ? (\n        <Alert severity=\"error\">{error}</Alert>\n      ) : cantieri.length === 0 ? (\n        <Paper sx={{ p: 3, textAlign: 'center' }}>\n          <Typography variant=\"h6\">Nessun cantiere trovato</Typography>\n          <Typography variant=\"body2\" color=\"text.secondary\">\n            Crea un nuovo cantiere per iniziare\n          </Typography>\n          <Button\n            variant=\"contained\"\n            className=\"primary-button\"\n            startIcon={<AddIcon />}\n            onClick={handleOpenCreateDialog}\n            sx={{ mt: 2 }}\n          >\n            Nuovo Cantiere\n          </Button>\n        </Paper>\n      ) : (\n        <Grid container spacing={3}>\n          {cantieri.map((cantiere) => (\n            <Grid item xs={12} sm={6} md={4} key={cantiere.id_cantiere}>\n              <Card>\n                <CardContent>\n                  <Box className=\"cantiere-header\">\n                    <ConstructionIcon />\n                    <Typography variant=\"h6\" component=\"div\">\n                      {cantiere.nome}\n                    </Typography>\n                  </Box>\n                  <Typography variant=\"body2\" color=\"text.secondary\" sx={{ mb: 1 }}>\n                    <strong>Descrizione:</strong> {cantiere.descrizione || 'N/A'}\n                  </Typography>\n                  <Typography variant=\"body2\" color=\"text.secondary\" sx={{ mb: 1 }}>\n                    <strong>Password:</strong> ********\n                  </Typography>\n                  <Box sx={{ display: 'flex', alignItems: 'center', mb: 1 }}>\n                    <Typography variant=\"body2\" color=\"text.secondary\" sx={{ mr: 1 }}>\n                      <strong>Codice Univoco:</strong> {cantiere.codice_univoco || 'N/A'}\n                    </Typography>\n                    <IconButton\n                      size=\"small\"\n                      onClick={() => {\n                        navigator.clipboard.writeText(cantiere.codice_univoco);\n                        setNotification({\n                          open: true,\n                          message: 'Codice univoco copiato negli appunti',\n                          severity: 'success'\n                        });\n                      }}\n                      title=\"Copia codice univoco\"\n                    >\n                      <ContentCopyIcon fontSize=\"small\" />\n                    </IconButton>\n                  </Box>\n                </CardContent>\n                <CardActions>\n                  <Button\n                    variant=\"contained\"\n                    className=\"primary-button\"\n                    onClick={() => handleSelectCantiere(cantiere)}\n                  >\n                    Gestione Cavi\n                  </Button>\n                  <Button\n                    variant=\"contained\"\n                    className=\"error-button\"\n                    startIcon={<DeleteIcon />}\n                    onClick={() => handleOpenDeleteDialog(cantiere)}\n                  >\n                    Elimina\n                  </Button>\n                </CardActions>\n              </Card>\n            </Grid>\n          ))}\n        </Grid>\n      )}\n\n      {/* Dialog per creare un nuovo cantiere */}\n      <Dialog open={openCreateDialog} onClose={handleCloseCreateDialog}>\n        <DialogTitle>Crea Nuovo Cantiere</DialogTitle>\n        <DialogContent>\n          <DialogContentText>\n            Inserisci i dati per creare un nuovo cantiere.\n          </DialogContentText>\n          <TextField\n            autoFocus\n            margin=\"dense\"\n            name=\"nome\"\n            label=\"Nome Cantiere\"\n            type=\"text\"\n            fullWidth\n            variant=\"outlined\"\n            value={newCantiereData.nome}\n            onChange={handleInputChange}\n            required\n          />\n          <TextField\n            margin=\"dense\"\n            name=\"descrizione\"\n            label=\"Descrizione\"\n            type=\"text\"\n            fullWidth\n            variant=\"outlined\"\n            value={newCantiereData.descrizione}\n            onChange={handleInputChange}\n          />\n          <TextField\n            margin=\"dense\"\n            name=\"password_cantiere\"\n            label=\"Password\"\n            type=\"password\"\n            fullWidth\n            variant=\"outlined\"\n            value={newCantiereData.password_cantiere}\n            onChange={handleInputChange}\n            required\n          />\n          <TextField\n            margin=\"dense\"\n            name=\"conferma_password\"\n            label=\"Conferma Password\"\n            type=\"password\"\n            fullWidth\n            variant=\"outlined\"\n            value={newCantiereData.conferma_password}\n            onChange={handleInputChange}\n            required\n          />\n\n        </DialogContent>\n        <DialogActions>\n          <Button variant=\"contained\" onClick={handleCloseCreateDialog}>Annulla</Button>\n          <Button onClick={handleCreateCantiere} variant=\"contained\" className=\"primary-button\">\n            Crea\n          </Button>\n        </DialogActions>\n      </Dialog>\n\n      {/* Dialog per eliminare un cantiere */}\n      <Dialog open={openDeleteDialog} onClose={handleCloseDeleteDialog}>\n        <DialogTitle>Elimina Cantiere</DialogTitle>\n        <DialogContent>\n          <DialogContentText>\n            ATTENZIONE: Sei sicuro di voler eliminare il cantiere \"{selectedCantiere?.nome}\" e tutti i suoi dati?\n            Questa operazione non può essere annullata.\n          </DialogContentText>\n        </DialogContent>\n        <DialogActions>\n          <Button variant=\"contained\" onClick={handleCloseDeleteDialog}>Annulla</Button>\n          <Button onClick={handleDeleteCantiere} variant=\"contained\" className=\"error-button\">\n            Elimina\n          </Button>\n        </DialogActions>\n      </Dialog>\n\n      {/* Notifica */}\n      <Snackbar\n        open={notification.open}\n        autoHideDuration={6000}\n        onClose={handleCloseNotification}\n        anchorOrigin={{ vertical: 'bottom', horizontal: 'center' }}\n      >\n        <Alert onClose={handleCloseNotification} severity={notification.severity} sx={{ width: '100%' }}>\n          <div style={{ whiteSpace: 'pre-line' }}>{notification.message}</div>\n        </Alert>\n      </Snackbar>\n    </Box>\n  );\n};\n\nexport default UserPage;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAClD,SACEC,GAAG,EACHC,UAAU,EACVC,KAAK,EACLC,MAAM,EACNC,IAAI,EACJC,IAAI,EACJC,WAAW,EACXC,WAAW,EACXC,MAAM,EACNC,aAAa,EACbC,aAAa,EACbC,iBAAiB,EACjBC,WAAW,EACXC,SAAS,EACTC,QAAQ,EACRC,KAAK,QACA,eAAe;AACtB,SACEC,YAAY,IAAIC,gBAAgB,EAChCC,MAAM,IAAIC,UAAU,EACpBC,GAAG,IAAIC,OAAO,EACdC,WAAW,IAAIC,eAAe,QACzB,qBAAqB;AAC5B,SAASC,OAAO,QAAQ,wBAAwB;AAChD,SAASC,WAAW,QAAQ,kBAAkB;AAC9C,OAAOC,eAAe,MAAM,6BAA6B;AACzD,OAAO,gBAAgB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAExB,MAAMC,QAAQ,GAAGA,CAAA,KAAM;EAAAC,EAAA;EACrB,MAAM;IAAEC,IAAI;IAAEC,eAAe;IAAEC;EAAiB,CAAC,GAAGT,OAAO,CAAC,CAAC;EAC7D,MAAMU,QAAQ,GAAGT,WAAW,CAAC,CAAC;EAC9B,MAAM,CAACU,QAAQ,EAAEC,WAAW,CAAC,GAAGtC,QAAQ,CAAC,EAAE,CAAC;EAC5C,MAAM,CAACuC,OAAO,EAAEC,UAAU,CAAC,GAAGxC,QAAQ,CAAC,IAAI,CAAC;EAC5C,MAAM,CAACyC,KAAK,EAAEC,QAAQ,CAAC,GAAG1C,QAAQ,CAAC,IAAI,CAAC;EACxC,MAAM,CAAC2C,gBAAgB,EAAEC,mBAAmB,CAAC,GAAG5C,QAAQ,CAAC,KAAK,CAAC;EAC/D,MAAM,CAAC6C,gBAAgB,EAAEC,mBAAmB,CAAC,GAAG9C,QAAQ,CAAC,KAAK,CAAC;EAC/D,MAAM,CAAC+C,gBAAgB,EAAEC,mBAAmB,CAAC,GAAGhD,QAAQ,CAAC,IAAI,CAAC;EAC9D,MAAM,CAACiD,eAAe,EAAEC,kBAAkB,CAAC,GAAGlD,QAAQ,CAAC;IACrDmD,IAAI,EAAE,EAAE;IACRC,WAAW,EAAE,EAAE;IACfC,iBAAiB,EAAE,EAAE;IACrBC,iBAAiB,EAAE;EACrB,CAAC,CAAC;EACF,MAAM,CAACC,YAAY,EAAEC,eAAe,CAAC,GAAGxD,QAAQ,CAAC;IAC/CyD,IAAI,EAAE,KAAK;IACXC,OAAO,EAAE,EAAE;IACXC,QAAQ,EAAE;EACZ,CAAC,CAAC;;EAEF;EACA1D,SAAS,CAAC,MAAM;IACd,MAAM2D,aAAa,GAAG,MAAAA,CAAA,KAAY;MAChC,IAAI;QACFpB,UAAU,CAAC,IAAI,CAAC;QAChB,IAAIqB,IAAI;;QAER;QACA,IAAI,CAAA5B,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAE6B,IAAI,MAAK,OAAO,IAAI5B,eAAe,IAAIC,gBAAgB,EAAE;UACjE;UACA0B,IAAI,GAAG,MAAMjC,eAAe,CAACmC,eAAe,CAAC5B,gBAAgB,CAAC6B,EAAE,CAAC;QACnE,CAAC,MAAM;UACL;UACAH,IAAI,GAAG,MAAMjC,eAAe,CAACqC,aAAa,CAAC,CAAC;QAC9C;QAEA3B,WAAW,CAACuB,IAAI,CAAC;MACnB,CAAC,CAAC,OAAOK,GAAG,EAAE;QACZC,OAAO,CAAC1B,KAAK,CAAC,sCAAsC,EAAEyB,GAAG,CAAC;QAC1DxB,QAAQ,CAAC,qDAAqD,CAAC;MACjE,CAAC,SAAS;QACRF,UAAU,CAAC,KAAK,CAAC;MACnB;IACF,CAAC;IAEDoB,aAAa,CAAC,CAAC;EACjB,CAAC,EAAE,CAAC3B,IAAI,EAAEC,eAAe,EAAEC,gBAAgB,CAAC,CAAC;;EAE7C;EACA,MAAMiC,sBAAsB,GAAGA,CAAA,KAAM;IACnClB,kBAAkB,CAAC;MACjBC,IAAI,EAAE,EAAE;MACRC,WAAW,EAAE,EAAE;MACfC,iBAAiB,EAAE,EAAE;MACrBC,iBAAiB,EAAE;IACrB,CAAC,CAAC;IACFV,mBAAmB,CAAC,IAAI,CAAC;EAC3B,CAAC;;EAED;EACA,MAAMyB,uBAAuB,GAAGA,CAAA,KAAM;IACpCzB,mBAAmB,CAAC,KAAK,CAAC;EAC5B,CAAC;;EAED;EACA,MAAM0B,sBAAsB,GAAIC,QAAQ,IAAK;IAC3CvB,mBAAmB,CAACuB,QAAQ,CAAC;IAC7BzB,mBAAmB,CAAC,IAAI,CAAC;EAC3B,CAAC;;EAED;EACA,MAAM0B,uBAAuB,GAAGA,CAAA,KAAM;IACpC1B,mBAAmB,CAAC,KAAK,CAAC;IAC1BE,mBAAmB,CAAC,IAAI,CAAC;EAC3B,CAAC;;EAED;EACA,MAAMyB,iBAAiB,GAAIC,CAAC,IAAK;IAC/B,MAAM;MAAEC,IAAI;MAAEC;IAAM,CAAC,GAAGF,CAAC,CAACG,MAAM;IAChC3B,kBAAkB,CAAC;MACjB,GAAGD,eAAe;MAClB,CAAC0B,IAAI,GAAGC;IACV,CAAC,CAAC;EACJ,CAAC;;EAED;EACA,MAAME,oBAAoB,GAAG,MAAAA,CAAA,KAAY;IACvC;IACA,IAAI,CAAC7B,eAAe,CAACE,IAAI,IAAI,CAACF,eAAe,CAACI,iBAAiB,EAAE;MAC/DG,eAAe,CAAC;QACdC,IAAI,EAAE,IAAI;QACVC,OAAO,EAAE,wCAAwC;QACjDC,QAAQ,EAAE;MACZ,CAAC,CAAC;MACF;IACF;;IAEA;IACA,IAAIV,eAAe,CAACI,iBAAiB,KAAKJ,eAAe,CAACK,iBAAiB,EAAE;MAC3EE,eAAe,CAAC;QACdC,IAAI,EAAE,IAAI;QACVC,OAAO,EAAE,4BAA4B;QACrCC,QAAQ,EAAE;MACZ,CAAC,CAAC;MACF;IACF;IAEA,IAAI;MACF,MAAMoB,eAAe,GAAG,MAAMnD,eAAe,CAACoD,cAAc,CAAC;QAC3D7B,IAAI,EAAEF,eAAe,CAACE,IAAI;QAC1BC,WAAW,EAAEH,eAAe,CAACG,WAAW;QACxCC,iBAAiB,EAAEJ,eAAe,CAACI;MACrC,CAAC,CAAC;;MAEF;MACAf,WAAW,CAAC,CAAC,GAAGD,QAAQ,EAAE0C,eAAe,CAAC,CAAC;;MAE3C;MACAV,uBAAuB,CAAC,CAAC;;MAEzB;MACAb,eAAe,CAAC;QACdC,IAAI,EAAE,IAAI;QACVC,OAAO,EAAE,YAAYqB,eAAe,CAAC5B,IAAI,0CAA0C4B,eAAe,CAACE,cAAc,eAAehC,eAAe,CAACI,iBAAiB,mDAAmD;QACpNM,QAAQ,EAAE;MACZ,CAAC,CAAC;IACJ,CAAC,CAAC,OAAOO,GAAG,EAAE;MACZC,OAAO,CAAC1B,KAAK,CAAC,sCAAsC,EAAEyB,GAAG,CAAC;MAC1DV,eAAe,CAAC;QACdC,IAAI,EAAE,IAAI;QACVC,OAAO,EAAE,qCAAqC;QAC9CC,QAAQ,EAAE;MACZ,CAAC,CAAC;IACJ;EACF,CAAC;;EAED;EACA,MAAMuB,oBAAoB,GAAG,MAAAA,CAAA,KAAY;IACvC,IAAI,CAACnC,gBAAgB,EAAE;IAEvB,IAAI;MACF,MAAMnB,eAAe,CAACuD,cAAc,CAACpC,gBAAgB,CAACqC,WAAW,CAAC;;MAElE;MACA9C,WAAW,CAACD,QAAQ,CAACgD,MAAM,CAACC,CAAC,IAAIA,CAAC,CAACF,WAAW,KAAKrC,gBAAgB,CAACqC,WAAW,CAAC,CAAC;;MAEjF;MACAZ,uBAAuB,CAAC,CAAC;;MAEzB;MACAhB,eAAe,CAAC;QACdC,IAAI,EAAE,IAAI;QACVC,OAAO,EAAE,YAAYX,gBAAgB,CAACI,IAAI,0BAA0B;QACpEQ,QAAQ,EAAE;MACZ,CAAC,CAAC;IACJ,CAAC,CAAC,OAAOO,GAAG,EAAE;MACZC,OAAO,CAAC1B,KAAK,CAAC,yCAAyC,EAAEyB,GAAG,CAAC;MAC7DV,eAAe,CAAC;QACdC,IAAI,EAAE,IAAI;QACVC,OAAO,EAAE,wCAAwC;QACjDC,QAAQ,EAAE;MACZ,CAAC,CAAC;IACJ;EACF,CAAC;;EAED;EACA,MAAM4B,oBAAoB,GAAIhB,QAAQ,IAAK;IACzCJ,OAAO,CAACqB,GAAG,CAAC,uBAAuB,EAAEjB,QAAQ,CAAC;;IAE9C;IACAkB,YAAY,CAACC,OAAO,CAAC,oBAAoB,EAAEnB,QAAQ,CAACa,WAAW,CAACO,QAAQ,CAAC,CAAC,CAAC;IAC3EF,YAAY,CAACC,OAAO,CAAC,sBAAsB,EAAEnB,QAAQ,CAACpB,IAAI,CAAC;;IAE3D;IACAf,QAAQ,CAAC,4BAA4B,CAAC;EACxC,CAAC;;EAED;EACA,MAAMwD,uBAAuB,GAAGA,CAAA,KAAM;IACpCpC,eAAe,CAAC;MACd,GAAGD,YAAY;MACfE,IAAI,EAAE;IACR,CAAC,CAAC;EACJ,CAAC;EAED,oBACE3B,OAAA,CAAC5B,GAAG;IAAC2F,SAAS,EAAC,eAAe;IAAAC,QAAA,gBAC5BhE,OAAA,CAAC3B,UAAU;MAAC4F,OAAO,EAAC,IAAI;MAACC,YAAY;MAAAF,QAAA,EAClC5D,eAAe,IAAIC,gBAAgB,GAAG,eAAeA,gBAAgB,CAAC8D,QAAQ,EAAE,GAAG;IAAiB;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAC3F,CAAC,eAEbvE,OAAA,CAAC5B,GAAG;MAACoG,EAAE,EAAE;QAAEC,EAAE,EAAE,CAAC;QAAEC,OAAO,EAAE,MAAM;QAAEC,cAAc,EAAE,eAAe;QAAEC,UAAU,EAAE;MAAS,CAAE;MAAAZ,QAAA,gBACzFhE,OAAA,CAAC3B,UAAU;QAAC4F,OAAO,EAAC,OAAO;QAAAD,QAAA,EACxB5D,eAAe,IAAIC,gBAAgB,GAChC,uCAAuCA,gBAAgB,CAAC8D,QAAQ,EAAE,GAClE;MAAuC;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACjC,CAAC,eACbvE,OAAA,CAACzB,MAAM;QACL0F,OAAO,EAAC,WAAW;QACnBF,SAAS,EAAC,gBAAgB;QAC1Bc,SAAS,eAAE7E,OAAA,CAACP,OAAO;UAAA2E,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAE;QACvBO,OAAO,EAAExC,sBAAuB;QAAA0B,QAAA,EACjC;MAED;QAAAI,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAQ,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACN,CAAC,EAEL9D,OAAO,gBACNT,OAAA,CAAC3B,UAAU;MAAA2F,QAAA,EAAC;IAAuB;MAAAI,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAY,CAAC,GAC9C5D,KAAK,gBACPX,OAAA,CAACb,KAAK;MAAC0C,QAAQ,EAAC,OAAO;MAAAmC,QAAA,EAAErD;IAAK;MAAAyD,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAQ,CAAC,GACrChE,QAAQ,CAACwE,MAAM,KAAK,CAAC,gBACvB/E,OAAA,CAAC1B,KAAK;MAACkG,EAAE,EAAE;QAAEQ,CAAC,EAAE,CAAC;QAAEC,SAAS,EAAE;MAAS,CAAE;MAAAjB,QAAA,gBACvChE,OAAA,CAAC3B,UAAU;QAAC4F,OAAO,EAAC,IAAI;QAAAD,QAAA,EAAC;MAAuB;QAAAI,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAY,CAAC,eAC7DvE,OAAA,CAAC3B,UAAU;QAAC4F,OAAO,EAAC,OAAO;QAACiB,KAAK,EAAC,gBAAgB;QAAAlB,QAAA,EAAC;MAEnD;QAAAI,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAY,CAAC,eACbvE,OAAA,CAACzB,MAAM;QACL0F,OAAO,EAAC,WAAW;QACnBF,SAAS,EAAC,gBAAgB;QAC1Bc,SAAS,eAAE7E,OAAA,CAACP,OAAO;UAAA2E,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAE;QACvBO,OAAO,EAAExC,sBAAuB;QAChCkC,EAAE,EAAE;UAAEW,EAAE,EAAE;QAAE,CAAE;QAAAnB,QAAA,EACf;MAED;QAAAI,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAQ,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACJ,CAAC,gBAERvE,OAAA,CAACxB,IAAI;MAAC4G,SAAS;MAACC,OAAO,EAAE,CAAE;MAAArB,QAAA,EACxBzD,QAAQ,CAAC+E,GAAG,CAAE7C,QAAQ,iBACrBzC,OAAA,CAACxB,IAAI;QAAC+G,IAAI;QAACC,EAAE,EAAE,EAAG;QAACC,EAAE,EAAE,CAAE;QAACC,EAAE,EAAE,CAAE;QAAA1B,QAAA,eAC9BhE,OAAA,CAACvB,IAAI;UAAAuF,QAAA,gBACHhE,OAAA,CAACtB,WAAW;YAAAsF,QAAA,gBACVhE,OAAA,CAAC5B,GAAG;cAAC2F,SAAS,EAAC,iBAAiB;cAAAC,QAAA,gBAC9BhE,OAAA,CAACX,gBAAgB;gBAAA+E,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,eACpBvE,OAAA,CAAC3B,UAAU;gBAAC4F,OAAO,EAAC,IAAI;gBAAC0B,SAAS,EAAC,KAAK;gBAAA3B,QAAA,EACrCvB,QAAQ,CAACpB;cAAI;gBAAA+C,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACJ,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACV,CAAC,eACNvE,OAAA,CAAC3B,UAAU;cAAC4F,OAAO,EAAC,OAAO;cAACiB,KAAK,EAAC,gBAAgB;cAACV,EAAE,EAAE;gBAAEC,EAAE,EAAE;cAAE,CAAE;cAAAT,QAAA,gBAC/DhE,OAAA;gBAAAgE,QAAA,EAAQ;cAAY;gBAAAI,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC,KAAC,EAAC9B,QAAQ,CAACnB,WAAW,IAAI,KAAK;YAAA;cAAA8C,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAClD,CAAC,eACbvE,OAAA,CAAC3B,UAAU;cAAC4F,OAAO,EAAC,OAAO;cAACiB,KAAK,EAAC,gBAAgB;cAACV,EAAE,EAAE;gBAAEC,EAAE,EAAE;cAAE,CAAE;cAAAT,QAAA,gBAC/DhE,OAAA;gBAAAgE,QAAA,EAAQ;cAAS;gBAAAI,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC,aAC5B;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAY,CAAC,eACbvE,OAAA,CAAC5B,GAAG;cAACoG,EAAE,EAAE;gBAAEE,OAAO,EAAE,MAAM;gBAAEE,UAAU,EAAE,QAAQ;gBAAEH,EAAE,EAAE;cAAE,CAAE;cAAAT,QAAA,gBACxDhE,OAAA,CAAC3B,UAAU;gBAAC4F,OAAO,EAAC,OAAO;gBAACiB,KAAK,EAAC,gBAAgB;gBAACV,EAAE,EAAE;kBAAEoB,EAAE,EAAE;gBAAE,CAAE;gBAAA5B,QAAA,gBAC/DhE,OAAA;kBAAAgE,QAAA,EAAQ;gBAAe;kBAAAI,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,KAAC,EAAC9B,QAAQ,CAACU,cAAc,IAAI,KAAK;cAAA;gBAAAiB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACxD,CAAC,eACbvE,OAAA,CAAC6F,UAAU;gBACTC,IAAI,EAAC,OAAO;gBACZhB,OAAO,EAAEA,CAAA,KAAM;kBACbiB,SAAS,CAACC,SAAS,CAACC,SAAS,CAACxD,QAAQ,CAACU,cAAc,CAAC;kBACtDzB,eAAe,CAAC;oBACdC,IAAI,EAAE,IAAI;oBACVC,OAAO,EAAE,sCAAsC;oBAC/CC,QAAQ,EAAE;kBACZ,CAAC,CAAC;gBACJ,CAAE;gBACFqE,KAAK,EAAC,sBAAsB;gBAAAlC,QAAA,eAE5BhE,OAAA,CAACL,eAAe;kBAACwG,QAAQ,EAAC;gBAAO;kBAAA/B,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC1B,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACV,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACK,CAAC,eACdvE,OAAA,CAACrB,WAAW;YAAAqF,QAAA,gBACVhE,OAAA,CAACzB,MAAM;cACL0F,OAAO,EAAC,WAAW;cACnBF,SAAS,EAAC,gBAAgB;cAC1Be,OAAO,EAAEA,CAAA,KAAMrB,oBAAoB,CAAChB,QAAQ,CAAE;cAAAuB,QAAA,EAC/C;YAED;cAAAI,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC,eACTvE,OAAA,CAACzB,MAAM;cACL0F,OAAO,EAAC,WAAW;cACnBF,SAAS,EAAC,cAAc;cACxBc,SAAS,eAAE7E,OAAA,CAACT,UAAU;gBAAA6E,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAE;cAC1BO,OAAO,EAAEA,CAAA,KAAMtC,sBAAsB,CAACC,QAAQ,CAAE;cAAAuB,QAAA,EACjD;YAED;cAAAI,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACE,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACV;MAAC,GApD6B9B,QAAQ,CAACa,WAAW;QAAAc,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAqDpD,CACP;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACE,CACP,eAGDvE,OAAA,CAACpB,MAAM;MAAC+C,IAAI,EAAEd,gBAAiB;MAACuF,OAAO,EAAE7D,uBAAwB;MAAAyB,QAAA,gBAC/DhE,OAAA,CAAChB,WAAW;QAAAgF,QAAA,EAAC;MAAmB;QAAAI,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAa,CAAC,eAC9CvE,OAAA,CAAClB,aAAa;QAAAkF,QAAA,gBACZhE,OAAA,CAACjB,iBAAiB;UAAAiF,QAAA,EAAC;QAEnB;UAAAI,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAmB,CAAC,eACpBvE,OAAA,CAACf,SAAS;UACRoH,SAAS;UACTC,MAAM,EAAC,OAAO;UACdzD,IAAI,EAAC,MAAM;UACX0D,KAAK,EAAC,eAAe;UACrBC,IAAI,EAAC,MAAM;UACXC,SAAS;UACTxC,OAAO,EAAC,UAAU;UAClBnB,KAAK,EAAE3B,eAAe,CAACE,IAAK;UAC5BqF,QAAQ,EAAE/D,iBAAkB;UAC5BgE,QAAQ;QAAA;UAAAvC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACT,CAAC,eACFvE,OAAA,CAACf,SAAS;UACRqH,MAAM,EAAC,OAAO;UACdzD,IAAI,EAAC,aAAa;UAClB0D,KAAK,EAAC,aAAa;UACnBC,IAAI,EAAC,MAAM;UACXC,SAAS;UACTxC,OAAO,EAAC,UAAU;UAClBnB,KAAK,EAAE3B,eAAe,CAACG,WAAY;UACnCoF,QAAQ,EAAE/D;QAAkB;UAAAyB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC7B,CAAC,eACFvE,OAAA,CAACf,SAAS;UACRqH,MAAM,EAAC,OAAO;UACdzD,IAAI,EAAC,mBAAmB;UACxB0D,KAAK,EAAC,UAAU;UAChBC,IAAI,EAAC,UAAU;UACfC,SAAS;UACTxC,OAAO,EAAC,UAAU;UAClBnB,KAAK,EAAE3B,eAAe,CAACI,iBAAkB;UACzCmF,QAAQ,EAAE/D,iBAAkB;UAC5BgE,QAAQ;QAAA;UAAAvC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACT,CAAC,eACFvE,OAAA,CAACf,SAAS;UACRqH,MAAM,EAAC,OAAO;UACdzD,IAAI,EAAC,mBAAmB;UACxB0D,KAAK,EAAC,mBAAmB;UACzBC,IAAI,EAAC,UAAU;UACfC,SAAS;UACTxC,OAAO,EAAC,UAAU;UAClBnB,KAAK,EAAE3B,eAAe,CAACK,iBAAkB;UACzCkF,QAAQ,EAAE/D,iBAAkB;UAC5BgE,QAAQ;QAAA;UAAAvC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACT,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAEW,CAAC,eAChBvE,OAAA,CAACnB,aAAa;QAAAmF,QAAA,gBACZhE,OAAA,CAACzB,MAAM;UAAC0F,OAAO,EAAC,WAAW;UAACa,OAAO,EAAEvC,uBAAwB;UAAAyB,QAAA,EAAC;QAAO;UAAAI,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC,eAC9EvE,OAAA,CAACzB,MAAM;UAACuG,OAAO,EAAE9B,oBAAqB;UAACiB,OAAO,EAAC,WAAW;UAACF,SAAS,EAAC,gBAAgB;UAAAC,QAAA,EAAC;QAEtF;UAAAI,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACI,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACV,CAAC,eAGTvE,OAAA,CAACpB,MAAM;MAAC+C,IAAI,EAAEZ,gBAAiB;MAACqF,OAAO,EAAE1D,uBAAwB;MAAAsB,QAAA,gBAC/DhE,OAAA,CAAChB,WAAW;QAAAgF,QAAA,EAAC;MAAgB;QAAAI,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAa,CAAC,eAC3CvE,OAAA,CAAClB,aAAa;QAAAkF,QAAA,eACZhE,OAAA,CAACjB,iBAAiB;UAAAiF,QAAA,GAAC,0DACsC,EAAC/C,gBAAgB,aAAhBA,gBAAgB,uBAAhBA,gBAAgB,CAAEI,IAAI,EAAC,wEAEjF;QAAA;UAAA+C,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAmB;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACP,CAAC,eAChBvE,OAAA,CAACnB,aAAa;QAAAmF,QAAA,gBACZhE,OAAA,CAACzB,MAAM;UAAC0F,OAAO,EAAC,WAAW;UAACa,OAAO,EAAEpC,uBAAwB;UAAAsB,QAAA,EAAC;QAAO;UAAAI,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC,eAC9EvE,OAAA,CAACzB,MAAM;UAACuG,OAAO,EAAE1B,oBAAqB;UAACa,OAAO,EAAC,WAAW;UAACF,SAAS,EAAC,cAAc;UAAAC,QAAA,EAAC;QAEpF;UAAAI,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACI,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACV,CAAC,eAGTvE,OAAA,CAACd,QAAQ;MACPyC,IAAI,EAAEF,YAAY,CAACE,IAAK;MACxBiF,gBAAgB,EAAE,IAAK;MACvBR,OAAO,EAAEtC,uBAAwB;MACjC+C,YAAY,EAAE;QAAEC,QAAQ,EAAE,QAAQ;QAAEC,UAAU,EAAE;MAAS,CAAE;MAAA/C,QAAA,eAE3DhE,OAAA,CAACb,KAAK;QAACiH,OAAO,EAAEtC,uBAAwB;QAACjC,QAAQ,EAAEJ,YAAY,CAACI,QAAS;QAAC2C,EAAE,EAAE;UAAEwC,KAAK,EAAE;QAAO,CAAE;QAAAhD,QAAA,eAC9FhE,OAAA;UAAKiH,KAAK,EAAE;YAAEC,UAAU,EAAE;UAAW,CAAE;UAAAlD,QAAA,EAAEvC,YAAY,CAACG;QAAO;UAAAwC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC/D;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACA,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACR,CAAC;AAEV,CAAC;AAACrE,EAAA,CA5XID,QAAQ;EAAA,QACwCL,OAAO,EAC1CC,WAAW;AAAA;AAAAsH,EAAA,GAFxBlH,QAAQ;AA8Xd,eAAeA,QAAQ;AAAC,IAAAkH,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}