{"ast": null, "code": "var _jsxFileName = \"C:\\\\CMS\\\\webapp\\\\frontend\\\\src\\\\components\\\\cavi\\\\ModificaBobinaForm.js\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect } from 'react';\nimport { Box, Paper, Typography, TextField, Button, Grid, FormControl, InputLabel, Select, MenuItem, Divider, Alert, CircularProgress, Dialog, DialogTitle, DialogContent, DialogActions, Table, TableBody, TableCell, TableContainer, TableHead, TableRow, Radio, RadioGroup, FormControlLabel, IconButton, InputAdornment, List, ListItem, ListItemText, ListItemButton, ListItemSecondaryAction, Chip } from '@mui/material';\nimport { Search as SearchIcon, Save as SaveIcon, Cancel as CancelIcon, Warning as WarningIcon, Info as InfoIcon, AddCircleOutline as AddCircleOutlineIcon } from '@mui/icons-material';\nimport IncompatibleReelDialog from './IncompatibleReelDialog';\nimport { useNavigate, useParams } from 'react-router-dom';\nimport caviService from '../../services/caviService';\nimport parcoCaviService from '../../services/parcoCaviService';\nimport CavoDetailsView from './CavoDetailsView';\nimport { CABLE_STATES, REEL_STATES, determineCableState, determineReelState, canModifyCable, isCableSpare, isCableInstalled, getCableStateColor, getReelStateColor } from '../../utils/stateUtils';\n\n/**\n * Componente per la modifica della bobina di un cavo già posato\n *\n * @param {Object} props - Proprietà del componente\n * @param {string} props.cantiereId - ID del cantiere\n * @param {Function} props.onSuccess - Funzione chiamata al successo dell'operazione\n * @param {Function} props.onError - Funzione chiamata in caso di errore\n */\nimport { jsxDEV as _jsxDEV, Fragment as _Fragment } from \"react/jsx-dev-runtime\";\nconst ModificaBobinaForm = ({\n  cantiereId,\n  onSuccess,\n  onError\n}) => {\n  _s();\n  const navigate = useNavigate();\n  const {\n    cavoId\n  } = useParams();\n\n  // Stati per la gestione del form\n  const [loading, setLoading] = useState(false);\n  const [caviLoading, setCaviLoading] = useState(false);\n  const [bobineLoading, setBobineLoading] = useState(false);\n  const [searchResults, setSearchResults] = useState([]);\n  const [showSearchResults, setShowSearchResults] = useState(false);\n\n  // Stati per i dati\n  const [cavi, setCavi] = useState([]);\n  const [bobine, setBobine] = useState([]);\n  const [compatibleBobine, setCompatibleBobine] = useState([]);\n  const [selectedCavo, setSelectedCavo] = useState(null);\n  const [cavoIdInput, setCavoIdInput] = useState('');\n  const [selectedOption, setSelectedOption] = useState('');\n  const [selectedBobinaId, setSelectedBobinaId] = useState('');\n  const [bobinaSearchText, setBobinaSearchText] = useState('');\n\n  // Stati per gestire il dialog di incompatibilità\n  const [showIncompatibleReelDialog, setShowIncompatibleReelDialog] = useState(false);\n  const [incompatibleReelData, setIncompatibleReelData] = useState({\n    cavo: null,\n    bobina: null\n  });\n\n  // Stati per i dialoghi\n  const [showConfirmDialog, setShowConfirmDialog] = useState(false);\n  const [confirmDialogMessage, setConfirmDialogMessage] = useState('');\n  const [confirmDialogAction, setConfirmDialogAction] = useState(null);\n  const [showCavoDetailsDialog, setShowCavoDetailsDialog] = useState(false);\n\n  // Carica i cavi all'avvio\n  useEffect(() => {\n    loadCavi();\n  }, [cantiereId]);\n\n  // Carica le bobine quando viene selezionato un cavo\n  useEffect(() => {\n    if (selectedCavo) {\n      loadBobine();\n    }\n  }, [selectedCavo]);\n\n  // Pre-seleziona il cavo se viene fornito l'ID del cavo nell'URL\n  useEffect(() => {\n    if (cavoId && cavi.length > 0 && !selectedCavo) {\n      const cavoFromUrl = cavi.find(cavo => cavo.id_cavo === cavoId);\n      if (cavoFromUrl) {\n        console.log('Pre-selezione cavo dall\\'URL:', cavoFromUrl);\n        setSelectedCavo(cavoFromUrl);\n        setCavoIdInput(cavoId);\n      } else {\n        console.warn(`Cavo con ID ${cavoId} non trovato nella lista dei cavi installati`);\n        onError(`Cavo ${cavoId} non trovato o non installato`);\n      }\n    }\n  }, [cavoId, cavi, selectedCavo]);\n\n  // Funzione per caricare i cavi\n  const loadCavi = async () => {\n    try {\n      setCaviLoading(true);\n      console.log(`Caricamento cavi per il cantiere ${cantiereId}...`);\n\n      // Carica solo i cavi installati (con metratura_reale > 0)\n      const caviData = await caviService.getCavi(cantiereId, null, 'Installato');\n      console.log(`Caricati ${caviData.length} cavi installati`);\n\n      // Filtra i cavi che hanno metratura_reale > 0\n      const caviInstallati = caviData.filter(cavo => parseFloat(cavo.metratura_reale) > 0 || cavo.stato_installazione === 'Installato');\n      setCavi(caviInstallati);\n      console.log(`Filtrati ${caviInstallati.length} cavi effettivamente installati`);\n    } catch (error) {\n      console.error('Errore durante il caricamento dei cavi:', error);\n      onError('Errore durante il caricamento dei cavi: ' + (error.detail || error.message || 'Errore sconosciuto'));\n    } finally {\n      setCaviLoading(false);\n    }\n  };\n\n  // Funzione per caricare le bobine\n  const loadBobine = async () => {\n    if (!selectedCavo) return;\n    try {\n      setBobineLoading(true);\n      console.log(`Caricamento bobine per il cantiere ${cantiereId}...`);\n\n      // Carica tutte le bobine disponibili\n      const bobineData = await parcoCaviService.getBobine(cantiereId);\n      console.log(`Caricati ${bobineData.length} bobine`);\n\n      // Filtra le bobine compatibili\n      const compatibleBobineData = bobineData.filter(bobina => bobina.tipologia === selectedCavo.tipologia && bobina.sezione === selectedCavo.sezione && (bobina.stato_bobina === 'Disponibile' || bobina.stato_bobina === 'In uso'));\n      setBobine(bobineData);\n      setCompatibleBobine(compatibleBobineData);\n      console.log(`Filtrate ${compatibleBobineData.length} bobine compatibili`);\n    } catch (error) {\n      console.error('Errore durante il caricamento delle bobine:', error);\n      onError('Errore durante il caricamento delle bobine: ' + (error.detail || error.message || 'Errore sconosciuto'));\n    } finally {\n      setBobineLoading(false);\n    }\n  };\n\n  // Gestisce la ricerca di un cavo per ID\n  const handleSearchCavoById = async () => {\n    if (!cavoIdInput.trim()) {\n      onError('Inserisci un ID cavo valido');\n      return;\n    }\n    try {\n      setLoading(true);\n      const cavo = await caviService.getCavoById(cantiereId, cavoIdInput.trim());\n\n      // Verifica che il cavo sia installato\n      if (parseFloat(cavo.metratura_reale) <= 0 && cavo.stato_installazione !== 'Installato') {\n        onError('Il cavo selezionato non risulta installato');\n        setLoading(false);\n        return;\n      }\n      setSelectedCavo(cavo);\n      setSelectedOption(''); // Reset dell'opzione selezionata\n      setSelectedBobinaId(''); // Reset della bobina selezionata\n      setShowSearchResults(false);\n    } catch (error) {\n      console.error('Errore durante la ricerca del cavo:', error);\n      onError('Errore durante la ricerca del cavo: ' + (error.detail || error.message || 'Errore sconosciuto'));\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  // Gestisce la selezione di un cavo dalla lista\n  const handleSelectCavo = cavo => {\n    setSelectedCavo(cavo);\n    setSelectedOption(''); // Reset dell'opzione selezionata\n    setSelectedBobinaId(''); // Reset della bobina selezionata\n    setShowSearchResults(false);\n  };\n\n  // Gestisce il cambio dell'opzione selezionata\n  const handleOptionChange = event => {\n    setSelectedOption(event.target.value);\n    setSelectedBobinaId(''); // Reset della bobina selezionata quando cambia l'opzione\n  };\n\n  // Gestisce la selezione di una bobina\n  const handleSelectBobina = bobinaId => {\n    console.log('Bobina selezionata:', bobinaId);\n\n    // Trova la bobina selezionata\n    const bobina = bobine.find(b => b.id_bobina === bobinaId);\n    if (bobina && selectedCavo) {\n      // Verifica compatibilità\n      const isCompatible = String(bobina.tipologia || '').trim() === String(selectedCavo.tipologia || '').trim() && String(bobina.sezione || '').trim() === String(selectedCavo.sezione || '').trim();\n      if (!isCompatible) {\n        console.log('Bobina incompatibile selezionata:', bobina);\n        console.log('Cavo corrente:', selectedCavo);\n\n        // Aggiorna automaticamente le caratteristiche del cavo per renderlo compatibile\n        handleUpdateCavoForCompatibility(selectedCavo, bobina);\n\n        // Imposta la bobina selezionata per mostrare i dettagli\n        setSelectedBobinaId(bobinaId);\n        return;\n      }\n    }\n\n    // Se compatibile o nessun cavo selezionato, procedi normalmente\n    setSelectedBobinaId(bobinaId);\n  };\n\n  // Gestisce il salvataggio delle modifiche\n  const handleSave = () => {\n    if (!selectedCavo) {\n      onError('Seleziona un cavo prima di procedere');\n      return;\n    }\n    if (!selectedOption) {\n      onError('Seleziona un\\'opzione prima di procedere');\n      return;\n    }\n\n    // Verifica che sia stata selezionata una bobina se l'opzione è \"assegnaNuova\"\n    if (selectedOption === 'assegnaNuova' && !selectedBobinaId) {\n      onError('Seleziona una bobina prima di procedere');\n      return;\n    }\n\n    // Prepara il messaggio di conferma in base all'opzione selezionata\n    let message = '';\n    let action = null;\n    if (selectedOption === 'assegnaNuova') {\n      const bobina = bobine.find(b => b.id_bobina === selectedBobinaId);\n      message = `Sei sicuro di voler assegnare la bobina ${getBobinaNumber(selectedBobinaId)} al cavo ${selectedCavo.id_cavo}?`;\n      action = () => updateBobina(selectedBobinaId);\n    } else if (selectedOption === 'rimuoviBobina') {\n      message = `Sei sicuro di voler rimuovere la bobina attuale dal cavo ${selectedCavo.id_cavo}?`;\n      action = () => updateBobina('BOBINA_VUOTA');\n    } else if (selectedOption === 'annullaInstallazione') {\n      message = `ATTENZIONE: Questa operazione annullerà l'installazione del cavo ${selectedCavo.id_cavo}. Tutti i metri posati saranno restituiti alla bobina originale. Sei sicuro di voler procedere?`;\n      action = () => annullaInstallazione();\n    }\n    setConfirmDialogMessage(message);\n    setConfirmDialogAction(() => action);\n    setShowConfirmDialog(true);\n  };\n\n  // Funzione per aggiornare la bobina di un cavo\n  const updateBobina = async bobinaId => {\n    try {\n      setLoading(true);\n      await caviService.updateBobina(cantiereId, selectedCavo.id_cavo, bobinaId);\n      onSuccess(`Bobina ${bobinaId === 'BOBINA_VUOTA' ? 'vuota assegnata' : 'assegnata'} con successo`);\n\n      // Reset del form\n      setSelectedCavo(null);\n      setSelectedOption('');\n      setSelectedBobinaId('');\n      setCavoIdInput('');\n\n      // Ricarica i dati\n      loadCavi();\n    } catch (error) {\n      console.error('Errore durante l\\'aggiornamento della bobina:', error);\n      onError('Errore durante l\\'aggiornamento della bobina: ' + (error.detail || error.message || 'Errore sconosciuto'));\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  // Funzione per annullare l'installazione di un cavo\n  const annullaInstallazione = async () => {\n    try {\n      setLoading(true);\n\n      // Chiamata all'API per annullare l'installazione\n      await caviService.cancelInstallation(cantiereId, selectedCavo.id_cavo);\n      onSuccess(`Installazione del cavo ${selectedCavo.id_cavo} annullata con successo`);\n\n      // Reset del form\n      setSelectedCavo(null);\n      setSelectedOption('');\n      setSelectedBobinaId('');\n      setCavoIdInput('');\n\n      // Ricarica i dati\n      loadCavi();\n    } catch (error) {\n      console.error('Errore durante l\\'annullamento dell\\'installazione:', error);\n      onError('Errore durante l\\'annullamento dell\\'installazione: ' + (error.detail || error.message || 'Errore sconosciuto'));\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  // Funzione per chiudere il form e resettare tutto\n  const handleCloseForm = () => {\n    // Reset di tutti gli stati\n    setSelectedCavo(null);\n    setSelectedOption('');\n    setSelectedBobinaId('');\n    setCavoIdInput('');\n    setShowSearchResults(false);\n\n    // Messaggio di conferma\n    onSuccess('Operazione annullata');\n  };\n\n  // Gestisce la chiusura del dialog di incompatibilità\n  const handleCloseIncompatibleReelDialog = () => {\n    setShowIncompatibleReelDialog(false);\n    setIncompatibleReelData({\n      cavo: null,\n      bobina: null\n    });\n  };\n\n  // Gestisce l'aggiornamento delle caratteristiche del cavo per compatibilità\n  const handleUpdateCavoForCompatibility = async (cavo = null, bobina = null) => {\n    // Se non vengono passati parametri, usa i dati dal dialog di incompatibilità\n    if (!cavo || !bobina) {\n      const dialogData = incompatibleReelData;\n      cavo = dialogData.cavo;\n      bobina = dialogData.bobina;\n    }\n    if (!cavo || !bobina) {\n      console.error('Dati mancanti per l\\'aggiornamento del cavo:', {\n        cavo,\n        bobina\n      });\n      onError('Dati mancanti per l\\'aggiornamento del cavo');\n      return;\n    }\n    try {\n      setLoading(true);\n      console.log(`Aggiornamento caratteristiche del cavo ${cavo.id_cavo} per compatibilità con bobina ${bobina.id_bobina}`);\n\n      // Aggiorna le caratteristiche del cavo\n      await caviService.updateCavoForCompatibility(cantiereId, cavo.id_cavo, bobina.id_bobina);\n\n      // Aggiorna il cavo selezionato con le nuove caratteristiche\n      const updatedCavo = await caviService.getCavoById(cantiereId, cavo.id_cavo);\n      setSelectedCavo(updatedCavo);\n\n      // Mantieni la bobina selezionata\n      setSelectedBobinaId(bobina.id_bobina);\n      onSuccess(`Caratteristiche del cavo ${cavo.id_cavo} aggiornate automaticamente per compatibilità con bobina ${getBobinaNumber(bobina.id_bobina)}`);\n\n      // Chiudi il dialog solo se è aperto\n      if (showIncompatibleReelDialog) {\n        handleCloseIncompatibleReelDialog();\n      }\n\n      // Ricarica le bobine per aggiornare la compatibilità\n      loadBobine();\n    } catch (error) {\n      console.error('Errore durante l\\'aggiornamento del cavo:', error);\n      onError('Errore durante l\\'aggiornamento del cavo: ' + (error.detail || error.message || 'Errore sconosciuto'));\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  // Gestisce l'utilizzo di una bobina incompatibile senza aggiornare le caratteristiche del cavo\n  const handleContinueWithIncompatible = async () => {\n    const {\n      cavo,\n      bobina\n    } = incompatibleReelData;\n    if (!cavo || !bobina) {\n      console.error('Dati mancanti per utilizzare la bobina incompatibile:', {\n        cavo,\n        bobina\n      });\n      onError('Dati mancanti per utilizzare la bobina incompatibile');\n      return;\n    }\n    try {\n      setLoading(true);\n      console.log(`Utilizzo bobina incompatibile ${bobina.id_bobina} con cavo ${cavo.id_cavo} senza aggiornare le caratteristiche`);\n\n      // Mantieni la bobina selezionata\n      setSelectedBobinaId(bobina.id_bobina);\n      onSuccess(`Bobina incompatibile ${getBobinaNumber(bobina.id_bobina)} selezionata per il cavo ${cavo.id_cavo}`);\n      handleCloseIncompatibleReelDialog();\n    } catch (error) {\n      console.error('Errore durante la selezione della bobina incompatibile:', error);\n      onError('Errore durante la selezione della bobina incompatibile: ' + (error.detail || error.message || 'Errore sconosciuto'));\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  // Funzione per estrarre il numero della bobina dall'ID completo\n  const getBobinaNumber = idBobina => {\n    if (idBobina === 'BOBINA_VUOTA') return 'BOBINA VUOTA';\n\n    // L'ID completo è nel formato C{id_cantiere}_B{numero_bobina}\n    if (idBobina && idBobina.includes('_B')) {\n      return idBobina.split('_B')[1];\n    }\n    return idBobina;\n  };\n\n  // Renderizza il form per la selezione del cavo\n  const renderCavoSelectionForm = () => /*#__PURE__*/_jsxDEV(Box, {\n    children: [/*#__PURE__*/_jsxDEV(Typography, {\n      variant: \"subtitle2\",\n      sx: {\n        mb: 1,\n        fontWeight: 'bold'\n      },\n      children: \"Seleziona un cavo posato\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 454,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Paper, {\n      sx: {\n        p: 1.5,\n        mb: 2,\n        width: '100%'\n      },\n      children: /*#__PURE__*/_jsxDEV(Box, {\n        sx: {\n          display: 'flex',\n          alignItems: 'center',\n          width: '100%'\n        },\n        children: [/*#__PURE__*/_jsxDEV(Typography, {\n          variant: \"subtitle2\",\n          sx: {\n            mr: 1,\n            minWidth: '80px'\n          },\n          children: \"Cerca cavo\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 461,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(TextField, {\n          size: \"small\",\n          label: \"ID Cavo\",\n          variant: \"outlined\",\n          value: cavoIdInput,\n          onChange: e => setCavoIdInput(e.target.value),\n          placeholder: \"Inserisci l'ID del cavo\",\n          sx: {\n            flexGrow: 0,\n            width: '200px',\n            mr: 1\n          }\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 464,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Button, {\n          variant: \"contained\",\n          color: \"primary\",\n          onClick: handleSearchCavoById,\n          disabled: caviLoading || !cavoIdInput.trim(),\n          startIcon: caviLoading ? /*#__PURE__*/_jsxDEV(CircularProgress, {\n            size: 16\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 478,\n            columnNumber: 38\n          }, this) : /*#__PURE__*/_jsxDEV(SearchIcon, {\n            fontSize: \"small\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 478,\n            columnNumber: 71\n          }, this),\n          size: \"small\",\n          sx: {\n            minWidth: '80px',\n            height: '36px',\n            mr: 2\n          },\n          children: \"CERCA\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 473,\n          columnNumber: 11\n        }, this), selectedCavo && /*#__PURE__*/_jsxDEV(Box, {\n          sx: {\n            display: 'flex',\n            alignItems: 'center',\n            flexGrow: 1,\n            flexWrap: 'nowrap',\n            overflow: 'hidden',\n            ml: 4\n          },\n          children: [/*#__PURE__*/_jsxDEV(Box, {\n            sx: {\n              display: 'flex',\n              alignItems: 'center',\n              mr: 2\n            },\n            children: [/*#__PURE__*/_jsxDEV(Typography, {\n              variant: \"subtitle2\",\n              sx: {\n                fontWeight: 'bold',\n                whiteSpace: 'nowrap',\n                mr: 1,\n                fontSize: '0.95rem'\n              },\n              children: [\"Cavo: \", /*#__PURE__*/_jsxDEV(\"span\", {\n                style: {\n                  color: '#1976d2'\n                },\n                children: selectedCavo.id_cavo\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 490,\n                columnNumber: 25\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 489,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(Divider, {\n              orientation: \"vertical\",\n              flexItem: true,\n              sx: {\n                mx: 1.5\n              }\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 492,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(Box, {\n              sx: {\n                display: 'flex',\n                alignItems: 'center',\n                gap: 3,\n                flexWrap: 'nowrap',\n                overflow: 'hidden'\n              },\n              children: [/*#__PURE__*/_jsxDEV(Box, {\n                sx: {\n                  display: 'flex',\n                  alignItems: 'center',\n                  whiteSpace: 'nowrap'\n                },\n                children: [/*#__PURE__*/_jsxDEV(Typography, {\n                  variant: \"body2\",\n                  sx: {\n                    fontWeight: 'medium',\n                    fontSize: '0.9rem',\n                    mr: 0.5\n                  },\n                  children: \"Tipo:\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 495,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                  variant: \"body2\",\n                  sx: {\n                    fontSize: '0.9rem'\n                  },\n                  children: selectedCavo.tipologia || 'N/A'\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 496,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 494,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(Box, {\n                sx: {\n                  display: 'flex',\n                  alignItems: 'center',\n                  whiteSpace: 'nowrap'\n                },\n                children: [/*#__PURE__*/_jsxDEV(Typography, {\n                  variant: \"body2\",\n                  sx: {\n                    fontWeight: 'medium',\n                    fontSize: '0.9rem',\n                    mr: 0.5\n                  },\n                  children: \"Form:\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 499,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                  variant: \"body2\",\n                  sx: {\n                    fontSize: '0.9rem'\n                  },\n                  children: selectedCavo.sezione || 'N/A'\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 500,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 498,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(Box, {\n                sx: {\n                  display: 'flex',\n                  alignItems: 'center',\n                  whiteSpace: 'nowrap'\n                },\n                children: [/*#__PURE__*/_jsxDEV(Typography, {\n                  variant: \"body2\",\n                  sx: {\n                    fontWeight: 'medium',\n                    fontSize: '0.9rem',\n                    mr: 0.5\n                  },\n                  children: \"Metri:\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 503,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                  variant: \"body2\",\n                  sx: {\n                    fontSize: '0.9rem'\n                  },\n                  children: [selectedCavo.metratura_reale || 'N/A', \" m\"]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 504,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 502,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(Box, {\n                sx: {\n                  display: 'flex',\n                  alignItems: 'center',\n                  whiteSpace: 'nowrap'\n                },\n                children: [/*#__PURE__*/_jsxDEV(Typography, {\n                  variant: \"body2\",\n                  sx: {\n                    fontWeight: 'medium',\n                    fontSize: '0.9rem',\n                    mr: 0.5\n                  },\n                  children: \"Stato:\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 507,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(Chip, {\n                  size: \"small\",\n                  label: selectedCavo.stato_installazione || 'N/D',\n                  color: \"success\",\n                  sx: {\n                    height: '22px',\n                    '& .MuiChip-label': {\n                      px: 1,\n                      py: 0,\n                      fontSize: '0.85rem'\n                    }\n                  }\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 508,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 506,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 493,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 488,\n            columnNumber: 15\n          }, this), selectedCavo.id_bobina && /*#__PURE__*/_jsxDEV(_Fragment, {\n            children: [/*#__PURE__*/_jsxDEV(Divider, {\n              orientation: \"vertical\",\n              flexItem: true,\n              sx: {\n                mx: 2\n              }\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 521,\n              columnNumber: 19\n            }, this), /*#__PURE__*/_jsxDEV(Box, {\n              sx: {\n                display: 'flex',\n                alignItems: 'center'\n              },\n              children: [/*#__PURE__*/_jsxDEV(Typography, {\n                variant: \"subtitle2\",\n                sx: {\n                  fontWeight: 'bold',\n                  whiteSpace: 'nowrap',\n                  mr: 1,\n                  fontSize: '0.95rem',\n                  color: '#2e7d32'\n                },\n                children: [\"Bobina attuale: \", /*#__PURE__*/_jsxDEV(\"span\", {\n                  children: selectedCavo.id_bobina === 'BOBINA_VUOTA' ? 'VUOTA' : getBobinaNumber(selectedCavo.id_bobina)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 524,\n                  columnNumber: 39\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 523,\n                columnNumber: 21\n              }, this), (() => {\n                if (selectedCavo.id_bobina === 'BOBINA_VUOTA') {\n                  return /*#__PURE__*/_jsxDEV(Box, {\n                    sx: {\n                      display: 'flex',\n                      alignItems: 'center',\n                      whiteSpace: 'nowrap'\n                    },\n                    children: /*#__PURE__*/_jsxDEV(Typography, {\n                      variant: \"body2\",\n                      sx: {\n                        fontSize: '0.9rem',\n                        color: 'text.secondary',\n                        fontStyle: 'italic'\n                      },\n                      children: \"(Cavo posato senza bobina specifica)\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 530,\n                      columnNumber: 29\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 529,\n                    columnNumber: 27\n                  }, this);\n                }\n                const bobina = bobine.find(b => b.id_bobina === selectedCavo.id_bobina);\n                return bobina ? /*#__PURE__*/_jsxDEV(Box, {\n                  sx: {\n                    display: 'flex',\n                    alignItems: 'center',\n                    gap: 3,\n                    flexWrap: 'nowrap',\n                    overflow: 'hidden'\n                  },\n                  children: [/*#__PURE__*/_jsxDEV(Box, {\n                    sx: {\n                      display: 'flex',\n                      alignItems: 'center',\n                      whiteSpace: 'nowrap'\n                    },\n                    children: [/*#__PURE__*/_jsxDEV(Typography, {\n                      variant: \"body2\",\n                      sx: {\n                        fontWeight: 'medium',\n                        fontSize: '0.9rem',\n                        mr: 0.5\n                      },\n                      children: \"Residui:\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 541,\n                      columnNumber: 29\n                    }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                      variant: \"body2\",\n                      sx: {\n                        fontSize: '0.9rem',\n                        color: 'success.main',\n                        fontWeight: 'bold'\n                      },\n                      children: [bobina.metri_residui || 0, \" m\"]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 542,\n                      columnNumber: 29\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 540,\n                    columnNumber: 27\n                  }, this), /*#__PURE__*/_jsxDEV(Box, {\n                    sx: {\n                      display: 'flex',\n                      alignItems: 'center',\n                      whiteSpace: 'nowrap'\n                    },\n                    children: [/*#__PURE__*/_jsxDEV(Typography, {\n                      variant: \"body2\",\n                      sx: {\n                        fontWeight: 'medium',\n                        fontSize: '0.9rem',\n                        mr: 0.5\n                      },\n                      children: \"Stato:\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 547,\n                      columnNumber: 29\n                    }, this), /*#__PURE__*/_jsxDEV(Chip, {\n                      size: \"small\",\n                      label: bobina.stato_bobina || 'N/D',\n                      color: bobina.stato_bobina === 'Disponibile' ? 'success' : 'warning',\n                      sx: {\n                        height: '22px',\n                        '& .MuiChip-label': {\n                          px: 1,\n                          py: 0,\n                          fontSize: '0.85rem'\n                        }\n                      }\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 548,\n                      columnNumber: 29\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 546,\n                    columnNumber: 27\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 539,\n                  columnNumber: 25\n                }, this) : null;\n              })()]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 522,\n              columnNumber: 19\n            }, this)]\n          }, void 0, true), selectedBobinaId && selectedBobinaId !== (selectedCavo === null || selectedCavo === void 0 ? void 0 : selectedCavo.id_bobina) && /*#__PURE__*/_jsxDEV(_Fragment, {\n            children: [/*#__PURE__*/_jsxDEV(Divider, {\n              orientation: \"vertical\",\n              flexItem: true,\n              sx: {\n                mx: 2\n              }\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 565,\n              columnNumber: 19\n            }, this), /*#__PURE__*/_jsxDEV(Box, {\n              sx: {\n                display: 'flex',\n                alignItems: 'center'\n              },\n              children: [/*#__PURE__*/_jsxDEV(Typography, {\n                variant: \"subtitle2\",\n                sx: {\n                  fontWeight: 'bold',\n                  whiteSpace: 'nowrap',\n                  mr: 1,\n                  fontSize: '0.95rem',\n                  color: '#1976d2'\n                },\n                children: [\"Bobina selezionata: \", /*#__PURE__*/_jsxDEV(\"span\", {\n                  children: selectedBobinaId === 'BOBINA_VUOTA' ? 'VUOTA' : getBobinaNumber(selectedBobinaId)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 568,\n                  columnNumber: 43\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 567,\n                columnNumber: 21\n              }, this), (() => {\n                if (selectedBobinaId === 'BOBINA_VUOTA') {\n                  return /*#__PURE__*/_jsxDEV(Box, {\n                    sx: {\n                      display: 'flex',\n                      alignItems: 'center',\n                      whiteSpace: 'nowrap'\n                    },\n                    children: /*#__PURE__*/_jsxDEV(Typography, {\n                      variant: \"body2\",\n                      sx: {\n                        fontSize: '0.9rem',\n                        color: 'text.secondary',\n                        fontStyle: 'italic'\n                      },\n                      children: \"(Rimozione bobina attuale)\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 574,\n                      columnNumber: 29\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 573,\n                    columnNumber: 27\n                  }, this);\n                }\n                const bobina = bobine.find(b => b.id_bobina === selectedBobinaId);\n                if (!bobina) return null;\n\n                // Verifica compatibilità\n                const isCompatible = selectedCavo && String(bobina.tipologia || '').trim() === String(selectedCavo.tipologia || '').trim() && String(bobina.sezione || '').trim() === String(selectedCavo.sezione || '').trim();\n                return /*#__PURE__*/_jsxDEV(Box, {\n                  sx: {\n                    display: 'flex',\n                    alignItems: 'center',\n                    gap: 3,\n                    flexWrap: 'nowrap',\n                    overflow: 'hidden'\n                  },\n                  children: [/*#__PURE__*/_jsxDEV(Box, {\n                    sx: {\n                      display: 'flex',\n                      alignItems: 'center',\n                      whiteSpace: 'nowrap'\n                    },\n                    children: [/*#__PURE__*/_jsxDEV(Typography, {\n                      variant: \"body2\",\n                      sx: {\n                        fontWeight: 'medium',\n                        fontSize: '0.9rem',\n                        mr: 0.5\n                      },\n                      children: \"Tipo:\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 592,\n                      columnNumber: 29\n                    }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                      variant: \"body2\",\n                      sx: {\n                        fontSize: '0.9rem',\n                        color: isCompatible ? 'text.primary' : 'error.main'\n                      },\n                      children: bobina.tipologia || 'N/A'\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 593,\n                      columnNumber: 29\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 591,\n                    columnNumber: 27\n                  }, this), /*#__PURE__*/_jsxDEV(Box, {\n                    sx: {\n                      display: 'flex',\n                      alignItems: 'center',\n                      whiteSpace: 'nowrap'\n                    },\n                    children: [/*#__PURE__*/_jsxDEV(Typography, {\n                      variant: \"body2\",\n                      sx: {\n                        fontWeight: 'medium',\n                        fontSize: '0.9rem',\n                        mr: 0.5\n                      },\n                      children: \"Form:\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 598,\n                      columnNumber: 29\n                    }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                      variant: \"body2\",\n                      sx: {\n                        fontSize: '0.9rem',\n                        color: isCompatible ? 'text.primary' : 'error.main'\n                      },\n                      children: bobina.sezione || 'N/A'\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 599,\n                      columnNumber: 29\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 597,\n                    columnNumber: 27\n                  }, this), /*#__PURE__*/_jsxDEV(Box, {\n                    sx: {\n                      display: 'flex',\n                      alignItems: 'center',\n                      whiteSpace: 'nowrap'\n                    },\n                    children: [/*#__PURE__*/_jsxDEV(Typography, {\n                      variant: \"body2\",\n                      sx: {\n                        fontWeight: 'medium',\n                        fontSize: '0.9rem',\n                        mr: 0.5\n                      },\n                      children: \"Residui:\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 604,\n                      columnNumber: 29\n                    }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                      variant: \"body2\",\n                      sx: {\n                        fontSize: '0.9rem',\n                        color: 'primary.main',\n                        fontWeight: 'bold'\n                      },\n                      children: [bobina.metri_residui || 0, \" m\"]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 605,\n                      columnNumber: 29\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 603,\n                    columnNumber: 27\n                  }, this), /*#__PURE__*/_jsxDEV(Box, {\n                    sx: {\n                      display: 'flex',\n                      alignItems: 'center',\n                      whiteSpace: 'nowrap'\n                    },\n                    children: [/*#__PURE__*/_jsxDEV(Typography, {\n                      variant: \"body2\",\n                      sx: {\n                        fontWeight: 'medium',\n                        fontSize: '0.9rem',\n                        mr: 0.5\n                      },\n                      children: \"Stato:\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 610,\n                      columnNumber: 29\n                    }, this), /*#__PURE__*/_jsxDEV(Chip, {\n                      size: \"small\",\n                      label: bobina.stato_bobina || 'N/D',\n                      color: bobina.stato_bobina === 'Disponibile' ? 'success' : 'warning',\n                      sx: {\n                        height: '22px',\n                        '& .MuiChip-label': {\n                          px: 1,\n                          py: 0,\n                          fontSize: '0.85rem'\n                        }\n                      }\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 611,\n                      columnNumber: 29\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 609,\n                    columnNumber: 27\n                  }, this), !isCompatible && /*#__PURE__*/_jsxDEV(Box, {\n                    sx: {\n                      display: 'flex',\n                      alignItems: 'center',\n                      whiteSpace: 'nowrap'\n                    },\n                    children: /*#__PURE__*/_jsxDEV(Chip, {\n                      size: \"small\",\n                      label: \"Non compatibile\",\n                      color: \"error\",\n                      variant: \"outlined\",\n                      sx: {\n                        height: '22px',\n                        '& .MuiChip-label': {\n                          px: 1,\n                          py: 0,\n                          fontSize: '0.85rem'\n                        }\n                      }\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 620,\n                      columnNumber: 31\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 619,\n                    columnNumber: 29\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 590,\n                  columnNumber: 25\n                }, this);\n              })()]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 566,\n              columnNumber: 19\n            }, this)]\n          }, void 0, true)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 487,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 460,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 459,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Paper, {\n      sx: {\n        p: 1.5,\n        width: '100%'\n      },\n      children: [/*#__PURE__*/_jsxDEV(Typography, {\n        variant: \"subtitle2\",\n        sx: {\n          mb: 1\n        },\n        children: \"Seleziona dalla lista\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 642,\n        columnNumber: 9\n      }, this), caviLoading ? /*#__PURE__*/_jsxDEV(Box, {\n        sx: {\n          display: 'flex',\n          justifyContent: 'center',\n          my: 2\n        },\n        children: /*#__PURE__*/_jsxDEV(CircularProgress, {\n          size: 24\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 648,\n          columnNumber: 13\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 647,\n        columnNumber: 11\n      }, this) : cavi.length === 0 ? /*#__PURE__*/_jsxDEV(Alert, {\n        severity: \"info\",\n        sx: {\n          py: 0.5\n        },\n        children: /*#__PURE__*/_jsxDEV(Typography, {\n          variant: \"caption\",\n          children: \"Non ci sono cavi posati disponibili.\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 652,\n          columnNumber: 13\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 651,\n        columnNumber: 11\n      }, this) : /*#__PURE__*/_jsxDEV(TableContainer, {\n        component: Paper,\n        variant: \"outlined\",\n        sx: {\n          maxHeight: '300px',\n          overflow: 'auto',\n          width: '100%'\n        },\n        children: /*#__PURE__*/_jsxDEV(Table, {\n          size: \"small\",\n          stickyHeader: true,\n          children: [/*#__PURE__*/_jsxDEV(TableHead, {\n            children: /*#__PURE__*/_jsxDEV(TableRow, {\n              sx: {\n                '& th': {\n                  fontWeight: 'bold',\n                  py: 1,\n                  bgcolor: '#f5f5f5'\n                }\n              },\n              children: [/*#__PURE__*/_jsxDEV(TableCell, {\n                children: \"ID Cavo\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 659,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n                children: \"Tipologia\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 660,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n                children: \"Formazione\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 661,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n                children: \"Metri\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 662,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n                children: \"Bobina\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 663,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n                children: \"Stato\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 664,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n                align: \"center\",\n                sx: {\n                  width: '40px'\n                },\n                children: \"Info\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 665,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 658,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 657,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(TableBody, {\n            children: cavi.map(cavo => /*#__PURE__*/_jsxDEV(TableRow, {\n              hover: true,\n              onClick: () => handleSelectCavo(cavo),\n              sx: {\n                cursor: 'pointer',\n                '&:hover': {\n                  bgcolor: '#f1f8e9'\n                },\n                '& td': {\n                  py: 0.5\n                }\n              },\n              children: [/*#__PURE__*/_jsxDEV(TableCell, {\n                sx: {\n                  fontWeight: 'medium'\n                },\n                children: cavo.id_cavo\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 680,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n                children: cavo.tipologia || 'N/A'\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 681,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n                children: cavo.sezione || 'N/A'\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 682,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n                children: [cavo.metratura_reale || 'N/A', \" m\"]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 683,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n                children: cavo.id_bobina ? cavo.id_bobina === 'BOBINA_VUOTA' ? 'VUOTA' : getBobinaNumber(cavo.id_bobina) : 'VUOTA'\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 684,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n                children: /*#__PURE__*/_jsxDEV(Chip, {\n                  size: \"small\",\n                  label: \"Installato\",\n                  color: \"success\",\n                  sx: {\n                    height: '20px',\n                    '& .MuiChip-label': {\n                      px: 1,\n                      py: 0,\n                      fontSize: '0.7rem'\n                    }\n                  }\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 688,\n                  columnNumber: 23\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 687,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n                align: \"center\",\n                children: /*#__PURE__*/_jsxDEV(IconButton, {\n                  size: \"small\",\n                  onClick: e => {\n                    e.stopPropagation();\n                    setSelectedCavo(cavo);\n                    setShowCavoDetailsDialog(true);\n                  },\n                  children: /*#__PURE__*/_jsxDEV(InfoIcon, {\n                    fontSize: \"small\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 704,\n                    columnNumber: 25\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 696,\n                  columnNumber: 23\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 695,\n                columnNumber: 21\n              }, this)]\n            }, cavo.id_cavo, true, {\n              fileName: _jsxFileName,\n              lineNumber: 670,\n              columnNumber: 19\n            }, this))\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 668,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 656,\n          columnNumber: 13\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 655,\n        columnNumber: 11\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 641,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 453,\n    columnNumber: 5\n  }, this);\n\n  // Renderizza le opzioni di modifica\n  const renderModificaOptions = () => {\n    if (!selectedCavo) return null;\n    return /*#__PURE__*/_jsxDEV(Paper, {\n      sx: {\n        p: 3,\n        mb: 3\n      },\n      children: [/*#__PURE__*/_jsxDEV(Typography, {\n        variant: \"h6\",\n        gutterBottom: true,\n        children: \"Opzioni di modifica\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 725,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(RadioGroup, {\n        value: selectedOption,\n        onChange: handleOptionChange,\n        children: [/*#__PURE__*/_jsxDEV(FormControlLabel, {\n          value: \"assegnaNuova\",\n          control: /*#__PURE__*/_jsxDEV(Radio, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 735,\n            columnNumber: 22\n          }, this),\n          label: \"Assegna nuova bobina\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 733,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(FormControlLabel, {\n          value: \"rimuoviBobina\",\n          control: /*#__PURE__*/_jsxDEV(Radio, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 740,\n            columnNumber: 22\n          }, this),\n          label: \"Rimuovi bobina attuale\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 738,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(FormControlLabel, {\n          value: \"annullaInstallazione\",\n          control: /*#__PURE__*/_jsxDEV(Radio, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 745,\n            columnNumber: 22\n          }, this),\n          label: \"Annulla installazione\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 743,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 729,\n        columnNumber: 9\n      }, this), selectedOption === 'assegnaNuova' && renderBobineSelection(), selectedOption === 'rimuoviBobina' && /*#__PURE__*/_jsxDEV(Alert, {\n        severity: \"info\",\n        sx: {\n          mt: 2\n        },\n        children: \"Questa operazione rimuover\\xE0 l'associazione con la bobina attuale, assegnando una \\\"BOBINA_VUOTA\\\" al cavo. Il cavo rimarr\\xE0 nello stato posato e i metri posati rimarranno invariati. La bobina attuale (se presente) riavr\\xE0 i suoi metri restituiti.\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 753,\n        columnNumber: 11\n      }, this), selectedOption === 'annullaInstallazione' && /*#__PURE__*/_jsxDEV(Alert, {\n        severity: \"warning\",\n        sx: {\n          mt: 2\n        },\n        children: [/*#__PURE__*/_jsxDEV(Typography, {\n          variant: \"body2\",\n          fontWeight: \"bold\",\n          children: \"ATTENZIONE: Questa operazione annuller\\xE0 completamente l'installazione del cavo.\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 762,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(Typography, {\n          variant: \"body2\",\n          children: [\"- Il cavo torner\\xE0 allo stato \\\"Da installare\\\"\", /*#__PURE__*/_jsxDEV(\"br\", {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 766,\n            columnNumber: 59\n          }, this), \"- La metratura reale sar\\xE0 azzerata\", /*#__PURE__*/_jsxDEV(\"br\", {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 767,\n            columnNumber: 49\n          }, this), \"- L'associazione con la bobina sar\\xE0 rimossa\", /*#__PURE__*/_jsxDEV(\"br\", {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 768,\n            columnNumber: 58\n          }, this), \"- I metri posati saranno restituiti alla bobina originale (se presente)\"]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 765,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 761,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(Box, {\n        sx: {\n          mt: 3,\n          display: 'flex',\n          justifyContent: 'flex-end',\n          gap: 2\n        },\n        children: [/*#__PURE__*/_jsxDEV(Button, {\n          variant: \"outlined\",\n          color: \"secondary\",\n          startIcon: /*#__PURE__*/_jsxDEV(CancelIcon, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 778,\n            columnNumber: 24\n          }, this),\n          onClick: handleCloseForm,\n          disabled: loading,\n          children: \"Annulla operazione\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 775,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Button, {\n          variant: \"contained\",\n          color: \"primary\",\n          startIcon: /*#__PURE__*/_jsxDEV(SaveIcon, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 787,\n            columnNumber: 24\n          }, this),\n          onClick: handleSave,\n          disabled: loading || !selectedOption || selectedOption === 'assegnaNuova' && !selectedBobinaId,\n          children: \"Salva modifiche\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 784,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 774,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 724,\n      columnNumber: 7\n    }, this);\n  };\n\n  // Renderizza la selezione delle bobine\n  const renderBobineSelection = () => {\n    if (bobineLoading) {\n      return /*#__PURE__*/_jsxDEV(Box, {\n        sx: {\n          display: 'flex',\n          justifyContent: 'center',\n          mt: 2\n        },\n        children: /*#__PURE__*/_jsxDEV(CircularProgress, {}, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 803,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 802,\n        columnNumber: 9\n      }, this);\n    }\n\n    // Filtra le bobine in base al testo di ricerca\n    const bobineFiltrate = bobine.filter(bobina => {\n      const searchLower = bobinaSearchText.toLowerCase();\n      return !bobinaSearchText || getBobinaNumber(bobina.id_bobina).toLowerCase().includes(searchLower) || String(bobina.tipologia || '').toLowerCase().includes(searchLower) || String(bobina.sezione || '').toLowerCase().includes(searchLower);\n    });\n\n    // Separa le bobine compatibili e non compatibili\n    const bobineCompatibili = selectedCavo ? bobineFiltrate.filter(bobina => String(bobina.tipologia || '').trim() === String(selectedCavo.tipologia || '').trim() && String(bobina.sezione || '').trim() === String(selectedCavo.sezione || '').trim() && (bobina.stato_bobina === 'Disponibile' || bobina.stato_bobina === 'In uso')) : [];\n    const bobineNonCompatibili = selectedCavo ? bobineFiltrate.filter(bobina => !(String(bobina.tipologia || '').trim() === String(selectedCavo.tipologia || '').trim() && String(bobina.sezione || '').trim() === String(selectedCavo.sezione || '').trim()) && (bobina.stato_bobina === 'Disponibile' || bobina.stato_bobina === 'In uso')) : [];\n\n    // Ordina per metri residui (decrescente)\n    bobineCompatibili.sort((a, b) => b.metri_residui - a.metri_residui);\n    bobineNonCompatibili.sort((a, b) => b.metri_residui - a.metri_residui);\n    return /*#__PURE__*/_jsxDEV(Box, {\n      sx: {\n        mt: 2\n      },\n      children: [/*#__PURE__*/_jsxDEV(Box, {\n        sx: {\n          mb: 2\n        },\n        children: /*#__PURE__*/_jsxDEV(TextField, {\n          fullWidth: true,\n          label: \"Ricerca bobina (ID, tipologia, formazione)\",\n          value: bobinaSearchText,\n          onChange: e => setBobinaSearchText(e.target.value),\n          InputProps: {\n            startAdornment: /*#__PURE__*/_jsxDEV(InputAdornment, {\n              position: \"start\",\n              children: /*#__PURE__*/_jsxDEV(SearchIcon, {}, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 848,\n                columnNumber: 19\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 847,\n              columnNumber: 17\n            }, this)\n          },\n          sx: {\n            mb: 2\n          }\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 840,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 839,\n        columnNumber: 9\n      }, this), selectedBobinaId && /*#__PURE__*/_jsxDEV(Alert, {\n        severity: \"success\",\n        sx: {\n          mb: 2\n        },\n        children: [\"Bobina selezionata: \", /*#__PURE__*/_jsxDEV(\"strong\", {\n          children: getBobinaNumber(selectedBobinaId)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 861,\n          columnNumber: 33\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 860,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(Grid, {\n        container: true,\n        spacing: 2,\n        children: [/*#__PURE__*/_jsxDEV(Grid, {\n          item: true,\n          xs: 12,\n          md: 6,\n          children: /*#__PURE__*/_jsxDEV(Paper, {\n            variant: \"outlined\",\n            sx: {\n              p: 2,\n              height: '100%'\n            },\n            children: [/*#__PURE__*/_jsxDEV(Typography, {\n              variant: \"subtitle2\",\n              sx: {\n                fontWeight: 'bold',\n                mb: 1\n              },\n              children: \"ELENCO BOBINE COMPATIBILI\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 870,\n              columnNumber: 15\n            }, this), bobineCompatibili.length > 0 ? /*#__PURE__*/_jsxDEV(_Fragment, {\n              children: [/*#__PURE__*/_jsxDEV(Box, {\n                sx: {\n                  display: 'flex',\n                  alignItems: 'center',\n                  width: '100%',\n                  py: 0.8,\n                  px: 1.8,\n                  bgcolor: '#f5f5f5',\n                  borderRadius: 1,\n                  mb: 1\n                },\n                children: [/*#__PURE__*/_jsxDEV(Box, {\n                  sx: {\n                    width: '60px',\n                    mr: 2\n                  },\n                  children: /*#__PURE__*/_jsxDEV(Typography, {\n                    variant: \"caption\",\n                    sx: {\n                      fontWeight: 'bold',\n                      fontSize: '0.85rem'\n                    },\n                    children: \"ID\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 878,\n                    columnNumber: 23\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 877,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(Box, {\n                  sx: {\n                    width: '120px',\n                    mr: 2\n                  },\n                  children: /*#__PURE__*/_jsxDEV(Typography, {\n                    variant: \"caption\",\n                    sx: {\n                      fontWeight: 'bold',\n                      fontSize: '0.85rem'\n                    },\n                    children: \"Tipo\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 881,\n                    columnNumber: 23\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 880,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(Box, {\n                  sx: {\n                    width: '100px',\n                    mr: 2\n                  },\n                  children: /*#__PURE__*/_jsxDEV(Typography, {\n                    variant: \"caption\",\n                    sx: {\n                      fontWeight: 'bold',\n                      fontSize: '0.85rem'\n                    },\n                    children: \"Form.\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 884,\n                    columnNumber: 23\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 883,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(Box, {\n                  sx: {\n                    width: '100px',\n                    mr: 2\n                  },\n                  children: /*#__PURE__*/_jsxDEV(Typography, {\n                    variant: \"caption\",\n                    sx: {\n                      fontWeight: 'bold',\n                      fontSize: '0.85rem'\n                    },\n                    children: \"Residui\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 887,\n                    columnNumber: 23\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 886,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(Box, {\n                  sx: {\n                    flexGrow: 0\n                  },\n                  children: /*#__PURE__*/_jsxDEV(Typography, {\n                    variant: \"caption\",\n                    sx: {\n                      fontWeight: 'bold',\n                      fontSize: '0.85rem'\n                    },\n                    children: \"Stato\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 890,\n                    columnNumber: 23\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 889,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 876,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(List, {\n                sx: {\n                  maxHeight: bobineCompatibili.length > 6 ? '300px' : 'auto',\n                  overflowY: bobineCompatibili.length > 6 ? 'auto' : 'visible',\n                  overflowX: 'hidden',\n                  bgcolor: 'background.paper'\n                },\n                children: bobineCompatibili.map(bobina => /*#__PURE__*/_jsxDEV(ListItem, {\n                  disablePadding: true,\n                  secondaryAction: /*#__PURE__*/_jsxDEV(IconButton, {\n                    edge: \"end\",\n                    size: \"small\",\n                    onClick: () => handleSelectBobina(bobina.id_bobina),\n                    children: /*#__PURE__*/_jsxDEV(AddCircleOutlineIcon, {\n                      color: \"primary\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 904,\n                      columnNumber: 29\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 899,\n                    columnNumber: 27\n                  }, this),\n                  sx: {\n                    bgcolor: selectedBobinaId === bobina.id_bobina ? 'rgba(76, 175, 80, 0.12)' : 'inherit',\n                    borderRadius: '4px',\n                    mb: 0.5,\n                    border: selectedBobinaId === bobina.id_bobina ? '1px solid #4caf50' : 'none'\n                  },\n                  children: /*#__PURE__*/_jsxDEV(ListItemButton, {\n                    dense: true,\n                    onClick: () => handleSelectBobina(bobina.id_bobina),\n                    children: /*#__PURE__*/_jsxDEV(Box, {\n                      sx: {\n                        display: 'flex',\n                        alignItems: 'center',\n                        width: '100%',\n                        py: 0.8\n                      },\n                      children: [/*#__PURE__*/_jsxDEV(Box, {\n                        sx: {\n                          width: '60px',\n                          mr: 2\n                        },\n                        children: /*#__PURE__*/_jsxDEV(Typography, {\n                          variant: \"body2\",\n                          sx: {\n                            fontWeight: 'bold',\n                            fontSize: '0.9rem'\n                          },\n                          children: getBobinaNumber(bobina.id_bobina)\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 920,\n                          columnNumber: 31\n                        }, this)\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 919,\n                        columnNumber: 29\n                      }, this), /*#__PURE__*/_jsxDEV(Box, {\n                        sx: {\n                          width: '120px',\n                          mr: 2\n                        },\n                        children: /*#__PURE__*/_jsxDEV(Typography, {\n                          variant: \"body2\",\n                          sx: {\n                            fontSize: '0.85rem'\n                          },\n                          children: bobina.tipologia || 'N/A'\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 925,\n                          columnNumber: 31\n                        }, this)\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 924,\n                        columnNumber: 29\n                      }, this), /*#__PURE__*/_jsxDEV(Box, {\n                        sx: {\n                          width: '100px',\n                          mr: 2\n                        },\n                        children: /*#__PURE__*/_jsxDEV(Typography, {\n                          variant: \"body2\",\n                          sx: {\n                            fontSize: '0.85rem'\n                          },\n                          children: bobina.sezione || 'N/A'\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 930,\n                          columnNumber: 31\n                        }, this)\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 929,\n                        columnNumber: 29\n                      }, this), /*#__PURE__*/_jsxDEV(Box, {\n                        sx: {\n                          width: '100px',\n                          mr: 2\n                        },\n                        children: /*#__PURE__*/_jsxDEV(Typography, {\n                          variant: \"body2\",\n                          sx: {\n                            fontWeight: 'bold',\n                            fontSize: '0.85rem',\n                            color: 'success.main'\n                          },\n                          children: [bobina.metri_residui || 0, \" m\"]\n                        }, void 0, true, {\n                          fileName: _jsxFileName,\n                          lineNumber: 935,\n                          columnNumber: 31\n                        }, this)\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 934,\n                        columnNumber: 29\n                      }, this), /*#__PURE__*/_jsxDEV(Box, {\n                        sx: {\n                          flexGrow: 0\n                        },\n                        children: /*#__PURE__*/_jsxDEV(Chip, {\n                          size: \"small\",\n                          label: bobina.stato_bobina || 'N/D',\n                          color: getReelStateColor(bobina.stato_bobina),\n                          variant: \"outlined\",\n                          sx: {\n                            height: 22,\n                            fontSize: '0.8rem',\n                            '& .MuiChip-label': {\n                              px: 1,\n                              py: 0\n                            }\n                          }\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 940,\n                          columnNumber: 31\n                        }, this)\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 939,\n                        columnNumber: 29\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 918,\n                      columnNumber: 27\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 914,\n                    columnNumber: 25\n                  }, this)\n                }, bobina.id_bobina, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 895,\n                  columnNumber: 23\n                }, this))\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 893,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true) : /*#__PURE__*/_jsxDEV(Alert, {\n              severity: \"info\",\n              sx: {\n                mt: 1\n              },\n              children: \"Nessuna bobina compatibile disponibile. Puoi usare BOBINA VUOTA o selezionare una bobina non compatibile.\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 955,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 869,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 868,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Grid, {\n          item: true,\n          xs: 12,\n          md: 6,\n          children: /*#__PURE__*/_jsxDEV(Paper, {\n            variant: \"outlined\",\n            sx: {\n              p: 2,\n              height: '100%'\n            },\n            children: [/*#__PURE__*/_jsxDEV(Typography, {\n              variant: \"subtitle2\",\n              sx: {\n                fontWeight: 'bold',\n                mb: 1\n              },\n              children: \"ELENCO BOBINE NON COMPATIBILI\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 965,\n              columnNumber: 15\n            }, this), bobineNonCompatibili.length > 0 ? /*#__PURE__*/_jsxDEV(_Fragment, {\n              children: [/*#__PURE__*/_jsxDEV(Box, {\n                sx: {\n                  display: 'flex',\n                  alignItems: 'center',\n                  width: '100%',\n                  py: 0.8,\n                  px: 1.8,\n                  bgcolor: '#f5f5f5',\n                  borderRadius: 1,\n                  mb: 1\n                },\n                children: [/*#__PURE__*/_jsxDEV(Box, {\n                  sx: {\n                    width: '60px',\n                    mr: 2\n                  },\n                  children: /*#__PURE__*/_jsxDEV(Typography, {\n                    variant: \"caption\",\n                    sx: {\n                      fontWeight: 'bold',\n                      fontSize: '0.85rem'\n                    },\n                    children: \"ID\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 973,\n                    columnNumber: 23\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 972,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(Box, {\n                  sx: {\n                    width: '120px',\n                    mr: 2\n                  },\n                  children: /*#__PURE__*/_jsxDEV(Typography, {\n                    variant: \"caption\",\n                    sx: {\n                      fontWeight: 'bold',\n                      fontSize: '0.85rem'\n                    },\n                    children: \"Tipo\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 976,\n                    columnNumber: 23\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 975,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(Box, {\n                  sx: {\n                    width: '100px',\n                    mr: 2\n                  },\n                  children: /*#__PURE__*/_jsxDEV(Typography, {\n                    variant: \"caption\",\n                    sx: {\n                      fontWeight: 'bold',\n                      fontSize: '0.85rem'\n                    },\n                    children: \"Form.\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 979,\n                    columnNumber: 23\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 978,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(Box, {\n                  sx: {\n                    width: '100px',\n                    mr: 2\n                  },\n                  children: /*#__PURE__*/_jsxDEV(Typography, {\n                    variant: \"caption\",\n                    sx: {\n                      fontWeight: 'bold',\n                      fontSize: '0.85rem'\n                    },\n                    children: \"Residui\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 982,\n                    columnNumber: 23\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 981,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(Box, {\n                  sx: {\n                    flexGrow: 0\n                  },\n                  children: /*#__PURE__*/_jsxDEV(Typography, {\n                    variant: \"caption\",\n                    sx: {\n                      fontWeight: 'bold',\n                      fontSize: '0.85rem'\n                    },\n                    children: \"Stato\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 985,\n                    columnNumber: 23\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 984,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 971,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(List, {\n                sx: {\n                  maxHeight: bobineNonCompatibili.length > 6 ? '300px' : 'auto',\n                  overflowY: bobineNonCompatibili.length > 6 ? 'auto' : 'visible',\n                  overflowX: 'hidden',\n                  bgcolor: 'background.paper'\n                },\n                children: bobineNonCompatibili.map(bobina => /*#__PURE__*/_jsxDEV(ListItem, {\n                  disablePadding: true,\n                  secondaryAction: /*#__PURE__*/_jsxDEV(IconButton, {\n                    edge: \"end\",\n                    size: \"small\",\n                    onClick: () => handleSelectBobina(bobina.id_bobina),\n                    children: /*#__PURE__*/_jsxDEV(AddCircleOutlineIcon, {\n                      color: \"primary\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 999,\n                      columnNumber: 29\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 994,\n                    columnNumber: 27\n                  }, this),\n                  sx: {\n                    bgcolor: selectedBobinaId === bobina.id_bobina ? 'rgba(76, 175, 80, 0.12)' : 'inherit',\n                    borderRadius: '4px',\n                    mb: 0.5,\n                    border: selectedBobinaId === bobina.id_bobina ? '1px solid #4caf50' : 'none'\n                  },\n                  children: /*#__PURE__*/_jsxDEV(ListItemButton, {\n                    dense: true,\n                    onClick: () => handleSelectBobina(bobina.id_bobina),\n                    children: /*#__PURE__*/_jsxDEV(Box, {\n                      sx: {\n                        display: 'flex',\n                        alignItems: 'center',\n                        width: '100%',\n                        py: 0.8\n                      },\n                      children: [/*#__PURE__*/_jsxDEV(Box, {\n                        sx: {\n                          width: '60px',\n                          mr: 2\n                        },\n                        children: /*#__PURE__*/_jsxDEV(Typography, {\n                          variant: \"body2\",\n                          sx: {\n                            fontWeight: 'bold',\n                            fontSize: '0.9rem'\n                          },\n                          children: getBobinaNumber(bobina.id_bobina)\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 1015,\n                          columnNumber: 31\n                        }, this)\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 1014,\n                        columnNumber: 29\n                      }, this), /*#__PURE__*/_jsxDEV(Box, {\n                        sx: {\n                          width: '120px',\n                          mr: 2\n                        },\n                        children: /*#__PURE__*/_jsxDEV(Typography, {\n                          variant: \"body2\",\n                          sx: {\n                            fontSize: '0.85rem'\n                          },\n                          children: bobina.tipologia || 'N/A'\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 1020,\n                          columnNumber: 31\n                        }, this)\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 1019,\n                        columnNumber: 29\n                      }, this), /*#__PURE__*/_jsxDEV(Box, {\n                        sx: {\n                          width: '100px',\n                          mr: 2\n                        },\n                        children: /*#__PURE__*/_jsxDEV(Typography, {\n                          variant: \"body2\",\n                          sx: {\n                            fontSize: '0.85rem'\n                          },\n                          children: bobina.sezione || 'N/A'\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 1025,\n                          columnNumber: 31\n                        }, this)\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 1024,\n                        columnNumber: 29\n                      }, this), /*#__PURE__*/_jsxDEV(Box, {\n                        sx: {\n                          width: '100px',\n                          mr: 2\n                        },\n                        children: /*#__PURE__*/_jsxDEV(Typography, {\n                          variant: \"body2\",\n                          sx: {\n                            fontWeight: 'bold',\n                            fontSize: '0.85rem',\n                            color: 'success.main'\n                          },\n                          children: [bobina.metri_residui || 0, \" m\"]\n                        }, void 0, true, {\n                          fileName: _jsxFileName,\n                          lineNumber: 1030,\n                          columnNumber: 31\n                        }, this)\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 1029,\n                        columnNumber: 29\n                      }, this), /*#__PURE__*/_jsxDEV(Box, {\n                        sx: {\n                          display: 'flex',\n                          gap: 1\n                        },\n                        children: [/*#__PURE__*/_jsxDEV(Chip, {\n                          size: \"small\",\n                          label: bobina.stato_bobina || 'N/D',\n                          color: getReelStateColor(bobina.stato_bobina),\n                          variant: \"outlined\",\n                          sx: {\n                            height: 22,\n                            fontSize: '0.8rem',\n                            '& .MuiChip-label': {\n                              px: 1,\n                              py: 0\n                            }\n                          }\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 1035,\n                          columnNumber: 31\n                        }, this), /*#__PURE__*/_jsxDEV(Chip, {\n                          size: \"small\",\n                          label: \"Non comp.\",\n                          color: \"warning\",\n                          variant: \"outlined\",\n                          sx: {\n                            height: 22,\n                            fontSize: '0.8rem',\n                            '& .MuiChip-label': {\n                              px: 1,\n                              py: 0\n                            }\n                          }\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 1042,\n                          columnNumber: 31\n                        }, this)]\n                      }, void 0, true, {\n                        fileName: _jsxFileName,\n                        lineNumber: 1034,\n                        columnNumber: 29\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1013,\n                      columnNumber: 27\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1009,\n                    columnNumber: 25\n                  }, this)\n                }, bobina.id_bobina, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 990,\n                  columnNumber: 23\n                }, this))\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 988,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true) : /*#__PURE__*/_jsxDEV(Alert, {\n              severity: \"info\",\n              sx: {\n                mt: 1\n              },\n              children: \"Nessuna bobina non compatibile disponibile con i filtri attuali.\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1057,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 964,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 963,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 866,\n        columnNumber: 9\n      }, this), bobineCompatibili.length === 0 && bobineNonCompatibili.length === 0 && /*#__PURE__*/_jsxDEV(Alert, {\n        severity: \"info\",\n        sx: {\n          mt: 2\n        },\n        children: bobinaSearchText ? 'Nessuna bobina trovata con i criteri di ricerca specificati.' : 'Non ci sono bobine disponibili.'\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 1067,\n        columnNumber: 11\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 837,\n      columnNumber: 7\n    }, this);\n  };\n  return /*#__PURE__*/_jsxDEV(Box, {\n    children: [renderCavoSelectionForm(), renderModificaOptions(), /*#__PURE__*/_jsxDEV(Dialog, {\n      open: showCavoDetailsDialog,\n      onClose: () => setShowCavoDetailsDialog(false),\n      maxWidth: \"md\",\n      fullWidth: true,\n      children: [/*#__PURE__*/_jsxDEV(DialogTitle, {\n        children: \"Dettagli completi del cavo\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 1090,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(DialogContent, {\n        children: selectedCavo && /*#__PURE__*/_jsxDEV(CavoDetailsView, {\n          cavo: selectedCavo\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 1092,\n          columnNumber: 28\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 1091,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(DialogActions, {\n        children: /*#__PURE__*/_jsxDEV(Button, {\n          onClick: () => setShowCavoDetailsDialog(false),\n          children: \"Chiudi\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 1095,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 1094,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 1084,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Dialog, {\n      open: showConfirmDialog,\n      onClose: () => setShowConfirmDialog(false),\n      children: [/*#__PURE__*/_jsxDEV(DialogTitle, {\n        children: \"Conferma operazione\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 1104,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(DialogContent, {\n        children: /*#__PURE__*/_jsxDEV(Typography, {\n          children: confirmDialogMessage\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 1106,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 1105,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(DialogActions, {\n        children: [/*#__PURE__*/_jsxDEV(Button, {\n          onClick: () => setShowConfirmDialog(false),\n          color: \"primary\",\n          children: \"Annulla\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 1109,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Button, {\n          onClick: () => {\n            setShowConfirmDialog(false);\n            if (confirmDialogAction) confirmDialogAction();\n          },\n          color: \"primary\",\n          variant: \"contained\",\n          autoFocus: true,\n          children: \"Conferma\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 1115,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 1108,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 1100,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(IncompatibleReelDialog, {\n      open: showIncompatibleReelDialog,\n      onClose: handleCloseIncompatibleReelDialog,\n      cavo: incompatibleReelData.cavo,\n      bobina: incompatibleReelData.bobina,\n      onUpdateCavo: handleUpdateCavoForCompatibility,\n      onSelectAnotherReel: handleCloseIncompatibleReelDialog,\n      onContinueWithIncompatible: handleContinueWithIncompatible\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 1130,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 1076,\n    columnNumber: 5\n  }, this);\n};\n_s(ModificaBobinaForm, \"TJvChHSsI+vxfPs2teIZ/pqfz+I=\", false, function () {\n  return [useNavigate, useParams];\n});\n_c = ModificaBobinaForm;\nexport default ModificaBobinaForm;\nvar _c;\n$RefreshReg$(_c, \"ModificaBobinaForm\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "Box", "Paper", "Typography", "TextField", "<PERSON><PERSON>", "Grid", "FormControl", "InputLabel", "Select", "MenuItem", "Divider", "<PERSON><PERSON>", "CircularProgress", "Dialog", "DialogTitle", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "DialogActions", "Table", "TableBody", "TableCell", "TableContainer", "TableHead", "TableRow", "Radio", "RadioGroup", "FormControlLabel", "IconButton", "InputAdornment", "List", "ListItem", "ListItemText", "ListItemButton", "ListItemSecondaryAction", "Chip", "Search", "SearchIcon", "Save", "SaveIcon", "Cancel", "CancelIcon", "Warning", "WarningIcon", "Info", "InfoIcon", "AddCircleOutline", "AddCircleOutlineIcon", "IncompatibleReelDialog", "useNavigate", "useParams", "caviService", "parcoCaviService", "CavoDetailsView", "CABLE_STATES", "REEL_STATES", "determineCableState", "determineReelState", "canModifyCable", "isCableSpare", "isCableInstalled", "getCableStateColor", "getReelStateColor", "jsxDEV", "_jsxDEV", "Fragment", "_Fragment", "ModificaBobinaForm", "cantiereId", "onSuccess", "onError", "_s", "navigate", "cavoId", "loading", "setLoading", "caviLoading", "setCaviLoading", "bob<PERSON><PERSON><PERSON><PERSON>", "setBobineLoading", "searchResults", "setSearchResults", "showSearchResults", "setShowSearchResults", "cavi", "<PERSON><PERSON><PERSON>", "bobine", "set<PERSON>ob<PERSON>", "compatibleBobine", "setCompatibleBobine", "selected<PERSON><PERSON><PERSON>", "setSelectedCavo", "cavoIdInput", "setCavoIdInput", "selectedOption", "setSelectedOption", "selectedBobinaId", "setSelectedBobinaId", "bobinaSearchText", "setBobinaSearchText", "showIncompatibleReelDialog", "setShowIncompatibleReelDialog", "incompatibleReelData", "setIncompatibleReelData", "cavo", "bobina", "showConfirmDialog", "setShowConfirmDialog", "confirmDialogMessage", "setConfirmDialogMessage", "confirmDialogAction", "setConfirmDialogAction", "showCavoDetailsDialog", "setShowCavoDetailsDialog", "loadCavi", "loadBobine", "length", "cavoFromUrl", "find", "id_cavo", "console", "log", "warn", "caviData", "get<PERSON><PERSON>", "caviInstallati", "filter", "parseFloat", "metratura_reale", "stato_installazione", "error", "detail", "message", "bobine<PERSON><PERSON>", "getBobine", "compatibleBobineData", "tipologia", "sezione", "stato_bobina", "handleSearchCavoById", "trim", "getCavoById", "handleSelectCavo", "handleOptionChange", "event", "target", "value", "handleSelectBobina", "bobina<PERSON>d", "b", "id_bobina", "isCompatible", "String", "handleUpdateCavoForCompatibility", "handleSave", "action", "getBobinaNumber", "updateBobina", "annullaInstallazione", "cancelInstallation", "handleCloseForm", "handleCloseIncompatibleReelDialog", "dialogData", "updateCavoForCompatibility", "updatedCavo", "handleContinueWithIncompatible", "idBobina", "includes", "split", "renderCavoSelectionForm", "children", "variant", "sx", "mb", "fontWeight", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "p", "width", "display", "alignItems", "mr", "min<PERSON><PERSON><PERSON>", "size", "label", "onChange", "e", "placeholder", "flexGrow", "color", "onClick", "disabled", "startIcon", "fontSize", "height", "flexWrap", "overflow", "ml", "whiteSpace", "style", "orientation", "flexItem", "mx", "gap", "px", "py", "fontStyle", "metri_residui", "justifyContent", "my", "severity", "component", "maxHeight", "<PERSON><PERSON><PERSON><PERSON>", "bgcolor", "align", "map", "hover", "cursor", "stopPropagation", "renderModificaOptions", "gutterBottom", "control", "renderBobineSelection", "mt", "bobineFiltrate", "searchLower", "toLowerCase", "bobineCom<PERSON><PERSON><PERSON><PERSON>", "bobineNonCompatibili", "sort", "a", "fullWidth", "InputProps", "startAdornment", "position", "container", "spacing", "item", "xs", "md", "borderRadius", "overflowY", "overflowX", "disablePadding", "secondaryAction", "edge", "border", "dense", "open", "onClose", "max<PERSON><PERSON><PERSON>", "autoFocus", "onUpdateCavo", "onSelectAnotherReel", "onContinueWithIncompatible", "_c", "$RefreshReg$"], "sources": ["C:/CMS/webapp/frontend/src/components/cavi/ModificaBobinaForm.js"], "sourcesContent": ["import React, { useState, useEffect } from 'react';\nimport {\n  Box,\n  Paper,\n  Typography,\n  TextField,\n  Button,\n  Grid,\n  FormControl,\n  InputLabel,\n  Select,\n  MenuItem,\n  Divider,\n  Alert,\n  CircularProgress,\n  Dialog,\n  DialogTitle,\n  DialogContent,\n  DialogActions,\n  Table,\n  TableBody,\n  TableCell,\n  TableContainer,\n  TableHead,\n  TableRow,\n  Radio,\n  RadioGroup,\n  FormControlLabel,\n  IconButton,\n  InputAdornment,\n  List,\n  ListItem,\n  ListItemText,\n  ListItemButton,\n  ListItemSecondaryAction,\n  Chip\n} from '@mui/material';\nimport {\n  Search as SearchIcon,\n  Save as SaveIcon,\n  Cancel as CancelIcon,\n  Warning as WarningIcon,\n  Info as InfoIcon,\n  AddCircleOutline as AddCircleOutlineIcon\n} from '@mui/icons-material';\nimport IncompatibleReelDialog from './IncompatibleReelDialog';\nimport { useNavigate, useParams } from 'react-router-dom';\nimport caviService from '../../services/caviService';\nimport parcoCaviService from '../../services/parcoCaviService';\nimport CavoDetailsView from './CavoDetailsView';\nimport {\n  CABLE_STATES,\n  REEL_STATES,\n  determineCableState,\n  determineReelState,\n  canModifyCable,\n  isCableSpare,\n  isCableInstalled,\n  getCableStateColor,\n  getReelStateColor\n} from '../../utils/stateUtils';\n\n/**\n * Componente per la modifica della bobina di un cavo già posato\n *\n * @param {Object} props - Proprietà del componente\n * @param {string} props.cantiereId - ID del cantiere\n * @param {Function} props.onSuccess - Funzione chiamata al successo dell'operazione\n * @param {Function} props.onError - Funzione chiamata in caso di errore\n */\nconst ModificaBobinaForm = ({ cantiereId, onSuccess, onError }) => {\n  const navigate = useNavigate();\n  const { cavoId } = useParams();\n\n  // Stati per la gestione del form\n  const [loading, setLoading] = useState(false);\n  const [caviLoading, setCaviLoading] = useState(false);\n  const [bobineLoading, setBobineLoading] = useState(false);\n  const [searchResults, setSearchResults] = useState([]);\n  const [showSearchResults, setShowSearchResults] = useState(false);\n\n  // Stati per i dati\n  const [cavi, setCavi] = useState([]);\n  const [bobine, setBobine] = useState([]);\n  const [compatibleBobine, setCompatibleBobine] = useState([]);\n  const [selectedCavo, setSelectedCavo] = useState(null);\n  const [cavoIdInput, setCavoIdInput] = useState('');\n  const [selectedOption, setSelectedOption] = useState('');\n  const [selectedBobinaId, setSelectedBobinaId] = useState('');\n  const [bobinaSearchText, setBobinaSearchText] = useState('');\n\n  // Stati per gestire il dialog di incompatibilità\n  const [showIncompatibleReelDialog, setShowIncompatibleReelDialog] = useState(false);\n  const [incompatibleReelData, setIncompatibleReelData] = useState({ cavo: null, bobina: null });\n\n  // Stati per i dialoghi\n  const [showConfirmDialog, setShowConfirmDialog] = useState(false);\n  const [confirmDialogMessage, setConfirmDialogMessage] = useState('');\n  const [confirmDialogAction, setConfirmDialogAction] = useState(null);\n  const [showCavoDetailsDialog, setShowCavoDetailsDialog] = useState(false);\n\n  // Carica i cavi all'avvio\n  useEffect(() => {\n    loadCavi();\n  }, [cantiereId]);\n\n  // Carica le bobine quando viene selezionato un cavo\n  useEffect(() => {\n    if (selectedCavo) {\n      loadBobine();\n    }\n  }, [selectedCavo]);\n\n  // Pre-seleziona il cavo se viene fornito l'ID del cavo nell'URL\n  useEffect(() => {\n    if (cavoId && cavi.length > 0 && !selectedCavo) {\n      const cavoFromUrl = cavi.find(cavo => cavo.id_cavo === cavoId);\n      if (cavoFromUrl) {\n        console.log('Pre-selezione cavo dall\\'URL:', cavoFromUrl);\n        setSelectedCavo(cavoFromUrl);\n        setCavoIdInput(cavoId);\n      } else {\n        console.warn(`Cavo con ID ${cavoId} non trovato nella lista dei cavi installati`);\n        onError(`Cavo ${cavoId} non trovato o non installato`);\n      }\n    }\n  }, [cavoId, cavi, selectedCavo]);\n\n  // Funzione per caricare i cavi\n  const loadCavi = async () => {\n    try {\n      setCaviLoading(true);\n      console.log(`Caricamento cavi per il cantiere ${cantiereId}...`);\n\n      // Carica solo i cavi installati (con metratura_reale > 0)\n      const caviData = await caviService.getCavi(cantiereId, null, 'Installato');\n      console.log(`Caricati ${caviData.length} cavi installati`);\n\n      // Filtra i cavi che hanno metratura_reale > 0\n      const caviInstallati = caviData.filter(cavo =>\n        parseFloat(cavo.metratura_reale) > 0 || cavo.stato_installazione === 'Installato'\n      );\n\n      setCavi(caviInstallati);\n      console.log(`Filtrati ${caviInstallati.length} cavi effettivamente installati`);\n    } catch (error) {\n      console.error('Errore durante il caricamento dei cavi:', error);\n      onError('Errore durante il caricamento dei cavi: ' + (error.detail || error.message || 'Errore sconosciuto'));\n    } finally {\n      setCaviLoading(false);\n    }\n  };\n\n  // Funzione per caricare le bobine\n  const loadBobine = async () => {\n    if (!selectedCavo) return;\n\n    try {\n      setBobineLoading(true);\n      console.log(`Caricamento bobine per il cantiere ${cantiereId}...`);\n\n      // Carica tutte le bobine disponibili\n      const bobineData = await parcoCaviService.getBobine(cantiereId);\n      console.log(`Caricati ${bobineData.length} bobine`);\n\n      // Filtra le bobine compatibili\n      const compatibleBobineData = bobineData.filter(bobina =>\n        bobina.tipologia === selectedCavo.tipologia &&\n        bobina.sezione === selectedCavo.sezione &&\n        (bobina.stato_bobina === 'Disponibile' || bobina.stato_bobina === 'In uso')\n      );\n\n      setBobine(bobineData);\n      setCompatibleBobine(compatibleBobineData);\n      console.log(`Filtrate ${compatibleBobineData.length} bobine compatibili`);\n    } catch (error) {\n      console.error('Errore durante il caricamento delle bobine:', error);\n      onError('Errore durante il caricamento delle bobine: ' + (error.detail || error.message || 'Errore sconosciuto'));\n    } finally {\n      setBobineLoading(false);\n    }\n  };\n\n  // Gestisce la ricerca di un cavo per ID\n  const handleSearchCavoById = async () => {\n    if (!cavoIdInput.trim()) {\n      onError('Inserisci un ID cavo valido');\n      return;\n    }\n\n    try {\n      setLoading(true);\n      const cavo = await caviService.getCavoById(cantiereId, cavoIdInput.trim());\n\n      // Verifica che il cavo sia installato\n      if (parseFloat(cavo.metratura_reale) <= 0 && cavo.stato_installazione !== 'Installato') {\n        onError('Il cavo selezionato non risulta installato');\n        setLoading(false);\n        return;\n      }\n\n      setSelectedCavo(cavo);\n      setSelectedOption(''); // Reset dell'opzione selezionata\n      setSelectedBobinaId(''); // Reset della bobina selezionata\n      setShowSearchResults(false);\n    } catch (error) {\n      console.error('Errore durante la ricerca del cavo:', error);\n      onError('Errore durante la ricerca del cavo: ' + (error.detail || error.message || 'Errore sconosciuto'));\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  // Gestisce la selezione di un cavo dalla lista\n  const handleSelectCavo = (cavo) => {\n    setSelectedCavo(cavo);\n    setSelectedOption(''); // Reset dell'opzione selezionata\n    setSelectedBobinaId(''); // Reset della bobina selezionata\n    setShowSearchResults(false);\n  };\n\n  // Gestisce il cambio dell'opzione selezionata\n  const handleOptionChange = (event) => {\n    setSelectedOption(event.target.value);\n    setSelectedBobinaId(''); // Reset della bobina selezionata quando cambia l'opzione\n  };\n\n  // Gestisce la selezione di una bobina\n  const handleSelectBobina = (bobinaId) => {\n    console.log('Bobina selezionata:', bobinaId);\n\n    // Trova la bobina selezionata\n    const bobina = bobine.find(b => b.id_bobina === bobinaId);\n\n    if (bobina && selectedCavo) {\n      // Verifica compatibilità\n      const isCompatible =\n        String(bobina.tipologia || '').trim() === String(selectedCavo.tipologia || '').trim() &&\n        String(bobina.sezione || '').trim() === String(selectedCavo.sezione || '').trim();\n\n      if (!isCompatible) {\n        console.log('Bobina incompatibile selezionata:', bobina);\n        console.log('Cavo corrente:', selectedCavo);\n\n        // Aggiorna automaticamente le caratteristiche del cavo per renderlo compatibile\n        handleUpdateCavoForCompatibility(selectedCavo, bobina);\n\n        // Imposta la bobina selezionata per mostrare i dettagli\n        setSelectedBobinaId(bobinaId);\n        return;\n      }\n    }\n\n    // Se compatibile o nessun cavo selezionato, procedi normalmente\n    setSelectedBobinaId(bobinaId);\n  };\n\n\n\n  // Gestisce il salvataggio delle modifiche\n  const handleSave = () => {\n    if (!selectedCavo) {\n      onError('Seleziona un cavo prima di procedere');\n      return;\n    }\n\n    if (!selectedOption) {\n      onError('Seleziona un\\'opzione prima di procedere');\n      return;\n    }\n\n    // Verifica che sia stata selezionata una bobina se l'opzione è \"assegnaNuova\"\n    if (selectedOption === 'assegnaNuova' && !selectedBobinaId) {\n      onError('Seleziona una bobina prima di procedere');\n      return;\n    }\n\n    // Prepara il messaggio di conferma in base all'opzione selezionata\n    let message = '';\n    let action = null;\n\n    if (selectedOption === 'assegnaNuova') {\n      const bobina = bobine.find(b => b.id_bobina === selectedBobinaId);\n      message = `Sei sicuro di voler assegnare la bobina ${getBobinaNumber(selectedBobinaId)} al cavo ${selectedCavo.id_cavo}?`;\n      action = () => updateBobina(selectedBobinaId);\n    } else if (selectedOption === 'rimuoviBobina') {\n      message = `Sei sicuro di voler rimuovere la bobina attuale dal cavo ${selectedCavo.id_cavo}?`;\n      action = () => updateBobina('BOBINA_VUOTA');\n    } else if (selectedOption === 'annullaInstallazione') {\n      message = `ATTENZIONE: Questa operazione annullerà l'installazione del cavo ${selectedCavo.id_cavo}. Tutti i metri posati saranno restituiti alla bobina originale. Sei sicuro di voler procedere?`;\n      action = () => annullaInstallazione();\n    }\n\n    setConfirmDialogMessage(message);\n    setConfirmDialogAction(() => action);\n    setShowConfirmDialog(true);\n  };\n\n  // Funzione per aggiornare la bobina di un cavo\n  const updateBobina = async (bobinaId) => {\n    try {\n      setLoading(true);\n      await caviService.updateBobina(cantiereId, selectedCavo.id_cavo, bobinaId);\n\n      onSuccess(`Bobina ${bobinaId === 'BOBINA_VUOTA' ? 'vuota assegnata' : 'assegnata'} con successo`);\n\n      // Reset del form\n      setSelectedCavo(null);\n      setSelectedOption('');\n      setSelectedBobinaId('');\n      setCavoIdInput('');\n\n      // Ricarica i dati\n      loadCavi();\n    } catch (error) {\n      console.error('Errore durante l\\'aggiornamento della bobina:', error);\n      onError('Errore durante l\\'aggiornamento della bobina: ' + (error.detail || error.message || 'Errore sconosciuto'));\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  // Funzione per annullare l'installazione di un cavo\n  const annullaInstallazione = async () => {\n    try {\n      setLoading(true);\n\n      // Chiamata all'API per annullare l'installazione\n      await caviService.cancelInstallation(cantiereId, selectedCavo.id_cavo);\n\n      onSuccess(`Installazione del cavo ${selectedCavo.id_cavo} annullata con successo`);\n\n      // Reset del form\n      setSelectedCavo(null);\n      setSelectedOption('');\n      setSelectedBobinaId('');\n      setCavoIdInput('');\n\n      // Ricarica i dati\n      loadCavi();\n    } catch (error) {\n      console.error('Errore durante l\\'annullamento dell\\'installazione:', error);\n      onError('Errore durante l\\'annullamento dell\\'installazione: ' + (error.detail || error.message || 'Errore sconosciuto'));\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  // Funzione per chiudere il form e resettare tutto\n  const handleCloseForm = () => {\n    // Reset di tutti gli stati\n    setSelectedCavo(null);\n    setSelectedOption('');\n    setSelectedBobinaId('');\n    setCavoIdInput('');\n    setShowSearchResults(false);\n\n    // Messaggio di conferma\n    onSuccess('Operazione annullata');\n  };\n\n  // Gestisce la chiusura del dialog di incompatibilità\n  const handleCloseIncompatibleReelDialog = () => {\n    setShowIncompatibleReelDialog(false);\n    setIncompatibleReelData({ cavo: null, bobina: null });\n  };\n\n  // Gestisce l'aggiornamento delle caratteristiche del cavo per compatibilità\n  const handleUpdateCavoForCompatibility = async (cavo = null, bobina = null) => {\n    // Se non vengono passati parametri, usa i dati dal dialog di incompatibilità\n    if (!cavo || !bobina) {\n      const dialogData = incompatibleReelData;\n      cavo = dialogData.cavo;\n      bobina = dialogData.bobina;\n    }\n\n    if (!cavo || !bobina) {\n      console.error('Dati mancanti per l\\'aggiornamento del cavo:', { cavo, bobina });\n      onError('Dati mancanti per l\\'aggiornamento del cavo');\n      return;\n    }\n\n    try {\n      setLoading(true);\n      console.log(`Aggiornamento caratteristiche del cavo ${cavo.id_cavo} per compatibilità con bobina ${bobina.id_bobina}`);\n\n      // Aggiorna le caratteristiche del cavo\n      await caviService.updateCavoForCompatibility(cantiereId, cavo.id_cavo, bobina.id_bobina);\n\n      // Aggiorna il cavo selezionato con le nuove caratteristiche\n      const updatedCavo = await caviService.getCavoById(cantiereId, cavo.id_cavo);\n      setSelectedCavo(updatedCavo);\n\n      // Mantieni la bobina selezionata\n      setSelectedBobinaId(bobina.id_bobina);\n\n      onSuccess(`Caratteristiche del cavo ${cavo.id_cavo} aggiornate automaticamente per compatibilità con bobina ${getBobinaNumber(bobina.id_bobina)}`);\n\n      // Chiudi il dialog solo se è aperto\n      if (showIncompatibleReelDialog) {\n        handleCloseIncompatibleReelDialog();\n      }\n\n      // Ricarica le bobine per aggiornare la compatibilità\n      loadBobine();\n    } catch (error) {\n      console.error('Errore durante l\\'aggiornamento del cavo:', error);\n      onError('Errore durante l\\'aggiornamento del cavo: ' + (error.detail || error.message || 'Errore sconosciuto'));\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  // Gestisce l'utilizzo di una bobina incompatibile senza aggiornare le caratteristiche del cavo\n  const handleContinueWithIncompatible = async () => {\n    const { cavo, bobina } = incompatibleReelData;\n    if (!cavo || !bobina) {\n      console.error('Dati mancanti per utilizzare la bobina incompatibile:', { cavo, bobina });\n      onError('Dati mancanti per utilizzare la bobina incompatibile');\n      return;\n    }\n\n    try {\n      setLoading(true);\n      console.log(`Utilizzo bobina incompatibile ${bobina.id_bobina} con cavo ${cavo.id_cavo} senza aggiornare le caratteristiche`);\n\n      // Mantieni la bobina selezionata\n      setSelectedBobinaId(bobina.id_bobina);\n\n      onSuccess(`Bobina incompatibile ${getBobinaNumber(bobina.id_bobina)} selezionata per il cavo ${cavo.id_cavo}`);\n      handleCloseIncompatibleReelDialog();\n    } catch (error) {\n      console.error('Errore durante la selezione della bobina incompatibile:', error);\n      onError('Errore durante la selezione della bobina incompatibile: ' + (error.detail || error.message || 'Errore sconosciuto'));\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  // Funzione per estrarre il numero della bobina dall'ID completo\n  const getBobinaNumber = (idBobina) => {\n    if (idBobina === 'BOBINA_VUOTA') return 'BOBINA VUOTA';\n\n    // L'ID completo è nel formato C{id_cantiere}_B{numero_bobina}\n    if (idBobina && idBobina.includes('_B')) {\n      return idBobina.split('_B')[1];\n    }\n    return idBobina;\n  };\n\n  // Renderizza il form per la selezione del cavo\n  const renderCavoSelectionForm = () => (\n    <Box>\n      <Typography variant=\"subtitle2\" sx={{ mb: 1, fontWeight: 'bold' }}>\n        Seleziona un cavo posato\n      </Typography>\n\n      {/* Ricerca per ID - Versione compatta con dettagli cavo selezionato */}\n      <Paper sx={{ p: 1.5, mb: 2, width: '100%' }}>\n        <Box sx={{ display: 'flex', alignItems: 'center', width: '100%' }}>\n          <Typography variant=\"subtitle2\" sx={{ mr: 1, minWidth: '80px' }}>\n            Cerca cavo\n          </Typography>\n          <TextField\n            size=\"small\"\n            label=\"ID Cavo\"\n            variant=\"outlined\"\n            value={cavoIdInput}\n            onChange={(e) => setCavoIdInput(e.target.value)}\n            placeholder=\"Inserisci l'ID del cavo\"\n            sx={{ flexGrow: 0, width: '200px', mr: 1 }}\n          />\n          <Button\n            variant=\"contained\"\n            color=\"primary\"\n            onClick={handleSearchCavoById}\n            disabled={caviLoading || !cavoIdInput.trim()}\n            startIcon={caviLoading ? <CircularProgress size={16} /> : <SearchIcon fontSize=\"small\" />}\n            size=\"small\"\n            sx={{ minWidth: '80px', height: '36px', mr: 2 }}\n          >\n            CERCA\n          </Button>\n\n          {/* Dettagli cavo selezionato in riga singola */}\n          {selectedCavo && (\n            <Box sx={{ display: 'flex', alignItems: 'center', flexGrow: 1, flexWrap: 'nowrap', overflow: 'hidden', ml: 4 }}>\n              <Box sx={{ display: 'flex', alignItems: 'center', mr: 2 }}>\n                <Typography variant=\"subtitle2\" sx={{ fontWeight: 'bold', whiteSpace: 'nowrap', mr: 1, fontSize: '0.95rem' }}>\n                  Cavo: <span style={{ color: '#1976d2' }}>{selectedCavo.id_cavo}</span>\n                </Typography>\n                <Divider orientation=\"vertical\" flexItem sx={{ mx: 1.5 }} />\n                <Box sx={{ display: 'flex', alignItems: 'center', gap: 3, flexWrap: 'nowrap', overflow: 'hidden' }}>\n                  <Box sx={{ display: 'flex', alignItems: 'center', whiteSpace: 'nowrap' }}>\n                    <Typography variant=\"body2\" sx={{ fontWeight: 'medium', fontSize: '0.9rem', mr: 0.5 }}>Tipo:</Typography>\n                    <Typography variant=\"body2\" sx={{ fontSize: '0.9rem' }}>{selectedCavo.tipologia || 'N/A'}</Typography>\n                  </Box>\n                  <Box sx={{ display: 'flex', alignItems: 'center', whiteSpace: 'nowrap' }}>\n                    <Typography variant=\"body2\" sx={{ fontWeight: 'medium', fontSize: '0.9rem', mr: 0.5 }}>Form:</Typography>\n                    <Typography variant=\"body2\" sx={{ fontSize: '0.9rem' }}>{selectedCavo.sezione || 'N/A'}</Typography>\n                  </Box>\n                  <Box sx={{ display: 'flex', alignItems: 'center', whiteSpace: 'nowrap' }}>\n                    <Typography variant=\"body2\" sx={{ fontWeight: 'medium', fontSize: '0.9rem', mr: 0.5 }}>Metri:</Typography>\n                    <Typography variant=\"body2\" sx={{ fontSize: '0.9rem' }}>{selectedCavo.metratura_reale || 'N/A'} m</Typography>\n                  </Box>\n                  <Box sx={{ display: 'flex', alignItems: 'center', whiteSpace: 'nowrap' }}>\n                    <Typography variant=\"body2\" sx={{ fontWeight: 'medium', fontSize: '0.9rem', mr: 0.5 }}>Stato:</Typography>\n                    <Chip\n                      size=\"small\"\n                      label={selectedCavo.stato_installazione || 'N/D'}\n                      color=\"success\"\n                      sx={{ height: '22px', '& .MuiChip-label': { px: 1, py: 0, fontSize: '0.85rem' } }}\n                    />\n                  </Box>\n                </Box>\n              </Box>\n\n              {/* Dettagli bobina attuale */}\n              {selectedCavo.id_bobina && (\n                <>\n                  <Divider orientation=\"vertical\" flexItem sx={{ mx: 2 }} />\n                  <Box sx={{ display: 'flex', alignItems: 'center' }}>\n                    <Typography variant=\"subtitle2\" sx={{ fontWeight: 'bold', whiteSpace: 'nowrap', mr: 1, fontSize: '0.95rem', color: '#2e7d32' }}>\n                      Bobina attuale: <span>{selectedCavo.id_bobina === 'BOBINA_VUOTA' ? 'VUOTA' : getBobinaNumber(selectedCavo.id_bobina)}</span>\n                    </Typography>\n                    {(() => {\n                      if (selectedCavo.id_bobina === 'BOBINA_VUOTA') {\n                        return (\n                          <Box sx={{ display: 'flex', alignItems: 'center', whiteSpace: 'nowrap' }}>\n                            <Typography variant=\"body2\" sx={{ fontSize: '0.9rem', color: 'text.secondary', fontStyle: 'italic' }}>\n                              (Cavo posato senza bobina specifica)\n                            </Typography>\n                          </Box>\n                        );\n                      }\n\n                      const bobina = bobine.find(b => b.id_bobina === selectedCavo.id_bobina);\n                      return bobina ? (\n                        <Box sx={{ display: 'flex', alignItems: 'center', gap: 3, flexWrap: 'nowrap', overflow: 'hidden' }}>\n                          <Box sx={{ display: 'flex', alignItems: 'center', whiteSpace: 'nowrap' }}>\n                            <Typography variant=\"body2\" sx={{ fontWeight: 'medium', fontSize: '0.9rem', mr: 0.5 }}>Residui:</Typography>\n                            <Typography variant=\"body2\" sx={{ fontSize: '0.9rem', color: 'success.main', fontWeight: 'bold' }}>\n                              {bobina.metri_residui || 0} m\n                            </Typography>\n                          </Box>\n                          <Box sx={{ display: 'flex', alignItems: 'center', whiteSpace: 'nowrap' }}>\n                            <Typography variant=\"body2\" sx={{ fontWeight: 'medium', fontSize: '0.9rem', mr: 0.5 }}>Stato:</Typography>\n                            <Chip\n                              size=\"small\"\n                              label={bobina.stato_bobina || 'N/D'}\n                              color={bobina.stato_bobina === 'Disponibile' ? 'success' : 'warning'}\n                              sx={{ height: '22px', '& .MuiChip-label': { px: 1, py: 0, fontSize: '0.85rem' } }}\n                            />\n                          </Box>\n                        </Box>\n                      ) : null;\n                    })()}\n                  </Box>\n                </>\n              )}\n\n              {/* Dettagli bobina selezionata (se diversa da quella attuale) */}\n              {selectedBobinaId && selectedBobinaId !== selectedCavo?.id_bobina && (\n                <>\n                  <Divider orientation=\"vertical\" flexItem sx={{ mx: 2 }} />\n                  <Box sx={{ display: 'flex', alignItems: 'center' }}>\n                    <Typography variant=\"subtitle2\" sx={{ fontWeight: 'bold', whiteSpace: 'nowrap', mr: 1, fontSize: '0.95rem', color: '#1976d2' }}>\n                      Bobina selezionata: <span>{selectedBobinaId === 'BOBINA_VUOTA' ? 'VUOTA' : getBobinaNumber(selectedBobinaId)}</span>\n                    </Typography>\n                    {(() => {\n                      if (selectedBobinaId === 'BOBINA_VUOTA') {\n                        return (\n                          <Box sx={{ display: 'flex', alignItems: 'center', whiteSpace: 'nowrap' }}>\n                            <Typography variant=\"body2\" sx={{ fontSize: '0.9rem', color: 'text.secondary', fontStyle: 'italic' }}>\n                              (Rimozione bobina attuale)\n                            </Typography>\n                          </Box>\n                        );\n                      }\n\n                      const bobina = bobine.find(b => b.id_bobina === selectedBobinaId);\n                      if (!bobina) return null;\n\n                      // Verifica compatibilità\n                      const isCompatible = selectedCavo &&\n                        String(bobina.tipologia || '').trim() === String(selectedCavo.tipologia || '').trim() &&\n                        String(bobina.sezione || '').trim() === String(selectedCavo.sezione || '').trim();\n\n                      return (\n                        <Box sx={{ display: 'flex', alignItems: 'center', gap: 3, flexWrap: 'nowrap', overflow: 'hidden' }}>\n                          <Box sx={{ display: 'flex', alignItems: 'center', whiteSpace: 'nowrap' }}>\n                            <Typography variant=\"body2\" sx={{ fontWeight: 'medium', fontSize: '0.9rem', mr: 0.5 }}>Tipo:</Typography>\n                            <Typography variant=\"body2\" sx={{ fontSize: '0.9rem', color: isCompatible ? 'text.primary' : 'error.main' }}>\n                              {bobina.tipologia || 'N/A'}\n                            </Typography>\n                          </Box>\n                          <Box sx={{ display: 'flex', alignItems: 'center', whiteSpace: 'nowrap' }}>\n                            <Typography variant=\"body2\" sx={{ fontWeight: 'medium', fontSize: '0.9rem', mr: 0.5 }}>Form:</Typography>\n                            <Typography variant=\"body2\" sx={{ fontSize: '0.9rem', color: isCompatible ? 'text.primary' : 'error.main' }}>\n                              {bobina.sezione || 'N/A'}\n                            </Typography>\n                          </Box>\n                          <Box sx={{ display: 'flex', alignItems: 'center', whiteSpace: 'nowrap' }}>\n                            <Typography variant=\"body2\" sx={{ fontWeight: 'medium', fontSize: '0.9rem', mr: 0.5 }}>Residui:</Typography>\n                            <Typography variant=\"body2\" sx={{ fontSize: '0.9rem', color: 'primary.main', fontWeight: 'bold' }}>\n                              {bobina.metri_residui || 0} m\n                            </Typography>\n                          </Box>\n                          <Box sx={{ display: 'flex', alignItems: 'center', whiteSpace: 'nowrap' }}>\n                            <Typography variant=\"body2\" sx={{ fontWeight: 'medium', fontSize: '0.9rem', mr: 0.5 }}>Stato:</Typography>\n                            <Chip\n                              size=\"small\"\n                              label={bobina.stato_bobina || 'N/D'}\n                              color={bobina.stato_bobina === 'Disponibile' ? 'success' : 'warning'}\n                              sx={{ height: '22px', '& .MuiChip-label': { px: 1, py: 0, fontSize: '0.85rem' } }}\n                            />\n                          </Box>\n                          {!isCompatible && (\n                            <Box sx={{ display: 'flex', alignItems: 'center', whiteSpace: 'nowrap' }}>\n                              <Chip\n                                size=\"small\"\n                                label=\"Non compatibile\"\n                                color=\"error\"\n                                variant=\"outlined\"\n                                sx={{ height: '22px', '& .MuiChip-label': { px: 1, py: 0, fontSize: '0.85rem' } }}\n                              />\n                            </Box>\n                          )}\n                        </Box>\n                      );\n                    })()}\n                  </Box>\n                </>\n              )}\n            </Box>\n          )}\n        </Box>\n      </Paper>\n\n      {/* Lista cavi - versione compatta */}\n      <Paper sx={{ p: 1.5, width: '100%' }}>\n        <Typography variant=\"subtitle2\" sx={{ mb: 1 }}>\n          Seleziona dalla lista\n        </Typography>\n\n        {caviLoading ? (\n          <Box sx={{ display: 'flex', justifyContent: 'center', my: 2 }}>\n            <CircularProgress size={24} />\n          </Box>\n        ) : cavi.length === 0 ? (\n          <Alert severity=\"info\" sx={{ py: 0.5 }}>\n            <Typography variant=\"caption\">Non ci sono cavi posati disponibili.</Typography>\n          </Alert>\n        ) : (\n          <TableContainer component={Paper} variant=\"outlined\" sx={{ maxHeight: '300px', overflow: 'auto', width: '100%' }}>\n            <Table size=\"small\" stickyHeader>\n              <TableHead>\n                <TableRow sx={{ '& th': { fontWeight: 'bold', py: 1, bgcolor: '#f5f5f5' } }}>\n                  <TableCell>ID Cavo</TableCell>\n                  <TableCell>Tipologia</TableCell>\n                  <TableCell>Formazione</TableCell>\n                  <TableCell>Metri</TableCell>\n                  <TableCell>Bobina</TableCell>\n                  <TableCell>Stato</TableCell>\n                  <TableCell align=\"center\" sx={{ width: '40px' }}>Info</TableCell>\n                </TableRow>\n              </TableHead>\n              <TableBody>\n                {cavi.map((cavo) => (\n                  <TableRow\n                    key={cavo.id_cavo}\n                    hover\n                    onClick={() => handleSelectCavo(cavo)}\n                    sx={{\n                      cursor: 'pointer',\n                      '&:hover': { bgcolor: '#f1f8e9' },\n                      '& td': { py: 0.5 }\n                    }}\n                  >\n                    <TableCell sx={{ fontWeight: 'medium' }}>{cavo.id_cavo}</TableCell>\n                    <TableCell>{cavo.tipologia || 'N/A'}</TableCell>\n                    <TableCell>{cavo.sezione || 'N/A'}</TableCell>\n                    <TableCell>{cavo.metratura_reale || 'N/A'} m</TableCell>\n                    <TableCell>\n                      {cavo.id_bobina ? (cavo.id_bobina === 'BOBINA_VUOTA' ? 'VUOTA' : getBobinaNumber(cavo.id_bobina)) : 'VUOTA'}\n                    </TableCell>\n                    <TableCell>\n                      <Chip\n                        size=\"small\"\n                        label=\"Installato\"\n                        color=\"success\"\n                        sx={{ height: '20px', '& .MuiChip-label': { px: 1, py: 0, fontSize: '0.7rem' } }}\n                      />\n                    </TableCell>\n                    <TableCell align=\"center\">\n                      <IconButton\n                        size=\"small\"\n                        onClick={(e) => {\n                          e.stopPropagation();\n                          setSelectedCavo(cavo);\n                          setShowCavoDetailsDialog(true);\n                        }}\n                      >\n                        <InfoIcon fontSize=\"small\" />\n                      </IconButton>\n                    </TableCell>\n                  </TableRow>\n                ))}\n              </TableBody>\n            </Table>\n          </TableContainer>\n        )}\n      </Paper>\n    </Box>\n  );\n\n\n\n  // Renderizza le opzioni di modifica\n  const renderModificaOptions = () => {\n    if (!selectedCavo) return null;\n\n    return (\n      <Paper sx={{ p: 3, mb: 3 }}>\n        <Typography variant=\"h6\" gutterBottom>\n          Opzioni di modifica\n        </Typography>\n\n        <RadioGroup\n          value={selectedOption}\n          onChange={handleOptionChange}\n        >\n          <FormControlLabel\n            value=\"assegnaNuova\"\n            control={<Radio />}\n            label=\"Assegna nuova bobina\"\n          />\n          <FormControlLabel\n            value=\"rimuoviBobina\"\n            control={<Radio />}\n            label=\"Rimuovi bobina attuale\"\n          />\n          <FormControlLabel\n            value=\"annullaInstallazione\"\n            control={<Radio />}\n            label=\"Annulla installazione\"\n          />\n        </RadioGroup>\n\n        {selectedOption === 'assegnaNuova' && renderBobineSelection()}\n\n        {selectedOption === 'rimuoviBobina' && (\n          <Alert severity=\"info\" sx={{ mt: 2 }}>\n            Questa operazione rimuoverà l'associazione con la bobina attuale, assegnando una \"BOBINA_VUOTA\" al cavo.\n            Il cavo rimarrà nello stato posato e i metri posati rimarranno invariati.\n            La bobina attuale (se presente) riavrà i suoi metri restituiti.\n          </Alert>\n        )}\n\n        {selectedOption === 'annullaInstallazione' && (\n          <Alert severity=\"warning\" sx={{ mt: 2 }}>\n            <Typography variant=\"body2\" fontWeight=\"bold\">\n              ATTENZIONE: Questa operazione annullerà completamente l'installazione del cavo.\n            </Typography>\n            <Typography variant=\"body2\">\n              - Il cavo tornerà allo stato \"Da installare\"<br />\n              - La metratura reale sarà azzerata<br />\n              - L'associazione con la bobina sarà rimossa<br />\n              - I metri posati saranno restituiti alla bobina originale (se presente)\n            </Typography>\n          </Alert>\n        )}\n\n        <Box sx={{ mt: 3, display: 'flex', justifyContent: 'flex-end', gap: 2 }}>\n          <Button\n            variant=\"outlined\"\n            color=\"secondary\"\n            startIcon={<CancelIcon />}\n            onClick={handleCloseForm}\n            disabled={loading}\n          >\n            Annulla operazione\n          </Button>\n          <Button\n            variant=\"contained\"\n            color=\"primary\"\n            startIcon={<SaveIcon />}\n            onClick={handleSave}\n            disabled={loading || !selectedOption || (selectedOption === 'assegnaNuova' && !selectedBobinaId)}\n          >\n            Salva modifiche\n          </Button>\n        </Box>\n      </Paper>\n    );\n  };\n\n  // Renderizza la selezione delle bobine\n  const renderBobineSelection = () => {\n    if (bobineLoading) {\n      return (\n        <Box sx={{ display: 'flex', justifyContent: 'center', mt: 2 }}>\n          <CircularProgress />\n        </Box>\n      );\n    }\n\n    // Filtra le bobine in base al testo di ricerca\n    const bobineFiltrate = bobine.filter(bobina => {\n      const searchLower = bobinaSearchText.toLowerCase();\n      return !bobinaSearchText ||\n        getBobinaNumber(bobina.id_bobina).toLowerCase().includes(searchLower) ||\n        String(bobina.tipologia || '').toLowerCase().includes(searchLower) ||\n        String(bobina.sezione || '').toLowerCase().includes(searchLower);\n    });\n\n    // Separa le bobine compatibili e non compatibili\n    const bobineCompatibili = selectedCavo\n      ? bobineFiltrate.filter(bobina =>\n          String(bobina.tipologia || '').trim() === String(selectedCavo.tipologia || '').trim() &&\n          String(bobina.sezione || '').trim() === String(selectedCavo.sezione || '').trim() &&\n          (bobina.stato_bobina === 'Disponibile' || bobina.stato_bobina === 'In uso'))\n      : [];\n\n    const bobineNonCompatibili = selectedCavo\n      ? bobineFiltrate.filter(bobina =>\n          !(String(bobina.tipologia || '').trim() === String(selectedCavo.tipologia || '').trim() &&\n            String(bobina.sezione || '').trim() === String(selectedCavo.sezione || '').trim()) &&\n          (bobina.stato_bobina === 'Disponibile' || bobina.stato_bobina === 'In uso'))\n      : [];\n\n    // Ordina per metri residui (decrescente)\n    bobineCompatibili.sort((a, b) => b.metri_residui - a.metri_residui);\n    bobineNonCompatibili.sort((a, b) => b.metri_residui - a.metri_residui);\n\n    return (\n      <Box sx={{ mt: 2 }}>\n        {/* Campo di ricerca rapida */}\n        <Box sx={{ mb: 2 }}>\n          <TextField\n            fullWidth\n            label=\"Ricerca bobina (ID, tipologia, formazione)\"\n            value={bobinaSearchText}\n            onChange={(e) => setBobinaSearchText(e.target.value)}\n            InputProps={{\n              startAdornment: (\n                <InputAdornment position=\"start\">\n                  <SearchIcon />\n                </InputAdornment>\n              )\n            }}\n            sx={{ mb: 2 }}\n          />\n        </Box>\n\n\n\n        {/* Bobina selezionata */}\n        {selectedBobinaId && (\n          <Alert severity=\"success\" sx={{ mb: 2 }}>\n            Bobina selezionata: <strong>{getBobinaNumber(selectedBobinaId)}</strong>\n          </Alert>\n        )}\n\n        {/* Griglia per le due liste di bobine */}\n        <Grid container spacing={2}>\n          {/* Colonna sinistra: Bobine compatibili */}\n          <Grid item xs={12} md={6}>\n            <Paper variant=\"outlined\" sx={{ p: 2, height: '100%' }}>\n              <Typography variant=\"subtitle2\" sx={{ fontWeight: 'bold', mb: 1 }}>\n                ELENCO BOBINE COMPATIBILI\n              </Typography>\n\n              {bobineCompatibili.length > 0 ? (\n                <>\n                  <Box sx={{ display: 'flex', alignItems: 'center', width: '100%', py: 0.8, px: 1.8, bgcolor: '#f5f5f5', borderRadius: 1, mb: 1 }}>\n                    <Box sx={{ width: '60px', mr: 2 }}>\n                      <Typography variant=\"caption\" sx={{ fontWeight: 'bold', fontSize: '0.85rem' }}>ID</Typography>\n                    </Box>\n                    <Box sx={{ width: '120px', mr: 2 }}>\n                      <Typography variant=\"caption\" sx={{ fontWeight: 'bold', fontSize: '0.85rem' }}>Tipo</Typography>\n                    </Box>\n                    <Box sx={{ width: '100px', mr: 2 }}>\n                      <Typography variant=\"caption\" sx={{ fontWeight: 'bold', fontSize: '0.85rem' }}>Form.</Typography>\n                    </Box>\n                    <Box sx={{ width: '100px', mr: 2 }}>\n                      <Typography variant=\"caption\" sx={{ fontWeight: 'bold', fontSize: '0.85rem' }}>Residui</Typography>\n                    </Box>\n                    <Box sx={{ flexGrow: 0 }}>\n                      <Typography variant=\"caption\" sx={{ fontWeight: 'bold', fontSize: '0.85rem' }}>Stato</Typography>\n                    </Box>\n                  </Box>\n                  <List sx={{ maxHeight: bobineCompatibili.length > 6 ? '300px' : 'auto', overflowY: bobineCompatibili.length > 6 ? 'auto' : 'visible', overflowX: 'hidden', bgcolor: 'background.paper' }}>\n                    {bobineCompatibili.map((bobina) => (\n                      <ListItem\n                        key={bobina.id_bobina}\n                        disablePadding\n                        secondaryAction={\n                          <IconButton\n                            edge=\"end\"\n                            size=\"small\"\n                            onClick={() => handleSelectBobina(bobina.id_bobina)}\n                          >\n                            <AddCircleOutlineIcon color=\"primary\" />\n                          </IconButton>\n                        }\n                        sx={{\n                          bgcolor: selectedBobinaId === bobina.id_bobina ? 'rgba(76, 175, 80, 0.12)' : 'inherit',\n                          borderRadius: '4px',\n                          mb: 0.5,\n                          border: selectedBobinaId === bobina.id_bobina ? '1px solid #4caf50' : 'none',\n                        }}\n                      >\n                        <ListItemButton\n                          dense\n                          onClick={() => handleSelectBobina(bobina.id_bobina)}\n                        >\n                          <Box sx={{ display: 'flex', alignItems: 'center', width: '100%', py: 0.8 }}>\n                            <Box sx={{ width: '60px', mr: 2 }}>\n                              <Typography variant=\"body2\" sx={{ fontWeight: 'bold', fontSize: '0.9rem' }}>\n                                {getBobinaNumber(bobina.id_bobina)}\n                              </Typography>\n                            </Box>\n                            <Box sx={{ width: '120px', mr: 2 }}>\n                              <Typography variant=\"body2\" sx={{ fontSize: '0.85rem' }}>\n                                {bobina.tipologia || 'N/A'}\n                              </Typography>\n                            </Box>\n                            <Box sx={{ width: '100px', mr: 2 }}>\n                              <Typography variant=\"body2\" sx={{ fontSize: '0.85rem' }}>\n                                {bobina.sezione || 'N/A'}\n                              </Typography>\n                            </Box>\n                            <Box sx={{ width: '100px', mr: 2 }}>\n                              <Typography variant=\"body2\" sx={{ fontWeight: 'bold', fontSize: '0.85rem', color: 'success.main' }}>\n                                {bobina.metri_residui || 0} m\n                              </Typography>\n                            </Box>\n                            <Box sx={{ flexGrow: 0 }}>\n                              <Chip\n                                size=\"small\"\n                                label={bobina.stato_bobina || 'N/D'}\n                                color={getReelStateColor(bobina.stato_bobina)}\n                                variant=\"outlined\"\n                                sx={{ height: 22, fontSize: '0.8rem', '& .MuiChip-label': { px: 1, py: 0 } }}\n                              />\n                            </Box>\n                          </Box>\n                        </ListItemButton>\n                      </ListItem>\n                    ))}\n                  </List>\n                </>\n              ) : (\n                <Alert severity=\"info\" sx={{ mt: 1 }}>\n                  Nessuna bobina compatibile disponibile. Puoi usare BOBINA VUOTA o selezionare una bobina non compatibile.\n                </Alert>\n              )}\n            </Paper>\n          </Grid>\n\n          {/* Colonna destra: Bobine non compatibili */}\n          <Grid item xs={12} md={6}>\n            <Paper variant=\"outlined\" sx={{ p: 2, height: '100%' }}>\n              <Typography variant=\"subtitle2\" sx={{ fontWeight: 'bold', mb: 1 }}>\n                ELENCO BOBINE NON COMPATIBILI\n              </Typography>\n\n              {bobineNonCompatibili.length > 0 ? (\n                <>\n                  <Box sx={{ display: 'flex', alignItems: 'center', width: '100%', py: 0.8, px: 1.8, bgcolor: '#f5f5f5', borderRadius: 1, mb: 1 }}>\n                    <Box sx={{ width: '60px', mr: 2 }}>\n                      <Typography variant=\"caption\" sx={{ fontWeight: 'bold', fontSize: '0.85rem' }}>ID</Typography>\n                    </Box>\n                    <Box sx={{ width: '120px', mr: 2 }}>\n                      <Typography variant=\"caption\" sx={{ fontWeight: 'bold', fontSize: '0.85rem' }}>Tipo</Typography>\n                    </Box>\n                    <Box sx={{ width: '100px', mr: 2 }}>\n                      <Typography variant=\"caption\" sx={{ fontWeight: 'bold', fontSize: '0.85rem' }}>Form.</Typography>\n                    </Box>\n                    <Box sx={{ width: '100px', mr: 2 }}>\n                      <Typography variant=\"caption\" sx={{ fontWeight: 'bold', fontSize: '0.85rem' }}>Residui</Typography>\n                    </Box>\n                    <Box sx={{ flexGrow: 0 }}>\n                      <Typography variant=\"caption\" sx={{ fontWeight: 'bold', fontSize: '0.85rem' }}>Stato</Typography>\n                    </Box>\n                  </Box>\n                  <List sx={{ maxHeight: bobineNonCompatibili.length > 6 ? '300px' : 'auto', overflowY: bobineNonCompatibili.length > 6 ? 'auto' : 'visible', overflowX: 'hidden', bgcolor: 'background.paper' }}>\n                    {bobineNonCompatibili.map((bobina) => (\n                      <ListItem\n                        key={bobina.id_bobina}\n                        disablePadding\n                        secondaryAction={\n                          <IconButton\n                            edge=\"end\"\n                            size=\"small\"\n                            onClick={() => handleSelectBobina(bobina.id_bobina)}\n                          >\n                            <AddCircleOutlineIcon color=\"primary\" />\n                          </IconButton>\n                        }\n                        sx={{\n                          bgcolor: selectedBobinaId === bobina.id_bobina ? 'rgba(76, 175, 80, 0.12)' : 'inherit',\n                          borderRadius: '4px',\n                          mb: 0.5,\n                          border: selectedBobinaId === bobina.id_bobina ? '1px solid #4caf50' : 'none',\n                        }}\n                      >\n                        <ListItemButton\n                          dense\n                          onClick={() => handleSelectBobina(bobina.id_bobina)}\n                        >\n                          <Box sx={{ display: 'flex', alignItems: 'center', width: '100%', py: 0.8 }}>\n                            <Box sx={{ width: '60px', mr: 2 }}>\n                              <Typography variant=\"body2\" sx={{ fontWeight: 'bold', fontSize: '0.9rem' }}>\n                                {getBobinaNumber(bobina.id_bobina)}\n                              </Typography>\n                            </Box>\n                            <Box sx={{ width: '120px', mr: 2 }}>\n                              <Typography variant=\"body2\" sx={{ fontSize: '0.85rem' }}>\n                                {bobina.tipologia || 'N/A'}\n                              </Typography>\n                            </Box>\n                            <Box sx={{ width: '100px', mr: 2 }}>\n                              <Typography variant=\"body2\" sx={{ fontSize: '0.85rem' }}>\n                                {bobina.sezione || 'N/A'}\n                              </Typography>\n                            </Box>\n                            <Box sx={{ width: '100px', mr: 2 }}>\n                              <Typography variant=\"body2\" sx={{ fontWeight: 'bold', fontSize: '0.85rem', color: 'success.main' }}>\n                                {bobina.metri_residui || 0} m\n                              </Typography>\n                            </Box>\n                            <Box sx={{ display: 'flex', gap: 1 }}>\n                              <Chip\n                                size=\"small\"\n                                label={bobina.stato_bobina || 'N/D'}\n                                color={getReelStateColor(bobina.stato_bobina)}\n                                variant=\"outlined\"\n                                sx={{ height: 22, fontSize: '0.8rem', '& .MuiChip-label': { px: 1, py: 0 } }}\n                              />\n                              <Chip\n                                size=\"small\"\n                                label=\"Non comp.\"\n                                color=\"warning\"\n                                variant=\"outlined\"\n                                sx={{ height: 22, fontSize: '0.8rem', '& .MuiChip-label': { px: 1, py: 0 } }}\n                              />\n                            </Box>\n                          </Box>\n                        </ListItemButton>\n                      </ListItem>\n                    ))}\n                  </List>\n                </>\n              ) : (\n                <Alert severity=\"info\" sx={{ mt: 1 }}>\n                  Nessuna bobina non compatibile disponibile con i filtri attuali.\n                </Alert>\n              )}\n            </Paper>\n          </Grid>\n        </Grid>\n\n        {/* Messaggio se non ci sono bobine */}\n        {bobineCompatibili.length === 0 && bobineNonCompatibili.length === 0 && (\n          <Alert severity=\"info\" sx={{ mt: 2 }}>\n            {bobinaSearchText ? 'Nessuna bobina trovata con i criteri di ricerca specificati.' : 'Non ci sono bobine disponibili.'}\n          </Alert>\n        )}\n      </Box>\n    );\n  };\n\n  return (\n    <Box>\n      {/* Form per la selezione del cavo */}\n      {renderCavoSelectionForm()}\n\n      {/* Opzioni di modifica */}\n      {renderModificaOptions()}\n\n      {/* Dialog per la visualizzazione dei dettagli completi del cavo */}\n      <Dialog\n        open={showCavoDetailsDialog}\n        onClose={() => setShowCavoDetailsDialog(false)}\n        maxWidth=\"md\"\n        fullWidth\n      >\n        <DialogTitle>Dettagli completi del cavo</DialogTitle>\n        <DialogContent>\n          {selectedCavo && <CavoDetailsView cavo={selectedCavo} />}\n        </DialogContent>\n        <DialogActions>\n          <Button onClick={() => setShowCavoDetailsDialog(false)}>Chiudi</Button>\n        </DialogActions>\n      </Dialog>\n\n      {/* Dialog di conferma */}\n      <Dialog\n        open={showConfirmDialog}\n        onClose={() => setShowConfirmDialog(false)}\n      >\n        <DialogTitle>Conferma operazione</DialogTitle>\n        <DialogContent>\n          <Typography>{confirmDialogMessage}</Typography>\n        </DialogContent>\n        <DialogActions>\n          <Button\n            onClick={() => setShowConfirmDialog(false)}\n            color=\"primary\"\n          >\n            Annulla\n          </Button>\n          <Button\n            onClick={() => {\n              setShowConfirmDialog(false);\n              if (confirmDialogAction) confirmDialogAction();\n            }}\n            color=\"primary\"\n            variant=\"contained\"\n            autoFocus\n          >\n            Conferma\n          </Button>\n        </DialogActions>\n      </Dialog>\n\n      {/* Dialog per bobine incompatibili */}\n      <IncompatibleReelDialog\n        open={showIncompatibleReelDialog}\n        onClose={handleCloseIncompatibleReelDialog}\n        cavo={incompatibleReelData.cavo}\n        bobina={incompatibleReelData.bobina}\n        onUpdateCavo={handleUpdateCavoForCompatibility}\n        onSelectAnotherReel={handleCloseIncompatibleReelDialog}\n        onContinueWithIncompatible={handleContinueWithIncompatible}\n      />\n    </Box>\n  );\n};\n\nexport default ModificaBobinaForm;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAClD,SACEC,GAAG,EACHC,KAAK,EACLC,UAAU,EACVC,SAAS,EACTC,MAAM,EACNC,IAAI,EACJC,WAAW,EACXC,UAAU,EACVC,MAAM,EACNC,QAAQ,EACRC,OAAO,EACPC,KAAK,EACLC,gBAAgB,EAChBC,MAAM,EACNC,WAAW,EACXC,aAAa,EACbC,aAAa,EACbC,KAAK,EACLC,SAAS,EACTC,SAAS,EACTC,cAAc,EACdC,SAAS,EACTC,QAAQ,EACRC,KAAK,EACLC,UAAU,EACVC,gBAAgB,EAChBC,UAAU,EACVC,cAAc,EACdC,IAAI,EACJC,QAAQ,EACRC,YAAY,EACZC,cAAc,EACdC,uBAAuB,EACvBC,IAAI,QACC,eAAe;AACtB,SACEC,MAAM,IAAIC,UAAU,EACpBC,IAAI,IAAIC,QAAQ,EAChBC,MAAM,IAAIC,UAAU,EACpBC,OAAO,IAAIC,WAAW,EACtBC,IAAI,IAAIC,QAAQ,EAChBC,gBAAgB,IAAIC,oBAAoB,QACnC,qBAAqB;AAC5B,OAAOC,sBAAsB,MAAM,0BAA0B;AAC7D,SAASC,WAAW,EAAEC,SAAS,QAAQ,kBAAkB;AACzD,OAAOC,WAAW,MAAM,4BAA4B;AACpD,OAAOC,gBAAgB,MAAM,iCAAiC;AAC9D,OAAOC,eAAe,MAAM,mBAAmB;AAC/C,SACEC,YAAY,EACZC,WAAW,EACXC,mBAAmB,EACnBC,kBAAkB,EAClBC,cAAc,EACdC,YAAY,EACZC,gBAAgB,EAChBC,kBAAkB,EAClBC,iBAAiB,QACZ,wBAAwB;;AAE/B;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAPA,SAAAC,MAAA,IAAAC,OAAA,EAAAC,QAAA,IAAAC,SAAA;AAQA,MAAMC,kBAAkB,GAAGA,CAAC;EAAEC,UAAU;EAAEC,SAAS;EAAEC;AAAQ,CAAC,KAAK;EAAAC,EAAA;EACjE,MAAMC,QAAQ,GAAGvB,WAAW,CAAC,CAAC;EAC9B,MAAM;IAAEwB;EAAO,CAAC,GAAGvB,SAAS,CAAC,CAAC;;EAE9B;EACA,MAAM,CAACwB,OAAO,EAAEC,UAAU,CAAC,GAAG3E,QAAQ,CAAC,KAAK,CAAC;EAC7C,MAAM,CAAC4E,WAAW,EAAEC,cAAc,CAAC,GAAG7E,QAAQ,CAAC,KAAK,CAAC;EACrD,MAAM,CAAC8E,aAAa,EAAEC,gBAAgB,CAAC,GAAG/E,QAAQ,CAAC,KAAK,CAAC;EACzD,MAAM,CAACgF,aAAa,EAAEC,gBAAgB,CAAC,GAAGjF,QAAQ,CAAC,EAAE,CAAC;EACtD,MAAM,CAACkF,iBAAiB,EAAEC,oBAAoB,CAAC,GAAGnF,QAAQ,CAAC,KAAK,CAAC;;EAEjE;EACA,MAAM,CAACoF,IAAI,EAAEC,OAAO,CAAC,GAAGrF,QAAQ,CAAC,EAAE,CAAC;EACpC,MAAM,CAACsF,MAAM,EAAEC,SAAS,CAAC,GAAGvF,QAAQ,CAAC,EAAE,CAAC;EACxC,MAAM,CAACwF,gBAAgB,EAAEC,mBAAmB,CAAC,GAAGzF,QAAQ,CAAC,EAAE,CAAC;EAC5D,MAAM,CAAC0F,YAAY,EAAEC,eAAe,CAAC,GAAG3F,QAAQ,CAAC,IAAI,CAAC;EACtD,MAAM,CAAC4F,WAAW,EAAEC,cAAc,CAAC,GAAG7F,QAAQ,CAAC,EAAE,CAAC;EAClD,MAAM,CAAC8F,cAAc,EAAEC,iBAAiB,CAAC,GAAG/F,QAAQ,CAAC,EAAE,CAAC;EACxD,MAAM,CAACgG,gBAAgB,EAAEC,mBAAmB,CAAC,GAAGjG,QAAQ,CAAC,EAAE,CAAC;EAC5D,MAAM,CAACkG,gBAAgB,EAAEC,mBAAmB,CAAC,GAAGnG,QAAQ,CAAC,EAAE,CAAC;;EAE5D;EACA,MAAM,CAACoG,0BAA0B,EAAEC,6BAA6B,CAAC,GAAGrG,QAAQ,CAAC,KAAK,CAAC;EACnF,MAAM,CAACsG,oBAAoB,EAAEC,uBAAuB,CAAC,GAAGvG,QAAQ,CAAC;IAAEwG,IAAI,EAAE,IAAI;IAAEC,MAAM,EAAE;EAAK,CAAC,CAAC;;EAE9F;EACA,MAAM,CAACC,iBAAiB,EAAEC,oBAAoB,CAAC,GAAG3G,QAAQ,CAAC,KAAK,CAAC;EACjE,MAAM,CAAC4G,oBAAoB,EAAEC,uBAAuB,CAAC,GAAG7G,QAAQ,CAAC,EAAE,CAAC;EACpE,MAAM,CAAC8G,mBAAmB,EAAEC,sBAAsB,CAAC,GAAG/G,QAAQ,CAAC,IAAI,CAAC;EACpE,MAAM,CAACgH,qBAAqB,EAAEC,wBAAwB,CAAC,GAAGjH,QAAQ,CAAC,KAAK,CAAC;;EAEzE;EACAC,SAAS,CAAC,MAAM;IACdiH,QAAQ,CAAC,CAAC;EACZ,CAAC,EAAE,CAAC9C,UAAU,CAAC,CAAC;;EAEhB;EACAnE,SAAS,CAAC,MAAM;IACd,IAAIyF,YAAY,EAAE;MAChByB,UAAU,CAAC,CAAC;IACd;EACF,CAAC,EAAE,CAACzB,YAAY,CAAC,CAAC;;EAElB;EACAzF,SAAS,CAAC,MAAM;IACd,IAAIwE,MAAM,IAAIW,IAAI,CAACgC,MAAM,GAAG,CAAC,IAAI,CAAC1B,YAAY,EAAE;MAC9C,MAAM2B,WAAW,GAAGjC,IAAI,CAACkC,IAAI,CAACd,IAAI,IAAIA,IAAI,CAACe,OAAO,KAAK9C,MAAM,CAAC;MAC9D,IAAI4C,WAAW,EAAE;QACfG,OAAO,CAACC,GAAG,CAAC,+BAA+B,EAAEJ,WAAW,CAAC;QACzD1B,eAAe,CAAC0B,WAAW,CAAC;QAC5BxB,cAAc,CAACpB,MAAM,CAAC;MACxB,CAAC,MAAM;QACL+C,OAAO,CAACE,IAAI,CAAC,eAAejD,MAAM,8CAA8C,CAAC;QACjFH,OAAO,CAAC,QAAQG,MAAM,+BAA+B,CAAC;MACxD;IACF;EACF,CAAC,EAAE,CAACA,MAAM,EAAEW,IAAI,EAAEM,YAAY,CAAC,CAAC;;EAEhC;EACA,MAAMwB,QAAQ,GAAG,MAAAA,CAAA,KAAY;IAC3B,IAAI;MACFrC,cAAc,CAAC,IAAI,CAAC;MACpB2C,OAAO,CAACC,GAAG,CAAC,oCAAoCrD,UAAU,KAAK,CAAC;;MAEhE;MACA,MAAMuD,QAAQ,GAAG,MAAMxE,WAAW,CAACyE,OAAO,CAACxD,UAAU,EAAE,IAAI,EAAE,YAAY,CAAC;MAC1EoD,OAAO,CAACC,GAAG,CAAC,YAAYE,QAAQ,CAACP,MAAM,kBAAkB,CAAC;;MAE1D;MACA,MAAMS,cAAc,GAAGF,QAAQ,CAACG,MAAM,CAACtB,IAAI,IACzCuB,UAAU,CAACvB,IAAI,CAACwB,eAAe,CAAC,GAAG,CAAC,IAAIxB,IAAI,CAACyB,mBAAmB,KAAK,YACvE,CAAC;MAED5C,OAAO,CAACwC,cAAc,CAAC;MACvBL,OAAO,CAACC,GAAG,CAAC,YAAYI,cAAc,CAACT,MAAM,iCAAiC,CAAC;IACjF,CAAC,CAAC,OAAOc,KAAK,EAAE;MACdV,OAAO,CAACU,KAAK,CAAC,yCAAyC,EAAEA,KAAK,CAAC;MAC/D5D,OAAO,CAAC,0CAA0C,IAAI4D,KAAK,CAACC,MAAM,IAAID,KAAK,CAACE,OAAO,IAAI,oBAAoB,CAAC,CAAC;IAC/G,CAAC,SAAS;MACRvD,cAAc,CAAC,KAAK,CAAC;IACvB;EACF,CAAC;;EAED;EACA,MAAMsC,UAAU,GAAG,MAAAA,CAAA,KAAY;IAC7B,IAAI,CAACzB,YAAY,EAAE;IAEnB,IAAI;MACFX,gBAAgB,CAAC,IAAI,CAAC;MACtByC,OAAO,CAACC,GAAG,CAAC,sCAAsCrD,UAAU,KAAK,CAAC;;MAElE;MACA,MAAMiE,UAAU,GAAG,MAAMjF,gBAAgB,CAACkF,SAAS,CAAClE,UAAU,CAAC;MAC/DoD,OAAO,CAACC,GAAG,CAAC,YAAYY,UAAU,CAACjB,MAAM,SAAS,CAAC;;MAEnD;MACA,MAAMmB,oBAAoB,GAAGF,UAAU,CAACP,MAAM,CAACrB,MAAM,IACnDA,MAAM,CAAC+B,SAAS,KAAK9C,YAAY,CAAC8C,SAAS,IAC3C/B,MAAM,CAACgC,OAAO,KAAK/C,YAAY,CAAC+C,OAAO,KACtChC,MAAM,CAACiC,YAAY,KAAK,aAAa,IAAIjC,MAAM,CAACiC,YAAY,KAAK,QAAQ,CAC5E,CAAC;MAEDnD,SAAS,CAAC8C,UAAU,CAAC;MACrB5C,mBAAmB,CAAC8C,oBAAoB,CAAC;MACzCf,OAAO,CAACC,GAAG,CAAC,YAAYc,oBAAoB,CAACnB,MAAM,qBAAqB,CAAC;IAC3E,CAAC,CAAC,OAAOc,KAAK,EAAE;MACdV,OAAO,CAACU,KAAK,CAAC,6CAA6C,EAAEA,KAAK,CAAC;MACnE5D,OAAO,CAAC,8CAA8C,IAAI4D,KAAK,CAACC,MAAM,IAAID,KAAK,CAACE,OAAO,IAAI,oBAAoB,CAAC,CAAC;IACnH,CAAC,SAAS;MACRrD,gBAAgB,CAAC,KAAK,CAAC;IACzB;EACF,CAAC;;EAED;EACA,MAAM4D,oBAAoB,GAAG,MAAAA,CAAA,KAAY;IACvC,IAAI,CAAC/C,WAAW,CAACgD,IAAI,CAAC,CAAC,EAAE;MACvBtE,OAAO,CAAC,6BAA6B,CAAC;MACtC;IACF;IAEA,IAAI;MACFK,UAAU,CAAC,IAAI,CAAC;MAChB,MAAM6B,IAAI,GAAG,MAAMrD,WAAW,CAAC0F,WAAW,CAACzE,UAAU,EAAEwB,WAAW,CAACgD,IAAI,CAAC,CAAC,CAAC;;MAE1E;MACA,IAAIb,UAAU,CAACvB,IAAI,CAACwB,eAAe,CAAC,IAAI,CAAC,IAAIxB,IAAI,CAACyB,mBAAmB,KAAK,YAAY,EAAE;QACtF3D,OAAO,CAAC,4CAA4C,CAAC;QACrDK,UAAU,CAAC,KAAK,CAAC;QACjB;MACF;MAEAgB,eAAe,CAACa,IAAI,CAAC;MACrBT,iBAAiB,CAAC,EAAE,CAAC,CAAC,CAAC;MACvBE,mBAAmB,CAAC,EAAE,CAAC,CAAC,CAAC;MACzBd,oBAAoB,CAAC,KAAK,CAAC;IAC7B,CAAC,CAAC,OAAO+C,KAAK,EAAE;MACdV,OAAO,CAACU,KAAK,CAAC,qCAAqC,EAAEA,KAAK,CAAC;MAC3D5D,OAAO,CAAC,sCAAsC,IAAI4D,KAAK,CAACC,MAAM,IAAID,KAAK,CAACE,OAAO,IAAI,oBAAoB,CAAC,CAAC;IAC3G,CAAC,SAAS;MACRzD,UAAU,CAAC,KAAK,CAAC;IACnB;EACF,CAAC;;EAED;EACA,MAAMmE,gBAAgB,GAAItC,IAAI,IAAK;IACjCb,eAAe,CAACa,IAAI,CAAC;IACrBT,iBAAiB,CAAC,EAAE,CAAC,CAAC,CAAC;IACvBE,mBAAmB,CAAC,EAAE,CAAC,CAAC,CAAC;IACzBd,oBAAoB,CAAC,KAAK,CAAC;EAC7B,CAAC;;EAED;EACA,MAAM4D,kBAAkB,GAAIC,KAAK,IAAK;IACpCjD,iBAAiB,CAACiD,KAAK,CAACC,MAAM,CAACC,KAAK,CAAC;IACrCjD,mBAAmB,CAAC,EAAE,CAAC,CAAC,CAAC;EAC3B,CAAC;;EAED;EACA,MAAMkD,kBAAkB,GAAIC,QAAQ,IAAK;IACvC5B,OAAO,CAACC,GAAG,CAAC,qBAAqB,EAAE2B,QAAQ,CAAC;;IAE5C;IACA,MAAM3C,MAAM,GAAGnB,MAAM,CAACgC,IAAI,CAAC+B,CAAC,IAAIA,CAAC,CAACC,SAAS,KAAKF,QAAQ,CAAC;IAEzD,IAAI3C,MAAM,IAAIf,YAAY,EAAE;MAC1B;MACA,MAAM6D,YAAY,GAChBC,MAAM,CAAC/C,MAAM,CAAC+B,SAAS,IAAI,EAAE,CAAC,CAACI,IAAI,CAAC,CAAC,KAAKY,MAAM,CAAC9D,YAAY,CAAC8C,SAAS,IAAI,EAAE,CAAC,CAACI,IAAI,CAAC,CAAC,IACrFY,MAAM,CAAC/C,MAAM,CAACgC,OAAO,IAAI,EAAE,CAAC,CAACG,IAAI,CAAC,CAAC,KAAKY,MAAM,CAAC9D,YAAY,CAAC+C,OAAO,IAAI,EAAE,CAAC,CAACG,IAAI,CAAC,CAAC;MAEnF,IAAI,CAACW,YAAY,EAAE;QACjB/B,OAAO,CAACC,GAAG,CAAC,mCAAmC,EAAEhB,MAAM,CAAC;QACxDe,OAAO,CAACC,GAAG,CAAC,gBAAgB,EAAE/B,YAAY,CAAC;;QAE3C;QACA+D,gCAAgC,CAAC/D,YAAY,EAAEe,MAAM,CAAC;;QAEtD;QACAR,mBAAmB,CAACmD,QAAQ,CAAC;QAC7B;MACF;IACF;;IAEA;IACAnD,mBAAmB,CAACmD,QAAQ,CAAC;EAC/B,CAAC;;EAID;EACA,MAAMM,UAAU,GAAGA,CAAA,KAAM;IACvB,IAAI,CAAChE,YAAY,EAAE;MACjBpB,OAAO,CAAC,sCAAsC,CAAC;MAC/C;IACF;IAEA,IAAI,CAACwB,cAAc,EAAE;MACnBxB,OAAO,CAAC,0CAA0C,CAAC;MACnD;IACF;;IAEA;IACA,IAAIwB,cAAc,KAAK,cAAc,IAAI,CAACE,gBAAgB,EAAE;MAC1D1B,OAAO,CAAC,yCAAyC,CAAC;MAClD;IACF;;IAEA;IACA,IAAI8D,OAAO,GAAG,EAAE;IAChB,IAAIuB,MAAM,GAAG,IAAI;IAEjB,IAAI7D,cAAc,KAAK,cAAc,EAAE;MACrC,MAAMW,MAAM,GAAGnB,MAAM,CAACgC,IAAI,CAAC+B,CAAC,IAAIA,CAAC,CAACC,SAAS,KAAKtD,gBAAgB,CAAC;MACjEoC,OAAO,GAAG,2CAA2CwB,eAAe,CAAC5D,gBAAgB,CAAC,YAAYN,YAAY,CAAC6B,OAAO,GAAG;MACzHoC,MAAM,GAAGA,CAAA,KAAME,YAAY,CAAC7D,gBAAgB,CAAC;IAC/C,CAAC,MAAM,IAAIF,cAAc,KAAK,eAAe,EAAE;MAC7CsC,OAAO,GAAG,4DAA4D1C,YAAY,CAAC6B,OAAO,GAAG;MAC7FoC,MAAM,GAAGA,CAAA,KAAME,YAAY,CAAC,cAAc,CAAC;IAC7C,CAAC,MAAM,IAAI/D,cAAc,KAAK,sBAAsB,EAAE;MACpDsC,OAAO,GAAG,oEAAoE1C,YAAY,CAAC6B,OAAO,iGAAiG;MACnMoC,MAAM,GAAGA,CAAA,KAAMG,oBAAoB,CAAC,CAAC;IACvC;IAEAjD,uBAAuB,CAACuB,OAAO,CAAC;IAChCrB,sBAAsB,CAAC,MAAM4C,MAAM,CAAC;IACpChD,oBAAoB,CAAC,IAAI,CAAC;EAC5B,CAAC;;EAED;EACA,MAAMkD,YAAY,GAAG,MAAOT,QAAQ,IAAK;IACvC,IAAI;MACFzE,UAAU,CAAC,IAAI,CAAC;MAChB,MAAMxB,WAAW,CAAC0G,YAAY,CAACzF,UAAU,EAAEsB,YAAY,CAAC6B,OAAO,EAAE6B,QAAQ,CAAC;MAE1E/E,SAAS,CAAC,UAAU+E,QAAQ,KAAK,cAAc,GAAG,iBAAiB,GAAG,WAAW,eAAe,CAAC;;MAEjG;MACAzD,eAAe,CAAC,IAAI,CAAC;MACrBI,iBAAiB,CAAC,EAAE,CAAC;MACrBE,mBAAmB,CAAC,EAAE,CAAC;MACvBJ,cAAc,CAAC,EAAE,CAAC;;MAElB;MACAqB,QAAQ,CAAC,CAAC;IACZ,CAAC,CAAC,OAAOgB,KAAK,EAAE;MACdV,OAAO,CAACU,KAAK,CAAC,+CAA+C,EAAEA,KAAK,CAAC;MACrE5D,OAAO,CAAC,gDAAgD,IAAI4D,KAAK,CAACC,MAAM,IAAID,KAAK,CAACE,OAAO,IAAI,oBAAoB,CAAC,CAAC;IACrH,CAAC,SAAS;MACRzD,UAAU,CAAC,KAAK,CAAC;IACnB;EACF,CAAC;;EAED;EACA,MAAMmF,oBAAoB,GAAG,MAAAA,CAAA,KAAY;IACvC,IAAI;MACFnF,UAAU,CAAC,IAAI,CAAC;;MAEhB;MACA,MAAMxB,WAAW,CAAC4G,kBAAkB,CAAC3F,UAAU,EAAEsB,YAAY,CAAC6B,OAAO,CAAC;MAEtElD,SAAS,CAAC,0BAA0BqB,YAAY,CAAC6B,OAAO,yBAAyB,CAAC;;MAElF;MACA5B,eAAe,CAAC,IAAI,CAAC;MACrBI,iBAAiB,CAAC,EAAE,CAAC;MACrBE,mBAAmB,CAAC,EAAE,CAAC;MACvBJ,cAAc,CAAC,EAAE,CAAC;;MAElB;MACAqB,QAAQ,CAAC,CAAC;IACZ,CAAC,CAAC,OAAOgB,KAAK,EAAE;MACdV,OAAO,CAACU,KAAK,CAAC,qDAAqD,EAAEA,KAAK,CAAC;MAC3E5D,OAAO,CAAC,sDAAsD,IAAI4D,KAAK,CAACC,MAAM,IAAID,KAAK,CAACE,OAAO,IAAI,oBAAoB,CAAC,CAAC;IAC3H,CAAC,SAAS;MACRzD,UAAU,CAAC,KAAK,CAAC;IACnB;EACF,CAAC;;EAED;EACA,MAAMqF,eAAe,GAAGA,CAAA,KAAM;IAC5B;IACArE,eAAe,CAAC,IAAI,CAAC;IACrBI,iBAAiB,CAAC,EAAE,CAAC;IACrBE,mBAAmB,CAAC,EAAE,CAAC;IACvBJ,cAAc,CAAC,EAAE,CAAC;IAClBV,oBAAoB,CAAC,KAAK,CAAC;;IAE3B;IACAd,SAAS,CAAC,sBAAsB,CAAC;EACnC,CAAC;;EAED;EACA,MAAM4F,iCAAiC,GAAGA,CAAA,KAAM;IAC9C5D,6BAA6B,CAAC,KAAK,CAAC;IACpCE,uBAAuB,CAAC;MAAEC,IAAI,EAAE,IAAI;MAAEC,MAAM,EAAE;IAAK,CAAC,CAAC;EACvD,CAAC;;EAED;EACA,MAAMgD,gCAAgC,GAAG,MAAAA,CAAOjD,IAAI,GAAG,IAAI,EAAEC,MAAM,GAAG,IAAI,KAAK;IAC7E;IACA,IAAI,CAACD,IAAI,IAAI,CAACC,MAAM,EAAE;MACpB,MAAMyD,UAAU,GAAG5D,oBAAoB;MACvCE,IAAI,GAAG0D,UAAU,CAAC1D,IAAI;MACtBC,MAAM,GAAGyD,UAAU,CAACzD,MAAM;IAC5B;IAEA,IAAI,CAACD,IAAI,IAAI,CAACC,MAAM,EAAE;MACpBe,OAAO,CAACU,KAAK,CAAC,8CAA8C,EAAE;QAAE1B,IAAI;QAAEC;MAAO,CAAC,CAAC;MAC/EnC,OAAO,CAAC,6CAA6C,CAAC;MACtD;IACF;IAEA,IAAI;MACFK,UAAU,CAAC,IAAI,CAAC;MAChB6C,OAAO,CAACC,GAAG,CAAC,0CAA0CjB,IAAI,CAACe,OAAO,iCAAiCd,MAAM,CAAC6C,SAAS,EAAE,CAAC;;MAEtH;MACA,MAAMnG,WAAW,CAACgH,0BAA0B,CAAC/F,UAAU,EAAEoC,IAAI,CAACe,OAAO,EAAEd,MAAM,CAAC6C,SAAS,CAAC;;MAExF;MACA,MAAMc,WAAW,GAAG,MAAMjH,WAAW,CAAC0F,WAAW,CAACzE,UAAU,EAAEoC,IAAI,CAACe,OAAO,CAAC;MAC3E5B,eAAe,CAACyE,WAAW,CAAC;;MAE5B;MACAnE,mBAAmB,CAACQ,MAAM,CAAC6C,SAAS,CAAC;MAErCjF,SAAS,CAAC,4BAA4BmC,IAAI,CAACe,OAAO,4DAA4DqC,eAAe,CAACnD,MAAM,CAAC6C,SAAS,CAAC,EAAE,CAAC;;MAElJ;MACA,IAAIlD,0BAA0B,EAAE;QAC9B6D,iCAAiC,CAAC,CAAC;MACrC;;MAEA;MACA9C,UAAU,CAAC,CAAC;IACd,CAAC,CAAC,OAAOe,KAAK,EAAE;MACdV,OAAO,CAACU,KAAK,CAAC,2CAA2C,EAAEA,KAAK,CAAC;MACjE5D,OAAO,CAAC,4CAA4C,IAAI4D,KAAK,CAACC,MAAM,IAAID,KAAK,CAACE,OAAO,IAAI,oBAAoB,CAAC,CAAC;IACjH,CAAC,SAAS;MACRzD,UAAU,CAAC,KAAK,CAAC;IACnB;EACF,CAAC;;EAED;EACA,MAAM0F,8BAA8B,GAAG,MAAAA,CAAA,KAAY;IACjD,MAAM;MAAE7D,IAAI;MAAEC;IAAO,CAAC,GAAGH,oBAAoB;IAC7C,IAAI,CAACE,IAAI,IAAI,CAACC,MAAM,EAAE;MACpBe,OAAO,CAACU,KAAK,CAAC,uDAAuD,EAAE;QAAE1B,IAAI;QAAEC;MAAO,CAAC,CAAC;MACxFnC,OAAO,CAAC,sDAAsD,CAAC;MAC/D;IACF;IAEA,IAAI;MACFK,UAAU,CAAC,IAAI,CAAC;MAChB6C,OAAO,CAACC,GAAG,CAAC,iCAAiChB,MAAM,CAAC6C,SAAS,aAAa9C,IAAI,CAACe,OAAO,sCAAsC,CAAC;;MAE7H;MACAtB,mBAAmB,CAACQ,MAAM,CAAC6C,SAAS,CAAC;MAErCjF,SAAS,CAAC,wBAAwBuF,eAAe,CAACnD,MAAM,CAAC6C,SAAS,CAAC,4BAA4B9C,IAAI,CAACe,OAAO,EAAE,CAAC;MAC9G0C,iCAAiC,CAAC,CAAC;IACrC,CAAC,CAAC,OAAO/B,KAAK,EAAE;MACdV,OAAO,CAACU,KAAK,CAAC,yDAAyD,EAAEA,KAAK,CAAC;MAC/E5D,OAAO,CAAC,0DAA0D,IAAI4D,KAAK,CAACC,MAAM,IAAID,KAAK,CAACE,OAAO,IAAI,oBAAoB,CAAC,CAAC;IAC/H,CAAC,SAAS;MACRzD,UAAU,CAAC,KAAK,CAAC;IACnB;EACF,CAAC;;EAED;EACA,MAAMiF,eAAe,GAAIU,QAAQ,IAAK;IACpC,IAAIA,QAAQ,KAAK,cAAc,EAAE,OAAO,cAAc;;IAEtD;IACA,IAAIA,QAAQ,IAAIA,QAAQ,CAACC,QAAQ,CAAC,IAAI,CAAC,EAAE;MACvC,OAAOD,QAAQ,CAACE,KAAK,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;IAChC;IACA,OAAOF,QAAQ;EACjB,CAAC;;EAED;EACA,MAAMG,uBAAuB,GAAGA,CAAA,kBAC9BzG,OAAA,CAAC9D,GAAG;IAAAwK,QAAA,gBACF1G,OAAA,CAAC5D,UAAU;MAACuK,OAAO,EAAC,WAAW;MAACC,EAAE,EAAE;QAAEC,EAAE,EAAE,CAAC;QAAEC,UAAU,EAAE;MAAO,CAAE;MAAAJ,QAAA,EAAC;IAEnE;MAAAK,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAY,CAAC,eAGblH,OAAA,CAAC7D,KAAK;MAACyK,EAAE,EAAE;QAAEO,CAAC,EAAE,GAAG;QAAEN,EAAE,EAAE,CAAC;QAAEO,KAAK,EAAE;MAAO,CAAE;MAAAV,QAAA,eAC1C1G,OAAA,CAAC9D,GAAG;QAAC0K,EAAE,EAAE;UAAES,OAAO,EAAE,MAAM;UAAEC,UAAU,EAAE,QAAQ;UAAEF,KAAK,EAAE;QAAO,CAAE;QAAAV,QAAA,gBAChE1G,OAAA,CAAC5D,UAAU;UAACuK,OAAO,EAAC,WAAW;UAACC,EAAE,EAAE;YAAEW,EAAE,EAAE,CAAC;YAAEC,QAAQ,EAAE;UAAO,CAAE;UAAAd,QAAA,EAAC;QAEjE;UAAAK,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAY,CAAC,eACblH,OAAA,CAAC3D,SAAS;UACRoL,IAAI,EAAC,OAAO;UACZC,KAAK,EAAC,SAAS;UACff,OAAO,EAAC,UAAU;UAClBzB,KAAK,EAAEtD,WAAY;UACnB+F,QAAQ,EAAGC,CAAC,IAAK/F,cAAc,CAAC+F,CAAC,CAAC3C,MAAM,CAACC,KAAK,CAAE;UAChD2C,WAAW,EAAC,yBAAyB;UACrCjB,EAAE,EAAE;YAAEkB,QAAQ,EAAE,CAAC;YAAEV,KAAK,EAAE,OAAO;YAAEG,EAAE,EAAE;UAAE;QAAE;UAAAR,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC5C,CAAC,eACFlH,OAAA,CAAC1D,MAAM;UACLqK,OAAO,EAAC,WAAW;UACnBoB,KAAK,EAAC,SAAS;UACfC,OAAO,EAAErD,oBAAqB;UAC9BsD,QAAQ,EAAErH,WAAW,IAAI,CAACgB,WAAW,CAACgD,IAAI,CAAC,CAAE;UAC7CsD,SAAS,EAAEtH,WAAW,gBAAGZ,OAAA,CAAClD,gBAAgB;YAAC2K,IAAI,EAAE;UAAG;YAAAV,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,gBAAGlH,OAAA,CAAC3B,UAAU;YAAC8J,QAAQ,EAAC;UAAO;YAAApB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAE;UAC1FO,IAAI,EAAC,OAAO;UACZb,EAAE,EAAE;YAAEY,QAAQ,EAAE,MAAM;YAAEY,MAAM,EAAE,MAAM;YAAEb,EAAE,EAAE;UAAE,CAAE;UAAAb,QAAA,EACjD;QAED;UAAAK,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC,EAGRxF,YAAY,iBACX1B,OAAA,CAAC9D,GAAG;UAAC0K,EAAE,EAAE;YAAES,OAAO,EAAE,MAAM;YAAEC,UAAU,EAAE,QAAQ;YAAEQ,QAAQ,EAAE,CAAC;YAAEO,QAAQ,EAAE,QAAQ;YAAEC,QAAQ,EAAE,QAAQ;YAAEC,EAAE,EAAE;UAAE,CAAE;UAAA7B,QAAA,gBAC7G1G,OAAA,CAAC9D,GAAG;YAAC0K,EAAE,EAAE;cAAES,OAAO,EAAE,MAAM;cAAEC,UAAU,EAAE,QAAQ;cAAEC,EAAE,EAAE;YAAE,CAAE;YAAAb,QAAA,gBACxD1G,OAAA,CAAC5D,UAAU;cAACuK,OAAO,EAAC,WAAW;cAACC,EAAE,EAAE;gBAAEE,UAAU,EAAE,MAAM;gBAAE0B,UAAU,EAAE,QAAQ;gBAAEjB,EAAE,EAAE,CAAC;gBAAEY,QAAQ,EAAE;cAAU,CAAE;cAAAzB,QAAA,GAAC,QACtG,eAAA1G,OAAA;gBAAMyI,KAAK,EAAE;kBAAEV,KAAK,EAAE;gBAAU,CAAE;gBAAArB,QAAA,EAAEhF,YAAY,CAAC6B;cAAO;gBAAAwD,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC5D,CAAC,eACblH,OAAA,CAACpD,OAAO;cAAC8L,WAAW,EAAC,UAAU;cAACC,QAAQ;cAAC/B,EAAE,EAAE;gBAAEgC,EAAE,EAAE;cAAI;YAAE;cAAA7B,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,eAC5DlH,OAAA,CAAC9D,GAAG;cAAC0K,EAAE,EAAE;gBAAES,OAAO,EAAE,MAAM;gBAAEC,UAAU,EAAE,QAAQ;gBAAEuB,GAAG,EAAE,CAAC;gBAAER,QAAQ,EAAE,QAAQ;gBAAEC,QAAQ,EAAE;cAAS,CAAE;cAAA5B,QAAA,gBACjG1G,OAAA,CAAC9D,GAAG;gBAAC0K,EAAE,EAAE;kBAAES,OAAO,EAAE,MAAM;kBAAEC,UAAU,EAAE,QAAQ;kBAAEkB,UAAU,EAAE;gBAAS,CAAE;gBAAA9B,QAAA,gBACvE1G,OAAA,CAAC5D,UAAU;kBAACuK,OAAO,EAAC,OAAO;kBAACC,EAAE,EAAE;oBAAEE,UAAU,EAAE,QAAQ;oBAAEqB,QAAQ,EAAE,QAAQ;oBAAEZ,EAAE,EAAE;kBAAI,CAAE;kBAAAb,QAAA,EAAC;gBAAK;kBAAAK,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAY,CAAC,eACzGlH,OAAA,CAAC5D,UAAU;kBAACuK,OAAO,EAAC,OAAO;kBAACC,EAAE,EAAE;oBAAEuB,QAAQ,EAAE;kBAAS,CAAE;kBAAAzB,QAAA,EAAEhF,YAAY,CAAC8C,SAAS,IAAI;gBAAK;kBAAAuC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAa,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACnG,CAAC,eACNlH,OAAA,CAAC9D,GAAG;gBAAC0K,EAAE,EAAE;kBAAES,OAAO,EAAE,MAAM;kBAAEC,UAAU,EAAE,QAAQ;kBAAEkB,UAAU,EAAE;gBAAS,CAAE;gBAAA9B,QAAA,gBACvE1G,OAAA,CAAC5D,UAAU;kBAACuK,OAAO,EAAC,OAAO;kBAACC,EAAE,EAAE;oBAAEE,UAAU,EAAE,QAAQ;oBAAEqB,QAAQ,EAAE,QAAQ;oBAAEZ,EAAE,EAAE;kBAAI,CAAE;kBAAAb,QAAA,EAAC;gBAAK;kBAAAK,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAY,CAAC,eACzGlH,OAAA,CAAC5D,UAAU;kBAACuK,OAAO,EAAC,OAAO;kBAACC,EAAE,EAAE;oBAAEuB,QAAQ,EAAE;kBAAS,CAAE;kBAAAzB,QAAA,EAAEhF,YAAY,CAAC+C,OAAO,IAAI;gBAAK;kBAAAsC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAa,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACjG,CAAC,eACNlH,OAAA,CAAC9D,GAAG;gBAAC0K,EAAE,EAAE;kBAAES,OAAO,EAAE,MAAM;kBAAEC,UAAU,EAAE,QAAQ;kBAAEkB,UAAU,EAAE;gBAAS,CAAE;gBAAA9B,QAAA,gBACvE1G,OAAA,CAAC5D,UAAU;kBAACuK,OAAO,EAAC,OAAO;kBAACC,EAAE,EAAE;oBAAEE,UAAU,EAAE,QAAQ;oBAAEqB,QAAQ,EAAE,QAAQ;oBAAEZ,EAAE,EAAE;kBAAI,CAAE;kBAAAb,QAAA,EAAC;gBAAM;kBAAAK,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAY,CAAC,eAC1GlH,OAAA,CAAC5D,UAAU;kBAACuK,OAAO,EAAC,OAAO;kBAACC,EAAE,EAAE;oBAAEuB,QAAQ,EAAE;kBAAS,CAAE;kBAAAzB,QAAA,GAAEhF,YAAY,CAACsC,eAAe,IAAI,KAAK,EAAC,IAAE;gBAAA;kBAAA+C,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAY,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC3G,CAAC,eACNlH,OAAA,CAAC9D,GAAG;gBAAC0K,EAAE,EAAE;kBAAES,OAAO,EAAE,MAAM;kBAAEC,UAAU,EAAE,QAAQ;kBAAEkB,UAAU,EAAE;gBAAS,CAAE;gBAAA9B,QAAA,gBACvE1G,OAAA,CAAC5D,UAAU;kBAACuK,OAAO,EAAC,OAAO;kBAACC,EAAE,EAAE;oBAAEE,UAAU,EAAE,QAAQ;oBAAEqB,QAAQ,EAAE,QAAQ;oBAAEZ,EAAE,EAAE;kBAAI,CAAE;kBAAAb,QAAA,EAAC;gBAAM;kBAAAK,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAY,CAAC,eAC1GlH,OAAA,CAAC7B,IAAI;kBACHsJ,IAAI,EAAC,OAAO;kBACZC,KAAK,EAAEhG,YAAY,CAACuC,mBAAmB,IAAI,KAAM;kBACjD8D,KAAK,EAAC,SAAS;kBACfnB,EAAE,EAAE;oBAAEwB,MAAM,EAAE,MAAM;oBAAE,kBAAkB,EAAE;sBAAEU,EAAE,EAAE,CAAC;sBAAEC,EAAE,EAAE,CAAC;sBAAEZ,QAAQ,EAAE;oBAAU;kBAAE;gBAAE;kBAAApB,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACnF,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACC,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC,EAGLxF,YAAY,CAAC4D,SAAS,iBACrBtF,OAAA,CAAAE,SAAA;YAAAwG,QAAA,gBACE1G,OAAA,CAACpD,OAAO;cAAC8L,WAAW,EAAC,UAAU;cAACC,QAAQ;cAAC/B,EAAE,EAAE;gBAAEgC,EAAE,EAAE;cAAE;YAAE;cAAA7B,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,eAC1DlH,OAAA,CAAC9D,GAAG;cAAC0K,EAAE,EAAE;gBAAES,OAAO,EAAE,MAAM;gBAAEC,UAAU,EAAE;cAAS,CAAE;cAAAZ,QAAA,gBACjD1G,OAAA,CAAC5D,UAAU;gBAACuK,OAAO,EAAC,WAAW;gBAACC,EAAE,EAAE;kBAAEE,UAAU,EAAE,MAAM;kBAAE0B,UAAU,EAAE,QAAQ;kBAAEjB,EAAE,EAAE,CAAC;kBAAEY,QAAQ,EAAE,SAAS;kBAAEJ,KAAK,EAAE;gBAAU,CAAE;gBAAArB,QAAA,GAAC,kBAC9G,eAAA1G,OAAA;kBAAA0G,QAAA,EAAOhF,YAAY,CAAC4D,SAAS,KAAK,cAAc,GAAG,OAAO,GAAGM,eAAe,CAAClE,YAAY,CAAC4D,SAAS;gBAAC;kBAAAyB,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAO,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAClH,CAAC,EACZ,CAAC,MAAM;gBACN,IAAIxF,YAAY,CAAC4D,SAAS,KAAK,cAAc,EAAE;kBAC7C,oBACEtF,OAAA,CAAC9D,GAAG;oBAAC0K,EAAE,EAAE;sBAAES,OAAO,EAAE,MAAM;sBAAEC,UAAU,EAAE,QAAQ;sBAAEkB,UAAU,EAAE;oBAAS,CAAE;oBAAA9B,QAAA,eACvE1G,OAAA,CAAC5D,UAAU;sBAACuK,OAAO,EAAC,OAAO;sBAACC,EAAE,EAAE;wBAAEuB,QAAQ,EAAE,QAAQ;wBAAEJ,KAAK,EAAE,gBAAgB;wBAAEiB,SAAS,EAAE;sBAAS,CAAE;sBAAAtC,QAAA,EAAC;oBAEtG;sBAAAK,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAY;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACV,CAAC;gBAEV;gBAEA,MAAMzE,MAAM,GAAGnB,MAAM,CAACgC,IAAI,CAAC+B,CAAC,IAAIA,CAAC,CAACC,SAAS,KAAK5D,YAAY,CAAC4D,SAAS,CAAC;gBACvE,OAAO7C,MAAM,gBACXzC,OAAA,CAAC9D,GAAG;kBAAC0K,EAAE,EAAE;oBAAES,OAAO,EAAE,MAAM;oBAAEC,UAAU,EAAE,QAAQ;oBAAEuB,GAAG,EAAE,CAAC;oBAAER,QAAQ,EAAE,QAAQ;oBAAEC,QAAQ,EAAE;kBAAS,CAAE;kBAAA5B,QAAA,gBACjG1G,OAAA,CAAC9D,GAAG;oBAAC0K,EAAE,EAAE;sBAAES,OAAO,EAAE,MAAM;sBAAEC,UAAU,EAAE,QAAQ;sBAAEkB,UAAU,EAAE;oBAAS,CAAE;oBAAA9B,QAAA,gBACvE1G,OAAA,CAAC5D,UAAU;sBAACuK,OAAO,EAAC,OAAO;sBAACC,EAAE,EAAE;wBAAEE,UAAU,EAAE,QAAQ;wBAAEqB,QAAQ,EAAE,QAAQ;wBAAEZ,EAAE,EAAE;sBAAI,CAAE;sBAAAb,QAAA,EAAC;oBAAQ;sBAAAK,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAY,CAAC,eAC5GlH,OAAA,CAAC5D,UAAU;sBAACuK,OAAO,EAAC,OAAO;sBAACC,EAAE,EAAE;wBAAEuB,QAAQ,EAAE,QAAQ;wBAAEJ,KAAK,EAAE,cAAc;wBAAEjB,UAAU,EAAE;sBAAO,CAAE;sBAAAJ,QAAA,GAC/FjE,MAAM,CAACwG,aAAa,IAAI,CAAC,EAAC,IAC7B;oBAAA;sBAAAlC,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAY,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACV,CAAC,eACNlH,OAAA,CAAC9D,GAAG;oBAAC0K,EAAE,EAAE;sBAAES,OAAO,EAAE,MAAM;sBAAEC,UAAU,EAAE,QAAQ;sBAAEkB,UAAU,EAAE;oBAAS,CAAE;oBAAA9B,QAAA,gBACvE1G,OAAA,CAAC5D,UAAU;sBAACuK,OAAO,EAAC,OAAO;sBAACC,EAAE,EAAE;wBAAEE,UAAU,EAAE,QAAQ;wBAAEqB,QAAQ,EAAE,QAAQ;wBAAEZ,EAAE,EAAE;sBAAI,CAAE;sBAAAb,QAAA,EAAC;oBAAM;sBAAAK,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAY,CAAC,eAC1GlH,OAAA,CAAC7B,IAAI;sBACHsJ,IAAI,EAAC,OAAO;sBACZC,KAAK,EAAEjF,MAAM,CAACiC,YAAY,IAAI,KAAM;sBACpCqD,KAAK,EAAEtF,MAAM,CAACiC,YAAY,KAAK,aAAa,GAAG,SAAS,GAAG,SAAU;sBACrEkC,EAAE,EAAE;wBAAEwB,MAAM,EAAE,MAAM;wBAAE,kBAAkB,EAAE;0BAAEU,EAAE,EAAE,CAAC;0BAAEC,EAAE,EAAE,CAAC;0BAAEZ,QAAQ,EAAE;wBAAU;sBAAE;oBAAE;sBAAApB,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACnF,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACC,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH,CAAC,GACJ,IAAI;cACV,CAAC,EAAE,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACD,CAAC;UAAA,eACN,CACH,EAGAlF,gBAAgB,IAAIA,gBAAgB,MAAKN,YAAY,aAAZA,YAAY,uBAAZA,YAAY,CAAE4D,SAAS,kBAC/DtF,OAAA,CAAAE,SAAA;YAAAwG,QAAA,gBACE1G,OAAA,CAACpD,OAAO;cAAC8L,WAAW,EAAC,UAAU;cAACC,QAAQ;cAAC/B,EAAE,EAAE;gBAAEgC,EAAE,EAAE;cAAE;YAAE;cAAA7B,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,eAC1DlH,OAAA,CAAC9D,GAAG;cAAC0K,EAAE,EAAE;gBAAES,OAAO,EAAE,MAAM;gBAAEC,UAAU,EAAE;cAAS,CAAE;cAAAZ,QAAA,gBACjD1G,OAAA,CAAC5D,UAAU;gBAACuK,OAAO,EAAC,WAAW;gBAACC,EAAE,EAAE;kBAAEE,UAAU,EAAE,MAAM;kBAAE0B,UAAU,EAAE,QAAQ;kBAAEjB,EAAE,EAAE,CAAC;kBAAEY,QAAQ,EAAE,SAAS;kBAAEJ,KAAK,EAAE;gBAAU,CAAE;gBAAArB,QAAA,GAAC,sBAC1G,eAAA1G,OAAA;kBAAA0G,QAAA,EAAO1E,gBAAgB,KAAK,cAAc,GAAG,OAAO,GAAG4D,eAAe,CAAC5D,gBAAgB;gBAAC;kBAAA+E,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAO,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC1G,CAAC,EACZ,CAAC,MAAM;gBACN,IAAIlF,gBAAgB,KAAK,cAAc,EAAE;kBACvC,oBACEhC,OAAA,CAAC9D,GAAG;oBAAC0K,EAAE,EAAE;sBAAES,OAAO,EAAE,MAAM;sBAAEC,UAAU,EAAE,QAAQ;sBAAEkB,UAAU,EAAE;oBAAS,CAAE;oBAAA9B,QAAA,eACvE1G,OAAA,CAAC5D,UAAU;sBAACuK,OAAO,EAAC,OAAO;sBAACC,EAAE,EAAE;wBAAEuB,QAAQ,EAAE,QAAQ;wBAAEJ,KAAK,EAAE,gBAAgB;wBAAEiB,SAAS,EAAE;sBAAS,CAAE;sBAAAtC,QAAA,EAAC;oBAEtG;sBAAAK,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAY;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACV,CAAC;gBAEV;gBAEA,MAAMzE,MAAM,GAAGnB,MAAM,CAACgC,IAAI,CAAC+B,CAAC,IAAIA,CAAC,CAACC,SAAS,KAAKtD,gBAAgB,CAAC;gBACjE,IAAI,CAACS,MAAM,EAAE,OAAO,IAAI;;gBAExB;gBACA,MAAM8C,YAAY,GAAG7D,YAAY,IAC/B8D,MAAM,CAAC/C,MAAM,CAAC+B,SAAS,IAAI,EAAE,CAAC,CAACI,IAAI,CAAC,CAAC,KAAKY,MAAM,CAAC9D,YAAY,CAAC8C,SAAS,IAAI,EAAE,CAAC,CAACI,IAAI,CAAC,CAAC,IACrFY,MAAM,CAAC/C,MAAM,CAACgC,OAAO,IAAI,EAAE,CAAC,CAACG,IAAI,CAAC,CAAC,KAAKY,MAAM,CAAC9D,YAAY,CAAC+C,OAAO,IAAI,EAAE,CAAC,CAACG,IAAI,CAAC,CAAC;gBAEnF,oBACE5E,OAAA,CAAC9D,GAAG;kBAAC0K,EAAE,EAAE;oBAAES,OAAO,EAAE,MAAM;oBAAEC,UAAU,EAAE,QAAQ;oBAAEuB,GAAG,EAAE,CAAC;oBAAER,QAAQ,EAAE,QAAQ;oBAAEC,QAAQ,EAAE;kBAAS,CAAE;kBAAA5B,QAAA,gBACjG1G,OAAA,CAAC9D,GAAG;oBAAC0K,EAAE,EAAE;sBAAES,OAAO,EAAE,MAAM;sBAAEC,UAAU,EAAE,QAAQ;sBAAEkB,UAAU,EAAE;oBAAS,CAAE;oBAAA9B,QAAA,gBACvE1G,OAAA,CAAC5D,UAAU;sBAACuK,OAAO,EAAC,OAAO;sBAACC,EAAE,EAAE;wBAAEE,UAAU,EAAE,QAAQ;wBAAEqB,QAAQ,EAAE,QAAQ;wBAAEZ,EAAE,EAAE;sBAAI,CAAE;sBAAAb,QAAA,EAAC;oBAAK;sBAAAK,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAY,CAAC,eACzGlH,OAAA,CAAC5D,UAAU;sBAACuK,OAAO,EAAC,OAAO;sBAACC,EAAE,EAAE;wBAAEuB,QAAQ,EAAE,QAAQ;wBAAEJ,KAAK,EAAExC,YAAY,GAAG,cAAc,GAAG;sBAAa,CAAE;sBAAAmB,QAAA,EACzGjE,MAAM,CAAC+B,SAAS,IAAI;oBAAK;sBAAAuC,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAChB,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACV,CAAC,eACNlH,OAAA,CAAC9D,GAAG;oBAAC0K,EAAE,EAAE;sBAAES,OAAO,EAAE,MAAM;sBAAEC,UAAU,EAAE,QAAQ;sBAAEkB,UAAU,EAAE;oBAAS,CAAE;oBAAA9B,QAAA,gBACvE1G,OAAA,CAAC5D,UAAU;sBAACuK,OAAO,EAAC,OAAO;sBAACC,EAAE,EAAE;wBAAEE,UAAU,EAAE,QAAQ;wBAAEqB,QAAQ,EAAE,QAAQ;wBAAEZ,EAAE,EAAE;sBAAI,CAAE;sBAAAb,QAAA,EAAC;oBAAK;sBAAAK,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAY,CAAC,eACzGlH,OAAA,CAAC5D,UAAU;sBAACuK,OAAO,EAAC,OAAO;sBAACC,EAAE,EAAE;wBAAEuB,QAAQ,EAAE,QAAQ;wBAAEJ,KAAK,EAAExC,YAAY,GAAG,cAAc,GAAG;sBAAa,CAAE;sBAAAmB,QAAA,EACzGjE,MAAM,CAACgC,OAAO,IAAI;oBAAK;sBAAAsC,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACd,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACV,CAAC,eACNlH,OAAA,CAAC9D,GAAG;oBAAC0K,EAAE,EAAE;sBAAES,OAAO,EAAE,MAAM;sBAAEC,UAAU,EAAE,QAAQ;sBAAEkB,UAAU,EAAE;oBAAS,CAAE;oBAAA9B,QAAA,gBACvE1G,OAAA,CAAC5D,UAAU;sBAACuK,OAAO,EAAC,OAAO;sBAACC,EAAE,EAAE;wBAAEE,UAAU,EAAE,QAAQ;wBAAEqB,QAAQ,EAAE,QAAQ;wBAAEZ,EAAE,EAAE;sBAAI,CAAE;sBAAAb,QAAA,EAAC;oBAAQ;sBAAAK,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAY,CAAC,eAC5GlH,OAAA,CAAC5D,UAAU;sBAACuK,OAAO,EAAC,OAAO;sBAACC,EAAE,EAAE;wBAAEuB,QAAQ,EAAE,QAAQ;wBAAEJ,KAAK,EAAE,cAAc;wBAAEjB,UAAU,EAAE;sBAAO,CAAE;sBAAAJ,QAAA,GAC/FjE,MAAM,CAACwG,aAAa,IAAI,CAAC,EAAC,IAC7B;oBAAA;sBAAAlC,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAY,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACV,CAAC,eACNlH,OAAA,CAAC9D,GAAG;oBAAC0K,EAAE,EAAE;sBAAES,OAAO,EAAE,MAAM;sBAAEC,UAAU,EAAE,QAAQ;sBAAEkB,UAAU,EAAE;oBAAS,CAAE;oBAAA9B,QAAA,gBACvE1G,OAAA,CAAC5D,UAAU;sBAACuK,OAAO,EAAC,OAAO;sBAACC,EAAE,EAAE;wBAAEE,UAAU,EAAE,QAAQ;wBAAEqB,QAAQ,EAAE,QAAQ;wBAAEZ,EAAE,EAAE;sBAAI,CAAE;sBAAAb,QAAA,EAAC;oBAAM;sBAAAK,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAY,CAAC,eAC1GlH,OAAA,CAAC7B,IAAI;sBACHsJ,IAAI,EAAC,OAAO;sBACZC,KAAK,EAAEjF,MAAM,CAACiC,YAAY,IAAI,KAAM;sBACpCqD,KAAK,EAAEtF,MAAM,CAACiC,YAAY,KAAK,aAAa,GAAG,SAAS,GAAG,SAAU;sBACrEkC,EAAE,EAAE;wBAAEwB,MAAM,EAAE,MAAM;wBAAE,kBAAkB,EAAE;0BAAEU,EAAE,EAAE,CAAC;0BAAEC,EAAE,EAAE,CAAC;0BAAEZ,QAAQ,EAAE;wBAAU;sBAAE;oBAAE;sBAAApB,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACnF,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACC,CAAC,EACL,CAAC3B,YAAY,iBACZvF,OAAA,CAAC9D,GAAG;oBAAC0K,EAAE,EAAE;sBAAES,OAAO,EAAE,MAAM;sBAAEC,UAAU,EAAE,QAAQ;sBAAEkB,UAAU,EAAE;oBAAS,CAAE;oBAAA9B,QAAA,eACvE1G,OAAA,CAAC7B,IAAI;sBACHsJ,IAAI,EAAC,OAAO;sBACZC,KAAK,EAAC,iBAAiB;sBACvBK,KAAK,EAAC,OAAO;sBACbpB,OAAO,EAAC,UAAU;sBAClBC,EAAE,EAAE;wBAAEwB,MAAM,EAAE,MAAM;wBAAE,kBAAkB,EAAE;0BAAEU,EAAE,EAAE,CAAC;0BAAEC,EAAE,EAAE,CAAC;0BAAEZ,QAAQ,EAAE;wBAAU;sBAAE;oBAAE;sBAAApB,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACnF;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACC,CACN;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACE,CAAC;cAEV,CAAC,EAAE,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACD,CAAC;UAAA,eACN,CACH;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACE,CACN;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACE;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACD,CAAC,eAGRlH,OAAA,CAAC7D,KAAK;MAACyK,EAAE,EAAE;QAAEO,CAAC,EAAE,GAAG;QAAEC,KAAK,EAAE;MAAO,CAAE;MAAAV,QAAA,gBACnC1G,OAAA,CAAC5D,UAAU;QAACuK,OAAO,EAAC,WAAW;QAACC,EAAE,EAAE;UAAEC,EAAE,EAAE;QAAE,CAAE;QAAAH,QAAA,EAAC;MAE/C;QAAAK,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAY,CAAC,EAEZtG,WAAW,gBACVZ,OAAA,CAAC9D,GAAG;QAAC0K,EAAE,EAAE;UAAES,OAAO,EAAE,MAAM;UAAE6B,cAAc,EAAE,QAAQ;UAAEC,EAAE,EAAE;QAAE,CAAE;QAAAzC,QAAA,eAC5D1G,OAAA,CAAClD,gBAAgB;UAAC2K,IAAI,EAAE;QAAG;UAAAV,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC3B,CAAC,GACJ9F,IAAI,CAACgC,MAAM,KAAK,CAAC,gBACnBpD,OAAA,CAACnD,KAAK;QAACuM,QAAQ,EAAC,MAAM;QAACxC,EAAE,EAAE;UAAEmC,EAAE,EAAE;QAAI,CAAE;QAAArC,QAAA,eACrC1G,OAAA,CAAC5D,UAAU;UAACuK,OAAO,EAAC,SAAS;UAAAD,QAAA,EAAC;QAAoC;UAAAK,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAY;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC1E,CAAC,gBAERlH,OAAA,CAAC1C,cAAc;QAAC+L,SAAS,EAAElN,KAAM;QAACwK,OAAO,EAAC,UAAU;QAACC,EAAE,EAAE;UAAE0C,SAAS,EAAE,OAAO;UAAEhB,QAAQ,EAAE,MAAM;UAAElB,KAAK,EAAE;QAAO,CAAE;QAAAV,QAAA,eAC/G1G,OAAA,CAAC7C,KAAK;UAACsK,IAAI,EAAC,OAAO;UAAC8B,YAAY;UAAA7C,QAAA,gBAC9B1G,OAAA,CAACzC,SAAS;YAAAmJ,QAAA,eACR1G,OAAA,CAACxC,QAAQ;cAACoJ,EAAE,EAAE;gBAAE,MAAM,EAAE;kBAAEE,UAAU,EAAE,MAAM;kBAAEiC,EAAE,EAAE,CAAC;kBAAES,OAAO,EAAE;gBAAU;cAAE,CAAE;cAAA9C,QAAA,gBAC1E1G,OAAA,CAAC3C,SAAS;gBAAAqJ,QAAA,EAAC;cAAO;gBAAAK,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAW,CAAC,eAC9BlH,OAAA,CAAC3C,SAAS;gBAAAqJ,QAAA,EAAC;cAAS;gBAAAK,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAW,CAAC,eAChClH,OAAA,CAAC3C,SAAS;gBAAAqJ,QAAA,EAAC;cAAU;gBAAAK,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAW,CAAC,eACjClH,OAAA,CAAC3C,SAAS;gBAAAqJ,QAAA,EAAC;cAAK;gBAAAK,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAW,CAAC,eAC5BlH,OAAA,CAAC3C,SAAS;gBAAAqJ,QAAA,EAAC;cAAM;gBAAAK,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAW,CAAC,eAC7BlH,OAAA,CAAC3C,SAAS;gBAAAqJ,QAAA,EAAC;cAAK;gBAAAK,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAW,CAAC,eAC5BlH,OAAA,CAAC3C,SAAS;gBAACoM,KAAK,EAAC,QAAQ;gBAAC7C,EAAE,EAAE;kBAAEQ,KAAK,EAAE;gBAAO,CAAE;gBAAAV,QAAA,EAAC;cAAI;gBAAAK,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAW,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACzD;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACF,CAAC,eACZlH,OAAA,CAAC5C,SAAS;YAAAsJ,QAAA,EACPtF,IAAI,CAACsI,GAAG,CAAElH,IAAI,iBACbxC,OAAA,CAACxC,QAAQ;cAEPmM,KAAK;cACL3B,OAAO,EAAEA,CAAA,KAAMlD,gBAAgB,CAACtC,IAAI,CAAE;cACtCoE,EAAE,EAAE;gBACFgD,MAAM,EAAE,SAAS;gBACjB,SAAS,EAAE;kBAAEJ,OAAO,EAAE;gBAAU,CAAC;gBACjC,MAAM,EAAE;kBAAET,EAAE,EAAE;gBAAI;cACpB,CAAE;cAAArC,QAAA,gBAEF1G,OAAA,CAAC3C,SAAS;gBAACuJ,EAAE,EAAE;kBAAEE,UAAU,EAAE;gBAAS,CAAE;gBAAAJ,QAAA,EAAElE,IAAI,CAACe;cAAO;gBAAAwD,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAY,CAAC,eACnElH,OAAA,CAAC3C,SAAS;gBAAAqJ,QAAA,EAAElE,IAAI,CAACgC,SAAS,IAAI;cAAK;gBAAAuC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAY,CAAC,eAChDlH,OAAA,CAAC3C,SAAS;gBAAAqJ,QAAA,EAAElE,IAAI,CAACiC,OAAO,IAAI;cAAK;gBAAAsC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAY,CAAC,eAC9ClH,OAAA,CAAC3C,SAAS;gBAAAqJ,QAAA,GAAElE,IAAI,CAACwB,eAAe,IAAI,KAAK,EAAC,IAAE;cAAA;gBAAA+C,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAW,CAAC,eACxDlH,OAAA,CAAC3C,SAAS;gBAAAqJ,QAAA,EACPlE,IAAI,CAAC8C,SAAS,GAAI9C,IAAI,CAAC8C,SAAS,KAAK,cAAc,GAAG,OAAO,GAAGM,eAAe,CAACpD,IAAI,CAAC8C,SAAS,CAAC,GAAI;cAAO;gBAAAyB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAClG,CAAC,eACZlH,OAAA,CAAC3C,SAAS;gBAAAqJ,QAAA,eACR1G,OAAA,CAAC7B,IAAI;kBACHsJ,IAAI,EAAC,OAAO;kBACZC,KAAK,EAAC,YAAY;kBAClBK,KAAK,EAAC,SAAS;kBACfnB,EAAE,EAAE;oBAAEwB,MAAM,EAAE,MAAM;oBAAE,kBAAkB,EAAE;sBAAEU,EAAE,EAAE,CAAC;sBAAEC,EAAE,EAAE,CAAC;sBAAEZ,QAAQ,EAAE;oBAAS;kBAAE;gBAAE;kBAAApB,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAClF;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACO,CAAC,eACZlH,OAAA,CAAC3C,SAAS;gBAACoM,KAAK,EAAC,QAAQ;gBAAA/C,QAAA,eACvB1G,OAAA,CAACpC,UAAU;kBACT6J,IAAI,EAAC,OAAO;kBACZO,OAAO,EAAGJ,CAAC,IAAK;oBACdA,CAAC,CAACiC,eAAe,CAAC,CAAC;oBACnBlI,eAAe,CAACa,IAAI,CAAC;oBACrBS,wBAAwB,CAAC,IAAI,CAAC;kBAChC,CAAE;kBAAAyD,QAAA,eAEF1G,OAAA,CAACnB,QAAQ;oBAACsJ,QAAQ,EAAC;kBAAO;oBAAApB,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACnB;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACJ,CAAC;YAAA,GAnCP1E,IAAI,CAACe,OAAO;cAAAwD,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAoCT,CACX;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACO,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACP;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACM,CACjB;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACI,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACL,CACN;;EAID;EACA,MAAM4C,qBAAqB,GAAGA,CAAA,KAAM;IAClC,IAAI,CAACpI,YAAY,EAAE,OAAO,IAAI;IAE9B,oBACE1B,OAAA,CAAC7D,KAAK;MAACyK,EAAE,EAAE;QAAEO,CAAC,EAAE,CAAC;QAAEN,EAAE,EAAE;MAAE,CAAE;MAAAH,QAAA,gBACzB1G,OAAA,CAAC5D,UAAU;QAACuK,OAAO,EAAC,IAAI;QAACoD,YAAY;QAAArD,QAAA,EAAC;MAEtC;QAAAK,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAY,CAAC,eAEblH,OAAA,CAACtC,UAAU;QACTwH,KAAK,EAAEpD,cAAe;QACtB6F,QAAQ,EAAE5C,kBAAmB;QAAA2B,QAAA,gBAE7B1G,OAAA,CAACrC,gBAAgB;UACfuH,KAAK,EAAC,cAAc;UACpB8E,OAAO,eAAEhK,OAAA,CAACvC,KAAK;YAAAsJ,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAE;UACnBQ,KAAK,EAAC;QAAsB;UAAAX,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC7B,CAAC,eACFlH,OAAA,CAACrC,gBAAgB;UACfuH,KAAK,EAAC,eAAe;UACrB8E,OAAO,eAAEhK,OAAA,CAACvC,KAAK;YAAAsJ,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAE;UACnBQ,KAAK,EAAC;QAAwB;UAAAX,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC/B,CAAC,eACFlH,OAAA,CAACrC,gBAAgB;UACfuH,KAAK,EAAC,sBAAsB;UAC5B8E,OAAO,eAAEhK,OAAA,CAACvC,KAAK;YAAAsJ,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAE;UACnBQ,KAAK,EAAC;QAAuB;UAAAX,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC9B,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACQ,CAAC,EAEZpF,cAAc,KAAK,cAAc,IAAImI,qBAAqB,CAAC,CAAC,EAE5DnI,cAAc,KAAK,eAAe,iBACjC9B,OAAA,CAACnD,KAAK;QAACuM,QAAQ,EAAC,MAAM;QAACxC,EAAE,EAAE;UAAEsD,EAAE,EAAE;QAAE,CAAE;QAAAxD,QAAA,EAAC;MAItC;QAAAK,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAO,CACR,EAEApF,cAAc,KAAK,sBAAsB,iBACxC9B,OAAA,CAACnD,KAAK;QAACuM,QAAQ,EAAC,SAAS;QAACxC,EAAE,EAAE;UAAEsD,EAAE,EAAE;QAAE,CAAE;QAAAxD,QAAA,gBACtC1G,OAAA,CAAC5D,UAAU;UAACuK,OAAO,EAAC,OAAO;UAACG,UAAU,EAAC,MAAM;UAAAJ,QAAA,EAAC;QAE9C;UAAAK,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAY,CAAC,eACblH,OAAA,CAAC5D,UAAU;UAACuK,OAAO,EAAC,OAAO;UAAAD,QAAA,GAAC,mDACkB,eAAA1G,OAAA;YAAA+G,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAK,CAAC,yCAChB,eAAAlH,OAAA;YAAA+G,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAK,CAAC,kDACG,eAAAlH,OAAA;YAAA+G,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAK,CAAC,2EAEnD;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAY,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACR,CACR,eAEDlH,OAAA,CAAC9D,GAAG;QAAC0K,EAAE,EAAE;UAAEsD,EAAE,EAAE,CAAC;UAAE7C,OAAO,EAAE,MAAM;UAAE6B,cAAc,EAAE,UAAU;UAAEL,GAAG,EAAE;QAAE,CAAE;QAAAnC,QAAA,gBACtE1G,OAAA,CAAC1D,MAAM;UACLqK,OAAO,EAAC,UAAU;UAClBoB,KAAK,EAAC,WAAW;UACjBG,SAAS,eAAElI,OAAA,CAACvB,UAAU;YAAAsI,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAE;UAC1Bc,OAAO,EAAEhC,eAAgB;UACzBiC,QAAQ,EAAEvH,OAAQ;UAAAgG,QAAA,EACnB;QAED;UAAAK,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC,eACTlH,OAAA,CAAC1D,MAAM;UACLqK,OAAO,EAAC,WAAW;UACnBoB,KAAK,EAAC,SAAS;UACfG,SAAS,eAAElI,OAAA,CAACzB,QAAQ;YAAAwI,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAE;UACxBc,OAAO,EAAEtC,UAAW;UACpBuC,QAAQ,EAAEvH,OAAO,IAAI,CAACoB,cAAc,IAAKA,cAAc,KAAK,cAAc,IAAI,CAACE,gBAAkB;UAAA0E,QAAA,EAClG;QAED;UAAAK,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACN,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACD,CAAC;EAEZ,CAAC;;EAED;EACA,MAAM+C,qBAAqB,GAAGA,CAAA,KAAM;IAClC,IAAInJ,aAAa,EAAE;MACjB,oBACEd,OAAA,CAAC9D,GAAG;QAAC0K,EAAE,EAAE;UAAES,OAAO,EAAE,MAAM;UAAE6B,cAAc,EAAE,QAAQ;UAAEgB,EAAE,EAAE;QAAE,CAAE;QAAAxD,QAAA,eAC5D1G,OAAA,CAAClD,gBAAgB;UAAAiK,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACjB,CAAC;IAEV;;IAEA;IACA,MAAMiD,cAAc,GAAG7I,MAAM,CAACwC,MAAM,CAACrB,MAAM,IAAI;MAC7C,MAAM2H,WAAW,GAAGlI,gBAAgB,CAACmI,WAAW,CAAC,CAAC;MAClD,OAAO,CAACnI,gBAAgB,IACtB0D,eAAe,CAACnD,MAAM,CAAC6C,SAAS,CAAC,CAAC+E,WAAW,CAAC,CAAC,CAAC9D,QAAQ,CAAC6D,WAAW,CAAC,IACrE5E,MAAM,CAAC/C,MAAM,CAAC+B,SAAS,IAAI,EAAE,CAAC,CAAC6F,WAAW,CAAC,CAAC,CAAC9D,QAAQ,CAAC6D,WAAW,CAAC,IAClE5E,MAAM,CAAC/C,MAAM,CAACgC,OAAO,IAAI,EAAE,CAAC,CAAC4F,WAAW,CAAC,CAAC,CAAC9D,QAAQ,CAAC6D,WAAW,CAAC;IACpE,CAAC,CAAC;;IAEF;IACA,MAAME,iBAAiB,GAAG5I,YAAY,GAClCyI,cAAc,CAACrG,MAAM,CAACrB,MAAM,IAC1B+C,MAAM,CAAC/C,MAAM,CAAC+B,SAAS,IAAI,EAAE,CAAC,CAACI,IAAI,CAAC,CAAC,KAAKY,MAAM,CAAC9D,YAAY,CAAC8C,SAAS,IAAI,EAAE,CAAC,CAACI,IAAI,CAAC,CAAC,IACrFY,MAAM,CAAC/C,MAAM,CAACgC,OAAO,IAAI,EAAE,CAAC,CAACG,IAAI,CAAC,CAAC,KAAKY,MAAM,CAAC9D,YAAY,CAAC+C,OAAO,IAAI,EAAE,CAAC,CAACG,IAAI,CAAC,CAAC,KAChFnC,MAAM,CAACiC,YAAY,KAAK,aAAa,IAAIjC,MAAM,CAACiC,YAAY,KAAK,QAAQ,CAAC,CAAC,GAC9E,EAAE;IAEN,MAAM6F,oBAAoB,GAAG7I,YAAY,GACrCyI,cAAc,CAACrG,MAAM,CAACrB,MAAM,IAC1B,EAAE+C,MAAM,CAAC/C,MAAM,CAAC+B,SAAS,IAAI,EAAE,CAAC,CAACI,IAAI,CAAC,CAAC,KAAKY,MAAM,CAAC9D,YAAY,CAAC8C,SAAS,IAAI,EAAE,CAAC,CAACI,IAAI,CAAC,CAAC,IACrFY,MAAM,CAAC/C,MAAM,CAACgC,OAAO,IAAI,EAAE,CAAC,CAACG,IAAI,CAAC,CAAC,KAAKY,MAAM,CAAC9D,YAAY,CAAC+C,OAAO,IAAI,EAAE,CAAC,CAACG,IAAI,CAAC,CAAC,CAAC,KACnFnC,MAAM,CAACiC,YAAY,KAAK,aAAa,IAAIjC,MAAM,CAACiC,YAAY,KAAK,QAAQ,CAAC,CAAC,GAC9E,EAAE;;IAEN;IACA4F,iBAAiB,CAACE,IAAI,CAAC,CAACC,CAAC,EAAEpF,CAAC,KAAKA,CAAC,CAAC4D,aAAa,GAAGwB,CAAC,CAACxB,aAAa,CAAC;IACnEsB,oBAAoB,CAACC,IAAI,CAAC,CAACC,CAAC,EAAEpF,CAAC,KAAKA,CAAC,CAAC4D,aAAa,GAAGwB,CAAC,CAACxB,aAAa,CAAC;IAEtE,oBACEjJ,OAAA,CAAC9D,GAAG;MAAC0K,EAAE,EAAE;QAAEsD,EAAE,EAAE;MAAE,CAAE;MAAAxD,QAAA,gBAEjB1G,OAAA,CAAC9D,GAAG;QAAC0K,EAAE,EAAE;UAAEC,EAAE,EAAE;QAAE,CAAE;QAAAH,QAAA,eACjB1G,OAAA,CAAC3D,SAAS;UACRqO,SAAS;UACThD,KAAK,EAAC,4CAA4C;UAClDxC,KAAK,EAAEhD,gBAAiB;UACxByF,QAAQ,EAAGC,CAAC,IAAKzF,mBAAmB,CAACyF,CAAC,CAAC3C,MAAM,CAACC,KAAK,CAAE;UACrDyF,UAAU,EAAE;YACVC,cAAc,eACZ5K,OAAA,CAACnC,cAAc;cAACgN,QAAQ,EAAC,OAAO;cAAAnE,QAAA,eAC9B1G,OAAA,CAAC3B,UAAU;gBAAA0I,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACA;UAEpB,CAAE;UACFN,EAAE,EAAE;YAAEC,EAAE,EAAE;UAAE;QAAE;UAAAE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACf;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACC,CAAC,EAKLlF,gBAAgB,iBACfhC,OAAA,CAACnD,KAAK;QAACuM,QAAQ,EAAC,SAAS;QAACxC,EAAE,EAAE;UAAEC,EAAE,EAAE;QAAE,CAAE;QAAAH,QAAA,GAAC,sBACnB,eAAA1G,OAAA;UAAA0G,QAAA,EAASd,eAAe,CAAC5D,gBAAgB;QAAC;UAAA+E,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAS,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACnE,CACR,eAGDlH,OAAA,CAACzD,IAAI;QAACuO,SAAS;QAACC,OAAO,EAAE,CAAE;QAAArE,QAAA,gBAEzB1G,OAAA,CAACzD,IAAI;UAACyO,IAAI;UAACC,EAAE,EAAE,EAAG;UAACC,EAAE,EAAE,CAAE;UAAAxE,QAAA,eACvB1G,OAAA,CAAC7D,KAAK;YAACwK,OAAO,EAAC,UAAU;YAACC,EAAE,EAAE;cAAEO,CAAC,EAAE,CAAC;cAAEiB,MAAM,EAAE;YAAO,CAAE;YAAA1B,QAAA,gBACrD1G,OAAA,CAAC5D,UAAU;cAACuK,OAAO,EAAC,WAAW;cAACC,EAAE,EAAE;gBAAEE,UAAU,EAAE,MAAM;gBAAED,EAAE,EAAE;cAAE,CAAE;cAAAH,QAAA,EAAC;YAEnE;cAAAK,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAY,CAAC,EAEZoD,iBAAiB,CAAClH,MAAM,GAAG,CAAC,gBAC3BpD,OAAA,CAAAE,SAAA;cAAAwG,QAAA,gBACE1G,OAAA,CAAC9D,GAAG;gBAAC0K,EAAE,EAAE;kBAAES,OAAO,EAAE,MAAM;kBAAEC,UAAU,EAAE,QAAQ;kBAAEF,KAAK,EAAE,MAAM;kBAAE2B,EAAE,EAAE,GAAG;kBAAED,EAAE,EAAE,GAAG;kBAAEU,OAAO,EAAE,SAAS;kBAAE2B,YAAY,EAAE,CAAC;kBAAEtE,EAAE,EAAE;gBAAE,CAAE;gBAAAH,QAAA,gBAC9H1G,OAAA,CAAC9D,GAAG;kBAAC0K,EAAE,EAAE;oBAAEQ,KAAK,EAAE,MAAM;oBAAEG,EAAE,EAAE;kBAAE,CAAE;kBAAAb,QAAA,eAChC1G,OAAA,CAAC5D,UAAU;oBAACuK,OAAO,EAAC,SAAS;oBAACC,EAAE,EAAE;sBAAEE,UAAU,EAAE,MAAM;sBAAEqB,QAAQ,EAAE;oBAAU,CAAE;oBAAAzB,QAAA,EAAC;kBAAE;oBAAAK,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAY;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAC3F,CAAC,eACNlH,OAAA,CAAC9D,GAAG;kBAAC0K,EAAE,EAAE;oBAAEQ,KAAK,EAAE,OAAO;oBAAEG,EAAE,EAAE;kBAAE,CAAE;kBAAAb,QAAA,eACjC1G,OAAA,CAAC5D,UAAU;oBAACuK,OAAO,EAAC,SAAS;oBAACC,EAAE,EAAE;sBAAEE,UAAU,EAAE,MAAM;sBAAEqB,QAAQ,EAAE;oBAAU,CAAE;oBAAAzB,QAAA,EAAC;kBAAI;oBAAAK,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAY;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAC7F,CAAC,eACNlH,OAAA,CAAC9D,GAAG;kBAAC0K,EAAE,EAAE;oBAAEQ,KAAK,EAAE,OAAO;oBAAEG,EAAE,EAAE;kBAAE,CAAE;kBAAAb,QAAA,eACjC1G,OAAA,CAAC5D,UAAU;oBAACuK,OAAO,EAAC,SAAS;oBAACC,EAAE,EAAE;sBAAEE,UAAU,EAAE,MAAM;sBAAEqB,QAAQ,EAAE;oBAAU,CAAE;oBAAAzB,QAAA,EAAC;kBAAK;oBAAAK,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAY;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAC9F,CAAC,eACNlH,OAAA,CAAC9D,GAAG;kBAAC0K,EAAE,EAAE;oBAAEQ,KAAK,EAAE,OAAO;oBAAEG,EAAE,EAAE;kBAAE,CAAE;kBAAAb,QAAA,eACjC1G,OAAA,CAAC5D,UAAU;oBAACuK,OAAO,EAAC,SAAS;oBAACC,EAAE,EAAE;sBAAEE,UAAU,EAAE,MAAM;sBAAEqB,QAAQ,EAAE;oBAAU,CAAE;oBAAAzB,QAAA,EAAC;kBAAO;oBAAAK,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAY;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAChG,CAAC,eACNlH,OAAA,CAAC9D,GAAG;kBAAC0K,EAAE,EAAE;oBAAEkB,QAAQ,EAAE;kBAAE,CAAE;kBAAApB,QAAA,eACvB1G,OAAA,CAAC5D,UAAU;oBAACuK,OAAO,EAAC,SAAS;oBAACC,EAAE,EAAE;sBAAEE,UAAU,EAAE,MAAM;sBAAEqB,QAAQ,EAAE;oBAAU,CAAE;oBAAAzB,QAAA,EAAC;kBAAK;oBAAAK,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAY;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAC9F,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CAAC,eACNlH,OAAA,CAAClC,IAAI;gBAAC8I,EAAE,EAAE;kBAAE0C,SAAS,EAAEgB,iBAAiB,CAAClH,MAAM,GAAG,CAAC,GAAG,OAAO,GAAG,MAAM;kBAAEgI,SAAS,EAAEd,iBAAiB,CAAClH,MAAM,GAAG,CAAC,GAAG,MAAM,GAAG,SAAS;kBAAEiI,SAAS,EAAE,QAAQ;kBAAE7B,OAAO,EAAE;gBAAmB,CAAE;gBAAA9C,QAAA,EACtL4D,iBAAiB,CAACZ,GAAG,CAAEjH,MAAM,iBAC5BzC,OAAA,CAACjC,QAAQ;kBAEPuN,cAAc;kBACdC,eAAe,eACbvL,OAAA,CAACpC,UAAU;oBACT4N,IAAI,EAAC,KAAK;oBACV/D,IAAI,EAAC,OAAO;oBACZO,OAAO,EAAEA,CAAA,KAAM7C,kBAAkB,CAAC1C,MAAM,CAAC6C,SAAS,CAAE;oBAAAoB,QAAA,eAEpD1G,OAAA,CAACjB,oBAAoB;sBAACgJ,KAAK,EAAC;oBAAS;sBAAAhB,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAE;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAC9B,CACb;kBACDN,EAAE,EAAE;oBACF4C,OAAO,EAAExH,gBAAgB,KAAKS,MAAM,CAAC6C,SAAS,GAAG,yBAAyB,GAAG,SAAS;oBACtF6F,YAAY,EAAE,KAAK;oBACnBtE,EAAE,EAAE,GAAG;oBACP4E,MAAM,EAAEzJ,gBAAgB,KAAKS,MAAM,CAAC6C,SAAS,GAAG,mBAAmB,GAAG;kBACxE,CAAE;kBAAAoB,QAAA,eAEF1G,OAAA,CAAC/B,cAAc;oBACbyN,KAAK;oBACL1D,OAAO,EAAEA,CAAA,KAAM7C,kBAAkB,CAAC1C,MAAM,CAAC6C,SAAS,CAAE;oBAAAoB,QAAA,eAEpD1G,OAAA,CAAC9D,GAAG;sBAAC0K,EAAE,EAAE;wBAAES,OAAO,EAAE,MAAM;wBAAEC,UAAU,EAAE,QAAQ;wBAAEF,KAAK,EAAE,MAAM;wBAAE2B,EAAE,EAAE;sBAAI,CAAE;sBAAArC,QAAA,gBACzE1G,OAAA,CAAC9D,GAAG;wBAAC0K,EAAE,EAAE;0BAAEQ,KAAK,EAAE,MAAM;0BAAEG,EAAE,EAAE;wBAAE,CAAE;wBAAAb,QAAA,eAChC1G,OAAA,CAAC5D,UAAU;0BAACuK,OAAO,EAAC,OAAO;0BAACC,EAAE,EAAE;4BAAEE,UAAU,EAAE,MAAM;4BAAEqB,QAAQ,EAAE;0BAAS,CAAE;0BAAAzB,QAAA,EACxEd,eAAe,CAACnD,MAAM,CAAC6C,SAAS;wBAAC;0BAAAyB,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OACxB;sBAAC;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACV,CAAC,eACNlH,OAAA,CAAC9D,GAAG;wBAAC0K,EAAE,EAAE;0BAAEQ,KAAK,EAAE,OAAO;0BAAEG,EAAE,EAAE;wBAAE,CAAE;wBAAAb,QAAA,eACjC1G,OAAA,CAAC5D,UAAU;0BAACuK,OAAO,EAAC,OAAO;0BAACC,EAAE,EAAE;4BAAEuB,QAAQ,EAAE;0BAAU,CAAE;0BAAAzB,QAAA,EACrDjE,MAAM,CAAC+B,SAAS,IAAI;wBAAK;0BAAAuC,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAChB;sBAAC;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACV,CAAC,eACNlH,OAAA,CAAC9D,GAAG;wBAAC0K,EAAE,EAAE;0BAAEQ,KAAK,EAAE,OAAO;0BAAEG,EAAE,EAAE;wBAAE,CAAE;wBAAAb,QAAA,eACjC1G,OAAA,CAAC5D,UAAU;0BAACuK,OAAO,EAAC,OAAO;0BAACC,EAAE,EAAE;4BAAEuB,QAAQ,EAAE;0BAAU,CAAE;0BAAAzB,QAAA,EACrDjE,MAAM,CAACgC,OAAO,IAAI;wBAAK;0BAAAsC,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OACd;sBAAC;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACV,CAAC,eACNlH,OAAA,CAAC9D,GAAG;wBAAC0K,EAAE,EAAE;0BAAEQ,KAAK,EAAE,OAAO;0BAAEG,EAAE,EAAE;wBAAE,CAAE;wBAAAb,QAAA,eACjC1G,OAAA,CAAC5D,UAAU;0BAACuK,OAAO,EAAC,OAAO;0BAACC,EAAE,EAAE;4BAAEE,UAAU,EAAE,MAAM;4BAAEqB,QAAQ,EAAE,SAAS;4BAAEJ,KAAK,EAAE;0BAAe,CAAE;0BAAArB,QAAA,GAChGjE,MAAM,CAACwG,aAAa,IAAI,CAAC,EAAC,IAC7B;wBAAA;0BAAAlC,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAY;sBAAC;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACV,CAAC,eACNlH,OAAA,CAAC9D,GAAG;wBAAC0K,EAAE,EAAE;0BAAEkB,QAAQ,EAAE;wBAAE,CAAE;wBAAApB,QAAA,eACvB1G,OAAA,CAAC7B,IAAI;0BACHsJ,IAAI,EAAC,OAAO;0BACZC,KAAK,EAAEjF,MAAM,CAACiC,YAAY,IAAI,KAAM;0BACpCqD,KAAK,EAAEjI,iBAAiB,CAAC2C,MAAM,CAACiC,YAAY,CAAE;0BAC9CiC,OAAO,EAAC,UAAU;0BAClBC,EAAE,EAAE;4BAAEwB,MAAM,EAAE,EAAE;4BAAED,QAAQ,EAAE,QAAQ;4BAAE,kBAAkB,EAAE;8BAAEW,EAAE,EAAE,CAAC;8BAAEC,EAAE,EAAE;4BAAE;0BAAE;wBAAE;0BAAAhC,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAC9E;sBAAC;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACC,CAAC;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACH;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACQ;gBAAC,GArDZzE,MAAM,CAAC6C,SAAS;kBAAAyB,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAsDb,CACX;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACE,CAAC;YAAA,eACP,CAAC,gBAEHlH,OAAA,CAACnD,KAAK;cAACuM,QAAQ,EAAC,MAAM;cAACxC,EAAE,EAAE;gBAAEsD,EAAE,EAAE;cAAE,CAAE;cAAAxD,QAAA,EAAC;YAEtC;cAAAK,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CACR;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACI;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACJ,CAAC,eAGPlH,OAAA,CAACzD,IAAI;UAACyO,IAAI;UAACC,EAAE,EAAE,EAAG;UAACC,EAAE,EAAE,CAAE;UAAAxE,QAAA,eACvB1G,OAAA,CAAC7D,KAAK;YAACwK,OAAO,EAAC,UAAU;YAACC,EAAE,EAAE;cAAEO,CAAC,EAAE,CAAC;cAAEiB,MAAM,EAAE;YAAO,CAAE;YAAA1B,QAAA,gBACrD1G,OAAA,CAAC5D,UAAU;cAACuK,OAAO,EAAC,WAAW;cAACC,EAAE,EAAE;gBAAEE,UAAU,EAAE,MAAM;gBAAED,EAAE,EAAE;cAAE,CAAE;cAAAH,QAAA,EAAC;YAEnE;cAAAK,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAY,CAAC,EAEZqD,oBAAoB,CAACnH,MAAM,GAAG,CAAC,gBAC9BpD,OAAA,CAAAE,SAAA;cAAAwG,QAAA,gBACE1G,OAAA,CAAC9D,GAAG;gBAAC0K,EAAE,EAAE;kBAAES,OAAO,EAAE,MAAM;kBAAEC,UAAU,EAAE,QAAQ;kBAAEF,KAAK,EAAE,MAAM;kBAAE2B,EAAE,EAAE,GAAG;kBAAED,EAAE,EAAE,GAAG;kBAAEU,OAAO,EAAE,SAAS;kBAAE2B,YAAY,EAAE,CAAC;kBAAEtE,EAAE,EAAE;gBAAE,CAAE;gBAAAH,QAAA,gBAC9H1G,OAAA,CAAC9D,GAAG;kBAAC0K,EAAE,EAAE;oBAAEQ,KAAK,EAAE,MAAM;oBAAEG,EAAE,EAAE;kBAAE,CAAE;kBAAAb,QAAA,eAChC1G,OAAA,CAAC5D,UAAU;oBAACuK,OAAO,EAAC,SAAS;oBAACC,EAAE,EAAE;sBAAEE,UAAU,EAAE,MAAM;sBAAEqB,QAAQ,EAAE;oBAAU,CAAE;oBAAAzB,QAAA,EAAC;kBAAE;oBAAAK,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAY;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAC3F,CAAC,eACNlH,OAAA,CAAC9D,GAAG;kBAAC0K,EAAE,EAAE;oBAAEQ,KAAK,EAAE,OAAO;oBAAEG,EAAE,EAAE;kBAAE,CAAE;kBAAAb,QAAA,eACjC1G,OAAA,CAAC5D,UAAU;oBAACuK,OAAO,EAAC,SAAS;oBAACC,EAAE,EAAE;sBAAEE,UAAU,EAAE,MAAM;sBAAEqB,QAAQ,EAAE;oBAAU,CAAE;oBAAAzB,QAAA,EAAC;kBAAI;oBAAAK,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAY;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAC7F,CAAC,eACNlH,OAAA,CAAC9D,GAAG;kBAAC0K,EAAE,EAAE;oBAAEQ,KAAK,EAAE,OAAO;oBAAEG,EAAE,EAAE;kBAAE,CAAE;kBAAAb,QAAA,eACjC1G,OAAA,CAAC5D,UAAU;oBAACuK,OAAO,EAAC,SAAS;oBAACC,EAAE,EAAE;sBAAEE,UAAU,EAAE,MAAM;sBAAEqB,QAAQ,EAAE;oBAAU,CAAE;oBAAAzB,QAAA,EAAC;kBAAK;oBAAAK,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAY;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAC9F,CAAC,eACNlH,OAAA,CAAC9D,GAAG;kBAAC0K,EAAE,EAAE;oBAAEQ,KAAK,EAAE,OAAO;oBAAEG,EAAE,EAAE;kBAAE,CAAE;kBAAAb,QAAA,eACjC1G,OAAA,CAAC5D,UAAU;oBAACuK,OAAO,EAAC,SAAS;oBAACC,EAAE,EAAE;sBAAEE,UAAU,EAAE,MAAM;sBAAEqB,QAAQ,EAAE;oBAAU,CAAE;oBAAAzB,QAAA,EAAC;kBAAO;oBAAAK,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAY;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAChG,CAAC,eACNlH,OAAA,CAAC9D,GAAG;kBAAC0K,EAAE,EAAE;oBAAEkB,QAAQ,EAAE;kBAAE,CAAE;kBAAApB,QAAA,eACvB1G,OAAA,CAAC5D,UAAU;oBAACuK,OAAO,EAAC,SAAS;oBAACC,EAAE,EAAE;sBAAEE,UAAU,EAAE,MAAM;sBAAEqB,QAAQ,EAAE;oBAAU,CAAE;oBAAAzB,QAAA,EAAC;kBAAK;oBAAAK,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAY;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAC9F,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CAAC,eACNlH,OAAA,CAAClC,IAAI;gBAAC8I,EAAE,EAAE;kBAAE0C,SAAS,EAAEiB,oBAAoB,CAACnH,MAAM,GAAG,CAAC,GAAG,OAAO,GAAG,MAAM;kBAAEgI,SAAS,EAAEb,oBAAoB,CAACnH,MAAM,GAAG,CAAC,GAAG,MAAM,GAAG,SAAS;kBAAEiI,SAAS,EAAE,QAAQ;kBAAE7B,OAAO,EAAE;gBAAmB,CAAE;gBAAA9C,QAAA,EAC5L6D,oBAAoB,CAACb,GAAG,CAAEjH,MAAM,iBAC/BzC,OAAA,CAACjC,QAAQ;kBAEPuN,cAAc;kBACdC,eAAe,eACbvL,OAAA,CAACpC,UAAU;oBACT4N,IAAI,EAAC,KAAK;oBACV/D,IAAI,EAAC,OAAO;oBACZO,OAAO,EAAEA,CAAA,KAAM7C,kBAAkB,CAAC1C,MAAM,CAAC6C,SAAS,CAAE;oBAAAoB,QAAA,eAEpD1G,OAAA,CAACjB,oBAAoB;sBAACgJ,KAAK,EAAC;oBAAS;sBAAAhB,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAE;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAC9B,CACb;kBACDN,EAAE,EAAE;oBACF4C,OAAO,EAAExH,gBAAgB,KAAKS,MAAM,CAAC6C,SAAS,GAAG,yBAAyB,GAAG,SAAS;oBACtF6F,YAAY,EAAE,KAAK;oBACnBtE,EAAE,EAAE,GAAG;oBACP4E,MAAM,EAAEzJ,gBAAgB,KAAKS,MAAM,CAAC6C,SAAS,GAAG,mBAAmB,GAAG;kBACxE,CAAE;kBAAAoB,QAAA,eAEF1G,OAAA,CAAC/B,cAAc;oBACbyN,KAAK;oBACL1D,OAAO,EAAEA,CAAA,KAAM7C,kBAAkB,CAAC1C,MAAM,CAAC6C,SAAS,CAAE;oBAAAoB,QAAA,eAEpD1G,OAAA,CAAC9D,GAAG;sBAAC0K,EAAE,EAAE;wBAAES,OAAO,EAAE,MAAM;wBAAEC,UAAU,EAAE,QAAQ;wBAAEF,KAAK,EAAE,MAAM;wBAAE2B,EAAE,EAAE;sBAAI,CAAE;sBAAArC,QAAA,gBACzE1G,OAAA,CAAC9D,GAAG;wBAAC0K,EAAE,EAAE;0BAAEQ,KAAK,EAAE,MAAM;0BAAEG,EAAE,EAAE;wBAAE,CAAE;wBAAAb,QAAA,eAChC1G,OAAA,CAAC5D,UAAU;0BAACuK,OAAO,EAAC,OAAO;0BAACC,EAAE,EAAE;4BAAEE,UAAU,EAAE,MAAM;4BAAEqB,QAAQ,EAAE;0BAAS,CAAE;0BAAAzB,QAAA,EACxEd,eAAe,CAACnD,MAAM,CAAC6C,SAAS;wBAAC;0BAAAyB,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OACxB;sBAAC;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACV,CAAC,eACNlH,OAAA,CAAC9D,GAAG;wBAAC0K,EAAE,EAAE;0BAAEQ,KAAK,EAAE,OAAO;0BAAEG,EAAE,EAAE;wBAAE,CAAE;wBAAAb,QAAA,eACjC1G,OAAA,CAAC5D,UAAU;0BAACuK,OAAO,EAAC,OAAO;0BAACC,EAAE,EAAE;4BAAEuB,QAAQ,EAAE;0BAAU,CAAE;0BAAAzB,QAAA,EACrDjE,MAAM,CAAC+B,SAAS,IAAI;wBAAK;0BAAAuC,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAChB;sBAAC;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACV,CAAC,eACNlH,OAAA,CAAC9D,GAAG;wBAAC0K,EAAE,EAAE;0BAAEQ,KAAK,EAAE,OAAO;0BAAEG,EAAE,EAAE;wBAAE,CAAE;wBAAAb,QAAA,eACjC1G,OAAA,CAAC5D,UAAU;0BAACuK,OAAO,EAAC,OAAO;0BAACC,EAAE,EAAE;4BAAEuB,QAAQ,EAAE;0BAAU,CAAE;0BAAAzB,QAAA,EACrDjE,MAAM,CAACgC,OAAO,IAAI;wBAAK;0BAAAsC,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OACd;sBAAC;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACV,CAAC,eACNlH,OAAA,CAAC9D,GAAG;wBAAC0K,EAAE,EAAE;0BAAEQ,KAAK,EAAE,OAAO;0BAAEG,EAAE,EAAE;wBAAE,CAAE;wBAAAb,QAAA,eACjC1G,OAAA,CAAC5D,UAAU;0BAACuK,OAAO,EAAC,OAAO;0BAACC,EAAE,EAAE;4BAAEE,UAAU,EAAE,MAAM;4BAAEqB,QAAQ,EAAE,SAAS;4BAAEJ,KAAK,EAAE;0BAAe,CAAE;0BAAArB,QAAA,GAChGjE,MAAM,CAACwG,aAAa,IAAI,CAAC,EAAC,IAC7B;wBAAA;0BAAAlC,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAY;sBAAC;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACV,CAAC,eACNlH,OAAA,CAAC9D,GAAG;wBAAC0K,EAAE,EAAE;0BAAES,OAAO,EAAE,MAAM;0BAAEwB,GAAG,EAAE;wBAAE,CAAE;wBAAAnC,QAAA,gBACnC1G,OAAA,CAAC7B,IAAI;0BACHsJ,IAAI,EAAC,OAAO;0BACZC,KAAK,EAAEjF,MAAM,CAACiC,YAAY,IAAI,KAAM;0BACpCqD,KAAK,EAAEjI,iBAAiB,CAAC2C,MAAM,CAACiC,YAAY,CAAE;0BAC9CiC,OAAO,EAAC,UAAU;0BAClBC,EAAE,EAAE;4BAAEwB,MAAM,EAAE,EAAE;4BAAED,QAAQ,EAAE,QAAQ;4BAAE,kBAAkB,EAAE;8BAAEW,EAAE,EAAE,CAAC;8BAAEC,EAAE,EAAE;4BAAE;0BAAE;wBAAE;0BAAAhC,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAC9E,CAAC,eACFlH,OAAA,CAAC7B,IAAI;0BACHsJ,IAAI,EAAC,OAAO;0BACZC,KAAK,EAAC,WAAW;0BACjBK,KAAK,EAAC,SAAS;0BACfpB,OAAO,EAAC,UAAU;0BAClBC,EAAE,EAAE;4BAAEwB,MAAM,EAAE,EAAE;4BAAED,QAAQ,EAAE,QAAQ;4BAAE,kBAAkB,EAAE;8BAAEW,EAAE,EAAE,CAAC;8BAAEC,EAAE,EAAE;4BAAE;0BAAE;wBAAE;0BAAAhC,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAC9E,CAAC;sBAAA;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACC,CAAC;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACH;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACQ;gBAAC,GA5DZzE,MAAM,CAAC6C,SAAS;kBAAAyB,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OA6Db,CACX;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACE,CAAC;YAAA,eACP,CAAC,gBAEHlH,OAAA,CAACnD,KAAK;cAACuM,QAAQ,EAAC,MAAM;cAACxC,EAAE,EAAE;gBAAEsD,EAAE,EAAE;cAAE,CAAE;cAAAxD,QAAA,EAAC;YAEtC;cAAAK,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CACR;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACI;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACJ,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,EAGNoD,iBAAiB,CAAClH,MAAM,KAAK,CAAC,IAAImH,oBAAoB,CAACnH,MAAM,KAAK,CAAC,iBAClEpD,OAAA,CAACnD,KAAK;QAACuM,QAAQ,EAAC,MAAM;QAACxC,EAAE,EAAE;UAAEsD,EAAE,EAAE;QAAE,CAAE;QAAAxD,QAAA,EAClCxE,gBAAgB,GAAG,8DAA8D,GAAG;MAAiC;QAAA6E,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACjH,CACR;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACE,CAAC;EAEV,CAAC;EAED,oBACElH,OAAA,CAAC9D,GAAG;IAAAwK,QAAA,GAEDD,uBAAuB,CAAC,CAAC,EAGzBqD,qBAAqB,CAAC,CAAC,eAGxB9J,OAAA,CAACjD,MAAM;MACL4O,IAAI,EAAE3I,qBAAsB;MAC5B4I,OAAO,EAAEA,CAAA,KAAM3I,wBAAwB,CAAC,KAAK,CAAE;MAC/C4I,QAAQ,EAAC,IAAI;MACbnB,SAAS;MAAAhE,QAAA,gBAET1G,OAAA,CAAChD,WAAW;QAAA0J,QAAA,EAAC;MAA0B;QAAAK,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAa,CAAC,eACrDlH,OAAA,CAAC/C,aAAa;QAAAyJ,QAAA,EACXhF,YAAY,iBAAI1B,OAAA,CAACX,eAAe;UAACmD,IAAI,EAAEd;QAAa;UAAAqF,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC3C,CAAC,eAChBlH,OAAA,CAAC9C,aAAa;QAAAwJ,QAAA,eACZ1G,OAAA,CAAC1D,MAAM;UAAC0L,OAAO,EAAEA,CAAA,KAAM/E,wBAAwB,CAAC,KAAK,CAAE;UAAAyD,QAAA,EAAC;QAAM;UAAAK,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC1D,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACV,CAAC,eAGTlH,OAAA,CAACjD,MAAM;MACL4O,IAAI,EAAEjJ,iBAAkB;MACxBkJ,OAAO,EAAEA,CAAA,KAAMjJ,oBAAoB,CAAC,KAAK,CAAE;MAAA+D,QAAA,gBAE3C1G,OAAA,CAAChD,WAAW;QAAA0J,QAAA,EAAC;MAAmB;QAAAK,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAa,CAAC,eAC9ClH,OAAA,CAAC/C,aAAa;QAAAyJ,QAAA,eACZ1G,OAAA,CAAC5D,UAAU;UAAAsK,QAAA,EAAE9D;QAAoB;UAAAmE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAa;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAClC,CAAC,eAChBlH,OAAA,CAAC9C,aAAa;QAAAwJ,QAAA,gBACZ1G,OAAA,CAAC1D,MAAM;UACL0L,OAAO,EAAEA,CAAA,KAAMrF,oBAAoB,CAAC,KAAK,CAAE;UAC3CoF,KAAK,EAAC,SAAS;UAAArB,QAAA,EAChB;QAED;UAAAK,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC,eACTlH,OAAA,CAAC1D,MAAM;UACL0L,OAAO,EAAEA,CAAA,KAAM;YACbrF,oBAAoB,CAAC,KAAK,CAAC;YAC3B,IAAIG,mBAAmB,EAAEA,mBAAmB,CAAC,CAAC;UAChD,CAAE;UACFiF,KAAK,EAAC,SAAS;UACfpB,OAAO,EAAC,WAAW;UACnBmF,SAAS;UAAApF,QAAA,EACV;QAED;UAAAK,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACI,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACV,CAAC,eAGTlH,OAAA,CAAChB,sBAAsB;MACrB2M,IAAI,EAAEvJ,0BAA2B;MACjCwJ,OAAO,EAAE3F,iCAAkC;MAC3CzD,IAAI,EAAEF,oBAAoB,CAACE,IAAK;MAChCC,MAAM,EAAEH,oBAAoB,CAACG,MAAO;MACpCsJ,YAAY,EAAEtG,gCAAiC;MAC/CuG,mBAAmB,EAAE/F,iCAAkC;MACvDgG,0BAA0B,EAAE5F;IAA+B;MAAAU,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAC5D,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACC,CAAC;AAEV,CAAC;AAAC3G,EAAA,CA9iCIJ,kBAAkB;EAAA,QACLlB,WAAW,EACTC,SAAS;AAAA;AAAAgN,EAAA,GAFxB/L,kBAAkB;AAgjCxB,eAAeA,kBAAkB;AAAC,IAAA+L,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}