{"ast": null, "code": "import axios from 'axios';\nconst API_URL = 'http://localhost:8001/api';\n\n// Crea un'istanza di axios con configurazione personalizzata\nconst axiosInstance = axios.create({\n  baseURL: API_URL,\n  headers: {\n    'Content-Type': 'application/json'\n  }\n});\n\n// Configura axios per includere il token in tutte le richieste\naxiosInstance.interceptors.request.use(config => {\n  const token = localStorage.getItem('token');\n  if (token) {\n    config.headers.Authorization = `Bearer ${token}`;\n  }\n  return config;\n}, error => {\n  return Promise.reject(error);\n});\n\n// Gestione degli errori di autenticazione\naxiosInstance.interceptors.response.use(response => response, error => {\n  console.error('Errore nella risposta API:', error);\n  if (error.response && error.response.status === 401) {\n    // Se la risposta è 401 Unauthorized, effettua il logout\n    console.log('Errore 401 rilevato, rimozione token');\n    localStorage.removeItem('token');\n\n    // Pulisci eventuali selezioni di cantiere precedenti\n    localStorage.removeItem('selectedCantiereId');\n    localStorage.removeItem('selectedCantiereName');\n    console.log('Rimossi dati cantiere precedenti dal localStorage');\n\n    // Non reindirizzare automaticamente, lascia che sia il componente React a gestire il reindirizzamento\n    // Questo evita loop di reindirizzamento\n  }\n  return Promise.reject(error);\n});\nconst authService = {\n  // Login standard (admin o utente standard)\n  login: async (credentials, loginType) => {\n    try {\n      console.log(`Tentativo di login ${loginType} con API_URL: ${API_URL}`);\n      if (loginType === 'standard') {\n        // Converti le credenziali nel formato richiesto da OAuth2\n        const formData = new FormData();\n        formData.append('username', credentials.username);\n        formData.append('password', credentials.password);\n        console.log(`Invio richiesta POST a ${API_URL}/auth/login`);\n        // Usa axios direttamente per il login perché richiede FormData\n        const response = await axios.post(`${API_URL}/auth/login`, formData, {\n          headers: {\n            'Content-Type': 'multipart/form-data'\n          }\n        });\n        console.log('Risposta ricevuta:', response);\n        return response.data;\n      } else if (loginType === 'cantiere') {\n        // Login cantiere\n        console.log(`Invio richiesta POST a ${API_URL}/auth/login/cantiere`);\n        const response = await axiosInstance.post('/auth/login/cantiere', {\n          codice_univoco: credentials.codice_univoco,\n          password: credentials.password\n        });\n        console.log('Risposta ricevuta:', response);\n\n        // Salva l'ID e il nome del cantiere nel localStorage\n        if (response.data.cantiere_id) {\n          console.log('Salvando ID cantiere nel localStorage:', response.data.cantiere_id);\n          localStorage.setItem('selectedCantiereId', response.data.cantiere_id.toString());\n          localStorage.setItem('selectedCantiereName', response.data.cantiere_name || `Cantiere ${response.data.cantiere_id}`);\n        } else {\n          console.warn('Risposta login cantiere non contiene cantiere_id:', response.data);\n        }\n        return response.data;\n      } else {\n        throw new Error('Tipo di login non valido');\n      }\n    } catch (error) {\n      console.error('Login error:', error);\n      if (error.response) {\n        console.error('Dettagli errore:', error.response.status, error.response.data);\n        throw error.response.data;\n      } else if (error.request) {\n        console.error('Nessuna risposta ricevuta:', error.request);\n        throw {\n          detail: 'Errore di connessione al server. Verifica che il backend sia in esecuzione.'\n        };\n      } else {\n        console.error('Errore durante la configurazione della richiesta:', error.message);\n        throw {\n          detail: error.message\n        };\n      }\n    }\n  },\n  // Verifica la validità del token\n  checkToken: async () => {\n    try {\n      console.log('Verifica token in corso...');\n      const response = await axiosInstance.post('/auth/test-token');\n      console.log('Risposta verifica token:', response.data);\n\n      // Controlla se l'utente è impersonato da un admin\n      // Questo valore viene ora impostato dal backend nel token JWT\n      const isImpersonated = response.data.is_impersonated === true;\n\n      // Se l'utente è impersonato, salva lo stato nel localStorage\n      if (isImpersonated) {\n        console.log('Utente impersonato da admin, salvataggio stato nel localStorage');\n        localStorage.setItem('isImpersonating', 'true');\n      } else {\n        // Altrimenti, assicurati che non ci sia uno stato di impersonificazione salvato\n        localStorage.removeItem('isImpersonating');\n      }\n\n      // Costruisci l'oggetto utente con i dati dal token\n      const userData = {\n        id: response.data.user_id,\n        username: response.data.username,\n        role: response.data.role,\n        isImpersonated: isImpersonated\n      };\n\n      // Se l'utente è un utente cantiere, aggiungi i dati del cantiere\n      if (response.data.role === 'cantieri_user' && response.data.cantiere_id) {\n        userData.cantiere_id = response.data.cantiere_id;\n        userData.cantiere_name = response.data.cantiere_name || `Cantiere ${response.data.cantiere_id}`;\n\n        // Salva l'ID e il nome del cantiere nel localStorage\n        localStorage.setItem('selectedCantiereId', response.data.cantiere_id.toString());\n        localStorage.setItem('selectedCantiereName', userData.cantiere_name);\n        console.log('Salvati dati cantiere nel localStorage durante checkToken:', {\n          cantiere_id: response.data.cantiere_id,\n          cantiere_name: userData.cantiere_name\n        });\n      }\n\n      // Se l'utente è impersonato, aggiungi i dati dell'utente impersonato\n      if (isImpersonated && response.data.impersonated_id) {\n        // Salva i dati dell'utente impersonato nel localStorage\n        const impersonatedUserData = {\n          id: response.data.impersonated_id,\n          username: response.data.impersonated_username,\n          role: response.data.impersonated_role\n        };\n        localStorage.setItem('impersonatedUser', JSON.stringify(impersonatedUserData));\n      }\n      return userData;\n    } catch (error) {\n      console.error('Check token error:', error);\n      // Pulisci il localStorage per evitare loop\n      localStorage.removeItem('token');\n      localStorage.removeItem('isImpersonating');\n\n      // Pulisci eventuali selezioni di cantiere precedenti\n      localStorage.removeItem('selectedCantiereId');\n      localStorage.removeItem('selectedCantiereName');\n      console.log('Rimossi dati cantiere precedenti dal localStorage');\n      throw error.response ? error.response.data : error;\n    }\n  },\n  // Impersona un altro utente (solo per admin)\n  impersonateUser: async userId => {\n    try {\n      const response = await axiosInstance.post('/auth/impersonate', {\n        user_id: userId\n      });\n      return response.data;\n    } catch (error) {\n      console.error('Impersonate user error:', error);\n      throw error.response ? error.response.data : error;\n    }\n  }\n};\nexport default authService;", "map": {"version": 3, "names": ["axios", "API_URL", "axiosInstance", "create", "baseURL", "headers", "interceptors", "request", "use", "config", "token", "localStorage", "getItem", "Authorization", "error", "Promise", "reject", "response", "console", "status", "log", "removeItem", "authService", "login", "credentials", "loginType", "formData", "FormData", "append", "username", "password", "post", "data", "codice_univoco", "cantiere_id", "setItem", "toString", "cantiere_name", "warn", "Error", "detail", "message", "checkToken", "isImpersonated", "is_impersonated", "userData", "id", "user_id", "role", "impersonated_id", "impersonated<PERSON><PERSON><PERSON><PERSON>", "impersonated_username", "impersonated_role", "JSON", "stringify", "impersonate<PERSON><PERSON>", "userId"], "sources": ["C:/Users/<USER>/PycharmProjects/CMS/webapp/frontend/src/services/authService.js"], "sourcesContent": ["import axios from 'axios';\r\n\r\nconst API_URL = 'http://localhost:8001/api';\r\n\r\n// Crea un'istanza di axios con configurazione personalizzata\r\nconst axiosInstance = axios.create({\r\n  baseURL: API_URL,\r\n  headers: {\r\n    'Content-Type': 'application/json'\r\n  }\r\n});\r\n\r\n// Configura axios per includere il token in tutte le richieste\r\naxiosInstance.interceptors.request.use(\r\n  (config) => {\r\n    const token = localStorage.getItem('token');\r\n    if (token) {\r\n      config.headers.Authorization = `Bearer ${token}`;\r\n    }\r\n    return config;\r\n  },\r\n  (error) => {\r\n    return Promise.reject(error);\r\n  }\r\n);\r\n\r\n// Gestione degli errori di autenticazione\r\naxiosInstance.interceptors.response.use(\r\n  (response) => response,\r\n  (error) => {\r\n    console.error('Errore nella risposta API:', error);\r\n    if (error.response && error.response.status === 401) {\r\n      // Se la risposta è 401 Unauthorized, effettua il logout\r\n      console.log('Errore 401 rilevato, rimozione token');\r\n      localStorage.removeItem('token');\r\n\r\n      // Pulisci eventuali selezioni di cantiere precedenti\r\n      localStorage.removeItem('selectedCantiereId');\r\n      localStorage.removeItem('selectedCantiereName');\r\n      console.log('Rimossi dati cantiere precedenti dal localStorage');\r\n\r\n      // Non reindirizzare automaticamente, lascia che sia il componente React a gestire il reindirizzamento\r\n      // Questo evita loop di reindirizzamento\r\n    }\r\n    return Promise.reject(error);\r\n  }\r\n);\r\n\r\nconst authService = {\r\n  // Login standard (admin o utente standard)\r\n  login: async (credentials, loginType) => {\r\n    try {\r\n      console.log(`Tentativo di login ${loginType} con API_URL: ${API_URL}`);\r\n\r\n      if (loginType === 'standard') {\r\n        // Converti le credenziali nel formato richiesto da OAuth2\r\n        const formData = new FormData();\r\n        formData.append('username', credentials.username);\r\n        formData.append('password', credentials.password);\r\n\r\n        console.log(`Invio richiesta POST a ${API_URL}/auth/login`);\r\n        // Usa axios direttamente per il login perché richiede FormData\r\n        const response = await axios.post(`${API_URL}/auth/login`, formData, {\r\n          headers: {\r\n            'Content-Type': 'multipart/form-data',\r\n          },\r\n        });\r\n        console.log('Risposta ricevuta:', response);\r\n        return response.data;\r\n      } else if (loginType === 'cantiere') {\r\n        // Login cantiere\r\n        console.log(`Invio richiesta POST a ${API_URL}/auth/login/cantiere`);\r\n        const response = await axiosInstance.post('/auth/login/cantiere', {\r\n          codice_univoco: credentials.codice_univoco,\r\n          password: credentials.password\r\n        });\r\n        console.log('Risposta ricevuta:', response);\r\n\r\n        // Salva l'ID e il nome del cantiere nel localStorage\r\n        if (response.data.cantiere_id) {\r\n          console.log('Salvando ID cantiere nel localStorage:', response.data.cantiere_id);\r\n          localStorage.setItem('selectedCantiereId', response.data.cantiere_id.toString());\r\n          localStorage.setItem('selectedCantiereName', response.data.cantiere_name || `Cantiere ${response.data.cantiere_id}`);\r\n        } else {\r\n          console.warn('Risposta login cantiere non contiene cantiere_id:', response.data);\r\n        }\r\n\r\n        return response.data;\r\n      } else {\r\n        throw new Error('Tipo di login non valido');\r\n      }\r\n    } catch (error) {\r\n      console.error('Login error:', error);\r\n      if (error.response) {\r\n        console.error('Dettagli errore:', error.response.status, error.response.data);\r\n        throw error.response.data;\r\n      } else if (error.request) {\r\n        console.error('Nessuna risposta ricevuta:', error.request);\r\n        throw { detail: 'Errore di connessione al server. Verifica che il backend sia in esecuzione.' };\r\n      } else {\r\n        console.error('Errore durante la configurazione della richiesta:', error.message);\r\n        throw { detail: error.message };\r\n      }\r\n    }\r\n  },\r\n\r\n\r\n\r\n  // Verifica la validità del token\r\n  checkToken: async () => {\r\n    try {\r\n      console.log('Verifica token in corso...');\r\n      const response = await axiosInstance.post('/auth/test-token');\r\n      console.log('Risposta verifica token:', response.data);\r\n\r\n      // Controlla se l'utente è impersonato da un admin\r\n      // Questo valore viene ora impostato dal backend nel token JWT\r\n      const isImpersonated = response.data.is_impersonated === true;\r\n\r\n      // Se l'utente è impersonato, salva lo stato nel localStorage\r\n      if (isImpersonated) {\r\n        console.log('Utente impersonato da admin, salvataggio stato nel localStorage');\r\n        localStorage.setItem('isImpersonating', 'true');\r\n      } else {\r\n        // Altrimenti, assicurati che non ci sia uno stato di impersonificazione salvato\r\n        localStorage.removeItem('isImpersonating');\r\n      }\r\n\r\n      // Costruisci l'oggetto utente con i dati dal token\r\n      const userData = {\r\n        id: response.data.user_id,\r\n        username: response.data.username,\r\n        role: response.data.role,\r\n        isImpersonated: isImpersonated\r\n      };\r\n\r\n      // Se l'utente è un utente cantiere, aggiungi i dati del cantiere\r\n      if (response.data.role === 'cantieri_user' && response.data.cantiere_id) {\r\n        userData.cantiere_id = response.data.cantiere_id;\r\n        userData.cantiere_name = response.data.cantiere_name || `Cantiere ${response.data.cantiere_id}`;\r\n\r\n        // Salva l'ID e il nome del cantiere nel localStorage\r\n        localStorage.setItem('selectedCantiereId', response.data.cantiere_id.toString());\r\n        localStorage.setItem('selectedCantiereName', userData.cantiere_name);\r\n        console.log('Salvati dati cantiere nel localStorage durante checkToken:', {\r\n          cantiere_id: response.data.cantiere_id,\r\n          cantiere_name: userData.cantiere_name\r\n        });\r\n      }\r\n\r\n      // Se l'utente è impersonato, aggiungi i dati dell'utente impersonato\r\n      if (isImpersonated && response.data.impersonated_id) {\r\n        // Salva i dati dell'utente impersonato nel localStorage\r\n        const impersonatedUserData = {\r\n          id: response.data.impersonated_id,\r\n          username: response.data.impersonated_username,\r\n          role: response.data.impersonated_role\r\n        };\r\n\r\n        localStorage.setItem('impersonatedUser', JSON.stringify(impersonatedUserData));\r\n      }\r\n\r\n      return userData;\r\n    } catch (error) {\r\n      console.error('Check token error:', error);\r\n      // Pulisci il localStorage per evitare loop\r\n      localStorage.removeItem('token');\r\n      localStorage.removeItem('isImpersonating');\r\n\r\n      // Pulisci eventuali selezioni di cantiere precedenti\r\n      localStorage.removeItem('selectedCantiereId');\r\n      localStorage.removeItem('selectedCantiereName');\r\n      console.log('Rimossi dati cantiere precedenti dal localStorage');\r\n      throw error.response ? error.response.data : error;\r\n    }\r\n  },\r\n\r\n  // Impersona un altro utente (solo per admin)\r\n  impersonateUser: async (userId) => {\r\n    try {\r\n      const response = await axiosInstance.post('/auth/impersonate', {\r\n        user_id: userId\r\n      });\r\n      return response.data;\r\n    } catch (error) {\r\n      console.error('Impersonate user error:', error);\r\n      throw error.response ? error.response.data : error;\r\n    }\r\n  }\r\n};\r\n\r\nexport default authService;\r\n"], "mappings": "AAAA,OAAOA,KAAK,MAAM,OAAO;AAEzB,MAAMC,OAAO,GAAG,2BAA2B;;AAE3C;AACA,MAAMC,aAAa,GAAGF,KAAK,CAACG,MAAM,CAAC;EACjCC,OAAO,EAAEH,OAAO;EAChBI,OAAO,EAAE;IACP,cAAc,EAAE;EAClB;AACF,CAAC,CAAC;;AAEF;AACAH,aAAa,CAACI,YAAY,CAACC,OAAO,CAACC,GAAG,CACnCC,MAAM,IAAK;EACV,MAAMC,KAAK,GAAGC,YAAY,CAACC,OAAO,CAAC,OAAO,CAAC;EAC3C,IAAIF,KAAK,EAAE;IACTD,MAAM,CAACJ,OAAO,CAACQ,aAAa,GAAG,UAAUH,KAAK,EAAE;EAClD;EACA,OAAOD,MAAM;AACf,CAAC,EACAK,KAAK,IAAK;EACT,OAAOC,OAAO,CAACC,MAAM,CAACF,KAAK,CAAC;AAC9B,CACF,CAAC;;AAED;AACAZ,aAAa,CAACI,YAAY,CAACW,QAAQ,CAACT,GAAG,CACpCS,QAAQ,IAAKA,QAAQ,EACrBH,KAAK,IAAK;EACTI,OAAO,CAACJ,KAAK,CAAC,4BAA4B,EAAEA,KAAK,CAAC;EAClD,IAAIA,KAAK,CAACG,QAAQ,IAAIH,KAAK,CAACG,QAAQ,CAACE,MAAM,KAAK,GAAG,EAAE;IACnD;IACAD,OAAO,CAACE,GAAG,CAAC,sCAAsC,CAAC;IACnDT,YAAY,CAACU,UAAU,CAAC,OAAO,CAAC;;IAEhC;IACAV,YAAY,CAACU,UAAU,CAAC,oBAAoB,CAAC;IAC7CV,YAAY,CAACU,UAAU,CAAC,sBAAsB,CAAC;IAC/CH,OAAO,CAACE,GAAG,CAAC,mDAAmD,CAAC;;IAEhE;IACA;EACF;EACA,OAAOL,OAAO,CAACC,MAAM,CAACF,KAAK,CAAC;AAC9B,CACF,CAAC;AAED,MAAMQ,WAAW,GAAG;EAClB;EACAC,KAAK,EAAE,MAAAA,CAAOC,WAAW,EAAEC,SAAS,KAAK;IACvC,IAAI;MACFP,OAAO,CAACE,GAAG,CAAC,sBAAsBK,SAAS,iBAAiBxB,OAAO,EAAE,CAAC;MAEtE,IAAIwB,SAAS,KAAK,UAAU,EAAE;QAC5B;QACA,MAAMC,QAAQ,GAAG,IAAIC,QAAQ,CAAC,CAAC;QAC/BD,QAAQ,CAACE,MAAM,CAAC,UAAU,EAAEJ,WAAW,CAACK,QAAQ,CAAC;QACjDH,QAAQ,CAACE,MAAM,CAAC,UAAU,EAAEJ,WAAW,CAACM,QAAQ,CAAC;QAEjDZ,OAAO,CAACE,GAAG,CAAC,0BAA0BnB,OAAO,aAAa,CAAC;QAC3D;QACA,MAAMgB,QAAQ,GAAG,MAAMjB,KAAK,CAAC+B,IAAI,CAAC,GAAG9B,OAAO,aAAa,EAAEyB,QAAQ,EAAE;UACnErB,OAAO,EAAE;YACP,cAAc,EAAE;UAClB;QACF,CAAC,CAAC;QACFa,OAAO,CAACE,GAAG,CAAC,oBAAoB,EAAEH,QAAQ,CAAC;QAC3C,OAAOA,QAAQ,CAACe,IAAI;MACtB,CAAC,MAAM,IAAIP,SAAS,KAAK,UAAU,EAAE;QACnC;QACAP,OAAO,CAACE,GAAG,CAAC,0BAA0BnB,OAAO,sBAAsB,CAAC;QACpE,MAAMgB,QAAQ,GAAG,MAAMf,aAAa,CAAC6B,IAAI,CAAC,sBAAsB,EAAE;UAChEE,cAAc,EAAET,WAAW,CAACS,cAAc;UAC1CH,QAAQ,EAAEN,WAAW,CAACM;QACxB,CAAC,CAAC;QACFZ,OAAO,CAACE,GAAG,CAAC,oBAAoB,EAAEH,QAAQ,CAAC;;QAE3C;QACA,IAAIA,QAAQ,CAACe,IAAI,CAACE,WAAW,EAAE;UAC7BhB,OAAO,CAACE,GAAG,CAAC,wCAAwC,EAAEH,QAAQ,CAACe,IAAI,CAACE,WAAW,CAAC;UAChFvB,YAAY,CAACwB,OAAO,CAAC,oBAAoB,EAAElB,QAAQ,CAACe,IAAI,CAACE,WAAW,CAACE,QAAQ,CAAC,CAAC,CAAC;UAChFzB,YAAY,CAACwB,OAAO,CAAC,sBAAsB,EAAElB,QAAQ,CAACe,IAAI,CAACK,aAAa,IAAI,YAAYpB,QAAQ,CAACe,IAAI,CAACE,WAAW,EAAE,CAAC;QACtH,CAAC,MAAM;UACLhB,OAAO,CAACoB,IAAI,CAAC,mDAAmD,EAAErB,QAAQ,CAACe,IAAI,CAAC;QAClF;QAEA,OAAOf,QAAQ,CAACe,IAAI;MACtB,CAAC,MAAM;QACL,MAAM,IAAIO,KAAK,CAAC,0BAA0B,CAAC;MAC7C;IACF,CAAC,CAAC,OAAOzB,KAAK,EAAE;MACdI,OAAO,CAACJ,KAAK,CAAC,cAAc,EAAEA,KAAK,CAAC;MACpC,IAAIA,KAAK,CAACG,QAAQ,EAAE;QAClBC,OAAO,CAACJ,KAAK,CAAC,kBAAkB,EAAEA,KAAK,CAACG,QAAQ,CAACE,MAAM,EAAEL,KAAK,CAACG,QAAQ,CAACe,IAAI,CAAC;QAC7E,MAAMlB,KAAK,CAACG,QAAQ,CAACe,IAAI;MAC3B,CAAC,MAAM,IAAIlB,KAAK,CAACP,OAAO,EAAE;QACxBW,OAAO,CAACJ,KAAK,CAAC,4BAA4B,EAAEA,KAAK,CAACP,OAAO,CAAC;QAC1D,MAAM;UAAEiC,MAAM,EAAE;QAA8E,CAAC;MACjG,CAAC,MAAM;QACLtB,OAAO,CAACJ,KAAK,CAAC,mDAAmD,EAAEA,KAAK,CAAC2B,OAAO,CAAC;QACjF,MAAM;UAAED,MAAM,EAAE1B,KAAK,CAAC2B;QAAQ,CAAC;MACjC;IACF;EACF,CAAC;EAID;EACAC,UAAU,EAAE,MAAAA,CAAA,KAAY;IACtB,IAAI;MACFxB,OAAO,CAACE,GAAG,CAAC,4BAA4B,CAAC;MACzC,MAAMH,QAAQ,GAAG,MAAMf,aAAa,CAAC6B,IAAI,CAAC,kBAAkB,CAAC;MAC7Db,OAAO,CAACE,GAAG,CAAC,0BAA0B,EAAEH,QAAQ,CAACe,IAAI,CAAC;;MAEtD;MACA;MACA,MAAMW,cAAc,GAAG1B,QAAQ,CAACe,IAAI,CAACY,eAAe,KAAK,IAAI;;MAE7D;MACA,IAAID,cAAc,EAAE;QAClBzB,OAAO,CAACE,GAAG,CAAC,iEAAiE,CAAC;QAC9ET,YAAY,CAACwB,OAAO,CAAC,iBAAiB,EAAE,MAAM,CAAC;MACjD,CAAC,MAAM;QACL;QACAxB,YAAY,CAACU,UAAU,CAAC,iBAAiB,CAAC;MAC5C;;MAEA;MACA,MAAMwB,QAAQ,GAAG;QACfC,EAAE,EAAE7B,QAAQ,CAACe,IAAI,CAACe,OAAO;QACzBlB,QAAQ,EAAEZ,QAAQ,CAACe,IAAI,CAACH,QAAQ;QAChCmB,IAAI,EAAE/B,QAAQ,CAACe,IAAI,CAACgB,IAAI;QACxBL,cAAc,EAAEA;MAClB,CAAC;;MAED;MACA,IAAI1B,QAAQ,CAACe,IAAI,CAACgB,IAAI,KAAK,eAAe,IAAI/B,QAAQ,CAACe,IAAI,CAACE,WAAW,EAAE;QACvEW,QAAQ,CAACX,WAAW,GAAGjB,QAAQ,CAACe,IAAI,CAACE,WAAW;QAChDW,QAAQ,CAACR,aAAa,GAAGpB,QAAQ,CAACe,IAAI,CAACK,aAAa,IAAI,YAAYpB,QAAQ,CAACe,IAAI,CAACE,WAAW,EAAE;;QAE/F;QACAvB,YAAY,CAACwB,OAAO,CAAC,oBAAoB,EAAElB,QAAQ,CAACe,IAAI,CAACE,WAAW,CAACE,QAAQ,CAAC,CAAC,CAAC;QAChFzB,YAAY,CAACwB,OAAO,CAAC,sBAAsB,EAAEU,QAAQ,CAACR,aAAa,CAAC;QACpEnB,OAAO,CAACE,GAAG,CAAC,4DAA4D,EAAE;UACxEc,WAAW,EAAEjB,QAAQ,CAACe,IAAI,CAACE,WAAW;UACtCG,aAAa,EAAEQ,QAAQ,CAACR;QAC1B,CAAC,CAAC;MACJ;;MAEA;MACA,IAAIM,cAAc,IAAI1B,QAAQ,CAACe,IAAI,CAACiB,eAAe,EAAE;QACnD;QACA,MAAMC,oBAAoB,GAAG;UAC3BJ,EAAE,EAAE7B,QAAQ,CAACe,IAAI,CAACiB,eAAe;UACjCpB,QAAQ,EAAEZ,QAAQ,CAACe,IAAI,CAACmB,qBAAqB;UAC7CH,IAAI,EAAE/B,QAAQ,CAACe,IAAI,CAACoB;QACtB,CAAC;QAEDzC,YAAY,CAACwB,OAAO,CAAC,kBAAkB,EAAEkB,IAAI,CAACC,SAAS,CAACJ,oBAAoB,CAAC,CAAC;MAChF;MAEA,OAAOL,QAAQ;IACjB,CAAC,CAAC,OAAO/B,KAAK,EAAE;MACdI,OAAO,CAACJ,KAAK,CAAC,oBAAoB,EAAEA,KAAK,CAAC;MAC1C;MACAH,YAAY,CAACU,UAAU,CAAC,OAAO,CAAC;MAChCV,YAAY,CAACU,UAAU,CAAC,iBAAiB,CAAC;;MAE1C;MACAV,YAAY,CAACU,UAAU,CAAC,oBAAoB,CAAC;MAC7CV,YAAY,CAACU,UAAU,CAAC,sBAAsB,CAAC;MAC/CH,OAAO,CAACE,GAAG,CAAC,mDAAmD,CAAC;MAChE,MAAMN,KAAK,CAACG,QAAQ,GAAGH,KAAK,CAACG,QAAQ,CAACe,IAAI,GAAGlB,KAAK;IACpD;EACF,CAAC;EAED;EACAyC,eAAe,EAAE,MAAOC,MAAM,IAAK;IACjC,IAAI;MACF,MAAMvC,QAAQ,GAAG,MAAMf,aAAa,CAAC6B,IAAI,CAAC,mBAAmB,EAAE;QAC7DgB,OAAO,EAAES;MACX,CAAC,CAAC;MACF,OAAOvC,QAAQ,CAACe,IAAI;IACtB,CAAC,CAAC,OAAOlB,KAAK,EAAE;MACdI,OAAO,CAACJ,KAAK,CAAC,yBAAyB,EAAEA,KAAK,CAAC;MAC/C,MAAMA,KAAK,CAACG,QAAQ,GAAGH,KAAK,CAACG,QAAQ,CAACe,IAAI,GAAGlB,KAAK;IACpD;EACF;AACF,CAAC;AAED,eAAeQ,WAAW", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}