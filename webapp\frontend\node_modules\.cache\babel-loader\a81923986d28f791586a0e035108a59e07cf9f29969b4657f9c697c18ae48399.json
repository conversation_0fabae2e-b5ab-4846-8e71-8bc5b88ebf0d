{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\PycharmProjects\\\\CMS\\\\webapp\\\\frontend\\\\src\\\\components\\\\cavi\\\\InserisciMetriForm.js\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect } from 'react';\nimport axios from 'axios';\nimport { Box, Paper, Typography, TextField, Button, Grid, FormControl, InputLabel, Select, MenuItem, Divider, Alert, CircularProgress, FormHelperText, IconButton, Chip, Dialog, DialogTitle, DialogContent, DialogActions, Table, TableBody, TableCell, TableContainer, TableHead, TableRow, List, ListItem, ListItemText, ListItemSecondaryAction } from '@mui/material';\nimport { Search as SearchIcon, Save as SaveIcon, Cancel as CancelIcon, Warning as WarningIcon, Info as InfoIcon } from '@mui/icons-material';\nimport { useNavigate } from 'react-router-dom';\nimport caviService from '../../services/caviService';\nimport axiosInstance from '../../services/axiosConfig';\nimport IncompatibleReelDialog from './IncompatibleReelDialog';\nimport CavoDetailsView from './CavoDetailsView';\nimport { CABLE_STATES, REEL_STATES, determineCableState, determineReelState, canModifyCable, isCableSpare, isCableInstalled, getCableStateColor, getReelStateColor } from '../../utils/stateUtils';\nimport parcoCaviService from '../../services/parcoCaviService';\nimport { redirectToVisualizzaCavi } from '../../utils/navigationUtils';\n\n/**\n * Componente per l'inserimento dei metri posati di un cavo\n * Versione semplificata con workflow compresso in un'unica pagina\n *\n * @param {Object} props - Proprietà del componente\n * @param {string} props.cantiereId - ID del cantiere\n * @param {Function} props.onSuccess - Funzione chiamata al successo dell'operazione\n * @param {Function} props.onError - Funzione chiamata in caso di errore\n */\nimport { jsxDEV as _jsxDEV, Fragment as _Fragment } from \"react/jsx-dev-runtime\";\nconst InserisciMetriForm = ({\n  cantiereId,\n  onSuccess,\n  onError\n}) => {\n  _s();\n  const navigate = useNavigate();\n\n  // Stati per la gestione del form\n  const [loading, setLoading] = useState(false);\n  const [caviLoading, setCaviLoading] = useState(false);\n  const [bobineLoading, setBobineLoading] = useState(false);\n  const [searchResults, setSearchResults] = useState([]);\n  const [showSearchResults, setShowSearchResults] = useState(false);\n\n  // Stati per i dati\n  const [cavi, setCavi] = useState([]);\n  const [bobine, setBobine] = useState([]);\n  const [selectedCavo, setSelectedCavo] = useState(null);\n  const [cavoIdInput, setCavoIdInput] = useState('');\n\n  // Stato per la gestione dei passi (mantenuto per compatibilità con funzioni esistenti)\n  const [activeStep, setActiveStep] = useState(0);\n\n  // Stati per il form\n  const [formData, setFormData] = useState({\n    id_cavo: '',\n    metri_posati: '',\n    id_bobina: ''\n  });\n\n  // Stati per la validazione\n  const [formErrors, setFormErrors] = useState({});\n  const [formWarnings, setFormWarnings] = useState({});\n\n  // Stati per i dialoghi speciali\n  const [showIncompatibleReelDialog, setShowIncompatibleReelDialog] = useState(false);\n  const [incompatibleReel, setIncompatibleReel] = useState(null); // Mantenuto per compatibilità\n  const [incompatibleReelData, setIncompatibleReelData] = useState({\n    cavo: null,\n    bobina: null\n  });\n  const [showAlreadyLaidDialog, setShowAlreadyLaidDialog] = useState(false);\n  const [alreadyLaidCavo, setAlreadyLaidCavo] = useState(null);\n  const [showCavoDetailsDialog, setShowCavoDetailsDialog] = useState(false);\n\n  // Carica la lista delle bobine all'avvio\n  useEffect(() => {\n    loadBobine();\n  }, [cantiereId]);\n\n  // Funzione per estrarre il numero della bobina dall'ID completo\n  const getBobinaNumber = idBobina => {\n    // L'ID completo è nel formato C{id_cantiere}_B{numero_bobina}\n    if (idBobina && idBobina.includes('_B')) {\n      return idBobina.split('_B')[1];\n    }\n    return idBobina;\n  };\n\n  // Funzione per caricare i cavi\n  const loadCavi = async () => {\n    try {\n      setCaviLoading(true);\n      console.log(`Caricamento cavi per il cantiere ${cantiereId}...`);\n\n      // Carica tutti i cavi, inclusi quelli SPARE e installati\n      try {\n        const caviData = await caviService.getCavi(cantiereId);\n        console.log(`Caricati ${caviData.length} cavi`);\n\n        // Mostra tutti i cavi (da installare, in corso, installati e SPARE)\n        // Questo permette di vedere anche i cavi già installati per eventuale modifica della bobina\n        setCavi(caviData);\n      } catch (loadError) {\n        console.error('Errore nel primo tentativo di caricamento dei cavi:', loadError);\n\n        // Se è un errore di rete, prova un secondo tentativo dopo una breve pausa\n        if (loadError.isNetworkError || !loadError.response || loadError.code === 'ECONNABORTED' || loadError.message && loadError.message.includes('Network Error')) {\n          console.log('Errore di rete, tentativo di recupero dopo 1 secondo...');\n          await new Promise(resolve => setTimeout(resolve, 1000));\n          try {\n            // Secondo tentativo con timeout aumentato\n            const token = localStorage.getItem('token');\n            const API_URL = axiosInstance.defaults.baseURL;\n\n            // Usa l'ID cantiere originale per la richiesta alternativa\n            const retryResponse = await axios.get(`${API_URL}/cavi/${cantiereId}`, {\n              headers: {\n                'Authorization': `Bearer ${token}`\n              },\n              timeout: 30000 // 30 secondi\n            });\n            console.log(`Recupero riuscito al secondo tentativo: ${retryResponse.data.length} cavi`);\n            setCavi(retryResponse.data);\n          } catch (retryError) {\n            console.error('Anche il secondo tentativo è fallito:', retryError);\n            throw retryError;\n          }\n        } else {\n          throw loadError;\n        }\n      }\n    } catch (error) {\n      console.error('Errore nel caricamento dei cavi:', error);\n\n      // Messaggio di errore più dettagliato\n      let errorMessage = 'Errore nel caricamento dei cavi';\n      if (error.isNetworkError) {\n        errorMessage = 'Errore di connessione al server. Verifica la rete e riprova.';\n      } else if (error.detail) {\n        errorMessage = error.detail;\n      } else if (error.message) {\n        errorMessage = error.message;\n      }\n      onError(errorMessage);\n    } finally {\n      setCaviLoading(false);\n    }\n  };\n\n  // Funzione per caricare le bobine\n  const loadBobine = async () => {\n    try {\n      setBobineLoading(true);\n      console.log('Caricamento bobine iniziato...');\n\n      // Carica tutte le bobine disponibili\n      const bobineData = await parcoCaviService.getBobine(cantiereId);\n      console.log(`Bobine caricate: ${bobineData.length}`);\n\n      // Filtra solo per stato (disponibile o in uso) e metri residui > 0\n      const bobineUtilizzabili = bobineData.filter(bobina => bobina.stato_bobina !== 'Terminata' && bobina.stato_bobina !== 'Over' && bobina.metri_residui > 0);\n      console.log(`Bobine utilizzabili: ${bobineUtilizzabili.length}`);\n\n      // Se c'è un cavo selezionato, evidenzia le bobine compatibili ma mostra tutte\n      if (selectedCavo && selectedCavo.tipologia && selectedCavo.sezione) {\n        console.log('Cavo selezionato, evidenziando bobine compatibili...');\n        console.log('Dati cavo:', {\n          id_cavo: selectedCavo.id_cavo,\n          tipologia: selectedCavo.tipologia,\n          sezione: selectedCavo.sezione\n        });\n\n        // Ordina le bobine: prima le compatibili, poi le altre, tutte ordinate per metri residui\n        const cavoTipologia = String(selectedCavo.tipologia || '').trim().toLowerCase();\n        const cavoSezione = String(selectedCavo.sezione || '0').trim();\n\n        // Identifica le bobine compatibili\n        const bobineCompatibili = [];\n        const bobineNonCompatibili = [];\n\n        // Dividi le bobine in compatibili e non compatibili\n        bobineUtilizzabili.forEach(bobina => {\n          const bobinaTipologia = String(bobina.tipologia || '').trim().toLowerCase();\n          const bobinaSezione = String(bobina.sezione || '0').trim();\n\n          // Verifica compatibilità\n          const tipologiaMatch = bobinaTipologia === cavoTipologia;\n          const sezioneMatch = bobinaSezione === cavoSezione;\n          const isCompatible = tipologiaMatch && sezioneMatch;\n          console.log(`Verifica compatibilità bobina ${bobina.id_bobina}:`, {\n            'Tipologia bobina': `\"${bobina.tipologia}\"`,\n            'Tipologia cavo': `\"${selectedCavo.tipologia}\"`,\n            'Tipologie uguali?': tipologiaMatch,\n            'Sezione bobina': `\"${String(bobina.sezione)}\"`,\n            'Sezione cavo': `\"${String(selectedCavo.sezione)}\"`,\n            'Sezioni uguali?': sezioneMatch,\n            'Stato bobina': bobina.stato_bobina,\n            'Metri residui': bobina.metri_residui,\n            'Compatibile?': isCompatible\n          });\n          if (isCompatible) {\n            bobineCompatibili.push(bobina);\n          } else {\n            bobineNonCompatibili.push(bobina);\n          }\n        });\n        console.log(`Bobine compatibili trovate: ${bobineCompatibili.length}`);\n        console.log(`Bobine non compatibili: ${bobineNonCompatibili.length}`);\n\n        // Log dettagliato delle bobine compatibili\n        if (bobineCompatibili.length > 0) {\n          console.log('Dettaglio bobine compatibili:');\n          bobineCompatibili.forEach(bobina => {\n            console.log(`- ${bobina.id_bobina}: ${bobina.tipologia} / ${bobina.sezione} (${bobina.metri_residui}m)`);\n          });\n        } else {\n          console.log('ATTENZIONE: Nessuna bobina compatibile trovata!');\n        }\n\n        // Ordina entrambi gli array per metri residui (decrescente)\n        bobineCompatibili.sort((a, b) => b.metri_residui - a.metri_residui);\n        bobineNonCompatibili.sort((a, b) => b.metri_residui - a.metri_residui);\n\n        // Concatena gli array: prima le compatibili, poi le non compatibili\n        const bobineOrdinate = [...bobineCompatibili, ...bobineNonCompatibili];\n\n        // Imposta le bobine nel componente\n        setBobine(bobineOrdinate);\n      } else {\n        // Se non c'è un cavo selezionato, mostra tutte le bobine ordinate per metri residui\n        console.log('Nessun cavo selezionato, mostrando tutte le bobine...');\n        bobineUtilizzabili.sort((a, b) => b.metri_residui - a.metri_residui);\n        setBobine(bobineUtilizzabili);\n      }\n    } catch (error) {\n      console.error('Errore nel caricamento delle bobine:', error);\n      onError('Errore nel caricamento delle bobine: ' + (error.message || 'Errore sconosciuto'));\n    } finally {\n      setBobineLoading(false);\n    }\n  };\n\n  // Gestisce la ricerca di un cavo per ID o pattern\n  const handleSearchCavoById = async () => {\n    if (!cavoIdInput.trim()) {\n      onError('Inserisci un ID cavo valido');\n      return;\n    }\n    try {\n      setCaviLoading(true);\n      console.log(`Ricerca cavo con pattern: ${cavoIdInput.trim()} nel cantiere ${cantiereId}`);\n\n      // Cerca tutti i cavi che corrispondono al pattern\n      const caviData = await caviService.getCavi(cantiereId);\n\n      // Filtra i cavi in base all'input (ricerca parziale)\n      const filteredCavi = caviData.filter(cavo => cavo.id_cavo.toLowerCase().includes(cavoIdInput.trim().toLowerCase()));\n      console.log(`Trovati ${filteredCavi.length} cavi corrispondenti al pattern`);\n\n      // Se c'è una corrispondenza esatta, seleziona direttamente quel cavo\n      const exactMatch = filteredCavi.find(cavo => cavo.id_cavo.toLowerCase() === cavoIdInput.trim().toLowerCase());\n      if (exactMatch) {\n        console.log('Trovata corrispondenza esatta:', exactMatch);\n\n        // Verifica se il cavo è già installato\n        if (exactMatch.stato_installazione === 'Installato' || exactMatch.metratura_reale && exactMatch.metratura_reale > 0) {\n          console.log('Cavo già installato, mostra dialogo:', exactMatch);\n          setAlreadyLaidCavo(exactMatch);\n          setShowAlreadyLaidDialog(true);\n          setCaviLoading(false);\n          return;\n        }\n\n        // Verifica se il cavo è SPARE\n        if (exactMatch.modificato_manualmente === 3) {\n          console.log('Cavo SPARE trovato:', exactMatch);\n          // Gestione del cavo SPARE - verrà gestito in handleCavoSelect\n        }\n\n        // Seleziona il cavo direttamente\n        handleCavoSelect(exactMatch);\n      } else if (filteredCavi.length > 0) {\n        // Mostra i risultati della ricerca in una tabella\n        setSearchResults(filteredCavi);\n        setShowSearchResults(true);\n      } else {\n        // Nessun cavo trovato\n        onError(`Nessun cavo trovato con pattern \"${cavoIdInput.trim()}\" nel cantiere ${cantiereId}`);\n      }\n    } catch (error) {\n      console.error('Errore nella ricerca del cavo:', error);\n\n      // Messaggio di errore più dettagliato\n      let errorMessage = 'Errore nella ricerca dei cavi';\n      if (error.isNetworkError) {\n        errorMessage = 'Errore di connessione al server. Verifica la rete e riprova.';\n      } else if (error.status === 404) {\n        errorMessage = `Cavo con ID \"${cavoIdInput.trim()}\" non trovato nel cantiere ${cantiereId}`;\n      } else if (error.detail) {\n        errorMessage = error.detail;\n      } else if (error.message) {\n        errorMessage = error.message;\n      }\n      onError(errorMessage);\n    } finally {\n      setCaviLoading(false);\n    }\n  };\n\n  // Gestisce la selezione di un cavo\n  const handleCavoSelect = cavo => {\n    // Verifica se il cavo è già installato\n    if (cavo.stato_installazione === 'Installato' || cavo.metratura_reale && cavo.metratura_reale > 0) {\n      // Mostra il dialogo per cavi già posati\n      setAlreadyLaidCavo(cavo);\n      setShowAlreadyLaidDialog(true);\n      return;\n    }\n    // Verifica se il cavo è SPARE (modificato_manualmente = 3)\n    else if (cavo.modificato_manualmente === 3) {\n      // Chiedi conferma all'utente per riattivare il cavo SPARE\n      if (window.confirm(`Il cavo ${cavo.id_cavo} è marcato come SPARE. Vuoi riattivarlo?`)) {\n        // Riattiva il cavo SPARE (imposta modificato_manualmente = 0)\n        reactivateSpare(cavo.id_cavo).then(() => {\n          // Aggiorna il cavo selezionato con modificato_manualmente = 0\n          const updatedCavo = {\n            ...cavo,\n            modificato_manualmente: 0\n          };\n          setSelectedCavo(updatedCavo);\n          setFormData({\n            ...formData,\n            id_cavo: updatedCavo.id_cavo,\n            metri_posati: ''\n          });\n          // Nascondi i risultati della ricerca\n          setShowSearchResults(false);\n\n          // Carica le bobine compatibili\n          loadBobine();\n        }).catch(error => {\n          console.error('Errore durante la riattivazione del cavo SPARE:', error);\n          onError('Errore durante la riattivazione del cavo SPARE: ' + (error.message || 'Errore sconosciuto'));\n        });\n      } else {\n        // L'utente ha annullato la riattivazione\n        return;\n      }\n    } else {\n      // Cavo normale (non SPARE e non installato)\n      setSelectedCavo(cavo);\n      setFormData({\n        ...formData,\n        id_cavo: cavo.id_cavo,\n        metri_posati: ''\n      });\n      // Nascondi i risultati della ricerca\n      setShowSearchResults(false);\n\n      // Carica le bobine compatibili\n      if (cavo.tipologia && cavo.n_conduttori && cavo.sezione) {\n        console.log(`Caricamento bobine compatibili per il cavo ${cavo.id_cavo}...`);\n        loadBobine();\n      }\n    }\n  };\n\n  // Funzione per riattivare un cavo SPARE\n  const reactivateSpare = async cavoId => {\n    try {\n      // Chiamata API per riattivare il cavo SPARE\n      await caviService.reactivateSpare(cantiereId, cavoId);\n      onSuccess(`Cavo ${cavoId} riattivato con successo`);\n      return true;\n    } catch (error) {\n      console.error('Errore durante la riattivazione del cavo SPARE:', error);\n      onError('Errore durante la riattivazione del cavo SPARE: ' + (error.message || 'Errore sconosciuto'));\n      throw error;\n    }\n  };\n\n  // Gestisce il cambio dei valori nel form\n  const handleFormChange = e => {\n    const {\n      name,\n      value\n    } = e.target;\n\n    // Gestione speciale per il cambio di bobina\n    if (name === 'id_bobina' && value && value !== 'BOBINA_VUOTA') {\n      // Verifica compatibilità tra cavo e bobina\n      const bobina = bobine.find(b => b.id_bobina === value);\n      if (bobina && selectedCavo) {\n        // Nella nuova configurazione, controlliamo solo tipologia e formazione (sezione)\n        // Utilizziamo una comparazione più robusta con trim() e gestione di null/undefined\n        const isCompatible = String(bobina.tipologia || '').trim() === String(selectedCavo.tipologia || '').trim() && String(bobina.sezione || '0').trim() === String(selectedCavo.sezione || '0').trim();\n        if (!isCompatible) {\n          console.log('Bobina incompatibile selezionata:', bobina);\n          console.log('Cavo corrente:', selectedCavo);\n          console.log('Confronto formazione:', {\n            cavoFormazione: String(selectedCavo.sezione || '0'),\n            bobinaFormazione: String(bobina.sezione || '0')\n          });\n\n          // Mostra il dialogo di incompatibilità\n          setIncompatibleReelData({\n            cavo: selectedCavo,\n            bobina: bobina\n          });\n          setShowIncompatibleReelDialog(true);\n\n          // Non aggiornare il valore del form finché l'utente non conferma\n          return;\n        }\n      }\n    }\n    setFormData({\n      ...formData,\n      [name]: value\n    });\n\n    // Validazione in tempo reale\n    validateField(name, value);\n  };\n\n  // Validazione di un campo\n  const validateField = (name, value) => {\n    let error = null;\n    let warning = null;\n    if (name === 'metri_posati') {\n      // Controllo input vuoto\n      if (!value || value.trim() === '') {\n        error = 'Inserire un valore per i metri posati';\n        return false;\n      }\n\n      // Controllo formato numerico\n      if (isNaN(parseFloat(value)) || parseFloat(value) <= 0) {\n        error = 'Inserire un valore numerico positivo';\n        return false;\n      }\n      const metriPosati = parseFloat(value);\n\n      // Controllo metri teorici cavo\n      if (selectedCavo && selectedCavo.metri_teorici && metriPosati > parseFloat(selectedCavo.metri_teorici)) {\n        warning = `I metri posati (${metriPosati}m) superano i metri teorici del cavo (${selectedCavo.metri_teorici}m)`;\n      }\n\n      // Controllo metri residui bobina (se bobina reale)\n      if (formData.id_bobina && formData.id_bobina !== 'BOBINA_VUOTA') {\n        const bobina = bobine.find(b => b.id_bobina === formData.id_bobina);\n        if (bobina && metriPosati > parseFloat(bobina.metri_residui)) {\n          warning = `I metri posati (${metriPosati}m) superano i metri residui della bobina (${bobina.metri_residui}m). La bobina andrà in stato OVER.`;\n        }\n      }\n    } else if (name === 'id_bobina') {\n      // Controllo che sia selezionata una bobina\n      if (!value || value.trim() === '') {\n        error = 'È necessario selezionare una bobina';\n        return false;\n      }\n    }\n\n    // Aggiorna gli errori\n    setFormErrors(prev => ({\n      ...prev,\n      [name]: error\n    }));\n\n    // Aggiorna gli avvisi\n    setFormWarnings(prev => ({\n      ...prev,\n      [name]: warning\n    }));\n    return !error;\n  };\n\n  // Stati per i dialoghi di conferma\n  const [notificationShown, setNotificationShown] = useState(false);\n  const [showConfirmDialog, setShowConfirmDialog] = useState(false);\n  const [confirmDialogProps, setConfirmDialogProps] = useState({\n    title: '',\n    message: '',\n    onConfirm: () => {}\n  });\n\n  // Nota: Lo stato per il dialogo di bobina incompatibile è già dichiarato sopra\n\n  // Validazione completa del form\n  const validateForm = () => {\n    let isValid = true;\n    const errors = {};\n    const warnings = {};\n\n    // Reset della variabile di notifica\n    setNotificationShown(false);\n\n    // Validazione metri posati\n    if (!formData.metri_posati || formData.metri_posati.trim() === '') {\n      errors.metri_posati = 'Inserire un valore per i metri posati';\n      isValid = false;\n    } else if (isNaN(parseFloat(formData.metri_posati)) || parseFloat(formData.metri_posati) <= 0) {\n      errors.metri_posati = 'Inserire un valore numerico positivo';\n      isValid = false;\n    }\n\n    // Validazione bobina (deve essere selezionata)\n    if (!formData.id_bobina || formData.id_bobina.trim() === '') {\n      errors.id_bobina = 'È necessario selezionare una bobina';\n      isValid = false;\n    }\n    if (isValid) {\n      const metriPosati = parseFloat(formData.metri_posati);\n\n      // Controllo metri teorici cavo\n      if (selectedCavo && selectedCavo.metri_teorici && metriPosati > parseFloat(selectedCavo.metri_teorici)) {\n        warnings.metri_posati = `I metri posati (${metriPosati}m) superano i metri teorici del cavo (${selectedCavo.metri_teorici}m)`;\n        setNotificationShown(true); // Imposta la variabile per evitare notifiche duplicate\n        // Non mostrare più il popup di conferma, solo l'avviso nel form\n        // Continua con la validazione senza interrompere il flusso\n      }\n\n      // Controllo metri residui bobina (se bobina reale)\n      if (isValid && formData.id_bobina && formData.id_bobina !== 'BOBINA_VUOTA') {\n        const bobina = bobine.find(b => b.id_bobina === formData.id_bobina);\n        if (bobina && metriPosati > parseFloat(bobina.metri_residui)) {\n          warnings.metri_posati = `I metri posati (${metriPosati}m) superano i metri residui della bobina (${bobina.metri_residui}m). La bobina andrà in stato OVER.`;\n          setNotificationShown(true); // Imposta la variabile per evitare notifiche duplicate\n\n          // Mostra il dialogo di conferma invece di window.confirm\n          setConfirmDialogProps({\n            title: 'Attenzione: Bobina in stato OVER',\n            message: `I metri posati (${metriPosati}m) superano i metri residui della bobina (${bobina.metri_residui}m). Questo porterà la bobina in stato OVER. Vuoi continuare?`,\n            onConfirm: () => {\n              // Continua con la validazione\n              handleNext();\n            }\n          });\n          setShowConfirmDialog(true);\n          return false; // Interrompi la validazione fino alla conferma dell'utente\n        }\n      }\n    }\n    setFormErrors(errors);\n    setFormWarnings(warnings);\n    return isValid;\n  };\n\n  // Gestisce il passaggio al passo successivo\n  const handleNext = () => {\n    if (activeStep === 2) {\n      // Validazione prima di passare al passo successivo (da Inserisci Metri a Conferma)\n      if (!validateForm()) {\n        return;\n      }\n    } else if (activeStep === 1) {\n      // Carica le bobine prima di passare al passo successivo (da Associa Bobina a Inserisci Metri)\n      loadBobine();\n    }\n    setActiveStep(prevActiveStep => prevActiveStep + 1);\n  };\n\n  // Gestisce il ritorno al passo precedente\n  const handleBack = () => {\n    setActiveStep(prevActiveStep => prevActiveStep - 1);\n  };\n\n  // Gestisce il reset del form\n  const handleReset = () => {\n    setActiveStep(0);\n    setSelectedCavo(null);\n    setCavoIdInput('');\n    setFormData({\n      id_cavo: '',\n      metri_posati: '',\n      id_bobina: ''\n    });\n    setFormErrors({});\n    setFormWarnings({});\n  };\n\n  // Determina lo stato di installazione in base ai metri posati\n  // Utilizziamo la funzione dalla utility stateUtils\n  const determineInstallationStatus = (metriPosati, metriTeorici) => {\n    return determineCableState(metriPosati, metriTeorici);\n  };\n\n  // Gestisce l'invio del form\n  const handleSubmit = async () => {\n    // Dichiarazione delle variabili al di fuori del blocco try/catch\n    // in modo che siano accessibili anche nel blocco catch\n    let idBobina;\n    let statoInstallazione;\n    let metriPosati;\n    let forceOver = false;\n    try {\n      setLoading(true);\n\n      // Validazione finale\n      if (!validateForm()) {\n        setLoading(false);\n        return;\n      }\n\n      // Prepara i dati da inviare\n      metriPosati = parseFloat(formData.metri_posati);\n\n      // Gestione speciale per BOBINA_VUOTA\n      idBobina = formData.id_bobina;\n\n      // Gestione differenziata per cavi non posati e cavi posati\n      if (!idBobina || idBobina === '') {\n        // È necessario selezionare una bobina, anche BOBINA_VUOTA\n        setFormErrors({\n          ...formErrors,\n          id_bobina: 'È necessario selezionare una bobina'\n        });\n        setLoading(false);\n        return;\n      } else if (idBobina === 'BOBINA_VUOTA') {\n        // Per cavi posati senza bobina specifica\n        console.log('Usando BOBINA_VUOTA per il cavo');\n        // Assicurati che BOBINA_VUOTA venga passato come stringa e non come null\n        idBobina = 'BOBINA_VUOTA';\n      } else {\n        // Per cavi posati con bobina specifica\n        console.log(`Usando bobina reale: ${idBobina}`);\n      }\n\n      // Determina lo stato di installazione\n      statoInstallazione = determineInstallationStatus(metriPosati, selectedCavo.metri_teorici);\n\n      // Imposta sempre forceOver a true per evitare blocchi quando la bobina va in OVER\n      forceOver = true;\n      console.log('Impostando forceOver a true per garantire il completamento dell\\'operazione');\n\n      // Log delle condizioni che richiederebbero forceOver\n      if (idBobina === 'BOBINA_VUOTA') {\n        console.log('Operazione con BOBINA_VUOTA');\n      }\n      // Per bobine reali, verifica i metri residui\n      else if (idBobina && idBobina !== 'BOBINA_VUOTA') {\n        const bobina = bobine.find(b => b.id_bobina === idBobina);\n        if (bobina && metriPosati > parseFloat(bobina.metri_residui)) {\n          console.log(`La bobina ${idBobina} andrà in stato OVER (metri posati ${metriPosati} > metri residui ${bobina.metri_residui})`);\n        }\n      }\n\n      // Verifica anche se i metri posati superano i metri teorici\n      if (selectedCavo && selectedCavo.metri_teorici && metriPosati > parseFloat(selectedCavo.metri_teorici)) {\n        console.log(`I metri posati (${metriPosati}) superano i metri teorici (${selectedCavo.metri_teorici})`);\n      }\n\n      // Log per debug\n      console.log('Invio dati:', {\n        cantiereId,\n        cavoId: formData.id_cavo,\n        metriPosati,\n        idBobina,\n        forceOver,\n        statoInstallazione\n      });\n\n      // Conferma finale prima dell'invio solo se non è già stata mostrata una notifica\n      if (!notificationShown) {\n        const confirmMessage = `Confermi l'aggiornamento del cavo ${formData.id_cavo} con ${metriPosati}m posati?`;\n\n        // Usa il dialogo di conferma invece di window.confirm\n        setConfirmDialogProps({\n          title: 'Conferma aggiornamento',\n          message: confirmMessage,\n          onConfirm: async () => {\n            // Esegui la chiamata API qui invece che nel flusso principale\n            try {\n              setLoading(true);\n\n              // Imposta sempre forceOver a true per evitare blocchi quando la bobina va in OVER\n              console.log('Impostando forceOver a true nel dialogo di conferma per garantire il completamento dell\\'operazione');\n              await caviService.updateMetriPosati(cantiereId, formData.id_cavo, metriPosati, idBobina, true // Forza sempre a true per evitare blocchi\n              );\n\n              // Messaggio di successo con dettagli sulla bobina\n              let successMessage = `Metri posati aggiornati con successo. Stato cavo: ${statoInstallazione}`;\n              if (idBobina === 'BOBINA_VUOTA') {\n                successMessage += '. Cavo associato a BOBINA VUOTA';\n              } else if (idBobina) {\n                const bobina = bobine.find(b => b.id_bobina === idBobina);\n                if (bobina) {\n                  successMessage += `. Cavo associato alla bobina ${idBobina}`;\n                }\n              }\n\n              // Gestione successo\n              onSuccess(successMessage);\n\n              // Reset del form\n              handleReset();\n\n              // Ricarica i cavi\n              loadCavi();\n            } catch (error) {\n              console.error('Errore durante l\\'aggiornamento dei metri posati:', error);\n\n              // Gestione speciale per BOBINA_VUOTA\n              if (idBobina === 'BOBINA_VUOTA' && error.success) {\n                // Se è un \"errore\" di successo per BOBINA_VUOTA, trattiamolo come un successo\n                let successMessage = `Metri posati aggiornati con successo. Stato cavo: ${statoInstallazione}. Cavo associato a BOBINA VUOTA`;\n                onSuccess(successMessage);\n\n                // Reset del form\n                handleReset();\n\n                // Ricarica i cavi\n                loadCavi();\n                return;\n              }\n\n              // Gestione dettagliata degli errori\n              let errorMessage = 'Errore durante l\\'aggiornamento dei metri posati';\n              if (error.response) {\n                var _error$response$data;\n                // Il server ha risposto con un codice di errore\n                const status = error.response.status;\n                const detail = ((_error$response$data = error.response.data) === null || _error$response$data === void 0 ? void 0 : _error$response$data.detail) || error.message;\n                if (status === 400) {\n                  // Errore di validazione\n                  if (detail.includes('metri residui')) {\n                    errorMessage = `La bobina non ha metri residui sufficienti. Usa l'opzione \"BOBINA VUOTA\" o seleziona un'altra bobina.`;\n                  } else if (detail.includes('già posato')) {\n                    errorMessage = `Il cavo risulta già posato. Usa la funzione \"Modifica bobina cavo posato\".`;\n                  } else {\n                    errorMessage = detail;\n                  }\n                } else if (status === 404) {\n                  // Gestione speciale per BOBINA_VUOTA quando la bobina non viene trovata\n                  if (idBobina === 'BOBINA_VUOTA' && detail.includes('non trovata')) {\n                    let successMessage = `Metri posati aggiornati con successo. Stato cavo: ${statoInstallazione}. Cavo associato a BOBINA VUOTA`;\n                    onSuccess(successMessage);\n\n                    // Reset del form\n                    handleReset();\n\n                    // Ricarica i cavi\n                    loadCavi();\n                    return;\n                  } else {\n                    errorMessage = `Cavo o bobina non trovati: ${detail}`;\n                  }\n                } else {\n                  errorMessage = `Errore del server (${status}): ${detail}`;\n                }\n              } else if (error.request) {\n                // La richiesta è stata inviata ma non è stata ricevuta risposta\n                errorMessage = 'Nessuna risposta dal server. Verifica la connessione di rete.';\n              } else if (error.detail && idBobina === 'BOBINA_VUOTA') {\n                // Gestione speciale per errori con BOBINA_VUOTA\n                errorMessage = error.detail;\n                if (error.status === 200 || error.success) {\n                  let successMessage = `Metri posati aggiornati con successo. Stato cavo: ${statoInstallazione}. Cavo associato a BOBINA VUOTA`;\n                  onSuccess(successMessage);\n\n                  // Reset del form\n                  handleReset();\n\n                  // Ricarica i cavi\n                  loadCavi();\n                  return;\n                }\n              } else {\n                // Errore durante la configurazione della richiesta\n                errorMessage = error.message || error.detail || 'Errore sconosciuto';\n              }\n              onError(errorMessage);\n            } finally {\n              setLoading(false);\n            }\n          }\n        });\n        setShowConfirmDialog(true);\n        setLoading(false);\n        return;\n      }\n\n      // Chiamata API\n      console.log('Invio richiesta updateMetriPosati con i seguenti parametri:');\n      console.log('- cantiereId:', cantiereId);\n      console.log('- id_cavo:', formData.id_cavo);\n      console.log('- metri_posati:', metriPosati);\n      console.log('- id_bobina:', idBobina, typeof idBobina);\n      console.log('- forceOver:', forceOver);\n\n      // Imposta sempre forceOver a true per evitare blocchi quando la bobina va in OVER\n      console.log('Impostando forceOver a true nella chiamata diretta per garantire il completamento dell\\'operazione');\n      await caviService.updateMetriPosati(cantiereId, formData.id_cavo, metriPosati, idBobina, true // Forza sempre a true per evitare blocchi\n      );\n\n      // Messaggio di successo con dettagli sulla bobina\n      let successMessage = `Metri posati aggiornati con successo. Stato cavo: ${statoInstallazione}`;\n      if (idBobina === 'BOBINA_VUOTA') {\n        successMessage += '. Cavo associato a BOBINA VUOTA';\n      } else if (idBobina) {\n        const bobina = bobine.find(b => b.id_bobina === idBobina);\n        if (bobina) {\n          successMessage += `. Cavo associato alla bobina ${idBobina}`;\n        }\n      }\n\n      // Gestione successo\n      onSuccess(successMessage);\n\n      // Reset del form\n      handleReset();\n\n      // Ricarica i cavi\n      loadCavi();\n    } catch (error) {\n      console.error('Errore durante l\\'aggiornamento dei metri posati:', error);\n\n      // Gestione speciale per BOBINA_VUOTA\n      if (idBobina === 'BOBINA_VUOTA' && error.success) {\n        // Se è un \"errore\" di successo per BOBINA_VUOTA, trattiamolo come un successo\n        let successMessage = `Metri posati aggiornati con successo. Stato cavo: ${statoInstallazione}. Cavo associato a BOBINA VUOTA`;\n        onSuccess(successMessage);\n\n        // Reset del form\n        handleReset();\n\n        // Ricarica i cavi\n        loadCavi();\n        return;\n      }\n\n      // Gestione dettagliata degli errori\n      let errorMessage = 'Errore durante l\\'aggiornamento dei metri posati';\n      if (error.response) {\n        var _error$response$data2;\n        // Il server ha risposto con un codice di errore\n        const status = error.response.status;\n        const detail = ((_error$response$data2 = error.response.data) === null || _error$response$data2 === void 0 ? void 0 : _error$response$data2.detail) || error.message;\n        if (status === 400) {\n          // Errore di validazione\n          if (detail.includes('metri residui')) {\n            errorMessage = `La bobina non ha metri residui sufficienti. Usa l'opzione \"BOBINA VUOTA\" o seleziona un'altra bobina.`;\n          } else if (detail.includes('già posato')) {\n            errorMessage = `Il cavo risulta già posato. Usa la funzione \"Modifica bobina cavo posato\".`;\n          } else {\n            errorMessage = detail;\n          }\n        } else if (status === 404) {\n          // Gestione speciale per BOBINA_VUOTA quando la bobina non viene trovata\n          if (idBobina === 'BOBINA_VUOTA' && detail.includes('non trovata')) {\n            let successMessage = `Metri posati aggiornati con successo. Stato cavo: ${statoInstallazione}. Cavo associato a BOBINA VUOTA`;\n            onSuccess(successMessage);\n\n            // Reset del form\n            handleReset();\n\n            // Ricarica i cavi\n            loadCavi();\n            return;\n          } else {\n            errorMessage = `Cavo o bobina non trovati: ${detail}`;\n          }\n        } else {\n          errorMessage = `Errore del server (${status}): ${detail}`;\n        }\n      } else if (error.request) {\n        // La richiesta è stata inviata ma non è stata ricevuta risposta\n        errorMessage = 'Nessuna risposta dal server. Verifica la connessione di rete.';\n      } else if (error.detail && idBobina === 'BOBINA_VUOTA') {\n        // Gestione speciale per errori con BOBINA_VUOTA\n        errorMessage = error.detail;\n        if (error.status === 200 || error.success) {\n          let successMessage = `Metri posati aggiornati con successo. Stato cavo: ${statoInstallazione}. Cavo associato a BOBINA VUOTA`;\n          onSuccess(successMessage);\n\n          // Reset del form\n          handleReset();\n\n          // Ricarica i cavi\n          loadCavi();\n          return;\n        }\n      } else {\n        // Errore durante la configurazione della richiesta\n        errorMessage = error.message || error.detail || 'Errore sconosciuto';\n      }\n      onError(errorMessage);\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  // Renderizza il passo 1: Selezione del cavo\n  const renderStep1 = () => {\n    return /*#__PURE__*/_jsxDEV(Box, {\n      children: [/*#__PURE__*/_jsxDEV(Typography, {\n        variant: \"subtitle2\",\n        sx: {\n          mb: 1,\n          fontWeight: 'bold'\n        },\n        children: \"Seleziona un cavo\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 978,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Paper, {\n        sx: {\n          p: 1.5,\n          mb: 2,\n          width: '100%'\n        },\n        children: /*#__PURE__*/_jsxDEV(Box, {\n          sx: {\n            display: 'flex',\n            alignItems: 'center',\n            width: '100%'\n          },\n          children: [/*#__PURE__*/_jsxDEV(Typography, {\n            variant: \"subtitle2\",\n            sx: {\n              mr: 1,\n              minWidth: '80px'\n            },\n            children: \"Cerca cavo\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 985,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(TextField, {\n            size: \"small\",\n            label: \"ID Cavo\",\n            variant: \"outlined\",\n            value: cavoIdInput,\n            onChange: e => setCavoIdInput(e.target.value),\n            placeholder: \"Inserisci l'ID del cavo\",\n            sx: {\n              flexGrow: 1,\n              mr: 1\n            }\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 988,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Button, {\n            variant: \"contained\",\n            color: \"primary\",\n            onClick: handleSearchCavoById,\n            disabled: caviLoading || !cavoIdInput.trim(),\n            startIcon: caviLoading ? /*#__PURE__*/_jsxDEV(CircularProgress, {\n              size: 16\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1002,\n              columnNumber: 40\n            }, this) : /*#__PURE__*/_jsxDEV(SearchIcon, {\n              fontSize: \"small\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1002,\n              columnNumber: 73\n            }, this),\n            size: \"small\",\n            sx: {\n              minWidth: '80px',\n              height: '36px'\n            },\n            children: \"CERCA\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 997,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 984,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 983,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Paper, {\n        sx: {\n          p: 1.5,\n          width: '100%'\n        },\n        children: [/*#__PURE__*/_jsxDEV(Typography, {\n          variant: \"subtitle2\",\n          sx: {\n            mb: 1\n          },\n          children: \"Seleziona dalla lista\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 1013,\n          columnNumber: 11\n        }, this), caviLoading ? /*#__PURE__*/_jsxDEV(Box, {\n          sx: {\n            display: 'flex',\n            justifyContent: 'center',\n            my: 2\n          },\n          children: /*#__PURE__*/_jsxDEV(CircularProgress, {\n            size: 24\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 1019,\n            columnNumber: 15\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 1018,\n          columnNumber: 13\n        }, this) : cavi.length === 0 ? /*#__PURE__*/_jsxDEV(Alert, {\n          severity: \"info\",\n          sx: {\n            py: 0.5\n          },\n          children: /*#__PURE__*/_jsxDEV(Typography, {\n            variant: \"caption\",\n            children: \"Non ci sono cavi disponibili da installare.\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 1023,\n            columnNumber: 15\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 1022,\n          columnNumber: 13\n        }, this) : /*#__PURE__*/_jsxDEV(TableContainer, {\n          component: Paper,\n          variant: \"outlined\",\n          sx: {\n            maxHeight: '300px',\n            overflow: 'auto',\n            width: '100%'\n          },\n          children: /*#__PURE__*/_jsxDEV(Table, {\n            size: \"small\",\n            stickyHeader: true,\n            children: [/*#__PURE__*/_jsxDEV(TableHead, {\n              children: /*#__PURE__*/_jsxDEV(TableRow, {\n                sx: {\n                  '& th': {\n                    fontWeight: 'bold',\n                    py: 1,\n                    bgcolor: '#f5f5f5'\n                  }\n                },\n                children: [/*#__PURE__*/_jsxDEV(TableCell, {\n                  children: \"ID Cavo\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1030,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n                  children: \"Tipologia\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1031,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n                  children: \"Formazione\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1032,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n                  children: \"Metri\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1033,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n                  children: \"Stato\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1034,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n                  align: \"center\",\n                  sx: {\n                    width: '40px'\n                  },\n                  children: \"Info\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1035,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 1029,\n                columnNumber: 19\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1028,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(TableBody, {\n              children: cavi.map(cavo => /*#__PURE__*/_jsxDEV(TableRow, {\n                hover: true,\n                onClick: () => handleCavoSelect(cavo),\n                sx: {\n                  cursor: 'pointer',\n                  '&:hover': {\n                    bgcolor: '#f1f8e9'\n                  },\n                  '& td': {\n                    py: 0.5\n                  }\n                },\n                children: [/*#__PURE__*/_jsxDEV(TableCell, {\n                  sx: {\n                    fontWeight: 'medium'\n                  },\n                  children: cavo.id_cavo\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1050,\n                  columnNumber: 23\n                }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n                  children: cavo.tipologia || 'N/A'\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1051,\n                  columnNumber: 23\n                }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n                  children: cavo.sezione || 'N/A'\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1052,\n                  columnNumber: 23\n                }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n                  children: cavo.metri_teorici || 'N/A'\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1053,\n                  columnNumber: 23\n                }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n                  children: /*#__PURE__*/_jsxDEV(Chip, {\n                    size: \"small\",\n                    label: isCableSpare(cavo) ? 'SPARE' : isCableInstalled(cavo) ? 'Installato' : cavo.stato_installazione,\n                    color: isCableSpare(cavo) ? 'error' : isCableInstalled(cavo) ? 'success' : getCableStateColor(cavo.stato_installazione),\n                    sx: {\n                      height: '20px',\n                      '& .MuiChip-label': {\n                        px: 1,\n                        py: 0,\n                        fontSize: '0.7rem'\n                      }\n                    }\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1055,\n                    columnNumber: 25\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1054,\n                  columnNumber: 23\n                }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n                  align: \"center\",\n                  children: /*#__PURE__*/_jsxDEV(IconButton, {\n                    size: \"small\",\n                    onClick: e => {\n                      e.stopPropagation();\n                      setSelectedCavo(cavo);\n                      setShowCavoDetailsDialog(true);\n                    },\n                    children: /*#__PURE__*/_jsxDEV(InfoIcon, {\n                      fontSize: \"small\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1071,\n                      columnNumber: 27\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1063,\n                    columnNumber: 25\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1062,\n                  columnNumber: 23\n                }, this)]\n              }, cavo.id_cavo, true, {\n                fileName: _jsxFileName,\n                lineNumber: 1040,\n                columnNumber: 21\n              }, this))\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1038,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 1027,\n            columnNumber: 15\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 1026,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 1012,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 977,\n      columnNumber: 7\n    }, this);\n  };\n\n  // Renderizza il passo 2: Inserimento metri\n  const renderStep2 = () => {\n    if (!selectedCavo) return null;\n    return /*#__PURE__*/_jsxDEV(Box, {\n      children: [/*#__PURE__*/_jsxDEV(Typography, {\n        variant: \"subtitle2\",\n        sx: {\n          mb: 1,\n          fontWeight: 'bold'\n        },\n        children: \"Inserisci metri posati\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 1091,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Paper, {\n        sx: {\n          p: 1.5,\n          mb: 2,\n          width: '100%'\n        },\n        children: /*#__PURE__*/_jsxDEV(Box, {\n          sx: {\n            display: 'flex',\n            flexWrap: 'wrap',\n            alignItems: 'center',\n            width: '100%'\n          },\n          children: [/*#__PURE__*/_jsxDEV(Typography, {\n            variant: \"subtitle2\",\n            sx: {\n              mr: 2,\n              fontWeight: 'bold'\n            },\n            children: [\"Cavo selezionato: \", /*#__PURE__*/_jsxDEV(\"span\", {\n              style: {\n                color: '#1976d2'\n              },\n              children: selectedCavo.id_cavo\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1099,\n              columnNumber: 33\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 1098,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Box, {\n            sx: {\n              display: 'flex',\n              flexWrap: 'wrap',\n              gap: 2,\n              ml: 'auto'\n            },\n            children: [/*#__PURE__*/_jsxDEV(Box, {\n              sx: {\n                display: 'flex',\n                alignItems: 'center'\n              },\n              children: [/*#__PURE__*/_jsxDEV(Typography, {\n                variant: \"body2\",\n                sx: {\n                  mr: 0.5,\n                  fontWeight: 'medium'\n                },\n                children: \"Tipologia:\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1104,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                variant: \"body2\",\n                children: selectedCavo.tipologia || 'N/A'\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1105,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 1103,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Box, {\n              sx: {\n                display: 'flex',\n                alignItems: 'center'\n              },\n              children: [/*#__PURE__*/_jsxDEV(Typography, {\n                variant: \"body2\",\n                sx: {\n                  mr: 0.5,\n                  fontWeight: 'medium'\n                },\n                children: \"Formazione:\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1109,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                variant: \"body2\",\n                children: selectedCavo.sezione || 'N/A'\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1110,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 1108,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Box, {\n              sx: {\n                display: 'flex',\n                alignItems: 'center'\n              },\n              children: [/*#__PURE__*/_jsxDEV(Typography, {\n                variant: \"body2\",\n                sx: {\n                  mr: 0.5,\n                  fontWeight: 'medium'\n                },\n                children: \"Metri teorici:\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1114,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                variant: \"body2\",\n                children: [selectedCavo.metri_teorici || 'N/A', \" m\"]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 1115,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 1113,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Box, {\n              sx: {\n                display: 'flex',\n                alignItems: 'center'\n              },\n              children: [/*#__PURE__*/_jsxDEV(Typography, {\n                variant: \"body2\",\n                sx: {\n                  mr: 0.5,\n                  fontWeight: 'medium'\n                },\n                children: \"Stato:\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1119,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(Chip, {\n                label: selectedCavo.stato_installazione || 'N/D',\n                size: \"small\",\n                color: getCableStateColor(selectedCavo.stato_installazione),\n                variant: \"outlined\",\n                sx: {\n                  height: '20px',\n                  '& .MuiChip-label': {\n                    px: 1,\n                    py: 0\n                  }\n                }\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1120,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 1118,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 1102,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 1097,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 1096,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Paper, {\n        sx: {\n          p: 2,\n          width: '100%'\n        },\n        children: [/*#__PURE__*/_jsxDEV(Typography, {\n          variant: \"subtitle2\",\n          gutterBottom: true,\n          sx: {\n            fontWeight: 'bold'\n          },\n          children: \"Inserisci i metri posati\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 1133,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Box, {\n          sx: {\n            display: 'flex',\n            flexWrap: 'wrap',\n            gap: 2,\n            mb: 2\n          },\n          children: [/*#__PURE__*/_jsxDEV(Box, {\n            sx: {\n              p: 1,\n              bgcolor: '#f5f5f5',\n              borderRadius: 1,\n              flex: '1 1 auto',\n              minWidth: '200px',\n              maxWidth: '400px'\n            },\n            children: [/*#__PURE__*/_jsxDEV(Typography, {\n              variant: \"caption\",\n              sx: {\n                fontWeight: 'bold',\n                color: 'primary.main',\n                display: 'block',\n                mb: 0.5\n              },\n              children: \"Informazioni cavo\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1140,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Box, {\n              sx: {\n                display: 'flex',\n                justifyContent: 'space-between',\n                mb: 0.5\n              },\n              children: [/*#__PURE__*/_jsxDEV(Typography, {\n                variant: \"body2\",\n                sx: {\n                  fontWeight: 'medium',\n                  fontSize: '0.8rem'\n                },\n                children: \"Metri teorici:\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1144,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                variant: \"body2\",\n                sx: {\n                  fontSize: '0.8rem'\n                },\n                children: [selectedCavo.metri_teorici || 'N/A', \" m\"]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 1145,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 1143,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Box, {\n              sx: {\n                display: 'flex',\n                justifyContent: 'space-between'\n              },\n              children: [/*#__PURE__*/_jsxDEV(Typography, {\n                variant: \"body2\",\n                sx: {\n                  fontWeight: 'medium',\n                  fontSize: '0.8rem'\n                },\n                children: \"Stato attuale:\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1148,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(Chip, {\n                label: selectedCavo.stato_installazione || 'N/D',\n                size: \"small\",\n                color: getCableStateColor(selectedCavo.stato_installazione),\n                variant: \"outlined\",\n                sx: {\n                  height: '18px',\n                  '& .MuiChip-label': {\n                    px: 1,\n                    py: 0,\n                    fontSize: '0.7rem'\n                  }\n                }\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1149,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 1147,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 1139,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Box, {\n            sx: {\n              p: 1,\n              bgcolor: '#f5f5f5',\n              borderRadius: 1,\n              flex: '1 1 auto',\n              minWidth: '200px',\n              maxWidth: '400px'\n            },\n            children: [/*#__PURE__*/_jsxDEV(Typography, {\n              variant: \"caption\",\n              sx: {\n                fontWeight: 'bold',\n                color: 'secondary.main',\n                display: 'block',\n                mb: 0.5\n              },\n              children: \"Informazioni bobina\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1160,\n              columnNumber: 15\n            }, this), formData.id_bobina && formData.id_bobina !== 'BOBINA_VUOTA' ? (() => {\n              const bobina = bobine.find(b => b.id_bobina === formData.id_bobina);\n              return bobina ? /*#__PURE__*/_jsxDEV(_Fragment, {\n                children: [/*#__PURE__*/_jsxDEV(Box, {\n                  sx: {\n                    display: 'flex',\n                    justifyContent: 'space-between',\n                    mb: 0.5\n                  },\n                  children: [/*#__PURE__*/_jsxDEV(Typography, {\n                    variant: \"body2\",\n                    sx: {\n                      fontWeight: 'medium',\n                      fontSize: '0.8rem'\n                    },\n                    children: \"ID Bobina:\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1168,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                    variant: \"body2\",\n                    sx: {\n                      fontSize: '0.8rem'\n                    },\n                    children: getBobinaNumber(bobina.id_bobina)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1169,\n                    columnNumber: 23\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1167,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(Box, {\n                  sx: {\n                    display: 'flex',\n                    justifyContent: 'space-between',\n                    mb: 0.5\n                  },\n                  children: [/*#__PURE__*/_jsxDEV(Typography, {\n                    variant: \"body2\",\n                    sx: {\n                      fontWeight: 'medium',\n                      fontSize: '0.8rem'\n                    },\n                    children: \"Metri residui:\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1172,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                    variant: \"body2\",\n                    sx: {\n                      fontSize: '0.8rem'\n                    },\n                    children: [bobina.metri_residui || 0, \" m\"]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1173,\n                    columnNumber: 23\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1171,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(Box, {\n                  sx: {\n                    display: 'flex',\n                    justifyContent: 'space-between'\n                  },\n                  children: [/*#__PURE__*/_jsxDEV(Typography, {\n                    variant: \"body2\",\n                    sx: {\n                      fontWeight: 'medium',\n                      fontSize: '0.8rem'\n                    },\n                    children: \"Stato:\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1176,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(Chip, {\n                    label: bobina.stato_bobina || 'N/D',\n                    size: \"small\",\n                    color: getReelStateColor(bobina.stato_bobina),\n                    variant: \"outlined\",\n                    sx: {\n                      height: '18px',\n                      '& .MuiChip-label': {\n                        px: 1,\n                        py: 0,\n                        fontSize: '0.7rem'\n                      }\n                    }\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1177,\n                    columnNumber: 23\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1175,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true) : /*#__PURE__*/_jsxDEV(Typography, {\n                variant: \"body2\",\n                sx: {\n                  fontSize: '0.8rem'\n                },\n                children: \"Bobina non trovata\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1187,\n                columnNumber: 19\n              }, this);\n            })() : /*#__PURE__*/_jsxDEV(Typography, {\n              variant: \"body2\",\n              sx: {\n                fontSize: '0.8rem'\n              },\n              children: formData.id_bobina === 'BOBINA_VUOTA' ? \"Utilizzo BOBINA VUOTA (nessuna bobina associata)\" : \"Nessuna bobina selezionata\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1190,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 1159,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 1138,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Box, {\n          sx: {\n            mt: 2,\n            mb: 2,\n            width: '100%'\n          },\n          children: [/*#__PURE__*/_jsxDEV(Typography, {\n            variant: \"subtitle2\",\n            gutterBottom: true,\n            sx: {\n              fontWeight: 'bold'\n            },\n            children: \"Metratura posata\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 1200,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(TextField, {\n            size: \"small\",\n            label: \"Metri posati\",\n            variant: \"outlined\",\n            name: \"metri_posati\",\n            type: \"number\",\n            value: formData.metri_posati,\n            onChange: handleFormChange,\n            error: !!formErrors.metri_posati,\n            helperText: formErrors.metri_posati || formWarnings.metri_posati,\n            FormHelperTextProps: {\n              sx: {\n                color: formWarnings.metri_posati && !formErrors.metri_posati ? 'warning.main' : 'error.main'\n              }\n            },\n            sx: {\n              mb: 1,\n              width: '200px'\n            },\n            inputProps: {\n              max: 999999,\n              // Limite a 6 cifre\n              step: 0.1\n            }\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 1203,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 1199,\n          columnNumber: 11\n        }, this), formWarnings.metri_posati && !formErrors.metri_posati && !notificationShown && /*#__PURE__*/_jsxDEV(Alert, {\n          severity: \"warning\",\n          sx: {\n            mb: 2\n          },\n          children: formWarnings.metri_posati\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 1225,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(Alert, {\n          severity: \"info\",\n          sx: {\n            mt: 2\n          },\n          children: /*#__PURE__*/_jsxDEV(Typography, {\n            variant: \"body2\",\n            children: \"Inserisci i metri di cavo effettivamente posati. Questo valore aggiorna lo stato di installazione del cavo.\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 1231,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 1230,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 1132,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 1090,\n      columnNumber: 7\n    }, this);\n  };\n\n  // Renderizza il passo 3: Associazione bobina\n  const renderStep3 = () => {\n    // Funzione per costruire l'ID completo della bobina\n    const buildFullBobinaId = numeroBobina => {\n      return `C${cantiereId}_B${numeroBobina}`;\n    };\n\n    // Verifica se una bobina ha metri residui sufficienti\n    const hasSufficientMeters = bobina => {\n      if (!bobina || !formData.metri_posati) return true;\n      return parseFloat(bobina.metri_residui) >= parseFloat(formData.metri_posati);\n    };\n\n    // Gestisce l'input diretto del numero della bobina\n    const handleBobinaNumberInput = e => {\n      const numeroBobina = e.target.value.trim();\n\n      // Gestione esplicita dell'input 'v' per bobina vuota\n      if (numeroBobina.toLowerCase() === 'v') {\n        setFormData({\n          ...formData,\n          id_bobina: ''\n        });\n        setFormErrors({\n          ...formErrors,\n          id_bobina_input: null\n        });\n        return;\n      }\n      if (numeroBobina) {\n        // Costruisci l'ID completo\n        const idBobinaCompleto = buildFullBobinaId(numeroBobina);\n\n        // Verifica se la bobina esiste\n        const bobinaEsistente = bobine.find(b => b.id_bobina === idBobinaCompleto);\n        if (bobinaEsistente) {\n          // Verifica se la bobina è in stato OVER o TERMINATA\n          if (bobinaEsistente.stato_bobina === 'Over' || bobinaEsistente.stato_bobina === 'Terminata') {\n            setFormErrors({\n              ...formErrors,\n              id_bobina_input: `La bobina ${numeroBobina} è in stato ${bobinaEsistente.stato_bobina} e non può essere utilizzata`\n            });\n            return;\n          }\n\n          // Verifica compatibilità tra cavo e bobina\n          if (selectedCavo) {\n            // Converti i valori in stringhe, gestendo null e undefined\n            const cavoTipologia = String(selectedCavo.tipologia || '');\n            const cavoSezione = String(selectedCavo.sezione || '0');\n            const bobinaTipologia = String(bobinaEsistente.tipologia || '');\n            const bobinaSezione = String(bobinaEsistente.sezione || '0');\n\n            // Log per debug\n            console.log(`Verifica compatibilità bobina ${bobinaEsistente.id_bobina}:`, {\n              tipologia: `${bobinaTipologia} === ${cavoTipologia}`,\n              formazione: `${bobinaSezione} === ${cavoSezione}`\n            });\n            if (bobinaTipologia !== cavoTipologia || bobinaSezione !== cavoSezione) {\n              // Mostra il dialogo per bobine incompatibili\n              setIncompatibleReelData({\n                cavo: selectedCavo,\n                bobina: bobinaEsistente\n              });\n              setShowIncompatibleReelDialog(true);\n              return;\n            }\n          }\n\n          // Verifica se la bobina ha metri residui sufficienti\n          if (hasSufficientMeters(bobinaEsistente)) {\n            // Se la bobina esiste e ha metri sufficienti, imposta l'ID completo\n            setFormData({\n              ...formData,\n              id_bobina: idBobinaCompleto\n            });\n            setFormErrors({\n              ...formErrors,\n              id_bobina_input: null\n            });\n          } else {\n            // Se la bobina non ha metri sufficienti, mostra un avviso\n            setFormErrors({\n              ...formErrors,\n              id_bobina_input: `La bobina ${numeroBobina} non ha metri residui sufficienti (${bobinaEsistente.metri_residui} m disponibili)`\n            });\n          }\n        } else {\n          // Se la bobina non esiste, mostra un errore\n          setFormErrors({\n            ...formErrors,\n            id_bobina_input: `Bobina ${numeroBobina} non trovata`\n          });\n        }\n      } else {\n        // Se l'input è vuoto, resetta l'ID bobina\n        setFormData({\n          ...formData,\n          id_bobina: ''\n        });\n        setFormErrors({\n          ...formErrors,\n          id_bobina_input: null\n        });\n      }\n    };\n    return /*#__PURE__*/_jsxDEV(Box, {\n      children: [/*#__PURE__*/_jsxDEV(Typography, {\n        variant: \"subtitle2\",\n        sx: {\n          mb: 1,\n          fontWeight: 'bold'\n        },\n        children: \"Associa bobina\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 1355,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Paper, {\n        sx: {\n          p: 2\n        },\n        children: [/*#__PURE__*/_jsxDEV(Typography, {\n          variant: \"body2\",\n          sx: {\n            mb: 1\n          },\n          children: \"Seleziona una bobina da associare al cavo o usa \\\"BOBINA VUOTA\\\" se non desideri associare una bobina specifica.\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 1360,\n          columnNumber: 11\n        }, this), bobineLoading ? /*#__PURE__*/_jsxDEV(Box, {\n          sx: {\n            display: 'flex',\n            justifyContent: 'center',\n            my: 1\n          },\n          children: /*#__PURE__*/_jsxDEV(CircularProgress, {\n            size: 24\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 1366,\n            columnNumber: 15\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 1365,\n          columnNumber: 13\n        }, this) : /*#__PURE__*/_jsxDEV(Box, {\n          children: /*#__PURE__*/_jsxDEV(Box, {\n            sx: {\n              display: 'flex',\n              flexWrap: 'wrap',\n              gap: 2\n            },\n            children: [/*#__PURE__*/_jsxDEV(Box, {\n              sx: {\n                flex: '1 1 200px',\n                minWidth: '200px',\n                maxWidth: '300px'\n              },\n              children: [/*#__PURE__*/_jsxDEV(Typography, {\n                variant: \"caption\",\n                sx: {\n                  fontWeight: 'bold',\n                  display: 'block',\n                  mb: 0.5\n                },\n                children: \"Inserimento diretto\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1373,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(Box, {\n                sx: {\n                  display: 'flex',\n                  alignItems: 'flex-start',\n                  gap: 1\n                },\n                children: /*#__PURE__*/_jsxDEV(TextField, {\n                  size: \"small\",\n                  label: \"Numero bobina\",\n                  variant: \"outlined\",\n                  placeholder: \"Solo Y\",\n                  error: !!formErrors.id_bobina_input,\n                  onBlur: handleBobinaNumberInput,\n                  sx: {\n                    flex: 1\n                  }\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1377,\n                  columnNumber: 21\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1376,\n                columnNumber: 19\n              }, this), formErrors.id_bobina_input && /*#__PURE__*/_jsxDEV(Typography, {\n                variant: \"caption\",\n                color: \"error\",\n                sx: {\n                  display: 'block',\n                  mt: 0.5\n                },\n                children: formErrors.id_bobina_input\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1388,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                variant: \"caption\",\n                sx: {\n                  display: 'block',\n                  mt: 0.5\n                },\n                children: [\"ID Bobina: \", /*#__PURE__*/_jsxDEV(\"strong\", {\n                  children: formData.id_bobina || '-'\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1393,\n                  columnNumber: 32\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 1392,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 1372,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(Box, {\n              sx: {\n                flex: '1 1 300px',\n                minWidth: '300px'\n              },\n              children: [/*#__PURE__*/_jsxDEV(Typography, {\n                variant: \"caption\",\n                sx: {\n                  fontWeight: 'bold',\n                  display: 'block',\n                  mb: 0.5\n                },\n                children: selectedCavo ? 'Bobine compatibili con il cavo selezionato' : 'Seleziona una bobina'\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1399,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(FormControl, {\n                fullWidth: true,\n                size: \"small\",\n                error: !!formErrors.id_bobina,\n                children: [/*#__PURE__*/_jsxDEV(InputLabel, {\n                  id: \"bobina-select-label\",\n                  children: \"Seleziona bobina\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1404,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(Select, {\n                  labelId: \"bobina-select-label\",\n                  id: \"bobina-select\",\n                  name: \"id_bobina\",\n                  value: formData.id_bobina,\n                  label: \"Seleziona bobina\",\n                  onChange: handleFormChange,\n                  sx: {\n                    maxHeight: '38px'\n                  },\n                  children: [/*#__PURE__*/_jsxDEV(MenuItem, {\n                    value: \"BOBINA_VUOTA\",\n                    children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n                      children: \"BOBINA VUOTA\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1415,\n                      columnNumber: 25\n                    }, this), \" (nessuna bobina associata)\"]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1414,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(Divider, {}, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1417,\n                    columnNumber: 23\n                  }, this), bobine.length > 0 ? /*#__PURE__*/_jsxDEV(Box, {\n                    component: \"li\",\n                    sx: {\n                      p: 0.5,\n                      bgcolor: 'background.paper'\n                    },\n                    children: /*#__PURE__*/_jsxDEV(Typography, {\n                      variant: \"caption\",\n                      sx: {\n                        fontWeight: 'bold',\n                        color: 'success.main',\n                        fontSize: '0.7rem'\n                      },\n                      children: [\"Bobine disponibili: \", bobine.length, \" (le compatibili sono evidenziate)\"]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1420,\n                      columnNumber: 27\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1419,\n                    columnNumber: 25\n                  }, this) : null, bobine.map(bobina => /*#__PURE__*/_jsxDEV(MenuItem, {\n                    value: bobina.id_bobina,\n                    disabled: bobina.metri_residui < parseFloat(formData.metri_posati || 0),\n                    sx: {\n                      py: 0.5,\n                      '&.Mui-selected': {\n                        bgcolor: 'success.light'\n                      },\n                      '&.Mui-selected:hover': {\n                        bgcolor: 'success.light'\n                      },\n                      bgcolor: selectedCavo && String(bobina.tipologia || '').trim() === String(selectedCavo.tipologia || '').trim() && String(bobina.sezione || '0').trim() === String(selectedCavo.sezione || '0').trim() ? 'rgba(76, 175, 80, 0.08)' : 'inherit'\n                    },\n                    children: /*#__PURE__*/_jsxDEV(Box, {\n                      sx: {\n                        display: 'flex',\n                        flexDirection: 'column',\n                        width: '100%'\n                      },\n                      children: [/*#__PURE__*/_jsxDEV(Box, {\n                        sx: {\n                          display: 'flex',\n                          justifyContent: 'space-between',\n                          alignItems: 'center',\n                          width: '100%'\n                        },\n                        children: [/*#__PURE__*/_jsxDEV(Typography, {\n                          variant: \"body2\",\n                          sx: {\n                            fontWeight: 'bold',\n                            fontSize: '0.8rem'\n                          },\n                          children: [getBobinaNumber(bobina.id_bobina), \" - \", bobina.tipologia || 'N/A']\n                        }, void 0, true, {\n                          fileName: _jsxFileName,\n                          lineNumber: 1442,\n                          columnNumber: 31\n                        }, this), selectedCavo && String(bobina.tipologia || '').trim() === String(selectedCavo.tipologia || '').trim() && String(bobina.sezione || '0').trim() === String(selectedCavo.sezione || '0').trim() && /*#__PURE__*/_jsxDEV(Chip, {\n                          size: \"small\",\n                          label: \"Compatibile\",\n                          color: \"success\",\n                          variant: \"outlined\",\n                          sx: {\n                            height: 16,\n                            fontSize: '0.6rem',\n                            '& .MuiChip-label': {\n                              px: 0.5,\n                              py: 0\n                            }\n                          }\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 1448,\n                          columnNumber: 33\n                        }, this)]\n                      }, void 0, true, {\n                        fileName: _jsxFileName,\n                        lineNumber: 1441,\n                        columnNumber: 29\n                      }, this), /*#__PURE__*/_jsxDEV(Box, {\n                        sx: {\n                          display: 'flex',\n                          justifyContent: 'space-between',\n                          width: '100%'\n                        },\n                        children: [/*#__PURE__*/_jsxDEV(Typography, {\n                          variant: \"caption\",\n                          sx: {\n                            fontSize: '0.7rem'\n                          },\n                          children: bobina.sezione || 'N/A'\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 1458,\n                          columnNumber: 31\n                        }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                          variant: \"caption\",\n                          sx: {\n                            fontWeight: 'bold',\n                            fontSize: '0.7rem',\n                            color: bobina.metri_residui < parseFloat(formData.metri_posati || 0) ? 'error.main' : 'success.main'\n                          },\n                          children: [bobina.metri_residui || 0, \" m disponibili\"]\n                        }, void 0, true, {\n                          fileName: _jsxFileName,\n                          lineNumber: 1461,\n                          columnNumber: 31\n                        }, this)]\n                      }, void 0, true, {\n                        fileName: _jsxFileName,\n                        lineNumber: 1457,\n                        columnNumber: 29\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1440,\n                      columnNumber: 27\n                    }, this)\n                  }, bobina.id_bobina, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1426,\n                    columnNumber: 25\n                  }, this))]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1405,\n                  columnNumber: 21\n                }, this), formErrors.id_bobina && /*#__PURE__*/_jsxDEV(FormHelperText, {\n                  children: formErrors.id_bobina\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1470,\n                  columnNumber: 23\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 1403,\n                columnNumber: 19\n              }, this), bobine.length === 0 && !bobineLoading && /*#__PURE__*/_jsxDEV(Typography, {\n                variant: \"caption\",\n                color: \"warning.main\",\n                sx: {\n                  display: 'block',\n                  mt: 0.5\n                },\n                children: \"Nessuna bobina compatibile trovata. Usa BOBINA VUOTA o seleziona una bobina non compatibile.\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1475,\n                columnNumber: 21\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 1398,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 1370,\n            columnNumber: 15\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 1369,\n          columnNumber: 13\n        }, this), !bobineLoading && formData.id_bobina && /*#__PURE__*/_jsxDEV(Box, {\n          sx: {\n            mt: 3,\n            p: 2,\n            bgcolor: 'background.paper',\n            borderRadius: 1,\n            border: '1px solid #e0e0e0'\n          },\n          children: [/*#__PURE__*/_jsxDEV(Typography, {\n            variant: \"subtitle2\",\n            gutterBottom: true,\n            children: \"Dettagli bobina selezionata\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 1487,\n            columnNumber: 15\n          }, this), (() => {\n            const bobina = bobine.find(b => b.id_bobina === formData.id_bobina);\n            if (bobina) {\n              return /*#__PURE__*/_jsxDEV(Grid, {\n                container: true,\n                spacing: 2,\n                children: [/*#__PURE__*/_jsxDEV(Grid, {\n                  item: true,\n                  xs: 12,\n                  md: 6,\n                  children: [/*#__PURE__*/_jsxDEV(Typography, {\n                    variant: \"body2\",\n                    children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n                      children: \"Numero:\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1497,\n                      columnNumber: 27\n                    }, this), \" \", getBobinaNumber(bobina.id_bobina)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1496,\n                    columnNumber: 25\n                  }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                    variant: \"body2\",\n                    children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n                      children: \"Tipologia:\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1500,\n                      columnNumber: 27\n                    }, this), \" \", bobina.tipologia || 'N/A']\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1499,\n                    columnNumber: 25\n                  }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                    variant: \"body2\",\n                    children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n                      children: \"Conduttori:\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1503,\n                      columnNumber: 27\n                    }, this), \" \", bobina.n_conduttori || 'N/A', \" x \", bobina.sezione || 'N/A']\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1502,\n                    columnNumber: 25\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1495,\n                  columnNumber: 23\n                }, this), /*#__PURE__*/_jsxDEV(Grid, {\n                  item: true,\n                  xs: 12,\n                  md: 6,\n                  children: [/*#__PURE__*/_jsxDEV(Typography, {\n                    variant: \"body2\",\n                    children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n                      children: \"Metri totali:\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1508,\n                      columnNumber: 27\n                    }, this), \" \", bobina.metri_totali || 0, \" m\"]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1507,\n                    columnNumber: 25\n                  }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                    variant: \"body2\",\n                    children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n                      children: \"Metri residui:\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1511,\n                      columnNumber: 27\n                    }, this), \" \", bobina.metri_residui || 0, \" m\"]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1510,\n                    columnNumber: 25\n                  }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                    variant: \"body2\",\n                    children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n                      children: \"Stato:\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1514,\n                      columnNumber: 27\n                    }, this), \" \", bobina.stato_bobina || 'N/A']\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1513,\n                    columnNumber: 25\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1506,\n                  columnNumber: 23\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 1494,\n                columnNumber: 21\n              }, this);\n            }\n            return /*#__PURE__*/_jsxDEV(Typography, {\n              variant: \"body2\",\n              color: \"error\",\n              children: \"Bobina non trovata nel database\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1521,\n              columnNumber: 19\n            }, this);\n          })()]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 1486,\n          columnNumber: 13\n        }, this), bobine.length === 0 && !bobineLoading && /*#__PURE__*/_jsxDEV(Alert, {\n          severity: \"warning\",\n          sx: {\n            mt: 2\n          },\n          children: \"Non ci sono bobine disponibili. Puoi procedere senza associare una bobina o aggiungere prima una nuova bobina.\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 1530,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 1359,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 1354,\n      columnNumber: 7\n    }, this);\n  };\n\n  // Renderizza il passo 4: Conferma\n  const renderStep4 = () => {\n    // Ottieni il numero della bobina se presente\n    let numeroBobina = 'Nessuna';\n    let bobinaInfo = null;\n    if (formData.id_bobina === 'BOBINA_VUOTA') {\n      numeroBobina = 'BOBINA VUOTA';\n    } else if (formData.id_bobina) {\n      numeroBobina = getBobinaNumber(formData.id_bobina);\n      // Trova i dettagli della bobina selezionata\n      bobinaInfo = bobine.find(b => b.id_bobina === formData.id_bobina);\n    }\n\n    // Determina lo stato di installazione\n    const statoInstallazione = determineInstallationStatus(parseFloat(formData.metri_posati), selectedCavo.metri_teorici);\n    return /*#__PURE__*/_jsxDEV(Box, {\n      children: [/*#__PURE__*/_jsxDEV(Typography, {\n        variant: \"h6\",\n        gutterBottom: true,\n        children: \"Conferma inserimento\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 1559,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Paper, {\n        sx: {\n          p: 3\n        },\n        children: [/*#__PURE__*/_jsxDEV(Typography, {\n          variant: \"subtitle1\",\n          gutterBottom: true,\n          children: \"Riepilogo dati\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 1564,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(CavoDetailsView, {\n          cavo: selectedCavo,\n          compact: true,\n          title: \"Dettagli del cavo\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 1569,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Box, {\n          sx: {\n            mt: 3,\n            p: 2,\n            bgcolor: '#f5f5f5',\n            borderRadius: 1\n          },\n          children: [/*#__PURE__*/_jsxDEV(Typography, {\n            variant: \"subtitle2\",\n            gutterBottom: true,\n            sx: {\n              fontWeight: 'bold'\n            },\n            children: \"Informazioni sull'operazione:\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 1577,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Grid, {\n            container: true,\n            spacing: 2,\n            children: [/*#__PURE__*/_jsxDEV(Grid, {\n              item: true,\n              xs: 12,\n              md: 6,\n              children: [/*#__PURE__*/_jsxDEV(Typography, {\n                variant: \"body2\",\n                children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n                  children: \"Metri Posati:\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1583,\n                  columnNumber: 19\n                }, this), \" \", formData.metri_posati, \" m\"]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 1582,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                variant: \"body2\",\n                children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n                  children: \"Stato Installazione:\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1586,\n                  columnNumber: 19\n                }, this), \" \", statoInstallazione]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 1585,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 1581,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Grid, {\n              item: true,\n              xs: 12,\n              md: 6,\n              children: [/*#__PURE__*/_jsxDEV(Typography, {\n                variant: \"body2\",\n                children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n                  children: \"Bobina Associata:\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1591,\n                  columnNumber: 19\n                }, this), \" \", numeroBobina]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 1590,\n                columnNumber: 17\n              }, this), bobinaInfo && /*#__PURE__*/_jsxDEV(Typography, {\n                variant: \"body2\",\n                children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n                  children: \"Metri Residui Bobina:\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1595,\n                  columnNumber: 21\n                }, this), \" \", bobinaInfo.metri_residui, \" m\"]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 1594,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 1589,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 1580,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 1576,\n          columnNumber: 11\n        }, this), bobinaInfo && parseFloat(formData.metri_posati) > parseFloat(bobinaInfo.metri_residui) && !notificationShown && /*#__PURE__*/_jsxDEV(Alert, {\n          severity: \"warning\",\n          sx: {\n            mt: 3\n          },\n          children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n            children: \"Attenzione:\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 1604,\n            columnNumber: 15\n          }, this), \" I metri posati (\", formData.metri_posati, \"m) superano i metri residui della bobina (\", bobinaInfo.metri_residui, \"m). Questo porter\\xE0 la bobina in stato OVER.\"]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 1603,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(Alert, {\n          severity: \"info\",\n          sx: {\n            mt: 3\n          },\n          children: [\"Conferma per aggiornare i metri posati e lo stato di installazione del cavo.\", formData.id_bobina && formData.id_bobina !== 'BOBINA_VUOTA' && ' I metri posati verranno sottratti dai metri residui della bobina selezionata.']\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 1609,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 1563,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 1558,\n      columnNumber: 7\n    }, this);\n  };\n\n  // Renderizza il contenuto in base al passo attivo\n  const getStepContent = step => {\n    switch (step) {\n      case 0:\n        return renderStep1();\n      // Seleziona Cavo\n      case 1:\n        return renderStep3();\n      // Associa Bobina\n      case 2:\n        return renderStep2();\n      // Inserisci Metri\n      case 3:\n        return renderStep4();\n      // Conferma\n      default:\n        return 'Passo sconosciuto';\n    }\n  };\n\n  // Gestisce la chiusura del dialogo per cavi già posati\n  const handleCloseAlreadyLaidDialog = () => {\n    setShowAlreadyLaidDialog(false);\n    setAlreadyLaidCavo(null);\n  };\n\n  // Gestisce l'opzione di modificare la bobina di un cavo già posato\n  const handleModifyReel = () => {\n    if (alreadyLaidCavo) {\n      navigate(`/dashboard/cavi/modifica-bobina/${cantiereId}/${alreadyLaidCavo.id_cavo}`);\n    }\n    handleCloseAlreadyLaidDialog();\n  };\n\n  // Gestisce l'opzione di selezionare un altro cavo\n  const handleSelectAnotherCable = () => {\n    handleCloseAlreadyLaidDialog();\n    // Reset del form per selezionare un nuovo cavo\n    setSelectedCavo(null);\n    setCavoIdInput('');\n    setShowSearchResults(false);\n  };\n\n  // Gestisce la chiusura del dialogo per bobine incompatibili\n  const handleCloseIncompatibleReelDialog = () => {\n    setShowIncompatibleReelDialog(false);\n    setIncompatibleReelData({\n      cavo: null,\n      bobina: null\n    });\n  };\n\n  // Gestisce l'aggiornamento delle caratteristiche del cavo per farle corrispondere a quelle della bobina\n  const handleUpdateCavoToMatchReel = async () => {\n    const {\n      cavo,\n      bobina\n    } = incompatibleReelData;\n    if (!cavo || !bobina) {\n      console.error('Dati mancanti per aggiornare il cavo:', {\n        cavo,\n        bobina\n      });\n      onError('Dati mancanti per aggiornare il cavo');\n      return;\n    }\n    try {\n      setLoading(true);\n      console.log(`Aggiornamento caratteristiche del cavo ${cavo.id_cavo} per compatibilità con bobina ${bobina.id_bobina}`);\n      console.log('Dati cavo prima dell\\'aggiornamento:', cavo);\n      console.log('Dati bobina:', bobina);\n\n      // Aggiorna le caratteristiche del cavo\n      await caviService.updateCavoForCompatibility(cantiereId, cavo.id_cavo, bobina.id_bobina);\n\n      // Aggiorna il cavo selezionato con le nuove caratteristiche\n      const updatedCavo = await caviService.getCavoById(cantiereId, cavo.id_cavo);\n      console.log('Dati cavo dopo l\\'aggiornamento:', updatedCavo);\n      setSelectedCavo(updatedCavo);\n\n      // Imposta la bobina selezionata\n      setFormData({\n        ...formData,\n        id_bobina: bobina.id_bobina\n      });\n\n      // Ricarica le bobine per aggiornare l'interfaccia\n      await loadBobine();\n      onSuccess(`Caratteristiche del cavo ${cavo.id_cavo} aggiornate per corrispondere alla bobina ${bobina.id_bobina}`);\n      handleCloseIncompatibleReelDialog();\n    } catch (error) {\n      console.error('Errore durante l\\'aggiornamento delle caratteristiche del cavo:', error);\n      onError('Errore durante l\\'aggiornamento delle caratteristiche del cavo: ' + (error.detail || error.message || 'Errore sconosciuto'));\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  // Gestisce la selezione di un'altra bobina\n  const handleSelectAnotherReel = () => {\n    handleCloseIncompatibleReelDialog();\n    // Reset della bobina selezionata\n    setFormData({\n      ...formData,\n      id_bobina: ''\n    });\n  };\n  return /*#__PURE__*/_jsxDEV(Box, {\n    children: [/*#__PURE__*/_jsxDEV(Paper, {\n      sx: {\n        p: 3,\n        mb: 3\n      },\n      children: [/*#__PURE__*/_jsxDEV(Typography, {\n        variant: \"h6\",\n        gutterBottom: true,\n        children: \"Cerca cavo\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 1721,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Grid, {\n        container: true,\n        spacing: 2,\n        alignItems: \"center\",\n        children: [/*#__PURE__*/_jsxDEV(Grid, {\n          item: true,\n          xs: 9,\n          children: /*#__PURE__*/_jsxDEV(TextField, {\n            fullWidth: true,\n            label: \"ID Cavo\",\n            variant: \"outlined\",\n            value: cavoIdInput,\n            onChange: e => setCavoIdInput(e.target.value),\n            placeholder: \"Inserisci l'ID del cavo o parte di esso\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 1726,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 1725,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Grid, {\n          item: true,\n          xs: 3,\n          children: /*#__PURE__*/_jsxDEV(Button, {\n            fullWidth: true,\n            variant: \"contained\",\n            color: \"primary\",\n            onClick: handleSearchCavoById,\n            disabled: caviLoading || !cavoIdInput.trim(),\n            startIcon: caviLoading ? /*#__PURE__*/_jsxDEV(CircularProgress, {\n              size: 20\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1742,\n              columnNumber: 40\n            }, this) : /*#__PURE__*/_jsxDEV(SearchIcon, {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1742,\n              columnNumber: 73\n            }, this),\n            children: \"Cerca\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 1736,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 1735,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 1724,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 1720,\n      columnNumber: 7\n    }, this), showSearchResults && searchResults.length > 0 && /*#__PURE__*/_jsxDEV(Paper, {\n      sx: {\n        p: 3,\n        mb: 3\n      },\n      children: [/*#__PURE__*/_jsxDEV(Typography, {\n        variant: \"h6\",\n        gutterBottom: true,\n        children: \"Risultati della ricerca\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 1753,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(TableContainer, {\n        children: /*#__PURE__*/_jsxDEV(Table, {\n          size: \"small\",\n          children: [/*#__PURE__*/_jsxDEV(TableHead, {\n            children: /*#__PURE__*/_jsxDEV(TableRow, {\n              sx: {\n                bgcolor: '#f5f5f5'\n              },\n              children: [/*#__PURE__*/_jsxDEV(TableCell, {\n                children: \"ID Cavo\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1760,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n                children: \"Tipologia\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1761,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n                children: \"Formazione\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1762,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n                children: \"Ubicazione\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1763,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n                children: \"Metri Teorici\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1764,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n                children: \"Stato\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1765,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n                children: \"Azioni\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1766,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 1759,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 1758,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(TableBody, {\n            children: searchResults.map(cavo => /*#__PURE__*/_jsxDEV(TableRow, {\n              children: [/*#__PURE__*/_jsxDEV(TableCell, {\n                children: cavo.id_cavo\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1772,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n                children: cavo.tipologia || 'N/A'\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1773,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n                children: cavo.sezione || 'N/A'\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1774,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n                children: [\"Da: \", cavo.ubicazione_partenza || 'N/A', /*#__PURE__*/_jsxDEV(\"br\", {}, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1775,\n                  columnNumber: 71\n                }, this), \"A: \", cavo.ubicazione_arrivo || 'N/A']\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 1775,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n                children: [cavo.metri_teorici || 'N/A', \" m\"]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 1776,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n                children: /*#__PURE__*/_jsxDEV(Chip, {\n                  label: cavo.stato_installazione || 'N/D',\n                  size: \"small\",\n                  color: getCableStateColor(cavo.stato_installazione),\n                  variant: \"outlined\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1778,\n                  columnNumber: 23\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1777,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n                children: /*#__PURE__*/_jsxDEV(Button, {\n                  size: \"small\",\n                  variant: \"contained\",\n                  color: \"primary\",\n                  onClick: () => handleCavoSelect(cavo),\n                  disabled: isCableInstalled(cavo),\n                  children: \"Seleziona\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1786,\n                  columnNumber: 23\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1785,\n                columnNumber: 21\n              }, this)]\n            }, cavo.id_cavo, true, {\n              fileName: _jsxFileName,\n              lineNumber: 1771,\n              columnNumber: 19\n            }, this))\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 1769,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 1757,\n          columnNumber: 13\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 1756,\n        columnNumber: 11\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 1752,\n      columnNumber: 9\n    }, this), selectedCavo && /*#__PURE__*/_jsxDEV(Paper, {\n      sx: {\n        p: 3\n      },\n      children: [/*#__PURE__*/_jsxDEV(Typography, {\n        variant: \"h6\",\n        gutterBottom: true,\n        children: \"Inserimento metri posati\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 1807,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(Box, {\n        sx: {\n          p: 2,\n          bgcolor: '#f5f5f5',\n          borderRadius: 1,\n          mb: 3\n        },\n        children: [/*#__PURE__*/_jsxDEV(Typography, {\n          variant: \"subtitle2\",\n          gutterBottom: true,\n          sx: {\n            fontWeight: 'bold',\n            color: 'primary.main'\n          },\n          children: [\"Cavo selezionato: \", selectedCavo.id_cavo]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 1813,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(Grid, {\n          container: true,\n          spacing: 2,\n          children: [/*#__PURE__*/_jsxDEV(Grid, {\n            item: true,\n            xs: 12,\n            md: 6,\n            children: [/*#__PURE__*/_jsxDEV(Typography, {\n              variant: \"body2\",\n              children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n                children: \"Tipologia:\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1818,\n                columnNumber: 45\n              }, this), \" \", selectedCavo.tipologia || 'N/A']\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 1818,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(Typography, {\n              variant: \"body2\",\n              children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n                children: \"Formazione:\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1819,\n                columnNumber: 45\n              }, this), \" \", selectedCavo.sezione || 'N/A']\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 1819,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(Typography, {\n              variant: \"body2\",\n              children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n                children: \"Metri teorici:\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1820,\n                columnNumber: 45\n              }, this), \" \", selectedCavo.metri_teorici || 'N/A', \" m\"]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 1820,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 1817,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(Grid, {\n            item: true,\n            xs: 12,\n            md: 6,\n            children: [/*#__PURE__*/_jsxDEV(Typography, {\n              variant: \"body2\",\n              children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n                children: \"Ubicazione partenza:\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1823,\n                columnNumber: 45\n              }, this), \" \", selectedCavo.ubicazione_partenza || 'N/A']\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 1823,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(Typography, {\n              variant: \"body2\",\n              children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n                children: \"Ubicazione arrivo:\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1824,\n                columnNumber: 45\n              }, this), \" \", selectedCavo.ubicazione_arrivo || 'N/A']\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 1824,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(Typography, {\n              variant: \"body2\",\n              children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n                children: \"Stato:\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1826,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(Chip, {\n                label: selectedCavo.stato_installazione || 'N/D',\n                size: \"small\",\n                color: getCableStateColor(selectedCavo.stato_installazione),\n                variant: \"outlined\",\n                sx: {\n                  ml: 1\n                }\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1827,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 1825,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 1822,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 1816,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 1812,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(Grid, {\n        container: true,\n        spacing: 3,\n        children: [/*#__PURE__*/_jsxDEV(Grid, {\n          item: true,\n          xs: 12,\n          md: 6,\n          children: [/*#__PURE__*/_jsxDEV(Typography, {\n            variant: \"subtitle2\",\n            gutterBottom: true,\n            sx: {\n              fontWeight: 'bold'\n            },\n            children: \"Metratura posata\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 1842,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(TextField, {\n            size: \"small\",\n            fullWidth: true,\n            label: \"Metri posati\",\n            variant: \"outlined\",\n            name: \"metri_posati\",\n            type: \"number\",\n            value: formData.metri_posati,\n            onChange: handleFormChange,\n            error: !!formErrors.metri_posati,\n            helperText: formErrors.metri_posati || formWarnings.metri_posati,\n            FormHelperTextProps: {\n              sx: {\n                color: formWarnings.metri_posati && !formErrors.metri_posati ? 'warning.main' : 'error.main'\n              }\n            },\n            sx: {\n              mb: 1\n            }\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 1845,\n            columnNumber: 15\n          }, this), formWarnings.metri_posati && !formErrors.metri_posati && /*#__PURE__*/_jsxDEV(Alert, {\n            severity: \"warning\",\n            sx: {\n              mb: 2\n            },\n            children: formWarnings.metri_posati\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 1862,\n            columnNumber: 17\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 1841,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(Grid, {\n          item: true,\n          xs: 12,\n          md: 6,\n          children: [/*#__PURE__*/_jsxDEV(Typography, {\n            variant: \"subtitle2\",\n            gutterBottom: true,\n            sx: {\n              fontWeight: 'bold'\n            },\n            children: \"Associa bobina\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 1870,\n            columnNumber: 15\n          }, this), bobineLoading ? /*#__PURE__*/_jsxDEV(Box, {\n            sx: {\n              display: 'flex',\n              justifyContent: 'center',\n              my: 2\n            },\n            children: /*#__PURE__*/_jsxDEV(CircularProgress, {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1875,\n              columnNumber: 19\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 1874,\n            columnNumber: 17\n          }, this) : /*#__PURE__*/_jsxDEV(Box, {\n            children: [/*#__PURE__*/_jsxDEV(Typography, {\n              variant: \"subtitle2\",\n              gutterBottom: true,\n              sx: {\n                color: 'primary.main',\n                fontWeight: 'bold',\n                mb: 1\n              },\n              children: selectedCavo ? 'Bobine compatibili con il cavo selezionato' : 'Seleziona una bobina'\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1879,\n              columnNumber: 19\n            }, this), /*#__PURE__*/_jsxDEV(FormControl, {\n              fullWidth: true,\n              size: \"small\",\n              error: !!formErrors.id_bobina,\n              children: [/*#__PURE__*/_jsxDEV(InputLabel, {\n                id: \"bobina-select-label\",\n                children: \"Seleziona bobina\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1884,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(Select, {\n                labelId: \"bobina-select-label\",\n                id: \"bobina-select\",\n                name: \"id_bobina\",\n                value: formData.id_bobina,\n                label: \"Seleziona bobina\",\n                onChange: handleFormChange,\n                children: [/*#__PURE__*/_jsxDEV(MenuItem, {\n                  value: \"BOBINA_VUOTA\",\n                  children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n                    children: \"BOBINA VUOTA\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1894,\n                    columnNumber: 25\n                  }, this), \" (nessuna bobina associata)\"]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1893,\n                  columnNumber: 23\n                }, this), /*#__PURE__*/_jsxDEV(Divider, {}, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1896,\n                  columnNumber: 23\n                }, this), bobine.length > 0 ? /*#__PURE__*/_jsxDEV(Box, {\n                  component: \"li\",\n                  sx: {\n                    p: 1,\n                    bgcolor: 'background.paper'\n                  },\n                  children: /*#__PURE__*/_jsxDEV(Typography, {\n                    variant: \"caption\",\n                    sx: {\n                      fontWeight: 'bold',\n                      color: 'success.main'\n                    },\n                    children: [bobine.length, \" bobine disponibili\", selectedCavo && /*#__PURE__*/_jsxDEV(_Fragment, {\n                      children: [\" -\", /*#__PURE__*/_jsxDEV(\"span\", {\n                        style: {\n                          color: bobine.some(bobina => bobina.tipologia === selectedCavo.tipologia && String(bobina.sezione) === String(selectedCavo.sezione)) ? 'green' : 'orange'\n                        },\n                        children: [bobine.filter(bobina => bobina.tipologia === selectedCavo.tipologia && String(bobina.sezione) === String(selectedCavo.sezione)).length, \" compatibili\"]\n                      }, void 0, true, {\n                        fileName: _jsxFileName,\n                        lineNumber: 1903,\n                        columnNumber: 33\n                      }, this)]\n                    }, void 0, true)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1899,\n                    columnNumber: 27\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1898,\n                  columnNumber: 25\n                }, this) : null, bobine.map(bobina => /*#__PURE__*/_jsxDEV(MenuItem, {\n                  value: bobina.id_bobina,\n                  disabled: bobina.metri_residui < parseFloat(formData.metri_posati || 0),\n                  sx: {\n                    '&.Mui-selected': {\n                      bgcolor: 'success.light'\n                    },\n                    '&.Mui-selected:hover': {\n                      bgcolor: 'success.light'\n                    },\n                    bgcolor: selectedCavo && bobina.tipologia === selectedCavo.tipologia && String(bobina.sezione) === String(selectedCavo.sezione) ? 'rgba(76, 175, 80, 0.12)' : 'inherit',\n                    border: selectedCavo && bobina.tipologia === selectedCavo.tipologia && String(bobina.sezione) === String(selectedCavo.sezione) ? '1px solid #4caf50' : 'none',\n                    borderRadius: '4px',\n                    margin: '2px 0'\n                  },\n                  children: /*#__PURE__*/_jsxDEV(Box, {\n                    sx: {\n                      display: 'flex',\n                      flexDirection: 'column',\n                      width: '100%'\n                    },\n                    children: [/*#__PURE__*/_jsxDEV(Box, {\n                      sx: {\n                        display: 'flex',\n                        justifyContent: 'space-between',\n                        alignItems: 'center',\n                        width: '100%'\n                      },\n                      children: [/*#__PURE__*/_jsxDEV(Typography, {\n                        variant: \"body2\",\n                        sx: {\n                          fontWeight: 'bold'\n                        },\n                        children: [getBobinaNumber(bobina.id_bobina), \" - \", bobina.tipologia || 'N/A']\n                      }, void 0, true, {\n                        fileName: _jsxFileName,\n                        lineNumber: 1938,\n                        columnNumber: 31\n                      }, this), selectedCavo && bobina.tipologia === selectedCavo.tipologia && String(bobina.sezione) === String(selectedCavo.sezione) && /*#__PURE__*/_jsxDEV(Chip, {\n                        size: \"small\",\n                        label: \"Compatibile\",\n                        color: \"success\",\n                        variant: \"outlined\",\n                        sx: {\n                          height: 20,\n                          fontSize: '0.6rem'\n                        }\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 1944,\n                        columnNumber: 33\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1937,\n                      columnNumber: 29\n                    }, this), /*#__PURE__*/_jsxDEV(Box, {\n                      sx: {\n                        display: 'flex',\n                        justifyContent: 'space-between',\n                        width: '100%'\n                      },\n                      children: [/*#__PURE__*/_jsxDEV(Typography, {\n                        variant: \"caption\",\n                        children: bobina.sezione || 'N/A'\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 1954,\n                        columnNumber: 31\n                      }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                        variant: \"caption\",\n                        sx: {\n                          fontWeight: 'bold',\n                          color: bobina.metri_residui < parseFloat(formData.metri_posati || 0) ? 'error.main' : 'success.main'\n                        },\n                        children: [bobina.metri_residui || 0, \" m disponibili\"]\n                      }, void 0, true, {\n                        fileName: _jsxFileName,\n                        lineNumber: 1957,\n                        columnNumber: 31\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1953,\n                      columnNumber: 29\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1936,\n                    columnNumber: 27\n                  }, this)\n                }, bobina.id_bobina, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1917,\n                  columnNumber: 25\n                }, this))]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 1885,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(FormHelperText, {\n                children: formErrors.id_bobina || 'È necessario selezionare una bobina o \"BOBINA VUOTA\"'\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1965,\n                columnNumber: 21\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 1883,\n              columnNumber: 19\n            }, this), bobine.length === 0 && !bobineLoading && /*#__PURE__*/_jsxDEV(Alert, {\n              severity: \"warning\",\n              sx: {\n                mt: 2,\n                fontSize: '0.8rem'\n              },\n              children: \"Nessuna bobina disponibile trovata. Puoi usare BOBINA VUOTA.\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1971,\n              columnNumber: 21\n            }, this), bobine.length > 0 && selectedCavo && !bobine.some(bobina =>\n            // Nota: n_conduttori non è più utilizzato per la compatibilità\n            bobina.tipologia === selectedCavo.tipologia && String(bobina.sezione) === String(selectedCavo.sezione)) && /*#__PURE__*/_jsxDEV(Alert, {\n              severity: \"info\",\n              sx: {\n                mt: 2,\n                fontSize: '0.8rem'\n              },\n              children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n                children: \"Nessuna bobina compatibile trovata.\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1982,\n                columnNumber: 23\n              }, this), \" Hai due opzioni:\", /*#__PURE__*/_jsxDEV(\"ul\", {\n                style: {\n                  margin: '5px 0',\n                  paddingLeft: '20px'\n                },\n                children: [/*#__PURE__*/_jsxDEV(\"li\", {\n                  children: \"Usa \\\"BOBINA VUOTA\\\" (potrai associare una bobina in seguito)\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1984,\n                  columnNumber: 25\n                }, this), /*#__PURE__*/_jsxDEV(\"li\", {\n                  children: \"Seleziona una bobina non compatibile (ti verr\\xE0 chiesto se vuoi aggiornare le caratteristiche del cavo)\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1985,\n                  columnNumber: 25\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 1983,\n                columnNumber: 23\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 1981,\n              columnNumber: 21\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 1878,\n            columnNumber: 17\n          }, this), !bobineLoading && formData.id_bobina && formData.id_bobina !== 'BOBINA_VUOTA' && (() => {\n            const bobina = bobine.find(b => b.id_bobina === formData.id_bobina);\n            if (bobina) {\n              return /*#__PURE__*/_jsxDEV(Box, {\n                sx: {\n                  mt: 2,\n                  p: 2,\n                  bgcolor: '#f5f5f5',\n                  borderRadius: 1\n                },\n                children: [/*#__PURE__*/_jsxDEV(Typography, {\n                  variant: \"body2\",\n                  children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n                    children: \"Bobina:\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1998,\n                    columnNumber: 51\n                  }, this), \" \", getBobinaNumber(bobina.id_bobina)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1998,\n                  columnNumber: 23\n                }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                  variant: \"body2\",\n                  children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n                    children: \"Metri residui:\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1999,\n                    columnNumber: 51\n                  }, this), \" \", bobina.metri_residui || 0, \" m\"]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1999,\n                  columnNumber: 23\n                }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                  variant: \"body2\",\n                  children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n                    children: \"Stato:\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 2001,\n                    columnNumber: 25\n                  }, this), /*#__PURE__*/_jsxDEV(Chip, {\n                    label: bobina.stato_bobina || 'N/D',\n                    size: \"small\",\n                    color: getReelStateColor(bobina.stato_bobina),\n                    variant: \"outlined\",\n                    sx: {\n                      ml: 1\n                    }\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 2002,\n                    columnNumber: 25\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 2000,\n                  columnNumber: 23\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 1997,\n                columnNumber: 21\n              }, this);\n            }\n            return null;\n          })()]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 1869,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 1839,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(Box, {\n        sx: {\n          mt: 2,\n          display: 'flex',\n          justifyContent: 'flex-end',\n          gap: 1\n        },\n        children: [/*#__PURE__*/_jsxDEV(Button, {\n          variant: \"outlined\",\n          color: \"secondary\",\n          onClick: () => {\n            setSelectedCavo(null);\n            setFormData({\n              id_cavo: '',\n              metri_posati: '',\n              id_bobina: ''\n            });\n          },\n          startIcon: /*#__PURE__*/_jsxDEV(CancelIcon, {\n            fontSize: \"small\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 2030,\n            columnNumber: 26\n          }, this),\n          disabled: loading,\n          size: \"small\",\n          sx: {\n            minWidth: '80px',\n            height: '32px'\n          },\n          children: \"ANNULLA\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 2019,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(Button, {\n          variant: \"contained\",\n          color: \"primary\",\n          onClick: handleSubmit,\n          endIcon: loading ? undefined : /*#__PURE__*/_jsxDEV(SaveIcon, {\n            fontSize: \"small\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 2042,\n            columnNumber: 46\n          }, this),\n          disabled: loading || !formData.metri_posati || !formData.id_bobina,\n          size: \"small\",\n          sx: {\n            minWidth: '80px',\n            height: '32px'\n          },\n          children: loading ? /*#__PURE__*/_jsxDEV(CircularProgress, {\n            size: 16\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 2047,\n            columnNumber: 26\n          }, this) : 'SALVA'\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 2038,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 2018,\n        columnNumber: 11\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 1806,\n      columnNumber: 9\n    }, this), /*#__PURE__*/_jsxDEV(Dialog, {\n      open: showConfirmDialog,\n      onClose: () => setShowConfirmDialog(false),\n      maxWidth: \"sm\",\n      fullWidth: true,\n      children: [/*#__PURE__*/_jsxDEV(DialogTitle, {\n        sx: {\n          bgcolor: 'warning.light'\n        },\n        children: /*#__PURE__*/_jsxDEV(Box, {\n          sx: {\n            display: 'flex',\n            alignItems: 'center',\n            gap: 1\n          },\n          children: [/*#__PURE__*/_jsxDEV(WarningIcon, {\n            color: \"warning\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 2057,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Typography, {\n            variant: \"h6\",\n            children: confirmDialogProps.title\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 2058,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 2056,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 2055,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(DialogContent, {\n        children: /*#__PURE__*/_jsxDEV(Typography, {\n          variant: \"body1\",\n          sx: {\n            mt: 2\n          },\n          children: confirmDialogProps.message\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 2062,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 2061,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(DialogActions, {\n        children: [/*#__PURE__*/_jsxDEV(Button, {\n          onClick: () => setShowConfirmDialog(false),\n          color: \"secondary\",\n          variant: \"outlined\",\n          children: \"Annulla\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 2067,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Button, {\n          onClick: () => {\n            setShowConfirmDialog(false);\n            confirmDialogProps.onConfirm();\n          },\n          color: \"primary\",\n          variant: \"contained\",\n          autoFocus: true,\n          children: \"Conferma\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 2070,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 2066,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 2054,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Dialog, {\n      open: showAlreadyLaidDialog,\n      onClose: handleCloseAlreadyLaidDialog,\n      maxWidth: \"sm\",\n      fullWidth: true,\n      children: [/*#__PURE__*/_jsxDEV(DialogTitle, {\n        sx: {\n          bgcolor: 'warning.light'\n        },\n        children: /*#__PURE__*/_jsxDEV(Box, {\n          sx: {\n            display: 'flex',\n            alignItems: 'center',\n            gap: 1\n          },\n          children: [/*#__PURE__*/_jsxDEV(WarningIcon, {\n            color: \"warning\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 2088,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Typography, {\n            variant: \"h6\",\n            children: \"Cavo gi\\xE0 posato\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 2089,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 2087,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 2086,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(DialogContent, {\n        children: alreadyLaidCavo && /*#__PURE__*/_jsxDEV(Box, {\n          sx: {\n            mt: 2\n          },\n          children: [/*#__PURE__*/_jsxDEV(Typography, {\n            variant: \"body1\",\n            paragraph: true,\n            children: [\"Il cavo \", /*#__PURE__*/_jsxDEV(\"strong\", {\n              children: alreadyLaidCavo.id_cavo\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 2096,\n              columnNumber: 25\n            }, this), \" risulta gi\\xE0 posato (\", alreadyLaidCavo.metratura_reale || 0, \"m).\"]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 2095,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(Typography, {\n            variant: \"body1\",\n            paragraph: true,\n            children: \"Puoi scegliere di:\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 2098,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(Typography, {\n            variant: \"body2\",\n            component: \"ul\",\n            children: [/*#__PURE__*/_jsxDEV(\"li\", {\n              children: \"Modificare la bobina associata al cavo\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 2102,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"li\", {\n              children: \"Selezionare un altro cavo\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 2103,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"li\", {\n              children: \"Annullare l'operazione\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 2104,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 2101,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 2094,\n          columnNumber: 13\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 2092,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(DialogActions, {\n        sx: {\n          p: 2,\n          justifyContent: 'space-between'\n        },\n        children: [/*#__PURE__*/_jsxDEV(Button, {\n          onClick: handleCloseAlreadyLaidDialog,\n          color: \"secondary\",\n          children: \"Annulla operazione\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 2110,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Box, {\n          children: [/*#__PURE__*/_jsxDEV(Button, {\n            onClick: handleSelectAnotherCable,\n            color: \"primary\",\n            sx: {\n              mr: 1\n            },\n            children: \"Seleziona altro cavo\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 2114,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Button, {\n            onClick: handleModifyReel,\n            variant: \"contained\",\n            color: \"primary\",\n            children: \"Modifica bobina\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 2117,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 2113,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 2109,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 2085,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(IncompatibleReelDialog, {\n      open: showIncompatibleReelDialog,\n      onClose: handleCloseIncompatibleReelDialog,\n      cavo: incompatibleReelData.cavo,\n      bobina: incompatibleReelData.bobina,\n      onUpdateCavo: handleUpdateCavoToMatchReel,\n      onSelectAnotherReel: handleSelectAnotherReel\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 2125,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Dialog, {\n      open: showCavoDetailsDialog,\n      onClose: () => setShowCavoDetailsDialog(false),\n      maxWidth: \"md\",\n      fullWidth: true,\n      children: [/*#__PURE__*/_jsxDEV(DialogTitle, {\n        children: /*#__PURE__*/_jsxDEV(Box, {\n          sx: {\n            display: 'flex',\n            alignItems: 'center',\n            gap: 1\n          },\n          children: [/*#__PURE__*/_jsxDEV(InfoIcon, {\n            color: \"primary\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 2143,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Typography, {\n            variant: \"h6\",\n            children: \"Dettagli Cavo\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 2144,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 2142,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 2141,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(DialogContent, {\n        children: /*#__PURE__*/_jsxDEV(CavoDetailsView, {\n          cavo: selectedCavo\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 2148,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 2147,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(DialogActions, {\n        children: /*#__PURE__*/_jsxDEV(Button, {\n          onClick: () => setShowCavoDetailsDialog(false),\n          color: \"primary\",\n          children: \"Chiudi\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 2151,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 2150,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 2135,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 1718,\n    columnNumber: 5\n  }, this);\n};\n_s(InserisciMetriForm, \"5xQmDMZ4lzfRbP71tmjFjXDgm+0=\", false, function () {\n  return [useNavigate];\n});\n_c = InserisciMetriForm;\nexport default InserisciMetriForm;\nvar _c;\n$RefreshReg$(_c, \"InserisciMetriForm\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "axios", "Box", "Paper", "Typography", "TextField", "<PERSON><PERSON>", "Grid", "FormControl", "InputLabel", "Select", "MenuItem", "Divider", "<PERSON><PERSON>", "CircularProgress", "FormHelperText", "IconButton", "Chip", "Dialog", "DialogTitle", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "DialogActions", "Table", "TableBody", "TableCell", "TableContainer", "TableHead", "TableRow", "List", "ListItem", "ListItemText", "ListItemSecondaryAction", "Search", "SearchIcon", "Save", "SaveIcon", "Cancel", "CancelIcon", "Warning", "WarningIcon", "Info", "InfoIcon", "useNavigate", "caviService", "axiosInstance", "IncompatibleReelDialog", "CavoDetailsView", "CABLE_STATES", "REEL_STATES", "determineCableState", "determineReelState", "canModifyCable", "isCableSpare", "isCableInstalled", "getCableStateColor", "getReelStateColor", "parcoCaviService", "redirectToVisualizzaCavi", "jsxDEV", "_jsxDEV", "Fragment", "_Fragment", "InserisciMetriForm", "cantiereId", "onSuccess", "onError", "_s", "navigate", "loading", "setLoading", "caviLoading", "setCaviLoading", "bob<PERSON><PERSON><PERSON><PERSON>", "setBobineLoading", "searchResults", "setSearchResults", "showSearchResults", "setShowSearchResults", "cavi", "<PERSON><PERSON><PERSON>", "bobine", "set<PERSON>ob<PERSON>", "selected<PERSON><PERSON><PERSON>", "setSelectedCavo", "cavoIdInput", "setCavoIdInput", "activeStep", "setActiveStep", "formData", "setFormData", "id_cavo", "metri_posati", "id_bobina", "formErrors", "setFormErrors", "formWarnings", "setForm<PERSON><PERSON>nings", "showIncompatibleReelDialog", "setShowIncompatibleReelDialog", "incompatibleReel", "setIncompatibleReel", "incompatibleReelData", "setIncompatibleReelData", "cavo", "bobina", "showAlreadyLaidDialog", "setShowAlreadyLaidDialog", "alreadyLaidCavo", "setAlreadyLaidCavo", "showCavoDetailsDialog", "setShowCavoDetailsDialog", "loadBobine", "getBobinaNumber", "idBobina", "includes", "split", "loadCavi", "console", "log", "caviData", "get<PERSON><PERSON>", "length", "loadError", "error", "isNetworkError", "response", "code", "message", "Promise", "resolve", "setTimeout", "token", "localStorage", "getItem", "API_URL", "defaults", "baseURL", "retryResponse", "get", "headers", "timeout", "data", "retryError", "errorMessage", "detail", "bobine<PERSON><PERSON>", "getBobine", "b<PERSON><PERSON><PERSON><PERSON><PERSON>", "filter", "stato_bobina", "metri_residui", "tipologia", "sezione", "cavoTipologia", "String", "trim", "toLowerCase", "cavoSezione", "bobineCom<PERSON><PERSON><PERSON><PERSON>", "bobineNonCompatibili", "for<PERSON>ach", "bobinaTipologia", "bobinaSezione", "tipologiaMatch", "sezioneMatch", "isCompatible", "push", "sort", "a", "b", "bobineOrdinate", "handleSearchCavoById", "filteredCavi", "exactMatch", "find", "stato_installazione", "metratura_reale", "modificato_manualmente", "handleCavoSelect", "status", "window", "confirm", "reactivateSpare", "then", "updatedCavo", "catch", "n_conduttori", "cavoId", "handleFormChange", "e", "name", "value", "target", "cavoFormazione", "bobinaFormazione", "validateField", "warning", "isNaN", "parseFloat", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "metri_te<PERSON>ci", "prev", "notificationShown", "setNotificationShown", "showConfirmDialog", "setShowConfirmDialog", "confirmDialogProps", "setConfirmDialogProps", "title", "onConfirm", "validateForm", "<PERSON><PERSON><PERSON><PERSON>", "errors", "warnings", "handleNext", "prevActiveStep", "handleBack", "handleReset", "determineInstallationStatus", "metriTeorici", "handleSubmit", "statoInstallazione", "forceOver", "confirmMessage", "updateMetri<PERSON><PERSON><PERSON>", "successMessage", "success", "_error$response$data", "request", "_error$response$data2", "renderStep1", "children", "variant", "sx", "mb", "fontWeight", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "p", "width", "display", "alignItems", "mr", "min<PERSON><PERSON><PERSON>", "size", "label", "onChange", "placeholder", "flexGrow", "color", "onClick", "disabled", "startIcon", "fontSize", "height", "justifyContent", "my", "severity", "py", "component", "maxHeight", "overflow", "<PERSON><PERSON><PERSON><PERSON>", "bgcolor", "align", "map", "hover", "cursor", "px", "stopPropagation", "renderStep2", "flexWrap", "style", "gap", "ml", "gutterBottom", "borderRadius", "flex", "max<PERSON><PERSON><PERSON>", "mt", "type", "helperText", "FormHelperTextProps", "inputProps", "max", "step", "renderStep3", "buildFullBobinaId", "numeroBobina", "hasSufficientMeters", "handleBobinaNumberInput", "id_bobina_input", "idBobinaCompleto", "<PERSON><PERSON><PERSON>", "formazione", "onBlur", "fullWidth", "id", "labelId", "flexDirection", "border", "container", "spacing", "item", "xs", "md", "metri_totali", "renderStep4", "bobinaInfo", "compact", "getStepContent", "handleCloseAlreadyLaidDialog", "handleModifyReel", "handleSelectAnotherCable", "handleCloseIncompatibleReelDialog", "handleUpdateCavoToMatchReel", "updateCavoForCompatibility", "getCavoById", "handleSelectAnotherReel", "ubicazione_partenza", "ubicazione_arrivo", "some", "margin", "paddingLeft", "endIcon", "undefined", "open", "onClose", "autoFocus", "paragraph", "onUpdateCavo", "onSelectAnotherReel", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/PycharmProjects/CMS/webapp/frontend/src/components/cavi/InserisciMetriForm.js"], "sourcesContent": ["import React, { useState, useEffect } from 'react';\nimport axios from 'axios';\nimport {\n  Box,\n  Paper,\n  Typography,\n  TextField,\n  Button,\n  Grid,\n  FormControl,\n  InputLabel,\n  Select,\n  MenuItem,\n  Divider,\n  Alert,\n  CircularProgress,\n  FormHelperText,\n  IconButton,\n  Chip,\n  Dialog,\n  DialogTitle,\n  DialogContent,\n  DialogActions,\n  Table,\n  TableBody,\n  TableCell,\n  TableContainer,\n  TableHead,\n  TableRow,\n  List,\n  ListItem,\n  ListItemText,\n  ListItemSecondaryAction\n} from '@mui/material';\nimport {\n  Search as SearchIcon,\n  Save as SaveIcon,\n  Cancel as CancelIcon,\n  Warning as WarningIcon,\n  Info as InfoIcon\n} from '@mui/icons-material';\nimport { useNavigate } from 'react-router-dom';\nimport caviService from '../../services/caviService';\nimport axiosInstance from '../../services/axiosConfig';\nimport IncompatibleReelDialog from './IncompatibleReelDialog';\nimport CavoDetailsView from './CavoDetailsView';\nimport {\n  CABLE_STATES,\n  REEL_STATES,\n  determineCableState,\n  determineReelState,\n  canModifyCable,\n  isCableSpare,\n  isCableInstalled,\n  getCableStateColor,\n  getReelStateColor\n} from '../../utils/stateUtils';\nimport parcoCaviService from '../../services/parcoCaviService';\nimport { redirectToVisualizzaCavi } from '../../utils/navigationUtils';\n\n/**\n * Componente per l'inserimento dei metri posati di un cavo\n * Versione semplificata con workflow compresso in un'unica pagina\n *\n * @param {Object} props - Proprietà del componente\n * @param {string} props.cantiereId - ID del cantiere\n * @param {Function} props.onSuccess - Funzione chiamata al successo dell'operazione\n * @param {Function} props.onError - Funzione chiamata in caso di errore\n */\nconst InserisciMetriForm = ({ cantiereId, onSuccess, onError }) => {\n  const navigate = useNavigate();\n\n  // Stati per la gestione del form\n  const [loading, setLoading] = useState(false);\n  const [caviLoading, setCaviLoading] = useState(false);\n  const [bobineLoading, setBobineLoading] = useState(false);\n  const [searchResults, setSearchResults] = useState([]);\n  const [showSearchResults, setShowSearchResults] = useState(false);\n\n  // Stati per i dati\n  const [cavi, setCavi] = useState([]);\n  const [bobine, setBobine] = useState([]);\n  const [selectedCavo, setSelectedCavo] = useState(null);\n  const [cavoIdInput, setCavoIdInput] = useState('');\n\n  // Stato per la gestione dei passi (mantenuto per compatibilità con funzioni esistenti)\n  const [activeStep, setActiveStep] = useState(0);\n\n  // Stati per il form\n  const [formData, setFormData] = useState({\n    id_cavo: '',\n    metri_posati: '',\n    id_bobina: ''\n  });\n\n  // Stati per la validazione\n  const [formErrors, setFormErrors] = useState({});\n  const [formWarnings, setFormWarnings] = useState({});\n\n  // Stati per i dialoghi speciali\n  const [showIncompatibleReelDialog, setShowIncompatibleReelDialog] = useState(false);\n  const [incompatibleReel, setIncompatibleReel] = useState(null); // Mantenuto per compatibilità\n  const [incompatibleReelData, setIncompatibleReelData] = useState({ cavo: null, bobina: null });\n  const [showAlreadyLaidDialog, setShowAlreadyLaidDialog] = useState(false);\n  const [alreadyLaidCavo, setAlreadyLaidCavo] = useState(null);\n  const [showCavoDetailsDialog, setShowCavoDetailsDialog] = useState(false);\n\n  // Carica la lista delle bobine all'avvio\n  useEffect(() => {\n    loadBobine();\n  }, [cantiereId]);\n\n  // Funzione per estrarre il numero della bobina dall'ID completo\n  const getBobinaNumber = (idBobina) => {\n    // L'ID completo è nel formato C{id_cantiere}_B{numero_bobina}\n    if (idBobina && idBobina.includes('_B')) {\n      return idBobina.split('_B')[1];\n    }\n    return idBobina;\n  };\n\n  // Funzione per caricare i cavi\n  const loadCavi = async () => {\n    try {\n      setCaviLoading(true);\n      console.log(`Caricamento cavi per il cantiere ${cantiereId}...`);\n\n      // Carica tutti i cavi, inclusi quelli SPARE e installati\n      try {\n        const caviData = await caviService.getCavi(cantiereId);\n        console.log(`Caricati ${caviData.length} cavi`);\n\n        // Mostra tutti i cavi (da installare, in corso, installati e SPARE)\n        // Questo permette di vedere anche i cavi già installati per eventuale modifica della bobina\n        setCavi(caviData);\n      } catch (loadError) {\n        console.error('Errore nel primo tentativo di caricamento dei cavi:', loadError);\n\n        // Se è un errore di rete, prova un secondo tentativo dopo una breve pausa\n        if (loadError.isNetworkError || !loadError.response ||\n            loadError.code === 'ECONNABORTED' ||\n            (loadError.message && loadError.message.includes('Network Error'))) {\n\n          console.log('Errore di rete, tentativo di recupero dopo 1 secondo...');\n          await new Promise(resolve => setTimeout(resolve, 1000));\n\n          try {\n            // Secondo tentativo con timeout aumentato\n            const token = localStorage.getItem('token');\n            const API_URL = axiosInstance.defaults.baseURL;\n\n            // Usa l'ID cantiere originale per la richiesta alternativa\n            const retryResponse = await axios.get(\n              `${API_URL}/cavi/${cantiereId}`,\n              {\n                headers: {\n                  'Authorization': `Bearer ${token}`\n                },\n                timeout: 30000 // 30 secondi\n              }\n            );\n\n            console.log(`Recupero riuscito al secondo tentativo: ${retryResponse.data.length} cavi`);\n            setCavi(retryResponse.data);\n          } catch (retryError) {\n            console.error('Anche il secondo tentativo è fallito:', retryError);\n            throw retryError;\n          }\n        } else {\n          throw loadError;\n        }\n      }\n    } catch (error) {\n      console.error('Errore nel caricamento dei cavi:', error);\n\n      // Messaggio di errore più dettagliato\n      let errorMessage = 'Errore nel caricamento dei cavi';\n\n      if (error.isNetworkError) {\n        errorMessage = 'Errore di connessione al server. Verifica la rete e riprova.';\n      } else if (error.detail) {\n        errorMessage = error.detail;\n      } else if (error.message) {\n        errorMessage = error.message;\n      }\n\n      onError(errorMessage);\n    } finally {\n      setCaviLoading(false);\n    }\n  };\n\n  // Funzione per caricare le bobine\n  const loadBobine = async () => {\n    try {\n      setBobineLoading(true);\n      console.log('Caricamento bobine iniziato...');\n\n      // Carica tutte le bobine disponibili\n      const bobineData = await parcoCaviService.getBobine(cantiereId);\n      console.log(`Bobine caricate: ${bobineData.length}`);\n\n      // Filtra solo per stato (disponibile o in uso) e metri residui > 0\n      const bobineUtilizzabili = bobineData.filter(bobina =>\n        bobina.stato_bobina !== 'Terminata' &&\n        bobina.stato_bobina !== 'Over' &&\n        bobina.metri_residui > 0\n      );\n\n      console.log(`Bobine utilizzabili: ${bobineUtilizzabili.length}`);\n\n      // Se c'è un cavo selezionato, evidenzia le bobine compatibili ma mostra tutte\n      if (selectedCavo && selectedCavo.tipologia && selectedCavo.sezione) {\n        console.log('Cavo selezionato, evidenziando bobine compatibili...');\n        console.log('Dati cavo:', {\n          id_cavo: selectedCavo.id_cavo,\n          tipologia: selectedCavo.tipologia,\n          sezione: selectedCavo.sezione\n        });\n\n        // Ordina le bobine: prima le compatibili, poi le altre, tutte ordinate per metri residui\n        const cavoTipologia = String(selectedCavo.tipologia || '').trim().toLowerCase();\n        const cavoSezione = String(selectedCavo.sezione || '0').trim();\n\n        // Identifica le bobine compatibili\n        const bobineCompatibili = [];\n        const bobineNonCompatibili = [];\n\n        // Dividi le bobine in compatibili e non compatibili\n        bobineUtilizzabili.forEach(bobina => {\n          const bobinaTipologia = String(bobina.tipologia || '').trim().toLowerCase();\n          const bobinaSezione = String(bobina.sezione || '0').trim();\n\n          // Verifica compatibilità\n          const tipologiaMatch = bobinaTipologia === cavoTipologia;\n          const sezioneMatch = bobinaSezione === cavoSezione;\n          const isCompatible = tipologiaMatch && sezioneMatch;\n\n          console.log(`Verifica compatibilità bobina ${bobina.id_bobina}:`, {\n            'Tipologia bobina': `\"${bobina.tipologia}\"`,\n            'Tipologia cavo': `\"${selectedCavo.tipologia}\"`,\n            'Tipologie uguali?': tipologiaMatch,\n            'Sezione bobina': `\"${String(bobina.sezione)}\"`,\n            'Sezione cavo': `\"${String(selectedCavo.sezione)}\"`,\n            'Sezioni uguali?': sezioneMatch,\n            'Stato bobina': bobina.stato_bobina,\n            'Metri residui': bobina.metri_residui,\n            'Compatibile?': isCompatible\n          });\n\n          if (isCompatible) {\n            bobineCompatibili.push(bobina);\n          } else {\n            bobineNonCompatibili.push(bobina);\n          }\n        });\n\n        console.log(`Bobine compatibili trovate: ${bobineCompatibili.length}`);\n        console.log(`Bobine non compatibili: ${bobineNonCompatibili.length}`);\n\n        // Log dettagliato delle bobine compatibili\n        if (bobineCompatibili.length > 0) {\n          console.log('Dettaglio bobine compatibili:');\n          bobineCompatibili.forEach(bobina => {\n            console.log(`- ${bobina.id_bobina}: ${bobina.tipologia} / ${bobina.sezione} (${bobina.metri_residui}m)`);\n          });\n        } else {\n          console.log('ATTENZIONE: Nessuna bobina compatibile trovata!');\n        }\n\n        // Ordina entrambi gli array per metri residui (decrescente)\n        bobineCompatibili.sort((a, b) => b.metri_residui - a.metri_residui);\n        bobineNonCompatibili.sort((a, b) => b.metri_residui - a.metri_residui);\n\n        // Concatena gli array: prima le compatibili, poi le non compatibili\n        const bobineOrdinate = [...bobineCompatibili, ...bobineNonCompatibili];\n\n        // Imposta le bobine nel componente\n        setBobine(bobineOrdinate);\n      } else {\n        // Se non c'è un cavo selezionato, mostra tutte le bobine ordinate per metri residui\n        console.log('Nessun cavo selezionato, mostrando tutte le bobine...');\n        bobineUtilizzabili.sort((a, b) => b.metri_residui - a.metri_residui);\n        setBobine(bobineUtilizzabili);\n      }\n    } catch (error) {\n      console.error('Errore nel caricamento delle bobine:', error);\n      onError('Errore nel caricamento delle bobine: ' + (error.message || 'Errore sconosciuto'));\n    } finally {\n      setBobineLoading(false);\n    }\n  };\n\n  // Gestisce la ricerca di un cavo per ID o pattern\n  const handleSearchCavoById = async () => {\n    if (!cavoIdInput.trim()) {\n      onError('Inserisci un ID cavo valido');\n      return;\n    }\n\n    try {\n      setCaviLoading(true);\n      console.log(`Ricerca cavo con pattern: ${cavoIdInput.trim()} nel cantiere ${cantiereId}`);\n\n      // Cerca tutti i cavi che corrispondono al pattern\n      const caviData = await caviService.getCavi(cantiereId);\n\n      // Filtra i cavi in base all'input (ricerca parziale)\n      const filteredCavi = caviData.filter(cavo =>\n        cavo.id_cavo.toLowerCase().includes(cavoIdInput.trim().toLowerCase())\n      );\n\n      console.log(`Trovati ${filteredCavi.length} cavi corrispondenti al pattern`);\n\n      // Se c'è una corrispondenza esatta, seleziona direttamente quel cavo\n      const exactMatch = filteredCavi.find(cavo =>\n        cavo.id_cavo.toLowerCase() === cavoIdInput.trim().toLowerCase()\n      );\n\n      if (exactMatch) {\n        console.log('Trovata corrispondenza esatta:', exactMatch);\n\n        // Verifica se il cavo è già installato\n        if (exactMatch.stato_installazione === 'Installato' || (exactMatch.metratura_reale && exactMatch.metratura_reale > 0)) {\n          console.log('Cavo già installato, mostra dialogo:', exactMatch);\n          setAlreadyLaidCavo(exactMatch);\n          setShowAlreadyLaidDialog(true);\n          setCaviLoading(false);\n          return;\n        }\n\n        // Verifica se il cavo è SPARE\n        if (exactMatch.modificato_manualmente === 3) {\n          console.log('Cavo SPARE trovato:', exactMatch);\n          // Gestione del cavo SPARE - verrà gestito in handleCavoSelect\n        }\n\n        // Seleziona il cavo direttamente\n        handleCavoSelect(exactMatch);\n      } else if (filteredCavi.length > 0) {\n        // Mostra i risultati della ricerca in una tabella\n        setSearchResults(filteredCavi);\n        setShowSearchResults(true);\n      } else {\n        // Nessun cavo trovato\n        onError(`Nessun cavo trovato con pattern \"${cavoIdInput.trim()}\" nel cantiere ${cantiereId}`);\n      }\n    } catch (error) {\n      console.error('Errore nella ricerca del cavo:', error);\n\n      // Messaggio di errore più dettagliato\n      let errorMessage = 'Errore nella ricerca dei cavi';\n\n      if (error.isNetworkError) {\n        errorMessage = 'Errore di connessione al server. Verifica la rete e riprova.';\n      } else if (error.status === 404) {\n        errorMessage = `Cavo con ID \"${cavoIdInput.trim()}\" non trovato nel cantiere ${cantiereId}`;\n      } else if (error.detail) {\n        errorMessage = error.detail;\n      } else if (error.message) {\n        errorMessage = error.message;\n      }\n\n      onError(errorMessage);\n    } finally {\n      setCaviLoading(false);\n    }\n  };\n\n  // Gestisce la selezione di un cavo\n  const handleCavoSelect = (cavo) => {\n    // Verifica se il cavo è già installato\n    if (cavo.stato_installazione === 'Installato' || (cavo.metratura_reale && cavo.metratura_reale > 0)) {\n      // Mostra il dialogo per cavi già posati\n      setAlreadyLaidCavo(cavo);\n      setShowAlreadyLaidDialog(true);\n      return;\n    }\n    // Verifica se il cavo è SPARE (modificato_manualmente = 3)\n    else if (cavo.modificato_manualmente === 3) {\n      // Chiedi conferma all'utente per riattivare il cavo SPARE\n      if (window.confirm(`Il cavo ${cavo.id_cavo} è marcato come SPARE. Vuoi riattivarlo?`)) {\n        // Riattiva il cavo SPARE (imposta modificato_manualmente = 0)\n        reactivateSpare(cavo.id_cavo).then(() => {\n          // Aggiorna il cavo selezionato con modificato_manualmente = 0\n          const updatedCavo = { ...cavo, modificato_manualmente: 0 };\n          setSelectedCavo(updatedCavo);\n          setFormData({\n            ...formData,\n            id_cavo: updatedCavo.id_cavo,\n            metri_posati: ''\n          });\n          // Nascondi i risultati della ricerca\n          setShowSearchResults(false);\n\n          // Carica le bobine compatibili\n          loadBobine();\n        }).catch(error => {\n          console.error('Errore durante la riattivazione del cavo SPARE:', error);\n          onError('Errore durante la riattivazione del cavo SPARE: ' + (error.message || 'Errore sconosciuto'));\n        });\n      } else {\n        // L'utente ha annullato la riattivazione\n        return;\n      }\n    } else {\n      // Cavo normale (non SPARE e non installato)\n      setSelectedCavo(cavo);\n      setFormData({\n        ...formData,\n        id_cavo: cavo.id_cavo,\n        metri_posati: ''\n      });\n      // Nascondi i risultati della ricerca\n      setShowSearchResults(false);\n\n      // Carica le bobine compatibili\n      if (cavo.tipologia && cavo.n_conduttori && cavo.sezione) {\n        console.log(`Caricamento bobine compatibili per il cavo ${cavo.id_cavo}...`);\n        loadBobine();\n      }\n    }\n  };\n\n  // Funzione per riattivare un cavo SPARE\n  const reactivateSpare = async (cavoId) => {\n    try {\n      // Chiamata API per riattivare il cavo SPARE\n      await caviService.reactivateSpare(cantiereId, cavoId);\n      onSuccess(`Cavo ${cavoId} riattivato con successo`);\n      return true;\n    } catch (error) {\n      console.error('Errore durante la riattivazione del cavo SPARE:', error);\n      onError('Errore durante la riattivazione del cavo SPARE: ' + (error.message || 'Errore sconosciuto'));\n      throw error;\n    }\n  };\n\n  // Gestisce il cambio dei valori nel form\n  const handleFormChange = (e) => {\n    const { name, value } = e.target;\n\n    // Gestione speciale per il cambio di bobina\n    if (name === 'id_bobina' && value && value !== 'BOBINA_VUOTA') {\n      // Verifica compatibilità tra cavo e bobina\n      const bobina = bobine.find(b => b.id_bobina === value);\n\n      if (bobina && selectedCavo) {\n        // Nella nuova configurazione, controlliamo solo tipologia e formazione (sezione)\n        // Utilizziamo una comparazione più robusta con trim() e gestione di null/undefined\n        const isCompatible =\n          String(bobina.tipologia || '').trim() === String(selectedCavo.tipologia || '').trim() &&\n          String(bobina.sezione || '0').trim() === String(selectedCavo.sezione || '0').trim();\n\n        if (!isCompatible) {\n          console.log('Bobina incompatibile selezionata:', bobina);\n          console.log('Cavo corrente:', selectedCavo);\n          console.log('Confronto formazione:', {\n            cavoFormazione: String(selectedCavo.sezione || '0'),\n            bobinaFormazione: String(bobina.sezione || '0')\n          });\n\n          // Mostra il dialogo di incompatibilità\n          setIncompatibleReelData({\n            cavo: selectedCavo,\n            bobina: bobina\n          });\n          setShowIncompatibleReelDialog(true);\n\n          // Non aggiornare il valore del form finché l'utente non conferma\n          return;\n        }\n      }\n    }\n\n    setFormData({\n      ...formData,\n      [name]: value\n    });\n\n    // Validazione in tempo reale\n    validateField(name, value);\n  };\n\n  // Validazione di un campo\n  const validateField = (name, value) => {\n    let error = null;\n    let warning = null;\n\n    if (name === 'metri_posati') {\n      // Controllo input vuoto\n      if (!value || value.trim() === '') {\n        error = 'Inserire un valore per i metri posati';\n        return false;\n      }\n\n      // Controllo formato numerico\n      if (isNaN(parseFloat(value)) || parseFloat(value) <= 0) {\n        error = 'Inserire un valore numerico positivo';\n        return false;\n      }\n\n      const metriPosati = parseFloat(value);\n\n      // Controllo metri teorici cavo\n      if (selectedCavo && selectedCavo.metri_teorici && metriPosati > parseFloat(selectedCavo.metri_teorici)) {\n        warning = `I metri posati (${metriPosati}m) superano i metri teorici del cavo (${selectedCavo.metri_teorici}m)`;\n      }\n\n      // Controllo metri residui bobina (se bobina reale)\n      if (formData.id_bobina && formData.id_bobina !== 'BOBINA_VUOTA') {\n        const bobina = bobine.find(b => b.id_bobina === formData.id_bobina);\n        if (bobina && metriPosati > parseFloat(bobina.metri_residui)) {\n          warning = `I metri posati (${metriPosati}m) superano i metri residui della bobina (${bobina.metri_residui}m). La bobina andrà in stato OVER.`;\n        }\n      }\n    } else if (name === 'id_bobina') {\n      // Controllo che sia selezionata una bobina\n      if (!value || value.trim() === '') {\n        error = 'È necessario selezionare una bobina';\n        return false;\n      }\n    }\n\n    // Aggiorna gli errori\n    setFormErrors(prev => ({\n      ...prev,\n      [name]: error\n    }));\n\n    // Aggiorna gli avvisi\n    setFormWarnings(prev => ({\n      ...prev,\n      [name]: warning\n    }));\n\n    return !error;\n  };\n\n  // Stati per i dialoghi di conferma\n  const [notificationShown, setNotificationShown] = useState(false);\n  const [showConfirmDialog, setShowConfirmDialog] = useState(false);\n  const [confirmDialogProps, setConfirmDialogProps] = useState({\n    title: '',\n    message: '',\n    onConfirm: () => {}\n  });\n\n  // Nota: Lo stato per il dialogo di bobina incompatibile è già dichiarato sopra\n\n  // Validazione completa del form\n  const validateForm = () => {\n    let isValid = true;\n    const errors = {};\n    const warnings = {};\n\n    // Reset della variabile di notifica\n    setNotificationShown(false);\n\n    // Validazione metri posati\n    if (!formData.metri_posati || formData.metri_posati.trim() === '') {\n      errors.metri_posati = 'Inserire un valore per i metri posati';\n      isValid = false;\n    } else if (isNaN(parseFloat(formData.metri_posati)) || parseFloat(formData.metri_posati) <= 0) {\n      errors.metri_posati = 'Inserire un valore numerico positivo';\n      isValid = false;\n    }\n\n    // Validazione bobina (deve essere selezionata)\n    if (!formData.id_bobina || formData.id_bobina.trim() === '') {\n      errors.id_bobina = 'È necessario selezionare una bobina';\n      isValid = false;\n    }\n\n    if (isValid) {\n      const metriPosati = parseFloat(formData.metri_posati);\n\n      // Controllo metri teorici cavo\n      if (selectedCavo && selectedCavo.metri_teorici && metriPosati > parseFloat(selectedCavo.metri_teorici)) {\n        warnings.metri_posati = `I metri posati (${metriPosati}m) superano i metri teorici del cavo (${selectedCavo.metri_teorici}m)`;\n        setNotificationShown(true); // Imposta la variabile per evitare notifiche duplicate\n        // Non mostrare più il popup di conferma, solo l'avviso nel form\n        // Continua con la validazione senza interrompere il flusso\n      }\n\n      // Controllo metri residui bobina (se bobina reale)\n      if (isValid && formData.id_bobina && formData.id_bobina !== 'BOBINA_VUOTA') {\n        const bobina = bobine.find(b => b.id_bobina === formData.id_bobina);\n        if (bobina && metriPosati > parseFloat(bobina.metri_residui)) {\n          warnings.metri_posati = `I metri posati (${metriPosati}m) superano i metri residui della bobina (${bobina.metri_residui}m). La bobina andrà in stato OVER.`;\n          setNotificationShown(true); // Imposta la variabile per evitare notifiche duplicate\n\n          // Mostra il dialogo di conferma invece di window.confirm\n          setConfirmDialogProps({\n            title: 'Attenzione: Bobina in stato OVER',\n            message: `I metri posati (${metriPosati}m) superano i metri residui della bobina (${bobina.metri_residui}m). Questo porterà la bobina in stato OVER. Vuoi continuare?`,\n            onConfirm: () => {\n              // Continua con la validazione\n              handleNext();\n            }\n          });\n          setShowConfirmDialog(true);\n          return false; // Interrompi la validazione fino alla conferma dell'utente\n        }\n      }\n    }\n\n    setFormErrors(errors);\n    setFormWarnings(warnings);\n    return isValid;\n  };\n\n  // Gestisce il passaggio al passo successivo\n  const handleNext = () => {\n    if (activeStep === 2) {\n      // Validazione prima di passare al passo successivo (da Inserisci Metri a Conferma)\n      if (!validateForm()) {\n        return;\n      }\n    } else if (activeStep === 1) {\n      // Carica le bobine prima di passare al passo successivo (da Associa Bobina a Inserisci Metri)\n      loadBobine();\n    }\n\n    setActiveStep((prevActiveStep) => prevActiveStep + 1);\n  };\n\n  // Gestisce il ritorno al passo precedente\n  const handleBack = () => {\n    setActiveStep((prevActiveStep) => prevActiveStep - 1);\n  };\n\n  // Gestisce il reset del form\n  const handleReset = () => {\n    setActiveStep(0);\n    setSelectedCavo(null);\n    setCavoIdInput('');\n    setFormData({\n      id_cavo: '',\n      metri_posati: '',\n      id_bobina: ''\n    });\n    setFormErrors({});\n    setFormWarnings({});\n  };\n\n  // Determina lo stato di installazione in base ai metri posati\n  // Utilizziamo la funzione dalla utility stateUtils\n  const determineInstallationStatus = (metriPosati, metriTeorici) => {\n    return determineCableState(metriPosati, metriTeorici);\n  };\n\n  // Gestisce l'invio del form\n  const handleSubmit = async () => {\n    // Dichiarazione delle variabili al di fuori del blocco try/catch\n    // in modo che siano accessibili anche nel blocco catch\n    let idBobina;\n    let statoInstallazione;\n    let metriPosati;\n    let forceOver = false;\n\n    try {\n      setLoading(true);\n\n      // Validazione finale\n      if (!validateForm()) {\n        setLoading(false);\n        return;\n      }\n\n      // Prepara i dati da inviare\n      metriPosati = parseFloat(formData.metri_posati);\n\n      // Gestione speciale per BOBINA_VUOTA\n      idBobina = formData.id_bobina;\n\n      // Gestione differenziata per cavi non posati e cavi posati\n      if (!idBobina || idBobina === '') {\n        // È necessario selezionare una bobina, anche BOBINA_VUOTA\n        setFormErrors({\n          ...formErrors,\n          id_bobina: 'È necessario selezionare una bobina'\n        });\n        setLoading(false);\n        return;\n      } else if (idBobina === 'BOBINA_VUOTA') {\n        // Per cavi posati senza bobina specifica\n        console.log('Usando BOBINA_VUOTA per il cavo');\n        // Assicurati che BOBINA_VUOTA venga passato come stringa e non come null\n        idBobina = 'BOBINA_VUOTA';\n      } else {\n        // Per cavi posati con bobina specifica\n        console.log(`Usando bobina reale: ${idBobina}`);\n      }\n\n      // Determina lo stato di installazione\n      statoInstallazione = determineInstallationStatus(metriPosati, selectedCavo.metri_teorici);\n\n      // Imposta sempre forceOver a true per evitare blocchi quando la bobina va in OVER\n      forceOver = true;\n      console.log('Impostando forceOver a true per garantire il completamento dell\\'operazione');\n\n      // Log delle condizioni che richiederebbero forceOver\n      if (idBobina === 'BOBINA_VUOTA') {\n        console.log('Operazione con BOBINA_VUOTA');\n      }\n      // Per bobine reali, verifica i metri residui\n      else if (idBobina && idBobina !== 'BOBINA_VUOTA') {\n        const bobina = bobine.find(b => b.id_bobina === idBobina);\n        if (bobina && metriPosati > parseFloat(bobina.metri_residui)) {\n          console.log(`La bobina ${idBobina} andrà in stato OVER (metri posati ${metriPosati} > metri residui ${bobina.metri_residui})`);\n        }\n      }\n\n      // Verifica anche se i metri posati superano i metri teorici\n      if (selectedCavo && selectedCavo.metri_teorici && metriPosati > parseFloat(selectedCavo.metri_teorici)) {\n        console.log(`I metri posati (${metriPosati}) superano i metri teorici (${selectedCavo.metri_teorici})`);\n      }\n\n      // Log per debug\n      console.log('Invio dati:', {\n        cantiereId,\n        cavoId: formData.id_cavo,\n        metriPosati,\n        idBobina,\n        forceOver,\n        statoInstallazione\n      });\n\n      // Conferma finale prima dell'invio solo se non è già stata mostrata una notifica\n      if (!notificationShown) {\n        const confirmMessage = `Confermi l'aggiornamento del cavo ${formData.id_cavo} con ${metriPosati}m posati?`;\n\n        // Usa il dialogo di conferma invece di window.confirm\n        setConfirmDialogProps({\n          title: 'Conferma aggiornamento',\n          message: confirmMessage,\n          onConfirm: async () => {\n            // Esegui la chiamata API qui invece che nel flusso principale\n            try {\n              setLoading(true);\n\n              // Imposta sempre forceOver a true per evitare blocchi quando la bobina va in OVER\n              console.log('Impostando forceOver a true nel dialogo di conferma per garantire il completamento dell\\'operazione');\n              await caviService.updateMetriPosati(\n                cantiereId,\n                formData.id_cavo,\n                metriPosati,\n                idBobina,\n                true // Forza sempre a true per evitare blocchi\n              );\n\n              // Messaggio di successo con dettagli sulla bobina\n              let successMessage = `Metri posati aggiornati con successo. Stato cavo: ${statoInstallazione}`;\n              if (idBobina === 'BOBINA_VUOTA') {\n                successMessage += '. Cavo associato a BOBINA VUOTA';\n              } else if (idBobina) {\n                const bobina = bobine.find(b => b.id_bobina === idBobina);\n                if (bobina) {\n                  successMessage += `. Cavo associato alla bobina ${idBobina}`;\n                }\n              }\n\n              // Gestione successo\n              onSuccess(successMessage);\n\n              // Reset del form\n              handleReset();\n\n              // Ricarica i cavi\n              loadCavi();\n            } catch (error) {\n              console.error('Errore durante l\\'aggiornamento dei metri posati:', error);\n\n              // Gestione speciale per BOBINA_VUOTA\n              if (idBobina === 'BOBINA_VUOTA' && error.success) {\n                // Se è un \"errore\" di successo per BOBINA_VUOTA, trattiamolo come un successo\n                let successMessage = `Metri posati aggiornati con successo. Stato cavo: ${statoInstallazione}. Cavo associato a BOBINA VUOTA`;\n                onSuccess(successMessage);\n\n                // Reset del form\n                handleReset();\n\n                // Ricarica i cavi\n                loadCavi();\n                return;\n              }\n\n              // Gestione dettagliata degli errori\n              let errorMessage = 'Errore durante l\\'aggiornamento dei metri posati';\n\n              if (error.response) {\n                // Il server ha risposto con un codice di errore\n                const status = error.response.status;\n                const detail = error.response.data?.detail || error.message;\n\n                if (status === 400) {\n                  // Errore di validazione\n                  if (detail.includes('metri residui')) {\n                    errorMessage = `La bobina non ha metri residui sufficienti. Usa l'opzione \"BOBINA VUOTA\" o seleziona un'altra bobina.`;\n                  } else if (detail.includes('già posato')) {\n                    errorMessage = `Il cavo risulta già posato. Usa la funzione \"Modifica bobina cavo posato\".`;\n                  } else {\n                    errorMessage = detail;\n                  }\n                } else if (status === 404) {\n                  // Gestione speciale per BOBINA_VUOTA quando la bobina non viene trovata\n                  if (idBobina === 'BOBINA_VUOTA' && detail.includes('non trovata')) {\n                    let successMessage = `Metri posati aggiornati con successo. Stato cavo: ${statoInstallazione}. Cavo associato a BOBINA VUOTA`;\n                    onSuccess(successMessage);\n\n                    // Reset del form\n                    handleReset();\n\n                    // Ricarica i cavi\n                    loadCavi();\n                    return;\n                  } else {\n                    errorMessage = `Cavo o bobina non trovati: ${detail}`;\n                  }\n                } else {\n                  errorMessage = `Errore del server (${status}): ${detail}`;\n                }\n              } else if (error.request) {\n                // La richiesta è stata inviata ma non è stata ricevuta risposta\n                errorMessage = 'Nessuna risposta dal server. Verifica la connessione di rete.';\n              } else if (error.detail && idBobina === 'BOBINA_VUOTA') {\n                // Gestione speciale per errori con BOBINA_VUOTA\n                errorMessage = error.detail;\n                if (error.status === 200 || error.success) {\n                  let successMessage = `Metri posati aggiornati con successo. Stato cavo: ${statoInstallazione}. Cavo associato a BOBINA VUOTA`;\n                  onSuccess(successMessage);\n\n                  // Reset del form\n                  handleReset();\n\n                  // Ricarica i cavi\n                  loadCavi();\n                  return;\n                }\n              } else {\n                // Errore durante la configurazione della richiesta\n                errorMessage = error.message || error.detail || 'Errore sconosciuto';\n              }\n\n              onError(errorMessage);\n            } finally {\n              setLoading(false);\n            }\n          }\n        });\n        setShowConfirmDialog(true);\n        setLoading(false);\n        return;\n      }\n\n      // Chiamata API\n      console.log('Invio richiesta updateMetriPosati con i seguenti parametri:');\n      console.log('- cantiereId:', cantiereId);\n      console.log('- id_cavo:', formData.id_cavo);\n      console.log('- metri_posati:', metriPosati);\n      console.log('- id_bobina:', idBobina, typeof idBobina);\n      console.log('- forceOver:', forceOver);\n\n      // Imposta sempre forceOver a true per evitare blocchi quando la bobina va in OVER\n      console.log('Impostando forceOver a true nella chiamata diretta per garantire il completamento dell\\'operazione');\n      await caviService.updateMetriPosati(\n        cantiereId,\n        formData.id_cavo,\n        metriPosati,\n        idBobina,\n        true // Forza sempre a true per evitare blocchi\n      );\n\n      // Messaggio di successo con dettagli sulla bobina\n      let successMessage = `Metri posati aggiornati con successo. Stato cavo: ${statoInstallazione}`;\n      if (idBobina === 'BOBINA_VUOTA') {\n        successMessage += '. Cavo associato a BOBINA VUOTA';\n      } else if (idBobina) {\n        const bobina = bobine.find(b => b.id_bobina === idBobina);\n        if (bobina) {\n          successMessage += `. Cavo associato alla bobina ${idBobina}`;\n        }\n      }\n\n      // Gestione successo\n      onSuccess(successMessage);\n\n      // Reset del form\n      handleReset();\n\n      // Ricarica i cavi\n      loadCavi();\n    } catch (error) {\n      console.error('Errore durante l\\'aggiornamento dei metri posati:', error);\n\n      // Gestione speciale per BOBINA_VUOTA\n      if (idBobina === 'BOBINA_VUOTA' && error.success) {\n        // Se è un \"errore\" di successo per BOBINA_VUOTA, trattiamolo come un successo\n        let successMessage = `Metri posati aggiornati con successo. Stato cavo: ${statoInstallazione}. Cavo associato a BOBINA VUOTA`;\n        onSuccess(successMessage);\n\n        // Reset del form\n        handleReset();\n\n        // Ricarica i cavi\n        loadCavi();\n        return;\n      }\n\n      // Gestione dettagliata degli errori\n      let errorMessage = 'Errore durante l\\'aggiornamento dei metri posati';\n\n      if (error.response) {\n        // Il server ha risposto con un codice di errore\n        const status = error.response.status;\n        const detail = error.response.data?.detail || error.message;\n\n        if (status === 400) {\n          // Errore di validazione\n          if (detail.includes('metri residui')) {\n            errorMessage = `La bobina non ha metri residui sufficienti. Usa l'opzione \"BOBINA VUOTA\" o seleziona un'altra bobina.`;\n          } else if (detail.includes('già posato')) {\n            errorMessage = `Il cavo risulta già posato. Usa la funzione \"Modifica bobina cavo posato\".`;\n          } else {\n            errorMessage = detail;\n          }\n        } else if (status === 404) {\n          // Gestione speciale per BOBINA_VUOTA quando la bobina non viene trovata\n          if (idBobina === 'BOBINA_VUOTA' && detail.includes('non trovata')) {\n            let successMessage = `Metri posati aggiornati con successo. Stato cavo: ${statoInstallazione}. Cavo associato a BOBINA VUOTA`;\n            onSuccess(successMessage);\n\n            // Reset del form\n            handleReset();\n\n            // Ricarica i cavi\n            loadCavi();\n            return;\n          } else {\n            errorMessage = `Cavo o bobina non trovati: ${detail}`;\n          }\n        } else {\n          errorMessage = `Errore del server (${status}): ${detail}`;\n        }\n      } else if (error.request) {\n        // La richiesta è stata inviata ma non è stata ricevuta risposta\n        errorMessage = 'Nessuna risposta dal server. Verifica la connessione di rete.';\n      } else if (error.detail && idBobina === 'BOBINA_VUOTA') {\n        // Gestione speciale per errori con BOBINA_VUOTA\n        errorMessage = error.detail;\n        if (error.status === 200 || error.success) {\n          let successMessage = `Metri posati aggiornati con successo. Stato cavo: ${statoInstallazione}. Cavo associato a BOBINA VUOTA`;\n          onSuccess(successMessage);\n\n          // Reset del form\n          handleReset();\n\n          // Ricarica i cavi\n          loadCavi();\n          return;\n        }\n      } else {\n        // Errore durante la configurazione della richiesta\n        errorMessage = error.message || error.detail || 'Errore sconosciuto';\n      }\n\n      onError(errorMessage);\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  // Renderizza il passo 1: Selezione del cavo\n  const renderStep1 = () => {\n    return (\n      <Box>\n        <Typography variant=\"subtitle2\" sx={{ mb: 1, fontWeight: 'bold' }}>\n          Seleziona un cavo\n        </Typography>\n\n        {/* Ricerca per ID - Versione compatta */}\n        <Paper sx={{ p: 1.5, mb: 2, width: '100%' }}>\n          <Box sx={{ display: 'flex', alignItems: 'center', width: '100%' }}>\n            <Typography variant=\"subtitle2\" sx={{ mr: 1, minWidth: '80px' }}>\n              Cerca cavo\n            </Typography>\n            <TextField\n              size=\"small\"\n              label=\"ID Cavo\"\n              variant=\"outlined\"\n              value={cavoIdInput}\n              onChange={(e) => setCavoIdInput(e.target.value)}\n              placeholder=\"Inserisci l'ID del cavo\"\n              sx={{ flexGrow: 1, mr: 1 }}\n            />\n            <Button\n              variant=\"contained\"\n              color=\"primary\"\n              onClick={handleSearchCavoById}\n              disabled={caviLoading || !cavoIdInput.trim()}\n              startIcon={caviLoading ? <CircularProgress size={16} /> : <SearchIcon fontSize=\"small\" />}\n              size=\"small\"\n              sx={{ minWidth: '80px', height: '36px' }}\n            >\n              CERCA\n            </Button>\n          </Box>\n        </Paper>\n\n        {/* Lista cavi - versione compatta */}\n        <Paper sx={{ p: 1.5, width: '100%' }}>\n          <Typography variant=\"subtitle2\" sx={{ mb: 1 }}>\n            Seleziona dalla lista\n          </Typography>\n\n          {caviLoading ? (\n            <Box sx={{ display: 'flex', justifyContent: 'center', my: 2 }}>\n              <CircularProgress size={24} />\n            </Box>\n          ) : cavi.length === 0 ? (\n            <Alert severity=\"info\" sx={{ py: 0.5 }}>\n              <Typography variant=\"caption\">Non ci sono cavi disponibili da installare.</Typography>\n            </Alert>\n          ) : (\n            <TableContainer component={Paper} variant=\"outlined\" sx={{ maxHeight: '300px', overflow: 'auto', width: '100%' }}>\n              <Table size=\"small\" stickyHeader>\n                <TableHead>\n                  <TableRow sx={{ '& th': { fontWeight: 'bold', py: 1, bgcolor: '#f5f5f5' } }}>\n                    <TableCell>ID Cavo</TableCell>\n                    <TableCell>Tipologia</TableCell>\n                    <TableCell>Formazione</TableCell>\n                    <TableCell>Metri</TableCell>\n                    <TableCell>Stato</TableCell>\n                    <TableCell align=\"center\" sx={{ width: '40px' }}>Info</TableCell>\n                  </TableRow>\n                </TableHead>\n                <TableBody>\n                  {cavi.map((cavo) => (\n                    <TableRow\n                      key={cavo.id_cavo}\n                      hover\n                      onClick={() => handleCavoSelect(cavo)}\n                      sx={{\n                        cursor: 'pointer',\n                        '&:hover': { bgcolor: '#f1f8e9' },\n                        '& td': { py: 0.5 }\n                      }}\n                    >\n                      <TableCell sx={{ fontWeight: 'medium' }}>{cavo.id_cavo}</TableCell>\n                      <TableCell>{cavo.tipologia || 'N/A'}</TableCell>\n                      <TableCell>{cavo.sezione || 'N/A'}</TableCell>\n                      <TableCell>{cavo.metri_teorici || 'N/A'}</TableCell>\n                      <TableCell>\n                        <Chip\n                          size=\"small\"\n                          label={isCableSpare(cavo) ? 'SPARE' : isCableInstalled(cavo) ? 'Installato' : cavo.stato_installazione}\n                          color={isCableSpare(cavo) ? 'error' : isCableInstalled(cavo) ? 'success' : getCableStateColor(cavo.stato_installazione)}\n                          sx={{ height: '20px', '& .MuiChip-label': { px: 1, py: 0, fontSize: '0.7rem' } }}\n                        />\n                      </TableCell>\n                      <TableCell align=\"center\">\n                        <IconButton\n                          size=\"small\"\n                          onClick={(e) => {\n                            e.stopPropagation();\n                            setSelectedCavo(cavo);\n                            setShowCavoDetailsDialog(true);\n                          }}\n                        >\n                          <InfoIcon fontSize=\"small\" />\n                        </IconButton>\n                      </TableCell>\n                    </TableRow>\n                  ))}\n                </TableBody>\n              </Table>\n            </TableContainer>\n          )}\n        </Paper>\n      </Box>\n    );\n  };\n\n  // Renderizza il passo 2: Inserimento metri\n  const renderStep2 = () => {\n    if (!selectedCavo) return null;\n\n    return (\n      <Box>\n        <Typography variant=\"subtitle2\" sx={{ mb: 1, fontWeight: 'bold' }}>\n          Inserisci metri posati\n        </Typography>\n\n        {/* Dettagli cavo in formato compatto */}\n        <Paper sx={{ p: 1.5, mb: 2, width: '100%' }}>\n          <Box sx={{ display: 'flex', flexWrap: 'wrap', alignItems: 'center', width: '100%' }}>\n            <Typography variant=\"subtitle2\" sx={{ mr: 2, fontWeight: 'bold' }}>\n              Cavo selezionato: <span style={{ color: '#1976d2' }}>{selectedCavo.id_cavo}</span>\n            </Typography>\n\n            <Box sx={{ display: 'flex', flexWrap: 'wrap', gap: 2, ml: 'auto' }}>\n              <Box sx={{ display: 'flex', alignItems: 'center' }}>\n                <Typography variant=\"body2\" sx={{ mr: 0.5, fontWeight: 'medium' }}>Tipologia:</Typography>\n                <Typography variant=\"body2\">{selectedCavo.tipologia || 'N/A'}</Typography>\n              </Box>\n\n              <Box sx={{ display: 'flex', alignItems: 'center' }}>\n                <Typography variant=\"body2\" sx={{ mr: 0.5, fontWeight: 'medium' }}>Formazione:</Typography>\n                <Typography variant=\"body2\">{selectedCavo.sezione || 'N/A'}</Typography>\n              </Box>\n\n              <Box sx={{ display: 'flex', alignItems: 'center' }}>\n                <Typography variant=\"body2\" sx={{ mr: 0.5, fontWeight: 'medium' }}>Metri teorici:</Typography>\n                <Typography variant=\"body2\">{selectedCavo.metri_teorici || 'N/A'} m</Typography>\n              </Box>\n\n              <Box sx={{ display: 'flex', alignItems: 'center' }}>\n                <Typography variant=\"body2\" sx={{ mr: 0.5, fontWeight: 'medium' }}>Stato:</Typography>\n                <Chip\n                  label={selectedCavo.stato_installazione || 'N/D'}\n                  size=\"small\"\n                  color={getCableStateColor(selectedCavo.stato_installazione)}\n                  variant=\"outlined\"\n                  sx={{ height: '20px', '& .MuiChip-label': { px: 1, py: 0 } }}\n                />\n              </Box>\n            </Box>\n          </Box>\n        </Paper>\n\n        <Paper sx={{ p: 2, width: '100%' }}>\n          <Typography variant=\"subtitle2\" gutterBottom sx={{ fontWeight: 'bold' }}>\n            Inserisci i metri posati\n          </Typography>\n\n          {/* Informazioni sul cavo e sulla bobina in una riga */}\n          <Box sx={{ display: 'flex', flexWrap: 'wrap', gap: 2, mb: 2 }}>\n            <Box sx={{ p: 1, bgcolor: '#f5f5f5', borderRadius: 1, flex: '1 1 auto', minWidth: '200px', maxWidth: '400px' }}>\n              <Typography variant=\"caption\" sx={{ fontWeight: 'bold', color: 'primary.main', display: 'block', mb: 0.5 }}>\n                Informazioni cavo\n              </Typography>\n              <Box sx={{ display: 'flex', justifyContent: 'space-between', mb: 0.5 }}>\n                <Typography variant=\"body2\" sx={{ fontWeight: 'medium', fontSize: '0.8rem' }}>Metri teorici:</Typography>\n                <Typography variant=\"body2\" sx={{ fontSize: '0.8rem' }}>{selectedCavo.metri_teorici || 'N/A'} m</Typography>\n              </Box>\n              <Box sx={{ display: 'flex', justifyContent: 'space-between' }}>\n                <Typography variant=\"body2\" sx={{ fontWeight: 'medium', fontSize: '0.8rem' }}>Stato attuale:</Typography>\n                <Chip\n                  label={selectedCavo.stato_installazione || 'N/D'}\n                  size=\"small\"\n                  color={getCableStateColor(selectedCavo.stato_installazione)}\n                  variant=\"outlined\"\n                  sx={{ height: '18px', '& .MuiChip-label': { px: 1, py: 0, fontSize: '0.7rem' } }}\n                />\n              </Box>\n            </Box>\n\n            <Box sx={{ p: 1, bgcolor: '#f5f5f5', borderRadius: 1, flex: '1 1 auto', minWidth: '200px', maxWidth: '400px' }}>\n              <Typography variant=\"caption\" sx={{ fontWeight: 'bold', color: 'secondary.main', display: 'block', mb: 0.5 }}>\n                Informazioni bobina\n              </Typography>\n              {formData.id_bobina && formData.id_bobina !== 'BOBINA_VUOTA' ? (() => {\n                const bobina = bobine.find(b => b.id_bobina === formData.id_bobina);\n                return bobina ? (\n                  <>\n                    <Box sx={{ display: 'flex', justifyContent: 'space-between', mb: 0.5 }}>\n                      <Typography variant=\"body2\" sx={{ fontWeight: 'medium', fontSize: '0.8rem' }}>ID Bobina:</Typography>\n                      <Typography variant=\"body2\" sx={{ fontSize: '0.8rem' }}>{getBobinaNumber(bobina.id_bobina)}</Typography>\n                    </Box>\n                    <Box sx={{ display: 'flex', justifyContent: 'space-between', mb: 0.5 }}>\n                      <Typography variant=\"body2\" sx={{ fontWeight: 'medium', fontSize: '0.8rem' }}>Metri residui:</Typography>\n                      <Typography variant=\"body2\" sx={{ fontSize: '0.8rem' }}>{bobina.metri_residui || 0} m</Typography>\n                    </Box>\n                    <Box sx={{ display: 'flex', justifyContent: 'space-between' }}>\n                      <Typography variant=\"body2\" sx={{ fontWeight: 'medium', fontSize: '0.8rem' }}>Stato:</Typography>\n                      <Chip\n                        label={bobina.stato_bobina || 'N/D'}\n                        size=\"small\"\n                        color={getReelStateColor(bobina.stato_bobina)}\n                        variant=\"outlined\"\n                        sx={{ height: '18px', '& .MuiChip-label': { px: 1, py: 0, fontSize: '0.7rem' } }}\n                      />\n                    </Box>\n                  </>\n                ) : (\n                  <Typography variant=\"body2\" sx={{ fontSize: '0.8rem' }}>Bobina non trovata</Typography>\n                );\n              })() : (\n                <Typography variant=\"body2\" sx={{ fontSize: '0.8rem' }}>\n                  {formData.id_bobina === 'BOBINA_VUOTA' ?\n                    \"Utilizzo BOBINA VUOTA (nessuna bobina associata)\" :\n                    \"Nessuna bobina selezionata\"}\n                </Typography>\n              )}\n            </Box>\n          </Box>\n\n          <Box sx={{ mt: 2, mb: 2, width: '100%' }}>\n            <Typography variant=\"subtitle2\" gutterBottom sx={{ fontWeight: 'bold' }}>\n              Metratura posata\n            </Typography>\n            <TextField\n              size=\"small\"\n              label=\"Metri posati\"\n              variant=\"outlined\"\n              name=\"metri_posati\"\n              type=\"number\"\n              value={formData.metri_posati}\n              onChange={handleFormChange}\n              error={!!formErrors.metri_posati}\n              helperText={formErrors.metri_posati || formWarnings.metri_posati}\n              FormHelperTextProps={{\n                sx: { color: formWarnings.metri_posati && !formErrors.metri_posati ? 'warning.main' : 'error.main' }\n              }}\n              sx={{ mb: 1, width: '200px' }}\n              inputProps={{\n                max: 999999, // Limite a 6 cifre\n                step: 0.1\n              }}\n            />\n          </Box>\n\n          {formWarnings.metri_posati && !formErrors.metri_posati && !notificationShown && (\n            <Alert severity=\"warning\" sx={{ mb: 2 }}>\n              {formWarnings.metri_posati}\n            </Alert>\n          )}\n\n          <Alert severity=\"info\" sx={{ mt: 2 }}>\n            <Typography variant=\"body2\">\n              Inserisci i metri di cavo effettivamente posati. Questo valore aggiorna lo stato di installazione del cavo.\n            </Typography>\n          </Alert>\n        </Paper>\n      </Box>\n    );\n  };\n\n  // Renderizza il passo 3: Associazione bobina\n  const renderStep3 = () => {\n\n    // Funzione per costruire l'ID completo della bobina\n    const buildFullBobinaId = (numeroBobina) => {\n      return `C${cantiereId}_B${numeroBobina}`;\n    };\n\n    // Verifica se una bobina ha metri residui sufficienti\n    const hasSufficientMeters = (bobina) => {\n      if (!bobina || !formData.metri_posati) return true;\n      return parseFloat(bobina.metri_residui) >= parseFloat(formData.metri_posati);\n    };\n\n    // Gestisce l'input diretto del numero della bobina\n    const handleBobinaNumberInput = (e) => {\n      const numeroBobina = e.target.value.trim();\n\n      // Gestione esplicita dell'input 'v' per bobina vuota\n      if (numeroBobina.toLowerCase() === 'v') {\n        setFormData({\n          ...formData,\n          id_bobina: ''\n        });\n        setFormErrors({\n          ...formErrors,\n          id_bobina_input: null\n        });\n        return;\n      }\n\n      if (numeroBobina) {\n        // Costruisci l'ID completo\n        const idBobinaCompleto = buildFullBobinaId(numeroBobina);\n\n        // Verifica se la bobina esiste\n        const bobinaEsistente = bobine.find(b => b.id_bobina === idBobinaCompleto);\n\n        if (bobinaEsistente) {\n          // Verifica se la bobina è in stato OVER o TERMINATA\n          if (bobinaEsistente.stato_bobina === 'Over' || bobinaEsistente.stato_bobina === 'Terminata') {\n            setFormErrors({\n              ...formErrors,\n              id_bobina_input: `La bobina ${numeroBobina} è in stato ${bobinaEsistente.stato_bobina} e non può essere utilizzata`\n            });\n            return;\n          }\n\n          // Verifica compatibilità tra cavo e bobina\n          if (selectedCavo) {\n            // Converti i valori in stringhe, gestendo null e undefined\n            const cavoTipologia = String(selectedCavo.tipologia || '');\n            const cavoSezione = String(selectedCavo.sezione || '0');\n\n            const bobinaTipologia = String(bobinaEsistente.tipologia || '');\n            const bobinaSezione = String(bobinaEsistente.sezione || '0');\n\n            // Log per debug\n            console.log(`Verifica compatibilità bobina ${bobinaEsistente.id_bobina}:`, {\n              tipologia: `${bobinaTipologia} === ${cavoTipologia}`,\n              formazione: `${bobinaSezione} === ${cavoSezione}`\n            });\n\n            if (bobinaTipologia !== cavoTipologia ||\n                bobinaSezione !== cavoSezione) {\n              // Mostra il dialogo per bobine incompatibili\n              setIncompatibleReelData({\n                cavo: selectedCavo,\n                bobina: bobinaEsistente\n              });\n              setShowIncompatibleReelDialog(true);\n              return;\n            }\n          }\n\n          // Verifica se la bobina ha metri residui sufficienti\n          if (hasSufficientMeters(bobinaEsistente)) {\n            // Se la bobina esiste e ha metri sufficienti, imposta l'ID completo\n            setFormData({\n              ...formData,\n              id_bobina: idBobinaCompleto\n            });\n            setFormErrors({\n              ...formErrors,\n              id_bobina_input: null\n            });\n          } else {\n            // Se la bobina non ha metri sufficienti, mostra un avviso\n            setFormErrors({\n              ...formErrors,\n              id_bobina_input: `La bobina ${numeroBobina} non ha metri residui sufficienti (${bobinaEsistente.metri_residui} m disponibili)`\n            });\n          }\n        } else {\n          // Se la bobina non esiste, mostra un errore\n          setFormErrors({\n            ...formErrors,\n            id_bobina_input: `Bobina ${numeroBobina} non trovata`\n          });\n        }\n      } else {\n        // Se l'input è vuoto, resetta l'ID bobina\n        setFormData({\n          ...formData,\n          id_bobina: ''\n        });\n        setFormErrors({\n          ...formErrors,\n          id_bobina_input: null\n        });\n      }\n    };\n\n    return (\n      <Box>\n        <Typography variant=\"subtitle2\" sx={{ mb: 1, fontWeight: 'bold' }}>\n          Associa bobina\n        </Typography>\n\n        <Paper sx={{ p: 2 }}>\n          <Typography variant=\"body2\" sx={{ mb: 1 }}>\n            Seleziona una bobina da associare al cavo o usa \"BOBINA VUOTA\" se non desideri associare una bobina specifica.\n          </Typography>\n\n          {bobineLoading ? (\n            <Box sx={{ display: 'flex', justifyContent: 'center', my: 1 }}>\n              <CircularProgress size={24} />\n            </Box>\n          ) : (\n            <Box>\n              <Box sx={{ display: 'flex', flexWrap: 'wrap', gap: 2 }}>\n                {/* Input diretto - versione compatta */}\n                <Box sx={{ flex: '1 1 200px', minWidth: '200px', maxWidth: '300px' }}>\n                  <Typography variant=\"caption\" sx={{ fontWeight: 'bold', display: 'block', mb: 0.5 }}>\n                    Inserimento diretto\n                  </Typography>\n                  <Box sx={{ display: 'flex', alignItems: 'flex-start', gap: 1 }}>\n                    <TextField\n                      size=\"small\"\n                      label=\"Numero bobina\"\n                      variant=\"outlined\"\n                      placeholder=\"Solo Y\"\n                      error={!!formErrors.id_bobina_input}\n                      onBlur={handleBobinaNumberInput}\n                      sx={{ flex: 1 }}\n                    />\n                  </Box>\n                  {formErrors.id_bobina_input && (\n                    <Typography variant=\"caption\" color=\"error\" sx={{ display: 'block', mt: 0.5 }}>\n                      {formErrors.id_bobina_input}\n                    </Typography>\n                  )}\n                  <Typography variant=\"caption\" sx={{ display: 'block', mt: 0.5 }}>\n                    ID Bobina: <strong>{formData.id_bobina || '-'}</strong>\n                  </Typography>\n                </Box>\n\n                {/* Selezione dalla lista - versione compatta */}\n                <Box sx={{ flex: '1 1 300px', minWidth: '300px' }}>\n                  <Typography variant=\"caption\" sx={{ fontWeight: 'bold', display: 'block', mb: 0.5 }}>\n                    {selectedCavo ? 'Bobine compatibili con il cavo selezionato' : 'Seleziona una bobina'}\n                  </Typography>\n\n                  <FormControl fullWidth size=\"small\" error={!!formErrors.id_bobina}>\n                    <InputLabel id=\"bobina-select-label\">Seleziona bobina</InputLabel>\n                    <Select\n                      labelId=\"bobina-select-label\"\n                      id=\"bobina-select\"\n                      name=\"id_bobina\"\n                      value={formData.id_bobina}\n                      label=\"Seleziona bobina\"\n                      onChange={handleFormChange}\n                      sx={{ maxHeight: '38px' }}\n                    >\n                      <MenuItem value=\"BOBINA_VUOTA\">\n                        <strong>BOBINA VUOTA</strong> (nessuna bobina associata)\n                      </MenuItem>\n                      <Divider />\n                      {bobine.length > 0 ? (\n                        <Box component=\"li\" sx={{ p: 0.5, bgcolor: 'background.paper' }}>\n                          <Typography variant=\"caption\" sx={{ fontWeight: 'bold', color: 'success.main', fontSize: '0.7rem' }}>\n                            Bobine disponibili: {bobine.length} (le compatibili sono evidenziate)\n                          </Typography>\n                        </Box>\n                      ) : null}\n                      {bobine.map((bobina) => (\n                        <MenuItem\n                          key={bobina.id_bobina}\n                          value={bobina.id_bobina}\n                          disabled={bobina.metri_residui < parseFloat(formData.metri_posati || 0)}\n                          sx={{\n                            py: 0.5,\n                            '&.Mui-selected': { bgcolor: 'success.light' },\n                            '&.Mui-selected:hover': { bgcolor: 'success.light' },\n                            bgcolor: selectedCavo &&\n                                   String(bobina.tipologia || '').trim() === String(selectedCavo.tipologia || '').trim() &&\n                                   String(bobina.sezione || '0').trim() === String(selectedCavo.sezione || '0').trim() ?\n                                   'rgba(76, 175, 80, 0.08)' : 'inherit'\n                          }}\n                        >\n                          <Box sx={{ display: 'flex', flexDirection: 'column', width: '100%' }}>\n                            <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', width: '100%' }}>\n                              <Typography variant=\"body2\" sx={{ fontWeight: 'bold', fontSize: '0.8rem' }}>\n                                {getBobinaNumber(bobina.id_bobina)} - {bobina.tipologia || 'N/A'}\n                              </Typography>\n                              {selectedCavo &&\n                               String(bobina.tipologia || '').trim() === String(selectedCavo.tipologia || '').trim() &&\n                               String(bobina.sezione || '0').trim() === String(selectedCavo.sezione || '0').trim() && (\n                                <Chip\n                                  size=\"small\"\n                                  label=\"Compatibile\"\n                                  color=\"success\"\n                                  variant=\"outlined\"\n                                  sx={{ height: 16, fontSize: '0.6rem', '& .MuiChip-label': { px: 0.5, py: 0 } }}\n                                />\n                              )}\n                            </Box>\n                            <Box sx={{ display: 'flex', justifyContent: 'space-between', width: '100%' }}>\n                              <Typography variant=\"caption\" sx={{ fontSize: '0.7rem' }}>\n                                {bobina.sezione || 'N/A'}\n                              </Typography>\n                              <Typography variant=\"caption\" sx={{ fontWeight: 'bold', fontSize: '0.7rem', color: bobina.metri_residui < parseFloat(formData.metri_posati || 0) ? 'error.main' : 'success.main' }}>\n                                {bobina.metri_residui || 0} m disponibili\n                              </Typography>\n                            </Box>\n                          </Box>\n                        </MenuItem>\n                      ))}\n                    </Select>\n                    {formErrors.id_bobina && (\n                      <FormHelperText>{formErrors.id_bobina}</FormHelperText>\n                    )}\n                  </FormControl>\n\n                  {bobine.length === 0 && !bobineLoading && (\n                    <Typography variant=\"caption\" color=\"warning.main\" sx={{ display: 'block', mt: 0.5 }}>\n                      Nessuna bobina compatibile trovata. Usa BOBINA VUOTA o seleziona una bobina non compatibile.\n                    </Typography>\n                  )}\n                </Box>\n              </Box>\n            </Box>\n          )}\n\n          {/* Mostra dettagli della bobina selezionata */}\n          {!bobineLoading && formData.id_bobina && (\n            <Box sx={{ mt: 3, p: 2, bgcolor: 'background.paper', borderRadius: 1, border: '1px solid #e0e0e0' }}>\n              <Typography variant=\"subtitle2\" gutterBottom>\n                Dettagli bobina selezionata\n              </Typography>\n              {(() => {\n                const bobina = bobine.find(b => b.id_bobina === formData.id_bobina);\n                if (bobina) {\n                  return (\n                    <Grid container spacing={2}>\n                      <Grid item xs={12} md={6}>\n                        <Typography variant=\"body2\">\n                          <strong>Numero:</strong> {getBobinaNumber(bobina.id_bobina)}\n                        </Typography>\n                        <Typography variant=\"body2\">\n                          <strong>Tipologia:</strong> {bobina.tipologia || 'N/A'}\n                        </Typography>\n                        <Typography variant=\"body2\">\n                          <strong>Conduttori:</strong> {bobina.n_conduttori || 'N/A'} x {bobina.sezione || 'N/A'}\n                        </Typography>\n                      </Grid>\n                      <Grid item xs={12} md={6}>\n                        <Typography variant=\"body2\">\n                          <strong>Metri totali:</strong> {bobina.metri_totali || 0} m\n                        </Typography>\n                        <Typography variant=\"body2\">\n                          <strong>Metri residui:</strong> {bobina.metri_residui || 0} m\n                        </Typography>\n                        <Typography variant=\"body2\">\n                          <strong>Stato:</strong> {bobina.stato_bobina || 'N/A'}\n                        </Typography>\n                      </Grid>\n                    </Grid>\n                  );\n                }\n                return (\n                  <Typography variant=\"body2\" color=\"error\">\n                    Bobina non trovata nel database\n                  </Typography>\n                );\n              })()}\n            </Box>\n          )}\n\n          {bobine.length === 0 && !bobineLoading && (\n            <Alert severity=\"warning\" sx={{ mt: 2 }}>\n              Non ci sono bobine disponibili. Puoi procedere senza associare una bobina o aggiungere prima una nuova bobina.\n            </Alert>\n          )}\n        </Paper>\n      </Box>\n    );\n  };\n\n  // Renderizza il passo 4: Conferma\n  const renderStep4 = () => {\n\n    // Ottieni il numero della bobina se presente\n    let numeroBobina = 'Nessuna';\n    let bobinaInfo = null;\n\n    if (formData.id_bobina === 'BOBINA_VUOTA') {\n      numeroBobina = 'BOBINA VUOTA';\n    } else if (formData.id_bobina) {\n      numeroBobina = getBobinaNumber(formData.id_bobina);\n      // Trova i dettagli della bobina selezionata\n      bobinaInfo = bobine.find(b => b.id_bobina === formData.id_bobina);\n    }\n\n    // Determina lo stato di installazione\n    const statoInstallazione = determineInstallationStatus(parseFloat(formData.metri_posati), selectedCavo.metri_teorici);\n\n    return (\n      <Box>\n        <Typography variant=\"h6\" gutterBottom>\n          Conferma inserimento\n        </Typography>\n\n        <Paper sx={{ p: 3 }}>\n          <Typography variant=\"subtitle1\" gutterBottom>\n            Riepilogo dati\n          </Typography>\n\n          {/* Dettagli del cavo */}\n          <CavoDetailsView\n            cavo={selectedCavo}\n            compact={true}\n            title=\"Dettagli del cavo\"\n          />\n\n          {/* Informazioni sull'operazione */}\n          <Box sx={{ mt: 3, p: 2, bgcolor: '#f5f5f5', borderRadius: 1 }}>\n            <Typography variant=\"subtitle2\" gutterBottom sx={{ fontWeight: 'bold' }}>\n              Informazioni sull'operazione:\n            </Typography>\n            <Grid container spacing={2}>\n              <Grid item xs={12} md={6}>\n                <Typography variant=\"body2\">\n                  <strong>Metri Posati:</strong> {formData.metri_posati} m\n                </Typography>\n                <Typography variant=\"body2\">\n                  <strong>Stato Installazione:</strong> {statoInstallazione}\n                </Typography>\n              </Grid>\n              <Grid item xs={12} md={6}>\n                <Typography variant=\"body2\">\n                  <strong>Bobina Associata:</strong> {numeroBobina}\n                </Typography>\n                {bobinaInfo && (\n                  <Typography variant=\"body2\">\n                    <strong>Metri Residui Bobina:</strong> {bobinaInfo.metri_residui} m\n                  </Typography>\n                )}\n              </Grid>\n            </Grid>\n          </Box>\n\n          {bobinaInfo && parseFloat(formData.metri_posati) > parseFloat(bobinaInfo.metri_residui) && !notificationShown && (\n            <Alert severity=\"warning\" sx={{ mt: 3 }}>\n              <strong>Attenzione:</strong> I metri posati ({formData.metri_posati}m) superano i metri residui della bobina ({bobinaInfo.metri_residui}m).\n              Questo porterà la bobina in stato OVER.\n            </Alert>\n          )}\n\n          <Alert severity=\"info\" sx={{ mt: 3 }}>\n            Conferma per aggiornare i metri posati e lo stato di installazione del cavo.\n            {formData.id_bobina && formData.id_bobina !== 'BOBINA_VUOTA' && ' I metri posati verranno sottratti dai metri residui della bobina selezionata.'}\n          </Alert>\n        </Paper>\n      </Box>\n    );\n  };\n\n  // Renderizza il contenuto in base al passo attivo\n  const getStepContent = (step) => {\n    switch (step) {\n      case 0:\n        return renderStep1(); // Seleziona Cavo\n      case 1:\n        return renderStep3(); // Associa Bobina\n      case 2:\n        return renderStep2(); // Inserisci Metri\n      case 3:\n        return renderStep4(); // Conferma\n      default:\n        return 'Passo sconosciuto';\n    }\n  };\n\n  // Gestisce la chiusura del dialogo per cavi già posati\n  const handleCloseAlreadyLaidDialog = () => {\n    setShowAlreadyLaidDialog(false);\n    setAlreadyLaidCavo(null);\n  };\n\n\n\n  // Gestisce l'opzione di modificare la bobina di un cavo già posato\n  const handleModifyReel = () => {\n    if (alreadyLaidCavo) {\n      navigate(`/dashboard/cavi/modifica-bobina/${cantiereId}/${alreadyLaidCavo.id_cavo}`);\n    }\n    handleCloseAlreadyLaidDialog();\n  };\n\n  // Gestisce l'opzione di selezionare un altro cavo\n  const handleSelectAnotherCable = () => {\n    handleCloseAlreadyLaidDialog();\n    // Reset del form per selezionare un nuovo cavo\n    setSelectedCavo(null);\n    setCavoIdInput('');\n    setShowSearchResults(false);\n  };\n\n  // Gestisce la chiusura del dialogo per bobine incompatibili\n  const handleCloseIncompatibleReelDialog = () => {\n    setShowIncompatibleReelDialog(false);\n    setIncompatibleReelData({ cavo: null, bobina: null });\n  };\n\n  // Gestisce l'aggiornamento delle caratteristiche del cavo per farle corrispondere a quelle della bobina\n  const handleUpdateCavoToMatchReel = async () => {\n    const { cavo, bobina } = incompatibleReelData;\n    if (!cavo || !bobina) {\n      console.error('Dati mancanti per aggiornare il cavo:', { cavo, bobina });\n      onError('Dati mancanti per aggiornare il cavo');\n      return;\n    }\n\n    try {\n      setLoading(true);\n      console.log(`Aggiornamento caratteristiche del cavo ${cavo.id_cavo} per compatibilità con bobina ${bobina.id_bobina}`);\n      console.log('Dati cavo prima dell\\'aggiornamento:', cavo);\n      console.log('Dati bobina:', bobina);\n\n      // Aggiorna le caratteristiche del cavo\n      await caviService.updateCavoForCompatibility(cantiereId, cavo.id_cavo, bobina.id_bobina);\n\n      // Aggiorna il cavo selezionato con le nuove caratteristiche\n      const updatedCavo = await caviService.getCavoById(cantiereId, cavo.id_cavo);\n      console.log('Dati cavo dopo l\\'aggiornamento:', updatedCavo);\n      setSelectedCavo(updatedCavo);\n\n      // Imposta la bobina selezionata\n      setFormData({\n        ...formData,\n        id_bobina: bobina.id_bobina\n      });\n\n      // Ricarica le bobine per aggiornare l'interfaccia\n      await loadBobine();\n\n      onSuccess(`Caratteristiche del cavo ${cavo.id_cavo} aggiornate per corrispondere alla bobina ${bobina.id_bobina}`);\n      handleCloseIncompatibleReelDialog();\n    } catch (error) {\n      console.error('Errore durante l\\'aggiornamento delle caratteristiche del cavo:', error);\n      onError('Errore durante l\\'aggiornamento delle caratteristiche del cavo: ' + (error.detail || error.message || 'Errore sconosciuto'));\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  // Gestisce la selezione di un'altra bobina\n  const handleSelectAnotherReel = () => {\n    handleCloseIncompatibleReelDialog();\n    // Reset della bobina selezionata\n    setFormData({\n      ...formData,\n      id_bobina: ''\n    });\n  };\n\n  return (\n    <Box>\n      {/* Sezione di ricerca */}\n      <Paper sx={{ p: 3, mb: 3 }}>\n        <Typography variant=\"h6\" gutterBottom>\n          Cerca cavo\n        </Typography>\n        <Grid container spacing={2} alignItems=\"center\">\n          <Grid item xs={9}>\n            <TextField\n              fullWidth\n              label=\"ID Cavo\"\n              variant=\"outlined\"\n              value={cavoIdInput}\n              onChange={(e) => setCavoIdInput(e.target.value)}\n              placeholder=\"Inserisci l'ID del cavo o parte di esso\"\n            />\n          </Grid>\n          <Grid item xs={3}>\n            <Button\n              fullWidth\n              variant=\"contained\"\n              color=\"primary\"\n              onClick={handleSearchCavoById}\n              disabled={caviLoading || !cavoIdInput.trim()}\n              startIcon={caviLoading ? <CircularProgress size={20} /> : <SearchIcon />}\n            >\n              Cerca\n            </Button>\n          </Grid>\n        </Grid>\n      </Paper>\n\n      {/* Risultati della ricerca */}\n      {showSearchResults && searchResults.length > 0 && (\n        <Paper sx={{ p: 3, mb: 3 }}>\n          <Typography variant=\"h6\" gutterBottom>\n            Risultati della ricerca\n          </Typography>\n          <TableContainer>\n            <Table size=\"small\">\n              <TableHead>\n                <TableRow sx={{ bgcolor: '#f5f5f5' }}>\n                  <TableCell>ID Cavo</TableCell>\n                  <TableCell>Tipologia</TableCell>\n                  <TableCell>Formazione</TableCell>\n                  <TableCell>Ubicazione</TableCell>\n                  <TableCell>Metri Teorici</TableCell>\n                  <TableCell>Stato</TableCell>\n                  <TableCell>Azioni</TableCell>\n                </TableRow>\n              </TableHead>\n              <TableBody>\n                {searchResults.map((cavo) => (\n                  <TableRow key={cavo.id_cavo}>\n                    <TableCell>{cavo.id_cavo}</TableCell>\n                    <TableCell>{cavo.tipologia || 'N/A'}</TableCell>\n                    <TableCell>{cavo.sezione || 'N/A'}</TableCell>\n                    <TableCell>Da: {cavo.ubicazione_partenza || 'N/A'}<br/>A: {cavo.ubicazione_arrivo || 'N/A'}</TableCell>\n                    <TableCell>{cavo.metri_teorici || 'N/A'} m</TableCell>\n                    <TableCell>\n                      <Chip\n                        label={cavo.stato_installazione || 'N/D'}\n                        size=\"small\"\n                        color={getCableStateColor(cavo.stato_installazione)}\n                        variant=\"outlined\"\n                      />\n                    </TableCell>\n                    <TableCell>\n                      <Button\n                        size=\"small\"\n                        variant=\"contained\"\n                        color=\"primary\"\n                        onClick={() => handleCavoSelect(cavo)}\n                        disabled={isCableInstalled(cavo)}\n                      >\n                        Seleziona\n                      </Button>\n                    </TableCell>\n                  </TableRow>\n                ))}\n              </TableBody>\n            </Table>\n          </TableContainer>\n        </Paper>\n      )}\n\n      {/* Form per inserimento metri e selezione bobina */}\n      {selectedCavo && (\n        <Paper sx={{ p: 3 }}>\n          <Typography variant=\"h6\" gutterBottom>\n            Inserimento metri posati\n          </Typography>\n\n          {/* Dettagli del cavo selezionato */}\n          <Box sx={{ p: 2, bgcolor: '#f5f5f5', borderRadius: 1, mb: 3 }}>\n            <Typography variant=\"subtitle2\" gutterBottom sx={{ fontWeight: 'bold', color: 'primary.main' }}>\n              Cavo selezionato: {selectedCavo.id_cavo}\n            </Typography>\n            <Grid container spacing={2}>\n              <Grid item xs={12} md={6}>\n                <Typography variant=\"body2\"><strong>Tipologia:</strong> {selectedCavo.tipologia || 'N/A'}</Typography>\n                <Typography variant=\"body2\"><strong>Formazione:</strong> {selectedCavo.sezione || 'N/A'}</Typography>\n                <Typography variant=\"body2\"><strong>Metri teorici:</strong> {selectedCavo.metri_teorici || 'N/A'} m</Typography>\n              </Grid>\n              <Grid item xs={12} md={6}>\n                <Typography variant=\"body2\"><strong>Ubicazione partenza:</strong> {selectedCavo.ubicazione_partenza || 'N/A'}</Typography>\n                <Typography variant=\"body2\"><strong>Ubicazione arrivo:</strong> {selectedCavo.ubicazione_arrivo || 'N/A'}</Typography>\n                <Typography variant=\"body2\">\n                  <strong>Stato:</strong>\n                  <Chip\n                    label={selectedCavo.stato_installazione || 'N/D'}\n                    size=\"small\"\n                    color={getCableStateColor(selectedCavo.stato_installazione)}\n                    variant=\"outlined\"\n                    sx={{ ml: 1 }}\n                  />\n                </Typography>\n              </Grid>\n            </Grid>\n          </Box>\n\n          <Grid container spacing={3}>\n            {/* Colonna sinistra: Metri posati */}\n            <Grid item xs={12} md={6}>\n              <Typography variant=\"subtitle2\" gutterBottom sx={{ fontWeight: 'bold' }}>\n                Metratura posata\n              </Typography>\n              <TextField\n                size=\"small\"\n                fullWidth\n                label=\"Metri posati\"\n                variant=\"outlined\"\n                name=\"metri_posati\"\n                type=\"number\"\n                value={formData.metri_posati}\n                onChange={handleFormChange}\n                error={!!formErrors.metri_posati}\n                helperText={formErrors.metri_posati || formWarnings.metri_posati}\n                FormHelperTextProps={{\n                  sx: { color: formWarnings.metri_posati && !formErrors.metri_posati ? 'warning.main' : 'error.main' }\n                }}\n                sx={{ mb: 1 }}\n              />\n              {formWarnings.metri_posati && !formErrors.metri_posati && (\n                <Alert severity=\"warning\" sx={{ mb: 2 }}>\n                  {formWarnings.metri_posati}\n                </Alert>\n              )}\n            </Grid>\n\n            {/* Colonna destra: Selezione bobina */}\n            <Grid item xs={12} md={6}>\n              <Typography variant=\"subtitle2\" gutterBottom sx={{ fontWeight: 'bold' }}>\n                Associa bobina\n              </Typography>\n              {bobineLoading ? (\n                <Box sx={{ display: 'flex', justifyContent: 'center', my: 2 }}>\n                  <CircularProgress />\n                </Box>\n              ) : (\n                <Box>\n                  <Typography variant=\"subtitle2\" gutterBottom sx={{ color: 'primary.main', fontWeight: 'bold', mb: 1 }}>\n                    {selectedCavo ? 'Bobine compatibili con il cavo selezionato' : 'Seleziona una bobina'}\n                  </Typography>\n\n                  <FormControl fullWidth size=\"small\" error={!!formErrors.id_bobina}>\n                    <InputLabel id=\"bobina-select-label\">Seleziona bobina</InputLabel>\n                    <Select\n                      labelId=\"bobina-select-label\"\n                      id=\"bobina-select\"\n                      name=\"id_bobina\"\n                      value={formData.id_bobina}\n                      label=\"Seleziona bobina\"\n                      onChange={handleFormChange}\n                    >\n                      <MenuItem value=\"BOBINA_VUOTA\">\n                        <strong>BOBINA VUOTA</strong> (nessuna bobina associata)\n                      </MenuItem>\n                      <Divider />\n                      {bobine.length > 0 ? (\n                        <Box component=\"li\" sx={{ p: 1, bgcolor: 'background.paper' }}>\n                          <Typography variant=\"caption\" sx={{ fontWeight: 'bold', color: 'success.main' }}>\n                            {bobine.length} bobine disponibili\n                            {selectedCavo && (\n                              <> -\n                                <span style={{ color: bobine.some(bobina =>\n                                  bobina.tipologia === selectedCavo.tipologia &&\n                                  String(bobina.sezione) === String(selectedCavo.sezione)) ? 'green' : 'orange' }}>\n                                  {bobine.filter(bobina =>\n                                    bobina.tipologia === selectedCavo.tipologia &&\n                                    String(bobina.sezione) === String(selectedCavo.sezione)\n                                  ).length} compatibili\n                                </span>\n                              </>\n                            )}\n                          </Typography>\n                        </Box>\n                      ) : null}\n                      {bobine.map((bobina) => (\n                        <MenuItem\n                          key={bobina.id_bobina}\n                          value={bobina.id_bobina}\n                          disabled={bobina.metri_residui < parseFloat(formData.metri_posati || 0)}\n                          sx={{\n                            '&.Mui-selected': { bgcolor: 'success.light' },\n                            '&.Mui-selected:hover': { bgcolor: 'success.light' },\n                            bgcolor: selectedCavo &&\n                                   bobina.tipologia === selectedCavo.tipologia &&\n                                   String(bobina.sezione) === String(selectedCavo.sezione) ?\n                                   'rgba(76, 175, 80, 0.12)' : 'inherit',\n                            border: selectedCavo &&\n                                   bobina.tipologia === selectedCavo.tipologia &&\n                                   String(bobina.sezione) === String(selectedCavo.sezione) ?\n                                   '1px solid #4caf50' : 'none',\n                            borderRadius: '4px',\n                            margin: '2px 0'\n                          }}\n                        >\n                          <Box sx={{ display: 'flex', flexDirection: 'column', width: '100%' }}>\n                            <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', width: '100%' }}>\n                              <Typography variant=\"body2\" sx={{ fontWeight: 'bold' }}>\n                                {getBobinaNumber(bobina.id_bobina)} - {bobina.tipologia || 'N/A'}\n                              </Typography>\n                              {selectedCavo &&\n                               bobina.tipologia === selectedCavo.tipologia &&\n                               String(bobina.sezione) === String(selectedCavo.sezione) && (\n                                <Chip\n                                  size=\"small\"\n                                  label=\"Compatibile\"\n                                  color=\"success\"\n                                  variant=\"outlined\"\n                                  sx={{ height: 20, fontSize: '0.6rem' }}\n                                />\n                              )}\n                            </Box>\n                            <Box sx={{ display: 'flex', justifyContent: 'space-between', width: '100%' }}>\n                              <Typography variant=\"caption\">\n                                {bobina.sezione || 'N/A'}\n                              </Typography>\n                              <Typography variant=\"caption\" sx={{ fontWeight: 'bold', color: bobina.metri_residui < parseFloat(formData.metri_posati || 0) ? 'error.main' : 'success.main' }}>\n                                {bobina.metri_residui || 0} m disponibili\n                              </Typography>\n                            </Box>\n                          </Box>\n                        </MenuItem>\n                      ))}\n                    </Select>\n                    <FormHelperText>\n                      {formErrors.id_bobina || 'È necessario selezionare una bobina o \"BOBINA VUOTA\"'}\n                    </FormHelperText>\n                  </FormControl>\n\n                  {bobine.length === 0 && !bobineLoading && (\n                    <Alert severity=\"warning\" sx={{ mt: 2, fontSize: '0.8rem' }}>\n                      Nessuna bobina disponibile trovata. Puoi usare BOBINA VUOTA.\n                    </Alert>\n                  )}\n\n                  {bobine.length > 0 && selectedCavo && !bobine.some(bobina =>\n                    // Nota: n_conduttori non è più utilizzato per la compatibilità\n                    bobina.tipologia === selectedCavo.tipologia &&\n                    String(bobina.sezione) === String(selectedCavo.sezione)\n                  ) && (\n                    <Alert severity=\"info\" sx={{ mt: 2, fontSize: '0.8rem' }}>\n                      <strong>Nessuna bobina compatibile trovata.</strong> Hai due opzioni:\n                      <ul style={{ margin: '5px 0', paddingLeft: '20px' }}>\n                        <li>Usa \"BOBINA VUOTA\" (potrai associare una bobina in seguito)</li>\n                        <li>Seleziona una bobina non compatibile (ti verrà chiesto se vuoi aggiornare le caratteristiche del cavo)</li>\n                      </ul>\n                    </Alert>\n                  )}\n                </Box>\n              )}\n\n              {/* Mostra dettagli della bobina selezionata */}\n              {!bobineLoading && formData.id_bobina && formData.id_bobina !== 'BOBINA_VUOTA' && (() => {\n                const bobina = bobine.find(b => b.id_bobina === formData.id_bobina);\n                if (bobina) {\n                  return (\n                    <Box sx={{ mt: 2, p: 2, bgcolor: '#f5f5f5', borderRadius: 1 }}>\n                      <Typography variant=\"body2\"><strong>Bobina:</strong> {getBobinaNumber(bobina.id_bobina)}</Typography>\n                      <Typography variant=\"body2\"><strong>Metri residui:</strong> {bobina.metri_residui || 0} m</Typography>\n                      <Typography variant=\"body2\">\n                        <strong>Stato:</strong>\n                        <Chip\n                          label={bobina.stato_bobina || 'N/D'}\n                          size=\"small\"\n                          color={getReelStateColor(bobina.stato_bobina)}\n                          variant=\"outlined\"\n                          sx={{ ml: 1 }}\n                        />\n                      </Typography>\n                    </Box>\n                  );\n                }\n                return null;\n              })()}\n            </Grid>\n          </Grid>\n\n          <Box sx={{ mt: 2, display: 'flex', justifyContent: 'flex-end', gap: 1 }}>\n            <Button\n              variant=\"outlined\"\n              color=\"secondary\"\n              onClick={() => {\n                setSelectedCavo(null);\n                setFormData({\n                  id_cavo: '',\n                  metri_posati: '',\n                  id_bobina: ''\n                });\n              }}\n              startIcon={<CancelIcon fontSize=\"small\" />}\n              disabled={loading}\n              size=\"small\"\n              sx={{ minWidth: '80px', height: '32px' }}\n            >\n              ANNULLA\n            </Button>\n\n            <Button\n              variant=\"contained\"\n              color=\"primary\"\n              onClick={handleSubmit}\n              endIcon={loading ? undefined : <SaveIcon fontSize=\"small\" />}\n              disabled={loading || !formData.metri_posati || !formData.id_bobina}\n              size=\"small\"\n              sx={{ minWidth: '80px', height: '32px' }}\n            >\n              {loading ? <CircularProgress size={16} /> : 'SALVA'}\n            </Button>\n          </Box>\n        </Paper>\n      )}\n\n      {/* Dialogo di conferma generico */}\n      <Dialog open={showConfirmDialog} onClose={() => setShowConfirmDialog(false)} maxWidth=\"sm\" fullWidth>\n        <DialogTitle sx={{ bgcolor: 'warning.light' }}>\n          <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>\n            <WarningIcon color=\"warning\" />\n            <Typography variant=\"h6\">{confirmDialogProps.title}</Typography>\n          </Box>\n        </DialogTitle>\n        <DialogContent>\n          <Typography variant=\"body1\" sx={{ mt: 2 }}>\n            {confirmDialogProps.message}\n          </Typography>\n        </DialogContent>\n        <DialogActions>\n          <Button onClick={() => setShowConfirmDialog(false)} color=\"secondary\" variant=\"outlined\">\n            Annulla\n          </Button>\n          <Button\n            onClick={() => {\n              setShowConfirmDialog(false);\n              confirmDialogProps.onConfirm();\n            }}\n            color=\"primary\"\n            variant=\"contained\"\n            autoFocus\n          >\n            Conferma\n          </Button>\n        </DialogActions>\n      </Dialog>\n\n      {/* Dialogo per cavi già posati */}\n      <Dialog open={showAlreadyLaidDialog} onClose={handleCloseAlreadyLaidDialog} maxWidth=\"sm\" fullWidth>\n        <DialogTitle sx={{ bgcolor: 'warning.light' }}>\n          <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>\n            <WarningIcon color=\"warning\" />\n            <Typography variant=\"h6\">Cavo già posato</Typography>\n          </Box>\n        </DialogTitle>\n        <DialogContent>\n          {alreadyLaidCavo && (\n            <Box sx={{ mt: 2 }}>\n              <Typography variant=\"body1\" paragraph>\n                Il cavo <strong>{alreadyLaidCavo.id_cavo}</strong> risulta già posato ({alreadyLaidCavo.metratura_reale || 0}m).\n              </Typography>\n              <Typography variant=\"body1\" paragraph>\n                Puoi scegliere di:\n              </Typography>\n              <Typography variant=\"body2\" component=\"ul\">\n                <li>Modificare la bobina associata al cavo</li>\n                <li>Selezionare un altro cavo</li>\n                <li>Annullare l'operazione</li>\n              </Typography>\n            </Box>\n          )}\n        </DialogContent>\n        <DialogActions sx={{ p: 2, justifyContent: 'space-between' }}>\n          <Button onClick={handleCloseAlreadyLaidDialog} color=\"secondary\">\n            Annulla operazione\n          </Button>\n          <Box>\n            <Button onClick={handleSelectAnotherCable} color=\"primary\" sx={{ mr: 1 }}>\n              Seleziona altro cavo\n            </Button>\n            <Button onClick={handleModifyReel} variant=\"contained\" color=\"primary\">\n              Modifica bobina\n            </Button>\n          </Box>\n        </DialogActions>\n      </Dialog>\n\n      {/* Dialogo per bobine incompatibili */}\n      <IncompatibleReelDialog\n        open={showIncompatibleReelDialog}\n        onClose={handleCloseIncompatibleReelDialog}\n        cavo={incompatibleReelData.cavo}\n        bobina={incompatibleReelData.bobina}\n        onUpdateCavo={handleUpdateCavoToMatchReel}\n        onSelectAnotherReel={handleSelectAnotherReel}\n      />\n\n      {/* Dialogo per visualizzare i dettagli del cavo */}\n      <Dialog\n        open={showCavoDetailsDialog}\n        onClose={() => setShowCavoDetailsDialog(false)}\n        maxWidth=\"md\"\n        fullWidth\n      >\n        <DialogTitle>\n          <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>\n            <InfoIcon color=\"primary\" />\n            <Typography variant=\"h6\">Dettagli Cavo</Typography>\n          </Box>\n        </DialogTitle>\n        <DialogContent>\n          <CavoDetailsView cavo={selectedCavo} />\n        </DialogContent>\n        <DialogActions>\n          <Button onClick={() => setShowCavoDetailsDialog(false)} color=\"primary\">\n            Chiudi\n          </Button>\n        </DialogActions>\n      </Dialog>\n    </Box>\n  );\n};\n\nexport default InserisciMetriForm;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAClD,OAAOC,KAAK,MAAM,OAAO;AACzB,SACEC,GAAG,EACHC,KAAK,EACLC,UAAU,EACVC,SAAS,EACTC,MAAM,EACNC,IAAI,EACJC,WAAW,EACXC,UAAU,EACVC,MAAM,EACNC,QAAQ,EACRC,OAAO,EACPC,KAAK,EACLC,gBAAgB,EAChBC,cAAc,EACdC,UAAU,EACVC,IAAI,EACJC,MAAM,EACNC,WAAW,EACXC,aAAa,EACbC,aAAa,EACbC,KAAK,EACLC,SAAS,EACTC,SAAS,EACTC,cAAc,EACdC,SAAS,EACTC,QAAQ,EACRC,IAAI,EACJC,QAAQ,EACRC,YAAY,EACZC,uBAAuB,QAClB,eAAe;AACtB,SACEC,MAAM,IAAIC,UAAU,EACpBC,IAAI,IAAIC,QAAQ,EAChBC,MAAM,IAAIC,UAAU,EACpBC,OAAO,IAAIC,WAAW,EACtBC,IAAI,IAAIC,QAAQ,QACX,qBAAqB;AAC5B,SAASC,WAAW,QAAQ,kBAAkB;AAC9C,OAAOC,WAAW,MAAM,4BAA4B;AACpD,OAAOC,aAAa,MAAM,4BAA4B;AACtD,OAAOC,sBAAsB,MAAM,0BAA0B;AAC7D,OAAOC,eAAe,MAAM,mBAAmB;AAC/C,SACEC,YAAY,EACZC,WAAW,EACXC,mBAAmB,EACnBC,kBAAkB,EAClBC,cAAc,EACdC,YAAY,EACZC,gBAAgB,EAChBC,kBAAkB,EAClBC,iBAAiB,QACZ,wBAAwB;AAC/B,OAAOC,gBAAgB,MAAM,iCAAiC;AAC9D,SAASC,wBAAwB,QAAQ,6BAA6B;;AAEtE;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AARA,SAAAC,MAAA,IAAAC,OAAA,EAAAC,QAAA,IAAAC,SAAA;AASA,MAAMC,kBAAkB,GAAGA,CAAC;EAAEC,UAAU;EAAEC,SAAS;EAAEC;AAAQ,CAAC,KAAK;EAAAC,EAAA;EACjE,MAAMC,QAAQ,GAAGzB,WAAW,CAAC,CAAC;;EAE9B;EACA,MAAM,CAAC0B,OAAO,EAAEC,UAAU,CAAC,GAAGtE,QAAQ,CAAC,KAAK,CAAC;EAC7C,MAAM,CAACuE,WAAW,EAAEC,cAAc,CAAC,GAAGxE,QAAQ,CAAC,KAAK,CAAC;EACrD,MAAM,CAACyE,aAAa,EAAEC,gBAAgB,CAAC,GAAG1E,QAAQ,CAAC,KAAK,CAAC;EACzD,MAAM,CAAC2E,aAAa,EAAEC,gBAAgB,CAAC,GAAG5E,QAAQ,CAAC,EAAE,CAAC;EACtD,MAAM,CAAC6E,iBAAiB,EAAEC,oBAAoB,CAAC,GAAG9E,QAAQ,CAAC,KAAK,CAAC;;EAEjE;EACA,MAAM,CAAC+E,IAAI,EAAEC,OAAO,CAAC,GAAGhF,QAAQ,CAAC,EAAE,CAAC;EACpC,MAAM,CAACiF,MAAM,EAAEC,SAAS,CAAC,GAAGlF,QAAQ,CAAC,EAAE,CAAC;EACxC,MAAM,CAACmF,YAAY,EAAEC,eAAe,CAAC,GAAGpF,QAAQ,CAAC,IAAI,CAAC;EACtD,MAAM,CAACqF,WAAW,EAAEC,cAAc,CAAC,GAAGtF,QAAQ,CAAC,EAAE,CAAC;;EAElD;EACA,MAAM,CAACuF,UAAU,EAAEC,aAAa,CAAC,GAAGxF,QAAQ,CAAC,CAAC,CAAC;;EAE/C;EACA,MAAM,CAACyF,QAAQ,EAAEC,WAAW,CAAC,GAAG1F,QAAQ,CAAC;IACvC2F,OAAO,EAAE,EAAE;IACXC,YAAY,EAAE,EAAE;IAChBC,SAAS,EAAE;EACb,CAAC,CAAC;;EAEF;EACA,MAAM,CAACC,UAAU,EAAEC,aAAa,CAAC,GAAG/F,QAAQ,CAAC,CAAC,CAAC,CAAC;EAChD,MAAM,CAACgG,YAAY,EAAEC,eAAe,CAAC,GAAGjG,QAAQ,CAAC,CAAC,CAAC,CAAC;;EAEpD;EACA,MAAM,CAACkG,0BAA0B,EAAEC,6BAA6B,CAAC,GAAGnG,QAAQ,CAAC,KAAK,CAAC;EACnF,MAAM,CAACoG,gBAAgB,EAAEC,mBAAmB,CAAC,GAAGrG,QAAQ,CAAC,IAAI,CAAC,CAAC,CAAC;EAChE,MAAM,CAACsG,oBAAoB,EAAEC,uBAAuB,CAAC,GAAGvG,QAAQ,CAAC;IAAEwG,IAAI,EAAE,IAAI;IAAEC,MAAM,EAAE;EAAK,CAAC,CAAC;EAC9F,MAAM,CAACC,qBAAqB,EAAEC,wBAAwB,CAAC,GAAG3G,QAAQ,CAAC,KAAK,CAAC;EACzE,MAAM,CAAC4G,eAAe,EAAEC,kBAAkB,CAAC,GAAG7G,QAAQ,CAAC,IAAI,CAAC;EAC5D,MAAM,CAAC8G,qBAAqB,EAAEC,wBAAwB,CAAC,GAAG/G,QAAQ,CAAC,KAAK,CAAC;;EAEzE;EACAC,SAAS,CAAC,MAAM;IACd+G,UAAU,CAAC,CAAC;EACd,CAAC,EAAE,CAAChD,UAAU,CAAC,CAAC;;EAEhB;EACA,MAAMiD,eAAe,GAAIC,QAAQ,IAAK;IACpC;IACA,IAAIA,QAAQ,IAAIA,QAAQ,CAACC,QAAQ,CAAC,IAAI,CAAC,EAAE;MACvC,OAAOD,QAAQ,CAACE,KAAK,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;IAChC;IACA,OAAOF,QAAQ;EACjB,CAAC;;EAED;EACA,MAAMG,QAAQ,GAAG,MAAAA,CAAA,KAAY;IAC3B,IAAI;MACF7C,cAAc,CAAC,IAAI,CAAC;MACpB8C,OAAO,CAACC,GAAG,CAAC,oCAAoCvD,UAAU,KAAK,CAAC;;MAEhE;MACA,IAAI;QACF,MAAMwD,QAAQ,GAAG,MAAM5E,WAAW,CAAC6E,OAAO,CAACzD,UAAU,CAAC;QACtDsD,OAAO,CAACC,GAAG,CAAC,YAAYC,QAAQ,CAACE,MAAM,OAAO,CAAC;;QAE/C;QACA;QACA1C,OAAO,CAACwC,QAAQ,CAAC;MACnB,CAAC,CAAC,OAAOG,SAAS,EAAE;QAClBL,OAAO,CAACM,KAAK,CAAC,qDAAqD,EAAED,SAAS,CAAC;;QAE/E;QACA,IAAIA,SAAS,CAACE,cAAc,IAAI,CAACF,SAAS,CAACG,QAAQ,IAC/CH,SAAS,CAACI,IAAI,KAAK,cAAc,IAChCJ,SAAS,CAACK,OAAO,IAAIL,SAAS,CAACK,OAAO,CAACb,QAAQ,CAAC,eAAe,CAAE,EAAE;UAEtEG,OAAO,CAACC,GAAG,CAAC,yDAAyD,CAAC;UACtE,MAAM,IAAIU,OAAO,CAACC,OAAO,IAAIC,UAAU,CAACD,OAAO,EAAE,IAAI,CAAC,CAAC;UAEvD,IAAI;YACF;YACA,MAAME,KAAK,GAAGC,YAAY,CAACC,OAAO,CAAC,OAAO,CAAC;YAC3C,MAAMC,OAAO,GAAG1F,aAAa,CAAC2F,QAAQ,CAACC,OAAO;;YAE9C;YACA,MAAMC,aAAa,GAAG,MAAMxI,KAAK,CAACyI,GAAG,CACnC,GAAGJ,OAAO,SAASvE,UAAU,EAAE,EAC/B;cACE4E,OAAO,EAAE;gBACP,eAAe,EAAE,UAAUR,KAAK;cAClC,CAAC;cACDS,OAAO,EAAE,KAAK,CAAC;YACjB,CACF,CAAC;YAEDvB,OAAO,CAACC,GAAG,CAAC,2CAA2CmB,aAAa,CAACI,IAAI,CAACpB,MAAM,OAAO,CAAC;YACxF1C,OAAO,CAAC0D,aAAa,CAACI,IAAI,CAAC;UAC7B,CAAC,CAAC,OAAOC,UAAU,EAAE;YACnBzB,OAAO,CAACM,KAAK,CAAC,uCAAuC,EAAEmB,UAAU,CAAC;YAClE,MAAMA,UAAU;UAClB;QACF,CAAC,MAAM;UACL,MAAMpB,SAAS;QACjB;MACF;IACF,CAAC,CAAC,OAAOC,KAAK,EAAE;MACdN,OAAO,CAACM,KAAK,CAAC,kCAAkC,EAAEA,KAAK,CAAC;;MAExD;MACA,IAAIoB,YAAY,GAAG,iCAAiC;MAEpD,IAAIpB,KAAK,CAACC,cAAc,EAAE;QACxBmB,YAAY,GAAG,8DAA8D;MAC/E,CAAC,MAAM,IAAIpB,KAAK,CAACqB,MAAM,EAAE;QACvBD,YAAY,GAAGpB,KAAK,CAACqB,MAAM;MAC7B,CAAC,MAAM,IAAIrB,KAAK,CAACI,OAAO,EAAE;QACxBgB,YAAY,GAAGpB,KAAK,CAACI,OAAO;MAC9B;MAEA9D,OAAO,CAAC8E,YAAY,CAAC;IACvB,CAAC,SAAS;MACRxE,cAAc,CAAC,KAAK,CAAC;IACvB;EACF,CAAC;;EAED;EACA,MAAMwC,UAAU,GAAG,MAAAA,CAAA,KAAY;IAC7B,IAAI;MACFtC,gBAAgB,CAAC,IAAI,CAAC;MACtB4C,OAAO,CAACC,GAAG,CAAC,gCAAgC,CAAC;;MAE7C;MACA,MAAM2B,UAAU,GAAG,MAAMzF,gBAAgB,CAAC0F,SAAS,CAACnF,UAAU,CAAC;MAC/DsD,OAAO,CAACC,GAAG,CAAC,oBAAoB2B,UAAU,CAACxB,MAAM,EAAE,CAAC;;MAEpD;MACA,MAAM0B,kBAAkB,GAAGF,UAAU,CAACG,MAAM,CAAC5C,MAAM,IACjDA,MAAM,CAAC6C,YAAY,KAAK,WAAW,IACnC7C,MAAM,CAAC6C,YAAY,KAAK,MAAM,IAC9B7C,MAAM,CAAC8C,aAAa,GAAG,CACzB,CAAC;MAEDjC,OAAO,CAACC,GAAG,CAAC,wBAAwB6B,kBAAkB,CAAC1B,MAAM,EAAE,CAAC;;MAEhE;MACA,IAAIvC,YAAY,IAAIA,YAAY,CAACqE,SAAS,IAAIrE,YAAY,CAACsE,OAAO,EAAE;QAClEnC,OAAO,CAACC,GAAG,CAAC,sDAAsD,CAAC;QACnED,OAAO,CAACC,GAAG,CAAC,YAAY,EAAE;UACxB5B,OAAO,EAAER,YAAY,CAACQ,OAAO;UAC7B6D,SAAS,EAAErE,YAAY,CAACqE,SAAS;UACjCC,OAAO,EAAEtE,YAAY,CAACsE;QACxB,CAAC,CAAC;;QAEF;QACA,MAAMC,aAAa,GAAGC,MAAM,CAACxE,YAAY,CAACqE,SAAS,IAAI,EAAE,CAAC,CAACI,IAAI,CAAC,CAAC,CAACC,WAAW,CAAC,CAAC;QAC/E,MAAMC,WAAW,GAAGH,MAAM,CAACxE,YAAY,CAACsE,OAAO,IAAI,GAAG,CAAC,CAACG,IAAI,CAAC,CAAC;;QAE9D;QACA,MAAMG,iBAAiB,GAAG,EAAE;QAC5B,MAAMC,oBAAoB,GAAG,EAAE;;QAE/B;QACAZ,kBAAkB,CAACa,OAAO,CAACxD,MAAM,IAAI;UACnC,MAAMyD,eAAe,GAAGP,MAAM,CAAClD,MAAM,CAAC+C,SAAS,IAAI,EAAE,CAAC,CAACI,IAAI,CAAC,CAAC,CAACC,WAAW,CAAC,CAAC;UAC3E,MAAMM,aAAa,GAAGR,MAAM,CAAClD,MAAM,CAACgD,OAAO,IAAI,GAAG,CAAC,CAACG,IAAI,CAAC,CAAC;;UAE1D;UACA,MAAMQ,cAAc,GAAGF,eAAe,KAAKR,aAAa;UACxD,MAAMW,YAAY,GAAGF,aAAa,KAAKL,WAAW;UAClD,MAAMQ,YAAY,GAAGF,cAAc,IAAIC,YAAY;UAEnD/C,OAAO,CAACC,GAAG,CAAC,iCAAiCd,MAAM,CAACZ,SAAS,GAAG,EAAE;YAChE,kBAAkB,EAAE,IAAIY,MAAM,CAAC+C,SAAS,GAAG;YAC3C,gBAAgB,EAAE,IAAIrE,YAAY,CAACqE,SAAS,GAAG;YAC/C,mBAAmB,EAAEY,cAAc;YACnC,gBAAgB,EAAE,IAAIT,MAAM,CAAClD,MAAM,CAACgD,OAAO,CAAC,GAAG;YAC/C,cAAc,EAAE,IAAIE,MAAM,CAACxE,YAAY,CAACsE,OAAO,CAAC,GAAG;YACnD,iBAAiB,EAAEY,YAAY;YAC/B,cAAc,EAAE5D,MAAM,CAAC6C,YAAY;YACnC,eAAe,EAAE7C,MAAM,CAAC8C,aAAa;YACrC,cAAc,EAAEe;UAClB,CAAC,CAAC;UAEF,IAAIA,YAAY,EAAE;YAChBP,iBAAiB,CAACQ,IAAI,CAAC9D,MAAM,CAAC;UAChC,CAAC,MAAM;YACLuD,oBAAoB,CAACO,IAAI,CAAC9D,MAAM,CAAC;UACnC;QACF,CAAC,CAAC;QAEFa,OAAO,CAACC,GAAG,CAAC,+BAA+BwC,iBAAiB,CAACrC,MAAM,EAAE,CAAC;QACtEJ,OAAO,CAACC,GAAG,CAAC,2BAA2ByC,oBAAoB,CAACtC,MAAM,EAAE,CAAC;;QAErE;QACA,IAAIqC,iBAAiB,CAACrC,MAAM,GAAG,CAAC,EAAE;UAChCJ,OAAO,CAACC,GAAG,CAAC,+BAA+B,CAAC;UAC5CwC,iBAAiB,CAACE,OAAO,CAACxD,MAAM,IAAI;YAClCa,OAAO,CAACC,GAAG,CAAC,KAAKd,MAAM,CAACZ,SAAS,KAAKY,MAAM,CAAC+C,SAAS,MAAM/C,MAAM,CAACgD,OAAO,KAAKhD,MAAM,CAAC8C,aAAa,IAAI,CAAC;UAC1G,CAAC,CAAC;QACJ,CAAC,MAAM;UACLjC,OAAO,CAACC,GAAG,CAAC,iDAAiD,CAAC;QAChE;;QAEA;QACAwC,iBAAiB,CAACS,IAAI,CAAC,CAACC,CAAC,EAAEC,CAAC,KAAKA,CAAC,CAACnB,aAAa,GAAGkB,CAAC,CAAClB,aAAa,CAAC;QACnES,oBAAoB,CAACQ,IAAI,CAAC,CAACC,CAAC,EAAEC,CAAC,KAAKA,CAAC,CAACnB,aAAa,GAAGkB,CAAC,CAAClB,aAAa,CAAC;;QAEtE;QACA,MAAMoB,cAAc,GAAG,CAAC,GAAGZ,iBAAiB,EAAE,GAAGC,oBAAoB,CAAC;;QAEtE;QACA9E,SAAS,CAACyF,cAAc,CAAC;MAC3B,CAAC,MAAM;QACL;QACArD,OAAO,CAACC,GAAG,CAAC,uDAAuD,CAAC;QACpE6B,kBAAkB,CAACoB,IAAI,CAAC,CAACC,CAAC,EAAEC,CAAC,KAAKA,CAAC,CAACnB,aAAa,GAAGkB,CAAC,CAAClB,aAAa,CAAC;QACpErE,SAAS,CAACkE,kBAAkB,CAAC;MAC/B;IACF,CAAC,CAAC,OAAOxB,KAAK,EAAE;MACdN,OAAO,CAACM,KAAK,CAAC,sCAAsC,EAAEA,KAAK,CAAC;MAC5D1D,OAAO,CAAC,uCAAuC,IAAI0D,KAAK,CAACI,OAAO,IAAI,oBAAoB,CAAC,CAAC;IAC5F,CAAC,SAAS;MACRtD,gBAAgB,CAAC,KAAK,CAAC;IACzB;EACF,CAAC;;EAED;EACA,MAAMkG,oBAAoB,GAAG,MAAAA,CAAA,KAAY;IACvC,IAAI,CAACvF,WAAW,CAACuE,IAAI,CAAC,CAAC,EAAE;MACvB1F,OAAO,CAAC,6BAA6B,CAAC;MACtC;IACF;IAEA,IAAI;MACFM,cAAc,CAAC,IAAI,CAAC;MACpB8C,OAAO,CAACC,GAAG,CAAC,6BAA6BlC,WAAW,CAACuE,IAAI,CAAC,CAAC,iBAAiB5F,UAAU,EAAE,CAAC;;MAEzF;MACA,MAAMwD,QAAQ,GAAG,MAAM5E,WAAW,CAAC6E,OAAO,CAACzD,UAAU,CAAC;;MAEtD;MACA,MAAM6G,YAAY,GAAGrD,QAAQ,CAAC6B,MAAM,CAAC7C,IAAI,IACvCA,IAAI,CAACb,OAAO,CAACkE,WAAW,CAAC,CAAC,CAAC1C,QAAQ,CAAC9B,WAAW,CAACuE,IAAI,CAAC,CAAC,CAACC,WAAW,CAAC,CAAC,CACtE,CAAC;MAEDvC,OAAO,CAACC,GAAG,CAAC,WAAWsD,YAAY,CAACnD,MAAM,iCAAiC,CAAC;;MAE5E;MACA,MAAMoD,UAAU,GAAGD,YAAY,CAACE,IAAI,CAACvE,IAAI,IACvCA,IAAI,CAACb,OAAO,CAACkE,WAAW,CAAC,CAAC,KAAKxE,WAAW,CAACuE,IAAI,CAAC,CAAC,CAACC,WAAW,CAAC,CAChE,CAAC;MAED,IAAIiB,UAAU,EAAE;QACdxD,OAAO,CAACC,GAAG,CAAC,gCAAgC,EAAEuD,UAAU,CAAC;;QAEzD;QACA,IAAIA,UAAU,CAACE,mBAAmB,KAAK,YAAY,IAAKF,UAAU,CAACG,eAAe,IAAIH,UAAU,CAACG,eAAe,GAAG,CAAE,EAAE;UACrH3D,OAAO,CAACC,GAAG,CAAC,sCAAsC,EAAEuD,UAAU,CAAC;UAC/DjE,kBAAkB,CAACiE,UAAU,CAAC;UAC9BnE,wBAAwB,CAAC,IAAI,CAAC;UAC9BnC,cAAc,CAAC,KAAK,CAAC;UACrB;QACF;;QAEA;QACA,IAAIsG,UAAU,CAACI,sBAAsB,KAAK,CAAC,EAAE;UAC3C5D,OAAO,CAACC,GAAG,CAAC,qBAAqB,EAAEuD,UAAU,CAAC;UAC9C;QACF;;QAEA;QACAK,gBAAgB,CAACL,UAAU,CAAC;MAC9B,CAAC,MAAM,IAAID,YAAY,CAACnD,MAAM,GAAG,CAAC,EAAE;QAClC;QACA9C,gBAAgB,CAACiG,YAAY,CAAC;QAC9B/F,oBAAoB,CAAC,IAAI,CAAC;MAC5B,CAAC,MAAM;QACL;QACAZ,OAAO,CAAC,oCAAoCmB,WAAW,CAACuE,IAAI,CAAC,CAAC,kBAAkB5F,UAAU,EAAE,CAAC;MAC/F;IACF,CAAC,CAAC,OAAO4D,KAAK,EAAE;MACdN,OAAO,CAACM,KAAK,CAAC,gCAAgC,EAAEA,KAAK,CAAC;;MAEtD;MACA,IAAIoB,YAAY,GAAG,+BAA+B;MAElD,IAAIpB,KAAK,CAACC,cAAc,EAAE;QACxBmB,YAAY,GAAG,8DAA8D;MAC/E,CAAC,MAAM,IAAIpB,KAAK,CAACwD,MAAM,KAAK,GAAG,EAAE;QAC/BpC,YAAY,GAAG,gBAAgB3D,WAAW,CAACuE,IAAI,CAAC,CAAC,8BAA8B5F,UAAU,EAAE;MAC7F,CAAC,MAAM,IAAI4D,KAAK,CAACqB,MAAM,EAAE;QACvBD,YAAY,GAAGpB,KAAK,CAACqB,MAAM;MAC7B,CAAC,MAAM,IAAIrB,KAAK,CAACI,OAAO,EAAE;QACxBgB,YAAY,GAAGpB,KAAK,CAACI,OAAO;MAC9B;MAEA9D,OAAO,CAAC8E,YAAY,CAAC;IACvB,CAAC,SAAS;MACRxE,cAAc,CAAC,KAAK,CAAC;IACvB;EACF,CAAC;;EAED;EACA,MAAM2G,gBAAgB,GAAI3E,IAAI,IAAK;IACjC;IACA,IAAIA,IAAI,CAACwE,mBAAmB,KAAK,YAAY,IAAKxE,IAAI,CAACyE,eAAe,IAAIzE,IAAI,CAACyE,eAAe,GAAG,CAAE,EAAE;MACnG;MACApE,kBAAkB,CAACL,IAAI,CAAC;MACxBG,wBAAwB,CAAC,IAAI,CAAC;MAC9B;IACF;IACA;IAAA,KACK,IAAIH,IAAI,CAAC0E,sBAAsB,KAAK,CAAC,EAAE;MAC1C;MACA,IAAIG,MAAM,CAACC,OAAO,CAAC,WAAW9E,IAAI,CAACb,OAAO,0CAA0C,CAAC,EAAE;QACrF;QACA4F,eAAe,CAAC/E,IAAI,CAACb,OAAO,CAAC,CAAC6F,IAAI,CAAC,MAAM;UACvC;UACA,MAAMC,WAAW,GAAG;YAAE,GAAGjF,IAAI;YAAE0E,sBAAsB,EAAE;UAAE,CAAC;UAC1D9F,eAAe,CAACqG,WAAW,CAAC;UAC5B/F,WAAW,CAAC;YACV,GAAGD,QAAQ;YACXE,OAAO,EAAE8F,WAAW,CAAC9F,OAAO;YAC5BC,YAAY,EAAE;UAChB,CAAC,CAAC;UACF;UACAd,oBAAoB,CAAC,KAAK,CAAC;;UAE3B;UACAkC,UAAU,CAAC,CAAC;QACd,CAAC,CAAC,CAAC0E,KAAK,CAAC9D,KAAK,IAAI;UAChBN,OAAO,CAACM,KAAK,CAAC,iDAAiD,EAAEA,KAAK,CAAC;UACvE1D,OAAO,CAAC,kDAAkD,IAAI0D,KAAK,CAACI,OAAO,IAAI,oBAAoB,CAAC,CAAC;QACvG,CAAC,CAAC;MACJ,CAAC,MAAM;QACL;QACA;MACF;IACF,CAAC,MAAM;MACL;MACA5C,eAAe,CAACoB,IAAI,CAAC;MACrBd,WAAW,CAAC;QACV,GAAGD,QAAQ;QACXE,OAAO,EAAEa,IAAI,CAACb,OAAO;QACrBC,YAAY,EAAE;MAChB,CAAC,CAAC;MACF;MACAd,oBAAoB,CAAC,KAAK,CAAC;;MAE3B;MACA,IAAI0B,IAAI,CAACgD,SAAS,IAAIhD,IAAI,CAACmF,YAAY,IAAInF,IAAI,CAACiD,OAAO,EAAE;QACvDnC,OAAO,CAACC,GAAG,CAAC,8CAA8Cf,IAAI,CAACb,OAAO,KAAK,CAAC;QAC5EqB,UAAU,CAAC,CAAC;MACd;IACF;EACF,CAAC;;EAED;EACA,MAAMuE,eAAe,GAAG,MAAOK,MAAM,IAAK;IACxC,IAAI;MACF;MACA,MAAMhJ,WAAW,CAAC2I,eAAe,CAACvH,UAAU,EAAE4H,MAAM,CAAC;MACrD3H,SAAS,CAAC,QAAQ2H,MAAM,0BAA0B,CAAC;MACnD,OAAO,IAAI;IACb,CAAC,CAAC,OAAOhE,KAAK,EAAE;MACdN,OAAO,CAACM,KAAK,CAAC,iDAAiD,EAAEA,KAAK,CAAC;MACvE1D,OAAO,CAAC,kDAAkD,IAAI0D,KAAK,CAACI,OAAO,IAAI,oBAAoB,CAAC,CAAC;MACrG,MAAMJ,KAAK;IACb;EACF,CAAC;;EAED;EACA,MAAMiE,gBAAgB,GAAIC,CAAC,IAAK;IAC9B,MAAM;MAAEC,IAAI;MAAEC;IAAM,CAAC,GAAGF,CAAC,CAACG,MAAM;;IAEhC;IACA,IAAIF,IAAI,KAAK,WAAW,IAAIC,KAAK,IAAIA,KAAK,KAAK,cAAc,EAAE;MAC7D;MACA,MAAMvF,MAAM,GAAGxB,MAAM,CAAC8F,IAAI,CAACL,CAAC,IAAIA,CAAC,CAAC7E,SAAS,KAAKmG,KAAK,CAAC;MAEtD,IAAIvF,MAAM,IAAItB,YAAY,EAAE;QAC1B;QACA;QACA,MAAMmF,YAAY,GAChBX,MAAM,CAAClD,MAAM,CAAC+C,SAAS,IAAI,EAAE,CAAC,CAACI,IAAI,CAAC,CAAC,KAAKD,MAAM,CAACxE,YAAY,CAACqE,SAAS,IAAI,EAAE,CAAC,CAACI,IAAI,CAAC,CAAC,IACrFD,MAAM,CAAClD,MAAM,CAACgD,OAAO,IAAI,GAAG,CAAC,CAACG,IAAI,CAAC,CAAC,KAAKD,MAAM,CAACxE,YAAY,CAACsE,OAAO,IAAI,GAAG,CAAC,CAACG,IAAI,CAAC,CAAC;QAErF,IAAI,CAACU,YAAY,EAAE;UACjBhD,OAAO,CAACC,GAAG,CAAC,mCAAmC,EAAEd,MAAM,CAAC;UACxDa,OAAO,CAACC,GAAG,CAAC,gBAAgB,EAAEpC,YAAY,CAAC;UAC3CmC,OAAO,CAACC,GAAG,CAAC,uBAAuB,EAAE;YACnC2E,cAAc,EAAEvC,MAAM,CAACxE,YAAY,CAACsE,OAAO,IAAI,GAAG,CAAC;YACnD0C,gBAAgB,EAAExC,MAAM,CAAClD,MAAM,CAACgD,OAAO,IAAI,GAAG;UAChD,CAAC,CAAC;;UAEF;UACAlD,uBAAuB,CAAC;YACtBC,IAAI,EAAErB,YAAY;YAClBsB,MAAM,EAAEA;UACV,CAAC,CAAC;UACFN,6BAA6B,CAAC,IAAI,CAAC;;UAEnC;UACA;QACF;MACF;IACF;IAEAT,WAAW,CAAC;MACV,GAAGD,QAAQ;MACX,CAACsG,IAAI,GAAGC;IACV,CAAC,CAAC;;IAEF;IACAI,aAAa,CAACL,IAAI,EAAEC,KAAK,CAAC;EAC5B,CAAC;;EAED;EACA,MAAMI,aAAa,GAAGA,CAACL,IAAI,EAAEC,KAAK,KAAK;IACrC,IAAIpE,KAAK,GAAG,IAAI;IAChB,IAAIyE,OAAO,GAAG,IAAI;IAElB,IAAIN,IAAI,KAAK,cAAc,EAAE;MAC3B;MACA,IAAI,CAACC,KAAK,IAAIA,KAAK,CAACpC,IAAI,CAAC,CAAC,KAAK,EAAE,EAAE;QACjChC,KAAK,GAAG,uCAAuC;QAC/C,OAAO,KAAK;MACd;;MAEA;MACA,IAAI0E,KAAK,CAACC,UAAU,CAACP,KAAK,CAAC,CAAC,IAAIO,UAAU,CAACP,KAAK,CAAC,IAAI,CAAC,EAAE;QACtDpE,KAAK,GAAG,sCAAsC;QAC9C,OAAO,KAAK;MACd;MAEA,MAAM4E,WAAW,GAAGD,UAAU,CAACP,KAAK,CAAC;;MAErC;MACA,IAAI7G,YAAY,IAAIA,YAAY,CAACsH,aAAa,IAAID,WAAW,GAAGD,UAAU,CAACpH,YAAY,CAACsH,aAAa,CAAC,EAAE;QACtGJ,OAAO,GAAG,mBAAmBG,WAAW,yCAAyCrH,YAAY,CAACsH,aAAa,IAAI;MACjH;;MAEA;MACA,IAAIhH,QAAQ,CAACI,SAAS,IAAIJ,QAAQ,CAACI,SAAS,KAAK,cAAc,EAAE;QAC/D,MAAMY,MAAM,GAAGxB,MAAM,CAAC8F,IAAI,CAACL,CAAC,IAAIA,CAAC,CAAC7E,SAAS,KAAKJ,QAAQ,CAACI,SAAS,CAAC;QACnE,IAAIY,MAAM,IAAI+F,WAAW,GAAGD,UAAU,CAAC9F,MAAM,CAAC8C,aAAa,CAAC,EAAE;UAC5D8C,OAAO,GAAG,mBAAmBG,WAAW,6CAA6C/F,MAAM,CAAC8C,aAAa,oCAAoC;QAC/I;MACF;IACF,CAAC,MAAM,IAAIwC,IAAI,KAAK,WAAW,EAAE;MAC/B;MACA,IAAI,CAACC,KAAK,IAAIA,KAAK,CAACpC,IAAI,CAAC,CAAC,KAAK,EAAE,EAAE;QACjChC,KAAK,GAAG,qCAAqC;QAC7C,OAAO,KAAK;MACd;IACF;;IAEA;IACA7B,aAAa,CAAC2G,IAAI,KAAK;MACrB,GAAGA,IAAI;MACP,CAACX,IAAI,GAAGnE;IACV,CAAC,CAAC,CAAC;;IAEH;IACA3B,eAAe,CAACyG,IAAI,KAAK;MACvB,GAAGA,IAAI;MACP,CAACX,IAAI,GAAGM;IACV,CAAC,CAAC,CAAC;IAEH,OAAO,CAACzE,KAAK;EACf,CAAC;;EAED;EACA,MAAM,CAAC+E,iBAAiB,EAAEC,oBAAoB,CAAC,GAAG5M,QAAQ,CAAC,KAAK,CAAC;EACjE,MAAM,CAAC6M,iBAAiB,EAAEC,oBAAoB,CAAC,GAAG9M,QAAQ,CAAC,KAAK,CAAC;EACjE,MAAM,CAAC+M,kBAAkB,EAAEC,qBAAqB,CAAC,GAAGhN,QAAQ,CAAC;IAC3DiN,KAAK,EAAE,EAAE;IACTjF,OAAO,EAAE,EAAE;IACXkF,SAAS,EAAEA,CAAA,KAAM,CAAC;EACpB,CAAC,CAAC;;EAEF;;EAEA;EACA,MAAMC,YAAY,GAAGA,CAAA,KAAM;IACzB,IAAIC,OAAO,GAAG,IAAI;IAClB,MAAMC,MAAM,GAAG,CAAC,CAAC;IACjB,MAAMC,QAAQ,GAAG,CAAC,CAAC;;IAEnB;IACAV,oBAAoB,CAAC,KAAK,CAAC;;IAE3B;IACA,IAAI,CAACnH,QAAQ,CAACG,YAAY,IAAIH,QAAQ,CAACG,YAAY,CAACgE,IAAI,CAAC,CAAC,KAAK,EAAE,EAAE;MACjEyD,MAAM,CAACzH,YAAY,GAAG,uCAAuC;MAC7DwH,OAAO,GAAG,KAAK;IACjB,CAAC,MAAM,IAAId,KAAK,CAACC,UAAU,CAAC9G,QAAQ,CAACG,YAAY,CAAC,CAAC,IAAI2G,UAAU,CAAC9G,QAAQ,CAACG,YAAY,CAAC,IAAI,CAAC,EAAE;MAC7FyH,MAAM,CAACzH,YAAY,GAAG,sCAAsC;MAC5DwH,OAAO,GAAG,KAAK;IACjB;;IAEA;IACA,IAAI,CAAC3H,QAAQ,CAACI,SAAS,IAAIJ,QAAQ,CAACI,SAAS,CAAC+D,IAAI,CAAC,CAAC,KAAK,EAAE,EAAE;MAC3DyD,MAAM,CAACxH,SAAS,GAAG,qCAAqC;MACxDuH,OAAO,GAAG,KAAK;IACjB;IAEA,IAAIA,OAAO,EAAE;MACX,MAAMZ,WAAW,GAAGD,UAAU,CAAC9G,QAAQ,CAACG,YAAY,CAAC;;MAErD;MACA,IAAIT,YAAY,IAAIA,YAAY,CAACsH,aAAa,IAAID,WAAW,GAAGD,UAAU,CAACpH,YAAY,CAACsH,aAAa,CAAC,EAAE;QACtGa,QAAQ,CAAC1H,YAAY,GAAG,mBAAmB4G,WAAW,yCAAyCrH,YAAY,CAACsH,aAAa,IAAI;QAC7HG,oBAAoB,CAAC,IAAI,CAAC,CAAC,CAAC;QAC5B;QACA;MACF;;MAEA;MACA,IAAIQ,OAAO,IAAI3H,QAAQ,CAACI,SAAS,IAAIJ,QAAQ,CAACI,SAAS,KAAK,cAAc,EAAE;QAC1E,MAAMY,MAAM,GAAGxB,MAAM,CAAC8F,IAAI,CAACL,CAAC,IAAIA,CAAC,CAAC7E,SAAS,KAAKJ,QAAQ,CAACI,SAAS,CAAC;QACnE,IAAIY,MAAM,IAAI+F,WAAW,GAAGD,UAAU,CAAC9F,MAAM,CAAC8C,aAAa,CAAC,EAAE;UAC5D+D,QAAQ,CAAC1H,YAAY,GAAG,mBAAmB4G,WAAW,6CAA6C/F,MAAM,CAAC8C,aAAa,oCAAoC;UAC3JqD,oBAAoB,CAAC,IAAI,CAAC,CAAC,CAAC;;UAE5B;UACAI,qBAAqB,CAAC;YACpBC,KAAK,EAAE,kCAAkC;YACzCjF,OAAO,EAAE,mBAAmBwE,WAAW,6CAA6C/F,MAAM,CAAC8C,aAAa,8DAA8D;YACtK2D,SAAS,EAAEA,CAAA,KAAM;cACf;cACAK,UAAU,CAAC,CAAC;YACd;UACF,CAAC,CAAC;UACFT,oBAAoB,CAAC,IAAI,CAAC;UAC1B,OAAO,KAAK,CAAC,CAAC;QAChB;MACF;IACF;IAEA/G,aAAa,CAACsH,MAAM,CAAC;IACrBpH,eAAe,CAACqH,QAAQ,CAAC;IACzB,OAAOF,OAAO;EAChB,CAAC;;EAED;EACA,MAAMG,UAAU,GAAGA,CAAA,KAAM;IACvB,IAAIhI,UAAU,KAAK,CAAC,EAAE;MACpB;MACA,IAAI,CAAC4H,YAAY,CAAC,CAAC,EAAE;QACnB;MACF;IACF,CAAC,MAAM,IAAI5H,UAAU,KAAK,CAAC,EAAE;MAC3B;MACAyB,UAAU,CAAC,CAAC;IACd;IAEAxB,aAAa,CAAEgI,cAAc,IAAKA,cAAc,GAAG,CAAC,CAAC;EACvD,CAAC;;EAED;EACA,MAAMC,UAAU,GAAGA,CAAA,KAAM;IACvBjI,aAAa,CAAEgI,cAAc,IAAKA,cAAc,GAAG,CAAC,CAAC;EACvD,CAAC;;EAED;EACA,MAAME,WAAW,GAAGA,CAAA,KAAM;IACxBlI,aAAa,CAAC,CAAC,CAAC;IAChBJ,eAAe,CAAC,IAAI,CAAC;IACrBE,cAAc,CAAC,EAAE,CAAC;IAClBI,WAAW,CAAC;MACVC,OAAO,EAAE,EAAE;MACXC,YAAY,EAAE,EAAE;MAChBC,SAAS,EAAE;IACb,CAAC,CAAC;IACFE,aAAa,CAAC,CAAC,CAAC,CAAC;IACjBE,eAAe,CAAC,CAAC,CAAC,CAAC;EACrB,CAAC;;EAED;EACA;EACA,MAAM0H,2BAA2B,GAAGA,CAACnB,WAAW,EAAEoB,YAAY,KAAK;IACjE,OAAO1K,mBAAmB,CAACsJ,WAAW,EAAEoB,YAAY,CAAC;EACvD,CAAC;;EAED;EACA,MAAMC,YAAY,GAAG,MAAAA,CAAA,KAAY;IAC/B;IACA;IACA,IAAI3G,QAAQ;IACZ,IAAI4G,kBAAkB;IACtB,IAAItB,WAAW;IACf,IAAIuB,SAAS,GAAG,KAAK;IAErB,IAAI;MACFzJ,UAAU,CAAC,IAAI,CAAC;;MAEhB;MACA,IAAI,CAAC6I,YAAY,CAAC,CAAC,EAAE;QACnB7I,UAAU,CAAC,KAAK,CAAC;QACjB;MACF;;MAEA;MACAkI,WAAW,GAAGD,UAAU,CAAC9G,QAAQ,CAACG,YAAY,CAAC;;MAE/C;MACAsB,QAAQ,GAAGzB,QAAQ,CAACI,SAAS;;MAE7B;MACA,IAAI,CAACqB,QAAQ,IAAIA,QAAQ,KAAK,EAAE,EAAE;QAChC;QACAnB,aAAa,CAAC;UACZ,GAAGD,UAAU;UACbD,SAAS,EAAE;QACb,CAAC,CAAC;QACFvB,UAAU,CAAC,KAAK,CAAC;QACjB;MACF,CAAC,MAAM,IAAI4C,QAAQ,KAAK,cAAc,EAAE;QACtC;QACAI,OAAO,CAACC,GAAG,CAAC,iCAAiC,CAAC;QAC9C;QACAL,QAAQ,GAAG,cAAc;MAC3B,CAAC,MAAM;QACL;QACAI,OAAO,CAACC,GAAG,CAAC,wBAAwBL,QAAQ,EAAE,CAAC;MACjD;;MAEA;MACA4G,kBAAkB,GAAGH,2BAA2B,CAACnB,WAAW,EAAErH,YAAY,CAACsH,aAAa,CAAC;;MAEzF;MACAsB,SAAS,GAAG,IAAI;MAChBzG,OAAO,CAACC,GAAG,CAAC,6EAA6E,CAAC;;MAE1F;MACA,IAAIL,QAAQ,KAAK,cAAc,EAAE;QAC/BI,OAAO,CAACC,GAAG,CAAC,6BAA6B,CAAC;MAC5C;MACA;MAAA,KACK,IAAIL,QAAQ,IAAIA,QAAQ,KAAK,cAAc,EAAE;QAChD,MAAMT,MAAM,GAAGxB,MAAM,CAAC8F,IAAI,CAACL,CAAC,IAAIA,CAAC,CAAC7E,SAAS,KAAKqB,QAAQ,CAAC;QACzD,IAAIT,MAAM,IAAI+F,WAAW,GAAGD,UAAU,CAAC9F,MAAM,CAAC8C,aAAa,CAAC,EAAE;UAC5DjC,OAAO,CAACC,GAAG,CAAC,aAAaL,QAAQ,sCAAsCsF,WAAW,oBAAoB/F,MAAM,CAAC8C,aAAa,GAAG,CAAC;QAChI;MACF;;MAEA;MACA,IAAIpE,YAAY,IAAIA,YAAY,CAACsH,aAAa,IAAID,WAAW,GAAGD,UAAU,CAACpH,YAAY,CAACsH,aAAa,CAAC,EAAE;QACtGnF,OAAO,CAACC,GAAG,CAAC,mBAAmBiF,WAAW,+BAA+BrH,YAAY,CAACsH,aAAa,GAAG,CAAC;MACzG;;MAEA;MACAnF,OAAO,CAACC,GAAG,CAAC,aAAa,EAAE;QACzBvD,UAAU;QACV4H,MAAM,EAAEnG,QAAQ,CAACE,OAAO;QACxB6G,WAAW;QACXtF,QAAQ;QACR6G,SAAS;QACTD;MACF,CAAC,CAAC;;MAEF;MACA,IAAI,CAACnB,iBAAiB,EAAE;QACtB,MAAMqB,cAAc,GAAG,qCAAqCvI,QAAQ,CAACE,OAAO,QAAQ6G,WAAW,WAAW;;QAE1G;QACAQ,qBAAqB,CAAC;UACpBC,KAAK,EAAE,wBAAwB;UAC/BjF,OAAO,EAAEgG,cAAc;UACvBd,SAAS,EAAE,MAAAA,CAAA,KAAY;YACrB;YACA,IAAI;cACF5I,UAAU,CAAC,IAAI,CAAC;;cAEhB;cACAgD,OAAO,CAACC,GAAG,CAAC,qGAAqG,CAAC;cAClH,MAAM3E,WAAW,CAACqL,iBAAiB,CACjCjK,UAAU,EACVyB,QAAQ,CAACE,OAAO,EAChB6G,WAAW,EACXtF,QAAQ,EACR,IAAI,CAAC;cACP,CAAC;;cAED;cACA,IAAIgH,cAAc,GAAG,qDAAqDJ,kBAAkB,EAAE;cAC9F,IAAI5G,QAAQ,KAAK,cAAc,EAAE;gBAC/BgH,cAAc,IAAI,iCAAiC;cACrD,CAAC,MAAM,IAAIhH,QAAQ,EAAE;gBACnB,MAAMT,MAAM,GAAGxB,MAAM,CAAC8F,IAAI,CAACL,CAAC,IAAIA,CAAC,CAAC7E,SAAS,KAAKqB,QAAQ,CAAC;gBACzD,IAAIT,MAAM,EAAE;kBACVyH,cAAc,IAAI,gCAAgChH,QAAQ,EAAE;gBAC9D;cACF;;cAEA;cACAjD,SAAS,CAACiK,cAAc,CAAC;;cAEzB;cACAR,WAAW,CAAC,CAAC;;cAEb;cACArG,QAAQ,CAAC,CAAC;YACZ,CAAC,CAAC,OAAOO,KAAK,EAAE;cACdN,OAAO,CAACM,KAAK,CAAC,mDAAmD,EAAEA,KAAK,CAAC;;cAEzE;cACA,IAAIV,QAAQ,KAAK,cAAc,IAAIU,KAAK,CAACuG,OAAO,EAAE;gBAChD;gBACA,IAAID,cAAc,GAAG,qDAAqDJ,kBAAkB,iCAAiC;gBAC7H7J,SAAS,CAACiK,cAAc,CAAC;;gBAEzB;gBACAR,WAAW,CAAC,CAAC;;gBAEb;gBACArG,QAAQ,CAAC,CAAC;gBACV;cACF;;cAEA;cACA,IAAI2B,YAAY,GAAG,kDAAkD;cAErE,IAAIpB,KAAK,CAACE,QAAQ,EAAE;gBAAA,IAAAsG,oBAAA;gBAClB;gBACA,MAAMhD,MAAM,GAAGxD,KAAK,CAACE,QAAQ,CAACsD,MAAM;gBACpC,MAAMnC,MAAM,GAAG,EAAAmF,oBAAA,GAAAxG,KAAK,CAACE,QAAQ,CAACgB,IAAI,cAAAsF,oBAAA,uBAAnBA,oBAAA,CAAqBnF,MAAM,KAAIrB,KAAK,CAACI,OAAO;gBAE3D,IAAIoD,MAAM,KAAK,GAAG,EAAE;kBAClB;kBACA,IAAInC,MAAM,CAAC9B,QAAQ,CAAC,eAAe,CAAC,EAAE;oBACpC6B,YAAY,GAAG,uGAAuG;kBACxH,CAAC,MAAM,IAAIC,MAAM,CAAC9B,QAAQ,CAAC,YAAY,CAAC,EAAE;oBACxC6B,YAAY,GAAG,4EAA4E;kBAC7F,CAAC,MAAM;oBACLA,YAAY,GAAGC,MAAM;kBACvB;gBACF,CAAC,MAAM,IAAImC,MAAM,KAAK,GAAG,EAAE;kBACzB;kBACA,IAAIlE,QAAQ,KAAK,cAAc,IAAI+B,MAAM,CAAC9B,QAAQ,CAAC,aAAa,CAAC,EAAE;oBACjE,IAAI+G,cAAc,GAAG,qDAAqDJ,kBAAkB,iCAAiC;oBAC7H7J,SAAS,CAACiK,cAAc,CAAC;;oBAEzB;oBACAR,WAAW,CAAC,CAAC;;oBAEb;oBACArG,QAAQ,CAAC,CAAC;oBACV;kBACF,CAAC,MAAM;oBACL2B,YAAY,GAAG,8BAA8BC,MAAM,EAAE;kBACvD;gBACF,CAAC,MAAM;kBACLD,YAAY,GAAG,sBAAsBoC,MAAM,MAAMnC,MAAM,EAAE;gBAC3D;cACF,CAAC,MAAM,IAAIrB,KAAK,CAACyG,OAAO,EAAE;gBACxB;gBACArF,YAAY,GAAG,+DAA+D;cAChF,CAAC,MAAM,IAAIpB,KAAK,CAACqB,MAAM,IAAI/B,QAAQ,KAAK,cAAc,EAAE;gBACtD;gBACA8B,YAAY,GAAGpB,KAAK,CAACqB,MAAM;gBAC3B,IAAIrB,KAAK,CAACwD,MAAM,KAAK,GAAG,IAAIxD,KAAK,CAACuG,OAAO,EAAE;kBACzC,IAAID,cAAc,GAAG,qDAAqDJ,kBAAkB,iCAAiC;kBAC7H7J,SAAS,CAACiK,cAAc,CAAC;;kBAEzB;kBACAR,WAAW,CAAC,CAAC;;kBAEb;kBACArG,QAAQ,CAAC,CAAC;kBACV;gBACF;cACF,CAAC,MAAM;gBACL;gBACA2B,YAAY,GAAGpB,KAAK,CAACI,OAAO,IAAIJ,KAAK,CAACqB,MAAM,IAAI,oBAAoB;cACtE;cAEA/E,OAAO,CAAC8E,YAAY,CAAC;YACvB,CAAC,SAAS;cACR1E,UAAU,CAAC,KAAK,CAAC;YACnB;UACF;QACF,CAAC,CAAC;QACFwI,oBAAoB,CAAC,IAAI,CAAC;QAC1BxI,UAAU,CAAC,KAAK,CAAC;QACjB;MACF;;MAEA;MACAgD,OAAO,CAACC,GAAG,CAAC,6DAA6D,CAAC;MAC1ED,OAAO,CAACC,GAAG,CAAC,eAAe,EAAEvD,UAAU,CAAC;MACxCsD,OAAO,CAACC,GAAG,CAAC,YAAY,EAAE9B,QAAQ,CAACE,OAAO,CAAC;MAC3C2B,OAAO,CAACC,GAAG,CAAC,iBAAiB,EAAEiF,WAAW,CAAC;MAC3ClF,OAAO,CAACC,GAAG,CAAC,cAAc,EAAEL,QAAQ,EAAE,OAAOA,QAAQ,CAAC;MACtDI,OAAO,CAACC,GAAG,CAAC,cAAc,EAAEwG,SAAS,CAAC;;MAEtC;MACAzG,OAAO,CAACC,GAAG,CAAC,oGAAoG,CAAC;MACjH,MAAM3E,WAAW,CAACqL,iBAAiB,CACjCjK,UAAU,EACVyB,QAAQ,CAACE,OAAO,EAChB6G,WAAW,EACXtF,QAAQ,EACR,IAAI,CAAC;MACP,CAAC;;MAED;MACA,IAAIgH,cAAc,GAAG,qDAAqDJ,kBAAkB,EAAE;MAC9F,IAAI5G,QAAQ,KAAK,cAAc,EAAE;QAC/BgH,cAAc,IAAI,iCAAiC;MACrD,CAAC,MAAM,IAAIhH,QAAQ,EAAE;QACnB,MAAMT,MAAM,GAAGxB,MAAM,CAAC8F,IAAI,CAACL,CAAC,IAAIA,CAAC,CAAC7E,SAAS,KAAKqB,QAAQ,CAAC;QACzD,IAAIT,MAAM,EAAE;UACVyH,cAAc,IAAI,gCAAgChH,QAAQ,EAAE;QAC9D;MACF;;MAEA;MACAjD,SAAS,CAACiK,cAAc,CAAC;;MAEzB;MACAR,WAAW,CAAC,CAAC;;MAEb;MACArG,QAAQ,CAAC,CAAC;IACZ,CAAC,CAAC,OAAOO,KAAK,EAAE;MACdN,OAAO,CAACM,KAAK,CAAC,mDAAmD,EAAEA,KAAK,CAAC;;MAEzE;MACA,IAAIV,QAAQ,KAAK,cAAc,IAAIU,KAAK,CAACuG,OAAO,EAAE;QAChD;QACA,IAAID,cAAc,GAAG,qDAAqDJ,kBAAkB,iCAAiC;QAC7H7J,SAAS,CAACiK,cAAc,CAAC;;QAEzB;QACAR,WAAW,CAAC,CAAC;;QAEb;QACArG,QAAQ,CAAC,CAAC;QACV;MACF;;MAEA;MACA,IAAI2B,YAAY,GAAG,kDAAkD;MAErE,IAAIpB,KAAK,CAACE,QAAQ,EAAE;QAAA,IAAAwG,qBAAA;QAClB;QACA,MAAMlD,MAAM,GAAGxD,KAAK,CAACE,QAAQ,CAACsD,MAAM;QACpC,MAAMnC,MAAM,GAAG,EAAAqF,qBAAA,GAAA1G,KAAK,CAACE,QAAQ,CAACgB,IAAI,cAAAwF,qBAAA,uBAAnBA,qBAAA,CAAqBrF,MAAM,KAAIrB,KAAK,CAACI,OAAO;QAE3D,IAAIoD,MAAM,KAAK,GAAG,EAAE;UAClB;UACA,IAAInC,MAAM,CAAC9B,QAAQ,CAAC,eAAe,CAAC,EAAE;YACpC6B,YAAY,GAAG,uGAAuG;UACxH,CAAC,MAAM,IAAIC,MAAM,CAAC9B,QAAQ,CAAC,YAAY,CAAC,EAAE;YACxC6B,YAAY,GAAG,4EAA4E;UAC7F,CAAC,MAAM;YACLA,YAAY,GAAGC,MAAM;UACvB;QACF,CAAC,MAAM,IAAImC,MAAM,KAAK,GAAG,EAAE;UACzB;UACA,IAAIlE,QAAQ,KAAK,cAAc,IAAI+B,MAAM,CAAC9B,QAAQ,CAAC,aAAa,CAAC,EAAE;YACjE,IAAI+G,cAAc,GAAG,qDAAqDJ,kBAAkB,iCAAiC;YAC7H7J,SAAS,CAACiK,cAAc,CAAC;;YAEzB;YACAR,WAAW,CAAC,CAAC;;YAEb;YACArG,QAAQ,CAAC,CAAC;YACV;UACF,CAAC,MAAM;YACL2B,YAAY,GAAG,8BAA8BC,MAAM,EAAE;UACvD;QACF,CAAC,MAAM;UACLD,YAAY,GAAG,sBAAsBoC,MAAM,MAAMnC,MAAM,EAAE;QAC3D;MACF,CAAC,MAAM,IAAIrB,KAAK,CAACyG,OAAO,EAAE;QACxB;QACArF,YAAY,GAAG,+DAA+D;MAChF,CAAC,MAAM,IAAIpB,KAAK,CAACqB,MAAM,IAAI/B,QAAQ,KAAK,cAAc,EAAE;QACtD;QACA8B,YAAY,GAAGpB,KAAK,CAACqB,MAAM;QAC3B,IAAIrB,KAAK,CAACwD,MAAM,KAAK,GAAG,IAAIxD,KAAK,CAACuG,OAAO,EAAE;UACzC,IAAID,cAAc,GAAG,qDAAqDJ,kBAAkB,iCAAiC;UAC7H7J,SAAS,CAACiK,cAAc,CAAC;;UAEzB;UACAR,WAAW,CAAC,CAAC;;UAEb;UACArG,QAAQ,CAAC,CAAC;UACV;QACF;MACF,CAAC,MAAM;QACL;QACA2B,YAAY,GAAGpB,KAAK,CAACI,OAAO,IAAIJ,KAAK,CAACqB,MAAM,IAAI,oBAAoB;MACtE;MAEA/E,OAAO,CAAC8E,YAAY,CAAC;IACvB,CAAC,SAAS;MACR1E,UAAU,CAAC,KAAK,CAAC;IACnB;EACF,CAAC;;EAED;EACA,MAAMiK,WAAW,GAAGA,CAAA,KAAM;IACxB,oBACE3K,OAAA,CAACzD,GAAG;MAAAqO,QAAA,gBACF5K,OAAA,CAACvD,UAAU;QAACoO,OAAO,EAAC,WAAW;QAACC,EAAE,EAAE;UAAEC,EAAE,EAAE,CAAC;UAAEC,UAAU,EAAE;QAAO,CAAE;QAAAJ,QAAA,EAAC;MAEnE;QAAAK,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAY,CAAC,eAGbpL,OAAA,CAACxD,KAAK;QAACsO,EAAE,EAAE;UAAEO,CAAC,EAAE,GAAG;UAAEN,EAAE,EAAE,CAAC;UAAEO,KAAK,EAAE;QAAO,CAAE;QAAAV,QAAA,eAC1C5K,OAAA,CAACzD,GAAG;UAACuO,EAAE,EAAE;YAAES,OAAO,EAAE,MAAM;YAAEC,UAAU,EAAE,QAAQ;YAAEF,KAAK,EAAE;UAAO,CAAE;UAAAV,QAAA,gBAChE5K,OAAA,CAACvD,UAAU;YAACoO,OAAO,EAAC,WAAW;YAACC,EAAE,EAAE;cAAEW,EAAE,EAAE,CAAC;cAAEC,QAAQ,EAAE;YAAO,CAAE;YAAAd,QAAA,EAAC;UAEjE;YAAAK,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAY,CAAC,eACbpL,OAAA,CAACtD,SAAS;YACRiP,IAAI,EAAC,OAAO;YACZC,KAAK,EAAC,SAAS;YACff,OAAO,EAAC,UAAU;YAClBzC,KAAK,EAAE3G,WAAY;YACnBoK,QAAQ,EAAG3D,CAAC,IAAKxG,cAAc,CAACwG,CAAC,CAACG,MAAM,CAACD,KAAK,CAAE;YAChD0D,WAAW,EAAC,yBAAyB;YACrChB,EAAE,EAAE;cAAEiB,QAAQ,EAAE,CAAC;cAAEN,EAAE,EAAE;YAAE;UAAE;YAAAR,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC5B,CAAC,eACFpL,OAAA,CAACrD,MAAM;YACLkO,OAAO,EAAC,WAAW;YACnBmB,KAAK,EAAC,SAAS;YACfC,OAAO,EAAEjF,oBAAqB;YAC9BkF,QAAQ,EAAEvL,WAAW,IAAI,CAACc,WAAW,CAACuE,IAAI,CAAC,CAAE;YAC7CmG,SAAS,EAAExL,WAAW,gBAAGX,OAAA,CAAC7C,gBAAgB;cAACwO,IAAI,EAAE;YAAG;cAAAV,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,gBAAGpL,OAAA,CAAC1B,UAAU;cAAC8N,QAAQ,EAAC;YAAO;cAAAnB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAE;YAC1FO,IAAI,EAAC,OAAO;YACZb,EAAE,EAAE;cAAEY,QAAQ,EAAE,MAAM;cAAEW,MAAM,EAAE;YAAO,CAAE;YAAAzB,QAAA,EAC1C;UAED;YAAAK,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACN;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACD,CAAC,eAGRpL,OAAA,CAACxD,KAAK;QAACsO,EAAE,EAAE;UAAEO,CAAC,EAAE,GAAG;UAAEC,KAAK,EAAE;QAAO,CAAE;QAAAV,QAAA,gBACnC5K,OAAA,CAACvD,UAAU;UAACoO,OAAO,EAAC,WAAW;UAACC,EAAE,EAAE;YAAEC,EAAE,EAAE;UAAE,CAAE;UAAAH,QAAA,EAAC;QAE/C;UAAAK,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAY,CAAC,EAEZzK,WAAW,gBACVX,OAAA,CAACzD,GAAG;UAACuO,EAAE,EAAE;YAAES,OAAO,EAAE,MAAM;YAAEe,cAAc,EAAE,QAAQ;YAAEC,EAAE,EAAE;UAAE,CAAE;UAAA3B,QAAA,eAC5D5K,OAAA,CAAC7C,gBAAgB;YAACwO,IAAI,EAAE;UAAG;YAAAV,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC3B,CAAC,GACJjK,IAAI,CAAC2C,MAAM,KAAK,CAAC,gBACnB9D,OAAA,CAAC9C,KAAK;UAACsP,QAAQ,EAAC,MAAM;UAAC1B,EAAE,EAAE;YAAE2B,EAAE,EAAE;UAAI,CAAE;UAAA7B,QAAA,eACrC5K,OAAA,CAACvD,UAAU;YAACoO,OAAO,EAAC,SAAS;YAAAD,QAAA,EAAC;UAA2C;YAAAK,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAY;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACjF,CAAC,gBAERpL,OAAA,CAAClC,cAAc;UAAC4O,SAAS,EAAElQ,KAAM;UAACqO,OAAO,EAAC,UAAU;UAACC,EAAE,EAAE;YAAE6B,SAAS,EAAE,OAAO;YAAEC,QAAQ,EAAE,MAAM;YAAEtB,KAAK,EAAE;UAAO,CAAE;UAAAV,QAAA,eAC/G5K,OAAA,CAACrC,KAAK;YAACgO,IAAI,EAAC,OAAO;YAACkB,YAAY;YAAAjC,QAAA,gBAC9B5K,OAAA,CAACjC,SAAS;cAAA6M,QAAA,eACR5K,OAAA,CAAChC,QAAQ;gBAAC8M,EAAE,EAAE;kBAAE,MAAM,EAAE;oBAAEE,UAAU,EAAE,MAAM;oBAAEyB,EAAE,EAAE,CAAC;oBAAEK,OAAO,EAAE;kBAAU;gBAAE,CAAE;gBAAAlC,QAAA,gBAC1E5K,OAAA,CAACnC,SAAS;kBAAA+M,QAAA,EAAC;gBAAO;kBAAAK,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAW,CAAC,eAC9BpL,OAAA,CAACnC,SAAS;kBAAA+M,QAAA,EAAC;gBAAS;kBAAAK,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAW,CAAC,eAChCpL,OAAA,CAACnC,SAAS;kBAAA+M,QAAA,EAAC;gBAAU;kBAAAK,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAW,CAAC,eACjCpL,OAAA,CAACnC,SAAS;kBAAA+M,QAAA,EAAC;gBAAK;kBAAAK,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAW,CAAC,eAC5BpL,OAAA,CAACnC,SAAS;kBAAA+M,QAAA,EAAC;gBAAK;kBAAAK,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAW,CAAC,eAC5BpL,OAAA,CAACnC,SAAS;kBAACkP,KAAK,EAAC,QAAQ;kBAACjC,EAAE,EAAE;oBAAEQ,KAAK,EAAE;kBAAO,CAAE;kBAAAV,QAAA,EAAC;gBAAI;kBAAAK,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAW,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACzD;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACF,CAAC,eACZpL,OAAA,CAACpC,SAAS;cAAAgN,QAAA,EACPzJ,IAAI,CAAC6L,GAAG,CAAEpK,IAAI,iBACb5C,OAAA,CAAChC,QAAQ;gBAEPiP,KAAK;gBACLhB,OAAO,EAAEA,CAAA,KAAM1E,gBAAgB,CAAC3E,IAAI,CAAE;gBACtCkI,EAAE,EAAE;kBACFoC,MAAM,EAAE,SAAS;kBACjB,SAAS,EAAE;oBAAEJ,OAAO,EAAE;kBAAU,CAAC;kBACjC,MAAM,EAAE;oBAAEL,EAAE,EAAE;kBAAI;gBACpB,CAAE;gBAAA7B,QAAA,gBAEF5K,OAAA,CAACnC,SAAS;kBAACiN,EAAE,EAAE;oBAAEE,UAAU,EAAE;kBAAS,CAAE;kBAAAJ,QAAA,EAAEhI,IAAI,CAACb;gBAAO;kBAAAkJ,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAY,CAAC,eACnEpL,OAAA,CAACnC,SAAS;kBAAA+M,QAAA,EAAEhI,IAAI,CAACgD,SAAS,IAAI;gBAAK;kBAAAqF,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAY,CAAC,eAChDpL,OAAA,CAACnC,SAAS;kBAAA+M,QAAA,EAAEhI,IAAI,CAACiD,OAAO,IAAI;gBAAK;kBAAAoF,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAY,CAAC,eAC9CpL,OAAA,CAACnC,SAAS;kBAAA+M,QAAA,EAAEhI,IAAI,CAACiG,aAAa,IAAI;gBAAK;kBAAAoC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAY,CAAC,eACpDpL,OAAA,CAACnC,SAAS;kBAAA+M,QAAA,eACR5K,OAAA,CAAC1C,IAAI;oBACHqO,IAAI,EAAC,OAAO;oBACZC,KAAK,EAAEnM,YAAY,CAACmD,IAAI,CAAC,GAAG,OAAO,GAAGlD,gBAAgB,CAACkD,IAAI,CAAC,GAAG,YAAY,GAAGA,IAAI,CAACwE,mBAAoB;oBACvG4E,KAAK,EAAEvM,YAAY,CAACmD,IAAI,CAAC,GAAG,OAAO,GAAGlD,gBAAgB,CAACkD,IAAI,CAAC,GAAG,SAAS,GAAGjD,kBAAkB,CAACiD,IAAI,CAACwE,mBAAmB,CAAE;oBACxH0D,EAAE,EAAE;sBAAEuB,MAAM,EAAE,MAAM;sBAAE,kBAAkB,EAAE;wBAAEc,EAAE,EAAE,CAAC;wBAAEV,EAAE,EAAE,CAAC;wBAAEL,QAAQ,EAAE;sBAAS;oBAAE;kBAAE;oBAAAnB,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAClF;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACO,CAAC,eACZpL,OAAA,CAACnC,SAAS;kBAACkP,KAAK,EAAC,QAAQ;kBAAAnC,QAAA,eACvB5K,OAAA,CAAC3C,UAAU;oBACTsO,IAAI,EAAC,OAAO;oBACZM,OAAO,EAAG/D,CAAC,IAAK;sBACdA,CAAC,CAACkF,eAAe,CAAC,CAAC;sBACnB5L,eAAe,CAACoB,IAAI,CAAC;sBACrBO,wBAAwB,CAAC,IAAI,CAAC;oBAChC,CAAE;oBAAAyH,QAAA,eAEF5K,OAAA,CAAClB,QAAQ;sBAACsN,QAAQ,EAAC;oBAAO;sBAAAnB,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAE;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACnB;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACJ,CAAC;cAAA,GAhCPxI,IAAI,CAACb,OAAO;gBAAAkJ,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAiCT,CACX;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACO,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACP;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACM,CACjB;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACI,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACL,CAAC;EAEV,CAAC;;EAED;EACA,MAAMiC,WAAW,GAAGA,CAAA,KAAM;IACxB,IAAI,CAAC9L,YAAY,EAAE,OAAO,IAAI;IAE9B,oBACEvB,OAAA,CAACzD,GAAG;MAAAqO,QAAA,gBACF5K,OAAA,CAACvD,UAAU;QAACoO,OAAO,EAAC,WAAW;QAACC,EAAE,EAAE;UAAEC,EAAE,EAAE,CAAC;UAAEC,UAAU,EAAE;QAAO,CAAE;QAAAJ,QAAA,EAAC;MAEnE;QAAAK,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAY,CAAC,eAGbpL,OAAA,CAACxD,KAAK;QAACsO,EAAE,EAAE;UAAEO,CAAC,EAAE,GAAG;UAAEN,EAAE,EAAE,CAAC;UAAEO,KAAK,EAAE;QAAO,CAAE;QAAAV,QAAA,eAC1C5K,OAAA,CAACzD,GAAG;UAACuO,EAAE,EAAE;YAAES,OAAO,EAAE,MAAM;YAAE+B,QAAQ,EAAE,MAAM;YAAE9B,UAAU,EAAE,QAAQ;YAAEF,KAAK,EAAE;UAAO,CAAE;UAAAV,QAAA,gBAClF5K,OAAA,CAACvD,UAAU;YAACoO,OAAO,EAAC,WAAW;YAACC,EAAE,EAAE;cAAEW,EAAE,EAAE,CAAC;cAAET,UAAU,EAAE;YAAO,CAAE;YAAAJ,QAAA,GAAC,oBAC/C,eAAA5K,OAAA;cAAMuN,KAAK,EAAE;gBAAEvB,KAAK,EAAE;cAAU,CAAE;cAAApB,QAAA,EAAErJ,YAAY,CAACQ;YAAO;cAAAkJ,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACxE,CAAC,eAEbpL,OAAA,CAACzD,GAAG;YAACuO,EAAE,EAAE;cAAES,OAAO,EAAE,MAAM;cAAE+B,QAAQ,EAAE,MAAM;cAAEE,GAAG,EAAE,CAAC;cAAEC,EAAE,EAAE;YAAO,CAAE;YAAA7C,QAAA,gBACjE5K,OAAA,CAACzD,GAAG;cAACuO,EAAE,EAAE;gBAAES,OAAO,EAAE,MAAM;gBAAEC,UAAU,EAAE;cAAS,CAAE;cAAAZ,QAAA,gBACjD5K,OAAA,CAACvD,UAAU;gBAACoO,OAAO,EAAC,OAAO;gBAACC,EAAE,EAAE;kBAAEW,EAAE,EAAE,GAAG;kBAAET,UAAU,EAAE;gBAAS,CAAE;gBAAAJ,QAAA,EAAC;cAAU;gBAAAK,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAY,CAAC,eAC1FpL,OAAA,CAACvD,UAAU;gBAACoO,OAAO,EAAC,OAAO;gBAAAD,QAAA,EAAErJ,YAAY,CAACqE,SAAS,IAAI;cAAK;gBAAAqF,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAa,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACvE,CAAC,eAENpL,OAAA,CAACzD,GAAG;cAACuO,EAAE,EAAE;gBAAES,OAAO,EAAE,MAAM;gBAAEC,UAAU,EAAE;cAAS,CAAE;cAAAZ,QAAA,gBACjD5K,OAAA,CAACvD,UAAU;gBAACoO,OAAO,EAAC,OAAO;gBAACC,EAAE,EAAE;kBAAEW,EAAE,EAAE,GAAG;kBAAET,UAAU,EAAE;gBAAS,CAAE;gBAAAJ,QAAA,EAAC;cAAW;gBAAAK,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAY,CAAC,eAC3FpL,OAAA,CAACvD,UAAU;gBAACoO,OAAO,EAAC,OAAO;gBAAAD,QAAA,EAAErJ,YAAY,CAACsE,OAAO,IAAI;cAAK;gBAAAoF,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAa,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACrE,CAAC,eAENpL,OAAA,CAACzD,GAAG;cAACuO,EAAE,EAAE;gBAAES,OAAO,EAAE,MAAM;gBAAEC,UAAU,EAAE;cAAS,CAAE;cAAAZ,QAAA,gBACjD5K,OAAA,CAACvD,UAAU;gBAACoO,OAAO,EAAC,OAAO;gBAACC,EAAE,EAAE;kBAAEW,EAAE,EAAE,GAAG;kBAAET,UAAU,EAAE;gBAAS,CAAE;gBAAAJ,QAAA,EAAC;cAAc;gBAAAK,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAY,CAAC,eAC9FpL,OAAA,CAACvD,UAAU;gBAACoO,OAAO,EAAC,OAAO;gBAAAD,QAAA,GAAErJ,YAAY,CAACsH,aAAa,IAAI,KAAK,EAAC,IAAE;cAAA;gBAAAoC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAY,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC7E,CAAC,eAENpL,OAAA,CAACzD,GAAG;cAACuO,EAAE,EAAE;gBAAES,OAAO,EAAE,MAAM;gBAAEC,UAAU,EAAE;cAAS,CAAE;cAAAZ,QAAA,gBACjD5K,OAAA,CAACvD,UAAU;gBAACoO,OAAO,EAAC,OAAO;gBAACC,EAAE,EAAE;kBAAEW,EAAE,EAAE,GAAG;kBAAET,UAAU,EAAE;gBAAS,CAAE;gBAAAJ,QAAA,EAAC;cAAM;gBAAAK,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAY,CAAC,eACtFpL,OAAA,CAAC1C,IAAI;gBACHsO,KAAK,EAAErK,YAAY,CAAC6F,mBAAmB,IAAI,KAAM;gBACjDuE,IAAI,EAAC,OAAO;gBACZK,KAAK,EAAErM,kBAAkB,CAAC4B,YAAY,CAAC6F,mBAAmB,CAAE;gBAC5DyD,OAAO,EAAC,UAAU;gBAClBC,EAAE,EAAE;kBAAEuB,MAAM,EAAE,MAAM;kBAAE,kBAAkB,EAAE;oBAAEc,EAAE,EAAE,CAAC;oBAAEV,EAAE,EAAE;kBAAE;gBAAE;cAAE;gBAAAxB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC9D,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACC,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACD,CAAC,eAERpL,OAAA,CAACxD,KAAK;QAACsO,EAAE,EAAE;UAAEO,CAAC,EAAE,CAAC;UAAEC,KAAK,EAAE;QAAO,CAAE;QAAAV,QAAA,gBACjC5K,OAAA,CAACvD,UAAU;UAACoO,OAAO,EAAC,WAAW;UAAC6C,YAAY;UAAC5C,EAAE,EAAE;YAAEE,UAAU,EAAE;UAAO,CAAE;UAAAJ,QAAA,EAAC;QAEzE;UAAAK,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAY,CAAC,eAGbpL,OAAA,CAACzD,GAAG;UAACuO,EAAE,EAAE;YAAES,OAAO,EAAE,MAAM;YAAE+B,QAAQ,EAAE,MAAM;YAAEE,GAAG,EAAE,CAAC;YAAEzC,EAAE,EAAE;UAAE,CAAE;UAAAH,QAAA,gBAC5D5K,OAAA,CAACzD,GAAG;YAACuO,EAAE,EAAE;cAAEO,CAAC,EAAE,CAAC;cAAEyB,OAAO,EAAE,SAAS;cAAEa,YAAY,EAAE,CAAC;cAAEC,IAAI,EAAE,UAAU;cAAElC,QAAQ,EAAE,OAAO;cAAEmC,QAAQ,EAAE;YAAQ,CAAE;YAAAjD,QAAA,gBAC7G5K,OAAA,CAACvD,UAAU;cAACoO,OAAO,EAAC,SAAS;cAACC,EAAE,EAAE;gBAAEE,UAAU,EAAE,MAAM;gBAAEgB,KAAK,EAAE,cAAc;gBAAET,OAAO,EAAE,OAAO;gBAAER,EAAE,EAAE;cAAI,CAAE;cAAAH,QAAA,EAAC;YAE5G;cAAAK,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAY,CAAC,eACbpL,OAAA,CAACzD,GAAG;cAACuO,EAAE,EAAE;gBAAES,OAAO,EAAE,MAAM;gBAAEe,cAAc,EAAE,eAAe;gBAAEvB,EAAE,EAAE;cAAI,CAAE;cAAAH,QAAA,gBACrE5K,OAAA,CAACvD,UAAU;gBAACoO,OAAO,EAAC,OAAO;gBAACC,EAAE,EAAE;kBAAEE,UAAU,EAAE,QAAQ;kBAAEoB,QAAQ,EAAE;gBAAS,CAAE;gBAAAxB,QAAA,EAAC;cAAc;gBAAAK,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAY,CAAC,eACzGpL,OAAA,CAACvD,UAAU;gBAACoO,OAAO,EAAC,OAAO;gBAACC,EAAE,EAAE;kBAAEsB,QAAQ,EAAE;gBAAS,CAAE;gBAAAxB,QAAA,GAAErJ,YAAY,CAACsH,aAAa,IAAI,KAAK,EAAC,IAAE;cAAA;gBAAAoC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAY,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACzG,CAAC,eACNpL,OAAA,CAACzD,GAAG;cAACuO,EAAE,EAAE;gBAAES,OAAO,EAAE,MAAM;gBAAEe,cAAc,EAAE;cAAgB,CAAE;cAAA1B,QAAA,gBAC5D5K,OAAA,CAACvD,UAAU;gBAACoO,OAAO,EAAC,OAAO;gBAACC,EAAE,EAAE;kBAAEE,UAAU,EAAE,QAAQ;kBAAEoB,QAAQ,EAAE;gBAAS,CAAE;gBAAAxB,QAAA,EAAC;cAAc;gBAAAK,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAY,CAAC,eACzGpL,OAAA,CAAC1C,IAAI;gBACHsO,KAAK,EAAErK,YAAY,CAAC6F,mBAAmB,IAAI,KAAM;gBACjDuE,IAAI,EAAC,OAAO;gBACZK,KAAK,EAAErM,kBAAkB,CAAC4B,YAAY,CAAC6F,mBAAmB,CAAE;gBAC5DyD,OAAO,EAAC,UAAU;gBAClBC,EAAE,EAAE;kBAAEuB,MAAM,EAAE,MAAM;kBAAE,kBAAkB,EAAE;oBAAEc,EAAE,EAAE,CAAC;oBAAEV,EAAE,EAAE,CAAC;oBAAEL,QAAQ,EAAE;kBAAS;gBAAE;cAAE;gBAAAnB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAClF,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACC,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC,eAENpL,OAAA,CAACzD,GAAG;YAACuO,EAAE,EAAE;cAAEO,CAAC,EAAE,CAAC;cAAEyB,OAAO,EAAE,SAAS;cAAEa,YAAY,EAAE,CAAC;cAAEC,IAAI,EAAE,UAAU;cAAElC,QAAQ,EAAE,OAAO;cAAEmC,QAAQ,EAAE;YAAQ,CAAE;YAAAjD,QAAA,gBAC7G5K,OAAA,CAACvD,UAAU;cAACoO,OAAO,EAAC,SAAS;cAACC,EAAE,EAAE;gBAAEE,UAAU,EAAE,MAAM;gBAAEgB,KAAK,EAAE,gBAAgB;gBAAET,OAAO,EAAE,OAAO;gBAAER,EAAE,EAAE;cAAI,CAAE;cAAAH,QAAA,EAAC;YAE9G;cAAAK,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAY,CAAC,EACZvJ,QAAQ,CAACI,SAAS,IAAIJ,QAAQ,CAACI,SAAS,KAAK,cAAc,GAAG,CAAC,MAAM;cACpE,MAAMY,MAAM,GAAGxB,MAAM,CAAC8F,IAAI,CAACL,CAAC,IAAIA,CAAC,CAAC7E,SAAS,KAAKJ,QAAQ,CAACI,SAAS,CAAC;cACnE,OAAOY,MAAM,gBACX7C,OAAA,CAAAE,SAAA;gBAAA0K,QAAA,gBACE5K,OAAA,CAACzD,GAAG;kBAACuO,EAAE,EAAE;oBAAES,OAAO,EAAE,MAAM;oBAAEe,cAAc,EAAE,eAAe;oBAAEvB,EAAE,EAAE;kBAAI,CAAE;kBAAAH,QAAA,gBACrE5K,OAAA,CAACvD,UAAU;oBAACoO,OAAO,EAAC,OAAO;oBAACC,EAAE,EAAE;sBAAEE,UAAU,EAAE,QAAQ;sBAAEoB,QAAQ,EAAE;oBAAS,CAAE;oBAAAxB,QAAA,EAAC;kBAAU;oBAAAK,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAY,CAAC,eACrGpL,OAAA,CAACvD,UAAU;oBAACoO,OAAO,EAAC,OAAO;oBAACC,EAAE,EAAE;sBAAEsB,QAAQ,EAAE;oBAAS,CAAE;oBAAAxB,QAAA,EAAEvH,eAAe,CAACR,MAAM,CAACZ,SAAS;kBAAC;oBAAAgJ,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAa,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACrG,CAAC,eACNpL,OAAA,CAACzD,GAAG;kBAACuO,EAAE,EAAE;oBAAES,OAAO,EAAE,MAAM;oBAAEe,cAAc,EAAE,eAAe;oBAAEvB,EAAE,EAAE;kBAAI,CAAE;kBAAAH,QAAA,gBACrE5K,OAAA,CAACvD,UAAU;oBAACoO,OAAO,EAAC,OAAO;oBAACC,EAAE,EAAE;sBAAEE,UAAU,EAAE,QAAQ;sBAAEoB,QAAQ,EAAE;oBAAS,CAAE;oBAAAxB,QAAA,EAAC;kBAAc;oBAAAK,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAY,CAAC,eACzGpL,OAAA,CAACvD,UAAU;oBAACoO,OAAO,EAAC,OAAO;oBAACC,EAAE,EAAE;sBAAEsB,QAAQ,EAAE;oBAAS,CAAE;oBAAAxB,QAAA,GAAE/H,MAAM,CAAC8C,aAAa,IAAI,CAAC,EAAC,IAAE;kBAAA;oBAAAsF,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAY,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAC/F,CAAC,eACNpL,OAAA,CAACzD,GAAG;kBAACuO,EAAE,EAAE;oBAAES,OAAO,EAAE,MAAM;oBAAEe,cAAc,EAAE;kBAAgB,CAAE;kBAAA1B,QAAA,gBAC5D5K,OAAA,CAACvD,UAAU;oBAACoO,OAAO,EAAC,OAAO;oBAACC,EAAE,EAAE;sBAAEE,UAAU,EAAE,QAAQ;sBAAEoB,QAAQ,EAAE;oBAAS,CAAE;oBAAAxB,QAAA,EAAC;kBAAM;oBAAAK,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAY,CAAC,eACjGpL,OAAA,CAAC1C,IAAI;oBACHsO,KAAK,EAAE/I,MAAM,CAAC6C,YAAY,IAAI,KAAM;oBACpCiG,IAAI,EAAC,OAAO;oBACZK,KAAK,EAAEpM,iBAAiB,CAACiD,MAAM,CAAC6C,YAAY,CAAE;oBAC9CmF,OAAO,EAAC,UAAU;oBAClBC,EAAE,EAAE;sBAAEuB,MAAM,EAAE,MAAM;sBAAE,kBAAkB,EAAE;wBAAEc,EAAE,EAAE,CAAC;wBAAEV,EAAE,EAAE,CAAC;wBAAEL,QAAQ,EAAE;sBAAS;oBAAE;kBAAE;oBAAAnB,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAClF,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACC,CAAC;cAAA,eACN,CAAC,gBAEHpL,OAAA,CAACvD,UAAU;gBAACoO,OAAO,EAAC,OAAO;gBAACC,EAAE,EAAE;kBAAEsB,QAAQ,EAAE;gBAAS,CAAE;gBAAAxB,QAAA,EAAC;cAAkB;gBAAAK,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAY,CACvF;YACH,CAAC,EAAE,CAAC,gBACFpL,OAAA,CAACvD,UAAU;cAACoO,OAAO,EAAC,OAAO;cAACC,EAAE,EAAE;gBAAEsB,QAAQ,EAAE;cAAS,CAAE;cAAAxB,QAAA,EACpD/I,QAAQ,CAACI,SAAS,KAAK,cAAc,GACpC,kDAAkD,GAClD;YAA4B;cAAAgJ,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACpB,CACb;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACE,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,eAENpL,OAAA,CAACzD,GAAG;UAACuO,EAAE,EAAE;YAAEgD,EAAE,EAAE,CAAC;YAAE/C,EAAE,EAAE,CAAC;YAAEO,KAAK,EAAE;UAAO,CAAE;UAAAV,QAAA,gBACvC5K,OAAA,CAACvD,UAAU;YAACoO,OAAO,EAAC,WAAW;YAAC6C,YAAY;YAAC5C,EAAE,EAAE;cAAEE,UAAU,EAAE;YAAO,CAAE;YAAAJ,QAAA,EAAC;UAEzE;YAAAK,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAY,CAAC,eACbpL,OAAA,CAACtD,SAAS;YACRiP,IAAI,EAAC,OAAO;YACZC,KAAK,EAAC,cAAc;YACpBf,OAAO,EAAC,UAAU;YAClB1C,IAAI,EAAC,cAAc;YACnB4F,IAAI,EAAC,QAAQ;YACb3F,KAAK,EAAEvG,QAAQ,CAACG,YAAa;YAC7B6J,QAAQ,EAAE5D,gBAAiB;YAC3BjE,KAAK,EAAE,CAAC,CAAC9B,UAAU,CAACF,YAAa;YACjCgM,UAAU,EAAE9L,UAAU,CAACF,YAAY,IAAII,YAAY,CAACJ,YAAa;YACjEiM,mBAAmB,EAAE;cACnBnD,EAAE,EAAE;gBAAEkB,KAAK,EAAE5J,YAAY,CAACJ,YAAY,IAAI,CAACE,UAAU,CAACF,YAAY,GAAG,cAAc,GAAG;cAAa;YACrG,CAAE;YACF8I,EAAE,EAAE;cAAEC,EAAE,EAAE,CAAC;cAAEO,KAAK,EAAE;YAAQ,CAAE;YAC9B4C,UAAU,EAAE;cACVC,GAAG,EAAE,MAAM;cAAE;cACbC,IAAI,EAAE;YACR;UAAE;YAAAnD,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACC,CAAC,EAELhJ,YAAY,CAACJ,YAAY,IAAI,CAACE,UAAU,CAACF,YAAY,IAAI,CAAC+G,iBAAiB,iBAC1E/I,OAAA,CAAC9C,KAAK;UAACsP,QAAQ,EAAC,SAAS;UAAC1B,EAAE,EAAE;YAAEC,EAAE,EAAE;UAAE,CAAE;UAAAH,QAAA,EACrCxI,YAAY,CAACJ;QAAY;UAAAiJ,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACrB,CACR,eAEDpL,OAAA,CAAC9C,KAAK;UAACsP,QAAQ,EAAC,MAAM;UAAC1B,EAAE,EAAE;YAAEgD,EAAE,EAAE;UAAE,CAAE;UAAAlD,QAAA,eACnC5K,OAAA,CAACvD,UAAU;YAACoO,OAAO,EAAC,OAAO;YAAAD,QAAA,EAAC;UAE5B;YAAAK,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAY;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACR,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACL,CAAC;EAEV,CAAC;;EAED;EACA,MAAMiD,WAAW,GAAGA,CAAA,KAAM;IAExB;IACA,MAAMC,iBAAiB,GAAIC,YAAY,IAAK;MAC1C,OAAO,IAAInO,UAAU,KAAKmO,YAAY,EAAE;IAC1C,CAAC;;IAED;IACA,MAAMC,mBAAmB,GAAI3L,MAAM,IAAK;MACtC,IAAI,CAACA,MAAM,IAAI,CAAChB,QAAQ,CAACG,YAAY,EAAE,OAAO,IAAI;MAClD,OAAO2G,UAAU,CAAC9F,MAAM,CAAC8C,aAAa,CAAC,IAAIgD,UAAU,CAAC9G,QAAQ,CAACG,YAAY,CAAC;IAC9E,CAAC;;IAED;IACA,MAAMyM,uBAAuB,GAAIvG,CAAC,IAAK;MACrC,MAAMqG,YAAY,GAAGrG,CAAC,CAACG,MAAM,CAACD,KAAK,CAACpC,IAAI,CAAC,CAAC;;MAE1C;MACA,IAAIuI,YAAY,CAACtI,WAAW,CAAC,CAAC,KAAK,GAAG,EAAE;QACtCnE,WAAW,CAAC;UACV,GAAGD,QAAQ;UACXI,SAAS,EAAE;QACb,CAAC,CAAC;QACFE,aAAa,CAAC;UACZ,GAAGD,UAAU;UACbwM,eAAe,EAAE;QACnB,CAAC,CAAC;QACF;MACF;MAEA,IAAIH,YAAY,EAAE;QAChB;QACA,MAAMI,gBAAgB,GAAGL,iBAAiB,CAACC,YAAY,CAAC;;QAExD;QACA,MAAMK,eAAe,GAAGvN,MAAM,CAAC8F,IAAI,CAACL,CAAC,IAAIA,CAAC,CAAC7E,SAAS,KAAK0M,gBAAgB,CAAC;QAE1E,IAAIC,eAAe,EAAE;UACnB;UACA,IAAIA,eAAe,CAAClJ,YAAY,KAAK,MAAM,IAAIkJ,eAAe,CAAClJ,YAAY,KAAK,WAAW,EAAE;YAC3FvD,aAAa,CAAC;cACZ,GAAGD,UAAU;cACbwM,eAAe,EAAE,aAAaH,YAAY,eAAeK,eAAe,CAAClJ,YAAY;YACvF,CAAC,CAAC;YACF;UACF;;UAEA;UACA,IAAInE,YAAY,EAAE;YAChB;YACA,MAAMuE,aAAa,GAAGC,MAAM,CAACxE,YAAY,CAACqE,SAAS,IAAI,EAAE,CAAC;YAC1D,MAAMM,WAAW,GAAGH,MAAM,CAACxE,YAAY,CAACsE,OAAO,IAAI,GAAG,CAAC;YAEvD,MAAMS,eAAe,GAAGP,MAAM,CAAC6I,eAAe,CAAChJ,SAAS,IAAI,EAAE,CAAC;YAC/D,MAAMW,aAAa,GAAGR,MAAM,CAAC6I,eAAe,CAAC/I,OAAO,IAAI,GAAG,CAAC;;YAE5D;YACAnC,OAAO,CAACC,GAAG,CAAC,iCAAiCiL,eAAe,CAAC3M,SAAS,GAAG,EAAE;cACzE2D,SAAS,EAAE,GAAGU,eAAe,QAAQR,aAAa,EAAE;cACpD+I,UAAU,EAAE,GAAGtI,aAAa,QAAQL,WAAW;YACjD,CAAC,CAAC;YAEF,IAAII,eAAe,KAAKR,aAAa,IACjCS,aAAa,KAAKL,WAAW,EAAE;cACjC;cACAvD,uBAAuB,CAAC;gBACtBC,IAAI,EAAErB,YAAY;gBAClBsB,MAAM,EAAE+L;cACV,CAAC,CAAC;cACFrM,6BAA6B,CAAC,IAAI,CAAC;cACnC;YACF;UACF;;UAEA;UACA,IAAIiM,mBAAmB,CAACI,eAAe,CAAC,EAAE;YACxC;YACA9M,WAAW,CAAC;cACV,GAAGD,QAAQ;cACXI,SAAS,EAAE0M;YACb,CAAC,CAAC;YACFxM,aAAa,CAAC;cACZ,GAAGD,UAAU;cACbwM,eAAe,EAAE;YACnB,CAAC,CAAC;UACJ,CAAC,MAAM;YACL;YACAvM,aAAa,CAAC;cACZ,GAAGD,UAAU;cACbwM,eAAe,EAAE,aAAaH,YAAY,sCAAsCK,eAAe,CAACjJ,aAAa;YAC/G,CAAC,CAAC;UACJ;QACF,CAAC,MAAM;UACL;UACAxD,aAAa,CAAC;YACZ,GAAGD,UAAU;YACbwM,eAAe,EAAE,UAAUH,YAAY;UACzC,CAAC,CAAC;QACJ;MACF,CAAC,MAAM;QACL;QACAzM,WAAW,CAAC;UACV,GAAGD,QAAQ;UACXI,SAAS,EAAE;QACb,CAAC,CAAC;QACFE,aAAa,CAAC;UACZ,GAAGD,UAAU;UACbwM,eAAe,EAAE;QACnB,CAAC,CAAC;MACJ;IACF,CAAC;IAED,oBACE1O,OAAA,CAACzD,GAAG;MAAAqO,QAAA,gBACF5K,OAAA,CAACvD,UAAU;QAACoO,OAAO,EAAC,WAAW;QAACC,EAAE,EAAE;UAAEC,EAAE,EAAE,CAAC;UAAEC,UAAU,EAAE;QAAO,CAAE;QAAAJ,QAAA,EAAC;MAEnE;QAAAK,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAY,CAAC,eAEbpL,OAAA,CAACxD,KAAK;QAACsO,EAAE,EAAE;UAAEO,CAAC,EAAE;QAAE,CAAE;QAAAT,QAAA,gBAClB5K,OAAA,CAACvD,UAAU;UAACoO,OAAO,EAAC,OAAO;UAACC,EAAE,EAAE;YAAEC,EAAE,EAAE;UAAE,CAAE;UAAAH,QAAA,EAAC;QAE3C;UAAAK,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAY,CAAC,EAEZvK,aAAa,gBACZb,OAAA,CAACzD,GAAG;UAACuO,EAAE,EAAE;YAAES,OAAO,EAAE,MAAM;YAAEe,cAAc,EAAE,QAAQ;YAAEC,EAAE,EAAE;UAAE,CAAE;UAAA3B,QAAA,eAC5D5K,OAAA,CAAC7C,gBAAgB;YAACwO,IAAI,EAAE;UAAG;YAAAV,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC3B,CAAC,gBAENpL,OAAA,CAACzD,GAAG;UAAAqO,QAAA,eACF5K,OAAA,CAACzD,GAAG;YAACuO,EAAE,EAAE;cAAES,OAAO,EAAE,MAAM;cAAE+B,QAAQ,EAAE,MAAM;cAAEE,GAAG,EAAE;YAAE,CAAE;YAAA5C,QAAA,gBAErD5K,OAAA,CAACzD,GAAG;cAACuO,EAAE,EAAE;gBAAE8C,IAAI,EAAE,WAAW;gBAAElC,QAAQ,EAAE,OAAO;gBAAEmC,QAAQ,EAAE;cAAQ,CAAE;cAAAjD,QAAA,gBACnE5K,OAAA,CAACvD,UAAU;gBAACoO,OAAO,EAAC,SAAS;gBAACC,EAAE,EAAE;kBAAEE,UAAU,EAAE,MAAM;kBAAEO,OAAO,EAAE,OAAO;kBAAER,EAAE,EAAE;gBAAI,CAAE;gBAAAH,QAAA,EAAC;cAErF;gBAAAK,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAY,CAAC,eACbpL,OAAA,CAACzD,GAAG;gBAACuO,EAAE,EAAE;kBAAES,OAAO,EAAE,MAAM;kBAAEC,UAAU,EAAE,YAAY;kBAAEgC,GAAG,EAAE;gBAAE,CAAE;gBAAA5C,QAAA,eAC7D5K,OAAA,CAACtD,SAAS;kBACRiP,IAAI,EAAC,OAAO;kBACZC,KAAK,EAAC,eAAe;kBACrBf,OAAO,EAAC,UAAU;kBAClBiB,WAAW,EAAC,QAAQ;kBACpB9H,KAAK,EAAE,CAAC,CAAC9B,UAAU,CAACwM,eAAgB;kBACpCI,MAAM,EAAEL,uBAAwB;kBAChC3D,EAAE,EAAE;oBAAE8C,IAAI,EAAE;kBAAE;gBAAE;kBAAA3C,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACjB;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACC,CAAC,EACLlJ,UAAU,CAACwM,eAAe,iBACzB1O,OAAA,CAACvD,UAAU;gBAACoO,OAAO,EAAC,SAAS;gBAACmB,KAAK,EAAC,OAAO;gBAAClB,EAAE,EAAE;kBAAES,OAAO,EAAE,OAAO;kBAAEuC,EAAE,EAAE;gBAAI,CAAE;gBAAAlD,QAAA,EAC3E1I,UAAU,CAACwM;cAAe;gBAAAzD,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACjB,CACb,eACDpL,OAAA,CAACvD,UAAU;gBAACoO,OAAO,EAAC,SAAS;gBAACC,EAAE,EAAE;kBAAES,OAAO,EAAE,OAAO;kBAAEuC,EAAE,EAAE;gBAAI,CAAE;gBAAAlD,QAAA,GAAC,aACpD,eAAA5K,OAAA;kBAAA4K,QAAA,EAAS/I,QAAQ,CAACI,SAAS,IAAI;gBAAG;kBAAAgJ,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAS,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC7C,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACV,CAAC,eAGNpL,OAAA,CAACzD,GAAG;cAACuO,EAAE,EAAE;gBAAE8C,IAAI,EAAE,WAAW;gBAAElC,QAAQ,EAAE;cAAQ,CAAE;cAAAd,QAAA,gBAChD5K,OAAA,CAACvD,UAAU;gBAACoO,OAAO,EAAC,SAAS;gBAACC,EAAE,EAAE;kBAAEE,UAAU,EAAE,MAAM;kBAAEO,OAAO,EAAE,OAAO;kBAAER,EAAE,EAAE;gBAAI,CAAE;gBAAAH,QAAA,EACjFrJ,YAAY,GAAG,4CAA4C,GAAG;cAAsB;gBAAA0J,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC3E,CAAC,eAEbpL,OAAA,CAACnD,WAAW;gBAACkS,SAAS;gBAACpD,IAAI,EAAC,OAAO;gBAAC3H,KAAK,EAAE,CAAC,CAAC9B,UAAU,CAACD,SAAU;gBAAA2I,QAAA,gBAChE5K,OAAA,CAAClD,UAAU;kBAACkS,EAAE,EAAC,qBAAqB;kBAAApE,QAAA,EAAC;gBAAgB;kBAAAK,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAY,CAAC,eAClEpL,OAAA,CAACjD,MAAM;kBACLkS,OAAO,EAAC,qBAAqB;kBAC7BD,EAAE,EAAC,eAAe;kBAClB7G,IAAI,EAAC,WAAW;kBAChBC,KAAK,EAAEvG,QAAQ,CAACI,SAAU;kBAC1B2J,KAAK,EAAC,kBAAkB;kBACxBC,QAAQ,EAAE5D,gBAAiB;kBAC3B6C,EAAE,EAAE;oBAAE6B,SAAS,EAAE;kBAAO,CAAE;kBAAA/B,QAAA,gBAE1B5K,OAAA,CAAChD,QAAQ;oBAACoL,KAAK,EAAC,cAAc;oBAAAwC,QAAA,gBAC5B5K,OAAA;sBAAA4K,QAAA,EAAQ;oBAAY;sBAAAK,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAQ,CAAC,+BAC/B;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAU,CAAC,eACXpL,OAAA,CAAC/C,OAAO;oBAAAgO,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE,CAAC,EACV/J,MAAM,CAACyC,MAAM,GAAG,CAAC,gBAChB9D,OAAA,CAACzD,GAAG;oBAACmQ,SAAS,EAAC,IAAI;oBAAC5B,EAAE,EAAE;sBAAEO,CAAC,EAAE,GAAG;sBAAEyB,OAAO,EAAE;oBAAmB,CAAE;oBAAAlC,QAAA,eAC9D5K,OAAA,CAACvD,UAAU;sBAACoO,OAAO,EAAC,SAAS;sBAACC,EAAE,EAAE;wBAAEE,UAAU,EAAE,MAAM;wBAAEgB,KAAK,EAAE,cAAc;wBAAEI,QAAQ,EAAE;sBAAS,CAAE;sBAAAxB,QAAA,GAAC,sBAC/E,EAACvJ,MAAM,CAACyC,MAAM,EAAC,oCACrC;oBAAA;sBAAAmH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAY;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACV,CAAC,GACJ,IAAI,EACP/J,MAAM,CAAC2L,GAAG,CAAEnK,MAAM,iBACjB7C,OAAA,CAAChD,QAAQ;oBAEPoL,KAAK,EAAEvF,MAAM,CAACZ,SAAU;oBACxBiK,QAAQ,EAAErJ,MAAM,CAAC8C,aAAa,GAAGgD,UAAU,CAAC9G,QAAQ,CAACG,YAAY,IAAI,CAAC,CAAE;oBACxE8I,EAAE,EAAE;sBACF2B,EAAE,EAAE,GAAG;sBACP,gBAAgB,EAAE;wBAAEK,OAAO,EAAE;sBAAgB,CAAC;sBAC9C,sBAAsB,EAAE;wBAAEA,OAAO,EAAE;sBAAgB,CAAC;sBACpDA,OAAO,EAAEvL,YAAY,IACdwE,MAAM,CAAClD,MAAM,CAAC+C,SAAS,IAAI,EAAE,CAAC,CAACI,IAAI,CAAC,CAAC,KAAKD,MAAM,CAACxE,YAAY,CAACqE,SAAS,IAAI,EAAE,CAAC,CAACI,IAAI,CAAC,CAAC,IACrFD,MAAM,CAAClD,MAAM,CAACgD,OAAO,IAAI,GAAG,CAAC,CAACG,IAAI,CAAC,CAAC,KAAKD,MAAM,CAACxE,YAAY,CAACsE,OAAO,IAAI,GAAG,CAAC,CAACG,IAAI,CAAC,CAAC,GACnF,yBAAyB,GAAG;oBACrC,CAAE;oBAAA4E,QAAA,eAEF5K,OAAA,CAACzD,GAAG;sBAACuO,EAAE,EAAE;wBAAES,OAAO,EAAE,MAAM;wBAAE2D,aAAa,EAAE,QAAQ;wBAAE5D,KAAK,EAAE;sBAAO,CAAE;sBAAAV,QAAA,gBACnE5K,OAAA,CAACzD,GAAG;wBAACuO,EAAE,EAAE;0BAAES,OAAO,EAAE,MAAM;0BAAEe,cAAc,EAAE,eAAe;0BAAEd,UAAU,EAAE,QAAQ;0BAAEF,KAAK,EAAE;wBAAO,CAAE;wBAAAV,QAAA,gBACjG5K,OAAA,CAACvD,UAAU;0BAACoO,OAAO,EAAC,OAAO;0BAACC,EAAE,EAAE;4BAAEE,UAAU,EAAE,MAAM;4BAAEoB,QAAQ,EAAE;0BAAS,CAAE;0BAAAxB,QAAA,GACxEvH,eAAe,CAACR,MAAM,CAACZ,SAAS,CAAC,EAAC,KAAG,EAACY,MAAM,CAAC+C,SAAS,IAAI,KAAK;wBAAA;0BAAAqF,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OACtD,CAAC,EACZ7J,YAAY,IACZwE,MAAM,CAAClD,MAAM,CAAC+C,SAAS,IAAI,EAAE,CAAC,CAACI,IAAI,CAAC,CAAC,KAAKD,MAAM,CAACxE,YAAY,CAACqE,SAAS,IAAI,EAAE,CAAC,CAACI,IAAI,CAAC,CAAC,IACrFD,MAAM,CAAClD,MAAM,CAACgD,OAAO,IAAI,GAAG,CAAC,CAACG,IAAI,CAAC,CAAC,KAAKD,MAAM,CAACxE,YAAY,CAACsE,OAAO,IAAI,GAAG,CAAC,CAACG,IAAI,CAAC,CAAC,iBAClFhG,OAAA,CAAC1C,IAAI;0BACHqO,IAAI,EAAC,OAAO;0BACZC,KAAK,EAAC,aAAa;0BACnBI,KAAK,EAAC,SAAS;0BACfnB,OAAO,EAAC,UAAU;0BAClBC,EAAE,EAAE;4BAAEuB,MAAM,EAAE,EAAE;4BAAED,QAAQ,EAAE,QAAQ;4BAAE,kBAAkB,EAAE;8BAAEe,EAAE,EAAE,GAAG;8BAAEV,EAAE,EAAE;4BAAE;0BAAE;wBAAE;0BAAAxB,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAChF,CACF;sBAAA;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACE,CAAC,eACNpL,OAAA,CAACzD,GAAG;wBAACuO,EAAE,EAAE;0BAAES,OAAO,EAAE,MAAM;0BAAEe,cAAc,EAAE,eAAe;0BAAEhB,KAAK,EAAE;wBAAO,CAAE;wBAAAV,QAAA,gBAC3E5K,OAAA,CAACvD,UAAU;0BAACoO,OAAO,EAAC,SAAS;0BAACC,EAAE,EAAE;4BAAEsB,QAAQ,EAAE;0BAAS,CAAE;0BAAAxB,QAAA,EACtD/H,MAAM,CAACgD,OAAO,IAAI;wBAAK;0BAAAoF,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OACd,CAAC,eACbpL,OAAA,CAACvD,UAAU;0BAACoO,OAAO,EAAC,SAAS;0BAACC,EAAE,EAAE;4BAAEE,UAAU,EAAE,MAAM;4BAAEoB,QAAQ,EAAE,QAAQ;4BAAEJ,KAAK,EAAEnJ,MAAM,CAAC8C,aAAa,GAAGgD,UAAU,CAAC9G,QAAQ,CAACG,YAAY,IAAI,CAAC,CAAC,GAAG,YAAY,GAAG;0BAAe,CAAE;0BAAA4I,QAAA,GAChL/H,MAAM,CAAC8C,aAAa,IAAI,CAAC,EAAC,gBAC7B;wBAAA;0BAAAsF,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAY,CAAC;sBAAA;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACV,CAAC;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACH;kBAAC,GAtCDvI,MAAM,CAACZ,SAAS;oBAAAgJ,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAuCb,CACX,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACI,CAAC,EACRlJ,UAAU,CAACD,SAAS,iBACnBjC,OAAA,CAAC5C,cAAc;kBAAAwN,QAAA,EAAE1I,UAAU,CAACD;gBAAS;kBAAAgJ,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAiB,CACvD;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACU,CAAC,EAEb/J,MAAM,CAACyC,MAAM,KAAK,CAAC,IAAI,CAACjD,aAAa,iBACpCb,OAAA,CAACvD,UAAU;gBAACoO,OAAO,EAAC,SAAS;gBAACmB,KAAK,EAAC,cAAc;gBAAClB,EAAE,EAAE;kBAAES,OAAO,EAAE,OAAO;kBAAEuC,EAAE,EAAE;gBAAI,CAAE;gBAAAlD,QAAA,EAAC;cAEtF;gBAAAK,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAY,CACb;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACE,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CACN,EAGA,CAACvK,aAAa,IAAIgB,QAAQ,CAACI,SAAS,iBACnCjC,OAAA,CAACzD,GAAG;UAACuO,EAAE,EAAE;YAAEgD,EAAE,EAAE,CAAC;YAAEzC,CAAC,EAAE,CAAC;YAAEyB,OAAO,EAAE,kBAAkB;YAAEa,YAAY,EAAE,CAAC;YAAEwB,MAAM,EAAE;UAAoB,CAAE;UAAAvE,QAAA,gBAClG5K,OAAA,CAACvD,UAAU;YAACoO,OAAO,EAAC,WAAW;YAAC6C,YAAY;YAAA9C,QAAA,EAAC;UAE7C;YAAAK,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAY,CAAC,EACZ,CAAC,MAAM;YACN,MAAMvI,MAAM,GAAGxB,MAAM,CAAC8F,IAAI,CAACL,CAAC,IAAIA,CAAC,CAAC7E,SAAS,KAAKJ,QAAQ,CAACI,SAAS,CAAC;YACnE,IAAIY,MAAM,EAAE;cACV,oBACE7C,OAAA,CAACpD,IAAI;gBAACwS,SAAS;gBAACC,OAAO,EAAE,CAAE;gBAAAzE,QAAA,gBACzB5K,OAAA,CAACpD,IAAI;kBAAC0S,IAAI;kBAACC,EAAE,EAAE,EAAG;kBAACC,EAAE,EAAE,CAAE;kBAAA5E,QAAA,gBACvB5K,OAAA,CAACvD,UAAU;oBAACoO,OAAO,EAAC,OAAO;oBAAAD,QAAA,gBACzB5K,OAAA;sBAAA4K,QAAA,EAAQ;oBAAO;sBAAAK,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAQ,CAAC,KAAC,EAAC/H,eAAe,CAACR,MAAM,CAACZ,SAAS,CAAC;kBAAA;oBAAAgJ,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACjD,CAAC,eACbpL,OAAA,CAACvD,UAAU;oBAACoO,OAAO,EAAC,OAAO;oBAAAD,QAAA,gBACzB5K,OAAA;sBAAA4K,QAAA,EAAQ;oBAAU;sBAAAK,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAQ,CAAC,KAAC,EAACvI,MAAM,CAAC+C,SAAS,IAAI,KAAK;kBAAA;oBAAAqF,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAC5C,CAAC,eACbpL,OAAA,CAACvD,UAAU;oBAACoO,OAAO,EAAC,OAAO;oBAAAD,QAAA,gBACzB5K,OAAA;sBAAA4K,QAAA,EAAQ;oBAAW;sBAAAK,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAQ,CAAC,KAAC,EAACvI,MAAM,CAACkF,YAAY,IAAI,KAAK,EAAC,KAAG,EAAClF,MAAM,CAACgD,OAAO,IAAI,KAAK;kBAAA;oBAAAoF,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAC5E,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACT,CAAC,eACPpL,OAAA,CAACpD,IAAI;kBAAC0S,IAAI;kBAACC,EAAE,EAAE,EAAG;kBAACC,EAAE,EAAE,CAAE;kBAAA5E,QAAA,gBACvB5K,OAAA,CAACvD,UAAU;oBAACoO,OAAO,EAAC,OAAO;oBAAAD,QAAA,gBACzB5K,OAAA;sBAAA4K,QAAA,EAAQ;oBAAa;sBAAAK,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAQ,CAAC,KAAC,EAACvI,MAAM,CAAC4M,YAAY,IAAI,CAAC,EAAC,IAC3D;kBAAA;oBAAAxE,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAY,CAAC,eACbpL,OAAA,CAACvD,UAAU;oBAACoO,OAAO,EAAC,OAAO;oBAAAD,QAAA,gBACzB5K,OAAA;sBAAA4K,QAAA,EAAQ;oBAAc;sBAAAK,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAQ,CAAC,KAAC,EAACvI,MAAM,CAAC8C,aAAa,IAAI,CAAC,EAAC,IAC7D;kBAAA;oBAAAsF,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAY,CAAC,eACbpL,OAAA,CAACvD,UAAU;oBAACoO,OAAO,EAAC,OAAO;oBAAAD,QAAA,gBACzB5K,OAAA;sBAAA4K,QAAA,EAAQ;oBAAM;sBAAAK,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAQ,CAAC,KAAC,EAACvI,MAAM,CAAC6C,YAAY,IAAI,KAAK;kBAAA;oBAAAuF,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAC3C,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACT,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CAAC;YAEX;YACA,oBACEpL,OAAA,CAACvD,UAAU;cAACoO,OAAO,EAAC,OAAO;cAACmB,KAAK,EAAC,OAAO;cAAApB,QAAA,EAAC;YAE1C;cAAAK,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAY,CAAC;UAEjB,CAAC,EAAE,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACD,CACN,EAEA/J,MAAM,CAACyC,MAAM,KAAK,CAAC,IAAI,CAACjD,aAAa,iBACpCb,OAAA,CAAC9C,KAAK;UAACsP,QAAQ,EAAC,SAAS;UAAC1B,EAAE,EAAE;YAAEgD,EAAE,EAAE;UAAE,CAAE;UAAAlD,QAAA,EAAC;QAEzC;UAAAK,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAO,CACR;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACI,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACL,CAAC;EAEV,CAAC;;EAED;EACA,MAAMsE,WAAW,GAAGA,CAAA,KAAM;IAExB;IACA,IAAInB,YAAY,GAAG,SAAS;IAC5B,IAAIoB,UAAU,GAAG,IAAI;IAErB,IAAI9N,QAAQ,CAACI,SAAS,KAAK,cAAc,EAAE;MACzCsM,YAAY,GAAG,cAAc;IAC/B,CAAC,MAAM,IAAI1M,QAAQ,CAACI,SAAS,EAAE;MAC7BsM,YAAY,GAAGlL,eAAe,CAACxB,QAAQ,CAACI,SAAS,CAAC;MAClD;MACA0N,UAAU,GAAGtO,MAAM,CAAC8F,IAAI,CAACL,CAAC,IAAIA,CAAC,CAAC7E,SAAS,KAAKJ,QAAQ,CAACI,SAAS,CAAC;IACnE;;IAEA;IACA,MAAMiI,kBAAkB,GAAGH,2BAA2B,CAACpB,UAAU,CAAC9G,QAAQ,CAACG,YAAY,CAAC,EAAET,YAAY,CAACsH,aAAa,CAAC;IAErH,oBACE7I,OAAA,CAACzD,GAAG;MAAAqO,QAAA,gBACF5K,OAAA,CAACvD,UAAU;QAACoO,OAAO,EAAC,IAAI;QAAC6C,YAAY;QAAA9C,QAAA,EAAC;MAEtC;QAAAK,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAY,CAAC,eAEbpL,OAAA,CAACxD,KAAK;QAACsO,EAAE,EAAE;UAAEO,CAAC,EAAE;QAAE,CAAE;QAAAT,QAAA,gBAClB5K,OAAA,CAACvD,UAAU;UAACoO,OAAO,EAAC,WAAW;UAAC6C,YAAY;UAAA9C,QAAA,EAAC;QAE7C;UAAAK,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAY,CAAC,eAGbpL,OAAA,CAACb,eAAe;UACdyD,IAAI,EAAErB,YAAa;UACnBqO,OAAO,EAAE,IAAK;UACdvG,KAAK,EAAC;QAAmB;UAAA4B,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC1B,CAAC,eAGFpL,OAAA,CAACzD,GAAG;UAACuO,EAAE,EAAE;YAAEgD,EAAE,EAAE,CAAC;YAAEzC,CAAC,EAAE,CAAC;YAAEyB,OAAO,EAAE,SAAS;YAAEa,YAAY,EAAE;UAAE,CAAE;UAAA/C,QAAA,gBAC5D5K,OAAA,CAACvD,UAAU;YAACoO,OAAO,EAAC,WAAW;YAAC6C,YAAY;YAAC5C,EAAE,EAAE;cAAEE,UAAU,EAAE;YAAO,CAAE;YAAAJ,QAAA,EAAC;UAEzE;YAAAK,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAY,CAAC,eACbpL,OAAA,CAACpD,IAAI;YAACwS,SAAS;YAACC,OAAO,EAAE,CAAE;YAAAzE,QAAA,gBACzB5K,OAAA,CAACpD,IAAI;cAAC0S,IAAI;cAACC,EAAE,EAAE,EAAG;cAACC,EAAE,EAAE,CAAE;cAAA5E,QAAA,gBACvB5K,OAAA,CAACvD,UAAU;gBAACoO,OAAO,EAAC,OAAO;gBAAAD,QAAA,gBACzB5K,OAAA;kBAAA4K,QAAA,EAAQ;gBAAa;kBAAAK,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,KAAC,EAACvJ,QAAQ,CAACG,YAAY,EAAC,IACxD;cAAA;gBAAAiJ,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAY,CAAC,eACbpL,OAAA,CAACvD,UAAU;gBAACoO,OAAO,EAAC,OAAO;gBAAAD,QAAA,gBACzB5K,OAAA;kBAAA4K,QAAA,EAAQ;gBAAoB;kBAAAK,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,KAAC,EAAClB,kBAAkB;cAAA;gBAAAe,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC/C,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACT,CAAC,eACPpL,OAAA,CAACpD,IAAI;cAAC0S,IAAI;cAACC,EAAE,EAAE,EAAG;cAACC,EAAE,EAAE,CAAE;cAAA5E,QAAA,gBACvB5K,OAAA,CAACvD,UAAU;gBAACoO,OAAO,EAAC,OAAO;gBAAAD,QAAA,gBACzB5K,OAAA;kBAAA4K,QAAA,EAAQ;gBAAiB;kBAAAK,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,KAAC,EAACmD,YAAY;cAAA;gBAAAtD,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACtC,CAAC,EACZuE,UAAU,iBACT3P,OAAA,CAACvD,UAAU;gBAACoO,OAAO,EAAC,OAAO;gBAAAD,QAAA,gBACzB5K,OAAA;kBAAA4K,QAAA,EAAQ;gBAAqB;kBAAAK,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,KAAC,EAACuE,UAAU,CAAChK,aAAa,EAAC,IACnE;cAAA;gBAAAsF,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAY,CACb;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACG,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACJ,CAAC,EAELuE,UAAU,IAAIhH,UAAU,CAAC9G,QAAQ,CAACG,YAAY,CAAC,GAAG2G,UAAU,CAACgH,UAAU,CAAChK,aAAa,CAAC,IAAI,CAACoD,iBAAiB,iBAC3G/I,OAAA,CAAC9C,KAAK;UAACsP,QAAQ,EAAC,SAAS;UAAC1B,EAAE,EAAE;YAAEgD,EAAE,EAAE;UAAE,CAAE;UAAAlD,QAAA,gBACtC5K,OAAA;YAAA4K,QAAA,EAAQ;UAAW;YAAAK,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC,qBAAiB,EAACvJ,QAAQ,CAACG,YAAY,EAAC,4CAA0C,EAAC2N,UAAU,CAAChK,aAAa,EAAC,gDAE1I;QAAA;UAAAsF,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAO,CACR,eAEDpL,OAAA,CAAC9C,KAAK;UAACsP,QAAQ,EAAC,MAAM;UAAC1B,EAAE,EAAE;YAAEgD,EAAE,EAAE;UAAE,CAAE;UAAAlD,QAAA,GAAC,8EAEpC,EAAC/I,QAAQ,CAACI,SAAS,IAAIJ,QAAQ,CAACI,SAAS,KAAK,cAAc,IAAI,gFAAgF;QAAA;UAAAgJ,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC3I,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACL,CAAC;EAEV,CAAC;;EAED;EACA,MAAMyE,cAAc,GAAIzB,IAAI,IAAK;IAC/B,QAAQA,IAAI;MACV,KAAK,CAAC;QACJ,OAAOzD,WAAW,CAAC,CAAC;MAAE;MACxB,KAAK,CAAC;QACJ,OAAO0D,WAAW,CAAC,CAAC;MAAE;MACxB,KAAK,CAAC;QACJ,OAAOhB,WAAW,CAAC,CAAC;MAAE;MACxB,KAAK,CAAC;QACJ,OAAOqC,WAAW,CAAC,CAAC;MAAE;MACxB;QACE,OAAO,mBAAmB;IAC9B;EACF,CAAC;;EAED;EACA,MAAMI,4BAA4B,GAAGA,CAAA,KAAM;IACzC/M,wBAAwB,CAAC,KAAK,CAAC;IAC/BE,kBAAkB,CAAC,IAAI,CAAC;EAC1B,CAAC;;EAID;EACA,MAAM8M,gBAAgB,GAAGA,CAAA,KAAM;IAC7B,IAAI/M,eAAe,EAAE;MACnBxC,QAAQ,CAAC,mCAAmCJ,UAAU,IAAI4C,eAAe,CAACjB,OAAO,EAAE,CAAC;IACtF;IACA+N,4BAA4B,CAAC,CAAC;EAChC,CAAC;;EAED;EACA,MAAME,wBAAwB,GAAGA,CAAA,KAAM;IACrCF,4BAA4B,CAAC,CAAC;IAC9B;IACAtO,eAAe,CAAC,IAAI,CAAC;IACrBE,cAAc,CAAC,EAAE,CAAC;IAClBR,oBAAoB,CAAC,KAAK,CAAC;EAC7B,CAAC;;EAED;EACA,MAAM+O,iCAAiC,GAAGA,CAAA,KAAM;IAC9C1N,6BAA6B,CAAC,KAAK,CAAC;IACpCI,uBAAuB,CAAC;MAAEC,IAAI,EAAE,IAAI;MAAEC,MAAM,EAAE;IAAK,CAAC,CAAC;EACvD,CAAC;;EAED;EACA,MAAMqN,2BAA2B,GAAG,MAAAA,CAAA,KAAY;IAC9C,MAAM;MAAEtN,IAAI;MAAEC;IAAO,CAAC,GAAGH,oBAAoB;IAC7C,IAAI,CAACE,IAAI,IAAI,CAACC,MAAM,EAAE;MACpBa,OAAO,CAACM,KAAK,CAAC,uCAAuC,EAAE;QAAEpB,IAAI;QAAEC;MAAO,CAAC,CAAC;MACxEvC,OAAO,CAAC,sCAAsC,CAAC;MAC/C;IACF;IAEA,IAAI;MACFI,UAAU,CAAC,IAAI,CAAC;MAChBgD,OAAO,CAACC,GAAG,CAAC,0CAA0Cf,IAAI,CAACb,OAAO,iCAAiCc,MAAM,CAACZ,SAAS,EAAE,CAAC;MACtHyB,OAAO,CAACC,GAAG,CAAC,sCAAsC,EAAEf,IAAI,CAAC;MACzDc,OAAO,CAACC,GAAG,CAAC,cAAc,EAAEd,MAAM,CAAC;;MAEnC;MACA,MAAM7D,WAAW,CAACmR,0BAA0B,CAAC/P,UAAU,EAAEwC,IAAI,CAACb,OAAO,EAAEc,MAAM,CAACZ,SAAS,CAAC;;MAExF;MACA,MAAM4F,WAAW,GAAG,MAAM7I,WAAW,CAACoR,WAAW,CAAChQ,UAAU,EAAEwC,IAAI,CAACb,OAAO,CAAC;MAC3E2B,OAAO,CAACC,GAAG,CAAC,kCAAkC,EAAEkE,WAAW,CAAC;MAC5DrG,eAAe,CAACqG,WAAW,CAAC;;MAE5B;MACA/F,WAAW,CAAC;QACV,GAAGD,QAAQ;QACXI,SAAS,EAAEY,MAAM,CAACZ;MACpB,CAAC,CAAC;;MAEF;MACA,MAAMmB,UAAU,CAAC,CAAC;MAElB/C,SAAS,CAAC,4BAA4BuC,IAAI,CAACb,OAAO,6CAA6Cc,MAAM,CAACZ,SAAS,EAAE,CAAC;MAClHgO,iCAAiC,CAAC,CAAC;IACrC,CAAC,CAAC,OAAOjM,KAAK,EAAE;MACdN,OAAO,CAACM,KAAK,CAAC,iEAAiE,EAAEA,KAAK,CAAC;MACvF1D,OAAO,CAAC,kEAAkE,IAAI0D,KAAK,CAACqB,MAAM,IAAIrB,KAAK,CAACI,OAAO,IAAI,oBAAoB,CAAC,CAAC;IACvI,CAAC,SAAS;MACR1D,UAAU,CAAC,KAAK,CAAC;IACnB;EACF,CAAC;;EAED;EACA,MAAM2P,uBAAuB,GAAGA,CAAA,KAAM;IACpCJ,iCAAiC,CAAC,CAAC;IACnC;IACAnO,WAAW,CAAC;MACV,GAAGD,QAAQ;MACXI,SAAS,EAAE;IACb,CAAC,CAAC;EACJ,CAAC;EAED,oBACEjC,OAAA,CAACzD,GAAG;IAAAqO,QAAA,gBAEF5K,OAAA,CAACxD,KAAK;MAACsO,EAAE,EAAE;QAAEO,CAAC,EAAE,CAAC;QAAEN,EAAE,EAAE;MAAE,CAAE;MAAAH,QAAA,gBACzB5K,OAAA,CAACvD,UAAU;QAACoO,OAAO,EAAC,IAAI;QAAC6C,YAAY;QAAA9C,QAAA,EAAC;MAEtC;QAAAK,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAY,CAAC,eACbpL,OAAA,CAACpD,IAAI;QAACwS,SAAS;QAACC,OAAO,EAAE,CAAE;QAAC7D,UAAU,EAAC,QAAQ;QAAAZ,QAAA,gBAC7C5K,OAAA,CAACpD,IAAI;UAAC0S,IAAI;UAACC,EAAE,EAAE,CAAE;UAAA3E,QAAA,eACf5K,OAAA,CAACtD,SAAS;YACRqS,SAAS;YACTnD,KAAK,EAAC,SAAS;YACff,OAAO,EAAC,UAAU;YAClBzC,KAAK,EAAE3G,WAAY;YACnBoK,QAAQ,EAAG3D,CAAC,IAAKxG,cAAc,CAACwG,CAAC,CAACG,MAAM,CAACD,KAAK,CAAE;YAChD0D,WAAW,EAAC;UAAyC;YAAAb,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACtD;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACE,CAAC,eACPpL,OAAA,CAACpD,IAAI;UAAC0S,IAAI;UAACC,EAAE,EAAE,CAAE;UAAA3E,QAAA,eACf5K,OAAA,CAACrD,MAAM;YACLoS,SAAS;YACTlE,OAAO,EAAC,WAAW;YACnBmB,KAAK,EAAC,SAAS;YACfC,OAAO,EAAEjF,oBAAqB;YAC9BkF,QAAQ,EAAEvL,WAAW,IAAI,CAACc,WAAW,CAACuE,IAAI,CAAC,CAAE;YAC7CmG,SAAS,EAAExL,WAAW,gBAAGX,OAAA,CAAC7C,gBAAgB;cAACwO,IAAI,EAAE;YAAG;cAAAV,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,gBAAGpL,OAAA,CAAC1B,UAAU;cAAA2M,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAE;YAAAR,QAAA,EAC1E;UAED;YAAAK,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACL,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACF,CAAC,EAGPnK,iBAAiB,IAAIF,aAAa,CAAC+C,MAAM,GAAG,CAAC,iBAC5C9D,OAAA,CAACxD,KAAK;MAACsO,EAAE,EAAE;QAAEO,CAAC,EAAE,CAAC;QAAEN,EAAE,EAAE;MAAE,CAAE;MAAAH,QAAA,gBACzB5K,OAAA,CAACvD,UAAU;QAACoO,OAAO,EAAC,IAAI;QAAC6C,YAAY;QAAA9C,QAAA,EAAC;MAEtC;QAAAK,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAY,CAAC,eACbpL,OAAA,CAAClC,cAAc;QAAA8M,QAAA,eACb5K,OAAA,CAACrC,KAAK;UAACgO,IAAI,EAAC,OAAO;UAAAf,QAAA,gBACjB5K,OAAA,CAACjC,SAAS;YAAA6M,QAAA,eACR5K,OAAA,CAAChC,QAAQ;cAAC8M,EAAE,EAAE;gBAAEgC,OAAO,EAAE;cAAU,CAAE;cAAAlC,QAAA,gBACnC5K,OAAA,CAACnC,SAAS;gBAAA+M,QAAA,EAAC;cAAO;gBAAAK,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAW,CAAC,eAC9BpL,OAAA,CAACnC,SAAS;gBAAA+M,QAAA,EAAC;cAAS;gBAAAK,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAW,CAAC,eAChCpL,OAAA,CAACnC,SAAS;gBAAA+M,QAAA,EAAC;cAAU;gBAAAK,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAW,CAAC,eACjCpL,OAAA,CAACnC,SAAS;gBAAA+M,QAAA,EAAC;cAAU;gBAAAK,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAW,CAAC,eACjCpL,OAAA,CAACnC,SAAS;gBAAA+M,QAAA,EAAC;cAAa;gBAAAK,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAW,CAAC,eACpCpL,OAAA,CAACnC,SAAS;gBAAA+M,QAAA,EAAC;cAAK;gBAAAK,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAW,CAAC,eAC5BpL,OAAA,CAACnC,SAAS;gBAAA+M,QAAA,EAAC;cAAM;gBAAAK,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAW,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACrB;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACF,CAAC,eACZpL,OAAA,CAACpC,SAAS;YAAAgN,QAAA,EACP7J,aAAa,CAACiM,GAAG,CAAEpK,IAAI,iBACtB5C,OAAA,CAAChC,QAAQ;cAAA4M,QAAA,gBACP5K,OAAA,CAACnC,SAAS;gBAAA+M,QAAA,EAAEhI,IAAI,CAACb;cAAO;gBAAAkJ,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAY,CAAC,eACrCpL,OAAA,CAACnC,SAAS;gBAAA+M,QAAA,EAAEhI,IAAI,CAACgD,SAAS,IAAI;cAAK;gBAAAqF,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAY,CAAC,eAChDpL,OAAA,CAACnC,SAAS;gBAAA+M,QAAA,EAAEhI,IAAI,CAACiD,OAAO,IAAI;cAAK;gBAAAoF,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAY,CAAC,eAC9CpL,OAAA,CAACnC,SAAS;gBAAA+M,QAAA,GAAC,MAAI,EAAChI,IAAI,CAAC0N,mBAAmB,IAAI,KAAK,eAACtQ,OAAA;kBAAAiL,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAI,CAAC,OAAG,EAACxI,IAAI,CAAC2N,iBAAiB,IAAI,KAAK;cAAA;gBAAAtF,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAY,CAAC,eACvGpL,OAAA,CAACnC,SAAS;gBAAA+M,QAAA,GAAEhI,IAAI,CAACiG,aAAa,IAAI,KAAK,EAAC,IAAE;cAAA;gBAAAoC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAW,CAAC,eACtDpL,OAAA,CAACnC,SAAS;gBAAA+M,QAAA,eACR5K,OAAA,CAAC1C,IAAI;kBACHsO,KAAK,EAAEhJ,IAAI,CAACwE,mBAAmB,IAAI,KAAM;kBACzCuE,IAAI,EAAC,OAAO;kBACZK,KAAK,EAAErM,kBAAkB,CAACiD,IAAI,CAACwE,mBAAmB,CAAE;kBACpDyD,OAAO,EAAC;gBAAU;kBAAAI,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACnB;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACO,CAAC,eACZpL,OAAA,CAACnC,SAAS;gBAAA+M,QAAA,eACR5K,OAAA,CAACrD,MAAM;kBACLgP,IAAI,EAAC,OAAO;kBACZd,OAAO,EAAC,WAAW;kBACnBmB,KAAK,EAAC,SAAS;kBACfC,OAAO,EAAEA,CAAA,KAAM1E,gBAAgB,CAAC3E,IAAI,CAAE;kBACtCsJ,QAAQ,EAAExM,gBAAgB,CAACkD,IAAI,CAAE;kBAAAgI,QAAA,EAClC;gBAED;kBAAAK,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACA,CAAC;YAAA,GAxBCxI,IAAI,CAACb,OAAO;cAAAkJ,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAyBjB,CACX;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACO,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACP;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACM,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACZ,CACR,EAGA7J,YAAY,iBACXvB,OAAA,CAACxD,KAAK;MAACsO,EAAE,EAAE;QAAEO,CAAC,EAAE;MAAE,CAAE;MAAAT,QAAA,gBAClB5K,OAAA,CAACvD,UAAU;QAACoO,OAAO,EAAC,IAAI;QAAC6C,YAAY;QAAA9C,QAAA,EAAC;MAEtC;QAAAK,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAY,CAAC,eAGbpL,OAAA,CAACzD,GAAG;QAACuO,EAAE,EAAE;UAAEO,CAAC,EAAE,CAAC;UAAEyB,OAAO,EAAE,SAAS;UAAEa,YAAY,EAAE,CAAC;UAAE5C,EAAE,EAAE;QAAE,CAAE;QAAAH,QAAA,gBAC5D5K,OAAA,CAACvD,UAAU;UAACoO,OAAO,EAAC,WAAW;UAAC6C,YAAY;UAAC5C,EAAE,EAAE;YAAEE,UAAU,EAAE,MAAM;YAAEgB,KAAK,EAAE;UAAe,CAAE;UAAApB,QAAA,GAAC,oBAC5E,EAACrJ,YAAY,CAACQ,OAAO;QAAA;UAAAkJ,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC7B,CAAC,eACbpL,OAAA,CAACpD,IAAI;UAACwS,SAAS;UAACC,OAAO,EAAE,CAAE;UAAAzE,QAAA,gBACzB5K,OAAA,CAACpD,IAAI;YAAC0S,IAAI;YAACC,EAAE,EAAE,EAAG;YAACC,EAAE,EAAE,CAAE;YAAA5E,QAAA,gBACvB5K,OAAA,CAACvD,UAAU;cAACoO,OAAO,EAAC,OAAO;cAAAD,QAAA,gBAAC5K,OAAA;gBAAA4K,QAAA,EAAQ;cAAU;gBAAAK,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC,KAAC,EAAC7J,YAAY,CAACqE,SAAS,IAAI,KAAK;YAAA;cAAAqF,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAa,CAAC,eACtGpL,OAAA,CAACvD,UAAU;cAACoO,OAAO,EAAC,OAAO;cAAAD,QAAA,gBAAC5K,OAAA;gBAAA4K,QAAA,EAAQ;cAAW;gBAAAK,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC,KAAC,EAAC7J,YAAY,CAACsE,OAAO,IAAI,KAAK;YAAA;cAAAoF,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAa,CAAC,eACrGpL,OAAA,CAACvD,UAAU;cAACoO,OAAO,EAAC,OAAO;cAAAD,QAAA,gBAAC5K,OAAA;gBAAA4K,QAAA,EAAQ;cAAc;gBAAAK,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC,KAAC,EAAC7J,YAAY,CAACsH,aAAa,IAAI,KAAK,EAAC,IAAE;YAAA;cAAAoC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAY,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC5G,CAAC,eACPpL,OAAA,CAACpD,IAAI;YAAC0S,IAAI;YAACC,EAAE,EAAE,EAAG;YAACC,EAAE,EAAE,CAAE;YAAA5E,QAAA,gBACvB5K,OAAA,CAACvD,UAAU;cAACoO,OAAO,EAAC,OAAO;cAAAD,QAAA,gBAAC5K,OAAA;gBAAA4K,QAAA,EAAQ;cAAoB;gBAAAK,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC,KAAC,EAAC7J,YAAY,CAAC+O,mBAAmB,IAAI,KAAK;YAAA;cAAArF,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAa,CAAC,eAC1HpL,OAAA,CAACvD,UAAU;cAACoO,OAAO,EAAC,OAAO;cAAAD,QAAA,gBAAC5K,OAAA;gBAAA4K,QAAA,EAAQ;cAAkB;gBAAAK,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC,KAAC,EAAC7J,YAAY,CAACgP,iBAAiB,IAAI,KAAK;YAAA;cAAAtF,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAa,CAAC,eACtHpL,OAAA,CAACvD,UAAU;cAACoO,OAAO,EAAC,OAAO;cAAAD,QAAA,gBACzB5K,OAAA;gBAAA4K,QAAA,EAAQ;cAAM;gBAAAK,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC,eACvBpL,OAAA,CAAC1C,IAAI;gBACHsO,KAAK,EAAErK,YAAY,CAAC6F,mBAAmB,IAAI,KAAM;gBACjDuE,IAAI,EAAC,OAAO;gBACZK,KAAK,EAAErM,kBAAkB,CAAC4B,YAAY,CAAC6F,mBAAmB,CAAE;gBAC5DyD,OAAO,EAAC,UAAU;gBAClBC,EAAE,EAAE;kBAAE2C,EAAE,EAAE;gBAAE;cAAE;gBAAAxC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACf,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACQ,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACT,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACJ,CAAC,eAENpL,OAAA,CAACpD,IAAI;QAACwS,SAAS;QAACC,OAAO,EAAE,CAAE;QAAAzE,QAAA,gBAEzB5K,OAAA,CAACpD,IAAI;UAAC0S,IAAI;UAACC,EAAE,EAAE,EAAG;UAACC,EAAE,EAAE,CAAE;UAAA5E,QAAA,gBACvB5K,OAAA,CAACvD,UAAU;YAACoO,OAAO,EAAC,WAAW;YAAC6C,YAAY;YAAC5C,EAAE,EAAE;cAAEE,UAAU,EAAE;YAAO,CAAE;YAAAJ,QAAA,EAAC;UAEzE;YAAAK,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAY,CAAC,eACbpL,OAAA,CAACtD,SAAS;YACRiP,IAAI,EAAC,OAAO;YACZoD,SAAS;YACTnD,KAAK,EAAC,cAAc;YACpBf,OAAO,EAAC,UAAU;YAClB1C,IAAI,EAAC,cAAc;YACnB4F,IAAI,EAAC,QAAQ;YACb3F,KAAK,EAAEvG,QAAQ,CAACG,YAAa;YAC7B6J,QAAQ,EAAE5D,gBAAiB;YAC3BjE,KAAK,EAAE,CAAC,CAAC9B,UAAU,CAACF,YAAa;YACjCgM,UAAU,EAAE9L,UAAU,CAACF,YAAY,IAAII,YAAY,CAACJ,YAAa;YACjEiM,mBAAmB,EAAE;cACnBnD,EAAE,EAAE;gBAAEkB,KAAK,EAAE5J,YAAY,CAACJ,YAAY,IAAI,CAACE,UAAU,CAACF,YAAY,GAAG,cAAc,GAAG;cAAa;YACrG,CAAE;YACF8I,EAAE,EAAE;cAAEC,EAAE,EAAE;YAAE;UAAE;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACf,CAAC,EACDhJ,YAAY,CAACJ,YAAY,IAAI,CAACE,UAAU,CAACF,YAAY,iBACpDhC,OAAA,CAAC9C,KAAK;YAACsP,QAAQ,EAAC,SAAS;YAAC1B,EAAE,EAAE;cAAEC,EAAE,EAAE;YAAE,CAAE;YAAAH,QAAA,EACrCxI,YAAY,CAACJ;UAAY;YAAAiJ,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACrB,CACR;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACG,CAAC,eAGPpL,OAAA,CAACpD,IAAI;UAAC0S,IAAI;UAACC,EAAE,EAAE,EAAG;UAACC,EAAE,EAAE,CAAE;UAAA5E,QAAA,gBACvB5K,OAAA,CAACvD,UAAU;YAACoO,OAAO,EAAC,WAAW;YAAC6C,YAAY;YAAC5C,EAAE,EAAE;cAAEE,UAAU,EAAE;YAAO,CAAE;YAAAJ,QAAA,EAAC;UAEzE;YAAAK,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAY,CAAC,EACZvK,aAAa,gBACZb,OAAA,CAACzD,GAAG;YAACuO,EAAE,EAAE;cAAES,OAAO,EAAE,MAAM;cAAEe,cAAc,EAAE,QAAQ;cAAEC,EAAE,EAAE;YAAE,CAAE;YAAA3B,QAAA,eAC5D5K,OAAA,CAAC7C,gBAAgB;cAAA8N,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACjB,CAAC,gBAENpL,OAAA,CAACzD,GAAG;YAAAqO,QAAA,gBACF5K,OAAA,CAACvD,UAAU;cAACoO,OAAO,EAAC,WAAW;cAAC6C,YAAY;cAAC5C,EAAE,EAAE;gBAAEkB,KAAK,EAAE,cAAc;gBAAEhB,UAAU,EAAE,MAAM;gBAAED,EAAE,EAAE;cAAE,CAAE;cAAAH,QAAA,EACnGrJ,YAAY,GAAG,4CAA4C,GAAG;YAAsB;cAAA0J,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC3E,CAAC,eAEbpL,OAAA,CAACnD,WAAW;cAACkS,SAAS;cAACpD,IAAI,EAAC,OAAO;cAAC3H,KAAK,EAAE,CAAC,CAAC9B,UAAU,CAACD,SAAU;cAAA2I,QAAA,gBAChE5K,OAAA,CAAClD,UAAU;gBAACkS,EAAE,EAAC,qBAAqB;gBAAApE,QAAA,EAAC;cAAgB;gBAAAK,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAY,CAAC,eAClEpL,OAAA,CAACjD,MAAM;gBACLkS,OAAO,EAAC,qBAAqB;gBAC7BD,EAAE,EAAC,eAAe;gBAClB7G,IAAI,EAAC,WAAW;gBAChBC,KAAK,EAAEvG,QAAQ,CAACI,SAAU;gBAC1B2J,KAAK,EAAC,kBAAkB;gBACxBC,QAAQ,EAAE5D,gBAAiB;gBAAA2C,QAAA,gBAE3B5K,OAAA,CAAChD,QAAQ;kBAACoL,KAAK,EAAC,cAAc;kBAAAwC,QAAA,gBAC5B5K,OAAA;oBAAA4K,QAAA,EAAQ;kBAAY;oBAAAK,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAQ,CAAC,+BAC/B;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAU,CAAC,eACXpL,OAAA,CAAC/C,OAAO;kBAAAgO,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAC,EACV/J,MAAM,CAACyC,MAAM,GAAG,CAAC,gBAChB9D,OAAA,CAACzD,GAAG;kBAACmQ,SAAS,EAAC,IAAI;kBAAC5B,EAAE,EAAE;oBAAEO,CAAC,EAAE,CAAC;oBAAEyB,OAAO,EAAE;kBAAmB,CAAE;kBAAAlC,QAAA,eAC5D5K,OAAA,CAACvD,UAAU;oBAACoO,OAAO,EAAC,SAAS;oBAACC,EAAE,EAAE;sBAAEE,UAAU,EAAE,MAAM;sBAAEgB,KAAK,EAAE;oBAAe,CAAE;oBAAApB,QAAA,GAC7EvJ,MAAM,CAACyC,MAAM,EAAC,qBACf,EAACvC,YAAY,iBACXvB,OAAA,CAAAE,SAAA;sBAAA0K,QAAA,GAAE,IACA,eAAA5K,OAAA;wBAAMuN,KAAK,EAAE;0BAAEvB,KAAK,EAAE3K,MAAM,CAACmP,IAAI,CAAC3N,MAAM,IACtCA,MAAM,CAAC+C,SAAS,KAAKrE,YAAY,CAACqE,SAAS,IAC3CG,MAAM,CAAClD,MAAM,CAACgD,OAAO,CAAC,KAAKE,MAAM,CAACxE,YAAY,CAACsE,OAAO,CAAC,CAAC,GAAG,OAAO,GAAG;wBAAS,CAAE;wBAAA+E,QAAA,GAC/EvJ,MAAM,CAACoE,MAAM,CAAC5C,MAAM,IACnBA,MAAM,CAAC+C,SAAS,KAAKrE,YAAY,CAACqE,SAAS,IAC3CG,MAAM,CAAClD,MAAM,CAACgD,OAAO,CAAC,KAAKE,MAAM,CAACxE,YAAY,CAACsE,OAAO,CACxD,CAAC,CAAC/B,MAAM,EAAC,cACX;sBAAA;wBAAAmH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAM,CAAC;oBAAA,eACP,CACH;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACS;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACV,CAAC,GACJ,IAAI,EACP/J,MAAM,CAAC2L,GAAG,CAAEnK,MAAM,iBACjB7C,OAAA,CAAChD,QAAQ;kBAEPoL,KAAK,EAAEvF,MAAM,CAACZ,SAAU;kBACxBiK,QAAQ,EAAErJ,MAAM,CAAC8C,aAAa,GAAGgD,UAAU,CAAC9G,QAAQ,CAACG,YAAY,IAAI,CAAC,CAAE;kBACxE8I,EAAE,EAAE;oBACF,gBAAgB,EAAE;sBAAEgC,OAAO,EAAE;oBAAgB,CAAC;oBAC9C,sBAAsB,EAAE;sBAAEA,OAAO,EAAE;oBAAgB,CAAC;oBACpDA,OAAO,EAAEvL,YAAY,IACdsB,MAAM,CAAC+C,SAAS,KAAKrE,YAAY,CAACqE,SAAS,IAC3CG,MAAM,CAAClD,MAAM,CAACgD,OAAO,CAAC,KAAKE,MAAM,CAACxE,YAAY,CAACsE,OAAO,CAAC,GACvD,yBAAyB,GAAG,SAAS;oBAC5CsJ,MAAM,EAAE5N,YAAY,IACbsB,MAAM,CAAC+C,SAAS,KAAKrE,YAAY,CAACqE,SAAS,IAC3CG,MAAM,CAAClD,MAAM,CAACgD,OAAO,CAAC,KAAKE,MAAM,CAACxE,YAAY,CAACsE,OAAO,CAAC,GACvD,mBAAmB,GAAG,MAAM;oBACnC8H,YAAY,EAAE,KAAK;oBACnB8C,MAAM,EAAE;kBACV,CAAE;kBAAA7F,QAAA,eAEF5K,OAAA,CAACzD,GAAG;oBAACuO,EAAE,EAAE;sBAAES,OAAO,EAAE,MAAM;sBAAE2D,aAAa,EAAE,QAAQ;sBAAE5D,KAAK,EAAE;oBAAO,CAAE;oBAAAV,QAAA,gBACnE5K,OAAA,CAACzD,GAAG;sBAACuO,EAAE,EAAE;wBAAES,OAAO,EAAE,MAAM;wBAAEe,cAAc,EAAE,eAAe;wBAAEd,UAAU,EAAE,QAAQ;wBAAEF,KAAK,EAAE;sBAAO,CAAE;sBAAAV,QAAA,gBACjG5K,OAAA,CAACvD,UAAU;wBAACoO,OAAO,EAAC,OAAO;wBAACC,EAAE,EAAE;0BAAEE,UAAU,EAAE;wBAAO,CAAE;wBAAAJ,QAAA,GACpDvH,eAAe,CAACR,MAAM,CAACZ,SAAS,CAAC,EAAC,KAAG,EAACY,MAAM,CAAC+C,SAAS,IAAI,KAAK;sBAAA;wBAAAqF,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACtD,CAAC,EACZ7J,YAAY,IACZsB,MAAM,CAAC+C,SAAS,KAAKrE,YAAY,CAACqE,SAAS,IAC3CG,MAAM,CAAClD,MAAM,CAACgD,OAAO,CAAC,KAAKE,MAAM,CAACxE,YAAY,CAACsE,OAAO,CAAC,iBACtD7F,OAAA,CAAC1C,IAAI;wBACHqO,IAAI,EAAC,OAAO;wBACZC,KAAK,EAAC,aAAa;wBACnBI,KAAK,EAAC,SAAS;wBACfnB,OAAO,EAAC,UAAU;wBAClBC,EAAE,EAAE;0BAAEuB,MAAM,EAAE,EAAE;0BAAED,QAAQ,EAAE;wBAAS;sBAAE;wBAAAnB,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACxC,CACF;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACE,CAAC,eACNpL,OAAA,CAACzD,GAAG;sBAACuO,EAAE,EAAE;wBAAES,OAAO,EAAE,MAAM;wBAAEe,cAAc,EAAE,eAAe;wBAAEhB,KAAK,EAAE;sBAAO,CAAE;sBAAAV,QAAA,gBAC3E5K,OAAA,CAACvD,UAAU;wBAACoO,OAAO,EAAC,SAAS;wBAAAD,QAAA,EAC1B/H,MAAM,CAACgD,OAAO,IAAI;sBAAK;wBAAAoF,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACd,CAAC,eACbpL,OAAA,CAACvD,UAAU;wBAACoO,OAAO,EAAC,SAAS;wBAACC,EAAE,EAAE;0BAAEE,UAAU,EAAE,MAAM;0BAAEgB,KAAK,EAAEnJ,MAAM,CAAC8C,aAAa,GAAGgD,UAAU,CAAC9G,QAAQ,CAACG,YAAY,IAAI,CAAC,CAAC,GAAG,YAAY,GAAG;wBAAe,CAAE;wBAAA4I,QAAA,GAC5J/H,MAAM,CAAC8C,aAAa,IAAI,CAAC,EAAC,gBAC7B;sBAAA;wBAAAsF,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAY,CAAC;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACV,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACH;gBAAC,GA3CDvI,MAAM,CAACZ,SAAS;kBAAAgJ,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OA4Cb,CACX,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACI,CAAC,eACTpL,OAAA,CAAC5C,cAAc;gBAAAwN,QAAA,EACZ1I,UAAU,CAACD,SAAS,IAAI;cAAsD;gBAAAgJ,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACjE,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACN,CAAC,EAEb/J,MAAM,CAACyC,MAAM,KAAK,CAAC,IAAI,CAACjD,aAAa,iBACpCb,OAAA,CAAC9C,KAAK;cAACsP,QAAQ,EAAC,SAAS;cAAC1B,EAAE,EAAE;gBAAEgD,EAAE,EAAE,CAAC;gBAAE1B,QAAQ,EAAE;cAAS,CAAE;cAAAxB,QAAA,EAAC;YAE7D;cAAAK,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CACR,EAEA/J,MAAM,CAACyC,MAAM,GAAG,CAAC,IAAIvC,YAAY,IAAI,CAACF,MAAM,CAACmP,IAAI,CAAC3N,MAAM;YACvD;YACAA,MAAM,CAAC+C,SAAS,KAAKrE,YAAY,CAACqE,SAAS,IAC3CG,MAAM,CAAClD,MAAM,CAACgD,OAAO,CAAC,KAAKE,MAAM,CAACxE,YAAY,CAACsE,OAAO,CACxD,CAAC,iBACC7F,OAAA,CAAC9C,KAAK;cAACsP,QAAQ,EAAC,MAAM;cAAC1B,EAAE,EAAE;gBAAEgD,EAAE,EAAE,CAAC;gBAAE1B,QAAQ,EAAE;cAAS,CAAE;cAAAxB,QAAA,gBACvD5K,OAAA;gBAAA4K,QAAA,EAAQ;cAAmC;gBAAAK,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC,qBACpD,eAAApL,OAAA;gBAAIuN,KAAK,EAAE;kBAAEkD,MAAM,EAAE,OAAO;kBAAEC,WAAW,EAAE;gBAAO,CAAE;gBAAA9F,QAAA,gBAClD5K,OAAA;kBAAA4K,QAAA,EAAI;gBAA2D;kBAAAK,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAI,CAAC,eACpEpL,OAAA;kBAAA4K,QAAA,EAAI;gBAAsG;kBAAAK,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAI,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC7G,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACA,CACR;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACE,CACN,EAGA,CAACvK,aAAa,IAAIgB,QAAQ,CAACI,SAAS,IAAIJ,QAAQ,CAACI,SAAS,KAAK,cAAc,IAAI,CAAC,MAAM;YACvF,MAAMY,MAAM,GAAGxB,MAAM,CAAC8F,IAAI,CAACL,CAAC,IAAIA,CAAC,CAAC7E,SAAS,KAAKJ,QAAQ,CAACI,SAAS,CAAC;YACnE,IAAIY,MAAM,EAAE;cACV,oBACE7C,OAAA,CAACzD,GAAG;gBAACuO,EAAE,EAAE;kBAAEgD,EAAE,EAAE,CAAC;kBAAEzC,CAAC,EAAE,CAAC;kBAAEyB,OAAO,EAAE,SAAS;kBAAEa,YAAY,EAAE;gBAAE,CAAE;gBAAA/C,QAAA,gBAC5D5K,OAAA,CAACvD,UAAU;kBAACoO,OAAO,EAAC,OAAO;kBAAAD,QAAA,gBAAC5K,OAAA;oBAAA4K,QAAA,EAAQ;kBAAO;oBAAAK,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAQ,CAAC,KAAC,EAAC/H,eAAe,CAACR,MAAM,CAACZ,SAAS,CAAC;gBAAA;kBAAAgJ,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAa,CAAC,eACrGpL,OAAA,CAACvD,UAAU;kBAACoO,OAAO,EAAC,OAAO;kBAAAD,QAAA,gBAAC5K,OAAA;oBAAA4K,QAAA,EAAQ;kBAAc;oBAAAK,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAQ,CAAC,KAAC,EAACvI,MAAM,CAAC8C,aAAa,IAAI,CAAC,EAAC,IAAE;gBAAA;kBAAAsF,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAY,CAAC,eACtGpL,OAAA,CAACvD,UAAU;kBAACoO,OAAO,EAAC,OAAO;kBAAAD,QAAA,gBACzB5K,OAAA;oBAAA4K,QAAA,EAAQ;kBAAM;oBAAAK,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAQ,CAAC,eACvBpL,OAAA,CAAC1C,IAAI;oBACHsO,KAAK,EAAE/I,MAAM,CAAC6C,YAAY,IAAI,KAAM;oBACpCiG,IAAI,EAAC,OAAO;oBACZK,KAAK,EAAEpM,iBAAiB,CAACiD,MAAM,CAAC6C,YAAY,CAAE;oBAC9CmF,OAAO,EAAC,UAAU;oBAClBC,EAAE,EAAE;sBAAE2C,EAAE,EAAE;oBAAE;kBAAE;oBAAAxC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACf,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACQ,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACV,CAAC;YAEV;YACA,OAAO,IAAI;UACb,CAAC,EAAE,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACA,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,eAEPpL,OAAA,CAACzD,GAAG;QAACuO,EAAE,EAAE;UAAEgD,EAAE,EAAE,CAAC;UAAEvC,OAAO,EAAE,MAAM;UAAEe,cAAc,EAAE,UAAU;UAAEkB,GAAG,EAAE;QAAE,CAAE;QAAA5C,QAAA,gBACtE5K,OAAA,CAACrD,MAAM;UACLkO,OAAO,EAAC,UAAU;UAClBmB,KAAK,EAAC,WAAW;UACjBC,OAAO,EAAEA,CAAA,KAAM;YACbzK,eAAe,CAAC,IAAI,CAAC;YACrBM,WAAW,CAAC;cACVC,OAAO,EAAE,EAAE;cACXC,YAAY,EAAE,EAAE;cAChBC,SAAS,EAAE;YACb,CAAC,CAAC;UACJ,CAAE;UACFkK,SAAS,eAAEnM,OAAA,CAACtB,UAAU;YAAC0N,QAAQ,EAAC;UAAO;YAAAnB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAE;UAC3Cc,QAAQ,EAAEzL,OAAQ;UAClBkL,IAAI,EAAC,OAAO;UACZb,EAAE,EAAE;YAAEY,QAAQ,EAAE,MAAM;YAAEW,MAAM,EAAE;UAAO,CAAE;UAAAzB,QAAA,EAC1C;QAED;UAAAK,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC,eAETpL,OAAA,CAACrD,MAAM;UACLkO,OAAO,EAAC,WAAW;UACnBmB,KAAK,EAAC,SAAS;UACfC,OAAO,EAAEhC,YAAa;UACtB0G,OAAO,EAAElQ,OAAO,GAAGmQ,SAAS,gBAAG5Q,OAAA,CAACxB,QAAQ;YAAC4N,QAAQ,EAAC;UAAO;YAAAnB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAE;UAC7Dc,QAAQ,EAAEzL,OAAO,IAAI,CAACoB,QAAQ,CAACG,YAAY,IAAI,CAACH,QAAQ,CAACI,SAAU;UACnE0J,IAAI,EAAC,OAAO;UACZb,EAAE,EAAE;YAAEY,QAAQ,EAAE,MAAM;YAAEW,MAAM,EAAE;UAAO,CAAE;UAAAzB,QAAA,EAExCnK,OAAO,gBAAGT,OAAA,CAAC7C,gBAAgB;YAACwO,IAAI,EAAE;UAAG;YAAAV,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,GAAG;QAAO;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC7C,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACN,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACD,CACR,eAGDpL,OAAA,CAACzC,MAAM;MAACsT,IAAI,EAAE5H,iBAAkB;MAAC6H,OAAO,EAAEA,CAAA,KAAM5H,oBAAoB,CAAC,KAAK,CAAE;MAAC2E,QAAQ,EAAC,IAAI;MAACkB,SAAS;MAAAnE,QAAA,gBAClG5K,OAAA,CAACxC,WAAW;QAACsN,EAAE,EAAE;UAAEgC,OAAO,EAAE;QAAgB,CAAE;QAAAlC,QAAA,eAC5C5K,OAAA,CAACzD,GAAG;UAACuO,EAAE,EAAE;YAAES,OAAO,EAAE,MAAM;YAAEC,UAAU,EAAE,QAAQ;YAAEgC,GAAG,EAAE;UAAE,CAAE;UAAA5C,QAAA,gBACzD5K,OAAA,CAACpB,WAAW;YAACoN,KAAK,EAAC;UAAS;YAAAf,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,eAC/BpL,OAAA,CAACvD,UAAU;YAACoO,OAAO,EAAC,IAAI;YAAAD,QAAA,EAAEzB,kBAAkB,CAACE;UAAK;YAAA4B,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAa,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC7D;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACK,CAAC,eACdpL,OAAA,CAACvC,aAAa;QAAAmN,QAAA,eACZ5K,OAAA,CAACvD,UAAU;UAACoO,OAAO,EAAC,OAAO;UAACC,EAAE,EAAE;YAAEgD,EAAE,EAAE;UAAE,CAAE;UAAAlD,QAAA,EACvCzB,kBAAkB,CAAC/E;QAAO;UAAA6G,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACjB;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACA,CAAC,eAChBpL,OAAA,CAACtC,aAAa;QAAAkN,QAAA,gBACZ5K,OAAA,CAACrD,MAAM;UAACsP,OAAO,EAAEA,CAAA,KAAM/C,oBAAoB,CAAC,KAAK,CAAE;UAAC8C,KAAK,EAAC,WAAW;UAACnB,OAAO,EAAC,UAAU;UAAAD,QAAA,EAAC;QAEzF;UAAAK,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC,eACTpL,OAAA,CAACrD,MAAM;UACLsP,OAAO,EAAEA,CAAA,KAAM;YACb/C,oBAAoB,CAAC,KAAK,CAAC;YAC3BC,kBAAkB,CAACG,SAAS,CAAC,CAAC;UAChC,CAAE;UACF0C,KAAK,EAAC,SAAS;UACfnB,OAAO,EAAC,WAAW;UACnBkG,SAAS;UAAAnG,QAAA,EACV;QAED;UAAAK,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACI,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACV,CAAC,eAGTpL,OAAA,CAACzC,MAAM;MAACsT,IAAI,EAAE/N,qBAAsB;MAACgO,OAAO,EAAEhB,4BAA6B;MAACjC,QAAQ,EAAC,IAAI;MAACkB,SAAS;MAAAnE,QAAA,gBACjG5K,OAAA,CAACxC,WAAW;QAACsN,EAAE,EAAE;UAAEgC,OAAO,EAAE;QAAgB,CAAE;QAAAlC,QAAA,eAC5C5K,OAAA,CAACzD,GAAG;UAACuO,EAAE,EAAE;YAAES,OAAO,EAAE,MAAM;YAAEC,UAAU,EAAE,QAAQ;YAAEgC,GAAG,EAAE;UAAE,CAAE;UAAA5C,QAAA,gBACzD5K,OAAA,CAACpB,WAAW;YAACoN,KAAK,EAAC;UAAS;YAAAf,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,eAC/BpL,OAAA,CAACvD,UAAU;YAACoO,OAAO,EAAC,IAAI;YAAAD,QAAA,EAAC;UAAe;YAAAK,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAY,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAClD;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACK,CAAC,eACdpL,OAAA,CAACvC,aAAa;QAAAmN,QAAA,EACX5H,eAAe,iBACdhD,OAAA,CAACzD,GAAG;UAACuO,EAAE,EAAE;YAAEgD,EAAE,EAAE;UAAE,CAAE;UAAAlD,QAAA,gBACjB5K,OAAA,CAACvD,UAAU;YAACoO,OAAO,EAAC,OAAO;YAACmG,SAAS;YAAApG,QAAA,GAAC,UAC5B,eAAA5K,OAAA;cAAA4K,QAAA,EAAS5H,eAAe,CAACjB;YAAO;cAAAkJ,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAS,CAAC,4BAAqB,EAACpI,eAAe,CAACqE,eAAe,IAAI,CAAC,EAAC,KAC/G;UAAA;YAAA4D,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAY,CAAC,eACbpL,OAAA,CAACvD,UAAU;YAACoO,OAAO,EAAC,OAAO;YAACmG,SAAS;YAAApG,QAAA,EAAC;UAEtC;YAAAK,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAY,CAAC,eACbpL,OAAA,CAACvD,UAAU;YAACoO,OAAO,EAAC,OAAO;YAAC6B,SAAS,EAAC,IAAI;YAAA9B,QAAA,gBACxC5K,OAAA;cAAA4K,QAAA,EAAI;YAAsC;cAAAK,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eAC/CpL,OAAA;cAAA4K,QAAA,EAAI;YAAyB;cAAAK,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eAClCpL,OAAA;cAAA4K,QAAA,EAAI;YAAsB;cAAAK,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACrB,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACV;MACN;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACY,CAAC,eAChBpL,OAAA,CAACtC,aAAa;QAACoN,EAAE,EAAE;UAAEO,CAAC,EAAE,CAAC;UAAEiB,cAAc,EAAE;QAAgB,CAAE;QAAA1B,QAAA,gBAC3D5K,OAAA,CAACrD,MAAM;UAACsP,OAAO,EAAE6D,4BAA6B;UAAC9D,KAAK,EAAC,WAAW;UAAApB,QAAA,EAAC;QAEjE;UAAAK,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC,eACTpL,OAAA,CAACzD,GAAG;UAAAqO,QAAA,gBACF5K,OAAA,CAACrD,MAAM;YAACsP,OAAO,EAAE+D,wBAAyB;YAAChE,KAAK,EAAC,SAAS;YAAClB,EAAE,EAAE;cAAEW,EAAE,EAAE;YAAE,CAAE;YAAAb,QAAA,EAAC;UAE1E;YAAAK,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC,eACTpL,OAAA,CAACrD,MAAM;YAACsP,OAAO,EAAE8D,gBAAiB;YAAClF,OAAO,EAAC,WAAW;YAACmB,KAAK,EAAC,SAAS;YAAApB,QAAA,EAAC;UAEvE;YAAAK,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACN,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACO,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACV,CAAC,eAGTpL,OAAA,CAACd,sBAAsB;MACrB2R,IAAI,EAAEvO,0BAA2B;MACjCwO,OAAO,EAAEb,iCAAkC;MAC3CrN,IAAI,EAAEF,oBAAoB,CAACE,IAAK;MAChCC,MAAM,EAAEH,oBAAoB,CAACG,MAAO;MACpCoO,YAAY,EAAEf,2BAA4B;MAC1CgB,mBAAmB,EAAEb;IAAwB;MAAApF,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAC9C,CAAC,eAGFpL,OAAA,CAACzC,MAAM;MACLsT,IAAI,EAAE3N,qBAAsB;MAC5B4N,OAAO,EAAEA,CAAA,KAAM3N,wBAAwB,CAAC,KAAK,CAAE;MAC/C0K,QAAQ,EAAC,IAAI;MACbkB,SAAS;MAAAnE,QAAA,gBAET5K,OAAA,CAACxC,WAAW;QAAAoN,QAAA,eACV5K,OAAA,CAACzD,GAAG;UAACuO,EAAE,EAAE;YAAES,OAAO,EAAE,MAAM;YAAEC,UAAU,EAAE,QAAQ;YAAEgC,GAAG,EAAE;UAAE,CAAE;UAAA5C,QAAA,gBACzD5K,OAAA,CAAClB,QAAQ;YAACkN,KAAK,EAAC;UAAS;YAAAf,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,eAC5BpL,OAAA,CAACvD,UAAU;YAACoO,OAAO,EAAC,IAAI;YAAAD,QAAA,EAAC;UAAa;YAAAK,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAY,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAChD;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACK,CAAC,eACdpL,OAAA,CAACvC,aAAa;QAAAmN,QAAA,eACZ5K,OAAA,CAACb,eAAe;UAACyD,IAAI,EAAErB;QAAa;UAAA0J,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC1B,CAAC,eAChBpL,OAAA,CAACtC,aAAa;QAAAkN,QAAA,eACZ5K,OAAA,CAACrD,MAAM;UAACsP,OAAO,EAAEA,CAAA,KAAM9I,wBAAwB,CAAC,KAAK,CAAE;UAAC6I,KAAK,EAAC,SAAS;UAAApB,QAAA,EAAC;QAExE;UAAAK,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACI,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACV,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACN,CAAC;AAEV,CAAC;AAAC7K,EAAA,CAxiEIJ,kBAAkB;EAAA,QACLpB,WAAW;AAAA;AAAAoS,EAAA,GADxBhR,kBAAkB;AA0iExB,eAAeA,kBAAkB;AAAC,IAAAgR,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}