{"ast": null, "code": "module.exports = require('./head');", "map": {"version": 3, "names": ["module", "exports", "require"], "sources": ["C:/CMS/webapp/frontend/node_modules/lodash/first.js"], "sourcesContent": ["module.exports = require('./head');\n"], "mappings": "AAAAA,MAAM,CAACC,OAAO,GAAGC,OAAO,CAAC,QAAQ,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}