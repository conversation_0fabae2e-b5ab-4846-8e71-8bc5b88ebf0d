{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\PycharmProjects\\\\CMS\\\\webapp\\\\frontend\\\\src\\\\context\\\\AuthContext.js\",\n  _s = $RefreshSig$(),\n  _s2 = $RefreshSig$();\nimport React, { createContext, useState, useContext, useEffect } from 'react';\nimport { useNavigate } from 'react-router-dom';\nimport authService from '../services/authService';\n\n// Crea il contesto di autenticazione\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst AuthContext = /*#__PURE__*/createContext(null);\n\n// Hook personalizzato per utilizzare il contesto di autenticazione\nexport const useAuth = () => {\n  _s();\n  return useContext(AuthContext);\n};\n_s(useAuth, \"gDsCjeeItUuvgOWf1v4qoK9RF6k=\");\nexport const AuthProvider = ({\n  children\n}) => {\n  _s2();\n  const [user, setUser] = useState(null);\n  const [isAuthenticated, setIsAuthenticated] = useState(false);\n  const [loading, setLoading] = useState(true);\n  const [isImpersonating, setIsImpersonating] = useState(localStorage.getItem('isImpersonating') === 'true');\n  const [impersonatedUser, setImpersonatedUser] = useState(JSON.parse(localStorage.getItem('impersonatedUser') || 'null'));\n  const navigate = useNavigate();\n\n  // Verifica se l'utente è già autenticato all'avvio dell'applicazione\n  useEffect(() => {\n    const checkAuth = async () => {\n      try {\n        console.log('Verificando autenticazione all\\'avvio...');\n        // Prima di tutto, imposta loading a true\n        setLoading(true);\n\n        // Pulisci eventuali token non validi o scaduti\n        const token = localStorage.getItem('token');\n        console.log('Token trovato nel localStorage:', token ? 'Sì' : 'No');\n        if (token) {\n          try {\n            // Verifica la validità del token\n            console.log('Tentativo di verifica token...');\n            const userData = await authService.checkToken();\n            console.log('Token valido, dati utente:', userData);\n            setUser(userData);\n            setIsAuthenticated(true);\n\n            // Imposta lo stato di impersonificazione in base ai dati dell'utente\n            // Questo valore viene ora impostato dal backend nel token JWT e recuperato da authService.checkToken\n            const impersonatingState = userData.isImpersonated === true;\n            console.log('Stato di impersonificazione recuperato dai dati utente:', impersonatingState);\n            setIsImpersonating(impersonatingState);\n          } catch (tokenError) {\n            console.error('Errore durante la verifica del token:', tokenError);\n            // Se il token non è valido, rimuovilo\n            console.log('Rimozione token non valido dal localStorage');\n            localStorage.removeItem('token');\n            setUser(null);\n            setIsAuthenticated(false);\n            localStorage.removeItem('isImpersonating');\n            setIsImpersonating(false);\n          }\n        } else {\n          console.log('Nessun token trovato, utente non autenticato');\n          setUser(null);\n          setIsAuthenticated(false);\n          localStorage.removeItem('isImpersonating');\n          setIsImpersonating(false);\n        }\n      } catch (error) {\n        console.error('Errore generale durante la verifica dell\\'autenticazione:', error);\n        // In caso di errore generale, assicurati che l'utente non sia autenticato\n        localStorage.removeItem('token');\n        setUser(null);\n        setIsAuthenticated(false);\n      } finally {\n        // Assicurati che loading sia impostato a false alla fine\n        console.log('Completata verifica autenticazione, loading:', false);\n        setTimeout(() => {\n          setLoading(false);\n        }, 500); // Aggiungi un piccolo ritardo per assicurarsi che lo stato sia aggiornato\n      }\n    };\n\n    // Esegui la verifica dell'autenticazione\n    checkAuth();\n  }, []);\n\n  // Funzione di login\n  const login = async (credentials, loginType) => {\n    try {\n      console.log(`Tentativo di login come ${loginType}`, credentials);\n      const response = await authService.login(credentials, loginType);\n      console.log('Risposta login ricevuta:', response);\n      const {\n        access_token,\n        user_id,\n        username,\n        role,\n        cantiere_id,\n        cantiere_name\n      } = response;\n\n      // Salva il token nel localStorage\n      console.log('Salvataggio token nel localStorage');\n      localStorage.setItem('token', access_token);\n\n      // Imposta i dati dell'utente\n      const userData = {\n        id: user_id,\n        username,\n        role\n      };\n      console.log('Impostazione dati utente:', userData);\n      setUser(userData);\n      setIsAuthenticated(true);\n\n      // Se è un utente cantiere, salva l'ID e il nome del cantiere nel localStorage\n      if (role === 'cantieri_user' && cantiere_id) {\n        console.log('Utente cantiere, salvataggio dati cantiere:', {\n          cantiere_id,\n          cantiere_name\n        });\n        localStorage.setItem('selectedCantiereId', cantiere_id.toString());\n        localStorage.setItem('selectedCantiereName', cantiere_name || `Cantiere ${cantiere_id}`);\n      }\n\n      // Resetta lo stato di impersonificazione a false quando si effettua un login normale\n      if (isImpersonating) {\n        console.log('Reset dello stato di impersonificazione durante login normale');\n        localStorage.removeItem('isImpersonating');\n        setIsImpersonating(false);\n      }\n      return userData;\n    } catch (error) {\n      console.error('Errore durante il login:', error);\n      throw error;\n    }\n  };\n\n  // Funzione di logout\n  const logout = () => {\n    // Se l'utente corrente è un amministratore che ha effettuato \"login as user\",\n    // reindirizza alla dashboard amministratore invece che alla pagina di login\n    if (isImpersonating) {\n      console.log('Logout da utente impersonato, ritorno al menu amministratore');\n\n      // Rimuovi i dati dell'utente impersonato\n      localStorage.removeItem('impersonatedUser');\n      localStorage.removeItem('isImpersonating');\n      setImpersonatedUser(null);\n      setIsImpersonating(false);\n\n      // Reindirizza alla dashboard amministratore\n      navigate('/dashboard/admin');\n    } else {\n      // Logout normale\n      console.log('Logout normale, ritorno alla pagina di login');\n      localStorage.removeItem('token');\n      localStorage.removeItem('isImpersonating');\n      localStorage.removeItem('impersonatedUser');\n      setUser(null);\n      setImpersonatedUser(null);\n      setIsAuthenticated(false);\n      navigate('/login');\n    }\n  };\n\n  // Funzione per impostare il token\n  const setToken = token => {\n    localStorage.setItem('token', token);\n  };\n\n  // Funzione per impersonare un utente\n  const impersonateUser = async userId => {\n    try {\n      // Chiama l'endpoint di impersonificazione\n      const response = await authService.impersonateUser(userId);\n\n      // Salva il token nel localStorage\n      localStorage.setItem('token', response.access_token);\n\n      // Salva i dati dell'utente impersonato\n      const impersonatedUserData = {\n        id: response.impersonated_id,\n        username: response.impersonated_username,\n        role: response.impersonated_role\n      };\n\n      // Salva i dati dell'utente impersonato nel localStorage\n      localStorage.setItem('impersonatedUser', JSON.stringify(impersonatedUserData));\n      setImpersonatedUser(impersonatedUserData);\n\n      // Imposta lo stato di impersonificazione a true\n      setIsImpersonating(true);\n      localStorage.setItem('isImpersonating', 'true');\n\n      // L'utente rimane l'amministratore, ma ora ha informazioni sull'utente impersonato\n      // Non modifichiamo lo stato dell'utente perché rimane l'amministratore\n\n      console.log('Impersonificazione attivata, logout riporterà al menu amministratore');\n      console.log('Utente impersonato:', impersonatedUserData.username, 'Ruolo:', impersonatedUserData.role);\n      return {\n        ...user,\n        // Mantiene i dati dell'amministratore\n        isImpersonated: true,\n        impersonatedUser: impersonatedUserData // Aggiunge i dati dell'utente impersonato\n      };\n    } catch (error) {\n      console.error('Errore durante l\\'impersonificazione:', error);\n      throw error;\n    }\n  };\n\n  // Valore del contesto\n  const value = {\n    user,\n    setUser,\n    isAuthenticated,\n    loading,\n    login,\n    logout,\n    setToken,\n    impersonateUser,\n    isImpersonating,\n    impersonatedUser\n  };\n  return /*#__PURE__*/_jsxDEV(AuthContext.Provider, {\n    value: value,\n    children: children\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 209,\n    columnNumber: 10\n  }, this);\n};\n_s2(AuthProvider, \"xvLZ91MIk5xAuqnMb7R8+CuhYus=\", false, function () {\n  return [useNavigate];\n});\n_c = AuthProvider;\nvar _c;\n$RefreshReg$(_c, \"AuthProvider\");", "map": {"version": 3, "names": ["React", "createContext", "useState", "useContext", "useEffect", "useNavigate", "authService", "jsxDEV", "_jsxDEV", "AuthContext", "useAuth", "_s", "<PERSON>th<PERSON><PERSON><PERSON>", "children", "_s2", "user", "setUser", "isAuthenticated", "setIsAuthenticated", "loading", "setLoading", "isImpersonating", "setIsImpersonating", "localStorage", "getItem", "impersonated<PERSON><PERSON>", "setImpersonatedUser", "JSON", "parse", "navigate", "checkAuth", "console", "log", "token", "userData", "checkToken", "impersonating<PERSON><PERSON>", "isImpersonated", "tokenError", "error", "removeItem", "setTimeout", "login", "credentials", "loginType", "response", "access_token", "user_id", "username", "role", "cantiere_id", "cantiere_name", "setItem", "id", "toString", "logout", "setToken", "impersonate<PERSON><PERSON>", "userId", "impersonated<PERSON><PERSON><PERSON><PERSON>", "impersonated_id", "impersonated_username", "impersonated_role", "stringify", "value", "Provider", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/PycharmProjects/CMS/webapp/frontend/src/context/AuthContext.js"], "sourcesContent": ["import React, { createContext, useState, useContext, useEffect } from 'react';\nimport { useNavigate } from 'react-router-dom';\nimport authService from '../services/authService';\n\n// Crea il contesto di autenticazione\nconst AuthContext = createContext(null);\n\n// Hook personalizzato per utilizzare il contesto di autenticazione\nexport const useAuth = () => useContext(AuthContext);\n\nexport const AuthProvider = ({ children }) => {\n  const [user, setUser] = useState(null);\n  const [isAuthenticated, setIsAuthenticated] = useState(false);\n  const [loading, setLoading] = useState(true);\n  const [isImpersonating, setIsImpersonating] = useState(localStorage.getItem('isImpersonating') === 'true');\n  const [impersonatedUser, setImpersonatedUser] = useState(JSON.parse(localStorage.getItem('impersonatedUser') || 'null'));\n  const navigate = useNavigate();\n\n  // Verifica se l'utente è già autenticato all'avvio dell'applicazione\n  useEffect(() => {\n    const checkAuth = async () => {\n      try {\n        console.log('Verificando autenticazione all\\'avvio...');\n        // Prima di tutto, imposta loading a true\n        setLoading(true);\n\n        // Pulisci eventuali token non validi o scaduti\n        const token = localStorage.getItem('token');\n        console.log('Token trovato nel localStorage:', token ? 'Sì' : 'No');\n\n        if (token) {\n          try {\n            // Verifica la validità del token\n            console.log('Tentativo di verifica token...');\n            const userData = await authService.checkToken();\n            console.log('Token valido, dati utente:', userData);\n            setUser(userData);\n            setIsAuthenticated(true);\n\n            // Imposta lo stato di impersonificazione in base ai dati dell'utente\n            // Questo valore viene ora impostato dal backend nel token JWT e recuperato da authService.checkToken\n            const impersonatingState = userData.isImpersonated === true;\n            console.log('Stato di impersonificazione recuperato dai dati utente:', impersonatingState);\n            setIsImpersonating(impersonatingState);\n          } catch (tokenError) {\n            console.error('Errore durante la verifica del token:', tokenError);\n            // Se il token non è valido, rimuovilo\n            console.log('Rimozione token non valido dal localStorage');\n            localStorage.removeItem('token');\n            setUser(null);\n            setIsAuthenticated(false);\n            localStorage.removeItem('isImpersonating');\n            setIsImpersonating(false);\n          }\n        } else {\n          console.log('Nessun token trovato, utente non autenticato');\n          setUser(null);\n          setIsAuthenticated(false);\n          localStorage.removeItem('isImpersonating');\n          setIsImpersonating(false);\n        }\n      } catch (error) {\n        console.error('Errore generale durante la verifica dell\\'autenticazione:', error);\n        // In caso di errore generale, assicurati che l'utente non sia autenticato\n        localStorage.removeItem('token');\n        setUser(null);\n        setIsAuthenticated(false);\n      } finally {\n        // Assicurati che loading sia impostato a false alla fine\n        console.log('Completata verifica autenticazione, loading:', false);\n        setTimeout(() => {\n          setLoading(false);\n        }, 500); // Aggiungi un piccolo ritardo per assicurarsi che lo stato sia aggiornato\n      }\n    };\n\n    // Esegui la verifica dell'autenticazione\n    checkAuth();\n  }, []);\n\n  // Funzione di login\n  const login = async (credentials, loginType) => {\n    try {\n      console.log(`Tentativo di login come ${loginType}`, credentials);\n      const response = await authService.login(credentials, loginType);\n      console.log('Risposta login ricevuta:', response);\n\n      const { access_token, user_id, username, role, cantiere_id, cantiere_name } = response;\n\n      // Salva il token nel localStorage\n      console.log('Salvataggio token nel localStorage');\n      localStorage.setItem('token', access_token);\n\n      // Imposta i dati dell'utente\n      const userData = { id: user_id, username, role };\n      console.log('Impostazione dati utente:', userData);\n      setUser(userData);\n      setIsAuthenticated(true);\n\n      // Se è un utente cantiere, salva l'ID e il nome del cantiere nel localStorage\n      if (role === 'cantieri_user' && cantiere_id) {\n        console.log('Utente cantiere, salvataggio dati cantiere:', { cantiere_id, cantiere_name });\n        localStorage.setItem('selectedCantiereId', cantiere_id.toString());\n        localStorage.setItem('selectedCantiereName', cantiere_name || `Cantiere ${cantiere_id}`);\n      }\n\n      // Resetta lo stato di impersonificazione a false quando si effettua un login normale\n      if (isImpersonating) {\n        console.log('Reset dello stato di impersonificazione durante login normale');\n        localStorage.removeItem('isImpersonating');\n        setIsImpersonating(false);\n      }\n\n      return userData;\n    } catch (error) {\n      console.error('Errore durante il login:', error);\n      throw error;\n    }\n  };\n\n  // Funzione di logout\n  const logout = () => {\n    // Se l'utente corrente è un amministratore che ha effettuato \"login as user\",\n    // reindirizza alla dashboard amministratore invece che alla pagina di login\n    if (isImpersonating) {\n      console.log('Logout da utente impersonato, ritorno al menu amministratore');\n\n      // Rimuovi i dati dell'utente impersonato\n      localStorage.removeItem('impersonatedUser');\n      localStorage.removeItem('isImpersonating');\n      setImpersonatedUser(null);\n      setIsImpersonating(false);\n\n      // Reindirizza alla dashboard amministratore\n      navigate('/dashboard/admin');\n    } else {\n      // Logout normale\n      console.log('Logout normale, ritorno alla pagina di login');\n      localStorage.removeItem('token');\n      localStorage.removeItem('isImpersonating');\n      localStorage.removeItem('impersonatedUser');\n      setUser(null);\n      setImpersonatedUser(null);\n      setIsAuthenticated(false);\n      navigate('/login');\n    }\n  };\n\n  // Funzione per impostare il token\n  const setToken = (token) => {\n    localStorage.setItem('token', token);\n  };\n\n  // Funzione per impersonare un utente\n  const impersonateUser = async (userId) => {\n    try {\n      // Chiama l'endpoint di impersonificazione\n      const response = await authService.impersonateUser(userId);\n\n      // Salva il token nel localStorage\n      localStorage.setItem('token', response.access_token);\n\n      // Salva i dati dell'utente impersonato\n      const impersonatedUserData = {\n        id: response.impersonated_id,\n        username: response.impersonated_username,\n        role: response.impersonated_role\n      };\n\n      // Salva i dati dell'utente impersonato nel localStorage\n      localStorage.setItem('impersonatedUser', JSON.stringify(impersonatedUserData));\n      setImpersonatedUser(impersonatedUserData);\n\n      // Imposta lo stato di impersonificazione a true\n      setIsImpersonating(true);\n      localStorage.setItem('isImpersonating', 'true');\n\n      // L'utente rimane l'amministratore, ma ora ha informazioni sull'utente impersonato\n      // Non modifichiamo lo stato dell'utente perché rimane l'amministratore\n\n      console.log('Impersonificazione attivata, logout riporterà al menu amministratore');\n      console.log('Utente impersonato:', impersonatedUserData.username, 'Ruolo:', impersonatedUserData.role);\n\n      return {\n        ...user,  // Mantiene i dati dell'amministratore\n        isImpersonated: true,\n        impersonatedUser: impersonatedUserData  // Aggiunge i dati dell'utente impersonato\n      };\n    } catch (error) {\n      console.error('Errore durante l\\'impersonificazione:', error);\n      throw error;\n    }\n  };\n\n  // Valore del contesto\n  const value = {\n    user,\n    setUser,\n    isAuthenticated,\n    loading,\n    login,\n    logout,\n    setToken,\n    impersonateUser,\n    isImpersonating,\n    impersonatedUser\n  };\n\n  return <AuthContext.Provider value={value}>{children}</AuthContext.Provider>;\n};\n"], "mappings": ";;;AAAA,OAAOA,KAAK,IAAIC,aAAa,EAAEC,QAAQ,EAAEC,UAAU,EAAEC,SAAS,QAAQ,OAAO;AAC7E,SAASC,WAAW,QAAQ,kBAAkB;AAC9C,OAAOC,WAAW,MAAM,yBAAyB;;AAEjD;AAAA,SAAAC,MAAA,IAAAC,OAAA;AACA,MAAMC,WAAW,gBAAGR,aAAa,CAAC,IAAI,CAAC;;AAEvC;AACA,OAAO,MAAMS,OAAO,GAAGA,CAAA;EAAAC,EAAA;EAAA,OAAMR,UAAU,CAACM,WAAW,CAAC;AAAA;AAACE,EAAA,CAAxCD,OAAO;AAEpB,OAAO,MAAME,YAAY,GAAGA,CAAC;EAAEC;AAAS,CAAC,KAAK;EAAAC,GAAA;EAC5C,MAAM,CAACC,IAAI,EAAEC,OAAO,CAAC,GAAGd,QAAQ,CAAC,IAAI,CAAC;EACtC,MAAM,CAACe,eAAe,EAAEC,kBAAkB,CAAC,GAAGhB,QAAQ,CAAC,KAAK,CAAC;EAC7D,MAAM,CAACiB,OAAO,EAAEC,UAAU,CAAC,GAAGlB,QAAQ,CAAC,IAAI,CAAC;EAC5C,MAAM,CAACmB,eAAe,EAAEC,kBAAkB,CAAC,GAAGpB,QAAQ,CAACqB,YAAY,CAACC,OAAO,CAAC,iBAAiB,CAAC,KAAK,MAAM,CAAC;EAC1G,MAAM,CAACC,gBAAgB,EAAEC,mBAAmB,CAAC,GAAGxB,QAAQ,CAACyB,IAAI,CAACC,KAAK,CAACL,YAAY,CAACC,OAAO,CAAC,kBAAkB,CAAC,IAAI,MAAM,CAAC,CAAC;EACxH,MAAMK,QAAQ,GAAGxB,WAAW,CAAC,CAAC;;EAE9B;EACAD,SAAS,CAAC,MAAM;IACd,MAAM0B,SAAS,GAAG,MAAAA,CAAA,KAAY;MAC5B,IAAI;QACFC,OAAO,CAACC,GAAG,CAAC,0CAA0C,CAAC;QACvD;QACAZ,UAAU,CAAC,IAAI,CAAC;;QAEhB;QACA,MAAMa,KAAK,GAAGV,YAAY,CAACC,OAAO,CAAC,OAAO,CAAC;QAC3CO,OAAO,CAACC,GAAG,CAAC,iCAAiC,EAAEC,KAAK,GAAG,IAAI,GAAG,IAAI,CAAC;QAEnE,IAAIA,KAAK,EAAE;UACT,IAAI;YACF;YACAF,OAAO,CAACC,GAAG,CAAC,gCAAgC,CAAC;YAC7C,MAAME,QAAQ,GAAG,MAAM5B,WAAW,CAAC6B,UAAU,CAAC,CAAC;YAC/CJ,OAAO,CAACC,GAAG,CAAC,4BAA4B,EAAEE,QAAQ,CAAC;YACnDlB,OAAO,CAACkB,QAAQ,CAAC;YACjBhB,kBAAkB,CAAC,IAAI,CAAC;;YAExB;YACA;YACA,MAAMkB,kBAAkB,GAAGF,QAAQ,CAACG,cAAc,KAAK,IAAI;YAC3DN,OAAO,CAACC,GAAG,CAAC,yDAAyD,EAAEI,kBAAkB,CAAC;YAC1Fd,kBAAkB,CAACc,kBAAkB,CAAC;UACxC,CAAC,CAAC,OAAOE,UAAU,EAAE;YACnBP,OAAO,CAACQ,KAAK,CAAC,uCAAuC,EAAED,UAAU,CAAC;YAClE;YACAP,OAAO,CAACC,GAAG,CAAC,6CAA6C,CAAC;YAC1DT,YAAY,CAACiB,UAAU,CAAC,OAAO,CAAC;YAChCxB,OAAO,CAAC,IAAI,CAAC;YACbE,kBAAkB,CAAC,KAAK,CAAC;YACzBK,YAAY,CAACiB,UAAU,CAAC,iBAAiB,CAAC;YAC1ClB,kBAAkB,CAAC,KAAK,CAAC;UAC3B;QACF,CAAC,MAAM;UACLS,OAAO,CAACC,GAAG,CAAC,8CAA8C,CAAC;UAC3DhB,OAAO,CAAC,IAAI,CAAC;UACbE,kBAAkB,CAAC,KAAK,CAAC;UACzBK,YAAY,CAACiB,UAAU,CAAC,iBAAiB,CAAC;UAC1ClB,kBAAkB,CAAC,KAAK,CAAC;QAC3B;MACF,CAAC,CAAC,OAAOiB,KAAK,EAAE;QACdR,OAAO,CAACQ,KAAK,CAAC,2DAA2D,EAAEA,KAAK,CAAC;QACjF;QACAhB,YAAY,CAACiB,UAAU,CAAC,OAAO,CAAC;QAChCxB,OAAO,CAAC,IAAI,CAAC;QACbE,kBAAkB,CAAC,KAAK,CAAC;MAC3B,CAAC,SAAS;QACR;QACAa,OAAO,CAACC,GAAG,CAAC,8CAA8C,EAAE,KAAK,CAAC;QAClES,UAAU,CAAC,MAAM;UACfrB,UAAU,CAAC,KAAK,CAAC;QACnB,CAAC,EAAE,GAAG,CAAC,CAAC,CAAC;MACX;IACF,CAAC;;IAED;IACAU,SAAS,CAAC,CAAC;EACb,CAAC,EAAE,EAAE,CAAC;;EAEN;EACA,MAAMY,KAAK,GAAG,MAAAA,CAAOC,WAAW,EAAEC,SAAS,KAAK;IAC9C,IAAI;MACFb,OAAO,CAACC,GAAG,CAAC,2BAA2BY,SAAS,EAAE,EAAED,WAAW,CAAC;MAChE,MAAME,QAAQ,GAAG,MAAMvC,WAAW,CAACoC,KAAK,CAACC,WAAW,EAAEC,SAAS,CAAC;MAChEb,OAAO,CAACC,GAAG,CAAC,0BAA0B,EAAEa,QAAQ,CAAC;MAEjD,MAAM;QAAEC,YAAY;QAAEC,OAAO;QAAEC,QAAQ;QAAEC,IAAI;QAAEC,WAAW;QAAEC;MAAc,CAAC,GAAGN,QAAQ;;MAEtF;MACAd,OAAO,CAACC,GAAG,CAAC,oCAAoC,CAAC;MACjDT,YAAY,CAAC6B,OAAO,CAAC,OAAO,EAAEN,YAAY,CAAC;;MAE3C;MACA,MAAMZ,QAAQ,GAAG;QAAEmB,EAAE,EAAEN,OAAO;QAAEC,QAAQ;QAAEC;MAAK,CAAC;MAChDlB,OAAO,CAACC,GAAG,CAAC,2BAA2B,EAAEE,QAAQ,CAAC;MAClDlB,OAAO,CAACkB,QAAQ,CAAC;MACjBhB,kBAAkB,CAAC,IAAI,CAAC;;MAExB;MACA,IAAI+B,IAAI,KAAK,eAAe,IAAIC,WAAW,EAAE;QAC3CnB,OAAO,CAACC,GAAG,CAAC,6CAA6C,EAAE;UAAEkB,WAAW;UAAEC;QAAc,CAAC,CAAC;QAC1F5B,YAAY,CAAC6B,OAAO,CAAC,oBAAoB,EAAEF,WAAW,CAACI,QAAQ,CAAC,CAAC,CAAC;QAClE/B,YAAY,CAAC6B,OAAO,CAAC,sBAAsB,EAAED,aAAa,IAAI,YAAYD,WAAW,EAAE,CAAC;MAC1F;;MAEA;MACA,IAAI7B,eAAe,EAAE;QACnBU,OAAO,CAACC,GAAG,CAAC,+DAA+D,CAAC;QAC5ET,YAAY,CAACiB,UAAU,CAAC,iBAAiB,CAAC;QAC1ClB,kBAAkB,CAAC,KAAK,CAAC;MAC3B;MAEA,OAAOY,QAAQ;IACjB,CAAC,CAAC,OAAOK,KAAK,EAAE;MACdR,OAAO,CAACQ,KAAK,CAAC,0BAA0B,EAAEA,KAAK,CAAC;MAChD,MAAMA,KAAK;IACb;EACF,CAAC;;EAED;EACA,MAAMgB,MAAM,GAAGA,CAAA,KAAM;IACnB;IACA;IACA,IAAIlC,eAAe,EAAE;MACnBU,OAAO,CAACC,GAAG,CAAC,8DAA8D,CAAC;;MAE3E;MACAT,YAAY,CAACiB,UAAU,CAAC,kBAAkB,CAAC;MAC3CjB,YAAY,CAACiB,UAAU,CAAC,iBAAiB,CAAC;MAC1Cd,mBAAmB,CAAC,IAAI,CAAC;MACzBJ,kBAAkB,CAAC,KAAK,CAAC;;MAEzB;MACAO,QAAQ,CAAC,kBAAkB,CAAC;IAC9B,CAAC,MAAM;MACL;MACAE,OAAO,CAACC,GAAG,CAAC,8CAA8C,CAAC;MAC3DT,YAAY,CAACiB,UAAU,CAAC,OAAO,CAAC;MAChCjB,YAAY,CAACiB,UAAU,CAAC,iBAAiB,CAAC;MAC1CjB,YAAY,CAACiB,UAAU,CAAC,kBAAkB,CAAC;MAC3CxB,OAAO,CAAC,IAAI,CAAC;MACbU,mBAAmB,CAAC,IAAI,CAAC;MACzBR,kBAAkB,CAAC,KAAK,CAAC;MACzBW,QAAQ,CAAC,QAAQ,CAAC;IACpB;EACF,CAAC;;EAED;EACA,MAAM2B,QAAQ,GAAIvB,KAAK,IAAK;IAC1BV,YAAY,CAAC6B,OAAO,CAAC,OAAO,EAAEnB,KAAK,CAAC;EACtC,CAAC;;EAED;EACA,MAAMwB,eAAe,GAAG,MAAOC,MAAM,IAAK;IACxC,IAAI;MACF;MACA,MAAMb,QAAQ,GAAG,MAAMvC,WAAW,CAACmD,eAAe,CAACC,MAAM,CAAC;;MAE1D;MACAnC,YAAY,CAAC6B,OAAO,CAAC,OAAO,EAAEP,QAAQ,CAACC,YAAY,CAAC;;MAEpD;MACA,MAAMa,oBAAoB,GAAG;QAC3BN,EAAE,EAAER,QAAQ,CAACe,eAAe;QAC5BZ,QAAQ,EAAEH,QAAQ,CAACgB,qBAAqB;QACxCZ,IAAI,EAAEJ,QAAQ,CAACiB;MACjB,CAAC;;MAED;MACAvC,YAAY,CAAC6B,OAAO,CAAC,kBAAkB,EAAEzB,IAAI,CAACoC,SAAS,CAACJ,oBAAoB,CAAC,CAAC;MAC9EjC,mBAAmB,CAACiC,oBAAoB,CAAC;;MAEzC;MACArC,kBAAkB,CAAC,IAAI,CAAC;MACxBC,YAAY,CAAC6B,OAAO,CAAC,iBAAiB,EAAE,MAAM,CAAC;;MAE/C;MACA;;MAEArB,OAAO,CAACC,GAAG,CAAC,sEAAsE,CAAC;MACnFD,OAAO,CAACC,GAAG,CAAC,qBAAqB,EAAE2B,oBAAoB,CAACX,QAAQ,EAAE,QAAQ,EAAEW,oBAAoB,CAACV,IAAI,CAAC;MAEtG,OAAO;QACL,GAAGlC,IAAI;QAAG;QACVsB,cAAc,EAAE,IAAI;QACpBZ,gBAAgB,EAAEkC,oBAAoB,CAAE;MAC1C,CAAC;IACH,CAAC,CAAC,OAAOpB,KAAK,EAAE;MACdR,OAAO,CAACQ,KAAK,CAAC,uCAAuC,EAAEA,KAAK,CAAC;MAC7D,MAAMA,KAAK;IACb;EACF,CAAC;;EAED;EACA,MAAMyB,KAAK,GAAG;IACZjD,IAAI;IACJC,OAAO;IACPC,eAAe;IACfE,OAAO;IACPuB,KAAK;IACLa,MAAM;IACNC,QAAQ;IACRC,eAAe;IACfpC,eAAe;IACfI;EACF,CAAC;EAED,oBAAOjB,OAAA,CAACC,WAAW,CAACwD,QAAQ;IAACD,KAAK,EAAEA,KAAM;IAAAnD,QAAA,EAAEA;EAAQ;IAAAqD,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OAAuB,CAAC;AAC9E,CAAC;AAACvD,GAAA,CAvMWF,YAAY;EAAA,QAMNP,WAAW;AAAA;AAAAiE,EAAA,GANjB1D,YAAY;AAAA,IAAA0D,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}