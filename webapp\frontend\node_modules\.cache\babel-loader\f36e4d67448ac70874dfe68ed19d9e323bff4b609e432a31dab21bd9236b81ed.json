{"ast": null, "code": "import { numericPatterns } from \"../constants.js\";\nimport { Parser } from \"../Parser.js\";\nimport { parseNDigits, parseNumericPattern } from \"../utils.js\";\nexport class MinuteParser extends Parser {\n  priority = 60;\n  parse(dateString, token, match) {\n    switch (token) {\n      case \"m\":\n        return parseNumericPattern(numericPatterns.minute, dateString);\n      case \"mo\":\n        return match.ordinalNumber(dateString, {\n          unit: \"minute\"\n        });\n      default:\n        return parseNDigits(token.length, dateString);\n    }\n  }\n  validate(_date, value) {\n    return value >= 0 && value <= 59;\n  }\n  set(date, _flags, value) {\n    date.setMinutes(value, 0, 0);\n    return date;\n  }\n  incompatibleTokens = [\"t\", \"T\"];\n}", "map": {"version": 3, "names": ["numericPatterns", "<PERSON><PERSON><PERSON>", "parseNDigits", "parseNumericPattern", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "priority", "parse", "dateString", "token", "match", "minute", "ordinalNumber", "unit", "length", "validate", "_date", "value", "set", "date", "_flags", "setMinutes", "incompatibleTokens"], "sources": ["C:/Users/<USER>/PycharmProjects/CMS/webapp/frontend/node_modules/date-fns/parse/_lib/parsers/MinuteParser.js"], "sourcesContent": ["import { numericPatterns } from \"../constants.js\";\nimport { Parser } from \"../Parser.js\";\n\nimport { parseNDigits, parseNumericPattern } from \"../utils.js\";\n\nexport class MinuteParser extends Parser {\n  priority = 60;\n\n  parse(dateString, token, match) {\n    switch (token) {\n      case \"m\":\n        return parseNumericPattern(numericPatterns.minute, dateString);\n      case \"mo\":\n        return match.ordinalNumber(dateString, { unit: \"minute\" });\n      default:\n        return parseNDigits(token.length, dateString);\n    }\n  }\n\n  validate(_date, value) {\n    return value >= 0 && value <= 59;\n  }\n\n  set(date, _flags, value) {\n    date.setMinutes(value, 0, 0);\n    return date;\n  }\n\n  incompatibleTokens = [\"t\", \"T\"];\n}\n"], "mappings": "AAAA,SAASA,eAAe,QAAQ,iBAAiB;AACjD,SAASC,MAAM,QAAQ,cAAc;AAErC,SAASC,YAAY,EAAEC,mBAAmB,QAAQ,aAAa;AAE/D,OAAO,MAAMC,YAAY,SAASH,MAAM,CAAC;EACvCI,QAAQ,GAAG,EAAE;EAEbC,KAAKA,CAACC,UAAU,EAAEC,KAAK,EAAEC,KAAK,EAAE;IAC9B,QAAQD,KAAK;MACX,KAAK,GAAG;QACN,OAAOL,mBAAmB,CAACH,eAAe,CAACU,MAAM,EAAEH,UAAU,CAAC;MAChE,KAAK,IAAI;QACP,OAAOE,KAAK,CAACE,aAAa,CAACJ,UAAU,EAAE;UAAEK,IAAI,EAAE;QAAS,CAAC,CAAC;MAC5D;QACE,OAAOV,YAAY,CAACM,KAAK,CAACK,MAAM,EAAEN,UAAU,CAAC;IACjD;EACF;EAEAO,QAAQA,CAACC,KAAK,EAAEC,KAAK,EAAE;IACrB,OAAOA,KAAK,IAAI,CAAC,IAAIA,KAAK,IAAI,EAAE;EAClC;EAEAC,GAAGA,CAACC,IAAI,EAAEC,MAAM,EAAEH,KAAK,EAAE;IACvBE,IAAI,CAACE,UAAU,CAACJ,KAAK,EAAE,CAAC,EAAE,CAAC,CAAC;IAC5B,OAAOE,IAAI;EACb;EAEAG,kBAAkB,GAAG,CAAC,GAAG,EAAE,GAAG,CAAC;AACjC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}