{"ast": null, "code": "import { formatDecimalParts } from \"./formatDecimal.js\";\nexport var prefixExponent;\nexport default function (x, p) {\n  var d = formatDecimalParts(x, p);\n  if (!d) return x + \"\";\n  var coefficient = d[0],\n    exponent = d[1],\n    i = exponent - (prefixExponent = Math.max(-8, Math.min(8, Math.floor(exponent / 3))) * 3) + 1,\n    n = coefficient.length;\n  return i === n ? coefficient : i > n ? coefficient + new Array(i - n + 1).join(\"0\") : i > 0 ? coefficient.slice(0, i) + \".\" + coefficient.slice(i) : \"0.\" + new Array(1 - i).join(\"0\") + formatDecimalParts(x, Math.max(0, p + i - 1))[0]; // less than 1y!\n}", "map": {"version": 3, "names": ["formatDecimalParts", "prefixExponent", "x", "p", "d", "coefficient", "exponent", "i", "Math", "max", "min", "floor", "n", "length", "Array", "join", "slice"], "sources": ["C:/Users/<USER>/PycharmProjects/CMS/webapp/frontend/node_modules/d3-format/src/formatPrefixAuto.js"], "sourcesContent": ["import {formatDecimalParts} from \"./formatDecimal.js\";\n\nexport var prefixExponent;\n\nexport default function(x, p) {\n  var d = formatDecimalParts(x, p);\n  if (!d) return x + \"\";\n  var coefficient = d[0],\n      exponent = d[1],\n      i = exponent - (prefixExponent = Math.max(-8, Math.min(8, Math.floor(exponent / 3))) * 3) + 1,\n      n = coefficient.length;\n  return i === n ? coefficient\n      : i > n ? coefficient + new Array(i - n + 1).join(\"0\")\n      : i > 0 ? coefficient.slice(0, i) + \".\" + coefficient.slice(i)\n      : \"0.\" + new Array(1 - i).join(\"0\") + formatDecimalParts(x, Math.max(0, p + i - 1))[0]; // less than 1y!\n}\n"], "mappings": "AAAA,SAAQA,kBAAkB,QAAO,oBAAoB;AAErD,OAAO,IAAIC,cAAc;AAEzB,eAAe,UAASC,CAAC,EAAEC,CAAC,EAAE;EAC5B,IAAIC,CAAC,GAAGJ,kBAAkB,CAACE,CAAC,EAAEC,CAAC,CAAC;EAChC,IAAI,CAACC,CAAC,EAAE,OAAOF,CAAC,GAAG,EAAE;EACrB,IAAIG,WAAW,GAAGD,CAAC,CAAC,CAAC,CAAC;IAClBE,QAAQ,GAAGF,CAAC,CAAC,CAAC,CAAC;IACfG,CAAC,GAAGD,QAAQ,IAAIL,cAAc,GAAGO,IAAI,CAACC,GAAG,CAAC,CAAC,CAAC,EAAED,IAAI,CAACE,GAAG,CAAC,CAAC,EAAEF,IAAI,CAACG,KAAK,CAACL,QAAQ,GAAG,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,GAAG,CAAC;IAC7FM,CAAC,GAAGP,WAAW,CAACQ,MAAM;EAC1B,OAAON,CAAC,KAAKK,CAAC,GAAGP,WAAW,GACtBE,CAAC,GAAGK,CAAC,GAAGP,WAAW,GAAG,IAAIS,KAAK,CAACP,CAAC,GAAGK,CAAC,GAAG,CAAC,CAAC,CAACG,IAAI,CAAC,GAAG,CAAC,GACpDR,CAAC,GAAG,CAAC,GAAGF,WAAW,CAACW,KAAK,CAAC,CAAC,EAAET,CAAC,CAAC,GAAG,GAAG,GAAGF,WAAW,CAACW,KAAK,CAACT,CAAC,CAAC,GAC5D,IAAI,GAAG,IAAIO,KAAK,CAAC,CAAC,GAAGP,CAAC,CAAC,CAACQ,IAAI,CAAC,GAAG,CAAC,GAAGf,kBAAkB,CAACE,CAAC,EAAEM,IAAI,CAACC,GAAG,CAAC,CAAC,EAAEN,CAAC,GAAGI,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;AAC9F", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}