{"ast": null, "code": "import { formatDistance } from \"./lt/_lib/formatDistance.js\";\nimport { formatLong } from \"./lt/_lib/formatLong.js\";\nimport { formatRelative } from \"./lt/_lib/formatRelative.js\";\nimport { localize } from \"./lt/_lib/localize.js\";\nimport { match } from \"./lt/_lib/match.js\";\n\n/**\n * @category Locales\n * @summary Lithuanian locale.\n * @language Lithuanian\n * @iso-639-2 lit\n * <AUTHOR> [@pshpak](https://github.com/pshpak)\n * <AUTHOR> [@eduardopsll](https://github.com/eduardopsll)\n */\nexport const lt = {\n  code: \"lt\",\n  formatDistance: formatDistance,\n  formatLong: formatLong,\n  formatRelative: formatRelative,\n  localize: localize,\n  match: match,\n  options: {\n    weekStartsOn: 1 /* Monday */,\n    firstWeekContainsDate: 4\n  }\n};\n\n// Fallback for modularized imports:\nexport default lt;", "map": {"version": 3, "names": ["formatDistance", "formatLong", "formatRelative", "localize", "match", "lt", "code", "options", "weekStartsOn", "firstWeekContainsDate"], "sources": ["C:/CMS/webapp/frontend/node_modules/date-fns/locale/lt.js"], "sourcesContent": ["import { formatDistance } from \"./lt/_lib/formatDistance.js\";\nimport { formatLong } from \"./lt/_lib/formatLong.js\";\nimport { formatRelative } from \"./lt/_lib/formatRelative.js\";\nimport { localize } from \"./lt/_lib/localize.js\";\nimport { match } from \"./lt/_lib/match.js\";\n\n/**\n * @category Locales\n * @summary Lithuanian locale.\n * @language Lithuanian\n * @iso-639-2 lit\n * <AUTHOR> [@pshpak](https://github.com/pshpak)\n * <AUTHOR> [@eduardopsll](https://github.com/eduardopsll)\n */\nexport const lt = {\n  code: \"lt\",\n  formatDistance: formatDistance,\n  formatLong: formatLong,\n  formatRelative: formatRelative,\n  localize: localize,\n  match: match,\n  options: {\n    weekStartsOn: 1 /* Monday */,\n    firstWeekContainsDate: 4,\n  },\n};\n\n// Fallback for modularized imports:\nexport default lt;\n"], "mappings": "AAAA,SAASA,cAAc,QAAQ,6BAA6B;AAC5D,SAASC,UAAU,QAAQ,yBAAyB;AACpD,SAASC,cAAc,QAAQ,6BAA6B;AAC5D,SAASC,QAAQ,QAAQ,uBAAuB;AAChD,SAASC,KAAK,QAAQ,oBAAoB;;AAE1C;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO,MAAMC,EAAE,GAAG;EAChBC,IAAI,EAAE,IAAI;EACVN,cAAc,EAAEA,cAAc;EAC9BC,UAAU,EAAEA,UAAU;EACtBC,cAAc,EAAEA,cAAc;EAC9BC,QAAQ,EAAEA,QAAQ;EAClBC,KAAK,EAAEA,KAAK;EACZG,OAAO,EAAE;IACPC,YAAY,EAAE,CAAC,CAAC;IAChBC,qBAAqB,EAAE;EACzB;AACF,CAAC;;AAED;AACA,eAAeJ,EAAE", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}