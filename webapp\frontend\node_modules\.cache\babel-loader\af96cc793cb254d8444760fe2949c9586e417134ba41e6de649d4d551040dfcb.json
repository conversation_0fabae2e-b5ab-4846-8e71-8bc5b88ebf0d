{"ast": null, "code": "export default function (a, b) {\n  return b < a ? -1 : b > a ? 1 : b >= a ? 0 : NaN;\n}", "map": {"version": 3, "names": ["a", "b", "NaN"], "sources": ["C:/Users/<USER>/PycharmProjects/CMS/webapp/frontend/node_modules/d3-shape/src/descending.js"], "sourcesContent": ["export default function(a, b) {\n  return b < a ? -1 : b > a ? 1 : b >= a ? 0 : NaN;\n}\n"], "mappings": "AAAA,eAAe,UAASA,CAAC,EAAEC,CAAC,EAAE;EAC5B,OAAOA,CAAC,GAAGD,CAAC,GAAG,CAAC,CAAC,GAAGC,CAAC,GAAGD,CAAC,GAAG,CAAC,GAAGC,CAAC,IAAID,CAAC,GAAG,CAAC,GAAGE,GAAG;AAClD", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}