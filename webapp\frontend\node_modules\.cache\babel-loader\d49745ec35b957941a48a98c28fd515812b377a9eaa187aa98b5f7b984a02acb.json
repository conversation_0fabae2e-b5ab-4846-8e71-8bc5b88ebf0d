{"ast": null, "code": "'use client';\n\nimport _extends from \"@babel/runtime/helpers/esm/extends\";\nimport * as React from 'react';\nimport { singleItemFieldValueManager, singleItemValueManager } from \"../internals/utils/valueManagers.js\";\nimport { validateTime } from \"../validation/index.js\";\nimport { useUtils } from \"../internals/hooks/useUtils.js\";\nimport { usePickerTranslations } from \"../hooks/usePickerTranslations.js\";\nexport function useTimeManager(parameters = {}) {\n  const {\n    enableAccessibleFieldDOMStructure = true,\n    ampm\n  } = parameters;\n  return React.useMemo(() => ({\n    valueType: 'time',\n    validator: validateTime,\n    internal_valueManager: singleItemValueManager,\n    internal_fieldValueManager: singleItemFieldValueManager,\n    internal_enableAccessibleFieldDOMStructure: enableAccessibleFieldDOMStructure,\n    internal_useApplyDefaultValuesToFieldInternalProps: useApplyDefaultValuesToTimeFieldInternalProps,\n    internal_useOpenPickerButtonAriaLabel: createUseOpenPickerButtonAriaLabel(ampm)\n  }), [ampm, enableAccessibleFieldDOMStructure]);\n}\nfunction createUseOpenPickerButtonAriaLabel(ampm) {\n  return function useOpenPickerButtonAriaLabel(value) {\n    const utils = useUtils();\n    const translations = usePickerTranslations();\n    return React.useMemo(() => {\n      const formatKey = ampm ?? utils.is12HourCycleInCurrentLocale() ? 'fullTime12h' : 'fullTime24h';\n      const formattedValue = utils.isValid(value) ? utils.format(value, formatKey) : null;\n      return translations.openTimePickerDialogue(formattedValue);\n    }, [value, translations, utils]);\n  };\n}\nfunction useApplyDefaultValuesToTimeFieldInternalProps(internalProps) {\n  const utils = useUtils();\n  const validationProps = useApplyDefaultValuesToTimeValidationProps(internalProps);\n  const ampm = React.useMemo(() => internalProps.ampm ?? utils.is12HourCycleInCurrentLocale(), [internalProps.ampm, utils]);\n  return React.useMemo(() => _extends({}, internalProps, validationProps, {\n    format: internalProps.format ?? (ampm ? utils.formats.fullTime12h : utils.formats.fullTime24h)\n  }), [internalProps, validationProps, ampm, utils]);\n}\nexport function useApplyDefaultValuesToTimeValidationProps(props) {\n  return React.useMemo(() => ({\n    disablePast: props.disablePast ?? false,\n    disableFuture: props.disableFuture ?? false\n  }), [props.disablePast, props.disableFuture]);\n}", "map": {"version": 3, "names": ["_extends", "React", "singleItemFieldValueManager", "singleItemValueManager", "validateTime", "useUtils", "usePickerTranslations", "useTimeManager", "parameters", "enableAccessibleFieldDOMStructure", "ampm", "useMemo", "valueType", "validator", "internal_valueManager", "internal_fieldValueManager", "internal_enableAccessibleFieldDOMStructure", "internal_useApplyDefaultValuesToFieldInternalProps", "useApplyDefaultValuesToTimeFieldInternalProps", "internal_useOpenPickerButtonAriaLabel", "createUseOpenPickerButtonAriaLabel", "useOpenPickerButtonAriaLabel", "value", "utils", "translations", "formatKey", "is12HourCycleInCurrentLocale", "formattedValue", "<PERSON><PERSON><PERSON><PERSON>", "format", "openTimePickerDialogue", "internalProps", "validationProps", "useApplyDefaultValuesToTimeValidationProps", "formats", "fullTime12h", "fullTime24h", "props", "disablePast", "disableFuture"], "sources": ["C:/Users/<USER>/PycharmProjects/CMS/webapp/frontend/node_modules/@mui/x-date-pickers/esm/managers/useTimeManager.js"], "sourcesContent": ["'use client';\n\nimport _extends from \"@babel/runtime/helpers/esm/extends\";\nimport * as React from 'react';\nimport { singleItemFieldValueManager, singleItemValueManager } from \"../internals/utils/valueManagers.js\";\nimport { validateTime } from \"../validation/index.js\";\nimport { useUtils } from \"../internals/hooks/useUtils.js\";\nimport { usePickerTranslations } from \"../hooks/usePickerTranslations.js\";\nexport function useTimeManager(parameters = {}) {\n  const {\n    enableAccessibleFieldDOMStructure = true,\n    ampm\n  } = parameters;\n  return React.useMemo(() => ({\n    valueType: 'time',\n    validator: validateTime,\n    internal_valueManager: singleItemValueManager,\n    internal_fieldValueManager: singleItemFieldValueManager,\n    internal_enableAccessibleFieldDOMStructure: enableAccessibleFieldDOMStructure,\n    internal_useApplyDefaultValuesToFieldInternalProps: useApplyDefaultValuesToTimeFieldInternalProps,\n    internal_useOpenPickerButtonAriaLabel: createUseOpenPickerButtonAriaLabel(ampm)\n  }), [ampm, enableAccessibleFieldDOMStructure]);\n}\nfunction createUseOpenPickerButtonAriaLabel(ampm) {\n  return function useOpenPickerButtonAriaLabel(value) {\n    const utils = useUtils();\n    const translations = usePickerTranslations();\n    return React.useMemo(() => {\n      const formatKey = ampm ?? utils.is12HourCycleInCurrentLocale() ? 'fullTime12h' : 'fullTime24h';\n      const formattedValue = utils.isValid(value) ? utils.format(value, formatKey) : null;\n      return translations.openTimePickerDialogue(formattedValue);\n    }, [value, translations, utils]);\n  };\n}\nfunction useApplyDefaultValuesToTimeFieldInternalProps(internalProps) {\n  const utils = useUtils();\n  const validationProps = useApplyDefaultValuesToTimeValidationProps(internalProps);\n  const ampm = React.useMemo(() => internalProps.ampm ?? utils.is12HourCycleInCurrentLocale(), [internalProps.ampm, utils]);\n  return React.useMemo(() => _extends({}, internalProps, validationProps, {\n    format: internalProps.format ?? (ampm ? utils.formats.fullTime12h : utils.formats.fullTime24h)\n  }), [internalProps, validationProps, ampm, utils]);\n}\nexport function useApplyDefaultValuesToTimeValidationProps(props) {\n  return React.useMemo(() => ({\n    disablePast: props.disablePast ?? false,\n    disableFuture: props.disableFuture ?? false\n  }), [props.disablePast, props.disableFuture]);\n}"], "mappings": "AAAA,YAAY;;AAEZ,OAAOA,QAAQ,MAAM,oCAAoC;AACzD,OAAO,KAAKC,KAAK,MAAM,OAAO;AAC9B,SAASC,2BAA2B,EAAEC,sBAAsB,QAAQ,qCAAqC;AACzG,SAASC,YAAY,QAAQ,wBAAwB;AACrD,SAASC,QAAQ,QAAQ,gCAAgC;AACzD,SAASC,qBAAqB,QAAQ,mCAAmC;AACzE,OAAO,SAASC,cAAcA,CAACC,UAAU,GAAG,CAAC,CAAC,EAAE;EAC9C,MAAM;IACJC,iCAAiC,GAAG,IAAI;IACxCC;EACF,CAAC,GAAGF,UAAU;EACd,OAAOP,KAAK,CAACU,OAAO,CAAC,OAAO;IAC1BC,SAAS,EAAE,MAAM;IACjBC,SAAS,EAAET,YAAY;IACvBU,qBAAqB,EAAEX,sBAAsB;IAC7CY,0BAA0B,EAAEb,2BAA2B;IACvDc,0CAA0C,EAAEP,iCAAiC;IAC7EQ,kDAAkD,EAAEC,6CAA6C;IACjGC,qCAAqC,EAAEC,kCAAkC,CAACV,IAAI;EAChF,CAAC,CAAC,EAAE,CAACA,IAAI,EAAED,iCAAiC,CAAC,CAAC;AAChD;AACA,SAASW,kCAAkCA,CAACV,IAAI,EAAE;EAChD,OAAO,SAASW,4BAA4BA,CAACC,KAAK,EAAE;IAClD,MAAMC,KAAK,GAAGlB,QAAQ,CAAC,CAAC;IACxB,MAAMmB,YAAY,GAAGlB,qBAAqB,CAAC,CAAC;IAC5C,OAAOL,KAAK,CAACU,OAAO,CAAC,MAAM;MACzB,MAAMc,SAAS,GAAGf,IAAI,IAAIa,KAAK,CAACG,4BAA4B,CAAC,CAAC,GAAG,aAAa,GAAG,aAAa;MAC9F,MAAMC,cAAc,GAAGJ,KAAK,CAACK,OAAO,CAACN,KAAK,CAAC,GAAGC,KAAK,CAACM,MAAM,CAACP,KAAK,EAAEG,SAAS,CAAC,GAAG,IAAI;MACnF,OAAOD,YAAY,CAACM,sBAAsB,CAACH,cAAc,CAAC;IAC5D,CAAC,EAAE,CAACL,KAAK,EAAEE,YAAY,EAAED,KAAK,CAAC,CAAC;EAClC,CAAC;AACH;AACA,SAASL,6CAA6CA,CAACa,aAAa,EAAE;EACpE,MAAMR,KAAK,GAAGlB,QAAQ,CAAC,CAAC;EACxB,MAAM2B,eAAe,GAAGC,0CAA0C,CAACF,aAAa,CAAC;EACjF,MAAMrB,IAAI,GAAGT,KAAK,CAACU,OAAO,CAAC,MAAMoB,aAAa,CAACrB,IAAI,IAAIa,KAAK,CAACG,4BAA4B,CAAC,CAAC,EAAE,CAACK,aAAa,CAACrB,IAAI,EAAEa,KAAK,CAAC,CAAC;EACzH,OAAOtB,KAAK,CAACU,OAAO,CAAC,MAAMX,QAAQ,CAAC,CAAC,CAAC,EAAE+B,aAAa,EAAEC,eAAe,EAAE;IACtEH,MAAM,EAAEE,aAAa,CAACF,MAAM,KAAKnB,IAAI,GAAGa,KAAK,CAACW,OAAO,CAACC,WAAW,GAAGZ,KAAK,CAACW,OAAO,CAACE,WAAW;EAC/F,CAAC,CAAC,EAAE,CAACL,aAAa,EAAEC,eAAe,EAAEtB,IAAI,EAAEa,KAAK,CAAC,CAAC;AACpD;AACA,OAAO,SAASU,0CAA0CA,CAACI,KAAK,EAAE;EAChE,OAAOpC,KAAK,CAACU,OAAO,CAAC,OAAO;IAC1B2B,WAAW,EAAED,KAAK,CAACC,WAAW,IAAI,KAAK;IACvCC,aAAa,EAAEF,KAAK,CAACE,aAAa,IAAI;EACxC,CAAC,CAAC,EAAE,CAACF,KAAK,CAACC,WAAW,EAAED,KAAK,CAACE,aAAa,CAAC,CAAC;AAC/C", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}