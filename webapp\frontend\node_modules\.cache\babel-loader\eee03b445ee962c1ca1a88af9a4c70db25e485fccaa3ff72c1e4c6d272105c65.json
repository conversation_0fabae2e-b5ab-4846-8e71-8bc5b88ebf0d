{"ast": null, "code": "var _jsxFileName = \"C:\\\\CMS\\\\webapp\\\\frontend\\\\src\\\\components\\\\comande\\\\CreaComandaConCavi.js\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect } from 'react';\nimport { Box, Card, CardContent, Typography, Button, Dialog, DialogTitle, DialogContent, DialogActions, TextField, MenuItem, Alert, CircularProgress, Stepper, Step, StepLabel, Table, TableBody, TableCell, TableContainer, TableHead, TableRow, Paper, Checkbox, Chip, Grid, Autocomplete } from '@mui/material';\nimport { Add as AddIcon, Cable as CableIcon, Assignment as AssignmentIcon } from '@mui/icons-material';\nimport comandeService from '../../services/comandeService';\nimport responsabiliService from '../../services/responsabiliService';\nimport { jsxDEV as _jsxDEV, Fragment as _Fragment } from \"react/jsx-dev-runtime\";\nconst CreaComandaConCavi = ({\n  cantiereId,\n  open,\n  onClose,\n  onSuccess,\n  tipoComandaPreselezionato = null,\n  caviPreselezionati = []\n}) => {\n  _s();\n  const [activeStep, setActiveStep] = useState(0);\n  const [loading, setLoading] = useState(false);\n  const [error, setError] = useState(null);\n\n  // Step 1: Selezione tipo comanda\n  const [tipoComanda, setTipoComanda] = useState(tipoComandaPreselezionato || 'POSA');\n\n  // Aggiorna il tipo comanda quando cambia il prop\n  useEffect(() => {\n    if (tipoComandaPreselezionato) {\n      setTipoComanda(tipoComandaPreselezionato);\n    }\n  }, [tipoComandaPreselezionato]);\n\n  // Step 2: Selezione cavi\n  const [caviDisponibili, setCaviDisponibili] = useState([]);\n  const [caviSelezionati, setCaviSelezionati] = useState(caviPreselezionati || []);\n  const [loadingCavi, setLoadingCavi] = useState(false);\n\n  // Step 3: Dettagli comanda\n  const [formData, setFormData] = useState({\n    responsabile: '',\n    responsabile_email: '',\n    responsabile_telefono: '',\n    data_scadenza: '',\n    numero_componenti_squadra: 1,\n    note_capo_cantiere: ''\n  });\n\n  // Gestione responsabili\n  const [responsabiliDisponibili, setResponsabiliDisponibili] = useState([]);\n  const [loadingResponsabili, setLoadingResponsabili] = useState(false);\n  const steps = ['Tipo Comanda', 'Selezione Cavi', 'Dettagli Comanda'];\n\n  // Carica cavi disponibili quando cambia il tipo comanda\n  useEffect(() => {\n    if (open && tipoComanda) {\n      loadCaviDisponibili();\n    }\n  }, [open, tipoComanda]);\n\n  // Carica responsabili disponibili quando si apre il dialog\n  useEffect(() => {\n    if (open && cantiereId) {\n      loadResponsabiliDisponibili();\n    }\n  }, [open, cantiereId]);\n\n  // Se ci sono cavi preselezionati, salta al passo dei dettagli\n  useEffect(() => {\n    if (open && caviPreselezionati.length > 0 && tipoComandaPreselezionato) {\n      setActiveStep(2); // Vai direttamente ai dettagli\n      setCaviSelezionati(caviPreselezionati); // Imposta i cavi preselezionati\n    }\n  }, [open, caviPreselezionati, tipoComandaPreselezionato]);\n  const loadCaviDisponibili = async () => {\n    try {\n      setLoadingCavi(true);\n      const response = await comandeService.getCaviDisponibili(cantiereId, tipoComanda);\n      setCaviDisponibili(response.cavi_disponibili || []);\n\n      // Non resettare la selezione se ci sono cavi preselezionati\n      if (caviPreselezionati.length === 0) {\n        setCaviSelezionati([]);\n      }\n      setError(null);\n    } catch (err) {\n      console.error('Errore nel caricamento dei cavi:', err);\n      setError('Errore nel caricamento dei cavi disponibili');\n    } finally {\n      setLoadingCavi(false);\n    }\n  };\n  const loadResponsabiliDisponibili = async () => {\n    try {\n      setLoadingResponsabili(true);\n      const responsabili = await responsabiliService.getResponsabiliCantiere(cantiereId);\n      setResponsabiliDisponibili(responsabili || []);\n    } catch (err) {\n      console.error('Errore nel caricamento dei responsabili:', err);\n      // Non mostrare errore per i responsabili, è opzionale\n    } finally {\n      setLoadingResponsabili(false);\n    }\n  };\n  const handleNext = () => {\n    if (activeStep === 1 && caviSelezionati.length === 0) {\n      setError('Seleziona almeno un cavo');\n      return;\n    }\n    if (activeStep === 2) {\n      if (!formData.responsabile.trim()) {\n        setError('Seleziona un responsabile dall\\'elenco');\n        return;\n      }\n    }\n    setError(null);\n    setActiveStep(prevStep => prevStep + 1);\n  };\n  const handleBack = () => {\n    setActiveStep(prevStep => prevStep - 1);\n  };\n  const handleCavoToggle = cavo => {\n    const isSelected = caviSelezionati.some(c => c.id_cavo === cavo.id_cavo);\n    if (isSelected) {\n      setCaviSelezionati(caviSelezionati.filter(c => c.id_cavo !== cavo.id_cavo));\n    } else {\n      setCaviSelezionati([...caviSelezionati, cavo]);\n    }\n  };\n  const handleSubmit = async () => {\n    try {\n      setLoading(true);\n      setError(null);\n      console.log('🚀 Inizio creazione comanda:', {\n        tipoComanda,\n        responsabile: formData.responsabile,\n        numeroCavi: caviSelezionati.length\n      });\n      const comandaData = {\n        tipo_comanda: tipoComanda,\n        descrizione: `Comanda ${getTipoComandaLabel(tipoComanda)} per ${caviSelezionati.length} cavi`,\n        responsabile: formData.responsabile,\n        responsabile_email: formData.responsabile_email || null,\n        responsabile_telefono: formData.responsabile_telefono || null,\n        data_scadenza: formData.data_scadenza || null,\n        numero_componenti_squadra: formData.numero_componenti_squadra || 1,\n        note_capo_cantiere: formData.note_capo_cantiere\n      };\n      const listaIdCavi = caviSelezionati.map(c => c.id_cavo);\n      console.log('📋 Dati comanda preparati:', comandaData);\n      console.log('🔗 Cavi da assegnare:', listaIdCavi);\n      const response = await comandeService.createComandaConCavi(cantiereId, comandaData, listaIdCavi);\n      console.log('✅ Comanda creata con successo:', response);\n\n      // Mostra messaggio di successo specifico per tipo comanda\n      let successMessage = `Comanda ${getTipoComandaLabel(tipoComanda)} creata con successo!`;\n      if (tipoComanda === 'POSA') {\n        successMessage += ` I ${caviSelezionati.length} cavi sono ora \"In corso\" e assegnati a ${formData.responsabile}.`;\n      }\n      console.log('📢 Messaggio successo:', successMessage);\n      onSuccess && onSuccess(response, successMessage);\n      handleClose();\n    } catch (err) {\n      console.error('❌ Errore nella creazione comanda:', err);\n      let errorMessage = 'Errore nella creazione della comanda';\n      if (err.detail) {\n        errorMessage = err.detail;\n      } else if (err.message) {\n        errorMessage = err.message;\n      } else if (typeof err === 'string') {\n        errorMessage = err;\n      }\n      console.error('📢 Messaggio errore:', errorMessage);\n      setError(errorMessage);\n    } finally {\n      setLoading(false);\n    }\n  };\n  const handleClose = () => {\n    // Reset al passo iniziale solo se non ci sono cavi preselezionati\n    if (caviPreselezionati.length > 0 && tipoComandaPreselezionato) {\n      setActiveStep(2); // Torna ai dettagli se ci sono cavi preselezionati\n    } else {\n      setActiveStep(0); // Altrimenti torna al primo passo\n    }\n    setTipoComanda(tipoComandaPreselezionato || 'POSA');\n    setCaviDisponibili([]);\n    setCaviSelezionati(caviPreselezionati || []);\n    setFormData({\n      responsabile: '',\n      responsabile_email: '',\n      responsabile_telefono: '',\n      data_scadenza: '',\n      numero_componenti_squadra: 1,\n      note_capo_cantiere: ''\n    });\n    setError(null);\n    onClose && onClose();\n  };\n  const getTipoComandaLabel = tipo => {\n    switch (tipo) {\n      case 'POSA':\n        return 'Posa';\n      case 'COLLEGAMENTO_PARTENZA':\n        return 'Collegamento Partenza';\n      case 'COLLEGAMENTO_ARRIVO':\n        return 'Collegamento Arrivo';\n      case 'CERTIFICAZIONE':\n        return 'Certificazione';\n      default:\n        return tipo;\n    }\n  };\n  const renderStepContent = () => {\n    switch (activeStep) {\n      case 0:\n        return /*#__PURE__*/_jsxDEV(Box, {\n          children: [/*#__PURE__*/_jsxDEV(Typography, {\n            variant: \"h6\",\n            gutterBottom: true,\n            children: \"Seleziona il tipo di comanda\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 267,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(TextField, {\n            fullWidth: true,\n            select: true,\n            label: \"Tipo Comanda\",\n            value: tipoComanda,\n            onChange: e => setTipoComanda(e.target.value),\n            margin: \"normal\",\n            children: [/*#__PURE__*/_jsxDEV(MenuItem, {\n              value: \"POSA\",\n              children: \"Posa\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 278,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(MenuItem, {\n              value: \"COLLEGAMENTO_PARTENZA\",\n              children: \"Collegamento Partenza\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 279,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(MenuItem, {\n              value: \"COLLEGAMENTO_ARRIVO\",\n              children: \"Collegamento Arrivo\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 280,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(MenuItem, {\n              value: \"CERTIFICAZIONE\",\n              children: \"Certificazione\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 281,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 270,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Alert, {\n            severity: \"info\",\n            sx: {\n              mt: 2\n            },\n            children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n              children: getTipoComandaLabel(tipoComanda)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 284,\n              columnNumber: 15\n            }, this), \":\", tipoComanda === 'POSA' && ' Comanda per la posa fisica dei cavi', tipoComanda === 'COLLEGAMENTO_PARTENZA' && ' Comanda per il collegamento lato partenza', tipoComanda === 'COLLEGAMENTO_ARRIVO' && ' Comanda per il collegamento lato arrivo', tipoComanda === 'CERTIFICAZIONE' && ' Comanda per la certificazione e test dei cavi']\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 283,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 266,\n          columnNumber: 11\n        }, this);\n      case 1:\n        return /*#__PURE__*/_jsxDEV(Box, {\n          children: [/*#__PURE__*/_jsxDEV(Typography, {\n            variant: \"h6\",\n            gutterBottom: true,\n            children: [\"Seleziona i cavi per la comanda di \", getTipoComandaLabel(tipoComanda)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 296,\n            columnNumber: 13\n          }, this), loadingCavi ? /*#__PURE__*/_jsxDEV(Box, {\n            display: \"flex\",\n            justifyContent: \"center\",\n            p: 3,\n            children: /*#__PURE__*/_jsxDEV(CircularProgress, {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 302,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 301,\n            columnNumber: 15\n          }, this) : /*#__PURE__*/_jsxDEV(_Fragment, {\n            children: [/*#__PURE__*/_jsxDEV(Box, {\n              mb: 2,\n              children: /*#__PURE__*/_jsxDEV(Chip, {\n                icon: /*#__PURE__*/_jsxDEV(CableIcon, {}, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 308,\n                  columnNumber: 27\n                }, this),\n                label: `${caviSelezionati.length} cavi selezionati di ${caviDisponibili.length} disponibili`,\n                color: caviSelezionati.length > 0 ? 'primary' : 'default'\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 307,\n                columnNumber: 19\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 306,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(TableContainer, {\n              component: Paper,\n              sx: {\n                maxHeight: 400\n              },\n              children: /*#__PURE__*/_jsxDEV(Table, {\n                stickyHeader: true,\n                children: [/*#__PURE__*/_jsxDEV(TableHead, {\n                  children: /*#__PURE__*/_jsxDEV(TableRow, {\n                    children: [/*#__PURE__*/_jsxDEV(TableCell, {\n                      padding: \"checkbox\",\n                      children: \"Sel.\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 318,\n                      columnNumber: 25\n                    }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n                      children: \"ID Cavo\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 319,\n                      columnNumber: 25\n                    }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n                      children: \"Tipologia\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 320,\n                      columnNumber: 25\n                    }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n                      children: \"Sezione\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 321,\n                      columnNumber: 25\n                    }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n                      children: \"Metri\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 322,\n                      columnNumber: 25\n                    }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n                      children: \"Partenza\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 323,\n                      columnNumber: 25\n                    }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n                      children: \"Arrivo\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 324,\n                      columnNumber: 25\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 317,\n                    columnNumber: 23\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 316,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(TableBody, {\n                  children: caviDisponibili.map(cavo => {\n                    const isSelected = caviSelezionati.some(c => c.id_cavo === cavo.id_cavo);\n                    return /*#__PURE__*/_jsxDEV(TableRow, {\n                      hover: true,\n                      onClick: () => handleCavoToggle(cavo),\n                      sx: {\n                        cursor: 'pointer'\n                      },\n                      children: [/*#__PURE__*/_jsxDEV(TableCell, {\n                        padding: \"checkbox\",\n                        children: /*#__PURE__*/_jsxDEV(Checkbox, {\n                          checked: isSelected\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 338,\n                          columnNumber: 31\n                        }, this)\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 337,\n                        columnNumber: 29\n                      }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n                        children: cavo.id_cavo\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 340,\n                        columnNumber: 29\n                      }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n                        children: cavo.tipologia\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 341,\n                        columnNumber: 29\n                      }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n                        children: cavo.sezione\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 342,\n                        columnNumber: 29\n                      }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n                        children: cavo.metri_teorici\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 343,\n                        columnNumber: 29\n                      }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n                        children: cavo.ubicazione_partenza\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 344,\n                        columnNumber: 29\n                      }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n                        children: cavo.ubicazione_arrivo\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 345,\n                        columnNumber: 29\n                      }, this)]\n                    }, cavo.id_cavo, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 331,\n                      columnNumber: 27\n                    }, this);\n                  })\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 327,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 315,\n                columnNumber: 19\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 314,\n              columnNumber: 17\n            }, this), caviDisponibili.length === 0 && /*#__PURE__*/_jsxDEV(Alert, {\n              severity: \"warning\",\n              sx: {\n                mt: 2\n              },\n              children: \"Nessun cavo disponibile per il tipo di comanda selezionato.\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 354,\n              columnNumber: 19\n            }, this)]\n          }, void 0, true)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 295,\n          columnNumber: 11\n        }, this);\n      case 2:\n        return /*#__PURE__*/_jsxDEV(Box, {\n          children: [/*#__PURE__*/_jsxDEV(Typography, {\n            variant: \"h6\",\n            gutterBottom: true,\n            children: \"Dettagli della comanda\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 366,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Grid, {\n            container: true,\n            spacing: 3,\n            children: [/*#__PURE__*/_jsxDEV(Grid, {\n              item: true,\n              xs: 12,\n              sm: 8,\n              children: /*#__PURE__*/_jsxDEV(TextField, {\n                fullWidth: true,\n                select: true,\n                label: \"Responsabile\",\n                value: formData.responsabile,\n                onChange: e => {\n                  const nomeResponsabile = e.target.value;\n                  const responsabile = responsabiliDisponibili.find(r => r.nome_responsabile === nomeResponsabile);\n                  if (responsabile) {\n                    setFormData({\n                      ...formData,\n                      responsabile: nomeResponsabile,\n                      responsabile_email: responsabile.email || '',\n                      responsabile_telefono: responsabile.telefono || ''\n                    });\n                  } else {\n                    setFormData({\n                      ...formData,\n                      responsabile: nomeResponsabile\n                    });\n                  }\n                },\n                required: true,\n                helperText: \"Chi eseguir\\xE0 il lavoro (obbligatorio)\",\n                size: \"medium\",\n                InputProps: {\n                  endAdornment: loadingResponsabili ? /*#__PURE__*/_jsxDEV(CircularProgress, {\n                    color: \"inherit\",\n                    size: 20\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 396,\n                    columnNumber: 57\n                  }, this) : null\n                },\n                children: responsabiliDisponibili.map(responsabile => /*#__PURE__*/_jsxDEV(MenuItem, {\n                  value: responsabile.nome_responsabile,\n                  children: /*#__PURE__*/_jsxDEV(Box, {\n                    children: [/*#__PURE__*/_jsxDEV(Typography, {\n                      variant: \"body1\",\n                      children: responsabile.nome_responsabile\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 402,\n                      columnNumber: 25\n                    }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                      variant: \"body2\",\n                      color: \"textSecondary\",\n                      children: [responsabile.email && `📧 ${responsabile.email}`, responsabile.email && responsabile.telefono && ' • ', responsabile.telefono && `📞 ${responsabile.telefono}`]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 405,\n                      columnNumber: 25\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 401,\n                    columnNumber: 23\n                  }, this)\n                }, responsabile.id_responsabile, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 400,\n                  columnNumber: 21\n                }, this))\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 373,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 372,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Grid, {\n              item: true,\n              xs: 12,\n              sm: 4,\n              children: /*#__PURE__*/_jsxDEV(TextField, {\n                fullWidth: true,\n                label: \"Data Scadenza\",\n                type: \"date\",\n                value: formData.data_scadenza,\n                onChange: e => setFormData({\n                  ...formData,\n                  data_scadenza: e.target.value\n                }),\n                InputLabelProps: {\n                  shrink: true\n                },\n                helperText: \"Scadenza prevista\",\n                size: \"medium\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 417,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 416,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Grid, {\n              item: true,\n              xs: 12,\n              sm: 6,\n              children: /*#__PURE__*/_jsxDEV(TextField, {\n                fullWidth: true,\n                label: \"Numero Componenti Squadra\",\n                type: \"number\",\n                value: formData.numero_componenti_squadra,\n                onChange: e => setFormData({\n                  ...formData,\n                  numero_componenti_squadra: Math.max(1, parseInt(e.target.value) || 1)\n                }),\n                inputProps: {\n                  min: 1,\n                  max: 20\n                },\n                helperText: \"Persone previste per il lavoro (per calcolo resa oraria)\",\n                size: \"medium\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 431,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 430,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 370,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Grid, {\n            container: true,\n            spacing: 3,\n            sx: {\n              mt: 1\n            },\n            children: /*#__PURE__*/_jsxDEV(Grid, {\n              item: true,\n              xs: 12,\n              children: /*#__PURE__*/_jsxDEV(TextField, {\n                fullWidth: true,\n                label: \"Note per il Responsabile\",\n                value: formData.note_capo_cantiere,\n                onChange: e => setFormData({\n                  ...formData,\n                  note_capo_cantiere: e.target.value\n                }),\n                multiline: true,\n                rows: 3,\n                helperText: \"Istruzioni specifiche per il responsabile\",\n                size: \"medium\",\n                placeholder: \"Inserisci eventuali istruzioni specifiche, precauzioni o dettagli tecnici per l'esecuzione del lavoro...\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 450,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 449,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 448,\n            columnNumber: 13\n          }, this), formData.responsabile && /*#__PURE__*/_jsxDEV(Box, {\n            sx: {\n              mt: 3,\n              p: 2,\n              bgcolor: 'grey.50',\n              borderRadius: 1\n            },\n            children: [/*#__PURE__*/_jsxDEV(Typography, {\n              variant: \"subtitle2\",\n              gutterBottom: true,\n              children: \"\\uD83D\\uDCCB Riepilogo Comanda:\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 467,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(Box, {\n              sx: {\n                display: 'flex',\n                flexWrap: 'wrap',\n                gap: 2,\n                mt: 1\n              },\n              children: [/*#__PURE__*/_jsxDEV(Typography, {\n                variant: \"body2\",\n                children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n                  children: \"Tipo:\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 472,\n                  columnNumber: 21\n                }, this), \" \", getTipoComandaLabel(tipoComanda)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 471,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                variant: \"body2\",\n                children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n                  children: \"Cavi selezionati:\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 475,\n                  columnNumber: 21\n                }, this), \" \", caviSelezionati.length]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 474,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                variant: \"body2\",\n                children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n                  children: \"Responsabile:\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 478,\n                  columnNumber: 21\n                }, this), \" \", formData.responsabile]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 477,\n                columnNumber: 19\n              }, this), formData.numero_componenti_squadra > 1 && /*#__PURE__*/_jsxDEV(Typography, {\n                variant: \"body2\",\n                children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n                  children: \"Squadra:\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 482,\n                  columnNumber: 23\n                }, this), \" \", formData.numero_componenti_squadra, \" persone\"]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 481,\n                columnNumber: 21\n              }, this), formData.data_scadenza && /*#__PURE__*/_jsxDEV(Typography, {\n                variant: \"body2\",\n                children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n                  children: \"Scadenza:\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 487,\n                  columnNumber: 23\n                }, this), \" \", new Date(formData.data_scadenza).toLocaleDateString('it-IT')]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 486,\n                columnNumber: 21\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 470,\n              columnNumber: 17\n            }, this), caviSelezionati.length > 0 && /*#__PURE__*/_jsxDEV(Box, {\n              sx: {\n                mt: 2\n              },\n              children: [/*#__PURE__*/_jsxDEV(Typography, {\n                variant: \"body2\",\n                sx: {\n                  fontWeight: 'bold',\n                  mb: 1\n                },\n                children: \"\\uD83D\\uDD17 Cavi assegnati:\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 495,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(Box, {\n                sx: {\n                  display: 'flex',\n                  flexWrap: 'wrap',\n                  gap: 0.5,\n                  maxHeight: '120px',\n                  overflowY: 'auto',\n                  p: 1,\n                  bgcolor: 'background.paper',\n                  borderRadius: 1,\n                  border: '1px solid',\n                  borderColor: 'divider'\n                },\n                children: caviSelezionati.map((cavo, index) => /*#__PURE__*/_jsxDEV(Box, {\n                  sx: {\n                    display: 'inline-flex',\n                    alignItems: 'center',\n                    bgcolor: 'primary.light',\n                    color: 'primary.contrastText',\n                    px: 1,\n                    py: 0.5,\n                    borderRadius: 1,\n                    fontSize: '0.75rem',\n                    fontWeight: 'medium'\n                  },\n                  children: [cavo.id_cavo, cavo.tipologia && /*#__PURE__*/_jsxDEV(Typography, {\n                    variant: \"caption\",\n                    sx: {\n                      ml: 0.5,\n                      opacity: 0.8\n                    },\n                    children: [\"(\", cavo.tipologia, \")\"]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 527,\n                    columnNumber: 29\n                  }, this)]\n                }, cavo.id_cavo, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 511,\n                  columnNumber: 25\n                }, this))\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 498,\n                columnNumber: 21\n              }, this), (() => {\n                const metriTotali = caviSelezionati.reduce((sum, cavo) => sum + (cavo.metri_teorici || 0), 0);\n                const tipologie = [...new Set(caviSelezionati.map(c => c.tipologia).filter(Boolean))];\n                return /*#__PURE__*/_jsxDEV(Box, {\n                  sx: {\n                    mt: 1,\n                    display: 'flex',\n                    flexWrap: 'wrap',\n                    gap: 2\n                  },\n                  children: [metriTotali > 0 && /*#__PURE__*/_jsxDEV(Typography, {\n                    variant: \"caption\",\n                    color: \"text.secondary\",\n                    children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n                      children: \"Metri totali:\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 544,\n                      columnNumber: 31\n                    }, this), \" \", metriTotali.toFixed(0), \"m\"]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 543,\n                    columnNumber: 29\n                  }, this), tipologie.length > 0 && /*#__PURE__*/_jsxDEV(Typography, {\n                    variant: \"caption\",\n                    color: \"text.secondary\",\n                    children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n                      children: \"Tipologie:\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 549,\n                      columnNumber: 31\n                    }, this), \" \", tipologie.join(', ')]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 548,\n                    columnNumber: 29\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 541,\n                  columnNumber: 25\n                }, this);\n              })()]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 494,\n              columnNumber: 19\n            }, this), formData.data_scadenza && formData.numero_componenti_squadra && /*#__PURE__*/_jsxDEV(Box, {\n              sx: {\n                mt: 2,\n                p: 1.5,\n                bgcolor: 'info.light',\n                borderRadius: 1,\n                color: 'info.contrastText'\n              },\n              children: [/*#__PURE__*/_jsxDEV(Typography, {\n                variant: \"caption\",\n                sx: {\n                  fontWeight: 'bold'\n                },\n                children: \"\\uD83D\\uDCA1 Stima Lavoro:\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 561,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(Box, {\n                sx: {\n                  display: 'flex',\n                  flexWrap: 'wrap',\n                  gap: 2,\n                  mt: 0.5\n                },\n                children: (() => {\n                  const oggi = new Date();\n                  const scadenza = new Date(formData.data_scadenza);\n                  const giorniDisponibili = Math.max(1, Math.ceil((scadenza - oggi) / (1000 * 60 * 60 * 24)));\n                  const oreGiornaliere = 8; // Ore lavorative standard\n                  const oreTotaliDisponibili = giorniDisponibili * oreGiornaliere * formData.numero_componenti_squadra;\n\n                  // Stima metri totali (approssimativa)\n                  const metriTotali = caviSelezionati.reduce((sum, cavo) => sum + (cavo.metri_teorici || 0), 0);\n\n                  // Resa stimata in base al tipo di comanda\n                  let resaStimata = 0;\n                  let unitaMisura = '';\n                  if (tipoComanda === 'POSA') {\n                    resaStimata = metriTotali > 0 ? (oreTotaliDisponibili / metriTotali * formData.numero_componenti_squadra).toFixed(1) : 0;\n                    unitaMisura = 'm/h per persona';\n                  } else if (tipoComanda.includes('COLLEGAMENTO')) {\n                    resaStimata = caviSelezionati.length > 0 ? (oreTotaliDisponibili / caviSelezionati.length).toFixed(1) : 0;\n                    unitaMisura = 'h per collegamento';\n                  } else if (tipoComanda === 'CERTIFICAZIONE') {\n                    resaStimata = caviSelezionati.length > 0 ? (oreTotaliDisponibili / caviSelezionati.length).toFixed(1) : 0;\n                    unitaMisura = 'h per test';\n                  }\n                  return /*#__PURE__*/_jsxDEV(_Fragment, {\n                    children: [/*#__PURE__*/_jsxDEV(Typography, {\n                      variant: \"caption\",\n                      children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n                        children: \"Giorni disponibili:\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 593,\n                        columnNumber: 31\n                      }, this), \" \", giorniDisponibili]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 592,\n                      columnNumber: 29\n                    }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                      variant: \"caption\",\n                      children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n                        children: \"Ore totali squadra:\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 596,\n                        columnNumber: 31\n                      }, this), \" \", oreTotaliDisponibili, \"h\"]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 595,\n                      columnNumber: 29\n                    }, this), tipoComanda === 'POSA' && metriTotali > 0 && /*#__PURE__*/_jsxDEV(Typography, {\n                      variant: \"caption\",\n                      children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n                        children: \"Metri totali:\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 600,\n                        columnNumber: 33\n                      }, this), \" \", metriTotali.toFixed(0), \"m\"]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 599,\n                      columnNumber: 31\n                    }, this), resaStimata > 0 && /*#__PURE__*/_jsxDEV(Typography, {\n                      variant: \"caption\",\n                      children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n                        children: \"Resa richiesta:\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 605,\n                        columnNumber: 33\n                      }, this), \" \", resaStimata, \" \", unitaMisura]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 604,\n                      columnNumber: 31\n                    }, this)]\n                  }, void 0, true);\n                })()\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 564,\n                columnNumber: 21\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 560,\n              columnNumber: 19\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 466,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 365,\n          columnNumber: 11\n        }, this);\n      default:\n        return null;\n    }\n  };\n  return /*#__PURE__*/_jsxDEV(Dialog, {\n    open: open,\n    onClose: handleClose,\n    maxWidth: \"lg\",\n    fullWidth: true,\n    children: [/*#__PURE__*/_jsxDEV(DialogTitle, {\n      children: /*#__PURE__*/_jsxDEV(Box, {\n        display: \"flex\",\n        alignItems: \"center\",\n        gap: 1,\n        children: [/*#__PURE__*/_jsxDEV(AssignmentIcon, {}, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 628,\n          columnNumber: 11\n        }, this), \"Crea Nuova Comanda con Cavi\"]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 627,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 626,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(DialogContent, {\n      children: /*#__PURE__*/_jsxDEV(Box, {\n        sx: {\n          pt: 2\n        },\n        children: [/*#__PURE__*/_jsxDEV(Stepper, {\n          activeStep: activeStep,\n          sx: {\n            mb: 3\n          },\n          children: steps.map(label => /*#__PURE__*/_jsxDEV(Step, {\n            children: /*#__PURE__*/_jsxDEV(StepLabel, {\n              children: label\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 638,\n              columnNumber: 17\n            }, this)\n          }, label, false, {\n            fileName: _jsxFileName,\n            lineNumber: 637,\n            columnNumber: 15\n          }, this))\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 635,\n          columnNumber: 11\n        }, this), error && /*#__PURE__*/_jsxDEV(Alert, {\n          severity: \"error\",\n          sx: {\n            mb: 2\n          },\n          children: error\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 644,\n          columnNumber: 13\n        }, this), renderStepContent()]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 634,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 633,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(DialogActions, {\n      sx: {\n        p: 3,\n        gap: 2\n      },\n      children: activeStep < steps.length - 1 ? /*#__PURE__*/_jsxDEV(_Fragment, {\n        children: [/*#__PURE__*/_jsxDEV(Button, {\n          onClick: handleClose,\n          variant: \"outlined\",\n          sx: {\n            minWidth: 120\n          },\n          children: \"Chiudi\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 656,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(Button, {\n          onClick: handleNext,\n          variant: \"contained\",\n          disabled: activeStep === 1 && loadingCavi,\n          sx: {\n            minWidth: 120\n          },\n          children: \"Avanti\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 663,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true) : /*#__PURE__*/_jsxDEV(_Fragment, {\n        children: [/*#__PURE__*/_jsxDEV(Button, {\n          onClick: handleClose,\n          variant: \"outlined\",\n          sx: {\n            minWidth: 120\n          },\n          children: \"Chiudi\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 674,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(Button, {\n          onClick: handleSubmit,\n          variant: \"contained\",\n          disabled: loading || caviSelezionati.length === 0 || !formData.responsabile.trim(),\n          startIcon: loading ? /*#__PURE__*/_jsxDEV(CircularProgress, {\n            size: 20\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 689,\n            columnNumber: 36\n          }, this) : /*#__PURE__*/_jsxDEV(AddIcon, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 689,\n            columnNumber: 69\n          }, this),\n          sx: {\n            minWidth: 120\n          },\n          children: loading ? 'Creazione...' : 'Crea Comanda'\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 681,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 653,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 625,\n    columnNumber: 5\n  }, this);\n};\n_s(CreaComandaConCavi, \"Fz2p7/42rayudcftW6UwYbVzVLs=\");\n_c = CreaComandaConCavi;\nexport default CreaComandaConCavi;\nvar _c;\n$RefreshReg$(_c, \"CreaComandaConCavi\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "Box", "Card", "<PERSON><PERSON><PERSON><PERSON>", "Typography", "<PERSON><PERSON>", "Dialog", "DialogTitle", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "DialogActions", "TextField", "MenuItem", "<PERSON><PERSON>", "CircularProgress", "Stepper", "Step", "<PERSON><PERSON><PERSON><PERSON>", "Table", "TableBody", "TableCell", "TableContainer", "TableHead", "TableRow", "Paper", "Checkbox", "Chip", "Grid", "Autocomplete", "Add", "AddIcon", "Cable", "CableIcon", "Assignment", "AssignmentIcon", "comandeService", "responsabiliService", "jsxDEV", "_jsxDEV", "Fragment", "_Fragment", "CreaComandaConCavi", "cantiereId", "open", "onClose", "onSuccess", "tipoComandaPreselezionato", "caviPreselezionati", "_s", "activeStep", "setActiveStep", "loading", "setLoading", "error", "setError", "tipoComanda", "setTipoComanda", "caviDisponibili", "setCaviDisponibili", "caviSelezionati", "setCaviSelezionati", "loadingCavi", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "formData", "setFormData", "responsabile", "responsabile_email", "responsabile_telefono", "data_scadenza", "numero_componenti_squadra", "note_capo_cantiere", "responsabiliDisponibili", "setResponsabiliDisponibili", "loadingResponsabili", "setLoadingResponsabili", "steps", "loadCaviDisponibili", "loadResponsabiliDisponibili", "length", "response", "getCaviDisponibili", "cavi_disponibili", "err", "console", "responsabili", "getResponsabiliCantiere", "handleNext", "trim", "prevStep", "handleBack", "handleCavoToggle", "cavo", "isSelected", "some", "c", "id_cavo", "filter", "handleSubmit", "log", "numeroCavi", "comandaData", "tipo_comanda", "descrizione", "getTipoComandaLabel", "listaIdCavi", "map", "createComandaConCavi", "successMessage", "handleClose", "errorMessage", "detail", "message", "tipo", "renderStepContent", "children", "variant", "gutterBottom", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "fullWidth", "select", "label", "value", "onChange", "e", "target", "margin", "severity", "sx", "mt", "display", "justifyContent", "p", "mb", "icon", "color", "component", "maxHeight", "<PERSON><PERSON><PERSON><PERSON>", "padding", "hover", "onClick", "cursor", "checked", "tipologia", "sezione", "metri_te<PERSON>ci", "ubicazione_partenza", "ubicazione_arrivo", "container", "spacing", "item", "xs", "sm", "nomeResponsabile", "find", "r", "nome_responsabile", "email", "telefono", "required", "helperText", "size", "InputProps", "endAdornment", "id_responsabile", "type", "InputLabelProps", "shrink", "Math", "max", "parseInt", "inputProps", "min", "multiline", "rows", "placeholder", "bgcolor", "borderRadius", "flexWrap", "gap", "Date", "toLocaleDateString", "fontWeight", "overflowY", "border", "borderColor", "index", "alignItems", "px", "py", "fontSize", "ml", "opacity", "metriTotali", "reduce", "sum", "tipologie", "Set", "Boolean", "toFixed", "join", "oggi", "scadenza", "gior<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "ceil", "oreGiornaliere", "oreTotaliDisponibili", "resaS<PERSON><PERSON>", "unitaMisura", "includes", "max<PERSON><PERSON><PERSON>", "pt", "min<PERSON><PERSON><PERSON>", "disabled", "startIcon", "_c", "$RefreshReg$"], "sources": ["C:/CMS/webapp/frontend/src/components/comande/CreaComandaConCavi.js"], "sourcesContent": ["import React, { useState, useEffect } from 'react';\nimport {\n  Box,\n  Card,\n  CardContent,\n  Typo<PERSON>,\n  Button,\n  Dialog,\n  DialogTitle,\n  DialogContent,\n  DialogActions,\n  TextField,\n  MenuItem,\n  Alert,\n  CircularProgress,\n  Stepper,\n  Step,\n  StepLabel,\n  Table,\n  TableBody,\n  TableCell,\n  TableContainer,\n  TableHead,\n  TableRow,\n  Paper,\n  Checkbox,\n  Chip,\n  Grid,\n  Autocomplete\n} from '@mui/material';\nimport {\n  Add as AddIcon,\n  Cable as CableIcon,\n  Assignment as AssignmentIcon\n} from '@mui/icons-material';\nimport comandeService from '../../services/comandeService';\nimport responsabiliService from '../../services/responsabiliService';\n\nconst CreaComandaConCavi = ({\n  cantiereId,\n  open,\n  onClose,\n  onSuccess,\n  tipoComandaPreselezionato = null,\n  caviPreselezionati = []\n}) => {\n  const [activeStep, setActiveStep] = useState(0);\n  const [loading, setLoading] = useState(false);\n  const [error, setError] = useState(null);\n  \n  // Step 1: Selezione tipo comanda\n  const [tipoComanda, setTipoComanda] = useState(tipoComandaPreselezionato || 'POSA');\n\n  // Aggiorna il tipo comanda quando cambia il prop\n  useEffect(() => {\n    if (tipoComandaPreselezionato) {\n      setTipoComanda(tipoComandaPreselezionato);\n    }\n  }, [tipoComandaPreselezionato]);\n  \n  // Step 2: Selezione cavi\n  const [caviDisponibili, setCaviDisponibili] = useState([]);\n  const [caviSelezionati, setCaviSelezionati] = useState(caviPreselezionati || []);\n  const [loadingCavi, setLoadingCavi] = useState(false);\n\n  // Step 3: Dettagli comanda\n  const [formData, setFormData] = useState({\n    responsabile: '',\n    responsabile_email: '',\n    responsabile_telefono: '',\n    data_scadenza: '',\n    numero_componenti_squadra: 1,\n    note_capo_cantiere: ''\n  });\n\n  // Gestione responsabili\n  const [responsabiliDisponibili, setResponsabiliDisponibili] = useState([]);\n  const [loadingResponsabili, setLoadingResponsabili] = useState(false);\n\n  const steps = ['Tipo Comanda', 'Selezione Cavi', 'Dettagli Comanda'];\n\n  // Carica cavi disponibili quando cambia il tipo comanda\n  useEffect(() => {\n    if (open && tipoComanda) {\n      loadCaviDisponibili();\n    }\n  }, [open, tipoComanda]);\n\n  // Carica responsabili disponibili quando si apre il dialog\n  useEffect(() => {\n    if (open && cantiereId) {\n      loadResponsabiliDisponibili();\n    }\n  }, [open, cantiereId]);\n\n  // Se ci sono cavi preselezionati, salta al passo dei dettagli\n  useEffect(() => {\n    if (open && caviPreselezionati.length > 0 && tipoComandaPreselezionato) {\n      setActiveStep(2); // Vai direttamente ai dettagli\n      setCaviSelezionati(caviPreselezionati); // Imposta i cavi preselezionati\n    }\n  }, [open, caviPreselezionati, tipoComandaPreselezionato]);\n\n  const loadCaviDisponibili = async () => {\n    try {\n      setLoadingCavi(true);\n      const response = await comandeService.getCaviDisponibili(cantiereId, tipoComanda);\n      setCaviDisponibili(response.cavi_disponibili || []);\n\n      // Non resettare la selezione se ci sono cavi preselezionati\n      if (caviPreselezionati.length === 0) {\n        setCaviSelezionati([]);\n      }\n\n      setError(null);\n    } catch (err) {\n      console.error('Errore nel caricamento dei cavi:', err);\n      setError('Errore nel caricamento dei cavi disponibili');\n    } finally {\n      setLoadingCavi(false);\n    }\n  };\n\n  const loadResponsabiliDisponibili = async () => {\n    try {\n      setLoadingResponsabili(true);\n      const responsabili = await responsabiliService.getResponsabiliCantiere(cantiereId);\n      setResponsabiliDisponibili(responsabili || []);\n    } catch (err) {\n      console.error('Errore nel caricamento dei responsabili:', err);\n      // Non mostrare errore per i responsabili, è opzionale\n    } finally {\n      setLoadingResponsabili(false);\n    }\n  };\n\n  const handleNext = () => {\n    if (activeStep === 1 && caviSelezionati.length === 0) {\n      setError('Seleziona almeno un cavo');\n      return;\n    }\n    if (activeStep === 2) {\n      if (!formData.responsabile.trim()) {\n        setError('Seleziona un responsabile dall\\'elenco');\n        return;\n      }\n    }\n    setError(null);\n    setActiveStep((prevStep) => prevStep + 1);\n  };\n\n  const handleBack = () => {\n    setActiveStep((prevStep) => prevStep - 1);\n  };\n\n  const handleCavoToggle = (cavo) => {\n    const isSelected = caviSelezionati.some(c => c.id_cavo === cavo.id_cavo);\n    if (isSelected) {\n      setCaviSelezionati(caviSelezionati.filter(c => c.id_cavo !== cavo.id_cavo));\n    } else {\n      setCaviSelezionati([...caviSelezionati, cavo]);\n    }\n  };\n\n  const handleSubmit = async () => {\n    try {\n      setLoading(true);\n      setError(null);\n\n      console.log('🚀 Inizio creazione comanda:', {\n        tipoComanda,\n        responsabile: formData.responsabile,\n        numeroCavi: caviSelezionati.length\n      });\n\n      const comandaData = {\n        tipo_comanda: tipoComanda,\n        descrizione: `Comanda ${getTipoComandaLabel(tipoComanda)} per ${caviSelezionati.length} cavi`,\n        responsabile: formData.responsabile,\n        responsabile_email: formData.responsabile_email || null,\n        responsabile_telefono: formData.responsabile_telefono || null,\n        data_scadenza: formData.data_scadenza || null,\n        numero_componenti_squadra: formData.numero_componenti_squadra || 1,\n        note_capo_cantiere: formData.note_capo_cantiere\n      };\n\n      const listaIdCavi = caviSelezionati.map(c => c.id_cavo);\n\n      console.log('📋 Dati comanda preparati:', comandaData);\n      console.log('🔗 Cavi da assegnare:', listaIdCavi);\n\n      const response = await comandeService.createComandaConCavi(\n        cantiereId,\n        comandaData,\n        listaIdCavi\n      );\n\n      console.log('✅ Comanda creata con successo:', response);\n\n      // Mostra messaggio di successo specifico per tipo comanda\n      let successMessage = `Comanda ${getTipoComandaLabel(tipoComanda)} creata con successo!`;\n      if (tipoComanda === 'POSA') {\n        successMessage += ` I ${caviSelezionati.length} cavi sono ora \"In corso\" e assegnati a ${formData.responsabile}.`;\n      }\n\n      console.log('📢 Messaggio successo:', successMessage);\n\n      onSuccess && onSuccess(response, successMessage);\n      handleClose();\n    } catch (err) {\n      console.error('❌ Errore nella creazione comanda:', err);\n\n      let errorMessage = 'Errore nella creazione della comanda';\n      if (err.detail) {\n        errorMessage = err.detail;\n      } else if (err.message) {\n        errorMessage = err.message;\n      } else if (typeof err === 'string') {\n        errorMessage = err;\n      }\n\n      console.error('📢 Messaggio errore:', errorMessage);\n      setError(errorMessage);\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  const handleClose = () => {\n    // Reset al passo iniziale solo se non ci sono cavi preselezionati\n    if (caviPreselezionati.length > 0 && tipoComandaPreselezionato) {\n      setActiveStep(2); // Torna ai dettagli se ci sono cavi preselezionati\n    } else {\n      setActiveStep(0); // Altrimenti torna al primo passo\n    }\n\n    setTipoComanda(tipoComandaPreselezionato || 'POSA');\n    setCaviDisponibili([]);\n    setCaviSelezionati(caviPreselezionati || []);\n    setFormData({\n      responsabile: '',\n      responsabile_email: '',\n      responsabile_telefono: '',\n      data_scadenza: '',\n      numero_componenti_squadra: 1,\n      note_capo_cantiere: ''\n    });\n    setError(null);\n    onClose && onClose();\n  };\n\n  const getTipoComandaLabel = (tipo) => {\n    switch (tipo) {\n      case 'POSA': return 'Posa';\n      case 'COLLEGAMENTO_PARTENZA': return 'Collegamento Partenza';\n      case 'COLLEGAMENTO_ARRIVO': return 'Collegamento Arrivo';\n      case 'CERTIFICAZIONE': return 'Certificazione';\n      default: return tipo;\n    }\n  };\n\n  const renderStepContent = () => {\n    switch (activeStep) {\n      case 0:\n        return (\n          <Box>\n            <Typography variant=\"h6\" gutterBottom>\n              Seleziona il tipo di comanda\n            </Typography>\n            <TextField\n              fullWidth\n              select\n              label=\"Tipo Comanda\"\n              value={tipoComanda}\n              onChange={(e) => setTipoComanda(e.target.value)}\n              margin=\"normal\"\n            >\n              <MenuItem value=\"POSA\">Posa</MenuItem>\n              <MenuItem value=\"COLLEGAMENTO_PARTENZA\">Collegamento Partenza</MenuItem>\n              <MenuItem value=\"COLLEGAMENTO_ARRIVO\">Collegamento Arrivo</MenuItem>\n              <MenuItem value=\"CERTIFICAZIONE\">Certificazione</MenuItem>\n            </TextField>\n            <Alert severity=\"info\" sx={{ mt: 2 }}>\n              <strong>{getTipoComandaLabel(tipoComanda)}</strong>: \n              {tipoComanda === 'POSA' && ' Comanda per la posa fisica dei cavi'}\n              {tipoComanda === 'COLLEGAMENTO_PARTENZA' && ' Comanda per il collegamento lato partenza'}\n              {tipoComanda === 'COLLEGAMENTO_ARRIVO' && ' Comanda per il collegamento lato arrivo'}\n              {tipoComanda === 'CERTIFICAZIONE' && ' Comanda per la certificazione e test dei cavi'}\n            </Alert>\n          </Box>\n        );\n\n      case 1:\n        return (\n          <Box>\n            <Typography variant=\"h6\" gutterBottom>\n              Seleziona i cavi per la comanda di {getTipoComandaLabel(tipoComanda)}\n            </Typography>\n            \n            {loadingCavi ? (\n              <Box display=\"flex\" justifyContent=\"center\" p={3}>\n                <CircularProgress />\n              </Box>\n            ) : (\n              <>\n                <Box mb={2}>\n                  <Chip \n                    icon={<CableIcon />}\n                    label={`${caviSelezionati.length} cavi selezionati di ${caviDisponibili.length} disponibili`}\n                    color={caviSelezionati.length > 0 ? 'primary' : 'default'}\n                  />\n                </Box>\n                \n                <TableContainer component={Paper} sx={{ maxHeight: 400 }}>\n                  <Table stickyHeader>\n                    <TableHead>\n                      <TableRow>\n                        <TableCell padding=\"checkbox\">Sel.</TableCell>\n                        <TableCell>ID Cavo</TableCell>\n                        <TableCell>Tipologia</TableCell>\n                        <TableCell>Sezione</TableCell>\n                        <TableCell>Metri</TableCell>\n                        <TableCell>Partenza</TableCell>\n                        <TableCell>Arrivo</TableCell>\n                      </TableRow>\n                    </TableHead>\n                    <TableBody>\n                      {caviDisponibili.map((cavo) => {\n                        const isSelected = caviSelezionati.some(c => c.id_cavo === cavo.id_cavo);\n                        return (\n                          <TableRow \n                            key={cavo.id_cavo}\n                            hover\n                            onClick={() => handleCavoToggle(cavo)}\n                            sx={{ cursor: 'pointer' }}\n                          >\n                            <TableCell padding=\"checkbox\">\n                              <Checkbox checked={isSelected} />\n                            </TableCell>\n                            <TableCell>{cavo.id_cavo}</TableCell>\n                            <TableCell>{cavo.tipologia}</TableCell>\n                            <TableCell>{cavo.sezione}</TableCell>\n                            <TableCell>{cavo.metri_teorici}</TableCell>\n                            <TableCell>{cavo.ubicazione_partenza}</TableCell>\n                            <TableCell>{cavo.ubicazione_arrivo}</TableCell>\n                          </TableRow>\n                        );\n                      })}\n                    </TableBody>\n                  </Table>\n                </TableContainer>\n\n                {caviDisponibili.length === 0 && (\n                  <Alert severity=\"warning\" sx={{ mt: 2 }}>\n                    Nessun cavo disponibile per il tipo di comanda selezionato.\n                  </Alert>\n                )}\n              </>\n            )}\n          </Box>\n        );\n\n      case 2:\n        return (\n          <Box>\n            <Typography variant=\"h6\" gutterBottom>\n              Dettagli della comanda\n            </Typography>\n            \n            <Grid container spacing={3}>\n              {/* Prima riga: Responsabile e Data Scadenza */}\n              <Grid item xs={12} sm={8}>\n                <TextField\n                  fullWidth\n                  select\n                  label=\"Responsabile\"\n                  value={formData.responsabile}\n                  onChange={(e) => {\n                    const nomeResponsabile = e.target.value;\n                    const responsabile = responsabiliDisponibili.find(r => r.nome_responsabile === nomeResponsabile);\n                    if (responsabile) {\n                      setFormData({\n                        ...formData,\n                        responsabile: nomeResponsabile,\n                        responsabile_email: responsabile.email || '',\n                        responsabile_telefono: responsabile.telefono || ''\n                      });\n                    } else {\n                      setFormData({ ...formData, responsabile: nomeResponsabile });\n                    }\n                  }}\n                  required\n                  helperText=\"Chi eseguirà il lavoro (obbligatorio)\"\n                  size=\"medium\"\n                  InputProps={{\n                    endAdornment: loadingResponsabili ? <CircularProgress color=\"inherit\" size={20} /> : null\n                  }}\n                >\n                  {responsabiliDisponibili.map((responsabile) => (\n                    <MenuItem key={responsabile.id_responsabile} value={responsabile.nome_responsabile}>\n                      <Box>\n                        <Typography variant=\"body1\">\n                          {responsabile.nome_responsabile}\n                        </Typography>\n                        <Typography variant=\"body2\" color=\"textSecondary\">\n                          {responsabile.email && `📧 ${responsabile.email}`}\n                          {responsabile.email && responsabile.telefono && ' • '}\n                          {responsabile.telefono && `📞 ${responsabile.telefono}`}\n                        </Typography>\n                      </Box>\n                    </MenuItem>\n                  ))}\n                </TextField>\n              </Grid>\n\n              <Grid item xs={12} sm={4}>\n                <TextField\n                  fullWidth\n                  label=\"Data Scadenza\"\n                  type=\"date\"\n                  value={formData.data_scadenza}\n                  onChange={(e) => setFormData({ ...formData, data_scadenza: e.target.value })}\n                  InputLabelProps={{ shrink: true }}\n                  helperText=\"Scadenza prevista\"\n                  size=\"medium\"\n                />\n              </Grid>\n\n              {/* Seconda riga: Numero Componenti Squadra */}\n              <Grid item xs={12} sm={6}>\n                <TextField\n                  fullWidth\n                  label=\"Numero Componenti Squadra\"\n                  type=\"number\"\n                  value={formData.numero_componenti_squadra}\n                  onChange={(e) => setFormData({\n                    ...formData,\n                    numero_componenti_squadra: Math.max(1, parseInt(e.target.value) || 1)\n                  })}\n                  inputProps={{ min: 1, max: 20 }}\n                  helperText=\"Persone previste per il lavoro (per calcolo resa oraria)\"\n                  size=\"medium\"\n                />\n              </Grid>\n            </Grid>\n\n            {/* Terza riga: Note per il Responsabile */}\n            <Grid container spacing={3} sx={{ mt: 1 }}>\n              <Grid item xs={12}>\n                <TextField\n                  fullWidth\n                  label=\"Note per il Responsabile\"\n                  value={formData.note_capo_cantiere}\n                  onChange={(e) => setFormData({ ...formData, note_capo_cantiere: e.target.value })}\n                  multiline\n                  rows={3}\n                  helperText=\"Istruzioni specifiche per il responsabile\"\n                  size=\"medium\"\n                  placeholder=\"Inserisci eventuali istruzioni specifiche, precauzioni o dettagli tecnici per l'esecuzione del lavoro...\"\n                />\n              </Grid>\n            </Grid>\n\n            {/* Riepilogo Comanda */}\n            {formData.responsabile && (\n              <Box sx={{ mt: 3, p: 2, bgcolor: 'grey.50', borderRadius: 1 }}>\n                <Typography variant=\"subtitle2\" gutterBottom>\n                  📋 Riepilogo Comanda:\n                </Typography>\n                <Box sx={{ display: 'flex', flexWrap: 'wrap', gap: 2, mt: 1 }}>\n                  <Typography variant=\"body2\">\n                    <strong>Tipo:</strong> {getTipoComandaLabel(tipoComanda)}\n                  </Typography>\n                  <Typography variant=\"body2\">\n                    <strong>Cavi selezionati:</strong> {caviSelezionati.length}\n                  </Typography>\n                  <Typography variant=\"body2\">\n                    <strong>Responsabile:</strong> {formData.responsabile}\n                  </Typography>\n                  {formData.numero_componenti_squadra > 1 && (\n                    <Typography variant=\"body2\">\n                      <strong>Squadra:</strong> {formData.numero_componenti_squadra} persone\n                    </Typography>\n                  )}\n                  {formData.data_scadenza && (\n                    <Typography variant=\"body2\">\n                      <strong>Scadenza:</strong> {new Date(formData.data_scadenza).toLocaleDateString('it-IT')}\n                    </Typography>\n                  )}\n                </Box>\n\n                {/* Lista cavi selezionati */}\n                {caviSelezionati.length > 0 && (\n                  <Box sx={{ mt: 2 }}>\n                    <Typography variant=\"body2\" sx={{ fontWeight: 'bold', mb: 1 }}>\n                      🔗 Cavi assegnati:\n                    </Typography>\n                    <Box sx={{\n                      display: 'flex',\n                      flexWrap: 'wrap',\n                      gap: 0.5,\n                      maxHeight: '120px',\n                      overflowY: 'auto',\n                      p: 1,\n                      bgcolor: 'background.paper',\n                      borderRadius: 1,\n                      border: '1px solid',\n                      borderColor: 'divider'\n                    }}>\n                      {caviSelezionati.map((cavo, index) => (\n                        <Box\n                          key={cavo.id_cavo}\n                          sx={{\n                            display: 'inline-flex',\n                            alignItems: 'center',\n                            bgcolor: 'primary.light',\n                            color: 'primary.contrastText',\n                            px: 1,\n                            py: 0.5,\n                            borderRadius: 1,\n                            fontSize: '0.75rem',\n                            fontWeight: 'medium'\n                          }}\n                        >\n                          {cavo.id_cavo}\n                          {cavo.tipologia && (\n                            <Typography variant=\"caption\" sx={{ ml: 0.5, opacity: 0.8 }}>\n                              ({cavo.tipologia})\n                            </Typography>\n                          )}\n                        </Box>\n                      ))}\n                    </Box>\n\n                    {/* Informazioni aggiuntive sui cavi */}\n                    {(() => {\n                      const metriTotali = caviSelezionati.reduce((sum, cavo) => sum + (cavo.metri_teorici || 0), 0);\n                      const tipologie = [...new Set(caviSelezionati.map(c => c.tipologia).filter(Boolean))];\n\n                      return (\n                        <Box sx={{ mt: 1, display: 'flex', flexWrap: 'wrap', gap: 2 }}>\n                          {metriTotali > 0 && (\n                            <Typography variant=\"caption\" color=\"text.secondary\">\n                              <strong>Metri totali:</strong> {metriTotali.toFixed(0)}m\n                            </Typography>\n                          )}\n                          {tipologie.length > 0 && (\n                            <Typography variant=\"caption\" color=\"text.secondary\">\n                              <strong>Tipologie:</strong> {tipologie.join(', ')}\n                            </Typography>\n                          )}\n                        </Box>\n                      );\n                    })()}\n                  </Box>\n                )}\n\n                {/* Calcolo ore previste e resa stimata */}\n                {formData.data_scadenza && formData.numero_componenti_squadra && (\n                  <Box sx={{ mt: 2, p: 1.5, bgcolor: 'info.light', borderRadius: 1, color: 'info.contrastText' }}>\n                    <Typography variant=\"caption\" sx={{ fontWeight: 'bold' }}>\n                      💡 Stima Lavoro:\n                    </Typography>\n                    <Box sx={{ display: 'flex', flexWrap: 'wrap', gap: 2, mt: 0.5 }}>\n                      {(() => {\n                        const oggi = new Date();\n                        const scadenza = new Date(formData.data_scadenza);\n                        const giorniDisponibili = Math.max(1, Math.ceil((scadenza - oggi) / (1000 * 60 * 60 * 24)));\n                        const oreGiornaliere = 8; // Ore lavorative standard\n                        const oreTotaliDisponibili = giorniDisponibili * oreGiornaliere * formData.numero_componenti_squadra;\n\n                        // Stima metri totali (approssimativa)\n                        const metriTotali = caviSelezionati.reduce((sum, cavo) => sum + (cavo.metri_teorici || 0), 0);\n\n                        // Resa stimata in base al tipo di comanda\n                        let resaStimata = 0;\n                        let unitaMisura = '';\n\n                        if (tipoComanda === 'POSA') {\n                          resaStimata = metriTotali > 0 ? (oreTotaliDisponibili / metriTotali * formData.numero_componenti_squadra).toFixed(1) : 0;\n                          unitaMisura = 'm/h per persona';\n                        } else if (tipoComanda.includes('COLLEGAMENTO')) {\n                          resaStimata = caviSelezionati.length > 0 ? (oreTotaliDisponibili / caviSelezionati.length).toFixed(1) : 0;\n                          unitaMisura = 'h per collegamento';\n                        } else if (tipoComanda === 'CERTIFICAZIONE') {\n                          resaStimata = caviSelezionati.length > 0 ? (oreTotaliDisponibili / caviSelezionati.length).toFixed(1) : 0;\n                          unitaMisura = 'h per test';\n                        }\n\n                        return (\n                          <>\n                            <Typography variant=\"caption\">\n                              <strong>Giorni disponibili:</strong> {giorniDisponibili}\n                            </Typography>\n                            <Typography variant=\"caption\">\n                              <strong>Ore totali squadra:</strong> {oreTotaliDisponibili}h\n                            </Typography>\n                            {tipoComanda === 'POSA' && metriTotali > 0 && (\n                              <Typography variant=\"caption\">\n                                <strong>Metri totali:</strong> {metriTotali.toFixed(0)}m\n                              </Typography>\n                            )}\n                            {resaStimata > 0 && (\n                              <Typography variant=\"caption\">\n                                <strong>Resa richiesta:</strong> {resaStimata} {unitaMisura}\n                              </Typography>\n                            )}\n                          </>\n                        );\n                      })()}\n                    </Box>\n                  </Box>\n                )}\n              </Box>\n            )}\n          </Box>\n        );\n\n      default:\n        return null;\n    }\n  };\n\n  return (\n    <Dialog open={open} onClose={handleClose} maxWidth=\"lg\" fullWidth>\n      <DialogTitle>\n        <Box display=\"flex\" alignItems=\"center\" gap={1}>\n          <AssignmentIcon />\n          Crea Nuova Comanda con Cavi\n        </Box>\n      </DialogTitle>\n      \n      <DialogContent>\n        <Box sx={{ pt: 2 }}>\n          <Stepper activeStep={activeStep} sx={{ mb: 3 }}>\n            {steps.map((label) => (\n              <Step key={label}>\n                <StepLabel>{label}</StepLabel>\n              </Step>\n            ))}\n          </Stepper>\n\n          {error && (\n            <Alert severity=\"error\" sx={{ mb: 2 }}>\n              {error}\n            </Alert>\n          )}\n\n          {renderStepContent()}\n        </Box>\n      </DialogContent>\n\n      <DialogActions sx={{ p: 3, gap: 2 }}>\n        {activeStep < steps.length - 1 ? (\n          <>\n            <Button\n              onClick={handleClose}\n              variant=\"outlined\"\n              sx={{ minWidth: 120 }}\n            >\n              Chiudi\n            </Button>\n            <Button\n              onClick={handleNext}\n              variant=\"contained\"\n              disabled={activeStep === 1 && loadingCavi}\n              sx={{ minWidth: 120 }}\n            >\n              Avanti\n            </Button>\n          </>\n        ) : (\n          <>\n            <Button\n              onClick={handleClose}\n              variant=\"outlined\"\n              sx={{ minWidth: 120 }}\n            >\n              Chiudi\n            </Button>\n            <Button\n              onClick={handleSubmit}\n              variant=\"contained\"\n              disabled={\n                loading ||\n                caviSelezionati.length === 0 ||\n                !formData.responsabile.trim()\n              }\n              startIcon={loading ? <CircularProgress size={20} /> : <AddIcon />}\n              sx={{ minWidth: 120 }}\n            >\n              {loading ? 'Creazione...' : 'Crea Comanda'}\n            </Button>\n          </>\n        )}\n      </DialogActions>\n    </Dialog>\n  );\n};\n\nexport default CreaComandaConCavi;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAClD,SACEC,GAAG,EACHC,IAAI,EACJC,WAAW,EACXC,UAAU,EACVC,MAAM,EACNC,MAAM,EACNC,WAAW,EACXC,aAAa,EACbC,aAAa,EACbC,SAAS,EACTC,QAAQ,EACRC,KAAK,EACLC,gBAAgB,EAChBC,OAAO,EACPC,IAAI,EACJC,SAAS,EACTC,KAAK,EACLC,SAAS,EACTC,SAAS,EACTC,cAAc,EACdC,SAAS,EACTC,QAAQ,EACRC,KAAK,EACLC,QAAQ,EACRC,IAAI,EACJC,IAAI,EACJC,YAAY,QACP,eAAe;AACtB,SACEC,GAAG,IAAIC,OAAO,EACdC,KAAK,IAAIC,SAAS,EAClBC,UAAU,IAAIC,cAAc,QACvB,qBAAqB;AAC5B,OAAOC,cAAc,MAAM,+BAA+B;AAC1D,OAAOC,mBAAmB,MAAM,oCAAoC;AAAC,SAAAC,MAAA,IAAAC,OAAA,EAAAC,QAAA,IAAAC,SAAA;AAErE,MAAMC,kBAAkB,GAAGA,CAAC;EAC1BC,UAAU;EACVC,IAAI;EACJC,OAAO;EACPC,SAAS;EACTC,yBAAyB,GAAG,IAAI;EAChCC,kBAAkB,GAAG;AACvB,CAAC,KAAK;EAAAC,EAAA;EACJ,MAAM,CAACC,UAAU,EAAEC,aAAa,CAAC,GAAGlD,QAAQ,CAAC,CAAC,CAAC;EAC/C,MAAM,CAACmD,OAAO,EAAEC,UAAU,CAAC,GAAGpD,QAAQ,CAAC,KAAK,CAAC;EAC7C,MAAM,CAACqD,KAAK,EAAEC,QAAQ,CAAC,GAAGtD,QAAQ,CAAC,IAAI,CAAC;;EAExC;EACA,MAAM,CAACuD,WAAW,EAAEC,cAAc,CAAC,GAAGxD,QAAQ,CAAC8C,yBAAyB,IAAI,MAAM,CAAC;;EAEnF;EACA7C,SAAS,CAAC,MAAM;IACd,IAAI6C,yBAAyB,EAAE;MAC7BU,cAAc,CAACV,yBAAyB,CAAC;IAC3C;EACF,CAAC,EAAE,CAACA,yBAAyB,CAAC,CAAC;;EAE/B;EACA,MAAM,CAACW,eAAe,EAAEC,kBAAkB,CAAC,GAAG1D,QAAQ,CAAC,EAAE,CAAC;EAC1D,MAAM,CAAC2D,eAAe,EAAEC,kBAAkB,CAAC,GAAG5D,QAAQ,CAAC+C,kBAAkB,IAAI,EAAE,CAAC;EAChF,MAAM,CAACc,WAAW,EAAEC,cAAc,CAAC,GAAG9D,QAAQ,CAAC,KAAK,CAAC;;EAErD;EACA,MAAM,CAAC+D,QAAQ,EAAEC,WAAW,CAAC,GAAGhE,QAAQ,CAAC;IACvCiE,YAAY,EAAE,EAAE;IAChBC,kBAAkB,EAAE,EAAE;IACtBC,qBAAqB,EAAE,EAAE;IACzBC,aAAa,EAAE,EAAE;IACjBC,yBAAyB,EAAE,CAAC;IAC5BC,kBAAkB,EAAE;EACtB,CAAC,CAAC;;EAEF;EACA,MAAM,CAACC,uBAAuB,EAAEC,0BAA0B,CAAC,GAAGxE,QAAQ,CAAC,EAAE,CAAC;EAC1E,MAAM,CAACyE,mBAAmB,EAAEC,sBAAsB,CAAC,GAAG1E,QAAQ,CAAC,KAAK,CAAC;EAErE,MAAM2E,KAAK,GAAG,CAAC,cAAc,EAAE,gBAAgB,EAAE,kBAAkB,CAAC;;EAEpE;EACA1E,SAAS,CAAC,MAAM;IACd,IAAI0C,IAAI,IAAIY,WAAW,EAAE;MACvBqB,mBAAmB,CAAC,CAAC;IACvB;EACF,CAAC,EAAE,CAACjC,IAAI,EAAEY,WAAW,CAAC,CAAC;;EAEvB;EACAtD,SAAS,CAAC,MAAM;IACd,IAAI0C,IAAI,IAAID,UAAU,EAAE;MACtBmC,2BAA2B,CAAC,CAAC;IAC/B;EACF,CAAC,EAAE,CAAClC,IAAI,EAAED,UAAU,CAAC,CAAC;;EAEtB;EACAzC,SAAS,CAAC,MAAM;IACd,IAAI0C,IAAI,IAAII,kBAAkB,CAAC+B,MAAM,GAAG,CAAC,IAAIhC,yBAAyB,EAAE;MACtEI,aAAa,CAAC,CAAC,CAAC,CAAC,CAAC;MAClBU,kBAAkB,CAACb,kBAAkB,CAAC,CAAC,CAAC;IAC1C;EACF,CAAC,EAAE,CAACJ,IAAI,EAAEI,kBAAkB,EAAED,yBAAyB,CAAC,CAAC;EAEzD,MAAM8B,mBAAmB,GAAG,MAAAA,CAAA,KAAY;IACtC,IAAI;MACFd,cAAc,CAAC,IAAI,CAAC;MACpB,MAAMiB,QAAQ,GAAG,MAAM5C,cAAc,CAAC6C,kBAAkB,CAACtC,UAAU,EAAEa,WAAW,CAAC;MACjFG,kBAAkB,CAACqB,QAAQ,CAACE,gBAAgB,IAAI,EAAE,CAAC;;MAEnD;MACA,IAAIlC,kBAAkB,CAAC+B,MAAM,KAAK,CAAC,EAAE;QACnClB,kBAAkB,CAAC,EAAE,CAAC;MACxB;MAEAN,QAAQ,CAAC,IAAI,CAAC;IAChB,CAAC,CAAC,OAAO4B,GAAG,EAAE;MACZC,OAAO,CAAC9B,KAAK,CAAC,kCAAkC,EAAE6B,GAAG,CAAC;MACtD5B,QAAQ,CAAC,6CAA6C,CAAC;IACzD,CAAC,SAAS;MACRQ,cAAc,CAAC,KAAK,CAAC;IACvB;EACF,CAAC;EAED,MAAMe,2BAA2B,GAAG,MAAAA,CAAA,KAAY;IAC9C,IAAI;MACFH,sBAAsB,CAAC,IAAI,CAAC;MAC5B,MAAMU,YAAY,GAAG,MAAMhD,mBAAmB,CAACiD,uBAAuB,CAAC3C,UAAU,CAAC;MAClF8B,0BAA0B,CAACY,YAAY,IAAI,EAAE,CAAC;IAChD,CAAC,CAAC,OAAOF,GAAG,EAAE;MACZC,OAAO,CAAC9B,KAAK,CAAC,0CAA0C,EAAE6B,GAAG,CAAC;MAC9D;IACF,CAAC,SAAS;MACRR,sBAAsB,CAAC,KAAK,CAAC;IAC/B;EACF,CAAC;EAED,MAAMY,UAAU,GAAGA,CAAA,KAAM;IACvB,IAAIrC,UAAU,KAAK,CAAC,IAAIU,eAAe,CAACmB,MAAM,KAAK,CAAC,EAAE;MACpDxB,QAAQ,CAAC,0BAA0B,CAAC;MACpC;IACF;IACA,IAAIL,UAAU,KAAK,CAAC,EAAE;MACpB,IAAI,CAACc,QAAQ,CAACE,YAAY,CAACsB,IAAI,CAAC,CAAC,EAAE;QACjCjC,QAAQ,CAAC,wCAAwC,CAAC;QAClD;MACF;IACF;IACAA,QAAQ,CAAC,IAAI,CAAC;IACdJ,aAAa,CAAEsC,QAAQ,IAAKA,QAAQ,GAAG,CAAC,CAAC;EAC3C,CAAC;EAED,MAAMC,UAAU,GAAGA,CAAA,KAAM;IACvBvC,aAAa,CAAEsC,QAAQ,IAAKA,QAAQ,GAAG,CAAC,CAAC;EAC3C,CAAC;EAED,MAAME,gBAAgB,GAAIC,IAAI,IAAK;IACjC,MAAMC,UAAU,GAAGjC,eAAe,CAACkC,IAAI,CAACC,CAAC,IAAIA,CAAC,CAACC,OAAO,KAAKJ,IAAI,CAACI,OAAO,CAAC;IACxE,IAAIH,UAAU,EAAE;MACdhC,kBAAkB,CAACD,eAAe,CAACqC,MAAM,CAACF,CAAC,IAAIA,CAAC,CAACC,OAAO,KAAKJ,IAAI,CAACI,OAAO,CAAC,CAAC;IAC7E,CAAC,MAAM;MACLnC,kBAAkB,CAAC,CAAC,GAAGD,eAAe,EAAEgC,IAAI,CAAC,CAAC;IAChD;EACF,CAAC;EAED,MAAMM,YAAY,GAAG,MAAAA,CAAA,KAAY;IAC/B,IAAI;MACF7C,UAAU,CAAC,IAAI,CAAC;MAChBE,QAAQ,CAAC,IAAI,CAAC;MAEd6B,OAAO,CAACe,GAAG,CAAC,8BAA8B,EAAE;QAC1C3C,WAAW;QACXU,YAAY,EAAEF,QAAQ,CAACE,YAAY;QACnCkC,UAAU,EAAExC,eAAe,CAACmB;MAC9B,CAAC,CAAC;MAEF,MAAMsB,WAAW,GAAG;QAClBC,YAAY,EAAE9C,WAAW;QACzB+C,WAAW,EAAE,WAAWC,mBAAmB,CAAChD,WAAW,CAAC,QAAQI,eAAe,CAACmB,MAAM,OAAO;QAC7Fb,YAAY,EAAEF,QAAQ,CAACE,YAAY;QACnCC,kBAAkB,EAAEH,QAAQ,CAACG,kBAAkB,IAAI,IAAI;QACvDC,qBAAqB,EAAEJ,QAAQ,CAACI,qBAAqB,IAAI,IAAI;QAC7DC,aAAa,EAAEL,QAAQ,CAACK,aAAa,IAAI,IAAI;QAC7CC,yBAAyB,EAAEN,QAAQ,CAACM,yBAAyB,IAAI,CAAC;QAClEC,kBAAkB,EAAEP,QAAQ,CAACO;MAC/B,CAAC;MAED,MAAMkC,WAAW,GAAG7C,eAAe,CAAC8C,GAAG,CAACX,CAAC,IAAIA,CAAC,CAACC,OAAO,CAAC;MAEvDZ,OAAO,CAACe,GAAG,CAAC,4BAA4B,EAAEE,WAAW,CAAC;MACtDjB,OAAO,CAACe,GAAG,CAAC,uBAAuB,EAAEM,WAAW,CAAC;MAEjD,MAAMzB,QAAQ,GAAG,MAAM5C,cAAc,CAACuE,oBAAoB,CACxDhE,UAAU,EACV0D,WAAW,EACXI,WACF,CAAC;MAEDrB,OAAO,CAACe,GAAG,CAAC,gCAAgC,EAAEnB,QAAQ,CAAC;;MAEvD;MACA,IAAI4B,cAAc,GAAG,WAAWJ,mBAAmB,CAAChD,WAAW,CAAC,uBAAuB;MACvF,IAAIA,WAAW,KAAK,MAAM,EAAE;QAC1BoD,cAAc,IAAI,MAAMhD,eAAe,CAACmB,MAAM,2CAA2Cf,QAAQ,CAACE,YAAY,GAAG;MACnH;MAEAkB,OAAO,CAACe,GAAG,CAAC,wBAAwB,EAAES,cAAc,CAAC;MAErD9D,SAAS,IAAIA,SAAS,CAACkC,QAAQ,EAAE4B,cAAc,CAAC;MAChDC,WAAW,CAAC,CAAC;IACf,CAAC,CAAC,OAAO1B,GAAG,EAAE;MACZC,OAAO,CAAC9B,KAAK,CAAC,mCAAmC,EAAE6B,GAAG,CAAC;MAEvD,IAAI2B,YAAY,GAAG,sCAAsC;MACzD,IAAI3B,GAAG,CAAC4B,MAAM,EAAE;QACdD,YAAY,GAAG3B,GAAG,CAAC4B,MAAM;MAC3B,CAAC,MAAM,IAAI5B,GAAG,CAAC6B,OAAO,EAAE;QACtBF,YAAY,GAAG3B,GAAG,CAAC6B,OAAO;MAC5B,CAAC,MAAM,IAAI,OAAO7B,GAAG,KAAK,QAAQ,EAAE;QAClC2B,YAAY,GAAG3B,GAAG;MACpB;MAEAC,OAAO,CAAC9B,KAAK,CAAC,sBAAsB,EAAEwD,YAAY,CAAC;MACnDvD,QAAQ,CAACuD,YAAY,CAAC;IACxB,CAAC,SAAS;MACRzD,UAAU,CAAC,KAAK,CAAC;IACnB;EACF,CAAC;EAED,MAAMwD,WAAW,GAAGA,CAAA,KAAM;IACxB;IACA,IAAI7D,kBAAkB,CAAC+B,MAAM,GAAG,CAAC,IAAIhC,yBAAyB,EAAE;MAC9DI,aAAa,CAAC,CAAC,CAAC,CAAC,CAAC;IACpB,CAAC,MAAM;MACLA,aAAa,CAAC,CAAC,CAAC,CAAC,CAAC;IACpB;IAEAM,cAAc,CAACV,yBAAyB,IAAI,MAAM,CAAC;IACnDY,kBAAkB,CAAC,EAAE,CAAC;IACtBE,kBAAkB,CAACb,kBAAkB,IAAI,EAAE,CAAC;IAC5CiB,WAAW,CAAC;MACVC,YAAY,EAAE,EAAE;MAChBC,kBAAkB,EAAE,EAAE;MACtBC,qBAAqB,EAAE,EAAE;MACzBC,aAAa,EAAE,EAAE;MACjBC,yBAAyB,EAAE,CAAC;MAC5BC,kBAAkB,EAAE;IACtB,CAAC,CAAC;IACFhB,QAAQ,CAAC,IAAI,CAAC;IACdV,OAAO,IAAIA,OAAO,CAAC,CAAC;EACtB,CAAC;EAED,MAAM2D,mBAAmB,GAAIS,IAAI,IAAK;IACpC,QAAQA,IAAI;MACV,KAAK,MAAM;QAAE,OAAO,MAAM;MAC1B,KAAK,uBAAuB;QAAE,OAAO,uBAAuB;MAC5D,KAAK,qBAAqB;QAAE,OAAO,qBAAqB;MACxD,KAAK,gBAAgB;QAAE,OAAO,gBAAgB;MAC9C;QAAS,OAAOA,IAAI;IACtB;EACF,CAAC;EAED,MAAMC,iBAAiB,GAAGA,CAAA,KAAM;IAC9B,QAAQhE,UAAU;MAChB,KAAK,CAAC;QACJ,oBACEX,OAAA,CAACpC,GAAG;UAAAgH,QAAA,gBACF5E,OAAA,CAACjC,UAAU;YAAC8G,OAAO,EAAC,IAAI;YAACC,YAAY;YAAAF,QAAA,EAAC;UAEtC;YAAAG,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAY,CAAC,eACblF,OAAA,CAAC3B,SAAS;YACR8G,SAAS;YACTC,MAAM;YACNC,KAAK,EAAC,cAAc;YACpBC,KAAK,EAAErE,WAAY;YACnBsE,QAAQ,EAAGC,CAAC,IAAKtE,cAAc,CAACsE,CAAC,CAACC,MAAM,CAACH,KAAK,CAAE;YAChDI,MAAM,EAAC,QAAQ;YAAAd,QAAA,gBAEf5E,OAAA,CAAC1B,QAAQ;cAACgH,KAAK,EAAC,MAAM;cAAAV,QAAA,EAAC;YAAI;cAAAG,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAU,CAAC,eACtClF,OAAA,CAAC1B,QAAQ;cAACgH,KAAK,EAAC,uBAAuB;cAAAV,QAAA,EAAC;YAAqB;cAAAG,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAU,CAAC,eACxElF,OAAA,CAAC1B,QAAQ;cAACgH,KAAK,EAAC,qBAAqB;cAAAV,QAAA,EAAC;YAAmB;cAAAG,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAU,CAAC,eACpElF,OAAA,CAAC1B,QAAQ;cAACgH,KAAK,EAAC,gBAAgB;cAAAV,QAAA,EAAC;YAAc;cAAAG,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAU,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACjD,CAAC,eACZlF,OAAA,CAACzB,KAAK;YAACoH,QAAQ,EAAC,MAAM;YAACC,EAAE,EAAE;cAAEC,EAAE,EAAE;YAAE,CAAE;YAAAjB,QAAA,gBACnC5E,OAAA;cAAA4E,QAAA,EAASX,mBAAmB,CAAChD,WAAW;YAAC;cAAA8D,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAS,CAAC,KACnD,EAACjE,WAAW,KAAK,MAAM,IAAI,sCAAsC,EAChEA,WAAW,KAAK,uBAAuB,IAAI,4CAA4C,EACvFA,WAAW,KAAK,qBAAqB,IAAI,0CAA0C,EACnFA,WAAW,KAAK,gBAAgB,IAAI,gDAAgD;UAAA;YAAA8D,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAChF,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACL,CAAC;MAGV,KAAK,CAAC;QACJ,oBACElF,OAAA,CAACpC,GAAG;UAAAgH,QAAA,gBACF5E,OAAA,CAACjC,UAAU;YAAC8G,OAAO,EAAC,IAAI;YAACC,YAAY;YAAAF,QAAA,GAAC,qCACD,EAACX,mBAAmB,CAAChD,WAAW,CAAC;UAAA;YAAA8D,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC1D,CAAC,EAEZ3D,WAAW,gBACVvB,OAAA,CAACpC,GAAG;YAACkI,OAAO,EAAC,MAAM;YAACC,cAAc,EAAC,QAAQ;YAACC,CAAC,EAAE,CAAE;YAAApB,QAAA,eAC/C5E,OAAA,CAACxB,gBAAgB;cAAAuG,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACjB,CAAC,gBAENlF,OAAA,CAAAE,SAAA;YAAA0E,QAAA,gBACE5E,OAAA,CAACpC,GAAG;cAACqI,EAAE,EAAE,CAAE;cAAArB,QAAA,eACT5E,OAAA,CAACZ,IAAI;gBACH8G,IAAI,eAAElG,OAAA,CAACN,SAAS;kBAAAqF,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAE;gBACpBG,KAAK,EAAE,GAAGhE,eAAe,CAACmB,MAAM,wBAAwBrB,eAAe,CAACqB,MAAM,cAAe;gBAC7F2D,KAAK,EAAE9E,eAAe,CAACmB,MAAM,GAAG,CAAC,GAAG,SAAS,GAAG;cAAU;gBAAAuC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC3D;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACC,CAAC,eAENlF,OAAA,CAACjB,cAAc;cAACqH,SAAS,EAAElH,KAAM;cAAC0G,EAAE,EAAE;gBAAES,SAAS,EAAE;cAAI,CAAE;cAAAzB,QAAA,eACvD5E,OAAA,CAACpB,KAAK;gBAAC0H,YAAY;gBAAA1B,QAAA,gBACjB5E,OAAA,CAAChB,SAAS;kBAAA4F,QAAA,eACR5E,OAAA,CAACf,QAAQ;oBAAA2F,QAAA,gBACP5E,OAAA,CAAClB,SAAS;sBAACyH,OAAO,EAAC,UAAU;sBAAA3B,QAAA,EAAC;oBAAI;sBAAAG,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAW,CAAC,eAC9ClF,OAAA,CAAClB,SAAS;sBAAA8F,QAAA,EAAC;oBAAO;sBAAAG,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAW,CAAC,eAC9BlF,OAAA,CAAClB,SAAS;sBAAA8F,QAAA,EAAC;oBAAS;sBAAAG,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAW,CAAC,eAChClF,OAAA,CAAClB,SAAS;sBAAA8F,QAAA,EAAC;oBAAO;sBAAAG,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAW,CAAC,eAC9BlF,OAAA,CAAClB,SAAS;sBAAA8F,QAAA,EAAC;oBAAK;sBAAAG,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAW,CAAC,eAC5BlF,OAAA,CAAClB,SAAS;sBAAA8F,QAAA,EAAC;oBAAQ;sBAAAG,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAW,CAAC,eAC/BlF,OAAA,CAAClB,SAAS;sBAAA8F,QAAA,EAAC;oBAAM;sBAAAG,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAW,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACrB;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACF,CAAC,eACZlF,OAAA,CAACnB,SAAS;kBAAA+F,QAAA,EACPzD,eAAe,CAACgD,GAAG,CAAEd,IAAI,IAAK;oBAC7B,MAAMC,UAAU,GAAGjC,eAAe,CAACkC,IAAI,CAACC,CAAC,IAAIA,CAAC,CAACC,OAAO,KAAKJ,IAAI,CAACI,OAAO,CAAC;oBACxE,oBACEzD,OAAA,CAACf,QAAQ;sBAEPuH,KAAK;sBACLC,OAAO,EAAEA,CAAA,KAAMrD,gBAAgB,CAACC,IAAI,CAAE;sBACtCuC,EAAE,EAAE;wBAAEc,MAAM,EAAE;sBAAU,CAAE;sBAAA9B,QAAA,gBAE1B5E,OAAA,CAAClB,SAAS;wBAACyH,OAAO,EAAC,UAAU;wBAAA3B,QAAA,eAC3B5E,OAAA,CAACb,QAAQ;0BAACwH,OAAO,EAAErD;wBAAW;0BAAAyB,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAE;sBAAC;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACxB,CAAC,eACZlF,OAAA,CAAClB,SAAS;wBAAA8F,QAAA,EAAEvB,IAAI,CAACI;sBAAO;wBAAAsB,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAY,CAAC,eACrClF,OAAA,CAAClB,SAAS;wBAAA8F,QAAA,EAAEvB,IAAI,CAACuD;sBAAS;wBAAA7B,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAY,CAAC,eACvClF,OAAA,CAAClB,SAAS;wBAAA8F,QAAA,EAAEvB,IAAI,CAACwD;sBAAO;wBAAA9B,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAY,CAAC,eACrClF,OAAA,CAAClB,SAAS;wBAAA8F,QAAA,EAAEvB,IAAI,CAACyD;sBAAa;wBAAA/B,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAY,CAAC,eAC3ClF,OAAA,CAAClB,SAAS;wBAAA8F,QAAA,EAAEvB,IAAI,CAAC0D;sBAAmB;wBAAAhC,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAY,CAAC,eACjDlF,OAAA,CAAClB,SAAS;wBAAA8F,QAAA,EAAEvB,IAAI,CAAC2D;sBAAiB;wBAAAjC,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAY,CAAC;oBAAA,GAb1C7B,IAAI,CAACI,OAAO;sBAAAsB,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAcT,CAAC;kBAEf,CAAC;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACO,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACP;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACM,CAAC,EAEhB/D,eAAe,CAACqB,MAAM,KAAK,CAAC,iBAC3BxC,OAAA,CAACzB,KAAK;cAACoH,QAAQ,EAAC,SAAS;cAACC,EAAE,EAAE;gBAAEC,EAAE,EAAE;cAAE,CAAE;cAAAjB,QAAA,EAAC;YAEzC;cAAAG,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CACR;UAAA,eACD,CACH;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACE,CAAC;MAGV,KAAK,CAAC;QACJ,oBACElF,OAAA,CAACpC,GAAG;UAAAgH,QAAA,gBACF5E,OAAA,CAACjC,UAAU;YAAC8G,OAAO,EAAC,IAAI;YAACC,YAAY;YAAAF,QAAA,EAAC;UAEtC;YAAAG,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAY,CAAC,eAEblF,OAAA,CAACX,IAAI;YAAC4H,SAAS;YAACC,OAAO,EAAE,CAAE;YAAAtC,QAAA,gBAEzB5E,OAAA,CAACX,IAAI;cAAC8H,IAAI;cAACC,EAAE,EAAE,EAAG;cAACC,EAAE,EAAE,CAAE;cAAAzC,QAAA,eACvB5E,OAAA,CAAC3B,SAAS;gBACR8G,SAAS;gBACTC,MAAM;gBACNC,KAAK,EAAC,cAAc;gBACpBC,KAAK,EAAE7D,QAAQ,CAACE,YAAa;gBAC7B4D,QAAQ,EAAGC,CAAC,IAAK;kBACf,MAAM8B,gBAAgB,GAAG9B,CAAC,CAACC,MAAM,CAACH,KAAK;kBACvC,MAAM3D,YAAY,GAAGM,uBAAuB,CAACsF,IAAI,CAACC,CAAC,IAAIA,CAAC,CAACC,iBAAiB,KAAKH,gBAAgB,CAAC;kBAChG,IAAI3F,YAAY,EAAE;oBAChBD,WAAW,CAAC;sBACV,GAAGD,QAAQ;sBACXE,YAAY,EAAE2F,gBAAgB;sBAC9B1F,kBAAkB,EAAED,YAAY,CAAC+F,KAAK,IAAI,EAAE;sBAC5C7F,qBAAqB,EAAEF,YAAY,CAACgG,QAAQ,IAAI;oBAClD,CAAC,CAAC;kBACJ,CAAC,MAAM;oBACLjG,WAAW,CAAC;sBAAE,GAAGD,QAAQ;sBAAEE,YAAY,EAAE2F;oBAAiB,CAAC,CAAC;kBAC9D;gBACF,CAAE;gBACFM,QAAQ;gBACRC,UAAU,EAAC,0CAAuC;gBAClDC,IAAI,EAAC,QAAQ;gBACbC,UAAU,EAAE;kBACVC,YAAY,EAAE7F,mBAAmB,gBAAGnC,OAAA,CAACxB,gBAAgB;oBAAC2H,KAAK,EAAC,SAAS;oBAAC2B,IAAI,EAAE;kBAAG;oBAAA/C,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE,CAAC,GAAG;gBACvF,CAAE;gBAAAN,QAAA,EAED3C,uBAAuB,CAACkC,GAAG,CAAExC,YAAY,iBACxC3B,OAAA,CAAC1B,QAAQ;kBAAoCgH,KAAK,EAAE3D,YAAY,CAAC8F,iBAAkB;kBAAA7C,QAAA,eACjF5E,OAAA,CAACpC,GAAG;oBAAAgH,QAAA,gBACF5E,OAAA,CAACjC,UAAU;sBAAC8G,OAAO,EAAC,OAAO;sBAAAD,QAAA,EACxBjD,YAAY,CAAC8F;oBAAiB;sBAAA1C,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACrB,CAAC,eACblF,OAAA,CAACjC,UAAU;sBAAC8G,OAAO,EAAC,OAAO;sBAACsB,KAAK,EAAC,eAAe;sBAAAvB,QAAA,GAC9CjD,YAAY,CAAC+F,KAAK,IAAI,MAAM/F,YAAY,CAAC+F,KAAK,EAAE,EAChD/F,YAAY,CAAC+F,KAAK,IAAI/F,YAAY,CAACgG,QAAQ,IAAI,KAAK,EACpDhG,YAAY,CAACgG,QAAQ,IAAI,MAAMhG,YAAY,CAACgG,QAAQ,EAAE;oBAAA;sBAAA5C,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAC7C,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACV;gBAAC,GAVOvD,YAAY,CAACsG,eAAe;kBAAAlD,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAWjC,CACX;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACO;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACR,CAAC,eAEPlF,OAAA,CAACX,IAAI;cAAC8H,IAAI;cAACC,EAAE,EAAE,EAAG;cAACC,EAAE,EAAE,CAAE;cAAAzC,QAAA,eACvB5E,OAAA,CAAC3B,SAAS;gBACR8G,SAAS;gBACTE,KAAK,EAAC,eAAe;gBACrB6C,IAAI,EAAC,MAAM;gBACX5C,KAAK,EAAE7D,QAAQ,CAACK,aAAc;gBAC9ByD,QAAQ,EAAGC,CAAC,IAAK9D,WAAW,CAAC;kBAAE,GAAGD,QAAQ;kBAAEK,aAAa,EAAE0D,CAAC,CAACC,MAAM,CAACH;gBAAM,CAAC,CAAE;gBAC7E6C,eAAe,EAAE;kBAAEC,MAAM,EAAE;gBAAK,CAAE;gBAClCP,UAAU,EAAC,mBAAmB;gBAC9BC,IAAI,EAAC;cAAQ;gBAAA/C,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACd;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACE,CAAC,eAGPlF,OAAA,CAACX,IAAI;cAAC8H,IAAI;cAACC,EAAE,EAAE,EAAG;cAACC,EAAE,EAAE,CAAE;cAAAzC,QAAA,eACvB5E,OAAA,CAAC3B,SAAS;gBACR8G,SAAS;gBACTE,KAAK,EAAC,2BAA2B;gBACjC6C,IAAI,EAAC,QAAQ;gBACb5C,KAAK,EAAE7D,QAAQ,CAACM,yBAA0B;gBAC1CwD,QAAQ,EAAGC,CAAC,IAAK9D,WAAW,CAAC;kBAC3B,GAAGD,QAAQ;kBACXM,yBAAyB,EAAEsG,IAAI,CAACC,GAAG,CAAC,CAAC,EAAEC,QAAQ,CAAC/C,CAAC,CAACC,MAAM,CAACH,KAAK,CAAC,IAAI,CAAC;gBACtE,CAAC,CAAE;gBACHkD,UAAU,EAAE;kBAAEC,GAAG,EAAE,CAAC;kBAAEH,GAAG,EAAE;gBAAG,CAAE;gBAChCT,UAAU,EAAC,0DAA0D;gBACrEC,IAAI,EAAC;cAAQ;gBAAA/C,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACd;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACE,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC,eAGPlF,OAAA,CAACX,IAAI;YAAC4H,SAAS;YAACC,OAAO,EAAE,CAAE;YAACtB,EAAE,EAAE;cAAEC,EAAE,EAAE;YAAE,CAAE;YAAAjB,QAAA,eACxC5E,OAAA,CAACX,IAAI;cAAC8H,IAAI;cAACC,EAAE,EAAE,EAAG;cAAAxC,QAAA,eAChB5E,OAAA,CAAC3B,SAAS;gBACR8G,SAAS;gBACTE,KAAK,EAAC,0BAA0B;gBAChCC,KAAK,EAAE7D,QAAQ,CAACO,kBAAmB;gBACnCuD,QAAQ,EAAGC,CAAC,IAAK9D,WAAW,CAAC;kBAAE,GAAGD,QAAQ;kBAAEO,kBAAkB,EAAEwD,CAAC,CAACC,MAAM,CAACH;gBAAM,CAAC,CAAE;gBAClFoD,SAAS;gBACTC,IAAI,EAAE,CAAE;gBACRd,UAAU,EAAC,2CAA2C;gBACtDC,IAAI,EAAC,QAAQ;gBACbc,WAAW,EAAC;cAA0G;gBAAA7D,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACvH;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACE;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC,EAGNzD,QAAQ,CAACE,YAAY,iBACpB3B,OAAA,CAACpC,GAAG;YAACgI,EAAE,EAAE;cAAEC,EAAE,EAAE,CAAC;cAAEG,CAAC,EAAE,CAAC;cAAE6C,OAAO,EAAE,SAAS;cAAEC,YAAY,EAAE;YAAE,CAAE;YAAAlE,QAAA,gBAC5D5E,OAAA,CAACjC,UAAU;cAAC8G,OAAO,EAAC,WAAW;cAACC,YAAY;cAAAF,QAAA,EAAC;YAE7C;cAAAG,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAY,CAAC,eACblF,OAAA,CAACpC,GAAG;cAACgI,EAAE,EAAE;gBAAEE,OAAO,EAAE,MAAM;gBAAEiD,QAAQ,EAAE,MAAM;gBAAEC,GAAG,EAAE,CAAC;gBAAEnD,EAAE,EAAE;cAAE,CAAE;cAAAjB,QAAA,gBAC5D5E,OAAA,CAACjC,UAAU;gBAAC8G,OAAO,EAAC,OAAO;gBAAAD,QAAA,gBACzB5E,OAAA;kBAAA4E,QAAA,EAAQ;gBAAK;kBAAAG,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,KAAC,EAACjB,mBAAmB,CAAChD,WAAW,CAAC;cAAA;gBAAA8D,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC9C,CAAC,eACblF,OAAA,CAACjC,UAAU;gBAAC8G,OAAO,EAAC,OAAO;gBAAAD,QAAA,gBACzB5E,OAAA;kBAAA4E,QAAA,EAAQ;gBAAiB;kBAAAG,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,KAAC,EAAC7D,eAAe,CAACmB,MAAM;cAAA;gBAAAuC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAChD,CAAC,eACblF,OAAA,CAACjC,UAAU;gBAAC8G,OAAO,EAAC,OAAO;gBAAAD,QAAA,gBACzB5E,OAAA;kBAAA4E,QAAA,EAAQ;gBAAa;kBAAAG,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,KAAC,EAACzD,QAAQ,CAACE,YAAY;cAAA;gBAAAoD,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC3C,CAAC,EACZzD,QAAQ,CAACM,yBAAyB,GAAG,CAAC,iBACrC/B,OAAA,CAACjC,UAAU;gBAAC8G,OAAO,EAAC,OAAO;gBAAAD,QAAA,gBACzB5E,OAAA;kBAAA4E,QAAA,EAAQ;gBAAQ;kBAAAG,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,KAAC,EAACzD,QAAQ,CAACM,yBAAyB,EAAC,UAChE;cAAA;gBAAAgD,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAY,CACb,EACAzD,QAAQ,CAACK,aAAa,iBACrB9B,OAAA,CAACjC,UAAU;gBAAC8G,OAAO,EAAC,OAAO;gBAAAD,QAAA,gBACzB5E,OAAA;kBAAA4E,QAAA,EAAQ;gBAAS;kBAAAG,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,KAAC,EAAC,IAAI+D,IAAI,CAACxH,QAAQ,CAACK,aAAa,CAAC,CAACoH,kBAAkB,CAAC,OAAO,CAAC;cAAA;gBAAAnE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC9E,CACb;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACE,CAAC,EAGL7D,eAAe,CAACmB,MAAM,GAAG,CAAC,iBACzBxC,OAAA,CAACpC,GAAG;cAACgI,EAAE,EAAE;gBAAEC,EAAE,EAAE;cAAE,CAAE;cAAAjB,QAAA,gBACjB5E,OAAA,CAACjC,UAAU;gBAAC8G,OAAO,EAAC,OAAO;gBAACe,EAAE,EAAE;kBAAEuD,UAAU,EAAE,MAAM;kBAAElD,EAAE,EAAE;gBAAE,CAAE;gBAAArB,QAAA,EAAC;cAE/D;gBAAAG,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAY,CAAC,eACblF,OAAA,CAACpC,GAAG;gBAACgI,EAAE,EAAE;kBACPE,OAAO,EAAE,MAAM;kBACfiD,QAAQ,EAAE,MAAM;kBAChBC,GAAG,EAAE,GAAG;kBACR3C,SAAS,EAAE,OAAO;kBAClB+C,SAAS,EAAE,MAAM;kBACjBpD,CAAC,EAAE,CAAC;kBACJ6C,OAAO,EAAE,kBAAkB;kBAC3BC,YAAY,EAAE,CAAC;kBACfO,MAAM,EAAE,WAAW;kBACnBC,WAAW,EAAE;gBACf,CAAE;gBAAA1E,QAAA,EACCvD,eAAe,CAAC8C,GAAG,CAAC,CAACd,IAAI,EAAEkG,KAAK,kBAC/BvJ,OAAA,CAACpC,GAAG;kBAEFgI,EAAE,EAAE;oBACFE,OAAO,EAAE,aAAa;oBACtB0D,UAAU,EAAE,QAAQ;oBACpBX,OAAO,EAAE,eAAe;oBACxB1C,KAAK,EAAE,sBAAsB;oBAC7BsD,EAAE,EAAE,CAAC;oBACLC,EAAE,EAAE,GAAG;oBACPZ,YAAY,EAAE,CAAC;oBACfa,QAAQ,EAAE,SAAS;oBACnBR,UAAU,EAAE;kBACd,CAAE;kBAAAvE,QAAA,GAEDvB,IAAI,CAACI,OAAO,EACZJ,IAAI,CAACuD,SAAS,iBACb5G,OAAA,CAACjC,UAAU;oBAAC8G,OAAO,EAAC,SAAS;oBAACe,EAAE,EAAE;sBAAEgE,EAAE,EAAE,GAAG;sBAAEC,OAAO,EAAE;oBAAI,CAAE;oBAAAjF,QAAA,GAAC,GAC1D,EAACvB,IAAI,CAACuD,SAAS,EAAC,GACnB;kBAAA;oBAAA7B,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAY,CACb;gBAAA,GAlBI7B,IAAI,CAACI,OAAO;kBAAAsB,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAmBd,CACN;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACC,CAAC,EAGL,CAAC,MAAM;gBACN,MAAM4E,WAAW,GAAGzI,eAAe,CAAC0I,MAAM,CAAC,CAACC,GAAG,EAAE3G,IAAI,KAAK2G,GAAG,IAAI3G,IAAI,CAACyD,aAAa,IAAI,CAAC,CAAC,EAAE,CAAC,CAAC;gBAC7F,MAAMmD,SAAS,GAAG,CAAC,GAAG,IAAIC,GAAG,CAAC7I,eAAe,CAAC8C,GAAG,CAACX,CAAC,IAAIA,CAAC,CAACoD,SAAS,CAAC,CAAClD,MAAM,CAACyG,OAAO,CAAC,CAAC,CAAC;gBAErF,oBACEnK,OAAA,CAACpC,GAAG;kBAACgI,EAAE,EAAE;oBAAEC,EAAE,EAAE,CAAC;oBAAEC,OAAO,EAAE,MAAM;oBAAEiD,QAAQ,EAAE,MAAM;oBAAEC,GAAG,EAAE;kBAAE,CAAE;kBAAApE,QAAA,GAC3DkF,WAAW,GAAG,CAAC,iBACd9J,OAAA,CAACjC,UAAU;oBAAC8G,OAAO,EAAC,SAAS;oBAACsB,KAAK,EAAC,gBAAgB;oBAAAvB,QAAA,gBAClD5E,OAAA;sBAAA4E,QAAA,EAAQ;oBAAa;sBAAAG,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAQ,CAAC,KAAC,EAAC4E,WAAW,CAACM,OAAO,CAAC,CAAC,CAAC,EAAC,GACzD;kBAAA;oBAAArF,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAY,CACb,EACA+E,SAAS,CAACzH,MAAM,GAAG,CAAC,iBACnBxC,OAAA,CAACjC,UAAU;oBAAC8G,OAAO,EAAC,SAAS;oBAACsB,KAAK,EAAC,gBAAgB;oBAAAvB,QAAA,gBAClD5E,OAAA;sBAAA4E,QAAA,EAAQ;oBAAU;sBAAAG,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAQ,CAAC,KAAC,EAAC+E,SAAS,CAACI,IAAI,CAAC,IAAI,CAAC;kBAAA;oBAAAtF,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACvC,CACb;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACE,CAAC;cAEV,CAAC,EAAE,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACD,CACN,EAGAzD,QAAQ,CAACK,aAAa,IAAIL,QAAQ,CAACM,yBAAyB,iBAC3D/B,OAAA,CAACpC,GAAG;cAACgI,EAAE,EAAE;gBAAEC,EAAE,EAAE,CAAC;gBAAEG,CAAC,EAAE,GAAG;gBAAE6C,OAAO,EAAE,YAAY;gBAAEC,YAAY,EAAE,CAAC;gBAAE3C,KAAK,EAAE;cAAoB,CAAE;cAAAvB,QAAA,gBAC7F5E,OAAA,CAACjC,UAAU;gBAAC8G,OAAO,EAAC,SAAS;gBAACe,EAAE,EAAE;kBAAEuD,UAAU,EAAE;gBAAO,CAAE;gBAAAvE,QAAA,EAAC;cAE1D;gBAAAG,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAY,CAAC,eACblF,OAAA,CAACpC,GAAG;gBAACgI,EAAE,EAAE;kBAAEE,OAAO,EAAE,MAAM;kBAAEiD,QAAQ,EAAE,MAAM;kBAAEC,GAAG,EAAE,CAAC;kBAAEnD,EAAE,EAAE;gBAAI,CAAE;gBAAAjB,QAAA,EAC7D,CAAC,MAAM;kBACN,MAAM0F,IAAI,GAAG,IAAIrB,IAAI,CAAC,CAAC;kBACvB,MAAMsB,QAAQ,GAAG,IAAItB,IAAI,CAACxH,QAAQ,CAACK,aAAa,CAAC;kBACjD,MAAM0I,iBAAiB,GAAGnC,IAAI,CAACC,GAAG,CAAC,CAAC,EAAED,IAAI,CAACoC,IAAI,CAAC,CAACF,QAAQ,GAAGD,IAAI,KAAK,IAAI,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,CAAC,CAAC,CAAC;kBAC3F,MAAMI,cAAc,GAAG,CAAC,CAAC,CAAC;kBAC1B,MAAMC,oBAAoB,GAAGH,iBAAiB,GAAGE,cAAc,GAAGjJ,QAAQ,CAACM,yBAAyB;;kBAEpG;kBACA,MAAM+H,WAAW,GAAGzI,eAAe,CAAC0I,MAAM,CAAC,CAACC,GAAG,EAAE3G,IAAI,KAAK2G,GAAG,IAAI3G,IAAI,CAACyD,aAAa,IAAI,CAAC,CAAC,EAAE,CAAC,CAAC;;kBAE7F;kBACA,IAAI8D,WAAW,GAAG,CAAC;kBACnB,IAAIC,WAAW,GAAG,EAAE;kBAEpB,IAAI5J,WAAW,KAAK,MAAM,EAAE;oBAC1B2J,WAAW,GAAGd,WAAW,GAAG,CAAC,GAAG,CAACa,oBAAoB,GAAGb,WAAW,GAAGrI,QAAQ,CAACM,yBAAyB,EAAEqI,OAAO,CAAC,CAAC,CAAC,GAAG,CAAC;oBACxHS,WAAW,GAAG,iBAAiB;kBACjC,CAAC,MAAM,IAAI5J,WAAW,CAAC6J,QAAQ,CAAC,cAAc,CAAC,EAAE;oBAC/CF,WAAW,GAAGvJ,eAAe,CAACmB,MAAM,GAAG,CAAC,GAAG,CAACmI,oBAAoB,GAAGtJ,eAAe,CAACmB,MAAM,EAAE4H,OAAO,CAAC,CAAC,CAAC,GAAG,CAAC;oBACzGS,WAAW,GAAG,oBAAoB;kBACpC,CAAC,MAAM,IAAI5J,WAAW,KAAK,gBAAgB,EAAE;oBAC3C2J,WAAW,GAAGvJ,eAAe,CAACmB,MAAM,GAAG,CAAC,GAAG,CAACmI,oBAAoB,GAAGtJ,eAAe,CAACmB,MAAM,EAAE4H,OAAO,CAAC,CAAC,CAAC,GAAG,CAAC;oBACzGS,WAAW,GAAG,YAAY;kBAC5B;kBAEA,oBACE7K,OAAA,CAAAE,SAAA;oBAAA0E,QAAA,gBACE5E,OAAA,CAACjC,UAAU;sBAAC8G,OAAO,EAAC,SAAS;sBAAAD,QAAA,gBAC3B5E,OAAA;wBAAA4E,QAAA,EAAQ;sBAAmB;wBAAAG,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAQ,CAAC,KAAC,EAACsF,iBAAiB;oBAAA;sBAAAzF,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAC7C,CAAC,eACblF,OAAA,CAACjC,UAAU;sBAAC8G,OAAO,EAAC,SAAS;sBAAAD,QAAA,gBAC3B5E,OAAA;wBAAA4E,QAAA,EAAQ;sBAAmB;wBAAAG,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAQ,CAAC,KAAC,EAACyF,oBAAoB,EAAC,GAC7D;oBAAA;sBAAA5F,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAY,CAAC,EACZjE,WAAW,KAAK,MAAM,IAAI6I,WAAW,GAAG,CAAC,iBACxC9J,OAAA,CAACjC,UAAU;sBAAC8G,OAAO,EAAC,SAAS;sBAAAD,QAAA,gBAC3B5E,OAAA;wBAAA4E,QAAA,EAAQ;sBAAa;wBAAAG,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAQ,CAAC,KAAC,EAAC4E,WAAW,CAACM,OAAO,CAAC,CAAC,CAAC,EAAC,GACzD;oBAAA;sBAAArF,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAY,CACb,EACA0F,WAAW,GAAG,CAAC,iBACd5K,OAAA,CAACjC,UAAU;sBAAC8G,OAAO,EAAC,SAAS;sBAAAD,QAAA,gBAC3B5E,OAAA;wBAAA4E,QAAA,EAAQ;sBAAe;wBAAAG,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAQ,CAAC,KAAC,EAAC0F,WAAW,EAAC,GAAC,EAACC,WAAW;oBAAA;sBAAA9F,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACjD,CACb;kBAAA,eACD,CAAC;gBAEP,CAAC,EAAE;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACD,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CACN;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACE,CACN;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACE,CAAC;MAGV;QACE,OAAO,IAAI;IACf;EACF,CAAC;EAED,oBACElF,OAAA,CAAC/B,MAAM;IAACoC,IAAI,EAAEA,IAAK;IAACC,OAAO,EAAEgE,WAAY;IAACyG,QAAQ,EAAC,IAAI;IAAC5F,SAAS;IAAAP,QAAA,gBAC/D5E,OAAA,CAAC9B,WAAW;MAAA0G,QAAA,eACV5E,OAAA,CAACpC,GAAG;QAACkI,OAAO,EAAC,MAAM;QAAC0D,UAAU,EAAC,QAAQ;QAACR,GAAG,EAAE,CAAE;QAAApE,QAAA,gBAC7C5E,OAAA,CAACJ,cAAc;UAAAmF,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,+BAEpB;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAK;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACK,CAAC,eAEdlF,OAAA,CAAC7B,aAAa;MAAAyG,QAAA,eACZ5E,OAAA,CAACpC,GAAG;QAACgI,EAAE,EAAE;UAAEoF,EAAE,EAAE;QAAE,CAAE;QAAApG,QAAA,gBACjB5E,OAAA,CAACvB,OAAO;UAACkC,UAAU,EAAEA,UAAW;UAACiF,EAAE,EAAE;YAAEK,EAAE,EAAE;UAAE,CAAE;UAAArB,QAAA,EAC5CvC,KAAK,CAAC8B,GAAG,CAAEkB,KAAK,iBACfrF,OAAA,CAACtB,IAAI;YAAAkG,QAAA,eACH5E,OAAA,CAACrB,SAAS;cAAAiG,QAAA,EAAES;YAAK;cAAAN,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAY;UAAC,GADrBG,KAAK;YAAAN,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAEV,CACP;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACK,CAAC,EAETnE,KAAK,iBACJf,OAAA,CAACzB,KAAK;UAACoH,QAAQ,EAAC,OAAO;UAACC,EAAE,EAAE;YAAEK,EAAE,EAAE;UAAE,CAAE;UAAArB,QAAA,EACnC7D;QAAK;UAAAgE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACD,CACR,EAEAP,iBAAiB,CAAC,CAAC;MAAA;QAAAI,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACjB;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACO,CAAC,eAEhBlF,OAAA,CAAC5B,aAAa;MAACwH,EAAE,EAAE;QAAEI,CAAC,EAAE,CAAC;QAAEgD,GAAG,EAAE;MAAE,CAAE;MAAApE,QAAA,EACjCjE,UAAU,GAAG0B,KAAK,CAACG,MAAM,GAAG,CAAC,gBAC5BxC,OAAA,CAAAE,SAAA;QAAA0E,QAAA,gBACE5E,OAAA,CAAChC,MAAM;UACLyI,OAAO,EAAEnC,WAAY;UACrBO,OAAO,EAAC,UAAU;UAClBe,EAAE,EAAE;YAAEqF,QAAQ,EAAE;UAAI,CAAE;UAAArG,QAAA,EACvB;QAED;UAAAG,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC,eACTlF,OAAA,CAAChC,MAAM;UACLyI,OAAO,EAAEzD,UAAW;UACpB6B,OAAO,EAAC,WAAW;UACnBqG,QAAQ,EAAEvK,UAAU,KAAK,CAAC,IAAIY,WAAY;UAC1CqE,EAAE,EAAE;YAAEqF,QAAQ,EAAE;UAAI,CAAE;UAAArG,QAAA,EACvB;QAED;UAAAG,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC;MAAA,eACT,CAAC,gBAEHlF,OAAA,CAAAE,SAAA;QAAA0E,QAAA,gBACE5E,OAAA,CAAChC,MAAM;UACLyI,OAAO,EAAEnC,WAAY;UACrBO,OAAO,EAAC,UAAU;UAClBe,EAAE,EAAE;YAAEqF,QAAQ,EAAE;UAAI,CAAE;UAAArG,QAAA,EACvB;QAED;UAAAG,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC,eACTlF,OAAA,CAAChC,MAAM;UACLyI,OAAO,EAAE9C,YAAa;UACtBkB,OAAO,EAAC,WAAW;UACnBqG,QAAQ,EACNrK,OAAO,IACPQ,eAAe,CAACmB,MAAM,KAAK,CAAC,IAC5B,CAACf,QAAQ,CAACE,YAAY,CAACsB,IAAI,CAAC,CAC7B;UACDkI,SAAS,EAAEtK,OAAO,gBAAGb,OAAA,CAACxB,gBAAgB;YAACsJ,IAAI,EAAE;UAAG;YAAA/C,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,gBAAGlF,OAAA,CAACR,OAAO;YAAAuF,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAE;UAClEU,EAAE,EAAE;YAAEqF,QAAQ,EAAE;UAAI,CAAE;UAAArG,QAAA,EAErB/D,OAAO,GAAG,cAAc,GAAG;QAAc;UAAAkE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACpC,CAAC;MAAA,eACT;IACH;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACY,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACV,CAAC;AAEb,CAAC;AAACxE,EAAA,CAppBIP,kBAAkB;AAAAiL,EAAA,GAAlBjL,kBAAkB;AAspBxB,eAAeA,kBAAkB;AAAC,IAAAiL,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}