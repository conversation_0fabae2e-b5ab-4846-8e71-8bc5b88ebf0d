{"ast": null, "code": "import { formatDistance } from \"./az/_lib/formatDistance.mjs\";\nimport { formatLong } from \"./az/_lib/formatLong.mjs\";\nimport { formatRelative } from \"./az/_lib/formatRelative.mjs\";\nimport { localize } from \"./az/_lib/localize.mjs\";\nimport { match } from \"./az/_lib/match.mjs\";\n\n/**\n * @category Locales\n * @summary Azerbaijani locale.\n * @language Azerbaijani\n * @iso-639-2 aze\n */\n\nexport const az = {\n  code: \"az\",\n  formatDistance: formatDistance,\n  formatLong: formatLong,\n  formatRelative: formatRelative,\n  localize: localize,\n  match: match,\n  options: {\n    weekStartsOn: 1,\n    firstWeekContainsDate: 1\n  }\n};\n\n// Fallback for modularized imports:\nexport default az;", "map": {"version": 3, "names": ["formatDistance", "formatLong", "formatRelative", "localize", "match", "az", "code", "options", "weekStartsOn", "firstWeekContainsDate"], "sources": ["C:/CMS/webapp/frontend/node_modules/date-fns/locale/az.mjs"], "sourcesContent": ["import { formatDistance } from \"./az/_lib/formatDistance.mjs\";\nimport { formatLong } from \"./az/_lib/formatLong.mjs\";\nimport { formatRelative } from \"./az/_lib/formatRelative.mjs\";\nimport { localize } from \"./az/_lib/localize.mjs\";\nimport { match } from \"./az/_lib/match.mjs\";\n\n/**\n * @category Locales\n * @summary Azerbaijani locale.\n * @language Azerbaijani\n * @iso-639-2 aze\n */\n\nexport const az = {\n  code: \"az\",\n  formatDistance: formatDistance,\n  formatLong: formatLong,\n  formatRelative: formatRelative,\n  localize: localize,\n  match: match,\n  options: {\n    weekStartsOn: 1,\n    firstWeekContainsDate: 1,\n  },\n};\n\n// Fallback for modularized imports:\nexport default az;\n"], "mappings": "AAAA,SAASA,cAAc,QAAQ,8BAA8B;AAC7D,SAASC,UAAU,QAAQ,0BAA0B;AACrD,SAASC,cAAc,QAAQ,8BAA8B;AAC7D,SAASC,QAAQ,QAAQ,wBAAwB;AACjD,SAASC,KAAK,QAAQ,qBAAqB;;AAE3C;AACA;AACA;AACA;AACA;AACA;;AAEA,OAAO,MAAMC,EAAE,GAAG;EAChBC,IAAI,EAAE,IAAI;EACVN,cAAc,EAAEA,cAAc;EAC9BC,UAAU,EAAEA,UAAU;EACtBC,cAAc,EAAEA,cAAc;EAC9BC,QAAQ,EAAEA,QAAQ;EAClBC,KAAK,EAAEA,KAAK;EACZG,OAAO,EAAE;IACPC,YAAY,EAAE,CAAC;IACfC,qBAAqB,EAAE;EACzB;AACF,CAAC;;AAED;AACA,eAAeJ,EAAE", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}