{"ast": null, "code": "const formatRelativeLocale = {\n  lastWeek: date => {\n    switch (date.getDay()) {\n      case 6:\n        //Σάββατο\n        return \"'το προηγούμενο' eeee 'στις' p\";\n      default:\n        return \"'την προηγούμενη' eeee 'στις' p\";\n    }\n  },\n  yesterday: \"'χθες στις' p\",\n  today: \"'σήμερα στις' p\",\n  tomorrow: \"'αύριο στις' p\",\n  nextWeek: \"eeee 'στις' p\",\n  other: \"P\"\n};\nexport const formatRelative = (token, date) => {\n  const format = formatRelativeLocale[token];\n  if (typeof format === \"function\") return format(date);\n  return format;\n};", "map": {"version": 3, "names": ["formatRelativeLocale", "lastWeek", "date", "getDay", "yesterday", "today", "tomorrow", "nextWeek", "other", "formatRelative", "token", "format"], "sources": ["C:/CMS/webapp/frontend/node_modules/date-fns/locale/el/_lib/formatRelative.mjs"], "sourcesContent": ["const formatRelativeLocale = {\n  lastWeek: (date) => {\n    switch (date.getDay()) {\n      case 6: //Σάββατο\n        return \"'το προηγούμενο' eeee 'στις' p\";\n      default:\n        return \"'την προηγούμενη' eeee 'στις' p\";\n    }\n  },\n  yesterday: \"'χθες στις' p\",\n  today: \"'σήμερα στις' p\",\n  tomorrow: \"'αύριο στις' p\",\n  nextWeek: \"eeee 'στις' p\",\n  other: \"P\",\n};\n\nexport const formatRelative = (token, date) => {\n  const format = formatRelativeLocale[token];\n\n  if (typeof format === \"function\") return format(date);\n\n  return format;\n};\n"], "mappings": "AAAA,MAAMA,oBAAoB,GAAG;EAC3BC,QAAQ,EAAGC,IAAI,IAAK;IAClB,QAAQA,IAAI,CAACC,MAAM,CAAC,CAAC;MACnB,KAAK,CAAC;QAAE;QACN,OAAO,gCAAgC;MACzC;QACE,OAAO,iCAAiC;IAC5C;EACF,CAAC;EACDC,SAAS,EAAE,eAAe;EAC1BC,KAAK,EAAE,iBAAiB;EACxBC,QAAQ,EAAE,gBAAgB;EAC1BC,QAAQ,EAAE,eAAe;EACzBC,KAAK,EAAE;AACT,CAAC;AAED,OAAO,MAAMC,cAAc,GAAGA,CAACC,KAAK,EAAER,IAAI,KAAK;EAC7C,MAAMS,MAAM,GAAGX,oBAAoB,CAACU,KAAK,CAAC;EAE1C,IAAI,OAAOC,MAAM,KAAK,UAAU,EAAE,OAAOA,MAAM,CAACT,IAAI,CAAC;EAErD,OAAOS,MAAM;AACf,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}