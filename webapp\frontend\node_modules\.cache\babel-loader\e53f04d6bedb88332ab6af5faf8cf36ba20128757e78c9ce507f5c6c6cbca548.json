{"ast": null, "code": "import axios from 'axios';\nconst API_URL = 'http://localhost:8000/api';\n\n// Crea un'istanza di axios con configurazione personalizzata\nconst axiosInstance = axios.create({\n  baseURL: API_URL,\n  headers: {\n    'Content-Type': 'application/json'\n  }\n});\n\n// Configura axios per includere il token in tutte le richieste\naxiosInstance.interceptors.request.use(config => {\n  const token = localStorage.getItem('token');\n  if (token) {\n    config.headers.Authorization = `Bearer ${token}`;\n  }\n  return config;\n}, error => {\n  return Promise.reject(error);\n});\nconst excelService = {\n  // Importa cavi da Excel\n  importCavi: async (cantiereId, formData) => {\n    try {\n      // Assicurati che cantiereId sia un numero\n      const cantiereIdNum = parseInt(cantiereId, 10);\n      if (isNaN(cantiereIdNum)) {\n        throw new Error(`ID cantiere non valido: ${cantiereId}`);\n      }\n\n      // Modifica la configurazione per l'upload di file\n      const config = {\n        headers: {\n          'Content-Type': 'multipart/form-data',\n          'Authorization': `Bearer ${localStorage.getItem('token')}`\n        }\n      };\n      const response = await axios.post(`${API_URL}/excel/${cantiereIdNum}/import-cavi`, formData, config);\n      return response.data;\n    } catch (error) {\n      console.error('Import cavi error:', error);\n      throw error.response ? error.response.data : error;\n    }\n  },\n  // Importa parco bobine da Excel\n  importParcoBobine: async (cantiereId, formData) => {\n    try {\n      // Assicurati che cantiereId sia un numero\n      const cantiereIdNum = parseInt(cantiereId, 10);\n      if (isNaN(cantiereIdNum)) {\n        throw new Error(`ID cantiere non valido: ${cantiereId}`);\n      }\n\n      // Modifica la configurazione per l'upload di file\n      const config = {\n        headers: {\n          'Content-Type': 'multipart/form-data',\n          'Authorization': `Bearer ${localStorage.getItem('token')}`\n        }\n      };\n      const response = await axios.post(`${API_URL}/excel/${cantiereIdNum}/import-parco-bobine`, formData, config);\n      return response.data;\n    } catch (error) {\n      console.error('Import parco bobine error:', error);\n      throw error.response ? error.response.data : error;\n    }\n  },\n  // Crea template Excel per cavi\n  createCaviTemplate: async () => {\n    try {\n      const response = await axiosInstance.get('/excel/template-cavi');\n      return response.data;\n    } catch (error) {\n      console.error('Create cavi template error:', error);\n      throw error.response ? error.response.data : error;\n    }\n  },\n  // Crea template Excel per parco bobine\n  createParcoBobineTemplate: async () => {\n    try {\n      const response = await axiosInstance.get('/excel/template-parco-bobine');\n      return response.data;\n    } catch (error) {\n      console.error('Create parco bobine template error:', error);\n      throw error.response ? error.response.data : error;\n    }\n  },\n  // Esporta cavi in Excel\n  exportCavi: async cantiereId => {\n    try {\n      // Assicurati che cantiereId sia un numero\n      const cantiereIdNum = parseInt(cantiereId, 10);\n      if (isNaN(cantiereIdNum)) {\n        throw new Error(`ID cantiere non valido: ${cantiereId}`);\n      }\n      const response = await axiosInstance.get(`/excel/${cantiereIdNum}/export-cavi`);\n      return response.data;\n    } catch (error) {\n      console.error('Export cavi error:', error);\n      throw error.response ? error.response.data : error;\n    }\n  },\n  // Esporta parco bobine in Excel\n  exportParcoBobine: async cantiereId => {\n    try {\n      // Assicurati che cantiereId sia un numero\n      const cantiereIdNum = parseInt(cantiereId, 10);\n      if (isNaN(cantiereIdNum)) {\n        throw new Error(`ID cantiere non valido: ${cantiereId}`);\n      }\n      const response = await axiosInstance.get(`/excel/${cantiereIdNum}/export-parco-bobine`);\n      return response.data;\n    } catch (error) {\n      console.error('Export parco bobine error:', error);\n      throw error.response ? error.response.data : error;\n    }\n  }\n};\nexport default excelService;", "map": {"version": 3, "names": ["axios", "API_URL", "axiosInstance", "create", "baseURL", "headers", "interceptors", "request", "use", "config", "token", "localStorage", "getItem", "Authorization", "error", "Promise", "reject", "excelService", "importCavi", "cantiereId", "formData", "cantiereIdNum", "parseInt", "isNaN", "Error", "response", "post", "data", "console", "importParcoBobine", "createCaviTemplate", "get", "createParcoBobineTemplate", "exportCavi", "exportParcoBobine"], "sources": ["C:/Users/<USER>/PycharmProjects/CMS/webapp/frontend/src/services/excelService.js"], "sourcesContent": ["import axios from 'axios';\n\nconst API_URL = 'http://localhost:8000/api';\n\n// Crea un'istanza di axios con configurazione personalizzata\nconst axiosInstance = axios.create({\n  baseURL: API_URL,\n  headers: {\n    'Content-Type': 'application/json'\n  }\n});\n\n// Configura axios per includere il token in tutte le richieste\naxiosInstance.interceptors.request.use(\n  (config) => {\n    const token = localStorage.getItem('token');\n    if (token) {\n      config.headers.Authorization = `Bearer ${token}`;\n    }\n    return config;\n  },\n  (error) => {\n    return Promise.reject(error);\n  }\n);\n\nconst excelService = {\n  // Importa cavi da Excel\n  importCavi: async (cantiereId, formData) => {\n    try {\n      // Assicurati che cantiereId sia un numero\n      const cantiereIdNum = parseInt(cantiereId, 10);\n      if (isNaN(cantiereIdNum)) {\n        throw new Error(`ID cantiere non valido: ${cantiereId}`);\n      }\n\n      // Modifica la configurazione per l'upload di file\n      const config = {\n        headers: {\n          'Content-Type': 'multipart/form-data',\n          'Authorization': `Bearer ${localStorage.getItem('token')}`\n        }\n      };\n\n      const response = await axios.post(`${API_URL}/excel/${cantiereIdNum}/import-cavi`, formData, config);\n      return response.data;\n    } catch (error) {\n      console.error('Import cavi error:', error);\n      throw error.response ? error.response.data : error;\n    }\n  },\n\n  // Importa parco bobine da Excel\n  importParcoBobine: async (cantiereId, formData) => {\n    try {\n      // Assicurati che cantiereId sia un numero\n      const cantiereIdNum = parseInt(cantiereId, 10);\n      if (isNaN(cantiereIdNum)) {\n        throw new Error(`ID cantiere non valido: ${cantiereId}`);\n      }\n\n      // Modifica la configurazione per l'upload di file\n      const config = {\n        headers: {\n          'Content-Type': 'multipart/form-data',\n          'Authorization': `Bearer ${localStorage.getItem('token')}`\n        }\n      };\n\n      const response = await axios.post(`${API_URL}/excel/${cantiereIdNum}/import-parco-bobine`, formData, config);\n      return response.data;\n    } catch (error) {\n      console.error('Import parco bobine error:', error);\n      throw error.response ? error.response.data : error;\n    }\n  },\n\n  // Crea template Excel per cavi\n  createCaviTemplate: async () => {\n    try {\n      const response = await axiosInstance.get('/excel/template-cavi');\n      return response.data;\n    } catch (error) {\n      console.error('Create cavi template error:', error);\n      throw error.response ? error.response.data : error;\n    }\n  },\n\n  // Crea template Excel per parco bobine\n  createParcoBobineTemplate: async () => {\n    try {\n      const response = await axiosInstance.get('/excel/template-parco-bobine');\n      return response.data;\n    } catch (error) {\n      console.error('Create parco bobine template error:', error);\n      throw error.response ? error.response.data : error;\n    }\n  },\n\n  // Esporta cavi in Excel\n  exportCavi: async (cantiereId) => {\n    try {\n      // Assicurati che cantiereId sia un numero\n      const cantiereIdNum = parseInt(cantiereId, 10);\n      if (isNaN(cantiereIdNum)) {\n        throw new Error(`ID cantiere non valido: ${cantiereId}`);\n      }\n\n      const response = await axiosInstance.get(`/excel/${cantiereIdNum}/export-cavi`);\n      return response.data;\n    } catch (error) {\n      console.error('Export cavi error:', error);\n      throw error.response ? error.response.data : error;\n    }\n  },\n\n  // Esporta parco bobine in Excel\n  exportParcoBobine: async (cantiereId) => {\n    try {\n      // Assicurati che cantiereId sia un numero\n      const cantiereIdNum = parseInt(cantiereId, 10);\n      if (isNaN(cantiereIdNum)) {\n        throw new Error(`ID cantiere non valido: ${cantiereId}`);\n      }\n\n      const response = await axiosInstance.get(`/excel/${cantiereIdNum}/export-parco-bobine`);\n      return response.data;\n    } catch (error) {\n      console.error('Export parco bobine error:', error);\n      throw error.response ? error.response.data : error;\n    }\n  }\n};\n\nexport default excelService;\n"], "mappings": "AAAA,OAAOA,KAAK,MAAM,OAAO;AAEzB,MAAMC,OAAO,GAAG,2BAA2B;;AAE3C;AACA,MAAMC,aAAa,GAAGF,KAAK,CAACG,MAAM,CAAC;EACjCC,OAAO,EAAEH,OAAO;EAChBI,OAAO,EAAE;IACP,cAAc,EAAE;EAClB;AACF,CAAC,CAAC;;AAEF;AACAH,aAAa,CAACI,YAAY,CAACC,OAAO,CAACC,GAAG,CACnCC,MAAM,IAAK;EACV,MAAMC,KAAK,GAAGC,YAAY,CAACC,OAAO,CAAC,OAAO,CAAC;EAC3C,IAAIF,KAAK,EAAE;IACTD,MAAM,CAACJ,OAAO,CAACQ,aAAa,GAAG,UAAUH,KAAK,EAAE;EAClD;EACA,OAAOD,MAAM;AACf,CAAC,EACAK,KAAK,IAAK;EACT,OAAOC,OAAO,CAACC,MAAM,CAACF,KAAK,CAAC;AAC9B,CACF,CAAC;AAED,MAAMG,YAAY,GAAG;EACnB;EACAC,UAAU,EAAE,MAAAA,CAAOC,UAAU,EAAEC,QAAQ,KAAK;IAC1C,IAAI;MACF;MACA,MAAMC,aAAa,GAAGC,QAAQ,CAACH,UAAU,EAAE,EAAE,CAAC;MAC9C,IAAII,KAAK,CAACF,aAAa,CAAC,EAAE;QACxB,MAAM,IAAIG,KAAK,CAAC,2BAA2BL,UAAU,EAAE,CAAC;MAC1D;;MAEA;MACA,MAAMV,MAAM,GAAG;QACbJ,OAAO,EAAE;UACP,cAAc,EAAE,qBAAqB;UACrC,eAAe,EAAE,UAAUM,YAAY,CAACC,OAAO,CAAC,OAAO,CAAC;QAC1D;MACF,CAAC;MAED,MAAMa,QAAQ,GAAG,MAAMzB,KAAK,CAAC0B,IAAI,CAAC,GAAGzB,OAAO,UAAUoB,aAAa,cAAc,EAAED,QAAQ,EAAEX,MAAM,CAAC;MACpG,OAAOgB,QAAQ,CAACE,IAAI;IACtB,CAAC,CAAC,OAAOb,KAAK,EAAE;MACdc,OAAO,CAACd,KAAK,CAAC,oBAAoB,EAAEA,KAAK,CAAC;MAC1C,MAAMA,KAAK,CAACW,QAAQ,GAAGX,KAAK,CAACW,QAAQ,CAACE,IAAI,GAAGb,KAAK;IACpD;EACF,CAAC;EAED;EACAe,iBAAiB,EAAE,MAAAA,CAAOV,UAAU,EAAEC,QAAQ,KAAK;IACjD,IAAI;MACF;MACA,MAAMC,aAAa,GAAGC,QAAQ,CAACH,UAAU,EAAE,EAAE,CAAC;MAC9C,IAAII,KAAK,CAACF,aAAa,CAAC,EAAE;QACxB,MAAM,IAAIG,KAAK,CAAC,2BAA2BL,UAAU,EAAE,CAAC;MAC1D;;MAEA;MACA,MAAMV,MAAM,GAAG;QACbJ,OAAO,EAAE;UACP,cAAc,EAAE,qBAAqB;UACrC,eAAe,EAAE,UAAUM,YAAY,CAACC,OAAO,CAAC,OAAO,CAAC;QAC1D;MACF,CAAC;MAED,MAAMa,QAAQ,GAAG,MAAMzB,KAAK,CAAC0B,IAAI,CAAC,GAAGzB,OAAO,UAAUoB,aAAa,sBAAsB,EAAED,QAAQ,EAAEX,MAAM,CAAC;MAC5G,OAAOgB,QAAQ,CAACE,IAAI;IACtB,CAAC,CAAC,OAAOb,KAAK,EAAE;MACdc,OAAO,CAACd,KAAK,CAAC,4BAA4B,EAAEA,KAAK,CAAC;MAClD,MAAMA,KAAK,CAACW,QAAQ,GAAGX,KAAK,CAACW,QAAQ,CAACE,IAAI,GAAGb,KAAK;IACpD;EACF,CAAC;EAED;EACAgB,kBAAkB,EAAE,MAAAA,CAAA,KAAY;IAC9B,IAAI;MACF,MAAML,QAAQ,GAAG,MAAMvB,aAAa,CAAC6B,GAAG,CAAC,sBAAsB,CAAC;MAChE,OAAON,QAAQ,CAACE,IAAI;IACtB,CAAC,CAAC,OAAOb,KAAK,EAAE;MACdc,OAAO,CAACd,KAAK,CAAC,6BAA6B,EAAEA,KAAK,CAAC;MACnD,MAAMA,KAAK,CAACW,QAAQ,GAAGX,KAAK,CAACW,QAAQ,CAACE,IAAI,GAAGb,KAAK;IACpD;EACF,CAAC;EAED;EACAkB,yBAAyB,EAAE,MAAAA,CAAA,KAAY;IACrC,IAAI;MACF,MAAMP,QAAQ,GAAG,MAAMvB,aAAa,CAAC6B,GAAG,CAAC,8BAA8B,CAAC;MACxE,OAAON,QAAQ,CAACE,IAAI;IACtB,CAAC,CAAC,OAAOb,KAAK,EAAE;MACdc,OAAO,CAACd,KAAK,CAAC,qCAAqC,EAAEA,KAAK,CAAC;MAC3D,MAAMA,KAAK,CAACW,QAAQ,GAAGX,KAAK,CAACW,QAAQ,CAACE,IAAI,GAAGb,KAAK;IACpD;EACF,CAAC;EAED;EACAmB,UAAU,EAAE,MAAOd,UAAU,IAAK;IAChC,IAAI;MACF;MACA,MAAME,aAAa,GAAGC,QAAQ,CAACH,UAAU,EAAE,EAAE,CAAC;MAC9C,IAAII,KAAK,CAACF,aAAa,CAAC,EAAE;QACxB,MAAM,IAAIG,KAAK,CAAC,2BAA2BL,UAAU,EAAE,CAAC;MAC1D;MAEA,MAAMM,QAAQ,GAAG,MAAMvB,aAAa,CAAC6B,GAAG,CAAC,UAAUV,aAAa,cAAc,CAAC;MAC/E,OAAOI,QAAQ,CAACE,IAAI;IACtB,CAAC,CAAC,OAAOb,KAAK,EAAE;MACdc,OAAO,CAACd,KAAK,CAAC,oBAAoB,EAAEA,KAAK,CAAC;MAC1C,MAAMA,KAAK,CAACW,QAAQ,GAAGX,KAAK,CAACW,QAAQ,CAACE,IAAI,GAAGb,KAAK;IACpD;EACF,CAAC;EAED;EACAoB,iBAAiB,EAAE,MAAOf,UAAU,IAAK;IACvC,IAAI;MACF;MACA,MAAME,aAAa,GAAGC,QAAQ,CAACH,UAAU,EAAE,EAAE,CAAC;MAC9C,IAAII,KAAK,CAACF,aAAa,CAAC,EAAE;QACxB,MAAM,IAAIG,KAAK,CAAC,2BAA2BL,UAAU,EAAE,CAAC;MAC1D;MAEA,MAAMM,QAAQ,GAAG,MAAMvB,aAAa,CAAC6B,GAAG,CAAC,UAAUV,aAAa,sBAAsB,CAAC;MACvF,OAAOI,QAAQ,CAACE,IAAI;IACtB,CAAC,CAAC,OAAOb,KAAK,EAAE;MACdc,OAAO,CAACd,KAAK,CAAC,4BAA4B,EAAEA,KAAK,CAAC;MAClD,MAAMA,KAAK,CAACW,QAAQ,GAAGX,KAAK,CAACW,QAAQ,CAACE,IAAI,GAAGb,KAAK;IACpD;EACF;AACF,CAAC;AAED,eAAeG,YAAY", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}