{"ast": null, "code": "'use client';\n\nimport useIsFocusVisible from '@mui/utils/useIsFocusVisible';\nexport default useIsFocusVisible;", "map": {"version": 3, "names": ["useIsFocusVisible"], "sources": ["C:/Users/<USER>/PycharmProjects/CMS/webapp/frontend/node_modules/@mui/material/utils/useIsFocusVisible.js"], "sourcesContent": ["'use client';\n\nimport useIsFocusVisible from '@mui/utils/useIsFocusVisible';\nexport default useIsFocusVisible;"], "mappings": "AAAA,YAAY;;AAEZ,OAAOA,iBAAiB,MAAM,8BAA8B;AAC5D,eAAeA,iBAAiB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}