{"ast": null, "code": "import { constructFrom } from \"./constructFrom.mjs\";\n\n/**\n * @name transpose\n * @category Generic Helpers\n * @summary Transpose the date to the given constructor.\n *\n * @description\n * The function transposes the date to the given constructor. It helps you\n * to transpose the date in the system time zone to say `UTCDate` or any other\n * date extension.\n *\n * @typeParam DateInputType - The input `Date` type derived from the passed argument.\n * @typeParam DateOutputType - The output `Date` type derived from the passed constructor.\n *\n * @param fromDate - The date to use values from\n * @param constructor - The date constructor to use\n *\n * @returns Date transposed to the given constructor\n *\n * @example\n * // Create July 10, 2022 00:00 in locale time zone\n * const date = new Date(2022, 6, 10)\n * //=> 'Sun Jul 10 2022 00:00:00 GMT+0800 (Singapore Standard Time)'\n *\n * @example\n * // Transpose the date to July 10, 2022 00:00 in UTC\n * transpose(date, UTCDate)\n * //=> 'Sun Jul 10 2022 00:00:00 GMT+0000 (Coordinated Universal Time)'\n */\nexport function transpose(fromDate, constructor) {\n  const date = constructor instanceof Date ? constructFrom(constructor, 0) : new constructor(0);\n  date.setFullYear(fromDate.getFullYear(), fromDate.getMonth(), fromDate.getDate());\n  date.setHours(fromDate.getHours(), fromDate.getMinutes(), fromDate.getSeconds(), fromDate.getMilliseconds());\n  return date;\n}\n\n// Fallback for modularized imports:\nexport default transpose;", "map": {"version": 3, "names": ["constructFrom", "transpose", "fromDate", "constructor", "date", "Date", "setFullYear", "getFullYear", "getMonth", "getDate", "setHours", "getHours", "getMinutes", "getSeconds", "getMilliseconds"], "sources": ["C:/CMS/webapp/frontend/node_modules/date-fns/transpose.mjs"], "sourcesContent": ["import { constructFrom } from \"./constructFrom.mjs\";\n\n/**\n * @name transpose\n * @category Generic Helpers\n * @summary Transpose the date to the given constructor.\n *\n * @description\n * The function transposes the date to the given constructor. It helps you\n * to transpose the date in the system time zone to say `UTCDate` or any other\n * date extension.\n *\n * @typeParam DateInputType - The input `Date` type derived from the passed argument.\n * @typeParam DateOutputType - The output `Date` type derived from the passed constructor.\n *\n * @param fromDate - The date to use values from\n * @param constructor - The date constructor to use\n *\n * @returns Date transposed to the given constructor\n *\n * @example\n * // Create July 10, 2022 00:00 in locale time zone\n * const date = new Date(2022, 6, 10)\n * //=> 'Sun Jul 10 2022 00:00:00 GMT+0800 (Singapore Standard Time)'\n *\n * @example\n * // Transpose the date to July 10, 2022 00:00 in UTC\n * transpose(date, UTCDate)\n * //=> 'Sun Jul 10 2022 00:00:00 GMT+0000 (Coordinated Universal Time)'\n */\nexport function transpose(fromDate, constructor) {\n  const date =\n    constructor instanceof Date\n      ? constructFrom(constructor, 0)\n      : new constructor(0);\n  date.setFullYear(\n    fromDate.getFullYear(),\n    fromDate.getMonth(),\n    fromDate.getDate(),\n  );\n  date.setHours(\n    fromDate.getHours(),\n    fromDate.getMinutes(),\n    fromDate.getSeconds(),\n    fromDate.getMilliseconds(),\n  );\n  return date;\n}\n\n// Fallback for modularized imports:\nexport default transpose;\n"], "mappings": "AAAA,SAASA,aAAa,QAAQ,qBAAqB;;AAEnD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO,SAASC,SAASA,CAACC,QAAQ,EAAEC,WAAW,EAAE;EAC/C,MAAMC,IAAI,GACRD,WAAW,YAAYE,IAAI,GACvBL,aAAa,CAACG,WAAW,EAAE,CAAC,CAAC,GAC7B,IAAIA,WAAW,CAAC,CAAC,CAAC;EACxBC,IAAI,CAACE,WAAW,CACdJ,QAAQ,CAACK,WAAW,CAAC,CAAC,EACtBL,QAAQ,CAACM,QAAQ,CAAC,CAAC,EACnBN,QAAQ,CAACO,OAAO,CAAC,CACnB,CAAC;EACDL,IAAI,CAACM,QAAQ,CACXR,QAAQ,CAACS,QAAQ,CAAC,CAAC,EACnBT,QAAQ,CAACU,UAAU,CAAC,CAAC,EACrBV,QAAQ,CAACW,UAAU,CAAC,CAAC,EACrBX,QAAQ,CAACY,eAAe,CAAC,CAC3B,CAAC;EACD,OAAOV,IAAI;AACb;;AAEA;AACA,eAAeH,SAAS", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}