{"ast": null, "code": "var _jsxFileName = \"C:\\\\CMS\\\\webapp\\\\frontend\\\\src\\\\pages\\\\cavi\\\\VisualizzaCaviPage.js\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect, useCallback, useRef } from 'react';\nimport { Box, Typography, Paper, Button, Grid, Card, CardContent, Alert, IconButton, Chip, CircularProgress, LinearProgress, Dialog, DialogTitle, DialogContent, DialogActions, Snackbar, FormControl, InputLabel, Select, MenuItem, Stack } from '@mui/material';\nimport InfoIcon from '@mui/icons-material/Info';\nimport { Cable as CableIcon, CheckCircle as CheckCircleIcon, Schedule as ScheduleIcon, Link as LinkIcon, LinkOff as LinkOffIcon, Timeline as TimelineIcon, CheckBox as CheckBoxIcon, CheckBoxOutlineBlank as CheckBoxOutlineBlankIcon, Visibility as VisibilityIcon, Edit as EditIcon, Delete as DeleteIcon, Add as AddIcon, SelectAll as SelectAllIcon, ContentCopy as CopyIcon, Settings as SettingsIcon, Verified as VerifiedIcon } from '@mui/icons-material';\nimport { useNavigate } from 'react-router-dom';\nimport { useAuth } from '../../context/AuthContext';\nimport { useGlobalContext } from '../../context/GlobalContext';\n// import PosaCaviCollegamenti from '../../components/cavi/PosaCaviCollegamenti'; // OBSOLETO: Componente eliminato\nimport caviService from '../../services/caviService';\nimport parcoCaviService from '../../services/parcoCaviService';\nimport CavoForm from '../../components/cavi/CavoForm';\nimport { normalizeInstallationStatus } from '../../utils/validationUtils';\nimport CaviFilterableTable from '../../components/cavi/CaviFilterableTable';\nimport InserisciMetriDialogCompleto from '../../components/cavi/InserisciMetriDialogCompleto';\nimport ModificaBobinaDialogCompleto from '../../components/cavi/ModificaBobinaDialogCompleto';\nimport CollegamentiCavo from '../../components/cavi/CollegamentiCavo';\nimport CertificazioneCaviImproved from '../../components/cavi/CertificazioneCaviImproved';\nimport './CaviPage.css';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst VisualizzaCaviPage = () => {\n  _s();\n  var _caviAttivi$, _caviAttivi$2, _caviAttivi$3;\n  const {\n    isImpersonating,\n    user\n  } = useAuth();\n  const {\n    openEliminaCavoDialog,\n    setOpenEliminaCavoDialog,\n    openModificaCavoDialog,\n    setOpenModificaCavoDialog,\n    openAggiungiCavoDialog,\n    setOpenAggiungiCavoDialog\n  } = useGlobalContext();\n  const navigate = useNavigate();\n  const [cantiereId, setCantiereId] = useState(null);\n  const [cantiereName, setCantiereName] = useState('');\n  const [caviAttivi, setCaviAttivi] = useState([]);\n  const [caviSpare, setCaviSpare] = useState([]);\n  const [loading, setLoading] = useState(true);\n  const [error, setError] = useState(null);\n  // Stato per le notifiche\n  const [notification, setNotification] = useState({\n    open: false,\n    message: '',\n    severity: 'success'\n  });\n  // Rimosso stato viewMode\n\n  // Stato per il dialogo dei dettagli del cavo\n  const [selectedCavo, setSelectedCavo] = useState(null);\n  const [detailsDialogOpen, setDetailsDialogOpen] = useState(false);\n\n  // Stati per la selezione dei cavi\n  const [selectionEnabled, setSelectionEnabled] = useState(false);\n  const [selectedCaviAttivi, setSelectedCaviAttivi] = useState([]);\n  const [selectedCaviSpare, setSelectedCaviSpare] = useState([]);\n\n  // Stati per i dialoghi di azione sui pulsanti stato\n  const [inserisciMetriDialog, setInserisciMetriDialog] = useState({\n    open: false,\n    cavo: null,\n    loading: false\n  });\n  const [collegamentiDialog, setCollegamentiDialog] = useState({\n    open: false,\n    cavo: null,\n    loading: false\n  });\n  const [modificaBobinaDialog, setModificaBobinaDialog] = useState({\n    open: false,\n    cavo: null,\n    loading: false\n  });\n\n  // Ref per il componente CertificazioneCavi\n  const certificazioneRef = useRef(null);\n\n  // Stati per statistiche avanzate\n  const [statistics, setStatistics] = useState({\n    totaleCavi: 0,\n    caviInstallati: 0,\n    caviDaInstallare: 0,\n    caviInCorso: 0,\n    caviCollegati: 0,\n    caviNonCollegati: 0,\n    caviCertificati: 0,\n    caviNonCertificati: 0,\n    percentualeInstallazione: 0,\n    percentualeCollegamento: 0,\n    percentualeCertificazione: 0,\n    iap: 0,\n    // Indice di Avanzamento Ponderato\n    metriTotali: 0,\n    metriInstallati: 0,\n    metriRimanenti: 0\n  });\n\n  // Stato per la gestione delle revisioni\n  const [revisioniDisponibili, setRevisioniDisponibili] = useState([]);\n  const [revisioneSelezionata, setRevisioneSelezionata] = useState('');\n  const [revisioneCorrente, setRevisioneCorrente] = useState('');\n\n  // Rimosso stato per il debug\n\n  // Funzione per calcolare l'Indice di Avanzamento Ponderato (IAP)\n  const calculateIAP = (nTot, nInst, nColl, nCert) => {\n    // Pesi per le fasi del progetto\n    const Wp = 2.0; // Peso fase Posa\n    const Wc = 1.5; // Peso fase Collegamento\n    const Wz = 0.5; // Peso fase Certificazione\n\n    // Se non ci sono cavi, ritorna 0\n    if (nTot === 0) return 0;\n\n    // Calcolo del numeratore (Sforzo Completato)\n    const sforzoSoloInstallati = (nInst - nColl) * Wp;\n    const sforzoSoloCollegati = (nColl - nCert) * (Wp + Wc);\n    const sforzoCertificati = nCert * (Wp + Wc + Wz);\n    const numeratore = sforzoSoloInstallati + sforzoSoloCollegati + sforzoCertificati;\n\n    // Calcolo del denominatore (Sforzo Massimo Previsto)\n    const denominatore = nTot * (Wp + Wc + Wz);\n\n    // Calcolo finale dell'IAP in percentuale\n    const iap = numeratore / denominatore * 100;\n    console.log('Calcolo IAP:', {\n      nTot,\n      nInst,\n      nColl,\n      nCert,\n      pesi: {\n        Wp,\n        Wc,\n        Wz\n      },\n      sforzoSoloInstallati,\n      sforzoSoloCollegati,\n      sforzoCertificati,\n      numeratore,\n      denominatore,\n      iap: Math.round(iap * 100) / 100\n    });\n    return Math.round(iap * 100) / 100; // Arrotonda a 2 decimali\n  };\n\n  // Funzione per calcolare le statistiche avanzate\n  const calculateStatistics = (caviAttiviData, caviSpareData) => {\n    const tuttiCavi = [...(caviAttiviData || []), ...(caviSpareData || [])];\n    if (tuttiCavi.length === 0) {\n      console.log('Nessun cavo disponibile per il calcolo delle statistiche');\n      return;\n    }\n    console.log('Calcolo statistiche con dati:', {\n      caviAttivi: (caviAttiviData === null || caviAttiviData === void 0 ? void 0 : caviAttiviData.length) || 0,\n      caviSpare: (caviSpareData === null || caviSpareData === void 0 ? void 0 : caviSpareData.length) || 0,\n      totale: tuttiCavi.length\n    });\n    const totaleCavi = tuttiCavi.length;\n\n    // Calcola stati di installazione\n    const caviInstallati = tuttiCavi.filter(cavo => cavo.stato_installazione === 'Installato' || cavo.stato_installazione === 'INSTALLATO' || cavo.stato_installazione === 'POSATO').length;\n    const caviDaInstallare = tuttiCavi.filter(cavo => cavo.stato_installazione === 'Da installare' || cavo.stato_installazione === 'DA_INSTALLARE').length;\n    const caviInCorso = tuttiCavi.filter(cavo => cavo.stato_installazione === 'In corso' || cavo.stato_installazione === 'IN_CORSO').length;\n\n    // Calcola stati di collegamento\n    const caviCollegati = tuttiCavi.filter(cavo => cavo.collegamenti === 3 && cavo.responsabile_partenza && cavo.responsabile_arrivo).length;\n    const caviNonCollegati = totaleCavi - caviCollegati;\n\n    // Calcola certificazioni basandosi sul campo 'certificato' dei cavi\n    const caviCertificati = tuttiCavi.filter(cavo => cavo.certificato === true || cavo.certificato === 'true').length;\n\n    // Calcola l'Indice di Avanzamento Ponderato (IAP)\n    const iap = calculateIAP(totaleCavi, caviInstallati, caviCollegati, caviCertificati);\n\n    // Calcola percentuali tradizionali per confronto\n    const percentualeInstallazione = iap; // Sostituito con IAP\n    const percentualeCollegamento = totaleCavi > 0 ? Math.round(caviCollegati / totaleCavi * 100) : 0;\n\n    // Calcola metri\n    const metriTotali = tuttiCavi.reduce((sum, cavo) => sum + (parseFloat(cavo.metri_teorici) || 0), 0);\n    const metriInstallati = tuttiCavi.filter(cavo => cavo.stato_installazione === 'Installato' || cavo.stato_installazione === 'INSTALLATO' || cavo.stato_installazione === 'POSATO').reduce((sum, cavo) => sum + (parseFloat(cavo.metratura_reale) || parseFloat(cavo.metri_teorici) || 0), 0);\n    const metriRimanenti = metriTotali - metriInstallati;\n    const caviNonCertificati = totaleCavi - caviCertificati;\n    const percentualeCertificazione = totaleCavi > 0 ? Math.round(caviCertificati / totaleCavi * 100) : 0;\n    const newStatistics = {\n      totaleCavi,\n      caviInstallati,\n      caviDaInstallare,\n      caviInCorso,\n      caviCollegati,\n      caviNonCollegati,\n      caviCertificati,\n      caviNonCertificati,\n      percentualeInstallazione,\n      percentualeCollegamento,\n      percentualeCertificazione,\n      iap,\n      // Indice di Avanzamento Ponderato\n      metriTotali: Math.round(metriTotali),\n      metriInstallati: Math.round(metriInstallati),\n      metriRimanenti: Math.round(metriRimanenti)\n    };\n    console.log('Nuove statistiche calcolate:', newStatistics);\n    setStatistics(newStatistics);\n  };\n\n  // Funzione per caricare gli stati di installazione disponibili\n  const loadStatiInstallazione = () => {\n    // Usa i valori dell'enum StatoInstallazione\n    setStatiInstallazione(['Installato', 'Da installare', 'In corso']);\n  };\n\n  // Funzione per caricare le revisioni disponibili\n  const loadRevisioni = async cantiereIdToUse => {\n    try {\n      console.log('Caricamento revisioni per cantiere:', cantiereIdToUse);\n\n      // Carica la revisione corrente\n      const revisioneCorrenteData = await caviService.getRevisioneCorrente(cantiereIdToUse);\n      console.log('Revisione corrente:', revisioneCorrenteData);\n      setRevisioneCorrente(revisioneCorrenteData.revisione_corrente);\n\n      // Carica tutte le revisioni disponibili\n      const revisioniData = await caviService.getRevisioniDisponibili(cantiereIdToUse);\n      console.log('Revisioni disponibili:', revisioniData);\n      setRevisioniDisponibili(revisioniData.revisioni || []);\n\n      // LOGICA REVISIONI: La revisione corrente è quella di default\n      // Non impostiamo una revisione selezionata, così il sistema usa automaticamente la corrente\n      console.log('Logica revisioni: usando revisione corrente di default');\n    } catch (error) {\n      console.error('Errore nel caricamento delle revisioni:', error);\n    }\n  };\n\n  // Funzione per gestire il cambio di revisione\n  const handleRevisioneChange = event => {\n    const nuovaRevisione = event.target.value;\n\n    // LOGICA REVISIONI:\n    // - Se vuoto o \"corrente\" -> usa revisione corrente (non specificare parametro)\n    // - Se specifica -> usa quella revisione per visualizzazione storica\n    if (nuovaRevisione === '' || nuovaRevisione === 'corrente') {\n      setRevisioneSelezionata('');\n      console.log('Passaggio a revisione corrente (default)');\n    } else {\n      setRevisioneSelezionata(nuovaRevisione);\n      console.log('Passaggio a revisione storica:', nuovaRevisione);\n    }\n  };\n\n  // Stato per filtri e ordinamento\n  const [filters, setFilters] = useState({\n    stato_installazione: '',\n    tipologia: '',\n    sort_by: '',\n    sort_order: 'asc'\n  });\n\n  // Opzioni per i filtri\n  const [statiInstallazione, setStatiInstallazione] = useState([]);\n  const [tipologieCavi, setTipologieCavi] = useState([]);\n\n  // Rimossa funzione di debug\n\n  // Funzione per caricare i cavi\n  // Il parametro silentLoading permette di evitare di mostrare lo stato di caricamento\n  const fetchCavi = async (silentLoading = false) => {\n    try {\n      if (!silentLoading) {\n        setLoading(true);\n      }\n      console.log('Caricamento cavi per cantiere:', cantiereId);\n\n      // Verifica che cantiereId sia valido\n      if (!cantiereId) {\n        console.error('fetchCavi: cantiereId non valido:', cantiereId);\n        setError('ID cantiere non valido o mancante. Ricarica la pagina.');\n        setLoading(false);\n        return;\n      }\n\n      // Recupera il cantiereId dal localStorage come fallback\n      let cantiereIdToUse = cantiereId;\n      if (!cantiereIdToUse) {\n        cantiereIdToUse = localStorage.getItem('selectedCantiereId');\n        console.log('Usando cantiereId dal localStorage:', cantiereIdToUse);\n        if (!cantiereIdToUse) {\n          console.error('Impossibile trovare un ID cantiere valido');\n          setError('ID cantiere non trovato. Ricarica la pagina.');\n          setLoading(false);\n          return;\n        }\n      }\n\n      // Carica i cavi attivi\n      console.log('Caricamento cavi attivi (tipo_cavo=0)...');\n      let attivi = [];\n      try {\n        attivi = await caviService.getCavi(cantiereIdToUse, 0, filters);\n        console.log('Cavi attivi caricati:', attivi ? attivi.length : 0);\n      } catch (attiviError) {\n        console.error('Errore nel caricamento dei cavi attivi:', attiviError);\n        // Continua con un array vuoto\n        attivi = [];\n      }\n\n      // Verifica se ci sono cavi con modificato_manualmente = 3 tra i cavi attivi\n      if (attivi && attivi.length > 0) {\n        const caviSpareTraAttivi = attivi.filter(cavo => cavo.modificato_manualmente === 3);\n        if (caviSpareTraAttivi.length > 0) {\n          console.error('ERRORE: Trovati cavi con modificato_manualmente = 3 tra i cavi attivi:', caviSpareTraAttivi);\n        }\n      }\n      setCaviAttivi(attivi || []);\n\n      // Carica i cavi SPARE con la nuova funzione dedicata\n      let spare = [];\n      try {\n        console.log('Caricamento cavi SPARE con funzione dedicata...');\n        spare = await caviService.getCaviSpare(cantiereIdToUse);\n        console.log('Cavi SPARE caricati con funzione dedicata:', spare ? spare.length : 0);\n        if (spare && spare.length > 0) {\n          console.log('Primo cavo SPARE:', spare[0]);\n        }\n      } catch (spareError) {\n        console.error('Errore nel caricamento dei cavi SPARE con funzione dedicata:', spareError);\n        // Se fallisce, prova con il metodo standard\n        try {\n          console.log('Tentativo con metodo standard...');\n          spare = await caviService.getCavi(cantiereIdToUse, 3);\n          console.log('Cavi SPARE caricati con metodo standard:', spare ? spare.length : 0);\n        } catch (standardError) {\n          console.error('Errore anche con metodo standard:', standardError);\n          // Continua con un array vuoto\n          spare = [];\n        }\n      }\n      setCaviSpare(spare || []);\n\n      // Se siamo arrivati qui, rimuovi eventuali messaggi di errore precedenti\n      setError('');\n    } catch (error) {\n      console.error('Errore generale nel caricamento dei cavi:', error);\n      setError(`Errore nel caricamento dei cavi: ${error.message || 'Errore sconosciuto'}`);\n\n      // Prova a ricaricare la pagina dopo un ritardo se l'errore persiste\n      setTimeout(() => {\n        // Verifica se siamo ancora in errore\n        if (document.body.textContent.includes('Errore nel caricamento dei cavi')) {\n          console.log('Errore persistente, tentativo di ricaricamento della pagina...');\n          window.location.reload();\n        }\n      }, 5000); // 5 secondi di ritardo\n    } finally {\n      if (!silentLoading) {\n        setLoading(false);\n      }\n    }\n  };\n\n  // Carica i dati del cantiere e dei cavi\n  useEffect(() => {\n    // Carica gli stati di installazione all'avvio\n    loadStatiInstallazione();\n    const fetchData = async () => {\n      try {\n        console.log('Inizializzazione VisualizzaCaviPage...');\n\n        // Verifica che l'utente sia autenticato\n        const token = localStorage.getItem('token');\n        console.log('Token presente:', !!token);\n        if (!token) {\n          setError('Sessione scaduta. Effettua nuovamente il login.');\n          setLoading(false);\n          return;\n        }\n\n        // Recupera l'ID del cantiere selezionato dal localStorage\n        let selectedCantiereId = localStorage.getItem('selectedCantiereId');\n        let selectedCantiereName = localStorage.getItem('selectedCantiereName');\n        console.log('Cantiere selezionato dal localStorage:', {\n          selectedCantiereId,\n          selectedCantiereName\n        });\n        console.log('Dati utente:', user);\n\n        // Stampa tutti i dati nel localStorage per debug\n        console.log('DEBUG - Tutti i dati nel localStorage:');\n        for (let i = 0; i < localStorage.length; i++) {\n          const key = localStorage.key(i);\n          console.log(`${key}: ${localStorage.getItem(key)}`);\n        }\n\n        // SOLUZIONE DIRETTA: Ottieni l'ID del cantiere direttamente dal token JWT\n        if ((user === null || user === void 0 ? void 0 : user.role) === 'cantieri_user') {\n          console.log('Utente cantiere rilevato, tentativo di recupero ID cantiere dai dati utente');\n\n          // Verifica se l'utente ha un ID cantiere nei dati utente\n          if (user.cantiere_id) {\n            console.log('Trovato ID cantiere nei dati utente:', user.cantiere_id);\n            selectedCantiereId = user.cantiere_id.toString();\n            selectedCantiereName = user.cantiere_name || `Cantiere ${user.cantiere_id}`;\n\n            // Salva l'ID e il nome del cantiere nel localStorage\n            localStorage.setItem('selectedCantiereId', selectedCantiereId);\n            localStorage.setItem('selectedCantiereName', selectedCantiereName);\n            console.log('Salvato ID cantiere nel localStorage:', selectedCantiereId);\n          } else {\n            // Tentativo di recupero dal token JWT\n            try {\n              console.log('Tentativo di decodifica del token JWT per recuperare l\\'ID cantiere');\n              const token = localStorage.getItem('token');\n              if (token) {\n                // Decodifica il token JWT (senza verifica della firma)\n                const base64Url = token.split('.')[1];\n                const base64 = base64Url.replace(/-/g, '+').replace(/_/g, '/');\n                const jsonPayload = decodeURIComponent(atob(base64).split('').map(c => {\n                  return '%' + ('00' + c.charCodeAt(0).toString(16)).slice(-2);\n                }).join(''));\n                const payload = JSON.parse(jsonPayload);\n                console.log('Payload del token JWT:', payload);\n                if (payload.cantiere_id) {\n                  console.log('Trovato ID cantiere nel token JWT:', payload.cantiere_id);\n                  selectedCantiereId = payload.cantiere_id.toString();\n                  // Usa un nome generico se non disponibile\n                  selectedCantiereName = `Cantiere ${payload.cantiere_id}`;\n\n                  // Salva l'ID e il nome del cantiere nel localStorage\n                  localStorage.setItem('selectedCantiereId', selectedCantiereId);\n                  localStorage.setItem('selectedCantiereName', selectedCantiereName);\n                  console.log('Salvato ID cantiere nel localStorage:', selectedCantiereId);\n                }\n              }\n            } catch (e) {\n              console.error('Errore durante la decodifica del token JWT:', e);\n            }\n          }\n        }\n\n        // SOLUZIONE TEMPORANEA: Se ancora non abbiamo un ID cantiere, usa un valore hardcoded per debug\n        if (!selectedCantiereId || selectedCantiereId === 'undefined' || selectedCantiereId === 'null') {\n          console.warn('ATTENZIONE: Nessun ID cantiere trovato, utilizzo valore hardcoded per debug');\n          // Usa il primo cantiere disponibile (questo è solo per debug)\n          selectedCantiereId = '1'; // Sostituisci con un ID cantiere valido nel tuo database\n          selectedCantiereName = 'Cantiere Debug';\n\n          // Salva l'ID e il nome del cantiere nel localStorage\n          localStorage.setItem('selectedCantiereId', selectedCantiereId);\n          localStorage.setItem('selectedCantiereName', selectedCantiereName);\n          console.log('Salvato ID cantiere hardcoded nel localStorage:', selectedCantiereId);\n        }\n\n        // Verifica finale\n        if (!selectedCantiereId) {\n          setError('Nessun cantiere selezionato. Torna alla pagina dei cantieri.');\n          setLoading(false);\n          return;\n        }\n\n        // Verifica che l'ID del cantiere sia un numero valido\n        const cantiereIdNum = parseInt(selectedCantiereId, 10);\n        console.log('ID cantiere convertito a numero:', cantiereIdNum);\n        if (isNaN(cantiereIdNum)) {\n          setError(`ID cantiere non valido: ${selectedCantiereId}. Torna alla pagina dei cantieri.`);\n          setLoading(false);\n          return;\n        }\n\n        // Usa il numero convertito, non la stringa\n        setCantiereId(cantiereIdNum);\n        setCantiereName(selectedCantiereName || `Cantiere ${cantiereIdNum}`);\n\n        // Carica le revisioni disponibili\n        await loadRevisioni(cantiereIdNum);\n\n        // Carica i cavi attivi con gestione degli errori migliorata\n        console.log('Caricamento cavi attivi per cantiere:', cantiereIdNum);\n        try {\n          // Imposta un timeout per evitare che la richiesta rimanga bloccata\n          const timeoutPromise = new Promise((_, reject) => {\n            setTimeout(() => reject(new Error('Timeout durante il caricamento dei cavi attivi')), 30000); // Aumentato a 30 secondi\n          });\n\n          // Esegui la richiesta con un timeout di sicurezza e applica i filtri\n          console.log('Iniziando chiamata API per cavi attivi con filtri:', filters);\n          const caviPromise = caviService.getCavi(cantiereIdNum, 0, filters);\n          const attivi = await Promise.race([caviPromise, timeoutPromise]);\n          console.log('Cavi attivi caricati:', attivi);\n          console.log('Numero di cavi attivi trovati:', attivi ? attivi.length : 0);\n          if (attivi && attivi.length > 0) {\n            console.log('Primo cavo attivo:', attivi[0]);\n          } else {\n            console.warn('Nessun cavo attivo trovato per il cantiere', cantiereIdNum);\n          }\n          setCaviAttivi(attivi || []);\n\n          // Calcola le statistiche dopo aver caricato i cavi attivi\n          calculateStatistics(attivi || [], caviSpare);\n        } catch (caviError) {\n          console.error('Errore nel caricamento dei cavi attivi:', caviError);\n          console.error('Dettagli errore cavi attivi:', {\n            message: caviError.message,\n            status: caviError.status,\n            data: caviError.data,\n            stack: caviError.stack,\n            code: caviError.code,\n            name: caviError.name,\n            response: caviError.response ? {\n              status: caviError.response.status,\n              statusText: caviError.response.statusText,\n              data: caviError.response.data\n            } : 'No response'\n          });\n\n          // Non interrompere il flusso, continua con i cavi spare\n          setCaviAttivi([]);\n          console.warn('Continuazione del flusso dopo errore nei cavi attivi');\n\n          // Aggiungi un messaggio di errore visibile all'utente\n          setError(`Errore nel caricamento dei cavi attivi: ${caviError.message}. Controlla la console per maggiori dettagli.`);\n        }\n\n        // Carica i cavi spare con gestione degli errori migliorata\n        console.log('Caricamento cavi spare per cantiere:', cantiereIdNum);\n        try {\n          // Imposta un timeout per evitare che la richiesta rimanga bloccata\n          const timeoutPromise = new Promise((_, reject) => {\n            setTimeout(() => reject(new Error('Timeout durante il caricamento dei cavi spare')), 30000); // Aumentato a 30 secondi\n          });\n\n          // Esegui la richiesta con un timeout di sicurezza\n          console.log('Iniziando chiamata API per cavi spare...');\n          // Non applichiamo i filtri ai cavi spare, solo agli attivi\n          const sparePromise = caviService.getCavi(cantiereIdNum, 3);\n          const spare = await Promise.race([sparePromise, timeoutPromise]);\n          console.log('Cavi spare caricati:', spare);\n          console.log('Numero di cavi spare trovati:', spare ? spare.length : 0);\n          if (spare && spare.length > 0) {\n            console.log('Primo cavo spare:', spare[0]);\n          } else {\n            console.warn('Nessun cavo spare trovato per il cantiere', cantiereIdNum);\n          }\n          setCaviSpare(spare || []);\n\n          // Calcola le statistiche dopo aver caricato i cavi spare\n          calculateStatistics(caviAttivi, spare || []);\n        } catch (spareError) {\n          console.error('Errore nel caricamento dei cavi spare:', spareError);\n          console.error('Dettagli errore cavi spare:', {\n            message: spareError.message,\n            status: spareError.status,\n            data: spareError.data,\n            stack: spareError.stack,\n            code: spareError.code,\n            name: spareError.name,\n            response: spareError.response ? {\n              status: spareError.response.status,\n              statusText: spareError.response.statusText,\n              data: spareError.response.data\n            } : 'No response'\n          });\n\n          // Non interrompere il flusso, imposta un array vuoto\n          setCaviSpare([]);\n\n          // Aggiungi un messaggio di errore visibile all'utente se non c'è già un errore per i cavi attivi\n          if (!error) {\n            setError(`Errore nel caricamento dei cavi spare: ${spareError.message}. Controlla la console per maggiori dettagli.`);\n          }\n        }\n\n        // Se siamo arrivati qui, almeno abbiamo caricato l'interfaccia di base\n        setLoading(false);\n      } catch (err) {\n        var _err$response, _err$response2, _err$response3, _err$response4, _err$response5, _err$response5$data;\n        console.error('Errore nel caricamento dei cavi:', err);\n        console.error('Dettagli errore generale:', {\n          message: err.message,\n          status: err.status || ((_err$response = err.response) === null || _err$response === void 0 ? void 0 : _err$response.status),\n          data: err.data || ((_err$response2 = err.response) === null || _err$response2 === void 0 ? void 0 : _err$response2.data),\n          stack: err.stack\n        });\n\n        // Estrai il messaggio di errore dettagliato\n        let errorMessage = 'Errore sconosciuto';\n        if (err.message && err.message.includes('ID cantiere non valido')) {\n          errorMessage = err.message;\n        } else if (err.status === 401 || err.status === 403 || ((_err$response3 = err.response) === null || _err$response3 === void 0 ? void 0 : _err$response3.status) === 401 || ((_err$response4 = err.response) === null || _err$response4 === void 0 ? void 0 : _err$response4.status) === 403) {\n          errorMessage = 'Sessione scaduta o non autorizzata. Effettua nuovamente il login.';\n        } else if ((_err$response5 = err.response) !== null && _err$response5 !== void 0 && (_err$response5$data = _err$response5.data) !== null && _err$response5$data !== void 0 && _err$response5$data.detail) {\n          // Estrai il messaggio di errore dettagliato dall'API\n          errorMessage = `Errore API: ${err.response.data.detail}`;\n        } else if (err.code === 'ERR_NETWORK') {\n          // Errore di rete\n          errorMessage = 'Network Error. Verifica che il backend sia in esecuzione e accessibile.';\n        } else if (err.message) {\n          errorMessage = err.message;\n        }\n        setError(`Impossibile caricare i cavi: ${errorMessage}. Riprova più tardi.`);\n\n        // Imposta array vuoti per evitare errori di rendering\n        setCaviAttivi([]);\n        setCaviSpare([]);\n      } finally {\n        setLoading(false);\n      }\n    };\n    fetchData();\n  }, [filters]); // Ricarica i dati quando cambiano i filtri\n\n  // I filtri sono ora gestiti dal componente CaviFilterableTable\n\n  // Funzione per aprire il dialogo dei dettagli del cavo\n  const handleOpenDetails = cavo => {\n    setSelectedCavo(cavo);\n    setDetailsDialogOpen(true);\n  };\n\n  // Funzione per chiudere il dialogo dei dettagli del cavo\n  const handleCloseDetails = () => {\n    setDetailsDialogOpen(false);\n    setSelectedCavo(null);\n  };\n\n  // Funzione per chiudere la notifica\n  const handleCloseNotification = () => {\n    setNotification(prev => ({\n      ...prev,\n      open: false\n    }));\n  };\n\n  // Funzione per mostrare una notifica\n  const showNotification = (message, severity = 'success') => {\n    setNotification({\n      open: true,\n      message,\n      severity\n    });\n  };\n\n  // Funzioni per gestire la selezione dei cavi\n  const handleSelectionToggle = () => {\n    setSelectionEnabled(!selectionEnabled);\n    // Pulisci le selezioni quando si disabilita la modalità selezione\n    if (selectionEnabled) {\n      setSelectedCaviAttivi([]);\n      setSelectedCaviSpare([]);\n    }\n  };\n  const handleCaviAttiviSelectionChange = selectedIds => {\n    setSelectedCaviAttivi(selectedIds);\n  };\n  const handleCaviSpareSelectionChange = selectedIds => {\n    setSelectedCaviSpare(selectedIds);\n  };\n\n  // Funzione per ottenere tutti i cavi selezionati\n  const getAllSelectedCavi = () => {\n    const selectedAttiviCavi = caviAttivi.filter(cavo => selectedCaviAttivi.includes(cavo.id_cavo));\n    const selectedSpareCavi = caviSpare.filter(cavo => selectedCaviSpare.includes(cavo.id_cavo));\n    return [...selectedAttiviCavi, ...selectedSpareCavi];\n  };\n\n  // Funzione per ottenere il conteggio totale dei cavi selezionati\n  const getTotalSelectedCount = () => {\n    return selectedCaviAttivi.length + selectedCaviSpare.length;\n  };\n\n  // Funzioni per gestire le azioni del menu contestuale\n  const handleContextMenuAction = (cavo, action) => {\n    console.log('Azione menu contestuale:', action, 'per cavo:', cavo);\n    switch (action) {\n      case 'view_details':\n        handleOpenDetails(cavo);\n        break;\n      case 'edit':\n        // Funzionalità di modifica cavo non ancora implementata\n        showNotification('Funzionalità di modifica cavo in sviluppo', 'info');\n        break;\n      case 'delete':\n        // Funzionalità di eliminazione cavo non ancora implementata\n        showNotification('Funzionalità di eliminazione cavo in sviluppo', 'info');\n        break;\n      case 'select':\n        if (caviAttivi.some(c => c.id_cavo === cavo.id_cavo)) {\n          // È un cavo attivo\n          const isSelected = selectedCaviAttivi.includes(cavo.id_cavo);\n          if (isSelected) {\n            setSelectedCaviAttivi(prev => prev.filter(id => id !== cavo.id_cavo));\n          } else {\n            setSelectedCaviAttivi(prev => [...prev, cavo.id_cavo]);\n          }\n        } else {\n          // È un cavo spare\n          const isSelected = selectedCaviSpare.includes(cavo.id_cavo);\n          if (isSelected) {\n            setSelectedCaviSpare(prev => prev.filter(id => id !== cavo.id_cavo));\n          } else {\n            setSelectedCaviSpare(prev => [...prev, cavo.id_cavo]);\n          }\n        }\n        // Abilita automaticamente la modalità selezione se non è già attiva\n        if (!selectionEnabled) {\n          setSelectionEnabled(true);\n        }\n        break;\n      case 'copy_id':\n        navigator.clipboard.writeText(cavo.id_cavo);\n        showNotification(`ID cavo ${cavo.id_cavo} copiato negli appunti`, 'success');\n        break;\n      case 'copy_details':\n        const details = `ID: ${cavo.id_cavo}\\nTipologia: ${cavo.tipologia}\\nSezione: ${cavo.sezione}\\nMetri: ${cavo.metri_teorici}`;\n        navigator.clipboard.writeText(details);\n        showNotification('Dettagli cavo copiati negli appunti', 'success');\n        break;\n      case 'add_new':\n        // Funzionalità di aggiunta cavo non ancora implementata\n        showNotification('Funzionalità di aggiunta cavo in sviluppo', 'info');\n        break;\n      default:\n        console.warn('Azione non riconosciuta:', action);\n    }\n  };\n\n  // Definizione degli elementi del menu contestuale\n  const getContextMenuItems = cavo => {\n    const isSelected = caviAttivi.some(c => c.id_cavo === (cavo === null || cavo === void 0 ? void 0 : cavo.id_cavo)) ? selectedCaviAttivi.includes(cavo === null || cavo === void 0 ? void 0 : cavo.id_cavo) : selectedCaviSpare.includes(cavo === null || cavo === void 0 ? void 0 : cavo.id_cavo);\n    return [{\n      type: 'header',\n      label: `Cavo ${(cavo === null || cavo === void 0 ? void 0 : cavo.id_cavo) || ''}`\n    }, {\n      id: 'view_details',\n      label: 'Visualizza Dettagli',\n      icon: /*#__PURE__*/_jsxDEV(VisibilityIcon, {\n        fontSize: \"small\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 797,\n        columnNumber: 15\n      }, this),\n      action: 'view_details',\n      onClick: handleContextMenuAction\n    }, {\n      type: 'divider'\n    }, {\n      id: 'edit',\n      label: 'Modifica',\n      icon: /*#__PURE__*/_jsxDEV(EditIcon, {\n        fontSize: \"small\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 807,\n        columnNumber: 15\n      }, this),\n      action: 'edit',\n      onClick: handleContextMenuAction,\n      color: 'primary'\n    }, {\n      id: 'delete',\n      label: 'Elimina',\n      icon: /*#__PURE__*/_jsxDEV(DeleteIcon, {\n        fontSize: \"small\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 815,\n        columnNumber: 15\n      }, this),\n      action: 'delete',\n      onClick: handleContextMenuAction,\n      color: 'error'\n    }, {\n      type: 'divider'\n    }, {\n      id: 'add_new',\n      label: 'Aggiungi nuovo cavo',\n      icon: /*#__PURE__*/_jsxDEV(AddIcon, {\n        fontSize: \"small\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 826,\n        columnNumber: 15\n      }, this),\n      action: 'add_new',\n      onClick: handleContextMenuAction,\n      color: 'success'\n    }, {\n      type: 'divider'\n    }, {\n      id: 'select',\n      label: isSelected ? 'Deseleziona' : 'Seleziona',\n      icon: /*#__PURE__*/_jsxDEV(SelectAllIcon, {\n        fontSize: \"small\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 837,\n        columnNumber: 15\n      }, this),\n      action: 'select',\n      onClick: handleContextMenuAction,\n      color: isSelected ? 'warning' : 'success'\n    }, {\n      type: 'divider'\n    }, {\n      id: 'copy_id',\n      label: 'Copia ID',\n      icon: /*#__PURE__*/_jsxDEV(CopyIcon, {\n        fontSize: \"small\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 848,\n        columnNumber: 15\n      }, this),\n      action: 'copy_id',\n      onClick: handleContextMenuAction,\n      shortcut: 'Ctrl+C'\n    }, {\n      id: 'copy_details',\n      label: 'Copia Dettagli',\n      icon: /*#__PURE__*/_jsxDEV(CopyIcon, {\n        fontSize: \"small\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 856,\n        columnNumber: 15\n      }, this),\n      action: 'copy_details',\n      onClick: handleContextMenuAction,\n      description: 'Copia ID, tipologia, sezione e metri'\n    }];\n  };\n\n  // Funzioni per gestire le azioni sui pulsanti stato\n  const handleStatusAction = async (cavo, actionType, actionLabel) => {\n    console.log('🎯 CLICK PULSANTE STATO RILEVATO!');\n    console.log('Azione pulsante stato:', actionType, 'per cavo:', cavo);\n    console.log('Action label:', actionLabel);\n    if (actionType === 'insert_meters') {\n      // Apri il dialogo per inserire i metri posati\n      setInserisciMetriDialog({\n        open: true,\n        cavo: cavo,\n        loading: false\n      });\n    } else if (actionType === 'modify_reel') {\n      // Apri il dialog completo per modificare la bobina con cavo preselezionato\n      console.log('Apertura dialog modifica bobina per cavo:', cavo.id_cavo);\n      setModificaBobinaDialog({\n        open: true,\n        cavo: cavo,\n        loading: false\n      });\n    } else if (actionType === 'connect_cable' || actionType === 'connect_arrival' || actionType === 'connect_departure' || actionType === 'disconnect_cable' || actionType === 'manage_connections') {\n      // Verifica se il cavo è installato\n      if (cavo.stato_installazione !== 'Installato') {\n        console.log('Azione collegamenti ignorata per cavo non installato:', cavo.id_cavo);\n        showNotification('I collegamenti sono disponibili solo per cavi installati', 'info');\n        return;\n      }\n\n      // Cavo installato - apri il popup per gestire i collegamenti\n      console.log('Apertura popup collegamenti per cavo installato:', cavo.id_cavo, 'azione:', actionType);\n\n      // Usa setTimeout per evitare conflitti di stato\n      setTimeout(() => {\n        setCollegamentiDialog({\n          open: true,\n          cavo: cavo,\n          loading: false\n        });\n      }, 50);\n    } else if (actionType === 'create_certificate') {\n      // Apri il dialog per creare una certificazione usando il componente esistente\n      console.log('Apertura dialog creazione certificazione per cavo:', cavo.id_cavo);\n\n      // Usa il componente CertificazioneCavi esistente con tutti i prerequisiti\n      if (certificazioneRef.current) {\n        certificazioneRef.current.createCertificationForCavo(cavo);\n      }\n    } else if (actionType === 'view_certificate') {\n      // Apri il dialog per visualizzare la certificazione esistente\n      console.log('Apertura dialog visualizzazione certificazione per cavo:', cavo.id_cavo);\n\n      // Usa il componente CertificazioneCavi esistente per visualizzare\n      if (certificazioneRef.current) {\n        certificazioneRef.current.viewCertificationForCavo(cavo);\n      }\n    } else if (actionType === 'generate_pdf') {\n      // Genera il PDF del certificato per il cavo\n      console.log('Generazione PDF certificato per cavo:', cavo.id_cavo);\n\n      // Usa il componente CertificazioneCavi esistente per generare il PDF\n      if (certificazioneRef.current) {\n        certificazioneRef.current.generatePdfForCavo(cavo);\n      }\n    }\n  };\n\n  // Funzioni per gestire i callback del dialog modifica bobina\n  const handleModificaBobinaSuccess = message => {\n    showNotification(message, 'success');\n    // Ricarica i dati per aggiornare lo stato\n    setTimeout(() => fetchCavi(true), 500);\n  };\n  const handleModificaBobinaError = message => {\n    showNotification(message, 'error');\n  };\n\n  // Funzioni per chiudere i dialoghi\n  const handleCloseInserisciMetri = () => {\n    if (!inserisciMetriDialog.loading) {\n      setInserisciMetriDialog({\n        open: false,\n        cavo: null,\n        loading: false\n      });\n    }\n  };\n  const handleCloseModificaBobina = () => {\n    if (!modificaBobinaDialog.loading) {\n      setModificaBobinaDialog({\n        open: false,\n        cavo: null,\n        loading: false\n      });\n    }\n  };\n  const handleCloseCollegamenti = useCallback(() => {\n    if (!collegamentiDialog.loading) {\n      setCollegamentiDialog(prev => ({\n        ...prev,\n        open: false\n      }));\n    }\n  }, [collegamentiDialog.loading]);\n\n  // Nessuna funzione di navigazione necessaria, tutto è gestito dal menu principale\n\n  // Dashboard minimal con statistiche essenziali per visualizzazione cavi\n  const renderDashboard = () => /*#__PURE__*/_jsxDEV(Paper, {\n    sx: {\n      p: 2,\n      mb: 3,\n      bgcolor: 'grey.50'\n    },\n    children: /*#__PURE__*/_jsxDEV(Stack, {\n      direction: \"row\",\n      spacing: 4,\n      alignItems: \"center\",\n      justifyContent: \"space-between\",\n      flexWrap: \"wrap\",\n      children: [/*#__PURE__*/_jsxDEV(Stack, {\n        direction: \"row\",\n        alignItems: \"center\",\n        spacing: 1,\n        children: [/*#__PURE__*/_jsxDEV(CableIcon, {\n          color: \"primary\",\n          fontSize: \"small\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 979,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Box, {\n          children: [/*#__PURE__*/_jsxDEV(Typography, {\n            variant: \"h6\",\n            fontWeight: \"bold\",\n            sx: {\n              lineHeight: 1\n            },\n            children: statistics.totaleCavi\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 981,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Typography, {\n            variant: \"caption\",\n            color: \"text.secondary\",\n            children: \"Totale\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 984,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 980,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 978,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Stack, {\n        direction: \"row\",\n        alignItems: \"center\",\n        spacing: 1,\n        children: [/*#__PURE__*/_jsxDEV(CheckCircleIcon, {\n          color: \"success\",\n          fontSize: \"small\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 991,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Box, {\n          children: [/*#__PURE__*/_jsxDEV(Typography, {\n            variant: \"h6\",\n            fontWeight: \"bold\",\n            sx: {\n              lineHeight: 1\n            },\n            children: statistics.caviInstallati\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 993,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Typography, {\n            variant: \"caption\",\n            color: \"text.secondary\",\n            children: \"Installati\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 996,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 992,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 990,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Stack, {\n        direction: \"row\",\n        alignItems: \"center\",\n        spacing: 1,\n        children: [/*#__PURE__*/_jsxDEV(LinkIcon, {\n          color: \"info\",\n          fontSize: \"small\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 1003,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Box, {\n          children: [/*#__PURE__*/_jsxDEV(Typography, {\n            variant: \"h6\",\n            fontWeight: \"bold\",\n            sx: {\n              lineHeight: 1\n            },\n            children: statistics.caviCollegati\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 1005,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Typography, {\n            variant: \"caption\",\n            color: \"text.secondary\",\n            children: \"Collegati\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 1008,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 1004,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 1002,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Stack, {\n        direction: \"row\",\n        alignItems: \"center\",\n        spacing: 1,\n        children: [/*#__PURE__*/_jsxDEV(VerifiedIcon, {\n          color: \"warning\",\n          fontSize: \"small\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 1015,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Box, {\n          children: [/*#__PURE__*/_jsxDEV(Typography, {\n            variant: \"h6\",\n            fontWeight: \"bold\",\n            sx: {\n              lineHeight: 1\n            },\n            children: statistics.caviCertificati\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 1017,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Typography, {\n            variant: \"caption\",\n            color: \"text.secondary\",\n            children: \"Certificati\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 1020,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 1016,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 1014,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Stack, {\n        direction: \"row\",\n        alignItems: \"center\",\n        spacing: 1,\n        children: [/*#__PURE__*/_jsxDEV(Box, {\n          sx: {\n            width: 32,\n            height: 32,\n            borderRadius: '50%',\n            bgcolor: statistics.iap >= 80 ? 'success.main' : statistics.iap >= 50 ? 'warning.main' : 'error.main',\n            display: 'flex',\n            alignItems: 'center',\n            justifyContent: 'center'\n          },\n          children: /*#__PURE__*/_jsxDEV(Typography, {\n            variant: \"caption\",\n            fontWeight: \"bold\",\n            color: \"white\",\n            children: [statistics.iap, \"%\"]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 1037,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 1027,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Box, {\n          children: [/*#__PURE__*/_jsxDEV(Typography, {\n            variant: \"body2\",\n            fontWeight: \"medium\",\n            sx: {\n              lineHeight: 1\n            },\n            children: \"IAP (Avanzamento Ponderato)\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 1042,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Typography, {\n            variant: \"caption\",\n            color: \"text.secondary\",\n            children: [statistics.metriInstallati, \"m installati\"]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 1045,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 1041,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 1026,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 976,\n      columnNumber: 7\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 975,\n    columnNumber: 5\n  }, this);\n\n  // La visualizzazione dei cavi è ora gestita dal componente CaviFilterableTable\n\n  // Rimossa funzione handleViewModeChange\n\n  // Renderizza il dialogo dei dettagli del cavo\n  const renderDetailsDialog = () => {\n    if (!selectedCavo) return null;\n    return /*#__PURE__*/_jsxDEV(Dialog, {\n      open: detailsDialogOpen,\n      onClose: handleCloseDetails,\n      maxWidth: \"md\",\n      fullWidth: true,\n      children: [/*#__PURE__*/_jsxDEV(DialogTitle, {\n        children: [\"Dettagli Cavo: \", selectedCavo.id_cavo]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 1064,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(DialogContent, {\n        dividers: true,\n        children: /*#__PURE__*/_jsxDEV(Grid, {\n          container: true,\n          spacing: 2,\n          children: [/*#__PURE__*/_jsxDEV(Grid, {\n            item: true,\n            xs: 12,\n            md: 6,\n            children: [/*#__PURE__*/_jsxDEV(Typography, {\n              variant: \"subtitle1\",\n              gutterBottom: true,\n              children: \"Informazioni Generali\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1070,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Box, {\n              sx: {\n                mb: 2\n              },\n              children: [/*#__PURE__*/_jsxDEV(Typography, {\n                variant: \"body2\",\n                children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n                  children: \"Sistema:\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1072,\n                  columnNumber: 45\n                }, this), \" \", selectedCavo.sistema || 'N/A']\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 1072,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                variant: \"body2\",\n                children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n                  children: \"Utility:\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1073,\n                  columnNumber: 45\n                }, this), \" \", selectedCavo.utility || 'N/A']\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 1073,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                variant: \"body2\",\n                children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n                  children: \"Tipologia:\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1074,\n                  columnNumber: 45\n                }, this), \" \", selectedCavo.tipologia || 'N/A']\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 1074,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                variant: \"body2\",\n                children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n                  children: \"Colore:\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1075,\n                  columnNumber: 45\n                }, this), \" \", selectedCavo.colore_cavo || 'N/A']\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 1075,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                variant: \"body2\",\n                children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n                  children: \"Formazione:\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1077,\n                  columnNumber: 45\n                }, this), \" \", selectedCavo.sezione || 'N/A']\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 1077,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 1071,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Typography, {\n              variant: \"subtitle1\",\n              gutterBottom: true,\n              children: \"Partenza\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1081,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Box, {\n              sx: {\n                mb: 2\n              },\n              children: [/*#__PURE__*/_jsxDEV(Typography, {\n                variant: \"body2\",\n                children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n                  children: \"Ubicazione:\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1083,\n                  columnNumber: 45\n                }, this), \" \", selectedCavo.ubicazione_partenza || 'N/A']\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 1083,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                variant: \"body2\",\n                children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n                  children: \"Utenza:\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1084,\n                  columnNumber: 45\n                }, this), \" \", selectedCavo.utenza_partenza || 'N/A']\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 1084,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                variant: \"body2\",\n                children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n                  children: \"Descrizione:\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1085,\n                  columnNumber: 45\n                }, this), \" \", selectedCavo.descrizione_utenza_partenza || 'N/A']\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 1085,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                variant: \"body2\",\n                children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n                  children: \"Responsabile:\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1086,\n                  columnNumber: 45\n                }, this), \" \", selectedCavo.responsabile_partenza || 'N/A']\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 1086,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                variant: \"body2\",\n                children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n                  children: \"Comanda:\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1087,\n                  columnNumber: 45\n                }, this), \" \", selectedCavo.comanda_partenza || 'N/A']\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 1087,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 1082,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 1069,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Grid, {\n            item: true,\n            xs: 12,\n            md: 6,\n            children: [/*#__PURE__*/_jsxDEV(Typography, {\n              variant: \"subtitle1\",\n              gutterBottom: true,\n              children: \"Arrivo\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1092,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Box, {\n              sx: {\n                mb: 2\n              },\n              children: [/*#__PURE__*/_jsxDEV(Typography, {\n                variant: \"body2\",\n                children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n                  children: \"Ubicazione:\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1094,\n                  columnNumber: 45\n                }, this), \" \", selectedCavo.ubicazione_arrivo || 'N/A']\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 1094,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                variant: \"body2\",\n                children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n                  children: \"Utenza:\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1095,\n                  columnNumber: 45\n                }, this), \" \", selectedCavo.utenza_arrivo || 'N/A']\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 1095,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                variant: \"body2\",\n                children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n                  children: \"Descrizione:\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1096,\n                  columnNumber: 45\n                }, this), \" \", selectedCavo.descrizione_utenza_arrivo || 'N/A']\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 1096,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                variant: \"body2\",\n                children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n                  children: \"Responsabile:\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1097,\n                  columnNumber: 45\n                }, this), \" \", selectedCavo.responsabile_arrivo || 'N/A']\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 1097,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                variant: \"body2\",\n                children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n                  children: \"Comanda:\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1098,\n                  columnNumber: 45\n                }, this), \" \", selectedCavo.comanda_arrivo || 'N/A']\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 1098,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 1093,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Typography, {\n              variant: \"subtitle1\",\n              gutterBottom: true,\n              children: \"Installazione\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1101,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Box, {\n              sx: {\n                mb: 2\n              },\n              children: [/*#__PURE__*/_jsxDEV(Typography, {\n                variant: \"body2\",\n                children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n                  children: \"Metri Teorici:\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1103,\n                  columnNumber: 45\n                }, this), \" \", selectedCavo.metri_teorici || 'N/A']\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 1103,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                variant: \"body2\",\n                children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n                  children: \"Metratura Reale:\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1104,\n                  columnNumber: 45\n                }, this), \" \", selectedCavo.metratura_reale || '0']\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 1104,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                variant: \"body2\",\n                children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n                  children: \"Stato:\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1105,\n                  columnNumber: 45\n                }, this), \" \", normalizeInstallationStatus(selectedCavo.stato_installazione)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 1105,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                variant: \"body2\",\n                children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n                  children: \"Collegamenti:\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1106,\n                  columnNumber: 45\n                }, this), \" \", selectedCavo.collegamenti || '0']\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 1106,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                variant: \"body2\",\n                children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n                  children: \"Bobina:\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1107,\n                  columnNumber: 45\n                }, this), \" \", selectedCavo.id_bobina || 'N/A']\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 1107,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                variant: \"body2\",\n                children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n                  children: \"Responsabile Posa:\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1108,\n                  columnNumber: 45\n                }, this), \" \", selectedCavo.responsabile_posa || 'N/A']\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 1108,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                variant: \"body2\",\n                children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n                  children: \"Comanda Posa:\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1109,\n                  columnNumber: 45\n                }, this), \" \", selectedCavo.comanda_posa || 'N/A']\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 1109,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                variant: \"body2\",\n                children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n                  children: \"Ultimo Aggiornamento:\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1110,\n                  columnNumber: 45\n                }, this), \" \", new Date(selectedCavo.timestamp).toLocaleString()]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 1110,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 1102,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 1091,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 1068,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 1067,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(DialogActions, {\n        children: /*#__PURE__*/_jsxDEV(Button, {\n          onClick: handleCloseDetails,\n          children: \"Chiudi\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 1116,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 1115,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 1063,\n      columnNumber: 7\n    }, this);\n  };\n\n  // Il pannello dei filtri è ora gestito dal componente CaviFilterableTable\n\n  return /*#__PURE__*/_jsxDEV(Box, {\n    className: \"cavi-page\",\n    children: loading ? /*#__PURE__*/_jsxDEV(Box, {\n      sx: {\n        display: 'flex',\n        flexDirection: 'column',\n        alignItems: 'center',\n        mt: 4\n      },\n      children: [/*#__PURE__*/_jsxDEV(CircularProgress, {\n        size: 40\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 1130,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(Typography, {\n        sx: {\n          mt: 2\n        },\n        children: \"Caricamento cavi...\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 1131,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(Button, {\n        variant: \"outlined\",\n        color: \"primary\",\n        onClick: () => window.location.reload(),\n        sx: {\n          mt: 2\n        },\n        children: \"Ricarica la pagina\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 1132,\n        columnNumber: 11\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 1129,\n      columnNumber: 9\n    }, this) : error ? /*#__PURE__*/_jsxDEV(Box, {\n      children: [/*#__PURE__*/_jsxDEV(Alert, {\n        severity: \"error\",\n        sx: {\n          mb: 2\n        },\n        children: [error, error.includes('Network Error') && /*#__PURE__*/_jsxDEV(Typography, {\n          variant: \"body2\",\n          sx: {\n            mt: 1\n          },\n          children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n            children: \"Suggerimento:\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 1147,\n            columnNumber: 17\n          }, this), \" Verifica che il server backend sia in esecuzione sulla porta 8001.\", /*#__PURE__*/_jsxDEV(\"br\", {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 1148,\n            columnNumber: 17\n          }, this), \"Puoi avviare il backend eseguendo il file \", /*#__PURE__*/_jsxDEV(\"code\", {\n            children: \"run_system.py\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 1149,\n            columnNumber: 59\n          }, this), \" nella cartella principale del progetto.\"]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 1146,\n          columnNumber: 15\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 1143,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(Box, {\n        sx: {\n          display: 'flex',\n          gap: 2\n        },\n        children: /*#__PURE__*/_jsxDEV(Button, {\n          variant: \"contained\",\n          className: \"primary-button\",\n          onClick: () => window.location.reload(),\n          children: \"Ricarica la pagina\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 1154,\n          columnNumber: 13\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 1153,\n        columnNumber: 11\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 1142,\n      columnNumber: 9\n    }, this) : /*#__PURE__*/_jsxDEV(Box, {\n      children: [revisioniDisponibili.length > 0 && /*#__PURE__*/_jsxDEV(Paper, {\n        sx: {\n          p: 2,\n          mb: 3\n        },\n        children: /*#__PURE__*/_jsxDEV(Box, {\n          sx: {\n            display: 'flex',\n            alignItems: 'center',\n            gap: 2\n          },\n          children: [/*#__PURE__*/_jsxDEV(Typography, {\n            variant: \"h6\",\n            children: \"Visualizzazione:\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 1169,\n            columnNumber: 17\n          }, this), /*#__PURE__*/_jsxDEV(FormControl, {\n            size: \"small\",\n            sx: {\n              minWidth: 250\n            },\n            children: [/*#__PURE__*/_jsxDEV(InputLabel, {\n              children: \"Revisione da Visualizzare\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1171,\n              columnNumber: 19\n            }, this), /*#__PURE__*/_jsxDEV(Select, {\n              value: revisioneSelezionata || 'corrente',\n              onChange: handleRevisioneChange,\n              label: \"Revisione da Visualizzare\",\n              children: [/*#__PURE__*/_jsxDEV(MenuItem, {\n                value: \"corrente\",\n                children: [\"\\uD83D\\uDCCB Revisione Corrente \", revisioneCorrente && `(${revisioneCorrente})`]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 1177,\n                columnNumber: 21\n              }, this), revisioniDisponibili.map(rev => /*#__PURE__*/_jsxDEV(MenuItem, {\n                value: rev.revisione,\n                children: [\"\\uD83D\\uDCDA \", rev.revisione, \" (\", rev.cavi_count, \" cavi)\", rev.revisione === revisioneCorrente && ' - Attuale']\n              }, rev.revisione, true, {\n                fileName: _jsxFileName,\n                lineNumber: 1181,\n                columnNumber: 23\n              }, this))]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 1172,\n              columnNumber: 19\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 1170,\n            columnNumber: 17\n          }, this), /*#__PURE__*/_jsxDEV(Chip, {\n            label: revisioneSelezionata ? `Storico: ${revisioneSelezionata}` : `Corrente: ${revisioneCorrente || 'N/A'}`,\n            color: revisioneSelezionata ? \"secondary\" : \"primary\",\n            variant: \"outlined\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 1188,\n            columnNumber: 17\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 1168,\n          columnNumber: 15\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 1167,\n        columnNumber: 13\n      }, this), renderDashboard(), /*#__PURE__*/_jsxDEV(Box, {\n        sx: {\n          mt: 4\n        },\n        children: [selectionEnabled && getTotalSelectedCount() > 0 && /*#__PURE__*/_jsxDEV(Box, {\n          sx: {\n            mb: 2\n          },\n          children: /*#__PURE__*/_jsxDEV(Chip, {\n            label: `${getTotalSelectedCount()} cavi selezionati`,\n            color: \"primary\",\n            variant: \"outlined\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 1209,\n            columnNumber: 17\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 1208,\n          columnNumber: 15\n        }, this), process.env.NODE_ENV === 'development' && caviAttivi.length > 0 && /*#__PURE__*/_jsxDEV(Box, {\n          sx: {\n            mb: 2,\n            p: 1,\n            bgcolor: '#f0f0f0',\n            borderRadius: 1,\n            fontSize: '0.8rem',\n            fontFamily: 'monospace',\n            display: 'none'\n          },\n          children: Object.keys(caviAttivi[0]).map(key => /*#__PURE__*/_jsxDEV(\"div\", {\n            children: [key, \": \", JSON.stringify(caviAttivi[0][key])]\n          }, key, true, {\n            fileName: _jsxFileName,\n            lineNumber: 1221,\n            columnNumber: 19\n          }, this))\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 1219,\n          columnNumber: 15\n        }, this), /*#__PURE__*/_jsxDEV(CaviFilterableTable, {\n          cavi: caviAttivi,\n          loading: loading,\n          onFilteredDataChange: filteredData => console.log('Cavi attivi filtrati:', filteredData.length),\n          revisioneCorrente: ((_caviAttivi$ = caviAttivi[0]) === null || _caviAttivi$ === void 0 ? void 0 : _caviAttivi$.revisione_ufficiale) || ((_caviAttivi$2 = caviAttivi[0]) === null || _caviAttivi$2 === void 0 ? void 0 : _caviAttivi$2.revisione) || ((_caviAttivi$3 = caviAttivi[0]) === null || _caviAttivi$3 === void 0 ? void 0 : _caviAttivi$3.rev),\n          selectionEnabled: selectionEnabled,\n          selectedCavi: selectedCaviAttivi,\n          onSelectionChange: handleCaviAttiviSelectionChange,\n          onSelectionToggle: handleSelectionToggle,\n          contextMenuItems: getContextMenuItems,\n          onContextMenuAction: handleContextMenuAction,\n          onStatusAction: handleStatusAction\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 1226,\n          columnNumber: 13\n        }, this), caviAttivi.length === 0 && !loading && /*#__PURE__*/_jsxDEV(Alert, {\n          severity: \"info\",\n          sx: {\n            mt: 2\n          },\n          children: \"Nessun cavo attivo trovato. I cavi attivi appariranno qui.\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 1240,\n          columnNumber: 15\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 1205,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(Box, {\n        sx: {\n          mt: 4\n        },\n        children: [/*#__PURE__*/_jsxDEV(Box, {\n          sx: {\n            display: 'flex',\n            justifyContent: 'space-between',\n            alignItems: 'center',\n            mb: 2\n          },\n          children: /*#__PURE__*/_jsxDEV(Typography, {\n            variant: \"h5\",\n            children: [\"Cavi Spare \", caviSpare.length > 0 ? `(${caviSpare.length})` : '']\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 1249,\n            columnNumber: 15\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 1248,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(CaviFilterableTable, {\n          cavi: caviSpare,\n          loading: loading,\n          onFilteredDataChange: filteredData => console.log('Cavi spare filtrati:', filteredData.length),\n          selectionEnabled: selectionEnabled,\n          selectedCavi: selectedCaviSpare,\n          onSelectionChange: handleCaviSpareSelectionChange,\n          onSelectionToggle: handleSelectionToggle,\n          contextMenuItems: getContextMenuItems,\n          onContextMenuAction: handleContextMenuAction,\n          onStatusAction: handleStatusAction\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 1253,\n          columnNumber: 13\n        }, this), caviSpare.length === 0 && !loading && /*#__PURE__*/_jsxDEV(Alert, {\n          severity: \"info\",\n          sx: {\n            mt: 2\n          },\n          children: \"Nessun cavo SPARE trovato. I cavi marcati come SPARE appariranno qui.\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 1266,\n          columnNumber: 15\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 1247,\n        columnNumber: 11\n      }, this), renderDetailsDialog(), /*#__PURE__*/_jsxDEV(InserisciMetriDialogCompleto, {\n        open: inserisciMetriDialog.open,\n        onClose: handleCloseInserisciMetri,\n        cavo: inserisciMetriDialog.cavo,\n        cantiereId: cantiereId,\n        onSuccess: message => {\n          showNotification(message, 'success');\n          // Ricarica i dati per aggiornare lo stato\n          setTimeout(() => fetchCavi(true), 500);\n        },\n        onError: message => {\n          showNotification(message, 'error');\n        },\n        loading: inserisciMetriDialog.loading\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 1284,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(ModificaBobinaDialogCompleto, {\n        open: modificaBobinaDialog.open,\n        onClose: handleCloseModificaBobina,\n        cavo: modificaBobinaDialog.cavo,\n        cantiereId: cantiereId,\n        onSuccess: handleModificaBobinaSuccess,\n        onError: handleModificaBobinaError\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 1300,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(Dialog, {\n        open: collegamentiDialog.open,\n        onClose: handleCloseCollegamenti,\n        maxWidth: \"md\",\n        fullWidth: true,\n        disableEscapeKeyDown: false,\n        keepMounted: false,\n        disablePortal: false,\n        disableScrollLock: true,\n        hideBackdrop: false,\n        disableAutoFocus: true,\n        disableEnforceFocus: true,\n        disableRestoreFocus: true,\n        transitionDuration: 0,\n        TransitionProps: {\n          timeout: 0,\n          appear: false,\n          enter: false,\n          exit: false\n        },\n        PaperProps: {\n          style: {\n            transition: 'none',\n            transform: 'none'\n          }\n        },\n        children: /*#__PURE__*/_jsxDEV(DialogContent, {\n          children: collegamentiDialog.cavo && /*#__PURE__*/_jsxDEV(CollegamentiCavo, {\n            cantiereId: cantiereId,\n            selectedCavo: collegamentiDialog.cavo,\n            onSuccess: message => {\n              if (message) {\n                showNotification(message, 'success');\n                // Chiudi il dialog immediatamente\n                setCollegamentiDialog(prev => ({\n                  ...prev,\n                  open: false\n                }));\n                // Ricarica i dati per aggiornare lo stato dei collegamenti\n                setTimeout(() => fetchCavi(true), 300);\n              }\n              // Non chiudere il dialog se message è null (annullamento)\n            },\n            onError: message => {\n              showNotification(message, 'error');\n            },\n            onClose: handleCloseCollegamenti\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 1339,\n            columnNumber: 17\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 1337,\n          columnNumber: 13\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 1310,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(Box, {\n        sx: {\n          display: 'none'\n        },\n        children: /*#__PURE__*/_jsxDEV(CertificazioneCaviImproved, {\n          ref: certificazioneRef,\n          cantiereId: cantiereId,\n          onSuccess: message => {\n            showNotification(message, 'success');\n            // Ricarica i dati per aggiornare lo stato di certificazione\n            setTimeout(() => fetchCavi(true), 500);\n          },\n          onError: message => {\n            showNotification(message, 'error');\n          }\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 1363,\n          columnNumber: 13\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 1362,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(Snackbar, {\n        open: notification.open,\n        autoHideDuration: 4000,\n        onClose: handleCloseNotification,\n        anchorOrigin: {\n          vertical: 'bottom',\n          horizontal: 'center'\n        },\n        children: /*#__PURE__*/_jsxDEV(Alert, {\n          onClose: handleCloseNotification,\n          severity: notification.severity,\n          sx: {\n            width: '100%'\n          },\n          children: notification.message\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 1384,\n          columnNumber: 13\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 1378,\n        columnNumber: 11\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 1164,\n      columnNumber: 9\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 1127,\n    columnNumber: 5\n  }, this);\n};\n_s(VisualizzaCaviPage, \"nHud9qGDnm8U6ji4LJivEptaiJc=\", false, function () {\n  return [useAuth, useGlobalContext, useNavigate];\n});\n_c = VisualizzaCaviPage;\nexport default VisualizzaCaviPage;\nvar _c;\n$RefreshReg$(_c, \"VisualizzaCaviPage\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "useCallback", "useRef", "Box", "Typography", "Paper", "<PERSON><PERSON>", "Grid", "Card", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "IconButton", "Chip", "CircularProgress", "LinearProgress", "Dialog", "DialogTitle", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "DialogActions", "Snackbar", "FormControl", "InputLabel", "Select", "MenuItem", "<PERSON><PERSON>", "InfoIcon", "Cable", "CableIcon", "CheckCircle", "CheckCircleIcon", "Schedule", "ScheduleIcon", "Link", "LinkIcon", "<PERSON><PERSON><PERSON>", "LinkOffIcon", "Timeline", "TimelineIcon", "CheckBox", "CheckBoxIcon", "CheckBoxOutlineBlank", "CheckBoxOutlineBlankIcon", "Visibility", "VisibilityIcon", "Edit", "EditIcon", "Delete", "DeleteIcon", "Add", "AddIcon", "SelectAll", "SelectAllIcon", "ContentCopy", "CopyIcon", "Settings", "SettingsIcon", "Verified", "VerifiedIcon", "useNavigate", "useAuth", "useGlobalContext", "caviService", "parcoCaviService", "CavoForm", "normalizeInstallationStatus", "CaviFilterableTable", "InserisciMetriDialogCompleto", "ModificaBobinaDialogCompleto", "CollegamentiCavo", "CertificazioneCaviImproved", "jsxDEV", "_jsxDEV", "VisualizzaCaviPage", "_s", "_caviAttivi$", "_caviAttivi$2", "_caviAttivi$3", "isImpersonating", "user", "openEliminaCavoDialog", "setOpenEliminaCavoDialog", "openModificaCavoDialog", "setOpenModificaCavoDialog", "openAggiungiCavoDialog", "setOpenAggiungiCavoDialog", "navigate", "cantiereId", "setCantiereId", "cantiereName", "setCantiereName", "caviAttivi", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "caviSpare", "setCaviSpare", "loading", "setLoading", "error", "setError", "notification", "setNotification", "open", "message", "severity", "selected<PERSON><PERSON><PERSON>", "setSelectedCavo", "detailsDialogOpen", "setDetailsDialogOpen", "selectionEnabled", "setSelectionEnabled", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "setSele<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "selectedCaviSpare", "setSelectedCaviSpare", "inserisciMetriDialog", "setInserisciMetriDialog", "cavo", "collegamentiDialog", "setCollegamentiDialog", "modificaBobinaDialog", "setModificaBobinaDialog", "certificazioneRef", "statistics", "setStatistics", "totaleCavi", "caviInstallati", "caviDaInstallare", "caviInCorso", "caviCollegati", "caviNonCollegati", "caviCertificati", "caviNonCertificati", "percentualeInstallazione", "percentualeCollegamento", "percentualeCertificazione", "iap", "metriTotali", "metriInstallati", "metriR<PERSON><PERSON><PERSON>", "revisioniDisponibili", "setRevisioniDisponibili", "revisioneSelezionata", "setRevisioneSelezionata", "revisioneCorrente", "setRevisioneCorrente", "calculateIAP", "nTot", "nInst", "nColl", "nCert", "Wp", "Wc", "Wz", "sforzoSoloInstallati", "sforzoSoloCollegati", "sforzoCertificati", "numeratore", "denominatore", "console", "log", "pesi", "Math", "round", "calculateStatistics", "caviAttiviData", "caviSpareData", "tuttiCavi", "length", "totale", "filter", "stato_installazione", "colle<PERSON>nti", "responsabile_partenza", "responsabile_arrivo", "certificato", "reduce", "sum", "parseFloat", "metri_te<PERSON>ci", "metratura_reale", "newStatistics", "loadStatiInstallazione", "setStatiInstallazione", "loadRevisioni", "cantiereIdToUse", "revisioneCorrenteData", "getRevisioneCorrente", "revisione_corrente", "revisioniData", "getRevisioniDisponibili", "revisioni", "handleRevisioneChange", "event", "nuovaRevisione", "target", "value", "filters", "setFilters", "tipologia", "sort_by", "sort_order", "statiInstallazione", "tipologieCavi", "setTipologieCavi", "<PERSON><PERSON><PERSON>", "silentLoading", "localStorage", "getItem", "attivi", "get<PERSON><PERSON>", "attiviError", "caviSpareTra<PERSON>ttivi", "modificato_manualmente", "spare", "getCaviSpare", "spareError", "standardError", "setTimeout", "document", "body", "textContent", "includes", "window", "location", "reload", "fetchData", "token", "selectedCantiereId", "selectedCantiereName", "i", "key", "role", "cantiere_id", "toString", "cantiere_name", "setItem", "base64Url", "split", "base64", "replace", "jsonPayload", "decodeURIComponent", "atob", "map", "c", "charCodeAt", "slice", "join", "payload", "JSON", "parse", "e", "warn", "cantiereIdNum", "parseInt", "isNaN", "timeoutPromise", "Promise", "_", "reject", "Error", "caviPromise", "race", "caviError", "status", "data", "stack", "code", "name", "response", "statusText", "sparePromise", "err", "_err$response", "_err$response2", "_err$response3", "_err$response4", "_err$response5", "_err$response5$data", "errorMessage", "detail", "handleOpenDetails", "handleCloseDetails", "handleCloseNotification", "prev", "showNotification", "handleSelectionToggle", "handleCaviAttiviSelectionChange", "selectedIds", "handleCaviSpareSelectionChange", "getAllSelectedCavi", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "id_cavo", "selected<PERSON><PERSON><PERSON><PERSON><PERSON>", "getTotalSelectedCount", "handleContextMenuAction", "action", "some", "isSelected", "id", "navigator", "clipboard", "writeText", "details", "sezione", "getContextMenuItems", "type", "label", "icon", "fontSize", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "onClick", "color", "shortcut", "description", "handleStatusAction", "actionType", "actionLabel", "current", "createCertificationForCavo", "viewCertificationForCavo", "generatePdfForCavo", "handleModificaBobinaSuccess", "handleModificaBobinaError", "handleCloseInserisciMetri", "handleCloseModificaBobina", "handleCloseCollegamenti", "renderDashboard", "sx", "p", "mb", "bgcolor", "children", "direction", "spacing", "alignItems", "justifyContent", "flexWrap", "variant", "fontWeight", "lineHeight", "width", "height", "borderRadius", "display", "renderDetailsDialog", "onClose", "max<PERSON><PERSON><PERSON>", "fullWidth", "dividers", "container", "item", "xs", "md", "gutterBottom", "sistema", "utility", "colore_cavo", "ubicazione_partenza", "utenza_partenza", "descrizione_utenza_partenza", "comanda_partenza", "ubicazione_arrivo", "utenza_arrivo", "descrizione_utenza_arrivo", "comanda_arrivo", "id_bobina", "responsabile_posa", "comanda_posa", "Date", "timestamp", "toLocaleString", "className", "flexDirection", "mt", "size", "gap", "min<PERSON><PERSON><PERSON>", "onChange", "rev", "revisione", "cavi_count", "process", "env", "NODE_ENV", "fontFamily", "Object", "keys", "stringify", "cavi", "onFilteredDataChange", "filteredData", "revisione_ufficiale", "<PERSON><PERSON><PERSON>", "onSelectionChange", "onSelectionToggle", "contextMenuItems", "onContextMenuAction", "onStatusAction", "onSuccess", "onError", "disableEscapeKeyDown", "keepMounted", "disable<PERSON><PERSON><PERSON>", "disableScrollLock", "hideBackdrop", "disableAutoFocus", "disableEnforceFocus", "disable<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "transitionDuration", "TransitionProps", "timeout", "appear", "enter", "exit", "PaperProps", "style", "transition", "transform", "ref", "autoHideDuration", "anchor<PERSON><PERSON><PERSON>", "vertical", "horizontal", "_c", "$RefreshReg$"], "sources": ["C:/CMS/webapp/frontend/src/pages/cavi/VisualizzaCaviPage.js"], "sourcesContent": ["import React, { useState, useEffect, useCallback, useRef } from 'react';\nimport {\n  Box,\n  Typography,\n  Paper,\n  Button,\n  Grid,\n  Card,\n  CardContent,\n  Alert,\n  IconButton,\n  Chip,\n  CircularProgress,\n  LinearProgress,\n  Dialog,\n  DialogTitle,\n  DialogContent,\n  DialogActions,\n  Snackbar,\n  FormControl,\n  InputLabel,\n  Select,\n  MenuItem,\n  Stack\n} from '@mui/material';\nimport InfoIcon from '@mui/icons-material/Info';\nimport {\n  Cable as CableIcon,\n  CheckCircle as CheckCircleIcon,\n  Schedule as ScheduleIcon,\n  Link as LinkIcon,\n  LinkOff as LinkOffIcon,\n  Timeline as TimelineIcon,\n  CheckBox as CheckBoxIcon,\n  CheckBoxOutlineBlank as CheckBoxOutlineBlankIcon,\n  Visibility as VisibilityIcon,\n  Edit as EditIcon,\n  Delete as DeleteIcon,\n  Add as AddIcon,\n  SelectAll as SelectAllIcon,\n  ContentCopy as CopyIcon,\n  Settings as SettingsIcon,\n  Verified as VerifiedIcon\n} from '@mui/icons-material';\nimport { useNavigate } from 'react-router-dom';\nimport { useAuth } from '../../context/AuthContext';\nimport { useGlobalContext } from '../../context/GlobalContext';\n// import PosaCaviCollegamenti from '../../components/cavi/PosaCaviCollegamenti'; // OBSOLETO: Componente eliminato\nimport caviService from '../../services/caviService';\nimport parcoCaviService from '../../services/parcoCaviService';\nimport CavoForm from '../../components/cavi/CavoForm';\nimport { normalizeInstallationStatus } from '../../utils/validationUtils';\nimport CaviFilterableTable from '../../components/cavi/CaviFilterableTable';\nimport InserisciMetriDialogCompleto from '../../components/cavi/InserisciMetriDialogCompleto';\nimport ModificaBobinaDialogCompleto from '../../components/cavi/ModificaBobinaDialogCompleto';\nimport CollegamentiCavo from '../../components/cavi/CollegamentiCavo';\nimport CertificazioneCaviImproved from '../../components/cavi/CertificazioneCaviImproved';\n\nimport './CaviPage.css';\n\nconst VisualizzaCaviPage = () => {\n  const { isImpersonating, user } = useAuth();\n  const { openEliminaCavoDialog, setOpenEliminaCavoDialog, openModificaCavoDialog, setOpenModificaCavoDialog, openAggiungiCavoDialog, setOpenAggiungiCavoDialog } = useGlobalContext();\n  const navigate = useNavigate();\n  const [cantiereId, setCantiereId] = useState(null);\n  const [cantiereName, setCantiereName] = useState('');\n  const [caviAttivi, setCaviAttivi] = useState([]);\n  const [caviSpare, setCaviSpare] = useState([]);\n  const [loading, setLoading] = useState(true);\n  const [error, setError] = useState(null);\n  // Stato per le notifiche\n  const [notification, setNotification] = useState({ open: false, message: '', severity: 'success' });\n  // Rimosso stato viewMode\n\n  // Stato per il dialogo dei dettagli del cavo\n  const [selectedCavo, setSelectedCavo] = useState(null);\n  const [detailsDialogOpen, setDetailsDialogOpen] = useState(false);\n\n  // Stati per la selezione dei cavi\n  const [selectionEnabled, setSelectionEnabled] = useState(false);\n  const [selectedCaviAttivi, setSelectedCaviAttivi] = useState([]);\n  const [selectedCaviSpare, setSelectedCaviSpare] = useState([]);\n\n  // Stati per i dialoghi di azione sui pulsanti stato\n  const [inserisciMetriDialog, setInserisciMetriDialog] = useState({\n    open: false,\n    cavo: null,\n    loading: false\n  });\n\n  const [collegamentiDialog, setCollegamentiDialog] = useState({\n    open: false,\n    cavo: null,\n    loading: false\n  });\n  const [modificaBobinaDialog, setModificaBobinaDialog] = useState({\n    open: false,\n    cavo: null,\n    loading: false\n  });\n\n  // Ref per il componente CertificazioneCavi\n  const certificazioneRef = useRef(null);\n\n\n\n  // Stati per statistiche avanzate\n  const [statistics, setStatistics] = useState({\n    totaleCavi: 0,\n    caviInstallati: 0,\n    caviDaInstallare: 0,\n    caviInCorso: 0,\n    caviCollegati: 0,\n    caviNonCollegati: 0,\n    caviCertificati: 0,\n    caviNonCertificati: 0,\n    percentualeInstallazione: 0,\n    percentualeCollegamento: 0,\n    percentualeCertificazione: 0,\n    iap: 0, // Indice di Avanzamento Ponderato\n    metriTotali: 0,\n    metriInstallati: 0,\n    metriRimanenti: 0\n  });\n\n\n\n  // Stato per la gestione delle revisioni\n  const [revisioniDisponibili, setRevisioniDisponibili] = useState([]);\n  const [revisioneSelezionata, setRevisioneSelezionata] = useState('');\n  const [revisioneCorrente, setRevisioneCorrente] = useState('');\n\n  // Rimosso stato per il debug\n\n  // Funzione per calcolare l'Indice di Avanzamento Ponderato (IAP)\n  const calculateIAP = (nTot, nInst, nColl, nCert) => {\n    // Pesi per le fasi del progetto\n    const Wp = 2.0;  // Peso fase Posa\n    const Wc = 1.5;  // Peso fase Collegamento\n    const Wz = 0.5;  // Peso fase Certificazione\n\n    // Se non ci sono cavi, ritorna 0\n    if (nTot === 0) return 0;\n\n    // Calcolo del numeratore (Sforzo Completato)\n    const sforzoSoloInstallati = (nInst - nColl) * Wp;\n    const sforzoSoloCollegati = (nColl - nCert) * (Wp + Wc);\n    const sforzoCertificati = nCert * (Wp + Wc + Wz);\n    const numeratore = sforzoSoloInstallati + sforzoSoloCollegati + sforzoCertificati;\n\n    // Calcolo del denominatore (Sforzo Massimo Previsto)\n    const denominatore = nTot * (Wp + Wc + Wz);\n\n    // Calcolo finale dell'IAP in percentuale\n    const iap = (numeratore / denominatore) * 100;\n\n    console.log('Calcolo IAP:', {\n      nTot, nInst, nColl, nCert,\n      pesi: { Wp, Wc, Wz },\n      sforzoSoloInstallati,\n      sforzoSoloCollegati,\n      sforzoCertificati,\n      numeratore,\n      denominatore,\n      iap: Math.round(iap * 100) / 100\n    });\n\n    return Math.round(iap * 100) / 100; // Arrotonda a 2 decimali\n  };\n\n  // Funzione per calcolare le statistiche avanzate\n  const calculateStatistics = (caviAttiviData, caviSpareData) => {\n    const tuttiCavi = [...(caviAttiviData || []), ...(caviSpareData || [])];\n\n    if (tuttiCavi.length === 0) {\n      console.log('Nessun cavo disponibile per il calcolo delle statistiche');\n      return;\n    }\n\n    console.log('Calcolo statistiche con dati:', {\n      caviAttivi: caviAttiviData?.length || 0,\n      caviSpare: caviSpareData?.length || 0,\n      totale: tuttiCavi.length\n    });\n\n    const totaleCavi = tuttiCavi.length;\n\n    // Calcola stati di installazione\n    const caviInstallati = tuttiCavi.filter(cavo =>\n      cavo.stato_installazione === 'Installato' ||\n      cavo.stato_installazione === 'INSTALLATO' ||\n      cavo.stato_installazione === 'POSATO'\n    ).length;\n\n    const caviDaInstallare = tuttiCavi.filter(cavo =>\n      cavo.stato_installazione === 'Da installare' ||\n      cavo.stato_installazione === 'DA_INSTALLARE'\n    ).length;\n\n    const caviInCorso = tuttiCavi.filter(cavo =>\n      cavo.stato_installazione === 'In corso' ||\n      cavo.stato_installazione === 'IN_CORSO'\n    ).length;\n\n    // Calcola stati di collegamento\n    const caviCollegati = tuttiCavi.filter(cavo =>\n      cavo.collegamenti === 3 &&\n      cavo.responsabile_partenza &&\n      cavo.responsabile_arrivo\n    ).length;\n\n    const caviNonCollegati = totaleCavi - caviCollegati;\n\n    // Calcola certificazioni basandosi sul campo 'certificato' dei cavi\n    const caviCertificati = tuttiCavi.filter(cavo => cavo.certificato === true || cavo.certificato === 'true').length;\n\n    // Calcola l'Indice di Avanzamento Ponderato (IAP)\n    const iap = calculateIAP(totaleCavi, caviInstallati, caviCollegati, caviCertificati);\n\n    // Calcola percentuali tradizionali per confronto\n    const percentualeInstallazione = iap; // Sostituito con IAP\n    const percentualeCollegamento = totaleCavi > 0 ? Math.round((caviCollegati / totaleCavi) * 100) : 0;\n\n    // Calcola metri\n    const metriTotali = tuttiCavi.reduce((sum, cavo) => sum + (parseFloat(cavo.metri_teorici) || 0), 0);\n    const metriInstallati = tuttiCavi\n      .filter(cavo => cavo.stato_installazione === 'Installato' || cavo.stato_installazione === 'INSTALLATO' || cavo.stato_installazione === 'POSATO')\n      .reduce((sum, cavo) => sum + (parseFloat(cavo.metratura_reale) || parseFloat(cavo.metri_teorici) || 0), 0);\n    const metriRimanenti = metriTotali - metriInstallati;\n    const caviNonCertificati = totaleCavi - caviCertificati;\n    const percentualeCertificazione = totaleCavi > 0 ? Math.round((caviCertificati / totaleCavi) * 100) : 0;\n\n    const newStatistics = {\n      totaleCavi,\n      caviInstallati,\n      caviDaInstallare,\n      caviInCorso,\n      caviCollegati,\n      caviNonCollegati,\n      caviCertificati,\n      caviNonCertificati,\n      percentualeInstallazione,\n      percentualeCollegamento,\n      percentualeCertificazione,\n      iap, // Indice di Avanzamento Ponderato\n      metriTotali: Math.round(metriTotali),\n      metriInstallati: Math.round(metriInstallati),\n      metriRimanenti: Math.round(metriRimanenti)\n    };\n\n    console.log('Nuove statistiche calcolate:', newStatistics);\n    setStatistics(newStatistics);\n  };\n\n  // Funzione per caricare gli stati di installazione disponibili\n  const loadStatiInstallazione = () => {\n    // Usa i valori dell'enum StatoInstallazione\n    setStatiInstallazione(['Installato', 'Da installare', 'In corso']);\n  };\n\n  // Funzione per caricare le revisioni disponibili\n  const loadRevisioni = async (cantiereIdToUse) => {\n    try {\n      console.log('Caricamento revisioni per cantiere:', cantiereIdToUse);\n\n      // Carica la revisione corrente\n      const revisioneCorrenteData = await caviService.getRevisioneCorrente(cantiereIdToUse);\n      console.log('Revisione corrente:', revisioneCorrenteData);\n      setRevisioneCorrente(revisioneCorrenteData.revisione_corrente);\n\n      // Carica tutte le revisioni disponibili\n      const revisioniData = await caviService.getRevisioniDisponibili(cantiereIdToUse);\n      console.log('Revisioni disponibili:', revisioniData);\n      setRevisioniDisponibili(revisioniData.revisioni || []);\n\n      // LOGICA REVISIONI: La revisione corrente è quella di default\n      // Non impostiamo una revisione selezionata, così il sistema usa automaticamente la corrente\n      console.log('Logica revisioni: usando revisione corrente di default');\n    } catch (error) {\n      console.error('Errore nel caricamento delle revisioni:', error);\n    }\n  };\n\n  // Funzione per gestire il cambio di revisione\n  const handleRevisioneChange = (event) => {\n    const nuovaRevisione = event.target.value;\n\n    // LOGICA REVISIONI:\n    // - Se vuoto o \"corrente\" -> usa revisione corrente (non specificare parametro)\n    // - Se specifica -> usa quella revisione per visualizzazione storica\n    if (nuovaRevisione === '' || nuovaRevisione === 'corrente') {\n      setRevisioneSelezionata('');\n      console.log('Passaggio a revisione corrente (default)');\n    } else {\n      setRevisioneSelezionata(nuovaRevisione);\n      console.log('Passaggio a revisione storica:', nuovaRevisione);\n    }\n  };\n\n  // Stato per filtri e ordinamento\n  const [filters, setFilters] = useState({\n    stato_installazione: '',\n    tipologia: '',\n    sort_by: '',\n    sort_order: 'asc'\n  });\n\n  // Opzioni per i filtri\n  const [statiInstallazione, setStatiInstallazione] = useState([]);\n  const [tipologieCavi, setTipologieCavi] = useState([]);\n\n  // Rimossa funzione di debug\n\n  // Funzione per caricare i cavi\n  // Il parametro silentLoading permette di evitare di mostrare lo stato di caricamento\n  const fetchCavi = async (silentLoading = false) => {\n    try {\n      if (!silentLoading) {\n        setLoading(true);\n      }\n      console.log('Caricamento cavi per cantiere:', cantiereId);\n\n      // Verifica che cantiereId sia valido\n      if (!cantiereId) {\n        console.error('fetchCavi: cantiereId non valido:', cantiereId);\n        setError('ID cantiere non valido o mancante. Ricarica la pagina.');\n        setLoading(false);\n        return;\n      }\n\n      // Recupera il cantiereId dal localStorage come fallback\n      let cantiereIdToUse = cantiereId;\n      if (!cantiereIdToUse) {\n        cantiereIdToUse = localStorage.getItem('selectedCantiereId');\n        console.log('Usando cantiereId dal localStorage:', cantiereIdToUse);\n        if (!cantiereIdToUse) {\n          console.error('Impossibile trovare un ID cantiere valido');\n          setError('ID cantiere non trovato. Ricarica la pagina.');\n          setLoading(false);\n          return;\n        }\n      }\n\n      // Carica i cavi attivi\n      console.log('Caricamento cavi attivi (tipo_cavo=0)...');\n      let attivi = [];\n      try {\n        attivi = await caviService.getCavi(cantiereIdToUse, 0, filters);\n        console.log('Cavi attivi caricati:', attivi ? attivi.length : 0);\n      } catch (attiviError) {\n        console.error('Errore nel caricamento dei cavi attivi:', attiviError);\n        // Continua con un array vuoto\n        attivi = [];\n      }\n\n      // Verifica se ci sono cavi con modificato_manualmente = 3 tra i cavi attivi\n      if (attivi && attivi.length > 0) {\n        const caviSpareTraAttivi = attivi.filter(cavo => cavo.modificato_manualmente === 3);\n        if (caviSpareTraAttivi.length > 0) {\n          console.error('ERRORE: Trovati cavi con modificato_manualmente = 3 tra i cavi attivi:', caviSpareTraAttivi);\n        }\n      }\n\n      setCaviAttivi(attivi || []);\n\n      // Carica i cavi SPARE con la nuova funzione dedicata\n      let spare = [];\n      try {\n        console.log('Caricamento cavi SPARE con funzione dedicata...');\n        spare = await caviService.getCaviSpare(cantiereIdToUse);\n        console.log('Cavi SPARE caricati con funzione dedicata:', spare ? spare.length : 0);\n        if (spare && spare.length > 0) {\n          console.log('Primo cavo SPARE:', spare[0]);\n        }\n      } catch (spareError) {\n        console.error('Errore nel caricamento dei cavi SPARE con funzione dedicata:', spareError);\n        // Se fallisce, prova con il metodo standard\n        try {\n          console.log('Tentativo con metodo standard...');\n          spare = await caviService.getCavi(cantiereIdToUse, 3);\n          console.log('Cavi SPARE caricati con metodo standard:', spare ? spare.length : 0);\n        } catch (standardError) {\n          console.error('Errore anche con metodo standard:', standardError);\n          // Continua con un array vuoto\n          spare = [];\n        }\n      }\n      setCaviSpare(spare || []);\n\n\n\n      // Se siamo arrivati qui, rimuovi eventuali messaggi di errore precedenti\n      setError('');\n    } catch (error) {\n      console.error('Errore generale nel caricamento dei cavi:', error);\n      setError(`Errore nel caricamento dei cavi: ${error.message || 'Errore sconosciuto'}`);\n\n      // Prova a ricaricare la pagina dopo un ritardo se l'errore persiste\n      setTimeout(() => {\n        // Verifica se siamo ancora in errore\n        if (document.body.textContent.includes('Errore nel caricamento dei cavi')) {\n          console.log('Errore persistente, tentativo di ricaricamento della pagina...');\n          window.location.reload();\n        }\n      }, 5000); // 5 secondi di ritardo\n    } finally {\n      if (!silentLoading) {\n        setLoading(false);\n      }\n    }\n  };\n\n  // Carica i dati del cantiere e dei cavi\n  useEffect(() => {\n    // Carica gli stati di installazione all'avvio\n    loadStatiInstallazione();\n\n    const fetchData = async () => {\n      try {\n        console.log('Inizializzazione VisualizzaCaviPage...');\n\n        // Verifica che l'utente sia autenticato\n        const token = localStorage.getItem('token');\n        console.log('Token presente:', !!token);\n        if (!token) {\n          setError('Sessione scaduta. Effettua nuovamente il login.');\n          setLoading(false);\n          return;\n        }\n\n        // Recupera l'ID del cantiere selezionato dal localStorage\n        let selectedCantiereId = localStorage.getItem('selectedCantiereId');\n        let selectedCantiereName = localStorage.getItem('selectedCantiereName');\n\n        console.log('Cantiere selezionato dal localStorage:', { selectedCantiereId, selectedCantiereName });\n        console.log('Dati utente:', user);\n\n        // Stampa tutti i dati nel localStorage per debug\n        console.log('DEBUG - Tutti i dati nel localStorage:');\n        for (let i = 0; i < localStorage.length; i++) {\n          const key = localStorage.key(i);\n          console.log(`${key}: ${localStorage.getItem(key)}`);\n        }\n\n        // SOLUZIONE DIRETTA: Ottieni l'ID del cantiere direttamente dal token JWT\n        if (user?.role === 'cantieri_user') {\n          console.log('Utente cantiere rilevato, tentativo di recupero ID cantiere dai dati utente');\n\n          // Verifica se l'utente ha un ID cantiere nei dati utente\n          if (user.cantiere_id) {\n            console.log('Trovato ID cantiere nei dati utente:', user.cantiere_id);\n            selectedCantiereId = user.cantiere_id.toString();\n            selectedCantiereName = user.cantiere_name || `Cantiere ${user.cantiere_id}`;\n\n            // Salva l'ID e il nome del cantiere nel localStorage\n            localStorage.setItem('selectedCantiereId', selectedCantiereId);\n            localStorage.setItem('selectedCantiereName', selectedCantiereName);\n            console.log('Salvato ID cantiere nel localStorage:', selectedCantiereId);\n          } else {\n            // Tentativo di recupero dal token JWT\n            try {\n              console.log('Tentativo di decodifica del token JWT per recuperare l\\'ID cantiere');\n              const token = localStorage.getItem('token');\n              if (token) {\n                // Decodifica il token JWT (senza verifica della firma)\n                const base64Url = token.split('.')[1];\n                const base64 = base64Url.replace(/-/g, '+').replace(/_/g, '/');\n                const jsonPayload = decodeURIComponent(atob(base64).split('').map(c => {\n                  return '%' + ('00' + c.charCodeAt(0).toString(16)).slice(-2);\n                }).join(''));\n\n                const payload = JSON.parse(jsonPayload);\n                console.log('Payload del token JWT:', payload);\n\n                if (payload.cantiere_id) {\n                  console.log('Trovato ID cantiere nel token JWT:', payload.cantiere_id);\n                  selectedCantiereId = payload.cantiere_id.toString();\n                  // Usa un nome generico se non disponibile\n                  selectedCantiereName = `Cantiere ${payload.cantiere_id}`;\n\n                  // Salva l'ID e il nome del cantiere nel localStorage\n                  localStorage.setItem('selectedCantiereId', selectedCantiereId);\n                  localStorage.setItem('selectedCantiereName', selectedCantiereName);\n                  console.log('Salvato ID cantiere nel localStorage:', selectedCantiereId);\n                }\n              }\n            } catch (e) {\n              console.error('Errore durante la decodifica del token JWT:', e);\n            }\n          }\n        }\n\n        // SOLUZIONE TEMPORANEA: Se ancora non abbiamo un ID cantiere, usa un valore hardcoded per debug\n        if (!selectedCantiereId || selectedCantiereId === 'undefined' || selectedCantiereId === 'null') {\n          console.warn('ATTENZIONE: Nessun ID cantiere trovato, utilizzo valore hardcoded per debug');\n          // Usa il primo cantiere disponibile (questo è solo per debug)\n          selectedCantiereId = '1'; // Sostituisci con un ID cantiere valido nel tuo database\n          selectedCantiereName = 'Cantiere Debug';\n\n          // Salva l'ID e il nome del cantiere nel localStorage\n          localStorage.setItem('selectedCantiereId', selectedCantiereId);\n          localStorage.setItem('selectedCantiereName', selectedCantiereName);\n          console.log('Salvato ID cantiere hardcoded nel localStorage:', selectedCantiereId);\n        }\n\n        // Verifica finale\n        if (!selectedCantiereId) {\n          setError('Nessun cantiere selezionato. Torna alla pagina dei cantieri.');\n          setLoading(false);\n          return;\n        }\n\n        // Verifica che l'ID del cantiere sia un numero valido\n        const cantiereIdNum = parseInt(selectedCantiereId, 10);\n        console.log('ID cantiere convertito a numero:', cantiereIdNum);\n        if (isNaN(cantiereIdNum)) {\n          setError(`ID cantiere non valido: ${selectedCantiereId}. Torna alla pagina dei cantieri.`);\n          setLoading(false);\n          return;\n        }\n\n        // Usa il numero convertito, non la stringa\n        setCantiereId(cantiereIdNum);\n        setCantiereName(selectedCantiereName || `Cantiere ${cantiereIdNum}`);\n\n        // Carica le revisioni disponibili\n        await loadRevisioni(cantiereIdNum);\n\n\n\n        // Carica i cavi attivi con gestione degli errori migliorata\n        console.log('Caricamento cavi attivi per cantiere:', cantiereIdNum);\n        try {\n          // Imposta un timeout per evitare che la richiesta rimanga bloccata\n          const timeoutPromise = new Promise((_, reject) => {\n            setTimeout(() => reject(new Error('Timeout durante il caricamento dei cavi attivi')), 30000); // Aumentato a 30 secondi\n          });\n\n          // Esegui la richiesta con un timeout di sicurezza e applica i filtri\n          console.log('Iniziando chiamata API per cavi attivi con filtri:', filters);\n          const caviPromise = caviService.getCavi(cantiereIdNum, 0, filters);\n          const attivi = await Promise.race([caviPromise, timeoutPromise]);\n\n          console.log('Cavi attivi caricati:', attivi);\n          console.log('Numero di cavi attivi trovati:', attivi ? attivi.length : 0);\n          if (attivi && attivi.length > 0) {\n            console.log('Primo cavo attivo:', attivi[0]);\n          } else {\n            console.warn('Nessun cavo attivo trovato per il cantiere', cantiereIdNum);\n          }\n          setCaviAttivi(attivi || []);\n\n          // Calcola le statistiche dopo aver caricato i cavi attivi\n          calculateStatistics(attivi || [], caviSpare);\n        } catch (caviError) {\n          console.error('Errore nel caricamento dei cavi attivi:', caviError);\n          console.error('Dettagli errore cavi attivi:', {\n            message: caviError.message,\n            status: caviError.status,\n            data: caviError.data,\n            stack: caviError.stack,\n            code: caviError.code,\n            name: caviError.name,\n            response: caviError.response ? {\n              status: caviError.response.status,\n              statusText: caviError.response.statusText,\n              data: caviError.response.data\n            } : 'No response'\n          });\n\n          // Non interrompere il flusso, continua con i cavi spare\n          setCaviAttivi([]);\n          console.warn('Continuazione del flusso dopo errore nei cavi attivi');\n\n          // Aggiungi un messaggio di errore visibile all'utente\n          setError(`Errore nel caricamento dei cavi attivi: ${caviError.message}. Controlla la console per maggiori dettagli.`);\n        }\n\n        // Carica i cavi spare con gestione degli errori migliorata\n        console.log('Caricamento cavi spare per cantiere:', cantiereIdNum);\n        try {\n          // Imposta un timeout per evitare che la richiesta rimanga bloccata\n          const timeoutPromise = new Promise((_, reject) => {\n            setTimeout(() => reject(new Error('Timeout durante il caricamento dei cavi spare')), 30000); // Aumentato a 30 secondi\n          });\n\n          // Esegui la richiesta con un timeout di sicurezza\n          console.log('Iniziando chiamata API per cavi spare...');\n          // Non applichiamo i filtri ai cavi spare, solo agli attivi\n          const sparePromise = caviService.getCavi(cantiereIdNum, 3);\n          const spare = await Promise.race([sparePromise, timeoutPromise]);\n\n          console.log('Cavi spare caricati:', spare);\n          console.log('Numero di cavi spare trovati:', spare ? spare.length : 0);\n          if (spare && spare.length > 0) {\n            console.log('Primo cavo spare:', spare[0]);\n          } else {\n            console.warn('Nessun cavo spare trovato per il cantiere', cantiereIdNum);\n          }\n          setCaviSpare(spare || []);\n\n          // Calcola le statistiche dopo aver caricato i cavi spare\n          calculateStatistics(caviAttivi, spare || []);\n        } catch (spareError) {\n          console.error('Errore nel caricamento dei cavi spare:', spareError);\n          console.error('Dettagli errore cavi spare:', {\n            message: spareError.message,\n            status: spareError.status,\n            data: spareError.data,\n            stack: spareError.stack,\n            code: spareError.code,\n            name: spareError.name,\n            response: spareError.response ? {\n              status: spareError.response.status,\n              statusText: spareError.response.statusText,\n              data: spareError.response.data\n            } : 'No response'\n          });\n\n          // Non interrompere il flusso, imposta un array vuoto\n          setCaviSpare([]);\n\n          // Aggiungi un messaggio di errore visibile all'utente se non c'è già un errore per i cavi attivi\n          if (!error) {\n            setError(`Errore nel caricamento dei cavi spare: ${spareError.message}. Controlla la console per maggiori dettagli.`);\n          }\n        }\n\n        // Se siamo arrivati qui, almeno abbiamo caricato l'interfaccia di base\n        setLoading(false);\n\n      } catch (err) {\n        console.error('Errore nel caricamento dei cavi:', err);\n        console.error('Dettagli errore generale:', {\n          message: err.message,\n          status: err.status || err.response?.status,\n          data: err.data || err.response?.data,\n          stack: err.stack\n        });\n\n        // Estrai il messaggio di errore dettagliato\n        let errorMessage = 'Errore sconosciuto';\n\n        if (err.message && err.message.includes('ID cantiere non valido')) {\n          errorMessage = err.message;\n        } else if (err.status === 401 || err.status === 403 ||\n                  err.response?.status === 401 || err.response?.status === 403) {\n          errorMessage = 'Sessione scaduta o non autorizzata. Effettua nuovamente il login.';\n        } else if (err.response?.data?.detail) {\n          // Estrai il messaggio di errore dettagliato dall'API\n          errorMessage = `Errore API: ${err.response.data.detail}`;\n        } else if (err.code === 'ERR_NETWORK') {\n          // Errore di rete\n          errorMessage = 'Network Error. Verifica che il backend sia in esecuzione e accessibile.';\n        } else if (err.message) {\n          errorMessage = err.message;\n        }\n\n        setError(`Impossibile caricare i cavi: ${errorMessage}. Riprova più tardi.`);\n\n        // Imposta array vuoti per evitare errori di rendering\n        setCaviAttivi([]);\n        setCaviSpare([]);\n      } finally {\n        setLoading(false);\n      }\n    };\n\n    fetchData();\n  }, [filters]); // Ricarica i dati quando cambiano i filtri\n\n  // I filtri sono ora gestiti dal componente CaviFilterableTable\n\n  // Funzione per aprire il dialogo dei dettagli del cavo\n  const handleOpenDetails = (cavo) => {\n    setSelectedCavo(cavo);\n    setDetailsDialogOpen(true);\n  };\n\n  // Funzione per chiudere il dialogo dei dettagli del cavo\n  const handleCloseDetails = () => {\n    setDetailsDialogOpen(false);\n    setSelectedCavo(null);\n  };\n\n  // Funzione per chiudere la notifica\n  const handleCloseNotification = () => {\n    setNotification(prev => ({ ...prev, open: false }));\n  };\n\n  // Funzione per mostrare una notifica\n  const showNotification = (message, severity = 'success') => {\n    setNotification({ open: true, message, severity });\n  };\n\n  // Funzioni per gestire la selezione dei cavi\n  const handleSelectionToggle = () => {\n    setSelectionEnabled(!selectionEnabled);\n    // Pulisci le selezioni quando si disabilita la modalità selezione\n    if (selectionEnabled) {\n      setSelectedCaviAttivi([]);\n      setSelectedCaviSpare([]);\n    }\n  };\n\n  const handleCaviAttiviSelectionChange = (selectedIds) => {\n    setSelectedCaviAttivi(selectedIds);\n  };\n\n  const handleCaviSpareSelectionChange = (selectedIds) => {\n    setSelectedCaviSpare(selectedIds);\n  };\n\n  // Funzione per ottenere tutti i cavi selezionati\n  const getAllSelectedCavi = () => {\n    const selectedAttiviCavi = caviAttivi.filter(cavo => selectedCaviAttivi.includes(cavo.id_cavo));\n    const selectedSpareCavi = caviSpare.filter(cavo => selectedCaviSpare.includes(cavo.id_cavo));\n    return [...selectedAttiviCavi, ...selectedSpareCavi];\n  };\n\n  // Funzione per ottenere il conteggio totale dei cavi selezionati\n  const getTotalSelectedCount = () => {\n    return selectedCaviAttivi.length + selectedCaviSpare.length;\n  };\n\n  // Funzioni per gestire le azioni del menu contestuale\n  const handleContextMenuAction = (cavo, action) => {\n    console.log('Azione menu contestuale:', action, 'per cavo:', cavo);\n\n    switch (action) {\n      case 'view_details':\n        handleOpenDetails(cavo);\n        break;\n      case 'edit':\n        // Funzionalità di modifica cavo non ancora implementata\n        showNotification('Funzionalità di modifica cavo in sviluppo', 'info');\n        break;\n      case 'delete':\n        // Funzionalità di eliminazione cavo non ancora implementata\n        showNotification('Funzionalità di eliminazione cavo in sviluppo', 'info');\n        break;\n      case 'select':\n        if (caviAttivi.some(c => c.id_cavo === cavo.id_cavo)) {\n          // È un cavo attivo\n          const isSelected = selectedCaviAttivi.includes(cavo.id_cavo);\n          if (isSelected) {\n            setSelectedCaviAttivi(prev => prev.filter(id => id !== cavo.id_cavo));\n          } else {\n            setSelectedCaviAttivi(prev => [...prev, cavo.id_cavo]);\n          }\n        } else {\n          // È un cavo spare\n          const isSelected = selectedCaviSpare.includes(cavo.id_cavo);\n          if (isSelected) {\n            setSelectedCaviSpare(prev => prev.filter(id => id !== cavo.id_cavo));\n          } else {\n            setSelectedCaviSpare(prev => [...prev, cavo.id_cavo]);\n          }\n        }\n        // Abilita automaticamente la modalità selezione se non è già attiva\n        if (!selectionEnabled) {\n          setSelectionEnabled(true);\n        }\n        break;\n      case 'copy_id':\n        navigator.clipboard.writeText(cavo.id_cavo);\n        showNotification(`ID cavo ${cavo.id_cavo} copiato negli appunti`, 'success');\n        break;\n      case 'copy_details':\n        const details = `ID: ${cavo.id_cavo}\\nTipologia: ${cavo.tipologia}\\nSezione: ${cavo.sezione}\\nMetri: ${cavo.metri_teorici}`;\n        navigator.clipboard.writeText(details);\n        showNotification('Dettagli cavo copiati negli appunti', 'success');\n        break;\n      case 'add_new':\n        // Funzionalità di aggiunta cavo non ancora implementata\n        showNotification('Funzionalità di aggiunta cavo in sviluppo', 'info');\n        break;\n      default:\n        console.warn('Azione non riconosciuta:', action);\n    }\n  };\n\n  // Definizione degli elementi del menu contestuale\n  const getContextMenuItems = (cavo) => {\n    const isSelected = caviAttivi.some(c => c.id_cavo === cavo?.id_cavo)\n      ? selectedCaviAttivi.includes(cavo?.id_cavo)\n      : selectedCaviSpare.includes(cavo?.id_cavo);\n\n    return [\n      {\n        type: 'header',\n        label: `Cavo ${cavo?.id_cavo || ''}`\n      },\n      {\n        id: 'view_details',\n        label: 'Visualizza Dettagli',\n        icon: <VisibilityIcon fontSize=\"small\" />,\n        action: 'view_details',\n        onClick: handleContextMenuAction\n      },\n      {\n        type: 'divider'\n      },\n      {\n        id: 'edit',\n        label: 'Modifica',\n        icon: <EditIcon fontSize=\"small\" />,\n        action: 'edit',\n        onClick: handleContextMenuAction,\n        color: 'primary'\n      },\n      {\n        id: 'delete',\n        label: 'Elimina',\n        icon: <DeleteIcon fontSize=\"small\" />,\n        action: 'delete',\n        onClick: handleContextMenuAction,\n        color: 'error'\n      },\n      {\n        type: 'divider'\n      },\n      {\n        id: 'add_new',\n        label: 'Aggiungi nuovo cavo',\n        icon: <AddIcon fontSize=\"small\" />,\n        action: 'add_new',\n        onClick: handleContextMenuAction,\n        color: 'success'\n      },\n      {\n        type: 'divider'\n      },\n      {\n        id: 'select',\n        label: isSelected ? 'Deseleziona' : 'Seleziona',\n        icon: <SelectAllIcon fontSize=\"small\" />,\n        action: 'select',\n        onClick: handleContextMenuAction,\n        color: isSelected ? 'warning' : 'success'\n      },\n      {\n        type: 'divider'\n      },\n      {\n        id: 'copy_id',\n        label: 'Copia ID',\n        icon: <CopyIcon fontSize=\"small\" />,\n        action: 'copy_id',\n        onClick: handleContextMenuAction,\n        shortcut: 'Ctrl+C'\n      },\n      {\n        id: 'copy_details',\n        label: 'Copia Dettagli',\n        icon: <CopyIcon fontSize=\"small\" />,\n        action: 'copy_details',\n        onClick: handleContextMenuAction,\n        description: 'Copia ID, tipologia, sezione e metri'\n      }\n    ];\n  };\n\n  // Funzioni per gestire le azioni sui pulsanti stato\n  const handleStatusAction = async (cavo, actionType, actionLabel) => {\n    console.log('🎯 CLICK PULSANTE STATO RILEVATO!');\n    console.log('Azione pulsante stato:', actionType, 'per cavo:', cavo);\n    console.log('Action label:', actionLabel);\n\n    if (actionType === 'insert_meters') {\n      // Apri il dialogo per inserire i metri posati\n      setInserisciMetriDialog({\n        open: true,\n        cavo: cavo,\n        loading: false\n      });\n    } else if (actionType === 'modify_reel') {\n      // Apri il dialog completo per modificare la bobina con cavo preselezionato\n      console.log('Apertura dialog modifica bobina per cavo:', cavo.id_cavo);\n      setModificaBobinaDialog({\n        open: true,\n        cavo: cavo,\n        loading: false\n      });\n    } else if (actionType === 'connect_cable' || actionType === 'connect_arrival' ||\n               actionType === 'connect_departure' || actionType === 'disconnect_cable' ||\n               actionType === 'manage_connections') {\n\n      // Verifica se il cavo è installato\n      if (cavo.stato_installazione !== 'Installato') {\n        console.log('Azione collegamenti ignorata per cavo non installato:', cavo.id_cavo);\n        showNotification('I collegamenti sono disponibili solo per cavi installati', 'info');\n        return;\n      }\n\n      // Cavo installato - apri il popup per gestire i collegamenti\n      console.log('Apertura popup collegamenti per cavo installato:', cavo.id_cavo, 'azione:', actionType);\n\n      // Usa setTimeout per evitare conflitti di stato\n      setTimeout(() => {\n        setCollegamentiDialog({\n          open: true,\n          cavo: cavo,\n          loading: false\n        });\n      }, 50);\n    } else if (actionType === 'create_certificate') {\n      // Apri il dialog per creare una certificazione usando il componente esistente\n      console.log('Apertura dialog creazione certificazione per cavo:', cavo.id_cavo);\n\n      // Usa il componente CertificazioneCavi esistente con tutti i prerequisiti\n      if (certificazioneRef.current) {\n        certificazioneRef.current.createCertificationForCavo(cavo);\n      }\n    } else if (actionType === 'view_certificate') {\n      // Apri il dialog per visualizzare la certificazione esistente\n      console.log('Apertura dialog visualizzazione certificazione per cavo:', cavo.id_cavo);\n\n      // Usa il componente CertificazioneCavi esistente per visualizzare\n      if (certificazioneRef.current) {\n        certificazioneRef.current.viewCertificationForCavo(cavo);\n      }\n    } else if (actionType === 'generate_pdf') {\n      // Genera il PDF del certificato per il cavo\n      console.log('Generazione PDF certificato per cavo:', cavo.id_cavo);\n\n      // Usa il componente CertificazioneCavi esistente per generare il PDF\n      if (certificazioneRef.current) {\n        certificazioneRef.current.generatePdfForCavo(cavo);\n      }\n    }\n  };\n\n\n\n\n  // Funzioni per gestire i callback del dialog modifica bobina\n  const handleModificaBobinaSuccess = (message) => {\n    showNotification(message, 'success');\n    // Ricarica i dati per aggiornare lo stato\n    setTimeout(() => fetchCavi(true), 500);\n  };\n\n  const handleModificaBobinaError = (message) => {\n    showNotification(message, 'error');\n  };\n\n  // Funzioni per chiudere i dialoghi\n  const handleCloseInserisciMetri = () => {\n    if (!inserisciMetriDialog.loading) {\n      setInserisciMetriDialog({ open: false, cavo: null, loading: false });\n    }\n  };\n\n  const handleCloseModificaBobina = () => {\n    if (!modificaBobinaDialog.loading) {\n      setModificaBobinaDialog({ open: false, cavo: null, loading: false });\n    }\n  };\n\n  const handleCloseCollegamenti = useCallback(() => {\n    if (!collegamentiDialog.loading) {\n      setCollegamentiDialog(prev => ({ ...prev, open: false }));\n    }\n  }, [collegamentiDialog.loading]);\n\n\n\n\n\n  // Nessuna funzione di navigazione necessaria, tutto è gestito dal menu principale\n\n  // Dashboard minimal con statistiche essenziali per visualizzazione cavi\n  const renderDashboard = () => (\n    <Paper sx={{ p: 2, mb: 3, bgcolor: 'grey.50' }}>\n      <Stack direction=\"row\" spacing={4} alignItems=\"center\" justifyContent=\"space-between\" flexWrap=\"wrap\">\n        {/* Statistiche essenziali in formato compatto */}\n        <Stack direction=\"row\" alignItems=\"center\" spacing={1}>\n          <CableIcon color=\"primary\" fontSize=\"small\" />\n          <Box>\n            <Typography variant=\"h6\" fontWeight=\"bold\" sx={{ lineHeight: 1 }}>\n              {statistics.totaleCavi}\n            </Typography>\n            <Typography variant=\"caption\" color=\"text.secondary\">\n              Totale\n            </Typography>\n          </Box>\n        </Stack>\n\n        <Stack direction=\"row\" alignItems=\"center\" spacing={1}>\n          <CheckCircleIcon color=\"success\" fontSize=\"small\" />\n          <Box>\n            <Typography variant=\"h6\" fontWeight=\"bold\" sx={{ lineHeight: 1 }}>\n              {statistics.caviInstallati}\n            </Typography>\n            <Typography variant=\"caption\" color=\"text.secondary\">\n              Installati\n            </Typography>\n          </Box>\n        </Stack>\n\n        <Stack direction=\"row\" alignItems=\"center\" spacing={1}>\n          <LinkIcon color=\"info\" fontSize=\"small\" />\n          <Box>\n            <Typography variant=\"h6\" fontWeight=\"bold\" sx={{ lineHeight: 1 }}>\n              {statistics.caviCollegati}\n            </Typography>\n            <Typography variant=\"caption\" color=\"text.secondary\">\n              Collegati\n            </Typography>\n          </Box>\n        </Stack>\n\n        <Stack direction=\"row\" alignItems=\"center\" spacing={1}>\n          <VerifiedIcon color=\"warning\" fontSize=\"small\" />\n          <Box>\n            <Typography variant=\"h6\" fontWeight=\"bold\" sx={{ lineHeight: 1 }}>\n              {statistics.caviCertificati}\n            </Typography>\n            <Typography variant=\"caption\" color=\"text.secondary\">\n              Certificati\n            </Typography>\n          </Box>\n        </Stack>\n\n        <Stack direction=\"row\" alignItems=\"center\" spacing={1}>\n          <Box sx={{\n            width: 32,\n            height: 32,\n            borderRadius: '50%',\n            bgcolor: statistics.iap >= 80 ? 'success.main' :\n                     statistics.iap >= 50 ? 'warning.main' : 'error.main',\n            display: 'flex',\n            alignItems: 'center',\n            justifyContent: 'center'\n          }}>\n            <Typography variant=\"caption\" fontWeight=\"bold\" color=\"white\">\n              {statistics.iap}%\n            </Typography>\n          </Box>\n          <Box>\n            <Typography variant=\"body2\" fontWeight=\"medium\" sx={{ lineHeight: 1 }}>\n              IAP (Avanzamento Ponderato)\n            </Typography>\n            <Typography variant=\"caption\" color=\"text.secondary\">\n              {statistics.metriInstallati}m installati\n            </Typography>\n          </Box>\n        </Stack>\n      </Stack>\n    </Paper>\n  );\n\n  // La visualizzazione dei cavi è ora gestita dal componente CaviFilterableTable\n\n  // Rimossa funzione handleViewModeChange\n\n  // Renderizza il dialogo dei dettagli del cavo\n  const renderDetailsDialog = () => {\n    if (!selectedCavo) return null;\n\n    return (\n      <Dialog open={detailsDialogOpen} onClose={handleCloseDetails} maxWidth=\"md\" fullWidth>\n        <DialogTitle>\n          Dettagli Cavo: {selectedCavo.id_cavo}\n        </DialogTitle>\n        <DialogContent dividers>\n          <Grid container spacing={2}>\n            <Grid item xs={12} md={6}>\n              <Typography variant=\"subtitle1\" gutterBottom>Informazioni Generali</Typography>\n              <Box sx={{ mb: 2 }}>\n                <Typography variant=\"body2\"><strong>Sistema:</strong> {selectedCavo.sistema || 'N/A'}</Typography>\n                <Typography variant=\"body2\"><strong>Utility:</strong> {selectedCavo.utility || 'N/A'}</Typography>\n                <Typography variant=\"body2\"><strong>Tipologia:</strong> {selectedCavo.tipologia || 'N/A'}</Typography>\n                <Typography variant=\"body2\"><strong>Colore:</strong> {selectedCavo.colore_cavo || 'N/A'}</Typography>\n                {/* n_conduttori field is now a spare field (kept in DB but hidden in UI) */}\n                <Typography variant=\"body2\"><strong>Formazione:</strong> {selectedCavo.sezione || 'N/A'}</Typography>\n                {/* sh field is now a spare field (kept in DB but hidden in UI) */}\n              </Box>\n\n              <Typography variant=\"subtitle1\" gutterBottom>Partenza</Typography>\n              <Box sx={{ mb: 2 }}>\n                <Typography variant=\"body2\"><strong>Ubicazione:</strong> {selectedCavo.ubicazione_partenza || 'N/A'}</Typography>\n                <Typography variant=\"body2\"><strong>Utenza:</strong> {selectedCavo.utenza_partenza || 'N/A'}</Typography>\n                <Typography variant=\"body2\"><strong>Descrizione:</strong> {selectedCavo.descrizione_utenza_partenza || 'N/A'}</Typography>\n                <Typography variant=\"body2\"><strong>Responsabile:</strong> {selectedCavo.responsabile_partenza || 'N/A'}</Typography>\n                <Typography variant=\"body2\"><strong>Comanda:</strong> {selectedCavo.comanda_partenza || 'N/A'}</Typography>\n              </Box>\n            </Grid>\n\n            <Grid item xs={12} md={6}>\n              <Typography variant=\"subtitle1\" gutterBottom>Arrivo</Typography>\n              <Box sx={{ mb: 2 }}>\n                <Typography variant=\"body2\"><strong>Ubicazione:</strong> {selectedCavo.ubicazione_arrivo || 'N/A'}</Typography>\n                <Typography variant=\"body2\"><strong>Utenza:</strong> {selectedCavo.utenza_arrivo || 'N/A'}</Typography>\n                <Typography variant=\"body2\"><strong>Descrizione:</strong> {selectedCavo.descrizione_utenza_arrivo || 'N/A'}</Typography>\n                <Typography variant=\"body2\"><strong>Responsabile:</strong> {selectedCavo.responsabile_arrivo || 'N/A'}</Typography>\n                <Typography variant=\"body2\"><strong>Comanda:</strong> {selectedCavo.comanda_arrivo || 'N/A'}</Typography>\n              </Box>\n\n              <Typography variant=\"subtitle1\" gutterBottom>Installazione</Typography>\n              <Box sx={{ mb: 2 }}>\n                <Typography variant=\"body2\"><strong>Metri Teorici:</strong> {selectedCavo.metri_teorici || 'N/A'}</Typography>\n                <Typography variant=\"body2\"><strong>Metratura Reale:</strong> {selectedCavo.metratura_reale || '0'}</Typography>\n                <Typography variant=\"body2\"><strong>Stato:</strong> {normalizeInstallationStatus(selectedCavo.stato_installazione)}</Typography>\n                <Typography variant=\"body2\"><strong>Collegamenti:</strong> {selectedCavo.collegamenti || '0'}</Typography>\n                <Typography variant=\"body2\"><strong>Bobina:</strong> {selectedCavo.id_bobina || 'N/A'}</Typography>\n                <Typography variant=\"body2\"><strong>Responsabile Posa:</strong> {selectedCavo.responsabile_posa || 'N/A'}</Typography>\n                <Typography variant=\"body2\"><strong>Comanda Posa:</strong> {selectedCavo.comanda_posa || 'N/A'}</Typography>\n                <Typography variant=\"body2\"><strong>Ultimo Aggiornamento:</strong> {new Date(selectedCavo.timestamp).toLocaleString()}</Typography>\n              </Box>\n            </Grid>\n          </Grid>\n        </DialogContent>\n        <DialogActions>\n          <Button onClick={handleCloseDetails}>Chiudi</Button>\n        </DialogActions>\n      </Dialog>\n    );\n  };\n\n  // Il pannello dei filtri è ora gestito dal componente CaviFilterableTable\n\n\n\n  return (\n    <Box className=\"cavi-page\">\n      {loading ? (\n        <Box sx={{ display: 'flex', flexDirection: 'column', alignItems: 'center', mt: 4 }}>\n          <CircularProgress size={40} />\n          <Typography sx={{ mt: 2 }}>Caricamento cavi...</Typography>\n          <Button\n            variant=\"outlined\"\n            color=\"primary\"\n            onClick={() => window.location.reload()}\n            sx={{ mt: 2 }}\n          >\n            Ricarica la pagina\n          </Button>\n        </Box>\n      ) : error ? (\n        <Box>\n          <Alert severity=\"error\" sx={{ mb: 2 }}>\n            {error}\n            {error.includes('Network Error') && (\n              <Typography variant=\"body2\" sx={{ mt: 1 }}>\n                <strong>Suggerimento:</strong> Verifica che il server backend sia in esecuzione sulla porta 8001.\n                <br />\n                Puoi avviare il backend eseguendo il file <code>run_system.py</code> nella cartella principale del progetto.\n              </Typography>\n            )}\n          </Alert>\n          <Box sx={{ display: 'flex', gap: 2 }}>\n            <Button\n              variant=\"contained\"\n              className=\"primary-button\"\n              onClick={() => window.location.reload()}\n            >\n              Ricarica la pagina\n            </Button>\n          </Box>\n        </Box>\n      ) : (\n        <Box>\n          {/* Selettore Revisione */}\n          {revisioniDisponibili.length > 0 && (\n            <Paper sx={{ p: 2, mb: 3 }}>\n              <Box sx={{ display: 'flex', alignItems: 'center', gap: 2 }}>\n                <Typography variant=\"h6\">Visualizzazione:</Typography>\n                <FormControl size=\"small\" sx={{ minWidth: 250 }}>\n                  <InputLabel>Revisione da Visualizzare</InputLabel>\n                  <Select\n                    value={revisioneSelezionata || 'corrente'}\n                    onChange={handleRevisioneChange}\n                    label=\"Revisione da Visualizzare\"\n                  >\n                    <MenuItem value=\"corrente\">\n                      📋 Revisione Corrente {revisioneCorrente && `(${revisioneCorrente})`}\n                    </MenuItem>\n                    {revisioniDisponibili.map((rev) => (\n                      <MenuItem key={rev.revisione} value={rev.revisione}>\n                        📚 {rev.revisione} ({rev.cavi_count} cavi)\n                        {rev.revisione === revisioneCorrente && ' - Attuale'}\n                      </MenuItem>\n                    ))}\n                  </Select>\n                </FormControl>\n                <Chip\n                  label={\n                    revisioneSelezionata\n                      ? `Storico: ${revisioneSelezionata}`\n                      : `Corrente: ${revisioneCorrente || 'N/A'}`\n                  }\n                  color={revisioneSelezionata ? \"secondary\" : \"primary\"}\n                  variant=\"outlined\"\n                />\n              </Box>\n            </Paper>\n          )}\n\n          {/* Dashboard con statistiche avanzate */}\n          {renderDashboard()}\n\n          {/* Sezione Cavi */}\n          <Box sx={{ mt: 4 }}>\n            {/* Contatore selezione - solo quando ci sono cavi selezionati */}\n            {selectionEnabled && getTotalSelectedCount() > 0 && (\n              <Box sx={{ mb: 2 }}>\n                <Chip\n                  label={`${getTotalSelectedCount()} cavi selezionati`}\n                  color=\"primary\"\n                  variant=\"outlined\"\n                />\n              </Box>\n            )}\n\n            {/* Debug: Mostra le proprietà del primo cavo per verificare il nome del campo revisione */}\n            {process.env.NODE_ENV === 'development' && caviAttivi.length > 0 && (\n              <Box sx={{ mb: 2, p: 1, bgcolor: '#f0f0f0', borderRadius: 1, fontSize: '0.8rem', fontFamily: 'monospace', display: 'none' }}>\n                {Object.keys(caviAttivi[0]).map(key => (\n                  <div key={key}>{key}: {JSON.stringify(caviAttivi[0][key])}</div>\n                ))}\n              </Box>\n            )}\n\n            <CaviFilterableTable\n              cavi={caviAttivi}\n              loading={loading}\n              onFilteredDataChange={(filteredData) => console.log('Cavi attivi filtrati:', filteredData.length)}\n              revisioneCorrente={caviAttivi[0]?.revisione_ufficiale || caviAttivi[0]?.revisione || caviAttivi[0]?.rev}\n              selectionEnabled={selectionEnabled}\n              selectedCavi={selectedCaviAttivi}\n              onSelectionChange={handleCaviAttiviSelectionChange}\n              onSelectionToggle={handleSelectionToggle}\n              contextMenuItems={getContextMenuItems}\n              onContextMenuAction={handleContextMenuAction}\n              onStatusAction={handleStatusAction}\n            />\n            {caviAttivi.length === 0 && !loading && (\n              <Alert severity=\"info\" sx={{ mt: 2 }}>\n                Nessun cavo attivo trovato. I cavi attivi appariranno qui.\n              </Alert>\n            )}\n          </Box>\n\n          {/* Sezione Cavi Spare */}\n          <Box sx={{ mt: 4 }}>\n            <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 2 }}>\n              <Typography variant=\"h5\">\n                Cavi Spare {caviSpare.length > 0 ? `(${caviSpare.length})` : ''}\n              </Typography>\n            </Box>\n            <CaviFilterableTable\n              cavi={caviSpare}\n              loading={loading}\n              onFilteredDataChange={(filteredData) => console.log('Cavi spare filtrati:', filteredData.length)}\n              selectionEnabled={selectionEnabled}\n              selectedCavi={selectedCaviSpare}\n              onSelectionChange={handleCaviSpareSelectionChange}\n              onSelectionToggle={handleSelectionToggle}\n              contextMenuItems={getContextMenuItems}\n              onContextMenuAction={handleContextMenuAction}\n              onStatusAction={handleStatusAction}\n            />\n            {caviSpare.length === 0 && !loading && (\n              <Alert severity=\"info\" sx={{ mt: 2 }}>\n                Nessun cavo SPARE trovato. I cavi marcati come SPARE appariranno qui.\n              </Alert>\n            )}\n          </Box>\n\n          {/* Rimossa sezione Debug */}\n\n          {/* Dialogo dei dettagli del cavo */}\n          {renderDetailsDialog()}\n\n          {/* OBSOLETO: Dialog eliminazione cavo rimosso - Funzionalità integrata nel menu contestuale */}\n\n          {/* OBSOLETO: Dialog modifica cavo rimosso - Funzionalità integrata nel menu contestuale */}\n\n          {/* OBSOLETO: Dialog aggiunta cavo rimosso - Funzionalità integrata nel menu contestuale */}\n\n          {/* Dialoghi per azioni sui pulsanti stato */}\n          <InserisciMetriDialogCompleto\n            open={inserisciMetriDialog.open}\n            onClose={handleCloseInserisciMetri}\n            cavo={inserisciMetriDialog.cavo}\n            cantiereId={cantiereId}\n            onSuccess={(message) => {\n              showNotification(message, 'success');\n              // Ricarica i dati per aggiornare lo stato\n              setTimeout(() => fetchCavi(true), 500);\n            }}\n            onError={(message) => {\n              showNotification(message, 'error');\n            }}\n            loading={inserisciMetriDialog.loading}\n          />\n\n          <ModificaBobinaDialogCompleto\n            open={modificaBobinaDialog.open}\n            onClose={handleCloseModificaBobina}\n            cavo={modificaBobinaDialog.cavo}\n            cantiereId={cantiereId}\n            onSuccess={handleModificaBobinaSuccess}\n            onError={handleModificaBobinaError}\n          />\n\n          {/* Dialog per la gestione collegamenti */}\n          <Dialog\n            open={collegamentiDialog.open}\n            onClose={handleCloseCollegamenti}\n            maxWidth=\"md\"\n            fullWidth\n            disableEscapeKeyDown={false}\n            keepMounted={false}\n            disablePortal={false}\n            disableScrollLock={true}\n            hideBackdrop={false}\n            disableAutoFocus={true}\n            disableEnforceFocus={true}\n            disableRestoreFocus={true}\n            transitionDuration={0}\n            TransitionProps={{\n              timeout: 0,\n              appear: false,\n              enter: false,\n              exit: false\n            }}\n            PaperProps={{\n              style: {\n                transition: 'none',\n                transform: 'none'\n              }\n            }}\n          >\n            <DialogContent>\n              {collegamentiDialog.cavo && (\n                <CollegamentiCavo\n                  cantiereId={cantiereId}\n                  selectedCavo={collegamentiDialog.cavo}\n                  onSuccess={(message) => {\n                    if (message) {\n                      showNotification(message, 'success');\n                      // Chiudi il dialog immediatamente\n                      setCollegamentiDialog(prev => ({ ...prev, open: false }));\n                      // Ricarica i dati per aggiornare lo stato dei collegamenti\n                      setTimeout(() => fetchCavi(true), 300);\n                    }\n                    // Non chiudere il dialog se message è null (annullamento)\n                  }}\n                  onError={(message) => {\n                    showNotification(message, 'error');\n                  }}\n                  onClose={handleCloseCollegamenti}\n                />\n              )}\n            </DialogContent>\n          </Dialog>\n\n          {/* Componente CertificazioneCaviImproved per i dialog completi */}\n          <Box sx={{ display: 'none' }}>\n            <CertificazioneCaviImproved\n              ref={certificazioneRef}\n              cantiereId={cantiereId}\n              onSuccess={(message) => {\n                showNotification(message, 'success');\n                // Ricarica i dati per aggiornare lo stato di certificazione\n                setTimeout(() => fetchCavi(true), 500);\n              }}\n              onError={(message) => {\n                showNotification(message, 'error');\n              }}\n            />\n          </Box>\n\n          {/* Snackbar per le notifiche */}\n          <Snackbar\n            open={notification.open}\n            autoHideDuration={4000}\n            onClose={handleCloseNotification}\n            anchorOrigin={{ vertical: 'bottom', horizontal: 'center' }}\n          >\n            <Alert onClose={handleCloseNotification} severity={notification.severity} sx={{ width: '100%' }}>\n              {notification.message}\n            </Alert>\n          </Snackbar>\n        </Box>\n      )}\n    </Box>\n  );\n};\n\nexport default VisualizzaCaviPage;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,EAAEC,WAAW,EAAEC,MAAM,QAAQ,OAAO;AACvE,SACEC,GAAG,EACHC,UAAU,EACVC,KAAK,EACLC,MAAM,EACNC,IAAI,EACJC,IAAI,EACJC,WAAW,EACXC,KAAK,EACLC,UAAU,EACVC,IAAI,EACJC,gBAAgB,EAChBC,cAAc,EACdC,MAAM,EACNC,WAAW,EACXC,aAAa,EACbC,aAAa,EACbC,QAAQ,EACRC,WAAW,EACXC,UAAU,EACVC,MAAM,EACNC,QAAQ,EACRC,KAAK,QACA,eAAe;AACtB,OAAOC,QAAQ,MAAM,0BAA0B;AAC/C,SACEC,KAAK,IAAIC,SAAS,EAClBC,WAAW,IAAIC,eAAe,EAC9BC,QAAQ,IAAIC,YAAY,EACxBC,IAAI,IAAIC,QAAQ,EAChBC,OAAO,IAAIC,WAAW,EACtBC,QAAQ,IAAIC,YAAY,EACxBC,QAAQ,IAAIC,YAAY,EACxBC,oBAAoB,IAAIC,wBAAwB,EAChDC,UAAU,IAAIC,cAAc,EAC5BC,IAAI,IAAIC,QAAQ,EAChBC,MAAM,IAAIC,UAAU,EACpBC,GAAG,IAAIC,OAAO,EACdC,SAAS,IAAIC,aAAa,EAC1BC,WAAW,IAAIC,QAAQ,EACvBC,QAAQ,IAAIC,YAAY,EACxBC,QAAQ,IAAIC,YAAY,QACnB,qBAAqB;AAC5B,SAASC,WAAW,QAAQ,kBAAkB;AAC9C,SAASC,OAAO,QAAQ,2BAA2B;AACnD,SAASC,gBAAgB,QAAQ,6BAA6B;AAC9D;AACA,OAAOC,WAAW,MAAM,4BAA4B;AACpD,OAAOC,gBAAgB,MAAM,iCAAiC;AAC9D,OAAOC,QAAQ,MAAM,gCAAgC;AACrD,SAASC,2BAA2B,QAAQ,6BAA6B;AACzE,OAAOC,mBAAmB,MAAM,2CAA2C;AAC3E,OAAOC,4BAA4B,MAAM,oDAAoD;AAC7F,OAAOC,4BAA4B,MAAM,oDAAoD;AAC7F,OAAOC,gBAAgB,MAAM,wCAAwC;AACrE,OAAOC,0BAA0B,MAAM,kDAAkD;AAEzF,OAAO,gBAAgB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAExB,MAAMC,kBAAkB,GAAGA,CAAA,KAAM;EAAAC,EAAA;EAAA,IAAAC,YAAA,EAAAC,aAAA,EAAAC,aAAA;EAC/B,MAAM;IAAEC,eAAe;IAAEC;EAAK,CAAC,GAAGnB,OAAO,CAAC,CAAC;EAC3C,MAAM;IAAEoB,qBAAqB;IAAEC,wBAAwB;IAAEC,sBAAsB;IAAEC,yBAAyB;IAAEC,sBAAsB;IAAEC;EAA0B,CAAC,GAAGxB,gBAAgB,CAAC,CAAC;EACpL,MAAMyB,QAAQ,GAAG3B,WAAW,CAAC,CAAC;EAC9B,MAAM,CAAC4B,UAAU,EAAEC,aAAa,CAAC,GAAGxF,QAAQ,CAAC,IAAI,CAAC;EAClD,MAAM,CAACyF,YAAY,EAAEC,eAAe,CAAC,GAAG1F,QAAQ,CAAC,EAAE,CAAC;EACpD,MAAM,CAAC2F,UAAU,EAAEC,aAAa,CAAC,GAAG5F,QAAQ,CAAC,EAAE,CAAC;EAChD,MAAM,CAAC6F,SAAS,EAAEC,YAAY,CAAC,GAAG9F,QAAQ,CAAC,EAAE,CAAC;EAC9C,MAAM,CAAC+F,OAAO,EAAEC,UAAU,CAAC,GAAGhG,QAAQ,CAAC,IAAI,CAAC;EAC5C,MAAM,CAACiG,KAAK,EAAEC,QAAQ,CAAC,GAAGlG,QAAQ,CAAC,IAAI,CAAC;EACxC;EACA,MAAM,CAACmG,YAAY,EAAEC,eAAe,CAAC,GAAGpG,QAAQ,CAAC;IAAEqG,IAAI,EAAE,KAAK;IAAEC,OAAO,EAAE,EAAE;IAAEC,QAAQ,EAAE;EAAU,CAAC,CAAC;EACnG;;EAEA;EACA,MAAM,CAACC,YAAY,EAAEC,eAAe,CAAC,GAAGzG,QAAQ,CAAC,IAAI,CAAC;EACtD,MAAM,CAAC0G,iBAAiB,EAAEC,oBAAoB,CAAC,GAAG3G,QAAQ,CAAC,KAAK,CAAC;;EAEjE;EACA,MAAM,CAAC4G,gBAAgB,EAAEC,mBAAmB,CAAC,GAAG7G,QAAQ,CAAC,KAAK,CAAC;EAC/D,MAAM,CAAC8G,kBAAkB,EAAEC,qBAAqB,CAAC,GAAG/G,QAAQ,CAAC,EAAE,CAAC;EAChE,MAAM,CAACgH,iBAAiB,EAAEC,oBAAoB,CAAC,GAAGjH,QAAQ,CAAC,EAAE,CAAC;;EAE9D;EACA,MAAM,CAACkH,oBAAoB,EAAEC,uBAAuB,CAAC,GAAGnH,QAAQ,CAAC;IAC/DqG,IAAI,EAAE,KAAK;IACXe,IAAI,EAAE,IAAI;IACVrB,OAAO,EAAE;EACX,CAAC,CAAC;EAEF,MAAM,CAACsB,kBAAkB,EAAEC,qBAAqB,CAAC,GAAGtH,QAAQ,CAAC;IAC3DqG,IAAI,EAAE,KAAK;IACXe,IAAI,EAAE,IAAI;IACVrB,OAAO,EAAE;EACX,CAAC,CAAC;EACF,MAAM,CAACwB,oBAAoB,EAAEC,uBAAuB,CAAC,GAAGxH,QAAQ,CAAC;IAC/DqG,IAAI,EAAE,KAAK;IACXe,IAAI,EAAE,IAAI;IACVrB,OAAO,EAAE;EACX,CAAC,CAAC;;EAEF;EACA,MAAM0B,iBAAiB,GAAGtH,MAAM,CAAC,IAAI,CAAC;;EAItC;EACA,MAAM,CAACuH,UAAU,EAAEC,aAAa,CAAC,GAAG3H,QAAQ,CAAC;IAC3C4H,UAAU,EAAE,CAAC;IACbC,cAAc,EAAE,CAAC;IACjBC,gBAAgB,EAAE,CAAC;IACnBC,WAAW,EAAE,CAAC;IACdC,aAAa,EAAE,CAAC;IAChBC,gBAAgB,EAAE,CAAC;IACnBC,eAAe,EAAE,CAAC;IAClBC,kBAAkB,EAAE,CAAC;IACrBC,wBAAwB,EAAE,CAAC;IAC3BC,uBAAuB,EAAE,CAAC;IAC1BC,yBAAyB,EAAE,CAAC;IAC5BC,GAAG,EAAE,CAAC;IAAE;IACRC,WAAW,EAAE,CAAC;IACdC,eAAe,EAAE,CAAC;IAClBC,cAAc,EAAE;EAClB,CAAC,CAAC;;EAIF;EACA,MAAM,CAACC,oBAAoB,EAAEC,uBAAuB,CAAC,GAAG5I,QAAQ,CAAC,EAAE,CAAC;EACpE,MAAM,CAAC6I,oBAAoB,EAAEC,uBAAuB,CAAC,GAAG9I,QAAQ,CAAC,EAAE,CAAC;EACpE,MAAM,CAAC+I,iBAAiB,EAAEC,oBAAoB,CAAC,GAAGhJ,QAAQ,CAAC,EAAE,CAAC;;EAE9D;;EAEA;EACA,MAAMiJ,YAAY,GAAGA,CAACC,IAAI,EAAEC,KAAK,EAAEC,KAAK,EAAEC,KAAK,KAAK;IAClD;IACA,MAAMC,EAAE,GAAG,GAAG,CAAC,CAAE;IACjB,MAAMC,EAAE,GAAG,GAAG,CAAC,CAAE;IACjB,MAAMC,EAAE,GAAG,GAAG,CAAC,CAAE;;IAEjB;IACA,IAAIN,IAAI,KAAK,CAAC,EAAE,OAAO,CAAC;;IAExB;IACA,MAAMO,oBAAoB,GAAG,CAACN,KAAK,GAAGC,KAAK,IAAIE,EAAE;IACjD,MAAMI,mBAAmB,GAAG,CAACN,KAAK,GAAGC,KAAK,KAAKC,EAAE,GAAGC,EAAE,CAAC;IACvD,MAAMI,iBAAiB,GAAGN,KAAK,IAAIC,EAAE,GAAGC,EAAE,GAAGC,EAAE,CAAC;IAChD,MAAMI,UAAU,GAAGH,oBAAoB,GAAGC,mBAAmB,GAAGC,iBAAiB;;IAEjF;IACA,MAAME,YAAY,GAAGX,IAAI,IAAII,EAAE,GAAGC,EAAE,GAAGC,EAAE,CAAC;;IAE1C;IACA,MAAMjB,GAAG,GAAIqB,UAAU,GAAGC,YAAY,GAAI,GAAG;IAE7CC,OAAO,CAACC,GAAG,CAAC,cAAc,EAAE;MAC1Bb,IAAI;MAAEC,KAAK;MAAEC,KAAK;MAAEC,KAAK;MACzBW,IAAI,EAAE;QAAEV,EAAE;QAAEC,EAAE;QAAEC;MAAG,CAAC;MACpBC,oBAAoB;MACpBC,mBAAmB;MACnBC,iBAAiB;MACjBC,UAAU;MACVC,YAAY;MACZtB,GAAG,EAAE0B,IAAI,CAACC,KAAK,CAAC3B,GAAG,GAAG,GAAG,CAAC,GAAG;IAC/B,CAAC,CAAC;IAEF,OAAO0B,IAAI,CAACC,KAAK,CAAC3B,GAAG,GAAG,GAAG,CAAC,GAAG,GAAG,CAAC,CAAC;EACtC,CAAC;;EAED;EACA,MAAM4B,mBAAmB,GAAGA,CAACC,cAAc,EAAEC,aAAa,KAAK;IAC7D,MAAMC,SAAS,GAAG,CAAC,IAAIF,cAAc,IAAI,EAAE,CAAC,EAAE,IAAIC,aAAa,IAAI,EAAE,CAAC,CAAC;IAEvE,IAAIC,SAAS,CAACC,MAAM,KAAK,CAAC,EAAE;MAC1BT,OAAO,CAACC,GAAG,CAAC,0DAA0D,CAAC;MACvE;IACF;IAEAD,OAAO,CAACC,GAAG,CAAC,+BAA+B,EAAE;MAC3CpE,UAAU,EAAE,CAAAyE,cAAc,aAAdA,cAAc,uBAAdA,cAAc,CAAEG,MAAM,KAAI,CAAC;MACvC1E,SAAS,EAAE,CAAAwE,aAAa,aAAbA,aAAa,uBAAbA,aAAa,CAAEE,MAAM,KAAI,CAAC;MACrCC,MAAM,EAAEF,SAAS,CAACC;IACpB,CAAC,CAAC;IAEF,MAAM3C,UAAU,GAAG0C,SAAS,CAACC,MAAM;;IAEnC;IACA,MAAM1C,cAAc,GAAGyC,SAAS,CAACG,MAAM,CAACrD,IAAI,IAC1CA,IAAI,CAACsD,mBAAmB,KAAK,YAAY,IACzCtD,IAAI,CAACsD,mBAAmB,KAAK,YAAY,IACzCtD,IAAI,CAACsD,mBAAmB,KAAK,QAC/B,CAAC,CAACH,MAAM;IAER,MAAMzC,gBAAgB,GAAGwC,SAAS,CAACG,MAAM,CAACrD,IAAI,IAC5CA,IAAI,CAACsD,mBAAmB,KAAK,eAAe,IAC5CtD,IAAI,CAACsD,mBAAmB,KAAK,eAC/B,CAAC,CAACH,MAAM;IAER,MAAMxC,WAAW,GAAGuC,SAAS,CAACG,MAAM,CAACrD,IAAI,IACvCA,IAAI,CAACsD,mBAAmB,KAAK,UAAU,IACvCtD,IAAI,CAACsD,mBAAmB,KAAK,UAC/B,CAAC,CAACH,MAAM;;IAER;IACA,MAAMvC,aAAa,GAAGsC,SAAS,CAACG,MAAM,CAACrD,IAAI,IACzCA,IAAI,CAACuD,YAAY,KAAK,CAAC,IACvBvD,IAAI,CAACwD,qBAAqB,IAC1BxD,IAAI,CAACyD,mBACP,CAAC,CAACN,MAAM;IAER,MAAMtC,gBAAgB,GAAGL,UAAU,GAAGI,aAAa;;IAEnD;IACA,MAAME,eAAe,GAAGoC,SAAS,CAACG,MAAM,CAACrD,IAAI,IAAIA,IAAI,CAAC0D,WAAW,KAAK,IAAI,IAAI1D,IAAI,CAAC0D,WAAW,KAAK,MAAM,CAAC,CAACP,MAAM;;IAEjH;IACA,MAAMhC,GAAG,GAAGU,YAAY,CAACrB,UAAU,EAAEC,cAAc,EAAEG,aAAa,EAAEE,eAAe,CAAC;;IAEpF;IACA,MAAME,wBAAwB,GAAGG,GAAG,CAAC,CAAC;IACtC,MAAMF,uBAAuB,GAAGT,UAAU,GAAG,CAAC,GAAGqC,IAAI,CAACC,KAAK,CAAElC,aAAa,GAAGJ,UAAU,GAAI,GAAG,CAAC,GAAG,CAAC;;IAEnG;IACA,MAAMY,WAAW,GAAG8B,SAAS,CAACS,MAAM,CAAC,CAACC,GAAG,EAAE5D,IAAI,KAAK4D,GAAG,IAAIC,UAAU,CAAC7D,IAAI,CAAC8D,aAAa,CAAC,IAAI,CAAC,CAAC,EAAE,CAAC,CAAC;IACnG,MAAMzC,eAAe,GAAG6B,SAAS,CAC9BG,MAAM,CAACrD,IAAI,IAAIA,IAAI,CAACsD,mBAAmB,KAAK,YAAY,IAAItD,IAAI,CAACsD,mBAAmB,KAAK,YAAY,IAAItD,IAAI,CAACsD,mBAAmB,KAAK,QAAQ,CAAC,CAC/IK,MAAM,CAAC,CAACC,GAAG,EAAE5D,IAAI,KAAK4D,GAAG,IAAIC,UAAU,CAAC7D,IAAI,CAAC+D,eAAe,CAAC,IAAIF,UAAU,CAAC7D,IAAI,CAAC8D,aAAa,CAAC,IAAI,CAAC,CAAC,EAAE,CAAC,CAAC;IAC5G,MAAMxC,cAAc,GAAGF,WAAW,GAAGC,eAAe;IACpD,MAAMN,kBAAkB,GAAGP,UAAU,GAAGM,eAAe;IACvD,MAAMI,yBAAyB,GAAGV,UAAU,GAAG,CAAC,GAAGqC,IAAI,CAACC,KAAK,CAAEhC,eAAe,GAAGN,UAAU,GAAI,GAAG,CAAC,GAAG,CAAC;IAEvG,MAAMwD,aAAa,GAAG;MACpBxD,UAAU;MACVC,cAAc;MACdC,gBAAgB;MAChBC,WAAW;MACXC,aAAa;MACbC,gBAAgB;MAChBC,eAAe;MACfC,kBAAkB;MAClBC,wBAAwB;MACxBC,uBAAuB;MACvBC,yBAAyB;MACzBC,GAAG;MAAE;MACLC,WAAW,EAAEyB,IAAI,CAACC,KAAK,CAAC1B,WAAW,CAAC;MACpCC,eAAe,EAAEwB,IAAI,CAACC,KAAK,CAACzB,eAAe,CAAC;MAC5CC,cAAc,EAAEuB,IAAI,CAACC,KAAK,CAACxB,cAAc;IAC3C,CAAC;IAEDoB,OAAO,CAACC,GAAG,CAAC,8BAA8B,EAAEqB,aAAa,CAAC;IAC1DzD,aAAa,CAACyD,aAAa,CAAC;EAC9B,CAAC;;EAED;EACA,MAAMC,sBAAsB,GAAGA,CAAA,KAAM;IACnC;IACAC,qBAAqB,CAAC,CAAC,YAAY,EAAE,eAAe,EAAE,UAAU,CAAC,CAAC;EACpE,CAAC;;EAED;EACA,MAAMC,aAAa,GAAG,MAAOC,eAAe,IAAK;IAC/C,IAAI;MACF1B,OAAO,CAACC,GAAG,CAAC,qCAAqC,EAAEyB,eAAe,CAAC;;MAEnE;MACA,MAAMC,qBAAqB,GAAG,MAAM3H,WAAW,CAAC4H,oBAAoB,CAACF,eAAe,CAAC;MACrF1B,OAAO,CAACC,GAAG,CAAC,qBAAqB,EAAE0B,qBAAqB,CAAC;MACzDzC,oBAAoB,CAACyC,qBAAqB,CAACE,kBAAkB,CAAC;;MAE9D;MACA,MAAMC,aAAa,GAAG,MAAM9H,WAAW,CAAC+H,uBAAuB,CAACL,eAAe,CAAC;MAChF1B,OAAO,CAACC,GAAG,CAAC,wBAAwB,EAAE6B,aAAa,CAAC;MACpDhD,uBAAuB,CAACgD,aAAa,CAACE,SAAS,IAAI,EAAE,CAAC;;MAEtD;MACA;MACAhC,OAAO,CAACC,GAAG,CAAC,wDAAwD,CAAC;IACvE,CAAC,CAAC,OAAO9D,KAAK,EAAE;MACd6D,OAAO,CAAC7D,KAAK,CAAC,yCAAyC,EAAEA,KAAK,CAAC;IACjE;EACF,CAAC;;EAED;EACA,MAAM8F,qBAAqB,GAAIC,KAAK,IAAK;IACvC,MAAMC,cAAc,GAAGD,KAAK,CAACE,MAAM,CAACC,KAAK;;IAEzC;IACA;IACA;IACA,IAAIF,cAAc,KAAK,EAAE,IAAIA,cAAc,KAAK,UAAU,EAAE;MAC1DnD,uBAAuB,CAAC,EAAE,CAAC;MAC3BgB,OAAO,CAACC,GAAG,CAAC,0CAA0C,CAAC;IACzD,CAAC,MAAM;MACLjB,uBAAuB,CAACmD,cAAc,CAAC;MACvCnC,OAAO,CAACC,GAAG,CAAC,gCAAgC,EAAEkC,cAAc,CAAC;IAC/D;EACF,CAAC;;EAED;EACA,MAAM,CAACG,OAAO,EAAEC,UAAU,CAAC,GAAGrM,QAAQ,CAAC;IACrC0K,mBAAmB,EAAE,EAAE;IACvB4B,SAAS,EAAE,EAAE;IACbC,OAAO,EAAE,EAAE;IACXC,UAAU,EAAE;EACd,CAAC,CAAC;;EAEF;EACA,MAAM,CAACC,kBAAkB,EAAEnB,qBAAqB,CAAC,GAAGtL,QAAQ,CAAC,EAAE,CAAC;EAChE,MAAM,CAAC0M,aAAa,EAAEC,gBAAgB,CAAC,GAAG3M,QAAQ,CAAC,EAAE,CAAC;;EAEtD;;EAEA;EACA;EACA,MAAM4M,SAAS,GAAG,MAAAA,CAAOC,aAAa,GAAG,KAAK,KAAK;IACjD,IAAI;MACF,IAAI,CAACA,aAAa,EAAE;QAClB7G,UAAU,CAAC,IAAI,CAAC;MAClB;MACA8D,OAAO,CAACC,GAAG,CAAC,gCAAgC,EAAExE,UAAU,CAAC;;MAEzD;MACA,IAAI,CAACA,UAAU,EAAE;QACfuE,OAAO,CAAC7D,KAAK,CAAC,mCAAmC,EAAEV,UAAU,CAAC;QAC9DW,QAAQ,CAAC,wDAAwD,CAAC;QAClEF,UAAU,CAAC,KAAK,CAAC;QACjB;MACF;;MAEA;MACA,IAAIwF,eAAe,GAAGjG,UAAU;MAChC,IAAI,CAACiG,eAAe,EAAE;QACpBA,eAAe,GAAGsB,YAAY,CAACC,OAAO,CAAC,oBAAoB,CAAC;QAC5DjD,OAAO,CAACC,GAAG,CAAC,qCAAqC,EAAEyB,eAAe,CAAC;QACnE,IAAI,CAACA,eAAe,EAAE;UACpB1B,OAAO,CAAC7D,KAAK,CAAC,2CAA2C,CAAC;UAC1DC,QAAQ,CAAC,8CAA8C,CAAC;UACxDF,UAAU,CAAC,KAAK,CAAC;UACjB;QACF;MACF;;MAEA;MACA8D,OAAO,CAACC,GAAG,CAAC,0CAA0C,CAAC;MACvD,IAAIiD,MAAM,GAAG,EAAE;MACf,IAAI;QACFA,MAAM,GAAG,MAAMlJ,WAAW,CAACmJ,OAAO,CAACzB,eAAe,EAAE,CAAC,EAAEY,OAAO,CAAC;QAC/DtC,OAAO,CAACC,GAAG,CAAC,uBAAuB,EAAEiD,MAAM,GAAGA,MAAM,CAACzC,MAAM,GAAG,CAAC,CAAC;MAClE,CAAC,CAAC,OAAO2C,WAAW,EAAE;QACpBpD,OAAO,CAAC7D,KAAK,CAAC,yCAAyC,EAAEiH,WAAW,CAAC;QACrE;QACAF,MAAM,GAAG,EAAE;MACb;;MAEA;MACA,IAAIA,MAAM,IAAIA,MAAM,CAACzC,MAAM,GAAG,CAAC,EAAE;QAC/B,MAAM4C,kBAAkB,GAAGH,MAAM,CAACvC,MAAM,CAACrD,IAAI,IAAIA,IAAI,CAACgG,sBAAsB,KAAK,CAAC,CAAC;QACnF,IAAID,kBAAkB,CAAC5C,MAAM,GAAG,CAAC,EAAE;UACjCT,OAAO,CAAC7D,KAAK,CAAC,wEAAwE,EAAEkH,kBAAkB,CAAC;QAC7G;MACF;MAEAvH,aAAa,CAACoH,MAAM,IAAI,EAAE,CAAC;;MAE3B;MACA,IAAIK,KAAK,GAAG,EAAE;MACd,IAAI;QACFvD,OAAO,CAACC,GAAG,CAAC,iDAAiD,CAAC;QAC9DsD,KAAK,GAAG,MAAMvJ,WAAW,CAACwJ,YAAY,CAAC9B,eAAe,CAAC;QACvD1B,OAAO,CAACC,GAAG,CAAC,4CAA4C,EAAEsD,KAAK,GAAGA,KAAK,CAAC9C,MAAM,GAAG,CAAC,CAAC;QACnF,IAAI8C,KAAK,IAAIA,KAAK,CAAC9C,MAAM,GAAG,CAAC,EAAE;UAC7BT,OAAO,CAACC,GAAG,CAAC,mBAAmB,EAAEsD,KAAK,CAAC,CAAC,CAAC,CAAC;QAC5C;MACF,CAAC,CAAC,OAAOE,UAAU,EAAE;QACnBzD,OAAO,CAAC7D,KAAK,CAAC,8DAA8D,EAAEsH,UAAU,CAAC;QACzF;QACA,IAAI;UACFzD,OAAO,CAACC,GAAG,CAAC,kCAAkC,CAAC;UAC/CsD,KAAK,GAAG,MAAMvJ,WAAW,CAACmJ,OAAO,CAACzB,eAAe,EAAE,CAAC,CAAC;UACrD1B,OAAO,CAACC,GAAG,CAAC,0CAA0C,EAAEsD,KAAK,GAAGA,KAAK,CAAC9C,MAAM,GAAG,CAAC,CAAC;QACnF,CAAC,CAAC,OAAOiD,aAAa,EAAE;UACtB1D,OAAO,CAAC7D,KAAK,CAAC,mCAAmC,EAAEuH,aAAa,CAAC;UACjE;UACAH,KAAK,GAAG,EAAE;QACZ;MACF;MACAvH,YAAY,CAACuH,KAAK,IAAI,EAAE,CAAC;;MAIzB;MACAnH,QAAQ,CAAC,EAAE,CAAC;IACd,CAAC,CAAC,OAAOD,KAAK,EAAE;MACd6D,OAAO,CAAC7D,KAAK,CAAC,2CAA2C,EAAEA,KAAK,CAAC;MACjEC,QAAQ,CAAC,oCAAoCD,KAAK,CAACK,OAAO,IAAI,oBAAoB,EAAE,CAAC;;MAErF;MACAmH,UAAU,CAAC,MAAM;QACf;QACA,IAAIC,QAAQ,CAACC,IAAI,CAACC,WAAW,CAACC,QAAQ,CAAC,iCAAiC,CAAC,EAAE;UACzE/D,OAAO,CAACC,GAAG,CAAC,gEAAgE,CAAC;UAC7E+D,MAAM,CAACC,QAAQ,CAACC,MAAM,CAAC,CAAC;QAC1B;MACF,CAAC,EAAE,IAAI,CAAC,CAAC,CAAC;IACZ,CAAC,SAAS;MACR,IAAI,CAACnB,aAAa,EAAE;QAClB7G,UAAU,CAAC,KAAK,CAAC;MACnB;IACF;EACF,CAAC;;EAED;EACA/F,SAAS,CAAC,MAAM;IACd;IACAoL,sBAAsB,CAAC,CAAC;IAExB,MAAM4C,SAAS,GAAG,MAAAA,CAAA,KAAY;MAC5B,IAAI;QACFnE,OAAO,CAACC,GAAG,CAAC,wCAAwC,CAAC;;QAErD;QACA,MAAMmE,KAAK,GAAGpB,YAAY,CAACC,OAAO,CAAC,OAAO,CAAC;QAC3CjD,OAAO,CAACC,GAAG,CAAC,iBAAiB,EAAE,CAAC,CAACmE,KAAK,CAAC;QACvC,IAAI,CAACA,KAAK,EAAE;UACVhI,QAAQ,CAAC,iDAAiD,CAAC;UAC3DF,UAAU,CAAC,KAAK,CAAC;UACjB;QACF;;QAEA;QACA,IAAImI,kBAAkB,GAAGrB,YAAY,CAACC,OAAO,CAAC,oBAAoB,CAAC;QACnE,IAAIqB,oBAAoB,GAAGtB,YAAY,CAACC,OAAO,CAAC,sBAAsB,CAAC;QAEvEjD,OAAO,CAACC,GAAG,CAAC,wCAAwC,EAAE;UAAEoE,kBAAkB;UAAEC;QAAqB,CAAC,CAAC;QACnGtE,OAAO,CAACC,GAAG,CAAC,cAAc,EAAEhF,IAAI,CAAC;;QAEjC;QACA+E,OAAO,CAACC,GAAG,CAAC,wCAAwC,CAAC;QACrD,KAAK,IAAIsE,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGvB,YAAY,CAACvC,MAAM,EAAE8D,CAAC,EAAE,EAAE;UAC5C,MAAMC,GAAG,GAAGxB,YAAY,CAACwB,GAAG,CAACD,CAAC,CAAC;UAC/BvE,OAAO,CAACC,GAAG,CAAC,GAAGuE,GAAG,KAAKxB,YAAY,CAACC,OAAO,CAACuB,GAAG,CAAC,EAAE,CAAC;QACrD;;QAEA;QACA,IAAI,CAAAvJ,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAEwJ,IAAI,MAAK,eAAe,EAAE;UAClCzE,OAAO,CAACC,GAAG,CAAC,6EAA6E,CAAC;;UAE1F;UACA,IAAIhF,IAAI,CAACyJ,WAAW,EAAE;YACpB1E,OAAO,CAACC,GAAG,CAAC,sCAAsC,EAAEhF,IAAI,CAACyJ,WAAW,CAAC;YACrEL,kBAAkB,GAAGpJ,IAAI,CAACyJ,WAAW,CAACC,QAAQ,CAAC,CAAC;YAChDL,oBAAoB,GAAGrJ,IAAI,CAAC2J,aAAa,IAAI,YAAY3J,IAAI,CAACyJ,WAAW,EAAE;;YAE3E;YACA1B,YAAY,CAAC6B,OAAO,CAAC,oBAAoB,EAAER,kBAAkB,CAAC;YAC9DrB,YAAY,CAAC6B,OAAO,CAAC,sBAAsB,EAAEP,oBAAoB,CAAC;YAClEtE,OAAO,CAACC,GAAG,CAAC,uCAAuC,EAAEoE,kBAAkB,CAAC;UAC1E,CAAC,MAAM;YACL;YACA,IAAI;cACFrE,OAAO,CAACC,GAAG,CAAC,qEAAqE,CAAC;cAClF,MAAMmE,KAAK,GAAGpB,YAAY,CAACC,OAAO,CAAC,OAAO,CAAC;cAC3C,IAAImB,KAAK,EAAE;gBACT;gBACA,MAAMU,SAAS,GAAGV,KAAK,CAACW,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC;gBACrC,MAAMC,MAAM,GAAGF,SAAS,CAACG,OAAO,CAAC,IAAI,EAAE,GAAG,CAAC,CAACA,OAAO,CAAC,IAAI,EAAE,GAAG,CAAC;gBAC9D,MAAMC,WAAW,GAAGC,kBAAkB,CAACC,IAAI,CAACJ,MAAM,CAAC,CAACD,KAAK,CAAC,EAAE,CAAC,CAACM,GAAG,CAACC,CAAC,IAAI;kBACrE,OAAO,GAAG,GAAG,CAAC,IAAI,GAAGA,CAAC,CAACC,UAAU,CAAC,CAAC,CAAC,CAACZ,QAAQ,CAAC,EAAE,CAAC,EAAEa,KAAK,CAAC,CAAC,CAAC,CAAC;gBAC9D,CAAC,CAAC,CAACC,IAAI,CAAC,EAAE,CAAC,CAAC;gBAEZ,MAAMC,OAAO,GAAGC,IAAI,CAACC,KAAK,CAACV,WAAW,CAAC;gBACvClF,OAAO,CAACC,GAAG,CAAC,wBAAwB,EAAEyF,OAAO,CAAC;gBAE9C,IAAIA,OAAO,CAAChB,WAAW,EAAE;kBACvB1E,OAAO,CAACC,GAAG,CAAC,oCAAoC,EAAEyF,OAAO,CAAChB,WAAW,CAAC;kBACtEL,kBAAkB,GAAGqB,OAAO,CAAChB,WAAW,CAACC,QAAQ,CAAC,CAAC;kBACnD;kBACAL,oBAAoB,GAAG,YAAYoB,OAAO,CAAChB,WAAW,EAAE;;kBAExD;kBACA1B,YAAY,CAAC6B,OAAO,CAAC,oBAAoB,EAAER,kBAAkB,CAAC;kBAC9DrB,YAAY,CAAC6B,OAAO,CAAC,sBAAsB,EAAEP,oBAAoB,CAAC;kBAClEtE,OAAO,CAACC,GAAG,CAAC,uCAAuC,EAAEoE,kBAAkB,CAAC;gBAC1E;cACF;YACF,CAAC,CAAC,OAAOwB,CAAC,EAAE;cACV7F,OAAO,CAAC7D,KAAK,CAAC,6CAA6C,EAAE0J,CAAC,CAAC;YACjE;UACF;QACF;;QAEA;QACA,IAAI,CAACxB,kBAAkB,IAAIA,kBAAkB,KAAK,WAAW,IAAIA,kBAAkB,KAAK,MAAM,EAAE;UAC9FrE,OAAO,CAAC8F,IAAI,CAAC,6EAA6E,CAAC;UAC3F;UACAzB,kBAAkB,GAAG,GAAG,CAAC,CAAC;UAC1BC,oBAAoB,GAAG,gBAAgB;;UAEvC;UACAtB,YAAY,CAAC6B,OAAO,CAAC,oBAAoB,EAAER,kBAAkB,CAAC;UAC9DrB,YAAY,CAAC6B,OAAO,CAAC,sBAAsB,EAAEP,oBAAoB,CAAC;UAClEtE,OAAO,CAACC,GAAG,CAAC,iDAAiD,EAAEoE,kBAAkB,CAAC;QACpF;;QAEA;QACA,IAAI,CAACA,kBAAkB,EAAE;UACvBjI,QAAQ,CAAC,8DAA8D,CAAC;UACxEF,UAAU,CAAC,KAAK,CAAC;UACjB;QACF;;QAEA;QACA,MAAM6J,aAAa,GAAGC,QAAQ,CAAC3B,kBAAkB,EAAE,EAAE,CAAC;QACtDrE,OAAO,CAACC,GAAG,CAAC,kCAAkC,EAAE8F,aAAa,CAAC;QAC9D,IAAIE,KAAK,CAACF,aAAa,CAAC,EAAE;UACxB3J,QAAQ,CAAC,2BAA2BiI,kBAAkB,mCAAmC,CAAC;UAC1FnI,UAAU,CAAC,KAAK,CAAC;UACjB;QACF;;QAEA;QACAR,aAAa,CAACqK,aAAa,CAAC;QAC5BnK,eAAe,CAAC0I,oBAAoB,IAAI,YAAYyB,aAAa,EAAE,CAAC;;QAEpE;QACA,MAAMtE,aAAa,CAACsE,aAAa,CAAC;;QAIlC;QACA/F,OAAO,CAACC,GAAG,CAAC,uCAAuC,EAAE8F,aAAa,CAAC;QACnE,IAAI;UACF;UACA,MAAMG,cAAc,GAAG,IAAIC,OAAO,CAAC,CAACC,CAAC,EAAEC,MAAM,KAAK;YAChD1C,UAAU,CAAC,MAAM0C,MAAM,CAAC,IAAIC,KAAK,CAAC,gDAAgD,CAAC,CAAC,EAAE,KAAK,CAAC,CAAC,CAAC;UAChG,CAAC,CAAC;;UAEF;UACAtG,OAAO,CAACC,GAAG,CAAC,oDAAoD,EAAEqC,OAAO,CAAC;UAC1E,MAAMiE,WAAW,GAAGvM,WAAW,CAACmJ,OAAO,CAAC4C,aAAa,EAAE,CAAC,EAAEzD,OAAO,CAAC;UAClE,MAAMY,MAAM,GAAG,MAAMiD,OAAO,CAACK,IAAI,CAAC,CAACD,WAAW,EAAEL,cAAc,CAAC,CAAC;UAEhElG,OAAO,CAACC,GAAG,CAAC,uBAAuB,EAAEiD,MAAM,CAAC;UAC5ClD,OAAO,CAACC,GAAG,CAAC,gCAAgC,EAAEiD,MAAM,GAAGA,MAAM,CAACzC,MAAM,GAAG,CAAC,CAAC;UACzE,IAAIyC,MAAM,IAAIA,MAAM,CAACzC,MAAM,GAAG,CAAC,EAAE;YAC/BT,OAAO,CAACC,GAAG,CAAC,oBAAoB,EAAEiD,MAAM,CAAC,CAAC,CAAC,CAAC;UAC9C,CAAC,MAAM;YACLlD,OAAO,CAAC8F,IAAI,CAAC,4CAA4C,EAAEC,aAAa,CAAC;UAC3E;UACAjK,aAAa,CAACoH,MAAM,IAAI,EAAE,CAAC;;UAE3B;UACA7C,mBAAmB,CAAC6C,MAAM,IAAI,EAAE,EAAEnH,SAAS,CAAC;QAC9C,CAAC,CAAC,OAAO0K,SAAS,EAAE;UAClBzG,OAAO,CAAC7D,KAAK,CAAC,yCAAyC,EAAEsK,SAAS,CAAC;UACnEzG,OAAO,CAAC7D,KAAK,CAAC,8BAA8B,EAAE;YAC5CK,OAAO,EAAEiK,SAAS,CAACjK,OAAO;YAC1BkK,MAAM,EAAED,SAAS,CAACC,MAAM;YACxBC,IAAI,EAAEF,SAAS,CAACE,IAAI;YACpBC,KAAK,EAAEH,SAAS,CAACG,KAAK;YACtBC,IAAI,EAAEJ,SAAS,CAACI,IAAI;YACpBC,IAAI,EAAEL,SAAS,CAACK,IAAI;YACpBC,QAAQ,EAAEN,SAAS,CAACM,QAAQ,GAAG;cAC7BL,MAAM,EAAED,SAAS,CAACM,QAAQ,CAACL,MAAM;cACjCM,UAAU,EAAEP,SAAS,CAACM,QAAQ,CAACC,UAAU;cACzCL,IAAI,EAAEF,SAAS,CAACM,QAAQ,CAACJ;YAC3B,CAAC,GAAG;UACN,CAAC,CAAC;;UAEF;UACA7K,aAAa,CAAC,EAAE,CAAC;UACjBkE,OAAO,CAAC8F,IAAI,CAAC,sDAAsD,CAAC;;UAEpE;UACA1J,QAAQ,CAAC,2CAA2CqK,SAAS,CAACjK,OAAO,+CAA+C,CAAC;QACvH;;QAEA;QACAwD,OAAO,CAACC,GAAG,CAAC,sCAAsC,EAAE8F,aAAa,CAAC;QAClE,IAAI;UACF;UACA,MAAMG,cAAc,GAAG,IAAIC,OAAO,CAAC,CAACC,CAAC,EAAEC,MAAM,KAAK;YAChD1C,UAAU,CAAC,MAAM0C,MAAM,CAAC,IAAIC,KAAK,CAAC,+CAA+C,CAAC,CAAC,EAAE,KAAK,CAAC,CAAC,CAAC;UAC/F,CAAC,CAAC;;UAEF;UACAtG,OAAO,CAACC,GAAG,CAAC,0CAA0C,CAAC;UACvD;UACA,MAAMgH,YAAY,GAAGjN,WAAW,CAACmJ,OAAO,CAAC4C,aAAa,EAAE,CAAC,CAAC;UAC1D,MAAMxC,KAAK,GAAG,MAAM4C,OAAO,CAACK,IAAI,CAAC,CAACS,YAAY,EAAEf,cAAc,CAAC,CAAC;UAEhElG,OAAO,CAACC,GAAG,CAAC,sBAAsB,EAAEsD,KAAK,CAAC;UAC1CvD,OAAO,CAACC,GAAG,CAAC,+BAA+B,EAAEsD,KAAK,GAAGA,KAAK,CAAC9C,MAAM,GAAG,CAAC,CAAC;UACtE,IAAI8C,KAAK,IAAIA,KAAK,CAAC9C,MAAM,GAAG,CAAC,EAAE;YAC7BT,OAAO,CAACC,GAAG,CAAC,mBAAmB,EAAEsD,KAAK,CAAC,CAAC,CAAC,CAAC;UAC5C,CAAC,MAAM;YACLvD,OAAO,CAAC8F,IAAI,CAAC,2CAA2C,EAAEC,aAAa,CAAC;UAC1E;UACA/J,YAAY,CAACuH,KAAK,IAAI,EAAE,CAAC;;UAEzB;UACAlD,mBAAmB,CAACxE,UAAU,EAAE0H,KAAK,IAAI,EAAE,CAAC;QAC9C,CAAC,CAAC,OAAOE,UAAU,EAAE;UACnBzD,OAAO,CAAC7D,KAAK,CAAC,wCAAwC,EAAEsH,UAAU,CAAC;UACnEzD,OAAO,CAAC7D,KAAK,CAAC,6BAA6B,EAAE;YAC3CK,OAAO,EAAEiH,UAAU,CAACjH,OAAO;YAC3BkK,MAAM,EAAEjD,UAAU,CAACiD,MAAM;YACzBC,IAAI,EAAElD,UAAU,CAACkD,IAAI;YACrBC,KAAK,EAAEnD,UAAU,CAACmD,KAAK;YACvBC,IAAI,EAAEpD,UAAU,CAACoD,IAAI;YACrBC,IAAI,EAAErD,UAAU,CAACqD,IAAI;YACrBC,QAAQ,EAAEtD,UAAU,CAACsD,QAAQ,GAAG;cAC9BL,MAAM,EAAEjD,UAAU,CAACsD,QAAQ,CAACL,MAAM;cAClCM,UAAU,EAAEvD,UAAU,CAACsD,QAAQ,CAACC,UAAU;cAC1CL,IAAI,EAAElD,UAAU,CAACsD,QAAQ,CAACJ;YAC5B,CAAC,GAAG;UACN,CAAC,CAAC;;UAEF;UACA3K,YAAY,CAAC,EAAE,CAAC;;UAEhB;UACA,IAAI,CAACG,KAAK,EAAE;YACVC,QAAQ,CAAC,0CAA0CqH,UAAU,CAACjH,OAAO,+CAA+C,CAAC;UACvH;QACF;;QAEA;QACAN,UAAU,CAAC,KAAK,CAAC;MAEnB,CAAC,CAAC,OAAOgL,GAAG,EAAE;QAAA,IAAAC,aAAA,EAAAC,cAAA,EAAAC,cAAA,EAAAC,cAAA,EAAAC,cAAA,EAAAC,mBAAA;QACZxH,OAAO,CAAC7D,KAAK,CAAC,kCAAkC,EAAE+K,GAAG,CAAC;QACtDlH,OAAO,CAAC7D,KAAK,CAAC,2BAA2B,EAAE;UACzCK,OAAO,EAAE0K,GAAG,CAAC1K,OAAO;UACpBkK,MAAM,EAAEQ,GAAG,CAACR,MAAM,MAAAS,aAAA,GAAID,GAAG,CAACH,QAAQ,cAAAI,aAAA,uBAAZA,aAAA,CAAcT,MAAM;UAC1CC,IAAI,EAAEO,GAAG,CAACP,IAAI,MAAAS,cAAA,GAAIF,GAAG,CAACH,QAAQ,cAAAK,cAAA,uBAAZA,cAAA,CAAcT,IAAI;UACpCC,KAAK,EAAEM,GAAG,CAACN;QACb,CAAC,CAAC;;QAEF;QACA,IAAIa,YAAY,GAAG,oBAAoB;QAEvC,IAAIP,GAAG,CAAC1K,OAAO,IAAI0K,GAAG,CAAC1K,OAAO,CAACuH,QAAQ,CAAC,wBAAwB,CAAC,EAAE;UACjE0D,YAAY,GAAGP,GAAG,CAAC1K,OAAO;QAC5B,CAAC,MAAM,IAAI0K,GAAG,CAACR,MAAM,KAAK,GAAG,IAAIQ,GAAG,CAACR,MAAM,KAAK,GAAG,IACzC,EAAAW,cAAA,GAAAH,GAAG,CAACH,QAAQ,cAAAM,cAAA,uBAAZA,cAAA,CAAcX,MAAM,MAAK,GAAG,IAAI,EAAAY,cAAA,GAAAJ,GAAG,CAACH,QAAQ,cAAAO,cAAA,uBAAZA,cAAA,CAAcZ,MAAM,MAAK,GAAG,EAAE;UACtEe,YAAY,GAAG,mEAAmE;QACpF,CAAC,MAAM,KAAAF,cAAA,GAAIL,GAAG,CAACH,QAAQ,cAAAQ,cAAA,gBAAAC,mBAAA,GAAZD,cAAA,CAAcZ,IAAI,cAAAa,mBAAA,eAAlBA,mBAAA,CAAoBE,MAAM,EAAE;UACrC;UACAD,YAAY,GAAG,eAAeP,GAAG,CAACH,QAAQ,CAACJ,IAAI,CAACe,MAAM,EAAE;QAC1D,CAAC,MAAM,IAAIR,GAAG,CAACL,IAAI,KAAK,aAAa,EAAE;UACrC;UACAY,YAAY,GAAG,yEAAyE;QAC1F,CAAC,MAAM,IAAIP,GAAG,CAAC1K,OAAO,EAAE;UACtBiL,YAAY,GAAGP,GAAG,CAAC1K,OAAO;QAC5B;QAEAJ,QAAQ,CAAC,gCAAgCqL,YAAY,sBAAsB,CAAC;;QAE5E;QACA3L,aAAa,CAAC,EAAE,CAAC;QACjBE,YAAY,CAAC,EAAE,CAAC;MAClB,CAAC,SAAS;QACRE,UAAU,CAAC,KAAK,CAAC;MACnB;IACF,CAAC;IAEDiI,SAAS,CAAC,CAAC;EACb,CAAC,EAAE,CAAC7B,OAAO,CAAC,CAAC,CAAC,CAAC;;EAEf;;EAEA;EACA,MAAMqF,iBAAiB,GAAIrK,IAAI,IAAK;IAClCX,eAAe,CAACW,IAAI,CAAC;IACrBT,oBAAoB,CAAC,IAAI,CAAC;EAC5B,CAAC;;EAED;EACA,MAAM+K,kBAAkB,GAAGA,CAAA,KAAM;IAC/B/K,oBAAoB,CAAC,KAAK,CAAC;IAC3BF,eAAe,CAAC,IAAI,CAAC;EACvB,CAAC;;EAED;EACA,MAAMkL,uBAAuB,GAAGA,CAAA,KAAM;IACpCvL,eAAe,CAACwL,IAAI,KAAK;MAAE,GAAGA,IAAI;MAAEvL,IAAI,EAAE;IAAM,CAAC,CAAC,CAAC;EACrD,CAAC;;EAED;EACA,MAAMwL,gBAAgB,GAAGA,CAACvL,OAAO,EAAEC,QAAQ,GAAG,SAAS,KAAK;IAC1DH,eAAe,CAAC;MAAEC,IAAI,EAAE,IAAI;MAAEC,OAAO;MAAEC;IAAS,CAAC,CAAC;EACpD,CAAC;;EAED;EACA,MAAMuL,qBAAqB,GAAGA,CAAA,KAAM;IAClCjL,mBAAmB,CAAC,CAACD,gBAAgB,CAAC;IACtC;IACA,IAAIA,gBAAgB,EAAE;MACpBG,qBAAqB,CAAC,EAAE,CAAC;MACzBE,oBAAoB,CAAC,EAAE,CAAC;IAC1B;EACF,CAAC;EAED,MAAM8K,+BAA+B,GAAIC,WAAW,IAAK;IACvDjL,qBAAqB,CAACiL,WAAW,CAAC;EACpC,CAAC;EAED,MAAMC,8BAA8B,GAAID,WAAW,IAAK;IACtD/K,oBAAoB,CAAC+K,WAAW,CAAC;EACnC,CAAC;;EAED;EACA,MAAME,kBAAkB,GAAGA,CAAA,KAAM;IAC/B,MAAMC,kBAAkB,GAAGxM,UAAU,CAAC8E,MAAM,CAACrD,IAAI,IAAIN,kBAAkB,CAAC+G,QAAQ,CAACzG,IAAI,CAACgL,OAAO,CAAC,CAAC;IAC/F,MAAMC,iBAAiB,GAAGxM,SAAS,CAAC4E,MAAM,CAACrD,IAAI,IAAIJ,iBAAiB,CAAC6G,QAAQ,CAACzG,IAAI,CAACgL,OAAO,CAAC,CAAC;IAC5F,OAAO,CAAC,GAAGD,kBAAkB,EAAE,GAAGE,iBAAiB,CAAC;EACtD,CAAC;;EAED;EACA,MAAMC,qBAAqB,GAAGA,CAAA,KAAM;IAClC,OAAOxL,kBAAkB,CAACyD,MAAM,GAAGvD,iBAAiB,CAACuD,MAAM;EAC7D,CAAC;;EAED;EACA,MAAMgI,uBAAuB,GAAGA,CAACnL,IAAI,EAAEoL,MAAM,KAAK;IAChD1I,OAAO,CAACC,GAAG,CAAC,0BAA0B,EAAEyI,MAAM,EAAE,WAAW,EAAEpL,IAAI,CAAC;IAElE,QAAQoL,MAAM;MACZ,KAAK,cAAc;QACjBf,iBAAiB,CAACrK,IAAI,CAAC;QACvB;MACF,KAAK,MAAM;QACT;QACAyK,gBAAgB,CAAC,2CAA2C,EAAE,MAAM,CAAC;QACrE;MACF,KAAK,QAAQ;QACX;QACAA,gBAAgB,CAAC,+CAA+C,EAAE,MAAM,CAAC;QACzE;MACF,KAAK,QAAQ;QACX,IAAIlM,UAAU,CAAC8M,IAAI,CAACrD,CAAC,IAAIA,CAAC,CAACgD,OAAO,KAAKhL,IAAI,CAACgL,OAAO,CAAC,EAAE;UACpD;UACA,MAAMM,UAAU,GAAG5L,kBAAkB,CAAC+G,QAAQ,CAACzG,IAAI,CAACgL,OAAO,CAAC;UAC5D,IAAIM,UAAU,EAAE;YACd3L,qBAAqB,CAAC6K,IAAI,IAAIA,IAAI,CAACnH,MAAM,CAACkI,EAAE,IAAIA,EAAE,KAAKvL,IAAI,CAACgL,OAAO,CAAC,CAAC;UACvE,CAAC,MAAM;YACLrL,qBAAqB,CAAC6K,IAAI,IAAI,CAAC,GAAGA,IAAI,EAAExK,IAAI,CAACgL,OAAO,CAAC,CAAC;UACxD;QACF,CAAC,MAAM;UACL;UACA,MAAMM,UAAU,GAAG1L,iBAAiB,CAAC6G,QAAQ,CAACzG,IAAI,CAACgL,OAAO,CAAC;UAC3D,IAAIM,UAAU,EAAE;YACdzL,oBAAoB,CAAC2K,IAAI,IAAIA,IAAI,CAACnH,MAAM,CAACkI,EAAE,IAAIA,EAAE,KAAKvL,IAAI,CAACgL,OAAO,CAAC,CAAC;UACtE,CAAC,MAAM;YACLnL,oBAAoB,CAAC2K,IAAI,IAAI,CAAC,GAAGA,IAAI,EAAExK,IAAI,CAACgL,OAAO,CAAC,CAAC;UACvD;QACF;QACA;QACA,IAAI,CAACxL,gBAAgB,EAAE;UACrBC,mBAAmB,CAAC,IAAI,CAAC;QAC3B;QACA;MACF,KAAK,SAAS;QACZ+L,SAAS,CAACC,SAAS,CAACC,SAAS,CAAC1L,IAAI,CAACgL,OAAO,CAAC;QAC3CP,gBAAgB,CAAC,WAAWzK,IAAI,CAACgL,OAAO,wBAAwB,EAAE,SAAS,CAAC;QAC5E;MACF,KAAK,cAAc;QACjB,MAAMW,OAAO,GAAG,OAAO3L,IAAI,CAACgL,OAAO,gBAAgBhL,IAAI,CAACkF,SAAS,cAAclF,IAAI,CAAC4L,OAAO,YAAY5L,IAAI,CAAC8D,aAAa,EAAE;QAC3H0H,SAAS,CAACC,SAAS,CAACC,SAAS,CAACC,OAAO,CAAC;QACtClB,gBAAgB,CAAC,qCAAqC,EAAE,SAAS,CAAC;QAClE;MACF,KAAK,SAAS;QACZ;QACAA,gBAAgB,CAAC,2CAA2C,EAAE,MAAM,CAAC;QACrE;MACF;QACE/H,OAAO,CAAC8F,IAAI,CAAC,0BAA0B,EAAE4C,MAAM,CAAC;IACpD;EACF,CAAC;;EAED;EACA,MAAMS,mBAAmB,GAAI7L,IAAI,IAAK;IACpC,MAAMsL,UAAU,GAAG/M,UAAU,CAAC8M,IAAI,CAACrD,CAAC,IAAIA,CAAC,CAACgD,OAAO,MAAKhL,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAEgL,OAAO,EAAC,GAChEtL,kBAAkB,CAAC+G,QAAQ,CAACzG,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAEgL,OAAO,CAAC,GAC1CpL,iBAAiB,CAAC6G,QAAQ,CAACzG,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAEgL,OAAO,CAAC;IAE7C,OAAO,CACL;MACEc,IAAI,EAAE,QAAQ;MACdC,KAAK,EAAE,QAAQ,CAAA/L,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAEgL,OAAO,KAAI,EAAE;IACpC,CAAC,EACD;MACEO,EAAE,EAAE,cAAc;MAClBQ,KAAK,EAAE,qBAAqB;MAC5BC,IAAI,eAAE5O,OAAA,CAAC5B,cAAc;QAACyQ,QAAQ,EAAC;MAAO;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC;MACzCjB,MAAM,EAAE,cAAc;MACtBkB,OAAO,EAAEnB;IACX,CAAC,EACD;MACEW,IAAI,EAAE;IACR,CAAC,EACD;MACEP,EAAE,EAAE,MAAM;MACVQ,KAAK,EAAE,UAAU;MACjBC,IAAI,eAAE5O,OAAA,CAAC1B,QAAQ;QAACuQ,QAAQ,EAAC;MAAO;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC;MACnCjB,MAAM,EAAE,MAAM;MACdkB,OAAO,EAAEnB,uBAAuB;MAChCoB,KAAK,EAAE;IACT,CAAC,EACD;MACEhB,EAAE,EAAE,QAAQ;MACZQ,KAAK,EAAE,SAAS;MAChBC,IAAI,eAAE5O,OAAA,CAACxB,UAAU;QAACqQ,QAAQ,EAAC;MAAO;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC;MACrCjB,MAAM,EAAE,QAAQ;MAChBkB,OAAO,EAAEnB,uBAAuB;MAChCoB,KAAK,EAAE;IACT,CAAC,EACD;MACET,IAAI,EAAE;IACR,CAAC,EACD;MACEP,EAAE,EAAE,SAAS;MACbQ,KAAK,EAAE,qBAAqB;MAC5BC,IAAI,eAAE5O,OAAA,CAACtB,OAAO;QAACmQ,QAAQ,EAAC;MAAO;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC;MAClCjB,MAAM,EAAE,SAAS;MACjBkB,OAAO,EAAEnB,uBAAuB;MAChCoB,KAAK,EAAE;IACT,CAAC,EACD;MACET,IAAI,EAAE;IACR,CAAC,EACD;MACEP,EAAE,EAAE,QAAQ;MACZQ,KAAK,EAAET,UAAU,GAAG,aAAa,GAAG,WAAW;MAC/CU,IAAI,eAAE5O,OAAA,CAACpB,aAAa;QAACiQ,QAAQ,EAAC;MAAO;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC;MACxCjB,MAAM,EAAE,QAAQ;MAChBkB,OAAO,EAAEnB,uBAAuB;MAChCoB,KAAK,EAAEjB,UAAU,GAAG,SAAS,GAAG;IAClC,CAAC,EACD;MACEQ,IAAI,EAAE;IACR,CAAC,EACD;MACEP,EAAE,EAAE,SAAS;MACbQ,KAAK,EAAE,UAAU;MACjBC,IAAI,eAAE5O,OAAA,CAAClB,QAAQ;QAAC+P,QAAQ,EAAC;MAAO;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC;MACnCjB,MAAM,EAAE,SAAS;MACjBkB,OAAO,EAAEnB,uBAAuB;MAChCqB,QAAQ,EAAE;IACZ,CAAC,EACD;MACEjB,EAAE,EAAE,cAAc;MAClBQ,KAAK,EAAE,gBAAgB;MACvBC,IAAI,eAAE5O,OAAA,CAAClB,QAAQ;QAAC+P,QAAQ,EAAC;MAAO;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC;MACnCjB,MAAM,EAAE,cAAc;MACtBkB,OAAO,EAAEnB,uBAAuB;MAChCsB,WAAW,EAAE;IACf,CAAC,CACF;EACH,CAAC;;EAED;EACA,MAAMC,kBAAkB,GAAG,MAAAA,CAAO1M,IAAI,EAAE2M,UAAU,EAAEC,WAAW,KAAK;IAClElK,OAAO,CAACC,GAAG,CAAC,mCAAmC,CAAC;IAChDD,OAAO,CAACC,GAAG,CAAC,wBAAwB,EAAEgK,UAAU,EAAE,WAAW,EAAE3M,IAAI,CAAC;IACpE0C,OAAO,CAACC,GAAG,CAAC,eAAe,EAAEiK,WAAW,CAAC;IAEzC,IAAID,UAAU,KAAK,eAAe,EAAE;MAClC;MACA5M,uBAAuB,CAAC;QACtBd,IAAI,EAAE,IAAI;QACVe,IAAI,EAAEA,IAAI;QACVrB,OAAO,EAAE;MACX,CAAC,CAAC;IACJ,CAAC,MAAM,IAAIgO,UAAU,KAAK,aAAa,EAAE;MACvC;MACAjK,OAAO,CAACC,GAAG,CAAC,2CAA2C,EAAE3C,IAAI,CAACgL,OAAO,CAAC;MACtE5K,uBAAuB,CAAC;QACtBnB,IAAI,EAAE,IAAI;QACVe,IAAI,EAAEA,IAAI;QACVrB,OAAO,EAAE;MACX,CAAC,CAAC;IACJ,CAAC,MAAM,IAAIgO,UAAU,KAAK,eAAe,IAAIA,UAAU,KAAK,iBAAiB,IAClEA,UAAU,KAAK,mBAAmB,IAAIA,UAAU,KAAK,kBAAkB,IACvEA,UAAU,KAAK,oBAAoB,EAAE;MAE9C;MACA,IAAI3M,IAAI,CAACsD,mBAAmB,KAAK,YAAY,EAAE;QAC7CZ,OAAO,CAACC,GAAG,CAAC,uDAAuD,EAAE3C,IAAI,CAACgL,OAAO,CAAC;QAClFP,gBAAgB,CAAC,0DAA0D,EAAE,MAAM,CAAC;QACpF;MACF;;MAEA;MACA/H,OAAO,CAACC,GAAG,CAAC,kDAAkD,EAAE3C,IAAI,CAACgL,OAAO,EAAE,SAAS,EAAE2B,UAAU,CAAC;;MAEpG;MACAtG,UAAU,CAAC,MAAM;QACfnG,qBAAqB,CAAC;UACpBjB,IAAI,EAAE,IAAI;UACVe,IAAI,EAAEA,IAAI;UACVrB,OAAO,EAAE;QACX,CAAC,CAAC;MACJ,CAAC,EAAE,EAAE,CAAC;IACR,CAAC,MAAM,IAAIgO,UAAU,KAAK,oBAAoB,EAAE;MAC9C;MACAjK,OAAO,CAACC,GAAG,CAAC,oDAAoD,EAAE3C,IAAI,CAACgL,OAAO,CAAC;;MAE/E;MACA,IAAI3K,iBAAiB,CAACwM,OAAO,EAAE;QAC7BxM,iBAAiB,CAACwM,OAAO,CAACC,0BAA0B,CAAC9M,IAAI,CAAC;MAC5D;IACF,CAAC,MAAM,IAAI2M,UAAU,KAAK,kBAAkB,EAAE;MAC5C;MACAjK,OAAO,CAACC,GAAG,CAAC,0DAA0D,EAAE3C,IAAI,CAACgL,OAAO,CAAC;;MAErF;MACA,IAAI3K,iBAAiB,CAACwM,OAAO,EAAE;QAC7BxM,iBAAiB,CAACwM,OAAO,CAACE,wBAAwB,CAAC/M,IAAI,CAAC;MAC1D;IACF,CAAC,MAAM,IAAI2M,UAAU,KAAK,cAAc,EAAE;MACxC;MACAjK,OAAO,CAACC,GAAG,CAAC,uCAAuC,EAAE3C,IAAI,CAACgL,OAAO,CAAC;;MAElE;MACA,IAAI3K,iBAAiB,CAACwM,OAAO,EAAE;QAC7BxM,iBAAiB,CAACwM,OAAO,CAACG,kBAAkB,CAAChN,IAAI,CAAC;MACpD;IACF;EACF,CAAC;;EAKD;EACA,MAAMiN,2BAA2B,GAAI/N,OAAO,IAAK;IAC/CuL,gBAAgB,CAACvL,OAAO,EAAE,SAAS,CAAC;IACpC;IACAmH,UAAU,CAAC,MAAMb,SAAS,CAAC,IAAI,CAAC,EAAE,GAAG,CAAC;EACxC,CAAC;EAED,MAAM0H,yBAAyB,GAAIhO,OAAO,IAAK;IAC7CuL,gBAAgB,CAACvL,OAAO,EAAE,OAAO,CAAC;EACpC,CAAC;;EAED;EACA,MAAMiO,yBAAyB,GAAGA,CAAA,KAAM;IACtC,IAAI,CAACrN,oBAAoB,CAACnB,OAAO,EAAE;MACjCoB,uBAAuB,CAAC;QAAEd,IAAI,EAAE,KAAK;QAAEe,IAAI,EAAE,IAAI;QAAErB,OAAO,EAAE;MAAM,CAAC,CAAC;IACtE;EACF,CAAC;EAED,MAAMyO,yBAAyB,GAAGA,CAAA,KAAM;IACtC,IAAI,CAACjN,oBAAoB,CAACxB,OAAO,EAAE;MACjCyB,uBAAuB,CAAC;QAAEnB,IAAI,EAAE,KAAK;QAAEe,IAAI,EAAE,IAAI;QAAErB,OAAO,EAAE;MAAM,CAAC,CAAC;IACtE;EACF,CAAC;EAED,MAAM0O,uBAAuB,GAAGvU,WAAW,CAAC,MAAM;IAChD,IAAI,CAACmH,kBAAkB,CAACtB,OAAO,EAAE;MAC/BuB,qBAAqB,CAACsK,IAAI,KAAK;QAAE,GAAGA,IAAI;QAAEvL,IAAI,EAAE;MAAM,CAAC,CAAC,CAAC;IAC3D;EACF,CAAC,EAAE,CAACgB,kBAAkB,CAACtB,OAAO,CAAC,CAAC;;EAMhC;;EAEA;EACA,MAAM2O,eAAe,GAAGA,CAAA,kBACtBlQ,OAAA,CAAClE,KAAK;IAACqU,EAAE,EAAE;MAAEC,CAAC,EAAE,CAAC;MAAEC,EAAE,EAAE,CAAC;MAAEC,OAAO,EAAE;IAAU,CAAE;IAAAC,QAAA,eAC7CvQ,OAAA,CAAC/C,KAAK;MAACuT,SAAS,EAAC,KAAK;MAACC,OAAO,EAAE,CAAE;MAACC,UAAU,EAAC,QAAQ;MAACC,cAAc,EAAC,eAAe;MAACC,QAAQ,EAAC,MAAM;MAAAL,QAAA,gBAEnGvQ,OAAA,CAAC/C,KAAK;QAACuT,SAAS,EAAC,KAAK;QAACE,UAAU,EAAC,QAAQ;QAACD,OAAO,EAAE,CAAE;QAAAF,QAAA,gBACpDvQ,OAAA,CAAC5C,SAAS;UAAC+R,KAAK,EAAC,SAAS;UAACN,QAAQ,EAAC;QAAO;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,eAC9CjP,OAAA,CAACpE,GAAG;UAAA2U,QAAA,gBACFvQ,OAAA,CAACnE,UAAU;YAACgV,OAAO,EAAC,IAAI;YAACC,UAAU,EAAC,MAAM;YAACX,EAAE,EAAE;cAAEY,UAAU,EAAE;YAAE,CAAE;YAAAR,QAAA,EAC9DrN,UAAU,CAACE;UAAU;YAAA0L,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACZ,CAAC,eACbjP,OAAA,CAACnE,UAAU;YAACgV,OAAO,EAAC,SAAS;YAAC1B,KAAK,EAAC,gBAAgB;YAAAoB,QAAA,EAAC;UAErD;YAAAzB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAY,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACV,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACD,CAAC,eAERjP,OAAA,CAAC/C,KAAK;QAACuT,SAAS,EAAC,KAAK;QAACE,UAAU,EAAC,QAAQ;QAACD,OAAO,EAAE,CAAE;QAAAF,QAAA,gBACpDvQ,OAAA,CAAC1C,eAAe;UAAC6R,KAAK,EAAC,SAAS;UAACN,QAAQ,EAAC;QAAO;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,eACpDjP,OAAA,CAACpE,GAAG;UAAA2U,QAAA,gBACFvQ,OAAA,CAACnE,UAAU;YAACgV,OAAO,EAAC,IAAI;YAACC,UAAU,EAAC,MAAM;YAACX,EAAE,EAAE;cAAEY,UAAU,EAAE;YAAE,CAAE;YAAAR,QAAA,EAC9DrN,UAAU,CAACG;UAAc;YAAAyL,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAChB,CAAC,eACbjP,OAAA,CAACnE,UAAU;YAACgV,OAAO,EAAC,SAAS;YAAC1B,KAAK,EAAC,gBAAgB;YAAAoB,QAAA,EAAC;UAErD;YAAAzB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAY,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACV,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACD,CAAC,eAERjP,OAAA,CAAC/C,KAAK;QAACuT,SAAS,EAAC,KAAK;QAACE,UAAU,EAAC,QAAQ;QAACD,OAAO,EAAE,CAAE;QAAAF,QAAA,gBACpDvQ,OAAA,CAACtC,QAAQ;UAACyR,KAAK,EAAC,MAAM;UAACN,QAAQ,EAAC;QAAO;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,eAC1CjP,OAAA,CAACpE,GAAG;UAAA2U,QAAA,gBACFvQ,OAAA,CAACnE,UAAU;YAACgV,OAAO,EAAC,IAAI;YAACC,UAAU,EAAC,MAAM;YAACX,EAAE,EAAE;cAAEY,UAAU,EAAE;YAAE,CAAE;YAAAR,QAAA,EAC9DrN,UAAU,CAACM;UAAa;YAAAsL,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACf,CAAC,eACbjP,OAAA,CAACnE,UAAU;YAACgV,OAAO,EAAC,SAAS;YAAC1B,KAAK,EAAC,gBAAgB;YAAAoB,QAAA,EAAC;UAErD;YAAAzB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAY,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACV,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACD,CAAC,eAERjP,OAAA,CAAC/C,KAAK;QAACuT,SAAS,EAAC,KAAK;QAACE,UAAU,EAAC,QAAQ;QAACD,OAAO,EAAE,CAAE;QAAAF,QAAA,gBACpDvQ,OAAA,CAACd,YAAY;UAACiQ,KAAK,EAAC,SAAS;UAACN,QAAQ,EAAC;QAAO;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,eACjDjP,OAAA,CAACpE,GAAG;UAAA2U,QAAA,gBACFvQ,OAAA,CAACnE,UAAU;YAACgV,OAAO,EAAC,IAAI;YAACC,UAAU,EAAC,MAAM;YAACX,EAAE,EAAE;cAAEY,UAAU,EAAE;YAAE,CAAE;YAAAR,QAAA,EAC9DrN,UAAU,CAACQ;UAAe;YAAAoL,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACjB,CAAC,eACbjP,OAAA,CAACnE,UAAU;YAACgV,OAAO,EAAC,SAAS;YAAC1B,KAAK,EAAC,gBAAgB;YAAAoB,QAAA,EAAC;UAErD;YAAAzB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAY,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACV,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACD,CAAC,eAERjP,OAAA,CAAC/C,KAAK;QAACuT,SAAS,EAAC,KAAK;QAACE,UAAU,EAAC,QAAQ;QAACD,OAAO,EAAE,CAAE;QAAAF,QAAA,gBACpDvQ,OAAA,CAACpE,GAAG;UAACuU,EAAE,EAAE;YACPa,KAAK,EAAE,EAAE;YACTC,MAAM,EAAE,EAAE;YACVC,YAAY,EAAE,KAAK;YACnBZ,OAAO,EAAEpN,UAAU,CAACa,GAAG,IAAI,EAAE,GAAG,cAAc,GACrCb,UAAU,CAACa,GAAG,IAAI,EAAE,GAAG,cAAc,GAAG,YAAY;YAC7DoN,OAAO,EAAE,MAAM;YACfT,UAAU,EAAE,QAAQ;YACpBC,cAAc,EAAE;UAClB,CAAE;UAAAJ,QAAA,eACAvQ,OAAA,CAACnE,UAAU;YAACgV,OAAO,EAAC,SAAS;YAACC,UAAU,EAAC,MAAM;YAAC3B,KAAK,EAAC,OAAO;YAAAoB,QAAA,GAC1DrN,UAAU,CAACa,GAAG,EAAC,GAClB;UAAA;YAAA+K,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAY;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACV,CAAC,eACNjP,OAAA,CAACpE,GAAG;UAAA2U,QAAA,gBACFvQ,OAAA,CAACnE,UAAU;YAACgV,OAAO,EAAC,OAAO;YAACC,UAAU,EAAC,QAAQ;YAACX,EAAE,EAAE;cAAEY,UAAU,EAAE;YAAE,CAAE;YAAAR,QAAA,EAAC;UAEvE;YAAAzB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAY,CAAC,eACbjP,OAAA,CAACnE,UAAU;YAACgV,OAAO,EAAC,SAAS;YAAC1B,KAAK,EAAC,gBAAgB;YAAAoB,QAAA,GACjDrN,UAAU,CAACe,eAAe,EAAC,cAC9B;UAAA;YAAA6K,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAY,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACV,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACD,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CACR;;EAED;;EAEA;;EAEA;EACA,MAAMmC,mBAAmB,GAAGA,CAAA,KAAM;IAChC,IAAI,CAACpP,YAAY,EAAE,OAAO,IAAI;IAE9B,oBACEhC,OAAA,CAACxD,MAAM;MAACqF,IAAI,EAAEK,iBAAkB;MAACmP,OAAO,EAAEnE,kBAAmB;MAACoE,QAAQ,EAAC,IAAI;MAACC,SAAS;MAAAhB,QAAA,gBACnFvQ,OAAA,CAACvD,WAAW;QAAA8T,QAAA,GAAC,iBACI,EAACvO,YAAY,CAAC4L,OAAO;MAAA;QAAAkB,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACzB,CAAC,eACdjP,OAAA,CAACtD,aAAa;QAAC8U,QAAQ;QAAAjB,QAAA,eACrBvQ,OAAA,CAAChE,IAAI;UAACyV,SAAS;UAAChB,OAAO,EAAE,CAAE;UAAAF,QAAA,gBACzBvQ,OAAA,CAAChE,IAAI;YAAC0V,IAAI;YAACC,EAAE,EAAE,EAAG;YAACC,EAAE,EAAE,CAAE;YAAArB,QAAA,gBACvBvQ,OAAA,CAACnE,UAAU;cAACgV,OAAO,EAAC,WAAW;cAACgB,YAAY;cAAAtB,QAAA,EAAC;YAAqB;cAAAzB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAY,CAAC,eAC/EjP,OAAA,CAACpE,GAAG;cAACuU,EAAE,EAAE;gBAAEE,EAAE,EAAE;cAAE,CAAE;cAAAE,QAAA,gBACjBvQ,OAAA,CAACnE,UAAU;gBAACgV,OAAO,EAAC,OAAO;gBAAAN,QAAA,gBAACvQ,OAAA;kBAAAuQ,QAAA,EAAQ;gBAAQ;kBAAAzB,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,KAAC,EAACjN,YAAY,CAAC8P,OAAO,IAAI,KAAK;cAAA;gBAAAhD,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAa,CAAC,eAClGjP,OAAA,CAACnE,UAAU;gBAACgV,OAAO,EAAC,OAAO;gBAAAN,QAAA,gBAACvQ,OAAA;kBAAAuQ,QAAA,EAAQ;gBAAQ;kBAAAzB,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,KAAC,EAACjN,YAAY,CAAC+P,OAAO,IAAI,KAAK;cAAA;gBAAAjD,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAa,CAAC,eAClGjP,OAAA,CAACnE,UAAU;gBAACgV,OAAO,EAAC,OAAO;gBAAAN,QAAA,gBAACvQ,OAAA;kBAAAuQ,QAAA,EAAQ;gBAAU;kBAAAzB,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,KAAC,EAACjN,YAAY,CAAC8F,SAAS,IAAI,KAAK;cAAA;gBAAAgH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAa,CAAC,eACtGjP,OAAA,CAACnE,UAAU;gBAACgV,OAAO,EAAC,OAAO;gBAAAN,QAAA,gBAACvQ,OAAA;kBAAAuQ,QAAA,EAAQ;gBAAO;kBAAAzB,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,KAAC,EAACjN,YAAY,CAACgQ,WAAW,IAAI,KAAK;cAAA;gBAAAlD,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAa,CAAC,eAErGjP,OAAA,CAACnE,UAAU;gBAACgV,OAAO,EAAC,OAAO;gBAAAN,QAAA,gBAACvQ,OAAA;kBAAAuQ,QAAA,EAAQ;gBAAW;kBAAAzB,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,KAAC,EAACjN,YAAY,CAACwM,OAAO,IAAI,KAAK;cAAA;gBAAAM,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAa,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAElG,CAAC,eAENjP,OAAA,CAACnE,UAAU;cAACgV,OAAO,EAAC,WAAW;cAACgB,YAAY;cAAAtB,QAAA,EAAC;YAAQ;cAAAzB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAY,CAAC,eAClEjP,OAAA,CAACpE,GAAG;cAACuU,EAAE,EAAE;gBAAEE,EAAE,EAAE;cAAE,CAAE;cAAAE,QAAA,gBACjBvQ,OAAA,CAACnE,UAAU;gBAACgV,OAAO,EAAC,OAAO;gBAAAN,QAAA,gBAACvQ,OAAA;kBAAAuQ,QAAA,EAAQ;gBAAW;kBAAAzB,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,KAAC,EAACjN,YAAY,CAACiQ,mBAAmB,IAAI,KAAK;cAAA;gBAAAnD,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAa,CAAC,eACjHjP,OAAA,CAACnE,UAAU;gBAACgV,OAAO,EAAC,OAAO;gBAAAN,QAAA,gBAACvQ,OAAA;kBAAAuQ,QAAA,EAAQ;gBAAO;kBAAAzB,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,KAAC,EAACjN,YAAY,CAACkQ,eAAe,IAAI,KAAK;cAAA;gBAAApD,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAa,CAAC,eACzGjP,OAAA,CAACnE,UAAU;gBAACgV,OAAO,EAAC,OAAO;gBAAAN,QAAA,gBAACvQ,OAAA;kBAAAuQ,QAAA,EAAQ;gBAAY;kBAAAzB,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,KAAC,EAACjN,YAAY,CAACmQ,2BAA2B,IAAI,KAAK;cAAA;gBAAArD,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAa,CAAC,eAC1HjP,OAAA,CAACnE,UAAU;gBAACgV,OAAO,EAAC,OAAO;gBAAAN,QAAA,gBAACvQ,OAAA;kBAAAuQ,QAAA,EAAQ;gBAAa;kBAAAzB,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,KAAC,EAACjN,YAAY,CAACoE,qBAAqB,IAAI,KAAK;cAAA;gBAAA0I,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAa,CAAC,eACrHjP,OAAA,CAACnE,UAAU;gBAACgV,OAAO,EAAC,OAAO;gBAAAN,QAAA,gBAACvQ,OAAA;kBAAAuQ,QAAA,EAAQ;gBAAQ;kBAAAzB,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,KAAC,EAACjN,YAAY,CAACoQ,gBAAgB,IAAI,KAAK;cAAA;gBAAAtD,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAa,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACxG,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACF,CAAC,eAEPjP,OAAA,CAAChE,IAAI;YAAC0V,IAAI;YAACC,EAAE,EAAE,EAAG;YAACC,EAAE,EAAE,CAAE;YAAArB,QAAA,gBACvBvQ,OAAA,CAACnE,UAAU;cAACgV,OAAO,EAAC,WAAW;cAACgB,YAAY;cAAAtB,QAAA,EAAC;YAAM;cAAAzB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAY,CAAC,eAChEjP,OAAA,CAACpE,GAAG;cAACuU,EAAE,EAAE;gBAAEE,EAAE,EAAE;cAAE,CAAE;cAAAE,QAAA,gBACjBvQ,OAAA,CAACnE,UAAU;gBAACgV,OAAO,EAAC,OAAO;gBAAAN,QAAA,gBAACvQ,OAAA;kBAAAuQ,QAAA,EAAQ;gBAAW;kBAAAzB,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,KAAC,EAACjN,YAAY,CAACqQ,iBAAiB,IAAI,KAAK;cAAA;gBAAAvD,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAa,CAAC,eAC/GjP,OAAA,CAACnE,UAAU;gBAACgV,OAAO,EAAC,OAAO;gBAAAN,QAAA,gBAACvQ,OAAA;kBAAAuQ,QAAA,EAAQ;gBAAO;kBAAAzB,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,KAAC,EAACjN,YAAY,CAACsQ,aAAa,IAAI,KAAK;cAAA;gBAAAxD,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAa,CAAC,eACvGjP,OAAA,CAACnE,UAAU;gBAACgV,OAAO,EAAC,OAAO;gBAAAN,QAAA,gBAACvQ,OAAA;kBAAAuQ,QAAA,EAAQ;gBAAY;kBAAAzB,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,KAAC,EAACjN,YAAY,CAACuQ,yBAAyB,IAAI,KAAK;cAAA;gBAAAzD,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAa,CAAC,eACxHjP,OAAA,CAACnE,UAAU;gBAACgV,OAAO,EAAC,OAAO;gBAAAN,QAAA,gBAACvQ,OAAA;kBAAAuQ,QAAA,EAAQ;gBAAa;kBAAAzB,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,KAAC,EAACjN,YAAY,CAACqE,mBAAmB,IAAI,KAAK;cAAA;gBAAAyI,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAa,CAAC,eACnHjP,OAAA,CAACnE,UAAU;gBAACgV,OAAO,EAAC,OAAO;gBAAAN,QAAA,gBAACvQ,OAAA;kBAAAuQ,QAAA,EAAQ;gBAAQ;kBAAAzB,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,KAAC,EAACjN,YAAY,CAACwQ,cAAc,IAAI,KAAK;cAAA;gBAAA1D,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAa,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACtG,CAAC,eAENjP,OAAA,CAACnE,UAAU;cAACgV,OAAO,EAAC,WAAW;cAACgB,YAAY;cAAAtB,QAAA,EAAC;YAAa;cAAAzB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAY,CAAC,eACvEjP,OAAA,CAACpE,GAAG;cAACuU,EAAE,EAAE;gBAAEE,EAAE,EAAE;cAAE,CAAE;cAAAE,QAAA,gBACjBvQ,OAAA,CAACnE,UAAU;gBAACgV,OAAO,EAAC,OAAO;gBAAAN,QAAA,gBAACvQ,OAAA;kBAAAuQ,QAAA,EAAQ;gBAAc;kBAAAzB,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,KAAC,EAACjN,YAAY,CAAC0E,aAAa,IAAI,KAAK;cAAA;gBAAAoI,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAa,CAAC,eAC9GjP,OAAA,CAACnE,UAAU;gBAACgV,OAAO,EAAC,OAAO;gBAAAN,QAAA,gBAACvQ,OAAA;kBAAAuQ,QAAA,EAAQ;gBAAgB;kBAAAzB,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,KAAC,EAACjN,YAAY,CAAC2E,eAAe,IAAI,GAAG;cAAA;gBAAAmI,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAa,CAAC,eAChHjP,OAAA,CAACnE,UAAU;gBAACgV,OAAO,EAAC,OAAO;gBAAAN,QAAA,gBAACvQ,OAAA;kBAAAuQ,QAAA,EAAQ;gBAAM;kBAAAzB,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,KAAC,EAACxP,2BAA2B,CAACuC,YAAY,CAACkE,mBAAmB,CAAC;cAAA;gBAAA4I,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAa,CAAC,eAChIjP,OAAA,CAACnE,UAAU;gBAACgV,OAAO,EAAC,OAAO;gBAAAN,QAAA,gBAACvQ,OAAA;kBAAAuQ,QAAA,EAAQ;gBAAa;kBAAAzB,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,KAAC,EAACjN,YAAY,CAACmE,YAAY,IAAI,GAAG;cAAA;gBAAA2I,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAa,CAAC,eAC1GjP,OAAA,CAACnE,UAAU;gBAACgV,OAAO,EAAC,OAAO;gBAAAN,QAAA,gBAACvQ,OAAA;kBAAAuQ,QAAA,EAAQ;gBAAO;kBAAAzB,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,KAAC,EAACjN,YAAY,CAACyQ,SAAS,IAAI,KAAK;cAAA;gBAAA3D,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAa,CAAC,eACnGjP,OAAA,CAACnE,UAAU;gBAACgV,OAAO,EAAC,OAAO;gBAAAN,QAAA,gBAACvQ,OAAA;kBAAAuQ,QAAA,EAAQ;gBAAkB;kBAAAzB,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,KAAC,EAACjN,YAAY,CAAC0Q,iBAAiB,IAAI,KAAK;cAAA;gBAAA5D,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAa,CAAC,eACtHjP,OAAA,CAACnE,UAAU;gBAACgV,OAAO,EAAC,OAAO;gBAAAN,QAAA,gBAACvQ,OAAA;kBAAAuQ,QAAA,EAAQ;gBAAa;kBAAAzB,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,KAAC,EAACjN,YAAY,CAAC2Q,YAAY,IAAI,KAAK;cAAA;gBAAA7D,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAa,CAAC,eAC5GjP,OAAA,CAACnE,UAAU;gBAACgV,OAAO,EAAC,OAAO;gBAAAN,QAAA,gBAACvQ,OAAA;kBAAAuQ,QAAA,EAAQ;gBAAqB;kBAAAzB,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,KAAC,EAAC,IAAI2D,IAAI,CAAC5Q,YAAY,CAAC6Q,SAAS,CAAC,CAACC,cAAc,CAAC,CAAC;cAAA;gBAAAhE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAa,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAChI,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACF,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACM,CAAC,eAChBjP,OAAA,CAACrD,aAAa;QAAA4T,QAAA,eACZvQ,OAAA,CAACjE,MAAM;UAACmT,OAAO,EAAEhC,kBAAmB;UAAAqD,QAAA,EAAC;QAAM;UAAAzB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACvC,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACV,CAAC;EAEb,CAAC;;EAED;;EAIA,oBACEjP,OAAA,CAACpE,GAAG;IAACmX,SAAS,EAAC,WAAW;IAAAxC,QAAA,EACvBhP,OAAO,gBACNvB,OAAA,CAACpE,GAAG;MAACuU,EAAE,EAAE;QAAEgB,OAAO,EAAE,MAAM;QAAE6B,aAAa,EAAE,QAAQ;QAAEtC,UAAU,EAAE,QAAQ;QAAEuC,EAAE,EAAE;MAAE,CAAE;MAAA1C,QAAA,gBACjFvQ,OAAA,CAAC1D,gBAAgB;QAAC4W,IAAI,EAAE;MAAG;QAAApE,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC,eAC9BjP,OAAA,CAACnE,UAAU;QAACsU,EAAE,EAAE;UAAE8C,EAAE,EAAE;QAAE,CAAE;QAAA1C,QAAA,EAAC;MAAmB;QAAAzB,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAY,CAAC,eAC3DjP,OAAA,CAACjE,MAAM;QACL8U,OAAO,EAAC,UAAU;QAClB1B,KAAK,EAAC,SAAS;QACfD,OAAO,EAAEA,CAAA,KAAM5F,MAAM,CAACC,QAAQ,CAACC,MAAM,CAAC,CAAE;QACxC2G,EAAE,EAAE;UAAE8C,EAAE,EAAE;QAAE,CAAE;QAAA1C,QAAA,EACf;MAED;QAAAzB,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAQ,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACN,CAAC,GACJxN,KAAK,gBACPzB,OAAA,CAACpE,GAAG;MAAA2U,QAAA,gBACFvQ,OAAA,CAAC7D,KAAK;QAAC4F,QAAQ,EAAC,OAAO;QAACoO,EAAE,EAAE;UAAEE,EAAE,EAAE;QAAE,CAAE;QAAAE,QAAA,GACnC9O,KAAK,EACLA,KAAK,CAAC4H,QAAQ,CAAC,eAAe,CAAC,iBAC9BrJ,OAAA,CAACnE,UAAU;UAACgV,OAAO,EAAC,OAAO;UAACV,EAAE,EAAE;YAAE8C,EAAE,EAAE;UAAE,CAAE;UAAA1C,QAAA,gBACxCvQ,OAAA;YAAAuQ,QAAA,EAAQ;UAAa;YAAAzB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC,uEAC9B,eAAAjP,OAAA;YAAA8O,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAK,CAAC,8CACoC,eAAAjP,OAAA;YAAAuQ,QAAA,EAAM;UAAa;YAAAzB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC,4CACtE;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAY,CACb;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACI,CAAC,eACRjP,OAAA,CAACpE,GAAG;QAACuU,EAAE,EAAE;UAAEgB,OAAO,EAAE,MAAM;UAAEgC,GAAG,EAAE;QAAE,CAAE;QAAA5C,QAAA,eACnCvQ,OAAA,CAACjE,MAAM;UACL8U,OAAO,EAAC,WAAW;UACnBkC,SAAS,EAAC,gBAAgB;UAC1B7D,OAAO,EAAEA,CAAA,KAAM5F,MAAM,CAACC,QAAQ,CAACC,MAAM,CAAC,CAAE;UAAA+G,QAAA,EACzC;QAED;UAAAzB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACN,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,gBAENjP,OAAA,CAACpE,GAAG;MAAA2U,QAAA,GAEDpM,oBAAoB,CAAC4B,MAAM,GAAG,CAAC,iBAC9B/F,OAAA,CAAClE,KAAK;QAACqU,EAAE,EAAE;UAAEC,CAAC,EAAE,CAAC;UAAEC,EAAE,EAAE;QAAE,CAAE;QAAAE,QAAA,eACzBvQ,OAAA,CAACpE,GAAG;UAACuU,EAAE,EAAE;YAAEgB,OAAO,EAAE,MAAM;YAAET,UAAU,EAAE,QAAQ;YAAEyC,GAAG,EAAE;UAAE,CAAE;UAAA5C,QAAA,gBACzDvQ,OAAA,CAACnE,UAAU;YAACgV,OAAO,EAAC,IAAI;YAAAN,QAAA,EAAC;UAAgB;YAAAzB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAY,CAAC,eACtDjP,OAAA,CAACnD,WAAW;YAACqW,IAAI,EAAC,OAAO;YAAC/C,EAAE,EAAE;cAAEiD,QAAQ,EAAE;YAAI,CAAE;YAAA7C,QAAA,gBAC9CvQ,OAAA,CAAClD,UAAU;cAAAyT,QAAA,EAAC;YAAyB;cAAAzB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAY,CAAC,eAClDjP,OAAA,CAACjD,MAAM;cACL4K,KAAK,EAAEtD,oBAAoB,IAAI,UAAW;cAC1CgP,QAAQ,EAAE9L,qBAAsB;cAChCoH,KAAK,EAAC,2BAA2B;cAAA4B,QAAA,gBAEjCvQ,OAAA,CAAChD,QAAQ;gBAAC2K,KAAK,EAAC,UAAU;gBAAA4I,QAAA,GAAC,kCACH,EAAChM,iBAAiB,IAAI,IAAIA,iBAAiB,GAAG;cAAA;gBAAAuK,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC5D,CAAC,EACV9K,oBAAoB,CAACwG,GAAG,CAAE2I,GAAG,iBAC5BtT,OAAA,CAAChD,QAAQ;gBAAqB2K,KAAK,EAAE2L,GAAG,CAACC,SAAU;gBAAAhD,QAAA,GAAC,eAC/C,EAAC+C,GAAG,CAACC,SAAS,EAAC,IAAE,EAACD,GAAG,CAACE,UAAU,EAAC,QACpC,EAACF,GAAG,CAACC,SAAS,KAAKhP,iBAAiB,IAAI,YAAY;cAAA,GAFvC+O,GAAG,CAACC,SAAS;gBAAAzE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAGlB,CACX,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACI,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACE,CAAC,eACdjP,OAAA,CAAC3D,IAAI;YACHsS,KAAK,EACHtK,oBAAoB,GAChB,YAAYA,oBAAoB,EAAE,GAClC,aAAaE,iBAAiB,IAAI,KAAK,EAC5C;YACD4K,KAAK,EAAE9K,oBAAoB,GAAG,WAAW,GAAG,SAAU;YACtDwM,OAAO,EAAC;UAAU;YAAA/B,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACnB,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACC;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACD,CACR,EAGAiB,eAAe,CAAC,CAAC,eAGlBlQ,OAAA,CAACpE,GAAG;QAACuU,EAAE,EAAE;UAAE8C,EAAE,EAAE;QAAE,CAAE;QAAA1C,QAAA,GAEhBnO,gBAAgB,IAAI0L,qBAAqB,CAAC,CAAC,GAAG,CAAC,iBAC9C9N,OAAA,CAACpE,GAAG;UAACuU,EAAE,EAAE;YAAEE,EAAE,EAAE;UAAE,CAAE;UAAAE,QAAA,eACjBvQ,OAAA,CAAC3D,IAAI;YACHsS,KAAK,EAAE,GAAGb,qBAAqB,CAAC,CAAC,mBAAoB;YACrDqB,KAAK,EAAC,SAAS;YACf0B,OAAO,EAAC;UAAU;YAAA/B,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACnB;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACC,CACN,EAGAwE,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,aAAa,IAAIxS,UAAU,CAAC4E,MAAM,GAAG,CAAC,iBAC9D/F,OAAA,CAACpE,GAAG;UAACuU,EAAE,EAAE;YAAEE,EAAE,EAAE,CAAC;YAAED,CAAC,EAAE,CAAC;YAAEE,OAAO,EAAE,SAAS;YAAEY,YAAY,EAAE,CAAC;YAAErC,QAAQ,EAAE,QAAQ;YAAE+E,UAAU,EAAE,WAAW;YAAEzC,OAAO,EAAE;UAAO,CAAE;UAAAZ,QAAA,EACzHsD,MAAM,CAACC,IAAI,CAAC3S,UAAU,CAAC,CAAC,CAAC,CAAC,CAACwJ,GAAG,CAACb,GAAG,iBACjC9J,OAAA;YAAAuQ,QAAA,GAAgBzG,GAAG,EAAC,IAAE,EAACmB,IAAI,CAAC8I,SAAS,CAAC5S,UAAU,CAAC,CAAC,CAAC,CAAC2I,GAAG,CAAC,CAAC;UAAA,GAA/CA,GAAG;YAAAgF,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAkD,CAChE;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACC,CACN,eAEDjP,OAAA,CAACN,mBAAmB;UAClBsU,IAAI,EAAE7S,UAAW;UACjBI,OAAO,EAAEA,OAAQ;UACjB0S,oBAAoB,EAAGC,YAAY,IAAK5O,OAAO,CAACC,GAAG,CAAC,uBAAuB,EAAE2O,YAAY,CAACnO,MAAM,CAAE;UAClGxB,iBAAiB,EAAE,EAAApE,YAAA,GAAAgB,UAAU,CAAC,CAAC,CAAC,cAAAhB,YAAA,uBAAbA,YAAA,CAAegU,mBAAmB,OAAA/T,aAAA,GAAIe,UAAU,CAAC,CAAC,CAAC,cAAAf,aAAA,uBAAbA,aAAA,CAAemT,SAAS,OAAAlT,aAAA,GAAIc,UAAU,CAAC,CAAC,CAAC,cAAAd,aAAA,uBAAbA,aAAA,CAAeiT,GAAG,CAAC;UACxGlR,gBAAgB,EAAEA,gBAAiB;UACnCgS,YAAY,EAAE9R,kBAAmB;UACjC+R,iBAAiB,EAAE9G,+BAAgC;UACnD+G,iBAAiB,EAAEhH,qBAAsB;UACzCiH,gBAAgB,EAAE9F,mBAAoB;UACtC+F,mBAAmB,EAAEzG,uBAAwB;UAC7C0G,cAAc,EAAEnF;QAAmB;UAAAR,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACpC,CAAC,EACD9N,UAAU,CAAC4E,MAAM,KAAK,CAAC,IAAI,CAACxE,OAAO,iBAClCvB,OAAA,CAAC7D,KAAK;UAAC4F,QAAQ,EAAC,MAAM;UAACoO,EAAE,EAAE;YAAE8C,EAAE,EAAE;UAAE,CAAE;UAAA1C,QAAA,EAAC;QAEtC;UAAAzB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAO,CACR;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACE,CAAC,eAGNjP,OAAA,CAACpE,GAAG;QAACuU,EAAE,EAAE;UAAE8C,EAAE,EAAE;QAAE,CAAE;QAAA1C,QAAA,gBACjBvQ,OAAA,CAACpE,GAAG;UAACuU,EAAE,EAAE;YAAEgB,OAAO,EAAE,MAAM;YAAER,cAAc,EAAE,eAAe;YAAED,UAAU,EAAE,QAAQ;YAAEL,EAAE,EAAE;UAAE,CAAE;UAAAE,QAAA,eACzFvQ,OAAA,CAACnE,UAAU;YAACgV,OAAO,EAAC,IAAI;YAAAN,QAAA,GAAC,aACZ,EAAClP,SAAS,CAAC0E,MAAM,GAAG,CAAC,GAAG,IAAI1E,SAAS,CAAC0E,MAAM,GAAG,GAAG,EAAE;UAAA;YAAA+I,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACrD;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACV,CAAC,eACNjP,OAAA,CAACN,mBAAmB;UAClBsU,IAAI,EAAE3S,SAAU;UAChBE,OAAO,EAAEA,OAAQ;UACjB0S,oBAAoB,EAAGC,YAAY,IAAK5O,OAAO,CAACC,GAAG,CAAC,sBAAsB,EAAE2O,YAAY,CAACnO,MAAM,CAAE;UACjG3D,gBAAgB,EAAEA,gBAAiB;UACnCgS,YAAY,EAAE5R,iBAAkB;UAChC6R,iBAAiB,EAAE5G,8BAA+B;UAClD6G,iBAAiB,EAAEhH,qBAAsB;UACzCiH,gBAAgB,EAAE9F,mBAAoB;UACtC+F,mBAAmB,EAAEzG,uBAAwB;UAC7C0G,cAAc,EAAEnF;QAAmB;UAAAR,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACpC,CAAC,EACD5N,SAAS,CAAC0E,MAAM,KAAK,CAAC,IAAI,CAACxE,OAAO,iBACjCvB,OAAA,CAAC7D,KAAK;UAAC4F,QAAQ,EAAC,MAAM;UAACoO,EAAE,EAAE;YAAE8C,EAAE,EAAE;UAAE,CAAE;UAAA1C,QAAA,EAAC;QAEtC;UAAAzB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAO,CACR;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACE,CAAC,EAKLmC,mBAAmB,CAAC,CAAC,eAStBpR,OAAA,CAACL,4BAA4B;QAC3BkC,IAAI,EAAEa,oBAAoB,CAACb,IAAK;QAChCwP,OAAO,EAAEtB,yBAA0B;QACnCnN,IAAI,EAAEF,oBAAoB,CAACE,IAAK;QAChC7B,UAAU,EAAEA,UAAW;QACvB2T,SAAS,EAAG5S,OAAO,IAAK;UACtBuL,gBAAgB,CAACvL,OAAO,EAAE,SAAS,CAAC;UACpC;UACAmH,UAAU,CAAC,MAAMb,SAAS,CAAC,IAAI,CAAC,EAAE,GAAG,CAAC;QACxC,CAAE;QACFuM,OAAO,EAAG7S,OAAO,IAAK;UACpBuL,gBAAgB,CAACvL,OAAO,EAAE,OAAO,CAAC;QACpC,CAAE;QACFP,OAAO,EAAEmB,oBAAoB,CAACnB;MAAQ;QAAAuN,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACvC,CAAC,eAEFjP,OAAA,CAACJ,4BAA4B;QAC3BiC,IAAI,EAAEkB,oBAAoB,CAAClB,IAAK;QAChCwP,OAAO,EAAErB,yBAA0B;QACnCpN,IAAI,EAAEG,oBAAoB,CAACH,IAAK;QAChC7B,UAAU,EAAEA,UAAW;QACvB2T,SAAS,EAAE7E,2BAA4B;QACvC8E,OAAO,EAAE7E;MAA0B;QAAAhB,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACpC,CAAC,eAGFjP,OAAA,CAACxD,MAAM;QACLqF,IAAI,EAAEgB,kBAAkB,CAAChB,IAAK;QAC9BwP,OAAO,EAAEpB,uBAAwB;QACjCqB,QAAQ,EAAC,IAAI;QACbC,SAAS;QACTqD,oBAAoB,EAAE,KAAM;QAC5BC,WAAW,EAAE,KAAM;QACnBC,aAAa,EAAE,KAAM;QACrBC,iBAAiB,EAAE,IAAK;QACxBC,YAAY,EAAE,KAAM;QACpBC,gBAAgB,EAAE,IAAK;QACvBC,mBAAmB,EAAE,IAAK;QAC1BC,mBAAmB,EAAE,IAAK;QAC1BC,kBAAkB,EAAE,CAAE;QACtBC,eAAe,EAAE;UACfC,OAAO,EAAE,CAAC;UACVC,MAAM,EAAE,KAAK;UACbC,KAAK,EAAE,KAAK;UACZC,IAAI,EAAE;QACR,CAAE;QACFC,UAAU,EAAE;UACVC,KAAK,EAAE;YACLC,UAAU,EAAE,MAAM;YAClBC,SAAS,EAAE;UACb;QACF,CAAE;QAAAtF,QAAA,eAEFvQ,OAAA,CAACtD,aAAa;UAAA6T,QAAA,EACX1N,kBAAkB,CAACD,IAAI,iBACtB5C,OAAA,CAACH,gBAAgB;YACfkB,UAAU,EAAEA,UAAW;YACvBiB,YAAY,EAAEa,kBAAkB,CAACD,IAAK;YACtC8R,SAAS,EAAG5S,OAAO,IAAK;cACtB,IAAIA,OAAO,EAAE;gBACXuL,gBAAgB,CAACvL,OAAO,EAAE,SAAS,CAAC;gBACpC;gBACAgB,qBAAqB,CAACsK,IAAI,KAAK;kBAAE,GAAGA,IAAI;kBAAEvL,IAAI,EAAE;gBAAM,CAAC,CAAC,CAAC;gBACzD;gBACAoH,UAAU,CAAC,MAAMb,SAAS,CAAC,IAAI,CAAC,EAAE,GAAG,CAAC;cACxC;cACA;YACF,CAAE;YACFuM,OAAO,EAAG7S,OAAO,IAAK;cACpBuL,gBAAgB,CAACvL,OAAO,EAAE,OAAO,CAAC;YACpC,CAAE;YACFuP,OAAO,EAAEpB;UAAwB;YAAAnB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAClC;QACF;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACY;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACV,CAAC,eAGTjP,OAAA,CAACpE,GAAG;QAACuU,EAAE,EAAE;UAAEgB,OAAO,EAAE;QAAO,CAAE;QAAAZ,QAAA,eAC3BvQ,OAAA,CAACF,0BAA0B;UACzBgW,GAAG,EAAE7S,iBAAkB;UACvBlC,UAAU,EAAEA,UAAW;UACvB2T,SAAS,EAAG5S,OAAO,IAAK;YACtBuL,gBAAgB,CAACvL,OAAO,EAAE,SAAS,CAAC;YACpC;YACAmH,UAAU,CAAC,MAAMb,SAAS,CAAC,IAAI,CAAC,EAAE,GAAG,CAAC;UACxC,CAAE;UACFuM,OAAO,EAAG7S,OAAO,IAAK;YACpBuL,gBAAgB,CAACvL,OAAO,EAAE,OAAO,CAAC;UACpC;QAAE;UAAAgN,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACC,CAAC,eAGNjP,OAAA,CAACpD,QAAQ;QACPiF,IAAI,EAAEF,YAAY,CAACE,IAAK;QACxBkU,gBAAgB,EAAE,IAAK;QACvB1E,OAAO,EAAElE,uBAAwB;QACjC6I,YAAY,EAAE;UAAEC,QAAQ,EAAE,QAAQ;UAAEC,UAAU,EAAE;QAAS,CAAE;QAAA3F,QAAA,eAE3DvQ,OAAA,CAAC7D,KAAK;UAACkV,OAAO,EAAElE,uBAAwB;UAACpL,QAAQ,EAAEJ,YAAY,CAACI,QAAS;UAACoO,EAAE,EAAE;YAAEa,KAAK,EAAE;UAAO,CAAE;UAAAT,QAAA,EAC7F5O,YAAY,CAACG;QAAO;UAAAgN,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAChB;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACA,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACR;EACN;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACE,CAAC;AAEV,CAAC;AAAC/O,EAAA,CAnzCID,kBAAkB;EAAA,QACYb,OAAO,EACyHC,gBAAgB,EACjKF,WAAW;AAAA;AAAAgX,EAAA,GAHxBlW,kBAAkB;AAqzCxB,eAAeA,kBAAkB;AAAC,IAAAkW,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}