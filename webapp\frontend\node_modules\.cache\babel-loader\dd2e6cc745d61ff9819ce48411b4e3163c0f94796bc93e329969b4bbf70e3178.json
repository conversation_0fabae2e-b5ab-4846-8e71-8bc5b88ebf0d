{"ast": null, "code": "import React,{createContext,useState,useContext,useEffect}from'react';import{useNavigate}from'react-router-dom';import authService from'../services/authService';// Crea il contesto di autenticazione\nimport{jsx as _jsx}from\"react/jsx-runtime\";const AuthContext=/*#__PURE__*/createContext(null);// Hook personalizzato per utilizzare il contesto di autenticazione\nexport const useAuth=()=>useContext(AuthContext);export const AuthProvider=_ref=>{let{children}=_ref;const[user,setUser]=useState(null);const[isAuthenticated,setIsAuthenticated]=useState(false);const[loading,setLoading]=useState(true);const navigate=useNavigate();// Verifica se l'utente è già autenticato all'avvio dell'applicazione\nuseEffect(()=>{const checkAuth=async()=>{try{const token=localStorage.getItem('token');if(token){// Verifica la validità del token\nconst userData=await authService.checkToken();setUser(userData);setIsAuthenticated(true);}}catch(error){console.error('Errore durante la verifica del token:',error);// Se il token non è valido, rimuovilo\nlocalStorage.removeItem('token');setUser(null);setIsAuthenticated(false);}finally{setLoading(false);}};checkAuth();},[]);// Funzione di login\nconst login=async(credentials,loginType)=>{try{let response;if(loginType==='standard'){response=await authService.login(credentials);}else if(loginType==='cantiere'){response=await authService.loginCantiere(credentials);}else{throw new Error('Tipo di login non valido');}const{access_token,user_id,username,role}=response;// Salva il token nel localStorage\nlocalStorage.setItem('token',access_token);// Imposta i dati dell'utente\nconst userData={id:user_id,username,role};setUser(userData);setIsAuthenticated(true);return userData;}catch(error){console.error('Errore durante il login:',error);throw error;}};// Funzione di logout\nconst logout=()=>{localStorage.removeItem('token');setUser(null);setIsAuthenticated(false);navigate('/login');};// Valore del contesto\nconst value={user,isAuthenticated,loading,login,logout};return/*#__PURE__*/_jsx(AuthContext.Provider,{value:value,children:children});};", "map": {"version": 3, "names": ["React", "createContext", "useState", "useContext", "useEffect", "useNavigate", "authService", "jsx", "_jsx", "AuthContext", "useAuth", "<PERSON>th<PERSON><PERSON><PERSON>", "_ref", "children", "user", "setUser", "isAuthenticated", "setIsAuthenticated", "loading", "setLoading", "navigate", "checkAuth", "token", "localStorage", "getItem", "userData", "checkToken", "error", "console", "removeItem", "login", "credentials", "loginType", "response", "loginCantiere", "Error", "access_token", "user_id", "username", "role", "setItem", "id", "logout", "value", "Provider"], "sources": ["C:/Users/<USER>/PycharmProjects/CMS/webapp/frontend/src/context/AuthContext.js"], "sourcesContent": ["import React, { createContext, useState, useContext, useEffect } from 'react';\nimport { useNavigate } from 'react-router-dom';\nimport authService from '../services/authService';\n\n// Crea il contesto di autenticazione\nconst AuthContext = createContext(null);\n\n// Hook personalizzato per utilizzare il contesto di autenticazione\nexport const useAuth = () => useContext(AuthContext);\n\nexport const AuthProvider = ({ children }) => {\n  const [user, setUser] = useState(null);\n  const [isAuthenticated, setIsAuthenticated] = useState(false);\n  const [loading, setLoading] = useState(true);\n  const navigate = useNavigate();\n\n  // Verifica se l'utente è già autenticato all'avvio dell'applicazione\n  useEffect(() => {\n    const checkAuth = async () => {\n      try {\n        const token = localStorage.getItem('token');\n        if (token) {\n          // Verifica la validità del token\n          const userData = await authService.checkToken();\n          setUser(userData);\n          setIsAuthenticated(true);\n        }\n      } catch (error) {\n        console.error('Errore durante la verifica del token:', error);\n        // Se il token non è valido, rimuovilo\n        localStorage.removeItem('token');\n        setUser(null);\n        setIsAuthenticated(false);\n      } finally {\n        setLoading(false);\n      }\n    };\n\n    checkAuth();\n  }, []);\n\n  // Funzione di login\n  const login = async (credentials, loginType) => {\n    try {\n      let response;\n      \n      if (loginType === 'standard') {\n        response = await authService.login(credentials);\n      } else if (loginType === 'cantiere') {\n        response = await authService.loginCantiere(credentials);\n      } else {\n        throw new Error('Tipo di login non valido');\n      }\n      \n      const { access_token, user_id, username, role } = response;\n      \n      // Salva il token nel localStorage\n      localStorage.setItem('token', access_token);\n      \n      // Imposta i dati dell'utente\n      const userData = { id: user_id, username, role };\n      setUser(userData);\n      setIsAuthenticated(true);\n      \n      return userData;\n    } catch (error) {\n      console.error('Errore durante il login:', error);\n      throw error;\n    }\n  };\n\n  // Funzione di logout\n  const logout = () => {\n    localStorage.removeItem('token');\n    setUser(null);\n    setIsAuthenticated(false);\n    navigate('/login');\n  };\n\n  // Valore del contesto\n  const value = {\n    user,\n    isAuthenticated,\n    loading,\n    login,\n    logout\n  };\n\n  return <AuthContext.Provider value={value}>{children}</AuthContext.Provider>;\n};\n"], "mappings": "AAAA,MAAO,CAAAA,KAAK,EAAIC,aAAa,CAAEC,QAAQ,CAAEC,UAAU,CAAEC,SAAS,KAAQ,OAAO,CAC7E,OAASC,WAAW,KAAQ,kBAAkB,CAC9C,MAAO,CAAAC,WAAW,KAAM,yBAAyB,CAEjD;AAAA,OAAAC,GAAA,IAAAC,IAAA,yBACA,KAAM,CAAAC,WAAW,cAAGR,aAAa,CAAC,IAAI,CAAC,CAEvC;AACA,MAAO,MAAM,CAAAS,OAAO,CAAGA,CAAA,GAAMP,UAAU,CAACM,WAAW,CAAC,CAEpD,MAAO,MAAM,CAAAE,YAAY,CAAGC,IAAA,EAAkB,IAAjB,CAAEC,QAAS,CAAC,CAAAD,IAAA,CACvC,KAAM,CAACE,IAAI,CAAEC,OAAO,CAAC,CAAGb,QAAQ,CAAC,IAAI,CAAC,CACtC,KAAM,CAACc,eAAe,CAAEC,kBAAkB,CAAC,CAAGf,QAAQ,CAAC,KAAK,CAAC,CAC7D,KAAM,CAACgB,OAAO,CAAEC,UAAU,CAAC,CAAGjB,QAAQ,CAAC,IAAI,CAAC,CAC5C,KAAM,CAAAkB,QAAQ,CAAGf,WAAW,CAAC,CAAC,CAE9B;AACAD,SAAS,CAAC,IAAM,CACd,KAAM,CAAAiB,SAAS,CAAG,KAAAA,CAAA,GAAY,CAC5B,GAAI,CACF,KAAM,CAAAC,KAAK,CAAGC,YAAY,CAACC,OAAO,CAAC,OAAO,CAAC,CAC3C,GAAIF,KAAK,CAAE,CACT;AACA,KAAM,CAAAG,QAAQ,CAAG,KAAM,CAAAnB,WAAW,CAACoB,UAAU,CAAC,CAAC,CAC/CX,OAAO,CAACU,QAAQ,CAAC,CACjBR,kBAAkB,CAAC,IAAI,CAAC,CAC1B,CACF,CAAE,MAAOU,KAAK,CAAE,CACdC,OAAO,CAACD,KAAK,CAAC,uCAAuC,CAAEA,KAAK,CAAC,CAC7D;AACAJ,YAAY,CAACM,UAAU,CAAC,OAAO,CAAC,CAChCd,OAAO,CAAC,IAAI,CAAC,CACbE,kBAAkB,CAAC,KAAK,CAAC,CAC3B,CAAC,OAAS,CACRE,UAAU,CAAC,KAAK,CAAC,CACnB,CACF,CAAC,CAEDE,SAAS,CAAC,CAAC,CACb,CAAC,CAAE,EAAE,CAAC,CAEN;AACA,KAAM,CAAAS,KAAK,CAAG,KAAAA,CAAOC,WAAW,CAAEC,SAAS,GAAK,CAC9C,GAAI,CACF,GAAI,CAAAC,QAAQ,CAEZ,GAAID,SAAS,GAAK,UAAU,CAAE,CAC5BC,QAAQ,CAAG,KAAM,CAAA3B,WAAW,CAACwB,KAAK,CAACC,WAAW,CAAC,CACjD,CAAC,IAAM,IAAIC,SAAS,GAAK,UAAU,CAAE,CACnCC,QAAQ,CAAG,KAAM,CAAA3B,WAAW,CAAC4B,aAAa,CAACH,WAAW,CAAC,CACzD,CAAC,IAAM,CACL,KAAM,IAAI,CAAAI,KAAK,CAAC,0BAA0B,CAAC,CAC7C,CAEA,KAAM,CAAEC,YAAY,CAAEC,OAAO,CAAEC,QAAQ,CAAEC,IAAK,CAAC,CAAGN,QAAQ,CAE1D;AACAV,YAAY,CAACiB,OAAO,CAAC,OAAO,CAAEJ,YAAY,CAAC,CAE3C;AACA,KAAM,CAAAX,QAAQ,CAAG,CAAEgB,EAAE,CAAEJ,OAAO,CAAEC,QAAQ,CAAEC,IAAK,CAAC,CAChDxB,OAAO,CAACU,QAAQ,CAAC,CACjBR,kBAAkB,CAAC,IAAI,CAAC,CAExB,MAAO,CAAAQ,QAAQ,CACjB,CAAE,MAAOE,KAAK,CAAE,CACdC,OAAO,CAACD,KAAK,CAAC,0BAA0B,CAAEA,KAAK,CAAC,CAChD,KAAM,CAAAA,KAAK,CACb,CACF,CAAC,CAED;AACA,KAAM,CAAAe,MAAM,CAAGA,CAAA,GAAM,CACnBnB,YAAY,CAACM,UAAU,CAAC,OAAO,CAAC,CAChCd,OAAO,CAAC,IAAI,CAAC,CACbE,kBAAkB,CAAC,KAAK,CAAC,CACzBG,QAAQ,CAAC,QAAQ,CAAC,CACpB,CAAC,CAED;AACA,KAAM,CAAAuB,KAAK,CAAG,CACZ7B,IAAI,CACJE,eAAe,CACfE,OAAO,CACPY,KAAK,CACLY,MACF,CAAC,CAED,mBAAOlC,IAAA,CAACC,WAAW,CAACmC,QAAQ,EAACD,KAAK,CAAEA,KAAM,CAAA9B,QAAA,CAAEA,QAAQ,CAAuB,CAAC,CAC9E,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}