{"ast": null, "code": "const formatDistanceLocale = {\n  lessThanXSeconds: {\n    one: \"أقل من ثانية\",\n    two: \"أقل من ثانيتين\",\n    threeToTen: \"أقل من {{count}} ثواني\",\n    other: \"أقل من {{count}} ثانية\"\n  },\n  xSeconds: {\n    one: \"ثانية\",\n    two: \"ثانيتين\",\n    threeToTen: \"{{count}} ثواني\",\n    other: \"{{count}} ثانية\"\n  },\n  halfAMinute: \"نص دقيقة\",\n  lessThanXMinutes: {\n    one: \"أقل من دقيقة\",\n    two: \"أقل من دقيقتين\",\n    threeToTen: \"أقل من {{count}} دقايق\",\n    other: \"أقل من {{count}} دقيقة\"\n  },\n  xMinutes: {\n    one: \"دقيقة\",\n    two: \"دقيقتين\",\n    threeToTen: \"{{count}} دقايق\",\n    other: \"{{count}} دقيقة\"\n  },\n  aboutXHours: {\n    one: \"حوالي ساعة\",\n    two: \"حوالي ساعتين\",\n    threeToTen: \"حوالي {{count}} ساعات\",\n    other: \"حوالي {{count}} ساعة\"\n  },\n  xHours: {\n    one: \"ساعة\",\n    two: \"ساعتين\",\n    threeToTen: \"{{count}} ساعات\",\n    other: \"{{count}} ساعة\"\n  },\n  xDays: {\n    one: \"يوم\",\n    two: \"يومين\",\n    threeToTen: \"{{count}} أيام\",\n    other: \"{{count}} يوم\"\n  },\n  aboutXWeeks: {\n    one: \"حوالي أسبوع\",\n    two: \"حوالي أسبوعين\",\n    threeToTen: \"حوالي {{count}} أسابيع\",\n    other: \"حوالي {{count}} أسبوع\"\n  },\n  xWeeks: {\n    one: \"أسبوع\",\n    two: \"أسبوعين\",\n    threeToTen: \"{{count}} أسابيع\",\n    other: \"{{count}} أسبوع\"\n  },\n  aboutXMonths: {\n    one: \"حوالي شهر\",\n    two: \"حوالي شهرين\",\n    threeToTen: \"حوالي {{count}} أشهر\",\n    other: \"حوالي {{count}} شهر\"\n  },\n  xMonths: {\n    one: \"شهر\",\n    two: \"شهرين\",\n    threeToTen: \"{{count}} أشهر\",\n    other: \"{{count}} شهر\"\n  },\n  aboutXYears: {\n    one: \"حوالي سنة\",\n    two: \"حوالي سنتين\",\n    threeToTen: \"حوالي {{count}} سنين\",\n    other: \"حوالي {{count}} سنة\"\n  },\n  xYears: {\n    one: \"عام\",\n    two: \"عامين\",\n    threeToTen: \"{{count}} أعوام\",\n    other: \"{{count}} عام\"\n  },\n  overXYears: {\n    one: \"أكثر من سنة\",\n    two: \"أكثر من سنتين\",\n    threeToTen: \"أكثر من {{count}} سنين\",\n    other: \"أكثر من {{count}} سنة\"\n  },\n  almostXYears: {\n    one: \"عام تقريبًا\",\n    two: \"عامين تقريبًا\",\n    threeToTen: \"{{count}} أعوام تقريبًا\",\n    other: \"{{count}} عام تقريبًا\"\n  }\n};\nexport const formatDistance = (token, count, options) => {\n  let result;\n  const tokenValue = formatDistanceLocale[token];\n  if (typeof tokenValue === \"string\") {\n    result = tokenValue;\n  } else if (count === 1) {\n    result = tokenValue.one;\n  } else if (count === 2) {\n    result = tokenValue.two;\n  } else if (count <= 10) {\n    result = tokenValue.threeToTen.replace(\"{{count}}\", String(count));\n  } else {\n    result = tokenValue.other.replace(\"{{count}}\", String(count));\n  }\n  if (options?.addSuffix) {\n    if (options.comparison && options.comparison > 0) {\n      return `في خلال ${result}`;\n    } else {\n      return `منذ ${result}`;\n    }\n  }\n  return result;\n};", "map": {"version": 3, "names": ["formatDistanceLocale", "lessThanXSeconds", "one", "two", "threeToTen", "other", "xSeconds", "halfAMinute", "lessThanXMinutes", "xMinutes", "aboutXHours", "xHours", "xDays", "aboutXWeeks", "xWeeks", "aboutXMonths", "xMonths", "aboutXYears", "xYears", "overXYears", "almostXYears", "formatDistance", "token", "count", "options", "result", "tokenValue", "replace", "String", "addSuffix", "comparison"], "sources": ["C:/CMS/webapp/frontend/node_modules/date-fns/locale/ar-EG/_lib/formatDistance.js"], "sourcesContent": ["const formatDistanceLocale = {\n  lessThanXSeconds: {\n    one: \"أقل من ثانية\",\n    two: \"أقل من ثانيتين\",\n    threeToTen: \"أقل من {{count}} ثواني\",\n    other: \"أقل من {{count}} ثانية\",\n  },\n\n  xSeconds: {\n    one: \"ثانية\",\n    two: \"ثانيتين\",\n    threeToTen: \"{{count}} ثواني\",\n    other: \"{{count}} ثانية\",\n  },\n\n  halfAMinute: \"نص دقيقة\",\n\n  lessThanXMinutes: {\n    one: \"أقل من دقيقة\",\n    two: \"أقل من دقيقتين\",\n    threeToTen: \"أقل من {{count}} دقايق\",\n    other: \"أقل من {{count}} دقيقة\",\n  },\n\n  xMinutes: {\n    one: \"دقيقة\",\n    two: \"دقيقتين\",\n    threeToTen: \"{{count}} دقايق\",\n    other: \"{{count}} دقيقة\",\n  },\n\n  aboutXHours: {\n    one: \"حوالي ساعة\",\n    two: \"حوالي ساعتين\",\n    threeToTen: \"حوالي {{count}} ساعات\",\n    other: \"حوالي {{count}} ساعة\",\n  },\n\n  xHours: {\n    one: \"ساعة\",\n    two: \"ساعتين\",\n    threeToTen: \"{{count}} ساعات\",\n    other: \"{{count}} ساعة\",\n  },\n\n  xDays: {\n    one: \"يوم\",\n    two: \"يومين\",\n    threeToTen: \"{{count}} أيام\",\n    other: \"{{count}} يوم\",\n  },\n\n  aboutXWeeks: {\n    one: \"حوالي أسبوع\",\n    two: \"حوالي أسبوعين\",\n    threeToTen: \"حوالي {{count}} أسابيع\",\n    other: \"حوالي {{count}} أسبوع\",\n  },\n\n  xWeeks: {\n    one: \"أسبوع\",\n    two: \"أسبوعين\",\n    threeToTen: \"{{count}} أسابيع\",\n    other: \"{{count}} أسبوع\",\n  },\n\n  aboutXMonths: {\n    one: \"حوالي شهر\",\n    two: \"حوالي شهرين\",\n    threeToTen: \"حوالي {{count}} أشهر\",\n    other: \"حوالي {{count}} شهر\",\n  },\n\n  xMonths: {\n    one: \"شهر\",\n    two: \"شهرين\",\n    threeToTen: \"{{count}} أشهر\",\n    other: \"{{count}} شهر\",\n  },\n\n  aboutXYears: {\n    one: \"حوالي سنة\",\n    two: \"حوالي سنتين\",\n    threeToTen: \"حوالي {{count}} سنين\",\n    other: \"حوالي {{count}} سنة\",\n  },\n\n  xYears: {\n    one: \"عام\",\n    two: \"عامين\",\n    threeToTen: \"{{count}} أعوام\",\n    other: \"{{count}} عام\",\n  },\n\n  overXYears: {\n    one: \"أكثر من سنة\",\n    two: \"أكثر من سنتين\",\n    threeToTen: \"أكثر من {{count}} سنين\",\n    other: \"أكثر من {{count}} سنة\",\n  },\n\n  almostXYears: {\n    one: \"عام تقريبًا\",\n    two: \"عامين تقريبًا\",\n    threeToTen: \"{{count}} أعوام تقريبًا\",\n    other: \"{{count}} عام تقريبًا\",\n  },\n};\n\nexport const formatDistance = (token, count, options) => {\n  let result;\n\n  const tokenValue = formatDistanceLocale[token];\n  if (typeof tokenValue === \"string\") {\n    result = tokenValue;\n  } else if (count === 1) {\n    result = tokenValue.one;\n  } else if (count === 2) {\n    result = tokenValue.two;\n  } else if (count <= 10) {\n    result = tokenValue.threeToTen.replace(\"{{count}}\", String(count));\n  } else {\n    result = tokenValue.other.replace(\"{{count}}\", String(count));\n  }\n\n  if (options?.addSuffix) {\n    if (options.comparison && options.comparison > 0) {\n      return `في خلال ${result}`;\n    } else {\n      return `منذ ${result}`;\n    }\n  }\n\n  return result;\n};\n"], "mappings": "AAAA,MAAMA,oBAAoB,GAAG;EAC3BC,gBAAgB,EAAE;IAChBC,GAAG,EAAE,cAAc;IACnBC,GAAG,EAAE,gBAAgB;IACrBC,UAAU,EAAE,wBAAwB;IACpCC,KAAK,EAAE;EACT,CAAC;EAEDC,QAAQ,EAAE;IACRJ,GAAG,EAAE,OAAO;IACZC,GAAG,EAAE,SAAS;IACdC,UAAU,EAAE,iBAAiB;IAC7BC,KAAK,EAAE;EACT,CAAC;EAEDE,WAAW,EAAE,UAAU;EAEvBC,gBAAgB,EAAE;IAChBN,GAAG,EAAE,cAAc;IACnBC,GAAG,EAAE,gBAAgB;IACrBC,UAAU,EAAE,wBAAwB;IACpCC,KAAK,EAAE;EACT,CAAC;EAEDI,QAAQ,EAAE;IACRP,GAAG,EAAE,OAAO;IACZC,GAAG,EAAE,SAAS;IACdC,UAAU,EAAE,iBAAiB;IAC7BC,KAAK,EAAE;EACT,CAAC;EAEDK,WAAW,EAAE;IACXR,GAAG,EAAE,YAAY;IACjBC,GAAG,EAAE,cAAc;IACnBC,UAAU,EAAE,uBAAuB;IACnCC,KAAK,EAAE;EACT,CAAC;EAEDM,MAAM,EAAE;IACNT,GAAG,EAAE,MAAM;IACXC,GAAG,EAAE,QAAQ;IACbC,UAAU,EAAE,iBAAiB;IAC7BC,KAAK,EAAE;EACT,CAAC;EAEDO,KAAK,EAAE;IACLV,GAAG,EAAE,KAAK;IACVC,GAAG,EAAE,OAAO;IACZC,UAAU,EAAE,gBAAgB;IAC5BC,KAAK,EAAE;EACT,CAAC;EAEDQ,WAAW,EAAE;IACXX,GAAG,EAAE,aAAa;IAClBC,GAAG,EAAE,eAAe;IACpBC,UAAU,EAAE,wBAAwB;IACpCC,KAAK,EAAE;EACT,CAAC;EAEDS,MAAM,EAAE;IACNZ,GAAG,EAAE,OAAO;IACZC,GAAG,EAAE,SAAS;IACdC,UAAU,EAAE,kBAAkB;IAC9BC,KAAK,EAAE;EACT,CAAC;EAEDU,YAAY,EAAE;IACZb,GAAG,EAAE,WAAW;IAChBC,GAAG,EAAE,aAAa;IAClBC,UAAU,EAAE,sBAAsB;IAClCC,KAAK,EAAE;EACT,CAAC;EAEDW,OAAO,EAAE;IACPd,GAAG,EAAE,KAAK;IACVC,GAAG,EAAE,OAAO;IACZC,UAAU,EAAE,gBAAgB;IAC5BC,KAAK,EAAE;EACT,CAAC;EAEDY,WAAW,EAAE;IACXf,GAAG,EAAE,WAAW;IAChBC,GAAG,EAAE,aAAa;IAClBC,UAAU,EAAE,sBAAsB;IAClCC,KAAK,EAAE;EACT,CAAC;EAEDa,MAAM,EAAE;IACNhB,GAAG,EAAE,KAAK;IACVC,GAAG,EAAE,OAAO;IACZC,UAAU,EAAE,iBAAiB;IAC7BC,KAAK,EAAE;EACT,CAAC;EAEDc,UAAU,EAAE;IACVjB,GAAG,EAAE,aAAa;IAClBC,GAAG,EAAE,eAAe;IACpBC,UAAU,EAAE,wBAAwB;IACpCC,KAAK,EAAE;EACT,CAAC;EAEDe,YAAY,EAAE;IACZlB,GAAG,EAAE,aAAa;IAClBC,GAAG,EAAE,eAAe;IACpBC,UAAU,EAAE,yBAAyB;IACrCC,KAAK,EAAE;EACT;AACF,CAAC;AAED,OAAO,MAAMgB,cAAc,GAAGA,CAACC,KAAK,EAAEC,KAAK,EAAEC,OAAO,KAAK;EACvD,IAAIC,MAAM;EAEV,MAAMC,UAAU,GAAG1B,oBAAoB,CAACsB,KAAK,CAAC;EAC9C,IAAI,OAAOI,UAAU,KAAK,QAAQ,EAAE;IAClCD,MAAM,GAAGC,UAAU;EACrB,CAAC,MAAM,IAAIH,KAAK,KAAK,CAAC,EAAE;IACtBE,MAAM,GAAGC,UAAU,CAACxB,GAAG;EACzB,CAAC,MAAM,IAAIqB,KAAK,KAAK,CAAC,EAAE;IACtBE,MAAM,GAAGC,UAAU,CAACvB,GAAG;EACzB,CAAC,MAAM,IAAIoB,KAAK,IAAI,EAAE,EAAE;IACtBE,MAAM,GAAGC,UAAU,CAACtB,UAAU,CAACuB,OAAO,CAAC,WAAW,EAAEC,MAAM,CAACL,KAAK,CAAC,CAAC;EACpE,CAAC,MAAM;IACLE,MAAM,GAAGC,UAAU,CAACrB,KAAK,CAACsB,OAAO,CAAC,WAAW,EAAEC,MAAM,CAACL,KAAK,CAAC,CAAC;EAC/D;EAEA,IAAIC,OAAO,EAAEK,SAAS,EAAE;IACtB,IAAIL,OAAO,CAACM,UAAU,IAAIN,OAAO,CAACM,UAAU,GAAG,CAAC,EAAE;MAChD,OAAO,WAAWL,MAAM,EAAE;IAC5B,CAAC,MAAM;MACL,OAAO,OAAOA,MAAM,EAAE;IACxB;EACF;EAEA,OAAOA,MAAM;AACf,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}