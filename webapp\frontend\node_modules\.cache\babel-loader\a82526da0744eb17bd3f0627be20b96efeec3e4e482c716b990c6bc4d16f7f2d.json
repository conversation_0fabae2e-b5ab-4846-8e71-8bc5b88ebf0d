{"ast": null, "code": "var _jsxFileName = \"C:\\\\CMS\\\\webapp\\\\frontend\\\\src\\\\components\\\\Footer.js\";\nimport React from 'react';\nimport { Box, Typography, Link, Container, Grid } from '@mui/material';\nimport Logo from './Logo';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst Footer = () => {\n  const currentYear = new Date().getFullYear();\n  return /*#__PURE__*/_jsxDEV(Box, {\n    className: \"cablys-footer\",\n    component: \"footer\",\n    children: /*#__PURE__*/_jsxDEV(Container, {\n      maxWidth: \"lg\",\n      children: [/*#__PURE__*/_jsxDEV(Grid, {\n        container: true,\n        spacing: 4,\n        justifyContent: \"space-between\",\n        children: [/*#__PURE__*/_jsxDEV(Grid, {\n          item: true,\n          xs: 12,\n          sm: 4,\n          children: [/*#__PURE__*/_jsxDEV(Box, {\n            sx: {\n              display: 'flex',\n              alignItems: 'center',\n              mb: 2\n            },\n            children: [/*#__PURE__*/_jsxDEV(Logo, {\n              width: 24,\n              height: 24\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 14,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Typography, {\n              variant: \"h6\",\n              sx: {\n                ml: 1,\n                fontWeight: 700\n              },\n              children: \"CABLYS\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 15,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 13,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Typography, {\n            variant: \"body2\",\n            color: \"text.secondary\",\n            children: \"Sistema avanzato per l'installazione e la gestione dei cavi nei cantieri.\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 19,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 12,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Grid, {\n          item: true,\n          xs: 12,\n          sm: 4,\n          children: [/*#__PURE__*/_jsxDEV(Typography, {\n            variant: \"subtitle1\",\n            sx: {\n              fontWeight: 600,\n              mb: 2\n            },\n            children: \"Collegamenti Rapidi\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 25,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Box, {\n            sx: {\n              display: 'flex',\n              flexDirection: 'column'\n            },\n            children: [/*#__PURE__*/_jsxDEV(Link, {\n              href: \"/dashboard\",\n              color: \"inherit\",\n              underline: \"hover\",\n              sx: {\n                mb: 1\n              },\n              children: \"Dashboard\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 29,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Link, {\n              href: \"/dashboard/cavi/visualizza\",\n              color: \"inherit\",\n              underline: \"hover\",\n              sx: {\n                mb: 1\n              },\n              children: \"Visualizza Cavi\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 32,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Link, {\n              href: \"/dashboard/cavi/report\",\n              color: \"inherit\",\n              underline: \"hover\",\n              sx: {\n                mb: 1\n              },\n              children: \"Report\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 35,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 28,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 24,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Grid, {\n          item: true,\n          xs: 12,\n          sm: 4,\n          children: [/*#__PURE__*/_jsxDEV(Typography, {\n            variant: \"subtitle1\",\n            sx: {\n              fontWeight: 600,\n              mb: 2\n            },\n            children: \"Contatti\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 42,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Typography, {\n            variant: \"body2\",\n            color: \"text.secondary\",\n            sx: {\n              mb: 1\n            },\n            children: \"Email: <EMAIL>\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 45,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Typography, {\n            variant: \"body2\",\n            color: \"text.secondary\",\n            sx: {\n              mb: 1\n            },\n            children: \"Telefono: +39 ************\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 48,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 41,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 11,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Box, {\n        sx: {\n          mt: 4,\n          pt: 2,\n          borderTop: '1px solid #e0e0e0',\n          textAlign: 'center'\n        },\n        children: /*#__PURE__*/_jsxDEV(Typography, {\n          variant: \"body2\",\n          color: \"text.secondary\",\n          children: [\"\\xA9 \", currentYear, \" CABLYS - Cable Installation Advance System. Tutti i diritti riservati.\"]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 55,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 54,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 10,\n      columnNumber: 7\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 9,\n    columnNumber: 5\n  }, this);\n};\n_c = Footer;\nexport default Footer;\nvar _c;\n$RefreshReg$(_c, \"Footer\");", "map": {"version": 3, "names": ["React", "Box", "Typography", "Link", "Container", "Grid", "Logo", "jsxDEV", "_jsxDEV", "Footer", "currentYear", "Date", "getFullYear", "className", "component", "children", "max<PERSON><PERSON><PERSON>", "container", "spacing", "justifyContent", "item", "xs", "sm", "sx", "display", "alignItems", "mb", "width", "height", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "variant", "ml", "fontWeight", "color", "flexDirection", "href", "underline", "mt", "pt", "borderTop", "textAlign", "_c", "$RefreshReg$"], "sources": ["C:/CMS/webapp/frontend/src/components/Footer.js"], "sourcesContent": ["import React from 'react';\r\nimport { Box, Typography, Link, Container, Grid } from '@mui/material';\r\nimport Logo from './Logo';\r\n\r\nconst Footer = () => {\r\n  const currentYear = new Date().getFullYear();\r\n  \r\n  return (\r\n    <Box className=\"cablys-footer\" component=\"footer\">\r\n      <Container maxWidth=\"lg\">\r\n        <Grid container spacing={4} justifyContent=\"space-between\">\r\n          <Grid item xs={12} sm={4}>\r\n            <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>\r\n              <Logo width={24} height={24} />\r\n              <Typography variant=\"h6\" sx={{ ml: 1, fontWeight: 700 }}>\r\n                CABLYS\r\n              </Typography>\r\n            </Box>\r\n            <Typography variant=\"body2\" color=\"text.secondary\">\r\n              Sistema avanzato per l'installazione e la gestione dei cavi nei cantieri.\r\n            </Typography>\r\n          </Grid>\r\n          \r\n          <Grid item xs={12} sm={4}>\r\n            <Typography variant=\"subtitle1\" sx={{ fontWeight: 600, mb: 2 }}>\r\n              Collegamenti Rapidi\r\n            </Typography>\r\n            <Box sx={{ display: 'flex', flexDirection: 'column' }}>\r\n              <Link href=\"/dashboard\" color=\"inherit\" underline=\"hover\" sx={{ mb: 1 }}>\r\n                Dashboard\r\n              </Link>\r\n              <Link href=\"/dashboard/cavi/visualizza\" color=\"inherit\" underline=\"hover\" sx={{ mb: 1 }}>\r\n                Visualizza Cavi\r\n              </Link>\r\n              <Link href=\"/dashboard/cavi/report\" color=\"inherit\" underline=\"hover\" sx={{ mb: 1 }}>\r\n                Report\r\n              </Link>\r\n            </Box>\r\n          </Grid>\r\n          \r\n          <Grid item xs={12} sm={4}>\r\n            <Typography variant=\"subtitle1\" sx={{ fontWeight: 600, mb: 2 }}>\r\n              Contatti\r\n            </Typography>\r\n            <Typography variant=\"body2\" color=\"text.secondary\" sx={{ mb: 1 }}>\r\n              Email: <EMAIL>\r\n            </Typography>\r\n            <Typography variant=\"body2\" color=\"text.secondary\" sx={{ mb: 1 }}>\r\n              Telefono: +39 ************\r\n            </Typography>\r\n          </Grid>\r\n        </Grid>\r\n        \r\n        <Box sx={{ mt: 4, pt: 2, borderTop: '1px solid #e0e0e0', textAlign: 'center' }}>\r\n          <Typography variant=\"body2\" color=\"text.secondary\">\r\n            © {currentYear} CABLYS - Cable Installation Advance System. Tutti i diritti riservati.\r\n          </Typography>\r\n        </Box>\r\n      </Container>\r\n    </Box>\r\n  );\r\n};\r\n\r\nexport default Footer;"], "mappings": ";AAAA,OAAOA,KAAK,MAAM,OAAO;AACzB,SAASC,GAAG,EAAEC,UAAU,EAAEC,IAAI,EAAEC,SAAS,EAAEC,IAAI,QAAQ,eAAe;AACtE,OAAOC,IAAI,MAAM,QAAQ;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAE1B,MAAMC,MAAM,GAAGA,CAAA,KAAM;EACnB,MAAMC,WAAW,GAAG,IAAIC,IAAI,CAAC,CAAC,CAACC,WAAW,CAAC,CAAC;EAE5C,oBACEJ,OAAA,CAACP,GAAG;IAACY,SAAS,EAAC,eAAe;IAACC,SAAS,EAAC,QAAQ;IAAAC,QAAA,eAC/CP,OAAA,CAACJ,SAAS;MAACY,QAAQ,EAAC,IAAI;MAAAD,QAAA,gBACtBP,OAAA,CAACH,IAAI;QAACY,SAAS;QAACC,OAAO,EAAE,CAAE;QAACC,cAAc,EAAC,eAAe;QAAAJ,QAAA,gBACxDP,OAAA,CAACH,IAAI;UAACe,IAAI;UAACC,EAAE,EAAE,EAAG;UAACC,EAAE,EAAE,CAAE;UAAAP,QAAA,gBACvBP,OAAA,CAACP,GAAG;YAACsB,EAAE,EAAE;cAAEC,OAAO,EAAE,MAAM;cAAEC,UAAU,EAAE,QAAQ;cAAEC,EAAE,EAAE;YAAE,CAAE;YAAAX,QAAA,gBACxDP,OAAA,CAACF,IAAI;cAACqB,KAAK,EAAE,EAAG;cAACC,MAAM,EAAE;YAAG;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,eAC/BxB,OAAA,CAACN,UAAU;cAAC+B,OAAO,EAAC,IAAI;cAACV,EAAE,EAAE;gBAAEW,EAAE,EAAE,CAAC;gBAAEC,UAAU,EAAE;cAAI,CAAE;cAAApB,QAAA,EAAC;YAEzD;cAAAc,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAY,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACV,CAAC,eACNxB,OAAA,CAACN,UAAU;YAAC+B,OAAO,EAAC,OAAO;YAACG,KAAK,EAAC,gBAAgB;YAAArB,QAAA,EAAC;UAEnD;YAAAc,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAY,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACT,CAAC,eAEPxB,OAAA,CAACH,IAAI;UAACe,IAAI;UAACC,EAAE,EAAE,EAAG;UAACC,EAAE,EAAE,CAAE;UAAAP,QAAA,gBACvBP,OAAA,CAACN,UAAU;YAAC+B,OAAO,EAAC,WAAW;YAACV,EAAE,EAAE;cAAEY,UAAU,EAAE,GAAG;cAAET,EAAE,EAAE;YAAE,CAAE;YAAAX,QAAA,EAAC;UAEhE;YAAAc,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAY,CAAC,eACbxB,OAAA,CAACP,GAAG;YAACsB,EAAE,EAAE;cAAEC,OAAO,EAAE,MAAM;cAAEa,aAAa,EAAE;YAAS,CAAE;YAAAtB,QAAA,gBACpDP,OAAA,CAACL,IAAI;cAACmC,IAAI,EAAC,YAAY;cAACF,KAAK,EAAC,SAAS;cAACG,SAAS,EAAC,OAAO;cAAChB,EAAE,EAAE;gBAAEG,EAAE,EAAE;cAAE,CAAE;cAAAX,QAAA,EAAC;YAEzE;cAAAc,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC,eACPxB,OAAA,CAACL,IAAI;cAACmC,IAAI,EAAC,4BAA4B;cAACF,KAAK,EAAC,SAAS;cAACG,SAAS,EAAC,OAAO;cAAChB,EAAE,EAAE;gBAAEG,EAAE,EAAE;cAAE,CAAE;cAAAX,QAAA,EAAC;YAEzF;cAAAc,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC,eACPxB,OAAA,CAACL,IAAI;cAACmC,IAAI,EAAC,wBAAwB;cAACF,KAAK,EAAC,SAAS;cAACG,SAAS,EAAC,OAAO;cAAChB,EAAE,EAAE;gBAAEG,EAAE,EAAE;cAAE,CAAE;cAAAX,QAAA,EAAC;YAErF;cAAAc,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACJ,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACF,CAAC,eAEPxB,OAAA,CAACH,IAAI;UAACe,IAAI;UAACC,EAAE,EAAE,EAAG;UAACC,EAAE,EAAE,CAAE;UAAAP,QAAA,gBACvBP,OAAA,CAACN,UAAU;YAAC+B,OAAO,EAAC,WAAW;YAACV,EAAE,EAAE;cAAEY,UAAU,EAAE,GAAG;cAAET,EAAE,EAAE;YAAE,CAAE;YAAAX,QAAA,EAAC;UAEhE;YAAAc,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAY,CAAC,eACbxB,OAAA,CAACN,UAAU;YAAC+B,OAAO,EAAC,OAAO;YAACG,KAAK,EAAC,gBAAgB;YAACb,EAAE,EAAE;cAAEG,EAAE,EAAE;YAAE,CAAE;YAAAX,QAAA,EAAC;UAElE;YAAAc,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAY,CAAC,eACbxB,OAAA,CAACN,UAAU;YAAC+B,OAAO,EAAC,OAAO;YAACG,KAAK,EAAC,gBAAgB;YAACb,EAAE,EAAE;cAAEG,EAAE,EAAE;YAAE,CAAE;YAAAX,QAAA,EAAC;UAElE;YAAAc,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAY,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACT,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,eAEPxB,OAAA,CAACP,GAAG;QAACsB,EAAE,EAAE;UAAEiB,EAAE,EAAE,CAAC;UAAEC,EAAE,EAAE,CAAC;UAAEC,SAAS,EAAE,mBAAmB;UAAEC,SAAS,EAAE;QAAS,CAAE;QAAA5B,QAAA,eAC7EP,OAAA,CAACN,UAAU;UAAC+B,OAAO,EAAC,OAAO;UAACG,KAAK,EAAC,gBAAgB;UAAArB,QAAA,GAAC,OAC/C,EAACL,WAAW,EAAC,yEACjB;QAAA;UAAAmB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAY;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACV,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACG;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACT,CAAC;AAEV,CAAC;AAACY,EAAA,GAzDInC,MAAM;AA2DZ,eAAeA,MAAM;AAAC,IAAAmC,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}