{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\PycharmProjects\\\\CMS\\\\webapp\\\\frontend\\\\src\\\\pages\\\\cavi\\\\posa\\\\EliminaCavoPage.js\",\n  _s = $RefreshSig$();\nimport React, { useState } from 'react';\nimport { Box, Typography, Paper, Button, IconButton, Alert, Snackbar, Dialog, DialogTitle, DialogContent, DialogContentText, DialogActions, CircularProgress, TextField, Radio, RadioGroup, FormControlLabel, FormControl, FormLabel } from '@mui/material';\nimport { ArrowBack as ArrowBackIcon, Refresh as RefreshIcon, Delete as DeleteIcon, Warning as WarningIcon } from '@mui/icons-material';\nimport { useNavigate } from 'react-router-dom';\nimport { useAuth } from '../../../context/AuthContext';\nimport AdminHomeButton from '../../../components/common/AdminHomeButton';\nimport caviService from '../../../services/caviService';\nimport { jsxDEV as _jsxDEV, Fragment as _Fragment } from \"react/jsx-dev-runtime\";\nconst EliminaCavoPage = () => {\n  _s();\n  const navigate = useNavigate();\n  const {\n    isImpersonating\n  } = useAuth();\n  const [alertMessage, setAlertMessage] = useState(null);\n  const [alertSeverity, setAlertSeverity] = useState('success');\n  const [openSnackbar, setOpenSnackbar] = useState(false);\n\n  // Stati per la gestione dei cavi\n  const [cavi, setCavi] = useState([]);\n  const [loading, setLoading] = useState(false);\n  const [selectedCavo, setSelectedCavo] = useState(null);\n  const [openDialog, setOpenDialog] = useState(false);\n  const [dialogType, setDialogType] = useState(''); // 'delete' o 'spare'\n  const [searchTerm, setSearchTerm] = useState('');\n  const [deleteMode, setDeleteMode] = useState('spare'); // 'spare' o 'delete'\n  const [forceSpare, setForceSpare] = useState(false);\n\n  // Recupera l'ID del cantiere selezionato dal localStorage\n  const cantiereId = localStorage.getItem('selectedCantiereId');\n  const cantiereName = localStorage.getItem('selectedCantiereName');\n\n  // Gestisce il ritorno alla pagina dei cantieri\n  const handleBackToCantieri = () => {\n    navigate('/dashboard/cantieri');\n  };\n\n  // Gestisce il ritorno al menu admin (per admin che impersonano utenti)\n  const handleBackToAdmin = () => {\n    navigate('/dashboard/admin');\n  };\n\n  // Gestisce il ritorno alla pagina principale di posa cavi\n  const handleBackToPosa = () => {\n    navigate('/dashboard/cavi/posa');\n  };\n\n  // Gestisce il successo di un'operazione\n  const handleSuccess = message => {\n    setAlertMessage(message);\n    setAlertSeverity('success');\n    setOpenSnackbar(true);\n  };\n\n  // Gestisce l'errore di un'operazione\n  const handleError = message => {\n    setAlertMessage(message);\n    setAlertSeverity('error');\n    setOpenSnackbar(true);\n  };\n\n  // Chiude lo snackbar\n  const handleCloseSnackbar = () => {\n    setOpenSnackbar(false);\n  };\n\n  // Carica i cavi del cantiere\n  const loadCavi = async () => {\n    if (!cantiereId) return;\n    try {\n      setLoading(true);\n      const data = await caviService.getCavi(cantiereId, 0); // Tipo 0 = cavi attivi\n      setCavi(data);\n    } catch (error) {\n      handleError(`Errore nel caricamento dei cavi: ${error.message || 'Errore sconosciuto'}`);\n      console.error('Errore nel caricamento dei cavi:', error);\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  // Carica i cavi all'avvio del componente\n  useEffect(() => {\n    loadCavi();\n  }, [cantiereId]);\n\n  // Gestisce la selezione di un cavo\n  const handleCavoSelect = cavo => {\n    setSelectedCavo(cavo);\n\n    // Determina il tipo di dialogo in base allo stato del cavo\n    const isInstalled = cavo.stato_installazione === 'Installato' || cavo.metratura_reale && cavo.metratura_reale > 0;\n    setDialogType(isInstalled ? 'spare' : 'confirm');\n    setOpenDialog(true);\n  };\n\n  // Chiude il dialogo\n  const handleCloseDialog = () => {\n    setOpenDialog(false);\n    setSelectedCavo(null);\n    setDialogType('');\n    setForceSpare(false);\n  };\n\n  // Gestisce l'eliminazione o la marcatura come SPARE di un cavo\n  const handleDeleteCavo = async () => {\n    if (!selectedCavo) return;\n    try {\n      setLoading(true);\n      if (dialogType === 'spare') {\n        // Marca il cavo come SPARE\n        await caviService.markCavoAsSpare(cantiereId, selectedCavo.id_cavo, forceSpare);\n        handleSuccess(`Cavo ${selectedCavo.id_cavo} marcato come SPARE con successo`);\n      } else {\n        // Elimina il cavo o marcalo come SPARE in base alla modalità selezionata\n        await caviService.deleteCavo(cantiereId, selectedCavo.id_cavo, deleteMode);\n        handleSuccess(`Cavo ${selectedCavo.id_cavo} ${deleteMode === 'spare' ? 'marcato come SPARE' : 'eliminato'} con successo`);\n      }\n\n      // Ricarica la lista dei cavi\n      loadCavi();\n      handleCloseDialog();\n    } catch (error) {\n      handleError(`Errore durante l'operazione: ${error.message || 'Errore sconosciuto'}`);\n      console.error('Errore durante l\\'eliminazione/marcatura del cavo:', error);\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  // Filtra i cavi in base al termine di ricerca\n  const filteredCavi = cavi.filter(cavo => {\n    if (!searchTerm) return true;\n    const searchLower = searchTerm.toLowerCase();\n    return cavo.id_cavo.toLowerCase().includes(searchLower) || cavo.tipologia && cavo.tipologia.toLowerCase().includes(searchLower) || cavo.ubicazione_partenza && cavo.ubicazione_partenza.toLowerCase().includes(searchLower) || cavo.ubicazione_arrivo && cavo.ubicazione_arrivo.toLowerCase().includes(searchLower);\n  });\n\n  // Gestisce il cambio del termine di ricerca\n  const handleSearchChange = event => {\n    setSearchTerm(event.target.value);\n  };\n\n  // Se non c'è un cantiere selezionato, reindirizza alla pagina dei cantieri\n  if (!cantiereId) {\n    navigate('/dashboard/cantieri');\n    return null;\n  }\n  return /*#__PURE__*/_jsxDEV(Box, {\n    children: [/*#__PURE__*/_jsxDEV(Box, {\n      sx: {\n        mb: 3,\n        display: 'flex',\n        alignItems: 'center',\n        justifyContent: 'space-between'\n      },\n      children: [/*#__PURE__*/_jsxDEV(Box, {\n        sx: {\n          display: 'flex',\n          alignItems: 'center'\n        },\n        children: [/*#__PURE__*/_jsxDEV(IconButton, {\n          onClick: handleBackToPosa,\n          sx: {\n            mr: 1\n          },\n          children: /*#__PURE__*/_jsxDEV(ArrowBackIcon, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 185,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 184,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Typography, {\n          variant: \"h4\",\n          children: \"Elimina Cavo\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 187,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(IconButton, {\n          onClick: () => window.location.reload(),\n          sx: {\n            ml: 2\n          },\n          color: \"primary\",\n          title: \"Ricarica la pagina\",\n          children: /*#__PURE__*/_jsxDEV(RefreshIcon, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 196,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 190,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 183,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(AdminHomeButton, {}, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 199,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 182,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Paper, {\n      sx: {\n        mb: 3,\n        p: 2\n      },\n      children: /*#__PURE__*/_jsxDEV(Box, {\n        sx: {\n          display: 'flex',\n          justifyContent: 'space-between',\n          alignItems: 'center'\n        },\n        children: [/*#__PURE__*/_jsxDEV(Typography, {\n          variant: \"h6\",\n          children: [\"Cantiere: \", cantiereName, \" (ID: \", cantiereId, \")\"]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 204,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Button, {\n          variant: \"contained\",\n          color: \"primary\",\n          startIcon: /*#__PURE__*/_jsxDEV(ArrowBackIcon, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 210,\n            columnNumber: 24\n          }, this),\n          onClick: handleBackToPosa,\n          children: \"Torna a Posa e Collegamenti\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 207,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 203,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 202,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Paper, {\n      sx: {\n        p: 3,\n        mb: 3\n      },\n      children: [/*#__PURE__*/_jsxDEV(Typography, {\n        variant: \"h6\",\n        gutterBottom: true,\n        children: \"Elimina Cavo\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 219,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Typography, {\n        variant: \"body1\",\n        paragraph: true,\n        children: \"Questa funzionalit\\xE0 consente di eliminare un cavo dal cantiere o marcarlo come SPARE.\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 222,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Typography, {\n        variant: \"body2\",\n        color: \"textSecondary\",\n        paragraph: true,\n        children: \"Seleziona un cavo dalla lista. Per i cavi gi\\xE0 posati, verr\\xE0 offerta solo l'opzione di marcarli come SPARE invece di eliminarli completamente.\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 225,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Box, {\n        sx: {\n          mb: 3,\n          display: 'flex',\n          alignItems: 'center'\n        },\n        children: [/*#__PURE__*/_jsxDEV(TextField, {\n          label: \"Cerca cavo\",\n          variant: \"outlined\",\n          fullWidth: true,\n          value: searchTerm,\n          onChange: handleSearchChange,\n          placeholder: \"Cerca per ID, tipologia, ubicazione...\",\n          InputProps: {\n            startAdornment: /*#__PURE__*/_jsxDEV(SearchIcon, {\n              color: \"action\",\n              sx: {\n                mr: 1\n              }\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 239,\n              columnNumber: 31\n            }, this)\n          }\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 231,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Button, {\n          variant: \"contained\",\n          color: \"primary\",\n          onClick: loadCavi,\n          sx: {\n            ml: 2,\n            minWidth: '120px'\n          },\n          disabled: loading,\n          children: loading ? /*#__PURE__*/_jsxDEV(CircularProgress, {\n            size: 24\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 249,\n            columnNumber: 24\n          }, this) : \"Aggiorna\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 242,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 230,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 218,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Paper, {\n      sx: {\n        p: 3\n      },\n      children: [/*#__PURE__*/_jsxDEV(Typography, {\n        variant: \"h6\",\n        gutterBottom: true,\n        children: \"Cavi disponibili\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 256,\n        columnNumber: 9\n      }, this), loading ? /*#__PURE__*/_jsxDEV(Box, {\n        sx: {\n          display: 'flex',\n          justifyContent: 'center',\n          p: 3\n        },\n        children: /*#__PURE__*/_jsxDEV(CircularProgress, {}, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 262,\n          columnNumber: 13\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 261,\n        columnNumber: 11\n      }, this) : filteredCavi.length === 0 ? /*#__PURE__*/_jsxDEV(Alert, {\n        severity: \"info\",\n        sx: {\n          mt: 2\n        },\n        children: searchTerm ? \"Nessun cavo corrisponde ai criteri di ricerca\" : \"Nessun cavo disponibile\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 265,\n        columnNumber: 11\n      }, this) : /*#__PURE__*/_jsxDEV(List, {\n        children: filteredCavi.map(cavo => {\n          const isInstalled = cavo.stato_installazione === 'Installato' || cavo.metratura_reale && cavo.metratura_reale > 0;\n          return /*#__PURE__*/_jsxDEV(React.Fragment, {\n            children: [/*#__PURE__*/_jsxDEV(ListItem, {\n              button: true,\n              onClick: () => handleCavoSelect(cavo),\n              children: [/*#__PURE__*/_jsxDEV(ListItemText, {\n                primary: /*#__PURE__*/_jsxDEV(Box, {\n                  sx: {\n                    display: 'flex',\n                    alignItems: 'center'\n                  },\n                  children: [/*#__PURE__*/_jsxDEV(Typography, {\n                    variant: \"subtitle1\",\n                    children: cavo.id_cavo\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 278,\n                    columnNumber: 27\n                  }, this), isInstalled && /*#__PURE__*/_jsxDEV(Chip, {\n                    size: \"small\",\n                    label: \"Installato\",\n                    color: \"primary\",\n                    sx: {\n                      ml: 1\n                    }\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 280,\n                    columnNumber: 29\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 277,\n                  columnNumber: 25\n                }, this),\n                secondary: /*#__PURE__*/_jsxDEV(_Fragment, {\n                  children: [/*#__PURE__*/_jsxDEV(Typography, {\n                    variant: \"body2\",\n                    component: \"span\",\n                    children: [cavo.tipologia || 'N/A', \" - \", cavo.n_conduttori || 'N/A', \" x \", cavo.sezione || 'N/A']\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 291,\n                    columnNumber: 27\n                  }, this), /*#__PURE__*/_jsxDEV(\"br\", {}, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 294,\n                    columnNumber: 27\n                  }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                    variant: \"body2\",\n                    component: \"span\",\n                    children: [\"Da: \", cavo.ubicazione_partenza || 'N/A', \" A: \", cavo.ubicazione_arrivo || 'N/A']\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 295,\n                    columnNumber: 27\n                  }, this), cavo.metratura_reale > 0 && /*#__PURE__*/_jsxDEV(_Fragment, {\n                    children: [/*#__PURE__*/_jsxDEV(\"br\", {}, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 300,\n                      columnNumber: 31\n                    }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                      variant: \"body2\",\n                      component: \"span\",\n                      children: [\"Metri posati: \", cavo.metratura_reale, \" m\"]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 301,\n                      columnNumber: 31\n                    }, this)]\n                  }, void 0, true)]\n                }, void 0, true)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 275,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(ListItemSecondaryAction, {\n                children: /*#__PURE__*/_jsxDEV(IconButton, {\n                  edge: \"end\",\n                  onClick: () => handleCavoSelect(cavo),\n                  color: \"error\",\n                  children: /*#__PURE__*/_jsxDEV(DeleteIcon, {}, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 311,\n                    columnNumber: 25\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 310,\n                  columnNumber: 23\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 309,\n                columnNumber: 21\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 274,\n              columnNumber: 19\n            }, this), /*#__PURE__*/_jsxDEV(Divider, {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 315,\n              columnNumber: 19\n            }, this)]\n          }, cavo.id_cavo, true, {\n            fileName: _jsxFileName,\n            lineNumber: 273,\n            columnNumber: 17\n          }, this);\n        })\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 269,\n        columnNumber: 11\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 255,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Dialog, {\n      open: openDialog && dialogType === 'spare',\n      onClose: handleCloseDialog,\n      children: [/*#__PURE__*/_jsxDEV(DialogTitle, {\n        children: /*#__PURE__*/_jsxDEV(Box, {\n          sx: {\n            display: 'flex',\n            alignItems: 'center'\n          },\n          children: [/*#__PURE__*/_jsxDEV(WarningIcon, {\n            color: \"warning\",\n            sx: {\n              mr: 1\n            }\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 327,\n            columnNumber: 13\n          }, this), \"Marca cavo come SPARE\"]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 326,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 325,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(DialogContent, {\n        children: [/*#__PURE__*/_jsxDEV(DialogContentText, {\n          children: [\"Il cavo \", /*#__PURE__*/_jsxDEV(\"strong\", {\n            children: selectedCavo === null || selectedCavo === void 0 ? void 0 : selectedCavo.id_cavo\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 333,\n            columnNumber: 21\n          }, this), \" risulta installato o parzialmente posato.\", (selectedCavo === null || selectedCavo === void 0 ? void 0 : selectedCavo.metratura_reale) > 0 && /*#__PURE__*/_jsxDEV(_Fragment, {\n            children: [\" Metri posati: \", /*#__PURE__*/_jsxDEV(\"strong\", {\n              children: [selectedCavo.metratura_reale, \" m\"]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 335,\n              columnNumber: 32\n            }, this), \".\"]\n          }, void 0, true)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 332,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(DialogContentText, {\n          sx: {\n            mt: 2\n          },\n          children: \"Non \\xE8 possibile eliminarlo definitivamente. Vuoi marcarlo come SPARE/consumato?\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 338,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(FormControlLabel, {\n          control: /*#__PURE__*/_jsxDEV(Checkbox, {\n            checked: forceSpare,\n            onChange: e => setForceSpare(e.target.checked)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 343,\n            columnNumber: 15\n          }, this),\n          label: \"Forza marcatura come SPARE\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 341,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 331,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(DialogActions, {\n        children: [/*#__PURE__*/_jsxDEV(Button, {\n          onClick: handleCloseDialog,\n          children: \"Annulla\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 352,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Button, {\n          onClick: handleDeleteCavo,\n          color: \"warning\",\n          variant: \"contained\",\n          disabled: loading,\n          children: loading ? /*#__PURE__*/_jsxDEV(CircularProgress, {\n            size: 24\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 359,\n            columnNumber: 24\n          }, this) : \"Marca come SPARE\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 353,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 351,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 324,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Dialog, {\n      open: openDialog && dialogType === 'confirm',\n      onClose: handleCloseDialog,\n      children: [/*#__PURE__*/_jsxDEV(DialogTitle, {\n        children: /*#__PURE__*/_jsxDEV(Box, {\n          sx: {\n            display: 'flex',\n            alignItems: 'center'\n          },\n          children: [/*#__PURE__*/_jsxDEV(DeleteIcon, {\n            color: \"error\",\n            sx: {\n              mr: 1\n            }\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 368,\n            columnNumber: 13\n          }, this), \"Elimina cavo\"]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 367,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 366,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(DialogContent, {\n        children: [/*#__PURE__*/_jsxDEV(DialogContentText, {\n          children: [\"Stai per eliminare il cavo \", /*#__PURE__*/_jsxDEV(\"strong\", {\n            children: selectedCavo === null || selectedCavo === void 0 ? void 0 : selectedCavo.id_cavo\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 374,\n            columnNumber: 40\n          }, this), \".\"]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 373,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(FormControl, {\n          component: \"fieldset\",\n          sx: {\n            mt: 2\n          },\n          children: [/*#__PURE__*/_jsxDEV(FormLabel, {\n            component: \"legend\",\n            children: \"Scegli l'operazione da eseguire:\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 378,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(RadioGroup, {\n            value: deleteMode,\n            onChange: e => setDeleteMode(e.target.value),\n            children: [/*#__PURE__*/_jsxDEV(FormControlLabel, {\n              value: \"spare\",\n              control: /*#__PURE__*/_jsxDEV(Radio, {}, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 385,\n                columnNumber: 26\n              }, this),\n              label: \"Marca come SPARE (mantiene il cavo nel database ma lo contrassegna come non attivo)\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 383,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(FormControlLabel, {\n              value: \"delete\",\n              control: /*#__PURE__*/_jsxDEV(Radio, {}, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 390,\n                columnNumber: 26\n              }, this),\n              label: \"Elimina definitivamente (rimuove completamente il cavo dal database)\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 388,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 379,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 377,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 372,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(DialogActions, {\n        children: [/*#__PURE__*/_jsxDEV(Button, {\n          onClick: handleCloseDialog,\n          children: \"Annulla\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 397,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Button, {\n          onClick: handleDeleteCavo,\n          color: \"error\",\n          variant: \"contained\",\n          disabled: loading,\n          children: loading ? /*#__PURE__*/_jsxDEV(CircularProgress, {\n            size: 24\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 404,\n            columnNumber: 24\n          }, this) : deleteMode === 'spare' ? \"Marca come SPARE\" : \"Elimina definitivamente\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 398,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 396,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 365,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Snackbar, {\n      open: openSnackbar,\n      autoHideDuration: 6000,\n      onClose: handleCloseSnackbar,\n      anchorOrigin: {\n        vertical: 'bottom',\n        horizontal: 'center'\n      },\n      children: /*#__PURE__*/_jsxDEV(Alert, {\n        onClose: handleCloseSnackbar,\n        severity: alertSeverity,\n        sx: {\n          width: '100%'\n        },\n        children: alertMessage\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 415,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 409,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 181,\n    columnNumber: 5\n  }, this);\n};\n_s(EliminaCavoPage, \"RkqJo82ELBhXlgYk2HAn5PhzZeE=\", false, function () {\n  return [useNavigate, useAuth];\n});\n_c = EliminaCavoPage;\nexport default EliminaCavoPage;\nvar _c;\n$RefreshReg$(_c, \"EliminaCavoPage\");", "map": {"version": 3, "names": ["React", "useState", "Box", "Typography", "Paper", "<PERSON><PERSON>", "IconButton", "<PERSON><PERSON>", "Snackbar", "Dialog", "DialogTitle", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "DialogContentText", "DialogActions", "CircularProgress", "TextField", "Radio", "RadioGroup", "FormControlLabel", "FormControl", "FormLabel", "ArrowBack", "ArrowBackIcon", "Refresh", "RefreshIcon", "Delete", "DeleteIcon", "Warning", "WarningIcon", "useNavigate", "useAuth", "AdminHomeButton", "caviService", "jsxDEV", "_jsxDEV", "Fragment", "_Fragment", "EliminaCavoPage", "_s", "navigate", "isImpersonating", "alertMessage", "setAlertMessage", "alertSeverity", "setAlertSeverity", "openSnackbar", "setOpenSnackbar", "cavi", "<PERSON><PERSON><PERSON>", "loading", "setLoading", "selected<PERSON><PERSON><PERSON>", "setSelectedCavo", "openDialog", "setOpenDialog", "dialogType", "setDialogType", "searchTerm", "setSearchTerm", "deleteMode", "setDeleteMode", "forceSpare", "setForceSpare", "cantiereId", "localStorage", "getItem", "cantiereName", "handleBackToCantieri", "handleBackToAdmin", "handleBackToPosa", "handleSuccess", "message", "handleError", "handleCloseSnackbar", "loadCavi", "data", "get<PERSON><PERSON>", "error", "console", "useEffect", "handleCavoSelect", "cavo", "isInstalled", "stato_installazione", "metratura_reale", "handleCloseDialog", "handleDeleteCavo", "markCavoAsSpare", "id_cavo", "deleteCavo", "filteredCavi", "filter", "searchLower", "toLowerCase", "includes", "tipologia", "ubicazione_partenza", "ubicazione_arrivo", "handleSearchChange", "event", "target", "value", "children", "sx", "mb", "display", "alignItems", "justifyContent", "onClick", "mr", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "variant", "window", "location", "reload", "ml", "color", "title", "p", "startIcon", "gutterBottom", "paragraph", "label", "fullWidth", "onChange", "placeholder", "InputProps", "startAdornment", "SearchIcon", "min<PERSON><PERSON><PERSON>", "disabled", "size", "length", "severity", "mt", "List", "map", "ListItem", "button", "ListItemText", "primary", "Chip", "secondary", "component", "n_conduttori", "sezione", "ListItemSecondaryAction", "edge", "Divider", "open", "onClose", "control", "Checkbox", "checked", "e", "autoHideDuration", "anchor<PERSON><PERSON><PERSON>", "vertical", "horizontal", "width", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/PycharmProjects/CMS/webapp/frontend/src/pages/cavi/posa/EliminaCavoPage.js"], "sourcesContent": ["import React, { useState } from 'react';\nimport {\n  Box,\n  Typography,\n  Paper,\n  Button,\n  IconButton,\n  Alert,\n  Snackbar,\n  Dialog,\n  DialogTitle,\n  DialogContent,\n  DialogContentText,\n  DialogActions,\n  CircularProgress,\n  TextField,\n  Radio,\n  RadioGroup,\n  FormControlLabel,\n  FormControl,\n  FormLabel\n} from '@mui/material';\nimport {\n  ArrowBack as ArrowBackIcon,\n  Refresh as RefreshIcon,\n  Delete as DeleteIcon,\n  Warning as WarningIcon\n} from '@mui/icons-material';\nimport { useNavigate } from 'react-router-dom';\nimport { useAuth } from '../../../context/AuthContext';\nimport AdminHomeButton from '../../../components/common/AdminHomeButton';\nimport caviService from '../../../services/caviService';\n\nconst EliminaCavoPage = () => {\n  const navigate = useNavigate();\n  const { isImpersonating } = useAuth();\n  const [alertMessage, setAlertMessage] = useState(null);\n  const [alertSeverity, setAlertSeverity] = useState('success');\n  const [openSnackbar, setOpenSnackbar] = useState(false);\n\n  // Stati per la gestione dei cavi\n  const [cavi, setCavi] = useState([]);\n  const [loading, setLoading] = useState(false);\n  const [selectedCavo, setSelectedCavo] = useState(null);\n  const [openDialog, setOpenDialog] = useState(false);\n  const [dialogType, setDialogType] = useState(''); // 'delete' o 'spare'\n  const [searchTerm, setSearchTerm] = useState('');\n  const [deleteMode, setDeleteMode] = useState('spare'); // 'spare' o 'delete'\n  const [forceSpare, setForceSpare] = useState(false);\n\n  // Recupera l'ID del cantiere selezionato dal localStorage\n  const cantiereId = localStorage.getItem('selectedCantiereId');\n  const cantiereName = localStorage.getItem('selectedCantiereName');\n\n  // Gestisce il ritorno alla pagina dei cantieri\n  const handleBackToCantieri = () => {\n    navigate('/dashboard/cantieri');\n  };\n\n  // Gestisce il ritorno al menu admin (per admin che impersonano utenti)\n  const handleBackToAdmin = () => {\n    navigate('/dashboard/admin');\n  };\n\n  // Gestisce il ritorno alla pagina principale di posa cavi\n  const handleBackToPosa = () => {\n    navigate('/dashboard/cavi/posa');\n  };\n\n  // Gestisce il successo di un'operazione\n  const handleSuccess = (message) => {\n    setAlertMessage(message);\n    setAlertSeverity('success');\n    setOpenSnackbar(true);\n  };\n\n  // Gestisce l'errore di un'operazione\n  const handleError = (message) => {\n    setAlertMessage(message);\n    setAlertSeverity('error');\n    setOpenSnackbar(true);\n  };\n\n  // Chiude lo snackbar\n  const handleCloseSnackbar = () => {\n    setOpenSnackbar(false);\n  };\n\n  // Carica i cavi del cantiere\n  const loadCavi = async () => {\n    if (!cantiereId) return;\n\n    try {\n      setLoading(true);\n      const data = await caviService.getCavi(cantiereId, 0); // Tipo 0 = cavi attivi\n      setCavi(data);\n    } catch (error) {\n      handleError(`Errore nel caricamento dei cavi: ${error.message || 'Errore sconosciuto'}`);\n      console.error('Errore nel caricamento dei cavi:', error);\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  // Carica i cavi all'avvio del componente\n  useEffect(() => {\n    loadCavi();\n  }, [cantiereId]);\n\n  // Gestisce la selezione di un cavo\n  const handleCavoSelect = (cavo) => {\n    setSelectedCavo(cavo);\n\n    // Determina il tipo di dialogo in base allo stato del cavo\n    const isInstalled = cavo.stato_installazione === 'Installato' || (cavo.metratura_reale && cavo.metratura_reale > 0);\n    setDialogType(isInstalled ? 'spare' : 'confirm');\n    setOpenDialog(true);\n  };\n\n  // Chiude il dialogo\n  const handleCloseDialog = () => {\n    setOpenDialog(false);\n    setSelectedCavo(null);\n    setDialogType('');\n    setForceSpare(false);\n  };\n\n  // Gestisce l'eliminazione o la marcatura come SPARE di un cavo\n  const handleDeleteCavo = async () => {\n    if (!selectedCavo) return;\n\n    try {\n      setLoading(true);\n\n      if (dialogType === 'spare') {\n        // Marca il cavo come SPARE\n        await caviService.markCavoAsSpare(cantiereId, selectedCavo.id_cavo, forceSpare);\n        handleSuccess(`Cavo ${selectedCavo.id_cavo} marcato come SPARE con successo`);\n      } else {\n        // Elimina il cavo o marcalo come SPARE in base alla modalità selezionata\n        await caviService.deleteCavo(cantiereId, selectedCavo.id_cavo, deleteMode);\n        handleSuccess(`Cavo ${selectedCavo.id_cavo} ${deleteMode === 'spare' ? 'marcato come SPARE' : 'eliminato'} con successo`);\n      }\n\n      // Ricarica la lista dei cavi\n      loadCavi();\n      handleCloseDialog();\n    } catch (error) {\n      handleError(`Errore durante l'operazione: ${error.message || 'Errore sconosciuto'}`);\n      console.error('Errore durante l\\'eliminazione/marcatura del cavo:', error);\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  // Filtra i cavi in base al termine di ricerca\n  const filteredCavi = cavi.filter(cavo => {\n    if (!searchTerm) return true;\n\n    const searchLower = searchTerm.toLowerCase();\n    return (\n      cavo.id_cavo.toLowerCase().includes(searchLower) ||\n      (cavo.tipologia && cavo.tipologia.toLowerCase().includes(searchLower)) ||\n      (cavo.ubicazione_partenza && cavo.ubicazione_partenza.toLowerCase().includes(searchLower)) ||\n      (cavo.ubicazione_arrivo && cavo.ubicazione_arrivo.toLowerCase().includes(searchLower))\n    );\n  });\n\n  // Gestisce il cambio del termine di ricerca\n  const handleSearchChange = (event) => {\n    setSearchTerm(event.target.value);\n  };\n\n  // Se non c'è un cantiere selezionato, reindirizza alla pagina dei cantieri\n  if (!cantiereId) {\n    navigate('/dashboard/cantieri');\n    return null;\n  }\n\n  return (\n    <Box>\n      <Box sx={{ mb: 3, display: 'flex', alignItems: 'center', justifyContent: 'space-between' }}>\n        <Box sx={{ display: 'flex', alignItems: 'center' }}>\n          <IconButton onClick={handleBackToPosa} sx={{ mr: 1 }}>\n            <ArrowBackIcon />\n          </IconButton>\n          <Typography variant=\"h4\">\n            Elimina Cavo\n          </Typography>\n          <IconButton\n            onClick={() => window.location.reload()}\n            sx={{ ml: 2 }}\n            color=\"primary\"\n            title=\"Ricarica la pagina\"\n          >\n            <RefreshIcon />\n          </IconButton>\n        </Box>\n        <AdminHomeButton />\n      </Box>\n\n      <Paper sx={{ mb: 3, p: 2 }}>\n        <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>\n          <Typography variant=\"h6\">\n            Cantiere: {cantiereName} (ID: {cantiereId})\n          </Typography>\n          <Button\n            variant=\"contained\"\n            color=\"primary\"\n            startIcon={<ArrowBackIcon />}\n            onClick={handleBackToPosa}\n          >\n            Torna a Posa e Collegamenti\n          </Button>\n        </Box>\n      </Paper>\n\n      <Paper sx={{ p: 3, mb: 3 }}>\n        <Typography variant=\"h6\" gutterBottom>\n          Elimina Cavo\n        </Typography>\n        <Typography variant=\"body1\" paragraph>\n          Questa funzionalità consente di eliminare un cavo dal cantiere o marcarlo come SPARE.\n        </Typography>\n        <Typography variant=\"body2\" color=\"textSecondary\" paragraph>\n          Seleziona un cavo dalla lista. Per i cavi già posati, verrà offerta solo l'opzione di marcarli come SPARE invece di eliminarli completamente.\n        </Typography>\n\n        {/* Campo di ricerca */}\n        <Box sx={{ mb: 3, display: 'flex', alignItems: 'center' }}>\n          <TextField\n            label=\"Cerca cavo\"\n            variant=\"outlined\"\n            fullWidth\n            value={searchTerm}\n            onChange={handleSearchChange}\n            placeholder=\"Cerca per ID, tipologia, ubicazione...\"\n            InputProps={{\n              startAdornment: <SearchIcon color=\"action\" sx={{ mr: 1 }} />\n            }}\n          />\n          <Button\n            variant=\"contained\"\n            color=\"primary\"\n            onClick={loadCavi}\n            sx={{ ml: 2, minWidth: '120px' }}\n            disabled={loading}\n          >\n            {loading ? <CircularProgress size={24} /> : \"Aggiorna\"}\n          </Button>\n        </Box>\n      </Paper>\n\n      {/* Lista dei cavi */}\n      <Paper sx={{ p: 3 }}>\n        <Typography variant=\"h6\" gutterBottom>\n          Cavi disponibili\n        </Typography>\n\n        {loading ? (\n          <Box sx={{ display: 'flex', justifyContent: 'center', p: 3 }}>\n            <CircularProgress />\n          </Box>\n        ) : filteredCavi.length === 0 ? (\n          <Alert severity=\"info\" sx={{ mt: 2 }}>\n            {searchTerm ? \"Nessun cavo corrisponde ai criteri di ricerca\" : \"Nessun cavo disponibile\"}\n          </Alert>\n        ) : (\n          <List>\n            {filteredCavi.map((cavo) => {\n              const isInstalled = cavo.stato_installazione === 'Installato' || (cavo.metratura_reale && cavo.metratura_reale > 0);\n              return (\n                <React.Fragment key={cavo.id_cavo}>\n                  <ListItem button onClick={() => handleCavoSelect(cavo)}>\n                    <ListItemText\n                      primary={\n                        <Box sx={{ display: 'flex', alignItems: 'center' }}>\n                          <Typography variant=\"subtitle1\">{cavo.id_cavo}</Typography>\n                          {isInstalled && (\n                            <Chip\n                              size=\"small\"\n                              label=\"Installato\"\n                              color=\"primary\"\n                              sx={{ ml: 1 }}\n                            />\n                          )}\n                        </Box>\n                      }\n                      secondary={\n                        <>\n                          <Typography variant=\"body2\" component=\"span\">\n                            {cavo.tipologia || 'N/A'} - {cavo.n_conduttori || 'N/A'} x {cavo.sezione || 'N/A'}\n                          </Typography>\n                          <br />\n                          <Typography variant=\"body2\" component=\"span\">\n                            Da: {cavo.ubicazione_partenza || 'N/A'} A: {cavo.ubicazione_arrivo || 'N/A'}\n                          </Typography>\n                          {cavo.metratura_reale > 0 && (\n                            <>\n                              <br />\n                              <Typography variant=\"body2\" component=\"span\">\n                                Metri posati: {cavo.metratura_reale} m\n                              </Typography>\n                            </>\n                          )}\n                        </>\n                      }\n                    />\n                    <ListItemSecondaryAction>\n                      <IconButton edge=\"end\" onClick={() => handleCavoSelect(cavo)} color=\"error\">\n                        <DeleteIcon />\n                      </IconButton>\n                    </ListItemSecondaryAction>\n                  </ListItem>\n                  <Divider />\n                </React.Fragment>\n              );\n            })}\n          </List>\n        )}\n      </Paper>\n\n      {/* Dialogo di conferma per cavi installati (solo SPARE) */}\n      <Dialog open={openDialog && dialogType === 'spare'} onClose={handleCloseDialog}>\n        <DialogTitle>\n          <Box sx={{ display: 'flex', alignItems: 'center' }}>\n            <WarningIcon color=\"warning\" sx={{ mr: 1 }} />\n            Marca cavo come SPARE\n          </Box>\n        </DialogTitle>\n        <DialogContent>\n          <DialogContentText>\n            Il cavo <strong>{selectedCavo?.id_cavo}</strong> risulta installato o parzialmente posato.\n            {selectedCavo?.metratura_reale > 0 && (\n              <> Metri posati: <strong>{selectedCavo.metratura_reale} m</strong>.</>\n            )}\n          </DialogContentText>\n          <DialogContentText sx={{ mt: 2 }}>\n            Non è possibile eliminarlo definitivamente. Vuoi marcarlo come SPARE/consumato?\n          </DialogContentText>\n          <FormControlLabel\n            control={\n              <Checkbox\n                checked={forceSpare}\n                onChange={(e) => setForceSpare(e.target.checked)}\n              />\n            }\n            label=\"Forza marcatura come SPARE\"\n          />\n        </DialogContent>\n        <DialogActions>\n          <Button onClick={handleCloseDialog}>Annulla</Button>\n          <Button\n            onClick={handleDeleteCavo}\n            color=\"warning\"\n            variant=\"contained\"\n            disabled={loading}\n          >\n            {loading ? <CircularProgress size={24} /> : \"Marca come SPARE\"}\n          </Button>\n        </DialogActions>\n      </Dialog>\n\n      {/* Dialogo di conferma per cavi non installati (scelta tra SPARE e DELETE) */}\n      <Dialog open={openDialog && dialogType === 'confirm'} onClose={handleCloseDialog}>\n        <DialogTitle>\n          <Box sx={{ display: 'flex', alignItems: 'center' }}>\n            <DeleteIcon color=\"error\" sx={{ mr: 1 }} />\n            Elimina cavo\n          </Box>\n        </DialogTitle>\n        <DialogContent>\n          <DialogContentText>\n            Stai per eliminare il cavo <strong>{selectedCavo?.id_cavo}</strong>.\n          </DialogContentText>\n\n          <FormControl component=\"fieldset\" sx={{ mt: 2 }}>\n            <FormLabel component=\"legend\">Scegli l'operazione da eseguire:</FormLabel>\n            <RadioGroup\n              value={deleteMode}\n              onChange={(e) => setDeleteMode(e.target.value)}\n            >\n              <FormControlLabel\n                value=\"spare\"\n                control={<Radio />}\n                label=\"Marca come SPARE (mantiene il cavo nel database ma lo contrassegna come non attivo)\"\n              />\n              <FormControlLabel\n                value=\"delete\"\n                control={<Radio />}\n                label=\"Elimina definitivamente (rimuove completamente il cavo dal database)\"\n              />\n            </RadioGroup>\n          </FormControl>\n        </DialogContent>\n        <DialogActions>\n          <Button onClick={handleCloseDialog}>Annulla</Button>\n          <Button\n            onClick={handleDeleteCavo}\n            color=\"error\"\n            variant=\"contained\"\n            disabled={loading}\n          >\n            {loading ? <CircularProgress size={24} /> : (deleteMode === 'spare' ? \"Marca come SPARE\" : \"Elimina definitivamente\")}\n          </Button>\n        </DialogActions>\n      </Dialog>\n\n      <Snackbar\n        open={openSnackbar}\n        autoHideDuration={6000}\n        onClose={handleCloseSnackbar}\n        anchorOrigin={{ vertical: 'bottom', horizontal: 'center' }}\n      >\n        <Alert onClose={handleCloseSnackbar} severity={alertSeverity} sx={{ width: '100%' }}>\n          {alertMessage}\n        </Alert>\n      </Snackbar>\n    </Box>\n  );\n};\n\nexport default EliminaCavoPage;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,QAAQ,OAAO;AACvC,SACEC,GAAG,EACHC,UAAU,EACVC,KAAK,EACLC,MAAM,EACNC,UAAU,EACVC,KAAK,EACLC,QAAQ,EACRC,MAAM,EACNC,WAAW,EACXC,aAAa,EACbC,iBAAiB,EACjBC,aAAa,EACbC,gBAAgB,EAChBC,SAAS,EACTC,KAAK,EACLC,UAAU,EACVC,gBAAgB,EAChBC,WAAW,EACXC,SAAS,QACJ,eAAe;AACtB,SACEC,SAAS,IAAIC,aAAa,EAC1BC,OAAO,IAAIC,WAAW,EACtBC,MAAM,IAAIC,UAAU,EACpBC,OAAO,IAAIC,WAAW,QACjB,qBAAqB;AAC5B,SAASC,WAAW,QAAQ,kBAAkB;AAC9C,SAASC,OAAO,QAAQ,8BAA8B;AACtD,OAAOC,eAAe,MAAM,4CAA4C;AACxE,OAAOC,WAAW,MAAM,+BAA+B;AAAC,SAAAC,MAAA,IAAAC,OAAA,EAAAC,QAAA,IAAAC,SAAA;AAExD,MAAMC,eAAe,GAAGA,CAAA,KAAM;EAAAC,EAAA;EAC5B,MAAMC,QAAQ,GAAGV,WAAW,CAAC,CAAC;EAC9B,MAAM;IAAEW;EAAgB,CAAC,GAAGV,OAAO,CAAC,CAAC;EACrC,MAAM,CAACW,YAAY,EAAEC,eAAe,CAAC,GAAGzC,QAAQ,CAAC,IAAI,CAAC;EACtD,MAAM,CAAC0C,aAAa,EAAEC,gBAAgB,CAAC,GAAG3C,QAAQ,CAAC,SAAS,CAAC;EAC7D,MAAM,CAAC4C,YAAY,EAAEC,eAAe,CAAC,GAAG7C,QAAQ,CAAC,KAAK,CAAC;;EAEvD;EACA,MAAM,CAAC8C,IAAI,EAAEC,OAAO,CAAC,GAAG/C,QAAQ,CAAC,EAAE,CAAC;EACpC,MAAM,CAACgD,OAAO,EAAEC,UAAU,CAAC,GAAGjD,QAAQ,CAAC,KAAK,CAAC;EAC7C,MAAM,CAACkD,YAAY,EAAEC,eAAe,CAAC,GAAGnD,QAAQ,CAAC,IAAI,CAAC;EACtD,MAAM,CAACoD,UAAU,EAAEC,aAAa,CAAC,GAAGrD,QAAQ,CAAC,KAAK,CAAC;EACnD,MAAM,CAACsD,UAAU,EAAEC,aAAa,CAAC,GAAGvD,QAAQ,CAAC,EAAE,CAAC,CAAC,CAAC;EAClD,MAAM,CAACwD,UAAU,EAAEC,aAAa,CAAC,GAAGzD,QAAQ,CAAC,EAAE,CAAC;EAChD,MAAM,CAAC0D,UAAU,EAAEC,aAAa,CAAC,GAAG3D,QAAQ,CAAC,OAAO,CAAC,CAAC,CAAC;EACvD,MAAM,CAAC4D,UAAU,EAAEC,aAAa,CAAC,GAAG7D,QAAQ,CAAC,KAAK,CAAC;;EAEnD;EACA,MAAM8D,UAAU,GAAGC,YAAY,CAACC,OAAO,CAAC,oBAAoB,CAAC;EAC7D,MAAMC,YAAY,GAAGF,YAAY,CAACC,OAAO,CAAC,sBAAsB,CAAC;;EAEjE;EACA,MAAME,oBAAoB,GAAGA,CAAA,KAAM;IACjC5B,QAAQ,CAAC,qBAAqB,CAAC;EACjC,CAAC;;EAED;EACA,MAAM6B,iBAAiB,GAAGA,CAAA,KAAM;IAC9B7B,QAAQ,CAAC,kBAAkB,CAAC;EAC9B,CAAC;;EAED;EACA,MAAM8B,gBAAgB,GAAGA,CAAA,KAAM;IAC7B9B,QAAQ,CAAC,sBAAsB,CAAC;EAClC,CAAC;;EAED;EACA,MAAM+B,aAAa,GAAIC,OAAO,IAAK;IACjC7B,eAAe,CAAC6B,OAAO,CAAC;IACxB3B,gBAAgB,CAAC,SAAS,CAAC;IAC3BE,eAAe,CAAC,IAAI,CAAC;EACvB,CAAC;;EAED;EACA,MAAM0B,WAAW,GAAID,OAAO,IAAK;IAC/B7B,eAAe,CAAC6B,OAAO,CAAC;IACxB3B,gBAAgB,CAAC,OAAO,CAAC;IACzBE,eAAe,CAAC,IAAI,CAAC;EACvB,CAAC;;EAED;EACA,MAAM2B,mBAAmB,GAAGA,CAAA,KAAM;IAChC3B,eAAe,CAAC,KAAK,CAAC;EACxB,CAAC;;EAED;EACA,MAAM4B,QAAQ,GAAG,MAAAA,CAAA,KAAY;IAC3B,IAAI,CAACX,UAAU,EAAE;IAEjB,IAAI;MACFb,UAAU,CAAC,IAAI,CAAC;MAChB,MAAMyB,IAAI,GAAG,MAAM3C,WAAW,CAAC4C,OAAO,CAACb,UAAU,EAAE,CAAC,CAAC,CAAC,CAAC;MACvDf,OAAO,CAAC2B,IAAI,CAAC;IACf,CAAC,CAAC,OAAOE,KAAK,EAAE;MACdL,WAAW,CAAC,oCAAoCK,KAAK,CAACN,OAAO,IAAI,oBAAoB,EAAE,CAAC;MACxFO,OAAO,CAACD,KAAK,CAAC,kCAAkC,EAAEA,KAAK,CAAC;IAC1D,CAAC,SAAS;MACR3B,UAAU,CAAC,KAAK,CAAC;IACnB;EACF,CAAC;;EAED;EACA6B,SAAS,CAAC,MAAM;IACdL,QAAQ,CAAC,CAAC;EACZ,CAAC,EAAE,CAACX,UAAU,CAAC,CAAC;;EAEhB;EACA,MAAMiB,gBAAgB,GAAIC,IAAI,IAAK;IACjC7B,eAAe,CAAC6B,IAAI,CAAC;;IAErB;IACA,MAAMC,WAAW,GAAGD,IAAI,CAACE,mBAAmB,KAAK,YAAY,IAAKF,IAAI,CAACG,eAAe,IAAIH,IAAI,CAACG,eAAe,GAAG,CAAE;IACnH5B,aAAa,CAAC0B,WAAW,GAAG,OAAO,GAAG,SAAS,CAAC;IAChD5B,aAAa,CAAC,IAAI,CAAC;EACrB,CAAC;;EAED;EACA,MAAM+B,iBAAiB,GAAGA,CAAA,KAAM;IAC9B/B,aAAa,CAAC,KAAK,CAAC;IACpBF,eAAe,CAAC,IAAI,CAAC;IACrBI,aAAa,CAAC,EAAE,CAAC;IACjBM,aAAa,CAAC,KAAK,CAAC;EACtB,CAAC;;EAED;EACA,MAAMwB,gBAAgB,GAAG,MAAAA,CAAA,KAAY;IACnC,IAAI,CAACnC,YAAY,EAAE;IAEnB,IAAI;MACFD,UAAU,CAAC,IAAI,CAAC;MAEhB,IAAIK,UAAU,KAAK,OAAO,EAAE;QAC1B;QACA,MAAMvB,WAAW,CAACuD,eAAe,CAACxB,UAAU,EAAEZ,YAAY,CAACqC,OAAO,EAAE3B,UAAU,CAAC;QAC/ES,aAAa,CAAC,QAAQnB,YAAY,CAACqC,OAAO,kCAAkC,CAAC;MAC/E,CAAC,MAAM;QACL;QACA,MAAMxD,WAAW,CAACyD,UAAU,CAAC1B,UAAU,EAAEZ,YAAY,CAACqC,OAAO,EAAE7B,UAAU,CAAC;QAC1EW,aAAa,CAAC,QAAQnB,YAAY,CAACqC,OAAO,IAAI7B,UAAU,KAAK,OAAO,GAAG,oBAAoB,GAAG,WAAW,eAAe,CAAC;MAC3H;;MAEA;MACAe,QAAQ,CAAC,CAAC;MACVW,iBAAiB,CAAC,CAAC;IACrB,CAAC,CAAC,OAAOR,KAAK,EAAE;MACdL,WAAW,CAAC,gCAAgCK,KAAK,CAACN,OAAO,IAAI,oBAAoB,EAAE,CAAC;MACpFO,OAAO,CAACD,KAAK,CAAC,oDAAoD,EAAEA,KAAK,CAAC;IAC5E,CAAC,SAAS;MACR3B,UAAU,CAAC,KAAK,CAAC;IACnB;EACF,CAAC;;EAED;EACA,MAAMwC,YAAY,GAAG3C,IAAI,CAAC4C,MAAM,CAACV,IAAI,IAAI;IACvC,IAAI,CAACxB,UAAU,EAAE,OAAO,IAAI;IAE5B,MAAMmC,WAAW,GAAGnC,UAAU,CAACoC,WAAW,CAAC,CAAC;IAC5C,OACEZ,IAAI,CAACO,OAAO,CAACK,WAAW,CAAC,CAAC,CAACC,QAAQ,CAACF,WAAW,CAAC,IAC/CX,IAAI,CAACc,SAAS,IAAId,IAAI,CAACc,SAAS,CAACF,WAAW,CAAC,CAAC,CAACC,QAAQ,CAACF,WAAW,CAAE,IACrEX,IAAI,CAACe,mBAAmB,IAAIf,IAAI,CAACe,mBAAmB,CAACH,WAAW,CAAC,CAAC,CAACC,QAAQ,CAACF,WAAW,CAAE,IACzFX,IAAI,CAACgB,iBAAiB,IAAIhB,IAAI,CAACgB,iBAAiB,CAACJ,WAAW,CAAC,CAAC,CAACC,QAAQ,CAACF,WAAW,CAAE;EAE1F,CAAC,CAAC;;EAEF;EACA,MAAMM,kBAAkB,GAAIC,KAAK,IAAK;IACpCzC,aAAa,CAACyC,KAAK,CAACC,MAAM,CAACC,KAAK,CAAC;EACnC,CAAC;;EAED;EACA,IAAI,CAACtC,UAAU,EAAE;IACfxB,QAAQ,CAAC,qBAAqB,CAAC;IAC/B,OAAO,IAAI;EACb;EAEA,oBACEL,OAAA,CAAChC,GAAG;IAAAoG,QAAA,gBACFpE,OAAA,CAAChC,GAAG;MAACqG,EAAE,EAAE;QAAEC,EAAE,EAAE,CAAC;QAAEC,OAAO,EAAE,MAAM;QAAEC,UAAU,EAAE,QAAQ;QAAEC,cAAc,EAAE;MAAgB,CAAE;MAAAL,QAAA,gBACzFpE,OAAA,CAAChC,GAAG;QAACqG,EAAE,EAAE;UAAEE,OAAO,EAAE,MAAM;UAAEC,UAAU,EAAE;QAAS,CAAE;QAAAJ,QAAA,gBACjDpE,OAAA,CAAC5B,UAAU;UAACsG,OAAO,EAAEvC,gBAAiB;UAACkC,EAAE,EAAE;YAAEM,EAAE,EAAE;UAAE,CAAE;UAAAP,QAAA,eACnDpE,OAAA,CAACZ,aAAa;YAAAwF,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACP,CAAC,eACb/E,OAAA,CAAC/B,UAAU;UAAC+G,OAAO,EAAC,IAAI;UAAAZ,QAAA,EAAC;QAEzB;UAAAQ,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAY,CAAC,eACb/E,OAAA,CAAC5B,UAAU;UACTsG,OAAO,EAAEA,CAAA,KAAMO,MAAM,CAACC,QAAQ,CAACC,MAAM,CAAC,CAAE;UACxCd,EAAE,EAAE;YAAEe,EAAE,EAAE;UAAE,CAAE;UACdC,KAAK,EAAC,SAAS;UACfC,KAAK,EAAC,oBAAoB;UAAAlB,QAAA,eAE1BpE,OAAA,CAACV,WAAW;YAAAsF,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACL,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACV,CAAC,eACN/E,OAAA,CAACH,eAAe;QAAA+E,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAChB,CAAC,eAEN/E,OAAA,CAAC9B,KAAK;MAACmG,EAAE,EAAE;QAAEC,EAAE,EAAE,CAAC;QAAEiB,CAAC,EAAE;MAAE,CAAE;MAAAnB,QAAA,eACzBpE,OAAA,CAAChC,GAAG;QAACqG,EAAE,EAAE;UAAEE,OAAO,EAAE,MAAM;UAAEE,cAAc,EAAE,eAAe;UAAED,UAAU,EAAE;QAAS,CAAE;QAAAJ,QAAA,gBAClFpE,OAAA,CAAC/B,UAAU;UAAC+G,OAAO,EAAC,IAAI;UAAAZ,QAAA,GAAC,YACb,EAACpC,YAAY,EAAC,QAAM,EAACH,UAAU,EAAC,GAC5C;QAAA;UAAA+C,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAY,CAAC,eACb/E,OAAA,CAAC7B,MAAM;UACL6G,OAAO,EAAC,WAAW;UACnBK,KAAK,EAAC,SAAS;UACfG,SAAS,eAAExF,OAAA,CAACZ,aAAa;YAAAwF,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAE;UAC7BL,OAAO,EAAEvC,gBAAiB;UAAAiC,QAAA,EAC3B;QAED;UAAAQ,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACN;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACD,CAAC,eAER/E,OAAA,CAAC9B,KAAK;MAACmG,EAAE,EAAE;QAAEkB,CAAC,EAAE,CAAC;QAAEjB,EAAE,EAAE;MAAE,CAAE;MAAAF,QAAA,gBACzBpE,OAAA,CAAC/B,UAAU;QAAC+G,OAAO,EAAC,IAAI;QAACS,YAAY;QAAArB,QAAA,EAAC;MAEtC;QAAAQ,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAY,CAAC,eACb/E,OAAA,CAAC/B,UAAU;QAAC+G,OAAO,EAAC,OAAO;QAACU,SAAS;QAAAtB,QAAA,EAAC;MAEtC;QAAAQ,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAY,CAAC,eACb/E,OAAA,CAAC/B,UAAU;QAAC+G,OAAO,EAAC,OAAO;QAACK,KAAK,EAAC,eAAe;QAACK,SAAS;QAAAtB,QAAA,EAAC;MAE5D;QAAAQ,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAY,CAAC,eAGb/E,OAAA,CAAChC,GAAG;QAACqG,EAAE,EAAE;UAAEC,EAAE,EAAE,CAAC;UAAEC,OAAO,EAAE,MAAM;UAAEC,UAAU,EAAE;QAAS,CAAE;QAAAJ,QAAA,gBACxDpE,OAAA,CAACnB,SAAS;UACR8G,KAAK,EAAC,YAAY;UAClBX,OAAO,EAAC,UAAU;UAClBY,SAAS;UACTzB,KAAK,EAAE5C,UAAW;UAClBsE,QAAQ,EAAE7B,kBAAmB;UAC7B8B,WAAW,EAAC,wCAAwC;UACpDC,UAAU,EAAE;YACVC,cAAc,eAAEhG,OAAA,CAACiG,UAAU;cAACZ,KAAK,EAAC,QAAQ;cAAChB,EAAE,EAAE;gBAAEM,EAAE,EAAE;cAAE;YAAE;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE;UAC7D;QAAE;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,eACF/E,OAAA,CAAC7B,MAAM;UACL6G,OAAO,EAAC,WAAW;UACnBK,KAAK,EAAC,SAAS;UACfX,OAAO,EAAElC,QAAS;UAClB6B,EAAE,EAAE;YAAEe,EAAE,EAAE,CAAC;YAAEc,QAAQ,EAAE;UAAQ,CAAE;UACjCC,QAAQ,EAAEpF,OAAQ;UAAAqD,QAAA,EAEjBrD,OAAO,gBAAGf,OAAA,CAACpB,gBAAgB;YAACwH,IAAI,EAAE;UAAG;YAAAxB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,GAAG;QAAU;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAChD,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACN,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACD,CAAC,eAGR/E,OAAA,CAAC9B,KAAK;MAACmG,EAAE,EAAE;QAAEkB,CAAC,EAAE;MAAE,CAAE;MAAAnB,QAAA,gBAClBpE,OAAA,CAAC/B,UAAU;QAAC+G,OAAO,EAAC,IAAI;QAACS,YAAY;QAAArB,QAAA,EAAC;MAEtC;QAAAQ,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAY,CAAC,EAEZhE,OAAO,gBACNf,OAAA,CAAChC,GAAG;QAACqG,EAAE,EAAE;UAAEE,OAAO,EAAE,MAAM;UAAEE,cAAc,EAAE,QAAQ;UAAEc,CAAC,EAAE;QAAE,CAAE;QAAAnB,QAAA,eAC3DpE,OAAA,CAACpB,gBAAgB;UAAAgG,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACjB,CAAC,GACJvB,YAAY,CAAC6C,MAAM,KAAK,CAAC,gBAC3BrG,OAAA,CAAC3B,KAAK;QAACiI,QAAQ,EAAC,MAAM;QAACjC,EAAE,EAAE;UAAEkC,EAAE,EAAE;QAAE,CAAE;QAAAnC,QAAA,EAClC7C,UAAU,GAAG,+CAA+C,GAAG;MAAyB;QAAAqD,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACpF,CAAC,gBAER/E,OAAA,CAACwG,IAAI;QAAApC,QAAA,EACFZ,YAAY,CAACiD,GAAG,CAAE1D,IAAI,IAAK;UAC1B,MAAMC,WAAW,GAAGD,IAAI,CAACE,mBAAmB,KAAK,YAAY,IAAKF,IAAI,CAACG,eAAe,IAAIH,IAAI,CAACG,eAAe,GAAG,CAAE;UACnH,oBACElD,OAAA,CAAClC,KAAK,CAACmC,QAAQ;YAAAmE,QAAA,gBACbpE,OAAA,CAAC0G,QAAQ;cAACC,MAAM;cAACjC,OAAO,EAAEA,CAAA,KAAM5B,gBAAgB,CAACC,IAAI,CAAE;cAAAqB,QAAA,gBACrDpE,OAAA,CAAC4G,YAAY;gBACXC,OAAO,eACL7G,OAAA,CAAChC,GAAG;kBAACqG,EAAE,EAAE;oBAAEE,OAAO,EAAE,MAAM;oBAAEC,UAAU,EAAE;kBAAS,CAAE;kBAAAJ,QAAA,gBACjDpE,OAAA,CAAC/B,UAAU;oBAAC+G,OAAO,EAAC,WAAW;oBAAAZ,QAAA,EAAErB,IAAI,CAACO;kBAAO;oBAAAsB,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAa,CAAC,EAC1D/B,WAAW,iBACVhD,OAAA,CAAC8G,IAAI;oBACHV,IAAI,EAAC,OAAO;oBACZT,KAAK,EAAC,YAAY;oBAClBN,KAAK,EAAC,SAAS;oBACfhB,EAAE,EAAE;sBAAEe,EAAE,EAAE;oBAAE;kBAAE;oBAAAR,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACf,CACF;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACE,CACN;gBACDgC,SAAS,eACP/G,OAAA,CAAAE,SAAA;kBAAAkE,QAAA,gBACEpE,OAAA,CAAC/B,UAAU;oBAAC+G,OAAO,EAAC,OAAO;oBAACgC,SAAS,EAAC,MAAM;oBAAA5C,QAAA,GACzCrB,IAAI,CAACc,SAAS,IAAI,KAAK,EAAC,KAAG,EAACd,IAAI,CAACkE,YAAY,IAAI,KAAK,EAAC,KAAG,EAAClE,IAAI,CAACmE,OAAO,IAAI,KAAK;kBAAA;oBAAAtC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACvE,CAAC,eACb/E,OAAA;oBAAA4E,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAK,CAAC,eACN/E,OAAA,CAAC/B,UAAU;oBAAC+G,OAAO,EAAC,OAAO;oBAACgC,SAAS,EAAC,MAAM;oBAAA5C,QAAA,GAAC,MACvC,EAACrB,IAAI,CAACe,mBAAmB,IAAI,KAAK,EAAC,MAAI,EAACf,IAAI,CAACgB,iBAAiB,IAAI,KAAK;kBAAA;oBAAAa,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACjE,CAAC,EACZhC,IAAI,CAACG,eAAe,GAAG,CAAC,iBACvBlD,OAAA,CAAAE,SAAA;oBAAAkE,QAAA,gBACEpE,OAAA;sBAAA4E,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAK,CAAC,eACN/E,OAAA,CAAC/B,UAAU;sBAAC+G,OAAO,EAAC,OAAO;sBAACgC,SAAS,EAAC,MAAM;sBAAA5C,QAAA,GAAC,gBAC7B,EAACrB,IAAI,CAACG,eAAe,EAAC,IACtC;oBAAA;sBAAA0B,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAY,CAAC;kBAAA,eACb,CACH;gBAAA,eACD;cACH;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACF,CAAC,eACF/E,OAAA,CAACmH,uBAAuB;gBAAA/C,QAAA,eACtBpE,OAAA,CAAC5B,UAAU;kBAACgJ,IAAI,EAAC,KAAK;kBAAC1C,OAAO,EAAEA,CAAA,KAAM5B,gBAAgB,CAACC,IAAI,CAAE;kBAACsC,KAAK,EAAC,OAAO;kBAAAjB,QAAA,eACzEpE,OAAA,CAACR,UAAU;oBAAAoF,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACJ;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACU,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAClB,CAAC,eACX/E,OAAA,CAACqH,OAAO;cAAAzC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC;UAAA,GA1CQhC,IAAI,CAACO,OAAO;YAAAsB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OA2CjB,CAAC;QAErB,CAAC;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACE,CACP;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACI,CAAC,eAGR/E,OAAA,CAACzB,MAAM;MAAC+I,IAAI,EAAEnG,UAAU,IAAIE,UAAU,KAAK,OAAQ;MAACkG,OAAO,EAAEpE,iBAAkB;MAAAiB,QAAA,gBAC7EpE,OAAA,CAACxB,WAAW;QAAA4F,QAAA,eACVpE,OAAA,CAAChC,GAAG;UAACqG,EAAE,EAAE;YAAEE,OAAO,EAAE,MAAM;YAAEC,UAAU,EAAE;UAAS,CAAE;UAAAJ,QAAA,gBACjDpE,OAAA,CAACN,WAAW;YAAC2F,KAAK,EAAC,SAAS;YAAChB,EAAE,EAAE;cAAEM,EAAE,EAAE;YAAE;UAAE;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,yBAEhD;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAK;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACK,CAAC,eACd/E,OAAA,CAACvB,aAAa;QAAA2F,QAAA,gBACZpE,OAAA,CAACtB,iBAAiB;UAAA0F,QAAA,GAAC,UACT,eAAApE,OAAA;YAAAoE,QAAA,EAASnD,YAAY,aAAZA,YAAY,uBAAZA,YAAY,CAAEqC;UAAO;YAAAsB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAS,CAAC,8CAChD,EAAC,CAAA9D,YAAY,aAAZA,YAAY,uBAAZA,YAAY,CAAEiC,eAAe,IAAG,CAAC,iBAChClD,OAAA,CAAAE,SAAA;YAAAkE,QAAA,GAAE,iBAAe,eAAApE,OAAA;cAAAoE,QAAA,GAASnD,YAAY,CAACiC,eAAe,EAAC,IAAE;YAAA;cAAA0B,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC,KAAC;UAAA,eAAE,CACtE;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACgB,CAAC,eACpB/E,OAAA,CAACtB,iBAAiB;UAAC2F,EAAE,EAAE;YAAEkC,EAAE,EAAE;UAAE,CAAE;UAAAnC,QAAA,EAAC;QAElC;UAAAQ,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAmB,CAAC,eACpB/E,OAAA,CAAChB,gBAAgB;UACfwI,OAAO,eACLxH,OAAA,CAACyH,QAAQ;YACPC,OAAO,EAAE/F,UAAW;YACpBkE,QAAQ,EAAG8B,CAAC,IAAK/F,aAAa,CAAC+F,CAAC,CAACzD,MAAM,CAACwD,OAAO;UAAE;YAAA9C,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAClD,CACF;UACDY,KAAK,EAAC;QAA4B;UAAAf,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACnC,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACW,CAAC,eAChB/E,OAAA,CAACrB,aAAa;QAAAyF,QAAA,gBACZpE,OAAA,CAAC7B,MAAM;UAACuG,OAAO,EAAEvB,iBAAkB;UAAAiB,QAAA,EAAC;QAAO;UAAAQ,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC,eACpD/E,OAAA,CAAC7B,MAAM;UACLuG,OAAO,EAAEtB,gBAAiB;UAC1BiC,KAAK,EAAC,SAAS;UACfL,OAAO,EAAC,WAAW;UACnBmB,QAAQ,EAAEpF,OAAQ;UAAAqD,QAAA,EAEjBrD,OAAO,gBAAGf,OAAA,CAACpB,gBAAgB;YAACwH,IAAI,EAAE;UAAG;YAAAxB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,GAAG;QAAkB;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACxD,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACI,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACV,CAAC,eAGT/E,OAAA,CAACzB,MAAM;MAAC+I,IAAI,EAAEnG,UAAU,IAAIE,UAAU,KAAK,SAAU;MAACkG,OAAO,EAAEpE,iBAAkB;MAAAiB,QAAA,gBAC/EpE,OAAA,CAACxB,WAAW;QAAA4F,QAAA,eACVpE,OAAA,CAAChC,GAAG;UAACqG,EAAE,EAAE;YAAEE,OAAO,EAAE,MAAM;YAAEC,UAAU,EAAE;UAAS,CAAE;UAAAJ,QAAA,gBACjDpE,OAAA,CAACR,UAAU;YAAC6F,KAAK,EAAC,OAAO;YAAChB,EAAE,EAAE;cAAEM,EAAE,EAAE;YAAE;UAAE;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,gBAE7C;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAK;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACK,CAAC,eACd/E,OAAA,CAACvB,aAAa;QAAA2F,QAAA,gBACZpE,OAAA,CAACtB,iBAAiB;UAAA0F,QAAA,GAAC,6BACU,eAAApE,OAAA;YAAAoE,QAAA,EAASnD,YAAY,aAAZA,YAAY,uBAAZA,YAAY,CAAEqC;UAAO;YAAAsB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAS,CAAC,KACrE;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAmB,CAAC,eAEpB/E,OAAA,CAACf,WAAW;UAAC+H,SAAS,EAAC,UAAU;UAAC3C,EAAE,EAAE;YAAEkC,EAAE,EAAE;UAAE,CAAE;UAAAnC,QAAA,gBAC9CpE,OAAA,CAACd,SAAS;YAAC8H,SAAS,EAAC,QAAQ;YAAA5C,QAAA,EAAC;UAAgC;YAAAQ,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAW,CAAC,eAC1E/E,OAAA,CAACjB,UAAU;YACToF,KAAK,EAAE1C,UAAW;YAClBoE,QAAQ,EAAG8B,CAAC,IAAKjG,aAAa,CAACiG,CAAC,CAACzD,MAAM,CAACC,KAAK,CAAE;YAAAC,QAAA,gBAE/CpE,OAAA,CAAChB,gBAAgB;cACfmF,KAAK,EAAC,OAAO;cACbqD,OAAO,eAAExH,OAAA,CAAClB,KAAK;gBAAA8F,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAE;cACnBY,KAAK,EAAC;YAAqF;cAAAf,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC5F,CAAC,eACF/E,OAAA,CAAChB,gBAAgB;cACfmF,KAAK,EAAC,QAAQ;cACdqD,OAAO,eAAExH,OAAA,CAAClB,KAAK;gBAAA8F,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAE;cACnBY,KAAK,EAAC;YAAsE;cAAAf,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC7E,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACQ,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACF,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACD,CAAC,eAChB/E,OAAA,CAACrB,aAAa;QAAAyF,QAAA,gBACZpE,OAAA,CAAC7B,MAAM;UAACuG,OAAO,EAAEvB,iBAAkB;UAAAiB,QAAA,EAAC;QAAO;UAAAQ,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC,eACpD/E,OAAA,CAAC7B,MAAM;UACLuG,OAAO,EAAEtB,gBAAiB;UAC1BiC,KAAK,EAAC,OAAO;UACbL,OAAO,EAAC,WAAW;UACnBmB,QAAQ,EAAEpF,OAAQ;UAAAqD,QAAA,EAEjBrD,OAAO,gBAAGf,OAAA,CAACpB,gBAAgB;YAACwH,IAAI,EAAE;UAAG;YAAAxB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,GAAItD,UAAU,KAAK,OAAO,GAAG,kBAAkB,GAAG;QAA0B;UAAAmD,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC/G,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACI,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACV,CAAC,eAET/E,OAAA,CAAC1B,QAAQ;MACPgJ,IAAI,EAAE3G,YAAa;MACnBiH,gBAAgB,EAAE,IAAK;MACvBL,OAAO,EAAEhF,mBAAoB;MAC7BsF,YAAY,EAAE;QAAEC,QAAQ,EAAE,QAAQ;QAAEC,UAAU,EAAE;MAAS,CAAE;MAAA3D,QAAA,eAE3DpE,OAAA,CAAC3B,KAAK;QAACkJ,OAAO,EAAEhF,mBAAoB;QAAC+D,QAAQ,EAAE7F,aAAc;QAAC4D,EAAE,EAAE;UAAE2D,KAAK,EAAE;QAAO,CAAE;QAAA5D,QAAA,EACjF7D;MAAY;QAAAqE,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACR;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACA,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACR,CAAC;AAEV,CAAC;AAAC3E,EAAA,CAnYID,eAAe;EAAA,QACFR,WAAW,EACAC,OAAO;AAAA;AAAAqI,EAAA,GAF/B9H,eAAe;AAqYrB,eAAeA,eAAe;AAAC,IAAA8H,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}