{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\PycharmProjects\\\\CMS\\\\webapp\\\\frontend\\\\src\\\\pages\\\\cavi\\\\VisualizzaCaviPage.js\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect } from 'react';\nimport { Box, Typography, Paper, Button, Grid, Card, CardContent, Alert, IconButton, Table, TableBody, TableCell, TableContainer, TableHead, TableRow, Tabs, Tab } from '@mui/material';\nimport { ArrowBack as ArrowBackIcon, Refresh as RefreshIcon, Home as HomeIcon, ViewList as ViewListIcon, ViewModule as ViewModuleIcon } from '@mui/icons-material';\nimport { useNavigate } from 'react-router-dom';\nimport { useAuth } from '../../context/AuthContext';\nimport caviService from '../../services/caviService';\nimport '../CaviPage.css';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst VisualizzaCaviPage = () => {\n  _s();\n  const {\n    isImpersonating,\n    user\n  } = useAuth();\n  const navigate = useNavigate();\n  const [cantiereId, setCantiereId] = useState(null);\n  const [cantiereName, setCantiereName] = useState('');\n  const [caviAttivi, setCaviAttivi] = useState([]);\n  const [caviSpare, setCaviSpare] = useState([]);\n  const [loading, setLoading] = useState(true);\n  const [error, setError] = useState(null);\n  const [viewMode, setViewMode] = useState('table'); // 'table' o 'card'\n\n  // Carica i dati del cantiere e dei cavi\n  useEffect(() => {\n    const fetchData = async () => {\n      try {\n        console.log('Inizializzazione VisualizzaCaviPage...');\n\n        // Verifica che l'utente sia autenticato\n        const token = localStorage.getItem('token');\n        console.log('Token presente:', !!token);\n        if (!token) {\n          setError('Sessione scaduta. Effettua nuovamente il login.');\n          setLoading(false);\n          return;\n        }\n\n        // Recupera l'ID del cantiere selezionato dal localStorage\n        let selectedCantiereId = localStorage.getItem('selectedCantiereId');\n        let selectedCantiereName = localStorage.getItem('selectedCantiereName');\n        console.log('Cantiere selezionato dal localStorage:', {\n          selectedCantiereId,\n          selectedCantiereName\n        });\n        console.log('Dati utente:', user);\n\n        // Stampa tutti i dati nel localStorage per debug\n        console.log('DEBUG - Tutti i dati nel localStorage:');\n        for (let i = 0; i < localStorage.length; i++) {\n          const key = localStorage.key(i);\n          console.log(`${key}: ${localStorage.getItem(key)}`);\n        }\n\n        // SOLUZIONE DIRETTA: Ottieni l'ID del cantiere direttamente dal token JWT\n        if ((user === null || user === void 0 ? void 0 : user.role) === 'cantieri_user') {\n          console.log('Utente cantiere rilevato, tentativo di recupero ID cantiere dai dati utente');\n\n          // Verifica se l'utente ha un ID cantiere nei dati utente\n          if (user.cantiere_id) {\n            console.log('Trovato ID cantiere nei dati utente:', user.cantiere_id);\n            selectedCantiereId = user.cantiere_id.toString();\n            selectedCantiereName = user.cantiere_name || `Cantiere ${user.cantiere_id}`;\n\n            // Salva l'ID e il nome del cantiere nel localStorage\n            localStorage.setItem('selectedCantiereId', selectedCantiereId);\n            localStorage.setItem('selectedCantiereName', selectedCantiereName);\n            console.log('Salvato ID cantiere nel localStorage:', selectedCantiereId);\n          } else {\n            // Tentativo di recupero dal token JWT\n            try {\n              console.log('Tentativo di decodifica del token JWT per recuperare l\\'ID cantiere');\n              const token = localStorage.getItem('token');\n              if (token) {\n                // Decodifica il token JWT (senza verifica della firma)\n                const base64Url = token.split('.')[1];\n                const base64 = base64Url.replace(/-/g, '+').replace(/_/g, '/');\n                const jsonPayload = decodeURIComponent(atob(base64).split('').map(c => {\n                  return '%' + ('00' + c.charCodeAt(0).toString(16)).slice(-2);\n                }).join(''));\n                const payload = JSON.parse(jsonPayload);\n                console.log('Payload del token JWT:', payload);\n                if (payload.cantiere_id) {\n                  console.log('Trovato ID cantiere nel token JWT:', payload.cantiere_id);\n                  selectedCantiereId = payload.cantiere_id.toString();\n                  // Usa un nome generico se non disponibile\n                  selectedCantiereName = `Cantiere ${payload.cantiere_id}`;\n\n                  // Salva l'ID e il nome del cantiere nel localStorage\n                  localStorage.setItem('selectedCantiereId', selectedCantiereId);\n                  localStorage.setItem('selectedCantiereName', selectedCantiereName);\n                  console.log('Salvato ID cantiere nel localStorage:', selectedCantiereId);\n                }\n              }\n            } catch (e) {\n              console.error('Errore durante la decodifica del token JWT:', e);\n            }\n          }\n        }\n\n        // SOLUZIONE TEMPORANEA: Se ancora non abbiamo un ID cantiere, usa un valore hardcoded per debug\n        if (!selectedCantiereId || selectedCantiereId === 'undefined' || selectedCantiereId === 'null') {\n          console.warn('ATTENZIONE: Nessun ID cantiere trovato, utilizzo valore hardcoded per debug');\n          // Usa il primo cantiere disponibile (questo è solo per debug)\n          selectedCantiereId = '1'; // Sostituisci con un ID cantiere valido nel tuo database\n          selectedCantiereName = 'Cantiere Debug';\n\n          // Salva l'ID e il nome del cantiere nel localStorage\n          localStorage.setItem('selectedCantiereId', selectedCantiereId);\n          localStorage.setItem('selectedCantiereName', selectedCantiereName);\n          console.log('Salvato ID cantiere hardcoded nel localStorage:', selectedCantiereId);\n        }\n\n        // Verifica finale\n        if (!selectedCantiereId) {\n          setError('Nessun cantiere selezionato. Torna alla pagina dei cantieri.');\n          setLoading(false);\n          return;\n        }\n\n        // Verifica che l'ID del cantiere sia un numero valido\n        const cantiereIdNum = parseInt(selectedCantiereId, 10);\n        console.log('ID cantiere convertito a numero:', cantiereIdNum);\n        if (isNaN(cantiereIdNum)) {\n          setError(`ID cantiere non valido: ${selectedCantiereId}. Torna alla pagina dei cantieri.`);\n          setLoading(false);\n          return;\n        }\n\n        // Usa il numero convertito, non la stringa\n        setCantiereId(cantiereIdNum);\n        setCantiereName(selectedCantiereName || `Cantiere ${cantiereIdNum}`);\n\n        // Carica i cavi attivi con gestione degli errori migliorata\n        console.log('Caricamento cavi attivi per cantiere:', cantiereIdNum);\n        try {\n          // Imposta un timeout per evitare che la richiesta rimanga bloccata\n          const timeoutPromise = new Promise((_, reject) => {\n            setTimeout(() => reject(new Error('Timeout durante il caricamento dei cavi attivi')), 30000); // Aumentato a 30 secondi\n          });\n\n          // Esegui la richiesta con un timeout di sicurezza\n          console.log('Iniziando chiamata API per cavi attivi...');\n          const caviPromise = caviService.getCavi(cantiereIdNum, 0);\n          const attivi = await Promise.race([caviPromise, timeoutPromise]);\n          console.log('Cavi attivi caricati:', attivi);\n          console.log('Numero di cavi attivi trovati:', attivi ? attivi.length : 0);\n          if (attivi && attivi.length > 0) {\n            console.log('Primo cavo attivo:', attivi[0]);\n          } else {\n            console.warn('Nessun cavo attivo trovato per il cantiere', cantiereIdNum);\n          }\n          setCaviAttivi(attivi || []);\n        } catch (caviError) {\n          console.error('Errore nel caricamento dei cavi attivi:', caviError);\n          console.error('Dettagli errore cavi attivi:', {\n            message: caviError.message,\n            status: caviError.status,\n            data: caviError.data,\n            stack: caviError.stack,\n            code: caviError.code,\n            name: caviError.name,\n            response: caviError.response ? {\n              status: caviError.response.status,\n              statusText: caviError.response.statusText,\n              data: caviError.response.data\n            } : 'No response'\n          });\n\n          // Non interrompere il flusso, continua con i cavi spare\n          setCaviAttivi([]);\n          console.warn('Continuazione del flusso dopo errore nei cavi attivi');\n\n          // Aggiungi un messaggio di errore visibile all'utente\n          setError(`Errore nel caricamento dei cavi attivi: ${caviError.message}. Controlla la console per maggiori dettagli.`);\n        }\n\n        // Carica i cavi spare con gestione degli errori migliorata\n        console.log('Caricamento cavi spare per cantiere:', cantiereIdNum);\n        try {\n          // Imposta un timeout per evitare che la richiesta rimanga bloccata\n          const timeoutPromise = new Promise((_, reject) => {\n            setTimeout(() => reject(new Error('Timeout durante il caricamento dei cavi spare')), 30000); // Aumentato a 30 secondi\n          });\n\n          // Esegui la richiesta con un timeout di sicurezza\n          console.log('Iniziando chiamata API per cavi spare...');\n          const sparePromise = caviService.getCavi(cantiereIdNum, 3);\n          const spare = await Promise.race([sparePromise, timeoutPromise]);\n          console.log('Cavi spare caricati:', spare);\n          console.log('Numero di cavi spare trovati:', spare ? spare.length : 0);\n          if (spare && spare.length > 0) {\n            console.log('Primo cavo spare:', spare[0]);\n          } else {\n            console.warn('Nessun cavo spare trovato per il cantiere', cantiereIdNum);\n          }\n          setCaviSpare(spare || []);\n        } catch (spareError) {\n          console.error('Errore nel caricamento dei cavi spare:', spareError);\n          console.error('Dettagli errore cavi spare:', {\n            message: spareError.message,\n            status: spareError.status,\n            data: spareError.data,\n            stack: spareError.stack,\n            code: spareError.code,\n            name: spareError.name,\n            response: spareError.response ? {\n              status: spareError.response.status,\n              statusText: spareError.response.statusText,\n              data: spareError.response.data\n            } : 'No response'\n          });\n\n          // Non interrompere il flusso, imposta un array vuoto\n          setCaviSpare([]);\n\n          // Aggiungi un messaggio di errore visibile all'utente se non c'è già un errore per i cavi attivi\n          if (!error) {\n            setError(`Errore nel caricamento dei cavi spare: ${spareError.message}. Controlla la console per maggiori dettagli.`);\n          }\n        }\n\n        // Se siamo arrivati qui, almeno abbiamo caricato l'interfaccia di base\n        setLoading(false);\n      } catch (err) {\n        var _err$response, _err$response2, _err$response3, _err$response4, _err$response5, _err$response5$data;\n        console.error('Errore nel caricamento dei cavi:', err);\n        console.error('Dettagli errore generale:', {\n          message: err.message,\n          status: err.status || ((_err$response = err.response) === null || _err$response === void 0 ? void 0 : _err$response.status),\n          data: err.data || ((_err$response2 = err.response) === null || _err$response2 === void 0 ? void 0 : _err$response2.data),\n          stack: err.stack\n        });\n\n        // Estrai il messaggio di errore dettagliato\n        let errorMessage = 'Errore sconosciuto';\n        if (err.message && err.message.includes('ID cantiere non valido')) {\n          errorMessage = err.message;\n        } else if (err.status === 401 || err.status === 403 || ((_err$response3 = err.response) === null || _err$response3 === void 0 ? void 0 : _err$response3.status) === 401 || ((_err$response4 = err.response) === null || _err$response4 === void 0 ? void 0 : _err$response4.status) === 403) {\n          errorMessage = 'Sessione scaduta o non autorizzata. Effettua nuovamente il login.';\n        } else if ((_err$response5 = err.response) !== null && _err$response5 !== void 0 && (_err$response5$data = _err$response5.data) !== null && _err$response5$data !== void 0 && _err$response5$data.detail) {\n          // Estrai il messaggio di errore dettagliato dall'API\n          errorMessage = `Errore API: ${err.response.data.detail}`;\n        } else if (err.code === 'ERR_NETWORK') {\n          // Errore di rete\n          errorMessage = 'Network Error. Verifica che il backend sia in esecuzione e accessibile.';\n        } else if (err.message) {\n          errorMessage = err.message;\n        }\n        setError(`Impossibile caricare i cavi: ${errorMessage}. Riprova più tardi.`);\n\n        // Imposta array vuoti per evitare errori di rendering\n        setCaviAttivi([]);\n        setCaviSpare([]);\n      } finally {\n        setLoading(false);\n      }\n    };\n    fetchData();\n  }, []);\n\n  // Nessuna funzione di navigazione necessaria, tutto è gestito dal menu principale\n\n  // Funzione per visualizzare i cavi in formato tabellare\n  const renderCaviTable = cavi => {\n    if (!cavi || cavi.length === 0) {\n      return /*#__PURE__*/_jsxDEV(Alert, {\n        severity: \"info\",\n        children: [\"Nessun cavo trovato in questa categoria.\", /*#__PURE__*/_jsxDEV(Button, {\n          variant: \"text\",\n          color: \"primary\",\n          onClick: () => window.location.reload(),\n          sx: {\n            ml: 2\n          },\n          children: \"Riprova\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 296,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 294,\n        columnNumber: 9\n      }, this);\n    }\n    if (viewMode === 'table') {\n      return /*#__PURE__*/_jsxDEV(TableContainer, {\n        component: Paper,\n        sx: {\n          mt: 2,\n          overflowX: 'auto'\n        },\n        children: /*#__PURE__*/_jsxDEV(Table, {\n          size: \"small\",\n          children: [/*#__PURE__*/_jsxDEV(TableHead, {\n            children: /*#__PURE__*/_jsxDEV(TableRow, {\n              children: [/*#__PURE__*/_jsxDEV(TableCell, {\n                children: \"ID Cavo\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 314,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n                children: \"Utility\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 315,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n                children: \"Tipologia\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 316,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n                children: \"N.Cond\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 317,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n                children: \"Sezione\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 318,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n                children: \"Ubicaz.Part.\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 319,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n                children: \"Ubicaz.Arr.\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 320,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n                children: \"Metri T.\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 321,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n                children: \"Stato\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 322,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 313,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 312,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(TableBody, {\n            children: cavi.map(cavo => {\n              // Formatta i valori per la visualizzazione come nella CLI\n              const id_cavo = String(cavo.id_cavo).replace('$', '');\n              const utility = cavo.utility || '-';\n              const tipologia = cavo.tipologia || '-';\n\n              // Gestisci n_conduttori come stringa o numero\n              let n_conduttori = '-';\n              try {\n                const n_cond_val = parseInt(cavo.n_conduttori, 10) || 0;\n                n_conduttori = n_cond_val > 0 ? String(n_cond_val) : '-';\n              } catch (e) {\n                n_conduttori = cavo.n_conduttori || '-';\n              }\n\n              // Gestisci sezione come stringa\n              let sezione = '-';\n              const sezione_val = cavo.sezione;\n              if (typeof sezione_val === 'number' && sezione_val === 0) {\n                sezione = '-';\n              } else {\n                sezione = sezione_val ? String(sezione_val) : '-';\n              }\n              const ubicazione_partenza = cavo.ubicazione_partenza || '-';\n              const ubicazione_arrivo = cavo.ubicazione_arrivo || '-';\n              const metri_teorici = cavo.metri_teorici ? `${parseFloat(cavo.metri_teorici).toFixed(2)}` : '-';\n              const stato = cavo.stato_installazione || '-';\n              return /*#__PURE__*/_jsxDEV(TableRow, {\n                children: [/*#__PURE__*/_jsxDEV(TableCell, {\n                  children: id_cavo\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 357,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n                  children: utility\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 358,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n                  children: tipologia\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 359,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n                  children: n_conduttori\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 360,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n                  children: sezione\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 361,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n                  children: ubicazione_partenza\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 362,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n                  children: ubicazione_arrivo\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 363,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n                  children: metri_teorici\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 364,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n                  children: stato\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 365,\n                  columnNumber: 21\n                }, this)]\n              }, cavo.id_cavo, true, {\n                fileName: _jsxFileName,\n                lineNumber: 356,\n                columnNumber: 19\n              }, this);\n            })\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 325,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 311,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 310,\n        columnNumber: 9\n      }, this);\n    } else {\n      // Visualizzazione a schede (card)\n      return /*#__PURE__*/_jsxDEV(Grid, {\n        container: true,\n        spacing: 2,\n        children: cavi.map(cavo => /*#__PURE__*/_jsxDEV(Grid, {\n          item: true,\n          xs: 12,\n          sm: 6,\n          md: 4,\n          children: /*#__PURE__*/_jsxDEV(Card, {\n            children: /*#__PURE__*/_jsxDEV(CardContent, {\n              children: [/*#__PURE__*/_jsxDEV(Typography, {\n                variant: \"h6\",\n                component: \"div\",\n                children: cavo.id_cavo\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 381,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                variant: \"body2\",\n                color: \"text.secondary\",\n                children: [\"Sistema: \", cavo.sistema || 'N/A']\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 384,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                variant: \"body2\",\n                color: \"text.secondary\",\n                children: [\"Tipologia: \", cavo.tipologia || 'N/A']\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 387,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                variant: \"body2\",\n                color: \"text.secondary\",\n                children: [\"Partenza: \", cavo.ubicazione_partenza || 'N/A', \" - \", cavo.utenza_partenza || 'N/A']\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 390,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                variant: \"body2\",\n                color: \"text.secondary\",\n                children: [\"Arrivo: \", cavo.ubicazione_arrivo || 'N/A', \" - \", cavo.utenza_arrivo || 'N/A']\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 393,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                variant: \"body2\",\n                color: \"text.secondary\",\n                children: [\"Metri teorici: \", cavo.metri_teorici || 'N/A']\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 396,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                variant: \"body2\",\n                color: \"text.secondary\",\n                children: [\"Metratura reale: \", cavo.metratura_reale || '0']\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 399,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                variant: \"body2\",\n                color: \"text.secondary\",\n                children: [\"Stato: \", cavo.stato_installazione || 'N/A']\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 402,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 380,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 379,\n            columnNumber: 15\n          }, this)\n        }, cavo.id_cavo, false, {\n          fileName: _jsxFileName,\n          lineNumber: 378,\n          columnNumber: 13\n        }, this))\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 376,\n        columnNumber: 9\n      }, this);\n    }\n  };\n\n  // Gestisce il cambio di modalità di visualizzazione\n  const handleViewModeChange = mode => {\n    setViewMode(mode);\n  };\n  return /*#__PURE__*/_jsxDEV(Box, {\n    className: \"cavi-page\",\n    children: [/*#__PURE__*/_jsxDEV(Paper, {\n      sx: {\n        mb: 3,\n        p: 2\n      },\n      children: /*#__PURE__*/_jsxDEV(Box, {\n        sx: {\n          display: 'flex',\n          justifyContent: 'flex-end',\n          alignItems: 'center',\n          gap: 2\n        },\n        children: [/*#__PURE__*/_jsxDEV(IconButton, {\n          onClick: () => window.location.reload(),\n          title: \"Ricarica la pagina\",\n          children: /*#__PURE__*/_jsxDEV(RefreshIcon, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 427,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 423,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Box, {\n          sx: {\n            display: 'flex',\n            border: '1px solid #ddd',\n            borderRadius: 1\n          },\n          children: [/*#__PURE__*/_jsxDEV(IconButton, {\n            color: viewMode === 'table' ? 'primary' : 'default',\n            onClick: () => handleViewModeChange('table'),\n            title: \"Vista tabellare\",\n            children: /*#__PURE__*/_jsxDEV(ViewListIcon, {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 435,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 430,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(IconButton, {\n            color: viewMode === 'card' ? 'primary' : 'default',\n            onClick: () => handleViewModeChange('card'),\n            title: \"Vista a schede\",\n            children: /*#__PURE__*/_jsxDEV(ViewModuleIcon, {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 442,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 437,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 429,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 422,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 421,\n      columnNumber: 7\n    }, this), loading ? /*#__PURE__*/_jsxDEV(Box, {\n      sx: {\n        display: 'flex',\n        flexDirection: 'column',\n        alignItems: 'center',\n        mt: 4\n      },\n      children: [/*#__PURE__*/_jsxDEV(Typography, {\n        children: \"Caricamento cavi...\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 450,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(Button, {\n        variant: \"outlined\",\n        color: \"primary\",\n        onClick: () => window.location.reload(),\n        sx: {\n          mt: 2\n        },\n        children: \"Ricarica la pagina\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 451,\n        columnNumber: 11\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 449,\n      columnNumber: 9\n    }, this) : error ? /*#__PURE__*/_jsxDEV(Box, {\n      children: [/*#__PURE__*/_jsxDEV(Alert, {\n        severity: \"error\",\n        sx: {\n          mb: 2\n        },\n        children: [error, error.includes('Network Error') && /*#__PURE__*/_jsxDEV(Typography, {\n          variant: \"body2\",\n          sx: {\n            mt: 1\n          },\n          children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n            children: \"Suggerimento:\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 466,\n            columnNumber: 17\n          }, this), \" Verifica che il server backend sia in esecuzione sulla porta 8001.\", /*#__PURE__*/_jsxDEV(\"br\", {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 467,\n            columnNumber: 17\n          }, this), \"Puoi avviare il backend eseguendo il file \", /*#__PURE__*/_jsxDEV(\"code\", {\n            children: \"run_system.py\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 468,\n            columnNumber: 59\n          }, this), \" nella cartella principale del progetto.\"]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 465,\n          columnNumber: 15\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 462,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(Box, {\n        sx: {\n          display: 'flex',\n          gap: 2\n        },\n        children: /*#__PURE__*/_jsxDEV(Button, {\n          variant: \"contained\",\n          className: \"primary-button\",\n          onClick: () => window.location.reload(),\n          children: \"Ricarica la pagina\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 473,\n          columnNumber: 13\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 472,\n        columnNumber: 11\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 461,\n      columnNumber: 9\n    }, this) : /*#__PURE__*/_jsxDEV(Box, {\n      children: [/*#__PURE__*/_jsxDEV(Box, {\n        sx: {\n          mb: 3\n        },\n        children: [/*#__PURE__*/_jsxDEV(Typography, {\n          variant: \"h5\",\n          gutterBottom: true,\n          children: \"Cavi Attivi\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 485,\n          columnNumber: 13\n        }, this), renderCaviTable(caviAttivi)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 484,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(Box, {\n        sx: {\n          mt: 4\n        },\n        children: [/*#__PURE__*/_jsxDEV(Typography, {\n          variant: \"h5\",\n          gutterBottom: true,\n          children: \"Cavi Spare\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 492,\n          columnNumber: 13\n        }, this), renderCaviTable(caviSpare)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 491,\n        columnNumber: 11\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 483,\n      columnNumber: 9\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 420,\n    columnNumber: 5\n  }, this);\n};\n_s(VisualizzaCaviPage, \"NrnyDwPvanid54PVrArxUObtaVY=\", false, function () {\n  return [useAuth, useNavigate];\n});\n_c = VisualizzaCaviPage;\nexport default VisualizzaCaviPage;\nvar _c;\n$RefreshReg$(_c, \"VisualizzaCaviPage\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "Box", "Typography", "Paper", "<PERSON><PERSON>", "Grid", "Card", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "IconButton", "Table", "TableBody", "TableCell", "TableContainer", "TableHead", "TableRow", "Tabs", "Tab", "ArrowBack", "ArrowBackIcon", "Refresh", "RefreshIcon", "Home", "HomeIcon", "ViewList", "ViewListIcon", "ViewModule", "ViewModuleIcon", "useNavigate", "useAuth", "caviService", "jsxDEV", "_jsxDEV", "VisualizzaCaviPage", "_s", "isImpersonating", "user", "navigate", "cantiereId", "setCantiereId", "cantiereName", "setCantiereName", "caviAttivi", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "caviSpare", "setCaviSpare", "loading", "setLoading", "error", "setError", "viewMode", "setViewMode", "fetchData", "console", "log", "token", "localStorage", "getItem", "selectedCantiereId", "selectedCantiereName", "i", "length", "key", "role", "cantiere_id", "toString", "cantiere_name", "setItem", "base64Url", "split", "base64", "replace", "jsonPayload", "decodeURIComponent", "atob", "map", "c", "charCodeAt", "slice", "join", "payload", "JSON", "parse", "e", "warn", "cantiereIdNum", "parseInt", "isNaN", "timeoutPromise", "Promise", "_", "reject", "setTimeout", "Error", "caviPromise", "get<PERSON><PERSON>", "attivi", "race", "caviError", "message", "status", "data", "stack", "code", "name", "response", "statusText", "sparePromise", "spare", "spareError", "err", "_err$response", "_err$response2", "_err$response3", "_err$response4", "_err$response5", "_err$response5$data", "errorMessage", "includes", "detail", "renderCaviTable", "cavi", "severity", "children", "variant", "color", "onClick", "window", "location", "reload", "sx", "ml", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "component", "mt", "overflowX", "size", "cavo", "id_cavo", "String", "utility", "tipologia", "n_conduttori", "n_cond_val", "sezione", "sezione_val", "ubicazione_partenza", "ubicazione_arrivo", "metri_te<PERSON>ci", "parseFloat", "toFixed", "stato", "stato_installazione", "container", "spacing", "item", "xs", "sm", "md", "sistema", "utenza_partenza", "utenza_arrivo", "metratura_reale", "handleViewModeChange", "mode", "className", "mb", "p", "display", "justifyContent", "alignItems", "gap", "title", "border", "borderRadius", "flexDirection", "gutterBottom", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/PycharmProjects/CMS/webapp/frontend/src/pages/cavi/VisualizzaCaviPage.js"], "sourcesContent": ["import React, { useState, useEffect } from 'react';\nimport {\n  Box,\n  Typography,\n  Paper,\n  Button,\n  Grid,\n  Card,\n  CardContent,\n  Alert,\n  IconButton,\n  Table,\n  TableBody,\n  TableCell,\n  TableContainer,\n  TableHead,\n  TableRow,\n  Tabs,\n  Tab\n} from '@mui/material';\nimport {\n  ArrowBack as ArrowBackIcon,\n  Refresh as RefreshIcon,\n  Home as HomeIcon,\n  ViewList as ViewListIcon,\n  ViewModule as ViewModuleIcon\n} from '@mui/icons-material';\nimport { useNavigate } from 'react-router-dom';\nimport { useAuth } from '../../context/AuthContext';\nimport caviService from '../../services/caviService';\nimport '../CaviPage.css';\n\nconst VisualizzaCaviPage = () => {\n  const { isImpersonating, user } = useAuth();\n  const navigate = useNavigate();\n  const [cantiereId, setCantiereId] = useState(null);\n  const [cantiereName, setCantiereName] = useState('');\n  const [caviAttivi, setCaviAttivi] = useState([]);\n  const [caviSpare, setCaviSpare] = useState([]);\n  const [loading, setLoading] = useState(true);\n  const [error, setError] = useState(null);\n  const [viewMode, setViewMode] = useState('table'); // 'table' o 'card'\n\n  // Carica i dati del cantiere e dei cavi\n  useEffect(() => {\n    const fetchData = async () => {\n      try {\n        console.log('Inizializzazione VisualizzaCaviPage...');\n\n        // Verifica che l'utente sia autenticato\n        const token = localStorage.getItem('token');\n        console.log('Token presente:', !!token);\n        if (!token) {\n          setError('Sessione scaduta. Effettua nuovamente il login.');\n          setLoading(false);\n          return;\n        }\n\n        // Recupera l'ID del cantiere selezionato dal localStorage\n        let selectedCantiereId = localStorage.getItem('selectedCantiereId');\n        let selectedCantiereName = localStorage.getItem('selectedCantiereName');\n\n        console.log('Cantiere selezionato dal localStorage:', { selectedCantiereId, selectedCantiereName });\n        console.log('Dati utente:', user);\n\n        // Stampa tutti i dati nel localStorage per debug\n        console.log('DEBUG - Tutti i dati nel localStorage:');\n        for (let i = 0; i < localStorage.length; i++) {\n          const key = localStorage.key(i);\n          console.log(`${key}: ${localStorage.getItem(key)}`);\n        }\n\n        // SOLUZIONE DIRETTA: Ottieni l'ID del cantiere direttamente dal token JWT\n        if (user?.role === 'cantieri_user') {\n          console.log('Utente cantiere rilevato, tentativo di recupero ID cantiere dai dati utente');\n\n          // Verifica se l'utente ha un ID cantiere nei dati utente\n          if (user.cantiere_id) {\n            console.log('Trovato ID cantiere nei dati utente:', user.cantiere_id);\n            selectedCantiereId = user.cantiere_id.toString();\n            selectedCantiereName = user.cantiere_name || `Cantiere ${user.cantiere_id}`;\n\n            // Salva l'ID e il nome del cantiere nel localStorage\n            localStorage.setItem('selectedCantiereId', selectedCantiereId);\n            localStorage.setItem('selectedCantiereName', selectedCantiereName);\n            console.log('Salvato ID cantiere nel localStorage:', selectedCantiereId);\n          } else {\n            // Tentativo di recupero dal token JWT\n            try {\n              console.log('Tentativo di decodifica del token JWT per recuperare l\\'ID cantiere');\n              const token = localStorage.getItem('token');\n              if (token) {\n                // Decodifica il token JWT (senza verifica della firma)\n                const base64Url = token.split('.')[1];\n                const base64 = base64Url.replace(/-/g, '+').replace(/_/g, '/');\n                const jsonPayload = decodeURIComponent(atob(base64).split('').map(c => {\n                  return '%' + ('00' + c.charCodeAt(0).toString(16)).slice(-2);\n                }).join(''));\n\n                const payload = JSON.parse(jsonPayload);\n                console.log('Payload del token JWT:', payload);\n\n                if (payload.cantiere_id) {\n                  console.log('Trovato ID cantiere nel token JWT:', payload.cantiere_id);\n                  selectedCantiereId = payload.cantiere_id.toString();\n                  // Usa un nome generico se non disponibile\n                  selectedCantiereName = `Cantiere ${payload.cantiere_id}`;\n\n                  // Salva l'ID e il nome del cantiere nel localStorage\n                  localStorage.setItem('selectedCantiereId', selectedCantiereId);\n                  localStorage.setItem('selectedCantiereName', selectedCantiereName);\n                  console.log('Salvato ID cantiere nel localStorage:', selectedCantiereId);\n                }\n              }\n            } catch (e) {\n              console.error('Errore durante la decodifica del token JWT:', e);\n            }\n          }\n        }\n\n        // SOLUZIONE TEMPORANEA: Se ancora non abbiamo un ID cantiere, usa un valore hardcoded per debug\n        if (!selectedCantiereId || selectedCantiereId === 'undefined' || selectedCantiereId === 'null') {\n          console.warn('ATTENZIONE: Nessun ID cantiere trovato, utilizzo valore hardcoded per debug');\n          // Usa il primo cantiere disponibile (questo è solo per debug)\n          selectedCantiereId = '1'; // Sostituisci con un ID cantiere valido nel tuo database\n          selectedCantiereName = 'Cantiere Debug';\n\n          // Salva l'ID e il nome del cantiere nel localStorage\n          localStorage.setItem('selectedCantiereId', selectedCantiereId);\n          localStorage.setItem('selectedCantiereName', selectedCantiereName);\n          console.log('Salvato ID cantiere hardcoded nel localStorage:', selectedCantiereId);\n        }\n\n        // Verifica finale\n        if (!selectedCantiereId) {\n          setError('Nessun cantiere selezionato. Torna alla pagina dei cantieri.');\n          setLoading(false);\n          return;\n        }\n\n        // Verifica che l'ID del cantiere sia un numero valido\n        const cantiereIdNum = parseInt(selectedCantiereId, 10);\n        console.log('ID cantiere convertito a numero:', cantiereIdNum);\n        if (isNaN(cantiereIdNum)) {\n          setError(`ID cantiere non valido: ${selectedCantiereId}. Torna alla pagina dei cantieri.`);\n          setLoading(false);\n          return;\n        }\n\n        // Usa il numero convertito, non la stringa\n        setCantiereId(cantiereIdNum);\n        setCantiereName(selectedCantiereName || `Cantiere ${cantiereIdNum}`);\n\n        // Carica i cavi attivi con gestione degli errori migliorata\n        console.log('Caricamento cavi attivi per cantiere:', cantiereIdNum);\n        try {\n          // Imposta un timeout per evitare che la richiesta rimanga bloccata\n          const timeoutPromise = new Promise((_, reject) => {\n            setTimeout(() => reject(new Error('Timeout durante il caricamento dei cavi attivi')), 30000); // Aumentato a 30 secondi\n          });\n\n          // Esegui la richiesta con un timeout di sicurezza\n          console.log('Iniziando chiamata API per cavi attivi...');\n          const caviPromise = caviService.getCavi(cantiereIdNum, 0);\n          const attivi = await Promise.race([caviPromise, timeoutPromise]);\n\n          console.log('Cavi attivi caricati:', attivi);\n          console.log('Numero di cavi attivi trovati:', attivi ? attivi.length : 0);\n          if (attivi && attivi.length > 0) {\n            console.log('Primo cavo attivo:', attivi[0]);\n          } else {\n            console.warn('Nessun cavo attivo trovato per il cantiere', cantiereIdNum);\n          }\n          setCaviAttivi(attivi || []);\n        } catch (caviError) {\n          console.error('Errore nel caricamento dei cavi attivi:', caviError);\n          console.error('Dettagli errore cavi attivi:', {\n            message: caviError.message,\n            status: caviError.status,\n            data: caviError.data,\n            stack: caviError.stack,\n            code: caviError.code,\n            name: caviError.name,\n            response: caviError.response ? {\n              status: caviError.response.status,\n              statusText: caviError.response.statusText,\n              data: caviError.response.data\n            } : 'No response'\n          });\n\n          // Non interrompere il flusso, continua con i cavi spare\n          setCaviAttivi([]);\n          console.warn('Continuazione del flusso dopo errore nei cavi attivi');\n\n          // Aggiungi un messaggio di errore visibile all'utente\n          setError(`Errore nel caricamento dei cavi attivi: ${caviError.message}. Controlla la console per maggiori dettagli.`);\n        }\n\n        // Carica i cavi spare con gestione degli errori migliorata\n        console.log('Caricamento cavi spare per cantiere:', cantiereIdNum);\n        try {\n          // Imposta un timeout per evitare che la richiesta rimanga bloccata\n          const timeoutPromise = new Promise((_, reject) => {\n            setTimeout(() => reject(new Error('Timeout durante il caricamento dei cavi spare')), 30000); // Aumentato a 30 secondi\n          });\n\n          // Esegui la richiesta con un timeout di sicurezza\n          console.log('Iniziando chiamata API per cavi spare...');\n          const sparePromise = caviService.getCavi(cantiereIdNum, 3);\n          const spare = await Promise.race([sparePromise, timeoutPromise]);\n\n          console.log('Cavi spare caricati:', spare);\n          console.log('Numero di cavi spare trovati:', spare ? spare.length : 0);\n          if (spare && spare.length > 0) {\n            console.log('Primo cavo spare:', spare[0]);\n          } else {\n            console.warn('Nessun cavo spare trovato per il cantiere', cantiereIdNum);\n          }\n          setCaviSpare(spare || []);\n        } catch (spareError) {\n          console.error('Errore nel caricamento dei cavi spare:', spareError);\n          console.error('Dettagli errore cavi spare:', {\n            message: spareError.message,\n            status: spareError.status,\n            data: spareError.data,\n            stack: spareError.stack,\n            code: spareError.code,\n            name: spareError.name,\n            response: spareError.response ? {\n              status: spareError.response.status,\n              statusText: spareError.response.statusText,\n              data: spareError.response.data\n            } : 'No response'\n          });\n\n          // Non interrompere il flusso, imposta un array vuoto\n          setCaviSpare([]);\n\n          // Aggiungi un messaggio di errore visibile all'utente se non c'è già un errore per i cavi attivi\n          if (!error) {\n            setError(`Errore nel caricamento dei cavi spare: ${spareError.message}. Controlla la console per maggiori dettagli.`);\n          }\n        }\n\n        // Se siamo arrivati qui, almeno abbiamo caricato l'interfaccia di base\n        setLoading(false);\n\n      } catch (err) {\n        console.error('Errore nel caricamento dei cavi:', err);\n        console.error('Dettagli errore generale:', {\n          message: err.message,\n          status: err.status || err.response?.status,\n          data: err.data || err.response?.data,\n          stack: err.stack\n        });\n\n        // Estrai il messaggio di errore dettagliato\n        let errorMessage = 'Errore sconosciuto';\n\n        if (err.message && err.message.includes('ID cantiere non valido')) {\n          errorMessage = err.message;\n        } else if (err.status === 401 || err.status === 403 ||\n                  err.response?.status === 401 || err.response?.status === 403) {\n          errorMessage = 'Sessione scaduta o non autorizzata. Effettua nuovamente il login.';\n        } else if (err.response?.data?.detail) {\n          // Estrai il messaggio di errore dettagliato dall'API\n          errorMessage = `Errore API: ${err.response.data.detail}`;\n        } else if (err.code === 'ERR_NETWORK') {\n          // Errore di rete\n          errorMessage = 'Network Error. Verifica che il backend sia in esecuzione e accessibile.';\n        } else if (err.message) {\n          errorMessage = err.message;\n        }\n\n        setError(`Impossibile caricare i cavi: ${errorMessage}. Riprova più tardi.`);\n\n        // Imposta array vuoti per evitare errori di rendering\n        setCaviAttivi([]);\n        setCaviSpare([]);\n      } finally {\n        setLoading(false);\n      }\n    };\n\n    fetchData();\n  }, []);\n\n  // Nessuna funzione di navigazione necessaria, tutto è gestito dal menu principale\n\n  // Funzione per visualizzare i cavi in formato tabellare\n  const renderCaviTable = (cavi) => {\n    if (!cavi || cavi.length === 0) {\n      return (\n        <Alert severity=\"info\">\n          Nessun cavo trovato in questa categoria.\n          <Button\n            variant=\"text\"\n            color=\"primary\"\n            onClick={() => window.location.reload()}\n            sx={{ ml: 2 }}\n          >\n            Riprova\n          </Button>\n        </Alert>\n      );\n    }\n\n    if (viewMode === 'table') {\n      return (\n        <TableContainer component={Paper} sx={{ mt: 2, overflowX: 'auto' }}>\n          <Table size=\"small\">\n            <TableHead>\n              <TableRow>\n                <TableCell>ID Cavo</TableCell>\n                <TableCell>Utility</TableCell>\n                <TableCell>Tipologia</TableCell>\n                <TableCell>N.Cond</TableCell>\n                <TableCell>Sezione</TableCell>\n                <TableCell>Ubicaz.Part.</TableCell>\n                <TableCell>Ubicaz.Arr.</TableCell>\n                <TableCell>Metri T.</TableCell>\n                <TableCell>Stato</TableCell>\n              </TableRow>\n            </TableHead>\n            <TableBody>\n              {cavi.map((cavo) => {\n                // Formatta i valori per la visualizzazione come nella CLI\n                const id_cavo = String(cavo.id_cavo).replace('$', '');\n                const utility = cavo.utility || '-';\n                const tipologia = cavo.tipologia || '-';\n\n                // Gestisci n_conduttori come stringa o numero\n                let n_conduttori = '-';\n                try {\n                  const n_cond_val = parseInt(cavo.n_conduttori, 10) || 0;\n                  n_conduttori = n_cond_val > 0 ? String(n_cond_val) : '-';\n                } catch (e) {\n                  n_conduttori = cavo.n_conduttori || '-';\n                }\n\n                // Gestisci sezione come stringa\n                let sezione = '-';\n                const sezione_val = cavo.sezione;\n                if (typeof sezione_val === 'number' && sezione_val === 0) {\n                  sezione = '-';\n                } else {\n                  sezione = sezione_val ? String(sezione_val) : '-';\n                }\n\n                const ubicazione_partenza = cavo.ubicazione_partenza || '-';\n                const ubicazione_arrivo = cavo.ubicazione_arrivo || '-';\n                const metri_teorici = cavo.metri_teorici ? `${parseFloat(cavo.metri_teorici).toFixed(2)}` : '-';\n                const stato = cavo.stato_installazione || '-';\n\n                return (\n                  <TableRow key={cavo.id_cavo}>\n                    <TableCell>{id_cavo}</TableCell>\n                    <TableCell>{utility}</TableCell>\n                    <TableCell>{tipologia}</TableCell>\n                    <TableCell>{n_conduttori}</TableCell>\n                    <TableCell>{sezione}</TableCell>\n                    <TableCell>{ubicazione_partenza}</TableCell>\n                    <TableCell>{ubicazione_arrivo}</TableCell>\n                    <TableCell>{metri_teorici}</TableCell>\n                    <TableCell>{stato}</TableCell>\n                  </TableRow>\n                );\n              })}\n            </TableBody>\n          </Table>\n        </TableContainer>\n      );\n    } else {\n      // Visualizzazione a schede (card)\n      return (\n        <Grid container spacing={2}>\n          {cavi.map((cavo) => (\n            <Grid item xs={12} sm={6} md={4} key={cavo.id_cavo}>\n              <Card>\n                <CardContent>\n                  <Typography variant=\"h6\" component=\"div\">\n                    {cavo.id_cavo}\n                  </Typography>\n                  <Typography variant=\"body2\" color=\"text.secondary\">\n                    Sistema: {cavo.sistema || 'N/A'}\n                  </Typography>\n                  <Typography variant=\"body2\" color=\"text.secondary\">\n                    Tipologia: {cavo.tipologia || 'N/A'}\n                  </Typography>\n                  <Typography variant=\"body2\" color=\"text.secondary\">\n                    Partenza: {cavo.ubicazione_partenza || 'N/A'} - {cavo.utenza_partenza || 'N/A'}\n                  </Typography>\n                  <Typography variant=\"body2\" color=\"text.secondary\">\n                    Arrivo: {cavo.ubicazione_arrivo || 'N/A'} - {cavo.utenza_arrivo || 'N/A'}\n                  </Typography>\n                  <Typography variant=\"body2\" color=\"text.secondary\">\n                    Metri teorici: {cavo.metri_teorici || 'N/A'}\n                  </Typography>\n                  <Typography variant=\"body2\" color=\"text.secondary\">\n                    Metratura reale: {cavo.metratura_reale || '0'}\n                  </Typography>\n                  <Typography variant=\"body2\" color=\"text.secondary\">\n                    Stato: {cavo.stato_installazione || 'N/A'}\n                  </Typography>\n                </CardContent>\n              </Card>\n            </Grid>\n          ))}\n        </Grid>\n      );\n    }\n  };\n\n  // Gestisce il cambio di modalità di visualizzazione\n  const handleViewModeChange = (mode) => {\n    setViewMode(mode);\n  };\n\n  return (\n    <Box className=\"cavi-page\">\n      <Paper sx={{ mb: 3, p: 2 }}>\n        <Box sx={{ display: 'flex', justifyContent: 'flex-end', alignItems: 'center', gap: 2 }}>\n          <IconButton\n            onClick={() => window.location.reload()}\n            title=\"Ricarica la pagina\"\n          >\n            <RefreshIcon />\n          </IconButton>\n          <Box sx={{ display: 'flex', border: '1px solid #ddd', borderRadius: 1 }}>\n            <IconButton\n              color={viewMode === 'table' ? 'primary' : 'default'}\n              onClick={() => handleViewModeChange('table')}\n              title=\"Vista tabellare\"\n            >\n              <ViewListIcon />\n            </IconButton>\n            <IconButton\n              color={viewMode === 'card' ? 'primary' : 'default'}\n              onClick={() => handleViewModeChange('card')}\n              title=\"Vista a schede\"\n            >\n              <ViewModuleIcon />\n            </IconButton>\n          </Box>\n        </Box>\n      </Paper>\n\n      {loading ? (\n        <Box sx={{ display: 'flex', flexDirection: 'column', alignItems: 'center', mt: 4 }}>\n          <Typography>Caricamento cavi...</Typography>\n          <Button\n            variant=\"outlined\"\n            color=\"primary\"\n            onClick={() => window.location.reload()}\n            sx={{ mt: 2 }}\n          >\n            Ricarica la pagina\n          </Button>\n        </Box>\n      ) : error ? (\n        <Box>\n          <Alert severity=\"error\" sx={{ mb: 2 }}>\n            {error}\n            {error.includes('Network Error') && (\n              <Typography variant=\"body2\" sx={{ mt: 1 }}>\n                <strong>Suggerimento:</strong> Verifica che il server backend sia in esecuzione sulla porta 8001.\n                <br />\n                Puoi avviare il backend eseguendo il file <code>run_system.py</code> nella cartella principale del progetto.\n              </Typography>\n            )}\n          </Alert>\n          <Box sx={{ display: 'flex', gap: 2 }}>\n            <Button\n              variant=\"contained\"\n              className=\"primary-button\"\n              onClick={() => window.location.reload()}\n            >\n              Ricarica la pagina\n            </Button>\n          </Box>\n        </Box>\n      ) : (\n        <Box>\n          <Box sx={{ mb: 3 }}>\n            <Typography variant=\"h5\" gutterBottom>\n              Cavi Attivi\n            </Typography>\n            {renderCaviTable(caviAttivi)}\n          </Box>\n\n          <Box sx={{ mt: 4 }}>\n            <Typography variant=\"h5\" gutterBottom>\n              Cavi Spare\n            </Typography>\n            {renderCaviTable(caviSpare)}\n          </Box>\n        </Box>\n      )}\n    </Box>\n  );\n};\n\nexport default VisualizzaCaviPage;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAClD,SACEC,GAAG,EACHC,UAAU,EACVC,KAAK,EACLC,MAAM,EACNC,IAAI,EACJC,IAAI,EACJC,WAAW,EACXC,KAAK,EACLC,UAAU,EACVC,KAAK,EACLC,SAAS,EACTC,SAAS,EACTC,cAAc,EACdC,SAAS,EACTC,QAAQ,EACRC,IAAI,EACJC,GAAG,QACE,eAAe;AACtB,SACEC,SAAS,IAAIC,aAAa,EAC1BC,OAAO,IAAIC,WAAW,EACtBC,IAAI,IAAIC,QAAQ,EAChBC,QAAQ,IAAIC,YAAY,EACxBC,UAAU,IAAIC,cAAc,QACvB,qBAAqB;AAC5B,SAASC,WAAW,QAAQ,kBAAkB;AAC9C,SAASC,OAAO,QAAQ,2BAA2B;AACnD,OAAOC,WAAW,MAAM,4BAA4B;AACpD,OAAO,iBAAiB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAEzB,MAAMC,kBAAkB,GAAGA,CAAA,KAAM;EAAAC,EAAA;EAC/B,MAAM;IAAEC,eAAe;IAAEC;EAAK,CAAC,GAAGP,OAAO,CAAC,CAAC;EAC3C,MAAMQ,QAAQ,GAAGT,WAAW,CAAC,CAAC;EAC9B,MAAM,CAACU,UAAU,EAAEC,aAAa,CAAC,GAAGxC,QAAQ,CAAC,IAAI,CAAC;EAClD,MAAM,CAACyC,YAAY,EAAEC,eAAe,CAAC,GAAG1C,QAAQ,CAAC,EAAE,CAAC;EACpD,MAAM,CAAC2C,UAAU,EAAEC,aAAa,CAAC,GAAG5C,QAAQ,CAAC,EAAE,CAAC;EAChD,MAAM,CAAC6C,SAAS,EAAEC,YAAY,CAAC,GAAG9C,QAAQ,CAAC,EAAE,CAAC;EAC9C,MAAM,CAAC+C,OAAO,EAAEC,UAAU,CAAC,GAAGhD,QAAQ,CAAC,IAAI,CAAC;EAC5C,MAAM,CAACiD,KAAK,EAAEC,QAAQ,CAAC,GAAGlD,QAAQ,CAAC,IAAI,CAAC;EACxC,MAAM,CAACmD,QAAQ,EAAEC,WAAW,CAAC,GAAGpD,QAAQ,CAAC,OAAO,CAAC,CAAC,CAAC;;EAEnD;EACAC,SAAS,CAAC,MAAM;IACd,MAAMoD,SAAS,GAAG,MAAAA,CAAA,KAAY;MAC5B,IAAI;QACFC,OAAO,CAACC,GAAG,CAAC,wCAAwC,CAAC;;QAErD;QACA,MAAMC,KAAK,GAAGC,YAAY,CAACC,OAAO,CAAC,OAAO,CAAC;QAC3CJ,OAAO,CAACC,GAAG,CAAC,iBAAiB,EAAE,CAAC,CAACC,KAAK,CAAC;QACvC,IAAI,CAACA,KAAK,EAAE;UACVN,QAAQ,CAAC,iDAAiD,CAAC;UAC3DF,UAAU,CAAC,KAAK,CAAC;UACjB;QACF;;QAEA;QACA,IAAIW,kBAAkB,GAAGF,YAAY,CAACC,OAAO,CAAC,oBAAoB,CAAC;QACnE,IAAIE,oBAAoB,GAAGH,YAAY,CAACC,OAAO,CAAC,sBAAsB,CAAC;QAEvEJ,OAAO,CAACC,GAAG,CAAC,wCAAwC,EAAE;UAAEI,kBAAkB;UAAEC;QAAqB,CAAC,CAAC;QACnGN,OAAO,CAACC,GAAG,CAAC,cAAc,EAAElB,IAAI,CAAC;;QAEjC;QACAiB,OAAO,CAACC,GAAG,CAAC,wCAAwC,CAAC;QACrD,KAAK,IAAIM,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGJ,YAAY,CAACK,MAAM,EAAED,CAAC,EAAE,EAAE;UAC5C,MAAME,GAAG,GAAGN,YAAY,CAACM,GAAG,CAACF,CAAC,CAAC;UAC/BP,OAAO,CAACC,GAAG,CAAC,GAAGQ,GAAG,KAAKN,YAAY,CAACC,OAAO,CAACK,GAAG,CAAC,EAAE,CAAC;QACrD;;QAEA;QACA,IAAI,CAAA1B,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAE2B,IAAI,MAAK,eAAe,EAAE;UAClCV,OAAO,CAACC,GAAG,CAAC,6EAA6E,CAAC;;UAE1F;UACA,IAAIlB,IAAI,CAAC4B,WAAW,EAAE;YACpBX,OAAO,CAACC,GAAG,CAAC,sCAAsC,EAAElB,IAAI,CAAC4B,WAAW,CAAC;YACrEN,kBAAkB,GAAGtB,IAAI,CAAC4B,WAAW,CAACC,QAAQ,CAAC,CAAC;YAChDN,oBAAoB,GAAGvB,IAAI,CAAC8B,aAAa,IAAI,YAAY9B,IAAI,CAAC4B,WAAW,EAAE;;YAE3E;YACAR,YAAY,CAACW,OAAO,CAAC,oBAAoB,EAAET,kBAAkB,CAAC;YAC9DF,YAAY,CAACW,OAAO,CAAC,sBAAsB,EAAER,oBAAoB,CAAC;YAClEN,OAAO,CAACC,GAAG,CAAC,uCAAuC,EAAEI,kBAAkB,CAAC;UAC1E,CAAC,MAAM;YACL;YACA,IAAI;cACFL,OAAO,CAACC,GAAG,CAAC,qEAAqE,CAAC;cAClF,MAAMC,KAAK,GAAGC,YAAY,CAACC,OAAO,CAAC,OAAO,CAAC;cAC3C,IAAIF,KAAK,EAAE;gBACT;gBACA,MAAMa,SAAS,GAAGb,KAAK,CAACc,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC;gBACrC,MAAMC,MAAM,GAAGF,SAAS,CAACG,OAAO,CAAC,IAAI,EAAE,GAAG,CAAC,CAACA,OAAO,CAAC,IAAI,EAAE,GAAG,CAAC;gBAC9D,MAAMC,WAAW,GAAGC,kBAAkB,CAACC,IAAI,CAACJ,MAAM,CAAC,CAACD,KAAK,CAAC,EAAE,CAAC,CAACM,GAAG,CAACC,CAAC,IAAI;kBACrE,OAAO,GAAG,GAAG,CAAC,IAAI,GAAGA,CAAC,CAACC,UAAU,CAAC,CAAC,CAAC,CAACZ,QAAQ,CAAC,EAAE,CAAC,EAAEa,KAAK,CAAC,CAAC,CAAC,CAAC;gBAC9D,CAAC,CAAC,CAACC,IAAI,CAAC,EAAE,CAAC,CAAC;gBAEZ,MAAMC,OAAO,GAAGC,IAAI,CAACC,KAAK,CAACV,WAAW,CAAC;gBACvCnB,OAAO,CAACC,GAAG,CAAC,wBAAwB,EAAE0B,OAAO,CAAC;gBAE9C,IAAIA,OAAO,CAAChB,WAAW,EAAE;kBACvBX,OAAO,CAACC,GAAG,CAAC,oCAAoC,EAAE0B,OAAO,CAAChB,WAAW,CAAC;kBACtEN,kBAAkB,GAAGsB,OAAO,CAAChB,WAAW,CAACC,QAAQ,CAAC,CAAC;kBACnD;kBACAN,oBAAoB,GAAG,YAAYqB,OAAO,CAAChB,WAAW,EAAE;;kBAExD;kBACAR,YAAY,CAACW,OAAO,CAAC,oBAAoB,EAAET,kBAAkB,CAAC;kBAC9DF,YAAY,CAACW,OAAO,CAAC,sBAAsB,EAAER,oBAAoB,CAAC;kBAClEN,OAAO,CAACC,GAAG,CAAC,uCAAuC,EAAEI,kBAAkB,CAAC;gBAC1E;cACF;YACF,CAAC,CAAC,OAAOyB,CAAC,EAAE;cACV9B,OAAO,CAACL,KAAK,CAAC,6CAA6C,EAAEmC,CAAC,CAAC;YACjE;UACF;QACF;;QAEA;QACA,IAAI,CAACzB,kBAAkB,IAAIA,kBAAkB,KAAK,WAAW,IAAIA,kBAAkB,KAAK,MAAM,EAAE;UAC9FL,OAAO,CAAC+B,IAAI,CAAC,6EAA6E,CAAC;UAC3F;UACA1B,kBAAkB,GAAG,GAAG,CAAC,CAAC;UAC1BC,oBAAoB,GAAG,gBAAgB;;UAEvC;UACAH,YAAY,CAACW,OAAO,CAAC,oBAAoB,EAAET,kBAAkB,CAAC;UAC9DF,YAAY,CAACW,OAAO,CAAC,sBAAsB,EAAER,oBAAoB,CAAC;UAClEN,OAAO,CAACC,GAAG,CAAC,iDAAiD,EAAEI,kBAAkB,CAAC;QACpF;;QAEA;QACA,IAAI,CAACA,kBAAkB,EAAE;UACvBT,QAAQ,CAAC,8DAA8D,CAAC;UACxEF,UAAU,CAAC,KAAK,CAAC;UACjB;QACF;;QAEA;QACA,MAAMsC,aAAa,GAAGC,QAAQ,CAAC5B,kBAAkB,EAAE,EAAE,CAAC;QACtDL,OAAO,CAACC,GAAG,CAAC,kCAAkC,EAAE+B,aAAa,CAAC;QAC9D,IAAIE,KAAK,CAACF,aAAa,CAAC,EAAE;UACxBpC,QAAQ,CAAC,2BAA2BS,kBAAkB,mCAAmC,CAAC;UAC1FX,UAAU,CAAC,KAAK,CAAC;UACjB;QACF;;QAEA;QACAR,aAAa,CAAC8C,aAAa,CAAC;QAC5B5C,eAAe,CAACkB,oBAAoB,IAAI,YAAY0B,aAAa,EAAE,CAAC;;QAEpE;QACAhC,OAAO,CAACC,GAAG,CAAC,uCAAuC,EAAE+B,aAAa,CAAC;QACnE,IAAI;UACF;UACA,MAAMG,cAAc,GAAG,IAAIC,OAAO,CAAC,CAACC,CAAC,EAAEC,MAAM,KAAK;YAChDC,UAAU,CAAC,MAAMD,MAAM,CAAC,IAAIE,KAAK,CAAC,gDAAgD,CAAC,CAAC,EAAE,KAAK,CAAC,CAAC,CAAC;UAChG,CAAC,CAAC;;UAEF;UACAxC,OAAO,CAACC,GAAG,CAAC,2CAA2C,CAAC;UACxD,MAAMwC,WAAW,GAAGhE,WAAW,CAACiE,OAAO,CAACV,aAAa,EAAE,CAAC,CAAC;UACzD,MAAMW,MAAM,GAAG,MAAMP,OAAO,CAACQ,IAAI,CAAC,CAACH,WAAW,EAAEN,cAAc,CAAC,CAAC;UAEhEnC,OAAO,CAACC,GAAG,CAAC,uBAAuB,EAAE0C,MAAM,CAAC;UAC5C3C,OAAO,CAACC,GAAG,CAAC,gCAAgC,EAAE0C,MAAM,GAAGA,MAAM,CAACnC,MAAM,GAAG,CAAC,CAAC;UACzE,IAAImC,MAAM,IAAIA,MAAM,CAACnC,MAAM,GAAG,CAAC,EAAE;YAC/BR,OAAO,CAACC,GAAG,CAAC,oBAAoB,EAAE0C,MAAM,CAAC,CAAC,CAAC,CAAC;UAC9C,CAAC,MAAM;YACL3C,OAAO,CAAC+B,IAAI,CAAC,4CAA4C,EAAEC,aAAa,CAAC;UAC3E;UACA1C,aAAa,CAACqD,MAAM,IAAI,EAAE,CAAC;QAC7B,CAAC,CAAC,OAAOE,SAAS,EAAE;UAClB7C,OAAO,CAACL,KAAK,CAAC,yCAAyC,EAAEkD,SAAS,CAAC;UACnE7C,OAAO,CAACL,KAAK,CAAC,8BAA8B,EAAE;YAC5CmD,OAAO,EAAED,SAAS,CAACC,OAAO;YAC1BC,MAAM,EAAEF,SAAS,CAACE,MAAM;YACxBC,IAAI,EAAEH,SAAS,CAACG,IAAI;YACpBC,KAAK,EAAEJ,SAAS,CAACI,KAAK;YACtBC,IAAI,EAAEL,SAAS,CAACK,IAAI;YACpBC,IAAI,EAAEN,SAAS,CAACM,IAAI;YACpBC,QAAQ,EAAEP,SAAS,CAACO,QAAQ,GAAG;cAC7BL,MAAM,EAAEF,SAAS,CAACO,QAAQ,CAACL,MAAM;cACjCM,UAAU,EAAER,SAAS,CAACO,QAAQ,CAACC,UAAU;cACzCL,IAAI,EAAEH,SAAS,CAACO,QAAQ,CAACJ;YAC3B,CAAC,GAAG;UACN,CAAC,CAAC;;UAEF;UACA1D,aAAa,CAAC,EAAE,CAAC;UACjBU,OAAO,CAAC+B,IAAI,CAAC,sDAAsD,CAAC;;UAEpE;UACAnC,QAAQ,CAAC,2CAA2CiD,SAAS,CAACC,OAAO,+CAA+C,CAAC;QACvH;;QAEA;QACA9C,OAAO,CAACC,GAAG,CAAC,sCAAsC,EAAE+B,aAAa,CAAC;QAClE,IAAI;UACF;UACA,MAAMG,cAAc,GAAG,IAAIC,OAAO,CAAC,CAACC,CAAC,EAAEC,MAAM,KAAK;YAChDC,UAAU,CAAC,MAAMD,MAAM,CAAC,IAAIE,KAAK,CAAC,+CAA+C,CAAC,CAAC,EAAE,KAAK,CAAC,CAAC,CAAC;UAC/F,CAAC,CAAC;;UAEF;UACAxC,OAAO,CAACC,GAAG,CAAC,0CAA0C,CAAC;UACvD,MAAMqD,YAAY,GAAG7E,WAAW,CAACiE,OAAO,CAACV,aAAa,EAAE,CAAC,CAAC;UAC1D,MAAMuB,KAAK,GAAG,MAAMnB,OAAO,CAACQ,IAAI,CAAC,CAACU,YAAY,EAAEnB,cAAc,CAAC,CAAC;UAEhEnC,OAAO,CAACC,GAAG,CAAC,sBAAsB,EAAEsD,KAAK,CAAC;UAC1CvD,OAAO,CAACC,GAAG,CAAC,+BAA+B,EAAEsD,KAAK,GAAGA,KAAK,CAAC/C,MAAM,GAAG,CAAC,CAAC;UACtE,IAAI+C,KAAK,IAAIA,KAAK,CAAC/C,MAAM,GAAG,CAAC,EAAE;YAC7BR,OAAO,CAACC,GAAG,CAAC,mBAAmB,EAAEsD,KAAK,CAAC,CAAC,CAAC,CAAC;UAC5C,CAAC,MAAM;YACLvD,OAAO,CAAC+B,IAAI,CAAC,2CAA2C,EAAEC,aAAa,CAAC;UAC1E;UACAxC,YAAY,CAAC+D,KAAK,IAAI,EAAE,CAAC;QAC3B,CAAC,CAAC,OAAOC,UAAU,EAAE;UACnBxD,OAAO,CAACL,KAAK,CAAC,wCAAwC,EAAE6D,UAAU,CAAC;UACnExD,OAAO,CAACL,KAAK,CAAC,6BAA6B,EAAE;YAC3CmD,OAAO,EAAEU,UAAU,CAACV,OAAO;YAC3BC,MAAM,EAAES,UAAU,CAACT,MAAM;YACzBC,IAAI,EAAEQ,UAAU,CAACR,IAAI;YACrBC,KAAK,EAAEO,UAAU,CAACP,KAAK;YACvBC,IAAI,EAAEM,UAAU,CAACN,IAAI;YACrBC,IAAI,EAAEK,UAAU,CAACL,IAAI;YACrBC,QAAQ,EAAEI,UAAU,CAACJ,QAAQ,GAAG;cAC9BL,MAAM,EAAES,UAAU,CAACJ,QAAQ,CAACL,MAAM;cAClCM,UAAU,EAAEG,UAAU,CAACJ,QAAQ,CAACC,UAAU;cAC1CL,IAAI,EAAEQ,UAAU,CAACJ,QAAQ,CAACJ;YAC5B,CAAC,GAAG;UACN,CAAC,CAAC;;UAEF;UACAxD,YAAY,CAAC,EAAE,CAAC;;UAEhB;UACA,IAAI,CAACG,KAAK,EAAE;YACVC,QAAQ,CAAC,0CAA0C4D,UAAU,CAACV,OAAO,+CAA+C,CAAC;UACvH;QACF;;QAEA;QACApD,UAAU,CAAC,KAAK,CAAC;MAEnB,CAAC,CAAC,OAAO+D,GAAG,EAAE;QAAA,IAAAC,aAAA,EAAAC,cAAA,EAAAC,cAAA,EAAAC,cAAA,EAAAC,cAAA,EAAAC,mBAAA;QACZ/D,OAAO,CAACL,KAAK,CAAC,kCAAkC,EAAE8D,GAAG,CAAC;QACtDzD,OAAO,CAACL,KAAK,CAAC,2BAA2B,EAAE;UACzCmD,OAAO,EAAEW,GAAG,CAACX,OAAO;UACpBC,MAAM,EAAEU,GAAG,CAACV,MAAM,MAAAW,aAAA,GAAID,GAAG,CAACL,QAAQ,cAAAM,aAAA,uBAAZA,aAAA,CAAcX,MAAM;UAC1CC,IAAI,EAAES,GAAG,CAACT,IAAI,MAAAW,cAAA,GAAIF,GAAG,CAACL,QAAQ,cAAAO,cAAA,uBAAZA,cAAA,CAAcX,IAAI;UACpCC,KAAK,EAAEQ,GAAG,CAACR;QACb,CAAC,CAAC;;QAEF;QACA,IAAIe,YAAY,GAAG,oBAAoB;QAEvC,IAAIP,GAAG,CAACX,OAAO,IAAIW,GAAG,CAACX,OAAO,CAACmB,QAAQ,CAAC,wBAAwB,CAAC,EAAE;UACjED,YAAY,GAAGP,GAAG,CAACX,OAAO;QAC5B,CAAC,MAAM,IAAIW,GAAG,CAACV,MAAM,KAAK,GAAG,IAAIU,GAAG,CAACV,MAAM,KAAK,GAAG,IACzC,EAAAa,cAAA,GAAAH,GAAG,CAACL,QAAQ,cAAAQ,cAAA,uBAAZA,cAAA,CAAcb,MAAM,MAAK,GAAG,IAAI,EAAAc,cAAA,GAAAJ,GAAG,CAACL,QAAQ,cAAAS,cAAA,uBAAZA,cAAA,CAAcd,MAAM,MAAK,GAAG,EAAE;UACtEiB,YAAY,GAAG,mEAAmE;QACpF,CAAC,MAAM,KAAAF,cAAA,GAAIL,GAAG,CAACL,QAAQ,cAAAU,cAAA,gBAAAC,mBAAA,GAAZD,cAAA,CAAcd,IAAI,cAAAe,mBAAA,eAAlBA,mBAAA,CAAoBG,MAAM,EAAE;UACrC;UACAF,YAAY,GAAG,eAAeP,GAAG,CAACL,QAAQ,CAACJ,IAAI,CAACkB,MAAM,EAAE;QAC1D,CAAC,MAAM,IAAIT,GAAG,CAACP,IAAI,KAAK,aAAa,EAAE;UACrC;UACAc,YAAY,GAAG,yEAAyE;QAC1F,CAAC,MAAM,IAAIP,GAAG,CAACX,OAAO,EAAE;UACtBkB,YAAY,GAAGP,GAAG,CAACX,OAAO;QAC5B;QAEAlD,QAAQ,CAAC,gCAAgCoE,YAAY,sBAAsB,CAAC;;QAE5E;QACA1E,aAAa,CAAC,EAAE,CAAC;QACjBE,YAAY,CAAC,EAAE,CAAC;MAClB,CAAC,SAAS;QACRE,UAAU,CAAC,KAAK,CAAC;MACnB;IACF,CAAC;IAEDK,SAAS,CAAC,CAAC;EACb,CAAC,EAAE,EAAE,CAAC;;EAEN;;EAEA;EACA,MAAMoE,eAAe,GAAIC,IAAI,IAAK;IAChC,IAAI,CAACA,IAAI,IAAIA,IAAI,CAAC5D,MAAM,KAAK,CAAC,EAAE;MAC9B,oBACE7B,OAAA,CAACxB,KAAK;QAACkH,QAAQ,EAAC,MAAM;QAAAC,QAAA,GAAC,0CAErB,eAAA3F,OAAA,CAAC5B,MAAM;UACLwH,OAAO,EAAC,MAAM;UACdC,KAAK,EAAC,SAAS;UACfC,OAAO,EAAEA,CAAA,KAAMC,MAAM,CAACC,QAAQ,CAACC,MAAM,CAAC,CAAE;UACxCC,EAAE,EAAE;YAAEC,EAAE,EAAE;UAAE,CAAE;UAAAR,QAAA,EACf;QAED;UAAAS,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACJ,CAAC;IAEZ;IAEA,IAAIrF,QAAQ,KAAK,OAAO,EAAE;MACxB,oBACElB,OAAA,CAACnB,cAAc;QAAC2H,SAAS,EAAErI,KAAM;QAAC+H,EAAE,EAAE;UAAEO,EAAE,EAAE,CAAC;UAAEC,SAAS,EAAE;QAAO,CAAE;QAAAf,QAAA,eACjE3F,OAAA,CAACtB,KAAK;UAACiI,IAAI,EAAC,OAAO;UAAAhB,QAAA,gBACjB3F,OAAA,CAAClB,SAAS;YAAA6G,QAAA,eACR3F,OAAA,CAACjB,QAAQ;cAAA4G,QAAA,gBACP3F,OAAA,CAACpB,SAAS;gBAAA+G,QAAA,EAAC;cAAO;gBAAAS,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAW,CAAC,eAC9BvG,OAAA,CAACpB,SAAS;gBAAA+G,QAAA,EAAC;cAAO;gBAAAS,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAW,CAAC,eAC9BvG,OAAA,CAACpB,SAAS;gBAAA+G,QAAA,EAAC;cAAS;gBAAAS,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAW,CAAC,eAChCvG,OAAA,CAACpB,SAAS;gBAAA+G,QAAA,EAAC;cAAM;gBAAAS,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAW,CAAC,eAC7BvG,OAAA,CAACpB,SAAS;gBAAA+G,QAAA,EAAC;cAAO;gBAAAS,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAW,CAAC,eAC9BvG,OAAA,CAACpB,SAAS;gBAAA+G,QAAA,EAAC;cAAY;gBAAAS,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAW,CAAC,eACnCvG,OAAA,CAACpB,SAAS;gBAAA+G,QAAA,EAAC;cAAW;gBAAAS,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAW,CAAC,eAClCvG,OAAA,CAACpB,SAAS;gBAAA+G,QAAA,EAAC;cAAQ;gBAAAS,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAW,CAAC,eAC/BvG,OAAA,CAACpB,SAAS;gBAAA+G,QAAA,EAAC;cAAK;gBAAAS,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAW,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACpB;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACF,CAAC,eACZvG,OAAA,CAACrB,SAAS;YAAAgH,QAAA,EACPF,IAAI,CAAC9C,GAAG,CAAEiE,IAAI,IAAK;cAClB;cACA,MAAMC,OAAO,GAAGC,MAAM,CAACF,IAAI,CAACC,OAAO,CAAC,CAACtE,OAAO,CAAC,GAAG,EAAE,EAAE,CAAC;cACrD,MAAMwE,OAAO,GAAGH,IAAI,CAACG,OAAO,IAAI,GAAG;cACnC,MAAMC,SAAS,GAAGJ,IAAI,CAACI,SAAS,IAAI,GAAG;;cAEvC;cACA,IAAIC,YAAY,GAAG,GAAG;cACtB,IAAI;gBACF,MAAMC,UAAU,GAAG5D,QAAQ,CAACsD,IAAI,CAACK,YAAY,EAAE,EAAE,CAAC,IAAI,CAAC;gBACvDA,YAAY,GAAGC,UAAU,GAAG,CAAC,GAAGJ,MAAM,CAACI,UAAU,CAAC,GAAG,GAAG;cAC1D,CAAC,CAAC,OAAO/D,CAAC,EAAE;gBACV8D,YAAY,GAAGL,IAAI,CAACK,YAAY,IAAI,GAAG;cACzC;;cAEA;cACA,IAAIE,OAAO,GAAG,GAAG;cACjB,MAAMC,WAAW,GAAGR,IAAI,CAACO,OAAO;cAChC,IAAI,OAAOC,WAAW,KAAK,QAAQ,IAAIA,WAAW,KAAK,CAAC,EAAE;gBACxDD,OAAO,GAAG,GAAG;cACf,CAAC,MAAM;gBACLA,OAAO,GAAGC,WAAW,GAAGN,MAAM,CAACM,WAAW,CAAC,GAAG,GAAG;cACnD;cAEA,MAAMC,mBAAmB,GAAGT,IAAI,CAACS,mBAAmB,IAAI,GAAG;cAC3D,MAAMC,iBAAiB,GAAGV,IAAI,CAACU,iBAAiB,IAAI,GAAG;cACvD,MAAMC,aAAa,GAAGX,IAAI,CAACW,aAAa,GAAG,GAAGC,UAAU,CAACZ,IAAI,CAACW,aAAa,CAAC,CAACE,OAAO,CAAC,CAAC,CAAC,EAAE,GAAG,GAAG;cAC/F,MAAMC,KAAK,GAAGd,IAAI,CAACe,mBAAmB,IAAI,GAAG;cAE7C,oBACE3H,OAAA,CAACjB,QAAQ;gBAAA4G,QAAA,gBACP3F,OAAA,CAACpB,SAAS;kBAAA+G,QAAA,EAAEkB;gBAAO;kBAAAT,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAY,CAAC,eAChCvG,OAAA,CAACpB,SAAS;kBAAA+G,QAAA,EAAEoB;gBAAO;kBAAAX,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAY,CAAC,eAChCvG,OAAA,CAACpB,SAAS;kBAAA+G,QAAA,EAAEqB;gBAAS;kBAAAZ,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAY,CAAC,eAClCvG,OAAA,CAACpB,SAAS;kBAAA+G,QAAA,EAAEsB;gBAAY;kBAAAb,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAY,CAAC,eACrCvG,OAAA,CAACpB,SAAS;kBAAA+G,QAAA,EAAEwB;gBAAO;kBAAAf,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAY,CAAC,eAChCvG,OAAA,CAACpB,SAAS;kBAAA+G,QAAA,EAAE0B;gBAAmB;kBAAAjB,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAY,CAAC,eAC5CvG,OAAA,CAACpB,SAAS;kBAAA+G,QAAA,EAAE2B;gBAAiB;kBAAAlB,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAY,CAAC,eAC1CvG,OAAA,CAACpB,SAAS;kBAAA+G,QAAA,EAAE4B;gBAAa;kBAAAnB,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAY,CAAC,eACtCvG,OAAA,CAACpB,SAAS;kBAAA+G,QAAA,EAAE+B;gBAAK;kBAAAtB,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAY,CAAC;cAAA,GATjBK,IAAI,CAACC,OAAO;gBAAAT,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAUjB,CAAC;YAEf,CAAC;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACO,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACP;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACM,CAAC;IAErB,CAAC,MAAM;MACL;MACA,oBACEvG,OAAA,CAAC3B,IAAI;QAACuJ,SAAS;QAACC,OAAO,EAAE,CAAE;QAAAlC,QAAA,EACxBF,IAAI,CAAC9C,GAAG,CAAEiE,IAAI,iBACb5G,OAAA,CAAC3B,IAAI;UAACyJ,IAAI;UAACC,EAAE,EAAE,EAAG;UAACC,EAAE,EAAE,CAAE;UAACC,EAAE,EAAE,CAAE;UAAAtC,QAAA,eAC9B3F,OAAA,CAAC1B,IAAI;YAAAqH,QAAA,eACH3F,OAAA,CAACzB,WAAW;cAAAoH,QAAA,gBACV3F,OAAA,CAAC9B,UAAU;gBAAC0H,OAAO,EAAC,IAAI;gBAACY,SAAS,EAAC,KAAK;gBAAAb,QAAA,EACrCiB,IAAI,CAACC;cAAO;gBAAAT,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CAAC,eACbvG,OAAA,CAAC9B,UAAU;gBAAC0H,OAAO,EAAC,OAAO;gBAACC,KAAK,EAAC,gBAAgB;gBAAAF,QAAA,GAAC,WACxC,EAACiB,IAAI,CAACsB,OAAO,IAAI,KAAK;cAAA;gBAAA9B,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACrB,CAAC,eACbvG,OAAA,CAAC9B,UAAU;gBAAC0H,OAAO,EAAC,OAAO;gBAACC,KAAK,EAAC,gBAAgB;gBAAAF,QAAA,GAAC,aACtC,EAACiB,IAAI,CAACI,SAAS,IAAI,KAAK;cAAA;gBAAAZ,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACzB,CAAC,eACbvG,OAAA,CAAC9B,UAAU;gBAAC0H,OAAO,EAAC,OAAO;gBAACC,KAAK,EAAC,gBAAgB;gBAAAF,QAAA,GAAC,YACvC,EAACiB,IAAI,CAACS,mBAAmB,IAAI,KAAK,EAAC,KAAG,EAACT,IAAI,CAACuB,eAAe,IAAI,KAAK;cAAA;gBAAA/B,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACpE,CAAC,eACbvG,OAAA,CAAC9B,UAAU;gBAAC0H,OAAO,EAAC,OAAO;gBAACC,KAAK,EAAC,gBAAgB;gBAAAF,QAAA,GAAC,UACzC,EAACiB,IAAI,CAACU,iBAAiB,IAAI,KAAK,EAAC,KAAG,EAACV,IAAI,CAACwB,aAAa,IAAI,KAAK;cAAA;gBAAAhC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC9D,CAAC,eACbvG,OAAA,CAAC9B,UAAU;gBAAC0H,OAAO,EAAC,OAAO;gBAACC,KAAK,EAAC,gBAAgB;gBAAAF,QAAA,GAAC,iBAClC,EAACiB,IAAI,CAACW,aAAa,IAAI,KAAK;cAAA;gBAAAnB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACjC,CAAC,eACbvG,OAAA,CAAC9B,UAAU;gBAAC0H,OAAO,EAAC,OAAO;gBAACC,KAAK,EAAC,gBAAgB;gBAAAF,QAAA,GAAC,mBAChC,EAACiB,IAAI,CAACyB,eAAe,IAAI,GAAG;cAAA;gBAAAjC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACnC,CAAC,eACbvG,OAAA,CAAC9B,UAAU;gBAAC0H,OAAO,EAAC,OAAO;gBAACC,KAAK,EAAC,gBAAgB;gBAAAF,QAAA,GAAC,SAC1C,EAACiB,IAAI,CAACe,mBAAmB,IAAI,KAAK;cAAA;gBAAAvB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC/B,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACF;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACV;QAAC,GA5B6BK,IAAI,CAACC,OAAO;UAAAT,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OA6B5C,CACP;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACE,CAAC;IAEX;EACF,CAAC;;EAED;EACA,MAAM+B,oBAAoB,GAAIC,IAAI,IAAK;IACrCpH,WAAW,CAACoH,IAAI,CAAC;EACnB,CAAC;EAED,oBACEvI,OAAA,CAAC/B,GAAG;IAACuK,SAAS,EAAC,WAAW;IAAA7C,QAAA,gBACxB3F,OAAA,CAAC7B,KAAK;MAAC+H,EAAE,EAAE;QAAEuC,EAAE,EAAE,CAAC;QAAEC,CAAC,EAAE;MAAE,CAAE;MAAA/C,QAAA,eACzB3F,OAAA,CAAC/B,GAAG;QAACiI,EAAE,EAAE;UAAEyC,OAAO,EAAE,MAAM;UAAEC,cAAc,EAAE,UAAU;UAAEC,UAAU,EAAE,QAAQ;UAAEC,GAAG,EAAE;QAAE,CAAE;QAAAnD,QAAA,gBACrF3F,OAAA,CAACvB,UAAU;UACTqH,OAAO,EAAEA,CAAA,KAAMC,MAAM,CAACC,QAAQ,CAACC,MAAM,CAAC,CAAE;UACxC8C,KAAK,EAAC,oBAAoB;UAAApD,QAAA,eAE1B3F,OAAA,CAACX,WAAW;YAAA+G,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACL,CAAC,eACbvG,OAAA,CAAC/B,GAAG;UAACiI,EAAE,EAAE;YAAEyC,OAAO,EAAE,MAAM;YAAEK,MAAM,EAAE,gBAAgB;YAAEC,YAAY,EAAE;UAAE,CAAE;UAAAtD,QAAA,gBACtE3F,OAAA,CAACvB,UAAU;YACToH,KAAK,EAAE3E,QAAQ,KAAK,OAAO,GAAG,SAAS,GAAG,SAAU;YACpD4E,OAAO,EAAEA,CAAA,KAAMwC,oBAAoB,CAAC,OAAO,CAAE;YAC7CS,KAAK,EAAC,iBAAiB;YAAApD,QAAA,eAEvB3F,OAAA,CAACP,YAAY;cAAA2G,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACN,CAAC,eACbvG,OAAA,CAACvB,UAAU;YACToH,KAAK,EAAE3E,QAAQ,KAAK,MAAM,GAAG,SAAS,GAAG,SAAU;YACnD4E,OAAO,EAAEA,CAAA,KAAMwC,oBAAoB,CAAC,MAAM,CAAE;YAC5CS,KAAK,EAAC,gBAAgB;YAAApD,QAAA,eAEtB3F,OAAA,CAACL,cAAc;cAAAyG,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACR,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACV,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACD,CAAC,EAEPzF,OAAO,gBACNd,OAAA,CAAC/B,GAAG;MAACiI,EAAE,EAAE;QAAEyC,OAAO,EAAE,MAAM;QAAEO,aAAa,EAAE,QAAQ;QAAEL,UAAU,EAAE,QAAQ;QAAEpC,EAAE,EAAE;MAAE,CAAE;MAAAd,QAAA,gBACjF3F,OAAA,CAAC9B,UAAU;QAAAyH,QAAA,EAAC;MAAmB;QAAAS,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAY,CAAC,eAC5CvG,OAAA,CAAC5B,MAAM;QACLwH,OAAO,EAAC,UAAU;QAClBC,KAAK,EAAC,SAAS;QACfC,OAAO,EAAEA,CAAA,KAAMC,MAAM,CAACC,QAAQ,CAACC,MAAM,CAAC,CAAE;QACxCC,EAAE,EAAE;UAAEO,EAAE,EAAE;QAAE,CAAE;QAAAd,QAAA,EACf;MAED;QAAAS,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAQ,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACN,CAAC,GACJvF,KAAK,gBACPhB,OAAA,CAAC/B,GAAG;MAAA0H,QAAA,gBACF3F,OAAA,CAACxB,KAAK;QAACkH,QAAQ,EAAC,OAAO;QAACQ,EAAE,EAAE;UAAEuC,EAAE,EAAE;QAAE,CAAE;QAAA9C,QAAA,GACnC3E,KAAK,EACLA,KAAK,CAACsE,QAAQ,CAAC,eAAe,CAAC,iBAC9BtF,OAAA,CAAC9B,UAAU;UAAC0H,OAAO,EAAC,OAAO;UAACM,EAAE,EAAE;YAAEO,EAAE,EAAE;UAAE,CAAE;UAAAd,QAAA,gBACxC3F,OAAA;YAAA2F,QAAA,EAAQ;UAAa;YAAAS,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC,uEAC9B,eAAAvG,OAAA;YAAAoG,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAK,CAAC,8CACoC,eAAAvG,OAAA;YAAA2F,QAAA,EAAM;UAAa;YAAAS,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC,4CACtE;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAY,CACb;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACI,CAAC,eACRvG,OAAA,CAAC/B,GAAG;QAACiI,EAAE,EAAE;UAAEyC,OAAO,EAAE,MAAM;UAAEG,GAAG,EAAE;QAAE,CAAE;QAAAnD,QAAA,eACnC3F,OAAA,CAAC5B,MAAM;UACLwH,OAAO,EAAC,WAAW;UACnB4C,SAAS,EAAC,gBAAgB;UAC1B1C,OAAO,EAAEA,CAAA,KAAMC,MAAM,CAACC,QAAQ,CAACC,MAAM,CAAC,CAAE;UAAAN,QAAA,EACzC;QAED;UAAAS,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACN,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,gBAENvG,OAAA,CAAC/B,GAAG;MAAA0H,QAAA,gBACF3F,OAAA,CAAC/B,GAAG;QAACiI,EAAE,EAAE;UAAEuC,EAAE,EAAE;QAAE,CAAE;QAAA9C,QAAA,gBACjB3F,OAAA,CAAC9B,UAAU;UAAC0H,OAAO,EAAC,IAAI;UAACuD,YAAY;UAAAxD,QAAA,EAAC;QAEtC;UAAAS,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAY,CAAC,EACZf,eAAe,CAAC9E,UAAU,CAAC;MAAA;QAAA0F,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACzB,CAAC,eAENvG,OAAA,CAAC/B,GAAG;QAACiI,EAAE,EAAE;UAAEO,EAAE,EAAE;QAAE,CAAE;QAAAd,QAAA,gBACjB3F,OAAA,CAAC9B,UAAU;UAAC0H,OAAO,EAAC,IAAI;UAACuD,YAAY;UAAAxD,QAAA,EAAC;QAEtC;UAAAS,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAY,CAAC,EACZf,eAAe,CAAC5E,SAAS,CAAC;MAAA;QAAAwF,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACxB,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CACN;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACE,CAAC;AAEV,CAAC;AAACrG,EAAA,CApdID,kBAAkB;EAAA,QACYJ,OAAO,EACxBD,WAAW;AAAA;AAAAwJ,EAAA,GAFxBnJ,kBAAkB;AAsdxB,eAAeA,kBAAkB;AAAC,IAAAmJ,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}