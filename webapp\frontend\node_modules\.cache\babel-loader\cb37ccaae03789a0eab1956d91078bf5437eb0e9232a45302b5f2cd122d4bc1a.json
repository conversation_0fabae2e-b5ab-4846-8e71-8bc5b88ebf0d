{"ast": null, "code": "import { formatDistance } from \"./de/_lib/formatDistance.js\";\nimport { formatLong } from \"./de/_lib/formatLong.js\";\nimport { formatRelative } from \"./de/_lib/formatRelative.js\";\nimport { localize } from \"./de/_lib/localize.js\";\nimport { match } from \"./de/_lib/match.js\";\n\n/**\n * @category Locales\n * @summary German locale.\n * @language German\n * @iso-639-2 deu\n * <AUTHOR> [@DeMuu](https://github.com/DeMuu)\n * <AUTHOR> [@asia-t](https://github.com/asia-t)\n * <AUTHOR> [@vanvuongngo](https://github.com/vanvuongngo)\n * <AUTHOR> [@pex](https://github.com/pex)\n * <AUTHOR> [@Philipp91](https://github.com/Philipp91)\n */\nexport const de = {\n  code: \"de\",\n  formatDistance: formatDistance,\n  formatLong: formatLong,\n  formatRelative: formatRelative,\n  localize: localize,\n  match: match,\n  options: {\n    weekStartsOn: 1 /* Monday */,\n    firstWeekContainsDate: 4\n  }\n};\n\n// Fallback for modularized imports:\nexport default de;", "map": {"version": 3, "names": ["formatDistance", "formatLong", "formatRelative", "localize", "match", "de", "code", "options", "weekStartsOn", "firstWeekContainsDate"], "sources": ["C:/Users/<USER>/PycharmProjects/CMS/webapp/frontend/node_modules/date-fns/locale/de.js"], "sourcesContent": ["import { formatDistance } from \"./de/_lib/formatDistance.js\";\nimport { formatLong } from \"./de/_lib/formatLong.js\";\nimport { formatRelative } from \"./de/_lib/formatRelative.js\";\nimport { localize } from \"./de/_lib/localize.js\";\nimport { match } from \"./de/_lib/match.js\";\n\n/**\n * @category Locales\n * @summary German locale.\n * @language German\n * @iso-639-2 deu\n * <AUTHOR> [@DeMuu](https://github.com/DeMuu)\n * <AUTHOR> [@asia-t](https://github.com/asia-t)\n * <AUTHOR> [@vanvuongngo](https://github.com/vanvuongngo)\n * <AUTHOR> [@pex](https://github.com/pex)\n * <AUTHOR> [@Philipp91](https://github.com/Philipp91)\n */\nexport const de = {\n  code: \"de\",\n  formatDistance: formatDistance,\n  formatLong: formatLong,\n  formatRelative: formatRelative,\n  localize: localize,\n  match: match,\n  options: {\n    weekStartsOn: 1 /* Monday */,\n    firstWeekContainsDate: 4,\n  },\n};\n\n// Fallback for modularized imports:\nexport default de;\n"], "mappings": "AAAA,SAASA,cAAc,QAAQ,6BAA6B;AAC5D,SAASC,UAAU,QAAQ,yBAAyB;AACpD,SAASC,cAAc,QAAQ,6BAA6B;AAC5D,SAASC,QAAQ,QAAQ,uBAAuB;AAChD,SAASC,KAAK,QAAQ,oBAAoB;;AAE1C;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO,MAAMC,EAAE,GAAG;EAChBC,IAAI,EAAE,IAAI;EACVN,cAAc,EAAEA,cAAc;EAC9BC,UAAU,EAAEA,UAAU;EACtBC,cAAc,EAAEA,cAAc;EAC9BC,QAAQ,EAAEA,QAAQ;EAClBC,KAAK,EAAEA,KAAK;EACZG,OAAO,EAAE;IACPC,YAAY,EAAE,CAAC,CAAC;IAChBC,qBAAqB,EAAE;EACzB;AACF,CAAC;;AAED;AACA,eAAeJ,EAAE", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}