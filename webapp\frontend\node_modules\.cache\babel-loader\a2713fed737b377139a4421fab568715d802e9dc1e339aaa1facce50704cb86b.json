{"ast": null, "code": "'use client';\n\nexport { default } from './SwipeableDrawer';", "map": {"version": 3, "names": ["default"], "sources": ["C:/Users/<USER>/PycharmProjects/CMS/webapp/frontend/node_modules/@mui/material/SwipeableDrawer/index.js"], "sourcesContent": ["'use client';\n\nexport { default } from './SwipeableDrawer';"], "mappings": "AAAA,YAAY;;AAEZ,SAASA,OAAO,QAAQ,mBAAmB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}