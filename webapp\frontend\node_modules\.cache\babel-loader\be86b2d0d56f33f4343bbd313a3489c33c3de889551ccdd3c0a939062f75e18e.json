{"ast": null, "code": "import { numericPatterns } from \"../constants.mjs\";\nimport { Parser } from \"../Parser.mjs\";\nimport { isLeapYearIndex, parseNDigits, parseNumericPattern } from \"../utils.mjs\";\nconst DAYS_IN_MONTH = [31, 28, 31, 30, 31, 30, 31, 31, 30, 31, 30, 31];\nconst DAYS_IN_MONTH_LEAP_YEAR = [31, 29, 31, 30, 31, 30, 31, 31, 30, 31, 30, 31];\n\n// Day of the month\nexport class DateParser extends Parser {\n  priority = 90;\n  subPriority = 1;\n  parse(dateString, token, match) {\n    switch (token) {\n      case \"d\":\n        return parseNumericPattern(numericPatterns.date, dateString);\n      case \"do\":\n        return match.ordinalNumber(dateString, {\n          unit: \"date\"\n        });\n      default:\n        return parseNDigits(token.length, dateString);\n    }\n  }\n  validate(date, value) {\n    const year = date.getFullYear();\n    const isLeapYear = isLeapYearIndex(year);\n    const month = date.getMonth();\n    if (isLeapYear) {\n      return value >= 1 && value <= DAYS_IN_MONTH_LEAP_YEAR[month];\n    } else {\n      return value >= 1 && value <= DAYS_IN_MONTH[month];\n    }\n  }\n  set(date, _flags, value) {\n    date.setDate(value);\n    date.setHours(0, 0, 0, 0);\n    return date;\n  }\n  incompatibleTokens = [\"Y\", \"R\", \"q\", \"Q\", \"w\", \"I\", \"D\", \"i\", \"e\", \"c\", \"t\", \"T\"];\n}", "map": {"version": 3, "names": ["numericPatterns", "<PERSON><PERSON><PERSON>", "isLeapYearIndex", "parseNDigits", "parseNumericPattern", "DAYS_IN_MONTH", "DAYS_IN_MONTH_LEAP_YEAR", "<PERSON><PERSON><PERSON><PERSON>", "priority", "subPriority", "parse", "dateString", "token", "match", "date", "ordinalNumber", "unit", "length", "validate", "value", "year", "getFullYear", "isLeapYear", "month", "getMonth", "set", "_flags", "setDate", "setHours", "incompatibleTokens"], "sources": ["C:/CMS/webapp/frontend/node_modules/date-fns/parse/_lib/parsers/DateParser.mjs"], "sourcesContent": ["import { numericPatterns } from \"../constants.mjs\";\nimport { Parser } from \"../Parser.mjs\";\nimport {\n  isLeapYearIndex,\n  parseNDigits,\n  parseNumericPattern,\n} from \"../utils.mjs\";\n\nconst DAYS_IN_MONTH = [31, 28, 31, 30, 31, 30, 31, 31, 30, 31, 30, 31];\nconst DAYS_IN_MONTH_LEAP_YEAR = [\n  31, 29, 31, 30, 31, 30, 31, 31, 30, 31, 30, 31,\n];\n\n// Day of the month\nexport class DateParser extends Parser {\n  priority = 90;\n  subPriority = 1;\n\n  parse(dateString, token, match) {\n    switch (token) {\n      case \"d\":\n        return parseNumericPattern(numericPatterns.date, dateString);\n      case \"do\":\n        return match.ordinalNumber(dateString, { unit: \"date\" });\n      default:\n        return parseNDigits(token.length, dateString);\n    }\n  }\n\n  validate(date, value) {\n    const year = date.getFullYear();\n    const isLeapYear = isLeapYearIndex(year);\n    const month = date.getMonth();\n    if (isLeapYear) {\n      return value >= 1 && value <= DAYS_IN_MONTH_LEAP_YEAR[month];\n    } else {\n      return value >= 1 && value <= DAYS_IN_MONTH[month];\n    }\n  }\n\n  set(date, _flags, value) {\n    date.setDate(value);\n    date.setHours(0, 0, 0, 0);\n    return date;\n  }\n\n  incompatibleTokens = [\n    \"Y\",\n    \"R\",\n    \"q\",\n    \"Q\",\n    \"w\",\n    \"I\",\n    \"D\",\n    \"i\",\n    \"e\",\n    \"c\",\n    \"t\",\n    \"T\",\n  ];\n}\n"], "mappings": "AAAA,SAASA,eAAe,QAAQ,kBAAkB;AAClD,SAASC,MAAM,QAAQ,eAAe;AACtC,SACEC,eAAe,EACfC,YAAY,EACZC,mBAAmB,QACd,cAAc;AAErB,MAAMC,aAAa,GAAG,CAAC,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,CAAC;AACtE,MAAMC,uBAAuB,GAAG,CAC9B,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,CAC/C;;AAED;AACA,OAAO,MAAMC,UAAU,SAASN,MAAM,CAAC;EACrCO,QAAQ,GAAG,EAAE;EACbC,WAAW,GAAG,CAAC;EAEfC,KAAKA,CAACC,UAAU,EAAEC,KAAK,EAAEC,KAAK,EAAE;IAC9B,QAAQD,KAAK;MACX,KAAK,GAAG;QACN,OAAOR,mBAAmB,CAACJ,eAAe,CAACc,IAAI,EAAEH,UAAU,CAAC;MAC9D,KAAK,IAAI;QACP,OAAOE,KAAK,CAACE,aAAa,CAACJ,UAAU,EAAE;UAAEK,IAAI,EAAE;QAAO,CAAC,CAAC;MAC1D;QACE,OAAOb,YAAY,CAACS,KAAK,CAACK,MAAM,EAAEN,UAAU,CAAC;IACjD;EACF;EAEAO,QAAQA,CAACJ,IAAI,EAAEK,KAAK,EAAE;IACpB,MAAMC,IAAI,GAAGN,IAAI,CAACO,WAAW,CAAC,CAAC;IAC/B,MAAMC,UAAU,GAAGpB,eAAe,CAACkB,IAAI,CAAC;IACxC,MAAMG,KAAK,GAAGT,IAAI,CAACU,QAAQ,CAAC,CAAC;IAC7B,IAAIF,UAAU,EAAE;MACd,OAAOH,KAAK,IAAI,CAAC,IAAIA,KAAK,IAAIb,uBAAuB,CAACiB,KAAK,CAAC;IAC9D,CAAC,MAAM;MACL,OAAOJ,KAAK,IAAI,CAAC,IAAIA,KAAK,IAAId,aAAa,CAACkB,KAAK,CAAC;IACpD;EACF;EAEAE,GAAGA,CAACX,IAAI,EAAEY,MAAM,EAAEP,KAAK,EAAE;IACvBL,IAAI,CAACa,OAAO,CAACR,KAAK,CAAC;IACnBL,IAAI,CAACc,QAAQ,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC;IACzB,OAAOd,IAAI;EACb;EAEAe,kBAAkB,GAAG,CACnB,GAAG,EACH,GAAG,EACH,GAAG,EACH,GAAG,EACH,GAAG,EACH,GAAG,EACH,GAAG,EACH,GAAG,EACH,GAAG,EACH,GAAG,EACH,GAAG,EACH,GAAG,CACJ;AACH", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}