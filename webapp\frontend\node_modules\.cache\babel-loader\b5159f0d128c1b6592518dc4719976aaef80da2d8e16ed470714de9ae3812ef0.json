{"ast": null, "code": "'use client';\n\nimport * as React from 'react';\nimport RadioGroupContext from './RadioGroupContext';\nexport default function useRadioGroup() {\n  return React.useContext(RadioGroupContext);\n}", "map": {"version": 3, "names": ["React", "RadioGroupContext", "useRadioGroup", "useContext"], "sources": ["C:/Users/<USER>/PycharmProjects/CMS/webapp/frontend/node_modules/@mui/material/RadioGroup/useRadioGroup.js"], "sourcesContent": ["'use client';\n\nimport * as React from 'react';\nimport RadioGroupContext from './RadioGroupContext';\nexport default function useRadioGroup() {\n  return React.useContext(RadioGroupContext);\n}"], "mappings": "AAAA,YAAY;;AAEZ,OAAO,KAAKA,KAAK,MAAM,OAAO;AAC9B,OAAOC,iBAAiB,MAAM,qBAAqB;AACnD,eAAe,SAASC,aAAaA,CAAA,EAAG;EACtC,OAAOF,KAAK,CAACG,UAAU,CAACF,iBAAiB,CAAC;AAC5C", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}