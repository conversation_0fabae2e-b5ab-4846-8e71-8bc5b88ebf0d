{"ast": null, "code": "var _jsxFileName = \"C:\\\\CMS\\\\webapp\\\\frontend\\\\src\\\\components\\\\responsabili\\\\GestioneResponsabili.js\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect } from 'react';\nimport { Box, Card, CardContent, Typography, Button, Table, TableBody, TableCell, TableContainer, TableHead, TableRow, Paper, IconButton, Dialog, DialogTitle, DialogContent, DialogActions, TextField, Alert, CircularProgress, Tooltip, Chip, Accordion, AccordionSummary, AccordionDetails, List, ListItem, ListItemText, Divider } from '@mui/material';\nimport { Add as AddIcon, Edit as EditIcon, Delete as DeleteIcon, Person as PersonIcon, Email as EmailIcon, Phone as PhoneIcon, ExpandMore as ExpandMoreIcon, Assignment as AssignmentIcon } from '@mui/icons-material';\nimport responsabiliService from '../../services/responsabiliService';\nimport comandeService from '../../services/comandeService';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst GestioneResponsabili = ({\n  cantiereId,\n  open,\n  onClose\n}) => {\n  _s();\n  const [responsabili, setResponsabili] = useState([]);\n  const [loading, setLoading] = useState(false);\n  const [error, setError] = useState(null);\n  const [openDialog, setOpenDialog] = useState(false);\n  const [dialogMode, setDialogMode] = useState('create'); // 'create', 'edit'\n  const [selectedResponsabile, setSelectedResponsabile] = useState(null);\n  const [comandePerResponsabile, setComandePerResponsabile] = useState({});\n  const [formData, setFormData] = useState({\n    nome_responsabile: '',\n    email: '',\n    telefono: ''\n  });\n  useEffect(() => {\n    if (open && cantiereId) {\n      loadResponsabili();\n    }\n  }, [open, cantiereId]);\n  const loadResponsabili = async () => {\n    try {\n      setLoading(true);\n      setError(null);\n      const data = await responsabiliService.getResponsabiliCantiere(cantiereId);\n      setResponsabili(data || []);\n\n      // Carica le comande per ogni responsabile\n      await loadComandePerResponsabili(data || []);\n    } catch (err) {\n      console.error('Errore nel caricamento dei responsabili:', err);\n      setError('Errore nel caricamento dei responsabili');\n    } finally {\n      setLoading(false);\n    }\n  };\n  const loadComandePerResponsabili = async responsabiliList => {\n    try {\n      const comandeMap = {};\n      for (const responsabile of responsabiliList) {\n        try {\n          const comande = await comandeService.getComandeByResponsabile(cantiereId, responsabile.nome_responsabile);\n          comandeMap[responsabile.id_responsabile] = comande || [];\n        } catch (err) {\n          console.error(`Errore nel caricamento comande per ${responsabile.nome_responsabile}:`, err);\n          comandeMap[responsabile.id_responsabile] = [];\n        }\n      }\n      setComandePerResponsabile(comandeMap);\n    } catch (err) {\n      console.error('Errore nel caricamento delle comande:', err);\n    }\n  };\n  const handleOpenDialog = (mode, responsabile = null) => {\n    setDialogMode(mode);\n    setSelectedResponsabile(responsabile);\n    if (mode === 'edit' && responsabile) {\n      setFormData({\n        nome_responsabile: responsabile.nome_responsabile || '',\n        email: responsabile.email || '',\n        telefono: responsabile.telefono || ''\n      });\n    } else {\n      setFormData({\n        nome_responsabile: '',\n        email: '',\n        telefono: ''\n      });\n    }\n    setOpenDialog(true);\n  };\n  const handleCloseDialog = () => {\n    setOpenDialog(false);\n    setSelectedResponsabile(null);\n    setError(null);\n  };\n  const handleSubmit = async () => {\n    try {\n      setError(null);\n\n      // Validazione\n      if (!formData.nome_responsabile.trim()) {\n        setError('Il nome del responsabile è obbligatorio');\n        return;\n      }\n      if (!formData.email && !formData.telefono) {\n        setError('Almeno uno tra email e telefono deve essere specificato');\n        return;\n      }\n      if (dialogMode === 'create') {\n        await responsabiliService.createResponsabile(cantiereId, formData);\n      } else if (dialogMode === 'edit') {\n        await responsabiliService.updateResponsabile(selectedResponsabile.id_responsabile, formData);\n      }\n      handleCloseDialog();\n      await loadResponsabili();\n    } catch (err) {\n      console.error('Errore nel salvataggio:', err);\n      setError(err.detail || 'Errore nel salvataggio del responsabile');\n    }\n  };\n  const handleDelete = async idResponsabile => {\n    if (!window.confirm('Sei sicuro di voler eliminare questo responsabile?')) {\n      return;\n    }\n    try {\n      await responsabiliService.deleteResponsabile(idResponsabile);\n      await loadResponsabili();\n    } catch (err) {\n      console.error('Errore nell\\'eliminazione:', err);\n      setError('Errore nell\\'eliminazione del responsabile');\n    }\n  };\n  const getTipoComandaLabel = tipo => {\n    const labels = {\n      'POSA': 'Posa',\n      'COLLEGAMENTO_PARTENZA': 'Coll. Partenza',\n      'COLLEGAMENTO_ARRIVO': 'Coll. Arrivo',\n      'CERTIFICAZIONE': 'Certificazione',\n      'TESTING': 'Testing'\n    };\n    return labels[tipo] || tipo;\n  };\n  const getStatoComandaColor = stato => {\n    const colors = {\n      'CREATA': 'default',\n      'IN_CORSO': 'primary',\n      'COMPLETATA': 'success',\n      'ANNULLATA': 'error'\n    };\n    return colors[stato] || 'default';\n  };\n  return /*#__PURE__*/_jsxDEV(Dialog, {\n    open: open,\n    onClose: onClose,\n    maxWidth: \"lg\",\n    fullWidth: true,\n    children: [/*#__PURE__*/_jsxDEV(DialogTitle, {\n      children: /*#__PURE__*/_jsxDEV(Box, {\n        display: \"flex\",\n        alignItems: \"center\",\n        gap: 1,\n        children: [/*#__PURE__*/_jsxDEV(PersonIcon, {}, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 198,\n          columnNumber: 11\n        }, this), \"Gestione Responsabili\"]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 197,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 196,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(DialogContent, {\n      children: /*#__PURE__*/_jsxDEV(Box, {\n        sx: {\n          pt: 1\n        },\n        children: [error && /*#__PURE__*/_jsxDEV(Alert, {\n          severity: \"error\",\n          sx: {\n            mb: 2\n          },\n          children: error\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 206,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(Box, {\n          display: \"flex\",\n          justifyContent: \"space-between\",\n          alignItems: \"center\",\n          mb: 2,\n          children: [/*#__PURE__*/_jsxDEV(Typography, {\n            variant: \"h6\",\n            children: \"Responsabili del Cantiere\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 213,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Button, {\n            variant: \"contained\",\n            startIcon: /*#__PURE__*/_jsxDEV(AddIcon, {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 218,\n              columnNumber: 26\n            }, this),\n            onClick: () => handleOpenDialog('create'),\n            children: \"Nuovo Responsabile\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 216,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 212,\n          columnNumber: 11\n        }, this), loading ? /*#__PURE__*/_jsxDEV(Box, {\n          display: \"flex\",\n          justifyContent: \"center\",\n          py: 4,\n          children: /*#__PURE__*/_jsxDEV(CircularProgress, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 227,\n            columnNumber: 15\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 226,\n          columnNumber: 13\n        }, this) : /*#__PURE__*/_jsxDEV(Box, {\n          children: responsabili.length === 0 ? /*#__PURE__*/_jsxDEV(Box, {\n            textAlign: \"center\",\n            py: 4,\n            children: [/*#__PURE__*/_jsxDEV(Typography, {\n              variant: \"h6\",\n              color: \"textSecondary\",\n              children: \"Nessun responsabile trovato\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 233,\n              columnNumber: 19\n            }, this), /*#__PURE__*/_jsxDEV(Typography, {\n              variant: \"body2\",\n              color: \"textSecondary\",\n              children: \"Clicca su \\\"Nuovo Responsabile\\\" per iniziare\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 236,\n              columnNumber: 19\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 232,\n            columnNumber: 17\n          }, this) : responsabili.map(responsabile => /*#__PURE__*/_jsxDEV(Accordion, {\n            sx: {\n              mb: 1\n            },\n            children: [/*#__PURE__*/_jsxDEV(AccordionSummary, {\n              expandIcon: /*#__PURE__*/_jsxDEV(ExpandMoreIcon, {}, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 243,\n                columnNumber: 51\n              }, this),\n              children: /*#__PURE__*/_jsxDEV(Box, {\n                display: \"flex\",\n                alignItems: \"center\",\n                justifyContent: \"space-between\",\n                width: \"100%\",\n                children: [/*#__PURE__*/_jsxDEV(Box, {\n                  display: \"flex\",\n                  alignItems: \"center\",\n                  gap: 2,\n                  children: [/*#__PURE__*/_jsxDEV(PersonIcon, {\n                    color: \"primary\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 246,\n                    columnNumber: 27\n                  }, this), /*#__PURE__*/_jsxDEV(Box, {\n                    children: [/*#__PURE__*/_jsxDEV(Typography, {\n                      variant: \"h6\",\n                      children: responsabile.nome_responsabile\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 248,\n                      columnNumber: 29\n                    }, this), /*#__PURE__*/_jsxDEV(Box, {\n                      display: \"flex\",\n                      gap: 2,\n                      mt: 0.5,\n                      children: [responsabile.email && /*#__PURE__*/_jsxDEV(Box, {\n                        display: \"flex\",\n                        alignItems: \"center\",\n                        gap: 0.5,\n                        children: [/*#__PURE__*/_jsxDEV(EmailIcon, {\n                          fontSize: \"small\",\n                          color: \"action\"\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 254,\n                          columnNumber: 35\n                        }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                          variant: \"body2\",\n                          color: \"textSecondary\",\n                          children: responsabile.email\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 255,\n                          columnNumber: 35\n                        }, this)]\n                      }, void 0, true, {\n                        fileName: _jsxFileName,\n                        lineNumber: 253,\n                        columnNumber: 33\n                      }, this), responsabile.telefono && /*#__PURE__*/_jsxDEV(Box, {\n                        display: \"flex\",\n                        alignItems: \"center\",\n                        gap: 0.5,\n                        children: [/*#__PURE__*/_jsxDEV(PhoneIcon, {\n                          fontSize: \"small\",\n                          color: \"action\"\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 262,\n                          columnNumber: 35\n                        }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                          variant: \"body2\",\n                          color: \"textSecondary\",\n                          children: responsabile.telefono\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 263,\n                          columnNumber: 35\n                        }, this)]\n                      }, void 0, true, {\n                        fileName: _jsxFileName,\n                        lineNumber: 261,\n                        columnNumber: 33\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 251,\n                      columnNumber: 29\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 247,\n                    columnNumber: 27\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 245,\n                  columnNumber: 25\n                }, this), /*#__PURE__*/_jsxDEV(Box, {\n                  display: \"flex\",\n                  gap: 1,\n                  onClick: e => e.stopPropagation(),\n                  children: [/*#__PURE__*/_jsxDEV(Chip, {\n                    icon: /*#__PURE__*/_jsxDEV(AssignmentIcon, {}, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 274,\n                      columnNumber: 35\n                    }, this),\n                    label: `${(comandePerResponsabile[responsabile.id_responsabile] || []).length} comande`,\n                    size: \"small\",\n                    color: \"primary\",\n                    variant: \"outlined\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 273,\n                    columnNumber: 27\n                  }, this), /*#__PURE__*/_jsxDEV(Tooltip, {\n                    title: \"Modifica\",\n                    children: /*#__PURE__*/_jsxDEV(IconButton, {\n                      size: \"small\",\n                      onClick: () => handleOpenDialog('edit', responsabile),\n                      children: /*#__PURE__*/_jsxDEV(EditIcon, {}, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 285,\n                        columnNumber: 31\n                      }, this)\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 281,\n                      columnNumber: 29\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 280,\n                    columnNumber: 27\n                  }, this), /*#__PURE__*/_jsxDEV(Tooltip, {\n                    title: \"Elimina\",\n                    children: /*#__PURE__*/_jsxDEV(IconButton, {\n                      size: \"small\",\n                      color: \"error\",\n                      onClick: () => handleDelete(responsabile.id_responsabile),\n                      children: /*#__PURE__*/_jsxDEV(DeleteIcon, {}, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 294,\n                        columnNumber: 31\n                      }, this)\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 289,\n                      columnNumber: 29\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 288,\n                    columnNumber: 27\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 272,\n                  columnNumber: 25\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 244,\n                columnNumber: 23\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 243,\n              columnNumber: 21\n            }, this), /*#__PURE__*/_jsxDEV(AccordionDetails, {\n              children: [/*#__PURE__*/_jsxDEV(Typography, {\n                variant: \"subtitle2\",\n                gutterBottom: true,\n                children: \"Comande Assegnate:\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 302,\n                columnNumber: 23\n              }, this), (comandePerResponsabile[responsabile.id_responsabile] || []).length === 0 ? /*#__PURE__*/_jsxDEV(Typography, {\n                variant: \"body2\",\n                color: \"textSecondary\",\n                style: {\n                  fontStyle: 'italic'\n                },\n                children: \"Nessuna comanda assegnata\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 307,\n                columnNumber: 25\n              }, this) : /*#__PURE__*/_jsxDEV(List, {\n                dense: true,\n                children: (comandePerResponsabile[responsabile.id_responsabile] || []).map(comanda => /*#__PURE__*/_jsxDEV(ListItem, {\n                  divider: true,\n                  children: /*#__PURE__*/_jsxDEV(ListItemText, {\n                    primary: /*#__PURE__*/_jsxDEV(Box, {\n                      display: \"flex\",\n                      alignItems: \"center\",\n                      gap: 1,\n                      children: [/*#__PURE__*/_jsxDEV(Typography, {\n                        variant: \"body2\",\n                        fontWeight: \"bold\",\n                        children: comanda.codice_comanda\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 317,\n                        columnNumber: 37\n                      }, this), /*#__PURE__*/_jsxDEV(Chip, {\n                        label: getTipoComandaLabel(comanda.tipo_comanda),\n                        size: \"small\",\n                        variant: \"outlined\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 320,\n                        columnNumber: 37\n                      }, this), /*#__PURE__*/_jsxDEV(Chip, {\n                        label: comanda.stato || 'CREATA',\n                        size: \"small\",\n                        color: getStatoComandaColor(comanda.stato)\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 325,\n                        columnNumber: 37\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 316,\n                      columnNumber: 35\n                    }, this),\n                    secondary: /*#__PURE__*/_jsxDEV(Typography, {\n                      variant: \"body2\",\n                      color: \"textSecondary\",\n                      children: [comanda.descrizione || 'Nessuna descrizione', comanda.data_creazione && ` • Creata: ${new Date(comanda.data_creazione).toLocaleDateString()}`]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 333,\n                      columnNumber: 35\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 314,\n                    columnNumber: 31\n                  }, this)\n                }, comanda.codice_comanda, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 313,\n                  columnNumber: 29\n                }, this))\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 311,\n                columnNumber: 25\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 301,\n              columnNumber: 21\n            }, this)]\n          }, responsabile.id_responsabile, true, {\n            fileName: _jsxFileName,\n            lineNumber: 242,\n            columnNumber: 19\n          }, this))\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 230,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 204,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 203,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(DialogActions, {\n      children: /*#__PURE__*/_jsxDEV(Button, {\n        onClick: onClose,\n        children: \"Chiudi\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 353,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 352,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Dialog, {\n      open: openDialog,\n      onClose: handleCloseDialog,\n      maxWidth: \"sm\",\n      fullWidth: true,\n      children: [/*#__PURE__*/_jsxDEV(DialogTitle, {\n        children: dialogMode === 'create' ? 'Nuovo Responsabile' : 'Modifica Responsabile'\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 360,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(DialogContent, {\n        children: /*#__PURE__*/_jsxDEV(Box, {\n          sx: {\n            pt: 1\n          },\n          children: [error && /*#__PURE__*/_jsxDEV(Alert, {\n            severity: \"error\",\n            sx: {\n              mb: 2\n            },\n            children: error\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 366,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(TextField, {\n            fullWidth: true,\n            label: \"Nome Responsabile\",\n            value: formData.nome_responsabile,\n            onChange: e => setFormData({\n              ...formData,\n              nome_responsabile: e.target.value\n            }),\n            margin: \"normal\",\n            required: true\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 371,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(TextField, {\n            fullWidth: true,\n            label: \"Email\",\n            type: \"email\",\n            value: formData.email,\n            onChange: e => setFormData({\n              ...formData,\n              email: e.target.value\n            }),\n            margin: \"normal\",\n            helperText: \"Email per notifiche (opzionale se inserisci telefono)\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 380,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(TextField, {\n            fullWidth: true,\n            label: \"Telefono\",\n            value: formData.telefono,\n            onChange: e => setFormData({\n              ...formData,\n              telefono: e.target.value\n            }),\n            margin: \"normal\",\n            helperText: \"Numero per SMS (opzionale se inserisci email)\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 390,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 364,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 363,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(DialogActions, {\n        children: [/*#__PURE__*/_jsxDEV(Button, {\n          onClick: handleCloseDialog,\n          children: \"Annulla\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 401,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Button, {\n          onClick: handleSubmit,\n          variant: \"contained\",\n          children: dialogMode === 'create' ? 'Crea' : 'Salva'\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 404,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 400,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 359,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 195,\n    columnNumber: 5\n  }, this);\n};\n_s(GestioneResponsabili, \"byYVs/mlfImcsDzYynNI3zmMheE=\");\n_c = GestioneResponsabili;\nexport default GestioneResponsabili;\nvar _c;\n$RefreshReg$(_c, \"GestioneResponsabili\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "Box", "Card", "<PERSON><PERSON><PERSON><PERSON>", "Typography", "<PERSON><PERSON>", "Table", "TableBody", "TableCell", "TableContainer", "TableHead", "TableRow", "Paper", "IconButton", "Dialog", "DialogTitle", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "DialogActions", "TextField", "<PERSON><PERSON>", "CircularProgress", "<PERSON><PERSON><PERSON>", "Chip", "Accordion", "AccordionSummary", "AccordionDetails", "List", "ListItem", "ListItemText", "Divider", "Add", "AddIcon", "Edit", "EditIcon", "Delete", "DeleteIcon", "Person", "PersonIcon", "Email", "EmailIcon", "Phone", "PhoneIcon", "ExpandMore", "ExpandMoreIcon", "Assignment", "AssignmentIcon", "responsabiliService", "comandeService", "jsxDEV", "_jsxDEV", "GestioneResponsabili", "cantiereId", "open", "onClose", "_s", "responsabili", "setResponsabili", "loading", "setLoading", "error", "setError", "openDialog", "setOpenDialog", "dialogMode", "setDialogMode", "selectedResponsabile", "setSelectedResponsabile", "comandePerResponsabile", "setComandePerResponsabile", "formData", "setFormData", "nome_responsabile", "email", "telefono", "loadResponsabili", "data", "getResponsabiliCantiere", "loadComandePerResponsabili", "err", "console", "responsabiliList", "comandeMap", "responsabile", "comande", "getComandeByResponsabile", "id_responsabile", "handleOpenDialog", "mode", "handleCloseDialog", "handleSubmit", "trim", "createResponsabile", "updateResponsabile", "detail", "handleDelete", "idResponsabile", "window", "confirm", "deleteResponsabile", "getTipoComandaLabel", "tipo", "labels", "getStatoComandaColor", "stato", "colors", "max<PERSON><PERSON><PERSON>", "fullWidth", "children", "display", "alignItems", "gap", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "sx", "pt", "severity", "mb", "justifyContent", "variant", "startIcon", "onClick", "py", "length", "textAlign", "color", "map", "expandIcon", "width", "mt", "fontSize", "e", "stopPropagation", "icon", "label", "size", "title", "gutterBottom", "style", "fontStyle", "dense", "comanda", "divider", "primary", "fontWeight", "codice_comanda", "tipo_comanda", "secondary", "descrizione", "data_creazione", "Date", "toLocaleDateString", "value", "onChange", "target", "margin", "required", "type", "helperText", "_c", "$RefreshReg$"], "sources": ["C:/CMS/webapp/frontend/src/components/responsabili/GestioneResponsabili.js"], "sourcesContent": ["import React, { useState, useEffect } from 'react';\nimport {\n  <PERSON>,\n  Card,\n  CardContent,\n  Typo<PERSON>,\n  Button,\n  Table,\n  TableBody,\n  TableCell,\n  TableContainer,\n  TableHead,\n  TableRow,\n  Paper,\n  IconButton,\n  Dialog,\n  DialogTitle,\n  DialogContent,\n  DialogActions,\n  TextField,\n  Alert,\n  CircularProgress,\n  Tooltip,\n  Chip,\n  Accordion,\n  AccordionSummary,\n  AccordionDetails,\n  List,\n  ListItem,\n  ListItemText,\n  Divider\n} from '@mui/material';\nimport {\n  Add as AddIcon,\n  Edit as EditIcon,\n  Delete as DeleteIcon,\n  Person as PersonIcon,\n  Email as EmailIcon,\n  Phone as PhoneIcon,\n  ExpandMore as ExpandMoreIcon,\n  Assignment as AssignmentIcon\n} from '@mui/icons-material';\nimport responsabiliService from '../../services/responsabiliService';\nimport comandeService from '../../services/comandeService';\n\nconst GestioneResponsabili = ({ cantiereId, open, onClose }) => {\n  const [responsabili, setResponsabili] = useState([]);\n  const [loading, setLoading] = useState(false);\n  const [error, setError] = useState(null);\n  const [openDialog, setOpenDialog] = useState(false);\n  const [dialogMode, setDialogMode] = useState('create'); // 'create', 'edit'\n  const [selectedResponsabile, setSelectedResponsabile] = useState(null);\n  const [comandePerResponsabile, setComandePerResponsabile] = useState({});\n  const [formData, setFormData] = useState({\n    nome_responsabile: '',\n    email: '',\n    telefono: ''\n  });\n\n  useEffect(() => {\n    if (open && cantiereId) {\n      loadResponsabili();\n    }\n  }, [open, cantiereId]);\n\n  const loadResponsabili = async () => {\n    try {\n      setLoading(true);\n      setError(null);\n      const data = await responsabiliService.getResponsabiliCantiere(cantiereId);\n      setResponsabili(data || []);\n      \n      // Carica le comande per ogni responsabile\n      await loadComandePerResponsabili(data || []);\n    } catch (err) {\n      console.error('Errore nel caricamento dei responsabili:', err);\n      setError('Errore nel caricamento dei responsabili');\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  const loadComandePerResponsabili = async (responsabiliList) => {\n    try {\n      const comandeMap = {};\n      \n      for (const responsabile of responsabiliList) {\n        try {\n          const comande = await comandeService.getComandeByResponsabile(cantiereId, responsabile.nome_responsabile);\n          comandeMap[responsabile.id_responsabile] = comande || [];\n        } catch (err) {\n          console.error(`Errore nel caricamento comande per ${responsabile.nome_responsabile}:`, err);\n          comandeMap[responsabile.id_responsabile] = [];\n        }\n      }\n      \n      setComandePerResponsabile(comandeMap);\n    } catch (err) {\n      console.error('Errore nel caricamento delle comande:', err);\n    }\n  };\n\n  const handleOpenDialog = (mode, responsabile = null) => {\n    setDialogMode(mode);\n    setSelectedResponsabile(responsabile);\n    \n    if (mode === 'edit' && responsabile) {\n      setFormData({\n        nome_responsabile: responsabile.nome_responsabile || '',\n        email: responsabile.email || '',\n        telefono: responsabile.telefono || ''\n      });\n    } else {\n      setFormData({\n        nome_responsabile: '',\n        email: '',\n        telefono: ''\n      });\n    }\n    \n    setOpenDialog(true);\n  };\n\n  const handleCloseDialog = () => {\n    setOpenDialog(false);\n    setSelectedResponsabile(null);\n    setError(null);\n  };\n\n  const handleSubmit = async () => {\n    try {\n      setError(null);\n      \n      // Validazione\n      if (!formData.nome_responsabile.trim()) {\n        setError('Il nome del responsabile è obbligatorio');\n        return;\n      }\n      \n      if (!formData.email && !formData.telefono) {\n        setError('Almeno uno tra email e telefono deve essere specificato');\n        return;\n      }\n\n      if (dialogMode === 'create') {\n        await responsabiliService.createResponsabile(cantiereId, formData);\n      } else if (dialogMode === 'edit') {\n        await responsabiliService.updateResponsabile(selectedResponsabile.id_responsabile, formData);\n      }\n\n      handleCloseDialog();\n      await loadResponsabili();\n    } catch (err) {\n      console.error('Errore nel salvataggio:', err);\n      setError(err.detail || 'Errore nel salvataggio del responsabile');\n    }\n  };\n\n  const handleDelete = async (idResponsabile) => {\n    if (!window.confirm('Sei sicuro di voler eliminare questo responsabile?')) {\n      return;\n    }\n\n    try {\n      await responsabiliService.deleteResponsabile(idResponsabile);\n      await loadResponsabili();\n    } catch (err) {\n      console.error('Errore nell\\'eliminazione:', err);\n      setError('Errore nell\\'eliminazione del responsabile');\n    }\n  };\n\n  const getTipoComandaLabel = (tipo) => {\n    const labels = {\n      'POSA': 'Posa',\n      'COLLEGAMENTO_PARTENZA': 'Coll. Partenza',\n      'COLLEGAMENTO_ARRIVO': 'Coll. Arrivo',\n      'CERTIFICAZIONE': 'Certificazione',\n      'TESTING': 'Testing'\n    };\n    return labels[tipo] || tipo;\n  };\n\n  const getStatoComandaColor = (stato) => {\n    const colors = {\n      'CREATA': 'default',\n      'IN_CORSO': 'primary',\n      'COMPLETATA': 'success',\n      'ANNULLATA': 'error'\n    };\n    return colors[stato] || 'default';\n  };\n\n  return (\n    <Dialog open={open} onClose={onClose} maxWidth=\"lg\" fullWidth>\n      <DialogTitle>\n        <Box display=\"flex\" alignItems=\"center\" gap={1}>\n          <PersonIcon />\n          Gestione Responsabili\n        </Box>\n      </DialogTitle>\n      \n      <DialogContent>\n        <Box sx={{ pt: 1 }}>\n          {error && (\n            <Alert severity=\"error\" sx={{ mb: 2 }}>\n              {error}\n            </Alert>\n          )}\n\n          {/* Toolbar */}\n          <Box display=\"flex\" justifyContent=\"space-between\" alignItems=\"center\" mb={2}>\n            <Typography variant=\"h6\">\n              Responsabili del Cantiere\n            </Typography>\n            <Button\n              variant=\"contained\"\n              startIcon={<AddIcon />}\n              onClick={() => handleOpenDialog('create')}\n            >\n              Nuovo Responsabile\n            </Button>\n          </Box>\n\n          {loading ? (\n            <Box display=\"flex\" justifyContent=\"center\" py={4}>\n              <CircularProgress />\n            </Box>\n          ) : (\n            <Box>\n              {responsabili.length === 0 ? (\n                <Box textAlign=\"center\" py={4}>\n                  <Typography variant=\"h6\" color=\"textSecondary\">\n                    Nessun responsabile trovato\n                  </Typography>\n                  <Typography variant=\"body2\" color=\"textSecondary\">\n                    Clicca su \"Nuovo Responsabile\" per iniziare\n                  </Typography>\n                </Box>\n              ) : (\n                responsabili.map((responsabile) => (\n                  <Accordion key={responsabile.id_responsabile} sx={{ mb: 1 }}>\n                    <AccordionSummary expandIcon={<ExpandMoreIcon />}>\n                      <Box display=\"flex\" alignItems=\"center\" justifyContent=\"space-between\" width=\"100%\">\n                        <Box display=\"flex\" alignItems=\"center\" gap={2}>\n                          <PersonIcon color=\"primary\" />\n                          <Box>\n                            <Typography variant=\"h6\">\n                              {responsabile.nome_responsabile}\n                            </Typography>\n                            <Box display=\"flex\" gap={2} mt={0.5}>\n                              {responsabile.email && (\n                                <Box display=\"flex\" alignItems=\"center\" gap={0.5}>\n                                  <EmailIcon fontSize=\"small\" color=\"action\" />\n                                  <Typography variant=\"body2\" color=\"textSecondary\">\n                                    {responsabile.email}\n                                  </Typography>\n                                </Box>\n                              )}\n                              {responsabile.telefono && (\n                                <Box display=\"flex\" alignItems=\"center\" gap={0.5}>\n                                  <PhoneIcon fontSize=\"small\" color=\"action\" />\n                                  <Typography variant=\"body2\" color=\"textSecondary\">\n                                    {responsabile.telefono}\n                                  </Typography>\n                                </Box>\n                              )}\n                            </Box>\n                          </Box>\n                        </Box>\n                        \n                        <Box display=\"flex\" gap={1} onClick={(e) => e.stopPropagation()}>\n                          <Chip\n                            icon={<AssignmentIcon />}\n                            label={`${(comandePerResponsabile[responsabile.id_responsabile] || []).length} comande`}\n                            size=\"small\"\n                            color=\"primary\"\n                            variant=\"outlined\"\n                          />\n                          <Tooltip title=\"Modifica\">\n                            <IconButton\n                              size=\"small\"\n                              onClick={() => handleOpenDialog('edit', responsabile)}\n                            >\n                              <EditIcon />\n                            </IconButton>\n                          </Tooltip>\n                          <Tooltip title=\"Elimina\">\n                            <IconButton\n                              size=\"small\"\n                              color=\"error\"\n                              onClick={() => handleDelete(responsabile.id_responsabile)}\n                            >\n                              <DeleteIcon />\n                            </IconButton>\n                          </Tooltip>\n                        </Box>\n                      </Box>\n                    </AccordionSummary>\n                    \n                    <AccordionDetails>\n                      <Typography variant=\"subtitle2\" gutterBottom>\n                        Comande Assegnate:\n                      </Typography>\n                      \n                      {(comandePerResponsabile[responsabile.id_responsabile] || []).length === 0 ? (\n                        <Typography variant=\"body2\" color=\"textSecondary\" style={{ fontStyle: 'italic' }}>\n                          Nessuna comanda assegnata\n                        </Typography>\n                      ) : (\n                        <List dense>\n                          {(comandePerResponsabile[responsabile.id_responsabile] || []).map((comanda) => (\n                            <ListItem key={comanda.codice_comanda} divider>\n                              <ListItemText\n                                primary={\n                                  <Box display=\"flex\" alignItems=\"center\" gap={1}>\n                                    <Typography variant=\"body2\" fontWeight=\"bold\">\n                                      {comanda.codice_comanda}\n                                    </Typography>\n                                    <Chip\n                                      label={getTipoComandaLabel(comanda.tipo_comanda)}\n                                      size=\"small\"\n                                      variant=\"outlined\"\n                                    />\n                                    <Chip\n                                      label={comanda.stato || 'CREATA'}\n                                      size=\"small\"\n                                      color={getStatoComandaColor(comanda.stato)}\n                                    />\n                                  </Box>\n                                }\n                                secondary={\n                                  <Typography variant=\"body2\" color=\"textSecondary\">\n                                    {comanda.descrizione || 'Nessuna descrizione'}\n                                    {comanda.data_creazione && ` • Creata: ${new Date(comanda.data_creazione).toLocaleDateString()}`}\n                                  </Typography>\n                                }\n                              />\n                            </ListItem>\n                          ))}\n                        </List>\n                      )}\n                    </AccordionDetails>\n                  </Accordion>\n                ))\n              )}\n            </Box>\n          )}\n        </Box>\n      </DialogContent>\n\n      <DialogActions>\n        <Button onClick={onClose}>\n          Chiudi\n        </Button>\n      </DialogActions>\n\n      {/* Dialog per creazione/modifica responsabile */}\n      <Dialog open={openDialog} onClose={handleCloseDialog} maxWidth=\"sm\" fullWidth>\n        <DialogTitle>\n          {dialogMode === 'create' ? 'Nuovo Responsabile' : 'Modifica Responsabile'}\n        </DialogTitle>\n        <DialogContent>\n          <Box sx={{ pt: 1 }}>\n            {error && (\n              <Alert severity=\"error\" sx={{ mb: 2 }}>\n                {error}\n              </Alert>\n            )}\n\n            <TextField\n              fullWidth\n              label=\"Nome Responsabile\"\n              value={formData.nome_responsabile}\n              onChange={(e) => setFormData({ ...formData, nome_responsabile: e.target.value })}\n              margin=\"normal\"\n              required\n            />\n\n            <TextField\n              fullWidth\n              label=\"Email\"\n              type=\"email\"\n              value={formData.email}\n              onChange={(e) => setFormData({ ...formData, email: e.target.value })}\n              margin=\"normal\"\n              helperText=\"Email per notifiche (opzionale se inserisci telefono)\"\n            />\n\n            <TextField\n              fullWidth\n              label=\"Telefono\"\n              value={formData.telefono}\n              onChange={(e) => setFormData({ ...formData, telefono: e.target.value })}\n              margin=\"normal\"\n              helperText=\"Numero per SMS (opzionale se inserisci email)\"\n            />\n          </Box>\n        </DialogContent>\n        <DialogActions>\n          <Button onClick={handleCloseDialog}>\n            Annulla\n          </Button>\n          <Button onClick={handleSubmit} variant=\"contained\">\n            {dialogMode === 'create' ? 'Crea' : 'Salva'}\n          </Button>\n        </DialogActions>\n      </Dialog>\n    </Dialog>\n  );\n};\n\nexport default GestioneResponsabili;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAClD,SACEC,GAAG,EACHC,IAAI,EACJC,WAAW,EACXC,UAAU,EACVC,MAAM,EACNC,KAAK,EACLC,SAAS,EACTC,SAAS,EACTC,cAAc,EACdC,SAAS,EACTC,QAAQ,EACRC,KAAK,EACLC,UAAU,EACVC,MAAM,EACNC,WAAW,EACXC,aAAa,EACbC,aAAa,EACbC,SAAS,EACTC,KAAK,EACLC,gBAAgB,EAChBC,OAAO,EACPC,IAAI,EACJC,SAAS,EACTC,gBAAgB,EAChBC,gBAAgB,EAChBC,IAAI,EACJC,QAAQ,EACRC,YAAY,EACZC,OAAO,QACF,eAAe;AACtB,SACEC,GAAG,IAAIC,OAAO,EACdC,IAAI,IAAIC,QAAQ,EAChBC,MAAM,IAAIC,UAAU,EACpBC,MAAM,IAAIC,UAAU,EACpBC,KAAK,IAAIC,SAAS,EAClBC,KAAK,IAAIC,SAAS,EAClBC,UAAU,IAAIC,cAAc,EAC5BC,UAAU,IAAIC,cAAc,QACvB,qBAAqB;AAC5B,OAAOC,mBAAmB,MAAM,oCAAoC;AACpE,OAAOC,cAAc,MAAM,+BAA+B;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAE3D,MAAMC,oBAAoB,GAAGA,CAAC;EAAEC,UAAU;EAAEC,IAAI;EAAEC;AAAQ,CAAC,KAAK;EAAAC,EAAA;EAC9D,MAAM,CAACC,YAAY,EAAEC,eAAe,CAAC,GAAGzD,QAAQ,CAAC,EAAE,CAAC;EACpD,MAAM,CAAC0D,OAAO,EAAEC,UAAU,CAAC,GAAG3D,QAAQ,CAAC,KAAK,CAAC;EAC7C,MAAM,CAAC4D,KAAK,EAAEC,QAAQ,CAAC,GAAG7D,QAAQ,CAAC,IAAI,CAAC;EACxC,MAAM,CAAC8D,UAAU,EAAEC,aAAa,CAAC,GAAG/D,QAAQ,CAAC,KAAK,CAAC;EACnD,MAAM,CAACgE,UAAU,EAAEC,aAAa,CAAC,GAAGjE,QAAQ,CAAC,QAAQ,CAAC,CAAC,CAAC;EACxD,MAAM,CAACkE,oBAAoB,EAAEC,uBAAuB,CAAC,GAAGnE,QAAQ,CAAC,IAAI,CAAC;EACtE,MAAM,CAACoE,sBAAsB,EAAEC,yBAAyB,CAAC,GAAGrE,QAAQ,CAAC,CAAC,CAAC,CAAC;EACxE,MAAM,CAACsE,QAAQ,EAAEC,WAAW,CAAC,GAAGvE,QAAQ,CAAC;IACvCwE,iBAAiB,EAAE,EAAE;IACrBC,KAAK,EAAE,EAAE;IACTC,QAAQ,EAAE;EACZ,CAAC,CAAC;EAEFzE,SAAS,CAAC,MAAM;IACd,IAAIoD,IAAI,IAAID,UAAU,EAAE;MACtBuB,gBAAgB,CAAC,CAAC;IACpB;EACF,CAAC,EAAE,CAACtB,IAAI,EAAED,UAAU,CAAC,CAAC;EAEtB,MAAMuB,gBAAgB,GAAG,MAAAA,CAAA,KAAY;IACnC,IAAI;MACFhB,UAAU,CAAC,IAAI,CAAC;MAChBE,QAAQ,CAAC,IAAI,CAAC;MACd,MAAMe,IAAI,GAAG,MAAM7B,mBAAmB,CAAC8B,uBAAuB,CAACzB,UAAU,CAAC;MAC1EK,eAAe,CAACmB,IAAI,IAAI,EAAE,CAAC;;MAE3B;MACA,MAAME,0BAA0B,CAACF,IAAI,IAAI,EAAE,CAAC;IAC9C,CAAC,CAAC,OAAOG,GAAG,EAAE;MACZC,OAAO,CAACpB,KAAK,CAAC,0CAA0C,EAAEmB,GAAG,CAAC;MAC9DlB,QAAQ,CAAC,yCAAyC,CAAC;IACrD,CAAC,SAAS;MACRF,UAAU,CAAC,KAAK,CAAC;IACnB;EACF,CAAC;EAED,MAAMmB,0BAA0B,GAAG,MAAOG,gBAAgB,IAAK;IAC7D,IAAI;MACF,MAAMC,UAAU,GAAG,CAAC,CAAC;MAErB,KAAK,MAAMC,YAAY,IAAIF,gBAAgB,EAAE;QAC3C,IAAI;UACF,MAAMG,OAAO,GAAG,MAAMpC,cAAc,CAACqC,wBAAwB,CAACjC,UAAU,EAAE+B,YAAY,CAACX,iBAAiB,CAAC;UACzGU,UAAU,CAACC,YAAY,CAACG,eAAe,CAAC,GAAGF,OAAO,IAAI,EAAE;QAC1D,CAAC,CAAC,OAAOL,GAAG,EAAE;UACZC,OAAO,CAACpB,KAAK,CAAC,sCAAsCuB,YAAY,CAACX,iBAAiB,GAAG,EAAEO,GAAG,CAAC;UAC3FG,UAAU,CAACC,YAAY,CAACG,eAAe,CAAC,GAAG,EAAE;QAC/C;MACF;MAEAjB,yBAAyB,CAACa,UAAU,CAAC;IACvC,CAAC,CAAC,OAAOH,GAAG,EAAE;MACZC,OAAO,CAACpB,KAAK,CAAC,uCAAuC,EAAEmB,GAAG,CAAC;IAC7D;EACF,CAAC;EAED,MAAMQ,gBAAgB,GAAGA,CAACC,IAAI,EAAEL,YAAY,GAAG,IAAI,KAAK;IACtDlB,aAAa,CAACuB,IAAI,CAAC;IACnBrB,uBAAuB,CAACgB,YAAY,CAAC;IAErC,IAAIK,IAAI,KAAK,MAAM,IAAIL,YAAY,EAAE;MACnCZ,WAAW,CAAC;QACVC,iBAAiB,EAAEW,YAAY,CAACX,iBAAiB,IAAI,EAAE;QACvDC,KAAK,EAAEU,YAAY,CAACV,KAAK,IAAI,EAAE;QAC/BC,QAAQ,EAAES,YAAY,CAACT,QAAQ,IAAI;MACrC,CAAC,CAAC;IACJ,CAAC,MAAM;MACLH,WAAW,CAAC;QACVC,iBAAiB,EAAE,EAAE;QACrBC,KAAK,EAAE,EAAE;QACTC,QAAQ,EAAE;MACZ,CAAC,CAAC;IACJ;IAEAX,aAAa,CAAC,IAAI,CAAC;EACrB,CAAC;EAED,MAAM0B,iBAAiB,GAAGA,CAAA,KAAM;IAC9B1B,aAAa,CAAC,KAAK,CAAC;IACpBI,uBAAuB,CAAC,IAAI,CAAC;IAC7BN,QAAQ,CAAC,IAAI,CAAC;EAChB,CAAC;EAED,MAAM6B,YAAY,GAAG,MAAAA,CAAA,KAAY;IAC/B,IAAI;MACF7B,QAAQ,CAAC,IAAI,CAAC;;MAEd;MACA,IAAI,CAACS,QAAQ,CAACE,iBAAiB,CAACmB,IAAI,CAAC,CAAC,EAAE;QACtC9B,QAAQ,CAAC,yCAAyC,CAAC;QACnD;MACF;MAEA,IAAI,CAACS,QAAQ,CAACG,KAAK,IAAI,CAACH,QAAQ,CAACI,QAAQ,EAAE;QACzCb,QAAQ,CAAC,yDAAyD,CAAC;QACnE;MACF;MAEA,IAAIG,UAAU,KAAK,QAAQ,EAAE;QAC3B,MAAMjB,mBAAmB,CAAC6C,kBAAkB,CAACxC,UAAU,EAAEkB,QAAQ,CAAC;MACpE,CAAC,MAAM,IAAIN,UAAU,KAAK,MAAM,EAAE;QAChC,MAAMjB,mBAAmB,CAAC8C,kBAAkB,CAAC3B,oBAAoB,CAACoB,eAAe,EAAEhB,QAAQ,CAAC;MAC9F;MAEAmB,iBAAiB,CAAC,CAAC;MACnB,MAAMd,gBAAgB,CAAC,CAAC;IAC1B,CAAC,CAAC,OAAOI,GAAG,EAAE;MACZC,OAAO,CAACpB,KAAK,CAAC,yBAAyB,EAAEmB,GAAG,CAAC;MAC7ClB,QAAQ,CAACkB,GAAG,CAACe,MAAM,IAAI,yCAAyC,CAAC;IACnE;EACF,CAAC;EAED,MAAMC,YAAY,GAAG,MAAOC,cAAc,IAAK;IAC7C,IAAI,CAACC,MAAM,CAACC,OAAO,CAAC,oDAAoD,CAAC,EAAE;MACzE;IACF;IAEA,IAAI;MACF,MAAMnD,mBAAmB,CAACoD,kBAAkB,CAACH,cAAc,CAAC;MAC5D,MAAMrB,gBAAgB,CAAC,CAAC;IAC1B,CAAC,CAAC,OAAOI,GAAG,EAAE;MACZC,OAAO,CAACpB,KAAK,CAAC,4BAA4B,EAAEmB,GAAG,CAAC;MAChDlB,QAAQ,CAAC,4CAA4C,CAAC;IACxD;EACF,CAAC;EAED,MAAMuC,mBAAmB,GAAIC,IAAI,IAAK;IACpC,MAAMC,MAAM,GAAG;MACb,MAAM,EAAE,MAAM;MACd,uBAAuB,EAAE,gBAAgB;MACzC,qBAAqB,EAAE,cAAc;MACrC,gBAAgB,EAAE,gBAAgB;MAClC,SAAS,EAAE;IACb,CAAC;IACD,OAAOA,MAAM,CAACD,IAAI,CAAC,IAAIA,IAAI;EAC7B,CAAC;EAED,MAAME,oBAAoB,GAAIC,KAAK,IAAK;IACtC,MAAMC,MAAM,GAAG;MACb,QAAQ,EAAE,SAAS;MACnB,UAAU,EAAE,SAAS;MACrB,YAAY,EAAE,SAAS;MACvB,WAAW,EAAE;IACf,CAAC;IACD,OAAOA,MAAM,CAACD,KAAK,CAAC,IAAI,SAAS;EACnC,CAAC;EAED,oBACEtD,OAAA,CAACnC,MAAM;IAACsC,IAAI,EAAEA,IAAK;IAACC,OAAO,EAAEA,OAAQ;IAACoD,QAAQ,EAAC,IAAI;IAACC,SAAS;IAAAC,QAAA,gBAC3D1D,OAAA,CAAClC,WAAW;MAAA4F,QAAA,eACV1D,OAAA,CAAChD,GAAG;QAAC2G,OAAO,EAAC,MAAM;QAACC,UAAU,EAAC,QAAQ;QAACC,GAAG,EAAE,CAAE;QAAAH,QAAA,gBAC7C1D,OAAA,CAACZ,UAAU;UAAA0E,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,yBAEhB;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAK;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACK,CAAC,eAEdjE,OAAA,CAACjC,aAAa;MAAA2F,QAAA,eACZ1D,OAAA,CAAChD,GAAG;QAACkH,EAAE,EAAE;UAAEC,EAAE,EAAE;QAAE,CAAE;QAAAT,QAAA,GAChBhD,KAAK,iBACJV,OAAA,CAAC9B,KAAK;UAACkG,QAAQ,EAAC,OAAO;UAACF,EAAE,EAAE;YAAEG,EAAE,EAAE;UAAE,CAAE;UAAAX,QAAA,EACnChD;QAAK;UAAAoD,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACD,CACR,eAGDjE,OAAA,CAAChD,GAAG;UAAC2G,OAAO,EAAC,MAAM;UAACW,cAAc,EAAC,eAAe;UAACV,UAAU,EAAC,QAAQ;UAACS,EAAE,EAAE,CAAE;UAAAX,QAAA,gBAC3E1D,OAAA,CAAC7C,UAAU;YAACoH,OAAO,EAAC,IAAI;YAAAb,QAAA,EAAC;UAEzB;YAAAI,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAY,CAAC,eACbjE,OAAA,CAAC5C,MAAM;YACLmH,OAAO,EAAC,WAAW;YACnBC,SAAS,eAAExE,OAAA,CAAClB,OAAO;cAAAgF,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAE;YACvBQ,OAAO,EAAEA,CAAA,KAAMpC,gBAAgB,CAAC,QAAQ,CAAE;YAAAqB,QAAA,EAC3C;UAED;YAAAI,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACN,CAAC,EAELzD,OAAO,gBACNR,OAAA,CAAChD,GAAG;UAAC2G,OAAO,EAAC,MAAM;UAACW,cAAc,EAAC,QAAQ;UAACI,EAAE,EAAE,CAAE;UAAAhB,QAAA,eAChD1D,OAAA,CAAC7B,gBAAgB;YAAA2F,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACjB,CAAC,gBAENjE,OAAA,CAAChD,GAAG;UAAA0G,QAAA,EACDpD,YAAY,CAACqE,MAAM,KAAK,CAAC,gBACxB3E,OAAA,CAAChD,GAAG;YAAC4H,SAAS,EAAC,QAAQ;YAACF,EAAE,EAAE,CAAE;YAAAhB,QAAA,gBAC5B1D,OAAA,CAAC7C,UAAU;cAACoH,OAAO,EAAC,IAAI;cAACM,KAAK,EAAC,eAAe;cAAAnB,QAAA,EAAC;YAE/C;cAAAI,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAY,CAAC,eACbjE,OAAA,CAAC7C,UAAU;cAACoH,OAAO,EAAC,OAAO;cAACM,KAAK,EAAC,eAAe;cAAAnB,QAAA,EAAC;YAElD;cAAAI,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAY,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACV,CAAC,GAEN3D,YAAY,CAACwE,GAAG,CAAE7C,YAAY,iBAC5BjC,OAAA,CAAC1B,SAAS;YAAoC4F,EAAE,EAAE;cAAEG,EAAE,EAAE;YAAE,CAAE;YAAAX,QAAA,gBAC1D1D,OAAA,CAACzB,gBAAgB;cAACwG,UAAU,eAAE/E,OAAA,CAACN,cAAc;gBAAAoE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAE;cAAAP,QAAA,eAC/C1D,OAAA,CAAChD,GAAG;gBAAC2G,OAAO,EAAC,MAAM;gBAACC,UAAU,EAAC,QAAQ;gBAACU,cAAc,EAAC,eAAe;gBAACU,KAAK,EAAC,MAAM;gBAAAtB,QAAA,gBACjF1D,OAAA,CAAChD,GAAG;kBAAC2G,OAAO,EAAC,MAAM;kBAACC,UAAU,EAAC,QAAQ;kBAACC,GAAG,EAAE,CAAE;kBAAAH,QAAA,gBAC7C1D,OAAA,CAACZ,UAAU;oBAACyF,KAAK,EAAC;kBAAS;oBAAAf,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE,CAAC,eAC9BjE,OAAA,CAAChD,GAAG;oBAAA0G,QAAA,gBACF1D,OAAA,CAAC7C,UAAU;sBAACoH,OAAO,EAAC,IAAI;sBAAAb,QAAA,EACrBzB,YAAY,CAACX;oBAAiB;sBAAAwC,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACrB,CAAC,eACbjE,OAAA,CAAChD,GAAG;sBAAC2G,OAAO,EAAC,MAAM;sBAACE,GAAG,EAAE,CAAE;sBAACoB,EAAE,EAAE,GAAI;sBAAAvB,QAAA,GACjCzB,YAAY,CAACV,KAAK,iBACjBvB,OAAA,CAAChD,GAAG;wBAAC2G,OAAO,EAAC,MAAM;wBAACC,UAAU,EAAC,QAAQ;wBAACC,GAAG,EAAE,GAAI;wBAAAH,QAAA,gBAC/C1D,OAAA,CAACV,SAAS;0BAAC4F,QAAQ,EAAC,OAAO;0BAACL,KAAK,EAAC;wBAAQ;0BAAAf,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAE,CAAC,eAC7CjE,OAAA,CAAC7C,UAAU;0BAACoH,OAAO,EAAC,OAAO;0BAACM,KAAK,EAAC,eAAe;0BAAAnB,QAAA,EAC9CzB,YAAY,CAACV;wBAAK;0BAAAuC,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OACT,CAAC;sBAAA;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACV,CACN,EACAhC,YAAY,CAACT,QAAQ,iBACpBxB,OAAA,CAAChD,GAAG;wBAAC2G,OAAO,EAAC,MAAM;wBAACC,UAAU,EAAC,QAAQ;wBAACC,GAAG,EAAE,GAAI;wBAAAH,QAAA,gBAC/C1D,OAAA,CAACR,SAAS;0BAAC0F,QAAQ,EAAC,OAAO;0BAACL,KAAK,EAAC;wBAAQ;0BAAAf,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAE,CAAC,eAC7CjE,OAAA,CAAC7C,UAAU;0BAACoH,OAAO,EAAC,OAAO;0BAACM,KAAK,EAAC,eAAe;0BAAAnB,QAAA,EAC9CzB,YAAY,CAACT;wBAAQ;0BAAAsC,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OACZ,CAAC;sBAAA;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACV,CACN;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACE,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACH,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH,CAAC,eAENjE,OAAA,CAAChD,GAAG;kBAAC2G,OAAO,EAAC,MAAM;kBAACE,GAAG,EAAE,CAAE;kBAACY,OAAO,EAAGU,CAAC,IAAKA,CAAC,CAACC,eAAe,CAAC,CAAE;kBAAA1B,QAAA,gBAC9D1D,OAAA,CAAC3B,IAAI;oBACHgH,IAAI,eAAErF,OAAA,CAACJ,cAAc;sBAAAkE,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAE,CAAE;oBACzBqB,KAAK,EAAE,GAAG,CAACpE,sBAAsB,CAACe,YAAY,CAACG,eAAe,CAAC,IAAI,EAAE,EAAEuC,MAAM,UAAW;oBACxFY,IAAI,EAAC,OAAO;oBACZV,KAAK,EAAC,SAAS;oBACfN,OAAO,EAAC;kBAAU;oBAAAT,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACnB,CAAC,eACFjE,OAAA,CAAC5B,OAAO;oBAACoH,KAAK,EAAC,UAAU;oBAAA9B,QAAA,eACvB1D,OAAA,CAACpC,UAAU;sBACT2H,IAAI,EAAC,OAAO;sBACZd,OAAO,EAAEA,CAAA,KAAMpC,gBAAgB,CAAC,MAAM,EAAEJ,YAAY,CAAE;sBAAAyB,QAAA,eAEtD1D,OAAA,CAAChB,QAAQ;wBAAA8E,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAE;oBAAC;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACF;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACN,CAAC,eACVjE,OAAA,CAAC5B,OAAO;oBAACoH,KAAK,EAAC,SAAS;oBAAA9B,QAAA,eACtB1D,OAAA,CAACpC,UAAU;sBACT2H,IAAI,EAAC,OAAO;sBACZV,KAAK,EAAC,OAAO;sBACbJ,OAAO,EAAEA,CAAA,KAAM5B,YAAY,CAACZ,YAAY,CAACG,eAAe,CAAE;sBAAAsB,QAAA,eAE1D1D,OAAA,CAACd,UAAU;wBAAA4E,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAE;oBAAC;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACJ;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACN,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACP,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACU,CAAC,eAEnBjE,OAAA,CAACxB,gBAAgB;cAAAkF,QAAA,gBACf1D,OAAA,CAAC7C,UAAU;gBAACoH,OAAO,EAAC,WAAW;gBAACkB,YAAY;gBAAA/B,QAAA,EAAC;cAE7C;gBAAAI,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAY,CAAC,EAEZ,CAAC/C,sBAAsB,CAACe,YAAY,CAACG,eAAe,CAAC,IAAI,EAAE,EAAEuC,MAAM,KAAK,CAAC,gBACxE3E,OAAA,CAAC7C,UAAU;gBAACoH,OAAO,EAAC,OAAO;gBAACM,KAAK,EAAC,eAAe;gBAACa,KAAK,EAAE;kBAAEC,SAAS,EAAE;gBAAS,CAAE;gBAAAjC,QAAA,EAAC;cAElF;gBAAAI,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAY,CAAC,gBAEbjE,OAAA,CAACvB,IAAI;gBAACmH,KAAK;gBAAAlC,QAAA,EACR,CAACxC,sBAAsB,CAACe,YAAY,CAACG,eAAe,CAAC,IAAI,EAAE,EAAE0C,GAAG,CAAEe,OAAO,iBACxE7F,OAAA,CAACtB,QAAQ;kBAA8BoH,OAAO;kBAAApC,QAAA,eAC5C1D,OAAA,CAACrB,YAAY;oBACXoH,OAAO,eACL/F,OAAA,CAAChD,GAAG;sBAAC2G,OAAO,EAAC,MAAM;sBAACC,UAAU,EAAC,QAAQ;sBAACC,GAAG,EAAE,CAAE;sBAAAH,QAAA,gBAC7C1D,OAAA,CAAC7C,UAAU;wBAACoH,OAAO,EAAC,OAAO;wBAACyB,UAAU,EAAC,MAAM;wBAAAtC,QAAA,EAC1CmC,OAAO,CAACI;sBAAc;wBAAAnC,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACb,CAAC,eACbjE,OAAA,CAAC3B,IAAI;wBACHiH,KAAK,EAAEpC,mBAAmB,CAAC2C,OAAO,CAACK,YAAY,CAAE;wBACjDX,IAAI,EAAC,OAAO;wBACZhB,OAAO,EAAC;sBAAU;wBAAAT,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACnB,CAAC,eACFjE,OAAA,CAAC3B,IAAI;wBACHiH,KAAK,EAAEO,OAAO,CAACvC,KAAK,IAAI,QAAS;wBACjCiC,IAAI,EAAC,OAAO;wBACZV,KAAK,EAAExB,oBAAoB,CAACwC,OAAO,CAACvC,KAAK;sBAAE;wBAAAQ,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAC5C,CAAC;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACC,CACN;oBACDkC,SAAS,eACPnG,OAAA,CAAC7C,UAAU;sBAACoH,OAAO,EAAC,OAAO;sBAACM,KAAK,EAAC,eAAe;sBAAAnB,QAAA,GAC9CmC,OAAO,CAACO,WAAW,IAAI,qBAAqB,EAC5CP,OAAO,CAACQ,cAAc,IAAI,cAAc,IAAIC,IAAI,CAACT,OAAO,CAACQ,cAAc,CAAC,CAACE,kBAAkB,CAAC,CAAC,EAAE;oBAAA;sBAAAzC,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACtF;kBACb;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACF;gBAAC,GAzBW4B,OAAO,CAACI,cAAc;kBAAAnC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OA0B3B,CACX;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACE,CACP;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACe,CAAC;UAAA,GArGLhC,YAAY,CAACG,eAAe;YAAA0B,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAsGjC,CACZ;QACF;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACE,CACN;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACE;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACO,CAAC,eAEhBjE,OAAA,CAAChC,aAAa;MAAA0F,QAAA,eACZ1D,OAAA,CAAC5C,MAAM;QAACqH,OAAO,EAAErE,OAAQ;QAAAsD,QAAA,EAAC;MAE1B;QAAAI,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAQ;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACI,CAAC,eAGhBjE,OAAA,CAACnC,MAAM;MAACsC,IAAI,EAAES,UAAW;MAACR,OAAO,EAAEmC,iBAAkB;MAACiB,QAAQ,EAAC,IAAI;MAACC,SAAS;MAAAC,QAAA,gBAC3E1D,OAAA,CAAClC,WAAW;QAAA4F,QAAA,EACT5C,UAAU,KAAK,QAAQ,GAAG,oBAAoB,GAAG;MAAuB;QAAAgD,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC9D,CAAC,eACdjE,OAAA,CAACjC,aAAa;QAAA2F,QAAA,eACZ1D,OAAA,CAAChD,GAAG;UAACkH,EAAE,EAAE;YAAEC,EAAE,EAAE;UAAE,CAAE;UAAAT,QAAA,GAChBhD,KAAK,iBACJV,OAAA,CAAC9B,KAAK;YAACkG,QAAQ,EAAC,OAAO;YAACF,EAAE,EAAE;cAAEG,EAAE,EAAE;YAAE,CAAE;YAAAX,QAAA,EACnChD;UAAK;YAAAoD,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACD,CACR,eAEDjE,OAAA,CAAC/B,SAAS;YACRwF,SAAS;YACT6B,KAAK,EAAC,mBAAmB;YACzBkB,KAAK,EAAEpF,QAAQ,CAACE,iBAAkB;YAClCmF,QAAQ,EAAGtB,CAAC,IAAK9D,WAAW,CAAC;cAAE,GAAGD,QAAQ;cAAEE,iBAAiB,EAAE6D,CAAC,CAACuB,MAAM,CAACF;YAAM,CAAC,CAAE;YACjFG,MAAM,EAAC,QAAQ;YACfC,QAAQ;UAAA;YAAA9C,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACT,CAAC,eAEFjE,OAAA,CAAC/B,SAAS;YACRwF,SAAS;YACT6B,KAAK,EAAC,OAAO;YACbuB,IAAI,EAAC,OAAO;YACZL,KAAK,EAAEpF,QAAQ,CAACG,KAAM;YACtBkF,QAAQ,EAAGtB,CAAC,IAAK9D,WAAW,CAAC;cAAE,GAAGD,QAAQ;cAAEG,KAAK,EAAE4D,CAAC,CAACuB,MAAM,CAACF;YAAM,CAAC,CAAE;YACrEG,MAAM,EAAC,QAAQ;YACfG,UAAU,EAAC;UAAuD;YAAAhD,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACnE,CAAC,eAEFjE,OAAA,CAAC/B,SAAS;YACRwF,SAAS;YACT6B,KAAK,EAAC,UAAU;YAChBkB,KAAK,EAAEpF,QAAQ,CAACI,QAAS;YACzBiF,QAAQ,EAAGtB,CAAC,IAAK9D,WAAW,CAAC;cAAE,GAAGD,QAAQ;cAAEI,QAAQ,EAAE2D,CAAC,CAACuB,MAAM,CAACF;YAAM,CAAC,CAAE;YACxEG,MAAM,EAAC,QAAQ;YACfG,UAAU,EAAC;UAA+C;YAAAhD,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC3D,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACC;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACO,CAAC,eAChBjE,OAAA,CAAChC,aAAa;QAAA0F,QAAA,gBACZ1D,OAAA,CAAC5C,MAAM;UAACqH,OAAO,EAAElC,iBAAkB;UAAAmB,QAAA,EAAC;QAEpC;UAAAI,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC,eACTjE,OAAA,CAAC5C,MAAM;UAACqH,OAAO,EAAEjC,YAAa;UAAC+B,OAAO,EAAC,WAAW;UAAAb,QAAA,EAC/C5C,UAAU,KAAK,QAAQ,GAAG,MAAM,GAAG;QAAO;UAAAgD,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACrC,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACI,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACV,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CAAC;AAEb,CAAC;AAAC5D,EAAA,CA7WIJ,oBAAoB;AAAA8G,EAAA,GAApB9G,oBAAoB;AA+W1B,eAAeA,oBAAoB;AAAC,IAAA8G,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}