{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\PycharmProjects\\\\CMS\\\\webapp\\\\frontend\\\\src\\\\components\\\\cavi\\\\InserisciMetriForm.js\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect } from 'react';\nimport axios from 'axios';\nimport { Box, Paper, Typography, TextField, Button, Grid, FormControl, InputLabel, Select, MenuItem, Divider, Alert, CircularProgress, FormHelperText, IconButton, Chip, InputAdornment, Dialog, DialogTitle, DialogContent, DialogActions, Table, TableBody, TableCell, TableContainer, TableHead, TableRow, List, ListItem, ListItemButton, ListItemText, ListItemSecondaryAction } from '@mui/material';\nimport { Search as SearchIcon, Save as SaveIcon, Cancel as CancelIcon, Warning as WarningIcon, Info as InfoIcon, AddCircleOutline as AddCircleOutlineIcon } from '@mui/icons-material';\nimport { useNavigate } from 'react-router-dom';\nimport caviService from '../../services/caviService';\nimport axiosInstance from '../../services/axiosConfig';\nimport IncompatibleReelDialog from './IncompatibleReelDialog';\nimport CavoDetailsView from './CavoDetailsView';\nimport { CABLE_STATES, REEL_STATES, determineCableState, determineReelState, canModifyCable, isCableSpare, isCableInstalled, getCableStateColor, getReelStateColor } from '../../utils/stateUtils';\nimport parcoCaviService from '../../services/parcoCaviService';\nimport { redirectToVisualizzaCavi } from '../../utils/navigationUtils';\n\n/**\n * Componente per l'inserimento dei metri posati di un cavo\n * Versione semplificata con workflow compresso in un'unica pagina\n *\n * @param {Object} props - Proprietà del componente\n * @param {string} props.cantiereId - ID del cantiere\n * @param {Function} props.onSuccess - Funzione chiamata al successo dell'operazione\n * @param {Function} props.onError - Funzione chiamata in caso di errore\n */\nimport { jsxDEV as _jsxDEV, Fragment as _Fragment } from \"react/jsx-dev-runtime\";\nconst InserisciMetriForm = ({\n  cantiereId,\n  onSuccess,\n  onError\n}) => {\n  _s();\n  const navigate = useNavigate();\n\n  // Stati per la gestione del form\n  const [loading, setLoading] = useState(false);\n  const [caviLoading, setCaviLoading] = useState(false);\n  const [bobineLoading, setBobineLoading] = useState(false);\n  const [searchResults, setSearchResults] = useState([]);\n  const [showSearchResults, setShowSearchResults] = useState(false);\n\n  // Stati per i dati\n  const [cavi, setCavi] = useState([]);\n  const [bobine, setBobine] = useState([]);\n  const [selectedCavo, setSelectedCavo] = useState(null);\n  const [cavoIdInput, setCavoIdInput] = useState('');\n\n  // Stato per la gestione dei passi (mantenuto per compatibilità con funzioni esistenti)\n  const [activeStep, setActiveStep] = useState(0);\n\n  // Stati per il form\n  const [formData, setFormData] = useState({\n    id_cavo: '',\n    metri_posati: '',\n    id_bobina: ''\n  });\n\n  // Stati per la validazione\n  const [formErrors, setFormErrors] = useState({});\n  const [formWarnings, setFormWarnings] = useState({});\n\n  // Stati per i dialoghi speciali\n  const [showIncompatibleReelDialog, setShowIncompatibleReelDialog] = useState(false);\n  const [incompatibleReel, setIncompatibleReel] = useState(null); // Mantenuto per compatibilità\n  const [incompatibleReelData, setIncompatibleReelData] = useState({\n    cavo: null,\n    bobina: null\n  });\n  const [showAlreadyLaidDialog, setShowAlreadyLaidDialog] = useState(false);\n\n  // Stati per i filtri delle bobine\n  const [tipologiaFilter, setTipologiaFilter] = useState('');\n  const [searchText, setSearchText] = useState('');\n  const [alreadyLaidCavo, setAlreadyLaidCavo] = useState(null);\n  const [showCavoDetailsDialog, setShowCavoDetailsDialog] = useState(false);\n\n  // Gestisce la selezione di una bobina\n  const handleSelectBobina = idBobina => {\n    setFormData({\n      ...formData,\n      id_bobina: idBobina\n    });\n\n    // Reset degli errori\n    setFormErrors(prev => ({\n      ...prev,\n      id_bobina: null,\n      id_bobina_input: null\n    }));\n  };\n\n  // Gestisce il cambio del filtro tipologia\n  const handleTipologiaFilterChange = event => {\n    setTipologiaFilter(event.target.value);\n  };\n\n  // Gestisce il cambio del testo di ricerca\n  const handleSearchTextChange = event => {\n    setSearchText(event.target.value);\n  };\n\n  // Carica la lista delle bobine all'avvio\n  useEffect(() => {\n    loadBobine();\n  }, [cantiereId]);\n\n  // Funzione per estrarre il numero della bobina dall'ID completo\n  const getBobinaNumber = idBobina => {\n    // L'ID completo è nel formato C{id_cantiere}_B{numero_bobina}\n    if (idBobina && idBobina.includes('_B')) {\n      return idBobina.split('_B')[1];\n    }\n    return idBobina;\n  };\n\n  // Funzione per caricare i cavi\n  const loadCavi = async () => {\n    try {\n      setCaviLoading(true);\n      console.log(`Caricamento cavi per il cantiere ${cantiereId}...`);\n\n      // Carica tutti i cavi, inclusi quelli SPARE e installati\n      try {\n        const caviData = await caviService.getCavi(cantiereId);\n        console.log(`Caricati ${caviData.length} cavi`);\n\n        // Mostra tutti i cavi (da installare, in corso, installati e SPARE)\n        // Questo permette di vedere anche i cavi già installati per eventuale modifica della bobina\n        setCavi(caviData);\n      } catch (loadError) {\n        console.error('Errore nel primo tentativo di caricamento dei cavi:', loadError);\n\n        // Se è un errore di rete, prova un secondo tentativo dopo una breve pausa\n        if (loadError.isNetworkError || !loadError.response || loadError.code === 'ECONNABORTED' || loadError.message && loadError.message.includes('Network Error')) {\n          console.log('Errore di rete, tentativo di recupero dopo 1 secondo...');\n          await new Promise(resolve => setTimeout(resolve, 1000));\n          try {\n            // Secondo tentativo con timeout aumentato\n            const token = localStorage.getItem('token');\n            const API_URL = axiosInstance.defaults.baseURL;\n\n            // Usa l'ID cantiere originale per la richiesta alternativa\n            const retryResponse = await axios.get(`${API_URL}/cavi/${cantiereId}`, {\n              headers: {\n                'Authorization': `Bearer ${token}`\n              },\n              timeout: 30000 // 30 secondi\n            });\n            console.log(`Recupero riuscito al secondo tentativo: ${retryResponse.data.length} cavi`);\n            setCavi(retryResponse.data);\n          } catch (retryError) {\n            console.error('Anche il secondo tentativo è fallito:', retryError);\n            throw retryError;\n          }\n        } else {\n          throw loadError;\n        }\n      }\n    } catch (error) {\n      console.error('Errore nel caricamento dei cavi:', error);\n\n      // Messaggio di errore più dettagliato\n      let errorMessage = 'Errore nel caricamento dei cavi';\n      if (error.isNetworkError) {\n        errorMessage = 'Errore di connessione al server. Verifica la rete e riprova.';\n      } else if (error.detail) {\n        errorMessage = error.detail;\n      } else if (error.message) {\n        errorMessage = error.message;\n      }\n      onError(errorMessage);\n    } finally {\n      setCaviLoading(false);\n    }\n  };\n\n  // Funzione per caricare le bobine\n  const loadBobine = async () => {\n    try {\n      setBobineLoading(true);\n      console.log('Caricamento bobine iniziato...');\n\n      // Carica tutte le bobine disponibili\n      const bobineData = await parcoCaviService.getBobine(cantiereId);\n      console.log(`Bobine caricate: ${bobineData.length}`);\n\n      // Filtra solo per stato (disponibile o in uso) e metri residui > 0\n      const bobineUtilizzabili = bobineData.filter(bobina => bobina.stato_bobina !== 'Terminata' && bobina.stato_bobina !== 'Over' && bobina.metri_residui > 0);\n      console.log(`Bobine utilizzabili: ${bobineUtilizzabili.length}`);\n\n      // Se c'è un cavo selezionato, evidenzia le bobine compatibili ma mostra tutte\n      if (selectedCavo && selectedCavo.tipologia && selectedCavo.sezione) {\n        console.log('Cavo selezionato, evidenziando bobine compatibili...');\n        console.log('Dati cavo:', {\n          id_cavo: selectedCavo.id_cavo,\n          tipologia: selectedCavo.tipologia,\n          sezione: selectedCavo.sezione\n        });\n\n        // Ordina le bobine: prima le compatibili, poi le altre, tutte ordinate per metri residui\n        const cavoTipologia = String(selectedCavo.tipologia || '').trim().toLowerCase();\n        const cavoSezione = String(selectedCavo.sezione || '0').trim();\n\n        // Identifica le bobine compatibili\n        const bobineCompatibili = [];\n        const bobineNonCompatibili = [];\n\n        // Dividi le bobine in compatibili e non compatibili\n        bobineUtilizzabili.forEach(bobina => {\n          const bobinaTipologia = String(bobina.tipologia || '').trim().toLowerCase();\n          const bobinaSezione = String(bobina.sezione || '0').trim();\n\n          // Verifica compatibilità\n          const tipologiaMatch = bobinaTipologia === cavoTipologia;\n          const sezioneMatch = bobinaSezione === cavoSezione;\n          const isCompatible = tipologiaMatch && sezioneMatch;\n          console.log(`Verifica compatibilità bobina ${bobina.id_bobina}:`, {\n            'Tipologia bobina': `\"${bobina.tipologia}\"`,\n            'Tipologia cavo': `\"${selectedCavo.tipologia}\"`,\n            'Tipologie uguali?': tipologiaMatch,\n            'Sezione bobina': `\"${String(bobina.sezione)}\"`,\n            'Sezione cavo': `\"${String(selectedCavo.sezione)}\"`,\n            'Sezioni uguali?': sezioneMatch,\n            'Stato bobina': bobina.stato_bobina,\n            'Metri residui': bobina.metri_residui,\n            'Compatibile?': isCompatible\n          });\n          if (isCompatible) {\n            bobineCompatibili.push(bobina);\n          } else {\n            bobineNonCompatibili.push(bobina);\n          }\n        });\n        console.log(`Bobine compatibili trovate: ${bobineCompatibili.length}`);\n        console.log(`Bobine non compatibili: ${bobineNonCompatibili.length}`);\n\n        // Log dettagliato delle bobine compatibili\n        if (bobineCompatibili.length > 0) {\n          console.log('Dettaglio bobine compatibili:');\n          bobineCompatibili.forEach(bobina => {\n            console.log(`- ${bobina.id_bobina}: ${bobina.tipologia} / ${bobina.sezione} (${bobina.metri_residui}m)`);\n          });\n        } else {\n          console.log('ATTENZIONE: Nessuna bobina compatibile trovata!');\n        }\n\n        // Ordina entrambi gli array per metri residui (decrescente)\n        bobineCompatibili.sort((a, b) => b.metri_residui - a.metri_residui);\n        bobineNonCompatibili.sort((a, b) => b.metri_residui - a.metri_residui);\n\n        // Concatena gli array: prima le compatibili, poi le non compatibili\n        const bobineOrdinate = [...bobineCompatibili, ...bobineNonCompatibili];\n\n        // Imposta le bobine nel componente\n        setBobine(bobineOrdinate);\n      } else {\n        // Se non c'è un cavo selezionato, mostra tutte le bobine ordinate per metri residui\n        console.log('Nessun cavo selezionato, mostrando tutte le bobine...');\n        bobineUtilizzabili.sort((a, b) => b.metri_residui - a.metri_residui);\n        setBobine(bobineUtilizzabili);\n      }\n    } catch (error) {\n      console.error('Errore nel caricamento delle bobine:', error);\n      onError('Errore nel caricamento delle bobine: ' + (error.message || 'Errore sconosciuto'));\n    } finally {\n      setBobineLoading(false);\n    }\n  };\n\n  // Gestisce la ricerca di un cavo per ID o pattern\n  const handleSearchCavoById = async () => {\n    if (!cavoIdInput.trim()) {\n      onError('Inserisci un ID cavo valido');\n      return;\n    }\n    try {\n      setCaviLoading(true);\n      console.log(`Ricerca cavo con pattern: ${cavoIdInput.trim()} nel cantiere ${cantiereId}`);\n\n      // Cerca tutti i cavi che corrispondono al pattern\n      const caviData = await caviService.getCavi(cantiereId);\n\n      // Filtra i cavi in base all'input (ricerca parziale)\n      const filteredCavi = caviData.filter(cavo => cavo.id_cavo.toLowerCase().includes(cavoIdInput.trim().toLowerCase()));\n      console.log(`Trovati ${filteredCavi.length} cavi corrispondenti al pattern`);\n\n      // Se c'è una corrispondenza esatta, seleziona direttamente quel cavo\n      const exactMatch = filteredCavi.find(cavo => cavo.id_cavo.toLowerCase() === cavoIdInput.trim().toLowerCase());\n      if (exactMatch) {\n        console.log('Trovata corrispondenza esatta:', exactMatch);\n\n        // Verifica se il cavo è già installato\n        if (exactMatch.stato_installazione === 'Installato' || exactMatch.metratura_reale && exactMatch.metratura_reale > 0) {\n          console.log('Cavo già installato, mostra dialogo:', exactMatch);\n          setAlreadyLaidCavo(exactMatch);\n          setShowAlreadyLaidDialog(true);\n          setCaviLoading(false);\n          return;\n        }\n\n        // Verifica se il cavo è SPARE\n        if (exactMatch.modificato_manualmente === 3) {\n          console.log('Cavo SPARE trovato:', exactMatch);\n          // Gestione del cavo SPARE - verrà gestito in handleCavoSelect\n        }\n\n        // Seleziona il cavo direttamente\n        handleCavoSelect(exactMatch);\n      } else if (filteredCavi.length > 0) {\n        // Mostra i risultati della ricerca in una tabella\n        setSearchResults(filteredCavi);\n        setShowSearchResults(true);\n      } else {\n        // Nessun cavo trovato\n        onError(`Nessun cavo trovato con pattern \"${cavoIdInput.trim()}\" nel cantiere ${cantiereId}`);\n      }\n    } catch (error) {\n      console.error('Errore nella ricerca del cavo:', error);\n\n      // Messaggio di errore più dettagliato\n      let errorMessage = 'Errore nella ricerca dei cavi';\n      if (error.isNetworkError) {\n        errorMessage = 'Errore di connessione al server. Verifica la rete e riprova.';\n      } else if (error.status === 404) {\n        errorMessage = `Cavo con ID \"${cavoIdInput.trim()}\" non trovato nel cantiere ${cantiereId}`;\n      } else if (error.detail) {\n        errorMessage = error.detail;\n      } else if (error.message) {\n        errorMessage = error.message;\n      }\n      onError(errorMessage);\n    } finally {\n      setCaviLoading(false);\n    }\n  };\n\n  // Gestisce la selezione di un cavo\n  const handleCavoSelect = cavo => {\n    // Verifica se il cavo è già installato\n    if (cavo.stato_installazione === 'Installato' || cavo.metratura_reale && cavo.metratura_reale > 0) {\n      // Mostra il dialogo per cavi già posati\n      setAlreadyLaidCavo(cavo);\n      setShowAlreadyLaidDialog(true);\n      return;\n    }\n    // Verifica se il cavo è SPARE (modificato_manualmente = 3)\n    else if (cavo.modificato_manualmente === 3) {\n      // Chiedi conferma all'utente per riattivare il cavo SPARE\n      if (window.confirm(`Il cavo ${cavo.id_cavo} è marcato come SPARE. Vuoi riattivarlo?`)) {\n        // Riattiva il cavo SPARE (imposta modificato_manualmente = 0)\n        reactivateSpare(cavo.id_cavo).then(() => {\n          // Aggiorna il cavo selezionato con modificato_manualmente = 0\n          const updatedCavo = {\n            ...cavo,\n            modificato_manualmente: 0\n          };\n          setSelectedCavo(updatedCavo);\n          setFormData({\n            ...formData,\n            id_cavo: updatedCavo.id_cavo,\n            metri_posati: ''\n          });\n          // Nascondi i risultati della ricerca\n          setShowSearchResults(false);\n\n          // Carica le bobine compatibili\n          loadBobine();\n        }).catch(error => {\n          console.error('Errore durante la riattivazione del cavo SPARE:', error);\n          onError('Errore durante la riattivazione del cavo SPARE: ' + (error.message || 'Errore sconosciuto'));\n        });\n      } else {\n        // L'utente ha annullato la riattivazione\n        return;\n      }\n    } else {\n      // Cavo normale (non SPARE e non installato)\n      setSelectedCavo(cavo);\n      setFormData({\n        ...formData,\n        id_cavo: cavo.id_cavo,\n        metri_posati: ''\n      });\n      // Nascondi i risultati della ricerca\n      setShowSearchResults(false);\n\n      // Carica le bobine compatibili\n      if (cavo.tipologia && cavo.n_conduttori && cavo.sezione) {\n        console.log(`Caricamento bobine compatibili per il cavo ${cavo.id_cavo}...`);\n        loadBobine();\n      }\n    }\n  };\n\n  // Funzione per riattivare un cavo SPARE\n  const reactivateSpare = async cavoId => {\n    try {\n      // Chiamata API per riattivare il cavo SPARE\n      await caviService.reactivateSpare(cantiereId, cavoId);\n      onSuccess(`Cavo ${cavoId} riattivato con successo`);\n      return true;\n    } catch (error) {\n      console.error('Errore durante la riattivazione del cavo SPARE:', error);\n      onError('Errore durante la riattivazione del cavo SPARE: ' + (error.message || 'Errore sconosciuto'));\n      throw error;\n    }\n  };\n\n  // Gestisce il cambio dei valori nel form\n  const handleFormChange = e => {\n    const {\n      name,\n      value\n    } = e.target;\n\n    // Gestione speciale per il cambio di bobina\n    if (name === 'id_bobina' && value && value !== 'BOBINA_VUOTA') {\n      // Verifica compatibilità tra cavo e bobina\n      const bobina = bobine.find(b => b.id_bobina === value);\n      if (bobina && selectedCavo) {\n        // Nella nuova configurazione, controlliamo solo tipologia e formazione (sezione)\n        // Utilizziamo una comparazione più robusta con trim() e gestione di null/undefined\n        const isCompatible = String(bobina.tipologia || '').trim() === String(selectedCavo.tipologia || '').trim() && String(bobina.sezione || '0').trim() === String(selectedCavo.sezione || '0').trim();\n        if (!isCompatible) {\n          console.log('Bobina incompatibile selezionata:', bobina);\n          console.log('Cavo corrente:', selectedCavo);\n          console.log('Confronto formazione:', {\n            cavoFormazione: String(selectedCavo.sezione || '0'),\n            bobinaFormazione: String(bobina.sezione || '0')\n          });\n\n          // Mostra il dialogo di incompatibilità\n          setIncompatibleReelData({\n            cavo: selectedCavo,\n            bobina: bobina\n          });\n          setShowIncompatibleReelDialog(true);\n\n          // Non aggiornare il valore del form finché l'utente non conferma\n          return;\n        }\n      }\n    }\n    setFormData({\n      ...formData,\n      [name]: value\n    });\n\n    // Validazione in tempo reale\n    validateField(name, value);\n  };\n\n  // Validazione di un campo\n  const validateField = (name, value) => {\n    let error = null;\n    let warning = null;\n    if (name === 'metri_posati') {\n      // Controllo input vuoto\n      if (!value || value.trim() === '') {\n        error = 'Inserire un valore per i metri posati';\n        return false;\n      }\n\n      // Controllo formato numerico\n      if (isNaN(parseFloat(value)) || parseFloat(value) <= 0) {\n        error = 'Inserire un valore numerico positivo';\n        return false;\n      }\n      const metriPosati = parseFloat(value);\n\n      // Controllo metri teorici cavo\n      if (selectedCavo && selectedCavo.metri_teorici && metriPosati > parseFloat(selectedCavo.metri_teorici)) {\n        warning = `I metri posati (${metriPosati}m) superano i metri teorici del cavo (${selectedCavo.metri_teorici}m)`;\n      }\n\n      // Controllo metri residui bobina (se bobina reale)\n      if (formData.id_bobina && formData.id_bobina !== 'BOBINA_VUOTA') {\n        const bobina = bobine.find(b => b.id_bobina === formData.id_bobina);\n        if (bobina && metriPosati > parseFloat(bobina.metri_residui)) {\n          warning = `I metri posati (${metriPosati}m) superano i metri residui della bobina (${bobina.metri_residui}m). La bobina andrà in stato OVER.`;\n        }\n      }\n    } else if (name === 'id_bobina') {\n      // Controllo che sia selezionata una bobina\n      if (!value || value.trim() === '') {\n        error = 'È necessario selezionare una bobina';\n        return false;\n      }\n    }\n\n    // Aggiorna gli errori\n    setFormErrors(prev => ({\n      ...prev,\n      [name]: error\n    }));\n\n    // Aggiorna gli avvisi\n    setFormWarnings(prev => ({\n      ...prev,\n      [name]: warning\n    }));\n    return !error;\n  };\n\n  // Stati per i dialoghi di conferma\n  const [notificationShown, setNotificationShown] = useState(false);\n  const [showConfirmDialog, setShowConfirmDialog] = useState(false);\n  const [confirmDialogProps, setConfirmDialogProps] = useState({\n    title: '',\n    message: '',\n    onConfirm: () => {}\n  });\n\n  // Nota: Lo stato per il dialogo di bobina incompatibile è già dichiarato sopra\n\n  // Validazione completa del form\n  const validateForm = () => {\n    let isValid = true;\n    const errors = {};\n    const warnings = {};\n\n    // Reset della variabile di notifica\n    setNotificationShown(false);\n\n    // Validazione metri posati\n    if (!formData.metri_posati || formData.metri_posati.trim() === '') {\n      errors.metri_posati = 'Inserire un valore per i metri posati';\n      isValid = false;\n    } else if (isNaN(parseFloat(formData.metri_posati)) || parseFloat(formData.metri_posati) <= 0) {\n      errors.metri_posati = 'Inserire un valore numerico positivo';\n      isValid = false;\n    }\n\n    // Validazione bobina (deve essere selezionata)\n    if (!formData.id_bobina || formData.id_bobina.trim() === '') {\n      errors.id_bobina = 'È necessario selezionare una bobina';\n      isValid = false;\n    }\n    if (isValid) {\n      const metriPosati = parseFloat(formData.metri_posati);\n\n      // Controllo metri teorici cavo\n      if (selectedCavo && selectedCavo.metri_teorici && metriPosati > parseFloat(selectedCavo.metri_teorici)) {\n        warnings.metri_posati = `I metri posati (${metriPosati}m) superano i metri teorici del cavo (${selectedCavo.metri_teorici}m)`;\n        setNotificationShown(true); // Imposta la variabile per evitare notifiche duplicate\n        // Non mostrare più il popup di conferma, solo l'avviso nel form\n        // Continua con la validazione senza interrompere il flusso\n      }\n\n      // Controllo metri residui bobina (se bobina reale)\n      if (isValid && formData.id_bobina && formData.id_bobina !== 'BOBINA_VUOTA') {\n        const bobina = bobine.find(b => b.id_bobina === formData.id_bobina);\n        if (bobina && metriPosati > parseFloat(bobina.metri_residui)) {\n          warnings.metri_posati = `I metri posati (${metriPosati}m) superano i metri residui della bobina (${bobina.metri_residui}m). La bobina andrà in stato OVER.`;\n          setNotificationShown(true); // Imposta la variabile per evitare notifiche duplicate\n\n          // Mostra il dialogo di conferma invece di window.confirm\n          setConfirmDialogProps({\n            title: 'Attenzione: Bobina in stato OVER',\n            message: `I metri posati (${metriPosati}m) superano i metri residui della bobina (${bobina.metri_residui}m). Questo porterà la bobina in stato OVER. Vuoi continuare?`,\n            onConfirm: () => {\n              // Continua con la validazione\n              handleNext();\n            }\n          });\n          setShowConfirmDialog(true);\n          return false; // Interrompi la validazione fino alla conferma dell'utente\n        }\n      }\n    }\n    setFormErrors(errors);\n    setFormWarnings(warnings);\n    return isValid;\n  };\n\n  // Gestisce il passaggio al passo successivo\n  const handleNext = () => {\n    if (activeStep === 2) {\n      // Validazione prima di passare al passo successivo (da Inserisci Metri a Conferma)\n      if (!validateForm()) {\n        return;\n      }\n    } else if (activeStep === 1) {\n      // Carica le bobine prima di passare al passo successivo (da Associa Bobina a Inserisci Metri)\n      loadBobine();\n    }\n    setActiveStep(prevActiveStep => prevActiveStep + 1);\n  };\n\n  // Gestisce il ritorno al passo precedente\n  const handleBack = () => {\n    setActiveStep(prevActiveStep => prevActiveStep - 1);\n  };\n\n  // Gestisce il reset del form\n  const handleReset = () => {\n    setActiveStep(0);\n    setSelectedCavo(null);\n    setCavoIdInput('');\n    setFormData({\n      id_cavo: '',\n      metri_posati: '',\n      id_bobina: ''\n    });\n    setFormErrors({});\n    setFormWarnings({});\n  };\n\n  // Determina lo stato di installazione in base ai metri posati\n  // Utilizziamo la funzione dalla utility stateUtils\n  const determineInstallationStatus = (metriPosati, metriTeorici) => {\n    return determineCableState(metriPosati, metriTeorici);\n  };\n\n  // Gestisce l'invio del form\n  const handleSubmit = async () => {\n    // Dichiarazione delle variabili al di fuori del blocco try/catch\n    // in modo che siano accessibili anche nel blocco catch\n    let idBobina;\n    let statoInstallazione;\n    let metriPosati;\n    let forceOver = false;\n    try {\n      setLoading(true);\n\n      // Validazione finale\n      if (!validateForm()) {\n        setLoading(false);\n        return;\n      }\n\n      // Prepara i dati da inviare\n      metriPosati = parseFloat(formData.metri_posati);\n\n      // Gestione speciale per BOBINA_VUOTA\n      idBobina = formData.id_bobina;\n\n      // Gestione differenziata per cavi non posati e cavi posati\n      if (!idBobina || idBobina === '') {\n        // È necessario selezionare una bobina, anche BOBINA_VUOTA\n        setFormErrors({\n          ...formErrors,\n          id_bobina: 'È necessario selezionare una bobina'\n        });\n        setLoading(false);\n        return;\n      } else if (idBobina === 'BOBINA_VUOTA') {\n        // Per cavi posati senza bobina specifica\n        console.log('Usando BOBINA_VUOTA per il cavo');\n        // Assicurati che BOBINA_VUOTA venga passato come stringa e non come null\n        idBobina = 'BOBINA_VUOTA';\n      } else {\n        // Per cavi posati con bobina specifica\n        console.log(`Usando bobina reale: ${idBobina}`);\n      }\n\n      // Determina lo stato di installazione\n      statoInstallazione = determineInstallationStatus(metriPosati, selectedCavo.metri_teorici);\n\n      // Imposta sempre forceOver a true per evitare blocchi quando la bobina va in OVER\n      forceOver = true;\n      console.log('Impostando forceOver a true per garantire il completamento dell\\'operazione');\n\n      // Log delle condizioni che richiederebbero forceOver\n      if (idBobina === 'BOBINA_VUOTA') {\n        console.log('Operazione con BOBINA_VUOTA');\n      }\n      // Per bobine reali, verifica i metri residui\n      else if (idBobina && idBobina !== 'BOBINA_VUOTA') {\n        const bobina = bobine.find(b => b.id_bobina === idBobina);\n        if (bobina && metriPosati > parseFloat(bobina.metri_residui)) {\n          console.log(`La bobina ${idBobina} andrà in stato OVER (metri posati ${metriPosati} > metri residui ${bobina.metri_residui})`);\n        }\n      }\n\n      // Verifica anche se i metri posati superano i metri teorici\n      if (selectedCavo && selectedCavo.metri_teorici && metriPosati > parseFloat(selectedCavo.metri_teorici)) {\n        console.log(`I metri posati (${metriPosati}) superano i metri teorici (${selectedCavo.metri_teorici})`);\n      }\n\n      // Log per debug\n      console.log('Invio dati:', {\n        cantiereId,\n        cavoId: formData.id_cavo,\n        metriPosati,\n        idBobina,\n        forceOver,\n        statoInstallazione\n      });\n\n      // Conferma finale prima dell'invio solo se non è già stata mostrata una notifica\n      if (!notificationShown) {\n        const confirmMessage = `Confermi l'aggiornamento del cavo ${formData.id_cavo} con ${metriPosati}m posati?`;\n\n        // Usa il dialogo di conferma invece di window.confirm\n        setConfirmDialogProps({\n          title: 'Conferma aggiornamento',\n          message: confirmMessage,\n          onConfirm: async () => {\n            // Esegui la chiamata API qui invece che nel flusso principale\n            try {\n              setLoading(true);\n\n              // Imposta sempre forceOver a true per evitare blocchi quando la bobina va in OVER\n              console.log('Impostando forceOver a true nel dialogo di conferma per garantire il completamento dell\\'operazione');\n              await caviService.updateMetriPosati(cantiereId, formData.id_cavo, metriPosati, idBobina, true // Forza sempre a true per evitare blocchi\n              );\n\n              // Messaggio di successo con dettagli sulla bobina\n              let successMessage = `Metri posati aggiornati con successo. Stato cavo: ${statoInstallazione}`;\n              if (idBobina === 'BOBINA_VUOTA') {\n                successMessage += '. Cavo associato a BOBINA VUOTA';\n              } else if (idBobina) {\n                const bobina = bobine.find(b => b.id_bobina === idBobina);\n                if (bobina) {\n                  successMessage += `. Cavo associato alla bobina ${idBobina}`;\n                }\n              }\n\n              // Gestione successo\n              onSuccess(successMessage);\n\n              // Reset del form\n              handleReset();\n\n              // Ricarica i cavi\n              loadCavi();\n            } catch (error) {\n              console.error('Errore durante l\\'aggiornamento dei metri posati:', error);\n\n              // Gestione speciale per BOBINA_VUOTA\n              if (idBobina === 'BOBINA_VUOTA' && error.success) {\n                // Se è un \"errore\" di successo per BOBINA_VUOTA, trattiamolo come un successo\n                let successMessage = `Metri posati aggiornati con successo. Stato cavo: ${statoInstallazione}. Cavo associato a BOBINA VUOTA`;\n                onSuccess(successMessage);\n\n                // Reset del form\n                handleReset();\n\n                // Ricarica i cavi\n                loadCavi();\n                return;\n              }\n\n              // Gestione dettagliata degli errori\n              let errorMessage = 'Errore durante l\\'aggiornamento dei metri posati';\n              if (error.response) {\n                var _error$response$data;\n                // Il server ha risposto con un codice di errore\n                const status = error.response.status;\n                const detail = ((_error$response$data = error.response.data) === null || _error$response$data === void 0 ? void 0 : _error$response$data.detail) || error.message;\n                if (status === 400) {\n                  // Errore di validazione\n                  if (detail.includes('metri residui')) {\n                    errorMessage = `La bobina non ha metri residui sufficienti. Usa l'opzione \"BOBINA VUOTA\" o seleziona un'altra bobina.`;\n                  } else if (detail.includes('già posato')) {\n                    errorMessage = `Il cavo risulta già posato. Usa la funzione \"Modifica bobina cavo posato\".`;\n                  } else {\n                    errorMessage = detail;\n                  }\n                } else if (status === 404) {\n                  // Gestione speciale per BOBINA_VUOTA quando la bobina non viene trovata\n                  if (idBobina === 'BOBINA_VUOTA' && detail.includes('non trovata')) {\n                    let successMessage = `Metri posati aggiornati con successo. Stato cavo: ${statoInstallazione}. Cavo associato a BOBINA VUOTA`;\n                    onSuccess(successMessage);\n\n                    // Reset del form\n                    handleReset();\n\n                    // Ricarica i cavi\n                    loadCavi();\n                    return;\n                  } else {\n                    errorMessage = `Cavo o bobina non trovati: ${detail}`;\n                  }\n                } else {\n                  errorMessage = `Errore del server (${status}): ${detail}`;\n                }\n              } else if (error.request) {\n                // La richiesta è stata inviata ma non è stata ricevuta risposta\n                errorMessage = 'Nessuna risposta dal server. Verifica la connessione di rete.';\n              } else if (error.detail && idBobina === 'BOBINA_VUOTA') {\n                // Gestione speciale per errori con BOBINA_VUOTA\n                errorMessage = error.detail;\n                if (error.status === 200 || error.success) {\n                  let successMessage = `Metri posati aggiornati con successo. Stato cavo: ${statoInstallazione}. Cavo associato a BOBINA VUOTA`;\n                  onSuccess(successMessage);\n\n                  // Reset del form\n                  handleReset();\n\n                  // Ricarica i cavi\n                  loadCavi();\n                  return;\n                }\n              } else {\n                // Errore durante la configurazione della richiesta\n                errorMessage = error.message || error.detail || 'Errore sconosciuto';\n              }\n              onError(errorMessage);\n            } finally {\n              setLoading(false);\n            }\n          }\n        });\n        setShowConfirmDialog(true);\n        setLoading(false);\n        return;\n      }\n\n      // Chiamata API\n      console.log('Invio richiesta updateMetriPosati con i seguenti parametri:');\n      console.log('- cantiereId:', cantiereId);\n      console.log('- id_cavo:', formData.id_cavo);\n      console.log('- metri_posati:', metriPosati);\n      console.log('- id_bobina:', idBobina, typeof idBobina);\n      console.log('- forceOver:', forceOver);\n\n      // Imposta sempre forceOver a true per evitare blocchi quando la bobina va in OVER\n      console.log('Impostando forceOver a true nella chiamata diretta per garantire il completamento dell\\'operazione');\n      await caviService.updateMetriPosati(cantiereId, formData.id_cavo, metriPosati, idBobina, true // Forza sempre a true per evitare blocchi\n      );\n\n      // Messaggio di successo con dettagli sulla bobina\n      let successMessage = `Metri posati aggiornati con successo. Stato cavo: ${statoInstallazione}`;\n      if (idBobina === 'BOBINA_VUOTA') {\n        successMessage += '. Cavo associato a BOBINA VUOTA';\n      } else if (idBobina) {\n        const bobina = bobine.find(b => b.id_bobina === idBobina);\n        if (bobina) {\n          successMessage += `. Cavo associato alla bobina ${idBobina}`;\n        }\n      }\n\n      // Gestione successo\n      onSuccess(successMessage);\n\n      // Reset del form\n      handleReset();\n\n      // Ricarica i cavi\n      loadCavi();\n    } catch (error) {\n      console.error('Errore durante l\\'aggiornamento dei metri posati:', error);\n\n      // Gestione speciale per BOBINA_VUOTA\n      if (idBobina === 'BOBINA_VUOTA' && error.success) {\n        // Se è un \"errore\" di successo per BOBINA_VUOTA, trattiamolo come un successo\n        let successMessage = `Metri posati aggiornati con successo. Stato cavo: ${statoInstallazione}. Cavo associato a BOBINA VUOTA`;\n        onSuccess(successMessage);\n\n        // Reset del form\n        handleReset();\n\n        // Ricarica i cavi\n        loadCavi();\n        return;\n      }\n\n      // Gestione dettagliata degli errori\n      let errorMessage = 'Errore durante l\\'aggiornamento dei metri posati';\n      if (error.response) {\n        var _error$response$data2;\n        // Il server ha risposto con un codice di errore\n        const status = error.response.status;\n        const detail = ((_error$response$data2 = error.response.data) === null || _error$response$data2 === void 0 ? void 0 : _error$response$data2.detail) || error.message;\n        if (status === 400) {\n          // Errore di validazione\n          if (detail.includes('metri residui')) {\n            errorMessage = `La bobina non ha metri residui sufficienti. Usa l'opzione \"BOBINA VUOTA\" o seleziona un'altra bobina.`;\n          } else if (detail.includes('già posato')) {\n            errorMessage = `Il cavo risulta già posato. Usa la funzione \"Modifica bobina cavo posato\".`;\n          } else {\n            errorMessage = detail;\n          }\n        } else if (status === 404) {\n          // Gestione speciale per BOBINA_VUOTA quando la bobina non viene trovata\n          if (idBobina === 'BOBINA_VUOTA' && detail.includes('non trovata')) {\n            let successMessage = `Metri posati aggiornati con successo. Stato cavo: ${statoInstallazione}. Cavo associato a BOBINA VUOTA`;\n            onSuccess(successMessage);\n\n            // Reset del form\n            handleReset();\n\n            // Ricarica i cavi\n            loadCavi();\n            return;\n          } else {\n            errorMessage = `Cavo o bobina non trovati: ${detail}`;\n          }\n        } else {\n          errorMessage = `Errore del server (${status}): ${detail}`;\n        }\n      } else if (error.request) {\n        // La richiesta è stata inviata ma non è stata ricevuta risposta\n        errorMessage = 'Nessuna risposta dal server. Verifica la connessione di rete.';\n      } else if (error.detail && idBobina === 'BOBINA_VUOTA') {\n        // Gestione speciale per errori con BOBINA_VUOTA\n        errorMessage = error.detail;\n        if (error.status === 200 || error.success) {\n          let successMessage = `Metri posati aggiornati con successo. Stato cavo: ${statoInstallazione}. Cavo associato a BOBINA VUOTA`;\n          onSuccess(successMessage);\n\n          // Reset del form\n          handleReset();\n\n          // Ricarica i cavi\n          loadCavi();\n          return;\n        }\n      } else {\n        // Errore durante la configurazione della richiesta\n        errorMessage = error.message || error.detail || 'Errore sconosciuto';\n      }\n      onError(errorMessage);\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  // Renderizza il passo 1: Selezione del cavo\n  const renderStep1 = () => {\n    return /*#__PURE__*/_jsxDEV(Box, {\n      children: [/*#__PURE__*/_jsxDEV(Typography, {\n        variant: \"subtitle2\",\n        sx: {\n          mb: 1,\n          fontWeight: 'bold'\n        },\n        children: \"Seleziona un cavo\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 1010,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Paper, {\n        sx: {\n          p: 1.5,\n          mb: 2,\n          width: '100%'\n        },\n        children: /*#__PURE__*/_jsxDEV(Box, {\n          sx: {\n            display: 'flex',\n            alignItems: 'center',\n            width: '100%'\n          },\n          children: [/*#__PURE__*/_jsxDEV(Typography, {\n            variant: \"subtitle2\",\n            sx: {\n              mr: 1,\n              minWidth: '80px'\n            },\n            children: \"Cerca cavo\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 1017,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(TextField, {\n            size: \"small\",\n            label: \"ID Cavo\",\n            variant: \"outlined\",\n            value: cavoIdInput,\n            onChange: e => setCavoIdInput(e.target.value),\n            placeholder: \"Inserisci l'ID del cavo\",\n            sx: {\n              flexGrow: 1,\n              mr: 1\n            }\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 1020,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Button, {\n            variant: \"contained\",\n            color: \"primary\",\n            onClick: handleSearchCavoById,\n            disabled: caviLoading || !cavoIdInput.trim(),\n            startIcon: caviLoading ? /*#__PURE__*/_jsxDEV(CircularProgress, {\n              size: 16\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1034,\n              columnNumber: 40\n            }, this) : /*#__PURE__*/_jsxDEV(SearchIcon, {\n              fontSize: \"small\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1034,\n              columnNumber: 73\n            }, this),\n            size: \"small\",\n            sx: {\n              minWidth: '80px',\n              height: '36px'\n            },\n            children: \"CERCA\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 1029,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 1016,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 1015,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Paper, {\n        sx: {\n          p: 1.5,\n          width: '100%'\n        },\n        children: [/*#__PURE__*/_jsxDEV(Typography, {\n          variant: \"subtitle2\",\n          sx: {\n            mb: 1\n          },\n          children: \"Seleziona dalla lista\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 1045,\n          columnNumber: 11\n        }, this), caviLoading ? /*#__PURE__*/_jsxDEV(Box, {\n          sx: {\n            display: 'flex',\n            justifyContent: 'center',\n            my: 2\n          },\n          children: /*#__PURE__*/_jsxDEV(CircularProgress, {\n            size: 24\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 1051,\n            columnNumber: 15\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 1050,\n          columnNumber: 13\n        }, this) : cavi.length === 0 ? /*#__PURE__*/_jsxDEV(Alert, {\n          severity: \"info\",\n          sx: {\n            py: 0.5\n          },\n          children: /*#__PURE__*/_jsxDEV(Typography, {\n            variant: \"caption\",\n            children: \"Non ci sono cavi disponibili da installare.\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 1055,\n            columnNumber: 15\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 1054,\n          columnNumber: 13\n        }, this) : /*#__PURE__*/_jsxDEV(TableContainer, {\n          component: Paper,\n          variant: \"outlined\",\n          sx: {\n            maxHeight: '300px',\n            overflow: 'auto',\n            width: '100%'\n          },\n          children: /*#__PURE__*/_jsxDEV(Table, {\n            size: \"small\",\n            stickyHeader: true,\n            children: [/*#__PURE__*/_jsxDEV(TableHead, {\n              children: /*#__PURE__*/_jsxDEV(TableRow, {\n                sx: {\n                  '& th': {\n                    fontWeight: 'bold',\n                    py: 1,\n                    bgcolor: '#f5f5f5'\n                  }\n                },\n                children: [/*#__PURE__*/_jsxDEV(TableCell, {\n                  children: \"ID Cavo\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1062,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n                  children: \"Tipologia\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1063,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n                  children: \"Formazione\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1064,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n                  children: \"Metri\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1065,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n                  children: \"Stato\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1066,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n                  align: \"center\",\n                  sx: {\n                    width: '40px'\n                  },\n                  children: \"Info\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1067,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 1061,\n                columnNumber: 19\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1060,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(TableBody, {\n              children: cavi.map(cavo => /*#__PURE__*/_jsxDEV(TableRow, {\n                hover: true,\n                onClick: () => handleCavoSelect(cavo),\n                sx: {\n                  cursor: 'pointer',\n                  '&:hover': {\n                    bgcolor: '#f1f8e9'\n                  },\n                  '& td': {\n                    py: 0.5\n                  }\n                },\n                children: [/*#__PURE__*/_jsxDEV(TableCell, {\n                  sx: {\n                    fontWeight: 'medium'\n                  },\n                  children: cavo.id_cavo\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1082,\n                  columnNumber: 23\n                }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n                  children: cavo.tipologia || 'N/A'\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1083,\n                  columnNumber: 23\n                }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n                  children: cavo.sezione || 'N/A'\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1084,\n                  columnNumber: 23\n                }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n                  children: cavo.metri_teorici || 'N/A'\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1085,\n                  columnNumber: 23\n                }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n                  children: /*#__PURE__*/_jsxDEV(Chip, {\n                    size: \"small\",\n                    label: isCableSpare(cavo) ? 'SPARE' : isCableInstalled(cavo) ? 'Installato' : cavo.stato_installazione,\n                    color: isCableSpare(cavo) ? 'error' : isCableInstalled(cavo) ? 'success' : getCableStateColor(cavo.stato_installazione),\n                    sx: {\n                      height: '20px',\n                      '& .MuiChip-label': {\n                        px: 1,\n                        py: 0,\n                        fontSize: '0.7rem'\n                      }\n                    }\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1087,\n                    columnNumber: 25\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1086,\n                  columnNumber: 23\n                }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n                  align: \"center\",\n                  children: /*#__PURE__*/_jsxDEV(IconButton, {\n                    size: \"small\",\n                    onClick: e => {\n                      e.stopPropagation();\n                      setSelectedCavo(cavo);\n                      setShowCavoDetailsDialog(true);\n                    },\n                    children: /*#__PURE__*/_jsxDEV(InfoIcon, {\n                      fontSize: \"small\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1103,\n                      columnNumber: 27\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1095,\n                    columnNumber: 25\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1094,\n                  columnNumber: 23\n                }, this)]\n              }, cavo.id_cavo, true, {\n                fileName: _jsxFileName,\n                lineNumber: 1072,\n                columnNumber: 21\n              }, this))\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1070,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 1059,\n            columnNumber: 15\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 1058,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 1044,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 1009,\n      columnNumber: 7\n    }, this);\n  };\n\n  // Renderizza il passo 2: Inserimento metri\n  const renderStep2 = () => {\n    if (!selectedCavo) return null;\n    return /*#__PURE__*/_jsxDEV(Box, {\n      children: [/*#__PURE__*/_jsxDEV(Typography, {\n        variant: \"subtitle2\",\n        sx: {\n          mb: 1,\n          fontWeight: 'bold'\n        },\n        children: \"Inserisci metri posati\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 1123,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Paper, {\n        sx: {\n          p: 1.5,\n          mb: 2,\n          width: '100%'\n        },\n        children: /*#__PURE__*/_jsxDEV(Box, {\n          sx: {\n            display: 'flex',\n            flexWrap: 'wrap',\n            alignItems: 'center',\n            width: '100%'\n          },\n          children: [/*#__PURE__*/_jsxDEV(Typography, {\n            variant: \"subtitle2\",\n            sx: {\n              mr: 2,\n              fontWeight: 'bold'\n            },\n            children: [\"Cavo selezionato: \", /*#__PURE__*/_jsxDEV(\"span\", {\n              style: {\n                color: '#1976d2'\n              },\n              children: selectedCavo.id_cavo\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1131,\n              columnNumber: 33\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 1130,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Box, {\n            sx: {\n              display: 'flex',\n              flexWrap: 'wrap',\n              gap: 2,\n              ml: 'auto'\n            },\n            children: [/*#__PURE__*/_jsxDEV(Box, {\n              sx: {\n                display: 'flex',\n                alignItems: 'center'\n              },\n              children: [/*#__PURE__*/_jsxDEV(Typography, {\n                variant: \"body2\",\n                sx: {\n                  mr: 0.5,\n                  fontWeight: 'medium'\n                },\n                children: \"Tipologia:\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1136,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                variant: \"body2\",\n                children: selectedCavo.tipologia || 'N/A'\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1137,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 1135,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Box, {\n              sx: {\n                display: 'flex',\n                alignItems: 'center'\n              },\n              children: [/*#__PURE__*/_jsxDEV(Typography, {\n                variant: \"body2\",\n                sx: {\n                  mr: 0.5,\n                  fontWeight: 'medium'\n                },\n                children: \"Formazione:\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1141,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                variant: \"body2\",\n                children: selectedCavo.sezione || 'N/A'\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1142,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 1140,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Box, {\n              sx: {\n                display: 'flex',\n                alignItems: 'center'\n              },\n              children: [/*#__PURE__*/_jsxDEV(Typography, {\n                variant: \"body2\",\n                sx: {\n                  mr: 0.5,\n                  fontWeight: 'medium'\n                },\n                children: \"Metri teorici:\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1146,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                variant: \"body2\",\n                children: [selectedCavo.metri_teorici || 'N/A', \" m\"]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 1147,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 1145,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Box, {\n              sx: {\n                display: 'flex',\n                alignItems: 'center'\n              },\n              children: [/*#__PURE__*/_jsxDEV(Typography, {\n                variant: \"body2\",\n                sx: {\n                  mr: 0.5,\n                  fontWeight: 'medium'\n                },\n                children: \"Stato:\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1151,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(Chip, {\n                label: selectedCavo.stato_installazione || 'N/D',\n                size: \"small\",\n                color: getCableStateColor(selectedCavo.stato_installazione),\n                variant: \"outlined\",\n                sx: {\n                  height: '20px',\n                  '& .MuiChip-label': {\n                    px: 1,\n                    py: 0\n                  }\n                }\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1152,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 1150,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 1134,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 1129,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 1128,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Paper, {\n        sx: {\n          p: 2,\n          width: '100%'\n        },\n        children: [/*#__PURE__*/_jsxDEV(Typography, {\n          variant: \"subtitle2\",\n          gutterBottom: true,\n          sx: {\n            fontWeight: 'bold'\n          },\n          children: \"Inserisci i metri posati\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 1165,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Box, {\n          sx: {\n            display: 'flex',\n            flexWrap: 'wrap',\n            gap: 2,\n            mb: 2\n          },\n          children: [/*#__PURE__*/_jsxDEV(Box, {\n            sx: {\n              p: 1,\n              bgcolor: '#f5f5f5',\n              borderRadius: 1,\n              flex: '1 1 auto',\n              minWidth: '200px',\n              maxWidth: '400px'\n            },\n            children: [/*#__PURE__*/_jsxDEV(Typography, {\n              variant: \"caption\",\n              sx: {\n                fontWeight: 'bold',\n                color: 'primary.main',\n                display: 'block',\n                mb: 0.5\n              },\n              children: \"Informazioni cavo\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1172,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Box, {\n              sx: {\n                display: 'flex',\n                justifyContent: 'space-between',\n                mb: 0.5\n              },\n              children: [/*#__PURE__*/_jsxDEV(Typography, {\n                variant: \"body2\",\n                sx: {\n                  fontWeight: 'medium',\n                  fontSize: '0.8rem'\n                },\n                children: \"Metri teorici:\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1176,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                variant: \"body2\",\n                sx: {\n                  fontSize: '0.8rem'\n                },\n                children: [selectedCavo.metri_teorici || 'N/A', \" m\"]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 1177,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 1175,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Box, {\n              sx: {\n                display: 'flex',\n                justifyContent: 'space-between'\n              },\n              children: [/*#__PURE__*/_jsxDEV(Typography, {\n                variant: \"body2\",\n                sx: {\n                  fontWeight: 'medium',\n                  fontSize: '0.8rem'\n                },\n                children: \"Stato attuale:\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1180,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(Chip, {\n                label: selectedCavo.stato_installazione || 'N/D',\n                size: \"small\",\n                color: getCableStateColor(selectedCavo.stato_installazione),\n                variant: \"outlined\",\n                sx: {\n                  height: '18px',\n                  '& .MuiChip-label': {\n                    px: 1,\n                    py: 0,\n                    fontSize: '0.7rem'\n                  }\n                }\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1181,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 1179,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 1171,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Box, {\n            sx: {\n              p: 1,\n              bgcolor: '#f5f5f5',\n              borderRadius: 1,\n              flex: '1 1 auto',\n              minWidth: '200px',\n              maxWidth: '400px'\n            },\n            children: [/*#__PURE__*/_jsxDEV(Typography, {\n              variant: \"caption\",\n              sx: {\n                fontWeight: 'bold',\n                color: 'secondary.main',\n                display: 'block',\n                mb: 0.5\n              },\n              children: \"Informazioni bobina\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1192,\n              columnNumber: 15\n            }, this), formData.id_bobina && formData.id_bobina !== 'BOBINA_VUOTA' ? (() => {\n              const bobina = bobine.find(b => b.id_bobina === formData.id_bobina);\n              return bobina ? /*#__PURE__*/_jsxDEV(_Fragment, {\n                children: [/*#__PURE__*/_jsxDEV(Box, {\n                  sx: {\n                    display: 'flex',\n                    justifyContent: 'space-between',\n                    mb: 0.5\n                  },\n                  children: [/*#__PURE__*/_jsxDEV(Typography, {\n                    variant: \"body2\",\n                    sx: {\n                      fontWeight: 'medium',\n                      fontSize: '0.8rem'\n                    },\n                    children: \"ID Bobina:\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1200,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                    variant: \"body2\",\n                    sx: {\n                      fontSize: '0.8rem'\n                    },\n                    children: getBobinaNumber(bobina.id_bobina)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1201,\n                    columnNumber: 23\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1199,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(Box, {\n                  sx: {\n                    display: 'flex',\n                    justifyContent: 'space-between',\n                    mb: 0.5\n                  },\n                  children: [/*#__PURE__*/_jsxDEV(Typography, {\n                    variant: \"body2\",\n                    sx: {\n                      fontWeight: 'medium',\n                      fontSize: '0.8rem'\n                    },\n                    children: \"Metri residui:\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1204,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                    variant: \"body2\",\n                    sx: {\n                      fontSize: '0.8rem'\n                    },\n                    children: [bobina.metri_residui || 0, \" m\"]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1205,\n                    columnNumber: 23\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1203,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(Box, {\n                  sx: {\n                    display: 'flex',\n                    justifyContent: 'space-between'\n                  },\n                  children: [/*#__PURE__*/_jsxDEV(Typography, {\n                    variant: \"body2\",\n                    sx: {\n                      fontWeight: 'medium',\n                      fontSize: '0.8rem'\n                    },\n                    children: \"Stato:\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1208,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(Chip, {\n                    label: bobina.stato_bobina || 'N/D',\n                    size: \"small\",\n                    color: getReelStateColor(bobina.stato_bobina),\n                    variant: \"outlined\",\n                    sx: {\n                      height: '18px',\n                      '& .MuiChip-label': {\n                        px: 1,\n                        py: 0,\n                        fontSize: '0.7rem'\n                      }\n                    }\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1209,\n                    columnNumber: 23\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1207,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true) : /*#__PURE__*/_jsxDEV(Typography, {\n                variant: \"body2\",\n                sx: {\n                  fontSize: '0.8rem'\n                },\n                children: \"Bobina non trovata\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1219,\n                columnNumber: 19\n              }, this);\n            })() : /*#__PURE__*/_jsxDEV(Typography, {\n              variant: \"body2\",\n              sx: {\n                fontSize: '0.8rem'\n              },\n              children: formData.id_bobina === 'BOBINA_VUOTA' ? \"Utilizzo BOBINA VUOTA (nessuna bobina associata)\" : \"Nessuna bobina selezionata\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1222,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 1191,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 1170,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Box, {\n          sx: {\n            mt: 2,\n            mb: 2,\n            width: '100%'\n          },\n          children: [/*#__PURE__*/_jsxDEV(Typography, {\n            variant: \"subtitle2\",\n            gutterBottom: true,\n            sx: {\n              fontWeight: 'bold'\n            },\n            children: \"Metratura posata\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 1232,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(TextField, {\n            size: \"small\",\n            label: \"Metri posati\",\n            variant: \"outlined\",\n            name: \"metri_posati\",\n            type: \"number\",\n            value: formData.metri_posati,\n            onChange: handleFormChange,\n            error: !!formErrors.metri_posati,\n            helperText: formErrors.metri_posati || formWarnings.metri_posati,\n            FormHelperTextProps: {\n              sx: {\n                color: formWarnings.metri_posati && !formErrors.metri_posati ? 'warning.main' : 'error.main'\n              }\n            },\n            sx: {\n              mb: 1,\n              width: '200px'\n            },\n            inputProps: {\n              max: 999999,\n              // Limite a 6 cifre\n              step: 0.1\n            }\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 1235,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 1231,\n          columnNumber: 11\n        }, this), formWarnings.metri_posati && !formErrors.metri_posati && !notificationShown && /*#__PURE__*/_jsxDEV(Alert, {\n          severity: \"warning\",\n          sx: {\n            mb: 2\n          },\n          children: formWarnings.metri_posati\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 1257,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(Alert, {\n          severity: \"info\",\n          sx: {\n            mt: 2\n          },\n          children: /*#__PURE__*/_jsxDEV(Typography, {\n            variant: \"body2\",\n            children: \"Inserisci i metri di cavo effettivamente posati. Questo valore aggiorna lo stato di installazione del cavo.\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 1263,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 1262,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 1164,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 1122,\n      columnNumber: 7\n    }, this);\n  };\n\n  // Renderizza il passo 3: Associazione bobina\n  const renderStep3 = () => {\n    // Ottieni tutte le tipologie uniche dalle bobine\n    const tipologieUniche = [...new Set(bobine.map(bobina => bobina.tipologia || 'N/D'))];\n\n    // Funzione per costruire l'ID completo della bobina\n    const buildFullBobinaId = numeroBobina => {\n      return `C${cantiereId}_B${numeroBobina}`;\n    };\n\n    // Verifica se una bobina ha metri residui sufficienti\n    const hasSufficientMeters = bobina => {\n      if (!bobina || !formData.metri_posati) return true;\n      return parseFloat(bobina.metri_residui) >= parseFloat(formData.metri_posati);\n    };\n\n    // Filtra le bobine in base alla tipologia selezionata e al testo di ricerca\n    const bobineFiltrate = bobine.filter(bobina => {\n      // Filtro per tipologia\n      const matchesTipologia = !tipologiaFilter || String(bobina.tipologia || '').trim() === tipologiaFilter;\n\n      // Filtro per testo di ricerca\n      const searchLower = searchText.toLowerCase();\n      const matchesSearch = !searchText || getBobinaNumber(bobina.id_bobina).toLowerCase().includes(searchLower) || String(bobina.tipologia || '').toLowerCase().includes(searchLower) || String(bobina.sezione || '').toLowerCase().includes(searchLower);\n      return matchesTipologia && matchesSearch;\n    });\n\n    // Separa le bobine compatibili e non compatibili\n    const bobineCompatibili = selectedCavo ? bobineFiltrate.filter(bobina => String(bobina.tipologia || '').trim() === String(selectedCavo.tipologia || '').trim() && String(bobina.sezione || '0').trim() === String(selectedCavo.sezione || '0').trim()) : bobineFiltrate;\n    const bobineNonCompatibili = selectedCavo ? bobineFiltrate.filter(bobina => String(bobina.tipologia || '').trim() !== String(selectedCavo.tipologia || '').trim() || String(bobina.sezione || '0').trim() !== String(selectedCavo.sezione || '0').trim()) : [];\n\n    // Gestisce l'input diretto del numero della bobina\n    const handleBobinaNumberInput = e => {\n      const numeroBobina = e.target.value.trim();\n\n      // Gestione esplicita dell'input 'v' per bobina vuota\n      if (numeroBobina.toLowerCase() === 'v') {\n        setFormData({\n          ...formData,\n          id_bobina: ''\n        });\n        setFormErrors({\n          ...formErrors,\n          id_bobina_input: null\n        });\n        return;\n      }\n      if (numeroBobina) {\n        // Costruisci l'ID completo\n        const idBobinaCompleto = buildFullBobinaId(numeroBobina);\n\n        // Verifica se la bobina esiste\n        const bobinaEsistente = bobine.find(b => b.id_bobina === idBobinaCompleto);\n        if (bobinaEsistente) {\n          // Verifica se la bobina è in stato OVER o TERMINATA\n          if (bobinaEsistente.stato_bobina === 'Over' || bobinaEsistente.stato_bobina === 'Terminata') {\n            setFormErrors({\n              ...formErrors,\n              id_bobina_input: `La bobina ${numeroBobina} è in stato ${bobinaEsistente.stato_bobina} e non può essere utilizzata`\n            });\n            return;\n          }\n\n          // Verifica compatibilità tra cavo e bobina\n          if (selectedCavo) {\n            // Converti i valori in stringhe, gestendo null e undefined\n            const cavoTipologia = String(selectedCavo.tipologia || '');\n            const cavoSezione = String(selectedCavo.sezione || '0');\n            const bobinaTipologia = String(bobinaEsistente.tipologia || '');\n            const bobinaSezione = String(bobinaEsistente.sezione || '0');\n\n            // Log per debug\n            console.log(`Verifica compatibilità bobina ${bobinaEsistente.id_bobina}:`, {\n              tipologia: `${bobinaTipologia} === ${cavoTipologia}`,\n              formazione: `${bobinaSezione} === ${cavoSezione}`\n            });\n            if (bobinaTipologia !== cavoTipologia || bobinaSezione !== cavoSezione) {\n              // Mostra il dialogo per bobine incompatibili\n              setIncompatibleReelData({\n                cavo: selectedCavo,\n                bobina: bobinaEsistente\n              });\n              setShowIncompatibleReelDialog(true);\n              return;\n            }\n          }\n\n          // Verifica se la bobina ha metri residui sufficienti\n          if (hasSufficientMeters(bobinaEsistente)) {\n            // Se la bobina esiste e ha metri sufficienti, imposta l'ID completo\n            setFormData({\n              ...formData,\n              id_bobina: idBobinaCompleto\n            });\n            setFormErrors({\n              ...formErrors,\n              id_bobina_input: null\n            });\n          } else {\n            // Se la bobina non ha metri sufficienti, mostra un avviso\n            setFormErrors({\n              ...formErrors,\n              id_bobina_input: `La bobina ${numeroBobina} non ha metri residui sufficienti (${bobinaEsistente.metri_residui} m disponibili)`\n            });\n          }\n        } else {\n          // Se la bobina non esiste, mostra un errore\n          setFormErrors({\n            ...formErrors,\n            id_bobina_input: `Bobina ${numeroBobina} non trovata`\n          });\n        }\n      } else {\n        // Se l'input è vuoto, resetta l'ID bobina\n        setFormData({\n          ...formData,\n          id_bobina: ''\n        });\n        setFormErrors({\n          ...formErrors,\n          id_bobina_input: null\n        });\n      }\n    };\n    return /*#__PURE__*/_jsxDEV(Box, {\n      children: [/*#__PURE__*/_jsxDEV(Typography, {\n        variant: \"subtitle2\",\n        sx: {\n          mb: 1,\n          fontWeight: 'bold'\n        },\n        children: \"Associa bobina\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 1418,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Paper, {\n        sx: {\n          p: 2,\n          width: '100%'\n        },\n        children: [/*#__PURE__*/_jsxDEV(Box, {\n          sx: {\n            mb: 2\n          },\n          children: [/*#__PURE__*/_jsxDEV(Typography, {\n            variant: \"body2\",\n            sx: {\n              mb: 1\n            },\n            children: \"Seleziona una bobina da associare al cavo o usa \\\"BOBINA VUOTA\\\" se non desideri associare una bobina specifica.\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 1424,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Box, {\n            sx: {\n              display: 'flex',\n              justifyContent: 'space-between',\n              alignItems: 'center',\n              gap: 2\n            },\n            children: [/*#__PURE__*/_jsxDEV(TextField, {\n              size: \"small\",\n              label: \"Cerca bobina\",\n              variant: \"outlined\",\n              value: searchText,\n              onChange: handleSearchTextChange,\n              placeholder: \"Cerca per ID, tipologia...\",\n              InputProps: {\n                startAdornment: /*#__PURE__*/_jsxDEV(InputAdornment, {\n                  position: \"start\",\n                  children: /*#__PURE__*/_jsxDEV(SearchIcon, {\n                    fontSize: \"small\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1440,\n                    columnNumber: 23\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1439,\n                  columnNumber: 21\n                }, this),\n                endAdornment: searchText ? /*#__PURE__*/_jsxDEV(InputAdornment, {\n                  position: \"end\",\n                  children: /*#__PURE__*/_jsxDEV(IconButton, {\n                    size: \"small\",\n                    \"aria-label\": \"clear search\",\n                    onClick: () => setSearchText(''),\n                    edge: \"end\",\n                    children: /*#__PURE__*/_jsxDEV(CancelIcon, {\n                      fontSize: \"small\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1451,\n                      columnNumber: 25\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1445,\n                    columnNumber: 23\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1444,\n                  columnNumber: 21\n                }, this) : null\n              },\n              sx: {\n                flex: 1\n              }\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1430,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(FormControl, {\n              size: \"small\",\n              sx: {\n                minWidth: 200\n              },\n              children: [/*#__PURE__*/_jsxDEV(InputLabel, {\n                id: \"tipologia-filter-label\",\n                children: \"Filtra per tipologia\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1461,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(Select, {\n                labelId: \"tipologia-filter-label\",\n                value: tipologiaFilter,\n                label: \"Filtra per tipologia\",\n                onChange: handleTipologiaFilterChange,\n                children: [/*#__PURE__*/_jsxDEV(MenuItem, {\n                  value: \"\",\n                  children: \"Tutte le tipologie\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1468,\n                  columnNumber: 19\n                }, this), tipologieUniche.map(tipologia => /*#__PURE__*/_jsxDEV(MenuItem, {\n                  value: tipologia,\n                  children: tipologia\n                }, tipologia, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1470,\n                  columnNumber: 21\n                }, this))]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 1462,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 1460,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 1428,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 1423,\n          columnNumber: 11\n        }, this), bobineLoading ? /*#__PURE__*/_jsxDEV(Box, {\n          sx: {\n            display: 'flex',\n            justifyContent: 'center',\n            my: 1\n          },\n          children: /*#__PURE__*/_jsxDEV(CircularProgress, {\n            size: 24\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 1479,\n            columnNumber: 15\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 1478,\n          columnNumber: 13\n        }, this) : /*#__PURE__*/_jsxDEV(Box, {\n          children: [/*#__PURE__*/_jsxDEV(Box, {\n            sx: {\n              mb: 2\n            },\n            children: [/*#__PURE__*/_jsxDEV(Typography, {\n              variant: \"subtitle2\",\n              sx: {\n                fontWeight: 'bold',\n                display: 'block',\n                mb: 1\n              },\n              children: \"Inserimento diretto\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1485,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(Box, {\n              sx: {\n                display: 'flex',\n                alignItems: 'flex-start',\n                gap: 1,\n                maxWidth: '300px'\n              },\n              children: [/*#__PURE__*/_jsxDEV(TextField, {\n                size: \"small\",\n                label: \"Numero bobina\",\n                variant: \"outlined\",\n                placeholder: \"Solo Y\",\n                error: !!formErrors.id_bobina_input,\n                onBlur: handleBobinaNumberInput,\n                sx: {\n                  flex: 1\n                }\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1489,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(Button, {\n                variant: \"outlined\",\n                size: \"small\",\n                onClick: () => handleSelectBobina('BOBINA_VUOTA'),\n                sx: {\n                  height: '40px'\n                },\n                children: \"BOBINA VUOTA\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1498,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 1488,\n              columnNumber: 17\n            }, this), formErrors.id_bobina_input && /*#__PURE__*/_jsxDEV(Typography, {\n              variant: \"caption\",\n              color: \"error\",\n              sx: {\n                display: 'block',\n                mt: 0.5\n              },\n              children: formErrors.id_bobina_input\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1508,\n              columnNumber: 19\n            }, this), /*#__PURE__*/_jsxDEV(Typography, {\n              variant: \"caption\",\n              sx: {\n                display: 'block',\n                mt: 0.5\n              },\n              children: [\"ID Bobina: \", /*#__PURE__*/_jsxDEV(\"strong\", {\n                children: formData.id_bobina || '-'\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1513,\n                columnNumber: 30\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 1512,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 1484,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(Grid, {\n            container: true,\n            spacing: 2,\n            children: [/*#__PURE__*/_jsxDEV(Grid, {\n              item: true,\n              xs: 12,\n              md: 6,\n              children: /*#__PURE__*/_jsxDEV(Paper, {\n                variant: \"outlined\",\n                sx: {\n                  p: 2,\n                  height: '100%'\n                },\n                children: [/*#__PURE__*/_jsxDEV(Typography, {\n                  variant: \"subtitle2\",\n                  sx: {\n                    fontWeight: 'bold',\n                    mb: 1\n                  },\n                  children: \"ELENCO BOBINE COMPATIBILI\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1522,\n                  columnNumber: 21\n                }, this), bobineCompatibili.length > 0 ? /*#__PURE__*/_jsxDEV(List, {\n                  sx: {\n                    maxHeight: '300px',\n                    overflow: 'auto',\n                    bgcolor: 'background.paper'\n                  },\n                  children: bobineCompatibili.map(bobina => /*#__PURE__*/_jsxDEV(ListItem, {\n                    disablePadding: true,\n                    secondaryAction: /*#__PURE__*/_jsxDEV(IconButton, {\n                      edge: \"end\",\n                      size: \"small\",\n                      onClick: () => handleSelectBobina(bobina.id_bobina),\n                      disabled: bobina.metri_residui < parseFloat(formData.metri_posati || 0),\n                      children: /*#__PURE__*/_jsxDEV(AddCircleOutlineIcon, {\n                        color: bobina.metri_residui < parseFloat(formData.metri_posati || 0) ? 'disabled' : 'primary'\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 1539,\n                        columnNumber: 33\n                      }, this)\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1533,\n                      columnNumber: 31\n                    }, this),\n                    sx: {\n                      bgcolor: formData.id_bobina === bobina.id_bobina ? 'rgba(76, 175, 80, 0.12)' : 'inherit',\n                      borderRadius: '4px',\n                      mb: 0.5,\n                      border: formData.id_bobina === bobina.id_bobina ? '1px solid #4caf50' : 'none'\n                    },\n                    children: /*#__PURE__*/_jsxDEV(ListItemButton, {\n                      dense: true,\n                      onClick: () => handleSelectBobina(bobina.id_bobina),\n                      disabled: bobina.metri_residui < parseFloat(formData.metri_posati || 0),\n                      children: /*#__PURE__*/_jsxDEV(ListItemText, {\n                        primary: /*#__PURE__*/_jsxDEV(Box, {\n                          sx: {\n                            display: 'flex',\n                            justifyContent: 'space-between',\n                            alignItems: 'center'\n                          },\n                          children: [/*#__PURE__*/_jsxDEV(Typography, {\n                            variant: \"body2\",\n                            sx: {\n                              fontWeight: 'bold'\n                            },\n                            children: getBobinaNumber(bobina.id_bobina)\n                          }, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 1557,\n                            columnNumber: 37\n                          }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                            variant: \"caption\",\n                            sx: {\n                              fontWeight: 'bold',\n                              color: bobina.metri_residui < parseFloat(formData.metri_posati || 0) ? 'error.main' : 'success.main'\n                            },\n                            children: [bobina.metri_residui || 0, \" m\"]\n                          }, void 0, true, {\n                            fileName: _jsxFileName,\n                            lineNumber: 1560,\n                            columnNumber: 37\n                          }, this)]\n                        }, void 0, true, {\n                          fileName: _jsxFileName,\n                          lineNumber: 1556,\n                          columnNumber: 35\n                        }, this),\n                        secondary: /*#__PURE__*/_jsxDEV(_Fragment, {\n                          children: [/*#__PURE__*/_jsxDEV(Typography, {\n                            variant: \"caption\",\n                            component: \"span\",\n                            display: \"block\",\n                            children: [bobina.tipologia || 'N/A', \" - \", bobina.sezione || 'N/A']\n                          }, void 0, true, {\n                            fileName: _jsxFileName,\n                            lineNumber: 1567,\n                            columnNumber: 37\n                          }, this), /*#__PURE__*/_jsxDEV(Chip, {\n                            size: \"small\",\n                            label: bobina.stato_bobina || 'N/D',\n                            color: getReelStateColor(bobina.stato_bobina),\n                            variant: \"outlined\",\n                            sx: {\n                              height: 16,\n                              fontSize: '0.6rem',\n                              mt: 0.5,\n                              '& .MuiChip-label': {\n                                px: 0.5,\n                                py: 0\n                              }\n                            }\n                          }, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 1570,\n                            columnNumber: 37\n                          }, this)]\n                        }, void 0, true)\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 1554,\n                        columnNumber: 31\n                      }, this)\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1549,\n                      columnNumber: 29\n                    }, this)\n                  }, bobina.id_bobina, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1529,\n                    columnNumber: 27\n                  }, this))\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1527,\n                  columnNumber: 23\n                }, this) : /*#__PURE__*/_jsxDEV(Alert, {\n                  severity: \"info\",\n                  sx: {\n                    mt: 1\n                  },\n                  children: \"Nessuna bobina compatibile disponibile. Puoi usare BOBINA VUOTA o selezionare una bobina non compatibile.\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1585,\n                  columnNumber: 23\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 1521,\n                columnNumber: 19\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1520,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(Grid, {\n              item: true,\n              xs: 12,\n              md: 6,\n              children: /*#__PURE__*/_jsxDEV(Paper, {\n                variant: \"outlined\",\n                sx: {\n                  p: 2,\n                  height: '100%'\n                },\n                children: [/*#__PURE__*/_jsxDEV(Typography, {\n                  variant: \"subtitle2\",\n                  sx: {\n                    fontWeight: 'bold',\n                    mb: 1\n                  },\n                  children: \"ELENCO BOBINE NON COMPATIBILI\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1595,\n                  columnNumber: 21\n                }, this), bobineNonCompatibili.length > 0 ? /*#__PURE__*/_jsxDEV(List, {\n                  sx: {\n                    maxHeight: '300px',\n                    overflow: 'auto',\n                    bgcolor: 'background.paper'\n                  },\n                  children: bobineNonCompatibili.map(bobina => /*#__PURE__*/_jsxDEV(ListItem, {\n                    disablePadding: true,\n                    secondaryAction: /*#__PURE__*/_jsxDEV(IconButton, {\n                      edge: \"end\",\n                      size: \"small\",\n                      onClick: () => handleSelectBobina(bobina.id_bobina),\n                      disabled: bobina.metri_residui < parseFloat(formData.metri_posati || 0),\n                      children: /*#__PURE__*/_jsxDEV(AddCircleOutlineIcon, {\n                        color: bobina.metri_residui < parseFloat(formData.metri_posati || 0) ? 'disabled' : 'primary'\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 1612,\n                        columnNumber: 33\n                      }, this)\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1606,\n                      columnNumber: 31\n                    }, this),\n                    sx: {\n                      bgcolor: formData.id_bobina === bobina.id_bobina ? 'rgba(76, 175, 80, 0.12)' : 'inherit',\n                      borderRadius: '4px',\n                      mb: 0.5,\n                      border: formData.id_bobina === bobina.id_bobina ? '1px solid #4caf50' : 'none'\n                    },\n                    children: /*#__PURE__*/_jsxDEV(ListItemButton, {\n                      dense: true,\n                      onClick: () => handleSelectBobina(bobina.id_bobina),\n                      disabled: bobina.metri_residui < parseFloat(formData.metri_posati || 0),\n                      children: /*#__PURE__*/_jsxDEV(ListItemText, {\n                        primary: /*#__PURE__*/_jsxDEV(Box, {\n                          sx: {\n                            display: 'flex',\n                            justifyContent: 'space-between',\n                            alignItems: 'center'\n                          },\n                          children: [/*#__PURE__*/_jsxDEV(Typography, {\n                            variant: \"body2\",\n                            sx: {\n                              fontWeight: 'bold'\n                            },\n                            children: getBobinaNumber(bobina.id_bobina)\n                          }, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 1630,\n                            columnNumber: 37\n                          }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                            variant: \"caption\",\n                            sx: {\n                              fontWeight: 'bold',\n                              color: bobina.metri_residui < parseFloat(formData.metri_posati || 0) ? 'error.main' : 'success.main'\n                            },\n                            children: [bobina.metri_residui || 0, \" m\"]\n                          }, void 0, true, {\n                            fileName: _jsxFileName,\n                            lineNumber: 1633,\n                            columnNumber: 37\n                          }, this)]\n                        }, void 0, true, {\n                          fileName: _jsxFileName,\n                          lineNumber: 1629,\n                          columnNumber: 35\n                        }, this),\n                        secondary: /*#__PURE__*/_jsxDEV(_Fragment, {\n                          children: [/*#__PURE__*/_jsxDEV(Typography, {\n                            variant: \"caption\",\n                            component: \"span\",\n                            display: \"block\",\n                            children: [bobina.tipologia || 'N/A', \" - \", bobina.sezione || 'N/A']\n                          }, void 0, true, {\n                            fileName: _jsxFileName,\n                            lineNumber: 1640,\n                            columnNumber: 37\n                          }, this), /*#__PURE__*/_jsxDEV(Box, {\n                            sx: {\n                              display: 'flex',\n                              justifyContent: 'space-between',\n                              alignItems: 'center',\n                              mt: 0.5\n                            },\n                            children: [/*#__PURE__*/_jsxDEV(Chip, {\n                              size: \"small\",\n                              label: bobina.stato_bobina || 'N/D',\n                              color: getReelStateColor(bobina.stato_bobina),\n                              variant: \"outlined\",\n                              sx: {\n                                height: 16,\n                                fontSize: '0.6rem',\n                                '& .MuiChip-label': {\n                                  px: 0.5,\n                                  py: 0\n                                }\n                              }\n                            }, void 0, false, {\n                              fileName: _jsxFileName,\n                              lineNumber: 1644,\n                              columnNumber: 39\n                            }, this), /*#__PURE__*/_jsxDEV(Chip, {\n                              size: \"small\",\n                              label: \"Non compatibile\",\n                              color: \"warning\",\n                              variant: \"outlined\",\n                              sx: {\n                                height: 16,\n                                fontSize: '0.6rem',\n                                '& .MuiChip-label': {\n                                  px: 0.5,\n                                  py: 0\n                                }\n                              }\n                            }, void 0, false, {\n                              fileName: _jsxFileName,\n                              lineNumber: 1651,\n                              columnNumber: 39\n                            }, this)]\n                          }, void 0, true, {\n                            fileName: _jsxFileName,\n                            lineNumber: 1643,\n                            columnNumber: 37\n                          }, this)]\n                        }, void 0, true)\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 1627,\n                        columnNumber: 31\n                      }, this)\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1622,\n                      columnNumber: 29\n                    }, this)\n                  }, bobina.id_bobina, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1602,\n                    columnNumber: 27\n                  }, this))\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1600,\n                  columnNumber: 23\n                }, this) : /*#__PURE__*/_jsxDEV(Alert, {\n                  severity: \"info\",\n                  sx: {\n                    mt: 1\n                  },\n                  children: \"Nessuna bobina non compatibile disponibile con i filtri attuali.\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1667,\n                  columnNumber: 23\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 1594,\n                columnNumber: 19\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1593,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 1518,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 1482,\n          columnNumber: 13\n        }, this), !bobineLoading && formData.id_bobina && /*#__PURE__*/_jsxDEV(Box, {\n          sx: {\n            mt: 3,\n            p: 2,\n            bgcolor: 'background.paper',\n            borderRadius: 1,\n            border: '1px solid #e0e0e0'\n          },\n          children: [/*#__PURE__*/_jsxDEV(Typography, {\n            variant: \"subtitle2\",\n            gutterBottom: true,\n            children: \"Dettagli bobina selezionata\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 1680,\n            columnNumber: 15\n          }, this), (() => {\n            const bobina = bobine.find(b => b.id_bobina === formData.id_bobina);\n            if (bobina) {\n              return /*#__PURE__*/_jsxDEV(Grid, {\n                container: true,\n                spacing: 2,\n                children: [/*#__PURE__*/_jsxDEV(Grid, {\n                  item: true,\n                  xs: 12,\n                  md: 6,\n                  children: [/*#__PURE__*/_jsxDEV(Typography, {\n                    variant: \"body2\",\n                    children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n                      children: \"Numero:\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1690,\n                      columnNumber: 27\n                    }, this), \" \", getBobinaNumber(bobina.id_bobina)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1689,\n                    columnNumber: 25\n                  }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                    variant: \"body2\",\n                    children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n                      children: \"Tipologia:\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1693,\n                      columnNumber: 27\n                    }, this), \" \", bobina.tipologia || 'N/A']\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1692,\n                    columnNumber: 25\n                  }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                    variant: \"body2\",\n                    children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n                      children: \"Conduttori:\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1696,\n                      columnNumber: 27\n                    }, this), \" \", bobina.n_conduttori || 'N/A', \" x \", bobina.sezione || 'N/A']\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1695,\n                    columnNumber: 25\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1688,\n                  columnNumber: 23\n                }, this), /*#__PURE__*/_jsxDEV(Grid, {\n                  item: true,\n                  xs: 12,\n                  md: 6,\n                  children: [/*#__PURE__*/_jsxDEV(Typography, {\n                    variant: \"body2\",\n                    children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n                      children: \"Metri totali:\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1701,\n                      columnNumber: 27\n                    }, this), \" \", bobina.metri_totali || 0, \" m\"]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1700,\n                    columnNumber: 25\n                  }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                    variant: \"body2\",\n                    children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n                      children: \"Metri residui:\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1704,\n                      columnNumber: 27\n                    }, this), \" \", bobina.metri_residui || 0, \" m\"]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1703,\n                    columnNumber: 25\n                  }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                    variant: \"body2\",\n                    children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n                      children: \"Stato:\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1707,\n                      columnNumber: 27\n                    }, this), \" \", bobina.stato_bobina || 'N/A']\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1706,\n                    columnNumber: 25\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1699,\n                  columnNumber: 23\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 1687,\n                columnNumber: 21\n              }, this);\n            }\n            return /*#__PURE__*/_jsxDEV(Typography, {\n              variant: \"body2\",\n              color: \"error\",\n              children: \"Bobina non trovata nel database\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1714,\n              columnNumber: 19\n            }, this);\n          })()]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 1679,\n          columnNumber: 13\n        }, this), bobine.length === 0 && !bobineLoading && /*#__PURE__*/_jsxDEV(Alert, {\n          severity: \"warning\",\n          sx: {\n            mt: 2\n          },\n          children: \"Non ci sono bobine disponibili. Puoi procedere senza associare una bobina o aggiungere prima una nuova bobina.\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 1723,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 1422,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 1417,\n      columnNumber: 7\n    }, this);\n  };\n\n  // Renderizza il passo 4: Conferma\n  const renderStep4 = () => {\n    // Ottieni il numero della bobina se presente\n    let numeroBobina = 'Nessuna';\n    let bobinaInfo = null;\n    if (formData.id_bobina === 'BOBINA_VUOTA') {\n      numeroBobina = 'BOBINA VUOTA';\n    } else if (formData.id_bobina) {\n      numeroBobina = getBobinaNumber(formData.id_bobina);\n      // Trova i dettagli della bobina selezionata\n      bobinaInfo = bobine.find(b => b.id_bobina === formData.id_bobina);\n    }\n\n    // Determina lo stato di installazione\n    const statoInstallazione = determineInstallationStatus(parseFloat(formData.metri_posati), selectedCavo.metri_teorici);\n    return /*#__PURE__*/_jsxDEV(Box, {\n      children: [/*#__PURE__*/_jsxDEV(Typography, {\n        variant: \"h6\",\n        gutterBottom: true,\n        children: \"Conferma inserimento\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 1752,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Paper, {\n        sx: {\n          p: 3\n        },\n        children: [/*#__PURE__*/_jsxDEV(Typography, {\n          variant: \"subtitle1\",\n          gutterBottom: true,\n          children: \"Riepilogo dati\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 1757,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(CavoDetailsView, {\n          cavo: selectedCavo,\n          compact: true,\n          title: \"Dettagli del cavo\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 1762,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Box, {\n          sx: {\n            mt: 3,\n            p: 2,\n            bgcolor: '#f5f5f5',\n            borderRadius: 1\n          },\n          children: [/*#__PURE__*/_jsxDEV(Typography, {\n            variant: \"subtitle2\",\n            gutterBottom: true,\n            sx: {\n              fontWeight: 'bold'\n            },\n            children: \"Informazioni sull'operazione:\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 1770,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Grid, {\n            container: true,\n            spacing: 2,\n            children: [/*#__PURE__*/_jsxDEV(Grid, {\n              item: true,\n              xs: 12,\n              md: 6,\n              children: [/*#__PURE__*/_jsxDEV(Typography, {\n                variant: \"body2\",\n                children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n                  children: \"Metri Posati:\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1776,\n                  columnNumber: 19\n                }, this), \" \", formData.metri_posati, \" m\"]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 1775,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                variant: \"body2\",\n                children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n                  children: \"Stato Installazione:\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1779,\n                  columnNumber: 19\n                }, this), \" \", statoInstallazione]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 1778,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 1774,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Grid, {\n              item: true,\n              xs: 12,\n              md: 6,\n              children: [/*#__PURE__*/_jsxDEV(Typography, {\n                variant: \"body2\",\n                children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n                  children: \"Bobina Associata:\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1784,\n                  columnNumber: 19\n                }, this), \" \", numeroBobina]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 1783,\n                columnNumber: 17\n              }, this), bobinaInfo && /*#__PURE__*/_jsxDEV(Typography, {\n                variant: \"body2\",\n                children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n                  children: \"Metri Residui Bobina:\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1788,\n                  columnNumber: 21\n                }, this), \" \", bobinaInfo.metri_residui, \" m\"]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 1787,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 1782,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 1773,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 1769,\n          columnNumber: 11\n        }, this), bobinaInfo && parseFloat(formData.metri_posati) > parseFloat(bobinaInfo.metri_residui) && !notificationShown && /*#__PURE__*/_jsxDEV(Alert, {\n          severity: \"warning\",\n          sx: {\n            mt: 3\n          },\n          children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n            children: \"Attenzione:\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 1797,\n            columnNumber: 15\n          }, this), \" I metri posati (\", formData.metri_posati, \"m) superano i metri residui della bobina (\", bobinaInfo.metri_residui, \"m). Questo porter\\xE0 la bobina in stato OVER.\"]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 1796,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(Alert, {\n          severity: \"info\",\n          sx: {\n            mt: 3\n          },\n          children: [\"Conferma per aggiornare i metri posati e lo stato di installazione del cavo.\", formData.id_bobina && formData.id_bobina !== 'BOBINA_VUOTA' && ' I metri posati verranno sottratti dai metri residui della bobina selezionata.']\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 1802,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 1756,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 1751,\n      columnNumber: 7\n    }, this);\n  };\n\n  // Renderizza il contenuto in base al passo attivo\n  const getStepContent = step => {\n    switch (step) {\n      case 0:\n        return renderStep1();\n      // Seleziona Cavo\n      case 1:\n        return renderStep3();\n      // Associa Bobina\n      case 2:\n        return renderStep2();\n      // Inserisci Metri\n      case 3:\n        return renderStep4();\n      // Conferma\n      default:\n        return 'Passo sconosciuto';\n    }\n  };\n\n  // Gestisce la chiusura del dialogo per cavi già posati\n  const handleCloseAlreadyLaidDialog = () => {\n    setShowAlreadyLaidDialog(false);\n    setAlreadyLaidCavo(null);\n  };\n\n  // Gestisce l'opzione di modificare la bobina di un cavo già posato\n  const handleModifyReel = () => {\n    if (alreadyLaidCavo) {\n      navigate(`/dashboard/cavi/modifica-bobina/${cantiereId}/${alreadyLaidCavo.id_cavo}`);\n    }\n    handleCloseAlreadyLaidDialog();\n  };\n\n  // Gestisce l'opzione di selezionare un altro cavo\n  const handleSelectAnotherCable = () => {\n    handleCloseAlreadyLaidDialog();\n    // Reset del form per selezionare un nuovo cavo\n    setSelectedCavo(null);\n    setCavoIdInput('');\n    setShowSearchResults(false);\n  };\n\n  // Gestisce la chiusura del dialogo per bobine incompatibili\n  const handleCloseIncompatibleReelDialog = () => {\n    setShowIncompatibleReelDialog(false);\n    setIncompatibleReelData({\n      cavo: null,\n      bobina: null\n    });\n  };\n\n  // Gestisce l'aggiornamento delle caratteristiche del cavo per farle corrispondere a quelle della bobina\n  const handleUpdateCavoToMatchReel = async () => {\n    const {\n      cavo,\n      bobina\n    } = incompatibleReelData;\n    if (!cavo || !bobina) {\n      console.error('Dati mancanti per aggiornare il cavo:', {\n        cavo,\n        bobina\n      });\n      onError('Dati mancanti per aggiornare il cavo');\n      return;\n    }\n    try {\n      setLoading(true);\n      console.log(`Aggiornamento caratteristiche del cavo ${cavo.id_cavo} per compatibilità con bobina ${bobina.id_bobina}`);\n      console.log('Dati cavo prima dell\\'aggiornamento:', cavo);\n      console.log('Dati bobina:', bobina);\n\n      // Aggiorna le caratteristiche del cavo\n      await caviService.updateCavoForCompatibility(cantiereId, cavo.id_cavo, bobina.id_bobina);\n\n      // Aggiorna il cavo selezionato con le nuove caratteristiche\n      const updatedCavo = await caviService.getCavoById(cantiereId, cavo.id_cavo);\n      console.log('Dati cavo dopo l\\'aggiornamento:', updatedCavo);\n      setSelectedCavo(updatedCavo);\n\n      // Imposta la bobina selezionata\n      setFormData({\n        ...formData,\n        id_bobina: bobina.id_bobina\n      });\n\n      // Ricarica le bobine per aggiornare l'interfaccia\n      await loadBobine();\n      onSuccess(`Caratteristiche del cavo ${cavo.id_cavo} aggiornate per corrispondere alla bobina ${bobina.id_bobina}`);\n      handleCloseIncompatibleReelDialog();\n    } catch (error) {\n      console.error('Errore durante l\\'aggiornamento delle caratteristiche del cavo:', error);\n      onError('Errore durante l\\'aggiornamento delle caratteristiche del cavo: ' + (error.detail || error.message || 'Errore sconosciuto'));\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  // Gestisce la selezione di un'altra bobina\n  const handleSelectAnotherReel = () => {\n    handleCloseIncompatibleReelDialog();\n    // Reset della bobina selezionata\n    setFormData({\n      ...formData,\n      id_bobina: ''\n    });\n  };\n  return /*#__PURE__*/_jsxDEV(Box, {\n    children: [/*#__PURE__*/_jsxDEV(Box, {\n      sx: {\n        mb: 2\n      },\n      children: [/*#__PURE__*/_jsxDEV(Box, {\n        sx: {\n          display: 'flex',\n          alignItems: 'center',\n          mb: 1\n        },\n        children: [/*#__PURE__*/_jsxDEV(Typography, {\n          variant: \"subtitle2\",\n          sx: {\n            fontWeight: 'bold',\n            minWidth: '80px'\n          },\n          children: \"Cerca cavo\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 1915,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(TextField, {\n          size: \"small\",\n          value: cavoIdInput,\n          onChange: e => setCavoIdInput(e.target.value),\n          variant: \"outlined\",\n          sx: {\n            mr: 1,\n            flex: 1\n          }\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 1918,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Button, {\n          variant: \"contained\",\n          color: \"primary\",\n          onClick: handleSearchCavoById,\n          disabled: caviLoading || !cavoIdInput.trim(),\n          size: \"small\",\n          children: \"CERCA\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 1925,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 1914,\n        columnNumber: 9\n      }, this), selectedCavo && /*#__PURE__*/_jsxDEV(Box, {\n        sx: {\n          display: 'flex',\n          justifyContent: 'space-between',\n          width: '100%',\n          mt: 2\n        },\n        children: [/*#__PURE__*/_jsxDEV(Box, {\n          children: [/*#__PURE__*/_jsxDEV(Typography, {\n            variant: \"subtitle2\",\n            sx: {\n              fontWeight: 'bold'\n            },\n            children: [\"Cavo selezionato: \", /*#__PURE__*/_jsxDEV(\"span\", {\n              style: {\n                color: '#1976d2'\n              },\n              children: selectedCavo.id_cavo\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1941,\n              columnNumber: 35\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 1940,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(Box, {\n            sx: {\n              display: 'flex',\n              gap: 1\n            },\n            children: [/*#__PURE__*/_jsxDEV(Typography, {\n              variant: \"body2\",\n              children: [\"Tipologia: \", selectedCavo.tipologia || 'N/A']\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 1944,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(Typography, {\n              variant: \"body2\",\n              children: [\"Formazione: \", selectedCavo.sezione || 'N/A']\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 1945,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(Typography, {\n              variant: \"body2\",\n              children: [\"Metri teorici: \", selectedCavo.metri_teorici || 'N/A', \" m\"]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 1946,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 1943,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 1939,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(Box, {\n          children: /*#__PURE__*/_jsxDEV(Box, {\n            sx: {\n              display: 'flex',\n              flexDirection: 'column',\n              alignItems: 'flex-end'\n            },\n            children: [/*#__PURE__*/_jsxDEV(Typography, {\n              variant: \"body2\",\n              children: [\"Ubicazione partenza: \", selectedCavo.ubicazione_partenza || 'N/A']\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 1951,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(Typography, {\n              variant: \"body2\",\n              children: [\"Ubicazione arrivo: \", selectedCavo.ubicazione_arrivo || 'N/A']\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 1952,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(Typography, {\n              variant: \"body2\",\n              children: [\"Stato:\", /*#__PURE__*/_jsxDEV(Chip, {\n                size: \"small\",\n                label: selectedCavo.stato_installazione || 'N/D',\n                color: getCableStateColor(selectedCavo.stato_installazione),\n                sx: {\n                  ml: 1,\n                  height: '20px',\n                  '& .MuiChip-label': {\n                    px: 1,\n                    py: 0\n                  }\n                }\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1955,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 1953,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 1950,\n            columnNumber: 15\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 1949,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 1938,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(Divider, {\n        sx: {\n          my: 2\n        }\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 1967,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 1913,\n      columnNumber: 7\n    }, this), showSearchResults && searchResults.length > 0 && /*#__PURE__*/_jsxDEV(Paper, {\n      sx: {\n        p: 3,\n        mb: 3\n      },\n      children: [/*#__PURE__*/_jsxDEV(Typography, {\n        variant: \"h6\",\n        gutterBottom: true,\n        children: \"Risultati della ricerca\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 1973,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(TableContainer, {\n        children: /*#__PURE__*/_jsxDEV(Table, {\n          size: \"small\",\n          children: [/*#__PURE__*/_jsxDEV(TableHead, {\n            children: /*#__PURE__*/_jsxDEV(TableRow, {\n              sx: {\n                bgcolor: '#f5f5f5'\n              },\n              children: [/*#__PURE__*/_jsxDEV(TableCell, {\n                children: \"ID Cavo\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1980,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n                children: \"Tipologia\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1981,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n                children: \"Formazione\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1982,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n                children: \"Ubicazione\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1983,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n                children: \"Metri Teorici\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1984,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n                children: \"Stato\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1985,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n                children: \"Azioni\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1986,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 1979,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 1978,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(TableBody, {\n            children: searchResults.map(cavo => /*#__PURE__*/_jsxDEV(TableRow, {\n              children: [/*#__PURE__*/_jsxDEV(TableCell, {\n                children: cavo.id_cavo\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1992,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n                children: cavo.tipologia || 'N/A'\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1993,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n                children: cavo.sezione || 'N/A'\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1994,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n                children: [\"Da: \", cavo.ubicazione_partenza || 'N/A', /*#__PURE__*/_jsxDEV(\"br\", {}, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1995,\n                  columnNumber: 71\n                }, this), \"A: \", cavo.ubicazione_arrivo || 'N/A']\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 1995,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n                children: [cavo.metri_teorici || 'N/A', \" m\"]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 1996,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n                children: /*#__PURE__*/_jsxDEV(Chip, {\n                  label: cavo.stato_installazione || 'N/D',\n                  size: \"small\",\n                  color: getCableStateColor(cavo.stato_installazione),\n                  variant: \"outlined\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1998,\n                  columnNumber: 23\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1997,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n                children: /*#__PURE__*/_jsxDEV(Button, {\n                  size: \"small\",\n                  variant: \"contained\",\n                  color: \"primary\",\n                  onClick: () => handleCavoSelect(cavo),\n                  disabled: isCableInstalled(cavo),\n                  children: \"Seleziona\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 2006,\n                  columnNumber: 23\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 2005,\n                columnNumber: 21\n              }, this)]\n            }, cavo.id_cavo, true, {\n              fileName: _jsxFileName,\n              lineNumber: 1991,\n              columnNumber: 19\n            }, this))\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 1989,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 1977,\n          columnNumber: 13\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 1976,\n        columnNumber: 11\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 1972,\n      columnNumber: 9\n    }, this), selectedCavo && /*#__PURE__*/_jsxDEV(Box, {\n      children: renderStep3()\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 2025,\n      columnNumber: 9\n    }, this), /*#__PURE__*/_jsxDEV(Dialog, {\n      open: showConfirmDialog,\n      onClose: () => setShowConfirmDialog(false),\n      maxWidth: \"sm\",\n      fullWidth: true,\n      children: [/*#__PURE__*/_jsxDEV(DialogTitle, {\n        sx: {\n          bgcolor: 'warning.light'\n        },\n        children: /*#__PURE__*/_jsxDEV(Box, {\n          sx: {\n            display: 'flex',\n            alignItems: 'center',\n            gap: 1\n          },\n          children: [/*#__PURE__*/_jsxDEV(WarningIcon, {\n            color: \"warning\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 2034,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Typography, {\n            variant: \"h6\",\n            children: confirmDialogProps.title\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 2035,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 2033,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 2032,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(DialogContent, {\n        children: /*#__PURE__*/_jsxDEV(Typography, {\n          variant: \"body1\",\n          sx: {\n            mt: 2\n          },\n          children: confirmDialogProps.message\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 2039,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 2038,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(DialogActions, {\n        children: [/*#__PURE__*/_jsxDEV(Button, {\n          onClick: () => setShowConfirmDialog(false),\n          color: \"secondary\",\n          variant: \"outlined\",\n          children: \"Annulla\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 2044,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Button, {\n          onClick: () => {\n            setShowConfirmDialog(false);\n            confirmDialogProps.onConfirm();\n          },\n          color: \"primary\",\n          variant: \"contained\",\n          autoFocus: true,\n          children: \"Conferma\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 2047,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 2043,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 2031,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Dialog, {\n      open: showAlreadyLaidDialog,\n      onClose: handleCloseAlreadyLaidDialog,\n      maxWidth: \"sm\",\n      fullWidth: true,\n      children: [/*#__PURE__*/_jsxDEV(DialogTitle, {\n        sx: {\n          bgcolor: 'warning.light'\n        },\n        children: /*#__PURE__*/_jsxDEV(Box, {\n          sx: {\n            display: 'flex',\n            alignItems: 'center',\n            gap: 1\n          },\n          children: [/*#__PURE__*/_jsxDEV(WarningIcon, {\n            color: \"warning\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 2065,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Typography, {\n            variant: \"h6\",\n            children: \"Cavo gi\\xE0 posato\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 2066,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 2064,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 2063,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(DialogContent, {\n        children: alreadyLaidCavo && /*#__PURE__*/_jsxDEV(Box, {\n          sx: {\n            mt: 2\n          },\n          children: [/*#__PURE__*/_jsxDEV(Typography, {\n            variant: \"body1\",\n            paragraph: true,\n            children: [\"Il cavo \", /*#__PURE__*/_jsxDEV(\"strong\", {\n              children: alreadyLaidCavo.id_cavo\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 2073,\n              columnNumber: 25\n            }, this), \" risulta gi\\xE0 posato (\", alreadyLaidCavo.metratura_reale || 0, \"m).\"]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 2072,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(Typography, {\n            variant: \"body1\",\n            paragraph: true,\n            children: \"Puoi scegliere di:\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 2075,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(Typography, {\n            variant: \"body2\",\n            component: \"ul\",\n            children: [/*#__PURE__*/_jsxDEV(\"li\", {\n              children: \"Modificare la bobina associata al cavo\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 2079,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"li\", {\n              children: \"Selezionare un altro cavo\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 2080,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"li\", {\n              children: \"Annullare l'operazione\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 2081,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 2078,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 2071,\n          columnNumber: 13\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 2069,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(DialogActions, {\n        sx: {\n          p: 2,\n          justifyContent: 'space-between'\n        },\n        children: [/*#__PURE__*/_jsxDEV(Button, {\n          onClick: handleCloseAlreadyLaidDialog,\n          color: \"secondary\",\n          children: \"Annulla operazione\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 2087,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Box, {\n          children: [/*#__PURE__*/_jsxDEV(Button, {\n            onClick: handleSelectAnotherCable,\n            color: \"primary\",\n            sx: {\n              mr: 1\n            },\n            children: \"Seleziona altro cavo\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 2091,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Button, {\n            onClick: handleModifyReel,\n            variant: \"contained\",\n            color: \"primary\",\n            children: \"Modifica bobina\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 2094,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 2090,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 2086,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 2062,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(IncompatibleReelDialog, {\n      open: showIncompatibleReelDialog,\n      onClose: handleCloseIncompatibleReelDialog,\n      cavo: incompatibleReelData.cavo,\n      bobina: incompatibleReelData.bobina,\n      onUpdateCavo: handleUpdateCavoToMatchReel,\n      onSelectAnotherReel: handleSelectAnotherReel\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 2102,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Dialog, {\n      open: showCavoDetailsDialog,\n      onClose: () => setShowCavoDetailsDialog(false),\n      maxWidth: \"md\",\n      fullWidth: true,\n      children: [/*#__PURE__*/_jsxDEV(DialogTitle, {\n        children: /*#__PURE__*/_jsxDEV(Box, {\n          sx: {\n            display: 'flex',\n            alignItems: 'center',\n            gap: 1\n          },\n          children: [/*#__PURE__*/_jsxDEV(InfoIcon, {\n            color: \"primary\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 2120,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Typography, {\n            variant: \"h6\",\n            children: \"Dettagli Cavo\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 2121,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 2119,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 2118,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(DialogContent, {\n        children: /*#__PURE__*/_jsxDEV(CavoDetailsView, {\n          cavo: selectedCavo\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 2125,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 2124,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(DialogActions, {\n        children: /*#__PURE__*/_jsxDEV(Button, {\n          onClick: () => setShowCavoDetailsDialog(false),\n          color: \"primary\",\n          children: \"Chiudi\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 2128,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 2127,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 2112,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 1911,\n    columnNumber: 5\n  }, this);\n};\n_s(InserisciMetriForm, \"pRAk+BZQ/TWt/+FY7UaMJzV1xmY=\", false, function () {\n  return [useNavigate];\n});\n_c = InserisciMetriForm;\nexport default InserisciMetriForm;\nvar _c;\n$RefreshReg$(_c, \"InserisciMetriForm\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "axios", "Box", "Paper", "Typography", "TextField", "<PERSON><PERSON>", "Grid", "FormControl", "InputLabel", "Select", "MenuItem", "Divider", "<PERSON><PERSON>", "CircularProgress", "FormHelperText", "IconButton", "Chip", "InputAdornment", "Dialog", "DialogTitle", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "DialogActions", "Table", "TableBody", "TableCell", "TableContainer", "TableHead", "TableRow", "List", "ListItem", "ListItemButton", "ListItemText", "ListItemSecondaryAction", "Search", "SearchIcon", "Save", "SaveIcon", "Cancel", "CancelIcon", "Warning", "WarningIcon", "Info", "InfoIcon", "AddCircleOutline", "AddCircleOutlineIcon", "useNavigate", "caviService", "axiosInstance", "IncompatibleReelDialog", "CavoDetailsView", "CABLE_STATES", "REEL_STATES", "determineCableState", "determineReelState", "canModifyCable", "isCableSpare", "isCableInstalled", "getCableStateColor", "getReelStateColor", "parcoCaviService", "redirectToVisualizzaCavi", "jsxDEV", "_jsxDEV", "Fragment", "_Fragment", "InserisciMetriForm", "cantiereId", "onSuccess", "onError", "_s", "navigate", "loading", "setLoading", "caviLoading", "setCaviLoading", "bob<PERSON><PERSON><PERSON><PERSON>", "setBobineLoading", "searchResults", "setSearchResults", "showSearchResults", "setShowSearchResults", "cavi", "<PERSON><PERSON><PERSON>", "bobine", "set<PERSON>ob<PERSON>", "selected<PERSON><PERSON><PERSON>", "setSelectedCavo", "cavoIdInput", "setCavoIdInput", "activeStep", "setActiveStep", "formData", "setFormData", "id_cavo", "metri_posati", "id_bobina", "formErrors", "setFormErrors", "formWarnings", "setForm<PERSON><PERSON>nings", "showIncompatibleReelDialog", "setShowIncompatibleReelDialog", "incompatibleReel", "setIncompatibleReel", "incompatibleReelData", "setIncompatibleReelData", "cavo", "bobina", "showAlreadyLaidDialog", "setShowAlreadyLaidDialog", "tipologiaFilter", "setTipologiaFilter", "searchText", "setSearchText", "alreadyLaidCavo", "setAlreadyLaidCavo", "showCavoDetailsDialog", "setShowCavoDetailsDialog", "handleSelectBobina", "idBobina", "prev", "id_bobina_input", "handleTipologiaFilterChange", "event", "target", "value", "handleSearchTextChange", "loadBobine", "getBobinaNumber", "includes", "split", "loadCavi", "console", "log", "caviData", "get<PERSON><PERSON>", "length", "loadError", "error", "isNetworkError", "response", "code", "message", "Promise", "resolve", "setTimeout", "token", "localStorage", "getItem", "API_URL", "defaults", "baseURL", "retryResponse", "get", "headers", "timeout", "data", "retryError", "errorMessage", "detail", "bobine<PERSON><PERSON>", "getBobine", "b<PERSON><PERSON><PERSON><PERSON><PERSON>", "filter", "stato_bobina", "metri_residui", "tipologia", "sezione", "cavoTipologia", "String", "trim", "toLowerCase", "cavoSezione", "bobineCom<PERSON><PERSON><PERSON><PERSON>", "bobineNonCompatibili", "for<PERSON>ach", "bobinaTipologia", "bobinaSezione", "tipologiaMatch", "sezioneMatch", "isCompatible", "push", "sort", "a", "b", "bobineOrdinate", "handleSearchCavoById", "filteredCavi", "exactMatch", "find", "stato_installazione", "metratura_reale", "modificato_manualmente", "handleCavoSelect", "status", "window", "confirm", "reactivateSpare", "then", "updatedCavo", "catch", "n_conduttori", "cavoId", "handleFormChange", "e", "name", "cavoFormazione", "bobinaFormazione", "validateField", "warning", "isNaN", "parseFloat", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "metri_te<PERSON>ci", "notificationShown", "setNotificationShown", "showConfirmDialog", "setShowConfirmDialog", "confirmDialogProps", "setConfirmDialogProps", "title", "onConfirm", "validateForm", "<PERSON><PERSON><PERSON><PERSON>", "errors", "warnings", "handleNext", "prevActiveStep", "handleBack", "handleReset", "determineInstallationStatus", "metriTeorici", "handleSubmit", "statoInstallazione", "forceOver", "confirmMessage", "updateMetri<PERSON><PERSON><PERSON>", "successMessage", "success", "_error$response$data", "request", "_error$response$data2", "renderStep1", "children", "variant", "sx", "mb", "fontWeight", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "p", "width", "display", "alignItems", "mr", "min<PERSON><PERSON><PERSON>", "size", "label", "onChange", "placeholder", "flexGrow", "color", "onClick", "disabled", "startIcon", "fontSize", "height", "justifyContent", "my", "severity", "py", "component", "maxHeight", "overflow", "<PERSON><PERSON><PERSON><PERSON>", "bgcolor", "align", "map", "hover", "cursor", "px", "stopPropagation", "renderStep2", "flexWrap", "style", "gap", "ml", "gutterBottom", "borderRadius", "flex", "max<PERSON><PERSON><PERSON>", "mt", "type", "helperText", "FormHelperTextProps", "inputProps", "max", "step", "renderStep3", "tipologieUniche", "Set", "buildFullBobinaId", "numeroBobina", "hasSufficientMeters", "bobineFiltrate", "matchesTipologia", "searchLower", "matchesSearch", "handleBobinaNumberInput", "idBobinaCompleto", "<PERSON><PERSON><PERSON>", "formazione", "InputProps", "startAdornment", "position", "endAdornment", "edge", "id", "labelId", "onBlur", "container", "spacing", "item", "xs", "md", "disablePadding", "secondaryAction", "border", "dense", "primary", "secondary", "metri_totali", "renderStep4", "bobinaInfo", "compact", "getStepContent", "handleCloseAlreadyLaidDialog", "handleModifyReel", "handleSelectAnotherCable", "handleCloseIncompatibleReelDialog", "handleUpdateCavoToMatchReel", "updateCavoForCompatibility", "getCavoById", "handleSelectAnotherReel", "flexDirection", "ubicazione_partenza", "ubicazione_arrivo", "open", "onClose", "fullWidth", "autoFocus", "paragraph", "onUpdateCavo", "onSelectAnotherReel", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/PycharmProjects/CMS/webapp/frontend/src/components/cavi/InserisciMetriForm.js"], "sourcesContent": ["import React, { useState, useEffect } from 'react';\nimport axios from 'axios';\nimport {\n  Box,\n  Paper,\n  Typography,\n  TextField,\n  Button,\n  Grid,\n  FormControl,\n  InputLabel,\n  Select,\n  MenuItem,\n  Divider,\n  Alert,\n  CircularProgress,\n  FormHelperText,\n  IconButton,\n  Chip,\n  InputAdornment,\n  Dialog,\n  DialogTitle,\n  DialogContent,\n  DialogActions,\n  Table,\n  TableBody,\n  TableCell,\n  TableContainer,\n  TableHead,\n  TableRow,\n  List,\n  ListItem,\n  ListItemButton,\n  ListItemText,\n  ListItemSecondaryAction\n} from '@mui/material';\nimport {\n  Search as SearchIcon,\n  Save as SaveIcon,\n  Cancel as CancelIcon,\n  Warning as WarningIcon,\n  Info as InfoIcon,\n  AddCircleOutline as AddCircleOutlineIcon\n} from '@mui/icons-material';\nimport { useNavigate } from 'react-router-dom';\nimport caviService from '../../services/caviService';\nimport axiosInstance from '../../services/axiosConfig';\nimport IncompatibleReelDialog from './IncompatibleReelDialog';\nimport CavoDetailsView from './CavoDetailsView';\nimport {\n  CABLE_STATES,\n  REEL_STATES,\n  determineCableState,\n  determineReelState,\n  canModifyCable,\n  isCableSpare,\n  isCableInstalled,\n  getCableStateColor,\n  getReelStateColor\n} from '../../utils/stateUtils';\nimport parcoCaviService from '../../services/parcoCaviService';\nimport { redirectToVisualizzaCavi } from '../../utils/navigationUtils';\n\n/**\n * Componente per l'inserimento dei metri posati di un cavo\n * Versione semplificata con workflow compresso in un'unica pagina\n *\n * @param {Object} props - Proprietà del componente\n * @param {string} props.cantiereId - ID del cantiere\n * @param {Function} props.onSuccess - Funzione chiamata al successo dell'operazione\n * @param {Function} props.onError - Funzione chiamata in caso di errore\n */\nconst InserisciMetriForm = ({ cantiereId, onSuccess, onError }) => {\n  const navigate = useNavigate();\n\n  // Stati per la gestione del form\n  const [loading, setLoading] = useState(false);\n  const [caviLoading, setCaviLoading] = useState(false);\n  const [bobineLoading, setBobineLoading] = useState(false);\n  const [searchResults, setSearchResults] = useState([]);\n  const [showSearchResults, setShowSearchResults] = useState(false);\n\n  // Stati per i dati\n  const [cavi, setCavi] = useState([]);\n  const [bobine, setBobine] = useState([]);\n  const [selectedCavo, setSelectedCavo] = useState(null);\n  const [cavoIdInput, setCavoIdInput] = useState('');\n\n  // Stato per la gestione dei passi (mantenuto per compatibilità con funzioni esistenti)\n  const [activeStep, setActiveStep] = useState(0);\n\n  // Stati per il form\n  const [formData, setFormData] = useState({\n    id_cavo: '',\n    metri_posati: '',\n    id_bobina: ''\n  });\n\n  // Stati per la validazione\n  const [formErrors, setFormErrors] = useState({});\n  const [formWarnings, setFormWarnings] = useState({});\n\n  // Stati per i dialoghi speciali\n  const [showIncompatibleReelDialog, setShowIncompatibleReelDialog] = useState(false);\n  const [incompatibleReel, setIncompatibleReel] = useState(null); // Mantenuto per compatibilità\n  const [incompatibleReelData, setIncompatibleReelData] = useState({ cavo: null, bobina: null });\n  const [showAlreadyLaidDialog, setShowAlreadyLaidDialog] = useState(false);\n\n  // Stati per i filtri delle bobine\n  const [tipologiaFilter, setTipologiaFilter] = useState('');\n  const [searchText, setSearchText] = useState('');\n  const [alreadyLaidCavo, setAlreadyLaidCavo] = useState(null);\n  const [showCavoDetailsDialog, setShowCavoDetailsDialog] = useState(false);\n\n  // Gestisce la selezione di una bobina\n  const handleSelectBobina = (idBobina) => {\n    setFormData({\n      ...formData,\n      id_bobina: idBobina\n    });\n\n    // Reset degli errori\n    setFormErrors(prev => ({\n      ...prev,\n      id_bobina: null,\n      id_bobina_input: null\n    }));\n  };\n\n  // Gestisce il cambio del filtro tipologia\n  const handleTipologiaFilterChange = (event) => {\n    setTipologiaFilter(event.target.value);\n  };\n\n  // Gestisce il cambio del testo di ricerca\n  const handleSearchTextChange = (event) => {\n    setSearchText(event.target.value);\n  };\n\n  // Carica la lista delle bobine all'avvio\n  useEffect(() => {\n    loadBobine();\n  }, [cantiereId]);\n\n  // Funzione per estrarre il numero della bobina dall'ID completo\n  const getBobinaNumber = (idBobina) => {\n    // L'ID completo è nel formato C{id_cantiere}_B{numero_bobina}\n    if (idBobina && idBobina.includes('_B')) {\n      return idBobina.split('_B')[1];\n    }\n    return idBobina;\n  };\n\n  // Funzione per caricare i cavi\n  const loadCavi = async () => {\n    try {\n      setCaviLoading(true);\n      console.log(`Caricamento cavi per il cantiere ${cantiereId}...`);\n\n      // Carica tutti i cavi, inclusi quelli SPARE e installati\n      try {\n        const caviData = await caviService.getCavi(cantiereId);\n        console.log(`Caricati ${caviData.length} cavi`);\n\n        // Mostra tutti i cavi (da installare, in corso, installati e SPARE)\n        // Questo permette di vedere anche i cavi già installati per eventuale modifica della bobina\n        setCavi(caviData);\n      } catch (loadError) {\n        console.error('Errore nel primo tentativo di caricamento dei cavi:', loadError);\n\n        // Se è un errore di rete, prova un secondo tentativo dopo una breve pausa\n        if (loadError.isNetworkError || !loadError.response ||\n            loadError.code === 'ECONNABORTED' ||\n            (loadError.message && loadError.message.includes('Network Error'))) {\n\n          console.log('Errore di rete, tentativo di recupero dopo 1 secondo...');\n          await new Promise(resolve => setTimeout(resolve, 1000));\n\n          try {\n            // Secondo tentativo con timeout aumentato\n            const token = localStorage.getItem('token');\n            const API_URL = axiosInstance.defaults.baseURL;\n\n            // Usa l'ID cantiere originale per la richiesta alternativa\n            const retryResponse = await axios.get(\n              `${API_URL}/cavi/${cantiereId}`,\n              {\n                headers: {\n                  'Authorization': `Bearer ${token}`\n                },\n                timeout: 30000 // 30 secondi\n              }\n            );\n\n            console.log(`Recupero riuscito al secondo tentativo: ${retryResponse.data.length} cavi`);\n            setCavi(retryResponse.data);\n          } catch (retryError) {\n            console.error('Anche il secondo tentativo è fallito:', retryError);\n            throw retryError;\n          }\n        } else {\n          throw loadError;\n        }\n      }\n    } catch (error) {\n      console.error('Errore nel caricamento dei cavi:', error);\n\n      // Messaggio di errore più dettagliato\n      let errorMessage = 'Errore nel caricamento dei cavi';\n\n      if (error.isNetworkError) {\n        errorMessage = 'Errore di connessione al server. Verifica la rete e riprova.';\n      } else if (error.detail) {\n        errorMessage = error.detail;\n      } else if (error.message) {\n        errorMessage = error.message;\n      }\n\n      onError(errorMessage);\n    } finally {\n      setCaviLoading(false);\n    }\n  };\n\n  // Funzione per caricare le bobine\n  const loadBobine = async () => {\n    try {\n      setBobineLoading(true);\n      console.log('Caricamento bobine iniziato...');\n\n      // Carica tutte le bobine disponibili\n      const bobineData = await parcoCaviService.getBobine(cantiereId);\n      console.log(`Bobine caricate: ${bobineData.length}`);\n\n      // Filtra solo per stato (disponibile o in uso) e metri residui > 0\n      const bobineUtilizzabili = bobineData.filter(bobina =>\n        bobina.stato_bobina !== 'Terminata' &&\n        bobina.stato_bobina !== 'Over' &&\n        bobina.metri_residui > 0\n      );\n\n      console.log(`Bobine utilizzabili: ${bobineUtilizzabili.length}`);\n\n      // Se c'è un cavo selezionato, evidenzia le bobine compatibili ma mostra tutte\n      if (selectedCavo && selectedCavo.tipologia && selectedCavo.sezione) {\n        console.log('Cavo selezionato, evidenziando bobine compatibili...');\n        console.log('Dati cavo:', {\n          id_cavo: selectedCavo.id_cavo,\n          tipologia: selectedCavo.tipologia,\n          sezione: selectedCavo.sezione\n        });\n\n        // Ordina le bobine: prima le compatibili, poi le altre, tutte ordinate per metri residui\n        const cavoTipologia = String(selectedCavo.tipologia || '').trim().toLowerCase();\n        const cavoSezione = String(selectedCavo.sezione || '0').trim();\n\n        // Identifica le bobine compatibili\n        const bobineCompatibili = [];\n        const bobineNonCompatibili = [];\n\n        // Dividi le bobine in compatibili e non compatibili\n        bobineUtilizzabili.forEach(bobina => {\n          const bobinaTipologia = String(bobina.tipologia || '').trim().toLowerCase();\n          const bobinaSezione = String(bobina.sezione || '0').trim();\n\n          // Verifica compatibilità\n          const tipologiaMatch = bobinaTipologia === cavoTipologia;\n          const sezioneMatch = bobinaSezione === cavoSezione;\n          const isCompatible = tipologiaMatch && sezioneMatch;\n\n          console.log(`Verifica compatibilità bobina ${bobina.id_bobina}:`, {\n            'Tipologia bobina': `\"${bobina.tipologia}\"`,\n            'Tipologia cavo': `\"${selectedCavo.tipologia}\"`,\n            'Tipologie uguali?': tipologiaMatch,\n            'Sezione bobina': `\"${String(bobina.sezione)}\"`,\n            'Sezione cavo': `\"${String(selectedCavo.sezione)}\"`,\n            'Sezioni uguali?': sezioneMatch,\n            'Stato bobina': bobina.stato_bobina,\n            'Metri residui': bobina.metri_residui,\n            'Compatibile?': isCompatible\n          });\n\n          if (isCompatible) {\n            bobineCompatibili.push(bobina);\n          } else {\n            bobineNonCompatibili.push(bobina);\n          }\n        });\n\n        console.log(`Bobine compatibili trovate: ${bobineCompatibili.length}`);\n        console.log(`Bobine non compatibili: ${bobineNonCompatibili.length}`);\n\n        // Log dettagliato delle bobine compatibili\n        if (bobineCompatibili.length > 0) {\n          console.log('Dettaglio bobine compatibili:');\n          bobineCompatibili.forEach(bobina => {\n            console.log(`- ${bobina.id_bobina}: ${bobina.tipologia} / ${bobina.sezione} (${bobina.metri_residui}m)`);\n          });\n        } else {\n          console.log('ATTENZIONE: Nessuna bobina compatibile trovata!');\n        }\n\n        // Ordina entrambi gli array per metri residui (decrescente)\n        bobineCompatibili.sort((a, b) => b.metri_residui - a.metri_residui);\n        bobineNonCompatibili.sort((a, b) => b.metri_residui - a.metri_residui);\n\n        // Concatena gli array: prima le compatibili, poi le non compatibili\n        const bobineOrdinate = [...bobineCompatibili, ...bobineNonCompatibili];\n\n        // Imposta le bobine nel componente\n        setBobine(bobineOrdinate);\n      } else {\n        // Se non c'è un cavo selezionato, mostra tutte le bobine ordinate per metri residui\n        console.log('Nessun cavo selezionato, mostrando tutte le bobine...');\n        bobineUtilizzabili.sort((a, b) => b.metri_residui - a.metri_residui);\n        setBobine(bobineUtilizzabili);\n      }\n    } catch (error) {\n      console.error('Errore nel caricamento delle bobine:', error);\n      onError('Errore nel caricamento delle bobine: ' + (error.message || 'Errore sconosciuto'));\n    } finally {\n      setBobineLoading(false);\n    }\n  };\n\n  // Gestisce la ricerca di un cavo per ID o pattern\n  const handleSearchCavoById = async () => {\n    if (!cavoIdInput.trim()) {\n      onError('Inserisci un ID cavo valido');\n      return;\n    }\n\n    try {\n      setCaviLoading(true);\n      console.log(`Ricerca cavo con pattern: ${cavoIdInput.trim()} nel cantiere ${cantiereId}`);\n\n      // Cerca tutti i cavi che corrispondono al pattern\n      const caviData = await caviService.getCavi(cantiereId);\n\n      // Filtra i cavi in base all'input (ricerca parziale)\n      const filteredCavi = caviData.filter(cavo =>\n        cavo.id_cavo.toLowerCase().includes(cavoIdInput.trim().toLowerCase())\n      );\n\n      console.log(`Trovati ${filteredCavi.length} cavi corrispondenti al pattern`);\n\n      // Se c'è una corrispondenza esatta, seleziona direttamente quel cavo\n      const exactMatch = filteredCavi.find(cavo =>\n        cavo.id_cavo.toLowerCase() === cavoIdInput.trim().toLowerCase()\n      );\n\n      if (exactMatch) {\n        console.log('Trovata corrispondenza esatta:', exactMatch);\n\n        // Verifica se il cavo è già installato\n        if (exactMatch.stato_installazione === 'Installato' || (exactMatch.metratura_reale && exactMatch.metratura_reale > 0)) {\n          console.log('Cavo già installato, mostra dialogo:', exactMatch);\n          setAlreadyLaidCavo(exactMatch);\n          setShowAlreadyLaidDialog(true);\n          setCaviLoading(false);\n          return;\n        }\n\n        // Verifica se il cavo è SPARE\n        if (exactMatch.modificato_manualmente === 3) {\n          console.log('Cavo SPARE trovato:', exactMatch);\n          // Gestione del cavo SPARE - verrà gestito in handleCavoSelect\n        }\n\n        // Seleziona il cavo direttamente\n        handleCavoSelect(exactMatch);\n      } else if (filteredCavi.length > 0) {\n        // Mostra i risultati della ricerca in una tabella\n        setSearchResults(filteredCavi);\n        setShowSearchResults(true);\n      } else {\n        // Nessun cavo trovato\n        onError(`Nessun cavo trovato con pattern \"${cavoIdInput.trim()}\" nel cantiere ${cantiereId}`);\n      }\n    } catch (error) {\n      console.error('Errore nella ricerca del cavo:', error);\n\n      // Messaggio di errore più dettagliato\n      let errorMessage = 'Errore nella ricerca dei cavi';\n\n      if (error.isNetworkError) {\n        errorMessage = 'Errore di connessione al server. Verifica la rete e riprova.';\n      } else if (error.status === 404) {\n        errorMessage = `Cavo con ID \"${cavoIdInput.trim()}\" non trovato nel cantiere ${cantiereId}`;\n      } else if (error.detail) {\n        errorMessage = error.detail;\n      } else if (error.message) {\n        errorMessage = error.message;\n      }\n\n      onError(errorMessage);\n    } finally {\n      setCaviLoading(false);\n    }\n  };\n\n  // Gestisce la selezione di un cavo\n  const handleCavoSelect = (cavo) => {\n    // Verifica se il cavo è già installato\n    if (cavo.stato_installazione === 'Installato' || (cavo.metratura_reale && cavo.metratura_reale > 0)) {\n      // Mostra il dialogo per cavi già posati\n      setAlreadyLaidCavo(cavo);\n      setShowAlreadyLaidDialog(true);\n      return;\n    }\n    // Verifica se il cavo è SPARE (modificato_manualmente = 3)\n    else if (cavo.modificato_manualmente === 3) {\n      // Chiedi conferma all'utente per riattivare il cavo SPARE\n      if (window.confirm(`Il cavo ${cavo.id_cavo} è marcato come SPARE. Vuoi riattivarlo?`)) {\n        // Riattiva il cavo SPARE (imposta modificato_manualmente = 0)\n        reactivateSpare(cavo.id_cavo).then(() => {\n          // Aggiorna il cavo selezionato con modificato_manualmente = 0\n          const updatedCavo = { ...cavo, modificato_manualmente: 0 };\n          setSelectedCavo(updatedCavo);\n          setFormData({\n            ...formData,\n            id_cavo: updatedCavo.id_cavo,\n            metri_posati: ''\n          });\n          // Nascondi i risultati della ricerca\n          setShowSearchResults(false);\n\n          // Carica le bobine compatibili\n          loadBobine();\n        }).catch(error => {\n          console.error('Errore durante la riattivazione del cavo SPARE:', error);\n          onError('Errore durante la riattivazione del cavo SPARE: ' + (error.message || 'Errore sconosciuto'));\n        });\n      } else {\n        // L'utente ha annullato la riattivazione\n        return;\n      }\n    } else {\n      // Cavo normale (non SPARE e non installato)\n      setSelectedCavo(cavo);\n      setFormData({\n        ...formData,\n        id_cavo: cavo.id_cavo,\n        metri_posati: ''\n      });\n      // Nascondi i risultati della ricerca\n      setShowSearchResults(false);\n\n      // Carica le bobine compatibili\n      if (cavo.tipologia && cavo.n_conduttori && cavo.sezione) {\n        console.log(`Caricamento bobine compatibili per il cavo ${cavo.id_cavo}...`);\n        loadBobine();\n      }\n    }\n  };\n\n  // Funzione per riattivare un cavo SPARE\n  const reactivateSpare = async (cavoId) => {\n    try {\n      // Chiamata API per riattivare il cavo SPARE\n      await caviService.reactivateSpare(cantiereId, cavoId);\n      onSuccess(`Cavo ${cavoId} riattivato con successo`);\n      return true;\n    } catch (error) {\n      console.error('Errore durante la riattivazione del cavo SPARE:', error);\n      onError('Errore durante la riattivazione del cavo SPARE: ' + (error.message || 'Errore sconosciuto'));\n      throw error;\n    }\n  };\n\n  // Gestisce il cambio dei valori nel form\n  const handleFormChange = (e) => {\n    const { name, value } = e.target;\n\n    // Gestione speciale per il cambio di bobina\n    if (name === 'id_bobina' && value && value !== 'BOBINA_VUOTA') {\n      // Verifica compatibilità tra cavo e bobina\n      const bobina = bobine.find(b => b.id_bobina === value);\n\n      if (bobina && selectedCavo) {\n        // Nella nuova configurazione, controlliamo solo tipologia e formazione (sezione)\n        // Utilizziamo una comparazione più robusta con trim() e gestione di null/undefined\n        const isCompatible =\n          String(bobina.tipologia || '').trim() === String(selectedCavo.tipologia || '').trim() &&\n          String(bobina.sezione || '0').trim() === String(selectedCavo.sezione || '0').trim();\n\n        if (!isCompatible) {\n          console.log('Bobina incompatibile selezionata:', bobina);\n          console.log('Cavo corrente:', selectedCavo);\n          console.log('Confronto formazione:', {\n            cavoFormazione: String(selectedCavo.sezione || '0'),\n            bobinaFormazione: String(bobina.sezione || '0')\n          });\n\n          // Mostra il dialogo di incompatibilità\n          setIncompatibleReelData({\n            cavo: selectedCavo,\n            bobina: bobina\n          });\n          setShowIncompatibleReelDialog(true);\n\n          // Non aggiornare il valore del form finché l'utente non conferma\n          return;\n        }\n      }\n    }\n\n    setFormData({\n      ...formData,\n      [name]: value\n    });\n\n    // Validazione in tempo reale\n    validateField(name, value);\n  };\n\n  // Validazione di un campo\n  const validateField = (name, value) => {\n    let error = null;\n    let warning = null;\n\n    if (name === 'metri_posati') {\n      // Controllo input vuoto\n      if (!value || value.trim() === '') {\n        error = 'Inserire un valore per i metri posati';\n        return false;\n      }\n\n      // Controllo formato numerico\n      if (isNaN(parseFloat(value)) || parseFloat(value) <= 0) {\n        error = 'Inserire un valore numerico positivo';\n        return false;\n      }\n\n      const metriPosati = parseFloat(value);\n\n      // Controllo metri teorici cavo\n      if (selectedCavo && selectedCavo.metri_teorici && metriPosati > parseFloat(selectedCavo.metri_teorici)) {\n        warning = `I metri posati (${metriPosati}m) superano i metri teorici del cavo (${selectedCavo.metri_teorici}m)`;\n      }\n\n      // Controllo metri residui bobina (se bobina reale)\n      if (formData.id_bobina && formData.id_bobina !== 'BOBINA_VUOTA') {\n        const bobina = bobine.find(b => b.id_bobina === formData.id_bobina);\n        if (bobina && metriPosati > parseFloat(bobina.metri_residui)) {\n          warning = `I metri posati (${metriPosati}m) superano i metri residui della bobina (${bobina.metri_residui}m). La bobina andrà in stato OVER.`;\n        }\n      }\n    } else if (name === 'id_bobina') {\n      // Controllo che sia selezionata una bobina\n      if (!value || value.trim() === '') {\n        error = 'È necessario selezionare una bobina';\n        return false;\n      }\n    }\n\n    // Aggiorna gli errori\n    setFormErrors(prev => ({\n      ...prev,\n      [name]: error\n    }));\n\n    // Aggiorna gli avvisi\n    setFormWarnings(prev => ({\n      ...prev,\n      [name]: warning\n    }));\n\n    return !error;\n  };\n\n  // Stati per i dialoghi di conferma\n  const [notificationShown, setNotificationShown] = useState(false);\n  const [showConfirmDialog, setShowConfirmDialog] = useState(false);\n  const [confirmDialogProps, setConfirmDialogProps] = useState({\n    title: '',\n    message: '',\n    onConfirm: () => {}\n  });\n\n  // Nota: Lo stato per il dialogo di bobina incompatibile è già dichiarato sopra\n\n  // Validazione completa del form\n  const validateForm = () => {\n    let isValid = true;\n    const errors = {};\n    const warnings = {};\n\n    // Reset della variabile di notifica\n    setNotificationShown(false);\n\n    // Validazione metri posati\n    if (!formData.metri_posati || formData.metri_posati.trim() === '') {\n      errors.metri_posati = 'Inserire un valore per i metri posati';\n      isValid = false;\n    } else if (isNaN(parseFloat(formData.metri_posati)) || parseFloat(formData.metri_posati) <= 0) {\n      errors.metri_posati = 'Inserire un valore numerico positivo';\n      isValid = false;\n    }\n\n    // Validazione bobina (deve essere selezionata)\n    if (!formData.id_bobina || formData.id_bobina.trim() === '') {\n      errors.id_bobina = 'È necessario selezionare una bobina';\n      isValid = false;\n    }\n\n    if (isValid) {\n      const metriPosati = parseFloat(formData.metri_posati);\n\n      // Controllo metri teorici cavo\n      if (selectedCavo && selectedCavo.metri_teorici && metriPosati > parseFloat(selectedCavo.metri_teorici)) {\n        warnings.metri_posati = `I metri posati (${metriPosati}m) superano i metri teorici del cavo (${selectedCavo.metri_teorici}m)`;\n        setNotificationShown(true); // Imposta la variabile per evitare notifiche duplicate\n        // Non mostrare più il popup di conferma, solo l'avviso nel form\n        // Continua con la validazione senza interrompere il flusso\n      }\n\n      // Controllo metri residui bobina (se bobina reale)\n      if (isValid && formData.id_bobina && formData.id_bobina !== 'BOBINA_VUOTA') {\n        const bobina = bobine.find(b => b.id_bobina === formData.id_bobina);\n        if (bobina && metriPosati > parseFloat(bobina.metri_residui)) {\n          warnings.metri_posati = `I metri posati (${metriPosati}m) superano i metri residui della bobina (${bobina.metri_residui}m). La bobina andrà in stato OVER.`;\n          setNotificationShown(true); // Imposta la variabile per evitare notifiche duplicate\n\n          // Mostra il dialogo di conferma invece di window.confirm\n          setConfirmDialogProps({\n            title: 'Attenzione: Bobina in stato OVER',\n            message: `I metri posati (${metriPosati}m) superano i metri residui della bobina (${bobina.metri_residui}m). Questo porterà la bobina in stato OVER. Vuoi continuare?`,\n            onConfirm: () => {\n              // Continua con la validazione\n              handleNext();\n            }\n          });\n          setShowConfirmDialog(true);\n          return false; // Interrompi la validazione fino alla conferma dell'utente\n        }\n      }\n    }\n\n    setFormErrors(errors);\n    setFormWarnings(warnings);\n    return isValid;\n  };\n\n  // Gestisce il passaggio al passo successivo\n  const handleNext = () => {\n    if (activeStep === 2) {\n      // Validazione prima di passare al passo successivo (da Inserisci Metri a Conferma)\n      if (!validateForm()) {\n        return;\n      }\n    } else if (activeStep === 1) {\n      // Carica le bobine prima di passare al passo successivo (da Associa Bobina a Inserisci Metri)\n      loadBobine();\n    }\n\n    setActiveStep((prevActiveStep) => prevActiveStep + 1);\n  };\n\n  // Gestisce il ritorno al passo precedente\n  const handleBack = () => {\n    setActiveStep((prevActiveStep) => prevActiveStep - 1);\n  };\n\n  // Gestisce il reset del form\n  const handleReset = () => {\n    setActiveStep(0);\n    setSelectedCavo(null);\n    setCavoIdInput('');\n    setFormData({\n      id_cavo: '',\n      metri_posati: '',\n      id_bobina: ''\n    });\n    setFormErrors({});\n    setFormWarnings({});\n  };\n\n  // Determina lo stato di installazione in base ai metri posati\n  // Utilizziamo la funzione dalla utility stateUtils\n  const determineInstallationStatus = (metriPosati, metriTeorici) => {\n    return determineCableState(metriPosati, metriTeorici);\n  };\n\n  // Gestisce l'invio del form\n  const handleSubmit = async () => {\n    // Dichiarazione delle variabili al di fuori del blocco try/catch\n    // in modo che siano accessibili anche nel blocco catch\n    let idBobina;\n    let statoInstallazione;\n    let metriPosati;\n    let forceOver = false;\n\n    try {\n      setLoading(true);\n\n      // Validazione finale\n      if (!validateForm()) {\n        setLoading(false);\n        return;\n      }\n\n      // Prepara i dati da inviare\n      metriPosati = parseFloat(formData.metri_posati);\n\n      // Gestione speciale per BOBINA_VUOTA\n      idBobina = formData.id_bobina;\n\n      // Gestione differenziata per cavi non posati e cavi posati\n      if (!idBobina || idBobina === '') {\n        // È necessario selezionare una bobina, anche BOBINA_VUOTA\n        setFormErrors({\n          ...formErrors,\n          id_bobina: 'È necessario selezionare una bobina'\n        });\n        setLoading(false);\n        return;\n      } else if (idBobina === 'BOBINA_VUOTA') {\n        // Per cavi posati senza bobina specifica\n        console.log('Usando BOBINA_VUOTA per il cavo');\n        // Assicurati che BOBINA_VUOTA venga passato come stringa e non come null\n        idBobina = 'BOBINA_VUOTA';\n      } else {\n        // Per cavi posati con bobina specifica\n        console.log(`Usando bobina reale: ${idBobina}`);\n      }\n\n      // Determina lo stato di installazione\n      statoInstallazione = determineInstallationStatus(metriPosati, selectedCavo.metri_teorici);\n\n      // Imposta sempre forceOver a true per evitare blocchi quando la bobina va in OVER\n      forceOver = true;\n      console.log('Impostando forceOver a true per garantire il completamento dell\\'operazione');\n\n      // Log delle condizioni che richiederebbero forceOver\n      if (idBobina === 'BOBINA_VUOTA') {\n        console.log('Operazione con BOBINA_VUOTA');\n      }\n      // Per bobine reali, verifica i metri residui\n      else if (idBobina && idBobina !== 'BOBINA_VUOTA') {\n        const bobina = bobine.find(b => b.id_bobina === idBobina);\n        if (bobina && metriPosati > parseFloat(bobina.metri_residui)) {\n          console.log(`La bobina ${idBobina} andrà in stato OVER (metri posati ${metriPosati} > metri residui ${bobina.metri_residui})`);\n        }\n      }\n\n      // Verifica anche se i metri posati superano i metri teorici\n      if (selectedCavo && selectedCavo.metri_teorici && metriPosati > parseFloat(selectedCavo.metri_teorici)) {\n        console.log(`I metri posati (${metriPosati}) superano i metri teorici (${selectedCavo.metri_teorici})`);\n      }\n\n      // Log per debug\n      console.log('Invio dati:', {\n        cantiereId,\n        cavoId: formData.id_cavo,\n        metriPosati,\n        idBobina,\n        forceOver,\n        statoInstallazione\n      });\n\n      // Conferma finale prima dell'invio solo se non è già stata mostrata una notifica\n      if (!notificationShown) {\n        const confirmMessage = `Confermi l'aggiornamento del cavo ${formData.id_cavo} con ${metriPosati}m posati?`;\n\n        // Usa il dialogo di conferma invece di window.confirm\n        setConfirmDialogProps({\n          title: 'Conferma aggiornamento',\n          message: confirmMessage,\n          onConfirm: async () => {\n            // Esegui la chiamata API qui invece che nel flusso principale\n            try {\n              setLoading(true);\n\n              // Imposta sempre forceOver a true per evitare blocchi quando la bobina va in OVER\n              console.log('Impostando forceOver a true nel dialogo di conferma per garantire il completamento dell\\'operazione');\n              await caviService.updateMetriPosati(\n                cantiereId,\n                formData.id_cavo,\n                metriPosati,\n                idBobina,\n                true // Forza sempre a true per evitare blocchi\n              );\n\n              // Messaggio di successo con dettagli sulla bobina\n              let successMessage = `Metri posati aggiornati con successo. Stato cavo: ${statoInstallazione}`;\n              if (idBobina === 'BOBINA_VUOTA') {\n                successMessage += '. Cavo associato a BOBINA VUOTA';\n              } else if (idBobina) {\n                const bobina = bobine.find(b => b.id_bobina === idBobina);\n                if (bobina) {\n                  successMessage += `. Cavo associato alla bobina ${idBobina}`;\n                }\n              }\n\n              // Gestione successo\n              onSuccess(successMessage);\n\n              // Reset del form\n              handleReset();\n\n              // Ricarica i cavi\n              loadCavi();\n            } catch (error) {\n              console.error('Errore durante l\\'aggiornamento dei metri posati:', error);\n\n              // Gestione speciale per BOBINA_VUOTA\n              if (idBobina === 'BOBINA_VUOTA' && error.success) {\n                // Se è un \"errore\" di successo per BOBINA_VUOTA, trattiamolo come un successo\n                let successMessage = `Metri posati aggiornati con successo. Stato cavo: ${statoInstallazione}. Cavo associato a BOBINA VUOTA`;\n                onSuccess(successMessage);\n\n                // Reset del form\n                handleReset();\n\n                // Ricarica i cavi\n                loadCavi();\n                return;\n              }\n\n              // Gestione dettagliata degli errori\n              let errorMessage = 'Errore durante l\\'aggiornamento dei metri posati';\n\n              if (error.response) {\n                // Il server ha risposto con un codice di errore\n                const status = error.response.status;\n                const detail = error.response.data?.detail || error.message;\n\n                if (status === 400) {\n                  // Errore di validazione\n                  if (detail.includes('metri residui')) {\n                    errorMessage = `La bobina non ha metri residui sufficienti. Usa l'opzione \"BOBINA VUOTA\" o seleziona un'altra bobina.`;\n                  } else if (detail.includes('già posato')) {\n                    errorMessage = `Il cavo risulta già posato. Usa la funzione \"Modifica bobina cavo posato\".`;\n                  } else {\n                    errorMessage = detail;\n                  }\n                } else if (status === 404) {\n                  // Gestione speciale per BOBINA_VUOTA quando la bobina non viene trovata\n                  if (idBobina === 'BOBINA_VUOTA' && detail.includes('non trovata')) {\n                    let successMessage = `Metri posati aggiornati con successo. Stato cavo: ${statoInstallazione}. Cavo associato a BOBINA VUOTA`;\n                    onSuccess(successMessage);\n\n                    // Reset del form\n                    handleReset();\n\n                    // Ricarica i cavi\n                    loadCavi();\n                    return;\n                  } else {\n                    errorMessage = `Cavo o bobina non trovati: ${detail}`;\n                  }\n                } else {\n                  errorMessage = `Errore del server (${status}): ${detail}`;\n                }\n              } else if (error.request) {\n                // La richiesta è stata inviata ma non è stata ricevuta risposta\n                errorMessage = 'Nessuna risposta dal server. Verifica la connessione di rete.';\n              } else if (error.detail && idBobina === 'BOBINA_VUOTA') {\n                // Gestione speciale per errori con BOBINA_VUOTA\n                errorMessage = error.detail;\n                if (error.status === 200 || error.success) {\n                  let successMessage = `Metri posati aggiornati con successo. Stato cavo: ${statoInstallazione}. Cavo associato a BOBINA VUOTA`;\n                  onSuccess(successMessage);\n\n                  // Reset del form\n                  handleReset();\n\n                  // Ricarica i cavi\n                  loadCavi();\n                  return;\n                }\n              } else {\n                // Errore durante la configurazione della richiesta\n                errorMessage = error.message || error.detail || 'Errore sconosciuto';\n              }\n\n              onError(errorMessage);\n            } finally {\n              setLoading(false);\n            }\n          }\n        });\n        setShowConfirmDialog(true);\n        setLoading(false);\n        return;\n      }\n\n      // Chiamata API\n      console.log('Invio richiesta updateMetriPosati con i seguenti parametri:');\n      console.log('- cantiereId:', cantiereId);\n      console.log('- id_cavo:', formData.id_cavo);\n      console.log('- metri_posati:', metriPosati);\n      console.log('- id_bobina:', idBobina, typeof idBobina);\n      console.log('- forceOver:', forceOver);\n\n      // Imposta sempre forceOver a true per evitare blocchi quando la bobina va in OVER\n      console.log('Impostando forceOver a true nella chiamata diretta per garantire il completamento dell\\'operazione');\n      await caviService.updateMetriPosati(\n        cantiereId,\n        formData.id_cavo,\n        metriPosati,\n        idBobina,\n        true // Forza sempre a true per evitare blocchi\n      );\n\n      // Messaggio di successo con dettagli sulla bobina\n      let successMessage = `Metri posati aggiornati con successo. Stato cavo: ${statoInstallazione}`;\n      if (idBobina === 'BOBINA_VUOTA') {\n        successMessage += '. Cavo associato a BOBINA VUOTA';\n      } else if (idBobina) {\n        const bobina = bobine.find(b => b.id_bobina === idBobina);\n        if (bobina) {\n          successMessage += `. Cavo associato alla bobina ${idBobina}`;\n        }\n      }\n\n      // Gestione successo\n      onSuccess(successMessage);\n\n      // Reset del form\n      handleReset();\n\n      // Ricarica i cavi\n      loadCavi();\n    } catch (error) {\n      console.error('Errore durante l\\'aggiornamento dei metri posati:', error);\n\n      // Gestione speciale per BOBINA_VUOTA\n      if (idBobina === 'BOBINA_VUOTA' && error.success) {\n        // Se è un \"errore\" di successo per BOBINA_VUOTA, trattiamolo come un successo\n        let successMessage = `Metri posati aggiornati con successo. Stato cavo: ${statoInstallazione}. Cavo associato a BOBINA VUOTA`;\n        onSuccess(successMessage);\n\n        // Reset del form\n        handleReset();\n\n        // Ricarica i cavi\n        loadCavi();\n        return;\n      }\n\n      // Gestione dettagliata degli errori\n      let errorMessage = 'Errore durante l\\'aggiornamento dei metri posati';\n\n      if (error.response) {\n        // Il server ha risposto con un codice di errore\n        const status = error.response.status;\n        const detail = error.response.data?.detail || error.message;\n\n        if (status === 400) {\n          // Errore di validazione\n          if (detail.includes('metri residui')) {\n            errorMessage = `La bobina non ha metri residui sufficienti. Usa l'opzione \"BOBINA VUOTA\" o seleziona un'altra bobina.`;\n          } else if (detail.includes('già posato')) {\n            errorMessage = `Il cavo risulta già posato. Usa la funzione \"Modifica bobina cavo posato\".`;\n          } else {\n            errorMessage = detail;\n          }\n        } else if (status === 404) {\n          // Gestione speciale per BOBINA_VUOTA quando la bobina non viene trovata\n          if (idBobina === 'BOBINA_VUOTA' && detail.includes('non trovata')) {\n            let successMessage = `Metri posati aggiornati con successo. Stato cavo: ${statoInstallazione}. Cavo associato a BOBINA VUOTA`;\n            onSuccess(successMessage);\n\n            // Reset del form\n            handleReset();\n\n            // Ricarica i cavi\n            loadCavi();\n            return;\n          } else {\n            errorMessage = `Cavo o bobina non trovati: ${detail}`;\n          }\n        } else {\n          errorMessage = `Errore del server (${status}): ${detail}`;\n        }\n      } else if (error.request) {\n        // La richiesta è stata inviata ma non è stata ricevuta risposta\n        errorMessage = 'Nessuna risposta dal server. Verifica la connessione di rete.';\n      } else if (error.detail && idBobina === 'BOBINA_VUOTA') {\n        // Gestione speciale per errori con BOBINA_VUOTA\n        errorMessage = error.detail;\n        if (error.status === 200 || error.success) {\n          let successMessage = `Metri posati aggiornati con successo. Stato cavo: ${statoInstallazione}. Cavo associato a BOBINA VUOTA`;\n          onSuccess(successMessage);\n\n          // Reset del form\n          handleReset();\n\n          // Ricarica i cavi\n          loadCavi();\n          return;\n        }\n      } else {\n        // Errore durante la configurazione della richiesta\n        errorMessage = error.message || error.detail || 'Errore sconosciuto';\n      }\n\n      onError(errorMessage);\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  // Renderizza il passo 1: Selezione del cavo\n  const renderStep1 = () => {\n    return (\n      <Box>\n        <Typography variant=\"subtitle2\" sx={{ mb: 1, fontWeight: 'bold' }}>\n          Seleziona un cavo\n        </Typography>\n\n        {/* Ricerca per ID - Versione compatta */}\n        <Paper sx={{ p: 1.5, mb: 2, width: '100%' }}>\n          <Box sx={{ display: 'flex', alignItems: 'center', width: '100%' }}>\n            <Typography variant=\"subtitle2\" sx={{ mr: 1, minWidth: '80px' }}>\n              Cerca cavo\n            </Typography>\n            <TextField\n              size=\"small\"\n              label=\"ID Cavo\"\n              variant=\"outlined\"\n              value={cavoIdInput}\n              onChange={(e) => setCavoIdInput(e.target.value)}\n              placeholder=\"Inserisci l'ID del cavo\"\n              sx={{ flexGrow: 1, mr: 1 }}\n            />\n            <Button\n              variant=\"contained\"\n              color=\"primary\"\n              onClick={handleSearchCavoById}\n              disabled={caviLoading || !cavoIdInput.trim()}\n              startIcon={caviLoading ? <CircularProgress size={16} /> : <SearchIcon fontSize=\"small\" />}\n              size=\"small\"\n              sx={{ minWidth: '80px', height: '36px' }}\n            >\n              CERCA\n            </Button>\n          </Box>\n        </Paper>\n\n        {/* Lista cavi - versione compatta */}\n        <Paper sx={{ p: 1.5, width: '100%' }}>\n          <Typography variant=\"subtitle2\" sx={{ mb: 1 }}>\n            Seleziona dalla lista\n          </Typography>\n\n          {caviLoading ? (\n            <Box sx={{ display: 'flex', justifyContent: 'center', my: 2 }}>\n              <CircularProgress size={24} />\n            </Box>\n          ) : cavi.length === 0 ? (\n            <Alert severity=\"info\" sx={{ py: 0.5 }}>\n              <Typography variant=\"caption\">Non ci sono cavi disponibili da installare.</Typography>\n            </Alert>\n          ) : (\n            <TableContainer component={Paper} variant=\"outlined\" sx={{ maxHeight: '300px', overflow: 'auto', width: '100%' }}>\n              <Table size=\"small\" stickyHeader>\n                <TableHead>\n                  <TableRow sx={{ '& th': { fontWeight: 'bold', py: 1, bgcolor: '#f5f5f5' } }}>\n                    <TableCell>ID Cavo</TableCell>\n                    <TableCell>Tipologia</TableCell>\n                    <TableCell>Formazione</TableCell>\n                    <TableCell>Metri</TableCell>\n                    <TableCell>Stato</TableCell>\n                    <TableCell align=\"center\" sx={{ width: '40px' }}>Info</TableCell>\n                  </TableRow>\n                </TableHead>\n                <TableBody>\n                  {cavi.map((cavo) => (\n                    <TableRow\n                      key={cavo.id_cavo}\n                      hover\n                      onClick={() => handleCavoSelect(cavo)}\n                      sx={{\n                        cursor: 'pointer',\n                        '&:hover': { bgcolor: '#f1f8e9' },\n                        '& td': { py: 0.5 }\n                      }}\n                    >\n                      <TableCell sx={{ fontWeight: 'medium' }}>{cavo.id_cavo}</TableCell>\n                      <TableCell>{cavo.tipologia || 'N/A'}</TableCell>\n                      <TableCell>{cavo.sezione || 'N/A'}</TableCell>\n                      <TableCell>{cavo.metri_teorici || 'N/A'}</TableCell>\n                      <TableCell>\n                        <Chip\n                          size=\"small\"\n                          label={isCableSpare(cavo) ? 'SPARE' : isCableInstalled(cavo) ? 'Installato' : cavo.stato_installazione}\n                          color={isCableSpare(cavo) ? 'error' : isCableInstalled(cavo) ? 'success' : getCableStateColor(cavo.stato_installazione)}\n                          sx={{ height: '20px', '& .MuiChip-label': { px: 1, py: 0, fontSize: '0.7rem' } }}\n                        />\n                      </TableCell>\n                      <TableCell align=\"center\">\n                        <IconButton\n                          size=\"small\"\n                          onClick={(e) => {\n                            e.stopPropagation();\n                            setSelectedCavo(cavo);\n                            setShowCavoDetailsDialog(true);\n                          }}\n                        >\n                          <InfoIcon fontSize=\"small\" />\n                        </IconButton>\n                      </TableCell>\n                    </TableRow>\n                  ))}\n                </TableBody>\n              </Table>\n            </TableContainer>\n          )}\n        </Paper>\n      </Box>\n    );\n  };\n\n  // Renderizza il passo 2: Inserimento metri\n  const renderStep2 = () => {\n    if (!selectedCavo) return null;\n\n    return (\n      <Box>\n        <Typography variant=\"subtitle2\" sx={{ mb: 1, fontWeight: 'bold' }}>\n          Inserisci metri posati\n        </Typography>\n\n        {/* Dettagli cavo in formato compatto */}\n        <Paper sx={{ p: 1.5, mb: 2, width: '100%' }}>\n          <Box sx={{ display: 'flex', flexWrap: 'wrap', alignItems: 'center', width: '100%' }}>\n            <Typography variant=\"subtitle2\" sx={{ mr: 2, fontWeight: 'bold' }}>\n              Cavo selezionato: <span style={{ color: '#1976d2' }}>{selectedCavo.id_cavo}</span>\n            </Typography>\n\n            <Box sx={{ display: 'flex', flexWrap: 'wrap', gap: 2, ml: 'auto' }}>\n              <Box sx={{ display: 'flex', alignItems: 'center' }}>\n                <Typography variant=\"body2\" sx={{ mr: 0.5, fontWeight: 'medium' }}>Tipologia:</Typography>\n                <Typography variant=\"body2\">{selectedCavo.tipologia || 'N/A'}</Typography>\n              </Box>\n\n              <Box sx={{ display: 'flex', alignItems: 'center' }}>\n                <Typography variant=\"body2\" sx={{ mr: 0.5, fontWeight: 'medium' }}>Formazione:</Typography>\n                <Typography variant=\"body2\">{selectedCavo.sezione || 'N/A'}</Typography>\n              </Box>\n\n              <Box sx={{ display: 'flex', alignItems: 'center' }}>\n                <Typography variant=\"body2\" sx={{ mr: 0.5, fontWeight: 'medium' }}>Metri teorici:</Typography>\n                <Typography variant=\"body2\">{selectedCavo.metri_teorici || 'N/A'} m</Typography>\n              </Box>\n\n              <Box sx={{ display: 'flex', alignItems: 'center' }}>\n                <Typography variant=\"body2\" sx={{ mr: 0.5, fontWeight: 'medium' }}>Stato:</Typography>\n                <Chip\n                  label={selectedCavo.stato_installazione || 'N/D'}\n                  size=\"small\"\n                  color={getCableStateColor(selectedCavo.stato_installazione)}\n                  variant=\"outlined\"\n                  sx={{ height: '20px', '& .MuiChip-label': { px: 1, py: 0 } }}\n                />\n              </Box>\n            </Box>\n          </Box>\n        </Paper>\n\n        <Paper sx={{ p: 2, width: '100%' }}>\n          <Typography variant=\"subtitle2\" gutterBottom sx={{ fontWeight: 'bold' }}>\n            Inserisci i metri posati\n          </Typography>\n\n          {/* Informazioni sul cavo e sulla bobina in una riga */}\n          <Box sx={{ display: 'flex', flexWrap: 'wrap', gap: 2, mb: 2 }}>\n            <Box sx={{ p: 1, bgcolor: '#f5f5f5', borderRadius: 1, flex: '1 1 auto', minWidth: '200px', maxWidth: '400px' }}>\n              <Typography variant=\"caption\" sx={{ fontWeight: 'bold', color: 'primary.main', display: 'block', mb: 0.5 }}>\n                Informazioni cavo\n              </Typography>\n              <Box sx={{ display: 'flex', justifyContent: 'space-between', mb: 0.5 }}>\n                <Typography variant=\"body2\" sx={{ fontWeight: 'medium', fontSize: '0.8rem' }}>Metri teorici:</Typography>\n                <Typography variant=\"body2\" sx={{ fontSize: '0.8rem' }}>{selectedCavo.metri_teorici || 'N/A'} m</Typography>\n              </Box>\n              <Box sx={{ display: 'flex', justifyContent: 'space-between' }}>\n                <Typography variant=\"body2\" sx={{ fontWeight: 'medium', fontSize: '0.8rem' }}>Stato attuale:</Typography>\n                <Chip\n                  label={selectedCavo.stato_installazione || 'N/D'}\n                  size=\"small\"\n                  color={getCableStateColor(selectedCavo.stato_installazione)}\n                  variant=\"outlined\"\n                  sx={{ height: '18px', '& .MuiChip-label': { px: 1, py: 0, fontSize: '0.7rem' } }}\n                />\n              </Box>\n            </Box>\n\n            <Box sx={{ p: 1, bgcolor: '#f5f5f5', borderRadius: 1, flex: '1 1 auto', minWidth: '200px', maxWidth: '400px' }}>\n              <Typography variant=\"caption\" sx={{ fontWeight: 'bold', color: 'secondary.main', display: 'block', mb: 0.5 }}>\n                Informazioni bobina\n              </Typography>\n              {formData.id_bobina && formData.id_bobina !== 'BOBINA_VUOTA' ? (() => {\n                const bobina = bobine.find(b => b.id_bobina === formData.id_bobina);\n                return bobina ? (\n                  <>\n                    <Box sx={{ display: 'flex', justifyContent: 'space-between', mb: 0.5 }}>\n                      <Typography variant=\"body2\" sx={{ fontWeight: 'medium', fontSize: '0.8rem' }}>ID Bobina:</Typography>\n                      <Typography variant=\"body2\" sx={{ fontSize: '0.8rem' }}>{getBobinaNumber(bobina.id_bobina)}</Typography>\n                    </Box>\n                    <Box sx={{ display: 'flex', justifyContent: 'space-between', mb: 0.5 }}>\n                      <Typography variant=\"body2\" sx={{ fontWeight: 'medium', fontSize: '0.8rem' }}>Metri residui:</Typography>\n                      <Typography variant=\"body2\" sx={{ fontSize: '0.8rem' }}>{bobina.metri_residui || 0} m</Typography>\n                    </Box>\n                    <Box sx={{ display: 'flex', justifyContent: 'space-between' }}>\n                      <Typography variant=\"body2\" sx={{ fontWeight: 'medium', fontSize: '0.8rem' }}>Stato:</Typography>\n                      <Chip\n                        label={bobina.stato_bobina || 'N/D'}\n                        size=\"small\"\n                        color={getReelStateColor(bobina.stato_bobina)}\n                        variant=\"outlined\"\n                        sx={{ height: '18px', '& .MuiChip-label': { px: 1, py: 0, fontSize: '0.7rem' } }}\n                      />\n                    </Box>\n                  </>\n                ) : (\n                  <Typography variant=\"body2\" sx={{ fontSize: '0.8rem' }}>Bobina non trovata</Typography>\n                );\n              })() : (\n                <Typography variant=\"body2\" sx={{ fontSize: '0.8rem' }}>\n                  {formData.id_bobina === 'BOBINA_VUOTA' ?\n                    \"Utilizzo BOBINA VUOTA (nessuna bobina associata)\" :\n                    \"Nessuna bobina selezionata\"}\n                </Typography>\n              )}\n            </Box>\n          </Box>\n\n          <Box sx={{ mt: 2, mb: 2, width: '100%' }}>\n            <Typography variant=\"subtitle2\" gutterBottom sx={{ fontWeight: 'bold' }}>\n              Metratura posata\n            </Typography>\n            <TextField\n              size=\"small\"\n              label=\"Metri posati\"\n              variant=\"outlined\"\n              name=\"metri_posati\"\n              type=\"number\"\n              value={formData.metri_posati}\n              onChange={handleFormChange}\n              error={!!formErrors.metri_posati}\n              helperText={formErrors.metri_posati || formWarnings.metri_posati}\n              FormHelperTextProps={{\n                sx: { color: formWarnings.metri_posati && !formErrors.metri_posati ? 'warning.main' : 'error.main' }\n              }}\n              sx={{ mb: 1, width: '200px' }}\n              inputProps={{\n                max: 999999, // Limite a 6 cifre\n                step: 0.1\n              }}\n            />\n          </Box>\n\n          {formWarnings.metri_posati && !formErrors.metri_posati && !notificationShown && (\n            <Alert severity=\"warning\" sx={{ mb: 2 }}>\n              {formWarnings.metri_posati}\n            </Alert>\n          )}\n\n          <Alert severity=\"info\" sx={{ mt: 2 }}>\n            <Typography variant=\"body2\">\n              Inserisci i metri di cavo effettivamente posati. Questo valore aggiorna lo stato di installazione del cavo.\n            </Typography>\n          </Alert>\n        </Paper>\n      </Box>\n    );\n  };\n\n  // Renderizza il passo 3: Associazione bobina\n  const renderStep3 = () => {\n    // Ottieni tutte le tipologie uniche dalle bobine\n    const tipologieUniche = [...new Set(bobine.map(bobina => bobina.tipologia || 'N/D'))];\n\n    // Funzione per costruire l'ID completo della bobina\n    const buildFullBobinaId = (numeroBobina) => {\n      return `C${cantiereId}_B${numeroBobina}`;\n    };\n\n    // Verifica se una bobina ha metri residui sufficienti\n    const hasSufficientMeters = (bobina) => {\n      if (!bobina || !formData.metri_posati) return true;\n      return parseFloat(bobina.metri_residui) >= parseFloat(formData.metri_posati);\n    };\n\n    // Filtra le bobine in base alla tipologia selezionata e al testo di ricerca\n    const bobineFiltrate = bobine.filter(bobina => {\n      // Filtro per tipologia\n      const matchesTipologia = !tipologiaFilter ||\n        String(bobina.tipologia || '').trim() === tipologiaFilter;\n\n      // Filtro per testo di ricerca\n      const searchLower = searchText.toLowerCase();\n      const matchesSearch = !searchText ||\n        getBobinaNumber(bobina.id_bobina).toLowerCase().includes(searchLower) ||\n        String(bobina.tipologia || '').toLowerCase().includes(searchLower) ||\n        String(bobina.sezione || '').toLowerCase().includes(searchLower);\n\n      return matchesTipologia && matchesSearch;\n    });\n\n    // Separa le bobine compatibili e non compatibili\n    const bobineCompatibili = selectedCavo\n      ? bobineFiltrate.filter(bobina =>\n          String(bobina.tipologia || '').trim() === String(selectedCavo.tipologia || '').trim() &&\n          String(bobina.sezione || '0').trim() === String(selectedCavo.sezione || '0').trim())\n      : bobineFiltrate;\n\n    const bobineNonCompatibili = selectedCavo\n      ? bobineFiltrate.filter(bobina =>\n          String(bobina.tipologia || '').trim() !== String(selectedCavo.tipologia || '').trim() ||\n          String(bobina.sezione || '0').trim() !== String(selectedCavo.sezione || '0').trim())\n      : [];\n\n    // Gestisce l'input diretto del numero della bobina\n    const handleBobinaNumberInput = (e) => {\n      const numeroBobina = e.target.value.trim();\n\n      // Gestione esplicita dell'input 'v' per bobina vuota\n      if (numeroBobina.toLowerCase() === 'v') {\n        setFormData({\n          ...formData,\n          id_bobina: ''\n        });\n        setFormErrors({\n          ...formErrors,\n          id_bobina_input: null\n        });\n        return;\n      }\n\n      if (numeroBobina) {\n        // Costruisci l'ID completo\n        const idBobinaCompleto = buildFullBobinaId(numeroBobina);\n\n        // Verifica se la bobina esiste\n        const bobinaEsistente = bobine.find(b => b.id_bobina === idBobinaCompleto);\n\n        if (bobinaEsistente) {\n          // Verifica se la bobina è in stato OVER o TERMINATA\n          if (bobinaEsistente.stato_bobina === 'Over' || bobinaEsistente.stato_bobina === 'Terminata') {\n            setFormErrors({\n              ...formErrors,\n              id_bobina_input: `La bobina ${numeroBobina} è in stato ${bobinaEsistente.stato_bobina} e non può essere utilizzata`\n            });\n            return;\n          }\n\n          // Verifica compatibilità tra cavo e bobina\n          if (selectedCavo) {\n            // Converti i valori in stringhe, gestendo null e undefined\n            const cavoTipologia = String(selectedCavo.tipologia || '');\n            const cavoSezione = String(selectedCavo.sezione || '0');\n\n            const bobinaTipologia = String(bobinaEsistente.tipologia || '');\n            const bobinaSezione = String(bobinaEsistente.sezione || '0');\n\n            // Log per debug\n            console.log(`Verifica compatibilità bobina ${bobinaEsistente.id_bobina}:`, {\n              tipologia: `${bobinaTipologia} === ${cavoTipologia}`,\n              formazione: `${bobinaSezione} === ${cavoSezione}`\n            });\n\n            if (bobinaTipologia !== cavoTipologia ||\n                bobinaSezione !== cavoSezione) {\n              // Mostra il dialogo per bobine incompatibili\n              setIncompatibleReelData({\n                cavo: selectedCavo,\n                bobina: bobinaEsistente\n              });\n              setShowIncompatibleReelDialog(true);\n              return;\n            }\n          }\n\n          // Verifica se la bobina ha metri residui sufficienti\n          if (hasSufficientMeters(bobinaEsistente)) {\n            // Se la bobina esiste e ha metri sufficienti, imposta l'ID completo\n            setFormData({\n              ...formData,\n              id_bobina: idBobinaCompleto\n            });\n            setFormErrors({\n              ...formErrors,\n              id_bobina_input: null\n            });\n          } else {\n            // Se la bobina non ha metri sufficienti, mostra un avviso\n            setFormErrors({\n              ...formErrors,\n              id_bobina_input: `La bobina ${numeroBobina} non ha metri residui sufficienti (${bobinaEsistente.metri_residui} m disponibili)`\n            });\n          }\n        } else {\n          // Se la bobina non esiste, mostra un errore\n          setFormErrors({\n            ...formErrors,\n            id_bobina_input: `Bobina ${numeroBobina} non trovata`\n          });\n        }\n      } else {\n        // Se l'input è vuoto, resetta l'ID bobina\n        setFormData({\n          ...formData,\n          id_bobina: ''\n        });\n        setFormErrors({\n          ...formErrors,\n          id_bobina_input: null\n        });\n      }\n    };\n\n    return (\n      <Box>\n        <Typography variant=\"subtitle2\" sx={{ mb: 1, fontWeight: 'bold' }}>\n          Associa bobina\n        </Typography>\n\n        <Paper sx={{ p: 2, width: '100%' }}>\n          <Box sx={{ mb: 2 }}>\n            <Typography variant=\"body2\" sx={{ mb: 1 }}>\n              Seleziona una bobina da associare al cavo o usa \"BOBINA VUOTA\" se non desideri associare una bobina specifica.\n            </Typography>\n\n            <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', gap: 2 }}>\n              {/* Campo di ricerca */}\n              <TextField\n                size=\"small\"\n                label=\"Cerca bobina\"\n                variant=\"outlined\"\n                value={searchText}\n                onChange={handleSearchTextChange}\n                placeholder=\"Cerca per ID, tipologia...\"\n                InputProps={{\n                  startAdornment: (\n                    <InputAdornment position=\"start\">\n                      <SearchIcon fontSize=\"small\" />\n                    </InputAdornment>\n                  ),\n                  endAdornment: searchText ? (\n                    <InputAdornment position=\"end\">\n                      <IconButton\n                        size=\"small\"\n                        aria-label=\"clear search\"\n                        onClick={() => setSearchText('')}\n                        edge=\"end\"\n                      >\n                        <CancelIcon fontSize=\"small\" />\n                      </IconButton>\n                    </InputAdornment>\n                  ) : null\n                }}\n                sx={{ flex: 1 }}\n              />\n\n              {/* Filtro per tipologia */}\n              <FormControl size=\"small\" sx={{ minWidth: 200 }}>\n                <InputLabel id=\"tipologia-filter-label\">Filtra per tipologia</InputLabel>\n                <Select\n                  labelId=\"tipologia-filter-label\"\n                  value={tipologiaFilter}\n                  label=\"Filtra per tipologia\"\n                  onChange={handleTipologiaFilterChange}\n                >\n                  <MenuItem value=\"\">Tutte le tipologie</MenuItem>\n                  {tipologieUniche.map((tipologia) => (\n                    <MenuItem key={tipologia} value={tipologia}>{tipologia}</MenuItem>\n                  ))}\n                </Select>\n              </FormControl>\n            </Box>\n          </Box>\n\n          {bobineLoading ? (\n            <Box sx={{ display: 'flex', justifyContent: 'center', my: 1 }}>\n              <CircularProgress size={24} />\n            </Box>\n          ) : (\n            <Box>\n              {/* Input diretto - versione compatta */}\n              <Box sx={{ mb: 2 }}>\n                <Typography variant=\"subtitle2\" sx={{ fontWeight: 'bold', display: 'block', mb: 1 }}>\n                  Inserimento diretto\n                </Typography>\n                <Box sx={{ display: 'flex', alignItems: 'flex-start', gap: 1, maxWidth: '300px' }}>\n                  <TextField\n                    size=\"small\"\n                    label=\"Numero bobina\"\n                    variant=\"outlined\"\n                    placeholder=\"Solo Y\"\n                    error={!!formErrors.id_bobina_input}\n                    onBlur={handleBobinaNumberInput}\n                    sx={{ flex: 1 }}\n                  />\n                  <Button\n                    variant=\"outlined\"\n                    size=\"small\"\n                    onClick={() => handleSelectBobina('BOBINA_VUOTA')}\n                    sx={{ height: '40px' }}\n                  >\n                    BOBINA VUOTA\n                  </Button>\n                </Box>\n                {formErrors.id_bobina_input && (\n                  <Typography variant=\"caption\" color=\"error\" sx={{ display: 'block', mt: 0.5 }}>\n                    {formErrors.id_bobina_input}\n                  </Typography>\n                )}\n                <Typography variant=\"caption\" sx={{ display: 'block', mt: 0.5 }}>\n                  ID Bobina: <strong>{formData.id_bobina || '-'}</strong>\n                </Typography>\n              </Box>\n\n              {/* Griglia per le due liste di bobine */}\n              <Grid container spacing={2}>\n                {/* Colonna sinistra: Bobine compatibili */}\n                <Grid item xs={12} md={6}>\n                  <Paper variant=\"outlined\" sx={{ p: 2, height: '100%' }}>\n                    <Typography variant=\"subtitle2\" sx={{ fontWeight: 'bold', mb: 1 }}>\n                      ELENCO BOBINE COMPATIBILI\n                    </Typography>\n\n                    {bobineCompatibili.length > 0 ? (\n                      <List sx={{ maxHeight: '300px', overflow: 'auto', bgcolor: 'background.paper' }}>\n                        {bobineCompatibili.map((bobina) => (\n                          <ListItem\n                            key={bobina.id_bobina}\n                            disablePadding\n                            secondaryAction={\n                              <IconButton\n                                edge=\"end\"\n                                size=\"small\"\n                                onClick={() => handleSelectBobina(bobina.id_bobina)}\n                                disabled={bobina.metri_residui < parseFloat(formData.metri_posati || 0)}\n                              >\n                                <AddCircleOutlineIcon color={bobina.metri_residui < parseFloat(formData.metri_posati || 0) ? 'disabled' : 'primary'} />\n                              </IconButton>\n                            }\n                            sx={{\n                              bgcolor: formData.id_bobina === bobina.id_bobina ? 'rgba(76, 175, 80, 0.12)' : 'inherit',\n                              borderRadius: '4px',\n                              mb: 0.5,\n                              border: formData.id_bobina === bobina.id_bobina ? '1px solid #4caf50' : 'none',\n                            }}\n                          >\n                            <ListItemButton\n                              dense\n                              onClick={() => handleSelectBobina(bobina.id_bobina)}\n                              disabled={bobina.metri_residui < parseFloat(formData.metri_posati || 0)}\n                            >\n                              <ListItemText\n                                primary={\n                                  <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>\n                                    <Typography variant=\"body2\" sx={{ fontWeight: 'bold' }}>\n                                      {getBobinaNumber(bobina.id_bobina)}\n                                    </Typography>\n                                    <Typography variant=\"caption\" sx={{ fontWeight: 'bold', color: bobina.metri_residui < parseFloat(formData.metri_posati || 0) ? 'error.main' : 'success.main' }}>\n                                      {bobina.metri_residui || 0} m\n                                    </Typography>\n                                  </Box>\n                                }\n                                secondary={\n                                  <>\n                                    <Typography variant=\"caption\" component=\"span\" display=\"block\">\n                                      {bobina.tipologia || 'N/A'} - {bobina.sezione || 'N/A'}\n                                    </Typography>\n                                    <Chip\n                                      size=\"small\"\n                                      label={bobina.stato_bobina || 'N/D'}\n                                      color={getReelStateColor(bobina.stato_bobina)}\n                                      variant=\"outlined\"\n                                      sx={{ height: 16, fontSize: '0.6rem', mt: 0.5, '& .MuiChip-label': { px: 0.5, py: 0 } }}\n                                    />\n                                  </>\n                                }\n                              />\n                            </ListItemButton>\n                          </ListItem>\n                        ))}\n                      </List>\n                    ) : (\n                      <Alert severity=\"info\" sx={{ mt: 1 }}>\n                        Nessuna bobina compatibile disponibile. Puoi usare BOBINA VUOTA o selezionare una bobina non compatibile.\n                      </Alert>\n                    )}\n                  </Paper>\n                </Grid>\n\n                {/* Colonna destra: Bobine non compatibili */}\n                <Grid item xs={12} md={6}>\n                  <Paper variant=\"outlined\" sx={{ p: 2, height: '100%' }}>\n                    <Typography variant=\"subtitle2\" sx={{ fontWeight: 'bold', mb: 1 }}>\n                      ELENCO BOBINE NON COMPATIBILI\n                    </Typography>\n\n                    {bobineNonCompatibili.length > 0 ? (\n                      <List sx={{ maxHeight: '300px', overflow: 'auto', bgcolor: 'background.paper' }}>\n                        {bobineNonCompatibili.map((bobina) => (\n                          <ListItem\n                            key={bobina.id_bobina}\n                            disablePadding\n                            secondaryAction={\n                              <IconButton\n                                edge=\"end\"\n                                size=\"small\"\n                                onClick={() => handleSelectBobina(bobina.id_bobina)}\n                                disabled={bobina.metri_residui < parseFloat(formData.metri_posati || 0)}\n                              >\n                                <AddCircleOutlineIcon color={bobina.metri_residui < parseFloat(formData.metri_posati || 0) ? 'disabled' : 'primary'} />\n                              </IconButton>\n                            }\n                            sx={{\n                              bgcolor: formData.id_bobina === bobina.id_bobina ? 'rgba(76, 175, 80, 0.12)' : 'inherit',\n                              borderRadius: '4px',\n                              mb: 0.5,\n                              border: formData.id_bobina === bobina.id_bobina ? '1px solid #4caf50' : 'none',\n                            }}\n                          >\n                            <ListItemButton\n                              dense\n                              onClick={() => handleSelectBobina(bobina.id_bobina)}\n                              disabled={bobina.metri_residui < parseFloat(formData.metri_posati || 0)}\n                            >\n                              <ListItemText\n                                primary={\n                                  <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>\n                                    <Typography variant=\"body2\" sx={{ fontWeight: 'bold' }}>\n                                      {getBobinaNumber(bobina.id_bobina)}\n                                    </Typography>\n                                    <Typography variant=\"caption\" sx={{ fontWeight: 'bold', color: bobina.metri_residui < parseFloat(formData.metri_posati || 0) ? 'error.main' : 'success.main' }}>\n                                      {bobina.metri_residui || 0} m\n                                    </Typography>\n                                  </Box>\n                                }\n                                secondary={\n                                  <>\n                                    <Typography variant=\"caption\" component=\"span\" display=\"block\">\n                                      {bobina.tipologia || 'N/A'} - {bobina.sezione || 'N/A'}\n                                    </Typography>\n                                    <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mt: 0.5 }}>\n                                      <Chip\n                                        size=\"small\"\n                                        label={bobina.stato_bobina || 'N/D'}\n                                        color={getReelStateColor(bobina.stato_bobina)}\n                                        variant=\"outlined\"\n                                        sx={{ height: 16, fontSize: '0.6rem', '& .MuiChip-label': { px: 0.5, py: 0 } }}\n                                      />\n                                      <Chip\n                                        size=\"small\"\n                                        label=\"Non compatibile\"\n                                        color=\"warning\"\n                                        variant=\"outlined\"\n                                        sx={{ height: 16, fontSize: '0.6rem', '& .MuiChip-label': { px: 0.5, py: 0 } }}\n                                      />\n                                    </Box>\n                                  </>\n                                }\n                              />\n                            </ListItemButton>\n                          </ListItem>\n                        ))}\n                      </List>\n                    ) : (\n                      <Alert severity=\"info\" sx={{ mt: 1 }}>\n                        Nessuna bobina non compatibile disponibile con i filtri attuali.\n                      </Alert>\n                    )}\n                  </Paper>\n                </Grid>\n              </Grid>\n            </Box>\n          )}\n\n          {/* Mostra dettagli della bobina selezionata */}\n          {!bobineLoading && formData.id_bobina && (\n            <Box sx={{ mt: 3, p: 2, bgcolor: 'background.paper', borderRadius: 1, border: '1px solid #e0e0e0' }}>\n              <Typography variant=\"subtitle2\" gutterBottom>\n                Dettagli bobina selezionata\n              </Typography>\n              {(() => {\n                const bobina = bobine.find(b => b.id_bobina === formData.id_bobina);\n                if (bobina) {\n                  return (\n                    <Grid container spacing={2}>\n                      <Grid item xs={12} md={6}>\n                        <Typography variant=\"body2\">\n                          <strong>Numero:</strong> {getBobinaNumber(bobina.id_bobina)}\n                        </Typography>\n                        <Typography variant=\"body2\">\n                          <strong>Tipologia:</strong> {bobina.tipologia || 'N/A'}\n                        </Typography>\n                        <Typography variant=\"body2\">\n                          <strong>Conduttori:</strong> {bobina.n_conduttori || 'N/A'} x {bobina.sezione || 'N/A'}\n                        </Typography>\n                      </Grid>\n                      <Grid item xs={12} md={6}>\n                        <Typography variant=\"body2\">\n                          <strong>Metri totali:</strong> {bobina.metri_totali || 0} m\n                        </Typography>\n                        <Typography variant=\"body2\">\n                          <strong>Metri residui:</strong> {bobina.metri_residui || 0} m\n                        </Typography>\n                        <Typography variant=\"body2\">\n                          <strong>Stato:</strong> {bobina.stato_bobina || 'N/A'}\n                        </Typography>\n                      </Grid>\n                    </Grid>\n                  );\n                }\n                return (\n                  <Typography variant=\"body2\" color=\"error\">\n                    Bobina non trovata nel database\n                  </Typography>\n                );\n              })()}\n            </Box>\n          )}\n\n          {bobine.length === 0 && !bobineLoading && (\n            <Alert severity=\"warning\" sx={{ mt: 2 }}>\n              Non ci sono bobine disponibili. Puoi procedere senza associare una bobina o aggiungere prima una nuova bobina.\n            </Alert>\n          )}\n        </Paper>\n      </Box>\n    );\n  };\n\n  // Renderizza il passo 4: Conferma\n  const renderStep4 = () => {\n\n    // Ottieni il numero della bobina se presente\n    let numeroBobina = 'Nessuna';\n    let bobinaInfo = null;\n\n    if (formData.id_bobina === 'BOBINA_VUOTA') {\n      numeroBobina = 'BOBINA VUOTA';\n    } else if (formData.id_bobina) {\n      numeroBobina = getBobinaNumber(formData.id_bobina);\n      // Trova i dettagli della bobina selezionata\n      bobinaInfo = bobine.find(b => b.id_bobina === formData.id_bobina);\n    }\n\n    // Determina lo stato di installazione\n    const statoInstallazione = determineInstallationStatus(parseFloat(formData.metri_posati), selectedCavo.metri_teorici);\n\n    return (\n      <Box>\n        <Typography variant=\"h6\" gutterBottom>\n          Conferma inserimento\n        </Typography>\n\n        <Paper sx={{ p: 3 }}>\n          <Typography variant=\"subtitle1\" gutterBottom>\n            Riepilogo dati\n          </Typography>\n\n          {/* Dettagli del cavo */}\n          <CavoDetailsView\n            cavo={selectedCavo}\n            compact={true}\n            title=\"Dettagli del cavo\"\n          />\n\n          {/* Informazioni sull'operazione */}\n          <Box sx={{ mt: 3, p: 2, bgcolor: '#f5f5f5', borderRadius: 1 }}>\n            <Typography variant=\"subtitle2\" gutterBottom sx={{ fontWeight: 'bold' }}>\n              Informazioni sull'operazione:\n            </Typography>\n            <Grid container spacing={2}>\n              <Grid item xs={12} md={6}>\n                <Typography variant=\"body2\">\n                  <strong>Metri Posati:</strong> {formData.metri_posati} m\n                </Typography>\n                <Typography variant=\"body2\">\n                  <strong>Stato Installazione:</strong> {statoInstallazione}\n                </Typography>\n              </Grid>\n              <Grid item xs={12} md={6}>\n                <Typography variant=\"body2\">\n                  <strong>Bobina Associata:</strong> {numeroBobina}\n                </Typography>\n                {bobinaInfo && (\n                  <Typography variant=\"body2\">\n                    <strong>Metri Residui Bobina:</strong> {bobinaInfo.metri_residui} m\n                  </Typography>\n                )}\n              </Grid>\n            </Grid>\n          </Box>\n\n          {bobinaInfo && parseFloat(formData.metri_posati) > parseFloat(bobinaInfo.metri_residui) && !notificationShown && (\n            <Alert severity=\"warning\" sx={{ mt: 3 }}>\n              <strong>Attenzione:</strong> I metri posati ({formData.metri_posati}m) superano i metri residui della bobina ({bobinaInfo.metri_residui}m).\n              Questo porterà la bobina in stato OVER.\n            </Alert>\n          )}\n\n          <Alert severity=\"info\" sx={{ mt: 3 }}>\n            Conferma per aggiornare i metri posati e lo stato di installazione del cavo.\n            {formData.id_bobina && formData.id_bobina !== 'BOBINA_VUOTA' && ' I metri posati verranno sottratti dai metri residui della bobina selezionata.'}\n          </Alert>\n        </Paper>\n      </Box>\n    );\n  };\n\n  // Renderizza il contenuto in base al passo attivo\n  const getStepContent = (step) => {\n    switch (step) {\n      case 0:\n        return renderStep1(); // Seleziona Cavo\n      case 1:\n        return renderStep3(); // Associa Bobina\n      case 2:\n        return renderStep2(); // Inserisci Metri\n      case 3:\n        return renderStep4(); // Conferma\n      default:\n        return 'Passo sconosciuto';\n    }\n  };\n\n  // Gestisce la chiusura del dialogo per cavi già posati\n  const handleCloseAlreadyLaidDialog = () => {\n    setShowAlreadyLaidDialog(false);\n    setAlreadyLaidCavo(null);\n  };\n\n\n\n  // Gestisce l'opzione di modificare la bobina di un cavo già posato\n  const handleModifyReel = () => {\n    if (alreadyLaidCavo) {\n      navigate(`/dashboard/cavi/modifica-bobina/${cantiereId}/${alreadyLaidCavo.id_cavo}`);\n    }\n    handleCloseAlreadyLaidDialog();\n  };\n\n  // Gestisce l'opzione di selezionare un altro cavo\n  const handleSelectAnotherCable = () => {\n    handleCloseAlreadyLaidDialog();\n    // Reset del form per selezionare un nuovo cavo\n    setSelectedCavo(null);\n    setCavoIdInput('');\n    setShowSearchResults(false);\n  };\n\n  // Gestisce la chiusura del dialogo per bobine incompatibili\n  const handleCloseIncompatibleReelDialog = () => {\n    setShowIncompatibleReelDialog(false);\n    setIncompatibleReelData({ cavo: null, bobina: null });\n  };\n\n  // Gestisce l'aggiornamento delle caratteristiche del cavo per farle corrispondere a quelle della bobina\n  const handleUpdateCavoToMatchReel = async () => {\n    const { cavo, bobina } = incompatibleReelData;\n    if (!cavo || !bobina) {\n      console.error('Dati mancanti per aggiornare il cavo:', { cavo, bobina });\n      onError('Dati mancanti per aggiornare il cavo');\n      return;\n    }\n\n    try {\n      setLoading(true);\n      console.log(`Aggiornamento caratteristiche del cavo ${cavo.id_cavo} per compatibilità con bobina ${bobina.id_bobina}`);\n      console.log('Dati cavo prima dell\\'aggiornamento:', cavo);\n      console.log('Dati bobina:', bobina);\n\n      // Aggiorna le caratteristiche del cavo\n      await caviService.updateCavoForCompatibility(cantiereId, cavo.id_cavo, bobina.id_bobina);\n\n      // Aggiorna il cavo selezionato con le nuove caratteristiche\n      const updatedCavo = await caviService.getCavoById(cantiereId, cavo.id_cavo);\n      console.log('Dati cavo dopo l\\'aggiornamento:', updatedCavo);\n      setSelectedCavo(updatedCavo);\n\n      // Imposta la bobina selezionata\n      setFormData({\n        ...formData,\n        id_bobina: bobina.id_bobina\n      });\n\n      // Ricarica le bobine per aggiornare l'interfaccia\n      await loadBobine();\n\n      onSuccess(`Caratteristiche del cavo ${cavo.id_cavo} aggiornate per corrispondere alla bobina ${bobina.id_bobina}`);\n      handleCloseIncompatibleReelDialog();\n    } catch (error) {\n      console.error('Errore durante l\\'aggiornamento delle caratteristiche del cavo:', error);\n      onError('Errore durante l\\'aggiornamento delle caratteristiche del cavo: ' + (error.detail || error.message || 'Errore sconosciuto'));\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  // Gestisce la selezione di un'altra bobina\n  const handleSelectAnotherReel = () => {\n    handleCloseIncompatibleReelDialog();\n    // Reset della bobina selezionata\n    setFormData({\n      ...formData,\n      id_bobina: ''\n    });\n  };\n\n  return (\n    <Box>\n      {/* Sezione di ricerca - versione compatta */}\n      <Box sx={{ mb: 2 }}>\n        <Box sx={{ display: 'flex', alignItems: 'center', mb: 1 }}>\n          <Typography variant=\"subtitle2\" sx={{ fontWeight: 'bold', minWidth: '80px' }}>\n            Cerca cavo\n          </Typography>\n          <TextField\n            size=\"small\"\n            value={cavoIdInput}\n            onChange={(e) => setCavoIdInput(e.target.value)}\n            variant=\"outlined\"\n            sx={{ mr: 1, flex: 1 }}\n          />\n          <Button\n            variant=\"contained\"\n            color=\"primary\"\n            onClick={handleSearchCavoById}\n            disabled={caviLoading || !cavoIdInput.trim()}\n            size=\"small\"\n          >\n            CERCA\n          </Button>\n        </Box>\n\n        {/* Dettagli cavo selezionato */}\n        {selectedCavo && (\n          <Box sx={{ display: 'flex', justifyContent: 'space-between', width: '100%', mt: 2 }}>\n            <Box>\n              <Typography variant=\"subtitle2\" sx={{ fontWeight: 'bold' }}>\n                Cavo selezionato: <span style={{ color: '#1976d2' }}>{selectedCavo.id_cavo}</span>\n              </Typography>\n              <Box sx={{ display: 'flex', gap: 1 }}>\n                <Typography variant=\"body2\">Tipologia: {selectedCavo.tipologia || 'N/A'}</Typography>\n                <Typography variant=\"body2\">Formazione: {selectedCavo.sezione || 'N/A'}</Typography>\n                <Typography variant=\"body2\">Metri teorici: {selectedCavo.metri_teorici || 'N/A'} m</Typography>\n              </Box>\n            </Box>\n            <Box>\n              <Box sx={{ display: 'flex', flexDirection: 'column', alignItems: 'flex-end' }}>\n                <Typography variant=\"body2\">Ubicazione partenza: {selectedCavo.ubicazione_partenza || 'N/A'}</Typography>\n                <Typography variant=\"body2\">Ubicazione arrivo: {selectedCavo.ubicazione_arrivo || 'N/A'}</Typography>\n                <Typography variant=\"body2\">\n                  Stato:\n                  <Chip\n                    size=\"small\"\n                    label={selectedCavo.stato_installazione || 'N/D'}\n                    color={getCableStateColor(selectedCavo.stato_installazione)}\n                    sx={{ ml: 1, height: '20px', '& .MuiChip-label': { px: 1, py: 0 } }}\n                  />\n                </Typography>\n              </Box>\n            </Box>\n          </Box>\n        )}\n\n        <Divider sx={{ my: 2 }} />\n      </Box>\n\n      {/* Risultati della ricerca */}\n      {showSearchResults && searchResults.length > 0 && (\n        <Paper sx={{ p: 3, mb: 3 }}>\n          <Typography variant=\"h6\" gutterBottom>\n            Risultati della ricerca\n          </Typography>\n          <TableContainer>\n            <Table size=\"small\">\n              <TableHead>\n                <TableRow sx={{ bgcolor: '#f5f5f5' }}>\n                  <TableCell>ID Cavo</TableCell>\n                  <TableCell>Tipologia</TableCell>\n                  <TableCell>Formazione</TableCell>\n                  <TableCell>Ubicazione</TableCell>\n                  <TableCell>Metri Teorici</TableCell>\n                  <TableCell>Stato</TableCell>\n                  <TableCell>Azioni</TableCell>\n                </TableRow>\n              </TableHead>\n              <TableBody>\n                {searchResults.map((cavo) => (\n                  <TableRow key={cavo.id_cavo}>\n                    <TableCell>{cavo.id_cavo}</TableCell>\n                    <TableCell>{cavo.tipologia || 'N/A'}</TableCell>\n                    <TableCell>{cavo.sezione || 'N/A'}</TableCell>\n                    <TableCell>Da: {cavo.ubicazione_partenza || 'N/A'}<br/>A: {cavo.ubicazione_arrivo || 'N/A'}</TableCell>\n                    <TableCell>{cavo.metri_teorici || 'N/A'} m</TableCell>\n                    <TableCell>\n                      <Chip\n                        label={cavo.stato_installazione || 'N/D'}\n                        size=\"small\"\n                        color={getCableStateColor(cavo.stato_installazione)}\n                        variant=\"outlined\"\n                      />\n                    </TableCell>\n                    <TableCell>\n                      <Button\n                        size=\"small\"\n                        variant=\"contained\"\n                        color=\"primary\"\n                        onClick={() => handleCavoSelect(cavo)}\n                        disabled={isCableInstalled(cavo)}\n                      >\n                        Seleziona\n                      </Button>\n                    </TableCell>\n                  </TableRow>\n                ))}\n              </TableBody>\n            </Table>\n          </TableContainer>\n        </Paper>\n      )}\n      {/* Sezione di selezione bobina */}\n      {selectedCavo && (\n        <Box>\n          {renderStep3()}\n        </Box>\n      )}\n\n      {/* Dialogo di conferma generico */}\n      <Dialog open={showConfirmDialog} onClose={() => setShowConfirmDialog(false)} maxWidth=\"sm\" fullWidth>\n        <DialogTitle sx={{ bgcolor: 'warning.light' }}>\n          <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>\n            <WarningIcon color=\"warning\" />\n            <Typography variant=\"h6\">{confirmDialogProps.title}</Typography>\n          </Box>\n        </DialogTitle>\n        <DialogContent>\n          <Typography variant=\"body1\" sx={{ mt: 2 }}>\n            {confirmDialogProps.message}\n          </Typography>\n        </DialogContent>\n        <DialogActions>\n          <Button onClick={() => setShowConfirmDialog(false)} color=\"secondary\" variant=\"outlined\">\n            Annulla\n          </Button>\n          <Button\n            onClick={() => {\n              setShowConfirmDialog(false);\n              confirmDialogProps.onConfirm();\n            }}\n            color=\"primary\"\n            variant=\"contained\"\n            autoFocus\n          >\n            Conferma\n          </Button>\n        </DialogActions>\n      </Dialog>\n\n      {/* Dialogo per cavi già posati */}\n      <Dialog open={showAlreadyLaidDialog} onClose={handleCloseAlreadyLaidDialog} maxWidth=\"sm\" fullWidth>\n        <DialogTitle sx={{ bgcolor: 'warning.light' }}>\n          <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>\n            <WarningIcon color=\"warning\" />\n            <Typography variant=\"h6\">Cavo già posato</Typography>\n          </Box>\n        </DialogTitle>\n        <DialogContent>\n          {alreadyLaidCavo && (\n            <Box sx={{ mt: 2 }}>\n              <Typography variant=\"body1\" paragraph>\n                Il cavo <strong>{alreadyLaidCavo.id_cavo}</strong> risulta già posato ({alreadyLaidCavo.metratura_reale || 0}m).\n              </Typography>\n              <Typography variant=\"body1\" paragraph>\n                Puoi scegliere di:\n              </Typography>\n              <Typography variant=\"body2\" component=\"ul\">\n                <li>Modificare la bobina associata al cavo</li>\n                <li>Selezionare un altro cavo</li>\n                <li>Annullare l'operazione</li>\n              </Typography>\n            </Box>\n          )}\n        </DialogContent>\n        <DialogActions sx={{ p: 2, justifyContent: 'space-between' }}>\n          <Button onClick={handleCloseAlreadyLaidDialog} color=\"secondary\">\n            Annulla operazione\n          </Button>\n          <Box>\n            <Button onClick={handleSelectAnotherCable} color=\"primary\" sx={{ mr: 1 }}>\n              Seleziona altro cavo\n            </Button>\n            <Button onClick={handleModifyReel} variant=\"contained\" color=\"primary\">\n              Modifica bobina\n            </Button>\n          </Box>\n        </DialogActions>\n      </Dialog>\n\n      {/* Dialogo per bobine incompatibili */}\n      <IncompatibleReelDialog\n        open={showIncompatibleReelDialog}\n        onClose={handleCloseIncompatibleReelDialog}\n        cavo={incompatibleReelData.cavo}\n        bobina={incompatibleReelData.bobina}\n        onUpdateCavo={handleUpdateCavoToMatchReel}\n        onSelectAnotherReel={handleSelectAnotherReel}\n      />\n\n      {/* Dialogo per visualizzare i dettagli del cavo */}\n      <Dialog\n        open={showCavoDetailsDialog}\n        onClose={() => setShowCavoDetailsDialog(false)}\n        maxWidth=\"md\"\n        fullWidth\n      >\n        <DialogTitle>\n          <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>\n            <InfoIcon color=\"primary\" />\n            <Typography variant=\"h6\">Dettagli Cavo</Typography>\n          </Box>\n        </DialogTitle>\n        <DialogContent>\n          <CavoDetailsView cavo={selectedCavo} />\n        </DialogContent>\n        <DialogActions>\n          <Button onClick={() => setShowCavoDetailsDialog(false)} color=\"primary\">\n            Chiudi\n          </Button>\n        </DialogActions>\n      </Dialog>\n    </Box>\n  );\n};\n\nexport default InserisciMetriForm;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAClD,OAAOC,KAAK,MAAM,OAAO;AACzB,SACEC,GAAG,EACHC,KAAK,EACLC,UAAU,EACVC,SAAS,EACTC,MAAM,EACNC,IAAI,EACJC,WAAW,EACXC,UAAU,EACVC,MAAM,EACNC,QAAQ,EACRC,OAAO,EACPC,KAAK,EACLC,gBAAgB,EAChBC,cAAc,EACdC,UAAU,EACVC,IAAI,EACJC,cAAc,EACdC,MAAM,EACNC,WAAW,EACXC,aAAa,EACbC,aAAa,EACbC,KAAK,EACLC,SAAS,EACTC,SAAS,EACTC,cAAc,EACdC,SAAS,EACTC,QAAQ,EACRC,IAAI,EACJC,QAAQ,EACRC,cAAc,EACdC,YAAY,EACZC,uBAAuB,QAClB,eAAe;AACtB,SACEC,MAAM,IAAIC,UAAU,EACpBC,IAAI,IAAIC,QAAQ,EAChBC,MAAM,IAAIC,UAAU,EACpBC,OAAO,IAAIC,WAAW,EACtBC,IAAI,IAAIC,QAAQ,EAChBC,gBAAgB,IAAIC,oBAAoB,QACnC,qBAAqB;AAC5B,SAASC,WAAW,QAAQ,kBAAkB;AAC9C,OAAOC,WAAW,MAAM,4BAA4B;AACpD,OAAOC,aAAa,MAAM,4BAA4B;AACtD,OAAOC,sBAAsB,MAAM,0BAA0B;AAC7D,OAAOC,eAAe,MAAM,mBAAmB;AAC/C,SACEC,YAAY,EACZC,WAAW,EACXC,mBAAmB,EACnBC,kBAAkB,EAClBC,cAAc,EACdC,YAAY,EACZC,gBAAgB,EAChBC,kBAAkB,EAClBC,iBAAiB,QACZ,wBAAwB;AAC/B,OAAOC,gBAAgB,MAAM,iCAAiC;AAC9D,SAASC,wBAAwB,QAAQ,6BAA6B;;AAEtE;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AARA,SAAAC,MAAA,IAAAC,OAAA,EAAAC,QAAA,IAAAC,SAAA;AASA,MAAMC,kBAAkB,GAAGA,CAAC;EAAEC,UAAU;EAAEC,SAAS;EAAEC;AAAQ,CAAC,KAAK;EAAAC,EAAA;EACjE,MAAMC,QAAQ,GAAGzB,WAAW,CAAC,CAAC;;EAE9B;EACA,MAAM,CAAC0B,OAAO,EAAEC,UAAU,CAAC,GAAG1E,QAAQ,CAAC,KAAK,CAAC;EAC7C,MAAM,CAAC2E,WAAW,EAAEC,cAAc,CAAC,GAAG5E,QAAQ,CAAC,KAAK,CAAC;EACrD,MAAM,CAAC6E,aAAa,EAAEC,gBAAgB,CAAC,GAAG9E,QAAQ,CAAC,KAAK,CAAC;EACzD,MAAM,CAAC+E,aAAa,EAAEC,gBAAgB,CAAC,GAAGhF,QAAQ,CAAC,EAAE,CAAC;EACtD,MAAM,CAACiF,iBAAiB,EAAEC,oBAAoB,CAAC,GAAGlF,QAAQ,CAAC,KAAK,CAAC;;EAEjE;EACA,MAAM,CAACmF,IAAI,EAAEC,OAAO,CAAC,GAAGpF,QAAQ,CAAC,EAAE,CAAC;EACpC,MAAM,CAACqF,MAAM,EAAEC,SAAS,CAAC,GAAGtF,QAAQ,CAAC,EAAE,CAAC;EACxC,MAAM,CAACuF,YAAY,EAAEC,eAAe,CAAC,GAAGxF,QAAQ,CAAC,IAAI,CAAC;EACtD,MAAM,CAACyF,WAAW,EAAEC,cAAc,CAAC,GAAG1F,QAAQ,CAAC,EAAE,CAAC;;EAElD;EACA,MAAM,CAAC2F,UAAU,EAAEC,aAAa,CAAC,GAAG5F,QAAQ,CAAC,CAAC,CAAC;;EAE/C;EACA,MAAM,CAAC6F,QAAQ,EAAEC,WAAW,CAAC,GAAG9F,QAAQ,CAAC;IACvC+F,OAAO,EAAE,EAAE;IACXC,YAAY,EAAE,EAAE;IAChBC,SAAS,EAAE;EACb,CAAC,CAAC;;EAEF;EACA,MAAM,CAACC,UAAU,EAAEC,aAAa,CAAC,GAAGnG,QAAQ,CAAC,CAAC,CAAC,CAAC;EAChD,MAAM,CAACoG,YAAY,EAAEC,eAAe,CAAC,GAAGrG,QAAQ,CAAC,CAAC,CAAC,CAAC;;EAEpD;EACA,MAAM,CAACsG,0BAA0B,EAAEC,6BAA6B,CAAC,GAAGvG,QAAQ,CAAC,KAAK,CAAC;EACnF,MAAM,CAACwG,gBAAgB,EAAEC,mBAAmB,CAAC,GAAGzG,QAAQ,CAAC,IAAI,CAAC,CAAC,CAAC;EAChE,MAAM,CAAC0G,oBAAoB,EAAEC,uBAAuB,CAAC,GAAG3G,QAAQ,CAAC;IAAE4G,IAAI,EAAE,IAAI;IAAEC,MAAM,EAAE;EAAK,CAAC,CAAC;EAC9F,MAAM,CAACC,qBAAqB,EAAEC,wBAAwB,CAAC,GAAG/G,QAAQ,CAAC,KAAK,CAAC;;EAEzE;EACA,MAAM,CAACgH,eAAe,EAAEC,kBAAkB,CAAC,GAAGjH,QAAQ,CAAC,EAAE,CAAC;EAC1D,MAAM,CAACkH,UAAU,EAAEC,aAAa,CAAC,GAAGnH,QAAQ,CAAC,EAAE,CAAC;EAChD,MAAM,CAACoH,eAAe,EAAEC,kBAAkB,CAAC,GAAGrH,QAAQ,CAAC,IAAI,CAAC;EAC5D,MAAM,CAACsH,qBAAqB,EAAEC,wBAAwB,CAAC,GAAGvH,QAAQ,CAAC,KAAK,CAAC;;EAEzE;EACA,MAAMwH,kBAAkB,GAAIC,QAAQ,IAAK;IACvC3B,WAAW,CAAC;MACV,GAAGD,QAAQ;MACXI,SAAS,EAAEwB;IACb,CAAC,CAAC;;IAEF;IACAtB,aAAa,CAACuB,IAAI,KAAK;MACrB,GAAGA,IAAI;MACPzB,SAAS,EAAE,IAAI;MACf0B,eAAe,EAAE;IACnB,CAAC,CAAC,CAAC;EACL,CAAC;;EAED;EACA,MAAMC,2BAA2B,GAAIC,KAAK,IAAK;IAC7CZ,kBAAkB,CAACY,KAAK,CAACC,MAAM,CAACC,KAAK,CAAC;EACxC,CAAC;;EAED;EACA,MAAMC,sBAAsB,GAAIH,KAAK,IAAK;IACxCV,aAAa,CAACU,KAAK,CAACC,MAAM,CAACC,KAAK,CAAC;EACnC,CAAC;;EAED;EACA9H,SAAS,CAAC,MAAM;IACdgI,UAAU,CAAC,CAAC;EACd,CAAC,EAAE,CAAC7D,UAAU,CAAC,CAAC;;EAEhB;EACA,MAAM8D,eAAe,GAAIT,QAAQ,IAAK;IACpC;IACA,IAAIA,QAAQ,IAAIA,QAAQ,CAACU,QAAQ,CAAC,IAAI,CAAC,EAAE;MACvC,OAAOV,QAAQ,CAACW,KAAK,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;IAChC;IACA,OAAOX,QAAQ;EACjB,CAAC;;EAED;EACA,MAAMY,QAAQ,GAAG,MAAAA,CAAA,KAAY;IAC3B,IAAI;MACFzD,cAAc,CAAC,IAAI,CAAC;MACpB0D,OAAO,CAACC,GAAG,CAAC,oCAAoCnE,UAAU,KAAK,CAAC;;MAEhE;MACA,IAAI;QACF,MAAMoE,QAAQ,GAAG,MAAMxF,WAAW,CAACyF,OAAO,CAACrE,UAAU,CAAC;QACtDkE,OAAO,CAACC,GAAG,CAAC,YAAYC,QAAQ,CAACE,MAAM,OAAO,CAAC;;QAE/C;QACA;QACAtD,OAAO,CAACoD,QAAQ,CAAC;MACnB,CAAC,CAAC,OAAOG,SAAS,EAAE;QAClBL,OAAO,CAACM,KAAK,CAAC,qDAAqD,EAAED,SAAS,CAAC;;QAE/E;QACA,IAAIA,SAAS,CAACE,cAAc,IAAI,CAACF,SAAS,CAACG,QAAQ,IAC/CH,SAAS,CAACI,IAAI,KAAK,cAAc,IAChCJ,SAAS,CAACK,OAAO,IAAIL,SAAS,CAACK,OAAO,CAACb,QAAQ,CAAC,eAAe,CAAE,EAAE;UAEtEG,OAAO,CAACC,GAAG,CAAC,yDAAyD,CAAC;UACtE,MAAM,IAAIU,OAAO,CAACC,OAAO,IAAIC,UAAU,CAACD,OAAO,EAAE,IAAI,CAAC,CAAC;UAEvD,IAAI;YACF;YACA,MAAME,KAAK,GAAGC,YAAY,CAACC,OAAO,CAAC,OAAO,CAAC;YAC3C,MAAMC,OAAO,GAAGtG,aAAa,CAACuG,QAAQ,CAACC,OAAO;;YAE9C;YACA,MAAMC,aAAa,GAAG,MAAMxJ,KAAK,CAACyJ,GAAG,CACnC,GAAGJ,OAAO,SAASnF,UAAU,EAAE,EAC/B;cACEwF,OAAO,EAAE;gBACP,eAAe,EAAE,UAAUR,KAAK;cAClC,CAAC;cACDS,OAAO,EAAE,KAAK,CAAC;YACjB,CACF,CAAC;YAEDvB,OAAO,CAACC,GAAG,CAAC,2CAA2CmB,aAAa,CAACI,IAAI,CAACpB,MAAM,OAAO,CAAC;YACxFtD,OAAO,CAACsE,aAAa,CAACI,IAAI,CAAC;UAC7B,CAAC,CAAC,OAAOC,UAAU,EAAE;YACnBzB,OAAO,CAACM,KAAK,CAAC,uCAAuC,EAAEmB,UAAU,CAAC;YAClE,MAAMA,UAAU;UAClB;QACF,CAAC,MAAM;UACL,MAAMpB,SAAS;QACjB;MACF;IACF,CAAC,CAAC,OAAOC,KAAK,EAAE;MACdN,OAAO,CAACM,KAAK,CAAC,kCAAkC,EAAEA,KAAK,CAAC;;MAExD;MACA,IAAIoB,YAAY,GAAG,iCAAiC;MAEpD,IAAIpB,KAAK,CAACC,cAAc,EAAE;QACxBmB,YAAY,GAAG,8DAA8D;MAC/E,CAAC,MAAM,IAAIpB,KAAK,CAACqB,MAAM,EAAE;QACvBD,YAAY,GAAGpB,KAAK,CAACqB,MAAM;MAC7B,CAAC,MAAM,IAAIrB,KAAK,CAACI,OAAO,EAAE;QACxBgB,YAAY,GAAGpB,KAAK,CAACI,OAAO;MAC9B;MAEA1E,OAAO,CAAC0F,YAAY,CAAC;IACvB,CAAC,SAAS;MACRpF,cAAc,CAAC,KAAK,CAAC;IACvB;EACF,CAAC;;EAED;EACA,MAAMqD,UAAU,GAAG,MAAAA,CAAA,KAAY;IAC7B,IAAI;MACFnD,gBAAgB,CAAC,IAAI,CAAC;MACtBwD,OAAO,CAACC,GAAG,CAAC,gCAAgC,CAAC;;MAE7C;MACA,MAAM2B,UAAU,GAAG,MAAMrG,gBAAgB,CAACsG,SAAS,CAAC/F,UAAU,CAAC;MAC/DkE,OAAO,CAACC,GAAG,CAAC,oBAAoB2B,UAAU,CAACxB,MAAM,EAAE,CAAC;;MAEpD;MACA,MAAM0B,kBAAkB,GAAGF,UAAU,CAACG,MAAM,CAACxD,MAAM,IACjDA,MAAM,CAACyD,YAAY,KAAK,WAAW,IACnCzD,MAAM,CAACyD,YAAY,KAAK,MAAM,IAC9BzD,MAAM,CAAC0D,aAAa,GAAG,CACzB,CAAC;MAEDjC,OAAO,CAACC,GAAG,CAAC,wBAAwB6B,kBAAkB,CAAC1B,MAAM,EAAE,CAAC;;MAEhE;MACA,IAAInD,YAAY,IAAIA,YAAY,CAACiF,SAAS,IAAIjF,YAAY,CAACkF,OAAO,EAAE;QAClEnC,OAAO,CAACC,GAAG,CAAC,sDAAsD,CAAC;QACnED,OAAO,CAACC,GAAG,CAAC,YAAY,EAAE;UACxBxC,OAAO,EAAER,YAAY,CAACQ,OAAO;UAC7ByE,SAAS,EAAEjF,YAAY,CAACiF,SAAS;UACjCC,OAAO,EAAElF,YAAY,CAACkF;QACxB,CAAC,CAAC;;QAEF;QACA,MAAMC,aAAa,GAAGC,MAAM,CAACpF,YAAY,CAACiF,SAAS,IAAI,EAAE,CAAC,CAACI,IAAI,CAAC,CAAC,CAACC,WAAW,CAAC,CAAC;QAC/E,MAAMC,WAAW,GAAGH,MAAM,CAACpF,YAAY,CAACkF,OAAO,IAAI,GAAG,CAAC,CAACG,IAAI,CAAC,CAAC;;QAE9D;QACA,MAAMG,iBAAiB,GAAG,EAAE;QAC5B,MAAMC,oBAAoB,GAAG,EAAE;;QAE/B;QACAZ,kBAAkB,CAACa,OAAO,CAACpE,MAAM,IAAI;UACnC,MAAMqE,eAAe,GAAGP,MAAM,CAAC9D,MAAM,CAAC2D,SAAS,IAAI,EAAE,CAAC,CAACI,IAAI,CAAC,CAAC,CAACC,WAAW,CAAC,CAAC;UAC3E,MAAMM,aAAa,GAAGR,MAAM,CAAC9D,MAAM,CAAC4D,OAAO,IAAI,GAAG,CAAC,CAACG,IAAI,CAAC,CAAC;;UAE1D;UACA,MAAMQ,cAAc,GAAGF,eAAe,KAAKR,aAAa;UACxD,MAAMW,YAAY,GAAGF,aAAa,KAAKL,WAAW;UAClD,MAAMQ,YAAY,GAAGF,cAAc,IAAIC,YAAY;UAEnD/C,OAAO,CAACC,GAAG,CAAC,iCAAiC1B,MAAM,CAACZ,SAAS,GAAG,EAAE;YAChE,kBAAkB,EAAE,IAAIY,MAAM,CAAC2D,SAAS,GAAG;YAC3C,gBAAgB,EAAE,IAAIjF,YAAY,CAACiF,SAAS,GAAG;YAC/C,mBAAmB,EAAEY,cAAc;YACnC,gBAAgB,EAAE,IAAIT,MAAM,CAAC9D,MAAM,CAAC4D,OAAO,CAAC,GAAG;YAC/C,cAAc,EAAE,IAAIE,MAAM,CAACpF,YAAY,CAACkF,OAAO,CAAC,GAAG;YACnD,iBAAiB,EAAEY,YAAY;YAC/B,cAAc,EAAExE,MAAM,CAACyD,YAAY;YACnC,eAAe,EAAEzD,MAAM,CAAC0D,aAAa;YACrC,cAAc,EAAEe;UAClB,CAAC,CAAC;UAEF,IAAIA,YAAY,EAAE;YAChBP,iBAAiB,CAACQ,IAAI,CAAC1E,MAAM,CAAC;UAChC,CAAC,MAAM;YACLmE,oBAAoB,CAACO,IAAI,CAAC1E,MAAM,CAAC;UACnC;QACF,CAAC,CAAC;QAEFyB,OAAO,CAACC,GAAG,CAAC,+BAA+BwC,iBAAiB,CAACrC,MAAM,EAAE,CAAC;QACtEJ,OAAO,CAACC,GAAG,CAAC,2BAA2ByC,oBAAoB,CAACtC,MAAM,EAAE,CAAC;;QAErE;QACA,IAAIqC,iBAAiB,CAACrC,MAAM,GAAG,CAAC,EAAE;UAChCJ,OAAO,CAACC,GAAG,CAAC,+BAA+B,CAAC;UAC5CwC,iBAAiB,CAACE,OAAO,CAACpE,MAAM,IAAI;YAClCyB,OAAO,CAACC,GAAG,CAAC,KAAK1B,MAAM,CAACZ,SAAS,KAAKY,MAAM,CAAC2D,SAAS,MAAM3D,MAAM,CAAC4D,OAAO,KAAK5D,MAAM,CAAC0D,aAAa,IAAI,CAAC;UAC1G,CAAC,CAAC;QACJ,CAAC,MAAM;UACLjC,OAAO,CAACC,GAAG,CAAC,iDAAiD,CAAC;QAChE;;QAEA;QACAwC,iBAAiB,CAACS,IAAI,CAAC,CAACC,CAAC,EAAEC,CAAC,KAAKA,CAAC,CAACnB,aAAa,GAAGkB,CAAC,CAAClB,aAAa,CAAC;QACnES,oBAAoB,CAACQ,IAAI,CAAC,CAACC,CAAC,EAAEC,CAAC,KAAKA,CAAC,CAACnB,aAAa,GAAGkB,CAAC,CAAClB,aAAa,CAAC;;QAEtE;QACA,MAAMoB,cAAc,GAAG,CAAC,GAAGZ,iBAAiB,EAAE,GAAGC,oBAAoB,CAAC;;QAEtE;QACA1F,SAAS,CAACqG,cAAc,CAAC;MAC3B,CAAC,MAAM;QACL;QACArD,OAAO,CAACC,GAAG,CAAC,uDAAuD,CAAC;QACpE6B,kBAAkB,CAACoB,IAAI,CAAC,CAACC,CAAC,EAAEC,CAAC,KAAKA,CAAC,CAACnB,aAAa,GAAGkB,CAAC,CAAClB,aAAa,CAAC;QACpEjF,SAAS,CAAC8E,kBAAkB,CAAC;MAC/B;IACF,CAAC,CAAC,OAAOxB,KAAK,EAAE;MACdN,OAAO,CAACM,KAAK,CAAC,sCAAsC,EAAEA,KAAK,CAAC;MAC5DtE,OAAO,CAAC,uCAAuC,IAAIsE,KAAK,CAACI,OAAO,IAAI,oBAAoB,CAAC,CAAC;IAC5F,CAAC,SAAS;MACRlE,gBAAgB,CAAC,KAAK,CAAC;IACzB;EACF,CAAC;;EAED;EACA,MAAM8G,oBAAoB,GAAG,MAAAA,CAAA,KAAY;IACvC,IAAI,CAACnG,WAAW,CAACmF,IAAI,CAAC,CAAC,EAAE;MACvBtG,OAAO,CAAC,6BAA6B,CAAC;MACtC;IACF;IAEA,IAAI;MACFM,cAAc,CAAC,IAAI,CAAC;MACpB0D,OAAO,CAACC,GAAG,CAAC,6BAA6B9C,WAAW,CAACmF,IAAI,CAAC,CAAC,iBAAiBxG,UAAU,EAAE,CAAC;;MAEzF;MACA,MAAMoE,QAAQ,GAAG,MAAMxF,WAAW,CAACyF,OAAO,CAACrE,UAAU,CAAC;;MAEtD;MACA,MAAMyH,YAAY,GAAGrD,QAAQ,CAAC6B,MAAM,CAACzD,IAAI,IACvCA,IAAI,CAACb,OAAO,CAAC8E,WAAW,CAAC,CAAC,CAAC1C,QAAQ,CAAC1C,WAAW,CAACmF,IAAI,CAAC,CAAC,CAACC,WAAW,CAAC,CAAC,CACtE,CAAC;MAEDvC,OAAO,CAACC,GAAG,CAAC,WAAWsD,YAAY,CAACnD,MAAM,iCAAiC,CAAC;;MAE5E;MACA,MAAMoD,UAAU,GAAGD,YAAY,CAACE,IAAI,CAACnF,IAAI,IACvCA,IAAI,CAACb,OAAO,CAAC8E,WAAW,CAAC,CAAC,KAAKpF,WAAW,CAACmF,IAAI,CAAC,CAAC,CAACC,WAAW,CAAC,CAChE,CAAC;MAED,IAAIiB,UAAU,EAAE;QACdxD,OAAO,CAACC,GAAG,CAAC,gCAAgC,EAAEuD,UAAU,CAAC;;QAEzD;QACA,IAAIA,UAAU,CAACE,mBAAmB,KAAK,YAAY,IAAKF,UAAU,CAACG,eAAe,IAAIH,UAAU,CAACG,eAAe,GAAG,CAAE,EAAE;UACrH3D,OAAO,CAACC,GAAG,CAAC,sCAAsC,EAAEuD,UAAU,CAAC;UAC/DzE,kBAAkB,CAACyE,UAAU,CAAC;UAC9B/E,wBAAwB,CAAC,IAAI,CAAC;UAC9BnC,cAAc,CAAC,KAAK,CAAC;UACrB;QACF;;QAEA;QACA,IAAIkH,UAAU,CAACI,sBAAsB,KAAK,CAAC,EAAE;UAC3C5D,OAAO,CAACC,GAAG,CAAC,qBAAqB,EAAEuD,UAAU,CAAC;UAC9C;QACF;;QAEA;QACAK,gBAAgB,CAACL,UAAU,CAAC;MAC9B,CAAC,MAAM,IAAID,YAAY,CAACnD,MAAM,GAAG,CAAC,EAAE;QAClC;QACA1D,gBAAgB,CAAC6G,YAAY,CAAC;QAC9B3G,oBAAoB,CAAC,IAAI,CAAC;MAC5B,CAAC,MAAM;QACL;QACAZ,OAAO,CAAC,oCAAoCmB,WAAW,CAACmF,IAAI,CAAC,CAAC,kBAAkBxG,UAAU,EAAE,CAAC;MAC/F;IACF,CAAC,CAAC,OAAOwE,KAAK,EAAE;MACdN,OAAO,CAACM,KAAK,CAAC,gCAAgC,EAAEA,KAAK,CAAC;;MAEtD;MACA,IAAIoB,YAAY,GAAG,+BAA+B;MAElD,IAAIpB,KAAK,CAACC,cAAc,EAAE;QACxBmB,YAAY,GAAG,8DAA8D;MAC/E,CAAC,MAAM,IAAIpB,KAAK,CAACwD,MAAM,KAAK,GAAG,EAAE;QAC/BpC,YAAY,GAAG,gBAAgBvE,WAAW,CAACmF,IAAI,CAAC,CAAC,8BAA8BxG,UAAU,EAAE;MAC7F,CAAC,MAAM,IAAIwE,KAAK,CAACqB,MAAM,EAAE;QACvBD,YAAY,GAAGpB,KAAK,CAACqB,MAAM;MAC7B,CAAC,MAAM,IAAIrB,KAAK,CAACI,OAAO,EAAE;QACxBgB,YAAY,GAAGpB,KAAK,CAACI,OAAO;MAC9B;MAEA1E,OAAO,CAAC0F,YAAY,CAAC;IACvB,CAAC,SAAS;MACRpF,cAAc,CAAC,KAAK,CAAC;IACvB;EACF,CAAC;;EAED;EACA,MAAMuH,gBAAgB,GAAIvF,IAAI,IAAK;IACjC;IACA,IAAIA,IAAI,CAACoF,mBAAmB,KAAK,YAAY,IAAKpF,IAAI,CAACqF,eAAe,IAAIrF,IAAI,CAACqF,eAAe,GAAG,CAAE,EAAE;MACnG;MACA5E,kBAAkB,CAACT,IAAI,CAAC;MACxBG,wBAAwB,CAAC,IAAI,CAAC;MAC9B;IACF;IACA;IAAA,KACK,IAAIH,IAAI,CAACsF,sBAAsB,KAAK,CAAC,EAAE;MAC1C;MACA,IAAIG,MAAM,CAACC,OAAO,CAAC,WAAW1F,IAAI,CAACb,OAAO,0CAA0C,CAAC,EAAE;QACrF;QACAwG,eAAe,CAAC3F,IAAI,CAACb,OAAO,CAAC,CAACyG,IAAI,CAAC,MAAM;UACvC;UACA,MAAMC,WAAW,GAAG;YAAE,GAAG7F,IAAI;YAAEsF,sBAAsB,EAAE;UAAE,CAAC;UAC1D1G,eAAe,CAACiH,WAAW,CAAC;UAC5B3G,WAAW,CAAC;YACV,GAAGD,QAAQ;YACXE,OAAO,EAAE0G,WAAW,CAAC1G,OAAO;YAC5BC,YAAY,EAAE;UAChB,CAAC,CAAC;UACF;UACAd,oBAAoB,CAAC,KAAK,CAAC;;UAE3B;UACA+C,UAAU,CAAC,CAAC;QACd,CAAC,CAAC,CAACyE,KAAK,CAAC9D,KAAK,IAAI;UAChBN,OAAO,CAACM,KAAK,CAAC,iDAAiD,EAAEA,KAAK,CAAC;UACvEtE,OAAO,CAAC,kDAAkD,IAAIsE,KAAK,CAACI,OAAO,IAAI,oBAAoB,CAAC,CAAC;QACvG,CAAC,CAAC;MACJ,CAAC,MAAM;QACL;QACA;MACF;IACF,CAAC,MAAM;MACL;MACAxD,eAAe,CAACoB,IAAI,CAAC;MACrBd,WAAW,CAAC;QACV,GAAGD,QAAQ;QACXE,OAAO,EAAEa,IAAI,CAACb,OAAO;QACrBC,YAAY,EAAE;MAChB,CAAC,CAAC;MACF;MACAd,oBAAoB,CAAC,KAAK,CAAC;;MAE3B;MACA,IAAI0B,IAAI,CAAC4D,SAAS,IAAI5D,IAAI,CAAC+F,YAAY,IAAI/F,IAAI,CAAC6D,OAAO,EAAE;QACvDnC,OAAO,CAACC,GAAG,CAAC,8CAA8C3B,IAAI,CAACb,OAAO,KAAK,CAAC;QAC5EkC,UAAU,CAAC,CAAC;MACd;IACF;EACF,CAAC;;EAED;EACA,MAAMsE,eAAe,GAAG,MAAOK,MAAM,IAAK;IACxC,IAAI;MACF;MACA,MAAM5J,WAAW,CAACuJ,eAAe,CAACnI,UAAU,EAAEwI,MAAM,CAAC;MACrDvI,SAAS,CAAC,QAAQuI,MAAM,0BAA0B,CAAC;MACnD,OAAO,IAAI;IACb,CAAC,CAAC,OAAOhE,KAAK,EAAE;MACdN,OAAO,CAACM,KAAK,CAAC,iDAAiD,EAAEA,KAAK,CAAC;MACvEtE,OAAO,CAAC,kDAAkD,IAAIsE,KAAK,CAACI,OAAO,IAAI,oBAAoB,CAAC,CAAC;MACrG,MAAMJ,KAAK;IACb;EACF,CAAC;;EAED;EACA,MAAMiE,gBAAgB,GAAIC,CAAC,IAAK;IAC9B,MAAM;MAAEC,IAAI;MAAEhF;IAAM,CAAC,GAAG+E,CAAC,CAAChF,MAAM;;IAEhC;IACA,IAAIiF,IAAI,KAAK,WAAW,IAAIhF,KAAK,IAAIA,KAAK,KAAK,cAAc,EAAE;MAC7D;MACA,MAAMlB,MAAM,GAAGxB,MAAM,CAAC0G,IAAI,CAACL,CAAC,IAAIA,CAAC,CAACzF,SAAS,KAAK8B,KAAK,CAAC;MAEtD,IAAIlB,MAAM,IAAItB,YAAY,EAAE;QAC1B;QACA;QACA,MAAM+F,YAAY,GAChBX,MAAM,CAAC9D,MAAM,CAAC2D,SAAS,IAAI,EAAE,CAAC,CAACI,IAAI,CAAC,CAAC,KAAKD,MAAM,CAACpF,YAAY,CAACiF,SAAS,IAAI,EAAE,CAAC,CAACI,IAAI,CAAC,CAAC,IACrFD,MAAM,CAAC9D,MAAM,CAAC4D,OAAO,IAAI,GAAG,CAAC,CAACG,IAAI,CAAC,CAAC,KAAKD,MAAM,CAACpF,YAAY,CAACkF,OAAO,IAAI,GAAG,CAAC,CAACG,IAAI,CAAC,CAAC;QAErF,IAAI,CAACU,YAAY,EAAE;UACjBhD,OAAO,CAACC,GAAG,CAAC,mCAAmC,EAAE1B,MAAM,CAAC;UACxDyB,OAAO,CAACC,GAAG,CAAC,gBAAgB,EAAEhD,YAAY,CAAC;UAC3C+C,OAAO,CAACC,GAAG,CAAC,uBAAuB,EAAE;YACnCyE,cAAc,EAAErC,MAAM,CAACpF,YAAY,CAACkF,OAAO,IAAI,GAAG,CAAC;YACnDwC,gBAAgB,EAAEtC,MAAM,CAAC9D,MAAM,CAAC4D,OAAO,IAAI,GAAG;UAChD,CAAC,CAAC;;UAEF;UACA9D,uBAAuB,CAAC;YACtBC,IAAI,EAAErB,YAAY;YAClBsB,MAAM,EAAEA;UACV,CAAC,CAAC;UACFN,6BAA6B,CAAC,IAAI,CAAC;;UAEnC;UACA;QACF;MACF;IACF;IAEAT,WAAW,CAAC;MACV,GAAGD,QAAQ;MACX,CAACkH,IAAI,GAAGhF;IACV,CAAC,CAAC;;IAEF;IACAmF,aAAa,CAACH,IAAI,EAAEhF,KAAK,CAAC;EAC5B,CAAC;;EAED;EACA,MAAMmF,aAAa,GAAGA,CAACH,IAAI,EAAEhF,KAAK,KAAK;IACrC,IAAIa,KAAK,GAAG,IAAI;IAChB,IAAIuE,OAAO,GAAG,IAAI;IAElB,IAAIJ,IAAI,KAAK,cAAc,EAAE;MAC3B;MACA,IAAI,CAAChF,KAAK,IAAIA,KAAK,CAAC6C,IAAI,CAAC,CAAC,KAAK,EAAE,EAAE;QACjChC,KAAK,GAAG,uCAAuC;QAC/C,OAAO,KAAK;MACd;;MAEA;MACA,IAAIwE,KAAK,CAACC,UAAU,CAACtF,KAAK,CAAC,CAAC,IAAIsF,UAAU,CAACtF,KAAK,CAAC,IAAI,CAAC,EAAE;QACtDa,KAAK,GAAG,sCAAsC;QAC9C,OAAO,KAAK;MACd;MAEA,MAAM0E,WAAW,GAAGD,UAAU,CAACtF,KAAK,CAAC;;MAErC;MACA,IAAIxC,YAAY,IAAIA,YAAY,CAACgI,aAAa,IAAID,WAAW,GAAGD,UAAU,CAAC9H,YAAY,CAACgI,aAAa,CAAC,EAAE;QACtGJ,OAAO,GAAG,mBAAmBG,WAAW,yCAAyC/H,YAAY,CAACgI,aAAa,IAAI;MACjH;;MAEA;MACA,IAAI1H,QAAQ,CAACI,SAAS,IAAIJ,QAAQ,CAACI,SAAS,KAAK,cAAc,EAAE;QAC/D,MAAMY,MAAM,GAAGxB,MAAM,CAAC0G,IAAI,CAACL,CAAC,IAAIA,CAAC,CAACzF,SAAS,KAAKJ,QAAQ,CAACI,SAAS,CAAC;QACnE,IAAIY,MAAM,IAAIyG,WAAW,GAAGD,UAAU,CAACxG,MAAM,CAAC0D,aAAa,CAAC,EAAE;UAC5D4C,OAAO,GAAG,mBAAmBG,WAAW,6CAA6CzG,MAAM,CAAC0D,aAAa,oCAAoC;QAC/I;MACF;IACF,CAAC,MAAM,IAAIwC,IAAI,KAAK,WAAW,EAAE;MAC/B;MACA,IAAI,CAAChF,KAAK,IAAIA,KAAK,CAAC6C,IAAI,CAAC,CAAC,KAAK,EAAE,EAAE;QACjChC,KAAK,GAAG,qCAAqC;QAC7C,OAAO,KAAK;MACd;IACF;;IAEA;IACAzC,aAAa,CAACuB,IAAI,KAAK;MACrB,GAAGA,IAAI;MACP,CAACqF,IAAI,GAAGnE;IACV,CAAC,CAAC,CAAC;;IAEH;IACAvC,eAAe,CAACqB,IAAI,KAAK;MACvB,GAAGA,IAAI;MACP,CAACqF,IAAI,GAAGI;IACV,CAAC,CAAC,CAAC;IAEH,OAAO,CAACvE,KAAK;EACf,CAAC;;EAED;EACA,MAAM,CAAC4E,iBAAiB,EAAEC,oBAAoB,CAAC,GAAGzN,QAAQ,CAAC,KAAK,CAAC;EACjE,MAAM,CAAC0N,iBAAiB,EAAEC,oBAAoB,CAAC,GAAG3N,QAAQ,CAAC,KAAK,CAAC;EACjE,MAAM,CAAC4N,kBAAkB,EAAEC,qBAAqB,CAAC,GAAG7N,QAAQ,CAAC;IAC3D8N,KAAK,EAAE,EAAE;IACT9E,OAAO,EAAE,EAAE;IACX+E,SAAS,EAAEA,CAAA,KAAM,CAAC;EACpB,CAAC,CAAC;;EAEF;;EAEA;EACA,MAAMC,YAAY,GAAGA,CAAA,KAAM;IACzB,IAAIC,OAAO,GAAG,IAAI;IAClB,MAAMC,MAAM,GAAG,CAAC,CAAC;IACjB,MAAMC,QAAQ,GAAG,CAAC,CAAC;;IAEnB;IACAV,oBAAoB,CAAC,KAAK,CAAC;;IAE3B;IACA,IAAI,CAAC5H,QAAQ,CAACG,YAAY,IAAIH,QAAQ,CAACG,YAAY,CAAC4E,IAAI,CAAC,CAAC,KAAK,EAAE,EAAE;MACjEsD,MAAM,CAAClI,YAAY,GAAG,uCAAuC;MAC7DiI,OAAO,GAAG,KAAK;IACjB,CAAC,MAAM,IAAIb,KAAK,CAACC,UAAU,CAACxH,QAAQ,CAACG,YAAY,CAAC,CAAC,IAAIqH,UAAU,CAACxH,QAAQ,CAACG,YAAY,CAAC,IAAI,CAAC,EAAE;MAC7FkI,MAAM,CAAClI,YAAY,GAAG,sCAAsC;MAC5DiI,OAAO,GAAG,KAAK;IACjB;;IAEA;IACA,IAAI,CAACpI,QAAQ,CAACI,SAAS,IAAIJ,QAAQ,CAACI,SAAS,CAAC2E,IAAI,CAAC,CAAC,KAAK,EAAE,EAAE;MAC3DsD,MAAM,CAACjI,SAAS,GAAG,qCAAqC;MACxDgI,OAAO,GAAG,KAAK;IACjB;IAEA,IAAIA,OAAO,EAAE;MACX,MAAMX,WAAW,GAAGD,UAAU,CAACxH,QAAQ,CAACG,YAAY,CAAC;;MAErD;MACA,IAAIT,YAAY,IAAIA,YAAY,CAACgI,aAAa,IAAID,WAAW,GAAGD,UAAU,CAAC9H,YAAY,CAACgI,aAAa,CAAC,EAAE;QACtGY,QAAQ,CAACnI,YAAY,GAAG,mBAAmBsH,WAAW,yCAAyC/H,YAAY,CAACgI,aAAa,IAAI;QAC7HE,oBAAoB,CAAC,IAAI,CAAC,CAAC,CAAC;QAC5B;QACA;MACF;;MAEA;MACA,IAAIQ,OAAO,IAAIpI,QAAQ,CAACI,SAAS,IAAIJ,QAAQ,CAACI,SAAS,KAAK,cAAc,EAAE;QAC1E,MAAMY,MAAM,GAAGxB,MAAM,CAAC0G,IAAI,CAACL,CAAC,IAAIA,CAAC,CAACzF,SAAS,KAAKJ,QAAQ,CAACI,SAAS,CAAC;QACnE,IAAIY,MAAM,IAAIyG,WAAW,GAAGD,UAAU,CAACxG,MAAM,CAAC0D,aAAa,CAAC,EAAE;UAC5D4D,QAAQ,CAACnI,YAAY,GAAG,mBAAmBsH,WAAW,6CAA6CzG,MAAM,CAAC0D,aAAa,oCAAoC;UAC3JkD,oBAAoB,CAAC,IAAI,CAAC,CAAC,CAAC;;UAE5B;UACAI,qBAAqB,CAAC;YACpBC,KAAK,EAAE,kCAAkC;YACzC9E,OAAO,EAAE,mBAAmBsE,WAAW,6CAA6CzG,MAAM,CAAC0D,aAAa,8DAA8D;YACtKwD,SAAS,EAAEA,CAAA,KAAM;cACf;cACAK,UAAU,CAAC,CAAC;YACd;UACF,CAAC,CAAC;UACFT,oBAAoB,CAAC,IAAI,CAAC;UAC1B,OAAO,KAAK,CAAC,CAAC;QAChB;MACF;IACF;IAEAxH,aAAa,CAAC+H,MAAM,CAAC;IACrB7H,eAAe,CAAC8H,QAAQ,CAAC;IACzB,OAAOF,OAAO;EAChB,CAAC;;EAED;EACA,MAAMG,UAAU,GAAGA,CAAA,KAAM;IACvB,IAAIzI,UAAU,KAAK,CAAC,EAAE;MACpB;MACA,IAAI,CAACqI,YAAY,CAAC,CAAC,EAAE;QACnB;MACF;IACF,CAAC,MAAM,IAAIrI,UAAU,KAAK,CAAC,EAAE;MAC3B;MACAsC,UAAU,CAAC,CAAC;IACd;IAEArC,aAAa,CAAEyI,cAAc,IAAKA,cAAc,GAAG,CAAC,CAAC;EACvD,CAAC;;EAED;EACA,MAAMC,UAAU,GAAGA,CAAA,KAAM;IACvB1I,aAAa,CAAEyI,cAAc,IAAKA,cAAc,GAAG,CAAC,CAAC;EACvD,CAAC;;EAED;EACA,MAAME,WAAW,GAAGA,CAAA,KAAM;IACxB3I,aAAa,CAAC,CAAC,CAAC;IAChBJ,eAAe,CAAC,IAAI,CAAC;IACrBE,cAAc,CAAC,EAAE,CAAC;IAClBI,WAAW,CAAC;MACVC,OAAO,EAAE,EAAE;MACXC,YAAY,EAAE,EAAE;MAChBC,SAAS,EAAE;IACb,CAAC,CAAC;IACFE,aAAa,CAAC,CAAC,CAAC,CAAC;IACjBE,eAAe,CAAC,CAAC,CAAC,CAAC;EACrB,CAAC;;EAED;EACA;EACA,MAAMmI,2BAA2B,GAAGA,CAAClB,WAAW,EAAEmB,YAAY,KAAK;IACjE,OAAOnL,mBAAmB,CAACgK,WAAW,EAAEmB,YAAY,CAAC;EACvD,CAAC;;EAED;EACA,MAAMC,YAAY,GAAG,MAAAA,CAAA,KAAY;IAC/B;IACA;IACA,IAAIjH,QAAQ;IACZ,IAAIkH,kBAAkB;IACtB,IAAIrB,WAAW;IACf,IAAIsB,SAAS,GAAG,KAAK;IAErB,IAAI;MACFlK,UAAU,CAAC,IAAI,CAAC;;MAEhB;MACA,IAAI,CAACsJ,YAAY,CAAC,CAAC,EAAE;QACnBtJ,UAAU,CAAC,KAAK,CAAC;QACjB;MACF;;MAEA;MACA4I,WAAW,GAAGD,UAAU,CAACxH,QAAQ,CAACG,YAAY,CAAC;;MAE/C;MACAyB,QAAQ,GAAG5B,QAAQ,CAACI,SAAS;;MAE7B;MACA,IAAI,CAACwB,QAAQ,IAAIA,QAAQ,KAAK,EAAE,EAAE;QAChC;QACAtB,aAAa,CAAC;UACZ,GAAGD,UAAU;UACbD,SAAS,EAAE;QACb,CAAC,CAAC;QACFvB,UAAU,CAAC,KAAK,CAAC;QACjB;MACF,CAAC,MAAM,IAAI+C,QAAQ,KAAK,cAAc,EAAE;QACtC;QACAa,OAAO,CAACC,GAAG,CAAC,iCAAiC,CAAC;QAC9C;QACAd,QAAQ,GAAG,cAAc;MAC3B,CAAC,MAAM;QACL;QACAa,OAAO,CAACC,GAAG,CAAC,wBAAwBd,QAAQ,EAAE,CAAC;MACjD;;MAEA;MACAkH,kBAAkB,GAAGH,2BAA2B,CAAClB,WAAW,EAAE/H,YAAY,CAACgI,aAAa,CAAC;;MAEzF;MACAqB,SAAS,GAAG,IAAI;MAChBtG,OAAO,CAACC,GAAG,CAAC,6EAA6E,CAAC;;MAE1F;MACA,IAAId,QAAQ,KAAK,cAAc,EAAE;QAC/Ba,OAAO,CAACC,GAAG,CAAC,6BAA6B,CAAC;MAC5C;MACA;MAAA,KACK,IAAId,QAAQ,IAAIA,QAAQ,KAAK,cAAc,EAAE;QAChD,MAAMZ,MAAM,GAAGxB,MAAM,CAAC0G,IAAI,CAACL,CAAC,IAAIA,CAAC,CAACzF,SAAS,KAAKwB,QAAQ,CAAC;QACzD,IAAIZ,MAAM,IAAIyG,WAAW,GAAGD,UAAU,CAACxG,MAAM,CAAC0D,aAAa,CAAC,EAAE;UAC5DjC,OAAO,CAACC,GAAG,CAAC,aAAad,QAAQ,sCAAsC6F,WAAW,oBAAoBzG,MAAM,CAAC0D,aAAa,GAAG,CAAC;QAChI;MACF;;MAEA;MACA,IAAIhF,YAAY,IAAIA,YAAY,CAACgI,aAAa,IAAID,WAAW,GAAGD,UAAU,CAAC9H,YAAY,CAACgI,aAAa,CAAC,EAAE;QACtGjF,OAAO,CAACC,GAAG,CAAC,mBAAmB+E,WAAW,+BAA+B/H,YAAY,CAACgI,aAAa,GAAG,CAAC;MACzG;;MAEA;MACAjF,OAAO,CAACC,GAAG,CAAC,aAAa,EAAE;QACzBnE,UAAU;QACVwI,MAAM,EAAE/G,QAAQ,CAACE,OAAO;QACxBuH,WAAW;QACX7F,QAAQ;QACRmH,SAAS;QACTD;MACF,CAAC,CAAC;;MAEF;MACA,IAAI,CAACnB,iBAAiB,EAAE;QACtB,MAAMqB,cAAc,GAAG,qCAAqChJ,QAAQ,CAACE,OAAO,QAAQuH,WAAW,WAAW;;QAE1G;QACAO,qBAAqB,CAAC;UACpBC,KAAK,EAAE,wBAAwB;UAC/B9E,OAAO,EAAE6F,cAAc;UACvBd,SAAS,EAAE,MAAAA,CAAA,KAAY;YACrB;YACA,IAAI;cACFrJ,UAAU,CAAC,IAAI,CAAC;;cAEhB;cACA4D,OAAO,CAACC,GAAG,CAAC,qGAAqG,CAAC;cAClH,MAAMvF,WAAW,CAAC8L,iBAAiB,CACjC1K,UAAU,EACVyB,QAAQ,CAACE,OAAO,EAChBuH,WAAW,EACX7F,QAAQ,EACR,IAAI,CAAC;cACP,CAAC;;cAED;cACA,IAAIsH,cAAc,GAAG,qDAAqDJ,kBAAkB,EAAE;cAC9F,IAAIlH,QAAQ,KAAK,cAAc,EAAE;gBAC/BsH,cAAc,IAAI,iCAAiC;cACrD,CAAC,MAAM,IAAItH,QAAQ,EAAE;gBACnB,MAAMZ,MAAM,GAAGxB,MAAM,CAAC0G,IAAI,CAACL,CAAC,IAAIA,CAAC,CAACzF,SAAS,KAAKwB,QAAQ,CAAC;gBACzD,IAAIZ,MAAM,EAAE;kBACVkI,cAAc,IAAI,gCAAgCtH,QAAQ,EAAE;gBAC9D;cACF;;cAEA;cACApD,SAAS,CAAC0K,cAAc,CAAC;;cAEzB;cACAR,WAAW,CAAC,CAAC;;cAEb;cACAlG,QAAQ,CAAC,CAAC;YACZ,CAAC,CAAC,OAAOO,KAAK,EAAE;cACdN,OAAO,CAACM,KAAK,CAAC,mDAAmD,EAAEA,KAAK,CAAC;;cAEzE;cACA,IAAInB,QAAQ,KAAK,cAAc,IAAImB,KAAK,CAACoG,OAAO,EAAE;gBAChD;gBACA,IAAID,cAAc,GAAG,qDAAqDJ,kBAAkB,iCAAiC;gBAC7HtK,SAAS,CAAC0K,cAAc,CAAC;;gBAEzB;gBACAR,WAAW,CAAC,CAAC;;gBAEb;gBACAlG,QAAQ,CAAC,CAAC;gBACV;cACF;;cAEA;cACA,IAAI2B,YAAY,GAAG,kDAAkD;cAErE,IAAIpB,KAAK,CAACE,QAAQ,EAAE;gBAAA,IAAAmG,oBAAA;gBAClB;gBACA,MAAM7C,MAAM,GAAGxD,KAAK,CAACE,QAAQ,CAACsD,MAAM;gBACpC,MAAMnC,MAAM,GAAG,EAAAgF,oBAAA,GAAArG,KAAK,CAACE,QAAQ,CAACgB,IAAI,cAAAmF,oBAAA,uBAAnBA,oBAAA,CAAqBhF,MAAM,KAAIrB,KAAK,CAACI,OAAO;gBAE3D,IAAIoD,MAAM,KAAK,GAAG,EAAE;kBAClB;kBACA,IAAInC,MAAM,CAAC9B,QAAQ,CAAC,eAAe,CAAC,EAAE;oBACpC6B,YAAY,GAAG,uGAAuG;kBACxH,CAAC,MAAM,IAAIC,MAAM,CAAC9B,QAAQ,CAAC,YAAY,CAAC,EAAE;oBACxC6B,YAAY,GAAG,4EAA4E;kBAC7F,CAAC,MAAM;oBACLA,YAAY,GAAGC,MAAM;kBACvB;gBACF,CAAC,MAAM,IAAImC,MAAM,KAAK,GAAG,EAAE;kBACzB;kBACA,IAAI3E,QAAQ,KAAK,cAAc,IAAIwC,MAAM,CAAC9B,QAAQ,CAAC,aAAa,CAAC,EAAE;oBACjE,IAAI4G,cAAc,GAAG,qDAAqDJ,kBAAkB,iCAAiC;oBAC7HtK,SAAS,CAAC0K,cAAc,CAAC;;oBAEzB;oBACAR,WAAW,CAAC,CAAC;;oBAEb;oBACAlG,QAAQ,CAAC,CAAC;oBACV;kBACF,CAAC,MAAM;oBACL2B,YAAY,GAAG,8BAA8BC,MAAM,EAAE;kBACvD;gBACF,CAAC,MAAM;kBACLD,YAAY,GAAG,sBAAsBoC,MAAM,MAAMnC,MAAM,EAAE;gBAC3D;cACF,CAAC,MAAM,IAAIrB,KAAK,CAACsG,OAAO,EAAE;gBACxB;gBACAlF,YAAY,GAAG,+DAA+D;cAChF,CAAC,MAAM,IAAIpB,KAAK,CAACqB,MAAM,IAAIxC,QAAQ,KAAK,cAAc,EAAE;gBACtD;gBACAuC,YAAY,GAAGpB,KAAK,CAACqB,MAAM;gBAC3B,IAAIrB,KAAK,CAACwD,MAAM,KAAK,GAAG,IAAIxD,KAAK,CAACoG,OAAO,EAAE;kBACzC,IAAID,cAAc,GAAG,qDAAqDJ,kBAAkB,iCAAiC;kBAC7HtK,SAAS,CAAC0K,cAAc,CAAC;;kBAEzB;kBACAR,WAAW,CAAC,CAAC;;kBAEb;kBACAlG,QAAQ,CAAC,CAAC;kBACV;gBACF;cACF,CAAC,MAAM;gBACL;gBACA2B,YAAY,GAAGpB,KAAK,CAACI,OAAO,IAAIJ,KAAK,CAACqB,MAAM,IAAI,oBAAoB;cACtE;cAEA3F,OAAO,CAAC0F,YAAY,CAAC;YACvB,CAAC,SAAS;cACRtF,UAAU,CAAC,KAAK,CAAC;YACnB;UACF;QACF,CAAC,CAAC;QACFiJ,oBAAoB,CAAC,IAAI,CAAC;QAC1BjJ,UAAU,CAAC,KAAK,CAAC;QACjB;MACF;;MAEA;MACA4D,OAAO,CAACC,GAAG,CAAC,6DAA6D,CAAC;MAC1ED,OAAO,CAACC,GAAG,CAAC,eAAe,EAAEnE,UAAU,CAAC;MACxCkE,OAAO,CAACC,GAAG,CAAC,YAAY,EAAE1C,QAAQ,CAACE,OAAO,CAAC;MAC3CuC,OAAO,CAACC,GAAG,CAAC,iBAAiB,EAAE+E,WAAW,CAAC;MAC3ChF,OAAO,CAACC,GAAG,CAAC,cAAc,EAAEd,QAAQ,EAAE,OAAOA,QAAQ,CAAC;MACtDa,OAAO,CAACC,GAAG,CAAC,cAAc,EAAEqG,SAAS,CAAC;;MAEtC;MACAtG,OAAO,CAACC,GAAG,CAAC,oGAAoG,CAAC;MACjH,MAAMvF,WAAW,CAAC8L,iBAAiB,CACjC1K,UAAU,EACVyB,QAAQ,CAACE,OAAO,EAChBuH,WAAW,EACX7F,QAAQ,EACR,IAAI,CAAC;MACP,CAAC;;MAED;MACA,IAAIsH,cAAc,GAAG,qDAAqDJ,kBAAkB,EAAE;MAC9F,IAAIlH,QAAQ,KAAK,cAAc,EAAE;QAC/BsH,cAAc,IAAI,iCAAiC;MACrD,CAAC,MAAM,IAAItH,QAAQ,EAAE;QACnB,MAAMZ,MAAM,GAAGxB,MAAM,CAAC0G,IAAI,CAACL,CAAC,IAAIA,CAAC,CAACzF,SAAS,KAAKwB,QAAQ,CAAC;QACzD,IAAIZ,MAAM,EAAE;UACVkI,cAAc,IAAI,gCAAgCtH,QAAQ,EAAE;QAC9D;MACF;;MAEA;MACApD,SAAS,CAAC0K,cAAc,CAAC;;MAEzB;MACAR,WAAW,CAAC,CAAC;;MAEb;MACAlG,QAAQ,CAAC,CAAC;IACZ,CAAC,CAAC,OAAOO,KAAK,EAAE;MACdN,OAAO,CAACM,KAAK,CAAC,mDAAmD,EAAEA,KAAK,CAAC;;MAEzE;MACA,IAAInB,QAAQ,KAAK,cAAc,IAAImB,KAAK,CAACoG,OAAO,EAAE;QAChD;QACA,IAAID,cAAc,GAAG,qDAAqDJ,kBAAkB,iCAAiC;QAC7HtK,SAAS,CAAC0K,cAAc,CAAC;;QAEzB;QACAR,WAAW,CAAC,CAAC;;QAEb;QACAlG,QAAQ,CAAC,CAAC;QACV;MACF;;MAEA;MACA,IAAI2B,YAAY,GAAG,kDAAkD;MAErE,IAAIpB,KAAK,CAACE,QAAQ,EAAE;QAAA,IAAAqG,qBAAA;QAClB;QACA,MAAM/C,MAAM,GAAGxD,KAAK,CAACE,QAAQ,CAACsD,MAAM;QACpC,MAAMnC,MAAM,GAAG,EAAAkF,qBAAA,GAAAvG,KAAK,CAACE,QAAQ,CAACgB,IAAI,cAAAqF,qBAAA,uBAAnBA,qBAAA,CAAqBlF,MAAM,KAAIrB,KAAK,CAACI,OAAO;QAE3D,IAAIoD,MAAM,KAAK,GAAG,EAAE;UAClB;UACA,IAAInC,MAAM,CAAC9B,QAAQ,CAAC,eAAe,CAAC,EAAE;YACpC6B,YAAY,GAAG,uGAAuG;UACxH,CAAC,MAAM,IAAIC,MAAM,CAAC9B,QAAQ,CAAC,YAAY,CAAC,EAAE;YACxC6B,YAAY,GAAG,4EAA4E;UAC7F,CAAC,MAAM;YACLA,YAAY,GAAGC,MAAM;UACvB;QACF,CAAC,MAAM,IAAImC,MAAM,KAAK,GAAG,EAAE;UACzB;UACA,IAAI3E,QAAQ,KAAK,cAAc,IAAIwC,MAAM,CAAC9B,QAAQ,CAAC,aAAa,CAAC,EAAE;YACjE,IAAI4G,cAAc,GAAG,qDAAqDJ,kBAAkB,iCAAiC;YAC7HtK,SAAS,CAAC0K,cAAc,CAAC;;YAEzB;YACAR,WAAW,CAAC,CAAC;;YAEb;YACAlG,QAAQ,CAAC,CAAC;YACV;UACF,CAAC,MAAM;YACL2B,YAAY,GAAG,8BAA8BC,MAAM,EAAE;UACvD;QACF,CAAC,MAAM;UACLD,YAAY,GAAG,sBAAsBoC,MAAM,MAAMnC,MAAM,EAAE;QAC3D;MACF,CAAC,MAAM,IAAIrB,KAAK,CAACsG,OAAO,EAAE;QACxB;QACAlF,YAAY,GAAG,+DAA+D;MAChF,CAAC,MAAM,IAAIpB,KAAK,CAACqB,MAAM,IAAIxC,QAAQ,KAAK,cAAc,EAAE;QACtD;QACAuC,YAAY,GAAGpB,KAAK,CAACqB,MAAM;QAC3B,IAAIrB,KAAK,CAACwD,MAAM,KAAK,GAAG,IAAIxD,KAAK,CAACoG,OAAO,EAAE;UACzC,IAAID,cAAc,GAAG,qDAAqDJ,kBAAkB,iCAAiC;UAC7HtK,SAAS,CAAC0K,cAAc,CAAC;;UAEzB;UACAR,WAAW,CAAC,CAAC;;UAEb;UACAlG,QAAQ,CAAC,CAAC;UACV;QACF;MACF,CAAC,MAAM;QACL;QACA2B,YAAY,GAAGpB,KAAK,CAACI,OAAO,IAAIJ,KAAK,CAACqB,MAAM,IAAI,oBAAoB;MACtE;MAEA3F,OAAO,CAAC0F,YAAY,CAAC;IACvB,CAAC,SAAS;MACRtF,UAAU,CAAC,KAAK,CAAC;IACnB;EACF,CAAC;;EAED;EACA,MAAM0K,WAAW,GAAGA,CAAA,KAAM;IACxB,oBACEpL,OAAA,CAAC7D,GAAG;MAAAkP,QAAA,gBACFrL,OAAA,CAAC3D,UAAU;QAACiP,OAAO,EAAC,WAAW;QAACC,EAAE,EAAE;UAAEC,EAAE,EAAE,CAAC;UAAEC,UAAU,EAAE;QAAO,CAAE;QAAAJ,QAAA,EAAC;MAEnE;QAAAK,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAY,CAAC,eAGb7L,OAAA,CAAC5D,KAAK;QAACmP,EAAE,EAAE;UAAEO,CAAC,EAAE,GAAG;UAAEN,EAAE,EAAE,CAAC;UAAEO,KAAK,EAAE;QAAO,CAAE;QAAAV,QAAA,eAC1CrL,OAAA,CAAC7D,GAAG;UAACoP,EAAE,EAAE;YAAES,OAAO,EAAE,MAAM;YAAEC,UAAU,EAAE,QAAQ;YAAEF,KAAK,EAAE;UAAO,CAAE;UAAAV,QAAA,gBAChErL,OAAA,CAAC3D,UAAU;YAACiP,OAAO,EAAC,WAAW;YAACC,EAAE,EAAE;cAAEW,EAAE,EAAE,CAAC;cAAEC,QAAQ,EAAE;YAAO,CAAE;YAAAd,QAAA,EAAC;UAEjE;YAAAK,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAY,CAAC,eACb7L,OAAA,CAAC1D,SAAS;YACR8P,IAAI,EAAC,OAAO;YACZC,KAAK,EAAC,SAAS;YACff,OAAO,EAAC,UAAU;YAClBvH,KAAK,EAAEtC,WAAY;YACnB6K,QAAQ,EAAGxD,CAAC,IAAKpH,cAAc,CAACoH,CAAC,CAAChF,MAAM,CAACC,KAAK,CAAE;YAChDwI,WAAW,EAAC,yBAAyB;YACrChB,EAAE,EAAE;cAAEiB,QAAQ,EAAE,CAAC;cAAEN,EAAE,EAAE;YAAE;UAAE;YAAAR,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC5B,CAAC,eACF7L,OAAA,CAACzD,MAAM;YACL+O,OAAO,EAAC,WAAW;YACnBmB,KAAK,EAAC,SAAS;YACfC,OAAO,EAAE9E,oBAAqB;YAC9B+E,QAAQ,EAAEhM,WAAW,IAAI,CAACc,WAAW,CAACmF,IAAI,CAAC,CAAE;YAC7CgG,SAAS,EAAEjM,WAAW,gBAAGX,OAAA,CAACjD,gBAAgB;cAACqP,IAAI,EAAE;YAAG;cAAAV,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,gBAAG7L,OAAA,CAAC5B,UAAU;cAACyO,QAAQ,EAAC;YAAO;cAAAnB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAE;YAC1FO,IAAI,EAAC,OAAO;YACZb,EAAE,EAAE;cAAEY,QAAQ,EAAE,MAAM;cAAEW,MAAM,EAAE;YAAO,CAAE;YAAAzB,QAAA,EAC1C;UAED;YAAAK,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACN;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACD,CAAC,eAGR7L,OAAA,CAAC5D,KAAK;QAACmP,EAAE,EAAE;UAAEO,CAAC,EAAE,GAAG;UAAEC,KAAK,EAAE;QAAO,CAAE;QAAAV,QAAA,gBACnCrL,OAAA,CAAC3D,UAAU;UAACiP,OAAO,EAAC,WAAW;UAACC,EAAE,EAAE;YAAEC,EAAE,EAAE;UAAE,CAAE;UAAAH,QAAA,EAAC;QAE/C;UAAAK,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAY,CAAC,EAEZlL,WAAW,gBACVX,OAAA,CAAC7D,GAAG;UAACoP,EAAE,EAAE;YAAES,OAAO,EAAE,MAAM;YAAEe,cAAc,EAAE,QAAQ;YAAEC,EAAE,EAAE;UAAE,CAAE;UAAA3B,QAAA,eAC5DrL,OAAA,CAACjD,gBAAgB;YAACqP,IAAI,EAAE;UAAG;YAAAV,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC3B,CAAC,GACJ1K,IAAI,CAACuD,MAAM,KAAK,CAAC,gBACnB1E,OAAA,CAAClD,KAAK;UAACmQ,QAAQ,EAAC,MAAM;UAAC1B,EAAE,EAAE;YAAE2B,EAAE,EAAE;UAAI,CAAE;UAAA7B,QAAA,eACrCrL,OAAA,CAAC3D,UAAU;YAACiP,OAAO,EAAC,SAAS;YAAAD,QAAA,EAAC;UAA2C;YAAAK,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAY;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACjF,CAAC,gBAER7L,OAAA,CAACrC,cAAc;UAACwP,SAAS,EAAE/Q,KAAM;UAACkP,OAAO,EAAC,UAAU;UAACC,EAAE,EAAE;YAAE6B,SAAS,EAAE,OAAO;YAAEC,QAAQ,EAAE,MAAM;YAAEtB,KAAK,EAAE;UAAO,CAAE;UAAAV,QAAA,eAC/GrL,OAAA,CAACxC,KAAK;YAAC4O,IAAI,EAAC,OAAO;YAACkB,YAAY;YAAAjC,QAAA,gBAC9BrL,OAAA,CAACpC,SAAS;cAAAyN,QAAA,eACRrL,OAAA,CAACnC,QAAQ;gBAAC0N,EAAE,EAAE;kBAAE,MAAM,EAAE;oBAAEE,UAAU,EAAE,MAAM;oBAAEyB,EAAE,EAAE,CAAC;oBAAEK,OAAO,EAAE;kBAAU;gBAAE,CAAE;gBAAAlC,QAAA,gBAC1ErL,OAAA,CAACtC,SAAS;kBAAA2N,QAAA,EAAC;gBAAO;kBAAAK,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAW,CAAC,eAC9B7L,OAAA,CAACtC,SAAS;kBAAA2N,QAAA,EAAC;gBAAS;kBAAAK,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAW,CAAC,eAChC7L,OAAA,CAACtC,SAAS;kBAAA2N,QAAA,EAAC;gBAAU;kBAAAK,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAW,CAAC,eACjC7L,OAAA,CAACtC,SAAS;kBAAA2N,QAAA,EAAC;gBAAK;kBAAAK,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAW,CAAC,eAC5B7L,OAAA,CAACtC,SAAS;kBAAA2N,QAAA,EAAC;gBAAK;kBAAAK,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAW,CAAC,eAC5B7L,OAAA,CAACtC,SAAS;kBAAC8P,KAAK,EAAC,QAAQ;kBAACjC,EAAE,EAAE;oBAAEQ,KAAK,EAAE;kBAAO,CAAE;kBAAAV,QAAA,EAAC;gBAAI;kBAAAK,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAW,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACzD;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACF,CAAC,eACZ7L,OAAA,CAACvC,SAAS;cAAA4N,QAAA,EACPlK,IAAI,CAACsM,GAAG,CAAE7K,IAAI,iBACb5C,OAAA,CAACnC,QAAQ;gBAEP6P,KAAK;gBACLhB,OAAO,EAAEA,CAAA,KAAMvE,gBAAgB,CAACvF,IAAI,CAAE;gBACtC2I,EAAE,EAAE;kBACFoC,MAAM,EAAE,SAAS;kBACjB,SAAS,EAAE;oBAAEJ,OAAO,EAAE;kBAAU,CAAC;kBACjC,MAAM,EAAE;oBAAEL,EAAE,EAAE;kBAAI;gBACpB,CAAE;gBAAA7B,QAAA,gBAEFrL,OAAA,CAACtC,SAAS;kBAAC6N,EAAE,EAAE;oBAAEE,UAAU,EAAE;kBAAS,CAAE;kBAAAJ,QAAA,EAAEzI,IAAI,CAACb;gBAAO;kBAAA2J,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAY,CAAC,eACnE7L,OAAA,CAACtC,SAAS;kBAAA2N,QAAA,EAAEzI,IAAI,CAAC4D,SAAS,IAAI;gBAAK;kBAAAkF,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAY,CAAC,eAChD7L,OAAA,CAACtC,SAAS;kBAAA2N,QAAA,EAAEzI,IAAI,CAAC6D,OAAO,IAAI;gBAAK;kBAAAiF,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAY,CAAC,eAC9C7L,OAAA,CAACtC,SAAS;kBAAA2N,QAAA,EAAEzI,IAAI,CAAC2G,aAAa,IAAI;gBAAK;kBAAAmC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAY,CAAC,eACpD7L,OAAA,CAACtC,SAAS;kBAAA2N,QAAA,eACRrL,OAAA,CAAC9C,IAAI;oBACHkP,IAAI,EAAC,OAAO;oBACZC,KAAK,EAAE5M,YAAY,CAACmD,IAAI,CAAC,GAAG,OAAO,GAAGlD,gBAAgB,CAACkD,IAAI,CAAC,GAAG,YAAY,GAAGA,IAAI,CAACoF,mBAAoB;oBACvGyE,KAAK,EAAEhN,YAAY,CAACmD,IAAI,CAAC,GAAG,OAAO,GAAGlD,gBAAgB,CAACkD,IAAI,CAAC,GAAG,SAAS,GAAGjD,kBAAkB,CAACiD,IAAI,CAACoF,mBAAmB,CAAE;oBACxHuD,EAAE,EAAE;sBAAEuB,MAAM,EAAE,MAAM;sBAAE,kBAAkB,EAAE;wBAAEc,EAAE,EAAE,CAAC;wBAAEV,EAAE,EAAE,CAAC;wBAAEL,QAAQ,EAAE;sBAAS;oBAAE;kBAAE;oBAAAnB,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAClF;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACO,CAAC,eACZ7L,OAAA,CAACtC,SAAS;kBAAC8P,KAAK,EAAC,QAAQ;kBAAAnC,QAAA,eACvBrL,OAAA,CAAC/C,UAAU;oBACTmP,IAAI,EAAC,OAAO;oBACZM,OAAO,EAAG5D,CAAC,IAAK;sBACdA,CAAC,CAAC+E,eAAe,CAAC,CAAC;sBACnBrM,eAAe,CAACoB,IAAI,CAAC;sBACrBW,wBAAwB,CAAC,IAAI,CAAC;oBAChC,CAAE;oBAAA8H,QAAA,eAEFrL,OAAA,CAACpB,QAAQ;sBAACiO,QAAQ,EAAC;oBAAO;sBAAAnB,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAE;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACnB;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACJ,CAAC;cAAA,GAhCPjJ,IAAI,CAACb,OAAO;gBAAA2J,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAiCT,CACX;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACO,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACP;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACM,CACjB;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACI,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACL,CAAC;EAEV,CAAC;;EAED;EACA,MAAMiC,WAAW,GAAGA,CAAA,KAAM;IACxB,IAAI,CAACvM,YAAY,EAAE,OAAO,IAAI;IAE9B,oBACEvB,OAAA,CAAC7D,GAAG;MAAAkP,QAAA,gBACFrL,OAAA,CAAC3D,UAAU;QAACiP,OAAO,EAAC,WAAW;QAACC,EAAE,EAAE;UAAEC,EAAE,EAAE,CAAC;UAAEC,UAAU,EAAE;QAAO,CAAE;QAAAJ,QAAA,EAAC;MAEnE;QAAAK,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAY,CAAC,eAGb7L,OAAA,CAAC5D,KAAK;QAACmP,EAAE,EAAE;UAAEO,CAAC,EAAE,GAAG;UAAEN,EAAE,EAAE,CAAC;UAAEO,KAAK,EAAE;QAAO,CAAE;QAAAV,QAAA,eAC1CrL,OAAA,CAAC7D,GAAG;UAACoP,EAAE,EAAE;YAAES,OAAO,EAAE,MAAM;YAAE+B,QAAQ,EAAE,MAAM;YAAE9B,UAAU,EAAE,QAAQ;YAAEF,KAAK,EAAE;UAAO,CAAE;UAAAV,QAAA,gBAClFrL,OAAA,CAAC3D,UAAU;YAACiP,OAAO,EAAC,WAAW;YAACC,EAAE,EAAE;cAAEW,EAAE,EAAE,CAAC;cAAET,UAAU,EAAE;YAAO,CAAE;YAAAJ,QAAA,GAAC,oBAC/C,eAAArL,OAAA;cAAMgO,KAAK,EAAE;gBAAEvB,KAAK,EAAE;cAAU,CAAE;cAAApB,QAAA,EAAE9J,YAAY,CAACQ;YAAO;cAAA2J,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACxE,CAAC,eAEb7L,OAAA,CAAC7D,GAAG;YAACoP,EAAE,EAAE;cAAES,OAAO,EAAE,MAAM;cAAE+B,QAAQ,EAAE,MAAM;cAAEE,GAAG,EAAE,CAAC;cAAEC,EAAE,EAAE;YAAO,CAAE;YAAA7C,QAAA,gBACjErL,OAAA,CAAC7D,GAAG;cAACoP,EAAE,EAAE;gBAAES,OAAO,EAAE,MAAM;gBAAEC,UAAU,EAAE;cAAS,CAAE;cAAAZ,QAAA,gBACjDrL,OAAA,CAAC3D,UAAU;gBAACiP,OAAO,EAAC,OAAO;gBAACC,EAAE,EAAE;kBAAEW,EAAE,EAAE,GAAG;kBAAET,UAAU,EAAE;gBAAS,CAAE;gBAAAJ,QAAA,EAAC;cAAU;gBAAAK,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAY,CAAC,eAC1F7L,OAAA,CAAC3D,UAAU;gBAACiP,OAAO,EAAC,OAAO;gBAAAD,QAAA,EAAE9J,YAAY,CAACiF,SAAS,IAAI;cAAK;gBAAAkF,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAa,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACvE,CAAC,eAEN7L,OAAA,CAAC7D,GAAG;cAACoP,EAAE,EAAE;gBAAES,OAAO,EAAE,MAAM;gBAAEC,UAAU,EAAE;cAAS,CAAE;cAAAZ,QAAA,gBACjDrL,OAAA,CAAC3D,UAAU;gBAACiP,OAAO,EAAC,OAAO;gBAACC,EAAE,EAAE;kBAAEW,EAAE,EAAE,GAAG;kBAAET,UAAU,EAAE;gBAAS,CAAE;gBAAAJ,QAAA,EAAC;cAAW;gBAAAK,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAY,CAAC,eAC3F7L,OAAA,CAAC3D,UAAU;gBAACiP,OAAO,EAAC,OAAO;gBAAAD,QAAA,EAAE9J,YAAY,CAACkF,OAAO,IAAI;cAAK;gBAAAiF,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAa,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACrE,CAAC,eAEN7L,OAAA,CAAC7D,GAAG;cAACoP,EAAE,EAAE;gBAAES,OAAO,EAAE,MAAM;gBAAEC,UAAU,EAAE;cAAS,CAAE;cAAAZ,QAAA,gBACjDrL,OAAA,CAAC3D,UAAU;gBAACiP,OAAO,EAAC,OAAO;gBAACC,EAAE,EAAE;kBAAEW,EAAE,EAAE,GAAG;kBAAET,UAAU,EAAE;gBAAS,CAAE;gBAAAJ,QAAA,EAAC;cAAc;gBAAAK,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAY,CAAC,eAC9F7L,OAAA,CAAC3D,UAAU;gBAACiP,OAAO,EAAC,OAAO;gBAAAD,QAAA,GAAE9J,YAAY,CAACgI,aAAa,IAAI,KAAK,EAAC,IAAE;cAAA;gBAAAmC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAY,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC7E,CAAC,eAEN7L,OAAA,CAAC7D,GAAG;cAACoP,EAAE,EAAE;gBAAES,OAAO,EAAE,MAAM;gBAAEC,UAAU,EAAE;cAAS,CAAE;cAAAZ,QAAA,gBACjDrL,OAAA,CAAC3D,UAAU;gBAACiP,OAAO,EAAC,OAAO;gBAACC,EAAE,EAAE;kBAAEW,EAAE,EAAE,GAAG;kBAAET,UAAU,EAAE;gBAAS,CAAE;gBAAAJ,QAAA,EAAC;cAAM;gBAAAK,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAY,CAAC,eACtF7L,OAAA,CAAC9C,IAAI;gBACHmP,KAAK,EAAE9K,YAAY,CAACyG,mBAAmB,IAAI,KAAM;gBACjDoE,IAAI,EAAC,OAAO;gBACZK,KAAK,EAAE9M,kBAAkB,CAAC4B,YAAY,CAACyG,mBAAmB,CAAE;gBAC5DsD,OAAO,EAAC,UAAU;gBAClBC,EAAE,EAAE;kBAAEuB,MAAM,EAAE,MAAM;kBAAE,kBAAkB,EAAE;oBAAEc,EAAE,EAAE,CAAC;oBAAEV,EAAE,EAAE;kBAAE;gBAAE;cAAE;gBAAAxB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC9D,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACC,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACD,CAAC,eAER7L,OAAA,CAAC5D,KAAK;QAACmP,EAAE,EAAE;UAAEO,CAAC,EAAE,CAAC;UAAEC,KAAK,EAAE;QAAO,CAAE;QAAAV,QAAA,gBACjCrL,OAAA,CAAC3D,UAAU;UAACiP,OAAO,EAAC,WAAW;UAAC6C,YAAY;UAAC5C,EAAE,EAAE;YAAEE,UAAU,EAAE;UAAO,CAAE;UAAAJ,QAAA,EAAC;QAEzE;UAAAK,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAY,CAAC,eAGb7L,OAAA,CAAC7D,GAAG;UAACoP,EAAE,EAAE;YAAES,OAAO,EAAE,MAAM;YAAE+B,QAAQ,EAAE,MAAM;YAAEE,GAAG,EAAE,CAAC;YAAEzC,EAAE,EAAE;UAAE,CAAE;UAAAH,QAAA,gBAC5DrL,OAAA,CAAC7D,GAAG;YAACoP,EAAE,EAAE;cAAEO,CAAC,EAAE,CAAC;cAAEyB,OAAO,EAAE,SAAS;cAAEa,YAAY,EAAE,CAAC;cAAEC,IAAI,EAAE,UAAU;cAAElC,QAAQ,EAAE,OAAO;cAAEmC,QAAQ,EAAE;YAAQ,CAAE;YAAAjD,QAAA,gBAC7GrL,OAAA,CAAC3D,UAAU;cAACiP,OAAO,EAAC,SAAS;cAACC,EAAE,EAAE;gBAAEE,UAAU,EAAE,MAAM;gBAAEgB,KAAK,EAAE,cAAc;gBAAET,OAAO,EAAE,OAAO;gBAAER,EAAE,EAAE;cAAI,CAAE;cAAAH,QAAA,EAAC;YAE5G;cAAAK,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAY,CAAC,eACb7L,OAAA,CAAC7D,GAAG;cAACoP,EAAE,EAAE;gBAAES,OAAO,EAAE,MAAM;gBAAEe,cAAc,EAAE,eAAe;gBAAEvB,EAAE,EAAE;cAAI,CAAE;cAAAH,QAAA,gBACrErL,OAAA,CAAC3D,UAAU;gBAACiP,OAAO,EAAC,OAAO;gBAACC,EAAE,EAAE;kBAAEE,UAAU,EAAE,QAAQ;kBAAEoB,QAAQ,EAAE;gBAAS,CAAE;gBAAAxB,QAAA,EAAC;cAAc;gBAAAK,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAY,CAAC,eACzG7L,OAAA,CAAC3D,UAAU;gBAACiP,OAAO,EAAC,OAAO;gBAACC,EAAE,EAAE;kBAAEsB,QAAQ,EAAE;gBAAS,CAAE;gBAAAxB,QAAA,GAAE9J,YAAY,CAACgI,aAAa,IAAI,KAAK,EAAC,IAAE;cAAA;gBAAAmC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAY,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACzG,CAAC,eACN7L,OAAA,CAAC7D,GAAG;cAACoP,EAAE,EAAE;gBAAES,OAAO,EAAE,MAAM;gBAAEe,cAAc,EAAE;cAAgB,CAAE;cAAA1B,QAAA,gBAC5DrL,OAAA,CAAC3D,UAAU;gBAACiP,OAAO,EAAC,OAAO;gBAACC,EAAE,EAAE;kBAAEE,UAAU,EAAE,QAAQ;kBAAEoB,QAAQ,EAAE;gBAAS,CAAE;gBAAAxB,QAAA,EAAC;cAAc;gBAAAK,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAY,CAAC,eACzG7L,OAAA,CAAC9C,IAAI;gBACHmP,KAAK,EAAE9K,YAAY,CAACyG,mBAAmB,IAAI,KAAM;gBACjDoE,IAAI,EAAC,OAAO;gBACZK,KAAK,EAAE9M,kBAAkB,CAAC4B,YAAY,CAACyG,mBAAmB,CAAE;gBAC5DsD,OAAO,EAAC,UAAU;gBAClBC,EAAE,EAAE;kBAAEuB,MAAM,EAAE,MAAM;kBAAE,kBAAkB,EAAE;oBAAEc,EAAE,EAAE,CAAC;oBAAEV,EAAE,EAAE,CAAC;oBAAEL,QAAQ,EAAE;kBAAS;gBAAE;cAAE;gBAAAnB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAClF,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACC,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC,eAEN7L,OAAA,CAAC7D,GAAG;YAACoP,EAAE,EAAE;cAAEO,CAAC,EAAE,CAAC;cAAEyB,OAAO,EAAE,SAAS;cAAEa,YAAY,EAAE,CAAC;cAAEC,IAAI,EAAE,UAAU;cAAElC,QAAQ,EAAE,OAAO;cAAEmC,QAAQ,EAAE;YAAQ,CAAE;YAAAjD,QAAA,gBAC7GrL,OAAA,CAAC3D,UAAU;cAACiP,OAAO,EAAC,SAAS;cAACC,EAAE,EAAE;gBAAEE,UAAU,EAAE,MAAM;gBAAEgB,KAAK,EAAE,gBAAgB;gBAAET,OAAO,EAAE,OAAO;gBAAER,EAAE,EAAE;cAAI,CAAE;cAAAH,QAAA,EAAC;YAE9G;cAAAK,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAY,CAAC,EACZhK,QAAQ,CAACI,SAAS,IAAIJ,QAAQ,CAACI,SAAS,KAAK,cAAc,GAAG,CAAC,MAAM;cACpE,MAAMY,MAAM,GAAGxB,MAAM,CAAC0G,IAAI,CAACL,CAAC,IAAIA,CAAC,CAACzF,SAAS,KAAKJ,QAAQ,CAACI,SAAS,CAAC;cACnE,OAAOY,MAAM,gBACX7C,OAAA,CAAAE,SAAA;gBAAAmL,QAAA,gBACErL,OAAA,CAAC7D,GAAG;kBAACoP,EAAE,EAAE;oBAAES,OAAO,EAAE,MAAM;oBAAEe,cAAc,EAAE,eAAe;oBAAEvB,EAAE,EAAE;kBAAI,CAAE;kBAAAH,QAAA,gBACrErL,OAAA,CAAC3D,UAAU;oBAACiP,OAAO,EAAC,OAAO;oBAACC,EAAE,EAAE;sBAAEE,UAAU,EAAE,QAAQ;sBAAEoB,QAAQ,EAAE;oBAAS,CAAE;oBAAAxB,QAAA,EAAC;kBAAU;oBAAAK,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAY,CAAC,eACrG7L,OAAA,CAAC3D,UAAU;oBAACiP,OAAO,EAAC,OAAO;oBAACC,EAAE,EAAE;sBAAEsB,QAAQ,EAAE;oBAAS,CAAE;oBAAAxB,QAAA,EAAEnH,eAAe,CAACrB,MAAM,CAACZ,SAAS;kBAAC;oBAAAyJ,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAa,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACrG,CAAC,eACN7L,OAAA,CAAC7D,GAAG;kBAACoP,EAAE,EAAE;oBAAES,OAAO,EAAE,MAAM;oBAAEe,cAAc,EAAE,eAAe;oBAAEvB,EAAE,EAAE;kBAAI,CAAE;kBAAAH,QAAA,gBACrErL,OAAA,CAAC3D,UAAU;oBAACiP,OAAO,EAAC,OAAO;oBAACC,EAAE,EAAE;sBAAEE,UAAU,EAAE,QAAQ;sBAAEoB,QAAQ,EAAE;oBAAS,CAAE;oBAAAxB,QAAA,EAAC;kBAAc;oBAAAK,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAY,CAAC,eACzG7L,OAAA,CAAC3D,UAAU;oBAACiP,OAAO,EAAC,OAAO;oBAACC,EAAE,EAAE;sBAAEsB,QAAQ,EAAE;oBAAS,CAAE;oBAAAxB,QAAA,GAAExI,MAAM,CAAC0D,aAAa,IAAI,CAAC,EAAC,IAAE;kBAAA;oBAAAmF,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAY,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAC/F,CAAC,eACN7L,OAAA,CAAC7D,GAAG;kBAACoP,EAAE,EAAE;oBAAES,OAAO,EAAE,MAAM;oBAAEe,cAAc,EAAE;kBAAgB,CAAE;kBAAA1B,QAAA,gBAC5DrL,OAAA,CAAC3D,UAAU;oBAACiP,OAAO,EAAC,OAAO;oBAACC,EAAE,EAAE;sBAAEE,UAAU,EAAE,QAAQ;sBAAEoB,QAAQ,EAAE;oBAAS,CAAE;oBAAAxB,QAAA,EAAC;kBAAM;oBAAAK,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAY,CAAC,eACjG7L,OAAA,CAAC9C,IAAI;oBACHmP,KAAK,EAAExJ,MAAM,CAACyD,YAAY,IAAI,KAAM;oBACpC8F,IAAI,EAAC,OAAO;oBACZK,KAAK,EAAE7M,iBAAiB,CAACiD,MAAM,CAACyD,YAAY,CAAE;oBAC9CgF,OAAO,EAAC,UAAU;oBAClBC,EAAE,EAAE;sBAAEuB,MAAM,EAAE,MAAM;sBAAE,kBAAkB,EAAE;wBAAEc,EAAE,EAAE,CAAC;wBAAEV,EAAE,EAAE,CAAC;wBAAEL,QAAQ,EAAE;sBAAS;oBAAE;kBAAE;oBAAAnB,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAClF,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACC,CAAC;cAAA,eACN,CAAC,gBAEH7L,OAAA,CAAC3D,UAAU;gBAACiP,OAAO,EAAC,OAAO;gBAACC,EAAE,EAAE;kBAAEsB,QAAQ,EAAE;gBAAS,CAAE;gBAAAxB,QAAA,EAAC;cAAkB;gBAAAK,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAY,CACvF;YACH,CAAC,EAAE,CAAC,gBACF7L,OAAA,CAAC3D,UAAU;cAACiP,OAAO,EAAC,OAAO;cAACC,EAAE,EAAE;gBAAEsB,QAAQ,EAAE;cAAS,CAAE;cAAAxB,QAAA,EACpDxJ,QAAQ,CAACI,SAAS,KAAK,cAAc,GACpC,kDAAkD,GAClD;YAA4B;cAAAyJ,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACpB,CACb;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACE,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,eAEN7L,OAAA,CAAC7D,GAAG;UAACoP,EAAE,EAAE;YAAEgD,EAAE,EAAE,CAAC;YAAE/C,EAAE,EAAE,CAAC;YAAEO,KAAK,EAAE;UAAO,CAAE;UAAAV,QAAA,gBACvCrL,OAAA,CAAC3D,UAAU;YAACiP,OAAO,EAAC,WAAW;YAAC6C,YAAY;YAAC5C,EAAE,EAAE;cAAEE,UAAU,EAAE;YAAO,CAAE;YAAAJ,QAAA,EAAC;UAEzE;YAAAK,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAY,CAAC,eACb7L,OAAA,CAAC1D,SAAS;YACR8P,IAAI,EAAC,OAAO;YACZC,KAAK,EAAC,cAAc;YACpBf,OAAO,EAAC,UAAU;YAClBvC,IAAI,EAAC,cAAc;YACnByF,IAAI,EAAC,QAAQ;YACbzK,KAAK,EAAElC,QAAQ,CAACG,YAAa;YAC7BsK,QAAQ,EAAEzD,gBAAiB;YAC3BjE,KAAK,EAAE,CAAC,CAAC1C,UAAU,CAACF,YAAa;YACjCyM,UAAU,EAAEvM,UAAU,CAACF,YAAY,IAAII,YAAY,CAACJ,YAAa;YACjE0M,mBAAmB,EAAE;cACnBnD,EAAE,EAAE;gBAAEkB,KAAK,EAAErK,YAAY,CAACJ,YAAY,IAAI,CAACE,UAAU,CAACF,YAAY,GAAG,cAAc,GAAG;cAAa;YACrG,CAAE;YACFuJ,EAAE,EAAE;cAAEC,EAAE,EAAE,CAAC;cAAEO,KAAK,EAAE;YAAQ,CAAE;YAC9B4C,UAAU,EAAE;cACVC,GAAG,EAAE,MAAM;cAAE;cACbC,IAAI,EAAE;YACR;UAAE;YAAAnD,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACC,CAAC,EAELzJ,YAAY,CAACJ,YAAY,IAAI,CAACE,UAAU,CAACF,YAAY,IAAI,CAACwH,iBAAiB,iBAC1ExJ,OAAA,CAAClD,KAAK;UAACmQ,QAAQ,EAAC,SAAS;UAAC1B,EAAE,EAAE;YAAEC,EAAE,EAAE;UAAE,CAAE;UAAAH,QAAA,EACrCjJ,YAAY,CAACJ;QAAY;UAAA0J,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACrB,CACR,eAED7L,OAAA,CAAClD,KAAK;UAACmQ,QAAQ,EAAC,MAAM;UAAC1B,EAAE,EAAE;YAAEgD,EAAE,EAAE;UAAE,CAAE;UAAAlD,QAAA,eACnCrL,OAAA,CAAC3D,UAAU;YAACiP,OAAO,EAAC,OAAO;YAAAD,QAAA,EAAC;UAE5B;YAAAK,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAY;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACR,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACL,CAAC;EAEV,CAAC;;EAED;EACA,MAAMiD,WAAW,GAAGA,CAAA,KAAM;IACxB;IACA,MAAMC,eAAe,GAAG,CAAC,GAAG,IAAIC,GAAG,CAAC3N,MAAM,CAACoM,GAAG,CAAC5K,MAAM,IAAIA,MAAM,CAAC2D,SAAS,IAAI,KAAK,CAAC,CAAC,CAAC;;IAErF;IACA,MAAMyI,iBAAiB,GAAIC,YAAY,IAAK;MAC1C,OAAO,IAAI9O,UAAU,KAAK8O,YAAY,EAAE;IAC1C,CAAC;;IAED;IACA,MAAMC,mBAAmB,GAAItM,MAAM,IAAK;MACtC,IAAI,CAACA,MAAM,IAAI,CAAChB,QAAQ,CAACG,YAAY,EAAE,OAAO,IAAI;MAClD,OAAOqH,UAAU,CAACxG,MAAM,CAAC0D,aAAa,CAAC,IAAI8C,UAAU,CAACxH,QAAQ,CAACG,YAAY,CAAC;IAC9E,CAAC;;IAED;IACA,MAAMoN,cAAc,GAAG/N,MAAM,CAACgF,MAAM,CAACxD,MAAM,IAAI;MAC7C;MACA,MAAMwM,gBAAgB,GAAG,CAACrM,eAAe,IACvC2D,MAAM,CAAC9D,MAAM,CAAC2D,SAAS,IAAI,EAAE,CAAC,CAACI,IAAI,CAAC,CAAC,KAAK5D,eAAe;;MAE3D;MACA,MAAMsM,WAAW,GAAGpM,UAAU,CAAC2D,WAAW,CAAC,CAAC;MAC5C,MAAM0I,aAAa,GAAG,CAACrM,UAAU,IAC/BgB,eAAe,CAACrB,MAAM,CAACZ,SAAS,CAAC,CAAC4E,WAAW,CAAC,CAAC,CAAC1C,QAAQ,CAACmL,WAAW,CAAC,IACrE3I,MAAM,CAAC9D,MAAM,CAAC2D,SAAS,IAAI,EAAE,CAAC,CAACK,WAAW,CAAC,CAAC,CAAC1C,QAAQ,CAACmL,WAAW,CAAC,IAClE3I,MAAM,CAAC9D,MAAM,CAAC4D,OAAO,IAAI,EAAE,CAAC,CAACI,WAAW,CAAC,CAAC,CAAC1C,QAAQ,CAACmL,WAAW,CAAC;MAElE,OAAOD,gBAAgB,IAAIE,aAAa;IAC1C,CAAC,CAAC;;IAEF;IACA,MAAMxI,iBAAiB,GAAGxF,YAAY,GAClC6N,cAAc,CAAC/I,MAAM,CAACxD,MAAM,IAC1B8D,MAAM,CAAC9D,MAAM,CAAC2D,SAAS,IAAI,EAAE,CAAC,CAACI,IAAI,CAAC,CAAC,KAAKD,MAAM,CAACpF,YAAY,CAACiF,SAAS,IAAI,EAAE,CAAC,CAACI,IAAI,CAAC,CAAC,IACrFD,MAAM,CAAC9D,MAAM,CAAC4D,OAAO,IAAI,GAAG,CAAC,CAACG,IAAI,CAAC,CAAC,KAAKD,MAAM,CAACpF,YAAY,CAACkF,OAAO,IAAI,GAAG,CAAC,CAACG,IAAI,CAAC,CAAC,CAAC,GACtFwI,cAAc;IAElB,MAAMpI,oBAAoB,GAAGzF,YAAY,GACrC6N,cAAc,CAAC/I,MAAM,CAACxD,MAAM,IAC1B8D,MAAM,CAAC9D,MAAM,CAAC2D,SAAS,IAAI,EAAE,CAAC,CAACI,IAAI,CAAC,CAAC,KAAKD,MAAM,CAACpF,YAAY,CAACiF,SAAS,IAAI,EAAE,CAAC,CAACI,IAAI,CAAC,CAAC,IACrFD,MAAM,CAAC9D,MAAM,CAAC4D,OAAO,IAAI,GAAG,CAAC,CAACG,IAAI,CAAC,CAAC,KAAKD,MAAM,CAACpF,YAAY,CAACkF,OAAO,IAAI,GAAG,CAAC,CAACG,IAAI,CAAC,CAAC,CAAC,GACtF,EAAE;;IAEN;IACA,MAAM4I,uBAAuB,GAAI1G,CAAC,IAAK;MACrC,MAAMoG,YAAY,GAAGpG,CAAC,CAAChF,MAAM,CAACC,KAAK,CAAC6C,IAAI,CAAC,CAAC;;MAE1C;MACA,IAAIsI,YAAY,CAACrI,WAAW,CAAC,CAAC,KAAK,GAAG,EAAE;QACtC/E,WAAW,CAAC;UACV,GAAGD,QAAQ;UACXI,SAAS,EAAE;QACb,CAAC,CAAC;QACFE,aAAa,CAAC;UACZ,GAAGD,UAAU;UACbyB,eAAe,EAAE;QACnB,CAAC,CAAC;QACF;MACF;MAEA,IAAIuL,YAAY,EAAE;QAChB;QACA,MAAMO,gBAAgB,GAAGR,iBAAiB,CAACC,YAAY,CAAC;;QAExD;QACA,MAAMQ,eAAe,GAAGrO,MAAM,CAAC0G,IAAI,CAACL,CAAC,IAAIA,CAAC,CAACzF,SAAS,KAAKwN,gBAAgB,CAAC;QAE1E,IAAIC,eAAe,EAAE;UACnB;UACA,IAAIA,eAAe,CAACpJ,YAAY,KAAK,MAAM,IAAIoJ,eAAe,CAACpJ,YAAY,KAAK,WAAW,EAAE;YAC3FnE,aAAa,CAAC;cACZ,GAAGD,UAAU;cACbyB,eAAe,EAAE,aAAauL,YAAY,eAAeQ,eAAe,CAACpJ,YAAY;YACvF,CAAC,CAAC;YACF;UACF;;UAEA;UACA,IAAI/E,YAAY,EAAE;YAChB;YACA,MAAMmF,aAAa,GAAGC,MAAM,CAACpF,YAAY,CAACiF,SAAS,IAAI,EAAE,CAAC;YAC1D,MAAMM,WAAW,GAAGH,MAAM,CAACpF,YAAY,CAACkF,OAAO,IAAI,GAAG,CAAC;YAEvD,MAAMS,eAAe,GAAGP,MAAM,CAAC+I,eAAe,CAAClJ,SAAS,IAAI,EAAE,CAAC;YAC/D,MAAMW,aAAa,GAAGR,MAAM,CAAC+I,eAAe,CAACjJ,OAAO,IAAI,GAAG,CAAC;;YAE5D;YACAnC,OAAO,CAACC,GAAG,CAAC,iCAAiCmL,eAAe,CAACzN,SAAS,GAAG,EAAE;cACzEuE,SAAS,EAAE,GAAGU,eAAe,QAAQR,aAAa,EAAE;cACpDiJ,UAAU,EAAE,GAAGxI,aAAa,QAAQL,WAAW;YACjD,CAAC,CAAC;YAEF,IAAII,eAAe,KAAKR,aAAa,IACjCS,aAAa,KAAKL,WAAW,EAAE;cACjC;cACAnE,uBAAuB,CAAC;gBACtBC,IAAI,EAAErB,YAAY;gBAClBsB,MAAM,EAAE6M;cACV,CAAC,CAAC;cACFnN,6BAA6B,CAAC,IAAI,CAAC;cACnC;YACF;UACF;;UAEA;UACA,IAAI4M,mBAAmB,CAACO,eAAe,CAAC,EAAE;YACxC;YACA5N,WAAW,CAAC;cACV,GAAGD,QAAQ;cACXI,SAAS,EAAEwN;YACb,CAAC,CAAC;YACFtN,aAAa,CAAC;cACZ,GAAGD,UAAU;cACbyB,eAAe,EAAE;YACnB,CAAC,CAAC;UACJ,CAAC,MAAM;YACL;YACAxB,aAAa,CAAC;cACZ,GAAGD,UAAU;cACbyB,eAAe,EAAE,aAAauL,YAAY,sCAAsCQ,eAAe,CAACnJ,aAAa;YAC/G,CAAC,CAAC;UACJ;QACF,CAAC,MAAM;UACL;UACApE,aAAa,CAAC;YACZ,GAAGD,UAAU;YACbyB,eAAe,EAAE,UAAUuL,YAAY;UACzC,CAAC,CAAC;QACJ;MACF,CAAC,MAAM;QACL;QACApN,WAAW,CAAC;UACV,GAAGD,QAAQ;UACXI,SAAS,EAAE;QACb,CAAC,CAAC;QACFE,aAAa,CAAC;UACZ,GAAGD,UAAU;UACbyB,eAAe,EAAE;QACnB,CAAC,CAAC;MACJ;IACF,CAAC;IAED,oBACE3D,OAAA,CAAC7D,GAAG;MAAAkP,QAAA,gBACFrL,OAAA,CAAC3D,UAAU;QAACiP,OAAO,EAAC,WAAW;QAACC,EAAE,EAAE;UAAEC,EAAE,EAAE,CAAC;UAAEC,UAAU,EAAE;QAAO,CAAE;QAAAJ,QAAA,EAAC;MAEnE;QAAAK,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAY,CAAC,eAEb7L,OAAA,CAAC5D,KAAK;QAACmP,EAAE,EAAE;UAAEO,CAAC,EAAE,CAAC;UAAEC,KAAK,EAAE;QAAO,CAAE;QAAAV,QAAA,gBACjCrL,OAAA,CAAC7D,GAAG;UAACoP,EAAE,EAAE;YAAEC,EAAE,EAAE;UAAE,CAAE;UAAAH,QAAA,gBACjBrL,OAAA,CAAC3D,UAAU;YAACiP,OAAO,EAAC,OAAO;YAACC,EAAE,EAAE;cAAEC,EAAE,EAAE;YAAE,CAAE;YAAAH,QAAA,EAAC;UAE3C;YAAAK,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAY,CAAC,eAEb7L,OAAA,CAAC7D,GAAG;YAACoP,EAAE,EAAE;cAAES,OAAO,EAAE,MAAM;cAAEe,cAAc,EAAE,eAAe;cAAEd,UAAU,EAAE,QAAQ;cAAEgC,GAAG,EAAE;YAAE,CAAE;YAAA5C,QAAA,gBAE1FrL,OAAA,CAAC1D,SAAS;cACR8P,IAAI,EAAC,OAAO;cACZC,KAAK,EAAC,cAAc;cACpBf,OAAO,EAAC,UAAU;cAClBvH,KAAK,EAAEb,UAAW;cAClBoJ,QAAQ,EAAEtI,sBAAuB;cACjCuI,WAAW,EAAC,4BAA4B;cACxCqD,UAAU,EAAE;gBACVC,cAAc,eACZ7P,OAAA,CAAC7C,cAAc;kBAAC2S,QAAQ,EAAC,OAAO;kBAAAzE,QAAA,eAC9BrL,OAAA,CAAC5B,UAAU;oBAACyO,QAAQ,EAAC;kBAAO;oBAAAnB,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACjB,CACjB;gBACDkE,YAAY,EAAE7M,UAAU,gBACtBlD,OAAA,CAAC7C,cAAc;kBAAC2S,QAAQ,EAAC,KAAK;kBAAAzE,QAAA,eAC5BrL,OAAA,CAAC/C,UAAU;oBACTmP,IAAI,EAAC,OAAO;oBACZ,cAAW,cAAc;oBACzBM,OAAO,EAAEA,CAAA,KAAMvJ,aAAa,CAAC,EAAE,CAAE;oBACjC6M,IAAI,EAAC,KAAK;oBAAA3E,QAAA,eAEVrL,OAAA,CAACxB,UAAU;sBAACqO,QAAQ,EAAC;oBAAO;sBAAAnB,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAE;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACrB;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACC,CAAC,GACf;cACN,CAAE;cACFN,EAAE,EAAE;gBAAE8C,IAAI,EAAE;cAAE;YAAE;cAAA3C,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACjB,CAAC,eAGF7L,OAAA,CAACvD,WAAW;cAAC2P,IAAI,EAAC,OAAO;cAACb,EAAE,EAAE;gBAAEY,QAAQ,EAAE;cAAI,CAAE;cAAAd,QAAA,gBAC9CrL,OAAA,CAACtD,UAAU;gBAACuT,EAAE,EAAC,wBAAwB;gBAAA5E,QAAA,EAAC;cAAoB;gBAAAK,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAY,CAAC,eACzE7L,OAAA,CAACrD,MAAM;gBACLuT,OAAO,EAAC,wBAAwB;gBAChCnM,KAAK,EAAEf,eAAgB;gBACvBqJ,KAAK,EAAC,sBAAsB;gBAC5BC,QAAQ,EAAE1I,2BAA4B;gBAAAyH,QAAA,gBAEtCrL,OAAA,CAACpD,QAAQ;kBAACmH,KAAK,EAAC,EAAE;kBAAAsH,QAAA,EAAC;gBAAkB;kBAAAK,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAU,CAAC,EAC/CkD,eAAe,CAACtB,GAAG,CAAEjH,SAAS,iBAC7BxG,OAAA,CAACpD,QAAQ;kBAAiBmH,KAAK,EAAEyC,SAAU;kBAAA6E,QAAA,EAAE7E;gBAAS,GAAvCA,SAAS;kBAAAkF,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAyC,CAClE,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACI,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACE,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACX,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,EAELhL,aAAa,gBACZb,OAAA,CAAC7D,GAAG;UAACoP,EAAE,EAAE;YAAES,OAAO,EAAE,MAAM;YAAEe,cAAc,EAAE,QAAQ;YAAEC,EAAE,EAAE;UAAE,CAAE;UAAA3B,QAAA,eAC5DrL,OAAA,CAACjD,gBAAgB;YAACqP,IAAI,EAAE;UAAG;YAAAV,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC3B,CAAC,gBAEN7L,OAAA,CAAC7D,GAAG;UAAAkP,QAAA,gBAEFrL,OAAA,CAAC7D,GAAG;YAACoP,EAAE,EAAE;cAAEC,EAAE,EAAE;YAAE,CAAE;YAAAH,QAAA,gBACjBrL,OAAA,CAAC3D,UAAU;cAACiP,OAAO,EAAC,WAAW;cAACC,EAAE,EAAE;gBAAEE,UAAU,EAAE,MAAM;gBAAEO,OAAO,EAAE,OAAO;gBAAER,EAAE,EAAE;cAAE,CAAE;cAAAH,QAAA,EAAC;YAErF;cAAAK,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAY,CAAC,eACb7L,OAAA,CAAC7D,GAAG;cAACoP,EAAE,EAAE;gBAAES,OAAO,EAAE,MAAM;gBAAEC,UAAU,EAAE,YAAY;gBAAEgC,GAAG,EAAE,CAAC;gBAAEK,QAAQ,EAAE;cAAQ,CAAE;cAAAjD,QAAA,gBAChFrL,OAAA,CAAC1D,SAAS;gBACR8P,IAAI,EAAC,OAAO;gBACZC,KAAK,EAAC,eAAe;gBACrBf,OAAO,EAAC,UAAU;gBAClBiB,WAAW,EAAC,QAAQ;gBACpB3H,KAAK,EAAE,CAAC,CAAC1C,UAAU,CAACyB,eAAgB;gBACpCwM,MAAM,EAAEX,uBAAwB;gBAChCjE,EAAE,EAAE;kBAAE8C,IAAI,EAAE;gBAAE;cAAE;gBAAA3C,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACjB,CAAC,eACF7L,OAAA,CAACzD,MAAM;gBACL+O,OAAO,EAAC,UAAU;gBAClBc,IAAI,EAAC,OAAO;gBACZM,OAAO,EAAEA,CAAA,KAAMlJ,kBAAkB,CAAC,cAAc,CAAE;gBAClD+H,EAAE,EAAE;kBAAEuB,MAAM,EAAE;gBAAO,CAAE;gBAAAzB,QAAA,EACxB;cAED;gBAAAK,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACN,CAAC,EACL3J,UAAU,CAACyB,eAAe,iBACzB3D,OAAA,CAAC3D,UAAU;cAACiP,OAAO,EAAC,SAAS;cAACmB,KAAK,EAAC,OAAO;cAAClB,EAAE,EAAE;gBAAES,OAAO,EAAE,OAAO;gBAAEuC,EAAE,EAAE;cAAI,CAAE;cAAAlD,QAAA,EAC3EnJ,UAAU,CAACyB;YAAe;cAAA+H,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACjB,CACb,eACD7L,OAAA,CAAC3D,UAAU;cAACiP,OAAO,EAAC,SAAS;cAACC,EAAE,EAAE;gBAAES,OAAO,EAAE,OAAO;gBAAEuC,EAAE,EAAE;cAAI,CAAE;cAAAlD,QAAA,GAAC,aACpD,eAAArL,OAAA;gBAAAqL,QAAA,EAASxJ,QAAQ,CAACI,SAAS,IAAI;cAAG;gBAAAyJ,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAS,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC7C,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACV,CAAC,eAGN7L,OAAA,CAACxD,IAAI;YAAC4T,SAAS;YAACC,OAAO,EAAE,CAAE;YAAAhF,QAAA,gBAEzBrL,OAAA,CAACxD,IAAI;cAAC8T,IAAI;cAACC,EAAE,EAAE,EAAG;cAACC,EAAE,EAAE,CAAE;cAAAnF,QAAA,eACvBrL,OAAA,CAAC5D,KAAK;gBAACkP,OAAO,EAAC,UAAU;gBAACC,EAAE,EAAE;kBAAEO,CAAC,EAAE,CAAC;kBAAEgB,MAAM,EAAE;gBAAO,CAAE;gBAAAzB,QAAA,gBACrDrL,OAAA,CAAC3D,UAAU;kBAACiP,OAAO,EAAC,WAAW;kBAACC,EAAE,EAAE;oBAAEE,UAAU,EAAE,MAAM;oBAAED,EAAE,EAAE;kBAAE,CAAE;kBAAAH,QAAA,EAAC;gBAEnE;kBAAAK,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAY,CAAC,EAEZ9E,iBAAiB,CAACrC,MAAM,GAAG,CAAC,gBAC3B1E,OAAA,CAAClC,IAAI;kBAACyN,EAAE,EAAE;oBAAE6B,SAAS,EAAE,OAAO;oBAAEC,QAAQ,EAAE,MAAM;oBAAEE,OAAO,EAAE;kBAAmB,CAAE;kBAAAlC,QAAA,EAC7EtE,iBAAiB,CAAC0G,GAAG,CAAE5K,MAAM,iBAC5B7C,OAAA,CAACjC,QAAQ;oBAEP0S,cAAc;oBACdC,eAAe,eACb1Q,OAAA,CAAC/C,UAAU;sBACT+S,IAAI,EAAC,KAAK;sBACV5D,IAAI,EAAC,OAAO;sBACZM,OAAO,EAAEA,CAAA,KAAMlJ,kBAAkB,CAACX,MAAM,CAACZ,SAAS,CAAE;sBACpD0K,QAAQ,EAAE9J,MAAM,CAAC0D,aAAa,GAAG8C,UAAU,CAACxH,QAAQ,CAACG,YAAY,IAAI,CAAC,CAAE;sBAAAqJ,QAAA,eAExErL,OAAA,CAAClB,oBAAoB;wBAAC2N,KAAK,EAAE5J,MAAM,CAAC0D,aAAa,GAAG8C,UAAU,CAACxH,QAAQ,CAACG,YAAY,IAAI,CAAC,CAAC,GAAG,UAAU,GAAG;sBAAU;wBAAA0J,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAE;oBAAC;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAC7G,CACb;oBACDN,EAAE,EAAE;sBACFgC,OAAO,EAAE1L,QAAQ,CAACI,SAAS,KAAKY,MAAM,CAACZ,SAAS,GAAG,yBAAyB,GAAG,SAAS;sBACxFmM,YAAY,EAAE,KAAK;sBACnB5C,EAAE,EAAE,GAAG;sBACPmF,MAAM,EAAE9O,QAAQ,CAACI,SAAS,KAAKY,MAAM,CAACZ,SAAS,GAAG,mBAAmB,GAAG;oBAC1E,CAAE;oBAAAoJ,QAAA,eAEFrL,OAAA,CAAChC,cAAc;sBACb4S,KAAK;sBACLlE,OAAO,EAAEA,CAAA,KAAMlJ,kBAAkB,CAACX,MAAM,CAACZ,SAAS,CAAE;sBACpD0K,QAAQ,EAAE9J,MAAM,CAAC0D,aAAa,GAAG8C,UAAU,CAACxH,QAAQ,CAACG,YAAY,IAAI,CAAC,CAAE;sBAAAqJ,QAAA,eAExErL,OAAA,CAAC/B,YAAY;wBACX4S,OAAO,eACL7Q,OAAA,CAAC7D,GAAG;0BAACoP,EAAE,EAAE;4BAAES,OAAO,EAAE,MAAM;4BAAEe,cAAc,EAAE,eAAe;4BAAEd,UAAU,EAAE;0BAAS,CAAE;0BAAAZ,QAAA,gBAClFrL,OAAA,CAAC3D,UAAU;4BAACiP,OAAO,EAAC,OAAO;4BAACC,EAAE,EAAE;8BAAEE,UAAU,EAAE;4BAAO,CAAE;4BAAAJ,QAAA,EACpDnH,eAAe,CAACrB,MAAM,CAACZ,SAAS;0BAAC;4BAAAyJ,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OACxB,CAAC,eACb7L,OAAA,CAAC3D,UAAU;4BAACiP,OAAO,EAAC,SAAS;4BAACC,EAAE,EAAE;8BAAEE,UAAU,EAAE,MAAM;8BAAEgB,KAAK,EAAE5J,MAAM,CAAC0D,aAAa,GAAG8C,UAAU,CAACxH,QAAQ,CAACG,YAAY,IAAI,CAAC,CAAC,GAAG,YAAY,GAAG;4BAAe,CAAE;4BAAAqJ,QAAA,GAC5JxI,MAAM,CAAC0D,aAAa,IAAI,CAAC,EAAC,IAC7B;0BAAA;4BAAAmF,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OAAY,CAAC;wBAAA;0BAAAH,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OACV,CACN;wBACDiF,SAAS,eACP9Q,OAAA,CAAAE,SAAA;0BAAAmL,QAAA,gBACErL,OAAA,CAAC3D,UAAU;4BAACiP,OAAO,EAAC,SAAS;4BAAC6B,SAAS,EAAC,MAAM;4BAACnB,OAAO,EAAC,OAAO;4BAAAX,QAAA,GAC3DxI,MAAM,CAAC2D,SAAS,IAAI,KAAK,EAAC,KAAG,EAAC3D,MAAM,CAAC4D,OAAO,IAAI,KAAK;0BAAA;4BAAAiF,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OAC5C,CAAC,eACb7L,OAAA,CAAC9C,IAAI;4BACHkP,IAAI,EAAC,OAAO;4BACZC,KAAK,EAAExJ,MAAM,CAACyD,YAAY,IAAI,KAAM;4BACpCmG,KAAK,EAAE7M,iBAAiB,CAACiD,MAAM,CAACyD,YAAY,CAAE;4BAC9CgF,OAAO,EAAC,UAAU;4BAClBC,EAAE,EAAE;8BAAEuB,MAAM,EAAE,EAAE;8BAAED,QAAQ,EAAE,QAAQ;8BAAE0B,EAAE,EAAE,GAAG;8BAAE,kBAAkB,EAAE;gCAAEX,EAAE,EAAE,GAAG;gCAAEV,EAAE,EAAE;8BAAE;4BAAE;0BAAE;4BAAAxB,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OACzF,CAAC;wBAAA,eACF;sBACH;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACF;oBAAC;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACY;kBAAC,GAlDZhJ,MAAM,CAACZ,SAAS;oBAAAyJ,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAmDb,CACX;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACE,CAAC,gBAEP7L,OAAA,CAAClD,KAAK;kBAACmQ,QAAQ,EAAC,MAAM;kBAAC1B,EAAE,EAAE;oBAAEgD,EAAE,EAAE;kBAAE,CAAE;kBAAAlD,QAAA,EAAC;gBAEtC;kBAAAK,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAO,CACR;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACI;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACJ,CAAC,eAGP7L,OAAA,CAACxD,IAAI;cAAC8T,IAAI;cAACC,EAAE,EAAE,EAAG;cAACC,EAAE,EAAE,CAAE;cAAAnF,QAAA,eACvBrL,OAAA,CAAC5D,KAAK;gBAACkP,OAAO,EAAC,UAAU;gBAACC,EAAE,EAAE;kBAAEO,CAAC,EAAE,CAAC;kBAAEgB,MAAM,EAAE;gBAAO,CAAE;gBAAAzB,QAAA,gBACrDrL,OAAA,CAAC3D,UAAU;kBAACiP,OAAO,EAAC,WAAW;kBAACC,EAAE,EAAE;oBAAEE,UAAU,EAAE,MAAM;oBAAED,EAAE,EAAE;kBAAE,CAAE;kBAAAH,QAAA,EAAC;gBAEnE;kBAAAK,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAY,CAAC,EAEZ7E,oBAAoB,CAACtC,MAAM,GAAG,CAAC,gBAC9B1E,OAAA,CAAClC,IAAI;kBAACyN,EAAE,EAAE;oBAAE6B,SAAS,EAAE,OAAO;oBAAEC,QAAQ,EAAE,MAAM;oBAAEE,OAAO,EAAE;kBAAmB,CAAE;kBAAAlC,QAAA,EAC7ErE,oBAAoB,CAACyG,GAAG,CAAE5K,MAAM,iBAC/B7C,OAAA,CAACjC,QAAQ;oBAEP0S,cAAc;oBACdC,eAAe,eACb1Q,OAAA,CAAC/C,UAAU;sBACT+S,IAAI,EAAC,KAAK;sBACV5D,IAAI,EAAC,OAAO;sBACZM,OAAO,EAAEA,CAAA,KAAMlJ,kBAAkB,CAACX,MAAM,CAACZ,SAAS,CAAE;sBACpD0K,QAAQ,EAAE9J,MAAM,CAAC0D,aAAa,GAAG8C,UAAU,CAACxH,QAAQ,CAACG,YAAY,IAAI,CAAC,CAAE;sBAAAqJ,QAAA,eAExErL,OAAA,CAAClB,oBAAoB;wBAAC2N,KAAK,EAAE5J,MAAM,CAAC0D,aAAa,GAAG8C,UAAU,CAACxH,QAAQ,CAACG,YAAY,IAAI,CAAC,CAAC,GAAG,UAAU,GAAG;sBAAU;wBAAA0J,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAE;oBAAC;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAC7G,CACb;oBACDN,EAAE,EAAE;sBACFgC,OAAO,EAAE1L,QAAQ,CAACI,SAAS,KAAKY,MAAM,CAACZ,SAAS,GAAG,yBAAyB,GAAG,SAAS;sBACxFmM,YAAY,EAAE,KAAK;sBACnB5C,EAAE,EAAE,GAAG;sBACPmF,MAAM,EAAE9O,QAAQ,CAACI,SAAS,KAAKY,MAAM,CAACZ,SAAS,GAAG,mBAAmB,GAAG;oBAC1E,CAAE;oBAAAoJ,QAAA,eAEFrL,OAAA,CAAChC,cAAc;sBACb4S,KAAK;sBACLlE,OAAO,EAAEA,CAAA,KAAMlJ,kBAAkB,CAACX,MAAM,CAACZ,SAAS,CAAE;sBACpD0K,QAAQ,EAAE9J,MAAM,CAAC0D,aAAa,GAAG8C,UAAU,CAACxH,QAAQ,CAACG,YAAY,IAAI,CAAC,CAAE;sBAAAqJ,QAAA,eAExErL,OAAA,CAAC/B,YAAY;wBACX4S,OAAO,eACL7Q,OAAA,CAAC7D,GAAG;0BAACoP,EAAE,EAAE;4BAAES,OAAO,EAAE,MAAM;4BAAEe,cAAc,EAAE,eAAe;4BAAEd,UAAU,EAAE;0BAAS,CAAE;0BAAAZ,QAAA,gBAClFrL,OAAA,CAAC3D,UAAU;4BAACiP,OAAO,EAAC,OAAO;4BAACC,EAAE,EAAE;8BAAEE,UAAU,EAAE;4BAAO,CAAE;4BAAAJ,QAAA,EACpDnH,eAAe,CAACrB,MAAM,CAACZ,SAAS;0BAAC;4BAAAyJ,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OACxB,CAAC,eACb7L,OAAA,CAAC3D,UAAU;4BAACiP,OAAO,EAAC,SAAS;4BAACC,EAAE,EAAE;8BAAEE,UAAU,EAAE,MAAM;8BAAEgB,KAAK,EAAE5J,MAAM,CAAC0D,aAAa,GAAG8C,UAAU,CAACxH,QAAQ,CAACG,YAAY,IAAI,CAAC,CAAC,GAAG,YAAY,GAAG;4BAAe,CAAE;4BAAAqJ,QAAA,GAC5JxI,MAAM,CAAC0D,aAAa,IAAI,CAAC,EAAC,IAC7B;0BAAA;4BAAAmF,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OAAY,CAAC;wBAAA;0BAAAH,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OACV,CACN;wBACDiF,SAAS,eACP9Q,OAAA,CAAAE,SAAA;0BAAAmL,QAAA,gBACErL,OAAA,CAAC3D,UAAU;4BAACiP,OAAO,EAAC,SAAS;4BAAC6B,SAAS,EAAC,MAAM;4BAACnB,OAAO,EAAC,OAAO;4BAAAX,QAAA,GAC3DxI,MAAM,CAAC2D,SAAS,IAAI,KAAK,EAAC,KAAG,EAAC3D,MAAM,CAAC4D,OAAO,IAAI,KAAK;0BAAA;4BAAAiF,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OAC5C,CAAC,eACb7L,OAAA,CAAC7D,GAAG;4BAACoP,EAAE,EAAE;8BAAES,OAAO,EAAE,MAAM;8BAAEe,cAAc,EAAE,eAAe;8BAAEd,UAAU,EAAE,QAAQ;8BAAEsC,EAAE,EAAE;4BAAI,CAAE;4BAAAlD,QAAA,gBAC3FrL,OAAA,CAAC9C,IAAI;8BACHkP,IAAI,EAAC,OAAO;8BACZC,KAAK,EAAExJ,MAAM,CAACyD,YAAY,IAAI,KAAM;8BACpCmG,KAAK,EAAE7M,iBAAiB,CAACiD,MAAM,CAACyD,YAAY,CAAE;8BAC9CgF,OAAO,EAAC,UAAU;8BAClBC,EAAE,EAAE;gCAAEuB,MAAM,EAAE,EAAE;gCAAED,QAAQ,EAAE,QAAQ;gCAAE,kBAAkB,EAAE;kCAAEe,EAAE,EAAE,GAAG;kCAAEV,EAAE,EAAE;gCAAE;8BAAE;4BAAE;8BAAAxB,QAAA,EAAAC,YAAA;8BAAAC,UAAA;8BAAAC,YAAA;4BAAA,OAChF,CAAC,eACF7L,OAAA,CAAC9C,IAAI;8BACHkP,IAAI,EAAC,OAAO;8BACZC,KAAK,EAAC,iBAAiB;8BACvBI,KAAK,EAAC,SAAS;8BACfnB,OAAO,EAAC,UAAU;8BAClBC,EAAE,EAAE;gCAAEuB,MAAM,EAAE,EAAE;gCAAED,QAAQ,EAAE,QAAQ;gCAAE,kBAAkB,EAAE;kCAAEe,EAAE,EAAE,GAAG;kCAAEV,EAAE,EAAE;gCAAE;8BAAE;4BAAE;8BAAAxB,QAAA,EAAAC,YAAA;8BAAAC,UAAA;8BAAAC,YAAA;4BAAA,OAChF,CAAC;0BAAA;4BAAAH,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OACC,CAAC;wBAAA,eACN;sBACH;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACF;oBAAC;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACY;kBAAC,GA3DZhJ,MAAM,CAACZ,SAAS;oBAAAyJ,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OA4Db,CACX;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACE,CAAC,gBAEP7L,OAAA,CAAClD,KAAK;kBAACmQ,QAAQ,EAAC,MAAM;kBAAC1B,EAAE,EAAE;oBAAEgD,EAAE,EAAE;kBAAE,CAAE;kBAAAlD,QAAA,EAAC;gBAEtC;kBAAAK,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAO,CACR;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACI;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACJ,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACJ,CACN,EAGA,CAAChL,aAAa,IAAIgB,QAAQ,CAACI,SAAS,iBACnCjC,OAAA,CAAC7D,GAAG;UAACoP,EAAE,EAAE;YAAEgD,EAAE,EAAE,CAAC;YAAEzC,CAAC,EAAE,CAAC;YAAEyB,OAAO,EAAE,kBAAkB;YAAEa,YAAY,EAAE,CAAC;YAAEuC,MAAM,EAAE;UAAoB,CAAE;UAAAtF,QAAA,gBAClGrL,OAAA,CAAC3D,UAAU;YAACiP,OAAO,EAAC,WAAW;YAAC6C,YAAY;YAAA9C,QAAA,EAAC;UAE7C;YAAAK,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAY,CAAC,EACZ,CAAC,MAAM;YACN,MAAMhJ,MAAM,GAAGxB,MAAM,CAAC0G,IAAI,CAACL,CAAC,IAAIA,CAAC,CAACzF,SAAS,KAAKJ,QAAQ,CAACI,SAAS,CAAC;YACnE,IAAIY,MAAM,EAAE;cACV,oBACE7C,OAAA,CAACxD,IAAI;gBAAC4T,SAAS;gBAACC,OAAO,EAAE,CAAE;gBAAAhF,QAAA,gBACzBrL,OAAA,CAACxD,IAAI;kBAAC8T,IAAI;kBAACC,EAAE,EAAE,EAAG;kBAACC,EAAE,EAAE,CAAE;kBAAAnF,QAAA,gBACvBrL,OAAA,CAAC3D,UAAU;oBAACiP,OAAO,EAAC,OAAO;oBAAAD,QAAA,gBACzBrL,OAAA;sBAAAqL,QAAA,EAAQ;oBAAO;sBAAAK,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAQ,CAAC,KAAC,EAAC3H,eAAe,CAACrB,MAAM,CAACZ,SAAS,CAAC;kBAAA;oBAAAyJ,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACjD,CAAC,eACb7L,OAAA,CAAC3D,UAAU;oBAACiP,OAAO,EAAC,OAAO;oBAAAD,QAAA,gBACzBrL,OAAA;sBAAAqL,QAAA,EAAQ;oBAAU;sBAAAK,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAQ,CAAC,KAAC,EAAChJ,MAAM,CAAC2D,SAAS,IAAI,KAAK;kBAAA;oBAAAkF,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAC5C,CAAC,eACb7L,OAAA,CAAC3D,UAAU;oBAACiP,OAAO,EAAC,OAAO;oBAAAD,QAAA,gBACzBrL,OAAA;sBAAAqL,QAAA,EAAQ;oBAAW;sBAAAK,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAQ,CAAC,KAAC,EAAChJ,MAAM,CAAC8F,YAAY,IAAI,KAAK,EAAC,KAAG,EAAC9F,MAAM,CAAC4D,OAAO,IAAI,KAAK;kBAAA;oBAAAiF,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAC5E,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACT,CAAC,eACP7L,OAAA,CAACxD,IAAI;kBAAC8T,IAAI;kBAACC,EAAE,EAAE,EAAG;kBAACC,EAAE,EAAE,CAAE;kBAAAnF,QAAA,gBACvBrL,OAAA,CAAC3D,UAAU;oBAACiP,OAAO,EAAC,OAAO;oBAAAD,QAAA,gBACzBrL,OAAA;sBAAAqL,QAAA,EAAQ;oBAAa;sBAAAK,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAQ,CAAC,KAAC,EAAChJ,MAAM,CAACkO,YAAY,IAAI,CAAC,EAAC,IAC3D;kBAAA;oBAAArF,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAY,CAAC,eACb7L,OAAA,CAAC3D,UAAU;oBAACiP,OAAO,EAAC,OAAO;oBAAAD,QAAA,gBACzBrL,OAAA;sBAAAqL,QAAA,EAAQ;oBAAc;sBAAAK,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAQ,CAAC,KAAC,EAAChJ,MAAM,CAAC0D,aAAa,IAAI,CAAC,EAAC,IAC7D;kBAAA;oBAAAmF,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAY,CAAC,eACb7L,OAAA,CAAC3D,UAAU;oBAACiP,OAAO,EAAC,OAAO;oBAAAD,QAAA,gBACzBrL,OAAA;sBAAAqL,QAAA,EAAQ;oBAAM;sBAAAK,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAQ,CAAC,KAAC,EAAChJ,MAAM,CAACyD,YAAY,IAAI,KAAK;kBAAA;oBAAAoF,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAC3C,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACT,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CAAC;YAEX;YACA,oBACE7L,OAAA,CAAC3D,UAAU;cAACiP,OAAO,EAAC,OAAO;cAACmB,KAAK,EAAC,OAAO;cAAApB,QAAA,EAAC;YAE1C;cAAAK,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAY,CAAC;UAEjB,CAAC,EAAE,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACD,CACN,EAEAxK,MAAM,CAACqD,MAAM,KAAK,CAAC,IAAI,CAAC7D,aAAa,iBACpCb,OAAA,CAAClD,KAAK;UAACmQ,QAAQ,EAAC,SAAS;UAAC1B,EAAE,EAAE;YAAEgD,EAAE,EAAE;UAAE,CAAE;UAAAlD,QAAA,EAAC;QAEzC;UAAAK,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAO,CACR;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACI,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACL,CAAC;EAEV,CAAC;;EAED;EACA,MAAMmF,WAAW,GAAGA,CAAA,KAAM;IAExB;IACA,IAAI9B,YAAY,GAAG,SAAS;IAC5B,IAAI+B,UAAU,GAAG,IAAI;IAErB,IAAIpP,QAAQ,CAACI,SAAS,KAAK,cAAc,EAAE;MACzCiN,YAAY,GAAG,cAAc;IAC/B,CAAC,MAAM,IAAIrN,QAAQ,CAACI,SAAS,EAAE;MAC7BiN,YAAY,GAAGhL,eAAe,CAACrC,QAAQ,CAACI,SAAS,CAAC;MAClD;MACAgP,UAAU,GAAG5P,MAAM,CAAC0G,IAAI,CAACL,CAAC,IAAIA,CAAC,CAACzF,SAAS,KAAKJ,QAAQ,CAACI,SAAS,CAAC;IACnE;;IAEA;IACA,MAAM0I,kBAAkB,GAAGH,2BAA2B,CAACnB,UAAU,CAACxH,QAAQ,CAACG,YAAY,CAAC,EAAET,YAAY,CAACgI,aAAa,CAAC;IAErH,oBACEvJ,OAAA,CAAC7D,GAAG;MAAAkP,QAAA,gBACFrL,OAAA,CAAC3D,UAAU;QAACiP,OAAO,EAAC,IAAI;QAAC6C,YAAY;QAAA9C,QAAA,EAAC;MAEtC;QAAAK,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAY,CAAC,eAEb7L,OAAA,CAAC5D,KAAK;QAACmP,EAAE,EAAE;UAAEO,CAAC,EAAE;QAAE,CAAE;QAAAT,QAAA,gBAClBrL,OAAA,CAAC3D,UAAU;UAACiP,OAAO,EAAC,WAAW;UAAC6C,YAAY;UAAA9C,QAAA,EAAC;QAE7C;UAAAK,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAY,CAAC,eAGb7L,OAAA,CAACb,eAAe;UACdyD,IAAI,EAAErB,YAAa;UACnB2P,OAAO,EAAE,IAAK;UACdpH,KAAK,EAAC;QAAmB;UAAA4B,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC1B,CAAC,eAGF7L,OAAA,CAAC7D,GAAG;UAACoP,EAAE,EAAE;YAAEgD,EAAE,EAAE,CAAC;YAAEzC,CAAC,EAAE,CAAC;YAAEyB,OAAO,EAAE,SAAS;YAAEa,YAAY,EAAE;UAAE,CAAE;UAAA/C,QAAA,gBAC5DrL,OAAA,CAAC3D,UAAU;YAACiP,OAAO,EAAC,WAAW;YAAC6C,YAAY;YAAC5C,EAAE,EAAE;cAAEE,UAAU,EAAE;YAAO,CAAE;YAAAJ,QAAA,EAAC;UAEzE;YAAAK,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAY,CAAC,eACb7L,OAAA,CAACxD,IAAI;YAAC4T,SAAS;YAACC,OAAO,EAAE,CAAE;YAAAhF,QAAA,gBACzBrL,OAAA,CAACxD,IAAI;cAAC8T,IAAI;cAACC,EAAE,EAAE,EAAG;cAACC,EAAE,EAAE,CAAE;cAAAnF,QAAA,gBACvBrL,OAAA,CAAC3D,UAAU;gBAACiP,OAAO,EAAC,OAAO;gBAAAD,QAAA,gBACzBrL,OAAA;kBAAAqL,QAAA,EAAQ;gBAAa;kBAAAK,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,KAAC,EAAChK,QAAQ,CAACG,YAAY,EAAC,IACxD;cAAA;gBAAA0J,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAY,CAAC,eACb7L,OAAA,CAAC3D,UAAU;gBAACiP,OAAO,EAAC,OAAO;gBAAAD,QAAA,gBACzBrL,OAAA;kBAAAqL,QAAA,EAAQ;gBAAoB;kBAAAK,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,KAAC,EAAClB,kBAAkB;cAAA;gBAAAe,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC/C,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACT,CAAC,eACP7L,OAAA,CAACxD,IAAI;cAAC8T,IAAI;cAACC,EAAE,EAAE,EAAG;cAACC,EAAE,EAAE,CAAE;cAAAnF,QAAA,gBACvBrL,OAAA,CAAC3D,UAAU;gBAACiP,OAAO,EAAC,OAAO;gBAAAD,QAAA,gBACzBrL,OAAA;kBAAAqL,QAAA,EAAQ;gBAAiB;kBAAAK,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,KAAC,EAACqD,YAAY;cAAA;gBAAAxD,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACtC,CAAC,EACZoF,UAAU,iBACTjR,OAAA,CAAC3D,UAAU;gBAACiP,OAAO,EAAC,OAAO;gBAAAD,QAAA,gBACzBrL,OAAA;kBAAAqL,QAAA,EAAQ;gBAAqB;kBAAAK,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,KAAC,EAACoF,UAAU,CAAC1K,aAAa,EAAC,IACnE;cAAA;gBAAAmF,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAY,CACb;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACG,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACJ,CAAC,EAELoF,UAAU,IAAI5H,UAAU,CAACxH,QAAQ,CAACG,YAAY,CAAC,GAAGqH,UAAU,CAAC4H,UAAU,CAAC1K,aAAa,CAAC,IAAI,CAACiD,iBAAiB,iBAC3GxJ,OAAA,CAAClD,KAAK;UAACmQ,QAAQ,EAAC,SAAS;UAAC1B,EAAE,EAAE;YAAEgD,EAAE,EAAE;UAAE,CAAE;UAAAlD,QAAA,gBACtCrL,OAAA;YAAAqL,QAAA,EAAQ;UAAW;YAAAK,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC,qBAAiB,EAAChK,QAAQ,CAACG,YAAY,EAAC,4CAA0C,EAACiP,UAAU,CAAC1K,aAAa,EAAC,gDAE1I;QAAA;UAAAmF,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAO,CACR,eAED7L,OAAA,CAAClD,KAAK;UAACmQ,QAAQ,EAAC,MAAM;UAAC1B,EAAE,EAAE;YAAEgD,EAAE,EAAE;UAAE,CAAE;UAAAlD,QAAA,GAAC,8EAEpC,EAACxJ,QAAQ,CAACI,SAAS,IAAIJ,QAAQ,CAACI,SAAS,KAAK,cAAc,IAAI,gFAAgF;QAAA;UAAAyJ,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC3I,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACL,CAAC;EAEV,CAAC;;EAED;EACA,MAAMsF,cAAc,GAAItC,IAAI,IAAK;IAC/B,QAAQA,IAAI;MACV,KAAK,CAAC;QACJ,OAAOzD,WAAW,CAAC,CAAC;MAAE;MACxB,KAAK,CAAC;QACJ,OAAO0D,WAAW,CAAC,CAAC;MAAE;MACxB,KAAK,CAAC;QACJ,OAAOhB,WAAW,CAAC,CAAC;MAAE;MACxB,KAAK,CAAC;QACJ,OAAOkD,WAAW,CAAC,CAAC;MAAE;MACxB;QACE,OAAO,mBAAmB;IAC9B;EACF,CAAC;;EAED;EACA,MAAMI,4BAA4B,GAAGA,CAAA,KAAM;IACzCrO,wBAAwB,CAAC,KAAK,CAAC;IAC/BM,kBAAkB,CAAC,IAAI,CAAC;EAC1B,CAAC;;EAID;EACA,MAAMgO,gBAAgB,GAAGA,CAAA,KAAM;IAC7B,IAAIjO,eAAe,EAAE;MACnB5C,QAAQ,CAAC,mCAAmCJ,UAAU,IAAIgD,eAAe,CAACrB,OAAO,EAAE,CAAC;IACtF;IACAqP,4BAA4B,CAAC,CAAC;EAChC,CAAC;;EAED;EACA,MAAME,wBAAwB,GAAGA,CAAA,KAAM;IACrCF,4BAA4B,CAAC,CAAC;IAC9B;IACA5P,eAAe,CAAC,IAAI,CAAC;IACrBE,cAAc,CAAC,EAAE,CAAC;IAClBR,oBAAoB,CAAC,KAAK,CAAC;EAC7B,CAAC;;EAED;EACA,MAAMqQ,iCAAiC,GAAGA,CAAA,KAAM;IAC9ChP,6BAA6B,CAAC,KAAK,CAAC;IACpCI,uBAAuB,CAAC;MAAEC,IAAI,EAAE,IAAI;MAAEC,MAAM,EAAE;IAAK,CAAC,CAAC;EACvD,CAAC;;EAED;EACA,MAAM2O,2BAA2B,GAAG,MAAAA,CAAA,KAAY;IAC9C,MAAM;MAAE5O,IAAI;MAAEC;IAAO,CAAC,GAAGH,oBAAoB;IAC7C,IAAI,CAACE,IAAI,IAAI,CAACC,MAAM,EAAE;MACpByB,OAAO,CAACM,KAAK,CAAC,uCAAuC,EAAE;QAAEhC,IAAI;QAAEC;MAAO,CAAC,CAAC;MACxEvC,OAAO,CAAC,sCAAsC,CAAC;MAC/C;IACF;IAEA,IAAI;MACFI,UAAU,CAAC,IAAI,CAAC;MAChB4D,OAAO,CAACC,GAAG,CAAC,0CAA0C3B,IAAI,CAACb,OAAO,iCAAiCc,MAAM,CAACZ,SAAS,EAAE,CAAC;MACtHqC,OAAO,CAACC,GAAG,CAAC,sCAAsC,EAAE3B,IAAI,CAAC;MACzD0B,OAAO,CAACC,GAAG,CAAC,cAAc,EAAE1B,MAAM,CAAC;;MAEnC;MACA,MAAM7D,WAAW,CAACyS,0BAA0B,CAACrR,UAAU,EAAEwC,IAAI,CAACb,OAAO,EAAEc,MAAM,CAACZ,SAAS,CAAC;;MAExF;MACA,MAAMwG,WAAW,GAAG,MAAMzJ,WAAW,CAAC0S,WAAW,CAACtR,UAAU,EAAEwC,IAAI,CAACb,OAAO,CAAC;MAC3EuC,OAAO,CAACC,GAAG,CAAC,kCAAkC,EAAEkE,WAAW,CAAC;MAC5DjH,eAAe,CAACiH,WAAW,CAAC;;MAE5B;MACA3G,WAAW,CAAC;QACV,GAAGD,QAAQ;QACXI,SAAS,EAAEY,MAAM,CAACZ;MACpB,CAAC,CAAC;;MAEF;MACA,MAAMgC,UAAU,CAAC,CAAC;MAElB5D,SAAS,CAAC,4BAA4BuC,IAAI,CAACb,OAAO,6CAA6Cc,MAAM,CAACZ,SAAS,EAAE,CAAC;MAClHsP,iCAAiC,CAAC,CAAC;IACrC,CAAC,CAAC,OAAO3M,KAAK,EAAE;MACdN,OAAO,CAACM,KAAK,CAAC,iEAAiE,EAAEA,KAAK,CAAC;MACvFtE,OAAO,CAAC,kEAAkE,IAAIsE,KAAK,CAACqB,MAAM,IAAIrB,KAAK,CAACI,OAAO,IAAI,oBAAoB,CAAC,CAAC;IACvI,CAAC,SAAS;MACRtE,UAAU,CAAC,KAAK,CAAC;IACnB;EACF,CAAC;;EAED;EACA,MAAMiR,uBAAuB,GAAGA,CAAA,KAAM;IACpCJ,iCAAiC,CAAC,CAAC;IACnC;IACAzP,WAAW,CAAC;MACV,GAAGD,QAAQ;MACXI,SAAS,EAAE;IACb,CAAC,CAAC;EACJ,CAAC;EAED,oBACEjC,OAAA,CAAC7D,GAAG;IAAAkP,QAAA,gBAEFrL,OAAA,CAAC7D,GAAG;MAACoP,EAAE,EAAE;QAAEC,EAAE,EAAE;MAAE,CAAE;MAAAH,QAAA,gBACjBrL,OAAA,CAAC7D,GAAG;QAACoP,EAAE,EAAE;UAAES,OAAO,EAAE,MAAM;UAAEC,UAAU,EAAE,QAAQ;UAAET,EAAE,EAAE;QAAE,CAAE;QAAAH,QAAA,gBACxDrL,OAAA,CAAC3D,UAAU;UAACiP,OAAO,EAAC,WAAW;UAACC,EAAE,EAAE;YAAEE,UAAU,EAAE,MAAM;YAAEU,QAAQ,EAAE;UAAO,CAAE;UAAAd,QAAA,EAAC;QAE9E;UAAAK,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAY,CAAC,eACb7L,OAAA,CAAC1D,SAAS;UACR8P,IAAI,EAAC,OAAO;UACZrI,KAAK,EAAEtC,WAAY;UACnB6K,QAAQ,EAAGxD,CAAC,IAAKpH,cAAc,CAACoH,CAAC,CAAChF,MAAM,CAACC,KAAK,CAAE;UAChDuH,OAAO,EAAC,UAAU;UAClBC,EAAE,EAAE;YAAEW,EAAE,EAAE,CAAC;YAAEmC,IAAI,EAAE;UAAE;QAAE;UAAA3C,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACxB,CAAC,eACF7L,OAAA,CAACzD,MAAM;UACL+O,OAAO,EAAC,WAAW;UACnBmB,KAAK,EAAC,SAAS;UACfC,OAAO,EAAE9E,oBAAqB;UAC9B+E,QAAQ,EAAEhM,WAAW,IAAI,CAACc,WAAW,CAACmF,IAAI,CAAC,CAAE;UAC7CwF,IAAI,EAAC,OAAO;UAAAf,QAAA,EACb;QAED;UAAAK,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACN,CAAC,EAGLtK,YAAY,iBACXvB,OAAA,CAAC7D,GAAG;QAACoP,EAAE,EAAE;UAAES,OAAO,EAAE,MAAM;UAAEe,cAAc,EAAE,eAAe;UAAEhB,KAAK,EAAE,MAAM;UAAEwC,EAAE,EAAE;QAAE,CAAE;QAAAlD,QAAA,gBAClFrL,OAAA,CAAC7D,GAAG;UAAAkP,QAAA,gBACFrL,OAAA,CAAC3D,UAAU;YAACiP,OAAO,EAAC,WAAW;YAACC,EAAE,EAAE;cAAEE,UAAU,EAAE;YAAO,CAAE;YAAAJ,QAAA,GAAC,oBACxC,eAAArL,OAAA;cAAMgO,KAAK,EAAE;gBAAEvB,KAAK,EAAE;cAAU,CAAE;cAAApB,QAAA,EAAE9J,YAAY,CAACQ;YAAO;cAAA2J,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACxE,CAAC,eACb7L,OAAA,CAAC7D,GAAG;YAACoP,EAAE,EAAE;cAAES,OAAO,EAAE,MAAM;cAAEiC,GAAG,EAAE;YAAE,CAAE;YAAA5C,QAAA,gBACnCrL,OAAA,CAAC3D,UAAU;cAACiP,OAAO,EAAC,OAAO;cAAAD,QAAA,GAAC,aAAW,EAAC9J,YAAY,CAACiF,SAAS,IAAI,KAAK;YAAA;cAAAkF,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAa,CAAC,eACrF7L,OAAA,CAAC3D,UAAU;cAACiP,OAAO,EAAC,OAAO;cAAAD,QAAA,GAAC,cAAY,EAAC9J,YAAY,CAACkF,OAAO,IAAI,KAAK;YAAA;cAAAiF,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAa,CAAC,eACpF7L,OAAA,CAAC3D,UAAU;cAACiP,OAAO,EAAC,OAAO;cAAAD,QAAA,GAAC,iBAAe,EAAC9J,YAAY,CAACgI,aAAa,IAAI,KAAK,EAAC,IAAE;YAAA;cAAAmC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAY,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC5F,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,eACN7L,OAAA,CAAC7D,GAAG;UAAAkP,QAAA,eACFrL,OAAA,CAAC7D,GAAG;YAACoP,EAAE,EAAE;cAAES,OAAO,EAAE,MAAM;cAAE4F,aAAa,EAAE,QAAQ;cAAE3F,UAAU,EAAE;YAAW,CAAE;YAAAZ,QAAA,gBAC5ErL,OAAA,CAAC3D,UAAU;cAACiP,OAAO,EAAC,OAAO;cAAAD,QAAA,GAAC,uBAAqB,EAAC9J,YAAY,CAACsQ,mBAAmB,IAAI,KAAK;YAAA;cAAAnG,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAa,CAAC,eACzG7L,OAAA,CAAC3D,UAAU;cAACiP,OAAO,EAAC,OAAO;cAAAD,QAAA,GAAC,qBAAmB,EAAC9J,YAAY,CAACuQ,iBAAiB,IAAI,KAAK;YAAA;cAAApG,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAa,CAAC,eACrG7L,OAAA,CAAC3D,UAAU;cAACiP,OAAO,EAAC,OAAO;cAAAD,QAAA,GAAC,QAE1B,eAAArL,OAAA,CAAC9C,IAAI;gBACHkP,IAAI,EAAC,OAAO;gBACZC,KAAK,EAAE9K,YAAY,CAACyG,mBAAmB,IAAI,KAAM;gBACjDyE,KAAK,EAAE9M,kBAAkB,CAAC4B,YAAY,CAACyG,mBAAmB,CAAE;gBAC5DuD,EAAE,EAAE;kBAAE2C,EAAE,EAAE,CAAC;kBAAEpB,MAAM,EAAE,MAAM;kBAAE,kBAAkB,EAAE;oBAAEc,EAAE,EAAE,CAAC;oBAAEV,EAAE,EAAE;kBAAE;gBAAE;cAAE;gBAAAxB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACrE,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACQ,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACV;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CACN,eAED7L,OAAA,CAACnD,OAAO;QAAC0O,EAAE,EAAE;UAAEyB,EAAE,EAAE;QAAE;MAAE;QAAAtB,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACvB,CAAC,EAGL5K,iBAAiB,IAAIF,aAAa,CAAC2D,MAAM,GAAG,CAAC,iBAC5C1E,OAAA,CAAC5D,KAAK;MAACmP,EAAE,EAAE;QAAEO,CAAC,EAAE,CAAC;QAAEN,EAAE,EAAE;MAAE,CAAE;MAAAH,QAAA,gBACzBrL,OAAA,CAAC3D,UAAU;QAACiP,OAAO,EAAC,IAAI;QAAC6C,YAAY;QAAA9C,QAAA,EAAC;MAEtC;QAAAK,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAY,CAAC,eACb7L,OAAA,CAACrC,cAAc;QAAA0N,QAAA,eACbrL,OAAA,CAACxC,KAAK;UAAC4O,IAAI,EAAC,OAAO;UAAAf,QAAA,gBACjBrL,OAAA,CAACpC,SAAS;YAAAyN,QAAA,eACRrL,OAAA,CAACnC,QAAQ;cAAC0N,EAAE,EAAE;gBAAEgC,OAAO,EAAE;cAAU,CAAE;cAAAlC,QAAA,gBACnCrL,OAAA,CAACtC,SAAS;gBAAA2N,QAAA,EAAC;cAAO;gBAAAK,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAW,CAAC,eAC9B7L,OAAA,CAACtC,SAAS;gBAAA2N,QAAA,EAAC;cAAS;gBAAAK,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAW,CAAC,eAChC7L,OAAA,CAACtC,SAAS;gBAAA2N,QAAA,EAAC;cAAU;gBAAAK,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAW,CAAC,eACjC7L,OAAA,CAACtC,SAAS;gBAAA2N,QAAA,EAAC;cAAU;gBAAAK,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAW,CAAC,eACjC7L,OAAA,CAACtC,SAAS;gBAAA2N,QAAA,EAAC;cAAa;gBAAAK,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAW,CAAC,eACpC7L,OAAA,CAACtC,SAAS;gBAAA2N,QAAA,EAAC;cAAK;gBAAAK,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAW,CAAC,eAC5B7L,OAAA,CAACtC,SAAS;gBAAA2N,QAAA,EAAC;cAAM;gBAAAK,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAW,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACrB;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACF,CAAC,eACZ7L,OAAA,CAACvC,SAAS;YAAA4N,QAAA,EACPtK,aAAa,CAAC0M,GAAG,CAAE7K,IAAI,iBACtB5C,OAAA,CAACnC,QAAQ;cAAAwN,QAAA,gBACPrL,OAAA,CAACtC,SAAS;gBAAA2N,QAAA,EAAEzI,IAAI,CAACb;cAAO;gBAAA2J,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAY,CAAC,eACrC7L,OAAA,CAACtC,SAAS;gBAAA2N,QAAA,EAAEzI,IAAI,CAAC4D,SAAS,IAAI;cAAK;gBAAAkF,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAY,CAAC,eAChD7L,OAAA,CAACtC,SAAS;gBAAA2N,QAAA,EAAEzI,IAAI,CAAC6D,OAAO,IAAI;cAAK;gBAAAiF,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAY,CAAC,eAC9C7L,OAAA,CAACtC,SAAS;gBAAA2N,QAAA,GAAC,MAAI,EAACzI,IAAI,CAACiP,mBAAmB,IAAI,KAAK,eAAC7R,OAAA;kBAAA0L,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAI,CAAC,OAAG,EAACjJ,IAAI,CAACkP,iBAAiB,IAAI,KAAK;cAAA;gBAAApG,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAY,CAAC,eACvG7L,OAAA,CAACtC,SAAS;gBAAA2N,QAAA,GAAEzI,IAAI,CAAC2G,aAAa,IAAI,KAAK,EAAC,IAAE;cAAA;gBAAAmC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAW,CAAC,eACtD7L,OAAA,CAACtC,SAAS;gBAAA2N,QAAA,eACRrL,OAAA,CAAC9C,IAAI;kBACHmP,KAAK,EAAEzJ,IAAI,CAACoF,mBAAmB,IAAI,KAAM;kBACzCoE,IAAI,EAAC,OAAO;kBACZK,KAAK,EAAE9M,kBAAkB,CAACiD,IAAI,CAACoF,mBAAmB,CAAE;kBACpDsD,OAAO,EAAC;gBAAU;kBAAAI,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACnB;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACO,CAAC,eACZ7L,OAAA,CAACtC,SAAS;gBAAA2N,QAAA,eACRrL,OAAA,CAACzD,MAAM;kBACL6P,IAAI,EAAC,OAAO;kBACZd,OAAO,EAAC,WAAW;kBACnBmB,KAAK,EAAC,SAAS;kBACfC,OAAO,EAAEA,CAAA,KAAMvE,gBAAgB,CAACvF,IAAI,CAAE;kBACtC+J,QAAQ,EAAEjN,gBAAgB,CAACkD,IAAI,CAAE;kBAAAyI,QAAA,EAClC;gBAED;kBAAAK,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACA,CAAC;YAAA,GAxBCjJ,IAAI,CAACb,OAAO;cAAA2J,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAyBjB,CACX;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACO,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACP;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACM,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACZ,CACR,EAEAtK,YAAY,iBACXvB,OAAA,CAAC7D,GAAG;MAAAkP,QAAA,EACDyD,WAAW,CAAC;IAAC;MAAApD,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACX,CACN,eAGD7L,OAAA,CAAC5C,MAAM;MAAC2U,IAAI,EAAErI,iBAAkB;MAACsI,OAAO,EAAEA,CAAA,KAAMrI,oBAAoB,CAAC,KAAK,CAAE;MAAC2E,QAAQ,EAAC,IAAI;MAAC2D,SAAS;MAAA5G,QAAA,gBAClGrL,OAAA,CAAC3C,WAAW;QAACkO,EAAE,EAAE;UAAEgC,OAAO,EAAE;QAAgB,CAAE;QAAAlC,QAAA,eAC5CrL,OAAA,CAAC7D,GAAG;UAACoP,EAAE,EAAE;YAAES,OAAO,EAAE,MAAM;YAAEC,UAAU,EAAE,QAAQ;YAAEgC,GAAG,EAAE;UAAE,CAAE;UAAA5C,QAAA,gBACzDrL,OAAA,CAACtB,WAAW;YAAC+N,KAAK,EAAC;UAAS;YAAAf,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,eAC/B7L,OAAA,CAAC3D,UAAU;YAACiP,OAAO,EAAC,IAAI;YAAAD,QAAA,EAAEzB,kBAAkB,CAACE;UAAK;YAAA4B,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAa,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC7D;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACK,CAAC,eACd7L,OAAA,CAAC1C,aAAa;QAAA+N,QAAA,eACZrL,OAAA,CAAC3D,UAAU;UAACiP,OAAO,EAAC,OAAO;UAACC,EAAE,EAAE;YAAEgD,EAAE,EAAE;UAAE,CAAE;UAAAlD,QAAA,EACvCzB,kBAAkB,CAAC5E;QAAO;UAAA0G,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACjB;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACA,CAAC,eAChB7L,OAAA,CAACzC,aAAa;QAAA8N,QAAA,gBACZrL,OAAA,CAACzD,MAAM;UAACmQ,OAAO,EAAEA,CAAA,KAAM/C,oBAAoB,CAAC,KAAK,CAAE;UAAC8C,KAAK,EAAC,WAAW;UAACnB,OAAO,EAAC,UAAU;UAAAD,QAAA,EAAC;QAEzF;UAAAK,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC,eACT7L,OAAA,CAACzD,MAAM;UACLmQ,OAAO,EAAEA,CAAA,KAAM;YACb/C,oBAAoB,CAAC,KAAK,CAAC;YAC3BC,kBAAkB,CAACG,SAAS,CAAC,CAAC;UAChC,CAAE;UACF0C,KAAK,EAAC,SAAS;UACfnB,OAAO,EAAC,WAAW;UACnB4G,SAAS;UAAA7G,QAAA,EACV;QAED;UAAAK,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACI,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACV,CAAC,eAGT7L,OAAA,CAAC5C,MAAM;MAAC2U,IAAI,EAAEjP,qBAAsB;MAACkP,OAAO,EAAEZ,4BAA6B;MAAC9C,QAAQ,EAAC,IAAI;MAAC2D,SAAS;MAAA5G,QAAA,gBACjGrL,OAAA,CAAC3C,WAAW;QAACkO,EAAE,EAAE;UAAEgC,OAAO,EAAE;QAAgB,CAAE;QAAAlC,QAAA,eAC5CrL,OAAA,CAAC7D,GAAG;UAACoP,EAAE,EAAE;YAAES,OAAO,EAAE,MAAM;YAAEC,UAAU,EAAE,QAAQ;YAAEgC,GAAG,EAAE;UAAE,CAAE;UAAA5C,QAAA,gBACzDrL,OAAA,CAACtB,WAAW;YAAC+N,KAAK,EAAC;UAAS;YAAAf,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,eAC/B7L,OAAA,CAAC3D,UAAU;YAACiP,OAAO,EAAC,IAAI;YAAAD,QAAA,EAAC;UAAe;YAAAK,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAY,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAClD;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACK,CAAC,eACd7L,OAAA,CAAC1C,aAAa;QAAA+N,QAAA,EACXjI,eAAe,iBACdpD,OAAA,CAAC7D,GAAG;UAACoP,EAAE,EAAE;YAAEgD,EAAE,EAAE;UAAE,CAAE;UAAAlD,QAAA,gBACjBrL,OAAA,CAAC3D,UAAU;YAACiP,OAAO,EAAC,OAAO;YAAC6G,SAAS;YAAA9G,QAAA,GAAC,UAC5B,eAAArL,OAAA;cAAAqL,QAAA,EAASjI,eAAe,CAACrB;YAAO;cAAA2J,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAS,CAAC,4BAAqB,EAACzI,eAAe,CAAC6E,eAAe,IAAI,CAAC,EAAC,KAC/G;UAAA;YAAAyD,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAY,CAAC,eACb7L,OAAA,CAAC3D,UAAU;YAACiP,OAAO,EAAC,OAAO;YAAC6G,SAAS;YAAA9G,QAAA,EAAC;UAEtC;YAAAK,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAY,CAAC,eACb7L,OAAA,CAAC3D,UAAU;YAACiP,OAAO,EAAC,OAAO;YAAC6B,SAAS,EAAC,IAAI;YAAA9B,QAAA,gBACxCrL,OAAA;cAAAqL,QAAA,EAAI;YAAsC;cAAAK,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eAC/C7L,OAAA;cAAAqL,QAAA,EAAI;YAAyB;cAAAK,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eAClC7L,OAAA;cAAAqL,QAAA,EAAI;YAAsB;cAAAK,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACrB,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACV;MACN;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACY,CAAC,eAChB7L,OAAA,CAACzC,aAAa;QAACgO,EAAE,EAAE;UAAEO,CAAC,EAAE,CAAC;UAAEiB,cAAc,EAAE;QAAgB,CAAE;QAAA1B,QAAA,gBAC3DrL,OAAA,CAACzD,MAAM;UAACmQ,OAAO,EAAE0E,4BAA6B;UAAC3E,KAAK,EAAC,WAAW;UAAApB,QAAA,EAAC;QAEjE;UAAAK,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC,eACT7L,OAAA,CAAC7D,GAAG;UAAAkP,QAAA,gBACFrL,OAAA,CAACzD,MAAM;YAACmQ,OAAO,EAAE4E,wBAAyB;YAAC7E,KAAK,EAAC,SAAS;YAAClB,EAAE,EAAE;cAAEW,EAAE,EAAE;YAAE,CAAE;YAAAb,QAAA,EAAC;UAE1E;YAAAK,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC,eACT7L,OAAA,CAACzD,MAAM;YAACmQ,OAAO,EAAE2E,gBAAiB;YAAC/F,OAAO,EAAC,WAAW;YAACmB,KAAK,EAAC,SAAS;YAAApB,QAAA,EAAC;UAEvE;YAAAK,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACN,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACO,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACV,CAAC,eAGT7L,OAAA,CAACd,sBAAsB;MACrB6S,IAAI,EAAEzP,0BAA2B;MACjC0P,OAAO,EAAET,iCAAkC;MAC3C3O,IAAI,EAAEF,oBAAoB,CAACE,IAAK;MAChCC,MAAM,EAAEH,oBAAoB,CAACG,MAAO;MACpCuP,YAAY,EAAEZ,2BAA4B;MAC1Ca,mBAAmB,EAAEV;IAAwB;MAAAjG,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAC9C,CAAC,eAGF7L,OAAA,CAAC5C,MAAM;MACL2U,IAAI,EAAEzO,qBAAsB;MAC5B0O,OAAO,EAAEA,CAAA,KAAMzO,wBAAwB,CAAC,KAAK,CAAE;MAC/C+K,QAAQ,EAAC,IAAI;MACb2D,SAAS;MAAA5G,QAAA,gBAETrL,OAAA,CAAC3C,WAAW;QAAAgO,QAAA,eACVrL,OAAA,CAAC7D,GAAG;UAACoP,EAAE,EAAE;YAAES,OAAO,EAAE,MAAM;YAAEC,UAAU,EAAE,QAAQ;YAAEgC,GAAG,EAAE;UAAE,CAAE;UAAA5C,QAAA,gBACzDrL,OAAA,CAACpB,QAAQ;YAAC6N,KAAK,EAAC;UAAS;YAAAf,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,eAC5B7L,OAAA,CAAC3D,UAAU;YAACiP,OAAO,EAAC,IAAI;YAAAD,QAAA,EAAC;UAAa;YAAAK,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAY,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAChD;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACK,CAAC,eACd7L,OAAA,CAAC1C,aAAa;QAAA+N,QAAA,eACZrL,OAAA,CAACb,eAAe;UAACyD,IAAI,EAAErB;QAAa;UAAAmK,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC1B,CAAC,eAChB7L,OAAA,CAACzC,aAAa;QAAA8N,QAAA,eACZrL,OAAA,CAACzD,MAAM;UAACmQ,OAAO,EAAEA,CAAA,KAAMnJ,wBAAwB,CAAC,KAAK,CAAE;UAACkJ,KAAK,EAAC,SAAS;UAAApB,QAAA,EAAC;QAExE;UAAAK,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACI,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACV,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACN,CAAC;AAEV,CAAC;AAACtL,EAAA,CA9gEIJ,kBAAkB;EAAA,QACLpB,WAAW;AAAA;AAAAuT,EAAA,GADxBnS,kBAAkB;AAghExB,eAAeA,kBAAkB;AAAC,IAAAmS,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}