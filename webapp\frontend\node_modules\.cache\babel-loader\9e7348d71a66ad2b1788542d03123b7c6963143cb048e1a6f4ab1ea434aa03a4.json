{"ast": null, "code": "var formatDistanceLocale = {\n  lessThanXSeconds: {\n    one: '少於 1 秒',\n    other: '少於 {{count}} 秒'\n  },\n  xSeconds: {\n    one: '1 秒',\n    other: '{{count}} 秒'\n  },\n  halfAMinute: '半分鐘',\n  lessThanXMinutes: {\n    one: '少於 1 分鐘',\n    other: '少於 {{count}} 分鐘'\n  },\n  xMinutes: {\n    one: '1 分鐘',\n    other: '{{count}} 分鐘'\n  },\n  xHours: {\n    one: '1 小時',\n    other: '{{count}} 小時'\n  },\n  aboutXHours: {\n    one: '大約 1 小時',\n    other: '大約 {{count}} 小時'\n  },\n  xDays: {\n    one: '1 天',\n    other: '{{count}} 天'\n  },\n  aboutXWeeks: {\n    one: '大約 1 個星期',\n    other: '大約 {{count}} 個星期'\n  },\n  xWeeks: {\n    one: '1 個星期',\n    other: '{{count}} 個星期'\n  },\n  aboutXMonths: {\n    one: '大約 1 個月',\n    other: '大約 {{count}} 個月'\n  },\n  xMonths: {\n    one: '1 個月',\n    other: '{{count}} 個月'\n  },\n  aboutXYears: {\n    one: '大約 1 年',\n    other: '大約 {{count}} 年'\n  },\n  xYears: {\n    one: '1 年',\n    other: '{{count}} 年'\n  },\n  overXYears: {\n    one: '超過 1 年',\n    other: '超過 {{count}} 年'\n  },\n  almostXYears: {\n    one: '將近 1 年',\n    other: '將近 {{count}} 年'\n  }\n};\nvar formatDistance = function formatDistance(token, count, options) {\n  var result;\n  var tokenValue = formatDistanceLocale[token];\n  if (typeof tokenValue === 'string') {\n    result = tokenValue;\n  } else if (count === 1) {\n    result = tokenValue.one;\n  } else {\n    result = tokenValue.other.replace('{{count}}', String(count));\n  }\n  if (options !== null && options !== void 0 && options.addSuffix) {\n    if (options.comparison && options.comparison > 0) {\n      return result + '內';\n    } else {\n      return result + '前';\n    }\n  }\n  return result;\n};\nexport default formatDistance;", "map": {"version": 3, "names": ["formatDistanceLocale", "lessThanXSeconds", "one", "other", "xSeconds", "halfAMinute", "lessThanXMinutes", "xMinutes", "xHours", "aboutXHours", "xDays", "aboutXWeeks", "xWeeks", "aboutXMonths", "xMonths", "aboutXYears", "xYears", "overXYears", "almostXYears", "formatDistance", "token", "count", "options", "result", "tokenValue", "replace", "String", "addSuffix", "comparison"], "sources": ["C:/CMS/webapp/frontend/node_modules/date-fns/esm/locale/zh-TW/_lib/formatDistance/index.js"], "sourcesContent": ["var formatDistanceLocale = {\n  lessThanXSeconds: {\n    one: '少於 1 秒',\n    other: '少於 {{count}} 秒'\n  },\n  xSeconds: {\n    one: '1 秒',\n    other: '{{count}} 秒'\n  },\n  halfAMinute: '半分鐘',\n  lessThanXMinutes: {\n    one: '少於 1 分鐘',\n    other: '少於 {{count}} 分鐘'\n  },\n  xMinutes: {\n    one: '1 分鐘',\n    other: '{{count}} 分鐘'\n  },\n  xHours: {\n    one: '1 小時',\n    other: '{{count}} 小時'\n  },\n  aboutXHours: {\n    one: '大約 1 小時',\n    other: '大約 {{count}} 小時'\n  },\n  xDays: {\n    one: '1 天',\n    other: '{{count}} 天'\n  },\n  aboutXWeeks: {\n    one: '大約 1 個星期',\n    other: '大約 {{count}} 個星期'\n  },\n  xWeeks: {\n    one: '1 個星期',\n    other: '{{count}} 個星期'\n  },\n  aboutXMonths: {\n    one: '大約 1 個月',\n    other: '大約 {{count}} 個月'\n  },\n  xMonths: {\n    one: '1 個月',\n    other: '{{count}} 個月'\n  },\n  aboutXYears: {\n    one: '大約 1 年',\n    other: '大約 {{count}} 年'\n  },\n  xYears: {\n    one: '1 年',\n    other: '{{count}} 年'\n  },\n  overXYears: {\n    one: '超過 1 年',\n    other: '超過 {{count}} 年'\n  },\n  almostXYears: {\n    one: '將近 1 年',\n    other: '將近 {{count}} 年'\n  }\n};\nvar formatDistance = function formatDistance(token, count, options) {\n  var result;\n  var tokenValue = formatDistanceLocale[token];\n  if (typeof tokenValue === 'string') {\n    result = tokenValue;\n  } else if (count === 1) {\n    result = tokenValue.one;\n  } else {\n    result = tokenValue.other.replace('{{count}}', String(count));\n  }\n  if (options !== null && options !== void 0 && options.addSuffix) {\n    if (options.comparison && options.comparison > 0) {\n      return result + '內';\n    } else {\n      return result + '前';\n    }\n  }\n  return result;\n};\nexport default formatDistance;"], "mappings": "AAAA,IAAIA,oBAAoB,GAAG;EACzBC,gBAAgB,EAAE;IAChBC,GAAG,EAAE,QAAQ;IACbC,KAAK,EAAE;EACT,CAAC;EACDC,QAAQ,EAAE;IACRF,GAAG,EAAE,KAAK;IACVC,KAAK,EAAE;EACT,CAAC;EACDE,WAAW,EAAE,KAAK;EAClBC,gBAAgB,EAAE;IAChBJ,GAAG,EAAE,SAAS;IACdC,KAAK,EAAE;EACT,CAAC;EACDI,QAAQ,EAAE;IACRL,GAAG,EAAE,MAAM;IACXC,KAAK,EAAE;EACT,CAAC;EACDK,MAAM,EAAE;IACNN,GAAG,EAAE,MAAM;IACXC,KAAK,EAAE;EACT,CAAC;EACDM,WAAW,EAAE;IACXP,GAAG,EAAE,SAAS;IACdC,KAAK,EAAE;EACT,CAAC;EACDO,KAAK,EAAE;IACLR,GAAG,EAAE,KAAK;IACVC,KAAK,EAAE;EACT,CAAC;EACDQ,WAAW,EAAE;IACXT,GAAG,EAAE,UAAU;IACfC,KAAK,EAAE;EACT,CAAC;EACDS,MAAM,EAAE;IACNV,GAAG,EAAE,OAAO;IACZC,KAAK,EAAE;EACT,CAAC;EACDU,YAAY,EAAE;IACZX,GAAG,EAAE,SAAS;IACdC,KAAK,EAAE;EACT,CAAC;EACDW,OAAO,EAAE;IACPZ,GAAG,EAAE,MAAM;IACXC,KAAK,EAAE;EACT,CAAC;EACDY,WAAW,EAAE;IACXb,GAAG,EAAE,QAAQ;IACbC,KAAK,EAAE;EACT,CAAC;EACDa,MAAM,EAAE;IACNd,GAAG,EAAE,KAAK;IACVC,KAAK,EAAE;EACT,CAAC;EACDc,UAAU,EAAE;IACVf,GAAG,EAAE,QAAQ;IACbC,KAAK,EAAE;EACT,CAAC;EACDe,YAAY,EAAE;IACZhB,GAAG,EAAE,QAAQ;IACbC,KAAK,EAAE;EACT;AACF,CAAC;AACD,IAAIgB,cAAc,GAAG,SAASA,cAAcA,CAACC,KAAK,EAAEC,KAAK,EAAEC,OAAO,EAAE;EAClE,IAAIC,MAAM;EACV,IAAIC,UAAU,GAAGxB,oBAAoB,CAACoB,KAAK,CAAC;EAC5C,IAAI,OAAOI,UAAU,KAAK,QAAQ,EAAE;IAClCD,MAAM,GAAGC,UAAU;EACrB,CAAC,MAAM,IAAIH,KAAK,KAAK,CAAC,EAAE;IACtBE,MAAM,GAAGC,UAAU,CAACtB,GAAG;EACzB,CAAC,MAAM;IACLqB,MAAM,GAAGC,UAAU,CAACrB,KAAK,CAACsB,OAAO,CAAC,WAAW,EAAEC,MAAM,CAACL,KAAK,CAAC,CAAC;EAC/D;EACA,IAAIC,OAAO,KAAK,IAAI,IAAIA,OAAO,KAAK,KAAK,CAAC,IAAIA,OAAO,CAACK,SAAS,EAAE;IAC/D,IAAIL,OAAO,CAACM,UAAU,IAAIN,OAAO,CAACM,UAAU,GAAG,CAAC,EAAE;MAChD,OAAOL,MAAM,GAAG,GAAG;IACrB,CAAC,MAAM;MACL,OAAOA,MAAM,GAAG,GAAG;IACrB;EACF;EACA,OAAOA,MAAM;AACf,CAAC;AACD,eAAeJ,cAAc", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}