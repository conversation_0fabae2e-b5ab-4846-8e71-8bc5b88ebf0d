{"ast": null, "code": "import React,{useState,useEffect}from'react';import{Box,Typography,Chip,TableRow,TableCell,Button}from'@mui/material';import{Clear as ClearIcon}from'@mui/icons-material';import FilterableTable from'../common/FilterableTable';import{formatDate}from'../../utils/dateUtils';/**\n * Componente per visualizzare la lista dei cavi con filtri in stile Excel\n *\n * @param {Object} props - Proprietà del componente\n * @param {Array} props.cavi - Lista dei cavi da visualizzare\n * @param {boolean} props.loading - Indica se i dati sono in caricamento\n * @param {Function} props.onFilteredDataChange - Funzione chiamata quando i dati filtrati cambiano\n * @param {string} props.revisioneCorrente - Revisione corrente da mostrare nelle statistiche\n */import{jsx as _jsx,jsxs as _jsxs,Fragment as _Fragment}from\"react/jsx-runtime\";const CaviFilterableTable=_ref=>{let{cavi=[],loading=false,onFilteredDataChange=null,revisioneCorrente=null}=_ref;const[filteredCavi,setFilteredCavi]=useState(cavi);const[resetFilters,setResetFilters]=useState(false);// Aggiorna i dati filtrati quando cambiano i cavi\nuseEffect(()=>{setFilteredCavi(cavi);},[cavi]);// Notifica il componente padre quando cambiano i dati filtrati\nconst handleFilteredDataChange=data=>{setFilteredCavi(data);if(onFilteredDataChange){onFilteredDataChange(data);}};// Gestisce il reset dei filtri\nconst handleResetFilters=()=>{setResetFilters(false);};// Definizione delle colonne\nconst columns=[{field:'id_cavo',headerName:'ID Cavo',dataType:'text',headerStyle:{fontWeight:'bold'}},// Colonna Revisione rimossa e spostata nella tabella delle statistiche\n{field:'sistema',headerName:'Sistema',dataType:'text'},{field:'utility',headerName:'Utility',dataType:'text'},{field:'tipologia',headerName:'Tipologia',dataType:'text'},// n_conduttori field is now a spare field (kept in DB but hidden in UI)\n{field:'sezione',headerName:'Formazione',dataType:'text',align:'right',cellStyle:{textAlign:'right'}},{field:'metri_teorici',headerName:'Metri Teorici',dataType:'number',align:'right',cellStyle:{textAlign:'right'},renderCell:row=>row.metri_teorici?row.metri_teorici.toFixed(1):'0'},{field:'metratura_reale',headerName:'Metri Reali',dataType:'number',align:'right',cellStyle:{textAlign:'right'},renderCell:row=>row.metratura_reale?row.metratura_reale.toFixed(1):'0'},{field:'stato_installazione',headerName:'Stato',dataType:'text',renderCell:row=>{let color='default';if(row.stato_installazione==='INSTALLATO')color='success';else if(row.stato_installazione==='IN_CORSO')color='warning';else if(row.stato_installazione==='DA_INSTALLARE')color='error';return/*#__PURE__*/_jsx(Chip,{label:row.stato_installazione||'N/D',size:\"small\",color:color,variant:\"outlined\"});}},{field:'id_bobina',headerName:'Bobina',dataType:'text',renderCell:row=>{// Gestione differenziata per null e BOBINA_VUOTA\nif(row.id_bobina===null){// Per cavi non posati (id_bobina è null)\nreturn'-';}else if(row.id_bobina==='BOBINA_VUOTA'){// Per cavi posati senza bobina specifica\nreturn'BOBINA VUOTA';}else if(!row.id_bobina){// Per altri casi in cui id_bobina è falsy (undefined, stringa vuota)\nreturn'-';}// Estrai solo il numero della bobina (parte dopo '_B')\nconst match=row.id_bobina.match(/_B(.+)$/);return match?match[1]:row.id_bobina;}},{field:'timestamp',headerName:'Data Modifica',dataType:'date',renderCell:row=>formatDate(row.timestamp)},{field:'collegamenti',headerName:'Collegamenti',dataType:'number',align:'center',cellStyle:{textAlign:'center'},renderCell:row=>{let color='default';if(row.collegamenti===2)color='success';else if(row.collegamenti===1)color='warning';else color='error';return/*#__PURE__*/_jsx(Chip,{label:row.collegamenti,size:\"small\",color:color,variant:\"outlined\"});}}];// Renderizza una riga personalizzata\nconst renderRow=(row,index)=>{// Determina il colore di sfondo in base allo stato\nlet bgColor='inherit';if(row.stato_installazione==='INSTALLATO')bgColor='rgba(76, 175, 80, 0.1)';else if(row.stato_installazione==='IN_CORSO')bgColor='rgba(255, 152, 0, 0.1)';return/*#__PURE__*/_jsx(TableRow,{sx:{backgroundColor:bgColor,'&:hover':{backgroundColor:'rgba(0, 0, 0, 0.04)'}},children:columns.map(column=>/*#__PURE__*/_jsx(TableCell,{align:column.align||'left',sx:column.cellStyle,children:column.renderCell?column.renderCell(row):row[column.field]},column.field))},index);};// Calcola le statistiche\nconst calculateStats=()=>{if(!filteredCavi.length)return null;const totalCavi=filteredCavi.length;const installati=filteredCavi.filter(c=>c.stato_installazione==='INSTALLATO').length;const inCorso=filteredCavi.filter(c=>c.stato_installazione==='IN_CORSO').length;const daInstallare=filteredCavi.filter(c=>c.stato_installazione==='DA_INSTALLARE').length;const metriTeoriciTotali=filteredCavi.reduce((sum,c)=>sum+(c.metri_teorici||0),0);const metriRealiTotali=filteredCavi.reduce((sum,c)=>sum+(c.metratura_reale||0),0);const percentualeCompletamento=totalCavi?Math.round(installati/totalCavi*100):0;return{totalCavi,installati,inCorso,daInstallare,metriTeoriciTotali,metriRealiTotali,percentualeCompletamento};};const stats=calculateStats();return/*#__PURE__*/_jsxs(Box,{children:[stats&&/*#__PURE__*/_jsxs(Box,{sx:{mb:3,p:2,bgcolor:'background.paper',borderRadius:1,boxShadow:1},children:[/*#__PURE__*/_jsxs(Box,{sx:{display:'flex',justifyContent:'space-between',alignItems:'center',mb:1},children:[/*#__PURE__*/_jsxs(Typography,{variant:\"subtitle1\",children:[\"Statistiche tabella cavi in\",revisioneCorrente?/*#__PURE__*/_jsxs(_Fragment,{children:[\" \",/*#__PURE__*/_jsxs(\"span\",{style:{fontWeight:'bold'},children:[\" Rev. \\\"\",revisioneCorrente,\"\\\"\"]})]}):'']}),/*#__PURE__*/_jsx(Button,{variant:\"outlined\",className:\"primary-button\",size:\"small\",startIcon:/*#__PURE__*/_jsx(ClearIcon,{fontSize:\"small\"}),onClick:()=>setResetFilters(true),sx:{minWidth:'auto',py:0},children:\"Resetta filtri\"})]}),/*#__PURE__*/_jsxs(Box,{sx:{display:'flex',flexWrap:'wrap',gap:3},children:[/*#__PURE__*/_jsxs(Box,{children:[/*#__PURE__*/_jsx(Typography,{variant:\"body2\",color:\"text.secondary\",children:\"Completamento\"}),/*#__PURE__*/_jsxs(Typography,{variant:\"h6\",children:[stats.percentualeCompletamento,\"%\"]})]}),/*#__PURE__*/_jsxs(Box,{children:[/*#__PURE__*/_jsx(Typography,{variant:\"body2\",color:\"text.secondary\",children:\"Installati\"}),/*#__PURE__*/_jsx(Typography,{variant:\"h6\",color:\"success.main\",children:stats.installati})]}),/*#__PURE__*/_jsxs(Box,{children:[/*#__PURE__*/_jsx(Typography,{variant:\"body2\",color:\"text.secondary\",children:\"In corso\"}),/*#__PURE__*/_jsx(Typography,{variant:\"h6\",color:\"warning.main\",children:stats.inCorso})]}),/*#__PURE__*/_jsxs(Box,{children:[/*#__PURE__*/_jsx(Typography,{variant:\"body2\",color:\"text.secondary\",children:\"Da installare\"}),/*#__PURE__*/_jsx(Typography,{variant:\"h6\",color:\"error.main\",children:stats.daInstallare})]}),/*#__PURE__*/_jsxs(Box,{children:[/*#__PURE__*/_jsx(Typography,{variant:\"body2\",color:\"text.secondary\",children:\"Metri teorici\"}),/*#__PURE__*/_jsxs(Typography,{variant:\"h6\",children:[stats.metriTeoriciTotali.toFixed(1),\" m\"]})]}),/*#__PURE__*/_jsxs(Box,{children:[/*#__PURE__*/_jsx(Typography,{variant:\"body2\",color:\"text.secondary\",children:\"Metri reali\"}),/*#__PURE__*/_jsxs(Typography,{variant:\"h6\",children:[stats.metriRealiTotali.toFixed(1),\" m\"]})]})]})]}),/*#__PURE__*/_jsx(FilterableTable,{data:cavi,columns:columns,onFilteredDataChange:handleFilteredDataChange,loading:loading,emptyMessage:\"Nessun cavo disponibile\",renderRow:renderRow,onResetFilters:handleResetFilters},resetFilters?'reset':'normal')]});};export default CaviFilterableTable;", "map": {"version": 3, "names": ["React", "useState", "useEffect", "Box", "Typography", "Chip", "TableRow", "TableCell", "<PERSON><PERSON>", "Clear", "ClearIcon", "FilterableTable", "formatDate", "jsx", "_jsx", "jsxs", "_jsxs", "Fragment", "_Fragment", "CaviFilterableTable", "_ref", "cavi", "loading", "onFilteredDataChange", "revisioneCorrente", "filteredCavi", "setFilteredCavi", "resetFilters", "setResetFilters", "handleFilteredDataChange", "data", "handleResetFilters", "columns", "field", "headerName", "dataType", "headerStyle", "fontWeight", "align", "cellStyle", "textAlign", "renderCell", "row", "metri_te<PERSON>ci", "toFixed", "metratura_reale", "color", "stato_installazione", "label", "size", "variant", "id_bobina", "match", "timestamp", "colle<PERSON>nti", "renderRow", "index", "bgColor", "sx", "backgroundColor", "children", "map", "column", "calculateStats", "length", "totalCavi", "installati", "filter", "c", "inCorso", "daInstallare", "metriTeoriciTotali", "reduce", "sum", "metriRealiTotali", "percentualeCompletamento", "Math", "round", "stats", "mb", "p", "bgcolor", "borderRadius", "boxShadow", "display", "justifyContent", "alignItems", "style", "className", "startIcon", "fontSize", "onClick", "min<PERSON><PERSON><PERSON>", "py", "flexWrap", "gap", "emptyMessage", "onResetFilters"], "sources": ["C:/Users/<USER>/PycharmProjects/CMS/webapp/frontend/src/components/cavi/CaviFilterableTable.js"], "sourcesContent": ["import React, { useState, useEffect } from 'react';\nimport { Box, Typography, Chip, TableRow, TableCell, Button } from '@mui/material';\nimport { Clear as ClearIcon } from '@mui/icons-material';\nimport FilterableTable from '../common/FilterableTable';\nimport { formatDate } from '../../utils/dateUtils';\n\n/**\n * Componente per visualizzare la lista dei cavi con filtri in stile Excel\n *\n * @param {Object} props - Proprietà del componente\n * @param {Array} props.cavi - Lista dei cavi da visualizzare\n * @param {boolean} props.loading - Indica se i dati sono in caricamento\n * @param {Function} props.onFilteredDataChange - Funzione chiamata quando i dati filtrati cambiano\n * @param {string} props.revisioneCorrente - Revisione corrente da mostrare nelle statistiche\n */\nconst CaviFilterableTable = ({ cavi = [], loading = false, onFilteredDataChange = null, revisioneCorrente = null }) => {\n  const [filteredCavi, setFilteredCavi] = useState(cavi);\n  const [resetFilters, setResetFilters] = useState(false);\n\n  // Aggiorna i dati filtrati quando cambiano i cavi\n  useEffect(() => {\n    setFilteredCavi(cavi);\n  }, [cavi]);\n\n  // Notifica il componente padre quando cambiano i dati filtrati\n  const handleFilteredDataChange = (data) => {\n    setFilteredCavi(data);\n    if (onFilteredDataChange) {\n      onFilteredDataChange(data);\n    }\n  };\n\n  // Gestisce il reset dei filtri\n  const handleResetFilters = () => {\n    setResetFilters(false);\n  };\n\n  // Definizione delle colonne\n  const columns = [\n    {\n      field: 'id_cavo',\n      headerName: 'ID Cavo',\n      dataType: 'text',\n      headerStyle: { fontWeight: 'bold' }\n    },\n    // Colonna Revisione rimossa e spostata nella tabella delle statistiche\n    {\n      field: 'sistema',\n      headerName: 'Sistema',\n      dataType: 'text'\n    },\n    {\n      field: 'utility',\n      headerName: 'Utility',\n      dataType: 'text'\n    },\n    {\n      field: 'tipologia',\n      headerName: 'Tipologia',\n      dataType: 'text'\n    },\n    // n_conduttori field is now a spare field (kept in DB but hidden in UI)\n    {\n      field: 'sezione',\n      headerName: 'Formazione',\n      dataType: 'text',\n      align: 'right',\n      cellStyle: { textAlign: 'right' }\n    },\n    {\n      field: 'metri_teorici',\n      headerName: 'Metri Teorici',\n      dataType: 'number',\n      align: 'right',\n      cellStyle: { textAlign: 'right' },\n      renderCell: (row) => row.metri_teorici ? row.metri_teorici.toFixed(1) : '0'\n    },\n    {\n      field: 'metratura_reale',\n      headerName: 'Metri Reali',\n      dataType: 'number',\n      align: 'right',\n      cellStyle: { textAlign: 'right' },\n      renderCell: (row) => row.metratura_reale ? row.metratura_reale.toFixed(1) : '0'\n    },\n    {\n      field: 'stato_installazione',\n      headerName: 'Stato',\n      dataType: 'text',\n      renderCell: (row) => {\n        let color = 'default';\n        if (row.stato_installazione === 'INSTALLATO') color = 'success';\n        else if (row.stato_installazione === 'IN_CORSO') color = 'warning';\n        else if (row.stato_installazione === 'DA_INSTALLARE') color = 'error';\n\n        return (\n          <Chip\n            label={row.stato_installazione || 'N/D'}\n            size=\"small\"\n            color={color}\n            variant=\"outlined\"\n          />\n        );\n      }\n    },\n    {\n      field: 'id_bobina',\n      headerName: 'Bobina',\n      dataType: 'text',\n      renderCell: (row) => {\n        // Gestione differenziata per null e BOBINA_VUOTA\n        if (row.id_bobina === null) {\n          // Per cavi non posati (id_bobina è null)\n          return '-';\n        } else if (row.id_bobina === 'BOBINA_VUOTA') {\n          // Per cavi posati senza bobina specifica\n          return 'BOBINA VUOTA';\n        } else if (!row.id_bobina) {\n          // Per altri casi in cui id_bobina è falsy (undefined, stringa vuota)\n          return '-';\n        }\n\n        // Estrai solo il numero della bobina (parte dopo '_B')\n        const match = row.id_bobina.match(/_B(.+)$/);\n        return match ? match[1] : row.id_bobina;\n      }\n    },\n    {\n      field: 'timestamp',\n      headerName: 'Data Modifica',\n      dataType: 'date',\n      renderCell: (row) => formatDate(row.timestamp)\n    },\n    {\n      field: 'collegamenti',\n      headerName: 'Collegamenti',\n      dataType: 'number',\n      align: 'center',\n      cellStyle: { textAlign: 'center' },\n      renderCell: (row) => {\n        let color = 'default';\n        if (row.collegamenti === 2) color = 'success';\n        else if (row.collegamenti === 1) color = 'warning';\n        else color = 'error';\n\n        return (\n          <Chip\n            label={row.collegamenti}\n            size=\"small\"\n            color={color}\n            variant=\"outlined\"\n          />\n        );\n      }\n    }\n  ];\n\n  // Renderizza una riga personalizzata\n  const renderRow = (row, index) => {\n    // Determina il colore di sfondo in base allo stato\n    let bgColor = 'inherit';\n    if (row.stato_installazione === 'INSTALLATO') bgColor = 'rgba(76, 175, 80, 0.1)';\n    else if (row.stato_installazione === 'IN_CORSO') bgColor = 'rgba(255, 152, 0, 0.1)';\n\n    return (\n      <TableRow\n        key={index}\n        sx={{\n          backgroundColor: bgColor,\n          '&:hover': { backgroundColor: 'rgba(0, 0, 0, 0.04)' }\n        }}\n      >\n        {columns.map((column) => (\n          <TableCell\n            key={column.field}\n            align={column.align || 'left'}\n            sx={column.cellStyle}\n          >\n            {column.renderCell ? column.renderCell(row) : row[column.field]}\n          </TableCell>\n        ))}\n      </TableRow>\n    );\n  };\n\n  // Calcola le statistiche\n  const calculateStats = () => {\n    if (!filteredCavi.length) return null;\n\n    const totalCavi = filteredCavi.length;\n    const installati = filteredCavi.filter(c => c.stato_installazione === 'INSTALLATO').length;\n    const inCorso = filteredCavi.filter(c => c.stato_installazione === 'IN_CORSO').length;\n    const daInstallare = filteredCavi.filter(c => c.stato_installazione === 'DA_INSTALLARE').length;\n\n    const metriTeoriciTotali = filteredCavi.reduce((sum, c) => sum + (c.metri_teorici || 0), 0);\n    const metriRealiTotali = filteredCavi.reduce((sum, c) => sum + (c.metratura_reale || 0), 0);\n\n    const percentualeCompletamento = totalCavi ? Math.round((installati / totalCavi) * 100) : 0;\n\n    return {\n      totalCavi,\n      installati,\n      inCorso,\n      daInstallare,\n      metriTeoriciTotali,\n      metriRealiTotali,\n      percentualeCompletamento\n    };\n  };\n\n  const stats = calculateStats();\n\n  return (\n    <Box>\n      {stats && (\n        <Box sx={{ mb: 3, p: 2, bgcolor: 'background.paper', borderRadius: 1, boxShadow: 1 }}>\n          <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 1 }}>\n            <Typography variant=\"subtitle1\">\n              Statistiche tabella cavi in{revisioneCorrente ? (\n                <> <span style={{ fontWeight: 'bold' }}> Rev. \"{revisioneCorrente}\"</span></>\n              ) : ''}\n            </Typography>\n            <Button\n              variant=\"outlined\"\n              className=\"primary-button\"\n              size=\"small\"\n              startIcon={<ClearIcon fontSize=\"small\" />}\n              onClick={() => setResetFilters(true)}\n              sx={{ minWidth: 'auto', py: 0 }}\n            >\n              Resetta filtri\n            </Button>\n          </Box>\n          <Box sx={{ display: 'flex', flexWrap: 'wrap', gap: 3 }}>\n            <Box>\n              <Typography variant=\"body2\" color=\"text.secondary\">Completamento</Typography>\n              <Typography variant=\"h6\">{stats.percentualeCompletamento}%</Typography>\n            </Box>\n            <Box>\n              <Typography variant=\"body2\" color=\"text.secondary\">Installati</Typography>\n              <Typography variant=\"h6\" color=\"success.main\">{stats.installati}</Typography>\n            </Box>\n            <Box>\n              <Typography variant=\"body2\" color=\"text.secondary\">In corso</Typography>\n              <Typography variant=\"h6\" color=\"warning.main\">{stats.inCorso}</Typography>\n            </Box>\n            <Box>\n              <Typography variant=\"body2\" color=\"text.secondary\">Da installare</Typography>\n              <Typography variant=\"h6\" color=\"error.main\">{stats.daInstallare}</Typography>\n            </Box>\n            <Box>\n              <Typography variant=\"body2\" color=\"text.secondary\">Metri teorici</Typography>\n              <Typography variant=\"h6\">{stats.metriTeoriciTotali.toFixed(1)} m</Typography>\n            </Box>\n            <Box>\n              <Typography variant=\"body2\" color=\"text.secondary\">Metri reali</Typography>\n              <Typography variant=\"h6\">{stats.metriRealiTotali.toFixed(1)} m</Typography>\n            </Box>\n          </Box>\n        </Box>\n      )}\n\n      <FilterableTable\n        data={cavi}\n        columns={columns}\n        onFilteredDataChange={handleFilteredDataChange}\n        loading={loading}\n        emptyMessage=\"Nessun cavo disponibile\"\n        renderRow={renderRow}\n        onResetFilters={handleResetFilters}\n        key={resetFilters ? 'reset' : 'normal'} // Forza il re-render quando resetFilters cambia\n      />\n    </Box>\n  );\n};\n\nexport default CaviFilterableTable;\n"], "mappings": "AAAA,MAAO,CAAAA,KAAK,EAAIC,QAAQ,CAAEC,SAAS,KAAQ,OAAO,CAClD,OAASC,GAAG,CAAEC,UAAU,CAAEC,IAAI,CAAEC,QAAQ,CAAEC,SAAS,CAAEC,MAAM,KAAQ,eAAe,CAClF,OAASC,KAAK,GAAI,CAAAC,SAAS,KAAQ,qBAAqB,CACxD,MAAO,CAAAC,eAAe,KAAM,2BAA2B,CACvD,OAASC,UAAU,KAAQ,uBAAuB,CAElD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,GARA,OAAAC,GAAA,IAAAC,IAAA,CAAAC,IAAA,IAAAC,KAAA,CAAAC,QAAA,IAAAC,SAAA,yBASA,KAAM,CAAAC,mBAAmB,CAAGC,IAAA,EAA2F,IAA1F,CAAEC,IAAI,CAAG,EAAE,CAAEC,OAAO,CAAG,KAAK,CAAEC,oBAAoB,CAAG,IAAI,CAAEC,iBAAiB,CAAG,IAAK,CAAC,CAAAJ,IAAA,CAChH,KAAM,CAACK,YAAY,CAAEC,eAAe,CAAC,CAAGzB,QAAQ,CAACoB,IAAI,CAAC,CACtD,KAAM,CAACM,YAAY,CAAEC,eAAe,CAAC,CAAG3B,QAAQ,CAAC,KAAK,CAAC,CAEvD;AACAC,SAAS,CAAC,IAAM,CACdwB,eAAe,CAACL,IAAI,CAAC,CACvB,CAAC,CAAE,CAACA,IAAI,CAAC,CAAC,CAEV;AACA,KAAM,CAAAQ,wBAAwB,CAAIC,IAAI,EAAK,CACzCJ,eAAe,CAACI,IAAI,CAAC,CACrB,GAAIP,oBAAoB,CAAE,CACxBA,oBAAoB,CAACO,IAAI,CAAC,CAC5B,CACF,CAAC,CAED;AACA,KAAM,CAAAC,kBAAkB,CAAGA,CAAA,GAAM,CAC/BH,eAAe,CAAC,KAAK,CAAC,CACxB,CAAC,CAED;AACA,KAAM,CAAAI,OAAO,CAAG,CACd,CACEC,KAAK,CAAE,SAAS,CAChBC,UAAU,CAAE,SAAS,CACrBC,QAAQ,CAAE,MAAM,CAChBC,WAAW,CAAE,CAAEC,UAAU,CAAE,MAAO,CACpC,CAAC,CACD;AACA,CACEJ,KAAK,CAAE,SAAS,CAChBC,UAAU,CAAE,SAAS,CACrBC,QAAQ,CAAE,MACZ,CAAC,CACD,CACEF,KAAK,CAAE,SAAS,CAChBC,UAAU,CAAE,SAAS,CACrBC,QAAQ,CAAE,MACZ,CAAC,CACD,CACEF,KAAK,CAAE,WAAW,CAClBC,UAAU,CAAE,WAAW,CACvBC,QAAQ,CAAE,MACZ,CAAC,CACD;AACA,CACEF,KAAK,CAAE,SAAS,CAChBC,UAAU,CAAE,YAAY,CACxBC,QAAQ,CAAE,MAAM,CAChBG,KAAK,CAAE,OAAO,CACdC,SAAS,CAAE,CAAEC,SAAS,CAAE,OAAQ,CAClC,CAAC,CACD,CACEP,KAAK,CAAE,eAAe,CACtBC,UAAU,CAAE,eAAe,CAC3BC,QAAQ,CAAE,QAAQ,CAClBG,KAAK,CAAE,OAAO,CACdC,SAAS,CAAE,CAAEC,SAAS,CAAE,OAAQ,CAAC,CACjCC,UAAU,CAAGC,GAAG,EAAKA,GAAG,CAACC,aAAa,CAAGD,GAAG,CAACC,aAAa,CAACC,OAAO,CAAC,CAAC,CAAC,CAAG,GAC1E,CAAC,CACD,CACEX,KAAK,CAAE,iBAAiB,CACxBC,UAAU,CAAE,aAAa,CACzBC,QAAQ,CAAE,QAAQ,CAClBG,KAAK,CAAE,OAAO,CACdC,SAAS,CAAE,CAAEC,SAAS,CAAE,OAAQ,CAAC,CACjCC,UAAU,CAAGC,GAAG,EAAKA,GAAG,CAACG,eAAe,CAAGH,GAAG,CAACG,eAAe,CAACD,OAAO,CAAC,CAAC,CAAC,CAAG,GAC9E,CAAC,CACD,CACEX,KAAK,CAAE,qBAAqB,CAC5BC,UAAU,CAAE,OAAO,CACnBC,QAAQ,CAAE,MAAM,CAChBM,UAAU,CAAGC,GAAG,EAAK,CACnB,GAAI,CAAAI,KAAK,CAAG,SAAS,CACrB,GAAIJ,GAAG,CAACK,mBAAmB,GAAK,YAAY,CAAED,KAAK,CAAG,SAAS,CAAC,IAC3D,IAAIJ,GAAG,CAACK,mBAAmB,GAAK,UAAU,CAAED,KAAK,CAAG,SAAS,CAAC,IAC9D,IAAIJ,GAAG,CAACK,mBAAmB,GAAK,eAAe,CAAED,KAAK,CAAG,OAAO,CAErE,mBACEhC,IAAA,CAACT,IAAI,EACH2C,KAAK,CAAEN,GAAG,CAACK,mBAAmB,EAAI,KAAM,CACxCE,IAAI,CAAC,OAAO,CACZH,KAAK,CAAEA,KAAM,CACbI,OAAO,CAAC,UAAU,CACnB,CAAC,CAEN,CACF,CAAC,CACD,CACEjB,KAAK,CAAE,WAAW,CAClBC,UAAU,CAAE,QAAQ,CACpBC,QAAQ,CAAE,MAAM,CAChBM,UAAU,CAAGC,GAAG,EAAK,CACnB;AACA,GAAIA,GAAG,CAACS,SAAS,GAAK,IAAI,CAAE,CAC1B;AACA,MAAO,GAAG,CACZ,CAAC,IAAM,IAAIT,GAAG,CAACS,SAAS,GAAK,cAAc,CAAE,CAC3C;AACA,MAAO,cAAc,CACvB,CAAC,IAAM,IAAI,CAACT,GAAG,CAACS,SAAS,CAAE,CACzB;AACA,MAAO,GAAG,CACZ,CAEA;AACA,KAAM,CAAAC,KAAK,CAAGV,GAAG,CAACS,SAAS,CAACC,KAAK,CAAC,SAAS,CAAC,CAC5C,MAAO,CAAAA,KAAK,CAAGA,KAAK,CAAC,CAAC,CAAC,CAAGV,GAAG,CAACS,SAAS,CACzC,CACF,CAAC,CACD,CACElB,KAAK,CAAE,WAAW,CAClBC,UAAU,CAAE,eAAe,CAC3BC,QAAQ,CAAE,MAAM,CAChBM,UAAU,CAAGC,GAAG,EAAK9B,UAAU,CAAC8B,GAAG,CAACW,SAAS,CAC/C,CAAC,CACD,CACEpB,KAAK,CAAE,cAAc,CACrBC,UAAU,CAAE,cAAc,CAC1BC,QAAQ,CAAE,QAAQ,CAClBG,KAAK,CAAE,QAAQ,CACfC,SAAS,CAAE,CAAEC,SAAS,CAAE,QAAS,CAAC,CAClCC,UAAU,CAAGC,GAAG,EAAK,CACnB,GAAI,CAAAI,KAAK,CAAG,SAAS,CACrB,GAAIJ,GAAG,CAACY,YAAY,GAAK,CAAC,CAAER,KAAK,CAAG,SAAS,CAAC,IACzC,IAAIJ,GAAG,CAACY,YAAY,GAAK,CAAC,CAAER,KAAK,CAAG,SAAS,CAAC,IAC9C,CAAAA,KAAK,CAAG,OAAO,CAEpB,mBACEhC,IAAA,CAACT,IAAI,EACH2C,KAAK,CAAEN,GAAG,CAACY,YAAa,CACxBL,IAAI,CAAC,OAAO,CACZH,KAAK,CAAEA,KAAM,CACbI,OAAO,CAAC,UAAU,CACnB,CAAC,CAEN,CACF,CAAC,CACF,CAED;AACA,KAAM,CAAAK,SAAS,CAAGA,CAACb,GAAG,CAAEc,KAAK,GAAK,CAChC;AACA,GAAI,CAAAC,OAAO,CAAG,SAAS,CACvB,GAAIf,GAAG,CAACK,mBAAmB,GAAK,YAAY,CAAEU,OAAO,CAAG,wBAAwB,CAAC,IAC5E,IAAIf,GAAG,CAACK,mBAAmB,GAAK,UAAU,CAAEU,OAAO,CAAG,wBAAwB,CAEnF,mBACE3C,IAAA,CAACR,QAAQ,EAEPoD,EAAE,CAAE,CACFC,eAAe,CAAEF,OAAO,CACxB,SAAS,CAAE,CAAEE,eAAe,CAAE,qBAAsB,CACtD,CAAE,CAAAC,QAAA,CAED5B,OAAO,CAAC6B,GAAG,CAAEC,MAAM,eAClBhD,IAAA,CAACP,SAAS,EAER+B,KAAK,CAAEwB,MAAM,CAACxB,KAAK,EAAI,MAAO,CAC9BoB,EAAE,CAAEI,MAAM,CAACvB,SAAU,CAAAqB,QAAA,CAEpBE,MAAM,CAACrB,UAAU,CAAGqB,MAAM,CAACrB,UAAU,CAACC,GAAG,CAAC,CAAGA,GAAG,CAACoB,MAAM,CAAC7B,KAAK,CAAC,EAJ1D6B,MAAM,CAAC7B,KAKH,CACZ,CAAC,EAdGuB,KAeG,CAAC,CAEf,CAAC,CAED;AACA,KAAM,CAAAO,cAAc,CAAGA,CAAA,GAAM,CAC3B,GAAI,CAACtC,YAAY,CAACuC,MAAM,CAAE,MAAO,KAAI,CAErC,KAAM,CAAAC,SAAS,CAAGxC,YAAY,CAACuC,MAAM,CACrC,KAAM,CAAAE,UAAU,CAAGzC,YAAY,CAAC0C,MAAM,CAACC,CAAC,EAAIA,CAAC,CAACrB,mBAAmB,GAAK,YAAY,CAAC,CAACiB,MAAM,CAC1F,KAAM,CAAAK,OAAO,CAAG5C,YAAY,CAAC0C,MAAM,CAACC,CAAC,EAAIA,CAAC,CAACrB,mBAAmB,GAAK,UAAU,CAAC,CAACiB,MAAM,CACrF,KAAM,CAAAM,YAAY,CAAG7C,YAAY,CAAC0C,MAAM,CAACC,CAAC,EAAIA,CAAC,CAACrB,mBAAmB,GAAK,eAAe,CAAC,CAACiB,MAAM,CAE/F,KAAM,CAAAO,kBAAkB,CAAG9C,YAAY,CAAC+C,MAAM,CAAC,CAACC,GAAG,CAAEL,CAAC,GAAKK,GAAG,EAAIL,CAAC,CAACzB,aAAa,EAAI,CAAC,CAAC,CAAE,CAAC,CAAC,CAC3F,KAAM,CAAA+B,gBAAgB,CAAGjD,YAAY,CAAC+C,MAAM,CAAC,CAACC,GAAG,CAAEL,CAAC,GAAKK,GAAG,EAAIL,CAAC,CAACvB,eAAe,EAAI,CAAC,CAAC,CAAE,CAAC,CAAC,CAE3F,KAAM,CAAA8B,wBAAwB,CAAGV,SAAS,CAAGW,IAAI,CAACC,KAAK,CAAEX,UAAU,CAAGD,SAAS,CAAI,GAAG,CAAC,CAAG,CAAC,CAE3F,MAAO,CACLA,SAAS,CACTC,UAAU,CACVG,OAAO,CACPC,YAAY,CACZC,kBAAkB,CAClBG,gBAAgB,CAChBC,wBACF,CAAC,CACH,CAAC,CAED,KAAM,CAAAG,KAAK,CAAGf,cAAc,CAAC,CAAC,CAE9B,mBACE/C,KAAA,CAACb,GAAG,EAAAyD,QAAA,EACDkB,KAAK,eACJ9D,KAAA,CAACb,GAAG,EAACuD,EAAE,CAAE,CAAEqB,EAAE,CAAE,CAAC,CAAEC,CAAC,CAAE,CAAC,CAAEC,OAAO,CAAE,kBAAkB,CAAEC,YAAY,CAAE,CAAC,CAAEC,SAAS,CAAE,CAAE,CAAE,CAAAvB,QAAA,eACnF5C,KAAA,CAACb,GAAG,EAACuD,EAAE,CAAE,CAAE0B,OAAO,CAAE,MAAM,CAAEC,cAAc,CAAE,eAAe,CAAEC,UAAU,CAAE,QAAQ,CAAEP,EAAE,CAAE,CAAE,CAAE,CAAAnB,QAAA,eACzF5C,KAAA,CAACZ,UAAU,EAAC8C,OAAO,CAAC,WAAW,CAAAU,QAAA,EAAC,6BACH,CAACpC,iBAAiB,cAC3CR,KAAA,CAAAE,SAAA,EAAA0C,QAAA,EAAE,GAAC,cAAA5C,KAAA,SAAMuE,KAAK,CAAE,CAAElD,UAAU,CAAE,MAAO,CAAE,CAAAuB,QAAA,EAAC,UAAO,CAACpC,iBAAiB,CAAC,IAAC,EAAM,CAAC,EAAE,CAAC,CAC3E,EAAE,EACI,CAAC,cACbV,IAAA,CAACN,MAAM,EACL0C,OAAO,CAAC,UAAU,CAClBsC,SAAS,CAAC,gBAAgB,CAC1BvC,IAAI,CAAC,OAAO,CACZwC,SAAS,cAAE3E,IAAA,CAACJ,SAAS,EAACgF,QAAQ,CAAC,OAAO,CAAE,CAAE,CAC1CC,OAAO,CAAEA,CAAA,GAAM/D,eAAe,CAAC,IAAI,CAAE,CACrC8B,EAAE,CAAE,CAAEkC,QAAQ,CAAE,MAAM,CAAEC,EAAE,CAAE,CAAE,CAAE,CAAAjC,QAAA,CACjC,gBAED,CAAQ,CAAC,EACN,CAAC,cACN5C,KAAA,CAACb,GAAG,EAACuD,EAAE,CAAE,CAAE0B,OAAO,CAAE,MAAM,CAAEU,QAAQ,CAAE,MAAM,CAAEC,GAAG,CAAE,CAAE,CAAE,CAAAnC,QAAA,eACrD5C,KAAA,CAACb,GAAG,EAAAyD,QAAA,eACF9C,IAAA,CAACV,UAAU,EAAC8C,OAAO,CAAC,OAAO,CAACJ,KAAK,CAAC,gBAAgB,CAAAc,QAAA,CAAC,eAAa,CAAY,CAAC,cAC7E5C,KAAA,CAACZ,UAAU,EAAC8C,OAAO,CAAC,IAAI,CAAAU,QAAA,EAAEkB,KAAK,CAACH,wBAAwB,CAAC,GAAC,EAAY,CAAC,EACpE,CAAC,cACN3D,KAAA,CAACb,GAAG,EAAAyD,QAAA,eACF9C,IAAA,CAACV,UAAU,EAAC8C,OAAO,CAAC,OAAO,CAACJ,KAAK,CAAC,gBAAgB,CAAAc,QAAA,CAAC,YAAU,CAAY,CAAC,cAC1E9C,IAAA,CAACV,UAAU,EAAC8C,OAAO,CAAC,IAAI,CAACJ,KAAK,CAAC,cAAc,CAAAc,QAAA,CAAEkB,KAAK,CAACZ,UAAU,CAAa,CAAC,EAC1E,CAAC,cACNlD,KAAA,CAACb,GAAG,EAAAyD,QAAA,eACF9C,IAAA,CAACV,UAAU,EAAC8C,OAAO,CAAC,OAAO,CAACJ,KAAK,CAAC,gBAAgB,CAAAc,QAAA,CAAC,UAAQ,CAAY,CAAC,cACxE9C,IAAA,CAACV,UAAU,EAAC8C,OAAO,CAAC,IAAI,CAACJ,KAAK,CAAC,cAAc,CAAAc,QAAA,CAAEkB,KAAK,CAACT,OAAO,CAAa,CAAC,EACvE,CAAC,cACNrD,KAAA,CAACb,GAAG,EAAAyD,QAAA,eACF9C,IAAA,CAACV,UAAU,EAAC8C,OAAO,CAAC,OAAO,CAACJ,KAAK,CAAC,gBAAgB,CAAAc,QAAA,CAAC,eAAa,CAAY,CAAC,cAC7E9C,IAAA,CAACV,UAAU,EAAC8C,OAAO,CAAC,IAAI,CAACJ,KAAK,CAAC,YAAY,CAAAc,QAAA,CAAEkB,KAAK,CAACR,YAAY,CAAa,CAAC,EAC1E,CAAC,cACNtD,KAAA,CAACb,GAAG,EAAAyD,QAAA,eACF9C,IAAA,CAACV,UAAU,EAAC8C,OAAO,CAAC,OAAO,CAACJ,KAAK,CAAC,gBAAgB,CAAAc,QAAA,CAAC,eAAa,CAAY,CAAC,cAC7E5C,KAAA,CAACZ,UAAU,EAAC8C,OAAO,CAAC,IAAI,CAAAU,QAAA,EAAEkB,KAAK,CAACP,kBAAkB,CAAC3B,OAAO,CAAC,CAAC,CAAC,CAAC,IAAE,EAAY,CAAC,EAC1E,CAAC,cACN5B,KAAA,CAACb,GAAG,EAAAyD,QAAA,eACF9C,IAAA,CAACV,UAAU,EAAC8C,OAAO,CAAC,OAAO,CAACJ,KAAK,CAAC,gBAAgB,CAAAc,QAAA,CAAC,aAAW,CAAY,CAAC,cAC3E5C,KAAA,CAACZ,UAAU,EAAC8C,OAAO,CAAC,IAAI,CAAAU,QAAA,EAAEkB,KAAK,CAACJ,gBAAgB,CAAC9B,OAAO,CAAC,CAAC,CAAC,CAAC,IAAE,EAAY,CAAC,EACxE,CAAC,EACH,CAAC,EACH,CACN,cAED9B,IAAA,CAACH,eAAe,EACdmB,IAAI,CAAET,IAAK,CACXW,OAAO,CAAEA,OAAQ,CACjBT,oBAAoB,CAAEM,wBAAyB,CAC/CP,OAAO,CAAEA,OAAQ,CACjB0E,YAAY,CAAC,yBAAyB,CACtCzC,SAAS,CAAEA,SAAU,CACrB0C,cAAc,CAAElE,kBAAmB,EAC9BJ,YAAY,CAAG,OAAO,CAAG,QAC/B,CAAC,EACC,CAAC,CAEV,CAAC,CAED,cAAe,CAAAR,mBAAmB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}