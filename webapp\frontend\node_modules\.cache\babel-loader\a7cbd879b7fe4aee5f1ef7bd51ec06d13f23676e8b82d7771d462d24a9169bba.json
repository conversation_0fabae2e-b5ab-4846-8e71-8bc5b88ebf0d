{"ast": null, "code": "import requiredArgs from \"../_lib/requiredArgs/index.js\";\nimport previousDay from \"../previousDay/index.js\";\n/**\n * @name previousMonday\n * @category Weekday Helpers\n * @summary When is the previous Monday?\n *\n * @description\n * When is the previous Monday?\n *\n * @param {Date | number} date - the date to start counting from\n * @returns {Date} the previous Monday\n * @throws {TypeError} 1 argument required\n *\n * @example\n * // When is the previous Monday before Jun, 18, 2021?\n * const result = previousMonday(new Date(2021, 5, 18))\n * //=> Mon June 14 2021 00:00:00\n */\nexport default function previousMonday(date) {\n  requiredArgs(1, arguments);\n  return previousDay(date, 1);\n}", "map": {"version": 3, "names": ["requiredArgs", "previousDay", "previousMonday", "date", "arguments"], "sources": ["C:/CMS/webapp/frontend/node_modules/date-fns/esm/previousMonday/index.js"], "sourcesContent": ["import requiredArgs from \"../_lib/requiredArgs/index.js\";\nimport previousDay from \"../previousDay/index.js\";\n/**\n * @name previousMonday\n * @category Weekday Helpers\n * @summary When is the previous Monday?\n *\n * @description\n * When is the previous Monday?\n *\n * @param {Date | number} date - the date to start counting from\n * @returns {Date} the previous Monday\n * @throws {TypeError} 1 argument required\n *\n * @example\n * // When is the previous Monday before Jun, 18, 2021?\n * const result = previousMonday(new Date(2021, 5, 18))\n * //=> Mon June 14 2021 00:00:00\n */\nexport default function previousMonday(date) {\n  requiredArgs(1, arguments);\n  return previousDay(date, 1);\n}"], "mappings": "AAAA,OAAOA,YAAY,MAAM,+BAA+B;AACxD,OAAOC,WAAW,MAAM,yBAAyB;AACjD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,eAAe,SAASC,cAAcA,CAACC,IAAI,EAAE;EAC3CH,YAAY,CAAC,CAAC,EAAEI,SAAS,CAAC;EAC1B,OAAOH,WAAW,CAACE,IAAI,EAAE,CAAC,CAAC;AAC7B", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}