{"ast": null, "code": "/**\n * Formatta una data in formato leggibile\n * @param {string|Date} date - Data da formattare\n * @param {boolean} includeTime - Se includere l'ora nella formattazione\n * @returns {string} - Data formattata\n */\nexport const formatDate = (date, includeTime = true) => {\n  if (!date) return '';\n  try {\n    const dateObj = new Date(date);\n\n    // Verifica se la data è valida\n    if (isNaN(dateObj.getTime())) {\n      return date;\n    }\n\n    // Formatta la data\n    const options = {\n      year: 'numeric',\n      month: '2-digit',\n      day: '2-digit',\n      ...(includeTime ? {\n        hour: '2-digit',\n        minute: '2-digit'\n      } : {})\n    };\n    return dateObj.toLocaleDateString('it-IT', options);\n  } catch (error) {\n    console.error('Errore nella formattazione della data:', error);\n    return date;\n  }\n};\n\n/**\n * Converte una data in formato ISO\n * @param {string|Date} date - Data da convertire\n * @returns {string} - Data in formato ISO\n */\nexport const toISODate = date => {\n  if (!date) return '';\n  try {\n    const dateObj = new Date(date);\n\n    // Verifica se la data è valida\n    if (isNaN(dateObj.getTime())) {\n      return '';\n    }\n    return dateObj.toISOString().split('T')[0];\n  } catch (error) {\n    console.error('Errore nella conversione della data in ISO:', error);\n    return '';\n  }\n};\n\n/**\n * Verifica se una data è valida\n * @param {string|Date} date - Data da verificare\n * @returns {boolean} - True se la data è valida, false altrimenti\n */\nexport const isValidDate = date => {\n  if (!date) return false;\n  try {\n    const dateObj = new Date(date);\n    return !isNaN(dateObj.getTime());\n  } catch (error) {\n    return false;\n  }\n};", "map": {"version": 3, "names": ["formatDate", "date", "includeTime", "date<PERSON><PERSON>j", "Date", "isNaN", "getTime", "options", "year", "month", "day", "hour", "minute", "toLocaleDateString", "error", "console", "toISODate", "toISOString", "split", "isValidDate"], "sources": ["C:/Users/<USER>/PycharmProjects/CMS/webapp/frontend/src/utils/dateUtils.js"], "sourcesContent": ["/**\n * Formatta una data in formato leggibile\n * @param {string|Date} date - Data da formattare\n * @param {boolean} includeTime - Se includere l'ora nella formattazione\n * @returns {string} - Data formattata\n */\nexport const formatDate = (date, includeTime = true) => {\n  if (!date) return '';\n  \n  try {\n    const dateObj = new Date(date);\n    \n    // Verifica se la data è valida\n    if (isNaN(dateObj.getTime())) {\n      return date;\n    }\n    \n    // Formatta la data\n    const options = {\n      year: 'numeric',\n      month: '2-digit',\n      day: '2-digit',\n      ...(includeTime ? { hour: '2-digit', minute: '2-digit' } : {})\n    };\n    \n    return dateObj.toLocaleDateString('it-IT', options);\n  } catch (error) {\n    console.error('Errore nella formattazione della data:', error);\n    return date;\n  }\n};\n\n/**\n * Converte una data in formato ISO\n * @param {string|Date} date - Data da convertire\n * @returns {string} - Data in formato ISO\n */\nexport const toISODate = (date) => {\n  if (!date) return '';\n  \n  try {\n    const dateObj = new Date(date);\n    \n    // Verifica se la data è valida\n    if (isNaN(dateObj.getTime())) {\n      return '';\n    }\n    \n    return dateObj.toISOString().split('T')[0];\n  } catch (error) {\n    console.error('Errore nella conversione della data in ISO:', error);\n    return '';\n  }\n};\n\n/**\n * Verifica se una data è valida\n * @param {string|Date} date - Data da verificare\n * @returns {boolean} - True se la data è valida, false altrimenti\n */\nexport const isValidDate = (date) => {\n  if (!date) return false;\n  \n  try {\n    const dateObj = new Date(date);\n    return !isNaN(dateObj.getTime());\n  } catch (error) {\n    return false;\n  }\n};\n"], "mappings": "AAAA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO,MAAMA,UAAU,GAAGA,CAACC,IAAI,EAAEC,WAAW,GAAG,IAAI,KAAK;EACtD,IAAI,CAACD,IAAI,EAAE,OAAO,EAAE;EAEpB,IAAI;IACF,MAAME,OAAO,GAAG,IAAIC,IAAI,CAACH,IAAI,CAAC;;IAE9B;IACA,IAAII,KAAK,CAACF,OAAO,CAACG,OAAO,CAAC,CAAC,CAAC,EAAE;MAC5B,OAAOL,IAAI;IACb;;IAEA;IACA,MAAMM,OAAO,GAAG;MACdC,IAAI,EAAE,SAAS;MACfC,KAAK,EAAE,SAAS;MAChBC,GAAG,EAAE,SAAS;MACd,IAAIR,WAAW,GAAG;QAAES,IAAI,EAAE,SAAS;QAAEC,MAAM,EAAE;MAAU,CAAC,GAAG,CAAC,CAAC;IAC/D,CAAC;IAED,OAAOT,OAAO,CAACU,kBAAkB,CAAC,OAAO,EAAEN,OAAO,CAAC;EACrD,CAAC,CAAC,OAAOO,KAAK,EAAE;IACdC,OAAO,CAACD,KAAK,CAAC,wCAAwC,EAAEA,KAAK,CAAC;IAC9D,OAAOb,IAAI;EACb;AACF,CAAC;;AAED;AACA;AACA;AACA;AACA;AACA,OAAO,MAAMe,SAAS,GAAIf,IAAI,IAAK;EACjC,IAAI,CAACA,IAAI,EAAE,OAAO,EAAE;EAEpB,IAAI;IACF,MAAME,OAAO,GAAG,IAAIC,IAAI,CAACH,IAAI,CAAC;;IAE9B;IACA,IAAII,KAAK,CAACF,OAAO,CAACG,OAAO,CAAC,CAAC,CAAC,EAAE;MAC5B,OAAO,EAAE;IACX;IAEA,OAAOH,OAAO,CAACc,WAAW,CAAC,CAAC,CAACC,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC;EAC5C,CAAC,CAAC,OAAOJ,KAAK,EAAE;IACdC,OAAO,CAACD,KAAK,CAAC,6CAA6C,EAAEA,KAAK,CAAC;IACnE,OAAO,EAAE;EACX;AACF,CAAC;;AAED;AACA;AACA;AACA;AACA;AACA,OAAO,MAAMK,WAAW,GAAIlB,IAAI,IAAK;EACnC,IAAI,CAACA,IAAI,EAAE,OAAO,KAAK;EAEvB,IAAI;IACF,MAAME,OAAO,GAAG,IAAIC,IAAI,CAACH,IAAI,CAAC;IAC9B,OAAO,CAACI,KAAK,CAACF,OAAO,CAACG,OAAO,CAAC,CAAC,CAAC;EAClC,CAAC,CAAC,OAAOQ,KAAK,EAAE;IACd,OAAO,KAAK;EACd;AACF,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}