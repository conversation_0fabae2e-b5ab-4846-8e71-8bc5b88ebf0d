{"ast": null, "code": "import * as React from 'react';\nimport { singleItemValueManager } from '../utils/valueManagers';\nimport { getTodayDate } from '../utils/date-utils';\nimport { SECTION_TYPE_GRANULARITY } from '../utils/getDefaultReferenceDate';\nexport const useClockReferenceDate = ({\n  value,\n  referenceDate: referenceDateProp,\n  utils,\n  props,\n  timezone\n}) => {\n  const referenceDate = React.useMemo(() => singleItemValueManager.getInitialReferenceValue({\n    value,\n    utils,\n    props,\n    referenceDate: referenceDateProp,\n    granularity: SECTION_TYPE_GRANULARITY.day,\n    timezone,\n    getTodayDate: () => getTodayDate(utils, timezone, 'date')\n  }),\n  // We only want to compute the reference date on mount.\n  [] // eslint-disable-line react-hooks/exhaustive-deps\n  );\n  return value != null ? value : referenceDate;\n};", "map": {"version": 3, "names": ["React", "singleItemValueManager", "getTodayDate", "SECTION_TYPE_GRANULARITY", "useClockReferenceDate", "value", "referenceDate", "referenceDateProp", "utils", "props", "timezone", "useMemo", "getInitialReferenceValue", "granularity", "day"], "sources": ["C:/CMS/webapp/frontend/node_modules/@mui/x-date-pickers/internals/hooks/useClockReferenceDate.js"], "sourcesContent": ["import * as React from 'react';\nimport { singleItemValueManager } from '../utils/valueManagers';\nimport { getTodayDate } from '../utils/date-utils';\nimport { SECTION_TYPE_GRANULARITY } from '../utils/getDefaultReferenceDate';\nexport const useClockReferenceDate = ({\n  value,\n  referenceDate: referenceDateProp,\n  utils,\n  props,\n  timezone\n}) => {\n  const referenceDate = React.useMemo(() => singleItemValueManager.getInitialReferenceValue({\n    value,\n    utils,\n    props,\n    referenceDate: referenceDateProp,\n    granularity: SECTION_TYPE_GRANULARITY.day,\n    timezone,\n    getTodayDate: () => getTodayDate(utils, timezone, 'date')\n  }),\n  // We only want to compute the reference date on mount.\n  [] // eslint-disable-line react-hooks/exhaustive-deps\n  );\n  return value != null ? value : referenceDate;\n};"], "mappings": "AAAA,OAAO,KAAKA,KAAK,MAAM,OAAO;AAC9B,SAASC,sBAAsB,QAAQ,wBAAwB;AAC/D,SAASC,YAAY,QAAQ,qBAAqB;AAClD,SAASC,wBAAwB,QAAQ,kCAAkC;AAC3E,OAAO,MAAMC,qBAAqB,GAAGA,CAAC;EACpCC,KAAK;EACLC,aAAa,EAAEC,iBAAiB;EAChCC,KAAK;EACLC,KAAK;EACLC;AACF,CAAC,KAAK;EACJ,MAAMJ,aAAa,GAAGN,KAAK,CAACW,OAAO,CAAC,MAAMV,sBAAsB,CAACW,wBAAwB,CAAC;IACxFP,KAAK;IACLG,KAAK;IACLC,KAAK;IACLH,aAAa,EAAEC,iBAAiB;IAChCM,WAAW,EAAEV,wBAAwB,CAACW,GAAG;IACzCJ,QAAQ;IACRR,YAAY,EAAEA,CAAA,KAAMA,YAAY,CAACM,KAAK,EAAEE,QAAQ,EAAE,MAAM;EAC1D,CAAC,CAAC;EACF;EACA,EAAE,CAAC;EACH,CAAC;EACD,OAAOL,KAAK,IAAI,IAAI,GAAGA,KAAK,GAAGC,aAAa;AAC9C,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}