{"ast": null, "code": "import { addWeeks } from \"./addWeeks.mjs\";\nimport { startOfWeek } from \"./startOfWeek.mjs\";\nimport { toDate } from \"./toDate.mjs\";\n\n/**\n * The {@link eachWeekOfInterval} function options.\n */\n\n/**\n * @name eachWeekOfInterval\n * @category Interval Helpers\n * @summary Return the array of weeks within the specified time interval.\n *\n * @description\n * Return the array of weeks within the specified time interval.\n *\n * @typeParam DateType - The `Date` type, the function operates on. Gets inferred from passed arguments. Allows to use extensions like [`UTCDate`](https://github.com/date-fns/utc).\n *\n * @param interval - The interval.\n * @param options - An object with options.\n *\n * @returns The array with starts of weeks from the week of the interval start to the week of the interval end\n *\n * @example\n * // Each week within interval 6 October 2014 - 23 November 2014:\n * const result = eachWeekOfInterval({\n *   start: new Date(2014, 9, 6),\n *   end: new Date(2014, 10, 23)\n * })\n * //=> [\n * //   Sun Oct 05 2014 00:00:00,\n * //   Sun Oct 12 2014 00:00:00,\n * //   Sun Oct 19 2014 00:00:00,\n * //   Sun Oct 26 2014 00:00:00,\n * //   Sun Nov 02 2014 00:00:00,\n * //   Sun Nov 09 2014 00:00:00,\n * //   Sun Nov 16 2014 00:00:00,\n * //   Sun Nov 23 2014 00:00:00\n * // ]\n */\nexport function eachWeekOfInterval(interval, options) {\n  const startDate = toDate(interval.start);\n  const endDate = toDate(interval.end);\n  let reversed = +startDate > +endDate;\n  const startDateWeek = reversed ? startOfWeek(endDate, options) : startOfWeek(startDate, options);\n  const endDateWeek = reversed ? startOfWeek(startDate, options) : startOfWeek(endDate, options);\n\n  // Some timezones switch DST at midnight, making start of day unreliable in these timezones, 3pm is a safe bet\n  startDateWeek.setHours(15);\n  endDateWeek.setHours(15);\n  const endTime = +endDateWeek.getTime();\n  let currentDate = startDateWeek;\n  let step = options?.step ?? 1;\n  if (!step) return [];\n  if (step < 0) {\n    step = -step;\n    reversed = !reversed;\n  }\n  const dates = [];\n  while (+currentDate <= endTime) {\n    currentDate.setHours(0);\n    dates.push(toDate(currentDate));\n    currentDate = addWeeks(currentDate, step);\n    currentDate.setHours(15);\n  }\n  return reversed ? dates.reverse() : dates;\n}\n\n// Fallback for modularized imports:\nexport default eachWeekOfInterval;", "map": {"version": 3, "names": ["addWeeks", "startOfWeek", "toDate", "eachWeekOfInterval", "interval", "options", "startDate", "start", "endDate", "end", "reversed", "startDateWeek", "endDateWeek", "setHours", "endTime", "getTime", "currentDate", "step", "dates", "push", "reverse"], "sources": ["C:/CMS/webapp/frontend/node_modules/date-fns/eachWeekOfInterval.mjs"], "sourcesContent": ["import { addWeeks } from \"./addWeeks.mjs\";\nimport { startOfWeek } from \"./startOfWeek.mjs\";\nimport { toDate } from \"./toDate.mjs\";\n\n/**\n * The {@link eachWeekOfInterval} function options.\n */\n\n/**\n * @name eachWeekOfInterval\n * @category Interval Helpers\n * @summary Return the array of weeks within the specified time interval.\n *\n * @description\n * Return the array of weeks within the specified time interval.\n *\n * @typeParam DateType - The `Date` type, the function operates on. Gets inferred from passed arguments. Allows to use extensions like [`UTCDate`](https://github.com/date-fns/utc).\n *\n * @param interval - The interval.\n * @param options - An object with options.\n *\n * @returns The array with starts of weeks from the week of the interval start to the week of the interval end\n *\n * @example\n * // Each week within interval 6 October 2014 - 23 November 2014:\n * const result = eachWeekOfInterval({\n *   start: new Date(2014, 9, 6),\n *   end: new Date(2014, 10, 23)\n * })\n * //=> [\n * //   Sun Oct 05 2014 00:00:00,\n * //   Sun Oct 12 2014 00:00:00,\n * //   Sun Oct 19 2014 00:00:00,\n * //   Sun Oct 26 2014 00:00:00,\n * //   Sun Nov 02 2014 00:00:00,\n * //   Sun Nov 09 2014 00:00:00,\n * //   Sun Nov 16 2014 00:00:00,\n * //   Sun Nov 23 2014 00:00:00\n * // ]\n */\nexport function eachWeekOfInterval(interval, options) {\n  const startDate = toDate(interval.start);\n  const endDate = toDate(interval.end);\n\n  let reversed = +startDate > +endDate;\n  const startDateWeek = reversed\n    ? startOfWeek(endDate, options)\n    : startOfWeek(startDate, options);\n  const endDateWeek = reversed\n    ? startOfWeek(startDate, options)\n    : startOfWeek(endDate, options);\n\n  // Some timezones switch DST at midnight, making start of day unreliable in these timezones, 3pm is a safe bet\n  startDateWeek.setHours(15);\n  endDateWeek.setHours(15);\n\n  const endTime = +endDateWeek.getTime();\n  let currentDate = startDateWeek;\n\n  let step = options?.step ?? 1;\n  if (!step) return [];\n  if (step < 0) {\n    step = -step;\n    reversed = !reversed;\n  }\n\n  const dates = [];\n\n  while (+currentDate <= endTime) {\n    currentDate.setHours(0);\n    dates.push(toDate(currentDate));\n    currentDate = addWeeks(currentDate, step);\n    currentDate.setHours(15);\n  }\n\n  return reversed ? dates.reverse() : dates;\n}\n\n// Fallback for modularized imports:\nexport default eachWeekOfInterval;\n"], "mappings": "AAAA,SAASA,QAAQ,QAAQ,gBAAgB;AACzC,SAASC,WAAW,QAAQ,mBAAmB;AAC/C,SAASC,MAAM,QAAQ,cAAc;;AAErC;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO,SAASC,kBAAkBA,CAACC,QAAQ,EAAEC,OAAO,EAAE;EACpD,MAAMC,SAAS,GAAGJ,MAAM,CAACE,QAAQ,CAACG,KAAK,CAAC;EACxC,MAAMC,OAAO,GAAGN,MAAM,CAACE,QAAQ,CAACK,GAAG,CAAC;EAEpC,IAAIC,QAAQ,GAAG,CAACJ,SAAS,GAAG,CAACE,OAAO;EACpC,MAAMG,aAAa,GAAGD,QAAQ,GAC1BT,WAAW,CAACO,OAAO,EAAEH,OAAO,CAAC,GAC7BJ,WAAW,CAACK,SAAS,EAAED,OAAO,CAAC;EACnC,MAAMO,WAAW,GAAGF,QAAQ,GACxBT,WAAW,CAACK,SAAS,EAAED,OAAO,CAAC,GAC/BJ,WAAW,CAACO,OAAO,EAAEH,OAAO,CAAC;;EAEjC;EACAM,aAAa,CAACE,QAAQ,CAAC,EAAE,CAAC;EAC1BD,WAAW,CAACC,QAAQ,CAAC,EAAE,CAAC;EAExB,MAAMC,OAAO,GAAG,CAACF,WAAW,CAACG,OAAO,CAAC,CAAC;EACtC,IAAIC,WAAW,GAAGL,aAAa;EAE/B,IAAIM,IAAI,GAAGZ,OAAO,EAAEY,IAAI,IAAI,CAAC;EAC7B,IAAI,CAACA,IAAI,EAAE,OAAO,EAAE;EACpB,IAAIA,IAAI,GAAG,CAAC,EAAE;IACZA,IAAI,GAAG,CAACA,IAAI;IACZP,QAAQ,GAAG,CAACA,QAAQ;EACtB;EAEA,MAAMQ,KAAK,GAAG,EAAE;EAEhB,OAAO,CAACF,WAAW,IAAIF,OAAO,EAAE;IAC9BE,WAAW,CAACH,QAAQ,CAAC,CAAC,CAAC;IACvBK,KAAK,CAACC,IAAI,CAACjB,MAAM,CAACc,WAAW,CAAC,CAAC;IAC/BA,WAAW,GAAGhB,QAAQ,CAACgB,WAAW,EAAEC,IAAI,CAAC;IACzCD,WAAW,CAACH,QAAQ,CAAC,EAAE,CAAC;EAC1B;EAEA,OAAOH,QAAQ,GAAGQ,KAAK,CAACE,OAAO,CAAC,CAAC,GAAGF,KAAK;AAC3C;;AAEA;AACA,eAAef,kBAAkB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}