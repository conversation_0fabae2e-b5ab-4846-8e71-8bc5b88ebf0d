{"ast": null, "code": "import { constructNow } from \"./constructNow.mjs\";\nimport { isSameQuarter } from \"./isSameQuarter.mjs\";\n\n/**\n * @name isThisQuarter\n * @category Quarter Helpers\n * @summary Is the given date in the same quarter as the current date?\n * @pure false\n *\n * @description\n * Is the given date in the same quarter as the current date?\n *\n * @typeParam DateType - The `Date` type, the function operates on. Gets inferred from passed arguments. Allows to use extensions like [`UTCDate`](https://github.com/date-fns/utc).\n *\n * @param date - The date to check\n *\n * @returns The date is in this quarter\n *\n * @example\n * // If today is 25 September 2014, is 2 July 2014 in this quarter?\n * const result = isThisQuarter(new Date(2014, 6, 2))\n * //=> true\n */\nexport function isThisQuarter(date) {\n  return isSameQuarter(date, constructNow(date));\n}\n\n// Fallback for modularized imports:\nexport default isThisQuarter;", "map": {"version": 3, "names": ["constructNow", "isSameQuarter", "isThisQuarter", "date"], "sources": ["C:/CMS/webapp/frontend/node_modules/date-fns/isThisQuarter.mjs"], "sourcesContent": ["import { constructNow } from \"./constructNow.mjs\";\nimport { isSameQuarter } from \"./isSameQuarter.mjs\";\n\n/**\n * @name isThisQuarter\n * @category Quarter Helpers\n * @summary Is the given date in the same quarter as the current date?\n * @pure false\n *\n * @description\n * Is the given date in the same quarter as the current date?\n *\n * @typeParam DateType - The `Date` type, the function operates on. Gets inferred from passed arguments. Allows to use extensions like [`UTCDate`](https://github.com/date-fns/utc).\n *\n * @param date - The date to check\n *\n * @returns The date is in this quarter\n *\n * @example\n * // If today is 25 September 2014, is 2 July 2014 in this quarter?\n * const result = isThisQuarter(new Date(2014, 6, 2))\n * //=> true\n */\nexport function isThisQuarter(date) {\n  return isSameQuarter(date, constructNow(date));\n}\n\n// Fallback for modularized imports:\nexport default isThisQuarter;\n"], "mappings": "AAAA,SAASA,YAAY,QAAQ,oBAAoB;AACjD,SAASC,aAAa,QAAQ,qBAAqB;;AAEnD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO,SAASC,aAAaA,CAACC,IAAI,EAAE;EAClC,OAAOF,aAAa,CAACE,IAAI,EAAEH,YAAY,CAACG,IAAI,CAAC,CAAC;AAChD;;AAEA;AACA,eAAeD,aAAa", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}