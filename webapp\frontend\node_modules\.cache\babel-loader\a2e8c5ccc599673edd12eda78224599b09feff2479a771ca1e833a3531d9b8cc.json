{"ast": null, "code": "import React from'react';import{Box,Typography,Chip}from'@mui/material';import{Construction as ConstructionIcon}from'@mui/icons-material';/**\n * Componente che mostra il cantiere selezionato\n */import{jsx as _jsx,jsxs as _jsxs}from\"react/jsx-runtime\";const SelectedCantiereDisplay=()=>{// Recupera l'ID e il nome del cantiere selezionato dal localStorage\nconst selectedCantiereId=localStorage.getItem('selectedCantiereId');const selectedCantiereName=localStorage.getItem('selectedCantiereName');// Se non c'è un cantiere selezionato, non mostrare nulla\nif(!selectedCantiereId){return null;}return/*#__PURE__*/_jsxs(Box,{sx:{display:'flex',alignItems:'center',mx:2},children:[/*#__PURE__*/_jsx(Typography,{variant:\"body2\",color:\"textSecondary\",sx:{mr:1.5,fontSize:'1rem',fontWeight:500},children:\"Cantiere attivo:\"}),/*#__PURE__*/_jsx(Chip,{icon:/*#__PURE__*/_jsx(ConstructionIcon,{fontSize:\"medium\"}),label:selectedCantiereName||selectedCantiereId,color:\"secondary\",variant:\"outlined\",size:\"medium\",sx:{fontWeight:'bold',fontSize:'1rem',padding:'6px 0',height:'36px','& .MuiChip-label':{padding:'0 14px'}}})]});};export default SelectedCantiereDisplay;", "map": {"version": 3, "names": ["React", "Box", "Typography", "Chip", "Construction", "ConstructionIcon", "jsx", "_jsx", "jsxs", "_jsxs", "SelectedCantiereDisplay", "selectedCantiereId", "localStorage", "getItem", "selectedCantiereName", "sx", "display", "alignItems", "mx", "children", "variant", "color", "mr", "fontSize", "fontWeight", "icon", "label", "size", "padding", "height"], "sources": ["C:/Users/<USER>/PycharmProjects/CMS/webapp/frontend/src/components/common/SelectedCantiereDisplay.js"], "sourcesContent": ["import React from 'react';\nimport { Box, Typography, Chip } from '@mui/material';\nimport { Construction as ConstructionIcon } from '@mui/icons-material';\n\n/**\n * Componente che mostra il cantiere selezionato\n */\nconst SelectedCantiereDisplay = () => {\n  // Recupera l'ID e il nome del cantiere selezionato dal localStorage\n  const selectedCantiereId = localStorage.getItem('selectedCantiereId');\n  const selectedCantiereName = localStorage.getItem('selectedCantiereName');\n\n  // Se non c'è un cantiere selezionato, non mostrare nulla\n  if (!selectedCantiereId) {\n    return null;\n  }\n\n  return (\n    <Box sx={{ display: 'flex', alignItems: 'center', mx: 2 }}>\n      <Typography variant=\"body2\" color=\"textSecondary\" sx={{ mr: 1.5, fontSize: '1rem', fontWeight: 500 }}>\n        Cantiere attivo:\n      </Typography>\n      <Chip\n        icon={<ConstructionIcon fontSize=\"medium\" />}\n        label={selectedCantiereName || selectedCantiereId}\n        color=\"secondary\"\n        variant=\"outlined\"\n        size=\"medium\"\n        sx={{\n          fontWeight: 'bold',\n          fontSize: '1rem',\n          padding: '6px 0',\n          height: '36px',\n          '& .MuiChip-label': { padding: '0 14px' }\n        }}\n      />\n    </Box>\n  );\n};\n\nexport default SelectedCantiereDisplay;\n"], "mappings": "AAAA,MAAO,CAAAA,KAAK,KAAM,OAAO,CACzB,OAASC,GAAG,CAAEC,UAAU,CAAEC,IAAI,KAAQ,eAAe,CACrD,OAASC,YAAY,GAAI,CAAAC,gBAAgB,KAAQ,qBAAqB,CAEtE;AACA;AACA,GAFA,OAAAC,GAAA,IAAAC,IAAA,CAAAC,IAAA,IAAAC,KAAA,yBAGA,KAAM,CAAAC,uBAAuB,CAAGA,CAAA,GAAM,CACpC;AACA,KAAM,CAAAC,kBAAkB,CAAGC,YAAY,CAACC,OAAO,CAAC,oBAAoB,CAAC,CACrE,KAAM,CAAAC,oBAAoB,CAAGF,YAAY,CAACC,OAAO,CAAC,sBAAsB,CAAC,CAEzE;AACA,GAAI,CAACF,kBAAkB,CAAE,CACvB,MAAO,KAAI,CACb,CAEA,mBACEF,KAAA,CAACR,GAAG,EAACc,EAAE,CAAE,CAAEC,OAAO,CAAE,MAAM,CAAEC,UAAU,CAAE,QAAQ,CAAEC,EAAE,CAAE,CAAE,CAAE,CAAAC,QAAA,eACxDZ,IAAA,CAACL,UAAU,EAACkB,OAAO,CAAC,OAAO,CAACC,KAAK,CAAC,eAAe,CAACN,EAAE,CAAE,CAAEO,EAAE,CAAE,GAAG,CAAEC,QAAQ,CAAE,MAAM,CAAEC,UAAU,CAAE,GAAI,CAAE,CAAAL,QAAA,CAAC,kBAEtG,CAAY,CAAC,cACbZ,IAAA,CAACJ,IAAI,EACHsB,IAAI,cAAElB,IAAA,CAACF,gBAAgB,EAACkB,QAAQ,CAAC,QAAQ,CAAE,CAAE,CAC7CG,KAAK,CAAEZ,oBAAoB,EAAIH,kBAAmB,CAClDU,KAAK,CAAC,WAAW,CACjBD,OAAO,CAAC,UAAU,CAClBO,IAAI,CAAC,QAAQ,CACbZ,EAAE,CAAE,CACFS,UAAU,CAAE,MAAM,CAClBD,QAAQ,CAAE,MAAM,CAChBK,OAAO,CAAE,OAAO,CAChBC,MAAM,CAAE,MAAM,CACd,kBAAkB,CAAE,CAAED,OAAO,CAAE,QAAS,CAC1C,CAAE,CACH,CAAC,EACC,CAAC,CAEV,CAAC,CAED,cAAe,CAAAlB,uBAAuB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}