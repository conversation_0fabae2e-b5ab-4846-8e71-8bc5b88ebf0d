{"ast": null, "code": "import toDate from \"../toDate/index.js\";\nimport requiredArgs from \"../_lib/requiredArgs/index.js\";\n/**\n * @name isFirstDayOfMonth\n * @category Month Helpers\n * @summary Is the given date the first day of a month?\n *\n * @description\n * Is the given date the first day of a month?\n *\n * @param {Date|Number} date - the date to check\n * @returns {Boolean} the date is the first day of a month\n * @throws {TypeError} 1 argument required\n *\n * @example\n * // Is 1 September 2014 the first day of a month?\n * const result = isFirstDayOfMonth(new Date(2014, 8, 1))\n * //=> true\n */\nexport default function isFirstDayOfMonth(dirtyDate) {\n  requiredArgs(1, arguments);\n  return toDate(dirtyDate).getDate() === 1;\n}", "map": {"version": 3, "names": ["toDate", "requiredArgs", "isFirstDayOfMonth", "dirtyDate", "arguments", "getDate"], "sources": ["C:/CMS/webapp/frontend/node_modules/date-fns/esm/isFirstDayOfMonth/index.js"], "sourcesContent": ["import toDate from \"../toDate/index.js\";\nimport requiredArgs from \"../_lib/requiredArgs/index.js\";\n/**\n * @name isFirstDayOfMonth\n * @category Month Helpers\n * @summary Is the given date the first day of a month?\n *\n * @description\n * Is the given date the first day of a month?\n *\n * @param {Date|Number} date - the date to check\n * @returns {Boolean} the date is the first day of a month\n * @throws {TypeError} 1 argument required\n *\n * @example\n * // Is 1 September 2014 the first day of a month?\n * const result = isFirstDayOfMonth(new Date(2014, 8, 1))\n * //=> true\n */\nexport default function isFirstDayOfMonth(dirtyDate) {\n  requiredArgs(1, arguments);\n  return toDate(dirtyDate).getDate() === 1;\n}"], "mappings": "AAAA,OAAOA,MAAM,MAAM,oBAAoB;AACvC,OAAOC,YAAY,MAAM,+BAA+B;AACxD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,eAAe,SAASC,iBAAiBA,CAACC,SAAS,EAAE;EACnDF,YAAY,CAAC,CAAC,EAAEG,SAAS,CAAC;EAC1B,OAAOJ,MAAM,CAACG,SAAS,CAAC,CAACE,OAAO,CAAC,CAAC,KAAK,CAAC;AAC1C", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}