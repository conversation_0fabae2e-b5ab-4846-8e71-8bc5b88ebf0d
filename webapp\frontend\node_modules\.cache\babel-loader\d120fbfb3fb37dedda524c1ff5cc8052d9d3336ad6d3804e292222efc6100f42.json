{"ast": null, "code": "import { formatDistance } from \"./hu/_lib/formatDistance.mjs\";\nimport { formatLong } from \"./hu/_lib/formatLong.mjs\";\nimport { formatRelative } from \"./hu/_lib/formatRelative.mjs\";\nimport { localize } from \"./hu/_lib/localize.mjs\";\nimport { match } from \"./hu/_lib/match.mjs\";\n\n/**\n * @category Locales\n * @summary Hungarian locale.\n * @language Hungarian\n * @iso-639-2 hun\n * <AUTHOR> [@pshpak](https://github.com/pshpak)\n * <AUTHOR> [@eduardopsll](https://github.com/eduardopsll)\n * <AUTHOR> [@twodcube](https://github.com/twodcube)\n */\nexport const hu = {\n  code: \"hu\",\n  formatDistance: formatDistance,\n  formatLong: formatLong,\n  formatRelative: formatRelative,\n  localize: localize,\n  match: match,\n  options: {\n    weekStartsOn: 1 /* Monday */,\n    firstWeekContainsDate: 4\n  }\n};\n\n// Fallback for modularized imports:\nexport default hu;", "map": {"version": 3, "names": ["formatDistance", "formatLong", "formatRelative", "localize", "match", "hu", "code", "options", "weekStartsOn", "firstWeekContainsDate"], "sources": ["C:/CMS/webapp/frontend/node_modules/date-fns/locale/hu.mjs"], "sourcesContent": ["import { formatDistance } from \"./hu/_lib/formatDistance.mjs\";\nimport { formatLong } from \"./hu/_lib/formatLong.mjs\";\nimport { formatRelative } from \"./hu/_lib/formatRelative.mjs\";\nimport { localize } from \"./hu/_lib/localize.mjs\";\nimport { match } from \"./hu/_lib/match.mjs\";\n\n/**\n * @category Locales\n * @summary Hungarian locale.\n * @language Hungarian\n * @iso-639-2 hun\n * <AUTHOR> [@pshpak](https://github.com/pshpak)\n * <AUTHOR> [@eduardopsll](https://github.com/eduardopsll)\n * <AUTHOR> [@twodcube](https://github.com/twodcube)\n */\nexport const hu = {\n  code: \"hu\",\n  formatDistance: formatDistance,\n  formatLong: formatLong,\n  formatRelative: formatRelative,\n  localize: localize,\n  match: match,\n  options: {\n    weekStartsOn: 1 /* Monday */,\n    firstWeekContainsDate: 4,\n  },\n};\n\n// Fallback for modularized imports:\nexport default hu;\n"], "mappings": "AAAA,SAASA,cAAc,QAAQ,8BAA8B;AAC7D,SAASC,UAAU,QAAQ,0BAA0B;AACrD,SAASC,cAAc,QAAQ,8BAA8B;AAC7D,SAASC,QAAQ,QAAQ,wBAAwB;AACjD,SAASC,KAAK,QAAQ,qBAAqB;;AAE3C;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO,MAAMC,EAAE,GAAG;EAChBC,IAAI,EAAE,IAAI;EACVN,cAAc,EAAEA,cAAc;EAC9BC,UAAU,EAAEA,UAAU;EACtBC,cAAc,EAAEA,cAAc;EAC9BC,QAAQ,EAAEA,QAAQ;EAClBC,KAAK,EAAEA,KAAK;EACZG,OAAO,EAAE;IACPC,YAAY,EAAE,CAAC,CAAC;IAChBC,qBAAqB,EAAE;EACzB;AACF,CAAC;;AAED;AACA,eAAeJ,EAAE", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}