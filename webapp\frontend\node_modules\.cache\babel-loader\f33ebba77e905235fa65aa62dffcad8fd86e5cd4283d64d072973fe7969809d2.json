{"ast": null, "code": "import { bisect } from \"d3-array\";\nimport { linearish } from \"./linear.js\";\nimport { initRange } from \"./init.js\";\nexport default function quantize() {\n  var x0 = 0,\n    x1 = 1,\n    n = 1,\n    domain = [0.5],\n    range = [0, 1],\n    unknown;\n  function scale(x) {\n    return x != null && x <= x ? range[bisect(domain, x, 0, n)] : unknown;\n  }\n  function rescale() {\n    var i = -1;\n    domain = new Array(n);\n    while (++i < n) domain[i] = ((i + 1) * x1 - (i - n) * x0) / (n + 1);\n    return scale;\n  }\n  scale.domain = function (_) {\n    return arguments.length ? ([x0, x1] = _, x0 = +x0, x1 = +x1, rescale()) : [x0, x1];\n  };\n  scale.range = function (_) {\n    return arguments.length ? (n = (range = Array.from(_)).length - 1, rescale()) : range.slice();\n  };\n  scale.invertExtent = function (y) {\n    var i = range.indexOf(y);\n    return i < 0 ? [NaN, NaN] : i < 1 ? [x0, domain[0]] : i >= n ? [domain[n - 1], x1] : [domain[i - 1], domain[i]];\n  };\n  scale.unknown = function (_) {\n    return arguments.length ? (unknown = _, scale) : scale;\n  };\n  scale.thresholds = function () {\n    return domain.slice();\n  };\n  scale.copy = function () {\n    return quantize().domain([x0, x1]).range(range).unknown(unknown);\n  };\n  return initRange.apply(linearish(scale), arguments);\n}", "map": {"version": 3, "names": ["bisect", "linearish", "initRange", "quantize", "x0", "x1", "n", "domain", "range", "unknown", "scale", "x", "rescale", "i", "Array", "_", "arguments", "length", "from", "slice", "invertExtent", "y", "indexOf", "NaN", "thresholds", "copy", "apply"], "sources": ["C:/Users/<USER>/PycharmProjects/CMS/webapp/frontend/node_modules/d3-scale/src/quantize.js"], "sourcesContent": ["import {bisect} from \"d3-array\";\nimport {linearish} from \"./linear.js\";\nimport {initRange} from \"./init.js\";\n\nexport default function quantize() {\n  var x0 = 0,\n      x1 = 1,\n      n = 1,\n      domain = [0.5],\n      range = [0, 1],\n      unknown;\n\n  function scale(x) {\n    return x != null && x <= x ? range[bisect(domain, x, 0, n)] : unknown;\n  }\n\n  function rescale() {\n    var i = -1;\n    domain = new Array(n);\n    while (++i < n) domain[i] = ((i + 1) * x1 - (i - n) * x0) / (n + 1);\n    return scale;\n  }\n\n  scale.domain = function(_) {\n    return arguments.length ? ([x0, x1] = _, x0 = +x0, x1 = +x1, rescale()) : [x0, x1];\n  };\n\n  scale.range = function(_) {\n    return arguments.length ? (n = (range = Array.from(_)).length - 1, rescale()) : range.slice();\n  };\n\n  scale.invertExtent = function(y) {\n    var i = range.indexOf(y);\n    return i < 0 ? [NaN, NaN]\n        : i < 1 ? [x0, domain[0]]\n        : i >= n ? [domain[n - 1], x1]\n        : [domain[i - 1], domain[i]];\n  };\n\n  scale.unknown = function(_) {\n    return arguments.length ? (unknown = _, scale) : scale;\n  };\n\n  scale.thresholds = function() {\n    return domain.slice();\n  };\n\n  scale.copy = function() {\n    return quantize()\n        .domain([x0, x1])\n        .range(range)\n        .unknown(unknown);\n  };\n\n  return initRange.apply(linearish(scale), arguments);\n}\n"], "mappings": "AAAA,SAAQA,MAAM,QAAO,UAAU;AAC/B,SAAQC,SAAS,QAAO,aAAa;AACrC,SAAQC,SAAS,QAAO,WAAW;AAEnC,eAAe,SAASC,QAAQA,CAAA,EAAG;EACjC,IAAIC,EAAE,GAAG,CAAC;IACNC,EAAE,GAAG,CAAC;IACNC,CAAC,GAAG,CAAC;IACLC,MAAM,GAAG,CAAC,GAAG,CAAC;IACdC,KAAK,GAAG,CAAC,CAAC,EAAE,CAAC,CAAC;IACdC,OAAO;EAEX,SAASC,KAAKA,CAACC,CAAC,EAAE;IAChB,OAAOA,CAAC,IAAI,IAAI,IAAIA,CAAC,IAAIA,CAAC,GAAGH,KAAK,CAACR,MAAM,CAACO,MAAM,EAAEI,CAAC,EAAE,CAAC,EAAEL,CAAC,CAAC,CAAC,GAAGG,OAAO;EACvE;EAEA,SAASG,OAAOA,CAAA,EAAG;IACjB,IAAIC,CAAC,GAAG,CAAC,CAAC;IACVN,MAAM,GAAG,IAAIO,KAAK,CAACR,CAAC,CAAC;IACrB,OAAO,EAAEO,CAAC,GAAGP,CAAC,EAAEC,MAAM,CAACM,CAAC,CAAC,GAAG,CAAC,CAACA,CAAC,GAAG,CAAC,IAAIR,EAAE,GAAG,CAACQ,CAAC,GAAGP,CAAC,IAAIF,EAAE,KAAKE,CAAC,GAAG,CAAC,CAAC;IACnE,OAAOI,KAAK;EACd;EAEAA,KAAK,CAACH,MAAM,GAAG,UAASQ,CAAC,EAAE;IACzB,OAAOC,SAAS,CAACC,MAAM,IAAI,CAACb,EAAE,EAAEC,EAAE,CAAC,GAAGU,CAAC,EAAEX,EAAE,GAAG,CAACA,EAAE,EAAEC,EAAE,GAAG,CAACA,EAAE,EAAEO,OAAO,CAAC,CAAC,IAAI,CAACR,EAAE,EAAEC,EAAE,CAAC;EACpF,CAAC;EAEDK,KAAK,CAACF,KAAK,GAAG,UAASO,CAAC,EAAE;IACxB,OAAOC,SAAS,CAACC,MAAM,IAAIX,CAAC,GAAG,CAACE,KAAK,GAAGM,KAAK,CAACI,IAAI,CAACH,CAAC,CAAC,EAAEE,MAAM,GAAG,CAAC,EAAEL,OAAO,CAAC,CAAC,IAAIJ,KAAK,CAACW,KAAK,CAAC,CAAC;EAC/F,CAAC;EAEDT,KAAK,CAACU,YAAY,GAAG,UAASC,CAAC,EAAE;IAC/B,IAAIR,CAAC,GAAGL,KAAK,CAACc,OAAO,CAACD,CAAC,CAAC;IACxB,OAAOR,CAAC,GAAG,CAAC,GAAG,CAACU,GAAG,EAAEA,GAAG,CAAC,GACnBV,CAAC,GAAG,CAAC,GAAG,CAACT,EAAE,EAAEG,MAAM,CAAC,CAAC,CAAC,CAAC,GACvBM,CAAC,IAAIP,CAAC,GAAG,CAACC,MAAM,CAACD,CAAC,GAAG,CAAC,CAAC,EAAED,EAAE,CAAC,GAC5B,CAACE,MAAM,CAACM,CAAC,GAAG,CAAC,CAAC,EAAEN,MAAM,CAACM,CAAC,CAAC,CAAC;EAClC,CAAC;EAEDH,KAAK,CAACD,OAAO,GAAG,UAASM,CAAC,EAAE;IAC1B,OAAOC,SAAS,CAACC,MAAM,IAAIR,OAAO,GAAGM,CAAC,EAAEL,KAAK,IAAIA,KAAK;EACxD,CAAC;EAEDA,KAAK,CAACc,UAAU,GAAG,YAAW;IAC5B,OAAOjB,MAAM,CAACY,KAAK,CAAC,CAAC;EACvB,CAAC;EAEDT,KAAK,CAACe,IAAI,GAAG,YAAW;IACtB,OAAOtB,QAAQ,CAAC,CAAC,CACZI,MAAM,CAAC,CAACH,EAAE,EAAEC,EAAE,CAAC,CAAC,CAChBG,KAAK,CAACA,KAAK,CAAC,CACZC,OAAO,CAACA,OAAO,CAAC;EACvB,CAAC;EAED,OAAOP,SAAS,CAACwB,KAAK,CAACzB,SAAS,CAACS,KAAK,CAAC,EAAEM,SAAS,CAAC;AACrD", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}