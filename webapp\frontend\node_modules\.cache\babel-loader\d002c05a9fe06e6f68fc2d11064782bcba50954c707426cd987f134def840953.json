{"ast": null, "code": "var _jsxFileName = \"C:\\\\CMS\\\\webapp\\\\frontend\\\\src\\\\pages\\\\cavi\\\\TestCaviPage.js\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect } from 'react';\nimport { Box, Typography, Paper, Button, CircularProgress, Alert, TextField, Table, TableBody, TableCell, TableContainer, TableHead, TableRow } from '@mui/material';\nimport axios from 'axios';\nimport config from '../../config';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst API_URL = config.API_URL;\nconst TestCaviPage = () => {\n  _s();\n  const [cantiereId, setCantiereId] = useState('2');\n  const [loading, setLoading] = useState(false);\n  const [error, setError] = useState(null);\n  const [result, setResult] = useState(null);\n  const testDebugEndpoint = async () => {\n    setLoading(true);\n    setError(null);\n    setResult(null);\n    try {\n      const token = localStorage.getItem('token');\n      const response = await axios.get(`${API_URL}/cavi/debug/${cantiereId}`, {\n        headers: {\n          'Authorization': `Bearer ${token}`\n        }\n      });\n      console.log('Risposta debug endpoint:', response.data);\n      setResult(response.data);\n    } catch (err) {\n      console.error('Errore nel test debug endpoint:', err);\n      setError(err.message || 'Errore sconosciuto');\n    } finally {\n      setLoading(false);\n    }\n  };\n  const testRegularEndpoint = async () => {\n    setLoading(true);\n    setError(null);\n    setResult(null);\n    try {\n      const token = localStorage.getItem('token');\n      const response = await axios.get(`${API_URL}/cavi/${cantiereId}`, {\n        headers: {\n          'Authorization': `Bearer ${token}`\n        }\n      });\n      console.log('Risposta endpoint regolare:', response.data);\n      setResult({\n        total_cavi: response.data.length,\n        cavi: response.data.slice(0, 10)\n      });\n    } catch (err) {\n      console.error('Errore nel test endpoint regolare:', err);\n      setError(err.message || 'Errore sconosciuto');\n    } finally {\n      setLoading(false);\n    }\n  };\n  const testActiveCablesEndpoint = async () => {\n    setLoading(true);\n    setError(null);\n    setResult(null);\n    try {\n      const token = localStorage.getItem('token');\n      const response = await axios.get(`${API_URL}/cavi/${cantiereId}?tipo_cavo=0`, {\n        headers: {\n          'Authorization': `Bearer ${token}`\n        }\n      });\n      console.log('Risposta endpoint cavi attivi:', response.data);\n      setResult({\n        total_cavi: response.data.length,\n        cavi: response.data.slice(0, 10)\n      });\n    } catch (err) {\n      console.error('Errore nel test endpoint cavi attivi:', err);\n      setError(err.message || 'Errore sconosciuto');\n    } finally {\n      setLoading(false);\n    }\n  };\n  const testSpareCablesEndpoint = async () => {\n    setLoading(true);\n    setError(null);\n    setResult(null);\n    try {\n      const token = localStorage.getItem('token');\n      const response = await axios.get(`${API_URL}/cavi/${cantiereId}?tipo_cavo=3`, {\n        headers: {\n          'Authorization': `Bearer ${token}`\n        }\n      });\n      console.log('Risposta endpoint cavi spare:', response.data);\n      setResult({\n        total_cavi: response.data.length,\n        cavi: response.data.slice(0, 10)\n      });\n    } catch (err) {\n      console.error('Errore nel test endpoint cavi spare:', err);\n      setError(err.message || 'Errore sconosciuto');\n    } finally {\n      setLoading(false);\n    }\n  };\n  const testDirectSQL = async () => {\n    setLoading(true);\n    setError(null);\n    setResult(null);\n    try {\n      // Questa è una simulazione di una query SQL diretta\n      // In un'applicazione reale, dovresti avere un endpoint dedicato per questo\n      const sql = `\n        SELECT * FROM cavi\n        WHERE id_cantiere = ${cantiereId}\n        LIMIT 10\n      `;\n      setResult({\n        sql: sql,\n        message: \"Questa è solo una simulazione. In un'applicazione reale, dovresti avere un endpoint dedicato per eseguire query SQL dirette.\"\n      });\n    } catch (err) {\n      console.error('Errore nella simulazione SQL:', err);\n      setError(err.message || 'Errore sconosciuto');\n    } finally {\n      setLoading(false);\n    }\n  };\n  return /*#__PURE__*/_jsxDEV(Box, {\n    sx: {\n      p: 3\n    },\n    children: [/*#__PURE__*/_jsxDEV(Typography, {\n      variant: \"h4\",\n      gutterBottom: true,\n      children: \"Test API Cavi\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 157,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Paper, {\n      sx: {\n        p: 3,\n        mb: 3\n      },\n      children: [/*#__PURE__*/_jsxDEV(Typography, {\n        variant: \"h6\",\n        gutterBottom: true,\n        children: \"Configurazione\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 162,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(TextField, {\n        label: \"ID Cantiere\",\n        value: cantiereId,\n        onChange: e => setCantiereId(e.target.value),\n        sx: {\n          mr: 2,\n          mb: 2\n        }\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 166,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Box, {\n        sx: {\n          display: 'flex',\n          flexWrap: 'wrap',\n          gap: 2,\n          mt: 2\n        },\n        children: [/*#__PURE__*/_jsxDEV(Button, {\n          variant: \"contained\",\n          color: \"primary\",\n          onClick: testDebugEndpoint,\n          disabled: loading,\n          children: \"Test Debug Endpoint\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 174,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Button, {\n          variant: \"contained\",\n          color: \"secondary\",\n          onClick: testRegularEndpoint,\n          disabled: loading,\n          children: \"Test Endpoint Regolare\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 183,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Button, {\n          variant: \"contained\",\n          color: \"success\",\n          onClick: testActiveCablesEndpoint,\n          disabled: loading,\n          children: \"Test Cavi Attivi\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 192,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Button, {\n          variant: \"contained\",\n          color: \"info\",\n          onClick: testSpareCablesEndpoint,\n          disabled: loading,\n          children: \"Test Cavi Spare\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 201,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Button, {\n          variant: \"contained\",\n          color: \"warning\",\n          onClick: testDirectSQL,\n          disabled: loading,\n          children: \"Simula Query SQL\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 210,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 173,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 161,\n      columnNumber: 7\n    }, this), loading && /*#__PURE__*/_jsxDEV(Box, {\n      sx: {\n        display: 'flex',\n        justifyContent: 'center',\n        my: 4\n      },\n      children: /*#__PURE__*/_jsxDEV(CircularProgress, {}, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 223,\n        columnNumber: 11\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 222,\n      columnNumber: 9\n    }, this), error && /*#__PURE__*/_jsxDEV(Alert, {\n      severity: \"error\",\n      sx: {\n        mb: 3\n      },\n      children: error\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 228,\n      columnNumber: 9\n    }, this), result && /*#__PURE__*/_jsxDEV(Paper, {\n      sx: {\n        p: 3\n      },\n      children: [/*#__PURE__*/_jsxDEV(Typography, {\n        variant: \"h6\",\n        gutterBottom: true,\n        children: \"Risultato\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 235,\n        columnNumber: 11\n      }, this), result.sql ? /*#__PURE__*/_jsxDEV(Box, {\n        children: [/*#__PURE__*/_jsxDEV(Typography, {\n          variant: \"subtitle1\",\n          children: \"Query SQL:\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 241,\n          columnNumber: 15\n        }, this), /*#__PURE__*/_jsxDEV(\"pre\", {\n          children: result.sql\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 242,\n          columnNumber: 15\n        }, this), /*#__PURE__*/_jsxDEV(Alert, {\n          severity: \"info\",\n          children: result.message\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 243,\n          columnNumber: 15\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 240,\n        columnNumber: 13\n      }, this) : /*#__PURE__*/_jsxDEV(Box, {\n        children: [/*#__PURE__*/_jsxDEV(Typography, {\n          variant: \"subtitle1\",\n          children: [\"Totale cavi: \", result.total_cavi || 0, result.cavi_attivi && ` (Attivi: ${result.cavi_attivi}, Spare: ${result.cavi_spare})`]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 247,\n          columnNumber: 15\n        }, this), result.cavi && result.cavi.length > 0 ? /*#__PURE__*/_jsxDEV(TableContainer, {\n          component: Paper,\n          sx: {\n            mt: 2,\n            maxHeight: 400,\n            overflow: 'auto'\n          },\n          children: /*#__PURE__*/_jsxDEV(Table, {\n            size: \"small\",\n            children: [/*#__PURE__*/_jsxDEV(TableHead, {\n              children: /*#__PURE__*/_jsxDEV(TableRow, {\n                children: [/*#__PURE__*/_jsxDEV(TableCell, {\n                  children: \"ID Cavo\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 257,\n                  columnNumber: 25\n                }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n                  children: \"Utility\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 258,\n                  columnNumber: 25\n                }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n                  children: \"Tipologia\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 259,\n                  columnNumber: 25\n                }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n                  children: \"N.Cond\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 260,\n                  columnNumber: 25\n                }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n                  children: \"Sezione\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 261,\n                  columnNumber: 25\n                }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n                  children: \"Ubicaz.Part.\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 262,\n                  columnNumber: 25\n                }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n                  children: \"Ubicaz.Arr.\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 263,\n                  columnNumber: 25\n                }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n                  children: \"Metri T.\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 264,\n                  columnNumber: 25\n                }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n                  children: \"Stato\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 265,\n                  columnNumber: 25\n                }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n                  children: \"Mod. Man.\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 266,\n                  columnNumber: 25\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 256,\n                columnNumber: 23\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 255,\n              columnNumber: 21\n            }, this), /*#__PURE__*/_jsxDEV(TableBody, {\n              children: result.cavi.map(cavo => /*#__PURE__*/_jsxDEV(TableRow, {\n                children: [/*#__PURE__*/_jsxDEV(TableCell, {\n                  children: cavo.id_cavo\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 272,\n                  columnNumber: 27\n                }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n                  children: cavo.utility || '-'\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 273,\n                  columnNumber: 27\n                }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n                  children: cavo.tipologia || '-'\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 274,\n                  columnNumber: 27\n                }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n                  children: cavo.n_conduttori || '-'\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 275,\n                  columnNumber: 27\n                }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n                  children: cavo.sezione || '-'\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 276,\n                  columnNumber: 27\n                }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n                  children: cavo.ubicazione_partenza || '-'\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 277,\n                  columnNumber: 27\n                }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n                  children: cavo.ubicazione_arrivo || '-'\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 278,\n                  columnNumber: 27\n                }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n                  children: cavo.metri_teorici || '-'\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 279,\n                  columnNumber: 27\n                }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n                  children: cavo.stato_installazione || '-'\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 280,\n                  columnNumber: 27\n                }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n                  children: cavo.modificato_manualmente\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 281,\n                  columnNumber: 27\n                }, this)]\n              }, cavo.id_cavo, true, {\n                fileName: _jsxFileName,\n                lineNumber: 271,\n                columnNumber: 25\n              }, this))\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 269,\n              columnNumber: 21\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 254,\n            columnNumber: 19\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 253,\n          columnNumber: 17\n        }, this) : /*#__PURE__*/_jsxDEV(Alert, {\n          severity: \"info\",\n          sx: {\n            mt: 2\n          },\n          children: \"Nessun cavo trovato\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 288,\n          columnNumber: 17\n        }, this), /*#__PURE__*/_jsxDEV(Typography, {\n          variant: \"subtitle1\",\n          sx: {\n            mt: 3\n          },\n          children: \"Dati completi:\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 293,\n          columnNumber: 15\n        }, this), /*#__PURE__*/_jsxDEV(\"pre\", {\n          style: {\n            maxHeight: '300px',\n            overflow: 'auto',\n            backgroundColor: '#f5f5f5',\n            padding: '10px'\n          },\n          children: JSON.stringify(result, null, 2)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 296,\n          columnNumber: 15\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 246,\n        columnNumber: 13\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 234,\n      columnNumber: 9\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 156,\n    columnNumber: 5\n  }, this);\n};\n_s(TestCaviPage, \"r9uaO5KpMblQfpcIDuY3mpR+EYU=\");\n_c = TestCaviPage;\nexport default TestCaviPage;\nvar _c;\n$RefreshReg$(_c, \"TestCaviPage\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "Box", "Typography", "Paper", "<PERSON><PERSON>", "CircularProgress", "<PERSON><PERSON>", "TextField", "Table", "TableBody", "TableCell", "TableContainer", "TableHead", "TableRow", "axios", "config", "jsxDEV", "_jsxDEV", "API_URL", "TestCaviPage", "_s", "cantiereId", "setCantiereId", "loading", "setLoading", "error", "setError", "result", "setResult", "testDebugEndpoint", "token", "localStorage", "getItem", "response", "get", "headers", "console", "log", "data", "err", "message", "testRegularEndpoint", "total_cavi", "length", "cavi", "slice", "testActiveCablesEndpoint", "testSpareCablesEndpoint", "testDirectSQL", "sql", "sx", "p", "children", "variant", "gutterBottom", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "mb", "label", "value", "onChange", "e", "target", "mr", "display", "flexWrap", "gap", "mt", "color", "onClick", "disabled", "justifyContent", "my", "severity", "cavi_attivi", "cavi_spare", "component", "maxHeight", "overflow", "size", "map", "cavo", "id_cavo", "utility", "tipologia", "n_conduttori", "sezione", "ubicazione_partenza", "ubicazione_arrivo", "metri_te<PERSON>ci", "stato_installazione", "modificato_manualmente", "style", "backgroundColor", "padding", "JSON", "stringify", "_c", "$RefreshReg$"], "sources": ["C:/CMS/webapp/frontend/src/pages/cavi/TestCaviPage.js"], "sourcesContent": ["import React, { useState, useEffect } from 'react';\nimport {\n  Box,\n  Typography,\n  Paper,\n  Button,\n  CircularProgress,\n  Alert,\n  TextField,\n  Table,\n  TableBody,\n  TableCell,\n  TableContainer,\n  TableHead,\n  TableRow\n} from '@mui/material';\nimport axios from 'axios';\nimport config from '../../config';\n\nconst API_URL = config.API_URL;\n\nconst TestCaviPage = () => {\n  const [cantiereId, setCantiereId] = useState('2');\n  const [loading, setLoading] = useState(false);\n  const [error, setError] = useState(null);\n  const [result, setResult] = useState(null);\n\n  const testDebugEndpoint = async () => {\n    setLoading(true);\n    setError(null);\n    setResult(null);\n\n    try {\n      const token = localStorage.getItem('token');\n      const response = await axios.get(`${API_URL}/cavi/debug/${cantiereId}`, {\n        headers: {\n          'Authorization': `Bearer ${token}`\n        }\n      });\n\n      console.log('Risposta debug endpoint:', response.data);\n      setResult(response.data);\n    } catch (err) {\n      console.error('Errore nel test debug endpoint:', err);\n      setError(err.message || 'Errore sconosciuto');\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  const testRegularEndpoint = async () => {\n    setLoading(true);\n    setError(null);\n    setResult(null);\n\n    try {\n      const token = localStorage.getItem('token');\n      const response = await axios.get(`${API_URL}/cavi/${cantiereId}`, {\n        headers: {\n          'Authorization': `Bearer ${token}`\n        }\n      });\n\n      console.log('Risposta endpoint regolare:', response.data);\n      setResult({\n        total_cavi: response.data.length,\n        cavi: response.data.slice(0, 10)\n      });\n    } catch (err) {\n      console.error('Errore nel test endpoint regolare:', err);\n      setError(err.message || 'Errore sconosciuto');\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  const testActiveCablesEndpoint = async () => {\n    setLoading(true);\n    setError(null);\n    setResult(null);\n\n    try {\n      const token = localStorage.getItem('token');\n      const response = await axios.get(`${API_URL}/cavi/${cantiereId}?tipo_cavo=0`, {\n        headers: {\n          'Authorization': `Bearer ${token}`\n        }\n      });\n\n      console.log('Risposta endpoint cavi attivi:', response.data);\n      setResult({\n        total_cavi: response.data.length,\n        cavi: response.data.slice(0, 10)\n      });\n    } catch (err) {\n      console.error('Errore nel test endpoint cavi attivi:', err);\n      setError(err.message || 'Errore sconosciuto');\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  const testSpareCablesEndpoint = async () => {\n    setLoading(true);\n    setError(null);\n    setResult(null);\n\n    try {\n      const token = localStorage.getItem('token');\n      const response = await axios.get(`${API_URL}/cavi/${cantiereId}?tipo_cavo=3`, {\n        headers: {\n          'Authorization': `Bearer ${token}`\n        }\n      });\n\n      console.log('Risposta endpoint cavi spare:', response.data);\n      setResult({\n        total_cavi: response.data.length,\n        cavi: response.data.slice(0, 10)\n      });\n    } catch (err) {\n      console.error('Errore nel test endpoint cavi spare:', err);\n      setError(err.message || 'Errore sconosciuto');\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  const testDirectSQL = async () => {\n    setLoading(true);\n    setError(null);\n    setResult(null);\n\n    try {\n      // Questa è una simulazione di una query SQL diretta\n      // In un'applicazione reale, dovresti avere un endpoint dedicato per questo\n      const sql = `\n        SELECT * FROM cavi\n        WHERE id_cantiere = ${cantiereId}\n        LIMIT 10\n      `;\n\n      setResult({\n        sql: sql,\n        message: \"Questa è solo una simulazione. In un'applicazione reale, dovresti avere un endpoint dedicato per eseguire query SQL dirette.\"\n      });\n    } catch (err) {\n      console.error('Errore nella simulazione SQL:', err);\n      setError(err.message || 'Errore sconosciuto');\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  return (\n    <Box sx={{ p: 3 }}>\n      <Typography variant=\"h4\" gutterBottom>\n        Test API Cavi\n      </Typography>\n\n      <Paper sx={{ p: 3, mb: 3 }}>\n        <Typography variant=\"h6\" gutterBottom>\n          Configurazione\n        </Typography>\n\n        <TextField\n          label=\"ID Cantiere\"\n          value={cantiereId}\n          onChange={(e) => setCantiereId(e.target.value)}\n          sx={{ mr: 2, mb: 2 }}\n        />\n\n        <Box sx={{ display: 'flex', flexWrap: 'wrap', gap: 2, mt: 2 }}>\n          <Button\n            variant=\"contained\"\n            color=\"primary\"\n            onClick={testDebugEndpoint}\n            disabled={loading}\n          >\n            Test Debug Endpoint\n          </Button>\n\n          <Button\n            variant=\"contained\"\n            color=\"secondary\"\n            onClick={testRegularEndpoint}\n            disabled={loading}\n          >\n            Test Endpoint Regolare\n          </Button>\n\n          <Button\n            variant=\"contained\"\n            color=\"success\"\n            onClick={testActiveCablesEndpoint}\n            disabled={loading}\n          >\n            Test Cavi Attivi\n          </Button>\n\n          <Button\n            variant=\"contained\"\n            color=\"info\"\n            onClick={testSpareCablesEndpoint}\n            disabled={loading}\n          >\n            Test Cavi Spare\n          </Button>\n\n          <Button\n            variant=\"contained\"\n            color=\"warning\"\n            onClick={testDirectSQL}\n            disabled={loading}\n          >\n            Simula Query SQL\n          </Button>\n        </Box>\n      </Paper>\n\n      {loading && (\n        <Box sx={{ display: 'flex', justifyContent: 'center', my: 4 }}>\n          <CircularProgress />\n        </Box>\n      )}\n\n      {error && (\n        <Alert severity=\"error\" sx={{ mb: 3 }}>\n          {error}\n        </Alert>\n      )}\n\n      {result && (\n        <Paper sx={{ p: 3 }}>\n          <Typography variant=\"h6\" gutterBottom>\n            Risultato\n          </Typography>\n\n          {result.sql ? (\n            <Box>\n              <Typography variant=\"subtitle1\">Query SQL:</Typography>\n              <pre>{result.sql}</pre>\n              <Alert severity=\"info\">{result.message}</Alert>\n            </Box>\n          ) : (\n            <Box>\n              <Typography variant=\"subtitle1\">\n                Totale cavi: {result.total_cavi || 0}\n                {result.cavi_attivi && ` (Attivi: ${result.cavi_attivi}, Spare: ${result.cavi_spare})`}\n              </Typography>\n\n              {result.cavi && result.cavi.length > 0 ? (\n                <TableContainer component={Paper} sx={{ mt: 2, maxHeight: 400, overflow: 'auto' }}>\n                  <Table size=\"small\">\n                    <TableHead>\n                      <TableRow>\n                        <TableCell>ID Cavo</TableCell>\n                        <TableCell>Utility</TableCell>\n                        <TableCell>Tipologia</TableCell>\n                        <TableCell>N.Cond</TableCell>\n                        <TableCell>Sezione</TableCell>\n                        <TableCell>Ubicaz.Part.</TableCell>\n                        <TableCell>Ubicaz.Arr.</TableCell>\n                        <TableCell>Metri T.</TableCell>\n                        <TableCell>Stato</TableCell>\n                        <TableCell>Mod. Man.</TableCell>\n                      </TableRow>\n                    </TableHead>\n                    <TableBody>\n                      {result.cavi.map((cavo) => (\n                        <TableRow key={cavo.id_cavo}>\n                          <TableCell>{cavo.id_cavo}</TableCell>\n                          <TableCell>{cavo.utility || '-'}</TableCell>\n                          <TableCell>{cavo.tipologia || '-'}</TableCell>\n                          <TableCell>{cavo.n_conduttori || '-'}</TableCell>\n                          <TableCell>{cavo.sezione || '-'}</TableCell>\n                          <TableCell>{cavo.ubicazione_partenza || '-'}</TableCell>\n                          <TableCell>{cavo.ubicazione_arrivo || '-'}</TableCell>\n                          <TableCell>{cavo.metri_teorici || '-'}</TableCell>\n                          <TableCell>{cavo.stato_installazione || '-'}</TableCell>\n                          <TableCell>{cavo.modificato_manualmente}</TableCell>\n                        </TableRow>\n                      ))}\n                    </TableBody>\n                  </Table>\n                </TableContainer>\n              ) : (\n                <Alert severity=\"info\" sx={{ mt: 2 }}>\n                  Nessun cavo trovato\n                </Alert>\n              )}\n\n              <Typography variant=\"subtitle1\" sx={{ mt: 3 }}>\n                Dati completi:\n              </Typography>\n              <pre style={{ maxHeight: '300px', overflow: 'auto', backgroundColor: '#f5f5f5', padding: '10px' }}>\n                {JSON.stringify(result, null, 2)}\n              </pre>\n            </Box>\n          )}\n        </Paper>\n      )}\n    </Box>\n  );\n};\n\nexport default TestCaviPage;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAClD,SACEC,GAAG,EACHC,UAAU,EACVC,KAAK,EACLC,MAAM,EACNC,gBAAgB,EAChBC,KAAK,EACLC,SAAS,EACTC,KAAK,EACLC,SAAS,EACTC,SAAS,EACTC,cAAc,EACdC,SAAS,EACTC,QAAQ,QACH,eAAe;AACtB,OAAOC,KAAK,MAAM,OAAO;AACzB,OAAOC,MAAM,MAAM,cAAc;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAElC,MAAMC,OAAO,GAAGH,MAAM,CAACG,OAAO;AAE9B,MAAMC,YAAY,GAAGA,CAAA,KAAM;EAAAC,EAAA;EACzB,MAAM,CAACC,UAAU,EAAEC,aAAa,CAAC,GAAGvB,QAAQ,CAAC,GAAG,CAAC;EACjD,MAAM,CAACwB,OAAO,EAAEC,UAAU,CAAC,GAAGzB,QAAQ,CAAC,KAAK,CAAC;EAC7C,MAAM,CAAC0B,KAAK,EAAEC,QAAQ,CAAC,GAAG3B,QAAQ,CAAC,IAAI,CAAC;EACxC,MAAM,CAAC4B,MAAM,EAAEC,SAAS,CAAC,GAAG7B,QAAQ,CAAC,IAAI,CAAC;EAE1C,MAAM8B,iBAAiB,GAAG,MAAAA,CAAA,KAAY;IACpCL,UAAU,CAAC,IAAI,CAAC;IAChBE,QAAQ,CAAC,IAAI,CAAC;IACdE,SAAS,CAAC,IAAI,CAAC;IAEf,IAAI;MACF,MAAME,KAAK,GAAGC,YAAY,CAACC,OAAO,CAAC,OAAO,CAAC;MAC3C,MAAMC,QAAQ,GAAG,MAAMnB,KAAK,CAACoB,GAAG,CAAC,GAAGhB,OAAO,eAAeG,UAAU,EAAE,EAAE;QACtEc,OAAO,EAAE;UACP,eAAe,EAAE,UAAUL,KAAK;QAClC;MACF,CAAC,CAAC;MAEFM,OAAO,CAACC,GAAG,CAAC,0BAA0B,EAAEJ,QAAQ,CAACK,IAAI,CAAC;MACtDV,SAAS,CAACK,QAAQ,CAACK,IAAI,CAAC;IAC1B,CAAC,CAAC,OAAOC,GAAG,EAAE;MACZH,OAAO,CAACX,KAAK,CAAC,iCAAiC,EAAEc,GAAG,CAAC;MACrDb,QAAQ,CAACa,GAAG,CAACC,OAAO,IAAI,oBAAoB,CAAC;IAC/C,CAAC,SAAS;MACRhB,UAAU,CAAC,KAAK,CAAC;IACnB;EACF,CAAC;EAED,MAAMiB,mBAAmB,GAAG,MAAAA,CAAA,KAAY;IACtCjB,UAAU,CAAC,IAAI,CAAC;IAChBE,QAAQ,CAAC,IAAI,CAAC;IACdE,SAAS,CAAC,IAAI,CAAC;IAEf,IAAI;MACF,MAAME,KAAK,GAAGC,YAAY,CAACC,OAAO,CAAC,OAAO,CAAC;MAC3C,MAAMC,QAAQ,GAAG,MAAMnB,KAAK,CAACoB,GAAG,CAAC,GAAGhB,OAAO,SAASG,UAAU,EAAE,EAAE;QAChEc,OAAO,EAAE;UACP,eAAe,EAAE,UAAUL,KAAK;QAClC;MACF,CAAC,CAAC;MAEFM,OAAO,CAACC,GAAG,CAAC,6BAA6B,EAAEJ,QAAQ,CAACK,IAAI,CAAC;MACzDV,SAAS,CAAC;QACRc,UAAU,EAAET,QAAQ,CAACK,IAAI,CAACK,MAAM;QAChCC,IAAI,EAAEX,QAAQ,CAACK,IAAI,CAACO,KAAK,CAAC,CAAC,EAAE,EAAE;MACjC,CAAC,CAAC;IACJ,CAAC,CAAC,OAAON,GAAG,EAAE;MACZH,OAAO,CAACX,KAAK,CAAC,oCAAoC,EAAEc,GAAG,CAAC;MACxDb,QAAQ,CAACa,GAAG,CAACC,OAAO,IAAI,oBAAoB,CAAC;IAC/C,CAAC,SAAS;MACRhB,UAAU,CAAC,KAAK,CAAC;IACnB;EACF,CAAC;EAED,MAAMsB,wBAAwB,GAAG,MAAAA,CAAA,KAAY;IAC3CtB,UAAU,CAAC,IAAI,CAAC;IAChBE,QAAQ,CAAC,IAAI,CAAC;IACdE,SAAS,CAAC,IAAI,CAAC;IAEf,IAAI;MACF,MAAME,KAAK,GAAGC,YAAY,CAACC,OAAO,CAAC,OAAO,CAAC;MAC3C,MAAMC,QAAQ,GAAG,MAAMnB,KAAK,CAACoB,GAAG,CAAC,GAAGhB,OAAO,SAASG,UAAU,cAAc,EAAE;QAC5Ec,OAAO,EAAE;UACP,eAAe,EAAE,UAAUL,KAAK;QAClC;MACF,CAAC,CAAC;MAEFM,OAAO,CAACC,GAAG,CAAC,gCAAgC,EAAEJ,QAAQ,CAACK,IAAI,CAAC;MAC5DV,SAAS,CAAC;QACRc,UAAU,EAAET,QAAQ,CAACK,IAAI,CAACK,MAAM;QAChCC,IAAI,EAAEX,QAAQ,CAACK,IAAI,CAACO,KAAK,CAAC,CAAC,EAAE,EAAE;MACjC,CAAC,CAAC;IACJ,CAAC,CAAC,OAAON,GAAG,EAAE;MACZH,OAAO,CAACX,KAAK,CAAC,uCAAuC,EAAEc,GAAG,CAAC;MAC3Db,QAAQ,CAACa,GAAG,CAACC,OAAO,IAAI,oBAAoB,CAAC;IAC/C,CAAC,SAAS;MACRhB,UAAU,CAAC,KAAK,CAAC;IACnB;EACF,CAAC;EAED,MAAMuB,uBAAuB,GAAG,MAAAA,CAAA,KAAY;IAC1CvB,UAAU,CAAC,IAAI,CAAC;IAChBE,QAAQ,CAAC,IAAI,CAAC;IACdE,SAAS,CAAC,IAAI,CAAC;IAEf,IAAI;MACF,MAAME,KAAK,GAAGC,YAAY,CAACC,OAAO,CAAC,OAAO,CAAC;MAC3C,MAAMC,QAAQ,GAAG,MAAMnB,KAAK,CAACoB,GAAG,CAAC,GAAGhB,OAAO,SAASG,UAAU,cAAc,EAAE;QAC5Ec,OAAO,EAAE;UACP,eAAe,EAAE,UAAUL,KAAK;QAClC;MACF,CAAC,CAAC;MAEFM,OAAO,CAACC,GAAG,CAAC,+BAA+B,EAAEJ,QAAQ,CAACK,IAAI,CAAC;MAC3DV,SAAS,CAAC;QACRc,UAAU,EAAET,QAAQ,CAACK,IAAI,CAACK,MAAM;QAChCC,IAAI,EAAEX,QAAQ,CAACK,IAAI,CAACO,KAAK,CAAC,CAAC,EAAE,EAAE;MACjC,CAAC,CAAC;IACJ,CAAC,CAAC,OAAON,GAAG,EAAE;MACZH,OAAO,CAACX,KAAK,CAAC,sCAAsC,EAAEc,GAAG,CAAC;MAC1Db,QAAQ,CAACa,GAAG,CAACC,OAAO,IAAI,oBAAoB,CAAC;IAC/C,CAAC,SAAS;MACRhB,UAAU,CAAC,KAAK,CAAC;IACnB;EACF,CAAC;EAED,MAAMwB,aAAa,GAAG,MAAAA,CAAA,KAAY;IAChCxB,UAAU,CAAC,IAAI,CAAC;IAChBE,QAAQ,CAAC,IAAI,CAAC;IACdE,SAAS,CAAC,IAAI,CAAC;IAEf,IAAI;MACF;MACA;MACA,MAAMqB,GAAG,GAAG;AAClB;AACA,8BAA8B5B,UAAU;AACxC;AACA,OAAO;MAEDO,SAAS,CAAC;QACRqB,GAAG,EAAEA,GAAG;QACRT,OAAO,EAAE;MACX,CAAC,CAAC;IACJ,CAAC,CAAC,OAAOD,GAAG,EAAE;MACZH,OAAO,CAACX,KAAK,CAAC,+BAA+B,EAAEc,GAAG,CAAC;MACnDb,QAAQ,CAACa,GAAG,CAACC,OAAO,IAAI,oBAAoB,CAAC;IAC/C,CAAC,SAAS;MACRhB,UAAU,CAAC,KAAK,CAAC;IACnB;EACF,CAAC;EAED,oBACEP,OAAA,CAAChB,GAAG;IAACiD,EAAE,EAAE;MAAEC,CAAC,EAAE;IAAE,CAAE;IAAAC,QAAA,gBAChBnC,OAAA,CAACf,UAAU;MAACmD,OAAO,EAAC,IAAI;MAACC,YAAY;MAAAF,QAAA,EAAC;IAEtC;MAAAG,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAY,CAAC,eAEbzC,OAAA,CAACd,KAAK;MAAC+C,EAAE,EAAE;QAAEC,CAAC,EAAE,CAAC;QAAEQ,EAAE,EAAE;MAAE,CAAE;MAAAP,QAAA,gBACzBnC,OAAA,CAACf,UAAU;QAACmD,OAAO,EAAC,IAAI;QAACC,YAAY;QAAAF,QAAA,EAAC;MAEtC;QAAAG,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAY,CAAC,eAEbzC,OAAA,CAACV,SAAS;QACRqD,KAAK,EAAC,aAAa;QACnBC,KAAK,EAAExC,UAAW;QAClByC,QAAQ,EAAGC,CAAC,IAAKzC,aAAa,CAACyC,CAAC,CAACC,MAAM,CAACH,KAAK,CAAE;QAC/CX,EAAE,EAAE;UAAEe,EAAE,EAAE,CAAC;UAAEN,EAAE,EAAE;QAAE;MAAE;QAAAJ,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACtB,CAAC,eAEFzC,OAAA,CAAChB,GAAG;QAACiD,EAAE,EAAE;UAAEgB,OAAO,EAAE,MAAM;UAAEC,QAAQ,EAAE,MAAM;UAAEC,GAAG,EAAE,CAAC;UAAEC,EAAE,EAAE;QAAE,CAAE;QAAAjB,QAAA,gBAC5DnC,OAAA,CAACb,MAAM;UACLiD,OAAO,EAAC,WAAW;UACnBiB,KAAK,EAAC,SAAS;UACfC,OAAO,EAAE1C,iBAAkB;UAC3B2C,QAAQ,EAAEjD,OAAQ;UAAA6B,QAAA,EACnB;QAED;UAAAG,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC,eAETzC,OAAA,CAACb,MAAM;UACLiD,OAAO,EAAC,WAAW;UACnBiB,KAAK,EAAC,WAAW;UACjBC,OAAO,EAAE9B,mBAAoB;UAC7B+B,QAAQ,EAAEjD,OAAQ;UAAA6B,QAAA,EACnB;QAED;UAAAG,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC,eAETzC,OAAA,CAACb,MAAM;UACLiD,OAAO,EAAC,WAAW;UACnBiB,KAAK,EAAC,SAAS;UACfC,OAAO,EAAEzB,wBAAyB;UAClC0B,QAAQ,EAAEjD,OAAQ;UAAA6B,QAAA,EACnB;QAED;UAAAG,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC,eAETzC,OAAA,CAACb,MAAM;UACLiD,OAAO,EAAC,WAAW;UACnBiB,KAAK,EAAC,MAAM;UACZC,OAAO,EAAExB,uBAAwB;UACjCyB,QAAQ,EAAEjD,OAAQ;UAAA6B,QAAA,EACnB;QAED;UAAAG,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC,eAETzC,OAAA,CAACb,MAAM;UACLiD,OAAO,EAAC,WAAW;UACnBiB,KAAK,EAAC,SAAS;UACfC,OAAO,EAAEvB,aAAc;UACvBwB,QAAQ,EAAEjD,OAAQ;UAAA6B,QAAA,EACnB;QAED;UAAAG,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACN,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACD,CAAC,EAEPnC,OAAO,iBACNN,OAAA,CAAChB,GAAG;MAACiD,EAAE,EAAE;QAAEgB,OAAO,EAAE,MAAM;QAAEO,cAAc,EAAE,QAAQ;QAAEC,EAAE,EAAE;MAAE,CAAE;MAAAtB,QAAA,eAC5DnC,OAAA,CAACZ,gBAAgB;QAAAkD,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACjB,CACN,EAEAjC,KAAK,iBACJR,OAAA,CAACX,KAAK;MAACqE,QAAQ,EAAC,OAAO;MAACzB,EAAE,EAAE;QAAES,EAAE,EAAE;MAAE,CAAE;MAAAP,QAAA,EACnC3B;IAAK;MAAA8B,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACD,CACR,EAEA/B,MAAM,iBACLV,OAAA,CAACd,KAAK;MAAC+C,EAAE,EAAE;QAAEC,CAAC,EAAE;MAAE,CAAE;MAAAC,QAAA,gBAClBnC,OAAA,CAACf,UAAU;QAACmD,OAAO,EAAC,IAAI;QAACC,YAAY;QAAAF,QAAA,EAAC;MAEtC;QAAAG,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAY,CAAC,EAEZ/B,MAAM,CAACsB,GAAG,gBACThC,OAAA,CAAChB,GAAG;QAAAmD,QAAA,gBACFnC,OAAA,CAACf,UAAU;UAACmD,OAAO,EAAC,WAAW;UAAAD,QAAA,EAAC;QAAU;UAAAG,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAY,CAAC,eACvDzC,OAAA;UAAAmC,QAAA,EAAMzB,MAAM,CAACsB;QAAG;UAAAM,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CAAC,eACvBzC,OAAA,CAACX,KAAK;UAACqE,QAAQ,EAAC,MAAM;UAAAvB,QAAA,EAAEzB,MAAM,CAACa;QAAO;UAAAe,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC5C,CAAC,gBAENzC,OAAA,CAAChB,GAAG;QAAAmD,QAAA,gBACFnC,OAAA,CAACf,UAAU;UAACmD,OAAO,EAAC,WAAW;UAAAD,QAAA,GAAC,eACjB,EAACzB,MAAM,CAACe,UAAU,IAAI,CAAC,EACnCf,MAAM,CAACiD,WAAW,IAAI,aAAajD,MAAM,CAACiD,WAAW,YAAYjD,MAAM,CAACkD,UAAU,GAAG;QAAA;UAAAtB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC5E,CAAC,EAEZ/B,MAAM,CAACiB,IAAI,IAAIjB,MAAM,CAACiB,IAAI,CAACD,MAAM,GAAG,CAAC,gBACpC1B,OAAA,CAACN,cAAc;UAACmE,SAAS,EAAE3E,KAAM;UAAC+C,EAAE,EAAE;YAAEmB,EAAE,EAAE,CAAC;YAAEU,SAAS,EAAE,GAAG;YAAEC,QAAQ,EAAE;UAAO,CAAE;UAAA5B,QAAA,eAChFnC,OAAA,CAACT,KAAK;YAACyE,IAAI,EAAC,OAAO;YAAA7B,QAAA,gBACjBnC,OAAA,CAACL,SAAS;cAAAwC,QAAA,eACRnC,OAAA,CAACJ,QAAQ;gBAAAuC,QAAA,gBACPnC,OAAA,CAACP,SAAS;kBAAA0C,QAAA,EAAC;gBAAO;kBAAAG,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAW,CAAC,eAC9BzC,OAAA,CAACP,SAAS;kBAAA0C,QAAA,EAAC;gBAAO;kBAAAG,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAW,CAAC,eAC9BzC,OAAA,CAACP,SAAS;kBAAA0C,QAAA,EAAC;gBAAS;kBAAAG,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAW,CAAC,eAChCzC,OAAA,CAACP,SAAS;kBAAA0C,QAAA,EAAC;gBAAM;kBAAAG,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAW,CAAC,eAC7BzC,OAAA,CAACP,SAAS;kBAAA0C,QAAA,EAAC;gBAAO;kBAAAG,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAW,CAAC,eAC9BzC,OAAA,CAACP,SAAS;kBAAA0C,QAAA,EAAC;gBAAY;kBAAAG,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAW,CAAC,eACnCzC,OAAA,CAACP,SAAS;kBAAA0C,QAAA,EAAC;gBAAW;kBAAAG,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAW,CAAC,eAClCzC,OAAA,CAACP,SAAS;kBAAA0C,QAAA,EAAC;gBAAQ;kBAAAG,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAW,CAAC,eAC/BzC,OAAA,CAACP,SAAS;kBAAA0C,QAAA,EAAC;gBAAK;kBAAAG,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAW,CAAC,eAC5BzC,OAAA,CAACP,SAAS;kBAAA0C,QAAA,EAAC;gBAAS;kBAAAG,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAW,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACxB;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACF,CAAC,eACZzC,OAAA,CAACR,SAAS;cAAA2C,QAAA,EACPzB,MAAM,CAACiB,IAAI,CAACsC,GAAG,CAAEC,IAAI,iBACpBlE,OAAA,CAACJ,QAAQ;gBAAAuC,QAAA,gBACPnC,OAAA,CAACP,SAAS;kBAAA0C,QAAA,EAAE+B,IAAI,CAACC;gBAAO;kBAAA7B,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAY,CAAC,eACrCzC,OAAA,CAACP,SAAS;kBAAA0C,QAAA,EAAE+B,IAAI,CAACE,OAAO,IAAI;gBAAG;kBAAA9B,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAY,CAAC,eAC5CzC,OAAA,CAACP,SAAS;kBAAA0C,QAAA,EAAE+B,IAAI,CAACG,SAAS,IAAI;gBAAG;kBAAA/B,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAY,CAAC,eAC9CzC,OAAA,CAACP,SAAS;kBAAA0C,QAAA,EAAE+B,IAAI,CAACI,YAAY,IAAI;gBAAG;kBAAAhC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAY,CAAC,eACjDzC,OAAA,CAACP,SAAS;kBAAA0C,QAAA,EAAE+B,IAAI,CAACK,OAAO,IAAI;gBAAG;kBAAAjC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAY,CAAC,eAC5CzC,OAAA,CAACP,SAAS;kBAAA0C,QAAA,EAAE+B,IAAI,CAACM,mBAAmB,IAAI;gBAAG;kBAAAlC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAY,CAAC,eACxDzC,OAAA,CAACP,SAAS;kBAAA0C,QAAA,EAAE+B,IAAI,CAACO,iBAAiB,IAAI;gBAAG;kBAAAnC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAY,CAAC,eACtDzC,OAAA,CAACP,SAAS;kBAAA0C,QAAA,EAAE+B,IAAI,CAACQ,aAAa,IAAI;gBAAG;kBAAApC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAY,CAAC,eAClDzC,OAAA,CAACP,SAAS;kBAAA0C,QAAA,EAAE+B,IAAI,CAACS,mBAAmB,IAAI;gBAAG;kBAAArC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAY,CAAC,eACxDzC,OAAA,CAACP,SAAS;kBAAA0C,QAAA,EAAE+B,IAAI,CAACU;gBAAsB;kBAAAtC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAY,CAAC;cAAA,GAVvCyB,IAAI,CAACC,OAAO;gBAAA7B,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAWjB,CACX;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACO,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACP;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACM,CAAC,gBAEjBzC,OAAA,CAACX,KAAK;UAACqE,QAAQ,EAAC,MAAM;UAACzB,EAAE,EAAE;YAAEmB,EAAE,EAAE;UAAE,CAAE;UAAAjB,QAAA,EAAC;QAEtC;UAAAG,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAO,CACR,eAEDzC,OAAA,CAACf,UAAU;UAACmD,OAAO,EAAC,WAAW;UAACH,EAAE,EAAE;YAAEmB,EAAE,EAAE;UAAE,CAAE;UAAAjB,QAAA,EAAC;QAE/C;UAAAG,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAY,CAAC,eACbzC,OAAA;UAAK6E,KAAK,EAAE;YAAEf,SAAS,EAAE,OAAO;YAAEC,QAAQ,EAAE,MAAM;YAAEe,eAAe,EAAE,SAAS;YAAEC,OAAO,EAAE;UAAO,CAAE;UAAA5C,QAAA,EAC/F6C,IAAI,CAACC,SAAS,CAACvE,MAAM,EAAE,IAAI,EAAE,CAAC;QAAC;UAAA4B,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC7B,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CACN;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACI,CACR;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACE,CAAC;AAEV,CAAC;AAACtC,EAAA,CA3RID,YAAY;AAAAgF,EAAA,GAAZhF,YAAY;AA6RlB,eAAeA,YAAY;AAAC,IAAAgF,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}