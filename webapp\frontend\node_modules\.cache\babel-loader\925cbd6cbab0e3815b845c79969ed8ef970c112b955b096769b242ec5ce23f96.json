{"ast": null, "code": "var _jsxFileName = \"C:\\\\CMS\\\\webapp\\\\frontend\\\\src\\\\components\\\\charts\\\\ProgressChart.js\";\nimport React from 'react';\nimport { <PERSON><PERSON><PERSON>, Pie, Cell, BarChart, Bar, XAxis, YAxis, CartesianGrid, Tooltip, Legend, ResponsiveContainer, LineChart, Line, ReferenceLine } from 'recharts';\nimport { Box, Typography, Grid, Paper } from '@mui/material';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst COLORS = {\n  primary: '#2c3e50',\n  secondary: '#34495e',\n  success: '#3498db',\n  warning: '#5d6d7e',\n  info: '#85929e',\n  error: '#566573',\n  light: '#ecf0f1',\n  dark: '#2c3e50',\n  accent: '#7fb3d3'\n};\nconst ProgressChart = ({\n  data\n}) => {\n  if (!data) return null;\n\n  // Dati per il grafico a torta dell'avanzamento\n  const progressData = [{\n    name: '<PERSON><PERSON> Posati',\n    value: data.metri_posati,\n    color: COLORS.success\n  }, {\n    name: '<PERSON><PERSON>',\n    value: data.metri_da_posare,\n    color: COLORS.warning\n  }];\n\n  // Dati per il grafico a torta dei cavi\n  const caviData = [{\n    name: 'Cavi Posati',\n    value: data.cavi_posati,\n    color: COLORS.success\n  }, {\n    name: 'Cavi Rimanenti',\n    value: data.cavi_rimanenti,\n    color: COLORS.warning\n  }];\n\n  // Dati per il grafico a barre delle metriche principali\n  const metricsData = [{\n    name: 'Metri',\n    Totali: data.metri_totali,\n    Posati: data.metri_posati,\n    Rimanenti: data.metri_da_posare\n  }, {\n    name: 'Cavi',\n    Totali: data.totale_cavi,\n    Posati: data.cavi_posati,\n    Rimanenti: data.cavi_rimanenti\n  }];\n\n  // Dati per il grafico temporale della posa recente (ordinati cronologicamente)\n  const posaTrendData = data.posa_recente ? [...data.posa_recente].sort((a, b) => new Date(a.data.split('/').reverse().join('-')) - new Date(b.data.split('/').reverse().join('-'))).map(posa => ({\n    data: posa.data,\n    metri: parseFloat(posa.metri) || 0\n  })) : [];\n  const CustomTooltip = ({\n    active,\n    payload,\n    label\n  }) => {\n    if (active && payload && payload.length) {\n      return /*#__PURE__*/_jsxDEV(Paper, {\n        sx: {\n          p: 1,\n          border: '1px solid #ccc'\n        },\n        children: [/*#__PURE__*/_jsxDEV(Typography, {\n          variant: \"body2\",\n          children: `${label}`\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 92,\n          columnNumber: 11\n        }, this), payload.map((entry, index) => /*#__PURE__*/_jsxDEV(Typography, {\n          variant: \"body2\",\n          style: {\n            color: entry.color\n          },\n          children: `${entry.name}: ${entry.value}`\n        }, index, false, {\n          fileName: _jsxFileName,\n          lineNumber: 94,\n          columnNumber: 13\n        }, this))]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 91,\n        columnNumber: 9\n      }, this);\n    }\n    return null;\n  };\n  const renderCustomizedLabel = ({\n    cx,\n    cy,\n    midAngle,\n    innerRadius,\n    outerRadius,\n    percent\n  }) => {\n    if (percent < 0.05) return null; // Non mostrare etichette per fette troppo piccole\n\n    const RADIAN = Math.PI / 180;\n    const radius = innerRadius + (outerRadius - innerRadius) * 0.5;\n    const x = cx + radius * Math.cos(-midAngle * RADIAN);\n    const y = cy + radius * Math.sin(-midAngle * RADIAN);\n    return /*#__PURE__*/_jsxDEV(\"text\", {\n      x: x,\n      y: y,\n      fill: \"white\",\n      textAnchor: x > cx ? 'start' : 'end',\n      dominantBaseline: \"central\",\n      fontSize: \"12\",\n      fontWeight: \"bold\",\n      children: `${(percent * 100).toFixed(0)}%`\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 113,\n      columnNumber: 7\n    }, this);\n  };\n  return /*#__PURE__*/_jsxDEV(Paper, {\n    sx: {\n      p: 3,\n      border: '1px solid #e0e0e0',\n      borderRadius: 2,\n      width: '100%'\n    },\n    children: /*#__PURE__*/_jsxDEV(Grid, {\n      container: true,\n      spacing: 3,\n      children: [data.posa_recente && data.posa_recente.length > 0 && /*#__PURE__*/_jsxDEV(Grid, {\n        item: true,\n        xs: 12,\n        children: /*#__PURE__*/_jsxDEV(Box, {\n          sx: {\n            border: '1px solid #e0e0e0',\n            borderRadius: 2,\n            overflow: 'hidden',\n            height: '500px',\n            width: '100%'\n          },\n          children: [/*#__PURE__*/_jsxDEV(Box, {\n            sx: {\n              bgcolor: '#f8f9fa',\n              p: 2,\n              borderBottom: '1px solid #e0e0e0'\n            },\n            children: /*#__PURE__*/_jsxDEV(Box, {\n              sx: {\n                display: 'flex',\n                justifyContent: 'space-between',\n                alignItems: 'center'\n              },\n              children: [/*#__PURE__*/_jsxDEV(Typography, {\n                variant: \"h6\",\n                sx: {\n                  fontWeight: 600,\n                  color: '#2c3e50'\n                },\n                children: \"\\uD83D\\uDCC8 Andamento Posa Giornaliera\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 146,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(Box, {\n                sx: {\n                  display: 'flex',\n                  alignItems: 'center',\n                  gap: 2\n                },\n                children: [/*#__PURE__*/_jsxDEV(Typography, {\n                  variant: \"caption\",\n                  sx: {\n                    color: '#666'\n                  },\n                  children: [\"Media: \", data.media_giornaliera || 0, \"m/giorno\"]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 150,\n                  columnNumber: 19\n                }, this), data.giorni_lavorativi_effettivi && /*#__PURE__*/_jsxDEV(Typography, {\n                  variant: \"caption\",\n                  sx: {\n                    color: '#666'\n                  },\n                  children: [data.giorni_lavorativi_effettivi, \" giorni di lavoro\"]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 154,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 149,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 145,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 140,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Box, {\n            sx: {\n              p: 3,\n              height: 'calc(100% - 80px)'\n            },\n            children: /*#__PURE__*/_jsxDEV(ResponsiveContainer, {\n              width: \"100%\",\n              height: \"100%\",\n              children: /*#__PURE__*/_jsxDEV(LineChart, {\n                data: posaTrendData,\n                margin: {\n                  top: 30,\n                  right: 50,\n                  left: 40,\n                  bottom: 80\n                },\n                children: [/*#__PURE__*/_jsxDEV(CartesianGrid, {\n                  strokeDasharray: \"3 3\",\n                  stroke: \"#e0e0e0\",\n                  horizontal: true,\n                  vertical: false\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 172,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(XAxis, {\n                  dataKey: \"data\",\n                  stroke: \"#666\",\n                  fontSize: 12,\n                  angle: -45,\n                  textAnchor: \"end\",\n                  height: 60,\n                  interval: 0\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 178,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(YAxis, {\n                  stroke: \"#666\",\n                  fontSize: 12,\n                  label: {\n                    value: 'Metri Posati',\n                    angle: -90,\n                    position: 'insideLeft',\n                    style: {\n                      textAnchor: 'middle',\n                      fill: '#666'\n                    }\n                  }\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 187,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(Tooltip, {\n                  content: ({\n                    active,\n                    payload,\n                    label\n                  }) => {\n                    if (active && payload && payload.length) {\n                      return /*#__PURE__*/_jsxDEV(Box, {\n                        sx: {\n                          bgcolor: 'white',\n                          p: 2,\n                          border: '1px solid #e0e0e0',\n                          borderRadius: 1,\n                          boxShadow: '0 4px 8px rgba(0,0,0,0.1)'\n                        },\n                        children: [/*#__PURE__*/_jsxDEV(Typography, {\n                          variant: \"body2\",\n                          sx: {\n                            fontWeight: 600,\n                            mb: 1\n                          },\n                          children: label\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 208,\n                          columnNumber: 29\n                        }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                          variant: \"body2\",\n                          sx: {\n                            color: COLORS.primary\n                          },\n                          children: [\"\\uD83D\\uDCCF \", payload[0].value, \"m posati\"]\n                        }, void 0, true, {\n                          fileName: _jsxFileName,\n                          lineNumber: 211,\n                          columnNumber: 29\n                        }, this), data.media_giornaliera && /*#__PURE__*/_jsxDEV(Typography, {\n                          variant: \"caption\",\n                          sx: {\n                            color: '#666',\n                            display: 'block',\n                            mt: 0.5\n                          },\n                          children: [\"Media: \", data.media_giornaliera, \"m/giorno\"]\n                        }, void 0, true, {\n                          fileName: _jsxFileName,\n                          lineNumber: 215,\n                          columnNumber: 31\n                        }, this)]\n                      }, void 0, true, {\n                        fileName: _jsxFileName,\n                        lineNumber: 201,\n                        columnNumber: 27\n                      }, this);\n                    }\n                    return null;\n                  }\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 197,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(Line, {\n                  type: \"monotone\",\n                  dataKey: \"metri\",\n                  stroke: COLORS.success,\n                  strokeWidth: 4,\n                  dot: {\n                    fill: COLORS.success,\n                    strokeWidth: 3,\n                    r: 6,\n                    stroke: 'white'\n                  },\n                  activeDot: {\n                    r: 8,\n                    fill: COLORS.success,\n                    stroke: 'white',\n                    strokeWidth: 3\n                  }\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 225,\n                  columnNumber: 19\n                }, this), data.media_giornaliera && /*#__PURE__*/_jsxDEV(ReferenceLine, {\n                  y: data.media_giornaliera,\n                  stroke: COLORS.warning,\n                  strokeDasharray: \"5 5\",\n                  strokeWidth: 2,\n                  label: {\n                    value: `Media: ${data.media_giornaliera}m`,\n                    position: \"topRight\",\n                    style: {\n                      fill: COLORS.warning,\n                      fontSize: '12px',\n                      fontWeight: 600\n                    }\n                  }\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 245,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 163,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 162,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 161,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 133,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 132,\n        columnNumber: 11\n      }, this), (!data.posa_recente || data.posa_recente.length === 0) && /*#__PURE__*/_jsxDEV(Grid, {\n        item: true,\n        xs: 12,\n        children: /*#__PURE__*/_jsxDEV(Box, {\n          sx: {\n            border: '1px solid #e0e0e0',\n            borderRadius: 1,\n            p: 4,\n            textAlign: 'center',\n            bgcolor: '#f8f9fa'\n          },\n          children: [/*#__PURE__*/_jsxDEV(Typography, {\n            variant: \"h6\",\n            sx: {\n              color: '#666',\n              mb: 1\n            },\n            children: \"\\uD83D\\uDCCA Nessun Dato di Posa Disponibile\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 274,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(Typography, {\n            variant: \"body2\",\n            sx: {\n              color: '#999'\n            },\n            children: \"Il grafico dell'andamento temporale verr\\xE0 visualizzato non appena saranno registrati dati di posa giornaliera.\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 277,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 267,\n          columnNumber: 13\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 266,\n        columnNumber: 11\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 129,\n      columnNumber: 7\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 128,\n    columnNumber: 5\n  }, this);\n};\n_c = ProgressChart;\nexport default ProgressChart;\nvar _c;\n$RefreshReg$(_c, \"ProgressChart\");", "map": {"version": 3, "names": ["React", "<PERSON><PERSON><PERSON>", "Pie", "Cell", "<PERSON><PERSON><PERSON>", "Bar", "XAxis", "YA<PERSON>s", "Cartesian<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "Legend", "ResponsiveContainer", "Line<PERSON>hart", "Line", "ReferenceLine", "Box", "Typography", "Grid", "Paper", "jsxDEV", "_jsxDEV", "COLORS", "primary", "secondary", "success", "warning", "info", "error", "light", "dark", "accent", "ProgressChart", "data", "progressData", "name", "value", "metri_posati", "color", "metri_da_posare", "caviData", "cavi_posati", "cavi_rimanenti", "metricsData", "Totali", "metri_totali", "Posati", "<PERSON><PERSON><PERSON><PERSON>", "totale_cavi", "posaTrendData", "posa_recente", "sort", "a", "b", "Date", "split", "reverse", "join", "map", "posa", "metri", "parseFloat", "CustomTooltip", "active", "payload", "label", "length", "sx", "p", "border", "children", "variant", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "entry", "index", "style", "renderCustomizedLabel", "cx", "cy", "midAngle", "innerRadius", "outerRadius", "percent", "RADIAN", "Math", "PI", "radius", "x", "cos", "y", "sin", "fill", "textAnchor", "dominantBaseline", "fontSize", "fontWeight", "toFixed", "borderRadius", "width", "container", "spacing", "item", "xs", "overflow", "height", "bgcolor", "borderBottom", "display", "justifyContent", "alignItems", "gap", "media_giornaliera", "giorni_lavorativi_effettivi", "margin", "top", "right", "left", "bottom", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "stroke", "horizontal", "vertical", "dataKey", "angle", "interval", "position", "content", "boxShadow", "mb", "mt", "type", "strokeWidth", "dot", "r", "activeDot", "textAlign", "_c", "$RefreshReg$"], "sources": ["C:/CMS/webapp/frontend/src/components/charts/ProgressChart.js"], "sourcesContent": ["import React from 'react';\nimport {\n  <PERSON><PERSON><PERSON>,\n  Pie,\n  Cell,\n  BarChart,\n  Bar,\n  XAxis,\n  YAxis,\n  CartesianGrid,\n  Tooltip,\n  Legend,\n  ResponsiveContainer,\n  LineChart,\n  Line,\n  ReferenceLine\n} from 'recharts';\nimport { Box, Typography, Grid, Paper } from '@mui/material';\n\nconst COLORS = {\n  primary: '#2c3e50',\n  secondary: '#34495e',\n  success: '#3498db',\n  warning: '#5d6d7e',\n  info: '#85929e',\n  error: '#566573',\n  light: '#ecf0f1',\n  dark: '#2c3e50',\n  accent: '#7fb3d3'\n};\n\nconst ProgressChart = ({ data }) => {\n  if (!data) return null;\n\n  // Dati per il grafico a torta dell'avanzamento\n  const progressData = [\n    {\n      name: 'Metri Posati',\n      value: data.metri_posati,\n      color: COLORS.success\n    },\n    {\n      name: 'Metri Rimanenti',\n      value: data.metri_da_posare,\n      color: COLORS.warning\n    }\n  ];\n\n  // Dati per il grafico a torta dei cavi\n  const caviData = [\n    {\n      name: '<PERSON><PERSON>',\n      value: data.cavi_posati,\n      color: COLORS.success\n    },\n    {\n      name: '<PERSON><PERSON>enti',\n      value: data.cavi_rimanenti,\n      color: COLORS.warning\n    }\n  ];\n\n  // Dati per il grafico a barre delle metriche principali\n  const metricsData = [\n    {\n      name: 'Metri',\n      Totali: data.metri_totali,\n      Posati: data.metri_posati,\n      Rimanenti: data.metri_da_posare\n    },\n    {\n      name: 'Cavi',\n      Totali: data.totale_cavi,\n      Posati: data.cavi_posati,\n      Rimanenti: data.cavi_rimanenti\n    }\n  ];\n\n  // Dati per il grafico temporale della posa recente (ordinati cronologicamente)\n  const posaTrendData = data.posa_recente ?\n    [...data.posa_recente]\n      .sort((a, b) => new Date(a.data.split('/').reverse().join('-')) - new Date(b.data.split('/').reverse().join('-')))\n      .map(posa => ({\n        data: posa.data,\n        metri: parseFloat(posa.metri) || 0\n      })) : [];\n\n  const CustomTooltip = ({ active, payload, label }) => {\n    if (active && payload && payload.length) {\n      return (\n        <Paper sx={{ p: 1, border: '1px solid #ccc' }}>\n          <Typography variant=\"body2\">{`${label}`}</Typography>\n          {payload.map((entry, index) => (\n            <Typography key={index} variant=\"body2\" style={{ color: entry.color }}>\n              {`${entry.name}: ${entry.value}`}\n            </Typography>\n          ))}\n        </Paper>\n      );\n    }\n    return null;\n  };\n\n  const renderCustomizedLabel = ({ cx, cy, midAngle, innerRadius, outerRadius, percent }) => {\n    if (percent < 0.05) return null; // Non mostrare etichette per fette troppo piccole\n\n    const RADIAN = Math.PI / 180;\n    const radius = innerRadius + (outerRadius - innerRadius) * 0.5;\n    const x = cx + radius * Math.cos(-midAngle * RADIAN);\n    const y = cy + radius * Math.sin(-midAngle * RADIAN);\n\n    return (\n      <text\n        x={x}\n        y={y}\n        fill=\"white\"\n        textAnchor={x > cx ? 'start' : 'end'}\n        dominantBaseline=\"central\"\n        fontSize=\"12\"\n        fontWeight=\"bold\"\n      >\n        {`${(percent * 100).toFixed(0)}%`}\n      </text>\n    );\n  };\n\n  return (\n    <Paper sx={{ p: 3, border: '1px solid #e0e0e0', borderRadius: 2, width: '100%' }}>\n      <Grid container spacing={3}>\n        {/* Grafico Posa Giornaliera - Andamento Temporale */}\n        {data.posa_recente && data.posa_recente.length > 0 && (\n          <Grid item xs={12}>\n          <Box sx={{\n            border: '1px solid #e0e0e0',\n            borderRadius: 2,\n            overflow: 'hidden',\n            height: '500px',\n            width: '100%'\n          }}>\n            <Box sx={{\n              bgcolor: '#f8f9fa',\n              p: 2,\n              borderBottom: '1px solid #e0e0e0'\n            }}>\n              <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>\n                <Typography variant=\"h6\" sx={{ fontWeight: 600, color: '#2c3e50' }}>\n                  📈 Andamento Posa Giornaliera\n                </Typography>\n                <Box sx={{ display: 'flex', alignItems: 'center', gap: 2 }}>\n                  <Typography variant=\"caption\" sx={{ color: '#666' }}>\n                    Media: {data.media_giornaliera || 0}m/giorno\n                  </Typography>\n                  {data.giorni_lavorativi_effettivi && (\n                    <Typography variant=\"caption\" sx={{ color: '#666' }}>\n                      {data.giorni_lavorativi_effettivi} giorni di lavoro\n                    </Typography>\n                  )}\n                </Box>\n              </Box>\n            </Box>\n            <Box sx={{ p: 3, height: 'calc(100% - 80px)' }}>\n              <ResponsiveContainer width=\"100%\" height=\"100%\">\n                <LineChart\n                  data={posaTrendData}\n                  margin={{\n                    top: 30,\n                    right: 50,\n                    left: 40,\n                    bottom: 80\n                  }}\n                >\n                  <CartesianGrid\n                    strokeDasharray=\"3 3\"\n                    stroke=\"#e0e0e0\"\n                    horizontal={true}\n                    vertical={false}\n                  />\n                  <XAxis\n                    dataKey=\"data\"\n                    stroke=\"#666\"\n                    fontSize={12}\n                    angle={-45}\n                    textAnchor=\"end\"\n                    height={60}\n                    interval={0}\n                  />\n                  <YAxis\n                    stroke=\"#666\"\n                    fontSize={12}\n                    label={{\n                      value: 'Metri Posati',\n                      angle: -90,\n                      position: 'insideLeft',\n                      style: { textAnchor: 'middle', fill: '#666' }\n                    }}\n                  />\n                  <Tooltip\n                    content={({ active, payload, label }) => {\n                      if (active && payload && payload.length) {\n                        return (\n                          <Box sx={{\n                            bgcolor: 'white',\n                            p: 2,\n                            border: '1px solid #e0e0e0',\n                            borderRadius: 1,\n                            boxShadow: '0 4px 8px rgba(0,0,0,0.1)'\n                          }}>\n                            <Typography variant=\"body2\" sx={{ fontWeight: 600, mb: 1 }}>\n                              {label}\n                            </Typography>\n                            <Typography variant=\"body2\" sx={{ color: COLORS.primary }}>\n                              📏 {payload[0].value}m posati\n                            </Typography>\n                            {data.media_giornaliera && (\n                              <Typography variant=\"caption\" sx={{ color: '#666', display: 'block', mt: 0.5 }}>\n                                Media: {data.media_giornaliera}m/giorno\n                              </Typography>\n                            )}\n                          </Box>\n                        );\n                      }\n                      return null;\n                    }}\n                  />\n                  <Line\n                    type=\"monotone\"\n                    dataKey=\"metri\"\n                    stroke={COLORS.success}\n                    strokeWidth={4}\n                    dot={{\n                      fill: COLORS.success,\n                      strokeWidth: 3,\n                      r: 6,\n                      stroke: 'white'\n                    }}\n                    activeDot={{\n                      r: 8,\n                      fill: COLORS.success,\n                      stroke: 'white',\n                      strokeWidth: 3\n                    }}\n                  />\n                  {/* Linea della media */}\n                  {data.media_giornaliera && (\n                    <ReferenceLine\n                      y={data.media_giornaliera}\n                      stroke={COLORS.warning}\n                      strokeDasharray=\"5 5\"\n                      strokeWidth={2}\n                      label={{\n                        value: `Media: ${data.media_giornaliera}m`,\n                        position: \"topRight\",\n                        style: { fill: COLORS.warning, fontSize: '12px', fontWeight: 600 }\n                      }}\n                    />\n                  )}\n                </LineChart>\n              </ResponsiveContainer>\n            </Box>\n          </Box>\n        </Grid>\n      )}\n\n        {/* Messaggio quando non ci sono dati di posa */}\n        {(!data.posa_recente || data.posa_recente.length === 0) && (\n          <Grid item xs={12}>\n            <Box sx={{\n              border: '1px solid #e0e0e0',\n              borderRadius: 1,\n              p: 4,\n              textAlign: 'center',\n              bgcolor: '#f8f9fa'\n            }}>\n              <Typography variant=\"h6\" sx={{ color: '#666', mb: 1 }}>\n                📊 Nessun Dato di Posa Disponibile\n              </Typography>\n              <Typography variant=\"body2\" sx={{ color: '#999' }}>\n                Il grafico dell'andamento temporale verrà visualizzato non appena saranno registrati dati di posa giornaliera.\n              </Typography>\n            </Box>\n          </Grid>\n        )}\n      </Grid>\n    </Paper>\n  );\n};\n\nexport default ProgressChart;\n"], "mappings": ";AAAA,OAAOA,KAAK,MAAM,OAAO;AACzB,SACEC,QAAQ,EACRC,GAAG,EACHC,IAAI,EACJC,QAAQ,EACRC,GAAG,EACHC,KAAK,EACLC,KAAK,EACLC,aAAa,EACbC,OAAO,EACPC,MAAM,EACNC,mBAAmB,EACnBC,SAAS,EACTC,IAAI,EACJC,aAAa,QACR,UAAU;AACjB,SAASC,GAAG,EAAEC,UAAU,EAAEC,IAAI,EAAEC,KAAK,QAAQ,eAAe;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAE7D,MAAMC,MAAM,GAAG;EACbC,OAAO,EAAE,SAAS;EAClBC,SAAS,EAAE,SAAS;EACpBC,OAAO,EAAE,SAAS;EAClBC,OAAO,EAAE,SAAS;EAClBC,IAAI,EAAE,SAAS;EACfC,KAAK,EAAE,SAAS;EAChBC,KAAK,EAAE,SAAS;EAChBC,IAAI,EAAE,SAAS;EACfC,MAAM,EAAE;AACV,CAAC;AAED,MAAMC,aAAa,GAAGA,CAAC;EAAEC;AAAK,CAAC,KAAK;EAClC,IAAI,CAACA,IAAI,EAAE,OAAO,IAAI;;EAEtB;EACA,MAAMC,YAAY,GAAG,CACnB;IACEC,IAAI,EAAE,cAAc;IACpBC,KAAK,EAAEH,IAAI,CAACI,YAAY;IACxBC,KAAK,EAAEhB,MAAM,CAACG;EAChB,CAAC,EACD;IACEU,IAAI,EAAE,iBAAiB;IACvBC,KAAK,EAAEH,IAAI,CAACM,eAAe;IAC3BD,KAAK,EAAEhB,MAAM,CAACI;EAChB,CAAC,CACF;;EAED;EACA,MAAMc,QAAQ,GAAG,CACf;IACEL,IAAI,EAAE,aAAa;IACnBC,KAAK,EAAEH,IAAI,CAACQ,WAAW;IACvBH,KAAK,EAAEhB,MAAM,CAACG;EAChB,CAAC,EACD;IACEU,IAAI,EAAE,gBAAgB;IACtBC,KAAK,EAAEH,IAAI,CAACS,cAAc;IAC1BJ,KAAK,EAAEhB,MAAM,CAACI;EAChB,CAAC,CACF;;EAED;EACA,MAAMiB,WAAW,GAAG,CAClB;IACER,IAAI,EAAE,OAAO;IACbS,MAAM,EAAEX,IAAI,CAACY,YAAY;IACzBC,MAAM,EAAEb,IAAI,CAACI,YAAY;IACzBU,SAAS,EAAEd,IAAI,CAACM;EAClB,CAAC,EACD;IACEJ,IAAI,EAAE,MAAM;IACZS,MAAM,EAAEX,IAAI,CAACe,WAAW;IACxBF,MAAM,EAAEb,IAAI,CAACQ,WAAW;IACxBM,SAAS,EAAEd,IAAI,CAACS;EAClB,CAAC,CACF;;EAED;EACA,MAAMO,aAAa,GAAGhB,IAAI,CAACiB,YAAY,GACrC,CAAC,GAAGjB,IAAI,CAACiB,YAAY,CAAC,CACnBC,IAAI,CAAC,CAACC,CAAC,EAAEC,CAAC,KAAK,IAAIC,IAAI,CAACF,CAAC,CAACnB,IAAI,CAACsB,KAAK,CAAC,GAAG,CAAC,CAACC,OAAO,CAAC,CAAC,CAACC,IAAI,CAAC,GAAG,CAAC,CAAC,GAAG,IAAIH,IAAI,CAACD,CAAC,CAACpB,IAAI,CAACsB,KAAK,CAAC,GAAG,CAAC,CAACC,OAAO,CAAC,CAAC,CAACC,IAAI,CAAC,GAAG,CAAC,CAAC,CAAC,CACjHC,GAAG,CAACC,IAAI,KAAK;IACZ1B,IAAI,EAAE0B,IAAI,CAAC1B,IAAI;IACf2B,KAAK,EAAEC,UAAU,CAACF,IAAI,CAACC,KAAK,CAAC,IAAI;EACnC,CAAC,CAAC,CAAC,GAAG,EAAE;EAEZ,MAAME,aAAa,GAAGA,CAAC;IAAEC,MAAM;IAAEC,OAAO;IAAEC;EAAM,CAAC,KAAK;IACpD,IAAIF,MAAM,IAAIC,OAAO,IAAIA,OAAO,CAACE,MAAM,EAAE;MACvC,oBACE7C,OAAA,CAACF,KAAK;QAACgD,EAAE,EAAE;UAAEC,CAAC,EAAE,CAAC;UAAEC,MAAM,EAAE;QAAiB,CAAE;QAAAC,QAAA,gBAC5CjD,OAAA,CAACJ,UAAU;UAACsD,OAAO,EAAC,OAAO;UAAAD,QAAA,EAAE,GAAGL,KAAK;QAAE;UAAAO,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAa,CAAC,EACpDX,OAAO,CAACN,GAAG,CAAC,CAACkB,KAAK,EAAEC,KAAK,kBACxBxD,OAAA,CAACJ,UAAU;UAAasD,OAAO,EAAC,OAAO;UAACO,KAAK,EAAE;YAAExC,KAAK,EAAEsC,KAAK,CAACtC;UAAM,CAAE;UAAAgC,QAAA,EACnE,GAAGM,KAAK,CAACzC,IAAI,KAAKyC,KAAK,CAACxC,KAAK;QAAE,GADjByC,KAAK;UAAAL,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAEV,CACb,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACG,CAAC;IAEZ;IACA,OAAO,IAAI;EACb,CAAC;EAED,MAAMI,qBAAqB,GAAGA,CAAC;IAAEC,EAAE;IAAEC,EAAE;IAAEC,QAAQ;IAAEC,WAAW;IAAEC,WAAW;IAAEC;EAAQ,CAAC,KAAK;IACzF,IAAIA,OAAO,GAAG,IAAI,EAAE,OAAO,IAAI,CAAC,CAAC;;IAEjC,MAAMC,MAAM,GAAGC,IAAI,CAACC,EAAE,GAAG,GAAG;IAC5B,MAAMC,MAAM,GAAGN,WAAW,GAAG,CAACC,WAAW,GAAGD,WAAW,IAAI,GAAG;IAC9D,MAAMO,CAAC,GAAGV,EAAE,GAAGS,MAAM,GAAGF,IAAI,CAACI,GAAG,CAAC,CAACT,QAAQ,GAAGI,MAAM,CAAC;IACpD,MAAMM,CAAC,GAAGX,EAAE,GAAGQ,MAAM,GAAGF,IAAI,CAACM,GAAG,CAAC,CAACX,QAAQ,GAAGI,MAAM,CAAC;IAEpD,oBACEjE,OAAA;MACEqE,CAAC,EAAEA,CAAE;MACLE,CAAC,EAAEA,CAAE;MACLE,IAAI,EAAC,OAAO;MACZC,UAAU,EAAEL,CAAC,GAAGV,EAAE,GAAG,OAAO,GAAG,KAAM;MACrCgB,gBAAgB,EAAC,SAAS;MAC1BC,QAAQ,EAAC,IAAI;MACbC,UAAU,EAAC,MAAM;MAAA5B,QAAA,EAEhB,GAAG,CAACe,OAAO,GAAG,GAAG,EAAEc,OAAO,CAAC,CAAC,CAAC;IAAG;MAAA3B,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAC7B,CAAC;EAEX,CAAC;EAED,oBACEtD,OAAA,CAACF,KAAK;IAACgD,EAAE,EAAE;MAAEC,CAAC,EAAE,CAAC;MAAEC,MAAM,EAAE,mBAAmB;MAAE+B,YAAY,EAAE,CAAC;MAAEC,KAAK,EAAE;IAAO,CAAE;IAAA/B,QAAA,eAC/EjD,OAAA,CAACH,IAAI;MAACoF,SAAS;MAACC,OAAO,EAAE,CAAE;MAAAjC,QAAA,GAExBrC,IAAI,CAACiB,YAAY,IAAIjB,IAAI,CAACiB,YAAY,CAACgB,MAAM,GAAG,CAAC,iBAChD7C,OAAA,CAACH,IAAI;QAACsF,IAAI;QAACC,EAAE,EAAE,EAAG;QAAAnC,QAAA,eAClBjD,OAAA,CAACL,GAAG;UAACmD,EAAE,EAAE;YACPE,MAAM,EAAE,mBAAmB;YAC3B+B,YAAY,EAAE,CAAC;YACfM,QAAQ,EAAE,QAAQ;YAClBC,MAAM,EAAE,OAAO;YACfN,KAAK,EAAE;UACT,CAAE;UAAA/B,QAAA,gBACAjD,OAAA,CAACL,GAAG;YAACmD,EAAE,EAAE;cACPyC,OAAO,EAAE,SAAS;cAClBxC,CAAC,EAAE,CAAC;cACJyC,YAAY,EAAE;YAChB,CAAE;YAAAvC,QAAA,eACAjD,OAAA,CAACL,GAAG;cAACmD,EAAE,EAAE;gBAAE2C,OAAO,EAAE,MAAM;gBAAEC,cAAc,EAAE,eAAe;gBAAEC,UAAU,EAAE;cAAS,CAAE;cAAA1C,QAAA,gBAClFjD,OAAA,CAACJ,UAAU;gBAACsD,OAAO,EAAC,IAAI;gBAACJ,EAAE,EAAE;kBAAE+B,UAAU,EAAE,GAAG;kBAAE5D,KAAK,EAAE;gBAAU,CAAE;gBAAAgC,QAAA,EAAC;cAEpE;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAY,CAAC,eACbtD,OAAA,CAACL,GAAG;gBAACmD,EAAE,EAAE;kBAAE2C,OAAO,EAAE,MAAM;kBAAEE,UAAU,EAAE,QAAQ;kBAAEC,GAAG,EAAE;gBAAE,CAAE;gBAAA3C,QAAA,gBACzDjD,OAAA,CAACJ,UAAU;kBAACsD,OAAO,EAAC,SAAS;kBAACJ,EAAE,EAAE;oBAAE7B,KAAK,EAAE;kBAAO,CAAE;kBAAAgC,QAAA,GAAC,SAC5C,EAACrC,IAAI,CAACiF,iBAAiB,IAAI,CAAC,EAAC,UACtC;gBAAA;kBAAA1C,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAY,CAAC,EACZ1C,IAAI,CAACkF,2BAA2B,iBAC/B9F,OAAA,CAACJ,UAAU;kBAACsD,OAAO,EAAC,SAAS;kBAACJ,EAAE,EAAE;oBAAE7B,KAAK,EAAE;kBAAO,CAAE;kBAAAgC,QAAA,GACjDrC,IAAI,CAACkF,2BAA2B,EAAC,mBACpC;gBAAA;kBAAA3C,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAY,CACb;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACE,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC,eACNtD,OAAA,CAACL,GAAG;YAACmD,EAAE,EAAE;cAAEC,CAAC,EAAE,CAAC;cAAEuC,MAAM,EAAE;YAAoB,CAAE;YAAArC,QAAA,eAC7CjD,OAAA,CAACT,mBAAmB;cAACyF,KAAK,EAAC,MAAM;cAACM,MAAM,EAAC,MAAM;cAAArC,QAAA,eAC7CjD,OAAA,CAACR,SAAS;gBACRoB,IAAI,EAAEgB,aAAc;gBACpBmE,MAAM,EAAE;kBACNC,GAAG,EAAE,EAAE;kBACPC,KAAK,EAAE,EAAE;kBACTC,IAAI,EAAE,EAAE;kBACRC,MAAM,EAAE;gBACV,CAAE;gBAAAlD,QAAA,gBAEFjD,OAAA,CAACZ,aAAa;kBACZgH,eAAe,EAAC,KAAK;kBACrBC,MAAM,EAAC,SAAS;kBAChBC,UAAU,EAAE,IAAK;kBACjBC,QAAQ,EAAE;gBAAM;kBAAApD,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACjB,CAAC,eACFtD,OAAA,CAACd,KAAK;kBACJsH,OAAO,EAAC,MAAM;kBACdH,MAAM,EAAC,MAAM;kBACbzB,QAAQ,EAAE,EAAG;kBACb6B,KAAK,EAAE,CAAC,EAAG;kBACX/B,UAAU,EAAC,KAAK;kBAChBY,MAAM,EAAE,EAAG;kBACXoB,QAAQ,EAAE;gBAAE;kBAAAvD,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACb,CAAC,eACFtD,OAAA,CAACb,KAAK;kBACJkH,MAAM,EAAC,MAAM;kBACbzB,QAAQ,EAAE,EAAG;kBACbhC,KAAK,EAAE;oBACL7B,KAAK,EAAE,cAAc;oBACrB0F,KAAK,EAAE,CAAC,EAAE;oBACVE,QAAQ,EAAE,YAAY;oBACtBlD,KAAK,EAAE;sBAAEiB,UAAU,EAAE,QAAQ;sBAAED,IAAI,EAAE;oBAAO;kBAC9C;gBAAE;kBAAAtB,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH,CAAC,eACFtD,OAAA,CAACX,OAAO;kBACNuH,OAAO,EAAEA,CAAC;oBAAElE,MAAM;oBAAEC,OAAO;oBAAEC;kBAAM,CAAC,KAAK;oBACvC,IAAIF,MAAM,IAAIC,OAAO,IAAIA,OAAO,CAACE,MAAM,EAAE;sBACvC,oBACE7C,OAAA,CAACL,GAAG;wBAACmD,EAAE,EAAE;0BACPyC,OAAO,EAAE,OAAO;0BAChBxC,CAAC,EAAE,CAAC;0BACJC,MAAM,EAAE,mBAAmB;0BAC3B+B,YAAY,EAAE,CAAC;0BACf8B,SAAS,EAAE;wBACb,CAAE;wBAAA5D,QAAA,gBACAjD,OAAA,CAACJ,UAAU;0BAACsD,OAAO,EAAC,OAAO;0BAACJ,EAAE,EAAE;4BAAE+B,UAAU,EAAE,GAAG;4BAAEiC,EAAE,EAAE;0BAAE,CAAE;0BAAA7D,QAAA,EACxDL;wBAAK;0BAAAO,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OACI,CAAC,eACbtD,OAAA,CAACJ,UAAU;0BAACsD,OAAO,EAAC,OAAO;0BAACJ,EAAE,EAAE;4BAAE7B,KAAK,EAAEhB,MAAM,CAACC;0BAAQ,CAAE;0BAAA+C,QAAA,GAAC,eACtD,EAACN,OAAO,CAAC,CAAC,CAAC,CAAC5B,KAAK,EAAC,UACvB;wBAAA;0BAAAoC,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAY,CAAC,EACZ1C,IAAI,CAACiF,iBAAiB,iBACrB7F,OAAA,CAACJ,UAAU;0BAACsD,OAAO,EAAC,SAAS;0BAACJ,EAAE,EAAE;4BAAE7B,KAAK,EAAE,MAAM;4BAAEwE,OAAO,EAAE,OAAO;4BAAEsB,EAAE,EAAE;0BAAI,CAAE;0BAAA9D,QAAA,GAAC,SACvE,EAACrC,IAAI,CAACiF,iBAAiB,EAAC,UACjC;wBAAA;0BAAA1C,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAY,CACb;sBAAA;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACE,CAAC;oBAEV;oBACA,OAAO,IAAI;kBACb;gBAAE;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH,CAAC,eACFtD,OAAA,CAACP,IAAI;kBACHuH,IAAI,EAAC,UAAU;kBACfR,OAAO,EAAC,OAAO;kBACfH,MAAM,EAAEpG,MAAM,CAACG,OAAQ;kBACvB6G,WAAW,EAAE,CAAE;kBACfC,GAAG,EAAE;oBACHzC,IAAI,EAAExE,MAAM,CAACG,OAAO;oBACpB6G,WAAW,EAAE,CAAC;oBACdE,CAAC,EAAE,CAAC;oBACJd,MAAM,EAAE;kBACV,CAAE;kBACFe,SAAS,EAAE;oBACTD,CAAC,EAAE,CAAC;oBACJ1C,IAAI,EAAExE,MAAM,CAACG,OAAO;oBACpBiG,MAAM,EAAE,OAAO;oBACfY,WAAW,EAAE;kBACf;gBAAE;kBAAA9D,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH,CAAC,EAED1C,IAAI,CAACiF,iBAAiB,iBACrB7F,OAAA,CAACN,aAAa;kBACZ6E,CAAC,EAAE3D,IAAI,CAACiF,iBAAkB;kBAC1BQ,MAAM,EAAEpG,MAAM,CAACI,OAAQ;kBACvB+F,eAAe,EAAC,KAAK;kBACrBa,WAAW,EAAE,CAAE;kBACfrE,KAAK,EAAE;oBACL7B,KAAK,EAAE,UAAUH,IAAI,CAACiF,iBAAiB,GAAG;oBAC1Cc,QAAQ,EAAE,UAAU;oBACpBlD,KAAK,EAAE;sBAAEgB,IAAI,EAAExE,MAAM,CAACI,OAAO;sBAAEuE,QAAQ,EAAE,MAAM;sBAAEC,UAAU,EAAE;oBAAI;kBACnE;gBAAE;kBAAA1B,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH,CACF;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACQ;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACO;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACnB,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACF,CACP,EAGE,CAAC,CAAC1C,IAAI,CAACiB,YAAY,IAAIjB,IAAI,CAACiB,YAAY,CAACgB,MAAM,KAAK,CAAC,kBACpD7C,OAAA,CAACH,IAAI;QAACsF,IAAI;QAACC,EAAE,EAAE,EAAG;QAAAnC,QAAA,eAChBjD,OAAA,CAACL,GAAG;UAACmD,EAAE,EAAE;YACPE,MAAM,EAAE,mBAAmB;YAC3B+B,YAAY,EAAE,CAAC;YACfhC,CAAC,EAAE,CAAC;YACJsE,SAAS,EAAE,QAAQ;YACnB9B,OAAO,EAAE;UACX,CAAE;UAAAtC,QAAA,gBACAjD,OAAA,CAACJ,UAAU;YAACsD,OAAO,EAAC,IAAI;YAACJ,EAAE,EAAE;cAAE7B,KAAK,EAAE,MAAM;cAAE6F,EAAE,EAAE;YAAE,CAAE;YAAA7D,QAAA,EAAC;UAEvD;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAY,CAAC,eACbtD,OAAA,CAACJ,UAAU;YAACsD,OAAO,EAAC,OAAO;YAACJ,EAAE,EAAE;cAAE7B,KAAK,EAAE;YAAO,CAAE;YAAAgC,QAAA,EAAC;UAEnD;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAY,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACV;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACF,CACP;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACG;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACF,CAAC;AAEZ,CAAC;AAACgE,EAAA,GA9PI3G,aAAa;AAgQnB,eAAeA,aAAa;AAAC,IAAA2G,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}