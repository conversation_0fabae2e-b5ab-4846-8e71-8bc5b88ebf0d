{"ast": null, "code": "import { constructNow } from \"./constructNow.mjs\";\nimport { isSameDay } from \"./isSameDay.mjs\";\nimport { subDays } from \"./subDays.mjs\";\n\n/**\n * @name isYesterday\n * @category Day Helpers\n * @summary Is the given date yesterday?\n * @pure false\n *\n * @description\n * Is the given date yesterday?\n *\n * @typeParam DateType - The `Date` type, the function operates on. Gets inferred from passed arguments. Allows to use extensions like [`UTCDate`](https://github.com/date-fns/utc).\n *\n * @param date - The date to check\n *\n * @returns The date is yesterday\n *\n * @example\n * // If today is 6 October 2014, is 5 October 14:00:00 yesterday?\n * const result = isYesterday(new Date(2014, 9, 5, 14, 0))\n * //=> true\n */\nexport function isYesterday(date) {\n  return isSameDay(date, subDays(constructNow(date), 1));\n}\n\n// Fallback for modularized imports:\nexport default isYesterday;", "map": {"version": 3, "names": ["constructNow", "isSameDay", "subDays", "isYesterday", "date"], "sources": ["C:/CMS/webapp/frontend/node_modules/date-fns/isYesterday.mjs"], "sourcesContent": ["import { constructNow } from \"./constructNow.mjs\";\nimport { isSameDay } from \"./isSameDay.mjs\";\nimport { subDays } from \"./subDays.mjs\";\n\n/**\n * @name isYesterday\n * @category Day Helpers\n * @summary Is the given date yesterday?\n * @pure false\n *\n * @description\n * Is the given date yesterday?\n *\n * @typeParam DateType - The `Date` type, the function operates on. Gets inferred from passed arguments. Allows to use extensions like [`UTCDate`](https://github.com/date-fns/utc).\n *\n * @param date - The date to check\n *\n * @returns The date is yesterday\n *\n * @example\n * // If today is 6 October 2014, is 5 October 14:00:00 yesterday?\n * const result = isYesterday(new Date(2014, 9, 5, 14, 0))\n * //=> true\n */\nexport function isYesterday(date) {\n  return isSameDay(date, subDays(constructNow(date), 1));\n}\n\n// Fallback for modularized imports:\nexport default isYesterday;\n"], "mappings": "AAAA,SAASA,YAAY,QAAQ,oBAAoB;AACjD,SAASC,SAAS,QAAQ,iBAAiB;AAC3C,SAASC,OAAO,QAAQ,eAAe;;AAEvC;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO,SAASC,WAAWA,CAACC,IAAI,EAAE;EAChC,OAAOH,SAAS,CAACG,IAAI,EAAEF,OAAO,CAACF,YAAY,CAACI,IAAI,CAAC,EAAE,CAAC,CAAC,CAAC;AACxD;;AAEA;AACA,eAAeD,WAAW", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}