{"ast": null, "code": "var _jsxFileName = \"C:\\\\CMS\\\\webapp\\\\frontend\\\\src\\\\pages\\\\comande\\\\ComandePage.js\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect } from 'react';\nimport { Container, Typography, Box, Breadcrumbs, Link, Alert } from '@mui/material';\nimport { Home as HomeIcon, Assignment as ComandaIcon } from '@mui/icons-material';\nimport { useParams, useNavigate } from 'react-router-dom';\nimport ComandeList from '../../components/comande/ComandeList';\nimport { useAuth } from '../../context/AuthContext';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst ComandePage = () => {\n  _s();\n  const {\n    cantiereId\n  } = useParams();\n  const navigate = useNavigate();\n  const {\n    user,\n    isImpersonating,\n    impersonatedUser\n  } = useAuth();\n  const [cantiereName, setCantiereName] = useState('');\n  const [error, setError] = useState(null);\n\n  // Determina l'utente effettivo (impersonato o normale)\n  const effectiveUser = isImpersonating ? impersonatedUser : user;\n  useEffect(() => {\n    // Verifica che l'utente abbia accesso al cantiere\n    if (!cantiereId) {\n      setError('ID cantiere non specificato');\n      return;\n    }\n\n    // Carica il nome del cantiere (potresti voler fare una chiamata API qui)\n    // Per ora uso un placeholder\n    setCantiereName(`Cantiere ${cantiereId}`);\n  }, [cantiereId]);\n  const handleBreadcrumbClick = path => {\n    navigate(path);\n  };\n  if (error) {\n    return /*#__PURE__*/_jsxDEV(Container, {\n      maxWidth: \"lg\",\n      children: /*#__PURE__*/_jsxDEV(Box, {\n        sx: {\n          mt: 4\n        },\n        children: /*#__PURE__*/_jsxDEV(Alert, {\n          severity: \"error\",\n          children: error\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 48,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 47,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 46,\n      columnNumber: 7\n    }, this);\n  }\n  return /*#__PURE__*/_jsxDEV(Container, {\n    maxWidth: \"lg\",\n    children: /*#__PURE__*/_jsxDEV(Box, {\n      sx: {\n        mt: 4,\n        mb: 4\n      },\n      children: [/*#__PURE__*/_jsxDEV(Breadcrumbs, {\n        \"aria-label\": \"breadcrumb\",\n        sx: {\n          mb: 2\n        },\n        children: [/*#__PURE__*/_jsxDEV(Link, {\n          color: \"inherit\",\n          href: \"#\",\n          onClick: () => handleBreadcrumbClick('/'),\n          sx: {\n            display: 'flex',\n            alignItems: 'center',\n            textDecoration: 'none'\n          },\n          children: [/*#__PURE__*/_jsxDEV(HomeIcon, {\n            sx: {\n              mr: 0.5\n            },\n            fontSize: \"inherit\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 65,\n            columnNumber: 13\n          }, this), \"Home\"]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 59,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Link, {\n          color: \"inherit\",\n          href: \"#\",\n          onClick: () => handleBreadcrumbClick(`/cantieri/${cantiereId}`),\n          sx: {\n            textDecoration: 'none'\n          },\n          children: cantiereName\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 68,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Typography, {\n          color: \"text.primary\",\n          sx: {\n            display: 'flex',\n            alignItems: 'center'\n          },\n          children: [/*#__PURE__*/_jsxDEV(ComandaIcon, {\n            sx: {\n              mr: 0.5\n            },\n            fontSize: \"inherit\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 77,\n            columnNumber: 13\n          }, this), \"Comande\"]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 76,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 58,\n        columnNumber: 9\n      }, this), isImpersonating && /*#__PURE__*/_jsxDEV(Alert, {\n        severity: \"info\",\n        sx: {\n          mb: 2\n        },\n        children: [\"Stai visualizzando come: \", effectiveUser === null || effectiveUser === void 0 ? void 0 : effectiveUser.username, \" (\", effectiveUser === null || effectiveUser === void 0 ? void 0 : effectiveUser.role, \")\"]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 84,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(ComandeList, {\n        cantiereId: cantiereId,\n        cantiereName: cantiereName\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 90,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 56,\n      columnNumber: 7\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 55,\n    columnNumber: 5\n  }, this);\n};\n_s(ComandePage, \"tbOiftaTj8H3rDtZhWAdiMjUP8c=\", false, function () {\n  return [useParams, useNavigate, useAuth];\n});\n_c = ComandePage;\nexport default ComandePage;\nvar _c;\n$RefreshReg$(_c, \"ComandePage\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "Container", "Typography", "Box", "Breadcrumbs", "Link", "<PERSON><PERSON>", "Home", "HomeIcon", "Assignment", "ComandaIcon", "useParams", "useNavigate", "ComandeList", "useAuth", "jsxDEV", "_jsxDEV", "ComandePage", "_s", "cantiereId", "navigate", "user", "isImpersonating", "impersonated<PERSON><PERSON>", "cantiereName", "setCantiereName", "error", "setError", "effectiveUser", "handleBreadcrumbClick", "path", "max<PERSON><PERSON><PERSON>", "children", "sx", "mt", "severity", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "mb", "color", "href", "onClick", "display", "alignItems", "textDecoration", "mr", "fontSize", "username", "role", "_c", "$RefreshReg$"], "sources": ["C:/CMS/webapp/frontend/src/pages/comande/ComandePage.js"], "sourcesContent": ["import React, { useState, useEffect } from 'react';\nimport {\n  Container,\n  Typography,\n  Box,\n  Breadcrumbs,\n  Link,\n  Alert\n} from '@mui/material';\nimport {\n  Home as HomeIcon,\n  Assignment as ComandaIcon\n} from '@mui/icons-material';\nimport { useParams, useNavigate } from 'react-router-dom';\nimport ComandeList from '../../components/comande/ComandeList';\nimport { useAuth } from '../../context/AuthContext';\n\nconst ComandePage = () => {\n  const { cantiereId } = useParams();\n  const navigate = useNavigate();\n  const { user, isImpersonating, impersonatedUser } = useAuth();\n  const [cantiereName, setCantiereName] = useState('');\n  const [error, setError] = useState(null);\n\n  // Determina l'utente effettivo (impersonato o normale)\n  const effectiveUser = isImpersonating ? impersonatedUser : user;\n\n  useEffect(() => {\n    // Verifica che l'utente abbia accesso al cantiere\n    if (!cantiereId) {\n      setError('ID cantiere non specificato');\n      return;\n    }\n\n    // Carica il nome del cantiere (potresti voler fare una chiamata API qui)\n    // Per ora uso un placeholder\n    setCantiereName(`Cantiere ${cantiereId}`);\n  }, [cantiereId]);\n\n  const handleBreadcrumbClick = (path) => {\n    navigate(path);\n  };\n\n  if (error) {\n    return (\n      <Container maxWidth=\"lg\">\n        <Box sx={{ mt: 4 }}>\n          <Alert severity=\"error\">{error}</Alert>\n        </Box>\n      </Container>\n    );\n  }\n\n  return (\n    <Container maxWidth=\"lg\">\n      <Box sx={{ mt: 4, mb: 4 }}>\n        {/* Breadcrumbs */}\n        <Breadcrumbs aria-label=\"breadcrumb\" sx={{ mb: 2 }}>\n          <Link\n            color=\"inherit\"\n            href=\"#\"\n            onClick={() => handleBreadcrumbClick('/')}\n            sx={{ display: 'flex', alignItems: 'center', textDecoration: 'none' }}\n          >\n            <HomeIcon sx={{ mr: 0.5 }} fontSize=\"inherit\" />\n            Home\n          </Link>\n          <Link\n            color=\"inherit\"\n            href=\"#\"\n            onClick={() => handleBreadcrumbClick(`/cantieri/${cantiereId}`)}\n            sx={{ textDecoration: 'none' }}\n          >\n            {cantiereName}\n          </Link>\n          <Typography color=\"text.primary\" sx={{ display: 'flex', alignItems: 'center' }}>\n            <ComandaIcon sx={{ mr: 0.5 }} fontSize=\"inherit\" />\n            Comande\n          </Typography>\n        </Breadcrumbs>\n\n        {/* Informazioni utente se impersonato */}\n        {isImpersonating && (\n          <Alert severity=\"info\" sx={{ mb: 2 }}>\n            Stai visualizzando come: {effectiveUser?.username} ({effectiveUser?.role})\n          </Alert>\n        )}\n\n        {/* Componente principale */}\n        <ComandeList \n          cantiereId={cantiereId} \n          cantiereName={cantiereName}\n        />\n      </Box>\n    </Container>\n  );\n};\n\nexport default ComandePage;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAClD,SACEC,SAAS,EACTC,UAAU,EACVC,GAAG,EACHC,WAAW,EACXC,IAAI,EACJC,KAAK,QACA,eAAe;AACtB,SACEC,IAAI,IAAIC,QAAQ,EAChBC,UAAU,IAAIC,WAAW,QACpB,qBAAqB;AAC5B,SAASC,SAAS,EAAEC,WAAW,QAAQ,kBAAkB;AACzD,OAAOC,WAAW,MAAM,sCAAsC;AAC9D,SAASC,OAAO,QAAQ,2BAA2B;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAEpD,MAAMC,WAAW,GAAGA,CAAA,KAAM;EAAAC,EAAA;EACxB,MAAM;IAAEC;EAAW,CAAC,GAAGR,SAAS,CAAC,CAAC;EAClC,MAAMS,QAAQ,GAAGR,WAAW,CAAC,CAAC;EAC9B,MAAM;IAAES,IAAI;IAAEC,eAAe;IAAEC;EAAiB,CAAC,GAAGT,OAAO,CAAC,CAAC;EAC7D,MAAM,CAACU,YAAY,EAAEC,eAAe,CAAC,GAAG1B,QAAQ,CAAC,EAAE,CAAC;EACpD,MAAM,CAAC2B,KAAK,EAAEC,QAAQ,CAAC,GAAG5B,QAAQ,CAAC,IAAI,CAAC;;EAExC;EACA,MAAM6B,aAAa,GAAGN,eAAe,GAAGC,gBAAgB,GAAGF,IAAI;EAE/DrB,SAAS,CAAC,MAAM;IACd;IACA,IAAI,CAACmB,UAAU,EAAE;MACfQ,QAAQ,CAAC,6BAA6B,CAAC;MACvC;IACF;;IAEA;IACA;IACAF,eAAe,CAAC,YAAYN,UAAU,EAAE,CAAC;EAC3C,CAAC,EAAE,CAACA,UAAU,CAAC,CAAC;EAEhB,MAAMU,qBAAqB,GAAIC,IAAI,IAAK;IACtCV,QAAQ,CAACU,IAAI,CAAC;EAChB,CAAC;EAED,IAAIJ,KAAK,EAAE;IACT,oBACEV,OAAA,CAACf,SAAS;MAAC8B,QAAQ,EAAC,IAAI;MAAAC,QAAA,eACtBhB,OAAA,CAACb,GAAG;QAAC8B,EAAE,EAAE;UAAEC,EAAE,EAAE;QAAE,CAAE;QAAAF,QAAA,eACjBhB,OAAA,CAACV,KAAK;UAAC6B,QAAQ,EAAC,OAAO;UAAAH,QAAA,EAAEN;QAAK;UAAAU,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACpC;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACG,CAAC;EAEhB;EAEA,oBACEvB,OAAA,CAACf,SAAS;IAAC8B,QAAQ,EAAC,IAAI;IAAAC,QAAA,eACtBhB,OAAA,CAACb,GAAG;MAAC8B,EAAE,EAAE;QAAEC,EAAE,EAAE,CAAC;QAAEM,EAAE,EAAE;MAAE,CAAE;MAAAR,QAAA,gBAExBhB,OAAA,CAACZ,WAAW;QAAC,cAAW,YAAY;QAAC6B,EAAE,EAAE;UAAEO,EAAE,EAAE;QAAE,CAAE;QAAAR,QAAA,gBACjDhB,OAAA,CAACX,IAAI;UACHoC,KAAK,EAAC,SAAS;UACfC,IAAI,EAAC,GAAG;UACRC,OAAO,EAAEA,CAAA,KAAMd,qBAAqB,CAAC,GAAG,CAAE;UAC1CI,EAAE,EAAE;YAAEW,OAAO,EAAE,MAAM;YAAEC,UAAU,EAAE,QAAQ;YAAEC,cAAc,EAAE;UAAO,CAAE;UAAAd,QAAA,gBAEtEhB,OAAA,CAACR,QAAQ;YAACyB,EAAE,EAAE;cAAEc,EAAE,EAAE;YAAI,CAAE;YAACC,QAAQ,EAAC;UAAS;YAAAZ,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,QAElD;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CAAC,eACPvB,OAAA,CAACX,IAAI;UACHoC,KAAK,EAAC,SAAS;UACfC,IAAI,EAAC,GAAG;UACRC,OAAO,EAAEA,CAAA,KAAMd,qBAAqB,CAAC,aAAaV,UAAU,EAAE,CAAE;UAChEc,EAAE,EAAE;YAAEa,cAAc,EAAE;UAAO,CAAE;UAAAd,QAAA,EAE9BR;QAAY;UAAAY,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACT,CAAC,eACPvB,OAAA,CAACd,UAAU;UAACuC,KAAK,EAAC,cAAc;UAACR,EAAE,EAAE;YAAEW,OAAO,EAAE,MAAM;YAAEC,UAAU,EAAE;UAAS,CAAE;UAAAb,QAAA,gBAC7EhB,OAAA,CAACN,WAAW;YAACuB,EAAE,EAAE;cAAEc,EAAE,EAAE;YAAI,CAAE;YAACC,QAAQ,EAAC;UAAS;YAAAZ,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,WAErD;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAY,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACF,CAAC,EAGbjB,eAAe,iBACdN,OAAA,CAACV,KAAK;QAAC6B,QAAQ,EAAC,MAAM;QAACF,EAAE,EAAE;UAAEO,EAAE,EAAE;QAAE,CAAE;QAAAR,QAAA,GAAC,2BACX,EAACJ,aAAa,aAAbA,aAAa,uBAAbA,aAAa,CAAEqB,QAAQ,EAAC,IAAE,EAACrB,aAAa,aAAbA,aAAa,uBAAbA,aAAa,CAAEsB,IAAI,EAAC,GAC3E;MAAA;QAAAd,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAO,CACR,eAGDvB,OAAA,CAACH,WAAW;QACVM,UAAU,EAAEA,UAAW;QACvBK,YAAY,EAAEA;MAAa;QAAAY,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC5B,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACC;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACG,CAAC;AAEhB,CAAC;AAACrB,EAAA,CA/EID,WAAW;EAAA,QACQN,SAAS,EACfC,WAAW,EACwBE,OAAO;AAAA;AAAAqC,EAAA,GAHvDlC,WAAW;AAiFjB,eAAeA,WAAW;AAAC,IAAAkC,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}