{"ast": null, "code": "var _jsxFileName = \"C:\\\\CMS\\\\webapp\\\\frontend\\\\src\\\\components\\\\cantieri\\\\EditCantiereDialog.js\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect } from 'react';\nimport { Dialog, DialogTitle, DialogContent, DialogActions, Button, TextField, Box, Typography, Alert, IconButton, Divider, Grid, FormControl, InputLabel, Select, MenuItem } from '@mui/material';\nimport { Edit as EditIcon, Lock as LockIcon, Construction as ConstructionIcon, Business as BusinessIcon, LocationOn as LocationIcon, Description as DescriptionIcon } from '@mui/icons-material';\nimport cantieriService from '../../services/cantieriService';\nimport PasswordManagementDialog from './PasswordManagementDialog';\n\n/**\n * Dialog per la modifica dei dati del cantiere\n * Include accesso rapido alla gestione password\n */\nimport { jsxDEV as _jsxDEV, Fragment as _Fragment } from \"react/jsx-dev-runtime\";\nconst EditCantiereDialog = ({\n  open,\n  onClose,\n  cantiere,\n  onCantiereUpdated = null\n}) => {\n  _s();\n  const [loading, setLoading] = useState(false);\n  const [error, setError] = useState('');\n  const [success, setSuccess] = useState('');\n  const [showPasswordDialog, setShowPasswordDialog] = useState(false);\n  const [formData, setFormData] = useState({\n    commessa: '',\n    descrizione: '',\n    nome_cliente: '',\n    indirizzo_cantiere: '',\n    citta_cantiere: '',\n    nazione_cantiere: '',\n    riferimenti_normativi: '',\n    documentazione_progetto: ''\n  });\n\n  // Inizializza i dati del form quando si apre il dialog\n  useEffect(() => {\n    if (open && cantiere) {\n      setFormData({\n        commessa: cantiere.commessa || cantiere.nome || '',\n        descrizione: cantiere.descrizione || '',\n        nome_cliente: cantiere.nome_cliente || '',\n        indirizzo_cantiere: cantiere.indirizzo_cantiere || '',\n        citta_cantiere: cantiere.citta_cantiere || '',\n        nazione_cantiere: cantiere.nazione_cantiere || 'Italia',\n        riferimenti_normativi: cantiere.riferimenti_normativi || '',\n        documentazione_progetto: cantiere.documentazione_progetto || ''\n      });\n      setError('');\n      setSuccess('');\n    }\n  }, [open, cantiere]);\n\n  // Gestisce i cambiamenti nei campi del form\n  const handleInputChange = e => {\n    const {\n      name,\n      value\n    } = e.target;\n    setFormData(prev => ({\n      ...prev,\n      [name]: value\n    }));\n  };\n\n  // Gestisce il salvataggio delle modifiche\n  const handleSave = async () => {\n    // Validazioni\n    if (!formData.nome.trim()) {\n      setError('Il nome del cantiere è obbligatorio');\n      return;\n    }\n    setLoading(true);\n    setError('');\n    try {\n      const updatedCantiere = await cantieriService.updateCantiere(cantiere.id_cantiere, {\n        commessa: formData.commessa.trim(),\n        descrizione: formData.descrizione.trim() || null,\n        nome_cliente: formData.nome_cliente.trim() || null,\n        indirizzo_cantiere: formData.indirizzo_cantiere.trim() || null,\n        citta_cantiere: formData.citta_cantiere.trim() || null,\n        nazione_cantiere: formData.nazione_cantiere.trim() || null,\n        riferimenti_normativi: formData.riferimenti_normativi.trim() || null,\n        documentazione_progetto: formData.documentazione_progetto.trim() || null\n      });\n      setSuccess('Cantiere aggiornato con successo!');\n\n      // Notifica il componente padre\n      if (onCantiereUpdated) {\n        onCantiereUpdated(updatedCantiere);\n      }\n\n      // Chiudi il dialog dopo un breve delay\n      setTimeout(() => {\n        handleClose();\n      }, 1500);\n    } catch (err) {\n      console.error('Errore nell\\'aggiornamento del cantiere:', err);\n      setError(err.detail || 'Errore nell\\'aggiornamento del cantiere');\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  // Gestisce l'apertura del dialog password\n  const handleOpenPasswordDialog = () => {\n    setShowPasswordDialog(true);\n  };\n\n  // Gestisce la chiusura del dialog password\n  const handleClosePasswordDialog = () => {\n    setShowPasswordDialog(false);\n  };\n\n  // Gestisce la chiusura del dialog principale\n  const handleClose = () => {\n    setError('');\n    setSuccess('');\n    onClose();\n  };\n\n  // Gestisce il cambio password completato\n  const handlePasswordChanged = () => {\n    setSuccess('Password cambiata con successo!');\n    // Il dialog password si chiuderà automaticamente\n  };\n  if (!cantiere) return null;\n  return /*#__PURE__*/_jsxDEV(_Fragment, {\n    children: [/*#__PURE__*/_jsxDEV(Dialog, {\n      open: open,\n      onClose: handleClose,\n      maxWidth: \"sm\",\n      fullWidth: true,\n      children: [/*#__PURE__*/_jsxDEV(DialogTitle, {\n        children: /*#__PURE__*/_jsxDEV(Box, {\n          sx: {\n            display: 'flex',\n            alignItems: 'center',\n            gap: 1\n          },\n          children: [/*#__PURE__*/_jsxDEV(ConstructionIcon, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 165,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Typography, {\n            variant: \"h6\",\n            children: \"Modifica Cantiere\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 166,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 164,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 163,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(DialogContent, {\n        children: [error && /*#__PURE__*/_jsxDEV(Alert, {\n          severity: \"error\",\n          sx: {\n            mb: 2\n          },\n          children: error\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 174,\n          columnNumber: 13\n        }, this), success && /*#__PURE__*/_jsxDEV(Alert, {\n          severity: \"success\",\n          sx: {\n            mb: 2\n          },\n          children: success\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 180,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(Box, {\n          sx: {\n            mb: 2\n          },\n          children: [/*#__PURE__*/_jsxDEV(Typography, {\n            variant: \"body2\",\n            color: \"text.secondary\",\n            gutterBottom: true,\n            children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n              children: \"Codice Univoco:\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 187,\n              columnNumber: 15\n            }, this), \" \", cantiere.codice_univoco]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 186,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Typography, {\n            variant: \"body2\",\n            color: \"text.secondary\",\n            children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n              children: \"Data Creazione:\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 190,\n              columnNumber: 15\n            }, this), \" \", new Date(cantiere.data_creazione).toLocaleDateString('it-IT')]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 189,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 185,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Divider, {\n          sx: {\n            my: 2\n          }\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 194,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Box, {\n          sx: {\n            mb: 3\n          },\n          children: [/*#__PURE__*/_jsxDEV(Typography, {\n            variant: \"subtitle1\",\n            gutterBottom: true,\n            sx: {\n              fontWeight: 'bold',\n              color: 'primary.main',\n              display: 'flex',\n              alignItems: 'center',\n              gap: 1\n            },\n            children: [/*#__PURE__*/_jsxDEV(DescriptionIcon, {\n              fontSize: \"small\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 205,\n              columnNumber: 15\n            }, this), \"Informazioni Generali\"]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 198,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Grid, {\n            container: true,\n            spacing: 2,\n            children: [/*#__PURE__*/_jsxDEV(Grid, {\n              item: true,\n              xs: 12,\n              children: /*#__PURE__*/_jsxDEV(TextField, {\n                fullWidth: true,\n                label: \"Nome Commessa\",\n                name: \"commessa\",\n                value: formData.commessa,\n                onChange: handleInputChange,\n                required: true,\n                disabled: loading\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 211,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 210,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Grid, {\n              item: true,\n              xs: 12,\n              children: /*#__PURE__*/_jsxDEV(TextField, {\n                fullWidth: true,\n                label: \"Descrizione\",\n                name: \"descrizione\",\n                value: formData.descrizione,\n                onChange: handleInputChange,\n                multiline: true,\n                rows: 2,\n                disabled: loading\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 223,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 222,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 209,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 197,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Divider, {\n          sx: {\n            my: 2\n          }\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 237,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Box, {\n          sx: {\n            mb: 3\n          },\n          children: [/*#__PURE__*/_jsxDEV(Typography, {\n            variant: \"subtitle1\",\n            gutterBottom: true,\n            sx: {\n              fontWeight: 'bold',\n              color: 'primary.main',\n              display: 'flex',\n              alignItems: 'center',\n              gap: 1\n            },\n            children: [/*#__PURE__*/_jsxDEV(BusinessIcon, {\n              fontSize: \"small\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 248,\n              columnNumber: 15\n            }, this), \"Cliente e Localizzazione\"]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 241,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Grid, {\n            container: true,\n            spacing: 2,\n            children: [/*#__PURE__*/_jsxDEV(Grid, {\n              item: true,\n              xs: 12,\n              md: 6,\n              children: /*#__PURE__*/_jsxDEV(TextField, {\n                fullWidth: true,\n                label: \"Nome Cliente\",\n                name: \"nome_cliente\",\n                value: formData.nome_cliente,\n                onChange: handleInputChange,\n                disabled: loading\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 254,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 253,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Grid, {\n              item: true,\n              xs: 12,\n              md: 6,\n              children: /*#__PURE__*/_jsxDEV(FormControl, {\n                fullWidth: true,\n                children: [/*#__PURE__*/_jsxDEV(InputLabel, {\n                  children: \"Nazione\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 266,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(Select, {\n                  name: \"nazione_cantiere\",\n                  value: formData.nazione_cantiere,\n                  onChange: handleInputChange,\n                  disabled: loading,\n                  label: \"Nazione\",\n                  children: [/*#__PURE__*/_jsxDEV(MenuItem, {\n                    value: \"Italia\",\n                    children: \"Italia\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 274,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(MenuItem, {\n                    value: \"Francia\",\n                    children: \"Francia\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 275,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(MenuItem, {\n                    value: \"Germania\",\n                    children: \"Germania\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 276,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(MenuItem, {\n                    value: \"Spagna\",\n                    children: \"Spagna\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 277,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(MenuItem, {\n                    value: \"Regno Unito\",\n                    children: \"Regno Unito\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 278,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(MenuItem, {\n                    value: \"Svizzera\",\n                    children: \"Svizzera\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 279,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(MenuItem, {\n                    value: \"Austria\",\n                    children: \"Austria\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 280,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(MenuItem, {\n                    value: \"Altro\",\n                    children: \"Altro\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 281,\n                    columnNumber: 21\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 267,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 265,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 264,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Grid, {\n              item: true,\n              xs: 12,\n              md: 6,\n              children: /*#__PURE__*/_jsxDEV(TextField, {\n                fullWidth: true,\n                label: \"Citt\\xE0\",\n                name: \"citta_cantiere\",\n                value: formData.citta_cantiere,\n                onChange: handleInputChange,\n                disabled: loading\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 287,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 286,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Grid, {\n              item: true,\n              xs: 12,\n              md: 6,\n              children: /*#__PURE__*/_jsxDEV(TextField, {\n                fullWidth: true,\n                label: \"Indirizzo Cantiere\",\n                name: \"indirizzo_cantiere\",\n                value: formData.indirizzo_cantiere,\n                onChange: handleInputChange,\n                disabled: loading\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 298,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 297,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 252,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 240,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(TextField, {\n          fullWidth: true,\n          label: \"Codice Progetto\",\n          name: \"codice_progetto\",\n          value: formData.codice_progetto,\n          onChange: handleInputChange,\n          sx: {\n            mb: 2\n          },\n          disabled: loading,\n          placeholder: \"Es. PROJ-2025-001\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 310,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(TextField, {\n          fullWidth: true,\n          label: \"Riferimenti Normativi\",\n          name: \"riferimenti_normativi\",\n          value: formData.riferimenti_normativi,\n          onChange: handleInputChange,\n          sx: {\n            mb: 2\n          },\n          disabled: loading,\n          placeholder: \"Es. CEI 64-8 Parte 6; Specifica Cliente XYZ Rev.2\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 321,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(TextField, {\n          fullWidth: true,\n          label: \"Documentazione di Progetto\",\n          name: \"documentazione_progetto\",\n          value: formData.documentazione_progetto,\n          onChange: handleInputChange,\n          multiline: true,\n          rows: 2,\n          sx: {\n            mb: 3\n          },\n          disabled: loading,\n          placeholder: \"Es. Schemi elettrici DWG-001, Layout impianto LAY-002\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 332,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Divider, {\n          sx: {\n            my: 2\n          }\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 345,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Box, {\n          sx: {\n            p: 2,\n            bgcolor: 'background.default',\n            borderRadius: 1,\n            border: '1px solid',\n            borderColor: 'divider'\n          },\n          children: [/*#__PURE__*/_jsxDEV(Typography, {\n            variant: \"subtitle2\",\n            gutterBottom: true,\n            sx: {\n              display: 'flex',\n              alignItems: 'center',\n              gap: 1\n            },\n            children: [/*#__PURE__*/_jsxDEV(LockIcon, {\n              fontSize: \"small\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 356,\n              columnNumber: 15\n            }, this), \"Gestione Password\"]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 355,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Typography, {\n            variant: \"body2\",\n            color: \"text.secondary\",\n            sx: {\n              mb: 2\n            },\n            children: \"Visualizza o modifica la password del cantiere\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 359,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Button, {\n            variant: \"outlined\",\n            startIcon: /*#__PURE__*/_jsxDEV(LockIcon, {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 364,\n              columnNumber: 26\n            }, this),\n            onClick: handleOpenPasswordDialog,\n            fullWidth: true,\n            children: \"Gestisci Password\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 362,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 348,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 172,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(DialogActions, {\n        children: [/*#__PURE__*/_jsxDEV(Button, {\n          onClick: handleClose,\n          disabled: loading,\n          children: \"Annulla\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 374,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Button, {\n          onClick: handleSave,\n          variant: \"contained\",\n          disabled: loading || !formData.nome.trim(),\n          startIcon: /*#__PURE__*/_jsxDEV(EditIcon, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 381,\n            columnNumber: 24\n          }, this),\n          children: loading ? 'Salvataggio...' : 'Salva Modifiche'\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 377,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 373,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 157,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(PasswordManagementDialog, {\n      open: showPasswordDialog,\n      onClose: handleClosePasswordDialog,\n      cantiere: cantiere,\n      onPasswordChanged: handlePasswordChanged\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 389,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true);\n};\n_s(EditCantiereDialog, \"4HUsNqZIQ+TfXYvqq8S9ZiR2qFc=\");\n_c = EditCantiereDialog;\nexport default EditCantiereDialog;\nvar _c;\n$RefreshReg$(_c, \"EditCantiereDialog\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "Dialog", "DialogTitle", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "DialogActions", "<PERSON><PERSON>", "TextField", "Box", "Typography", "<PERSON><PERSON>", "IconButton", "Divider", "Grid", "FormControl", "InputLabel", "Select", "MenuItem", "Edit", "EditIcon", "Lock", "LockIcon", "Construction", "ConstructionIcon", "Business", "BusinessIcon", "LocationOn", "LocationIcon", "Description", "DescriptionIcon", "cantieriService", "PasswordManagementDialog", "jsxDEV", "_jsxDEV", "Fragment", "_Fragment", "EditCantiereDialog", "open", "onClose", "cantiere", "onCantiereUpdated", "_s", "loading", "setLoading", "error", "setError", "success", "setSuccess", "showPasswordDialog", "setShowPasswordDialog", "formData", "setFormData", "commessa", "descrizione", "nome_cliente", "indirizzo_cantiere", "citta_cantiere", "nazione_cantiere", "riferimenti_normativi", "documentazione_progetto", "nome", "handleInputChange", "e", "name", "value", "target", "prev", "handleSave", "trim", "updatedCantiere", "updateCantiere", "id_cantiere", "setTimeout", "handleClose", "err", "console", "detail", "handleOpenPasswordDialog", "handleClosePasswordDialog", "handlePasswordChanged", "children", "max<PERSON><PERSON><PERSON>", "fullWidth", "sx", "display", "alignItems", "gap", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "variant", "severity", "mb", "color", "gutterBottom", "codice_univoco", "Date", "data_creazione", "toLocaleDateString", "my", "fontWeight", "fontSize", "container", "spacing", "item", "xs", "label", "onChange", "required", "disabled", "multiline", "rows", "md", "codice_progetto", "placeholder", "p", "bgcolor", "borderRadius", "border", "borderColor", "startIcon", "onClick", "onPasswordChanged", "_c", "$RefreshReg$"], "sources": ["C:/CMS/webapp/frontend/src/components/cantieri/EditCantiereDialog.js"], "sourcesContent": ["import React, { useState, useEffect } from 'react';\nimport {\n  Dialog,\n  DialogTitle,\n  DialogContent,\n  DialogActions,\n  Button,\n  TextField,\n  Box,\n  Typography,\n  Alert,\n  IconButton,\n  Divider,\n  Grid,\n  FormControl,\n  InputLabel,\n  Select,\n  MenuItem\n} from '@mui/material';\nimport {\n  Edit as EditIcon,\n  Lock as LockIcon,\n  Construction as ConstructionIcon,\n  Business as BusinessIcon,\n  LocationOn as LocationIcon,\n  Description as DescriptionIcon\n} from '@mui/icons-material';\nimport cantieriService from '../../services/cantieriService';\nimport PasswordManagementDialog from './PasswordManagementDialog';\n\n/**\n * Dialog per la modifica dei dati del cantiere\n * Include accesso rapido alla gestione password\n */\nconst EditCantiereDialog = ({\n  open,\n  onClose,\n  cantiere,\n  onCantiereUpdated = null\n}) => {\n  const [loading, setLoading] = useState(false);\n  const [error, setError] = useState('');\n  const [success, setSuccess] = useState('');\n  const [showPasswordDialog, setShowPasswordDialog] = useState(false);\n  \n  const [formData, setFormData] = useState({\n    commessa: '',\n    descrizione: '',\n    nome_cliente: '',\n    indirizzo_cantiere: '',\n    citta_cantiere: '',\n    nazione_cantiere: '',\n    riferimenti_normativi: '',\n    documentazione_progetto: ''\n  });\n\n  // Inizializza i dati del form quando si apre il dialog\n  useEffect(() => {\n    if (open && cantiere) {\n      setFormData({\n        commessa: cantiere.commessa || cantiere.nome || '',\n        descrizione: cantiere.descrizione || '',\n        nome_cliente: cantiere.nome_cliente || '',\n        indirizzo_cantiere: cantiere.indirizzo_cantiere || '',\n        citta_cantiere: cantiere.citta_cantiere || '',\n        nazione_cantiere: cantiere.nazione_cantiere || 'Italia',\n        riferimenti_normativi: cantiere.riferimenti_normativi || '',\n        documentazione_progetto: cantiere.documentazione_progetto || ''\n      });\n      setError('');\n      setSuccess('');\n    }\n  }, [open, cantiere]);\n\n  // Gestisce i cambiamenti nei campi del form\n  const handleInputChange = (e) => {\n    const { name, value } = e.target;\n    setFormData(prev => ({\n      ...prev,\n      [name]: value\n    }));\n  };\n\n  // Gestisce il salvataggio delle modifiche\n  const handleSave = async () => {\n    // Validazioni\n    if (!formData.nome.trim()) {\n      setError('Il nome del cantiere è obbligatorio');\n      return;\n    }\n\n    setLoading(true);\n    setError('');\n    \n    try {\n      const updatedCantiere = await cantieriService.updateCantiere(\n        cantiere.id_cantiere,\n        {\n          commessa: formData.commessa.trim(),\n          descrizione: formData.descrizione.trim() || null,\n          nome_cliente: formData.nome_cliente.trim() || null,\n          indirizzo_cantiere: formData.indirizzo_cantiere.trim() || null,\n          citta_cantiere: formData.citta_cantiere.trim() || null,\n          nazione_cantiere: formData.nazione_cantiere.trim() || null,\n          riferimenti_normativi: formData.riferimenti_normativi.trim() || null,\n          documentazione_progetto: formData.documentazione_progetto.trim() || null\n        }\n      );\n      \n      setSuccess('Cantiere aggiornato con successo!');\n      \n      // Notifica il componente padre\n      if (onCantiereUpdated) {\n        onCantiereUpdated(updatedCantiere);\n      }\n      \n      // Chiudi il dialog dopo un breve delay\n      setTimeout(() => {\n        handleClose();\n      }, 1500);\n      \n    } catch (err) {\n      console.error('Errore nell\\'aggiornamento del cantiere:', err);\n      setError(err.detail || 'Errore nell\\'aggiornamento del cantiere');\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  // Gestisce l'apertura del dialog password\n  const handleOpenPasswordDialog = () => {\n    setShowPasswordDialog(true);\n  };\n\n  // Gestisce la chiusura del dialog password\n  const handleClosePasswordDialog = () => {\n    setShowPasswordDialog(false);\n  };\n\n  // Gestisce la chiusura del dialog principale\n  const handleClose = () => {\n    setError('');\n    setSuccess('');\n    onClose();\n  };\n\n  // Gestisce il cambio password completato\n  const handlePasswordChanged = () => {\n    setSuccess('Password cambiata con successo!');\n    // Il dialog password si chiuderà automaticamente\n  };\n\n  if (!cantiere) return null;\n\n  return (\n    <>\n      <Dialog \n        open={open} \n        onClose={handleClose}\n        maxWidth=\"sm\"\n        fullWidth\n      >\n        <DialogTitle>\n          <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>\n            <ConstructionIcon />\n            <Typography variant=\"h6\">\n              Modifica Cantiere\n            </Typography>\n          </Box>\n        </DialogTitle>\n        \n        <DialogContent>\n          {error && (\n            <Alert severity=\"error\" sx={{ mb: 2 }}>\n              {error}\n            </Alert>\n          )}\n          \n          {success && (\n            <Alert severity=\"success\" sx={{ mb: 2 }}>\n              {success}\n            </Alert>\n          )}\n\n          <Box sx={{ mb: 2 }}>\n            <Typography variant=\"body2\" color=\"text.secondary\" gutterBottom>\n              <strong>Codice Univoco:</strong> {cantiere.codice_univoco}\n            </Typography>\n            <Typography variant=\"body2\" color=\"text.secondary\">\n              <strong>Data Creazione:</strong> {new Date(cantiere.data_creazione).toLocaleDateString('it-IT')}\n            </Typography>\n          </Box>\n\n          <Divider sx={{ my: 2 }} />\n          \n          {/* Sezione Informazioni Generali */}\n          <Box sx={{ mb: 3 }}>\n            <Typography variant=\"subtitle1\" gutterBottom sx={{\n              fontWeight: 'bold',\n              color: 'primary.main',\n              display: 'flex',\n              alignItems: 'center',\n              gap: 1\n            }}>\n              <DescriptionIcon fontSize=\"small\" />\n              Informazioni Generali\n            </Typography>\n\n            <Grid container spacing={2}>\n              <Grid item xs={12}>\n                <TextField\n                  fullWidth\n                  label=\"Nome Commessa\"\n                  name=\"commessa\"\n                  value={formData.commessa}\n                  onChange={handleInputChange}\n                  required\n                  disabled={loading}\n                />\n              </Grid>\n\n              <Grid item xs={12}>\n                <TextField\n                  fullWidth\n                  label=\"Descrizione\"\n                  name=\"descrizione\"\n                  value={formData.descrizione}\n                  onChange={handleInputChange}\n                  multiline\n                  rows={2}\n                  disabled={loading}\n                />\n              </Grid>\n            </Grid>\n          </Box>\n\n          <Divider sx={{ my: 2 }} />\n\n          {/* Sezione Cliente e Localizzazione */}\n          <Box sx={{ mb: 3 }}>\n            <Typography variant=\"subtitle1\" gutterBottom sx={{\n              fontWeight: 'bold',\n              color: 'primary.main',\n              display: 'flex',\n              alignItems: 'center',\n              gap: 1\n            }}>\n              <BusinessIcon fontSize=\"small\" />\n              Cliente e Localizzazione\n            </Typography>\n\n            <Grid container spacing={2}>\n              <Grid item xs={12} md={6}>\n                <TextField\n                  fullWidth\n                  label=\"Nome Cliente\"\n                  name=\"nome_cliente\"\n                  value={formData.nome_cliente}\n                  onChange={handleInputChange}\n                  disabled={loading}\n                />\n              </Grid>\n\n              <Grid item xs={12} md={6}>\n                <FormControl fullWidth>\n                  <InputLabel>Nazione</InputLabel>\n                  <Select\n                    name=\"nazione_cantiere\"\n                    value={formData.nazione_cantiere}\n                    onChange={handleInputChange}\n                    disabled={loading}\n                    label=\"Nazione\"\n                  >\n                    <MenuItem value=\"Italia\">Italia</MenuItem>\n                    <MenuItem value=\"Francia\">Francia</MenuItem>\n                    <MenuItem value=\"Germania\">Germania</MenuItem>\n                    <MenuItem value=\"Spagna\">Spagna</MenuItem>\n                    <MenuItem value=\"Regno Unito\">Regno Unito</MenuItem>\n                    <MenuItem value=\"Svizzera\">Svizzera</MenuItem>\n                    <MenuItem value=\"Austria\">Austria</MenuItem>\n                    <MenuItem value=\"Altro\">Altro</MenuItem>\n                  </Select>\n                </FormControl>\n              </Grid>\n\n              <Grid item xs={12} md={6}>\n                <TextField\n                  fullWidth\n                  label=\"Città\"\n                  name=\"citta_cantiere\"\n                  value={formData.citta_cantiere}\n                  onChange={handleInputChange}\n                  disabled={loading}\n                />\n              </Grid>\n\n              <Grid item xs={12} md={6}>\n                <TextField\n                  fullWidth\n                  label=\"Indirizzo Cantiere\"\n                  name=\"indirizzo_cantiere\"\n                  value={formData.indirizzo_cantiere}\n                  onChange={handleInputChange}\n                  disabled={loading}\n                />\n              </Grid>\n            </Grid>\n          </Box>\n\n          <TextField\n            fullWidth\n            label=\"Codice Progetto\"\n            name=\"codice_progetto\"\n            value={formData.codice_progetto}\n            onChange={handleInputChange}\n            sx={{ mb: 2 }}\n            disabled={loading}\n            placeholder=\"Es. PROJ-2025-001\"\n          />\n\n          <TextField\n            fullWidth\n            label=\"Riferimenti Normativi\"\n            name=\"riferimenti_normativi\"\n            value={formData.riferimenti_normativi}\n            onChange={handleInputChange}\n            sx={{ mb: 2 }}\n            disabled={loading}\n            placeholder=\"Es. CEI 64-8 Parte 6; Specifica Cliente XYZ Rev.2\"\n          />\n\n          <TextField\n            fullWidth\n            label=\"Documentazione di Progetto\"\n            name=\"documentazione_progetto\"\n            value={formData.documentazione_progetto}\n            onChange={handleInputChange}\n            multiline\n            rows={2}\n            sx={{ mb: 3 }}\n            disabled={loading}\n            placeholder=\"Es. Schemi elettrici DWG-001, Layout impianto LAY-002\"\n          />\n\n          <Divider sx={{ my: 2 }} />\n\n          {/* Sezione Gestione Password */}\n          <Box sx={{ \n            p: 2, \n            bgcolor: 'background.default', \n            borderRadius: 1,\n            border: '1px solid',\n            borderColor: 'divider'\n          }}>\n            <Typography variant=\"subtitle2\" gutterBottom sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>\n              <LockIcon fontSize=\"small\" />\n              Gestione Password\n            </Typography>\n            <Typography variant=\"body2\" color=\"text.secondary\" sx={{ mb: 2 }}>\n              Visualizza o modifica la password del cantiere\n            </Typography>\n            <Button\n              variant=\"outlined\"\n              startIcon={<LockIcon />}\n              onClick={handleOpenPasswordDialog}\n              fullWidth\n            >\n              Gestisci Password\n            </Button>\n          </Box>\n        </DialogContent>\n        \n        <DialogActions>\n          <Button onClick={handleClose} disabled={loading}>\n            Annulla\n          </Button>\n          <Button \n            onClick={handleSave} \n            variant=\"contained\"\n            disabled={loading || !formData.nome.trim()}\n            startIcon={<EditIcon />}\n          >\n            {loading ? 'Salvataggio...' : 'Salva Modifiche'}\n          </Button>\n        </DialogActions>\n      </Dialog>\n\n      {/* Dialog per la gestione password */}\n      <PasswordManagementDialog\n        open={showPasswordDialog}\n        onClose={handleClosePasswordDialog}\n        cantiere={cantiere}\n        onPasswordChanged={handlePasswordChanged}\n      />\n    </>\n  );\n};\n\nexport default EditCantiereDialog;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAClD,SACEC,MAAM,EACNC,WAAW,EACXC,aAAa,EACbC,aAAa,EACbC,MAAM,EACNC,SAAS,EACTC,GAAG,EACHC,UAAU,EACVC,KAAK,EACLC,UAAU,EACVC,OAAO,EACPC,IAAI,EACJC,WAAW,EACXC,UAAU,EACVC,MAAM,EACNC,QAAQ,QACH,eAAe;AACtB,SACEC,IAAI,IAAIC,QAAQ,EAChBC,IAAI,IAAIC,QAAQ,EAChBC,YAAY,IAAIC,gBAAgB,EAChCC,QAAQ,IAAIC,YAAY,EACxBC,UAAU,IAAIC,YAAY,EAC1BC,WAAW,IAAIC,eAAe,QACzB,qBAAqB;AAC5B,OAAOC,eAAe,MAAM,gCAAgC;AAC5D,OAAOC,wBAAwB,MAAM,4BAA4B;;AAEjE;AACA;AACA;AACA;AAHA,SAAAC,MAAA,IAAAC,OAAA,EAAAC,QAAA,IAAAC,SAAA;AAIA,MAAMC,kBAAkB,GAAGA,CAAC;EAC1BC,IAAI;EACJC,OAAO;EACPC,QAAQ;EACRC,iBAAiB,GAAG;AACtB,CAAC,KAAK;EAAAC,EAAA;EACJ,MAAM,CAACC,OAAO,EAAEC,UAAU,CAAC,GAAG3C,QAAQ,CAAC,KAAK,CAAC;EAC7C,MAAM,CAAC4C,KAAK,EAAEC,QAAQ,CAAC,GAAG7C,QAAQ,CAAC,EAAE,CAAC;EACtC,MAAM,CAAC8C,OAAO,EAAEC,UAAU,CAAC,GAAG/C,QAAQ,CAAC,EAAE,CAAC;EAC1C,MAAM,CAACgD,kBAAkB,EAAEC,qBAAqB,CAAC,GAAGjD,QAAQ,CAAC,KAAK,CAAC;EAEnE,MAAM,CAACkD,QAAQ,EAAEC,WAAW,CAAC,GAAGnD,QAAQ,CAAC;IACvCoD,QAAQ,EAAE,EAAE;IACZC,WAAW,EAAE,EAAE;IACfC,YAAY,EAAE,EAAE;IAChBC,kBAAkB,EAAE,EAAE;IACtBC,cAAc,EAAE,EAAE;IAClBC,gBAAgB,EAAE,EAAE;IACpBC,qBAAqB,EAAE,EAAE;IACzBC,uBAAuB,EAAE;EAC3B,CAAC,CAAC;;EAEF;EACA1D,SAAS,CAAC,MAAM;IACd,IAAIoC,IAAI,IAAIE,QAAQ,EAAE;MACpBY,WAAW,CAAC;QACVC,QAAQ,EAAEb,QAAQ,CAACa,QAAQ,IAAIb,QAAQ,CAACqB,IAAI,IAAI,EAAE;QAClDP,WAAW,EAAEd,QAAQ,CAACc,WAAW,IAAI,EAAE;QACvCC,YAAY,EAAEf,QAAQ,CAACe,YAAY,IAAI,EAAE;QACzCC,kBAAkB,EAAEhB,QAAQ,CAACgB,kBAAkB,IAAI,EAAE;QACrDC,cAAc,EAAEjB,QAAQ,CAACiB,cAAc,IAAI,EAAE;QAC7CC,gBAAgB,EAAElB,QAAQ,CAACkB,gBAAgB,IAAI,QAAQ;QACvDC,qBAAqB,EAAEnB,QAAQ,CAACmB,qBAAqB,IAAI,EAAE;QAC3DC,uBAAuB,EAAEpB,QAAQ,CAACoB,uBAAuB,IAAI;MAC/D,CAAC,CAAC;MACFd,QAAQ,CAAC,EAAE,CAAC;MACZE,UAAU,CAAC,EAAE,CAAC;IAChB;EACF,CAAC,EAAE,CAACV,IAAI,EAAEE,QAAQ,CAAC,CAAC;;EAEpB;EACA,MAAMsB,iBAAiB,GAAIC,CAAC,IAAK;IAC/B,MAAM;MAAEC,IAAI;MAAEC;IAAM,CAAC,GAAGF,CAAC,CAACG,MAAM;IAChCd,WAAW,CAACe,IAAI,KAAK;MACnB,GAAGA,IAAI;MACP,CAACH,IAAI,GAAGC;IACV,CAAC,CAAC,CAAC;EACL,CAAC;;EAED;EACA,MAAMG,UAAU,GAAG,MAAAA,CAAA,KAAY;IAC7B;IACA,IAAI,CAACjB,QAAQ,CAACU,IAAI,CAACQ,IAAI,CAAC,CAAC,EAAE;MACzBvB,QAAQ,CAAC,qCAAqC,CAAC;MAC/C;IACF;IAEAF,UAAU,CAAC,IAAI,CAAC;IAChBE,QAAQ,CAAC,EAAE,CAAC;IAEZ,IAAI;MACF,MAAMwB,eAAe,GAAG,MAAMvC,eAAe,CAACwC,cAAc,CAC1D/B,QAAQ,CAACgC,WAAW,EACpB;QACEnB,QAAQ,EAAEF,QAAQ,CAACE,QAAQ,CAACgB,IAAI,CAAC,CAAC;QAClCf,WAAW,EAAEH,QAAQ,CAACG,WAAW,CAACe,IAAI,CAAC,CAAC,IAAI,IAAI;QAChDd,YAAY,EAAEJ,QAAQ,CAACI,YAAY,CAACc,IAAI,CAAC,CAAC,IAAI,IAAI;QAClDb,kBAAkB,EAAEL,QAAQ,CAACK,kBAAkB,CAACa,IAAI,CAAC,CAAC,IAAI,IAAI;QAC9DZ,cAAc,EAAEN,QAAQ,CAACM,cAAc,CAACY,IAAI,CAAC,CAAC,IAAI,IAAI;QACtDX,gBAAgB,EAAEP,QAAQ,CAACO,gBAAgB,CAACW,IAAI,CAAC,CAAC,IAAI,IAAI;QAC1DV,qBAAqB,EAAER,QAAQ,CAACQ,qBAAqB,CAACU,IAAI,CAAC,CAAC,IAAI,IAAI;QACpET,uBAAuB,EAAET,QAAQ,CAACS,uBAAuB,CAACS,IAAI,CAAC,CAAC,IAAI;MACtE,CACF,CAAC;MAEDrB,UAAU,CAAC,mCAAmC,CAAC;;MAE/C;MACA,IAAIP,iBAAiB,EAAE;QACrBA,iBAAiB,CAAC6B,eAAe,CAAC;MACpC;;MAEA;MACAG,UAAU,CAAC,MAAM;QACfC,WAAW,CAAC,CAAC;MACf,CAAC,EAAE,IAAI,CAAC;IAEV,CAAC,CAAC,OAAOC,GAAG,EAAE;MACZC,OAAO,CAAC/B,KAAK,CAAC,0CAA0C,EAAE8B,GAAG,CAAC;MAC9D7B,QAAQ,CAAC6B,GAAG,CAACE,MAAM,IAAI,yCAAyC,CAAC;IACnE,CAAC,SAAS;MACRjC,UAAU,CAAC,KAAK,CAAC;IACnB;EACF,CAAC;;EAED;EACA,MAAMkC,wBAAwB,GAAGA,CAAA,KAAM;IACrC5B,qBAAqB,CAAC,IAAI,CAAC;EAC7B,CAAC;;EAED;EACA,MAAM6B,yBAAyB,GAAGA,CAAA,KAAM;IACtC7B,qBAAqB,CAAC,KAAK,CAAC;EAC9B,CAAC;;EAED;EACA,MAAMwB,WAAW,GAAGA,CAAA,KAAM;IACxB5B,QAAQ,CAAC,EAAE,CAAC;IACZE,UAAU,CAAC,EAAE,CAAC;IACdT,OAAO,CAAC,CAAC;EACX,CAAC;;EAED;EACA,MAAMyC,qBAAqB,GAAGA,CAAA,KAAM;IAClChC,UAAU,CAAC,iCAAiC,CAAC;IAC7C;EACF,CAAC;EAED,IAAI,CAACR,QAAQ,EAAE,OAAO,IAAI;EAE1B,oBACEN,OAAA,CAAAE,SAAA;IAAA6C,QAAA,gBACE/C,OAAA,CAAC/B,MAAM;MACLmC,IAAI,EAAEA,IAAK;MACXC,OAAO,EAAEmC,WAAY;MACrBQ,QAAQ,EAAC,IAAI;MACbC,SAAS;MAAAF,QAAA,gBAET/C,OAAA,CAAC9B,WAAW;QAAA6E,QAAA,eACV/C,OAAA,CAACzB,GAAG;UAAC2E,EAAE,EAAE;YAAEC,OAAO,EAAE,MAAM;YAAEC,UAAU,EAAE,QAAQ;YAAEC,GAAG,EAAE;UAAE,CAAE;UAAAN,QAAA,gBACzD/C,OAAA,CAACV,gBAAgB;YAAAgE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,eACpBzD,OAAA,CAACxB,UAAU;YAACkF,OAAO,EAAC,IAAI;YAAAX,QAAA,EAAC;UAEzB;YAAAO,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAY,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACV;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACK,CAAC,eAEdzD,OAAA,CAAC7B,aAAa;QAAA4E,QAAA,GACXpC,KAAK,iBACJX,OAAA,CAACvB,KAAK;UAACkF,QAAQ,EAAC,OAAO;UAACT,EAAE,EAAE;YAAEU,EAAE,EAAE;UAAE,CAAE;UAAAb,QAAA,EACnCpC;QAAK;UAAA2C,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACD,CACR,EAEA5C,OAAO,iBACNb,OAAA,CAACvB,KAAK;UAACkF,QAAQ,EAAC,SAAS;UAACT,EAAE,EAAE;YAAEU,EAAE,EAAE;UAAE,CAAE;UAAAb,QAAA,EACrClC;QAAO;UAAAyC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CACR,eAEDzD,OAAA,CAACzB,GAAG;UAAC2E,EAAE,EAAE;YAAEU,EAAE,EAAE;UAAE,CAAE;UAAAb,QAAA,gBACjB/C,OAAA,CAACxB,UAAU;YAACkF,OAAO,EAAC,OAAO;YAACG,KAAK,EAAC,gBAAgB;YAACC,YAAY;YAAAf,QAAA,gBAC7D/C,OAAA;cAAA+C,QAAA,EAAQ;YAAe;cAAAO,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC,KAAC,EAACnD,QAAQ,CAACyD,cAAc;UAAA;YAAAT,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC/C,CAAC,eACbzD,OAAA,CAACxB,UAAU;YAACkF,OAAO,EAAC,OAAO;YAACG,KAAK,EAAC,gBAAgB;YAAAd,QAAA,gBAChD/C,OAAA;cAAA+C,QAAA,EAAQ;YAAe;cAAAO,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC,KAAC,EAAC,IAAIO,IAAI,CAAC1D,QAAQ,CAAC2D,cAAc,CAAC,CAACC,kBAAkB,CAAC,OAAO,CAAC;UAAA;YAAAZ,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACrF,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACV,CAAC,eAENzD,OAAA,CAACrB,OAAO;UAACuE,EAAE,EAAE;YAAEiB,EAAE,EAAE;UAAE;QAAE;UAAAb,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,eAG1BzD,OAAA,CAACzB,GAAG;UAAC2E,EAAE,EAAE;YAAEU,EAAE,EAAE;UAAE,CAAE;UAAAb,QAAA,gBACjB/C,OAAA,CAACxB,UAAU;YAACkF,OAAO,EAAC,WAAW;YAACI,YAAY;YAACZ,EAAE,EAAE;cAC/CkB,UAAU,EAAE,MAAM;cAClBP,KAAK,EAAE,cAAc;cACrBV,OAAO,EAAE,MAAM;cACfC,UAAU,EAAE,QAAQ;cACpBC,GAAG,EAAE;YACP,CAAE;YAAAN,QAAA,gBACA/C,OAAA,CAACJ,eAAe;cAACyE,QAAQ,EAAC;YAAO;cAAAf,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,yBAEtC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAY,CAAC,eAEbzD,OAAA,CAACpB,IAAI;YAAC0F,SAAS;YAACC,OAAO,EAAE,CAAE;YAAAxB,QAAA,gBACzB/C,OAAA,CAACpB,IAAI;cAAC4F,IAAI;cAACC,EAAE,EAAE,EAAG;cAAA1B,QAAA,eAChB/C,OAAA,CAAC1B,SAAS;gBACR2E,SAAS;gBACTyB,KAAK,EAAC,eAAe;gBACrB5C,IAAI,EAAC,UAAU;gBACfC,KAAK,EAAEd,QAAQ,CAACE,QAAS;gBACzBwD,QAAQ,EAAE/C,iBAAkB;gBAC5BgD,QAAQ;gBACRC,QAAQ,EAAEpE;cAAQ;gBAAA6C,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACnB;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACE,CAAC,eAEPzD,OAAA,CAACpB,IAAI;cAAC4F,IAAI;cAACC,EAAE,EAAE,EAAG;cAAA1B,QAAA,eAChB/C,OAAA,CAAC1B,SAAS;gBACR2E,SAAS;gBACTyB,KAAK,EAAC,aAAa;gBACnB5C,IAAI,EAAC,aAAa;gBAClBC,KAAK,EAAEd,QAAQ,CAACG,WAAY;gBAC5BuD,QAAQ,EAAE/C,iBAAkB;gBAC5BkD,SAAS;gBACTC,IAAI,EAAE,CAAE;gBACRF,QAAQ,EAAEpE;cAAQ;gBAAA6C,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACnB;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACE,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACJ,CAAC,eAENzD,OAAA,CAACrB,OAAO;UAACuE,EAAE,EAAE;YAAEiB,EAAE,EAAE;UAAE;QAAE;UAAAb,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,eAG1BzD,OAAA,CAACzB,GAAG;UAAC2E,EAAE,EAAE;YAAEU,EAAE,EAAE;UAAE,CAAE;UAAAb,QAAA,gBACjB/C,OAAA,CAACxB,UAAU;YAACkF,OAAO,EAAC,WAAW;YAACI,YAAY;YAACZ,EAAE,EAAE;cAC/CkB,UAAU,EAAE,MAAM;cAClBP,KAAK,EAAE,cAAc;cACrBV,OAAO,EAAE,MAAM;cACfC,UAAU,EAAE,QAAQ;cACpBC,GAAG,EAAE;YACP,CAAE;YAAAN,QAAA,gBACA/C,OAAA,CAACR,YAAY;cAAC6E,QAAQ,EAAC;YAAO;cAAAf,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,4BAEnC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAY,CAAC,eAEbzD,OAAA,CAACpB,IAAI;YAAC0F,SAAS;YAACC,OAAO,EAAE,CAAE;YAAAxB,QAAA,gBACzB/C,OAAA,CAACpB,IAAI;cAAC4F,IAAI;cAACC,EAAE,EAAE,EAAG;cAACO,EAAE,EAAE,CAAE;cAAAjC,QAAA,eACvB/C,OAAA,CAAC1B,SAAS;gBACR2E,SAAS;gBACTyB,KAAK,EAAC,cAAc;gBACpB5C,IAAI,EAAC,cAAc;gBACnBC,KAAK,EAAEd,QAAQ,CAACI,YAAa;gBAC7BsD,QAAQ,EAAE/C,iBAAkB;gBAC5BiD,QAAQ,EAAEpE;cAAQ;gBAAA6C,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACnB;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACE,CAAC,eAEPzD,OAAA,CAACpB,IAAI;cAAC4F,IAAI;cAACC,EAAE,EAAE,EAAG;cAACO,EAAE,EAAE,CAAE;cAAAjC,QAAA,eACvB/C,OAAA,CAACnB,WAAW;gBAACoE,SAAS;gBAAAF,QAAA,gBACpB/C,OAAA,CAAClB,UAAU;kBAAAiE,QAAA,EAAC;gBAAO;kBAAAO,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAY,CAAC,eAChCzD,OAAA,CAACjB,MAAM;kBACL+C,IAAI,EAAC,kBAAkB;kBACvBC,KAAK,EAAEd,QAAQ,CAACO,gBAAiB;kBACjCmD,QAAQ,EAAE/C,iBAAkB;kBAC5BiD,QAAQ,EAAEpE,OAAQ;kBAClBiE,KAAK,EAAC,SAAS;kBAAA3B,QAAA,gBAEf/C,OAAA,CAAChB,QAAQ;oBAAC+C,KAAK,EAAC,QAAQ;oBAAAgB,QAAA,EAAC;kBAAM;oBAAAO,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAU,CAAC,eAC1CzD,OAAA,CAAChB,QAAQ;oBAAC+C,KAAK,EAAC,SAAS;oBAAAgB,QAAA,EAAC;kBAAO;oBAAAO,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAU,CAAC,eAC5CzD,OAAA,CAAChB,QAAQ;oBAAC+C,KAAK,EAAC,UAAU;oBAAAgB,QAAA,EAAC;kBAAQ;oBAAAO,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAU,CAAC,eAC9CzD,OAAA,CAAChB,QAAQ;oBAAC+C,KAAK,EAAC,QAAQ;oBAAAgB,QAAA,EAAC;kBAAM;oBAAAO,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAU,CAAC,eAC1CzD,OAAA,CAAChB,QAAQ;oBAAC+C,KAAK,EAAC,aAAa;oBAAAgB,QAAA,EAAC;kBAAW;oBAAAO,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAU,CAAC,eACpDzD,OAAA,CAAChB,QAAQ;oBAAC+C,KAAK,EAAC,UAAU;oBAAAgB,QAAA,EAAC;kBAAQ;oBAAAO,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAU,CAAC,eAC9CzD,OAAA,CAAChB,QAAQ;oBAAC+C,KAAK,EAAC,SAAS;oBAAAgB,QAAA,EAAC;kBAAO;oBAAAO,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAU,CAAC,eAC5CzD,OAAA,CAAChB,QAAQ;oBAAC+C,KAAK,EAAC,OAAO;oBAAAgB,QAAA,EAAC;kBAAK;oBAAAO,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAU,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAClC,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACE;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACV,CAAC,eAEPzD,OAAA,CAACpB,IAAI;cAAC4F,IAAI;cAACC,EAAE,EAAE,EAAG;cAACO,EAAE,EAAE,CAAE;cAAAjC,QAAA,eACvB/C,OAAA,CAAC1B,SAAS;gBACR2E,SAAS;gBACTyB,KAAK,EAAC,UAAO;gBACb5C,IAAI,EAAC,gBAAgB;gBACrBC,KAAK,EAAEd,QAAQ,CAACM,cAAe;gBAC/BoD,QAAQ,EAAE/C,iBAAkB;gBAC5BiD,QAAQ,EAAEpE;cAAQ;gBAAA6C,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACnB;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACE,CAAC,eAEPzD,OAAA,CAACpB,IAAI;cAAC4F,IAAI;cAACC,EAAE,EAAE,EAAG;cAACO,EAAE,EAAE,CAAE;cAAAjC,QAAA,eACvB/C,OAAA,CAAC1B,SAAS;gBACR2E,SAAS;gBACTyB,KAAK,EAAC,oBAAoB;gBAC1B5C,IAAI,EAAC,oBAAoB;gBACzBC,KAAK,EAAEd,QAAQ,CAACK,kBAAmB;gBACnCqD,QAAQ,EAAE/C,iBAAkB;gBAC5BiD,QAAQ,EAAEpE;cAAQ;gBAAA6C,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACnB;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACE,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACJ,CAAC,eAENzD,OAAA,CAAC1B,SAAS;UACR2E,SAAS;UACTyB,KAAK,EAAC,iBAAiB;UACvB5C,IAAI,EAAC,iBAAiB;UACtBC,KAAK,EAAEd,QAAQ,CAACgE,eAAgB;UAChCN,QAAQ,EAAE/C,iBAAkB;UAC5BsB,EAAE,EAAE;YAAEU,EAAE,EAAE;UAAE,CAAE;UACdiB,QAAQ,EAAEpE,OAAQ;UAClByE,WAAW,EAAC;QAAmB;UAAA5B,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAChC,CAAC,eAEFzD,OAAA,CAAC1B,SAAS;UACR2E,SAAS;UACTyB,KAAK,EAAC,uBAAuB;UAC7B5C,IAAI,EAAC,uBAAuB;UAC5BC,KAAK,EAAEd,QAAQ,CAACQ,qBAAsB;UACtCkD,QAAQ,EAAE/C,iBAAkB;UAC5BsB,EAAE,EAAE;YAAEU,EAAE,EAAE;UAAE,CAAE;UACdiB,QAAQ,EAAEpE,OAAQ;UAClByE,WAAW,EAAC;QAAmD;UAAA5B,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAChE,CAAC,eAEFzD,OAAA,CAAC1B,SAAS;UACR2E,SAAS;UACTyB,KAAK,EAAC,4BAA4B;UAClC5C,IAAI,EAAC,yBAAyB;UAC9BC,KAAK,EAAEd,QAAQ,CAACS,uBAAwB;UACxCiD,QAAQ,EAAE/C,iBAAkB;UAC5BkD,SAAS;UACTC,IAAI,EAAE,CAAE;UACR7B,EAAE,EAAE;YAAEU,EAAE,EAAE;UAAE,CAAE;UACdiB,QAAQ,EAAEpE,OAAQ;UAClByE,WAAW,EAAC;QAAuD;UAAA5B,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACpE,CAAC,eAEFzD,OAAA,CAACrB,OAAO;UAACuE,EAAE,EAAE;YAAEiB,EAAE,EAAE;UAAE;QAAE;UAAAb,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,eAG1BzD,OAAA,CAACzB,GAAG;UAAC2E,EAAE,EAAE;YACPiC,CAAC,EAAE,CAAC;YACJC,OAAO,EAAE,oBAAoB;YAC7BC,YAAY,EAAE,CAAC;YACfC,MAAM,EAAE,WAAW;YACnBC,WAAW,EAAE;UACf,CAAE;UAAAxC,QAAA,gBACA/C,OAAA,CAACxB,UAAU;YAACkF,OAAO,EAAC,WAAW;YAACI,YAAY;YAACZ,EAAE,EAAE;cAAEC,OAAO,EAAE,MAAM;cAAEC,UAAU,EAAE,QAAQ;cAAEC,GAAG,EAAE;YAAE,CAAE;YAAAN,QAAA,gBACjG/C,OAAA,CAACZ,QAAQ;cAACiF,QAAQ,EAAC;YAAO;cAAAf,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,qBAE/B;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAY,CAAC,eACbzD,OAAA,CAACxB,UAAU;YAACkF,OAAO,EAAC,OAAO;YAACG,KAAK,EAAC,gBAAgB;YAACX,EAAE,EAAE;cAAEU,EAAE,EAAE;YAAE,CAAE;YAAAb,QAAA,EAAC;UAElE;YAAAO,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAY,CAAC,eACbzD,OAAA,CAAC3B,MAAM;YACLqF,OAAO,EAAC,UAAU;YAClB8B,SAAS,eAAExF,OAAA,CAACZ,QAAQ;cAAAkE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAE;YACxBgC,OAAO,EAAE7C,wBAAyB;YAClCK,SAAS;YAAAF,QAAA,EACV;UAED;YAAAO,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACN,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACO,CAAC,eAEhBzD,OAAA,CAAC5B,aAAa;QAAA2E,QAAA,gBACZ/C,OAAA,CAAC3B,MAAM;UAACoH,OAAO,EAAEjD,WAAY;UAACqC,QAAQ,EAAEpE,OAAQ;UAAAsC,QAAA,EAAC;QAEjD;UAAAO,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC,eACTzD,OAAA,CAAC3B,MAAM;UACLoH,OAAO,EAAEvD,UAAW;UACpBwB,OAAO,EAAC,WAAW;UACnBmB,QAAQ,EAAEpE,OAAO,IAAI,CAACQ,QAAQ,CAACU,IAAI,CAACQ,IAAI,CAAC,CAAE;UAC3CqD,SAAS,eAAExF,OAAA,CAACd,QAAQ;YAAAoE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAE;UAAAV,QAAA,EAEvBtC,OAAO,GAAG,gBAAgB,GAAG;QAAiB;UAAA6C,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACzC,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACI,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACV,CAAC,eAGTzD,OAAA,CAACF,wBAAwB;MACvBM,IAAI,EAAEW,kBAAmB;MACzBV,OAAO,EAAEwC,yBAA0B;MACnCvC,QAAQ,EAAEA,QAAS;MACnBoF,iBAAiB,EAAE5C;IAAsB;MAAAQ,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAC1C,CAAC;EAAA,eACF,CAAC;AAEP,CAAC;AAACjD,EAAA,CA1WIL,kBAAkB;AAAAwF,EAAA,GAAlBxF,kBAAkB;AA4WxB,eAAeA,kBAAkB;AAAC,IAAAwF,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}