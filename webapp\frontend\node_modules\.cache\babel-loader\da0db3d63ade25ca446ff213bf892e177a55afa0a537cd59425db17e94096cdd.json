{"ast": null, "code": "export const buildDeprecatedPropsWarning = message => {\n  let alreadyWarned = false;\n  if (process.env.NODE_ENV === 'production') {\n    return () => {};\n  }\n  const cleanMessage = Array.isArray(message) ? message.join('\\n') : message;\n  return deprecatedProps => {\n    const deprecatedKeys = Object.entries(deprecatedProps).filter(([, value]) => value !== undefined).map(([key]) => `- ${key}`);\n    if (!alreadyWarned && deprecatedKeys.length > 0) {\n      alreadyWarned = true;\n      console.warn([cleanMessage, 'deprecated props observed:', ...deprecatedKeys].join('\\n'));\n    }\n  };\n};\nexport const buildWarning = (message, gravity = 'warning') => {\n  let alreadyWarned = false;\n  const cleanMessage = Array.isArray(message) ? message.join('\\n') : message;\n  return () => {\n    if (!alreadyWarned) {\n      alreadyWarned = true;\n      if (gravity === 'error') {\n        console.error(cleanMessage);\n      } else {\n        console.warn(cleanMessage);\n      }\n    }\n  };\n};", "map": {"version": 3, "names": ["buildDeprecatedPropsWarning", "message", "alreadyWarned", "process", "env", "NODE_ENV", "cleanMessage", "Array", "isArray", "join", "deprecatedProps", "deprecatedKeys", "Object", "entries", "filter", "value", "undefined", "map", "key", "length", "console", "warn", "buildWarning", "gravity", "error"], "sources": ["C:/CMS/webapp/frontend/node_modules/@mui/x-date-pickers/internals/utils/warning.js"], "sourcesContent": ["export const buildDeprecatedPropsWarning = message => {\n  let alreadyWarned = false;\n  if (process.env.NODE_ENV === 'production') {\n    return () => {};\n  }\n  const cleanMessage = Array.isArray(message) ? message.join('\\n') : message;\n  return deprecatedProps => {\n    const deprecatedKeys = Object.entries(deprecatedProps).filter(([, value]) => value !== undefined).map(([key]) => `- ${key}`);\n    if (!alreadyWarned && deprecatedKeys.length > 0) {\n      alreadyWarned = true;\n      console.warn([cleanMessage, 'deprecated props observed:', ...deprecatedKeys].join('\\n'));\n    }\n  };\n};\nexport const buildWarning = (message, gravity = 'warning') => {\n  let alreadyWarned = false;\n  const cleanMessage = Array.isArray(message) ? message.join('\\n') : message;\n  return () => {\n    if (!alreadyWarned) {\n      alreadyWarned = true;\n      if (gravity === 'error') {\n        console.error(cleanMessage);\n      } else {\n        console.warn(cleanMessage);\n      }\n    }\n  };\n};"], "mappings": "AAAA,OAAO,MAAMA,2BAA2B,GAAGC,OAAO,IAAI;EACpD,IAAIC,aAAa,GAAG,KAAK;EACzB,IAAIC,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,EAAE;IACzC,OAAO,MAAM,CAAC,CAAC;EACjB;EACA,MAAMC,YAAY,GAAGC,KAAK,CAACC,OAAO,CAACP,OAAO,CAAC,GAAGA,OAAO,CAACQ,IAAI,CAAC,IAAI,CAAC,GAAGR,OAAO;EAC1E,OAAOS,eAAe,IAAI;IACxB,MAAMC,cAAc,GAAGC,MAAM,CAACC,OAAO,CAACH,eAAe,CAAC,CAACI,MAAM,CAAC,CAAC,GAAGC,KAAK,CAAC,KAAKA,KAAK,KAAKC,SAAS,CAAC,CAACC,GAAG,CAAC,CAAC,CAACC,GAAG,CAAC,KAAK,KAAKA,GAAG,EAAE,CAAC;IAC5H,IAAI,CAAChB,aAAa,IAAIS,cAAc,CAACQ,MAAM,GAAG,CAAC,EAAE;MAC/CjB,aAAa,GAAG,IAAI;MACpBkB,OAAO,CAACC,IAAI,CAAC,CAACf,YAAY,EAAE,4BAA4B,EAAE,GAAGK,cAAc,CAAC,CAACF,IAAI,CAAC,IAAI,CAAC,CAAC;IAC1F;EACF,CAAC;AACH,CAAC;AACD,OAAO,MAAMa,YAAY,GAAGA,CAACrB,OAAO,EAAEsB,OAAO,GAAG,SAAS,KAAK;EAC5D,IAAIrB,aAAa,GAAG,KAAK;EACzB,MAAMI,YAAY,GAAGC,KAAK,CAACC,OAAO,CAACP,OAAO,CAAC,GAAGA,OAAO,CAACQ,IAAI,CAAC,IAAI,CAAC,GAAGR,OAAO;EAC1E,OAAO,MAAM;IACX,IAAI,CAACC,aAAa,EAAE;MAClBA,aAAa,GAAG,IAAI;MACpB,IAAIqB,OAAO,KAAK,OAAO,EAAE;QACvBH,OAAO,CAACI,KAAK,CAAClB,YAAY,CAAC;MAC7B,CAAC,MAAM;QACLc,OAAO,CAACC,IAAI,CAACf,YAAY,CAAC;MAC5B;IACF;EACF,CAAC;AACH,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}