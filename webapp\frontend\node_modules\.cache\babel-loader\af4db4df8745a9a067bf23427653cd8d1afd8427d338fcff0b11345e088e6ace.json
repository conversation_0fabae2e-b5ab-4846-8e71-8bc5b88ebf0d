{"ast": null, "code": "var _jsxFileName = \"C:\\\\CMS\\\\webapp\\\\frontend\\\\src\\\\pages\\\\cavi\\\\ReportCaviPageNew.js\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect } from 'react';\nimport '../../styles/reports.css';\nimport { Box, Typography, Paper, Grid, Card, CardContent, CardActions, Button, Chip, Alert, CircularProgress, Divider, IconButton, Tooltip, Dialog, DialogTitle, DialogContent, DialogActions, FormControl, InputLabel, Select, MenuItem, TextField, Accordion, AccordionSummary, AccordionDetails, Switch, FormControlLabel } from '@mui/material';\nimport { Assessment as AssessmentIcon, BarChart as BarChartIcon, PieChart as PieChartIcon, Timeline as TimelineIcon, List as ListIcon, Download as DownloadIcon, Visibility as VisibilityIcon, Refresh as RefreshIcon, ArrowBack as ArrowBackIcon, DateRange as DateRangeIcon, Cable as CableIcon, Inventory as InventoryIcon, ExpandMore as ExpandMoreIcon, ShowChart as ShowChartIcon } from '@mui/icons-material';\nimport { useNavigate, useParams } from 'react-router-dom';\nimport { useAuth } from '../../context/AuthContext';\nimport AdminHomeButton from '../../components/common/AdminHomeButton';\nimport reportService from '../../services/reportService';\nimport FilterableTable from '../../components/common/FilterableTable';\nimport EmptyState from '../../components/common/EmptyState';\nimport MetricCard from '../../components/common/MetricCard';\nimport ReportSection from '../../components/common/ReportSection';\n\n// Import dei componenti grafici\nimport ProgressChart from '../../components/charts/ProgressChart';\nimport BobineChart from '../../components/charts/BobineChart';\nimport BoqChart from '../../components/charts/BoqChart';\nimport TimelineChart from '../../components/charts/TimelineChart';\nimport CaviStatoChart from '../../components/charts/CaviStatoChart';\nimport { jsxDEV as _jsxDEV, Fragment as _Fragment } from \"react/jsx-dev-runtime\";\nconst ReportCaviPageNew = () => {\n  _s();\n  const navigate = useNavigate();\n  const {\n    cantiereId\n  } = useParams();\n  const {\n    user\n  } = useAuth();\n  const [loading, setLoading] = useState(false);\n  const [error, setError] = useState(null);\n  const [reportData, setReportData] = useState(null);\n  const [selectedReport, setSelectedReport] = useState(null);\n  const [openDialog, setOpenDialog] = useState(false);\n  const [dialogType, setDialogType] = useState('');\n  const [selectedReportType, setSelectedReportType] = useState('progress');\n  const [formData, setFormData] = useState({\n    formato: 'video',\n    data_inizio: '',\n    data_fine: '',\n    id_bobina: ''\n  });\n\n  // New state to store all report data\n  const [reportsData, setReportsData] = useState({\n    progress: null,\n    boq: null,\n    bobine: null,\n    caviStato: null,\n    bobinaSpecifica: null,\n    posaPeriodo: null\n  });\n\n  // State per controllo visualizzazione grafici\n  const [showCharts, setShowCharts] = useState(true);\n\n  // Load all basic reports on component mount\n  useEffect(() => {\n    const loadAllReports = async () => {\n      setLoading(true);\n      try {\n        // Create individual promises that handle their own errors\n        const progressPromise = reportService.getProgressReport(cantiereId, 'video').catch(err => {\n          console.error('Error loading progress report:', err);\n          return {\n            content: null\n          };\n        });\n        const boqPromise = reportService.getBillOfQuantities(cantiereId, 'video').catch(err => {\n          console.error('Error loading BOQ report:', err);\n          return {\n            content: null\n          };\n        });\n        const bobinePromise = reportService.getBobineReport(cantiereId, 'video').catch(err => {\n          console.error('Error loading bobine report:', err);\n          return {\n            content: null\n          };\n        });\n        const caviStatoPromise = reportService.getCaviStatoReport(cantiereId, 'video').catch(err => {\n          console.error('Error loading cavi stato report:', err);\n          return {\n            content: null\n          };\n        });\n\n        // Wait for all promises to resolve (they won't reject due to the catch handlers)\n        const [progressData, boqData, bobineData, caviStatoData] = await Promise.all([progressPromise, boqPromise, bobinePromise, caviStatoPromise]);\n\n        // Set the data for each report, even if some are null\n        setReportsData({\n          progress: progressData.content,\n          boq: boqData.content,\n          bobine: bobineData.content,\n          caviStato: caviStatoData.content,\n          bobinaSpecifica: null,\n          posaPeriodo: null\n        });\n\n        // Only set error to null if we successfully loaded at least one report\n        if (progressData.content || boqData.content || bobineData.content || caviStatoData.content) {\n          setError(null);\n        } else {\n          setError('Errore nel caricamento dei report. Riprova più tardi.');\n        }\n      } catch (err) {\n        // This catch block should rarely be hit due to the individual error handling above\n        console.error('Unexpected error loading reports:', err);\n        setError('Errore nel caricamento dei report. Riprova più tardi.');\n      } finally {\n        setLoading(false);\n      }\n    };\n    if (cantiereId) {\n      loadAllReports();\n    }\n  }, [cantiereId]);\n\n  // Configurazione dei report disponibili\n  const reportTypes = [{\n    id: 'progress',\n    title: 'Report Avanzamento',\n    description: 'Panoramica completa dell\\'avanzamento dei lavori con metriche di performance e previsioni',\n    icon: /*#__PURE__*/_jsxDEV(AssessmentIcon, {}, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 171,\n      columnNumber: 13\n    }, this),\n    color: 'primary',\n    features: ['Metri posati vs teorici', 'Percentuale completamento', 'Previsioni timeline', 'Performance giornaliera']\n  }, {\n    id: 'boq',\n    title: 'Bill of Quantities',\n    description: 'Distinta materiali dettagliata con analisi dei consumi e disponibilità',\n    icon: /*#__PURE__*/_jsxDEV(ListIcon, {}, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 179,\n      columnNumber: 13\n    }, this),\n    color: 'secondary',\n    features: ['Materiali per tipologia', 'Consumi vs disponibilità', 'Previsioni acquisti', 'Analisi costi']\n  }, {\n    id: 'posa-periodo',\n    title: 'Report Posa per Periodo',\n    description: 'Analisi temporale della posa con trend e pattern di lavoro',\n    icon: /*#__PURE__*/_jsxDEV(TimelineIcon, {}, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 188,\n      columnNumber: 13\n    }, this),\n    color: 'warning',\n    features: ['Trend temporali', 'Performance periodiche', 'Analisi stagionali', 'Produttività team']\n  }];\n\n  // Nuova funzione per generare report con formato specificato\n  const generateReportWithFormat = async (reportType, format) => {\n    try {\n      setLoading(true);\n      setError(null);\n      let response;\n      switch (reportType) {\n        case 'progress':\n          response = await reportService.getProgressReport(cantiereId, format);\n          break;\n        case 'boq':\n          response = await reportService.getBillOfQuantities(cantiereId, format);\n          break;\n        case 'posa-periodo':\n          if (!formData.data_inizio || !formData.data_fine) {\n            setError('Seleziona le date di inizio e fine periodo');\n            return;\n          }\n          response = await reportService.getPosaPerPeriodoReport(cantiereId, formData.data_inizio, formData.data_fine, format);\n          break;\n        default:\n          throw new Error('Tipo di report non riconosciuto');\n      }\n      if (format === 'video') {\n        // For special reports, update the specific report data\n        if (reportType === 'bobina-specifica' || reportType === 'posa-periodo') {\n          setReportsData(prev => ({\n            ...prev,\n            [reportType === 'bobina-specifica' ? 'bobinaSpecifica' : 'posaPeriodo']: response.content\n          }));\n        }\n        setReportData(response.content);\n      } else {\n        // Per PDF/Excel, apri il link di download\n        if (response.file_url) {\n          window.open(response.file_url, '_blank');\n        }\n      }\n    } catch (err) {\n      console.error('Errore nella generazione del report:', err);\n      setError(err.detail || err.message || 'Errore durante la generazione del report');\n    } finally {\n      setLoading(false);\n    }\n  };\n  const handleReportSelect = reportType => {\n    setSelectedReport(reportType);\n    setDialogType(reportType.id);\n\n    // Per report che necessitano di parametri aggiuntivi, mostra il dialog\n    if (reportType.id === 'posa-periodo' || reportType.id === 'bobina-specifica') {\n      // Imposta valori di default per alcuni report\n      if (reportType.id === 'posa-periodo') {\n        const today = new Date();\n        const lastMonth = new Date();\n        lastMonth.setMonth(today.getMonth() - 1);\n        setFormData({\n          ...formData,\n          data_inizio: lastMonth.toISOString().split('T')[0],\n          data_fine: today.toISOString().split('T')[0]\n        });\n      }\n      setOpenDialog(true);\n    } else {\n      // Per report senza parametri aggiuntivi, genera direttamente con formato 'video'\n      generateReportWithFormat(reportType.id, 'video');\n    }\n  };\n  const handleGenerateReport = async () => {\n    await generateReportWithFormat(dialogType, formData.formato);\n    setOpenDialog(false);\n  };\n  const handleCloseDialog = () => {\n    setOpenDialog(false);\n    setError(null);\n    setFormData({\n      formato: 'video',\n      data_inizio: '',\n      data_fine: '',\n      id_bobina: ''\n    });\n  };\n  const renderReportContent = () => {\n    if (!reportData) return null;\n    return /*#__PURE__*/_jsxDEV(Paper, {\n      sx: {\n        p: 3,\n        mt: 3\n      },\n      children: [/*#__PURE__*/_jsxDEV(Box, {\n        sx: {\n          display: 'flex',\n          justifyContent: 'space-between',\n          alignItems: 'center',\n          mb: 2\n        },\n        children: [/*#__PURE__*/_jsxDEV(Typography, {\n          variant: \"h6\",\n          children: [selectedReport === null || selectedReport === void 0 ? void 0 : selectedReport.title, \" - \", reportData.nome_cantiere]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 298,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Box, {\n          sx: {\n            display: 'flex',\n            gap: 1\n          },\n          children: [/*#__PURE__*/_jsxDEV(Button, {\n            startIcon: /*#__PURE__*/_jsxDEV(DownloadIcon, {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 304,\n              columnNumber: 26\n            }, this),\n            onClick: () => generateReportWithFormat(dialogType, 'pdf'),\n            variant: \"outlined\",\n            size: \"small\",\n            color: \"primary\",\n            children: \"PDF\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 303,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Button, {\n            startIcon: /*#__PURE__*/_jsxDEV(DownloadIcon, {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 313,\n              columnNumber: 26\n            }, this),\n            onClick: () => generateReportWithFormat(dialogType, 'excel'),\n            variant: \"outlined\",\n            size: \"small\",\n            color: \"success\",\n            children: \"Excel\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 312,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Button, {\n            startIcon: /*#__PURE__*/_jsxDEV(RefreshIcon, {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 322,\n              columnNumber: 26\n            }, this),\n            onClick: () => setReportData(null),\n            variant: \"outlined\",\n            size: \"small\",\n            children: \"Nuovo Report\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 321,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 301,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 297,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Divider, {\n        sx: {\n          mb: 3\n        }\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 332,\n        columnNumber: 9\n      }, this), dialogType === 'progress' && renderProgressReport(reportData), dialogType === 'boq' && renderBoqReport(reportData), dialogType === 'posa-periodo' && renderPosaPeriodoReport(reportData)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 296,\n      columnNumber: 7\n    }, this);\n  };\n  const renderProgressReport = data => /*#__PURE__*/_jsxDEV(Box, {\n    children: [/*#__PURE__*/_jsxDEV(Box, {\n      sx: {\n        display: 'flex',\n        justifyContent: 'space-between',\n        alignItems: 'center',\n        mb: 3,\n        p: 2,\n        bgcolor: '#f8f9fa',\n        borderRadius: 2,\n        border: '1px solid #e0e0e0'\n      },\n      children: [/*#__PURE__*/_jsxDEV(Typography, {\n        variant: \"h5\",\n        sx: {\n          fontWeight: 600,\n          color: '#2c3e50'\n        },\n        children: \"\\uD83D\\uDCCA Report Avanzamento Lavori\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 355,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(FormControlLabel, {\n        control: /*#__PURE__*/_jsxDEV(Switch, {\n          checked: showCharts,\n          onChange: e => setShowCharts(e.target.checked),\n          color: \"primary\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 360,\n          columnNumber: 13\n        }, this),\n        label: /*#__PURE__*/_jsxDEV(Box, {\n          sx: {\n            display: 'flex',\n            alignItems: 'center'\n          },\n          children: [/*#__PURE__*/_jsxDEV(ShowChartIcon, {\n            sx: {\n              mr: 1\n            }\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 368,\n            columnNumber: 15\n          }, this), \"Grafici\"]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 367,\n          columnNumber: 13\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 358,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 345,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Grid, {\n      container: true,\n      spacing: 3,\n      sx: {\n        mb: 4\n      },\n      children: [/*#__PURE__*/_jsxDEV(Grid, {\n        item: true,\n        xs: 12,\n        sm: 6,\n        md: 3,\n        children: /*#__PURE__*/_jsxDEV(MetricCard, {\n          title: \"Metri Totali\",\n          value: data.metri_totali,\n          unit: \"m\",\n          subtitle: \"Lunghezza complessiva del progetto\",\n          gradient: \"linear-gradient(135deg, #667eea 0%, #764ba2 100%)\",\n          size: \"medium\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 378,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 377,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Grid, {\n        item: true,\n        xs: 12,\n        sm: 6,\n        md: 3,\n        children: /*#__PURE__*/_jsxDEV(MetricCard, {\n          title: \"Metri Posati\",\n          value: data.metri_posati,\n          unit: \"m\",\n          subtitle: `${data.percentuale_avanzamento}% completato`,\n          gradient: \"linear-gradient(135deg, #f093fb 0%, #f5576c 100%)\",\n          progress: data.percentuale_avanzamento,\n          trend: data.percentuale_avanzamento > 50 ? 'up' : data.percentuale_avanzamento > 25 ? 'flat' : 'down',\n          trendValue: `${data.percentuale_avanzamento}%`,\n          size: \"medium\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 389,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 388,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Grid, {\n        item: true,\n        xs: 12,\n        sm: 6,\n        md: 3,\n        children: /*#__PURE__*/_jsxDEV(MetricCard, {\n          title: \"Metri Rimanenti\",\n          value: data.metri_da_posare,\n          unit: \"m\",\n          subtitle: `${(100 - data.percentuale_avanzamento).toFixed(1)}% da completare`,\n          gradient: \"linear-gradient(135deg, #4facfe 0%, #00f2fe 100%)\",\n          size: \"medium\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 403,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 402,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Grid, {\n        item: true,\n        xs: 12,\n        sm: 6,\n        md: 3,\n        children: /*#__PURE__*/_jsxDEV(MetricCard, {\n          title: \"Media/Giorno\",\n          value: data.media_giornaliera || 0,\n          unit: \"m\",\n          subtitle: data.giorni_stimati ? `${data.giorni_stimati} giorni lavorativi rimasti` : data.media_giornaliera > 0 ? 'Calcolo in corso' : 'Nessuna posa recente',\n          gradient: \"linear-gradient(135deg, #fa709a 0%, #fee140 100%)\",\n          size: \"medium\",\n          tooltip: data.giorni_lavorativi_effettivi ? `Calcolata su ${data.giorni_lavorativi_effettivi} giorni di lavoro effettivo. Include solo i giorni in cui è stata effettuata posa.` : 'Media giornaliera basata sui giorni di lavoro effettivo'\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 414,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 413,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 376,\n      columnNumber: 7\n    }, this), showCharts && /*#__PURE__*/_jsxDEV(Box, {\n      sx: {\n        mb: 4\n      },\n      children: /*#__PURE__*/_jsxDEV(ProgressChart, {\n        data: data\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 439,\n        columnNumber: 11\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 438,\n      columnNumber: 9\n    }, this), /*#__PURE__*/_jsxDEV(Grid, {\n      container: true,\n      spacing: 3,\n      sx: {\n        mb: 4\n      },\n      children: [/*#__PURE__*/_jsxDEV(Grid, {\n        item: true,\n        xs: 12,\n        md: 6,\n        children: /*#__PURE__*/_jsxDEV(Card, {\n          sx: {\n            height: '100%',\n            border: '1px solid #e0e0e0'\n          },\n          children: /*#__PURE__*/_jsxDEV(CardContent, {\n            sx: {\n              p: 3\n            },\n            children: [/*#__PURE__*/_jsxDEV(Box, {\n              sx: {\n                display: 'flex',\n                alignItems: 'center',\n                mb: 2\n              },\n              children: [/*#__PURE__*/_jsxDEV(CableIcon, {\n                sx: {\n                  color: '#3498db',\n                  mr: 1,\n                  fontSize: 28\n                }\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 449,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                variant: \"h6\",\n                sx: {\n                  fontWeight: 600,\n                  color: '#2c3e50'\n                },\n                children: \"Stato Cavi\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 450,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 448,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Grid, {\n              container: true,\n              spacing: 2,\n              children: [/*#__PURE__*/_jsxDEV(Grid, {\n                item: true,\n                xs: 6,\n                children: /*#__PURE__*/_jsxDEV(Box, {\n                  sx: {\n                    textAlign: 'center',\n                    p: 2,\n                    bgcolor: '#f8f9fa',\n                    borderRadius: 1\n                  },\n                  children: [/*#__PURE__*/_jsxDEV(Typography, {\n                    variant: \"h4\",\n                    sx: {\n                      fontWeight: 700,\n                      color: '#2c3e50',\n                      mb: 1\n                    },\n                    children: data.totale_cavi\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 457,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                    variant: \"body2\",\n                    sx: {\n                      color: '#666'\n                    },\n                    children: \"Cavi Totali\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 460,\n                    columnNumber: 21\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 456,\n                  columnNumber: 19\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 455,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(Grid, {\n                item: true,\n                xs: 6,\n                children: /*#__PURE__*/_jsxDEV(Box, {\n                  sx: {\n                    textAlign: 'center',\n                    p: 2,\n                    bgcolor: '#e8f5e8',\n                    borderRadius: 1\n                  },\n                  children: [/*#__PURE__*/_jsxDEV(Typography, {\n                    variant: \"h4\",\n                    sx: {\n                      fontWeight: 700,\n                      color: '#27ae60',\n                      mb: 1\n                    },\n                    children: data.cavi_posati\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 467,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                    variant: \"body2\",\n                    sx: {\n                      color: '#666'\n                    },\n                    children: [\"Cavi Posati (\", data.percentuale_cavi, \"%)\"]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 470,\n                    columnNumber: 21\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 466,\n                  columnNumber: 19\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 465,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 454,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Box, {\n              sx: {\n                mt: 2\n              },\n              children: [/*#__PURE__*/_jsxDEV(Box, {\n                sx: {\n                  display: 'flex',\n                  justifyContent: 'space-between',\n                  mb: 1\n                },\n                children: [/*#__PURE__*/_jsxDEV(Typography, {\n                  variant: \"body2\",\n                  children: \"Progresso\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 478,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                  variant: \"body2\",\n                  sx: {\n                    fontWeight: 600\n                  },\n                  children: [data.percentuale_cavi, \"%\"]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 479,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 477,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(Box, {\n                sx: {\n                  width: '100%',\n                  height: 8,\n                  bgcolor: '#e0e0e0',\n                  borderRadius: 4,\n                  overflow: 'hidden'\n                },\n                children: /*#__PURE__*/_jsxDEV(Box, {\n                  sx: {\n                    width: `${data.percentuale_cavi}%`,\n                    height: '100%',\n                    bgcolor: '#27ae60',\n                    transition: 'width 0.3s ease'\n                  }\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 490,\n                  columnNumber: 19\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 483,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 476,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 447,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 446,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 445,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Grid, {\n        item: true,\n        xs: 12,\n        md: 6,\n        children: /*#__PURE__*/_jsxDEV(Card, {\n          sx: {\n            height: '100%',\n            border: '1px solid #e0e0e0'\n          },\n          children: /*#__PURE__*/_jsxDEV(CardContent, {\n            sx: {\n              p: 3\n            },\n            children: [/*#__PURE__*/_jsxDEV(Box, {\n              sx: {\n                display: 'flex',\n                alignItems: 'center',\n                mb: 2\n              },\n              children: [/*#__PURE__*/_jsxDEV(TimelineIcon, {\n                sx: {\n                  color: '#e74c3c',\n                  mr: 1,\n                  fontSize: 28\n                }\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 506,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                variant: \"h6\",\n                sx: {\n                  fontWeight: 600,\n                  color: '#2c3e50'\n                },\n                children: \"Timeline Progetto\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 507,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 505,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Box, {\n              sx: {\n                textAlign: 'center',\n                mb: 2\n              },\n              children: [/*#__PURE__*/_jsxDEV(Typography, {\n                variant: \"h4\",\n                sx: {\n                  fontWeight: 700,\n                  color: '#e74c3c',\n                  mb: 1\n                },\n                children: [data.media_giornaliera || 0, \"m\"]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 512,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                variant: \"body2\",\n                sx: {\n                  color: '#666',\n                  mb: 1\n                },\n                children: \"Media Giornaliera\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 515,\n                columnNumber: 17\n              }, this), data.giorni_lavorativi_effettivi && /*#__PURE__*/_jsxDEV(Typography, {\n                variant: \"caption\",\n                sx: {\n                  color: '#999',\n                  fontSize: '0.75rem'\n                },\n                children: [\"Basata su \", data.giorni_lavorativi_effettivi, \" giorni di lavoro effettivo\"]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 519,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 511,\n              columnNumber: 15\n            }, this), data.giorni_stimati ? /*#__PURE__*/_jsxDEV(Box, {\n              sx: {\n                textAlign: 'center',\n                p: 2,\n                bgcolor: '#fff3cd',\n                borderRadius: 1\n              },\n              children: [/*#__PURE__*/_jsxDEV(Typography, {\n                variant: \"h6\",\n                sx: {\n                  fontWeight: 600,\n                  color: '#856404',\n                  mb: 0.5\n                },\n                children: data.data_completamento\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 526,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                variant: \"body2\",\n                sx: {\n                  color: '#856404'\n                },\n                children: [\"Completamento previsto in \", data.giorni_stimati, \" giorni\"]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 529,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 525,\n              columnNumber: 17\n            }, this) : /*#__PURE__*/_jsxDEV(Box, {\n              sx: {\n                textAlign: 'center',\n                p: 2,\n                bgcolor: '#f8f9fa',\n                borderRadius: 1\n              },\n              children: /*#__PURE__*/_jsxDEV(Typography, {\n                variant: \"body2\",\n                sx: {\n                  color: '#666'\n                },\n                children: data.media_giornaliera > 0 ? 'Timeline in calcolo...' : 'Necessaria attività di posa per calcolare la timeline'\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 535,\n                columnNumber: 19\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 534,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 504,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 503,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 502,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 444,\n      columnNumber: 7\n    }, this), data.posa_recente && data.posa_recente.length > 0 && /*#__PURE__*/_jsxDEV(Card, {\n      sx: {\n        border: '1px solid #e0e0e0'\n      },\n      children: /*#__PURE__*/_jsxDEV(CardContent, {\n        sx: {\n          p: 3\n        },\n        children: [/*#__PURE__*/_jsxDEV(Box, {\n          sx: {\n            display: 'flex',\n            alignItems: 'center',\n            mb: 3\n          },\n          children: [/*#__PURE__*/_jsxDEV(DateRangeIcon, {\n            sx: {\n              color: '#9b59b6',\n              mr: 1,\n              fontSize: 28\n            }\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 550,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(Typography, {\n            variant: \"h6\",\n            sx: {\n              fontWeight: 600,\n              color: '#2c3e50'\n            },\n            children: \"\\uD83D\\uDCC8 Attivit\\xE0 Recente\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 551,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 549,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(Grid, {\n          container: true,\n          spacing: 2,\n          children: data.posa_recente.slice(0, 5).map((posa, index) => /*#__PURE__*/_jsxDEV(Grid, {\n            item: true,\n            xs: 12,\n            sm: 6,\n            md: 4,\n            children: /*#__PURE__*/_jsxDEV(Box, {\n              sx: {\n                p: 2,\n                border: '1px solid #e0e0e0',\n                borderRadius: 2,\n                bgcolor: index === 0 ? '#f0f8ff' : '#fafafa',\n                transition: 'all 0.2s',\n                '&:hover': {\n                  bgcolor: '#f5f5f5',\n                  transform: 'translateY(-2px)',\n                  boxShadow: '0 4px 8px rgba(0,0,0,0.1)'\n                }\n              },\n              children: [/*#__PURE__*/_jsxDEV(Typography, {\n                variant: \"body2\",\n                sx: {\n                  color: '#666',\n                  mb: 1\n                },\n                children: posa.data\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 572,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                variant: \"h5\",\n                sx: {\n                  fontWeight: 700,\n                  color: '#2c3e50'\n                },\n                children: [posa.metri, \"m\"]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 575,\n                columnNumber: 21\n              }, this), index === 0 && /*#__PURE__*/_jsxDEV(Chip, {\n                label: \"Pi\\xF9 recente\",\n                size: \"small\",\n                sx: {\n                  mt: 1,\n                  bgcolor: '#3498db',\n                  color: 'white',\n                  fontSize: '0.7rem'\n                }\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 579,\n                columnNumber: 23\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 560,\n              columnNumber: 19\n            }, this)\n          }, index, false, {\n            fileName: _jsxFileName,\n            lineNumber: 559,\n            columnNumber: 17\n          }, this))\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 557,\n          columnNumber: 13\n        }, this), data.posa_recente.length > 5 && /*#__PURE__*/_jsxDEV(Box, {\n          sx: {\n            mt: 3,\n            textAlign: 'center'\n          },\n          children: /*#__PURE__*/_jsxDEV(Accordion, {\n            children: [/*#__PURE__*/_jsxDEV(AccordionSummary, {\n              expandIcon: /*#__PURE__*/_jsxDEV(ExpandMoreIcon, {}, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 599,\n                columnNumber: 49\n              }, this),\n              children: /*#__PURE__*/_jsxDEV(Typography, {\n                variant: \"body2\",\n                sx: {\n                  color: '#3498db'\n                },\n                children: [\"Mostra tutti i \", data.posa_recente.length, \" record\"]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 600,\n                columnNumber: 21\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 599,\n              columnNumber: 19\n            }, this), /*#__PURE__*/_jsxDEV(AccordionDetails, {\n              children: /*#__PURE__*/_jsxDEV(FilterableTable, {\n                data: data.posa_recente.map(posa => ({\n                  data: posa.data,\n                  metri: `${posa.metri}m`\n                })),\n                columns: [{\n                  field: 'data',\n                  headerName: 'Data',\n                  width: 200\n                }, {\n                  field: 'metri',\n                  headerName: 'Metri Posati',\n                  width: 150,\n                  align: 'right'\n                }],\n                pagination: true,\n                pageSize: 10\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 605,\n                columnNumber: 21\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 604,\n              columnNumber: 19\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 598,\n            columnNumber: 17\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 597,\n          columnNumber: 15\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 548,\n        columnNumber: 11\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 547,\n      columnNumber: 9\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 343,\n    columnNumber: 5\n  }, this);\n  const renderBoqReport = data => /*#__PURE__*/_jsxDEV(Box, {\n    children: [/*#__PURE__*/_jsxDEV(Box, {\n      sx: {\n        display: 'flex',\n        alignItems: 'center',\n        mb: 3,\n        p: 2,\n        bgcolor: '#f8f9fa',\n        borderRadius: 2,\n        border: '1px solid #e0e0e0'\n      },\n      children: [/*#__PURE__*/_jsxDEV(ListIcon, {\n        sx: {\n          color: '#8e44ad',\n          mr: 1,\n          fontSize: 28\n        }\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 639,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Typography, {\n        variant: \"h5\",\n        sx: {\n          fontWeight: 600,\n          color: '#2c3e50'\n        },\n        children: \"\\uD83D\\uDCCB Bill of Quantities - Distinta Materiali\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 640,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 630,\n      columnNumber: 7\n    }, this), showCharts && /*#__PURE__*/_jsxDEV(Box, {\n      sx: {\n        mb: 4\n      },\n      children: /*#__PURE__*/_jsxDEV(BoqChart, {\n        data: data\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 648,\n        columnNumber: 11\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 647,\n      columnNumber: 9\n    }, this), /*#__PURE__*/_jsxDEV(Card, {\n      sx: {\n        mb: 3,\n        border: '1px solid #e0e0e0'\n      },\n      children: /*#__PURE__*/_jsxDEV(CardContent, {\n        sx: {\n          p: 3\n        },\n        children: [/*#__PURE__*/_jsxDEV(Box, {\n          sx: {\n            display: 'flex',\n            alignItems: 'center',\n            mb: 3\n          },\n          children: [/*#__PURE__*/_jsxDEV(CableIcon, {\n            sx: {\n              color: '#e67e22',\n              mr: 1,\n              fontSize: 24\n            }\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 656,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Typography, {\n            variant: \"h6\",\n            sx: {\n              fontWeight: 600,\n              color: '#2c3e50'\n            },\n            children: \"Cavi per Tipologia\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 657,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 655,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(FilterableTable, {\n          data: data.cavi_per_tipo || [],\n          columns: [{\n            field: 'tipologia',\n            headerName: 'Tipologia',\n            width: 150\n          }, {\n            field: 'sezione',\n            headerName: 'Sezione',\n            width: 100\n          }, {\n            field: 'num_cavi',\n            headerName: 'Cavi',\n            width: 80,\n            align: 'right',\n            dataType: 'number'\n          }, {\n            field: 'metri_teorici',\n            headerName: 'Metri Teorici',\n            width: 120,\n            align: 'right',\n            dataType: 'number',\n            renderCell: row => `${row.metri_teorici}m`\n          }, {\n            field: 'metri_reali',\n            headerName: 'Metri Reali',\n            width: 120,\n            align: 'right',\n            dataType: 'number',\n            renderCell: row => `${row.metri_reali}m`\n          }, {\n            field: 'metri_da_posare',\n            headerName: 'Da Posare',\n            width: 120,\n            align: 'right',\n            dataType: 'number',\n            renderCell: row => `${row.metri_da_posare}m`\n          }],\n          pageSize: 10\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 661,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 654,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 653,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Card, {\n      sx: {\n        border: '1px solid #e0e0e0'\n      },\n      children: /*#__PURE__*/_jsxDEV(CardContent, {\n        sx: {\n          p: 3\n        },\n        children: [/*#__PURE__*/_jsxDEV(Box, {\n          sx: {\n            display: 'flex',\n            alignItems: 'center',\n            mb: 3\n          },\n          children: [/*#__PURE__*/_jsxDEV(InventoryIcon, {\n            sx: {\n              color: '#16a085',\n              mr: 1,\n              fontSize: 24\n            }\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 683,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Typography, {\n            variant: \"h6\",\n            sx: {\n              fontWeight: 600,\n              color: '#2c3e50'\n            },\n            children: \"Bobine Disponibili\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 684,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 682,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(FilterableTable, {\n          data: data.bobine_per_tipo || [],\n          columns: [{\n            field: 'tipologia',\n            headerName: 'Tipologia',\n            width: 150\n          }, {\n            field: 'sezione',\n            headerName: 'Sezione',\n            width: 100\n          }, {\n            field: 'num_bobine',\n            headerName: 'Bobine',\n            width: 100,\n            align: 'right',\n            dataType: 'number'\n          }, {\n            field: 'metri_disponibili',\n            headerName: 'Metri Disponibili',\n            width: 150,\n            align: 'right',\n            dataType: 'number',\n            renderCell: row => `${row.metri_disponibili}m`\n          }],\n          pageSize: 10\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 688,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 681,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 680,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 628,\n    columnNumber: 5\n  }, this);\n  const renderPosaPeriodoReport = data => /*#__PURE__*/_jsxDEV(Box, {\n    children: [/*#__PURE__*/_jsxDEV(Box, {\n      sx: {\n        display: 'flex',\n        justifyContent: 'space-between',\n        alignItems: 'center',\n        mb: 3\n      },\n      children: [/*#__PURE__*/_jsxDEV(Typography, {\n        variant: \"h5\",\n        sx: {\n          fontWeight: 600,\n          color: 'warning.main'\n        },\n        children: \"Report Posa per Periodo\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 712,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(FormControlLabel, {\n        control: /*#__PURE__*/_jsxDEV(Switch, {\n          checked: showCharts,\n          onChange: e => setShowCharts(e.target.checked),\n          color: \"primary\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 717,\n          columnNumber: 13\n        }, this),\n        label: /*#__PURE__*/_jsxDEV(Box, {\n          sx: {\n            display: 'flex',\n            alignItems: 'center'\n          },\n          children: [/*#__PURE__*/_jsxDEV(ShowChartIcon, {\n            sx: {\n              mr: 1\n            }\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 725,\n            columnNumber: 15\n          }, this), \"Grafici\"]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 724,\n          columnNumber: 13\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 715,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 711,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Grid, {\n      container: true,\n      spacing: 3,\n      sx: {\n        mb: 4\n      },\n      children: [/*#__PURE__*/_jsxDEV(Grid, {\n        item: true,\n        xs: 12,\n        md: 3,\n        children: /*#__PURE__*/_jsxDEV(Paper, {\n          sx: {\n            p: 3,\n            textAlign: 'center',\n            bgcolor: 'warning.main',\n            color: 'white'\n          },\n          children: [/*#__PURE__*/_jsxDEV(Typography, {\n            variant: \"h4\",\n            sx: {\n              fontWeight: 'bold',\n              mb: 1\n            },\n            children: [data.totale_metri_periodo, \"m\"]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 736,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Typography, {\n            variant: \"body1\",\n            children: \"Metri Totali\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 739,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Typography, {\n            variant: \"caption\",\n            children: [data.data_inizio, \" - \", data.data_fine]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 740,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 735,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 734,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Grid, {\n        item: true,\n        xs: 12,\n        md: 3,\n        children: /*#__PURE__*/_jsxDEV(Paper, {\n          sx: {\n            p: 3,\n            textAlign: 'center',\n            bgcolor: 'info.main',\n            color: 'white'\n          },\n          children: [/*#__PURE__*/_jsxDEV(Typography, {\n            variant: \"h4\",\n            sx: {\n              fontWeight: 'bold',\n              mb: 1\n            },\n            children: data.giorni_attivi\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 745,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Typography, {\n            variant: \"body1\",\n            children: \"Giorni Attivi\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 748,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 744,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 743,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Grid, {\n        item: true,\n        xs: 12,\n        md: 3,\n        children: /*#__PURE__*/_jsxDEV(Paper, {\n          sx: {\n            p: 3,\n            textAlign: 'center',\n            bgcolor: 'success.main',\n            color: 'white'\n          },\n          children: [/*#__PURE__*/_jsxDEV(Typography, {\n            variant: \"h4\",\n            sx: {\n              fontWeight: 'bold',\n              mb: 1\n            },\n            children: [data.media_giornaliera, \"m\"]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 753,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Typography, {\n            variant: \"body1\",\n            children: \"Media/Giorno\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 756,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 752,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 751,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Grid, {\n        item: true,\n        xs: 12,\n        md: 3,\n        children: /*#__PURE__*/_jsxDEV(Paper, {\n          sx: {\n            p: 3,\n            textAlign: 'center',\n            bgcolor: 'primary.main',\n            color: 'white'\n          },\n          children: [/*#__PURE__*/_jsxDEV(Typography, {\n            variant: \"h4\",\n            sx: {\n              fontWeight: 'bold',\n              mb: 1\n            },\n            children: [Math.round(data.totale_metri_periodo / data.giorni_attivi * 7), \"m\"]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 761,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Typography, {\n            variant: \"body1\",\n            children: \"Media/Settimana\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 764,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 760,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 759,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 733,\n      columnNumber: 7\n    }, this), showCharts && /*#__PURE__*/_jsxDEV(Box, {\n      sx: {\n        mb: 4\n      },\n      children: /*#__PURE__*/_jsxDEV(TimelineChart, {\n        data: data\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 772,\n        columnNumber: 11\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 771,\n      columnNumber: 9\n    }, this), /*#__PURE__*/_jsxDEV(Paper, {\n      sx: {\n        p: 3\n      },\n      children: [/*#__PURE__*/_jsxDEV(Typography, {\n        variant: \"h6\",\n        sx: {\n          mb: 2,\n          fontWeight: 600\n        },\n        children: \"Dettaglio Posa Giornaliera\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 778,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(FilterableTable, {\n        data: data.posa_giornaliera || [],\n        columns: [{\n          field: 'data',\n          headerName: 'Data',\n          width: 200\n        }, {\n          field: 'metri',\n          headerName: 'Metri Posati',\n          width: 150,\n          align: 'right',\n          dataType: 'number',\n          renderCell: row => `${row.metri}m`\n        }],\n        pageSize: 10\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 781,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 777,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 709,\n    columnNumber: 5\n  }, this);\n  const renderDialog = () => /*#__PURE__*/_jsxDEV(Dialog, {\n    open: openDialog,\n    onClose: handleCloseDialog,\n    maxWidth: \"sm\",\n    fullWidth: true,\n    children: [/*#__PURE__*/_jsxDEV(DialogTitle, {\n      children: selectedReport === null || selectedReport === void 0 ? void 0 : selectedReport.title\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 798,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(DialogContent, {\n      children: [error && /*#__PURE__*/_jsxDEV(Alert, {\n        severity: \"error\",\n        sx: {\n          mb: 2\n        },\n        children: error\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 803,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(Grid, {\n        container: true,\n        spacing: 2,\n        sx: {\n          mt: 1\n        },\n        children: [/*#__PURE__*/_jsxDEV(Grid, {\n          item: true,\n          xs: 12,\n          children: /*#__PURE__*/_jsxDEV(FormControl, {\n            fullWidth: true,\n            children: [/*#__PURE__*/_jsxDEV(InputLabel, {\n              children: \"Formato\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 811,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Select, {\n              value: formData.formato,\n              label: \"Formato\",\n              onChange: e => setFormData({\n                ...formData,\n                formato: e.target.value\n              }),\n              children: [/*#__PURE__*/_jsxDEV(MenuItem, {\n                value: \"video\",\n                children: \"Visualizza a schermo\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 817,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(MenuItem, {\n                value: \"pdf\",\n                children: \"Download PDF\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 818,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(MenuItem, {\n                value: \"excel\",\n                children: \"Download Excel\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 819,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 812,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 810,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 809,\n          columnNumber: 11\n        }, this), dialogType === 'bobina-specifica' && /*#__PURE__*/_jsxDEV(Grid, {\n          item: true,\n          xs: 12,\n          children: /*#__PURE__*/_jsxDEV(TextField, {\n            fullWidth: true,\n            label: \"ID Bobina\",\n            value: formData.id_bobina,\n            onChange: e => setFormData({\n              ...formData,\n              id_bobina: e.target.value\n            }),\n            placeholder: \"Es: 1, 2, A, B...\",\n            helperText: \"Inserisci solo la parte finale dell'ID (es: 1 per C1_B1)\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 826,\n            columnNumber: 15\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 825,\n          columnNumber: 13\n        }, this), dialogType === 'posa-periodo' && /*#__PURE__*/_jsxDEV(_Fragment, {\n          children: [/*#__PURE__*/_jsxDEV(Grid, {\n            item: true,\n            xs: 6,\n            children: /*#__PURE__*/_jsxDEV(TextField, {\n              fullWidth: true,\n              type: \"date\",\n              label: \"Data Inizio\",\n              value: formData.data_inizio,\n              onChange: e => setFormData({\n                ...formData,\n                data_inizio: e.target.value\n              }),\n              InputLabelProps: {\n                shrink: true\n              }\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 840,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 839,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(Grid, {\n            item: true,\n            xs: 6,\n            children: /*#__PURE__*/_jsxDEV(TextField, {\n              fullWidth: true,\n              type: \"date\",\n              label: \"Data Fine\",\n              value: formData.data_fine,\n              onChange: e => setFormData({\n                ...formData,\n                data_fine: e.target.value\n              }),\n              InputLabelProps: {\n                shrink: true\n              }\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 850,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 849,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 808,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 801,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(DialogActions, {\n      children: [/*#__PURE__*/_jsxDEV(Button, {\n        onClick: handleCloseDialog,\n        children: \"Annulla\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 864,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Button, {\n        onClick: handleGenerateReport,\n        variant: \"contained\",\n        disabled: loading,\n        startIcon: loading ? /*#__PURE__*/_jsxDEV(CircularProgress, {\n          size: 20\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 869,\n          columnNumber: 32\n        }, this) : /*#__PURE__*/_jsxDEV(VisibilityIcon, {}, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 869,\n          columnNumber: 65\n        }, this),\n        children: loading ? 'Generazione...' : 'Genera Report'\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 865,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 863,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 797,\n    columnNumber: 5\n  }, this);\n  return /*#__PURE__*/_jsxDEV(Box, {\n    className: \"report-main-container report-fade-in\",\n    children: [/*#__PURE__*/_jsxDEV(Box, {\n      sx: {\n        display: 'flex',\n        justifyContent: 'flex-end',\n        alignItems: 'center',\n        mb: 2\n      },\n      children: /*#__PURE__*/_jsxDEV(AdminHomeButton, {}, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 881,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 880,\n      columnNumber: 7\n    }, this), loading && /*#__PURE__*/_jsxDEV(Box, {\n      sx: {\n        display: 'flex',\n        justifyContent: 'center',\n        my: 4\n      },\n      children: /*#__PURE__*/_jsxDEV(CircularProgress, {}, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 887,\n        columnNumber: 11\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 886,\n      columnNumber: 9\n    }, this), /*#__PURE__*/_jsxDEV(Box, {\n      sx: {\n        mt: 3\n      },\n      children: [/*#__PURE__*/_jsxDEV(Box, {\n        sx: {\n          mb: 3\n        },\n        children: [/*#__PURE__*/_jsxDEV(Typography, {\n          variant: \"h6\",\n          sx: {\n            fontWeight: 600,\n            color: '#2c3e50',\n            mb: 2,\n            textAlign: 'center'\n          },\n          children: \"\\uD83C\\uDFAF Seleziona il tipo di report\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 895,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Grid, {\n          container: true,\n          spacing: 2,\n          children: [/*#__PURE__*/_jsxDEV(Grid, {\n            item: true,\n            xs: 6,\n            sm: 4,\n            md: 3,\n            children: /*#__PURE__*/_jsxDEV(Card, {\n              className: `report-card ${selectedReportType === 'progress' ? 'report-card-selected' : ''}`,\n              sx: {\n                height: '140px',\n                cursor: 'pointer',\n                border: selectedReportType === 'progress' ? '2px solid #3498db' : '1px solid #e0e0e0',\n                bgcolor: selectedReportType === 'progress' ? '#f0f8ff' : 'white',\n                transition: 'all 0.2s'\n              },\n              onClick: () => setSelectedReportType('progress'),\n              children: /*#__PURE__*/_jsxDEV(CardContent, {\n                sx: {\n                  p: 2,\n                  textAlign: 'center',\n                  height: '100%',\n                  display: 'flex',\n                  flexDirection: 'column',\n                  justifyContent: 'center'\n                },\n                children: [/*#__PURE__*/_jsxDEV(AssessmentIcon, {\n                  sx: {\n                    fontSize: 32,\n                    color: '#3498db',\n                    mb: 1\n                  }\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 913,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                  variant: \"subtitle1\",\n                  sx: {\n                    fontWeight: 600,\n                    mb: 0.5,\n                    fontSize: '1.1rem'\n                  },\n                  children: \"Avanzamento\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 914,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                  variant: \"body2\",\n                  sx: {\n                    color: '#666',\n                    fontSize: '0.9rem'\n                  },\n                  children: \"Panoramica lavori\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 917,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 912,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 901,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 900,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Grid, {\n            item: true,\n            xs: 6,\n            sm: 4,\n            md: 3,\n            children: /*#__PURE__*/_jsxDEV(Card, {\n              sx: {\n                height: '140px',\n                cursor: 'pointer',\n                border: selectedReportType === 'boq' ? '2px solid #8e44ad' : '1px solid #e0e0e0',\n                bgcolor: selectedReportType === 'boq' ? '#f8f4ff' : 'white',\n                transition: 'all 0.2s'\n              },\n              onClick: () => setSelectedReportType('boq'),\n              children: /*#__PURE__*/_jsxDEV(CardContent, {\n                sx: {\n                  p: 2,\n                  textAlign: 'center',\n                  height: '100%',\n                  display: 'flex',\n                  flexDirection: 'column',\n                  justifyContent: 'center'\n                },\n                children: [/*#__PURE__*/_jsxDEV(ListIcon, {\n                  sx: {\n                    fontSize: 32,\n                    color: '#8e44ad',\n                    mb: 1\n                  }\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 937,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                  variant: \"subtitle1\",\n                  sx: {\n                    fontWeight: 600,\n                    mb: 0.5,\n                    fontSize: '1.1rem'\n                  },\n                  children: \"Bill of Quantities\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 938,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                  variant: \"body2\",\n                  sx: {\n                    color: '#666',\n                    fontSize: '0.9rem'\n                  },\n                  children: \"Distinta materiali\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 941,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 936,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 926,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 925,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Grid, {\n            item: true,\n            xs: 6,\n            sm: 4,\n            md: 3,\n            children: /*#__PURE__*/_jsxDEV(Card, {\n              sx: {\n                height: '140px',\n                cursor: 'pointer',\n                border: selectedReportType === 'posa-periodo' ? '2px solid #9b59b6' : '1px solid #e0e0e0',\n                bgcolor: selectedReportType === 'posa-periodo' ? '#f8f4ff' : 'white',\n                transition: 'all 0.2s'\n              },\n              onClick: () => setSelectedReportType('posa-periodo'),\n              children: /*#__PURE__*/_jsxDEV(CardContent, {\n                sx: {\n                  p: 2,\n                  textAlign: 'center',\n                  height: '100%',\n                  display: 'flex',\n                  flexDirection: 'column',\n                  justifyContent: 'center'\n                },\n                children: [/*#__PURE__*/_jsxDEV(TimelineIcon, {\n                  sx: {\n                    fontSize: 32,\n                    color: '#9b59b6',\n                    mb: 1\n                  }\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 963,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                  variant: \"subtitle1\",\n                  sx: {\n                    fontWeight: 600,\n                    mb: 0.5,\n                    fontSize: '1.1rem'\n                  },\n                  children: \"Posa per Periodo\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 964,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                  variant: \"body2\",\n                  sx: {\n                    color: '#666',\n                    fontSize: '0.9rem'\n                  },\n                  children: \"Analisi temporale\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 967,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 962,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 952,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 951,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 898,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 894,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Box, {\n        sx: {\n          minHeight: '400px'\n        },\n        children: [selectedReportType === 'progress' && /*#__PURE__*/_jsxDEV(Paper, {\n          sx: {\n            p: 3\n          },\n          children: reportsData.progress ? /*#__PURE__*/_jsxDEV(Box, {\n            children: [/*#__PURE__*/_jsxDEV(Box, {\n              sx: {\n                display: 'flex',\n                justifyContent: 'flex-end',\n                mb: 2\n              },\n              children: [/*#__PURE__*/_jsxDEV(Button, {\n                startIcon: /*#__PURE__*/_jsxDEV(DownloadIcon, {}, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 985,\n                  columnNumber: 34\n                }, this),\n                onClick: () => generateReportWithFormat('progress', 'pdf'),\n                variant: \"outlined\",\n                size: \"small\",\n                color: \"primary\",\n                sx: {\n                  mr: 1\n                },\n                children: \"PDF\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 984,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(Button, {\n                startIcon: /*#__PURE__*/_jsxDEV(DownloadIcon, {}, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 995,\n                  columnNumber: 34\n                }, this),\n                onClick: () => generateReportWithFormat('progress', 'excel'),\n                variant: \"outlined\",\n                size: \"small\",\n                color: \"success\",\n                children: \"Excel\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 994,\n                columnNumber: 21\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 983,\n              columnNumber: 19\n            }, this), renderProgressReport(reportsData.progress)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 982,\n            columnNumber: 17\n          }, this) : loading ? /*#__PURE__*/_jsxDEV(EmptyState, {\n            type: \"loading\",\n            reportType: \"progress\",\n            title: \"Caricamento Report Avanzamento...\",\n            description: \"Stiamo elaborando i dati dell'avanzamento dei lavori\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 1007,\n            columnNumber: 17\n          }, this) : /*#__PURE__*/_jsxDEV(EmptyState, {\n            type: \"error\",\n            reportType: \"progress\",\n            title: \"Errore nel caricamento\",\n            description: \"Impossibile caricare il report di avanzamento. Verifica la connessione e riprova.\",\n            onRetry: () => {\n              setLoading(true);\n              reportService.getProgressReport(cantiereId, 'video').then(data => {\n                setReportsData(prev => ({\n                  ...prev,\n                  progress: data.content\n                }));\n              }).catch(err => {\n                console.error('Error retrying progress report:', err);\n              }).finally(() => {\n                setLoading(false);\n              });\n            },\n            loading: loading\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 1014,\n            columnNumber: 17\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 980,\n          columnNumber: 13\n        }, this), selectedReportType === 'boq' && /*#__PURE__*/_jsxDEV(Paper, {\n          sx: {\n            p: 3\n          },\n          children: reportsData.boq ? /*#__PURE__*/_jsxDEV(Box, {\n            children: [/*#__PURE__*/_jsxDEV(Box, {\n              sx: {\n                display: 'flex',\n                justifyContent: 'flex-end',\n                mb: 2\n              },\n              children: [/*#__PURE__*/_jsxDEV(Button, {\n                startIcon: /*#__PURE__*/_jsxDEV(DownloadIcon, {}, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1048,\n                  columnNumber: 34\n                }, this),\n                onClick: () => generateReportWithFormat('boq', 'pdf'),\n                variant: \"outlined\",\n                size: \"small\",\n                color: \"primary\",\n                sx: {\n                  mr: 1\n                },\n                children: \"PDF\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1047,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(Button, {\n                startIcon: /*#__PURE__*/_jsxDEV(DownloadIcon, {}, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1058,\n                  columnNumber: 34\n                }, this),\n                onClick: () => generateReportWithFormat('boq', 'excel'),\n                variant: \"outlined\",\n                size: \"small\",\n                color: \"success\",\n                children: \"Excel\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1057,\n                columnNumber: 21\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 1046,\n              columnNumber: 19\n            }, this), renderBoqReport(reportsData.boq)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 1045,\n            columnNumber: 17\n          }, this) : loading ? /*#__PURE__*/_jsxDEV(EmptyState, {\n            type: \"loading\",\n            reportType: \"boq\",\n            title: \"Caricamento Bill of Quantities...\",\n            description: \"Stiamo elaborando la distinta materiali\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 1070,\n            columnNumber: 17\n          }, this) : /*#__PURE__*/_jsxDEV(EmptyState, {\n            type: \"error\",\n            reportType: \"boq\",\n            title: \"Errore nel caricamento\",\n            description: \"Impossibile caricare la distinta materiali. Verifica la connessione e riprova.\",\n            onRetry: () => {\n              setLoading(true);\n              reportService.getBillOfQuantities(cantiereId, 'video').then(data => {\n                setReportsData(prev => ({\n                  ...prev,\n                  boq: data.content\n                }));\n              }).catch(err => {\n                console.error('Error retrying BOQ report:', err);\n              }).finally(() => {\n                setLoading(false);\n              });\n            },\n            loading: loading\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 1077,\n            columnNumber: 17\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 1043,\n          columnNumber: 13\n        }, this), selectedReportType === 'posa-periodo' && /*#__PURE__*/_jsxDEV(Paper, {\n          sx: {\n            p: 3\n          },\n          children: reportsData.posaPeriodo ? /*#__PURE__*/_jsxDEV(Box, {\n            children: [/*#__PURE__*/_jsxDEV(Box, {\n              sx: {\n                display: 'flex',\n                justifyContent: 'flex-end',\n                mb: 2\n              },\n              children: [/*#__PURE__*/_jsxDEV(Button, {\n                startIcon: /*#__PURE__*/_jsxDEV(DownloadIcon, {}, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1113,\n                  columnNumber: 34\n                }, this),\n                onClick: () => generateReportWithFormat('posa-periodo', 'pdf'),\n                variant: \"outlined\",\n                size: \"small\",\n                color: \"primary\",\n                sx: {\n                  mr: 1\n                },\n                children: \"PDF\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1112,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(Button, {\n                startIcon: /*#__PURE__*/_jsxDEV(DownloadIcon, {}, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1123,\n                  columnNumber: 34\n                }, this),\n                onClick: () => generateReportWithFormat('posa-periodo', 'excel'),\n                variant: \"outlined\",\n                size: \"small\",\n                color: \"success\",\n                children: \"Excel\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1122,\n                columnNumber: 21\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 1111,\n              columnNumber: 19\n            }, this), renderPosaPeriodoReport(reportsData.posaPeriodo)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 1110,\n            columnNumber: 17\n          }, this) : /*#__PURE__*/_jsxDEV(EmptyState, {\n            type: \"action-required\",\n            reportType: \"posa-periodo\",\n            title: \"Seleziona un Periodo\",\n            description: \"Scegli un intervallo di date per analizzare i trend temporali, pattern di lavoro e produttivit\\xE0 del team.\",\n            actionLabel: \"Seleziona Periodo\",\n            onAction: () => {\n              setDialogType('posa-periodo');\n              // Set default date range (last month to today)\n              const today = new Date();\n              const lastMonth = new Date();\n              lastMonth.setMonth(today.getMonth() - 1);\n              setFormData({\n                ...formData,\n                data_inizio: lastMonth.toISOString().split('T')[0],\n                data_fine: today.toISOString().split('T')[0]\n              });\n              setOpenDialog(true);\n            }\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 1135,\n            columnNumber: 17\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 1108,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 977,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 892,\n      columnNumber: 7\n    }, this), renderDialog()]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 878,\n    columnNumber: 5\n  }, this);\n};\n_s(ReportCaviPageNew, \"A7Tak7ZNqlgfHityJ1Jz8L67TXo=\", false, function () {\n  return [useNavigate, useParams, useAuth];\n});\n_c = ReportCaviPageNew;\nexport default ReportCaviPageNew;\nvar _c;\n$RefreshReg$(_c, \"ReportCaviPageNew\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "Box", "Typography", "Paper", "Grid", "Card", "<PERSON><PERSON><PERSON><PERSON>", "CardActions", "<PERSON><PERSON>", "Chip", "<PERSON><PERSON>", "CircularProgress", "Divider", "IconButton", "<PERSON><PERSON><PERSON>", "Dialog", "DialogTitle", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "DialogActions", "FormControl", "InputLabel", "Select", "MenuItem", "TextField", "Accordion", "AccordionSummary", "AccordionDetails", "Switch", "FormControlLabel", "Assessment", "AssessmentIcon", "<PERSON><PERSON><PERSON>", "BarChartIcon", "<PERSON><PERSON><PERSON>", "PieChartIcon", "Timeline", "TimelineIcon", "List", "ListIcon", "Download", "DownloadIcon", "Visibility", "VisibilityIcon", "Refresh", "RefreshIcon", "ArrowBack", "ArrowBackIcon", "DateRange", "DateRangeIcon", "Cable", "CableIcon", "Inventory", "InventoryIcon", "ExpandMore", "ExpandMoreIcon", "ShowChart", "ShowChartIcon", "useNavigate", "useParams", "useAuth", "AdminHomeButton", "reportService", "FilterableTable", "EmptyState", "MetricCard", "ReportSection", "ProgressChart", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "TimelineChart", "Cavi<PERSON>tato<PERSON>hart", "jsxDEV", "_jsxDEV", "Fragment", "_Fragment", "ReportCaviPageNew", "_s", "navigate", "cantiereId", "user", "loading", "setLoading", "error", "setError", "reportData", "setReportData", "selectedReport", "setSelectedReport", "openDialog", "setOpenDialog", "dialogType", "setDialogType", "selectedReportType", "setSelectedReportType", "formData", "setFormData", "formato", "data_inizio", "data_fine", "id_bobina", "reportsData", "setReportsData", "progress", "boq", "bobine", "caviStato", "bobinaSpecifica", "posaPeriodo", "show<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "loadAllReports", "progressPromise", "getProgressReport", "catch", "err", "console", "content", "boq<PERSON><PERSON><PERSON>", "getBillOfQuantities", "bob<PERSON><PERSON><PERSON><PERSON>", "getBobineReport", "caviStatoPromise", "getCaviStatoReport", "progressData", "boqData", "bobine<PERSON><PERSON>", "caviStatoData", "Promise", "all", "reportTypes", "id", "title", "description", "icon", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "color", "features", "generateReportWithFormat", "reportType", "format", "response", "getPosaPerPeriodoReport", "Error", "prev", "file_url", "window", "open", "detail", "message", "handleReportSelect", "today", "Date", "lastM<PERSON>h", "setMonth", "getMonth", "toISOString", "split", "handleGenerateReport", "handleCloseDialog", "renderReportContent", "sx", "p", "mt", "children", "display", "justifyContent", "alignItems", "mb", "variant", "nome_cantiere", "gap", "startIcon", "onClick", "size", "renderProgressReport", "renderBoqReport", "renderPosaPeriodoReport", "data", "bgcolor", "borderRadius", "border", "fontWeight", "control", "checked", "onChange", "e", "target", "label", "mr", "container", "spacing", "item", "xs", "sm", "md", "value", "metri_totali", "unit", "subtitle", "gradient", "metri_posati", "percentuale_avanzamento", "trend", "trendValue", "metri_da_posare", "toFixed", "media_giornaliera", "giorni_stimati", "tooltip", "giorni_lavorativi_effettivi", "height", "fontSize", "textAlign", "totale_cavi", "cavi_posati", "percentuale_cavi", "width", "overflow", "transition", "data_completamento", "posa_recente", "length", "slice", "map", "posa", "index", "transform", "boxShadow", "metri", "expandIcon", "columns", "field", "headerName", "align", "pagination", "pageSize", "cavi_per_tipo", "dataType", "renderCell", "row", "metri_te<PERSON>ci", "metri_reali", "bobine_per_tipo", "metri_disponibili", "totale_metri_periodo", "giorni_attivi", "Math", "round", "posa_giornal<PERSON>", "renderDialog", "onClose", "max<PERSON><PERSON><PERSON>", "fullWidth", "severity", "placeholder", "helperText", "type", "InputLabelProps", "shrink", "disabled", "className", "my", "cursor", "flexDirection", "minHeight", "onRetry", "then", "finally", "actionLabel", "onAction", "_c", "$RefreshReg$"], "sources": ["C:/CMS/webapp/frontend/src/pages/cavi/ReportCaviPageNew.js"], "sourcesContent": ["import React, { useState, useEffect } from 'react';\nimport '../../styles/reports.css';\nimport {\n  Box,\n  Typography,\n  Paper,\n  Grid,\n  Card,\n  CardContent,\n  CardActions,\n  Button,\n  Chip,\n  Alert,\n  CircularProgress,\n  Divider,\n  IconButton,\n  Tooltip,\n  Dialog,\n  DialogTitle,\n  DialogContent,\n  DialogActions,\n  FormControl,\n  InputLabel,\n  Select,\n  MenuItem,\n  TextField,\n  Accordion,\n  AccordionSummary,\n  AccordionDetails,\n  Switch,\n  FormControlLabel\n} from '@mui/material';\nimport {\n  Assessment as AssessmentIcon,\n  <PERSON><PERSON>hart as BarChartIcon,\n  PieChart as PieChartIcon,\n  Timeline as TimelineIcon,\n  List as ListIcon,\n  Download as DownloadIcon,\n  Visibility as VisibilityIcon,\n  Refresh as RefreshIcon,\n  ArrowBack as ArrowBackIcon,\n  DateRange as DateRangeIcon,\n  Cable as CableIcon,\n  Inventory as InventoryIcon,\n  ExpandMore as ExpandMoreIcon,\n  Show<PERSON>hart as ShowChartIcon\n} from '@mui/icons-material';\nimport { useNavigate, useParams } from 'react-router-dom';\nimport { useAuth } from '../../context/AuthContext';\nimport AdminHomeButton from '../../components/common/AdminHomeButton';\nimport reportService from '../../services/reportService';\nimport FilterableTable from '../../components/common/FilterableTable';\nimport EmptyState from '../../components/common/EmptyState';\nimport MetricCard from '../../components/common/MetricCard';\nimport ReportSection from '../../components/common/ReportSection';\n\n// Import dei componenti grafici\nimport ProgressChart from '../../components/charts/ProgressChart';\nimport BobineChart from '../../components/charts/BobineChart';\nimport BoqChart from '../../components/charts/BoqChart';\nimport TimelineChart from '../../components/charts/TimelineChart';\nimport CaviStatoChart from '../../components/charts/CaviStatoChart';\n\nconst ReportCaviPageNew = () => {\n  const navigate = useNavigate();\n  const { cantiereId } = useParams();\n  const { user } = useAuth();\n\n  const [loading, setLoading] = useState(false);\n  const [error, setError] = useState(null);\n  const [reportData, setReportData] = useState(null);\n  const [selectedReport, setSelectedReport] = useState(null);\n  const [openDialog, setOpenDialog] = useState(false);\n  const [dialogType, setDialogType] = useState('');\n  const [selectedReportType, setSelectedReportType] = useState('progress');\n  const [formData, setFormData] = useState({\n    formato: 'video',\n    data_inizio: '',\n    data_fine: '',\n    id_bobina: ''\n  });\n\n  // New state to store all report data\n  const [reportsData, setReportsData] = useState({\n    progress: null,\n    boq: null,\n    bobine: null,\n    caviStato: null,\n    bobinaSpecifica: null,\n    posaPeriodo: null\n  });\n\n  // State per controllo visualizzazione grafici\n  const [showCharts, setShowCharts] = useState(true);\n\n  // Load all basic reports on component mount\n  useEffect(() => {\n    const loadAllReports = async () => {\n      setLoading(true);\n      try {\n        // Create individual promises that handle their own errors\n        const progressPromise = reportService.getProgressReport(cantiereId, 'video')\n          .catch(err => {\n            console.error('Error loading progress report:', err);\n            return { content: null };\n          });\n\n        const boqPromise = reportService.getBillOfQuantities(cantiereId, 'video')\n          .catch(err => {\n            console.error('Error loading BOQ report:', err);\n            return { content: null };\n          });\n\n        const bobinePromise = reportService.getBobineReport(cantiereId, 'video')\n          .catch(err => {\n            console.error('Error loading bobine report:', err);\n            return { content: null };\n          });\n\n        const caviStatoPromise = reportService.getCaviStatoReport(cantiereId, 'video')\n          .catch(err => {\n            console.error('Error loading cavi stato report:', err);\n            return { content: null };\n          });\n\n        // Wait for all promises to resolve (they won't reject due to the catch handlers)\n        const [progressData, boqData, bobineData, caviStatoData] = await Promise.all([\n          progressPromise,\n          boqPromise,\n          bobinePromise,\n          caviStatoPromise\n        ]);\n\n        // Set the data for each report, even if some are null\n        setReportsData({\n          progress: progressData.content,\n          boq: boqData.content,\n          bobine: bobineData.content,\n          caviStato: caviStatoData.content,\n          bobinaSpecifica: null,\n          posaPeriodo: null\n        });\n\n        // Only set error to null if we successfully loaded at least one report\n        if (progressData.content || boqData.content || bobineData.content || caviStatoData.content) {\n          setError(null);\n        } else {\n          setError('Errore nel caricamento dei report. Riprova più tardi.');\n        }\n      } catch (err) {\n        // This catch block should rarely be hit due to the individual error handling above\n        console.error('Unexpected error loading reports:', err);\n        setError('Errore nel caricamento dei report. Riprova più tardi.');\n      } finally {\n        setLoading(false);\n      }\n    };\n\n    if (cantiereId) {\n      loadAllReports();\n    }\n  }, [cantiereId]);\n\n  // Configurazione dei report disponibili\n  const reportTypes = [\n    {\n      id: 'progress',\n      title: 'Report Avanzamento',\n      description: 'Panoramica completa dell\\'avanzamento dei lavori con metriche di performance e previsioni',\n      icon: <AssessmentIcon />,\n      color: 'primary',\n      features: ['Metri posati vs teorici', 'Percentuale completamento', 'Previsioni timeline', 'Performance giornaliera']\n    },\n    {\n      id: 'boq',\n      title: 'Bill of Quantities',\n      description: 'Distinta materiali dettagliata con analisi dei consumi e disponibilità',\n      icon: <ListIcon />,\n      color: 'secondary',\n      features: ['Materiali per tipologia', 'Consumi vs disponibilità', 'Previsioni acquisti', 'Analisi costi']\n    },\n\n    {\n      id: 'posa-periodo',\n      title: 'Report Posa per Periodo',\n      description: 'Analisi temporale della posa con trend e pattern di lavoro',\n      icon: <TimelineIcon />,\n      color: 'warning',\n      features: ['Trend temporali', 'Performance periodiche', 'Analisi stagionali', 'Produttività team']\n    },\n\n  ];\n\n  // Nuova funzione per generare report con formato specificato\n  const generateReportWithFormat = async (reportType, format) => {\n    try {\n      setLoading(true);\n      setError(null);\n\n      let response;\n\n      switch (reportType) {\n        case 'progress':\n          response = await reportService.getProgressReport(cantiereId, format);\n          break;\n        case 'boq':\n          response = await reportService.getBillOfQuantities(cantiereId, format);\n          break;\n\n        case 'posa-periodo':\n          if (!formData.data_inizio || !formData.data_fine) {\n            setError('Seleziona le date di inizio e fine periodo');\n            return;\n          }\n          response = await reportService.getPosaPerPeriodoReport(\n            cantiereId,\n            formData.data_inizio,\n            formData.data_fine,\n            format\n          );\n          break;\n        default:\n          throw new Error('Tipo di report non riconosciuto');\n      }\n\n      if (format === 'video') {\n        // For special reports, update the specific report data\n        if (reportType === 'bobina-specifica' || reportType === 'posa-periodo') {\n          setReportsData(prev => ({\n            ...prev,\n            [reportType === 'bobina-specifica' ? 'bobinaSpecifica' : 'posaPeriodo']: response.content\n          }));\n        }\n        setReportData(response.content);\n      } else {\n        // Per PDF/Excel, apri il link di download\n        if (response.file_url) {\n          window.open(response.file_url, '_blank');\n        }\n      }\n    } catch (err) {\n      console.error('Errore nella generazione del report:', err);\n      setError(err.detail || err.message || 'Errore durante la generazione del report');\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  const handleReportSelect = (reportType) => {\n    setSelectedReport(reportType);\n    setDialogType(reportType.id);\n\n    // Per report che necessitano di parametri aggiuntivi, mostra il dialog\n    if (reportType.id === 'posa-periodo' || reportType.id === 'bobina-specifica') {\n      // Imposta valori di default per alcuni report\n      if (reportType.id === 'posa-periodo') {\n        const today = new Date();\n        const lastMonth = new Date();\n        lastMonth.setMonth(today.getMonth() - 1);\n\n        setFormData({\n          ...formData,\n          data_inizio: lastMonth.toISOString().split('T')[0],\n          data_fine: today.toISOString().split('T')[0]\n        });\n      }\n\n      setOpenDialog(true);\n    } else {\n      // Per report senza parametri aggiuntivi, genera direttamente con formato 'video'\n      generateReportWithFormat(reportType.id, 'video');\n    }\n  };\n\n  const handleGenerateReport = async () => {\n    await generateReportWithFormat(dialogType, formData.formato);\n    setOpenDialog(false);\n  };\n\n  const handleCloseDialog = () => {\n    setOpenDialog(false);\n    setError(null);\n    setFormData({\n      formato: 'video',\n      data_inizio: '',\n      data_fine: '',\n      id_bobina: ''\n    });\n  };\n\n  const renderReportContent = () => {\n    if (!reportData) return null;\n\n    return (\n      <Paper sx={{ p: 3, mt: 3 }}>\n        <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 2 }}>\n          <Typography variant=\"h6\">\n            {selectedReport?.title} - {reportData.nome_cantiere}\n          </Typography>\n          <Box sx={{ display: 'flex', gap: 1 }}>\n            {/* Export buttons */}\n            <Button\n              startIcon={<DownloadIcon />}\n              onClick={() => generateReportWithFormat(dialogType, 'pdf')}\n              variant=\"outlined\"\n              size=\"small\"\n              color=\"primary\"\n            >\n              PDF\n            </Button>\n            <Button\n              startIcon={<DownloadIcon />}\n              onClick={() => generateReportWithFormat(dialogType, 'excel')}\n              variant=\"outlined\"\n              size=\"small\"\n              color=\"success\"\n            >\n              Excel\n            </Button>\n            <Button\n              startIcon={<RefreshIcon />}\n              onClick={() => setReportData(null)}\n              variant=\"outlined\"\n              size=\"small\"\n            >\n              Nuovo Report\n            </Button>\n          </Box>\n        </Box>\n\n        <Divider sx={{ mb: 3 }} />\n\n        {/* Renderizza il contenuto specifico del report */}\n        {dialogType === 'progress' && renderProgressReport(reportData)}\n        {dialogType === 'boq' && renderBoqReport(reportData)}\n        {dialogType === 'posa-periodo' && renderPosaPeriodoReport(reportData)}\n      </Paper>\n    );\n  };\n\n  const renderProgressReport = (data) => (\n    <Box>\n      {/* Header con controlli migliorato */}\n      <Box sx={{\n        display: 'flex',\n        justifyContent: 'space-between',\n        alignItems: 'center',\n        mb: 3,\n        p: 2,\n        bgcolor: '#f8f9fa',\n        borderRadius: 2,\n        border: '1px solid #e0e0e0'\n      }}>\n        <Typography variant=\"h5\" sx={{ fontWeight: 600, color: '#2c3e50' }}>\n          📊 Report Avanzamento Lavori\n        </Typography>\n        <FormControlLabel\n          control={\n            <Switch\n              checked={showCharts}\n              onChange={(e) => setShowCharts(e.target.checked)}\n              color=\"primary\"\n            />\n          }\n          label={\n            <Box sx={{ display: 'flex', alignItems: 'center' }}>\n              <ShowChartIcon sx={{ mr: 1 }} />\n              Grafici\n            </Box>\n          }\n        />\n      </Box>\n\n      {/* Metriche Principali - Cards Moderne con MetricCard */}\n      <Grid container spacing={3} sx={{ mb: 4 }}>\n        <Grid item xs={12} sm={6} md={3}>\n          <MetricCard\n            title=\"Metri Totali\"\n            value={data.metri_totali}\n            unit=\"m\"\n            subtitle=\"Lunghezza complessiva del progetto\"\n            gradient=\"linear-gradient(135deg, #667eea 0%, #764ba2 100%)\"\n            size=\"medium\"\n          />\n        </Grid>\n\n        <Grid item xs={12} sm={6} md={3}>\n          <MetricCard\n            title=\"Metri Posati\"\n            value={data.metri_posati}\n            unit=\"m\"\n            subtitle={`${data.percentuale_avanzamento}% completato`}\n            gradient=\"linear-gradient(135deg, #f093fb 0%, #f5576c 100%)\"\n            progress={data.percentuale_avanzamento}\n            trend={data.percentuale_avanzamento > 50 ? 'up' : data.percentuale_avanzamento > 25 ? 'flat' : 'down'}\n            trendValue={`${data.percentuale_avanzamento}%`}\n            size=\"medium\"\n          />\n        </Grid>\n\n        <Grid item xs={12} sm={6} md={3}>\n          <MetricCard\n            title=\"Metri Rimanenti\"\n            value={data.metri_da_posare}\n            unit=\"m\"\n            subtitle={`${(100 - data.percentuale_avanzamento).toFixed(1)}% da completare`}\n            gradient=\"linear-gradient(135deg, #4facfe 0%, #00f2fe 100%)\"\n            size=\"medium\"\n          />\n        </Grid>\n\n        <Grid item xs={12} sm={6} md={3}>\n          <MetricCard\n            title=\"Media/Giorno\"\n            value={data.media_giornaliera || 0}\n            unit=\"m\"\n            subtitle={\n              data.giorni_stimati\n                ? `${data.giorni_stimati} giorni lavorativi rimasti`\n                : (data.media_giornaliera > 0\n                    ? 'Calcolo in corso'\n                    : 'Nessuna posa recente')\n            }\n            gradient=\"linear-gradient(135deg, #fa709a 0%, #fee140 100%)\"\n            size=\"medium\"\n            tooltip={\n              data.giorni_lavorativi_effettivi\n                ? `Calcolata su ${data.giorni_lavorativi_effettivi} giorni di lavoro effettivo. Include solo i giorni in cui è stata effettuata posa.`\n                : 'Media giornaliera basata sui giorni di lavoro effettivo'\n            }\n          />\n        </Grid>\n      </Grid>\n\n      {/* Grafici */}\n      {showCharts && (\n        <Box sx={{ mb: 4 }}>\n          <ProgressChart data={data} />\n        </Box>\n      )}\n\n      {/* Dettagli Performance - Cards Informative */}\n      <Grid container spacing={3} sx={{ mb: 4 }}>\n        <Grid item xs={12} md={6}>\n          <Card sx={{ height: '100%', border: '1px solid #e0e0e0' }}>\n            <CardContent sx={{ p: 3 }}>\n              <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>\n                <CableIcon sx={{ color: '#3498db', mr: 1, fontSize: 28 }} />\n                <Typography variant=\"h6\" sx={{ fontWeight: 600, color: '#2c3e50' }}>\n                  Stato Cavi\n                </Typography>\n              </Box>\n              <Grid container spacing={2}>\n                <Grid item xs={6}>\n                  <Box sx={{ textAlign: 'center', p: 2, bgcolor: '#f8f9fa', borderRadius: 1 }}>\n                    <Typography variant=\"h4\" sx={{ fontWeight: 700, color: '#2c3e50', mb: 1 }}>\n                      {data.totale_cavi}\n                    </Typography>\n                    <Typography variant=\"body2\" sx={{ color: '#666' }}>\n                      Cavi Totali\n                    </Typography>\n                  </Box>\n                </Grid>\n                <Grid item xs={6}>\n                  <Box sx={{ textAlign: 'center', p: 2, bgcolor: '#e8f5e8', borderRadius: 1 }}>\n                    <Typography variant=\"h4\" sx={{ fontWeight: 700, color: '#27ae60', mb: 1 }}>\n                      {data.cavi_posati}\n                    </Typography>\n                    <Typography variant=\"body2\" sx={{ color: '#666' }}>\n                      Cavi Posati ({data.percentuale_cavi}%)\n                    </Typography>\n                  </Box>\n                </Grid>\n              </Grid>\n              <Box sx={{ mt: 2 }}>\n                <Box sx={{ display: 'flex', justifyContent: 'space-between', mb: 1 }}>\n                  <Typography variant=\"body2\">Progresso</Typography>\n                  <Typography variant=\"body2\" sx={{ fontWeight: 600 }}>\n                    {data.percentuale_cavi}%\n                  </Typography>\n                </Box>\n                <Box sx={{\n                  width: '100%',\n                  height: 8,\n                  bgcolor: '#e0e0e0',\n                  borderRadius: 4,\n                  overflow: 'hidden'\n                }}>\n                  <Box sx={{\n                    width: `${data.percentuale_cavi}%`,\n                    height: '100%',\n                    bgcolor: '#27ae60',\n                    transition: 'width 0.3s ease'\n                  }} />\n                </Box>\n              </Box>\n            </CardContent>\n          </Card>\n        </Grid>\n\n        <Grid item xs={12} md={6}>\n          <Card sx={{ height: '100%', border: '1px solid #e0e0e0' }}>\n            <CardContent sx={{ p: 3 }}>\n              <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>\n                <TimelineIcon sx={{ color: '#e74c3c', mr: 1, fontSize: 28 }} />\n                <Typography variant=\"h6\" sx={{ fontWeight: 600, color: '#2c3e50' }}>\n                  Timeline Progetto\n                </Typography>\n              </Box>\n              <Box sx={{ textAlign: 'center', mb: 2 }}>\n                <Typography variant=\"h4\" sx={{ fontWeight: 700, color: '#e74c3c', mb: 1 }}>\n                  {data.media_giornaliera || 0}m\n                </Typography>\n                <Typography variant=\"body2\" sx={{ color: '#666', mb: 1 }}>\n                  Media Giornaliera\n                </Typography>\n                {data.giorni_lavorativi_effettivi && (\n                  <Typography variant=\"caption\" sx={{ color: '#999', fontSize: '0.75rem' }}>\n                    Basata su {data.giorni_lavorativi_effettivi} giorni di lavoro effettivo\n                  </Typography>\n                )}\n              </Box>\n              {data.giorni_stimati ? (\n                <Box sx={{ textAlign: 'center', p: 2, bgcolor: '#fff3cd', borderRadius: 1 }}>\n                  <Typography variant=\"h6\" sx={{ fontWeight: 600, color: '#856404', mb: 0.5 }}>\n                    {data.data_completamento}\n                  </Typography>\n                  <Typography variant=\"body2\" sx={{ color: '#856404' }}>\n                    Completamento previsto in {data.giorni_stimati} giorni\n                  </Typography>\n                </Box>\n              ) : (\n                <Box sx={{ textAlign: 'center', p: 2, bgcolor: '#f8f9fa', borderRadius: 1 }}>\n                  <Typography variant=\"body2\" sx={{ color: '#666' }}>\n                    {data.media_giornaliera > 0 ? 'Timeline in calcolo...' : 'Necessaria attività di posa per calcolare la timeline'}\n                  </Typography>\n                </Box>\n              )}\n            </CardContent>\n          </Card>\n        </Grid>\n      </Grid>\n\n      {/* Attività Recente - Design Migliorato */}\n      {data.posa_recente && data.posa_recente.length > 0 && (\n        <Card sx={{ border: '1px solid #e0e0e0' }}>\n          <CardContent sx={{ p: 3 }}>\n            <Box sx={{ display: 'flex', alignItems: 'center', mb: 3 }}>\n              <DateRangeIcon sx={{ color: '#9b59b6', mr: 1, fontSize: 28 }} />\n              <Typography variant=\"h6\" sx={{ fontWeight: 600, color: '#2c3e50' }}>\n                📈 Attività Recente\n              </Typography>\n            </Box>\n\n            {/* Mostra solo gli ultimi 5 record in formato card per mobile-friendly */}\n            <Grid container spacing={2}>\n              {data.posa_recente.slice(0, 5).map((posa, index) => (\n                <Grid item xs={12} sm={6} md={4} key={index}>\n                  <Box sx={{\n                    p: 2,\n                    border: '1px solid #e0e0e0',\n                    borderRadius: 2,\n                    bgcolor: index === 0 ? '#f0f8ff' : '#fafafa',\n                    transition: 'all 0.2s',\n                    '&:hover': {\n                      bgcolor: '#f5f5f5',\n                      transform: 'translateY(-2px)',\n                      boxShadow: '0 4px 8px rgba(0,0,0,0.1)'\n                    }\n                  }}>\n                    <Typography variant=\"body2\" sx={{ color: '#666', mb: 1 }}>\n                      {posa.data}\n                    </Typography>\n                    <Typography variant=\"h5\" sx={{ fontWeight: 700, color: '#2c3e50' }}>\n                      {posa.metri}m\n                    </Typography>\n                    {index === 0 && (\n                      <Chip\n                        label=\"Più recente\"\n                        size=\"small\"\n                        sx={{\n                          mt: 1,\n                          bgcolor: '#3498db',\n                          color: 'white',\n                          fontSize: '0.7rem'\n                        }}\n                      />\n                    )}\n                  </Box>\n                </Grid>\n              ))}\n            </Grid>\n\n            {/* Link per vedere tutti i dati se ce ne sono di più */}\n            {data.posa_recente.length > 5 && (\n              <Box sx={{ mt: 3, textAlign: 'center' }}>\n                <Accordion>\n                  <AccordionSummary expandIcon={<ExpandMoreIcon />}>\n                    <Typography variant=\"body2\" sx={{ color: '#3498db' }}>\n                      Mostra tutti i {data.posa_recente.length} record\n                    </Typography>\n                  </AccordionSummary>\n                  <AccordionDetails>\n                    <FilterableTable\n                      data={data.posa_recente.map(posa => ({\n                        data: posa.data,\n                        metri: `${posa.metri}m`\n                      }))}\n                      columns={[\n                        { field: 'data', headerName: 'Data', width: 200 },\n                        { field: 'metri', headerName: 'Metri Posati', width: 150, align: 'right' }\n                      ]}\n                      pagination={true}\n                      pageSize={10}\n                    />\n                  </AccordionDetails>\n                </Accordion>\n              </Box>\n            )}\n          </CardContent>\n        </Card>\n      )}\n    </Box>\n  );\n\n  const renderBoqReport = (data) => (\n    <Box>\n      {/* Header migliorato */}\n      <Box sx={{\n        display: 'flex',\n        alignItems: 'center',\n        mb: 3,\n        p: 2,\n        bgcolor: '#f8f9fa',\n        borderRadius: 2,\n        border: '1px solid #e0e0e0'\n      }}>\n        <ListIcon sx={{ color: '#8e44ad', mr: 1, fontSize: 28 }} />\n        <Typography variant=\"h5\" sx={{ fontWeight: 600, color: '#2c3e50' }}>\n          📋 Bill of Quantities - Distinta Materiali\n        </Typography>\n      </Box>\n\n      {/* Grafici BOQ se disponibili */}\n      {showCharts && (\n        <Box sx={{ mb: 4 }}>\n          <BoqChart data={data} />\n        </Box>\n      )}\n\n      {/* Cavi per Tipologia - Design migliorato */}\n      <Card sx={{ mb: 3, border: '1px solid #e0e0e0' }}>\n        <CardContent sx={{ p: 3 }}>\n          <Box sx={{ display: 'flex', alignItems: 'center', mb: 3 }}>\n            <CableIcon sx={{ color: '#e67e22', mr: 1, fontSize: 24 }} />\n            <Typography variant=\"h6\" sx={{ fontWeight: 600, color: '#2c3e50' }}>\n              Cavi per Tipologia\n            </Typography>\n          </Box>\n          <FilterableTable\n            data={data.cavi_per_tipo || []}\n            columns={[\n              { field: 'tipologia', headerName: 'Tipologia', width: 150 },\n              { field: 'sezione', headerName: 'Sezione', width: 100 },\n              { field: 'num_cavi', headerName: 'Cavi', width: 80, align: 'right', dataType: 'number' },\n              { field: 'metri_teorici', headerName: 'Metri Teorici', width: 120, align: 'right', dataType: 'number',\n                renderCell: (row) => `${row.metri_teorici}m` },\n              { field: 'metri_reali', headerName: 'Metri Reali', width: 120, align: 'right', dataType: 'number',\n                renderCell: (row) => `${row.metri_reali}m` },\n              { field: 'metri_da_posare', headerName: 'Da Posare', width: 120, align: 'right', dataType: 'number',\n                renderCell: (row) => `${row.metri_da_posare}m` }\n            ]}\n            pageSize={10}\n          />\n        </CardContent>\n      </Card>\n\n      {/* Bobine Disponibili - Design migliorato */}\n      <Card sx={{ border: '1px solid #e0e0e0' }}>\n        <CardContent sx={{ p: 3 }}>\n          <Box sx={{ display: 'flex', alignItems: 'center', mb: 3 }}>\n            <InventoryIcon sx={{ color: '#16a085', mr: 1, fontSize: 24 }} />\n            <Typography variant=\"h6\" sx={{ fontWeight: 600, color: '#2c3e50' }}>\n              Bobine Disponibili\n            </Typography>\n          </Box>\n          <FilterableTable\n            data={data.bobine_per_tipo || []}\n            columns={[\n              { field: 'tipologia', headerName: 'Tipologia', width: 150 },\n              { field: 'sezione', headerName: 'Sezione', width: 100 },\n              { field: 'num_bobine', headerName: 'Bobine', width: 100, align: 'right', dataType: 'number' },\n              { field: 'metri_disponibili', headerName: 'Metri Disponibili', width: 150, align: 'right', dataType: 'number',\n                renderCell: (row) => `${row.metri_disponibili}m` }\n            ]}\n            pageSize={10}\n          />\n        </CardContent>\n      </Card>\n    </Box>\n  );\n\n\n\n\n\n  const renderPosaPeriodoReport = (data) => (\n    <Box>\n      {/* Header */}\n      <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 3 }}>\n        <Typography variant=\"h5\" sx={{ fontWeight: 600, color: 'warning.main' }}>\n          Report Posa per Periodo\n        </Typography>\n        <FormControlLabel\n          control={\n            <Switch\n              checked={showCharts}\n              onChange={(e) => setShowCharts(e.target.checked)}\n              color=\"primary\"\n            />\n          }\n          label={\n            <Box sx={{ display: 'flex', alignItems: 'center' }}>\n              <ShowChartIcon sx={{ mr: 1 }} />\n              Grafici\n            </Box>\n          }\n        />\n      </Box>\n\n      {/* Statistiche Periodo - Layout Orizzontale */}\n      <Grid container spacing={3} sx={{ mb: 4 }}>\n        <Grid item xs={12} md={3}>\n          <Paper sx={{ p: 3, textAlign: 'center', bgcolor: 'warning.main', color: 'white' }}>\n            <Typography variant=\"h4\" sx={{ fontWeight: 'bold', mb: 1 }}>\n              {data.totale_metri_periodo}m\n            </Typography>\n            <Typography variant=\"body1\">Metri Totali</Typography>\n            <Typography variant=\"caption\">{data.data_inizio} - {data.data_fine}</Typography>\n          </Paper>\n        </Grid>\n        <Grid item xs={12} md={3}>\n          <Paper sx={{ p: 3, textAlign: 'center', bgcolor: 'info.main', color: 'white' }}>\n            <Typography variant=\"h4\" sx={{ fontWeight: 'bold', mb: 1 }}>\n              {data.giorni_attivi}\n            </Typography>\n            <Typography variant=\"body1\">Giorni Attivi</Typography>\n          </Paper>\n        </Grid>\n        <Grid item xs={12} md={3}>\n          <Paper sx={{ p: 3, textAlign: 'center', bgcolor: 'success.main', color: 'white' }}>\n            <Typography variant=\"h4\" sx={{ fontWeight: 'bold', mb: 1 }}>\n              {data.media_giornaliera}m\n            </Typography>\n            <Typography variant=\"body1\">Media/Giorno</Typography>\n          </Paper>\n        </Grid>\n        <Grid item xs={12} md={3}>\n          <Paper sx={{ p: 3, textAlign: 'center', bgcolor: 'primary.main', color: 'white' }}>\n            <Typography variant=\"h4\" sx={{ fontWeight: 'bold', mb: 1 }}>\n              {Math.round(data.totale_metri_periodo / data.giorni_attivi * 7)}m\n            </Typography>\n            <Typography variant=\"body1\">Media/Settimana</Typography>\n          </Paper>\n        </Grid>\n      </Grid>\n\n      {/* Grafici */}\n      {showCharts && (\n        <Box sx={{ mb: 4 }}>\n          <TimelineChart data={data} />\n        </Box>\n      )}\n\n      {/* Posa Giornaliera */}\n      <Paper sx={{ p: 3 }}>\n        <Typography variant=\"h6\" sx={{ mb: 2, fontWeight: 600 }}>\n          Dettaglio Posa Giornaliera\n        </Typography>\n        <FilterableTable\n          data={data.posa_giornaliera || []}\n          columns={[\n            { field: 'data', headerName: 'Data', width: 200 },\n            { field: 'metri', headerName: 'Metri Posati', width: 150, align: 'right', dataType: 'number',\n              renderCell: (row) => `${row.metri}m` }\n          ]}\n          pageSize={10}\n        />\n      </Paper>\n    </Box>\n  );\n\n\n\n  const renderDialog = () => (\n    <Dialog open={openDialog} onClose={handleCloseDialog} maxWidth=\"sm\" fullWidth>\n      <DialogTitle>\n        {selectedReport?.title}\n      </DialogTitle>\n      <DialogContent>\n        {error && (\n          <Alert severity=\"error\" sx={{ mb: 2 }}>\n            {error}\n          </Alert>\n        )}\n\n        <Grid container spacing={2} sx={{ mt: 1 }}>\n          <Grid item xs={12}>\n            <FormControl fullWidth>\n              <InputLabel>Formato</InputLabel>\n              <Select\n                value={formData.formato}\n                label=\"Formato\"\n                onChange={(e) => setFormData({ ...formData, formato: e.target.value })}\n              >\n                <MenuItem value=\"video\">Visualizza a schermo</MenuItem>\n                <MenuItem value=\"pdf\">Download PDF</MenuItem>\n                <MenuItem value=\"excel\">Download Excel</MenuItem>\n              </Select>\n            </FormControl>\n          </Grid>\n\n          {dialogType === 'bobina-specifica' && (\n            <Grid item xs={12}>\n              <TextField\n                fullWidth\n                label=\"ID Bobina\"\n                value={formData.id_bobina}\n                onChange={(e) => setFormData({ ...formData, id_bobina: e.target.value })}\n                placeholder=\"Es: 1, 2, A, B...\"\n                helperText=\"Inserisci solo la parte finale dell'ID (es: 1 per C1_B1)\"\n              />\n            </Grid>\n          )}\n\n          {dialogType === 'posa-periodo' && (\n            <>\n              <Grid item xs={6}>\n                <TextField\n                  fullWidth\n                  type=\"date\"\n                  label=\"Data Inizio\"\n                  value={formData.data_inizio}\n                  onChange={(e) => setFormData({ ...formData, data_inizio: e.target.value })}\n                  InputLabelProps={{ shrink: true }}\n                />\n              </Grid>\n              <Grid item xs={6}>\n                <TextField\n                  fullWidth\n                  type=\"date\"\n                  label=\"Data Fine\"\n                  value={formData.data_fine}\n                  onChange={(e) => setFormData({ ...formData, data_fine: e.target.value })}\n                  InputLabelProps={{ shrink: true }}\n                />\n              </Grid>\n            </>\n          )}\n        </Grid>\n      </DialogContent>\n      <DialogActions>\n        <Button onClick={handleCloseDialog}>Annulla</Button>\n        <Button\n          onClick={handleGenerateReport}\n          variant=\"contained\"\n          disabled={loading}\n          startIcon={loading ? <CircularProgress size={20} /> : <VisibilityIcon />}\n        >\n          {loading ? 'Generazione...' : 'Genera Report'}\n        </Button>\n      </DialogActions>\n    </Dialog>\n  );\n\n  return (\n    <Box className=\"report-main-container report-fade-in\">\n      {/* Header */}\n      <Box sx={{ display: 'flex', justifyContent: 'flex-end', alignItems: 'center', mb: 2 }}>\n        <AdminHomeButton />\n      </Box>\n\n      {/* Loading indicator */}\n      {loading && (\n        <Box sx={{ display: 'flex', justifyContent: 'center', my: 4 }}>\n          <CircularProgress />\n        </Box>\n      )}\n\n      {/* Reports Navigation */}\n      <Box sx={{ mt: 3 }}>\n        {/* Report Navigation - Design Compatto */}\n        <Box sx={{ mb: 3 }}>\n          <Typography variant=\"h6\" sx={{ fontWeight: 600, color: '#2c3e50', mb: 2, textAlign: 'center' }}>\n            🎯 Seleziona il tipo di report\n          </Typography>\n          <Grid container spacing={2}>\n            {/* Report Avanzamento */}\n            <Grid item xs={6} sm={4} md={3}>\n              <Card\n                className={`report-card ${selectedReportType === 'progress' ? 'report-card-selected' : ''}`}\n                sx={{\n                  height: '140px',\n                  cursor: 'pointer',\n                  border: selectedReportType === 'progress' ? '2px solid #3498db' : '1px solid #e0e0e0',\n                  bgcolor: selectedReportType === 'progress' ? '#f0f8ff' : 'white',\n                  transition: 'all 0.2s'\n                }}\n                onClick={() => setSelectedReportType('progress')}\n              >\n                <CardContent sx={{ p: 2, textAlign: 'center', height: '100%', display: 'flex', flexDirection: 'column', justifyContent: 'center' }}>\n                  <AssessmentIcon sx={{ fontSize: 32, color: '#3498db', mb: 1 }} />\n                  <Typography variant=\"subtitle1\" sx={{ fontWeight: 600, mb: 0.5, fontSize: '1.1rem' }}>\n                    Avanzamento\n                  </Typography>\n                  <Typography variant=\"body2\" sx={{ color: '#666', fontSize: '0.9rem' }}>\n                    Panoramica lavori\n                  </Typography>\n                </CardContent>\n              </Card>\n            </Grid>\n\n            {/* Bill of Quantities */}\n            <Grid item xs={6} sm={4} md={3}>\n              <Card\n                sx={{\n                  height: '140px',\n                  cursor: 'pointer',\n                  border: selectedReportType === 'boq' ? '2px solid #8e44ad' : '1px solid #e0e0e0',\n                  bgcolor: selectedReportType === 'boq' ? '#f8f4ff' : 'white',\n                  transition: 'all 0.2s'\n                }}\n                onClick={() => setSelectedReportType('boq')}\n              >\n                <CardContent sx={{ p: 2, textAlign: 'center', height: '100%', display: 'flex', flexDirection: 'column', justifyContent: 'center' }}>\n                  <ListIcon sx={{ fontSize: 32, color: '#8e44ad', mb: 1 }} />\n                  <Typography variant=\"subtitle1\" sx={{ fontWeight: 600, mb: 0.5, fontSize: '1.1rem' }}>\n                    Bill of Quantities\n                  </Typography>\n                  <Typography variant=\"body2\" sx={{ color: '#666', fontSize: '0.9rem' }}>\n                    Distinta materiali\n                  </Typography>\n                </CardContent>\n              </Card>\n            </Grid>\n\n\n\n            {/* Posa per Periodo */}\n            <Grid item xs={6} sm={4} md={3}>\n              <Card\n                sx={{\n                  height: '140px',\n                  cursor: 'pointer',\n                  border: selectedReportType === 'posa-periodo' ? '2px solid #9b59b6' : '1px solid #e0e0e0',\n                  bgcolor: selectedReportType === 'posa-periodo' ? '#f8f4ff' : 'white',\n                  transition: 'all 0.2s'\n                }}\n                onClick={() => setSelectedReportType('posa-periodo')}\n              >\n                <CardContent sx={{ p: 2, textAlign: 'center', height: '100%', display: 'flex', flexDirection: 'column', justifyContent: 'center' }}>\n                  <TimelineIcon sx={{ fontSize: 32, color: '#9b59b6', mb: 1 }} />\n                  <Typography variant=\"subtitle1\" sx={{ fontWeight: 600, mb: 0.5, fontSize: '1.1rem' }}>\n                    Posa per Periodo\n                  </Typography>\n                  <Typography variant=\"body2\" sx={{ color: '#666', fontSize: '0.9rem' }}>\n                    Analisi temporale\n                  </Typography>\n                </CardContent>\n              </Card>\n            </Grid>\n          </Grid>\n        </Box>\n\n        {/* Report Content */}\n        <Box sx={{ minHeight: '400px' }}>\n          {/* Progress Report */}\n          {selectedReportType === 'progress' && (\n            <Paper sx={{ p: 3 }}>\n              {reportsData.progress ? (\n                <Box>\n                  <Box sx={{ display: 'flex', justifyContent: 'flex-end', mb: 2 }}>\n                    <Button\n                      startIcon={<DownloadIcon />}\n                      onClick={() => generateReportWithFormat('progress', 'pdf')}\n                      variant=\"outlined\"\n                      size=\"small\"\n                      color=\"primary\"\n                      sx={{ mr: 1 }}\n                    >\n                      PDF\n                    </Button>\n                    <Button\n                      startIcon={<DownloadIcon />}\n                      onClick={() => generateReportWithFormat('progress', 'excel')}\n                      variant=\"outlined\"\n                      size=\"small\"\n                      color=\"success\"\n                    >\n                      Excel\n                    </Button>\n                  </Box>\n                  {renderProgressReport(reportsData.progress)}\n                </Box>\n              ) : loading ? (\n                <EmptyState\n                  type=\"loading\"\n                  reportType=\"progress\"\n                  title=\"Caricamento Report Avanzamento...\"\n                  description=\"Stiamo elaborando i dati dell'avanzamento dei lavori\"\n                />\n              ) : (\n                <EmptyState\n                  type=\"error\"\n                  reportType=\"progress\"\n                  title=\"Errore nel caricamento\"\n                  description=\"Impossibile caricare il report di avanzamento. Verifica la connessione e riprova.\"\n                  onRetry={() => {\n                    setLoading(true);\n                    reportService.getProgressReport(cantiereId, 'video')\n                      .then(data => {\n                        setReportsData(prev => ({\n                          ...prev,\n                          progress: data.content\n                        }));\n                      })\n                      .catch(err => {\n                        console.error('Error retrying progress report:', err);\n                      })\n                      .finally(() => {\n                        setLoading(false);\n                      });\n                  }}\n                  loading={loading}\n                />\n              )}\n            </Paper>\n          )}\n\n          {/* Bill of Quantities */}\n          {selectedReportType === 'boq' && (\n            <Paper sx={{ p: 3 }}>\n              {reportsData.boq ? (\n                <Box>\n                  <Box sx={{ display: 'flex', justifyContent: 'flex-end', mb: 2 }}>\n                    <Button\n                      startIcon={<DownloadIcon />}\n                      onClick={() => generateReportWithFormat('boq', 'pdf')}\n                      variant=\"outlined\"\n                      size=\"small\"\n                      color=\"primary\"\n                      sx={{ mr: 1 }}\n                    >\n                      PDF\n                    </Button>\n                    <Button\n                      startIcon={<DownloadIcon />}\n                      onClick={() => generateReportWithFormat('boq', 'excel')}\n                      variant=\"outlined\"\n                      size=\"small\"\n                      color=\"success\"\n                    >\n                      Excel\n                    </Button>\n                  </Box>\n                  {renderBoqReport(reportsData.boq)}\n                </Box>\n              ) : loading ? (\n                <EmptyState\n                  type=\"loading\"\n                  reportType=\"boq\"\n                  title=\"Caricamento Bill of Quantities...\"\n                  description=\"Stiamo elaborando la distinta materiali\"\n                />\n              ) : (\n                <EmptyState\n                  type=\"error\"\n                  reportType=\"boq\"\n                  title=\"Errore nel caricamento\"\n                  description=\"Impossibile caricare la distinta materiali. Verifica la connessione e riprova.\"\n                  onRetry={() => {\n                    setLoading(true);\n                    reportService.getBillOfQuantities(cantiereId, 'video')\n                      .then(data => {\n                        setReportsData(prev => ({\n                          ...prev,\n                          boq: data.content\n                        }));\n                      })\n                      .catch(err => {\n                        console.error('Error retrying BOQ report:', err);\n                      })\n                      .finally(() => {\n                        setLoading(false);\n                      });\n                  }}\n                  loading={loading}\n                />\n              )}\n            </Paper>\n          )}\n\n\n\n          {/* Posa per Periodo Report */}\n          {selectedReportType === 'posa-periodo' && (\n            <Paper sx={{ p: 3 }}>\n              {reportsData.posaPeriodo ? (\n                <Box>\n                  <Box sx={{ display: 'flex', justifyContent: 'flex-end', mb: 2 }}>\n                    <Button\n                      startIcon={<DownloadIcon />}\n                      onClick={() => generateReportWithFormat('posa-periodo', 'pdf')}\n                      variant=\"outlined\"\n                      size=\"small\"\n                      color=\"primary\"\n                      sx={{ mr: 1 }}\n                    >\n                      PDF\n                    </Button>\n                    <Button\n                      startIcon={<DownloadIcon />}\n                      onClick={() => generateReportWithFormat('posa-periodo', 'excel')}\n                      variant=\"outlined\"\n                      size=\"small\"\n                      color=\"success\"\n                    >\n                      Excel\n                    </Button>\n                  </Box>\n                  {renderPosaPeriodoReport(reportsData.posaPeriodo)}\n                </Box>\n              ) : (\n                <EmptyState\n                  type=\"action-required\"\n                  reportType=\"posa-periodo\"\n                  title=\"Seleziona un Periodo\"\n                  description=\"Scegli un intervallo di date per analizzare i trend temporali, pattern di lavoro e produttività del team.\"\n                  actionLabel=\"Seleziona Periodo\"\n                  onAction={() => {\n                    setDialogType('posa-periodo');\n                    // Set default date range (last month to today)\n                    const today = new Date();\n                    const lastMonth = new Date();\n                    lastMonth.setMonth(today.getMonth() - 1);\n\n                    setFormData({\n                      ...formData,\n                      data_inizio: lastMonth.toISOString().split('T')[0],\n                      data_fine: today.toISOString().split('T')[0]\n                    });\n                    setOpenDialog(true);\n                  }}\n                />\n              )}\n            </Paper>\n          )}\n        </Box>\n      </Box>\n\n      {/* Dialog per configurazione report */}\n      {renderDialog()}\n    </Box>\n  );\n};\n\nexport default ReportCaviPageNew;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAClD,OAAO,0BAA0B;AACjC,SACEC,GAAG,EACHC,UAAU,EACVC,KAAK,EACLC,IAAI,EACJC,IAAI,EACJC,WAAW,EACXC,WAAW,EACXC,MAAM,EACNC,IAAI,EACJC,KAAK,EACLC,gBAAgB,EAChBC,OAAO,EACPC,UAAU,EACVC,OAAO,EACPC,MAAM,EACNC,WAAW,EACXC,aAAa,EACbC,aAAa,EACbC,WAAW,EACXC,UAAU,EACVC,MAAM,EACNC,QAAQ,EACRC,SAAS,EACTC,SAAS,EACTC,gBAAgB,EAChBC,gBAAgB,EAChBC,MAAM,EACNC,gBAAgB,QACX,eAAe;AACtB,SACEC,UAAU,IAAIC,cAAc,EAC5BC,QAAQ,IAAIC,YAAY,EACxBC,QAAQ,IAAIC,YAAY,EACxBC,QAAQ,IAAIC,YAAY,EACxBC,IAAI,IAAIC,QAAQ,EAChBC,QAAQ,IAAIC,YAAY,EACxBC,UAAU,IAAIC,cAAc,EAC5BC,OAAO,IAAIC,WAAW,EACtBC,SAAS,IAAIC,aAAa,EAC1BC,SAAS,IAAIC,aAAa,EAC1BC,KAAK,IAAIC,SAAS,EAClBC,SAAS,IAAIC,aAAa,EAC1BC,UAAU,IAAIC,cAAc,EAC5BC,SAAS,IAAIC,aAAa,QACrB,qBAAqB;AAC5B,SAASC,WAAW,EAAEC,SAAS,QAAQ,kBAAkB;AACzD,SAASC,OAAO,QAAQ,2BAA2B;AACnD,OAAOC,eAAe,MAAM,yCAAyC;AACrE,OAAOC,aAAa,MAAM,8BAA8B;AACxD,OAAOC,eAAe,MAAM,yCAAyC;AACrE,OAAOC,UAAU,MAAM,oCAAoC;AAC3D,OAAOC,UAAU,MAAM,oCAAoC;AAC3D,OAAOC,aAAa,MAAM,uCAAuC;;AAEjE;AACA,OAAOC,aAAa,MAAM,uCAAuC;AACjE,OAAOC,WAAW,MAAM,qCAAqC;AAC7D,OAAOC,QAAQ,MAAM,kCAAkC;AACvD,OAAOC,aAAa,MAAM,uCAAuC;AACjE,OAAOC,cAAc,MAAM,wCAAwC;AAAC,SAAAC,MAAA,IAAAC,OAAA,EAAAC,QAAA,IAAAC,SAAA;AAEpE,MAAMC,iBAAiB,GAAGA,CAAA,KAAM;EAAAC,EAAA;EAC9B,MAAMC,QAAQ,GAAGpB,WAAW,CAAC,CAAC;EAC9B,MAAM;IAAEqB;EAAW,CAAC,GAAGpB,SAAS,CAAC,CAAC;EAClC,MAAM;IAAEqB;EAAK,CAAC,GAAGpB,OAAO,CAAC,CAAC;EAE1B,MAAM,CAACqB,OAAO,EAAEC,UAAU,CAAC,GAAGlF,QAAQ,CAAC,KAAK,CAAC;EAC7C,MAAM,CAACmF,KAAK,EAAEC,QAAQ,CAAC,GAAGpF,QAAQ,CAAC,IAAI,CAAC;EACxC,MAAM,CAACqF,UAAU,EAAEC,aAAa,CAAC,GAAGtF,QAAQ,CAAC,IAAI,CAAC;EAClD,MAAM,CAACuF,cAAc,EAAEC,iBAAiB,CAAC,GAAGxF,QAAQ,CAAC,IAAI,CAAC;EAC1D,MAAM,CAACyF,UAAU,EAAEC,aAAa,CAAC,GAAG1F,QAAQ,CAAC,KAAK,CAAC;EACnD,MAAM,CAAC2F,UAAU,EAAEC,aAAa,CAAC,GAAG5F,QAAQ,CAAC,EAAE,CAAC;EAChD,MAAM,CAAC6F,kBAAkB,EAAEC,qBAAqB,CAAC,GAAG9F,QAAQ,CAAC,UAAU,CAAC;EACxE,MAAM,CAAC+F,QAAQ,EAAEC,WAAW,CAAC,GAAGhG,QAAQ,CAAC;IACvCiG,OAAO,EAAE,OAAO;IAChBC,WAAW,EAAE,EAAE;IACfC,SAAS,EAAE,EAAE;IACbC,SAAS,EAAE;EACb,CAAC,CAAC;;EAEF;EACA,MAAM,CAACC,WAAW,EAAEC,cAAc,CAAC,GAAGtG,QAAQ,CAAC;IAC7CuG,QAAQ,EAAE,IAAI;IACdC,GAAG,EAAE,IAAI;IACTC,MAAM,EAAE,IAAI;IACZC,SAAS,EAAE,IAAI;IACfC,eAAe,EAAE,IAAI;IACrBC,WAAW,EAAE;EACf,CAAC,CAAC;;EAEF;EACA,MAAM,CAACC,UAAU,EAAEC,aAAa,CAAC,GAAG9G,QAAQ,CAAC,IAAI,CAAC;;EAElD;EACAC,SAAS,CAAC,MAAM;IACd,MAAM8G,cAAc,GAAG,MAAAA,CAAA,KAAY;MACjC7B,UAAU,CAAC,IAAI,CAAC;MAChB,IAAI;QACF;QACA,MAAM8B,eAAe,GAAGlD,aAAa,CAACmD,iBAAiB,CAAClC,UAAU,EAAE,OAAO,CAAC,CACzEmC,KAAK,CAACC,GAAG,IAAI;UACZC,OAAO,CAACjC,KAAK,CAAC,gCAAgC,EAAEgC,GAAG,CAAC;UACpD,OAAO;YAAEE,OAAO,EAAE;UAAK,CAAC;QAC1B,CAAC,CAAC;QAEJ,MAAMC,UAAU,GAAGxD,aAAa,CAACyD,mBAAmB,CAACxC,UAAU,EAAE,OAAO,CAAC,CACtEmC,KAAK,CAACC,GAAG,IAAI;UACZC,OAAO,CAACjC,KAAK,CAAC,2BAA2B,EAAEgC,GAAG,CAAC;UAC/C,OAAO;YAAEE,OAAO,EAAE;UAAK,CAAC;QAC1B,CAAC,CAAC;QAEJ,MAAMG,aAAa,GAAG1D,aAAa,CAAC2D,eAAe,CAAC1C,UAAU,EAAE,OAAO,CAAC,CACrEmC,KAAK,CAACC,GAAG,IAAI;UACZC,OAAO,CAACjC,KAAK,CAAC,8BAA8B,EAAEgC,GAAG,CAAC;UAClD,OAAO;YAAEE,OAAO,EAAE;UAAK,CAAC;QAC1B,CAAC,CAAC;QAEJ,MAAMK,gBAAgB,GAAG5D,aAAa,CAAC6D,kBAAkB,CAAC5C,UAAU,EAAE,OAAO,CAAC,CAC3EmC,KAAK,CAACC,GAAG,IAAI;UACZC,OAAO,CAACjC,KAAK,CAAC,kCAAkC,EAAEgC,GAAG,CAAC;UACtD,OAAO;YAAEE,OAAO,EAAE;UAAK,CAAC;QAC1B,CAAC,CAAC;;QAEJ;QACA,MAAM,CAACO,YAAY,EAAEC,OAAO,EAAEC,UAAU,EAAEC,aAAa,CAAC,GAAG,MAAMC,OAAO,CAACC,GAAG,CAAC,CAC3EjB,eAAe,EACfM,UAAU,EACVE,aAAa,EACbE,gBAAgB,CACjB,CAAC;;QAEF;QACApB,cAAc,CAAC;UACbC,QAAQ,EAAEqB,YAAY,CAACP,OAAO;UAC9Bb,GAAG,EAAEqB,OAAO,CAACR,OAAO;UACpBZ,MAAM,EAAEqB,UAAU,CAACT,OAAO;UAC1BX,SAAS,EAAEqB,aAAa,CAACV,OAAO;UAChCV,eAAe,EAAE,IAAI;UACrBC,WAAW,EAAE;QACf,CAAC,CAAC;;QAEF;QACA,IAAIgB,YAAY,CAACP,OAAO,IAAIQ,OAAO,CAACR,OAAO,IAAIS,UAAU,CAACT,OAAO,IAAIU,aAAa,CAACV,OAAO,EAAE;UAC1FjC,QAAQ,CAAC,IAAI,CAAC;QAChB,CAAC,MAAM;UACLA,QAAQ,CAAC,uDAAuD,CAAC;QACnE;MACF,CAAC,CAAC,OAAO+B,GAAG,EAAE;QACZ;QACAC,OAAO,CAACjC,KAAK,CAAC,mCAAmC,EAAEgC,GAAG,CAAC;QACvD/B,QAAQ,CAAC,uDAAuD,CAAC;MACnE,CAAC,SAAS;QACRF,UAAU,CAAC,KAAK,CAAC;MACnB;IACF,CAAC;IAED,IAAIH,UAAU,EAAE;MACdgC,cAAc,CAAC,CAAC;IAClB;EACF,CAAC,EAAE,CAAChC,UAAU,CAAC,CAAC;;EAEhB;EACA,MAAMmD,WAAW,GAAG,CAClB;IACEC,EAAE,EAAE,UAAU;IACdC,KAAK,EAAE,oBAAoB;IAC3BC,WAAW,EAAE,2FAA2F;IACxGC,IAAI,eAAE7D,OAAA,CAAC1C,cAAc;MAAAwG,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC;IACxBC,KAAK,EAAE,SAAS;IAChBC,QAAQ,EAAE,CAAC,yBAAyB,EAAE,2BAA2B,EAAE,qBAAqB,EAAE,yBAAyB;EACrH,CAAC,EACD;IACET,EAAE,EAAE,KAAK;IACTC,KAAK,EAAE,oBAAoB;IAC3BC,WAAW,EAAE,wEAAwE;IACrFC,IAAI,eAAE7D,OAAA,CAAClC,QAAQ;MAAAgG,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC;IAClBC,KAAK,EAAE,WAAW;IAClBC,QAAQ,EAAE,CAAC,yBAAyB,EAAE,0BAA0B,EAAE,qBAAqB,EAAE,eAAe;EAC1G,CAAC,EAED;IACET,EAAE,EAAE,cAAc;IAClBC,KAAK,EAAE,yBAAyB;IAChCC,WAAW,EAAE,4DAA4D;IACzEC,IAAI,eAAE7D,OAAA,CAACpC,YAAY;MAAAkG,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC;IACtBC,KAAK,EAAE,SAAS;IAChBC,QAAQ,EAAE,CAAC,iBAAiB,EAAE,wBAAwB,EAAE,oBAAoB,EAAE,mBAAmB;EACnG,CAAC,CAEF;;EAED;EACA,MAAMC,wBAAwB,GAAG,MAAAA,CAAOC,UAAU,EAAEC,MAAM,KAAK;IAC7D,IAAI;MACF7D,UAAU,CAAC,IAAI,CAAC;MAChBE,QAAQ,CAAC,IAAI,CAAC;MAEd,IAAI4D,QAAQ;MAEZ,QAAQF,UAAU;QAChB,KAAK,UAAU;UACbE,QAAQ,GAAG,MAAMlF,aAAa,CAACmD,iBAAiB,CAAClC,UAAU,EAAEgE,MAAM,CAAC;UACpE;QACF,KAAK,KAAK;UACRC,QAAQ,GAAG,MAAMlF,aAAa,CAACyD,mBAAmB,CAACxC,UAAU,EAAEgE,MAAM,CAAC;UACtE;QAEF,KAAK,cAAc;UACjB,IAAI,CAAChD,QAAQ,CAACG,WAAW,IAAI,CAACH,QAAQ,CAACI,SAAS,EAAE;YAChDf,QAAQ,CAAC,4CAA4C,CAAC;YACtD;UACF;UACA4D,QAAQ,GAAG,MAAMlF,aAAa,CAACmF,uBAAuB,CACpDlE,UAAU,EACVgB,QAAQ,CAACG,WAAW,EACpBH,QAAQ,CAACI,SAAS,EAClB4C,MACF,CAAC;UACD;QACF;UACE,MAAM,IAAIG,KAAK,CAAC,iCAAiC,CAAC;MACtD;MAEA,IAAIH,MAAM,KAAK,OAAO,EAAE;QACtB;QACA,IAAID,UAAU,KAAK,kBAAkB,IAAIA,UAAU,KAAK,cAAc,EAAE;UACtExC,cAAc,CAAC6C,IAAI,KAAK;YACtB,GAAGA,IAAI;YACP,CAACL,UAAU,KAAK,kBAAkB,GAAG,iBAAiB,GAAG,aAAa,GAAGE,QAAQ,CAAC3B;UACpF,CAAC,CAAC,CAAC;QACL;QACA/B,aAAa,CAAC0D,QAAQ,CAAC3B,OAAO,CAAC;MACjC,CAAC,MAAM;QACL;QACA,IAAI2B,QAAQ,CAACI,QAAQ,EAAE;UACrBC,MAAM,CAACC,IAAI,CAACN,QAAQ,CAACI,QAAQ,EAAE,QAAQ,CAAC;QAC1C;MACF;IACF,CAAC,CAAC,OAAOjC,GAAG,EAAE;MACZC,OAAO,CAACjC,KAAK,CAAC,sCAAsC,EAAEgC,GAAG,CAAC;MAC1D/B,QAAQ,CAAC+B,GAAG,CAACoC,MAAM,IAAIpC,GAAG,CAACqC,OAAO,IAAI,0CAA0C,CAAC;IACnF,CAAC,SAAS;MACRtE,UAAU,CAAC,KAAK,CAAC;IACnB;EACF,CAAC;EAED,MAAMuE,kBAAkB,GAAIX,UAAU,IAAK;IACzCtD,iBAAiB,CAACsD,UAAU,CAAC;IAC7BlD,aAAa,CAACkD,UAAU,CAACX,EAAE,CAAC;;IAE5B;IACA,IAAIW,UAAU,CAACX,EAAE,KAAK,cAAc,IAAIW,UAAU,CAACX,EAAE,KAAK,kBAAkB,EAAE;MAC5E;MACA,IAAIW,UAAU,CAACX,EAAE,KAAK,cAAc,EAAE;QACpC,MAAMuB,KAAK,GAAG,IAAIC,IAAI,CAAC,CAAC;QACxB,MAAMC,SAAS,GAAG,IAAID,IAAI,CAAC,CAAC;QAC5BC,SAAS,CAACC,QAAQ,CAACH,KAAK,CAACI,QAAQ,CAAC,CAAC,GAAG,CAAC,CAAC;QAExC9D,WAAW,CAAC;UACV,GAAGD,QAAQ;UACXG,WAAW,EAAE0D,SAAS,CAACG,WAAW,CAAC,CAAC,CAACC,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC;UAClD7D,SAAS,EAAEuD,KAAK,CAACK,WAAW,CAAC,CAAC,CAACC,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC;QAC7C,CAAC,CAAC;MACJ;MAEAtE,aAAa,CAAC,IAAI,CAAC;IACrB,CAAC,MAAM;MACL;MACAmD,wBAAwB,CAACC,UAAU,CAACX,EAAE,EAAE,OAAO,CAAC;IAClD;EACF,CAAC;EAED,MAAM8B,oBAAoB,GAAG,MAAAA,CAAA,KAAY;IACvC,MAAMpB,wBAAwB,CAAClD,UAAU,EAAEI,QAAQ,CAACE,OAAO,CAAC;IAC5DP,aAAa,CAAC,KAAK,CAAC;EACtB,CAAC;EAED,MAAMwE,iBAAiB,GAAGA,CAAA,KAAM;IAC9BxE,aAAa,CAAC,KAAK,CAAC;IACpBN,QAAQ,CAAC,IAAI,CAAC;IACdY,WAAW,CAAC;MACVC,OAAO,EAAE,OAAO;MAChBC,WAAW,EAAE,EAAE;MACfC,SAAS,EAAE,EAAE;MACbC,SAAS,EAAE;IACb,CAAC,CAAC;EACJ,CAAC;EAED,MAAM+D,mBAAmB,GAAGA,CAAA,KAAM;IAChC,IAAI,CAAC9E,UAAU,EAAE,OAAO,IAAI;IAE5B,oBACEZ,OAAA,CAACrE,KAAK;MAACgK,EAAE,EAAE;QAAEC,CAAC,EAAE,CAAC;QAAEC,EAAE,EAAE;MAAE,CAAE;MAAAC,QAAA,gBACzB9F,OAAA,CAACvE,GAAG;QAACkK,EAAE,EAAE;UAAEI,OAAO,EAAE,MAAM;UAAEC,cAAc,EAAE,eAAe;UAAEC,UAAU,EAAE,QAAQ;UAAEC,EAAE,EAAE;QAAE,CAAE;QAAAJ,QAAA,gBACzF9F,OAAA,CAACtE,UAAU;UAACyK,OAAO,EAAC,IAAI;UAAAL,QAAA,GACrBhF,cAAc,aAAdA,cAAc,uBAAdA,cAAc,CAAE6C,KAAK,EAAC,KAAG,EAAC/C,UAAU,CAACwF,aAAa;QAAA;UAAAtC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACzC,CAAC,eACbjE,OAAA,CAACvE,GAAG;UAACkK,EAAE,EAAE;YAAEI,OAAO,EAAE,MAAM;YAAEM,GAAG,EAAE;UAAE,CAAE;UAAAP,QAAA,gBAEnC9F,OAAA,CAAChE,MAAM;YACLsK,SAAS,eAAEtG,OAAA,CAAChC,YAAY;cAAA8F,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAE;YAC5BsC,OAAO,EAAEA,CAAA,KAAMnC,wBAAwB,CAAClD,UAAU,EAAE,KAAK,CAAE;YAC3DiF,OAAO,EAAC,UAAU;YAClBK,IAAI,EAAC,OAAO;YACZtC,KAAK,EAAC,SAAS;YAAA4B,QAAA,EAChB;UAED;YAAAhC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC,eACTjE,OAAA,CAAChE,MAAM;YACLsK,SAAS,eAAEtG,OAAA,CAAChC,YAAY;cAAA8F,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAE;YAC5BsC,OAAO,EAAEA,CAAA,KAAMnC,wBAAwB,CAAClD,UAAU,EAAE,OAAO,CAAE;YAC7DiF,OAAO,EAAC,UAAU;YAClBK,IAAI,EAAC,OAAO;YACZtC,KAAK,EAAC,SAAS;YAAA4B,QAAA,EAChB;UAED;YAAAhC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC,eACTjE,OAAA,CAAChE,MAAM;YACLsK,SAAS,eAAEtG,OAAA,CAAC5B,WAAW;cAAA0F,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAE;YAC3BsC,OAAO,EAAEA,CAAA,KAAM1F,aAAa,CAAC,IAAI,CAAE;YACnCsF,OAAO,EAAC,UAAU;YAClBK,IAAI,EAAC,OAAO;YAAAV,QAAA,EACb;UAED;YAAAhC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACN,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,eAENjE,OAAA,CAAC5D,OAAO;QAACuJ,EAAE,EAAE;UAAEO,EAAE,EAAE;QAAE;MAAE;QAAApC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC,EAGzB/C,UAAU,KAAK,UAAU,IAAIuF,oBAAoB,CAAC7F,UAAU,CAAC,EAC7DM,UAAU,KAAK,KAAK,IAAIwF,eAAe,CAAC9F,UAAU,CAAC,EACnDM,UAAU,KAAK,cAAc,IAAIyF,uBAAuB,CAAC/F,UAAU,CAAC;IAAA;MAAAkD,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAChE,CAAC;EAEZ,CAAC;EAED,MAAMwC,oBAAoB,GAAIG,IAAI,iBAChC5G,OAAA,CAACvE,GAAG;IAAAqK,QAAA,gBAEF9F,OAAA,CAACvE,GAAG;MAACkK,EAAE,EAAE;QACPI,OAAO,EAAE,MAAM;QACfC,cAAc,EAAE,eAAe;QAC/BC,UAAU,EAAE,QAAQ;QACpBC,EAAE,EAAE,CAAC;QACLN,CAAC,EAAE,CAAC;QACJiB,OAAO,EAAE,SAAS;QAClBC,YAAY,EAAE,CAAC;QACfC,MAAM,EAAE;MACV,CAAE;MAAAjB,QAAA,gBACA9F,OAAA,CAACtE,UAAU;QAACyK,OAAO,EAAC,IAAI;QAACR,EAAE,EAAE;UAAEqB,UAAU,EAAE,GAAG;UAAE9C,KAAK,EAAE;QAAU,CAAE;QAAA4B,QAAA,EAAC;MAEpE;QAAAhC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAY,CAAC,eACbjE,OAAA,CAAC5C,gBAAgB;QACf6J,OAAO,eACLjH,OAAA,CAAC7C,MAAM;UACL+J,OAAO,EAAE9E,UAAW;UACpB+E,QAAQ,EAAGC,CAAC,IAAK/E,aAAa,CAAC+E,CAAC,CAACC,MAAM,CAACH,OAAO,CAAE;UACjDhD,KAAK,EAAC;QAAS;UAAAJ,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAChB,CACF;QACDqD,KAAK,eACHtH,OAAA,CAACvE,GAAG;UAACkK,EAAE,EAAE;YAAEI,OAAO,EAAE,MAAM;YAAEE,UAAU,EAAE;UAAS,CAAE;UAAAH,QAAA,gBACjD9F,OAAA,CAAChB,aAAa;YAAC2G,EAAE,EAAE;cAAE4B,EAAE,EAAE;YAAE;UAAE;YAAAzD,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,WAElC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAK;MACN;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACF,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACC,CAAC,eAGNjE,OAAA,CAACpE,IAAI;MAAC4L,SAAS;MAACC,OAAO,EAAE,CAAE;MAAC9B,EAAE,EAAE;QAAEO,EAAE,EAAE;MAAE,CAAE;MAAAJ,QAAA,gBACxC9F,OAAA,CAACpE,IAAI;QAAC8L,IAAI;QAACC,EAAE,EAAE,EAAG;QAACC,EAAE,EAAE,CAAE;QAACC,EAAE,EAAE,CAAE;QAAA/B,QAAA,eAC9B9F,OAAA,CAACR,UAAU;UACTmE,KAAK,EAAC,cAAc;UACpBmE,KAAK,EAAElB,IAAI,CAACmB,YAAa;UACzBC,IAAI,EAAC,GAAG;UACRC,QAAQ,EAAC,oCAAoC;UAC7CC,QAAQ,EAAC,mDAAmD;UAC5D1B,IAAI,EAAC;QAAQ;UAAA1C,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACd;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACE,CAAC,eAEPjE,OAAA,CAACpE,IAAI;QAAC8L,IAAI;QAACC,EAAE,EAAE,EAAG;QAACC,EAAE,EAAE,CAAE;QAACC,EAAE,EAAE,CAAE;QAAA/B,QAAA,eAC9B9F,OAAA,CAACR,UAAU;UACTmE,KAAK,EAAC,cAAc;UACpBmE,KAAK,EAAElB,IAAI,CAACuB,YAAa;UACzBH,IAAI,EAAC,GAAG;UACRC,QAAQ,EAAE,GAAGrB,IAAI,CAACwB,uBAAuB,cAAe;UACxDF,QAAQ,EAAC,mDAAmD;UAC5DpG,QAAQ,EAAE8E,IAAI,CAACwB,uBAAwB;UACvCC,KAAK,EAAEzB,IAAI,CAACwB,uBAAuB,GAAG,EAAE,GAAG,IAAI,GAAGxB,IAAI,CAACwB,uBAAuB,GAAG,EAAE,GAAG,MAAM,GAAG,MAAO;UACtGE,UAAU,EAAE,GAAG1B,IAAI,CAACwB,uBAAuB,GAAI;UAC/C5B,IAAI,EAAC;QAAQ;UAAA1C,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACd;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACE,CAAC,eAEPjE,OAAA,CAACpE,IAAI;QAAC8L,IAAI;QAACC,EAAE,EAAE,EAAG;QAACC,EAAE,EAAE,CAAE;QAACC,EAAE,EAAE,CAAE;QAAA/B,QAAA,eAC9B9F,OAAA,CAACR,UAAU;UACTmE,KAAK,EAAC,iBAAiB;UACvBmE,KAAK,EAAElB,IAAI,CAAC2B,eAAgB;UAC5BP,IAAI,EAAC,GAAG;UACRC,QAAQ,EAAE,GAAG,CAAC,GAAG,GAAGrB,IAAI,CAACwB,uBAAuB,EAAEI,OAAO,CAAC,CAAC,CAAC,iBAAkB;UAC9EN,QAAQ,EAAC,mDAAmD;UAC5D1B,IAAI,EAAC;QAAQ;UAAA1C,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACd;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACE,CAAC,eAEPjE,OAAA,CAACpE,IAAI;QAAC8L,IAAI;QAACC,EAAE,EAAE,EAAG;QAACC,EAAE,EAAE,CAAE;QAACC,EAAE,EAAE,CAAE;QAAA/B,QAAA,eAC9B9F,OAAA,CAACR,UAAU;UACTmE,KAAK,EAAC,cAAc;UACpBmE,KAAK,EAAElB,IAAI,CAAC6B,iBAAiB,IAAI,CAAE;UACnCT,IAAI,EAAC,GAAG;UACRC,QAAQ,EACNrB,IAAI,CAAC8B,cAAc,GACf,GAAG9B,IAAI,CAAC8B,cAAc,4BAA4B,GACjD9B,IAAI,CAAC6B,iBAAiB,GAAG,CAAC,GACvB,kBAAkB,GAClB,sBACT;UACDP,QAAQ,EAAC,mDAAmD;UAC5D1B,IAAI,EAAC,QAAQ;UACbmC,OAAO,EACL/B,IAAI,CAACgC,2BAA2B,GAC5B,gBAAgBhC,IAAI,CAACgC,2BAA2B,oFAAoF,GACpI;QACL;UAAA9E,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACF;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACE,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,EAGN7B,UAAU,iBACTpC,OAAA,CAACvE,GAAG;MAACkK,EAAE,EAAE;QAAEO,EAAE,EAAE;MAAE,CAAE;MAAAJ,QAAA,eACjB9F,OAAA,CAACN,aAAa;QAACkH,IAAI,EAAEA;MAAK;QAAA9C,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAC1B,CACN,eAGDjE,OAAA,CAACpE,IAAI;MAAC4L,SAAS;MAACC,OAAO,EAAE,CAAE;MAAC9B,EAAE,EAAE;QAAEO,EAAE,EAAE;MAAE,CAAE;MAAAJ,QAAA,gBACxC9F,OAAA,CAACpE,IAAI;QAAC8L,IAAI;QAACC,EAAE,EAAE,EAAG;QAACE,EAAE,EAAE,CAAE;QAAA/B,QAAA,eACvB9F,OAAA,CAACnE,IAAI;UAAC8J,EAAE,EAAE;YAAEkD,MAAM,EAAE,MAAM;YAAE9B,MAAM,EAAE;UAAoB,CAAE;UAAAjB,QAAA,eACxD9F,OAAA,CAAClE,WAAW;YAAC6J,EAAE,EAAE;cAAEC,CAAC,EAAE;YAAE,CAAE;YAAAE,QAAA,gBACxB9F,OAAA,CAACvE,GAAG;cAACkK,EAAE,EAAE;gBAAEI,OAAO,EAAE,MAAM;gBAAEE,UAAU,EAAE,QAAQ;gBAAEC,EAAE,EAAE;cAAE,CAAE;cAAAJ,QAAA,gBACxD9F,OAAA,CAACtB,SAAS;gBAACiH,EAAE,EAAE;kBAAEzB,KAAK,EAAE,SAAS;kBAAEqD,EAAE,EAAE,CAAC;kBAAEuB,QAAQ,EAAE;gBAAG;cAAE;gBAAAhF,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,eAC5DjE,OAAA,CAACtE,UAAU;gBAACyK,OAAO,EAAC,IAAI;gBAACR,EAAE,EAAE;kBAAEqB,UAAU,EAAE,GAAG;kBAAE9C,KAAK,EAAE;gBAAU,CAAE;gBAAA4B,QAAA,EAAC;cAEpE;gBAAAhC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAY,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACV,CAAC,eACNjE,OAAA,CAACpE,IAAI;cAAC4L,SAAS;cAACC,OAAO,EAAE,CAAE;cAAA3B,QAAA,gBACzB9F,OAAA,CAACpE,IAAI;gBAAC8L,IAAI;gBAACC,EAAE,EAAE,CAAE;gBAAA7B,QAAA,eACf9F,OAAA,CAACvE,GAAG;kBAACkK,EAAE,EAAE;oBAAEoD,SAAS,EAAE,QAAQ;oBAAEnD,CAAC,EAAE,CAAC;oBAAEiB,OAAO,EAAE,SAAS;oBAAEC,YAAY,EAAE;kBAAE,CAAE;kBAAAhB,QAAA,gBAC1E9F,OAAA,CAACtE,UAAU;oBAACyK,OAAO,EAAC,IAAI;oBAACR,EAAE,EAAE;sBAAEqB,UAAU,EAAE,GAAG;sBAAE9C,KAAK,EAAE,SAAS;sBAAEgC,EAAE,EAAE;oBAAE,CAAE;oBAAAJ,QAAA,EACvEc,IAAI,CAACoC;kBAAW;oBAAAlF,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACP,CAAC,eACbjE,OAAA,CAACtE,UAAU;oBAACyK,OAAO,EAAC,OAAO;oBAACR,EAAE,EAAE;sBAAEzB,KAAK,EAAE;oBAAO,CAAE;oBAAA4B,QAAA,EAAC;kBAEnD;oBAAAhC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAY,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACV;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACF,CAAC,eACPjE,OAAA,CAACpE,IAAI;gBAAC8L,IAAI;gBAACC,EAAE,EAAE,CAAE;gBAAA7B,QAAA,eACf9F,OAAA,CAACvE,GAAG;kBAACkK,EAAE,EAAE;oBAAEoD,SAAS,EAAE,QAAQ;oBAAEnD,CAAC,EAAE,CAAC;oBAAEiB,OAAO,EAAE,SAAS;oBAAEC,YAAY,EAAE;kBAAE,CAAE;kBAAAhB,QAAA,gBAC1E9F,OAAA,CAACtE,UAAU;oBAACyK,OAAO,EAAC,IAAI;oBAACR,EAAE,EAAE;sBAAEqB,UAAU,EAAE,GAAG;sBAAE9C,KAAK,EAAE,SAAS;sBAAEgC,EAAE,EAAE;oBAAE,CAAE;oBAAAJ,QAAA,EACvEc,IAAI,CAACqC;kBAAW;oBAAAnF,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACP,CAAC,eACbjE,OAAA,CAACtE,UAAU;oBAACyK,OAAO,EAAC,OAAO;oBAACR,EAAE,EAAE;sBAAEzB,KAAK,EAAE;oBAAO,CAAE;oBAAA4B,QAAA,GAAC,eACpC,EAACc,IAAI,CAACsC,gBAAgB,EAAC,IACtC;kBAAA;oBAAApF,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAY,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACV;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACF,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC,eACPjE,OAAA,CAACvE,GAAG;cAACkK,EAAE,EAAE;gBAAEE,EAAE,EAAE;cAAE,CAAE;cAAAC,QAAA,gBACjB9F,OAAA,CAACvE,GAAG;gBAACkK,EAAE,EAAE;kBAAEI,OAAO,EAAE,MAAM;kBAAEC,cAAc,EAAE,eAAe;kBAAEE,EAAE,EAAE;gBAAE,CAAE;gBAAAJ,QAAA,gBACnE9F,OAAA,CAACtE,UAAU;kBAACyK,OAAO,EAAC,OAAO;kBAAAL,QAAA,EAAC;gBAAS;kBAAAhC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAY,CAAC,eAClDjE,OAAA,CAACtE,UAAU;kBAACyK,OAAO,EAAC,OAAO;kBAACR,EAAE,EAAE;oBAAEqB,UAAU,EAAE;kBAAI,CAAE;kBAAAlB,QAAA,GACjDc,IAAI,CAACsC,gBAAgB,EAAC,GACzB;gBAAA;kBAAApF,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAY,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACV,CAAC,eACNjE,OAAA,CAACvE,GAAG;gBAACkK,EAAE,EAAE;kBACPwD,KAAK,EAAE,MAAM;kBACbN,MAAM,EAAE,CAAC;kBACThC,OAAO,EAAE,SAAS;kBAClBC,YAAY,EAAE,CAAC;kBACfsC,QAAQ,EAAE;gBACZ,CAAE;gBAAAtD,QAAA,eACA9F,OAAA,CAACvE,GAAG;kBAACkK,EAAE,EAAE;oBACPwD,KAAK,EAAE,GAAGvC,IAAI,CAACsC,gBAAgB,GAAG;oBAClCL,MAAM,EAAE,MAAM;oBACdhC,OAAO,EAAE,SAAS;oBAClBwC,UAAU,EAAE;kBACd;gBAAE;kBAAAvF,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACF,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACK;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACV;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,eAEPjE,OAAA,CAACpE,IAAI;QAAC8L,IAAI;QAACC,EAAE,EAAE,EAAG;QAACE,EAAE,EAAE,CAAE;QAAA/B,QAAA,eACvB9F,OAAA,CAACnE,IAAI;UAAC8J,EAAE,EAAE;YAAEkD,MAAM,EAAE,MAAM;YAAE9B,MAAM,EAAE;UAAoB,CAAE;UAAAjB,QAAA,eACxD9F,OAAA,CAAClE,WAAW;YAAC6J,EAAE,EAAE;cAAEC,CAAC,EAAE;YAAE,CAAE;YAAAE,QAAA,gBACxB9F,OAAA,CAACvE,GAAG;cAACkK,EAAE,EAAE;gBAAEI,OAAO,EAAE,MAAM;gBAAEE,UAAU,EAAE,QAAQ;gBAAEC,EAAE,EAAE;cAAE,CAAE;cAAAJ,QAAA,gBACxD9F,OAAA,CAACpC,YAAY;gBAAC+H,EAAE,EAAE;kBAAEzB,KAAK,EAAE,SAAS;kBAAEqD,EAAE,EAAE,CAAC;kBAAEuB,QAAQ,EAAE;gBAAG;cAAE;gBAAAhF,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,eAC/DjE,OAAA,CAACtE,UAAU;gBAACyK,OAAO,EAAC,IAAI;gBAACR,EAAE,EAAE;kBAAEqB,UAAU,EAAE,GAAG;kBAAE9C,KAAK,EAAE;gBAAU,CAAE;gBAAA4B,QAAA,EAAC;cAEpE;gBAAAhC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAY,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACV,CAAC,eACNjE,OAAA,CAACvE,GAAG;cAACkK,EAAE,EAAE;gBAAEoD,SAAS,EAAE,QAAQ;gBAAE7C,EAAE,EAAE;cAAE,CAAE;cAAAJ,QAAA,gBACtC9F,OAAA,CAACtE,UAAU;gBAACyK,OAAO,EAAC,IAAI;gBAACR,EAAE,EAAE;kBAAEqB,UAAU,EAAE,GAAG;kBAAE9C,KAAK,EAAE,SAAS;kBAAEgC,EAAE,EAAE;gBAAE,CAAE;gBAAAJ,QAAA,GACvEc,IAAI,CAAC6B,iBAAiB,IAAI,CAAC,EAAC,GAC/B;cAAA;gBAAA3E,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAY,CAAC,eACbjE,OAAA,CAACtE,UAAU;gBAACyK,OAAO,EAAC,OAAO;gBAACR,EAAE,EAAE;kBAAEzB,KAAK,EAAE,MAAM;kBAAEgC,EAAE,EAAE;gBAAE,CAAE;gBAAAJ,QAAA,EAAC;cAE1D;gBAAAhC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAY,CAAC,EACZ2C,IAAI,CAACgC,2BAA2B,iBAC/B5I,OAAA,CAACtE,UAAU;gBAACyK,OAAO,EAAC,SAAS;gBAACR,EAAE,EAAE;kBAAEzB,KAAK,EAAE,MAAM;kBAAE4E,QAAQ,EAAE;gBAAU,CAAE;gBAAAhD,QAAA,GAAC,YAC9D,EAACc,IAAI,CAACgC,2BAA2B,EAAC,6BAC9C;cAAA;gBAAA9E,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAY,CACb;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACE,CAAC,EACL2C,IAAI,CAAC8B,cAAc,gBAClB1I,OAAA,CAACvE,GAAG;cAACkK,EAAE,EAAE;gBAAEoD,SAAS,EAAE,QAAQ;gBAAEnD,CAAC,EAAE,CAAC;gBAAEiB,OAAO,EAAE,SAAS;gBAAEC,YAAY,EAAE;cAAE,CAAE;cAAAhB,QAAA,gBAC1E9F,OAAA,CAACtE,UAAU;gBAACyK,OAAO,EAAC,IAAI;gBAACR,EAAE,EAAE;kBAAEqB,UAAU,EAAE,GAAG;kBAAE9C,KAAK,EAAE,SAAS;kBAAEgC,EAAE,EAAE;gBAAI,CAAE;gBAAAJ,QAAA,EACzEc,IAAI,CAAC0C;cAAkB;gBAAAxF,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACd,CAAC,eACbjE,OAAA,CAACtE,UAAU;gBAACyK,OAAO,EAAC,OAAO;gBAACR,EAAE,EAAE;kBAAEzB,KAAK,EAAE;gBAAU,CAAE;gBAAA4B,QAAA,GAAC,4BAC1B,EAACc,IAAI,CAAC8B,cAAc,EAAC,SACjD;cAAA;gBAAA5E,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAY,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACV,CAAC,gBAENjE,OAAA,CAACvE,GAAG;cAACkK,EAAE,EAAE;gBAAEoD,SAAS,EAAE,QAAQ;gBAAEnD,CAAC,EAAE,CAAC;gBAAEiB,OAAO,EAAE,SAAS;gBAAEC,YAAY,EAAE;cAAE,CAAE;cAAAhB,QAAA,eAC1E9F,OAAA,CAACtE,UAAU;gBAACyK,OAAO,EAAC,OAAO;gBAACR,EAAE,EAAE;kBAAEzB,KAAK,EAAE;gBAAO,CAAE;gBAAA4B,QAAA,EAC/Cc,IAAI,CAAC6B,iBAAiB,GAAG,CAAC,GAAG,wBAAwB,GAAG;cAAuD;gBAAA3E,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACtG;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACV,CACN;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACU;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACV;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,EAGN2C,IAAI,CAAC2C,YAAY,IAAI3C,IAAI,CAAC2C,YAAY,CAACC,MAAM,GAAG,CAAC,iBAChDxJ,OAAA,CAACnE,IAAI;MAAC8J,EAAE,EAAE;QAAEoB,MAAM,EAAE;MAAoB,CAAE;MAAAjB,QAAA,eACxC9F,OAAA,CAAClE,WAAW;QAAC6J,EAAE,EAAE;UAAEC,CAAC,EAAE;QAAE,CAAE;QAAAE,QAAA,gBACxB9F,OAAA,CAACvE,GAAG;UAACkK,EAAE,EAAE;YAAEI,OAAO,EAAE,MAAM;YAAEE,UAAU,EAAE,QAAQ;YAAEC,EAAE,EAAE;UAAE,CAAE;UAAAJ,QAAA,gBACxD9F,OAAA,CAACxB,aAAa;YAACmH,EAAE,EAAE;cAAEzB,KAAK,EAAE,SAAS;cAAEqD,EAAE,EAAE,CAAC;cAAEuB,QAAQ,EAAE;YAAG;UAAE;YAAAhF,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,eAChEjE,OAAA,CAACtE,UAAU;YAACyK,OAAO,EAAC,IAAI;YAACR,EAAE,EAAE;cAAEqB,UAAU,EAAE,GAAG;cAAE9C,KAAK,EAAE;YAAU,CAAE;YAAA4B,QAAA,EAAC;UAEpE;YAAAhC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAY,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACV,CAAC,eAGNjE,OAAA,CAACpE,IAAI;UAAC4L,SAAS;UAACC,OAAO,EAAE,CAAE;UAAA3B,QAAA,EACxBc,IAAI,CAAC2C,YAAY,CAACE,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC,CAACC,GAAG,CAAC,CAACC,IAAI,EAAEC,KAAK,kBAC7C5J,OAAA,CAACpE,IAAI;YAAC8L,IAAI;YAACC,EAAE,EAAE,EAAG;YAACC,EAAE,EAAE,CAAE;YAACC,EAAE,EAAE,CAAE;YAAA/B,QAAA,eAC9B9F,OAAA,CAACvE,GAAG;cAACkK,EAAE,EAAE;gBACPC,CAAC,EAAE,CAAC;gBACJmB,MAAM,EAAE,mBAAmB;gBAC3BD,YAAY,EAAE,CAAC;gBACfD,OAAO,EAAE+C,KAAK,KAAK,CAAC,GAAG,SAAS,GAAG,SAAS;gBAC5CP,UAAU,EAAE,UAAU;gBACtB,SAAS,EAAE;kBACTxC,OAAO,EAAE,SAAS;kBAClBgD,SAAS,EAAE,kBAAkB;kBAC7BC,SAAS,EAAE;gBACb;cACF,CAAE;cAAAhE,QAAA,gBACA9F,OAAA,CAACtE,UAAU;gBAACyK,OAAO,EAAC,OAAO;gBAACR,EAAE,EAAE;kBAAEzB,KAAK,EAAE,MAAM;kBAAEgC,EAAE,EAAE;gBAAE,CAAE;gBAAAJ,QAAA,EACtD6D,IAAI,CAAC/C;cAAI;gBAAA9C,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACA,CAAC,eACbjE,OAAA,CAACtE,UAAU;gBAACyK,OAAO,EAAC,IAAI;gBAACR,EAAE,EAAE;kBAAEqB,UAAU,EAAE,GAAG;kBAAE9C,KAAK,EAAE;gBAAU,CAAE;gBAAA4B,QAAA,GAChE6D,IAAI,CAACI,KAAK,EAAC,GACd;cAAA;gBAAAjG,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAY,CAAC,EACZ2F,KAAK,KAAK,CAAC,iBACV5J,OAAA,CAAC/D,IAAI;gBACHqL,KAAK,EAAC,gBAAa;gBACnBd,IAAI,EAAC,OAAO;gBACZb,EAAE,EAAE;kBACFE,EAAE,EAAE,CAAC;kBACLgB,OAAO,EAAE,SAAS;kBAClB3C,KAAK,EAAE,OAAO;kBACd4E,QAAQ,EAAE;gBACZ;cAAE;gBAAAhF,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CACF;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACE;UAAC,GA/B8B2F,KAAK;YAAA9F,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAgCrC,CACP;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACE,CAAC,EAGN2C,IAAI,CAAC2C,YAAY,CAACC,MAAM,GAAG,CAAC,iBAC3BxJ,OAAA,CAACvE,GAAG;UAACkK,EAAE,EAAE;YAAEE,EAAE,EAAE,CAAC;YAAEkD,SAAS,EAAE;UAAS,CAAE;UAAAjD,QAAA,eACtC9F,OAAA,CAAChD,SAAS;YAAA8I,QAAA,gBACR9F,OAAA,CAAC/C,gBAAgB;cAAC+M,UAAU,eAAEhK,OAAA,CAAClB,cAAc;gBAAAgF,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAE;cAAA6B,QAAA,eAC/C9F,OAAA,CAACtE,UAAU;gBAACyK,OAAO,EAAC,OAAO;gBAACR,EAAE,EAAE;kBAAEzB,KAAK,EAAE;gBAAU,CAAE;gBAAA4B,QAAA,GAAC,iBACrC,EAACc,IAAI,CAAC2C,YAAY,CAACC,MAAM,EAAC,SAC3C;cAAA;gBAAA1F,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAY;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACG,CAAC,eACnBjE,OAAA,CAAC9C,gBAAgB;cAAA4I,QAAA,eACf9F,OAAA,CAACV,eAAe;gBACdsH,IAAI,EAAEA,IAAI,CAAC2C,YAAY,CAACG,GAAG,CAACC,IAAI,KAAK;kBACnC/C,IAAI,EAAE+C,IAAI,CAAC/C,IAAI;kBACfmD,KAAK,EAAE,GAAGJ,IAAI,CAACI,KAAK;gBACtB,CAAC,CAAC,CAAE;gBACJE,OAAO,EAAE,CACP;kBAAEC,KAAK,EAAE,MAAM;kBAAEC,UAAU,EAAE,MAAM;kBAAEhB,KAAK,EAAE;gBAAI,CAAC,EACjD;kBAAEe,KAAK,EAAE,OAAO;kBAAEC,UAAU,EAAE,cAAc;kBAAEhB,KAAK,EAAE,GAAG;kBAAEiB,KAAK,EAAE;gBAAQ,CAAC,CAC1E;gBACFC,UAAU,EAAE,IAAK;gBACjBC,QAAQ,EAAE;cAAG;gBAAAxG,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACd;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACc,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACV;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACT,CACN;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACU;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACV,CACP;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACE,CACN;EAED,MAAMyC,eAAe,GAAIE,IAAI,iBAC3B5G,OAAA,CAACvE,GAAG;IAAAqK,QAAA,gBAEF9F,OAAA,CAACvE,GAAG;MAACkK,EAAE,EAAE;QACPI,OAAO,EAAE,MAAM;QACfE,UAAU,EAAE,QAAQ;QACpBC,EAAE,EAAE,CAAC;QACLN,CAAC,EAAE,CAAC;QACJiB,OAAO,EAAE,SAAS;QAClBC,YAAY,EAAE,CAAC;QACfC,MAAM,EAAE;MACV,CAAE;MAAAjB,QAAA,gBACA9F,OAAA,CAAClC,QAAQ;QAAC6H,EAAE,EAAE;UAAEzB,KAAK,EAAE,SAAS;UAAEqD,EAAE,EAAE,CAAC;UAAEuB,QAAQ,EAAE;QAAG;MAAE;QAAAhF,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC,eAC3DjE,OAAA,CAACtE,UAAU;QAACyK,OAAO,EAAC,IAAI;QAACR,EAAE,EAAE;UAAEqB,UAAU,EAAE,GAAG;UAAE9C,KAAK,EAAE;QAAU,CAAE;QAAA4B,QAAA,EAAC;MAEpE;QAAAhC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAY,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACV,CAAC,EAGL7B,UAAU,iBACTpC,OAAA,CAACvE,GAAG;MAACkK,EAAE,EAAE;QAAEO,EAAE,EAAE;MAAE,CAAE;MAAAJ,QAAA,eACjB9F,OAAA,CAACJ,QAAQ;QAACgH,IAAI,EAAEA;MAAK;QAAA9C,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACrB,CACN,eAGDjE,OAAA,CAACnE,IAAI;MAAC8J,EAAE,EAAE;QAAEO,EAAE,EAAE,CAAC;QAAEa,MAAM,EAAE;MAAoB,CAAE;MAAAjB,QAAA,eAC/C9F,OAAA,CAAClE,WAAW;QAAC6J,EAAE,EAAE;UAAEC,CAAC,EAAE;QAAE,CAAE;QAAAE,QAAA,gBACxB9F,OAAA,CAACvE,GAAG;UAACkK,EAAE,EAAE;YAAEI,OAAO,EAAE,MAAM;YAAEE,UAAU,EAAE,QAAQ;YAAEC,EAAE,EAAE;UAAE,CAAE;UAAAJ,QAAA,gBACxD9F,OAAA,CAACtB,SAAS;YAACiH,EAAE,EAAE;cAAEzB,KAAK,EAAE,SAAS;cAAEqD,EAAE,EAAE,CAAC;cAAEuB,QAAQ,EAAE;YAAG;UAAE;YAAAhF,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,eAC5DjE,OAAA,CAACtE,UAAU;YAACyK,OAAO,EAAC,IAAI;YAACR,EAAE,EAAE;cAAEqB,UAAU,EAAE,GAAG;cAAE9C,KAAK,EAAE;YAAU,CAAE;YAAA4B,QAAA,EAAC;UAEpE;YAAAhC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAY,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACV,CAAC,eACNjE,OAAA,CAACV,eAAe;UACdsH,IAAI,EAAEA,IAAI,CAAC2D,aAAa,IAAI,EAAG;UAC/BN,OAAO,EAAE,CACP;YAAEC,KAAK,EAAE,WAAW;YAAEC,UAAU,EAAE,WAAW;YAAEhB,KAAK,EAAE;UAAI,CAAC,EAC3D;YAAEe,KAAK,EAAE,SAAS;YAAEC,UAAU,EAAE,SAAS;YAAEhB,KAAK,EAAE;UAAI,CAAC,EACvD;YAAEe,KAAK,EAAE,UAAU;YAAEC,UAAU,EAAE,MAAM;YAAEhB,KAAK,EAAE,EAAE;YAAEiB,KAAK,EAAE,OAAO;YAAEI,QAAQ,EAAE;UAAS,CAAC,EACxF;YAAEN,KAAK,EAAE,eAAe;YAAEC,UAAU,EAAE,eAAe;YAAEhB,KAAK,EAAE,GAAG;YAAEiB,KAAK,EAAE,OAAO;YAAEI,QAAQ,EAAE,QAAQ;YACnGC,UAAU,EAAGC,GAAG,IAAK,GAAGA,GAAG,CAACC,aAAa;UAAI,CAAC,EAChD;YAAET,KAAK,EAAE,aAAa;YAAEC,UAAU,EAAE,aAAa;YAAEhB,KAAK,EAAE,GAAG;YAAEiB,KAAK,EAAE,OAAO;YAAEI,QAAQ,EAAE,QAAQ;YAC/FC,UAAU,EAAGC,GAAG,IAAK,GAAGA,GAAG,CAACE,WAAW;UAAI,CAAC,EAC9C;YAAEV,KAAK,EAAE,iBAAiB;YAAEC,UAAU,EAAE,WAAW;YAAEhB,KAAK,EAAE,GAAG;YAAEiB,KAAK,EAAE,OAAO;YAAEI,QAAQ,EAAE,QAAQ;YACjGC,UAAU,EAAGC,GAAG,IAAK,GAAGA,GAAG,CAACnC,eAAe;UAAI,CAAC,CAClD;UACF+B,QAAQ,EAAE;QAAG;UAAAxG,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACd,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACS;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACV,CAAC,eAGPjE,OAAA,CAACnE,IAAI;MAAC8J,EAAE,EAAE;QAAEoB,MAAM,EAAE;MAAoB,CAAE;MAAAjB,QAAA,eACxC9F,OAAA,CAAClE,WAAW;QAAC6J,EAAE,EAAE;UAAEC,CAAC,EAAE;QAAE,CAAE;QAAAE,QAAA,gBACxB9F,OAAA,CAACvE,GAAG;UAACkK,EAAE,EAAE;YAAEI,OAAO,EAAE,MAAM;YAAEE,UAAU,EAAE,QAAQ;YAAEC,EAAE,EAAE;UAAE,CAAE;UAAAJ,QAAA,gBACxD9F,OAAA,CAACpB,aAAa;YAAC+G,EAAE,EAAE;cAAEzB,KAAK,EAAE,SAAS;cAAEqD,EAAE,EAAE,CAAC;cAAEuB,QAAQ,EAAE;YAAG;UAAE;YAAAhF,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,eAChEjE,OAAA,CAACtE,UAAU;YAACyK,OAAO,EAAC,IAAI;YAACR,EAAE,EAAE;cAAEqB,UAAU,EAAE,GAAG;cAAE9C,KAAK,EAAE;YAAU,CAAE;YAAA4B,QAAA,EAAC;UAEpE;YAAAhC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAY,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACV,CAAC,eACNjE,OAAA,CAACV,eAAe;UACdsH,IAAI,EAAEA,IAAI,CAACiE,eAAe,IAAI,EAAG;UACjCZ,OAAO,EAAE,CACP;YAAEC,KAAK,EAAE,WAAW;YAAEC,UAAU,EAAE,WAAW;YAAEhB,KAAK,EAAE;UAAI,CAAC,EAC3D;YAAEe,KAAK,EAAE,SAAS;YAAEC,UAAU,EAAE,SAAS;YAAEhB,KAAK,EAAE;UAAI,CAAC,EACvD;YAAEe,KAAK,EAAE,YAAY;YAAEC,UAAU,EAAE,QAAQ;YAAEhB,KAAK,EAAE,GAAG;YAAEiB,KAAK,EAAE,OAAO;YAAEI,QAAQ,EAAE;UAAS,CAAC,EAC7F;YAAEN,KAAK,EAAE,mBAAmB;YAAEC,UAAU,EAAE,mBAAmB;YAAEhB,KAAK,EAAE,GAAG;YAAEiB,KAAK,EAAE,OAAO;YAAEI,QAAQ,EAAE,QAAQ;YAC3GC,UAAU,EAAGC,GAAG,IAAK,GAAGA,GAAG,CAACI,iBAAiB;UAAI,CAAC,CACpD;UACFR,QAAQ,EAAE;QAAG;UAAAxG,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACd,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACS;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACV,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACJ,CACN;EAMD,MAAM0C,uBAAuB,GAAIC,IAAI,iBACnC5G,OAAA,CAACvE,GAAG;IAAAqK,QAAA,gBAEF9F,OAAA,CAACvE,GAAG;MAACkK,EAAE,EAAE;QAAEI,OAAO,EAAE,MAAM;QAAEC,cAAc,EAAE,eAAe;QAAEC,UAAU,EAAE,QAAQ;QAAEC,EAAE,EAAE;MAAE,CAAE;MAAAJ,QAAA,gBACzF9F,OAAA,CAACtE,UAAU;QAACyK,OAAO,EAAC,IAAI;QAACR,EAAE,EAAE;UAAEqB,UAAU,EAAE,GAAG;UAAE9C,KAAK,EAAE;QAAe,CAAE;QAAA4B,QAAA,EAAC;MAEzE;QAAAhC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAY,CAAC,eACbjE,OAAA,CAAC5C,gBAAgB;QACf6J,OAAO,eACLjH,OAAA,CAAC7C,MAAM;UACL+J,OAAO,EAAE9E,UAAW;UACpB+E,QAAQ,EAAGC,CAAC,IAAK/E,aAAa,CAAC+E,CAAC,CAACC,MAAM,CAACH,OAAO,CAAE;UACjDhD,KAAK,EAAC;QAAS;UAAAJ,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAChB,CACF;QACDqD,KAAK,eACHtH,OAAA,CAACvE,GAAG;UAACkK,EAAE,EAAE;YAAEI,OAAO,EAAE,MAAM;YAAEE,UAAU,EAAE;UAAS,CAAE;UAAAH,QAAA,gBACjD9F,OAAA,CAAChB,aAAa;YAAC2G,EAAE,EAAE;cAAE4B,EAAE,EAAE;YAAE;UAAE;YAAAzD,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,WAElC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAK;MACN;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACF,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACC,CAAC,eAGNjE,OAAA,CAACpE,IAAI;MAAC4L,SAAS;MAACC,OAAO,EAAE,CAAE;MAAC9B,EAAE,EAAE;QAAEO,EAAE,EAAE;MAAE,CAAE;MAAAJ,QAAA,gBACxC9F,OAAA,CAACpE,IAAI;QAAC8L,IAAI;QAACC,EAAE,EAAE,EAAG;QAACE,EAAE,EAAE,CAAE;QAAA/B,QAAA,eACvB9F,OAAA,CAACrE,KAAK;UAACgK,EAAE,EAAE;YAAEC,CAAC,EAAE,CAAC;YAAEmD,SAAS,EAAE,QAAQ;YAAElC,OAAO,EAAE,cAAc;YAAE3C,KAAK,EAAE;UAAQ,CAAE;UAAA4B,QAAA,gBAChF9F,OAAA,CAACtE,UAAU;YAACyK,OAAO,EAAC,IAAI;YAACR,EAAE,EAAE;cAAEqB,UAAU,EAAE,MAAM;cAAEd,EAAE,EAAE;YAAE,CAAE;YAAAJ,QAAA,GACxDc,IAAI,CAACmE,oBAAoB,EAAC,GAC7B;UAAA;YAAAjH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAY,CAAC,eACbjE,OAAA,CAACtE,UAAU;YAACyK,OAAO,EAAC,OAAO;YAAAL,QAAA,EAAC;UAAY;YAAAhC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAY,CAAC,eACrDjE,OAAA,CAACtE,UAAU;YAACyK,OAAO,EAAC,SAAS;YAAAL,QAAA,GAAEc,IAAI,CAACnF,WAAW,EAAC,KAAG,EAACmF,IAAI,CAAClF,SAAS;UAAA;YAAAoC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAa,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC3E;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACJ,CAAC,eACPjE,OAAA,CAACpE,IAAI;QAAC8L,IAAI;QAACC,EAAE,EAAE,EAAG;QAACE,EAAE,EAAE,CAAE;QAAA/B,QAAA,eACvB9F,OAAA,CAACrE,KAAK;UAACgK,EAAE,EAAE;YAAEC,CAAC,EAAE,CAAC;YAAEmD,SAAS,EAAE,QAAQ;YAAElC,OAAO,EAAE,WAAW;YAAE3C,KAAK,EAAE;UAAQ,CAAE;UAAA4B,QAAA,gBAC7E9F,OAAA,CAACtE,UAAU;YAACyK,OAAO,EAAC,IAAI;YAACR,EAAE,EAAE;cAAEqB,UAAU,EAAE,MAAM;cAAEd,EAAE,EAAE;YAAE,CAAE;YAAAJ,QAAA,EACxDc,IAAI,CAACoE;UAAa;YAAAlH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACT,CAAC,eACbjE,OAAA,CAACtE,UAAU;YAACyK,OAAO,EAAC,OAAO;YAAAL,QAAA,EAAC;UAAa;YAAAhC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAY,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACjD;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACJ,CAAC,eACPjE,OAAA,CAACpE,IAAI;QAAC8L,IAAI;QAACC,EAAE,EAAE,EAAG;QAACE,EAAE,EAAE,CAAE;QAAA/B,QAAA,eACvB9F,OAAA,CAACrE,KAAK;UAACgK,EAAE,EAAE;YAAEC,CAAC,EAAE,CAAC;YAAEmD,SAAS,EAAE,QAAQ;YAAElC,OAAO,EAAE,cAAc;YAAE3C,KAAK,EAAE;UAAQ,CAAE;UAAA4B,QAAA,gBAChF9F,OAAA,CAACtE,UAAU;YAACyK,OAAO,EAAC,IAAI;YAACR,EAAE,EAAE;cAAEqB,UAAU,EAAE,MAAM;cAAEd,EAAE,EAAE;YAAE,CAAE;YAAAJ,QAAA,GACxDc,IAAI,CAAC6B,iBAAiB,EAAC,GAC1B;UAAA;YAAA3E,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAY,CAAC,eACbjE,OAAA,CAACtE,UAAU;YAACyK,OAAO,EAAC,OAAO;YAAAL,QAAA,EAAC;UAAY;YAAAhC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAY,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAChD;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACJ,CAAC,eACPjE,OAAA,CAACpE,IAAI;QAAC8L,IAAI;QAACC,EAAE,EAAE,EAAG;QAACE,EAAE,EAAE,CAAE;QAAA/B,QAAA,eACvB9F,OAAA,CAACrE,KAAK;UAACgK,EAAE,EAAE;YAAEC,CAAC,EAAE,CAAC;YAAEmD,SAAS,EAAE,QAAQ;YAAElC,OAAO,EAAE,cAAc;YAAE3C,KAAK,EAAE;UAAQ,CAAE;UAAA4B,QAAA,gBAChF9F,OAAA,CAACtE,UAAU;YAACyK,OAAO,EAAC,IAAI;YAACR,EAAE,EAAE;cAAEqB,UAAU,EAAE,MAAM;cAAEd,EAAE,EAAE;YAAE,CAAE;YAAAJ,QAAA,GACxDmF,IAAI,CAACC,KAAK,CAACtE,IAAI,CAACmE,oBAAoB,GAAGnE,IAAI,CAACoE,aAAa,GAAG,CAAC,CAAC,EAAC,GAClE;UAAA;YAAAlH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAY,CAAC,eACbjE,OAAA,CAACtE,UAAU;YAACyK,OAAO,EAAC,OAAO;YAAAL,QAAA,EAAC;UAAe;YAAAhC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAY,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACnD;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACJ,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,EAGN7B,UAAU,iBACTpC,OAAA,CAACvE,GAAG;MAACkK,EAAE,EAAE;QAAEO,EAAE,EAAE;MAAE,CAAE;MAAAJ,QAAA,eACjB9F,OAAA,CAACH,aAAa;QAAC+G,IAAI,EAAEA;MAAK;QAAA9C,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAC1B,CACN,eAGDjE,OAAA,CAACrE,KAAK;MAACgK,EAAE,EAAE;QAAEC,CAAC,EAAE;MAAE,CAAE;MAAAE,QAAA,gBAClB9F,OAAA,CAACtE,UAAU;QAACyK,OAAO,EAAC,IAAI;QAACR,EAAE,EAAE;UAAEO,EAAE,EAAE,CAAC;UAAEc,UAAU,EAAE;QAAI,CAAE;QAAAlB,QAAA,EAAC;MAEzD;QAAAhC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAY,CAAC,eACbjE,OAAA,CAACV,eAAe;QACdsH,IAAI,EAAEA,IAAI,CAACuE,gBAAgB,IAAI,EAAG;QAClClB,OAAO,EAAE,CACP;UAAEC,KAAK,EAAE,MAAM;UAAEC,UAAU,EAAE,MAAM;UAAEhB,KAAK,EAAE;QAAI,CAAC,EACjD;UAAEe,KAAK,EAAE,OAAO;UAAEC,UAAU,EAAE,cAAc;UAAEhB,KAAK,EAAE,GAAG;UAAEiB,KAAK,EAAE,OAAO;UAAEI,QAAQ,EAAE,QAAQ;UAC1FC,UAAU,EAAGC,GAAG,IAAK,GAAGA,GAAG,CAACX,KAAK;QAAI,CAAC,CACxC;QACFO,QAAQ,EAAE;MAAG;QAAAxG,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACd,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACG,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACL,CACN;EAID,MAAMmH,YAAY,GAAGA,CAAA,kBACnBpL,OAAA,CAACzD,MAAM;IAACsI,IAAI,EAAE7D,UAAW;IAACqK,OAAO,EAAE5F,iBAAkB;IAAC6F,QAAQ,EAAC,IAAI;IAACC,SAAS;IAAAzF,QAAA,gBAC3E9F,OAAA,CAACxD,WAAW;MAAAsJ,QAAA,EACThF,cAAc,aAAdA,cAAc,uBAAdA,cAAc,CAAE6C;IAAK;MAAAG,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACX,CAAC,eACdjE,OAAA,CAACvD,aAAa;MAAAqJ,QAAA,GACXpF,KAAK,iBACJV,OAAA,CAAC9D,KAAK;QAACsP,QAAQ,EAAC,OAAO;QAAC7F,EAAE,EAAE;UAAEO,EAAE,EAAE;QAAE,CAAE;QAAAJ,QAAA,EACnCpF;MAAK;QAAAoD,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACD,CACR,eAEDjE,OAAA,CAACpE,IAAI;QAAC4L,SAAS;QAACC,OAAO,EAAE,CAAE;QAAC9B,EAAE,EAAE;UAAEE,EAAE,EAAE;QAAE,CAAE;QAAAC,QAAA,gBACxC9F,OAAA,CAACpE,IAAI;UAAC8L,IAAI;UAACC,EAAE,EAAE,EAAG;UAAA7B,QAAA,eAChB9F,OAAA,CAACrD,WAAW;YAAC4O,SAAS;YAAAzF,QAAA,gBACpB9F,OAAA,CAACpD,UAAU;cAAAkJ,QAAA,EAAC;YAAO;cAAAhC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAY,CAAC,eAChCjE,OAAA,CAACnD,MAAM;cACLiL,KAAK,EAAExG,QAAQ,CAACE,OAAQ;cACxB8F,KAAK,EAAC,SAAS;cACfH,QAAQ,EAAGC,CAAC,IAAK7F,WAAW,CAAC;gBAAE,GAAGD,QAAQ;gBAAEE,OAAO,EAAE4F,CAAC,CAACC,MAAM,CAACS;cAAM,CAAC,CAAE;cAAAhC,QAAA,gBAEvE9F,OAAA,CAAClD,QAAQ;gBAACgL,KAAK,EAAC,OAAO;gBAAAhC,QAAA,EAAC;cAAoB;gBAAAhC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAU,CAAC,eACvDjE,OAAA,CAAClD,QAAQ;gBAACgL,KAAK,EAAC,KAAK;gBAAAhC,QAAA,EAAC;cAAY;gBAAAhC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAU,CAAC,eAC7CjE,OAAA,CAAClD,QAAQ;gBAACgL,KAAK,EAAC,OAAO;gBAAAhC,QAAA,EAAC;cAAc;gBAAAhC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAU,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC3C,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACE;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACV,CAAC,EAEN/C,UAAU,KAAK,kBAAkB,iBAChClB,OAAA,CAACpE,IAAI;UAAC8L,IAAI;UAACC,EAAE,EAAE,EAAG;UAAA7B,QAAA,eAChB9F,OAAA,CAACjD,SAAS;YACRwO,SAAS;YACTjE,KAAK,EAAC,WAAW;YACjBQ,KAAK,EAAExG,QAAQ,CAACK,SAAU;YAC1BwF,QAAQ,EAAGC,CAAC,IAAK7F,WAAW,CAAC;cAAE,GAAGD,QAAQ;cAAEK,SAAS,EAAEyF,CAAC,CAACC,MAAM,CAACS;YAAM,CAAC,CAAE;YACzE2D,WAAW,EAAC,mBAAmB;YAC/BC,UAAU,EAAC;UAA0D;YAAA5H,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACtE;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACE,CACP,EAEA/C,UAAU,KAAK,cAAc,iBAC5BlB,OAAA,CAAAE,SAAA;UAAA4F,QAAA,gBACE9F,OAAA,CAACpE,IAAI;YAAC8L,IAAI;YAACC,EAAE,EAAE,CAAE;YAAA7B,QAAA,eACf9F,OAAA,CAACjD,SAAS;cACRwO,SAAS;cACTI,IAAI,EAAC,MAAM;cACXrE,KAAK,EAAC,aAAa;cACnBQ,KAAK,EAAExG,QAAQ,CAACG,WAAY;cAC5B0F,QAAQ,EAAGC,CAAC,IAAK7F,WAAW,CAAC;gBAAE,GAAGD,QAAQ;gBAAEG,WAAW,EAAE2F,CAAC,CAACC,MAAM,CAACS;cAAM,CAAC,CAAE;cAC3E8D,eAAe,EAAE;gBAAEC,MAAM,EAAE;cAAK;YAAE;cAAA/H,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACnC;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACE,CAAC,eACPjE,OAAA,CAACpE,IAAI;YAAC8L,IAAI;YAACC,EAAE,EAAE,CAAE;YAAA7B,QAAA,eACf9F,OAAA,CAACjD,SAAS;cACRwO,SAAS;cACTI,IAAI,EAAC,MAAM;cACXrE,KAAK,EAAC,WAAW;cACjBQ,KAAK,EAAExG,QAAQ,CAACI,SAAU;cAC1ByF,QAAQ,EAAGC,CAAC,IAAK7F,WAAW,CAAC;gBAAE,GAAGD,QAAQ;gBAAEI,SAAS,EAAE0F,CAAC,CAACC,MAAM,CAACS;cAAM,CAAC,CAAE;cACzE8D,eAAe,EAAE;gBAAEC,MAAM,EAAE;cAAK;YAAE;cAAA/H,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACnC;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACE,CAAC;QAAA,eACP,CACH;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACG,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACM,CAAC,eAChBjE,OAAA,CAACtD,aAAa;MAAAoJ,QAAA,gBACZ9F,OAAA,CAAChE,MAAM;QAACuK,OAAO,EAAEd,iBAAkB;QAAAK,QAAA,EAAC;MAAO;QAAAhC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAQ,CAAC,eACpDjE,OAAA,CAAChE,MAAM;QACLuK,OAAO,EAAEf,oBAAqB;QAC9BW,OAAO,EAAC,WAAW;QACnB2F,QAAQ,EAAEtL,OAAQ;QAClB8F,SAAS,EAAE9F,OAAO,gBAAGR,OAAA,CAAC7D,gBAAgB;UAACqK,IAAI,EAAE;QAAG;UAAA1C,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,gBAAGjE,OAAA,CAAC9B,cAAc;UAAA4F,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAE;QAAA6B,QAAA,EAExEtF,OAAO,GAAG,gBAAgB,GAAG;MAAe;QAAAsD,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACvC,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACI,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACV,CACT;EAED,oBACEjE,OAAA,CAACvE,GAAG;IAACsQ,SAAS,EAAC,sCAAsC;IAAAjG,QAAA,gBAEnD9F,OAAA,CAACvE,GAAG;MAACkK,EAAE,EAAE;QAAEI,OAAO,EAAE,MAAM;QAAEC,cAAc,EAAE,UAAU;QAAEC,UAAU,EAAE,QAAQ;QAAEC,EAAE,EAAE;MAAE,CAAE;MAAAJ,QAAA,eACpF9F,OAAA,CAACZ,eAAe;QAAA0E,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAChB,CAAC,EAGLzD,OAAO,iBACNR,OAAA,CAACvE,GAAG;MAACkK,EAAE,EAAE;QAAEI,OAAO,EAAE,MAAM;QAAEC,cAAc,EAAE,QAAQ;QAAEgG,EAAE,EAAE;MAAE,CAAE;MAAAlG,QAAA,eAC5D9F,OAAA,CAAC7D,gBAAgB;QAAA2H,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACjB,CACN,eAGDjE,OAAA,CAACvE,GAAG;MAACkK,EAAE,EAAE;QAAEE,EAAE,EAAE;MAAE,CAAE;MAAAC,QAAA,gBAEjB9F,OAAA,CAACvE,GAAG;QAACkK,EAAE,EAAE;UAAEO,EAAE,EAAE;QAAE,CAAE;QAAAJ,QAAA,gBACjB9F,OAAA,CAACtE,UAAU;UAACyK,OAAO,EAAC,IAAI;UAACR,EAAE,EAAE;YAAEqB,UAAU,EAAE,GAAG;YAAE9C,KAAK,EAAE,SAAS;YAAEgC,EAAE,EAAE,CAAC;YAAE6C,SAAS,EAAE;UAAS,CAAE;UAAAjD,QAAA,EAAC;QAEhG;UAAAhC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAY,CAAC,eACbjE,OAAA,CAACpE,IAAI;UAAC4L,SAAS;UAACC,OAAO,EAAE,CAAE;UAAA3B,QAAA,gBAEzB9F,OAAA,CAACpE,IAAI;YAAC8L,IAAI;YAACC,EAAE,EAAE,CAAE;YAACC,EAAE,EAAE,CAAE;YAACC,EAAE,EAAE,CAAE;YAAA/B,QAAA,eAC7B9F,OAAA,CAACnE,IAAI;cACHkQ,SAAS,EAAE,eAAe3K,kBAAkB,KAAK,UAAU,GAAG,sBAAsB,GAAG,EAAE,EAAG;cAC5FuE,EAAE,EAAE;gBACFkD,MAAM,EAAE,OAAO;gBACfoD,MAAM,EAAE,SAAS;gBACjBlF,MAAM,EAAE3F,kBAAkB,KAAK,UAAU,GAAG,mBAAmB,GAAG,mBAAmB;gBACrFyF,OAAO,EAAEzF,kBAAkB,KAAK,UAAU,GAAG,SAAS,GAAG,OAAO;gBAChEiI,UAAU,EAAE;cACd,CAAE;cACF9C,OAAO,EAAEA,CAAA,KAAMlF,qBAAqB,CAAC,UAAU,CAAE;cAAAyE,QAAA,eAEjD9F,OAAA,CAAClE,WAAW;gBAAC6J,EAAE,EAAE;kBAAEC,CAAC,EAAE,CAAC;kBAAEmD,SAAS,EAAE,QAAQ;kBAAEF,MAAM,EAAE,MAAM;kBAAE9C,OAAO,EAAE,MAAM;kBAAEmG,aAAa,EAAE,QAAQ;kBAAElG,cAAc,EAAE;gBAAS,CAAE;gBAAAF,QAAA,gBACjI9F,OAAA,CAAC1C,cAAc;kBAACqI,EAAE,EAAE;oBAAEmD,QAAQ,EAAE,EAAE;oBAAE5E,KAAK,EAAE,SAAS;oBAAEgC,EAAE,EAAE;kBAAE;gBAAE;kBAAApC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAC,eACjEjE,OAAA,CAACtE,UAAU;kBAACyK,OAAO,EAAC,WAAW;kBAACR,EAAE,EAAE;oBAAEqB,UAAU,EAAE,GAAG;oBAAEd,EAAE,EAAE,GAAG;oBAAE4C,QAAQ,EAAE;kBAAS,CAAE;kBAAAhD,QAAA,EAAC;gBAEtF;kBAAAhC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAY,CAAC,eACbjE,OAAA,CAACtE,UAAU;kBAACyK,OAAO,EAAC,OAAO;kBAACR,EAAE,EAAE;oBAAEzB,KAAK,EAAE,MAAM;oBAAE4E,QAAQ,EAAE;kBAAS,CAAE;kBAAAhD,QAAA,EAAC;gBAEvE;kBAAAhC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAY,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACF;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACV;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC,eAGPjE,OAAA,CAACpE,IAAI;YAAC8L,IAAI;YAACC,EAAE,EAAE,CAAE;YAACC,EAAE,EAAE,CAAE;YAACC,EAAE,EAAE,CAAE;YAAA/B,QAAA,eAC7B9F,OAAA,CAACnE,IAAI;cACH8J,EAAE,EAAE;gBACFkD,MAAM,EAAE,OAAO;gBACfoD,MAAM,EAAE,SAAS;gBACjBlF,MAAM,EAAE3F,kBAAkB,KAAK,KAAK,GAAG,mBAAmB,GAAG,mBAAmB;gBAChFyF,OAAO,EAAEzF,kBAAkB,KAAK,KAAK,GAAG,SAAS,GAAG,OAAO;gBAC3DiI,UAAU,EAAE;cACd,CAAE;cACF9C,OAAO,EAAEA,CAAA,KAAMlF,qBAAqB,CAAC,KAAK,CAAE;cAAAyE,QAAA,eAE5C9F,OAAA,CAAClE,WAAW;gBAAC6J,EAAE,EAAE;kBAAEC,CAAC,EAAE,CAAC;kBAAEmD,SAAS,EAAE,QAAQ;kBAAEF,MAAM,EAAE,MAAM;kBAAE9C,OAAO,EAAE,MAAM;kBAAEmG,aAAa,EAAE,QAAQ;kBAAElG,cAAc,EAAE;gBAAS,CAAE;gBAAAF,QAAA,gBACjI9F,OAAA,CAAClC,QAAQ;kBAAC6H,EAAE,EAAE;oBAAEmD,QAAQ,EAAE,EAAE;oBAAE5E,KAAK,EAAE,SAAS;oBAAEgC,EAAE,EAAE;kBAAE;gBAAE;kBAAApC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAC,eAC3DjE,OAAA,CAACtE,UAAU;kBAACyK,OAAO,EAAC,WAAW;kBAACR,EAAE,EAAE;oBAAEqB,UAAU,EAAE,GAAG;oBAAEd,EAAE,EAAE,GAAG;oBAAE4C,QAAQ,EAAE;kBAAS,CAAE;kBAAAhD,QAAA,EAAC;gBAEtF;kBAAAhC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAY,CAAC,eACbjE,OAAA,CAACtE,UAAU;kBAACyK,OAAO,EAAC,OAAO;kBAACR,EAAE,EAAE;oBAAEzB,KAAK,EAAE,MAAM;oBAAE4E,QAAQ,EAAE;kBAAS,CAAE;kBAAAhD,QAAA,EAAC;gBAEvE;kBAAAhC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAY,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACF;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACV;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC,eAKPjE,OAAA,CAACpE,IAAI;YAAC8L,IAAI;YAACC,EAAE,EAAE,CAAE;YAACC,EAAE,EAAE,CAAE;YAACC,EAAE,EAAE,CAAE;YAAA/B,QAAA,eAC7B9F,OAAA,CAACnE,IAAI;cACH8J,EAAE,EAAE;gBACFkD,MAAM,EAAE,OAAO;gBACfoD,MAAM,EAAE,SAAS;gBACjBlF,MAAM,EAAE3F,kBAAkB,KAAK,cAAc,GAAG,mBAAmB,GAAG,mBAAmB;gBACzFyF,OAAO,EAAEzF,kBAAkB,KAAK,cAAc,GAAG,SAAS,GAAG,OAAO;gBACpEiI,UAAU,EAAE;cACd,CAAE;cACF9C,OAAO,EAAEA,CAAA,KAAMlF,qBAAqB,CAAC,cAAc,CAAE;cAAAyE,QAAA,eAErD9F,OAAA,CAAClE,WAAW;gBAAC6J,EAAE,EAAE;kBAAEC,CAAC,EAAE,CAAC;kBAAEmD,SAAS,EAAE,QAAQ;kBAAEF,MAAM,EAAE,MAAM;kBAAE9C,OAAO,EAAE,MAAM;kBAAEmG,aAAa,EAAE,QAAQ;kBAAElG,cAAc,EAAE;gBAAS,CAAE;gBAAAF,QAAA,gBACjI9F,OAAA,CAACpC,YAAY;kBAAC+H,EAAE,EAAE;oBAAEmD,QAAQ,EAAE,EAAE;oBAAE5E,KAAK,EAAE,SAAS;oBAAEgC,EAAE,EAAE;kBAAE;gBAAE;kBAAApC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAC,eAC/DjE,OAAA,CAACtE,UAAU;kBAACyK,OAAO,EAAC,WAAW;kBAACR,EAAE,EAAE;oBAAEqB,UAAU,EAAE,GAAG;oBAAEd,EAAE,EAAE,GAAG;oBAAE4C,QAAQ,EAAE;kBAAS,CAAE;kBAAAhD,QAAA,EAAC;gBAEtF;kBAAAhC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAY,CAAC,eACbjE,OAAA,CAACtE,UAAU;kBAACyK,OAAO,EAAC,OAAO;kBAACR,EAAE,EAAE;oBAAEzB,KAAK,EAAE,MAAM;oBAAE4E,QAAQ,EAAE;kBAAS,CAAE;kBAAAhD,QAAA,EAAC;gBAEvE;kBAAAhC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAY,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACF;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACV;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACJ,CAAC,eAGNjE,OAAA,CAACvE,GAAG;QAACkK,EAAE,EAAE;UAAEwG,SAAS,EAAE;QAAQ,CAAE;QAAArG,QAAA,GAE7B1E,kBAAkB,KAAK,UAAU,iBAChCpB,OAAA,CAACrE,KAAK;UAACgK,EAAE,EAAE;YAAEC,CAAC,EAAE;UAAE,CAAE;UAAAE,QAAA,EACjBlE,WAAW,CAACE,QAAQ,gBACnB9B,OAAA,CAACvE,GAAG;YAAAqK,QAAA,gBACF9F,OAAA,CAACvE,GAAG;cAACkK,EAAE,EAAE;gBAAEI,OAAO,EAAE,MAAM;gBAAEC,cAAc,EAAE,UAAU;gBAAEE,EAAE,EAAE;cAAE,CAAE;cAAAJ,QAAA,gBAC9D9F,OAAA,CAAChE,MAAM;gBACLsK,SAAS,eAAEtG,OAAA,CAAChC,YAAY;kBAAA8F,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAE;gBAC5BsC,OAAO,EAAEA,CAAA,KAAMnC,wBAAwB,CAAC,UAAU,EAAE,KAAK,CAAE;gBAC3D+B,OAAO,EAAC,UAAU;gBAClBK,IAAI,EAAC,OAAO;gBACZtC,KAAK,EAAC,SAAS;gBACfyB,EAAE,EAAE;kBAAE4B,EAAE,EAAE;gBAAE,CAAE;gBAAAzB,QAAA,EACf;cAED;gBAAAhC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC,eACTjE,OAAA,CAAChE,MAAM;gBACLsK,SAAS,eAAEtG,OAAA,CAAChC,YAAY;kBAAA8F,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAE;gBAC5BsC,OAAO,EAAEA,CAAA,KAAMnC,wBAAwB,CAAC,UAAU,EAAE,OAAO,CAAE;gBAC7D+B,OAAO,EAAC,UAAU;gBAClBK,IAAI,EAAC,OAAO;gBACZtC,KAAK,EAAC,SAAS;gBAAA4B,QAAA,EAChB;cAED;gBAAAhC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACN,CAAC,EACLwC,oBAAoB,CAAC7E,WAAW,CAACE,QAAQ,CAAC;UAAA;YAAAgC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACxC,CAAC,GACJzD,OAAO,gBACTR,OAAA,CAACT,UAAU;YACToM,IAAI,EAAC,SAAS;YACdtH,UAAU,EAAC,UAAU;YACrBV,KAAK,EAAC,mCAAmC;YACzCC,WAAW,EAAC;UAAsD;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACnE,CAAC,gBAEFjE,OAAA,CAACT,UAAU;YACToM,IAAI,EAAC,OAAO;YACZtH,UAAU,EAAC,UAAU;YACrBV,KAAK,EAAC,wBAAwB;YAC9BC,WAAW,EAAC,mFAAmF;YAC/FwI,OAAO,EAAEA,CAAA,KAAM;cACb3L,UAAU,CAAC,IAAI,CAAC;cAChBpB,aAAa,CAACmD,iBAAiB,CAAClC,UAAU,EAAE,OAAO,CAAC,CACjD+L,IAAI,CAACzF,IAAI,IAAI;gBACZ/E,cAAc,CAAC6C,IAAI,KAAK;kBACtB,GAAGA,IAAI;kBACP5C,QAAQ,EAAE8E,IAAI,CAAChE;gBACjB,CAAC,CAAC,CAAC;cACL,CAAC,CAAC,CACDH,KAAK,CAACC,GAAG,IAAI;gBACZC,OAAO,CAACjC,KAAK,CAAC,iCAAiC,EAAEgC,GAAG,CAAC;cACvD,CAAC,CAAC,CACD4J,OAAO,CAAC,MAAM;gBACb7L,UAAU,CAAC,KAAK,CAAC;cACnB,CAAC,CAAC;YACN,CAAE;YACFD,OAAO,EAAEA;UAAQ;YAAAsD,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAClB;QACF;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACI,CACR,EAGA7C,kBAAkB,KAAK,KAAK,iBAC3BpB,OAAA,CAACrE,KAAK;UAACgK,EAAE,EAAE;YAAEC,CAAC,EAAE;UAAE,CAAE;UAAAE,QAAA,EACjBlE,WAAW,CAACG,GAAG,gBACd/B,OAAA,CAACvE,GAAG;YAAAqK,QAAA,gBACF9F,OAAA,CAACvE,GAAG;cAACkK,EAAE,EAAE;gBAAEI,OAAO,EAAE,MAAM;gBAAEC,cAAc,EAAE,UAAU;gBAAEE,EAAE,EAAE;cAAE,CAAE;cAAAJ,QAAA,gBAC9D9F,OAAA,CAAChE,MAAM;gBACLsK,SAAS,eAAEtG,OAAA,CAAChC,YAAY;kBAAA8F,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAE;gBAC5BsC,OAAO,EAAEA,CAAA,KAAMnC,wBAAwB,CAAC,KAAK,EAAE,KAAK,CAAE;gBACtD+B,OAAO,EAAC,UAAU;gBAClBK,IAAI,EAAC,OAAO;gBACZtC,KAAK,EAAC,SAAS;gBACfyB,EAAE,EAAE;kBAAE4B,EAAE,EAAE;gBAAE,CAAE;gBAAAzB,QAAA,EACf;cAED;gBAAAhC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC,eACTjE,OAAA,CAAChE,MAAM;gBACLsK,SAAS,eAAEtG,OAAA,CAAChC,YAAY;kBAAA8F,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAE;gBAC5BsC,OAAO,EAAEA,CAAA,KAAMnC,wBAAwB,CAAC,KAAK,EAAE,OAAO,CAAE;gBACxD+B,OAAO,EAAC,UAAU;gBAClBK,IAAI,EAAC,OAAO;gBACZtC,KAAK,EAAC,SAAS;gBAAA4B,QAAA,EAChB;cAED;gBAAAhC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACN,CAAC,EACLyC,eAAe,CAAC9E,WAAW,CAACG,GAAG,CAAC;UAAA;YAAA+B,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC9B,CAAC,GACJzD,OAAO,gBACTR,OAAA,CAACT,UAAU;YACToM,IAAI,EAAC,SAAS;YACdtH,UAAU,EAAC,KAAK;YAChBV,KAAK,EAAC,mCAAmC;YACzCC,WAAW,EAAC;UAAyC;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACtD,CAAC,gBAEFjE,OAAA,CAACT,UAAU;YACToM,IAAI,EAAC,OAAO;YACZtH,UAAU,EAAC,KAAK;YAChBV,KAAK,EAAC,wBAAwB;YAC9BC,WAAW,EAAC,gFAAgF;YAC5FwI,OAAO,EAAEA,CAAA,KAAM;cACb3L,UAAU,CAAC,IAAI,CAAC;cAChBpB,aAAa,CAACyD,mBAAmB,CAACxC,UAAU,EAAE,OAAO,CAAC,CACnD+L,IAAI,CAACzF,IAAI,IAAI;gBACZ/E,cAAc,CAAC6C,IAAI,KAAK;kBACtB,GAAGA,IAAI;kBACP3C,GAAG,EAAE6E,IAAI,CAAChE;gBACZ,CAAC,CAAC,CAAC;cACL,CAAC,CAAC,CACDH,KAAK,CAACC,GAAG,IAAI;gBACZC,OAAO,CAACjC,KAAK,CAAC,4BAA4B,EAAEgC,GAAG,CAAC;cAClD,CAAC,CAAC,CACD4J,OAAO,CAAC,MAAM;gBACb7L,UAAU,CAAC,KAAK,CAAC;cACnB,CAAC,CAAC;YACN,CAAE;YACFD,OAAO,EAAEA;UAAQ;YAAAsD,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAClB;QACF;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACI,CACR,EAKA7C,kBAAkB,KAAK,cAAc,iBACpCpB,OAAA,CAACrE,KAAK;UAACgK,EAAE,EAAE;YAAEC,CAAC,EAAE;UAAE,CAAE;UAAAE,QAAA,EACjBlE,WAAW,CAACO,WAAW,gBACtBnC,OAAA,CAACvE,GAAG;YAAAqK,QAAA,gBACF9F,OAAA,CAACvE,GAAG;cAACkK,EAAE,EAAE;gBAAEI,OAAO,EAAE,MAAM;gBAAEC,cAAc,EAAE,UAAU;gBAAEE,EAAE,EAAE;cAAE,CAAE;cAAAJ,QAAA,gBAC9D9F,OAAA,CAAChE,MAAM;gBACLsK,SAAS,eAAEtG,OAAA,CAAChC,YAAY;kBAAA8F,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAE;gBAC5BsC,OAAO,EAAEA,CAAA,KAAMnC,wBAAwB,CAAC,cAAc,EAAE,KAAK,CAAE;gBAC/D+B,OAAO,EAAC,UAAU;gBAClBK,IAAI,EAAC,OAAO;gBACZtC,KAAK,EAAC,SAAS;gBACfyB,EAAE,EAAE;kBAAE4B,EAAE,EAAE;gBAAE,CAAE;gBAAAzB,QAAA,EACf;cAED;gBAAAhC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC,eACTjE,OAAA,CAAChE,MAAM;gBACLsK,SAAS,eAAEtG,OAAA,CAAChC,YAAY;kBAAA8F,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAE;gBAC5BsC,OAAO,EAAEA,CAAA,KAAMnC,wBAAwB,CAAC,cAAc,EAAE,OAAO,CAAE;gBACjE+B,OAAO,EAAC,UAAU;gBAClBK,IAAI,EAAC,OAAO;gBACZtC,KAAK,EAAC,SAAS;gBAAA4B,QAAA,EAChB;cAED;gBAAAhC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACN,CAAC,EACL0C,uBAAuB,CAAC/E,WAAW,CAACO,WAAW,CAAC;UAAA;YAAA2B,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC9C,CAAC,gBAENjE,OAAA,CAACT,UAAU;YACToM,IAAI,EAAC,iBAAiB;YACtBtH,UAAU,EAAC,cAAc;YACzBV,KAAK,EAAC,sBAAsB;YAC5BC,WAAW,EAAC,8GAA2G;YACvH2I,WAAW,EAAC,mBAAmB;YAC/BC,QAAQ,EAAEA,CAAA,KAAM;cACdrL,aAAa,CAAC,cAAc,CAAC;cAC7B;cACA,MAAM8D,KAAK,GAAG,IAAIC,IAAI,CAAC,CAAC;cACxB,MAAMC,SAAS,GAAG,IAAID,IAAI,CAAC,CAAC;cAC5BC,SAAS,CAACC,QAAQ,CAACH,KAAK,CAACI,QAAQ,CAAC,CAAC,GAAG,CAAC,CAAC;cAExC9D,WAAW,CAAC;gBACV,GAAGD,QAAQ;gBACXG,WAAW,EAAE0D,SAAS,CAACG,WAAW,CAAC,CAAC,CAACC,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC;gBAClD7D,SAAS,EAAEuD,KAAK,CAACK,WAAW,CAAC,CAAC,CAACC,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC;cAC7C,CAAC,CAAC;cACFtE,aAAa,CAAC,IAAI,CAAC;YACrB;UAAE;YAAA6C,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH;QACF;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACI,CACR;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACE,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,EAGLmH,YAAY,CAAC,CAAC;EAAA;IAAAtH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACZ,CAAC;AAEV,CAAC;AAAC7D,EAAA,CA7kCID,iBAAiB;EAAA,QACJlB,WAAW,EACLC,SAAS,EACfC,OAAO;AAAA;AAAAsN,EAAA,GAHpBtM,iBAAiB;AA+kCvB,eAAeA,iBAAiB;AAAC,IAAAsM,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}