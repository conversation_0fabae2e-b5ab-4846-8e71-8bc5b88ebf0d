{"ast": null, "code": "const formatRelativeLocale = {\n  lastWeek: \"eeee 'trecută la' p\",\n  yesterday: \"'ieri la' p\",\n  today: \"'astăzi la' p\",\n  tomorrow: \"'mâine la' p\",\n  nextWeek: \"eeee 'viitoare la' p\",\n  other: \"P\"\n};\nexport const formatRelative = (token, _date, _baseDate, _options) => formatRelativeLocale[token];", "map": {"version": 3, "names": ["formatRelativeLocale", "lastWeek", "yesterday", "today", "tomorrow", "nextWeek", "other", "formatRelative", "token", "_date", "_baseDate", "_options"], "sources": ["C:/CMS/webapp/frontend/node_modules/date-fns/locale/ro/_lib/formatRelative.js"], "sourcesContent": ["const formatRelativeLocale = {\n  lastWeek: \"eeee 'trecută la' p\",\n  yesterday: \"'ieri la' p\",\n  today: \"'astăzi la' p\",\n  tomorrow: \"'mâine la' p\",\n  nextWeek: \"eeee 'viitoare la' p\",\n  other: \"P\",\n};\n\nexport const formatRelative = (token, _date, _baseDate, _options) =>\n  formatRelativeLocale[token];\n"], "mappings": "AAAA,MAAMA,oBAAoB,GAAG;EAC3BC,QAAQ,EAAE,qBAAqB;EAC/BC,SAAS,EAAE,aAAa;EACxBC,KAAK,EAAE,eAAe;EACtBC,QAAQ,EAAE,cAAc;EACxBC,QAAQ,EAAE,sBAAsB;EAChCC,KAAK,EAAE;AACT,CAAC;AAED,OAAO,MAAMC,cAAc,GAAGA,CAACC,KAAK,EAAEC,KAAK,EAAEC,SAAS,EAAEC,QAAQ,KAC9DX,oBAAoB,CAACQ,KAAK,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}