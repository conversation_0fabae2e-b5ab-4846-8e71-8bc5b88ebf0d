{"ast": null, "code": "function _typeof(o) {\n  \"@babel/helpers - typeof\";\n\n  return _typeof = \"function\" == typeof Symbol && \"symbol\" == typeof Symbol.iterator ? function (o) {\n    return typeof o;\n  } : function (o) {\n    return o && \"function\" == typeof Symbol && o.constructor === Symbol && o !== Symbol.prototype ? \"symbol\" : typeof o;\n  }, _typeof(o);\n}\nfunction ownKeys(e, r) {\n  var t = Object.keys(e);\n  if (Object.getOwnPropertySymbols) {\n    var o = Object.getOwnPropertySymbols(e);\n    r && (o = o.filter(function (r) {\n      return Object.getOwnPropertyDescriptor(e, r).enumerable;\n    })), t.push.apply(t, o);\n  }\n  return t;\n}\nfunction _objectSpread(e) {\n  for (var r = 1; r < arguments.length; r++) {\n    var t = null != arguments[r] ? arguments[r] : {};\n    r % 2 ? ownKeys(Object(t), !0).forEach(function (r) {\n      _defineProperty(e, r, t[r]);\n    }) : Object.getOwnPropertyDescriptors ? Object.defineProperties(e, Object.getOwnPropertyDescriptors(t)) : ownKeys(Object(t)).forEach(function (r) {\n      Object.defineProperty(e, r, Object.getOwnPropertyDescriptor(t, r));\n    });\n  }\n  return e;\n}\nfunction _defineProperty(obj, key, value) {\n  key = _toPropertyKey(key);\n  if (key in obj) {\n    Object.defineProperty(obj, key, {\n      value: value,\n      enumerable: true,\n      configurable: true,\n      writable: true\n    });\n  } else {\n    obj[key] = value;\n  }\n  return obj;\n}\nfunction _toPropertyKey(t) {\n  var i = _toPrimitive(t, \"string\");\n  return \"symbol\" == _typeof(i) ? i : i + \"\";\n}\nfunction _toPrimitive(t, r) {\n  if (\"object\" != _typeof(t) || !t) return t;\n  var e = t[Symbol.toPrimitive];\n  if (void 0 !== e) {\n    var i = e.call(t, r || \"default\");\n    if (\"object\" != _typeof(i)) return i;\n    throw new TypeError(\"@@toPrimitive must return a primitive value.\");\n  }\n  return (\"string\" === r ? String : Number)(t);\n}\nimport { Legend } from '../component/Legend';\nimport { getMainColorOfGraphicItem } from './ChartUtils';\nimport { findChildByType } from './ReactUtils';\nexport var getLegendProps = function getLegendProps(_ref) {\n  var children = _ref.children,\n    formattedGraphicalItems = _ref.formattedGraphicalItems,\n    legendWidth = _ref.legendWidth,\n    legendContent = _ref.legendContent;\n  var legendItem = findChildByType(children, Legend);\n  if (!legendItem) {\n    return null;\n  }\n  var legendDefaultProps = Legend.defaultProps;\n  var legendProps = legendDefaultProps !== undefined ? _objectSpread(_objectSpread({}, legendDefaultProps), legendItem.props) : {};\n  var legendData;\n  if (legendItem.props && legendItem.props.payload) {\n    legendData = legendItem.props && legendItem.props.payload;\n  } else if (legendContent === 'children') {\n    legendData = (formattedGraphicalItems || []).reduce(function (result, _ref2) {\n      var item = _ref2.item,\n        props = _ref2.props;\n      var data = props.sectors || props.data || [];\n      return result.concat(data.map(function (entry) {\n        return {\n          type: legendItem.props.iconType || item.props.legendType,\n          value: entry.name,\n          color: entry.fill,\n          payload: entry\n        };\n      }));\n    }, []);\n  } else {\n    legendData = (formattedGraphicalItems || []).map(function (_ref3) {\n      var item = _ref3.item;\n      var itemDefaultProps = item.type.defaultProps;\n      var itemProps = itemDefaultProps !== undefined ? _objectSpread(_objectSpread({}, itemDefaultProps), item.props) : {};\n      var dataKey = itemProps.dataKey,\n        name = itemProps.name,\n        legendType = itemProps.legendType,\n        hide = itemProps.hide;\n      return {\n        inactive: hide,\n        dataKey: dataKey,\n        type: legendProps.iconType || legendType || 'square',\n        color: getMainColorOfGraphicItem(item),\n        value: name || dataKey,\n        // @ts-expect-error property strokeDasharray is required in Payload but optional in props\n        payload: itemProps\n      };\n    });\n  }\n  return _objectSpread(_objectSpread(_objectSpread({}, legendProps), Legend.getWithHeight(legendItem, legendWidth)), {}, {\n    payload: legendData,\n    item: legendItem\n  });\n};", "map": {"version": 3, "names": ["_typeof", "o", "Symbol", "iterator", "constructor", "prototype", "ownKeys", "e", "r", "t", "Object", "keys", "getOwnPropertySymbols", "filter", "getOwnPropertyDescriptor", "enumerable", "push", "apply", "_objectSpread", "arguments", "length", "for<PERSON>ach", "_defineProperty", "getOwnPropertyDescriptors", "defineProperties", "defineProperty", "obj", "key", "value", "_to<PERSON><PERSON><PERSON><PERSON><PERSON>", "configurable", "writable", "i", "_toPrimitive", "toPrimitive", "call", "TypeError", "String", "Number", "Legend", "getMainColorOfGraphicItem", "findChildByType", "getLegendProps", "_ref", "children", "formattedGraphicalItems", "legend<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "legendItem", "legendDefaultProps", "defaultProps", "legendProps", "undefined", "props", "legendData", "payload", "reduce", "result", "_ref2", "item", "data", "sectors", "concat", "map", "entry", "type", "iconType", "legendType", "name", "color", "fill", "_ref3", "itemDefaultProps", "itemProps", "dataKey", "hide", "inactive", "getWithHeight"], "sources": ["C:/CMS/webapp/frontend/node_modules/recharts/es6/util/getLegendProps.js"], "sourcesContent": ["function _typeof(o) { \"@babel/helpers - typeof\"; return _typeof = \"function\" == typeof Symbol && \"symbol\" == typeof Symbol.iterator ? function (o) { return typeof o; } : function (o) { return o && \"function\" == typeof Symbol && o.constructor === Symbol && o !== Symbol.prototype ? \"symbol\" : typeof o; }, _typeof(o); }\nfunction ownKeys(e, r) { var t = Object.keys(e); if (Object.getOwnPropertySymbols) { var o = Object.getOwnPropertySymbols(e); r && (o = o.filter(function (r) { return Object.getOwnPropertyDescriptor(e, r).enumerable; })), t.push.apply(t, o); } return t; }\nfunction _objectSpread(e) { for (var r = 1; r < arguments.length; r++) { var t = null != arguments[r] ? arguments[r] : {}; r % 2 ? ownKeys(Object(t), !0).forEach(function (r) { _defineProperty(e, r, t[r]); }) : Object.getOwnPropertyDescriptors ? Object.defineProperties(e, Object.getOwnPropertyDescriptors(t)) : ownKeys(Object(t)).forEach(function (r) { Object.defineProperty(e, r, Object.getOwnPropertyDescriptor(t, r)); }); } return e; }\nfunction _defineProperty(obj, key, value) { key = _toPropertyKey(key); if (key in obj) { Object.defineProperty(obj, key, { value: value, enumerable: true, configurable: true, writable: true }); } else { obj[key] = value; } return obj; }\nfunction _toPropertyKey(t) { var i = _toPrimitive(t, \"string\"); return \"symbol\" == _typeof(i) ? i : i + \"\"; }\nfunction _toPrimitive(t, r) { if (\"object\" != _typeof(t) || !t) return t; var e = t[Symbol.toPrimitive]; if (void 0 !== e) { var i = e.call(t, r || \"default\"); if (\"object\" != _typeof(i)) return i; throw new TypeError(\"@@toPrimitive must return a primitive value.\"); } return (\"string\" === r ? String : Number)(t); }\nimport { Legend } from '../component/Legend';\nimport { getMainColorOfGraphicItem } from './ChartUtils';\nimport { findChildByType } from './ReactUtils';\nexport var getLegendProps = function getLegendProps(_ref) {\n  var children = _ref.children,\n    formattedGraphicalItems = _ref.formattedGraphicalItems,\n    legendWidth = _ref.legendWidth,\n    legendContent = _ref.legendContent;\n  var legendItem = findChildByType(children, Legend);\n  if (!legendItem) {\n    return null;\n  }\n  var legendDefaultProps = Legend.defaultProps;\n  var legendProps = legendDefaultProps !== undefined ? _objectSpread(_objectSpread({}, legendDefaultProps), legendItem.props) : {};\n  var legendData;\n  if (legendItem.props && legendItem.props.payload) {\n    legendData = legendItem.props && legendItem.props.payload;\n  } else if (legendContent === 'children') {\n    legendData = (formattedGraphicalItems || []).reduce(function (result, _ref2) {\n      var item = _ref2.item,\n        props = _ref2.props;\n      var data = props.sectors || props.data || [];\n      return result.concat(data.map(function (entry) {\n        return {\n          type: legendItem.props.iconType || item.props.legendType,\n          value: entry.name,\n          color: entry.fill,\n          payload: entry\n        };\n      }));\n    }, []);\n  } else {\n    legendData = (formattedGraphicalItems || []).map(function (_ref3) {\n      var item = _ref3.item;\n      var itemDefaultProps = item.type.defaultProps;\n      var itemProps = itemDefaultProps !== undefined ? _objectSpread(_objectSpread({}, itemDefaultProps), item.props) : {};\n      var dataKey = itemProps.dataKey,\n        name = itemProps.name,\n        legendType = itemProps.legendType,\n        hide = itemProps.hide;\n      return {\n        inactive: hide,\n        dataKey: dataKey,\n        type: legendProps.iconType || legendType || 'square',\n        color: getMainColorOfGraphicItem(item),\n        value: name || dataKey,\n        // @ts-expect-error property strokeDasharray is required in Payload but optional in props\n        payload: itemProps\n      };\n    });\n  }\n  return _objectSpread(_objectSpread(_objectSpread({}, legendProps), Legend.getWithHeight(legendItem, legendWidth)), {}, {\n    payload: legendData,\n    item: legendItem\n  });\n};"], "mappings": "AAAA,SAASA,OAAOA,CAACC,CAAC,EAAE;EAAE,yBAAyB;;EAAE,OAAOD,OAAO,GAAG,UAAU,IAAI,OAAOE,MAAM,IAAI,QAAQ,IAAI,OAAOA,MAAM,CAACC,QAAQ,GAAG,UAAUF,CAAC,EAAE;IAAE,OAAO,OAAOA,CAAC;EAAE,CAAC,GAAG,UAAUA,CAAC,EAAE;IAAE,OAAOA,CAAC,IAAI,UAAU,IAAI,OAAOC,MAAM,IAAID,CAAC,CAACG,WAAW,KAAKF,MAAM,IAAID,CAAC,KAAKC,MAAM,CAACG,SAAS,GAAG,QAAQ,GAAG,OAAOJ,CAAC;EAAE,CAAC,EAAED,OAAO,CAACC,CAAC,CAAC;AAAE;AAC7T,SAASK,OAAOA,CAACC,CAAC,EAAEC,CAAC,EAAE;EAAE,IAAIC,CAAC,GAAGC,MAAM,CAACC,IAAI,CAACJ,CAAC,CAAC;EAAE,IAAIG,MAAM,CAACE,qBAAqB,EAAE;IAAE,IAAIX,CAAC,GAAGS,MAAM,CAACE,qBAAqB,CAACL,CAAC,CAAC;IAAEC,CAAC,KAAKP,CAAC,GAAGA,CAAC,CAACY,MAAM,CAAC,UAAUL,CAAC,EAAE;MAAE,OAAOE,MAAM,CAACI,wBAAwB,CAACP,CAAC,EAAEC,CAAC,CAAC,CAACO,UAAU;IAAE,CAAC,CAAC,CAAC,EAAEN,CAAC,CAACO,IAAI,CAACC,KAAK,CAACR,CAAC,EAAER,CAAC,CAAC;EAAE;EAAE,OAAOQ,CAAC;AAAE;AAC9P,SAASS,aAAaA,CAACX,CAAC,EAAE;EAAE,KAAK,IAAIC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGW,SAAS,CAACC,MAAM,EAAEZ,CAAC,EAAE,EAAE;IAAE,IAAIC,CAAC,GAAG,IAAI,IAAIU,SAAS,CAACX,CAAC,CAAC,GAAGW,SAAS,CAACX,CAAC,CAAC,GAAG,CAAC,CAAC;IAAEA,CAAC,GAAG,CAAC,GAAGF,OAAO,CAACI,MAAM,CAACD,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAACY,OAAO,CAAC,UAAUb,CAAC,EAAE;MAAEc,eAAe,CAACf,CAAC,EAAEC,CAAC,EAAEC,CAAC,CAACD,CAAC,CAAC,CAAC;IAAE,CAAC,CAAC,GAAGE,MAAM,CAACa,yBAAyB,GAAGb,MAAM,CAACc,gBAAgB,CAACjB,CAAC,EAAEG,MAAM,CAACa,yBAAyB,CAACd,CAAC,CAAC,CAAC,GAAGH,OAAO,CAACI,MAAM,CAACD,CAAC,CAAC,CAAC,CAACY,OAAO,CAAC,UAAUb,CAAC,EAAE;MAAEE,MAAM,CAACe,cAAc,CAAClB,CAAC,EAAEC,CAAC,EAAEE,MAAM,CAACI,wBAAwB,CAACL,CAAC,EAAED,CAAC,CAAC,CAAC;IAAE,CAAC,CAAC;EAAE;EAAE,OAAOD,CAAC;AAAE;AACtb,SAASe,eAAeA,CAACI,GAAG,EAAEC,GAAG,EAAEC,KAAK,EAAE;EAAED,GAAG,GAAGE,cAAc,CAACF,GAAG,CAAC;EAAE,IAAIA,GAAG,IAAID,GAAG,EAAE;IAAEhB,MAAM,CAACe,cAAc,CAACC,GAAG,EAAEC,GAAG,EAAE;MAAEC,KAAK,EAAEA,KAAK;MAAEb,UAAU,EAAE,IAAI;MAAEe,YAAY,EAAE,IAAI;MAAEC,QAAQ,EAAE;IAAK,CAAC,CAAC;EAAE,CAAC,MAAM;IAAEL,GAAG,CAACC,GAAG,CAAC,GAAGC,KAAK;EAAE;EAAE,OAAOF,GAAG;AAAE;AAC3O,SAASG,cAAcA,CAACpB,CAAC,EAAE;EAAE,IAAIuB,CAAC,GAAGC,YAAY,CAACxB,CAAC,EAAE,QAAQ,CAAC;EAAE,OAAO,QAAQ,IAAIT,OAAO,CAACgC,CAAC,CAAC,GAAGA,CAAC,GAAGA,CAAC,GAAG,EAAE;AAAE;AAC5G,SAASC,YAAYA,CAACxB,CAAC,EAAED,CAAC,EAAE;EAAE,IAAI,QAAQ,IAAIR,OAAO,CAACS,CAAC,CAAC,IAAI,CAACA,CAAC,EAAE,OAAOA,CAAC;EAAE,IAAIF,CAAC,GAAGE,CAAC,CAACP,MAAM,CAACgC,WAAW,CAAC;EAAE,IAAI,KAAK,CAAC,KAAK3B,CAAC,EAAE;IAAE,IAAIyB,CAAC,GAAGzB,CAAC,CAAC4B,IAAI,CAAC1B,CAAC,EAAED,CAAC,IAAI,SAAS,CAAC;IAAE,IAAI,QAAQ,IAAIR,OAAO,CAACgC,CAAC,CAAC,EAAE,OAAOA,CAAC;IAAE,MAAM,IAAII,SAAS,CAAC,8CAA8C,CAAC;EAAE;EAAE,OAAO,CAAC,QAAQ,KAAK5B,CAAC,GAAG6B,MAAM,GAAGC,MAAM,EAAE7B,CAAC,CAAC;AAAE;AAC3T,SAAS8B,MAAM,QAAQ,qBAAqB;AAC5C,SAASC,yBAAyB,QAAQ,cAAc;AACxD,SAASC,eAAe,QAAQ,cAAc;AAC9C,OAAO,IAAIC,cAAc,GAAG,SAASA,cAAcA,CAACC,IAAI,EAAE;EACxD,IAAIC,QAAQ,GAAGD,IAAI,CAACC,QAAQ;IAC1BC,uBAAuB,GAAGF,IAAI,CAACE,uBAAuB;IACtDC,WAAW,GAAGH,IAAI,CAACG,WAAW;IAC9BC,aAAa,GAAGJ,IAAI,CAACI,aAAa;EACpC,IAAIC,UAAU,GAAGP,eAAe,CAACG,QAAQ,EAAEL,MAAM,CAAC;EAClD,IAAI,CAACS,UAAU,EAAE;IACf,OAAO,IAAI;EACb;EACA,IAAIC,kBAAkB,GAAGV,MAAM,CAACW,YAAY;EAC5C,IAAIC,WAAW,GAAGF,kBAAkB,KAAKG,SAAS,GAAGlC,aAAa,CAACA,aAAa,CAAC,CAAC,CAAC,EAAE+B,kBAAkB,CAAC,EAAED,UAAU,CAACK,KAAK,CAAC,GAAG,CAAC,CAAC;EAChI,IAAIC,UAAU;EACd,IAAIN,UAAU,CAACK,KAAK,IAAIL,UAAU,CAACK,KAAK,CAACE,OAAO,EAAE;IAChDD,UAAU,GAAGN,UAAU,CAACK,KAAK,IAAIL,UAAU,CAACK,KAAK,CAACE,OAAO;EAC3D,CAAC,MAAM,IAAIR,aAAa,KAAK,UAAU,EAAE;IACvCO,UAAU,GAAG,CAACT,uBAAuB,IAAI,EAAE,EAAEW,MAAM,CAAC,UAAUC,MAAM,EAAEC,KAAK,EAAE;MAC3E,IAAIC,IAAI,GAAGD,KAAK,CAACC,IAAI;QACnBN,KAAK,GAAGK,KAAK,CAACL,KAAK;MACrB,IAAIO,IAAI,GAAGP,KAAK,CAACQ,OAAO,IAAIR,KAAK,CAACO,IAAI,IAAI,EAAE;MAC5C,OAAOH,MAAM,CAACK,MAAM,CAACF,IAAI,CAACG,GAAG,CAAC,UAAUC,KAAK,EAAE;QAC7C,OAAO;UACLC,IAAI,EAAEjB,UAAU,CAACK,KAAK,CAACa,QAAQ,IAAIP,IAAI,CAACN,KAAK,CAACc,UAAU;UACxDvC,KAAK,EAAEoC,KAAK,CAACI,IAAI;UACjBC,KAAK,EAAEL,KAAK,CAACM,IAAI;UACjBf,OAAO,EAAES;QACX,CAAC;MACH,CAAC,CAAC,CAAC;IACL,CAAC,EAAE,EAAE,CAAC;EACR,CAAC,MAAM;IACLV,UAAU,GAAG,CAACT,uBAAuB,IAAI,EAAE,EAAEkB,GAAG,CAAC,UAAUQ,KAAK,EAAE;MAChE,IAAIZ,IAAI,GAAGY,KAAK,CAACZ,IAAI;MACrB,IAAIa,gBAAgB,GAAGb,IAAI,CAACM,IAAI,CAACf,YAAY;MAC7C,IAAIuB,SAAS,GAAGD,gBAAgB,KAAKpB,SAAS,GAAGlC,aAAa,CAACA,aAAa,CAAC,CAAC,CAAC,EAAEsD,gBAAgB,CAAC,EAAEb,IAAI,CAACN,KAAK,CAAC,GAAG,CAAC,CAAC;MACpH,IAAIqB,OAAO,GAAGD,SAAS,CAACC,OAAO;QAC7BN,IAAI,GAAGK,SAAS,CAACL,IAAI;QACrBD,UAAU,GAAGM,SAAS,CAACN,UAAU;QACjCQ,IAAI,GAAGF,SAAS,CAACE,IAAI;MACvB,OAAO;QACLC,QAAQ,EAAED,IAAI;QACdD,OAAO,EAAEA,OAAO;QAChBT,IAAI,EAAEd,WAAW,CAACe,QAAQ,IAAIC,UAAU,IAAI,QAAQ;QACpDE,KAAK,EAAE7B,yBAAyB,CAACmB,IAAI,CAAC;QACtC/B,KAAK,EAAEwC,IAAI,IAAIM,OAAO;QACtB;QACAnB,OAAO,EAAEkB;MACX,CAAC;IACH,CAAC,CAAC;EACJ;EACA,OAAOvD,aAAa,CAACA,aAAa,CAACA,aAAa,CAAC,CAAC,CAAC,EAAEiC,WAAW,CAAC,EAAEZ,MAAM,CAACsC,aAAa,CAAC7B,UAAU,EAAEF,WAAW,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE;IACrHS,OAAO,EAAED,UAAU;IACnBK,IAAI,EAAEX;EACR,CAAC,CAAC;AACJ,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}