{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\PycharmProjects\\\\CMS\\\\webapp\\\\frontend\\\\src\\\\context\\\\AuthContext.js\",\n  _s = $RefreshSig$(),\n  _s2 = $RefreshSig$();\nimport React, { createContext, useState, useContext, useEffect } from 'react';\nimport { useNavigate } from 'react-router-dom';\nimport authService from '../services/authService';\n\n// Crea il contesto di autenticazione\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst AuthContext = /*#__PURE__*/createContext(null);\n\n// Hook personalizzato per utilizzare il contesto di autenticazione\nexport const useAuth = () => {\n  _s();\n  return useContext(AuthContext);\n};\n_s(useAuth, \"gDsCjeeItUuvgOWf1v4qoK9RF6k=\");\nexport const AuthProvider = ({\n  children\n}) => {\n  _s2();\n  const [user, setUser] = useState(null);\n  const [isAuthenticated, setIsAuthenticated] = useState(false);\n  const [loading, setLoading] = useState(true);\n  const navigate = useNavigate();\n\n  // Verifica se l'utente è già autenticato all'avvio dell'applicazione\n  useEffect(() => {\n    const checkAuth = async () => {\n      try {\n        const token = localStorage.getItem('token');\n        if (token) {\n          // Verifica la validità del token\n          const userData = await authService.checkToken();\n          setUser(userData);\n          setIsAuthenticated(true);\n        }\n      } catch (error) {\n        console.error('Errore durante la verifica del token:', error);\n        // Se il token non è valido, rimuovilo\n        localStorage.removeItem('token');\n        setUser(null);\n        setIsAuthenticated(false);\n      } finally {\n        setLoading(false);\n      }\n    };\n    checkAuth();\n  }, []);\n\n  // Funzione di login\n  const login = async (credentials, loginType) => {\n    try {\n      let response;\n      if (loginType === 'standard') {\n        response = await authService.login(credentials);\n      } else if (loginType === 'cantiere') {\n        response = await authService.loginCantiere(credentials);\n      } else {\n        throw new Error('Tipo di login non valido');\n      }\n      const {\n        access_token,\n        user_id,\n        username,\n        role\n      } = response;\n\n      // Salva il token nel localStorage\n      localStorage.setItem('token', access_token);\n\n      // Imposta i dati dell'utente\n      const userData = {\n        id: user_id,\n        username,\n        role\n      };\n      setUser(userData);\n      setIsAuthenticated(true);\n      return userData;\n    } catch (error) {\n      console.error('Errore durante il login:', error);\n      throw error;\n    }\n  };\n\n  // Funzione di logout\n  const logout = () => {\n    localStorage.removeItem('token');\n    setUser(null);\n    setIsAuthenticated(false);\n    navigate('/login');\n  };\n\n  // Valore del contesto\n  const value = {\n    user,\n    isAuthenticated,\n    loading,\n    login,\n    logout\n  };\n  return /*#__PURE__*/_jsxDEV(AuthContext.Provider, {\n    value: value,\n    children: children\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 89,\n    columnNumber: 10\n  }, this);\n};\n_s2(AuthProvider, \"DZ43dVOC6ATcOj/spkf7xa39n3k=\", false, function () {\n  return [useNavigate];\n});\n_c = AuthProvider;\nvar _c;\n$RefreshReg$(_c, \"AuthProvider\");", "map": {"version": 3, "names": ["React", "createContext", "useState", "useContext", "useEffect", "useNavigate", "authService", "jsxDEV", "_jsxDEV", "AuthContext", "useAuth", "_s", "<PERSON>th<PERSON><PERSON><PERSON>", "children", "_s2", "user", "setUser", "isAuthenticated", "setIsAuthenticated", "loading", "setLoading", "navigate", "checkAuth", "token", "localStorage", "getItem", "userData", "checkToken", "error", "console", "removeItem", "login", "credentials", "loginType", "response", "loginCantiere", "Error", "access_token", "user_id", "username", "role", "setItem", "id", "logout", "value", "Provider", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/PycharmProjects/CMS/webapp/frontend/src/context/AuthContext.js"], "sourcesContent": ["import React, { createContext, useState, useContext, useEffect } from 'react';\nimport { useNavigate } from 'react-router-dom';\nimport authService from '../services/authService';\n\n// Crea il contesto di autenticazione\nconst AuthContext = createContext(null);\n\n// Hook personalizzato per utilizzare il contesto di autenticazione\nexport const useAuth = () => useContext(AuthContext);\n\nexport const AuthProvider = ({ children }) => {\n  const [user, setUser] = useState(null);\n  const [isAuthenticated, setIsAuthenticated] = useState(false);\n  const [loading, setLoading] = useState(true);\n  const navigate = useNavigate();\n\n  // Verifica se l'utente è già autenticato all'avvio dell'applicazione\n  useEffect(() => {\n    const checkAuth = async () => {\n      try {\n        const token = localStorage.getItem('token');\n        if (token) {\n          // Verifica la validità del token\n          const userData = await authService.checkToken();\n          setUser(userData);\n          setIsAuthenticated(true);\n        }\n      } catch (error) {\n        console.error('Errore durante la verifica del token:', error);\n        // Se il token non è valido, rimuovilo\n        localStorage.removeItem('token');\n        setUser(null);\n        setIsAuthenticated(false);\n      } finally {\n        setLoading(false);\n      }\n    };\n\n    checkAuth();\n  }, []);\n\n  // Funzione di login\n  const login = async (credentials, loginType) => {\n    try {\n      let response;\n      \n      if (loginType === 'standard') {\n        response = await authService.login(credentials);\n      } else if (loginType === 'cantiere') {\n        response = await authService.loginCantiere(credentials);\n      } else {\n        throw new Error('Tipo di login non valido');\n      }\n      \n      const { access_token, user_id, username, role } = response;\n      \n      // Salva il token nel localStorage\n      localStorage.setItem('token', access_token);\n      \n      // Imposta i dati dell'utente\n      const userData = { id: user_id, username, role };\n      setUser(userData);\n      setIsAuthenticated(true);\n      \n      return userData;\n    } catch (error) {\n      console.error('Errore durante il login:', error);\n      throw error;\n    }\n  };\n\n  // Funzione di logout\n  const logout = () => {\n    localStorage.removeItem('token');\n    setUser(null);\n    setIsAuthenticated(false);\n    navigate('/login');\n  };\n\n  // Valore del contesto\n  const value = {\n    user,\n    isAuthenticated,\n    loading,\n    login,\n    logout\n  };\n\n  return <AuthContext.Provider value={value}>{children}</AuthContext.Provider>;\n};\n"], "mappings": ";;;AAAA,OAAOA,KAAK,IAAIC,aAAa,EAAEC,QAAQ,EAAEC,UAAU,EAAEC,SAAS,QAAQ,OAAO;AAC7E,SAASC,WAAW,QAAQ,kBAAkB;AAC9C,OAAOC,WAAW,MAAM,yBAAyB;;AAEjD;AAAA,SAAAC,MAAA,IAAAC,OAAA;AACA,MAAMC,WAAW,gBAAGR,aAAa,CAAC,IAAI,CAAC;;AAEvC;AACA,OAAO,MAAMS,OAAO,GAAGA,CAAA;EAAAC,EAAA;EAAA,OAAMR,UAAU,CAACM,WAAW,CAAC;AAAA;AAACE,EAAA,CAAxCD,OAAO;AAEpB,OAAO,MAAME,YAAY,GAAGA,CAAC;EAAEC;AAAS,CAAC,KAAK;EAAAC,GAAA;EAC5C,MAAM,CAACC,IAAI,EAAEC,OAAO,CAAC,GAAGd,QAAQ,CAAC,IAAI,CAAC;EACtC,MAAM,CAACe,eAAe,EAAEC,kBAAkB,CAAC,GAAGhB,QAAQ,CAAC,KAAK,CAAC;EAC7D,MAAM,CAACiB,OAAO,EAAEC,UAAU,CAAC,GAAGlB,QAAQ,CAAC,IAAI,CAAC;EAC5C,MAAMmB,QAAQ,GAAGhB,WAAW,CAAC,CAAC;;EAE9B;EACAD,SAAS,CAAC,MAAM;IACd,MAAMkB,SAAS,GAAG,MAAAA,CAAA,KAAY;MAC5B,IAAI;QACF,MAAMC,KAAK,GAAGC,YAAY,CAACC,OAAO,CAAC,OAAO,CAAC;QAC3C,IAAIF,KAAK,EAAE;UACT;UACA,MAAMG,QAAQ,GAAG,MAAMpB,WAAW,CAACqB,UAAU,CAAC,CAAC;UAC/CX,OAAO,CAACU,QAAQ,CAAC;UACjBR,kBAAkB,CAAC,IAAI,CAAC;QAC1B;MACF,CAAC,CAAC,OAAOU,KAAK,EAAE;QACdC,OAAO,CAACD,KAAK,CAAC,uCAAuC,EAAEA,KAAK,CAAC;QAC7D;QACAJ,YAAY,CAACM,UAAU,CAAC,OAAO,CAAC;QAChCd,OAAO,CAAC,IAAI,CAAC;QACbE,kBAAkB,CAAC,KAAK,CAAC;MAC3B,CAAC,SAAS;QACRE,UAAU,CAAC,KAAK,CAAC;MACnB;IACF,CAAC;IAEDE,SAAS,CAAC,CAAC;EACb,CAAC,EAAE,EAAE,CAAC;;EAEN;EACA,MAAMS,KAAK,GAAG,MAAAA,CAAOC,WAAW,EAAEC,SAAS,KAAK;IAC9C,IAAI;MACF,IAAIC,QAAQ;MAEZ,IAAID,SAAS,KAAK,UAAU,EAAE;QAC5BC,QAAQ,GAAG,MAAM5B,WAAW,CAACyB,KAAK,CAACC,WAAW,CAAC;MACjD,CAAC,MAAM,IAAIC,SAAS,KAAK,UAAU,EAAE;QACnCC,QAAQ,GAAG,MAAM5B,WAAW,CAAC6B,aAAa,CAACH,WAAW,CAAC;MACzD,CAAC,MAAM;QACL,MAAM,IAAII,KAAK,CAAC,0BAA0B,CAAC;MAC7C;MAEA,MAAM;QAAEC,YAAY;QAAEC,OAAO;QAAEC,QAAQ;QAAEC;MAAK,CAAC,GAAGN,QAAQ;;MAE1D;MACAV,YAAY,CAACiB,OAAO,CAAC,OAAO,EAAEJ,YAAY,CAAC;;MAE3C;MACA,MAAMX,QAAQ,GAAG;QAAEgB,EAAE,EAAEJ,OAAO;QAAEC,QAAQ;QAAEC;MAAK,CAAC;MAChDxB,OAAO,CAACU,QAAQ,CAAC;MACjBR,kBAAkB,CAAC,IAAI,CAAC;MAExB,OAAOQ,QAAQ;IACjB,CAAC,CAAC,OAAOE,KAAK,EAAE;MACdC,OAAO,CAACD,KAAK,CAAC,0BAA0B,EAAEA,KAAK,CAAC;MAChD,MAAMA,KAAK;IACb;EACF,CAAC;;EAED;EACA,MAAMe,MAAM,GAAGA,CAAA,KAAM;IACnBnB,YAAY,CAACM,UAAU,CAAC,OAAO,CAAC;IAChCd,OAAO,CAAC,IAAI,CAAC;IACbE,kBAAkB,CAAC,KAAK,CAAC;IACzBG,QAAQ,CAAC,QAAQ,CAAC;EACpB,CAAC;;EAED;EACA,MAAMuB,KAAK,GAAG;IACZ7B,IAAI;IACJE,eAAe;IACfE,OAAO;IACPY,KAAK;IACLY;EACF,CAAC;EAED,oBAAOnC,OAAA,CAACC,WAAW,CAACoC,QAAQ;IAACD,KAAK,EAAEA,KAAM;IAAA/B,QAAA,EAAEA;EAAQ;IAAAiC,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OAAuB,CAAC;AAC9E,CAAC;AAACnC,GAAA,CA/EWF,YAAY;EAAA,QAINP,WAAW;AAAA;AAAA6C,EAAA,GAJjBtC,YAAY;AAAA,IAAAsC,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}