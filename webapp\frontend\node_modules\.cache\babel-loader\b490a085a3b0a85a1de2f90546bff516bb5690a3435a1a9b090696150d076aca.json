{"ast": null, "code": "import axios from 'axios';\nimport config from '../config';\nimport axiosInstance from './axiosConfig';\nconst API_URL = config.API_URL;\nconst reportService = {\n  // Ottiene il report di avanzamento\n  getProgressReport: async (cantiereId, formato = 'video') => {\n    try {\n      // Assicurati che cantiereId sia un numero\n      const cantiereIdNum = parseInt(cantiereId, 10);\n      if (isNaN(cantiereIdNum)) {\n        throw new Error(`ID cantiere non valido: ${cantiereId}`);\n      }\n      const response = await axiosInstance.get(`/reports/${cantiereIdNum}/progress?formato=${formato}`);\n      return response.data;\n    } catch (error) {\n      console.error('Get progress report error:', error);\n      throw error.response ? error.response.data : error;\n    }\n  },\n  // Ottiene la distinta materiali (Bill of Quantities)\n  getBillOfQuantities: async (cantiereId, formato = 'video') => {\n    try {\n      // Assicurati che cantiereId sia un numero\n      const cantiereIdNum = parseInt(cantiereId, 10);\n      if (isNaN(cantiereIdNum)) {\n        throw new Error(`ID cantiere non valido: ${cantiereId}`);\n      }\n      const response = await axiosInstance.get(`/reports/${cantiereIdNum}/boq?formato=${formato}`);\n      return response.data;\n    } catch (error) {\n      console.error('Get bill of quantities error:', error);\n      throw error.response ? error.response.data : error;\n    }\n  },\n  // Ottiene il report di posa per periodo\n  getPosaPerPeriodoReport: async (cantiereId, dataInizio, dataFine, formato = 'video') => {\n    try {\n      // Assicurati che cantiereId sia un numero\n      const cantiereIdNum = parseInt(cantiereId, 10);\n      if (isNaN(cantiereIdNum)) {\n        throw new Error(`ID cantiere non valido: ${cantiereId}`);\n      }\n      const response = await axiosInstance.get(`/reports/${cantiereIdNum}/posa-periodo?data_inizio=${dataInizio}&data_fine=${dataFine}&formato=${formato}`);\n      return response.data;\n    } catch (error) {\n      console.error('Get posa per periodo report error:', error);\n      throw error.response ? error.response.data : error;\n    }\n  },\n  // Ottiene il report dei cavi per stato\n  getCaviStatoReport: async (cantiereId, formato = 'video') => {\n    try {\n      const cantiereIdNum = parseInt(cantiereId, 10);\n      if (isNaN(cantiereIdNum)) {\n        throw new Error(`ID cantiere non valido: ${cantiereId}`);\n      }\n      const response = await axiosInstance.get(`/reports/${cantiereIdNum}/cavi-stato?formato=${formato}`);\n      return response.data;\n    } catch (error) {\n      console.error('Get cavi stato report error:', error);\n      throw error.response ? error.response.data : error;\n    }\n  }\n};\nexport default reportService;", "map": {"version": 3, "names": ["axios", "config", "axiosInstance", "API_URL", "reportService", "getProgressReport", "cantiereId", "formato", "cantiereIdNum", "parseInt", "isNaN", "Error", "response", "get", "data", "error", "console", "getBillOfQuantities", "getPosaPerPeriodoReport", "dataInizio", "dataFine", "getCaviStatoReport"], "sources": ["C:/CMS/webapp/frontend/src/services/reportService.js"], "sourcesContent": ["import axios from 'axios';\r\nimport config from '../config';\r\nimport axiosInstance from './axiosConfig';\r\n\r\nconst API_URL = config.API_URL;\r\n\r\nconst reportService = {\r\n  // Ottiene il report di avanzamento\r\n  getProgressReport: async (cantiereId, formato = 'video') => {\r\n    try {\r\n      // Assicurati che cantiereId sia un numero\r\n      const cantiereIdNum = parseInt(cantiereId, 10);\r\n      if (isNaN(cantiereIdNum)) {\r\n        throw new Error(`ID cantiere non valido: ${cantiereId}`);\r\n      }\r\n\r\n      const response = await axiosInstance.get(`/reports/${cantiereIdNum}/progress?formato=${formato}`);\r\n      return response.data;\r\n    } catch (error) {\r\n      console.error('Get progress report error:', error);\r\n      throw error.response ? error.response.data : error;\r\n    }\r\n  },\r\n\r\n  // Ottiene la distinta materiali (Bill of Quantities)\r\n  getBillOfQuantities: async (cantiereId, formato = 'video') => {\r\n    try {\r\n      // Assicurati che cantiereId sia un numero\r\n      const cantiereIdNum = parseInt(cantiereId, 10);\r\n      if (isNaN(cantiereIdNum)) {\r\n        throw new Error(`ID cantiere non valido: ${cantiereId}`);\r\n      }\r\n\r\n      const response = await axiosInstance.get(`/reports/${cantiereIdNum}/boq?formato=${formato}`);\r\n      return response.data;\r\n    } catch (error) {\r\n      console.error('Get bill of quantities error:', error);\r\n      throw error.response ? error.response.data : error;\r\n    }\r\n  },\r\n\r\n\r\n\r\n  // Ottiene il report di posa per periodo\r\n  getPosaPerPeriodoReport: async (cantiereId, dataInizio, dataFine, formato = 'video') => {\r\n    try {\r\n      // Assicurati che cantiereId sia un numero\r\n      const cantiereIdNum = parseInt(cantiereId, 10);\r\n      if (isNaN(cantiereIdNum)) {\r\n        throw new Error(`ID cantiere non valido: ${cantiereId}`);\r\n      }\r\n\r\n      const response = await axiosInstance.get(\r\n        `/reports/${cantiereIdNum}/posa-periodo?data_inizio=${dataInizio}&data_fine=${dataFine}&formato=${formato}`\r\n      );\r\n      return response.data;\r\n    } catch (error) {\r\n      console.error('Get posa per periodo report error:', error);\r\n      throw error.response ? error.response.data : error;\r\n    }\r\n  },\r\n\r\n  // Ottiene il report dei cavi per stato\r\n  getCaviStatoReport: async (cantiereId, formato = 'video') => {\r\n    try {\r\n      const cantiereIdNum = parseInt(cantiereId, 10);\r\n      if (isNaN(cantiereIdNum)) {\r\n        throw new Error(`ID cantiere non valido: ${cantiereId}`);\r\n      }\r\n\r\n      const response = await axiosInstance.get(`/reports/${cantiereIdNum}/cavi-stato?formato=${formato}`);\r\n      return response.data;\r\n    } catch (error) {\r\n      console.error('Get cavi stato report error:', error);\r\n      throw error.response ? error.response.data : error;\r\n    }\r\n  }\r\n};\r\n\r\nexport default reportService;\r\n"], "mappings": "AAAA,OAAOA,KAAK,MAAM,OAAO;AACzB,OAAOC,MAAM,MAAM,WAAW;AAC9B,OAAOC,aAAa,MAAM,eAAe;AAEzC,MAAMC,OAAO,GAAGF,MAAM,CAACE,OAAO;AAE9B,MAAMC,aAAa,GAAG;EACpB;EACAC,iBAAiB,EAAE,MAAAA,CAAOC,UAAU,EAAEC,OAAO,GAAG,OAAO,KAAK;IAC1D,IAAI;MACF;MACA,MAAMC,aAAa,GAAGC,QAAQ,CAACH,UAAU,EAAE,EAAE,CAAC;MAC9C,IAAII,KAAK,CAACF,aAAa,CAAC,EAAE;QACxB,MAAM,IAAIG,KAAK,CAAC,2BAA2BL,UAAU,EAAE,CAAC;MAC1D;MAEA,MAAMM,QAAQ,GAAG,MAAMV,aAAa,CAACW,GAAG,CAAC,YAAYL,aAAa,qBAAqBD,OAAO,EAAE,CAAC;MACjG,OAAOK,QAAQ,CAACE,IAAI;IACtB,CAAC,CAAC,OAAOC,KAAK,EAAE;MACdC,OAAO,CAACD,KAAK,CAAC,4BAA4B,EAAEA,KAAK,CAAC;MAClD,MAAMA,KAAK,CAACH,QAAQ,GAAGG,KAAK,CAACH,QAAQ,CAACE,IAAI,GAAGC,KAAK;IACpD;EACF,CAAC;EAED;EACAE,mBAAmB,EAAE,MAAAA,CAAOX,UAAU,EAAEC,OAAO,GAAG,OAAO,KAAK;IAC5D,IAAI;MACF;MACA,MAAMC,aAAa,GAAGC,QAAQ,CAACH,UAAU,EAAE,EAAE,CAAC;MAC9C,IAAII,KAAK,CAACF,aAAa,CAAC,EAAE;QACxB,MAAM,IAAIG,KAAK,CAAC,2BAA2BL,UAAU,EAAE,CAAC;MAC1D;MAEA,MAAMM,QAAQ,GAAG,MAAMV,aAAa,CAACW,GAAG,CAAC,YAAYL,aAAa,gBAAgBD,OAAO,EAAE,CAAC;MAC5F,OAAOK,QAAQ,CAACE,IAAI;IACtB,CAAC,CAAC,OAAOC,KAAK,EAAE;MACdC,OAAO,CAACD,KAAK,CAAC,+BAA+B,EAAEA,KAAK,CAAC;MACrD,MAAMA,KAAK,CAACH,QAAQ,GAAGG,KAAK,CAACH,QAAQ,CAACE,IAAI,GAAGC,KAAK;IACpD;EACF,CAAC;EAID;EACAG,uBAAuB,EAAE,MAAAA,CAAOZ,UAAU,EAAEa,UAAU,EAAEC,QAAQ,EAAEb,OAAO,GAAG,OAAO,KAAK;IACtF,IAAI;MACF;MACA,MAAMC,aAAa,GAAGC,QAAQ,CAACH,UAAU,EAAE,EAAE,CAAC;MAC9C,IAAII,KAAK,CAACF,aAAa,CAAC,EAAE;QACxB,MAAM,IAAIG,KAAK,CAAC,2BAA2BL,UAAU,EAAE,CAAC;MAC1D;MAEA,MAAMM,QAAQ,GAAG,MAAMV,aAAa,CAACW,GAAG,CACtC,YAAYL,aAAa,6BAA6BW,UAAU,cAAcC,QAAQ,YAAYb,OAAO,EAC3G,CAAC;MACD,OAAOK,QAAQ,CAACE,IAAI;IACtB,CAAC,CAAC,OAAOC,KAAK,EAAE;MACdC,OAAO,CAACD,KAAK,CAAC,oCAAoC,EAAEA,KAAK,CAAC;MAC1D,MAAMA,KAAK,CAACH,QAAQ,GAAGG,KAAK,CAACH,QAAQ,CAACE,IAAI,GAAGC,KAAK;IACpD;EACF,CAAC;EAED;EACAM,kBAAkB,EAAE,MAAAA,CAAOf,UAAU,EAAEC,OAAO,GAAG,OAAO,KAAK;IAC3D,IAAI;MACF,MAAMC,aAAa,GAAGC,QAAQ,CAACH,UAAU,EAAE,EAAE,CAAC;MAC9C,IAAII,KAAK,CAACF,aAAa,CAAC,EAAE;QACxB,MAAM,IAAIG,KAAK,CAAC,2BAA2BL,UAAU,EAAE,CAAC;MAC1D;MAEA,MAAMM,QAAQ,GAAG,MAAMV,aAAa,CAACW,GAAG,CAAC,YAAYL,aAAa,uBAAuBD,OAAO,EAAE,CAAC;MACnG,OAAOK,QAAQ,CAACE,IAAI;IACtB,CAAC,CAAC,OAAOC,KAAK,EAAE;MACdC,OAAO,CAACD,KAAK,CAAC,8BAA8B,EAAEA,KAAK,CAAC;MACpD,MAAMA,KAAK,CAACH,QAAQ,GAAGG,KAAK,CAACH,QAAQ,CAACE,IAAI,GAAGC,KAAK;IACpD;EACF;AACF,CAAC;AAED,eAAeX,aAAa", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}