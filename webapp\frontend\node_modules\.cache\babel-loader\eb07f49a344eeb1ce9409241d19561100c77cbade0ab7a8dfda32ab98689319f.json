{"ast": null, "code": "import { constructNow } from \"./constructNow.mjs\";\nimport { isSameHour } from \"./isSameHour.mjs\";\n\n/**\n * @name isThisHour\n * @category Hour Helpers\n * @summary Is the given date in the same hour as the current date?\n * @pure false\n *\n * @description\n * Is the given date in the same hour as the current date?\n *\n * @typeParam DateType - The `Date` type, the function operates on. Gets inferred from passed arguments. Allows to use extensions like [`UTCDate`](https://github.com/date-fns/utc).\n *\n * @param date - The date to check\n *\n * @returns The date is in this hour\n *\n * @example\n * // If now is 25 September 2014 18:30:15.500,\n * // is 25 September 2014 18:00:00 in this hour?\n * const result = isThisHour(new Date(2014, 8, 25, 18))\n * //=> true\n */\nexport function isThisHour(date) {\n  return isSameHour(date, constructNow(date));\n}\n\n// Fallback for modularized imports:\nexport default isThisHour;", "map": {"version": 3, "names": ["constructNow", "isSameHour", "isThisHour", "date"], "sources": ["C:/CMS/webapp/frontend/node_modules/date-fns/isThisHour.mjs"], "sourcesContent": ["import { constructNow } from \"./constructNow.mjs\";\nimport { isSameHour } from \"./isSameHour.mjs\";\n\n/**\n * @name isThisHour\n * @category Hour Helpers\n * @summary Is the given date in the same hour as the current date?\n * @pure false\n *\n * @description\n * Is the given date in the same hour as the current date?\n *\n * @typeParam DateType - The `Date` type, the function operates on. Gets inferred from passed arguments. Allows to use extensions like [`UTCDate`](https://github.com/date-fns/utc).\n *\n * @param date - The date to check\n *\n * @returns The date is in this hour\n *\n * @example\n * // If now is 25 September 2014 18:30:15.500,\n * // is 25 September 2014 18:00:00 in this hour?\n * const result = isThisHour(new Date(2014, 8, 25, 18))\n * //=> true\n */\nexport function isThisHour(date) {\n  return isSameHour(date, constructNow(date));\n}\n\n// Fallback for modularized imports:\nexport default isThisHour;\n"], "mappings": "AAAA,SAASA,YAAY,QAAQ,oBAAoB;AACjD,SAASC,UAAU,QAAQ,kBAAkB;;AAE7C;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO,SAASC,UAAUA,CAACC,IAAI,EAAE;EAC/B,OAAOF,UAAU,CAACE,IAAI,EAAEH,YAAY,CAACG,IAAI,CAAC,CAAC;AAC7C;;AAEA;AACA,eAAeD,UAAU", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}