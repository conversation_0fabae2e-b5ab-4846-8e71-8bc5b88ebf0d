{"ast": null, "code": "var formatDistanceLocale = {\n  lessThanXSeconds: {\n    one: {\n      standalone: 'manje od 1 sekunde',\n      withPrepositionAgo: 'manje od 1 sekunde',\n      withPrepositionIn: 'manje od 1 sekundu'\n    },\n    dual: 'manje od {{count}} sekunde',\n    other: 'manje od {{count}} sekundi'\n  },\n  xSeconds: {\n    one: {\n      standalone: '1 sekunda',\n      withPrepositionAgo: '1 sekunde',\n      withPrepositionIn: '1 sekundu'\n    },\n    dual: '{{count}} sekunde',\n    other: '{{count}} sekundi'\n  },\n  halfAMinute: 'pola minute',\n  lessThanXMinutes: {\n    one: {\n      standalone: 'manje od 1 minute',\n      withPrepositionAgo: 'manje od 1 minute',\n      withPrepositionIn: 'manje od 1 minutu'\n    },\n    dual: 'manje od {{count}} minute',\n    other: 'manje od {{count}} minuta'\n  },\n  xMinutes: {\n    one: {\n      standalone: '1 minuta',\n      withPrepositionAgo: '1 minute',\n      withPrepositionIn: '1 minutu'\n    },\n    dual: '{{count}} minute',\n    other: '{{count}} minuta'\n  },\n  aboutXHours: {\n    one: {\n      standalone: 'oko 1 sat',\n      withPrepositionAgo: 'oko 1 sat',\n      withPrepositionIn: 'oko 1 sat'\n    },\n    dual: 'oko {{count}} sata',\n    other: 'oko {{count}} sati'\n  },\n  xHours: {\n    one: {\n      standalone: '1 sat',\n      withPrepositionAgo: '1 sat',\n      withPrepositionIn: '1 sat'\n    },\n    dual: '{{count}} sata',\n    other: '{{count}} sati'\n  },\n  xDays: {\n    one: {\n      standalone: '1 dan',\n      withPrepositionAgo: '1 dan',\n      withPrepositionIn: '1 dan'\n    },\n    dual: '{{count}} dana',\n    other: '{{count}} dana'\n  },\n  aboutXWeeks: {\n    one: {\n      standalone: 'oko 1 tjedan',\n      withPrepositionAgo: 'oko 1 tjedan',\n      withPrepositionIn: 'oko 1 tjedan'\n    },\n    dual: 'oko {{count}} tjedna',\n    other: 'oko {{count}} tjedana'\n  },\n  xWeeks: {\n    one: {\n      standalone: '1 tjedan',\n      withPrepositionAgo: '1 tjedan',\n      withPrepositionIn: '1 tjedan'\n    },\n    dual: '{{count}} tjedna',\n    other: '{{count}} tjedana'\n  },\n  aboutXMonths: {\n    one: {\n      standalone: 'oko 1 mjesec',\n      withPrepositionAgo: 'oko 1 mjesec',\n      withPrepositionIn: 'oko 1 mjesec'\n    },\n    dual: 'oko {{count}} mjeseca',\n    other: 'oko {{count}} mjeseci'\n  },\n  xMonths: {\n    one: {\n      standalone: '1 mjesec',\n      withPrepositionAgo: '1 mjesec',\n      withPrepositionIn: '1 mjesec'\n    },\n    dual: '{{count}} mjeseca',\n    other: '{{count}} mjeseci'\n  },\n  aboutXYears: {\n    one: {\n      standalone: 'oko 1 godinu',\n      withPrepositionAgo: 'oko 1 godinu',\n      withPrepositionIn: 'oko 1 godinu'\n    },\n    dual: 'oko {{count}} godine',\n    other: 'oko {{count}} godina'\n  },\n  xYears: {\n    one: {\n      standalone: '1 godina',\n      withPrepositionAgo: '1 godine',\n      withPrepositionIn: '1 godinu'\n    },\n    dual: '{{count}} godine',\n    other: '{{count}} godina'\n  },\n  overXYears: {\n    one: {\n      standalone: 'preko 1 godinu',\n      withPrepositionAgo: 'preko 1 godinu',\n      withPrepositionIn: 'preko 1 godinu'\n    },\n    dual: 'preko {{count}} godine',\n    other: 'preko {{count}} godina'\n  },\n  almostXYears: {\n    one: {\n      standalone: 'gotovo 1 godinu',\n      withPrepositionAgo: 'gotovo 1 godinu',\n      withPrepositionIn: 'gotovo 1 godinu'\n    },\n    dual: 'gotovo {{count}} godine',\n    other: 'gotovo {{count}} godina'\n  }\n};\nvar formatDistance = function formatDistance(token, count, options) {\n  var result;\n  var tokenValue = formatDistanceLocale[token];\n  if (typeof tokenValue === 'string') {\n    result = tokenValue;\n  } else if (count === 1) {\n    if (options !== null && options !== void 0 && options.addSuffix) {\n      if (options.comparison && options.comparison > 0) {\n        result = tokenValue.one.withPrepositionIn;\n      } else {\n        result = tokenValue.one.withPrepositionAgo;\n      }\n    } else {\n      result = tokenValue.one.standalone;\n    }\n  } else if (count % 10 > 1 && count % 10 < 5 &&\n  // if last digit is between 2 and 4\n  String(count).substr(-2, 1) !== '1' // unless the 2nd to last digit is \"1\"\n  ) {\n    result = tokenValue.dual.replace('{{count}}', String(count));\n  } else {\n    result = tokenValue.other.replace('{{count}}', String(count));\n  }\n  if (options !== null && options !== void 0 && options.addSuffix) {\n    if (options.comparison && options.comparison > 0) {\n      return 'za ' + result;\n    } else {\n      return 'prije ' + result;\n    }\n  }\n  return result;\n};\nexport default formatDistance;", "map": {"version": 3, "names": ["formatDistanceLocale", "lessThanXSeconds", "one", "standalone", "withPrepositionAgo", "withPrepositionIn", "dual", "other", "xSeconds", "halfAMinute", "lessThanXMinutes", "xMinutes", "aboutXHours", "xHours", "xDays", "aboutXWeeks", "xWeeks", "aboutXMonths", "xMonths", "aboutXYears", "xYears", "overXYears", "almostXYears", "formatDistance", "token", "count", "options", "result", "tokenValue", "addSuffix", "comparison", "String", "substr", "replace"], "sources": ["C:/CMS/webapp/frontend/node_modules/date-fns/esm/locale/hr/_lib/formatDistance/index.js"], "sourcesContent": ["var formatDistanceLocale = {\n  lessThanXSeconds: {\n    one: {\n      standalone: 'manje od 1 sekunde',\n      withPrepositionAgo: 'manje od 1 sekunde',\n      withPrepositionIn: 'manje od 1 sekundu'\n    },\n    dual: 'manje od {{count}} sekunde',\n    other: 'manje od {{count}} sekundi'\n  },\n  xSeconds: {\n    one: {\n      standalone: '1 sekunda',\n      withPrepositionAgo: '1 sekunde',\n      withPrepositionIn: '1 sekundu'\n    },\n    dual: '{{count}} sekunde',\n    other: '{{count}} sekundi'\n  },\n  halfAMinute: 'pola minute',\n  lessThanXMinutes: {\n    one: {\n      standalone: 'manje od 1 minute',\n      withPrepositionAgo: 'manje od 1 minute',\n      withPrepositionIn: 'manje od 1 minutu'\n    },\n    dual: 'manje od {{count}} minute',\n    other: 'manje od {{count}} minuta'\n  },\n  xMinutes: {\n    one: {\n      standalone: '1 minuta',\n      withPrepositionAgo: '1 minute',\n      withPrepositionIn: '1 minutu'\n    },\n    dual: '{{count}} minute',\n    other: '{{count}} minuta'\n  },\n  aboutXHours: {\n    one: {\n      standalone: 'oko 1 sat',\n      withPrepositionAgo: 'oko 1 sat',\n      withPrepositionIn: 'oko 1 sat'\n    },\n    dual: 'oko {{count}} sata',\n    other: 'oko {{count}} sati'\n  },\n  xHours: {\n    one: {\n      standalone: '1 sat',\n      withPrepositionAgo: '1 sat',\n      withPrepositionIn: '1 sat'\n    },\n    dual: '{{count}} sata',\n    other: '{{count}} sati'\n  },\n  xDays: {\n    one: {\n      standalone: '1 dan',\n      withPrepositionAgo: '1 dan',\n      withPrepositionIn: '1 dan'\n    },\n    dual: '{{count}} dana',\n    other: '{{count}} dana'\n  },\n  aboutXWeeks: {\n    one: {\n      standalone: 'oko 1 tjedan',\n      withPrepositionAgo: 'oko 1 tjedan',\n      withPrepositionIn: 'oko 1 tjedan'\n    },\n    dual: 'oko {{count}} tjedna',\n    other: 'oko {{count}} tjedana'\n  },\n  xWeeks: {\n    one: {\n      standalone: '1 tjedan',\n      withPrepositionAgo: '1 tjedan',\n      withPrepositionIn: '1 tjedan'\n    },\n    dual: '{{count}} tjedna',\n    other: '{{count}} tjedana'\n  },\n  aboutXMonths: {\n    one: {\n      standalone: 'oko 1 mjesec',\n      withPrepositionAgo: 'oko 1 mjesec',\n      withPrepositionIn: 'oko 1 mjesec'\n    },\n    dual: 'oko {{count}} mjeseca',\n    other: 'oko {{count}} mjeseci'\n  },\n  xMonths: {\n    one: {\n      standalone: '1 mjesec',\n      withPrepositionAgo: '1 mjesec',\n      withPrepositionIn: '1 mjesec'\n    },\n    dual: '{{count}} mjeseca',\n    other: '{{count}} mjeseci'\n  },\n  aboutXYears: {\n    one: {\n      standalone: 'oko 1 godinu',\n      withPrepositionAgo: 'oko 1 godinu',\n      withPrepositionIn: 'oko 1 godinu'\n    },\n    dual: 'oko {{count}} godine',\n    other: 'oko {{count}} godina'\n  },\n  xYears: {\n    one: {\n      standalone: '1 godina',\n      withPrepositionAgo: '1 godine',\n      withPrepositionIn: '1 godinu'\n    },\n    dual: '{{count}} godine',\n    other: '{{count}} godina'\n  },\n  overXYears: {\n    one: {\n      standalone: 'preko 1 godinu',\n      withPrepositionAgo: 'preko 1 godinu',\n      withPrepositionIn: 'preko 1 godinu'\n    },\n    dual: 'preko {{count}} godine',\n    other: 'preko {{count}} godina'\n  },\n  almostXYears: {\n    one: {\n      standalone: 'gotovo 1 godinu',\n      withPrepositionAgo: 'gotovo 1 godinu',\n      withPrepositionIn: 'gotovo 1 godinu'\n    },\n    dual: 'gotovo {{count}} godine',\n    other: 'gotovo {{count}} godina'\n  }\n};\nvar formatDistance = function formatDistance(token, count, options) {\n  var result;\n  var tokenValue = formatDistanceLocale[token];\n  if (typeof tokenValue === 'string') {\n    result = tokenValue;\n  } else if (count === 1) {\n    if (options !== null && options !== void 0 && options.addSuffix) {\n      if (options.comparison && options.comparison > 0) {\n        result = tokenValue.one.withPrepositionIn;\n      } else {\n        result = tokenValue.one.withPrepositionAgo;\n      }\n    } else {\n      result = tokenValue.one.standalone;\n    }\n  } else if (count % 10 > 1 && count % 10 < 5 &&\n  // if last digit is between 2 and 4\n  String(count).substr(-2, 1) !== '1' // unless the 2nd to last digit is \"1\"\n  ) {\n    result = tokenValue.dual.replace('{{count}}', String(count));\n  } else {\n    result = tokenValue.other.replace('{{count}}', String(count));\n  }\n  if (options !== null && options !== void 0 && options.addSuffix) {\n    if (options.comparison && options.comparison > 0) {\n      return 'za ' + result;\n    } else {\n      return 'prije ' + result;\n    }\n  }\n  return result;\n};\nexport default formatDistance;"], "mappings": "AAAA,IAAIA,oBAAoB,GAAG;EACzBC,gBAAgB,EAAE;IAChBC,GAAG,EAAE;MACHC,UAAU,EAAE,oBAAoB;MAChCC,kBAAkB,EAAE,oBAAoB;MACxCC,iBAAiB,EAAE;IACrB,CAAC;IACDC,IAAI,EAAE,4BAA4B;IAClCC,KAAK,EAAE;EACT,CAAC;EACDC,QAAQ,EAAE;IACRN,GAAG,EAAE;MACHC,UAAU,EAAE,WAAW;MACvBC,kBAAkB,EAAE,WAAW;MAC/BC,iBAAiB,EAAE;IACrB,CAAC;IACDC,IAAI,EAAE,mBAAmB;IACzBC,KAAK,EAAE;EACT,CAAC;EACDE,WAAW,EAAE,aAAa;EAC1BC,gBAAgB,EAAE;IAChBR,GAAG,EAAE;MACHC,UAAU,EAAE,mBAAmB;MAC/BC,kBAAkB,EAAE,mBAAmB;MACvCC,iBAAiB,EAAE;IACrB,CAAC;IACDC,IAAI,EAAE,2BAA2B;IACjCC,KAAK,EAAE;EACT,CAAC;EACDI,QAAQ,EAAE;IACRT,GAAG,EAAE;MACHC,UAAU,EAAE,UAAU;MACtBC,kBAAkB,EAAE,UAAU;MAC9BC,iBAAiB,EAAE;IACrB,CAAC;IACDC,IAAI,EAAE,kBAAkB;IACxBC,KAAK,EAAE;EACT,CAAC;EACDK,WAAW,EAAE;IACXV,GAAG,EAAE;MACHC,UAAU,EAAE,WAAW;MACvBC,kBAAkB,EAAE,WAAW;MAC/BC,iBAAiB,EAAE;IACrB,CAAC;IACDC,IAAI,EAAE,oBAAoB;IAC1BC,KAAK,EAAE;EACT,CAAC;EACDM,MAAM,EAAE;IACNX,GAAG,EAAE;MACHC,UAAU,EAAE,OAAO;MACnBC,kBAAkB,EAAE,OAAO;MAC3BC,iBAAiB,EAAE;IACrB,CAAC;IACDC,IAAI,EAAE,gBAAgB;IACtBC,KAAK,EAAE;EACT,CAAC;EACDO,KAAK,EAAE;IACLZ,GAAG,EAAE;MACHC,UAAU,EAAE,OAAO;MACnBC,kBAAkB,EAAE,OAAO;MAC3BC,iBAAiB,EAAE;IACrB,CAAC;IACDC,IAAI,EAAE,gBAAgB;IACtBC,KAAK,EAAE;EACT,CAAC;EACDQ,WAAW,EAAE;IACXb,GAAG,EAAE;MACHC,UAAU,EAAE,cAAc;MAC1BC,kBAAkB,EAAE,cAAc;MAClCC,iBAAiB,EAAE;IACrB,CAAC;IACDC,IAAI,EAAE,sBAAsB;IAC5BC,KAAK,EAAE;EACT,CAAC;EACDS,MAAM,EAAE;IACNd,GAAG,EAAE;MACHC,UAAU,EAAE,UAAU;MACtBC,kBAAkB,EAAE,UAAU;MAC9BC,iBAAiB,EAAE;IACrB,CAAC;IACDC,IAAI,EAAE,kBAAkB;IACxBC,KAAK,EAAE;EACT,CAAC;EACDU,YAAY,EAAE;IACZf,GAAG,EAAE;MACHC,UAAU,EAAE,cAAc;MAC1BC,kBAAkB,EAAE,cAAc;MAClCC,iBAAiB,EAAE;IACrB,CAAC;IACDC,IAAI,EAAE,uBAAuB;IAC7BC,KAAK,EAAE;EACT,CAAC;EACDW,OAAO,EAAE;IACPhB,GAAG,EAAE;MACHC,UAAU,EAAE,UAAU;MACtBC,kBAAkB,EAAE,UAAU;MAC9BC,iBAAiB,EAAE;IACrB,CAAC;IACDC,IAAI,EAAE,mBAAmB;IACzBC,KAAK,EAAE;EACT,CAAC;EACDY,WAAW,EAAE;IACXjB,GAAG,EAAE;MACHC,UAAU,EAAE,cAAc;MAC1BC,kBAAkB,EAAE,cAAc;MAClCC,iBAAiB,EAAE;IACrB,CAAC;IACDC,IAAI,EAAE,sBAAsB;IAC5BC,KAAK,EAAE;EACT,CAAC;EACDa,MAAM,EAAE;IACNlB,GAAG,EAAE;MACHC,UAAU,EAAE,UAAU;MACtBC,kBAAkB,EAAE,UAAU;MAC9BC,iBAAiB,EAAE;IACrB,CAAC;IACDC,IAAI,EAAE,kBAAkB;IACxBC,KAAK,EAAE;EACT,CAAC;EACDc,UAAU,EAAE;IACVnB,GAAG,EAAE;MACHC,UAAU,EAAE,gBAAgB;MAC5BC,kBAAkB,EAAE,gBAAgB;MACpCC,iBAAiB,EAAE;IACrB,CAAC;IACDC,IAAI,EAAE,wBAAwB;IAC9BC,KAAK,EAAE;EACT,CAAC;EACDe,YAAY,EAAE;IACZpB,GAAG,EAAE;MACHC,UAAU,EAAE,iBAAiB;MAC7BC,kBAAkB,EAAE,iBAAiB;MACrCC,iBAAiB,EAAE;IACrB,CAAC;IACDC,IAAI,EAAE,yBAAyB;IAC/BC,KAAK,EAAE;EACT;AACF,CAAC;AACD,IAAIgB,cAAc,GAAG,SAASA,cAAcA,CAACC,KAAK,EAAEC,KAAK,EAAEC,OAAO,EAAE;EAClE,IAAIC,MAAM;EACV,IAAIC,UAAU,GAAG5B,oBAAoB,CAACwB,KAAK,CAAC;EAC5C,IAAI,OAAOI,UAAU,KAAK,QAAQ,EAAE;IAClCD,MAAM,GAAGC,UAAU;EACrB,CAAC,MAAM,IAAIH,KAAK,KAAK,CAAC,EAAE;IACtB,IAAIC,OAAO,KAAK,IAAI,IAAIA,OAAO,KAAK,KAAK,CAAC,IAAIA,OAAO,CAACG,SAAS,EAAE;MAC/D,IAAIH,OAAO,CAACI,UAAU,IAAIJ,OAAO,CAACI,UAAU,GAAG,CAAC,EAAE;QAChDH,MAAM,GAAGC,UAAU,CAAC1B,GAAG,CAACG,iBAAiB;MAC3C,CAAC,MAAM;QACLsB,MAAM,GAAGC,UAAU,CAAC1B,GAAG,CAACE,kBAAkB;MAC5C;IACF,CAAC,MAAM;MACLuB,MAAM,GAAGC,UAAU,CAAC1B,GAAG,CAACC,UAAU;IACpC;EACF,CAAC,MAAM,IAAIsB,KAAK,GAAG,EAAE,GAAG,CAAC,IAAIA,KAAK,GAAG,EAAE,GAAG,CAAC;EAC3C;EACAM,MAAM,CAACN,KAAK,CAAC,CAACO,MAAM,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,KAAK,GAAG,CAAC;EAAA,EAClC;IACAL,MAAM,GAAGC,UAAU,CAACtB,IAAI,CAAC2B,OAAO,CAAC,WAAW,EAAEF,MAAM,CAACN,KAAK,CAAC,CAAC;EAC9D,CAAC,MAAM;IACLE,MAAM,GAAGC,UAAU,CAACrB,KAAK,CAAC0B,OAAO,CAAC,WAAW,EAAEF,MAAM,CAACN,KAAK,CAAC,CAAC;EAC/D;EACA,IAAIC,OAAO,KAAK,IAAI,IAAIA,OAAO,KAAK,KAAK,CAAC,IAAIA,OAAO,CAACG,SAAS,EAAE;IAC/D,IAAIH,OAAO,CAACI,UAAU,IAAIJ,OAAO,CAACI,UAAU,GAAG,CAAC,EAAE;MAChD,OAAO,KAAK,GAAGH,MAAM;IACvB,CAAC,MAAM;MACL,OAAO,QAAQ,GAAGA,MAAM;IAC1B;EACF;EACA,OAAOA,MAAM;AACf,CAAC;AACD,eAAeJ,cAAc", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}