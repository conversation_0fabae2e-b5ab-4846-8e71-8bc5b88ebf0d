{"ast": null, "code": "var _jsxFileName = \"C:\\\\CMS\\\\webapp\\\\frontend\\\\src\\\\components\\\\cavi\\\\PosaCaviCollegamenti.js\",\n  _s = $RefreshSig$();\nimport React, { useState } from 'react';\nimport { Box, Typography, Button, Paper, Divider, List, ListItem, ListItemText, ListItemIcon, ListItemButton, Dialog, DialogTitle, DialogContent, DialogContentText, DialogActions, TextField, FormControl, InputLabel, Select, MenuItem, Grid, Alert, CircularProgress, FormHelperText, Radio, RadioGroup, FormControlLabel, FormLabel } from '@mui/material';\nimport { Add as AddIcon, Edit as EditIcon, Delete as DeleteIcon, Cable as CableIcon, Save as SaveIcon, Warning as WarningIcon } from '@mui/icons-material';\nimport { useNavigate } from 'react-router-dom';\nimport caviService from '../../services/caviService';\nimport { validateCavoData, validateField, isEmpty } from '../../utils/validationUtils';\nimport { redirectToVisualizzaCavi } from '../../utils/navigationUtils';\nimport CavoForm from './CavoForm';\nimport SelezionaCavoForm from './SelezionaCavoForm';\nimport { jsxDEV as _jsxDEV, Fragment as _Fragment } from \"react/jsx-dev-runtime\";\nconst PosaCaviCollegamenti = ({\n  cantiereId: propCantiereId,\n  onSuccess,\n  onError,\n  initialOption = null,\n  preselectedCavo = null,\n  dialogOnly = false\n}) => {\n  _s();\n  // Log del cantiereId all'avvio\n  console.log('PosaCaviCollegamenti - cantiereId da props:', propCantiereId);\n\n  // Aggiungi navigate per la navigazione programmatica\n  const navigate = useNavigate();\n\n  // Se cantiereId non è definito nelle props, prova a recuperarlo dal localStorage\n  const cantiereId = propCantiereId || parseInt(localStorage.getItem('selectedCantiereId'), 10);\n  console.log('PosaCaviCollegamenti - cantiereId effettivo:', cantiereId);\n  const [loading, setLoading] = useState(false);\n  const [selectedOption, setSelectedOption] = useState(null);\n  const [openDialog, setOpenDialog] = useState(false);\n  const [dialogType, setDialogType] = useState('');\n  const [selectedCavo, setSelectedCavo] = useState(null);\n  const [formData, setFormData] = useState({\n    id_cavo: '',\n    metri_posati: '',\n    id_bobina: ''\n  });\n  const [cavoIdInput, setCavoIdInput] = useState('');\n  const [formErrors, setFormErrors] = useState({});\n  const [formWarnings, setFormWarnings] = useState({});\n  const [cavi, setCavi] = useState([]);\n  const [caviLoading, setCaviLoading] = useState(false);\n  const [deleteMode, setDeleteMode] = useState('spare'); // 'spare' o 'delete'\n\n  // Inizializza il componente con l'opzione iniziale se specificata\n  React.useEffect(() => {\n    if (initialOption) {\n      // Imposta direttamente le opzioni invece di chiamare handleOptionSelect\n      // per evitare dipendenze circolari\n      setSelectedOption(initialOption);\n      if (initialOption === 'eliminaCavo') {\n        if (preselectedCavo) {\n          // Se c'è un cavo preselezionato, usalo direttamente\n          setSelectedCavo(preselectedCavo);\n          setDialogType('eliminaCavo');\n          setOpenDialog(true);\n        } else {\n          // Altrimenti carica la lista dei cavi\n          loadCavi('eliminaCavo');\n          setDialogType('eliminaCavo');\n          setOpenDialog(true);\n        }\n      } else if (initialOption === 'modificaCavo') {\n        if (preselectedCavo) {\n          // Se c'è un cavo preselezionato, usalo direttamente per la modifica\n          setSelectedCavo(preselectedCavo);\n          setDialogType('modificaCavo');\n          setFormData({\n            ...preselectedCavo,\n            metri_teorici: preselectedCavo.metri_teorici || '',\n            metratura_reale: preselectedCavo.metratura_reale || '0'\n          });\n          setOpenDialog(true);\n        } else {\n          // Altrimenti carica la lista dei cavi\n          loadCavi('modificaCavo');\n          setDialogType('selezionaCavo');\n          setOpenDialog(true);\n        }\n      } else if (initialOption === 'aggiungiCavo') {\n        setDialogType('aggiungiCavo');\n        setOpenDialog(true);\n      }\n    }\n    // eslint-disable-next-line react-hooks/exhaustive-deps\n  }, [initialOption, preselectedCavo]);\n\n  // Carica i cavi attivi per la selezione\n  const loadCavi = async operationType => {\n    try {\n      setCaviLoading(true);\n\n      // Verifica che cantiereId sia valido\n      if (!cantiereId) {\n        throw new Error('ID cantiere non valido o mancante');\n      }\n      console.log('Caricamento cavi per cantiere:', cantiereId);\n      const caviData = await caviService.getCavi(cantiereId, 0);\n\n      // Filtra i cavi in base al tipo di operazione\n      if (operationType === 'modificaCavo') {\n        // Per modifica cavo, mostra solo i cavi non posati (metratura_reale = 0 e stato != Installato)\n        const caviNonPosati = caviData.filter(cavo => parseFloat(cavo.metratura_reale) === 0 && cavo.stato_installazione !== 'Installato');\n        setCavi(caviNonPosati);\n      } else {\n        // Per altre operazioni, mostra tutti i cavi\n        setCavi(caviData);\n      }\n    } catch (error) {\n      console.error('Errore nel caricamento dei cavi:', error);\n\n      // Gestione più dettagliata dell'errore\n      let errorMessage = 'Errore nel caricamento dei cavi';\n      if (error.response) {\n        // Errore dal server con risposta\n        errorMessage += `: ${error.response.status} ${error.response.statusText}`;\n        if (error.response.data && error.response.data.detail) {\n          errorMessage += ` - ${error.response.data.detail}`;\n        }\n      } else if (error.request) {\n        // Errore di rete senza risposta dal server\n        errorMessage += ': Nessuna risposta dal server. Verifica la connessione di rete.';\n      } else if (error.message) {\n        // Errore con messaggio\n        errorMessage += `: ${error.message}`;\n      }\n      onError(errorMessage);\n    } finally {\n      setCaviLoading(false);\n    }\n  };\n\n  // Gestisce la selezione di un'opzione dal menu\n  const handleOptionSelect = option => {\n    setSelectedOption(option);\n    if (option === 'inserisciMetri' || option === 'modificaBobina') {\n      loadCavi(option);\n      setDialogType(option);\n      setOpenDialog(true);\n    } else if (option === 'aggiungiCavo') {\n      // Apri il dialog per aggiungere un nuovo cavo IMMEDIATAMENTE\n      setDialogType('aggiungiCavo');\n      setOpenDialog(true);\n    } else if (option === 'modificaCavo') {\n      loadCavi('modificaCavo');\n      setDialogType('selezionaCavo');\n      setOpenDialog(true);\n    } else if (option === 'eliminaCavo') {\n      loadCavi('eliminaCavo');\n      setDialogType('eliminaCavo');\n      setOpenDialog(true);\n    }\n  };\n\n  // Gestisce la chiusura del dialog\n  const handleCloseDialog = () => {\n    console.log('Chiusura dialog...');\n    // Reset COMPLETO dello stato del componente\n    setOpenDialog(false);\n    setSelectedOption(null); // RESET selectedOption per eliminare la schermata di caricamento\n    setDialogType(''); // RESET dialogType\n    setSelectedCavo(null);\n    setFormData({\n      id_cavo: '',\n      metri_posati: '',\n      id_bobina: ''\n    });\n    setCavoIdInput('');\n    setFormErrors({});\n    setFormWarnings({});\n    setDeleteMode('spare'); // Reset alla modalità predefinita\n    setLoading(false); // Assicurati che loading sia false quando chiudi il dialog\n\n    // NON chiamare onSuccess quando si annulla il dialog\n    // Il genitore gestirà la chiusura tramite onClose del Dialog\n  };\n\n  // Gestisce la selezione di un cavo\n  const handleCavoSelect = cavo => {\n    setSelectedCavo(cavo);\n    if (dialogType === 'inserisciMetri') {\n      setFormData({\n        ...formData,\n        id_cavo: cavo.id_cavo,\n        metri_posati: ''\n      });\n    } else if (dialogType === 'modificaBobina') {\n      setFormData({\n        ...formData,\n        id_cavo: cavo.id_cavo,\n        id_bobina: cavo.id_bobina || ''\n      });\n    } else if (dialogType === 'selezionaCavo') {\n      setDialogType('modificaCavo');\n      setSelectedCavo(cavo);\n      setFormData({\n        ...cavo,\n        metri_teorici: cavo.metri_teorici || '',\n        metratura_reale: cavo.metratura_reale || '0'\n      });\n    }\n  };\n\n  // Gestisce la ricerca di un cavo per ID\n  const handleSearchCavoById = async () => {\n    if (!cavoIdInput.trim()) {\n      onError('Inserisci un ID cavo valido');\n      return;\n    }\n    try {\n      setCaviLoading(true);\n\n      // Verifica che cantiereId sia valido\n      if (!cantiereId) {\n        throw new Error('ID cantiere non valido o mancante');\n      }\n      console.log('Ricerca cavo con ID:', cavoIdInput, 'per cantiere:', cantiereId);\n      const caviData = await caviService.getCavi(cantiereId, 0);\n      const cavo = caviData.find(c => c.id_cavo === cavoIdInput.trim());\n      if (!cavo) {\n        onError(`Cavo con ID ${cavoIdInput} non trovato`);\n        return;\n      }\n\n      // Verifica se stiamo cercando un cavo per modificarlo\n      if (dialogType === 'selezionaCavo') {\n        // Verifica che il cavo non sia già posato\n        if (parseFloat(cavo.metratura_reale) > 0 || cavo.stato_installazione === 'Installato') {\n          onError(`Il cavo ${cavo.id_cavo} risulta già posato. Utilizza l'opzione \"Modifica bobina cavo posato\" per modificarlo.`);\n          return;\n        }\n      }\n\n      // Seleziona il cavo trovato\n      handleCavoSelect(cavo);\n    } catch (error) {\n      console.error('Errore nel caricamento dei cavi:', error);\n\n      // Gestione più dettagliata dell'errore\n      let errorMessage = 'Errore nel caricamento dei cavi';\n      if (error.response) {\n        // Errore dal server con risposta\n        errorMessage += `: ${error.response.status} ${error.response.statusText}`;\n        if (error.response.data && error.response.data.detail) {\n          errorMessage += ` - ${error.response.data.detail}`;\n        }\n      } else if (error.request) {\n        // Errore di rete senza risposta dal server\n        errorMessage += ': Nessuna risposta dal server. Verifica la connessione di rete.';\n      } else if (error.message) {\n        // Errore con messaggio\n        errorMessage += `: ${error.message}`;\n      }\n      onError(errorMessage);\n    } finally {\n      setCaviLoading(false);\n    }\n  };\n\n  // Gestisce il cambio dell'input dell'ID cavo\n  const handleCavoIdInputChange = e => {\n    setCavoIdInput(e.target.value);\n  };\n\n  // Gestisce il cambio dei valori nel form con validazione\n  const handleFormChange = e => {\n    const {\n      name,\n      value\n    } = e.target;\n\n    // Aggiorna il valore nel form\n    setFormData({\n      ...formData,\n      [name]: value\n    });\n\n    // Valida il campo\n    if (dialogType === 'modificaCavo') {\n      const additionalParams = {};\n      if (name === 'metratura_reale') {\n        additionalParams.metriTeorici = parseFloat(formData.metri_teorici || 0);\n      }\n      const result = validateField(name, value, additionalParams);\n\n      // Aggiorna gli errori\n      setFormErrors(prev => ({\n        ...prev,\n        [name]: !result.valid ? result.message : null\n      }));\n\n      // Aggiorna gli avvisi\n      setFormWarnings(prev => ({\n        ...prev,\n        [name]: result.warning ? result.message : null\n      }));\n    }\n  };\n\n  // Gestisce il salvataggio del form con validazione\n  const handleSave = async () => {\n    try {\n      setLoading(true);\n      if (dialogType === 'inserisciMetri') {\n        // Valida i metri posati\n        if (isEmpty(formData.metri_posati) || isNaN(parseFloat(formData.metri_posati))) {\n          setFormErrors({\n            metri_posati: 'Inserire un valore numerico valido'\n          });\n          setLoading(false);\n          return;\n        }\n        try {\n          await caviService.updateMetriPosati(cantiereId, formData.id_cavo, parseFloat(formData.metri_posati));\n          // Prima chiudi il dialog, poi chiama onSuccess\n          handleCloseDialog();\n          onSuccess('Metri posati aggiornati con successo');\n          // Reindirizza alla pagina di visualizzazione cavi con un ritardo maggiore\n          setTimeout(() => {\n            redirectToVisualizzaCavi(navigate, 1000);\n          }, 500);\n        } catch (error) {\n          console.error('Errore durante l\\'aggiornamento dei metri posati:', error);\n          onError('Errore durante l\\'aggiornamento dei metri posati: ' + (error.message || 'Errore sconosciuto'));\n          setLoading(false);\n        }\n      } else if (dialogType === 'modificaBobina') {\n        try {\n          await caviService.updateBobina(cantiereId, formData.id_cavo, formData.id_bobina);\n          // Prima chiama onSuccess, poi chiudi il dialog\n          onSuccess('Bobina aggiornata con successo');\n          // Chiudi il dialog\n          handleCloseDialog();\n          // Reindirizza alla pagina di visualizzazione cavi con un ritardo maggiore\n          setTimeout(() => {\n            redirectToVisualizzaCavi(navigate, 1000);\n          }, 500);\n        } catch (error) {\n          console.error('Errore durante l\\'aggiornamento della bobina:', error);\n          onError('Errore durante l\\'aggiornamento della bobina: ' + (error.message || 'Errore sconosciuto'));\n          setLoading(false);\n        }\n      } else if (dialogType === 'modificaCavo') {\n        // Validazione completa dei dati del cavo\n        const validation = validateCavoData(formData);\n        if (!validation.isValid) {\n          setFormErrors(validation.errors);\n          setFormWarnings(validation.warnings);\n          setLoading(false);\n          return;\n        }\n\n        // Usa i dati validati\n        const validatedData = validation.validatedData;\n\n        // Rimuovi i campi di sistema che non devono essere modificati\n        const dataToSend = {\n          ...validatedData\n        };\n        delete dataToSend.id_bobina; // Rimuovi id_bobina perché è un campo di sistema\n        delete dataToSend.metratura_reale; // Rimuovi metratura_reale perché è un campo di sistema\n        delete dataToSend.modificato_manualmente; // Rimuovi modificato_manualmente perché è un campo di sistema\n        delete dataToSend.timestamp; // Rimuovi timestamp perché è un campo di sistema\n        delete dataToSend.stato_installazione; // Rimuovi stato_installazione perché è un campo di sistema per cavi non posati\n\n        // Imposta modificato_manualmente a 1 per indicare che il cavo è stato modificato manualmente\n        dataToSend.modificato_manualmente = 1;\n        console.log('Dati inviati al server:', dataToSend);\n        try {\n          console.log('Invio dati al server per aggiornamento cavo:', dataToSend);\n          const result = await caviService.updateCavo(cantiereId, dataToSend.id_cavo, dataToSend);\n          console.log('Risposta dal server dopo aggiornamento cavo:', result);\n\n          // Prima chiama onSuccess, poi chiudi il dialog\n          onSuccess('Cavo modificato con successo');\n          // Chiudi il dialog\n          handleCloseDialog();\n          // Reindirizza alla pagina di visualizzazione cavi con un ritardo maggiore\n          setTimeout(() => {\n            redirectToVisualizzaCavi(navigate, 1000);\n          }, 500);\n          return;\n        } catch (error) {\n          console.error('Errore durante l\\'aggiornamento del cavo:', error);\n\n          // Gestione più dettagliata dell'errore\n          let errorMessage = 'Errore durante l\\'aggiornamento del cavo';\n          if (error.response) {\n            // Errore dal server con risposta\n            errorMessage += `: ${error.response.status} ${error.response.statusText}`;\n            if (error.response.data && error.response.data.detail) {\n              errorMessage += ` - ${error.response.data.detail}`;\n            }\n          } else if (error.request) {\n            // Errore di rete senza risposta dal server\n            errorMessage += ': Nessuna risposta dal server. Verifica la connessione di rete.';\n\n            // Anche se c'è un errore di rete, la modifica potrebbe essere stata salvata\n            // Quindi consideriamo l'operazione come riuscita\n            console.log('Considerando l\\'operazione come riuscita nonostante l\\'errore di rete');\n            // Prima chiama onSuccess, poi chiudi il dialog\n            onSuccess('Cavo modificato con successo (la connessione potrebbe essere instabile)');\n            // Chiudi il dialog\n            handleCloseDialog();\n            // Reindirizza alla pagina di visualizzazione cavi con un ritardo maggiore\n            setTimeout(() => {\n              redirectToVisualizzaCavi(navigate, 1000);\n            }, 500);\n            return;\n          } else if (error.message) {\n            // Errore con messaggio\n            errorMessage += `: ${error.message}`;\n\n            // Se il messaggio indica che la modifica potrebbe essere stata salvata comunque\n            if (error.message.includes('La modifica potrebbe essere stata salvata')) {\n              console.log('Considerando l\\'operazione come riuscita nonostante l\\'errore');\n              handleCloseDialog();\n              onSuccess('Cavo modificato con successo (la connessione potrebbe essere instabile)');\n              // Reindirizza alla pagina di visualizzazione cavi con un ritardo maggiore\n              redirectToVisualizzaCavi(navigate, 1000);\n              return;\n            }\n          }\n          onError(errorMessage);\n          setLoading(false);\n          return;\n        }\n\n        // Mostra avvisi se presenti\n        if (Object.keys(validation.warnings).length > 0) {\n          const warningMessages = Object.values(validation.warnings).join('\\n');\n          console.warn('Avvisi durante il salvataggio:', warningMessages);\n        }\n      } else if (dialogType === 'eliminaCavo') {\n        // Usa il cavo corretto (selectedCavo o preselectedCavo)\n        const cavoToDelete = selectedCavo || preselectedCavo;\n        if (!cavoToDelete) {\n          onError('Nessun cavo selezionato per l\\'eliminazione');\n          return;\n        }\n\n        // Verifica se il cavo è installato\n        const isInstalled = cavoToDelete.stato_installazione === 'Installato' || cavoToDelete.metratura_reale && cavoToDelete.metratura_reale > 0;\n        if (isInstalled) {\n          // Se è installato, marca solo come SPARE\n          console.log('Marcando cavo installato come SPARE:', cavoToDelete.id_cavo);\n          try {\n            // Prima prova con markCavoAsSpare\n            const result = await caviService.markCavoAsSpare(cantiereId, cavoToDelete.id_cavo);\n            console.log('Risultato marcatura SPARE:', result);\n            console.log('Nuovo valore modificato_manualmente:', result.modificato_manualmente);\n            // Chiudi il dialog prima di chiamare onSuccess\n            handleCloseDialog();\n            onSuccess(`Cavo ${cavoToDelete.id_cavo} marcato come SPARE con successo`);\n            // Reindirizza alla pagina di visualizzazione cavi con un ritardo\n            redirectToVisualizzaCavi(navigate, 500);\n          } catch (markError) {\n            console.error('Errore con markCavoAsSpare, tentativo con deleteCavo mode=spare:', markError);\n            // Se fallisce, prova con deleteCavo mode=spare\n            const result = await caviService.deleteCavo(cantiereId, cavoToDelete.id_cavo, 'spare');\n            console.log('Risultato marcatura SPARE con deleteCavo:', result);\n            // Chiudi il dialog prima di chiamare onSuccess\n            handleCloseDialog();\n            onSuccess(`Cavo ${cavoToDelete.id_cavo} marcato come SPARE con successo`);\n          }\n        } else {\n          // Se non è installato, usa la modalità selezionata (SPARE o DELETE)\n          console.log('Eliminando cavo non installato con modalità:', deleteMode);\n          const result = await caviService.deleteCavo(cantiereId, cavoToDelete.id_cavo, deleteMode);\n          console.log('Risultato eliminazione/marcatura:', result);\n          // Chiudi il dialog prima di chiamare onSuccess\n          handleCloseDialog();\n          onSuccess(`Cavo ${cavoToDelete.id_cavo} ${deleteMode === 'spare' ? 'marcato come SPARE' : 'eliminato'} con successo`);\n        }\n      }\n\n      // Non chiamare handleCloseDialog() qui, perché il dialog verrà chiuso dal genitore\n      // quando viene chiamato onSuccess()\n    } catch (error) {\n      console.error('Errore durante l\\'operazione:', error);\n\n      // Gestione più dettagliata dell'errore\n      let errorMessage = 'Errore sconosciuto';\n      if (error.detail) {\n        // Errore dal backend con dettaglio\n        errorMessage = error.detail;\n      } else if (error.message) {\n        // Errore con messaggio\n        errorMessage = error.message;\n      } else if (typeof error === 'string') {\n        // Errore come stringa\n        errorMessage = error;\n      }\n      onError('Errore durante l\\'operazione: ' + errorMessage);\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  // Renderizza il dialog in base al tipo\n  const renderDialog = () => {\n    if (dialogType === 'aggiungiCavo' || dialogType === 'modificaCavo') {\n      // Dialog unificato per aggiungere e modificare cavi\n      const isEditMode = dialogType === 'modificaCavo';\n      const cavoToEdit = selectedCavo || preselectedCavo;\n      return /*#__PURE__*/_jsxDEV(Dialog, {\n        open: openDialog,\n        onClose: handleCloseDialog,\n        maxWidth: \"md\",\n        fullWidth: true,\n        PaperProps: {\n          sx: {\n            width: '80%',\n            // Allargato di 2cm (rispetto a sm che è circa 60%)\n            minHeight: '80vh',\n            // Allungato di 1cm (aggiungendo altezza minima)\n            maxHeight: '90vh',\n            overflow: 'auto'\n          }\n        },\n        children: [/*#__PURE__*/_jsxDEV(DialogTitle, {\n          sx: {\n            pb: 1\n          },\n          children: isEditMode ? 'Modifica Cavo' : 'Aggiungi Nuovo Cavo'\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 577,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(DialogContent, {\n          sx: {\n            pt: 0,\n            pb: 1\n          },\n          children: /*#__PURE__*/_jsxDEV(Box, {\n            sx: {\n              mt: 0\n            },\n            children: isEditMode && cavoToEdit ? /*#__PURE__*/_jsxDEV(CavoForm, {\n              mode: \"edit\",\n              initialData: formData.id_cavo ? formData : cavoToEdit,\n              cantiereId: cantiereId,\n              onSubmit: async validatedData => {\n                try {\n                  // Rimuovi i campi di sistema che non devono essere modificati\n                  const dataToSend = {\n                    ...validatedData\n                  };\n                  delete dataToSend.id_bobina; // Rimuovi id_bobina perché è un campo di sistema\n                  delete dataToSend.metratura_reale; // Rimuovi metratura_reale perché è un campo di sistema\n                  delete dataToSend.modificato_manualmente; // Rimuovi modificato_manualmente perché è un campo di sistema\n                  delete dataToSend.timestamp; // Rimuovi timestamp perché è un campo di sistema\n                  delete dataToSend.stato_installazione; // Rimuovi stato_installazione perché è un campo di sistema per cavi non posati\n\n                  // Imposta modificato_manualmente a 1 per indicare che il cavo è stato modificato manualmente\n                  dataToSend.modificato_manualmente = 1;\n                  await caviService.updateCavo(cantiereId, dataToSend.id_cavo, dataToSend);\n                  return true;\n                } catch (error) {\n                  throw error;\n                }\n              },\n              onSuccess: message => {\n                handleCloseDialog(); // Prima resetta lo stato\n                onSuccess(message); // Poi notifica il successo\n              },\n              onError: onError,\n              onCancel: handleCloseDialog,\n              isDialog: true\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 583,\n              columnNumber: 17\n            }, this) : isEditMode && !cavoToEdit ? /*#__PURE__*/_jsxDEV(SelezionaCavoForm, {\n              cantiereId: cantiereId,\n              onSuccess: message => {\n                handleCloseDialog(); // Prima resetta lo stato\n                onSuccess(message); // Poi notifica il successo\n              },\n              onError: onError,\n              isDialog: true\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 615,\n              columnNumber: 17\n            }, this) : /*#__PURE__*/_jsxDEV(CavoForm, {\n              mode: \"add\",\n              cantiereId: cantiereId,\n              onSubmit: async validatedData => {\n                try {\n                  await caviService.createCavo(cantiereId, validatedData);\n                  return true;\n                } catch (error) {\n                  throw error;\n                }\n              },\n              onSuccess: message => {\n                handleCloseDialog(); // Prima resetta lo stato\n                onSuccess(message); // Poi notifica il successo\n              },\n              onError: onError,\n              onCancel: handleCloseDialog,\n              isDialog: true\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 625,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 581,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 580,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 563,\n        columnNumber: 9\n      }, this);\n    } else if (dialogType === 'inserisciMetri') {\n      return /*#__PURE__*/_jsxDEV(Dialog, {\n        open: openDialog,\n        onClose: handleCloseDialog,\n        maxWidth: \"sm\",\n        fullWidth: true,\n        children: [/*#__PURE__*/_jsxDEV(DialogTitle, {\n          children: \"Inserisci Metri Posati\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 653,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(DialogContent, {\n          children: caviLoading ? /*#__PURE__*/_jsxDEV(CircularProgress, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 656,\n            columnNumber: 15\n          }, this) : cavi.length === 0 ? /*#__PURE__*/_jsxDEV(Alert, {\n            severity: \"info\",\n            children: \"Nessun cavo disponibile\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 658,\n            columnNumber: 15\n          }, this) : !selectedCavo ? /*#__PURE__*/_jsxDEV(Box, {\n            children: [/*#__PURE__*/_jsxDEV(Typography, {\n              variant: \"subtitle1\",\n              gutterBottom: true,\n              children: \"Seleziona un cavo:\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 661,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(List, {\n              children: cavi.map(cavo => /*#__PURE__*/_jsxDEV(ListItem, {\n                button: true,\n                onClick: () => handleCavoSelect(cavo),\n                children: /*#__PURE__*/_jsxDEV(ListItemText, {\n                  primary: cavo.id_cavo,\n                  secondary: `${cavo.tipologia || 'N/A'} - Da: ${cavo.ubicazione_partenza || 'N/A'} A: ${cavo.ubicazione_arrivo || 'N/A'}`\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 671,\n                  columnNumber: 23\n                }, this)\n              }, cavo.id_cavo, false, {\n                fileName: _jsxFileName,\n                lineNumber: 666,\n                columnNumber: 21\n              }, this))\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 664,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 660,\n            columnNumber: 15\n          }, this) : /*#__PURE__*/_jsxDEV(Box, {\n            sx: {\n              mt: 2\n            },\n            children: [/*#__PURE__*/_jsxDEV(Typography, {\n              variant: \"subtitle1\",\n              gutterBottom: true,\n              children: [\"Cavo selezionato: \", selectedCavo.id_cavo]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 681,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(Typography, {\n              variant: \"body2\",\n              gutterBottom: true,\n              children: [\"Metri teorici: \", selectedCavo.metri_teorici || 'N/A']\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 684,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(Typography, {\n              variant: \"body2\",\n              gutterBottom: true,\n              children: [\"Metratura attuale: \", selectedCavo.metratura_reale || '0']\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 687,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(TextField, {\n              margin: \"dense\",\n              name: \"metri_posati\",\n              label: \"Metri posati da aggiungere\",\n              type: \"number\",\n              fullWidth: true,\n              variant: \"outlined\",\n              value: formData.metri_posati,\n              onChange: handleFormChange,\n              required: true,\n              error: !!formErrors.metri_posati,\n              helperText: formErrors.metri_posati,\n              sx: {\n                mt: 2\n              }\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 690,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 680,\n            columnNumber: 15\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 654,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(DialogActions, {\n          children: selectedCavo && /*#__PURE__*/_jsxDEV(Button, {\n            onClick: handleSave,\n            disabled: loading || !formData.metri_posati,\n            startIcon: loading ? /*#__PURE__*/_jsxDEV(CircularProgress, {\n              size: 20\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 712,\n              columnNumber: 38\n            }, this) : /*#__PURE__*/_jsxDEV(SaveIcon, {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 712,\n              columnNumber: 71\n            }, this),\n            children: \"Salva\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 709,\n            columnNumber: 15\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 707,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 652,\n        columnNumber: 9\n      }, this);\n    } else if (dialogType === 'modificaBobina') {\n      return /*#__PURE__*/_jsxDEV(Dialog, {\n        open: openDialog,\n        onClose: handleCloseDialog,\n        maxWidth: \"md\",\n        fullWidth: true,\n        children: [/*#__PURE__*/_jsxDEV(DialogTitle, {\n          children: \"Modifica Bobina Cavo Posato\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 723,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(DialogContent, {\n          children: caviLoading ? /*#__PURE__*/_jsxDEV(CircularProgress, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 726,\n            columnNumber: 15\n          }, this) : cavi.length === 0 ? /*#__PURE__*/_jsxDEV(Alert, {\n            severity: \"info\",\n            children: \"Nessun cavo disponibile\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 728,\n            columnNumber: 15\n          }, this) : !selectedCavo ? /*#__PURE__*/_jsxDEV(Box, {\n            children: [/*#__PURE__*/_jsxDEV(Typography, {\n              variant: \"subtitle1\",\n              gutterBottom: true,\n              children: \"Seleziona un cavo:\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 731,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(List, {\n              children: cavi.map(cavo => /*#__PURE__*/_jsxDEV(ListItem, {\n                button: true,\n                onClick: () => handleCavoSelect(cavo),\n                children: /*#__PURE__*/_jsxDEV(ListItemText, {\n                  primary: cavo.id_cavo,\n                  secondary: `Bobina attuale: ${cavo.id_bobina ? cavo.id_bobina === 'BOBINA_VUOTA' ? 'BOBINA VUOTA' : cavo.id_bobina : 'BOBINA VUOTA'}`\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 741,\n                  columnNumber: 23\n                }, this)\n              }, cavo.id_cavo, false, {\n                fileName: _jsxFileName,\n                lineNumber: 736,\n                columnNumber: 21\n              }, this))\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 734,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 730,\n            columnNumber: 15\n          }, this) : /*#__PURE__*/_jsxDEV(Box, {\n            sx: {\n              mt: 2\n            },\n            children: [/*#__PURE__*/_jsxDEV(Typography, {\n              variant: \"subtitle1\",\n              gutterBottom: true,\n              children: [\"Cavo selezionato: \", selectedCavo.id_cavo]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 751,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(Typography, {\n              variant: \"body2\",\n              gutterBottom: true,\n              children: [\"Bobina attuale: \", selectedCavo.id_bobina ? selectedCavo.id_bobina === 'BOBINA_VUOTA' ? 'BOBINA VUOTA' : selectedCavo.id_bobina : 'BOBINA VUOTA']\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 754,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(TextField, {\n              margin: \"dense\",\n              name: \"id_bobina\",\n              label: \"ID Bobina\",\n              fullWidth: true,\n              variant: \"outlined\",\n              value: formData.id_bobina,\n              onChange: handleFormChange,\n              sx: {\n                mt: 2\n              }\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 757,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 750,\n            columnNumber: 15\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 724,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(DialogActions, {\n          children: selectedCavo && /*#__PURE__*/_jsxDEV(Button, {\n            onClick: handleSave,\n            disabled: loading,\n            startIcon: loading ? /*#__PURE__*/_jsxDEV(CircularProgress, {\n              size: 20\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 775,\n              columnNumber: 38\n            }, this) : /*#__PURE__*/_jsxDEV(SaveIcon, {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 775,\n              columnNumber: 71\n            }, this),\n            children: \"Salva\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 772,\n            columnNumber: 15\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 770,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 722,\n        columnNumber: 9\n      }, this);\n    } else if (dialogType === 'selezionaCavo') {\n      return /*#__PURE__*/_jsxDEV(Dialog, {\n        open: openDialog,\n        onClose: handleCloseDialog,\n        maxWidth: \"sm\",\n        fullWidth: true,\n        children: [/*#__PURE__*/_jsxDEV(DialogTitle, {\n          children: \"Modifica Cavo\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 786,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(DialogContent, {\n          children: [/*#__PURE__*/_jsxDEV(Alert, {\n            severity: \"info\",\n            sx: {\n              mb: 2\n            },\n            children: \"Puoi modificare solo i cavi non ancora posati (metratura = 0 e stato diverso da \\\"Installato\\\"). Per modificare cavi gi\\xE0 posati, utilizzare l'opzione \\\"Modifica bobina cavo posato\\\".\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 788,\n            columnNumber: 13\n          }, this), caviLoading ? /*#__PURE__*/_jsxDEV(CircularProgress, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 794,\n            columnNumber: 15\n          }, this) : !selectedCavo ? /*#__PURE__*/_jsxDEV(Box, {\n            sx: {\n              p: 2\n            },\n            children: [/*#__PURE__*/_jsxDEV(Typography, {\n              variant: \"subtitle1\",\n              gutterBottom: true,\n              children: \"Inserisci l'ID del cavo da modificare:\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 797,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(Box, {\n              sx: {\n                display: 'flex',\n                alignItems: 'center',\n                mt: 2\n              },\n              children: [/*#__PURE__*/_jsxDEV(TextField, {\n                fullWidth: true,\n                label: \"ID Cavo\",\n                variant: \"outlined\",\n                value: cavoIdInput,\n                onChange: handleCavoIdInputChange,\n                placeholder: \"Inserisci l'ID del cavo\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 801,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(Button, {\n                variant: \"contained\",\n                color: \"primary\",\n                onClick: handleSearchCavoById,\n                disabled: caviLoading || !cavoIdInput.trim(),\n                sx: {\n                  ml: 2,\n                  minWidth: '120px'\n                },\n                children: caviLoading ? /*#__PURE__*/_jsxDEV(CircularProgress, {\n                  size: 24\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 816,\n                  columnNumber: 36\n                }, this) : \"Cerca\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 809,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 800,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 796,\n            columnNumber: 15\n          }, this) : null]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 787,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(DialogActions, {}, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 822,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 785,\n        columnNumber: 9\n      }, this);\n    } else if (dialogType === 'eliminaCavo') {\n      // Se c'è un preselectedCavo, usalo direttamente invece di selectedCavo\n      const cavoToDelete = selectedCavo || preselectedCavo;\n      // Verifica se il cavo selezionato è installato\n      const isInstalled = cavoToDelete && (cavoToDelete.stato_installazione === 'Installato' || cavoToDelete.metratura_reale && cavoToDelete.metratura_reale > 0);\n      return /*#__PURE__*/_jsxDEV(Dialog, {\n        open: openDialog,\n        onClose: handleCloseDialog,\n        maxWidth: \"sm\",\n        fullWidth: true,\n        children: [/*#__PURE__*/_jsxDEV(DialogTitle, {\n          children: !cavoToDelete ? 'Elimina Cavo' : isInstalled ? 'Marca Cavo come SPARE' : 'Elimina Cavo'\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 834,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(DialogContent, {\n          children: caviLoading ? /*#__PURE__*/_jsxDEV(CircularProgress, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 840,\n            columnNumber: 15\n          }, this) : !cavoToDelete ? /*#__PURE__*/_jsxDEV(Box, {\n            sx: {\n              p: 2\n            },\n            children: [/*#__PURE__*/_jsxDEV(Typography, {\n              variant: \"subtitle1\",\n              gutterBottom: true,\n              children: \"Inserisci l'ID del cavo da eliminare:\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 843,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(Box, {\n              sx: {\n                display: 'flex',\n                alignItems: 'center',\n                mt: 2\n              },\n              children: [/*#__PURE__*/_jsxDEV(TextField, {\n                fullWidth: true,\n                label: \"ID Cavo\",\n                variant: \"outlined\",\n                value: cavoIdInput,\n                onChange: handleCavoIdInputChange,\n                placeholder: \"Inserisci l'ID del cavo\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 847,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(Button, {\n                variant: \"contained\",\n                color: \"primary\",\n                onClick: handleSearchCavoById,\n                disabled: caviLoading || !cavoIdInput.trim(),\n                sx: {\n                  ml: 2,\n                  minWidth: '120px'\n                },\n                children: caviLoading ? /*#__PURE__*/_jsxDEV(CircularProgress, {\n                  size: 24\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 862,\n                  columnNumber: 36\n                }, this) : \"Cerca\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 855,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 846,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 842,\n            columnNumber: 15\n          }, this) : dialogType === 'eliminaCavo' && isInstalled ? /*#__PURE__*/_jsxDEV(_Fragment, {\n            children: [/*#__PURE__*/_jsxDEV(DialogContentText, {\n              children: [\"Il cavo \", /*#__PURE__*/_jsxDEV(\"strong\", {\n                children: cavoToDelete.id_cavo\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 869,\n                columnNumber: 27\n              }, this), \" risulta installato o parzialmente posato.\", cavoToDelete.metratura_reale > 0 && /*#__PURE__*/_jsxDEV(_Fragment, {\n                children: [\" Metri posati: \", /*#__PURE__*/_jsxDEV(\"strong\", {\n                  children: [cavoToDelete.metratura_reale, \" m\"]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 871,\n                  columnNumber: 38\n                }, this), \".\"]\n              }, void 0, true)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 868,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(DialogContentText, {\n              sx: {\n                mt: 2\n              },\n              children: \"Non \\xE8 possibile eliminarlo definitivamente. Vuoi marcarlo come SPARE/consumato?\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 874,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true) : dialogType === 'eliminaCavo' ? /*#__PURE__*/_jsxDEV(_Fragment, {\n            children: [/*#__PURE__*/_jsxDEV(DialogContentText, {\n              children: [\"Stai per eliminare il cavo \", /*#__PURE__*/_jsxDEV(\"strong\", {\n                children: cavoToDelete.id_cavo\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 881,\n                columnNumber: 46\n              }, this), \".\"]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 880,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(FormControl, {\n              component: \"fieldset\",\n              sx: {\n                mt: 2\n              },\n              children: [/*#__PURE__*/_jsxDEV(FormLabel, {\n                component: \"legend\",\n                children: \"Scegli l'operazione da eseguire:\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 885,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(RadioGroup, {\n                value: deleteMode,\n                onChange: e => setDeleteMode(e.target.value),\n                children: [/*#__PURE__*/_jsxDEV(FormControlLabel, {\n                  value: \"spare\",\n                  control: /*#__PURE__*/_jsxDEV(Radio, {}, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 892,\n                    columnNumber: 32\n                  }, this),\n                  label: \"Marca come SPARE (mantiene il cavo nel database ma lo contrassegna come non attivo)\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 890,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(FormControlLabel, {\n                  value: \"delete\",\n                  control: /*#__PURE__*/_jsxDEV(Radio, {}, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 897,\n                    columnNumber: 32\n                  }, this),\n                  label: \"Elimina definitivamente (rimuove completamente il cavo dal database)\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 895,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 886,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 884,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true) : null\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 838,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(DialogActions, {\n          children: dialogType === 'eliminaCavo' && cavoToDelete && /*#__PURE__*/_jsxDEV(Button, {\n            onClick: handleSave,\n            disabled: loading,\n            color: isInstalled ? \"warning\" : \"error\",\n            startIcon: loading ? /*#__PURE__*/_jsxDEV(CircularProgress, {\n              size: 20\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 911,\n              columnNumber: 38\n            }, this) : isInstalled ? /*#__PURE__*/_jsxDEV(WarningIcon, {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 911,\n              columnNumber: 85\n            }, this) : /*#__PURE__*/_jsxDEV(DeleteIcon, {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 911,\n              columnNumber: 103\n            }, this),\n            children: isInstalled ? \"Marca come SPARE\" : deleteMode === 'spare' ? \"Marca come SPARE\" : \"Elimina definitivamente\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 907,\n            columnNumber: 15\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 905,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 833,\n        columnNumber: 9\n      }, this);\n    }\n    return null;\n  };\n\n  // Funzione per renderizzare solo il contenuto (senza Dialog wrapper)\n  const renderDialogContent = () => {\n    if (dialogType === 'modificaCavo') {\n      // Se c'è un preselectedCavo, usalo direttamente invece di selectedCavo\n      const cavoToEdit = selectedCavo || preselectedCavo;\n      return /*#__PURE__*/_jsxDEV(_Fragment, {\n        children: [/*#__PURE__*/_jsxDEV(DialogTitle, {\n          sx: {\n            pb: 1\n          },\n          children: \"Modifica Cavo\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 932,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(DialogContent, {\n          sx: {\n            pt: 0,\n            pb: 1\n          },\n          children: /*#__PURE__*/_jsxDEV(Box, {\n            sx: {\n              mt: 0\n            },\n            children: cavoToEdit ? /*#__PURE__*/_jsxDEV(CavoForm, {\n              mode: \"edit\",\n              initialData: formData.id_cavo ? formData : cavoToEdit,\n              cantiereId: cantiereId,\n              onSubmit: async validatedData => {\n                try {\n                  // Rimuovi i campi di sistema che non devono essere modificati\n                  const dataToSend = {\n                    ...validatedData\n                  };\n                  delete dataToSend.id_bobina; // Rimuovi id_bobina perché è un campo di sistema\n                  delete dataToSend.metratura_reale; // Rimuovi metratura_reale perché è un campo di sistema\n                  delete dataToSend.modificato_manualmente; // Rimuovi modificato_manualmente perché è un campo di sistema\n                  delete dataToSend.timestamp; // Rimuovi timestamp perché è un campo di sistema\n                  delete dataToSend.stato_installazione; // Rimuovi stato_installazione perché è un campo di sistema per cavi non posati\n\n                  // Imposta modificato_manualmente a 1 per indicare che il cavo è stato modificato manualmente\n                  dataToSend.modificato_manualmente = 1;\n                  await caviService.updateCavo(cantiereId, dataToSend.id_cavo, dataToSend);\n                  return true;\n                } catch (error) {\n                  throw error;\n                }\n              },\n              onSuccess: message => {\n                onSuccess(message);\n                // Non chiamare handleCloseDialog() in modalità dialogOnly\n              },\n              onError: onError,\n              isDialog: true\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 936,\n              columnNumber: 17\n            }, this) : /*#__PURE__*/_jsxDEV(SelezionaCavoForm, {\n              cantiereId: cantiereId,\n              onSuccess: message => {\n                onSuccess(message);\n                // Non chiamare handleCloseDialog() in modalità dialogOnly\n              },\n              onError: onError,\n              isDialog: true\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 968,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 934,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 933,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true);\n    } else if (dialogType === 'eliminaCavo') {\n      // Se c'è un preselectedCavo, usalo direttamente invece di selectedCavo\n      const cavoToDelete = selectedCavo || preselectedCavo;\n      // Verifica se il cavo selezionato è installato\n      const isInstalled = cavoToDelete && (cavoToDelete.stato_installazione === 'Installato' || cavoToDelete.metratura_reale && cavoToDelete.metratura_reale > 0);\n      return /*#__PURE__*/_jsxDEV(_Fragment, {\n        children: [/*#__PURE__*/_jsxDEV(DialogTitle, {\n          children: !cavoToDelete ? 'Elimina Cavo' : isInstalled ? 'Marca Cavo come SPARE' : 'Elimina Cavo'\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 991,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(DialogContent, {\n          children: caviLoading ? /*#__PURE__*/_jsxDEV(CircularProgress, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 997,\n            columnNumber: 15\n          }, this) : !cavoToDelete ? /*#__PURE__*/_jsxDEV(Box, {\n            sx: {\n              p: 2\n            },\n            children: [/*#__PURE__*/_jsxDEV(Typography, {\n              variant: \"subtitle1\",\n              gutterBottom: true,\n              children: \"Inserisci l'ID del cavo da eliminare:\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1000,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(Box, {\n              sx: {\n                display: 'flex',\n                alignItems: 'center',\n                mt: 2\n              },\n              children: [/*#__PURE__*/_jsxDEV(TextField, {\n                fullWidth: true,\n                label: \"ID Cavo\",\n                variant: \"outlined\",\n                value: cavoIdInput,\n                onChange: handleCavoIdInputChange,\n                placeholder: \"Inserisci l'ID del cavo\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1004,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(Button, {\n                variant: \"contained\",\n                color: \"primary\",\n                onClick: handleSearchCavoById,\n                disabled: caviLoading || !cavoIdInput.trim(),\n                sx: {\n                  ml: 2,\n                  minWidth: '120px'\n                },\n                children: caviLoading ? /*#__PURE__*/_jsxDEV(CircularProgress, {\n                  size: 24\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1019,\n                  columnNumber: 36\n                }, this) : \"Cerca\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1012,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 1003,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 999,\n            columnNumber: 15\n          }, this) : dialogType === 'eliminaCavo' && isInstalled ? /*#__PURE__*/_jsxDEV(_Fragment, {\n            children: [/*#__PURE__*/_jsxDEV(DialogContentText, {\n              children: [\"Il cavo \", /*#__PURE__*/_jsxDEV(\"strong\", {\n                children: cavoToDelete.id_cavo\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1026,\n                columnNumber: 27\n              }, this), \" risulta installato o parzialmente posato.\", cavoToDelete.metratura_reale > 0 && /*#__PURE__*/_jsxDEV(_Fragment, {\n                children: [\" Metri posati: \", /*#__PURE__*/_jsxDEV(\"strong\", {\n                  children: [cavoToDelete.metratura_reale, \" m\"]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1028,\n                  columnNumber: 38\n                }, this), \".\"]\n              }, void 0, true)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 1025,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(DialogContentText, {\n              sx: {\n                mt: 2\n              },\n              children: \"Non \\xE8 possibile eliminarlo definitivamente. Vuoi marcarlo come SPARE/consumato?\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1031,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true) : dialogType === 'eliminaCavo' ? /*#__PURE__*/_jsxDEV(_Fragment, {\n            children: [/*#__PURE__*/_jsxDEV(DialogContentText, {\n              children: [\"Stai per eliminare il cavo \", /*#__PURE__*/_jsxDEV(\"strong\", {\n                children: cavoToDelete.id_cavo\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1038,\n                columnNumber: 46\n              }, this), \".\"]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 1037,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(FormControl, {\n              component: \"fieldset\",\n              sx: {\n                mt: 2\n              },\n              children: [/*#__PURE__*/_jsxDEV(FormLabel, {\n                component: \"legend\",\n                children: \"Scegli l'operazione da eseguire:\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1042,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(RadioGroup, {\n                value: deleteMode,\n                onChange: e => setDeleteMode(e.target.value),\n                children: [/*#__PURE__*/_jsxDEV(FormControlLabel, {\n                  value: \"spare\",\n                  control: /*#__PURE__*/_jsxDEV(Radio, {}, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1049,\n                    columnNumber: 32\n                  }, this),\n                  label: \"Marca come SPARE (mantiene il cavo nel database ma lo contrassegna come non attivo)\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1047,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(FormControlLabel, {\n                  value: \"delete\",\n                  control: /*#__PURE__*/_jsxDEV(Radio, {}, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1054,\n                    columnNumber: 32\n                  }, this),\n                  label: \"Elimina definitivamente (rimuove completamente il cavo dal database)\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1052,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 1043,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 1041,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true) : null\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 995,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(DialogActions, {\n          children: dialogType === 'eliminaCavo' && cavoToDelete && /*#__PURE__*/_jsxDEV(Button, {\n            onClick: handleSave,\n            disabled: loading,\n            color: isInstalled ? \"warning\" : \"error\",\n            startIcon: loading ? /*#__PURE__*/_jsxDEV(CircularProgress, {\n              size: 20\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1068,\n              columnNumber: 38\n            }, this) : isInstalled ? /*#__PURE__*/_jsxDEV(WarningIcon, {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1068,\n              columnNumber: 85\n            }, this) : /*#__PURE__*/_jsxDEV(DeleteIcon, {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1068,\n              columnNumber: 103\n            }, this),\n            children: isInstalled ? \"Marca come SPARE\" : deleteMode === 'spare' ? \"Marca come SPARE\" : \"Elimina definitivamente\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 1064,\n            columnNumber: 15\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 1062,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true);\n    } else if (dialogType === 'aggiungiCavo') {\n      return /*#__PURE__*/_jsxDEV(_Fragment, {\n        children: [/*#__PURE__*/_jsxDEV(DialogTitle, {\n          sx: {\n            pb: 1\n          },\n          children: \"Aggiungi Nuovo Cavo\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 1079,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(DialogContent, {\n          sx: {\n            pt: 0,\n            pb: 1\n          },\n          children: /*#__PURE__*/_jsxDEV(Box, {\n            sx: {\n              mt: 0\n            },\n            children: /*#__PURE__*/_jsxDEV(CavoForm, {\n              mode: \"add\",\n              cantiereId: cantiereId,\n              onSubmit: async validatedData => {\n                try {\n                  await caviService.createCavo(cantiereId, validatedData);\n                  return true;\n                } catch (error) {\n                  throw error;\n                }\n              },\n              onSuccess: message => {\n                onSuccess(message);\n                // Non chiamare handleCloseDialog() in modalità dialogOnly\n              },\n              onError: onError,\n              isDialog: true\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1082,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 1081,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 1080,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true);\n    }\n    return null;\n  };\n\n  // Se è in modalità dialogOnly, renderizza solo il contenuto (senza Dialog wrapper)\n  if (dialogOnly) {\n    return renderDialogContent();\n  }\n  return /*#__PURE__*/_jsxDEV(Box, {\n    sx: {\n      display: 'flex'\n    },\n    children: [/*#__PURE__*/_jsxDEV(Box, {\n      sx: {\n        width: '280px',\n        mr: 3\n      },\n      children: /*#__PURE__*/_jsxDEV(Paper, {\n        sx: {\n          p: 2,\n          mb: 3\n        },\n        children: [/*#__PURE__*/_jsxDEV(Typography, {\n          variant: \"h6\",\n          gutterBottom: true,\n          children: \"Posa Cavi e Collegamenti\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 1120,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Divider, {\n          sx: {\n            mb: 2\n          }\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 1123,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(List, {\n          component: \"nav\",\n          dense: true,\n          children: [/*#__PURE__*/_jsxDEV(ListItemButton, {\n            onClick: () => handleOptionSelect('inserisciMetri'),\n            children: [/*#__PURE__*/_jsxDEV(ListItemIcon, {\n              children: /*#__PURE__*/_jsxDEV(CableIcon, {}, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1127,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1126,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(ListItemText, {\n              primary: \"1. Inserisci metri posati\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1129,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 1125,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(ListItemButton, {\n            onClick: () => handleOptionSelect('modificaBobina'),\n            children: [/*#__PURE__*/_jsxDEV(ListItemIcon, {\n              children: /*#__PURE__*/_jsxDEV(EditIcon, {}, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1134,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1133,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(ListItemText, {\n              primary: \"2. Modifica bobina cavo posato\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1136,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 1132,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(ListItemButton, {\n            onClick: () => handleOptionSelect('aggiungiCavo'),\n            children: [/*#__PURE__*/_jsxDEV(ListItemIcon, {\n              children: /*#__PURE__*/_jsxDEV(AddIcon, {}, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1141,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1140,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(ListItemText, {\n              primary: \"3. Aggiungi nuovo cavo\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1143,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 1139,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 1124,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 1119,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 1118,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Box, {\n      sx: {\n        flexGrow: 1\n      },\n      children: /*#__PURE__*/_jsxDEV(Paper, {\n        sx: {\n          p: 3,\n          minHeight: '300px',\n          display: 'flex',\n          alignItems: 'center',\n          justifyContent: 'center'\n        },\n        children: [!selectedOption && /*#__PURE__*/_jsxDEV(Typography, {\n          variant: \"body1\",\n          children: \"Seleziona un'opzione dal menu a sinistra per iniziare.\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 1153,\n          columnNumber: 13\n        }, this), selectedOption && !openDialog && /*#__PURE__*/_jsxDEV(Box, {\n          sx: {\n            textAlign: 'center'\n          },\n          children: [/*#__PURE__*/_jsxDEV(Typography, {\n            variant: \"h6\",\n            gutterBottom: true,\n            children: [selectedOption === 'inserisciMetri' && 'Inserisci metri posati', selectedOption === 'modificaCavo' && 'Modifica cavo', selectedOption === 'aggiungiCavo' && 'Aggiungi nuovo cavo', selectedOption === 'eliminaCavo' && 'Elimina cavo', selectedOption === 'modificaBobina' && 'Modifica bobina cavo posato']\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 1159,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(Typography, {\n            variant: \"body1\",\n            children: \"Caricamento in corso...\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 1166,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(CircularProgress, {\n            sx: {\n              mt: 2\n            }\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 1169,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 1158,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 1151,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 1150,\n      columnNumber: 7\n    }, this), renderDialog()]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 1116,\n    columnNumber: 5\n  }, this);\n};\n_s(PosaCaviCollegamenti, \"iT4/SZ0cNA98E3RS5ZcI0FqlIJU=\", false, function () {\n  return [useNavigate];\n});\n_c = PosaCaviCollegamenti;\nexport default PosaCaviCollegamenti;\nvar _c;\n$RefreshReg$(_c, \"PosaCaviCollegamenti\");", "map": {"version": 3, "names": ["React", "useState", "Box", "Typography", "<PERSON><PERSON>", "Paper", "Divider", "List", "ListItem", "ListItemText", "ListItemIcon", "ListItemButton", "Dialog", "DialogTitle", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "DialogContentText", "DialogActions", "TextField", "FormControl", "InputLabel", "Select", "MenuItem", "Grid", "<PERSON><PERSON>", "CircularProgress", "FormHelperText", "Radio", "RadioGroup", "FormControlLabel", "FormLabel", "Add", "AddIcon", "Edit", "EditIcon", "Delete", "DeleteIcon", "Cable", "CableIcon", "Save", "SaveIcon", "Warning", "WarningIcon", "useNavigate", "caviService", "validateCavoData", "validateField", "isEmpty", "redirectToVisualizzaCavi", "CavoForm", "SelezionaCavoForm", "jsxDEV", "_jsxDEV", "Fragment", "_Fragment", "PosaCaviCollegamenti", "cantiereId", "propCantiereId", "onSuccess", "onError", "initialOption", "preselectedCavo", "dialogOnly", "_s", "console", "log", "navigate", "parseInt", "localStorage", "getItem", "loading", "setLoading", "selectedOption", "setSelectedOption", "openDialog", "setOpenDialog", "dialogType", "setDialogType", "selected<PERSON><PERSON><PERSON>", "setSelectedCavo", "formData", "setFormData", "id_cavo", "metri_posati", "id_bobina", "cavoIdInput", "setCavoIdInput", "formErrors", "setFormErrors", "formWarnings", "setForm<PERSON><PERSON>nings", "cavi", "<PERSON><PERSON><PERSON>", "caviLoading", "setCaviLoading", "deleteMode", "setDeleteMode", "useEffect", "loadCavi", "metri_te<PERSON>ci", "metratura_reale", "operationType", "Error", "caviData", "get<PERSON><PERSON>", "cavi<PERSON><PERSON><PERSON><PERSON><PERSON>", "filter", "cavo", "parseFloat", "stato_installazione", "error", "errorMessage", "response", "status", "statusText", "data", "detail", "request", "message", "handleOptionSelect", "option", "handleCloseDialog", "handleCavoSelect", "handleSearchCavoById", "trim", "find", "c", "handleCavoIdInputChange", "e", "target", "value", "handleFormChange", "name", "additionalParams", "metriTeorici", "result", "prev", "valid", "warning", "handleSave", "isNaN", "updateMetri<PERSON><PERSON><PERSON>", "setTimeout", "updateBobina", "validation", "<PERSON><PERSON><PERSON><PERSON>", "errors", "warnings", "validatedData", "dataToSend", "modificato_manualmente", "timestamp", "updateCavo", "includes", "Object", "keys", "length", "warningMessages", "values", "join", "warn", "cavoToDelete", "isInstalled", "markCavoAsSpare", "<PERSON><PERSON><PERSON><PERSON>", "deleteCavo", "renderDialog", "isEditMode", "cavoToEdit", "open", "onClose", "max<PERSON><PERSON><PERSON>", "fullWidth", "PaperProps", "sx", "width", "minHeight", "maxHeight", "overflow", "children", "pb", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "pt", "mt", "mode", "initialData", "onSubmit", "onCancel", "isDialog", "createCavo", "severity", "variant", "gutterBottom", "map", "button", "onClick", "primary", "secondary", "tipologia", "ubicazione_partenza", "ubicazione_arrivo", "margin", "label", "type", "onChange", "required", "helperText", "disabled", "startIcon", "size", "mb", "p", "display", "alignItems", "placeholder", "color", "ml", "min<PERSON><PERSON><PERSON>", "component", "control", "renderDialogContent", "mr", "dense", "flexGrow", "justifyContent", "textAlign", "_c", "$RefreshReg$"], "sources": ["C:/CMS/webapp/frontend/src/components/cavi/PosaCaviCollegamenti.js"], "sourcesContent": ["import React, { useState } from 'react';\nimport {\n  Box,\n  Typography,\n  Button,\n  Paper,\n  Divider,\n  List,\n  ListItem,\n  ListItemText,\n  ListItemIcon,\n  ListItemButton,\n  Dialog,\n  DialogTitle,\n  DialogContent,\n  DialogContentText,\n  DialogActions,\n  TextField,\n  FormControl,\n  InputLabel,\n  Select,\n  MenuItem,\n  Grid,\n  Alert,\n  CircularProgress,\n  FormHelperText,\n  Radio,\n  RadioGroup,\n  FormControlLabel,\n  FormLabel\n} from '@mui/material';\nimport {\n  Add as AddIcon,\n  Edit as EditIcon,\n  Delete as DeleteIcon,\n  Cable as CableIcon,\n  Save as SaveIcon,\n  Warning as WarningIcon\n} from '@mui/icons-material';\nimport { useNavigate } from 'react-router-dom';\nimport caviService from '../../services/caviService';\nimport { validateCavoData, validateField, isEmpty } from '../../utils/validationUtils';\nimport { redirectToVisualizzaCavi } from '../../utils/navigationUtils';\nimport CavoForm from './CavoForm';\nimport SelezionaCavoForm from './SelezionaCavoForm';\n\nconst PosaCaviCollegamenti = ({ cantiereId: propCantiereId, onSuccess, onError, initialOption = null, preselectedCavo = null, dialogOnly = false }) => {\n  // Log del cantiereId all'avvio\n  console.log('PosaCaviCollegamenti - cantiereId da props:', propCantiereId);\n\n  // Aggiungi navigate per la navigazione programmatica\n  const navigate = useNavigate();\n\n  // Se cantiereId non è definito nelle props, prova a recuperarlo dal localStorage\n  const cantiereId = propCantiereId || parseInt(localStorage.getItem('selectedCantiereId'), 10);\n  console.log('PosaCaviCollegamenti - cantiereId effettivo:', cantiereId);\n  const [loading, setLoading] = useState(false);\n  const [selectedOption, setSelectedOption] = useState(null);\n  const [openDialog, setOpenDialog] = useState(false);\n  const [dialogType, setDialogType] = useState('');\n  const [selectedCavo, setSelectedCavo] = useState(null);\n  const [formData, setFormData] = useState({\n    id_cavo: '',\n    metri_posati: '',\n    id_bobina: ''\n  });\n  const [cavoIdInput, setCavoIdInput] = useState('');\n  const [formErrors, setFormErrors] = useState({});\n  const [formWarnings, setFormWarnings] = useState({});\n  const [cavi, setCavi] = useState([]);\n  const [caviLoading, setCaviLoading] = useState(false);\n  const [deleteMode, setDeleteMode] = useState('spare'); // 'spare' o 'delete'\n\n  // Inizializza il componente con l'opzione iniziale se specificata\n  React.useEffect(() => {\n    if (initialOption) {\n      // Imposta direttamente le opzioni invece di chiamare handleOptionSelect\n      // per evitare dipendenze circolari\n      setSelectedOption(initialOption);\n\n      if (initialOption === 'eliminaCavo') {\n        if (preselectedCavo) {\n          // Se c'è un cavo preselezionato, usalo direttamente\n          setSelectedCavo(preselectedCavo);\n          setDialogType('eliminaCavo');\n          setOpenDialog(true);\n        } else {\n          // Altrimenti carica la lista dei cavi\n          loadCavi('eliminaCavo');\n          setDialogType('eliminaCavo');\n          setOpenDialog(true);\n        }\n      } else if (initialOption === 'modificaCavo') {\n        if (preselectedCavo) {\n          // Se c'è un cavo preselezionato, usalo direttamente per la modifica\n          setSelectedCavo(preselectedCavo);\n          setDialogType('modificaCavo');\n          setFormData({\n            ...preselectedCavo,\n            metri_teorici: preselectedCavo.metri_teorici || '',\n            metratura_reale: preselectedCavo.metratura_reale || '0'\n          });\n          setOpenDialog(true);\n        } else {\n          // Altrimenti carica la lista dei cavi\n          loadCavi('modificaCavo');\n          setDialogType('selezionaCavo');\n          setOpenDialog(true);\n        }\n      } else if (initialOption === 'aggiungiCavo') {\n        setDialogType('aggiungiCavo');\n        setOpenDialog(true);\n      }\n    }\n    // eslint-disable-next-line react-hooks/exhaustive-deps\n  }, [initialOption, preselectedCavo]);\n\n  // Carica i cavi attivi per la selezione\n  const loadCavi = async (operationType) => {\n    try {\n      setCaviLoading(true);\n\n      // Verifica che cantiereId sia valido\n      if (!cantiereId) {\n        throw new Error('ID cantiere non valido o mancante');\n      }\n\n      console.log('Caricamento cavi per cantiere:', cantiereId);\n      const caviData = await caviService.getCavi(cantiereId, 0);\n\n      // Filtra i cavi in base al tipo di operazione\n      if (operationType === 'modificaCavo') {\n        // Per modifica cavo, mostra solo i cavi non posati (metratura_reale = 0 e stato != Installato)\n        const caviNonPosati = caviData.filter(cavo =>\n          parseFloat(cavo.metratura_reale) === 0 &&\n          cavo.stato_installazione !== 'Installato'\n        );\n        setCavi(caviNonPosati);\n      } else {\n        // Per altre operazioni, mostra tutti i cavi\n        setCavi(caviData);\n      }\n    } catch (error) {\n      console.error('Errore nel caricamento dei cavi:', error);\n\n      // Gestione più dettagliata dell'errore\n      let errorMessage = 'Errore nel caricamento dei cavi';\n\n      if (error.response) {\n        // Errore dal server con risposta\n        errorMessage += `: ${error.response.status} ${error.response.statusText}`;\n        if (error.response.data && error.response.data.detail) {\n          errorMessage += ` - ${error.response.data.detail}`;\n        }\n      } else if (error.request) {\n        // Errore di rete senza risposta dal server\n        errorMessage += ': Nessuna risposta dal server. Verifica la connessione di rete.';\n      } else if (error.message) {\n        // Errore con messaggio\n        errorMessage += `: ${error.message}`;\n      }\n\n      onError(errorMessage);\n    } finally {\n      setCaviLoading(false);\n    }\n  };\n\n  // Gestisce la selezione di un'opzione dal menu\n  const handleOptionSelect = (option) => {\n    setSelectedOption(option);\n\n    if (option === 'inserisciMetri' || option === 'modificaBobina') {\n      loadCavi(option);\n      setDialogType(option);\n      setOpenDialog(true);\n    } else if (option === 'aggiungiCavo') {\n      // Apri il dialog per aggiungere un nuovo cavo IMMEDIATAMENTE\n      setDialogType('aggiungiCavo');\n      setOpenDialog(true);\n    } else if (option === 'modificaCavo') {\n      loadCavi('modificaCavo');\n      setDialogType('selezionaCavo');\n      setOpenDialog(true);\n    } else if (option === 'eliminaCavo') {\n      loadCavi('eliminaCavo');\n      setDialogType('eliminaCavo');\n      setOpenDialog(true);\n    }\n  };\n\n  // Gestisce la chiusura del dialog\n  const handleCloseDialog = () => {\n    console.log('Chiusura dialog...');\n    // Reset COMPLETO dello stato del componente\n    setOpenDialog(false);\n    setSelectedOption(null); // RESET selectedOption per eliminare la schermata di caricamento\n    setDialogType(''); // RESET dialogType\n    setSelectedCavo(null);\n    setFormData({\n      id_cavo: '',\n      metri_posati: '',\n      id_bobina: ''\n    });\n    setCavoIdInput('');\n    setFormErrors({});\n    setFormWarnings({});\n    setDeleteMode('spare'); // Reset alla modalità predefinita\n    setLoading(false); // Assicurati che loading sia false quando chiudi il dialog\n\n    // NON chiamare onSuccess quando si annulla il dialog\n    // Il genitore gestirà la chiusura tramite onClose del Dialog\n  };\n\n  // Gestisce la selezione di un cavo\n  const handleCavoSelect = (cavo) => {\n    setSelectedCavo(cavo);\n    if (dialogType === 'inserisciMetri') {\n      setFormData({\n        ...formData,\n        id_cavo: cavo.id_cavo,\n        metri_posati: ''\n      });\n    } else if (dialogType === 'modificaBobina') {\n      setFormData({\n        ...formData,\n        id_cavo: cavo.id_cavo,\n        id_bobina: cavo.id_bobina || ''\n      });\n    } else if (dialogType === 'selezionaCavo') {\n      setDialogType('modificaCavo');\n      setSelectedCavo(cavo);\n      setFormData({\n        ...cavo,\n        metri_teorici: cavo.metri_teorici || '',\n        metratura_reale: cavo.metratura_reale || '0'\n      });\n    }\n  };\n\n  // Gestisce la ricerca di un cavo per ID\n  const handleSearchCavoById = async () => {\n    if (!cavoIdInput.trim()) {\n      onError('Inserisci un ID cavo valido');\n      return;\n    }\n\n    try {\n      setCaviLoading(true);\n\n      // Verifica che cantiereId sia valido\n      if (!cantiereId) {\n        throw new Error('ID cantiere non valido o mancante');\n      }\n\n      console.log('Ricerca cavo con ID:', cavoIdInput, 'per cantiere:', cantiereId);\n      const caviData = await caviService.getCavi(cantiereId, 0);\n      const cavo = caviData.find(c => c.id_cavo === cavoIdInput.trim());\n\n      if (!cavo) {\n        onError(`Cavo con ID ${cavoIdInput} non trovato`);\n        return;\n      }\n\n      // Verifica se stiamo cercando un cavo per modificarlo\n      if (dialogType === 'selezionaCavo') {\n        // Verifica che il cavo non sia già posato\n        if (parseFloat(cavo.metratura_reale) > 0 || cavo.stato_installazione === 'Installato') {\n          onError(`Il cavo ${cavo.id_cavo} risulta già posato. Utilizza l'opzione \"Modifica bobina cavo posato\" per modificarlo.`);\n          return;\n        }\n      }\n\n      // Seleziona il cavo trovato\n      handleCavoSelect(cavo);\n    } catch (error) {\n      console.error('Errore nel caricamento dei cavi:', error);\n\n      // Gestione più dettagliata dell'errore\n      let errorMessage = 'Errore nel caricamento dei cavi';\n\n      if (error.response) {\n        // Errore dal server con risposta\n        errorMessage += `: ${error.response.status} ${error.response.statusText}`;\n        if (error.response.data && error.response.data.detail) {\n          errorMessage += ` - ${error.response.data.detail}`;\n        }\n      } else if (error.request) {\n        // Errore di rete senza risposta dal server\n        errorMessage += ': Nessuna risposta dal server. Verifica la connessione di rete.';\n      } else if (error.message) {\n        // Errore con messaggio\n        errorMessage += `: ${error.message}`;\n      }\n\n      onError(errorMessage);\n    } finally {\n      setCaviLoading(false);\n    }\n  };\n\n  // Gestisce il cambio dell'input dell'ID cavo\n  const handleCavoIdInputChange = (e) => {\n    setCavoIdInput(e.target.value);\n  };\n\n  // Gestisce il cambio dei valori nel form con validazione\n  const handleFormChange = (e) => {\n    const { name, value } = e.target;\n\n    // Aggiorna il valore nel form\n    setFormData({\n      ...formData,\n      [name]: value\n    });\n\n    // Valida il campo\n    if (dialogType === 'modificaCavo') {\n      const additionalParams = {};\n      if (name === 'metratura_reale') {\n        additionalParams.metriTeorici = parseFloat(formData.metri_teorici || 0);\n      }\n\n      const result = validateField(name, value, additionalParams);\n\n      // Aggiorna gli errori\n      setFormErrors(prev => ({\n        ...prev,\n        [name]: !result.valid ? result.message : null\n      }));\n\n      // Aggiorna gli avvisi\n      setFormWarnings(prev => ({\n        ...prev,\n        [name]: result.warning ? result.message : null\n      }));\n    }\n  };\n\n  // Gestisce il salvataggio del form con validazione\n  const handleSave = async () => {\n    try {\n      setLoading(true);\n\n      if (dialogType === 'inserisciMetri') {\n        // Valida i metri posati\n        if (isEmpty(formData.metri_posati) || isNaN(parseFloat(formData.metri_posati))) {\n          setFormErrors({ metri_posati: 'Inserire un valore numerico valido' });\n          setLoading(false);\n          return;\n        }\n\n        try {\n          await caviService.updateMetriPosati(\n            cantiereId,\n            formData.id_cavo,\n            parseFloat(formData.metri_posati)\n          );\n          // Prima chiudi il dialog, poi chiama onSuccess\n          handleCloseDialog();\n          onSuccess('Metri posati aggiornati con successo');\n          // Reindirizza alla pagina di visualizzazione cavi con un ritardo maggiore\n          setTimeout(() => {\n            redirectToVisualizzaCavi(navigate, 1000);\n          }, 500);\n        } catch (error) {\n          console.error('Errore durante l\\'aggiornamento dei metri posati:', error);\n          onError('Errore durante l\\'aggiornamento dei metri posati: ' + (error.message || 'Errore sconosciuto'));\n          setLoading(false);\n        }\n      } else if (dialogType === 'modificaBobina') {\n        try {\n          await caviService.updateBobina(\n            cantiereId,\n            formData.id_cavo,\n            formData.id_bobina\n          );\n          // Prima chiama onSuccess, poi chiudi il dialog\n          onSuccess('Bobina aggiornata con successo');\n          // Chiudi il dialog\n          handleCloseDialog();\n          // Reindirizza alla pagina di visualizzazione cavi con un ritardo maggiore\n          setTimeout(() => {\n            redirectToVisualizzaCavi(navigate, 1000);\n          }, 500);\n        } catch (error) {\n          console.error('Errore durante l\\'aggiornamento della bobina:', error);\n          onError('Errore durante l\\'aggiornamento della bobina: ' + (error.message || 'Errore sconosciuto'));\n          setLoading(false);\n        }\n      } else if (dialogType === 'modificaCavo') {\n        // Validazione completa dei dati del cavo\n        const validation = validateCavoData(formData);\n\n        if (!validation.isValid) {\n          setFormErrors(validation.errors);\n          setFormWarnings(validation.warnings);\n          setLoading(false);\n          return;\n        }\n\n        // Usa i dati validati\n        const validatedData = validation.validatedData;\n\n        // Rimuovi i campi di sistema che non devono essere modificati\n        const dataToSend = { ...validatedData };\n        delete dataToSend.id_bobina; // Rimuovi id_bobina perché è un campo di sistema\n        delete dataToSend.metratura_reale; // Rimuovi metratura_reale perché è un campo di sistema\n        delete dataToSend.modificato_manualmente; // Rimuovi modificato_manualmente perché è un campo di sistema\n        delete dataToSend.timestamp; // Rimuovi timestamp perché è un campo di sistema\n        delete dataToSend.stato_installazione; // Rimuovi stato_installazione perché è un campo di sistema per cavi non posati\n\n        // Imposta modificato_manualmente a 1 per indicare che il cavo è stato modificato manualmente\n        dataToSend.modificato_manualmente = 1;\n\n        console.log('Dati inviati al server:', dataToSend);\n\n        try {\n          console.log('Invio dati al server per aggiornamento cavo:', dataToSend);\n          const result = await caviService.updateCavo(cantiereId, dataToSend.id_cavo, dataToSend);\n          console.log('Risposta dal server dopo aggiornamento cavo:', result);\n\n          // Prima chiama onSuccess, poi chiudi il dialog\n          onSuccess('Cavo modificato con successo');\n          // Chiudi il dialog\n          handleCloseDialog();\n          // Reindirizza alla pagina di visualizzazione cavi con un ritardo maggiore\n          setTimeout(() => {\n            redirectToVisualizzaCavi(navigate, 1000);\n          }, 500);\n          return;\n        } catch (error) {\n          console.error('Errore durante l\\'aggiornamento del cavo:', error);\n\n          // Gestione più dettagliata dell'errore\n          let errorMessage = 'Errore durante l\\'aggiornamento del cavo';\n\n          if (error.response) {\n            // Errore dal server con risposta\n            errorMessage += `: ${error.response.status} ${error.response.statusText}`;\n            if (error.response.data && error.response.data.detail) {\n              errorMessage += ` - ${error.response.data.detail}`;\n            }\n          } else if (error.request) {\n            // Errore di rete senza risposta dal server\n            errorMessage += ': Nessuna risposta dal server. Verifica la connessione di rete.';\n\n            // Anche se c'è un errore di rete, la modifica potrebbe essere stata salvata\n            // Quindi consideriamo l'operazione come riuscita\n            console.log('Considerando l\\'operazione come riuscita nonostante l\\'errore di rete');\n            // Prima chiama onSuccess, poi chiudi il dialog\n            onSuccess('Cavo modificato con successo (la connessione potrebbe essere instabile)');\n            // Chiudi il dialog\n            handleCloseDialog();\n            // Reindirizza alla pagina di visualizzazione cavi con un ritardo maggiore\n            setTimeout(() => {\n              redirectToVisualizzaCavi(navigate, 1000);\n            }, 500);\n            return;\n          } else if (error.message) {\n            // Errore con messaggio\n            errorMessage += `: ${error.message}`;\n\n            // Se il messaggio indica che la modifica potrebbe essere stata salvata comunque\n            if (error.message.includes('La modifica potrebbe essere stata salvata')) {\n              console.log('Considerando l\\'operazione come riuscita nonostante l\\'errore');\n              handleCloseDialog();\n              onSuccess('Cavo modificato con successo (la connessione potrebbe essere instabile)');\n              // Reindirizza alla pagina di visualizzazione cavi con un ritardo maggiore\n              redirectToVisualizzaCavi(navigate, 1000);\n              return;\n            }\n          }\n\n          onError(errorMessage);\n          setLoading(false);\n          return;\n        }\n\n        // Mostra avvisi se presenti\n        if (Object.keys(validation.warnings).length > 0) {\n          const warningMessages = Object.values(validation.warnings).join('\\n');\n          console.warn('Avvisi durante il salvataggio:', warningMessages);\n        }\n      } else if (dialogType === 'eliminaCavo') {\n        // Usa il cavo corretto (selectedCavo o preselectedCavo)\n        const cavoToDelete = selectedCavo || preselectedCavo;\n\n        if (!cavoToDelete) {\n          onError('Nessun cavo selezionato per l\\'eliminazione');\n          return;\n        }\n\n        // Verifica se il cavo è installato\n        const isInstalled = cavoToDelete.stato_installazione === 'Installato' || (cavoToDelete.metratura_reale && cavoToDelete.metratura_reale > 0);\n\n        if (isInstalled) {\n          // Se è installato, marca solo come SPARE\n          console.log('Marcando cavo installato come SPARE:', cavoToDelete.id_cavo);\n          try {\n            // Prima prova con markCavoAsSpare\n            const result = await caviService.markCavoAsSpare(cantiereId, cavoToDelete.id_cavo);\n            console.log('Risultato marcatura SPARE:', result);\n            console.log('Nuovo valore modificato_manualmente:', result.modificato_manualmente);\n            // Chiudi il dialog prima di chiamare onSuccess\n            handleCloseDialog();\n            onSuccess(`Cavo ${cavoToDelete.id_cavo} marcato come SPARE con successo`);\n            // Reindirizza alla pagina di visualizzazione cavi con un ritardo\n            redirectToVisualizzaCavi(navigate, 500);\n          } catch (markError) {\n            console.error('Errore con markCavoAsSpare, tentativo con deleteCavo mode=spare:', markError);\n            // Se fallisce, prova con deleteCavo mode=spare\n            const result = await caviService.deleteCavo(cantiereId, cavoToDelete.id_cavo, 'spare');\n            console.log('Risultato marcatura SPARE con deleteCavo:', result);\n            // Chiudi il dialog prima di chiamare onSuccess\n            handleCloseDialog();\n            onSuccess(`Cavo ${cavoToDelete.id_cavo} marcato come SPARE con successo`);\n          }\n        } else {\n          // Se non è installato, usa la modalità selezionata (SPARE o DELETE)\n          console.log('Eliminando cavo non installato con modalità:', deleteMode);\n          const result = await caviService.deleteCavo(cantiereId, cavoToDelete.id_cavo, deleteMode);\n          console.log('Risultato eliminazione/marcatura:', result);\n          // Chiudi il dialog prima di chiamare onSuccess\n          handleCloseDialog();\n          onSuccess(`Cavo ${cavoToDelete.id_cavo} ${deleteMode === 'spare' ? 'marcato come SPARE' : 'eliminato'} con successo`);\n        }\n      }\n\n      // Non chiamare handleCloseDialog() qui, perché il dialog verrà chiuso dal genitore\n      // quando viene chiamato onSuccess()\n    } catch (error) {\n      console.error('Errore durante l\\'operazione:', error);\n\n      // Gestione più dettagliata dell'errore\n      let errorMessage = 'Errore sconosciuto';\n\n      if (error.detail) {\n        // Errore dal backend con dettaglio\n        errorMessage = error.detail;\n      } else if (error.message) {\n        // Errore con messaggio\n        errorMessage = error.message;\n      } else if (typeof error === 'string') {\n        // Errore come stringa\n        errorMessage = error;\n      }\n\n      onError('Errore durante l\\'operazione: ' + errorMessage);\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  // Renderizza il dialog in base al tipo\n  const renderDialog = () => {\n    if (dialogType === 'aggiungiCavo' || dialogType === 'modificaCavo') {\n      // Dialog unificato per aggiungere e modificare cavi\n      const isEditMode = dialogType === 'modificaCavo';\n      const cavoToEdit = selectedCavo || preselectedCavo;\n\n      return (\n        <Dialog\n          open={openDialog}\n          onClose={handleCloseDialog}\n          maxWidth=\"md\"\n          fullWidth\n          PaperProps={{\n            sx: {\n              width: '80%',  // Allargato di 2cm (rispetto a sm che è circa 60%)\n              minHeight: '80vh', // Allungato di 1cm (aggiungendo altezza minima)\n              maxHeight: '90vh',\n              overflow: 'auto'\n            }\n          }}\n        >\n          <DialogTitle sx={{ pb: 1 }}>\n            {isEditMode ? 'Modifica Cavo' : 'Aggiungi Nuovo Cavo'}\n          </DialogTitle>\n          <DialogContent sx={{ pt: 0, pb: 1 }}>\n            <Box sx={{ mt: 0 }}>\n              {isEditMode && cavoToEdit ? (\n                <CavoForm\n                  mode=\"edit\"\n                  initialData={formData.id_cavo ? formData : cavoToEdit}\n                  cantiereId={cantiereId}\n                  onSubmit={async (validatedData) => {\n                    try {\n                      // Rimuovi i campi di sistema che non devono essere modificati\n                      const dataToSend = { ...validatedData };\n                      delete dataToSend.id_bobina; // Rimuovi id_bobina perché è un campo di sistema\n                      delete dataToSend.metratura_reale; // Rimuovi metratura_reale perché è un campo di sistema\n                      delete dataToSend.modificato_manualmente; // Rimuovi modificato_manualmente perché è un campo di sistema\n                      delete dataToSend.timestamp; // Rimuovi timestamp perché è un campo di sistema\n                      delete dataToSend.stato_installazione; // Rimuovi stato_installazione perché è un campo di sistema per cavi non posati\n\n                      // Imposta modificato_manualmente a 1 per indicare che il cavo è stato modificato manualmente\n                      dataToSend.modificato_manualmente = 1;\n\n                      await caviService.updateCavo(cantiereId, dataToSend.id_cavo, dataToSend);\n                      return true;\n                    } catch (error) {\n                      throw error;\n                    }\n                  }}\n                  onSuccess={(message) => {\n                    handleCloseDialog(); // Prima resetta lo stato\n                    onSuccess(message); // Poi notifica il successo\n                  }}\n                  onError={onError}\n                  onCancel={handleCloseDialog}\n                  isDialog={true}\n                />\n              ) : isEditMode && !cavoToEdit ? (\n                <SelezionaCavoForm\n                  cantiereId={cantiereId}\n                  onSuccess={(message) => {\n                    handleCloseDialog(); // Prima resetta lo stato\n                    onSuccess(message); // Poi notifica il successo\n                  }}\n                  onError={onError}\n                  isDialog={true}\n                />\n              ) : (\n                <CavoForm\n                  mode=\"add\"\n                  cantiereId={cantiereId}\n                  onSubmit={async (validatedData) => {\n                    try {\n                      await caviService.createCavo(cantiereId, validatedData);\n                      return true;\n                    } catch (error) {\n                      throw error;\n                    }\n                  }}\n                  onSuccess={(message) => {\n                    handleCloseDialog(); // Prima resetta lo stato\n                    onSuccess(message); // Poi notifica il successo\n                  }}\n                  onError={onError}\n                  onCancel={handleCloseDialog}\n                  isDialog={true}\n                />\n              )}\n            </Box>\n          </DialogContent>\n          {/* No DialogActions needed here as CavoForm has its own buttons */}\n        </Dialog>\n      );\n    } else if (dialogType === 'inserisciMetri') {\n      return (\n        <Dialog open={openDialog} onClose={handleCloseDialog} maxWidth=\"sm\" fullWidth>\n          <DialogTitle>Inserisci Metri Posati</DialogTitle>\n          <DialogContent>\n            {caviLoading ? (\n              <CircularProgress />\n            ) : cavi.length === 0 ? (\n              <Alert severity=\"info\">Nessun cavo disponibile</Alert>\n            ) : !selectedCavo ? (\n              <Box>\n                <Typography variant=\"subtitle1\" gutterBottom>\n                  Seleziona un cavo:\n                </Typography>\n                <List>\n                  {cavi.map((cavo) => (\n                    <ListItem\n                      button\n                      key={cavo.id_cavo}\n                      onClick={() => handleCavoSelect(cavo)}\n                    >\n                      <ListItemText\n                        primary={cavo.id_cavo}\n                        secondary={`${cavo.tipologia || 'N/A'} - Da: ${cavo.ubicazione_partenza || 'N/A'} A: ${cavo.ubicazione_arrivo || 'N/A'}`}\n                      />\n                    </ListItem>\n                  ))}\n                </List>\n              </Box>\n            ) : (\n              <Box sx={{ mt: 2 }}>\n                <Typography variant=\"subtitle1\" gutterBottom>\n                  Cavo selezionato: {selectedCavo.id_cavo}\n                </Typography>\n                <Typography variant=\"body2\" gutterBottom>\n                  Metri teorici: {selectedCavo.metri_teorici || 'N/A'}\n                </Typography>\n                <Typography variant=\"body2\" gutterBottom>\n                  Metratura attuale: {selectedCavo.metratura_reale || '0'}\n                </Typography>\n                <TextField\n                  margin=\"dense\"\n                  name=\"metri_posati\"\n                  label=\"Metri posati da aggiungere\"\n                  type=\"number\"\n                  fullWidth\n                  variant=\"outlined\"\n                  value={formData.metri_posati}\n                  onChange={handleFormChange}\n                  required\n                  error={!!formErrors.metri_posati}\n                  helperText={formErrors.metri_posati}\n                  sx={{ mt: 2 }}\n                />\n              </Box>\n            )}\n          </DialogContent>\n          <DialogActions>\n            {selectedCavo && (\n              <Button\n                onClick={handleSave}\n                disabled={loading || !formData.metri_posati}\n                startIcon={loading ? <CircularProgress size={20} /> : <SaveIcon />}\n              >\n                Salva\n              </Button>\n            )}\n          </DialogActions>\n        </Dialog>\n      );\n    } else if (dialogType === 'modificaBobina') {\n      return (\n        <Dialog open={openDialog} onClose={handleCloseDialog} maxWidth=\"md\" fullWidth>\n          <DialogTitle>Modifica Bobina Cavo Posato</DialogTitle>\n          <DialogContent>\n            {caviLoading ? (\n              <CircularProgress />\n            ) : cavi.length === 0 ? (\n              <Alert severity=\"info\">Nessun cavo disponibile</Alert>\n            ) : !selectedCavo ? (\n              <Box>\n                <Typography variant=\"subtitle1\" gutterBottom>\n                  Seleziona un cavo:\n                </Typography>\n                <List>\n                  {cavi.map((cavo) => (\n                    <ListItem\n                      button\n                      key={cavo.id_cavo}\n                      onClick={() => handleCavoSelect(cavo)}\n                    >\n                      <ListItemText\n                        primary={cavo.id_cavo}\n                        secondary={`Bobina attuale: ${cavo.id_bobina ? (cavo.id_bobina === 'BOBINA_VUOTA' ? 'BOBINA VUOTA' : cavo.id_bobina) : 'BOBINA VUOTA'}`}\n                      />\n                    </ListItem>\n                  ))}\n                </List>\n              </Box>\n            ) : (\n              <Box sx={{ mt: 2 }}>\n                <Typography variant=\"subtitle1\" gutterBottom>\n                  Cavo selezionato: {selectedCavo.id_cavo}\n                </Typography>\n                <Typography variant=\"body2\" gutterBottom>\n                  Bobina attuale: {selectedCavo.id_bobina ? (selectedCavo.id_bobina === 'BOBINA_VUOTA' ? 'BOBINA VUOTA' : selectedCavo.id_bobina) : 'BOBINA VUOTA'}\n                </Typography>\n                <TextField\n                  margin=\"dense\"\n                  name=\"id_bobina\"\n                  label=\"ID Bobina\"\n                  fullWidth\n                  variant=\"outlined\"\n                  value={formData.id_bobina}\n                  onChange={handleFormChange}\n                  sx={{ mt: 2 }}\n                />\n              </Box>\n            )}\n          </DialogContent>\n          <DialogActions>\n            {selectedCavo && (\n              <Button\n                onClick={handleSave}\n                disabled={loading}\n                startIcon={loading ? <CircularProgress size={20} /> : <SaveIcon />}\n              >\n                Salva\n              </Button>\n            )}\n          </DialogActions>\n        </Dialog>\n      );\n    } else if (dialogType === 'selezionaCavo') {\n      return (\n        <Dialog open={openDialog} onClose={handleCloseDialog} maxWidth=\"sm\" fullWidth>\n          <DialogTitle>Modifica Cavo</DialogTitle>\n          <DialogContent>\n            <Alert severity=\"info\" sx={{ mb: 2 }}>\n              Puoi modificare solo i cavi non ancora posati (metratura = 0 e stato diverso da \"Installato\").\n              Per modificare cavi già posati, utilizzare l'opzione \"Modifica bobina cavo posato\".\n            </Alert>\n\n            {caviLoading ? (\n              <CircularProgress />\n            ) : !selectedCavo ? (\n              <Box sx={{ p: 2 }}>\n                <Typography variant=\"subtitle1\" gutterBottom>\n                  Inserisci l'ID del cavo da modificare:\n                </Typography>\n                <Box sx={{ display: 'flex', alignItems: 'center', mt: 2 }}>\n                  <TextField\n                    fullWidth\n                    label=\"ID Cavo\"\n                    variant=\"outlined\"\n                    value={cavoIdInput}\n                    onChange={handleCavoIdInputChange}\n                    placeholder=\"Inserisci l'ID del cavo\"\n                  />\n                  <Button\n                    variant=\"contained\"\n                    color=\"primary\"\n                    onClick={handleSearchCavoById}\n                    disabled={caviLoading || !cavoIdInput.trim()}\n                    sx={{ ml: 2, minWidth: '120px' }}\n                  >\n                    {caviLoading ? <CircularProgress size={24} /> : \"Cerca\"}\n                  </Button>\n                </Box>\n              </Box>\n            ) : null}\n          </DialogContent>\n          <DialogActions>\n          </DialogActions>\n        </Dialog>\n      );\n    } else if (dialogType === 'eliminaCavo') {\n      // Se c'è un preselectedCavo, usalo direttamente invece di selectedCavo\n      const cavoToDelete = selectedCavo || preselectedCavo;\n      // Verifica se il cavo selezionato è installato\n      const isInstalled = cavoToDelete && (cavoToDelete.stato_installazione === 'Installato' || (cavoToDelete.metratura_reale && cavoToDelete.metratura_reale > 0));\n\n      return (\n        <Dialog open={openDialog} onClose={handleCloseDialog} maxWidth=\"sm\" fullWidth>\n          <DialogTitle>\n            {!cavoToDelete ? 'Elimina Cavo' :\n             isInstalled ? 'Marca Cavo come SPARE' : 'Elimina Cavo'}\n          </DialogTitle>\n          <DialogContent>\n            {caviLoading ? (\n              <CircularProgress />\n            ) : !cavoToDelete ? (\n              <Box sx={{ p: 2 }}>\n                <Typography variant=\"subtitle1\" gutterBottom>\n                  Inserisci l'ID del cavo da eliminare:\n                </Typography>\n                <Box sx={{ display: 'flex', alignItems: 'center', mt: 2 }}>\n                  <TextField\n                    fullWidth\n                    label=\"ID Cavo\"\n                    variant=\"outlined\"\n                    value={cavoIdInput}\n                    onChange={handleCavoIdInputChange}\n                    placeholder=\"Inserisci l'ID del cavo\"\n                  />\n                  <Button\n                    variant=\"contained\"\n                    color=\"primary\"\n                    onClick={handleSearchCavoById}\n                    disabled={caviLoading || !cavoIdInput.trim()}\n                    sx={{ ml: 2, minWidth: '120px' }}\n                  >\n                    {caviLoading ? <CircularProgress size={24} /> : \"Cerca\"}\n                  </Button>\n                </Box>\n              </Box>\n            ) : dialogType === 'eliminaCavo' && isInstalled ? (\n              <>\n                <DialogContentText>\n                  Il cavo <strong>{cavoToDelete.id_cavo}</strong> risulta installato o parzialmente posato.\n                  {cavoToDelete.metratura_reale > 0 && (\n                    <> Metri posati: <strong>{cavoToDelete.metratura_reale} m</strong>.</>\n                  )}\n                </DialogContentText>\n                <DialogContentText sx={{ mt: 2 }}>\n                  Non è possibile eliminarlo definitivamente. Vuoi marcarlo come SPARE/consumato?\n                </DialogContentText>\n              </>\n            ) : dialogType === 'eliminaCavo' ? (\n              <>\n                <DialogContentText>\n                  Stai per eliminare il cavo <strong>{cavoToDelete.id_cavo}</strong>.\n                </DialogContentText>\n\n                <FormControl component=\"fieldset\" sx={{ mt: 2 }}>\n                  <FormLabel component=\"legend\">Scegli l'operazione da eseguire:</FormLabel>\n                  <RadioGroup\n                    value={deleteMode}\n                    onChange={(e) => setDeleteMode(e.target.value)}\n                  >\n                    <FormControlLabel\n                      value=\"spare\"\n                      control={<Radio />}\n                      label=\"Marca come SPARE (mantiene il cavo nel database ma lo contrassegna come non attivo)\"\n                    />\n                    <FormControlLabel\n                      value=\"delete\"\n                      control={<Radio />}\n                      label=\"Elimina definitivamente (rimuove completamente il cavo dal database)\"\n                    />\n                  </RadioGroup>\n                </FormControl>\n              </>\n            ) : null}\n          </DialogContent>\n          <DialogActions>\n            {dialogType === 'eliminaCavo' && cavoToDelete && (\n              <Button\n                onClick={handleSave}\n                disabled={loading}\n                color={isInstalled ? \"warning\" : \"error\"}\n                startIcon={loading ? <CircularProgress size={20} /> : isInstalled ? <WarningIcon /> : <DeleteIcon />}\n              >\n                {isInstalled ? \"Marca come SPARE\" : (deleteMode === 'spare' ? \"Marca come SPARE\" : \"Elimina definitivamente\")}\n              </Button>\n            )}\n          </DialogActions>\n        </Dialog>\n      );\n    }\n\n    return null;\n  };\n\n  // Funzione per renderizzare solo il contenuto (senza Dialog wrapper)\n  const renderDialogContent = () => {\n    if (dialogType === 'modificaCavo') {\n      // Se c'è un preselectedCavo, usalo direttamente invece di selectedCavo\n      const cavoToEdit = selectedCavo || preselectedCavo;\n\n      return (\n        <>\n          <DialogTitle sx={{ pb: 1 }}>Modifica Cavo</DialogTitle>\n          <DialogContent sx={{ pt: 0, pb: 1 }}>\n            <Box sx={{ mt: 0 }}>\n              {cavoToEdit ? (\n                <CavoForm\n                  mode=\"edit\"\n                  initialData={formData.id_cavo ? formData : cavoToEdit}\n                  cantiereId={cantiereId}\n                  onSubmit={async (validatedData) => {\n                    try {\n                      // Rimuovi i campi di sistema che non devono essere modificati\n                      const dataToSend = { ...validatedData };\n                      delete dataToSend.id_bobina; // Rimuovi id_bobina perché è un campo di sistema\n                      delete dataToSend.metratura_reale; // Rimuovi metratura_reale perché è un campo di sistema\n                      delete dataToSend.modificato_manualmente; // Rimuovi modificato_manualmente perché è un campo di sistema\n                      delete dataToSend.timestamp; // Rimuovi timestamp perché è un campo di sistema\n                      delete dataToSend.stato_installazione; // Rimuovi stato_installazione perché è un campo di sistema per cavi non posati\n\n                      // Imposta modificato_manualmente a 1 per indicare che il cavo è stato modificato manualmente\n                      dataToSend.modificato_manualmente = 1;\n\n                      await caviService.updateCavo(cantiereId, dataToSend.id_cavo, dataToSend);\n                      return true;\n                    } catch (error) {\n                      throw error;\n                    }\n                  }}\n                  onSuccess={(message) => {\n                    onSuccess(message);\n                    // Non chiamare handleCloseDialog() in modalità dialogOnly\n                  }}\n                  onError={onError}\n                  isDialog={true}\n\n                />\n              ) : (\n                <SelezionaCavoForm\n                  cantiereId={cantiereId}\n                  onSuccess={(message) => {\n                    onSuccess(message);\n                    // Non chiamare handleCloseDialog() in modalità dialogOnly\n                  }}\n                  onError={onError}\n                  isDialog={true}\n\n                />\n              )}\n            </Box>\n          </DialogContent>\n        </>\n      );\n    } else if (dialogType === 'eliminaCavo') {\n      // Se c'è un preselectedCavo, usalo direttamente invece di selectedCavo\n      const cavoToDelete = selectedCavo || preselectedCavo;\n      // Verifica se il cavo selezionato è installato\n      const isInstalled = cavoToDelete && (cavoToDelete.stato_installazione === 'Installato' || (cavoToDelete.metratura_reale && cavoToDelete.metratura_reale > 0));\n\n      return (\n        <>\n          <DialogTitle>\n            {!cavoToDelete ? 'Elimina Cavo' :\n             isInstalled ? 'Marca Cavo come SPARE' : 'Elimina Cavo'}\n          </DialogTitle>\n          <DialogContent>\n            {caviLoading ? (\n              <CircularProgress />\n            ) : !cavoToDelete ? (\n              <Box sx={{ p: 2 }}>\n                <Typography variant=\"subtitle1\" gutterBottom>\n                  Inserisci l'ID del cavo da eliminare:\n                </Typography>\n                <Box sx={{ display: 'flex', alignItems: 'center', mt: 2 }}>\n                  <TextField\n                    fullWidth\n                    label=\"ID Cavo\"\n                    variant=\"outlined\"\n                    value={cavoIdInput}\n                    onChange={handleCavoIdInputChange}\n                    placeholder=\"Inserisci l'ID del cavo\"\n                  />\n                  <Button\n                    variant=\"contained\"\n                    color=\"primary\"\n                    onClick={handleSearchCavoById}\n                    disabled={caviLoading || !cavoIdInput.trim()}\n                    sx={{ ml: 2, minWidth: '120px' }}\n                  >\n                    {caviLoading ? <CircularProgress size={24} /> : \"Cerca\"}\n                  </Button>\n                </Box>\n              </Box>\n            ) : dialogType === 'eliminaCavo' && isInstalled ? (\n              <>\n                <DialogContentText>\n                  Il cavo <strong>{cavoToDelete.id_cavo}</strong> risulta installato o parzialmente posato.\n                  {cavoToDelete.metratura_reale > 0 && (\n                    <> Metri posati: <strong>{cavoToDelete.metratura_reale} m</strong>.</>\n                  )}\n                </DialogContentText>\n                <DialogContentText sx={{ mt: 2 }}>\n                  Non è possibile eliminarlo definitivamente. Vuoi marcarlo come SPARE/consumato?\n                </DialogContentText>\n              </>\n            ) : dialogType === 'eliminaCavo' ? (\n              <>\n                <DialogContentText>\n                  Stai per eliminare il cavo <strong>{cavoToDelete.id_cavo}</strong>.\n                </DialogContentText>\n\n                <FormControl component=\"fieldset\" sx={{ mt: 2 }}>\n                  <FormLabel component=\"legend\">Scegli l'operazione da eseguire:</FormLabel>\n                  <RadioGroup\n                    value={deleteMode}\n                    onChange={(e) => setDeleteMode(e.target.value)}\n                  >\n                    <FormControlLabel\n                      value=\"spare\"\n                      control={<Radio />}\n                      label=\"Marca come SPARE (mantiene il cavo nel database ma lo contrassegna come non attivo)\"\n                    />\n                    <FormControlLabel\n                      value=\"delete\"\n                      control={<Radio />}\n                      label=\"Elimina definitivamente (rimuove completamente il cavo dal database)\"\n                    />\n                  </RadioGroup>\n                </FormControl>\n              </>\n            ) : null}\n          </DialogContent>\n          <DialogActions>\n            {dialogType === 'eliminaCavo' && cavoToDelete && (\n              <Button\n                onClick={handleSave}\n                disabled={loading}\n                color={isInstalled ? \"warning\" : \"error\"}\n                startIcon={loading ? <CircularProgress size={20} /> : isInstalled ? <WarningIcon /> : <DeleteIcon />}\n              >\n                {isInstalled ? \"Marca come SPARE\" : (deleteMode === 'spare' ? \"Marca come SPARE\" : \"Elimina definitivamente\")}\n              </Button>\n            )}\n          </DialogActions>\n        </>\n      );\n    } else if (dialogType === 'aggiungiCavo') {\n      return (\n        <>\n          <DialogTitle sx={{ pb: 1 }}>Aggiungi Nuovo Cavo</DialogTitle>\n          <DialogContent sx={{ pt: 0, pb: 1 }}>\n            <Box sx={{ mt: 0 }}>\n              <CavoForm\n                mode=\"add\"\n                cantiereId={cantiereId}\n                onSubmit={async (validatedData) => {\n                  try {\n                    await caviService.createCavo(cantiereId, validatedData);\n                    return true;\n                  } catch (error) {\n                    throw error;\n                  }\n                }}\n                onSuccess={(message) => {\n                  onSuccess(message);\n                  // Non chiamare handleCloseDialog() in modalità dialogOnly\n                }}\n                onError={onError}\n                isDialog={true}\n\n              />\n            </Box>\n          </DialogContent>\n        </>\n      );\n    }\n\n    return null;\n  };\n\n  // Se è in modalità dialogOnly, renderizza solo il contenuto (senza Dialog wrapper)\n  if (dialogOnly) {\n    return renderDialogContent();\n  }\n\n  return (\n    <Box sx={{ display: 'flex' }}>\n      {/* Menu a cascata nella sidebar */}\n      <Box sx={{ width: '280px', mr: 3 }}>\n        <Paper sx={{ p: 2, mb: 3 }}>\n          <Typography variant=\"h6\" gutterBottom>\n            Posa Cavi e Collegamenti\n          </Typography>\n          <Divider sx={{ mb: 2 }} />\n          <List component=\"nav\" dense>\n            <ListItemButton onClick={() => handleOptionSelect('inserisciMetri')}>\n              <ListItemIcon>\n                <CableIcon />\n              </ListItemIcon>\n              <ListItemText primary=\"1. Inserisci metri posati\" />\n            </ListItemButton>\n\n            <ListItemButton onClick={() => handleOptionSelect('modificaBobina')}>\n              <ListItemIcon>\n                <EditIcon />\n              </ListItemIcon>\n              <ListItemText primary=\"2. Modifica bobina cavo posato\" />\n            </ListItemButton>\n\n            <ListItemButton onClick={() => handleOptionSelect('aggiungiCavo')}>\n              <ListItemIcon>\n                <AddIcon />\n              </ListItemIcon>\n              <ListItemText primary=\"3. Aggiungi nuovo cavo\" />\n            </ListItemButton>\n          </List>\n        </Paper>\n      </Box>\n\n      {/* Area principale per il contenuto */}\n      <Box sx={{ flexGrow: 1 }}>\n        <Paper sx={{ p: 3, minHeight: '300px', display: 'flex', alignItems: 'center', justifyContent: 'center' }}>\n          {!selectedOption && (\n            <Typography variant=\"body1\">\n              Seleziona un'opzione dal menu a sinistra per iniziare.\n            </Typography>\n          )}\n          {selectedOption && !openDialog && (\n            <Box sx={{ textAlign: 'center' }}>\n              <Typography variant=\"h6\" gutterBottom>\n                {selectedOption === 'inserisciMetri' && 'Inserisci metri posati'}\n                {selectedOption === 'modificaCavo' && 'Modifica cavo'}\n                {selectedOption === 'aggiungiCavo' && 'Aggiungi nuovo cavo'}\n                {selectedOption === 'eliminaCavo' && 'Elimina cavo'}\n                {selectedOption === 'modificaBobina' && 'Modifica bobina cavo posato'}\n              </Typography>\n              <Typography variant=\"body1\">\n                Caricamento in corso...\n              </Typography>\n              <CircularProgress sx={{ mt: 2 }} />\n            </Box>\n          )}\n        </Paper>\n      </Box>\n\n      {renderDialog()}\n    </Box>\n  );\n};\n\nexport default PosaCaviCollegamenti;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,QAAQ,OAAO;AACvC,SACEC,GAAG,EACHC,UAAU,EACVC,MAAM,EACNC,KAAK,EACLC,OAAO,EACPC,IAAI,EACJC,QAAQ,EACRC,YAAY,EACZC,YAAY,EACZC,cAAc,EACdC,MAAM,EACNC,WAAW,EACXC,aAAa,EACbC,iBAAiB,EACjBC,aAAa,EACbC,SAAS,EACTC,WAAW,EACXC,UAAU,EACVC,MAAM,EACNC,QAAQ,EACRC,IAAI,EACJC,KAAK,EACLC,gBAAgB,EAChBC,cAAc,EACdC,KAAK,EACLC,UAAU,EACVC,gBAAgB,EAChBC,SAAS,QACJ,eAAe;AACtB,SACEC,GAAG,IAAIC,OAAO,EACdC,IAAI,IAAIC,QAAQ,EAChBC,MAAM,IAAIC,UAAU,EACpBC,KAAK,IAAIC,SAAS,EAClBC,IAAI,IAAIC,QAAQ,EAChBC,OAAO,IAAIC,WAAW,QACjB,qBAAqB;AAC5B,SAASC,WAAW,QAAQ,kBAAkB;AAC9C,OAAOC,WAAW,MAAM,4BAA4B;AACpD,SAASC,gBAAgB,EAAEC,aAAa,EAAEC,OAAO,QAAQ,6BAA6B;AACtF,SAASC,wBAAwB,QAAQ,6BAA6B;AACtE,OAAOC,QAAQ,MAAM,YAAY;AACjC,OAAOC,iBAAiB,MAAM,qBAAqB;AAAC,SAAAC,MAAA,IAAAC,OAAA,EAAAC,QAAA,IAAAC,SAAA;AAEpD,MAAMC,oBAAoB,GAAGA,CAAC;EAAEC,UAAU,EAAEC,cAAc;EAAEC,SAAS;EAAEC,OAAO;EAAEC,aAAa,GAAG,IAAI;EAAEC,eAAe,GAAG,IAAI;EAAEC,UAAU,GAAG;AAAM,CAAC,KAAK;EAAAC,EAAA;EACrJ;EACAC,OAAO,CAACC,GAAG,CAAC,6CAA6C,EAAER,cAAc,CAAC;;EAE1E;EACA,MAAMS,QAAQ,GAAGvB,WAAW,CAAC,CAAC;;EAE9B;EACA,MAAMa,UAAU,GAAGC,cAAc,IAAIU,QAAQ,CAACC,YAAY,CAACC,OAAO,CAAC,oBAAoB,CAAC,EAAE,EAAE,CAAC;EAC7FL,OAAO,CAACC,GAAG,CAAC,8CAA8C,EAAET,UAAU,CAAC;EACvE,MAAM,CAACc,OAAO,EAAEC,UAAU,CAAC,GAAGrE,QAAQ,CAAC,KAAK,CAAC;EAC7C,MAAM,CAACsE,cAAc,EAAEC,iBAAiB,CAAC,GAAGvE,QAAQ,CAAC,IAAI,CAAC;EAC1D,MAAM,CAACwE,UAAU,EAAEC,aAAa,CAAC,GAAGzE,QAAQ,CAAC,KAAK,CAAC;EACnD,MAAM,CAAC0E,UAAU,EAAEC,aAAa,CAAC,GAAG3E,QAAQ,CAAC,EAAE,CAAC;EAChD,MAAM,CAAC4E,YAAY,EAAEC,eAAe,CAAC,GAAG7E,QAAQ,CAAC,IAAI,CAAC;EACtD,MAAM,CAAC8E,QAAQ,EAAEC,WAAW,CAAC,GAAG/E,QAAQ,CAAC;IACvCgF,OAAO,EAAE,EAAE;IACXC,YAAY,EAAE,EAAE;IAChBC,SAAS,EAAE;EACb,CAAC,CAAC;EACF,MAAM,CAACC,WAAW,EAAEC,cAAc,CAAC,GAAGpF,QAAQ,CAAC,EAAE,CAAC;EAClD,MAAM,CAACqF,UAAU,EAAEC,aAAa,CAAC,GAAGtF,QAAQ,CAAC,CAAC,CAAC,CAAC;EAChD,MAAM,CAACuF,YAAY,EAAEC,eAAe,CAAC,GAAGxF,QAAQ,CAAC,CAAC,CAAC,CAAC;EACpD,MAAM,CAACyF,IAAI,EAAEC,OAAO,CAAC,GAAG1F,QAAQ,CAAC,EAAE,CAAC;EACpC,MAAM,CAAC2F,WAAW,EAAEC,cAAc,CAAC,GAAG5F,QAAQ,CAAC,KAAK,CAAC;EACrD,MAAM,CAAC6F,UAAU,EAAEC,aAAa,CAAC,GAAG9F,QAAQ,CAAC,OAAO,CAAC,CAAC,CAAC;;EAEvD;EACAD,KAAK,CAACgG,SAAS,CAAC,MAAM;IACpB,IAAIrC,aAAa,EAAE;MACjB;MACA;MACAa,iBAAiB,CAACb,aAAa,CAAC;MAEhC,IAAIA,aAAa,KAAK,aAAa,EAAE;QACnC,IAAIC,eAAe,EAAE;UACnB;UACAkB,eAAe,CAAClB,eAAe,CAAC;UAChCgB,aAAa,CAAC,aAAa,CAAC;UAC5BF,aAAa,CAAC,IAAI,CAAC;QACrB,CAAC,MAAM;UACL;UACAuB,QAAQ,CAAC,aAAa,CAAC;UACvBrB,aAAa,CAAC,aAAa,CAAC;UAC5BF,aAAa,CAAC,IAAI,CAAC;QACrB;MACF,CAAC,MAAM,IAAIf,aAAa,KAAK,cAAc,EAAE;QAC3C,IAAIC,eAAe,EAAE;UACnB;UACAkB,eAAe,CAAClB,eAAe,CAAC;UAChCgB,aAAa,CAAC,cAAc,CAAC;UAC7BI,WAAW,CAAC;YACV,GAAGpB,eAAe;YAClBsC,aAAa,EAAEtC,eAAe,CAACsC,aAAa,IAAI,EAAE;YAClDC,eAAe,EAAEvC,eAAe,CAACuC,eAAe,IAAI;UACtD,CAAC,CAAC;UACFzB,aAAa,CAAC,IAAI,CAAC;QACrB,CAAC,MAAM;UACL;UACAuB,QAAQ,CAAC,cAAc,CAAC;UACxBrB,aAAa,CAAC,eAAe,CAAC;UAC9BF,aAAa,CAAC,IAAI,CAAC;QACrB;MACF,CAAC,MAAM,IAAIf,aAAa,KAAK,cAAc,EAAE;QAC3CiB,aAAa,CAAC,cAAc,CAAC;QAC7BF,aAAa,CAAC,IAAI,CAAC;MACrB;IACF;IACA;EACF,CAAC,EAAE,CAACf,aAAa,EAAEC,eAAe,CAAC,CAAC;;EAEpC;EACA,MAAMqC,QAAQ,GAAG,MAAOG,aAAa,IAAK;IACxC,IAAI;MACFP,cAAc,CAAC,IAAI,CAAC;;MAEpB;MACA,IAAI,CAACtC,UAAU,EAAE;QACf,MAAM,IAAI8C,KAAK,CAAC,mCAAmC,CAAC;MACtD;MAEAtC,OAAO,CAACC,GAAG,CAAC,gCAAgC,EAAET,UAAU,CAAC;MACzD,MAAM+C,QAAQ,GAAG,MAAM3D,WAAW,CAAC4D,OAAO,CAAChD,UAAU,EAAE,CAAC,CAAC;;MAEzD;MACA,IAAI6C,aAAa,KAAK,cAAc,EAAE;QACpC;QACA,MAAMI,aAAa,GAAGF,QAAQ,CAACG,MAAM,CAACC,IAAI,IACxCC,UAAU,CAACD,IAAI,CAACP,eAAe,CAAC,KAAK,CAAC,IACtCO,IAAI,CAACE,mBAAmB,KAAK,YAC/B,CAAC;QACDjB,OAAO,CAACa,aAAa,CAAC;MACxB,CAAC,MAAM;QACL;QACAb,OAAO,CAACW,QAAQ,CAAC;MACnB;IACF,CAAC,CAAC,OAAOO,KAAK,EAAE;MACd9C,OAAO,CAAC8C,KAAK,CAAC,kCAAkC,EAAEA,KAAK,CAAC;;MAExD;MACA,IAAIC,YAAY,GAAG,iCAAiC;MAEpD,IAAID,KAAK,CAACE,QAAQ,EAAE;QAClB;QACAD,YAAY,IAAI,KAAKD,KAAK,CAACE,QAAQ,CAACC,MAAM,IAAIH,KAAK,CAACE,QAAQ,CAACE,UAAU,EAAE;QACzE,IAAIJ,KAAK,CAACE,QAAQ,CAACG,IAAI,IAAIL,KAAK,CAACE,QAAQ,CAACG,IAAI,CAACC,MAAM,EAAE;UACrDL,YAAY,IAAI,MAAMD,KAAK,CAACE,QAAQ,CAACG,IAAI,CAACC,MAAM,EAAE;QACpD;MACF,CAAC,MAAM,IAAIN,KAAK,CAACO,OAAO,EAAE;QACxB;QACAN,YAAY,IAAI,iEAAiE;MACnF,CAAC,MAAM,IAAID,KAAK,CAACQ,OAAO,EAAE;QACxB;QACAP,YAAY,IAAI,KAAKD,KAAK,CAACQ,OAAO,EAAE;MACtC;MAEA3D,OAAO,CAACoD,YAAY,CAAC;IACvB,CAAC,SAAS;MACRjB,cAAc,CAAC,KAAK,CAAC;IACvB;EACF,CAAC;;EAED;EACA,MAAMyB,kBAAkB,GAAIC,MAAM,IAAK;IACrC/C,iBAAiB,CAAC+C,MAAM,CAAC;IAEzB,IAAIA,MAAM,KAAK,gBAAgB,IAAIA,MAAM,KAAK,gBAAgB,EAAE;MAC9DtB,QAAQ,CAACsB,MAAM,CAAC;MAChB3C,aAAa,CAAC2C,MAAM,CAAC;MACrB7C,aAAa,CAAC,IAAI,CAAC;IACrB,CAAC,MAAM,IAAI6C,MAAM,KAAK,cAAc,EAAE;MACpC;MACA3C,aAAa,CAAC,cAAc,CAAC;MAC7BF,aAAa,CAAC,IAAI,CAAC;IACrB,CAAC,MAAM,IAAI6C,MAAM,KAAK,cAAc,EAAE;MACpCtB,QAAQ,CAAC,cAAc,CAAC;MACxBrB,aAAa,CAAC,eAAe,CAAC;MAC9BF,aAAa,CAAC,IAAI,CAAC;IACrB,CAAC,MAAM,IAAI6C,MAAM,KAAK,aAAa,EAAE;MACnCtB,QAAQ,CAAC,aAAa,CAAC;MACvBrB,aAAa,CAAC,aAAa,CAAC;MAC5BF,aAAa,CAAC,IAAI,CAAC;IACrB;EACF,CAAC;;EAED;EACA,MAAM8C,iBAAiB,GAAGA,CAAA,KAAM;IAC9BzD,OAAO,CAACC,GAAG,CAAC,oBAAoB,CAAC;IACjC;IACAU,aAAa,CAAC,KAAK,CAAC;IACpBF,iBAAiB,CAAC,IAAI,CAAC,CAAC,CAAC;IACzBI,aAAa,CAAC,EAAE,CAAC,CAAC,CAAC;IACnBE,eAAe,CAAC,IAAI,CAAC;IACrBE,WAAW,CAAC;MACVC,OAAO,EAAE,EAAE;MACXC,YAAY,EAAE,EAAE;MAChBC,SAAS,EAAE;IACb,CAAC,CAAC;IACFE,cAAc,CAAC,EAAE,CAAC;IAClBE,aAAa,CAAC,CAAC,CAAC,CAAC;IACjBE,eAAe,CAAC,CAAC,CAAC,CAAC;IACnBM,aAAa,CAAC,OAAO,CAAC,CAAC,CAAC;IACxBzB,UAAU,CAAC,KAAK,CAAC,CAAC,CAAC;;IAEnB;IACA;EACF,CAAC;;EAED;EACA,MAAMmD,gBAAgB,GAAIf,IAAI,IAAK;IACjC5B,eAAe,CAAC4B,IAAI,CAAC;IACrB,IAAI/B,UAAU,KAAK,gBAAgB,EAAE;MACnCK,WAAW,CAAC;QACV,GAAGD,QAAQ;QACXE,OAAO,EAAEyB,IAAI,CAACzB,OAAO;QACrBC,YAAY,EAAE;MAChB,CAAC,CAAC;IACJ,CAAC,MAAM,IAAIP,UAAU,KAAK,gBAAgB,EAAE;MAC1CK,WAAW,CAAC;QACV,GAAGD,QAAQ;QACXE,OAAO,EAAEyB,IAAI,CAACzB,OAAO;QACrBE,SAAS,EAAEuB,IAAI,CAACvB,SAAS,IAAI;MAC/B,CAAC,CAAC;IACJ,CAAC,MAAM,IAAIR,UAAU,KAAK,eAAe,EAAE;MACzCC,aAAa,CAAC,cAAc,CAAC;MAC7BE,eAAe,CAAC4B,IAAI,CAAC;MACrB1B,WAAW,CAAC;QACV,GAAG0B,IAAI;QACPR,aAAa,EAAEQ,IAAI,CAACR,aAAa,IAAI,EAAE;QACvCC,eAAe,EAAEO,IAAI,CAACP,eAAe,IAAI;MAC3C,CAAC,CAAC;IACJ;EACF,CAAC;;EAED;EACA,MAAMuB,oBAAoB,GAAG,MAAAA,CAAA,KAAY;IACvC,IAAI,CAACtC,WAAW,CAACuC,IAAI,CAAC,CAAC,EAAE;MACvBjE,OAAO,CAAC,6BAA6B,CAAC;MACtC;IACF;IAEA,IAAI;MACFmC,cAAc,CAAC,IAAI,CAAC;;MAEpB;MACA,IAAI,CAACtC,UAAU,EAAE;QACf,MAAM,IAAI8C,KAAK,CAAC,mCAAmC,CAAC;MACtD;MAEAtC,OAAO,CAACC,GAAG,CAAC,sBAAsB,EAAEoB,WAAW,EAAE,eAAe,EAAE7B,UAAU,CAAC;MAC7E,MAAM+C,QAAQ,GAAG,MAAM3D,WAAW,CAAC4D,OAAO,CAAChD,UAAU,EAAE,CAAC,CAAC;MACzD,MAAMmD,IAAI,GAAGJ,QAAQ,CAACsB,IAAI,CAACC,CAAC,IAAIA,CAAC,CAAC5C,OAAO,KAAKG,WAAW,CAACuC,IAAI,CAAC,CAAC,CAAC;MAEjE,IAAI,CAACjB,IAAI,EAAE;QACThD,OAAO,CAAC,eAAe0B,WAAW,cAAc,CAAC;QACjD;MACF;;MAEA;MACA,IAAIT,UAAU,KAAK,eAAe,EAAE;QAClC;QACA,IAAIgC,UAAU,CAACD,IAAI,CAACP,eAAe,CAAC,GAAG,CAAC,IAAIO,IAAI,CAACE,mBAAmB,KAAK,YAAY,EAAE;UACrFlD,OAAO,CAAC,WAAWgD,IAAI,CAACzB,OAAO,wFAAwF,CAAC;UACxH;QACF;MACF;;MAEA;MACAwC,gBAAgB,CAACf,IAAI,CAAC;IACxB,CAAC,CAAC,OAAOG,KAAK,EAAE;MACd9C,OAAO,CAAC8C,KAAK,CAAC,kCAAkC,EAAEA,KAAK,CAAC;;MAExD;MACA,IAAIC,YAAY,GAAG,iCAAiC;MAEpD,IAAID,KAAK,CAACE,QAAQ,EAAE;QAClB;QACAD,YAAY,IAAI,KAAKD,KAAK,CAACE,QAAQ,CAACC,MAAM,IAAIH,KAAK,CAACE,QAAQ,CAACE,UAAU,EAAE;QACzE,IAAIJ,KAAK,CAACE,QAAQ,CAACG,IAAI,IAAIL,KAAK,CAACE,QAAQ,CAACG,IAAI,CAACC,MAAM,EAAE;UACrDL,YAAY,IAAI,MAAMD,KAAK,CAACE,QAAQ,CAACG,IAAI,CAACC,MAAM,EAAE;QACpD;MACF,CAAC,MAAM,IAAIN,KAAK,CAACO,OAAO,EAAE;QACxB;QACAN,YAAY,IAAI,iEAAiE;MACnF,CAAC,MAAM,IAAID,KAAK,CAACQ,OAAO,EAAE;QACxB;QACAP,YAAY,IAAI,KAAKD,KAAK,CAACQ,OAAO,EAAE;MACtC;MAEA3D,OAAO,CAACoD,YAAY,CAAC;IACvB,CAAC,SAAS;MACRjB,cAAc,CAAC,KAAK,CAAC;IACvB;EACF,CAAC;;EAED;EACA,MAAMiC,uBAAuB,GAAIC,CAAC,IAAK;IACrC1C,cAAc,CAAC0C,CAAC,CAACC,MAAM,CAACC,KAAK,CAAC;EAChC,CAAC;;EAED;EACA,MAAMC,gBAAgB,GAAIH,CAAC,IAAK;IAC9B,MAAM;MAAEI,IAAI;MAAEF;IAAM,CAAC,GAAGF,CAAC,CAACC,MAAM;;IAEhC;IACAhD,WAAW,CAAC;MACV,GAAGD,QAAQ;MACX,CAACoD,IAAI,GAAGF;IACV,CAAC,CAAC;;IAEF;IACA,IAAItD,UAAU,KAAK,cAAc,EAAE;MACjC,MAAMyD,gBAAgB,GAAG,CAAC,CAAC;MAC3B,IAAID,IAAI,KAAK,iBAAiB,EAAE;QAC9BC,gBAAgB,CAACC,YAAY,GAAG1B,UAAU,CAAC5B,QAAQ,CAACmB,aAAa,IAAI,CAAC,CAAC;MACzE;MAEA,MAAMoC,MAAM,GAAGzF,aAAa,CAACsF,IAAI,EAAEF,KAAK,EAAEG,gBAAgB,CAAC;;MAE3D;MACA7C,aAAa,CAACgD,IAAI,KAAK;QACrB,GAAGA,IAAI;QACP,CAACJ,IAAI,GAAG,CAACG,MAAM,CAACE,KAAK,GAAGF,MAAM,CAACjB,OAAO,GAAG;MAC3C,CAAC,CAAC,CAAC;;MAEH;MACA5B,eAAe,CAAC8C,IAAI,KAAK;QACvB,GAAGA,IAAI;QACP,CAACJ,IAAI,GAAGG,MAAM,CAACG,OAAO,GAAGH,MAAM,CAACjB,OAAO,GAAG;MAC5C,CAAC,CAAC,CAAC;IACL;EACF,CAAC;;EAED;EACA,MAAMqB,UAAU,GAAG,MAAAA,CAAA,KAAY;IAC7B,IAAI;MACFpE,UAAU,CAAC,IAAI,CAAC;MAEhB,IAAIK,UAAU,KAAK,gBAAgB,EAAE;QACnC;QACA,IAAI7B,OAAO,CAACiC,QAAQ,CAACG,YAAY,CAAC,IAAIyD,KAAK,CAAChC,UAAU,CAAC5B,QAAQ,CAACG,YAAY,CAAC,CAAC,EAAE;UAC9EK,aAAa,CAAC;YAAEL,YAAY,EAAE;UAAqC,CAAC,CAAC;UACrEZ,UAAU,CAAC,KAAK,CAAC;UACjB;QACF;QAEA,IAAI;UACF,MAAM3B,WAAW,CAACiG,iBAAiB,CACjCrF,UAAU,EACVwB,QAAQ,CAACE,OAAO,EAChB0B,UAAU,CAAC5B,QAAQ,CAACG,YAAY,CAClC,CAAC;UACD;UACAsC,iBAAiB,CAAC,CAAC;UACnB/D,SAAS,CAAC,sCAAsC,CAAC;UACjD;UACAoF,UAAU,CAAC,MAAM;YACf9F,wBAAwB,CAACkB,QAAQ,EAAE,IAAI,CAAC;UAC1C,CAAC,EAAE,GAAG,CAAC;QACT,CAAC,CAAC,OAAO4C,KAAK,EAAE;UACd9C,OAAO,CAAC8C,KAAK,CAAC,mDAAmD,EAAEA,KAAK,CAAC;UACzEnD,OAAO,CAAC,oDAAoD,IAAImD,KAAK,CAACQ,OAAO,IAAI,oBAAoB,CAAC,CAAC;UACvG/C,UAAU,CAAC,KAAK,CAAC;QACnB;MACF,CAAC,MAAM,IAAIK,UAAU,KAAK,gBAAgB,EAAE;QAC1C,IAAI;UACF,MAAMhC,WAAW,CAACmG,YAAY,CAC5BvF,UAAU,EACVwB,QAAQ,CAACE,OAAO,EAChBF,QAAQ,CAACI,SACX,CAAC;UACD;UACA1B,SAAS,CAAC,gCAAgC,CAAC;UAC3C;UACA+D,iBAAiB,CAAC,CAAC;UACnB;UACAqB,UAAU,CAAC,MAAM;YACf9F,wBAAwB,CAACkB,QAAQ,EAAE,IAAI,CAAC;UAC1C,CAAC,EAAE,GAAG,CAAC;QACT,CAAC,CAAC,OAAO4C,KAAK,EAAE;UACd9C,OAAO,CAAC8C,KAAK,CAAC,+CAA+C,EAAEA,KAAK,CAAC;UACrEnD,OAAO,CAAC,gDAAgD,IAAImD,KAAK,CAACQ,OAAO,IAAI,oBAAoB,CAAC,CAAC;UACnG/C,UAAU,CAAC,KAAK,CAAC;QACnB;MACF,CAAC,MAAM,IAAIK,UAAU,KAAK,cAAc,EAAE;QACxC;QACA,MAAMoE,UAAU,GAAGnG,gBAAgB,CAACmC,QAAQ,CAAC;QAE7C,IAAI,CAACgE,UAAU,CAACC,OAAO,EAAE;UACvBzD,aAAa,CAACwD,UAAU,CAACE,MAAM,CAAC;UAChCxD,eAAe,CAACsD,UAAU,CAACG,QAAQ,CAAC;UACpC5E,UAAU,CAAC,KAAK,CAAC;UACjB;QACF;;QAEA;QACA,MAAM6E,aAAa,GAAGJ,UAAU,CAACI,aAAa;;QAE9C;QACA,MAAMC,UAAU,GAAG;UAAE,GAAGD;QAAc,CAAC;QACvC,OAAOC,UAAU,CAACjE,SAAS,CAAC,CAAC;QAC7B,OAAOiE,UAAU,CAACjD,eAAe,CAAC,CAAC;QACnC,OAAOiD,UAAU,CAACC,sBAAsB,CAAC,CAAC;QAC1C,OAAOD,UAAU,CAACE,SAAS,CAAC,CAAC;QAC7B,OAAOF,UAAU,CAACxC,mBAAmB,CAAC,CAAC;;QAEvC;QACAwC,UAAU,CAACC,sBAAsB,GAAG,CAAC;QAErCtF,OAAO,CAACC,GAAG,CAAC,yBAAyB,EAAEoF,UAAU,CAAC;QAElD,IAAI;UACFrF,OAAO,CAACC,GAAG,CAAC,8CAA8C,EAAEoF,UAAU,CAAC;UACvE,MAAMd,MAAM,GAAG,MAAM3F,WAAW,CAAC4G,UAAU,CAAChG,UAAU,EAAE6F,UAAU,CAACnE,OAAO,EAAEmE,UAAU,CAAC;UACvFrF,OAAO,CAACC,GAAG,CAAC,8CAA8C,EAAEsE,MAAM,CAAC;;UAEnE;UACA7E,SAAS,CAAC,8BAA8B,CAAC;UACzC;UACA+D,iBAAiB,CAAC,CAAC;UACnB;UACAqB,UAAU,CAAC,MAAM;YACf9F,wBAAwB,CAACkB,QAAQ,EAAE,IAAI,CAAC;UAC1C,CAAC,EAAE,GAAG,CAAC;UACP;QACF,CAAC,CAAC,OAAO4C,KAAK,EAAE;UACd9C,OAAO,CAAC8C,KAAK,CAAC,2CAA2C,EAAEA,KAAK,CAAC;;UAEjE;UACA,IAAIC,YAAY,GAAG,0CAA0C;UAE7D,IAAID,KAAK,CAACE,QAAQ,EAAE;YAClB;YACAD,YAAY,IAAI,KAAKD,KAAK,CAACE,QAAQ,CAACC,MAAM,IAAIH,KAAK,CAACE,QAAQ,CAACE,UAAU,EAAE;YACzE,IAAIJ,KAAK,CAACE,QAAQ,CAACG,IAAI,IAAIL,KAAK,CAACE,QAAQ,CAACG,IAAI,CAACC,MAAM,EAAE;cACrDL,YAAY,IAAI,MAAMD,KAAK,CAACE,QAAQ,CAACG,IAAI,CAACC,MAAM,EAAE;YACpD;UACF,CAAC,MAAM,IAAIN,KAAK,CAACO,OAAO,EAAE;YACxB;YACAN,YAAY,IAAI,iEAAiE;;YAEjF;YACA;YACA/C,OAAO,CAACC,GAAG,CAAC,uEAAuE,CAAC;YACpF;YACAP,SAAS,CAAC,yEAAyE,CAAC;YACpF;YACA+D,iBAAiB,CAAC,CAAC;YACnB;YACAqB,UAAU,CAAC,MAAM;cACf9F,wBAAwB,CAACkB,QAAQ,EAAE,IAAI,CAAC;YAC1C,CAAC,EAAE,GAAG,CAAC;YACP;UACF,CAAC,MAAM,IAAI4C,KAAK,CAACQ,OAAO,EAAE;YACxB;YACAP,YAAY,IAAI,KAAKD,KAAK,CAACQ,OAAO,EAAE;;YAEpC;YACA,IAAIR,KAAK,CAACQ,OAAO,CAACmC,QAAQ,CAAC,2CAA2C,CAAC,EAAE;cACvEzF,OAAO,CAACC,GAAG,CAAC,+DAA+D,CAAC;cAC5EwD,iBAAiB,CAAC,CAAC;cACnB/D,SAAS,CAAC,yEAAyE,CAAC;cACpF;cACAV,wBAAwB,CAACkB,QAAQ,EAAE,IAAI,CAAC;cACxC;YACF;UACF;UAEAP,OAAO,CAACoD,YAAY,CAAC;UACrBxC,UAAU,CAAC,KAAK,CAAC;UACjB;QACF;;QAEA;QACA,IAAImF,MAAM,CAACC,IAAI,CAACX,UAAU,CAACG,QAAQ,CAAC,CAACS,MAAM,GAAG,CAAC,EAAE;UAC/C,MAAMC,eAAe,GAAGH,MAAM,CAACI,MAAM,CAACd,UAAU,CAACG,QAAQ,CAAC,CAACY,IAAI,CAAC,IAAI,CAAC;UACrE/F,OAAO,CAACgG,IAAI,CAAC,gCAAgC,EAAEH,eAAe,CAAC;QACjE;MACF,CAAC,MAAM,IAAIjF,UAAU,KAAK,aAAa,EAAE;QACvC;QACA,MAAMqF,YAAY,GAAGnF,YAAY,IAAIjB,eAAe;QAEpD,IAAI,CAACoG,YAAY,EAAE;UACjBtG,OAAO,CAAC,6CAA6C,CAAC;UACtD;QACF;;QAEA;QACA,MAAMuG,WAAW,GAAGD,YAAY,CAACpD,mBAAmB,KAAK,YAAY,IAAKoD,YAAY,CAAC7D,eAAe,IAAI6D,YAAY,CAAC7D,eAAe,GAAG,CAAE;QAE3I,IAAI8D,WAAW,EAAE;UACf;UACAlG,OAAO,CAACC,GAAG,CAAC,sCAAsC,EAAEgG,YAAY,CAAC/E,OAAO,CAAC;UACzE,IAAI;YACF;YACA,MAAMqD,MAAM,GAAG,MAAM3F,WAAW,CAACuH,eAAe,CAAC3G,UAAU,EAAEyG,YAAY,CAAC/E,OAAO,CAAC;YAClFlB,OAAO,CAACC,GAAG,CAAC,4BAA4B,EAAEsE,MAAM,CAAC;YACjDvE,OAAO,CAACC,GAAG,CAAC,sCAAsC,EAAEsE,MAAM,CAACe,sBAAsB,CAAC;YAClF;YACA7B,iBAAiB,CAAC,CAAC;YACnB/D,SAAS,CAAC,QAAQuG,YAAY,CAAC/E,OAAO,kCAAkC,CAAC;YACzE;YACAlC,wBAAwB,CAACkB,QAAQ,EAAE,GAAG,CAAC;UACzC,CAAC,CAAC,OAAOkG,SAAS,EAAE;YAClBpG,OAAO,CAAC8C,KAAK,CAAC,kEAAkE,EAAEsD,SAAS,CAAC;YAC5F;YACA,MAAM7B,MAAM,GAAG,MAAM3F,WAAW,CAACyH,UAAU,CAAC7G,UAAU,EAAEyG,YAAY,CAAC/E,OAAO,EAAE,OAAO,CAAC;YACtFlB,OAAO,CAACC,GAAG,CAAC,2CAA2C,EAAEsE,MAAM,CAAC;YAChE;YACAd,iBAAiB,CAAC,CAAC;YACnB/D,SAAS,CAAC,QAAQuG,YAAY,CAAC/E,OAAO,kCAAkC,CAAC;UAC3E;QACF,CAAC,MAAM;UACL;UACAlB,OAAO,CAACC,GAAG,CAAC,8CAA8C,EAAE8B,UAAU,CAAC;UACvE,MAAMwC,MAAM,GAAG,MAAM3F,WAAW,CAACyH,UAAU,CAAC7G,UAAU,EAAEyG,YAAY,CAAC/E,OAAO,EAAEa,UAAU,CAAC;UACzF/B,OAAO,CAACC,GAAG,CAAC,mCAAmC,EAAEsE,MAAM,CAAC;UACxD;UACAd,iBAAiB,CAAC,CAAC;UACnB/D,SAAS,CAAC,QAAQuG,YAAY,CAAC/E,OAAO,IAAIa,UAAU,KAAK,OAAO,GAAG,oBAAoB,GAAG,WAAW,eAAe,CAAC;QACvH;MACF;;MAEA;MACA;IACF,CAAC,CAAC,OAAOe,KAAK,EAAE;MACd9C,OAAO,CAAC8C,KAAK,CAAC,+BAA+B,EAAEA,KAAK,CAAC;;MAErD;MACA,IAAIC,YAAY,GAAG,oBAAoB;MAEvC,IAAID,KAAK,CAACM,MAAM,EAAE;QAChB;QACAL,YAAY,GAAGD,KAAK,CAACM,MAAM;MAC7B,CAAC,MAAM,IAAIN,KAAK,CAACQ,OAAO,EAAE;QACxB;QACAP,YAAY,GAAGD,KAAK,CAACQ,OAAO;MAC9B,CAAC,MAAM,IAAI,OAAOR,KAAK,KAAK,QAAQ,EAAE;QACpC;QACAC,YAAY,GAAGD,KAAK;MACtB;MAEAnD,OAAO,CAAC,gCAAgC,GAAGoD,YAAY,CAAC;IAC1D,CAAC,SAAS;MACRxC,UAAU,CAAC,KAAK,CAAC;IACnB;EACF,CAAC;;EAED;EACA,MAAM+F,YAAY,GAAGA,CAAA,KAAM;IACzB,IAAI1F,UAAU,KAAK,cAAc,IAAIA,UAAU,KAAK,cAAc,EAAE;MAClE;MACA,MAAM2F,UAAU,GAAG3F,UAAU,KAAK,cAAc;MAChD,MAAM4F,UAAU,GAAG1F,YAAY,IAAIjB,eAAe;MAElD,oBACET,OAAA,CAACvC,MAAM;QACL4J,IAAI,EAAE/F,UAAW;QACjBgG,OAAO,EAAEjD,iBAAkB;QAC3BkD,QAAQ,EAAC,IAAI;QACbC,SAAS;QACTC,UAAU,EAAE;UACVC,EAAE,EAAE;YACFC,KAAK,EAAE,KAAK;YAAG;YACfC,SAAS,EAAE,MAAM;YAAE;YACnBC,SAAS,EAAE,MAAM;YACjBC,QAAQ,EAAE;UACZ;QACF,CAAE;QAAAC,QAAA,gBAEF/H,OAAA,CAACtC,WAAW;UAACgK,EAAE,EAAE;YAAEM,EAAE,EAAE;UAAE,CAAE;UAAAD,QAAA,EACxBZ,UAAU,GAAG,eAAe,GAAG;QAAqB;UAAAc,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC1C,CAAC,eACdpI,OAAA,CAACrC,aAAa;UAAC+J,EAAE,EAAE;YAAEW,EAAE,EAAE,CAAC;YAAEL,EAAE,EAAE;UAAE,CAAE;UAAAD,QAAA,eAClC/H,OAAA,CAACjD,GAAG;YAAC2K,EAAE,EAAE;cAAEY,EAAE,EAAE;YAAE,CAAE;YAAAP,QAAA,EAChBZ,UAAU,IAAIC,UAAU,gBACvBpH,OAAA,CAACH,QAAQ;cACP0I,IAAI,EAAC,MAAM;cACXC,WAAW,EAAE5G,QAAQ,CAACE,OAAO,GAAGF,QAAQ,GAAGwF,UAAW;cACtDhH,UAAU,EAAEA,UAAW;cACvBqI,QAAQ,EAAE,MAAOzC,aAAa,IAAK;gBACjC,IAAI;kBACF;kBACA,MAAMC,UAAU,GAAG;oBAAE,GAAGD;kBAAc,CAAC;kBACvC,OAAOC,UAAU,CAACjE,SAAS,CAAC,CAAC;kBAC7B,OAAOiE,UAAU,CAACjD,eAAe,CAAC,CAAC;kBACnC,OAAOiD,UAAU,CAACC,sBAAsB,CAAC,CAAC;kBAC1C,OAAOD,UAAU,CAACE,SAAS,CAAC,CAAC;kBAC7B,OAAOF,UAAU,CAACxC,mBAAmB,CAAC,CAAC;;kBAEvC;kBACAwC,UAAU,CAACC,sBAAsB,GAAG,CAAC;kBAErC,MAAM1G,WAAW,CAAC4G,UAAU,CAAChG,UAAU,EAAE6F,UAAU,CAACnE,OAAO,EAAEmE,UAAU,CAAC;kBACxE,OAAO,IAAI;gBACb,CAAC,CAAC,OAAOvC,KAAK,EAAE;kBACd,MAAMA,KAAK;gBACb;cACF,CAAE;cACFpD,SAAS,EAAG4D,OAAO,IAAK;gBACtBG,iBAAiB,CAAC,CAAC,CAAC,CAAC;gBACrB/D,SAAS,CAAC4D,OAAO,CAAC,CAAC,CAAC;cACtB,CAAE;cACF3D,OAAO,EAAEA,OAAQ;cACjBmI,QAAQ,EAAErE,iBAAkB;cAC5BsE,QAAQ,EAAE;YAAK;cAAAV,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAChB,CAAC,GACAjB,UAAU,IAAI,CAACC,UAAU,gBAC3BpH,OAAA,CAACF,iBAAiB;cAChBM,UAAU,EAAEA,UAAW;cACvBE,SAAS,EAAG4D,OAAO,IAAK;gBACtBG,iBAAiB,CAAC,CAAC,CAAC,CAAC;gBACrB/D,SAAS,CAAC4D,OAAO,CAAC,CAAC,CAAC;cACtB,CAAE;cACF3D,OAAO,EAAEA,OAAQ;cACjBoI,QAAQ,EAAE;YAAK;cAAAV,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAChB,CAAC,gBAEFpI,OAAA,CAACH,QAAQ;cACP0I,IAAI,EAAC,KAAK;cACVnI,UAAU,EAAEA,UAAW;cACvBqI,QAAQ,EAAE,MAAOzC,aAAa,IAAK;gBACjC,IAAI;kBACF,MAAMxG,WAAW,CAACoJ,UAAU,CAACxI,UAAU,EAAE4F,aAAa,CAAC;kBACvD,OAAO,IAAI;gBACb,CAAC,CAAC,OAAOtC,KAAK,EAAE;kBACd,MAAMA,KAAK;gBACb;cACF,CAAE;cACFpD,SAAS,EAAG4D,OAAO,IAAK;gBACtBG,iBAAiB,CAAC,CAAC,CAAC,CAAC;gBACrB/D,SAAS,CAAC4D,OAAO,CAAC,CAAC,CAAC;cACtB,CAAE;cACF3D,OAAO,EAAEA,OAAQ;cACjBmI,QAAQ,EAAErE,iBAAkB;cAC5BsE,QAAQ,EAAE;YAAK;cAAAV,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAChB;UACF;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACE;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACO,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAEV,CAAC;IAEb,CAAC,MAAM,IAAI5G,UAAU,KAAK,gBAAgB,EAAE;MAC1C,oBACExB,OAAA,CAACvC,MAAM;QAAC4J,IAAI,EAAE/F,UAAW;QAACgG,OAAO,EAAEjD,iBAAkB;QAACkD,QAAQ,EAAC,IAAI;QAACC,SAAS;QAAAO,QAAA,gBAC3E/H,OAAA,CAACtC,WAAW;UAAAqK,QAAA,EAAC;QAAsB;UAAAE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAa,CAAC,eACjDpI,OAAA,CAACrC,aAAa;UAAAoK,QAAA,EACXtF,WAAW,gBACVzC,OAAA,CAAC3B,gBAAgB;YAAA4J,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,GAClB7F,IAAI,CAACiE,MAAM,KAAK,CAAC,gBACnBxG,OAAA,CAAC5B,KAAK;YAACyK,QAAQ,EAAC,MAAM;YAAAd,QAAA,EAAC;UAAuB;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAO,CAAC,GACpD,CAAC1G,YAAY,gBACf1B,OAAA,CAACjD,GAAG;YAAAgL,QAAA,gBACF/H,OAAA,CAAChD,UAAU;cAAC8L,OAAO,EAAC,WAAW;cAACC,YAAY;cAAAhB,QAAA,EAAC;YAE7C;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAY,CAAC,eACbpI,OAAA,CAAC5C,IAAI;cAAA2K,QAAA,EACFxF,IAAI,CAACyG,GAAG,CAAEzF,IAAI,iBACbvD,OAAA,CAAC3C,QAAQ;gBACP4L,MAAM;gBAENC,OAAO,EAAEA,CAAA,KAAM5E,gBAAgB,CAACf,IAAI,CAAE;gBAAAwE,QAAA,eAEtC/H,OAAA,CAAC1C,YAAY;kBACX6L,OAAO,EAAE5F,IAAI,CAACzB,OAAQ;kBACtBsH,SAAS,EAAE,GAAG7F,IAAI,CAAC8F,SAAS,IAAI,KAAK,UAAU9F,IAAI,CAAC+F,mBAAmB,IAAI,KAAK,OAAO/F,IAAI,CAACgG,iBAAiB,IAAI,KAAK;gBAAG;kBAAAtB,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAC1H;cAAC,GANG7E,IAAI,CAACzB,OAAO;gBAAAmG,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAOT,CACX;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACE,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACJ,CAAC,gBAENpI,OAAA,CAACjD,GAAG;YAAC2K,EAAE,EAAE;cAAEY,EAAE,EAAE;YAAE,CAAE;YAAAP,QAAA,gBACjB/H,OAAA,CAAChD,UAAU;cAAC8L,OAAO,EAAC,WAAW;cAACC,YAAY;cAAAhB,QAAA,GAAC,oBACzB,EAACrG,YAAY,CAACI,OAAO;YAAA;cAAAmG,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC7B,CAAC,eACbpI,OAAA,CAAChD,UAAU;cAAC8L,OAAO,EAAC,OAAO;cAACC,YAAY;cAAAhB,QAAA,GAAC,iBACxB,EAACrG,YAAY,CAACqB,aAAa,IAAI,KAAK;YAAA;cAAAkF,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACzC,CAAC,eACbpI,OAAA,CAAChD,UAAU;cAAC8L,OAAO,EAAC,OAAO;cAACC,YAAY;cAAAhB,QAAA,GAAC,qBACpB,EAACrG,YAAY,CAACsB,eAAe,IAAI,GAAG;YAAA;cAAAiF,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC7C,CAAC,eACbpI,OAAA,CAAClC,SAAS;cACR0L,MAAM,EAAC,OAAO;cACdxE,IAAI,EAAC,cAAc;cACnByE,KAAK,EAAC,4BAA4B;cAClCC,IAAI,EAAC,QAAQ;cACblC,SAAS;cACTsB,OAAO,EAAC,UAAU;cAClBhE,KAAK,EAAElD,QAAQ,CAACG,YAAa;cAC7B4H,QAAQ,EAAE5E,gBAAiB;cAC3B6E,QAAQ;cACRlG,KAAK,EAAE,CAAC,CAACvB,UAAU,CAACJ,YAAa;cACjC8H,UAAU,EAAE1H,UAAU,CAACJ,YAAa;cACpC2F,EAAE,EAAE;gBAAEY,EAAE,EAAE;cAAE;YAAE;cAAAL,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACf,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACC;QACN;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACY,CAAC,eAChBpI,OAAA,CAACnC,aAAa;UAAAkK,QAAA,EACXrG,YAAY,iBACX1B,OAAA,CAAC/C,MAAM;YACLiM,OAAO,EAAE3D,UAAW;YACpBuE,QAAQ,EAAE5I,OAAO,IAAI,CAACU,QAAQ,CAACG,YAAa;YAC5CgI,SAAS,EAAE7I,OAAO,gBAAGlB,OAAA,CAAC3B,gBAAgB;cAAC2L,IAAI,EAAE;YAAG;cAAA/B,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,gBAAGpI,OAAA,CAACZ,QAAQ;cAAA6I,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAE;YAAAL,QAAA,EACpE;UAED;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ;QACT;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACY,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACV,CAAC;IAEb,CAAC,MAAM,IAAI5G,UAAU,KAAK,gBAAgB,EAAE;MAC1C,oBACExB,OAAA,CAACvC,MAAM;QAAC4J,IAAI,EAAE/F,UAAW;QAACgG,OAAO,EAAEjD,iBAAkB;QAACkD,QAAQ,EAAC,IAAI;QAACC,SAAS;QAAAO,QAAA,gBAC3E/H,OAAA,CAACtC,WAAW;UAAAqK,QAAA,EAAC;QAA2B;UAAAE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAa,CAAC,eACtDpI,OAAA,CAACrC,aAAa;UAAAoK,QAAA,EACXtF,WAAW,gBACVzC,OAAA,CAAC3B,gBAAgB;YAAA4J,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,GAClB7F,IAAI,CAACiE,MAAM,KAAK,CAAC,gBACnBxG,OAAA,CAAC5B,KAAK;YAACyK,QAAQ,EAAC,MAAM;YAAAd,QAAA,EAAC;UAAuB;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAO,CAAC,GACpD,CAAC1G,YAAY,gBACf1B,OAAA,CAACjD,GAAG;YAAAgL,QAAA,gBACF/H,OAAA,CAAChD,UAAU;cAAC8L,OAAO,EAAC,WAAW;cAACC,YAAY;cAAAhB,QAAA,EAAC;YAE7C;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAY,CAAC,eACbpI,OAAA,CAAC5C,IAAI;cAAA2K,QAAA,EACFxF,IAAI,CAACyG,GAAG,CAAEzF,IAAI,iBACbvD,OAAA,CAAC3C,QAAQ;gBACP4L,MAAM;gBAENC,OAAO,EAAEA,CAAA,KAAM5E,gBAAgB,CAACf,IAAI,CAAE;gBAAAwE,QAAA,eAEtC/H,OAAA,CAAC1C,YAAY;kBACX6L,OAAO,EAAE5F,IAAI,CAACzB,OAAQ;kBACtBsH,SAAS,EAAE,mBAAmB7F,IAAI,CAACvB,SAAS,GAAIuB,IAAI,CAACvB,SAAS,KAAK,cAAc,GAAG,cAAc,GAAGuB,IAAI,CAACvB,SAAS,GAAI,cAAc;gBAAG;kBAAAiG,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACzI;cAAC,GANG7E,IAAI,CAACzB,OAAO;gBAAAmG,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAOT,CACX;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACE,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACJ,CAAC,gBAENpI,OAAA,CAACjD,GAAG;YAAC2K,EAAE,EAAE;cAAEY,EAAE,EAAE;YAAE,CAAE;YAAAP,QAAA,gBACjB/H,OAAA,CAAChD,UAAU;cAAC8L,OAAO,EAAC,WAAW;cAACC,YAAY;cAAAhB,QAAA,GAAC,oBACzB,EAACrG,YAAY,CAACI,OAAO;YAAA;cAAAmG,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC7B,CAAC,eACbpI,OAAA,CAAChD,UAAU;cAAC8L,OAAO,EAAC,OAAO;cAACC,YAAY;cAAAhB,QAAA,GAAC,kBACvB,EAACrG,YAAY,CAACM,SAAS,GAAIN,YAAY,CAACM,SAAS,KAAK,cAAc,GAAG,cAAc,GAAGN,YAAY,CAACM,SAAS,GAAI,cAAc;YAAA;cAAAiG,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACtI,CAAC,eACbpI,OAAA,CAAClC,SAAS;cACR0L,MAAM,EAAC,OAAO;cACdxE,IAAI,EAAC,WAAW;cAChByE,KAAK,EAAC,WAAW;cACjBjC,SAAS;cACTsB,OAAO,EAAC,UAAU;cAClBhE,KAAK,EAAElD,QAAQ,CAACI,SAAU;cAC1B2H,QAAQ,EAAE5E,gBAAiB;cAC3B2C,EAAE,EAAE;gBAAEY,EAAE,EAAE;cAAE;YAAE;cAAAL,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACf,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACC;QACN;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACY,CAAC,eAChBpI,OAAA,CAACnC,aAAa;UAAAkK,QAAA,EACXrG,YAAY,iBACX1B,OAAA,CAAC/C,MAAM;YACLiM,OAAO,EAAE3D,UAAW;YACpBuE,QAAQ,EAAE5I,OAAQ;YAClB6I,SAAS,EAAE7I,OAAO,gBAAGlB,OAAA,CAAC3B,gBAAgB;cAAC2L,IAAI,EAAE;YAAG;cAAA/B,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,gBAAGpI,OAAA,CAACZ,QAAQ;cAAA6I,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAE;YAAAL,QAAA,EACpE;UAED;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ;QACT;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACY,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACV,CAAC;IAEb,CAAC,MAAM,IAAI5G,UAAU,KAAK,eAAe,EAAE;MACzC,oBACExB,OAAA,CAACvC,MAAM;QAAC4J,IAAI,EAAE/F,UAAW;QAACgG,OAAO,EAAEjD,iBAAkB;QAACkD,QAAQ,EAAC,IAAI;QAACC,SAAS;QAAAO,QAAA,gBAC3E/H,OAAA,CAACtC,WAAW;UAAAqK,QAAA,EAAC;QAAa;UAAAE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAa,CAAC,eACxCpI,OAAA,CAACrC,aAAa;UAAAoK,QAAA,gBACZ/H,OAAA,CAAC5B,KAAK;YAACyK,QAAQ,EAAC,MAAM;YAACnB,EAAE,EAAE;cAAEuC,EAAE,EAAE;YAAE,CAAE;YAAAlC,QAAA,EAAC;UAGtC;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAO,CAAC,EAEP3F,WAAW,gBACVzC,OAAA,CAAC3B,gBAAgB;YAAA4J,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,GAClB,CAAC1G,YAAY,gBACf1B,OAAA,CAACjD,GAAG;YAAC2K,EAAE,EAAE;cAAEwC,CAAC,EAAE;YAAE,CAAE;YAAAnC,QAAA,gBAChB/H,OAAA,CAAChD,UAAU;cAAC8L,OAAO,EAAC,WAAW;cAACC,YAAY;cAAAhB,QAAA,EAAC;YAE7C;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAY,CAAC,eACbpI,OAAA,CAACjD,GAAG;cAAC2K,EAAE,EAAE;gBAAEyC,OAAO,EAAE,MAAM;gBAAEC,UAAU,EAAE,QAAQ;gBAAE9B,EAAE,EAAE;cAAE,CAAE;cAAAP,QAAA,gBACxD/H,OAAA,CAAClC,SAAS;gBACR0J,SAAS;gBACTiC,KAAK,EAAC,SAAS;gBACfX,OAAO,EAAC,UAAU;gBAClBhE,KAAK,EAAE7C,WAAY;gBACnB0H,QAAQ,EAAEhF,uBAAwB;gBAClC0F,WAAW,EAAC;cAAyB;gBAAApC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACtC,CAAC,eACFpI,OAAA,CAAC/C,MAAM;gBACL6L,OAAO,EAAC,WAAW;gBACnBwB,KAAK,EAAC,SAAS;gBACfpB,OAAO,EAAE3E,oBAAqB;gBAC9BuF,QAAQ,EAAErH,WAAW,IAAI,CAACR,WAAW,CAACuC,IAAI,CAAC,CAAE;gBAC7CkD,EAAE,EAAE;kBAAE6C,EAAE,EAAE,CAAC;kBAAEC,QAAQ,EAAE;gBAAQ,CAAE;gBAAAzC,QAAA,EAEhCtF,WAAW,gBAAGzC,OAAA,CAAC3B,gBAAgB;kBAAC2L,IAAI,EAAE;gBAAG;kBAAA/B,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAC,GAAG;cAAO;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACjD,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACN,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC,GACJ,IAAI;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACK,CAAC,eAChBpI,OAAA,CAACnC,aAAa;UAAAoK,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACC,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACV,CAAC;IAEb,CAAC,MAAM,IAAI5G,UAAU,KAAK,aAAa,EAAE;MACvC;MACA,MAAMqF,YAAY,GAAGnF,YAAY,IAAIjB,eAAe;MACpD;MACA,MAAMqG,WAAW,GAAGD,YAAY,KAAKA,YAAY,CAACpD,mBAAmB,KAAK,YAAY,IAAKoD,YAAY,CAAC7D,eAAe,IAAI6D,YAAY,CAAC7D,eAAe,GAAG,CAAE,CAAC;MAE7J,oBACEhD,OAAA,CAACvC,MAAM;QAAC4J,IAAI,EAAE/F,UAAW;QAACgG,OAAO,EAAEjD,iBAAkB;QAACkD,QAAQ,EAAC,IAAI;QAACC,SAAS;QAAAO,QAAA,gBAC3E/H,OAAA,CAACtC,WAAW;UAAAqK,QAAA,EACT,CAAClB,YAAY,GAAG,cAAc,GAC9BC,WAAW,GAAG,uBAAuB,GAAG;QAAc;UAAAmB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC5C,CAAC,eACdpI,OAAA,CAACrC,aAAa;UAAAoK,QAAA,EACXtF,WAAW,gBACVzC,OAAA,CAAC3B,gBAAgB;YAAA4J,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,GAClB,CAACvB,YAAY,gBACf7G,OAAA,CAACjD,GAAG;YAAC2K,EAAE,EAAE;cAAEwC,CAAC,EAAE;YAAE,CAAE;YAAAnC,QAAA,gBAChB/H,OAAA,CAAChD,UAAU;cAAC8L,OAAO,EAAC,WAAW;cAACC,YAAY;cAAAhB,QAAA,EAAC;YAE7C;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAY,CAAC,eACbpI,OAAA,CAACjD,GAAG;cAAC2K,EAAE,EAAE;gBAAEyC,OAAO,EAAE,MAAM;gBAAEC,UAAU,EAAE,QAAQ;gBAAE9B,EAAE,EAAE;cAAE,CAAE;cAAAP,QAAA,gBACxD/H,OAAA,CAAClC,SAAS;gBACR0J,SAAS;gBACTiC,KAAK,EAAC,SAAS;gBACfX,OAAO,EAAC,UAAU;gBAClBhE,KAAK,EAAE7C,WAAY;gBACnB0H,QAAQ,EAAEhF,uBAAwB;gBAClC0F,WAAW,EAAC;cAAyB;gBAAApC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACtC,CAAC,eACFpI,OAAA,CAAC/C,MAAM;gBACL6L,OAAO,EAAC,WAAW;gBACnBwB,KAAK,EAAC,SAAS;gBACfpB,OAAO,EAAE3E,oBAAqB;gBAC9BuF,QAAQ,EAAErH,WAAW,IAAI,CAACR,WAAW,CAACuC,IAAI,CAAC,CAAE;gBAC7CkD,EAAE,EAAE;kBAAE6C,EAAE,EAAE,CAAC;kBAAEC,QAAQ,EAAE;gBAAQ,CAAE;gBAAAzC,QAAA,EAEhCtF,WAAW,gBAAGzC,OAAA,CAAC3B,gBAAgB;kBAAC2L,IAAI,EAAE;gBAAG;kBAAA/B,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAC,GAAG;cAAO;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACjD,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACN,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC,GACJ5G,UAAU,KAAK,aAAa,IAAIsF,WAAW,gBAC7C9G,OAAA,CAAAE,SAAA;YAAA6H,QAAA,gBACE/H,OAAA,CAACpC,iBAAiB;cAAAmK,QAAA,GAAC,UACT,eAAA/H,OAAA;gBAAA+H,QAAA,EAASlB,YAAY,CAAC/E;cAAO;gBAAAmG,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAS,CAAC,8CAC/C,EAACvB,YAAY,CAAC7D,eAAe,GAAG,CAAC,iBAC/BhD,OAAA,CAAAE,SAAA;gBAAA6H,QAAA,GAAE,iBAAe,eAAA/H,OAAA;kBAAA+H,QAAA,GAASlB,YAAY,CAAC7D,eAAe,EAAC,IAAE;gBAAA;kBAAAiF,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,KAAC;cAAA,eAAE,CACtE;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACgB,CAAC,eACpBpI,OAAA,CAACpC,iBAAiB;cAAC8J,EAAE,EAAE;gBAAEY,EAAE,EAAE;cAAE,CAAE;cAAAP,QAAA,EAAC;YAElC;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAmB,CAAC;UAAA,eACpB,CAAC,GACD5G,UAAU,KAAK,aAAa,gBAC9BxB,OAAA,CAAAE,SAAA;YAAA6H,QAAA,gBACE/H,OAAA,CAACpC,iBAAiB;cAAAmK,QAAA,GAAC,6BACU,eAAA/H,OAAA;gBAAA+H,QAAA,EAASlB,YAAY,CAAC/E;cAAO;gBAAAmG,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAS,CAAC,KACpE;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAmB,CAAC,eAEpBpI,OAAA,CAACjC,WAAW;cAAC0M,SAAS,EAAC,UAAU;cAAC/C,EAAE,EAAE;gBAAEY,EAAE,EAAE;cAAE,CAAE;cAAAP,QAAA,gBAC9C/H,OAAA,CAACtB,SAAS;gBAAC+L,SAAS,EAAC,QAAQ;gBAAA1C,QAAA,EAAC;cAAgC;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAW,CAAC,eAC1EpI,OAAA,CAACxB,UAAU;gBACTsG,KAAK,EAAEnC,UAAW;gBAClBgH,QAAQ,EAAG/E,CAAC,IAAKhC,aAAa,CAACgC,CAAC,CAACC,MAAM,CAACC,KAAK,CAAE;gBAAAiD,QAAA,gBAE/C/H,OAAA,CAACvB,gBAAgB;kBACfqG,KAAK,EAAC,OAAO;kBACb4F,OAAO,eAAE1K,OAAA,CAACzB,KAAK;oBAAA0J,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE,CAAE;kBACnBqB,KAAK,EAAC;gBAAqF;kBAAAxB,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAC5F,CAAC,eACFpI,OAAA,CAACvB,gBAAgB;kBACfqG,KAAK,EAAC,QAAQ;kBACd4F,OAAO,eAAE1K,OAAA,CAACzB,KAAK;oBAAA0J,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE,CAAE;kBACnBqB,KAAK,EAAC;gBAAsE;kBAAAxB,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAC7E,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACQ,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACF,CAAC;UAAA,eACd,CAAC,GACD;QAAI;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACK,CAAC,eAChBpI,OAAA,CAACnC,aAAa;UAAAkK,QAAA,EACXvG,UAAU,KAAK,aAAa,IAAIqF,YAAY,iBAC3C7G,OAAA,CAAC/C,MAAM;YACLiM,OAAO,EAAE3D,UAAW;YACpBuE,QAAQ,EAAE5I,OAAQ;YAClBoJ,KAAK,EAAExD,WAAW,GAAG,SAAS,GAAG,OAAQ;YACzCiD,SAAS,EAAE7I,OAAO,gBAAGlB,OAAA,CAAC3B,gBAAgB;cAAC2L,IAAI,EAAE;YAAG;cAAA/B,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,GAAGtB,WAAW,gBAAG9G,OAAA,CAACV,WAAW;cAAA2I,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,gBAAGpI,OAAA,CAAChB,UAAU;cAAAiJ,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAE;YAAAL,QAAA,EAEpGjB,WAAW,GAAG,kBAAkB,GAAInE,UAAU,KAAK,OAAO,GAAG,kBAAkB,GAAG;UAA0B;YAAAsF,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACvG;QACT;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACY,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACV,CAAC;IAEb;IAEA,OAAO,IAAI;EACb,CAAC;;EAED;EACA,MAAMuC,mBAAmB,GAAGA,CAAA,KAAM;IAChC,IAAInJ,UAAU,KAAK,cAAc,EAAE;MACjC;MACA,MAAM4F,UAAU,GAAG1F,YAAY,IAAIjB,eAAe;MAElD,oBACET,OAAA,CAAAE,SAAA;QAAA6H,QAAA,gBACE/H,OAAA,CAACtC,WAAW;UAACgK,EAAE,EAAE;YAAEM,EAAE,EAAE;UAAE,CAAE;UAAAD,QAAA,EAAC;QAAa;UAAAE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAa,CAAC,eACvDpI,OAAA,CAACrC,aAAa;UAAC+J,EAAE,EAAE;YAAEW,EAAE,EAAE,CAAC;YAAEL,EAAE,EAAE;UAAE,CAAE;UAAAD,QAAA,eAClC/H,OAAA,CAACjD,GAAG;YAAC2K,EAAE,EAAE;cAAEY,EAAE,EAAE;YAAE,CAAE;YAAAP,QAAA,EAChBX,UAAU,gBACTpH,OAAA,CAACH,QAAQ;cACP0I,IAAI,EAAC,MAAM;cACXC,WAAW,EAAE5G,QAAQ,CAACE,OAAO,GAAGF,QAAQ,GAAGwF,UAAW;cACtDhH,UAAU,EAAEA,UAAW;cACvBqI,QAAQ,EAAE,MAAOzC,aAAa,IAAK;gBACjC,IAAI;kBACF;kBACA,MAAMC,UAAU,GAAG;oBAAE,GAAGD;kBAAc,CAAC;kBACvC,OAAOC,UAAU,CAACjE,SAAS,CAAC,CAAC;kBAC7B,OAAOiE,UAAU,CAACjD,eAAe,CAAC,CAAC;kBACnC,OAAOiD,UAAU,CAACC,sBAAsB,CAAC,CAAC;kBAC1C,OAAOD,UAAU,CAACE,SAAS,CAAC,CAAC;kBAC7B,OAAOF,UAAU,CAACxC,mBAAmB,CAAC,CAAC;;kBAEvC;kBACAwC,UAAU,CAACC,sBAAsB,GAAG,CAAC;kBAErC,MAAM1G,WAAW,CAAC4G,UAAU,CAAChG,UAAU,EAAE6F,UAAU,CAACnE,OAAO,EAAEmE,UAAU,CAAC;kBACxE,OAAO,IAAI;gBACb,CAAC,CAAC,OAAOvC,KAAK,EAAE;kBACd,MAAMA,KAAK;gBACb;cACF,CAAE;cACFpD,SAAS,EAAG4D,OAAO,IAAK;gBACtB5D,SAAS,CAAC4D,OAAO,CAAC;gBAClB;cACF,CAAE;cACF3D,OAAO,EAAEA,OAAQ;cACjBoI,QAAQ,EAAE;YAAK;cAAAV,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAEhB,CAAC,gBAEFpI,OAAA,CAACF,iBAAiB;cAChBM,UAAU,EAAEA,UAAW;cACvBE,SAAS,EAAG4D,OAAO,IAAK;gBACtB5D,SAAS,CAAC4D,OAAO,CAAC;gBAClB;cACF,CAAE;cACF3D,OAAO,EAAEA,OAAQ;cACjBoI,QAAQ,EAAE;YAAK;cAAAV,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAEhB;UACF;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACE;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACO,CAAC;MAAA,eAChB,CAAC;IAEP,CAAC,MAAM,IAAI5G,UAAU,KAAK,aAAa,EAAE;MACvC;MACA,MAAMqF,YAAY,GAAGnF,YAAY,IAAIjB,eAAe;MACpD;MACA,MAAMqG,WAAW,GAAGD,YAAY,KAAKA,YAAY,CAACpD,mBAAmB,KAAK,YAAY,IAAKoD,YAAY,CAAC7D,eAAe,IAAI6D,YAAY,CAAC7D,eAAe,GAAG,CAAE,CAAC;MAE7J,oBACEhD,OAAA,CAAAE,SAAA;QAAA6H,QAAA,gBACE/H,OAAA,CAACtC,WAAW;UAAAqK,QAAA,EACT,CAAClB,YAAY,GAAG,cAAc,GAC9BC,WAAW,GAAG,uBAAuB,GAAG;QAAc;UAAAmB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC5C,CAAC,eACdpI,OAAA,CAACrC,aAAa;UAAAoK,QAAA,EACXtF,WAAW,gBACVzC,OAAA,CAAC3B,gBAAgB;YAAA4J,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,GAClB,CAACvB,YAAY,gBACf7G,OAAA,CAACjD,GAAG;YAAC2K,EAAE,EAAE;cAAEwC,CAAC,EAAE;YAAE,CAAE;YAAAnC,QAAA,gBAChB/H,OAAA,CAAChD,UAAU;cAAC8L,OAAO,EAAC,WAAW;cAACC,YAAY;cAAAhB,QAAA,EAAC;YAE7C;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAY,CAAC,eACbpI,OAAA,CAACjD,GAAG;cAAC2K,EAAE,EAAE;gBAAEyC,OAAO,EAAE,MAAM;gBAAEC,UAAU,EAAE,QAAQ;gBAAE9B,EAAE,EAAE;cAAE,CAAE;cAAAP,QAAA,gBACxD/H,OAAA,CAAClC,SAAS;gBACR0J,SAAS;gBACTiC,KAAK,EAAC,SAAS;gBACfX,OAAO,EAAC,UAAU;gBAClBhE,KAAK,EAAE7C,WAAY;gBACnB0H,QAAQ,EAAEhF,uBAAwB;gBAClC0F,WAAW,EAAC;cAAyB;gBAAApC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACtC,CAAC,eACFpI,OAAA,CAAC/C,MAAM;gBACL6L,OAAO,EAAC,WAAW;gBACnBwB,KAAK,EAAC,SAAS;gBACfpB,OAAO,EAAE3E,oBAAqB;gBAC9BuF,QAAQ,EAAErH,WAAW,IAAI,CAACR,WAAW,CAACuC,IAAI,CAAC,CAAE;gBAC7CkD,EAAE,EAAE;kBAAE6C,EAAE,EAAE,CAAC;kBAAEC,QAAQ,EAAE;gBAAQ,CAAE;gBAAAzC,QAAA,EAEhCtF,WAAW,gBAAGzC,OAAA,CAAC3B,gBAAgB;kBAAC2L,IAAI,EAAE;gBAAG;kBAAA/B,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAC,GAAG;cAAO;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACjD,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACN,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC,GACJ5G,UAAU,KAAK,aAAa,IAAIsF,WAAW,gBAC7C9G,OAAA,CAAAE,SAAA;YAAA6H,QAAA,gBACE/H,OAAA,CAACpC,iBAAiB;cAAAmK,QAAA,GAAC,UACT,eAAA/H,OAAA;gBAAA+H,QAAA,EAASlB,YAAY,CAAC/E;cAAO;gBAAAmG,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAS,CAAC,8CAC/C,EAACvB,YAAY,CAAC7D,eAAe,GAAG,CAAC,iBAC/BhD,OAAA,CAAAE,SAAA;gBAAA6H,QAAA,GAAE,iBAAe,eAAA/H,OAAA;kBAAA+H,QAAA,GAASlB,YAAY,CAAC7D,eAAe,EAAC,IAAE;gBAAA;kBAAAiF,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,KAAC;cAAA,eAAE,CACtE;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACgB,CAAC,eACpBpI,OAAA,CAACpC,iBAAiB;cAAC8J,EAAE,EAAE;gBAAEY,EAAE,EAAE;cAAE,CAAE;cAAAP,QAAA,EAAC;YAElC;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAmB,CAAC;UAAA,eACpB,CAAC,GACD5G,UAAU,KAAK,aAAa,gBAC9BxB,OAAA,CAAAE,SAAA;YAAA6H,QAAA,gBACE/H,OAAA,CAACpC,iBAAiB;cAAAmK,QAAA,GAAC,6BACU,eAAA/H,OAAA;gBAAA+H,QAAA,EAASlB,YAAY,CAAC/E;cAAO;gBAAAmG,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAS,CAAC,KACpE;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAmB,CAAC,eAEpBpI,OAAA,CAACjC,WAAW;cAAC0M,SAAS,EAAC,UAAU;cAAC/C,EAAE,EAAE;gBAAEY,EAAE,EAAE;cAAE,CAAE;cAAAP,QAAA,gBAC9C/H,OAAA,CAACtB,SAAS;gBAAC+L,SAAS,EAAC,QAAQ;gBAAA1C,QAAA,EAAC;cAAgC;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAW,CAAC,eAC1EpI,OAAA,CAACxB,UAAU;gBACTsG,KAAK,EAAEnC,UAAW;gBAClBgH,QAAQ,EAAG/E,CAAC,IAAKhC,aAAa,CAACgC,CAAC,CAACC,MAAM,CAACC,KAAK,CAAE;gBAAAiD,QAAA,gBAE/C/H,OAAA,CAACvB,gBAAgB;kBACfqG,KAAK,EAAC,OAAO;kBACb4F,OAAO,eAAE1K,OAAA,CAACzB,KAAK;oBAAA0J,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE,CAAE;kBACnBqB,KAAK,EAAC;gBAAqF;kBAAAxB,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAC5F,CAAC,eACFpI,OAAA,CAACvB,gBAAgB;kBACfqG,KAAK,EAAC,QAAQ;kBACd4F,OAAO,eAAE1K,OAAA,CAACzB,KAAK;oBAAA0J,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE,CAAE;kBACnBqB,KAAK,EAAC;gBAAsE;kBAAAxB,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAC7E,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACQ,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACF,CAAC;UAAA,eACd,CAAC,GACD;QAAI;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACK,CAAC,eAChBpI,OAAA,CAACnC,aAAa;UAAAkK,QAAA,EACXvG,UAAU,KAAK,aAAa,IAAIqF,YAAY,iBAC3C7G,OAAA,CAAC/C,MAAM;YACLiM,OAAO,EAAE3D,UAAW;YACpBuE,QAAQ,EAAE5I,OAAQ;YAClBoJ,KAAK,EAAExD,WAAW,GAAG,SAAS,GAAG,OAAQ;YACzCiD,SAAS,EAAE7I,OAAO,gBAAGlB,OAAA,CAAC3B,gBAAgB;cAAC2L,IAAI,EAAE;YAAG;cAAA/B,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,GAAGtB,WAAW,gBAAG9G,OAAA,CAACV,WAAW;cAAA2I,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,gBAAGpI,OAAA,CAAChB,UAAU;cAAAiJ,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAE;YAAAL,QAAA,EAEpGjB,WAAW,GAAG,kBAAkB,GAAInE,UAAU,KAAK,OAAO,GAAG,kBAAkB,GAAG;UAA0B;YAAAsF,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACvG;QACT;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACY,CAAC;MAAA,eAChB,CAAC;IAEP,CAAC,MAAM,IAAI5G,UAAU,KAAK,cAAc,EAAE;MACxC,oBACExB,OAAA,CAAAE,SAAA;QAAA6H,QAAA,gBACE/H,OAAA,CAACtC,WAAW;UAACgK,EAAE,EAAE;YAAEM,EAAE,EAAE;UAAE,CAAE;UAAAD,QAAA,EAAC;QAAmB;UAAAE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAa,CAAC,eAC7DpI,OAAA,CAACrC,aAAa;UAAC+J,EAAE,EAAE;YAAEW,EAAE,EAAE,CAAC;YAAEL,EAAE,EAAE;UAAE,CAAE;UAAAD,QAAA,eAClC/H,OAAA,CAACjD,GAAG;YAAC2K,EAAE,EAAE;cAAEY,EAAE,EAAE;YAAE,CAAE;YAAAP,QAAA,eACjB/H,OAAA,CAACH,QAAQ;cACP0I,IAAI,EAAC,KAAK;cACVnI,UAAU,EAAEA,UAAW;cACvBqI,QAAQ,EAAE,MAAOzC,aAAa,IAAK;gBACjC,IAAI;kBACF,MAAMxG,WAAW,CAACoJ,UAAU,CAACxI,UAAU,EAAE4F,aAAa,CAAC;kBACvD,OAAO,IAAI;gBACb,CAAC,CAAC,OAAOtC,KAAK,EAAE;kBACd,MAAMA,KAAK;gBACb;cACF,CAAE;cACFpD,SAAS,EAAG4D,OAAO,IAAK;gBACtB5D,SAAS,CAAC4D,OAAO,CAAC;gBAClB;cACF,CAAE;cACF3D,OAAO,EAAEA,OAAQ;cACjBoI,QAAQ,EAAE;YAAK;cAAAV,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAEhB;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACC;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACO,CAAC;MAAA,eAChB,CAAC;IAEP;IAEA,OAAO,IAAI;EACb,CAAC;;EAED;EACA,IAAI1H,UAAU,EAAE;IACd,OAAOiK,mBAAmB,CAAC,CAAC;EAC9B;EAEA,oBACE3K,OAAA,CAACjD,GAAG;IAAC2K,EAAE,EAAE;MAAEyC,OAAO,EAAE;IAAO,CAAE;IAAApC,QAAA,gBAE3B/H,OAAA,CAACjD,GAAG;MAAC2K,EAAE,EAAE;QAAEC,KAAK,EAAE,OAAO;QAAEiD,EAAE,EAAE;MAAE,CAAE;MAAA7C,QAAA,eACjC/H,OAAA,CAAC9C,KAAK;QAACwK,EAAE,EAAE;UAAEwC,CAAC,EAAE,CAAC;UAAED,EAAE,EAAE;QAAE,CAAE;QAAAlC,QAAA,gBACzB/H,OAAA,CAAChD,UAAU;UAAC8L,OAAO,EAAC,IAAI;UAACC,YAAY;UAAAhB,QAAA,EAAC;QAEtC;UAAAE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAY,CAAC,eACbpI,OAAA,CAAC7C,OAAO;UAACuK,EAAE,EAAE;YAAEuC,EAAE,EAAE;UAAE;QAAE;UAAAhC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,eAC1BpI,OAAA,CAAC5C,IAAI;UAACqN,SAAS,EAAC,KAAK;UAACI,KAAK;UAAA9C,QAAA,gBACzB/H,OAAA,CAACxC,cAAc;YAAC0L,OAAO,EAAEA,CAAA,KAAM/E,kBAAkB,CAAC,gBAAgB,CAAE;YAAA4D,QAAA,gBAClE/H,OAAA,CAACzC,YAAY;cAAAwK,QAAA,eACX/H,OAAA,CAACd,SAAS;gBAAA+I,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACD,CAAC,eACfpI,OAAA,CAAC1C,YAAY;cAAC6L,OAAO,EAAC;YAA2B;cAAAlB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACtC,CAAC,eAEjBpI,OAAA,CAACxC,cAAc;YAAC0L,OAAO,EAAEA,CAAA,KAAM/E,kBAAkB,CAAC,gBAAgB,CAAE;YAAA4D,QAAA,gBAClE/H,OAAA,CAACzC,YAAY;cAAAwK,QAAA,eACX/H,OAAA,CAAClB,QAAQ;gBAAAmJ,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACA,CAAC,eACfpI,OAAA,CAAC1C,YAAY;cAAC6L,OAAO,EAAC;YAAgC;cAAAlB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC3C,CAAC,eAEjBpI,OAAA,CAACxC,cAAc;YAAC0L,OAAO,EAAEA,CAAA,KAAM/E,kBAAkB,CAAC,cAAc,CAAE;YAAA4D,QAAA,gBAChE/H,OAAA,CAACzC,YAAY;cAAAwK,QAAA,eACX/H,OAAA,CAACpB,OAAO;gBAAAqJ,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACC,CAAC,eACfpI,OAAA,CAAC1C,YAAY;cAAC6L,OAAO,EAAC;YAAwB;cAAAlB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACnC,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACb,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACF;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACL,CAAC,eAGNpI,OAAA,CAACjD,GAAG;MAAC2K,EAAE,EAAE;QAAEoD,QAAQ,EAAE;MAAE,CAAE;MAAA/C,QAAA,eACvB/H,OAAA,CAAC9C,KAAK;QAACwK,EAAE,EAAE;UAAEwC,CAAC,EAAE,CAAC;UAAEtC,SAAS,EAAE,OAAO;UAAEuC,OAAO,EAAE,MAAM;UAAEC,UAAU,EAAE,QAAQ;UAAEW,cAAc,EAAE;QAAS,CAAE;QAAAhD,QAAA,GACtG,CAAC3G,cAAc,iBACdpB,OAAA,CAAChD,UAAU;UAAC8L,OAAO,EAAC,OAAO;UAAAf,QAAA,EAAC;QAE5B;UAAAE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAY,CACb,EACAhH,cAAc,IAAI,CAACE,UAAU,iBAC5BtB,OAAA,CAACjD,GAAG;UAAC2K,EAAE,EAAE;YAAEsD,SAAS,EAAE;UAAS,CAAE;UAAAjD,QAAA,gBAC/B/H,OAAA,CAAChD,UAAU;YAAC8L,OAAO,EAAC,IAAI;YAACC,YAAY;YAAAhB,QAAA,GAClC3G,cAAc,KAAK,gBAAgB,IAAI,wBAAwB,EAC/DA,cAAc,KAAK,cAAc,IAAI,eAAe,EACpDA,cAAc,KAAK,cAAc,IAAI,qBAAqB,EAC1DA,cAAc,KAAK,aAAa,IAAI,cAAc,EAClDA,cAAc,KAAK,gBAAgB,IAAI,6BAA6B;UAAA;YAAA6G,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC3D,CAAC,eACbpI,OAAA,CAAChD,UAAU;YAAC8L,OAAO,EAAC,OAAO;YAAAf,QAAA,EAAC;UAE5B;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAY,CAAC,eACbpI,OAAA,CAAC3B,gBAAgB;YAACqJ,EAAE,EAAE;cAAEY,EAAE,EAAE;YAAE;UAAE;YAAAL,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAChC,CACN;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACI;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACL,CAAC,EAELlB,YAAY,CAAC,CAAC;EAAA;IAAAe,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACZ,CAAC;AAEV,CAAC;AAACzH,EAAA,CA3mCIR,oBAAoB;EAAA,QAKPZ,WAAW;AAAA;AAAA0L,EAAA,GALxB9K,oBAAoB;AA6mC1B,eAAeA,oBAAoB;AAAC,IAAA8K,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}