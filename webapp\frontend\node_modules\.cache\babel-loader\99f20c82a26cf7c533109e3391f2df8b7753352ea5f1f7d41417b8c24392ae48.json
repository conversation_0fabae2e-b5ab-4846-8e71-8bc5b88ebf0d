{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\PycharmProjects\\\\CMS\\\\webapp\\\\frontend\\\\src\\\\components\\\\cavi\\\\ParcoCavi.js\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect } from 'react';\nimport { Box, Typography, Button, Paper, Grid, Card, CardContent, CardActions, Dialog, DialogTitle, DialogContent, DialogActions, TextField, FormControl, InputLabel, Select, MenuItem, List, ListItem, ListItemText, ListItemIcon, ListItemButton, Divider, Alert, CircularProgress, Table, TableBody, TableCell, TableContainer, TableHead, TableRow, FormHelperText } from '@mui/material';\nimport { Add as AddIcon, Edit as EditIcon, Delete as DeleteIcon, History as HistoryIcon, Save as SaveIcon, ViewList as ViewListIcon, Warning as WarningIcon } from '@mui/icons-material';\nimport parcoCaviService from '../../services/parcoCaviService';\nimport ConfigurazioneDialog from './ConfigurazioneDialog';\nimport { validateBobinaData, validateBobinaField, validateBobinaId, isEmpty } from '../../utils/bobinaValidationUtils';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst ParcoCavi = ({\n  cantiereId,\n  onSuccess,\n  onError,\n  initialOption = null\n}) => {\n  _s();\n  const [loading, setLoading] = useState(false);\n  const [bobine, setBobine] = useState([]);\n  const [selectedOption, setSelectedOption] = useState(initialOption);\n  const [openDialog, setOpenDialog] = useState(false);\n  const [dialogType, setDialogType] = useState('');\n  const [selectedBobina, setSelectedBobina] = useState(null);\n  const [formData, setFormData] = useState({\n    numero_bobina: '',\n    utility: '',\n    tipologia: '',\n    n_conduttori: '',\n    sezione: '',\n    metri_totali: '',\n    metri_residui: '',\n    stato_bobina: 'Disponibile',\n    ubicazione_bobina: '',\n    fornitore: '',\n    n_DDT: '',\n    data_DDT: '',\n    configurazione: ''\n  });\n  const [formErrors, setFormErrors] = useState({});\n  const [formWarnings, setFormWarnings] = useState({});\n  const [storicoUtilizzo, setStoricoUtilizzo] = useState([]);\n  const [openConfigDialog, setOpenConfigDialog] = useState(false);\n  const [isFirstInsertion, setIsFirstInsertion] = useState(false);\n\n  // Carica le bobine disponibili\n  const loadBobine = async () => {\n    try {\n      setLoading(true);\n      const data = await parcoCaviService.getBobine(cantiereId);\n      setBobine(data);\n    } catch (error) {\n      onError('Errore nel caricamento delle bobine');\n      console.error('Errore nel caricamento delle bobine:', error);\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  // Carica lo storico utilizzo bobine\n  const loadStoricoUtilizzo = async () => {\n    try {\n      setLoading(true);\n      const data = await parcoCaviService.getStoricoUtilizzo(cantiereId);\n      setStoricoUtilizzo(data);\n    } catch (error) {\n      onError('Errore nel caricamento dello storico utilizzo');\n      console.error('Errore nel caricamento dello storico utilizzo:', error);\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  // Carica i dati all'avvio del componente e gestisce l'opzione iniziale\n  // Utilizziamo una ref per tenere traccia se l'effetto è già stato eseguito\n  const initialLoadDone = React.useRef(false);\n  useEffect(() => {\n    // Esegui solo una volta all'avvio del componente\n    if (!initialLoadDone.current) {\n      console.log('Primo caricamento del componente, initialOption:', initialOption);\n      initialLoadDone.current = true;\n      if (initialOption === 'creaBobina') {\n        console.log('Avvio processo creazione bobina');\n        checkIfFirstInsertion();\n      } else if (initialOption) {\n        console.log('Eseguendo handleOptionSelect con:', initialOption);\n        handleOptionSelect(initialOption);\n      } else {\n        console.log('Caricando bobine');\n        loadBobine();\n      }\n    }\n  }, []); // Dipendenze vuote per eseguire solo al mount\n\n  // Verifica se è il primo inserimento di una bobina per un cantiere\n  const checkIfFirstInsertion = async () => {\n    try {\n      setLoading(true);\n      const isFirst = await parcoCaviService.isFirstBobinaInsertion(cantiereId);\n      setIsFirstInsertion(isFirst);\n      console.log('È il primo inserimento di una bobina?', isFirst);\n      if (isFirst) {\n        // Se è il primo inserimento, mostra il dialog di configurazione\n        console.log('Mostrando il dialog di configurazione');\n        setOpenConfigDialog(true);\n      } else {\n        // Non è il primo inserimento, procedi con il form normale\n        console.log('Non è il primo inserimento, mostrando il form normale');\n        setDialogType('creaBobina');\n        setFormData({\n          numero_bobina: '',\n          utility: '',\n          tipologia: '',\n          n_conduttori: '',\n          sezione: '',\n          metri_totali: '',\n          metri_residui: '',\n          stato_bobina: 'Disponibile',\n          ubicazione_bobina: '',\n          fornitore: '',\n          n_DDT: '',\n          data_DDT: '',\n          configurazione: ''\n        });\n        setOpenDialog(true);\n      }\n    } catch (error) {\n      onError('Errore nel controllo dell\\'inserimento della prima bobina');\n      console.error('Errore nel controllo dell\\'inserimento della prima bobina:', error);\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  // Gestisce la conferma della configurazione\n  const handleConfigConfirm = configValue => {\n    console.log('Configurazione selezionata:', configValue);\n    // Chiudi il dialog di configurazione\n    setOpenConfigDialog(false);\n\n    // Imposta i valori di default per la bobina\n    const defaultFormData = {\n      numero_bobina: configValue === 's' ? '1' : '',\n      // Se usiamo numeri progressivi, imposta il default a 1\n      utility: '',\n      tipologia: '',\n      n_conduttori: '',\n      sezione: '',\n      metri_totali: '',\n      metri_residui: '',\n      stato_bobina: 'Disponibile',\n      ubicazione_bobina: '',\n      fornitore: '',\n      n_DDT: '',\n      data_DDT: '',\n      configurazione: configValue // Imposta la configurazione scelta\n    };\n    console.log('Impostando i dati del form con configurazione:', configValue);\n    setFormData(defaultFormData);\n    setDialogType('creaBobina');\n    setOpenDialog(true);\n    console.log('Dialog di creazione bobina aperto');\n  };\n\n  // Gestisce la selezione di un'opzione dal menu\n  const handleOptionSelect = option => {\n    setSelectedOption(option);\n    if (option === 'visualizzaBobine') {\n      loadBobine();\n    } else if (option === 'creaBobina') {\n      checkIfFirstInsertion();\n    } else if (option === 'modificaBobina') {\n      loadBobine();\n      setDialogType('selezionaBobina');\n      setOpenDialog(true);\n    } else if (option === 'eliminaBobina') {\n      loadBobine();\n      setDialogType('eliminaBobina');\n      setOpenDialog(true);\n    } else if (option === 'visualizzaStorico') {\n      loadStoricoUtilizzo();\n      setDialogType('visualizzaStorico');\n      setOpenDialog(true);\n    }\n  };\n\n  // Gestisce la chiusura del dialog\n  const handleCloseDialog = () => {\n    setOpenDialog(false);\n    setSelectedBobina(null);\n    setFormData({\n      numero_bobina: '',\n      utility: '',\n      tipologia: '',\n      n_conduttori: '',\n      sezione: '',\n      metri_totali: '',\n      metri_residui: '',\n      stato_bobina: 'Disponibile',\n      ubicazione_bobina: '',\n      fornitore: '',\n      n_DDT: '',\n      data_DDT: '',\n      configurazione: ''\n    });\n    setFormErrors({});\n    setFormWarnings({});\n  };\n\n  // Gestisce la selezione di una bobina\n  const handleBobinaSelect = bobina => {\n    setSelectedBobina(bobina);\n    if (dialogType === 'selezionaBobina') {\n      setDialogType('modificaBobina');\n      setFormData({\n        numero_bobina: bobina.numero_bobina,\n        utility: bobina.utility || '',\n        tipologia: bobina.tipologia || '',\n        n_conduttori: bobina.n_conduttori || '',\n        sezione: bobina.sezione || '',\n        metri_totali: bobina.metri_totali || '',\n        metri_residui: bobina.metri_residui || '',\n        stato_bobina: bobina.stato_bobina || 'DISPONIBILE',\n        ubicazione_bobina: bobina.ubicazione_bobina || '',\n        fornitore: bobina.fornitore || '',\n        n_DDT: bobina.n_DDT || '',\n        data_DDT: bobina.data_DDT || '',\n        configurazione: bobina.configurazione || ''\n      });\n    }\n  };\n\n  // Gestisce il cambio dei valori nel form con validazione\n  const handleFormChange = e => {\n    const {\n      name,\n      value\n    } = e.target;\n\n    // Aggiorna il valore nel form\n    setFormData({\n      ...formData,\n      [name]: value\n    });\n\n    // Valida il campo\n    if (dialogType === 'creaBobina' || dialogType === 'modificaBobina') {\n      // Validazione speciale per numero_bobina quando configurazione è 'n'\n      if (name === 'numero_bobina' && formData.configurazione === 'n') {\n        const idResult = validateBobinaId(value);\n        if (!idResult.valid) {\n          setFormErrors(prev => ({\n            ...prev,\n            [name]: idResult.message\n          }));\n        } else {\n          setFormErrors(prev => {\n            const newErrors = {\n              ...prev\n            };\n            delete newErrors[name];\n            return newErrors;\n          });\n        }\n        return;\n      }\n      const result = validateBobinaField(name, value);\n\n      // Aggiorna gli errori\n      if (!result.valid) {\n        setFormErrors(prev => ({\n          ...prev,\n          [name]: result.message\n        }));\n      } else {\n        setFormErrors(prev => {\n          const newErrors = {\n            ...prev\n          };\n          delete newErrors[name];\n          return newErrors;\n        });\n\n        // Aggiorna gli avvisi\n        if (result.warning) {\n          setFormWarnings(prev => ({\n            ...prev,\n            [name]: result.message\n          }));\n        } else {\n          setFormWarnings(prev => {\n            const newWarnings = {\n              ...prev\n            };\n            delete newWarnings[name];\n            return newWarnings;\n          });\n        }\n      }\n    }\n  };\n\n  // Gestisce il salvataggio del form\n  const handleSave = async () => {\n    try {\n      // Validazione completa dei dati prima del salvataggio\n      if (dialogType === 'creaBobina' || dialogType === 'modificaBobina') {\n        const validation = validateBobinaData(formData);\n        if (!validation.isValid) {\n          setFormErrors(validation.errors);\n          setFormWarnings(validation.warnings);\n          onError('Correggi gli errori nel form prima di salvare');\n          return;\n        }\n      }\n      setLoading(true);\n      if (dialogType === 'creaBobina') {\n        // Prepara i dati per la creazione della bobina\n        const bobinaData = {\n          ...formData,\n          // Converti i campi numerici da stringa a numero\n          metri_totali: parseFloat(formData.metri_totali),\n          // Assicurati che la configurazione sia impostata correttamente\n          configurazione: formData.configurazione || 's'\n        };\n\n        // Invia i dati al backend\n        await parcoCaviService.createBobina(cantiereId, bobinaData);\n        onSuccess('Bobina creata con successo');\n\n        // Ricarica le bobine e chiudi il dialog\n        loadBobine();\n        handleCloseDialog();\n      } else if (dialogType === 'modificaBobina') {\n        // Per la modifica, usa l'ID bobina completo o il numero bobina\n        const bobinaId = selectedBobina.id_bobina || formData.numero_bobina;\n        await parcoCaviService.updateBobina(cantiereId, bobinaId, formData);\n        onSuccess('Bobina modificata con successo');\n      } else if (dialogType === 'eliminaBobina') {\n        // Per l'eliminazione, usa l'ID bobina completo\n        const bobinaId = selectedBobina.id_bobina || selectedBobina.numero_bobina;\n        await parcoCaviService.deleteBobina(cantiereId, bobinaId);\n        onSuccess('Bobina eliminata con successo');\n      }\n      handleCloseDialog();\n      loadBobine(); // Ricarica le bobine dopo l'operazione\n    } catch (error) {\n      onError('Errore durante l\\'operazione: ' + (error.detail || error.message || 'Errore sconosciuto'));\n      console.error('Errore durante l\\'operazione:', error);\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  // Renderizza le bobine in formato card\n  const renderBobineCards = () => {\n    if (bobine.length === 0) {\n      return /*#__PURE__*/_jsxDEV(Alert, {\n        severity: \"info\",\n        children: \"Nessuna bobina disponibile\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 390,\n        columnNumber: 9\n      }, this);\n    }\n    return /*#__PURE__*/_jsxDEV(Grid, {\n      container: true,\n      spacing: 2,\n      children: bobine.map(bobina => /*#__PURE__*/_jsxDEV(Grid, {\n        item: true,\n        xs: 12,\n        sm: 6,\n        md: 4,\n        children: /*#__PURE__*/_jsxDEV(Card, {\n          children: [/*#__PURE__*/_jsxDEV(CardContent, {\n            children: [/*#__PURE__*/_jsxDEV(Typography, {\n              variant: \"h6\",\n              component: \"div\",\n              children: [\"Bobina: \", bobina.numero_bobina]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 400,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(Typography, {\n              variant: \"body2\",\n              color: \"text.secondary\",\n              children: [\"Utility: \", bobina.utility || 'N/A']\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 403,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(Typography, {\n              variant: \"body2\",\n              color: \"text.secondary\",\n              children: [\"Tipologia: \", bobina.tipologia || 'N/A']\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 406,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(Typography, {\n              variant: \"body2\",\n              color: \"text.secondary\",\n              children: [\"N\\xB0 Conduttori: \", bobina.n_conduttori || 'N/A']\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 409,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(Typography, {\n              variant: \"body2\",\n              color: \"text.secondary\",\n              children: [\"Sezione: \", bobina.sezione || 'N/A']\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 412,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(Typography, {\n              variant: \"body2\",\n              color: \"text.secondary\",\n              children: [\"Metri totali: \", bobina.metri_totali || 'N/A', \" m\"]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 415,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(Typography, {\n              variant: \"body2\",\n              color: \"text.secondary\",\n              children: [\"Metri residui: \", bobina.metri_residui || 'N/A', \" m\"]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 418,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(Typography, {\n              variant: \"body2\",\n              color: \"text.secondary\",\n              children: [\"Stato: \", bobina.stato_bobina || 'DISPONIBILE']\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 421,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(Typography, {\n              variant: \"body2\",\n              color: \"text.secondary\",\n              children: [\"Ubicazione: \", bobina.ubicazione_bobina || 'N/A']\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 424,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(Typography, {\n              variant: \"body2\",\n              color: \"text.secondary\",\n              children: [\"Fornitore: \", bobina.fornitore || 'N/A']\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 427,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 399,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(CardActions, {\n            children: [/*#__PURE__*/_jsxDEV(Button, {\n              size: \"small\",\n              startIcon: /*#__PURE__*/_jsxDEV(EditIcon, {}, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 434,\n                columnNumber: 30\n              }, this),\n              onClick: () => {\n                setDialogType('selezionaBobina');\n                handleBobinaSelect(bobina);\n              },\n              children: \"Modifica\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 432,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(Button, {\n              size: \"small\",\n              color: \"error\",\n              startIcon: /*#__PURE__*/_jsxDEV(DeleteIcon, {}, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 445,\n                columnNumber: 30\n              }, this),\n              onClick: () => {\n                setDialogType('eliminaBobina');\n                setSelectedBobina(bobina);\n                setOpenDialog(true);\n              },\n              children: \"Elimina\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 442,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 431,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 398,\n          columnNumber: 13\n        }, this)\n      }, bobina.numero_bobina, false, {\n        fileName: _jsxFileName,\n        lineNumber: 397,\n        columnNumber: 11\n      }, this))\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 395,\n      columnNumber: 7\n    }, this);\n  };\n\n  // Renderizza il dialog in base al tipo\n  const renderDialog = () => {\n    if (dialogType === 'creaBobina' || dialogType === 'modificaBobina') {\n      return /*#__PURE__*/_jsxDEV(Dialog, {\n        open: openDialog,\n        onClose: handleCloseDialog,\n        maxWidth: \"md\",\n        fullWidth: true,\n        children: [/*#__PURE__*/_jsxDEV(DialogTitle, {\n          children: dialogType === 'creaBobina' ? 'Crea Nuova Bobina' : 'Modifica Bobina'\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 467,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(DialogContent, {\n          children: [Object.keys(formWarnings).length > 0 && /*#__PURE__*/_jsxDEV(Alert, {\n            severity: \"warning\",\n            sx: {\n              mb: 2,\n              mt: 1\n            },\n            children: [/*#__PURE__*/_jsxDEV(Typography, {\n              variant: \"subtitle2\",\n              children: \"Attenzione:\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 473,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"ul\", {\n              style: {\n                margin: 0,\n                paddingLeft: '20px'\n              },\n              children: Object.values(formWarnings).map((warning, index) => /*#__PURE__*/_jsxDEV(\"li\", {\n                children: warning\n              }, index, false, {\n                fileName: _jsxFileName,\n                lineNumber: 476,\n                columnNumber: 21\n              }, this))\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 474,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 472,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(Grid, {\n            container: true,\n            spacing: 2,\n            sx: {\n              mt: 1\n            },\n            children: [/*#__PURE__*/_jsxDEV(Grid, {\n              item: true,\n              xs: 12,\n              sm: 6,\n              children: /*#__PURE__*/_jsxDEV(TextField, {\n                name: \"numero_bobina\",\n                label: formData.configurazione === 's' ? \"Numero Bobina\" : \"ID Bobina\",\n                fullWidth: true,\n                variant: \"outlined\",\n                value: formData.numero_bobina,\n                onChange: handleFormChange,\n                disabled: dialogType === 'modificaBobina' || dialogType === 'creaBobina' && formData.configurazione === 's',\n                required: true,\n                error: !!formErrors.numero_bobina,\n                helperText: formErrors.numero_bobina || (dialogType === 'creaBobina' && formData.configurazione === 's' ? 'Numero generato automaticamente' : dialogType === 'creaBobina' && formData.configurazione === 'n' ? 'Inserisci l\\'ID della bobina (es. A123)' : '')\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 484,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 483,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Grid, {\n              item: true,\n              xs: 12,\n              sm: 6,\n              children: /*#__PURE__*/_jsxDEV(TextField, {\n                name: \"utility\",\n                label: \"Utility\",\n                fullWidth: true,\n                variant: \"outlined\",\n                value: formData.utility,\n                onChange: handleFormChange,\n                required: true,\n                error: !!formErrors.utility,\n                helperText: formErrors.utility || ''\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 505,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 504,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Grid, {\n              item: true,\n              xs: 12,\n              sm: 6,\n              children: /*#__PURE__*/_jsxDEV(TextField, {\n                name: \"tipologia\",\n                label: \"Tipologia\",\n                fullWidth: true,\n                variant: \"outlined\",\n                value: formData.tipologia,\n                onChange: handleFormChange,\n                required: true,\n                error: !!formErrors.tipologia,\n                helperText: formErrors.tipologia || ''\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 518,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 517,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Grid, {\n              item: true,\n              xs: 12,\n              sm: 6,\n              children: /*#__PURE__*/_jsxDEV(TextField, {\n                name: \"n_conduttori\",\n                label: \"N\\xB0 Conduttori\",\n                fullWidth: true,\n                variant: \"outlined\",\n                value: formData.n_conduttori,\n                onChange: handleFormChange,\n                required: true,\n                error: !!formErrors.n_conduttori,\n                helperText: formErrors.n_conduttori || ''\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 531,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 530,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Grid, {\n              item: true,\n              xs: 12,\n              sm: 6,\n              children: /*#__PURE__*/_jsxDEV(TextField, {\n                name: \"sezione\",\n                label: \"Sezione\",\n                fullWidth: true,\n                variant: \"outlined\",\n                value: formData.sezione,\n                onChange: handleFormChange,\n                required: true,\n                error: !!formErrors.sezione,\n                helperText: formErrors.sezione || ''\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 544,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 543,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Grid, {\n              item: true,\n              xs: 12,\n              sm: 6,\n              children: /*#__PURE__*/_jsxDEV(TextField, {\n                name: \"metri_totali\",\n                label: \"Metri Totali\",\n                type: \"number\",\n                fullWidth: true,\n                variant: \"outlined\",\n                value: formData.metri_totali,\n                onChange: handleFormChange,\n                required: true,\n                error: !!formErrors.metri_totali,\n                helperText: formErrors.metri_totali || ''\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 557,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 556,\n              columnNumber: 15\n            }, this), dialogType === 'modificaBobina' && /*#__PURE__*/_jsxDEV(Grid, {\n              item: true,\n              xs: 12,\n              sm: 6,\n              children: /*#__PURE__*/_jsxDEV(TextField, {\n                name: \"metri_residui\",\n                label: \"Metri Residui\",\n                type: \"number\",\n                fullWidth: true,\n                variant: \"outlined\",\n                value: formData.metri_residui,\n                onChange: handleFormChange,\n                required: true,\n                disabled: true,\n                helperText: \"I metri residui non possono essere modificati direttamente\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 572,\n                columnNumber: 19\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 571,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(Grid, {\n              item: true,\n              xs: 12,\n              sm: 6,\n              children: /*#__PURE__*/_jsxDEV(FormControl, {\n                fullWidth: true,\n                children: [/*#__PURE__*/_jsxDEV(InputLabel, {\n                  id: \"stato-bobina-label\",\n                  children: \"Stato Bobina\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 588,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(Select, {\n                  labelId: \"stato-bobina-label\",\n                  name: \"stato_bobina\",\n                  value: formData.stato_bobina,\n                  label: \"Stato Bobina\",\n                  onChange: handleFormChange,\n                  disabled: dialogType === 'creaBobina',\n                  children: [/*#__PURE__*/_jsxDEV(MenuItem, {\n                    value: \"Disponibile\",\n                    children: \"Disponibile\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 597,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(MenuItem, {\n                    value: \"In uso\",\n                    children: \"In uso\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 598,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(MenuItem, {\n                    value: \"Terminata\",\n                    children: \"Terminata\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 599,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(MenuItem, {\n                    value: \"Danneggiata\",\n                    children: \"Danneggiata\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 600,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(MenuItem, {\n                    value: \"Over\",\n                    children: \"Over\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 601,\n                    columnNumber: 21\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 589,\n                  columnNumber: 19\n                }, this), dialogType === 'creaBobina' && /*#__PURE__*/_jsxDEV(FormHelperText, {\n                  children: \"Per una nuova bobina, lo stato \\xE8 sempre \\\"Disponibile\\\"\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 604,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 587,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 586,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Grid, {\n              item: true,\n              xs: 12,\n              sm: 6,\n              children: /*#__PURE__*/_jsxDEV(TextField, {\n                name: \"ubicazione_bobina\",\n                label: \"Ubicazione Bobina\",\n                fullWidth: true,\n                variant: \"outlined\",\n                value: formData.ubicazione_bobina,\n                onChange: handleFormChange,\n                error: !!formErrors.ubicazione_bobina,\n                helperText: formErrors.ubicazione_bobina || ''\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 609,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 608,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Grid, {\n              item: true,\n              xs: 12,\n              sm: 6,\n              children: /*#__PURE__*/_jsxDEV(TextField, {\n                name: \"fornitore\",\n                label: \"Fornitore\",\n                fullWidth: true,\n                variant: \"outlined\",\n                value: formData.fornitore,\n                onChange: handleFormChange,\n                error: !!formErrors.fornitore,\n                helperText: formErrors.fornitore || ''\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 621,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 620,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Grid, {\n              item: true,\n              xs: 12,\n              sm: 6,\n              children: /*#__PURE__*/_jsxDEV(TextField, {\n                name: \"n_DDT\",\n                label: \"Numero DDT\",\n                fullWidth: true,\n                variant: \"outlined\",\n                value: formData.n_DDT,\n                onChange: handleFormChange,\n                error: !!formErrors.n_DDT,\n                helperText: formErrors.n_DDT || ''\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 633,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 632,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Grid, {\n              item: true,\n              xs: 12,\n              sm: 6,\n              children: /*#__PURE__*/_jsxDEV(TextField, {\n                name: \"data_DDT\",\n                label: \"Data DDT (YYYY-MM-DD)\",\n                fullWidth: true,\n                variant: \"outlined\",\n                value: formData.data_DDT,\n                onChange: handleFormChange,\n                placeholder: \"YYYY-MM-DD\",\n                error: !!formErrors.data_DDT,\n                helperText: formErrors.data_DDT || ''\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 645,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 644,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Grid, {\n              item: true,\n              xs: 12,\n              sm: 6,\n              children: /*#__PURE__*/_jsxDEV(TextField, {\n                name: \"configurazione\",\n                label: \"Configurazione\",\n                fullWidth: true,\n                variant: \"outlined\",\n                value: formData.configurazione,\n                onChange: handleFormChange,\n                disabled: true,\n                helperText: formData.configurazione === 's' ? 'Usa numeri progressivi' : 'Inserimento manuale ID bobina'\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 658,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 657,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 482,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 470,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(DialogActions, {\n          children: [/*#__PURE__*/_jsxDEV(Button, {\n            onClick: handleCloseDialog,\n            children: \"Annulla\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 672,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Button, {\n            onClick: handleSave,\n            disabled: loading || Object.keys(formErrors).length > 0,\n            startIcon: loading ? /*#__PURE__*/_jsxDEV(CircularProgress, {\n              size: 20\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 676,\n              columnNumber: 36\n            }, this) : /*#__PURE__*/_jsxDEV(SaveIcon, {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 676,\n              columnNumber: 69\n            }, this),\n            variant: \"contained\",\n            color: \"primary\",\n            children: \"Salva\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 673,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 671,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 466,\n        columnNumber: 9\n      }, this);\n    } else if (dialogType === 'selezionaBobina') {\n      return /*#__PURE__*/_jsxDEV(Dialog, {\n        open: openDialog,\n        onClose: handleCloseDialog,\n        maxWidth: \"md\",\n        fullWidth: true,\n        children: [/*#__PURE__*/_jsxDEV(DialogTitle, {\n          children: \"Seleziona Bobina da Modificare\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 688,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(DialogContent, {\n          children: loading ? /*#__PURE__*/_jsxDEV(CircularProgress, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 691,\n            columnNumber: 15\n          }, this) : bobine.length === 0 ? /*#__PURE__*/_jsxDEV(Alert, {\n            severity: \"info\",\n            children: \"Nessuna bobina disponibile\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 693,\n            columnNumber: 15\n          }, this) : /*#__PURE__*/_jsxDEV(List, {\n            children: bobine.map(bobina => /*#__PURE__*/_jsxDEV(ListItem, {\n              button: true,\n              onClick: () => handleBobinaSelect(bobina),\n              children: /*#__PURE__*/_jsxDEV(ListItemText, {\n                primary: `Bobina: ${bobina.numero_bobina}`,\n                secondary: `Tipologia: ${bobina.tipologia || 'N/A'} - Utility: ${bobina.utility || 'N/A'} - Residuo: ${bobina.metri_residui || 'N/A'} m`\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 702,\n                columnNumber: 21\n              }, this)\n            }, bobina.numero_bobina, false, {\n              fileName: _jsxFileName,\n              lineNumber: 697,\n              columnNumber: 19\n            }, this))\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 695,\n            columnNumber: 15\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 689,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(DialogActions, {\n          children: /*#__PURE__*/_jsxDEV(Button, {\n            onClick: handleCloseDialog,\n            children: \"Annulla\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 712,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 711,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 687,\n        columnNumber: 9\n      }, this);\n    } else if (dialogType === 'eliminaBobina') {\n      return /*#__PURE__*/_jsxDEV(Dialog, {\n        open: openDialog,\n        onClose: handleCloseDialog,\n        maxWidth: \"sm\",\n        fullWidth: true,\n        children: [/*#__PURE__*/_jsxDEV(DialogTitle, {\n          children: \"Elimina Bobina\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 719,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(DialogContent, {\n          children: !selectedBobina ? loading ? /*#__PURE__*/_jsxDEV(CircularProgress, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 723,\n            columnNumber: 17\n          }, this) : bobine.length === 0 ? /*#__PURE__*/_jsxDEV(Alert, {\n            severity: \"info\",\n            children: \"Nessuna bobina disponibile\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 725,\n            columnNumber: 17\n          }, this) : /*#__PURE__*/_jsxDEV(List, {\n            children: bobine.map(bobina => /*#__PURE__*/_jsxDEV(ListItem, {\n              button: true,\n              onClick: () => setSelectedBobina(bobina),\n              children: /*#__PURE__*/_jsxDEV(ListItemText, {\n                primary: `Bobina: ${bobina.numero_bobina}`,\n                secondary: `Tipologia: ${bobina.tipologia || 'N/A'} - Utility: ${bobina.utility || 'N/A'} - Residuo: ${bobina.metri_residui || 'N/A'} m`\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 734,\n                columnNumber: 23\n              }, this)\n            }, bobina.numero_bobina, false, {\n              fileName: _jsxFileName,\n              lineNumber: 729,\n              columnNumber: 21\n            }, this))\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 727,\n            columnNumber: 17\n          }, this) : /*#__PURE__*/_jsxDEV(Box, {\n            children: [/*#__PURE__*/_jsxDEV(Alert, {\n              severity: \"warning\",\n              sx: {\n                mb: 2\n              },\n              children: [\"Sei sicuro di voler eliminare la bobina \", selectedBobina.numero_bobina, \"?\"]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 744,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(Typography, {\n              variant: \"body1\",\n              children: \"Questa operazione non pu\\xF2 essere annullata.\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 747,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 743,\n            columnNumber: 15\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 720,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(DialogActions, {\n          children: [/*#__PURE__*/_jsxDEV(Button, {\n            onClick: handleCloseDialog,\n            children: \"Annulla\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 754,\n            columnNumber: 13\n          }, this), selectedBobina && /*#__PURE__*/_jsxDEV(Button, {\n            onClick: handleSave,\n            disabled: loading,\n            color: \"error\",\n            startIcon: loading ? /*#__PURE__*/_jsxDEV(CircularProgress, {\n              size: 20\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 760,\n              columnNumber: 38\n            }, this) : /*#__PURE__*/_jsxDEV(DeleteIcon, {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 760,\n              columnNumber: 71\n            }, this),\n            children: \"Elimina\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 756,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 753,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 718,\n        columnNumber: 9\n      }, this);\n    } else if (dialogType === 'visualizzaStorico') {\n      return /*#__PURE__*/_jsxDEV(Dialog, {\n        open: openDialog,\n        onClose: handleCloseDialog,\n        maxWidth: \"lg\",\n        fullWidth: true,\n        children: [/*#__PURE__*/_jsxDEV(DialogTitle, {\n          children: \"Storico Utilizzo Bobine\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 771,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(DialogContent, {\n          children: loading ? /*#__PURE__*/_jsxDEV(CircularProgress, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 774,\n            columnNumber: 15\n          }, this) : storicoUtilizzo.length === 0 ? /*#__PURE__*/_jsxDEV(Alert, {\n            severity: \"info\",\n            children: \"Nessun dato storico disponibile\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 776,\n            columnNumber: 15\n          }, this) : /*#__PURE__*/_jsxDEV(TableContainer, {\n            component: Paper,\n            sx: {\n              mt: 2\n            },\n            children: /*#__PURE__*/_jsxDEV(Table, {\n              size: \"small\",\n              children: [/*#__PURE__*/_jsxDEV(TableHead, {\n                children: /*#__PURE__*/_jsxDEV(TableRow, {\n                  children: [/*#__PURE__*/_jsxDEV(TableCell, {\n                    children: \"Bobina\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 782,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n                    children: \"Utility\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 783,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n                    children: \"Tipologia\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 784,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n                    children: \"N\\xB0 Conduttori\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 785,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n                    children: \"Sezione\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 786,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n                    children: \"Metri Totali\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 787,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n                    children: \"Metri Residui\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 788,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n                    children: \"Cavi Associati\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 789,\n                    columnNumber: 23\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 781,\n                  columnNumber: 21\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 780,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(TableBody, {\n                children: storicoUtilizzo.map((record, index) => /*#__PURE__*/_jsxDEV(TableRow, {\n                  children: [/*#__PURE__*/_jsxDEV(TableCell, {\n                    children: record.numero_bobina\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 795,\n                    columnNumber: 25\n                  }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n                    children: record.utility\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 796,\n                    columnNumber: 25\n                  }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n                    children: record.tipologia\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 797,\n                    columnNumber: 25\n                  }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n                    children: record.n_conduttori\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 798,\n                    columnNumber: 25\n                  }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n                    children: record.sezione\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 799,\n                    columnNumber: 25\n                  }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n                    children: record.metri_totali\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 800,\n                    columnNumber: 25\n                  }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n                    children: record.metri_residui\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 801,\n                    columnNumber: 25\n                  }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n                    children: record.cavi.length\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 802,\n                    columnNumber: 25\n                  }, this)]\n                }, index, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 794,\n                  columnNumber: 23\n                }, this))\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 792,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 779,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 778,\n            columnNumber: 15\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 772,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(DialogActions, {\n          children: /*#__PURE__*/_jsxDEV(Button, {\n            onClick: handleCloseDialog,\n            children: \"Chiudi\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 811,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 810,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 770,\n        columnNumber: 9\n      }, this);\n    }\n    return null;\n  };\n  return /*#__PURE__*/_jsxDEV(Box, {\n    children: [selectedOption === 'visualizzaBobine' && !openDialog ? /*#__PURE__*/_jsxDEV(Paper, {\n      sx: {\n        p: 3\n      },\n      children: [/*#__PURE__*/_jsxDEV(Typography, {\n        variant: \"h6\",\n        gutterBottom: true,\n        children: \"Bobine Disponibili\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 824,\n        columnNumber: 11\n      }, this), loading ? /*#__PURE__*/_jsxDEV(Box, {\n        sx: {\n          display: 'flex',\n          justifyContent: 'center',\n          my: 4\n        },\n        children: /*#__PURE__*/_jsxDEV(CircularProgress, {}, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 829,\n          columnNumber: 15\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 828,\n        columnNumber: 13\n      }, this) : renderBobineCards()]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 823,\n      columnNumber: 9\n    }, this) : !openDialog ? /*#__PURE__*/_jsxDEV(Paper, {\n      sx: {\n        p: 3,\n        minHeight: '300px',\n        display: 'flex',\n        alignItems: 'center',\n        justifyContent: 'center'\n      },\n      children: !selectedOption ? /*#__PURE__*/_jsxDEV(Typography, {\n        variant: \"body1\",\n        children: \"Seleziona un'opzione dal menu principale per iniziare.\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 838,\n        columnNumber: 13\n      }, this) : /*#__PURE__*/_jsxDEV(Box, {\n        sx: {\n          textAlign: 'center'\n        },\n        children: [/*#__PURE__*/_jsxDEV(Typography, {\n          variant: \"h6\",\n          gutterBottom: true,\n          children: [selectedOption === 'creaBobina' && 'Crea Nuova Bobina', selectedOption === 'modificaBobina' && 'Modifica Bobina', selectedOption === 'eliminaBobina' && 'Elimina Bobina', selectedOption === 'visualizzaStorico' && 'Visualizza Storico Utilizzo']\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 843,\n          columnNumber: 15\n        }, this), /*#__PURE__*/_jsxDEV(CircularProgress, {\n          sx: {\n            mt: 2\n          }\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 849,\n          columnNumber: 15\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 842,\n        columnNumber: 13\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 836,\n      columnNumber: 9\n    }, this) : null, renderDialog(), /*#__PURE__*/_jsxDEV(ConfigurazioneDialog, {\n      open: openConfigDialog,\n      onClose: () => setOpenConfigDialog(false),\n      onConfirm: handleConfigConfirm\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 858,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 821,\n    columnNumber: 5\n  }, this);\n};\n_s(ParcoCavi, \"aPVvt0JmyP4/WtJb67LlF7JuBzs=\");\n_c = ParcoCavi;\nexport default ParcoCavi;\nvar _c;\n$RefreshReg$(_c, \"ParcoCavi\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "Box", "Typography", "<PERSON><PERSON>", "Paper", "Grid", "Card", "<PERSON><PERSON><PERSON><PERSON>", "CardActions", "Dialog", "DialogTitle", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "DialogActions", "TextField", "FormControl", "InputLabel", "Select", "MenuItem", "List", "ListItem", "ListItemText", "ListItemIcon", "ListItemButton", "Divider", "<PERSON><PERSON>", "CircularProgress", "Table", "TableBody", "TableCell", "TableContainer", "TableHead", "TableRow", "FormHelperText", "Add", "AddIcon", "Edit", "EditIcon", "Delete", "DeleteIcon", "History", "HistoryIcon", "Save", "SaveIcon", "ViewList", "ViewListIcon", "Warning", "WarningIcon", "parcoCaviService", "ConfigurazioneDialog", "validateBobinaData", "validateBob<PERSON>F<PERSON>", "validateBobinaId", "isEmpty", "jsxDEV", "_jsxDEV", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "cantiereId", "onSuccess", "onError", "initialOption", "_s", "loading", "setLoading", "bobine", "set<PERSON>ob<PERSON>", "selectedOption", "setSelectedOption", "openDialog", "setOpenDialog", "dialogType", "setDialogType", "<PERSON><PERSON><PERSON><PERSON>", "setSelectedBobina", "formData", "setFormData", "numero_bobina", "utility", "tipologia", "n_conduttori", "sezione", "metri_totali", "metri_residui", "stato_bobina", "ubicazione_bobina", "fornitore", "n_DDT", "data_DDT", "configurazione", "formErrors", "setFormErrors", "formWarnings", "setForm<PERSON><PERSON>nings", "storico<PERSON><PERSON><PERSON><PERSON>", "setStoricoUtilizzo", "openConfigDialog", "setOpenConfigDialog", "isFirstInsertion", "setIsFirstInsertion", "loadBobine", "data", "getBobine", "error", "console", "loadStoricoUtilizzo", "getStoricoUtilizzo", "initialLoadDone", "useRef", "current", "log", "checkIfFirstInsertion", "handleOptionSelect", "<PERSON><PERSON><PERSON><PERSON>", "isFirstBobinaInsertion", "handleConfigConfirm", "config<PERSON><PERSON><PERSON>", "defaultFormData", "option", "handleCloseDialog", "handleBobinaSelect", "bobina", "handleFormChange", "e", "name", "value", "target", "idResult", "valid", "prev", "message", "newErrors", "result", "warning", "newWarnings", "handleSave", "validation", "<PERSON><PERSON><PERSON><PERSON>", "errors", "warnings", "bobina<PERSON><PERSON>", "parseFloat", "createBobina", "bobina<PERSON>d", "id_bobina", "updateBobina", "deleteBobina", "detail", "renderBobineCards", "length", "severity", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "container", "spacing", "map", "item", "xs", "sm", "md", "variant", "component", "color", "size", "startIcon", "onClick", "renderDialog", "open", "onClose", "max<PERSON><PERSON><PERSON>", "fullWidth", "Object", "keys", "sx", "mb", "mt", "style", "margin", "paddingLeft", "values", "index", "label", "onChange", "disabled", "required", "helperText", "type", "id", "labelId", "placeholder", "button", "primary", "secondary", "record", "cavi", "p", "gutterBottom", "display", "justifyContent", "my", "minHeight", "alignItems", "textAlign", "onConfirm", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/PycharmProjects/CMS/webapp/frontend/src/components/cavi/ParcoCavi.js"], "sourcesContent": ["import React, { useState, useEffect } from 'react';\nimport {\n  Box,\n  Typography,\n  Button,\n  Paper,\n  Grid,\n  Card,\n  CardContent,\n  CardActions,\n  Dialog,\n  DialogTitle,\n  DialogContent,\n  DialogActions,\n  TextField,\n  FormControl,\n  InputLabel,\n  Select,\n  MenuItem,\n  List,\n  ListItem,\n  ListItemText,\n  ListItemIcon,\n  ListItemButton,\n  Divider,\n  Alert,\n  CircularProgress,\n  Table,\n  TableBody,\n  TableCell,\n  TableContainer,\n  TableHead,\n  TableRow,\n  FormHelperText\n} from '@mui/material';\nimport {\n  Add as AddIcon,\n  Edit as EditIcon,\n  Delete as DeleteIcon,\n  History as HistoryIcon,\n  Save as SaveIcon,\n  ViewList as ViewListIcon,\n  Warning as WarningIcon\n} from '@mui/icons-material';\nimport parcoCaviService from '../../services/parcoCaviService';\nimport ConfigurazioneDialog from './ConfigurazioneDialog';\nimport { validateBobinaData, validateBobinaField, validateBobinaId, isEmpty } from '../../utils/bobinaValidationUtils';\n\nconst ParcoCavi = ({ cantiereId, onSuccess, onError, initialOption = null }) => {\n  const [loading, setLoading] = useState(false);\n  const [bobine, setBobine] = useState([]);\n  const [selectedOption, setSelectedOption] = useState(initialOption);\n  const [openDialog, setOpenDialog] = useState(false);\n  const [dialogType, setDialogType] = useState('');\n  const [selectedBobina, setSelectedBobina] = useState(null);\n  const [formData, setFormData] = useState({\n    numero_bobina: '',\n    utility: '',\n    tipologia: '',\n    n_conduttori: '',\n    sezione: '',\n    metri_totali: '',\n    metri_residui: '',\n    stato_bobina: 'Disponibile',\n    ubicazione_bobina: '',\n    fornitore: '',\n    n_DDT: '',\n    data_DDT: '',\n    configurazione: ''\n  });\n  const [formErrors, setFormErrors] = useState({});\n  const [formWarnings, setFormWarnings] = useState({});\n  const [storicoUtilizzo, setStoricoUtilizzo] = useState([]);\n  const [openConfigDialog, setOpenConfigDialog] = useState(false);\n  const [isFirstInsertion, setIsFirstInsertion] = useState(false);\n\n  // Carica le bobine disponibili\n  const loadBobine = async () => {\n    try {\n      setLoading(true);\n      const data = await parcoCaviService.getBobine(cantiereId);\n      setBobine(data);\n    } catch (error) {\n      onError('Errore nel caricamento delle bobine');\n      console.error('Errore nel caricamento delle bobine:', error);\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  // Carica lo storico utilizzo bobine\n  const loadStoricoUtilizzo = async () => {\n    try {\n      setLoading(true);\n      const data = await parcoCaviService.getStoricoUtilizzo(cantiereId);\n      setStoricoUtilizzo(data);\n    } catch (error) {\n      onError('Errore nel caricamento dello storico utilizzo');\n      console.error('Errore nel caricamento dello storico utilizzo:', error);\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  // Carica i dati all'avvio del componente e gestisce l'opzione iniziale\n  // Utilizziamo una ref per tenere traccia se l'effetto è già stato eseguito\n  const initialLoadDone = React.useRef(false);\n\n  useEffect(() => {\n    // Esegui solo una volta all'avvio del componente\n    if (!initialLoadDone.current) {\n      console.log('Primo caricamento del componente, initialOption:', initialOption);\n      initialLoadDone.current = true;\n\n      if (initialOption === 'creaBobina') {\n        console.log('Avvio processo creazione bobina');\n        checkIfFirstInsertion();\n      } else if (initialOption) {\n        console.log('Eseguendo handleOptionSelect con:', initialOption);\n        handleOptionSelect(initialOption);\n      } else {\n        console.log('Caricando bobine');\n        loadBobine();\n      }\n    }\n  }, []);  // Dipendenze vuote per eseguire solo al mount\n\n  // Verifica se è il primo inserimento di una bobina per un cantiere\n  const checkIfFirstInsertion = async () => {\n    try {\n      setLoading(true);\n      const isFirst = await parcoCaviService.isFirstBobinaInsertion(cantiereId);\n      setIsFirstInsertion(isFirst);\n      console.log('È il primo inserimento di una bobina?', isFirst);\n\n      if (isFirst) {\n        // Se è il primo inserimento, mostra il dialog di configurazione\n        console.log('Mostrando il dialog di configurazione');\n        setOpenConfigDialog(true);\n      } else {\n        // Non è il primo inserimento, procedi con il form normale\n        console.log('Non è il primo inserimento, mostrando il form normale');\n        setDialogType('creaBobina');\n        setFormData({\n          numero_bobina: '',\n          utility: '',\n          tipologia: '',\n          n_conduttori: '',\n          sezione: '',\n          metri_totali: '',\n          metri_residui: '',\n          stato_bobina: 'Disponibile',\n          ubicazione_bobina: '',\n          fornitore: '',\n          n_DDT: '',\n          data_DDT: '',\n          configurazione: ''\n        });\n        setOpenDialog(true);\n      }\n    } catch (error) {\n      onError('Errore nel controllo dell\\'inserimento della prima bobina');\n      console.error('Errore nel controllo dell\\'inserimento della prima bobina:', error);\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  // Gestisce la conferma della configurazione\n  const handleConfigConfirm = (configValue) => {\n    console.log('Configurazione selezionata:', configValue);\n    // Chiudi il dialog di configurazione\n    setOpenConfigDialog(false);\n\n    // Imposta i valori di default per la bobina\n    const defaultFormData = {\n      numero_bobina: configValue === 's' ? '1' : '', // Se usiamo numeri progressivi, imposta il default a 1\n      utility: '',\n      tipologia: '',\n      n_conduttori: '',\n      sezione: '',\n      metri_totali: '',\n      metri_residui: '',\n      stato_bobina: 'Disponibile',\n      ubicazione_bobina: '',\n      fornitore: '',\n      n_DDT: '',\n      data_DDT: '',\n      configurazione: configValue // Imposta la configurazione scelta\n    };\n\n    console.log('Impostando i dati del form con configurazione:', configValue);\n    setFormData(defaultFormData);\n    setDialogType('creaBobina');\n    setOpenDialog(true);\n    console.log('Dialog di creazione bobina aperto');\n  };\n\n  // Gestisce la selezione di un'opzione dal menu\n  const handleOptionSelect = (option) => {\n    setSelectedOption(option);\n\n    if (option === 'visualizzaBobine') {\n      loadBobine();\n    } else if (option === 'creaBobina') {\n      checkIfFirstInsertion();\n    } else if (option === 'modificaBobina') {\n      loadBobine();\n      setDialogType('selezionaBobina');\n      setOpenDialog(true);\n    } else if (option === 'eliminaBobina') {\n      loadBobine();\n      setDialogType('eliminaBobina');\n      setOpenDialog(true);\n    } else if (option === 'visualizzaStorico') {\n      loadStoricoUtilizzo();\n      setDialogType('visualizzaStorico');\n      setOpenDialog(true);\n    }\n  };\n\n  // Gestisce la chiusura del dialog\n  const handleCloseDialog = () => {\n    setOpenDialog(false);\n    setSelectedBobina(null);\n    setFormData({\n      numero_bobina: '',\n      utility: '',\n      tipologia: '',\n      n_conduttori: '',\n      sezione: '',\n      metri_totali: '',\n      metri_residui: '',\n      stato_bobina: 'Disponibile',\n      ubicazione_bobina: '',\n      fornitore: '',\n      n_DDT: '',\n      data_DDT: '',\n      configurazione: ''\n    });\n    setFormErrors({});\n    setFormWarnings({});\n  };\n\n  // Gestisce la selezione di una bobina\n  const handleBobinaSelect = (bobina) => {\n    setSelectedBobina(bobina);\n    if (dialogType === 'selezionaBobina') {\n      setDialogType('modificaBobina');\n      setFormData({\n        numero_bobina: bobina.numero_bobina,\n        utility: bobina.utility || '',\n        tipologia: bobina.tipologia || '',\n        n_conduttori: bobina.n_conduttori || '',\n        sezione: bobina.sezione || '',\n        metri_totali: bobina.metri_totali || '',\n        metri_residui: bobina.metri_residui || '',\n        stato_bobina: bobina.stato_bobina || 'DISPONIBILE',\n        ubicazione_bobina: bobina.ubicazione_bobina || '',\n        fornitore: bobina.fornitore || '',\n        n_DDT: bobina.n_DDT || '',\n        data_DDT: bobina.data_DDT || '',\n        configurazione: bobina.configurazione || ''\n      });\n    }\n  };\n\n  // Gestisce il cambio dei valori nel form con validazione\n  const handleFormChange = (e) => {\n    const { name, value } = e.target;\n\n    // Aggiorna il valore nel form\n    setFormData({\n      ...formData,\n      [name]: value\n    });\n\n    // Valida il campo\n    if (dialogType === 'creaBobina' || dialogType === 'modificaBobina') {\n      // Validazione speciale per numero_bobina quando configurazione è 'n'\n      if (name === 'numero_bobina' && formData.configurazione === 'n') {\n        const idResult = validateBobinaId(value);\n        if (!idResult.valid) {\n          setFormErrors(prev => ({\n            ...prev,\n            [name]: idResult.message\n          }));\n        } else {\n          setFormErrors(prev => {\n            const newErrors = { ...prev };\n            delete newErrors[name];\n            return newErrors;\n          });\n        }\n        return;\n      }\n\n      const result = validateBobinaField(name, value);\n\n      // Aggiorna gli errori\n      if (!result.valid) {\n        setFormErrors(prev => ({\n          ...prev,\n          [name]: result.message\n        }));\n      } else {\n        setFormErrors(prev => {\n          const newErrors = { ...prev };\n          delete newErrors[name];\n          return newErrors;\n        });\n\n        // Aggiorna gli avvisi\n        if (result.warning) {\n          setFormWarnings(prev => ({\n            ...prev,\n            [name]: result.message\n          }));\n        } else {\n          setFormWarnings(prev => {\n            const newWarnings = { ...prev };\n            delete newWarnings[name];\n            return newWarnings;\n          });\n        }\n      }\n    }\n  };\n\n  // Gestisce il salvataggio del form\n  const handleSave = async () => {\n    try {\n      // Validazione completa dei dati prima del salvataggio\n      if (dialogType === 'creaBobina' || dialogType === 'modificaBobina') {\n        const validation = validateBobinaData(formData);\n\n        if (!validation.isValid) {\n          setFormErrors(validation.errors);\n          setFormWarnings(validation.warnings);\n          onError('Correggi gli errori nel form prima di salvare');\n          return;\n        }\n      }\n\n      setLoading(true);\n\n      if (dialogType === 'creaBobina') {\n        // Prepara i dati per la creazione della bobina\n        const bobinaData = {\n          ...formData,\n          // Converti i campi numerici da stringa a numero\n          metri_totali: parseFloat(formData.metri_totali),\n          // Assicurati che la configurazione sia impostata correttamente\n          configurazione: formData.configurazione || 's'\n        };\n\n        // Invia i dati al backend\n        await parcoCaviService.createBobina(cantiereId, bobinaData);\n        onSuccess('Bobina creata con successo');\n\n        // Ricarica le bobine e chiudi il dialog\n        loadBobine();\n        handleCloseDialog();\n      } else if (dialogType === 'modificaBobina') {\n        // Per la modifica, usa l'ID bobina completo o il numero bobina\n        const bobinaId = selectedBobina.id_bobina || formData.numero_bobina;\n        await parcoCaviService.updateBobina(cantiereId, bobinaId, formData);\n        onSuccess('Bobina modificata con successo');\n      } else if (dialogType === 'eliminaBobina') {\n        // Per l'eliminazione, usa l'ID bobina completo\n        const bobinaId = selectedBobina.id_bobina || selectedBobina.numero_bobina;\n        await parcoCaviService.deleteBobina(cantiereId, bobinaId);\n        onSuccess('Bobina eliminata con successo');\n      }\n\n      handleCloseDialog();\n      loadBobine(); // Ricarica le bobine dopo l'operazione\n    } catch (error) {\n      onError('Errore durante l\\'operazione: ' + (error.detail || error.message || 'Errore sconosciuto'));\n      console.error('Errore durante l\\'operazione:', error);\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  // Renderizza le bobine in formato card\n  const renderBobineCards = () => {\n    if (bobine.length === 0) {\n      return (\n        <Alert severity=\"info\">Nessuna bobina disponibile</Alert>\n      );\n    }\n\n    return (\n      <Grid container spacing={2}>\n        {bobine.map((bobina) => (\n          <Grid item xs={12} sm={6} md={4} key={bobina.numero_bobina}>\n            <Card>\n              <CardContent>\n                <Typography variant=\"h6\" component=\"div\">\n                  Bobina: {bobina.numero_bobina}\n                </Typography>\n                <Typography variant=\"body2\" color=\"text.secondary\">\n                  Utility: {bobina.utility || 'N/A'}\n                </Typography>\n                <Typography variant=\"body2\" color=\"text.secondary\">\n                  Tipologia: {bobina.tipologia || 'N/A'}\n                </Typography>\n                <Typography variant=\"body2\" color=\"text.secondary\">\n                  N° Conduttori: {bobina.n_conduttori || 'N/A'}\n                </Typography>\n                <Typography variant=\"body2\" color=\"text.secondary\">\n                  Sezione: {bobina.sezione || 'N/A'}\n                </Typography>\n                <Typography variant=\"body2\" color=\"text.secondary\">\n                  Metri totali: {bobina.metri_totali || 'N/A'} m\n                </Typography>\n                <Typography variant=\"body2\" color=\"text.secondary\">\n                  Metri residui: {bobina.metri_residui || 'N/A'} m\n                </Typography>\n                <Typography variant=\"body2\" color=\"text.secondary\">\n                  Stato: {bobina.stato_bobina || 'DISPONIBILE'}\n                </Typography>\n                <Typography variant=\"body2\" color=\"text.secondary\">\n                  Ubicazione: {bobina.ubicazione_bobina || 'N/A'}\n                </Typography>\n                <Typography variant=\"body2\" color=\"text.secondary\">\n                  Fornitore: {bobina.fornitore || 'N/A'}\n                </Typography>\n              </CardContent>\n              <CardActions>\n                <Button\n                  size=\"small\"\n                  startIcon={<EditIcon />}\n                  onClick={() => {\n                    setDialogType('selezionaBobina');\n                    handleBobinaSelect(bobina);\n                  }}\n                >\n                  Modifica\n                </Button>\n                <Button\n                  size=\"small\"\n                  color=\"error\"\n                  startIcon={<DeleteIcon />}\n                  onClick={() => {\n                    setDialogType('eliminaBobina');\n                    setSelectedBobina(bobina);\n                    setOpenDialog(true);\n                  }}\n                >\n                  Elimina\n                </Button>\n              </CardActions>\n            </Card>\n          </Grid>\n        ))}\n      </Grid>\n    );\n  };\n\n  // Renderizza il dialog in base al tipo\n  const renderDialog = () => {\n    if (dialogType === 'creaBobina' || dialogType === 'modificaBobina') {\n      return (\n        <Dialog open={openDialog} onClose={handleCloseDialog} maxWidth=\"md\" fullWidth>\n          <DialogTitle>\n            {dialogType === 'creaBobina' ? 'Crea Nuova Bobina' : 'Modifica Bobina'}\n          </DialogTitle>\n          <DialogContent>\n            {Object.keys(formWarnings).length > 0 && (\n              <Alert severity=\"warning\" sx={{ mb: 2, mt: 1 }}>\n                <Typography variant=\"subtitle2\">Attenzione:</Typography>\n                <ul style={{ margin: 0, paddingLeft: '20px' }}>\n                  {Object.values(formWarnings).map((warning, index) => (\n                    <li key={index}>{warning}</li>\n                  ))}\n                </ul>\n              </Alert>\n            )}\n\n            <Grid container spacing={2} sx={{ mt: 1 }}>\n              <Grid item xs={12} sm={6}>\n                <TextField\n                  name=\"numero_bobina\"\n                  label={formData.configurazione === 's' ? \"Numero Bobina\" : \"ID Bobina\"}\n                  fullWidth\n                  variant=\"outlined\"\n                  value={formData.numero_bobina}\n                  onChange={handleFormChange}\n                  disabled={dialogType === 'modificaBobina' || (dialogType === 'creaBobina' && formData.configurazione === 's')}\n                  required\n                  error={!!formErrors.numero_bobina}\n                  helperText={\n                    formErrors.numero_bobina ||\n                    (dialogType === 'creaBobina' && formData.configurazione === 's'\n                      ? 'Numero generato automaticamente'\n                      : dialogType === 'creaBobina' && formData.configurazione === 'n'\n                        ? 'Inserisci l\\'ID della bobina (es. A123)'\n                        : '')\n                  }\n                />\n              </Grid>\n              <Grid item xs={12} sm={6}>\n                <TextField\n                  name=\"utility\"\n                  label=\"Utility\"\n                  fullWidth\n                  variant=\"outlined\"\n                  value={formData.utility}\n                  onChange={handleFormChange}\n                  required\n                  error={!!formErrors.utility}\n                  helperText={formErrors.utility || ''}\n                />\n              </Grid>\n              <Grid item xs={12} sm={6}>\n                <TextField\n                  name=\"tipologia\"\n                  label=\"Tipologia\"\n                  fullWidth\n                  variant=\"outlined\"\n                  value={formData.tipologia}\n                  onChange={handleFormChange}\n                  required\n                  error={!!formErrors.tipologia}\n                  helperText={formErrors.tipologia || ''}\n                />\n              </Grid>\n              <Grid item xs={12} sm={6}>\n                <TextField\n                  name=\"n_conduttori\"\n                  label=\"N° Conduttori\"\n                  fullWidth\n                  variant=\"outlined\"\n                  value={formData.n_conduttori}\n                  onChange={handleFormChange}\n                  required\n                  error={!!formErrors.n_conduttori}\n                  helperText={formErrors.n_conduttori || ''}\n                />\n              </Grid>\n              <Grid item xs={12} sm={6}>\n                <TextField\n                  name=\"sezione\"\n                  label=\"Sezione\"\n                  fullWidth\n                  variant=\"outlined\"\n                  value={formData.sezione}\n                  onChange={handleFormChange}\n                  required\n                  error={!!formErrors.sezione}\n                  helperText={formErrors.sezione || ''}\n                />\n              </Grid>\n              <Grid item xs={12} sm={6}>\n                <TextField\n                  name=\"metri_totali\"\n                  label=\"Metri Totali\"\n                  type=\"number\"\n                  fullWidth\n                  variant=\"outlined\"\n                  value={formData.metri_totali}\n                  onChange={handleFormChange}\n                  required\n                  error={!!formErrors.metri_totali}\n                  helperText={formErrors.metri_totali || ''}\n                />\n              </Grid>\n              {dialogType === 'modificaBobina' && (\n                <Grid item xs={12} sm={6}>\n                  <TextField\n                    name=\"metri_residui\"\n                    label=\"Metri Residui\"\n                    type=\"number\"\n                    fullWidth\n                    variant=\"outlined\"\n                    value={formData.metri_residui}\n                    onChange={handleFormChange}\n                    required\n                    disabled={true}\n                    helperText=\"I metri residui non possono essere modificati direttamente\"\n                  />\n                </Grid>\n              )}\n              <Grid item xs={12} sm={6}>\n                <FormControl fullWidth>\n                  <InputLabel id=\"stato-bobina-label\">Stato Bobina</InputLabel>\n                  <Select\n                    labelId=\"stato-bobina-label\"\n                    name=\"stato_bobina\"\n                    value={formData.stato_bobina}\n                    label=\"Stato Bobina\"\n                    onChange={handleFormChange}\n                    disabled={dialogType === 'creaBobina'}\n                  >\n                    <MenuItem value=\"Disponibile\">Disponibile</MenuItem>\n                    <MenuItem value=\"In uso\">In uso</MenuItem>\n                    <MenuItem value=\"Terminata\">Terminata</MenuItem>\n                    <MenuItem value=\"Danneggiata\">Danneggiata</MenuItem>\n                    <MenuItem value=\"Over\">Over</MenuItem>\n                  </Select>\n                  {dialogType === 'creaBobina' && (\n                    <FormHelperText>Per una nuova bobina, lo stato è sempre \"Disponibile\"</FormHelperText>\n                  )}\n                </FormControl>\n              </Grid>\n              <Grid item xs={12} sm={6}>\n                <TextField\n                  name=\"ubicazione_bobina\"\n                  label=\"Ubicazione Bobina\"\n                  fullWidth\n                  variant=\"outlined\"\n                  value={formData.ubicazione_bobina}\n                  onChange={handleFormChange}\n                  error={!!formErrors.ubicazione_bobina}\n                  helperText={formErrors.ubicazione_bobina || ''}\n                />\n              </Grid>\n              <Grid item xs={12} sm={6}>\n                <TextField\n                  name=\"fornitore\"\n                  label=\"Fornitore\"\n                  fullWidth\n                  variant=\"outlined\"\n                  value={formData.fornitore}\n                  onChange={handleFormChange}\n                  error={!!formErrors.fornitore}\n                  helperText={formErrors.fornitore || ''}\n                />\n              </Grid>\n              <Grid item xs={12} sm={6}>\n                <TextField\n                  name=\"n_DDT\"\n                  label=\"Numero DDT\"\n                  fullWidth\n                  variant=\"outlined\"\n                  value={formData.n_DDT}\n                  onChange={handleFormChange}\n                  error={!!formErrors.n_DDT}\n                  helperText={formErrors.n_DDT || ''}\n                />\n              </Grid>\n              <Grid item xs={12} sm={6}>\n                <TextField\n                  name=\"data_DDT\"\n                  label=\"Data DDT (YYYY-MM-DD)\"\n                  fullWidth\n                  variant=\"outlined\"\n                  value={formData.data_DDT}\n                  onChange={handleFormChange}\n                  placeholder=\"YYYY-MM-DD\"\n                  error={!!formErrors.data_DDT}\n                  helperText={formErrors.data_DDT || ''}\n                />\n              </Grid>\n              <Grid item xs={12} sm={6}>\n                <TextField\n                  name=\"configurazione\"\n                  label=\"Configurazione\"\n                  fullWidth\n                  variant=\"outlined\"\n                  value={formData.configurazione}\n                  onChange={handleFormChange}\n                  disabled={true}\n                  helperText={formData.configurazione === 's' ? 'Usa numeri progressivi' : 'Inserimento manuale ID bobina'}\n                />\n              </Grid>\n            </Grid>\n          </DialogContent>\n          <DialogActions>\n            <Button onClick={handleCloseDialog}>Annulla</Button>\n            <Button\n              onClick={handleSave}\n              disabled={loading || Object.keys(formErrors).length > 0}\n              startIcon={loading ? <CircularProgress size={20} /> : <SaveIcon />}\n              variant=\"contained\"\n              color=\"primary\"\n            >\n              Salva\n            </Button>\n          </DialogActions>\n        </Dialog>\n      );\n    } else if (dialogType === 'selezionaBobina') {\n      return (\n        <Dialog open={openDialog} onClose={handleCloseDialog} maxWidth=\"md\" fullWidth>\n          <DialogTitle>Seleziona Bobina da Modificare</DialogTitle>\n          <DialogContent>\n            {loading ? (\n              <CircularProgress />\n            ) : bobine.length === 0 ? (\n              <Alert severity=\"info\">Nessuna bobina disponibile</Alert>\n            ) : (\n              <List>\n                {bobine.map((bobina) => (\n                  <ListItem\n                    button\n                    key={bobina.numero_bobina}\n                    onClick={() => handleBobinaSelect(bobina)}\n                  >\n                    <ListItemText\n                      primary={`Bobina: ${bobina.numero_bobina}`}\n                      secondary={`Tipologia: ${bobina.tipologia || 'N/A'} - Utility: ${bobina.utility || 'N/A'} - Residuo: ${bobina.metri_residui || 'N/A'} m`}\n                    />\n                  </ListItem>\n                ))}\n              </List>\n            )}\n          </DialogContent>\n          <DialogActions>\n            <Button onClick={handleCloseDialog}>Annulla</Button>\n          </DialogActions>\n        </Dialog>\n      );\n    } else if (dialogType === 'eliminaBobina') {\n      return (\n        <Dialog open={openDialog} onClose={handleCloseDialog} maxWidth=\"sm\" fullWidth>\n          <DialogTitle>Elimina Bobina</DialogTitle>\n          <DialogContent>\n            {!selectedBobina ? (\n              loading ? (\n                <CircularProgress />\n              ) : bobine.length === 0 ? (\n                <Alert severity=\"info\">Nessuna bobina disponibile</Alert>\n              ) : (\n                <List>\n                  {bobine.map((bobina) => (\n                    <ListItem\n                      button\n                      key={bobina.numero_bobina}\n                      onClick={() => setSelectedBobina(bobina)}\n                    >\n                      <ListItemText\n                        primary={`Bobina: ${bobina.numero_bobina}`}\n                        secondary={`Tipologia: ${bobina.tipologia || 'N/A'} - Utility: ${bobina.utility || 'N/A'} - Residuo: ${bobina.metri_residui || 'N/A'} m`}\n                      />\n                    </ListItem>\n                  ))}\n                </List>\n              )\n            ) : (\n              <Box>\n                <Alert severity=\"warning\" sx={{ mb: 2 }}>\n                  Sei sicuro di voler eliminare la bobina {selectedBobina.numero_bobina}?\n                </Alert>\n                <Typography variant=\"body1\">\n                  Questa operazione non può essere annullata.\n                </Typography>\n              </Box>\n            )}\n          </DialogContent>\n          <DialogActions>\n            <Button onClick={handleCloseDialog}>Annulla</Button>\n            {selectedBobina && (\n              <Button\n                onClick={handleSave}\n                disabled={loading}\n                color=\"error\"\n                startIcon={loading ? <CircularProgress size={20} /> : <DeleteIcon />}\n              >\n                Elimina\n              </Button>\n            )}\n          </DialogActions>\n        </Dialog>\n      );\n    } else if (dialogType === 'visualizzaStorico') {\n      return (\n        <Dialog open={openDialog} onClose={handleCloseDialog} maxWidth=\"lg\" fullWidth>\n          <DialogTitle>Storico Utilizzo Bobine</DialogTitle>\n          <DialogContent>\n            {loading ? (\n              <CircularProgress />\n            ) : storicoUtilizzo.length === 0 ? (\n              <Alert severity=\"info\">Nessun dato storico disponibile</Alert>\n            ) : (\n              <TableContainer component={Paper} sx={{ mt: 2 }}>\n                <Table size=\"small\">\n                  <TableHead>\n                    <TableRow>\n                      <TableCell>Bobina</TableCell>\n                      <TableCell>Utility</TableCell>\n                      <TableCell>Tipologia</TableCell>\n                      <TableCell>N° Conduttori</TableCell>\n                      <TableCell>Sezione</TableCell>\n                      <TableCell>Metri Totali</TableCell>\n                      <TableCell>Metri Residui</TableCell>\n                      <TableCell>Cavi Associati</TableCell>\n                    </TableRow>\n                  </TableHead>\n                  <TableBody>\n                    {storicoUtilizzo.map((record, index) => (\n                      <TableRow key={index}>\n                        <TableCell>{record.numero_bobina}</TableCell>\n                        <TableCell>{record.utility}</TableCell>\n                        <TableCell>{record.tipologia}</TableCell>\n                        <TableCell>{record.n_conduttori}</TableCell>\n                        <TableCell>{record.sezione}</TableCell>\n                        <TableCell>{record.metri_totali}</TableCell>\n                        <TableCell>{record.metri_residui}</TableCell>\n                        <TableCell>{record.cavi.length}</TableCell>\n                      </TableRow>\n                    ))}\n                  </TableBody>\n                </Table>\n              </TableContainer>\n            )}\n          </DialogContent>\n          <DialogActions>\n            <Button onClick={handleCloseDialog}>Chiudi</Button>\n          </DialogActions>\n        </Dialog>\n      );\n    }\n\n    return null;\n  };\n\n  return (\n    <Box>\n      {selectedOption === 'visualizzaBobine' && !openDialog ? (\n        <Paper sx={{ p: 3 }}>\n          <Typography variant=\"h6\" gutterBottom>\n            Bobine Disponibili\n          </Typography>\n          {loading ? (\n            <Box sx={{ display: 'flex', justifyContent: 'center', my: 4 }}>\n              <CircularProgress />\n            </Box>\n          ) : (\n            renderBobineCards()\n          )}\n        </Paper>\n      ) : !openDialog ? (\n        <Paper sx={{ p: 3, minHeight: '300px', display: 'flex', alignItems: 'center', justifyContent: 'center' }}>\n          {!selectedOption ? (\n            <Typography variant=\"body1\">\n              Seleziona un'opzione dal menu principale per iniziare.\n            </Typography>\n          ) : (\n            <Box sx={{ textAlign: 'center' }}>\n              <Typography variant=\"h6\" gutterBottom>\n                {selectedOption === 'creaBobina' && 'Crea Nuova Bobina'}\n                {selectedOption === 'modificaBobina' && 'Modifica Bobina'}\n                {selectedOption === 'eliminaBobina' && 'Elimina Bobina'}\n                {selectedOption === 'visualizzaStorico' && 'Visualizza Storico Utilizzo'}\n              </Typography>\n              <CircularProgress sx={{ mt: 2 }} />\n            </Box>\n          )}\n        </Paper>\n      ) : null}\n\n      {renderDialog()}\n\n      {/* Dialog di configurazione per il primo inserimento */}\n      <ConfigurazioneDialog\n        open={openConfigDialog}\n        onClose={() => setOpenConfigDialog(false)}\n        onConfirm={handleConfigConfirm}\n      />\n    </Box>\n  );\n};\n\nexport default ParcoCavi;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAClD,SACEC,GAAG,EACHC,UAAU,EACVC,MAAM,EACNC,KAAK,EACLC,IAAI,EACJC,IAAI,EACJC,WAAW,EACXC,WAAW,EACXC,MAAM,EACNC,WAAW,EACXC,aAAa,EACbC,aAAa,EACbC,SAAS,EACTC,WAAW,EACXC,UAAU,EACVC,MAAM,EACNC,QAAQ,EACRC,IAAI,EACJC,QAAQ,EACRC,YAAY,EACZC,YAAY,EACZC,cAAc,EACdC,OAAO,EACPC,KAAK,EACLC,gBAAgB,EAChBC,KAAK,EACLC,SAAS,EACTC,SAAS,EACTC,cAAc,EACdC,SAAS,EACTC,QAAQ,EACRC,cAAc,QACT,eAAe;AACtB,SACEC,GAAG,IAAIC,OAAO,EACdC,IAAI,IAAIC,QAAQ,EAChBC,MAAM,IAAIC,UAAU,EACpBC,OAAO,IAAIC,WAAW,EACtBC,IAAI,IAAIC,QAAQ,EAChBC,QAAQ,IAAIC,YAAY,EACxBC,OAAO,IAAIC,WAAW,QACjB,qBAAqB;AAC5B,OAAOC,gBAAgB,MAAM,iCAAiC;AAC9D,OAAOC,oBAAoB,MAAM,wBAAwB;AACzD,SAASC,kBAAkB,EAAEC,mBAAmB,EAAEC,gBAAgB,EAAEC,OAAO,QAAQ,mCAAmC;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAEvH,MAAMC,SAAS,GAAGA,CAAC;EAAEC,UAAU;EAAEC,SAAS;EAAEC,OAAO;EAAEC,aAAa,GAAG;AAAK,CAAC,KAAK;EAAAC,EAAA;EAC9E,MAAM,CAACC,OAAO,EAAEC,UAAU,CAAC,GAAG/D,QAAQ,CAAC,KAAK,CAAC;EAC7C,MAAM,CAACgE,MAAM,EAAEC,SAAS,CAAC,GAAGjE,QAAQ,CAAC,EAAE,CAAC;EACxC,MAAM,CAACkE,cAAc,EAAEC,iBAAiB,CAAC,GAAGnE,QAAQ,CAAC4D,aAAa,CAAC;EACnE,MAAM,CAACQ,UAAU,EAAEC,aAAa,CAAC,GAAGrE,QAAQ,CAAC,KAAK,CAAC;EACnD,MAAM,CAACsE,UAAU,EAAEC,aAAa,CAAC,GAAGvE,QAAQ,CAAC,EAAE,CAAC;EAChD,MAAM,CAACwE,cAAc,EAAEC,iBAAiB,CAAC,GAAGzE,QAAQ,CAAC,IAAI,CAAC;EAC1D,MAAM,CAAC0E,QAAQ,EAAEC,WAAW,CAAC,GAAG3E,QAAQ,CAAC;IACvC4E,aAAa,EAAE,EAAE;IACjBC,OAAO,EAAE,EAAE;IACXC,SAAS,EAAE,EAAE;IACbC,YAAY,EAAE,EAAE;IAChBC,OAAO,EAAE,EAAE;IACXC,YAAY,EAAE,EAAE;IAChBC,aAAa,EAAE,EAAE;IACjBC,YAAY,EAAE,aAAa;IAC3BC,iBAAiB,EAAE,EAAE;IACrBC,SAAS,EAAE,EAAE;IACbC,KAAK,EAAE,EAAE;IACTC,QAAQ,EAAE,EAAE;IACZC,cAAc,EAAE;EAClB,CAAC,CAAC;EACF,MAAM,CAACC,UAAU,EAAEC,aAAa,CAAC,GAAG1F,QAAQ,CAAC,CAAC,CAAC,CAAC;EAChD,MAAM,CAAC2F,YAAY,EAAEC,eAAe,CAAC,GAAG5F,QAAQ,CAAC,CAAC,CAAC,CAAC;EACpD,MAAM,CAAC6F,eAAe,EAAEC,kBAAkB,CAAC,GAAG9F,QAAQ,CAAC,EAAE,CAAC;EAC1D,MAAM,CAAC+F,gBAAgB,EAAEC,mBAAmB,CAAC,GAAGhG,QAAQ,CAAC,KAAK,CAAC;EAC/D,MAAM,CAACiG,gBAAgB,EAAEC,mBAAmB,CAAC,GAAGlG,QAAQ,CAAC,KAAK,CAAC;;EAE/D;EACA,MAAMmG,UAAU,GAAG,MAAAA,CAAA,KAAY;IAC7B,IAAI;MACFpC,UAAU,CAAC,IAAI,CAAC;MAChB,MAAMqC,IAAI,GAAG,MAAMpD,gBAAgB,CAACqD,SAAS,CAAC5C,UAAU,CAAC;MACzDQ,SAAS,CAACmC,IAAI,CAAC;IACjB,CAAC,CAAC,OAAOE,KAAK,EAAE;MACd3C,OAAO,CAAC,qCAAqC,CAAC;MAC9C4C,OAAO,CAACD,KAAK,CAAC,sCAAsC,EAAEA,KAAK,CAAC;IAC9D,CAAC,SAAS;MACRvC,UAAU,CAAC,KAAK,CAAC;IACnB;EACF,CAAC;;EAED;EACA,MAAMyC,mBAAmB,GAAG,MAAAA,CAAA,KAAY;IACtC,IAAI;MACFzC,UAAU,CAAC,IAAI,CAAC;MAChB,MAAMqC,IAAI,GAAG,MAAMpD,gBAAgB,CAACyD,kBAAkB,CAAChD,UAAU,CAAC;MAClEqC,kBAAkB,CAACM,IAAI,CAAC;IAC1B,CAAC,CAAC,OAAOE,KAAK,EAAE;MACd3C,OAAO,CAAC,+CAA+C,CAAC;MACxD4C,OAAO,CAACD,KAAK,CAAC,gDAAgD,EAAEA,KAAK,CAAC;IACxE,CAAC,SAAS;MACRvC,UAAU,CAAC,KAAK,CAAC;IACnB;EACF,CAAC;;EAED;EACA;EACA,MAAM2C,eAAe,GAAG3G,KAAK,CAAC4G,MAAM,CAAC,KAAK,CAAC;EAE3C1G,SAAS,CAAC,MAAM;IACd;IACA,IAAI,CAACyG,eAAe,CAACE,OAAO,EAAE;MAC5BL,OAAO,CAACM,GAAG,CAAC,kDAAkD,EAAEjD,aAAa,CAAC;MAC9E8C,eAAe,CAACE,OAAO,GAAG,IAAI;MAE9B,IAAIhD,aAAa,KAAK,YAAY,EAAE;QAClC2C,OAAO,CAACM,GAAG,CAAC,iCAAiC,CAAC;QAC9CC,qBAAqB,CAAC,CAAC;MACzB,CAAC,MAAM,IAAIlD,aAAa,EAAE;QACxB2C,OAAO,CAACM,GAAG,CAAC,mCAAmC,EAAEjD,aAAa,CAAC;QAC/DmD,kBAAkB,CAACnD,aAAa,CAAC;MACnC,CAAC,MAAM;QACL2C,OAAO,CAACM,GAAG,CAAC,kBAAkB,CAAC;QAC/BV,UAAU,CAAC,CAAC;MACd;IACF;EACF,CAAC,EAAE,EAAE,CAAC,CAAC,CAAE;;EAET;EACA,MAAMW,qBAAqB,GAAG,MAAAA,CAAA,KAAY;IACxC,IAAI;MACF/C,UAAU,CAAC,IAAI,CAAC;MAChB,MAAMiD,OAAO,GAAG,MAAMhE,gBAAgB,CAACiE,sBAAsB,CAACxD,UAAU,CAAC;MACzEyC,mBAAmB,CAACc,OAAO,CAAC;MAC5BT,OAAO,CAACM,GAAG,CAAC,uCAAuC,EAAEG,OAAO,CAAC;MAE7D,IAAIA,OAAO,EAAE;QACX;QACAT,OAAO,CAACM,GAAG,CAAC,uCAAuC,CAAC;QACpDb,mBAAmB,CAAC,IAAI,CAAC;MAC3B,CAAC,MAAM;QACL;QACAO,OAAO,CAACM,GAAG,CAAC,uDAAuD,CAAC;QACpEtC,aAAa,CAAC,YAAY,CAAC;QAC3BI,WAAW,CAAC;UACVC,aAAa,EAAE,EAAE;UACjBC,OAAO,EAAE,EAAE;UACXC,SAAS,EAAE,EAAE;UACbC,YAAY,EAAE,EAAE;UAChBC,OAAO,EAAE,EAAE;UACXC,YAAY,EAAE,EAAE;UAChBC,aAAa,EAAE,EAAE;UACjBC,YAAY,EAAE,aAAa;UAC3BC,iBAAiB,EAAE,EAAE;UACrBC,SAAS,EAAE,EAAE;UACbC,KAAK,EAAE,EAAE;UACTC,QAAQ,EAAE,EAAE;UACZC,cAAc,EAAE;QAClB,CAAC,CAAC;QACFnB,aAAa,CAAC,IAAI,CAAC;MACrB;IACF,CAAC,CAAC,OAAOiC,KAAK,EAAE;MACd3C,OAAO,CAAC,2DAA2D,CAAC;MACpE4C,OAAO,CAACD,KAAK,CAAC,4DAA4D,EAAEA,KAAK,CAAC;IACpF,CAAC,SAAS;MACRvC,UAAU,CAAC,KAAK,CAAC;IACnB;EACF,CAAC;;EAED;EACA,MAAMmD,mBAAmB,GAAIC,WAAW,IAAK;IAC3CZ,OAAO,CAACM,GAAG,CAAC,6BAA6B,EAAEM,WAAW,CAAC;IACvD;IACAnB,mBAAmB,CAAC,KAAK,CAAC;;IAE1B;IACA,MAAMoB,eAAe,GAAG;MACtBxC,aAAa,EAAEuC,WAAW,KAAK,GAAG,GAAG,GAAG,GAAG,EAAE;MAAE;MAC/CtC,OAAO,EAAE,EAAE;MACXC,SAAS,EAAE,EAAE;MACbC,YAAY,EAAE,EAAE;MAChBC,OAAO,EAAE,EAAE;MACXC,YAAY,EAAE,EAAE;MAChBC,aAAa,EAAE,EAAE;MACjBC,YAAY,EAAE,aAAa;MAC3BC,iBAAiB,EAAE,EAAE;MACrBC,SAAS,EAAE,EAAE;MACbC,KAAK,EAAE,EAAE;MACTC,QAAQ,EAAE,EAAE;MACZC,cAAc,EAAE2B,WAAW,CAAC;IAC9B,CAAC;IAEDZ,OAAO,CAACM,GAAG,CAAC,gDAAgD,EAAEM,WAAW,CAAC;IAC1ExC,WAAW,CAACyC,eAAe,CAAC;IAC5B7C,aAAa,CAAC,YAAY,CAAC;IAC3BF,aAAa,CAAC,IAAI,CAAC;IACnBkC,OAAO,CAACM,GAAG,CAAC,mCAAmC,CAAC;EAClD,CAAC;;EAED;EACA,MAAME,kBAAkB,GAAIM,MAAM,IAAK;IACrClD,iBAAiB,CAACkD,MAAM,CAAC;IAEzB,IAAIA,MAAM,KAAK,kBAAkB,EAAE;MACjClB,UAAU,CAAC,CAAC;IACd,CAAC,MAAM,IAAIkB,MAAM,KAAK,YAAY,EAAE;MAClCP,qBAAqB,CAAC,CAAC;IACzB,CAAC,MAAM,IAAIO,MAAM,KAAK,gBAAgB,EAAE;MACtClB,UAAU,CAAC,CAAC;MACZ5B,aAAa,CAAC,iBAAiB,CAAC;MAChCF,aAAa,CAAC,IAAI,CAAC;IACrB,CAAC,MAAM,IAAIgD,MAAM,KAAK,eAAe,EAAE;MACrClB,UAAU,CAAC,CAAC;MACZ5B,aAAa,CAAC,eAAe,CAAC;MAC9BF,aAAa,CAAC,IAAI,CAAC;IACrB,CAAC,MAAM,IAAIgD,MAAM,KAAK,mBAAmB,EAAE;MACzCb,mBAAmB,CAAC,CAAC;MACrBjC,aAAa,CAAC,mBAAmB,CAAC;MAClCF,aAAa,CAAC,IAAI,CAAC;IACrB;EACF,CAAC;;EAED;EACA,MAAMiD,iBAAiB,GAAGA,CAAA,KAAM;IAC9BjD,aAAa,CAAC,KAAK,CAAC;IACpBI,iBAAiB,CAAC,IAAI,CAAC;IACvBE,WAAW,CAAC;MACVC,aAAa,EAAE,EAAE;MACjBC,OAAO,EAAE,EAAE;MACXC,SAAS,EAAE,EAAE;MACbC,YAAY,EAAE,EAAE;MAChBC,OAAO,EAAE,EAAE;MACXC,YAAY,EAAE,EAAE;MAChBC,aAAa,EAAE,EAAE;MACjBC,YAAY,EAAE,aAAa;MAC3BC,iBAAiB,EAAE,EAAE;MACrBC,SAAS,EAAE,EAAE;MACbC,KAAK,EAAE,EAAE;MACTC,QAAQ,EAAE,EAAE;MACZC,cAAc,EAAE;IAClB,CAAC,CAAC;IACFE,aAAa,CAAC,CAAC,CAAC,CAAC;IACjBE,eAAe,CAAC,CAAC,CAAC,CAAC;EACrB,CAAC;;EAED;EACA,MAAM2B,kBAAkB,GAAIC,MAAM,IAAK;IACrC/C,iBAAiB,CAAC+C,MAAM,CAAC;IACzB,IAAIlD,UAAU,KAAK,iBAAiB,EAAE;MACpCC,aAAa,CAAC,gBAAgB,CAAC;MAC/BI,WAAW,CAAC;QACVC,aAAa,EAAE4C,MAAM,CAAC5C,aAAa;QACnCC,OAAO,EAAE2C,MAAM,CAAC3C,OAAO,IAAI,EAAE;QAC7BC,SAAS,EAAE0C,MAAM,CAAC1C,SAAS,IAAI,EAAE;QACjCC,YAAY,EAAEyC,MAAM,CAACzC,YAAY,IAAI,EAAE;QACvCC,OAAO,EAAEwC,MAAM,CAACxC,OAAO,IAAI,EAAE;QAC7BC,YAAY,EAAEuC,MAAM,CAACvC,YAAY,IAAI,EAAE;QACvCC,aAAa,EAAEsC,MAAM,CAACtC,aAAa,IAAI,EAAE;QACzCC,YAAY,EAAEqC,MAAM,CAACrC,YAAY,IAAI,aAAa;QAClDC,iBAAiB,EAAEoC,MAAM,CAACpC,iBAAiB,IAAI,EAAE;QACjDC,SAAS,EAAEmC,MAAM,CAACnC,SAAS,IAAI,EAAE;QACjCC,KAAK,EAAEkC,MAAM,CAAClC,KAAK,IAAI,EAAE;QACzBC,QAAQ,EAAEiC,MAAM,CAACjC,QAAQ,IAAI,EAAE;QAC/BC,cAAc,EAAEgC,MAAM,CAAChC,cAAc,IAAI;MAC3C,CAAC,CAAC;IACJ;EACF,CAAC;;EAED;EACA,MAAMiC,gBAAgB,GAAIC,CAAC,IAAK;IAC9B,MAAM;MAAEC,IAAI;MAAEC;IAAM,CAAC,GAAGF,CAAC,CAACG,MAAM;;IAEhC;IACAlD,WAAW,CAAC;MACV,GAAGD,QAAQ;MACX,CAACiD,IAAI,GAAGC;IACV,CAAC,CAAC;;IAEF;IACA,IAAItD,UAAU,KAAK,YAAY,IAAIA,UAAU,KAAK,gBAAgB,EAAE;MAClE;MACA,IAAIqD,IAAI,KAAK,eAAe,IAAIjD,QAAQ,CAACc,cAAc,KAAK,GAAG,EAAE;QAC/D,MAAMsC,QAAQ,GAAG1E,gBAAgB,CAACwE,KAAK,CAAC;QACxC,IAAI,CAACE,QAAQ,CAACC,KAAK,EAAE;UACnBrC,aAAa,CAACsC,IAAI,KAAK;YACrB,GAAGA,IAAI;YACP,CAACL,IAAI,GAAGG,QAAQ,CAACG;UACnB,CAAC,CAAC,CAAC;QACL,CAAC,MAAM;UACLvC,aAAa,CAACsC,IAAI,IAAI;YACpB,MAAME,SAAS,GAAG;cAAE,GAAGF;YAAK,CAAC;YAC7B,OAAOE,SAAS,CAACP,IAAI,CAAC;YACtB,OAAOO,SAAS;UAClB,CAAC,CAAC;QACJ;QACA;MACF;MAEA,MAAMC,MAAM,GAAGhF,mBAAmB,CAACwE,IAAI,EAAEC,KAAK,CAAC;;MAE/C;MACA,IAAI,CAACO,MAAM,CAACJ,KAAK,EAAE;QACjBrC,aAAa,CAACsC,IAAI,KAAK;UACrB,GAAGA,IAAI;UACP,CAACL,IAAI,GAAGQ,MAAM,CAACF;QACjB,CAAC,CAAC,CAAC;MACL,CAAC,MAAM;QACLvC,aAAa,CAACsC,IAAI,IAAI;UACpB,MAAME,SAAS,GAAG;YAAE,GAAGF;UAAK,CAAC;UAC7B,OAAOE,SAAS,CAACP,IAAI,CAAC;UACtB,OAAOO,SAAS;QAClB,CAAC,CAAC;;QAEF;QACA,IAAIC,MAAM,CAACC,OAAO,EAAE;UAClBxC,eAAe,CAACoC,IAAI,KAAK;YACvB,GAAGA,IAAI;YACP,CAACL,IAAI,GAAGQ,MAAM,CAACF;UACjB,CAAC,CAAC,CAAC;QACL,CAAC,MAAM;UACLrC,eAAe,CAACoC,IAAI,IAAI;YACtB,MAAMK,WAAW,GAAG;cAAE,GAAGL;YAAK,CAAC;YAC/B,OAAOK,WAAW,CAACV,IAAI,CAAC;YACxB,OAAOU,WAAW;UACpB,CAAC,CAAC;QACJ;MACF;IACF;EACF,CAAC;;EAED;EACA,MAAMC,UAAU,GAAG,MAAAA,CAAA,KAAY;IAC7B,IAAI;MACF;MACA,IAAIhE,UAAU,KAAK,YAAY,IAAIA,UAAU,KAAK,gBAAgB,EAAE;QAClE,MAAMiE,UAAU,GAAGrF,kBAAkB,CAACwB,QAAQ,CAAC;QAE/C,IAAI,CAAC6D,UAAU,CAACC,OAAO,EAAE;UACvB9C,aAAa,CAAC6C,UAAU,CAACE,MAAM,CAAC;UAChC7C,eAAe,CAAC2C,UAAU,CAACG,QAAQ,CAAC;UACpC/E,OAAO,CAAC,+CAA+C,CAAC;UACxD;QACF;MACF;MAEAI,UAAU,CAAC,IAAI,CAAC;MAEhB,IAAIO,UAAU,KAAK,YAAY,EAAE;QAC/B;QACA,MAAMqE,UAAU,GAAG;UACjB,GAAGjE,QAAQ;UACX;UACAO,YAAY,EAAE2D,UAAU,CAAClE,QAAQ,CAACO,YAAY,CAAC;UAC/C;UACAO,cAAc,EAAEd,QAAQ,CAACc,cAAc,IAAI;QAC7C,CAAC;;QAED;QACA,MAAMxC,gBAAgB,CAAC6F,YAAY,CAACpF,UAAU,EAAEkF,UAAU,CAAC;QAC3DjF,SAAS,CAAC,4BAA4B,CAAC;;QAEvC;QACAyC,UAAU,CAAC,CAAC;QACZmB,iBAAiB,CAAC,CAAC;MACrB,CAAC,MAAM,IAAIhD,UAAU,KAAK,gBAAgB,EAAE;QAC1C;QACA,MAAMwE,QAAQ,GAAGtE,cAAc,CAACuE,SAAS,IAAIrE,QAAQ,CAACE,aAAa;QACnE,MAAM5B,gBAAgB,CAACgG,YAAY,CAACvF,UAAU,EAAEqF,QAAQ,EAAEpE,QAAQ,CAAC;QACnEhB,SAAS,CAAC,gCAAgC,CAAC;MAC7C,CAAC,MAAM,IAAIY,UAAU,KAAK,eAAe,EAAE;QACzC;QACA,MAAMwE,QAAQ,GAAGtE,cAAc,CAACuE,SAAS,IAAIvE,cAAc,CAACI,aAAa;QACzE,MAAM5B,gBAAgB,CAACiG,YAAY,CAACxF,UAAU,EAAEqF,QAAQ,CAAC;QACzDpF,SAAS,CAAC,+BAA+B,CAAC;MAC5C;MAEA4D,iBAAiB,CAAC,CAAC;MACnBnB,UAAU,CAAC,CAAC,CAAC,CAAC;IAChB,CAAC,CAAC,OAAOG,KAAK,EAAE;MACd3C,OAAO,CAAC,gCAAgC,IAAI2C,KAAK,CAAC4C,MAAM,IAAI5C,KAAK,CAAC2B,OAAO,IAAI,oBAAoB,CAAC,CAAC;MACnG1B,OAAO,CAACD,KAAK,CAAC,+BAA+B,EAAEA,KAAK,CAAC;IACvD,CAAC,SAAS;MACRvC,UAAU,CAAC,KAAK,CAAC;IACnB;EACF,CAAC;;EAED;EACA,MAAMoF,iBAAiB,GAAGA,CAAA,KAAM;IAC9B,IAAInF,MAAM,CAACoF,MAAM,KAAK,CAAC,EAAE;MACvB,oBACE7F,OAAA,CAAC9B,KAAK;QAAC4H,QAAQ,EAAC,MAAM;QAAAC,QAAA,EAAC;MAA0B;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAO,CAAC;IAE7D;IAEA,oBACEnG,OAAA,CAACjD,IAAI;MAACqJ,SAAS;MAACC,OAAO,EAAE,CAAE;MAAAN,QAAA,EACxBtF,MAAM,CAAC6F,GAAG,CAAErC,MAAM,iBACjBjE,OAAA,CAACjD,IAAI;QAACwJ,IAAI;QAACC,EAAE,EAAE,EAAG;QAACC,EAAE,EAAE,CAAE;QAACC,EAAE,EAAE,CAAE;QAAAX,QAAA,eAC9B/F,OAAA,CAAChD,IAAI;UAAA+I,QAAA,gBACH/F,OAAA,CAAC/C,WAAW;YAAA8I,QAAA,gBACV/F,OAAA,CAACpD,UAAU;cAAC+J,OAAO,EAAC,IAAI;cAACC,SAAS,EAAC,KAAK;cAAAb,QAAA,GAAC,UAC/B,EAAC9B,MAAM,CAAC5C,aAAa;YAAA;cAAA2E,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACnB,CAAC,eACbnG,OAAA,CAACpD,UAAU;cAAC+J,OAAO,EAAC,OAAO;cAACE,KAAK,EAAC,gBAAgB;cAAAd,QAAA,GAAC,WACxC,EAAC9B,MAAM,CAAC3C,OAAO,IAAI,KAAK;YAAA;cAAA0E,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACvB,CAAC,eACbnG,OAAA,CAACpD,UAAU;cAAC+J,OAAO,EAAC,OAAO;cAACE,KAAK,EAAC,gBAAgB;cAAAd,QAAA,GAAC,aACtC,EAAC9B,MAAM,CAAC1C,SAAS,IAAI,KAAK;YAAA;cAAAyE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC3B,CAAC,eACbnG,OAAA,CAACpD,UAAU;cAAC+J,OAAO,EAAC,OAAO;cAACE,KAAK,EAAC,gBAAgB;cAAAd,QAAA,GAAC,oBAClC,EAAC9B,MAAM,CAACzC,YAAY,IAAI,KAAK;YAAA;cAAAwE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAClC,CAAC,eACbnG,OAAA,CAACpD,UAAU;cAAC+J,OAAO,EAAC,OAAO;cAACE,KAAK,EAAC,gBAAgB;cAAAd,QAAA,GAAC,WACxC,EAAC9B,MAAM,CAACxC,OAAO,IAAI,KAAK;YAAA;cAAAuE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACvB,CAAC,eACbnG,OAAA,CAACpD,UAAU;cAAC+J,OAAO,EAAC,OAAO;cAACE,KAAK,EAAC,gBAAgB;cAAAd,QAAA,GAAC,gBACnC,EAAC9B,MAAM,CAACvC,YAAY,IAAI,KAAK,EAAC,IAC9C;YAAA;cAAAsE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAY,CAAC,eACbnG,OAAA,CAACpD,UAAU;cAAC+J,OAAO,EAAC,OAAO;cAACE,KAAK,EAAC,gBAAgB;cAAAd,QAAA,GAAC,iBAClC,EAAC9B,MAAM,CAACtC,aAAa,IAAI,KAAK,EAAC,IAChD;YAAA;cAAAqE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAY,CAAC,eACbnG,OAAA,CAACpD,UAAU;cAAC+J,OAAO,EAAC,OAAO;cAACE,KAAK,EAAC,gBAAgB;cAAAd,QAAA,GAAC,SAC1C,EAAC9B,MAAM,CAACrC,YAAY,IAAI,aAAa;YAAA;cAAAoE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAClC,CAAC,eACbnG,OAAA,CAACpD,UAAU;cAAC+J,OAAO,EAAC,OAAO;cAACE,KAAK,EAAC,gBAAgB;cAAAd,QAAA,GAAC,cACrC,EAAC9B,MAAM,CAACpC,iBAAiB,IAAI,KAAK;YAAA;cAAAmE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACpC,CAAC,eACbnG,OAAA,CAACpD,UAAU;cAAC+J,OAAO,EAAC,OAAO;cAACE,KAAK,EAAC,gBAAgB;cAAAd,QAAA,GAAC,aACtC,EAAC9B,MAAM,CAACnC,SAAS,IAAI,KAAK;YAAA;cAAAkE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC3B,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACF,CAAC,eACdnG,OAAA,CAAC9C,WAAW;YAAA6I,QAAA,gBACV/F,OAAA,CAACnD,MAAM;cACLiK,IAAI,EAAC,OAAO;cACZC,SAAS,eAAE/G,OAAA,CAAClB,QAAQ;gBAAAkH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAE;cACxBa,OAAO,EAAEA,CAAA,KAAM;gBACbhG,aAAa,CAAC,iBAAiB,CAAC;gBAChCgD,kBAAkB,CAACC,MAAM,CAAC;cAC5B,CAAE;cAAA8B,QAAA,EACH;YAED;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC,eACTnG,OAAA,CAACnD,MAAM;cACLiK,IAAI,EAAC,OAAO;cACZD,KAAK,EAAC,OAAO;cACbE,SAAS,eAAE/G,OAAA,CAAChB,UAAU;gBAAAgH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAE;cAC1Ba,OAAO,EAAEA,CAAA,KAAM;gBACbhG,aAAa,CAAC,eAAe,CAAC;gBAC9BE,iBAAiB,CAAC+C,MAAM,CAAC;gBACzBnD,aAAa,CAAC,IAAI,CAAC;cACrB,CAAE;cAAAiF,QAAA,EACH;YAED;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACE,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACV;MAAC,GA1D6BlC,MAAM,CAAC5C,aAAa;QAAA2E,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OA2DpD,CACP;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACE,CAAC;EAEX,CAAC;;EAED;EACA,MAAMc,YAAY,GAAGA,CAAA,KAAM;IACzB,IAAIlG,UAAU,KAAK,YAAY,IAAIA,UAAU,KAAK,gBAAgB,EAAE;MAClE,oBACEf,OAAA,CAAC7C,MAAM;QAAC+J,IAAI,EAAErG,UAAW;QAACsG,OAAO,EAAEpD,iBAAkB;QAACqD,QAAQ,EAAC,IAAI;QAACC,SAAS;QAAAtB,QAAA,gBAC3E/F,OAAA,CAAC5C,WAAW;UAAA2I,QAAA,EACThF,UAAU,KAAK,YAAY,GAAG,mBAAmB,GAAG;QAAiB;UAAAiF,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC3D,CAAC,eACdnG,OAAA,CAAC3C,aAAa;UAAA0I,QAAA,GACXuB,MAAM,CAACC,IAAI,CAACnF,YAAY,CAAC,CAACyD,MAAM,GAAG,CAAC,iBACnC7F,OAAA,CAAC9B,KAAK;YAAC4H,QAAQ,EAAC,SAAS;YAAC0B,EAAE,EAAE;cAAEC,EAAE,EAAE,CAAC;cAAEC,EAAE,EAAE;YAAE,CAAE;YAAA3B,QAAA,gBAC7C/F,OAAA,CAACpD,UAAU;cAAC+J,OAAO,EAAC,WAAW;cAAAZ,QAAA,EAAC;YAAW;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAY,CAAC,eACxDnG,OAAA;cAAI2H,KAAK,EAAE;gBAAEC,MAAM,EAAE,CAAC;gBAAEC,WAAW,EAAE;cAAO,CAAE;cAAA9B,QAAA,EAC3CuB,MAAM,CAACQ,MAAM,CAAC1F,YAAY,CAAC,CAACkE,GAAG,CAAC,CAACzB,OAAO,EAAEkD,KAAK,kBAC9C/H,OAAA;gBAAA+F,QAAA,EAAiBlB;cAAO,GAAfkD,KAAK;gBAAA/B,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAe,CAC9B;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACA,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACA,CACR,eAEDnG,OAAA,CAACjD,IAAI;YAACqJ,SAAS;YAACC,OAAO,EAAE,CAAE;YAACmB,EAAE,EAAE;cAAEE,EAAE,EAAE;YAAE,CAAE;YAAA3B,QAAA,gBACxC/F,OAAA,CAACjD,IAAI;cAACwJ,IAAI;cAACC,EAAE,EAAE,EAAG;cAACC,EAAE,EAAE,CAAE;cAAAV,QAAA,eACvB/F,OAAA,CAACzC,SAAS;gBACR6G,IAAI,EAAC,eAAe;gBACpB4D,KAAK,EAAE7G,QAAQ,CAACc,cAAc,KAAK,GAAG,GAAG,eAAe,GAAG,WAAY;gBACvEoF,SAAS;gBACTV,OAAO,EAAC,UAAU;gBAClBtC,KAAK,EAAElD,QAAQ,CAACE,aAAc;gBAC9B4G,QAAQ,EAAE/D,gBAAiB;gBAC3BgE,QAAQ,EAAEnH,UAAU,KAAK,gBAAgB,IAAKA,UAAU,KAAK,YAAY,IAAII,QAAQ,CAACc,cAAc,KAAK,GAAK;gBAC9GkG,QAAQ;gBACRpF,KAAK,EAAE,CAAC,CAACb,UAAU,CAACb,aAAc;gBAClC+G,UAAU,EACRlG,UAAU,CAACb,aAAa,KACvBN,UAAU,KAAK,YAAY,IAAII,QAAQ,CAACc,cAAc,KAAK,GAAG,GAC3D,iCAAiC,GACjClB,UAAU,KAAK,YAAY,IAAII,QAAQ,CAACc,cAAc,KAAK,GAAG,GAC5D,yCAAyC,GACzC,EAAE;cACT;gBAAA+D,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACF;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACE,CAAC,eACPnG,OAAA,CAACjD,IAAI;cAACwJ,IAAI;cAACC,EAAE,EAAE,EAAG;cAACC,EAAE,EAAE,CAAE;cAAAV,QAAA,eACvB/F,OAAA,CAACzC,SAAS;gBACR6G,IAAI,EAAC,SAAS;gBACd4D,KAAK,EAAC,SAAS;gBACfX,SAAS;gBACTV,OAAO,EAAC,UAAU;gBAClBtC,KAAK,EAAElD,QAAQ,CAACG,OAAQ;gBACxB2G,QAAQ,EAAE/D,gBAAiB;gBAC3BiE,QAAQ;gBACRpF,KAAK,EAAE,CAAC,CAACb,UAAU,CAACZ,OAAQ;gBAC5B8G,UAAU,EAAElG,UAAU,CAACZ,OAAO,IAAI;cAAG;gBAAA0E,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACtC;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACE,CAAC,eACPnG,OAAA,CAACjD,IAAI;cAACwJ,IAAI;cAACC,EAAE,EAAE,EAAG;cAACC,EAAE,EAAE,CAAE;cAAAV,QAAA,eACvB/F,OAAA,CAACzC,SAAS;gBACR6G,IAAI,EAAC,WAAW;gBAChB4D,KAAK,EAAC,WAAW;gBACjBX,SAAS;gBACTV,OAAO,EAAC,UAAU;gBAClBtC,KAAK,EAAElD,QAAQ,CAACI,SAAU;gBAC1B0G,QAAQ,EAAE/D,gBAAiB;gBAC3BiE,QAAQ;gBACRpF,KAAK,EAAE,CAAC,CAACb,UAAU,CAACX,SAAU;gBAC9B6G,UAAU,EAAElG,UAAU,CAACX,SAAS,IAAI;cAAG;gBAAAyE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACxC;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACE,CAAC,eACPnG,OAAA,CAACjD,IAAI;cAACwJ,IAAI;cAACC,EAAE,EAAE,EAAG;cAACC,EAAE,EAAE,CAAE;cAAAV,QAAA,eACvB/F,OAAA,CAACzC,SAAS;gBACR6G,IAAI,EAAC,cAAc;gBACnB4D,KAAK,EAAC,kBAAe;gBACrBX,SAAS;gBACTV,OAAO,EAAC,UAAU;gBAClBtC,KAAK,EAAElD,QAAQ,CAACK,YAAa;gBAC7ByG,QAAQ,EAAE/D,gBAAiB;gBAC3BiE,QAAQ;gBACRpF,KAAK,EAAE,CAAC,CAACb,UAAU,CAACV,YAAa;gBACjC4G,UAAU,EAAElG,UAAU,CAACV,YAAY,IAAI;cAAG;gBAAAwE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC3C;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACE,CAAC,eACPnG,OAAA,CAACjD,IAAI;cAACwJ,IAAI;cAACC,EAAE,EAAE,EAAG;cAACC,EAAE,EAAE,CAAE;cAAAV,QAAA,eACvB/F,OAAA,CAACzC,SAAS;gBACR6G,IAAI,EAAC,SAAS;gBACd4D,KAAK,EAAC,SAAS;gBACfX,SAAS;gBACTV,OAAO,EAAC,UAAU;gBAClBtC,KAAK,EAAElD,QAAQ,CAACM,OAAQ;gBACxBwG,QAAQ,EAAE/D,gBAAiB;gBAC3BiE,QAAQ;gBACRpF,KAAK,EAAE,CAAC,CAACb,UAAU,CAACT,OAAQ;gBAC5B2G,UAAU,EAAElG,UAAU,CAACT,OAAO,IAAI;cAAG;gBAAAuE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACtC;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACE,CAAC,eACPnG,OAAA,CAACjD,IAAI;cAACwJ,IAAI;cAACC,EAAE,EAAE,EAAG;cAACC,EAAE,EAAE,CAAE;cAAAV,QAAA,eACvB/F,OAAA,CAACzC,SAAS;gBACR6G,IAAI,EAAC,cAAc;gBACnB4D,KAAK,EAAC,cAAc;gBACpBK,IAAI,EAAC,QAAQ;gBACbhB,SAAS;gBACTV,OAAO,EAAC,UAAU;gBAClBtC,KAAK,EAAElD,QAAQ,CAACO,YAAa;gBAC7BuG,QAAQ,EAAE/D,gBAAiB;gBAC3BiE,QAAQ;gBACRpF,KAAK,EAAE,CAAC,CAACb,UAAU,CAACR,YAAa;gBACjC0G,UAAU,EAAElG,UAAU,CAACR,YAAY,IAAI;cAAG;gBAAAsE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC3C;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACE,CAAC,EACNpF,UAAU,KAAK,gBAAgB,iBAC9Bf,OAAA,CAACjD,IAAI;cAACwJ,IAAI;cAACC,EAAE,EAAE,EAAG;cAACC,EAAE,EAAE,CAAE;cAAAV,QAAA,eACvB/F,OAAA,CAACzC,SAAS;gBACR6G,IAAI,EAAC,eAAe;gBACpB4D,KAAK,EAAC,eAAe;gBACrBK,IAAI,EAAC,QAAQ;gBACbhB,SAAS;gBACTV,OAAO,EAAC,UAAU;gBAClBtC,KAAK,EAAElD,QAAQ,CAACQ,aAAc;gBAC9BsG,QAAQ,EAAE/D,gBAAiB;gBAC3BiE,QAAQ;gBACRD,QAAQ,EAAE,IAAK;gBACfE,UAAU,EAAC;cAA4D;gBAAApC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACxE;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACE,CACP,eACDnG,OAAA,CAACjD,IAAI;cAACwJ,IAAI;cAACC,EAAE,EAAE,EAAG;cAACC,EAAE,EAAE,CAAE;cAAAV,QAAA,eACvB/F,OAAA,CAACxC,WAAW;gBAAC6J,SAAS;gBAAAtB,QAAA,gBACpB/F,OAAA,CAACvC,UAAU;kBAAC6K,EAAE,EAAC,oBAAoB;kBAAAvC,QAAA,EAAC;gBAAY;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAY,CAAC,eAC7DnG,OAAA,CAACtC,MAAM;kBACL6K,OAAO,EAAC,oBAAoB;kBAC5BnE,IAAI,EAAC,cAAc;kBACnBC,KAAK,EAAElD,QAAQ,CAACS,YAAa;kBAC7BoG,KAAK,EAAC,cAAc;kBACpBC,QAAQ,EAAE/D,gBAAiB;kBAC3BgE,QAAQ,EAAEnH,UAAU,KAAK,YAAa;kBAAAgF,QAAA,gBAEtC/F,OAAA,CAACrC,QAAQ;oBAAC0G,KAAK,EAAC,aAAa;oBAAA0B,QAAA,EAAC;kBAAW;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAU,CAAC,eACpDnG,OAAA,CAACrC,QAAQ;oBAAC0G,KAAK,EAAC,QAAQ;oBAAA0B,QAAA,EAAC;kBAAM;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAU,CAAC,eAC1CnG,OAAA,CAACrC,QAAQ;oBAAC0G,KAAK,EAAC,WAAW;oBAAA0B,QAAA,EAAC;kBAAS;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAU,CAAC,eAChDnG,OAAA,CAACrC,QAAQ;oBAAC0G,KAAK,EAAC,aAAa;oBAAA0B,QAAA,EAAC;kBAAW;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAU,CAAC,eACpDnG,OAAA,CAACrC,QAAQ;oBAAC0G,KAAK,EAAC,MAAM;oBAAA0B,QAAA,EAAC;kBAAI;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAU,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAChC,CAAC,EACRpF,UAAU,KAAK,YAAY,iBAC1Bf,OAAA,CAACtB,cAAc;kBAAAqH,QAAA,EAAC;gBAAqD;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAgB,CACtF;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACU;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACV,CAAC,eACPnG,OAAA,CAACjD,IAAI;cAACwJ,IAAI;cAACC,EAAE,EAAE,EAAG;cAACC,EAAE,EAAE,CAAE;cAAAV,QAAA,eACvB/F,OAAA,CAACzC,SAAS;gBACR6G,IAAI,EAAC,mBAAmB;gBACxB4D,KAAK,EAAC,mBAAmB;gBACzBX,SAAS;gBACTV,OAAO,EAAC,UAAU;gBAClBtC,KAAK,EAAElD,QAAQ,CAACU,iBAAkB;gBAClCoG,QAAQ,EAAE/D,gBAAiB;gBAC3BnB,KAAK,EAAE,CAAC,CAACb,UAAU,CAACL,iBAAkB;gBACtCuG,UAAU,EAAElG,UAAU,CAACL,iBAAiB,IAAI;cAAG;gBAAAmE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAChD;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACE,CAAC,eACPnG,OAAA,CAACjD,IAAI;cAACwJ,IAAI;cAACC,EAAE,EAAE,EAAG;cAACC,EAAE,EAAE,CAAE;cAAAV,QAAA,eACvB/F,OAAA,CAACzC,SAAS;gBACR6G,IAAI,EAAC,WAAW;gBAChB4D,KAAK,EAAC,WAAW;gBACjBX,SAAS;gBACTV,OAAO,EAAC,UAAU;gBAClBtC,KAAK,EAAElD,QAAQ,CAACW,SAAU;gBAC1BmG,QAAQ,EAAE/D,gBAAiB;gBAC3BnB,KAAK,EAAE,CAAC,CAACb,UAAU,CAACJ,SAAU;gBAC9BsG,UAAU,EAAElG,UAAU,CAACJ,SAAS,IAAI;cAAG;gBAAAkE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACxC;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACE,CAAC,eACPnG,OAAA,CAACjD,IAAI;cAACwJ,IAAI;cAACC,EAAE,EAAE,EAAG;cAACC,EAAE,EAAE,CAAE;cAAAV,QAAA,eACvB/F,OAAA,CAACzC,SAAS;gBACR6G,IAAI,EAAC,OAAO;gBACZ4D,KAAK,EAAC,YAAY;gBAClBX,SAAS;gBACTV,OAAO,EAAC,UAAU;gBAClBtC,KAAK,EAAElD,QAAQ,CAACY,KAAM;gBACtBkG,QAAQ,EAAE/D,gBAAiB;gBAC3BnB,KAAK,EAAE,CAAC,CAACb,UAAU,CAACH,KAAM;gBAC1BqG,UAAU,EAAElG,UAAU,CAACH,KAAK,IAAI;cAAG;gBAAAiE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACpC;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACE,CAAC,eACPnG,OAAA,CAACjD,IAAI;cAACwJ,IAAI;cAACC,EAAE,EAAE,EAAG;cAACC,EAAE,EAAE,CAAE;cAAAV,QAAA,eACvB/F,OAAA,CAACzC,SAAS;gBACR6G,IAAI,EAAC,UAAU;gBACf4D,KAAK,EAAC,uBAAuB;gBAC7BX,SAAS;gBACTV,OAAO,EAAC,UAAU;gBAClBtC,KAAK,EAAElD,QAAQ,CAACa,QAAS;gBACzBiG,QAAQ,EAAE/D,gBAAiB;gBAC3BsE,WAAW,EAAC,YAAY;gBACxBzF,KAAK,EAAE,CAAC,CAACb,UAAU,CAACF,QAAS;gBAC7BoG,UAAU,EAAElG,UAAU,CAACF,QAAQ,IAAI;cAAG;gBAAAgE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACvC;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACE,CAAC,eACPnG,OAAA,CAACjD,IAAI;cAACwJ,IAAI;cAACC,EAAE,EAAE,EAAG;cAACC,EAAE,EAAE,CAAE;cAAAV,QAAA,eACvB/F,OAAA,CAACzC,SAAS;gBACR6G,IAAI,EAAC,gBAAgB;gBACrB4D,KAAK,EAAC,gBAAgB;gBACtBX,SAAS;gBACTV,OAAO,EAAC,UAAU;gBAClBtC,KAAK,EAAElD,QAAQ,CAACc,cAAe;gBAC/BgG,QAAQ,EAAE/D,gBAAiB;gBAC3BgE,QAAQ,EAAE,IAAK;gBACfE,UAAU,EAAEjH,QAAQ,CAACc,cAAc,KAAK,GAAG,GAAG,wBAAwB,GAAG;cAAgC;gBAAA+D,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC1G;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACE,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACM,CAAC,eAChBnG,OAAA,CAAC1C,aAAa;UAAAyI,QAAA,gBACZ/F,OAAA,CAACnD,MAAM;YAACmK,OAAO,EAAEjD,iBAAkB;YAAAgC,QAAA,EAAC;UAAO;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC,eACpDnG,OAAA,CAACnD,MAAM;YACLmK,OAAO,EAAEjC,UAAW;YACpBmD,QAAQ,EAAE3H,OAAO,IAAI+G,MAAM,CAACC,IAAI,CAACrF,UAAU,CAAC,CAAC2D,MAAM,GAAG,CAAE;YACxDkB,SAAS,EAAExG,OAAO,gBAAGP,OAAA,CAAC7B,gBAAgB;cAAC2I,IAAI,EAAE;YAAG;cAAAd,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,gBAAGnG,OAAA,CAACZ,QAAQ;cAAA4G,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAE;YACnEQ,OAAO,EAAC,WAAW;YACnBE,KAAK,EAAC,SAAS;YAAAd,QAAA,EAChB;UAED;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACI,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACV,CAAC;IAEb,CAAC,MAAM,IAAIpF,UAAU,KAAK,iBAAiB,EAAE;MAC3C,oBACEf,OAAA,CAAC7C,MAAM;QAAC+J,IAAI,EAAErG,UAAW;QAACsG,OAAO,EAAEpD,iBAAkB;QAACqD,QAAQ,EAAC,IAAI;QAACC,SAAS;QAAAtB,QAAA,gBAC3E/F,OAAA,CAAC5C,WAAW;UAAA2I,QAAA,EAAC;QAA8B;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAa,CAAC,eACzDnG,OAAA,CAAC3C,aAAa;UAAA0I,QAAA,EACXxF,OAAO,gBACNP,OAAA,CAAC7B,gBAAgB;YAAA6H,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,GAClB1F,MAAM,CAACoF,MAAM,KAAK,CAAC,gBACrB7F,OAAA,CAAC9B,KAAK;YAAC4H,QAAQ,EAAC,MAAM;YAAAC,QAAA,EAAC;UAA0B;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAO,CAAC,gBAEzDnG,OAAA,CAACpC,IAAI;YAAAmI,QAAA,EACFtF,MAAM,CAAC6F,GAAG,CAAErC,MAAM,iBACjBjE,OAAA,CAACnC,QAAQ;cACP4K,MAAM;cAENzB,OAAO,EAAEA,CAAA,KAAMhD,kBAAkB,CAACC,MAAM,CAAE;cAAA8B,QAAA,eAE1C/F,OAAA,CAAClC,YAAY;gBACX4K,OAAO,EAAE,WAAWzE,MAAM,CAAC5C,aAAa,EAAG;gBAC3CsH,SAAS,EAAE,cAAc1E,MAAM,CAAC1C,SAAS,IAAI,KAAK,eAAe0C,MAAM,CAAC3C,OAAO,IAAI,KAAK,eAAe2C,MAAM,CAACtC,aAAa,IAAI,KAAK;cAAK;gBAAAqE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC1I;YAAC,GANGlC,MAAM,CAAC5C,aAAa;cAAA2E,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAOjB,CACX;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACE;QACP;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACY,CAAC,eAChBnG,OAAA,CAAC1C,aAAa;UAAAyI,QAAA,eACZ/F,OAAA,CAACnD,MAAM;YAACmK,OAAO,EAAEjD,iBAAkB;YAAAgC,QAAA,EAAC;UAAO;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACvC,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACV,CAAC;IAEb,CAAC,MAAM,IAAIpF,UAAU,KAAK,eAAe,EAAE;MACzC,oBACEf,OAAA,CAAC7C,MAAM;QAAC+J,IAAI,EAAErG,UAAW;QAACsG,OAAO,EAAEpD,iBAAkB;QAACqD,QAAQ,EAAC,IAAI;QAACC,SAAS;QAAAtB,QAAA,gBAC3E/F,OAAA,CAAC5C,WAAW;UAAA2I,QAAA,EAAC;QAAc;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAa,CAAC,eACzCnG,OAAA,CAAC3C,aAAa;UAAA0I,QAAA,EACX,CAAC9E,cAAc,GACdV,OAAO,gBACLP,OAAA,CAAC7B,gBAAgB;YAAA6H,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,GAClB1F,MAAM,CAACoF,MAAM,KAAK,CAAC,gBACrB7F,OAAA,CAAC9B,KAAK;YAAC4H,QAAQ,EAAC,MAAM;YAAAC,QAAA,EAAC;UAA0B;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAO,CAAC,gBAEzDnG,OAAA,CAACpC,IAAI;YAAAmI,QAAA,EACFtF,MAAM,CAAC6F,GAAG,CAAErC,MAAM,iBACjBjE,OAAA,CAACnC,QAAQ;cACP4K,MAAM;cAENzB,OAAO,EAAEA,CAAA,KAAM9F,iBAAiB,CAAC+C,MAAM,CAAE;cAAA8B,QAAA,eAEzC/F,OAAA,CAAClC,YAAY;gBACX4K,OAAO,EAAE,WAAWzE,MAAM,CAAC5C,aAAa,EAAG;gBAC3CsH,SAAS,EAAE,cAAc1E,MAAM,CAAC1C,SAAS,IAAI,KAAK,eAAe0C,MAAM,CAAC3C,OAAO,IAAI,KAAK,eAAe2C,MAAM,CAACtC,aAAa,IAAI,KAAK;cAAK;gBAAAqE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC1I;YAAC,GANGlC,MAAM,CAAC5C,aAAa;cAAA2E,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAOjB,CACX;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACE,CACP,gBAEDnG,OAAA,CAACrD,GAAG;YAAAoJ,QAAA,gBACF/F,OAAA,CAAC9B,KAAK;cAAC4H,QAAQ,EAAC,SAAS;cAAC0B,EAAE,EAAE;gBAAEC,EAAE,EAAE;cAAE,CAAE;cAAA1B,QAAA,GAAC,0CACC,EAAC9E,cAAc,CAACI,aAAa,EAAC,GACxE;YAAA;cAAA2E,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CAAC,eACRnG,OAAA,CAACpD,UAAU;cAAC+J,OAAO,EAAC,OAAO;cAAAZ,QAAA,EAAC;YAE5B;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAY,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACV;QACN;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACY,CAAC,eAChBnG,OAAA,CAAC1C,aAAa;UAAAyI,QAAA,gBACZ/F,OAAA,CAACnD,MAAM;YAACmK,OAAO,EAAEjD,iBAAkB;YAAAgC,QAAA,EAAC;UAAO;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC,EACnDlF,cAAc,iBACbjB,OAAA,CAACnD,MAAM;YACLmK,OAAO,EAAEjC,UAAW;YACpBmD,QAAQ,EAAE3H,OAAQ;YAClBsG,KAAK,EAAC,OAAO;YACbE,SAAS,EAAExG,OAAO,gBAAGP,OAAA,CAAC7B,gBAAgB;cAAC2I,IAAI,EAAE;YAAG;cAAAd,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,gBAAGnG,OAAA,CAAChB,UAAU;cAAAgH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAE;YAAAJ,QAAA,EACtE;UAED;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CACT;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACY,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACV,CAAC;IAEb,CAAC,MAAM,IAAIpF,UAAU,KAAK,mBAAmB,EAAE;MAC7C,oBACEf,OAAA,CAAC7C,MAAM;QAAC+J,IAAI,EAAErG,UAAW;QAACsG,OAAO,EAAEpD,iBAAkB;QAACqD,QAAQ,EAAC,IAAI;QAACC,SAAS;QAAAtB,QAAA,gBAC3E/F,OAAA,CAAC5C,WAAW;UAAA2I,QAAA,EAAC;QAAuB;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAa,CAAC,eAClDnG,OAAA,CAAC3C,aAAa;UAAA0I,QAAA,EACXxF,OAAO,gBACNP,OAAA,CAAC7B,gBAAgB;YAAA6H,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,GAClB7D,eAAe,CAACuD,MAAM,KAAK,CAAC,gBAC9B7F,OAAA,CAAC9B,KAAK;YAAC4H,QAAQ,EAAC,MAAM;YAAAC,QAAA,EAAC;UAA+B;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAO,CAAC,gBAE9DnG,OAAA,CAACzB,cAAc;YAACqI,SAAS,EAAE9J,KAAM;YAAC0K,EAAE,EAAE;cAAEE,EAAE,EAAE;YAAE,CAAE;YAAA3B,QAAA,eAC9C/F,OAAA,CAAC5B,KAAK;cAAC0I,IAAI,EAAC,OAAO;cAAAf,QAAA,gBACjB/F,OAAA,CAACxB,SAAS;gBAAAuH,QAAA,eACR/F,OAAA,CAACvB,QAAQ;kBAAAsH,QAAA,gBACP/F,OAAA,CAAC1B,SAAS;oBAAAyH,QAAA,EAAC;kBAAM;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAW,CAAC,eAC7BnG,OAAA,CAAC1B,SAAS;oBAAAyH,QAAA,EAAC;kBAAO;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAW,CAAC,eAC9BnG,OAAA,CAAC1B,SAAS;oBAAAyH,QAAA,EAAC;kBAAS;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAW,CAAC,eAChCnG,OAAA,CAAC1B,SAAS;oBAAAyH,QAAA,EAAC;kBAAa;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAW,CAAC,eACpCnG,OAAA,CAAC1B,SAAS;oBAAAyH,QAAA,EAAC;kBAAO;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAW,CAAC,eAC9BnG,OAAA,CAAC1B,SAAS;oBAAAyH,QAAA,EAAC;kBAAY;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAW,CAAC,eACnCnG,OAAA,CAAC1B,SAAS;oBAAAyH,QAAA,EAAC;kBAAa;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAW,CAAC,eACpCnG,OAAA,CAAC1B,SAAS;oBAAAyH,QAAA,EAAC;kBAAc;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAW,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAC7B;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACF,CAAC,eACZnG,OAAA,CAAC3B,SAAS;gBAAA0H,QAAA,EACPzD,eAAe,CAACgE,GAAG,CAAC,CAACsC,MAAM,EAAEb,KAAK,kBACjC/H,OAAA,CAACvB,QAAQ;kBAAAsH,QAAA,gBACP/F,OAAA,CAAC1B,SAAS;oBAAAyH,QAAA,EAAE6C,MAAM,CAACvH;kBAAa;oBAAA2E,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAY,CAAC,eAC7CnG,OAAA,CAAC1B,SAAS;oBAAAyH,QAAA,EAAE6C,MAAM,CAACtH;kBAAO;oBAAA0E,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAY,CAAC,eACvCnG,OAAA,CAAC1B,SAAS;oBAAAyH,QAAA,EAAE6C,MAAM,CAACrH;kBAAS;oBAAAyE,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAY,CAAC,eACzCnG,OAAA,CAAC1B,SAAS;oBAAAyH,QAAA,EAAE6C,MAAM,CAACpH;kBAAY;oBAAAwE,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAY,CAAC,eAC5CnG,OAAA,CAAC1B,SAAS;oBAAAyH,QAAA,EAAE6C,MAAM,CAACnH;kBAAO;oBAAAuE,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAY,CAAC,eACvCnG,OAAA,CAAC1B,SAAS;oBAAAyH,QAAA,EAAE6C,MAAM,CAAClH;kBAAY;oBAAAsE,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAY,CAAC,eAC5CnG,OAAA,CAAC1B,SAAS;oBAAAyH,QAAA,EAAE6C,MAAM,CAACjH;kBAAa;oBAAAqE,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAY,CAAC,eAC7CnG,OAAA,CAAC1B,SAAS;oBAAAyH,QAAA,EAAE6C,MAAM,CAACC,IAAI,CAAChD;kBAAM;oBAAAG,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAY,CAAC;gBAAA,GAR9B4B,KAAK;kBAAA/B,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OASV,CACX;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACO,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACP;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACM;QACjB;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACY,CAAC,eAChBnG,OAAA,CAAC1C,aAAa;UAAAyI,QAAA,eACZ/F,OAAA,CAACnD,MAAM;YAACmK,OAAO,EAAEjD,iBAAkB;YAAAgC,QAAA,EAAC;UAAM;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACtC,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACV,CAAC;IAEb;IAEA,OAAO,IAAI;EACb,CAAC;EAED,oBACEnG,OAAA,CAACrD,GAAG;IAAAoJ,QAAA,GACDpF,cAAc,KAAK,kBAAkB,IAAI,CAACE,UAAU,gBACnDb,OAAA,CAAClD,KAAK;MAAC0K,EAAE,EAAE;QAAEsB,CAAC,EAAE;MAAE,CAAE;MAAA/C,QAAA,gBAClB/F,OAAA,CAACpD,UAAU;QAAC+J,OAAO,EAAC,IAAI;QAACoC,YAAY;QAAAhD,QAAA,EAAC;MAEtC;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAY,CAAC,EACZ5F,OAAO,gBACNP,OAAA,CAACrD,GAAG;QAAC6K,EAAE,EAAE;UAAEwB,OAAO,EAAE,MAAM;UAAEC,cAAc,EAAE,QAAQ;UAAEC,EAAE,EAAE;QAAE,CAAE;QAAAnD,QAAA,eAC5D/F,OAAA,CAAC7B,gBAAgB;UAAA6H,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACjB,CAAC,GAENP,iBAAiB,CAAC,CACnB;IAAA;MAAAI,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACI,CAAC,GACN,CAACtF,UAAU,gBACbb,OAAA,CAAClD,KAAK;MAAC0K,EAAE,EAAE;QAAEsB,CAAC,EAAE,CAAC;QAAEK,SAAS,EAAE,OAAO;QAAEH,OAAO,EAAE,MAAM;QAAEI,UAAU,EAAE,QAAQ;QAAEH,cAAc,EAAE;MAAS,CAAE;MAAAlD,QAAA,EACtG,CAACpF,cAAc,gBACdX,OAAA,CAACpD,UAAU;QAAC+J,OAAO,EAAC,OAAO;QAAAZ,QAAA,EAAC;MAE5B;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAY,CAAC,gBAEbnG,OAAA,CAACrD,GAAG;QAAC6K,EAAE,EAAE;UAAE6B,SAAS,EAAE;QAAS,CAAE;QAAAtD,QAAA,gBAC/B/F,OAAA,CAACpD,UAAU;UAAC+J,OAAO,EAAC,IAAI;UAACoC,YAAY;UAAAhD,QAAA,GAClCpF,cAAc,KAAK,YAAY,IAAI,mBAAmB,EACtDA,cAAc,KAAK,gBAAgB,IAAI,iBAAiB,EACxDA,cAAc,KAAK,eAAe,IAAI,gBAAgB,EACtDA,cAAc,KAAK,mBAAmB,IAAI,6BAA6B;QAAA;UAAAqF,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC9D,CAAC,eACbnG,OAAA,CAAC7B,gBAAgB;UAACqJ,EAAE,EAAE;YAAEE,EAAE,EAAE;UAAE;QAAE;UAAA1B,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAChC;IACN;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACI,CAAC,GACN,IAAI,EAEPc,YAAY,CAAC,CAAC,eAGfjH,OAAA,CAACN,oBAAoB;MACnBwH,IAAI,EAAE1E,gBAAiB;MACvB2E,OAAO,EAAEA,CAAA,KAAM1E,mBAAmB,CAAC,KAAK,CAAE;MAC1C6G,SAAS,EAAE3F;IAAoB;MAAAqC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAChC,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACC,CAAC;AAEV,CAAC;AAAC7F,EAAA,CAhzBIL,SAAS;AAAAsJ,EAAA,GAATtJ,SAAS;AAkzBf,eAAeA,SAAS;AAAC,IAAAsJ,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}