{"ast": null, "code": "import { numberToLocale } from \"./localize.js\";\nconst formatDistanceLocale = {\n  lessThanXSeconds: {\n    one: \"প্রায় ১ সেকেন্ড\",\n    other: \"প্রায় {{count}} সেকেন্ড\"\n  },\n  xSeconds: {\n    one: \"১ সেকেন্ড\",\n    other: \"{{count}} সেকেন্ড\"\n  },\n  halfAMinute: \"আধ মিনিট\",\n  lessThanXMinutes: {\n    one: \"প্রায় ১ মিনিট\",\n    other: \"প্রায় {{count}} মিনিট\"\n  },\n  xMinutes: {\n    one: \"১ মিনিট\",\n    other: \"{{count}} মিনিট\"\n  },\n  aboutXHours: {\n    one: \"প্রায় ১ ঘন্টা\",\n    other: \"প্রায় {{count}} ঘন্টা\"\n  },\n  xHours: {\n    one: \"১ ঘন্টা\",\n    other: \"{{count}} ঘন্টা\"\n  },\n  xDays: {\n    one: \"১ দিন\",\n    other: \"{{count}} দিন\"\n  },\n  aboutXWeeks: {\n    one: \"প্রায় ১ সপ্তাহ\",\n    other: \"প্রায় {{count}} সপ্তাহ\"\n  },\n  xWeeks: {\n    one: \"১ সপ্তাহ\",\n    other: \"{{count}} সপ্তাহ\"\n  },\n  aboutXMonths: {\n    one: \"প্রায় ১ মাস\",\n    other: \"প্রায় {{count}} মাস\"\n  },\n  xMonths: {\n    one: \"১ মাস\",\n    other: \"{{count}} মাস\"\n  },\n  aboutXYears: {\n    one: \"প্রায় ১ বছর\",\n    other: \"প্রায় {{count}} বছর\"\n  },\n  xYears: {\n    one: \"১ বছর\",\n    other: \"{{count}} বছর\"\n  },\n  overXYears: {\n    one: \"১ বছরের বেশি\",\n    other: \"{{count}} বছরের বেশি\"\n  },\n  almostXYears: {\n    one: \"প্রায় ১ বছর\",\n    other: \"প্রায় {{count}} বছর\"\n  }\n};\nexport const formatDistance = (token, count, options) => {\n  let result;\n  const tokenValue = formatDistanceLocale[token];\n  if (typeof tokenValue === \"string\") {\n    result = tokenValue;\n  } else if (count === 1) {\n    result = tokenValue.one;\n  } else {\n    result = tokenValue.other.replace(\"{{count}}\", numberToLocale(count));\n  }\n  if (options?.addSuffix) {\n    if (options.comparison && options.comparison > 0) {\n      return result + \" এর মধ্যে\";\n    } else {\n      return result + \" আগে\";\n    }\n  }\n  return result;\n};", "map": {"version": 3, "names": ["numberToLocale", "formatDistanceLocale", "lessThanXSeconds", "one", "other", "xSeconds", "halfAMinute", "lessThanXMinutes", "xMinutes", "aboutXHours", "xHours", "xDays", "aboutXWeeks", "xWeeks", "aboutXMonths", "xMonths", "aboutXYears", "xYears", "overXYears", "almostXYears", "formatDistance", "token", "count", "options", "result", "tokenValue", "replace", "addSuffix", "comparison"], "sources": ["C:/CMS/webapp/frontend/node_modules/date-fns/locale/bn/_lib/formatDistance.js"], "sourcesContent": ["import { numberToLocale } from \"./localize.js\";\n\nconst formatDistanceLocale = {\n  lessThanXSeconds: {\n    one: \"প্রায় ১ সেকেন্ড\",\n    other: \"প্রায় {{count}} সেকেন্ড\",\n  },\n\n  xSeconds: {\n    one: \"১ সেকেন্ড\",\n    other: \"{{count}} সেকেন্ড\",\n  },\n\n  halfAMinute: \"আধ মিনিট\",\n\n  lessThanXMinutes: {\n    one: \"প্রায় ১ মিনিট\",\n    other: \"প্রায় {{count}} মিনিট\",\n  },\n\n  xMinutes: {\n    one: \"১ মিনিট\",\n    other: \"{{count}} মিনিট\",\n  },\n\n  aboutXHours: {\n    one: \"প্রায় ১ ঘন্টা\",\n    other: \"প্রায় {{count}} ঘন্টা\",\n  },\n\n  xHours: {\n    one: \"১ ঘন্টা\",\n    other: \"{{count}} ঘন্টা\",\n  },\n\n  xDays: {\n    one: \"১ দিন\",\n    other: \"{{count}} দিন\",\n  },\n\n  aboutXWeeks: {\n    one: \"প্রায় ১ সপ্তাহ\",\n    other: \"প্রায় {{count}} সপ্তাহ\",\n  },\n\n  xWeeks: {\n    one: \"১ সপ্তাহ\",\n    other: \"{{count}} সপ্তাহ\",\n  },\n\n  aboutXMonths: {\n    one: \"প্রায় ১ মাস\",\n    other: \"প্রায় {{count}} মাস\",\n  },\n\n  xMonths: {\n    one: \"১ মাস\",\n    other: \"{{count}} মাস\",\n  },\n\n  aboutXYears: {\n    one: \"প্রায় ১ বছর\",\n    other: \"প্রায় {{count}} বছর\",\n  },\n\n  xYears: {\n    one: \"১ বছর\",\n    other: \"{{count}} বছর\",\n  },\n\n  overXYears: {\n    one: \"১ বছরের বেশি\",\n    other: \"{{count}} বছরের বেশি\",\n  },\n\n  almostXYears: {\n    one: \"প্রায় ১ বছর\",\n    other: \"প্রায় {{count}} বছর\",\n  },\n};\n\nexport const formatDistance = (token, count, options) => {\n  let result;\n\n  const tokenValue = formatDistanceLocale[token];\n  if (typeof tokenValue === \"string\") {\n    result = tokenValue;\n  } else if (count === 1) {\n    result = tokenValue.one;\n  } else {\n    result = tokenValue.other.replace(\"{{count}}\", numberToLocale(count));\n  }\n\n  if (options?.addSuffix) {\n    if (options.comparison && options.comparison > 0) {\n      return result + \" এর মধ্যে\";\n    } else {\n      return result + \" আগে\";\n    }\n  }\n\n  return result;\n};\n"], "mappings": "AAAA,SAASA,cAAc,QAAQ,eAAe;AAE9C,MAAMC,oBAAoB,GAAG;EAC3BC,gBAAgB,EAAE;IAChBC,GAAG,EAAE,iBAAiB;IACtBC,KAAK,EAAE;EACT,CAAC;EAEDC,QAAQ,EAAE;IACRF,GAAG,EAAE,WAAW;IAChBC,KAAK,EAAE;EACT,CAAC;EAEDE,WAAW,EAAE,UAAU;EAEvBC,gBAAgB,EAAE;IAChBJ,GAAG,EAAE,eAAe;IACpBC,KAAK,EAAE;EACT,CAAC;EAEDI,QAAQ,EAAE;IACRL,GAAG,EAAE,SAAS;IACdC,KAAK,EAAE;EACT,CAAC;EAEDK,WAAW,EAAE;IACXN,GAAG,EAAE,eAAe;IACpBC,KAAK,EAAE;EACT,CAAC;EAEDM,MAAM,EAAE;IACNP,GAAG,EAAE,SAAS;IACdC,KAAK,EAAE;EACT,CAAC;EAEDO,KAAK,EAAE;IACLR,GAAG,EAAE,OAAO;IACZC,KAAK,EAAE;EACT,CAAC;EAEDQ,WAAW,EAAE;IACXT,GAAG,EAAE,gBAAgB;IACrBC,KAAK,EAAE;EACT,CAAC;EAEDS,MAAM,EAAE;IACNV,GAAG,EAAE,UAAU;IACfC,KAAK,EAAE;EACT,CAAC;EAEDU,YAAY,EAAE;IACZX,GAAG,EAAE,aAAa;IAClBC,KAAK,EAAE;EACT,CAAC;EAEDW,OAAO,EAAE;IACPZ,GAAG,EAAE,OAAO;IACZC,KAAK,EAAE;EACT,CAAC;EAEDY,WAAW,EAAE;IACXb,GAAG,EAAE,aAAa;IAClBC,KAAK,EAAE;EACT,CAAC;EAEDa,MAAM,EAAE;IACNd,GAAG,EAAE,OAAO;IACZC,KAAK,EAAE;EACT,CAAC;EAEDc,UAAU,EAAE;IACVf,GAAG,EAAE,cAAc;IACnBC,KAAK,EAAE;EACT,CAAC;EAEDe,YAAY,EAAE;IACZhB,GAAG,EAAE,aAAa;IAClBC,KAAK,EAAE;EACT;AACF,CAAC;AAED,OAAO,MAAMgB,cAAc,GAAGA,CAACC,KAAK,EAAEC,KAAK,EAAEC,OAAO,KAAK;EACvD,IAAIC,MAAM;EAEV,MAAMC,UAAU,GAAGxB,oBAAoB,CAACoB,KAAK,CAAC;EAC9C,IAAI,OAAOI,UAAU,KAAK,QAAQ,EAAE;IAClCD,MAAM,GAAGC,UAAU;EACrB,CAAC,MAAM,IAAIH,KAAK,KAAK,CAAC,EAAE;IACtBE,MAAM,GAAGC,UAAU,CAACtB,GAAG;EACzB,CAAC,MAAM;IACLqB,MAAM,GAAGC,UAAU,CAACrB,KAAK,CAACsB,OAAO,CAAC,WAAW,EAAE1B,cAAc,CAACsB,KAAK,CAAC,CAAC;EACvE;EAEA,IAAIC,OAAO,EAAEI,SAAS,EAAE;IACtB,IAAIJ,OAAO,CAACK,UAAU,IAAIL,OAAO,CAACK,UAAU,GAAG,CAAC,EAAE;MAChD,OAAOJ,MAAM,GAAG,WAAW;IAC7B,CAAC,MAAM;MACL,OAAOA,MAAM,GAAG,MAAM;IACxB;EACF;EAEA,OAAOA,MAAM;AACf,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}