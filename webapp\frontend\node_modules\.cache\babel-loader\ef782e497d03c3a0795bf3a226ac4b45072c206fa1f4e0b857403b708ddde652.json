{"ast": null, "code": "import _extends from \"@babel/runtime/helpers/esm/extends\";\nexport const DEFAULT_STEP_NAVIGATION = {\n  hasNextStep: false,\n  hasSeveralSteps: false,\n  goToNextStep: () => {},\n  areViewsInSameStep: () => true\n};\n\n/**\n * Create an object that determines whether there is a next step and allows to go to the next step.\n * @param {CreateStepNavigationParameters<TStep>} parameters The parameters of the createStepNavigation function\n * @returns {CreateStepNavigationReturnValue} The return value of the createStepNavigation function\n */\nexport function createStepNavigation(parameters) {\n  const {\n    steps,\n    isViewMatchingStep,\n    onStepChange\n  } = parameters;\n  return parametersBis => {\n    if (steps == null) {\n      return DEFAULT_STEP_NAVIGATION;\n    }\n    const currentStepIndex = steps.findIndex(step => isViewMatchingStep(parametersBis.view, step));\n    const nextStep = currentStepIndex === -1 || currentStepIndex === steps.length - 1 ? null : steps[currentStepIndex + 1];\n    return {\n      hasNextStep: nextStep != null,\n      hasSeveralSteps: steps.length > 1,\n      goToNextStep: () => {\n        if (nextStep == null) {\n          return;\n        }\n        onStepChange(_extends({}, parametersBis, {\n          step: nextStep\n        }));\n      },\n      areViewsInSameStep: (viewA, viewB) => {\n        const stepA = steps.find(step => isViewMatchingStep(viewA, step));\n        const stepB = steps.find(step => isViewMatchingStep(viewB, step));\n        return stepA === stepB;\n      }\n    };\n  };\n}", "map": {"version": 3, "names": ["_extends", "DEFAULT_STEP_NAVIGATION", "hasNextStep", "hasSeveralSteps", "goToNextStep", "areViewsInSameStep", "createStepNavigation", "parameters", "steps", "isViewMatchingStep", "onStepChange", "parametersBis", "currentStepIndex", "findIndex", "step", "view", "nextStep", "length", "viewA", "viewB", "stepA", "find", "stepB"], "sources": ["C:/Users/<USER>/PycharmProjects/CMS/webapp/frontend/node_modules/@mui/x-date-pickers/esm/internals/utils/createStepNavigation.js"], "sourcesContent": ["import _extends from \"@babel/runtime/helpers/esm/extends\";\nexport const DEFAULT_STEP_NAVIGATION = {\n  hasNextStep: false,\n  hasSeveralSteps: false,\n  goToNextStep: () => {},\n  areViewsInSameStep: () => true\n};\n\n/**\n * Create an object that determines whether there is a next step and allows to go to the next step.\n * @param {CreateStepNavigationParameters<TStep>} parameters The parameters of the createStepNavigation function\n * @returns {CreateStepNavigationReturnValue} The return value of the createStepNavigation function\n */\nexport function createStepNavigation(parameters) {\n  const {\n    steps,\n    isViewMatchingStep,\n    onStepChange\n  } = parameters;\n  return parametersBis => {\n    if (steps == null) {\n      return DEFAULT_STEP_NAVIGATION;\n    }\n    const currentStepIndex = steps.findIndex(step => isViewMatchingStep(parametersBis.view, step));\n    const nextStep = currentStepIndex === -1 || currentStepIndex === steps.length - 1 ? null : steps[currentStepIndex + 1];\n    return {\n      hasNextStep: nextStep != null,\n      hasSeveralSteps: steps.length > 1,\n      goToNextStep: () => {\n        if (nextStep == null) {\n          return;\n        }\n        onStepChange(_extends({}, parametersBis, {\n          step: nextStep\n        }));\n      },\n      areViewsInSameStep: (viewA, viewB) => {\n        const stepA = steps.find(step => isViewMatchingStep(viewA, step));\n        const stepB = steps.find(step => isViewMatchingStep(viewB, step));\n        return stepA === stepB;\n      }\n    };\n  };\n}"], "mappings": "AAAA,OAAOA,QAAQ,MAAM,oCAAoC;AACzD,OAAO,MAAMC,uBAAuB,GAAG;EACrCC,WAAW,EAAE,KAAK;EAClBC,eAAe,EAAE,KAAK;EACtBC,YAAY,EAAEA,CAAA,KAAM,CAAC,CAAC;EACtBC,kBAAkB,EAAEA,CAAA,KAAM;AAC5B,CAAC;;AAED;AACA;AACA;AACA;AACA;AACA,OAAO,SAASC,oBAAoBA,CAACC,UAAU,EAAE;EAC/C,MAAM;IACJC,KAAK;IACLC,kBAAkB;IAClBC;EACF,CAAC,GAAGH,UAAU;EACd,OAAOI,aAAa,IAAI;IACtB,IAAIH,KAAK,IAAI,IAAI,EAAE;MACjB,OAAOP,uBAAuB;IAChC;IACA,MAAMW,gBAAgB,GAAGJ,KAAK,CAACK,SAAS,CAACC,IAAI,IAAIL,kBAAkB,CAACE,aAAa,CAACI,IAAI,EAAED,IAAI,CAAC,CAAC;IAC9F,MAAME,QAAQ,GAAGJ,gBAAgB,KAAK,CAAC,CAAC,IAAIA,gBAAgB,KAAKJ,KAAK,CAACS,MAAM,GAAG,CAAC,GAAG,IAAI,GAAGT,KAAK,CAACI,gBAAgB,GAAG,CAAC,CAAC;IACtH,OAAO;MACLV,WAAW,EAAEc,QAAQ,IAAI,IAAI;MAC7Bb,eAAe,EAAEK,KAAK,CAACS,MAAM,GAAG,CAAC;MACjCb,YAAY,EAAEA,CAAA,KAAM;QAClB,IAAIY,QAAQ,IAAI,IAAI,EAAE;UACpB;QACF;QACAN,YAAY,CAACV,QAAQ,CAAC,CAAC,CAAC,EAAEW,aAAa,EAAE;UACvCG,IAAI,EAAEE;QACR,CAAC,CAAC,CAAC;MACL,CAAC;MACDX,kBAAkB,EAAEA,CAACa,KAAK,EAAEC,KAAK,KAAK;QACpC,MAAMC,KAAK,GAAGZ,KAAK,CAACa,IAAI,CAACP,IAAI,IAAIL,kBAAkB,CAACS,KAAK,EAAEJ,IAAI,CAAC,CAAC;QACjE,MAAMQ,KAAK,GAAGd,KAAK,CAACa,IAAI,CAACP,IAAI,IAAIL,kBAAkB,CAACU,KAAK,EAAEL,IAAI,CAAC,CAAC;QACjE,OAAOM,KAAK,KAAKE,KAAK;MACxB;IACF,CAAC;EACH,CAAC;AACH", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}